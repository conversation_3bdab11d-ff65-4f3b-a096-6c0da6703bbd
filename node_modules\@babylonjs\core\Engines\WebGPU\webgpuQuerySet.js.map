{"version": 3, "file": "webgpuQuerySet.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuQuerySet.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,eAAe,MAAM,mBAAmB,CAAC;AAGrD,gBAAgB;AAChB,MAAM,OAAO,cAAc;IAWvB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,YAAY,MAAoB,EAAE,KAAa,EAAE,IAAe,EAAE,MAAiB,EAAE,aAAkC,EAAE,qBAAqB,GAAG,IAAI,EAAE,KAAc;QAN7J,gBAAW,GAAgB,EAAE,CAAC;QAOlC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;QAEpD,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC;YACnC,KAAK,EAAE,KAAK,IAAI,UAAU;YAC1B,IAAI;YACJ,KAAK;SACR,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,eAAe,CAAC,CAAC,GAAG,KAAK,EAAE,eAAe,CAAC,WAAW,CAAC,YAAY,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QAEvK,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,IAAI,CAAC,cAAc,CAAC,eAAe,CAC/B,CAAC,GAAG,IAAI,CAAC,MAAM,EACf,eAAe,CAAC,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EACzE,SAAS,EACT,8BAA8B,CACjC,CACJ,CAAC;QACN,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,UAAkB,EAAE,UAAkB;QACrD,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;QAE1D,IAAI,MAAiB,CAAC;QACtB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CACxC,CAAC,GAAG,IAAI,CAAC,MAAM,EACf,eAAe,CAAC,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EACzE,SAAS,EACT,6BAA6B,CAChC,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;QAC9B,CAAC;QAED,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAC5F,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC;QAElF,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAEpD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACvD,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAEvC,IAAI,CAAC;YACD,MAAM,MAAM,CAAC,QAAQ,sCAA8B,CAAC;YACpD,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;YAErE,MAAM,CAAC,KAAK,EAAE,CAAC;YAEf,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;YAEnD,OAAO,QAAQ,CAAC;QACpB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAChE,8CAA8C;gBAC9C,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,MAAM,GAAG,CAAC;QACd,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAEvC,IAAI,CAAC;YACD,MAAM,MAAM,CAAC,QAAQ,sCAA8B,CAAC;YACpD,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAC7D,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAElC,MAAM,CAAC,KAAK,EAAE,CAAC;YAEf,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;YAEnD,OAAO,KAAK,CAAC;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAChE,8CAA8C;gBAC9C,OAAO,CAAC,CAAC;YACb,CAAC;YACD,MAAM,GAAG,CAAC;QACd,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,wBAAwB,CAAC,UAAU,GAAG,CAAC;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAEvC,IAAI,CAAC;YACD,MAAM,MAAM,CAAC,QAAQ,sCAA8B,CAAC;YACpD,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAC7D,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YAEhD,MAAM,CAAC,KAAK,EAAE,CAAC;YAEf,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;YAEnD,OAAO,KAAK,CAAC;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAChE,8CAA8C;gBAC9C,OAAO,CAAC,CAAC;YACb,CAAC;YACD,MAAM,GAAG,CAAC;QACd,CAAC;IACL,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["/* eslint-disable babylonjs/available */\r\n/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\nimport type { WebGPUBufferManager } from \"./webgpuBufferManager\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport type { QueryType } from \"./webgpuConstants\";\r\n\r\n/** @internal */\r\nexport class WebGPUQuerySet {\r\n    private _engine: WebGPUEngine;\r\n    private _device: GPUDevice;\r\n    private _bufferManager: WebGPUBufferManager;\r\n\r\n    private _count: number;\r\n    private _canUseMultipleBuffers: boolean;\r\n    private _querySet: GPUQuerySet;\r\n    private _queryBuffer: GPUBuffer;\r\n    private _dstBuffers: GPUBuffer[] = [];\r\n\r\n    public get querySet(): GPUQuerySet {\r\n        return this._querySet;\r\n    }\r\n\r\n    constructor(engine: WebGPUEngine, count: number, type: QueryType, device: GPUDevice, bufferManager: WebGPUBufferManager, canUseMultipleBuffers = true, label?: string) {\r\n        this._engine = engine;\r\n        this._device = device;\r\n        this._bufferManager = bufferManager;\r\n        this._count = count;\r\n        this._canUseMultipleBuffers = canUseMultipleBuffers;\r\n\r\n        this._querySet = device.createQuerySet({\r\n            label: label ?? \"QuerySet\",\r\n            type,\r\n            count,\r\n        });\r\n\r\n        this._queryBuffer = bufferManager.createRawBuffer(8 * count, WebGPUConstants.BufferUsage.QueryResolve | WebGPUConstants.BufferUsage.CopySrc, undefined, \"QueryBuffer\");\r\n\r\n        if (!canUseMultipleBuffers) {\r\n            this._dstBuffers.push(\r\n                this._bufferManager.createRawBuffer(\r\n                    8 * this._count,\r\n                    WebGPUConstants.BufferUsage.MapRead | WebGPUConstants.BufferUsage.CopyDst,\r\n                    undefined,\r\n                    \"QueryBufferNoMultipleBuffers\"\r\n                )\r\n            );\r\n        }\r\n    }\r\n\r\n    private _getBuffer(firstQuery: number, queryCount: number): GPUBuffer | null {\r\n        if (!this._canUseMultipleBuffers && this._dstBuffers.length === 0) {\r\n            return null;\r\n        }\r\n\r\n        const encoderResult = this._device.createCommandEncoder();\r\n\r\n        let buffer: GPUBuffer;\r\n        if (this._dstBuffers.length === 0) {\r\n            buffer = this._bufferManager.createRawBuffer(\r\n                8 * this._count,\r\n                WebGPUConstants.BufferUsage.MapRead | WebGPUConstants.BufferUsage.CopyDst,\r\n                undefined,\r\n                \"QueryBufferAdditionalBuffer\"\r\n            );\r\n        } else {\r\n            buffer = this._dstBuffers[this._dstBuffers.length - 1];\r\n            this._dstBuffers.length--;\r\n        }\r\n\r\n        encoderResult.resolveQuerySet(this._querySet, firstQuery, queryCount, this._queryBuffer, 0);\r\n        encoderResult.copyBufferToBuffer(this._queryBuffer, 0, buffer, 0, 8 * queryCount);\r\n\r\n        this._device.queue.submit([encoderResult.finish()]);\r\n\r\n        return buffer;\r\n    }\r\n\r\n    public async readValues(firstQuery = 0, queryCount = 1): Promise<BigUint64Array | null> {\r\n        const buffer = this._getBuffer(firstQuery, queryCount);\r\n        if (buffer === null) {\r\n            return null;\r\n        }\r\n        const engineId = this._engine.uniqueId;\r\n\r\n        try {\r\n            await buffer.mapAsync(WebGPUConstants.MapMode.Read);\r\n            const arrayBuf = new BigUint64Array(buffer.getMappedRange()).slice();\r\n\r\n            buffer.unmap();\r\n\r\n            this._dstBuffers[this._dstBuffers.length] = buffer;\r\n\r\n            return arrayBuf;\r\n        } catch (err) {\r\n            if (this._engine.isDisposed || this._engine.uniqueId !== engineId) {\r\n                // Engine disposed or context loss/restoration\r\n                return null;\r\n            }\r\n            throw err;\r\n        }\r\n    }\r\n\r\n    public async readValue(firstQuery = 0): Promise<number | null> {\r\n        const buffer = this._getBuffer(firstQuery, 1);\r\n        if (buffer === null) {\r\n            return null;\r\n        }\r\n        const engineId = this._engine.uniqueId;\r\n\r\n        try {\r\n            await buffer.mapAsync(WebGPUConstants.MapMode.Read);\r\n            const arrayBuf = new BigUint64Array(buffer.getMappedRange());\r\n            const value = Number(arrayBuf[0]);\r\n\r\n            buffer.unmap();\r\n\r\n            this._dstBuffers[this._dstBuffers.length] = buffer;\r\n\r\n            return value;\r\n        } catch (err) {\r\n            if (this._engine.isDisposed || this._engine.uniqueId !== engineId) {\r\n                // Engine disposed or context loss/restoration\r\n                return 0;\r\n            }\r\n            throw err;\r\n        }\r\n    }\r\n\r\n    public async readTwoValuesAndSubtract(firstQuery = 0): Promise<number | null> {\r\n        const buffer = this._getBuffer(firstQuery, 2);\r\n        if (buffer === null) {\r\n            return null;\r\n        }\r\n        const engineId = this._engine.uniqueId;\r\n\r\n        try {\r\n            await buffer.mapAsync(WebGPUConstants.MapMode.Read);\r\n            const arrayBuf = new BigUint64Array(buffer.getMappedRange());\r\n            const value = Number(arrayBuf[1] - arrayBuf[0]);\r\n\r\n            buffer.unmap();\r\n\r\n            this._dstBuffers[this._dstBuffers.length] = buffer;\r\n\r\n            return value;\r\n        } catch (err) {\r\n            if (this._engine.isDisposed || this._engine.uniqueId !== engineId) {\r\n                // Engine disposed or context loss/restoration\r\n                return 0;\r\n            }\r\n            throw err;\r\n        }\r\n    }\r\n\r\n    public dispose() {\r\n        this._querySet.destroy();\r\n        this._bufferManager.releaseBuffer(this._queryBuffer);\r\n        for (let i = 0; i < this._dstBuffers.length; ++i) {\r\n            this._bufferManager.releaseBuffer(this._dstBuffers[i]);\r\n        }\r\n    }\r\n}\r\n"]}