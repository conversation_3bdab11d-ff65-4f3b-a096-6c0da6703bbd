{"version": 3, "file": "axesViewer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Debug/axesViewer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAI/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD;;;GAGG;AACH,MAAM,OAAO,UAAU;IAanB;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC1E,CAAC;IAED,oDAAoD;IACpD,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,oDAAoD;IACpD,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,oDAAoD;IACpD,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;;OASG;IACH,YAAY,KAAa,EAAE,UAAU,GAAG,CAAC,EAAE,mBAAqC,CAAC,EAAE,KAAqB,EAAE,KAAqB,EAAE,KAAqB,EAAE,aAAa,GAAG,CAAC;QAhDjK,sBAAiB,GAAG,CAAC,CAAC;QACtB,eAAU,GAAG,KAAK,CAAC;QAE3B;;WAEG;QACI,UAAK,GAAoB,IAAI,CAAC;QAE7B,gBAAW,GAAG,CAAC,CAAC;QAyCpB,KAAK,GAAG,KAAK,IAAW,WAAW,CAAC,gBAAgB,CAAC;QACrD,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,kBAAkB,GAAG,IAAI,gBAAgB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACxE,kBAAkB,CAAC,eAAe,GAAG,IAAI,CAAC;YAC1C,kBAAkB,CAAC,aAAa,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC3D,KAAK,GAAG,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,oBAAoB,GAAG,IAAI,gBAAgB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAC1E,oBAAoB,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5C,oBAAoB,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/D,KAAK,GAAG,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE,oBAAoB,EAAE,aAAa,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,mBAAmB,GAAG,IAAI,gBAAgB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACzE,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC;YAC3C,mBAAmB,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7D,KAAK,GAAG,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE,mBAAmB,EAAE,aAAa,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;YAC3B,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAC/D,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAC/D,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,EAAE,EAAE,OAAO,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,QAAiB,EAAE,KAAc,EAAE,KAAc,EAAE,KAAc;QAC3E,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEhC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEhC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,MAAM,KAAK,GAAG,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3E,MAAM,KAAK,GAAG,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3E,MAAM,KAAK,GAAG,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3E,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,KAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC3F,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,yBAAyB;IAClB,OAAO;QACV,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,IAAmB,EAAE,EAAU;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACrC,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC/B,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["import { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { AxisDragGizmo } from \"../Gizmos/axisDragGizmo\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\n\r\n/**\r\n * The Axes viewer will show 3 axes in a specific point in space\r\n * @see https://doc.babylonjs.com/toolsAndResources/utilities/World_Axes\r\n */\r\nexport class AxesViewer {\r\n    private _xAxis: TransformNode;\r\n    private _yAxis: TransformNode;\r\n    private _zAxis: TransformNode;\r\n    private _scaleLinesFactor = 4;\r\n    private _instanced = false;\r\n\r\n    /**\r\n     * Gets the hosting scene\r\n     */\r\n    public scene: Nullable<Scene> = null;\r\n\r\n    private _scaleLines = 1;\r\n    /**\r\n     * Gets or sets a number used to scale line length\r\n     */\r\n    public get scaleLines() {\r\n        return this._scaleLines;\r\n    }\r\n\r\n    public set scaleLines(value: number) {\r\n        this._scaleLines = value;\r\n        this._xAxis.scaling.setAll(this._scaleLines * this._scaleLinesFactor);\r\n        this._yAxis.scaling.setAll(this._scaleLines * this._scaleLinesFactor);\r\n        this._zAxis.scaling.setAll(this._scaleLines * this._scaleLinesFactor);\r\n    }\r\n\r\n    /** Gets the node hierarchy used to render x-axis */\r\n    public get xAxis(): TransformNode {\r\n        return this._xAxis;\r\n    }\r\n\r\n    /** Gets the node hierarchy used to render y-axis */\r\n    public get yAxis(): TransformNode {\r\n        return this._yAxis;\r\n    }\r\n\r\n    /** Gets the node hierarchy used to render z-axis */\r\n    public get zAxis(): TransformNode {\r\n        return this._zAxis;\r\n    }\r\n\r\n    /**\r\n     * Creates a new AxesViewer\r\n     * @param scene defines the hosting scene\r\n     * @param scaleLines defines a number used to scale line length (1 by default)\r\n     * @param renderingGroupId defines a number used to set the renderingGroupId of the meshes (2 by default)\r\n     * @param xAxis defines the node hierarchy used to render the x-axis\r\n     * @param yAxis defines the node hierarchy used to render the y-axis\r\n     * @param zAxis defines the node hierarchy used to render the z-axis\r\n     * @param lineThickness The line thickness to use when creating the arrow. defaults to 1.\r\n     */\r\n    constructor(scene?: Scene, scaleLines = 1, renderingGroupId: Nullable<number> = 2, xAxis?: TransformNode, yAxis?: TransformNode, zAxis?: TransformNode, lineThickness = 1) {\r\n        scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        if (!xAxis) {\r\n            const redColoredMaterial = new StandardMaterial(\"xAxisMaterial\", scene);\r\n            redColoredMaterial.disableLighting = true;\r\n            redColoredMaterial.emissiveColor = Color3.Red().scale(0.5);\r\n            xAxis = AxisDragGizmo._CreateArrow(scene, redColoredMaterial, lineThickness);\r\n        }\r\n\r\n        if (!yAxis) {\r\n            const greenColoredMaterial = new StandardMaterial(\"yAxisMaterial\", scene);\r\n            greenColoredMaterial.disableLighting = true;\r\n            greenColoredMaterial.emissiveColor = Color3.Green().scale(0.5);\r\n            yAxis = AxisDragGizmo._CreateArrow(scene, greenColoredMaterial, lineThickness);\r\n        }\r\n\r\n        if (!zAxis) {\r\n            const blueColoredMaterial = new StandardMaterial(\"zAxisMaterial\", scene);\r\n            blueColoredMaterial.disableLighting = true;\r\n            blueColoredMaterial.emissiveColor = Color3.Blue().scale(0.5);\r\n            zAxis = AxisDragGizmo._CreateArrow(scene, blueColoredMaterial, lineThickness);\r\n        }\r\n\r\n        this._xAxis = xAxis;\r\n        this._yAxis = yAxis;\r\n        this._zAxis = zAxis;\r\n\r\n        this.scaleLines = scaleLines;\r\n\r\n        if (renderingGroupId != null) {\r\n            AxesViewer._SetRenderingGroupId(this._xAxis, renderingGroupId);\r\n            AxesViewer._SetRenderingGroupId(this._yAxis, renderingGroupId);\r\n            AxesViewer._SetRenderingGroupId(this._zAxis, renderingGroupId);\r\n        }\r\n\r\n        this.scene = scene;\r\n        this.update(new Vector3(), Vector3.Right(), Vector3.Up(), Vector3.Forward());\r\n    }\r\n\r\n    /**\r\n     * Force the viewer to update\r\n     * @param position defines the position of the viewer\r\n     * @param xaxis defines the x axis of the viewer\r\n     * @param yaxis defines the y axis of the viewer\r\n     * @param zaxis defines the z axis of the viewer\r\n     */\r\n    public update(position: Vector3, xaxis: Vector3, yaxis: Vector3, zaxis: Vector3): void {\r\n        this._xAxis.position.copyFrom(position);\r\n        this._xAxis.setDirection(xaxis);\r\n\r\n        this._yAxis.position.copyFrom(position);\r\n        this._yAxis.setDirection(yaxis);\r\n\r\n        this._zAxis.position.copyFrom(position);\r\n        this._zAxis.setDirection(zaxis);\r\n    }\r\n\r\n    /**\r\n     * Creates an instance of this axes viewer.\r\n     * @returns a new axes viewer with instanced meshes\r\n     */\r\n    public createInstance(): AxesViewer {\r\n        const xAxis = AxisDragGizmo._CreateArrowInstance(this.scene!, this._xAxis);\r\n        const yAxis = AxisDragGizmo._CreateArrowInstance(this.scene!, this._yAxis);\r\n        const zAxis = AxisDragGizmo._CreateArrowInstance(this.scene!, this._zAxis);\r\n        const axesViewer = new AxesViewer(this.scene!, this.scaleLines, null, xAxis, yAxis, zAxis);\r\n        axesViewer._instanced = true;\r\n        return axesViewer;\r\n    }\r\n\r\n    /** Releases resources */\r\n    public dispose() {\r\n        if (this._xAxis) {\r\n            this._xAxis.dispose(false, !this._instanced);\r\n        }\r\n\r\n        if (this._yAxis) {\r\n            this._yAxis.dispose(false, !this._instanced);\r\n        }\r\n\r\n        if (this._zAxis) {\r\n            this._zAxis.dispose(false, !this._instanced);\r\n        }\r\n\r\n        this.scene = null;\r\n    }\r\n\r\n    private static _SetRenderingGroupId(node: TransformNode, id: number) {\r\n        const meshes = node.getChildMeshes();\r\n        for (const mesh of meshes) {\r\n            mesh.renderingGroupId = id;\r\n        }\r\n    }\r\n}\r\n"]}