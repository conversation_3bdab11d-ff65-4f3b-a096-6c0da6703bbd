{"version": 3, "file": "abstractEngine.cubeTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.cubeTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAE7C,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,2BAA2B,EAAE,iEAA6D;AACnG,OAAO,EAAE,mBAAmB,EAAE,+BAA2B;AA6DzD,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG,UACxC,GAAW,EACX,KAAa,EACb,WAA0B,EAC1B,QAAwC,EACxC,kBAAyE,IAAI;IAE7E,MAAM,MAAM,GAAG,CAAC,IAAiB,EAAE,EAAE;QACjC,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QACpB,WAAY,CAAC,cAAc,EAAE,CAAC;QAEpC,IAAU,WAAY,CAAC,cAAc,KAAK,CAAC,EAAE,CAAC;YAC1C,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,OAAO,GAAG,CAAC,OAAqB,EAAE,SAAe,EAAE,EAAE;QACvD,IAAI,eAAe,IAAI,OAAO,EAAE,CAAC;YAC7B,eAAe,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,MAA8C,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7G,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,iBAAiB,GAAG,UACzC,KAAsB,EACtB,QAAyC,EACzC,KAAe,EACf,UAAiE,IAAI;IAErE,MAAM,WAAW,GAAkB,EAAE,CAAC;IAChC,WAAY,CAAC,cAAc,GAAG,CAAC,CAAC;IAEtC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;QACrC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC/E,CAAC;AACL,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG,UACxC,KAAsB,EACtB,OAAwB,EACxB,QAAkG,EAClG,KAAe,EACf,UAAiE,IAAI,EACrE,QAAiB;IAEjB,MAAM,YAAY,GAAuC,EAAE,CAAC;IACtD,YAAa,CAAC,cAAc,GAAG,CAAC,CAAC;IAEvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;QACrC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IACzG,CAAC;AACL,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG,UACvC,GAAW,EACX,KAAa,EACb,YAAgD,EAChD,KAAsB,EACtB,OAAwB,EACxB,QAAkG,EAClG,kBAAyE,IAAI,EAC7E,QAAiB;IAEjB,MAAM,gBAAgB,GAAG,UAAU,EAAE,CAAC;IAEtC,MAAM,MAAM,GAAG,CAAC,GAAmC,EAAE,EAAE;QACnD,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;QACpB,YAAa,CAAC,cAAc,EAAE,CAAC;QAErC,IAAI,KAAK,EAAE,CAAC;YACR,KAAK,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QAC9C,CAAC;QAED,IAAU,YAAa,CAAC,cAAc,KAAK,CAAC,IAAI,QAAQ,EAAE,CAAC;YACvD,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACpC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,OAAO,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;QAClD,IAAI,KAAK,EAAE,CAAC;YACR,KAAK,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YAClB,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QACxC,CAAC;IACL,CAAC,CAAC;IAEF,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAChF,IAAI,KAAK,EAAE,CAAC;QACR,KAAK,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAC3C,CAAC;AACL,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAC7C,OAAe,EACf,KAAsB,EACtB,KAAyB,EACzB,QAAkB,EAClB,SAAyC,IAAI,EAC7C,UAAiE,IAAI,EACrE,MAAe,EACf,kBAAuB,IAAI,EAC3B,oBAA6B,KAAK,EAClC,WAAmB,CAAC,EACpB,YAAoB,CAAC,EACrB,WAAsC,IAAI,EAC1C,6BAAsH,IAAI,EAC1H,eAAuG,IAAI,EAC3G,aAAa,GAAG,KAAK,EACrB,SAAoC,IAAI;IAExC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,IAAI,qCAA6B,CAAC;IAC5F,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC;IACtB,OAAO,CAAC,eAAe,GAAG,CAAC,QAAQ,CAAC;IACpC,OAAO,CAAC,mBAAmB,GAAG,QAAQ,CAAC;IACvC,OAAO,CAAC,oBAAoB,GAAG,SAAS,CAAC;IACzC,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC/H,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;QACvB,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,gDAAgD;IAC9F,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,UAAU,GAAG,eAAe,CAAC;QACrC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;IAC7B,CAAC;IAED,MAAM,eAAe,GAAG,OAAO,CAAC;IAChC,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzC,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IACjD,CAAC;IAED,MAAM,SAAS,GAAG,eAAe,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;IAElE,MAAM,aAAa,GAAG,2BAA2B,CAAC,SAAS,CAAC,CAAC;IAE7D,MAAM,YAAY,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;QACvD,+FAA+F;QAC/F,OAAO,CAAC,OAAO,EAAE,CAAC;QAClB,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAChC,CAAC;aAAM,IAAI,OAAO,EAAE,CAAC;YACjB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,CAAC,OAAqB,EAAE,SAAe,EAAE,EAAE;QAC/D,IAAI,OAAO,KAAK,eAAe,EAAE,CAAC;YAC9B,IAAI,OAAO,EAAE,CAAC;gBACV,YAAY,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACvE,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,qEAAqE;YACrE,MAAM,CAAC,IAAI,CAAC,kBAAkB,OAAO,yBAAyB,eAAe,EAAE,CAAC,CAAC;YACjF,IAAI,CAAC,qBAAqB,CACtB,eAAe,EACf,KAAK,EACL,KAAK,EACL,CAAC,CAAC,QAAQ,EACV,MAAM,EACN,YAAY,EACZ,MAAM,EACN,eAAe,EACf,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,OAAO,EACP,0BAA0B,EAC1B,YAAY,EACZ,aAAa,EACb,MAAM,CACT,CAAC;QACN,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,aAAa,EAAE,CAAC;QAChB,mFAAmF;QACnF,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YAC1B,MAAM,UAAU,GAAG,CAAC,IAAyC,EAAE,EAAE;gBAC7D,IAAI,0BAA0B,EAAE,CAAC;oBAC7B,0BAA0B,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC9C,CAAC;gBACD,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,MAAM,EAAE,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;oBAChG,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;YACF,IAAI,MAAM,EAAE,CAAC;gBACT,UAAU,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;iBAAM,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrC,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;gBAC7H,CAAC;qBAAM,CAAC;oBACJ,YAAY,CAAC,0CAA0C,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,IAAmB,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;YACpI,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,0FAA0F,CAAC,CAAC;QAChH,CAAC;QAED,IAAI,CAAC,gBAAgB,CACjB,KAAK,EACL,OAAO,EACP,CAAC,OAAwB,EAAE,IAAwC,EAAE,EAAE;YACnE,IAAI,YAAY,EAAE,CAAC;gBACf,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAChC,CAAC;QACL,CAAC,EACD,KAAK,EACL,YAAY,CACf,CAAC;IACN,CAAC;IAED,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC", "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { LoadImage } from \"../../Misc/fileTools\";\r\nimport { RandomGUID } from \"../../Misc/guid\";\r\nimport type { IWebRequest } from \"../../Misc/interfaces/iWebRequest\";\r\nimport { AbstractEngine } from \"../abstractEngine\";\r\nimport { _GetCompatibleTextureLoader } from \"core/Materials/Textures/Loaders/textureLoaderManager\";\r\nimport { GetExtensionFromUrl } from \"core/Misc/urlTools\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /** @internal */\r\n        createCubeTextureBase(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number,\r\n            fallback: Nullable<InternalTexture>,\r\n            beforeLoadCubeDataCallback: Nullable<(texture: InternalTexture, data: ArrayBufferView | ArrayBufferView[]) => void>,\r\n            imageHandler: Nullable<(texture: InternalTexture, imgs: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n            useSRGBBuffer: boolean,\r\n            buffer: Nullable<ArrayBufferView>\r\n        ): InternalTexture;\r\n\r\n        /** @internal */\r\n        _partialLoadFile(\r\n            url: string,\r\n            index: number,\r\n            loadedFiles: ArrayBuffer[],\r\n            onfinish: (files: ArrayBuffer[]) => void,\r\n            onErrorCallBack: Nullable<(message?: string, exception?: any) => void>\r\n        ): void;\r\n\r\n        /** @internal */\r\n        _cascadeLoadFiles(scene: Nullable<Scene>, onfinish: (images: ArrayBuffer[]) => void, files: string[], onError: Nullable<(message?: string, exception?: any) => void>): void;\r\n\r\n        /** @internal */\r\n        _cascadeLoadImgs(\r\n            scene: Nullable<Scene>,\r\n            texture: InternalTexture,\r\n            onfinish: Nullable<(texture: InternalTexture, images: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n            files: string[],\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            mimeType?: string\r\n        ): void;\r\n\r\n        /** @internal */\r\n        _partialLoadImg(\r\n            url: string,\r\n            index: number,\r\n            loadedImages: HTMLImageElement[] | ImageBitmap[],\r\n            scene: Nullable<Scene>,\r\n            texture: InternalTexture,\r\n            onfinish: Nullable<(texture: InternalTexture, images: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n            onErrorCallBack: Nullable<(message?: string, exception?: any) => void>,\r\n            mimeType?: string\r\n        ): void;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype._partialLoadFile = function (\r\n    url: string,\r\n    index: number,\r\n    loadedFiles: ArrayBuffer[],\r\n    onfinish: (files: ArrayBuffer[]) => void,\r\n    onErrorCallBack: Nullable<(message?: string, exception?: any) => void> = null\r\n): void {\r\n    const onload = (data: ArrayBuffer) => {\r\n        loadedFiles[index] = data;\r\n        (<any>loadedFiles)._internalCount++;\r\n\r\n        if ((<any>loadedFiles)._internalCount === 6) {\r\n            onfinish(loadedFiles);\r\n        }\r\n    };\r\n\r\n    const onerror = (request?: IWebRequest, exception?: any) => {\r\n        if (onErrorCallBack && request) {\r\n            onErrorCallBack(request.status + \" \" + request.statusText, exception);\r\n        }\r\n    };\r\n\r\n    this._loadFile(url, onload as (data: string | ArrayBuffer) => void, undefined, undefined, true, onerror);\r\n};\r\n\r\nAbstractEngine.prototype._cascadeLoadFiles = function (\r\n    scene: Nullable<Scene>,\r\n    onfinish: (images: ArrayBuffer[]) => void,\r\n    files: string[],\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null\r\n): void {\r\n    const loadedFiles: ArrayBuffer[] = [];\r\n    (<any>loadedFiles)._internalCount = 0;\r\n\r\n    for (let index = 0; index < 6; index++) {\r\n        this._partialLoadFile(files[index], index, loadedFiles, onfinish, onError);\r\n    }\r\n};\r\n\r\nAbstractEngine.prototype._cascadeLoadImgs = function (\r\n    scene: Nullable<Scene>,\r\n    texture: InternalTexture,\r\n    onfinish: Nullable<(texture: InternalTexture, images: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n    files: string[],\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    mimeType?: string\r\n) {\r\n    const loadedImages: HTMLImageElement[] | ImageBitmap[] = [];\r\n    (<any>loadedImages)._internalCount = 0;\r\n\r\n    for (let index = 0; index < 6; index++) {\r\n        this._partialLoadImg(files[index], index, loadedImages, scene, texture, onfinish, onError, mimeType);\r\n    }\r\n};\r\n\r\nAbstractEngine.prototype._partialLoadImg = function (\r\n    url: string,\r\n    index: number,\r\n    loadedImages: HTMLImageElement[] | ImageBitmap[],\r\n    scene: Nullable<Scene>,\r\n    texture: InternalTexture,\r\n    onfinish: Nullable<(texture: InternalTexture, images: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n    onErrorCallBack: Nullable<(message?: string, exception?: any) => void> = null,\r\n    mimeType?: string\r\n) {\r\n    const tokenPendingData = RandomGUID();\r\n\r\n    const onload = (img: HTMLImageElement | ImageBitmap) => {\r\n        loadedImages[index] = img;\r\n        (<any>loadedImages)._internalCount++;\r\n\r\n        if (scene) {\r\n            scene.removePendingData(tokenPendingData);\r\n        }\r\n\r\n        if ((<any>loadedImages)._internalCount === 6 && onfinish) {\r\n            onfinish(texture, loadedImages);\r\n        }\r\n    };\r\n\r\n    const onerror = (message?: string, exception?: any) => {\r\n        if (scene) {\r\n            scene.removePendingData(tokenPendingData);\r\n        }\r\n\r\n        if (onErrorCallBack) {\r\n            onErrorCallBack(message, exception);\r\n        }\r\n    };\r\n\r\n    LoadImage(url, onload, onerror, scene ? scene.offlineProvider : null, mimeType);\r\n    if (scene) {\r\n        scene.addPendingData(tokenPendingData);\r\n    }\r\n};\r\n\r\nAbstractEngine.prototype.createCubeTextureBase = function (\r\n    rootUrl: string,\r\n    scene: Nullable<Scene>,\r\n    files: Nullable<string[]>,\r\n    noMipmap?: boolean,\r\n    onLoad: Nullable<(data?: any) => void> = null,\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    format?: number,\r\n    forcedExtension: any = null,\r\n    createPolynomials: boolean = false,\r\n    lodScale: number = 0,\r\n    lodOffset: number = 0,\r\n    fallback: Nullable<InternalTexture> = null,\r\n    beforeLoadCubeDataCallback: Nullable<(texture: InternalTexture, data: ArrayBufferView | ArrayBufferView[]) => void> = null,\r\n    imageHandler: Nullable<(texture: InternalTexture, imgs: HTMLImageElement[] | ImageBitmap[]) => void> = null,\r\n    useSRGBBuffer = false,\r\n    buffer: Nullable<ArrayBufferView> = null\r\n): InternalTexture {\r\n    const texture = fallback ? fallback : new InternalTexture(this, InternalTextureSource.Cube);\r\n    texture.isCube = true;\r\n    texture.url = rootUrl;\r\n    texture.generateMipMaps = !noMipmap;\r\n    texture._lodGenerationScale = lodScale;\r\n    texture._lodGenerationOffset = lodOffset;\r\n    texture._useSRGBBuffer = !!useSRGBBuffer && this._caps.supportSRGBBuffers && (this.version > 1 || this.isWebGPU || !!noMipmap);\r\n    if (texture !== fallback) {\r\n        texture.label = rootUrl.substring(0, 60); // default label, can be overriden by the caller\r\n    }\r\n\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._extension = forcedExtension;\r\n        texture._files = files;\r\n        texture._buffer = buffer;\r\n    }\r\n\r\n    const originalRootUrl = rootUrl;\r\n    if (this._transformTextureUrl && !fallback) {\r\n        rootUrl = this._transformTextureUrl(rootUrl);\r\n    }\r\n\r\n    const extension = forcedExtension ?? GetExtensionFromUrl(rootUrl);\r\n\r\n    const loaderPromise = _GetCompatibleTextureLoader(extension);\r\n\r\n    const localOnError = (message?: string, exception?: any) => {\r\n        // if an error was thrown during load, dispose the texture, otherwise it will stay in the cache\r\n        texture.dispose();\r\n        if (onError) {\r\n            onError(message, exception);\r\n        } else if (message) {\r\n            Logger.Warn(message);\r\n        }\r\n    };\r\n\r\n    const onInternalError = (request?: IWebRequest, exception?: any) => {\r\n        if (rootUrl === originalRootUrl) {\r\n            if (request) {\r\n                localOnError(request.status + \" \" + request.statusText, exception);\r\n            }\r\n        } else {\r\n            // fall back to the original url if the transformed url fails to load\r\n            Logger.Warn(`Failed to load ${rootUrl}, falling back to the ${originalRootUrl}`);\r\n            this.createCubeTextureBase(\r\n                originalRootUrl,\r\n                scene,\r\n                files,\r\n                !!noMipmap,\r\n                onLoad,\r\n                localOnError,\r\n                format,\r\n                forcedExtension,\r\n                createPolynomials,\r\n                lodScale,\r\n                lodOffset,\r\n                texture,\r\n                beforeLoadCubeDataCallback,\r\n                imageHandler,\r\n                useSRGBBuffer,\r\n                buffer\r\n            );\r\n        }\r\n    };\r\n\r\n    if (loaderPromise) {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n        loaderPromise.then((loader) => {\r\n            const onLoadData = (data: ArrayBufferView | ArrayBufferView[]) => {\r\n                if (beforeLoadCubeDataCallback) {\r\n                    beforeLoadCubeDataCallback(texture, data);\r\n                }\r\n                loader.loadCubeData(data, texture, createPolynomials, onLoad, (message?: string, exception?: any) => {\r\n                    localOnError(message, exception);\r\n                });\r\n            };\r\n            if (buffer) {\r\n                onLoadData(buffer);\r\n            } else if (files && files.length === 6) {\r\n                if (loader.supportCascades) {\r\n                    this._cascadeLoadFiles(scene, (images) => onLoadData(images.map((image) => new Uint8Array(image))), files, localOnError);\r\n                } else {\r\n                    localOnError(\"Textures type does not support cascades.\");\r\n                }\r\n            } else {\r\n                this._loadFile(rootUrl, (data) => onLoadData(new Uint8Array(data as ArrayBuffer)), undefined, undefined, true, onInternalError);\r\n            }\r\n        });\r\n    } else {\r\n        if (!files || files.length === 0) {\r\n            throw new Error(\"Cannot load cubemap because files were not defined, or the correct loader was not found.\");\r\n        }\r\n\r\n        this._cascadeLoadImgs(\r\n            scene,\r\n            texture,\r\n            (texture: InternalTexture, imgs: HTMLImageElement[] | ImageBitmap[]) => {\r\n                if (imageHandler) {\r\n                    imageHandler(texture, imgs);\r\n                }\r\n            },\r\n            files,\r\n            localOnError\r\n        );\r\n    }\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    return texture;\r\n};\r\n"]}