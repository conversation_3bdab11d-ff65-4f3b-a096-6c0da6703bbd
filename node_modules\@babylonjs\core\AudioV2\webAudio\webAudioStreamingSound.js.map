{"version": 3, "file": "webAudioStreamingSound.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/webAudio/webAudioStreamingSound.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAKzC,OAAO,EAAE,cAAc,EAAE,MAAM,iCAAiC,CAAC;AACjE,OAAO,EAAE,uBAAuB,EAAE,MAAM,yCAAyC,CAAC;AAClF,OAAO,EAAE,uBAAuB,EAAE,MAAM,qDAAqD,CAAC;AAE9F,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAE1C,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAMnE,gBAAgB;AAChB,MAAM,OAAO,uBAAwB,SAAQ,cAAc;IAkBvD,gBAAgB;IAChB,YAAmB,IAAY,EAAE,MAAuB,EAAE,OAAwC;QAC9F,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAnBhB,aAAQ,GAA4B,IAAI,CAAC;QAChC,uBAAkB,GAAY,IAAI,CAAC;QACnC,0BAAqB,GAAW,CAAC,CAAC;QAC3C,YAAO,GAA2B,IAAI,CAAC;QAkB3C,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG;YACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK;YACnC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;YAC3B,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,QAAQ;YAC9C,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC;YACvC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;SACxC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,uBAAuB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,UAAU,CAAC,MAAgC,EAAE,OAAwC;QAC9F,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAE/C,IAAI,CAAC,CAAC,YAAY,YAAY,YAAY,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,CAAC;aAAM,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,IAAI,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,IAAoB,OAAO;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED,gBAAgB;IAChB,IAAoB,MAAM;QACtB,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,yBAAyB,CAAC;IACrC,CAAC;IAES,eAAe;QACrB,OAAO,IAAI,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpE,CAAC;IAEkB,QAAQ,CAAC,IAAqB;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9G,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;;AAEc,iCAAS,GAAG,KAAM,SAAQ,4BAA4B;IAGjE,IAAc,gBAAgB;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAChD,CAAC;IAED,IAAc,cAAc;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC;IAC9C,CAAC;CACJ,AAVuB,CAUtB;AAGN,gBAAgB;AAChB,MAAM,+BAAgC,SAAQ,uBAAuB;IAmBjE,YAAmB,KAA8B,EAAE,OAAqC;QACpF,KAAK,CAAC,KAAK,CAAC,CAAC;QAnBT,mCAA8B,GAAG,KAAK,CAAC;QACvC,oBAAe,GAAW,QAAQ,CAAC;QACnC,qBAAgB,GAAW,CAAC,CAAC;QAC7B,aAAQ,GAAY,KAAK,CAAC;QAC1B,oBAAe,GAA8B,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjF,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC;YACtC,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC;QACxC,CAAC,CAAC,CAAC;QA4NK,sBAAiB,GAAe,GAAG,EAAE;YACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC;QAEM,aAAQ,GAAe,GAAG,EAAE;YAChC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC;QAEM,aAAQ,GAA0B,CAAC,MAAW,EAAE,EAAE;YACtD,IAAI,CAAC,SAAS,kCAA0B,CAAC;YACzC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC;QAEM,0BAAqB,GAAG,GAAG,EAAE;YACjC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAClC,OAAO;YACX,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,gCAAwB,EAAE,CAAC;gBAC3D,IAAI,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAClF,CAAC,CAAC;QAEM,mBAAc,GAAG,GAAG,EAAE;YAC1B,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC,CAAC;QA9OE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAErD,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACpC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,YAAY,gBAAgB,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,MAAM,+BAAuB,EAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,8BAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QAClH,OAAO,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IAClF,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,gCAAwB,IAAI,IAAI,CAAC,MAAM,+BAAuB,CAAC;QAE1F,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,4BAAoB,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;QAElC,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;QACtC,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,8BAAsB,EAAE,CAAC;YAC3C,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;QAC/C,CAAC;IACL,CAAC;IAED,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,gBAAgB;IAChB,IAAW,SAAS;QAChB,IAAI,IAAI,CAAC,MAAM,+BAAuB,EAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEjF,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3D,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9E,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC1E,CAAC;IAED,gBAAgB;IACT,IAAI,CAAC,UAA+C,EAAE;QACzD,IAAI,IAAI,CAAC,MAAM,+BAAuB,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAE7C,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAEtC,IAAI,IAAI,CAAC,8BAA8B,EAAE,CAAC;YACtC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YACxC,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAChD,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,8BAAsB,EAAE,CAAC;YAC3C,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC/D,CAAC;QAED,IAAI,WAAW,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;QAElD,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,gBAAgB;IACT,KAAK;QACR,IAAI,IAAI,CAAC,MAAM,gCAAwB,IAAI,IAAI,CAAC,MAAM,+BAAuB,EAAE,CAAC;YAC5E,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,2BAAmB,CAAC;QAClC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QAExE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED,gBAAgB;IACT,MAAM;QACT,IAAI,IAAI,CAAC,MAAM,8BAAsB,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;aAAM,IAAI,IAAI,CAAC,8BAA8B,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;IACL,CAAC;IAED,gBAAgB;IACA,IAAI;QAChB,IAAI,IAAI,CAAC,MAAM,+BAAuB,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,iCAAiC,CAAC;IAC7C,CAAC;IAEkB,QAAQ,CAAC,IAAuB;QAC/C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,gGAAgG;QAChG,IAAI,IAAI,YAAY,uBAAuB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1D,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAuB;QAClD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,YAAY,uBAAuB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1D,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,qBAAqB,CAAC,YAA8B;QACxD,KAAK,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAE7D,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC9B,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACvC,YAAY,CAAC,OAAO,GAAG,MAAM,CAAC;QAE9B,YAAY,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QACxF,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QACtE,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;QAEtE,YAAY,CAAC,IAAI,EAAE,CAAC;QAEpB,IAAI,CAAC,WAAW,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC,CAAC;QAC9G,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE3C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACtC,CAAC;IAEO,YAAY,CAAC,GAAW;QAC5B,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAEO,aAAa,CAAC,IAAc;QAChC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAE1B,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;YAC5B,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAoCO,KAAK;QACT,IAAI,CAAC,SAAS,6BAAqB,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,gCAAwB,EAAE,CAAC;YACtC,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAEzC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAC/C,IAAI,CAAC,SAAS,4BAAoB,CAAC;YAEnC,uGAAuG;YACvG,2GAA2G;YAC3G,2FAA2F;YAC3F,0CAA0C;YAC1C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,SAAS,kCAA0B,CAAC;gBAEzC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACrB,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACnE,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACvE,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,kCAA0B,CAAC;QAC7C,CAAC;IACL,CAAC;IAEO,cAAc;QAClB,IAAI,CAAC,eAAe;YAChB,0CAA0C;aACzC,IAAI,CAAC,GAAG,EAAE;YACP,IAAI,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC,CAAC;YACF,0CAA0C;aACzC,KAAK,CAAC,GAAG,EAAE;YACR,MAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACxD,IAAI,CAAC,SAAS,kCAA0B,CAAC;QAC7C,CAAC,CAAC,CAAC;IACX,CAAC;IAKO,KAAK;QACT,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,4BAAoB,CAAC;QACnC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAClF,CAAC;CACJ", "sourcesContent": ["import { Logger } from \"../../Misc/logger\";\nimport { Tools } from \"../../Misc/tools\";\nimport type { Nullable } from \"../../types\";\nimport type { AbstractAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type {} from \"../abstractAudio/abstractSound\";\nimport type { IStreamingSoundOptions, IStreamingSoundPlayOptions, IStreamingSoundStoredOptions } from \"../abstractAudio/streamingSound\";\nimport { StreamingSound } from \"../abstractAudio/streamingSound\";\nimport { _StreamingSoundInstance } from \"../abstractAudio/streamingSoundInstance\";\nimport { _HasSpatialAudioOptions } from \"../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { _SpatialAudio } from \"../abstractAudio/subProperties/spatialAudio\";\nimport { _StereoAudio } from \"../abstractAudio/subProperties/stereoAudio\";\nimport { _CleanUrl } from \"../audioUtils\";\nimport { SoundState } from \"../soundState\";\nimport { _WebAudioBusAndSoundSubGraph } from \"./subNodes/webAudioBusAndSoundSubGraph\";\nimport { _SpatialWebAudio } from \"./subProperties/spatialWebAudio\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioOutNode, IWebAudioSuperNode } from \"./webAudioNode\";\n\ntype StreamingSoundSourceType = HTMLMediaElement | string | string[];\n\n/** @internal */\nexport class _WebAudioStreamingSound extends StreamingSound implements IWebAudioSuperNode {\n    private _spatial: Nullable<_SpatialAudio> = null;\n    private readonly _spatialAutoUpdate: boolean = true;\n    private readonly _spatialMinUpdateTime: number = 0;\n    private _stereo: Nullable<_StereoAudio> = null;\n\n    protected override readonly _options: IStreamingSoundStoredOptions;\n    protected _subGraph: _WebAudioBusAndSoundSubGraph;\n\n    /** @internal */\n    public _audioContext: AudioContext;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public _source: StreamingSoundSourceType;\n\n    /** @internal */\n    public constructor(name: string, engine: _WebAudioEngine, options: Partial<IStreamingSoundOptions>) {\n        super(name, engine);\n\n        if (typeof options.spatialAutoUpdate === \"boolean\") {\n            this._spatialAutoUpdate = options.spatialAutoUpdate;\n        }\n\n        if (typeof options.spatialMinUpdateTime === \"number\") {\n            this._spatialMinUpdateTime = options.spatialMinUpdateTime;\n        }\n\n        this._options = {\n            autoplay: options.autoplay ?? false,\n            loop: options.loop ?? false,\n            maxInstances: options.maxInstances ?? Infinity,\n            preloadCount: options.preloadCount ?? 1,\n            startOffset: options.startOffset ?? 0,\n        };\n\n        this._subGraph = new _WebAudioStreamingSound._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(source: StreamingSoundSourceType, options: Partial<IStreamingSoundOptions>): Promise<void> {\n        const audioContext = this.engine._audioContext;\n\n        if (!(audioContext instanceof AudioContext)) {\n            throw new Error(\"Unsupported audio context type.\");\n        }\n\n        this._audioContext = audioContext;\n        this._source = source;\n\n        if (options.outBus) {\n            this.outBus = options.outBus;\n        } else if (options.outBusAutoDefault !== false) {\n            await this.engine.isReadyPromise;\n            this.outBus = this.engine.defaultMainBus;\n        }\n\n        await this._subGraph.initAsync(options);\n\n        if (_HasSpatialAudioOptions(options)) {\n            this._initSpatialProperty();\n        }\n\n        if (this.preloadCount) {\n            await this.preloadInstancesAsync(this.preloadCount);\n        }\n\n        if (options.autoplay) {\n            this.play(options);\n        }\n\n        this.engine._addNode(this);\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._subGraph._inNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    /** @internal */\n    public override get spatial(): _SpatialAudio {\n        if (this._spatial) {\n            return this._spatial;\n        }\n        return this._initSpatialProperty();\n    }\n\n    /** @internal */\n    public override get stereo(): _StereoAudio {\n        return this._stereo ?? (this._stereo = new _StereoAudio(this._subGraph));\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._spatial = null;\n        this._stereo = null;\n\n        this._subGraph.dispose();\n\n        this.engine._removeNode(this);\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioStreamingSound\";\n    }\n\n    protected _createInstance(): _WebAudioStreamingSoundInstance {\n        return new _WebAudioStreamingSoundInstance(this, this._options);\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initSpatialProperty(): _SpatialAudio {\n        if (!this._spatial) {\n            this._spatial = new _SpatialWebAudio(this._subGraph, this._spatialAutoUpdate, this._spatialMinUpdateTime);\n        }\n\n        return this._spatial;\n    }\n\n    private static _SubGraph = class extends _WebAudioBusAndSoundSubGraph {\n        protected override _owner: _WebAudioStreamingSound;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n\n        protected get _upstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._upstreamNodes ?? null;\n        }\n    };\n}\n\n/** @internal */\nclass _WebAudioStreamingSoundInstance extends _StreamingSoundInstance implements IWebAudioOutNode {\n    private _currentTimeChangedWhilePaused = false;\n    private _enginePlayTime: number = Infinity;\n    private _enginePauseTime: number = 0;\n    private _isReady: boolean = false;\n    private _isReadyPromise: Promise<HTMLMediaElement> = new Promise((resolve, reject) => {\n        this._resolveIsReadyPromise = resolve;\n        this._rejectIsReadyPromise = reject;\n    });\n    private _mediaElement: HTMLMediaElement;\n    private _sourceNode: Nullable<MediaElementAudioSourceNode>;\n    private _volumeNode: GainNode;\n\n    protected override readonly _options: IStreamingSoundStoredOptions;\n    protected override _sound: _WebAudioStreamingSound;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    public constructor(sound: _WebAudioStreamingSound, options: IStreamingSoundStoredOptions) {\n        super(sound);\n\n        this._options = options;\n        this._volumeNode = new GainNode(sound._audioContext);\n\n        if (typeof sound._source === \"string\") {\n            this._initFromUrl(sound._source);\n        } else if (Array.isArray(sound._source)) {\n            this._initFromUrls(sound._source);\n        } else if (sound._source instanceof HTMLMediaElement) {\n            this._initFromMediaElement(sound._source);\n        }\n    }\n\n    /** @internal */\n    public get currentTime(): number {\n        if (this._state === SoundState.Stopped) {\n            return 0;\n        }\n\n        const timeSinceLastStart = this._state === SoundState.Paused ? 0 : this.engine.currentTime - this._enginePlayTime;\n        return this._enginePauseTime + timeSinceLastStart + this._options.startOffset;\n    }\n\n    public set currentTime(value: number) {\n        const restart = this._state === SoundState.Starting || this._state === SoundState.Started;\n\n        if (restart) {\n            this._mediaElement.pause();\n            this._setState(SoundState.Stopped);\n        }\n\n        this._options.startOffset = value;\n\n        if (restart) {\n            this.play({ startOffset: value });\n        } else if (this._state === SoundState.Paused) {\n            this._currentTimeChangedWhilePaused = true;\n        }\n    }\n\n    public get _outNode(): Nullable<AudioNode> {\n        return this._volumeNode;\n    }\n\n    /** @internal */\n    public get startTime(): number {\n        if (this._state === SoundState.Stopped) {\n            return 0;\n        }\n\n        return this._enginePlayTime;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this.stop();\n\n        this._sourceNode?.disconnect(this._volumeNode);\n        this._sourceNode = null;\n\n        this._mediaElement.removeEventListener(\"error\", this._onError);\n        this._mediaElement.removeEventListener(\"ended\", this._onEnded);\n        this._mediaElement.removeEventListener(\"canplaythrough\", this._onCanPlayThrough);\n\n        for (const source of Array.from(this._mediaElement.children)) {\n            this._mediaElement.removeChild(source);\n        }\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n        this.engine.userGestureObservable.removeCallback(this._onUserGesture);\n    }\n\n    /** @internal */\n    public play(options: Partial<IStreamingSoundPlayOptions> = {}): void {\n        if (this._state === SoundState.Started) {\n            return;\n        }\n\n        if (options.loop !== undefined) {\n            this._options.loop = options.loop;\n        }\n        this._mediaElement.loop = this._options.loop;\n\n        let startOffset = options.startOffset;\n\n        if (this._currentTimeChangedWhilePaused) {\n            startOffset = this._options.startOffset;\n            this._currentTimeChangedWhilePaused = false;\n        } else if (this._state === SoundState.Paused) {\n            startOffset = this.currentTime + this._options.startOffset;\n        }\n\n        if (startOffset && startOffset > 0) {\n            this._mediaElement.currentTime = startOffset;\n        }\n\n        this._volumeNode.gain.value = options.volume ?? 1;\n\n        this._play();\n    }\n\n    /** @internal */\n    public pause(): void {\n        if (this._state !== SoundState.Starting && this._state !== SoundState.Started) {\n            return;\n        }\n\n        this._setState(SoundState.Paused);\n        this._enginePauseTime += this.engine.currentTime - this._enginePlayTime;\n\n        this._mediaElement.pause();\n    }\n\n    /** @internal */\n    public resume(): void {\n        if (this._state === SoundState.Paused) {\n            this.play();\n        } else if (this._currentTimeChangedWhilePaused) {\n            this.play();\n        }\n    }\n\n    /** @internal */\n    public override stop(): void {\n        if (this._state === SoundState.Stopped) {\n            return;\n        }\n\n        this._stop();\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioStreamingSoundInstance\";\n    }\n\n    protected override _connect(node: AbstractAudioNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the sound's subgraph.\n        if (node instanceof _WebAudioStreamingSound && node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: AbstractAudioNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node instanceof _WebAudioStreamingSound && node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initFromMediaElement(mediaElement: HTMLMediaElement): void {\n        Tools.SetCorsBehavior(mediaElement.currentSrc, mediaElement);\n\n        mediaElement.controls = false;\n        mediaElement.loop = this._options.loop;\n        mediaElement.preload = \"auto\";\n\n        mediaElement.addEventListener(\"canplaythrough\", this._onCanPlayThrough, { once: true });\n        mediaElement.addEventListener(\"ended\", this._onEnded, { once: true });\n        mediaElement.addEventListener(\"error\", this._onError, { once: true });\n\n        mediaElement.load();\n\n        this._sourceNode = new MediaElementAudioSourceNode(this._sound._audioContext, { mediaElement: mediaElement });\n        this._sourceNode.connect(this._volumeNode);\n\n        if (!this._connect(this._sound)) {\n            throw new Error(\"Connect failed\");\n        }\n\n        this._mediaElement = mediaElement;\n    }\n\n    private _initFromUrl(url: string): void {\n        const audio = new Audio(_CleanUrl(url));\n        this._initFromMediaElement(audio);\n    }\n\n    private _initFromUrls(urls: string[]): void {\n        const audio = new Audio();\n\n        for (const url of urls) {\n            const source = document.createElement(\"source\");\n            source.src = _CleanUrl(url);\n            audio.appendChild(source);\n        }\n\n        this._initFromMediaElement(audio);\n    }\n\n    private _onCanPlayThrough: () => void = () => {\n        this._isReady = true;\n        this._resolveIsReadyPromise(this._mediaElement);\n        this.onReadyObservable.notifyObservers(this);\n    };\n\n    private _onEnded: () => void = () => {\n        this.onEndedObservable.notifyObservers(this);\n        this.dispose();\n    };\n\n    private _onError: (reason: any) => void = (reason: any) => {\n        this._setState(SoundState.FailedToStart);\n        this.onErrorObservable.notifyObservers(reason);\n        this._rejectIsReadyPromise(reason);\n        this.dispose();\n    };\n\n    private _onEngineStateChanged = () => {\n        if (this.engine.state !== \"running\") {\n            return;\n        }\n\n        if (this._options.loop && this.state === SoundState.Starting) {\n            this.play();\n        }\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    };\n\n    private _onUserGesture = () => {\n        this.play();\n    };\n\n    private _play(): void {\n        this._setState(SoundState.Starting);\n\n        if (!this._isReady) {\n            this._playWhenReady();\n            return;\n        }\n\n        if (this._state !== SoundState.Starting) {\n            return;\n        }\n\n        if (this.engine.state === \"running\") {\n            const result = this._mediaElement.play();\n\n            this._enginePlayTime = this.engine.currentTime;\n            this._setState(SoundState.Started);\n\n            // It's possible that the play() method fails on Safari, even if the audio engine's state is \"running\".\n            // This occurs when the audio context is paused by the system and resumed automatically by the audio engine\n            // without a user interaction (e.g. when the Vision Pro exits and reenters immersive mode).\n            // eslint-disable-next-line github/no-then\n            result.catch(() => {\n                this._setState(SoundState.FailedToStart);\n\n                if (this._options.loop) {\n                    this.engine.userGestureObservable.addOnce(this._onUserGesture);\n                }\n            });\n        } else if (this._options.loop) {\n            this.engine.stateChangedObservable.add(this._onEngineStateChanged);\n        } else {\n            this.stop();\n            this._setState(SoundState.FailedToStart);\n        }\n    }\n\n    private _playWhenReady(): void {\n        this._isReadyPromise\n            // eslint-disable-next-line github/no-then\n            .then(() => {\n                this._play();\n            })\n            // eslint-disable-next-line github/no-then\n            .catch(() => {\n                Logger.Error(\"Streaming sound instance failed to play\");\n                this._setState(SoundState.FailedToStart);\n            });\n    }\n\n    private _rejectIsReadyPromise: (reason?: any) => void;\n    private _resolveIsReadyPromise: (mediaElement: HTMLMediaElement) => void;\n\n    private _stop(): void {\n        this._mediaElement.pause();\n        this._setState(SoundState.Stopped);\n        this._onEnded();\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    }\n}\n"]}