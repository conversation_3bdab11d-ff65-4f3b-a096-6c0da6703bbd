(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/scripts/babylon-particles.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@babylonjs_84468d91._.js",
  "static/chunks/node_modules_@babylonjs_core_Misc_2670bd70._.js",
  "static/chunks/node_modules_@babylonjs_core_Maths_66d1ceec._.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_thinEngine_3fb6690a.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_Extensions_eb352c16._.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_WebGPU_8e4ce2b0._.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_webgpuEngine_8af33ccc.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_3f66c1a5._.js",
  "static/chunks/node_modules_@babylonjs_core_Animations_97aef4b3._.js",
  "static/chunks/node_modules_@babylonjs_core_Bones_b585a8c4._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Textures_02bb0bcd._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_standardMaterial_b33663ed.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_PBR_c07ded7b._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_Blocks_Fragment_b4c35ae3._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_Blocks_Dual_a660d8fd._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_Blocks_PBR_647c1564._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_Blocks_056522a2._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_8f84abb7._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_b752d330._.js",
  "static/chunks/node_modules_@babylonjs_core_PostProcesses_02d2a58b._.js",
  "static/chunks/node_modules_@babylonjs_core_Rendering_2ebcbf58._.js",
  "static/chunks/node_modules_@babylonjs_core_Lights_138ae6fc._.js",
  "static/chunks/node_modules_@babylonjs_core_scene_3453c960.js",
  "static/chunks/node_modules_@babylonjs_core_Cameras_7ee06a4b._.js",
  "static/chunks/node_modules_@babylonjs_core_Culling_467caafe._.js",
  "static/chunks/node_modules_@babylonjs_core_Meshes_mesh_0c7f1f0f.js",
  "static/chunks/node_modules_@babylonjs_core_Meshes_Builders_c6414030._.js",
  "static/chunks/node_modules_@babylonjs_core_Meshes_Node_2c92a0c0._.js",
  "static/chunks/node_modules_@babylonjs_core_Meshes_d4d8c229._.js",
  "static/chunks/node_modules_@babylonjs_core_Loading_8181ce0e._.js",
  "static/chunks/node_modules_@babylonjs_core_Audio_f1ed3b51._.js",
  "static/chunks/node_modules_@babylonjs_core_AudioV2_f378d7a6._.js",
  "static/chunks/node_modules_@babylonjs_core_Shaders_d48e81eb._.js",
  "static/chunks/node_modules_@babylonjs_core_Behaviors_852578cc._.js",
  "static/chunks/node_modules_@babylonjs_core_XR_225efa64._.js",
  "static/chunks/node_modules_@babylonjs_core_ShadersWGSL_7d39ba2d._.js",
  "static/chunks/node_modules_@babylonjs_core_Gizmos_2a85c942._.js",
  "static/chunks/node_modules_@babylonjs_core_Debug_cd5082e1._.js",
  "static/chunks/node_modules_@babylonjs_core_Physics_b3e4c737._.js",
  "static/chunks/node_modules_@babylonjs_core_FrameGraph_734ee2b1._.js",
  "static/chunks/node_modules_@babylonjs_core_FlowGraph_e5a0200d._.js",
  "static/chunks/node_modules_@babylonjs_core_Layers_eac11ab6._.js",
  "static/chunks/node_modules_@babylonjs_core_Particles_e4ebcfef._.js",
  "static/chunks/node_modules_@babylonjs_core_Sprites_dfcbfa43._.js",
  "static/chunks/node_modules_@babylonjs_core_3c40d0d7._.js",
  "static/chunks/node_modules_@babylonjs_loaders_glTF_bd13c10a._.js",
  "static/chunks/node_modules_f7f89866._.js",
  "static/chunks/src_scripts_babylon-particles_ts_0ef0a9df._.js",
  "static/chunks/src_scripts_babylon-particles_ts_0f570ddd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/scripts/babylon-particles.ts [app-client] (ecmascript)");
    });
});
}),
"[project]/src/scripts/advanced-3d-showcase.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@babylonjs_84468d91._.js",
  "static/chunks/node_modules_@babylonjs_core_Misc_2670bd70._.js",
  "static/chunks/node_modules_@babylonjs_core_Maths_66d1ceec._.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_thinEngine_3fb6690a.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_Extensions_eb352c16._.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_WebGPU_8e4ce2b0._.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_webgpuEngine_8af33ccc.js",
  "static/chunks/node_modules_@babylonjs_core_Engines_3f66c1a5._.js",
  "static/chunks/node_modules_@babylonjs_core_Animations_97aef4b3._.js",
  "static/chunks/node_modules_@babylonjs_core_Bones_b585a8c4._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Textures_02bb0bcd._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_standardMaterial_b33663ed.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_PBR_c07ded7b._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_Blocks_Fragment_b4c35ae3._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_Blocks_Dual_a660d8fd._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_Blocks_PBR_647c1564._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_Blocks_056522a2._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_Node_8f84abb7._.js",
  "static/chunks/node_modules_@babylonjs_core_Materials_b752d330._.js",
  "static/chunks/node_modules_@babylonjs_core_PostProcesses_02d2a58b._.js",
  "static/chunks/node_modules_@babylonjs_core_Rendering_2ebcbf58._.js",
  "static/chunks/node_modules_@babylonjs_core_Lights_138ae6fc._.js",
  "static/chunks/node_modules_@babylonjs_core_scene_3453c960.js",
  "static/chunks/node_modules_@babylonjs_core_Cameras_7ee06a4b._.js",
  "static/chunks/node_modules_@babylonjs_core_Culling_467caafe._.js",
  "static/chunks/node_modules_@babylonjs_core_Meshes_mesh_0c7f1f0f.js",
  "static/chunks/node_modules_@babylonjs_core_Meshes_Builders_c6414030._.js",
  "static/chunks/node_modules_@babylonjs_core_Meshes_Node_2c92a0c0._.js",
  "static/chunks/node_modules_@babylonjs_core_Meshes_d4d8c229._.js",
  "static/chunks/node_modules_@babylonjs_core_Loading_8181ce0e._.js",
  "static/chunks/node_modules_@babylonjs_core_Audio_f1ed3b51._.js",
  "static/chunks/node_modules_@babylonjs_core_AudioV2_f378d7a6._.js",
  "static/chunks/node_modules_@babylonjs_core_Shaders_d48e81eb._.js",
  "static/chunks/node_modules_@babylonjs_core_Behaviors_852578cc._.js",
  "static/chunks/node_modules_@babylonjs_core_XR_225efa64._.js",
  "static/chunks/node_modules_@babylonjs_core_ShadersWGSL_7d39ba2d._.js",
  "static/chunks/node_modules_@babylonjs_core_Gizmos_2a85c942._.js",
  "static/chunks/node_modules_@babylonjs_core_Debug_cd5082e1._.js",
  "static/chunks/node_modules_@babylonjs_core_Physics_b3e4c737._.js",
  "static/chunks/node_modules_@babylonjs_core_FrameGraph_734ee2b1._.js",
  "static/chunks/node_modules_@babylonjs_core_FlowGraph_e5a0200d._.js",
  "static/chunks/node_modules_@babylonjs_core_Layers_eac11ab6._.js",
  "static/chunks/node_modules_@babylonjs_core_Particles_e4ebcfef._.js",
  "static/chunks/node_modules_@babylonjs_core_Sprites_dfcbfa43._.js",
  "static/chunks/node_modules_@babylonjs_core_3c40d0d7._.js",
  "static/chunks/node_modules_@babylonjs_loaders_glTF_bd13c10a._.js",
  "static/chunks/node_modules_f7f89866._.js",
  "static/chunks/src_scripts_advanced-3d-showcase_ts_fc9b932d._.js",
  "static/chunks/src_scripts_advanced-3d-showcase_ts_0f570ddd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/scripts/advanced-3d-showcase.ts [app-client] (ecmascript)");
    });
});
}),
}]);