{"version": 3, "file": "debugLayer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Debug/debugLayer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAGrD,OAAO,EAAE,cAAc,EAAE,qCAAoC;AAmL7D,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE;IACjD,GAAG,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;QAC5C,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,CAAN,IAAkB,aAqBjB;AArBD,WAAkB,aAAa;IAC3B;;OAEG;IACH,6DAAc,CAAA;IACd;;OAEG;IACH,mDAAS,CAAA;IACT;;OAEG;IACH,6DAAc,CAAA;IACd;;OAEG;IACH,mDAAS,CAAA;IACT;;OAEG;IACH,yDAAY,CAAA;AAChB,CAAC,EArBiB,aAAa,KAAb,aAAa,QAqB9B;AAED;;;;GAIG;AACH,MAAM,OAAO,UAAU;IA0BnB;;OAEG;IACH,IAAW,2BAA2B;QAClC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACrC,IAAI,CAAC,4BAA4B,GAAG,IAAI,UAAU,EAAoE,CAAC;QAC3H,CAAC;QAED,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAGD;;OAEG;IACH,IAAW,4BAA4B;QACnC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACtC,IAAI,CAAC,6BAA6B,GAAG,IAAI,UAAU,EAAO,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;;;;;OAMG;IACH,YAAY,KAAa;QA1CzB,gEAAgE;QACxD,iBAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QA0C9C,IAAI,CAAC,MAAM,GAAG,KAAK,IAAW,WAAW,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YACrC,cAAc;YACd,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC1B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YACnC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACK,gBAAgB,CAAC,MAAmC;QACxD,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,CAAC;gBACjE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC1E,CAAC;YACD,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,4BAA4B,GAAG,SAAS,CAAC;QAClD,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,6BAA6B,CAAC,SAAS,EAAE,CAAC;gBAClE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,4BAA4B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC3E,CAAC;YACD,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;YAC3C,IAAI,CAAC,6BAA6B,GAAG,SAAS,CAAC;QACnD,CAAC;QAED,MAAM,WAAW,GAAsB;YACnC,GAAG,UAAU,CAAC,MAAM;YACpB,GAAG,MAAM;SACZ,CAAC;QAEF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEpE,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,MAAW,EAAE,mBAAuC;QAC9D,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,mBAAmB,EAAE,CAAC;gBACtB,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,iBAAiB,EAAE,CAAC;oBAC3E,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,qCAAqC,CAAC,mBAAmB,CAAC,CAAC;gBAC3F,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,8CAA8C,CAAC,mBAAmB,CAAC,CAAC;gBACpG,CAAC;YACL,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;gBACzC,UAAU,CAAC,GAAG,EAAE;oBACZ,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;gBAC7C,CAAC,EAAE,GAAG,CAAC,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,2BAA2B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACpF,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,mBAAmB;QACvB,0DAA0D;QAC1D,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE,CAAC;YACnC,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,mFAAmF;QACnF,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,WAAW,EAAE,CAAC;YAC7E,OAAO,OAAO,CAAC;QACnB,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC;IACtE,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACvC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC;QACnD,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC;IAEM,kBAAkB;QACrB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC;QACrD,CAAC;IACL,CAAC;IAEM,cAAc;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;QACjD,CAAC;IACL,CAAC;IAEM,UAAU;QACb,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,gEAAgE;IACzD,KAAK,CAAC,IAAI,CAAC,MAA0B;QACxC,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,OAAO,IAAI,CAAC,YAAY,IAAI,WAAW,EAAE,CAAC;gBAC1C,MAAM,YAAY,GAAG,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC;gBAEnG,uCAAuC;gBACvC,KAAK,CAAC,iBAAiB,CAAC,YAAY,EAAE,GAAG,EAAE;oBACvC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;oBAC9B,OAAO,CAAC,IAAI,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACJ,kCAAkC;gBAClC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;;AAtOD;;;;GAIG;AACW,uBAAY,GAAG,GAAG,KAAK,CAAC,cAAc,KAAK,cAAc,CAAC,OAAO,wCAAwC,AAA7F,CAA8F;AAExH;;GAEG;AACW,iBAAM,GAAsB;IACtC,OAAO,EAAE,KAAK;IACd,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE,IAAI;IACnB,SAAS,EAAE,KAAK;IAChB,YAAY,EAAE,IAAI;IAClB,WAAW,EAAE,IAAI;CACpB,AAPmB,CAOlB", "sourcesContent": ["import { Tools } from \"../Misc/tools\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Scene } from \"../scene\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { IInspectable } from \"../Misc/iInspectable\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { AbstractEngine } from \"core/Engines/abstractEngine\";\r\n\r\n// declare INSPECTOR namespace for compilation issue\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare let INSPECTOR: any;\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare let BABYLON: any;\r\n// load the inspector using require, if not present in the global namespace.\r\n\r\n/**\r\n * Interface used to define scene explorer extensibility option\r\n */\r\nexport interface IExplorerExtensibilityOption {\r\n    /**\r\n     * Define the option label\r\n     */\r\n    label: string;\r\n    /**\r\n     * Defines the action to execute on click\r\n     */\r\n    action: (entity: any) => void;\r\n    /**\r\n     * Keep popup open after click\r\n     */\r\n    keepOpenAfterClick?: boolean;\r\n}\r\n\r\n/**\r\n * Defines a group of actions associated with a predicate to use when extending the Inspector scene explorer\r\n */\r\nexport interface IExplorerExtensibilityGroup {\r\n    /**\r\n     * Defines a predicate to test if a given type mut be extended\r\n     */\r\n    predicate: (entity: any) => boolean;\r\n    /**\r\n     * Gets the list of options added to a type\r\n     */\r\n    entries: IExplorerExtensibilityOption[];\r\n}\r\n\r\n/**\r\n * Defines a new node that will be displayed as top level node in the explorer\r\n */\r\nexport interface IExplorerAdditionalChild {\r\n    /**\r\n     * Gets the name of the additional node\r\n     */\r\n    name: string;\r\n    /**\r\n     * Function used to return the class name of the child node\r\n     */\r\n    getClassName(): string;\r\n    /**\r\n     * List of inspectable custom properties (used by the Inspector)\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector#extensibility\r\n     */\r\n    inspectableCustomProperties: IInspectable[];\r\n}\r\n\r\n/**\r\n * Defines a new node that will be displayed as top level node in the explorer\r\n */\r\nexport interface IExplorerAdditionalNode {\r\n    /**\r\n     * Gets the name of the additional node\r\n     */\r\n    name: string;\r\n    /**\r\n     * Function used to return the list of child entries\r\n     */\r\n    getContent(): IExplorerAdditionalChild[];\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport type IInspectorContextMenuType = \"pipeline\" | \"node\" | \"materials\" | \"spriteManagers\" | \"particleSystems\" | \"frameGraphs\";\r\n\r\n/**\r\n * Context menu item\r\n */\r\nexport interface IInspectorContextMenuItem {\r\n    /**\r\n     * Display label - menu item\r\n     */\r\n    label: string;\r\n    /**\r\n     * Callback function that will be called when the menu item is selected\r\n     * @param entity the entity that is currently selected in the scene explorer\r\n     */\r\n    action: (entity?: unknown) => void;\r\n}\r\n\r\n/**\r\n * Interface used to define the options to use to create the Inspector\r\n */\r\nexport interface IInspectorOptions {\r\n    /**\r\n     * Display in overlay mode (default: false)\r\n     */\r\n    overlay?: boolean;\r\n    /**\r\n     * HTML element to use as root (the parent of the rendering canvas will be used as default value)\r\n     */\r\n    globalRoot?: HTMLElement;\r\n    /**\r\n     * Display the Scene explorer\r\n     */\r\n    showExplorer?: boolean;\r\n    /**\r\n     * Display the property inspector\r\n     */\r\n    showInspector?: boolean;\r\n    /**\r\n     * Display in embed mode (both panes on the right)\r\n     */\r\n    embedMode?: boolean;\r\n    /**\r\n     * let the Inspector handles resize of the canvas when panes are resized (default to true)\r\n     */\r\n    handleResize?: boolean;\r\n    /**\r\n     * Allow the panes to popup (default: true)\r\n     */\r\n    enablePopup?: boolean;\r\n    /**\r\n     * Allow the panes to be closed by users (default: true)\r\n     */\r\n    enableClose?: boolean;\r\n    /**\r\n     * Optional list of extensibility entries\r\n     */\r\n    explorerExtensibility?: IExplorerExtensibilityGroup[];\r\n    /**\r\n     * Optional list of additional top level nodes\r\n     */\r\n    additionalNodes?: IExplorerAdditionalNode[];\r\n    /**\r\n     * Optional URL to get the inspector script from (by default it uses the babylonjs CDN).\r\n     */\r\n    inspectorURL?: string;\r\n    /**\r\n     * Optional initial tab (default to DebugLayerTab.Properties)\r\n     */\r\n    initialTab?: DebugLayerTab;\r\n    /**\r\n     * Optional camera to use to render the gizmos from the inspector (default to the scene.activeCamera or the latest from scene.activeCameras)\r\n     */\r\n    gizmoCamera?: Camera;\r\n    /**\r\n     * Context menu for inspector tools such as \"Post Process\", \"Nodes\", \"Materials\", etc.\r\n     */\r\n    contextMenu?: Partial<Record<IInspectorContextMenuType, IInspectorContextMenuItem[]>>;\r\n    /**\r\n     * List of context menu items that should be completely overridden by custom items from the contextMenu property.\r\n     */\r\n    contextMenuOverride?: IInspectorContextMenuType[];\r\n\r\n    /**\r\n     * Should the default font loading be skipped\r\n     */\r\n    skipDefaultFontLoading?: boolean;\r\n}\r\n\r\ndeclare module \"../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /**\r\n         * @internal\r\n         * Backing field\r\n         */\r\n        _debugLayer: DebugLayer;\r\n\r\n        /**\r\n         * Gets the debug layer (aka Inspector) associated with the scene\r\n         * @see https://doc.babylonjs.com/toolsAndResources/inspector\r\n         */\r\n        debugLayer: DebugLayer;\r\n    }\r\n}\r\nObject.defineProperty(Scene.prototype, \"debugLayer\", {\r\n    get: function (this: Scene) {\r\n        if (!this._debugLayer) {\r\n            this._debugLayer = new DebugLayer(this);\r\n        }\r\n        return this._debugLayer;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\n/**\r\n * Enum of inspector action tab\r\n */\r\nexport const enum DebugLayerTab {\r\n    /**\r\n     * Properties tag (default)\r\n     */\r\n    Properties = 0,\r\n    /**\r\n     * Debug tab\r\n     */\r\n    Debug = 1,\r\n    /**\r\n     * Statistics tab\r\n     */\r\n    Statistics = 2,\r\n    /**\r\n     * Tools tab\r\n     */\r\n    Tools = 3,\r\n    /**\r\n     * Settings tab\r\n     */\r\n    Settings = 4,\r\n}\r\n\r\n/**\r\n * The debug layer (aka Inspector) is the go to tool in order to better understand\r\n * what is happening in your scene\r\n * @see https://doc.babylonjs.com/toolsAndResources/inspector\r\n */\r\nexport class DebugLayer {\r\n    /**\r\n     * Define the url to get the inspector script from.\r\n     * By default it uses the babylonjs CDN.\r\n     * @ignoreNaming\r\n     */\r\n    public static InspectorURL = `${Tools._DefaultCdnUrl}/v${AbstractEngine.Version}/inspector/babylon.inspector.bundle.js`;\r\n\r\n    /**\r\n     * The default configuration of the inspector\r\n     */\r\n    public static Config: IInspectorOptions = {\r\n        overlay: false,\r\n        showExplorer: true,\r\n        showInspector: true,\r\n        embedMode: false,\r\n        handleResize: true,\r\n        enablePopup: true,\r\n    };\r\n\r\n    private _scene: Scene;\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private BJSINSPECTOR = this._getGlobalInspector();\r\n\r\n    private _onPropertyChangedObservable?: Observable<{ object: any; property: string; value: any; initialValue: any }>;\r\n    /**\r\n     * Observable triggered when a property is changed through the inspector.\r\n     */\r\n    public get onPropertyChangedObservable() {\r\n        if (this.BJSINSPECTOR && this.BJSINSPECTOR.Inspector) {\r\n            return this.BJSINSPECTOR.Inspector.OnPropertyChangedObservable;\r\n        }\r\n\r\n        if (!this._onPropertyChangedObservable) {\r\n            this._onPropertyChangedObservable = new Observable<{ object: any; property: string; value: any; initialValue: any }>();\r\n        }\r\n\r\n        return this._onPropertyChangedObservable;\r\n    }\r\n\r\n    private _onSelectionChangedObservable?: Observable<any>;\r\n    /**\r\n     * Observable triggered when the selection is changed through the inspector.\r\n     */\r\n    public get onSelectionChangedObservable() {\r\n        if (this.BJSINSPECTOR && this.BJSINSPECTOR.Inspector) {\r\n            return this.BJSINSPECTOR.Inspector.OnSelectionChangeObservable;\r\n        }\r\n\r\n        if (!this._onSelectionChangedObservable) {\r\n            this._onSelectionChangedObservable = new Observable<any>();\r\n        }\r\n\r\n        return this._onSelectionChangedObservable;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new debug layer.\r\n     * The debug layer (aka Inspector) is the go to tool in order to better understand\r\n     * what is happening in your scene\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector\r\n     * @param scene Defines the scene to inspect\r\n     */\r\n    constructor(scene?: Scene) {\r\n        this._scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        if (!this._scene) {\r\n            return;\r\n        }\r\n        this._scene.onDisposeObservable.add(() => {\r\n            // Debug layer\r\n            if (this._scene._debugLayer) {\r\n                this._scene._debugLayer.hide();\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates the inspector window.\r\n     * @param config\r\n     */\r\n    private _createInspector(config?: Partial<IInspectorOptions>) {\r\n        if (this.isVisible()) {\r\n            return;\r\n        }\r\n\r\n        if (this._onPropertyChangedObservable) {\r\n            for (const observer of this._onPropertyChangedObservable.observers) {\r\n                this.BJSINSPECTOR.Inspector.OnPropertyChangedObservable.add(observer);\r\n            }\r\n            this._onPropertyChangedObservable.clear();\r\n            this._onPropertyChangedObservable = undefined;\r\n        }\r\n\r\n        if (this._onSelectionChangedObservable) {\r\n            for (const observer of this._onSelectionChangedObservable.observers) {\r\n                this.BJSINSPECTOR.Inspector.OnSelectionChangedObservable.add(observer);\r\n            }\r\n            this._onSelectionChangedObservable.clear();\r\n            this._onSelectionChangedObservable = undefined;\r\n        }\r\n\r\n        const userOptions: IInspectorOptions = {\r\n            ...DebugLayer.Config,\r\n            ...config,\r\n        };\r\n\r\n        this.BJSINSPECTOR = this.BJSINSPECTOR || this._getGlobalInspector();\r\n\r\n        this.BJSINSPECTOR.Inspector.Show(this._scene, userOptions);\r\n    }\r\n\r\n    /**\r\n     * Select a specific entity in the scene explorer and highlight a specific block in that entity property grid\r\n     * @param entity defines the entity to select\r\n     * @param lineContainerTitles defines the specific blocks to highlight (could be a string or an array of strings)\r\n     */\r\n    public select(entity: any, lineContainerTitles?: string | string[]) {\r\n        if (this.BJSINSPECTOR) {\r\n            if (lineContainerTitles) {\r\n                if (Object.prototype.toString.call(lineContainerTitles) == \"[object String]\") {\r\n                    this.BJSINSPECTOR.Inspector.MarkLineContainerTitleForHighlighting(lineContainerTitles);\r\n                } else {\r\n                    this.BJSINSPECTOR.Inspector.MarkMultipleLineContainerTitlesForHighlighting(lineContainerTitles);\r\n                }\r\n            }\r\n            if (!this.BJSINSPECTOR.Inspector.IsVisible) {\r\n                setTimeout(() => {\r\n                    this.select(entity, lineContainerTitles);\r\n                }, 100);\r\n            } else {\r\n                this.BJSINSPECTOR.Inspector.OnSelectionChangeObservable.notifyObservers(entity);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the inspector from bundle or global\r\n     * @returns the inspector instance if found otherwise, null\r\n     */\r\n    private _getGlobalInspector(): any {\r\n        // UMD Global name detection from Webpack Bundle UMD Name.\r\n        if (typeof INSPECTOR !== \"undefined\") {\r\n            return INSPECTOR;\r\n        }\r\n\r\n        // In case of module let s check the global emitted from the Inspector entry point.\r\n        if (typeof BABYLON !== \"undefined\" && typeof BABYLON.Inspector !== \"undefined\") {\r\n            return BABYLON;\r\n        }\r\n\r\n        return undefined;\r\n    }\r\n\r\n    /**\r\n     * Get if the inspector is visible or not.\r\n     * @returns true if visible otherwise, false\r\n     */\r\n    public isVisible(): boolean {\r\n        return this.BJSINSPECTOR && this.BJSINSPECTOR.Inspector.IsVisible;\r\n    }\r\n\r\n    /**\r\n     * Hide the inspector and close its window.\r\n     */\r\n    public hide() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector.Hide();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the number of opened panes in the inspector\r\n     */\r\n    public get openedPanes() {\r\n        if (this.BJSINSPECTOR) {\r\n            return this.BJSINSPECTOR.Inspector._OpenedPane;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Update the scene in the inspector\r\n     */\r\n    public setAsActiveScene() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector._SetNewScene(this._scene);\r\n        }\r\n    }\r\n\r\n    public popupSceneExplorer() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector.PopupSceneExplorer();\r\n        }\r\n    }\r\n\r\n    public popupInspector() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector.PopupInspector();\r\n        }\r\n    }\r\n\r\n    public popupEmbed() {\r\n        if (this.BJSINSPECTOR) {\r\n            this.BJSINSPECTOR.Inspector.PopupEmbed();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Launch the debugLayer.\r\n     * @param config Define the configuration of the inspector\r\n     * @returns a promise fulfilled when the debug layer is visible\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public async show(config?: IInspectorOptions): Promise<DebugLayer> {\r\n        return await new Promise((resolve) => {\r\n            if (typeof this.BJSINSPECTOR == \"undefined\") {\r\n                const inspectorUrl = config && config.inspectorURL ? config.inspectorURL : DebugLayer.InspectorURL;\r\n\r\n                // Load inspector and add it to the DOM\r\n                Tools.LoadBabylonScript(inspectorUrl, () => {\r\n                    this._createInspector(config);\r\n                    resolve(this);\r\n                });\r\n            } else {\r\n                // Otherwise creates the inspector\r\n                this._createInspector(config);\r\n                resolve(this);\r\n            }\r\n        });\r\n    }\r\n}\r\n"]}