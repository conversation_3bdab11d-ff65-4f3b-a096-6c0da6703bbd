{"version": 3, "file": "engine.cubeTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.cubeTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAwI9D,UAAU,CAAC,SAAS,CAAC,8BAA8B,GAAG,UAAU,IAAY,EAAE,OAAoC;IAC9G,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,8CAAqC,CAAC;IACtF,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;IAE9B,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;QAC1B,MAAM,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAChE,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED,MAAM,eAAe,GAAG;QACpB,iBAAiB,EAAE,KAAK;QACxB,kBAAkB,EAAE,CAAC;QACrB,eAAe,EAAE,KAAK;QACtB,GAAG,OAAO;KACb,CAAC;IAEF,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAEtE,IAAI,CAAC,yBAAyB,CAAC,eAAe,EAAE,IAAI,EAAE,eAAe,CAAC,iBAAiB,EAAE,eAAe,CAAC,kBAAkB,CAAC,CAAC;IAE7H,kCAAkC;IAClC,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;QAClC,IAAI,eAAe,CAAC,eAAe,EAAE,CAAC;YAClC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC9I,CAAC;aAAM,CAAC;YACJ,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,eAAe,EAAE,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC5I,CAAC;IACL,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAErD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAElD,OAAO,eAAe,CAAC;AAC3B,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,OAAwB,EAAE,UAAmB,EAAE,QAAiB;IACtH,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;IACxE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;IAC/G,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC;IAE/G,IAAI,UAAU,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;QACzF,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QACtE,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;IACpC,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG,UACrC,OAAe,EACf,KAAsB,EACtB,KAAyB,EACzB,QAAkB,EAClB,SAAyC,IAAI,EAC7C,UAAiE,IAAI,EACrE,MAAe,EACf,kBAAuB,IAAI,EAC3B,oBAA6B,KAAK,EAClC,WAAmB,CAAC,EACpB,YAAoB,CAAC,EACrB,WAAsC,IAAI,EAC1C,aAAmB,EACnB,aAAa,GAAG,KAAK,EACrB,SAAoC,IAAI;IAExC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,OAAO,IAAI,CAAC,qBAAqB,CAC7B,OAAO,EACP,KAAK,EACL,KAAK,EACL,CAAC,CAAC,QAAQ,EACV,MAAM,EACN,OAAO,EACP,MAAM,EACN,eAAe,EACf,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,CAAC,OAAwB,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,EAC3F,CAAC,OAAwB,EAAE,IAAwC,EAAE,EAAE;QACnE,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACvH,MAAM,MAAM,GAAG,KAAK,CAAC;QAErB,MAAM,KAAK,GAAG;YACV,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;SACjC,CAAC;QAEF,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;QACtK,IAAI,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;QAErE,IAAI,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;YACpD,WAAW,GAAG,cAAc,CAAC;QACjC,CAAC;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC/D,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAE7B,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;oBAChD,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;oBACvD,OAAO;gBACX,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;gBAClC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;gBAEpC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC9G,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,cAAgC,CAAC,CAAC;YACzH,CAAC;iBAAM,CAAC;gBACJ,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC/F,CAAC;QACL,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;QAElD,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,IAAI,MAAM,EAAE,CAAC;YACT,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QAC5B,CAAC;QAED,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEnC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;QACb,CAAC;IACL,CAAC,EACD,CAAC,CAAC,aAAa,EACf,MAAM,CACT,CAAC;AACN,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,OAAwB,EAAE,MAAM,GAAG,IAAI;IAC9F,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9D,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;QACvC,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Constants } from \"../constants\";\r\nimport type { DepthTextureCreationOptions } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport { GetExponentOfTwo } from \"../../Misc/tools.functions\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * @internal\r\n         */\r\n        _setCubeMapTextureParams(texture: InternalTexture, loadMipmap: boolean, maxLevel?: number): void;\r\n        /**\r\n         * Creates a depth stencil cube texture.\r\n         * This is only available in WebGL 2.\r\n         * @param size The size of face edge in the cube texture.\r\n         * @param options The options defining the cube texture.\r\n         * @returns The cube texture\r\n         */\r\n        _createDepthStencilCubeTexture(size: number, options: DepthTextureCreationOptions): InternalTexture;\r\n\r\n        /**\r\n         * Creates a cube texture\r\n         * @param rootUrl defines the url where the files to load is located\r\n         * @param scene defines the current scene\r\n         * @param files defines the list of files to load (1 per face)\r\n         * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @param createPolynomials if a polynomial sphere should be created for the cube texture\r\n         * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\r\n         * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\r\n         * @param fallback defines texture to use while falling back when (compressed) texture file not found.\r\n         * @param loaderOptions options to be passed to the loader\r\n         * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n         * @param buffer defines the data buffer to load instead of loading the rootUrl\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean | undefined,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number,\r\n            fallback: Nullable<InternalTexture>,\r\n            loaderOptions: any,\r\n            useSRGBBuffer: boolean,\r\n            buffer: Nullable<ArrayBufferView>\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Creates a cube texture\r\n         * @param rootUrl defines the url where the files to load is located\r\n         * @param scene defines the current scene\r\n         * @param files defines the list of files to load (1 per face)\r\n         * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Creates a cube texture\r\n         * @param rootUrl defines the url where the files to load is located\r\n         * @param scene defines the current scene\r\n         * @param files defines the list of files to load (1 per face)\r\n         * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @param createPolynomials if a polynomial sphere should be created for the cube texture\r\n         * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\r\n         * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number\r\n        ): InternalTexture;\r\n\r\n        /** @internal */\r\n        createCubeTextureBase(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number,\r\n            fallback: Nullable<InternalTexture>,\r\n            beforeLoadCubeDataCallback: Nullable<(texture: InternalTexture, data: ArrayBufferView | ArrayBufferView[]) => void>,\r\n            imageHandler: Nullable<(texture: InternalTexture, imgs: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n            useSRGBBuffer: boolean,\r\n            buffer: ArrayBufferView\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Force the mipmap generation for the given render target texture\r\n         * @param texture defines the render target texture to use\r\n         * @param unbind defines whether or not to unbind the texture after generation. Defaults to true.\r\n         */\r\n        generateMipMapsForCubemap(texture: InternalTexture, unbind?: boolean): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype._createDepthStencilCubeTexture = function (size: number, options: DepthTextureCreationOptions): InternalTexture {\r\n    const internalTexture = new InternalTexture(this, InternalTextureSource.DepthStencil);\r\n    internalTexture.isCube = true;\r\n\r\n    if (this.webGLVersion === 1) {\r\n        Logger.Error(\"Depth cube texture is not supported by WebGL 1.\");\r\n        return internalTexture;\r\n    }\r\n\r\n    const internalOptions = {\r\n        bilinearFiltering: false,\r\n        comparisonFunction: 0,\r\n        generateStencil: false,\r\n        ...options,\r\n    };\r\n\r\n    const gl = this._gl;\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, internalTexture, true);\r\n\r\n    this._setupDepthStencilTexture(internalTexture, size, internalOptions.bilinearFiltering, internalOptions.comparisonFunction);\r\n\r\n    // Create the depth/stencil buffer\r\n    for (let face = 0; face < 6; face++) {\r\n        if (internalOptions.generateStencil) {\r\n            gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + face, 0, gl.DEPTH24_STENCIL8, size, size, 0, gl.DEPTH_STENCIL, gl.UNSIGNED_INT_24_8, null);\r\n        } else {\r\n            gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + face, 0, gl.DEPTH_COMPONENT24, size, size, 0, gl.DEPTH_COMPONENT, gl.UNSIGNED_INT, null);\r\n        }\r\n    }\r\n\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n\r\n    this._internalTexturesCache.push(internalTexture);\r\n\r\n    return internalTexture;\r\n};\r\n\r\nThinEngine.prototype._setCubeMapTextureParams = function (texture: InternalTexture, loadMipmap: boolean, maxLevel?: number): void {\r\n    const gl = this._gl;\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAG_FILTER, gl.LINEAR);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MIN_FILTER, loadMipmap ? gl.LINEAR_MIPMAP_LINEAR : gl.LINEAR);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n    texture.samplingMode = loadMipmap ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : Constants.TEXTURE_LINEAR_LINEAR;\r\n\r\n    if (loadMipmap && this.getCaps().textureMaxLevel && maxLevel !== undefined && maxLevel > 0) {\r\n        gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAX_LEVEL, maxLevel);\r\n        texture._maxLodLevel = maxLevel;\r\n    }\r\n\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n};\r\n\r\nThinEngine.prototype.createCubeTexture = function (\r\n    rootUrl: string,\r\n    scene: Nullable<Scene>,\r\n    files: Nullable<string[]>,\r\n    noMipmap?: boolean,\r\n    onLoad: Nullable<(data?: any) => void> = null,\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    format?: number,\r\n    forcedExtension: any = null,\r\n    createPolynomials: boolean = false,\r\n    lodScale: number = 0,\r\n    lodOffset: number = 0,\r\n    fallback: Nullable<InternalTexture> = null,\r\n    loaderOptions?: any,\r\n    useSRGBBuffer = false,\r\n    buffer: Nullable<ArrayBufferView> = null\r\n): InternalTexture {\r\n    const gl = this._gl;\r\n\r\n    return this.createCubeTextureBase(\r\n        rootUrl,\r\n        scene,\r\n        files,\r\n        !!noMipmap,\r\n        onLoad,\r\n        onError,\r\n        format,\r\n        forcedExtension,\r\n        createPolynomials,\r\n        lodScale,\r\n        lodOffset,\r\n        fallback,\r\n        (texture: InternalTexture) => this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true),\r\n        (texture: InternalTexture, imgs: HTMLImageElement[] | ImageBitmap[]) => {\r\n            const width = this.needPOTTextures ? GetExponentOfTwo(imgs[0].width, this._caps.maxCubemapTextureSize) : imgs[0].width;\r\n            const height = width;\r\n\r\n            const faces = [\r\n                gl.TEXTURE_CUBE_MAP_POSITIVE_X,\r\n                gl.TEXTURE_CUBE_MAP_POSITIVE_Y,\r\n                gl.TEXTURE_CUBE_MAP_POSITIVE_Z,\r\n                gl.TEXTURE_CUBE_MAP_NEGATIVE_X,\r\n                gl.TEXTURE_CUBE_MAP_NEGATIVE_Y,\r\n                gl.TEXTURE_CUBE_MAP_NEGATIVE_Z,\r\n            ];\r\n\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n            this._unpackFlipY(false);\r\n\r\n            const internalFormat = format ? this._getInternalFormat(format, texture._useSRGBBuffer) : texture._useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : gl.RGBA;\r\n            let texelFormat = format ? this._getInternalFormat(format) : gl.RGBA;\r\n\r\n            if (texture._useSRGBBuffer && this.webGLVersion === 1) {\r\n                texelFormat = internalFormat;\r\n            }\r\n\r\n            for (let index = 0; index < faces.length; index++) {\r\n                if (imgs[index].width !== width || imgs[index].height !== height) {\r\n                    this._prepareWorkingCanvas();\r\n\r\n                    if (!this._workingCanvas || !this._workingContext) {\r\n                        Logger.Warn(\"Cannot create canvas to resize texture.\");\r\n                        return;\r\n                    }\r\n                    this._workingCanvas.width = width;\r\n                    this._workingCanvas.height = height;\r\n\r\n                    this._workingContext.drawImage(imgs[index], 0, 0, imgs[index].width, imgs[index].height, 0, 0, width, height);\r\n                    gl.texImage2D(faces[index], 0, internalFormat, texelFormat, gl.UNSIGNED_BYTE, this._workingCanvas as TexImageSource);\r\n                } else {\r\n                    gl.texImage2D(faces[index], 0, internalFormat, texelFormat, gl.UNSIGNED_BYTE, imgs[index]);\r\n                }\r\n            }\r\n\r\n            if (!noMipmap) {\r\n                gl.generateMipmap(gl.TEXTURE_CUBE_MAP);\r\n            }\r\n\r\n            this._setCubeMapTextureParams(texture, !noMipmap);\r\n\r\n            texture.width = width;\r\n            texture.height = height;\r\n            texture.isReady = true;\r\n            if (format) {\r\n                texture.format = format;\r\n            }\r\n\r\n            texture.onLoadedObservable.notifyObservers(texture);\r\n            texture.onLoadedObservable.clear();\r\n\r\n            if (onLoad) {\r\n                onLoad();\r\n            }\r\n        },\r\n        !!useSRGBBuffer,\r\n        buffer\r\n    );\r\n};\r\n\r\nThinEngine.prototype.generateMipMapsForCubemap = function (texture: InternalTexture, unbind = true) {\r\n    if (texture.generateMipMaps) {\r\n        const gl = this._gl;\r\n        this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n        gl.generateMipmap(gl.TEXTURE_CUBE_MAP);\r\n        if (unbind) {\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n        }\r\n    }\r\n};\r\n"]}