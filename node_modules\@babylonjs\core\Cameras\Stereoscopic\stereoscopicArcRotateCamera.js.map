{"version": 3, "file": "stereoscopicArcRotateCamera.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/Stereoscopic/stereoscopicArcRotateCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAEhE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,uBAAuB,EAAE,MAAM,iCAAiC,CAAC;AAE1E,IAAI,CAAC,kBAAkB,CAAC,6BAA6B,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;IAC5E,OAAO,GAAG,EAAE,CAAC,IAAI,2BAA2B,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,mBAAmB,EAAE,OAAO,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;AACxJ,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,2BAA4B,SAAQ,eAAe;IAC5D;;;;;;;;;;OAUG;IACH,YAAY,IAAY,EAAE,KAAa,EAAE,IAAY,EAAE,MAAc,EAAE,MAAe,EAAE,kBAA0B,EAAE,wBAAiC,EAAE,KAAa;QAChK,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAgBjC,gBAAW,GAAG,GAAG,EAAE,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAfjE,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;QACzD,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC,CAAC,MAAM,CAAC,yCAAyC,CAAC,CAAC,CAAC,MAAM,CAAC,+BAA+B,EAAE;YACxI,kBAAkB,EAAE,kBAAkB;SACzC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACa,YAAY;QACxB,OAAO,6BAA6B,CAAC;IACzC,CAAC;CAGJ", "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Node } from \"../../node\";\r\nimport { _SetStereoscopicRigMode } from \"../RigModes/stereoscopicRigMode\";\r\n\r\nNode.AddNodeConstructor(\"StereoscopicArcRotateCamera\", (name, scene, options) => {\r\n    return () => new StereoscopicArcRotateCamera(name, 0, 0, 1.0, Vector3.Zero(), options.interaxial_distance, options.isStereoscopicSideBySide, scene);\r\n});\r\n\r\n/**\r\n * Camera used to simulate stereoscopic rendering (based on ArcRotateCamera)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n */\r\nexport class StereoscopicArcRotateCamera extends ArcRotateCamera {\r\n    /**\r\n     * Creates a new StereoscopicArcRotateCamera\r\n     * @param name defines camera name\r\n     * @param alpha defines alpha angle (in radians)\r\n     * @param beta defines beta angle (in radians)\r\n     * @param radius defines radius\r\n     * @param target defines camera target\r\n     * @param interaxialDistance defines distance between each color axis\r\n     * @param isStereoscopicSideBySide defines is stereoscopic is done side by side or over under\r\n     * @param scene defines the hosting scene\r\n     */\r\n    constructor(name: string, alpha: number, beta: number, radius: number, target: Vector3, interaxialDistance: number, isStereoscopicSideBySide: boolean, scene?: Scene) {\r\n        super(name, alpha, beta, radius, target, scene);\r\n        this.interaxialDistance = interaxialDistance;\r\n        this.isStereoscopicSideBySide = isStereoscopicSideBySide;\r\n        this.setCameraRigMode(isStereoscopicSideBySide ? Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL : Camera.RIG_MODE_STEREOSCOPIC_OVERUNDER, {\r\n            interaxialDistance: interaxialDistance,\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Gets camera class name\r\n     * @returns StereoscopicArcRotateCamera\r\n     */\r\n    public override getClassName(): string {\r\n        return \"StereoscopicArcRotateCamera\";\r\n    }\r\n\r\n    protected override _setRigMode = () => _SetStereoscopicRigMode(this);\r\n}\r\n"]}