import { Vector3 } from "../Maths/math.vector.js";
import { StandardMaterial } from "../Materials/standardMaterial.js";
import { AxisDragGizmo } from "../Gizmos/axisDragGizmo.js";
import { Color3 } from "../Maths/math.color.js";
import { EngineStore } from "../Engines/engineStore.js";
/**
 * The Axes viewer will show 3 axes in a specific point in space
 * @see https://doc.babylonjs.com/toolsAndResources/utilities/World_Axes
 */
export class AxesViewer {
    /**
     * Gets or sets a number used to scale line length
     */
    get scaleLines() {
        return this._scaleLines;
    }
    set scaleLines(value) {
        this._scaleLines = value;
        this._xAxis.scaling.setAll(this._scaleLines * this._scaleLinesFactor);
        this._yAxis.scaling.setAll(this._scaleLines * this._scaleLinesFactor);
        this._zAxis.scaling.setAll(this._scaleLines * this._scaleLinesFactor);
    }
    /** Gets the node hierarchy used to render x-axis */
    get xAxis() {
        return this._xAxis;
    }
    /** Gets the node hierarchy used to render y-axis */
    get yAxis() {
        return this._yAxis;
    }
    /** Gets the node hierarchy used to render z-axis */
    get zAxis() {
        return this._zAxis;
    }
    /**
     * Creates a new AxesViewer
     * @param scene defines the hosting scene
     * @param scaleLines defines a number used to scale line length (1 by default)
     * @param renderingGroupId defines a number used to set the renderingGroupId of the meshes (2 by default)
     * @param xAxis defines the node hierarchy used to render the x-axis
     * @param yAxis defines the node hierarchy used to render the y-axis
     * @param zAxis defines the node hierarchy used to render the z-axis
     * @param lineThickness The line thickness to use when creating the arrow. defaults to 1.
     */
    constructor(scene, scaleLines = 1, renderingGroupId = 2, xAxis, yAxis, zAxis, lineThickness = 1) {
        this._scaleLinesFactor = 4;
        this._instanced = false;
        /**
         * Gets the hosting scene
         */
        this.scene = null;
        this._scaleLines = 1;
        scene = scene || EngineStore.LastCreatedScene;
        if (!scene) {
            return;
        }
        if (!xAxis) {
            const redColoredMaterial = new StandardMaterial("xAxisMaterial", scene);
            redColoredMaterial.disableLighting = true;
            redColoredMaterial.emissiveColor = Color3.Red().scale(0.5);
            xAxis = AxisDragGizmo._CreateArrow(scene, redColoredMaterial, lineThickness);
        }
        if (!yAxis) {
            const greenColoredMaterial = new StandardMaterial("yAxisMaterial", scene);
            greenColoredMaterial.disableLighting = true;
            greenColoredMaterial.emissiveColor = Color3.Green().scale(0.5);
            yAxis = AxisDragGizmo._CreateArrow(scene, greenColoredMaterial, lineThickness);
        }
        if (!zAxis) {
            const blueColoredMaterial = new StandardMaterial("zAxisMaterial", scene);
            blueColoredMaterial.disableLighting = true;
            blueColoredMaterial.emissiveColor = Color3.Blue().scale(0.5);
            zAxis = AxisDragGizmo._CreateArrow(scene, blueColoredMaterial, lineThickness);
        }
        this._xAxis = xAxis;
        this._yAxis = yAxis;
        this._zAxis = zAxis;
        this.scaleLines = scaleLines;
        if (renderingGroupId != null) {
            AxesViewer._SetRenderingGroupId(this._xAxis, renderingGroupId);
            AxesViewer._SetRenderingGroupId(this._yAxis, renderingGroupId);
            AxesViewer._SetRenderingGroupId(this._zAxis, renderingGroupId);
        }
        this.scene = scene;
        this.update(new Vector3(), Vector3.Right(), Vector3.Up(), Vector3.Forward());
    }
    /**
     * Force the viewer to update
     * @param position defines the position of the viewer
     * @param xaxis defines the x axis of the viewer
     * @param yaxis defines the y axis of the viewer
     * @param zaxis defines the z axis of the viewer
     */
    update(position, xaxis, yaxis, zaxis) {
        this._xAxis.position.copyFrom(position);
        this._xAxis.setDirection(xaxis);
        this._yAxis.position.copyFrom(position);
        this._yAxis.setDirection(yaxis);
        this._zAxis.position.copyFrom(position);
        this._zAxis.setDirection(zaxis);
    }
    /**
     * Creates an instance of this axes viewer.
     * @returns a new axes viewer with instanced meshes
     */
    createInstance() {
        const xAxis = AxisDragGizmo._CreateArrowInstance(this.scene, this._xAxis);
        const yAxis = AxisDragGizmo._CreateArrowInstance(this.scene, this._yAxis);
        const zAxis = AxisDragGizmo._CreateArrowInstance(this.scene, this._zAxis);
        const axesViewer = new AxesViewer(this.scene, this.scaleLines, null, xAxis, yAxis, zAxis);
        axesViewer._instanced = true;
        return axesViewer;
    }
    /** Releases resources */
    dispose() {
        if (this._xAxis) {
            this._xAxis.dispose(false, !this._instanced);
        }
        if (this._yAxis) {
            this._yAxis.dispose(false, !this._instanced);
        }
        if (this._zAxis) {
            this._zAxis.dispose(false, !this._instanced);
        }
        this.scene = null;
    }
    static _SetRenderingGroupId(node, id) {
        const meshes = node.getChildMeshes();
        for (const mesh of meshes) {
            mesh.renderingGroupId = id;
        }
    }
}
//# sourceMappingURL=axesViewer.js.map