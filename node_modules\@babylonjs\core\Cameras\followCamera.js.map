{"version": 3, "file": "followCamera.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Cameras/followCamera.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AACzE,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACxE,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACvD,OAAO,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,OAAO,YAAa,SAAQ,YAAY;IAuF1C;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,eAAuC,IAAI;QACnG,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QA/FjC;;WAEG;QAEI,WAAM,GAAW,EAAE,CAAC;QAE3B;;;;WAIG;QAEI,qBAAgB,GAAqB,IAAI,CAAC;QAEjD;;;;WAIG;QAEI,qBAAgB,GAAqB,IAAI,CAAC;QAEjD;;WAEG;QAEI,mBAAc,GAAW,CAAC,CAAC;QAElC;;;WAGG;QAEI,6BAAwB,GAAqB,IAAI,CAAC;QAEzD;;;WAGG;QAEI,6BAAwB,GAAqB,IAAI,CAAC;QAEzD;;;WAGG;QAEI,iBAAY,GAAW,CAAC,CAAC;QAEhC;;;WAGG;QAEI,2BAAsB,GAAqB,IAAI,CAAC;QAEvD;;;WAGG;QAEI,2BAAsB,GAAqB,IAAI,CAAC;QAEvD;;WAEG;QAEI,uBAAkB,GAAW,IAAI,CAAC;QAEzC;;WAEG;QAEI,mBAAc,GAAW,EAAE,CAAC;QAwB/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,IAAI,yBAAyB,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,aAAa,EAAE,CAAC,WAAW,EAAE,CAAC;QACxD,iFAAiF;QACjF,oFAAoF;IACxF,CAAC;IAEO,OAAO,CAAC,YAA0B;QACtC,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACvC,YAAY,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QACpE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9D,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC;QACjE,MAAM,cAAc,GAAG,YAAY,CAAC,mBAAmB,EAAE,CAAC;QAC1D,MAAM,OAAO,GAAW,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3E,MAAM,OAAO,GAAW,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3E,MAAM,EAAE,GAAW,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7C,MAAM,EAAE,GAAW,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC1E,MAAM,EAAE,GAAW,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7C,IAAI,EAAE,GAAW,EAAE,GAAG,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,oBAAoB;QACvE,IAAI,EAAE,GAAW,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAC9C,IAAI,EAAE,GAAW,EAAE,GAAG,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAElD,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACxD,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;QAC7D,CAAC;QAED,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACxD,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;QAC7D,CAAC;QAED,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACxD,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QAC9F,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;IACnC,CAAC;IAOD;;;;OAIG;IACa,aAAa,CAAC,OAAY,EAAE,gBAA0B;QAClE,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAE5C,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACa,aAAa;QACzB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC;IACL,CAAC;IAED,gBAAgB;IACA,YAAY;QACxB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,KAAK,CAAC,YAAY,EAAE,CAAC;QACrB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpC,CAAC;IACL,CAAC;IAEO,YAAY;QAChB,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACxC,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,sBAAsB,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC1F,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC;QACpD,CAAC;QACD,IAAI,IAAI,CAAC,sBAAsB,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC1F,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,wBAAwB,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC;QACxD,CAAC;QACD,IAAI,IAAI,CAAC,wBAAwB,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC;QACxD,CAAC;IACL,CAAC;IAED;;;OAGG;IACa,YAAY;QACxB,OAAO,cAAc,CAAC;IAC1B,CAAC;CACJ;AA7MU;IADN,SAAS,EAAE;4CACe;AAQpB;IADN,SAAS,EAAE;sDACqC;AAQ1C;IADN,SAAS,EAAE;sDACqC;AAM1C;IADN,SAAS,EAAE;oDACsB;AAO3B;IADN,SAAS,EAAE;8DAC6C;AAOlD;IADN,SAAS,EAAE;8DAC6C;AAOlD;IADN,SAAS,EAAE;kDACoB;AAOzB;IADN,SAAS,EAAE;4DAC2C;AAOhD;IADN,SAAS,EAAE;4DAC2C;AAMhD;IADN,SAAS,EAAE;wDAC6B;AAMlC;IADN,SAAS,EAAE;oDACuB;AAMnB;IADf,wBAAwB,CAAC,gBAAgB,CAAC;kDACU;AAoIzD;;;;GAIG;AACH,MAAM,OAAO,eAAgB,SAAQ,YAAY;IAM7C;;;;;;;;;OASG;IACH,YACI,IAAY;IACZ,2CAA2C;IACpC,KAAa;IACpB,0CAA0C;IACnC,IAAY;IACnB,+CAA+C;IACxC,MAAc;IACrB,2DAA2D;IAC3D,MAA8B,EAC9B,KAAY;QAEZ,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;QAT5B,UAAK,GAAL,KAAK,CAAQ;QAEb,SAAI,GAAJ,IAAI,CAAQ;QAEZ,WAAM,GAAN,MAAM,CAAQ;QAtBjB,0BAAqB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QA4BpD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,MAA8B;QAC/C,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,OAAO;QACX,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QACD,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxF,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExF,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;QAC9D,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC/D,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;IACnC,CAAC;IAED,gBAAgB;IACA,YAAY;QACxB,KAAK,CAAC,YAAY,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACa,YAAY;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;CACJ;AAED,sBAAsB;AACtB,aAAa,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;AACpD,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { serialize, serializeAsMeshReference } from \"../Misc/decorators\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { TargetCamera } from \"./targetCamera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { FollowCameraInputsManager } from \"./followCameraInputsManager\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\nNode.AddNodeConstructor(\"FollowCamera\", (name, scene) => {\r\n    return () => new FollowCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\nNode.AddNodeConstructor(\"ArcFollowCamera\", (name, scene) => {\r\n    return () => new ArcFollowCamera(name, 0, 0, 1.0, null, scene);\r\n});\r\n\r\n/**\r\n * A follow camera takes a mesh as a target and follows it as it moves. Both a free camera version followCamera and\r\n * an arc rotate version arcFollowCamera are available.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#followcamera\r\n */\r\nexport class FollowCamera extends TargetCamera {\r\n    /**\r\n     * Distance the follow camera should follow an object at\r\n     */\r\n    @serialize()\r\n    public radius: number = 12;\r\n\r\n    /**\r\n     * Minimum allowed distance of the camera to the axis of rotation\r\n     * (The camera can not get closer).\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public lowerRadiusLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Maximum allowed distance of the camera to the axis of rotation\r\n     * (The camera can not get further).\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public upperRadiusLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Define a rotation offset between the camera and the object it follows\r\n     */\r\n    @serialize()\r\n    public rotationOffset: number = 0;\r\n\r\n    /**\r\n     * Minimum allowed angle to camera position relative to target object.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public lowerRotationOffsetLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Maximum allowed angle to camera position relative to target object.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public upperRotationOffsetLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Define a height offset between the camera and the object it follows.\r\n     * It can help following an object from the top (like a car chasing a plane)\r\n     */\r\n    @serialize()\r\n    public heightOffset: number = 4;\r\n\r\n    /**\r\n     * Minimum allowed height of camera position relative to target object.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public lowerHeightOffsetLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Maximum allowed height of camera position relative to target object.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public upperHeightOffsetLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Define how fast the camera can accelerate to follow it s target.\r\n     */\r\n    @serialize()\r\n    public cameraAcceleration: number = 0.05;\r\n\r\n    /**\r\n     * Define the speed limit of the camera following an object.\r\n     */\r\n    @serialize()\r\n    public maxCameraSpeed: number = 20;\r\n\r\n    /**\r\n     * Define the target of the camera.\r\n     */\r\n    @serializeAsMeshReference(\"lockedTargetId\")\r\n    public override lockedTarget: Nullable<AbstractMesh>;\r\n\r\n    /**\r\n     * Defines the input associated with the camera.\r\n     */\r\n    public override inputs: FollowCameraInputsManager;\r\n\r\n    /**\r\n     * Instantiates the follow camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#followcamera\r\n     * @param name Define the name of the camera in the scene\r\n     * @param position Define the position of the camera\r\n     * @param scene Define the scene the camera belong to\r\n     * @param lockedTarget Define the target of the camera\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, lockedTarget: Nullable<AbstractMesh> = null) {\r\n        super(name, position, scene);\r\n\r\n        this.lockedTarget = lockedTarget;\r\n        this.inputs = new FollowCameraInputsManager(this);\r\n        this.inputs.addKeyboard().addMouseWheel().addPointers();\r\n        // Uncomment the following line when the relevant handlers have been implemented.\r\n        // this.inputs.addKeyboard().addMouseWheel().addPointers().addVRDeviceOrientation();\r\n    }\r\n\r\n    private _follow(cameraTarget: AbstractMesh) {\r\n        if (!cameraTarget) {\r\n            return;\r\n        }\r\n\r\n        const rotMatrix = TmpVectors.Matrix[0];\r\n        cameraTarget.absoluteRotationQuaternion.toRotationMatrix(rotMatrix);\r\n        const yRotation = Math.atan2(rotMatrix.m[8], rotMatrix.m[10]);\r\n\r\n        const radians = Tools.ToRadians(this.rotationOffset) + yRotation;\r\n        const targetPosition = cameraTarget.getAbsolutePosition();\r\n        const targetX: number = targetPosition.x + Math.sin(radians) * this.radius;\r\n\r\n        const targetZ: number = targetPosition.z + Math.cos(radians) * this.radius;\r\n        const dx: number = targetX - this.position.x;\r\n        const dy: number = targetPosition.y + this.heightOffset - this.position.y;\r\n        const dz: number = targetZ - this.position.z;\r\n        let vx: number = dx * this.cameraAcceleration * 2; //this is set to .05\r\n        let vy: number = dy * this.cameraAcceleration;\r\n        let vz: number = dz * this.cameraAcceleration * 2;\r\n\r\n        if (vx > this.maxCameraSpeed || vx < -this.maxCameraSpeed) {\r\n            vx = vx < 1 ? -this.maxCameraSpeed : this.maxCameraSpeed;\r\n        }\r\n\r\n        if (vy > this.maxCameraSpeed || vy < -this.maxCameraSpeed) {\r\n            vy = vy < 1 ? -this.maxCameraSpeed : this.maxCameraSpeed;\r\n        }\r\n\r\n        if (vz > this.maxCameraSpeed || vz < -this.maxCameraSpeed) {\r\n            vz = vz < 1 ? -this.maxCameraSpeed : this.maxCameraSpeed;\r\n        }\r\n\r\n        this.position = new Vector3(this.position.x + vx, this.position.y + vy, this.position.z + vz);\r\n        this.setTarget(targetPosition);\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public override attachControl(noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public override attachControl(ignored: any, noPreventDefault?: boolean): void {\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        this.inputs.attachElement(noPreventDefault);\r\n\r\n        this._reset = () => {};\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public override detachControl(): void {\r\n        this.inputs.detachElement();\r\n\r\n        if (this._reset) {\r\n            this._reset();\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public override _checkInputs(): void {\r\n        this.inputs.checkInputs();\r\n        this._checkLimits();\r\n        super._checkInputs();\r\n        if (this.lockedTarget) {\r\n            this._follow(this.lockedTarget);\r\n        }\r\n    }\r\n\r\n    private _checkLimits() {\r\n        if (this.lowerRadiusLimit !== null && this.radius < this.lowerRadiusLimit) {\r\n            this.radius = this.lowerRadiusLimit;\r\n        }\r\n        if (this.upperRadiusLimit !== null && this.radius > this.upperRadiusLimit) {\r\n            this.radius = this.upperRadiusLimit;\r\n        }\r\n\r\n        if (this.lowerHeightOffsetLimit !== null && this.heightOffset < this.lowerHeightOffsetLimit) {\r\n            this.heightOffset = this.lowerHeightOffsetLimit;\r\n        }\r\n        if (this.upperHeightOffsetLimit !== null && this.heightOffset > this.upperHeightOffsetLimit) {\r\n            this.heightOffset = this.upperHeightOffsetLimit;\r\n        }\r\n\r\n        if (this.lowerRotationOffsetLimit !== null && this.rotationOffset < this.lowerRotationOffsetLimit) {\r\n            this.rotationOffset = this.lowerRotationOffsetLimit;\r\n        }\r\n        if (this.upperRotationOffsetLimit !== null && this.rotationOffset > this.upperRotationOffsetLimit) {\r\n            this.rotationOffset = this.upperRotationOffsetLimit;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the camera class name.\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"FollowCamera\";\r\n    }\r\n}\r\n\r\n/**\r\n * Arc Rotate version of the follow camera.\r\n * It still follows a Defined mesh but in an Arc Rotate Camera fashion.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#followcamera\r\n */\r\nexport class ArcFollowCamera extends TargetCamera {\r\n    private _cartesianCoordinates: Vector3 = Vector3.Zero();\r\n\r\n    /** Define the camera target (the mesh it should follow) */\r\n    private _meshTarget: Nullable<AbstractMesh>;\r\n\r\n    /**\r\n     * Instantiates a new ArcFollowCamera\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#followcamera\r\n     * @param name Define the name of the camera\r\n     * @param alpha Define the rotation angle of the camera around the longitudinal axis\r\n     * @param beta Define the rotation angle of the camera around the elevation axis\r\n     * @param radius Define the radius of the camera from its target point\r\n     * @param target Define the target of the camera\r\n     * @param scene Define the scene the camera belongs to\r\n     */\r\n    constructor(\r\n        name: string,\r\n        /** The longitudinal angle of the camera */\r\n        public alpha: number,\r\n        /** The latitudinal angle of the camera */\r\n        public beta: number,\r\n        /** The radius of the camera from its target */\r\n        public radius: number,\r\n        /** Define the camera target (the mesh it should follow) */\r\n        target: Nullable<AbstractMesh>,\r\n        scene: Scene\r\n    ) {\r\n        super(name, Vector3.Zero(), scene);\r\n        this.setMeshTarget(target);\r\n    }\r\n\r\n    /**\r\n     * Sets the mesh to follow with this camera.\r\n     * @param target the target to follow\r\n     */\r\n    public setMeshTarget(target: Nullable<AbstractMesh>) {\r\n        this._meshTarget = target;\r\n        this._follow();\r\n    }\r\n\r\n    private _follow(): void {\r\n        if (!this._meshTarget) {\r\n            return;\r\n        }\r\n        this._cartesianCoordinates.x = this.radius * Math.cos(this.alpha) * Math.cos(this.beta);\r\n        this._cartesianCoordinates.y = this.radius * Math.sin(this.beta);\r\n        this._cartesianCoordinates.z = this.radius * Math.sin(this.alpha) * Math.cos(this.beta);\r\n\r\n        const targetPosition = this._meshTarget.getAbsolutePosition();\r\n        this.position = targetPosition.add(this._cartesianCoordinates);\r\n        this.setTarget(targetPosition);\r\n    }\r\n\r\n    /** @internal */\r\n    public override _checkInputs(): void {\r\n        super._checkInputs();\r\n        this._follow();\r\n    }\r\n\r\n    /**\r\n     * Returns the class name of the object.\r\n     * It is mostly used internally for serialization purposes.\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"ArcFollowCamera\";\r\n    }\r\n}\r\n\r\n// Register Class Name\r\nRegisterClass(\"BABYLON.FollowCamera\", FollowCamera);\r\nRegisterClass(\"BABYLON.ArcFollowCamera\", ArcFollowCamera);\r\n"]}