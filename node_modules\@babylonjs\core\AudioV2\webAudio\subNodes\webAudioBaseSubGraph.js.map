{"version": 3, "file": "webAudioBaseSubGraph.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/webAudio/subNodes/webAudioBaseSubGraph.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,sBAAsB,EAAE,MAAM,oDAAoD,CAAC;AAE5F,OAAO,EAAE,wBAAwB,EAAE,MAAM,mDAAmD,CAAC;AAG7F,OAAO,EAAE,sBAAsB,EAAE,MAAM,iDAAiD,CAAC;AAEzF,OAAO,EAAE,wBAAwB,EAAE,MAAM,yDAAyD,CAAC;AAGnG,OAAO,EAAE,8BAA8B,EAAE,MAAM,yBAAyB,CAAC;AACzE,OAAO,EAAE,gCAAgC,EAAE,MAAM,2BAA2B,CAAC;AAO7E,gBAAgB;AAChB,MAAM,OAAgB,qBAAsB,SAAQ,sBAAsB;IAItE,gBAAgB;IAChB,YAAmB,KAAyB;QACxC,KAAK,EAAE,CAAC;QAJF,gBAAW,GAAwB,IAAI,CAAC;QAM9C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,SAAS,CAAC,OAA8C;QACjE,MAAM,kBAAkB,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAE7D,IAAI,kBAAkB,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,wBAAwB,wCAAuB,CAAC;QAC/D,CAAC;QAED,MAAM,IAAI,CAAC,wBAAwB,oCAAqB,CAAC;QAEzD,MAAM,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAEjD,IAAI,kBAAkB,EAAE,CAAC;YACrB,MAAM,YAAY,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC5C,CAAC;YAED,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC1C,CAAC;QAED,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/B,IAAI,UAAU,CAAC,YAAY,EAAE,KAAK,wBAAwB,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,WAAW,GAAI,UAAqC,CAAC,IAAI,CAAC;QAE/D,kFAAkF;QAClF,gDAAgD;QAChD,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC1C,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;gBACtD,MAAM,MAAM,GAAI,IAAI,CAAC,KAAyB,CAAC,OAAO,CAAC;gBACvD,IAAI,MAAM,EAAE,CAAC;oBACT,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrC,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAID,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,0EAA0E;IAC1E,qEAAqE;IAC3D,cAAc,CAAC,IAAY;QACjC,QAAQ,IAAI,EAAE,CAAC;YACX;gBACI,OAAO,gCAAgC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAChE;gBACI,OAAO,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9D;gBACI,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAES,kBAAkB;QACxB,MAAM,YAAY,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAEhD,IAAI,YAAY,IAAI,UAAU,EAAE,CAAC;YAC7B,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { AbstractAudioNode } from \"../../abstractAudio/abstractAudioNode\";\nimport { _AbstractAudioSubGraph } from \"../../abstractAudio/subNodes/abstractAudioSubGraph\";\nimport type { _AbstractAudioSubNode } from \"../../abstractAudio/subNodes/abstractAudioSubNode\";\nimport { _GetAudioAnalyzerSubNode } from \"../../abstractAudio/subNodes/audioAnalyzerSubNode\";\nimport { AudioSubNode } from \"../../abstractAudio/subNodes/audioSubNode\";\nimport type { IVolumeAudioOptions } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport { _GetVolumeAudioSubNode } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport type { IAudioAnalyzerOptions } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport { _HasAudioAnalyzerOptions } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport type { IWebAudioInNode, IWebAudioSuperNode } from \"../webAudioNode\";\nimport type { _VolumeWebAudioSubNode } from \"./volumeWebAudioSubNode\";\nimport { _CreateVolumeAudioSubNodeAsync } from \"./volumeWebAudioSubNode\";\nimport { _CreateAudioAnalyzerSubNodeAsync } from \"./webAudioAnalyzerSubNode\";\n\n/**\n * Options for creating a WebAudioBaseSubGraph.\n */\nexport interface IWebAudioBaseSubGraphOptions extends IAudioAnalyzerOptions, IVolumeAudioOptions {}\n\n/** @internal */\nexport abstract class _WebAudioBaseSubGraph extends _AbstractAudioSubGraph {\n    protected _owner: IWebAudioSuperNode;\n    protected _outputNode: Nullable<AudioNode> = null;\n\n    /** @internal */\n    public constructor(owner: IWebAudioSuperNode) {\n        super();\n\n        this._owner = owner;\n    }\n\n    /** @internal */\n    public async initAsync(options: Partial<IWebAudioBaseSubGraphOptions>): Promise<void> {\n        const hasAnalyzerOptions = _HasAudioAnalyzerOptions(options);\n\n        if (hasAnalyzerOptions) {\n            await this.createAndAddSubNodeAsync(AudioSubNode.ANALYZER);\n        }\n\n        await this.createAndAddSubNodeAsync(AudioSubNode.VOLUME);\n\n        await this._createSubNodePromisesResolvedAsync();\n\n        if (hasAnalyzerOptions) {\n            const analyzerNode = _GetAudioAnalyzerSubNode(this);\n            if (!analyzerNode) {\n                throw new Error(\"No analyzer subnode.\");\n            }\n\n            analyzerNode.setOptions(options);\n        }\n\n        const volumeNode = _GetVolumeAudioSubNode(this);\n        if (!volumeNode) {\n            throw new Error(\"No volume subnode.\");\n        }\n\n        volumeNode.setOptions(options);\n\n        if (volumeNode.getClassName() !== \"_VolumeWebAudioSubNode\") {\n            throw new Error(\"Not a WebAudio subnode.\");\n        }\n\n        this._outputNode = (volumeNode as _VolumeWebAudioSubNode).node;\n\n        // Connect the new wrapped WebAudio node to the wrapped downstream WebAudio nodes.\n        // The wrapper nodes are unaware of this change.\n        if (this._outputNode && this._downstreamNodes) {\n            const it = this._downstreamNodes.values();\n            for (let next = it.next(); !next.done; next = it.next()) {\n                const inNode = (next.value as IWebAudioInNode)._inNode;\n                if (inNode) {\n                    this._outputNode.connect(inNode);\n                }\n            }\n        }\n    }\n\n    protected abstract readonly _downstreamNodes: Nullable<Set<AbstractAudioNode>>;\n\n    /** @internal */\n    public get _inNode(): Nullable<AudioNode> {\n        return this._outputNode;\n    }\n\n    /** @internal */\n    public get _outNode(): Nullable<AudioNode> {\n        return this._outputNode;\n    }\n\n    // Function is async, but throws synchronously. Avoiding breaking changes.\n    // eslint-disable-next-line @typescript-eslint/promise-function-async\n    protected _createSubNode(name: string): Promise<_AbstractAudioSubNode> {\n        switch (name) {\n            case AudioSubNode.ANALYZER:\n                return _CreateAudioAnalyzerSubNodeAsync(this._owner.engine);\n            case AudioSubNode.VOLUME:\n                return _CreateVolumeAudioSubNodeAsync(this._owner.engine);\n            default:\n                throw new Error(`Unknown subnode name: ${name}`);\n        }\n    }\n\n    protected _onSubNodesChanged(): void {\n        const analyzerNode = _GetAudioAnalyzerSubNode(this);\n        const volumeNode = _GetVolumeAudioSubNode(this);\n\n        if (analyzerNode && volumeNode) {\n            volumeNode.connect(analyzerNode);\n        }\n    }\n}\n"]}