{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/webgpuEngine.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/webgpuEngine.ts"], "sourcesContent": ["/* eslint-disable babylonjs/available */\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { ThinWebGPUEngine } from \"./thinWebGPUEngine\";\r\nimport type { Nullable, DataArray, IndicesArray, Immutable, FloatArray } from \"../types\";\r\nimport { Color4 } from \"../Maths/math\";\r\nimport { InternalTexture, InternalTextureSource } from \"../Materials/Textures/internalTexture\";\r\nimport type { IEffectCreationOptions, IShaderPath } from \"../Materials/effect\";\r\nimport { Effect } from \"../Materials/effect\";\r\nimport type { EffectFallbacks } from \"../Materials/effectFallbacks\";\r\nimport { Constants } from \"./constants\";\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nimport * as WebGPUConstants from \"./WebGPU/webgpuConstants\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { IWebGPURenderPipelineStageDescriptor } from \"./WebGPU/webgpuPipelineContext\";\r\nimport { WebGPUPipelineContext } from \"./WebGPU/webgpuPipelineContext\";\r\nimport type { IPipelineContext } from \"./IPipelineContext\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport type { IShaderProcessor } from \"./Processors/iShaderProcessor\";\r\nimport { WebGPUShaderProcessorGLSL } from \"./WebGPU/webgpuShaderProcessorsGLSL\";\r\nimport { WebGPUShaderProcessorWGSL } from \"./WebGPU/webgpuShaderProcessorsWGSL\";\r\nimport type { _IShaderProcessingContext } from \"./Processors/shaderProcessingOptions\";\r\nimport { WebGPUShaderProcessingContext } from \"./WebGPU/webgpuShaderProcessingContext\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { WebGPUTextureHelper } from \"./WebGPU/webgpuTextureHelper\";\r\nimport { WebGPUTextureManager } from \"./WebGPU/webgpuTextureManager\";\r\nimport { AbstractEngine } from \"./abstractEngine\";\r\nimport type { ISceneLike, AbstractEngineOptions } from \"./abstractEngine\";\r\nimport { WebGPUBufferManager } from \"./WebGPU/webgpuBufferManager\";\r\nimport type { IHardwareTextureWrapper } from \"../Materials/Textures/hardwareTextureWrapper\";\r\nimport { WebGPUHardwareTexture } from \"./WebGPU/webgpuHardwareTexture\";\r\nimport type { IColor4Like } from \"../Maths/math.like\";\r\nimport { UniformBuffer } from \"../Materials/uniformBuffer\";\r\nimport { WebGPUCacheSampler } from \"./WebGPU/webgpuCacheSampler\";\r\nimport { WebGPUCacheRenderPipelineTree } from \"./WebGPU/webgpuCacheRenderPipelineTree\";\r\nimport { WebGPUStencilStateComposer } from \"./WebGPU/webgpuStencilStateComposer\";\r\nimport { WebGPUDepthCullingState } from \"./WebGPU/webgpuDepthCullingState\";\r\nimport type { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport { WebGPUMaterialContext } from \"./WebGPU/webgpuMaterialContext\";\r\nimport { WebGPUDrawContext } from \"./WebGPU/webgpuDrawContext\";\r\nimport { WebGPUCacheBindGroups } from \"./WebGPU/webgpuCacheBindGroups\";\r\nimport { WebGPUClearQuad } from \"./WebGPU/webgpuClearQuad\";\r\nimport type { IStencilState } from \"../States/IStencilState\";\r\nimport { WebGPURenderItemBlendColor, WebGPURenderItemScissor, WebGPURenderItemStencilRef, WebGPURenderItemViewport, WebGPUBundleList } from \"./WebGPU/webgpuBundleList\";\r\nimport { WebGPUTimestampQuery } from \"./WebGPU/webgpuTimestampQuery\";\r\nimport type { ComputeEffect } from \"../Compute/computeEffect\";\r\nimport { WebGPUOcclusionQuery } from \"./WebGPU/webgpuOcclusionQuery\";\r\nimport { ShaderCodeInliner } from \"./Processors/shaderCodeInliner\";\r\nimport type { TwgslOptions } from \"./WebGPU/webgpuTintWASM\";\r\nimport { WebGPUTintWASM } from \"./WebGPU/webgpuTintWASM\";\r\nimport type { ExternalTexture } from \"../Materials/Textures/externalTexture\";\r\nimport { WebGPUShaderProcessor } from \"./WebGPU/webgpuShaderProcessor\";\r\nimport { ShaderLanguage } from \"../Materials/shaderLanguage\";\r\nimport type { InternalTextureCreationOptions, TextureSize } from \"../Materials/Textures/textureCreationOptions\";\r\nimport { WebGPUSnapshotRendering } from \"./WebGPU/webgpuSnapshotRendering\";\r\nimport type { WebGPUDataBuffer } from \"../Meshes/WebGPU/webgpuDataBuffer\";\r\nimport type { WebGPURenderTargetWrapper } from \"./WebGPU/webgpuRenderTargetWrapper\";\r\n\r\nimport \"../Buffers/buffer.align\";\r\n\r\nimport type { VideoTexture } from \"../Materials/Textures/videoTexture\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { RenderTargetWrapper } from \"./renderTargetWrapper\";\r\nimport type { Scene } from \"../scene\";\r\n\r\nimport { SphericalPolynomial } from \"../Maths/sphericalPolynomial\";\r\nimport { PerformanceMonitor } from \"../Misc/performanceMonitor\";\r\nimport {\r\n    CreateImageBitmapFromSource,\r\n    ExitFullscreen,\r\n    ExitPointerlock,\r\n    GetFontOffset,\r\n    RequestFullscreen,\r\n    RequestPointerlock,\r\n    ResizeImageBitmap,\r\n    _CommonDispose,\r\n    _CommonInit,\r\n} from \"./engine.common\";\r\nimport { IsWrapper } from \"../Materials/drawWrapper.functions\";\r\nimport { PerfCounter } from \"../Misc/perfCounter\";\r\nimport \"./AbstractEngine/abstractEngine.loadingScreen\";\r\nimport \"./AbstractEngine/abstractEngine.dom\";\r\nimport \"./AbstractEngine/abstractEngine.states\";\r\nimport \"./AbstractEngine/abstractEngine.stencil\";\r\nimport \"./AbstractEngine/abstractEngine.renderPass\";\r\nimport \"../Audio/audioEngine\";\r\nimport { resetCachedPipeline } from \"../Materials/effect.functions\";\r\n\r\nimport { WebGPUExternalTexture } from \"./WebGPU/webgpuExternalTexture\";\r\nimport type { TextureSampler } from \"../Materials/Textures/textureSampler\";\r\nimport type { StorageBuffer } from \"../Buffers/storageBuffer\";\r\nimport \"./WebGPU/Extensions/engine.alpha\";\r\nimport \"./WebGPU/Extensions/engine.rawTexture\";\r\nimport \"./WebGPU/Extensions/engine.readTexture\";\r\nimport \"./WebGPU/Extensions/engine.cubeTexture\";\r\nimport \"./WebGPU/Extensions/engine.renderTarget\";\r\nimport \"./WebGPU/Extensions/engine.renderTargetTexture\";\r\nimport \"./WebGPU/Extensions/engine.renderTargetCube\";\r\nimport \"./WebGPU/Extensions/engine.query\";\r\n\r\nconst ViewDescriptorSwapChainAntialiasing: GPUTextureViewDescriptor = {\r\n    label: `TextureView_SwapChain_ResolveTarget`,\r\n    dimension: WebGPUConstants.TextureDimension.E2d,\r\n    format: undefined as any, // will be updated with the right value\r\n    mipLevelCount: 1,\r\n    arrayLayerCount: 1,\r\n};\r\n\r\nconst ViewDescriptorSwapChain: GPUTextureViewDescriptor = {\r\n    label: `TextureView_SwapChain`,\r\n    dimension: WebGPUConstants.TextureDimension.E2d,\r\n    format: undefined as any, // will be updated with the right value\r\n    mipLevelCount: 1,\r\n    arrayLayerCount: 1,\r\n};\r\nconst TempColor4 = new Color4();\r\n\r\n/** @internal */\r\ninterface IWebGPURenderPassWrapper {\r\n    renderPassDescriptor: Nullable<GPURenderPassDescriptor>;\r\n\r\n    colorAttachmentViewDescriptor: Nullable<GPUTextureViewDescriptor>;\r\n    depthAttachmentViewDescriptor: Nullable<GPUTextureViewDescriptor>;\r\n    colorAttachmentGPUTextures: (WebGPUHardwareTexture | null)[];\r\n    depthTextureFormat: GPUTextureFormat | undefined;\r\n}\r\n\r\n/**\r\n * Options to load the associated Glslang library\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface GlslangOptions {\r\n    /**\r\n     * Defines an existing instance of Glslang (useful in modules who do not access the global instance).\r\n     */\r\n    glslang?: any;\r\n    /**\r\n     * Defines the URL of the glslang JS File.\r\n     */\r\n    jsPath?: string;\r\n    /**\r\n     * Defines the URL of the glslang WASM File.\r\n     */\r\n    wasmPath?: string;\r\n}\r\n\r\n/**\r\n * Options to create the WebGPU engine\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface WebGPUEngineOptions extends AbstractEngineOptions, GPURequestAdapterOptions {\r\n    /**\r\n     * The featureLevel property of the GPURequestAdapterOptions interface\r\n     */\r\n    featureLevel?: string;\r\n\r\n    /**\r\n     * Defines the category of adapter to use.\r\n     * Is it the discrete or integrated device.\r\n     */\r\n    powerPreference?: GPUPowerPreference;\r\n\r\n    /**\r\n     * When set to true, indicates that only a fallback adapter may be returned when requesting an adapter.\r\n     * If the user agent does not support a fallback adapter, will cause requestAdapter() to resolve to null.\r\n     * Default: false\r\n     */\r\n    forceFallbackAdapter?: boolean;\r\n\r\n    /**\r\n     * Defines the device descriptor used to create a device once we have retrieved an appropriate adapter\r\n     */\r\n    deviceDescriptor?: GPUDeviceDescriptor;\r\n\r\n    /**\r\n     * When requesting the device, enable all the features supported by the adapter. Default: false\r\n     * Note that this setting is ignored if you explicitely set deviceDescriptor.requiredFeatures\r\n     */\r\n    enableAllFeatures?: boolean;\r\n\r\n    /**\r\n     * When requesting the device, set the required limits to the maximum possible values (the ones from adapter.limits). Default: false\r\n     * Note that this setting is ignored if you explicitely set deviceDescriptor.requiredLimits\r\n     */\r\n    setMaximumLimits?: boolean;\r\n\r\n    /**\r\n     * Defines the requested Swap Chain Format.\r\n     */\r\n    swapChainFormat?: GPUTextureFormat;\r\n\r\n    /**\r\n     * Defines whether we should generate debug markers in the gpu command lists (can be seen with PIX for eg). Default: false\r\n     */\r\n    enableGPUDebugMarkers?: boolean;\r\n\r\n    /**\r\n     * Options to load the associated Glslang library\r\n     */\r\n    glslangOptions?: GlslangOptions;\r\n\r\n    /**\r\n     * Options to load the associated Twgsl library\r\n     */\r\n    twgslOptions?: TwgslOptions;\r\n}\r\n\r\n/**\r\n * The web GPU engine class provides support for WebGPU version of babylon.js.\r\n * @since 5.0.0\r\n */\r\nexport class WebGPUEngine extends ThinWebGPUEngine {\r\n    // Default glslang options.\r\n    private static readonly _GlslangDefaultOptions: GlslangOptions = {\r\n        jsPath: `${Tools._DefaultCdnUrl}/glslang/glslang.js`,\r\n        wasmPath: `${Tools._DefaultCdnUrl}/glslang/glslang.wasm`,\r\n    };\r\n\r\n    private static _InstanceId = 0;\r\n\r\n    /** A unique id to identify this instance */\r\n    public readonly uniqueId = -1;\r\n\r\n    // Page Life cycle and constants\r\n    private readonly _uploadEncoderDescriptor = { label: \"upload\" };\r\n    private readonly _renderEncoderDescriptor = { label: \"render\" };\r\n    /** @internal */\r\n    public readonly _clearDepthValue = 1;\r\n    /** @internal */\r\n    public readonly _clearReverseDepthValue = 0;\r\n    /** @internal */\r\n    public readonly _clearStencilValue = 0;\r\n    private readonly _defaultSampleCount = 4; // Only supported value for now.\r\n\r\n    // Engine Life Cycle\r\n    /** @internal */\r\n    public _options: WebGPUEngineOptions;\r\n    private _glslang: any = null;\r\n    private _tintWASM: Nullable<WebGPUTintWASM> = null;\r\n    private _glslangAndTintAreFullyLoaded = false;\r\n    private _adapter: GPUAdapter;\r\n    private _adapterSupportedExtensions: GPUFeatureName[];\r\n    private _adapterInfo: GPUAdapterInfo = {\r\n        vendor: \"\",\r\n        architecture: \"\",\r\n        device: \"\",\r\n        description: \"\",\r\n    };\r\n    private _adapterSupportedLimits: GPUSupportedLimits;\r\n    /** @internal */\r\n    public _device: GPUDevice;\r\n    private _deviceEnabledExtensions: GPUFeatureName[];\r\n    private _deviceLimits: GPUSupportedLimits;\r\n    private _context: GPUCanvasContext;\r\n    private _mainPassSampleCount: number;\r\n    private _glslangOptions?: GlslangOptions;\r\n    private _twgslOptions?: TwgslOptions;\r\n    /** @internal */\r\n    public _bufferManager: WebGPUBufferManager;\r\n    private _clearQuad: WebGPUClearQuad;\r\n    /** @internal */\r\n    public _cacheSampler: WebGPUCacheSampler;\r\n    private _cacheBindGroups: WebGPUCacheBindGroups;\r\n    private _emptyVertexBuffer: VertexBuffer;\r\n    /** @internal */\r\n    public _mrtAttachments: number[];\r\n    /** @internal */\r\n    public _compiledComputeEffects: { [key: string]: ComputeEffect } = {};\r\n    /** @internal */\r\n    public _counters: {\r\n        numEnableEffects: number;\r\n        numEnableDrawWrapper: number;\r\n        numBundleCreationNonCompatMode: number;\r\n        numBundleReuseNonCompatMode: number;\r\n    } = {\r\n        numEnableEffects: 0,\r\n        numEnableDrawWrapper: 0,\r\n        numBundleCreationNonCompatMode: 0,\r\n        numBundleReuseNonCompatMode: 0,\r\n    };\r\n    /**\r\n     * Counters from last frame\r\n     */\r\n    public readonly countersLastFrame: {\r\n        numEnableEffects: number;\r\n        numEnableDrawWrapper: number;\r\n        numBundleCreationNonCompatMode: number;\r\n        numBundleReuseNonCompatMode: number;\r\n    } = {\r\n        numEnableEffects: 0,\r\n        numEnableDrawWrapper: 0,\r\n        numBundleCreationNonCompatMode: 0,\r\n        numBundleReuseNonCompatMode: 0,\r\n    };\r\n    /**\r\n     * Max number of uncaptured error messages to log\r\n     */\r\n    public numMaxUncapturedErrors = 20;\r\n\r\n    /**\r\n     * Gets the list of created scenes\r\n     */\r\n    public override scenes: Scene[] = [];\r\n\r\n    /** @internal */\r\n    public override _virtualScenes = new Array<Scene>();\r\n\r\n    // Some of the internal state might change during the render pass.\r\n    // This happens mainly during clear for the state\r\n    // And when the frame starts to swap the target texture from the swap chain\r\n    private _mainTexture: GPUTexture;\r\n    private _depthTexture: GPUTexture;\r\n    private _mainTextureExtends: GPUExtent3D;\r\n    private _depthTextureFormat: GPUTextureFormat | undefined;\r\n    private _colorFormat: GPUTextureFormat | null;\r\n    /** @internal */\r\n    public _ubInvertY: WebGPUDataBuffer;\r\n    /** @internal */\r\n    public _ubDontInvertY: WebGPUDataBuffer;\r\n\r\n    private _commandBuffers: GPUCommandBuffer[] = [null as any, null as any];\r\n\r\n    // Frame Buffer Life Cycle (recreated for each render target pass)\r\n\r\n    private _mainRenderPassWrapper: IWebGPURenderPassWrapper = {\r\n        renderPassDescriptor: null,\r\n        colorAttachmentViewDescriptor: null,\r\n        depthAttachmentViewDescriptor: null,\r\n        colorAttachmentGPUTextures: [],\r\n        depthTextureFormat: undefined,\r\n    };\r\n    private _rttRenderPassWrapper: IWebGPURenderPassWrapper = {\r\n        renderPassDescriptor: null,\r\n        colorAttachmentViewDescriptor: null,\r\n        depthAttachmentViewDescriptor: null,\r\n        colorAttachmentGPUTextures: [],\r\n        depthTextureFormat: undefined,\r\n    };\r\n    /** @internal */\r\n    public _pendingDebugCommands: Array<[string, Nullable<string>, number?]> = [];\r\n\r\n    // DrawCall Life Cycle\r\n    // Effect is on the parent class\r\n    // protected _currentEffect: Nullable<Effect> = null;\r\n    private _defaultDrawContext: WebGPUDrawContext;\r\n    private _defaultMaterialContext: WebGPUMaterialContext;\r\n    /** @internal */\r\n    public override _currentDrawContext: WebGPUDrawContext;\r\n    /** @internal */\r\n    public _currentMaterialContext: WebGPUMaterialContext;\r\n    private _currentOverrideVertexBuffers: Nullable<{ [key: string]: Nullable<VertexBuffer> }> = null;\r\n    private _currentIndexBuffer: Nullable<DataBuffer> = null;\r\n    private _colorWriteLocal = true;\r\n    private _forceEnableEffect = false;\r\n\r\n    /**\r\n     * Gets or sets the snapshot rendering mode\r\n     */\r\n    public override get snapshotRenderingMode(): number {\r\n        return this._snapshotRendering.mode;\r\n    }\r\n\r\n    public override set snapshotRenderingMode(mode: number) {\r\n        this._snapshotRendering.mode = mode;\r\n    }\r\n\r\n    /**\r\n     * Creates a new snapshot at the next frame using the current snapshotRenderingMode\r\n     */\r\n    public snapshotRenderingReset(): void {\r\n        this._snapshotRendering.reset();\r\n    }\r\n\r\n    /**\r\n     * Enables or disables the snapshot rendering mode\r\n     * Note that the WebGL engine does not support snapshot rendering so setting the value won't have any effect for this engine\r\n     */\r\n    public override get snapshotRendering(): boolean {\r\n        return this._snapshotRendering.enabled;\r\n    }\r\n\r\n    public override set snapshotRendering(activate) {\r\n        this._snapshotRendering.enabled = activate;\r\n    }\r\n\r\n    /**\r\n     * Sets this to true to disable the cache for the samplers. You should do it only for testing purpose!\r\n     */\r\n    public get disableCacheSamplers(): boolean {\r\n        return this._cacheSampler ? this._cacheSampler.disabled : false;\r\n    }\r\n\r\n    public set disableCacheSamplers(disable: boolean) {\r\n        if (this._cacheSampler) {\r\n            this._cacheSampler.disabled = disable;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets this to true to disable the cache for the render pipelines. You should do it only for testing purpose!\r\n     */\r\n    public get disableCacheRenderPipelines(): boolean {\r\n        return this._cacheRenderPipeline ? this._cacheRenderPipeline.disabled : false;\r\n    }\r\n\r\n    public set disableCacheRenderPipelines(disable: boolean) {\r\n        if (this._cacheRenderPipeline) {\r\n            this._cacheRenderPipeline.disabled = disable;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets this to true to disable the cache for the bind groups. You should do it only for testing purpose!\r\n     */\r\n    public get disableCacheBindGroups(): boolean {\r\n        return this._cacheBindGroups ? this._cacheBindGroups.disabled : false;\r\n    }\r\n\r\n    public set disableCacheBindGroups(disable: boolean) {\r\n        if (this._cacheBindGroups) {\r\n            this._cacheBindGroups.disabled = disable;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if all created effects are ready\r\n     * @returns true if all effects are ready\r\n     */\r\n    public areAllEffectsReady(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Get Font size information\r\n     * @param font font name\r\n     * @returns an object containing ascent, height and descent\r\n     */\r\n    public override getFontOffset(font: string): { ascent: number; height: number; descent: number } {\r\n        return GetFontOffset(font);\r\n    }\r\n\r\n    /**\r\n     * Gets a Promise<boolean> indicating if the engine can be instantiated (ie. if a WebGPU context can be found)\r\n     */\r\n    // eslint-disable-next-line no-restricted-syntax\r\n    public static get IsSupportedAsync(): Promise<boolean> {\r\n        return !navigator.gpu\r\n            ? Promise.resolve(false)\r\n            : navigator.gpu\r\n                  .requestAdapter()\r\n                  // eslint-disable-next-line github/no-then\r\n                  .then(\r\n                      (adapter: GPUAdapter | undefined) => !!adapter,\r\n                      () => false\r\n                  )\r\n                  // eslint-disable-next-line github/no-then\r\n                  .catch(() => false);\r\n    }\r\n\r\n    /**\r\n     * Not supported by WebGPU, you should call IsSupportedAsync instead!\r\n     */\r\n    public static get IsSupported(): boolean {\r\n        Logger.Warn(\"You must call IsSupportedAsync for WebGPU!\");\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the engine supports uniform buffers\r\n     */\r\n    public get supportsUniformBuffers(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /** Gets the supported extensions by the WebGPU adapter */\r\n    public get supportedExtensions(): Immutable<GPUFeatureName[]> {\r\n        return this._adapterSupportedExtensions;\r\n    }\r\n\r\n    /** Gets the currently enabled extensions on the WebGPU device */\r\n    public get enabledExtensions(): Immutable<GPUFeatureName[]> {\r\n        return this._deviceEnabledExtensions;\r\n    }\r\n\r\n    /** Gets the supported limits by the WebGPU adapter */\r\n    public get supportedLimits(): GPUSupportedLimits {\r\n        return this._adapterSupportedLimits;\r\n    }\r\n\r\n    /** Gets the current limits of the WebGPU device */\r\n    public get currentLimits() {\r\n        return this._deviceLimits;\r\n    }\r\n\r\n    /**\r\n     * Returns a string describing the current engine\r\n     */\r\n    public override get description(): string {\r\n        const description = this.name + this.version;\r\n\r\n        return description;\r\n    }\r\n\r\n    /**\r\n     * Returns the version of the engine\r\n     */\r\n    public get version(): number {\r\n        return 1;\r\n    }\r\n\r\n    /**\r\n     * Gets an object containing information about the current engine context\r\n     * @returns an object containing the vendor, the renderer and the version of the current engine context\r\n     */\r\n    public getInfo() {\r\n        return {\r\n            vendor: this._adapterInfo.vendor || \"unknown vendor\",\r\n            renderer: this._adapterInfo.architecture || \"unknown renderer\",\r\n            version: this._adapterInfo.description || \"unknown version\",\r\n        };\r\n    }\r\n\r\n    /**\r\n     * (WebGPU only) True (default) to be in compatibility mode, meaning rendering all existing scenes without artifacts (same rendering than WebGL).\r\n     * Setting the property to false will improve performances but may not work in some scenes if some precautions are not taken.\r\n     * See https://doc.babylonjs.com/setup/support/webGPU/webGPUOptimization/webGPUNonCompatibilityMode for more details\r\n     */\r\n    public override get compatibilityMode() {\r\n        return this._compatibilityMode;\r\n    }\r\n\r\n    public override set compatibilityMode(mode: boolean) {\r\n        this._compatibilityMode = mode;\r\n    }\r\n\r\n    /** @internal */\r\n    public get currentSampleCount(): number {\r\n        return this._currentRenderTarget ? this._currentRenderTarget.samples : this._mainPassSampleCount;\r\n    }\r\n\r\n    /**\r\n     * Create a new instance of the gpu engine asynchronously\r\n     * @param canvas Defines the canvas to use to display the result\r\n     * @param options Defines the options passed to the engine to create the GPU context dependencies\r\n     * @returns a promise that resolves with the created engine\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public static CreateAsync(canvas: HTMLCanvasElement, options: WebGPUEngineOptions = {}): Promise<WebGPUEngine> {\r\n        const engine = new WebGPUEngine(canvas, options);\r\n\r\n        return new Promise((resolve) => {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n            engine.initAsync(options.glslangOptions, options.twgslOptions).then(() => resolve(engine));\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Indicates if the z range in NDC space is 0..1 (value: true) or -1..1 (value: false)\r\n     */\r\n    public override readonly isNDCHalfZRange: boolean = true;\r\n\r\n    /**\r\n     * Indicates that the origin of the texture/framebuffer space is the bottom left corner. If false, the origin is top left\r\n     */\r\n    public override readonly hasOriginBottomLeft: boolean = false;\r\n\r\n    /**\r\n     * Create a new instance of the gpu engine.\r\n     * @param canvas Defines the canvas to use to display the result\r\n     * @param options Defines the options passed to the engine to create the GPU context dependencies\r\n     */\r\n    public constructor(canvas: HTMLCanvasElement | OffscreenCanvas, options: WebGPUEngineOptions = {}) {\r\n        super(options.antialias ?? true, options);\r\n        this._name = \"WebGPU\";\r\n\r\n        this._drawCalls = new PerfCounter();\r\n\r\n        options.deviceDescriptor = options.deviceDescriptor || {};\r\n        options.enableGPUDebugMarkers = options.enableGPUDebugMarkers ?? false;\r\n\r\n        Logger.Log(`Babylon.js v${AbstractEngine.Version} - ${this.description} engine`);\r\n        if (!navigator.gpu) {\r\n            Logger.Error(\"WebGPU is not supported by your browser.\");\r\n            return;\r\n        }\r\n\r\n        options.swapChainFormat = options.swapChainFormat || navigator.gpu.getPreferredCanvasFormat();\r\n\r\n        this._isWebGPU = true;\r\n        this._shaderPlatformName = \"WEBGPU\";\r\n\r\n        this._renderingCanvas = canvas as HTMLCanvasElement;\r\n        this._options = options;\r\n\r\n        this._mainPassSampleCount = options.antialias ? this._defaultSampleCount : 1;\r\n\r\n        if (navigator && navigator.userAgent) {\r\n            this._setupMobileChecks();\r\n        }\r\n\r\n        this._sharedInit(this._renderingCanvas);\r\n\r\n        this._shaderProcessor = new WebGPUShaderProcessorGLSL();\r\n        this._shaderProcessorWGSL = new WebGPUShaderProcessorWGSL();\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Initialization\r\n    //------------------------------------------------------------------------------\r\n    private _workingGlslangAndTintPromise: Nullable<Promise<void>> = null;\r\n\r\n    /**\r\n     * Load the glslang and tintWASM libraries and prepare them for use.\r\n     * @returns a promise that resolves when the engine is ready to use the glslang and tintWASM\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public prepareGlslangAndTintAsync(): Promise<void> {\r\n        if (!this._workingGlslangAndTintPromise) {\r\n            this._workingGlslangAndTintPromise = new Promise<void>((resolve) => {\r\n                // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n                this._initGlslangAsync(this._glslangOptions ?? this._options?.glslangOptions).then((glslang: any) => {\r\n                    this._glslang = glslang;\r\n                    this._tintWASM = new WebGPUTintWASM();\r\n                    // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n                    this._tintWASM.initTwgsl(this._twgslOptions ?? this._options?.twgslOptions).then(() => {\r\n                        this._glslangAndTintAreFullyLoaded = true;\r\n                        resolve();\r\n                    });\r\n                });\r\n            });\r\n        }\r\n\r\n        return this._workingGlslangAndTintPromise;\r\n    }\r\n\r\n    /**\r\n     * Initializes the WebGPU context and dependencies.\r\n     * @param glslangOptions Defines the GLSLang compiler options if necessary\r\n     * @param twgslOptions Defines the Twgsl compiler options if necessary\r\n     * @returns a promise notifying the readiness of the engine.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public initAsync(glslangOptions?: GlslangOptions, twgslOptions?: TwgslOptions): Promise<void> {\r\n        (this.uniqueId as number) = WebGPUEngine._InstanceId++;\r\n        this._glslangOptions = glslangOptions;\r\n        this._twgslOptions = twgslOptions;\r\n        return (\r\n            navigator\r\n                .gpu!.requestAdapter(this._options)\r\n                // eslint-disable-next-line github/no-then\r\n                .then(async (adapter: GPUAdapter | undefined) => {\r\n                    if (!adapter) {\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"Could not retrieve a WebGPU adapter (adapter is null).\";\r\n                    } else {\r\n                        this._adapter = adapter!;\r\n                        this._adapterSupportedExtensions = [];\r\n                        this._adapter.features?.forEach((feature) => {\r\n                            this._adapterSupportedExtensions.push(feature as WebGPUConstants.FeatureName);\r\n                        });\r\n                        this._adapterSupportedLimits = this._adapter.limits;\r\n                        this._adapterInfo = this._adapter.info;\r\n\r\n                        const deviceDescriptor = this._options.deviceDescriptor ?? {};\r\n                        const requiredFeatures = deviceDescriptor?.requiredFeatures ?? (this._options.enableAllFeatures ? this._adapterSupportedExtensions : undefined);\r\n\r\n                        if (requiredFeatures) {\r\n                            const requestedExtensions = requiredFeatures;\r\n                            const validExtensions: GPUFeatureName[] = [];\r\n\r\n                            for (const extension of requestedExtensions) {\r\n                                if (this._adapterSupportedExtensions.indexOf(extension) !== -1) {\r\n                                    validExtensions.push(extension);\r\n                                }\r\n                            }\r\n\r\n                            deviceDescriptor.requiredFeatures = validExtensions;\r\n                        }\r\n\r\n                        if (this._options.setMaximumLimits && !deviceDescriptor.requiredLimits) {\r\n                            deviceDescriptor.requiredLimits = {};\r\n                            for (const name in this._adapterSupportedLimits) {\r\n                                if (name === \"minSubgroupSize\" || name === \"maxSubgroupSize\") {\r\n                                    // Chrome exposes these limits in \"webgpu developer\" mode, but these can't be set on the device.\r\n                                    continue;\r\n                                }\r\n                                deviceDescriptor.requiredLimits[name] = this._adapterSupportedLimits[name];\r\n                            }\r\n                        }\r\n\r\n                        deviceDescriptor.label = `BabylonWebGPUDevice${this.uniqueId}`;\r\n\r\n                        return await this._adapter.requestDevice(deviceDescriptor);\r\n                    }\r\n                })\r\n                // eslint-disable-next-line github/no-then\r\n                .then((device: GPUDevice) => {\r\n                    this._device = device;\r\n                    this._deviceEnabledExtensions = [];\r\n                    this._device.features?.forEach((feature) => {\r\n                        this._deviceEnabledExtensions.push(feature as WebGPUConstants.FeatureName);\r\n                    });\r\n                    this._deviceLimits = device.limits;\r\n\r\n                    let numUncapturedErrors = -1;\r\n                    this._device.addEventListener(\"uncapturederror\", (event) => {\r\n                        if (++numUncapturedErrors < this.numMaxUncapturedErrors) {\r\n                            Logger.Warn(`WebGPU uncaptured error (${numUncapturedErrors + 1}): ${(<GPUUncapturedErrorEvent>event).error} - ${(<any>event).error.message}`);\r\n                        } else if (numUncapturedErrors++ === this.numMaxUncapturedErrors) {\r\n                            Logger.Warn(\r\n                                `WebGPU uncaptured error: too many warnings (${this.numMaxUncapturedErrors}), no more warnings will be reported to the console for this engine.`\r\n                            );\r\n                        }\r\n                    });\r\n\r\n                    if (!this._doNotHandleContextLost) {\r\n                        // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n                        this._device.lost?.then((info) => {\r\n                            if (this._isDisposed) {\r\n                                return;\r\n                            }\r\n                            this._contextWasLost = true;\r\n                            Logger.Warn(\"WebGPU context lost. \" + info);\r\n                            this.onContextLostObservable.notifyObservers(this);\r\n                            // eslint-disable-next-line @typescript-eslint/no-misused-promises\r\n                            this._restoreEngineAfterContextLost(async () => {\r\n                                const snapshotRenderingMode = this.snapshotRenderingMode;\r\n                                const snapshotRendering = this.snapshotRendering;\r\n                                const disableCacheSamplers = this.disableCacheSamplers;\r\n                                const disableCacheRenderPipelines = this.disableCacheRenderPipelines;\r\n                                const disableCacheBindGroups = this.disableCacheBindGroups;\r\n                                const enableGPUTimingMeasurements = this.enableGPUTimingMeasurements;\r\n\r\n                                await this.initAsync(this._glslangOptions ?? this._options?.glslangOptions, this._twgslOptions ?? this._options?.twgslOptions);\r\n\r\n                                this.snapshotRenderingMode = snapshotRenderingMode;\r\n                                this.snapshotRendering = snapshotRendering;\r\n                                this.disableCacheSamplers = disableCacheSamplers;\r\n                                this.disableCacheRenderPipelines = disableCacheRenderPipelines;\r\n                                this.disableCacheBindGroups = disableCacheBindGroups;\r\n                                this.enableGPUTimingMeasurements = enableGPUTimingMeasurements;\r\n                                this._currentRenderPass = null;\r\n                            });\r\n                        });\r\n                    }\r\n                })\r\n                // eslint-disable-next-line github/no-then\r\n                .then(() => {\r\n                    this._initializeLimits();\r\n\r\n                    this._bufferManager = new WebGPUBufferManager(this, this._device);\r\n                    this._textureHelper = new WebGPUTextureManager(this, this._device, this._bufferManager, this._deviceEnabledExtensions);\r\n                    this._cacheSampler = new WebGPUCacheSampler(this._device);\r\n                    this._cacheBindGroups = new WebGPUCacheBindGroups(this._device, this._cacheSampler, this);\r\n                    this._timestampQuery = new WebGPUTimestampQuery(this, this._device, this._bufferManager);\r\n                    this._occlusionQuery = (this._device as any).createQuerySet ? new WebGPUOcclusionQuery(this, this._device, this._bufferManager) : (undefined as any);\r\n                    this._bundleList = new WebGPUBundleList(this._device);\r\n                    this._snapshotRendering = new WebGPUSnapshotRendering(this, this._snapshotRenderingMode, this._bundleList);\r\n\r\n                    this._ubInvertY = this._bufferManager.createBuffer(\r\n                        new Float32Array([-1, 0]),\r\n                        WebGPUConstants.BufferUsage.Uniform | WebGPUConstants.BufferUsage.CopyDst,\r\n                        \"UBInvertY\"\r\n                    );\r\n                    this._ubDontInvertY = this._bufferManager.createBuffer(\r\n                        new Float32Array([1, 0]),\r\n                        WebGPUConstants.BufferUsage.Uniform | WebGPUConstants.BufferUsage.CopyDst,\r\n                        \"UBDontInvertY\"\r\n                    );\r\n\r\n                    if (this.dbgVerboseLogsForFirstFrames) {\r\n                        if ((this as any)._count === undefined) {\r\n                            (this as any)._count = 0;\r\n                            Logger.Log([\"%c frame #\" + (this as any)._count + \" - begin\", \"background: #ffff00\"]);\r\n                        }\r\n                    }\r\n\r\n                    this._uploadEncoder = this._device.createCommandEncoder(this._uploadEncoderDescriptor);\r\n                    this._renderEncoder = this._device.createCommandEncoder(this._renderEncoderDescriptor);\r\n\r\n                    this._emptyVertexBuffer = new VertexBuffer(this, [0], \"\", {\r\n                        stride: 1,\r\n                        offset: 0,\r\n                        size: 1,\r\n                        label: \"EmptyVertexBuffer\",\r\n                    });\r\n\r\n                    this._cacheRenderPipeline = new WebGPUCacheRenderPipelineTree(this._device, this._emptyVertexBuffer);\r\n\r\n                    this._depthCullingState = new WebGPUDepthCullingState(this._cacheRenderPipeline);\r\n                    this._stencilStateComposer = new WebGPUStencilStateComposer(this._cacheRenderPipeline);\r\n                    this._stencilStateComposer.stencilGlobal = this._stencilState;\r\n\r\n                    this._depthCullingState.depthTest = true;\r\n                    this._depthCullingState.depthFunc = Constants.LEQUAL;\r\n                    this._depthCullingState.depthMask = true;\r\n\r\n                    this._textureHelper.setCommandEncoder(this._uploadEncoder);\r\n\r\n                    this._clearQuad = new WebGPUClearQuad(this._device, this, this._emptyVertexBuffer);\r\n                    this._defaultDrawContext = this.createDrawContext()!;\r\n                    this._currentDrawContext = this._defaultDrawContext;\r\n                    this._defaultMaterialContext = this.createMaterialContext()!;\r\n                    this._currentMaterialContext = this._defaultMaterialContext;\r\n\r\n                    this._initializeContextAndSwapChain();\r\n                    this._initializeMainAttachments();\r\n                    this.resize();\r\n                })\r\n                // eslint-disable-next-line github/no-then\r\n                .catch((e: any) => {\r\n                    Logger.Error(\"A fatal error occurred during WebGPU creation/initialization.\");\r\n                    throw e;\r\n                })\r\n        );\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    private _initGlslangAsync(glslangOptions?: GlslangOptions): Promise<any> {\r\n        glslangOptions = glslangOptions || {};\r\n        glslangOptions = {\r\n            ...WebGPUEngine._GlslangDefaultOptions,\r\n            ...glslangOptions,\r\n        };\r\n\r\n        if (glslangOptions.glslang) {\r\n            return glslangOptions.glslang;\r\n        }\r\n\r\n        if ((self as any).glslang) {\r\n            return (self as any).glslang(glslangOptions.wasmPath);\r\n        }\r\n\r\n        if (glslangOptions.jsPath && glslangOptions.wasmPath) {\r\n            // eslint-disable-next-line github/no-then\r\n            return Tools.LoadBabylonScriptAsync(glslangOptions.jsPath).then(() => {\r\n                return (self as any).glslang(Tools.GetBabylonScriptURL(glslangOptions.wasmPath!));\r\n            });\r\n        }\r\n\r\n        throw new Error(\"glslang is not available\");\r\n    }\r\n\r\n    private _initializeLimits(): void {\r\n        // Init caps\r\n        // TODO WEBGPU Real Capability check once limits will be working.\r\n\r\n        this._caps = {\r\n            maxTexturesImageUnits: this._deviceLimits.maxSampledTexturesPerShaderStage,\r\n            maxVertexTextureImageUnits: this._deviceLimits.maxSampledTexturesPerShaderStage,\r\n            maxCombinedTexturesImageUnits: this._deviceLimits.maxSampledTexturesPerShaderStage * 2,\r\n            maxTextureSize: this._deviceLimits.maxTextureDimension2D,\r\n            maxCubemapTextureSize: this._deviceLimits.maxTextureDimension2D,\r\n            maxRenderTextureSize: this._deviceLimits.maxTextureDimension2D,\r\n            maxVertexAttribs: this._deviceLimits.maxVertexAttributes,\r\n            maxDrawBuffers: 8,\r\n            maxVaryingVectors: this._deviceLimits.maxInterStageShaderVariables,\r\n            maxFragmentUniformVectors: Math.floor(this._deviceLimits.maxUniformBufferBindingSize / 4),\r\n            maxVertexUniformVectors: Math.floor(this._deviceLimits.maxUniformBufferBindingSize / 4),\r\n            standardDerivatives: true,\r\n            astc: (this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TextureCompressionASTC) >= 0 ? true : undefined) as any,\r\n            s3tc: (this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TextureCompressionBC) >= 0 ? true : undefined) as any,\r\n            pvrtc: null,\r\n            etc1: null,\r\n            etc2: (this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TextureCompressionETC2) >= 0 ? true : undefined) as any,\r\n            bptc: this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TextureCompressionBC) >= 0 ? true : undefined,\r\n            maxAnisotropy: 16, // Most implementations support maxAnisotropy values in range between 1 and 16, inclusive. The used value of maxAnisotropy will be clamped to the maximum value that the platform supports.\r\n            uintIndices: true,\r\n            fragmentDepthSupported: true,\r\n            highPrecisionShaderSupported: true,\r\n            colorBufferFloat: true,\r\n            supportFloatTexturesResolve: false, // See https://github.com/gpuweb/gpuweb/issues/3844\r\n            rg11b10ufColorRenderable: this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.RG11B10UFloatRenderable) >= 0,\r\n            textureFloat: true,\r\n            textureFloatLinearFiltering: this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.Float32Filterable) >= 0,\r\n            textureFloatRender: true,\r\n            textureHalfFloat: true,\r\n            textureHalfFloatLinearFiltering: true,\r\n            textureHalfFloatRender: true,\r\n            textureLOD: true,\r\n            texelFetch: true,\r\n            drawBuffersExtension: true,\r\n            depthTextureExtension: true,\r\n            vertexArrayObject: false,\r\n            instancedArrays: true,\r\n            timerQuery:\r\n                typeof BigUint64Array !== \"undefined\" && this._deviceEnabledExtensions.indexOf(WebGPUConstants.FeatureName.TimestampQuery) !== -1 ? (true as any) : undefined,\r\n            supportOcclusionQuery: typeof BigUint64Array !== \"undefined\",\r\n            canUseTimestampForTimerQuery: true,\r\n            multiview: false,\r\n            oculusMultiview: false,\r\n            parallelShaderCompile: undefined,\r\n            blendMinMax: true,\r\n            maxMSAASamples: 4, // the spec only supports values of 1 and 4\r\n            canUseGLInstanceID: true,\r\n            canUseGLVertexID: true,\r\n            supportComputeShaders: true,\r\n            supportSRGBBuffers: true,\r\n            supportTransformFeedbacks: false,\r\n            textureMaxLevel: true,\r\n            texture2DArrayMaxLayerCount: this._deviceLimits.maxTextureArrayLayers,\r\n            disableMorphTargetTexture: false,\r\n            textureNorm16: false, // in the works: https://github.com/gpuweb/gpuweb/issues/3001\r\n            blendParametersPerTarget: true,\r\n            dualSourceBlending: true,\r\n        };\r\n\r\n        this._features = {\r\n            forceBitmapOverHTMLImageElement: true,\r\n            supportRenderAndCopyToLodForFloatTextures: true,\r\n            supportDepthStencilTexture: true,\r\n            supportShadowSamplers: true,\r\n            uniformBufferHardCheckMatrix: false,\r\n            allowTexturePrefiltering: true,\r\n            trackUbosInFrame: true,\r\n            checkUbosContentBeforeUpload: true,\r\n            supportCSM: true,\r\n            basisNeedsPOT: false,\r\n            support3DTextures: true,\r\n            needTypeSuffixInShaderConstants: true,\r\n            supportMSAA: true,\r\n            supportSSAO2: true,\r\n            supportIBLShadows: true,\r\n            supportExtendedTextureFormats: true,\r\n            supportSwitchCaseInShader: true,\r\n            supportSyncTextureRead: false,\r\n            needsInvertingBitmap: false,\r\n            useUBOBindingCache: false,\r\n            needShaderCodeInlining: true,\r\n            needToAlwaysBindUniformBuffers: true,\r\n            supportRenderPasses: true,\r\n            supportSpriteInstancing: true,\r\n            forceVertexBufferStrideAndOffsetMultiple4Bytes: true,\r\n            _checkNonFloatVertexBuffersDontRecreatePipelineContext: true,\r\n            _collectUbosUpdatedInFrame: false,\r\n        };\r\n    }\r\n\r\n    private _initializeContextAndSwapChain(): void {\r\n        if (!this._renderingCanvas) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"The rendering canvas has not been set!\";\r\n        }\r\n        this._context = this._renderingCanvas.getContext(\"webgpu\") as unknown as GPUCanvasContext;\r\n        this._configureContext();\r\n        this._colorFormat = this._options.swapChainFormat!;\r\n        this._mainRenderPassWrapper.colorAttachmentGPUTextures = [new WebGPUHardwareTexture(this)];\r\n        this._mainRenderPassWrapper.colorAttachmentGPUTextures[0]!.format = this._colorFormat;\r\n        this._setColorFormat(this._mainRenderPassWrapper);\r\n    }\r\n\r\n    // Set default values as WebGL with depth and stencil attachment for the broadest Compat.\r\n    private _initializeMainAttachments(): void {\r\n        if (!this._bufferManager) {\r\n            return;\r\n        }\r\n\r\n        this.flushFramebuffer();\r\n\r\n        this._mainTextureExtends = {\r\n            width: this.getRenderWidth(true),\r\n            height: this.getRenderHeight(true),\r\n            depthOrArrayLayers: 1,\r\n        };\r\n\r\n        const bufferDataUpdate = new Float32Array([this.getRenderHeight(true)]);\r\n\r\n        this._bufferManager.setSubData(this._ubInvertY, 4, bufferDataUpdate);\r\n        this._bufferManager.setSubData(this._ubDontInvertY, 4, bufferDataUpdate);\r\n\r\n        let mainColorAttachments: GPURenderPassColorAttachment[];\r\n\r\n        if (this._options.antialias) {\r\n            const mainTextureDescriptor: GPUTextureDescriptor = {\r\n                label: `Texture_MainColor_${this._mainTextureExtends.width}x${this._mainTextureExtends.height}_antialiasing`,\r\n                size: this._mainTextureExtends,\r\n                mipLevelCount: 1,\r\n                sampleCount: this._mainPassSampleCount,\r\n                dimension: WebGPUConstants.TextureDimension.E2d,\r\n                format: this._options.swapChainFormat!,\r\n                usage: WebGPUConstants.TextureUsage.RenderAttachment,\r\n            };\r\n\r\n            if (this._mainTexture) {\r\n                this._textureHelper.releaseTexture(this._mainTexture);\r\n            }\r\n            this._mainTexture = this._device.createTexture(mainTextureDescriptor);\r\n            mainColorAttachments = [\r\n                {\r\n                    view: this._mainTexture.createView({\r\n                        label: \"TextureView_MainColor_antialiasing\",\r\n                        dimension: WebGPUConstants.TextureDimension.E2d,\r\n                        format: this._options.swapChainFormat!,\r\n                        mipLevelCount: 1,\r\n                        arrayLayerCount: 1,\r\n                    }),\r\n                    clearValue: new Color4(0, 0, 0, 1),\r\n                    loadOp: WebGPUConstants.LoadOp.Clear,\r\n                    storeOp: WebGPUConstants.StoreOp.Store, // don't use StoreOp.Discard, else using several cameras with different viewports or using scissors will fail because we call beginRenderPass / endPass several times for the same color attachment!\r\n                },\r\n            ];\r\n        } else {\r\n            mainColorAttachments = [\r\n                {\r\n                    view: undefined as any,\r\n                    clearValue: new Color4(0, 0, 0, 1),\r\n                    loadOp: WebGPUConstants.LoadOp.Clear,\r\n                    storeOp: WebGPUConstants.StoreOp.Store,\r\n                },\r\n            ];\r\n        }\r\n\r\n        this._mainRenderPassWrapper.depthTextureFormat = this.isStencilEnable ? WebGPUConstants.TextureFormat.Depth24PlusStencil8 : WebGPUConstants.TextureFormat.Depth32Float;\r\n\r\n        this._setDepthTextureFormat(this._mainRenderPassWrapper);\r\n        this._setColorFormat(this._mainRenderPassWrapper);\r\n\r\n        const depthTextureDescriptor: GPUTextureDescriptor = {\r\n            label: `Texture_MainDepthStencil_${this._mainTextureExtends.width}x${this._mainTextureExtends.height}`,\r\n            size: this._mainTextureExtends,\r\n            mipLevelCount: 1,\r\n            sampleCount: this._mainPassSampleCount,\r\n            dimension: WebGPUConstants.TextureDimension.E2d,\r\n            format: this._mainRenderPassWrapper.depthTextureFormat,\r\n            usage: WebGPUConstants.TextureUsage.RenderAttachment,\r\n        };\r\n\r\n        if (this._depthTexture) {\r\n            this._textureHelper.releaseTexture(this._depthTexture);\r\n        }\r\n        this._depthTexture = this._device.createTexture(depthTextureDescriptor);\r\n        const mainDepthAttachment: GPURenderPassDepthStencilAttachment = {\r\n            view: this._depthTexture.createView({\r\n                label: `TextureView_MainDepthStencil_${this._mainTextureExtends.width}x${this._mainTextureExtends.height}`,\r\n                dimension: WebGPUConstants.TextureDimension.E2d,\r\n                format: this._depthTexture.format,\r\n                mipLevelCount: 1,\r\n                arrayLayerCount: 1,\r\n            }),\r\n\r\n            depthClearValue: this._clearDepthValue,\r\n            depthLoadOp: WebGPUConstants.LoadOp.Clear,\r\n            depthStoreOp: WebGPUConstants.StoreOp.Store,\r\n            stencilClearValue: this._clearStencilValue,\r\n            stencilLoadOp: !this.isStencilEnable ? undefined : WebGPUConstants.LoadOp.Clear,\r\n            stencilStoreOp: !this.isStencilEnable ? undefined : WebGPUConstants.StoreOp.Store,\r\n        };\r\n\r\n        this._mainRenderPassWrapper.renderPassDescriptor = {\r\n            label: \"MainRenderPass\",\r\n            colorAttachments: mainColorAttachments,\r\n            depthStencilAttachment: mainDepthAttachment,\r\n        };\r\n\r\n        this.beginFrame();\r\n        this._startMainRenderPass(true, null, true, false);\r\n        this._endCurrentRenderPass();\r\n        this.endFrame();\r\n        this._frameId--; // We don't want to count the frame as a real frame, because it was only used to initialize the depth texture\r\n    }\r\n\r\n    /**\r\n     * Shared initialization across engines types.\r\n     * @param canvas The canvas associated with this instance of the engine.\r\n     */\r\n    protected override _sharedInit(canvas: HTMLCanvasElement) {\r\n        super._sharedInit(canvas);\r\n\r\n        _CommonInit(this, canvas, this._creationOptions);\r\n    }\r\n\r\n    private _configureContext(): void {\r\n        this._context.configure({\r\n            device: this._device,\r\n            format: this._options.swapChainFormat!,\r\n            usage: WebGPUConstants.TextureUsage.RenderAttachment | WebGPUConstants.TextureUsage.CopySrc,\r\n            alphaMode: this.premultipliedAlpha ? WebGPUConstants.CanvasAlphaMode.Premultiplied : WebGPUConstants.CanvasAlphaMode.Opaque,\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Resize an image and returns the image data as an uint8array\r\n     * @param image image to resize\r\n     * @param bufferWidth destination buffer width\r\n     * @param bufferHeight destination buffer height\r\n     * @returns an uint8array containing RGBA values of bufferWidth * bufferHeight size\r\n     */\r\n    public override resizeImageBitmap(image: HTMLImageElement | ImageBitmap, bufferWidth: number, bufferHeight: number): Uint8Array {\r\n        return ResizeImageBitmap(this, image, bufferWidth, bufferHeight);\r\n    }\r\n\r\n    /**\r\n     * Engine abstraction for loading and creating an image bitmap from a given source string.\r\n     * @param imageSource source to load the image from.\r\n     * @param options An object that sets options for the image's extraction.\r\n     * @returns ImageBitmap\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public override async _createImageBitmapFromSource(imageSource: string, options?: ImageBitmapOptions): Promise<ImageBitmap> {\r\n        return await CreateImageBitmapFromSource(this, imageSource, options);\r\n    }\r\n\r\n    /**\r\n     * Toggle full screen mode\r\n     * @param requestPointerLock defines if a pointer lock should be requested from the user\r\n     */\r\n    public override switchFullscreen(requestPointerLock: boolean): void {\r\n        if (this.isFullscreen) {\r\n            this.exitFullscreen();\r\n        } else {\r\n            this.enterFullscreen(requestPointerLock);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enters full screen mode\r\n     * @param requestPointerLock defines if a pointer lock should be requested from the user\r\n     */\r\n    public override enterFullscreen(requestPointerLock: boolean): void {\r\n        if (!this.isFullscreen) {\r\n            this._pointerLockRequested = requestPointerLock;\r\n            if (this._renderingCanvas) {\r\n                RequestFullscreen(this._renderingCanvas);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Exits full screen mode\r\n     */\r\n    public override exitFullscreen(): void {\r\n        if (this.isFullscreen) {\r\n            ExitFullscreen();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enters Pointerlock mode\r\n     */\r\n    public enterPointerlock(): void {\r\n        if (this._renderingCanvas) {\r\n            RequestPointerlock(this._renderingCanvas);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Exits Pointerlock mode\r\n     */\r\n    public exitPointerlock(): void {\r\n        ExitPointerlock();\r\n    }\r\n\r\n    protected override _rebuildBuffers(): void {\r\n        super._rebuildBuffers();\r\n\r\n        for (const storageBuffer of this._storageBuffers) {\r\n            // The buffer can already be rebuilt by the call to _rebuildGeometries(), which recreates the storage buffers for the ComputeShaderParticleSystem\r\n            if ((storageBuffer.getBuffer() as WebGPUDataBuffer).engineId !== this.uniqueId) {\r\n                storageBuffer._rebuild();\r\n            }\r\n        }\r\n    }\r\n\r\n    protected override _restoreEngineAfterContextLost(initEngine: () => void) {\r\n        WebGPUCacheRenderPipelineTree.ResetCache();\r\n        WebGPUCacheBindGroups.ResetCache();\r\n\r\n        // Clear the draw wrappers and material contexts\r\n        const cleanScenes = (scenes: Scene[]) => {\r\n            for (const scene of scenes) {\r\n                for (const mesh of scene.meshes) {\r\n                    const subMeshes = mesh.subMeshes;\r\n                    if (!subMeshes) {\r\n                        continue;\r\n                    }\r\n                    for (const subMesh of subMeshes) {\r\n                        subMesh._drawWrappers = [];\r\n                    }\r\n                }\r\n\r\n                for (const material of scene.materials) {\r\n                    material._materialContext?.reset();\r\n                }\r\n            }\r\n        };\r\n\r\n        cleanScenes(this.scenes);\r\n        cleanScenes(this._virtualScenes);\r\n\r\n        // The leftOver uniform buffers are removed from the list because they will be recreated when we rebuild the effects\r\n        const uboList: UniformBuffer[] = [];\r\n        for (const uniformBuffer of this._uniformBuffers) {\r\n            if (uniformBuffer.name.indexOf(\"leftOver\") < 0) {\r\n                uboList.push(uniformBuffer);\r\n            }\r\n        }\r\n        this._uniformBuffers = uboList;\r\n\r\n        super._restoreEngineAfterContextLost(initEngine);\r\n    }\r\n\r\n    /**\r\n     * Force a specific size of the canvas\r\n     * @param width defines the new canvas' width\r\n     * @param height defines the new canvas' height\r\n     * @param forceSetSize true to force setting the sizes of the underlying canvas\r\n     * @returns true if the size was changed\r\n     */\r\n    public override setSize(width: number, height: number, forceSetSize = false): boolean {\r\n        if (!super.setSize(width, height, forceSetSize)) {\r\n            return false;\r\n        }\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\"frame #\" + (this as any)._count + \" - setSize -\", width, height]);\r\n            }\r\n        }\r\n\r\n        this._initializeMainAttachments();\r\n\r\n        if (this.snapshotRendering) {\r\n            // reset snapshot rendering so that the next frame will record a new list of bundles\r\n            this.snapshotRenderingReset();\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    private _shaderProcessorWGSL: Nullable<IShaderProcessor>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override _getShaderProcessor(shaderLanguage: ShaderLanguage): Nullable<IShaderProcessor> {\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            return this._shaderProcessorWGSL;\r\n        }\r\n        return this._shaderProcessor;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getShaderProcessingContext(shaderLanguage: ShaderLanguage, pureMode: boolean): Nullable<_IShaderProcessingContext> {\r\n        return new WebGPUShaderProcessingContext(shaderLanguage, pureMode);\r\n    }\r\n\r\n    private _getCurrentRenderPass(): GPURenderPassEncoder {\r\n        if (this._currentRenderTarget && !this._currentRenderPass) {\r\n            // delayed creation of the render target pass, but we now need to create it as we are requested the render pass\r\n            this._startRenderTargetRenderPass(this._currentRenderTarget, false, null, false, false);\r\n        } else if (!this._currentRenderPass) {\r\n            this._startMainRenderPass(false);\r\n        }\r\n\r\n        return this._currentRenderPass!;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getCurrentRenderPassWrapper() {\r\n        return this._currentRenderTarget ? this._rttRenderPassWrapper : this._mainRenderPassWrapper;\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                          Static Pipeline WebGPU States\r\n    //------------------------------------------------------------------------------\r\n\r\n    /** @internal */\r\n    public applyStates() {\r\n        this._stencilStateComposer.apply();\r\n        this._cacheRenderPipeline.setAlphaBlendEnabled(this._alphaState._alphaBlend, this._alphaState._numTargetEnabled);\r\n    }\r\n\r\n    /**\r\n     * Force the entire cache to be cleared\r\n     * You should not have to use this function unless your engine needs to share the WebGPU context with another engine\r\n     * @param bruteForce defines a boolean to force clearing ALL caches (including stencil, detoh and alpha states)\r\n     */\r\n    public wipeCaches(bruteForce?: boolean): void {\r\n        if (this.preventCacheWipeBetweenFrames && !bruteForce) {\r\n            return;\r\n        }\r\n\r\n        //this._currentEffect = null; // can't reset _currentEffect, else some crashes can occur (for eg in ProceduralTexture which calls bindFrameBuffer (which calls wipeCaches) after having called enableEffect and before drawing into the texture)\r\n        // _forceEnableEffect = true assumes the role of _currentEffect = null\r\n        this._forceEnableEffect = true;\r\n        this._currentIndexBuffer = null;\r\n        this._currentOverrideVertexBuffers = null;\r\n        this._cacheRenderPipeline.setBuffers(null, null, null);\r\n\r\n        if (bruteForce) {\r\n            this._stencilStateComposer.reset();\r\n\r\n            this._depthCullingState.reset();\r\n            this._depthCullingState.depthFunc = Constants.LEQUAL;\r\n\r\n            this._alphaState.reset();\r\n            this._resetAlphaMode();\r\n            this._cacheRenderPipeline.setAlphaBlendFactors(this._alphaState._blendFunctionParameters, this._alphaState._blendEquationParameters);\r\n            this._cacheRenderPipeline.setAlphaBlendEnabled(this._alphaState._alphaBlend, this._alphaState._numTargetEnabled);\r\n\r\n            this.setColorWrite(true);\r\n        }\r\n\r\n        this._cachedVertexBuffers = null;\r\n        this._cachedIndexBuffer = null;\r\n        this._cachedEffectForVertexBuffers = null;\r\n    }\r\n\r\n    /**\r\n     * Enable or disable color writing\r\n     * @param enable defines the state to set\r\n     */\r\n    public override setColorWrite(enable: boolean): void {\r\n        this._colorWriteLocal = enable;\r\n        this._cacheRenderPipeline.setWriteMask(enable ? 0xf : 0);\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if color writing is enabled\r\n     * @returns the current color writing state\r\n     */\r\n    public override getColorWrite(): boolean {\r\n        return this._colorWriteLocal;\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Dynamic WebGPU States\r\n    //------------------------------------------------------------------------------\r\n\r\n    // index 0 is for main render pass, 1 for RTT render pass\r\n    private _viewportsCurrent: { x: number; y: number; w: number; h: number } = { x: 0, y: 0, w: 0, h: 0 };\r\n\r\n    private _mustUpdateViewport(): boolean {\r\n        const x = this._viewportCached.x,\r\n            y = this._viewportCached.y,\r\n            w = this._viewportCached.z,\r\n            h = this._viewportCached.w;\r\n\r\n        const update = this._viewportsCurrent.x !== x || this._viewportsCurrent.y !== y || this._viewportsCurrent.w !== w || this._viewportsCurrent.h !== h;\r\n\r\n        if (update) {\r\n            this._viewportsCurrent.x = this._viewportCached.x;\r\n            this._viewportsCurrent.y = this._viewportCached.y;\r\n            this._viewportsCurrent.w = this._viewportCached.z;\r\n            this._viewportsCurrent.h = this._viewportCached.w;\r\n        }\r\n\r\n        return update;\r\n    }\r\n\r\n    private _applyViewport(bundleList: Nullable<WebGPUBundleList>): void {\r\n        const x = Math.floor(this._viewportCached.x);\r\n        const w = Math.floor(this._viewportCached.z);\r\n        const h = Math.floor(this._viewportCached.w);\r\n\r\n        let y = Math.floor(this._viewportCached.y);\r\n\r\n        if (!this._currentRenderTarget) {\r\n            y = this.getRenderHeight(true) - y - h;\r\n        }\r\n\r\n        if (bundleList) {\r\n            bundleList.addItem(new WebGPURenderItemViewport(x, y, w, h));\r\n        } else {\r\n            this._getCurrentRenderPass().setViewport(x, y, w, h, 0, 1);\r\n        }\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\r\n                    \"frame #\" + (this as any)._count + \" - viewport applied - (\",\r\n                    this._viewportCached.x,\r\n                    this._viewportCached.y,\r\n                    this._viewportCached.z,\r\n                    this._viewportCached.w,\r\n                    \") current pass is main pass=\" + this._currentPassIsMainPass(),\r\n                ]);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _viewport(x: number, y: number, width: number, height: number): void {\r\n        this._viewportCached.x = x;\r\n        this._viewportCached.y = y;\r\n        this._viewportCached.z = width;\r\n        this._viewportCached.w = height;\r\n    }\r\n\r\n    private _scissorsCurrent: { x: number; y: number; w: number; h: number } = { x: 0, y: 0, w: 0, h: 0 };\r\n    protected _scissorCached = { x: 0, y: 0, z: 0, w: 0 };\r\n\r\n    private _mustUpdateScissor(): boolean {\r\n        const x = this._scissorCached.x,\r\n            y = this._scissorCached.y,\r\n            w = this._scissorCached.z,\r\n            h = this._scissorCached.w;\r\n\r\n        const update = this._scissorsCurrent.x !== x || this._scissorsCurrent.y !== y || this._scissorsCurrent.w !== w || this._scissorsCurrent.h !== h;\r\n\r\n        if (update) {\r\n            this._scissorsCurrent.x = this._scissorCached.x;\r\n            this._scissorsCurrent.y = this._scissorCached.y;\r\n            this._scissorsCurrent.w = this._scissorCached.z;\r\n            this._scissorsCurrent.h = this._scissorCached.w;\r\n        }\r\n\r\n        return update;\r\n    }\r\n\r\n    private _applyScissor(bundleList: Nullable<WebGPUBundleList>): void {\r\n        const y = this._currentRenderTarget ? this._scissorCached.y : this.getRenderHeight() - this._scissorCached.w - this._scissorCached.y;\r\n\r\n        if (bundleList) {\r\n            bundleList.addItem(new WebGPURenderItemScissor(this._scissorCached.x, y, this._scissorCached.z, this._scissorCached.w));\r\n        } else {\r\n            this._getCurrentRenderPass().setScissorRect(this._scissorCached.x, y, this._scissorCached.z, this._scissorCached.w);\r\n        }\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\r\n                    \"frame #\" + (this as any)._count + \" - scissor applied - (\",\r\n                    this._scissorCached.x,\r\n                    this._scissorCached.y,\r\n                    this._scissorCached.z,\r\n                    this._scissorCached.w,\r\n                    \") current pass is main pass=\" + this._currentPassIsMainPass(),\r\n                ]);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _scissorIsActive() {\r\n        return this._scissorCached.x !== 0 || this._scissorCached.y !== 0 || this._scissorCached.z !== 0 || this._scissorCached.w !== 0;\r\n    }\r\n\r\n    public enableScissor(x: number, y: number, width: number, height: number): void {\r\n        this._scissorCached.x = x;\r\n        this._scissorCached.y = y;\r\n        this._scissorCached.z = width;\r\n        this._scissorCached.w = height;\r\n    }\r\n\r\n    public disableScissor() {\r\n        this._scissorCached.x = this._scissorCached.y = this._scissorCached.z = this._scissorCached.w = 0;\r\n        this._scissorsCurrent.x = this._scissorsCurrent.y = this._scissorsCurrent.w = this._scissorsCurrent.h = 0;\r\n    }\r\n\r\n    private _stencilRefsCurrent = -1;\r\n\r\n    private _mustUpdateStencilRef(): boolean {\r\n        const update = this._stencilStateComposer.funcRef !== this._stencilRefsCurrent;\r\n        if (update) {\r\n            this._stencilRefsCurrent = this._stencilStateComposer.funcRef;\r\n        }\r\n        return update;\r\n    }\r\n\r\n    private _applyStencilRef(bundleList: Nullable<WebGPUBundleList>): void {\r\n        if (bundleList) {\r\n            bundleList.addItem(new WebGPURenderItemStencilRef(this._stencilStateComposer.funcRef ?? 0));\r\n        } else {\r\n            this._getCurrentRenderPass().setStencilReference(this._stencilStateComposer.funcRef ?? 0);\r\n        }\r\n    }\r\n\r\n    private _blendColorsCurrent: Array<Nullable<number>> = [null, null, null, null];\r\n\r\n    private _mustUpdateBlendColor(): boolean {\r\n        const colorBlend = this._alphaState._blendConstants;\r\n\r\n        const update =\r\n            colorBlend[0] !== this._blendColorsCurrent[0] ||\r\n            colorBlend[1] !== this._blendColorsCurrent[1] ||\r\n            colorBlend[2] !== this._blendColorsCurrent[2] ||\r\n            colorBlend[3] !== this._blendColorsCurrent[3];\r\n\r\n        if (update) {\r\n            this._blendColorsCurrent[0] = colorBlend[0];\r\n            this._blendColorsCurrent[1] = colorBlend[1];\r\n            this._blendColorsCurrent[2] = colorBlend[2];\r\n            this._blendColorsCurrent[3] = colorBlend[3];\r\n        }\r\n\r\n        return update;\r\n    }\r\n\r\n    private _applyBlendColor(bundleList: Nullable<WebGPUBundleList>): void {\r\n        if (bundleList) {\r\n            bundleList.addItem(new WebGPURenderItemBlendColor(this._alphaState._blendConstants.slice()));\r\n        } else {\r\n            this._getCurrentRenderPass().setBlendConstant(this._alphaState._blendConstants as GPUColor);\r\n        }\r\n    }\r\n\r\n    private _resetRenderPassStates() {\r\n        this._viewportsCurrent.x = this._viewportsCurrent.y = this._viewportsCurrent.w = this._viewportsCurrent.h = 0;\r\n        this._scissorsCurrent.x = this._scissorsCurrent.y = this._scissorsCurrent.w = this._scissorsCurrent.h = 0;\r\n        this._stencilRefsCurrent = -1;\r\n        this._blendColorsCurrent[0] = this._blendColorsCurrent[1] = this._blendColorsCurrent[2] = this._blendColorsCurrent[3] = null;\r\n    }\r\n\r\n    /**\r\n     * Clear the current render buffer or the current render target (if any is set up)\r\n     * @param color defines the color to use\r\n     * @param backBuffer defines if the back buffer must be cleared\r\n     * @param depth defines if the depth buffer must be cleared\r\n     * @param stencil defines if the stencil buffer must be cleared\r\n     */\r\n    public clear(color: Nullable<IColor4Like>, backBuffer: boolean, depth: boolean, stencil: boolean = false): void {\r\n        // Some PGs are using color3...\r\n        if (color && color.a === undefined) {\r\n            color.a = 1;\r\n        }\r\n\r\n        const hasScissor = this._scissorIsActive();\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\"frame #\" + (this as any)._count + \" - clear - backBuffer=\", backBuffer, \" depth=\", depth, \" stencil=\", stencil, \" scissor is active=\", hasScissor]);\r\n            }\r\n        }\r\n\r\n        // We need to recreate the render pass so that the new parameters for clear color / depth / stencil are taken into account\r\n        if (this._currentRenderTarget) {\r\n            if (hasScissor) {\r\n                if (!this._currentRenderPass) {\r\n                    this._startRenderTargetRenderPass(this._currentRenderTarget, false, backBuffer ? color : null, depth, stencil);\r\n                }\r\n                this._applyScissor(!this.compatibilityMode ? this._bundleList : null);\r\n                this._clearFullQuad(backBuffer ? color : null, depth, stencil);\r\n            } else {\r\n                if (this._currentRenderPass) {\r\n                    this._endCurrentRenderPass();\r\n                }\r\n                this._startRenderTargetRenderPass(this._currentRenderTarget, true, backBuffer ? color : null, depth, stencil);\r\n            }\r\n        } else {\r\n            if (!this._currentRenderPass || !hasScissor) {\r\n                this._startMainRenderPass(!hasScissor, backBuffer ? color : null, depth, stencil);\r\n            }\r\n            if (hasScissor) {\r\n                this._applyScissor(!this.compatibilityMode ? this._bundleList : null);\r\n                this._clearFullQuad(backBuffer ? color : null, depth, stencil);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _clearFullQuad(clearColor?: Nullable<IColor4Like>, clearDepth?: boolean, clearStencil?: boolean): void {\r\n        const renderPass = !this.compatibilityMode ? null : this._getCurrentRenderPass();\r\n\r\n        this._clearQuad.setColorFormat(this._colorFormat);\r\n        this._clearQuad.setDepthStencilFormat(this._depthTextureFormat);\r\n        this._clearQuad.setMRTAttachments(\r\n            this._cacheRenderPipeline.mrtAttachments ?? [],\r\n            this._cacheRenderPipeline.mrtTextureArray ?? [],\r\n            this._cacheRenderPipeline.mrtTextureCount\r\n        );\r\n\r\n        if (!this.compatibilityMode) {\r\n            this._bundleList.addItem(new WebGPURenderItemStencilRef(this._clearStencilValue));\r\n        } else {\r\n            renderPass!.setStencilReference(this._clearStencilValue);\r\n        }\r\n\r\n        const bundle = this._clearQuad.clear(renderPass, clearColor, clearDepth, clearStencil, this.currentSampleCount);\r\n\r\n        if (!this.compatibilityMode) {\r\n            this._bundleList.addBundle(bundle!);\r\n            this._applyStencilRef(this._bundleList);\r\n            this._reportDrawCall();\r\n        } else {\r\n            this._applyStencilRef(null);\r\n        }\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Vertex/Index/Storage Buffers\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Creates a vertex buffer\r\n     * @param data the data or the size for the vertex buffer\r\n     * @param _updatable whether the buffer should be created as updatable\r\n     * @param label defines the label of the buffer (for debug purpose)\r\n     * @returns the new buffer\r\n     */\r\n    public createVertexBuffer(data: DataArray | number, _updatable?: boolean, label?: string): DataBuffer {\r\n        let view: ArrayBufferView | number;\r\n\r\n        if (data instanceof Array) {\r\n            view = new Float32Array(data);\r\n        } else if (data instanceof ArrayBuffer) {\r\n            view = new Uint8Array(data);\r\n        } else {\r\n            view = data;\r\n        }\r\n\r\n        const dataBuffer = this._bufferManager.createBuffer(view, WebGPUConstants.BufferUsage.Vertex | WebGPUConstants.BufferUsage.CopyDst, label);\r\n        return dataBuffer;\r\n    }\r\n\r\n    /**\r\n     * Creates a vertex buffer\r\n     * @param data the data for the dynamic vertex buffer\r\n     * @param label defines the label of the buffer (for debug purpose)\r\n     * @returns the new buffer\r\n     */\r\n    public createDynamicVertexBuffer(data: DataArray, label?: string): DataBuffer {\r\n        return this.createVertexBuffer(data, undefined, label);\r\n    }\r\n\r\n    /**\r\n     * Creates a new index buffer\r\n     * @param indices defines the content of the index buffer\r\n     * @param _updatable defines if the index buffer must be updatable\r\n     * @param label defines the label of the buffer (for debug purpose)\r\n     * @returns a new buffer\r\n     */\r\n    public createIndexBuffer(indices: IndicesArray, _updatable?: boolean, label?: string): DataBuffer {\r\n        let is32Bits = true;\r\n        let view: ArrayBufferView | undefined;\r\n\r\n        if (indices instanceof Uint32Array || indices instanceof Int32Array) {\r\n            view = indices;\r\n        } else if (indices instanceof Uint16Array) {\r\n            view = indices;\r\n            is32Bits = false;\r\n        } else {\r\n            for (let index = 0; index < indices.length; index++) {\r\n                if (indices[index] > 65535) {\r\n                    view = new Uint32Array(indices);\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (!view) {\r\n                view = new Uint16Array(indices);\r\n                is32Bits = false;\r\n            }\r\n        }\r\n\r\n        const dataBuffer = this._bufferManager.createBuffer(view, WebGPUConstants.BufferUsage.Index | WebGPUConstants.BufferUsage.CopyDst, label);\r\n        dataBuffer.is32Bits = is32Bits;\r\n        return dataBuffer;\r\n    }\r\n\r\n    /**\r\n     * Update a dynamic index buffer\r\n     * @param indexBuffer defines the target index buffer\r\n     * @param indices defines the data to update\r\n     * @param offset defines the offset in the target index buffer where update should start\r\n     */\r\n    public override updateDynamicIndexBuffer(indexBuffer: DataBuffer, indices: IndicesArray, offset: number = 0): void {\r\n        const gpuBuffer = indexBuffer as WebGPUDataBuffer;\r\n\r\n        let view: ArrayBufferView;\r\n        if (indexBuffer.is32Bits) {\r\n            view = indices instanceof Uint32Array ? indices : new Uint32Array(indices);\r\n        } else {\r\n            view = indices instanceof Uint16Array ? indices : new Uint16Array(indices);\r\n        }\r\n\r\n        this._bufferManager.setSubData(gpuBuffer, offset, view);\r\n    }\r\n\r\n    /**\r\n     * Updates a dynamic vertex buffer.\r\n     * @param vertexBuffer the vertex buffer to update\r\n     * @param data the data used to update the vertex buffer\r\n     * @param byteOffset the byte offset of the data\r\n     * @param byteLength the byte length of the data\r\n     */\r\n    public override updateDynamicVertexBuffer(vertexBuffer: DataBuffer, data: DataArray, byteOffset?: number, byteLength?: number): void {\r\n        const dataBuffer = vertexBuffer as WebGPUDataBuffer;\r\n        if (byteOffset === undefined) {\r\n            byteOffset = 0;\r\n        }\r\n\r\n        let view: ArrayBufferView;\r\n        if (byteLength === undefined) {\r\n            if (data instanceof Array) {\r\n                view = new Float32Array(data);\r\n            } else if (data instanceof ArrayBuffer) {\r\n                view = new Uint8Array(data);\r\n            } else {\r\n                view = data;\r\n            }\r\n            byteLength = view.byteLength;\r\n        } else {\r\n            if (data instanceof Array) {\r\n                view = new Float32Array(data);\r\n            } else if (data instanceof ArrayBuffer) {\r\n                view = new Uint8Array(data);\r\n            } else {\r\n                view = data;\r\n            }\r\n        }\r\n\r\n        this._bufferManager.setSubData(dataBuffer, byteOffset, view, 0, byteLength);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _createBuffer(data: DataArray | number, creationFlags: number, label?: string): DataBuffer {\r\n        let view: ArrayBufferView | number;\r\n\r\n        if (data instanceof Array) {\r\n            view = new Float32Array(data);\r\n        } else if (data instanceof ArrayBuffer) {\r\n            view = new Uint8Array(data);\r\n        } else {\r\n            view = data;\r\n        }\r\n\r\n        let flags = 0;\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_READ) {\r\n            flags |= WebGPUConstants.BufferUsage.CopySrc;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_WRITE) {\r\n            flags |= WebGPUConstants.BufferUsage.CopyDst;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_UNIFORM) {\r\n            flags |= WebGPUConstants.BufferUsage.Uniform;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_VERTEX) {\r\n            flags |= WebGPUConstants.BufferUsage.Vertex;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_INDEX) {\r\n            flags |= WebGPUConstants.BufferUsage.Index;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_STORAGE) {\r\n            flags |= WebGPUConstants.BufferUsage.Storage;\r\n        }\r\n        if (creationFlags & Constants.BUFFER_CREATIONFLAG_INDIRECT) {\r\n            flags |= WebGPUConstants.BufferUsage.Indirect;\r\n        }\r\n\r\n        return this._bufferManager.createBuffer(view, flags, label);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public bindBuffersDirectly(): void {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Not implemented on WebGPU\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public updateAndBindInstancesBuffer(): void {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Not implemented on WebGPU\";\r\n    }\r\n\r\n    /**\r\n     * Unbind all instance attributes\r\n     */\r\n    public unbindInstanceAttributes(): void {\r\n        // Does nothing\r\n    }\r\n\r\n    /**\r\n     * Bind a list of vertex buffers with the engine\r\n     * @param vertexBuffers defines the list of vertex buffers to bind\r\n     * @param indexBuffer defines the index buffer to bind\r\n     * @param effect defines the effect associated with the vertex buffers\r\n     * @param overrideVertexBuffers defines optional list of avertex buffers that overrides the entries in vertexBuffers\r\n     */\r\n    public bindBuffers(\r\n        vertexBuffers: { [key: string]: Nullable<VertexBuffer> },\r\n        indexBuffer: Nullable<DataBuffer>,\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): void {\r\n        this._currentIndexBuffer = indexBuffer;\r\n        this._currentOverrideVertexBuffers = overrideVertexBuffers ?? null;\r\n        this._cacheRenderPipeline.setBuffers(vertexBuffers, indexBuffer, this._currentOverrideVertexBuffers);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseBuffer(buffer: DataBuffer): boolean {\r\n        return this._bufferManager.releaseBuffer(buffer);\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Uniform Buffers\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Create an uniform buffer\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n     * @param elements defines the content of the uniform buffer\r\n     * @param label defines a name for the buffer (for debugging purpose)\r\n     * @returns the webGL uniform buffer\r\n     */\r\n    public createUniformBuffer(elements: FloatArray, label?: string): DataBuffer {\r\n        let view: Float32Array;\r\n        if (elements instanceof Array) {\r\n            view = new Float32Array(elements);\r\n        } else {\r\n            view = elements;\r\n        }\r\n\r\n        const dataBuffer = this._bufferManager.createBuffer(view, WebGPUConstants.BufferUsage.Uniform | WebGPUConstants.BufferUsage.CopyDst, label);\r\n        return dataBuffer;\r\n    }\r\n\r\n    /**\r\n     * Create a dynamic uniform buffer (no different from a non dynamic uniform buffer in WebGPU)\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n     * @param elements defines the content of the uniform buffer\r\n     * @param label defines a name for the buffer (for debugging purpose)\r\n     * @returns the webGL uniform buffer\r\n     */\r\n    public createDynamicUniformBuffer(elements: FloatArray, label?: string): DataBuffer {\r\n        return this.createUniformBuffer(elements, label);\r\n    }\r\n\r\n    /**\r\n     * Update an existing uniform buffer\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n     * @param uniformBuffer defines the target uniform buffer\r\n     * @param elements defines the content to update\r\n     * @param offset defines the offset in the uniform buffer where update should start\r\n     * @param count defines the size of the data to update\r\n     */\r\n    public updateUniformBuffer(uniformBuffer: DataBuffer, elements: FloatArray, offset?: number, count?: number): void {\r\n        if (offset === undefined) {\r\n            offset = 0;\r\n        }\r\n\r\n        const dataBuffer = uniformBuffer as WebGPUDataBuffer;\r\n        let view: Float32Array;\r\n        if (count === undefined) {\r\n            if (elements instanceof Float32Array) {\r\n                view = elements;\r\n            } else {\r\n                view = new Float32Array(elements);\r\n            }\r\n            count = view.byteLength;\r\n        } else {\r\n            if (elements instanceof Float32Array) {\r\n                view = elements;\r\n            } else {\r\n                view = new Float32Array(elements);\r\n            }\r\n        }\r\n\r\n        this._bufferManager.setSubData(dataBuffer, offset, view, 0, count);\r\n    }\r\n\r\n    /**\r\n     * Bind a buffer to the current draw context\r\n     * @param buffer defines the buffer to bind\r\n     * @param _location not used in WebGPU\r\n     * @param name Name of the uniform variable to bind\r\n     */\r\n    public bindUniformBufferBase(buffer: DataBuffer, _location: number, name: string): void {\r\n        this._currentDrawContext.setBuffer(name, buffer as WebGPUDataBuffer);\r\n    }\r\n\r\n    /**\r\n     * Unused in WebGPU\r\n     */\r\n    public bindUniformBlock(): void {}\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Effects\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Create a new effect (used to store vertex/fragment shaders)\r\n     * @param baseName defines the base name of the effect (The name of file without .fragment.fx or .vertex.fx)\r\n     * @param attributesNamesOrOptions defines either a list of attribute names or an IEffectCreationOptions object\r\n     * @param uniformsNamesOrEngine defines either a list of uniform names or the engine to use\r\n     * @param samplers defines an array of string used to represent textures\r\n     * @param defines defines the string containing the defines to use to compile the shaders\r\n     * @param fallbacks defines the list of potential fallbacks to use if shader compilation fails\r\n     * @param onCompiled defines a function to call when the effect creation is successful\r\n     * @param onError defines a function to call when the effect creation has failed\r\n     * @param indexParameters defines an object containing the index values to use to compile shaders (like the maximum number of simultaneous lights)\r\n     * @param shaderLanguage the language the shader is written in (default: GLSL)\r\n     * @param extraInitializationsAsync additional async code to run before preparing the effect\r\n     * @returns the new Effect\r\n     */\r\n    public createEffect(\r\n        baseName: string | (IShaderPath & { vertexToken?: string; fragmentToken?: string }),\r\n        attributesNamesOrOptions: string[] | IEffectCreationOptions,\r\n        uniformsNamesOrEngine: string[] | AbstractEngine,\r\n        samplers?: string[],\r\n        defines?: string,\r\n        fallbacks?: EffectFallbacks,\r\n        onCompiled?: Nullable<(effect: Effect) => void>,\r\n        onError?: Nullable<(effect: Effect, errors: string) => void>,\r\n        indexParameters?: any,\r\n        shaderLanguage = ShaderLanguage.GLSL,\r\n        extraInitializationsAsync?: () => Promise<void>\r\n    ): Effect {\r\n        const vertex = typeof baseName === \"string\" ? baseName : baseName.vertexToken || baseName.vertexSource || baseName.vertexElement || baseName.vertex;\r\n        const fragment = typeof baseName === \"string\" ? baseName : baseName.fragmentToken || baseName.fragmentSource || baseName.fragmentElement || baseName.fragment;\r\n        const globalDefines = this._getGlobalDefines()!;\r\n\r\n        const isOptions = (attributesNamesOrOptions as IEffectCreationOptions).attributes !== undefined;\r\n\r\n        let fullDefines = defines ?? (<IEffectCreationOptions>attributesNamesOrOptions).defines ?? \"\";\r\n\r\n        if (globalDefines) {\r\n            fullDefines += \"\\n\" + globalDefines;\r\n        }\r\n\r\n        const name = vertex + \"+\" + fragment + \"@\" + fullDefines;\r\n        if (this._compiledEffects[name]) {\r\n            const compiledEffect = this._compiledEffects[name];\r\n            if (onCompiled && compiledEffect.isReady()) {\r\n                onCompiled(compiledEffect);\r\n            }\r\n            compiledEffect._refCount++;\r\n            return compiledEffect;\r\n        }\r\n        const effect = new Effect(\r\n            baseName,\r\n            attributesNamesOrOptions,\r\n            isOptions ? this : uniformsNamesOrEngine,\r\n            samplers,\r\n            this,\r\n            defines,\r\n            fallbacks,\r\n            onCompiled,\r\n            onError,\r\n            indexParameters,\r\n            name,\r\n            (<IEffectCreationOptions>attributesNamesOrOptions).shaderLanguage ?? shaderLanguage,\r\n            (<IEffectCreationOptions>attributesNamesOrOptions).extraInitializationsAsync ?? extraInitializationsAsync\r\n        );\r\n        this._compiledEffects[name] = effect;\r\n\r\n        return effect;\r\n    }\r\n\r\n    private _compileRawShaderToSpirV(source: string, type: string): Uint32Array {\r\n        return this._glslang.compileGLSL(source, type);\r\n    }\r\n\r\n    private _compileShaderToSpirV(source: string, type: string, defines: Nullable<string>, shaderVersion: string): Uint32Array {\r\n        return this._compileRawShaderToSpirV(shaderVersion + (defines ? defines + \"\\n\" : \"\") + source, type);\r\n    }\r\n\r\n    private _getWGSLShader(source: string, type: string, defines: Nullable<string>): string {\r\n        if (defines) {\r\n            defines = \"//\" + defines.split(\"\\n\").join(\"\\n//\") + \"\\n\";\r\n        } else {\r\n            defines = \"\";\r\n        }\r\n        return defines + source;\r\n    }\r\n\r\n    private _createPipelineStageDescriptor(\r\n        vertexShader: Uint32Array | string,\r\n        fragmentShader: Uint32Array | string,\r\n        shaderLanguage: ShaderLanguage,\r\n        disableUniformityAnalysisInVertex: boolean,\r\n        disableUniformityAnalysisInFragment: boolean\r\n    ): IWebGPURenderPipelineStageDescriptor {\r\n        if (this._tintWASM && shaderLanguage === ShaderLanguage.GLSL) {\r\n            vertexShader = this._tintWASM.convertSpirV2WGSL(vertexShader as Uint32Array, disableUniformityAnalysisInVertex);\r\n            fragmentShader = this._tintWASM.convertSpirV2WGSL(fragmentShader as Uint32Array, disableUniformityAnalysisInFragment);\r\n        }\r\n\r\n        return {\r\n            vertexStage: {\r\n                module: this._device.createShaderModule({\r\n                    label: \"vertex\",\r\n                    code: vertexShader,\r\n                }),\r\n                entryPoint: \"main\",\r\n            },\r\n            fragmentStage: {\r\n                module: this._device.createShaderModule({\r\n                    label: \"fragment\",\r\n                    code: fragmentShader,\r\n                }),\r\n                entryPoint: \"main\",\r\n            },\r\n        };\r\n    }\r\n\r\n    private _compileRawPipelineStageDescriptor(vertexCode: string, fragmentCode: string, shaderLanguage: ShaderLanguage): IWebGPURenderPipelineStageDescriptor {\r\n        const disableUniformityAnalysisInVertex = vertexCode.indexOf(Constants.DISABLEUA) >= 0;\r\n        const disableUniformityAnalysisInFragment = fragmentCode.indexOf(Constants.DISABLEUA) >= 0;\r\n\r\n        const vertexShader = shaderLanguage === ShaderLanguage.GLSL ? this._compileRawShaderToSpirV(vertexCode, \"vertex\") : vertexCode;\r\n        const fragmentShader = shaderLanguage === ShaderLanguage.GLSL ? this._compileRawShaderToSpirV(fragmentCode, \"fragment\") : fragmentCode;\r\n\r\n        return this._createPipelineStageDescriptor(vertexShader, fragmentShader, shaderLanguage, disableUniformityAnalysisInVertex, disableUniformityAnalysisInFragment);\r\n    }\r\n\r\n    private _compilePipelineStageDescriptor(\r\n        vertexCode: string,\r\n        fragmentCode: string,\r\n        defines: Nullable<string>,\r\n        shaderLanguage: ShaderLanguage\r\n    ): IWebGPURenderPipelineStageDescriptor {\r\n        this.onBeforeShaderCompilationObservable.notifyObservers(this);\r\n\r\n        const disableUniformityAnalysisInVertex = vertexCode.indexOf(Constants.DISABLEUA) >= 0;\r\n        const disableUniformityAnalysisInFragment = fragmentCode.indexOf(Constants.DISABLEUA) >= 0;\r\n\r\n        const shaderVersion = \"#version 450\\n\";\r\n        const vertexShader =\r\n            shaderLanguage === ShaderLanguage.GLSL ? this._compileShaderToSpirV(vertexCode, \"vertex\", defines, shaderVersion) : this._getWGSLShader(vertexCode, \"vertex\", defines);\r\n        const fragmentShader =\r\n            shaderLanguage === ShaderLanguage.GLSL\r\n                ? this._compileShaderToSpirV(fragmentCode, \"fragment\", defines, shaderVersion)\r\n                : this._getWGSLShader(fragmentCode, \"fragment\", defines);\r\n\r\n        const program = this._createPipelineStageDescriptor(vertexShader, fragmentShader, shaderLanguage, disableUniformityAnalysisInVertex, disableUniformityAnalysisInFragment);\r\n\r\n        this.onAfterShaderCompilationObservable.notifyObservers(this);\r\n\r\n        return program;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public createRawShaderProgram(): WebGLProgram {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Not available on WebGPU\";\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public createShaderProgram(): WebGLProgram {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Not available on WebGPU\";\r\n    }\r\n\r\n    /**\r\n     * Inline functions in shader code that are marked to be inlined\r\n     * @param code code to inline\r\n     * @returns inlined code\r\n     */\r\n    public inlineShaderCode(code: string): string {\r\n        const sci = new ShaderCodeInliner(code);\r\n        sci.debug = false;\r\n        sci.processCode();\r\n        return sci.code;\r\n    }\r\n\r\n    /**\r\n     * Creates a new pipeline context\r\n     * @param shaderProcessingContext defines the shader processing context used during the processing if available\r\n     * @returns the new pipeline\r\n     */\r\n    public createPipelineContext(shaderProcessingContext: Nullable<_IShaderProcessingContext>): IPipelineContext {\r\n        return new WebGPUPipelineContext(shaderProcessingContext! as WebGPUShaderProcessingContext, this);\r\n    }\r\n\r\n    /**\r\n     * Creates a new material context\r\n     * @returns the new context\r\n     */\r\n    public createMaterialContext(): WebGPUMaterialContext | undefined {\r\n        return new WebGPUMaterialContext();\r\n    }\r\n\r\n    /**\r\n     * Creates a new draw context\r\n     * @returns the new context\r\n     */\r\n    public createDrawContext(): WebGPUDrawContext | undefined {\r\n        return new WebGPUDrawContext(this._bufferManager);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\r\n    public async _preparePipelineContextAsync(\r\n        pipelineContext: IPipelineContext,\r\n        vertexSourceCode: string,\r\n        fragmentSourceCode: string,\r\n        createAsRaw: boolean,\r\n        rawVertexSourceCode: string,\r\n        rawFragmentSourceCode: string,\r\n        _rebuildRebind: any,\r\n        defines: Nullable<string>,\r\n        _transformFeedbackVaryings: Nullable<string[]>,\r\n        _key: string,\r\n        onReady: () => void\r\n    ) {\r\n        const webGpuContext = pipelineContext as WebGPUPipelineContext;\r\n        const shaderLanguage = webGpuContext.shaderProcessingContext.shaderLanguage;\r\n\r\n        if (shaderLanguage === ShaderLanguage.GLSL && !this._glslangAndTintAreFullyLoaded) {\r\n            await this.prepareGlslangAndTintAsync();\r\n        }\r\n\r\n        if (this.dbgShowShaderCode) {\r\n            Logger.Log([\"defines\", defines]);\r\n            Logger.Log(vertexSourceCode);\r\n            Logger.Log(fragmentSourceCode);\r\n            Logger.Log(\"***********************************************\");\r\n        }\r\n\r\n        webGpuContext.sources = {\r\n            fragment: fragmentSourceCode,\r\n            vertex: vertexSourceCode,\r\n            rawVertex: rawVertexSourceCode,\r\n            rawFragment: rawFragmentSourceCode,\r\n        };\r\n\r\n        if (createAsRaw) {\r\n            webGpuContext.stages = this._compileRawPipelineStageDescriptor(vertexSourceCode, fragmentSourceCode, shaderLanguage);\r\n        } else {\r\n            webGpuContext.stages = this._compilePipelineStageDescriptor(vertexSourceCode, fragmentSourceCode, defines, shaderLanguage);\r\n        }\r\n\r\n        onReady();\r\n    }\r\n\r\n    /**\r\n     * Gets the list of active attributes for a given WebGPU program\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param attributesNames defines the list of attribute names to get\r\n     * @returns an array of indices indicating the offset of each attribute\r\n     */\r\n    public getAttributes(pipelineContext: IPipelineContext, attributesNames: string[]): number[] {\r\n        const results = new Array(attributesNames.length);\r\n        const gpuPipelineContext = pipelineContext as WebGPUPipelineContext;\r\n\r\n        for (let i = 0; i < attributesNames.length; i++) {\r\n            const attributeName = attributesNames[i];\r\n            const attributeLocation = gpuPipelineContext.shaderProcessingContext.availableAttributes[attributeName];\r\n            if (attributeLocation === undefined) {\r\n                continue;\r\n            }\r\n\r\n            results[i] = attributeLocation;\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Activates an effect, making it the current one (ie. the one used for rendering)\r\n     * @param effect defines the effect to activate\r\n     */\r\n    public enableEffect(effect: Nullable<Effect | DrawWrapper>): void {\r\n        if (!effect) {\r\n            return;\r\n        }\r\n\r\n        if (!IsWrapper(effect)) {\r\n            this._currentEffect = effect;\r\n            this._currentMaterialContext = this._defaultMaterialContext;\r\n            this._currentDrawContext = this._defaultDrawContext;\r\n            this._counters.numEnableEffects++;\r\n            if (this.dbgLogIfNotDrawWrapper) {\r\n                Logger.Warn(\r\n                    `enableEffect has been called with an Effect and not a Wrapper! effect.uniqueId=${effect.uniqueId}, effect.name=${effect.name}, effect.name.vertex=${typeof effect.name === \"string\" ? \"\" : effect.name.vertex}, effect.name.fragment=${typeof effect.name === \"string\" ? \"\" : effect.name.fragment}`,\r\n                    10\r\n                );\r\n            }\r\n        } else if (\r\n            !effect.effect ||\r\n            (effect.effect === this._currentEffect &&\r\n                effect.materialContext === this._currentMaterialContext &&\r\n                effect.drawContext === this._currentDrawContext &&\r\n                !this._forceEnableEffect)\r\n        ) {\r\n            if (!effect.effect && this.dbgShowEmptyEnableEffectCalls) {\r\n                Logger.Log([\"drawWrapper=\", effect]);\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"Invalid call to enableEffect: the effect property is empty!\";\r\n            }\r\n            return;\r\n        } else {\r\n            this._currentEffect = effect.effect;\r\n            this._currentMaterialContext = effect.materialContext as WebGPUMaterialContext;\r\n            this._currentDrawContext = effect.drawContext as WebGPUDrawContext;\r\n            this._counters.numEnableDrawWrapper++;\r\n            if (!this._currentMaterialContext) {\r\n                Logger.Log([\"drawWrapper=\", effect]);\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw `Invalid call to enableEffect: the materialContext property is empty!`;\r\n            }\r\n        }\r\n\r\n        this._stencilStateComposer.stencilMaterial = undefined;\r\n\r\n        this._forceEnableEffect = false;\r\n\r\n        if (this._currentEffect.onBind) {\r\n            this._currentEffect.onBind(this._currentEffect);\r\n        }\r\n        if (this._currentEffect._onBindObservable) {\r\n            this._currentEffect._onBindObservable.notifyObservers(this._currentEffect);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseEffect(effect: Effect): void {\r\n        if (this._compiledEffects[effect._key]) {\r\n            delete this._compiledEffects[effect._key];\r\n\r\n            this._deletePipelineContext(effect.getPipelineContext() as WebGPUPipelineContext);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Force the engine to release all cached effects. This means that next effect compilation will have to be done completely even if a similar effect was already compiled\r\n     */\r\n    public releaseEffects() {\r\n        for (const name in this._compiledEffects) {\r\n            const webGPUPipelineContext = this._compiledEffects[name].getPipelineContext() as WebGPUPipelineContext;\r\n            this._deletePipelineContext(webGPUPipelineContext);\r\n        }\r\n\r\n        this._compiledEffects = {};\r\n\r\n        this.onReleaseEffectsObservable.notifyObservers(this);\r\n    }\r\n\r\n    public _deletePipelineContext(pipelineContext: IPipelineContext): void {\r\n        const webgpuPipelineContext = pipelineContext as WebGPUPipelineContext;\r\n        if (webgpuPipelineContext) {\r\n            resetCachedPipeline(webgpuPipelineContext);\r\n        }\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Textures\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Gets a boolean indicating that only power of 2 textures are supported\r\n     * Please note that you can still use non power of 2 textures but in this case the engine will forcefully convert them\r\n     */\r\n    public get needPOTTextures(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _createHardwareTexture(): IHardwareTextureWrapper {\r\n        return new WebGPUHardwareTexture(this);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseTexture(texture: InternalTexture): void {\r\n        const index = this._internalTexturesCache.indexOf(texture);\r\n        if (index !== -1) {\r\n            this._internalTexturesCache.splice(index, 1);\r\n        }\r\n\r\n        this._textureHelper.releaseTexture(texture);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getRGBABufferInternalSizedFormat(): number {\r\n        return Constants.TEXTUREFORMAT_RGBA;\r\n    }\r\n\r\n    public updateTextureComparisonFunction(texture: InternalTexture, comparisonFunction: number): void {\r\n        texture._comparisonFunction = comparisonFunction;\r\n    }\r\n\r\n    /**\r\n     * Creates an internal texture without binding it to a framebuffer\r\n     * @internal\r\n     * @param size defines the size of the texture\r\n     * @param options defines the options used to create the texture\r\n     * @param delayGPUTextureCreation true to delay the texture creation the first time it is really needed. false to create it right away\r\n     * @param source source type of the texture\r\n     * @returns a new internal texture\r\n     */\r\n    public _createInternalTexture(\r\n        size: TextureSize,\r\n        options: boolean | InternalTextureCreationOptions,\r\n        delayGPUTextureCreation = true,\r\n        source = InternalTextureSource.Unknown\r\n    ): InternalTexture {\r\n        const fullOptions: InternalTextureCreationOptions = {};\r\n\r\n        if (options !== undefined && typeof options === \"object\") {\r\n            fullOptions.generateMipMaps = options.generateMipMaps;\r\n            fullOptions.createMipMaps = options.createMipMaps;\r\n            fullOptions.type = options.type === undefined ? Constants.TEXTURETYPE_UNSIGNED_BYTE : options.type;\r\n            fullOptions.samplingMode = options.samplingMode === undefined ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : options.samplingMode;\r\n            fullOptions.format = options.format === undefined ? Constants.TEXTUREFORMAT_RGBA : options.format;\r\n            fullOptions.samples = options.samples ?? 1;\r\n            fullOptions.creationFlags = options.creationFlags ?? 0;\r\n            fullOptions.useSRGBBuffer = options.useSRGBBuffer ?? false;\r\n            fullOptions.label = options.label;\r\n        } else {\r\n            fullOptions.generateMipMaps = options;\r\n            fullOptions.type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n            fullOptions.samplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n            fullOptions.format = Constants.TEXTUREFORMAT_RGBA;\r\n            fullOptions.samples = 1;\r\n            fullOptions.creationFlags = 0;\r\n            fullOptions.useSRGBBuffer = false;\r\n        }\r\n\r\n        if (fullOptions.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n            fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        } else if (fullOptions.type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n            fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n        if (fullOptions.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n            fullOptions.type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n            Logger.Warn(\"Float textures are not supported. Type forced to TEXTURETYPE_UNSIGNED_BYTE\");\r\n        }\r\n\r\n        const texture = new InternalTexture(this, source);\r\n\r\n        const width = (<{ width: number; height: number; layers?: number }>size).width ?? <number>size;\r\n        const height = (<{ width: number; height: number; layers?: number }>size).height ?? <number>size;\r\n        const depth = (<{ width: number; height: number; depth?: number; layers?: number }>size).depth ?? 0;\r\n        const layers = (<{ width: number; height: number; depth?: number; layers?: number }>size).layers ?? 0;\r\n\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.depth = depth || layers;\r\n        texture.isReady = true;\r\n        texture.samples = fullOptions.samples;\r\n        texture.generateMipMaps = !!fullOptions.generateMipMaps;\r\n        texture.samplingMode = fullOptions.samplingMode;\r\n        texture.type = fullOptions.type;\r\n        texture.format = fullOptions.format;\r\n        texture.is2DArray = layers > 0;\r\n        texture.is3D = depth > 0;\r\n        texture._cachedWrapU = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n        texture._cachedWrapV = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n        texture._useSRGBBuffer = fullOptions.useSRGBBuffer;\r\n        texture.label = fullOptions.label;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        if (!delayGPUTextureCreation) {\r\n            const createMipMapsOnly = !fullOptions.generateMipMaps && fullOptions.createMipMaps;\r\n\r\n            if (createMipMapsOnly) {\r\n                // So that the call to createGPUTextureForInternalTexture creates the mipmaps\r\n                texture.generateMipMaps = true;\r\n            }\r\n\r\n            this._textureHelper.createGPUTextureForInternalTexture(texture, width, height, layers || 1, fullOptions.creationFlags);\r\n\r\n            if (createMipMapsOnly) {\r\n                // So that we don't automatically generate mipmaps when the render target is unbound\r\n                texture.generateMipMaps = false;\r\n            }\r\n        }\r\n\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * Usually called from Texture.ts.\r\n     * Passed information to create a hardware texture\r\n     * @param url defines a value which contains one of the following:\r\n     * * A conventional http URL, e.g. 'http://...' or 'file://...'\r\n     * * A base64 string of in-line texture data, e.g. 'data:image/jpg;base64,/...'\r\n     * * An indicator that data being passed using the buffer parameter, e.g. 'data:mytexture.jpg'\r\n     * @param noMipmap defines a boolean indicating that no mipmaps shall be generated.  Ignored for compressed textures.  They must be in the file\r\n     * @param invertY when true, image is flipped when loaded.  You probably want true. Certain compressed textures may invert this if their default is inverted (eg. ktx)\r\n     * @param scene needed for loading to the correct scene\r\n     * @param samplingMode mode with should be used sample / access the texture (Default: Texture.TRILINEAR_SAMPLINGMODE)\r\n     * @param onLoad optional callback to be called upon successful completion\r\n     * @param onError optional callback to be called upon failure\r\n     * @param buffer a source of a file previously fetched as either a base64 string, an ArrayBuffer (compressed or image format), HTMLImageElement (image format), or a Blob\r\n     * @param fallback an internal argument in case the function must be called again, due to etc1 not having alpha capabilities\r\n     * @param format internal format.  Default: RGB when extension is '.jpg' else RGBA.  Ignored for compressed textures\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     * @param mimeType defines an optional mime type\r\n     * @param loaderOptions options to be passed to the loader\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns a InternalTexture for assignment back into BABYLON.Texture\r\n     */\r\n    public createTexture(\r\n        url: Nullable<string>,\r\n        noMipmap: boolean,\r\n        invertY: boolean,\r\n        scene: Nullable<ISceneLike>,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<(texture: InternalTexture) => void> = null,\r\n        onError: Nullable<(message: string, exception: any) => void> = null,\r\n        buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null,\r\n        fallback: Nullable<InternalTexture> = null,\r\n        format: Nullable<number> = null,\r\n        forcedExtension: Nullable<string> = null,\r\n        mimeType?: string,\r\n        loaderOptions?: any,\r\n        creationFlags?: number,\r\n        useSRGBBuffer?: boolean\r\n    ): InternalTexture {\r\n        return this._createTextureBase(\r\n            url,\r\n            noMipmap,\r\n            invertY,\r\n            scene,\r\n            samplingMode,\r\n            onLoad,\r\n            onError,\r\n            (\r\n                texture: InternalTexture,\r\n                extension: string,\r\n                scene: Nullable<ISceneLike>,\r\n                img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n                invertY: boolean,\r\n                noMipmap: boolean,\r\n                isCompressed: boolean,\r\n                processFunction: (\r\n                    width: number,\r\n                    height: number,\r\n                    img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n                    extension: string,\r\n                    texture: InternalTexture,\r\n                    continuationCallback: () => void\r\n                ) => boolean\r\n            ) => {\r\n                const imageBitmap = img as ImageBitmap | { width: number; height: number }; // we will never get an HTMLImageElement in WebGPU\r\n\r\n                texture.baseWidth = imageBitmap.width;\r\n                texture.baseHeight = imageBitmap.height;\r\n                texture.width = imageBitmap.width;\r\n                texture.height = imageBitmap.height;\r\n                texture.format = texture.format !== -1 ? texture.format : (format ?? Constants.TEXTUREFORMAT_RGBA);\r\n                texture.type = texture.type !== -1 ? texture.type : Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n                texture._creationFlags = creationFlags ?? 0;\r\n\r\n                processFunction(texture.width, texture.height, imageBitmap, extension, texture, () => {});\r\n\r\n                if (!texture._hardwareTexture?.underlyingResource) {\r\n                    // the texture could have been created before reaching this point so don't recreate it if already existing\r\n                    const gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture, imageBitmap.width, imageBitmap.height, undefined, creationFlags);\r\n\r\n                    if (WebGPUTextureHelper.IsImageBitmap(imageBitmap)) {\r\n                        this._textureHelper.updateTexture(\r\n                            imageBitmap,\r\n                            texture,\r\n                            imageBitmap.width,\r\n                            imageBitmap.height,\r\n                            texture.depth,\r\n                            gpuTextureWrapper.format,\r\n                            0,\r\n                            0,\r\n                            invertY,\r\n                            false,\r\n                            0,\r\n                            0\r\n                        );\r\n                        if (!noMipmap && !isCompressed) {\r\n                            this._generateMipmaps(texture, this._uploadEncoder);\r\n                        }\r\n                    }\r\n                } else if (!noMipmap && !isCompressed) {\r\n                    this._generateMipmaps(texture, this._uploadEncoder);\r\n                }\r\n\r\n                if (scene) {\r\n                    scene.removePendingData(texture);\r\n                }\r\n\r\n                texture.isReady = true;\r\n\r\n                texture.onLoadedObservable.notifyObservers(texture);\r\n                texture.onLoadedObservable.clear();\r\n            },\r\n            () => false,\r\n            buffer,\r\n            fallback,\r\n            format,\r\n            forcedExtension,\r\n            mimeType,\r\n            loaderOptions,\r\n            useSRGBBuffer\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Wraps an external web gpu texture in a Babylon texture.\r\n     * @param texture defines the external texture\r\n     * @returns the babylon internal texture\r\n     */\r\n    public wrapWebGPUTexture(texture: GPUTexture): InternalTexture {\r\n        const hardwareTexture = new WebGPUHardwareTexture(this, texture);\r\n        const internalTexture = new InternalTexture(this, InternalTextureSource.Unknown, true);\r\n        internalTexture._hardwareTexture = hardwareTexture;\r\n        internalTexture.isReady = true;\r\n        return internalTexture;\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Wraps an external web gl texture in a Babylon texture.\r\n     * @returns the babylon internal texture\r\n     */\r\n    public wrapWebGLTexture(): InternalTexture {\r\n        throw new Error(\"wrapWebGLTexture is not supported, use wrapWebGPUTexture instead.\");\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getUseSRGBBuffer(useSRGBBuffer: boolean, _noMipmap: boolean): boolean {\r\n        return useSRGBBuffer && this._caps.supportSRGBBuffers;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _unpackFlipY(_value: boolean) {}\r\n\r\n    /**\r\n     * Update the sampling mode of a given texture\r\n     * @param samplingMode defines the required sampling mode\r\n     * @param texture defines the texture to update\r\n     * @param generateMipMaps defines whether to generate mipmaps for the texture\r\n     */\r\n    public updateTextureSamplingMode(samplingMode: number, texture: InternalTexture, generateMipMaps: boolean = false): void {\r\n        if (generateMipMaps) {\r\n            texture.generateMipMaps = true;\r\n            this._generateMipmaps(texture);\r\n        }\r\n\r\n        texture.samplingMode = samplingMode;\r\n    }\r\n\r\n    /**\r\n     * Update the sampling mode of a given texture\r\n     * @param texture defines the texture to update\r\n     * @param wrapU defines the texture wrap mode of the u coordinates\r\n     * @param wrapV defines the texture wrap mode of the v coordinates\r\n     * @param wrapR defines the texture wrap mode of the r coordinates\r\n     */\r\n    public updateTextureWrappingMode(texture: InternalTexture, wrapU: Nullable<number>, wrapV: Nullable<number> = null, wrapR: Nullable<number> = null): void {\r\n        if (wrapU !== null) {\r\n            texture._cachedWrapU = wrapU;\r\n        }\r\n        if (wrapV !== null) {\r\n            texture._cachedWrapV = wrapV;\r\n        }\r\n        if ((texture.is2DArray || texture.is3D) && wrapR !== null) {\r\n            texture._cachedWrapR = wrapR;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the dimensions of a texture\r\n     * @param texture texture to update\r\n     * @param width new width of the texture\r\n     * @param height new height of the texture\r\n     * @param depth new depth of the texture\r\n     */\r\n    public updateTextureDimensions(texture: InternalTexture, width: number, height: number, depth: number = 1): void {\r\n        if (!texture._hardwareTexture) {\r\n            // the gpu texture is not created yet, so when it is it will be created with the right dimensions\r\n            return;\r\n        }\r\n\r\n        if (texture.width === width && texture.height === height && texture.depth === depth) {\r\n            return;\r\n        }\r\n\r\n        const additionalUsages = (texture._hardwareTexture as WebGPUHardwareTexture).textureAdditionalUsages;\r\n\r\n        texture._hardwareTexture.release(); // don't defer the releasing! Else we will release at the end of this frame the gpu texture we are about to create in the next line...\r\n\r\n        this._textureHelper.createGPUTextureForInternalTexture(texture, width, height, depth, additionalUsages);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setInternalTexture(name: string, texture: Nullable<InternalTexture | ExternalTexture>, baseName?: string): void {\r\n        baseName = baseName ?? name;\r\n        if (this._currentEffect) {\r\n            const webgpuPipelineContext = this._currentEffect._pipelineContext as WebGPUPipelineContext;\r\n            const availableTexture = webgpuPipelineContext.shaderProcessingContext.availableTextures[baseName];\r\n\r\n            this._currentMaterialContext.setTexture(name, texture);\r\n\r\n            if (availableTexture && availableTexture.autoBindSampler) {\r\n                const samplerName = baseName + Constants.AUTOSAMPLERSUFFIX;\r\n                this._currentMaterialContext.setSampler(samplerName, texture as InternalTexture); // we can safely cast to InternalTexture because ExternalTexture always has autoBindSampler = false\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create a cube texture from prefiltered data (ie. the mipmaps contain ready to use data for PBR reflection)\r\n     * @param rootUrl defines the url where the file to load is located\r\n     * @param scene defines the current scene\r\n     * @param lodScale defines scale to apply to the mip map selection\r\n     * @param lodOffset defines offset to apply to the mip map selection\r\n     * @param onLoad defines an optional callback raised when the texture is loaded\r\n     * @param onError defines an optional callback raised if there is an issue to load the texture\r\n     * @param format defines the format of the data\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     * @param createPolynomials defines wheter or not to create polynomails harmonics for the texture\r\n     * @returns the cube texture as an InternalTexture\r\n     */\r\n    public override createPrefilteredCubeTexture(\r\n        rootUrl: string,\r\n        scene: Nullable<Scene>,\r\n        lodScale: number,\r\n        lodOffset: number,\r\n        onLoad: Nullable<(internalTexture: Nullable<InternalTexture>) => void> = null,\r\n        onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n        format?: number,\r\n        forcedExtension: any = null,\r\n        createPolynomials: boolean = true\r\n    ): InternalTexture {\r\n        const callback = (loadData: any) => {\r\n            if (!loadData) {\r\n                if (onLoad) {\r\n                    onLoad(null);\r\n                }\r\n                return;\r\n            }\r\n\r\n            const texture = loadData.texture as InternalTexture;\r\n            if (!createPolynomials) {\r\n                texture._sphericalPolynomial = new SphericalPolynomial();\r\n            } else if (loadData.info.sphericalPolynomial) {\r\n                texture._sphericalPolynomial = loadData.info.sphericalPolynomial;\r\n            }\r\n            texture._source = InternalTextureSource.CubePrefiltered;\r\n\r\n            if (onLoad) {\r\n                onLoad(texture);\r\n            }\r\n        };\r\n\r\n        return this.createCubeTexture(rootUrl, scene, null, false, callback, onError, format, forcedExtension, createPolynomials, lodScale, lodOffset);\r\n    }\r\n\r\n    /**\r\n     * Sets a texture to the according uniform.\r\n     * @param channel The texture channel\r\n     * @param unused unused parameter\r\n     * @param texture The texture to apply\r\n     * @param name The name of the uniform in the effect\r\n     */\r\n    public setTexture(channel: number, unused: Nullable<WebGLUniformLocation>, texture: Nullable<BaseTexture>, name: string): void {\r\n        this._setTexture(channel, texture, false, false, name, name);\r\n    }\r\n\r\n    /**\r\n     * Sets an array of texture to the WebGPU context\r\n     * @param channel defines the channel where the texture array must be set\r\n     * @param unused unused parameter\r\n     * @param textures defines the array of textures to bind\r\n     * @param name name of the channel\r\n     */\r\n    public setTextureArray(channel: number, unused: Nullable<WebGLUniformLocation>, textures: BaseTexture[], name: string): void {\r\n        for (let index = 0; index < textures.length; index++) {\r\n            this._setTexture(-1, textures[index], true, false, name + index.toString(), name);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override _setTexture(\r\n        channel: number,\r\n        texture: Nullable<BaseTexture>,\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        isPartOfTextureArray = false,\r\n        depthStencilTexture = false,\r\n        name = \"\",\r\n        baseName?: string\r\n    ): boolean {\r\n        // name == baseName for a texture that is not part of a texture array\r\n        // Else, name is something like 'myTexture0' / 'myTexture1' / ... and baseName is 'myTexture'\r\n        // baseName is used to look up the texture in the shaderProcessingContext.availableTextures map\r\n        // name is used to look up the texture in the _currentMaterialContext.textures map\r\n        baseName = baseName ?? name;\r\n        if (this._currentEffect) {\r\n            if (!texture) {\r\n                this._currentMaterialContext.setTexture(name, null);\r\n                return false;\r\n            }\r\n\r\n            // Video\r\n            if ((<VideoTexture>texture).video) {\r\n                (<VideoTexture>texture).update();\r\n            } else if (texture.delayLoadState === Constants.DELAYLOADSTATE_NOTLOADED) {\r\n                // Delay loading\r\n                texture.delayLoad();\r\n                return false;\r\n            }\r\n\r\n            let internalTexture: Nullable<InternalTexture> = null;\r\n            if (depthStencilTexture) {\r\n                internalTexture = (<RenderTargetTexture>texture).depthStencilTexture!;\r\n            } else if (texture.isReady()) {\r\n                internalTexture = <InternalTexture>texture.getInternalTexture();\r\n            } else if (texture.isCube) {\r\n                internalTexture = this.emptyCubeTexture;\r\n            } else if (texture.is3D) {\r\n                internalTexture = this.emptyTexture3D;\r\n            } else if (texture.is2DArray) {\r\n                internalTexture = this.emptyTexture2DArray;\r\n            } else {\r\n                internalTexture = this.emptyTexture;\r\n            }\r\n\r\n            if (internalTexture && !internalTexture.isMultiview) {\r\n                // CUBIC_MODE and SKYBOX_MODE both require CLAMP_TO_EDGE.  All other modes use REPEAT.\r\n                if (internalTexture.isCube && internalTexture._cachedCoordinatesMode !== texture.coordinatesMode) {\r\n                    internalTexture._cachedCoordinatesMode = texture.coordinatesMode;\r\n\r\n                    const textureWrapMode =\r\n                        texture.coordinatesMode !== Constants.TEXTURE_CUBIC_MODE && texture.coordinatesMode !== Constants.TEXTURE_SKYBOX_MODE\r\n                            ? Constants.TEXTURE_WRAP_ADDRESSMODE\r\n                            : Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n                    texture.wrapU = textureWrapMode;\r\n                    texture.wrapV = textureWrapMode;\r\n                }\r\n\r\n                internalTexture._cachedWrapU = texture.wrapU;\r\n                internalTexture._cachedWrapV = texture.wrapV;\r\n                if (internalTexture.is3D) {\r\n                    internalTexture._cachedWrapR = texture.wrapR;\r\n                }\r\n\r\n                this._setAnisotropicLevel(0, internalTexture, texture.anisotropicFilteringLevel);\r\n            }\r\n\r\n            this._setInternalTexture(name, internalTexture, baseName);\r\n        } else {\r\n            if (this.dbgVerboseLogsForFirstFrames) {\r\n                if ((this as any)._count === undefined) {\r\n                    (this as any)._count = 0;\r\n                }\r\n                if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                    Logger.Log([\"frame #\" + (this as any)._count + \" - _setTexture called with a null _currentEffect! texture=\", texture]);\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setAnisotropicLevel(target: number, internalTexture: InternalTexture, anisotropicFilteringLevel: number) {\r\n        if (internalTexture._cachedAnisotropicFilteringLevel !== anisotropicFilteringLevel) {\r\n            internalTexture._cachedAnisotropicFilteringLevel = Math.min(anisotropicFilteringLevel, this._caps.maxAnisotropy);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTexture(channel: number, texture: Nullable<InternalTexture>, name: string): void {\r\n        if (channel === undefined) {\r\n            return;\r\n        }\r\n\r\n        this._setInternalTexture(name, texture);\r\n    }\r\n\r\n    /**\r\n     * Generates the mipmaps for a texture\r\n     * @param texture texture to generate the mipmaps for\r\n     */\r\n    public generateMipmaps(texture: InternalTexture): void {\r\n        this._generateMipmaps(texture);\r\n    }\r\n\r\n    /**\r\n     * Update a portion of an internal texture\r\n     * @param texture defines the texture to update\r\n     * @param imageData defines the data to store into the texture\r\n     * @param xOffset defines the x coordinates of the update rectangle\r\n     * @param yOffset defines the y coordinates of the update rectangle\r\n     * @param width defines the width of the update rectangle\r\n     * @param height defines the height of the update rectangle\r\n     * @param faceIndex defines the face index if texture is a cube (0 by default)\r\n     * @param lod defines the lod level to update (0 by default)\r\n     * @param generateMipMaps defines whether to generate mipmaps or not\r\n     */\r\n    public updateTextureData(\r\n        texture: InternalTexture,\r\n        imageData: ArrayBufferView,\r\n        xOffset: number,\r\n        yOffset: number,\r\n        width: number,\r\n        height: number,\r\n        faceIndex: number = 0,\r\n        lod: number = 0,\r\n        generateMipMaps = false\r\n    ): void {\r\n        let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n        if (!texture._hardwareTexture?.underlyingResource) {\r\n            gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture);\r\n        }\r\n\r\n        const data = new Uint8Array(imageData.buffer, imageData.byteOffset, imageData.byteLength);\r\n\r\n        this._textureHelper.updateTexture(data, texture, width, height, texture.depth, gpuTextureWrapper.format, faceIndex, lod, texture.invertY, false, xOffset, yOffset);\r\n\r\n        if (generateMipMaps) {\r\n            this._generateMipmaps(texture);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadCompressedDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        internalFormat: number,\r\n        width: number,\r\n        height: number,\r\n        imageData: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0\r\n    ) {\r\n        let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n        if (!texture._hardwareTexture?.underlyingResource) {\r\n            texture.format = internalFormat;\r\n            gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture, width, height);\r\n        }\r\n\r\n        const data = new Uint8Array(imageData.buffer, imageData.byteOffset, imageData.byteLength);\r\n\r\n        this._textureHelper.updateTexture(data, texture, width, height, texture.depth, gpuTextureWrapper.format, faceIndex, lod, false, false, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        imageData: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0,\r\n        babylonInternalFormat?: number,\r\n        useTextureWidthAndHeight = false\r\n    ): void {\r\n        const lodMaxWidth = Math.round(Math.log(texture.width) * Math.LOG2E);\r\n        const lodMaxHeight = Math.round(Math.log(texture.height) * Math.LOG2E);\r\n\r\n        const width = useTextureWidthAndHeight ? texture.width : Math.pow(2, Math.max(lodMaxWidth - lod, 0));\r\n        const height = useTextureWidthAndHeight ? texture.height : Math.pow(2, Math.max(lodMaxHeight - lod, 0));\r\n\r\n        let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n        if (!texture._hardwareTexture?.underlyingResource) {\r\n            gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture, width, height);\r\n        }\r\n\r\n        const data = new Uint8Array(imageData.buffer, imageData.byteOffset, imageData.byteLength);\r\n\r\n        this._textureHelper.updateTexture(data, texture, width, height, texture.depth, gpuTextureWrapper.format, faceIndex, lod, texture.invertY, false, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadArrayBufferViewToTexture(texture: InternalTexture, imageData: ArrayBufferView, faceIndex: number = 0, lod: number = 0): void {\r\n        this._uploadDataToTextureDirectly(texture, imageData, faceIndex, lod);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadImageToTexture(texture: InternalTexture, image: HTMLImageElement | ImageBitmap, faceIndex: number = 0, lod: number = 0) {\r\n        let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n        if (!texture._hardwareTexture?.underlyingResource) {\r\n            gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture);\r\n        }\r\n\r\n        if (image instanceof HTMLImageElement) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"WebGPU engine: HTMLImageElement not supported in _uploadImageToTexture!\";\r\n        }\r\n\r\n        const bitmap = image; // in WebGPU we will always get an ImageBitmap, not an HTMLImageElement\r\n\r\n        const width = Math.ceil(texture.width / (1 << lod));\r\n        const height = Math.ceil(texture.height / (1 << lod));\r\n\r\n        this._textureHelper.updateTexture(bitmap, texture, width, height, texture.depth, gpuTextureWrapper.format, faceIndex, lod, texture.invertY, false, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * Reads pixels from the current frame buffer. Please note that this function can be slow\r\n     * @param x defines the x coordinate of the rectangle where pixels must be read\r\n     * @param y defines the y coordinate of the rectangle where pixels must be read\r\n     * @param width defines the width of the rectangle where pixels must be read\r\n     * @param height defines the height of the rectangle where pixels must be read\r\n     * @param _hasAlpha defines whether the output should have alpha or not (defaults to true)\r\n     * @param flushRenderer true to flush the renderer from the pending commands before reading the pixels\r\n     * @param data defines the data to fill with the read pixels (if not provided, a new one will be created)\r\n     * @returns a ArrayBufferView promise (Uint8Array) containing RGBA colors\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/promise-function-async\r\n    public readPixels(x: number, y: number, width: number, height: number, _hasAlpha = true, flushRenderer = true, data: Nullable<Uint8Array> = null): Promise<ArrayBufferView> {\r\n        const renderPassWrapper = this._getCurrentRenderPassWrapper();\r\n        const hardwareTexture = renderPassWrapper.colorAttachmentGPUTextures[0];\r\n        if (!hardwareTexture) {\r\n            // we are calling readPixels for a render pass with no color texture bound\r\n            return Promise.resolve(new Uint8Array(0));\r\n        }\r\n        const gpuTexture = hardwareTexture.underlyingResource;\r\n        const gpuTextureFormat = hardwareTexture.format;\r\n        if (!gpuTexture) {\r\n            // we are calling readPixels before startMainRenderPass has been called and no RTT is bound, so swapChainTexture is not setup yet!\r\n            return Promise.resolve(new Uint8Array(0));\r\n        }\r\n        if (flushRenderer) {\r\n            this.flushFramebuffer();\r\n        }\r\n        return this._textureHelper.readPixels(gpuTexture, x, y, width, height, gpuTextureFormat, undefined, undefined, data);\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Frame management\r\n    //------------------------------------------------------------------------------\r\n\r\n    private _measureFps(): void {\r\n        this._performanceMonitor.sampleFrame();\r\n        this._fps = this._performanceMonitor.averageFPS;\r\n        this._deltaTime = this._performanceMonitor.instantaneousFrameTime || 0;\r\n    }\r\n\r\n    private _performanceMonitor = new PerformanceMonitor();\r\n    /**\r\n     * Gets the performance monitor attached to this engine\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#engineinstrumentation\r\n     */\r\n    public get performanceMonitor(): PerformanceMonitor {\r\n        return this._performanceMonitor;\r\n    }\r\n\r\n    /**\r\n     * Begin a new frame\r\n     */\r\n    public override beginFrame(): void {\r\n        this._measureFps();\r\n        super.beginFrame();\r\n    }\r\n\r\n    /**\r\n     * End the current frame\r\n     */\r\n    public override endFrame() {\r\n        this._endCurrentRenderPass();\r\n\r\n        this._snapshotRendering.endFrame();\r\n\r\n        this._timestampQuery.endFrame(this._renderEncoder);\r\n        this._timestampIndex = 0;\r\n\r\n        this.flushFramebuffer();\r\n\r\n        this._textureHelper.destroyDeferredTextures();\r\n        this._bufferManager.destroyDeferredBuffers();\r\n\r\n        if (this._features._collectUbosUpdatedInFrame) {\r\n            if (this.dbgVerboseLogsForFirstFrames) {\r\n                if ((this as any)._count === undefined) {\r\n                    (this as any)._count = 0;\r\n                }\r\n                if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                    const list: Array<string> = [];\r\n                    for (const name in UniformBuffer._UpdatedUbosInFrame) {\r\n                        list.push(name + \":\" + UniformBuffer._UpdatedUbosInFrame[name]);\r\n                    }\r\n                    Logger.Log([\"frame #\" + (this as any)._count + \" - updated ubos -\", list.join(\", \")]);\r\n                }\r\n            }\r\n            UniformBuffer._UpdatedUbosInFrame = {};\r\n        }\r\n\r\n        this.countersLastFrame.numEnableEffects = this._counters.numEnableEffects;\r\n        this.countersLastFrame.numEnableDrawWrapper = this._counters.numEnableDrawWrapper;\r\n        this.countersLastFrame.numBundleCreationNonCompatMode = this._counters.numBundleCreationNonCompatMode;\r\n        this.countersLastFrame.numBundleReuseNonCompatMode = this._counters.numBundleReuseNonCompatMode;\r\n        this._counters.numEnableEffects = 0;\r\n        this._counters.numEnableDrawWrapper = 0;\r\n        this._counters.numBundleCreationNonCompatMode = 0;\r\n        this._counters.numBundleReuseNonCompatMode = 0;\r\n\r\n        this._cacheRenderPipeline.endFrame();\r\n        this._cacheBindGroups.endFrame();\r\n\r\n        this._pendingDebugCommands.length = 0;\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if ((this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\"%c frame #\" + (this as any)._count + \" - end\", \"background: #ffff00\"]);\r\n            }\r\n            if ((this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                (this as any)._count++;\r\n                if ((this as any)._count !== this.dbgVerboseLogsNumFrames) {\r\n                    Logger.Log([\"%c frame #\" + (this as any)._count + \" - begin\", \"background: #ffff00\"]);\r\n                }\r\n            }\r\n        }\r\n\r\n        super.endFrame();\r\n    }\r\n\r\n    /**Gets driver info if available */\r\n    public extractDriverInfo() {\r\n        return \"\";\r\n    }\r\n\r\n    /**\r\n     * Force a WebGPU flush (ie. a flush of all waiting commands)\r\n     */\r\n    public flushFramebuffer(): void {\r\n        // we need to end the current render pass (main or rtt) if any as we are not allowed to submit the command buffers when being in a pass\r\n        this._endCurrentRenderPass();\r\n\r\n        this._commandBuffers[0] = this._uploadEncoder.finish();\r\n        this._commandBuffers[1] = this._renderEncoder.finish();\r\n\r\n        this._device.queue.submit(this._commandBuffers);\r\n\r\n        this._uploadEncoder = this._device.createCommandEncoder(this._uploadEncoderDescriptor);\r\n        this._renderEncoder = this._device.createCommandEncoder(this._renderEncoderDescriptor);\r\n\r\n        this._timestampQuery.startFrame(this._uploadEncoder);\r\n\r\n        this._textureHelper.setCommandEncoder(this._uploadEncoder);\r\n\r\n        this._bundleList.reset();\r\n    }\r\n\r\n    /** @internal */\r\n    public _currentFrameBufferIsDefaultFrameBuffer() {\r\n        return this._currentPassIsMainPass();\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Render Pass\r\n    //------------------------------------------------------------------------------\r\n\r\n    private _startRenderTargetRenderPass(\r\n        renderTargetWrapper: RenderTargetWrapper,\r\n        setClearStates: boolean,\r\n        clearColor: Nullable<IColor4Like>,\r\n        clearDepth: boolean,\r\n        clearStencil: boolean\r\n    ) {\r\n        this._endCurrentRenderPass();\r\n\r\n        const rtWrapper = renderTargetWrapper as WebGPURenderTargetWrapper;\r\n\r\n        const depthStencilTexture = rtWrapper._depthStencilTexture;\r\n        const gpuDepthStencilWrapper = depthStencilTexture?._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n        const gpuDepthStencilTexture = gpuDepthStencilWrapper?.underlyingResource as Nullable<GPUTexture>;\r\n        const gpuDepthStencilMSAATexture = gpuDepthStencilWrapper?.getMSAATexture(0);\r\n\r\n        const depthTextureView = gpuDepthStencilTexture?.createView(this._rttRenderPassWrapper.depthAttachmentViewDescriptor!);\r\n        const depthMSAATextureView = gpuDepthStencilMSAATexture?.createView(this._rttRenderPassWrapper.depthAttachmentViewDescriptor!);\r\n        const depthTextureHasStencil = gpuDepthStencilWrapper ? WebGPUTextureHelper.HasStencilAspect(gpuDepthStencilWrapper.format) : false;\r\n\r\n        const colorAttachments: (GPURenderPassColorAttachment | null)[] = [];\r\n\r\n        if (this.useReverseDepthBuffer) {\r\n            this.setDepthFunctionToGreaterOrEqual();\r\n        }\r\n\r\n        const clearColorForIntegerRt = TempColor4;\r\n        if (clearColor) {\r\n            clearColorForIntegerRt.r = clearColor.r * 255;\r\n            clearColorForIntegerRt.g = clearColor.g * 255;\r\n            clearColorForIntegerRt.b = clearColor.b * 255;\r\n            clearColorForIntegerRt.a = clearColor.a * 255;\r\n        }\r\n\r\n        const mustClearColor = setClearStates && clearColor;\r\n        const mustClearDepth = setClearStates && clearDepth;\r\n        const mustClearStencil = setClearStates && clearStencil;\r\n\r\n        if (rtWrapper._attachments && rtWrapper.isMulti) {\r\n            // multi render targets\r\n            if (!this._mrtAttachments || this._mrtAttachments.length === 0) {\r\n                this._mrtAttachments = rtWrapper._defaultAttachments;\r\n            }\r\n            for (let i = 0; i < this._mrtAttachments.length; ++i) {\r\n                const index = this._mrtAttachments[i]; // if index == 0 it means the texture should not be written to => at render pass creation time, it means we should not clear it\r\n                const mrtTexture = rtWrapper.textures![i];\r\n                const gpuMRTWrapper = mrtTexture?._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n                const gpuMRTTexture = gpuMRTWrapper?.underlyingResource;\r\n                if (gpuMRTWrapper && gpuMRTTexture) {\r\n                    const baseArrayLayer = rtWrapper.getBaseArrayLayer(i);\r\n                    const gpuMSAATexture = gpuMRTWrapper.getMSAATexture(baseArrayLayer);\r\n\r\n                    const viewDescriptor = {\r\n                        ...this._rttRenderPassWrapper.colorAttachmentViewDescriptor!,\r\n                        dimension: mrtTexture.is3D ? WebGPUConstants.TextureViewDimension.E3d : WebGPUConstants.TextureViewDimension.E2d,\r\n                        format: gpuMRTWrapper.format,\r\n                        baseArrayLayer,\r\n                    };\r\n                    const msaaViewDescriptor = {\r\n                        ...this._rttRenderPassWrapper.colorAttachmentViewDescriptor!,\r\n                        dimension: mrtTexture.is3D ? WebGPUConstants.TextureViewDimension.E3d : WebGPUConstants.TextureViewDimension.E2d,\r\n                        format: gpuMRTWrapper.format,\r\n                        baseArrayLayer: 0,\r\n                    };\r\n                    const isRtInteger = mrtTexture.type === Constants.TEXTURETYPE_UNSIGNED_INTEGER || mrtTexture.type === Constants.TEXTURETYPE_UNSIGNED_SHORT;\r\n\r\n                    const colorTextureView = gpuMRTTexture.createView(viewDescriptor);\r\n                    const colorMSAATextureView = gpuMSAATexture?.createView(msaaViewDescriptor);\r\n\r\n                    colorAttachments.push({\r\n                        view: colorMSAATextureView ? colorMSAATextureView : colorTextureView,\r\n                        resolveTarget: gpuMSAATexture ? colorTextureView : undefined,\r\n                        depthSlice: mrtTexture.is3D ? (rtWrapper.layerIndices?.[i] ?? 0) : undefined,\r\n                        clearValue: index !== 0 && mustClearColor ? (isRtInteger ? clearColorForIntegerRt : clearColor) : undefined,\r\n                        loadOp: index !== 0 && mustClearColor ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load,\r\n                        storeOp: WebGPUConstants.StoreOp.Store,\r\n                    });\r\n                }\r\n            }\r\n            this._cacheRenderPipeline.setMRT(rtWrapper.textures!, this._mrtAttachments.length);\r\n            this._cacheRenderPipeline.setMRTAttachments(this._mrtAttachments);\r\n        } else {\r\n            // single render target\r\n            const internalTexture = rtWrapper.texture;\r\n            if (internalTexture) {\r\n                const gpuWrapper = internalTexture._hardwareTexture as WebGPUHardwareTexture;\r\n                const gpuTexture = gpuWrapper.underlyingResource!;\r\n\r\n                let depthSlice: number | undefined = undefined;\r\n\r\n                if (rtWrapper.is3D) {\r\n                    depthSlice = this._rttRenderPassWrapper.colorAttachmentViewDescriptor!.baseArrayLayer;\r\n                    this._rttRenderPassWrapper.colorAttachmentViewDescriptor!.baseArrayLayer = 0;\r\n                }\r\n\r\n                const gpuMSAATexture = gpuWrapper.getMSAATexture(0);\r\n                const colorTextureView = gpuTexture.createView(this._rttRenderPassWrapper.colorAttachmentViewDescriptor!);\r\n                const colorMSAATextureView = gpuMSAATexture?.createView(this._rttRenderPassWrapper.colorAttachmentViewDescriptor!);\r\n                const isRtInteger = internalTexture.type === Constants.TEXTURETYPE_UNSIGNED_INTEGER || internalTexture.type === Constants.TEXTURETYPE_UNSIGNED_SHORT;\r\n\r\n                colorAttachments.push({\r\n                    view: colorMSAATextureView ? colorMSAATextureView : colorTextureView,\r\n                    resolveTarget: gpuMSAATexture ? colorTextureView : undefined,\r\n                    depthSlice,\r\n                    clearValue: mustClearColor ? (isRtInteger ? clearColorForIntegerRt : clearColor) : undefined,\r\n                    loadOp: mustClearColor ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load,\r\n                    storeOp: WebGPUConstants.StoreOp.Store,\r\n                });\r\n            } else {\r\n                colorAttachments.push(null);\r\n            }\r\n        }\r\n\r\n        this._debugPushGroup?.(\"render target pass\" + (renderTargetWrapper.label ? \" (\" + renderTargetWrapper.label + \")\" : \"\"), 0);\r\n\r\n        this._rttRenderPassWrapper.renderPassDescriptor = {\r\n            label: (renderTargetWrapper.label ?? \"RTT\") + \" - RenderPass\",\r\n            colorAttachments,\r\n            depthStencilAttachment:\r\n                depthStencilTexture && gpuDepthStencilTexture\r\n                    ? {\r\n                          view: depthMSAATextureView ? depthMSAATextureView : depthTextureView!,\r\n                          depthClearValue: mustClearDepth ? (this.useReverseDepthBuffer ? this._clearReverseDepthValue : this._clearDepthValue) : undefined,\r\n                          depthLoadOp: mustClearDepth ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load,\r\n                          depthStoreOp: WebGPUConstants.StoreOp.Store,\r\n                          stencilClearValue: rtWrapper._depthStencilTextureWithStencil && mustClearStencil ? this._clearStencilValue : undefined,\r\n                          stencilLoadOp: !depthTextureHasStencil\r\n                              ? undefined\r\n                              : rtWrapper._depthStencilTextureWithStencil && mustClearStencil\r\n                                ? WebGPUConstants.LoadOp.Clear\r\n                                : WebGPUConstants.LoadOp.Load,\r\n                          stencilStoreOp: !depthTextureHasStencil ? undefined : WebGPUConstants.StoreOp.Store,\r\n                      }\r\n                    : undefined,\r\n            occlusionQuerySet: this._occlusionQuery?.hasQueries ? this._occlusionQuery.querySet : undefined,\r\n        };\r\n        this._timestampQuery.startPass(this._rttRenderPassWrapper.renderPassDescriptor, this._timestampIndex);\r\n        this._currentRenderPass = this._renderEncoder.beginRenderPass(this._rttRenderPassWrapper.renderPassDescriptor);\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                const internalTexture = rtWrapper.texture!;\r\n                Logger.Log([\r\n                    \"frame #\" +\r\n                        (this as any)._count +\r\n                        \" - render target begin pass - rtt name=\" +\r\n                        renderTargetWrapper.label +\r\n                        \", internalTexture.uniqueId=\" +\r\n                        internalTexture.uniqueId +\r\n                        \", width=\" +\r\n                        internalTexture.width +\r\n                        \", height=\" +\r\n                        internalTexture.height +\r\n                        \", setClearStates=\" +\r\n                        setClearStates,\r\n                    \"renderPassDescriptor=\",\r\n                    this._rttRenderPassWrapper.renderPassDescriptor,\r\n                ]);\r\n            }\r\n        }\r\n\r\n        this._debugFlushPendingCommands?.();\r\n\r\n        this._resetRenderPassStates();\r\n\r\n        if (!gpuDepthStencilWrapper || !WebGPUTextureHelper.HasStencilAspect(gpuDepthStencilWrapper.format)) {\r\n            this._stencilStateComposer.enabled = false;\r\n        }\r\n    }\r\n\r\n    private _startMainRenderPass(setClearStates: boolean, clearColor?: Nullable<IColor4Like>, clearDepth?: boolean, clearStencil?: boolean): void {\r\n        this._endCurrentRenderPass();\r\n\r\n        if (this.useReverseDepthBuffer) {\r\n            this.setDepthFunctionToGreaterOrEqual();\r\n        }\r\n\r\n        const mustClearColor = setClearStates && clearColor;\r\n        const mustClearDepth = setClearStates && clearDepth;\r\n        const mustClearStencil = setClearStates && clearStencil;\r\n\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.colorAttachments[0]!.clearValue = mustClearColor ? clearColor : undefined;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.colorAttachments[0]!.loadOp = mustClearColor ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.depthStencilAttachment!.depthClearValue = mustClearDepth\r\n            ? this.useReverseDepthBuffer\r\n                ? this._clearReverseDepthValue\r\n                : this._clearDepthValue\r\n            : undefined;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.depthStencilAttachment!.depthLoadOp = mustClearDepth ? WebGPUConstants.LoadOp.Clear : WebGPUConstants.LoadOp.Load;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.depthStencilAttachment!.stencilClearValue = mustClearStencil ? this._clearStencilValue : undefined;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.depthStencilAttachment!.stencilLoadOp = !this.isStencilEnable\r\n            ? undefined\r\n            : mustClearStencil\r\n              ? WebGPUConstants.LoadOp.Clear\r\n              : WebGPUConstants.LoadOp.Load;\r\n        this._mainRenderPassWrapper.renderPassDescriptor!.occlusionQuerySet = this._occlusionQuery?.hasQueries ? this._occlusionQuery.querySet : undefined;\r\n\r\n        const swapChainTexture = this._context.getCurrentTexture();\r\n        this._mainRenderPassWrapper.colorAttachmentGPUTextures[0]!.set(swapChainTexture);\r\n\r\n        // Resolve in case of MSAA\r\n        if (this._options.antialias) {\r\n            ViewDescriptorSwapChainAntialiasing.format = swapChainTexture.format;\r\n            this._mainRenderPassWrapper.renderPassDescriptor!.colorAttachments[0]!.resolveTarget = swapChainTexture.createView(ViewDescriptorSwapChainAntialiasing);\r\n        } else {\r\n            ViewDescriptorSwapChain.format = swapChainTexture.format;\r\n            this._mainRenderPassWrapper.renderPassDescriptor!.colorAttachments[0]!.view = swapChainTexture.createView(ViewDescriptorSwapChain);\r\n        }\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\r\n                    \"frame #\" + (this as any)._count + \" - main begin pass - texture width=\" + (this._mainTextureExtends as any).width,\r\n                    \" height=\" + (this._mainTextureExtends as any).height + \", setClearStates=\" + setClearStates,\r\n                    \"renderPassDescriptor=\",\r\n                    this._mainRenderPassWrapper.renderPassDescriptor,\r\n                ]);\r\n            }\r\n        }\r\n\r\n        this._debugPushGroup?.(\"main pass\", 0);\r\n\r\n        this._timestampQuery.startPass(this._mainRenderPassWrapper.renderPassDescriptor!, this._timestampIndex);\r\n        this._currentRenderPass = this._renderEncoder.beginRenderPass(this._mainRenderPassWrapper.renderPassDescriptor!);\r\n\r\n        this._setDepthTextureFormat(this._mainRenderPassWrapper);\r\n        this._setColorFormat(this._mainRenderPassWrapper);\r\n\r\n        this._debugFlushPendingCommands?.();\r\n\r\n        this._resetRenderPassStates();\r\n\r\n        if (!this._isStencilEnable) {\r\n            this._stencilStateComposer.enabled = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the frame buffer to the specified texture.\r\n     * @param texture The render target wrapper to render to\r\n     * @param faceIndex The face of the texture to render to in case of cube texture\r\n     * @param requiredWidth The width of the target to render to\r\n     * @param requiredHeight The height of the target to render to\r\n     * @param forceFullscreenViewport Forces the viewport to be the entire texture/screen if true\r\n     * @param lodLevel defines the lod level to bind to the frame buffer\r\n     * @param layer defines the 2d array index to bind to frame buffer to\r\n     */\r\n    public bindFramebuffer(\r\n        texture: RenderTargetWrapper,\r\n        faceIndex: number = 0,\r\n        requiredWidth?: number,\r\n        requiredHeight?: number,\r\n        forceFullscreenViewport?: boolean,\r\n        lodLevel = 0,\r\n        layer = 0\r\n    ): void {\r\n        const hardwareTexture = texture.texture?._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n\r\n        if (this._currentRenderTarget) {\r\n            this.unBindFramebuffer(this._currentRenderTarget);\r\n        } else {\r\n            this._endCurrentRenderPass();\r\n        }\r\n        this._currentRenderTarget = texture;\r\n\r\n        const depthStencilTexture = this._currentRenderTarget._depthStencilTexture;\r\n\r\n        this._rttRenderPassWrapper.colorAttachmentGPUTextures[0] = hardwareTexture;\r\n        this._rttRenderPassWrapper.depthTextureFormat = depthStencilTexture ? WebGPUTextureHelper.GetWebGPUTextureFormat(-1, depthStencilTexture.format) : undefined;\r\n\r\n        this._setDepthTextureFormat(this._rttRenderPassWrapper);\r\n        this._setColorFormat(this._rttRenderPassWrapper);\r\n\r\n        this._rttRenderPassWrapper.colorAttachmentViewDescriptor = {\r\n            format: this._colorFormat as GPUTextureFormat,\r\n            dimension: texture.is3D ? WebGPUConstants.TextureViewDimension.E3d : WebGPUConstants.TextureViewDimension.E2d,\r\n            mipLevelCount: 1,\r\n            baseArrayLayer: texture.isCube ? layer * 6 + faceIndex : layer,\r\n            baseMipLevel: lodLevel,\r\n            arrayLayerCount: 1,\r\n            aspect: WebGPUConstants.TextureAspect.All,\r\n        };\r\n\r\n        this._rttRenderPassWrapper.depthAttachmentViewDescriptor = {\r\n            format: this._depthTextureFormat!,\r\n            dimension: depthStencilTexture && depthStencilTexture.is3D ? WebGPUConstants.TextureViewDimension.E3d : WebGPUConstants.TextureViewDimension.E2d,\r\n            mipLevelCount: 1,\r\n            baseArrayLayer: depthStencilTexture ? (depthStencilTexture.isCube ? layer * 6 + faceIndex : layer) : 0,\r\n            baseMipLevel: 0,\r\n            arrayLayerCount: 1,\r\n            aspect: WebGPUConstants.TextureAspect.All,\r\n        };\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log([\r\n                    \"frame #\" +\r\n                        (this as any)._count +\r\n                        \" - bindFramebuffer - rtt name=\" +\r\n                        texture.label +\r\n                        \", internalTexture.uniqueId=\" +\r\n                        texture.texture?.uniqueId +\r\n                        \", face=\" +\r\n                        faceIndex +\r\n                        \", lodLevel=\" +\r\n                        lodLevel +\r\n                        \", layer=\" +\r\n                        layer,\r\n                    \"colorAttachmentViewDescriptor=\",\r\n                    this._rttRenderPassWrapper.colorAttachmentViewDescriptor,\r\n                    \"depthAttachmentViewDescriptor=\",\r\n                    this._rttRenderPassWrapper.depthAttachmentViewDescriptor,\r\n                ]);\r\n            }\r\n        }\r\n\r\n        // We don't create the render pass just now, we do a lazy creation of the render pass, hoping the render pass will be created by a call to clear()...\r\n\r\n        if (this._cachedViewport && !forceFullscreenViewport) {\r\n            this.setViewport(this._cachedViewport, requiredWidth, requiredHeight);\r\n        } else {\r\n            if (!requiredWidth) {\r\n                requiredWidth = texture.width;\r\n                if (lodLevel) {\r\n                    requiredWidth = requiredWidth / Math.pow(2, lodLevel);\r\n                }\r\n            }\r\n            if (!requiredHeight) {\r\n                requiredHeight = texture.height;\r\n                if (lodLevel) {\r\n                    requiredHeight = requiredHeight / Math.pow(2, lodLevel);\r\n                }\r\n            }\r\n\r\n            this._viewport(0, 0, requiredWidth, requiredHeight);\r\n        }\r\n\r\n        this.wipeCaches();\r\n    }\r\n\r\n    /**\r\n     * Unbind the current render target texture from the WebGPU context\r\n     * @param texture defines the render target wrapper to unbind\r\n     * @param disableGenerateMipMaps defines a boolean indicating that mipmaps must not be generated\r\n     * @param onBeforeUnbind defines a function which will be called before the effective unbind\r\n     */\r\n    public unBindFramebuffer(texture: RenderTargetWrapper, disableGenerateMipMaps = false, onBeforeUnbind?: () => void): void {\r\n        const saveCrt = this._currentRenderTarget;\r\n\r\n        this._currentRenderTarget = null; // to be iso with abstractEngine, this._currentRenderTarget must be null when onBeforeUnbind is called\r\n\r\n        if (onBeforeUnbind) {\r\n            onBeforeUnbind();\r\n        }\r\n\r\n        this._currentRenderTarget = saveCrt;\r\n\r\n        this._endCurrentRenderPass();\r\n\r\n        if (!disableGenerateMipMaps) {\r\n            if (texture.isMulti) {\r\n                this.generateMipMapsMultiFramebuffer(texture);\r\n            } else {\r\n                this.generateMipMapsFramebuffer(texture);\r\n            }\r\n        }\r\n\r\n        this._currentRenderTarget = null;\r\n\r\n        if (this.dbgVerboseLogsForFirstFrames) {\r\n            if ((this as any)._count === undefined) {\r\n                (this as any)._count = 0;\r\n            }\r\n            if (!(this as any)._count || (this as any)._count < this.dbgVerboseLogsNumFrames) {\r\n                Logger.Log(\"frame #\" + (this as any)._count + \" - unBindFramebuffer - rtt name=\" + texture.label + \", internalTexture.uniqueId=\", texture.texture?.uniqueId);\r\n            }\r\n        }\r\n\r\n        this._mrtAttachments = [];\r\n        this._cacheRenderPipeline.setMRT([]);\r\n        this._cacheRenderPipeline.setMRTAttachments(this._mrtAttachments);\r\n    }\r\n\r\n    /**\r\n     * Generates mipmaps for the texture of the (single) render target\r\n     * @param texture The render target containing the texture to generate the mipmaps for\r\n     */\r\n    public generateMipMapsFramebuffer(texture: RenderTargetWrapper): void {\r\n        if (!texture.isMulti && texture.texture?.generateMipMaps && !texture.isCube) {\r\n            this._generateMipmaps(texture.texture);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resolves the MSAA texture of the (single) render target into its non-MSAA version.\r\n     * Note that if \"texture\" is not a MSAA render target, no resolve is performed.\r\n     * @param _texture The render target texture containing the MSAA texture to resolve\r\n     */\r\n    public resolveFramebuffer(_texture: RenderTargetWrapper): void {\r\n        throw new Error(\"resolveFramebuffer is not yet implemented in WebGPU!\");\r\n    }\r\n\r\n    /**\r\n     * Unbind the current render target and bind the default framebuffer\r\n     */\r\n    public restoreDefaultFramebuffer(): void {\r\n        if (this._currentRenderTarget) {\r\n            this.unBindFramebuffer(this._currentRenderTarget);\r\n        } else if (!this._currentRenderPass) {\r\n            this._startMainRenderPass(false);\r\n        }\r\n\r\n        if (this._cachedViewport) {\r\n            this.setViewport(this._cachedViewport);\r\n        }\r\n\r\n        this.wipeCaches();\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Render\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setColorFormat(wrapper: IWebGPURenderPassWrapper): void {\r\n        const format = wrapper.colorAttachmentGPUTextures[0]?.format ?? null;\r\n        this._cacheRenderPipeline.setColorFormat(format);\r\n        if (this._colorFormat === format) {\r\n            return;\r\n        }\r\n        this._colorFormat = format;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setDepthTextureFormat(wrapper: IWebGPURenderPassWrapper): void {\r\n        this._cacheRenderPipeline.setDepthStencilFormat(wrapper.depthTextureFormat);\r\n        if (this._depthTextureFormat === wrapper.depthTextureFormat) {\r\n            return;\r\n        }\r\n        this._depthTextureFormat = wrapper.depthTextureFormat;\r\n    }\r\n\r\n    public setDitheringState(): void {\r\n        // Does not exist in WebGPU\r\n    }\r\n\r\n    public setRasterizerState(): void {\r\n        // Does not exist in WebGPU\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _executeWhenRenderingStateIsCompiled(pipelineContext: IPipelineContext, action: () => void) {\r\n        // No parallel shader compilation.\r\n        // No Async, so direct launch\r\n        action();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public bindSamplers(): void {}\r\n\r\n    /** @internal */\r\n    public _getUnpackAlignement(): number {\r\n        return 1;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTextureDirectly(): boolean {\r\n        return false;\r\n    }\r\n\r\n    public setStateCullFaceType(cullBackFaces?: boolean, force = false) {\r\n        const cullFace = (this.cullBackFaces ?? cullBackFaces ?? true) ? 1 : 2;\r\n        if (this._depthCullingState.cullFace !== cullFace || force) {\r\n            this._depthCullingState.cullFace = cullFace;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set various states to the webGL context\r\n     * @param culling defines culling state: true to enable culling, false to disable it\r\n     * @param zOffset defines the value to apply to zOffset (0 by default)\r\n     * @param force defines if states must be applied even if cache is up to date\r\n     * @param reverseSide defines if culling must be reversed (CCW if false, CW if true)\r\n     * @param cullBackFaces true to cull back faces, false to cull front faces (if culling is enabled)\r\n     * @param stencil stencil states to set\r\n     * @param zOffsetUnits defines the value to apply to zOffsetUnits (0 by default)\r\n     */\r\n    public setState(culling: boolean, zOffset: number = 0, force?: boolean, reverseSide = false, cullBackFaces?: boolean, stencil?: IStencilState, zOffsetUnits: number = 0): void {\r\n        // Culling\r\n        if (this._depthCullingState.cull !== culling || force) {\r\n            this._depthCullingState.cull = culling;\r\n        }\r\n\r\n        // Cull face\r\n        this.setStateCullFaceType(cullBackFaces, force);\r\n\r\n        // Z offset\r\n        this.setZOffset(zOffset);\r\n        this.setZOffsetUnits(zOffsetUnits);\r\n\r\n        // Front face\r\n        const frontFace = reverseSide ? (this._currentRenderTarget ? 1 : 2) : this._currentRenderTarget ? 2 : 1;\r\n        if (this._depthCullingState.frontFace !== frontFace || force) {\r\n            this._depthCullingState.frontFace = frontFace;\r\n        }\r\n\r\n        this._stencilStateComposer.stencilMaterial = stencil;\r\n    }\r\n\r\n    private _applyRenderPassChanges(bundleList: Nullable<WebGPUBundleList>): void {\r\n        const mustUpdateStencilRef = !this._stencilStateComposer.enabled ? false : this._mustUpdateStencilRef();\r\n        const mustUpdateBlendColor = !this._alphaState.alphaBlend ? false : this._mustUpdateBlendColor();\r\n\r\n        if (this._mustUpdateViewport()) {\r\n            this._applyViewport(bundleList);\r\n        }\r\n        if (this._mustUpdateScissor()) {\r\n            this._applyScissor(bundleList);\r\n        }\r\n        if (mustUpdateStencilRef) {\r\n            this._applyStencilRef(bundleList);\r\n        }\r\n        if (mustUpdateBlendColor) {\r\n            this._applyBlendColor(bundleList);\r\n        }\r\n    }\r\n\r\n    private _draw(drawType: number, fillMode: number, start: number, count: number, instancesCount: number): void {\r\n        const renderPass = this._getCurrentRenderPass();\r\n        const bundleList = this._bundleList;\r\n\r\n        this.applyStates();\r\n\r\n        const webgpuPipelineContext = this._currentEffect!._pipelineContext as WebGPUPipelineContext;\r\n\r\n        this.bindUniformBufferBase(this._currentRenderTarget ? this._ubInvertY : this._ubDontInvertY, 0, WebGPUShaderProcessor.InternalsUBOName);\r\n\r\n        if (webgpuPipelineContext.uniformBuffer) {\r\n            webgpuPipelineContext.uniformBuffer.update();\r\n            this.bindUniformBufferBase(webgpuPipelineContext.uniformBuffer.getBuffer()!, 0, WebGPUShaderProcessor.LeftOvertUBOName);\r\n        }\r\n\r\n        if (this._snapshotRendering.play) {\r\n            this._reportDrawCall();\r\n            return;\r\n        }\r\n\r\n        if (\r\n            !this.compatibilityMode &&\r\n            (this._currentDrawContext.isDirty(this._currentMaterialContext.updateId) || this._currentMaterialContext.isDirty || this._currentMaterialContext.forceBindGroupCreation)\r\n        ) {\r\n            this._currentDrawContext.fastBundle = undefined;\r\n        }\r\n\r\n        const useFastPath = !this.compatibilityMode && this._currentDrawContext.fastBundle;\r\n        let renderPass2: GPURenderPassEncoder | GPURenderBundleEncoder = renderPass;\r\n\r\n        if (useFastPath || this._snapshotRendering.record) {\r\n            this._applyRenderPassChanges(bundleList);\r\n            if (!this._snapshotRendering.record) {\r\n                this._counters.numBundleReuseNonCompatMode++;\r\n                if (this._currentDrawContext.indirectDrawBuffer) {\r\n                    this._currentDrawContext.setIndirectData(count, instancesCount || 1, start);\r\n                }\r\n                bundleList.addBundle(this._currentDrawContext.fastBundle);\r\n                this._reportDrawCall();\r\n                return;\r\n            }\r\n\r\n            renderPass2 = bundleList.getBundleEncoder(this._cacheRenderPipeline.colorFormats, this._depthTextureFormat, this.currentSampleCount); // for snapshot recording mode\r\n            bundleList.numDrawCalls++;\r\n        }\r\n\r\n        let textureState = 0;\r\n        if (this._currentMaterialContext.hasFloatOrDepthTextures) {\r\n            let bitVal = 1;\r\n            for (let i = 0; i < webgpuPipelineContext.shaderProcessingContext.textureNames.length; ++i) {\r\n                const textureName = webgpuPipelineContext.shaderProcessingContext.textureNames[i];\r\n                const texture = this._currentMaterialContext.textures[textureName]?.texture;\r\n                const textureIsDepth = texture && texture.format >= Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 && texture.format <= Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8;\r\n                if ((texture?.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) || textureIsDepth) {\r\n                    textureState |= bitVal;\r\n                }\r\n                bitVal = bitVal << 1;\r\n            }\r\n        }\r\n\r\n        this._currentMaterialContext.textureState = textureState;\r\n\r\n        const pipeline = this._cacheRenderPipeline.getRenderPipeline(fillMode, this._currentEffect!, this.currentSampleCount, textureState);\r\n        const bindGroups = this._cacheBindGroups.getBindGroups(webgpuPipelineContext, this._currentDrawContext, this._currentMaterialContext);\r\n\r\n        if (!this._snapshotRendering.record) {\r\n            this._applyRenderPassChanges(!this.compatibilityMode ? bundleList : null);\r\n            if (!this.compatibilityMode) {\r\n                this._counters.numBundleCreationNonCompatMode++;\r\n                renderPass2 = this._device.createRenderBundleEncoder({\r\n                    colorFormats: this._cacheRenderPipeline.colorFormats,\r\n                    depthStencilFormat: this._depthTextureFormat,\r\n                    sampleCount: WebGPUTextureHelper.GetSample(this.currentSampleCount),\r\n                });\r\n            }\r\n        }\r\n\r\n        // bind pipeline\r\n        renderPass2.setPipeline(pipeline);\r\n\r\n        // bind index/vertex buffers\r\n        if (this._currentIndexBuffer) {\r\n            renderPass2.setIndexBuffer(\r\n                this._currentIndexBuffer.underlyingResource,\r\n                this._currentIndexBuffer.is32Bits ? WebGPUConstants.IndexFormat.Uint32 : WebGPUConstants.IndexFormat.Uint16,\r\n                0\r\n            );\r\n        }\r\n\r\n        const vertexBuffers = this._cacheRenderPipeline.vertexBuffers;\r\n        for (let index = 0; index < vertexBuffers.length; index++) {\r\n            const vertexBuffer = vertexBuffers[index];\r\n\r\n            const buffer = vertexBuffer.effectiveBuffer;\r\n            if (buffer) {\r\n                renderPass2.setVertexBuffer(index, buffer.underlyingResource, vertexBuffer._validOffsetRange ? 0 : vertexBuffer.byteOffset);\r\n            }\r\n        }\r\n\r\n        // bind bind groups\r\n        for (let i = 0; i < bindGroups.length; i++) {\r\n            renderPass2.setBindGroup(i, bindGroups[i]);\r\n        }\r\n\r\n        // draw\r\n        const nonCompatMode = !this.compatibilityMode && !this._snapshotRendering.record;\r\n\r\n        if (nonCompatMode && this._currentDrawContext.indirectDrawBuffer) {\r\n            this._currentDrawContext.setIndirectData(count, instancesCount || 1, start);\r\n            if (drawType === 0) {\r\n                renderPass2.drawIndexedIndirect(this._currentDrawContext.indirectDrawBuffer, 0);\r\n            } else {\r\n                renderPass2.drawIndirect(this._currentDrawContext.indirectDrawBuffer, 0);\r\n            }\r\n        } else if (drawType === 0) {\r\n            renderPass2.drawIndexed(count, instancesCount || 1, start, 0, 0);\r\n        } else {\r\n            renderPass2.draw(count, instancesCount || 1, start, 0);\r\n        }\r\n\r\n        if (nonCompatMode) {\r\n            this._currentDrawContext.fastBundle = (renderPass2 as GPURenderBundleEncoder).finish();\r\n            bundleList.addBundle(this._currentDrawContext.fastBundle);\r\n        }\r\n\r\n        this._reportDrawCall();\r\n    }\r\n\r\n    /**\r\n     * Draw a list of indexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param indexStart defines the starting index\r\n     * @param indexCount defines the number of index to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawElementsType(fillMode: number, indexStart: number, indexCount: number, instancesCount: number = 1): void {\r\n        this._draw(0, fillMode, indexStart, indexCount, instancesCount);\r\n    }\r\n\r\n    /**\r\n     * Draw a list of unindexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawArraysType(fillMode: number, verticesStart: number, verticesCount: number, instancesCount: number = 1): void {\r\n        this._currentIndexBuffer = null;\r\n        this._draw(1, fillMode, verticesStart, verticesCount, instancesCount);\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Dispose\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Dispose and release all associated resources\r\n     */\r\n    public override dispose(): void {\r\n        this._isDisposed = true;\r\n        this.hideLoadingUI();\r\n        this._timestampQuery.dispose();\r\n        this._mainTexture?.destroy();\r\n        this._depthTexture?.destroy();\r\n        this._textureHelper.destroyDeferredTextures();\r\n        this._bufferManager.destroyDeferredBuffers();\r\n        this._device.destroy();\r\n\r\n        _CommonDispose(this, this._renderingCanvas);\r\n\r\n        super.dispose();\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Misc\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Gets the current render width\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render width\r\n     */\r\n    public getRenderWidth(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.width;\r\n        }\r\n\r\n        return this._renderingCanvas?.width ?? 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the current render height\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render height\r\n     */\r\n    public getRenderHeight(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.height;\r\n        }\r\n\r\n        return this._renderingCanvas?.height ?? 0;\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Errors\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Get the current error code of the WebGPU context\r\n     * @returns the error code\r\n     */\r\n    public getError(): number {\r\n        // TODO WEBGPU. from the webgpu errors.\r\n        return 0;\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              External Textures\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Creates an external texture\r\n     * @param video video element\r\n     * @returns the external texture, or null if external textures are not supported by the engine\r\n     */\r\n    public createExternalTexture(video: HTMLVideoElement): Nullable<ExternalTexture> {\r\n        const texture = new WebGPUExternalTexture(video);\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * Sets an internal texture to the according uniform.\r\n     * @param name The name of the uniform in the effect\r\n     * @param texture The texture to apply\r\n     */\r\n    public setExternalTexture(name: string, texture: Nullable<ExternalTexture>): void {\r\n        if (!texture) {\r\n            this._currentMaterialContext.setTexture(name, null);\r\n            return;\r\n        }\r\n        this._setInternalTexture(name, texture);\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Samplers\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Sets a texture sampler to the according uniform.\r\n     * @param name The name of the uniform in the effect\r\n     * @param sampler The sampler to apply\r\n     */\r\n    public setTextureSampler(name: string, sampler: Nullable<TextureSampler>): void {\r\n        this._currentMaterialContext?.setSampler(name, sampler);\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Storage Buffers\r\n    //------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * Creates a storage buffer\r\n     * @param data the data for the storage buffer or the size of the buffer\r\n     * @param creationFlags flags to use when creating the buffer (see Constants.BUFFER_CREATIONFLAG_XXX). The BUFFER_CREATIONFLAG_STORAGE flag will be automatically added\r\n     * @param label defines the label of the buffer (for debug purpose)\r\n     * @returns the new buffer\r\n     */\r\n    public createStorageBuffer(data: DataArray | number, creationFlags: number, label?: string): DataBuffer {\r\n        return this._createBuffer(data, creationFlags | Constants.BUFFER_CREATIONFLAG_STORAGE, label);\r\n    }\r\n\r\n    /**\r\n     * Updates a storage buffer\r\n     * @param buffer the storage buffer to update\r\n     * @param data the data used to update the storage buffer\r\n     * @param byteOffset the byte offset of the data\r\n     * @param byteLength the byte length of the data\r\n     */\r\n    public updateStorageBuffer(buffer: DataBuffer, data: DataArray, byteOffset?: number, byteLength?: number): void {\r\n        const dataBuffer = buffer as WebGPUDataBuffer;\r\n        if (byteOffset === undefined) {\r\n            byteOffset = 0;\r\n        }\r\n\r\n        let view: ArrayBufferView;\r\n        if (byteLength === undefined) {\r\n            if (data instanceof Array) {\r\n                view = new Float32Array(data);\r\n            } else if (data instanceof ArrayBuffer) {\r\n                view = new Uint8Array(data);\r\n            } else {\r\n                view = data;\r\n            }\r\n            byteLength = view.byteLength;\r\n        } else {\r\n            if (data instanceof Array) {\r\n                view = new Float32Array(data);\r\n            } else if (data instanceof ArrayBuffer) {\r\n                view = new Uint8Array(data);\r\n            } else {\r\n                view = data;\r\n            }\r\n        }\r\n\r\n        this._bufferManager.setSubData(dataBuffer, byteOffset, view, 0, byteLength);\r\n    }\r\n\r\n    private async _readFromGPUBuffer(gpuBuffer: GPUBuffer, size: number, buffer?: ArrayBufferView, noDelay?: boolean): Promise<ArrayBufferView> {\r\n        return await new Promise((resolve, reject) => {\r\n            const readFromBuffer = () => {\r\n                // eslint-disable-next-line github/no-then\r\n                gpuBuffer.mapAsync(WebGPUConstants.MapMode.Read, 0, size).then(\r\n                    () => {\r\n                        const copyArrayBuffer = gpuBuffer.getMappedRange(0, size);\r\n                        let data: ArrayBufferView | undefined = buffer;\r\n                        if (data === undefined) {\r\n                            data = new Uint8Array(size);\r\n                            (data as Uint8Array).set(new Uint8Array(copyArrayBuffer));\r\n                        } else {\r\n                            const ctor = data.constructor as any; // we want to create result data with the same type as buffer (Uint8Array, Float32Array, ...)\r\n                            data = new ctor(data.buffer);\r\n                            (data as any).set(new ctor(copyArrayBuffer));\r\n                        }\r\n                        gpuBuffer.unmap();\r\n                        this._bufferManager.releaseBuffer(gpuBuffer);\r\n                        resolve(data!);\r\n                    },\r\n                    (reason) => {\r\n                        if (this.isDisposed) {\r\n                            resolve(new Uint8Array());\r\n                        } else {\r\n                            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                            reject(reason);\r\n                        }\r\n                    }\r\n                );\r\n            };\r\n\r\n            if (noDelay) {\r\n                this.flushFramebuffer();\r\n                readFromBuffer();\r\n            } else {\r\n                // we are using onEndFrameObservable because we need to map the gpuBuffer AFTER the command buffers\r\n                // have been submitted, else we get the error: \"Buffer used in a submit while mapped\"\r\n                this.onEndFrameObservable.addOnce(() => {\r\n                    readFromBuffer();\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Read data from a storage buffer\r\n     * @param storageBuffer The storage buffer to read from\r\n     * @param offset The offset in the storage buffer to start reading from (default: 0)\r\n     * @param size  The number of bytes to read from the storage buffer (default: capacity of the buffer)\r\n     * @param buffer The buffer to write the data we have read from the storage buffer to (optional)\r\n     * @param noDelay If true, a call to flushFramebuffer will be issued so that the data can be read back immediately and not in engine.onEndFrameObservable. This can speed up data retrieval, at the cost of a small perf penalty (default: false).\r\n     * @returns If not undefined, returns the (promise) buffer (as provided by the 4th parameter) filled with the data, else it returns a (promise) Uint8Array with the data read from the storage buffer\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/promise-function-async\r\n    public readFromStorageBuffer(storageBuffer: DataBuffer, offset?: number, size?: number, buffer?: ArrayBufferView, noDelay?: boolean): Promise<ArrayBufferView> {\r\n        size = size || storageBuffer.capacity;\r\n\r\n        const gpuBuffer = this._bufferManager.createRawBuffer(\r\n            size,\r\n            WebGPUConstants.BufferUsage.MapRead | WebGPUConstants.BufferUsage.CopyDst,\r\n            undefined,\r\n            \"TempReadFromStorageBuffer\"\r\n        );\r\n\r\n        this._renderEncoder.copyBufferToBuffer(storageBuffer.underlyingResource, offset ?? 0, gpuBuffer, 0, size);\r\n\r\n        return this._readFromGPUBuffer(gpuBuffer, size, buffer, noDelay);\r\n    }\r\n\r\n    /**\r\n     * Read data from multiple storage buffers\r\n     * @param storageBuffers The list of storage buffers to read from\r\n     * @param offset The offset in the storage buffer to start reading from (default: 0). This is the same offset for all storage buffers!\r\n     * @param size  The number of bytes to read from each storage buffer (default: capacity of the first buffer)\r\n     * @param buffer The buffer to write the data we have read from the storage buffers to (optional). If provided, the buffer should be large enough to hold the data from all storage buffers!\r\n     * @param noDelay If true, a call to flushFramebuffer will be issued so that the data can be read back immediately and not in engine.onEndFrameObservable. This can speed up data retrieval, at the cost of a small perf penalty (default: false).\r\n     * @returns If not undefined, returns the (promise) buffer (as provided by the 4th parameter) filled with the data, else it returns a (promise) Uint8Array with the data read from the storage buffer\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/promise-function-async\r\n    public readFromMultipleStorageBuffers(storageBuffers: DataBuffer[], offset?: number, size?: number, buffer?: ArrayBufferView, noDelay?: boolean): Promise<ArrayBufferView> {\r\n        size = size || storageBuffers[0].capacity;\r\n\r\n        const gpuBuffer = this._bufferManager.createRawBuffer(\r\n            size * storageBuffers.length,\r\n            WebGPUConstants.BufferUsage.MapRead | WebGPUConstants.BufferUsage.CopyDst,\r\n            undefined,\r\n            \"TempReadFromMultipleStorageBuffers\"\r\n        );\r\n\r\n        for (let i = 0; i < storageBuffers.length; i++) {\r\n            this._renderEncoder.copyBufferToBuffer(storageBuffers[i].underlyingResource, offset ?? 0, gpuBuffer, i * size, size);\r\n        }\r\n\r\n        return this._readFromGPUBuffer(gpuBuffer, size * storageBuffers.length, buffer, noDelay);\r\n    }\r\n\r\n    /**\r\n     * Sets a storage buffer in the shader\r\n     * @param name Defines the name of the storage buffer as defined in the shader\r\n     * @param buffer Defines the value to give to the uniform\r\n     */\r\n    public setStorageBuffer(name: string, buffer: Nullable<StorageBuffer>): void {\r\n        this._currentDrawContext?.setBuffer(name, (buffer?.getBuffer() as WebGPUDataBuffer) ?? null);\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA,sCAAA,EAAwC;;;AACxC,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;;AACvC,OAAO,EAAE,eAAe,EAAyB,MAAM,uCAAuC,CAAC;AAE/F,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAG7C,gEAAgE;AAChE,OAAO,KAAK,eAAe,MAAM,0BAA0B,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEjD,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AAKvE,OAAO,EAAE,yBAAyB,EAAE,MAAM,qCAAqC,CAAC;AAChF,OAAO,EAAE,yBAAyB,EAAE,MAAM,qCAAqC,CAAC;AAEhF,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAC;AACvF,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AACrE,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AAEnE,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AAEvE,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAC;AACvF,OAAO,EAAE,0BAA0B,EAAE,MAAM,qCAAqC,CAAC;AACjF,OAAO,EAAE,uBAAuB,EAAE,MAAM,kCAAkC,CAAC;AAE3E,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAE3D,OAAO,EAAE,0BAA0B,EAAE,uBAAuB,EAAE,0BAA0B,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AACxK,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AAErE,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AACrE,OAAO,EAAE,iBAAiB,EAAE,MAAM,gCAAgC,CAAC;AAEnE,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAEzD,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AAGvE,OAAO,EAAE,uBAAuB,EAAE,MAAM,kCAAkC,CAAC;AAI3E,OAAO,yBAAyB,CAAC;AAOjC,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EAAE,kBAAkB,EAAE,MAAM,4BAA4B,CAAC;AAChE,OAAO,EACH,2BAA2B,EAC3B,cAAc,EACd,eAAe,EACf,aAAa,EACb,iBAAiB,EACjB,kBAAkB,EAClB,iBAAiB,EACjB,cAAc,EACd,WAAW,GACd,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,SAAS,EAAE,MAAM,oCAAoC,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,+CAA+C,CAAC;AACvD,OAAO,qCAAqC,CAAC;AAC7C,OAAO,wCAAwC,CAAC;AAChD,OAAO,yCAAyC,CAAC;AACjD,OAAO,4CAA4C,CAAC;AACpD,OAAO,sBAAsB,CAAC;AAC9B,OAAO,EAAE,mBAAmB,EAAE,MAAM,+BAA+B,CAAC;AAEpE,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AAGvE,OAAO,kCAAkC,CAAC;AAC1C,OAAO,uCAAuC,CAAC;AAC/C,OAAO,wCAAwC,CAAC;AAChD,OAAO,wCAAwC,CAAC;AAChD,OAAO,yCAAyC,CAAC;AACjD,OAAO,gDAAgD,CAAC;AACxD,OAAO,6CAA6C,CAAC;AACrD,OAAO,kCAAkC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1C,MAAM,mCAAmC,GAA6B;IAClE,KAAK,EAAE,oCAAqC;IAC5C,SAAS,EAAA,KAAA,wCAAA,EAAsC;IAC/C,MAAM,EAAE,SAAgB,EAAE,uCAAuC;IACjE,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE,CAAC;CACrB,CAAC;AAEF,MAAM,uBAAuB,GAA6B;IACtD,KAAK,EAAE,sBAAuB;IAC9B,SAAS,EAAA,KAAA,wCAAA,EAAsC;IAC/C,MAAM,EAAE,SAAgB,EAAE,uCAAuC;IACjE,aAAa,EAAE,CAAC;IAChB,eAAe,EAAE,CAAC;CACrB,CAAC;AACF,MAAM,UAAU,GAAG,IAAI,0KAAM,EAAE,CAAC;AAgG1B,MAAO,YAAa,+KAAQ,mBAAgB;IAgJ9C;;OAEG,CACH,IAAoB,qBAAqB,GAAA;QACrC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;IACxC,CAAC;IAED,IAAoB,qBAAqB,CAAC,IAAY,EAAA;QAClD,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;OAEG,CACI,sBAAsB,GAAA;QACzB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;IACpC,CAAC;IAED;;;OAGG,CACH,IAAoB,iBAAiB,GAAA;QACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;IAC3C,CAAC;IAED,IAAoB,iBAAiB,CAAC,QAAQ,EAAA;QAC1C,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,QAAQ,CAAC;IAC/C,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;IACpE,CAAC;IAED,IAAW,oBAAoB,CAAC,OAAgB,EAAA;QAC5C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,2BAA2B,GAAA;QAClC,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;IAClF,CAAC;IAED,IAAW,2BAA2B,CAAC,OAAgB,EAAA;QACnD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,OAAO,CAAC;QACjD,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,GAAA;QAC7B,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;IAC1E,CAAC;IAED,IAAW,sBAAsB,CAAC,OAAgB,EAAA;QAC9C,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,kBAAkB,GAAA;QACrB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACa,aAAa,CAAC,IAAY,EAAA;QACtC,iLAAO,gBAAA,AAAa,EAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG,CACH,gDAAgD;IACzC,MAAM,KAAK,gBAAgB,GAAA;QAC9B,OAAO,CAAC,SAAS,CAAC,GAAG,GACf,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GACtB,SAAS,CAAC,GAAG,CACR,cAAc,EAAE,AACjB,0CAA0C;SACzC,IAAI,CACD,CAAC,OAA+B,EAAE,CAAG,CAAD,AAAE,CAAC,OAAO,EAC9C,GAAG,CAAG,CAAD,IAAM,CACd,AACD,0CAA0C;SACzC,KAAK,CAAC,GAAG,CAAG,CAAD,IAAM,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,WAAW,GAAA;iKACzB,SAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,GAAA;QAC7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,wDAAA,EAA0D,CAC1D,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IAED,+DAAA,EAAiE,CACjE,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,oDAAA,EAAsD,CACtD,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED,iDAAA,EAAmD,CACnD,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAoB,WAAW,GAAA;QAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;QAE7C,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,gBAAgB;YACpD,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,IAAI,kBAAkB;YAC9D,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,iBAAiB;SAC9D,CAAC;IACN,CAAC;IAED;;;;OAIG,CACH,IAAoB,iBAAiB,GAAA;QACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,IAAoB,iBAAiB,CAAC,IAAa,EAAA;QAC/C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;IACrG,CAAC;IAED;;;;;OAKG,CACH,2FAA2F;IACpF,MAAM,CAAC,WAAW,CAAC,MAAyB,EAAmC;sBAAjC,iEAA+B,CAAA,CAAE;QAClF,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEjD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,mFAAmF;YACnF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAG,CAAD,MAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;IACP,CAAC;IAyDD;;;OAGG,CACH,2FAA2F;IACpF,0BAA0B,GAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACtC,IAAI,CAAC,6BAA6B,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;oBAEhB;oBAAxB;gBADvB,mFAAmF;gBACnF,IAAI,CAAC,iBAAiB,8BAAK,CAAC,eAAe,+FAAQ,CAAC,QAAQ,kEAAE,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,OAAY,EAAE,EAAE;wBAIjD;oBAH/C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;oBACxB,IAAI,CAAC,SAAS,GAAG,IAAI,+LAAc,EAAE,CAAC;;oBACtC,mFAAmF;oBACnF,IAAI,CAAC,SAAS,CAAC,SAAS,4BAAK,CAAC,aAAa,+CAAlB,4CAA0B,CAAC,QAAQ,kEAAE,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;wBAClF,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;wBAC1C,OAAO,EAAE,CAAC;oBACd,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;QACP,CAAC;QAED,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;;;;OAKG,CACH,2FAA2F;IACpF,SAAS,CAAC,cAA+B,EAAE,YAA2B,EAAA;QACxE,IAAI,CAAC,QAAmB,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QACvD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,OAAO,AACH,SAAS,CACJ,GAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,AACnC,0CAA0C;SACzC,IAAI,CAAC,KAAK,EAAE,OAA+B,EAAE,EAAE;YAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,4CAA4C;gBAC5C,MAAM,wDAAwD,CAAC;YACnE,CAAC,MAAM,CAAC;;gBACJ,IAAI,CAAC,QAAQ,GAAG,OAAQ,CAAC;gBACzB,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;+CAClC,CAAC,QAAQ,CAAC,QAAQ,4DAAtB,wBAAwB,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACxC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAsC,CAAC,CAAC;gBAClF,CAAC,CAAC,CAAC;gBACH,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACpD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;;gBAEvC,MAAM,gBAAgB,0CAAO,CAAC,QAAQ,CAAC,gBAAgB,2DAA9B,kCAAkC,CAAA,CAAE,CAAC;;gBAC9D,MAAM,gBAAgB,8HAAqB,gBAAgB,IAAI,0DAAtC,gBAAgB,qBAAuB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBAEhJ,IAAI,gBAAgB,EAAE,CAAC;oBACnB,MAAM,mBAAmB,GAAG,gBAAgB,CAAC;oBAC7C,MAAM,eAAe,GAAqB,EAAE,CAAC;oBAE7C,KAAK,MAAM,SAAS,IAAI,mBAAmB,CAAE,CAAC;wBAC1C,IAAI,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;4BAC7D,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACpC,CAAC;oBACL,CAAC;oBAED,gBAAgB,CAAC,gBAAgB,GAAG,eAAe,CAAC;gBACxD,CAAC;gBAED,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC;oBACrE,gBAAgB,CAAC,cAAc,GAAG,CAAA,CAAE,CAAC;oBACrC,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,uBAAuB,CAAE,CAAC;wBAC9C,IAAI,IAAI,KAAK,iBAAiB,IAAI,IAAI,KAAK,iBAAiB,EAAE,CAAC;4BAE3D,SAAS;wBACb,CAAC;wBACD,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;oBAC/E,CAAC;gBACL,CAAC;gBAED,gBAAgB,CAAC,KAAK,GAAG,sBAAmC,CAAE,CAAC,KAAhB,IAAI,CAAC,QAAQ;gBAE5D,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;YAC/D,CAAC;QACL,CAAC,CAAC,AACF,0CAA0C;SACzC,IAAI,CAAC,CAAC,MAAiB,EAAE,EAAE;gBAGxB;YAFA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAC;0CAC/B,CAAC,OAAO,CAAC,QAAQ,kFAAE,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBACvC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAsC,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;YAEnC,IAAI,mBAAmB,GAAG,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,KAAK,EAAE,EAAE;gBACvD,IAAI,EAAE,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBACtD,kKAAM,CAAC,IAAI,CAAC,mCAA4B,mBAAmB,GAAG,CAAC,EAAA,cAAgC,KAAM,CAAC,KAAK,EAAA,OAAgC,CAAE,CAAC,CAAC,IAAxB,KAAM,CAAC,KAAK,CAAC,OAAO;gBAC/I,CAAC,MAAM,IAAI,mBAAmB,EAAE,KAAK,IAAI,CAAC,sBAAsB,EAAE,CAAC;6KAC/D,SAAM,CAAC,IAAI,CACP,+CAA0E,OAA3B,IAAI,CAAC,sBAAsB,EAAA,qEAAsE,CACnJ,CAAC;gBACN,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBAChC,mFAAmF;;0CAC/E,CAAC,OAAO,CAAC,IAAI,uDAAjB,mBAAmB,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC7B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;wBACnB,OAAO;oBACX,CAAC;oBACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;6KAC5B,SAAM,CAAC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,CAAC;oBAC5C,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBACnD,kEAAkE;oBAClE,IAAI,CAAC,8BAA8B,CAAC,KAAK,IAAI,EAAE;;wBAC3C,MAAM,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;wBACzD,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;wBACjD,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;wBACvD,MAAM,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,CAAC;wBACrE,MAAM,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;wBAC3D,MAAM,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,CAAC;;wBAErE,MAAM,IAAI,CAAC,SAAS,8BAAK,CAAC,eAAe,cAApB,iFAA4B,CAAC,QAAQ,mDAAb,eAAe,cAAc,EAAE,2BAAI,CAAC,aAAa,wFAAI,IAAI,CAAC,QAAQ,oEAAE,YAAY,CAAC,CAAC;wBAE/H,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;wBACnD,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;wBAC3C,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;wBACjD,IAAI,CAAC,2BAA2B,GAAG,2BAA2B,CAAC;wBAC/D,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;wBACrD,IAAI,CAAC,2BAA2B,GAAG,2BAA2B,CAAC;wBAC/D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBACnC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC,AACF,0CAA0C;SACzC,IAAI,CAAC,GAAG,EAAE;YACP,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,IAAI,CAAC,cAAc,GAAG,uLAAI,sBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAClE,IAAI,CAAC,cAAc,GAAG,wLAAI,uBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACvH,IAAI,CAAC,aAAa,GAAG,sLAAI,qBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1D,IAAI,CAAC,gBAAgB,GAAG,yLAAI,wBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAC1F,IAAI,CAAC,eAAe,GAAG,wLAAI,uBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACzF,IAAI,CAAC,eAAe,GAAI,IAAI,CAAC,OAAe,CAAC,cAAc,CAAC,CAAC,CAAC,wLAAI,uBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAE,SAAiB,CAAC;YACrJ,IAAI,CAAC,WAAW,GAAG,oLAAI,mBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtD,IAAI,CAAC,kBAAkB,GAAG,2LAAI,0BAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAE3G,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAC9C,IAAI,YAAY,CAAC;gBAAC,CAAC,CAAC;gBAAE,CAAC;aAAC,CAAC,iLACzB,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,kLAAG,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,EACzE,WAAW,CACd,CAAC;YACF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAClD,IAAI,YAAY,CAAC;gBAAC,CAAC;gBAAE,CAAC;aAAC,CAAC,iLACxB,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,kLAAG,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,EACzE,eAAe,CAClB,CAAC;YAEF,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;6KACzB,SAAM,CAAC,GAAG,CAAC;wBAAC,YAAY,GAAI,IAAY,CAAC,MAAM,GAAG,UAAU;wBAAE,qBAAqB;qBAAC,CAAC,CAAC;gBAC1F,CAAC;YACL,CAAC;YAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACvF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAEvF,IAAI,CAAC,kBAAkB,GAAG,+JAAI,gBAAY,CAAC,IAAI,EAAE;gBAAC,CAAC;aAAC,EAAE,EAAE,EAAE;gBACtD,MAAM,EAAE,CAAC;gBACT,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,mBAAmB;aAC7B,CAAC,CAAC;YAEH,IAAI,CAAC,oBAAoB,GAAG,iMAAI,gCAA6B,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAErG,IAAI,CAAC,kBAAkB,GAAG,2LAAI,0BAAuB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACjF,IAAI,CAAC,qBAAqB,GAAG,8LAAI,6BAA0B,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACvF,IAAI,CAAC,qBAAqB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;YAE9D,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;YACzC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;YACrD,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;YAEzC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAE3D,IAAI,CAAC,UAAU,GAAG,mLAAI,kBAAe,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACnF,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,EAAG,CAAC;YACrD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,qBAAqB,EAAG,CAAC;YAC7D,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAE5D,IAAI,CAAC,8BAA8B,EAAE,CAAC;YACtC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC,CACD,AADE,0CACwC;SACzC,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;qKACd,SAAM,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;YAC9E,MAAM,CAAC,CAAC;QACZ,CAAC,CAAC,CACT,CAAC;IACN,CAAC;IAED,2FAA2F;IACnF,iBAAiB,CAAC,cAA+B,EAAA;QACrD,cAAc,GAAG,cAAc,IAAI,CAAA,CAAE,CAAC;QACtC,cAAc,GAAG;YACb,GAAG,YAAY,CAAC,sBAAsB;YACtC,GAAG,cAAc;SACpB,CAAC;QAEF,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,cAAc,CAAC,OAAO,CAAC;QAClC,CAAC;QAED,IAAK,IAAY,CAAC,OAAO,EAAE,CAAC;YACxB,OAAQ,IAAY,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,cAAc,CAAC,MAAM,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC;YACnD,0CAA0C;YAC1C,OAAO,gKAAK,CAAC,sBAAsB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACjE,OAAQ,IAAY,CAAC,OAAO,yJAAC,QAAK,CAAC,mBAAmB,CAAC,cAAc,CAAC,QAAS,CAAC,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC;QACP,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChD,CAAC;IAEO,iBAAiB,GAAA;QACrB,YAAY;QACZ,iEAAiE;QAEjE,IAAI,CAAC,KAAK,GAAG;YACT,qBAAqB,EAAE,IAAI,CAAC,aAAa,CAAC,gCAAgC;YAC1E,0BAA0B,EAAE,IAAI,CAAC,aAAa,CAAC,gCAAgC;YAC/E,6BAA6B,EAAE,IAAI,CAAC,aAAa,CAAC,gCAAgC,GAAG,CAAC;YACtF,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB;YACxD,qBAAqB,EAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB;YAC/D,oBAAoB,EAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB;YAC9D,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,mBAAmB;YACxD,cAAc,EAAE,CAAC;YACjB,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,4BAA4B;YAClE,yBAAyB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,2BAA2B,GAAG,CAAC,CAAC;YACzF,uBAAuB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,2BAA2B,GAAG,CAAC,CAAC;YACvF,mBAAmB,EAAE,IAAI;YACzB,IAAI,EAAE,AAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAA,2BAAA,sDAAA,GAAoD,KAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAQ;YAChI,IAAI,EAAE,AAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAA,yBAAA,oDAAA,GAAkD,KAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAQ;YAC9H,KAAK,EAAE,IAAI;YACX,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,AAAC,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAA,2BAAA,sDAAA,GAAoD,KAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAQ;YAChI,IAAI,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAA,yBAAA,oDAAA,GAAkD,KAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACrH,aAAa,EAAE,EAAE,EAAE,2LAA2L;YAC9M,WAAW,EAAE,IAAI;YACjB,sBAAsB,EAAE,IAAI;YAC5B,4BAA4B,EAAE,IAAI;YAClC,gBAAgB,EAAE,IAAI;YACtB,2BAA2B,EAAE,KAAK,EAAE,mDAAmD;YACvF,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAA,2BAAA,uDAAA,GAAqD,KAAI,CAAC;YACzH,YAAY,EAAE,IAAI;YAClB,2BAA2B,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAA,qBAAA,iDAAA,GAA+C,KAAI,CAAC;YACtH,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;YACtB,+BAA+B,EAAE,IAAI;YACrC,sBAAsB,EAAE,IAAI;YAC5B,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,IAAI;YAChB,oBAAoB,EAAE,IAAI;YAC1B,qBAAqB,EAAE,IAAI;YAC3B,iBAAiB,EAAE,KAAK;YACxB,eAAe,EAAE,IAAI;YACrB,UAAU,EACN,OAAO,cAAc,KAAK,WAAW,IAAI,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAA,kBAAA,8CAAA,GAA4C,MAAK,CAAC,CAAC,CAAC,CAAC,CAAE,IAAY,CAAC,CAAC,CAAC,SAAS;YACjK,qBAAqB,EAAE,OAAO,cAAc,KAAK,WAAW;YAC5D,4BAA4B,EAAE,IAAI;YAClC,SAAS,EAAE,KAAK;YAChB,eAAe,EAAE,KAAK;YACtB,qBAAqB,EAAE,SAAS;YAChC,WAAW,EAAE,IAAI;YACjB,cAAc,EAAE,CAAC,EAAE,2CAA2C;YAC9D,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;YACtB,qBAAqB,EAAE,IAAI;YAC3B,kBAAkB,EAAE,IAAI;YACxB,yBAAyB,EAAE,KAAK;YAChC,eAAe,EAAE,IAAI;YACrB,2BAA2B,EAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB;YACrE,yBAAyB,EAAE,KAAK;YAChC,aAAa,EAAE,KAAK,EAAE,6DAA6D;YACnF,wBAAwB,EAAE,IAAI;YAC9B,kBAAkB,EAAE,IAAI;SAC3B,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG;YACb,+BAA+B,EAAE,IAAI;YACrC,yCAAyC,EAAE,IAAI;YAC/C,0BAA0B,EAAE,IAAI;YAChC,qBAAqB,EAAE,IAAI;YAC3B,4BAA4B,EAAE,KAAK;YACnC,wBAAwB,EAAE,IAAI;YAC9B,gBAAgB,EAAE,IAAI;YACtB,4BAA4B,EAAE,IAAI;YAClC,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,KAAK;YACpB,iBAAiB,EAAE,IAAI;YACvB,+BAA+B,EAAE,IAAI;YACrC,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;YAClB,iBAAiB,EAAE,IAAI;YACvB,6BAA6B,EAAE,IAAI;YACnC,yBAAyB,EAAE,IAAI;YAC/B,sBAAsB,EAAE,KAAK;YAC7B,oBAAoB,EAAE,KAAK;YAC3B,kBAAkB,EAAE,KAAK;YACzB,sBAAsB,EAAE,IAAI;YAC5B,8BAA8B,EAAE,IAAI;YACpC,mBAAmB,EAAE,IAAI;YACzB,uBAAuB,EAAE,IAAI;YAC7B,8CAA8C,EAAE,IAAI;YACpD,sDAAsD,EAAE,IAAI;YAC5D,0BAA0B,EAAE,KAAK;SACpC,CAAC;IACN,CAAC;IAEO,8BAA8B,GAAA;QAClC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,4CAA4C;YAC5C,MAAM,wCAAwC,CAAC;QACnD,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAgC,CAAC;QAC1F,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAgB,CAAC;QACnD,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,GAAG;YAAC,yLAAI,wBAAqB,CAAC,IAAI,CAAC;SAAC,CAAC;QAC3F,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;QACtF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACtD,CAAC;IAED,yFAAyF;IACjF,0BAA0B,GAAA;QAC9B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,CAAC,mBAAmB,GAAG;YACvB,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YAChC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAClC,kBAAkB,EAAE,CAAC;SACxB,CAAC;QAEF,MAAM,gBAAgB,GAAG,IAAI,YAAY,CAAC;YAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;SAAC,CAAC,CAAC;QAExE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC;QACrE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC;QAEzE,IAAI,oBAAoD,CAAC;QAEzD,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC1B,MAAM,qBAAqB,GAAyB;gBAChD,KAAK,EAAE,4BAAqB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAA,KAAmC,OAA/B,IAAI,CAAC,EAAyC,iBAAtB,CAAC,MAAM,EAAA;gBAC7F,IAAI,EAAE,IAAI,CAAC,mBAAmB;gBAC9B,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,IAAI,CAAC,oBAAoB;gBACtC,SAAS,EAAA,KAAA,wCAAA,EAAsC;gBAC/C,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAgB;gBACtC,KAAK,EAAA,GAAA,iDAAA,EAA+C;aACvD,CAAC;YAEF,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1D,CAAC;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YACtE,oBAAoB,GAAG;gBACnB;oBACI,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;wBAC/B,KAAK,EAAE,oCAAoC;wBAC3C,SAAS,EAAA,KAAA,wCAAA,EAAsC;wBAC/C,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAgB;wBACtC,aAAa,EAAE,CAAC;wBAChB,eAAe,EAAE,CAAC;qBACrB,CAAC;oBACF,UAAU,EAAE,IAAI,0KAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAClC,MAAM,EAAA,QAAA,gCAAA,EAA8B;oBACpC,OAAO,EAAA,QAAA,iCAAA,EAA+B,EAAE,oMAAoM;iBAC/O;aACJ,CAAC;QACN,CAAC,MAAM,CAAC;YACJ,oBAAoB,GAAG;gBACnB;oBACI,IAAI,EAAE,SAAgB;oBACtB,UAAU,EAAE,IAAI,0KAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAClC,MAAM,EAAA,QAAA,gCAAA,EAA8B;oBACpC,OAAO,EAAA,QAAA,iCAAA,EAA+B;iBACzC;aACJ,CAAC;QACN,CAAC;QAED,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAA,uBAAA,qDAAA,GAAmD,CAAC,EAAA,eAAA,8CAAA,EAA2C,CAAC;QAEvK,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAElD,MAAM,sBAAsB,GAAyB;YACjD,KAAK,EAAE,mCAA4B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAA,KAAmC,CAAE,MAAjC,IAAI,CAAC,mBAAmB,CAAC,MAAM;YACpG,IAAI,EAAE,IAAI,CAAC,mBAAmB;YAC9B,aAAa,EAAE,CAAC;YAChB,WAAW,EAAE,IAAI,CAAC,oBAAoB;YACtC,SAAS,EAAA,KAAA,wCAAA,EAAsC;YAC/C,MAAM,EAAE,IAAI,CAAC,sBAAsB,CAAC,kBAAkB;YACtD,KAAK,EAAA,GAAA,iDAAA,EAA+C;SACvD,CAAC;QAEF,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3D,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;QACxE,MAAM,mBAAmB,GAAwC;YAC7D,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;gBAChC,KAAK,EAAE,uCAAgC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAA,KAAmC,CAAE,MAAjC,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBACxG,SAAS,EAAA,KAAA,wCAAA,EAAsC;gBAC/C,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;gBACjC,aAAa,EAAE,CAAC;gBAChB,eAAe,EAAE,CAAC;aACrB,CAAC;YAEF,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,WAAW,EAAA,QAAA,gCAAA,EAA8B;YACzC,YAAY,EAAA,QAAA,iCAAA,EAA+B;YAC3C,iBAAiB,EAAE,IAAI,CAAC,kBAAkB;YAC1C,aAAa,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA,QAAA,gCAAA,EAA6B;YAC/E,cAAc,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA,QAAA,iCAAA,EAA8B;SACpF,CAAC;QAEF,IAAI,CAAC,sBAAsB,CAAC,oBAAoB,GAAG;YAC/C,KAAK,EAAE,gBAAgB;YACvB,gBAAgB,EAAE,oBAAoB;YACtC,sBAAsB,EAAE,mBAAmB;SAC9C,CAAC;QAEF,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,6GAA6G;IAClI,CAAC;IAED;;;OAGG,CACgB,WAAW,CAAC,MAAyB,EAAA;QACpD,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;kLAE1B,cAAA,AAAW,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACrD,CAAC;IAEO,iBAAiB,GAAA;QACrB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YACpB,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAgB;YACtC,KAAK,EAAE,GAAA,iDAAA,MAAA,EAAA,wCAAA,EAAoF;YAC3F,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAA,gBAAA,iDAAA,GAA+C,CAAC,EAAA,SAAA,0CAAA,EAAuC;SAC9H,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG,CACa,iBAAiB,CAAC,KAAqC,EAAE,WAAmB,EAAE,YAAoB,EAAA;QAC9G,iLAAO,oBAAA,AAAiB,EAAC,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IACrE,CAAC;IAED;;;;;OAKG,CACH,gEAAgE;IAChD,KAAK,CAAC,4BAA4B,CAAC,WAAmB,EAAE,OAA4B,EAAA;QAChG,OAAO,gLAAM,8BAAA,AAA2B,EAAC,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED;;;OAGG,CACa,gBAAgB,CAAC,kBAA2B,EAAA;QACxD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1B,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;OAGG,CACa,eAAe,CAAC,kBAA2B,EAAA;QACvD,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,IAAI,CAAC,qBAAqB,GAAG,kBAAkB,CAAC;YAChD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;0LACxB,oBAAA,AAAiB,EAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACa,cAAc,GAAA;QAC1B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;sLACpB,iBAAA,AAAc,EAAE,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;OAEG,CACI,gBAAgB,GAAA;QACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;sLACxB,qBAAA,AAAkB,EAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG,CACI,eAAe,GAAA;kLAClB,kBAAA,AAAe,EAAE,CAAC;IACtB,CAAC;IAEkB,eAAe,GAAA;QAC9B,KAAK,CAAC,eAAe,EAAE,CAAC;QAExB,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,CAAE,CAAC;YAC/C,iJAAiJ;YACjJ,IAAK,aAAa,CAAC,SAAS,EAAuB,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC7E,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC7B,CAAC;QACL,CAAC;IACL,CAAC;IAEkB,8BAA8B,CAAC,UAAsB,EAAA;qMACpE,gCAA6B,CAAC,UAAU,EAAE,CAAC;6LAC3C,wBAAqB,CAAC,UAAU,EAAE,CAAC;QAEnC,gDAAgD;QAChD,MAAM,WAAW,GAAG,CAAC,MAAe,EAAE,EAAE;YACpC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;gBACzB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,CAAE,CAAC;oBAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;oBACjC,IAAI,CAAC,SAAS,EAAE,CAAC;wBACb,SAAS;oBACb,CAAC;oBACD,KAAK,MAAM,OAAO,IAAI,SAAS,CAAE,CAAC;wBAC9B,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC;oBAC/B,CAAC;gBACL,CAAC;gBAED,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,SAAS,CAAE,CAAC;;2DAC5B,gBAAgB,+DAAzB,QAAQ,mBAAmB,KAAK,EAAE,CAAC;gBACvC,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEjC,oHAAoH;QACpH,MAAM,OAAO,GAAoB,EAAE,CAAC;QACpC,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,eAAe,CAAE,CAAC;YAC/C,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7C,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;QAE/B,KAAK,CAAC,8BAA8B,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG,CACa,OAAO,CAAC,KAAa,EAAE,MAAc,EAAsB;2BAApB,YAAY,qDAAG,KAAK;QACvE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/E,kKAAM,CAAC,GAAG,CAAC;oBAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,cAAc;oBAAE,KAAK;oBAAE,MAAM;iBAAC,CAAC,CAAC;YACnF,CAAC;QACL,CAAC;QAED,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,oFAAoF;YACpF,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAClC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAID;;OAEG,CACa,mBAAmB,CAAC,cAA8B,EAAA;QAC9D,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACzC,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACrC,CAAC;QACD,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG,CACI,2BAA2B,CAAC,cAA8B,EAAE,QAAiB,EAAA;QAChF,OAAO,iMAAI,gCAA6B,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IACvE,CAAC;IAEO,qBAAqB,GAAA;QACzB,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxD,+GAA+G;YAC/G,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC5F,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,IAAI,CAAC,kBAAmB,CAAC;IACpC,CAAC;IAED,cAAA,EAAgB,CACT,4BAA4B,GAAA;QAC/B,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC;IAChG,CAAC;IAED,gFAAgF;IAChF,yDAAyD;IACzD,gFAAgF;IAEhF,cAAA,EAAgB,CACT,WAAW,GAAA;QACd,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACrH,CAAC;IAED;;;;OAIG,CACI,UAAU,CAAC,UAAoB,EAAA;QAClC,IAAI,IAAI,CAAC,6BAA6B,IAAI,CAAC,UAAU,EAAE,CAAC;YACpD,OAAO;QACX,CAAC;QAED,gPAAgP;QAChP,sEAAsE;QACtE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAC1C,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAEvD,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;YAEnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;YAErD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;YACrI,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;YAEjH,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;IAC9C,CAAC;IAED;;;OAGG,CACa,aAAa,CAAC,MAAe,EAAA;QACzC,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;QAC/B,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;IAED;;;OAGG,CACa,aAAa,GAAA;QACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IASO,mBAAmB,GAAA;QACvB,MAAM,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAC5B,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAC1B,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAC1B,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;QAE/B,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC;QAEpJ,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,cAAc,CAAC,UAAsC,EAAA;QACzD,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAE7C,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAE3C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC7B,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,OAAO,CAAC,oLAAI,2BAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,qBAAqB,EAAE,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;yKAC/E,SAAM,CAAC,GAAG,CAAC;oBACP,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,yBAAyB;oBAC5D,IAAI,CAAC,eAAe,CAAC,CAAC;oBACtB,IAAI,CAAC,eAAe,CAAC,CAAC;oBACtB,IAAI,CAAC,eAAe,CAAC,CAAC;oBACtB,IAAI,CAAC,eAAe,CAAC,CAAC;oBACtB,8BAA8B,GAAG,IAAI,CAAC,sBAAsB,EAAE;iBACjE,CAAC,CAAC;YACP,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,SAAS,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAA;QAChE,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,MAAM,CAAC;IACpC,CAAC;IAKO,kBAAkB,GAAA;QACtB,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,EAC3B,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,EACzB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,EACzB,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAE9B,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,CAAC;QAEhJ,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,aAAa,CAAC,UAAsC,EAAA;QACxD,MAAM,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAErI,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,OAAO,CAAC,oLAAI,0BAAuB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5H,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,qBAAqB,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACxH,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;yKAC/E,SAAM,CAAC,GAAG,CAAC;oBACP,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,wBAAwB;oBAC3D,IAAI,CAAC,cAAc,CAAC,CAAC;oBACrB,IAAI,CAAC,cAAc,CAAC,CAAC;oBACrB,IAAI,CAAC,cAAc,CAAC,CAAC;oBACrB,IAAI,CAAC,cAAc,CAAC,CAAC;oBACrB,8BAA8B,GAAG,IAAI,CAAC,sBAAsB,EAAE;iBACjE,CAAC,CAAC;YACP,CAAC;QACL,CAAC;IACL,CAAC;IAEO,gBAAgB,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC;IACpI,CAAC;IAEM,aAAa,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAA;QACpE,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,MAAM,CAAC;IACnC,CAAC;IAEM,cAAc,GAAA;QACjB,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;QAClG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9G,CAAC;IAIO,qBAAqB,GAAA;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,KAAK,IAAI,CAAC,mBAAmB,CAAC;QAC/E,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;QAClE,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,UAAsC,EAAA;QAC3D,IAAI,UAAU,EAAE,CAAC;;YACb,UAAU,CAAC,OAAO,CAAC,oLAAI,6BAA0B,EAAC,0CAAI,CAAC,qBAAqB,CAAC,OAAO,qGAAI,CAAC,CAAC,CAAC,CAAC;QAChG,CAAC,MAAM,CAAC;gBAC6C;YAAjD,IAAI,CAAC,qBAAqB,EAAE,CAAC,mBAAmB,6CAAK,CAAC,qBAAqB,CAAC,OAAO,uGAAI,CAAC,CAAC,CAAC;QAC9F,CAAC;IACL,CAAC;IAIO,qBAAqB,GAAA;QACzB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;QAEpD,MAAM,MAAM,GACR,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAC7C,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAC7C,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAC7C,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAElD,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,gBAAgB,CAAC,UAAsC,EAAA;QAC3D,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,OAAO,CAAC,oLAAI,6BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjG,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,qBAAqB,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,eAA2B,CAAC,CAAC;QAChG,CAAC;IACL,CAAC;IAEO,sBAAsB,GAAA;QAC1B,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9G,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1G,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IACjI,CAAC;IAED;;;;;;OAMG,CACI,KAAK,CAAC,KAA4B,EAAE,UAAmB,EAAE,KAAc,EAA0B;sBAAxB,iEAAmB,KAAK;QACpG,+BAA+B;QAC/B,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YACjC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE3C,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;yKAC/E,SAAM,CAAC,GAAG,CAAC;oBAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,wBAAwB;oBAAE,UAAU;oBAAE,SAAS;oBAAE,KAAK;oBAAE,WAAW;oBAAE,OAAO;oBAAE,qBAAqB;oBAAE,UAAU;iBAAC,CAAC,CAAC;YACrK,CAAC;QACL,CAAC;QAED,0HAA0H;QAC1H,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,UAAU,EAAE,CAAC;gBACb,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC3B,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBACnH,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACtE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACnE,CAAC,MAAM,CAAC;gBACJ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACjC,CAAC;gBACD,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAClH,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,UAAU,EAAE,CAAC;gBAC1C,IAAI,CAAC,oBAAoB,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACtF,CAAC;YACD,IAAI,UAAU,EAAE,CAAC;gBACb,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACtE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,UAAkC,EAAE,UAAoB,EAAE,YAAsB,EAAA;QACnG,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEjF,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;;QAChE,IAAI,CAAC,UAAU,CAAC,iBAAiB,kDACzB,CAAC,oBAAoB,CAAC,cAAc,cAAxC,mGAA4C,EAAE,oDAC1C,CAAC,oBAAoB,CAAC,eAAe,sEAAzC,6CAA6C,EAAE,EAC/C,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAC5C,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,oLAAI,6BAA0B,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACtF,CAAC,MAAM,CAAC;YACJ,UAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEhH,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAO,CAAC,CAAC;YACpC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACxC,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC;IACL,CAAC;IAED,gFAAgF;IAChF,4DAA4D;IAC5D,gFAAgF;IAEhF;;;;;;OAMG,CACI,kBAAkB,CAAC,IAAwB,EAAE,UAAoB,EAAE,KAAc,EAAA;QACpF,IAAI,IAA8B,CAAC;QAEnC,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;YACxB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,MAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;YACrC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC,MAAM,CAAC;YACJ,IAAI,GAAG,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,iLAAE,cAA2B,CAAZ,AAAa,CAAZ,KAAkB,kLAAG,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,EAAE,KAAK,CAAC,CAAC;QAC3I,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG,CACI,yBAAyB,CAAC,IAAe,EAAE,KAAc,EAAA;QAC5D,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;OAMG,CACI,iBAAiB,CAAC,OAAqB,EAAE,UAAoB,EAAE,KAAc,EAAA;QAChF,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,IAAiC,CAAC;QAEtC,IAAI,OAAO,YAAY,WAAW,IAAI,OAAO,YAAY,UAAU,EAAE,CAAC;YAClE,IAAI,GAAG,OAAO,CAAC;QACnB,CAAC,MAAM,IAAI,OAAO,YAAY,WAAW,EAAE,CAAC;YACxC,IAAI,GAAG,OAAO,CAAC;YACf,QAAQ,GAAG,KAAK,CAAC;QACrB,CAAC,MAAM,CAAC;YACJ,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAClD,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE,CAAC;oBACzB,IAAI,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;oBAChC,MAAM;gBACV,CAAC;YACL,CAAC;YAED,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,IAAI,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;gBAChC,QAAQ,GAAG,KAAK,CAAC;YACrB,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,iLAAE,cAA2B,CAAZ,AAAa,CAAZ,IAAiB,iLAAG,eAAe,AAAY,CAAC,AAAZ,OAAmB,EAAE,KAAK,CAAC,CAAC;QAC1I,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC/B,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG,CACa,wBAAwB,CAAC,WAAuB,EAAE,OAAqB,EAAoB;qBAAlB,iEAAiB,CAAC;QACvG,MAAM,SAAS,GAAG,WAA+B,CAAC;QAElD,IAAI,IAAqB,CAAC;QAC1B,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;YACvB,IAAI,GAAG,OAAO,YAAY,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC/E,CAAC,MAAM,CAAC;YACJ,IAAI,GAAG,OAAO,YAAY,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;OAMG,CACa,yBAAyB,CAAC,YAAwB,EAAE,IAAe,EAAE,UAAmB,EAAE,UAAmB,EAAA;QACzH,MAAM,UAAU,GAAG,YAAgC,CAAC;QACpD,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC3B,UAAU,GAAG,CAAC,CAAC;QACnB,CAAC;QAED,IAAI,IAAqB,CAAC;QAC1B,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;gBACxB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,MAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;gBACrC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,IAAI,CAAC;YAChB,CAAC;YACD,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;gBACxB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,MAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;gBACrC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG,CACI,aAAa,CAAC,IAAwB,EAAE,aAAqB,EAAE,KAAc,EAAA;QAChF,IAAI,IAA8B,CAAC;QAEnC,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;YACxB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,MAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;YACrC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC,MAAM,CAAC;YACJ,IAAI,GAAG,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,aAAa,GAAG,GAAA,MAAS,CAAC,wBAAwB,EAAE,CAAC;YACrD,KAAK,mLAAI,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,CAAC;QACjD,CAAC;QACD,IAAI,aAAa,GAAG,GAAA,MAAS,CAAC,yBAAyB,EAAE,CAAC;YACtD,KAAK,mLAAI,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,CAAC;QACjD,CAAC;QACD,IAAI,aAAa,GAAG,GAAA,MAAS,CAAC,2BAA2B,EAAE,CAAC;YACxD,KAAK,mLAAI,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,CAAC;QACjD,CAAC;QACD,IAAI,aAAa,GAAG,GAAA,MAAS,CAAC,0BAA0B,EAAE,CAAC;YACvD,KAAK,mLAAI,cAA2B,CAAC,AAAb,CAAC,KAAkB,CAAC;QAChD,CAAC;QACD,IAAI,aAAa,GAAG,IAAA,KAAS,CAAC,yBAAyB,EAAE,CAAC;YACtD,KAAK,mLAAI,cAA2B,CAAZ,AAAa,CAAZ,IAAiB,CAAC;QAC/C,CAAC;QACD,IAAI,aAAa,GAAG,IAAA,KAAS,CAAC,2BAA2B,EAAE,CAAC;YACxD,KAAK,mLAAI,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,CAAC;QACjD,CAAC;QACD,IAAI,aAAa,GAAG,IAAA,KAAS,CAAC,4BAA4B,EAAE,CAAC;YACzD,KAAK,mLAAI,cAA2B,CAAZ,AAAa,CAAZ,OAAoB,CAAC;QAClD,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG,CACI,mBAAmB,GAAA;QACtB,4CAA4C;QAC5C,MAAM,2BAA2B,CAAC;IACtC,CAAC;IAED;;OAEG,CACI,4BAA4B,GAAA;QAC/B,4CAA4C;QAC5C,MAAM,2BAA2B,CAAC;IACtC,CAAC;IAED;;OAEG,CACI,wBAAwB,GAAA;IAC3B,eAAe;IACnB,CAAC;IAED;;;;;;OAMG,CACI,WAAW,CACd,aAAwD,EACxD,WAAiC,EACjC,MAAc,EACd,qBAAkE,EAAA;QAElE,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC;QACvC,IAAI,CAAC,6BAA6B,wEAAG,qBAAqB,GAAI,IAAI,CAAC;QACnE,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,aAAa,EAAE,WAAW,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;IACzG,CAAC;IAED;;OAEG,CACI,cAAc,CAAC,MAAkB,EAAA;QACpC,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED,gFAAgF;IAChF,+CAA+C;IAC/C,gFAAgF;IAEhF;;;;;;OAMG,CACI,mBAAmB,CAAC,QAAoB,EAAE,KAAc,EAAA;QAC3D,IAAI,IAAkB,CAAC;QACvB,IAAI,QAAQ,YAAY,KAAK,EAAE,CAAC;YAC5B,IAAI,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC,MAAM,CAAC;YACJ,IAAI,GAAG,QAAQ,CAAC;QACpB,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,iLAAE,cAA2B,CAAC,AAAb,CAAC,MAAmB,kLAAG,cAA2B,CAAC,AAAb,CAAC,MAAmB,EAAE,KAAK,CAAC,CAAC;QAC5I,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;;OAMG,CACI,0BAA0B,CAAC,QAAoB,EAAE,KAAc,EAAA;QAClE,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;;OAOG,CACI,mBAAmB,CAAC,aAAyB,EAAE,QAAoB,EAAE,MAAe,EAAE,KAAc,EAAA;QACvG,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACvB,MAAM,GAAG,CAAC,CAAC;QACf,CAAC;QAED,MAAM,UAAU,GAAG,aAAiC,CAAC;QACrD,IAAI,IAAkB,CAAC;QACvB,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACtB,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;gBACnC,IAAI,GAAG,QAAQ,CAAC;YACpB,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC;YACD,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;QAC5B,CAAC,MAAM,CAAC;YACJ,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;gBACnC,IAAI,GAAG,QAAQ,CAAC;YACpB,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IAED;;;;;OAKG,CACI,qBAAqB,CAAC,MAAkB,EAAE,SAAiB,EAAE,IAAY,EAAA;QAC5E,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,MAA0B,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG,CACI,gBAAgB,GAAA,CAAU,CAAC;IAElC,gFAAgF;IAChF,uCAAuC;IACvC,gFAAgF;IAEhF;;;;;;;;;;;;;;OAcG,CACI,YAAY,CACf,QAAmF,EACnF,wBAA2D,EAC3D,qBAAgD,EAChD,QAAmB,EACnB,OAAgB,EAChB,SAA2B,EAC3B,UAA+C,EAC/C,OAA4D,EAC5D,eAAqB,EAE0B;6BAD/C,cAAc,kCAAA,uBAAA,EAAsB,mBAAtB,GACd,yBAA+C;QAE/C,MAAM,MAAM,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,MAAM,CAAC;QACpJ,MAAM,QAAQ,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,eAAe,IAAI,QAAQ,CAAC,QAAQ,CAAC;QAC9J,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAG,CAAC;QAEhD,MAAM,SAAS,GAAI,wBAAmD,CAAC,UAAU,KAAK,SAAS,CAAC;;QAEhG,IAAI,WAAW,8DAAuC,wBAAyB,CAAC,OAAO,gCAArE,OAAO,AAAkE,EAAE,CAAC;QAE9F,IAAI,aAAa,EAAE,CAAC;YAChB,WAAW,IAAI,IAAI,GAAG,aAAa,CAAC;QACxC,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,GAAG,GAAG,WAAW,CAAC;QACzD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACnD,IAAI,UAAU,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;gBACzC,UAAU,CAAC,cAAc,CAAC,CAAC;YAC/B,CAAC;YACD,cAAc,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO,cAAc,CAAC;QAC1B,CAAC;YAa4B,wBAAyB,kBACzB,wBAAyB;QAbtD,MAAM,MAAM,GAAG,kKAAI,SAAM,CACrB,QAAQ,EACR,wBAAwB,EACxB,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB,EACxC,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,eAAe,EACf,IAAI,uEAC+C,cAAc,+GAAI,cAAc,kFAChC,yBAAyB,qIAAI,yBAAyB,CAC5G,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAErC,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,wBAAwB,CAAC,MAAc,EAAE,IAAY,EAAA;QACzD,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACnD,CAAC;IAEO,qBAAqB,CAAC,MAAc,EAAE,IAAY,EAAE,OAAyB,EAAE,aAAqB,EAAA;QACxG,OAAO,IAAI,CAAC,wBAAwB,CAAC,aAAa,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,IAAI,CAAC,CAAC;IACzG,CAAC;IAEO,cAAc,CAAC,MAAc,EAAE,IAAY,EAAE,OAAyB,EAAA;QAC1E,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,GAAG,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;QAC7D,CAAC,MAAM,CAAC;YACJ,OAAO,GAAG,EAAE,CAAC;QACjB,CAAC;QACD,OAAO,OAAO,GAAG,MAAM,CAAC;IAC5B,CAAC;IAEO,8BAA8B,CAClC,YAAkC,EAClC,cAAoC,EACpC,cAA8B,EAC9B,iCAA0C,EAC1C,mCAA4C,EAAA;QAE5C,IAAI,IAAI,CAAC,SAAS,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC3D,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,YAA2B,EAAE,iCAAiC,CAAC,CAAC;YAChH,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,cAA6B,EAAE,mCAAmC,CAAC,CAAC;QAC1H,CAAC;QAED,OAAO;YACH,WAAW,EAAE;gBACT,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBACpC,KAAK,EAAE,QAAQ;oBACf,IAAI,EAAE,YAAY;iBACrB,CAAC;gBACF,UAAU,EAAE,MAAM;aACrB;YACD,aAAa,EAAE;gBACX,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBACpC,KAAK,EAAE,UAAU;oBACjB,IAAI,EAAE,cAAc;iBACvB,CAAC;gBACF,UAAU,EAAE,MAAM;aACrB;SACJ,CAAC;IACN,CAAC;IAEO,kCAAkC,CAAC,UAAkB,EAAE,YAAoB,EAAE,cAA8B,EAAA;QAC/G,MAAM,iCAAiC,GAAG,UAAU,CAAC,OAAO,CAAC,QAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,iBAAA;QACvF,MAAM,mCAAmC,GAAG,YAAY,CAAC,OAAO,CAAC,QAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,iBAAA;QAE3F,MAAM,YAAY,GAAG,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QAC/H,MAAM,cAAc,GAAG,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;QAEvI,OAAO,IAAI,CAAC,8BAA8B,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,iCAAiC,EAAE,mCAAmC,CAAC,CAAC;IACrK,CAAC;IAEO,+BAA+B,CACnC,UAAkB,EAClB,YAAoB,EACpB,OAAyB,EACzB,cAA8B,EAAA;QAE9B,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE/D,MAAM,iCAAiC,GAAG,UAAU,CAAC,OAAO,CAAC,QAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,iBAAA;QACvF,MAAM,mCAAmC,GAAG,YAAY,CAAC,OAAO,CAAC,QAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,iBAAA;QAE3F,MAAM,aAAa,GAAG,gBAAgB,CAAC;QACvC,MAAM,YAAY,GACd,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC3K,MAAM,cAAc,GAChB,cAAc,KAAA,EAAA,uBAAA,EAAwB,IAChC,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,CAAC,GAC5E,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAEjE,MAAM,OAAO,GAAG,IAAI,CAAC,8BAA8B,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,iCAAiC,EAAE,mCAAmC,CAAC,CAAC;QAE1K,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE9D,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG,CACI,sBAAsB,GAAA;QACzB,4CAA4C;QAC5C,MAAM,yBAAyB,CAAC;IACpC,CAAC;IAED;;OAEG,CACI,mBAAmB,GAAA;QACtB,4CAA4C;QAC5C,MAAM,yBAAyB,CAAC;IACpC,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,IAAY,EAAA;QAChC,MAAM,GAAG,GAAG,wLAAI,qBAAiB,CAAC,IAAI,CAAC,CAAC;QACxC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;QAClB,GAAG,CAAC,WAAW,EAAE,CAAC;QAClB,OAAO,GAAG,CAAC,IAAI,CAAC;IACpB,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,uBAA4D,EAAA;QACrF,OAAO,yLAAI,wBAAqB,CAAC,uBAAyD,EAAE,IAAI,CAAC,CAAC;IACtG,CAAC;IAED;;;OAGG,CACI,qBAAqB,GAAA;QACxB,OAAO,yLAAI,wBAAqB,EAAE,CAAC;IACvC,CAAC;IAED;;;OAGG,CACI,iBAAiB,GAAA;QACpB,OAAO,qLAAI,oBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG,CACH,kEAAkE;IAC3D,KAAK,CAAC,4BAA4B,CACrC,eAAiC,EACjC,gBAAwB,EACxB,kBAA0B,EAC1B,WAAoB,EACpB,mBAA2B,EAC3B,qBAA6B,EAC7B,cAAmB,EACnB,OAAyB,EACzB,0BAA8C,EAC9C,IAAY,EACZ,OAAmB,EAAA;QAEnB,MAAM,aAAa,GAAG,eAAwC,CAAC;QAC/D,MAAM,cAAc,GAAG,aAAa,CAAC,uBAAuB,CAAC,cAAc,CAAC;QAE5E,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,KAAI,CAAC,IAAI,CAAC,6BAA6B,EAAE,CAAC;YAChF,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;qKACzB,SAAM,CAAC,GAAG,CAAC;gBAAC,SAAS;gBAAE,OAAO;aAAC,CAAC,CAAC;oKACjC,UAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;qKAC7B,SAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;qKAC/B,SAAM,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAClE,CAAC;QAED,aAAa,CAAC,OAAO,GAAG;YACpB,QAAQ,EAAE,kBAAkB;YAC5B,MAAM,EAAE,gBAAgB;YACxB,SAAS,EAAE,mBAAmB;YAC9B,WAAW,EAAE,qBAAqB;SACrC,CAAC;QAEF,IAAI,WAAW,EAAE,CAAC;YACd,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,kCAAkC,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,cAAc,CAAC,CAAC;QACzH,CAAC,MAAM,CAAC;YACJ,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;QAC/H,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;;OAKG,CACI,aAAa,CAAC,eAAiC,EAAE,eAAyB,EAAA;QAC7E,MAAM,OAAO,GAAG,IAAI,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,kBAAkB,GAAG,eAAwC,CAAC;QAEpE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC9C,MAAM,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;YACxG,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;gBAClC,SAAS;YACb,CAAC;YAED,OAAO,CAAC,CAAC,CAAC,GAAG,iBAAiB,CAAC;QACnC,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG,CACI,YAAY,CAAC,MAAsC,EAAA;QACtD,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gMAAA,AAAS,EAAC,MAAM,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;YAC7B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;YAC5D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACpD,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;yKAC9B,SAAM,CAAC,IAAI,CACP,yFAAkF,MAAM,CAAC,QAAQ,EAAA,kBAAoD,OAAnC,MAAM,CAAC,IAAI,EAAA,yBAA2G,cAA5E,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAA,2BAAqF,CAAE,aAAtD,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GACnS,EAAE,CACL,CAAC;YACN,CAAC;QACL,CAAC,MAAM,IACH,CAAC,MAAM,CAAC,MAAM,IACb,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,IAClC,MAAM,CAAC,eAAe,KAAK,IAAI,CAAC,uBAAuB,IACvD,MAAM,CAAC,WAAW,KAAK,IAAI,CAAC,mBAAmB,IAC/C,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAC/B,CAAC;YACC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;yKACvD,SAAM,CAAC,GAAG,CAAC;oBAAC,cAAc;oBAAE,MAAM;iBAAC,CAAC,CAAC;gBACrC,4CAA4C;gBAC5C,MAAM,6DAA6D,CAAC;YACxE,CAAC;YACD,OAAO;QACX,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC;YACpC,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,eAAwC,CAAC;YAC/E,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,WAAgC,CAAC;YACnE,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;yKAChC,SAAM,CAAC,GAAG,CAAC;oBAAC,cAAc;oBAAE,MAAM;iBAAC,CAAC,CAAC;gBACrC,4CAA4C;gBAC5C,MAAM,qEAAsE,CAAC;YACjF,CAAC;QACL,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,SAAS,CAAC;QAEvD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEhC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YAC7B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;YACxC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC/E,CAAC;IACL,CAAC;IAED;;OAEG,CACI,cAAc,CAAC,MAAc,EAAA;QAChC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAE1C,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,kBAAkB,EAA2B,CAAC,CAAC;QACtF,CAAC;IACL,CAAC;IAED;;OAEG,CACI,cAAc,GAAA;QACjB,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAE,CAAC;YACvC,MAAM,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAA2B,CAAC;YACxG,IAAI,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAA,CAAE,CAAC;QAE3B,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAEM,sBAAsB,CAAC,eAAiC,EAAA;QAC3D,MAAM,qBAAqB,GAAG,eAAwC,CAAC;QACvE,IAAI,qBAAqB,EAAE,CAAC;2LACxB,sBAAmB,AAAnB,EAAoB,qBAAqB,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAED,gFAAgF;IAChF,wCAAwC;IACxC,gFAAgF;IAEhF;;;OAGG,CACH,IAAW,eAAe,GAAA;QACtB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,cAAA,EAAgB,CACT,sBAAsB,GAAA;QACzB,OAAO,yLAAI,wBAAqB,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG,CACI,eAAe,CAAC,OAAwB,EAAA;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG,CACI,iCAAiC,GAAA;QACpC,OAAO,SAAS,CAAC,kBAAkB,CAAC;IACxC,CAAC;IAEM,+BAA+B,CAAC,OAAwB,EAAE,kBAA0B,EAAA;QACvF,OAAO,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IACrD,CAAC;IAED;;;;;;;;OAQG,CACI,sBAAsB,CACzB,IAAiB,EACjB,OAAiD,EAEX;YADtC,uBAAuB,oEAAG,IAAI,WAC9B,MAAM,0CAAA,iCAAA,EAAgC,mBAAhC;QAEN,MAAM,WAAW,GAAmC,CAAA,CAAE,CAAC;QAEvD,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACvD,WAAW,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YACtD,WAAW,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;YAClD,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,EAAA,IAAA,mBAAyB,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;YACnG,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,EAAA,YAAA,gBAA8B,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;YAChI,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,EAAA,MAAA,UAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;;YAClG,WAAW,CAAC,OAAO,+BAAW,OAAO,4CAAf,OAAO,YAAY,CAAC,CAAC;;YAC3C,WAAW,CAAC,aAAa,qCAAW,aAAa,kDAArB,OAAO,kBAAkB,CAAC,CAAC;;YACvD,WAAW,CAAC,aAAa,GAAG,OAAO,2BAAC,aAAa,2EAAI,KAAK,CAAC;YAC3D,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QACtC,CAAC,MAAM,CAAC;YACJ,WAAW,CAAC,eAAe,GAAG,OAAO,CAAC;YACtC,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;YACvD,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,8BAA8B,CAAC;YACpE,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;YAClD,WAAW,CAAC,OAAO,GAAG,CAAC,CAAC;YACxB,WAAW,CAAC,aAAa,GAAG,CAAC,CAAC;YAC9B,WAAW,CAAC,aAAa,GAAG,KAAK,CAAC;QACtC,CAAC;QAED,IAAI,WAAW,CAAC,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,UAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,EAAA,wBAA2B,EAAE,CAAC;YAC9F,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QACtE,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,eAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAA,6BAA+B,EAAE,CAAC;YAC9G,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QACtE,CAAC;QACD,IAAI,WAAW,CAAC,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,UAAiB,EAAA,EAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YAC/E,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;qKACvD,SAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;QAC9F,CAAC;QAED,MAAM,OAAO,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;;QAElD,MAAM,KAAK,uBAA8D,KAAK,uCAAX,IAAK,UAAkB,IAAI,CAAC;;QAC/F,MAAM,MAAM,wBAA8D,MAAM,wCAAZ,IAAK,WAAmB,IAAI,CAAC;;QACjG,MAAM,KAAK,uBAA8E,KAAK,uCAAX,IAAK,UAAU,CAAC,CAAC;;QACpG,MAAM,MAAM,wBAA8E,MAAM,wCAAZ,IAAK,WAAW,CAAC,CAAC;QAEtG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,KAAK,IAAI,MAAM,CAAC;QAChC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;QACtC,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,WAAW,CAAC,eAAe,CAAC;QACxD,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;QAChD,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QAChC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACpC,OAAO,CAAC,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;QAC/B,OAAO,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;QACzB,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;QAC3D,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;QAC3D,OAAO,CAAC,cAAc,GAAG,WAAW,CAAC,aAAa,CAAC;QACnD,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QAElC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC3B,MAAM,iBAAiB,GAAG,CAAC,WAAW,CAAC,eAAe,IAAI,WAAW,CAAC,aAAa,CAAC;YAEpF,IAAI,iBAAiB,EAAE,CAAC;gBACpB,6EAA6E;gBAC7E,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;YACnC,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC;YAEvH,IAAI,iBAAiB,EAAE,CAAC;gBACpB,oFAAoF;gBACpF,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;YACpC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG,CACI,aAAa,CAChB,GAAqB,EACrB,QAAiB,EACjB,OAAgB,EAChB,KAA2B,EAS3B,OAAmB,EACnB,aAAsB,EACtB,aAAuB;2BAVvB,iEAAuB,GAAA,SAAA,MAAS,CAAC,0DAAA,MAAA,UAAA,iEAAA,MAAA,SAAA,MAA8B,EAC/D,yDAAA,MAAA,WAAA,EAAuD,IAAI,EAC3D,yDAAA,MAAA,SAAA,CAA+D,IAAI,EACnE,0DAAA,MAAA,kBAAA,CAAmG,IAAI,EACvG,6DAAsC,IAAI,EAC1C,SAA2B,kDAAA,GAAI,EAC/B,2DAAA,QAAoC,IAAI,EACxC,kDAAA,OAAiB,EACjB;QAIA,OAAO,IAAI,CAAC,kBAAkB,CAC1B,GAAG,EACH,QAAQ,EACR,OAAO,EACP,KAAK,EACL,YAAY,EACZ,MAAM,EACN,OAAO,EACP,CACI,OAAwB,EACxB,SAAiB,EACjB,KAA2B,EAC3B,GAAuE,EACvE,OAAgB,EAChB,QAAiB,EACjB,YAAqB,EACrB,eAOY,EACd,EAAE;;YACA,MAAM,WAAW,GAAG,GAAsD,CAAC,CAAC,kDAAkD;YAE9H,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC;YACtC,OAAO,CAAC,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC;YACxC,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;YAClC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;YACpC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,uCAAC,MAAM,GAAI,SAAS,CAAC,kBAAkB,CAAC,CAAC;YACnG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC;YACxF,OAAO,CAAC,cAAc,wDAAG,aAAa,GAAI,CAAC,CAAC;YAE5C,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,AAAE,CAAC,CAAC,CAAC;YAE1F,IAAI,GAAC,OAAO,6BAAC,gBAAgB,wFAAE,kBAAkB,GAAE,CAAC;gBAChD,0GAA0G;gBAC1G,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;gBAE3J,uLAAI,sBAAmB,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC;oBACjD,IAAI,CAAC,cAAc,CAAC,aAAa,CAC7B,WAAW,EACX,OAAO,EACP,WAAW,CAAC,KAAK,EACjB,WAAW,CAAC,MAAM,EAClB,OAAO,CAAC,KAAK,EACb,iBAAiB,CAAC,MAAM,EACxB,CAAC,EACD,CAAC,EACD,OAAO,EACP,KAAK,EACL,CAAC,EACD,CAAC,CACJ,CAAC;oBACF,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;wBAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;oBACxD,CAAC;gBACL,CAAC;YACL,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YACxD,CAAC;YAED,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;YAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAEvB,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QACvC,CAAC,EACD,GAAG,CAAG,CAAD,IAAM,EACX,MAAM,EACN,QAAQ,EACR,MAAM,EACN,eAAe,EACf,QAAQ,EACR,aAAa,EACb,aAAa,CAChB,CAAC;IACN,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,OAAmB,EAAA;QACxC,MAAM,eAAe,GAAG,wLAAI,yBAAqB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACjE,MAAM,eAAe,GAAG,sLAAI,mBAAe,CAAC,IAAI,EAAA,EAAA,iCAAA,KAAiC,IAAI,CAAC,CAAC;QACvF,eAAe,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACnD,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;QAC/B,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED,uDAAuD;IACvD;;;OAGG,CACI,gBAAgB,GAAA;QACnB,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;IACzF,CAAC;IAED;;OAEG,CACI,iBAAiB,CAAC,aAAsB,EAAE,SAAkB,EAAA;QAC/D,OAAO,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;IAC1D,CAAC;IAED;;OAEG,CACI,YAAY,CAAC,MAAe,EAAA,CAAG,CAAC;IAEvC;;;;;OAKG,CACI,yBAAyB,CAAC,YAAoB,EAAE,OAAwB,EAAkC;8BAAhC,iEAA2B,KAAK;QAC7G,IAAI,eAAe,EAAE,CAAC;YAClB,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACxC,CAAC;IAED;;;;;;OAMG,CACI,yBAAyB,CAAC,OAAwB,EAAE,KAAuB,EAAgE;oBAA9D,iEAA0B,IAAI,UAAE,iEAA0B,IAAI;QAC9I,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;QACjC,CAAC;QACD,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACxD,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACI,uBAAuB,CAAC,OAAwB,EAAE,KAAa,EAAE,MAAc,EAAmB;oBAAjB,iEAAgB,CAAC;QACrG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC5B,iGAAiG;YACjG,OAAO;QACX,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;YAClF,OAAO;QACX,CAAC;QAED,MAAM,gBAAgB,GAAI,OAAO,CAAC,gBAA0C,CAAC,uBAAuB,CAAC;QAErG,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,sIAAsI;QAE1K,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;IAC5G,CAAC;IAED;;OAEG,CACI,mBAAmB,CAAC,IAAY,EAAE,OAAoD,EAAE,QAAiB,EAAA;QAC5G,QAAQ,8CAAG,QAAQ,GAAI,IAAI,CAAC;QAC5B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAyC,CAAC;YAC5F,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YAEnG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAEvD,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,eAAe,EAAE,CAAC;gBACvD,MAAM,WAAW,GAAG,QAAQ,GAAG,QAAS,CAAC,iBAAiB,CAAC;gBAC3D,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,WAAW,EAAE,OAA0B,CAAC,CAAC,CAAC,mGAAmG;YACzL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG,CACa,4BAA4B,CACxC,OAAe,EACf,KAAsB,EACtB,QAAgB,EAChB,SAAiB,EAKgB;qBAJjC,iEAAyE,IAAI,YAC7E,iEAAiE,IAAI,EACrE,MAAe,mEACf,iEAAuB,IAAI,sBAC3B,iEAA6B,IAAI;QAEjC,MAAM,QAAQ,GAAG,CAAC,QAAa,EAAE,EAAE;YAC/B,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACZ,IAAI,MAAM,EAAE,CAAC;oBACT,MAAM,CAAC,IAAI,CAAC,CAAC;gBACjB,CAAC;gBACD,OAAO;YACX,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,OAA0B,CAAC;YACpD,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACrB,OAAO,CAAC,oBAAoB,GAAG,IAAI,6LAAmB,EAAE,CAAC;YAC7D,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3C,OAAO,CAAC,oBAAoB,GAAG,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC;YACrE,CAAC;YACD,OAAO,CAAC,OAAO,GAAA,EAAA,yCAAA,EAAwC,CAAC;YAExD,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC;QACL,CAAC,CAAC;QAEF,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;IACnJ,CAAC;IAED;;;;;;OAMG,CACI,UAAU,CAAC,OAAe,EAAE,MAAsC,EAAE,OAA8B,EAAE,IAAY,EAAA;QACnH,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;OAMG,CACI,eAAe,CAAC,OAAe,EAAE,MAAsC,EAAE,QAAuB,EAAE,IAAY,EAAA;QACjH,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,CAAC;QACtF,CAAC;IACL,CAAC;IAED;;OAEG,CACa,WAAW,CACvB,OAAe,EACf,OAA8B,EAKb;YAJjB,6DAA6D;+BAC7D,oBAAoB,6CAAG,KAAK,wBAC5B,mBAAmB,8CAAG,KAAK,SAC3B,IAAI,6DAAG,EAAE,EACT,QAAiB;QAEjB,qEAAqE;QACrE,6FAA6F;QAC7F,+FAA+F;QAC/F,kFAAkF;QAClF,QAAQ,8CAAG,QAAQ,GAAI,IAAI,CAAC;QAC5B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACpD,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,QAAQ;YACR,IAAmB,OAAQ,CAAC,KAAK,EAAE,CAAC;gBACjB,OAAQ,CAAC,MAAM,EAAE,CAAC;YACrC,CAAC,MAAM,IAAI,OAAO,CAAC,cAAc,KAAK,GAAA,MAAS,CAAC,wBAAwB,EAAE,CAAC;gBACvE,gBAAgB;gBAChB,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,eAAe,GAA8B,IAAI,CAAC;YACtD,IAAI,mBAAmB,EAAE,CAAC;gBACtB,eAAe,GAAyB,OAAQ,CAAC,mBAAoB,CAAC;YAC1E,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC3B,eAAe,GAAoB,OAAO,CAAC,kBAAkB,EAAE,CAAC;YACpE,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACxB,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC5C,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACtB,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;YAC1C,CAAC,MAAM,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC3B,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAC/C,CAAC,MAAM,CAAC;gBACJ,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;YACxC,CAAC;YAED,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;gBAClD,sFAAsF;gBACtF,IAAI,eAAe,CAAC,MAAM,IAAI,eAAe,CAAC,sBAAsB,KAAK,OAAO,CAAC,eAAe,EAAE,CAAC;oBAC/F,eAAe,CAAC,sBAAsB,GAAG,OAAO,CAAC,eAAe,CAAC;oBAEjE,MAAM,eAAe,GACjB,OAAO,CAAC,eAAe,KAAK,KAAA,IAAS,CAAC,GAAA,eAAkB,IAAI,CAAA,IACtD,EAD6D,CAAC,CAE9D,KADS,CAAC,GACD,CAAC,IAFmE,KAAK,SAAS,CAAC,CAC1D,KACC,CAAC,YAF2E;oBAGzH,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC;oBAChC,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC;gBACpC,CAAC;gBAED,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC;oBACvB,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBACjD,CAAC;gBAED,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,eAAe,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;YACrF,CAAC;YAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,eAAe,EAAE,QAAQ,CAAC,CAAC;QAC9D,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC7B,CAAC;gBACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;6KAC/E,SAAM,CAAC,GAAG,CAAC;wBAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,4DAA4D;wBAAE,OAAO;qBAAC,CAAC,CAAC;gBAC3H,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,oBAAoB,CAAC,MAAc,EAAE,eAAgC,EAAE,yBAAiC,EAAA;QAC3G,IAAI,eAAe,CAAC,gCAAgC,KAAK,yBAAyB,EAAE,CAAC;YACjF,eAAe,CAAC,gCAAgC,GAAG,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACrH,CAAC;IACL,CAAC;IAED;;OAEG,CACI,YAAY,CAAC,OAAe,EAAE,OAAkC,EAAE,IAAY,EAAA;QACjF,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG,CACI,eAAe,CAAC,OAAwB,EAAA;QAC3C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;;;;;;OAWG,CACI,iBAAiB,CACpB,OAAwB,EACxB,SAA0B,EAC1B,OAAe,EACf,OAAe,EACf,KAAa,EACb,MAAc,EAGS;wBAFvB,iEAAoB,CAAC,QACrB,iEAAc,CAAC,oBACf,eAAe,kDAAG,KAAK;;QAEvB,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAE1E,IAAI,uCAAS,gBAAgB,8DAAxB,OAAO,mBAAmB,kBAAkB,GAAE,CAAC;YAChD,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;QACxF,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;QAE1F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAEnK,IAAI,eAAe,EAAE,CAAC;YAClB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;IACL,CAAC;IAED;;OAEG,CACI,sCAAsC,CACzC,OAAwB,EACxB,cAAsB,EACtB,KAAa,EACb,MAAc,EACd,SAA0B,EAEX;wBADf,iEAAoB,CAAC,EACrB,uEAAc,CAAC;;QAEf,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAE1E,IAAI,uCAAS,gBAAgB,8DAAxB,OAAO,mBAAmB,kBAAkB,GAAE,CAAC;YAChD,OAAO,CAAC,MAAM,GAAG,cAAc,CAAC;YAChC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvG,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;QAE1F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACjJ,CAAC;IAED;;OAEG,CACI,4BAA4B,CAC/B,OAAwB,EACxB,SAA0B,EAIM;wBAHhC,iEAAoB,CAAC,QACrB,iEAAc,CAAC,EACf,qBAA8B,4EAC9B,wBAAwB,yCAAG,KAAK;;QAEhC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QAEvE,MAAM,KAAK,GAAG,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACrG,MAAM,MAAM,GAAG,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAExG,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAE1E,IAAI,uCAAS,gBAAgB,8DAAxB,OAAO,mBAAmB,kBAAkB,GAAE,CAAC;YAChD,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACvG,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC;QAE1F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3J,CAAC;IAED;;OAEG,CACI,+BAA+B,CAAC,OAAwB,EAAE,SAA0B,EAAwC;wBAAtC,iEAAoB,CAAC,QAAE,iEAAc,CAAC;QAC/H,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG,CACI,qBAAqB,CAAC,OAAwB,EAAE,KAAqC,EAAwC;wBAAtC,iEAAoB,CAAC,QAAE,iEAAc,CAAC;YAG3H,OAAO;QAFZ,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAE1E,IAAI,uCAAS,gBAAgB,wFAAE,kBAAkB,GAAE,CAAC;YAChD,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;QACxF,CAAC;QAED,IAAI,KAAK,YAAY,gBAAgB,EAAE,CAAC;YACpC,4CAA4C;YAC5C,MAAM,yEAAyE,CAAC;QACpF,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,uEAAuE;QAE7F,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAEtD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7J,CAAC;IAED;;;;;;;;;;OAUG,CACH,2GAA2G;IACpG,UAAU,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAA2E;wBAAzE,SAAS,wDAAG,IAAI,kBAAE,aAAa,oDAAG,IAAI,SAAE,iEAA6B,IAAI;QAC5I,MAAM,iBAAiB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAC9D,MAAM,eAAe,GAAG,iBAAiB,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,0EAA0E;YAC1E,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD,MAAM,UAAU,GAAG,eAAe,CAAC,kBAAkB,CAAC;QACtD,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC;QAChD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,kIAAkI;YAClI,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;QACD,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IACzH,CAAC;IAED,gFAAgF;IAChF,gDAAgD;IAChD,gFAAgF;IAExE,WAAW,GAAA;QACf,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;QAChD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,IAAI,CAAC,CAAC;IAC3E,CAAC;IAGD;;;OAGG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG,CACa,UAAU,GAAA;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,KAAK,CAAC,UAAU,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG,CACa,QAAQ,GAAA;QACpB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;QAEnC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnD,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE,CAAC;QAC9C,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC7B,CAAC;gBACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBAC/E,MAAM,IAAI,GAAkB,EAAE,CAAC;oBAC/B,IAAK,MAAM,IAAI,yKAAI,gBAAa,CAAC,mBAAmB,CAAE,CAAC;wBACnD,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,wKAAG,gBAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;oBACpE,CAAC;6KACD,SAAM,CAAC,GAAG,CAAC;wBAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,mBAAmB;wBAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;qBAAC,CAAC,CAAC;gBAC1F,CAAC;YACL,CAAC;iLACD,gBAAa,CAAC,mBAAmB,GAAG,CAAA,CAAE,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC1E,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;QAClF,IAAI,CAAC,iBAAiB,CAAC,8BAA8B,GAAG,IAAI,CAAC,SAAS,CAAC,8BAA8B,CAAC;QACtG,IAAI,CAAC,iBAAiB,CAAC,2BAA2B,GAAG,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC;QAChG,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,SAAS,CAAC,oBAAoB,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,SAAS,CAAC,8BAA8B,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,SAAS,CAAC,2BAA2B,GAAG,CAAC,CAAC;QAE/C,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;QACrC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAEjC,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEtC,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;yKACtD,SAAM,CAAC,GAAG,CAAC;oBAAC,YAAY,GAAI,IAAY,CAAC,MAAM,GAAG,QAAQ;oBAAE,qBAAqB;iBAAC,CAAC,CAAC;YACxF,CAAC;YACD,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACrD,IAAY,CAAC,MAAM,EAAE,CAAC;gBACvB,IAAK,IAAY,CAAC,MAAM,KAAK,IAAI,CAAC,uBAAuB,EAAE,CAAC;6KACxD,SAAM,CAAC,GAAG,CAAC;wBAAC,YAAY,GAAI,IAAY,CAAC,MAAM,GAAG,UAAU;wBAAE,qBAAqB;qBAAC,CAAC,CAAC;gBAC1F,CAAC;YACL,CAAC;QACL,CAAC;QAED,KAAK,CAAC,QAAQ,EAAE,CAAC;IACrB,CAAC;IAED,iCAAA,EAAmC,CAC5B,iBAAiB,GAAA;QACpB,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG,CACI,gBAAgB,GAAA;QACnB,uIAAuI;QACvI,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QACvD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QAEvD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAEhD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACvF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAEvF,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAErD,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE3D,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAED,cAAA,EAAgB,CACT,uCAAuC,GAAA;QAC1C,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAC;IACzC,CAAC;IAED,gFAAgF;IAChF,2CAA2C;IAC3C,gFAAgF;IAExE,4BAA4B,CAChC,mBAAwC,EACxC,cAAuB,EACvB,UAAiC,EACjC,UAAmB,EACnB,YAAqB,EAAA;0CAiIE,uBA8BvB;QA7JA,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,MAAM,SAAS,GAAG,mBAAgD,CAAC;QAEnE,MAAM,mBAAmB,GAAG,SAAS,CAAC,oBAAoB,CAAC;QAC3D,MAAM,sBAAsB,GAAG,mBAAmB,2EAAE,gBAAmD,CAAC;QACxG,MAAM,sBAAsB,mFAAG,sBAAsB,CAAE,kBAA0C,CAAC;QAClG,MAAM,0BAA0B,mFAAG,sBAAsB,CAAE,cAAc,CAAC,CAAC,CAAC,CAAC;QAE7E,MAAM,gBAAgB,GAAG,sBAAsB,iFAAE,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,CAAC;QACvH,MAAM,oBAAoB,2FAAG,0BAA0B,CAAE,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,CAAC;QAC/H,MAAM,sBAAsB,GAAG,sBAAsB,CAAC,CAAC,oLAAC,sBAAmB,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEpI,MAAM,gBAAgB,GAA4C,EAAE,CAAC;QAErE,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAC5C,CAAC;QAED,MAAM,sBAAsB,GAAG,UAAU,CAAC;QAC1C,IAAI,UAAU,EAAE,CAAC;YACb,sBAAsB,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC;YAC9C,sBAAsB,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC;YAC9C,sBAAsB,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC;YAC9C,sBAAsB,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC;QAClD,CAAC;QAED,MAAM,cAAc,GAAG,cAAc,IAAI,UAAU,CAAC;QACpD,MAAM,cAAc,GAAG,cAAc,IAAI,UAAU,CAAC;QACpD,MAAM,gBAAgB,GAAG,cAAc,IAAI,YAAY,CAAC;QAExD,IAAI,SAAS,CAAC,YAAY,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YAC9C,uBAAuB;YACvB,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7D,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,mBAAmB,CAAC;YACzD,CAAC;YACD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;gBACnD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,+HAA+H;gBACtK,MAAM,UAAU,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;gBAC1C,MAAM,aAAa,0BAAG,UAAU,kCAAE,gBAAmD,CAAC;gBACtF,MAAM,aAAa,iEAAG,aAAa,CAAE,kBAAkB,CAAC;gBACxD,IAAI,aAAa,IAAI,aAAa,EAAE,CAAC;;oBACjC,MAAM,cAAc,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;oBACtD,MAAM,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;oBAEpE,MAAM,cAAc,GAAG;wBACnB,GAAG,IAAI,CAAC,qBAAqB,CAAC,6BAA8B;wBAC5D,SAAS,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA,KAAA,4CAAA,GAA0C,CAAC,EAAA,KAAA,4CAAA,EAAyC;wBAChH,MAAM,EAAE,aAAa,CAAC,MAAM;wBAC5B,cAAc;qBACjB,CAAC;oBACF,MAAM,kBAAkB,GAAG;wBACvB,GAAG,IAAI,CAAC,qBAAqB,CAAC,6BAA8B;wBAC5D,SAAS,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA,KAAA,4CAAA,GAA0C,CAAC,EAAA,KAAA,4CAAA,EAAyC;wBAChH,MAAM,EAAE,aAAa,CAAC,MAAM;wBAC5B,cAAc,EAAE,CAAC;qBACpB,CAAC;oBACF,MAAM,WAAW,GAAG,UAAU,CAAC,IAAI,KAAK,KAAA,IAAS,CAAC,MAAA,IAAA,KAAA,aAA4B,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,CAAC,0BAA0B,CAAC;oBAE3I,MAAM,gBAAgB,GAAG,aAAa,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;oBAClE,MAAM,oBAAoB,mEAAG,cAAc,CAAE,UAAU,CAAC,kBAAkB,CAAC,CAAC;wBAKzC,SAAS;oBAH5C,gBAAgB,CAAC,IAAI,CAAC;wBAClB,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,gBAAgB;wBACpE,aAAa,EAAE,cAAc,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;wBAC5D,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,kEAAW,YAAY,mFAAE,CAAC,CAAC,CAAC,iFAAI,CAAC,CAAC,CAAC,CAAC,AAAC,SAAS;wBAC5E,UAAU,EAAE,KAAK,KAAK,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,AAAC,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,SAAS;wBAC3G,MAAM,EAAE,KAAK,KAAK,CAAC,IAAI,cAAc,CAAC,CAAC,CAAA,QAAA,gCAAA,GAA8B,CAAC,EAAA,OAAA,+BAAA,EAA4B;wBAClG,OAAO,EAAA,QAAA,iCAAA,EAA+B;qBACzC,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;YACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,QAAS,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YACnF,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtE,CAAC,MAAM,CAAC;YACJ,uBAAuB;YACvB,MAAM,eAAe,GAAG,SAAS,CAAC,OAAO,CAAC;YAC1C,IAAI,eAAe,EAAE,CAAC;gBAClB,MAAM,UAAU,GAAG,eAAe,CAAC,gBAAyC,CAAC;gBAC7E,MAAM,UAAU,GAAG,UAAU,CAAC,kBAAmB,CAAC;gBAElD,IAAI,UAAU,GAAuB,SAAS,CAAC;gBAE/C,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;oBACjB,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,cAAc,CAAC;oBACtF,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,cAAc,GAAG,CAAC,CAAC;gBACjF,CAAC;gBAED,MAAM,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBACpD,MAAM,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,CAAC;gBAC1G,MAAM,oBAAoB,mEAAG,cAAc,CAAE,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,6BAA8B,CAAC,CAAC;gBACnH,MAAM,WAAW,GAAG,eAAe,CAAC,IAAI,KAAK,KAAA,IAAS,CAAC,WAAA,IAAA,KAAA,QAA4B,IAAI,eAAe,CAAC,IAAI,KAAK,SAAS,CAAC,0BAA0B,CAAC;gBAErJ,gBAAgB,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,gBAAgB;oBACpE,aAAa,EAAE,cAAc,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;oBAC5D,UAAU;oBACV,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,AAAC,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,SAAS;oBAC5F,MAAM,EAAE,cAAc,CAAC,CAAC,CAAA,QAAA,gCAAA,GAA8B,CAAC,EAAA,OAAA,+BAAA,EAA4B;oBACnF,OAAO,EAAA,QAAA,iCAAA,EAA+B;iBACzC,CAAC,CAAC;YACP,CAAC,MAAM,CAAC;gBACJ,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;8CAEG,EAAC,eAAe,EAAE,mFAAtB,OAAuB,oBAAoB,GAAG,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,mBAAmB,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;QAE5H,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,GAAG;YAC9C,KAAK,EAAE,mDAAqB,KAAK,sDAAzB,mBAAmB,UAAU,KAAK,CAAC,GAAG,eAAe;YAC7D,gBAAgB;YAChB,sBAAsB,EAClB,mBAAmB,IAAI,sBAAsB,GACvC;gBACI,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,gBAAiB;gBACrE,eAAe,EAAE,cAAc,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,AAAC,SAAS;gBACjI,WAAW,EAAE,cAAc,CAAC,CAAC,CAAA,QAAA,gCAAA,GAA8B,CAAC,EAAA,OAAA,+BAAA,EAA4B;gBACxF,YAAY,EAAA,QAAA,iCAAA,EAA+B;gBAC3C,iBAAiB,EAAE,SAAS,CAAC,+BAA+B,IAAI,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS;gBACtH,aAAa,EAAE,CAAC,sBAAsB,GAChC,SAAS,GACT,SAAS,CAAC,+BAA+B,IAAI,gBAAgB,GAC5D,QAAA,gCAAA,MACA,OAAA,+BAAA,EAA4B;gBACnC,cAAc,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAA,QAAA,iCAAA,EAA8B;aACtF,GACD,SAAS;YACnB,iBAAiB,gCAAM,CAAC,eAAe,gFAAE,UAAU,CAAC,CAAC,EAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;SAClG,CAAC;QACF,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACtG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;QAE/G,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/E,MAAM,eAAe,GAAG,SAAS,CAAC,OAAQ,CAAC;yKAC3C,SAAM,CAAC,GAAG,CAAC;oBACP,SAAS,GACJ,IAAY,CAAC,MAAM,GACpB,yCAAyC,GACzC,mBAAmB,CAAC,KAAK,GACzB,6BAA6B,GAC7B,eAAe,CAAC,QAAQ,GACxB,UAAU,GACV,eAAe,CAAC,KAAK,GACrB,WAAW,GACX,eAAe,CAAC,MAAM,GACtB,mBAAmB,GACnB,cAAc;oBAClB,uBAAuB;oBACvB,IAAI,CAAC,qBAAqB,CAAC,oBAAoB;iBAClD,CAAC,CAAC;YACP,CAAC;QACL,CAAC;0DAEG,EAAC,0BAA0B,EAAE,EAAE,CAAC;QAEpC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,IAAI,CAAC,sBAAsB,IAAI,oLAAC,sBAAmB,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE,CAAC;YAClG,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG,KAAK,CAAC;QAC/C,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,cAAuB,EAAE,UAAkC,EAAE,UAAoB,EAAE,YAAsB,EAAA;;QAClI,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAC5C,CAAC;QAED,MAAM,cAAc,GAAG,cAAc,IAAI,UAAU,CAAC;QACpD,MAAM,cAAc,GAAG,cAAc,IAAI,UAAU,CAAC;QACpD,MAAM,gBAAgB,GAAG,cAAc,IAAI,YAAY,CAAC;QAExD,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAE,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;QAC5H,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAE,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC,CAAA,QAAA,gCAAA,GAA8B,CAAC,EAAA,OAAA,+BAAA,EAA4B,CAAC;QAC5J,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,sBAAuB,CAAC,eAAe,GAAG,cAAc,GACpG,IAAI,CAAC,qBAAqB,GACtB,IAAI,CAAC,uBAAuB,GAC5B,IAAI,CAAC,gBAAgB,GACzB,SAAS,CAAC;QAChB,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,sBAAuB,CAAC,WAAW,GAAG,cAAc,CAAC,CAAC,CAAA,QAAA,gCAAA,GAA8B,CAAC,EAAA,OAAA,+BAAA,EAA4B,CAAC;QACpK,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,sBAAuB,CAAC,iBAAiB,GAAG,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC;QACrJ,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,sBAAuB,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,eAAe,GACzG,SAAS,GACT,gBAAgB,GACf,QAAA,gCAAA,MACA,OAAA,+BAAA,EAA4B,CAAC;QACpC,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,iBAAiB,iCAAO,CAAC,eAAe,0DAApB,sBAAsB,UAAU,CAAC,CAAC,EAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;QAEnJ,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QAC3D,IAAI,CAAC,sBAAsB,CAAC,0BAA0B,CAAC,CAAC,CAAE,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAEjF,0BAA0B;QAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YAC1B,mCAAmC,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;YACrE,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAE,CAAC,aAAa,GAAG,gBAAgB,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC;QAC5J,CAAC,MAAM,CAAC;YACJ,uBAAuB,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;YACzD,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAE,CAAC,IAAI,GAAG,gBAAgB,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC;QACvI,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;yKAC/E,SAAM,CAAC,GAAG,CAAC;oBACP,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,qCAAqC,GAAI,IAAI,CAAC,mBAA2B,CAAC,KAAK;oBAClH,UAAU,GAAI,IAAI,CAAC,mBAA2B,CAAC,MAAM,GAAG,mBAAmB,GAAG,cAAc;oBAC5F,uBAAuB;oBACvB,IAAI,CAAC,sBAAsB,CAAC,oBAAoB;iBACnD,CAAC,CAAC;YACP,CAAC;QACL,CAAC;8CAEG,EAAC,eAAe,EAAE,mFAAtB,OAAuB,WAAW,EAAE,CAAC,CAAC,CAAC;QAEvC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACxG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,oBAAqB,CAAC,CAAC;QAEjH,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;0DAE9C,EAAC,0BAA0B,EAAE,EAAE,CAAC,sGAApC;QAEA,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG,KAAK,CAAC;QAC/C,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG,CACI,eAAe,CAClB,OAA4B,EAMnB;wBALT,iEAAoB,CAAC,EACrB,aAAsB,iDACtB,cAAuB,iDACvB,uBAAiC,4DACjC,QAAQ,yDAAG,CAAC,UACZ,KAAK,4DAAG,CAAC;;QAET,MAAM,eAAe,+BAAW,OAAO,qDAAf,OAAO,UAAU,gBAAmD,CAAC;QAE7F,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACtD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QAEpC,MAAM,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC;QAE3E,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;QAC3E,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,GAAG,mBAAmB,CAAC,CAAC,oLAAC,sBAAmB,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE7J,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACxD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAEjD,IAAI,CAAC,qBAAqB,CAAC,6BAA6B,GAAG;YACvD,MAAM,EAAE,IAAI,CAAC,YAAgC;YAC7C,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA,KAAA,4CAAA,GAA0C,CAAC,EAAA,KAAA,4CAAA,EAAyC;YAC7G,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK;YAC9D,YAAY,EAAE,QAAQ;YACtB,eAAe,EAAE,CAAC;YAClB,MAAM,EAAA,MAAA,qCAAA,EAAmC;SAC5C,CAAC;QAEF,IAAI,CAAC,qBAAqB,CAAC,6BAA6B,GAAG;YACvD,MAAM,EAAE,IAAI,CAAC,mBAAoB;YACjC,SAAS,EAAE,mBAAmB,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAA,KAAA,4CAAA,GAA0C,CAAC,EAAA,KAAA,4CAAA,EAAyC;YAChJ,aAAa,EAAE,CAAC;YAChB,cAAc,EAAE,mBAAmB,CAAC,CAAC,CAAC,AAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,AAAC,CAAC;YACtG,YAAY,EAAE,CAAC;YACf,eAAe,EAAE,CAAC;YAClB,MAAM,EAAA,MAAA,qCAAA,EAAmC;SAC5C,CAAC;QAEF,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBAOvE,OAAO;yKANf,SAAM,CAAC,GAAG,CAAC;oBACP,SAAS,GACJ,IAAY,CAAC,MAAM,GACpB,gCAAgC,GAChC,OAAO,CAAC,KAAK,GACb,6BAA6B,iCACrB,OAAO,wEAAE,QAAQ,IACzB,SAAS,GACT,SAAS,GACT,aAAa,GACb,QAAQ,GACR,UAAU,GACV,KAAK;oBACT,gCAAgC;oBAChC,IAAI,CAAC,qBAAqB,CAAC,6BAA6B;oBACxD,gCAAgC;oBAChC,IAAI,CAAC,qBAAqB,CAAC,6BAA6B;iBAC3D,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,qJAAqJ;QAErJ,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAC1E,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC9B,IAAI,QAAQ,EAAE,CAAC;oBACX,aAAa,GAAG,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBAC1D,CAAC;YACL,CAAC;YACD,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;gBAChC,IAAI,QAAQ,EAAE,CAAC;oBACX,cAAc,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBAC5D,CAAC;YACL,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,OAA4B,EAA6D;qCAA3D,sBAAsB,2CAAG,KAAK,EAAE,cAA2B;QAC9G,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAE1C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC,sGAAsG;QAExI,IAAI,cAAc,EAAE,CAAC;YACjB,cAAc,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QAEpC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC1B,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClB,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAK,IAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAY,CAAC,MAAM,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,CAAE,IAAY,CAAC,MAAM,IAAK,IAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;;yKAC/E,SAAM,CAAC,GAAG,CAAC,SAAS,GAAI,IAAY,CAAC,MAAM,GAAG,kCAAkC,GAAG,OAAO,CAAC,KAAK,GAAG,6BAA6B,8BAAU,OAAO,qDAAf,OAAO,UAAU,QAAQ,CAAC,CAAC;YACjK,CAAC;QACL,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrC,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACtE,CAAC;IAED;;;OAGG,CACI,0BAA0B,CAAC,OAA4B,EAAA;;QAC1D,IAAI,CAAC,OAAO,CAAC,OAAO,iCAAY,OAAO,qDAAf,OAAO,UAAU,eAAe,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC1E,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,QAA6B,EAAA;QACnD,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG,CACI,yBAAyB,GAAA;QAC5B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACtD,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED,gFAAgF;IAChF,sCAAsC;IACtC,gFAAgF;IAEhF;;OAEG,CACI,eAAe,CAAC,OAAiC,EAAA;;;QACpD,MAAM,MAAM,kGAAW,0BAA0B,CAAC,CAAC,CAAC,8GAAE,MAAM,uEAA7C,OAAO,uCAA0C,IAAI,CAAC;QACrE,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,YAAY,KAAK,MAAM,EAAE,CAAC;YAC/B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG,CACI,sBAAsB,CAAC,OAAiC,EAAA;QAC3D,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC5E,IAAI,IAAI,CAAC,mBAAmB,KAAK,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC1D,OAAO;QACX,CAAC;QACD,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAC1D,CAAC;IAEM,iBAAiB,GAAA;IACpB,2BAA2B;IAC/B,CAAC;IAEM,kBAAkB,GAAA;IACrB,2BAA2B;IAC/B,CAAC;IAED;;OAEG,CACI,oCAAoC,CAAC,eAAiC,EAAE,MAAkB,EAAA;QAC7F,kCAAkC;QAClC,6BAA6B;QAC7B,MAAM,EAAE,CAAC;IACb,CAAC;IAED;;OAEG,CACI,YAAY,GAAA,CAAU,CAAC;IAE9B,cAAA,EAAgB,CACT,oBAAoB,GAAA;QACvB,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;OAEG,CACI,oBAAoB,GAAA;QACvB,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,oBAAoB,CAAC,aAAuB,EAAe;YAAb,KAAK,oEAAG,KAAK;;QAC9D,MAAM,QAAQ,GAAG,EAAC,kCAAI,CAAC,aAAa,qEAAI,aAAa,uCAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,KAAK,QAAQ,IAAI,KAAK,EAAE,CAAC;YACzD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG,CACI,QAAQ,CAAC,OAAgB,EAAuI;sBAArI,iEAAkB,CAAC,EAAE,KAAe,+DAAE,WAAW,sDAAG,KAAK,EAAE,aAAuB,iDAAE,OAAuB,gEAAE,iEAAuB,CAAC;QACnK,UAAU;QACV,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,EAAE,CAAC;YACpD,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,OAAO,CAAC;QAC3C,CAAC;QAED,YAAY;QACZ,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAEhD,WAAW;QACX,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEnC,aAAa;QACb,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxG,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,KAAK,SAAS,IAAI,KAAK,EAAE,CAAC;YAC3D,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,OAAO,CAAC;IACzD,CAAC;IAEO,uBAAuB,CAAC,UAAsC,EAAA;QAClE,MAAM,oBAAoB,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACxG,MAAM,oBAAoB,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAEjG,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC7B,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;QACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QACtC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,QAAgB,EAAE,QAAgB,EAAE,KAAa,EAAE,KAAa,EAAE,cAAsB,EAAA;QAClG,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAChD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAEpC,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,MAAM,qBAAqB,GAAG,IAAI,CAAC,cAAe,CAAC,gBAAyC,CAAC;QAE7F,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,uLAAE,wBAAqB,CAAC,gBAAgB,CAAC,CAAC;QAEzI,IAAI,qBAAqB,CAAC,aAAa,EAAE,CAAC;YACtC,qBAAqB,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC7C,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,aAAa,CAAC,SAAS,EAAG,EAAE,CAAC,uLAAE,wBAAqB,CAAC,gBAAgB,CAAC,CAAC;QAC5H,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IACI,CAAC,IAAI,CAAC,iBAAiB,IACvB,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,IAAI,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,EAC1K,CAAC;YACC,IAAI,CAAC,mBAAmB,CAAC,UAAU,GAAG,SAAS,CAAC;QACpD,CAAC;QAED,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;QACnF,IAAI,WAAW,GAAkD,UAAU,CAAC;QAE5E,IAAI,WAAW,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBAClC,IAAI,CAAC,SAAS,CAAC,2BAA2B,EAAE,CAAC;gBAC7C,IAAI,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;oBAC9C,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;gBAChF,CAAC;gBACD,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;gBAC1D,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,OAAO;YACX,CAAC;YAED,WAAW,GAAG,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,8BAA8B;YACpK,UAAU,CAAC,YAAY,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,EAAE,CAAC;YACvD,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;;gBACzF,MAAM,WAAW,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;gBAClF,MAAM,OAAO,6DAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,WAAW,CAAC,uFAAlD,mDAAoD,OAAO,CAAC;gBAC5E,MAAM,cAAc,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,MAAA,GAAS,CAAC,IAAA,MAAA,IAAA,gBAA8B,IAAI,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC;gBAChK,IAAI,mDAAC,OAAO,CAAE,IAAI,MAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,UAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,IAAA,gBAAA,KAA2B,CAAC,IAAI,cAAc,EAAE,CAAC;oBAC/G,YAAY,IAAI,MAAM,CAAC;gBAC3B,CAAC;gBACD,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;YACzB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,YAAY,GAAG,YAAY,CAAC;QAEzD,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;QACpI,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAEtI,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;YAClC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC1E,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC1B,IAAI,CAAC,SAAS,CAAC,8BAA8B,EAAE,CAAC;gBAChD,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,CAAC;oBACjD,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,YAAY;oBACpD,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;oBAC5C,WAAW,qLAAE,sBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC;iBACtE,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAElC,4BAA4B;QAC5B,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,WAAW,CAAC,cAAc,CACtB,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAC3C,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAA,SAAA,sCAAA,GAAoC,CAAC,EAAA,SAAA,sCAAA,EAAmC,GAC3G,CAAC,CACJ,CAAC;QACN,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC;QAC9D,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACxD,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;YAE1C,MAAM,MAAM,GAAG,YAAY,CAAC,eAAe,CAAC;YAC5C,IAAI,MAAM,EAAE,CAAC;gBACT,WAAW,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,kBAAkB,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAChI,CAAC;QACL,CAAC;QAED,mBAAmB;QACnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACzC,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO;QACP,MAAM,aAAa,GAAG,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;QAEjF,IAAI,aAAa,IAAI,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;YAC/D,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;YAC5E,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;gBACjB,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;YACpF,CAAC,MAAM,CAAC;gBACJ,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC,MAAM,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;YACxB,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrE,CAAC,MAAM,CAAC;YACJ,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,CAAC,mBAAmB,CAAC,UAAU,GAAI,WAAsC,CAAC,MAAM,EAAE,CAAC;YACvF,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG,CACI,gBAAgB,CAAC,QAAgB,EAAE,UAAkB,EAAE,UAAkB,EAA4B;6BAA1B,iEAAyB,CAAC;QACxG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;OAMG,CACI,cAAc,CAAC,QAAgB,EAAE,aAAqB,EAAE,aAAqB,EAA4B;YAA1B,kFAAyB,CAAC;QAC5G,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IAC1E,CAAC;IAED,gFAAgF;IAChF,uCAAuC;IACvC,gFAAgF;IAEhF;;OAEG,CACa,OAAO,GAAA;YAInB;QAHA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;kCAC3B,CAAC,YAAY,0EAAE,OAAO,EAAE,CAAC;mCACzB,CAAC,aAAa,wDAAlB,oBAAoB,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,cAAc,CAAC,uBAAuB,EAAE,CAAC;QAC9C,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;kLAEvB,iBAAA,AAAc,EAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE5C,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED,gFAAgF;IAChF,oCAAoC;IACpC,gFAAgF;IAEhF;;;;OAIG,CACI,cAAc,GAAkB;wBAAjB,SAAS,wDAAG,KAAK;YAK5B;QAJP,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAC3C,CAAC;;QAED,qEAAW,CAAC,gBAAgB,kFAAE,KAAK,uFAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG,CACI,eAAe,GAAkB;wBAAjB,SAAS,wDAAG,KAAK;;QACpC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;QAC5C,CAAC;;QAED,sEAAW,CAAC,gBAAgB,kFAAE,MAAM,yDAA7B,gCAAiC,CAAC,CAAC;IAC9C,CAAC;IAED,gFAAgF;IAChF,sCAAsC;IACtC,gFAAgF;IAEhF;;;OAGG,CACI,QAAQ,GAAA;QACX,uCAAuC;QACvC,OAAO,CAAC,CAAC;IACb,CAAC;IAED,gFAAgF;IAChF,iDAAiD;IACjD,gFAAgF;IAEhF;;;;OAIG,CACI,qBAAqB,CAAC,KAAuB,EAAA;QAChD,MAAM,OAAO,GAAG,yLAAI,wBAAqB,CAAC,KAAK,CAAC,CAAC;QACjD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,IAAY,EAAE,OAAkC,EAAA;QACtE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACpD,OAAO;QACX,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC5C,CAAC;IAED,gFAAgF;IAChF,wCAAwC;IACxC,gFAAgF;IAEhF;;;;OAIG,CACI,iBAAiB,CAAC,IAAY,EAAE,OAAiC,EAAA;;6CAChE,CAAC,uBAAuB,kEAA5B,8BAA8B,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC5D,CAAC;IAED,gFAAgF;IAChF,+CAA+C;IAC/C,gFAAgF;IAEhF;;;;;;OAMG,CACI,mBAAmB,CAAC,IAAwB,EAAE,aAAqB,EAAE,KAAc,EAAA;QACtF,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,GAAG,IAAA,KAAS,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;IAClG,CAAC;IAED;;;;;;OAMG,CACI,mBAAmB,CAAC,MAAkB,EAAE,IAAe,EAAE,UAAmB,EAAE,UAAmB,EAAA;QACpG,MAAM,UAAU,GAAG,MAA0B,CAAC;QAC9C,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC3B,UAAU,GAAG,CAAC,CAAC;QACnB,CAAC;QAED,IAAI,IAAqB,CAAC;QAC1B,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;gBACxB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,MAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;gBACrC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,IAAI,CAAC;YAChB,CAAC;YACD,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;gBACxB,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,MAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;gBACrC,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;IAChF,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,SAAoB,EAAE,IAAY,EAAE,MAAwB,EAAE,OAAiB,EAAA;QAC5G,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,MAAM,cAAc,GAAG,GAAG,EAAE;gBACxB,0CAA0C;gBAC1C,SAAS,CAAC,QAAQ,CAAA,EAAA,gCAAA,KAA+B,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,CAC1D,GAAG,EAAE;oBACD,MAAM,eAAe,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;oBAC1D,IAAI,IAAI,GAAgC,MAAM,CAAC;oBAC/C,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;wBACrB,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;wBAC3B,IAAmB,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;oBAC9D,CAAC,MAAM,CAAC;wBACJ,MAAM,IAAI,GAAG,IAAI,CAAC,WAAkB,CAAC,CAAC,6FAA6F;wBACnI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC5B,IAAY,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;oBACjD,CAAC;oBACD,SAAS,CAAC,KAAK,EAAE,CAAC;oBAClB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;oBAC7C,OAAO,CAAC,IAAK,CAAC,CAAC;gBACnB,CAAC,EACD,CAAC,MAAM,EAAE,EAAE;oBACP,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBAClB,OAAO,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;oBAC9B,CAAC,MAAM,CAAC;wBACJ,2EAA2E;wBAC3E,MAAM,CAAC,MAAM,CAAC,CAAC;oBACnB,CAAC;gBACL,CAAC,CACJ,CAAC;YACN,CAAC,CAAC;YAEF,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,cAAc,EAAE,CAAC;YACrB,CAAC,MAAM,CAAC;gBACJ,mGAAmG;gBACnG,qFAAqF;gBACrF,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,GAAG,EAAE;oBACnC,cAAc,EAAE,CAAC;gBACrB,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;OAQG,CACH,2GAA2G;IACpG,qBAAqB,CAAC,aAAyB,EAAE,MAAe,EAAE,IAAa,EAAE,MAAwB,EAAE,OAAiB,EAAA;QAC/H,IAAI,GAAG,IAAI,IAAI,aAAa,CAAC,QAAQ,CAAC;QAEtC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CACjD,IAAI,iLACJ,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,kLAAG,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,EACzE,SAAS,EACT,2BAA2B,CAC9B,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,aAAa,CAAC,kBAAkB,EAAE,MAAM,0CAAI,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAE1G,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;;;OAQG,CACH,2GAA2G;IACpG,8BAA8B,CAAC,cAA4B,EAAE,MAAe,EAAE,IAAa,EAAE,MAAwB,EAAE,OAAiB,EAAA;QAC3I,IAAI,GAAG,IAAI,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;QAE1C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CACjD,IAAI,GAAG,cAAc,CAAC,MAAM,EAC5B,eAAe,CAAC,6KAAW,CAAC,OAAO,kLAAG,cAA2B,CAAZ,AAAa,CAAZ,MAAmB,EACzE,SAAS,EACT,oCAAoC,CACvC,CAAC;QAEF,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7C,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,kBAAkB,yCAAE,MAAM,GAAI,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,CAAC;QACzH,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,IAAI,GAAG,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7F,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,IAAY,EAAE,MAA+B,EAAA;;;yCAC7D,CAAC,mBAAmB,8DAAxB,0BAA0B,SAAS,CAAC,IAAI,0CAAG,MAAM,8BAAE,SAAS,EAAuB,iEAAI,IAAI,CAAC,CAAC;IACjG,CAAC;IAp5GD;;;;OAIG,CACH,YAAmB,MAA2C,EAAE,UAA+B,CAAA,CAAE,CAAA;;QAC7F,KAAK,+BAAS,SAAS,8CAAjB,OAAO,cAAc,IAAI,EAAE,OAAO,CAAC,CAAC;QAhW9C,0CAAA,EAA4C,CAC5B,IAAA,CAAA,QAAQ,GAAG,CAAC,CAAC,CAAC;QAE9B,gCAAgC;QACf,IAAA,CAAA,wBAAwB,GAAG;YAAE,KAAK,EAAE,QAAQ;QAAA,CAAE,CAAC;QAC/C,IAAA,CAAA,wBAAwB,GAAG;YAAE,KAAK,EAAE,QAAQ;QAAA,CAAE,CAAC;QAChE,cAAA,EAAgB,CACA,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;QACrC,cAAA,EAAgB,CACA,IAAA,CAAA,uBAAuB,GAAG,CAAC,CAAC;QAC5C,cAAA,EAAgB,CACA,IAAA,CAAA,kBAAkB,GAAG,CAAC,CAAC;QACtB,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC,CAAC,gCAAgC;QAKlE,IAAA,CAAA,QAAQ,GAAQ,IAAI,CAAC;QACrB,IAAA,CAAA,SAAS,GAA6B,IAAI,CAAC;QAC3C,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QAGtC,IAAA,CAAA,YAAY,GAAmB;YACnC,MAAM,EAAE,EAAE;YACV,YAAY,EAAE,EAAE;YAChB,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,EAAE;SAClB,CAAC;QAmBF,cAAA,EAAgB,CACT,IAAA,CAAA,uBAAuB,GAAqC,CAAA,CAAE,CAAC;QACtE,cAAA,EAAgB,CACT,IAAA,CAAA,SAAS,GAKZ;YACA,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,8BAA8B,EAAE,CAAC;YACjC,2BAA2B,EAAE,CAAC;SACjC,CAAC;QACF;;WAEG,CACa,IAAA,CAAA,iBAAiB,GAK7B;YACA,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,8BAA8B,EAAE,CAAC;YACjC,2BAA2B,EAAE,CAAC;SACjC,CAAC;QACF;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,EAAE,CAAC;QAEnC;;WAEG,CACa,IAAA,CAAA,MAAM,GAAY,EAAE,CAAC;QAErC,cAAA,EAAgB,CACA,IAAA,CAAA,cAAc,GAAG,IAAI,KAAK,EAAS,CAAC;QAe5C,IAAA,CAAA,eAAe,GAAuB;YAAC,IAAW;YAAE,IAAW;SAAC,CAAC;QAEzE,kEAAkE;QAE1D,IAAA,CAAA,sBAAsB,GAA6B;YACvD,oBAAoB,EAAE,IAAI;YAC1B,6BAA6B,EAAE,IAAI;YACnC,6BAA6B,EAAE,IAAI;YACnC,0BAA0B,EAAE,EAAE;YAC9B,kBAAkB,EAAE,SAAS;SAChC,CAAC;QACM,IAAA,CAAA,qBAAqB,GAA6B;YACtD,oBAAoB,EAAE,IAAI;YAC1B,6BAA6B,EAAE,IAAI;YACnC,6BAA6B,EAAE,IAAI;YACnC,0BAA0B,EAAE,EAAE;YAC9B,kBAAkB,EAAE,SAAS;SAChC,CAAC;QACF,cAAA,EAAgB,CACT,IAAA,CAAA,qBAAqB,GAA+C,EAAE,CAAC;QAWtE,IAAA,CAAA,6BAA6B,GAAwD,IAAI,CAAC;QAC1F,IAAA,CAAA,mBAAmB,GAAyB,IAAI,CAAC;QACjD,IAAA,CAAA,gBAAgB,GAAG,IAAI,CAAC;QACxB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QA2MnC;;WAEG,CACsB,IAAA,CAAA,eAAe,GAAY,IAAI,CAAC;QAEzD;;WAEG,CACsB,IAAA,CAAA,mBAAmB,GAAY,KAAK,CAAC;QA0C9D,gFAAgF;QAChF,8CAA8C;QAC9C,gFAAgF;QACxE,IAAA,CAAA,6BAA6B,GAA4B,IAAI,CAAC;QAitBtE,gFAAgF;QAChF,qDAAqD;QACrD,gFAAgF;QAEhF,yDAAyD;QACjD,IAAA,CAAA,iBAAiB,GAAmD;YAAE,CAAC,EAAE,CAAC;YAAE,CAAC,EAAE,CAAC;YAAE,CAAC,EAAE,CAAC;YAAE,CAAC,EAAE,CAAC;QAAA,CAAE,CAAC;QAgE/F,IAAA,CAAA,gBAAgB,GAAmD;YAAE,CAAC,EAAE,CAAC;YAAE,CAAC,EAAE,CAAC;YAAE,CAAC,EAAE,CAAC;YAAE,CAAC,EAAE,CAAC;QAAA,CAAE,CAAC;QAC5F,IAAA,CAAA,cAAc,GAAG;YAAE,CAAC,EAAE,CAAC;YAAE,CAAC,EAAE,CAAC;YAAE,CAAC,EAAE,CAAC;YAAE,CAAC,EAAE,CAAC;QAAA,CAAE,CAAC;QA8D9C,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC,CAAC;QAkBzB,IAAA,CAAA,mBAAmB,GAA4B;YAAC,IAAI;YAAE,IAAI;YAAE,IAAI;YAAE,IAAI;SAAC,CAAC;QAi9CxE,IAAA,CAAA,mBAAmB,GAAG,yKAAI,qBAAkB,EAAE,CAAC;QA51EnD,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QAEtB,IAAI,CAAC,UAAU,GAAG,kKAAI,cAAW,EAAE,CAAC;QAEpC,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,IAAI,CAAA,CAAE,CAAC;;QAC1D,OAAO,CAAC,qBAAqB,6CAAW,qBAAqB,0DAA7B,OAAO,0BAA0B,KAAK,CAAC;iKAEvE,SAAM,CAAC,GAAG,CAAC,eAA2C,OAA5B,qLAAc,CAAC,OAAO,EAAA,OAAsB,WAAZ,CAAC,WAAW,EAAA,QAAS,CAAC,CAAC;QACjF,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;oKACjB,UAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;YACzD,OAAO;QACX,CAAC;QAED,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,SAAS,CAAC,GAAG,CAAC,wBAAwB,EAAE,CAAC;QAE9F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;QAEpC,IAAI,CAAC,gBAAgB,GAAG,MAA2B,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7E,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YACnC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAExC,IAAI,CAAC,gBAAgB,GAAG,8LAAI,4BAAyB,EAAE,CAAC;QACxD,IAAI,CAAC,oBAAoB,GAAG,8LAAI,4BAAyB,EAAE,CAAC;IAChE,CAAC;;AAxYD,2BAA2B;AACH,aAAA,sBAAsB,GAAmB;IAC7D,MAAM,EAAE,GAAuB,+JAApB,QAAK,CAAC,cAAc,EAAA,oBAAqB;IACpD,QAAQ,EAAE,GAAuB,+JAApB,QAAK,CAAC,cAAc,EAAA,sBAAuB;CAC3D,AAH6C,CAG5C;AAEa,aAAA,WAAW,GAAG,CAAC,AAAJ,CAAK", "debugId": null}}]}