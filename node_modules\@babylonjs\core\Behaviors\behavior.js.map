{"version": 3, "file": "behavior.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Behaviors/behavior.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Nullable } from \"../types\";\r\n\r\n/**\r\n * Interface used to define a behavior\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface Behavior<T> {\r\n    /** gets or sets behavior's name */\r\n    name: string;\r\n\r\n    /**\r\n     * Function called when the behavior needs to be initialized (before attaching it to a target)\r\n     */\r\n    init(): void;\r\n    /**\r\n     * Called when the behavior is attached to a target\r\n     * @param target defines the target where the behavior is attached to\r\n     */\r\n    attach(target: T): void;\r\n    /**\r\n     * Called when the behavior is detached from its target\r\n     */\r\n    detach(): void;\r\n}\r\n\r\n/**\r\n * Interface implemented by classes supporting behaviors\r\n */\r\nexport interface IBehaviorAware<T> {\r\n    /**\r\n     * Attach a behavior\r\n     * @param behavior defines the behavior to attach\r\n     * @returns the current host\r\n     */\r\n    addBehavior(behavior: Behavior<T>): T;\r\n    /**\r\n     * Remove a behavior from the current object\r\n     * @param behavior defines the behavior to detach\r\n     * @returns the current host\r\n     */\r\n    removeBehavior(behavior: Behavior<T>): T;\r\n    /**\r\n     * Gets a behavior using its name to search\r\n     * @param name defines the name to search\r\n     * @returns the behavior or null if not found\r\n     */\r\n    getBehaviorByName(name: string): Nullable<Behavior<T>>;\r\n}\r\n"]}