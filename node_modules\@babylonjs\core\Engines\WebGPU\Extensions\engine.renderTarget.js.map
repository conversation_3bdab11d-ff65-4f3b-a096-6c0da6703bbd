{"version": 3, "file": "engine.renderTarget.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.renderTarget.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,6CAA6C,CAAC;AAGrG,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C,OAAO,EAAE,yBAAyB,EAAE,MAAM,8BAA8B,CAAC;AACzE,OAAO,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,+DAAwD;AAE3G,OAAO,6CAA6C,CAAC;AACrD,OAAO,EAAE,gBAAgB,EAAE,kCAAsC;AAiCjE,gBAAgB,CAAC,SAAS,CAAC,kCAAkC,GAAG,UAAU,OAAgB,EAAE,MAAe,EAAE,IAAiB;IAC1H,MAAM,SAAS,GAAG,IAAI,yBAAyB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7E,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/C,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,IAAiB,EAAE,OAA8C;IAC9H,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAA8B,CAAC;IAE3G,MAAM,WAAW,GAAgC,EAAE,CAAC;IAEpD,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QACvD,WAAW,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QACtD,WAAW,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACjH,WAAW,CAAC,qBAAqB,GAAG,WAAW,CAAC,mBAAmB,IAAI,OAAO,CAAC,qBAAqB,CAAC;QACrG,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;QAChI,WAAW,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;QACvD,WAAW,CAAC,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;QAC5D,WAAW,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QACtD,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QACtC,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAClC,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACpC,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;IACpC,CAAC;SAAM,CAAC;QACJ,WAAW,CAAC,eAAe,GAAG,OAAO,CAAC;QACtC,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACvC,WAAW,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAC1C,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,8BAA8B,CAAC;QACpE,WAAW,CAAC,aAAa,GAAG,CAAC,CAAC;QAC9B,WAAW,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED,MAAM,OAAO,GACT,WAAW,CAAC,eAAe,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,6CAAqC,CAAC,CAAC;IAErK,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,IAAI,qBAAqB,CAAC;IAC7D,SAAS,CAAC,QAAQ,GAAG,WAAW,CAAC,eAAe,EAAE,OAAO,IAAI,WAAW,CAAC,OAAO,IAAI,CAAC,CAAC;IACtF,SAAS,CAAC,oBAAoB,GAAG,WAAW,CAAC,mBAAmB,CAAC;IACjE,SAAS,CAAC,sBAAsB,GAAG,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAEpF,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAE/B,IAAI,SAAS,CAAC,oBAAoB,IAAI,SAAS,CAAC,sBAAsB,EAAE,CAAC;QACrE,SAAS,CAAC,yBAAyB,CAC/B,CAAC,EACD,KAAK,EAAE,+DAA+D;QACtE,SAAS,CAAC,sBAAsB,EAChC,SAAS,CAAC,OAAO,EACjB,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,2BAA2B,EACpH,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,SAAS,CACtE,CAAC;IACN,CAAC;IAED,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;QAC1C,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;YAChH,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC;QAE5H,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;YAChH,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;QACpC,CAAC;IACL,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,0BAA0B,GAAG,UAAU,IAAiB,EAAE,OAAoC,EAAE,OAAkC;IACzJ,MAAM,eAAe,GAAG;QACpB,iBAAiB,EAAE,KAAK;QACxB,kBAAkB,EAAE,CAAC;QACrB,eAAe,EAAE,KAAK;QACtB,OAAO,EAAE,CAAC;QACV,kBAAkB,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,2BAA2B;QAC9H,GAAG,OAAO;KACb,CAAC;IAEF,MAAM,UAAU,GAAG,gBAAgB,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;IAExE,OAAO,CAAC,+BAA+B,GAAG,UAAU,CAAC;IAErD,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,6CAAoC,CAAC,qCAA4B,CAAC,CAAC;IAEjI,eAAe,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAEtC,eAAe,CAAC,MAAM,GAAG,eAAe,CAAC,kBAAkB,CAAC;IAC5D,eAAe,CAAC,IAAI,GAAG,sBAAsB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAEtE,IAAI,CAAC,yBAAyB,CAAC,eAAe,EAAE,IAAI,EAAE,eAAe,CAAC,iBAAiB,EAAE,eAAe,CAAC,kBAAkB,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC;IAEtJ,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,eAAe,CAAC,CAAC;IAExE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAElD,OAAO,eAAe,CAAC;AAC3B,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,yBAAyB,GAAG,UACnD,eAAgC,EAChC,IAAiB,EACjB,iBAA0B,EAC1B,kBAA0B,EAC1B,OAAO,GAAG,CAAC;IAEX,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;IAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;IACjG,MAAM,MAAM,GAAwE,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;IACtG,MAAM,KAAK,GAAwE,IAAK,CAAC,KAAK,IAAI,CAAC,CAAC;IAEpG,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC;IAClC,eAAe,CAAC,UAAU,GAAG,MAAM,CAAC;IACpC,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;IAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;IAChC,eAAe,CAAC,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;IACvC,eAAe,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;IACjC,eAAe,CAAC,KAAK,GAAG,MAAM,IAAI,KAAK,CAAC;IACxC,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;IAC/B,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;IACxC,eAAe,CAAC,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC;IACpI,eAAe,CAAC,IAAI,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC,mCAAmC;IACvF,eAAe,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IACzD,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;IACnE,eAAe,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;AACvE,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,oCAAoC,GAAG,UAAU,SAAwC,EAAE,OAAe;IACjI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;QACpE,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC;IAE3D,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAElE,IAAI,SAAS,CAAC,oBAAoB,EAAE,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;QAC/E,SAAS,CAAC,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;IACrD,CAAC;IAED,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC7B,SAAS,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAEpC,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC", "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../../Materials/Textures/internalTexture\";\r\nimport type { RenderTargetCreationOptions, DepthTextureCreationOptions, TextureSize } from \"../../../Materials/Textures/textureCreationOptions\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { Constants } from \"../../constants\";\r\nimport type { RenderTargetWrapper } from \"../../renderTargetWrapper\";\r\nimport { WebGPURenderTargetWrapper } from \"../webgpuRenderTargetWrapper\";\r\nimport { GetTypeForDepthTexture, HasStencilAspect } from \"core/Materials/Textures/textureHelper.functions\";\r\n\r\nimport \"../../AbstractEngine/abstractEngine.texture\";\r\nimport { ThinWebGPUEngine } from \"core/Engines/thinWebGPUEngine\";\r\n\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a new render target texture\r\n         * @param size defines the size of the texture\r\n         * @param options defines the options used to create the texture\r\n         * @returns a new render target wrapper ready to render texture\r\n         */\r\n        createRenderTargetTexture(size: TextureSize, options: boolean | RenderTargetCreationOptions): RenderTargetWrapper;\r\n\r\n        /**\r\n         * Updates the sample count of a render target texture\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#multisample-render-targets\r\n         * @param rtWrapper defines the render target wrapper to update\r\n         * @param samples defines the sample count to set\r\n         * @returns the effective sample count (could be 0 if multisample render targets are not supported)\r\n         */\r\n        updateRenderTargetTextureSampleCount(rtWrapper: Nullable<RenderTargetWrapper>, samples: number): number;\r\n\r\n        /** @internal */\r\n        _createDepthStencilTexture(size: TextureSize, options: DepthTextureCreationOptions, rtWrapper: RenderTargetWrapper): InternalTexture;\r\n\r\n        /** @internal */\r\n        _createHardwareRenderTargetWrapper(isMulti: boolean, isCube: boolean, size: TextureSize): RenderTargetWrapper;\r\n\r\n        /** @internal */\r\n        _setupDepthStencilTexture(internalTexture: InternalTexture, size: TextureSize, bilinearFiltering: boolean, comparisonFunction: number, samples?: number): void;\r\n    }\r\n}\r\n\r\nThinWebGPUEngine.prototype._createHardwareRenderTargetWrapper = function (isMulti: boolean, isCube: boolean, size: TextureSize): WebGPURenderTargetWrapper {\r\n    const rtWrapper = new WebGPURenderTargetWrapper(isMulti, isCube, size, this);\r\n    this._renderTargetWrapperCache.push(rtWrapper);\r\n    return rtWrapper;\r\n};\r\n\r\nThinWebGPUEngine.prototype.createRenderTargetTexture = function (size: TextureSize, options: boolean | RenderTargetCreationOptions): WebGPURenderTargetWrapper {\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(false, false, size) as WebGPURenderTargetWrapper;\r\n\r\n    const fullOptions: RenderTargetCreationOptions = {};\r\n\r\n    if (options !== undefined && typeof options === \"object\") {\r\n        fullOptions.generateMipMaps = options.generateMipMaps;\r\n        fullOptions.generateDepthBuffer = options.generateDepthBuffer === undefined ? true : options.generateDepthBuffer;\r\n        fullOptions.generateStencilBuffer = fullOptions.generateDepthBuffer && options.generateStencilBuffer;\r\n        fullOptions.samplingMode = options.samplingMode === undefined ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : options.samplingMode;\r\n        fullOptions.creationFlags = options.creationFlags ?? 0;\r\n        fullOptions.noColorAttachment = !!options.noColorAttachment;\r\n        fullOptions.colorAttachment = options.colorAttachment;\r\n        fullOptions.samples = options.samples;\r\n        fullOptions.label = options.label;\r\n        fullOptions.format = options.format;\r\n        fullOptions.type = options.type;\r\n    } else {\r\n        fullOptions.generateMipMaps = options;\r\n        fullOptions.generateDepthBuffer = true;\r\n        fullOptions.generateStencilBuffer = false;\r\n        fullOptions.samplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n        fullOptions.creationFlags = 0;\r\n        fullOptions.noColorAttachment = false;\r\n    }\r\n\r\n    const texture =\r\n        fullOptions.colorAttachment || (fullOptions.noColorAttachment ? null : this._createInternalTexture(size, fullOptions, true, InternalTextureSource.RenderTarget));\r\n\r\n    rtWrapper.label = fullOptions.label ?? \"RenderTargetWrapper\";\r\n    rtWrapper._samples = fullOptions.colorAttachment?.samples ?? fullOptions.samples ?? 1;\r\n    rtWrapper._generateDepthBuffer = fullOptions.generateDepthBuffer;\r\n    rtWrapper._generateStencilBuffer = fullOptions.generateStencilBuffer ? true : false;\r\n\r\n    rtWrapper.setTextures(texture);\r\n\r\n    if (rtWrapper._generateDepthBuffer || rtWrapper._generateStencilBuffer) {\r\n        rtWrapper.createDepthStencilTexture(\r\n            0,\r\n            false, // force false as filtering is not supported for depth textures\r\n            rtWrapper._generateStencilBuffer,\r\n            rtWrapper.samples,\r\n            fullOptions.generateStencilBuffer ? Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 : Constants.TEXTUREFORMAT_DEPTH32_FLOAT,\r\n            fullOptions.label ? fullOptions.label + \"-DepthStencil\" : undefined\r\n        );\r\n    }\r\n\r\n    if (texture && !fullOptions.colorAttachment) {\r\n        if (options !== undefined && typeof options === \"object\" && options.createMipMaps && !fullOptions.generateMipMaps) {\r\n            texture.generateMipMaps = true;\r\n        }\r\n\r\n        this._textureHelper.createGPUTextureForInternalTexture(texture, undefined, undefined, undefined, fullOptions.creationFlags);\r\n\r\n        if (options !== undefined && typeof options === \"object\" && options.createMipMaps && !fullOptions.generateMipMaps) {\r\n            texture.generateMipMaps = false;\r\n        }\r\n    }\r\n\r\n    return rtWrapper;\r\n};\r\n\r\nThinWebGPUEngine.prototype._createDepthStencilTexture = function (size: TextureSize, options: DepthTextureCreationOptions, wrapper: WebGPURenderTargetWrapper): InternalTexture {\r\n    const internalOptions = {\r\n        bilinearFiltering: false,\r\n        comparisonFunction: 0,\r\n        generateStencil: false,\r\n        samples: 1,\r\n        depthTextureFormat: options.generateStencil ? Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 : Constants.TEXTUREFORMAT_DEPTH32_FLOAT,\r\n        ...options,\r\n    };\r\n\r\n    const hasStencil = HasStencilAspect(internalOptions.depthTextureFormat);\r\n\r\n    wrapper._depthStencilTextureWithStencil = hasStencil;\r\n\r\n    const internalTexture = new InternalTexture(this, hasStencil ? InternalTextureSource.DepthStencil : InternalTextureSource.Depth);\r\n\r\n    internalTexture.label = options.label;\r\n\r\n    internalTexture.format = internalOptions.depthTextureFormat;\r\n    internalTexture.type = GetTypeForDepthTexture(internalTexture.format);\r\n\r\n    this._setupDepthStencilTexture(internalTexture, size, internalOptions.bilinearFiltering, internalOptions.comparisonFunction, internalOptions.samples);\r\n\r\n    this._textureHelper.createGPUTextureForInternalTexture(internalTexture);\r\n\r\n    this._internalTexturesCache.push(internalTexture);\r\n\r\n    return internalTexture;\r\n};\r\n\r\nThinWebGPUEngine.prototype._setupDepthStencilTexture = function (\r\n    internalTexture: InternalTexture,\r\n    size: TextureSize,\r\n    bilinearFiltering: boolean,\r\n    comparisonFunction: number,\r\n    samples = 1\r\n): void {\r\n    const width = (<{ width: number; height: number; layers?: number }>size).width ?? <number>size;\r\n    const height = (<{ width: number; height: number; layers?: number }>size).height ?? <number>size;\r\n    const layers = (<{ width: number; height: number; depth?: number; layers?: number }>size).layers || 0;\r\n    const depth = (<{ width: number; height: number; depth?: number; layers?: number }>size).depth || 0;\r\n\r\n    internalTexture.baseWidth = width;\r\n    internalTexture.baseHeight = height;\r\n    internalTexture.width = width;\r\n    internalTexture.height = height;\r\n    internalTexture.is2DArray = layers > 0;\r\n    internalTexture.is3D = depth > 0;\r\n    internalTexture.depth = layers || depth;\r\n    internalTexture.isReady = true;\r\n    internalTexture.samples = samples;\r\n    internalTexture.generateMipMaps = false;\r\n    internalTexture.samplingMode = bilinearFiltering ? Constants.TEXTURE_BILINEAR_SAMPLINGMODE : Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n    internalTexture.type = Constants.TEXTURETYPE_FLOAT; // the right type will be set later\r\n    internalTexture._comparisonFunction = comparisonFunction;\r\n    internalTexture._cachedWrapU = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n    internalTexture._cachedWrapV = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n};\r\n\r\nThinWebGPUEngine.prototype.updateRenderTargetTextureSampleCount = function (rtWrapper: Nullable<RenderTargetWrapper>, samples: number): number {\r\n    if (!rtWrapper || !rtWrapper.texture || rtWrapper.samples === samples) {\r\n        return samples;\r\n    }\r\n\r\n    samples = Math.min(samples, this.getCaps().maxMSAASamples);\r\n\r\n    this._textureHelper.createMSAATexture(rtWrapper.texture, samples);\r\n\r\n    if (rtWrapper._depthStencilTexture) {\r\n        this._textureHelper.createMSAATexture(rtWrapper._depthStencilTexture, samples);\r\n        rtWrapper._depthStencilTexture.samples = samples;\r\n    }\r\n\r\n    rtWrapper._samples = samples;\r\n    rtWrapper.texture.samples = samples;\r\n\r\n    return samples;\r\n};\r\n"]}