(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "_": ()=>_define_property
});
function _define_property(obj, key, value) {
    if (key in obj) {
        Object.defineProperty(obj, key, {
            value: value,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else obj[key] = value;
    return obj;
}
;
}),
"[project]/node_modules/@babylonjs/materials/custom/customMaterial.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CustomMaterial": ()=>CustomMaterial,
    "CustomShaderStructure": ()=>CustomShaderStructure,
    "ShaderSpecialParts": ()=>ShaderSpecialParts
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/effect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$standardMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/standardMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.color.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Shaders$2f$default$2e$vertex$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Shaders/default.vertex.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Shaders$2f$default$2e$fragment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Shaders/default.fragment.js [app-client] (ecmascript)");
;
;
;
;
;
;
class CustomShaderStructure {
    constructor(){}
}
class ShaderSpecialParts {
    constructor(){}
}
class CustomMaterial extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$standardMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StandardMaterial"] {
    /**
     * Runs after the material is bound to a mesh
     * @param mesh mesh bound
     * @param effect bound effect used to render
     */ AttachAfterBind(mesh, effect) {
        if (this._newUniformInstances) {
            for(const el in this._newUniformInstances){
                const ea = el.toString().split("-");
                if (ea[0] == "vec2") {
                    effect.setVector2(ea[1], this._newUniformInstances[el]);
                } else if (ea[0] == "vec3") {
                    if (this._newUniformInstances[el] instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"]) {
                        effect.setColor3(ea[1], this._newUniformInstances[el]);
                    } else {
                        effect.setVector3(ea[1], this._newUniformInstances[el]);
                    }
                } else if (ea[0] == "vec4") {
                    if (this._newUniformInstances[el] instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color4"]) {
                        effect.setDirectColor4(ea[1], this._newUniformInstances[el]);
                    } else {
                        effect.setVector4(ea[1], this._newUniformInstances[el]);
                    }
                    effect.setVector4(ea[1], this._newUniformInstances[el]);
                } else if (ea[0] == "mat4") {
                    effect.setMatrix(ea[1], this._newUniformInstances[el]);
                } else if (ea[0] == "float") {
                    effect.setFloat(ea[1], this._newUniformInstances[el]);
                }
            }
        }
        if (this._newSamplerInstances) {
            for(const el in this._newSamplerInstances){
                const ea = el.toString().split("-");
                if (ea[0] == "sampler2D" && this._newSamplerInstances[el].isReady && this._newSamplerInstances[el].isReady()) {
                    effect.setTexture(ea[1], this._newSamplerInstances[el]);
                }
            }
        }
    }
    /**
     * @internal
     */ ReviewUniform(name, arr) {
        if (name == "uniform" && this._newUniforms) {
            for(let ind = 0; ind < this._newUniforms.length; ind++){
                if (this._customUniform[ind].indexOf("sampler") == -1) {
                    arr.push(this._newUniforms[ind].replace(/\[\d*\]/g, ""));
                }
            }
        }
        if (name == "sampler" && this._newUniforms) {
            for(let ind = 0; ind < this._newUniforms.length; ind++){
                if (this._customUniform[ind].indexOf("sampler") != -1) {
                    arr.push(this._newUniforms[ind].replace(/\[\d*\]/g, ""));
                }
            }
        }
        return arr;
    }
    /**
     * Builds the material
     * @param shaderName name of the shader
     * @param uniforms list of uniforms
     * @param uniformBuffers list of uniform buffers
     * @param samplers list of samplers
     * @param defines list of defines
     * @param attributes list of attributes
     * @returns the shader name
     */ Builder(shaderName, uniforms, uniformBuffers, samplers, defines, attributes) {
        if (attributes && this._customAttributes && this._customAttributes.length > 0) {
            attributes.push(...this._customAttributes);
        }
        this.ReviewUniform("uniform", uniforms);
        this.ReviewUniform("sampler", samplers);
        const name = this._createdShaderName;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[name + "VertexShader"] && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[name + "PixelShader"]) {
            return name;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[name + "VertexShader"] = this._injectCustomCode(this.VertexShader, "vertex");
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[name + "PixelShader"] = this._injectCustomCode(this.FragmentShader, "fragment");
        return name;
    }
    _injectCustomCode(code, shaderType) {
        const customCode = this._getCustomCode(shaderType);
        for(const point in customCode){
            const injectedCode = customCode[point];
            if (injectedCode && injectedCode.length > 0) {
                const fullPointName = "#define " + point;
                code = code.replace(fullPointName, "\n" + injectedCode + "\n" + fullPointName);
            }
        }
        return code;
    }
    _getCustomCode(shaderType) {
        var _this__customUniform;
        if (shaderType === "vertex") {
            var _this__customUniform1;
            return {
                CUSTOM_VERTEX_BEGIN: this.CustomParts.Vertex_Begin,
                CUSTOM_VERTEX_DEFINITIONS: (((_this__customUniform1 = this._customUniform) === null || _this__customUniform1 === void 0 ? void 0 : _this__customUniform1.join("\n")) || "") + (this.CustomParts.Vertex_Definitions || ""),
                CUSTOM_VERTEX_MAIN_BEGIN: this.CustomParts.Vertex_MainBegin,
                CUSTOM_VERTEX_UPDATE_POSITION: this.CustomParts.Vertex_Before_PositionUpdated,
                CUSTOM_VERTEX_UPDATE_NORMAL: this.CustomParts.Vertex_Before_NormalUpdated,
                CUSTOM_VERTEX_MAIN_END: this.CustomParts.Vertex_MainEnd,
                CUSTOM_VERTEX_UPDATE_WORLDPOS: this.CustomParts.Vertex_After_WorldPosComputed
            };
        }
        return {
            CUSTOM_FRAGMENT_BEGIN: this.CustomParts.Fragment_Begin,
            CUSTOM_FRAGMENT_DEFINITIONS: (((_this__customUniform = this._customUniform) === null || _this__customUniform === void 0 ? void 0 : _this__customUniform.join("\n")) || "") + (this.CustomParts.Fragment_Definitions || ""),
            CUSTOM_FRAGMENT_MAIN_BEGIN: this.CustomParts.Fragment_MainBegin,
            CUSTOM_FRAGMENT_UPDATE_DIFFUSE: this.CustomParts.Fragment_Custom_Diffuse,
            CUSTOM_FRAGMENT_UPDATE_ALPHA: this.CustomParts.Fragment_Custom_Alpha,
            CUSTOM_FRAGMENT_BEFORE_LIGHTS: this.CustomParts.Fragment_Before_Lights,
            CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR: this.CustomParts.Fragment_Before_FragColor,
            CUSTOM_FRAGMENT_MAIN_END: this.CustomParts.Fragment_MainEnd,
            CUSTOM_FRAGMENT_BEFORE_FOG: this.CustomParts.Fragment_Before_Fog
        };
    }
    _afterBind(mesh) {
        let effect = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null, subMesh = arguments.length > 2 ? arguments[2] : void 0;
        if (!effect) {
            return;
        }
        this.AttachAfterBind(mesh, effect);
        try {
            super._afterBind(mesh, effect, subMesh);
        } catch (e) {}
    }
    /**
     * Adds a new uniform to the shader
     * @param name the name of the uniform to add
     * @param kind the type of the uniform to add
     * @param param the value of the uniform to add
     * @returns the current material
     */ AddUniform(name, kind, param) {
        if (!this._customUniform) {
            this._customUniform = [];
            this._newUniforms = [];
            this._newSamplerInstances = {};
            this._newUniformInstances = {};
        }
        if (param) {
            if (kind.indexOf("sampler") != -1) {
                this._newSamplerInstances[kind + "-" + name] = param;
            } else {
                this._newUniformInstances[kind + "-" + name] = param;
            }
        }
        this._customUniform.push("uniform " + kind + " " + name + ";");
        this._newUniforms.push(name);
        return this;
    }
    /**
     * Adds a custom attribute
     * @param name the name of the attribute
     * @returns the current material
     */ AddAttribute(name) {
        if (!this._customAttributes) {
            this._customAttributes = [];
        }
        this._customAttributes.push(name);
        return this;
    }
    /**
     * Sets the code on Fragment_Begin portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Begin(shaderPart) {
        this.CustomParts.Fragment_Begin = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Definitions portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Definitions(shaderPart) {
        this.CustomParts.Fragment_Definitions = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_MainBegin portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_MainBegin(shaderPart) {
        this.CustomParts.Fragment_MainBegin = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_MainEnd portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_MainEnd(shaderPart) {
        this.CustomParts.Fragment_MainEnd = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Custom_Diffuse portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Custom_Diffuse(shaderPart) {
        this.CustomParts.Fragment_Custom_Diffuse = shaderPart.replace("result", "diffuseColor");
        return this;
    }
    /**
     * Sets the code on Fragment_Custom_Alpha portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Custom_Alpha(shaderPart) {
        this.CustomParts.Fragment_Custom_Alpha = shaderPart.replace("result", "alpha");
        return this;
    }
    /**
     * Sets the code on Fragment_Before_Lights portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Before_Lights(shaderPart) {
        this.CustomParts.Fragment_Before_Lights = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Before_Fog portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Before_Fog(shaderPart) {
        this.CustomParts.Fragment_Before_Fog = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Before_FragColor portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Before_FragColor(shaderPart) {
        this.CustomParts.Fragment_Before_FragColor = shaderPart.replace("result", "color");
        return this;
    }
    /**
     * Sets the code on Vertex_Begin portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_Begin(shaderPart) {
        this.CustomParts.Vertex_Begin = shaderPart;
        return this;
    }
    /**
     * Sets the code on Vertex_Definitions portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_Definitions(shaderPart) {
        this.CustomParts.Vertex_Definitions = shaderPart;
        return this;
    }
    /**
     * Sets the code on Vertex_MainBegin portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_MainBegin(shaderPart) {
        this.CustomParts.Vertex_MainBegin = shaderPart;
        return this;
    }
    /**
     * Sets the code on Vertex_Before_PositionUpdated portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_Before_PositionUpdated(shaderPart) {
        this.CustomParts.Vertex_Before_PositionUpdated = shaderPart.replace("result", "positionUpdated");
        return this;
    }
    /**
     * Sets the code on Vertex_Before_NormalUpdated portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_Before_NormalUpdated(shaderPart) {
        this.CustomParts.Vertex_Before_NormalUpdated = shaderPart.replace("result", "normalUpdated");
        return this;
    }
    /**
     * Sets the code on Vertex_After_WorldPosComputed portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_After_WorldPosComputed(shaderPart) {
        this.CustomParts.Vertex_After_WorldPosComputed = shaderPart;
        return this;
    }
    /**
     * Sets the code on Vertex_MainEnd portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_MainEnd(shaderPart) {
        this.CustomParts.Vertex_MainEnd = shaderPart;
        return this;
    }
    constructor(name, scene){
        super(name, scene, true);
        this.CustomParts = new ShaderSpecialParts();
        this.customShaderNameResolve = this.Builder;
        this.FragmentShader = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore["defaultPixelShader"];
        this.VertexShader = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore["defaultVertexShader"];
        CustomMaterial.ShaderIndexer++;
        this._createdShaderName = "custom_" + CustomMaterial.ShaderIndexer;
    }
}
/**
 * Index for each created shader
 */ CustomMaterial.ShaderIndexer = 1;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.CustomMaterial", CustomMaterial); //# sourceMappingURL=customMaterial.js.map
}),
"[project]/node_modules/@babylonjs/materials/custom/pbrCustomMaterial.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PBRCustomMaterial": ()=>PBRCustomMaterial,
    "ShaderAlbedoParts": ()=>ShaderAlbedoParts,
    "ShaderAlebdoParts": ()=>ShaderAlebdoParts
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/effect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/PBR/pbrMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$Processors$2f$shaderCodeInliner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/Processors/shaderCodeInliner.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.color.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Shaders$2f$pbr$2e$vertex$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Shaders/pbr.vertex.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Shaders$2f$pbr$2e$fragment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Shaders/pbr.fragment.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
class ShaderAlbedoParts {
    constructor(){}
}
const ShaderAlebdoParts = ShaderAlbedoParts;
class PBRCustomMaterial extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRMaterial"] {
    /**
     * Runs after the material is bound to a mesh
     * @param mesh mesh bound
     * @param effect bound effect used to render
     */ AttachAfterBind(mesh, effect) {
        if (this._newUniformInstances) {
            for(const el in this._newUniformInstances){
                const ea = el.toString().split("-");
                if (ea[0] == "vec2") {
                    effect.setVector2(ea[1], this._newUniformInstances[el]);
                } else if (ea[0] == "vec3") {
                    if (this._newUniformInstances[el] instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"]) {
                        effect.setColor3(ea[1], this._newUniformInstances[el]);
                    } else {
                        effect.setVector3(ea[1], this._newUniformInstances[el]);
                    }
                } else if (ea[0] == "vec4") {
                    if (this._newUniformInstances[el] instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color4"]) {
                        effect.setDirectColor4(ea[1], this._newUniformInstances[el]);
                    } else {
                        effect.setVector4(ea[1], this._newUniformInstances[el]);
                    }
                    effect.setVector4(ea[1], this._newUniformInstances[el]);
                } else if (ea[0] == "mat4") {
                    effect.setMatrix(ea[1], this._newUniformInstances[el]);
                } else if (ea[0] == "float") {
                    effect.setFloat(ea[1], this._newUniformInstances[el]);
                }
            }
        }
        if (this._newSamplerInstances) {
            for(const el in this._newSamplerInstances){
                const ea = el.toString().split("-");
                if (ea[0] == "sampler2D" && this._newSamplerInstances[el].isReady && this._newSamplerInstances[el].isReady()) {
                    effect.setTexture(ea[1], this._newSamplerInstances[el]);
                }
            }
        }
    }
    /**
     * @internal
     */ ReviewUniform(name, arr) {
        if (name == "uniform" && this._newUniforms) {
            for(let ind = 0; ind < this._newUniforms.length; ind++){
                if (this._customUniform[ind].indexOf("sampler") == -1) {
                    arr.push(this._newUniforms[ind].replace(/\[\d*\]/g, ""));
                }
            }
        }
        if (name == "sampler" && this._newUniforms) {
            for(let ind = 0; ind < this._newUniforms.length; ind++){
                if (this._customUniform[ind].indexOf("sampler") != -1) {
                    arr.push(this._newUniforms[ind].replace(/\[\d*\]/g, ""));
                }
            }
        }
        return arr;
    }
    /**
     * Builds the material
     * @param shaderName name of the shader
     * @param uniforms list of uniforms
     * @param uniformBuffers list of uniform buffers
     * @param samplers list of samplers
     * @param defines list of defines
     * @param attributes list of attributes
     * @param options options to compile the shader
     * @returns the shader name
     */ Builder(shaderName, uniforms, uniformBuffers, samplers, defines, attributes, options) {
        if (options) {
            const currentProcessing = options.processFinalCode;
            options.processFinalCode = (type, code)=>{
                if (type === "vertex") {
                    return currentProcessing ? currentProcessing(type, code) : code;
                }
                const sci = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$Processors$2f$shaderCodeInliner$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderCodeInliner"](code);
                sci.inlineToken = "#define pbr_inline";
                sci.processCode();
                return currentProcessing ? currentProcessing(type, sci.code) : sci.code;
            };
        }
        if (attributes && this._customAttributes && this._customAttributes.length > 0) {
            attributes.push(...this._customAttributes);
        }
        this.ReviewUniform("uniform", uniforms);
        this.ReviewUniform("sampler", samplers);
        const name = this._createdShaderName;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[name + "VertexShader"] && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[name + "PixelShader"]) {
            return name;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[name + "VertexShader"] = this._injectCustomCode(this.VertexShader, "vertex");
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[name + "PixelShader"] = this._injectCustomCode(this.FragmentShader, "fragment");
        return name;
    }
    _injectCustomCode(code, shaderType) {
        const customCode = this._getCustomCode(shaderType);
        for(const point in customCode){
            const injectedCode = customCode[point];
            if (injectedCode && injectedCode.length > 0) {
                const fullPointName = "#define " + point;
                code = code.replace(fullPointName, "\n" + injectedCode + "\n" + fullPointName);
            }
        }
        return code;
    }
    _getCustomCode(shaderType) {
        var _this__customUniform;
        if (shaderType === "vertex") {
            var _this__customUniform1;
            return {
                CUSTOM_VERTEX_BEGIN: this.CustomParts.Vertex_Begin,
                CUSTOM_VERTEX_DEFINITIONS: (((_this__customUniform1 = this._customUniform) === null || _this__customUniform1 === void 0 ? void 0 : _this__customUniform1.join("\n")) || "") + (this.CustomParts.Vertex_Definitions || ""),
                CUSTOM_VERTEX_MAIN_BEGIN: this.CustomParts.Vertex_MainBegin,
                CUSTOM_VERTEX_UPDATE_POSITION: this.CustomParts.Vertex_Before_PositionUpdated,
                CUSTOM_VERTEX_UPDATE_NORMAL: this.CustomParts.Vertex_Before_NormalUpdated,
                CUSTOM_VERTEX_MAIN_END: this.CustomParts.Vertex_MainEnd,
                CUSTOM_VERTEX_UPDATE_WORLDPOS: this.CustomParts.Vertex_After_WorldPosComputed
            };
        }
        return {
            CUSTOM_FRAGMENT_BEGIN: this.CustomParts.Fragment_Begin,
            CUSTOM_FRAGMENT_MAIN_BEGIN: this.CustomParts.Fragment_MainBegin,
            CUSTOM_FRAGMENT_DEFINITIONS: (((_this__customUniform = this._customUniform) === null || _this__customUniform === void 0 ? void 0 : _this__customUniform.join("\n")) || "") + (this.CustomParts.Fragment_Definitions || ""),
            CUSTOM_FRAGMENT_UPDATE_ALBEDO: this.CustomParts.Fragment_Custom_Albedo,
            CUSTOM_FRAGMENT_UPDATE_ALPHA: this.CustomParts.Fragment_Custom_Alpha,
            CUSTOM_FRAGMENT_BEFORE_LIGHTS: this.CustomParts.Fragment_Before_Lights,
            CUSTOM_FRAGMENT_UPDATE_METALLICROUGHNESS: this.CustomParts.Fragment_Custom_MetallicRoughness,
            CUSTOM_FRAGMENT_UPDATE_MICROSURFACE: this.CustomParts.Fragment_Custom_MicroSurface,
            CUSTOM_FRAGMENT_BEFORE_FINALCOLORCOMPOSITION: this.CustomParts.Fragment_Before_FinalColorComposition,
            CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR: this.CustomParts.Fragment_Before_FragColor,
            CUSTOM_FRAGMENT_MAIN_END: this.CustomParts.Fragment_MainEnd,
            CUSTOM_FRAGMENT_BEFORE_FOG: this.CustomParts.Fragment_Before_Fog
        };
    }
    _afterBind(mesh) {
        let effect = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null, subMesh = arguments.length > 2 ? arguments[2] : void 0;
        if (!effect) {
            return;
        }
        this.AttachAfterBind(mesh, effect);
        try {
            super._afterBind(mesh, effect, subMesh);
        } catch (e) {}
    }
    /**
     * Adds a new uniform to the shader
     * @param name the name of the uniform to add
     * @param kind the type of the uniform to add
     * @param param the value of the uniform to add
     * @returns the current material
     */ AddUniform(name, kind, param) {
        if (!this._customUniform) {
            this._customUniform = [];
            this._newUniforms = [];
            this._newSamplerInstances = {};
            this._newUniformInstances = {};
        }
        if (param) {
            if (kind.indexOf("sampler") != -1) {
                this._newSamplerInstances[kind + "-" + name] = param;
            } else {
                this._newUniformInstances[kind + "-" + name] = param;
            }
        }
        this._customUniform.push("uniform " + kind + " " + name + ";");
        this._newUniforms.push(name);
        return this;
    }
    /**
     * Adds a custom attribute
     * @param name the name of the attribute
     * @returns the current material
     */ AddAttribute(name) {
        if (!this._customAttributes) {
            this._customAttributes = [];
        }
        this._customAttributes.push(name);
        return this;
    }
    /**
     * Sets the code on Fragment_Begin portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Begin(shaderPart) {
        this.CustomParts.Fragment_Begin = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Definitions portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Definitions(shaderPart) {
        this.CustomParts.Fragment_Definitions = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_MainBegin portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_MainBegin(shaderPart) {
        this.CustomParts.Fragment_MainBegin = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Custom_Albedo portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Custom_Albedo(shaderPart) {
        this.CustomParts.Fragment_Custom_Albedo = shaderPart.replace("result", "surfaceAlbedo");
        return this;
    }
    /**
     * Sets the code on Fragment_Custom_Alpha portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Custom_Alpha(shaderPart) {
        this.CustomParts.Fragment_Custom_Alpha = shaderPart.replace("result", "alpha");
        return this;
    }
    /**
     * Sets the code on Fragment_Before_Lights portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Before_Lights(shaderPart) {
        this.CustomParts.Fragment_Before_Lights = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Custom_MetallicRoughness portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Custom_MetallicRoughness(shaderPart) {
        this.CustomParts.Fragment_Custom_MetallicRoughness = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Custom_MicroSurface portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Custom_MicroSurface(shaderPart) {
        this.CustomParts.Fragment_Custom_MicroSurface = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Before_Fog portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Before_Fog(shaderPart) {
        this.CustomParts.Fragment_Before_Fog = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Before_FinalColorComposition portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Before_FinalColorComposition(shaderPart) {
        this.CustomParts.Fragment_Before_FinalColorComposition = shaderPart;
        return this;
    }
    /**
     * Sets the code on Fragment_Before_FragColor portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_Before_FragColor(shaderPart) {
        this.CustomParts.Fragment_Before_FragColor = shaderPart.replace("result", "color");
        return this;
    }
    /**
     * Sets the code on Fragment_MainEnd portion
     * @param shaderPart the code string
     * @returns the current material
     */ Fragment_MainEnd(shaderPart) {
        this.CustomParts.Fragment_MainEnd = shaderPart;
        return this;
    }
    /**
     * Sets the code on Vertex_Begin portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_Begin(shaderPart) {
        this.CustomParts.Vertex_Begin = shaderPart;
        return this;
    }
    /**
     * Sets the code on Vertex_Definitions portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_Definitions(shaderPart) {
        this.CustomParts.Vertex_Definitions = shaderPart;
        return this;
    }
    /**
     * Sets the code on Vertex_MainBegin portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_MainBegin(shaderPart) {
        this.CustomParts.Vertex_MainBegin = shaderPart;
        return this;
    }
    /**
     * Sets the code on Vertex_Before_PositionUpdated portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_Before_PositionUpdated(shaderPart) {
        this.CustomParts.Vertex_Before_PositionUpdated = shaderPart.replace("result", "positionUpdated");
        return this;
    }
    /**
     * Sets the code on Vertex_Before_NormalUpdated portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_Before_NormalUpdated(shaderPart) {
        this.CustomParts.Vertex_Before_NormalUpdated = shaderPart.replace("result", "normalUpdated");
        return this;
    }
    /**
     * Sets the code on Vertex_After_WorldPosComputed portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_After_WorldPosComputed(shaderPart) {
        this.CustomParts.Vertex_After_WorldPosComputed = shaderPart;
        return this;
    }
    /**
     * Sets the code on Vertex_MainEnd portion
     * @param shaderPart the code string
     * @returns the current material
     */ Vertex_MainEnd(shaderPart) {
        this.CustomParts.Vertex_MainEnd = shaderPart;
        return this;
    }
    constructor(name, scene){
        super(name, scene, true);
        this.CustomParts = new ShaderAlbedoParts();
        this.customShaderNameResolve = this.Builder;
        this.FragmentShader = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore["pbrPixelShader"];
        this.VertexShader = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore["pbrVertexShader"];
        this.FragmentShader = this.FragmentShader.replace(/#include<pbrBlockAlbedoOpacity>/g, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].IncludesShadersStore["pbrBlockAlbedoOpacity"]);
        this.FragmentShader = this.FragmentShader.replace(/#include<pbrBlockReflectivity>/g, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].IncludesShadersStore["pbrBlockReflectivity"]);
        this.FragmentShader = this.FragmentShader.replace(/#include<pbrBlockFinalColorComposition>/g, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].IncludesShadersStore["pbrBlockFinalColorComposition"]);
        PBRCustomMaterial.ShaderIndexer++;
        this._createdShaderName = "custompbr_" + PBRCustomMaterial.ShaderIndexer;
    }
}
/**
 * Index for each created shader
 */ PBRCustomMaterial.ShaderIndexer = 1;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.PBRCustomMaterial", PBRCustomMaterial); //# sourceMappingURL=pbrCustomMaterial.js.map
}),
"[project]/node_modules/@babylonjs/materials/custom/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$materials$2f$custom$2f$customMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/materials/custom/customMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$materials$2f$custom$2f$pbrCustomMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/materials/custom/pbrCustomMaterial.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
}),
"[project]/node_modules/@babylonjs/materials/custom/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$materials$2f$custom$2f$customMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/materials/custom/customMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$materials$2f$custom$2f$pbrCustomMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/materials/custom/pbrCustomMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$materials$2f$custom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/materials/custom/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/@babylonjs/post-processes/asciiArt/asciiart.fragment.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Do not edit.
__turbopack_context__.s({
    "asciiartPixelShader": ()=>asciiartPixelShader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/shaderStore.js [app-client] (ecmascript)");
;
const name = "asciiartPixelShader";
const shader = "varying vec2 vUV;uniform sampler2D textureSampler;uniform sampler2D asciiArtFont;uniform vec4 asciiArtFontInfos;uniform vec4 asciiArtOptions;float getLuminance(vec3 color)\n{return clamp(dot(color,vec3(0.2126,0.7152,0.0722)),0.,1.);}\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) \n{float caracterSize=asciiArtFontInfos.x;float numChar=asciiArtFontInfos.y-1.0;float fontx=asciiArtFontInfos.z;float fonty=asciiArtFontInfos.w;float screenx=asciiArtOptions.x;float screeny=asciiArtOptions.y;float tileX=float(floor((gl_FragCoord.x)/caracterSize))*caracterSize/screenx;float tileY=float(floor((gl_FragCoord.y)/caracterSize))*caracterSize/screeny;vec2 tileUV=vec2(tileX,tileY);vec4 tileColor=texture2D(textureSampler,tileUV);vec4 baseColor=texture2D(textureSampler,vUV);float tileLuminance=getLuminance(tileColor.rgb);float offsetx=(float(floor(tileLuminance*numChar)))*caracterSize/fontx;float offsety=0.0;float x=float(mod(gl_FragCoord.x,caracterSize))/fontx;float y=float(mod(gl_FragCoord.y,caracterSize))/fonty;vec4 finalColor= texture2D(asciiArtFont,vec2(offsetx+x,offsety+(caracterSize/fonty-y)));finalColor.rgb*=tileColor.rgb;finalColor.a=1.0;finalColor= mix(finalColor,tileColor,asciiArtOptions.w);finalColor= mix(finalColor,baseColor,asciiArtOptions.z);gl_FragColor=finalColor;}";
// Sideeffect
if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].ShadersStore[name]) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].ShadersStore[name] = shader;
}
const asciiartPixelShader = {
    name,
    shader
}; //# sourceMappingURL=asciiart.fragment.js.map
}),
"[project]/node_modules/@babylonjs/post-processes/asciiArt/asciiArtPostProcess.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AsciiArtFontTexture": ()=>AsciiArtFontTexture,
    "AsciiArtPostProcess": ()=>AsciiArtPostProcess
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/decorators.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/decorators.serialization.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$baseTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/baseTexture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/texture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/PostProcesses/postProcess.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$Extensions$2f$engine$2e$dynamicTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.dynamicTexture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$asciiArt$2f$asciiart$2e$fragment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/asciiArt/asciiart.fragment.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
class AsciiArtFontTexture extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$baseTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseTexture"] {
    /**
     * Gets the size of one char in the texture (each char fits in size * size space in the texture).
     */ get charSize() {
        return this._charSize;
    }
    /**
     * Gets the max char width of a font.
     * @param font the font to use, use the W3C CSS notation
     * @returns the max char width
     */ _getFontWidth(font) {
        const fontDraw = document.createElement("canvas");
        const ctx = fontDraw.getContext("2d");
        ctx.fillStyle = "white";
        ctx.font = font;
        return ctx.measureText("W").width;
    }
    // More info here: https://videlais.com/2014/03/16/the-many-and-varied-problems-with-measuring-font-height-for-html5-canvas/
    /**
     * Gets the max char height of a font.
     * @param font the font to use, use the W3C CSS notation
     * @returns the max char height
     */ _getFontHeight(font) {
        const fontDraw = document.createElement("canvas");
        const ctx = fontDraw.getContext("2d");
        ctx.fillRect(0, 0, fontDraw.width, fontDraw.height);
        ctx.textBaseline = "top";
        ctx.fillStyle = "white";
        ctx.font = font;
        ctx.fillText("jH|", 0, 0);
        const pixels = ctx.getImageData(0, 0, fontDraw.width, fontDraw.height).data;
        let start = -1;
        let end = -1;
        for(let row = 0; row < fontDraw.height; row++){
            for(let column = 0; column < fontDraw.width; column++){
                const index = (row * fontDraw.width + column) * 4;
                if (pixels[index] === 0) {
                    if (column === fontDraw.width - 1 && start !== -1) {
                        end = row;
                        row = fontDraw.height;
                        break;
                    }
                    continue;
                } else {
                    if (start === -1) {
                        start = row;
                    }
                    break;
                }
            }
        }
        return {
            height: end - start + 1,
            offset: start - 1
        };
    }
    /**
     * Clones the current AsciiArtTexture.
     * @returns the clone of the texture.
     */ clone() {
        return new AsciiArtFontTexture(this.name, this._font, this._text, this.getScene());
    }
    /**
     * Parses a json object representing the texture and returns an instance of it.
     * @param source the source JSON representation
     * @param scene the scene to create the texture for
     * @returns the parsed texture
     */ static Parse(source, scene) {
        const texture = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SerializationHelper"].Parse(()=>new AsciiArtFontTexture(source.name, source.font, source.text, scene), source, scene, null);
        return texture;
    }
    /**
     * Create a new instance of the Ascii Art FontTexture class
     * @param name the name of the texture
     * @param font the font to use, use the W3C CSS notation
     * @param text the caracter set to use in the rendering.
     * @param scene the scene that owns the texture
     */ constructor(name, font, text, scene = null){
        super(scene);
        scene = this.getScene();
        if (!scene) {
            return;
        }
        this.name = name;
        this._text == text;
        this._font == font;
        this.wrapU = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].CLAMP_ADDRESSMODE;
        this.wrapV = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].CLAMP_ADDRESSMODE;
        //this.anisotropicFilteringLevel = 1;
        // Get the font specific info.
        const maxCharHeight = this._getFontHeight(font);
        const maxCharWidth = this._getFontWidth(font);
        this._charSize = Math.max(maxCharHeight.height, maxCharWidth);
        // This is an approximate size, but should always be able to fit at least the maxCharCount.
        const textureWidth = Math.ceil(this._charSize * text.length);
        const textureHeight = this._charSize;
        // Create the texture that will store the font characters.
        this._texture = scene.getEngine().createDynamicTexture(textureWidth, textureHeight, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].NEAREST_SAMPLINGMODE);
        //scene.getEngine().setclamp
        const textureSize = this.getSize();
        // Create a canvas with the final size: the one matching the texture.
        const canvas = document.createElement("canvas");
        canvas.width = textureSize.width;
        canvas.height = textureSize.height;
        const context = canvas.getContext("2d");
        context.textBaseline = "top";
        context.font = font;
        context.fillStyle = "white";
        context.imageSmoothingEnabled = false;
        // Sets the text in the texture.
        for(let i = 0; i < text.length; i++){
            context.fillText(text[i], i * this._charSize, -maxCharHeight.offset);
        }
        // Flush the text in the dynamic texture.
        scene.getEngine().updateDynamicTexture(this._texture, canvas, false, true);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])("font")
], AsciiArtFontTexture.prototype, "_font", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])("text")
], AsciiArtFontTexture.prototype, "_text", void 0);
class AsciiArtPostProcess extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PostProcess"] {
    /**
     * Instantiates a new Ascii Art Post Process.
     * @param name the name to give to the postprocess
     * @camera the camera to apply the post process to.
     * @param camera
     * @param options can either be the font name or an option object following the IAsciiArtPostProcessOptions format
     */ constructor(name, camera, options){
        super(name, "asciiart", [
            "asciiArtFontInfos",
            "asciiArtOptions"
        ], [
            "asciiArtFont"
        ], 1, camera, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].TRILINEAR_SAMPLINGMODE, undefined, true);
        /**
         * This defines the amount you want to mix the "tile" or caracter space colored in the ascii art.
         * This number is defined between 0 and 1;
         */ this.mixToTile = 0;
        /**
         * This defines the amount you want to mix the normal rendering pass in the ascii art.
         * This number is defined between 0 and 1;
         */ this.mixToNormal = 0;
        // Default values.
        let font = "40px Monospace";
        let characterSet = " `-.'_:,\"=^;<+!*?/cL\\zrs7TivJtC{3F)Il(xZfY5S2eajo14[nuyE]P6V9kXpKwGhqAUbOd8#HRDB0$mgMW&Q%N@";
        // Use options.
        if (options) {
            if (typeof options === "string") {
                font = options;
            } else {
                font = options.font || font;
                characterSet = options.characterSet || characterSet;
                this.mixToTile = options.mixToTile || this.mixToTile;
                this.mixToNormal = options.mixToNormal || this.mixToNormal;
            }
        }
        const scene = (camera === null || camera === void 0 ? void 0 : camera.getScene()) || this._scene;
        this._asciiArtFontTexture = new AsciiArtFontTexture(name, font, characterSet, scene);
        const textureSize = this._asciiArtFontTexture.getSize();
        this.onApply = (effect)=>{
            effect.setTexture("asciiArtFont", this._asciiArtFontTexture);
            effect.setFloat4("asciiArtFontInfos", this._asciiArtFontTexture.charSize, characterSet.length, textureSize.width, textureSize.height);
            effect.setFloat4("asciiArtOptions", this.width, this.height, this.mixToNormal, this.mixToTile);
        };
    }
} //# sourceMappingURL=asciiArtPostProcess.js.map
}),
"[project]/node_modules/@babylonjs/post-processes/asciiArt/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$asciiArt$2f$asciiArtPostProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/asciiArt/asciiArtPostProcess.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
;
}),
"[project]/node_modules/@babylonjs/post-processes/asciiArt/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$asciiArt$2f$asciiArtPostProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/asciiArt/asciiArtPostProcess.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$asciiArt$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/asciiArt/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/@babylonjs/post-processes/digitalRain/digitalrain.fragment.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Do not edit.
__turbopack_context__.s({
    "digitalrainPixelShader": ()=>digitalrainPixelShader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/shaderStore.js [app-client] (ecmascript)");
;
const name = "digitalrainPixelShader";
const shader = "varying vec2 vUV;uniform sampler2D textureSampler;uniform sampler2D digitalRainFont;uniform vec4 digitalRainFontInfos;uniform vec4 digitalRainOptions;uniform mat4 matrixSpeed;uniform float cosTimeZeroOne;float getLuminance(vec3 color)\n{return clamp(dot(color,vec3(0.2126,0.7152,0.0722)),0.,1.);}\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) \n{float caracterSize=digitalRainFontInfos.x;float numChar=digitalRainFontInfos.y-1.0;float fontx=digitalRainFontInfos.z;float fonty=digitalRainFontInfos.w;float screenx=digitalRainOptions.x;float screeny=digitalRainOptions.y;float ratio=screeny/fonty;float columnx=float(floor((gl_FragCoord.x)/caracterSize));float tileX=float(floor((gl_FragCoord.x)/caracterSize))*caracterSize/screenx;float tileY=float(floor((gl_FragCoord.y)/caracterSize))*caracterSize/screeny;vec2 tileUV=vec2(tileX,tileY);vec4 tileColor=texture2D(textureSampler,tileUV);vec4 baseColor=texture2D(textureSampler,vUV);float tileLuminance=getLuminance(tileColor.rgb);int st=int(mod(columnx,4.0));float speed=cosTimeZeroOne*(sin(tileX*314.5)*0.5+0.6); \nfloat x=float(mod(gl_FragCoord.x,caracterSize))/fontx;float y=float(mod(speed+gl_FragCoord.y/screeny,1.0));y*=ratio;vec4 finalColor= texture2D(digitalRainFont,vec2(x,1.0-y));vec3 high=finalColor.rgb*(vec3(1.2,1.2,1.2)*pow(1.0-y,30.0));finalColor.rgb*=vec3(pow(tileLuminance,5.0),pow(tileLuminance,1.5),pow(tileLuminance,3.0));finalColor.rgb+=high;finalColor.rgb=clamp(finalColor.rgb,0.,1.);finalColor.a=1.0;finalColor= mix(finalColor,tileColor,digitalRainOptions.w);finalColor= mix(finalColor,baseColor,digitalRainOptions.z);gl_FragColor=finalColor;}";
// Sideeffect
if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].ShadersStore[name]) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].ShadersStore[name] = shader;
}
const digitalrainPixelShader = {
    name,
    shader
}; //# sourceMappingURL=digitalrain.fragment.js.map
}),
"[project]/node_modules/@babylonjs/post-processes/digitalRain/digitalRainPostProcess.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "DigitalRainFontTexture": ()=>DigitalRainFontTexture,
    "DigitalRainPostProcess": ()=>DigitalRainPostProcess
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/decorators.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/decorators.serialization.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.vector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$baseTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/baseTexture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/texture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/PostProcesses/postProcess.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$Extensions$2f$engine$2e$dynamicTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.dynamicTexture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$digitalRain$2f$digitalrain$2e$fragment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/digitalRain/digitalrain.fragment.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
class DigitalRainFontTexture extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$baseTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseTexture"] {
    /**
     * Gets the size of one char in the texture (each char fits in size * size space in the texture).
     */ get charSize() {
        return this._charSize;
    }
    /**
     * Gets the max char width of a font.
     * @param font the font to use, use the W3C CSS notation
     * @returns the max char width
     */ _getFontWidth(font) {
        const fontDraw = document.createElement("canvas");
        const ctx = fontDraw.getContext("2d");
        ctx.fillStyle = "white";
        ctx.font = font;
        return ctx.measureText("W").width;
    }
    // More info here: https://videlais.com/2014/03/16/the-many-and-varied-problems-with-measuring-font-height-for-html5-canvas/
    /**
     * Gets the max char height of a font.
     * @param font the font to use, use the W3C CSS notation
     * @returns the max char height
     */ _getFontHeight(font) {
        const fontDraw = document.createElement("canvas");
        const ctx = fontDraw.getContext("2d");
        ctx.fillRect(0, 0, fontDraw.width, fontDraw.height);
        ctx.textBaseline = "top";
        ctx.fillStyle = "white";
        ctx.font = font;
        ctx.fillText("jH|", 0, 0);
        const pixels = ctx.getImageData(0, 0, fontDraw.width, fontDraw.height).data;
        let start = -1;
        let end = -1;
        for(let row = 0; row < fontDraw.height; row++){
            for(let column = 0; column < fontDraw.width; column++){
                const index = (row * fontDraw.width + column) * 4;
                if (pixels[index] === 0) {
                    if (column === fontDraw.width - 1 && start !== -1) {
                        end = row;
                        row = fontDraw.height;
                        break;
                    }
                    continue;
                } else {
                    if (start === -1) {
                        start = row;
                    }
                    break;
                }
            }
        }
        return {
            height: end - start + 1,
            offset: start - 1
        };
    }
    /**
     * Clones the current DigitalRainFontTexture.
     * @returns the clone of the texture.
     */ clone() {
        return new DigitalRainFontTexture(this.name, this._font, this._text, this.getScene());
    }
    /**
     * Parses a json object representing the texture and returns an instance of it.
     * @param source the source JSON representation
     * @param scene the scene to create the texture for
     * @returns the parsed texture
     */ static Parse(source, scene) {
        const texture = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SerializationHelper"].Parse(()=>new DigitalRainFontTexture(source.name, source.font, source.text, scene), source, scene, null);
        return texture;
    }
    /**
     * Create a new instance of the Digital Rain FontTexture class
     * @param name the name of the texture
     * @param font the font to use, use the W3C CSS notation
     * @param text the caracter set to use in the rendering.
     * @param scene the scene that owns the texture
     */ constructor(name, font, text, scene = null){
        super(scene);
        scene = this.getScene();
        if (!scene) {
            return;
        }
        this.name = name;
        this._text == text;
        this._font == font;
        this.wrapU = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].CLAMP_ADDRESSMODE;
        this.wrapV = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].CLAMP_ADDRESSMODE;
        // Get the font specific info.
        const maxCharHeight = this._getFontHeight(font);
        const maxCharWidth = this._getFontWidth(font);
        this._charSize = Math.max(maxCharHeight.height, maxCharWidth);
        // This is an approximate size, but should always be able to fit at least the maxCharCount.
        const textureWidth = this._charSize;
        const textureHeight = Math.ceil(this._charSize * text.length);
        // Create the texture that will store the font characters.
        this._texture = scene.getEngine().createDynamicTexture(textureWidth, textureHeight, false, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].NEAREST_SAMPLINGMODE);
        //scene.getEngine().setclamp
        const textureSize = this.getSize();
        // Create a canvas with the final size: the one matching the texture.
        const canvas = document.createElement("canvas");
        canvas.width = textureSize.width;
        canvas.height = textureSize.height;
        const context = canvas.getContext("2d");
        context.textBaseline = "top";
        context.font = font;
        context.fillStyle = "white";
        context.imageSmoothingEnabled = false;
        // Sets the text in the texture.
        for(let i = 0; i < text.length; i++){
            context.fillText(text[i], 0, i * this._charSize - maxCharHeight.offset);
        }
        // Flush the text in the dynamic texture.
        scene.getEngine().updateDynamicTexture(this._texture, canvas, false, true);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])("font")
], DigitalRainFontTexture.prototype, "_font", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])("text")
], DigitalRainFontTexture.prototype, "_text", void 0);
class DigitalRainPostProcess extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PostProcess"] {
    /**
     * Instantiates a new Digital Rain Post Process.
     * @param name the name to give to the postprocess
     * @camera the camera to apply the post process to.
     * @param camera
     * @param options can either be the font name or an option object following the IDigitalRainPostProcessOptions format
     */ constructor(name, camera, options){
        super(name, "digitalrain", [
            "digitalRainFontInfos",
            "digitalRainOptions",
            "cosTimeZeroOne",
            "matrixSpeed"
        ], [
            "digitalRainFont"
        ], 1.0, camera, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"].TRILINEAR_SAMPLINGMODE, undefined, true);
        /**
         * This defines the amount you want to mix the "tile" or caracter space colored in the digital rain.
         * This number is defined between 0 and 1;
         */ this.mixToTile = 0;
        /**
         * This defines the amount you want to mix the normal rendering pass in the digital rain.
         * This number is defined between 0 and 1;
         */ this.mixToNormal = 0;
        /**
         * Speed of the effect
         */ this.speed = 0.003;
        // Default values.
        let font = "15px Monospace";
        const characterSet = "古池や蛙飛び込む水の音ふるいけやかわずとびこむみずのおと初しぐれ猿も小蓑をほしげ也はつしぐれさるもこみのをほしげなり江戸の雨何石呑んだ時鳥えどのあめなんごくのんだほととぎす";
        // Use options.
        if (options) {
            if (typeof options === "string") {
                font = options;
            } else {
                font = options.font || font;
                this.mixToTile = options.mixToTile || this.mixToTile;
                this.mixToNormal = options.mixToNormal || this.mixToNormal;
            }
        }
        const scene = (camera === null || camera === void 0 ? void 0 : camera.getScene()) || null;
        this._digitalRainFontTexture = new DigitalRainFontTexture(name, font, characterSet, scene);
        const textureSize = this._digitalRainFontTexture.getSize();
        let alpha = 0.0;
        let cosTimeZeroOne = 0.0;
        const matrix = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Matrix"].FromValues(Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random(), Math.random());
        this.onApply = (effect)=>{
            effect.setTexture("digitalRainFont", this._digitalRainFontTexture);
            effect.setFloat4("digitalRainFontInfos", this._digitalRainFontTexture.charSize, characterSet.length, textureSize.width, textureSize.height);
            effect.setFloat4("digitalRainOptions", this.width, this.height, this.mixToNormal, this.mixToTile);
            effect.setMatrix("matrixSpeed", matrix);
            alpha += this.speed;
            cosTimeZeroOne = alpha;
            effect.setFloat("cosTimeZeroOne", cosTimeZeroOne);
        };
    }
} //# sourceMappingURL=digitalRainPostProcess.js.map
}),
"[project]/node_modules/@babylonjs/post-processes/digitalRain/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$digitalRain$2f$digitalRainPostProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/digitalRain/digitalRainPostProcess.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
;
}),
"[project]/node_modules/@babylonjs/post-processes/digitalRain/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$digitalRain$2f$digitalRainPostProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/digitalRain/digitalRainPostProcess.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$digitalRain$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/digitalRain/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/@babylonjs/post-processes/edgeDetection/edgeDetection.fragment.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Do not edit.
__turbopack_context__.s({
    "edgeDetectionPixelShader": ()=>edgeDetectionPixelShader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/shaderStore.js [app-client] (ecmascript)");
;
const name = "edgeDetectionPixelShader";
const shader = "precision highp float;varying vec2 vUV;uniform sampler2D textureSampler;uniform sampler2D normalSampler;uniform sampler2D depthSampler;uniform float width;uniform float height;uniform vec3 edgeColor;uniform float edgeIntensity;uniform float edgeWidth;uniform int renderMode; \nvec3 boxBlur(sampler2D sampler,vec2 uv,vec2 texelSize) {vec3 result=vec3(0.0);for (int x=-1; x<=1; x++) {for (int y=-1; y<=1; y++) {vec2 offset=vec2(float(x),float(y))*texelSize;result+=texture2D(sampler,uv+offset).rgb;}}\nreturn result/9.0;}\nvoid main(void) {vec2 texelSize=vec2(1.0/width,1.0/height);vec3 originalColor=texture2D(textureSampler,vUV).rgb;if (renderMode==1 || renderMode==2 || renderMode==3) {if (length(originalColor)==0.0) {originalColor=vec3(1.0,1.0,1.0); }\nif (originalColor.r==1.0 && originalColor.g==0.0 && originalColor.b==0.0) {originalColor=vec3(1.0,1.0,1.0); }}\nvec3 normal=texture2D(normalSampler,vUV).rgb;float depth=texture2D(depthSampler,vUV).r;float edgeStrength=0.0;int range=int(edgeWidth*8.0); \nfor (int x=-range; x<=range; x++) {for (int y=-range; y<=range; y++) {if (x==0 && y==0) {continue;}\nvec3 neighborNormal=texture2D(normalSampler,vUV+texelSize*vec2(float(x),float(y))).rgb;float neighborDepth=texture2D(depthSampler,vUV+texelSize*vec2(float(x),float(y))).r;float normalDiff=length(neighborNormal-normal);float depthDiff=abs(neighborDepth-depth);edgeStrength=max(edgeStrength,max(normalDiff,depthDiff));}}\nedgeStrength=smoothstep(edgeWidth,edgeWidth+edgeIntensity,edgeStrength);vec3 finalColor=mix(originalColor,edgeColor,edgeStrength);gl_FragColor=vec4(finalColor,1.0);}";
// Sideeffect
if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].ShadersStore[name]) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].ShadersStore[name] = shader;
}
const edgeDetectionPixelShader = {
    name,
    shader
}; //# sourceMappingURL=edgeDetection.fragment.js.map
}),
"[project]/node_modules/@babylonjs/post-processes/edgeDetection/edgeDetectionPostProcess.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "EdgeDetectionPostProcess": ()=>EdgeDetectionPostProcess
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/logger.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/PostProcesses/postProcess.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/constants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Rendering$2f$geometryBufferRendererSceneComponent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Rendering/geometryBufferRendererSceneComponent.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.color.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/decorators.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/decorators.serialization.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$engineStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/engineStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$renderTargetTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/renderTargetTexture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$edgeDetection$2f$edgeDetection$2e$fragment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/edgeDetection/edgeDetection.fragment.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
class EdgeDetectionPostProcess extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PostProcess"] {
    /**
     * Get the current class name of the current effect
     * @returns "EdgeDetectionPostProcess"
     */ getClassName() {
        return "EdgeDetectionPostProcess";
    }
    /**
     * Support test.
     */ static get IsSupported() {
        const engine = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$engineStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EngineStore"].LastCreatedEngine;
        if (!engine) {
            return false;
        }
        return engine.getCaps().drawBuffersExtension;
    }
    /**
     * @internal
     */ static _Parse(parsedPostProcess, targetCamera, scene, rootUrl) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SerializationHelper"].Parse(()=>new EdgeDetectionPostProcess(parsedPostProcess.name, scene, parsedPostProcess.options, targetCamera, parsedPostProcess.renderTargetSamplingMode, parsedPostProcess.textureType, parsedPostProcess.reusable), parsedPostProcess, scene, rootUrl);
    }
    /**
     * Creates a new instance of EdgeDetectionPostProcess.
     * @param name The name of the effect.
     * @param scene The scene where the edge detection post-process will be applied.
     * @param options The required width/height ratio or specific options for the post-process.
     * @param camera The camera to apply the post-process to.
     * @param samplingMode The sampling mode to be used when computing the pass. (default: TEXTURE_NEAREST_NEAREST)
     * @param reusable If the post-process can be reused on the same frame. (default: false)
     * @param textureType The type of textures used when performing the post-process. (default: TEXTURETYPE_HALF_FLOAT)
     */ constructor(name, scene, options, camera, samplingMode, reusable, textureType = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Constants"].TEXTURETYPE_UNSIGNED_BYTE){
        super(name, "edgeDetection", [
            "width",
            "height",
            "edgeColor",
            "edgeIntensity",
            "edgeWidth",
            "renderMode"
        ], [
            "normalSampler",
            "depthSampler"
        ], options, camera, samplingMode, scene.getEngine(), reusable, null, textureType);
        /**
         * Defines the color of the detected edges.
         */ this.edgeColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0, 0, 0);
        /**
         * Defines the intensity of the detected edges.
         * Higher values result in more pronounced edges.
         * default: 0.2  (min:0, max:1)
         */ this.edgeIntensity = 0.2;
        /**
         * Defines the width of the detected edges.
         * Higher values result in thicker edges.
         * default: 0.2 (min:0.125, max:1)
         */ this.edgeWidth = 0.2;
        /**
         * Defines the render mode.
         * default: 0
         * 0: general, 1: normal, 2: depth, 3: outline only
         */ this.renderMode = 0;
        this._geometryBufferRenderer = scene.enableGeometryBufferRenderer();
        if (!this._geometryBufferRenderer) {
            // Geometry buffer renderer is not supported. So, work as a passthrough.
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Error("Geometry Buffer Renderer support is required for this post-process.");
        } else {
            const h1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$renderTargetTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RenderTargetTexture"]("h1", {
                width: this.width,
                height: this.height
            }, scene, {
                samplingMode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Constants"].TEXTURE_NEAREST_NEAREST,
                generateMipMaps: false,
                generateDepthBuffer: false,
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Constants"].TEXTURETYPE_HALF_FLOAT
            });
            // Geometry buffer renderer is supported.
            this.onApply = (effect)=>{
                effect.setFloat("width", this.width);
                effect.setFloat("height", this.height);
                effect.setFloat("edgeIntensity", this.edgeIntensity);
                effect.setFloat("edgeWidth", this.edgeWidth);
                effect.setColor3("edgeColor", this.edgeColor);
                const normalTexture = this._geometryBufferRenderer.getGBuffer().textures[1];
                const depthTexture = this._geometryBufferRenderer.getGBuffer().textures[0];
                effect.setTexture("normalSampler", normalTexture);
                effect.setTexture("depthSampler", depthTexture);
                switch(this.renderMode){
                    case 0:
                        break;
                    case 1:
                        effect.setTexture("textureSampler", this._geometryBufferRenderer.getGBuffer().textures[1]);
                        effect.setFloat("edgeWidth", 0);
                        break;
                    case 2:
                        effect.setTexture("textureSampler", this._geometryBufferRenderer.getGBuffer().textures[0]);
                        effect.setFloat("edgeWidth", 0);
                        break;
                    case 3:
                        effect.setTexture("textureSampler", h1);
                        break;
                }
                effect.setInt("renderMode", this.renderMode);
            };
        }
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])()
], EdgeDetectionPostProcess.prototype, "edgeColor", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])()
], EdgeDetectionPostProcess.prototype, "edgeIntensity", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])()
], EdgeDetectionPostProcess.prototype, "edgeWidth", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])()
], EdgeDetectionPostProcess.prototype, "renderMode", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.EdgeDetectionPostProcess", EdgeDetectionPostProcess); //# sourceMappingURL=edgeDetectionPostProcess.js.map
}),
"[project]/node_modules/@babylonjs/post-processes/edgeDetection/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$edgeDetection$2f$edgeDetectionPostProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/edgeDetection/edgeDetectionPostProcess.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
;
}),
"[project]/node_modules/@babylonjs/post-processes/edgeDetection/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$edgeDetection$2f$edgeDetectionPostProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/edgeDetection/edgeDetectionPostProcess.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$edgeDetection$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/edgeDetection/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/@babylonjs/post-processes/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/* eslint-disable @typescript-eslint/no-restricted-imports */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$asciiArt$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/asciiArt/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$digitalRain$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/digitalRain/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$edgeDetection$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/edgeDetection/index.js [app-client] (ecmascript) <module evaluation>"); //# sourceMappingURL=index.js.map
;
;
;
}),
"[project]/node_modules/@babylonjs/post-processes/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$asciiArt$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/asciiArt/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$digitalRain$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/digitalRain/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$edgeDetection$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/edgeDetection/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/index.js [app-client] (ecmascript) <locals>");
}),
}]);

//# sourceMappingURL=node_modules_f7f89866._.js.map