{"version": 3, "file": "arcRotateCameraVRDeviceOrientationInput.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/Inputs/arcRotateCameraVRDeviceOrientationInput.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AACrE,OAAO,EAAE,4BAA4B,EAAE,MAAM,4CAA4C,CAAC;AAC1F,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAczC;;;GAGG;AACH,4BAA4B,CAAC,SAAS,CAAC,sBAAsB,GAAG;IAC5D,IAAI,CAAC,GAAG,CAAC,IAAI,uCAAuC,EAAE,CAAC,CAAC;IACxD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,OAAO,uCAAuC;IAsBhD;;OAEG;IACH;QAnBA;;WAEG;QACI,oBAAe,GAAG,CAAC,CAAC;QAE3B;;WAEG;QACI,oBAAe,GAAG,CAAC,CAAC;QAEnB,WAAM,GAAG,CAAC,CAAC;QACX,WAAM,GAAG,CAAC,CAAC;QACX,WAAM,GAAG,KAAK,CAAC;QAQnB,IAAI,CAAC,yBAAyB,GAAG,CAAC,GAA2B,EAAE,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpG,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QAErE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAE5C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC;QAEtE,IAAI,UAAU,EAAE,CAAC;YACb,wBAAwB;YACxB,IAAI,OAAO,sBAAsB,KAAK,WAAW,IAAI,OAAa,sBAAuB,CAAC,iBAAiB,KAAK,UAAU,EAAE,CAAC;gBACnH,sBAAuB;qBACxB,iBAAiB,EAAE;oBACpB,0CAA0C;qBACzC,IAAI,CAAC,CAAC,QAAgB,EAAE,EAAE;oBACvB,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;wBACzB,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;oBACrF,CAAC;yBAAM,CAAC;wBACJ,KAAK,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;oBAC1C,CAAC;gBACL,CAAC,CAAC;oBACF,0CAA0C;qBACzC,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;oBAClB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC;YACX,CAAC;iBAAM,CAAC;gBACJ,UAAU,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACrF,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,GAA2B;QAClD,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;QAC1D,CAAC;QAED,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;QAC1D,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YAEpB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC;YACpC,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACvE,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,MAAM,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,yCAAyC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,qBAAqB,CAAC;IACjC,CAAC;CACJ;AAEK,gBAAiB,CAAC,yCAAyC,CAAC,GAAG,uCAAuC,CAAC", "sourcesContent": ["import type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport { ArcRotateCameraInputsManager } from \"../../Cameras/arcRotateCameraInputsManager\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n// Module augmentation to abstract orientation inputs from camera.\r\ndeclare module \"../../Cameras/arcRotateCameraInputsManager\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface ArcRotateCameraInputsManager {\r\n        /**\r\n         * Add orientation input support to the input manager.\r\n         * @returns the current input manager\r\n         */\r\n        addVRDeviceOrientation(): ArcRotateCameraInputsManager;\r\n    }\r\n}\r\n\r\n/**\r\n * Add orientation input support to the input manager.\r\n * @returns the current input manager\r\n */\r\nArcRotateCameraInputsManager.prototype.addVRDeviceOrientation = function (): ArcRotateCameraInputsManager {\r\n    this.add(new ArcRotateCameraVRDeviceOrientationInput());\r\n    return this;\r\n};\r\n\r\n/**\r\n * Manage the device orientation inputs (gyroscope) to control an arc rotate camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class ArcRotateCameraVRDeviceOrientationInput implements ICameraInput<ArcRotateCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: ArcRotateCamera;\r\n\r\n    /**\r\n     * Defines a correction factor applied on the alpha value retrieved from the orientation events.\r\n     */\r\n    public alphaCorrection = 1;\r\n\r\n    /**\r\n     * Defines a correction factor applied on the gamma value retrieved from the orientation events.\r\n     */\r\n    public gammaCorrection = 1;\r\n\r\n    private _alpha = 0;\r\n    private _gamma = 0;\r\n    private _dirty = false;\r\n\r\n    private _deviceOrientationHandler: (evt: DeviceOrientationEvent) => void;\r\n\r\n    /**\r\n     * Instantiate a new ArcRotateCameraVRDeviceOrientationInput.\r\n     */\r\n    constructor() {\r\n        this._deviceOrientationHandler = (evt: DeviceOrientationEvent) => this._onOrientationEvent(evt);\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n\r\n        this.camera.attachControl(noPreventDefault);\r\n\r\n        const hostWindow = this.camera.getScene().getEngine().getHostWindow();\r\n\r\n        if (hostWindow) {\r\n            // check iOS 13+ support\r\n            if (typeof DeviceOrientationEvent !== \"undefined\" && typeof (<any>DeviceOrientationEvent).requestPermission === \"function\") {\r\n                (<any>DeviceOrientationEvent)\r\n                    .requestPermission()\r\n                    // eslint-disable-next-line github/no-then\r\n                    .then((response: string) => {\r\n                        if (response === \"granted\") {\r\n                            hostWindow.addEventListener(\"deviceorientation\", this._deviceOrientationHandler);\r\n                        } else {\r\n                            Tools.Warn(\"Permission not granted.\");\r\n                        }\r\n                    })\r\n                    // eslint-disable-next-line github/no-then\r\n                    .catch((error: any) => {\r\n                        Tools.Error(error);\r\n                    });\r\n            } else {\r\n                hostWindow.addEventListener(\"deviceorientation\", this._deviceOrientationHandler);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _onOrientationEvent(evt: DeviceOrientationEvent): void {\r\n        if (evt.alpha !== null) {\r\n            this._alpha = (+evt.alpha | 0) * this.alphaCorrection;\r\n        }\r\n\r\n        if (evt.gamma !== null) {\r\n            this._gamma = (+evt.gamma | 0) * this.gammaCorrection;\r\n        }\r\n        this._dirty = true;\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        if (this._dirty) {\r\n            this._dirty = false;\r\n\r\n            if (this._gamma < 0) {\r\n                this._gamma = 180 + this._gamma;\r\n            }\r\n\r\n            this.camera.alpha = (((-this._alpha / 180.0) * Math.PI) % Math.PI) * 2;\r\n            this.camera.beta = (this._gamma / 180.0) * Math.PI;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        window.removeEventListener(\"deviceorientation\", this._deviceOrientationHandler);\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ArcRotateCameraVRDeviceOrientationInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"VRDeviceOrientation\";\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"ArcRotateCameraVRDeviceOrientationInput\"] = ArcRotateCameraVRDeviceOrientationInput;\r\n"]}