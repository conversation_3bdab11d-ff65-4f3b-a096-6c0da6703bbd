{"version": 3, "file": "abstractEngine.alpha.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.alpha.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAczC,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,QAAgB,EAAE,cAAsB,CAAC;IAC3F,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE,CAAC;QAChD,OAAO;IACX,CAAC;IAED,QAAQ,QAAQ,EAAE,CAAC;QACf,KAAK,SAAS,CAAC,kBAAkB;YAC7B,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;YAC3H,MAAM;QACV,KAAK,SAAS,CAAC,wBAAwB;YACnC,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,0BAA0B,EAAE,SAAS,CAAC,0BAA0B,EAAE,WAAW,CAAC,CAAC;YACrI,MAAM;QACV,KAAK,SAAS,CAAC,+BAA+B;YAC1C,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,kCAAkC,EAAE,SAAS,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC;YACrJ,MAAM;QACV,KAAK,SAAS,CAAC,kBAAkB;YAC7B,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;YAC3H,MAAM;QACV,KAAK,SAAS,CAAC,kBAAkB;YAC7B,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;YAC3H,MAAM;QACV,KAAK,SAAS,CAAC,qBAAqB;YAChC,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAAC,SAAS,CAAC,qBAAqB,EAAE,SAAS,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;YAC3H,MAAM;IACd,CAAC;IACD,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC;AAChD,CAAC,CAAC", "sourcesContent": ["import { AbstractEngine } from \"../abstractEngine\";\r\nimport { Constants } from \"../constants\";\r\n\r\ndeclare module \"../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Sets the current alpha equation\r\n         * @param equation defines the equation to use (one of the Engine.ALPHA_EQUATION_XXX)\r\n         * @param targetIndex defines the index of the target to set the equation for (default is 0)\r\n         */\r\n        setAlphaEquation(equation: number, targetIndex?: number): void;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype.setAlphaEquation = function (equation: number, targetIndex: number = 0): void {\r\n    if (this._alphaEquation[targetIndex] === equation) {\r\n        return;\r\n    }\r\n\r\n    switch (equation) {\r\n        case Constants.ALPHA_EQUATION_ADD:\r\n            this._alphaState.setAlphaEquationParameters(Constants.GL_ALPHA_EQUATION_ADD, Constants.GL_ALPHA_EQUATION_ADD, targetIndex);\r\n            break;\r\n        case Constants.ALPHA_EQUATION_SUBSTRACT:\r\n            this._alphaState.setAlphaEquationParameters(Constants.GL_ALPHA_EQUATION_SUBTRACT, Constants.GL_ALPHA_EQUATION_SUBTRACT, targetIndex);\r\n            break;\r\n        case Constants.ALPHA_EQUATION_REVERSE_SUBTRACT:\r\n            this._alphaState.setAlphaEquationParameters(Constants.GL_ALPHA_EQUATION_REVERSE_SUBTRACT, Constants.GL_ALPHA_EQUATION_REVERSE_SUBTRACT, targetIndex);\r\n            break;\r\n        case Constants.ALPHA_EQUATION_MAX:\r\n            this._alphaState.setAlphaEquationParameters(Constants.GL_ALPHA_EQUATION_MAX, Constants.GL_ALPHA_EQUATION_MAX, targetIndex);\r\n            break;\r\n        case Constants.ALPHA_EQUATION_MIN:\r\n            this._alphaState.setAlphaEquationParameters(Constants.GL_ALPHA_EQUATION_MIN, Constants.GL_ALPHA_EQUATION_MIN, targetIndex);\r\n            break;\r\n        case Constants.ALPHA_EQUATION_DARKEN:\r\n            this._alphaState.setAlphaEquationParameters(Constants.GL_ALPHA_EQUATION_MIN, Constants.GL_ALPHA_EQUATION_ADD, targetIndex);\r\n            break;\r\n    }\r\n    this._alphaEquation[targetIndex] = equation;\r\n};\r\n"]}