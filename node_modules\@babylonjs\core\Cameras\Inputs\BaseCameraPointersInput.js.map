{"version": 3, "file": "BaseCameraPointersInput.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/Inputs/BaseCameraPointersInput.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAElD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAIzC,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAG/D;;;;GAIG;AACH,MAAM,OAAgB,uBAAuB;IAA7C;QAoBI;;WAEG;QACK,+BAA0B,GAAW,CAAC,CAAC,CAAC;QAGhD;;WAEG;QAEI,YAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAyU/B,CAAC;IAvUG;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,OAAO,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;QACzC,IAAI,4BAA4B,GAAG,CAAC,CAAC;QACrC,IAAI,6BAA6B,GAA2B,IAAI,CAAC;QAEjE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,EAAE;YACvB,MAAM,GAAG,GAAkB,CAAC,CAAC,KAAK,CAAC;YACnC,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,KAAK,OAAO,CAAC;YAE5C,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACtF,OAAO;YACX,CAAC;YAED,MAAM,UAAU,GAAgB,GAAG,CAAC,MAAM,CAAC;YAE3C,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC;YAC1B,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC;YAC9B,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC;YAEnC,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACvB,MAAM,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC;gBAC9B,MAAM,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC;gBAE9B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACxB,CAAC;iBAAM,IACH,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW;gBACxC,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,gBAAgB;gBAC7C,OAAO;gBACP,IAAI,CAAC,OAAO,EAAE,SAAS,KAAK,GAAG,CAAC,SAAS;gBACzC,IAAI,CAAC,OAAO,EAAE,SAAS,KAAK,GAAG,CAAC,SAAS,EAC3C,CAAC;gBACC,OAAO,CAAC,4EAA4E;YACxF,CAAC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,0BAA0B,KAAK,CAAC,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC;gBACzG,IAAI,CAAC;oBACD,UAAU,EAAE,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACjD,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACT,wDAAwD;gBAC5D,CAAC;gBAED,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;oBACxB,IAAI,CAAC,OAAO,GAAG;wBACX,CAAC,EAAE,GAAG,CAAC,OAAO;wBACd,CAAC,EAAE,GAAG,CAAC,OAAO;wBACd,SAAS,EAAE,GAAG,CAAC,SAAS;wBACxB,IAAI,EAAE,GAAG,CAAC,WAAW;qBACxB,CAAC;gBACN,CAAC;qBAAM,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;oBAC/B,IAAI,CAAC,OAAO,GAAG;wBACX,CAAC,EAAE,GAAG,CAAC,OAAO;wBACd,CAAC,EAAE,GAAG,CAAC,OAAO;wBACd,SAAS,EAAE,GAAG,CAAC,SAAS;wBACxB,IAAI,EAAE,GAAG,CAAC,WAAW;qBACxB,CAAC;gBACN,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,0DAA0D;gBACtE,CAAC;gBAED,IAAI,IAAI,CAAC,0BAA0B,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;oBACrD,IAAI,CAAC,0BAA0B,GAAG,GAAG,CAAC,SAAS,CAAC;gBACpD,CAAC;gBACD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAEvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACpB,GAAG,CAAC,cAAc,EAAE,CAAC;oBACrB,IAAI,OAAO,EAAE,CAAC;wBACV,OAAO,CAAC,KAAK,EAAE,CAAC;oBACpB,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;gBACvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,0BAA0B,KAAK,GAAG,CAAC,SAAS,IAAI,OAAO,CAAC,EAAE,CAAC;gBAClH,IAAI,CAAC;oBACD,UAAU,EAAE,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACrD,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACT,+BAA+B;gBACnC,CAAC;gBAED,IAAI,CAAC,OAAO,EAAE,CAAC;oBACX,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,iCAAiC;gBAC1D,CAAC;gBAED,gFAAgF;gBAChF,kFAAkF;gBAClF,kDAAkD;gBAClD,mEAAmE;gBACnE,kFAAkF;gBAClF,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACvC,CAAC;qBAAM,CAAC;oBACJ,yEAAyE;oBACzE,6DAA6D;oBAC7D,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;wBAC1E,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACxB,CAAC;yBAAM,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;wBACjF,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACxB,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACvC,CAAC;gBACL,CAAC;gBAED,IAAI,4BAA4B,KAAK,CAAC,IAAI,6BAA6B,EAAE,CAAC;oBACtE,gEAAgE;oBAChE,sBAAsB;oBACtB,IAAI,CAAC,YAAY,CACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,EACZ,4BAA4B,EAC5B,CAAC,EAAE,uBAAuB;oBAC1B,6BAA6B,EAC7B,IAAI,CAAC,wBAAwB;qBAChC,CAAC;oBACF,4BAA4B,GAAG,CAAC,CAAC;oBACjC,6BAA6B,GAAG,IAAI,CAAC;gBACzC,CAAC;gBAED,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAErB,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACpB,GAAG,CAAC,cAAc,EAAE,CAAC;gBACzB,CAAC;YACL,CAAC;iBAAM,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,WAAW,EAAE,CAAC;gBAClD,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACpB,GAAG,CAAC,cAAc,EAAE,CAAC;gBACzB,CAAC;gBAED,kBAAkB;gBAClB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;oBACxC,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC7C,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC7C,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;oBAC7B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;oBAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBACjD,CAAC;gBACD,0BAA0B;qBACrB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACpC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;oBAClF,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;oBACnB,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;oBACnB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;oBAC9C,MAAM,oBAAoB,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;oBAC3D,MAAM,qBAAqB,GAAG;wBAC1B,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;wBACxC,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;wBACxC,SAAS,EAAE,GAAG,CAAC,SAAS;wBACxB,IAAI,EAAE,CAAC,CAAC,IAAI;qBACf,CAAC;oBAEF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,4BAA4B,EAAE,oBAAoB,EAAE,6BAA6B,EAAE,qBAAqB,CAAC,CAAC;oBAExJ,6BAA6B,GAAG,qBAAqB,CAAC;oBACtD,4BAA4B,GAAG,oBAAoB,CAAC;gBACxD,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM;aACvB,QAAQ,EAAE;aACV,aAAa,CAAC,yBAAyB,CACpC,IAAI,CAAC,aAAa,EAClB,iBAAiB,CAAC,WAAW,GAAG,iBAAiB,CAAC,SAAS,GAAG,iBAAiB,CAAC,WAAW,GAAG,iBAAiB,CAAC,gBAAgB,CACnI,CAAC;QAEN,IAAI,CAAC,YAAY,GAAG,GAAG,EAAE;YACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACnC,4BAA4B,GAAG,CAAC,CAAC;YACjC,6BAA6B,GAAG,IAAI,CAAC;YACrC,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,GAAG,CAAC,GAAU,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAmB,CAAC,CAAC;QAEhF,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC;QAEtE,IAAI,UAAU,EAAE,CAAC;YACb,KAAK,CAAC,qBAAqB,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAC5F,CAAC;IACL,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC;YACtE,IAAI,UAAU,EAAE,CAAC;gBACb,KAAK,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;YAC9F,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YAEtB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,eAAe,EAAE,CAAC;gBAC1E,IAAI,YAAY,EAAE,CAAC;oBACf,YAAY,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC3E,CAAC;YACL,CAAC;YAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,yBAAyB,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;OAIG;IACH,6DAA6D;IACtD,WAAW,CAAC,IAAY,IAAG,CAAC;IAEnC,6DAA6D;IAC7D;;;;;;OAMG;IACI,OAAO,CAAC,KAA6B,EAAE,OAAe,EAAE,OAAe,IAAS,CAAC;IAExF;;;;;;;;;OASG;IACH,6DAA6D;IACtD,YAAY,CACf,OAA+B,EAC/B,OAA+B,EAC/B,4BAAoC,EACpC,oBAA4B,EAC5B,6BAAqD,EACrD,qBAA6C,IACxC,CAAC;IAEV;;;;OAIG;IACI,aAAa,CAAC,GAAiB;QAClC,GAAG,CAAC,cAAc,EAAE,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,IAAmB,IAAS,CAAC;IAEjD;;;;;OAKG;IACI,UAAU,CAAC,IAAmB,IAAS,CAAC;IAE/C;;;OAGG;IACI,WAAW,KAAU,CAAC;CAOhC;AAzUU;IADN,SAAS,EAAE;wDACe", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { EventState, Observer } from \"../../Misc/observable\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport type { PointerInfo, PointerTouch } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport type { IPointerEvent } from \"../../Events/deviceInputEvents\";\r\n\r\n/**\r\n * Base class for Camera Pointer Inputs.\r\n * See FollowCameraPointersInput in src/Cameras/Inputs/followCameraPointersInput.ts\r\n * for example usage.\r\n */\r\nexport abstract class BaseCameraPointersInput implements ICameraInput<Camera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public abstract camera: Camera;\r\n\r\n    /**\r\n     * Whether keyboard modifier keys are pressed at time of last mouse event.\r\n     */\r\n    protected _altKey: boolean;\r\n    protected _ctrlKey: boolean;\r\n    protected _metaKey: boolean;\r\n    protected _shiftKey: boolean;\r\n\r\n    /**\r\n     * Which mouse buttons were pressed at time of last mouse event.\r\n     * https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\r\n     */\r\n    protected _buttonsPressed: number;\r\n\r\n    /**\r\n     * Which pointer ID is currently down (only for mouse events, not used for touch events)\r\n     */\r\n    private _currentMousePointerIdDown: number = -1;\r\n    private _contextMenuBind: EventListener;\r\n\r\n    /**\r\n     * Defines the buttons associated with the input to handle camera move.\r\n     */\r\n    @serialize()\r\n    public buttons = [0, 1, 2];\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        const engine = this.camera.getEngine();\r\n        const element = engine.getInputElement();\r\n        let previousPinchSquaredDistance = 0;\r\n        let previousMultiTouchPanPosition: Nullable<PointerTouch> = null;\r\n\r\n        this._pointA = null;\r\n        this._pointB = null;\r\n\r\n        this._altKey = false;\r\n        this._ctrlKey = false;\r\n        this._metaKey = false;\r\n        this._shiftKey = false;\r\n        this._buttonsPressed = 0;\r\n\r\n        this._pointerInput = (p) => {\r\n            const evt = <IPointerEvent>p.event;\r\n            const isTouch = evt.pointerType === \"touch\";\r\n\r\n            if (p.type !== PointerEventTypes.POINTERMOVE && this.buttons.indexOf(evt.button) === -1) {\r\n                return;\r\n            }\r\n\r\n            const srcElement = <HTMLElement>evt.target;\r\n\r\n            this._altKey = evt.altKey;\r\n            this._ctrlKey = evt.ctrlKey;\r\n            this._metaKey = evt.metaKey;\r\n            this._shiftKey = evt.shiftKey;\r\n            this._buttonsPressed = evt.buttons;\r\n\r\n            if (engine.isPointerLock) {\r\n                const offsetX = evt.movementX;\r\n                const offsetY = evt.movementY;\r\n\r\n                this.onTouch(null, offsetX, offsetY);\r\n                this._pointA = null;\r\n                this._pointB = null;\r\n            } else if (\r\n                p.type !== PointerEventTypes.POINTERDOWN &&\r\n                p.type !== PointerEventTypes.POINTERDOUBLETAP &&\r\n                isTouch &&\r\n                this._pointA?.pointerId !== evt.pointerId &&\r\n                this._pointB?.pointerId !== evt.pointerId\r\n            ) {\r\n                return; // If we get a non-down event for a touch that we're not tracking, ignore it\r\n            } else if (p.type === PointerEventTypes.POINTERDOWN && (this._currentMousePointerIdDown === -1 || isTouch)) {\r\n                try {\r\n                    srcElement?.setPointerCapture(evt.pointerId);\r\n                } catch (e) {\r\n                    //Nothing to do with the error. Execution will continue.\r\n                }\r\n\r\n                if (this._pointA === null) {\r\n                    this._pointA = {\r\n                        x: evt.clientX,\r\n                        y: evt.clientY,\r\n                        pointerId: evt.pointerId,\r\n                        type: evt.pointerType,\r\n                    };\r\n                } else if (this._pointB === null) {\r\n                    this._pointB = {\r\n                        x: evt.clientX,\r\n                        y: evt.clientY,\r\n                        pointerId: evt.pointerId,\r\n                        type: evt.pointerType,\r\n                    };\r\n                } else {\r\n                    return; // We are already tracking two pointers so ignore this one\r\n                }\r\n\r\n                if (this._currentMousePointerIdDown === -1 && !isTouch) {\r\n                    this._currentMousePointerIdDown = evt.pointerId;\r\n                }\r\n                this.onButtonDown(evt);\r\n\r\n                if (!noPreventDefault) {\r\n                    evt.preventDefault();\r\n                    if (element) {\r\n                        element.focus();\r\n                    }\r\n                }\r\n            } else if (p.type === PointerEventTypes.POINTERDOUBLETAP) {\r\n                this.onDoubleTap(evt.pointerType);\r\n            } else if (p.type === PointerEventTypes.POINTERUP && (this._currentMousePointerIdDown === evt.pointerId || isTouch)) {\r\n                try {\r\n                    srcElement?.releasePointerCapture(evt.pointerId);\r\n                } catch (e) {\r\n                    //Nothing to do with the error.\r\n                }\r\n\r\n                if (!isTouch) {\r\n                    this._pointB = null; // Mouse and pen are mono pointer\r\n                }\r\n\r\n                //would be better to use pointers.remove(evt.pointerId) for multitouch gestures,\r\n                //but emptying completely pointers collection is required to fix a bug on iPhone :\r\n                //when changing orientation while pinching camera,\r\n                //one pointer stay pressed forever if we don't release all pointers\r\n                //will be ok to put back pointers.remove(evt.pointerId); when iPhone bug corrected\r\n                if (engine._badOS) {\r\n                    this._pointA = this._pointB = null;\r\n                } else {\r\n                    //only remove the impacted pointer in case of multitouch allowing on most\r\n                    //platforms switching from rotate to zoom and pan seamlessly.\r\n                    if (this._pointB && this._pointA && this._pointA.pointerId == evt.pointerId) {\r\n                        this._pointA = this._pointB;\r\n                        this._pointB = null;\r\n                    } else if (this._pointA && this._pointB && this._pointB.pointerId == evt.pointerId) {\r\n                        this._pointB = null;\r\n                    } else {\r\n                        this._pointA = this._pointB = null;\r\n                    }\r\n                }\r\n\r\n                if (previousPinchSquaredDistance !== 0 || previousMultiTouchPanPosition) {\r\n                    // Previous pinch data is populated but a button has been lifted\r\n                    // so pinch has ended.\r\n                    this.onMultiTouch(\r\n                        this._pointA,\r\n                        this._pointB,\r\n                        previousPinchSquaredDistance,\r\n                        0, // pinchSquaredDistance\r\n                        previousMultiTouchPanPosition,\r\n                        null // multiTouchPanPosition\r\n                    );\r\n                    previousPinchSquaredDistance = 0;\r\n                    previousMultiTouchPanPosition = null;\r\n                }\r\n\r\n                this._currentMousePointerIdDown = -1;\r\n                this.onButtonUp(evt);\r\n\r\n                if (!noPreventDefault) {\r\n                    evt.preventDefault();\r\n                }\r\n            } else if (p.type === PointerEventTypes.POINTERMOVE) {\r\n                if (!noPreventDefault) {\r\n                    evt.preventDefault();\r\n                }\r\n\r\n                // One button down\r\n                if (this._pointA && this._pointB === null) {\r\n                    const offsetX = evt.clientX - this._pointA.x;\r\n                    const offsetY = evt.clientY - this._pointA.y;\r\n                    this._pointA.x = evt.clientX;\r\n                    this._pointA.y = evt.clientY;\r\n                    this.onTouch(this._pointA, offsetX, offsetY);\r\n                }\r\n                // Two buttons down: pinch\r\n                else if (this._pointA && this._pointB) {\r\n                    const ed = this._pointA.pointerId === evt.pointerId ? this._pointA : this._pointB;\r\n                    ed.x = evt.clientX;\r\n                    ed.y = evt.clientY;\r\n                    const distX = this._pointA.x - this._pointB.x;\r\n                    const distY = this._pointA.y - this._pointB.y;\r\n                    const pinchSquaredDistance = distX * distX + distY * distY;\r\n                    const multiTouchPanPosition = {\r\n                        x: (this._pointA.x + this._pointB.x) / 2,\r\n                        y: (this._pointA.y + this._pointB.y) / 2,\r\n                        pointerId: evt.pointerId,\r\n                        type: p.type,\r\n                    };\r\n\r\n                    this.onMultiTouch(this._pointA, this._pointB, previousPinchSquaredDistance, pinchSquaredDistance, previousMultiTouchPanPosition, multiTouchPanPosition);\r\n\r\n                    previousMultiTouchPanPosition = multiTouchPanPosition;\r\n                    previousPinchSquaredDistance = pinchSquaredDistance;\r\n                }\r\n            }\r\n        };\r\n\r\n        this._observer = this.camera\r\n            .getScene()\r\n            ._inputManager._addCameraPointerObserver(\r\n                this._pointerInput,\r\n                PointerEventTypes.POINTERDOWN | PointerEventTypes.POINTERUP | PointerEventTypes.POINTERMOVE | PointerEventTypes.POINTERDOUBLETAP\r\n            );\r\n\r\n        this._onLostFocus = () => {\r\n            this._pointA = this._pointB = null;\r\n            previousPinchSquaredDistance = 0;\r\n            previousMultiTouchPanPosition = null;\r\n            this.onLostFocus();\r\n        };\r\n\r\n        this._contextMenuBind = (evt: Event) => this.onContextMenu(evt as PointerEvent);\r\n\r\n        if (element) {\r\n            element.addEventListener(\"contextmenu\", this._contextMenuBind, false);\r\n        }\r\n\r\n        const hostWindow = this.camera.getScene().getEngine().getHostWindow();\r\n\r\n        if (hostWindow) {\r\n            Tools.RegisterTopRootEvents(hostWindow, [{ name: \"blur\", handler: this._onLostFocus }]);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._onLostFocus) {\r\n            const hostWindow = this.camera.getScene().getEngine().getHostWindow();\r\n            if (hostWindow) {\r\n                Tools.UnregisterTopRootEvents(hostWindow, [{ name: \"blur\", handler: this._onLostFocus }]);\r\n            }\r\n        }\r\n\r\n        if (this._observer) {\r\n            this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\r\n            this._observer = null;\r\n\r\n            if (this._contextMenuBind) {\r\n                const inputElement = this.camera.getScene().getEngine().getInputElement();\r\n                if (inputElement) {\r\n                    inputElement.removeEventListener(\"contextmenu\", this._contextMenuBind);\r\n                }\r\n            }\r\n\r\n            this._onLostFocus = null;\r\n        }\r\n\r\n        this._altKey = false;\r\n        this._ctrlKey = false;\r\n        this._metaKey = false;\r\n        this._shiftKey = false;\r\n        this._buttonsPressed = 0;\r\n        this._currentMousePointerIdDown = -1;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"BaseCameraPointersInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"pointers\";\r\n    }\r\n\r\n    /**\r\n     * Called on pointer POINTERDOUBLETAP event.\r\n     * Override this method to provide functionality on POINTERDOUBLETAP event.\r\n     * @param type type of event\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public onDoubleTap(type: string) {}\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    /**\r\n     * Called on pointer POINTERMOVE event if only a single touch is active.\r\n     * Override this method to provide functionality.\r\n     * @param point The current position of the pointer\r\n     * @param offsetX The offsetX of the pointer when the event occurred\r\n     * @param offsetY The offsetY of the pointer when the event occurred\r\n     */\r\n    public onTouch(point: Nullable<PointerTouch>, offsetX: number, offsetY: number): void {}\r\n\r\n    /**\r\n     * Called on pointer POINTERMOVE event if multiple touches are active.\r\n     * Override this method to provide functionality.\r\n     * @param _pointA First point in the pair\r\n     * @param _pointB Second point in the pair\r\n     * @param previousPinchSquaredDistance Sqr Distance between the points the last time this event was fired (by this input)\r\n     * @param pinchSquaredDistance Sqr Distance between the points this time\r\n     * @param previousMultiTouchPanPosition Previous center point between the points\r\n     * @param multiTouchPanPosition Current center point between the points\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public onMultiTouch(\r\n        _pointA: Nullable<PointerTouch>,\r\n        _pointB: Nullable<PointerTouch>,\r\n        previousPinchSquaredDistance: number,\r\n        pinchSquaredDistance: number,\r\n        previousMultiTouchPanPosition: Nullable<PointerTouch>,\r\n        multiTouchPanPosition: Nullable<PointerTouch>\r\n    ): void {}\r\n\r\n    /**\r\n     * Called on JS contextmenu event.\r\n     * Override this method to provide functionality.\r\n     * @param evt the event to be handled\r\n     */\r\n    public onContextMenu(evt: PointerEvent): void {\r\n        evt.preventDefault();\r\n    }\r\n\r\n    /**\r\n     * Called each time a new POINTERDOWN event occurs. Ie, for each button\r\n     * press.\r\n     * Override this method to provide functionality.\r\n     * @param _evt Defines the event to track\r\n     */\r\n    public onButtonDown(_evt: IPointerEvent): void {}\r\n\r\n    /**\r\n     * Called each time a new POINTERUP event occurs. Ie, for each button\r\n     * release.\r\n     * Override this method to provide functionality.\r\n     * @param _evt Defines the event to track\r\n     */\r\n    public onButtonUp(_evt: IPointerEvent): void {}\r\n\r\n    /**\r\n     * Called when window becomes inactive.\r\n     * Override this method to provide functionality.\r\n     */\r\n    public onLostFocus(): void {}\r\n\r\n    private _pointerInput: (p: PointerInfo, s: EventState) => void;\r\n    private _observer: Nullable<Observer<PointerInfo>>;\r\n    private _onLostFocus: Nullable<(e: FocusEvent) => any>;\r\n    private _pointA: Nullable<PointerTouch>;\r\n    private _pointB: Nullable<PointerTouch>;\r\n}\r\n"]}