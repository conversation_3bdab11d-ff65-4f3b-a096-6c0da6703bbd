{"version": 3, "file": "engine.rawTexture.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.rawTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,6CAA6C,CAAC;AAGrG,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAG9C,OAAO,EAAE,gBAAgB,EAAE,kCAAsC;AAqMjE,gBAAgB,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAC1C,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,OAAe,SAAS,CAAC,yBAAyB,EAClD,gBAAwB,CAAC,EACzB,gBAAyB,KAAK;IAE9B,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,oCAA4B,CAAC;IACrE,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;IAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACpC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACnC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;IACvC,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;IAEvC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IAEzG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;IAExF,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAC1C,OAAkC,EAClC,UAAqC,EACrC,MAAc,EACd,OAAgB,EAChB,cAAgC,IAAI,EACpC,OAAe,SAAS,CAAC,yBAAyB,EAClD,gBAAyB,KAAK;IAE9B,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,OAAO;IACX,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC;QACjC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;QACnC,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;IAC3C,CAAC;IAED,IAAI,UAAU,EAAE,CAAC;QACb,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAC5E,MAAM,cAAc,GAAG,MAAM,KAAK,SAAS,CAAC,iBAAiB,CAAC;QAE9D,IAAI,cAAc,EAAE,CAAC;YACjB,UAAU,GAAG,2BAA2B,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9F,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;QAE7F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrJ,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAC9C,IAAiC,EACjC,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI;IAEpC,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,wCAAgC,CAAC;IAEzE,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC;QAClF,eAAe,GAAG,KAAK,CAAC;QACxB,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC,mJAAmJ,CAAC,CAAC;IACrK,CAAC;SAAM,IAAI,IAAI,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC;QAClG,eAAe,GAAG,KAAK,CAAC;QACxB,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC,wJAAwJ,CAAC,CAAC;IAC1K,CAAC;SAAM,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAChF,eAAe,GAAG,KAAK,CAAC;QACxB,MAAM,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;IACjG,CAAC;SAAM,IAAI,IAAI,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;QACnF,eAAe,GAAG,KAAK,CAAC;QACxB,MAAM,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;IACtG,CAAC;IAED,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC;IACjC,OAAO,CAAC,MAAM,GAAG,MAAM,KAAK,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,MAAM,CAAC;IAChG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IACrB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACpC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACpC,CAAC;IACD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACnC,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;IAC3D,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;IAE3D,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;IAEhE,IAAI,MAAM,KAAK,SAAS,CAAC,iBAAiB,EAAE,CAAC;QACzC,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAC5E,iBAAiB,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAClD,CAAC;IAED,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IAEvB,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAC9C,OAAwB,EACxB,UAA6B,EAC7B,OAAe,EACf,IAAY,EACZ,OAAgB,EAChB,cAAgC,IAAI;IAEpC,OAAO,CAAC,gBAAgB,GAAG,UAAU,CAAC;IACtC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IAEnC,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;IAC5E,MAAM,cAAc,GAAG,iBAAiB,CAAC,oBAAoB,CAAC;IAE9D,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAEjC,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACzC,IAAI,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,EAAE,CAAC;YACjB,QAAQ,GAAG,2BAA2B,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC1F,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;IACzF,CAAC;IAED,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,EAAE,iBAAiB,CAAC,kBAAmB,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACnK,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACxD,CAAC;IAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,2BAA2B,GAAG,UACrD,GAAW,EACX,KAAsB,EACtB,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,QAAiB,EACjB,QAAmE,EACnE,eAA4E,EAC5E,SAA+B,IAAI,EACnC,UAAiE,IAAI,EACrE,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,UAAmB,KAAK;IAExB,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;IAC5G,KAAK,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;IAC/B,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;IAClB,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;IAExB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,MAAM,OAAO,GAAG,CAAC,OAAqB,EAAE,SAAe,EAAE,EAAE;QACvD,KAAK,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;YACrB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAClE,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,IAAS,EAAE,EAAE;QACnC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YAClB,MAAM,cAAc,GAAG,MAAM,KAAK,SAAS,CAAC,iBAAiB,CAAC;YAC9D,MAAM,OAAO,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC;YAChD,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;YAC5E,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;gBAClD,MAAM,OAAO,GAAG,KAAK,IAAI,KAAK,CAAC;gBAC/B,MAAM,QAAQ,GAAG,EAAE,CAAC;gBACpB,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;oBACjD,IAAI,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;oBACnD,IAAI,cAAc,EAAE,CAAC;wBACjB,WAAW,GAAG,2BAA2B,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;oBACnF,CAAC;oBACD,QAAQ,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC;gBACtG,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,QAAQ,EAAE,iBAAiB,CAAC,kBAAmB,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9J,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,KAAK,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAElC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;QACb,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,CAAC,SAAS,CACV,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;QACL,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC,EACD,SAAS,EACT,KAAK,EAAE,eAAe,EACtB,IAAI,EACJ,OAAO,CACV,CAAC;IAEF,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAC5C,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,KAAa,EACb,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,cAAsB,SAAS,CAAC,yBAAyB,EACzD,gBAAwB,CAAC;IAEzB,MAAM,MAAM,uCAA8B,CAAC;IAC3C,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAElD,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;IAC5B,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;IAC3B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;IAEvC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;IAEzG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;IAElF,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAC5C,OAAwB,EACxB,UAAqC,EACrC,MAAc,EACd,OAAgB,EAChB,cAAgC,IAAI,EACpC,cAAsB,SAAS,CAAC,yBAAyB;IAEzD,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC;QACjC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACvC,CAAC;IAED,IAAI,UAAU,EAAE,CAAC;QACb,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAC5E,MAAM,cAAc,GAAG,MAAM,KAAK,SAAS,CAAC,iBAAiB,CAAC;QAE9D,IAAI,cAAc,EAAE,CAAC;YACjB,UAAU,GAAG,2BAA2B,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACrG,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;QAE7F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrJ,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,uBAAuB,GAAG,UACjD,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,KAAa,EACb,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,cAAsB,SAAS,CAAC,yBAAyB,EACzD,gBAAwB,CAAC;IAEzB,MAAM,MAAM,4CAAmC,CAAC;IAChD,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAElD,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;IAC5B,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;IAC3B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACpC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;IACzB,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;IAEvC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;IAErG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;IAEvF,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,uBAAuB,GAAG,UACjD,OAAwB,EACxB,UAAqC,EACrC,MAAc,EACd,OAAgB,EAChB,cAAgC,IAAI,EACpC,cAAsB,SAAS,CAAC,yBAAyB;IAEzD,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC;QACjC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACvC,CAAC;IAED,IAAI,UAAU,EAAE,CAAC;QACb,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAC5E,MAAM,cAAc,GAAG,MAAM,KAAK,SAAS,CAAC,iBAAiB,CAAC;QAE9D,IAAI,cAAc,EAAE,CAAC;YACjB,UAAU,GAAG,2BAA2B,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACrG,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;QAE7F,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrJ,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC;AAEF;;GAEG;AACH,SAAS,2BAA2B,CAAC,OAAY,EAAE,KAAa,EAAE,MAAc,EAAE,WAAmB;IACjG,kCAAkC;IAClC,IAAI,QAAa,CAAC;IAClB,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,WAAW,KAAK,SAAS,CAAC,iBAAiB,EAAE,CAAC;QAC9C,QAAQ,GAAG,IAAI,YAAY,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IACpD,CAAC;SAAM,IAAI,WAAW,KAAK,SAAS,CAAC,sBAAsB,EAAE,CAAC;QAC1D,QAAQ,GAAG,IAAI,WAAW,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/C,IAAI,GAAG,KAAK,CAAC,CAAC,2CAA2C;IAC7D,CAAC;SAAM,IAAI,WAAW,KAAK,SAAS,CAAC,4BAA4B,EAAE,CAAC;QAChE,QAAQ,GAAG,IAAI,WAAW,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IACnD,CAAC;SAAM,CAAC;QACJ,QAAQ,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,sBAAsB;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAErC,8BAA8B;YAC9B,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAC5C,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAC5C,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAE5C,kCAAkC;YAClC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAClC,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC", "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../../Materials/Textures/internalTexture\";\r\nimport type { IWebRequest } from \"../../../Misc/interfaces/iWebRequest\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { Constants } from \"../../constants\";\r\nimport type { WebGPUHardwareTexture } from \"../webgpuHardwareTexture\";\r\nimport { Logger } from \"../../../Misc/logger\";\r\n\r\nimport type { Scene } from \"../../../scene\";\r\nimport { ThinWebGPUEngine } from \"core/Engines/thinWebGPUEngine\";\r\n\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Update a raw texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store in the texture\r\n         * @param format defines the format of the data\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawTexture(texture: Nullable<InternalTexture>, data: Nullable<ArrayBufferView>, format: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store in the texture\r\n         * @param format defines the format of the data\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the compression used (null by default)\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n         */\r\n        updateRawTexture(\r\n            texture: Nullable<InternalTexture>,\r\n            data: Nullable<ArrayBufferView>,\r\n            format: number,\r\n            invertY: boolean,\r\n            compression: Nullable<string>,\r\n            type: number,\r\n            useSRGBBuffer: boolean\r\n        ): void;\r\n\r\n        /**\r\n         * Creates a new raw cube texture\r\n         * @param data defines the array of data to use to create each face\r\n         * @param size defines the size of the textures\r\n         * @param format defines the format of the data\r\n         * @param type defines the type of the data (like Engine.TEXTURETYPE_UNSIGNED_BYTE)\r\n         * @param generateMipMaps  defines if the engine should generate the mip levels\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param samplingMode defines the required sampling mode (like Texture.NEAREST_SAMPLINGMODE)\r\n         * @param compression defines the compression used (null by default)\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createRawCubeTexture(\r\n            data: Nullable<ArrayBufferView[]>,\r\n            size: number,\r\n            format: number,\r\n            type: number,\r\n            generateMipMaps: boolean,\r\n            invertY: boolean,\r\n            samplingMode: number,\r\n            compression: Nullable<string>\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Update a raw cube texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawCubeTexture(texture: InternalTexture, data: ArrayBufferView[], format: number, type: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw cube texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the compression used (null by default)\r\n         */\r\n        updateRawCubeTexture(texture: InternalTexture, data: ArrayBufferView[], format: number, type: number, invertY: boolean, compression: Nullable<string>): void;\r\n\r\n        /**\r\n         * Update a raw cube texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the compression used (null by default)\r\n         * @param level defines which level of the texture to update\r\n         */\r\n        updateRawCubeTexture(texture: InternalTexture, data: ArrayBufferView[], format: number, type: number, invertY: boolean, compression: Nullable<string>, level: number): void;\r\n\r\n        /**\r\n         * Creates a new raw cube texture from a specified url\r\n         * @param url defines the url where the data is located\r\n         * @param scene defines the current scene\r\n         * @param size defines the size of the textures\r\n         * @param format defines the format of the data\r\n         * @param type defines the type fo the data (like Engine.TEXTURETYPE_UNSIGNED_BYTE)\r\n         * @param noMipmap defines if the engine should avoid generating the mip levels\r\n         * @param callback defines a callback used to extract texture data from loaded data\r\n         * @param mipmapGenerator defines to provide an optional tool to generate mip levels\r\n         * @param onLoad defines a callback called when texture is loaded\r\n         * @param onError defines a callback called if there is an error\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createRawCubeTextureFromUrl(\r\n            url: string,\r\n            scene: Nullable<Scene>,\r\n            size: number,\r\n            format: number,\r\n            type: number,\r\n            noMipmap: boolean,\r\n            callback: (ArrayBuffer: ArrayBuffer) => Nullable<ArrayBufferView[]>,\r\n            mipmapGenerator: Nullable<(faces: ArrayBufferView[]) => ArrayBufferView[][]>,\r\n            onLoad: Nullable<() => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Creates a new raw cube texture from a specified url\r\n         * @param url defines the url where the data is located\r\n         * @param scene defines the current scene\r\n         * @param size defines the size of the textures\r\n         * @param format defines the format of the data\r\n         * @param type defines the type fo the data (like Engine.TEXTURETYPE_UNSIGNED_BYTE)\r\n         * @param noMipmap defines if the engine should avoid generating the mip levels\r\n         * @param callback defines a callback used to extract texture data from loaded data\r\n         * @param mipmapGenerator defines to provide an optional tool to generate mip levels\r\n         * @param onLoad defines a callback called when texture is loaded\r\n         * @param onError defines a callback called if there is an error\r\n         * @param samplingMode defines the required sampling mode (like Texture.NEAREST_SAMPLINGMODE)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createRawCubeTextureFromUrl(\r\n            url: string,\r\n            scene: Nullable<Scene>,\r\n            size: number,\r\n            format: number,\r\n            type: number,\r\n            noMipmap: boolean,\r\n            callback: (ArrayBuffer: ArrayBuffer) => Nullable<ArrayBufferView[]>,\r\n            mipmapGenerator: Nullable<(faces: ArrayBufferView[]) => ArrayBufferView[][]>,\r\n            onLoad: Nullable<() => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            samplingMode: number,\r\n            invertY: boolean\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Update a raw 3D texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawTexture3D(texture: InternalTexture, data: Nullable<ArrayBufferView>, format: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw 3D texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the used compression (can be null)\r\n         * @param textureType defines the texture Type (Engine.TEXTURETYPE_UNSIGNED_BYTE, Engine.TEXTURETYPE_FLOAT...)\r\n         */\r\n        updateRawTexture3D(texture: InternalTexture, data: Nullable<ArrayBufferView>, format: number, invertY: boolean, compression: Nullable<string>, textureType: number): void;\r\n\r\n        /**\r\n         * Update a raw 2D array texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawTexture2DArray(texture: InternalTexture, data: Nullable<ArrayBufferView>, format: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw 2D array texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the used compression (can be null)\r\n         * @param textureType defines the texture Type (Engine.TEXTURETYPE_UNSIGNED_BYTE, Engine.TEXTURETYPE_FLOAT...)\r\n         */\r\n        updateRawTexture2DArray(\r\n            texture: InternalTexture,\r\n            data: Nullable<ArrayBufferView>,\r\n            format: number,\r\n            invertY: boolean,\r\n            compression: Nullable<string>,\r\n            textureType: number\r\n        ): void;\r\n    }\r\n}\r\n\r\nThinWebGPUEngine.prototype.createRawTexture = function (\r\n    data: Nullable<ArrayBufferView>,\r\n    width: number,\r\n    height: number,\r\n    format: number,\r\n    generateMipMaps: boolean,\r\n    invertY: boolean,\r\n    samplingMode: number,\r\n    compression: Nullable<string> = null,\r\n    type: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n    creationFlags: number = 0,\r\n    useSRGBBuffer: boolean = false\r\n): InternalTexture {\r\n    const texture = new InternalTexture(this, InternalTextureSource.Raw);\r\n    texture.baseWidth = width;\r\n    texture.baseHeight = height;\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.format = format;\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n    texture.invertY = invertY;\r\n    texture._compression = compression;\r\n    texture.type = type;\r\n    texture._creationFlags = creationFlags;\r\n    texture._useSRGBBuffer = useSRGBBuffer;\r\n\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = data;\r\n    }\r\n\r\n    this._textureHelper.createGPUTextureForInternalTexture(texture, width, height, undefined, creationFlags);\r\n\r\n    this.updateRawTexture(texture, data, format, invertY, compression, type, useSRGBBuffer);\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    return texture;\r\n};\r\n\r\nThinWebGPUEngine.prototype.updateRawTexture = function (\r\n    texture: Nullable<InternalTexture>,\r\n    bufferView: Nullable<ArrayBufferView>,\r\n    format: number,\r\n    invertY: boolean,\r\n    compression: Nullable<string> = null,\r\n    type: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n    useSRGBBuffer: boolean = false\r\n): void {\r\n    if (!texture) {\r\n        return;\r\n    }\r\n\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = bufferView;\r\n        texture.invertY = invertY;\r\n        texture._compression = compression;\r\n        texture._useSRGBBuffer = useSRGBBuffer;\r\n    }\r\n\r\n    if (bufferView) {\r\n        const gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n        const needConversion = format === Constants.TEXTUREFORMAT_RGB;\r\n\r\n        if (needConversion) {\r\n            bufferView = ConvertRGBtoRGBATextureData(bufferView, texture.width, texture.height, type);\r\n        }\r\n\r\n        const data = new Uint8Array(bufferView.buffer, bufferView.byteOffset, bufferView.byteLength);\r\n\r\n        this._textureHelper.updateTexture(data, texture, texture.width, texture.height, texture.depth, gpuTextureWrapper.format, 0, 0, invertY, false, 0, 0);\r\n        if (texture.generateMipMaps) {\r\n            this._generateMipmaps(texture, this._uploadEncoder);\r\n        }\r\n    }\r\n\r\n    texture.isReady = true;\r\n};\r\n\r\nThinWebGPUEngine.prototype.createRawCubeTexture = function (\r\n    data: Nullable<ArrayBufferView[]>,\r\n    size: number,\r\n    format: number,\r\n    type: number,\r\n    generateMipMaps: boolean,\r\n    invertY: boolean,\r\n    samplingMode: number,\r\n    compression: Nullable<string> = null\r\n): InternalTexture {\r\n    const texture = new InternalTexture(this, InternalTextureSource.CubeRaw);\r\n\r\n    if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n        generateMipMaps = false;\r\n        samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        Logger.Warn(\"Float texture filtering is not supported. Mipmap generation and sampling mode are forced to false and TEXTURE_NEAREST_SAMPLINGMODE, respectively.\");\r\n    } else if (type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n        generateMipMaps = false;\r\n        samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        Logger.Warn(\"Half float texture filtering is not supported. Mipmap generation and sampling mode are forced to false and TEXTURE_NEAREST_SAMPLINGMODE, respectively.\");\r\n    } else if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatRender) {\r\n        generateMipMaps = false;\r\n        Logger.Warn(\"Render to float textures is not supported. Mipmap generation forced to false.\");\r\n    } else if (type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.colorBufferFloat) {\r\n        generateMipMaps = false;\r\n        Logger.Warn(\"Render to half float textures is not supported. Mipmap generation forced to false.\");\r\n    }\r\n\r\n    texture.isCube = true;\r\n    texture._originalFormat = format;\r\n    texture.format = format === Constants.TEXTUREFORMAT_RGB ? Constants.TEXTUREFORMAT_RGBA : format;\r\n    texture.type = type;\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.width = size;\r\n    texture.height = size;\r\n    texture.samplingMode = samplingMode;\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferViewArray = data;\r\n    }\r\n    texture.invertY = invertY;\r\n    texture._compression = compression;\r\n    texture._cachedWrapU = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n    texture._cachedWrapV = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n\r\n    this._textureHelper.createGPUTextureForInternalTexture(texture);\r\n\r\n    if (format === Constants.TEXTUREFORMAT_RGB) {\r\n        const gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n        gpuTextureWrapper._originalFormatIsRGB = true;\r\n    }\r\n\r\n    if (data) {\r\n        this.updateRawCubeTexture(texture, data, format, type, invertY, compression);\r\n    }\r\n\r\n    texture.isReady = true;\r\n\r\n    return texture;\r\n};\r\n\r\nThinWebGPUEngine.prototype.updateRawCubeTexture = function (\r\n    texture: InternalTexture,\r\n    bufferView: ArrayBufferView[],\r\n    _format: number,\r\n    type: number,\r\n    invertY: boolean,\r\n    compression: Nullable<string> = null\r\n): void {\r\n    texture._bufferViewArray = bufferView;\r\n    texture.invertY = invertY;\r\n    texture._compression = compression;\r\n\r\n    const gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n    const needConversion = gpuTextureWrapper._originalFormatIsRGB;\r\n\r\n    const faces = [0, 2, 4, 1, 3, 5];\r\n\r\n    const data = [];\r\n    for (let i = 0; i < bufferView.length; ++i) {\r\n        let faceData = bufferView[faces[i]];\r\n        if (needConversion) {\r\n            faceData = ConvertRGBtoRGBATextureData(faceData, texture.width, texture.height, type);\r\n        }\r\n        data.push(new Uint8Array(faceData.buffer, faceData.byteOffset, faceData.byteLength));\r\n    }\r\n\r\n    this._textureHelper.updateCubeTextures(data, gpuTextureWrapper.underlyingResource!, texture.width, texture.height, gpuTextureWrapper.format, invertY, false, 0, 0);\r\n    if (texture.generateMipMaps) {\r\n        this._generateMipmaps(texture, this._uploadEncoder);\r\n    }\r\n\r\n    texture.isReady = true;\r\n};\r\n\r\nThinWebGPUEngine.prototype.createRawCubeTextureFromUrl = function (\r\n    url: string,\r\n    scene: Nullable<Scene>,\r\n    size: number,\r\n    format: number,\r\n    type: number,\r\n    noMipmap: boolean,\r\n    callback: (ArrayBuffer: ArrayBuffer) => Nullable<ArrayBufferView[]>,\r\n    mipmapGenerator: Nullable<(faces: ArrayBufferView[]) => ArrayBufferView[][]>,\r\n    onLoad: Nullable<() => void> = null,\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n    invertY: boolean = false\r\n): InternalTexture {\r\n    const texture = this.createRawCubeTexture(null, size, format, type, !noMipmap, invertY, samplingMode, null);\r\n    scene?.addPendingData(texture);\r\n    texture.url = url;\r\n    texture.isReady = false;\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    const onerror = (request?: IWebRequest, exception?: any) => {\r\n        scene?.removePendingData(texture);\r\n        if (onError && request) {\r\n            onError(request.status + \" \" + request.statusText, exception);\r\n        }\r\n    };\r\n\r\n    const internalCallback = (data: any) => {\r\n        const width = texture.width;\r\n        const faceDataArrays = callback(data);\r\n\r\n        if (!faceDataArrays) {\r\n            return;\r\n        }\r\n\r\n        if (mipmapGenerator) {\r\n            const needConversion = format === Constants.TEXTUREFORMAT_RGB;\r\n            const mipData = mipmapGenerator(faceDataArrays);\r\n            const gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n            const faces = [0, 1, 2, 3, 4, 5];\r\n            for (let level = 0; level < mipData.length; level++) {\r\n                const mipSize = width >> level;\r\n                const allFaces = [];\r\n                for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\r\n                    let mipFaceData = mipData[level][faces[faceIndex]];\r\n                    if (needConversion) {\r\n                        mipFaceData = ConvertRGBtoRGBATextureData(mipFaceData, mipSize, mipSize, type);\r\n                    }\r\n                    allFaces.push(new Uint8Array(mipFaceData.buffer, mipFaceData.byteOffset, mipFaceData.byteLength));\r\n                }\r\n                this._textureHelper.updateCubeTextures(allFaces, gpuTextureWrapper.underlyingResource!, mipSize, mipSize, gpuTextureWrapper.format, invertY, false, 0, 0);\r\n            }\r\n        } else {\r\n            this.updateRawCubeTexture(texture, faceDataArrays, format, type, invertY);\r\n        }\r\n\r\n        texture.isReady = true;\r\n        scene?.removePendingData(texture);\r\n\r\n        if (onLoad) {\r\n            onLoad();\r\n        }\r\n    };\r\n\r\n    this._loadFile(\r\n        url,\r\n        (data) => {\r\n            internalCallback(data);\r\n        },\r\n        undefined,\r\n        scene?.offlineProvider,\r\n        true,\r\n        onerror\r\n    );\r\n\r\n    return texture;\r\n};\r\n\r\nThinWebGPUEngine.prototype.createRawTexture3D = function (\r\n    data: Nullable<ArrayBufferView>,\r\n    width: number,\r\n    height: number,\r\n    depth: number,\r\n    format: number,\r\n    generateMipMaps: boolean,\r\n    invertY: boolean,\r\n    samplingMode: number,\r\n    compression: Nullable<string> = null,\r\n    textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n    creationFlags: number = 0\r\n): InternalTexture {\r\n    const source = InternalTextureSource.Raw3D;\r\n    const texture = new InternalTexture(this, source);\r\n\r\n    texture.baseWidth = width;\r\n    texture.baseHeight = height;\r\n    texture.baseDepth = depth;\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.depth = depth;\r\n    texture.format = format;\r\n    texture.type = textureType;\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n    texture.is3D = true;\r\n    texture._creationFlags = creationFlags;\r\n\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = data;\r\n    }\r\n\r\n    this._textureHelper.createGPUTextureForInternalTexture(texture, width, height, undefined, creationFlags);\r\n\r\n    this.updateRawTexture3D(texture, data, format, invertY, compression, textureType);\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    return texture;\r\n};\r\n\r\nThinWebGPUEngine.prototype.updateRawTexture3D = function (\r\n    texture: InternalTexture,\r\n    bufferView: Nullable<ArrayBufferView>,\r\n    format: number,\r\n    invertY: boolean,\r\n    compression: Nullable<string> = null,\r\n    textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE\r\n): void {\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = bufferView;\r\n        texture.format = format;\r\n        texture.invertY = invertY;\r\n        texture._compression = compression;\r\n    }\r\n\r\n    if (bufferView) {\r\n        const gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n        const needConversion = format === Constants.TEXTUREFORMAT_RGB;\r\n\r\n        if (needConversion) {\r\n            bufferView = ConvertRGBtoRGBATextureData(bufferView, texture.width, texture.height, textureType);\r\n        }\r\n\r\n        const data = new Uint8Array(bufferView.buffer, bufferView.byteOffset, bufferView.byteLength);\r\n\r\n        this._textureHelper.updateTexture(data, texture, texture.width, texture.height, texture.depth, gpuTextureWrapper.format, 0, 0, invertY, false, 0, 0);\r\n        if (texture.generateMipMaps) {\r\n            this._generateMipmaps(texture, this._uploadEncoder);\r\n        }\r\n    }\r\n\r\n    texture.isReady = true;\r\n};\r\n\r\nThinWebGPUEngine.prototype.createRawTexture2DArray = function (\r\n    data: Nullable<ArrayBufferView>,\r\n    width: number,\r\n    height: number,\r\n    depth: number,\r\n    format: number,\r\n    generateMipMaps: boolean,\r\n    invertY: boolean,\r\n    samplingMode: number,\r\n    compression: Nullable<string> = null,\r\n    textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n    creationFlags: number = 0\r\n): InternalTexture {\r\n    const source = InternalTextureSource.Raw2DArray;\r\n    const texture = new InternalTexture(this, source);\r\n\r\n    texture.baseWidth = width;\r\n    texture.baseHeight = height;\r\n    texture.baseDepth = depth;\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.depth = depth;\r\n    texture.format = format;\r\n    texture.type = textureType;\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n    texture.is2DArray = true;\r\n    texture._creationFlags = creationFlags;\r\n\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = data;\r\n    }\r\n\r\n    this._textureHelper.createGPUTextureForInternalTexture(texture, width, height, depth, creationFlags);\r\n\r\n    this.updateRawTexture2DArray(texture, data, format, invertY, compression, textureType);\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    return texture;\r\n};\r\n\r\nThinWebGPUEngine.prototype.updateRawTexture2DArray = function (\r\n    texture: InternalTexture,\r\n    bufferView: Nullable<ArrayBufferView>,\r\n    format: number,\r\n    invertY: boolean,\r\n    compression: Nullable<string> = null,\r\n    textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE\r\n): void {\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = bufferView;\r\n        texture.format = format;\r\n        texture.invertY = invertY;\r\n        texture._compression = compression;\r\n    }\r\n\r\n    if (bufferView) {\r\n        const gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n        const needConversion = format === Constants.TEXTUREFORMAT_RGB;\r\n\r\n        if (needConversion) {\r\n            bufferView = ConvertRGBtoRGBATextureData(bufferView, texture.width, texture.height, textureType);\r\n        }\r\n\r\n        const data = new Uint8Array(bufferView.buffer, bufferView.byteOffset, bufferView.byteLength);\r\n\r\n        this._textureHelper.updateTexture(data, texture, texture.width, texture.height, texture.depth, gpuTextureWrapper.format, 0, 0, invertY, false, 0, 0);\r\n        if (texture.generateMipMaps) {\r\n            this._generateMipmaps(texture, this._uploadEncoder);\r\n        }\r\n    }\r\n\r\n    texture.isReady = true;\r\n};\r\n\r\n/**\r\n * @internal\r\n */\r\nfunction ConvertRGBtoRGBATextureData(rgbData: any, width: number, height: number, textureType: number): ArrayBufferView {\r\n    // Create new RGBA data container.\r\n    let rgbaData: any;\r\n    let val1 = 1;\r\n    if (textureType === Constants.TEXTURETYPE_FLOAT) {\r\n        rgbaData = new Float32Array(width * height * 4);\r\n    } else if (textureType === Constants.TEXTURETYPE_HALF_FLOAT) {\r\n        rgbaData = new Uint16Array(width * height * 4);\r\n        val1 = 15360; // 15360 is the encoding of 1 in half float\r\n    } else if (textureType === Constants.TEXTURETYPE_UNSIGNED_INTEGER) {\r\n        rgbaData = new Uint32Array(width * height * 4);\r\n    } else {\r\n        rgbaData = new Uint8Array(width * height * 4);\r\n    }\r\n\r\n    // Convert each pixel.\r\n    for (let x = 0; x < width; x++) {\r\n        for (let y = 0; y < height; y++) {\r\n            const index = (y * width + x) * 3;\r\n            const newIndex = (y * width + x) * 4;\r\n\r\n            // Map Old Value to new value.\r\n            rgbaData[newIndex + 0] = rgbData[index + 0];\r\n            rgbaData[newIndex + 1] = rgbData[index + 1];\r\n            rgbaData[newIndex + 2] = rgbData[index + 2];\r\n\r\n            // Add fully opaque alpha channel.\r\n            rgbaData[newIndex + 3] = val1;\r\n        }\r\n    }\r\n\r\n    return rgbaData;\r\n}\r\n"]}