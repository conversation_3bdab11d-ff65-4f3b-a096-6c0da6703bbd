{"version": 3, "file": "animationGroupMask.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Animations/animationGroupMask.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAkB,sBASjB;AATD,WAAkB,sBAAsB;IACpC;;OAEG;IACH,yEAAW,CAAA;IACX;;OAEG;IACH,yEAAW,CAAA;AACf,CAAC,EATiB,sBAAsB,KAAtB,sBAAsB,QASvC;AAED;;;;GAIG;AACH,MAAM,OAAO,kBAAkB;IAW3B;;;;OAIG;IACH,YACI,KAAgB;IAChB;;OAEG;IACI,6CAA6D;QAA7D,SAAI,GAAJ,IAAI,CAAyD;QAfxE;;WAEG;QACI,aAAQ,GAAG,KAAK,CAAC;QAcpB,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QACtC,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,IAAuB;QACxC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBACnB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC;YACD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,IAAuB;QAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBACnB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YACD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAAC,IAAY;QAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,2CAAmC,CAAC,CAAC;IAC1F,CAAC;CACJ", "sourcesContent": ["/**\r\n * Enum used to define the mode for an animation group mask\r\n */\r\nexport const enum AnimationGroupMaskMode {\r\n    /**\r\n     * The mask defines the animatable target names that should be included\r\n     */\r\n    Include = 0,\r\n    /**\r\n     * The mask defines the animatable target names in a \"exclude\" mode: all animatable targets will be animated except the ones defined in the mask\r\n     */\r\n    Exclude = 1,\r\n}\r\n\r\n/**\r\n * Defines a mask used to filter animation targets.\r\n * If you apply a mask to an animation group (see the AnimationGroup.mask property), only the animations whose target names match the mask will play.\r\n * Note that a target is defined by its name (string). This means that the same mask can be used for several animation groups, provided that their targets are named in the same way.\r\n */\r\nexport class AnimationGroupMask {\r\n    /**\r\n     * The set of target names included in the mask. If mode is AnimationGroupMaskMode.Exclude, the targets in this set will be excluded from the mask instead.\r\n     */\r\n    private _targetNames: Set<string>;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the mask is disabled (default is false)\r\n     */\r\n    public disabled = false;\r\n\r\n    /**\r\n     * Creates a new mask\r\n     * @param names The list of target names to add to the mask (optional)\r\n     * @param mode Defines the mode for the mask (default: AnimationGroupMaskMode.Include)\r\n     */\r\n    constructor(\r\n        names?: string[],\r\n        /**\r\n         * [0] Defines the mode for the mask\r\n         */\r\n        public mode: AnimationGroupMaskMode = AnimationGroupMaskMode.Include\r\n    ) {\r\n        this._targetNames = new Set<string>();\r\n        if (names) {\r\n            this.addTargetName(names);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds one or several target names to the mask\r\n     * @param name The name(s) to add to the mask\r\n     */\r\n    public addTargetName(name: string | string[]): void {\r\n        if (Array.isArray(name)) {\r\n            for (const n of name) {\r\n                this._targetNames.add(n);\r\n            }\r\n            return;\r\n        }\r\n\r\n        this._targetNames.add(name);\r\n    }\r\n\r\n    /**\r\n     * Removes one or several target names from the mask\r\n     * @param name The name(s) to remove from the mask\r\n     */\r\n    public removeTargetName(name: string | string[]): void {\r\n        if (Array.isArray(name)) {\r\n            for (const n of name) {\r\n                this._targetNames.delete(n);\r\n            }\r\n            return;\r\n        }\r\n\r\n        this._targetNames.delete(name);\r\n    }\r\n\r\n    /**\r\n     * Checks if the mask includes a target name.\r\n     * This method is intended to know if a given target name is included in the mask, not if the name is actually retained by the mask (see retainsTarget() instead).\r\n     * @param name The name to check with the mask\r\n     * @returns True if the mask includes the name, false otherwise\r\n     */\r\n    public hasTarget(name: string): boolean {\r\n        return this._targetNames.has(name);\r\n    }\r\n\r\n    /**\r\n     * Checks if the mask retains a target name.\r\n     * Note that in the \"Exclude\" mode, this will return false if the mask includes the name, and true otherwise!\r\n     * This method is intended to know if a given target name is retained by the mask, not if the name is in the list of target names.\r\n     * @param name The name to check with the mask\r\n     * @returns True if the mask retains the name, false otherwise\r\n     */\r\n    public retainsTarget(name: string): boolean {\r\n        return this._targetNames.has(name) === (this.mode === AnimationGroupMaskMode.Include);\r\n    }\r\n}\r\n"]}