(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[245],{4884:(e,t,i)=>{"use strict";i.d(t,{bL:()=>S,zi:()=>k});var r=i(12115),n=i(85185),s=i(6101),o=i(46081),a=i(5845),l=i(45503),u=i(11275),d=i(63655),h=i(95155),c="Switch",[p,m]=(0,o.A)(c),[f,g]=p(c),y=r.forwardRef((e,t)=>{let{__scopeSwitch:i,name:o,checked:l,defaultChecked:u,required:p,disabled:m,value:g="on",onCheckedChange:y,form:v,...b}=e,[S,k]=r.useState(null),A=(0,s.s)(t,e=>k(e)),P=r.useRef(!1),T=!S||v||!!S.closest("form"),[M,C]=(0,a.i)({prop:l,defaultProp:null!=u&&u,onChange:y,caller:c});return(0,h.jsxs)(f,{scope:i,checked:M,disabled:m,children:[(0,h.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":M,"aria-required":p,"data-state":w(M),"data-disabled":m?"":void 0,disabled:m,value:g,...b,ref:A,onClick:(0,n.m)(e.onClick,e=>{C(e=>!e),T&&(P.current=e.isPropagationStopped(),P.current||e.stopPropagation())})}),T&&(0,h.jsx)(x,{control:S,bubbles:!P.current,name:o,value:g,checked:M,required:p,disabled:m,form:v,style:{transform:"translateX(-100%)"}})]})});y.displayName=c;var v="SwitchThumb",b=r.forwardRef((e,t)=>{let{__scopeSwitch:i,...r}=e,n=g(v,i);return(0,h.jsx)(d.sG.span,{"data-state":w(n.checked),"data-disabled":n.disabled?"":void 0,...r,ref:t})});b.displayName=v;var x=r.forwardRef((e,t)=>{let{__scopeSwitch:i,control:n,checked:o,bubbles:a=!0,...d}=e,c=r.useRef(null),p=(0,s.s)(c,t),m=(0,l.Z)(o),f=(0,u.X)(n);return r.useEffect(()=>{let e=c.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==o&&t){let i=new Event("click",{bubbles:a});t.call(e,o),e.dispatchEvent(i)}},[m,o,a]),(0,h.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:o,...d,tabIndex:-1,ref:p,style:{...d.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var S=y,k=b},5845:(e,t,i)=>{"use strict";i.d(t,{i:()=>a});var r,n=i(12115),s=i(52712),o=(r||(r=i.t(n,2)))[" useInsertionEffect ".trim().toString()]||s.N;function a({prop:e,defaultProp:t,onChange:i=()=>{},caller:r}){let[s,a,l]=function({defaultProp:e,onChange:t}){let[i,r]=n.useState(e),s=n.useRef(i),a=n.useRef(t);return o(()=>{a.current=t},[t]),n.useEffect(()=>{s.current!==i&&(a.current?.(i),s.current=i)},[i,s]),[i,r,a]}({defaultProp:t,onChange:i}),u=void 0!==e,d=u?e:s;{let t=n.useRef(void 0!==e);n.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[d,n.useCallback(t=>{if(u){let i="function"==typeof t?t(e):t;i!==e&&l.current?.(i)}else a(t)},[u,e,a,l])]}Symbol("RADIX:SYNC_STATE")},6101:(e,t,i)=>{"use strict";i.d(t,{s:()=>o,t:()=>s});var r=i(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let i=!1,r=e.map(e=>{let r=n(e,t);return i||"function"!=typeof r||(i=!0),r});if(i)return()=>{for(let t=0;t<r.length;t++){let i=r[t];"function"==typeof i?i():n(e[t],null)}}}}function o(...e){return r.useCallback(s(...e),e)}},9771:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("volume-x",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]])},11275:(e,t,i)=>{"use strict";i.d(t,{X:()=>s});var r=i(12115),n=i(52712);function s(e){let[t,i]=r.useState(void 0);return(0,n.N)(()=>{if(e){i({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,n;if(!Array.isArray(t)||!t.length)return;let s=t[0];if("borderBoxSize"in s){let e=s.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,n=t.blockSize}else r=e.offsetWidth,n=e.offsetHeight;i({width:r,height:n})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}i(void 0)},[e]),t}},11518:(e,t,i)=>{"use strict";e.exports=i(82269).style},15273:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},15564:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return f},defaultHead:function(){return h}});let r=i(88229),n=i(6966),s=i(95155),o=n._(i(12115)),a=r._(i(85029)),l=i(42464),u=i(82830),d=i(17544);function h(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function c(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}i(43230);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:i}=t;return e.reduce(c,[]).reverse().concat(h(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,r={};return n=>{let s=!0,o=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){o=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?s=!1:t.add(n.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(n.props.hasOwnProperty(t))if("charSet"===t)i.has(t)?s=!1:i.add(t);else{let e=n.props[t],i=r[t]||new Set;("name"!==t||!o)&&i.has(e)?s=!1:(i.add(e),r[t]=i)}}}return s}}()).reverse().map((e,t)=>{let i=e.key||t;return o.default.cloneElement(e,{key:i})})}let f=function(e){let{children:t}=e,i=(0,o.useContext)(l.AmpStateContext),r=(0,o.useContext)(u.HeadManagerContext);return(0,s.jsx)(a.default,{reduceComponentsToState:m,headManager:r,inAmpMode:(0,d.isInAmpMode)(i),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17544:(e,t)=>{"use strict";function i(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||i&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return i}})},19946:(e,t,i)=>{"use strict";i.d(t,{A:()=>l});var r=i(12115);let n=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},s=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((e,t)=>{let{color:i="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:d,iconNode:h,...c}=e;return(0,r.createElement)("svg",{ref:t,...o,width:n,height:n,stroke:i,strokeWidth:l?24*Number(a)/Number(n):a,className:s("lucide",u),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(c)&&{"aria-hidden":"true"},...c},[...h.map(e=>{let[t,i]=e;return(0,r.createElement)(t,i)}),...Array.isArray(d)?d:[d]])}),l=(e,t)=>{let i=(0,r.forwardRef)((i,o)=>{let{className:l,...u}=i;return(0,r.createElement)(a,{ref:o,iconNode:t,className:s("lucide-".concat(n(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),l),...u})});return i.displayName=n(e),i}},22773:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("atom",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["path",{d:"M20.2 20.2c2.04-2.03.02-7.36-4.5-11.9-4.54-4.52-9.87-6.54-11.9-4.5-2.04 2.03-.02 7.36 4.5 11.9 4.54 4.52 9.87 6.54 11.9 4.5Z",key:"1l2ple"}],["path",{d:"M15.7 15.7c4.52-4.54 6.54-9.87 4.5-11.9-2.03-2.04-7.36-.02-11.9 4.5-4.52 4.54-6.54 9.87-4.5 11.9 2.03 2.04 7.36.02 11.9-4.5Z",key:"1wam0m"}]])},28883:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},33063:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return x}});let r=i(88229),n=i(6966),s=i(95155),o=n._(i(12115)),a=r._(i(47650)),l=r._(i(15564)),u=i(38883),d=i(95840),h=i(86752);i(43230);let c=i(70901),p=r._(i(51193)),m=i(6654),f={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,i,r,n,s,o){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,n=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function y(e){return o.use?{fetchPriority:e}:{fetchpriority:e}}let v=(0,o.forwardRef)((e,t)=>{let{src:i,srcSet:r,sizes:n,height:a,width:l,decoding:u,className:d,style:h,fetchPriority:c,placeholder:p,loading:f,unoptimized:v,fill:b,onLoadRef:x,onLoadingCompleteRef:w,setBlurComplete:S,setShowAltText:k,sizesInput:A,onLoad:P,onError:T,...M}=e,C=(0,o.useCallback)(e=>{e&&(T&&(e.src=e.src),e.complete&&g(e,p,x,w,S,v,A))},[i,p,x,w,S,T,v,A]),E=(0,m.useMergedRef)(t,C);return(0,s.jsx)("img",{...M,...y(c),loading:f,width:l,height:a,decoding:u,"data-nimg":b?"fill":"1",className:d,style:h,sizes:n,srcSet:r,src:i,ref:E,onLoad:e=>{g(e.currentTarget,p,x,w,S,v,A)},onError:e=>{k(!0),"empty"!==p&&S(!0),T&&T(e)}})});function b(e){let{isAppRouter:t,imgAttributes:i}=e,r={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...y(i.fetchPriority)};return t&&a.default.preload?(a.default.preload(i.src,r),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...r},"__nimg-"+i.src+i.srcSet+i.sizes)})}let x=(0,o.forwardRef)((e,t)=>{let i=(0,o.useContext)(c.RouterContext),r=(0,o.useContext)(h.ImageConfigContext),n=(0,o.useMemo)(()=>{var e;let t=f||r||d.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:n,qualities:s}},[r]),{onLoad:a,onLoadingComplete:l}=e,m=(0,o.useRef)(a);(0,o.useEffect)(()=>{m.current=a},[a]);let g=(0,o.useRef)(l);(0,o.useEffect)(()=>{g.current=l},[l]);let[y,x]=(0,o.useState)(!1),[w,S]=(0,o.useState)(!1),{props:k,meta:A}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:n,blurComplete:y,showAltText:w});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v,{...k,unoptimized:A.unoptimized,placeholder:A.placeholder,fill:A.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:x,setShowAltText:S,sizesInput:e.sizes,ref:t}),A.priority?(0,s.jsx)(b,{isAppRouter:!i,imgAttributes:k}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33786:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},37328:(e,t,i)=>{"use strict";function r(e,t,i){if(!t.has(e))throw TypeError("attempted to "+i+" private field on non-instance");return t.get(e)}function n(e,t){var i=r(e,t,"get");return i.get?i.get.call(e):i.value}function s(e,t,i){var n=r(e,t,"set");if(n.set)n.set.call(e,i);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=i}return i}i.d(t,{N:()=>c});var o,a=i(12115),l=i(46081),u=i(6101),d=i(99708),h=i(95155);function c(e){let t=e+"CollectionProvider",[i,r]=(0,l.A)(t),[n,s]=i(t,{collectionRef:{current:null},itemMap:new Map}),o=e=>{let{scope:t,children:i}=e,r=a.useRef(null),s=a.useRef(new Map).current;return(0,h.jsx)(n,{scope:t,itemMap:s,collectionRef:r,children:i})};o.displayName=t;let c=e+"CollectionSlot",p=(0,d.TL)(c),m=a.forwardRef((e,t)=>{let{scope:i,children:r}=e,n=s(c,i),o=(0,u.s)(t,n.collectionRef);return(0,h.jsx)(p,{ref:o,children:r})});m.displayName=c;let f=e+"CollectionItemSlot",g="data-radix-collection-item",y=(0,d.TL)(f),v=a.forwardRef((e,t)=>{let{scope:i,children:r,...n}=e,o=a.useRef(null),l=(0,u.s)(t,o),d=s(f,i);return a.useEffect(()=>(d.itemMap.set(o,{ref:o,...n}),()=>void d.itemMap.delete(o))),(0,h.jsx)(y,{...{[g]:""},ref:l,children:r})});return v.displayName=f,[{Provider:o,Slot:m,ItemSlot:v},function(t){let i=s(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=i.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(g,"]")));return Array.from(i.itemMap.values()).sort((e,i)=>t.indexOf(e.ref.current)-t.indexOf(i.ref.current))},[i.collectionRef,i.itemMap])},r]}var p=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let i=function(e,t){let i=e.length,r=f(t),n=r>=0?r:i+r;return n<0||n>=i?-1:n}(e,t);return -1===i?void 0:e[i]}function f(e){return e!=e||0===e?0:Math.trunc(e)}o=new WeakMap,class e extends Map{set(e,t){return p.get(this)&&(this.has(e)?n(this,o)[n(this,o).indexOf(e)]=e:n(this,o).push(e)),super.set(e,t),this}insert(e,t,i){let r,s=this.has(t),a=n(this,o).length,l=f(e),u=l>=0?l:a+l,d=u<0||u>=a?-1:u;if(d===this.size||s&&d===this.size-1||-1===d)return this.set(t,i),this;let h=this.size+ +!s;l<0&&u++;let c=[...n(this,o)],p=!1;for(let e=u;e<h;e++)if(u===e){let n=c[e];c[e]===t&&(n=c[e+1]),s&&this.delete(t),r=this.get(n),this.set(t,i)}else{p||c[e-1]!==t||(p=!0);let i=c[p?e:e-1],n=r;r=this.get(i),this.delete(i),this.set(i,n)}return this}with(t,i,r){let n=new e(this);return n.insert(t,i,r),n}before(e){let t=n(this,o).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,i){let r=n(this,o).indexOf(e);return -1===r?this:this.insert(r,t,i)}after(e){let t=n(this,o).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,i){let r=n(this,o).indexOf(e);return -1===r?this:this.insert(r+1,t,i)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return s(this,o,[]),super.clear()}delete(e){let t=super.delete(e);return t&&n(this,o).splice(n(this,o).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=m(n(this,o),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=m(n(this,o),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return n(this,o).indexOf(e)}keyAt(e){return m(n(this,o),e)}from(e,t){let i=this.indexOf(e);if(-1===i)return;let r=i+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let i=this.indexOf(e);if(-1===i)return;let r=i+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let i=0;for(let r of this){if(Reflect.apply(e,t,[r,i,this]))return r;i++}}findIndex(e,t){let i=0;for(let r of this){if(Reflect.apply(e,t,[r,i,this]))return i;i++}return -1}filter(t,i){let r=[],n=0;for(let e of this)Reflect.apply(t,i,[e,n,this])&&r.push(e),n++;return new e(r)}map(t,i){let r=[],n=0;for(let e of this)r.push([e[0],Reflect.apply(t,i,[e,n,this])]),n++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];let[r,n]=t,s=0,o=null!=n?n:this.at(0);for(let e of this)o=0===s&&1===t.length?e:Reflect.apply(r,this,[o,e,s,this]),s++;return o}reduceRight(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];let[r,n]=t,s=null!=n?n:this.at(-1);for(let e=this.size-1;e>=0;e--){let i=this.at(e);s=e===this.size-1&&1===t.length?i:Reflect.apply(r,this,[s,i,e,this])}return s}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let i=this.keyAt(e),r=this.get(i);t.set(i,r)}return t}toSpliced(){for(var t=arguments.length,i=Array(t),r=0;r<t;r++)i[r]=arguments[r];let n=[...this.entries()];return n.splice(...i),new e(n)}slice(t,i){let r=new e,n=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==i&&i>0&&(n=i-1);for(let e=t;e<=n;e++){let t=this.keyAt(e),i=this.get(t);r.set(t,i)}return r}every(e,t){let i=0;for(let r of this){if(!Reflect.apply(e,t,[r,i,this]))return!1;i++}return!0}some(e,t){let i=0;for(let r of this){if(Reflect.apply(e,t,[r,i,this]))return!0;i++}return!1}constructor(e){super(e),function(e,t,i){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,i)}(this,o,{writable:!0,value:void 0}),s(this,o,[...super.keys()]),p.set(this,!0)}}},38883:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(43230);let r=i(75100),n=i(95840),s=["-moz-initial","fill","none","scale-down",void 0];function o(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let u,d,h,{src:c,sizes:p,unoptimized:m=!1,priority:f=!1,loading:g,className:y,quality:v,width:b,height:x,fill:w=!1,style:S,overrideSrc:k,onLoad:A,onLoadingComplete:P,placeholder:T="empty",blurDataURL:M,fetchPriority:C,decoding:E="async",layout:R,objectFit:j,objectPosition:D,lazyBoundary:_,lazyRoot:V,...O}=e,{imgConf:F,showAltText:z,blurComplete:I,defaultLoader:L}=t,N=F||n.imageConfigDefault;if("allSizes"in N)u=N;else{let e=[...N.deviceSizes,...N.imageSizes].sort((e,t)=>e-t),t=N.deviceSizes.sort((e,t)=>e-t),r=null==(i=N.qualities)?void 0:i.sort((e,t)=>e-t);u={...N,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=O.loader||L;delete O.loader,delete O.srcSet;let U="__next_img_default"in B;if(U){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:i,...r}=t;return e(r)}}if(R){"fill"===R&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(S={...S,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!p&&(p=t)}let $="",W=a(b),H=a(x);if((l=c)&&"object"==typeof l&&(o(l)||void 0!==l.src)){let e=o(c)?c.default:c;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,h=e.blurHeight,M=M||e.blurDataURL,$=e.src,!w)if(W||H){if(W&&!H){let t=W/e.width;H=Math.round(e.height*t)}else if(!W&&H){let t=H/e.height;W=Math.round(e.width*t)}}else W=e.width,H=e.height}let G=!f&&("lazy"===g||void 0===g);(!(c="string"==typeof c?c:$)||c.startsWith("data:")||c.startsWith("blob:"))&&(m=!0,G=!1),u.unoptimized&&(m=!0),U&&!u.dangerouslyAllowSVG&&c.split("?",1)[0].endsWith(".svg")&&(m=!0);let q=a(v),K=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:j,objectPosition:D}:{},z?{}:{color:"transparent"},S),X=I||"empty"===T?null:"blur"===T?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:W,heightInt:H,blurWidth:d,blurHeight:h,blurDataURL:M||"",objectFit:K.objectFit})+'")':'url("'+T+'")',Y=s.includes(K.objectFit)?"fill"===K.objectFit?"100% 100%":"cover":K.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:K.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},J=function(e){let{config:t,src:i,unoptimized:r,width:n,quality:s,sizes:o,loader:a}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,i){let{deviceSizes:r,allSizes:n}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,o),d=l.length-1;return{sizes:o||"w"!==u?o:"100vw",srcSet:l.map((e,r)=>a({config:t,src:i,quality:s,width:e})+" "+("w"===u?e:r+1)+u).join(", "),src:a({config:t,src:i,quality:s,width:l[d]})}}({config:u,src:c,unoptimized:m,width:W,quality:q,sizes:p,loader:B});return{props:{...O,loading:G?"lazy":g,fetchPriority:C,width:W,height:H,decoding:E,className:y,style:{...K,...Z},sizes:J.sizes,srcSet:J.srcSet,src:k||J.src},meta:{unoptimized:m,priority:f,placeholder:T,fill:w}}}},39688:(e,t,i)=>{"use strict";i.d(t,{QP:()=>ee});let r=(e,t)=>{if(0===e.length)return t.classGroupId;let i=e[0],n=t.nextPart.get(i),s=n?r(e.slice(1),n):void 0;if(s)return s;if(0===t.validators.length)return;let o=e.join("-");return t.validators.find(({validator:e})=>e(o))?.classGroupId},n=/^\[(.+)\]$/,s=(e,t,i,r)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:o(t,e)).classGroupId=i;return}if("function"==typeof e)return a(e)?void s(e(r),t,i,r):void t.validators.push({validator:e,classGroupId:i});Object.entries(e).forEach(([e,n])=>{s(n,o(t,e),i,r)})})},o=(e,t)=>{let i=e;return t.split("-").forEach(e=>{i.nextPart.has(e)||i.nextPart.set(e,{nextPart:new Map,validators:[]}),i=i.nextPart.get(e)}),i},a=e=>e.isThemeGetter,l=/\s+/;function u(){let e,t,i=0,r="";for(;i<arguments.length;)(e=arguments[i++])&&(t=d(e))&&(r&&(r+=" "),r+=t);return r}let d=e=>{let t;if("string"==typeof e)return e;let i="";for(let r=0;r<e.length;r++)e[r]&&(t=d(e[r]))&&(i&&(i+=" "),i+=t);return i},h=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},c=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,m=/^\d+\/\d+$/,f=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,y=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,v=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>m.test(e),w=e=>!!e&&!Number.isNaN(Number(e)),S=e=>!!e&&Number.isInteger(Number(e)),k=e=>e.endsWith("%")&&w(e.slice(0,-1)),A=e=>f.test(e),P=()=>!0,T=e=>g.test(e)&&!y.test(e),M=()=>!1,C=e=>v.test(e),E=e=>b.test(e),R=e=>!D(e)&&!I(e),j=e=>H(e,X,M),D=e=>c.test(e),_=e=>H(e,Y,T),V=e=>H(e,Z,w),O=e=>H(e,q,M),F=e=>H(e,K,E),z=e=>H(e,Q,C),I=e=>p.test(e),L=e=>G(e,Y),N=e=>G(e,J),B=e=>G(e,q),U=e=>G(e,X),$=e=>G(e,K),W=e=>G(e,Q,!0),H=(e,t,i)=>{let r=c.exec(e);return!!r&&(r[1]?t(r[1]):i(r[2]))},G=(e,t,i=!1)=>{let r=p.exec(e);return!!r&&(r[1]?t(r[1]):i)},q=e=>"position"===e||"percentage"===e,K=e=>"image"===e||"url"===e,X=e=>"length"===e||"size"===e||"bg-size"===e,Y=e=>"length"===e,Z=e=>"number"===e,J=e=>"family-name"===e,Q=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let i,o,a,d=function(l){let u;return o=(i={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,i=new Map,r=new Map,n=(n,s)=>{i.set(n,s),++t>e&&(t=0,r=i,i=new Map)};return{get(e){let t=i.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(n(e,t),t):void 0},set(e,t){i.has(e)?i.set(e,t):n(e,t)}}})((u=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:i}=e,r=e=>{let t,i,r=[],n=0,s=0,o=0;for(let i=0;i<e.length;i++){let a=e[i];if(0===n&&0===s){if(":"===a){r.push(e.slice(o,i)),o=i+1;continue}if("/"===a){t=i;continue}}"["===a?n++:"]"===a?n--:"("===a?s++:")"===a&&s--}let a=0===r.length?e:e.substring(o),l=(i=a).endsWith("!")?i.substring(0,i.length-1):i.startsWith("!")?i.substring(1):i;return{modifiers:r,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",i=r;r=t=>t.startsWith(e)?i(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(i){let e=r;r=t=>i({className:t,parseClassName:e})}return r})(u),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let i=[],r=[];return e.forEach(e=>{"["===e[0]||t[e]?(i.push(...r.sort(),e),r=[]):r.push(e)}),i.push(...r.sort()),i}})(u),...(e=>{let t=(e=>{let{theme:t,classGroups:i}=e,r={nextPart:new Map,validators:[]};for(let e in i)s(i[e],r,e,t);return r})(e),{conflictingClassGroups:i,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let i=e.split("-");return""===i[0]&&1!==i.length&&i.shift(),r(i,t)||(e=>{if(n.test(e)){let t=n.exec(e)[1],i=t?.substring(0,t.indexOf(":"));if(i)return"arbitrary.."+i}})(e)},getConflictingClassGroupIds:(e,t)=>{let r=i[e]||[];return t&&o[e]?[...r,...o[e]]:r}}})(u)}).cache.get,a=i.cache.set,d=h,h(l)};function h(e){let t=o(e);if(t)return t;let r=((e,t)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:s}=t,o=[],a=e.trim().split(l),u="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{isExternal:l,modifiers:d,hasImportantModifier:h,baseClassName:c,maybePostfixModifierPosition:p}=i(t);if(l){u=t+(u.length>0?" "+u:u);continue}let m=!!p,f=r(m?c.substring(0,p):c);if(!f){if(!m||!(f=r(c))){u=t+(u.length>0?" "+u:u);continue}m=!1}let g=s(d).join(":"),y=h?g+"!":g,v=y+f;if(o.includes(v))continue;o.push(v);let b=n(f,m);for(let e=0;e<b.length;++e){let t=b[e];o.push(y+t)}u=t+(u.length>0?" "+u:u)}return u})(e,i);return a(e,r),r}return function(){return d(u.apply(null,arguments))}}(()=>{let e=h("color"),t=h("font"),i=h("text"),r=h("font-weight"),n=h("tracking"),s=h("leading"),o=h("breakpoint"),a=h("container"),l=h("spacing"),u=h("radius"),d=h("shadow"),c=h("inset-shadow"),p=h("text-shadow"),m=h("drop-shadow"),f=h("blur"),g=h("perspective"),y=h("aspect"),v=h("ease"),b=h("animate"),T=()=>["auto","avoid","all","avoid-page","page","left","right","column"],M=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...M(),I,D],E=()=>["auto","hidden","clip","visible","scroll"],H=()=>["auto","contain","none"],G=()=>[I,D,l],q=()=>[x,"full","auto",...G()],K=()=>[S,"none","subgrid",I,D],X=()=>["auto",{span:["full",S,I,D]},S,I,D],Y=()=>[S,"auto",I,D],Z=()=>["auto","min","max","fr",I,D],J=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...G()],et=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],ei=()=>[e,I,D],er=()=>[...M(),B,O,{position:[I,D]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],es=()=>["auto","cover","contain",U,j,{size:[I,D]}],eo=()=>[k,L,_],ea=()=>["","none","full",u,I,D],el=()=>["",w,L,_],eu=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eh=()=>[w,k,B,O],ec=()=>["","none",f,I,D],ep=()=>["none",w,I,D],em=()=>["none",w,I,D],ef=()=>[w,I,D],eg=()=>[x,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[A],breakpoint:[A],color:[P],container:[A],"drop-shadow":[A],ease:["in","out","in-out"],font:[R],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[A],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[A],shadow:[A],spacing:["px",w],text:[A],"text-shadow":[A],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,D,I,y]}],container:["container"],columns:[{columns:[w,D,I,a]}],"break-after":[{"break-after":T()}],"break-before":[{"break-before":T()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:H()}],"overscroll-x":[{"overscroll-x":H()}],"overscroll-y":[{"overscroll-y":H()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:q()}],"inset-x":[{"inset-x":q()}],"inset-y":[{"inset-y":q()}],start:[{start:q()}],end:[{end:q()}],top:[{top:q()}],right:[{right:q()}],bottom:[{bottom:q()}],left:[{left:q()}],visibility:["visible","invisible","collapse"],z:[{z:[S,"auto",I,D]}],basis:[{basis:[x,"full","auto",a,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",D]}],grow:[{grow:["",w,I,D]}],shrink:[{shrink:["",w,I,D]}],order:[{order:[S,"first","last","none",I,D]}],"grid-cols":[{"grid-cols":K()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":K()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...J(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...J()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":J()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",i,L,_]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,I,V]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",k,D]}],"font-family":[{font:[N,D,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,I,D]}],"line-clamp":[{"line-clamp":[w,"none",I,V]}],leading:[{leading:[s,...G()]}],"list-image":[{"list-image":["none",I,D]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",I,D]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ei()}],"text-color":[{text:ei()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...eu(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",I,_]}],"text-decoration-color":[{decoration:ei()}],"underline-offset":[{"underline-offset":[w,"auto",I,D]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",I,D]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",I,D]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:er()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:es()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},S,I,D],radial:["",I,D],conic:[S,I,D]},$,F]}],"bg-color":[{bg:ei()}],"gradient-from-pos":[{from:eo()}],"gradient-via-pos":[{via:eo()}],"gradient-to-pos":[{to:eo()}],"gradient-from":[{from:ei()}],"gradient-via":[{via:ei()}],"gradient-to":[{to:ei()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...eu(),"hidden","none"]}],"divide-style":[{divide:[...eu(),"hidden","none"]}],"border-color":[{border:ei()}],"border-color-x":[{"border-x":ei()}],"border-color-y":[{"border-y":ei()}],"border-color-s":[{"border-s":ei()}],"border-color-e":[{"border-e":ei()}],"border-color-t":[{"border-t":ei()}],"border-color-r":[{"border-r":ei()}],"border-color-b":[{"border-b":ei()}],"border-color-l":[{"border-l":ei()}],"divide-color":[{divide:ei()}],"outline-style":[{outline:[...eu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,I,D]}],"outline-w":[{outline:["",w,L,_]}],"outline-color":[{outline:ei()}],shadow:[{shadow:["","none",d,W,z]}],"shadow-color":[{shadow:ei()}],"inset-shadow":[{"inset-shadow":["none",c,W,z]}],"inset-shadow-color":[{"inset-shadow":ei()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ei()}],"ring-offset-w":[{"ring-offset":[w,_]}],"ring-offset-color":[{"ring-offset":ei()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":ei()}],"text-shadow":[{"text-shadow":["none",p,W,z]}],"text-shadow-color":[{"text-shadow":ei()}],opacity:[{opacity:[w,I,D]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":eh()}],"mask-image-linear-to-pos":[{"mask-linear-to":eh()}],"mask-image-linear-from-color":[{"mask-linear-from":ei()}],"mask-image-linear-to-color":[{"mask-linear-to":ei()}],"mask-image-t-from-pos":[{"mask-t-from":eh()}],"mask-image-t-to-pos":[{"mask-t-to":eh()}],"mask-image-t-from-color":[{"mask-t-from":ei()}],"mask-image-t-to-color":[{"mask-t-to":ei()}],"mask-image-r-from-pos":[{"mask-r-from":eh()}],"mask-image-r-to-pos":[{"mask-r-to":eh()}],"mask-image-r-from-color":[{"mask-r-from":ei()}],"mask-image-r-to-color":[{"mask-r-to":ei()}],"mask-image-b-from-pos":[{"mask-b-from":eh()}],"mask-image-b-to-pos":[{"mask-b-to":eh()}],"mask-image-b-from-color":[{"mask-b-from":ei()}],"mask-image-b-to-color":[{"mask-b-to":ei()}],"mask-image-l-from-pos":[{"mask-l-from":eh()}],"mask-image-l-to-pos":[{"mask-l-to":eh()}],"mask-image-l-from-color":[{"mask-l-from":ei()}],"mask-image-l-to-color":[{"mask-l-to":ei()}],"mask-image-x-from-pos":[{"mask-x-from":eh()}],"mask-image-x-to-pos":[{"mask-x-to":eh()}],"mask-image-x-from-color":[{"mask-x-from":ei()}],"mask-image-x-to-color":[{"mask-x-to":ei()}],"mask-image-y-from-pos":[{"mask-y-from":eh()}],"mask-image-y-to-pos":[{"mask-y-to":eh()}],"mask-image-y-from-color":[{"mask-y-from":ei()}],"mask-image-y-to-color":[{"mask-y-to":ei()}],"mask-image-radial":[{"mask-radial":[I,D]}],"mask-image-radial-from-pos":[{"mask-radial-from":eh()}],"mask-image-radial-to-pos":[{"mask-radial-to":eh()}],"mask-image-radial-from-color":[{"mask-radial-from":ei()}],"mask-image-radial-to-color":[{"mask-radial-to":ei()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":M()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":eh()}],"mask-image-conic-to-pos":[{"mask-conic-to":eh()}],"mask-image-conic-from-color":[{"mask-conic-from":ei()}],"mask-image-conic-to-color":[{"mask-conic-to":ei()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:er()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:es()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",I,D]}],filter:[{filter:["","none",I,D]}],blur:[{blur:ec()}],brightness:[{brightness:[w,I,D]}],contrast:[{contrast:[w,I,D]}],"drop-shadow":[{"drop-shadow":["","none",m,W,z]}],"drop-shadow-color":[{"drop-shadow":ei()}],grayscale:[{grayscale:["",w,I,D]}],"hue-rotate":[{"hue-rotate":[w,I,D]}],invert:[{invert:["",w,I,D]}],saturate:[{saturate:[w,I,D]}],sepia:[{sepia:["",w,I,D]}],"backdrop-filter":[{"backdrop-filter":["","none",I,D]}],"backdrop-blur":[{"backdrop-blur":ec()}],"backdrop-brightness":[{"backdrop-brightness":[w,I,D]}],"backdrop-contrast":[{"backdrop-contrast":[w,I,D]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,I,D]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,I,D]}],"backdrop-invert":[{"backdrop-invert":["",w,I,D]}],"backdrop-opacity":[{"backdrop-opacity":[w,I,D]}],"backdrop-saturate":[{"backdrop-saturate":[w,I,D]}],"backdrop-sepia":[{"backdrop-sepia":["",w,I,D]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",I,D]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",I,D]}],ease:[{ease:["linear","initial",v,I,D]}],delay:[{delay:[w,I,D]}],animate:[{animate:["none",b,I,D]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,I,D]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[I,D,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:ei()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ei()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",I,D]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",I,D]}],fill:[{fill:["none",...ei()]}],"stroke-w":[{stroke:[w,L,_,V]}],stroke:[{stroke:["none",...ei()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},40968:(e,t,i)=>{"use strict";i.d(t,{b:()=>a});var r=i(12115),n=i(63655),s=i(95155),o=r.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var i;t.target.closest("button, input, select, textarea")||(null==(i=e.onMouseDown)||i.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var a=o},42198:(e,t,i)=>{"use strict";function r(e,t,i){if(e instanceof EventTarget)return[e];if("string"==typeof e){let r=document;t&&(r=t.current);let n=i?.[e]??r.querySelectorAll(e);return n?Array.from(n):[]}return Array.from(e)}i.d(t,{K:()=>r})},42464:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=i(88229)._(i(12115)).default.createContext({})},43409:(e,t,i)=>{"use strict";let r;i.d(t,{P:()=>sc});var n=i(12115);let s=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(s),a=e=>180*e/Math.PI,l=e=>d(a(Math.atan2(e[1],e[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:l,rotateZ:l,skewX:e=>a(Math.atan(e[1])),skewY:e=>a(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},d=e=>((e%=360)<0&&(e+=360),e),h=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),c=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:h,scaleY:c,scale:e=>(h(e)+c(e))/2,rotateX:e=>d(a(Math.atan2(e[6],e[5]))),rotateY:e=>d(a(Math.atan2(-e[2],e[0]))),rotateZ:l,rotate:l,skewX:e=>a(Math.atan(e[4])),skewY:e=>a(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function m(e){return+!!e.includes("scale")}function f(e,t){let i,r;if(!e||"none"===e)return m(t);let n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=p,r=n;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=u,r=t}if(!r)return m(t);let s=i[t],o=r[1].split(",").map(g);return"function"==typeof s?s(o):o[s]}function g(e){return parseFloat(e.trim())}let y=e=>t=>"string"==typeof t&&t.startsWith(e),v=y("--"),b=y("var(--"),x=e=>!!b(e)&&w.test(e.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function S({top:e,left:t,right:i,bottom:r}){return{x:{min:t,max:i},y:{min:e,max:r}}}let k=(e,t,i)=>e+(t-e)*i;function A(e){return void 0===e||1===e}function P({scale:e,scaleX:t,scaleY:i}){return!A(e)||!A(t)||!A(i)}function T(e){return P(e)||M(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function M(e){var t,i;return(t=e.x)&&"0%"!==t||(i=e.y)&&"0%"!==i}function C(e,t,i,r,n){return void 0!==n&&(e=r+n*(e-r)),r+i*(e-r)+t}function E(e,t=0,i=1,r,n){e.min=C(e.min,t,i,r,n),e.max=C(e.max,t,i,r,n)}function R(e,{x:t,y:i}){E(e.x,t.translate,t.scale,t.originPoint),E(e.y,i.translate,i.scale,i.originPoint)}function j(e,t){e.min=e.min+t,e.max=e.max+t}function D(e,t,i,r,n=.5){let s=k(e.min,e.max,n);E(e,t,i,s,r)}function _(e,t){D(e.x,t.x,t.scaleX,t.scale,t.originX),D(e.y,t.y,t.scaleY,t.scale,t.originY)}function V(e,t){return S(function(e,t){if(!t)return e;let i=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let O=new Set(["width","height","top","left","right","bottom",...s]),F=(e,t,i)=>i>t?t:i<e?e:i,z={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},I={...z,transform:e=>F(0,1,e)},L={...z,default:1},N=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),B=N("deg"),U=N("%"),$=N("px"),W=N("vh"),H=N("vw"),G={...U,parse:e=>U.parse(e)/100,transform:e=>U.transform(100*e)},q=e=>t=>t.test(e),K=[z,$,U,B,H,W,{test:e=>"auto"===e,parse:e=>e}],X=e=>K.find(q(e)),Y=()=>{},Z=()=>{},J=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),Q=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ee=e=>e===z||e===$,et=new Set(["x","y","z"]),ei=s.filter(e=>!et.has(e)),er={width:({x:e},{paddingLeft:t="0",paddingRight:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),height:({y:e},{paddingTop:t="0",paddingBottom:i="0"})=>e.max-e.min-parseFloat(t)-parseFloat(i),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>f(t,"x"),y:(e,{transform:t})=>f(t,"y")};er.translateX=er.x,er.translateY=er.y;let en=e=>e,es={},eo=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ea={value:null,addProjectionMetrics:null};function el(e,t){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=eo.reduce((e,i)=>(e[i]=function(e,t){let i=new Set,r=new Set,n=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){o.has(t)&&(d.schedule(t),e()),l++,t(a)}let d={schedule:(e,t=!1,s=!1)=>{let a=s&&n?i:r;return t&&o.add(e),a.has(e)||a.add(e),e},cancel:e=>{r.delete(e),o.delete(e)},process:e=>{if(a=e,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(u),t&&ea.value&&ea.value.frameloop[t].push(l),l=0,i.clear(),n=!1,s&&(s=!1,d.process(e))}};return d}(s,t?i:void 0),e),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:d,update:h,preRender:c,render:p,postRender:m}=o,f=()=>{let s=es.useManualTiming?n.timestamp:performance.now();i=!1,es.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),d.process(n),h.process(n),c.process(n),p.process(n),m.process(n),n.isProcessing=!1,i&&t&&(r=!1,e(f))};return{schedule:eo.reduce((t,s)=>{let a=o[s];return t[s]=(t,s=!1,o=!1)=>(!i&&(i=!0,r=!0,n.isProcessing||e(f)),a.schedule(t,s,o)),t},{}),cancel:e=>{for(let t=0;t<eo.length;t++)o[eo[t]].cancel(e)},state:n,steps:o}}let{schedule:eu,cancel:ed,state:eh,steps:ec}=el("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:en,!0),ep=new Set,em=!1,ef=!1,eg=!1;function ey(){if(ef){let e=Array.from(ep).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),i=new Map;t.forEach(e=>{let t=function(e){let t=[];return ei.forEach(i=>{let r=e.getValue(i);void 0!==r&&(t.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),t}(e);t.length&&(i.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=i.get(e);t&&t.forEach(([t,i])=>{e.getValue(t)?.set(i)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}ef=!1,em=!1,ep.forEach(e=>e.complete(eg)),ep.clear()}function ev(){ep.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(ef=!0)})}class eb{constructor(e,t,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(ep.add(this),em||(em=!0,eu.read(ev),eu.resolveKeyframes(ey))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:i,motionValue:r}=this;if(null===e[0]){let n=r?.get(),s=e[e.length-1];if(void 0!==n)e[0]=n;else if(i&&t){let r=i.readValue(t,s);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=s),r&&void 0===n&&r.set(e[0])}for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),ep.delete(this)}cancel(){"scheduled"===this.state&&(ep.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let ex=e=>/^0[^.\s]+$/u.test(e),ew=e=>Math.round(1e5*e)/1e5,eS=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ek=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eA=(e,t)=>i=>!!("string"==typeof i&&ek.test(i)&&i.startsWith(e)||t&&null!=i&&Object.prototype.hasOwnProperty.call(i,t)),eP=(e,t,i)=>r=>{if("string"!=typeof r)return r;let[n,s,o,a]=r.match(eS);return{[e]:parseFloat(n),[t]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},eT={...z,transform:e=>Math.round(F(0,255,e))},eM={test:eA("rgb","red"),parse:eP("red","green","blue"),transform:({red:e,green:t,blue:i,alpha:r=1})=>"rgba("+eT.transform(e)+", "+eT.transform(t)+", "+eT.transform(i)+", "+ew(I.transform(r))+")"},eC={test:eA("#"),parse:function(e){let t="",i="",r="",n="";return e.length>5?(t=e.substring(1,3),i=e.substring(3,5),r=e.substring(5,7),n=e.substring(7,9)):(t=e.substring(1,2),i=e.substring(2,3),r=e.substring(3,4),n=e.substring(4,5),t+=t,i+=i,r+=r,n+=n),{red:parseInt(t,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:eM.transform},eE={test:eA("hsl","hue"),parse:eP("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:i,alpha:r=1})=>"hsla("+Math.round(e)+", "+U.transform(ew(t))+", "+U.transform(ew(i))+", "+ew(I.transform(r))+")"},eR={test:e=>eM.test(e)||eC.test(e)||eE.test(e),parse:e=>eM.test(e)?eM.parse(e):eE.test(e)?eE.parse(e):eC.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eM.transform(e):eE.transform(e),getAnimatableNone:e=>{let t=eR.parse(e);return t.alpha=0,eR.transform(t)}},ej=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eD="number",e_="color",eV=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eO(e){let t=e.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,o=t.replace(eV,e=>(eR.test(e)?(r.color.push(s),n.push(e_),i.push(eR.parse(e))):e.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(e)):(r.number.push(s),n.push(eD),i.push(parseFloat(e))),++s,"${}")).split("${}");return{values:i,split:o,indexes:r,types:n}}function eF(e){return eO(e).values}function ez(e){let{split:t,types:i}=eO(e),r=t.length;return e=>{let n="";for(let s=0;s<r;s++)if(n+=t[s],void 0!==e[s]){let t=i[s];t===eD?n+=ew(e[s]):t===e_?n+=eR.transform(e[s]):n+=e[s]}return n}}let eI=e=>"number"==typeof e?0:eR.test(e)?eR.getAnimatableNone(e):e,eL={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(eS)?.length||0)+(e.match(ej)?.length||0)>0},parse:eF,createTransformer:ez,getAnimatableNone:function(e){let t=eF(e);return ez(e)(t.map(eI))}},eN=new Set(["brightness","contrast","saturate","opacity"]);function eB(e){let[t,i]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=i.match(eS)||[];if(!r)return e;let n=i.replace(r,""),s=+!!eN.has(t);return r!==i&&(s*=100),t+"("+s+n+")"}let eU=/\b([a-z-]*)\(.*?\)/gu,e$={...eL,getAnimatableNone:e=>{let t=e.match(eU);return t?t.map(eB).join(" "):e}},eW={...z,transform:Math.round},eH={borderWidth:$,borderTopWidth:$,borderRightWidth:$,borderBottomWidth:$,borderLeftWidth:$,borderRadius:$,radius:$,borderTopLeftRadius:$,borderTopRightRadius:$,borderBottomRightRadius:$,borderBottomLeftRadius:$,width:$,maxWidth:$,height:$,maxHeight:$,top:$,right:$,bottom:$,left:$,padding:$,paddingTop:$,paddingRight:$,paddingBottom:$,paddingLeft:$,margin:$,marginTop:$,marginRight:$,marginBottom:$,marginLeft:$,backgroundPositionX:$,backgroundPositionY:$,rotate:B,rotateX:B,rotateY:B,rotateZ:B,scale:L,scaleX:L,scaleY:L,scaleZ:L,skew:B,skewX:B,skewY:B,distance:$,translateX:$,translateY:$,translateZ:$,x:$,y:$,z:$,perspective:$,transformPerspective:$,opacity:I,originX:G,originY:G,originZ:$,zIndex:eW,fillOpacity:I,strokeOpacity:I,numOctaves:eW},eG={...eH,color:eR,backgroundColor:eR,outlineColor:eR,fill:eR,stroke:eR,borderColor:eR,borderTopColor:eR,borderRightColor:eR,borderBottomColor:eR,borderLeftColor:eR,filter:e$,WebkitFilter:e$},eq=e=>eG[e];function eK(e,t){let i=eq(e);return i!==e$&&(i=eL),i.getAnimatableNone?i.getAnimatableNone(t):void 0}let eX=new Set(["auto","none","0"]);class eY extends eb{constructor(e,t,i,r,n){super(e,t,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:i}=this;if(!t||!t.current)return;super.readKeyframes();for(let i=0;i<e.length;i++){let r=e[i];if("string"==typeof r&&x(r=r.trim())){let n=function e(t,i,r=1){Z(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[n,s]=function(e){let t=Q.exec(e);if(!t)return[,];let[,i,r,n]=t;return[`--${i??r}`,n]}(t);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let e=o.trim();return J(e)?parseFloat(e):e}return x(s)?e(s,i,r+1):s}(r,t.current);void 0!==n&&(e[i]=n),i===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!O.has(i)||2!==e.length)return;let[r,n]=e,s=X(r),o=X(n);if(s!==o)if(ee(s)&&ee(o))for(let t=0;t<e.length;t++){let i=e[t];"string"==typeof i&&(e[t]=parseFloat(i))}else er[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,i=[];for(let t=0;t<e.length;t++){var r;(null===e[t]||("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||ex(r)))&&i.push(t)}i.length&&function(e,t,i){let r,n=0;for(;n<e.length&&!r;){let t=e[n];"string"==typeof t&&!eX.has(t)&&eO(t).values.length&&(r=e[n]),n++}if(r&&i)for(let n of t)e[n]=eK(i,r)}(e,i,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:i}=this;if(!e||!e.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=er[i](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(i,r).jump(r,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:i}=this;if(!e||!e.current)return;let r=e.getValue(t);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=er[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eZ=e=>!!(e&&e.getVelocity);function eJ(){r=void 0}let eQ={now:()=>(void 0===r&&eQ.set(eh.isProcessing||es.useManualTiming?eh.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(eJ)}};function e0(e,t){-1===e.indexOf(t)&&e.push(t)}function e1(e,t){let i=e.indexOf(t);i>-1&&e.splice(i,1)}class e2{constructor(){this.subscriptions=[]}add(e){return e0(this.subscriptions,e),()=>e1(this.subscriptions,e)}notify(e,t,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(e,t,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let e5={current:void 0};class e3{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=e=>{let t=eQ.now();if(this.updatedAt!==t&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty()},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=eQ.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new e2);let i=this.events[e].add(t);return"change"===e?()=>{i(),eu.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,t,i){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return e5.current&&e5.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=eQ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function e4(e,t){return new e3(e,t)}let e6=[...K,eR,eL],{schedule:e9}=el(queueMicrotask,!1),e8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},e7={};for(let e in e8)e7[e]={isEnabled:t=>e8[e].some(e=>!!t[e])};let te=()=>({translate:0,scale:1,origin:0,originPoint:0}),tt=()=>({x:te(),y:te()}),ti=()=>({min:0,max:0}),tr=()=>({x:ti(),y:ti()}),tn="undefined"!=typeof window,ts={current:null},to={current:!1},ta=new WeakMap;function tl(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function tu(e){return"string"==typeof e||Array.isArray(e)}let td=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],th=["initial",...td];function tc(e){return tl(e.animate)||th.some(t=>tu(e[t]))}function tp(e){return!!(tc(e)||e.variants)}function tm(e){let t=[{},{}];return e?.values.forEach((e,i)=>{t[0][i]=e.get(),t[1][i]=e.getVelocity()}),t}function tf(e,t,i,r){if("function"==typeof t){let[n,s]=tm(r);t=t(void 0!==i?i:e.custom,n,s)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[n,s]=tm(r);t=t(void 0!==i?i:e.custom,n,s)}return t}let tg=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ty{scrapeMotionValuesFromProps(e,t,i){return{}}constructor({parent:e,props:t,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eb,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=eQ.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,eu.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=tc(t),this.isVariantNode=tp(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==a[e]&&eZ(t)&&t.set(a[e])}}mount(e){this.current=e,ta.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),to.current||function(){if(to.current=!0,tn)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ts.current=e.matches;e.addEventListener("change",t),t()}else ts.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ts.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),ed(this.notifyUpdate),ed(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,t){let i;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=o.has(e);r&&this.onBindTransform&&this.onBindTransform();let n=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&eu.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{n(),i&&i(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in e7){let t=e7[e];if(!t)continue;let{isEnabled:i,Feature:r}=t;if(!this.features[e]&&r&&i(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):tr()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<tg.length;t++){let i=tg[t];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=e["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(e,t,i){for(let r in t){let n=t[r],s=i[r];if(eZ(n))e.addValue(r,n);else if(eZ(s))e.addValue(r,e4(n,{owner:e}));else if(s!==n)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(n):t.hasAnimated||t.set(n)}else{let t=e.getStaticValue(r);e.addValue(r,e4(void 0!==t?t:n,{owner:e}))}}for(let r in i)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let i=this.values.get(e);t!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return void 0===i&&void 0!==t&&(i=e4(null===t?void 0:t,{owner:this}),this.addValue(e,i)),i}readValue(e,t){let i=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];if(null!=i){if("string"==typeof i&&(J(i)||ex(i)))i=parseFloat(i);else{let r;r=i,!e6.find(q(r))&&eL.test(t)&&(i=eK(e,t))}this.setBaseTarget(e,eZ(i)?i.get():i)}return eZ(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=tf(this.props,i,this.presenceContext?.custom);r&&(t=r[e])}if(i&&void 0!==t)return t;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||eZ(r)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new e2),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}scheduleRenderMicrotask(){e9.render(this.render)}}class tv extends ty{constructor(){super(...arguments),this.KeyframeResolver=eY}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:i}){delete t[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eZ(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}let tb=(e,t)=>t&&"number"==typeof e?t.transform(e):e,tx={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tw=s.length;function tS(e,t,i){let{style:r,vars:n,transformOrigin:a}=e,l=!1,u=!1;for(let e in t){let i=t[e];if(o.has(e)){l=!0;continue}if(v(e)){n[e]=i;continue}{let t=tb(i,eH[e]);e.startsWith("origin")?(u=!0,a[e]=t):r[e]=t}}if(!t.transform&&(l||i?r.transform=function(e,t,i){let r="",n=!0;for(let o=0;o<tw;o++){let a=s[o],l=e[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||i){let e=tb(l,eH[a]);if(!u){n=!1;let t=tx[a]||a;r+=`${t}(${e}) `}i&&(t[a]=e)}}return r=r.trim(),i?r=i(t,n?"":r):n&&(r="none"),r}(t,e.transform,i):r.transform&&(r.transform="none")),u){let{originX:e="50%",originY:t="50%",originZ:i=0}=a;r.transformOrigin=`${e} ${t} ${i}`}}function tk(e,{style:t,vars:i},r,n){let s,o=e.style;for(s in t)o[s]=t[s];for(s in n?.applyProjectionStyles(o,r),i)o.setProperty(s,i[s])}let tA={};function tP(e,{layout:t,layoutId:i}){return o.has(e)||e.startsWith("origin")||(t||void 0!==i)&&(!!tA[e]||"opacity"===e)}function tT(e,t,i){let{style:r}=e,n={};for(let s in r)(eZ(r[s])||t.style&&eZ(t.style[s])||tP(s,e)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}class tM extends tv{constructor(){super(...arguments),this.type="html",this.renderInstance=tk}readValueFromInstance(e,t){if(o.has(t))return this.projection?.isProjecting?m(t):((e,t)=>{let{transform:i="none"}=getComputedStyle(e);return f(i,t)})(e,t);{let i=window.getComputedStyle(e),r=(v(t)?i.getPropertyValue(t):i[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return V(e,t)}build(e,t,i){tS(e,t,i.transformTemplate)}scrapeMotionValuesFromProps(e,t,i){return tT(e,t,i)}}let tC=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tE={offset:"stroke-dashoffset",array:"stroke-dasharray"},tR={offset:"strokeDashoffset",array:"strokeDasharray"};function tj(e,{attrX:t,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:o=0,...a},l,u,d){if(tS(e,a,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:c}=e;h.transform&&(c.transform=h.transform,delete h.transform),(c.transform||h.transformOrigin)&&(c.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),c.transform&&(c.transformBox=d?.transformBox??"fill-box",delete h.transformBox),void 0!==t&&(h.x=t),void 0!==i&&(h.y=i),void 0!==r&&(h.scale=r),void 0!==n&&function(e,t,i=1,r=0,n=!0){e.pathLength=1;let s=n?tE:tR;e[s.offset]=$.transform(-r);let o=$.transform(t),a=$.transform(i);e[s.array]=`${o} ${a}`}(h,n,s,o,!1)}let tD=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),t_=e=>"string"==typeof e&&"svg"===e.toLowerCase();function tV(e,t,i){let r=tT(e,t,i);for(let i in e)(eZ(e[i])||eZ(t[i]))&&(r[-1!==s.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=e[i]);return r}class tO extends tv{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tr}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(o.has(t)){let e=eq(t);return e&&e.default||0}return t=tD.has(t)?t:tC(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,i){return tV(e,t,i)}build(e,t,i){tj(e,t,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(e,t,i,r){for(let i in tk(e,t,void 0,r),t.attrs)e.setAttribute(tD.has(i)?i:tC(i),t.attrs[i])}mount(e){this.isSVGTag=t_(e.tagName),super.mount(e)}}let tF=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tz(e){if("string"!=typeof e||e.includes("-"));else if(tF.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var tI=i(95155);let tL=(0,n.createContext)({}),tN=(0,n.createContext)({strict:!1}),tB=(0,n.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),tU=(0,n.createContext)({});function t$(e){return Array.isArray(e)?e.join(" "):e}let tW=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tH(e,t,i){for(let r in t)eZ(t[r])||tP(r,i)||(e[r]=t[r])}let tG=()=>({...tW(),attrs:{}}),tq=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tK(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||tq.has(e)}let tX=e=>!tK(e);try{!function(e){"function"==typeof e&&(tX=t=>t.startsWith("on")?!tK(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let tY=(0,n.createContext)(null);function tZ(e){return eZ(e)?e.get():e}let tJ=e=>(t,i)=>{let r=(0,n.useContext)(tU),s=(0,n.useContext)(tY),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},i,r,n){return{latestValues:function(e,t,i,r){let n={},s=r(e,{});for(let e in s)n[e]=tZ(s[e]);let{initial:o,animate:a}=e,l=tc(e),u=tp(e);t&&u&&!l&&!1!==e.inherit&&(void 0===o&&(o=t.initial),void 0===a&&(a=t.animate));let d=!!i&&!1===i.initial,h=(d=d||!1===o)?a:o;if(h&&"boolean"!=typeof h&&!tl(h)){let t=Array.isArray(h)?h:[h];for(let i=0;i<t.length;i++){let r=tf(e,t[i]);if(r){let{transitionEnd:e,transition:t,...i}=r;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(n[e]=t)}for(let t in e)n[t]=e[t]}}}return n}(i,r,n,e),renderState:t()}})(e,t,r,s);return i?o():function(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}(o)},tQ=tJ({scrapeMotionValuesFromProps:tT,createRenderState:tW}),t0=tJ({scrapeMotionValuesFromProps:tV,createRenderState:tG}),t1=Symbol.for("motionComponentSymbol");function t2(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let t5="data-"+tC("framerAppearId"),t3=(0,n.createContext)({}),t4=tn?n.useLayoutEffect:n.useEffect;function t6(e){var t,i;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;s&&function(e){for(let t in e)e7[t]={...e7[t],...e[t]}}(s);let a=tz(e)?t0:tQ;function l(t,i){var s;let l,u={...(0,n.useContext)(tB),...t,layoutId:function(e){let{layoutId:t}=e,i=(0,n.useContext)(tL).id;return i&&void 0!==t?i+"-"+t:t}(t)},{isStatic:d}=u,h=function(e){let{initial:t,animate:i}=function(e,t){if(tc(e)){let{initial:t,animate:i}=e;return{initial:!1===t||tu(t)?t:void 0,animate:tu(i)?i:void 0}}return!1!==e.inherit?t:{}}(e,(0,n.useContext)(tU));return(0,n.useMemo)(()=>({initial:t,animate:i}),[t$(t),t$(i)])}(t),c=a(t,d);if(!d&&tn){(0,n.useContext)(tN).strict;let t=function(e){let{drag:t,layout:i}=e7;if(!t&&!i)return{};let r={...t,...i};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==i?void 0:i.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);l=t.MeasureLayout,h.visualElement=function(e,t,i,r,s){let{visualElement:o}=(0,n.useContext)(tU),a=(0,n.useContext)(tN),l=(0,n.useContext)(tY),u=(0,n.useContext)(tB).reducedMotion,d=(0,n.useRef)(null);r=r||a.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:o,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let h=d.current,c=(0,n.useContext)(t3);h&&!h.projection&&s&&("html"===h.type||"svg"===h.type)&&function(e,t,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:d}=t;e.projection=new i(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&t2(a),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:d,layoutScroll:l,layoutRoot:u})}(d.current,i,s,c);let p=(0,n.useRef)(!1);(0,n.useInsertionEffect)(()=>{h&&p.current&&h.update(i,l)});let m=i[t5],f=(0,n.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return t4(()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),h.scheduleRenderMicrotask(),f.current&&h.animationState&&h.animationState.animateChanges())}),(0,n.useEffect)(()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1),h.enteringChildren=void 0)}),h}(e,c,u,o,t.ProjectionNode)}return(0,tI.jsxs)(tU.Provider,{value:h,children:[l&&h.visualElement?(0,tI.jsx)(l,{visualElement:h.visualElement,...u}):null,function(e,t,i,{latestValues:r},s,o=!1){let a=(tz(e)?function(e,t,i,r){let s=(0,n.useMemo)(()=>{let i=tG();return tj(i,t,t_(r),e.transformTemplate,e.style),{...i.attrs,style:{...i.style}}},[t]);if(e.style){let t={};tH(t,e.style,e),s.style={...t,...s.style}}return s}:function(e,t){let i={},r=function(e,t){let i=e.style||{},r={};return tH(r,i,e),Object.assign(r,function({transformTemplate:e},t){return(0,n.useMemo)(()=>{let i=tW();return tS(i,t,e),Object.assign({},i.vars,i.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(i.tabIndex=0),i.style=r,i})(t,r,s,e),l=function(e,t,i){let r={};for(let n in e)("values"!==n||"object"!=typeof e.values)&&(tX(n)||!0===i&&tK(n)||!t&&!tK(n)||e.draggable&&n.startsWith("onDrag"))&&(r[n]=e[n]);return r}(t,"string"==typeof e,o),u=e!==n.Fragment?{...l,...a,ref:i}:{},{children:d}=t,h=(0,n.useMemo)(()=>eZ(d)?d.get():d,[d]);return(0,n.createElement)(e,{...u,children:h})}(e,t,(s=h.visualElement,(0,n.useCallback)(e=>{e&&c.onMount&&c.onMount(e),s&&(e?s.mount(e):s.unmount()),i&&("function"==typeof i?i(e):t2(i)&&(i.current=e))},[s])),c,d,r)]})}l.displayName="motion.".concat("string"==typeof e?e:"create(".concat(null!=(i=null!=(t=e.displayName)?t:e.name)?i:"",")"));let u=(0,n.forwardRef)(l);return u[t1]=e,u}function t9(e,t,i){let r=e.getProps();return tf(r,t,void 0!==i?i:r.custom,e)}function t8(e,t){return e?.[t]??e?.default??e}let t7=e=>Array.isArray(e);function ie(e,t){let i=e.getValue("willChange");if(eZ(i)&&i.add)return i.add(t);if(!i&&es.WillChange){let i=new es.WillChange("auto");e.addValue("willChange",i),i.add(t)}}function it(e){e.duration=0,e.type}let ii=(e,t)=>i=>t(e(i)),ir=(...e)=>e.reduce(ii),is=e=>1e3*e,io={layout:0,mainThread:0,waapi:0};function ia(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function il(e,t){return i=>i>0?t:e}let iu=(e,t,i)=>{let r=e*e,n=i*(t*t-r)+r;return n<0?0:Math.sqrt(n)},id=[eC,eM,eE];function ih(e){let t=id.find(t=>t.test(e));if(Y(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!t)return!1;let i=t.parse(e);return t===eE&&(i=function({hue:e,saturation:t,lightness:i,alpha:r}){e/=360,i/=100;let n=0,s=0,o=0;if(t/=100){let r=i<.5?i*(1+t):i+t-i*t,a=2*i-r;n=ia(a,r,e+1/3),s=ia(a,r,e),o=ia(a,r,e-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let ic=(e,t)=>{let i=ih(e),r=ih(t);if(!i||!r)return il(e,t);let n={...i};return e=>(n.red=iu(i.red,r.red,e),n.green=iu(i.green,r.green,e),n.blue=iu(i.blue,r.blue,e),n.alpha=k(i.alpha,r.alpha,e),eM.transform(n))},ip=new Set(["none","hidden"]);function im(e,t){return i=>k(e,t,i)}function ig(e){return"number"==typeof e?im:"string"==typeof e?x(e)?il:eR.test(e)?ic:ib:Array.isArray(e)?iy:"object"==typeof e?eR.test(e)?ic:iv:il}function iy(e,t){let i=[...e],r=i.length,n=e.map((e,i)=>ig(e)(e,t[i]));return e=>{for(let t=0;t<r;t++)i[t]=n[t](e);return i}}function iv(e,t){let i={...e,...t},r={};for(let n in i)void 0!==e[n]&&void 0!==t[n]&&(r[n]=ig(e[n])(e[n],t[n]));return e=>{for(let t in r)i[t]=r[t](e);return i}}let ib=(e,t)=>{let i=eL.createTransformer(t),r=eO(e),n=eO(t);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?ip.has(e)&&!n.values.length||ip.has(t)&&!r.values.length?function(e,t){return ip.has(e)?i=>i<=0?e:t:i=>i>=1?t:e}(e,t):ir(iy(function(e,t){let i=[],r={color:0,var:0,number:0};for(let n=0;n<t.values.length;n++){let s=t.types[n],o=e.indexes[s][r[s]],a=e.values[o]??0;i[n]=a,r[s]++}return i}(r,n),n.values),i):(Y(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),il(e,t))};function ix(e,t,i){return"number"==typeof e&&"number"==typeof t&&"number"==typeof i?k(e,t,i):ig(e)(e,t)}let iw=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>eu.update(t,e),stop:()=>ed(t),now:()=>eh.isProcessing?eh.timestamp:eQ.now()}},iS=(e,t,i=10)=>{let r="",n=Math.max(Math.round(t/i),2);for(let t=0;t<n;t++)r+=Math.round(1e4*e(t/(n-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function ik(e){let t=0,i=e.next(t);for(;!i.done&&t<2e4;)t+=50,i=e.next(t);return t>=2e4?1/0:t}function iA(e,t,i){var r,n;let s=Math.max(t-5,0);return r=i-e(s),(n=t-s)?1e3/n*r:0}let iP={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iT(e,t){return e*Math.sqrt(1-t*t)}let iM=["duration","bounce"],iC=["stiffness","damping","mass"];function iE(e,t){return t.some(t=>void 0!==e[t])}function iR(e=iP.visualDuration,t=iP.bounce){let i,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:n,restDelta:s}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:d,mass:h,duration:c,velocity:p,isResolvedFromDuration:m}=function(e){let t={velocity:iP.velocity,stiffness:iP.stiffness,damping:iP.damping,mass:iP.mass,isResolvedFromDuration:!1,...e};if(!iE(e,iC)&&iE(e,iM))if(e.visualDuration){let i=2*Math.PI/(1.2*e.visualDuration),r=i*i,n=2*F(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:iP.mass,stiffness:r,damping:n}}else{let i=function({duration:e=iP.duration,bounce:t=iP.bounce,velocity:i=iP.velocity,mass:r=iP.mass}){let n,s;Y(e<=is(iP.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-t;o=F(iP.minDamping,iP.maxDamping,o),e=F(iP.minDuration,iP.maxDuration,e/1e3),o<1?(n=t=>{let r=t*o,n=r*e;return .001-(r-i)/iT(t,o)*Math.exp(-n)},s=t=>{let r=t*o*e,s=Math.pow(o,2)*Math.pow(t,2)*e,a=Math.exp(-r),l=iT(Math.pow(t,2),o);return(r*i+i-s)*a*(-n(t)+.001>0?-1:1)/l}):(n=t=>-.001+Math.exp(-t*e)*((t-i)*e+1),s=t=>e*e*(i-t)*Math.exp(-t*e));let a=function(e,t,i){let r=i;for(let i=1;i<12;i++)r-=e(r)/t(r);return r}(n,s,5/e);if(e=is(e),isNaN(a))return{stiffness:iP.stiffness,damping:iP.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*o*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...i,mass:iP.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-((r.velocity||0)/1e3)}),f=p||0,g=d/(2*Math.sqrt(u*h)),y=a-o,v=Math.sqrt(u/h)/1e3,b=5>Math.abs(y);if(n||(n=b?iP.restSpeed.granular:iP.restSpeed.default),s||(s=b?iP.restDelta.granular:iP.restDelta.default),g<1){let e=iT(v,g);i=t=>a-Math.exp(-g*v*t)*((f+g*v*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)i=e=>a-Math.exp(-v*e)*(y+(f+v*y)*e);else{let e=v*Math.sqrt(g*g-1);i=t=>{let i=Math.exp(-g*v*t),r=Math.min(e*t,300);return a-i*((f+g*v*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}let x={calculatedDuration:m&&c||null,next:e=>{let t=i(e);if(m)l.done=e>=c;else{let r=0===e?f:0;g<1&&(r=0===e?is(f):iA(i,e,t));let o=Math.abs(a-t)<=s;l.done=Math.abs(r)<=n&&o}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(ik(x),2e4),t=iS(t=>x.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return x}function ij({keyframes:e,velocity:t=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:d}){let h,c,p=e[0],m={done:!1,value:p},f=i*t,g=p+f,y=void 0===o?g:o(g);y!==g&&(f=y-p);let v=e=>-f*Math.exp(-e/r),b=e=>y+v(e),x=e=>{let t=v(e),i=b(e);m.done=Math.abs(t)<=u,m.value=m.done?y:i},w=e=>{let t;if(t=m.value,void 0!==a&&t<a||void 0!==l&&t>l){var i;h=e,c=iR({keyframes:[m.value,(i=m.value,void 0===a?l:void 0===l||Math.abs(a-i)<Math.abs(l-i)?a:l)],velocity:iA(b,e,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:d})}};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(c||void 0!==h||(t=!0,x(e),w(e)),void 0!==h&&e>=h)?c.next(e-h):(t||x(e),m)}}}iR.applyToOptions=e=>{let t=function(e,t=100,i){let r=i({...e,keyframes:[0,t]}),n=Math.min(ik(r),2e4);return{type:"keyframes",ease:e=>r.next(n*e).value/t,duration:n/1e3}}(e,100,iR);return e.ease=t.ease,e.duration=is(t.duration),e.type="keyframes",e};let iD=(e,t,i)=>(((1-3*i+3*t)*e+(3*i-6*t))*e+3*t)*e;function i_(e,t,i,r){return e===t&&i===r?en:n=>0===n||1===n?n:iD(function(e,t,i,r,n){let s,o,a=0;do(s=iD(o=t+(i-t)/2,r,n)-e)>0?i=o:t=o;while(Math.abs(s)>1e-7&&++a<12);return o}(n,0,1,e,i),t,r)}let iV=i_(.42,0,1,1),iO=i_(0,0,.58,1),iF=i_(.42,0,.58,1),iz=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,iI=e=>t=>1-e(1-t),iL=i_(.33,1.53,.69,.99),iN=iI(iL),iB=iz(iN),iU=e=>(e*=2)<1?.5*iN(e):.5*(2-Math.pow(2,-10*(e-1))),i$=e=>1-Math.sin(Math.acos(e)),iW=iI(i$),iH=iz(i$),iG=e=>Array.isArray(e)&&"number"==typeof e[0],iq={linear:en,easeIn:iV,easeInOut:iF,easeOut:iO,circIn:i$,circInOut:iH,circOut:iW,backIn:iN,backInOut:iB,backOut:iL,anticipate:iU},iK=e=>{if(iG(e)){Z(4===e.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[t,i,r,n]=e;return i_(t,i,r,n)}return"string"==typeof e?(Z(void 0!==iq[e],`Invalid easing type '${e}'`,"invalid-easing-type"),iq[e]):e},iX=(e,t,i)=>{let r=t-e;return 0===r?1:(i-e)/r};function iY({duration:e=300,keyframes:t,times:i,ease:r="easeInOut"}){var n;let s=Array.isArray(r)&&"number"!=typeof r[0]?r.map(iK):iK(r),o={done:!1,value:t[0]},a=function(e,t,{clamp:i=!0,ease:r,mixer:n}={}){let s=e.length;if(Z(s===t.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let o=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,i){let r=[],n=i||es.mix||ix,s=e.length-1;for(let i=0;i<s;i++){let s=n(e[i],e[i+1]);t&&(s=ir(Array.isArray(t)?t[i]||en:t,s)),r.push(s)}return r}(t,r,n),l=a.length,u=i=>{if(o&&i<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(i<e[r+1]);r++);let n=iX(e[r],e[r+1],i);return a[r](n)};return i?t=>u(F(e[0],e[s-1],t)):u}((n=i&&i.length===t.length?i:function(e){let t=[0];return!function(e,t){let i=e[e.length-1];for(let r=1;r<=t;r++){let n=iX(0,t,r);e.push(k(i,1,n))}}(t,e.length-1),t}(t),n.map(t=>t*e)),t,{ease:Array.isArray(s)?s:t.map(()=>s||iF).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=a(t),o.done=t>=e,o)}}let iZ=e=>null!==e;function iJ(e,{repeat:t,repeatType:i="loop"},r,n=1){let s=e.filter(iZ),o=n<0||t&&"loop"!==i&&t%2==1?0:s.length-1;return o&&void 0!==r?r:s[o]}let iQ={decay:ij,inertia:ij,tween:iY,keyframes:iY,spring:iR};function i0(e){"string"==typeof e.type&&(e.type=iQ[e.type])}class i1{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}let i2=e=>e/100;class i5 extends i1{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:e}=this.options;e&&e.updatedAt!==eQ.now()&&this.tick(eQ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},io.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;i0(e);let{type:t=iY,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=e,{keyframes:o}=e,a=t||iY;a!==iY&&"number"!=typeof o[0]&&(this.mixKeyframes=ir(i2,ix(o[0],o[1])),o=[0,100]);let l=a({...e,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...e,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=ik(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:d,repeatType:h,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-r/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=i;if(d){let e=Math.min(this.currentTime,r)/o,t=Math.floor(e),i=e%1;!i&&e>=1&&(i=1),1===i&&t--,(t=Math.min(t,d+1))%2&&("reverse"===h?(i=1-i,c&&(i-=c/o)):"mirror"===h&&(b=s)),v=F(0,1,i)*o}let x=y?{done:!1,value:u[0]}:b.next(v);n&&(x.value=n(x.value));let{done:w}=x;y||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return S&&p!==ij&&(x.value=iJ(u,this.options,f,this.speed)),m&&m(x.value),S&&this.finish(),x}then(e,t){return this.finished.then(e,t)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(e){e=is(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(eQ.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:e=iw,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=t??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(eQ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,io.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}function i3(e){let t;return()=>(void 0===t&&(t=e()),t)}let i4=i3(()=>void 0!==window.ScrollTimeline),i6={},i9=function(e,t){let i=i3(e);return()=>i6[t]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),i8=([e,t,i,r])=>`cubic-bezier(${e}, ${t}, ${i}, ${r})`,i7={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i8([0,.65,.55,1]),circOut:i8([.55,0,1,.45]),backIn:i8([.31,.01,.66,-.59]),backOut:i8([.33,1.53,.69,.99])};function re(e){return"function"==typeof e&&"applyToOptions"in e}class rt extends i1{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=e;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=e,Z("string"!=typeof e.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:e,...t}){return re(e)&&i9()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let d={[t]:i};l&&(d.offset=l);let h=function e(t,i){if(t)return"function"==typeof t?i9()?iS(t,i):"ease-out":iG(t)?i8(t):Array.isArray(t)?t.map(t=>e(t,i)||i7.easeOut):i7[t]}(a,n);Array.isArray(h)&&(d.easing=h),ea.value&&io.waapi++;let c={delay:r,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(c.pseudoElement=u);let p=e.animate(d,c);return ea.value&&p.finished.finally(()=>{io.waapi--}),p}(t,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let e=iJ(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,i){t.startsWith("--")?e.style.setProperty(t,i):e.style[t]=i}(t,i,e),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(e){this.finishedTime=null,this.animation.currentTime=is(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&i4())?(this.animation.timeline=e,en):t(this)}}let ri={anticipate:iU,backInOut:iB,circInOut:iH};class rr extends rt{constructor(e){!function(e){"string"==typeof e.ease&&e.ease in ri&&(e.ease=ri[e.ease])}(e),i0(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!t)return;if(void 0!==e)return void t.set(e);let o=new i5({...s,autoplay:!1}),a=is(this.finishedTime??this.time);t.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let rn=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eL.test(e)||"0"===e)&&!e.startsWith("url(")),rs=new Set(["opacity","clipPath","filter","transform"]),ro=i3(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ra extends i1{constructor({autoplay:e=!0,delay:t=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=eQ.now();let h={autoplay:e,delay:t,type:i,repeat:r,repeatDelay:n,repeatType:s,name:a,motionValue:l,element:u,...d},c=u?.KeyframeResolver||eb;this.keyframeResolver=new c(o,(e,t,i)=>this.onKeyframesResolved(e,t,h,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=eQ.now(),!function(e,t,i,r){let n=e[0];if(null===n)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],o=rn(n,t),a=rn(s,t);return Y(o===a,`You are trying to animate ${t} from "${n}" to "${s}". "${o?s:n}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let i=0;i<e.length;i++)if(e[i]!==t)return!0}(e)||("spring"===i||re(i))&&r)}(e,n,s,o)&&((es.instantAnimations||!a)&&u?.(iJ(e,i,t)),e[0]=e[e.length-1],it(i),i.repeat=0);let d={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...i,keyframes:e},h=!l&&function(e){let{motionValue:t,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=e;if(!(t?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return ro()&&i&&rs.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}(d)?new rr({...d,element:d.motionValue.owner.current}):new i5(d);h.finished.then(()=>this.notifyFinished()).catch(en),this.pendingTimeline&&(this.stopTimeline=h.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=h}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eg=!0,ev(),ey(),eg=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let rl=e=>null!==e,ru={type:"spring",stiffness:500,damping:25,restSpeed:10},rd={type:"keyframes",duration:.8},rh={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rc=(e,t,i,r={},n,s)=>a=>{let l=t8(r,e)||{},u=l.delay||r.delay||0,{elapsed:d=0}=r;d-=is(u);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-d,onUpdate:e=>{t.set(e),l.onUpdate&&l.onUpdate(e)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:s?void 0:n};!function({when:e,delay:t,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...d}){return!!Object.keys(d).length}(l)&&Object.assign(h,((e,{keyframes:t})=>t.length>2?rd:o.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===t[1]?2*Math.sqrt(550):30,restSpeed:10}:ru:rh)(e,h)),h.duration&&(h.duration=is(h.duration)),h.repeatDelay&&(h.repeatDelay=is(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(it(h),0===h.delay&&(c=!0)),(es.instantAnimations||es.skipAnimations)&&(c=!0,it(h),h.delay=0),h.allowFlatten=!l.type&&!l.ease,c&&!s&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:i="loop"},r){let n=e.filter(rl),s=t&&"loop"!==i&&t%2==1?0:n.length-1;return n[s]}(h.keyframes,l);if(void 0!==e)return void eu.update(()=>{h.onUpdate(e),h.onComplete()})}return l.isSync?new i5(h):new ra(h)};function rp(e,t,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:o,...a}=t;r&&(s=r);let l=[],u=n&&e.animationState&&e.animationState.getState()[n];for(let t in a){let r=e.getValue(t,e.latestValues[t]??null),n=a[t];if(void 0===n||u&&function({protectedKeys:e,needsAnimating:t},i){let r=e.hasOwnProperty(i)&&!0!==t[i];return t[i]=!1,r}(u,t))continue;let o={delay:i,...t8(s||{},t)},d=r.get();if(void 0!==d&&!r.isAnimating&&!Array.isArray(n)&&n===d&&!o.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){let i=e.props[t5];if(i){let e=window.MotionHandoffAnimation(i,t,eu);null!==e&&(o.startTime=e,h=!0)}}ie(e,t),r.start(rc(t,r,n,e.shouldReduceMotion&&O.has(t)?{type:!1}:o,e,h));let c=r.animation;c&&l.push(c)}return o&&Promise.all(l).then(()=>{eu.update(()=>{o&&function(e,t){let{transitionEnd:i={},transition:r={},...n}=t9(e,t)||{};for(let t in n={...n,...i}){var s;let i=t7(s=n[t])?s[s.length-1]||0:s;e.hasValue(t)?e.getValue(t).set(i):e.addValue(t,e4(i))}}(e,o)})}),l}function rm(e,t,i,r=0,n=1){let s=Array.from(e).sort((e,t)=>e.sortNodePosition(t)).indexOf(t),o=e.size,a=(o-1)*r;return"function"==typeof i?i(s,o):1===n?s*r:a-s*r}function rf(e,t,i={}){let r=t9(e,t,"exit"===i.type?e.presenceContext?.custom:void 0),{transition:n=e.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(rp(e,r,i)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(e,t,i=0,r=0,n=0,s=1,o){let a=[];for(let l of e.variantChildren)l.notify("AnimationStart",t),a.push(rf(l,t,{...o,delay:i+("function"==typeof r?0:r)+rm(e.variantChildren,l,r,n,s)}).then(()=>l.notify("AnimationComplete",t)));return Promise.all(a)}(e,t,r,s,o,a,i)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([s(),o(i.delay)]);{let[e,t]="beforeChildren"===a?[s,o]:[o,s];return e().then(()=>t())}}function rg(e,t){if(!Array.isArray(t))return!1;let i=t.length;if(i!==e.length)return!1;for(let r=0;r<i;r++)if(t[r]!==e[r])return!1;return!0}let ry=th.length,rv=[...td].reverse(),rb=td.length;function rx(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rw(){return{animate:rx(!0),whileInView:rx(),whileHover:rx(),whileTap:rx(),whileDrag:rx(),whileFocus:rx(),exit:rx()}}class rS{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rk extends rS{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:i})=>(function(e,t,i={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>rf(e,t,i)));else if("string"==typeof t)r=rf(e,t,i);else{let n="function"==typeof t?t9(e,t,i.custom):t;r=Promise.all(rp(e,n,i))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,i))),i=rw(),r=!0,n=t=>(i,r)=>{let n=t9(e,r,"exit"===t?e.presenceContext?.custom:void 0);if(n){let{transition:e,transitionEnd:t,...r}=n;i={...i,...r,...t}}return i};function s(s){let{props:o}=e,a=function e(t){if(!t)return;if(!t.isControllingVariants){let i=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(i.initial=t.props.initial),i}let i={};for(let e=0;e<ry;e++){let r=th[e],n=t.props[r];(tu(n)||!1===n)&&(i[r]=n)}return i}(e.parent)||{},l=[],u=new Set,d={},h=1/0;for(let t=0;t<rb;t++){var c,p;let m=rv[t],f=i[m],g=void 0!==o[m]?o[m]:a[m],y=tu(g),v=m===s?f.isActive:null;!1===v&&(h=t);let b=g===a[m]&&g!==o[m]&&y;if(b&&r&&e.manuallyAnimateOnMount&&(b=!1),f.protectedKeys={...d},!f.isActive&&null===v||!g&&!f.prevProp||tl(g)||"boolean"==typeof g)continue;let x=(c=f.prevProp,"string"==typeof(p=g)?p!==c:!!Array.isArray(p)&&!rg(p,c)),w=x||m===s&&f.isActive&&!b&&y||t>h&&y,S=!1,k=Array.isArray(g)?g:[g],A=k.reduce(n(m),{});!1===v&&(A={});let{prevResolvedValues:P={}}=f,T={...P,...A},M=t=>{w=!0,u.has(t)&&(S=!0,u.delete(t)),f.needsAnimating[t]=!0;let i=e.getValue(t);i&&(i.liveStyle=!1)};for(let e in T){let t=A[e],i=P[e];if(!d.hasOwnProperty(e))(t7(t)&&t7(i)?rg(t,i):t===i)?void 0!==t&&u.has(e)?M(e):f.protectedKeys[e]=!0:null!=t?M(e):u.add(e)}f.prevProp=g,f.prevResolvedValues=A,f.isActive&&(d={...d,...A}),r&&e.blockInitialAnimation&&(w=!1);let C=b&&x,E=!C||S;w&&E&&l.push(...k.map(t=>{let i={type:m};if("string"==typeof t&&r&&!C&&e.manuallyAnimateOnMount&&e.parent){let{parent:r}=e,n=t9(r,t);if(r.enteringChildren&&n){let{delayChildren:t}=n.transition||{};i.delay=rm(r.enteringChildren,e,t)}}return{animation:t,options:i}}))}if(u.size){let t={};if("boolean"!=typeof o.initial){let i=t9(e,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(t.transition=i.transition)}u.forEach(i=>{let r=e.getBaseTarget(i),n=e.getValue(i);n&&(n.liveStyle=!0),t[i]=r??null}),l.push({animation:t})}let m=!!l.length;return r&&(!1===o.initial||o.initial===o.animate)&&!e.manuallyAnimateOnMount&&(m=!1),r=!1,m?t(l):Promise.resolve()}return{animateChanges:s,setActive:function(t,r){if(i[t].isActive===r)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,r)),i[t].isActive=r;let n=s(t);for(let e in i)i[e].protectedKeys={};return n},setAnimateFunction:function(i){t=i(e)},getState:()=>i,reset:()=>{i=rw(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();tl(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rA=0;class rP extends rS{constructor(){super(...arguments),this.id=rA++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let rT={x:!1,y:!1};function rM(e,t,i,r={passive:!0}){return e.addEventListener(t,i,r),()=>e.removeEventListener(t,i)}let rC=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rE(e){return{point:{x:e.pageX,y:e.pageY}}}function rR(e,t,i,r){return rM(e,t,e=>rC(e)&&i(e,rE(e)),r)}function rj(e){return e.max-e.min}function rD(e,t,i,r=.5){e.origin=r,e.originPoint=k(t.min,t.max,e.origin),e.scale=rj(i)/rj(t),e.translate=k(i.min,i.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function r_(e,t,i,r){rD(e.x,t.x,i.x,r?r.originX:void 0),rD(e.y,t.y,i.y,r?r.originY:void 0)}function rV(e,t,i){e.min=i.min+t.min,e.max=e.min+rj(t)}function rO(e,t,i){e.min=t.min-i.min,e.max=e.min+rj(t)}function rF(e,t,i){rO(e.x,t.x,i.x),rO(e.y,t.y,i.y)}function rz(e){return[e("x"),e("y")]}let rI=({current:e})=>e?e.ownerDocument.defaultView:null,rL=(e,t)=>Math.abs(e-t);class rN{constructor(e,t,{transformPagePoint:i,contextWindow:r=window,dragSnapToOrigin:n=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=r$(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,i=function(e,t){return Math.sqrt(rL(e.x,t.x)**2+rL(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=this.distanceThreshold;if(!t&&!i)return;let{point:r}=e,{timestamp:n}=eh;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=rB(t,this.transformPagePoint),eu.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=r$("pointercancel"===e.type?this.lastMoveEventInfo:rB(t,this.transformPagePoint),this.history);this.startEvent&&i&&i(e,s),r&&r(e,s)},!rC(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=r||window;let o=rB(rE(e),this.transformPagePoint),{point:a}=o,{timestamp:l}=eh;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=t;u&&u(e,r$(o,this.history)),this.removeListeners=ir(rR(this.contextWindow,"pointermove",this.handlePointerMove),rR(this.contextWindow,"pointerup",this.handlePointerUp),rR(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),ed(this.updatePoint)}}function rB(e,t){return t?{point:t(e.point)}:e}function rU(e,t){return{x:e.x-t.x,y:e.y-t.y}}function r$({point:e},t){return{point:e,delta:rU(e,rW(t)),offset:rU(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let i=e.length-1,r=null,n=rW(e);for(;i>=0&&(r=e[i],!(n.timestamp-r.timestamp>is(.1)));)i--;if(!r)return{x:0,y:0};let s=(n.timestamp-r.timestamp)/1e3;if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(t,.1)}}function rW(e){return e[e.length-1]}function rH(e,t,i){return{min:void 0!==t?e.min+t:void 0,max:void 0!==i?e.max+i-(e.max-e.min):void 0}}function rG(e,t){let i=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([i,r]=[r,i]),{min:i,max:r}}function rq(e,t,i){return{min:rK(e,t),max:rK(e,i)}}function rK(e,t){return"number"==typeof e?e:e[t]||0}let rX=new WeakMap;class rY{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=tr(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:t=!1,distanceThreshold:i}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let n=e=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rE(e).point)},s=(e,t)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(rT[e])return null;else return rT[e]=!0,()=>{rT[e]=!1};return rT.x||rT.y?null:(rT.x=rT.y=!0,()=>{rT.x=rT.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=e,this.latestPanInfo=t,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rz(e=>{let t=this.getAxisMotionValue(e).get()||0;if(U.test(t)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[e];r&&(t=rj(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),n&&eu.postRender(()=>n(e,t)),ie(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},o=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t;let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let i=null;return Math.abs(e.y)>t?i="y":Math.abs(e.x)>t&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),s&&s(e,t)},a=(e,t)=>{this.latestPointerEvent=e,this.latestPanInfo=t,this.stop(e,t),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>rz(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play()),{dragSnapToOrigin:u}=this.getProps();this.panSession=new rN(e,{onSessionStart:n,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:i,contextWindow:rI(this.visualElement)})}stop(e,t){let i=e||this.latestPointerEvent,r=t||this.latestPanInfo,n=this.isDragging;if(this.cancel(),!n||!r||!i)return;let{velocity:s}=r;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&eu.postRender(()=>o(i,r))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,i){let{drag:r}=this.getProps();if(!i||!rZ(e,r,this.currentDirection))return;let n=this.getAxisMotionValue(e),s=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:i},r){return void 0!==t&&e<t?e=r?k(t,e,r.min):Math.max(e,t):void 0!==i&&e>i&&(e=r?k(i,e,r.max):Math.min(e,i)),e}(s,this.constraints[e],this.elastic[e])),n.set(s)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;e&&t2(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(e,{top:t,left:i,bottom:r,right:n}){return{x:rH(e.x,i,n),y:rH(e.y,t,r)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:rq(e,"left","right"),y:rq(e,"top","bottom")}}(t),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&rz(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let i={};return void 0!==t.min&&(i.min=t.min-e.min),void 0!==t.max&&(i.max=t.max-e.min),i}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:i}=this.getProps();if(!t||!t2(t))return!1;let r=t.current;Z(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(e,t,i){let r=V(e,i),{scroll:n}=t;return n&&(j(r.x,n.offset.x),j(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o=(e=n.layout.layoutBox,{x:rG(e.x,s.x),y:rG(e.y,s.y)});if(i){let e=i(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=S(e))}return o}startAnimation(e){let{drag:t,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(rz(o=>{if(!rZ(o,t,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?e[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(e,t){let i=this.getAxisMotionValue(e);return ie(this.visualElement,e),i.start(rc(e,i,0,t,this.visualElement,!1))}stopAnimation(){rz(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){rz(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps();return i[t]||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){rz(t=>{let{drag:i}=this.getProps();if(!rZ(t,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(t);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[t];n.set(e[t]-k(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:i}=this.visualElement;if(!t2(t)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rz(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let i=t.get();r[e]=function(e,t){let i=.5,r=rj(e),n=rj(t);return n>r?i=iX(t.min,t.max-r,e.min):r>n&&(i=iX(e.min,e.max-n,t.min)),F(0,1,i)}({min:i,max:i},this.constraints[e])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),rz(t=>{if(!rZ(t,e,null))return;let i=this.getAxisMotionValue(t),{min:n,max:s}=this.constraints[t];i.set(k(n,s,r[t]))})}addListeners(){if(!this.visualElement.current)return;rX.set(this.visualElement,this);let e=rR(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:i=!0}=this.getProps();t&&i&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();t2(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",t);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),eu.read(t);let n=rM(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(rz(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{n(),e(),r(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=e;return{...e,drag:t,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function rZ(e,t,i){return(!0===t||t===e)&&(null===i||i===e)}class rJ extends rS{constructor(e){super(e),this.removeGroupControls=en,this.removeListeners=en,this.controls=new rY(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||en}unmount(){this.removeGroupControls(),this.removeListeners()}}let rQ=e=>(t,i)=>{e&&eu.postRender(()=>e(t,i))};class r0 extends rS{constructor(){super(...arguments),this.removePointerDownListener=en}onPointerDown(e){this.session=new rN(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rI(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rQ(e),onStart:rQ(t),onMove:i,onEnd:(e,t)=>{delete this.session,r&&eu.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=rR(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let r1={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function r2(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let r5={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!$.test(e))return e;else e=parseFloat(e);let i=r2(e,t.target.x),r=r2(e,t.target.y);return`${i}% ${r}%`}},r3=!1;class r4 extends n.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=e;for(let e in r9)tA[e]=r9[e],v(e)&&(tA[e].isCSSVariable=!0);n&&(t.group&&t.group.add(n),i&&i.register&&r&&i.register(n),r3&&n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),r1.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r3=!0,r||e.layoutDependency!==t||void 0===t||e.isPresent!==n?s.willUpdate():this.safeToRemove(),e.isPresent!==n&&(n?s.promote():s.relegate()||eu.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),e9.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:i}=this.props,{projection:r}=e;r3=!0,r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function r6(e){let[t,i]=function(e=!0){let t=(0,n.useContext)(tY);if(null===t)return[!0,null];let{isPresent:i,onExitComplete:r,register:s}=t,o=(0,n.useId)();(0,n.useEffect)(()=>{if(e)return s(o)},[e]);let a=(0,n.useCallback)(()=>e&&r&&r(o),[o,r,e]);return!i&&r?[!1,a]:[!0]}(),r=(0,n.useContext)(tL);return(0,tI.jsx)(r4,{...e,layoutGroup:r,switchLayoutGroup:(0,n.useContext)(t3),isPresent:t,safeToRemove:i})}let r9={borderRadius:{...r5,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r5,borderTopRightRadius:r5,borderBottomLeftRadius:r5,borderBottomRightRadius:r5,boxShadow:{correct:(e,{treeScale:t,projectionDelta:i})=>{let r=eL.parse(e);if(r.length>5)return e;let n=eL.createTransformer(e),s=+("number"!=typeof r[0]),o=i.x.scale*t.x,a=i.y.scale*t.y;r[0+s]/=o,r[1+s]/=a;let l=k(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};function r8(e){return"object"==typeof e&&null!==e}function r7(e){return r8(e)&&"ownerSVGElement"in e}let ne=(e,t)=>e.depth-t.depth;class nt{constructor(){this.children=[],this.isDirty=!1}add(e){e0(this.children,e),this.isDirty=!0}remove(e){e1(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(ne),this.isDirty=!1,this.children.forEach(e)}}let ni=["TopLeft","TopRight","BottomLeft","BottomRight"],nr=ni.length,nn=e=>"string"==typeof e?parseFloat(e):e,ns=e=>"number"==typeof e||$.test(e);function no(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let na=nu(0,.5,iW),nl=nu(.5,.95,en);function nu(e,t,i){return r=>r<e?0:r>t?1:i(iX(e,t,r))}function nd(e,t){e.min=t.min,e.max=t.max}function nh(e,t){nd(e.x,t.x),nd(e.y,t.y)}function nc(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function np(e,t,i,r,n){return e-=t,e=r+1/i*(e-r),void 0!==n&&(e=r+1/n*(e-r)),e}function nm(e,t,[i,r,n],s,o){!function(e,t=0,i=1,r=.5,n,s=e,o=e){if(U.test(t)&&(t=parseFloat(t),t=k(o.min,o.max,t/100)-o.min),"number"!=typeof t)return;let a=k(s.min,s.max,r);e===s&&(a-=t),e.min=np(e.min,t,i,a,n),e.max=np(e.max,t,i,a,n)}(e,t[i],t[r],t[n],t.scale,s,o)}let nf=["x","scaleX","originX"],ng=["y","scaleY","originY"];function ny(e,t,i,r){nm(e.x,t,nf,i?i.x:void 0,r?r.x:void 0),nm(e.y,t,ng,i?i.y:void 0,r?r.y:void 0)}function nv(e){return 0===e.translate&&1===e.scale}function nb(e){return nv(e.x)&&nv(e.y)}function nx(e,t){return e.min===t.min&&e.max===t.max}function nw(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nS(e,t){return nw(e.x,t.x)&&nw(e.y,t.y)}function nk(e){return rj(e.x)/rj(e.y)}function nA(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nP{constructor(){this.members=[]}add(e){e0(this.members,e),e.scheduleRender()}remove(e){if(e1(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,i=this.members.findIndex(t=>e===t);if(0===i)return!1;for(let e=i;e>=0;e--){let i=this.members[e];if(!1!==i.isPresent){t=i;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,t&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:i}=e;t.onExitComplete&&t.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nT={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nM=["","X","Y","Z"],nC=0;function nE(e,t,i,r){let{latestValues:n}=t;n[e]&&(i[e]=n[e],t.setStaticValue(e,0),r&&(r[e]=0))}function nR({attachResizeListener:e,defaultParent:t,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(e={},i=t?.()){this.id=nC++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ea.value&&(nT.nodes=nT.calculatedTargetDeltas=nT.calculatedProjections=0),this.nodes.forEach(n_),this.nodes.forEach(nN),this.nodes.forEach(nB),this.nodes.forEach(nV),ea.addProjectionMetrics&&ea.addProjectionMetrics(nT)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nt)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new e2),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let i=this.eventHandlers.get(e);i&&i.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=r7(t)&&!(r7(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),e){let i,r=0,n=()=>this.root.updateBlockedByResize=!1;eu.read(()=>{r=window.innerWidth}),e(t,()=>{let e=window.innerWidth;e!==r&&(r=e,this.root.updateBlockedByResize=!0,i&&i(),i=function(e,t){let i=eQ.now(),r=({timestamp:t})=>{let n=t-i;n>=250&&(ed(r),e(n-250))};return eu.setup(r,!0),()=>ed(r)}(n,250),r1.hasAnimatedSinceResize&&(r1.hasAnimatedSinceResize=!1,this.nodes.forEach(nL)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||nq,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!nS(this.targetLayout,r),u=!t&&i;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...t8(s,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,u)}else t||nL(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),ed(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nU),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:i}=t.options;if(!i)return;let r=i.props[t5];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",eu,!(e||i))}let{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&e(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:i}=this.options;if(void 0===t&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nF);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nz);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nI),this.nodes.forEach(nj),this.nodes.forEach(nD)):this.nodes.forEach(nz),this.clearAllSnapshots();let e=eQ.now();eh.delta=F(0,1e3/60,e-eh.timestamp),eh.timestamp=e,eh.isProcessing=!0,ec.update.process(eh),ec.preRender.process(eh),ec.render.process(eh),eh.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,e9.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nO),this.sharedNodes.forEach(n$)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,eu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){eu.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rj(this.snapshot.measuredBox.x)||rj(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=tr(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!n)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!nb(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;e&&this.instance&&(t||T(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let i=this.measurePageBox(),r=this.removeElementScroll(i);return e&&(r=this.removeTransform(r)),nY((t=r).x),nY(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return tr();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nJ))){let{scroll:e}=this.root;e&&(j(t.x,e.offset.x),j(t.y,e.offset.y))}return t}removeElementScroll(e){let t=tr();if(nh(t,e),this.scroll?.wasRoot)return t;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&nh(t,e),j(t.x,n.offset.x),j(t.y,n.offset.y))}return t}applyTransform(e,t=!1){let i=tr();nh(i,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&_(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),T(r.latestValues)&&_(i,r.latestValues)}return T(this.latestValues)&&_(i,this.latestValues),i}removeTransform(e){let t=tr();nh(t,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];if(!i.instance||!T(i.latestValues))continue;P(i.latestValues)&&i.updateSnapshot();let r=tr();nh(r,i.measurePageBox()),ny(t,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return T(this.latestValues)&&ny(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==eh.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==t;if(!(e||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=eh.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tr(),this.relativeTargetOrigin=tr(),rF(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nh(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=tr(),this.targetWithTransforms=tr()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,rV(s.x,o.x,a.x),rV(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nh(this.target,this.layout.layoutBox),R(this.target,this.targetDelta)):nh(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tr(),this.relativeTargetOrigin=tr(),rF(this.relativeTargetOrigin,this.target,e.target),nh(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ea.value&&nT.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||P(this.parent.latestValues)||M(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===eh.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;nh(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(e,t,i,r=!1){let n,s,o=i.length;if(o){t.x=t.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&_(e,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,R(e,s)),r&&T(n.latestValues)&&_(e,n.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=tr());let{target:a}=e;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nc(this.prevProjectionDelta.x,this.projectionDelta.x),nc(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),r_(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nA(this.projectionDelta.x,this.prevProjectionDelta.x)&&nA(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ea.value&&nT.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tt(),this.projectionDelta=tt(),this.projectionDeltaWithTransform=tt()}setAnimationOrigin(e,t=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=tt();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=tr(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),d=!u||u.members.length<=1,h=!!(l&&!d&&!0===this.options.crossfade&&!this.path.some(nG));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(nW(o.x,e.x,r),nW(o.y,e.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,g;rF(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=r,nH(p.x,m.x,f.x,g),nH(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,c=i,nx(u.x,c.x)&&nx(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=tr()),nh(i,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,i,r,n,s){n?(e.opacity=k(0,i.opacity??1,na(r)),e.opacityExit=k(t.opacity??1,0,nl(r))):s&&(e.opacity=k(t.opacity??1,i.opacity??1,r));for(let n=0;n<nr;n++){let s=`border${ni[n]}Radius`,o=no(t,s),a=no(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||ns(o)===ns(a)?(e[s]=Math.max(k(nn(o),nn(a),r),0),(U.test(a)||U.test(o))&&(e[s]+="%")):e[s]=a)}(t.rotate||i.rotate)&&(e.rotate=k(t.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,h,d)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(ed(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=eu.update(()=>{r1.hasAnimatedSinceResize=!0,io.layout++,this.motionValue||(this.motionValue=e4(0)),this.currentAnimation=function(e,t,i){let r=eZ(e)?e:e4(e);return r.start(rc("",r,t,i)),r.animation}(this.motionValue,[0,1e3],{...e,velocity:0,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{io.layout--},onComplete:()=>{io.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:i,layout:r,latestValues:n}=e;if(t&&i&&r){if(this!==e&&this.layout&&r&&nZ(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||tr();let t=rj(this.layout.layoutBox.x);i.x.min=e.target.x.min,i.x.max=i.x.min+t;let r=rj(this.layout.layoutBox.y);i.y.min=e.target.y.min,i.y.max=i.y.min+r}nh(t,i),_(t,n),r_(this.projectionDeltaWithTransform,this.layoutCorrected,t,n)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nP),this.sharedNodes.get(e).add(t);let i=t.options.initialPromotionConfig;t.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:i}=e;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(t=!0),!t)return;let r={};i.z&&nE("z",e,r,this.animationValues);for(let t=0;t<nM.length;t++)nE(`rotate${nM[t]}`,e,r,this.animationValues),nE(`skew${nM[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}applyProjectionStyles(e,t){if(!this.instance||this.isSVG)return;if(!this.isVisible){e.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,e.visibility="",e.opacity="",e.pointerEvents=tZ(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tZ(t?.pointerEvents)||""),this.hasProjected&&!T(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1);return}e.visibility="";let n=r.animationValues||r.latestValues;this.applyTransformsToTarget();let s=function(e,t,i){let r="",n=e.x.translate/t.x,s=e.y.translate/t.y,o=i?.z||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),i){let{transformPerspective:e,rotate:t,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n);i&&(s=i(n,s)),e.transform=s;let{x:o,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,r.animationValues?e.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,tA){if(void 0===n[t])continue;let{correct:i,applyTo:o,isCSSVariable:a}=tA[t],l="none"===s?n[t]:i(n[t],r);if(o){let t=o.length;for(let i=0;i<t;i++)e[o[i]]=l}else a?this.options.visualElement.renderState.vars[t]=l:e[t]=l}this.options.layoutId&&(e.pointerEvents=r===this?tZ(t?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop()),this.root.nodes.forEach(nF),this.root.sharedNodes.clear()}}}function nj(e){e.updateLayout()}function nD(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=e.layout,{animationType:n}=e.options,s=t.source!==e.layout.source;"size"===n?rz(e=>{let r=s?t.measuredBox[e]:t.layoutBox[e],n=rj(r);r.min=i[e].min,r.max=r.min+n}):nZ(n,t.layoutBox,i)&&rz(r=>{let n=s?t.measuredBox[r]:t.layoutBox[r],o=rj(i[r]);n.max=n.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});let o=tt();r_(o,i,t.layoutBox);let a=tt();s?r_(a,e.applyTransform(r,!0),t.measuredBox):r_(a,i,t.layoutBox);let l=!nb(o),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=tr();rF(o,t.layoutBox,n.layoutBox);let a=tr();rF(a,i,s.layoutBox),nS(o,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:i,snapshot:t,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function n_(e){ea.value&&nT.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function nV(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function nO(e){e.clearSnapshot()}function nF(e){e.clearMeasurements()}function nz(e){e.isLayoutDirty=!1}function nI(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function nL(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function nN(e){e.resolveTargetDelta()}function nB(e){e.calcProjection()}function nU(e){e.resetSkewAndRotation()}function n$(e){e.removeLeadSnapshot()}function nW(e,t,i){e.translate=k(t.translate,0,i),e.scale=k(t.scale,1,i),e.origin=t.origin,e.originPoint=t.originPoint}function nH(e,t,i,r){e.min=k(t.min,i.min,r),e.max=k(t.max,i.max,r)}function nG(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let nq={duration:.45,ease:[.4,0,.1,1]},nK=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),nX=nK("applewebkit/")&&!nK("chrome/")?Math.round:en;function nY(e){e.min=nX(e.min),e.max=nX(e.max)}function nZ(e,t,i){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nk(t)-nk(i)))}function nJ(e){return e!==e.root&&e.scroll?.wasRoot}let nQ=nR({attachResizeListener:(e,t)=>rM(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),n0={current:void 0},n1=nR({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!n0.current){let e=new nQ({});e.mount(window),e.setOptions({layoutScroll:!0}),n0.current=e}return n0.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});var n2=i(42198);function n5(e,t){let i=(0,n2.K)(e),r=new AbortController;return[i,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function n3(e){return!("touch"===e.pointerType||rT.x||rT.y)}function n4(e,t,i){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&eu.postRender(()=>n(t,rE(t)))}class n6 extends rS{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[r,n,s]=n5(e,i),o=e=>{if(!n3(e))return;let{target:i}=e,r=t(i,e);if("function"!=typeof r||!i)return;let s=e=>{n3(e)&&(r(e),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(e=>{e.addEventListener("pointerenter",o,n)}),s}(e,(e,t)=>(n4(this.node,t,"Start"),e=>n4(this.node,e,"End"))))}unmount(){}}class n9 extends rS{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ir(rM(this.node.current,"focus",()=>this.onFocus()),rM(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let n8=(e,t)=>!!t&&(e===t||n8(e,t.parentElement)),n7=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),se=new WeakSet;function st(e){return t=>{"Enter"===t.key&&e(t)}}function si(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function sr(e){return rC(e)&&!(rT.x||rT.y)}function sn(e,t,i){let{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&eu.postRender(()=>n(t,rE(t)))}class ss extends rS{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,i={}){let[r,n,s]=n5(e,i),o=e=>{let r=e.currentTarget;if(!sr(e))return;se.add(r);let s=t(r,e),o=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),se.has(r)&&se.delete(r),sr(e)&&"function"==typeof s&&s(e,{success:t})},a=e=>{o(e,r===window||r===document||i.useGlobalTarget||n8(r,e.target))},l=e=>{o(e,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return r.forEach(e=>{(i.useGlobalTarget?window:e).addEventListener("pointerdown",o,n),r8(e)&&"offsetHeight"in e&&(e.addEventListener("focus",e=>((e,t)=>{let i=e.currentTarget;if(!i)return;let r=st(()=>{if(se.has(i))return;si(i,"down");let e=st(()=>{si(i,"up")});i.addEventListener("keyup",e,t),i.addEventListener("blur",()=>si(i,"cancel"),t)});i.addEventListener("keydown",r,t),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),t)})(e,n)),n7.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),s}(e,(e,t)=>(sn(this.node,t,"Start"),(e,{success:t})=>sn(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let so=new WeakMap,sa=new WeakMap,sl=e=>{let t=so.get(e.target);t&&t(e)},su=e=>{e.forEach(sl)},sd={some:0,all:1};class sh extends rS{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:i,amount:r="some",once:n}=e,s={root:t?t.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:sd[r]},o=e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,n&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=t?i:r;s&&s(e)};var a=this.node.current;let l=function({root:e,...t}){let i=e||document;sa.has(i)||sa.set(i,{});let r=sa.get(i),n=JSON.stringify(t);return r[n]||(r[n]=new IntersectionObserver(su,{root:e,...t})),r[n]}(s);return so.set(a,o),l.observe(a),()=>{so.delete(a),l.unobserve(a)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return i=>e[i]!==t[i]}(e,t))&&this.startObserver()}unmount(){}}let sc=function(e,t){if("undefined"==typeof Proxy)return t6;let i=new Map,r=(i,r)=>t6(i,r,e,t);return new Proxy((e,t)=>r(e,t),{get:(n,s)=>"create"===s?r:(i.has(s)||i.set(s,t6(s,void 0,e,t)),i.get(s))})}({animation:{Feature:rk},exit:{Feature:rP},inView:{Feature:sh},tap:{Feature:ss},focus:{Feature:n9},hover:{Feature:n6},pan:{Feature:r0},drag:{Feature:rJ,ProjectionNode:n1,MeasureLayout:r6},layout:{ProjectionNode:n1,MeasureLayout:r6}},(e,t)=>tz(e)?new tO(t):new tM(t,{allowProjection:e!==n.Fragment}))},45503:(e,t,i)=>{"use strict";i.d(t,{Z:()=>n});var r=i(12115);function n(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},46081:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var r=i(12115),n=i(95155);function s(e,t=[]){let i=[],o=()=>{let t=i.map(e=>r.createContext(e));return function(i){let n=i?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...i,[e]:n}}),[i,n])}};return o.scopeName=e,[function(t,s){let o=r.createContext(s),a=i.length;i=[...i,s];let l=t=>{let{scope:i,children:s,...l}=t,u=i?.[e]?.[a]||o,d=r.useMemo(()=>l,Object.values(l));return(0,n.jsx)(u.Provider,{value:d,children:s})};return l.displayName=t+"Provider",[l,function(i,n){let l=n?.[e]?.[a]||o,u=r.useContext(l);if(u)return u;if(void 0!==s)return s;throw Error(`\`${i}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let i=()=>{let i=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=i.reduce((t,{useScope:i,scopeName:r})=>{let n=i(e)[`__scope${r}`];return{...t,...n}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return i.scopeName=t.scopeName,i}(o,...t)]}},51193:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:r,width:n,quality:s}=e,o=s||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+o+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),i.__next_img_default=!0;let r=i},52596:(e,t,i)=>{"use strict";function r(){for(var e,t,i=0,r="",n=arguments.length;i<n;i++)(e=arguments[i])&&(t=function e(t){var i,r,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(i=0;i<s;i++)t[i]&&(r=e(t[i]))&&(n&&(n+=" "),n+=r)}else for(r in t)t[r]&&(n&&(n+=" "),n+=r);return n}(e))&&(r&&(r+=" "),r+=t);return r}i.d(t,{$:()=>r})},52712:(e,t,i)=>{"use strict";i.d(t,{N:()=>n});var r=i(12115),n=globalThis?.document?r.useLayoutEffect:()=>{}},53311:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]])},54416:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59099:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},61724:(e,t,i)=>{"use strict";i.d(t,{UC:()=>Q,B8:()=>Z,bL:()=>Y,l9:()=>J});var r=i(12115),n=i.t(r,2),s=i(85185),o=i(46081),a=i(37328),l=i(6101),u=i(52712),d=n[" useId ".trim().toString()]||(()=>void 0),h=0;function c(e){let[t,i]=r.useState(d());return(0,u.N)(()=>{e||i(e=>e??String(h++))},[e]),e||(t?`radix-${t}`:"")}var p=i(63655),m=i(5845),f=i(94315),g=i(95155),y="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[x,w,S]=(0,a.N)(b),[k,A]=(0,o.A)(b,[S]),[P,T]=k(b),M=r.forwardRef((e,t)=>(0,g.jsx)(x.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,g.jsx)(x.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,g.jsx)(C,{...e,ref:t})})}));M.displayName=b;var C=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:i,orientation:n,loop:o=!1,dir:a,currentTabStopId:u,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:h,onEntryFocus:c,preventScrollOnEntryFocus:x=!1,...S}=e,k=r.useRef(null),A=(0,l.s)(t,k),T=(0,f.jH)(a),[M,C]=(0,m.i)({prop:u,defaultProp:null!=d?d:null,onChange:h,caller:b}),[E,R]=r.useState(!1),j=function(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}(c),_=w(i),V=r.useRef(!1),[O,F]=r.useState(0);return r.useEffect(()=>{let e=k.current;if(e)return e.addEventListener(y,j),()=>e.removeEventListener(y,j)},[j]),(0,g.jsx)(P,{scope:i,orientation:n,dir:T,loop:o,currentTabStopId:M,onItemFocus:r.useCallback(e=>C(e),[C]),onItemShiftTab:r.useCallback(()=>R(!0),[]),onFocusableItemAdd:r.useCallback(()=>F(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>F(e=>e-1),[]),children:(0,g.jsx)(p.sG.div,{tabIndex:E||0===O?-1:0,"data-orientation":n,...S,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,s.m)(e.onMouseDown,()=>{V.current=!0}),onFocus:(0,s.m)(e.onFocus,e=>{let t=!V.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(y,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===M),...e].filter(Boolean).map(e=>e.ref.current),x)}}V.current=!1}),onBlur:(0,s.m)(e.onBlur,()=>R(!1))})})}),E="RovingFocusGroupItem",R=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:i,focusable:n=!0,active:o=!1,tabStopId:a,children:l,...u}=e,d=c(),h=a||d,m=T(E,i),f=m.currentTabStopId===h,y=w(i),{onFocusableItemAdd:v,onFocusableItemRemove:b,currentTabStopId:S}=m;return r.useEffect(()=>{if(n)return v(),()=>b()},[n,v,b]),(0,g.jsx)(x.ItemSlot,{scope:i,id:h,focusable:n,active:o,children:(0,g.jsx)(p.sG.span,{tabIndex:f?0:-1,"data-orientation":m.orientation,...u,ref:t,onMouseDown:(0,s.m)(e.onMouseDown,e=>{n?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,s.m)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,s.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,i){var r;let n=(r=e.key,"rtl"!==i?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return j[n]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let i=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)i.reverse();else if("prev"===t||"next"===t){"prev"===t&&i.reverse();let r=i.indexOf(e.currentTarget);i=m.loop?function(e,t){return e.map((i,r)=>e[(t+r)%e.length])}(i,r+1):i.slice(r+1)}setTimeout(()=>D(i))}}),children:"function"==typeof l?l({isCurrentTabStop:f,hasTabStop:null!=S}):l})})});R.displayName=E;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=document.activeElement;for(let r of e)if(r===i||(r.focus({preventScroll:t}),document.activeElement!==i))return}var _=e=>{let{present:t,children:i}=e,n=function(e){var t,i;let[n,s]=r.useState(),o=r.useRef(null),a=r.useRef(e),l=r.useRef("none"),[d,h]=(t=e?"mounted":"unmounted",i={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=i[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=V(o.current);l.current="mounted"===d?e:"none"},[d]),(0,u.N)(()=>{let t=o.current,i=a.current;if(i!==e){let r=l.current,n=V(t);e?h("MOUNT"):"none"===n||(null==t?void 0:t.display)==="none"?h("UNMOUNT"):i&&r!==n?h("ANIMATION_OUT"):h("UNMOUNT"),a.current=e}},[e,h]),(0,u.N)(()=>{if(n){var e;let t,i=null!=(e=n.ownerDocument.defaultView)?e:window,r=e=>{let r=V(o.current).includes(e.animationName);if(e.target===n&&r&&(h("ANIMATION_END"),!a.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=i.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},s=e=>{e.target===n&&(l.current=V(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{i.clearTimeout(t),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}h("ANIMATION_END")},[n,h]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{o.current=e?getComputedStyle(e):null,s(e)},[])}}(t),s="function"==typeof i?i({present:n.isPresent}):r.Children.only(i),o=(0,l.s)(n.ref,function(e){var t,i;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,n=r&&"isReactWarning"in r&&r.isReactWarning;return n?e.ref:(n=(r=null==(i=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:i.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof i||n.isPresent?r.cloneElement(s,{ref:o}):null};function V(e){return(null==e?void 0:e.animationName)||"none"}_.displayName="Presence";var O="Tabs",[F,z]=(0,o.A)(O,[A]),I=A(),[L,N]=F(O),B=r.forwardRef((e,t)=>{let{__scopeTabs:i,value:r,onValueChange:n,defaultValue:s,orientation:o="horizontal",dir:a,activationMode:l="automatic",...u}=e,d=(0,f.jH)(a),[h,y]=(0,m.i)({prop:r,onChange:n,defaultProp:null!=s?s:"",caller:O});return(0,g.jsx)(L,{scope:i,baseId:c(),value:h,onValueChange:y,orientation:o,dir:d,activationMode:l,children:(0,g.jsx)(p.sG.div,{dir:d,"data-orientation":o,...u,ref:t})})});B.displayName=O;var U="TabsList",$=r.forwardRef((e,t)=>{let{__scopeTabs:i,loop:r=!0,...n}=e,s=N(U,i),o=I(i);return(0,g.jsx)(M,{asChild:!0,...o,orientation:s.orientation,dir:s.dir,loop:r,children:(0,g.jsx)(p.sG.div,{role:"tablist","aria-orientation":s.orientation,...n,ref:t})})});$.displayName=U;var W="TabsTrigger",H=r.forwardRef((e,t)=>{let{__scopeTabs:i,value:r,disabled:n=!1,...o}=e,a=N(W,i),l=I(i),u=K(a.baseId,r),d=X(a.baseId,r),h=r===a.value;return(0,g.jsx)(R,{asChild:!0,...l,focusable:!n,active:h,children:(0,g.jsx)(p.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":d,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...o,ref:t,onMouseDown:(0,s.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():a.onValueChange(r)}),onKeyDown:(0,s.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&a.onValueChange(r)}),onFocus:(0,s.m)(e.onFocus,()=>{let e="manual"!==a.activationMode;h||n||!e||a.onValueChange(r)})})})});H.displayName=W;var G="TabsContent",q=r.forwardRef((e,t)=>{let{__scopeTabs:i,value:n,forceMount:s,children:o,...a}=e,l=N(G,i),u=K(l.baseId,n),d=X(l.baseId,n),h=n===l.value,c=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>c.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,g.jsx)(_,{present:s||h,children:i=>{let{present:r}=i;return(0,g.jsx)(p.sG.div,{"data-state":h?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:d,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:c.current?"0s":void 0},children:r&&o})}})});function K(e,t){return"".concat(e,"-trigger-").concat(t)}function X(e,t){return"".concat(e,"-content-").concat(t)}q.displayName=G;var Y=B,Z=$,J=H,Q=q},63655:(e,t,i)=>{"use strict";i.d(t,{sG:()=>o});var r=i(12115);i(47650);var n=i(99708),s=i(95155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let i=(0,n.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:n,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(n?i:t,{...o,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{})},66766:(e,t,i)=>{"use strict";i.d(t,{default:()=>n.a});var r=i(71469),n=i.n(r)},68375:()=>{},70463:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},70901:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=i(88229)._(i(12115)).default.createContext(null)},71392:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("glasses",[["circle",{cx:"6",cy:"15",r:"4",key:"vux9w4"}],["circle",{cx:"18",cy:"15",r:"4",key:"18o8ve"}],["path",{d:"M14 15a2 2 0 0 0-2-2 2 2 0 0 0-2 2",key:"1ag4bs"}],["path",{d:"M2.5 13 5 7c.7-1.3 1.4-2 3-2",key:"1hm1gs"}],["path",{d:"M21.5 13 19 7c-.7-1.3-1.5-2-3-2",key:"1r31ai"}]])},71469:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return a}});let r=i(88229),n=i(38883),s=i(33063),o=r._(i(51193));function a(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=s.Image},71539:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},72713:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},72894:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},74466:(e,t,i)=>{"use strict";i.d(t,{F:()=>o});var r=i(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=r.$,o=(e,t)=>i=>{var r;if((null==t?void 0:t.variants)==null)return s(e,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:o,defaultVariants:a}=t,l=Object.keys(o).map(e=>{let t=null==i?void 0:i[e],r=null==a?void 0:a[e];if(null===t)return null;let s=n(t)||n(r);return o[e][s]}),u=i&&Object.entries(i).reduce((e,t)=>{let[i,r]=t;return void 0===r||(e[i]=r),e},{});return s(e,l,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:i,className:r,...n}=t;return Object.entries(n).every(e=>{let[t,i]=e;return Array.isArray(i)?i.includes({...a,...u}[t]):({...a,...u})[t]===i})?[...e,i,r]:e},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},74783:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},75100:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:n,blurDataURL:s,objectFit:o}=e,a=r?40*r:t,l=n?40*n:i,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},75313:(e,t,i)=>{"use strict";i.d(t,{Q6:()=>W,bL:()=>U,zi:()=>H,CC:()=>$});var r=i(12115);function n(e,[t,i]){return Math.min(i,Math.max(t,e))}var s=i(85185),o=i(6101),a=i(46081),l=i(5845),u=i(94315),d=i(45503),h=i(11275),c=i(63655),p=i(37328),m=i(95155),f=["PageUp","PageDown"],g=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],y={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},v="Slider",[b,x,w]=(0,p.N)(v),[S,k]=(0,a.A)(v,[w]),[A,P]=S(v),T=r.forwardRef((e,t)=>{let{name:i,min:o=0,max:a=100,step:u=1,orientation:d="horizontal",disabled:h=!1,minStepsBetweenThumbs:c=0,defaultValue:p=[o],value:y,onValueChange:v=()=>{},onValueCommit:x=()=>{},inverted:w=!1,form:S,...k}=e,P=r.useRef(new Set),T=r.useRef(0),M="horizontal"===d,[C=[],j]=(0,l.i)({prop:y,defaultProp:p,onChange:e=>{var t;null==(t=[...P.current][T.current])||t.focus(),v(e)}}),D=r.useRef(C);function _(e,t){let{commit:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1},r=(String(u).split(".")[1]||"").length,s=n(function(e,t){let i=Math.pow(10,t);return Math.round(e*i)/i}(Math.round((e-o)/u)*u+o,r),[o,a]);j(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0,r=[...e];return r[i]=t,r.sort((e,t)=>e-t)}(e,s,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,i)=>e[i+1]-t))>=t;return!0}(r,c*u))return e;{T.current=r.indexOf(s);let t=String(r)!==String(e);return t&&i&&x(r),t?r:e}})}return(0,m.jsx)(A,{scope:e.__scopeSlider,name:i,disabled:h,min:o,max:a,valueIndexToChangeRef:T,thumbs:P.current,values:C,orientation:d,form:S,children:(0,m.jsx)(b.Provider,{scope:e.__scopeSlider,children:(0,m.jsx)(b.Slot,{scope:e.__scopeSlider,children:(0,m.jsx)(M?E:R,{"aria-disabled":h,"data-disabled":h?"":void 0,...k,ref:t,onPointerDown:(0,s.m)(k.onPointerDown,()=>{h||(D.current=C)}),min:o,max:a,inverted:w,onSlideStart:h?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let i=e.map(e=>Math.abs(e-t)),r=Math.min(...i);return i.indexOf(r)}(C,e);_(e,t)},onSlideMove:h?void 0:function(e){_(e,T.current)},onSlideEnd:h?void 0:function(){let e=D.current[T.current];C[T.current]!==e&&x(C)},onHomeKeyDown:()=>!h&&_(o,0,{commit:!0}),onEndKeyDown:()=>!h&&_(a,C.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:i}=e;if(!h){let e=f.includes(t.key)||t.shiftKey&&g.includes(t.key),r=T.current;_(C[r]+u*(e?10:1)*i,r,{commit:!0})}}})})})})});T.displayName=v;var[M,C]=S(v,{startEdge:"left",endEdge:"right",size:"width",direction:1}),E=r.forwardRef((e,t)=>{let{min:i,max:n,dir:s,inverted:a,onSlideStart:l,onSlideMove:d,onSlideEnd:h,onStepKeyDown:c,...p}=e,[f,g]=r.useState(null),v=(0,o.s)(t,e=>g(e)),b=r.useRef(void 0),x=(0,u.jH)(s),w="ltr"===x,S=w&&!a||!w&&a;function k(e){let t=b.current||f.getBoundingClientRect(),r=B([0,t.width],S?[i,n]:[n,i]);return b.current=t,r(e-t.left)}return(0,m.jsx)(M,{scope:e.__scopeSlider,startEdge:S?"left":"right",endEdge:S?"right":"left",direction:S?1:-1,size:"width",children:(0,m.jsx)(j,{dir:x,"data-orientation":"horizontal",...p,ref:v,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=k(e.clientX);null==l||l(t)},onSlideMove:e=>{let t=k(e.clientX);null==d||d(t)},onSlideEnd:()=>{b.current=void 0,null==h||h()},onStepKeyDown:e=>{let t=y[S?"from-left":"from-right"].includes(e.key);null==c||c({event:e,direction:t?-1:1})}})})}),R=r.forwardRef((e,t)=>{let{min:i,max:n,inverted:s,onSlideStart:a,onSlideMove:l,onSlideEnd:u,onStepKeyDown:d,...h}=e,c=r.useRef(null),p=(0,o.s)(t,c),f=r.useRef(void 0),g=!s;function v(e){let t=f.current||c.current.getBoundingClientRect(),r=B([0,t.height],g?[n,i]:[i,n]);return f.current=t,r(e-t.top)}return(0,m.jsx)(M,{scope:e.__scopeSlider,startEdge:g?"bottom":"top",endEdge:g?"top":"bottom",size:"height",direction:g?1:-1,children:(0,m.jsx)(j,{"data-orientation":"vertical",...h,ref:p,style:{...h.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=v(e.clientY);null==a||a(t)},onSlideMove:e=>{let t=v(e.clientY);null==l||l(t)},onSlideEnd:()=>{f.current=void 0,null==u||u()},onStepKeyDown:e=>{let t=y[g?"from-bottom":"from-top"].includes(e.key);null==d||d({event:e,direction:t?-1:1})}})})}),j=r.forwardRef((e,t)=>{let{__scopeSlider:i,onSlideStart:r,onSlideMove:n,onSlideEnd:o,onHomeKeyDown:a,onEndKeyDown:l,onStepKeyDown:u,...d}=e,h=P(v,i);return(0,m.jsx)(c.sG.span,{...d,ref:t,onKeyDown:(0,s.m)(e.onKeyDown,e=>{"Home"===e.key?(a(e),e.preventDefault()):"End"===e.key?(l(e),e.preventDefault()):f.concat(g).includes(e.key)&&(u(e),e.preventDefault())}),onPointerDown:(0,s.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),h.thumbs.has(t)?t.focus():r(e)}),onPointerMove:(0,s.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&n(e)}),onPointerUp:(0,s.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),o(e))})})}),D="SliderTrack",_=r.forwardRef((e,t)=>{let{__scopeSlider:i,...r}=e,n=P(D,i);return(0,m.jsx)(c.sG.span,{"data-disabled":n.disabled?"":void 0,"data-orientation":n.orientation,...r,ref:t})});_.displayName=D;var V="SliderRange",O=r.forwardRef((e,t)=>{let{__scopeSlider:i,...n}=e,s=P(V,i),a=C(V,i),l=r.useRef(null),u=(0,o.s)(t,l),d=s.values.length,h=s.values.map(e=>N(e,s.min,s.max)),p=d>1?Math.min(...h):0,f=100-Math.max(...h);return(0,m.jsx)(c.sG.span,{"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,...n,ref:u,style:{...e.style,[a.startEdge]:p+"%",[a.endEdge]:f+"%"}})});O.displayName=V;var F="SliderThumb",z=r.forwardRef((e,t)=>{let i=x(e.__scopeSlider),[n,s]=r.useState(null),a=(0,o.s)(t,e=>s(e)),l=r.useMemo(()=>n?i().findIndex(e=>e.ref.current===n):-1,[i,n]);return(0,m.jsx)(I,{...e,ref:a,index:l})}),I=r.forwardRef((e,t)=>{let{__scopeSlider:i,index:n,name:a,...l}=e,u=P(F,i),d=C(F,i),[p,f]=r.useState(null),g=(0,o.s)(t,e=>f(e)),y=!p||u.form||!!p.closest("form"),v=(0,h.X)(p),x=u.values[n],w=void 0===x?0:N(x,u.min,u.max),S=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(n,u.values.length),k=null==v?void 0:v[d.size],A=k?function(e,t,i){let r=e/2,n=B([0,50],[0,r]);return(r-n(t)*i)*i}(k,w,d.direction):0;return r.useEffect(()=>{if(p)return u.thumbs.add(p),()=>{u.thumbs.delete(p)}},[p,u.thumbs]),(0,m.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[d.startEdge]:"calc(".concat(w,"% + ").concat(A,"px)")},children:[(0,m.jsx)(b.ItemSlot,{scope:e.__scopeSlider,children:(0,m.jsx)(c.sG.span,{role:"slider","aria-label":e["aria-label"]||S,"aria-valuemin":u.min,"aria-valuenow":x,"aria-valuemax":u.max,"aria-orientation":u.orientation,"data-orientation":u.orientation,"data-disabled":u.disabled?"":void 0,tabIndex:u.disabled?void 0:0,...l,ref:g,style:void 0===x?{display:"none"}:e.style,onFocus:(0,s.m)(e.onFocus,()=>{u.valueIndexToChangeRef.current=n})})}),y&&(0,m.jsx)(L,{name:null!=a?a:u.name?u.name+(u.values.length>1?"[]":""):void 0,form:u.form,value:x},n)]})});z.displayName=F;var L=r.forwardRef((e,t)=>{let{__scopeSlider:i,value:n,...s}=e,a=r.useRef(null),l=(0,o.s)(a,t),u=(0,d.Z)(n);return r.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(u!==n&&t){let i=new Event("input",{bubbles:!0});t.call(e,n),e.dispatchEvent(i)}},[u,n]),(0,m.jsx)(c.sG.input,{style:{display:"none"},...s,ref:l,defaultValue:n})});function N(e,t,i){return n(100/(i-t)*(e-t),[0,100])}function B(e,t){return i=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(i-e[0])}}L.displayName="RadioBubbleInput";var U=T,$=_,W=O,H=z},75684:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},76604:(e,t,i)=>{"use strict";i.d(t,{W:()=>o});var r=i(12115),n=i(42198);let s={some:0,all:1};function o(e,{root:t,margin:i,amount:a,once:l=!1,initial:u=!1}={}){let[d,h]=(0,r.useState)(u);return(0,r.useEffect)(()=>{if(!e.current||l&&d)return;let r={root:t&&t.current||void 0,margin:i,amount:a};return function(e,t,{root:i,margin:r,amount:o="some"}={}){let a=(0,n.K)(e),l=new WeakMap,u=new IntersectionObserver(e=>{e.forEach(e=>{let i=l.get(e.target);if(!!i!==e.isIntersecting)if(e.isIntersecting){let i=t(e.target,e);"function"==typeof i?l.set(e.target,i):u.unobserve(e.target)}else"function"==typeof i&&(i(e),l.delete(e.target))})},{root:i,rootMargin:r,threshold:"number"==typeof o?o:s[o]});return a.forEach(e=>u.observe(e)),()=>u.disconnect()}(e.current,()=>(h(!0),l?void 0:()=>h(!1)),r)},[t,e,i,l,a]),d}},82269:(e,t,i)=>{"use strict";var r=i(49509);i(68375);var n=i(12115),s=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(n),o=void 0!==r&&r.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,i=t.name,r=void 0===i?"stylesheet":i,n=t.optimizeForSpeed,s=void 0===n?o:n;u(a(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",u("boolean"==typeof s,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=s,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,i=e.prototype;return i.setOptimizeForSpeed=function(e){u("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},i.isOptimizeForSpeed=function(){return this._optimizeForSpeed},i.inject=function(){var e=this;if(u(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,i){return"number"==typeof i?e._serverSheet.cssRules[i]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),i},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},i.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},i.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},i.insertRule=function(e,t){if(u(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var i=this.getSheet();"number"!=typeof t&&(t=i.cssRules.length);try{i.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},i.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var i="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!i.cssRules[e])return e;i.deleteRule(e);try{i.insertRule(t,e)}catch(r){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),i.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];u(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},i.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];u(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},i.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},i.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,i){return i?t=t.concat(Array.prototype.map.call(e.getSheetForTag(i).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},i.makeStyleTag=function(e,t,i){t&&u(a(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return i?n.insertBefore(r,i):n.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function u(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,i=e.length;i;)t=33*t^e.charCodeAt(--i);return t>>>0},h={};function c(e,t){if(!t)return"jsx-"+e;var i=String(t),r=e+i;return h[r]||(h[r]="jsx-"+d(e+"-"+i)),h[r]}function p(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var i=e+t;return h[i]||(h[i]=t.replace(/__jsx-style-dynamic-selector/g,e)),h[i]}var m=function(){function e(e){var t=void 0===e?{}:e,i=t.styleSheet,r=void 0===i?null:i,n=t.optimizeForSpeed,s=void 0!==n&&n;this._sheet=r||new l({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),r&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var i=this.getIdAndRules(e),r=i.styleId,n=i.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var s=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=s,this._instancesCounts[r]=1},t.remove=function(e){var t=this,i=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(i in this._instancesCounts,"styleId: `"+i+"` not found"),this._instancesCounts[i]-=1,this._instancesCounts[i]<1){var r=this._fromServer&&this._fromServer[i];r?(r.parentNode.removeChild(r),delete this._fromServer[i]):(this._indices[i].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[i]),delete this._instancesCounts[i]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],i=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return i[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,i;return t=this.cssRules(),void 0===(i=e)&&(i={}),t.map(function(e){var t=e[0],r=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:i.nonce?i.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,i=e.dynamic,r=e.id;if(i){var n=c(r,i);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return p(n,e)}):[p(n,t)]}}return{styleId:c(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),f=n.createContext(null);f.displayName="StyleSheetContext";var g=s.default.useInsertionEffect||s.default.useLayoutEffect,y="undefined"!=typeof window?new m:void 0;function v(e){var t=y||n.useContext(f);return t&&("undefined"==typeof window?t.add(e):g(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}v.dynamic=function(e){return e.map(function(e){return c(e[0],e[1])}).join(" ")},t.style=v},85029:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let r=i(12115),n=r.useLayoutEffect,s=r.useEffect;function o(e){let{headManager:t,reduceComponentsToState:i}=e;function o(){if(t&&t.mountedInstances){let n=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(i(n,e))}}return n(()=>{var i;return null==t||null==(i=t.mountedInstances)||i.add(e.children),()=>{var i;null==t||null==(i=t.mountedInstances)||i.delete(e.children)}}),n(()=>(t&&(t._pendingUpdate=o),()=>{t&&(t._pendingUpdate=o)})),s(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},85185:(e,t,i)=>{"use strict";function r(e,t,{checkForDefaultPrevented:i=!0}={}){return function(r){if(e?.(r),!1===i||!r.defaultPrevented)return t?.(r)}}i.d(t,{m:()=>r})},86752:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return s}});let r=i(88229)._(i(12115)),n=i(95840),s=r.default.createContext(n.imageConfigDefault)},92657:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},94315:(e,t,i)=>{"use strict";i.d(t,{jH:()=>s});var r=i(12115);i(95155);var n=r.createContext(void 0);function s(e){let t=r.useContext(n);return e||t||"ltr"}},94498:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(19946).A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]])},95840:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return r}});let i=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},99708:(e,t,i)=>{"use strict";i.d(t,{TL:()=>o});var r=i(12115),n=i(6101),s=i(95155);function o(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:i,...s}=e;if(r.isValidElement(i)){var o;let e,a,l=(o=i,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),u=function(e,t){let i={...t};for(let r in t){let n=e[r],s=t[r];/^on[A-Z]/.test(r)?n&&s?i[r]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(i[r]=n):"style"===r?i[r]={...n,...s}:"className"===r&&(i[r]=[n,s].filter(Boolean).join(" "))}return{...e,...i}}(s,i.props);return i.type!==r.Fragment&&(u.ref=t?(0,n.t)(t,l):l),r.cloneElement(i,u)}return r.Children.count(i)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),i=r.forwardRef((e,i)=>{let{children:n,...o}=e,a=r.Children.toArray(n),u=a.find(l);if(u){let e=u.props.children,n=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...o,ref:i,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,s.jsx)(t,{...o,ref:i,children:n})});return i.displayName=`${e}.Slot`,i}var a=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}}]);