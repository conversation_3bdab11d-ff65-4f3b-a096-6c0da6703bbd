import { Action } from "./action.js";
import type { Condition } from "./condition.js";
import { Observable } from "../Misc/observable.js";
/**
 * This defines an action responsible to change the value of a property
 * by interpolating between its current value and the newly set one once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class InterpolateValueAction extends Action {
    /**
     * Defines the path of the property where the value should be interpolated
     */
    propertyPath: string;
    /**
     * Defines the target value at the end of the interpolation.
     */
    value: any;
    /**
     * Defines the time it will take for the property to interpolate to the value.
     */
    duration: number;
    /**
     * Defines if the other scene animations should be stopped when the action has been triggered
     */
    stopOtherAnimations?: boolean;
    /**
     * Defines a callback raised once the interpolation animation has been done.
     */
    onInterpolationDone?: () => void;
    /**
     * Observable triggered once the interpolation animation has been done.
     */
    onInterpolationDoneObservable: Observable<InterpolateValueAction>;
    private _target;
    private _effectiveTarget;
    private _property;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param target defines the object containing the value to interpolate
     * @param propertyPath defines the path to the property in the target object
     * @param value defines the target value at the end of the interpolation
     * @param duration defines the time it will take for the property to interpolate to the value.
     * @param condition defines the trigger related conditions
     * @param stopOtherAnimations defines if the other scene animations should be stopped when the action has been triggered
     * @param onInterpolationDone defines a callback raised once the interpolation animation has been done
     */
    constructor(triggerOptions: any, target: any, propertyPath: string, value: any, duration?: number, condition?: Condition, stopOtherAnimations?: boolean, onInterpolationDone?: () => void);
    /** @internal */
    _prepare(): void;
    /**
     * Execute the action starts the value interpolation.
     */
    execute(): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
