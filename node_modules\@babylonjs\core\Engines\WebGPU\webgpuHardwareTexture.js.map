{"version": 3, "file": "webgpuHardwareTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuHardwareTexture.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,KAAK,EAAE,MAAM,mCAAmC,CAAC;AAI1D,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAG5D,gBAAgB;AAChB,MAAM,OAAO,qBAAqB;IA6B9B,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEM,cAAc,CAAC,KAAa;QAC/B,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IACpD,CAAC;IAEM,cAAc,CAAC,OAAmB,EAAE,KAAa;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;IAC7C,CAAC;IAEM,kBAAkB,CAAC,KAAc;QACpC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC3E,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACJ,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5C,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBACxD,CAAC;gBACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACnC,CAAC;QACL,CAAC;IACL,CAAC;IAQD,YACY,OAAqB,EAC7B,kBAAwC,IAAI;QADpC,YAAO,GAAP,OAAO,CAAc;QA7CjC,gBAAgB;QACT,yBAAoB,GAAG,KAAK,CAAC;QAuC7B,WAAM,+DAA8D;QACpE,kBAAa,GAAG,CAAC,CAAC;QAClB,4BAAuB,GAAG,CAAC,CAAC;QAM/B,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;QACtC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEM,GAAG,CAAC,eAA2B;QAClC,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;IAC1C,CAAC;IAEM,QAAQ,CAAC,cAAsB,EAAE,eAAwB,EAAE,SAAkB,EAAE,MAAe,EAAE,IAAa,EAAE,KAAa,EAAE,MAAc,EAAE,KAAa;QAC9J,IAAI,aAAa,sDAAoE,CAAC;QACtF,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,MAAM,EAAE,CAAC;YACT,aAAa,GAAG,SAAS,CAAC,CAAC,mEAAgD,CAAC,uDAA0C,CAAC;YACvH,eAAe,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;QACvC,CAAC;aAAM,IAAI,IAAI,EAAE,CAAC;YACd,aAAa,sDAA2C,CAAC;YACzD,eAAe,GAAG,CAAC,CAAC;QACxB,CAAC;aAAM,IAAI,SAAS,EAAE,CAAC;YACnB,aAAa,iEAAgD,CAAC;YAC9D,eAAe,GAAG,KAAK,CAAC;QAC5B,CAAC;QAED,MAAM,MAAM,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnE,MAAM,MAAM,GAAG,mBAAmB,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,4DAAyC,CAAC,8CAAkC,CAAC;QAExJ,IAAI,CAAC,UAAU,CAAC;YACZ,KAAK,EAAE,cAAc,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,IAAI,MAAM,IAAI,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,IAC9J,IAAI,CAAC,MACT,IAAI,aAAa,EAAE;YACnB,MAAM;YACN,SAAS,EAAE,aAAa;YACxB,aAAa,EAAE,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACvE,cAAc,EAAE,CAAC;YACjB,YAAY,EAAE,CAAC;YACf,eAAe;YACf,MAAM;SACT,CAAC,CAAC;IACP,CAAC;IAEM,UAAU,CAAC,UAAqC,EAAE,oBAAoB,GAAG,KAAK;QACjF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAe,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QACxD,IAAI,oBAAoB,IAAI,UAAU,EAAE,CAAC;YACrC,MAAM,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC;YAChD,UAAU,CAAC,aAAa,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAe,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAClE,UAAU,CAAC,aAAa,GAAG,cAAc,CAAC;QAC9C,CAAC;IACL,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC;QAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,uBAAuB,EAAE,OAAO,EAAE,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;CACJ", "sourcesContent": ["/* eslint-disable jsdoc/require-jsdoc */\r\n/* eslint-disable babylonjs/available */\r\nimport type { IHardwareTextureWrapper } from \"../../Materials/Textures/hardwareTextureWrapper\";\r\nimport { ILog2 } from \"../../Maths/math.scalar.functions\";\r\nimport type { Nullable } from \"../../types\";\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport { WebGPUTextureHelper } from \"./webgpuTextureHelper\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\n\r\n/** @internal */\r\nexport class WebGPUHardwareTexture implements IHardwareTextureWrapper {\r\n    /**\r\n     * Cache of RenderPassDescriptor and BindGroup used when generating mipmaps (see WebGPUTextureHelper.generateMipmaps)\r\n     * @internal\r\n     */\r\n    public _mipmapGenRenderPassDescr: GPURenderPassDescriptor[][];\r\n    /** @internal */\r\n    public _mipmapGenBindGroup: GPUBindGroup[][];\r\n\r\n    /**\r\n     * Cache for the invertYPreMultiplyAlpha function (see WebGPUTextureHelper)\r\n     * @internal\r\n     */\r\n    public _copyInvertYTempTexture?: GPUTexture;\r\n    /** @internal */\r\n    public _copyInvertYRenderPassDescr: GPURenderPassDescriptor;\r\n    /** @internal */\r\n    public _copyInvertYBindGroup: GPUBindGroup;\r\n    /** @internal */\r\n    public _copyInvertYBindGroupWithOfst: GPUBindGroup;\r\n\r\n    /** @internal */\r\n    public _originalFormatIsRGB = false;\r\n\r\n    private _webgpuTexture: Nullable<GPUTexture>;\r\n    // There can be multiple MSAA textures for a single WebGPU texture because different layers of a 2DArrayTexture / 3DTexture\r\n    // or different faces of a cube texture can be bound to different render targets at the same time (in a multi RenderTargetWrapper)\r\n    private _webgpuMSAATexture: Nullable<GPUTexture[]>;\r\n\r\n    public get underlyingResource(): Nullable<GPUTexture> {\r\n        return this._webgpuTexture;\r\n    }\r\n\r\n    public getMSAATexture(index: number): Nullable<GPUTexture> {\r\n        return this._webgpuMSAATexture?.[index] ?? null;\r\n    }\r\n\r\n    public setMSAATexture(texture: GPUTexture, index: number) {\r\n        if (!this._webgpuMSAATexture) {\r\n            this._webgpuMSAATexture = [];\r\n        }\r\n\r\n        this._webgpuMSAATexture[index] = texture;\r\n    }\r\n\r\n    public releaseMSAATexture(index?: number): void {\r\n        if (this._webgpuMSAATexture) {\r\n            if (index !== undefined) {\r\n                this._engine._textureHelper.releaseTexture(this._webgpuMSAATexture[index]);\r\n                delete this._webgpuMSAATexture[index];\r\n            } else {\r\n                for (const texture of this._webgpuMSAATexture) {\r\n                    this._engine._textureHelper.releaseTexture(texture);\r\n                }\r\n                this._webgpuMSAATexture = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    public view: Nullable<GPUTextureView>;\r\n    public viewForWriting: Nullable<GPUTextureView>;\r\n    public format: GPUTextureFormat = WebGPUConstants.TextureFormat.RGBA8Unorm;\r\n    public textureUsages = 0;\r\n    public textureAdditionalUsages = 0;\r\n\r\n    constructor(\r\n        private _engine: WebGPUEngine,\r\n        existingTexture: Nullable<GPUTexture> = null\r\n    ) {\r\n        this._webgpuTexture = existingTexture;\r\n        this._webgpuMSAATexture = null;\r\n        this.view = null;\r\n        this.viewForWriting = null;\r\n    }\r\n\r\n    public set(hardwareTexture: GPUTexture): void {\r\n        this._webgpuTexture = hardwareTexture;\r\n    }\r\n\r\n    public setUsage(_textureSource: number, generateMipMaps: boolean, is2DArray: boolean, isCube: boolean, is3D: boolean, width: number, height: number, depth: number): void {\r\n        let viewDimension: GPUTextureViewDimension = WebGPUConstants.TextureViewDimension.E2d;\r\n        let arrayLayerCount = 1;\r\n        if (isCube) {\r\n            viewDimension = is2DArray ? WebGPUConstants.TextureViewDimension.CubeArray : WebGPUConstants.TextureViewDimension.Cube;\r\n            arrayLayerCount = 6 * (depth || 1);\r\n        } else if (is3D) {\r\n            viewDimension = WebGPUConstants.TextureViewDimension.E3d;\r\n            arrayLayerCount = 1;\r\n        } else if (is2DArray) {\r\n            viewDimension = WebGPUConstants.TextureViewDimension.E2dArray;\r\n            arrayLayerCount = depth;\r\n        }\r\n\r\n        const format = WebGPUTextureHelper.GetDepthFormatOnly(this.format);\r\n        const aspect = WebGPUTextureHelper.HasDepthAndStencilAspects(this.format) ? WebGPUConstants.TextureAspect.DepthOnly : WebGPUConstants.TextureAspect.All;\r\n\r\n        this.createView({\r\n            label: `TextureView${is3D ? \"3D\" : isCube ? \"Cube\" : \"2D\"}${is2DArray ? \"_Array\" + arrayLayerCount : \"\"}_${width}x${height}_${generateMipMaps ? \"wmips\" : \"womips\"}_${\r\n                this.format\r\n            }_${viewDimension}`,\r\n            format,\r\n            dimension: viewDimension,\r\n            mipLevelCount: generateMipMaps ? ILog2(Math.max(width, height)) + 1 : 1,\r\n            baseArrayLayer: 0,\r\n            baseMipLevel: 0,\r\n            arrayLayerCount,\r\n            aspect,\r\n        });\r\n    }\r\n\r\n    public createView(descriptor?: GPUTextureViewDescriptor, createViewForWriting = false): void {\r\n        this.view = this._webgpuTexture!.createView(descriptor);\r\n        if (createViewForWriting && descriptor) {\r\n            const saveNumMipMaps = descriptor.mipLevelCount;\r\n            descriptor.mipLevelCount = 1;\r\n            this.viewForWriting = this._webgpuTexture!.createView(descriptor);\r\n            descriptor.mipLevelCount = saveNumMipMaps;\r\n        }\r\n    }\r\n\r\n    public reset(): void {\r\n        this._webgpuTexture = null;\r\n        this._webgpuMSAATexture = null;\r\n        this.view = null;\r\n        this.viewForWriting = null;\r\n    }\r\n\r\n    public release(): void {\r\n        this._webgpuTexture?.destroy();\r\n        this.releaseMSAATexture();\r\n        this._copyInvertYTempTexture?.destroy();\r\n        this.reset();\r\n    }\r\n}\r\n"]}