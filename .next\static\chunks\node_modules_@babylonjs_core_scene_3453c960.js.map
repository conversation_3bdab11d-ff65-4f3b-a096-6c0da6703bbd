{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/scene.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/scene.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { Nullable } from \"./types\";\r\nimport { Tools } from \"./Misc/tools\";\r\nimport type { IAnimatable } from \"./Animations/animatable.interface\";\r\nimport { PrecisionDate } from \"./Misc/precisionDate\";\r\nimport type { Observer } from \"./Misc/observable\";\r\nimport { Observable } from \"./Misc/observable\";\r\nimport type { ISmartArrayLike } from \"./Misc/smartArray\";\r\nimport { SmartArrayNoDuplicate, SmartArray } from \"./Misc/smartArray\";\r\nimport { StringDictionary } from \"./Misc/stringDictionary\";\r\nimport { Tags } from \"./Misc/tags\";\r\nimport type { Vector2, Vector4 } from \"./Maths/math.vector\";\r\nimport { Vector3, Matrix, TmpVectors } from \"./Maths/math.vector\";\r\nimport type { IParticleSystem } from \"./Particles/IParticleSystem\";\r\nimport { ImageProcessingConfiguration } from \"./Materials/imageProcessingConfiguration\";\r\nimport { UniformBuffer } from \"./Materials/uniformBuffer\";\r\nimport { PickingInfo } from \"./Collisions/pickingInfo\";\r\nimport type { ICollisionCoordinator } from \"./Collisions/collisionCoordinator\";\r\nimport type { PointerEventTypes, PointerInfoPre, PointerInfo } from \"./Events/pointerEvents\";\r\nimport type { KeyboardInfoPre, KeyboardInfo } from \"./Events/keyboardEvents\";\r\nimport { ActionEvent } from \"./Actions/actionEvent\";\r\nimport { PostProcessManager } from \"./PostProcesses/postProcessManager\";\r\nimport type { IOfflineProvider } from \"./Offline/IOfflineProvider\";\r\nimport type { RenderingGroupInfo, IRenderingManagerAutoClearSetup } from \"./Rendering/renderingManager\";\r\nimport { RenderingManager } from \"./Rendering/renderingManager\";\r\nimport type {\r\n    ISceneComponent,\r\n    ISceneSerializableComponent,\r\n    SimpleStageAction,\r\n    RenderTargetsStageAction,\r\n    RenderTargetStageAction,\r\n    MeshStageAction,\r\n    EvaluateSubMeshStageAction,\r\n    PreActiveMeshStageAction,\r\n    CameraStageAction,\r\n    RenderingGroupStageAction,\r\n    RenderingMeshStageAction,\r\n    PointerMoveStageAction,\r\n    PointerUpDownStageAction,\r\n    CameraStageFrameBufferAction,\r\n} from \"./sceneComponent\";\r\nimport { Stage } from \"./sceneComponent\";\r\nimport { Constants } from \"./Engines/constants\";\r\nimport { IsWindowObjectExist } from \"./Misc/domManagement\";\r\nimport { EngineStore } from \"./Engines/engineStore\";\r\nimport type { AbstractActionManager } from \"./Actions/abstractActionManager\";\r\nimport { _WarnImport } from \"./Misc/devTools\";\r\nimport type { WebRequest } from \"./Misc/webRequest\";\r\nimport { InputManager } from \"./Inputs/scene.inputManager\";\r\nimport { PerfCounter } from \"./Misc/perfCounter\";\r\nimport type { IFileRequest } from \"./Misc/fileRequest\";\r\nimport { Color4, Color3 } from \"./Maths/math.color\";\r\nimport type { Plane } from \"./Maths/math.plane\";\r\nimport { Frustum } from \"./Maths/math.frustum\";\r\nimport { UniqueIdGenerator } from \"./Misc/uniqueIdGenerator\";\r\nimport type { LoadFileError, RequestFileError, ReadFileError } from \"./Misc/fileTools\";\r\nimport { ReadFile, RequestFile, LoadFile } from \"./Misc/fileTools\";\r\nimport type { IClipPlanesHolder } from \"./Misc/interfaces/iClipPlanesHolder\";\r\nimport type { IPointerEvent } from \"./Events/deviceInputEvents\";\r\nimport { LightConstants } from \"./Lights/lightConstants\";\r\nimport { _ObserveArray } from \"./Misc/arrayTools\";\r\nimport type { IAction } from \"./Actions/action\";\r\nimport type { AnimationPropertiesOverride } from \"./Animations/animationPropertiesOverride\";\r\nimport type { AnimationGroup } from \"./Animations/animationGroup\";\r\nimport type { Skeleton } from \"./Bones/skeleton\";\r\nimport type { Bone } from \"./Bones/bone\";\r\nimport type { Camera } from \"./Cameras/camera\";\r\nimport type { Collider } from \"./Collisions/collider\";\r\nimport type { Ray, MeshPredicate, TrianglePickingPredicate } from \"./Culling/ray.core\";\r\nimport type { Light } from \"./Lights/light\";\r\nimport type { PerformanceViewerCollector } from \"./Misc/PerformanceViewer/performanceViewerCollector\";\r\nimport type { MorphTarget } from \"./Morph/morphTarget\";\r\nimport type { MorphTargetManager } from \"./Morph/morphTargetManager\";\r\nimport type { PostProcess } from \"./PostProcesses/postProcess\";\r\nimport type { Material } from \"./Materials/material\";\r\nimport type { BaseTexture } from \"./Materials/Textures/baseTexture\";\r\nimport type { Geometry } from \"./Meshes/geometry\";\r\nimport type { TransformNode } from \"./Meshes/transformNode\";\r\nimport type { AbstractMesh } from \"./Meshes/abstractMesh\";\r\nimport type { MultiMaterial } from \"./Materials/multiMaterial\";\r\nimport type { Effect } from \"./Materials/effect\";\r\nimport type { RenderTargetTexture } from \"./Materials/Textures/renderTargetTexture\";\r\nimport type { Mesh } from \"./Meshes/mesh\";\r\nimport type { SubMesh } from \"./Meshes/subMesh\";\r\nimport type { Node } from \"./node\";\r\nimport type { Animation } from \"./Animations/animation\";\r\nimport type { Animatable } from \"./Animations/animatable.core\";\r\nimport type { Texture } from \"./Materials/Textures/texture\";\r\nimport { PointerPickingConfiguration } from \"./Inputs/pointerPickingConfiguration\";\r\nimport { Logger } from \"./Misc/logger\";\r\nimport type { AbstractEngine } from \"./Engines/abstractEngine\";\r\nimport { RegisterClass } from \"./Misc/typeStore\";\r\nimport type { FrameGraph } from \"./FrameGraph/frameGraph\";\r\nimport type { IAssetContainer } from \"./IAssetContainer\";\r\n\r\nimport type { EffectLayer } from \"./Layers/effectLayer\";\r\nimport type { Sound } from \"./Audio/sound\";\r\nimport type { Layer } from \"./Layers/layer\";\r\nimport type { LensFlareSystem } from \"./LensFlares/lensFlareSystem\";\r\nimport type { ProceduralTexture } from \"./Materials/Textures/Procedurals/proceduralTexture\";\r\n\r\n/**\r\n * Define an interface for all classes that will hold resources\r\n */\r\nexport interface IDisposable {\r\n    /**\r\n     * Releases all held resources\r\n     */\r\n    dispose(): void;\r\n}\r\n\r\n/** Interface defining initialization parameters for Scene class */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface SceneOptions {\r\n    /**\r\n     * Defines that scene should keep up-to-date a map of geometry to enable fast look-up by uniqueId\r\n     * It will improve performance when the number of geometries becomes important.\r\n     */\r\n    useGeometryUniqueIdsMap?: boolean;\r\n\r\n    /**\r\n     * Defines that each material of the scene should keep up-to-date a map of referencing meshes for fast disposing\r\n     * It will improve performance when the number of mesh becomes important, but might consume a bit more memory\r\n     */\r\n    useMaterialMeshMap?: boolean;\r\n\r\n    /**\r\n     * Defines that each mesh of the scene should keep up-to-date a map of referencing cloned meshes for fast disposing\r\n     * It will improve performance when the number of mesh becomes important, but might consume a bit more memory\r\n     */\r\n    useClonedMeshMap?: boolean;\r\n\r\n    /** Defines if the creation of the scene should impact the engine (Eg. UtilityLayer's scene) */\r\n    virtual?: boolean;\r\n}\r\n\r\n/**\r\n * Define how the scene should favor performance over ease of use\r\n */\r\nexport const enum ScenePerformancePriority {\r\n    /** Default mode. No change. Performance will be treated as less important than backward compatibility */\r\n    BackwardCompatible,\r\n    /** Some performance options will be turned on trying to strike a balance between perf and ease of use */\r\n    Intermediate,\r\n    /** Performance will be top priority */\r\n    Aggressive,\r\n}\r\n\r\n/**\r\n * Represents a scene to be rendered by the engine.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene\r\n */\r\nexport class Scene implements IAnimatable, IClipPlanesHolder, IAssetContainer {\r\n    /** The fog is deactivated */\r\n    public static readonly FOGMODE_NONE = Constants.FOGMODE_NONE;\r\n    /** The fog density is following an exponential function */\r\n    public static readonly FOGMODE_EXP = Constants.FOGMODE_EXP;\r\n    /** The fog density is following an exponential function faster than FOGMODE_EXP */\r\n    public static readonly FOGMODE_EXP2 = Constants.FOGMODE_EXP2;\r\n    /** The fog density is following a linear function. */\r\n    public static readonly FOGMODE_LINEAR = Constants.FOGMODE_LINEAR;\r\n\r\n    /**\r\n     * Gets or sets the minimum deltatime when deterministic lock step is enabled\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     */\r\n    public static MinDeltaTime = 1.0;\r\n    /**\r\n     * Gets or sets the maximum deltatime when deterministic lock step is enabled\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     */\r\n    public static MaxDeltaTime = 1000.0;\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Factory used to create the default material.\r\n     * @param scene The scene to create the material for\r\n     * @returns The default material\r\n     */\r\n    public static DefaultMaterialFactory(scene: Scene): Material {\r\n        throw _WarnImport(\"StandardMaterial\");\r\n    }\r\n\r\n    private static readonly _OriginalDefaultMaterialFactory = Scene.DefaultMaterialFactory;\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Factory used to create the a collision coordinator.\r\n     * @returns The collision coordinator\r\n     */\r\n    public static CollisionCoordinatorFactory(): ICollisionCoordinator {\r\n        throw _WarnImport(\"DefaultCollisionCoordinator\");\r\n    }\r\n\r\n    // Members\r\n\r\n    /** @internal */\r\n    public _tempPickingRay: Nullable<Ray>;\r\n\r\n    /** @internal */\r\n    public _cachedRayForTransform: Ray;\r\n\r\n    /** @internal */\r\n    public _pickWithRayInverseMatrix: Matrix;\r\n\r\n    /** @internal */\r\n    public _inputManager = new InputManager(this);\r\n\r\n    /** Define this parameter if you are using multiple cameras and you want to specify which one should be used for pointer position */\r\n    public cameraToUseForPointers: Nullable<Camera> = null;\r\n\r\n    /** @internal */\r\n    public readonly _isScene = true;\r\n\r\n    /** @internal */\r\n    public _blockEntityCollection = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean that indicates if the scene must clear the render buffer before rendering a frame\r\n     */\r\n    public autoClear = true;\r\n    /**\r\n     * Gets or sets a boolean that indicates if the scene must clear the depth and stencil buffers before rendering a frame\r\n     */\r\n    public autoClearDepthAndStencil = true;\r\n\r\n    private _clearColor: Color4 = new Color4(0.2, 0.2, 0.3, 1.0);\r\n\r\n    /**\r\n     * Observable triggered when the performance priority is changed\r\n     */\r\n    public onClearColorChangedObservable = new Observable<Color4>();\r\n\r\n    /**\r\n     * Defines the color used to clear the render buffer (Default is (0.2, 0.2, 0.3, 1.0))\r\n     */\r\n    public get clearColor(): Color4 {\r\n        return this._clearColor;\r\n    }\r\n\r\n    public set clearColor(value: Color4) {\r\n        if (value !== this._clearColor) {\r\n            this._clearColor = value;\r\n            this.onClearColorChangedObservable.notifyObservers(this._clearColor);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Defines the color used to simulate the ambient color (Default is (0, 0, 0))\r\n     */\r\n    public ambientColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * This is use to store the default BRDF lookup for PBR materials in your scene.\r\n     * It should only be one of the following (if not the default embedded one):\r\n     * * For uncorrelated BRDF (pbr.brdf.useEnergyConservation = false and pbr.brdf.useSmithVisibilityHeightCorrelated = false) : https://assets.babylonjs.com/environments/uncorrelatedBRDF.dds\r\n     * * For correlated BRDF (pbr.brdf.useEnergyConservation = false and pbr.brdf.useSmithVisibilityHeightCorrelated = true) : https://assets.babylonjs.com/environments/correlatedBRDF.dds\r\n     * * For correlated multi scattering BRDF (pbr.brdf.useEnergyConservation = true and pbr.brdf.useSmithVisibilityHeightCorrelated = true) : https://assets.babylonjs.com/environments/correlatedMSBRDF.dds\r\n     * The material properties need to be setup according to the type of texture in use.\r\n     */\r\n    public environmentBRDFTexture: BaseTexture;\r\n\r\n    /**\r\n     * Intensity of the environment (i.e. all indirect lighting) in all pbr material.\r\n     * This dims or reinforces the indirect lighting overall (reflection and diffuse).\r\n     * As in the majority of the scene they are the same (exception for multi room and so on),\r\n     * this is easier to reference from here than from all the materials.\r\n     * Note that this is more of a debugging parameter and is not physically accurate.\r\n     * If you want to modify the intensity of the IBL texture, you should update iblIntensity instead.\r\n     */\r\n    public environmentIntensity: number = 1;\r\n\r\n    /**\r\n     * Overall intensity of the IBL texture.\r\n     * This value is multiplied with the reflectionTexture.level value to calculate the final IBL intensity.\r\n     */\r\n    public iblIntensity = 1;\r\n\r\n    /** @internal */\r\n    protected _imageProcessingConfiguration: ImageProcessingConfiguration;\r\n    /**\r\n     * Default image processing configuration used either in the rendering\r\n     * Forward main pass or through the imageProcessingPostProcess if present.\r\n     * As in the majority of the scene they are the same (exception for multi camera),\r\n     * this is easier to reference from here than from all the materials and post process.\r\n     *\r\n     * No setter as we it is a shared configuration, you can set the values instead.\r\n     */\r\n    public get imageProcessingConfiguration(): ImageProcessingConfiguration {\r\n        return this._imageProcessingConfiguration;\r\n    }\r\n\r\n    private _performancePriority = ScenePerformancePriority.BackwardCompatible;\r\n\r\n    /**\r\n     * Observable triggered when the performance priority is changed\r\n     */\r\n    public onScenePerformancePriorityChangedObservable = new Observable<ScenePerformancePriority>();\r\n    /**\r\n     * Gets or sets a value indicating how to treat performance relatively to ease of use and backward compatibility\r\n     */\r\n    public get performancePriority() {\r\n        return this._performancePriority;\r\n    }\r\n\r\n    public set performancePriority(value) {\r\n        if (value === this._performancePriority) {\r\n            return;\r\n        }\r\n\r\n        this._performancePriority = value;\r\n\r\n        switch (value) {\r\n            case ScenePerformancePriority.BackwardCompatible:\r\n                this.skipFrustumClipping = false;\r\n                this._renderingManager.maintainStateBetweenFrames = false;\r\n                this.skipPointerMovePicking = false;\r\n                this.autoClear = true;\r\n                break;\r\n            case ScenePerformancePriority.Intermediate:\r\n                this.skipFrustumClipping = false;\r\n                this._renderingManager.maintainStateBetweenFrames = false;\r\n                this.skipPointerMovePicking = true;\r\n                this.autoClear = false;\r\n                break;\r\n            case ScenePerformancePriority.Aggressive:\r\n                this.skipFrustumClipping = true;\r\n                this._renderingManager.maintainStateBetweenFrames = true;\r\n                this.skipPointerMovePicking = true;\r\n                this.autoClear = false;\r\n                break;\r\n        }\r\n\r\n        this.onScenePerformancePriorityChangedObservable.notifyObservers(value);\r\n    }\r\n\r\n    private _forceWireframe = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if all rendering must be done in wireframe\r\n     */\r\n    public set forceWireframe(value: boolean) {\r\n        if (this._forceWireframe === value) {\r\n            return;\r\n        }\r\n        this._forceWireframe = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get forceWireframe(): boolean {\r\n        return this._forceWireframe;\r\n    }\r\n\r\n    private _skipFrustumClipping = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if we should skip the frustum clipping part of the active meshes selection\r\n     */\r\n    public set skipFrustumClipping(value: boolean) {\r\n        if (this._skipFrustumClipping === value) {\r\n            return;\r\n        }\r\n        this._skipFrustumClipping = value;\r\n    }\r\n    public get skipFrustumClipping(): boolean {\r\n        return this._skipFrustumClipping;\r\n    }\r\n\r\n    private _forcePointsCloud = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if all rendering must be done in point cloud\r\n     */\r\n    public set forcePointsCloud(value: boolean) {\r\n        if (this._forcePointsCloud === value) {\r\n            return;\r\n        }\r\n        this._forcePointsCloud = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get forcePointsCloud(): boolean {\r\n        return this._forcePointsCloud;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 1\r\n     */\r\n    public clipPlane: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 2\r\n     */\r\n    public clipPlane2: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 3\r\n     */\r\n    public clipPlane3: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 4\r\n     */\r\n    public clipPlane4: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 5\r\n     */\r\n    public clipPlane5: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets or sets the active clipplane 6\r\n     */\r\n    public clipPlane6: Nullable<Plane>;\r\n\r\n    /**\r\n     * Gets the list of root nodes (ie. nodes with no parent)\r\n     */\r\n    public rootNodes: Node[] = [];\r\n\r\n    /** All of the cameras added to this scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n     */\r\n    public cameras: Camera[] = [];\r\n\r\n    /**\r\n     * All of the lights added to this scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n     */\r\n    public lights: Light[] = [];\r\n\r\n    /**\r\n     * All of the (abstract) meshes added to this scene\r\n     */\r\n    public meshes: AbstractMesh[] = [];\r\n\r\n    /**\r\n     * The list of skeletons added to the scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons\r\n     */\r\n    public skeletons: Skeleton[] = [];\r\n\r\n    /**\r\n     * All of the particle systems added to this scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/particles/particle_system/particle_system_intro\r\n     */\r\n    public particleSystems: IParticleSystem[] = [];\r\n\r\n    /**\r\n     * Gets the current delta time used by animation engine\r\n     */\r\n    deltaTime: number;\r\n\r\n    /**\r\n     * Gets a list of Animations associated with the scene\r\n     */\r\n    public animations: Animation[] = [];\r\n\r\n    /**\r\n     * All of the animation groups added to this scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/groupAnimations\r\n     */\r\n    public animationGroups: AnimationGroup[] = [];\r\n\r\n    /**\r\n     * All of the multi-materials added to this scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/multiMaterials\r\n     */\r\n    public multiMaterials: MultiMaterial[] = [];\r\n\r\n    /**\r\n     * All of the materials added to this scene\r\n     * In the context of a Scene, it is not supposed to be modified manually.\r\n     * Any addition or removal should be done using the addMaterial and removeMaterial Scene methods.\r\n     * Note also that the order of the Material within the array is not significant and might change.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction\r\n     */\r\n    public materials: Material[] = [];\r\n\r\n    /**\r\n     * The list of morph target managers added to the scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/dynamicMeshMorph\r\n     */\r\n    public morphTargetManagers: MorphTargetManager[] = [];\r\n\r\n    /**\r\n     * The list of geometries used in the scene.\r\n     */\r\n    public geometries: Geometry[] = [];\r\n\r\n    /**\r\n     * All of the transform nodes added to this scene\r\n     * In the context of a Scene, it is not supposed to be modified manually.\r\n     * Any addition or removal should be done using the addTransformNode and removeTransformNode Scene methods.\r\n     * Note also that the order of the TransformNode within the array is not significant and might change.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/transforms/parent_pivot/transform_node\r\n     */\r\n    public transformNodes: TransformNode[] = [];\r\n\r\n    /**\r\n     * ActionManagers available on the scene.\r\n     */\r\n    public actionManagers: AbstractActionManager[] = [];\r\n\r\n    /**\r\n     * Textures to keep.\r\n     */\r\n    public textures: BaseTexture[] = [];\r\n\r\n    /** @internal */\r\n    protected _environmentTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Texture used in all pbr material as the reflection texture.\r\n     * As in the majority of the scene they are the same (exception for multi room and so on),\r\n     * this is easier to reference from here than from all the materials.\r\n     */\r\n    public get environmentTexture(): Nullable<BaseTexture> {\r\n        return this._environmentTexture;\r\n    }\r\n    /**\r\n     * Texture used in all pbr material as the reflection texture.\r\n     * As in the majority of the scene they are the same (exception for multi room and so on),\r\n     * this is easier to set here than in all the materials.\r\n     */\r\n    public set environmentTexture(value: Nullable<BaseTexture>) {\r\n        if (this._environmentTexture === value) {\r\n            return;\r\n        }\r\n\r\n        this._environmentTexture = value;\r\n        this.onEnvironmentTextureChangedObservable.notifyObservers(value);\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    /**\r\n     * The list of postprocesses added to the scene\r\n     */\r\n    public postProcesses: PostProcess[] = [];\r\n\r\n    /**\r\n     * The list of effect layers (highlights/glow) added to the scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/highlightLayer\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/glowLayer\r\n     */\r\n    public effectLayers: Array<EffectLayer> = [];\r\n\r\n    /**\r\n     * The list of sounds used in the scene.\r\n     */\r\n    public sounds: Nullable<Array<Sound>> = null;\r\n\r\n    /**\r\n     * The list of layers (background and foreground) of the scene\r\n     */\r\n    public layers: Array<Layer> = [];\r\n\r\n    /**\r\n     * The list of lens flare system added to the scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/lenseFlare\r\n     */\r\n    public lensFlareSystems: Array<LensFlareSystem> = [];\r\n\r\n    /**\r\n     * The list of procedural textures added to the scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/proceduralTextures\r\n     */\r\n    public proceduralTextures: Array<ProceduralTexture> = [];\r\n\r\n    /**\r\n     * @returns all meshes, lights, cameras, transformNodes and bones\r\n     */\r\n    public getNodes(): Array<Node> {\r\n        let nodes: Node[] = [];\r\n        nodes = nodes.concat(this.meshes);\r\n        nodes = nodes.concat(this.lights);\r\n        nodes = nodes.concat(this.cameras);\r\n        nodes = nodes.concat(this.transformNodes); // dummies\r\n        for (const skeleton of this.skeletons) {\r\n            nodes = nodes.concat(skeleton.bones);\r\n        }\r\n        return nodes;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if animations are enabled\r\n     */\r\n    public animationsEnabled = true;\r\n\r\n    private _animationPropertiesOverride: Nullable<AnimationPropertiesOverride> = null;\r\n\r\n    /**\r\n     * Gets or sets the animation properties override\r\n     */\r\n    public get animationPropertiesOverride(): Nullable<AnimationPropertiesOverride> {\r\n        return this._animationPropertiesOverride;\r\n    }\r\n\r\n    public set animationPropertiesOverride(value: Nullable<AnimationPropertiesOverride>) {\r\n        this._animationPropertiesOverride = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if a constant deltatime has to be used\r\n     * This is mostly useful for testing purposes when you do not want the animations to scale with the framerate\r\n     */\r\n    public useConstantAnimationDeltaTime = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if the scene must keep the meshUnderPointer property updated\r\n     * Please note that it requires to run a ray cast through the scene on every frame\r\n     */\r\n    public constantlyUpdateMeshUnderPointer = false;\r\n\r\n    /**\r\n     * Defines the HTML cursor to use when hovering over interactive elements\r\n     */\r\n    public hoverCursor = \"pointer\";\r\n    /**\r\n     * Defines the HTML default cursor to use (empty by default)\r\n     */\r\n    public defaultCursor: string = \"\";\r\n    /**\r\n     * Defines whether cursors are handled by the scene.\r\n     */\r\n    public doNotHandleCursors = false;\r\n    /**\r\n     * This is used to call preventDefault() on pointer down\r\n     * in order to block unwanted artifacts like system double clicks\r\n     */\r\n    public preventDefaultOnPointerDown = true;\r\n\r\n    /**\r\n     * This is used to call preventDefault() on pointer up\r\n     * in order to block unwanted artifacts like system double clicks\r\n     */\r\n    public preventDefaultOnPointerUp = true;\r\n\r\n    // Metadata\r\n    /**\r\n     * Gets or sets user defined metadata\r\n     */\r\n    public metadata: any = null;\r\n\r\n    /**\r\n     * For internal use only. Please do not use.\r\n     */\r\n    public reservedDataStore: any = null;\r\n\r\n    /**\r\n     * Gets the name of the plugin used to load this scene (null by default)\r\n     */\r\n    public loadingPluginName: string;\r\n\r\n    /**\r\n     * Use this array to add regular expressions used to disable offline support for specific urls\r\n     */\r\n    public disableOfflineSupportExceptionRules: RegExp[] = [];\r\n\r\n    /**\r\n     * An event triggered when the scene is disposed.\r\n     */\r\n    public onDisposeObservable = new Observable<Scene>();\r\n\r\n    private _onDisposeObserver: Nullable<Observer<Scene>> = null;\r\n    /** Sets a function to be executed when this scene is disposed. */\r\n    public set onDispose(callback: () => void) {\r\n        if (this._onDisposeObserver) {\r\n            this.onDisposeObservable.remove(this._onDisposeObserver);\r\n        }\r\n        this._onDisposeObserver = this.onDisposeObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered before rendering the scene (right after animations and physics)\r\n     */\r\n    public onBeforeRenderObservable = new Observable<Scene>();\r\n\r\n    private _onBeforeRenderObserver: Nullable<Observer<Scene>> = null;\r\n    /** Sets a function to be executed before rendering this scene */\r\n    public set beforeRender(callback: Nullable<() => void>) {\r\n        if (this._onBeforeRenderObserver) {\r\n            this.onBeforeRenderObservable.remove(this._onBeforeRenderObserver);\r\n        }\r\n        if (callback) {\r\n            this._onBeforeRenderObserver = this.onBeforeRenderObservable.add(callback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * An event triggered after rendering the scene\r\n     */\r\n    public onAfterRenderObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered after rendering the scene for an active camera (When scene.render is called this will be called after each camera)\r\n     * This is triggered for each \"sub\" camera in a Camera Rig unlike onAfterCameraRenderObservable\r\n     */\r\n    public onAfterRenderCameraObservable = new Observable<Camera>();\r\n\r\n    private _onAfterRenderObserver: Nullable<Observer<Scene>> = null;\r\n    /** Sets a function to be executed after rendering this scene */\r\n    public set afterRender(callback: Nullable<() => void>) {\r\n        if (this._onAfterRenderObserver) {\r\n            this.onAfterRenderObservable.remove(this._onAfterRenderObserver);\r\n        }\r\n\r\n        if (callback) {\r\n            this._onAfterRenderObserver = this.onAfterRenderObservable.add(callback);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * An event triggered before animating the scene\r\n     */\r\n    public onBeforeAnimationsObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered after animations processing\r\n     */\r\n    public onAfterAnimationsObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered before draw calls are ready to be sent\r\n     */\r\n    public onBeforeDrawPhaseObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered after draw calls have been sent\r\n     */\r\n    public onAfterDrawPhaseObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when the scene is ready\r\n     */\r\n    public onReadyObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered before rendering a camera\r\n     */\r\n    public onBeforeCameraRenderObservable = new Observable<Camera>();\r\n\r\n    private _onBeforeCameraRenderObserver: Nullable<Observer<Camera>> = null;\r\n    /** Sets a function to be executed before rendering a camera*/\r\n    public set beforeCameraRender(callback: () => void) {\r\n        if (this._onBeforeCameraRenderObserver) {\r\n            this.onBeforeCameraRenderObservable.remove(this._onBeforeCameraRenderObserver);\r\n        }\r\n\r\n        this._onBeforeCameraRenderObserver = this.onBeforeCameraRenderObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered after rendering a camera\r\n     * This is triggered for the full rig Camera only unlike onAfterRenderCameraObservable\r\n     */\r\n    public onAfterCameraRenderObservable = new Observable<Camera>();\r\n\r\n    private _onAfterCameraRenderObserver: Nullable<Observer<Camera>> = null;\r\n    /** Sets a function to be executed after rendering a camera*/\r\n    public set afterCameraRender(callback: () => void) {\r\n        if (this._onAfterCameraRenderObserver) {\r\n            this.onAfterCameraRenderObservable.remove(this._onAfterCameraRenderObserver);\r\n        }\r\n        this._onAfterCameraRenderObserver = this.onAfterCameraRenderObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered when active meshes evaluation is about to start\r\n     */\r\n    public onBeforeActiveMeshesEvaluationObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when active meshes evaluation is done\r\n     */\r\n    public onAfterActiveMeshesEvaluationObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when particles rendering is about to start\r\n     * Note: This event can be trigger more than once per frame (because particles can be rendered by render target textures as well)\r\n     */\r\n    public onBeforeParticlesRenderingObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when particles rendering is done\r\n     * Note: This event can be trigger more than once per frame (because particles can be rendered by render target textures as well)\r\n     */\r\n    public onAfterParticlesRenderingObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when SceneLoader.Append or SceneLoader.Load or SceneLoader.ImportMesh were successfully executed\r\n     */\r\n    public onDataLoadedObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when a camera is created\r\n     */\r\n    public onNewCameraAddedObservable = new Observable<Camera>();\r\n\r\n    /**\r\n     * An event triggered when a camera is removed\r\n     */\r\n    public onCameraRemovedObservable = new Observable<Camera>();\r\n\r\n    /**\r\n     * An event triggered when a light is created\r\n     */\r\n    public onNewLightAddedObservable = new Observable<Light>();\r\n\r\n    /**\r\n     * An event triggered when a light is removed\r\n     */\r\n    public onLightRemovedObservable = new Observable<Light>();\r\n\r\n    /**\r\n     * An event triggered when a geometry is created\r\n     */\r\n    public onNewGeometryAddedObservable = new Observable<Geometry>();\r\n\r\n    /**\r\n     * An event triggered when a geometry is removed\r\n     */\r\n    public onGeometryRemovedObservable = new Observable<Geometry>();\r\n\r\n    /**\r\n     * An event triggered when a transform node is created\r\n     */\r\n    public onNewTransformNodeAddedObservable = new Observable<TransformNode>();\r\n\r\n    /**\r\n     * An event triggered when a transform node is removed\r\n     */\r\n    public onTransformNodeRemovedObservable = new Observable<TransformNode>();\r\n\r\n    /**\r\n     * An event triggered when a mesh is created\r\n     */\r\n    public onNewMeshAddedObservable = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * An event triggered when a mesh is removed\r\n     */\r\n    public onMeshRemovedObservable = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * An event triggered when a skeleton is created\r\n     */\r\n    public onNewSkeletonAddedObservable = new Observable<Skeleton>();\r\n\r\n    /**\r\n     * An event triggered when a skeleton is removed\r\n     */\r\n    public onSkeletonRemovedObservable = new Observable<Skeleton>();\r\n\r\n    /**\r\n     * An event triggered when a particle system is created\r\n     */\r\n    public onNewParticleSystemAddedObservable = new Observable<IParticleSystem>();\r\n\r\n    /**\r\n     * An event triggered when a particle system is removed\r\n     */\r\n    public onParticleSystemRemovedObservable = new Observable<IParticleSystem>();\r\n\r\n    /**\r\n     * An event triggered when an animation group is created\r\n     */\r\n    public onNewAnimationGroupAddedObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * An event triggered when an animation group is removed\r\n     */\r\n    public onAnimationGroupRemovedObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * An event triggered when a material is created\r\n     */\r\n    public onNewMaterialAddedObservable = new Observable<Material>();\r\n\r\n    /**\r\n     * An event triggered when a multi material is created\r\n     */\r\n    public onNewMultiMaterialAddedObservable = new Observable<MultiMaterial>();\r\n\r\n    /**\r\n     * An event triggered when a material is removed\r\n     */\r\n    public onMaterialRemovedObservable = new Observable<Material>();\r\n\r\n    /**\r\n     * An event triggered when a multi material is removed\r\n     */\r\n    public onMultiMaterialRemovedObservable = new Observable<MultiMaterial>();\r\n\r\n    /**\r\n     * An event triggered when a texture is created\r\n     */\r\n    public onNewTextureAddedObservable = new Observable<BaseTexture>();\r\n\r\n    /**\r\n     * An event triggered when a texture is removed\r\n     */\r\n    public onTextureRemovedObservable = new Observable<BaseTexture>();\r\n\r\n    /**\r\n     * An event triggered when a frame graph is created\r\n     */\r\n    public onNewFrameGraphAddedObservable = new Observable<FrameGraph>();\r\n\r\n    /**\r\n     * An event triggered when a frame graph is removed\r\n     */\r\n    public onFrameGraphRemovedObservable = new Observable<FrameGraph>();\r\n\r\n    /**\r\n     * An event triggered when a post process is created\r\n     */\r\n    public onNewPostProcessAddedObservable = new Observable<PostProcess>();\r\n\r\n    /**\r\n     * An event triggered when a post process is removed\r\n     */\r\n    public onPostProcessRemovedObservable = new Observable<PostProcess>();\r\n\r\n    /**\r\n     * An event triggered when an effect layer is created\r\n     */\r\n    public onNewEffectLayerAddedObservable = new Observable<EffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when an effect layer is removed\r\n     */\r\n    public onEffectLayerRemovedObservable = new Observable<EffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when render targets are about to be rendered\r\n     * Can happen multiple times per frame.\r\n     */\r\n    public onBeforeRenderTargetsRenderObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when render targets were rendered.\r\n     * Can happen multiple times per frame.\r\n     */\r\n    public onAfterRenderTargetsRenderObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered before calculating deterministic simulation step\r\n     */\r\n    public onBeforeStepObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered after calculating deterministic simulation step\r\n     */\r\n    public onAfterStepObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when the activeCamera property is updated\r\n     */\r\n    public onActiveCameraChanged = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when the activeCameras property is updated\r\n     */\r\n    public onActiveCamerasChanged = new Observable<Scene>();\r\n\r\n    /**\r\n     * This Observable will be triggered before rendering each renderingGroup of each rendered camera.\r\n     * The RenderingGroupInfo class contains all the information about the context in which the observable is called\r\n     * If you wish to register an Observer only for a given set of renderingGroup, use the mask with a combination of the renderingGroup index elevated to the power of two (1 for renderingGroup 0, 2 for renderingrOup1, 4 for 2 and 8 for 3)\r\n     */\r\n    public onBeforeRenderingGroupObservable = new Observable<RenderingGroupInfo>();\r\n\r\n    /**\r\n     * This Observable will be triggered after rendering each renderingGroup of each rendered camera.\r\n     * The RenderingGroupInfo class contains all the information about the context in which the observable is called\r\n     * If you wish to register an Observer only for a given set of renderingGroup, use the mask with a combination of the renderingGroup index elevated to the power of two (1 for renderingGroup 0, 2 for renderingrOup1, 4 for 2 and 8 for 3)\r\n     */\r\n    public onAfterRenderingGroupObservable = new Observable<RenderingGroupInfo>();\r\n\r\n    /**\r\n     * This Observable will when a mesh has been imported into the scene.\r\n     */\r\n    public onMeshImportedObservable = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * This Observable will when an animation file has been imported into the scene.\r\n     */\r\n    public onAnimationFileImportedObservable = new Observable<Scene>();\r\n\r\n    /**\r\n     * An event triggered when the environmentTexture is changed.\r\n     */\r\n    public onEnvironmentTextureChangedObservable = new Observable<Nullable<BaseTexture>>();\r\n\r\n    /**\r\n     * An event triggered when the state of mesh under pointer, for a specific pointerId, changes.\r\n     */\r\n    public onMeshUnderPointerUpdatedObservable = new Observable<{ mesh: Nullable<AbstractMesh>; pointerId: number }>();\r\n\r\n    /**\r\n     * Gets or sets a user defined funtion to select LOD from a mesh and a camera.\r\n     * By default this function is undefined and Babylon.js will select LOD based on distance to camera\r\n     */\r\n    public customLODSelector: (mesh: AbstractMesh, camera: Camera) => Nullable<AbstractMesh>;\r\n\r\n    // Animations\r\n\r\n    /** @internal */\r\n    public _registeredForLateAnimationBindings = new SmartArrayNoDuplicate<any>(256);\r\n\r\n    // Pointers\r\n    private _pointerPickingConfiguration = new PointerPickingConfiguration();\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer down event\r\n     */\r\n    public get pointerDownPredicate() {\r\n        return this._pointerPickingConfiguration.pointerDownPredicate;\r\n    }\r\n\r\n    public set pointerDownPredicate(value) {\r\n        this._pointerPickingConfiguration.pointerDownPredicate = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer up event\r\n     */\r\n    public get pointerUpPredicate() {\r\n        return this._pointerPickingConfiguration.pointerUpPredicate;\r\n    }\r\n\r\n    public set pointerUpPredicate(value) {\r\n        this._pointerPickingConfiguration.pointerUpPredicate = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer move event\r\n     */\r\n    public get pointerMovePredicate() {\r\n        return this._pointerPickingConfiguration.pointerMovePredicate;\r\n    }\r\n\r\n    public set pointerMovePredicate(value) {\r\n        this._pointerPickingConfiguration.pointerMovePredicate = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer down event\r\n     */\r\n    public get pointerDownFastCheck() {\r\n        return this._pointerPickingConfiguration.pointerDownFastCheck;\r\n    }\r\n\r\n    public set pointerDownFastCheck(value) {\r\n        this._pointerPickingConfiguration.pointerDownFastCheck = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer up event\r\n     */\r\n    public get pointerUpFastCheck() {\r\n        return this._pointerPickingConfiguration.pointerUpFastCheck;\r\n    }\r\n\r\n    public set pointerUpFastCheck(value) {\r\n        this._pointerPickingConfiguration.pointerUpFastCheck = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate meshes for a pointer move event\r\n     */\r\n    public get pointerMoveFastCheck() {\r\n        return this._pointerPickingConfiguration.pointerMoveFastCheck;\r\n    }\r\n\r\n    public set pointerMoveFastCheck(value) {\r\n        this._pointerPickingConfiguration.pointerMoveFastCheck = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the user want to entirely skip the picking phase when a pointer move event occurs.\r\n     */\r\n    public get skipPointerMovePicking() {\r\n        return this._pointerPickingConfiguration.skipPointerMovePicking;\r\n    }\r\n\r\n    public set skipPointerMovePicking(value) {\r\n        this._pointerPickingConfiguration.skipPointerMovePicking = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the user want to entirely skip the picking phase when a pointer down event occurs.\r\n     */\r\n    public get skipPointerDownPicking() {\r\n        return this._pointerPickingConfiguration.skipPointerDownPicking;\r\n    }\r\n\r\n    public set skipPointerDownPicking(value) {\r\n        this._pointerPickingConfiguration.skipPointerDownPicking = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the user want to entirely skip the picking phase when a pointer up event occurs.  Off by default.\r\n     */\r\n    public get skipPointerUpPicking() {\r\n        return this._pointerPickingConfiguration.skipPointerUpPicking;\r\n    }\r\n\r\n    public set skipPointerUpPicking(value) {\r\n        this._pointerPickingConfiguration.skipPointerUpPicking = value;\r\n    }\r\n\r\n    /** Callback called when a pointer move is detected */\r\n    public onPointerMove?: (evt: IPointerEvent, pickInfo: PickingInfo, type: PointerEventTypes) => void;\r\n    /** Callback called when a pointer down is detected  */\r\n    public onPointerDown?: (evt: IPointerEvent, pickInfo: PickingInfo, type: PointerEventTypes) => void;\r\n    /** Callback called when a pointer up is detected  */\r\n    public onPointerUp?: (evt: IPointerEvent, pickInfo: Nullable<PickingInfo>, type: PointerEventTypes) => void;\r\n    /** Callback called when a pointer pick is detected */\r\n    public onPointerPick?: (evt: IPointerEvent, pickInfo: PickingInfo) => void;\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate faces for a pointer move event\r\n     */\r\n    public pointerMoveTrianglePredicate: ((p0: Vector3, p1: Vector3, p2: Vector3, ray: Ray) => boolean) | undefined;\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate faces for a pointer down event\r\n     */\r\n    public pointerDownTrianglePredicate: ((p0: Vector3, p1: Vector3, p2: Vector3, ray: Ray) => boolean) | undefined;\r\n\r\n    /**\r\n     * Gets or sets a predicate used to select candidate faces for a pointer up event\r\n     */\r\n    public pointerUpTrianglePredicate: ((p0: Vector3, p1: Vector3, p2: Vector3, ray: Ray) => boolean) | undefined;\r\n\r\n    /**\r\n     * This observable event is triggered when any ponter event is triggered. It is registered during Scene.attachControl() and it is called BEFORE the 3D engine process anything (mesh/sprite picking for instance).\r\n     * You have the possibility to skip the process and the call to onPointerObservable by setting PointerInfoPre.skipOnPointerObservable to true\r\n     */\r\n    public onPrePointerObservable = new Observable<PointerInfoPre>();\r\n\r\n    /**\r\n     * Observable event triggered each time an input event is received from the rendering canvas\r\n     */\r\n    public onPointerObservable = new Observable<PointerInfo>();\r\n\r\n    /**\r\n     * Gets the pointer coordinates without any translation (ie. straight out of the pointer event)\r\n     */\r\n    public get unTranslatedPointer(): Vector2 {\r\n        return this._inputManager.unTranslatedPointer;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the distance in pixel that you have to move to prevent some events. Default is 10 pixels\r\n     */\r\n    public static get DragMovementThreshold() {\r\n        return InputManager.DragMovementThreshold;\r\n    }\r\n\r\n    public static set DragMovementThreshold(value: number) {\r\n        InputManager.DragMovementThreshold = value;\r\n    }\r\n\r\n    /**\r\n     * Time in milliseconds to wait to raise long press events if button is still pressed. Default is 500 ms\r\n     */\r\n    public static get LongPressDelay() {\r\n        return InputManager.LongPressDelay;\r\n    }\r\n\r\n    public static set LongPressDelay(value: number) {\r\n        InputManager.LongPressDelay = value;\r\n    }\r\n\r\n    /**\r\n     * Time in milliseconds to wait to raise long press events if button is still pressed. Default is 300 ms\r\n     */\r\n    public static get DoubleClickDelay() {\r\n        return InputManager.DoubleClickDelay;\r\n    }\r\n\r\n    public static set DoubleClickDelay(value: number) {\r\n        InputManager.DoubleClickDelay = value;\r\n    }\r\n\r\n    /** If you need to check double click without raising a single click at first click, enable this flag */\r\n    public static get ExclusiveDoubleClickMode() {\r\n        return InputManager.ExclusiveDoubleClickMode;\r\n    }\r\n\r\n    public static set ExclusiveDoubleClickMode(value: boolean) {\r\n        InputManager.ExclusiveDoubleClickMode = value;\r\n    }\r\n\r\n    /**\r\n     * Bind the current view position to an effect.\r\n     * @param effect The effect to be bound\r\n     * @param variableName name of the shader variable that will hold the eye position\r\n     * @param isVector3 true to indicates that variableName is a Vector3 and not a Vector4\r\n     * @returns the computed eye position\r\n     */\r\n    public bindEyePosition(effect: Nullable<Effect>, variableName = \"vEyePosition\", isVector3 = false): Vector4 {\r\n        const eyePosition = this._forcedViewPosition\r\n            ? this._forcedViewPosition\r\n            : this._mirroredCameraPosition\r\n              ? this._mirroredCameraPosition\r\n              : (this.activeCamera?.globalPosition ?? Vector3.ZeroReadOnly);\r\n\r\n        const invertNormal = this.useRightHandedSystem === (this._mirroredCameraPosition != null);\r\n\r\n        TmpVectors.Vector4[0].set(eyePosition.x, eyePosition.y, eyePosition.z, invertNormal ? -1 : 1);\r\n\r\n        if (effect) {\r\n            if (isVector3) {\r\n                effect.setFloat3(variableName, TmpVectors.Vector4[0].x, TmpVectors.Vector4[0].y, TmpVectors.Vector4[0].z);\r\n            } else {\r\n                effect.setVector4(variableName, TmpVectors.Vector4[0]);\r\n            }\r\n        }\r\n\r\n        return TmpVectors.Vector4[0];\r\n    }\r\n\r\n    /**\r\n     * Update the scene ubo before it can be used in rendering processing\r\n     * @returns the scene UniformBuffer\r\n     */\r\n    public finalizeSceneUbo(): UniformBuffer {\r\n        const ubo = this.getSceneUniformBuffer();\r\n        const eyePosition = this.bindEyePosition(null);\r\n        ubo.updateFloat4(\"vEyePosition\", eyePosition.x, eyePosition.y, eyePosition.z, eyePosition.w);\r\n\r\n        ubo.update();\r\n\r\n        return ubo;\r\n    }\r\n\r\n    // Mirror\r\n    /** @internal */\r\n    public _mirroredCameraPosition: Nullable<Vector3>;\r\n\r\n    // Keyboard\r\n\r\n    /**\r\n     * This observable event is triggered when any keyboard event si raised and registered during Scene.attachControl()\r\n     * You have the possibility to skip the process and the call to onKeyboardObservable by setting KeyboardInfoPre.skipOnPointerObservable to true\r\n     */\r\n    public onPreKeyboardObservable = new Observable<KeyboardInfoPre>();\r\n\r\n    /**\r\n     * Observable event triggered each time an keyboard event is received from the hosting window\r\n     */\r\n    public onKeyboardObservable = new Observable<KeyboardInfo>();\r\n\r\n    // Coordinates system\r\n\r\n    private _useRightHandedSystem = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if the scene must use right-handed coordinates system\r\n     */\r\n    public set useRightHandedSystem(value: boolean) {\r\n        if (this._useRightHandedSystem === value) {\r\n            return;\r\n        }\r\n        this._useRightHandedSystem = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get useRightHandedSystem(): boolean {\r\n        return this._useRightHandedSystem;\r\n    }\r\n\r\n    // Deterministic lockstep\r\n    private _timeAccumulator: number = 0;\r\n    private _currentStepId: number = 0;\r\n    private _currentInternalStep: number = 0;\r\n\r\n    /**\r\n     * Sets the step Id used by deterministic lock step\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     * @param newStepId defines the step Id\r\n     */\r\n    public setStepId(newStepId: number): void {\r\n        this._currentStepId = newStepId;\r\n    }\r\n\r\n    /**\r\n     * Gets the step Id used by deterministic lock step\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     * @returns the step Id\r\n     */\r\n    public getStepId(): number {\r\n        return this._currentStepId;\r\n    }\r\n\r\n    /**\r\n     * Gets the internal step used by deterministic lock step\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#deterministic-lockstep\r\n     * @returns the internal step\r\n     */\r\n    public getInternalStep(): number {\r\n        return this._currentInternalStep;\r\n    }\r\n\r\n    // Fog\r\n\r\n    private _fogEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if fog is enabled on this scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is true)\r\n     */\r\n    public set fogEnabled(value: boolean) {\r\n        if (this._fogEnabled === value) {\r\n            return;\r\n        }\r\n        this._fogEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get fogEnabled(): boolean {\r\n        return this._fogEnabled;\r\n    }\r\n\r\n    private _fogMode = Scene.FOGMODE_NONE;\r\n    /**\r\n     * Gets or sets the fog mode to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * | mode | value |\r\n     * | --- | --- |\r\n     * | FOGMODE_NONE | 0 |\r\n     * | FOGMODE_EXP | 1 |\r\n     * | FOGMODE_EXP2 | 2 |\r\n     * | FOGMODE_LINEAR | 3 |\r\n     */\r\n    public set fogMode(value: number) {\r\n        if (this._fogMode === value) {\r\n            return;\r\n        }\r\n        this._fogMode = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_MiscDirtyFlag);\r\n    }\r\n    public get fogMode(): number {\r\n        return this._fogMode;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the fog color to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is Color3(0.2, 0.2, 0.3))\r\n     */\r\n    public fogColor = new Color3(0.2, 0.2, 0.3);\r\n    /**\r\n     * Gets or sets the fog density to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is 0.1)\r\n     */\r\n    public fogDensity = 0.1;\r\n    /**\r\n     * Gets or sets the fog start distance to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is 0)\r\n     */\r\n    public fogStart = 0;\r\n    /**\r\n     * Gets or sets the fog end distance to use\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/environment/environment_introduction#fog\r\n     * (Default is 1000)\r\n     */\r\n    public fogEnd = 1000.0;\r\n\r\n    /**\r\n     * Flag indicating that the frame buffer binding is handled by another component\r\n     */\r\n    public get prePass(): boolean {\r\n        return !!this.prePassRenderer && this.prePassRenderer.defaultRT.enabled;\r\n    }\r\n\r\n    /**\r\n     * Flag indicating if we need to store previous matrices when rendering\r\n     */\r\n    public needsPreviousWorldMatrices = false;\r\n\r\n    // Lights\r\n    private _shadowsEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if shadows are enabled on this scene\r\n     */\r\n    public set shadowsEnabled(value: boolean) {\r\n        if (this._shadowsEnabled === value) {\r\n            return;\r\n        }\r\n        this._shadowsEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_LightDirtyFlag);\r\n    }\r\n    public get shadowsEnabled(): boolean {\r\n        return this._shadowsEnabled;\r\n    }\r\n\r\n    private _lightsEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if lights are enabled on this scene\r\n     */\r\n    public set lightsEnabled(value: boolean) {\r\n        if (this._lightsEnabled === value) {\r\n            return;\r\n        }\r\n        this._lightsEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_LightDirtyFlag);\r\n    }\r\n\r\n    public get lightsEnabled(): boolean {\r\n        return this._lightsEnabled;\r\n    }\r\n\r\n    private _activeCameras: Nullable<Camera[]>;\r\n    private _unObserveActiveCameras: Nullable<() => void> = null;\r\n\r\n    /** All of the active cameras added to this scene. */\r\n    public get activeCameras(): Nullable<Camera[]> {\r\n        return this._activeCameras;\r\n    }\r\n\r\n    public set activeCameras(cameras: Nullable<Camera[]>) {\r\n        if (this._unObserveActiveCameras) {\r\n            this._unObserveActiveCameras();\r\n            this._unObserveActiveCameras = null;\r\n        }\r\n\r\n        if (cameras) {\r\n            this._unObserveActiveCameras = _ObserveArray(cameras, () => {\r\n                this.onActiveCamerasChanged.notifyObservers(this);\r\n            });\r\n        }\r\n\r\n        this._activeCameras = cameras;\r\n    }\r\n\r\n    /** @internal */\r\n    public _activeCamera: Nullable<Camera>;\r\n    /** Gets or sets the current active camera */\r\n    public get activeCamera(): Nullable<Camera> {\r\n        return this._activeCamera;\r\n    }\r\n\r\n    public set activeCamera(value: Nullable<Camera>) {\r\n        if (value === this._activeCamera) {\r\n            return;\r\n        }\r\n\r\n        this._activeCamera = value;\r\n        this.onActiveCameraChanged.notifyObservers(this);\r\n    }\r\n\r\n    /** @internal */\r\n    public get _hasDefaultMaterial() {\r\n        return Scene.DefaultMaterialFactory !== Scene._OriginalDefaultMaterialFactory;\r\n    }\r\n\r\n    private _defaultMaterial: Material;\r\n\r\n    /** The default material used on meshes when no material is affected */\r\n    public get defaultMaterial(): Material {\r\n        if (!this._defaultMaterial) {\r\n            this._defaultMaterial = Scene.DefaultMaterialFactory(this);\r\n        }\r\n\r\n        return this._defaultMaterial;\r\n    }\r\n\r\n    /** The default material used on meshes when no material is affected */\r\n    public set defaultMaterial(value: Material) {\r\n        this._defaultMaterial = value;\r\n    }\r\n\r\n    // Textures\r\n    private _texturesEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if textures are enabled on this scene\r\n     */\r\n    public set texturesEnabled(value: boolean) {\r\n        if (this._texturesEnabled === value) {\r\n            return;\r\n        }\r\n        this._texturesEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    public get texturesEnabled(): boolean {\r\n        return this._texturesEnabled;\r\n    }\r\n\r\n    private _frameGraph: Nullable<FrameGraph> = null;\r\n    private _currentCustomRenderFunction?: (updateCameras: boolean, ignoreAnimations: boolean) => void;\r\n    /**\r\n     * Gets or sets the frame graph used to render the scene. If set, the scene will use the frame graph to render the scene instead of the default render loop.\r\n     */\r\n    public get frameGraph() {\r\n        return this._frameGraph;\r\n    }\r\n\r\n    public set frameGraph(value: Nullable<FrameGraph>) {\r\n        if (this._frameGraph) {\r\n            this._frameGraph = value;\r\n            if (!value) {\r\n                this.customRenderFunction = this._currentCustomRenderFunction;\r\n            }\r\n            return;\r\n        }\r\n\r\n        this._frameGraph = value;\r\n        if (value) {\r\n            this._currentCustomRenderFunction = this.customRenderFunction;\r\n            this.customRenderFunction = this._renderWithFrameGraph;\r\n            this.activeCamera = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * List of frame graphs associated with the scene\r\n     */\r\n    public frameGraphs: FrameGraph[] = [];\r\n\r\n    // Physics\r\n    /**\r\n     * Gets or sets a boolean indicating if physic engines are enabled on this scene\r\n     */\r\n    public physicsEnabled = true;\r\n\r\n    // Particles\r\n    /**\r\n     * Gets or sets a boolean indicating if particles are enabled on this scene\r\n     */\r\n    public particlesEnabled = true;\r\n\r\n    // Sprites\r\n    /**\r\n     * Gets or sets a boolean indicating if sprites are enabled on this scene\r\n     */\r\n    public spritesEnabled = true;\r\n\r\n    // Skeletons\r\n    private _skeletonsEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if skeletons are enabled on this scene\r\n     */\r\n    public set skeletonsEnabled(value: boolean) {\r\n        if (this._skeletonsEnabled === value) {\r\n            return;\r\n        }\r\n        this._skeletonsEnabled = value;\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_AttributesDirtyFlag);\r\n    }\r\n\r\n    public get skeletonsEnabled(): boolean {\r\n        return this._skeletonsEnabled;\r\n    }\r\n\r\n    // Lens flares\r\n    /**\r\n     * Gets or sets a boolean indicating if lens flares are enabled on this scene\r\n     */\r\n    public lensFlaresEnabled = true;\r\n\r\n    // Collisions\r\n    /**\r\n     * Gets or sets a boolean indicating if collisions are enabled on this scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions\r\n     */\r\n    public collisionsEnabled = true;\r\n\r\n    private _collisionCoordinator: ICollisionCoordinator;\r\n\r\n    /** @internal */\r\n    public get collisionCoordinator(): ICollisionCoordinator {\r\n        if (!this._collisionCoordinator) {\r\n            this._collisionCoordinator = Scene.CollisionCoordinatorFactory();\r\n            this._collisionCoordinator.init(this);\r\n        }\r\n\r\n        return this._collisionCoordinator;\r\n    }\r\n\r\n    /**\r\n     * Defines the gravity applied to this scene (used only for collisions)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions\r\n     */\r\n    public gravity = new Vector3(0, -9.807, 0);\r\n\r\n    // Postprocesses\r\n    /**\r\n     * Gets or sets a boolean indicating if postprocesses are enabled on this scene\r\n     */\r\n    public postProcessesEnabled = true;\r\n    /**\r\n     * Gets the current postprocess manager\r\n     */\r\n    public postProcessManager: PostProcessManager;\r\n\r\n    // Customs render targets\r\n    /**\r\n     * Gets or sets a boolean indicating if render targets are enabled on this scene\r\n     */\r\n    public renderTargetsEnabled = true;\r\n    /**\r\n     * Gets or sets a boolean indicating if next render targets must be dumped as image for debugging purposes\r\n     * We recommend not using it and instead rely on Spector.js: http://spector.babylonjs.com\r\n     */\r\n    public dumpNextRenderTargets = false;\r\n    /**\r\n     * The list of user defined render targets added to the scene\r\n     */\r\n    public customRenderTargets: RenderTargetTexture[] = [];\r\n\r\n    /**\r\n     * Defines if texture loading must be delayed\r\n     * If true, textures will only be loaded when they need to be rendered\r\n     */\r\n    public useDelayedTextureLoading: boolean;\r\n\r\n    /**\r\n     * Gets the list of meshes imported to the scene through SceneLoader\r\n     */\r\n    public importedMeshesFiles: string[] = [];\r\n\r\n    // Probes\r\n    /**\r\n     * Gets or sets a boolean indicating if probes are enabled on this scene\r\n     */\r\n    public probesEnabled = true;\r\n\r\n    // Offline support\r\n    /**\r\n     * Gets or sets the current offline provider to use to store scene data\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeCached\r\n     */\r\n    public offlineProvider: IOfflineProvider;\r\n\r\n    /**\r\n     * Gets or sets the action manager associated with the scene\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n     */\r\n    public actionManager: AbstractActionManager;\r\n\r\n    private _meshesForIntersections = new SmartArrayNoDuplicate<AbstractMesh>(256);\r\n\r\n    // Procedural textures\r\n    /**\r\n     * Gets or sets a boolean indicating if procedural textures are enabled on this scene\r\n     */\r\n    public proceduralTexturesEnabled = true;\r\n\r\n    // Private\r\n    private _engine: AbstractEngine;\r\n\r\n    // Performance counters\r\n    private _totalVertices = new PerfCounter();\r\n    /** @internal */\r\n    public _activeIndices = new PerfCounter();\r\n    /** @internal */\r\n    public _activeParticles = new PerfCounter();\r\n    /** @internal */\r\n    public _activeBones = new PerfCounter();\r\n\r\n    private _animationRatio: number;\r\n\r\n    /** @internal */\r\n    public _animationTimeLast: number;\r\n\r\n    /** @internal */\r\n    public _animationTime: number = 0;\r\n\r\n    /**\r\n     * Gets or sets a general scale for animation speed\r\n     * @see https://www.babylonjs-playground.com/#IBU2W7#3\r\n     */\r\n    public animationTimeScale: number = 1;\r\n\r\n    /** @internal */\r\n    public _cachedMaterial: Nullable<Material>;\r\n    /** @internal */\r\n    public _cachedEffect: Nullable<Effect>;\r\n    /** @internal */\r\n    public _cachedVisibility: Nullable<number>;\r\n\r\n    private _renderId = 0;\r\n    private _frameId = 0;\r\n    private _executeWhenReadyTimeoutId: Nullable<ReturnType<typeof setTimeout>> = null;\r\n    /** @internal */\r\n    public _intermediateRendering = false;\r\n    private _defaultFrameBufferCleared = false;\r\n\r\n    private _viewUpdateFlag = -1;\r\n    private _projectionUpdateFlag = -1;\r\n\r\n    /** @internal */\r\n    public _toBeDisposed = new Array<Nullable<IDisposable>>(256);\r\n    private _activeRequests = new Array<IFileRequest>();\r\n\r\n    /** @internal */\r\n    public _pendingData = [] as any[];\r\n    private _isDisposed = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that all submeshes of active meshes must be rendered\r\n     * Use this boolean to avoid computing frustum clipping on submeshes (This could help when you are CPU bound)\r\n     */\r\n    public dispatchAllSubMeshesOfActiveMeshes: boolean = false;\r\n    private _activeMeshes = new SmartArray<AbstractMesh>(256);\r\n    private _processedMaterials = new SmartArray<Material>(256);\r\n    private _renderTargets = new SmartArrayNoDuplicate<RenderTargetTexture>(256);\r\n    private _materialsRenderTargets = new SmartArrayNoDuplicate<RenderTargetTexture>(256);\r\n    /** @internal */\r\n    public _activeParticleSystems = new SmartArray<IParticleSystem>(256);\r\n    private _activeSkeletons = new SmartArrayNoDuplicate<Skeleton>(32);\r\n    private _softwareSkinnedMeshes = new SmartArrayNoDuplicate<Mesh>(32);\r\n\r\n    private _renderingManager: RenderingManager;\r\n\r\n    /**\r\n     * Gets the scene's rendering manager\r\n     */\r\n    public get renderingManager(): RenderingManager {\r\n        return this._renderingManager;\r\n    }\r\n\r\n    /** @internal */\r\n    public _activeAnimatables = new Array<Animatable>();\r\n\r\n    private _transformMatrix = Matrix.Zero();\r\n    private _sceneUbo: UniformBuffer;\r\n\r\n    /** @internal */\r\n    public _viewMatrix: Matrix;\r\n    /** @internal */\r\n    public _projectionMatrix: Matrix;\r\n    /** @internal */\r\n    public _forcedViewPosition: Nullable<Vector3>;\r\n\r\n    /** @internal */\r\n    public _frustumPlanes: Plane[];\r\n    /**\r\n     * Gets the list of frustum planes (built from the active camera)\r\n     */\r\n    public get frustumPlanes(): Plane[] {\r\n        return this._frustumPlanes;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if lights must be sorted by priority (off by default)\r\n     * This is useful if there are more lights that the maximum simulteanous authorized\r\n     */\r\n    public requireLightSorting = false;\r\n\r\n    /** @internal */\r\n    public readonly useMaterialMeshMap: boolean;\r\n    /** @internal */\r\n    public readonly useClonedMeshMap: boolean;\r\n\r\n    private _externalData: StringDictionary<object>;\r\n    private _uid: Nullable<string>;\r\n\r\n    /**\r\n     * @internal\r\n     * Backing store of defined scene components.\r\n     */\r\n    public _components: ISceneComponent[] = [];\r\n\r\n    /**\r\n     * @internal\r\n     * Backing store of defined scene components.\r\n     */\r\n    public _serializableComponents: ISceneSerializableComponent[] = [];\r\n\r\n    /**\r\n     * List of components to register on the next registration step.\r\n     */\r\n    private _transientComponents: ISceneComponent[] = [];\r\n\r\n    /**\r\n     * Registers the transient components if needed.\r\n     */\r\n    private _registerTransientComponents(): void {\r\n        // Register components that have been associated lately to the scene.\r\n        if (this._transientComponents.length > 0) {\r\n            for (const component of this._transientComponents) {\r\n                component.register();\r\n            }\r\n            this._transientComponents.length = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Add a component to the scene.\r\n     * Note that the ccomponent could be registered on th next frame if this is called after\r\n     * the register component stage.\r\n     * @param component Defines the component to add to the scene\r\n     */\r\n    public _addComponent(component: ISceneComponent) {\r\n        this._components.push(component);\r\n        this._transientComponents.push(component);\r\n\r\n        const serializableComponent = component as any;\r\n        if (serializableComponent.addFromContainer && serializableComponent.serialize) {\r\n            this._serializableComponents.push(serializableComponent);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Gets a component from the scene.\r\n     * @param name defines the name of the component to retrieve\r\n     * @returns the component or null if not present\r\n     */\r\n    public _getComponent(name: string): Nullable<ISceneComponent> {\r\n        for (const component of this._components) {\r\n            if (component.name === name) {\r\n                return component;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening before camera updates.\r\n     */\r\n    public _beforeCameraUpdateStage = Stage.Create<SimpleStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening before clear the canvas.\r\n     */\r\n    public _beforeClearStage = Stage.Create<SimpleStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening before clear the canvas.\r\n     */\r\n    public _beforeRenderTargetClearStage = Stage.Create<RenderTargetStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions when collecting render targets for the frame.\r\n     */\r\n    public _gatherRenderTargetsStage = Stage.Create<RenderTargetsStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening for one camera in the frame.\r\n     */\r\n    public _gatherActiveCameraRenderTargetsStage = Stage.Create<RenderTargetsStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening during the per mesh ready checks.\r\n     */\r\n    public _isReadyForMeshStage = Stage.Create<MeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening before evaluate active mesh checks.\r\n     */\r\n    public _beforeEvaluateActiveMeshStage = Stage.Create<SimpleStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening during the evaluate sub mesh checks.\r\n     */\r\n    public _evaluateSubMeshStage = Stage.Create<EvaluateSubMeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening during the active mesh stage.\r\n     */\r\n    public _preActiveMeshStage = Stage.Create<PreActiveMeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening during the per camera render target step.\r\n     */\r\n    public _cameraDrawRenderTargetStage = Stage.Create<CameraStageFrameBufferAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just before the active camera is drawing.\r\n     */\r\n    public _beforeCameraDrawStage = Stage.Create<CameraStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just before a render target is drawing.\r\n     */\r\n    public _beforeRenderTargetDrawStage = Stage.Create<RenderTargetStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just before a rendering group is drawing.\r\n     */\r\n    public _beforeRenderingGroupDrawStage = Stage.Create<RenderingGroupStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just before a mesh is drawing.\r\n     */\r\n    public _beforeRenderingMeshStage = Stage.Create<RenderingMeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after a mesh has been drawn.\r\n     */\r\n    public _afterRenderingMeshStage = Stage.Create<RenderingMeshStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after a rendering group has been drawn.\r\n     */\r\n    public _afterRenderingGroupDrawStage = Stage.Create<RenderingGroupStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after the active camera has been drawn.\r\n     */\r\n    public _afterCameraDrawStage = Stage.Create<CameraStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after the post processing\r\n     */\r\n    public _afterCameraPostProcessStage = Stage.Create<CameraStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after a render target has been drawn.\r\n     */\r\n    public _afterRenderTargetDrawStage = Stage.Create<RenderTargetStageAction>();\r\n    /**\r\n     * Defines the actions happening just after the post processing on a render target\r\n     */\r\n    public _afterRenderTargetPostProcessStage = Stage.Create<RenderTargetStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening just after rendering all cameras and computing intersections.\r\n     */\r\n    public _afterRenderStage = Stage.Create<SimpleStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening when a pointer move event happens.\r\n     */\r\n    public _pointerMoveStage = Stage.Create<PointerMoveStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening when a pointer down event happens.\r\n     */\r\n    public _pointerDownStage = Stage.Create<PointerUpDownStageAction>();\r\n    /**\r\n     * @internal\r\n     * Defines the actions happening when a pointer up event happens.\r\n     */\r\n    public _pointerUpStage = Stage.Create<PointerUpDownStageAction>();\r\n\r\n    /**\r\n     * an optional map from Geometry Id to Geometry index in the 'geometries' array\r\n     */\r\n    private _geometriesByUniqueId: Nullable<{ [uniqueId: string]: number | undefined }> = null;\r\n\r\n    private _uniqueId = 0;\r\n\r\n    /**\r\n     * Gets the unique id of the scene\r\n     */\r\n    public get uniqueId() {\r\n        return this._uniqueId;\r\n    }\r\n\r\n    /**\r\n     * Creates a new Scene\r\n     * @param engine defines the engine to use to render this scene\r\n     * @param options defines the scene options\r\n     */\r\n    constructor(engine: AbstractEngine, options?: SceneOptions) {\r\n        this.activeCameras = [] as Camera[];\r\n\r\n        this._uniqueId = this.getUniqueId();\r\n\r\n        const fullOptions = {\r\n            useGeometryUniqueIdsMap: true,\r\n            useMaterialMeshMap: true,\r\n            useClonedMeshMap: true,\r\n            virtual: false,\r\n            ...options,\r\n        };\r\n\r\n        engine = this._engine = engine || EngineStore.LastCreatedEngine;\r\n        if (fullOptions.virtual) {\r\n            engine._virtualScenes.push(this);\r\n        } else {\r\n            EngineStore._LastCreatedScene = this;\r\n            engine.scenes.push(this);\r\n        }\r\n\r\n        this._uid = null;\r\n\r\n        this._renderingManager = new RenderingManager(this);\r\n\r\n        if (PostProcessManager) {\r\n            this.postProcessManager = new PostProcessManager(this);\r\n        }\r\n\r\n        if (IsWindowObjectExist()) {\r\n            this.attachControl();\r\n        }\r\n\r\n        // Uniform Buffer\r\n        this._createUbo();\r\n\r\n        // Default Image processing definition\r\n        if (ImageProcessingConfiguration) {\r\n            this._imageProcessingConfiguration = new ImageProcessingConfiguration();\r\n        }\r\n\r\n        this.setDefaultCandidateProviders();\r\n\r\n        if (fullOptions.useGeometryUniqueIdsMap) {\r\n            this._geometriesByUniqueId = {};\r\n        }\r\n\r\n        this.useMaterialMeshMap = fullOptions.useMaterialMeshMap;\r\n        this.useClonedMeshMap = fullOptions.useClonedMeshMap;\r\n\r\n        if (!options || !options.virtual) {\r\n            engine.onNewSceneAddedObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"Scene\" string\r\n     */\r\n    public getClassName(): string {\r\n        return \"Scene\";\r\n    }\r\n\r\n    private _defaultMeshCandidates: ISmartArrayLike<AbstractMesh> = {\r\n        data: [],\r\n        length: 0,\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getDefaultMeshCandidates(): ISmartArrayLike<AbstractMesh> {\r\n        this._defaultMeshCandidates.data = this.meshes;\r\n        this._defaultMeshCandidates.length = this.meshes.length;\r\n        return this._defaultMeshCandidates;\r\n    }\r\n\r\n    private _defaultSubMeshCandidates: ISmartArrayLike<SubMesh> = {\r\n        data: [],\r\n        length: 0,\r\n    };\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getDefaultSubMeshCandidates(mesh: AbstractMesh): ISmartArrayLike<SubMesh> {\r\n        this._defaultSubMeshCandidates.data = mesh.subMeshes;\r\n        this._defaultSubMeshCandidates.length = mesh.subMeshes.length;\r\n        return this._defaultSubMeshCandidates;\r\n    }\r\n\r\n    /**\r\n     * Sets the default candidate providers for the scene.\r\n     * This sets the getActiveMeshCandidates, getActiveSubMeshCandidates, getIntersectingSubMeshCandidates\r\n     * and getCollidingSubMeshCandidates to their default function\r\n     */\r\n    public setDefaultCandidateProviders(): void {\r\n        this.getActiveMeshCandidates = () => this._getDefaultMeshCandidates();\r\n        this.getActiveSubMeshCandidates = (mesh: AbstractMesh) => this._getDefaultSubMeshCandidates(mesh);\r\n        this.getIntersectingSubMeshCandidates = (mesh: AbstractMesh, localRay: Ray) => this._getDefaultSubMeshCandidates(mesh);\r\n        this.getCollidingSubMeshCandidates = (mesh: AbstractMesh, collider: Collider) => this._getDefaultSubMeshCandidates(mesh);\r\n    }\r\n\r\n    /**\r\n     * Gets the mesh that is currently under the pointer\r\n     */\r\n    public get meshUnderPointer(): Nullable<AbstractMesh> {\r\n        return this._inputManager.meshUnderPointer;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current on-screen X position of the pointer\r\n     */\r\n    public get pointerX(): number {\r\n        return this._inputManager.pointerX;\r\n    }\r\n\r\n    public set pointerX(value: number) {\r\n        this._inputManager.pointerX = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the current on-screen Y position of the pointer\r\n     */\r\n    public get pointerY(): number {\r\n        return this._inputManager.pointerY;\r\n    }\r\n\r\n    public set pointerY(value: number) {\r\n        this._inputManager.pointerY = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the cached material (ie. the latest rendered one)\r\n     * @returns the cached material\r\n     */\r\n    public getCachedMaterial(): Nullable<Material> {\r\n        return this._cachedMaterial;\r\n    }\r\n\r\n    /**\r\n     * Gets the cached effect (ie. the latest rendered one)\r\n     * @returns the cached effect\r\n     */\r\n    public getCachedEffect(): Nullable<Effect> {\r\n        return this._cachedEffect;\r\n    }\r\n\r\n    /**\r\n     * Gets the cached visibility state (ie. the latest rendered one)\r\n     * @returns the cached visibility state\r\n     */\r\n    public getCachedVisibility(): Nullable<number> {\r\n        return this._cachedVisibility;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the current material / effect / visibility must be bind again\r\n     * @param material defines the current material\r\n     * @param effect defines the current effect\r\n     * @param visibility defines the current visibility state\r\n     * @returns true if one parameter is not cached\r\n     */\r\n    public isCachedMaterialInvalid(material: Material, effect: Effect, visibility: number = 1) {\r\n        return this._cachedEffect !== effect || this._cachedMaterial !== material || this._cachedVisibility !== visibility;\r\n    }\r\n\r\n    /**\r\n     * Gets the engine associated with the scene\r\n     * @returns an Engine\r\n     */\r\n    public getEngine(): AbstractEngine {\r\n        return this._engine;\r\n    }\r\n\r\n    /**\r\n     * Gets the total number of vertices rendered per frame\r\n     * @returns the total number of vertices rendered per frame\r\n     */\r\n    public getTotalVertices(): number {\r\n        return this._totalVertices.current;\r\n    }\r\n\r\n    /**\r\n     * Gets the performance counter for total vertices\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#instrumentation\r\n     */\r\n    public get totalVerticesPerfCounter(): PerfCounter {\r\n        return this._totalVertices;\r\n    }\r\n\r\n    /**\r\n     * Gets the total number of active indices rendered per frame (You can deduce the number of rendered triangles by dividing this number by 3)\r\n     * @returns the total number of active indices rendered per frame\r\n     */\r\n    public getActiveIndices(): number {\r\n        return this._activeIndices.current;\r\n    }\r\n\r\n    /**\r\n     * Gets the performance counter for active indices\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#instrumentation\r\n     */\r\n    public get totalActiveIndicesPerfCounter(): PerfCounter {\r\n        return this._activeIndices;\r\n    }\r\n\r\n    /**\r\n     * Gets the total number of active particles rendered per frame\r\n     * @returns the total number of active particles rendered per frame\r\n     */\r\n    public getActiveParticles(): number {\r\n        return this._activeParticles.current;\r\n    }\r\n\r\n    /**\r\n     * Gets the performance counter for active particles\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#instrumentation\r\n     */\r\n    public get activeParticlesPerfCounter(): PerfCounter {\r\n        return this._activeParticles;\r\n    }\r\n\r\n    /**\r\n     * Gets the total number of active bones rendered per frame\r\n     * @returns the total number of active bones rendered per frame\r\n     */\r\n    public getActiveBones(): number {\r\n        return this._activeBones.current;\r\n    }\r\n\r\n    /**\r\n     * Gets the performance counter for active bones\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#instrumentation\r\n     */\r\n    public get activeBonesPerfCounter(): PerfCounter {\r\n        return this._activeBones;\r\n    }\r\n\r\n    /**\r\n     * Gets the array of active meshes\r\n     * @returns an array of AbstractMesh\r\n     */\r\n    public getActiveMeshes(): SmartArray<AbstractMesh> {\r\n        return this._activeMeshes;\r\n    }\r\n\r\n    /**\r\n     * Gets the animation ratio (which is 1.0 is the scene renders at 60fps and 2 if the scene renders at 30fps, etc.)\r\n     * @returns a number\r\n     */\r\n    public getAnimationRatio(): number {\r\n        return this._animationRatio !== undefined ? this._animationRatio : 1;\r\n    }\r\n\r\n    /**\r\n     * Gets an unique Id for the current render phase\r\n     * @returns a number\r\n     */\r\n    public getRenderId(): number {\r\n        return this._renderId;\r\n    }\r\n\r\n    /**\r\n     * Gets an unique Id for the current frame\r\n     * @returns a number\r\n     */\r\n    public getFrameId(): number {\r\n        return this._frameId;\r\n    }\r\n\r\n    /** Call this function if you want to manually increment the render Id*/\r\n    public incrementRenderId(): void {\r\n        this._renderId++;\r\n    }\r\n\r\n    private _createUbo(): void {\r\n        this.setSceneUniformBuffer(this.createSceneUniformBuffer());\r\n    }\r\n\r\n    /**\r\n     * Use this method to simulate a pointer move on a mesh\r\n     * The pickResult parameter can be obtained from a scene.pick or scene.pickWithRay\r\n     * @param pickResult pickingInfo of the object wished to simulate pointer event on\r\n     * @param pointerEventInit pointer event state to be used when simulating the pointer event (eg. pointer id for multitouch)\r\n     * @returns the current scene\r\n     */\r\n    public simulatePointerMove(pickResult: PickingInfo, pointerEventInit?: PointerEventInit): Scene {\r\n        this._inputManager.simulatePointerMove(pickResult, pointerEventInit);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Use this method to simulate a pointer down on a mesh\r\n     * The pickResult parameter can be obtained from a scene.pick or scene.pickWithRay\r\n     * @param pickResult pickingInfo of the object wished to simulate pointer event on\r\n     * @param pointerEventInit pointer event state to be used when simulating the pointer event (eg. pointer id for multitouch)\r\n     * @returns the current scene\r\n     */\r\n    public simulatePointerDown(pickResult: PickingInfo, pointerEventInit?: PointerEventInit): Scene {\r\n        this._inputManager.simulatePointerDown(pickResult, pointerEventInit);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Use this method to simulate a pointer up on a mesh\r\n     * The pickResult parameter can be obtained from a scene.pick or scene.pickWithRay\r\n     * @param pickResult pickingInfo of the object wished to simulate pointer event on\r\n     * @param pointerEventInit pointer event state to be used when simulating the pointer event (eg. pointer id for multitouch)\r\n     * @param doubleTap indicates that the pointer up event should be considered as part of a double click (false by default)\r\n     * @returns the current scene\r\n     */\r\n    public simulatePointerUp(pickResult: PickingInfo, pointerEventInit?: PointerEventInit, doubleTap?: boolean): Scene {\r\n        this._inputManager.simulatePointerUp(pickResult, pointerEventInit, doubleTap);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the current pointer event is captured (meaning that the scene has already handled the pointer down)\r\n     * @param pointerId defines the pointer id to use in a multi-touch scenario (0 by default)\r\n     * @returns true if the pointer was captured\r\n     */\r\n    public isPointerCaptured(pointerId = 0): boolean {\r\n        return this._inputManager.isPointerCaptured(pointerId);\r\n    }\r\n\r\n    /**\r\n     * Attach events to the canvas (To handle actionManagers triggers and raise onPointerMove, onPointerDown and onPointerUp\r\n     * @param attachUp defines if you want to attach events to pointerup\r\n     * @param attachDown defines if you want to attach events to pointerdown\r\n     * @param attachMove defines if you want to attach events to pointermove\r\n     */\r\n    public attachControl(attachUp = true, attachDown = true, attachMove = true): void {\r\n        this._inputManager.attachControl(attachUp, attachDown, attachMove);\r\n    }\r\n\r\n    /** Detaches all event handlers*/\r\n    public detachControl() {\r\n        this._inputManager.detachControl();\r\n    }\r\n\r\n    /**\r\n     * This function will check if the scene can be rendered (textures are loaded, shaders are compiled)\r\n     * Delay loaded resources are not taking in account\r\n     * @param checkRenderTargets true to also check that the meshes rendered as part of a render target are ready (default: true)\r\n     * @returns true if all required resources are ready\r\n     */\r\n    public isReady(checkRenderTargets = true): boolean {\r\n        if (this._isDisposed) {\r\n            return false;\r\n        }\r\n\r\n        let index: number;\r\n        const engine = this.getEngine();\r\n\r\n        const currentRenderPassId = engine.currentRenderPassId;\r\n\r\n        engine.currentRenderPassId = this.activeCamera?.renderPassId ?? currentRenderPassId;\r\n\r\n        let isReady = true;\r\n\r\n        // Pending data\r\n        if (this._pendingData.length > 0) {\r\n            isReady = false;\r\n        }\r\n\r\n        // Ensures that the pre-pass renderer is enabled if it is to be enabled.\r\n        this.prePassRenderer?.update();\r\n\r\n        // OIT\r\n        if (this.useOrderIndependentTransparency && this.depthPeelingRenderer) {\r\n            isReady &&= this.depthPeelingRenderer.isReady();\r\n        }\r\n\r\n        // Meshes\r\n        if (checkRenderTargets) {\r\n            this._processedMaterials.reset();\r\n            this._materialsRenderTargets.reset();\r\n        }\r\n\r\n        for (index = 0; index < this.meshes.length; index++) {\r\n            const mesh = this.meshes[index];\r\n\r\n            if (!mesh.subMeshes || mesh.subMeshes.length === 0) {\r\n                continue;\r\n            }\r\n\r\n            // Do not stop at the first encountered \"unready\" object as we want to ensure\r\n            // all materials are starting off their compilation in parallel.\r\n            if (!mesh.isReady(true)) {\r\n                isReady = false;\r\n                continue;\r\n            }\r\n\r\n            const hardwareInstancedRendering =\r\n                mesh.hasThinInstances ||\r\n                mesh.getClassName() === \"InstancedMesh\" ||\r\n                mesh.getClassName() === \"InstancedLinesMesh\" ||\r\n                (engine.getCaps().instancedArrays && (<Mesh>mesh).instances.length > 0);\r\n            // Is Ready For Mesh\r\n            for (const step of this._isReadyForMeshStage) {\r\n                if (!step.action(mesh, hardwareInstancedRendering)) {\r\n                    isReady = false;\r\n                }\r\n            }\r\n\r\n            if (!checkRenderTargets) {\r\n                continue;\r\n            }\r\n\r\n            const mat = mesh.material || this.defaultMaterial;\r\n            if (mat) {\r\n                if (mat._storeEffectOnSubMeshes) {\r\n                    for (const subMesh of mesh.subMeshes) {\r\n                        const material = subMesh.getMaterial();\r\n                        if (material && material.hasRenderTargetTextures && material.getRenderTargetTextures != null) {\r\n                            if (this._processedMaterials.indexOf(material) === -1) {\r\n                                this._processedMaterials.push(material);\r\n\r\n                                this._materialsRenderTargets.concatWithNoDuplicate(material.getRenderTargetTextures());\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    if (mat.hasRenderTargetTextures && mat.getRenderTargetTextures != null) {\r\n                        if (this._processedMaterials.indexOf(mat) === -1) {\r\n                            this._processedMaterials.push(mat);\r\n\r\n                            this._materialsRenderTargets.concatWithNoDuplicate(mat.getRenderTargetTextures());\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Render targets\r\n        if (checkRenderTargets) {\r\n            for (index = 0; index < this._materialsRenderTargets.length; ++index) {\r\n                const rtt = this._materialsRenderTargets.data[index];\r\n                if (!rtt.isReadyForRendering()) {\r\n                    isReady = false;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Geometries\r\n        for (index = 0; index < this.geometries.length; index++) {\r\n            const geometry = this.geometries[index];\r\n\r\n            if (geometry.delayLoadState === Constants.DELAYLOADSTATE_LOADING) {\r\n                isReady = false;\r\n            }\r\n        }\r\n\r\n        // Post-processes\r\n        if (this.activeCameras && this.activeCameras.length > 0) {\r\n            for (const camera of this.activeCameras) {\r\n                if (!camera.isReady(true)) {\r\n                    isReady = false;\r\n                }\r\n            }\r\n        } else if (this.activeCamera) {\r\n            if (!this.activeCamera.isReady(true)) {\r\n                isReady = false;\r\n            }\r\n        }\r\n\r\n        // Particles\r\n        for (const particleSystem of this.particleSystems) {\r\n            if (!particleSystem.isReady()) {\r\n                isReady = false;\r\n            }\r\n        }\r\n\r\n        // Layers\r\n        if (this.layers) {\r\n            for (const layer of this.layers) {\r\n                if (!layer.isReady()) {\r\n                    isReady = false;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Effect layers\r\n        if (this.effectLayers) {\r\n            for (const effectLayer of this.effectLayers) {\r\n                if (!effectLayer.isLayerReady()) {\r\n                    isReady = false;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Effects\r\n        if (!engine.areAllEffectsReady()) {\r\n            isReady = false;\r\n        }\r\n\r\n        engine.currentRenderPassId = currentRenderPassId;\r\n\r\n        return isReady;\r\n    }\r\n\r\n    /** Resets all cached information relative to material (including effect and visibility) */\r\n    public resetCachedMaterial(): void {\r\n        this._cachedMaterial = null;\r\n        this._cachedEffect = null;\r\n        this._cachedVisibility = null;\r\n    }\r\n\r\n    /**\r\n     * Registers a function to be called before every frame render\r\n     * @param func defines the function to register\r\n     */\r\n    public registerBeforeRender(func: () => void): void {\r\n        this.onBeforeRenderObservable.add(func);\r\n    }\r\n\r\n    /**\r\n     * Unregisters a function called before every frame render\r\n     * @param func defines the function to unregister\r\n     */\r\n    public unregisterBeforeRender(func: () => void): void {\r\n        this.onBeforeRenderObservable.removeCallback(func);\r\n    }\r\n\r\n    /**\r\n     * Registers a function to be called after every frame render\r\n     * @param func defines the function to register\r\n     */\r\n    public registerAfterRender(func: () => void): void {\r\n        this.onAfterRenderObservable.add(func);\r\n    }\r\n\r\n    /**\r\n     * Unregisters a function called after every frame render\r\n     * @param func defines the function to unregister\r\n     */\r\n    public unregisterAfterRender(func: () => void): void {\r\n        this.onAfterRenderObservable.removeCallback(func);\r\n    }\r\n\r\n    private _executeOnceBeforeRender(func: () => void): void {\r\n        const execFunc = () => {\r\n            func();\r\n            setTimeout(() => {\r\n                this.unregisterBeforeRender(execFunc);\r\n            });\r\n        };\r\n        this.registerBeforeRender(execFunc);\r\n    }\r\n\r\n    /**\r\n     * The provided function will run before render once and will be disposed afterwards.\r\n     * A timeout delay can be provided so that the function will be executed in N ms.\r\n     * The timeout is using the browser's native setTimeout so time percision cannot be guaranteed.\r\n     * @param func The function to be executed.\r\n     * @param timeout optional delay in ms\r\n     */\r\n    public executeOnceBeforeRender(func: () => void, timeout?: number): void {\r\n        if (timeout !== undefined) {\r\n            setTimeout(() => {\r\n                this._executeOnceBeforeRender(func);\r\n            }, timeout);\r\n        } else {\r\n            this._executeOnceBeforeRender(func);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This function can help adding any object to the list of data awaited to be ready in order to check for a complete scene loading.\r\n     * @param data defines the object to wait for\r\n     */\r\n    public addPendingData(data: any): void {\r\n        this._pendingData.push(data);\r\n    }\r\n\r\n    /**\r\n     * Remove a pending data from the loading list which has previously been added with addPendingData.\r\n     * @param data defines the object to remove from the pending list\r\n     */\r\n    public removePendingData(data: any): void {\r\n        const wasLoading = this.isLoading;\r\n        const index = this._pendingData.indexOf(data);\r\n\r\n        if (index !== -1) {\r\n            this._pendingData.splice(index, 1);\r\n        }\r\n\r\n        if (wasLoading && !this.isLoading) {\r\n            this.onDataLoadedObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the number of items waiting to be loaded\r\n     * @returns the number of items waiting to be loaded\r\n     */\r\n    public getWaitingItemsCount(): number {\r\n        return this._pendingData.length;\r\n    }\r\n\r\n    /**\r\n     * Returns a boolean indicating if the scene is still loading data\r\n     */\r\n    public get isLoading(): boolean {\r\n        return this._pendingData.length > 0;\r\n    }\r\n\r\n    /**\r\n     * Registers a function to be executed when the scene is ready\r\n     * @param func - the function to be executed\r\n     * @param checkRenderTargets true to also check that the meshes rendered as part of a render target are ready (default: false)\r\n     */\r\n    public executeWhenReady(func: () => void, checkRenderTargets = false): void {\r\n        this.onReadyObservable.addOnce(func);\r\n\r\n        if (this._executeWhenReadyTimeoutId !== null) {\r\n            return;\r\n        }\r\n\r\n        this._checkIsReady(checkRenderTargets);\r\n    }\r\n\r\n    /**\r\n     * Returns a promise that resolves when the scene is ready\r\n     * @param checkRenderTargets true to also check that the meshes rendered as part of a render target are ready (default: false)\r\n     * @returns A promise that resolves when the scene is ready\r\n     */\r\n    public async whenReadyAsync(checkRenderTargets = false): Promise<void> {\r\n        return await new Promise((resolve) => {\r\n            this.executeWhenReady(() => {\r\n                resolve();\r\n            }, checkRenderTargets);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _checkIsReady(checkRenderTargets = false) {\r\n        this._registerTransientComponents();\r\n\r\n        if (this.isReady(checkRenderTargets)) {\r\n            this.onReadyObservable.notifyObservers(this);\r\n\r\n            this.onReadyObservable.clear();\r\n            this._executeWhenReadyTimeoutId = null;\r\n            return;\r\n        }\r\n\r\n        if (this._isDisposed) {\r\n            this.onReadyObservable.clear();\r\n            this._executeWhenReadyTimeoutId = null;\r\n            return;\r\n        }\r\n\r\n        this._executeWhenReadyTimeoutId = setTimeout(() => {\r\n            // Ensure materials effects are checked outside render loops\r\n            this.incrementRenderId();\r\n            this._checkIsReady(checkRenderTargets);\r\n        }, 100);\r\n    }\r\n\r\n    /**\r\n     * Gets all animatable attached to the scene\r\n     */\r\n    public get animatables(): Animatable[] {\r\n        return this._activeAnimatables;\r\n    }\r\n\r\n    /**\r\n     * Resets the last animation time frame.\r\n     * Useful to override when animations start running when loading a scene for the first time.\r\n     */\r\n    public resetLastAnimationTimeFrame(): void {\r\n        this._animationTimeLast = PrecisionDate.Now;\r\n    }\r\n\r\n    // Matrix\r\n\r\n    /**\r\n     * Gets the current view matrix\r\n     * @returns a Matrix\r\n     */\r\n    public getViewMatrix(): Matrix {\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the current projection matrix\r\n     * @returns a Matrix\r\n     */\r\n    public getProjectionMatrix(): Matrix {\r\n        return this._projectionMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the current transform matrix\r\n     * @returns a Matrix made of View * Projection\r\n     */\r\n    public getTransformMatrix(): Matrix {\r\n        return this._transformMatrix;\r\n    }\r\n\r\n    /**\r\n     * Sets the current transform matrix\r\n     * @param viewL defines the View matrix to use\r\n     * @param projectionL defines the Projection matrix to use\r\n     * @param viewR defines the right View matrix to use (if provided)\r\n     * @param projectionR defines the right Projection matrix to use (if provided)\r\n     */\r\n    public setTransformMatrix(viewL: Matrix, projectionL: Matrix, viewR?: Matrix, projectionR?: Matrix): void {\r\n        // clear the multiviewSceneUbo if no viewR and projectionR are defined\r\n        if (!viewR && !projectionR && this._multiviewSceneUbo) {\r\n            this._multiviewSceneUbo.dispose();\r\n            this._multiviewSceneUbo = null;\r\n        }\r\n        if (this._viewUpdateFlag === viewL.updateFlag && this._projectionUpdateFlag === projectionL.updateFlag) {\r\n            return;\r\n        }\r\n\r\n        this._viewUpdateFlag = viewL.updateFlag;\r\n        this._projectionUpdateFlag = projectionL.updateFlag;\r\n        this._viewMatrix = viewL;\r\n        this._projectionMatrix = projectionL;\r\n\r\n        this._viewMatrix.multiplyToRef(this._projectionMatrix, this._transformMatrix);\r\n\r\n        // Update frustum\r\n        if (!this._frustumPlanes) {\r\n            this._frustumPlanes = Frustum.GetPlanes(this._transformMatrix);\r\n        } else {\r\n            Frustum.GetPlanesToRef(this._transformMatrix, this._frustumPlanes);\r\n        }\r\n\r\n        if (this._multiviewSceneUbo && this._multiviewSceneUbo.useUbo) {\r\n            this._updateMultiviewUbo(viewR, projectionR);\r\n        } else if (this._sceneUbo.useUbo) {\r\n            this._sceneUbo.updateMatrix(\"viewProjection\", this._transformMatrix);\r\n            this._sceneUbo.updateMatrix(\"view\", this._viewMatrix);\r\n            this._sceneUbo.updateMatrix(\"projection\", this._projectionMatrix);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the uniform buffer used to store scene data\r\n     * @returns a UniformBuffer\r\n     */\r\n    public getSceneUniformBuffer(): UniformBuffer {\r\n        return this._multiviewSceneUbo ? this._multiviewSceneUbo : this._sceneUbo;\r\n    }\r\n\r\n    /**\r\n     * Creates a scene UBO\r\n     * @param name name of the uniform buffer (optional, for debugging purpose only)\r\n     * @returns a new ubo\r\n     */\r\n    public createSceneUniformBuffer(name?: string): UniformBuffer {\r\n        const sceneUbo = new UniformBuffer(this._engine, undefined, false, name ?? \"scene\");\r\n        sceneUbo.addUniform(\"viewProjection\", 16);\r\n        sceneUbo.addUniform(\"view\", 16);\r\n        sceneUbo.addUniform(\"projection\", 16);\r\n        sceneUbo.addUniform(\"vEyePosition\", 4);\r\n\r\n        return sceneUbo;\r\n    }\r\n\r\n    /**\r\n     * Sets the scene ubo\r\n     * @param ubo the ubo to set for the scene\r\n     */\r\n    public setSceneUniformBuffer(ubo: UniformBuffer): void {\r\n        this._sceneUbo = ubo;\r\n        this._viewUpdateFlag = -1;\r\n        this._projectionUpdateFlag = -1;\r\n    }\r\n\r\n    /**\r\n     * Gets an unique (relatively to the current scene) Id\r\n     * @returns an unique number for the scene\r\n     */\r\n    public getUniqueId() {\r\n        return UniqueIdGenerator.UniqueId;\r\n    }\r\n\r\n    /**\r\n     * Add a mesh to the list of scene's meshes\r\n     * @param newMesh defines the mesh to add\r\n     * @param recursive if all child meshes should also be added to the scene\r\n     */\r\n    public addMesh(newMesh: AbstractMesh, recursive = false) {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        this.meshes.push(newMesh);\r\n\r\n        newMesh._resyncLightSources();\r\n\r\n        if (!newMesh.parent) {\r\n            newMesh._addToSceneRootNodes();\r\n        }\r\n\r\n        Tools.SetImmediate(() => {\r\n            this.onNewMeshAddedObservable.notifyObservers(newMesh);\r\n        });\r\n\r\n        if (recursive) {\r\n            const children = newMesh.getChildMeshes();\r\n\r\n            for (const m of children) {\r\n                this.addMesh(m);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh for the list of scene's meshes\r\n     * @param toRemove defines the mesh to remove\r\n     * @param recursive if all child meshes should also be removed from the scene\r\n     * @returns the index where the mesh was in the mesh list\r\n     */\r\n    public removeMesh(toRemove: AbstractMesh, recursive = false): number {\r\n        const index = this.meshes.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from the scene if the mesh found\r\n\r\n            this.meshes.splice(index, 1);\r\n\r\n            if (!toRemove.parent) {\r\n                toRemove._removeFromSceneRootNodes();\r\n            }\r\n        }\r\n\r\n        this._inputManager._invalidateMesh(toRemove);\r\n\r\n        this.onMeshRemovedObservable.notifyObservers(toRemove);\r\n        if (recursive) {\r\n            const children = toRemove.getChildMeshes();\r\n            for (const m of children) {\r\n                this.removeMesh(m);\r\n            }\r\n        }\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Add a transform node to the list of scene's transform nodes\r\n     * @param newTransformNode defines the transform node to add\r\n     */\r\n    public addTransformNode(newTransformNode: TransformNode) {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        if (newTransformNode.getScene() === this && newTransformNode._indexInSceneTransformNodesArray !== -1) {\r\n            // Already there?\r\n            return;\r\n        }\r\n\r\n        newTransformNode._indexInSceneTransformNodesArray = this.transformNodes.length;\r\n        this.transformNodes.push(newTransformNode);\r\n\r\n        if (!newTransformNode.parent) {\r\n            newTransformNode._addToSceneRootNodes();\r\n        }\r\n\r\n        this.onNewTransformNodeAddedObservable.notifyObservers(newTransformNode);\r\n    }\r\n\r\n    /**\r\n     * Remove a transform node for the list of scene's transform nodes\r\n     * @param toRemove defines the transform node to remove\r\n     * @returns the index where the transform node was in the transform node list\r\n     */\r\n    public removeTransformNode(toRemove: TransformNode): number {\r\n        const index = toRemove._indexInSceneTransformNodesArray;\r\n        if (index !== -1) {\r\n            if (index !== this.transformNodes.length - 1) {\r\n                const lastNode = this.transformNodes[this.transformNodes.length - 1];\r\n                this.transformNodes[index] = lastNode;\r\n                lastNode._indexInSceneTransformNodesArray = index;\r\n            }\r\n\r\n            toRemove._indexInSceneTransformNodesArray = -1;\r\n            this.transformNodes.pop();\r\n            if (!toRemove.parent) {\r\n                toRemove._removeFromSceneRootNodes();\r\n            }\r\n        }\r\n\r\n        this.onTransformNodeRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a skeleton for the list of scene's skeletons\r\n     * @param toRemove defines the skeleton to remove\r\n     * @returns the index where the skeleton was in the skeleton list\r\n     */\r\n    public removeSkeleton(toRemove: Skeleton): number {\r\n        const index = this.skeletons.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from the scene if found\r\n            this.skeletons.splice(index, 1);\r\n            this.onSkeletonRemovedObservable.notifyObservers(toRemove);\r\n\r\n            // Clean active container\r\n            this._executeActiveContainerCleanup(this._activeSkeletons);\r\n        }\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a morph target for the list of scene's morph targets\r\n     * @param toRemove defines the morph target to remove\r\n     * @returns the index where the morph target was in the morph target list\r\n     */\r\n    public removeMorphTargetManager(toRemove: MorphTargetManager): number {\r\n        const index = this.morphTargetManagers.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from the scene if found\r\n            this.morphTargetManagers.splice(index, 1);\r\n        }\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a light for the list of scene's lights\r\n     * @param toRemove defines the light to remove\r\n     * @returns the index where the light was in the light list\r\n     */\r\n    public removeLight(toRemove: Light): number {\r\n        const index = this.lights.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from meshes\r\n            for (const mesh of this.meshes) {\r\n                mesh._removeLightSource(toRemove, false);\r\n            }\r\n\r\n            // Remove from the scene if mesh found\r\n            this.lights.splice(index, 1);\r\n            this.sortLightsByPriority();\r\n\r\n            if (!toRemove.parent) {\r\n                toRemove._removeFromSceneRootNodes();\r\n            }\r\n        }\r\n        this.onLightRemovedObservable.notifyObservers(toRemove);\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a camera for the list of scene's cameras\r\n     * @param toRemove defines the camera to remove\r\n     * @returns the index where the camera was in the camera list\r\n     */\r\n    public removeCamera(toRemove: Camera): number {\r\n        const index = this.cameras.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            // Remove from the scene if mesh found\r\n            this.cameras.splice(index, 1);\r\n            if (!toRemove.parent) {\r\n                toRemove._removeFromSceneRootNodes();\r\n            }\r\n        }\r\n        // Remove from activeCameras\r\n        if (this.activeCameras) {\r\n            const index2 = this.activeCameras.indexOf(toRemove);\r\n            if (index2 !== -1) {\r\n                // Remove from the scene if mesh found\r\n                this.activeCameras.splice(index2, 1);\r\n            }\r\n        }\r\n        // Reset the activeCamera\r\n        if (this.activeCamera === toRemove) {\r\n            if (this.cameras.length > 0) {\r\n                this.activeCamera = this.cameras[0];\r\n            } else {\r\n                this.activeCamera = null;\r\n            }\r\n        }\r\n        this.onCameraRemovedObservable.notifyObservers(toRemove);\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a particle system for the list of scene's particle systems\r\n     * @param toRemove defines the particle system to remove\r\n     * @returns the index where the particle system was in the particle system list\r\n     */\r\n    public removeParticleSystem(toRemove: IParticleSystem): number {\r\n        const index = this.particleSystems.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.particleSystems.splice(index, 1);\r\n\r\n            // Clean active container\r\n            this._executeActiveContainerCleanup(this._activeParticleSystems);\r\n        }\r\n        this.onParticleSystemRemovedObservable.notifyObservers(toRemove);\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Remove a animation for the list of scene's animations\r\n     * @param toRemove defines the animation to remove\r\n     * @returns the index where the animation was in the animation list\r\n     */\r\n    public removeAnimation(toRemove: Animation): number {\r\n        const index = this.animations.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.animations.splice(index, 1);\r\n        }\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Will stop the animation of the given target\r\n     * @param target - the target\r\n     * @param animationName - the name of the animation to stop (all animations will be stopped if both this and targetMask are empty)\r\n     * @param targetMask - a function that determines if the animation should be stopped based on its target (all animations will be stopped if both this and animationName are empty)\r\n     */\r\n    public stopAnimation(target: any, animationName?: string, targetMask?: (target: any) => boolean): void {\r\n        // Do nothing as code will be provided by animation component\r\n    }\r\n\r\n    /**\r\n     * Removes the given animation group from this scene.\r\n     * @param toRemove The animation group to remove\r\n     * @returns The index of the removed animation group\r\n     */\r\n    public removeAnimationGroup(toRemove: AnimationGroup): number {\r\n        const index = this.animationGroups.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.animationGroups.splice(index, 1);\r\n        }\r\n        this.onAnimationGroupRemovedObservable.notifyObservers(toRemove);\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given multi-material from this scene.\r\n     * @param toRemove The multi-material to remove\r\n     * @returns The index of the removed multi-material\r\n     */\r\n    public removeMultiMaterial(toRemove: MultiMaterial): number {\r\n        const index = this.multiMaterials.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.multiMaterials.splice(index, 1);\r\n        }\r\n\r\n        this.onMultiMaterialRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given material from this scene.\r\n     * @param toRemove The material to remove\r\n     * @returns The index of the removed material\r\n     */\r\n    public removeMaterial(toRemove: Material): number {\r\n        const index = toRemove._indexInSceneMaterialArray;\r\n        if (index !== -1 && index < this.materials.length) {\r\n            if (index !== this.materials.length - 1) {\r\n                const lastMaterial = this.materials[this.materials.length - 1];\r\n                this.materials[index] = lastMaterial;\r\n                lastMaterial._indexInSceneMaterialArray = index;\r\n            }\r\n\r\n            toRemove._indexInSceneMaterialArray = -1;\r\n            this.materials.pop();\r\n        }\r\n\r\n        this.onMaterialRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given action manager from this scene.\r\n     * @deprecated\r\n     * @param toRemove The action manager to remove\r\n     * @returns The index of the removed action manager\r\n     */\r\n    public removeActionManager(toRemove: AbstractActionManager): number {\r\n        const index = this.actionManagers.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.actionManagers.splice(index, 1);\r\n        }\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given texture from this scene.\r\n     * @param toRemove The texture to remove\r\n     * @returns The index of the removed texture\r\n     */\r\n    public removeTexture(toRemove: BaseTexture): number {\r\n        const index = this.textures.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.textures.splice(index, 1);\r\n        }\r\n        this.onTextureRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given frame graph from this scene.\r\n     * @param toRemove The frame graph to remove\r\n     * @returns The index of the removed frame graph\r\n     */\r\n    public removeFrameGraph(toRemove: FrameGraph): number {\r\n        const index = this.frameGraphs.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.frameGraphs.splice(index, 1);\r\n        }\r\n        this.onFrameGraphRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given post-process from this scene.\r\n     * @param toRemove The post-process to remove\r\n     * @returns The index of the removed post-process\r\n     */\r\n    public removePostProcess(toRemove: PostProcess): number {\r\n        const index = this.postProcesses.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.postProcesses.splice(index, 1);\r\n        }\r\n        this.onPostProcessRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Removes the given layer from this scene.\r\n     * @param toRemove The layer to remove\r\n     * @returns The index of the removed layer\r\n     */\r\n    public removeEffectLayer(toRemove: EffectLayer): number {\r\n        const index = this.effectLayers.indexOf(toRemove);\r\n        if (index !== -1) {\r\n            this.effectLayers.splice(index, 1);\r\n        }\r\n        this.onEffectLayerRemovedObservable.notifyObservers(toRemove);\r\n\r\n        return index;\r\n    }\r\n\r\n    /**\r\n     * Adds the given light to this scene\r\n     * @param newLight The light to add\r\n     */\r\n    public addLight(newLight: Light): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.lights.push(newLight);\r\n        this.sortLightsByPriority();\r\n\r\n        if (!newLight.parent) {\r\n            newLight._addToSceneRootNodes();\r\n        }\r\n\r\n        // Add light to all meshes (To support if the light is removed and then re-added)\r\n        for (const mesh of this.meshes) {\r\n            if (mesh.lightSources.indexOf(newLight) === -1) {\r\n                mesh.lightSources.push(newLight);\r\n                mesh._resyncLightSources();\r\n            }\r\n        }\r\n\r\n        Tools.SetImmediate(() => {\r\n            this.onNewLightAddedObservable.notifyObservers(newLight);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Sorts the list list based on light priorities\r\n     */\r\n    public sortLightsByPriority(): void {\r\n        if (this.requireLightSorting) {\r\n            this.lights.sort(LightConstants.CompareLightsPriority);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds the given camera to this scene\r\n     * @param newCamera The camera to add\r\n     */\r\n    public addCamera(newCamera: Camera): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        this.cameras.push(newCamera);\r\n        Tools.SetImmediate(() => {\r\n            this.onNewCameraAddedObservable.notifyObservers(newCamera);\r\n        });\r\n\r\n        if (!newCamera.parent) {\r\n            newCamera._addToSceneRootNodes();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds the given skeleton to this scene\r\n     * @param newSkeleton The skeleton to add\r\n     */\r\n    public addSkeleton(newSkeleton: Skeleton): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.skeletons.push(newSkeleton);\r\n\r\n        Tools.SetImmediate(() => {\r\n            this.onNewSkeletonAddedObservable.notifyObservers(newSkeleton);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds the given particle system to this scene\r\n     * @param newParticleSystem The particle system to add\r\n     */\r\n    public addParticleSystem(newParticleSystem: IParticleSystem): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.particleSystems.push(newParticleSystem);\r\n\r\n        Tools.SetImmediate(() => {\r\n            this.onNewParticleSystemAddedObservable.notifyObservers(newParticleSystem);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds the given animation to this scene\r\n     * @param newAnimation The animation to add\r\n     */\r\n    public addAnimation(newAnimation: Animation): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.animations.push(newAnimation);\r\n    }\r\n\r\n    /**\r\n     * Adds the given animation group to this scene.\r\n     * @param newAnimationGroup The animation group to add\r\n     */\r\n    public addAnimationGroup(newAnimationGroup: AnimationGroup): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.animationGroups.push(newAnimationGroup);\r\n\r\n        Tools.SetImmediate(() => {\r\n            this.onNewAnimationGroupAddedObservable.notifyObservers(newAnimationGroup);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds the given multi-material to this scene\r\n     * @param newMultiMaterial The multi-material to add\r\n     */\r\n    public addMultiMaterial(newMultiMaterial: MultiMaterial): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.multiMaterials.push(newMultiMaterial);\r\n        Tools.SetImmediate(() => {\r\n            this.onNewMultiMaterialAddedObservable.notifyObservers(newMultiMaterial);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds the given material to this scene\r\n     * @param newMaterial The material to add\r\n     */\r\n    public addMaterial(newMaterial: Material): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        if (newMaterial.getScene() === this && newMaterial._indexInSceneMaterialArray !== -1) {\r\n            // Already there??\r\n            return;\r\n        }\r\n\r\n        newMaterial._indexInSceneMaterialArray = this.materials.length;\r\n        this.materials.push(newMaterial);\r\n        Tools.SetImmediate(() => {\r\n            this.onNewMaterialAddedObservable.notifyObservers(newMaterial);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds the given morph target to this scene\r\n     * @param newMorphTargetManager The morph target to add\r\n     */\r\n    public addMorphTargetManager(newMorphTargetManager: MorphTargetManager): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.morphTargetManagers.push(newMorphTargetManager);\r\n    }\r\n\r\n    /**\r\n     * Adds the given geometry to this scene\r\n     * @param newGeometry The geometry to add\r\n     */\r\n    public addGeometry(newGeometry: Geometry): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n\r\n        if (this._geometriesByUniqueId) {\r\n            this._geometriesByUniqueId[newGeometry.uniqueId] = this.geometries.length;\r\n        }\r\n\r\n        this.geometries.push(newGeometry);\r\n    }\r\n\r\n    /**\r\n     * Adds the given action manager to this scene\r\n     * @deprecated\r\n     * @param newActionManager The action manager to add\r\n     */\r\n    public addActionManager(newActionManager: AbstractActionManager): void {\r\n        this.actionManagers.push(newActionManager);\r\n    }\r\n\r\n    /**\r\n     * Adds the given texture to this scene.\r\n     * @param newTexture The texture to add\r\n     */\r\n    public addTexture(newTexture: BaseTexture): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.textures.push(newTexture);\r\n        this.onNewTextureAddedObservable.notifyObservers(newTexture);\r\n    }\r\n\r\n    /**\r\n     * Adds the given frame graph to this scene.\r\n     * @param newFrameGraph The frame graph to add\r\n     */\r\n    public addFrameGraph(newFrameGraph: FrameGraph): void {\r\n        this.frameGraphs.push(newFrameGraph);\r\n        Tools.SetImmediate(() => {\r\n            this.onNewFrameGraphAddedObservable.notifyObservers(newFrameGraph);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds the given post process to this scene.\r\n     * @param newPostProcess The post process to add\r\n     */\r\n    public addPostProcess(newPostProcess: PostProcess): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.postProcesses.push(newPostProcess);\r\n        Tools.SetImmediate(() => {\r\n            this.onNewPostProcessAddedObservable.notifyObservers(newPostProcess);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds the given effect layer to this scene.\r\n     * @param newEffectLayer The effect layer to add\r\n     */\r\n    public addEffectLayer(newEffectLayer: EffectLayer): void {\r\n        if (this._blockEntityCollection) {\r\n            return;\r\n        }\r\n        this.effectLayers.push(newEffectLayer);\r\n        Tools.SetImmediate(() => {\r\n            this.onNewEffectLayerAddedObservable.notifyObservers(newEffectLayer);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Switch active camera\r\n     * @param newCamera defines the new active camera\r\n     * @param attachControl defines if attachControl must be called for the new active camera (default: true)\r\n     */\r\n    public switchActiveCamera(newCamera: Camera, attachControl = true): void {\r\n        const canvas = this._engine.getInputElement();\r\n\r\n        if (!canvas) {\r\n            return;\r\n        }\r\n\r\n        if (this.activeCamera) {\r\n            this.activeCamera.detachControl();\r\n        }\r\n        this.activeCamera = newCamera;\r\n        if (attachControl) {\r\n            newCamera.attachControl();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * sets the active camera of the scene using its Id\r\n     * @param id defines the camera's Id\r\n     * @returns the new active camera or null if none found.\r\n     */\r\n    public setActiveCameraById(id: string): Nullable<Camera> {\r\n        const camera = this.getCameraById(id);\r\n\r\n        if (camera) {\r\n            this.activeCamera = camera;\r\n            return camera;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * sets the active camera of the scene using its name\r\n     * @param name defines the camera's name\r\n     * @returns the new active camera or null if none found.\r\n     */\r\n    public setActiveCameraByName(name: string): Nullable<Camera> {\r\n        const camera = this.getCameraByName(name);\r\n\r\n        if (camera) {\r\n            this.activeCamera = camera;\r\n            return camera;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * get an animation group using its name\r\n     * @param name defines the material's name\r\n     * @returns the animation group or null if none found.\r\n     */\r\n    public getAnimationGroupByName(name: string): Nullable<AnimationGroup> {\r\n        for (let index = 0; index < this.animationGroups.length; index++) {\r\n            if (this.animationGroups[index].name === name) {\r\n                return this.animationGroups[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _getMaterial(allowMultiMaterials: boolean, predicate: (m: Material) => boolean): Nullable<Material> {\r\n        for (let index = 0; index < this.materials.length; index++) {\r\n            const material = this.materials[index];\r\n            if (predicate(material)) {\r\n                return material;\r\n            }\r\n        }\r\n        if (allowMultiMaterials) {\r\n            for (let index = 0; index < this.multiMaterials.length; index++) {\r\n                const material = this.multiMaterials[index];\r\n                if (predicate(material)) {\r\n                    return material;\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Get a material using its unique id\r\n     * @param uniqueId defines the material's unique id\r\n     * @param allowMultiMaterials determines whether multimaterials should be considered\r\n     * @returns the material or null if none found.\r\n     * @deprecated Please use getMaterialByUniqueId instead.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public getMaterialByUniqueID(uniqueId: number, allowMultiMaterials: boolean = false): Nullable<Material> {\r\n        return this.getMaterialByUniqueId(uniqueId, allowMultiMaterials);\r\n    }\r\n\r\n    /**\r\n     * Get a material using its unique id\r\n     * @param uniqueId defines the material's unique id\r\n     * @param allowMultiMaterials determines whether multimaterials should be considered\r\n     * @returns the material or null if none found.\r\n     */\r\n    public getMaterialByUniqueId(uniqueId: number, allowMultiMaterials: boolean = false): Nullable<Material> {\r\n        return this._getMaterial(allowMultiMaterials, (m) => m.uniqueId === uniqueId);\r\n    }\r\n\r\n    /**\r\n     * get a material using its id\r\n     * @param id defines the material's Id\r\n     * @param allowMultiMaterials determines whether multimaterials should be considered\r\n     * @returns the material or null if none found.\r\n     */\r\n    public getMaterialById(id: string, allowMultiMaterials: boolean = false): Nullable<Material> {\r\n        return this._getMaterial(allowMultiMaterials, (m) => m.id === id);\r\n    }\r\n\r\n    /**\r\n     * Gets a material using its name\r\n     * @param name defines the material's name\r\n     * @param allowMultiMaterials determines whether multimaterials should be considered\r\n     * @returns the material or null if none found.\r\n     */\r\n    public getMaterialByName(name: string, allowMultiMaterials: boolean = false): Nullable<Material> {\r\n        return this._getMaterial(allowMultiMaterials, (m) => m.name === name);\r\n    }\r\n\r\n    /**\r\n     * Gets a last added material using a given id\r\n     * @param id defines the material's id\r\n     * @param allowMultiMaterials determines whether multimaterials should be considered\r\n     * @returns the last material with the given id or null if none found.\r\n     */\r\n    public getLastMaterialById(id: string, allowMultiMaterials: boolean = false): Nullable<Material> {\r\n        for (let index = this.materials.length - 1; index >= 0; index--) {\r\n            if (this.materials[index].id === id) {\r\n                return this.materials[index];\r\n            }\r\n        }\r\n        if (allowMultiMaterials) {\r\n            for (let index = this.multiMaterials.length - 1; index >= 0; index--) {\r\n                if (this.multiMaterials[index].id === id) {\r\n                    return this.multiMaterials[index];\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Get a texture using its unique id\r\n     * @param uniqueId defines the texture's unique id\r\n     * @returns the texture or null if none found.\r\n     */\r\n    public getTextureByUniqueId(uniqueId: number): Nullable<BaseTexture> {\r\n        for (let index = 0; index < this.textures.length; index++) {\r\n            if (this.textures[index].uniqueId === uniqueId) {\r\n                return this.textures[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a texture using its name\r\n     * @param name defines the texture's name\r\n     * @returns the texture or null if none found.\r\n     */\r\n    public getTextureByName(name: string): Nullable<BaseTexture> {\r\n        for (let index = 0; index < this.textures.length; index++) {\r\n            if (this.textures[index].name === name) {\r\n                return this.textures[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a camera using its Id\r\n     * @param id defines the Id to look for\r\n     * @returns the camera or null if not found\r\n     */\r\n    public getCameraById(id: string): Nullable<Camera> {\r\n        for (let index = 0; index < this.cameras.length; index++) {\r\n            if (this.cameras[index].id === id) {\r\n                return this.cameras[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a camera using its unique Id\r\n     * @param uniqueId defines the unique Id to look for\r\n     * @returns the camera or null if not found\r\n     */\r\n    public getCameraByUniqueId(uniqueId: number): Nullable<Camera> {\r\n        for (let index = 0; index < this.cameras.length; index++) {\r\n            if (this.cameras[index].uniqueId === uniqueId) {\r\n                return this.cameras[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a camera using its name\r\n     * @param name defines the camera's name\r\n     * @returns the camera or null if none found.\r\n     */\r\n    public getCameraByName(name: string): Nullable<Camera> {\r\n        for (let index = 0; index < this.cameras.length; index++) {\r\n            if (this.cameras[index].name === name) {\r\n                return this.cameras[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a bone using its Id\r\n     * @param id defines the bone's Id\r\n     * @returns the bone or null if not found\r\n     */\r\n    public getBoneById(id: string): Nullable<Bone> {\r\n        for (let skeletonIndex = 0; skeletonIndex < this.skeletons.length; skeletonIndex++) {\r\n            const skeleton = this.skeletons[skeletonIndex];\r\n            for (let boneIndex = 0; boneIndex < skeleton.bones.length; boneIndex++) {\r\n                if (skeleton.bones[boneIndex].id === id) {\r\n                    return skeleton.bones[boneIndex];\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a bone using its id\r\n     * @param name defines the bone's name\r\n     * @returns the bone or null if not found\r\n     */\r\n    public getBoneByName(name: string): Nullable<Bone> {\r\n        for (let skeletonIndex = 0; skeletonIndex < this.skeletons.length; skeletonIndex++) {\r\n            const skeleton = this.skeletons[skeletonIndex];\r\n            for (let boneIndex = 0; boneIndex < skeleton.bones.length; boneIndex++) {\r\n                if (skeleton.bones[boneIndex].name === name) {\r\n                    return skeleton.bones[boneIndex];\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a light node using its name\r\n     * @param name defines the light's name\r\n     * @returns the light or null if none found.\r\n     */\r\n    public getLightByName(name: string): Nullable<Light> {\r\n        for (let index = 0; index < this.lights.length; index++) {\r\n            if (this.lights[index].name === name) {\r\n                return this.lights[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a light node using its Id\r\n     * @param id defines the light's Id\r\n     * @returns the light or null if none found.\r\n     */\r\n    public getLightById(id: string): Nullable<Light> {\r\n        for (let index = 0; index < this.lights.length; index++) {\r\n            if (this.lights[index].id === id) {\r\n                return this.lights[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a light node using its scene-generated unique Id\r\n     * @param uniqueId defines the light's unique Id\r\n     * @returns the light or null if none found.\r\n     */\r\n    public getLightByUniqueId(uniqueId: number): Nullable<Light> {\r\n        for (let index = 0; index < this.lights.length; index++) {\r\n            if (this.lights[index].uniqueId === uniqueId) {\r\n                return this.lights[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a particle system by Id\r\n     * @param id defines the particle system Id\r\n     * @returns the corresponding system or null if none found\r\n     */\r\n    public getParticleSystemById(id: string): Nullable<IParticleSystem> {\r\n        for (let index = 0; index < this.particleSystems.length; index++) {\r\n            if (this.particleSystems[index].id === id) {\r\n                return this.particleSystems[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a geometry using its Id\r\n     * @param id defines the geometry's Id\r\n     * @returns the geometry or null if none found.\r\n     */\r\n    public getGeometryById(id: string): Nullable<Geometry> {\r\n        for (let index = 0; index < this.geometries.length; index++) {\r\n            if (this.geometries[index].id === id) {\r\n                return this.geometries[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    private _getGeometryByUniqueId(uniqueId: number): Nullable<Geometry> {\r\n        if (this._geometriesByUniqueId) {\r\n            const index = this._geometriesByUniqueId[uniqueId];\r\n            if (index !== undefined) {\r\n                return this.geometries[index];\r\n            }\r\n        } else {\r\n            for (let index = 0; index < this.geometries.length; index++) {\r\n                if (this.geometries[index].uniqueId === uniqueId) {\r\n                    return this.geometries[index];\r\n                }\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a frame graph using its name\r\n     * @param name defines the frame graph's name\r\n     * @returns the frame graph or null if none found.\r\n     */\r\n    public getFrameGraphByName(name: string): Nullable<FrameGraph> {\r\n        for (let index = 0; index < this.frameGraphs.length; index++) {\r\n            if (this.frameGraphs[index].name === name) {\r\n                return this.frameGraphs[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Add a new geometry to this scene\r\n     * @param geometry defines the geometry to be added to the scene.\r\n     * @param force defines if the geometry must be pushed even if a geometry with this id already exists\r\n     * @returns a boolean defining if the geometry was added or not\r\n     */\r\n    public pushGeometry(geometry: Geometry, force?: boolean): boolean {\r\n        if (!force && this._getGeometryByUniqueId(geometry.uniqueId)) {\r\n            return false;\r\n        }\r\n\r\n        this.addGeometry(geometry);\r\n\r\n        Tools.SetImmediate(() => {\r\n            this.onNewGeometryAddedObservable.notifyObservers(geometry);\r\n        });\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Removes an existing geometry\r\n     * @param geometry defines the geometry to be removed from the scene\r\n     * @returns a boolean defining if the geometry was removed or not\r\n     */\r\n    public removeGeometry(geometry: Geometry): boolean {\r\n        let index;\r\n        if (this._geometriesByUniqueId) {\r\n            index = this._geometriesByUniqueId[geometry.uniqueId];\r\n            if (index === undefined) {\r\n                return false;\r\n            }\r\n        } else {\r\n            index = this.geometries.indexOf(geometry);\r\n            if (index < 0) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (index !== this.geometries.length - 1) {\r\n            const lastGeometry = this.geometries[this.geometries.length - 1];\r\n            if (lastGeometry) {\r\n                this.geometries[index] = lastGeometry;\r\n                if (this._geometriesByUniqueId) {\r\n                    this._geometriesByUniqueId[lastGeometry.uniqueId] = index;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._geometriesByUniqueId) {\r\n            this._geometriesByUniqueId[geometry.uniqueId] = undefined;\r\n        }\r\n\r\n        this.geometries.pop();\r\n\r\n        this.onGeometryRemovedObservable.notifyObservers(geometry);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of geometries attached to the scene\r\n     * @returns an array of Geometry\r\n     */\r\n    public getGeometries(): Geometry[] {\r\n        return this.geometries;\r\n    }\r\n\r\n    /**\r\n     * Gets the first added mesh found of a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the mesh found or null if not found at all\r\n     */\r\n    public getMeshById(id: string): Nullable<AbstractMesh> {\r\n        for (let index = 0; index < this.meshes.length; index++) {\r\n            if (this.meshes[index].id === id) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a list of meshes using their Id\r\n     * @param id defines the Id to search for\r\n     * @returns a list of meshes\r\n     */\r\n    public getMeshesById(id: string): Array<AbstractMesh> {\r\n        return this.meshes.filter(function (m) {\r\n            return m.id === id;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Gets the first added transform node found of a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     */\r\n    public getTransformNodeById(id: string): Nullable<TransformNode> {\r\n        for (let index = 0; index < this.transformNodes.length; index++) {\r\n            if (this.transformNodes[index].id === id) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a transform node with its auto-generated unique Id\r\n     * @param uniqueId defines the unique Id to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     */\r\n    public getTransformNodeByUniqueId(uniqueId: number): Nullable<TransformNode> {\r\n        for (let index = 0; index < this.transformNodes.length; index++) {\r\n            if (this.transformNodes[index].uniqueId === uniqueId) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a list of transform nodes using their Id\r\n     * @param id defines the Id to search for\r\n     * @returns a list of transform nodes\r\n     */\r\n    public getTransformNodesById(id: string): Array<TransformNode> {\r\n        return this.transformNodes.filter(function (m) {\r\n            return m.id === id;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Gets a mesh with its auto-generated unique Id\r\n     * @param uniqueId defines the unique Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     */\r\n    public getMeshByUniqueId(uniqueId: number): Nullable<AbstractMesh> {\r\n        for (let index = 0; index < this.meshes.length; index++) {\r\n            if (this.meshes[index].uniqueId === uniqueId) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a the last added mesh using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     */\r\n    public getLastMeshById(id: string): Nullable<AbstractMesh> {\r\n        for (let index = this.meshes.length - 1; index >= 0; index--) {\r\n            if (this.meshes[index].id === id) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a the last transform node using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     */\r\n    public getLastTransformNodeById(id: string): Nullable<TransformNode> {\r\n        for (let index = this.transformNodes.length - 1; index >= 0; index--) {\r\n            if (this.transformNodes[index].id === id) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a the last added node (Mesh, Camera, Light) using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found node or null if not found at all\r\n     */\r\n    public getLastEntryById(id: string): Nullable<Node> {\r\n        let index: number;\r\n        for (index = this.meshes.length - 1; index >= 0; index--) {\r\n            if (this.meshes[index].id === id) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        for (index = this.transformNodes.length - 1; index >= 0; index--) {\r\n            if (this.transformNodes[index].id === id) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        for (index = this.cameras.length - 1; index >= 0; index--) {\r\n            if (this.cameras[index].id === id) {\r\n                return this.cameras[index];\r\n            }\r\n        }\r\n\r\n        for (index = this.lights.length - 1; index >= 0; index--) {\r\n            if (this.lights[index].id === id) {\r\n                return this.lights[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a node (Mesh, Camera, Light) using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found node or null if not found at all\r\n     */\r\n    public getNodeById(id: string): Nullable<Node> {\r\n        const mesh = this.getMeshById(id);\r\n        if (mesh) {\r\n            return mesh;\r\n        }\r\n\r\n        const transformNode = this.getTransformNodeById(id);\r\n        if (transformNode) {\r\n            return transformNode;\r\n        }\r\n\r\n        const light = this.getLightById(id);\r\n        if (light) {\r\n            return light;\r\n        }\r\n\r\n        const camera = this.getCameraById(id);\r\n        if (camera) {\r\n            return camera;\r\n        }\r\n\r\n        const bone = this.getBoneById(id);\r\n        if (bone) {\r\n            return bone;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a node (Mesh, Camera, Light) using a given name\r\n     * @param name defines the name to search for\r\n     * @returns the found node or null if not found at all.\r\n     */\r\n    public getNodeByName(name: string): Nullable<Node> {\r\n        const mesh = this.getMeshByName(name);\r\n        if (mesh) {\r\n            return mesh;\r\n        }\r\n\r\n        const transformNode = this.getTransformNodeByName(name);\r\n        if (transformNode) {\r\n            return transformNode;\r\n        }\r\n\r\n        const light = this.getLightByName(name);\r\n        if (light) {\r\n            return light;\r\n        }\r\n\r\n        const camera = this.getCameraByName(name);\r\n        if (camera) {\r\n            return camera;\r\n        }\r\n\r\n        const bone = this.getBoneByName(name);\r\n        if (bone) {\r\n            return bone;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a mesh using a given name\r\n     * @param name defines the name to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     */\r\n    public getMeshByName(name: string): Nullable<AbstractMesh> {\r\n        for (let index = 0; index < this.meshes.length; index++) {\r\n            if (this.meshes[index].name === name) {\r\n                return this.meshes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a transform node using a given name\r\n     * @param name defines the name to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     */\r\n    public getTransformNodeByName(name: string): Nullable<TransformNode> {\r\n        for (let index = 0; index < this.transformNodes.length; index++) {\r\n            if (this.transformNodes[index].name === name) {\r\n                return this.transformNodes[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a skeleton using a given Id (if many are found, this function will pick the last one)\r\n     * @param id defines the Id to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     */\r\n    public getLastSkeletonById(id: string): Nullable<Skeleton> {\r\n        for (let index = this.skeletons.length - 1; index >= 0; index--) {\r\n            if (this.skeletons[index].id === id) {\r\n                return this.skeletons[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a skeleton using a given auto generated unique id\r\n     * @param  uniqueId defines the unique id to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     */\r\n    public getSkeletonByUniqueId(uniqueId: number): Nullable<Skeleton> {\r\n        for (let index = 0; index < this.skeletons.length; index++) {\r\n            if (this.skeletons[index].uniqueId === uniqueId) {\r\n                return this.skeletons[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a skeleton using a given id (if many are found, this function will pick the first one)\r\n     * @param id defines the id to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     */\r\n    public getSkeletonById(id: string): Nullable<Skeleton> {\r\n        for (let index = 0; index < this.skeletons.length; index++) {\r\n            if (this.skeletons[index].id === id) {\r\n                return this.skeletons[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a skeleton using a given name\r\n     * @param name defines the name to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     */\r\n    public getSkeletonByName(name: string): Nullable<Skeleton> {\r\n        for (let index = 0; index < this.skeletons.length; index++) {\r\n            if (this.skeletons[index].name === name) {\r\n                return this.skeletons[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a morph target manager  using a given id (if many are found, this function will pick the last one)\r\n     * @param id defines the id to search for\r\n     * @returns the found morph target manager or null if not found at all.\r\n     */\r\n    public getMorphTargetManagerById(id: number): Nullable<MorphTargetManager> {\r\n        for (let index = 0; index < this.morphTargetManagers.length; index++) {\r\n            if (this.morphTargetManagers[index].uniqueId === id) {\r\n                return this.morphTargetManagers[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a morph target using a given id (if many are found, this function will pick the first one)\r\n     * @param id defines the id to search for\r\n     * @returns the found morph target or null if not found at all.\r\n     */\r\n    public getMorphTargetById(id: string): Nullable<MorphTarget> {\r\n        for (let managerIndex = 0; managerIndex < this.morphTargetManagers.length; ++managerIndex) {\r\n            const morphTargetManager = this.morphTargetManagers[managerIndex];\r\n            for (let index = 0; index < morphTargetManager.numTargets; ++index) {\r\n                const target = morphTargetManager.getTarget(index);\r\n                if (target.id === id) {\r\n                    return target;\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a morph target using a given name (if many are found, this function will pick the first one)\r\n     * @param name defines the name to search for\r\n     * @returns the found morph target or null if not found at all.\r\n     */\r\n    public getMorphTargetByName(name: string): Nullable<MorphTarget> {\r\n        for (let managerIndex = 0; managerIndex < this.morphTargetManagers.length; ++managerIndex) {\r\n            const morphTargetManager = this.morphTargetManagers[managerIndex];\r\n            for (let index = 0; index < morphTargetManager.numTargets; ++index) {\r\n                const target = morphTargetManager.getTarget(index);\r\n                if (target.name === name) {\r\n                    return target;\r\n                }\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a post process using a given name (if many are found, this function will pick the first one)\r\n     * @param name defines the name to search for\r\n     * @returns the found post process or null if not found at all.\r\n     */\r\n    public getPostProcessByName(name: string): Nullable<PostProcess> {\r\n        for (let postProcessIndex = 0; postProcessIndex < this.postProcesses.length; ++postProcessIndex) {\r\n            const postProcess = this.postProcesses[postProcessIndex];\r\n            if (postProcess.name === name) {\r\n                return postProcess;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the given mesh is active\r\n     * @param mesh defines the mesh to look for\r\n     * @returns true if the mesh is in the active list\r\n     */\r\n    public isActiveMesh(mesh: AbstractMesh): boolean {\r\n        return this._activeMeshes.indexOf(mesh) !== -1;\r\n    }\r\n\r\n    /**\r\n     * Return a unique id as a string which can serve as an identifier for the scene\r\n     */\r\n    public get uid(): string {\r\n        if (!this._uid) {\r\n            this._uid = Tools.RandomId();\r\n        }\r\n        return this._uid;\r\n    }\r\n\r\n    /**\r\n     * Add an externally attached data from its key.\r\n     * This method call will fail and return false, if such key already exists.\r\n     * If you don't care and just want to get the data no matter what, use the more convenient getOrAddExternalDataWithFactory() method.\r\n     * @param key the unique key that identifies the data\r\n     * @param data the data object to associate to the key for this Engine instance\r\n     * @returns true if no such key were already present and the data was added successfully, false otherwise\r\n     */\r\n    public addExternalData<T extends object>(key: string, data: T): boolean {\r\n        if (!this._externalData) {\r\n            this._externalData = new StringDictionary<object>();\r\n        }\r\n        return this._externalData.add(key, data);\r\n    }\r\n\r\n    /**\r\n     * Get an externally attached data from its key\r\n     * @param key the unique key that identifies the data\r\n     * @returns the associated data, if present (can be null), or undefined if not present\r\n     */\r\n    public getExternalData<T>(key: string): Nullable<T> {\r\n        if (!this._externalData) {\r\n            return null;\r\n        }\r\n        return <T>this._externalData.get(key);\r\n    }\r\n\r\n    /**\r\n     * Get an externally attached data from its key, create it using a factory if it's not already present\r\n     * @param key the unique key that identifies the data\r\n     * @param factory the factory that will be called to create the instance if and only if it doesn't exists\r\n     * @returns the associated data, can be null if the factory returned null.\r\n     */\r\n    public getOrAddExternalDataWithFactory<T extends object>(key: string, factory: (k: string) => T): T {\r\n        if (!this._externalData) {\r\n            this._externalData = new StringDictionary<object>();\r\n        }\r\n        return <T>this._externalData.getOrAddWithFactory(key, factory);\r\n    }\r\n\r\n    /**\r\n     * Remove an externally attached data from the Engine instance\r\n     * @param key the unique key that identifies the data\r\n     * @returns true if the data was successfully removed, false if it doesn't exist\r\n     */\r\n    public removeExternalData(key: string): boolean {\r\n        return this._externalData.remove(key);\r\n    }\r\n\r\n    private _evaluateSubMesh(subMesh: SubMesh, mesh: AbstractMesh, initialMesh: AbstractMesh, forcePush: boolean): void {\r\n        if (forcePush || subMesh.isInFrustum(this._frustumPlanes)) {\r\n            for (const step of this._evaluateSubMeshStage) {\r\n                step.action(mesh, subMesh);\r\n            }\r\n\r\n            const material = subMesh.getMaterial();\r\n            if (material !== null && material !== undefined) {\r\n                // Render targets\r\n                if (material.hasRenderTargetTextures && material.getRenderTargetTextures != null) {\r\n                    if (this._processedMaterials.indexOf(material) === -1) {\r\n                        this._processedMaterials.push(material);\r\n\r\n                        this._materialsRenderTargets.concatWithNoDuplicate(material.getRenderTargetTextures());\r\n                    }\r\n                }\r\n\r\n                // Dispatch\r\n                this._renderingManager.dispatch(subMesh, mesh, material);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the processed materials smart array preventing retention point in material dispose.\r\n     */\r\n    public freeProcessedMaterials(): void {\r\n        this._processedMaterials.dispose();\r\n    }\r\n\r\n    private _preventFreeActiveMeshesAndRenderingGroups = false;\r\n\r\n    /** Gets or sets a boolean blocking all the calls to freeActiveMeshes and freeRenderingGroups\r\n     * It can be used in order to prevent going through methods freeRenderingGroups and freeActiveMeshes several times to improve performance\r\n     * when disposing several meshes in a row or a hierarchy of meshes.\r\n     * When used, it is the responsibility of the user to blockfreeActiveMeshesAndRenderingGroups back to false.\r\n     */\r\n    public get blockfreeActiveMeshesAndRenderingGroups(): boolean {\r\n        return this._preventFreeActiveMeshesAndRenderingGroups;\r\n    }\r\n\r\n    public set blockfreeActiveMeshesAndRenderingGroups(value: boolean) {\r\n        if (this._preventFreeActiveMeshesAndRenderingGroups === value) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this.freeActiveMeshes();\r\n            this.freeRenderingGroups();\r\n        }\r\n\r\n        this._preventFreeActiveMeshesAndRenderingGroups = value;\r\n    }\r\n\r\n    /**\r\n     * Clear the active meshes smart array preventing retention point in mesh dispose.\r\n     */\r\n    public freeActiveMeshes(): void {\r\n        if (this.blockfreeActiveMeshesAndRenderingGroups) {\r\n            return;\r\n        }\r\n\r\n        this._activeMeshes.dispose();\r\n        if (this.activeCamera && this.activeCamera._activeMeshes) {\r\n            this.activeCamera._activeMeshes.dispose();\r\n        }\r\n        if (this.activeCameras) {\r\n            for (let i = 0; i < this.activeCameras.length; i++) {\r\n                const activeCamera = this.activeCameras[i];\r\n                if (activeCamera && activeCamera._activeMeshes) {\r\n                    activeCamera._activeMeshes.dispose();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clear the info related to rendering groups preventing retention points during dispose.\r\n     */\r\n    public freeRenderingGroups(): void {\r\n        if (this.blockfreeActiveMeshesAndRenderingGroups) {\r\n            return;\r\n        }\r\n\r\n        if (this._renderingManager) {\r\n            this._renderingManager.freeRenderingGroups();\r\n        }\r\n        if (this.textures) {\r\n            for (let i = 0; i < this.textures.length; i++) {\r\n                const texture = this.textures[i];\r\n                if (texture && (<RenderTargetTexture>texture).renderList) {\r\n                    (<RenderTargetTexture>texture).freeRenderingGroups();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _isInIntermediateRendering(): boolean {\r\n        return this._intermediateRendering;\r\n    }\r\n\r\n    /**\r\n     * Lambda returning the list of potentially active meshes.\r\n     */\r\n    public getActiveMeshCandidates: () => ISmartArrayLike<AbstractMesh>;\r\n\r\n    /**\r\n     * Lambda returning the list of potentially active sub meshes.\r\n     */\r\n    public getActiveSubMeshCandidates: (mesh: AbstractMesh) => ISmartArrayLike<SubMesh>;\r\n\r\n    /**\r\n     * Lambda returning the list of potentially intersecting sub meshes.\r\n     */\r\n    public getIntersectingSubMeshCandidates: (mesh: AbstractMesh, localRay: Ray) => ISmartArrayLike<SubMesh>;\r\n\r\n    /**\r\n     * Lambda returning the list of potentially colliding sub meshes.\r\n     */\r\n    public getCollidingSubMeshCandidates: (mesh: AbstractMesh, collider: Collider) => ISmartArrayLike<SubMesh>;\r\n\r\n    /** @internal */\r\n    public _activeMeshesFrozen = false;\r\n    /** @internal */\r\n    public _activeMeshesFrozenButKeepClipping = false;\r\n    private _skipEvaluateActiveMeshesCompletely = false;\r\n\r\n    /**\r\n     * Use this function to stop evaluating active meshes. The current list will be keep alive between frames\r\n     * @param skipEvaluateActiveMeshes defines an optional boolean indicating that the evaluate active meshes step must be completely skipped\r\n     * @param onSuccess optional success callback\r\n     * @param onError optional error callback\r\n     * @param freezeMeshes defines if meshes should be frozen (true by default)\r\n     * @param keepFrustumCulling defines if you want to keep running the frustum clipping (false by default)\r\n     * @returns the current scene\r\n     */\r\n    public freezeActiveMeshes(\r\n        skipEvaluateActiveMeshes = false,\r\n        onSuccess?: () => void,\r\n        onError?: (message: string) => void,\r\n        freezeMeshes = true,\r\n        keepFrustumCulling = false\r\n    ): Scene {\r\n        this.executeWhenReady(() => {\r\n            if (!this.activeCamera) {\r\n                if (onError) {\r\n                    onError(\"No active camera found\");\r\n                }\r\n                return;\r\n            }\r\n\r\n            if (!this._frustumPlanes) {\r\n                this.updateTransformMatrix();\r\n            }\r\n\r\n            this._evaluateActiveMeshes();\r\n            this._activeMeshesFrozen = true;\r\n            this._activeMeshesFrozenButKeepClipping = keepFrustumCulling;\r\n            this._skipEvaluateActiveMeshesCompletely = skipEvaluateActiveMeshes;\r\n\r\n            if (freezeMeshes) {\r\n                for (let index = 0; index < this._activeMeshes.length; index++) {\r\n                    this._activeMeshes.data[index]._freeze();\r\n                }\r\n            }\r\n            if (onSuccess) {\r\n                onSuccess();\r\n            }\r\n        });\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Use this function to restart evaluating active meshes on every frame\r\n     * @returns the current scene\r\n     */\r\n    public unfreezeActiveMeshes(): Scene {\r\n        for (let index = 0; index < this.meshes.length; index++) {\r\n            const mesh = this.meshes[index];\r\n            if (mesh._internalAbstractMeshDataInfo) {\r\n                mesh._internalAbstractMeshDataInfo._isActive = false;\r\n            }\r\n        }\r\n\r\n        for (let index = 0; index < this._activeMeshes.length; index++) {\r\n            this._activeMeshes.data[index]._unFreeze();\r\n        }\r\n\r\n        this._activeMeshesFrozen = false;\r\n        return this;\r\n    }\r\n\r\n    private _executeActiveContainerCleanup(container: SmartArray<any>) {\r\n        const isInFastMode = this._engine.snapshotRendering && this._engine.snapshotRenderingMode === Constants.SNAPSHOTRENDERING_FAST;\r\n\r\n        if (!isInFastMode && this._activeMeshesFrozen && this._activeMeshes.length) {\r\n            return; // Do not execute in frozen mode\r\n        }\r\n\r\n        // We need to ensure we are not in the rendering loop\r\n        this.onBeforeRenderObservable.addOnce(() => container.dispose());\r\n    }\r\n\r\n    private _evaluateActiveMeshes(): void {\r\n        if (this._engine.snapshotRendering && this._engine.snapshotRenderingMode === Constants.SNAPSHOTRENDERING_FAST) {\r\n            if (this._activeMeshes.length > 0) {\r\n                this.activeCamera?._activeMeshes.reset();\r\n                this._activeMeshes.reset();\r\n                this._renderingManager.reset();\r\n                this._processedMaterials.reset();\r\n                this._activeParticleSystems.reset();\r\n                this._activeSkeletons.reset();\r\n                this._softwareSkinnedMeshes.reset();\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (this._activeMeshesFrozen && this._activeMeshes.length) {\r\n            if (!this._skipEvaluateActiveMeshesCompletely) {\r\n                const len = this._activeMeshes.length;\r\n                for (let i = 0; i < len; i++) {\r\n                    const mesh = this._activeMeshes.data[i];\r\n                    mesh.computeWorldMatrix();\r\n                }\r\n            }\r\n\r\n            if (this._activeParticleSystems) {\r\n                const psLength = this._activeParticleSystems.length;\r\n                for (let i = 0; i < psLength; i++) {\r\n                    this._activeParticleSystems.data[i].animate();\r\n                }\r\n            }\r\n\r\n            this._renderingManager.resetSprites();\r\n\r\n            return;\r\n        }\r\n\r\n        if (!this.activeCamera) {\r\n            return;\r\n        }\r\n\r\n        this.onBeforeActiveMeshesEvaluationObservable.notifyObservers(this);\r\n\r\n        this.activeCamera._activeMeshes.reset();\r\n        this._activeMeshes.reset();\r\n        this._renderingManager.reset();\r\n        this._processedMaterials.reset();\r\n        this._activeParticleSystems.reset();\r\n        this._activeSkeletons.reset();\r\n        this._softwareSkinnedMeshes.reset();\r\n        this._materialsRenderTargets.reset();\r\n\r\n        for (const step of this._beforeEvaluateActiveMeshStage) {\r\n            step.action();\r\n        }\r\n\r\n        // Determine mesh candidates\r\n        const meshes = this.getActiveMeshCandidates();\r\n\r\n        // Check each mesh\r\n        const len = meshes.length;\r\n        for (let i = 0; i < len; i++) {\r\n            const mesh = meshes.data[i];\r\n            let currentLOD = mesh._internalAbstractMeshDataInfo._currentLOD.get(this.activeCamera);\r\n            if (currentLOD) {\r\n                currentLOD[1] = -1;\r\n            } else {\r\n                currentLOD = [mesh, -1];\r\n                mesh._internalAbstractMeshDataInfo._currentLOD.set(this.activeCamera, currentLOD);\r\n            }\r\n            if (mesh.isBlocked) {\r\n                continue;\r\n            }\r\n\r\n            this._totalVertices.addCount(mesh.getTotalVertices(), false);\r\n\r\n            if (!mesh.isReady() || !mesh.isEnabled() || mesh.scaling.hasAZeroComponent) {\r\n                continue;\r\n            }\r\n\r\n            mesh.computeWorldMatrix();\r\n\r\n            // Intersections\r\n            if (mesh.actionManager && mesh.actionManager.hasSpecificTriggers2(Constants.ACTION_OnIntersectionEnterTrigger, Constants.ACTION_OnIntersectionExitTrigger)) {\r\n                this._meshesForIntersections.pushNoDuplicate(mesh);\r\n            }\r\n\r\n            // Switch to current LOD\r\n            let meshToRender = this.customLODSelector ? this.customLODSelector(mesh, this.activeCamera) : mesh.getLOD(this.activeCamera);\r\n            currentLOD[0] = meshToRender;\r\n            currentLOD[1] = this._frameId;\r\n            if (meshToRender === undefined || meshToRender === null) {\r\n                continue;\r\n            }\r\n\r\n            // Compute world matrix if LOD is billboard\r\n            if (meshToRender !== mesh && meshToRender.billboardMode !== 0) {\r\n                meshToRender.computeWorldMatrix();\r\n            }\r\n\r\n            mesh._preActivate();\r\n\r\n            if (\r\n                mesh.isVisible &&\r\n                mesh.visibility > 0 &&\r\n                (mesh.layerMask & this.activeCamera.layerMask) !== 0 &&\r\n                (this._skipFrustumClipping || mesh.alwaysSelectAsActiveMesh || mesh.isInFrustum(this._frustumPlanes))\r\n            ) {\r\n                this._activeMeshes.push(mesh);\r\n                this.activeCamera._activeMeshes.push(mesh);\r\n\r\n                if (meshToRender !== mesh) {\r\n                    meshToRender._activate(this._renderId, false);\r\n                }\r\n\r\n                for (const step of this._preActiveMeshStage) {\r\n                    step.action(mesh);\r\n                }\r\n\r\n                if (mesh._activate(this._renderId, false)) {\r\n                    if (!mesh.isAnInstance) {\r\n                        meshToRender._internalAbstractMeshDataInfo._onlyForInstances = false;\r\n                    } else {\r\n                        if (mesh._internalAbstractMeshDataInfo._actAsRegularMesh) {\r\n                            meshToRender = mesh;\r\n                        }\r\n                    }\r\n                    meshToRender._internalAbstractMeshDataInfo._isActive = true;\r\n                    this._activeMesh(mesh, meshToRender);\r\n                }\r\n\r\n                mesh._postActivate();\r\n            }\r\n        }\r\n\r\n        this.onAfterActiveMeshesEvaluationObservable.notifyObservers(this);\r\n\r\n        // Particle systems\r\n        if (this.particlesEnabled) {\r\n            this.onBeforeParticlesRenderingObservable.notifyObservers(this);\r\n            for (let particleIndex = 0; particleIndex < this.particleSystems.length; particleIndex++) {\r\n                const particleSystem = this.particleSystems[particleIndex];\r\n\r\n                if (!particleSystem.isStarted() || !particleSystem.emitter) {\r\n                    continue;\r\n                }\r\n\r\n                const emitter = <any>particleSystem.emitter;\r\n                if (!emitter.position || emitter.isEnabled()) {\r\n                    this._activeParticleSystems.push(particleSystem);\r\n                    particleSystem.animate();\r\n                    this._renderingManager.dispatchParticles(particleSystem);\r\n                }\r\n            }\r\n            this.onAfterParticlesRenderingObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepareSkeleton(mesh: AbstractMesh): void {\r\n        if (!this._skeletonsEnabled || !mesh.skeleton) {\r\n            return;\r\n        }\r\n\r\n        if (this._activeSkeletons.pushNoDuplicate(mesh.skeleton)) {\r\n            mesh.skeleton.prepare();\r\n            this._activeBones.addCount(mesh.skeleton.bones.length, false);\r\n        }\r\n\r\n        if (!mesh.computeBonesUsingShaders) {\r\n            if (this._softwareSkinnedMeshes.pushNoDuplicate(<Mesh>mesh) && this.frameGraph) {\r\n                (<Mesh>mesh).applySkeleton(mesh.skeleton);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _activeMesh(sourceMesh: AbstractMesh, mesh: AbstractMesh): void {\r\n        this._prepareSkeleton(mesh);\r\n\r\n        let forcePush = sourceMesh.hasInstances || sourceMesh.isAnInstance || this.dispatchAllSubMeshesOfActiveMeshes || this._skipFrustumClipping || mesh.alwaysSelectAsActiveMesh;\r\n\r\n        if (mesh && mesh.subMeshes && mesh.subMeshes.length > 0) {\r\n            const subMeshes = this.getActiveSubMeshCandidates(mesh);\r\n            const len = subMeshes.length;\r\n            forcePush = forcePush || len === 1;\r\n            for (let i = 0; i < len; i++) {\r\n                const subMesh = subMeshes.data[i];\r\n                this._evaluateSubMesh(subMesh, mesh, sourceMesh, forcePush);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the transform matrix to update from the current active camera\r\n     * @param force defines a boolean used to force the update even if cache is up to date\r\n     */\r\n    public updateTransformMatrix(force?: boolean): void {\r\n        const activeCamera = this.activeCamera;\r\n        if (!activeCamera) {\r\n            return;\r\n        }\r\n\r\n        if (activeCamera._renderingMultiview) {\r\n            const leftCamera = activeCamera._rigCameras[0];\r\n            const rightCamera = activeCamera._rigCameras[1];\r\n            this.setTransformMatrix(leftCamera.getViewMatrix(), leftCamera.getProjectionMatrix(force), rightCamera.getViewMatrix(), rightCamera.getProjectionMatrix(force));\r\n        } else {\r\n            this.setTransformMatrix(activeCamera.getViewMatrix(), activeCamera.getProjectionMatrix(force));\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _useCurrentFrameBuffer = false;\r\n\r\n    private _bindFrameBuffer(camera: Nullable<Camera>, clear = true) {\r\n        if (!this._useCurrentFrameBuffer) {\r\n            if (camera && camera._multiviewTexture) {\r\n                camera._multiviewTexture._bindFrameBuffer();\r\n            } else if (camera && camera.outputRenderTarget) {\r\n                camera.outputRenderTarget._bindFrameBuffer();\r\n            } else {\r\n                if (!this._engine._currentFrameBufferIsDefaultFrameBuffer()) {\r\n                    this._engine.restoreDefaultFramebuffer();\r\n                }\r\n            }\r\n        }\r\n        if (clear) {\r\n            this._clearFrameBuffer(camera);\r\n        }\r\n    }\r\n\r\n    private _clearFrameBuffer(camera: Nullable<Camera>) {\r\n        // we assume the framebuffer currently bound is the right one\r\n        if (camera && camera._multiviewTexture) {\r\n            // no clearing\r\n        } else if (camera && camera.outputRenderTarget && !camera._renderingMultiview) {\r\n            const rtt = camera.outputRenderTarget;\r\n            if (rtt.onClearObservable.hasObservers()) {\r\n                rtt.onClearObservable.notifyObservers(this._engine);\r\n            } else if (!rtt.skipInitialClear && !camera.isRightCamera) {\r\n                if (this.autoClear) {\r\n                    this._engine.clear(rtt.clearColor || this._clearColor, !rtt._cleared, true, true);\r\n                }\r\n                rtt._cleared = true;\r\n            }\r\n        } else {\r\n            if (!this._defaultFrameBufferCleared) {\r\n                this._defaultFrameBufferCleared = true;\r\n                this._clear();\r\n            } else {\r\n                this._engine.clear(null, false, true, true);\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _allowPostProcessClearColor = true;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _renderForCamera(camera: Camera, rigParent?: Camera, bindFrameBuffer = true): void {\r\n        if (camera && camera._skipRendering) {\r\n            return;\r\n        }\r\n\r\n        const engine = this._engine;\r\n\r\n        // Use _activeCamera instead of activeCamera to avoid onActiveCameraChanged\r\n        this._activeCamera = camera;\r\n\r\n        if (!this.activeCamera) {\r\n            throw new Error(\"Active camera not set\");\r\n        }\r\n\r\n        // Viewport\r\n        engine.setViewport(this.activeCamera.viewport);\r\n\r\n        // Camera\r\n        this.resetCachedMaterial();\r\n        this._renderId++;\r\n\r\n        if (!this.prePass && bindFrameBuffer) {\r\n            let skipInitialClear = true;\r\n            if (camera._renderingMultiview && camera.outputRenderTarget) {\r\n                skipInitialClear = camera.outputRenderTarget.skipInitialClear;\r\n                if (this.autoClear) {\r\n                    this._defaultFrameBufferCleared = false;\r\n                    camera.outputRenderTarget.skipInitialClear = false;\r\n                }\r\n            }\r\n            this._bindFrameBuffer(this._activeCamera);\r\n            if (camera._renderingMultiview && camera.outputRenderTarget) {\r\n                camera.outputRenderTarget.skipInitialClear = skipInitialClear;\r\n            }\r\n        }\r\n\r\n        this.updateTransformMatrix();\r\n\r\n        this.onBeforeCameraRenderObservable.notifyObservers(this.activeCamera);\r\n\r\n        // Meshes\r\n        this._evaluateActiveMeshes();\r\n\r\n        // Software skinning\r\n        for (let softwareSkinnedMeshIndex = 0; softwareSkinnedMeshIndex < this._softwareSkinnedMeshes.length; softwareSkinnedMeshIndex++) {\r\n            const mesh = this._softwareSkinnedMeshes.data[softwareSkinnedMeshIndex];\r\n\r\n            mesh.applySkeleton(<Skeleton>mesh.skeleton);\r\n        }\r\n\r\n        // Render targets\r\n        this.onBeforeRenderTargetsRenderObservable.notifyObservers(this);\r\n\r\n        this._renderTargets.concatWithNoDuplicate(this._materialsRenderTargets);\r\n\r\n        if (camera.customRenderTargets && camera.customRenderTargets.length > 0) {\r\n            this._renderTargets.concatWithNoDuplicate(camera.customRenderTargets);\r\n        }\r\n\r\n        if (rigParent && rigParent.customRenderTargets && rigParent.customRenderTargets.length > 0) {\r\n            this._renderTargets.concatWithNoDuplicate(rigParent.customRenderTargets);\r\n        }\r\n\r\n        if (this.environmentTexture && this.environmentTexture.isRenderTarget) {\r\n            this._renderTargets.pushNoDuplicate(this.environmentTexture as RenderTargetTexture);\r\n        }\r\n\r\n        // Collects render targets from external components.\r\n        for (const step of this._gatherActiveCameraRenderTargetsStage) {\r\n            step.action(this._renderTargets);\r\n        }\r\n\r\n        let needRebind = false;\r\n        if (this.renderTargetsEnabled) {\r\n            this._intermediateRendering = true;\r\n\r\n            if (this._renderTargets.length > 0) {\r\n                Tools.StartPerformanceCounter(\"Render targets\", this._renderTargets.length > 0);\r\n                for (let renderIndex = 0; renderIndex < this._renderTargets.length; renderIndex++) {\r\n                    const renderTarget = this._renderTargets.data[renderIndex];\r\n                    if (renderTarget._shouldRender()) {\r\n                        this._renderId++;\r\n                        const hasSpecialRenderTargetCamera = renderTarget.activeCamera && renderTarget.activeCamera !== this.activeCamera;\r\n                        renderTarget.render(<boolean>hasSpecialRenderTargetCamera, this.dumpNextRenderTargets);\r\n                        needRebind = true;\r\n                    }\r\n                }\r\n                Tools.EndPerformanceCounter(\"Render targets\", this._renderTargets.length > 0);\r\n\r\n                this._renderId++;\r\n            }\r\n\r\n            for (const step of this._cameraDrawRenderTargetStage) {\r\n                needRebind = step.action(this.activeCamera) || needRebind;\r\n            }\r\n\r\n            this._intermediateRendering = false;\r\n        }\r\n\r\n        this._engine.currentRenderPassId = camera.outputRenderTarget?.renderPassId ?? camera.renderPassId ?? Constants.RENDERPASS_MAIN;\r\n\r\n        // Restore framebuffer after rendering to targets\r\n        if (needRebind && !this.prePass) {\r\n            this._bindFrameBuffer(this._activeCamera, false);\r\n            this.updateTransformMatrix();\r\n        }\r\n\r\n        this.onAfterRenderTargetsRenderObservable.notifyObservers(this);\r\n\r\n        // Prepare Frame\r\n        if (this.postProcessManager && !camera._multiviewTexture && !this.prePass) {\r\n            this.postProcessManager._prepareFrame();\r\n        }\r\n\r\n        // Before Camera Draw\r\n        for (const step of this._beforeCameraDrawStage) {\r\n            step.action(this.activeCamera);\r\n        }\r\n\r\n        // Render\r\n        this.onBeforeDrawPhaseObservable.notifyObservers(this);\r\n\r\n        if (engine.snapshotRendering && engine.snapshotRenderingMode === Constants.SNAPSHOTRENDERING_FAST) {\r\n            this.finalizeSceneUbo();\r\n        }\r\n        this._renderingManager.render(null, null, true, true);\r\n        this.onAfterDrawPhaseObservable.notifyObservers(this);\r\n\r\n        // After Camera Draw\r\n        for (const step of this._afterCameraDrawStage) {\r\n            step.action(this.activeCamera);\r\n        }\r\n\r\n        // Finalize frame\r\n        if (this.postProcessManager && !camera._multiviewTexture) {\r\n            // if the camera has an output render target, render the post process to the render target\r\n            const texture = camera.outputRenderTarget ? camera.outputRenderTarget.renderTarget! : undefined;\r\n            this.postProcessManager._finalizeFrame(camera.isIntermediate, texture);\r\n        }\r\n\r\n        // After post process\r\n        for (const step of this._afterCameraPostProcessStage) {\r\n            step.action(this.activeCamera);\r\n        }\r\n\r\n        // Reset some special arrays\r\n        this._renderTargets.reset();\r\n\r\n        this.onAfterCameraRenderObservable.notifyObservers(this.activeCamera);\r\n    }\r\n\r\n    private _processSubCameras(camera: Camera, bindFrameBuffer = true): void {\r\n        if (camera.cameraRigMode === Constants.RIG_MODE_NONE || camera._renderingMultiview) {\r\n            if (camera._renderingMultiview && !this._multiviewSceneUbo) {\r\n                this._createMultiviewUbo();\r\n            }\r\n            this._renderForCamera(camera, undefined, bindFrameBuffer);\r\n            this.onAfterRenderCameraObservable.notifyObservers(camera);\r\n            return;\r\n        }\r\n\r\n        if (camera._useMultiviewToSingleView) {\r\n            this._renderMultiviewToSingleView(camera);\r\n        } else {\r\n            // rig cameras\r\n            this.onBeforeCameraRenderObservable.notifyObservers(camera);\r\n            for (let index = 0; index < camera._rigCameras.length; index++) {\r\n                this._renderForCamera(camera._rigCameras[index], camera);\r\n            }\r\n        }\r\n\r\n        // Use _activeCamera instead of activeCamera to avoid onActiveCameraChanged\r\n        this._activeCamera = camera;\r\n        this.updateTransformMatrix();\r\n        this.onAfterRenderCameraObservable.notifyObservers(camera);\r\n    }\r\n\r\n    private _checkIntersections(): void {\r\n        for (let index = 0; index < this._meshesForIntersections.length; index++) {\r\n            const sourceMesh = this._meshesForIntersections.data[index];\r\n\r\n            if (!sourceMesh.actionManager) {\r\n                continue;\r\n            }\r\n\r\n            for (let actionIndex = 0; sourceMesh.actionManager && actionIndex < sourceMesh.actionManager.actions.length; actionIndex++) {\r\n                const action: IAction = sourceMesh.actionManager.actions[actionIndex];\r\n\r\n                if (action.trigger === Constants.ACTION_OnIntersectionEnterTrigger || action.trigger === Constants.ACTION_OnIntersectionExitTrigger) {\r\n                    const parameters = action.getTriggerParameter();\r\n                    const otherMesh = parameters.mesh ? parameters.mesh : parameters;\r\n\r\n                    const areIntersecting = otherMesh.intersectsMesh(sourceMesh, parameters.usePreciseIntersection);\r\n                    const currentIntersectionInProgress = sourceMesh._intersectionsInProgress.indexOf(otherMesh);\r\n\r\n                    if (areIntersecting && currentIntersectionInProgress === -1) {\r\n                        if (action.trigger === Constants.ACTION_OnIntersectionEnterTrigger) {\r\n                            action._executeCurrent(ActionEvent.CreateNew(sourceMesh, undefined, otherMesh));\r\n                            sourceMesh._intersectionsInProgress.push(otherMesh);\r\n                        } else if (action.trigger === Constants.ACTION_OnIntersectionExitTrigger) {\r\n                            sourceMesh._intersectionsInProgress.push(otherMesh);\r\n                        }\r\n                    } else if (!areIntersecting && currentIntersectionInProgress > -1) {\r\n                        //They intersected, and now they don't.\r\n\r\n                        //is this trigger an exit trigger? execute an event.\r\n                        if (action.trigger === Constants.ACTION_OnIntersectionExitTrigger) {\r\n                            action._executeCurrent(ActionEvent.CreateNew(sourceMesh, undefined, otherMesh));\r\n                        }\r\n\r\n                        //if this is an exit trigger, or no exit trigger exists, remove the id from the intersection in progress array.\r\n                        if (\r\n                            !sourceMesh.actionManager.hasSpecificTrigger(Constants.ACTION_OnIntersectionExitTrigger, (parameter) => {\r\n                                const parameterMesh = parameter.mesh ? parameter.mesh : parameter;\r\n                                return otherMesh === parameterMesh;\r\n                            }) ||\r\n                            action.trigger === Constants.ACTION_OnIntersectionExitTrigger\r\n                        ) {\r\n                            sourceMesh._intersectionsInProgress.splice(currentIntersectionInProgress, 1);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _advancePhysicsEngineStep(step: number) {\r\n        // Do nothing. Code will be replaced if physics engine component is referenced\r\n    }\r\n\r\n    /**\r\n     * User updatable function that will return a deterministic frame time when engine is in deterministic lock step mode\r\n     * @returns the frame time\r\n     */\r\n    public getDeterministicFrameTime: () => number = () => {\r\n        return this._engine.getTimeStep();\r\n    };\r\n\r\n    /** @internal */\r\n    public _animate(customDeltaTime?: number): void {\r\n        // Nothing to do as long as Animatable have not been imported.\r\n    }\r\n\r\n    /** Execute all animations (for a frame) */\r\n    public animate() {\r\n        if (this._engine.isDeterministicLockStep()) {\r\n            let deltaTime = Math.max(Scene.MinDeltaTime, Math.min(this._engine.getDeltaTime(), Scene.MaxDeltaTime)) + this._timeAccumulator;\r\n\r\n            const defaultFrameTime = this._engine.getTimeStep();\r\n            const defaultFPS = 1000.0 / defaultFrameTime / 1000.0;\r\n\r\n            let stepsTaken = 0;\r\n\r\n            const maxSubSteps = this._engine.getLockstepMaxSteps();\r\n\r\n            let internalSteps = Math.floor(deltaTime / defaultFrameTime);\r\n            internalSteps = Math.min(internalSteps, maxSubSteps);\r\n\r\n            while (deltaTime > 0 && stepsTaken < internalSteps) {\r\n                this.onBeforeStepObservable.notifyObservers(this);\r\n\r\n                // Animations\r\n                this._animationRatio = defaultFrameTime * defaultFPS;\r\n                this._animate(defaultFrameTime);\r\n                this.onAfterAnimationsObservable.notifyObservers(this);\r\n\r\n                // Physics\r\n                if (this.physicsEnabled) {\r\n                    this._advancePhysicsEngineStep(defaultFrameTime);\r\n                }\r\n\r\n                this.onAfterStepObservable.notifyObservers(this);\r\n                this._currentStepId++;\r\n\r\n                stepsTaken++;\r\n                deltaTime -= defaultFrameTime;\r\n            }\r\n\r\n            this._timeAccumulator = deltaTime < 0 ? 0 : deltaTime;\r\n        } else {\r\n            // Animations\r\n            const deltaTime = this.useConstantAnimationDeltaTime ? 16 : Math.max(Scene.MinDeltaTime, Math.min(this._engine.getDeltaTime(), Scene.MaxDeltaTime));\r\n            this._animationRatio = deltaTime * (60.0 / 1000.0);\r\n            this._animate();\r\n            this.onAfterAnimationsObservable.notifyObservers(this);\r\n\r\n            // Physics\r\n            if (this.physicsEnabled) {\r\n                this._advancePhysicsEngineStep(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _clear(): void {\r\n        if (this.autoClearDepthAndStencil || this.autoClear) {\r\n            this._engine.clear(this._clearColor, this.autoClear || this.forceWireframe || this.forcePointsCloud, this.autoClearDepthAndStencil, this.autoClearDepthAndStencil);\r\n        }\r\n    }\r\n\r\n    private _checkCameraRenderTarget(camera: Nullable<Camera>) {\r\n        if (camera?.outputRenderTarget && !camera?.isRigCamera) {\r\n            camera.outputRenderTarget._cleared = false;\r\n        }\r\n        if (camera?.rigCameras?.length) {\r\n            for (let i = 0; i < camera.rigCameras.length; ++i) {\r\n                const rtt = camera.rigCameras[i].outputRenderTarget;\r\n                if (rtt) {\r\n                    rtt._cleared = false;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resets the draw wrappers cache of all meshes\r\n     * @param passId If provided, releases only the draw wrapper corresponding to this render pass id\r\n     */\r\n    public resetDrawCache(passId?: number): void {\r\n        if (!this.meshes) {\r\n            return;\r\n        }\r\n\r\n        for (const mesh of this.meshes) {\r\n            mesh.resetDrawCache(passId);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If this function is defined it will take precedence over the standard render() function.\r\n     */\r\n    public customRenderFunction?: (updateCameras: boolean, ignoreAnimations: boolean) => void;\r\n\r\n    private _renderWithFrameGraph(updateCameras = true, ignoreAnimations = false): void {\r\n        this.activeCamera = null;\r\n\r\n        this._activeParticleSystems.reset();\r\n        this._activeSkeletons.reset();\r\n\r\n        // Update Cameras\r\n        if (updateCameras) {\r\n            for (const camera of this.cameras) {\r\n                camera.update();\r\n                if (camera.cameraRigMode !== Constants.RIG_MODE_NONE) {\r\n                    // rig cameras\r\n                    for (let index = 0; index < camera._rigCameras.length; index++) {\r\n                        camera._rigCameras[index].update();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this.onBeforeRenderObservable.notifyObservers(this);\r\n\r\n        // We must keep these steps because the procedural texture component relies on them.\r\n        // TODO: move the procedural texture component to the frame graph.\r\n        for (const step of this._beforeClearStage) {\r\n            step.action();\r\n        }\r\n\r\n        // Process meshes\r\n        const meshes = this.getActiveMeshCandidates();\r\n        const len = meshes.length;\r\n\r\n        for (let i = 0; i < len; i++) {\r\n            const mesh = meshes.data[i];\r\n\r\n            if (mesh.isBlocked) {\r\n                continue;\r\n            }\r\n\r\n            this._totalVertices.addCount(mesh.getTotalVertices(), false);\r\n\r\n            if (!mesh.isReady() || !mesh.isEnabled() || mesh.scaling.hasAZeroComponent) {\r\n                continue;\r\n            }\r\n\r\n            mesh.computeWorldMatrix();\r\n\r\n            if (mesh.actionManager && mesh.actionManager.hasSpecificTriggers2(Constants.ACTION_OnIntersectionEnterTrigger, Constants.ACTION_OnIntersectionExitTrigger)) {\r\n                this._meshesForIntersections.pushNoDuplicate(mesh);\r\n            }\r\n        }\r\n\r\n        // Animate Particle systems\r\n        if (this.particlesEnabled) {\r\n            for (let particleIndex = 0; particleIndex < this.particleSystems.length; particleIndex++) {\r\n                const particleSystem = this.particleSystems[particleIndex];\r\n\r\n                if (!particleSystem.isStarted() || !particleSystem.emitter) {\r\n                    continue;\r\n                }\r\n\r\n                const emitter = <any>particleSystem.emitter;\r\n                if (!emitter.position || emitter.isEnabled()) {\r\n                    this._activeParticleSystems.push(particleSystem);\r\n                    particleSystem.animate();\r\n                }\r\n            }\r\n        }\r\n\r\n        // Render the graph\r\n        this.frameGraph?.execute();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _renderRenderTarget(renderTarget: RenderTargetTexture, activeCamera: Nullable<Camera>, useCameraPostProcess = false, dumpForDebug = false) {\r\n        this._intermediateRendering = true;\r\n        if (renderTarget._shouldRender()) {\r\n            this._renderId++;\r\n\r\n            this.activeCamera = activeCamera;\r\n\r\n            if (!this.activeCamera) {\r\n                throw new Error(\"Active camera not set\");\r\n            }\r\n\r\n            // Viewport\r\n            this._engine.setViewport(this.activeCamera.viewport);\r\n\r\n            // Camera\r\n            this.updateTransformMatrix();\r\n\r\n            renderTarget.render(useCameraPostProcess, dumpForDebug);\r\n        }\r\n        this._intermediateRendering = false;\r\n    }\r\n\r\n    /**\r\n     * Render the scene\r\n     * @param updateCameras defines a boolean indicating if cameras must update according to their inputs (true by default)\r\n     * @param ignoreAnimations defines a boolean indicating if animations should not be executed (false by default)\r\n     */\r\n    public render(updateCameras = true, ignoreAnimations = false): void {\r\n        if (this.isDisposed) {\r\n            return;\r\n        }\r\n\r\n        if (this.onReadyObservable.hasObservers() && this._executeWhenReadyTimeoutId === null) {\r\n            this._checkIsReady();\r\n        }\r\n\r\n        this._frameId++;\r\n        this._defaultFrameBufferCleared = false;\r\n        this._checkCameraRenderTarget(this.activeCamera);\r\n        if (this.activeCameras?.length) {\r\n            for (const c of this.activeCameras) {\r\n                this._checkCameraRenderTarget(c);\r\n            }\r\n        }\r\n\r\n        // Register components that have been associated lately to the scene.\r\n        this._registerTransientComponents();\r\n\r\n        this._activeParticles.fetchNewFrame();\r\n        this._totalVertices.fetchNewFrame();\r\n        this._activeIndices.fetchNewFrame();\r\n        this._activeBones.fetchNewFrame();\r\n        this._meshesForIntersections.reset();\r\n        this.resetCachedMaterial();\r\n\r\n        this.onBeforeAnimationsObservable.notifyObservers(this);\r\n\r\n        // Actions\r\n        if (this.actionManager) {\r\n            this.actionManager.processTrigger(Constants.ACTION_OnEveryFrameTrigger);\r\n        }\r\n\r\n        // Animations\r\n        if (!ignoreAnimations) {\r\n            this.animate();\r\n        }\r\n\r\n        // Before camera update steps\r\n        for (const step of this._beforeCameraUpdateStage) {\r\n            step.action();\r\n        }\r\n\r\n        // Update Cameras\r\n        if (updateCameras) {\r\n            if (this.activeCameras && this.activeCameras.length > 0) {\r\n                for (let cameraIndex = 0; cameraIndex < this.activeCameras.length; cameraIndex++) {\r\n                    const camera = this.activeCameras[cameraIndex];\r\n                    camera.update();\r\n                    if (camera.cameraRigMode !== Constants.RIG_MODE_NONE) {\r\n                        // rig cameras\r\n                        for (let index = 0; index < camera._rigCameras.length; index++) {\r\n                            camera._rigCameras[index].update();\r\n                        }\r\n                    }\r\n                }\r\n            } else if (this.activeCamera) {\r\n                this.activeCamera.update();\r\n                if (this.activeCamera.cameraRigMode !== Constants.RIG_MODE_NONE) {\r\n                    // rig cameras\r\n                    for (let index = 0; index < this.activeCamera._rigCameras.length; index++) {\r\n                        this.activeCamera._rigCameras[index].update();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Custom render function?\r\n        if (this.customRenderFunction) {\r\n            this._renderId++;\r\n            this._engine.currentRenderPassId = Constants.RENDERPASS_MAIN;\r\n\r\n            this.customRenderFunction(updateCameras, ignoreAnimations);\r\n        } else {\r\n            // Before render\r\n            this.onBeforeRenderObservable.notifyObservers(this);\r\n\r\n            // Customs render targets\r\n            this.onBeforeRenderTargetsRenderObservable.notifyObservers(this);\r\n\r\n            const currentActiveCamera = this.activeCameras?.length ? this.activeCameras[0] : this.activeCamera;\r\n            if (this.renderTargetsEnabled) {\r\n                Tools.StartPerformanceCounter(\"Custom render targets\", this.customRenderTargets.length > 0);\r\n                for (let customIndex = 0; customIndex < this.customRenderTargets.length; customIndex++) {\r\n                    const renderTarget = this.customRenderTargets[customIndex];\r\n                    const activeCamera = renderTarget.activeCamera || this.activeCamera;\r\n\r\n                    this._renderRenderTarget(renderTarget, activeCamera, currentActiveCamera !== activeCamera, this.dumpNextRenderTargets);\r\n                }\r\n                Tools.EndPerformanceCounter(\"Custom render targets\", this.customRenderTargets.length > 0);\r\n                this._renderId++;\r\n            }\r\n\r\n            this._engine.currentRenderPassId = currentActiveCamera?.renderPassId ?? Constants.RENDERPASS_MAIN;\r\n\r\n            // Restore back buffer\r\n            this.activeCamera = currentActiveCamera;\r\n            if (this._activeCamera && this._activeCamera.cameraRigMode !== Constants.RIG_MODE_CUSTOM && !this.prePass) {\r\n                this._bindFrameBuffer(this._activeCamera, false);\r\n            }\r\n            this.onAfterRenderTargetsRenderObservable.notifyObservers(this);\r\n\r\n            for (const step of this._beforeClearStage) {\r\n                step.action();\r\n            }\r\n\r\n            // Clear\r\n            this._clearFrameBuffer(this.activeCamera);\r\n\r\n            // Collects render targets from external components.\r\n            for (const step of this._gatherRenderTargetsStage) {\r\n                step.action(this._renderTargets);\r\n            }\r\n\r\n            // Multi-cameras?\r\n            if (this.activeCameras && this.activeCameras.length > 0) {\r\n                for (let cameraIndex = 0; cameraIndex < this.activeCameras.length; cameraIndex++) {\r\n                    this._processSubCameras(this.activeCameras[cameraIndex], cameraIndex > 0);\r\n                }\r\n            } else {\r\n                if (!this.activeCamera) {\r\n                    throw new Error(\"No camera defined\");\r\n                }\r\n\r\n                this._processSubCameras(this.activeCamera, !!this.activeCamera.outputRenderTarget);\r\n            }\r\n        }\r\n\r\n        // Intersection checks\r\n        this._checkIntersections();\r\n\r\n        // Executes the after render stage actions.\r\n        for (const step of this._afterRenderStage) {\r\n            step.action();\r\n        }\r\n\r\n        // After render\r\n        if (this.afterRender) {\r\n            this.afterRender();\r\n        }\r\n\r\n        this.onAfterRenderObservable.notifyObservers(this);\r\n\r\n        // Cleaning\r\n        if (this._toBeDisposed.length) {\r\n            for (let index = 0; index < this._toBeDisposed.length; index++) {\r\n                const data = this._toBeDisposed[index];\r\n                if (data) {\r\n                    data.dispose();\r\n                }\r\n            }\r\n\r\n            this._toBeDisposed.length = 0;\r\n        }\r\n\r\n        if (this.dumpNextRenderTargets) {\r\n            this.dumpNextRenderTargets = false;\r\n        }\r\n\r\n        this._activeBones.addCount(0, true);\r\n        this._activeIndices.addCount(0, true);\r\n        this._activeParticles.addCount(0, true);\r\n\r\n        this._engine.restoreDefaultFramebuffer();\r\n    }\r\n\r\n    /**\r\n     * Freeze all materials\r\n     * A frozen material will not be updatable but should be faster to render\r\n     * Note: multimaterials will not be frozen, but their submaterials will\r\n     */\r\n    public freezeMaterials(): void {\r\n        for (let i = 0; i < this.materials.length; i++) {\r\n            this.materials[i].freeze();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Unfreeze all materials\r\n     * A frozen material will not be updatable but should be faster to render\r\n     */\r\n    public unfreezeMaterials(): void {\r\n        for (let i = 0; i < this.materials.length; i++) {\r\n            this.materials[i].unfreeze();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Releases all held resources\r\n     */\r\n    public dispose(): void {\r\n        if (this.isDisposed) {\r\n            return;\r\n        }\r\n\r\n        this.beforeRender = null;\r\n        this.afterRender = null;\r\n        this.metadata = null;\r\n\r\n        this.skeletons.length = 0;\r\n        this.morphTargetManagers.length = 0;\r\n        this._transientComponents.length = 0;\r\n        this._isReadyForMeshStage.clear();\r\n        this._beforeEvaluateActiveMeshStage.clear();\r\n        this._evaluateSubMeshStage.clear();\r\n        this._preActiveMeshStage.clear();\r\n        this._cameraDrawRenderTargetStage.clear();\r\n        this._beforeCameraDrawStage.clear();\r\n        this._beforeRenderTargetDrawStage.clear();\r\n        this._beforeRenderingGroupDrawStage.clear();\r\n        this._beforeRenderingMeshStage.clear();\r\n        this._afterRenderingMeshStage.clear();\r\n        this._afterRenderingGroupDrawStage.clear();\r\n        this._afterCameraDrawStage.clear();\r\n        this._afterRenderTargetDrawStage.clear();\r\n        this._afterRenderStage.clear();\r\n        this._beforeCameraUpdateStage.clear();\r\n        this._beforeClearStage.clear();\r\n        this._gatherRenderTargetsStage.clear();\r\n        this._gatherActiveCameraRenderTargetsStage.clear();\r\n        this._pointerMoveStage.clear();\r\n        this._pointerDownStage.clear();\r\n        this._pointerUpStage.clear();\r\n\r\n        this.importedMeshesFiles = [] as string[];\r\n\r\n        if (this._activeAnimatables && this.stopAllAnimations) {\r\n            // Ensures that no animatable notifies a callback that could start a new animation group, constantly adding new animatables to the active list...\r\n            for (const animatable of this._activeAnimatables) {\r\n                animatable.onAnimationEndObservable.clear();\r\n                animatable.onAnimationEnd = null;\r\n            }\r\n            this.stopAllAnimations();\r\n        }\r\n\r\n        this.resetCachedMaterial();\r\n\r\n        // Smart arrays\r\n        if (this.activeCamera) {\r\n            this.activeCamera._activeMeshes.dispose();\r\n            this.activeCamera = null;\r\n        }\r\n        this.activeCameras = null;\r\n\r\n        this._activeMeshes.dispose();\r\n        this._renderingManager.dispose();\r\n        this._processedMaterials.dispose();\r\n        this._activeParticleSystems.dispose();\r\n        this._activeSkeletons.dispose();\r\n        this._softwareSkinnedMeshes.dispose();\r\n        this._renderTargets.dispose();\r\n        this._materialsRenderTargets.dispose();\r\n        this._registeredForLateAnimationBindings.dispose();\r\n        this._meshesForIntersections.dispose();\r\n        this._toBeDisposed.length = 0;\r\n\r\n        // Abort active requests\r\n        const activeRequests = this._activeRequests.slice();\r\n        for (const request of activeRequests) {\r\n            request.abort();\r\n        }\r\n        this._activeRequests.length = 0;\r\n\r\n        // Events\r\n        try {\r\n            this.onDisposeObservable.notifyObservers(this);\r\n        } catch (e) {\r\n            Logger.Error(\"An error occurred while calling onDisposeObservable!\", e);\r\n        }\r\n\r\n        this.detachControl();\r\n\r\n        // Detach cameras\r\n        const canvas = this._engine.getInputElement();\r\n\r\n        if (canvas) {\r\n            for (let index = 0; index < this.cameras.length; index++) {\r\n                this.cameras[index].detachControl();\r\n            }\r\n        }\r\n\r\n        // Release animation groups\r\n        this._disposeList(this.animationGroups);\r\n\r\n        // Release lights\r\n        this._disposeList(this.lights);\r\n\r\n        // Release materials\r\n        if (this._defaultMaterial) {\r\n            this._defaultMaterial.dispose();\r\n        }\r\n        this._disposeList(this.multiMaterials);\r\n        this._disposeList(this.materials);\r\n\r\n        // Release meshes\r\n        this._disposeList(this.meshes, (item) => item.dispose(true));\r\n        this._disposeList(this.transformNodes, (item) => item.dispose(true));\r\n\r\n        // Release cameras\r\n        const cameras = this.cameras;\r\n        this._disposeList(cameras);\r\n\r\n        // Release particles\r\n        this._disposeList(this.particleSystems);\r\n\r\n        // Release postProcesses\r\n        this._disposeList(this.postProcesses);\r\n\r\n        // Release textures\r\n        this._disposeList(this.textures);\r\n\r\n        // Release morph targets\r\n        this._disposeList(this.morphTargetManagers);\r\n\r\n        // Release UBO\r\n        this._sceneUbo.dispose();\r\n\r\n        if (this._multiviewSceneUbo) {\r\n            this._multiviewSceneUbo.dispose();\r\n        }\r\n\r\n        // Post-processes\r\n        this.postProcessManager.dispose();\r\n\r\n        // Components\r\n        this._disposeList(this._components);\r\n\r\n        // Remove from engine\r\n        let index = this._engine.scenes.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._engine.scenes.splice(index, 1);\r\n        }\r\n\r\n        if (EngineStore._LastCreatedScene === this) {\r\n            EngineStore._LastCreatedScene = null;\r\n            let engineIndex = EngineStore.Instances.length - 1;\r\n            while (engineIndex >= 0) {\r\n                const engine = EngineStore.Instances[engineIndex];\r\n                if (engine.scenes.length > 0) {\r\n                    EngineStore._LastCreatedScene = engine.scenes[this._engine.scenes.length - 1];\r\n                    break;\r\n                }\r\n                engineIndex--;\r\n            }\r\n        }\r\n\r\n        index = this._engine._virtualScenes.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._engine._virtualScenes.splice(index, 1);\r\n        }\r\n\r\n        this._engine.wipeCaches(true);\r\n        this.onDisposeObservable.clear();\r\n        this.onBeforeRenderObservable.clear();\r\n        this.onAfterRenderObservable.clear();\r\n        this.onBeforeRenderTargetsRenderObservable.clear();\r\n        this.onAfterRenderTargetsRenderObservable.clear();\r\n        this.onAfterStepObservable.clear();\r\n        this.onBeforeStepObservable.clear();\r\n        this.onBeforeActiveMeshesEvaluationObservable.clear();\r\n        this.onAfterActiveMeshesEvaluationObservable.clear();\r\n        this.onBeforeParticlesRenderingObservable.clear();\r\n        this.onAfterParticlesRenderingObservable.clear();\r\n        this.onBeforeDrawPhaseObservable.clear();\r\n        this.onAfterDrawPhaseObservable.clear();\r\n        this.onBeforeAnimationsObservable.clear();\r\n        this.onAfterAnimationsObservable.clear();\r\n        this.onDataLoadedObservable.clear();\r\n        this.onBeforeRenderingGroupObservable.clear();\r\n        this.onAfterRenderingGroupObservable.clear();\r\n        this.onMeshImportedObservable.clear();\r\n        this.onBeforeCameraRenderObservable.clear();\r\n        this.onAfterCameraRenderObservable.clear();\r\n        this.onAfterRenderCameraObservable.clear();\r\n        this.onReadyObservable.clear();\r\n        this.onNewCameraAddedObservable.clear();\r\n        this.onCameraRemovedObservable.clear();\r\n        this.onNewLightAddedObservable.clear();\r\n        this.onLightRemovedObservable.clear();\r\n        this.onNewGeometryAddedObservable.clear();\r\n        this.onGeometryRemovedObservable.clear();\r\n        this.onNewTransformNodeAddedObservable.clear();\r\n        this.onTransformNodeRemovedObservable.clear();\r\n        this.onNewMeshAddedObservable.clear();\r\n        this.onMeshRemovedObservable.clear();\r\n        this.onNewSkeletonAddedObservable.clear();\r\n        this.onSkeletonRemovedObservable.clear();\r\n        this.onNewMaterialAddedObservable.clear();\r\n        this.onNewMultiMaterialAddedObservable.clear();\r\n        this.onMaterialRemovedObservable.clear();\r\n        this.onMultiMaterialRemovedObservable.clear();\r\n        this.onNewTextureAddedObservable.clear();\r\n        this.onTextureRemovedObservable.clear();\r\n        this.onPrePointerObservable.clear();\r\n        this.onPointerObservable.clear();\r\n        this.onPreKeyboardObservable.clear();\r\n        this.onKeyboardObservable.clear();\r\n        this.onActiveCameraChanged.clear();\r\n        this.onScenePerformancePriorityChangedObservable.clear();\r\n        this.onClearColorChangedObservable.clear();\r\n        this.onEnvironmentTextureChangedObservable.clear();\r\n        this.onMeshUnderPointerUpdatedObservable.clear();\r\n        this._isDisposed = true;\r\n    }\r\n\r\n    private _disposeList<T extends IDisposable>(items: T[], callback?: (item: T) => void): void {\r\n        const itemsCopy = items.slice(0);\r\n        callback = callback ?? ((item) => item.dispose());\r\n        for (const item of itemsCopy) {\r\n            callback(item);\r\n        }\r\n        items.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Gets if the scene is already disposed\r\n     */\r\n    public get isDisposed(): boolean {\r\n        return this._isDisposed;\r\n    }\r\n\r\n    /**\r\n     * Call this function to reduce memory footprint of the scene.\r\n     * Vertex buffers will not store CPU data anymore (this will prevent picking, collisions or physics to work correctly)\r\n     */\r\n    public clearCachedVertexData(): void {\r\n        for (let meshIndex = 0; meshIndex < this.meshes.length; meshIndex++) {\r\n            const mesh = this.meshes[meshIndex];\r\n            const geometry = (<Mesh>mesh).geometry;\r\n\r\n            if (geometry) {\r\n                geometry.clearCachedData();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This function will remove the local cached buffer data from texture.\r\n     * It will save memory but will prevent the texture from being rebuilt\r\n     */\r\n    public cleanCachedTextureBuffer(): void {\r\n        for (const baseTexture of this.textures) {\r\n            const buffer = (<Texture>baseTexture)._buffer;\r\n\r\n            if (buffer) {\r\n                (<Texture>baseTexture)._buffer = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the world extend vectors with an optional filter\r\n     *\r\n     * @param filterPredicate the predicate - which meshes should be included when calculating the world size\r\n     * @returns {{ min: Vector3; max: Vector3 }} min and max vectors\r\n     */\r\n    public getWorldExtends(filterPredicate?: (mesh: AbstractMesh) => boolean): { min: Vector3; max: Vector3 } {\r\n        const min = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        const max = new Vector3(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\r\n        filterPredicate = filterPredicate || (() => true);\r\n        const meshes = this.meshes.filter(filterPredicate);\r\n        for (const mesh of meshes) {\r\n            mesh.computeWorldMatrix(true);\r\n\r\n            if (!mesh.subMeshes || mesh.subMeshes.length === 0 || mesh.infiniteDistance) {\r\n                continue;\r\n            }\r\n\r\n            const boundingInfo = mesh.getBoundingInfo();\r\n\r\n            const minBox = boundingInfo.boundingBox.minimumWorld;\r\n            const maxBox = boundingInfo.boundingBox.maximumWorld;\r\n\r\n            Vector3.CheckExtends(minBox, min, max);\r\n            Vector3.CheckExtends(maxBox, min, max);\r\n        }\r\n\r\n        return {\r\n            min: min,\r\n            max: max,\r\n        };\r\n    }\r\n\r\n    // Picking\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a ray that can be used to pick in the scene\r\n     * @param x defines the x coordinate of the origin (on-screen)\r\n     * @param y defines the y coordinate of the origin (on-screen)\r\n     * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\r\n     * @param camera defines the camera to use for the picking\r\n     * @param cameraViewSpace defines if picking will be done in view space (false by default)\r\n     * @returns a Ray\r\n     */\r\n    public createPickingRay(x: number, y: number, world: Nullable<Matrix>, camera: Nullable<Camera>, cameraViewSpace = false): Ray {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a ray that can be used to pick in the scene\r\n     * @param x defines the x coordinate of the origin (on-screen)\r\n     * @param y defines the y coordinate of the origin (on-screen)\r\n     * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\r\n     * @param result defines the ray where to store the picking ray\r\n     * @param camera defines the camera to use for the picking\r\n     * @param cameraViewSpace defines if picking will be done in view space (false by default)\r\n     * @param enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\r\n     * @returns the current scene\r\n     */\r\n    public createPickingRayToRef(\r\n        x: number,\r\n        y: number,\r\n        world: Nullable<Matrix>,\r\n        result: Ray,\r\n        camera: Nullable<Camera>,\r\n        cameraViewSpace = false,\r\n        enableDistantPicking = false\r\n    ): Scene {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a ray that can be used to pick in the scene\r\n     * @param x defines the x coordinate of the origin (on-screen)\r\n     * @param y defines the y coordinate of the origin (on-screen)\r\n     * @param camera defines the camera to use for the picking\r\n     * @returns a Ray\r\n     */\r\n    public createPickingRayInCameraSpace(x: number, y: number, camera?: Camera): Ray {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Creates a ray that can be used to pick in the scene\r\n     * @param x defines the x coordinate of the origin (on-screen)\r\n     * @param y defines the y coordinate of the origin (on-screen)\r\n     * @param result defines the ray where to store the picking ray\r\n     * @param camera defines the camera to use for the picking\r\n     * @returns the current scene\r\n     */\r\n    public createPickingRayInCameraSpaceToRef(x: number, y: number, result: Ray, camera?: Camera): Scene {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    /** @internal */\r\n    public _registeredActions: number = 0;\r\n\r\n    /** Launch a ray to try to pick a mesh in the scene\r\n     * @param x position on screen\r\n     * @param y position on screen\r\n     * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @returns a PickingInfo\r\n     */\r\n    public pick(x: number, y: number, predicate?: MeshPredicate, fastCheck?: boolean, camera?: Nullable<Camera>, trianglePredicate?: TrianglePickingPredicate): PickingInfo {\r\n        const warn = _WarnImport(\"Ray\", true);\r\n        if (warn) {\r\n            Logger.Warn(warn);\r\n        }\r\n        // Dummy info if picking as not been imported\r\n        return new PickingInfo();\r\n    }\r\n\r\n    /** Launch a ray to try to pick a mesh in the scene using only bounding information of the main mesh (not using submeshes)\r\n     * @param x position on screen\r\n     * @param y position on screen\r\n     * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n     * @returns a PickingInfo (Please note that some info will not be set like distance, bv, bu and everything that cannot be capture by only using bounding infos)\r\n     */\r\n    public pickWithBoundingInfo(x: number, y: number, predicate?: MeshPredicate, fastCheck?: boolean, camera?: Nullable<Camera>): Nullable<PickingInfo> {\r\n        const warn = _WarnImport(\"Ray\", true);\r\n        if (warn) {\r\n            Logger.Warn(warn);\r\n        }\r\n        // Dummy info if picking as not been imported\r\n        return new PickingInfo();\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Use the given ray to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\r\n     * irrespective of orientation.\r\n     * @param ray The ray to use to pick meshes\r\n     * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must have isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @returns a PickingInfo\r\n     */\r\n    public pickWithRay(ray: Ray, predicate?: MeshPredicate, fastCheck?: boolean, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo> {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Launch a ray to try to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\r\n     * irrespective of orientation.\r\n     * @param x X position on screen\r\n     * @param y Y position on screen\r\n     * @param predicate Predicate function used to determine eligible meshes and instances. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n     * @param camera camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @returns an array of PickingInfo\r\n     */\r\n    public multiPick(x: number, y: number, predicate?: MeshPredicate, camera?: Camera, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * Launch a ray to try to pick a mesh in the scene\r\n     * @param ray Ray to use\r\n     * @param predicate Predicate function used to determine eligible meshes and instances. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @returns an array of PickingInfo\r\n     */\r\n    public multiPickWithRay(ray: Ray, predicate?: MeshPredicate, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n        throw _WarnImport(\"Ray\");\r\n    }\r\n\r\n    /**\r\n     * Force the value of meshUnderPointer\r\n     * @param mesh defines the mesh to use\r\n     * @param pointerId optional pointer id when using more than one pointer\r\n     * @param pickResult optional pickingInfo data used to find mesh\r\n     */\r\n    public setPointerOverMesh(mesh: Nullable<AbstractMesh>, pointerId?: number, pickResult?: Nullable<PickingInfo>): void {\r\n        this._inputManager.setPointerOverMesh(mesh, pointerId, pickResult);\r\n    }\r\n\r\n    /**\r\n     * Gets the mesh under the pointer\r\n     * @returns a Mesh or null if no mesh is under the pointer\r\n     */\r\n    public getPointerOverMesh(): Nullable<AbstractMesh> {\r\n        return this._inputManager.getPointerOverMesh();\r\n    }\r\n\r\n    // Misc.\r\n    /** @internal */\r\n    public _rebuildGeometries(): void {\r\n        for (const geometry of this.geometries) {\r\n            geometry._rebuild();\r\n        }\r\n\r\n        for (const mesh of this.meshes) {\r\n            mesh._rebuild();\r\n        }\r\n\r\n        if (this.postProcessManager) {\r\n            this.postProcessManager._rebuild();\r\n        }\r\n\r\n        for (const component of this._components) {\r\n            component.rebuild();\r\n        }\r\n\r\n        for (const system of this.particleSystems) {\r\n            system.rebuild();\r\n        }\r\n\r\n        if (this.spriteManagers) {\r\n            for (const spriteMgr of this.spriteManagers) {\r\n                spriteMgr.rebuild();\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuildTextures(): void {\r\n        for (const texture of this.textures) {\r\n            texture._rebuild(true);\r\n        }\r\n\r\n        this.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    /**\r\n     * Get from a list of objects by tags\r\n     * @param list the list of objects to use\r\n     * @param tagsQuery the query to use\r\n     * @param filter a predicate to filter for tags\r\n     * @returns\r\n     */\r\n    private _getByTags<T>(list: T[], tagsQuery: string, filter?: (item: T) => boolean): T[] {\r\n        if (tagsQuery === undefined) {\r\n            // returns the complete list (could be done with Tags.MatchesQuery but no need to have a for-loop here)\r\n            return list;\r\n        }\r\n\r\n        const listByTags = [];\r\n\r\n        for (const i in list) {\r\n            const item = list[i];\r\n            if (Tags && Tags.MatchesQuery(item, tagsQuery) && (!filter || filter(item))) {\r\n                listByTags.push(item);\r\n            }\r\n        }\r\n\r\n        return listByTags;\r\n    }\r\n\r\n    /**\r\n     * Get a list of meshes by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of Mesh\r\n     */\r\n    public getMeshesByTags(tagsQuery: string, filter?: (mesh: AbstractMesh) => boolean): AbstractMesh[] {\r\n        return this._getByTags(this.meshes, tagsQuery, filter);\r\n    }\r\n\r\n    /**\r\n     * Get a list of cameras by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of Camera\r\n     */\r\n    public getCamerasByTags(tagsQuery: string, filter?: (camera: Camera) => boolean): Camera[] {\r\n        return this._getByTags(this.cameras, tagsQuery, filter);\r\n    }\r\n\r\n    /**\r\n     * Get a list of lights by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of Light\r\n     */\r\n    public getLightsByTags(tagsQuery: string, filter?: (light: Light) => boolean): Light[] {\r\n        return this._getByTags(this.lights, tagsQuery, filter);\r\n    }\r\n\r\n    /**\r\n     * Get a list of materials by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of Material\r\n     */\r\n    public getMaterialByTags(tagsQuery: string, filter?: (material: Material) => boolean): Material[] {\r\n        return this._getByTags(this.materials, tagsQuery, filter).concat(this._getByTags(this.multiMaterials, tagsQuery, filter));\r\n    }\r\n\r\n    /**\r\n     * Get a list of transform nodes by tags\r\n     * @param tagsQuery defines the tags query to use\r\n     * @param filter defines a predicate used to filter results\r\n     * @returns an array of TransformNode\r\n     */\r\n    public getTransformNodesByTags(tagsQuery: string, filter?: (transform: TransformNode) => boolean): TransformNode[] {\r\n        return this._getByTags(this.transformNodes, tagsQuery, filter);\r\n    }\r\n\r\n    /**\r\n     * Overrides the default sort function applied in the rendering group to prepare the meshes.\r\n     * This allowed control for front to back rendering or reversly depending of the special needs.\r\n     *\r\n     * @param renderingGroupId The rendering group id corresponding to its index\r\n     * @param opaqueSortCompareFn The opaque queue comparison function use to sort.\r\n     * @param alphaTestSortCompareFn The alpha test queue comparison function use to sort.\r\n     * @param transparentSortCompareFn The transparent queue comparison function use to sort.\r\n     */\r\n    public setRenderingOrder(\r\n        renderingGroupId: number,\r\n        opaqueSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        alphaTestSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null,\r\n        transparentSortCompareFn: Nullable<(a: SubMesh, b: SubMesh) => number> = null\r\n    ): void {\r\n        this._renderingManager.setRenderingOrder(renderingGroupId, opaqueSortCompareFn, alphaTestSortCompareFn, transparentSortCompareFn);\r\n    }\r\n\r\n    /**\r\n     * Specifies whether or not the stencil and depth buffer are cleared between two rendering groups.\r\n     *\r\n     * @param renderingGroupId The rendering group id corresponding to its index\r\n     * @param autoClearDepthStencil Automatically clears depth and stencil between groups if true.\r\n     * @param depth Automatically clears depth between groups if true and autoClear is true.\r\n     * @param stencil Automatically clears stencil between groups if true and autoClear is true.\r\n     */\r\n    public setRenderingAutoClearDepthStencil(renderingGroupId: number, autoClearDepthStencil: boolean, depth = true, stencil = true): void {\r\n        this._renderingManager.setRenderingAutoClearDepthStencil(renderingGroupId, autoClearDepthStencil, depth, stencil);\r\n    }\r\n\r\n    /**\r\n     * Gets the current auto clear configuration for one rendering group of the rendering\r\n     * manager.\r\n     * @param index the rendering group index to get the information for\r\n     * @returns The auto clear setup for the requested rendering group\r\n     */\r\n    public getAutoClearDepthStencilSetup(index: number): IRenderingManagerAutoClearSetup {\r\n        return this._renderingManager.getAutoClearDepthStencilSetup(index);\r\n    }\r\n\r\n    private _blockMaterialDirtyMechanism = false;\r\n\r\n    /** @internal */\r\n    public _forceBlockMaterialDirtyMechanism(value: boolean) {\r\n        this._blockMaterialDirtyMechanism = value;\r\n    }\r\n\r\n    /** Gets or sets a boolean blocking all the calls to markAllMaterialsAsDirty (ie. the materials won't be updated if they are out of sync) */\r\n    public get blockMaterialDirtyMechanism(): boolean {\r\n        return this._blockMaterialDirtyMechanism;\r\n    }\r\n\r\n    public set blockMaterialDirtyMechanism(value: boolean) {\r\n        if (this._blockMaterialDirtyMechanism === value) {\r\n            return;\r\n        }\r\n\r\n        this._blockMaterialDirtyMechanism = value;\r\n\r\n        if (!value) {\r\n            // Do a complete update\r\n            this.markAllMaterialsAsDirty(Constants.MATERIAL_AllDirtyFlag);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Will flag all materials as dirty to trigger new shader compilation\r\n     * @param flag defines the flag used to specify which material part must be marked as dirty\r\n     * @param predicate If not null, it will be used to specify if a material has to be marked as dirty\r\n     */\r\n    public markAllMaterialsAsDirty(flag: number, predicate?: (mat: Material) => boolean): void {\r\n        if (this._blockMaterialDirtyMechanism) {\r\n            return;\r\n        }\r\n\r\n        for (const material of this.materials) {\r\n            if (predicate && !predicate(material)) {\r\n                continue;\r\n            }\r\n            material.markAsDirty(flag);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _loadFile(\r\n        fileOrUrl: File | string,\r\n        onSuccess: (data: string | ArrayBuffer, responseURL?: string) => void,\r\n        onProgress?: (ev: ProgressEvent) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest, exception?: LoadFileError) => void,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): IFileRequest {\r\n        const request = LoadFile(fileOrUrl, onSuccess, onProgress, useOfflineSupport ? this.offlineProvider : undefined, useArrayBuffer, onError, onOpened);\r\n        this._activeRequests.push(request);\r\n        request.onCompleteObservable.add((request) => {\r\n            this._activeRequests.splice(this._activeRequests.indexOf(request), 1);\r\n        });\r\n        return request;\r\n    }\r\n\r\n    public async _loadFileAsync(\r\n        fileOrUrl: File | string,\r\n        onProgress?: (data: any) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: false,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): Promise<string>;\r\n\r\n    public async _loadFileAsync(\r\n        fileOrUrl: File | string,\r\n        onProgress?: (data: any) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: true,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): Promise<ArrayBuffer>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public async _loadFileAsync(\r\n        fileOrUrl: File | string,\r\n        onProgress?: (data: any) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: boolean,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): Promise<string | ArrayBuffer> {\r\n        return await new Promise((resolve, reject) => {\r\n            this._loadFile(\r\n                fileOrUrl,\r\n                (data) => {\r\n                    resolve(data);\r\n                },\r\n                onProgress,\r\n                useOfflineSupport,\r\n                useArrayBuffer,\r\n                (request, exception) => {\r\n                    // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                    reject(exception);\r\n                },\r\n                onOpened\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _requestFile(\r\n        url: string,\r\n        onSuccess: (data: string | ArrayBuffer, request?: WebRequest) => void,\r\n        onProgress?: (ev: ProgressEvent) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (error: RequestFileError) => void,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): IFileRequest {\r\n        const request = RequestFile(url, onSuccess, onProgress, useOfflineSupport ? this.offlineProvider : undefined, useArrayBuffer, onError, onOpened);\r\n        this._activeRequests.push(request);\r\n        request.onCompleteObservable.add((request) => {\r\n            this._activeRequests.splice(this._activeRequests.indexOf(request), 1);\r\n        });\r\n        return request;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public async _requestFileAsync(\r\n        url: string,\r\n        onProgress?: (ev: ProgressEvent) => void,\r\n        useOfflineSupport?: boolean,\r\n        useArrayBuffer?: boolean,\r\n        onOpened?: (request: WebRequest) => void\r\n    ): Promise<string | ArrayBuffer> {\r\n        return await new Promise((resolve, reject) => {\r\n            this._requestFile(\r\n                url,\r\n                (data) => {\r\n                    resolve(data);\r\n                },\r\n                onProgress,\r\n                useOfflineSupport,\r\n                useArrayBuffer,\r\n                (error) => {\r\n                    reject(error);\r\n                },\r\n                onOpened\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _readFile(\r\n        file: File,\r\n        onSuccess: (data: string | ArrayBuffer) => void,\r\n        onProgress?: (ev: ProgressEvent) => any,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (error: ReadFileError) => void\r\n    ): IFileRequest {\r\n        const request = ReadFile(file, onSuccess, onProgress, useArrayBuffer, onError);\r\n        this._activeRequests.push(request);\r\n        request.onCompleteObservable.add((request) => {\r\n            this._activeRequests.splice(this._activeRequests.indexOf(request), 1);\r\n        });\r\n        return request;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public async _readFileAsync(file: File, onProgress?: (ev: ProgressEvent) => any, useArrayBuffer?: boolean): Promise<string | ArrayBuffer> {\r\n        return await new Promise((resolve, reject) => {\r\n            this._readFile(\r\n                file,\r\n                (data) => {\r\n                    resolve(data);\r\n                },\r\n                onProgress,\r\n                useArrayBuffer,\r\n                (error) => {\r\n                    reject(error);\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Internal perfCollector instance used for sharing between inspector and playground.\r\n     * Marked as protected to allow sharing between prototype extensions, but disallow access at toplevel.\r\n     */\r\n    protected _perfCollector: Nullable<PerformanceViewerCollector> = null;\r\n\r\n    // eslint-disable-next-line jsdoc/require-returns-check\r\n    /**\r\n     * This method gets the performance collector belonging to the scene, which is generally shared with the inspector.\r\n     * @returns the perf collector belonging to the scene.\r\n     */\r\n    public getPerfCollector(): PerformanceViewerCollector {\r\n        throw _WarnImport(\"performanceViewerSceneExtension\");\r\n    }\r\n\r\n    // deprecated\r\n\r\n    /**\r\n     * Sets the active camera of the scene using its Id\r\n     * @param id defines the camera's Id\r\n     * @returns the new active camera or null if none found.\r\n     * @deprecated Please use setActiveCameraById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    setActiveCameraByID(id: string): Nullable<Camera> {\r\n        return this.setActiveCameraById(id);\r\n    }\r\n    /**\r\n     * Get a material using its id\r\n     * @param id defines the material's Id\r\n     * @returns the material or null if none found.\r\n     * @deprecated Please use getMaterialById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getMaterialByID(id: string): Nullable<Material> {\r\n        return this.getMaterialById(id);\r\n    }\r\n    /**\r\n     * Gets a the last added material using a given id\r\n     * @param id defines the material's Id\r\n     * @returns the last material with the given id or null if none found.\r\n     * @deprecated Please use getLastMaterialById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getLastMaterialByID(id: string): Nullable<Material> {\r\n        return this.getLastMaterialById(id);\r\n    }\r\n\r\n    /**\r\n     * Get a texture using its unique id\r\n     * @param uniqueId defines the texture's unique id\r\n     * @returns the texture or null if none found.\r\n     * @deprecated Please use getTextureByUniqueId instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getTextureByUniqueID(uniqueId: number): Nullable<BaseTexture> {\r\n        return this.getTextureByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a camera using its Id\r\n     * @param id defines the Id to look for\r\n     * @returns the camera or null if not found\r\n     * @deprecated Please use getCameraById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getCameraByID(id: string): Nullable<Camera> {\r\n        return this.getCameraById(id);\r\n    }\r\n    /**\r\n     * Gets a camera using its unique Id\r\n     * @param uniqueId defines the unique Id to look for\r\n     * @returns the camera or null if not found\r\n     * @deprecated Please use getCameraByUniqueId instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getCameraByUniqueID(uniqueId: number): Nullable<Camera> {\r\n        return this.getCameraByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a bone using its Id\r\n     * @param id defines the bone's Id\r\n     * @returns the bone or null if not found\r\n     * @deprecated Please use getBoneById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getBoneByID(id: string): Nullable<Bone> {\r\n        return this.getBoneById(id);\r\n    }\r\n    /**\r\n     * Gets a light node using its Id\r\n     * @param id defines the light's Id\r\n     * @returns the light or null if none found.\r\n     * @deprecated Please use getLightById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getLightByID(id: string): Nullable<Light> {\r\n        return this.getLightById(id);\r\n    }\r\n    /**\r\n     * Gets a light node using its scene-generated unique Id\r\n     * @param uniqueId defines the light's unique Id\r\n     * @returns the light or null if none found.\r\n     * @deprecated Please use getLightByUniqueId instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getLightByUniqueID(uniqueId: number): Nullable<Light> {\r\n        return this.getLightByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a particle system by Id\r\n     * @param id defines the particle system Id\r\n     * @returns the corresponding system or null if none found\r\n     * @deprecated Please use getParticleSystemById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getParticleSystemByID(id: string): Nullable<IParticleSystem> {\r\n        return this.getParticleSystemById(id);\r\n    }\r\n    /**\r\n     * Gets a geometry using its Id\r\n     * @param id defines the geometry's Id\r\n     * @returns the geometry or null if none found.\r\n     * @deprecated Please use getGeometryById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getGeometryByID(id: string): Nullable<Geometry> {\r\n        return this.getGeometryById(id);\r\n    }\r\n    /**\r\n     * Gets the first added mesh found of a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the mesh found or null if not found at all\r\n     * @deprecated Please use getMeshById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getMeshByID(id: string): Nullable<AbstractMesh> {\r\n        return this.getMeshById(id);\r\n    }\r\n    /**\r\n     * Gets a mesh with its auto-generated unique Id\r\n     * @param uniqueId defines the unique Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     * @deprecated Please use getMeshByUniqueId instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getMeshByUniqueID(uniqueId: number): Nullable<AbstractMesh> {\r\n        return this.getMeshByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a the last added mesh using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found mesh or null if not found at all.\r\n     * @deprecated Please use getLastMeshById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getLastMeshByID(id: string): Nullable<AbstractMesh> {\r\n        return this.getLastMeshById(id);\r\n    }\r\n    /**\r\n     * Gets a list of meshes using their Id\r\n     * @param id defines the Id to search for\r\n     * @returns a list of meshes\r\n     * @deprecated Please use getMeshesById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getMeshesByID(id: string): Array<AbstractMesh> {\r\n        return this.getMeshesById(id);\r\n    }\r\n    /**\r\n     * Gets the first added transform node found of a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     * @deprecated Please use getTransformNodeById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getTransformNodeByID(id: string): Nullable<TransformNode> {\r\n        return this.getTransformNodeById(id);\r\n    }\r\n    /**\r\n     * Gets a transform node with its auto-generated unique Id\r\n     * @param uniqueId defines the unique Id to search for\r\n     * @returns the found transform node or null if not found at all.\r\n     * @deprecated Please use getTransformNodeByUniqueId instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getTransformNodeByUniqueID(uniqueId: number): Nullable<TransformNode> {\r\n        return this.getTransformNodeByUniqueId(uniqueId);\r\n    }\r\n    /**\r\n     * Gets a list of transform nodes using their Id\r\n     * @param id defines the Id to search for\r\n     * @returns a list of transform nodes\r\n     * @deprecated Please use getTransformNodesById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getTransformNodesByID(id: string): Array<TransformNode> {\r\n        return this.getTransformNodesById(id);\r\n    }\r\n    /**\r\n     * Gets a node (Mesh, Camera, Light) using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found node or null if not found at all\r\n     * @deprecated Please use getNodeById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getNodeByID(id: string): Nullable<Node> {\r\n        return this.getNodeById(id);\r\n    }\r\n    /**\r\n     * Gets a the last added node (Mesh, Camera, Light) using a given Id\r\n     * @param id defines the Id to search for\r\n     * @returns the found node or null if not found at all\r\n     * @deprecated Please use getLastEntryById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getLastEntryByID(id: string): Nullable<Node> {\r\n        return this.getLastEntryById(id);\r\n    }\r\n    /**\r\n     * Gets a skeleton using a given Id (if many are found, this function will pick the last one)\r\n     * @param id defines the Id to search for\r\n     * @returns the found skeleton or null if not found at all.\r\n     * @deprecated Please use getLastSkeletonById instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    getLastSkeletonByID(id: string): Nullable<Skeleton> {\r\n        return this.getLastSkeletonById(id);\r\n    }\r\n}\r\n\r\n// Register Class Name\r\nRegisterClass(\"BABYLON.Scene\", Scene);\r\n"], "names": [], "mappings": ";;;;AAEA,OAAO,EAAE,KAAK,EAAE,MAAM,cAAc,CAAC;AAErC,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAErD,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,OAAO,EAAE,qBAAqB,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AACtE,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAEnC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAElE,OAAO,EAAE,4BAA4B,EAAE,MAAM,0CAA0C,CAAC;AACxF,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAIvD,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAC;AAGxE,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAC;AAiBhE,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,OAAO,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AAEpD,OAAO,EAAE,WAAW,EAAE,MAAM,iBAAiB,CAAC;AAE9C,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAEjD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAEpD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAE7D,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAGnE,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AA4BlD,OAAO,EAAE,2BAA2B,EAAE,MAAM,sCAAsC,CAAC;AACnF,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AAEvC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDjD,IAAkB,wBAOjB;AAPD,CAAA,SAAkB,wBAAwB;IACtC,uGAAA,EAAyG,CACzG,wBAAA,CAAA,wBAAA,CAAA,qBAAA,GAAA,EAAA,GAAA,oBAAkB,CAAA;IAClB,uGAAA,EAAyG,CACzG,wBAAA,CAAA,wBAAA,CAAA,eAAA,GAAA,EAAA,GAAA,cAAY,CAAA;IACZ,qCAAA,EAAuC,CACvC,wBAAA,CAAA,wBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;AACd,CAAC,EAPiB,wBAAwB,IAAA,CAAxB,wBAAwB,GAAA,CAAA,CAAA,GAOzC;AAMK,MAAO,KAAK;IAqBd,uDAAuD;IACvD;;;;OAIG,CACI,MAAM,CAAC,sBAAsB,CAAC,KAAY,EAAA;QAC7C,qKAAM,cAAA,AAAW,EAAC,kBAAkB,CAAC,CAAC;IAC1C,CAAC;IAID,uDAAuD;IACvD;;;OAGG,CACI,MAAM,CAAC,2BAA2B,GAAA;QACrC,qKAAM,cAAA,AAAW,EAAC,6BAA6B,CAAC,CAAC;IACrD,CAAC;IAyCD;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;YAC7B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAmCD;;;;;;;OAOG,CACH,IAAW,4BAA4B,GAAA;QACnC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAQD;;OAEG,CACH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAK,EAAA;QAChC,IAAI,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACtC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,OAAQ,KAAK,EAAE,CAAC;YACZ,KAAA,EAAA,+CAAA;gBACI,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;gBACjC,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,GAAG,KAAK,CAAC;gBAC1D,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;gBACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,MAAM;YACV,KAAA,EAAA,yCAAA;gBACI,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;gBACjC,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,GAAG,KAAK,CAAC;gBAC1D,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;gBACnC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,MAAM;YACV,KAAA,EAAA,uCAAA;gBACI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;gBAChC,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,GAAG,IAAI,CAAC;gBACzD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;gBACnC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,MAAM;QACd,CAAC;QAED,IAAI,CAAC,2CAA2C,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC5E,CAAC;IAID;;OAEG,CACH,IAAW,cAAc,CAAC,KAAc,EAAA;QACpC,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAGD;;OAEG,CACH,IAAW,mBAAmB,CAAC,KAAc,EAAA;QACzC,IAAI,IAAI,CAAC,oBAAoB,KAAK,KAAK,EAAE,CAAC;YACtC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACtC,CAAC;IACD,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAGD;;OAEG,CACH,IAAW,gBAAgB,CAAC,KAAc,EAAA;QACtC,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YACnC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAgID;;;;OAIG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IACD;;;;OAIG,CACH,IAAW,kBAAkB,CAAC,KAA4B,EAAA;QACtD,IAAI,IAAI,CAAC,mBAAmB,KAAK,KAAK,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,qCAAqC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAClE,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IACtE,CAAC;IAoCD;;OAEG,CACI,QAAQ,GAAA;QACX,IAAI,KAAK,GAAW,EAAE,CAAC;QACvB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,UAAU;QACrD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;YACpC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IASD;;OAEG,CACH,IAAW,2BAA2B,GAAA;QAClC,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED,IAAW,2BAA2B,CAAC,KAA4C,EAAA;QAC/E,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC9C,CAAC;IAgED,gEAAA,EAAkE,CAClE,IAAW,SAAS,CAAC,QAAoB,EAAA;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrE,CAAC;IAQD,+DAAA,EAAiE,CACjE,IAAW,YAAY,CAAC,QAA8B,EAAA;QAClD,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC/E,CAAC;IACL,CAAC;IAcD,8DAAA,EAAgE,CAChE,IAAW,WAAW,CAAC,QAA8B,EAAA;QACjD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC7E,CAAC;IACL,CAAC;IAiCD,4DAAA,EAA8D,CAC9D,IAAW,kBAAkB,CAAC,QAAoB,EAAA;QAC9C,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC3F,CAAC;IASD,2DAAA,EAA6D,CAC7D,IAAW,iBAAiB,CAAC,QAAoB,EAAA;QAC7C,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACjF,CAAC;QACD,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzF,CAAC;IAyPD;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK,EAAA;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;IAChE,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAK,EAAA;QAC/B,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK,EAAA;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK,EAAA;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;IAChE,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAK,EAAA;QAC/B,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK,EAAA;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,GAAA;QAC7B,OAAO,IAAI,CAAC,4BAA4B,CAAC,sBAAsB,CAAC;IACpE,CAAC;IAED,IAAW,sBAAsB,CAAC,KAAK,EAAA;QACnC,IAAI,CAAC,4BAA4B,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACrE,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,GAAA;QAC7B,OAAO,IAAI,CAAC,4BAA4B,CAAC,sBAAsB,CAAC;IACpE,CAAC;IAED,IAAW,sBAAsB,CAAC,KAAK,EAAA;QACnC,IAAI,CAAC,4BAA4B,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACrE,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,CAAC;IAClE,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAK,EAAA;QACjC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACnE,CAAC;IAqCD;;OAEG,CACH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC;IAClD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,qBAAqB,GAAA;QACnC,iLAAO,eAAY,CAAC,qBAAqB,CAAC;IAC9C,CAAC;IAEM,MAAM,KAAK,qBAAqB,CAAC,KAAa,EAAA;kLACjD,eAAY,CAAC,qBAAqB,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,cAAc,GAAA;QAC5B,OAAO,yLAAY,CAAC,cAAc,CAAC;IACvC,CAAC;IAEM,MAAM,KAAK,cAAc,CAAC,KAAa,EAAA;kLAC1C,eAAY,CAAC,cAAc,GAAG,KAAK,CAAC;IACxC,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,gBAAgB,GAAA;QAC9B,iLAAO,eAAY,CAAC,gBAAgB,CAAC;IACzC,CAAC;IAEM,MAAM,KAAK,gBAAgB,CAAC,KAAa,EAAA;kLAC5C,eAAY,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED,sGAAA,EAAwG,CACjG,MAAM,KAAK,wBAAwB,GAAA;QACtC,iLAAO,eAAY,CAAC,wBAAwB,CAAC;IACjD,CAAC;IAEM,MAAM,KAAK,wBAAwB,CAAC,KAAc,EAAA;kLACrD,eAAY,CAAC,wBAAwB,GAAG,KAAK,CAAC;IAClD,CAAC;IAED;;;;;;OAMG,CACI,eAAe,CAAC,MAAwB,EAAkD;2BAAhD,YAAY,qDAAG,cAAc,EAAE,SAAS,oEAAG,KAAK;;;QAC7F,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,GACtC,IAAI,CAAC,mBAAmB,GACxB,IAAI,CAAC,uBAAuB,GAC1B,IAAI,CAAC,uBAAuB,8DAC3B,IAAI,CAAC,YAAY,0EAAE,cAAc,mQAAI,UAAO,CAAC,YAAY,CAAC,CAAC;QAEpE,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,KAAK,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,CAAC;yKAE1F,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9F,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM,CAAC,SAAS,CAAC,YAAY,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9G,CAAC,MAAM,CAAC;gBACJ,MAAM,CAAC,UAAU,CAAC,YAAY,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC;QAED,yKAAO,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG,CACI,gBAAgB,GAAA;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QAE7F,GAAG,CAAC,MAAM,EAAE,CAAC;QAEb,OAAO,GAAG,CAAC;IACf,CAAC;IAsBD;;OAEG,CACH,IAAW,oBAAoB,CAAC,KAAc,EAAA;QAC1C,IAAI,IAAI,CAAC,qBAAqB,KAAK,KAAK,EAAE,CAAC;YACvC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAOD;;;;OAIG,CACI,SAAS,CAAC,SAAiB,EAAA;QAC9B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;IACpC,CAAC;IAED;;;;OAIG,CACI,SAAS,GAAA;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;;OAIG,CACI,eAAe,GAAA;QAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAKD;;;;OAIG,CACH,IAAW,UAAU,CAAC,KAAc,EAAA;QAChC,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAGD;;;;;;;;;OASG,CACH,IAAW,OAAO,CAAC,KAAa,EAAA;QAC5B,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IACnE,CAAC;IACD,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IA2BD;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC;IAC5E,CAAC;IASD;;OAEG,CACH,IAAW,cAAc,CAAC,KAAc,EAAA;QACpC,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;IACpE,CAAC;IACD,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAGD;;OAEG,CACH,IAAW,aAAa,CAAC,KAAc,EAAA;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;IACpE,CAAC;IAED,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAKD,mDAAA,EAAqD,CACrD,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,OAA2B,EAAA;QAChD,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,uBAAuB,IAAG,gLAAA,AAAa,EAAC,OAAO,EAAE,GAAG,EAAE;gBACvD,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;IAClC,CAAC;IAID,2CAAA,EAA6C,CAC7C,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAAuB,EAAA;QAC3C,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,mBAAmB,GAAA;QAC1B,OAAO,KAAK,CAAC,sBAAsB,KAAK,KAAK,CAAC,+BAA+B,CAAC;IAClF,CAAC;IAID,qEAAA,EAAuE,CACvE,IAAW,eAAe,GAAA;QACtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,qEAAA,EAAuE,CACvE,IAAW,eAAe,CAAC,KAAe,EAAA;QACtC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IAID;;OAEG,CACH,IAAW,eAAe,CAAC,KAAc,EAAA;QACrC,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,EAAE,CAAC;YAClC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IACtE,CAAC;IAED,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAID;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAA2B,EAAA;QAC7C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,4BAA4B,CAAC;YAClE,CAAC;YACD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAC9D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACvD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;IACL,CAAC;IA2BD;;OAEG,CACH,IAAW,gBAAgB,CAAC,KAAc,EAAA;QACtC,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YACnC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;IACzE,CAAC;IAED,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAiBD,cAAA,EAAgB,CAChB,IAAW,oBAAoB,GAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC9B,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,2BAA2B,EAAE,CAAC;YACjE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IA0ID;;OAEG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAiBD;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAiCD;;OAEG,CACK,4BAA4B,GAAA;QAChC,qEAAqE;QACrE,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;gBAChD,SAAS,CAAC,QAAQ,EAAE,CAAC;YACzB,CAAC;YACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACI,aAAa,CAAC,SAA0B,EAAA;QAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE1C,MAAM,qBAAqB,GAAG,SAAgB,CAAC;QAC/C,IAAI,qBAAqB,CAAC,gBAAgB,IAAI,qBAAqB,CAAC,SAAS,EAAE,CAAC;YAC5E,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,aAAa,CAAC,IAAY,EAAA;QAC7B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,CAAE,CAAC;YACvC,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBAC1B,OAAO,SAAS,CAAC;YACrB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAiID;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IA8DD;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,OAAO,CAAC;IACnB,CAAC;IAOD;;OAEG,CACI,yBAAyB,GAAA;QAC5B,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/C,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACxD,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAOD;;OAEG,CACI,4BAA4B,CAAC,IAAkB,EAAA;QAClD,IAAI,CAAC,yBAAyB,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;QACrD,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAC9D,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED;;;;OAIG,CACI,4BAA4B,GAAA;QAC/B,IAAI,CAAC,uBAAuB,GAAG,GAAG,CAAG,CAAD,GAAK,CAAC,yBAAyB,EAAE,CAAC;QACtE,IAAI,CAAC,0BAA0B,GAAG,CAAC,IAAkB,EAAE,CAAG,CAAD,GAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;QAClG,IAAI,CAAC,gCAAgC,GAAG,CAAC,IAAkB,EAAE,QAAa,EAAE,CAAG,CAAD,GAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;QACvH,IAAI,CAAC,6BAA6B,GAAG,CAAC,IAAkB,EAAE,QAAkB,EAAE,CAAG,CAAD,GAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAC;IAC7H,CAAC;IAED;;OAEG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC;IAC/C,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa,EAAA;QAC7B,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxC,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa,EAAA;QAC7B,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxC,CAAC;IAED;;;OAGG,CACI,iBAAiB,GAAA;QACpB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;OAGG,CACI,eAAe,GAAA;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACI,mBAAmB,GAAA;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;;;OAMG,CACI,uBAAuB,CAAC,QAAkB,EAAE,MAAc,EAAwB;yBAAtB,iEAAqB,CAAC;QACrF,OAAO,IAAI,CAAC,aAAa,KAAK,MAAM,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ,IAAI,IAAI,CAAC,iBAAiB,KAAK,UAAU,CAAC;IACvH,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;OAGG,CACI,gBAAgB,GAAA;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;IACvC,CAAC;IAED;;;OAGG,CACH,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG,CACI,gBAAgB,GAAA;QACnB,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;IACvC,CAAC;IAED;;;OAGG,CACH,IAAW,6BAA6B,GAAA;QACpC,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG,CACI,kBAAkB,GAAA;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;IACzC,CAAC;IAED;;;OAGG,CACH,IAAW,0BAA0B,GAAA;QACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;IACrC,CAAC;IAED;;;OAGG,CACH,IAAW,sBAAsB,GAAA;QAC7B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACI,eAAe,GAAA;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACI,iBAAiB,GAAA;QACpB,OAAO,IAAI,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,CAAC;IAED;;;OAGG,CACI,WAAW,GAAA;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG,CACI,UAAU,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,sEAAA,EAAwE,CACjE,iBAAiB,GAAA;QACpB,IAAI,CAAC,SAAS,EAAE,CAAC;IACrB,CAAC;IAEO,UAAU,GAAA;QACd,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC,CAAC;IAChE,CAAC;IAED;;;;;;OAMG,CACI,mBAAmB,CAAC,UAAuB,EAAE,gBAAmC,EAAA;QACnF,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,mBAAmB,CAAC,UAAuB,EAAE,gBAAmC,EAAA;QACnF,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;QACrE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG,CACI,iBAAiB,CAAC,UAAuB,EAAE,gBAAmC,EAAE,SAAmB,EAAA;QACtG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,UAAU,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,iBAAiB,GAAc;wBAAb,SAAS,wDAAG,CAAC;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;OAKG,CACI,aAAa,GAAsD;YAArD,QAAQ,oEAAG,IAAI,eAAE,UAAU,uDAAG,IAAI,eAAE,UAAU,uDAAG,IAAI;QACtE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IACvE,CAAC;IAED,+BAAA,EAAiC,CAC1B,aAAa,GAAA;QAChB,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;IACvC,CAAC;IAED;;;;;OAKG,CACI,OAAO,GAA0B;iCAAzB,kBAAkB,+CAAG,IAAI;gCAmBpC,wEAAwE;;QAlBxE,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,KAAa,CAAC;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,MAAM,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC;;QAEvD,MAAM,CAAC,mBAAmB,4DAAG,IAAI,CAAC,YAAY,0EAAE,YAAY,6FAAI,mBAAmB,CAAC;QAEpF,IAAI,OAAO,GAAG,IAAI,CAAC;QAEnB,eAAe;QACf,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,OAAO,GAAG,KAAK,CAAC;QACpB,CAAC;qCAGG,CAAC,eAAe,0DAApB,sBAAsB,MAAM,EAAE,CAAC;QAE/B,MAAM;QACN,IAAI,IAAI,CAAC,+BAA+B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACpE,OAAO,IAAA,CAAP,OAAO,GAAK,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,EAAC;QACpD,CAAC;QAED,SAAS;QACT,IAAI,kBAAkB,EAAE,CAAC;YACrB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACzC,CAAC;QAED,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAClD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEhC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjD,SAAS;YACb,CAAC;YAED,6EAA6E;YAC7E,gEAAgE;YAChE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtB,OAAO,GAAG,KAAK,CAAC;gBAChB,SAAS;YACb,CAAC;YAED,MAAM,0BAA0B,GAC5B,IAAI,CAAC,gBAAgB,IACrB,IAAI,CAAC,YAAY,EAAE,KAAK,eAAe,IACvC,IAAI,CAAC,YAAY,EAAE,KAAK,oBAAoB,IAC3C,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe,IAAW,IAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5E,oBAAoB;YACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;gBAC3C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,0BAA0B,CAAC,EAAE,CAAC;oBACjD,OAAO,GAAG,KAAK,CAAC;gBACpB,CAAC;YACL,CAAC;YAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACtB,SAAS;YACb,CAAC;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC;YAClD,IAAI,GAAG,EAAE,CAAC;gBACN,IAAI,GAAG,CAAC,uBAAuB,EAAE,CAAC;oBAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;wBACnC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;wBACvC,IAAI,QAAQ,IAAI,QAAQ,CAAC,uBAAuB,IAAI,QAAQ,CAAC,uBAAuB,IAAI,IAAI,EAAE,CAAC;4BAC3F,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gCACpD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gCAExC,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC;4BAC3F,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,IAAI,GAAG,CAAC,uBAAuB,IAAI,GAAG,CAAC,uBAAuB,IAAI,IAAI,EAAE,CAAC;wBACrE,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;4BAC/C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;4BAEnC,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,GAAG,CAAC,uBAAuB,EAAE,CAAC,CAAC;wBACtF,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,iBAAiB;QACjB,IAAI,kBAAkB,EAAE,CAAC;YACrB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,EAAE,KAAK,CAAE,CAAC;gBACnE,MAAM,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrD,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAAE,CAAC;oBAC7B,OAAO,GAAG,KAAK,CAAC;gBACpB,CAAC;YACL,CAAC;QACL,CAAC;QAED,aAAa;QACb,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAExC,IAAI,QAAQ,CAAC,cAAc,KAAK,GAAA,MAAS,CAAC,sBAAsB,EAAE,CAAC;gBAC/D,OAAO,GAAG,KAAK,CAAC;YACpB,CAAC;QACL,CAAC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,aAAa,CAAE,CAAC;gBACtC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;oBACxB,OAAO,GAAG,KAAK,CAAC;gBACpB,CAAC;YACL,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnC,OAAO,GAAG,KAAK,CAAC;YACpB,CAAC;QACL,CAAC;QAED,YAAY;QACZ,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,eAAe,CAAE,CAAC;YAChD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC5B,OAAO,GAAG,KAAK,CAAC;YACpB,CAAC;QACL,CAAC;QAED,SAAS;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;gBAC9B,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;oBACnB,OAAO,GAAG,KAAK,CAAC;gBACpB,CAAC;YACL,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,CAAE,CAAC;gBAC1C,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,CAAC;oBAC9B,OAAO,GAAG,KAAK,CAAC;gBACpB,CAAC;YACL,CAAC;QACL,CAAC;QAED,UAAU;QACV,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC/B,OAAO,GAAG,KAAK,CAAC;QACpB,CAAC;QAED,MAAM,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAEjD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,yFAAA,EAA2F,CACpF,mBAAmB,GAAA;QACtB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAClC,CAAC;IAED;;;OAGG,CACI,oBAAoB,CAAC,IAAgB,EAAA;QACxC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;OAGG,CACI,sBAAsB,CAAC,IAAgB,EAAA;QAC1C,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG,CACI,mBAAmB,CAAC,IAAgB,EAAA;QACvC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;;OAGG,CACI,qBAAqB,CAAC,IAAgB,EAAA;QACzC,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAEO,wBAAwB,CAAC,IAAgB,EAAA;QAC7C,MAAM,QAAQ,GAAG,GAAG,EAAE;YAClB,IAAI,EAAE,CAAC;YACP,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QACF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG,CACI,uBAAuB,CAAC,IAAgB,EAAE,OAAgB,EAAA;QAC7D,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACxB,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC,EAAE,OAAO,CAAC,CAAC;QAChB,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,cAAc,CAAC,IAAS,EAAA;QAC3B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG,CACI,iBAAiB,CAAC,IAAS,EAAA;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,oBAAoB,GAAA;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IACxC,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,IAAgB,EAA4B;iCAA1B,kBAAkB,+CAAG,KAAK;QAChE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE,CAAC;YAC3C,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,cAAc,GAA2B;iCAA1B,kBAAkB,+CAAG,KAAK;QAClD,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACvB,OAAO,EAAE,CAAC;YACd,CAAC,EAAE,kBAAkB,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,aAAa,GAA2B;iCAA1B,kBAAkB,+CAAG,KAAK;QAC3C,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAE7C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;YACvC,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;YACvC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,0BAA0B,GAAG,UAAU,CAAC,GAAG,EAAE;YAC9C,4DAA4D;YAC5D,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QAC3C,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;OAGG,CACI,2BAA2B,GAAA;QAC9B,IAAI,CAAC,kBAAkB,mKAAG,gBAAa,CAAC,GAAG,CAAC;IAChD,CAAC;IAED,SAAS;IAET;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACI,mBAAmB,GAAA;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG,CACI,kBAAkB,GAAA;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;;;;OAMG,CACI,kBAAkB,CAAC,KAAa,EAAE,WAAmB,EAAE,KAAc,EAAE,WAAoB,EAAA;QAC9F,sEAAsE;QACtE,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACpD,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACnC,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,KAAK,WAAW,CAAC,UAAU,EAAE,CAAC;YACrG,OAAO;QACX,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,UAAU,CAAC;QACxC,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC,UAAU,CAAC;QACpD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC;QAErC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE9E,iBAAiB;QACjB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,sKAAG,UAAO,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACnE,CAAC,MAAM,CAAC;+KACJ,UAAO,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;YAC5D,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACjD,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACtD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,qBAAqB,GAAA;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;IAC9E,CAAC;IAED;;;;OAIG,CACI,wBAAwB,CAAC,IAAa,EAAA;QACzC,MAAM,QAAQ,GAAG,yKAAI,gBAAa,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,mBAAE,IAAI,qBAAI,OAAO,CAAC,CAAC;QACpF,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAC1C,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAChC,QAAQ,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACtC,QAAQ,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAEvC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG,CACI,qBAAqB,CAAC,GAAkB,EAAA;QAC3C,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACI,WAAW,GAAA;QACd,OAAO,wLAAiB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED;;;;OAIG,CACI,OAAO,CAAC,OAAqB,EAAmB;wBAAjB,SAAS,wDAAG,KAAK;QACnD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1B,OAAO,CAAC,mBAAmB,EAAE,CAAC;QAE9B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,CAAC,oBAAoB,EAAE,CAAC;QACnC,CAAC;gKAED,QAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,IAAI,SAAS,EAAE,CAAC;YACZ,MAAM,QAAQ,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;YAE1C,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,UAAU,CAAC,QAAsB,EAAmB;wBAAjB,SAAS,wDAAG,KAAK;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,0CAA0C;YAE1C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAE7B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACnB,QAAQ,CAAC,yBAAyB,EAAE,CAAC;YACzC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,SAAS,EAAE,CAAC;YACZ,MAAM,QAAQ,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC3C,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACI,gBAAgB,CAAC,gBAA+B,EAAA;QACnD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,gBAAgB,CAAC,QAAQ,EAAE,KAAK,IAAI,IAAI,gBAAgB,CAAC,gCAAgC,KAAK,CAAC,CAAC,EAAE,CAAC;YACnG,iBAAiB;YACjB,OAAO;QACX,CAAC;QAED,gBAAgB,CAAC,gCAAgC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC/E,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE3C,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC3B,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;IAC7E,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,QAAuB,EAAA;QAC9C,MAAM,KAAK,GAAG,QAAQ,CAAC,gCAAgC,CAAC;QACxD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,KAAK,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACrE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;gBACtC,QAAQ,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACtD,CAAC;YAED,QAAQ,CAAC,gCAAgC,GAAG,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACnB,QAAQ,CAAC,yBAAyB,EAAE,CAAC;YACzC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAEhE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,QAAkB,EAAA;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,iCAAiC;YACjC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAChC,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAE3D,yBAAyB;YACzB,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,wBAAwB,CAAC,QAA4B,EAAA;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,iCAAiC;YACjC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,QAAe,EAAA;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,qBAAqB;YACrB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;gBAC7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC7C,CAAC;YAED,sCAAsC;YACtC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACnB,QAAQ,CAAC,yBAAyB,EAAE,CAAC;YACzC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,QAAgB,EAAA;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,sCAAsC;YACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACnB,QAAQ,CAAC,yBAAyB,EAAE,CAAC;YACzC,CAAC;QACL,CAAC;QACD,4BAA4B;QAC5B,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;gBAChB,sCAAsC;gBACtC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;QACD,yBAAyB;QACzB,IAAI,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAC7B,CAAC;QACL,CAAC;QACD,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACzD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,QAAyB,EAAA;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAEtC,yBAAyB;YACzB,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACjE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,QAAmB,EAAA;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG,CACI,aAAa,CAAC,MAAW,EAAE,aAAsB,EAAE,UAAqC,EAAA;IAC3F,6DAA6D;IACjE,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,QAAwB,EAAA;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACjE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,QAAuB,EAAA;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAEhE,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,QAAkB,EAAA;QACpC,MAAM,KAAK,GAAG,QAAQ,CAAC,0BAA0B,CAAC;QAClD,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAChD,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC/D,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;gBACrC,YAAY,CAAC,0BAA0B,GAAG,KAAK,CAAC;YACpD,CAAC;YAED,QAAQ,CAAC,0BAA0B,GAAG,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE3D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG,CACI,mBAAmB,CAAC,QAA+B,EAAA;QACtD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,QAAqB,EAAA;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE1D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,QAAoB,EAAA;QACxC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE7D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,QAAqB,EAAA;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE9D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,QAAqB,EAAA;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAE9D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACI,QAAQ,CAAC,QAAe,EAAA;QAC3B,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACnB,QAAQ,CAAC,oBAAoB,EAAE,CAAC;QACpC,CAAC;QAED,iFAAiF;QACjF,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC7B,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/B,CAAC;QACL,CAAC;QAED,gKAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,oBAAoB,GAAA;QACvB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,oKAAC,iBAAc,CAAC,qBAAqB,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,SAAS,CAAC,SAAiB,EAAA;QAC9B,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gKAC7B,QAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACpB,SAAS,CAAC,oBAAoB,EAAE,CAAC;QACrC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,WAAW,CAAC,WAAqB,EAAA;QACpC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gKAEjC,QAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,iBAAiB,CAAC,iBAAkC,EAAA;QACvD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gKAE7C,QAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,YAAY,CAAC,YAAuB,EAAA;QACvC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG,CACI,iBAAiB,CAAC,iBAAiC,EAAA;QACtD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gKAE7C,QAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,kCAAkC,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,gBAAgB,CAAC,gBAA+B,EAAA;QACnD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3C,gKAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,WAAW,CAAC,WAAqB,EAAA;QACpC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE,KAAK,IAAI,IAAI,WAAW,CAAC,0BAA0B,KAAK,CAAC,CAAC,EAAE,CAAC;YACnF,kBAAkB;YAClB,OAAO;QACX,CAAC;QAED,WAAW,CAAC,0BAA0B,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gKACjC,QAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,qBAAqB,CAAC,qBAAyC,EAAA;QAClE,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACzD,CAAC;IAED;;;OAGG,CACI,WAAW,CAAC,WAAqB,EAAA;QACpC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,gBAAuC,EAAA;QAC3D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG,CACI,UAAU,CAAC,UAAuB,EAAA;QACrC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAED;;;OAGG,CACI,aAAa,CAAC,aAAyB,EAAA;QAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gKACrC,QAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,cAAc,CAAC,cAA2B,EAAA;QAC7C,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gKACxC,QAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,cAAc,CAAC,cAA2B,EAAA;QAC7C,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;+JACvC,SAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,SAAiB,EAAsB;4BAApB,aAAa,oDAAG,IAAI;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAE9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAC9B,IAAI,aAAa,EAAE,CAAC;YAChB,SAAS,CAAC,aAAa,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,EAAU,EAAA;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAEtC,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;YAC3B,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,IAAY,EAAA;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;YAC3B,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,uBAAuB,CAAC,IAAY,EAAA;QACvC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC/D,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBAC5C,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,YAAY,CAAC,mBAA4B,EAAE,SAAmC,EAAA;QAClF,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtB,OAAO,QAAQ,CAAC;YACpB,CAAC;QACL,CAAC;QACD,IAAI,mBAAmB,EAAE,CAAC;YACtB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC5C,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtB,OAAO,QAAQ,CAAC;gBACpB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACH,gEAAgE;IACzD,qBAAqB,CAAC,QAAgB,EAAsC;kCAApC,iEAA+B,KAAK;QAC/E,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC;IACrE,CAAC;IAED;;;;;OAKG,CACI,qBAAqB,CAAC,QAAgB,EAAsC;kCAApC,iEAA+B,KAAK;QAC/E,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;;;;OAKG,CACI,eAAe,CAAC,EAAU,EAAsC;kCAApC,iEAA+B,KAAK;QACnE,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;IACtE,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,IAAY,EAAsC;kCAApC,iEAA+B,KAAK;QACvE,OAAO,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;OAKG,CACI,mBAAmB,CAAC,EAAU,EAAsC;kCAApC,iEAA+B,KAAK;QACvE,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YAC9D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QACD,IAAI,mBAAmB,EAAE,CAAC;YACtB,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;gBACnE,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBACvC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACtC,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,QAAgB,EAAA;QACxC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACxD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,IAAY,EAAA;QAChC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACxD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,EAAU,EAAA;QAC3B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACvD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,QAAgB,EAAA;QACvC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACvD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,IAAY,EAAA;QAC/B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACvD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACpC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,EAAU,EAAA;QACzB,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,EAAE,CAAE,CAAC;YACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC/C,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;gBACrE,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBACtC,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACrC,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,IAAY,EAAA;QAC7B,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,EAAE,CAAE,CAAC;YACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC/C,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;gBACrE,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;oBAC1C,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACrC,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,IAAY,EAAA;QAC9B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,EAAU,EAAA;QAC1B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,QAAgB,EAAA;QACtC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,EAAU,EAAA;QACnC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC/D,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACxC,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,EAAU,EAAA;QAC7B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC1D,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,sBAAsB,CAAC,QAAgB,EAAA;QAC3C,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC1D,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBAC/C,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBAClC,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,IAAY,EAAA;QACnC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC3D,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACxC,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,QAAkB,EAAE,KAAe,EAAA;QACnD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gKAE3B,QAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,QAAkB,EAAA;QACpC,IAAI,KAAK,CAAC;QACV,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACtD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtB,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,IAAI,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACjE,IAAI,YAAY,EAAE,CAAC;gBACf,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;gBACtC,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBAC7B,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;QAEtB,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,EAAU,EAAA;QACzB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,EAAU,EAAA;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAU,CAAC;YACjC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,EAAU,EAAA;QAClC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC9D,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,0BAA0B,CAAC,QAAgB,EAAA;QAC9C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC9D,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACnD,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,EAAU,EAAA;QACnC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAU,CAAC;YACzC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC;QACvB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,QAAgB,EAAA;QACrC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,EAAU,EAAA;QAC7B,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YAC3D,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,wBAAwB,CAAC,EAAU,EAAA;QACtC,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YACnE,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,EAAU,EAAA;QAC9B,IAAI,KAAa,CAAC;QAClB,IAAK,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YACvD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,IAAK,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YAC/D,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAED,IAAK,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YACxD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;QAED,IAAK,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YACvD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,EAAU,EAAA;QACzB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,IAAI,EAAE,CAAC;YACP,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,aAAa,EAAE,CAAC;YAChB,OAAO,aAAa,CAAC;QACzB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QACpC,IAAI,KAAK,EAAE,CAAC;YACR,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,MAAM,EAAE,CAAC;YACT,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAClC,IAAI,IAAI,EAAE,CAAC;YACP,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,IAAY,EAAA;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;YACP,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,aAAa,EAAE,CAAC;YAChB,OAAO,aAAa,CAAC;QACzB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,KAAK,EAAE,CAAC;YACR,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,MAAM,EAAE,CAAC;YACT,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;YACP,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,IAAY,EAAA;QAC7B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACnC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,sBAAsB,CAAC,IAAY,EAAA;QACtC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC9D,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBAC3C,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,EAAU,EAAA;QACjC,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YAC9D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,QAAgB,EAAA;QACzC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACzD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,EAAU,EAAA;QAC7B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACzD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,IAAY,EAAA;QACjC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACzD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACtC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,yBAAyB,CAAC,EAAU,EAAA;QACvC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACnE,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;gBAClD,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,EAAU,EAAA;QAChC,IAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,YAAY,CAAE,CAAC;YACxF,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YAClE,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,UAAU,EAAE,EAAE,KAAK,CAAE,CAAC;gBACjE,MAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACnD,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBACnB,OAAO,MAAM,CAAC;gBAClB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,IAAY,EAAA;QACpC,IAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,YAAY,CAAE,CAAC;YACxF,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;YAClE,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,UAAU,EAAE,EAAE,KAAK,CAAE,CAAC;gBACjE,MAAM,MAAM,GAAG,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACnD,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;oBACvB,OAAO,MAAM,CAAC;gBAClB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,IAAY,EAAA;QACpC,IAAK,IAAI,gBAAgB,GAAG,CAAC,EAAE,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,gBAAgB,CAAE,CAAC;YAC9F,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;YACzD,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBAC5B,OAAO,WAAW,CAAC;YACvB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,IAAkB,EAAA;QAClC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG,CACH,IAAW,GAAG,GAAA;QACV,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,2JAAG,QAAK,CAAC,QAAQ,EAAE,CAAC;QACjC,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG,CACI,eAAe,CAAmB,GAAW,EAAE,IAAO,EAAA;QACzD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,uKAAI,mBAAgB,EAAU,CAAC;QACxD,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAI,GAAW,EAAA;QACjC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAU,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;OAKG,CACI,+BAA+B,CAAmB,GAAW,EAAE,OAAyB,EAAA;QAC3F,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,uKAAI,mBAAgB,EAAU,CAAC;QACxD,CAAC;QACD,OAAU,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACnE,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,GAAW,EAAA;QACjC,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1C,CAAC;IAEO,gBAAgB,CAAC,OAAgB,EAAE,IAAkB,EAAE,WAAyB,EAAE,SAAkB,EAAA;QACxG,IAAI,SAAS,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YACxD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAE,CAAC;gBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC/B,CAAC;YAED,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YACvC,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBAC9C,iBAAiB;gBACjB,IAAI,QAAQ,CAAC,uBAAuB,IAAI,QAAQ,CAAC,uBAAuB,IAAI,IAAI,EAAE,CAAC;oBAC/E,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBACpD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAExC,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,uBAAuB,EAAE,CAAC,CAAC;oBAC3F,CAAC;gBACL,CAAC;gBAED,WAAW;gBACX,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,sBAAsB,GAAA;QACzB,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;IACvC,CAAC;IAID;;;;OAIG,CACH,IAAW,uCAAuC,GAAA;QAC9C,OAAO,IAAI,CAAC,0CAA0C,CAAC;IAC3D,CAAC;IAED,IAAW,uCAAuC,CAAC,KAAc,EAAA;QAC7D,IAAI,IAAI,CAAC,0CAA0C,KAAK,KAAK,EAAE,CAAC;YAC5D,OAAO;QACX,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,0CAA0C,GAAG,KAAK,CAAC;IAC5D,CAAC;IAED;;OAEG,CACI,gBAAgB,GAAA;QACnB,IAAI,IAAI,CAAC,uCAAuC,EAAE,CAAC;YAC/C,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YACvD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC9C,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACjD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;gBAC3C,IAAI,YAAY,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC;oBAC7C,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gBACzC,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,mBAAmB,GAAA;QACtB,IAAI,IAAI,CAAC,uCAAuC,EAAE,CAAC;YAC/C,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;QACjD,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACjC,IAAI,OAAO,IAA0B,OAAQ,CAAC,UAAU,EAAE,CAAC;oBACjC,OAAQ,CAAC,mBAAmB,EAAE,CAAC;gBACzD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,0BAA0B,GAAA;QAC7B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IA4BD;;;;;;;;OAQG,CACI,kBAAkB,GAKK;uCAJ1B,wBAAwB,yCAAG,KAAK,EAChC,SAAsB,iDACtB,OAAmC,gEACnC,YAAY,qDAAG,IAAI,uBACnB,kBAAkB,+CAAG,KAAK;QAE1B,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrB,IAAI,OAAO,EAAE,CAAC;oBACV,OAAO,CAAC,wBAAwB,CAAC,CAAC;gBACtC,CAAC;gBACD,OAAO;YACX,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACjC,CAAC;YAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,IAAI,CAAC,kCAAkC,GAAG,kBAAkB,CAAC;YAC7D,IAAI,CAAC,mCAAmC,GAAG,wBAAwB,CAAC;YAEpE,IAAI,YAAY,EAAE,CAAC;gBACf,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBAC7D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;gBAC7C,CAAC;YACL,CAAC;YACD,IAAI,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,CAAC;YAChB,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,oBAAoB,GAAA;QACvB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChC,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBACrC,IAAI,CAAC,6BAA6B,CAAC,SAAS,GAAG,KAAK,CAAC;YACzD,CAAC;QACL,CAAC;QAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC7D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,8BAA8B,CAAC,SAA0B,EAAA;QAC7D,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,sBAAsB,CAAC;QAE/H,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACzE,OAAO,CAAC,gCAAgC;QAC5C,CAAC;QAED,qDAAqD;QACrD,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,GAAG,CAAG,CAAD,QAAU,CAAC,OAAO,EAAE,CAAC,CAAC;IACrE,CAAC;IAEO,qBAAqB,GAAA;QACzB,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,KAAK,GAAA,MAAS,CAAC,sBAAsB,EAAE,CAAC;YAC5G,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;;0CAC5B,CAAC,YAAY,uDAAjB,mBAAmB,aAAa,CAAC,KAAK,EAAE,CAAC;gBACzC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;gBAC3B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBAC/B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;gBACjC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;gBACpC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;gBAC9B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;YACxC,CAAC;YACD,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACxD,IAAI,CAAC,IAAI,CAAC,mCAAmC,EAAE,CAAC;gBAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBACtC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE,CAAC;oBAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC9B,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;gBACpD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,CAAE,CAAC;oBAChC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;gBAClD,CAAC;YACL,CAAC;YAED,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;YAEtC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,wCAAwC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEpE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QAErC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,8BAA8B,CAAE,CAAC;YACrD,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC;QAED,4BAA4B;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE9C,kBAAkB;QAClB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACvF,IAAI,UAAU,EAAE,CAAC;gBACb,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,CAAC,MAAM,CAAC;gBACJ,UAAU,GAAG;oBAAC,IAAI;oBAAE,CAAC,CAAC;iBAAC,CAAC;gBACxB,IAAI,CAAC,6BAA6B,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YACtF,CAAC;YACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,SAAS;YACb,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC,CAAC;YAE7D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACzE,SAAS;YACb,CAAC;YAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,gBAAgB;YAChB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAA,KAAS,CAAC,iCAAiC,EAAE,SAAS,CAAC,gCAAgC,CAAC,EAAE,CAAC;gBACzJ,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC;YAED,wBAAwB;YACxB,IAAI,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC7H,UAAU,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;YAC7B,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC9B,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;gBACtD,SAAS;YACb,CAAC;YAED,2CAA2C;YAC3C,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;gBAC5D,YAAY,CAAC,kBAAkB,EAAE,CAAC;YACtC,CAAC;YAED,IAAI,CAAC,YAAY,EAAE,CAAC;YAEpB,IACI,IAAI,CAAC,SAAS,IACd,IAAI,CAAC,UAAU,GAAG,CAAC,IACnB,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,IACpD,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EACvG,CAAC;gBACC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE3C,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;oBACxB,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBAClD,CAAC;gBAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,mBAAmB,CAAE,CAAC;oBAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACtB,CAAC;gBAED,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;oBACxC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;wBACrB,YAAY,CAAC,6BAA6B,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBACzE,CAAC,MAAM,CAAC;wBACJ,IAAI,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE,CAAC;4BACvD,YAAY,GAAG,IAAI,CAAC;wBACxB,CAAC;oBACL,CAAC;oBACD,YAAY,CAAC,6BAA6B,CAAC,SAAS,GAAG,IAAI,CAAC;oBAC5D,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;gBACzC,CAAC;gBAED,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,uCAAuC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEnE,mBAAmB;QACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAChE,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,aAAa,EAAE,CAAE,CAAC;gBACvF,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;gBAE3D,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;oBACzD,SAAS;gBACb,CAAC;gBAED,MAAM,OAAO,GAAQ,cAAc,CAAC,OAAO,CAAC;gBAC5C,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;oBAC3C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBACjD,cAAc,CAAC,OAAO,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;YACD,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACnE,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,gBAAgB,CAAC,IAAkB,EAAA;QACtC,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACxB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAO,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACtE,IAAK,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,UAAwB,EAAE,IAAkB,EAAA;QAC5D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAE5B,IAAI,SAAS,GAAG,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,kCAAkC,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,wBAAwB,CAAC;QAE5K,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;YAC7B,SAAS,GAAG,SAAS,IAAI,GAAG,KAAK,CAAC,CAAC;YACnC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC3B,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,qBAAqB,CAAC,KAAe,EAAA;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAI,YAAY,CAAC,mBAAmB,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,aAAa,EAAE,EAAE,WAAW,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;QACpK,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,aAAa,EAAE,EAAE,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;QACnG,CAAC;IACL,CAAC;IAKO,gBAAgB,CAAC,MAAwB,EAAc;oBAAZ,KAAK,4DAAG,IAAI;QAC3D,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC/B,IAAI,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBACrC,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;YAChD,CAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBAC7C,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;YACjD,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,uCAAuC,EAAE,EAAE,CAAC;oBAC1D,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC;gBAC7C,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,MAAwB,EAAA;QAC9C,6DAA6D;QAC7D,IAAI,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;QACrC,cAAc;QAClB,CAAC,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,kBAAkB,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;YAC5E,MAAM,GAAG,GAAG,MAAM,CAAC,kBAAkB,CAAC;YACtC,IAAI,GAAG,CAAC,iBAAiB,CAAC,YAAY,EAAE,EAAE,CAAC;gBACvC,GAAG,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxD,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBACxD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACjB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBACtF,CAAC;gBACD,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;YACxB,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBACnC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;gBACvC,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;IACL,CAAC;IAID;;OAEG,CACI,gBAAgB,CAAC,MAAc,EAAE,SAAkB,EAAwB;8BAAtB,eAAe,kDAAG,IAAI;;QAC9E,IAAI,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAClC,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5B,2EAA2E;QAC3E,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;QAED,WAAW;QACX,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE/C,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC;YACnC,IAAI,gBAAgB,GAAG,IAAI,CAAC;YAC5B,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBAC1D,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,CAAC;gBAC9D,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACjB,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;oBACxC,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,KAAK,CAAC;gBACvD,CAAC;YACL,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC1C,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBAC1D,MAAM,CAAC,kBAAkB,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;YAClE,CAAC;QACL,CAAC;QAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEvE,SAAS;QACT,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,oBAAoB;QACpB,IAAK,IAAI,wBAAwB,GAAG,CAAC,EAAE,wBAAwB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,wBAAwB,EAAE,CAAE,CAAC;YAC/H,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAExE,IAAI,CAAC,aAAa,CAAW,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,qCAAqC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEjE,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAExE,IAAI,MAAM,CAAC,mBAAmB,IAAI,MAAM,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,SAAS,IAAI,SAAS,CAAC,mBAAmB,IAAI,SAAS,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzF,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;YACpE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAyC,CAAC,CAAC;QACxF,CAAC;QAED,oDAAoD;QACpD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qCAAqC,CAAE,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YAEnC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wKACjC,QAAK,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChF,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,CAAE,CAAC;oBAChF,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC3D,IAAI,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;wBAC/B,IAAI,CAAC,SAAS,EAAE,CAAC;wBACjB,MAAM,4BAA4B,GAAG,YAAY,CAAC,YAAY,IAAI,YAAY,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CAAC;wBAClH,YAAY,CAAC,MAAM,CAAU,4BAA4B,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;wBACvF,UAAU,GAAG,IAAI,CAAC;oBACtB,CAAC;gBACL,CAAC;wKACD,QAAK,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAE9E,IAAI,CAAC,SAAS,EAAE,CAAC;YACrB,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,4BAA4B,CAAE,CAAC;gBACnD,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC;YAC9D,CAAC;YAED,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;QACxC,CAAC;;QAED,IAAI,CAAC,OAAO,CAAC,mBAAmB,2FAAU,kBAAkB,cAAzB,MAAM,sEAAqB,YAAY,6GAAI,MAAM,CAAC,YAAY,uCAAI,SAAS,CAAC,eAAe,CAAC;QAE/H,iDAAiD;QACjD,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAC9B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEhE,gBAAgB;QAChB,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACxE,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;QAC5C,CAAC;QAED,qBAAqB;QACrB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,sBAAsB,CAAE,CAAC;YAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC;QAED,SAAS;QACT,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEvD,IAAI,MAAM,CAAC,iBAAiB,IAAI,MAAM,CAAC,qBAAqB,KAAK,GAAA,MAAS,CAAC,sBAAsB,EAAE,CAAC;YAChG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEtD,oBAAoB;QACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;YACvD,0FAA0F;YAC1F,MAAM,OAAO,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAa,CAAC,CAAC,CAAC,SAAS,CAAC;YAChG,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAC3E,CAAC;QAED,qBAAqB;QACrB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,4BAA4B,CAAE,CAAC;YACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAE5B,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1E,CAAC;IAEO,kBAAkB,CAAC,MAAc,EAAwB;8BAAtB,eAAe,kDAAG,IAAI;QAC7D,IAAI,MAAM,CAAC,aAAa,KAAK,KAAA,IAAS,CAAC,EAAA,WAAa,IAAI,IAAA,EAAM,CAAC,mBAAmB,EAAE,CAAC;YACjF,IAAI,MAAM,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACzD,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/B,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;YAC1D,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC3D,OAAO;QACX,CAAC;QAED,IAAI,MAAM,CAAC,yBAAyB,EAAE,CAAC;YACnC,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC,MAAM,CAAC;YACJ,cAAc;YACd,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC5D,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC7D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;QAED,2EAA2E;QAC3E,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC/D,CAAC;IAEO,mBAAmB,GAAA;QACvB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACvE,MAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAE5D,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC5B,SAAS;YACb,CAAC;YAED,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,UAAU,CAAC,aAAa,IAAI,WAAW,GAAG,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,CAAE,CAAC;gBACzH,MAAM,MAAM,GAAY,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBAEtE,IAAI,MAAM,CAAC,OAAO,KAAK,MAAA,GAAS,CAAC,GAAA,OAAA,KAAA,IAAA,cAAiC,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,gCAAgC,EAAE,CAAC;oBAClI,MAAM,UAAU,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;oBAChD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC;oBAEjE,MAAM,eAAe,GAAG,SAAS,CAAC,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,sBAAsB,CAAC,CAAC;oBAChG,MAAM,6BAA6B,GAAG,UAAU,CAAC,wBAAwB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBAE7F,IAAI,eAAe,IAAI,6BAA6B,KAAK,CAAC,CAAC,EAAE,CAAC;wBAC1D,IAAI,MAAM,CAAC,OAAO,KAAK,IAAA,KAAS,CAAC,iCAAiC,EAAE,CAAC;4BACjE,MAAM,CAAC,eAAe,CAAC,+KAAW,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;4BAChF,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACxD,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,IAAA,KAAS,CAAC,gCAAgC,EAAE,CAAC;4BACvE,UAAU,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACxD,CAAC;oBACL,CAAC,MAAM,IAAI,CAAC,eAAe,IAAI,6BAA6B,GAAG,CAAC,CAAC,EAAE,CAAC;wBAChE,uCAAuC;wBAEvC,oDAAoD;wBACpD,IAAI,MAAM,CAAC,OAAO,KAAK,IAAA,KAAS,CAAC,gCAAgC,EAAE,CAAC;4BAChE,MAAM,CAAC,eAAe,iKAAC,eAAW,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;wBACpF,CAAC;wBAED,+GAA+G;wBAC/G,IACI,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAA,CAAA,IAAS,CAAC,gCAAgC,EAAE,CAAC,SAAS,EAAE,EAAE;4BACnG,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;4BAClE,OAAO,SAAS,KAAK,aAAa,CAAC;wBACvC,CAAC,CAAC,IACF,MAAM,CAAC,OAAO,KAAK,IAAA,KAAS,CAAC,gCAAgC,EAC/D,CAAC;4BACC,UAAU,CAAC,wBAAwB,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;wBACjF,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,yBAAyB,CAAC,IAAY,EAAA;IACzC,8EAA8E;IAClF,CAAC;IAUD,cAAA,EAAgB,CACT,QAAQ,CAAC,eAAwB,EAAA;IACpC,8DAA8D;IAClE,CAAC;IAED,yCAAA,EAA2C,CACpC,OAAO,GAAA;QACV,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC;YACzC,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAEhI,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YACpD,MAAM,UAAU,GAAG,MAAM,GAAG,gBAAgB,GAAG,MAAM,CAAC;YAEtD,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAEvD,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,gBAAgB,CAAC,CAAC;YAC7D,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAErD,MAAO,SAAS,GAAG,CAAC,IAAI,UAAU,GAAG,aAAa,CAAE,CAAC;gBACjD,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAElD,aAAa;gBACb,IAAI,CAAC,eAAe,GAAG,gBAAgB,GAAG,UAAU,CAAC;gBACrD,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAChC,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAEvD,UAAU;gBACV,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;gBACrD,CAAC;gBAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACjD,IAAI,CAAC,cAAc,EAAE,CAAC;gBAEtB,UAAU,EAAE,CAAC;gBACb,SAAS,IAAI,gBAAgB,CAAC;YAClC,CAAC;YAED,IAAI,CAAC,gBAAgB,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC1D,CAAC,MAAM,CAAC;YACJ,aAAa;YACb,MAAM,SAAS,GAAG,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;YACpJ,IAAI,CAAC,eAAe,GAAG,SAAS,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEvD,UAAU;YACV,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;IACL,CAAC;IAEO,MAAM,GAAA;QACV,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACvK,CAAC;IACL,CAAC;IAEO,wBAAwB,CAAC,MAAwB,EAAA;YAIjD,MAAM;QAHV,IAAI,MAAM,kDAAE,kBAAkB,KAAI,kDAAC,MAAM,CAAE,WAAW,GAAE,CAAC;YACrD,MAAM,CAAC,kBAAkB,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC/C,CAAC;QACD,iFAAY,UAAU,0EAAE,MAAM,EAAE,CAAC;YAC7B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;gBAChD,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC;gBACpD,IAAI,GAAG,EAAE,CAAC;oBACN,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACzB,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,cAAc,CAAC,MAAe,EAAA;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC7B,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;IACL,CAAC;IAOO,qBAAqB,GAA+C;4BAA9C,aAAa,oDAAG,IAAI,qBAAE,gBAAgB,iDAAG,KAAK;YAoExE,mBAAmB;;QAnEnB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAE9B,iBAAiB;QACjB,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAChC,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,MAAM,CAAC,aAAa,KAAK,GAAA,MAAS,CAAC,aAAa,EAAE,CAAC;oBACnD,cAAc;oBACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;wBAC7D,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;oBACvC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEpD,oFAAoF;QACpF,kEAAkE;QAClE,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAE,CAAC;YACxC,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC;QAED,iBAAiB;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC9C,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;QAE1B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAE5B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,SAAS;YACb,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,KAAK,CAAC,CAAC;YAE7D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACzE,SAAS;YACb,CAAC;YAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAE1B,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAA,KAAS,CAAC,iCAAiC,EAAE,SAAS,CAAC,gCAAgC,CAAC,EAAE,CAAC;gBACzJ,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,aAAa,EAAE,CAAE,CAAC;gBACvF,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;gBAE3D,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;oBACzD,SAAS;gBACb,CAAC;gBAED,MAAM,OAAO,GAAQ,cAAc,CAAC,OAAO,CAAC;gBAC5C,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;oBAC3C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;oBACjD,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC7B,CAAC;YACL,CAAC;QACL,CAAC;gCAGG,CAAC,UAAU,qDAAf,iBAAiB,OAAO,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG,CACI,mBAAmB,CAAC,YAAiC,EAAE,YAA8B,EAAoD;mCAAlD,oBAAoB,6CAAG,KAAK,iBAAE,YAAY,qDAAG,KAAK;QAC5I,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACnC,IAAI,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,EAAE,CAAC;YAEjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;YAEjC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC7C,CAAC;YAED,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAErD,SAAS;YACT,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAE7B,YAAY,CAAC,MAAM,CAAC,oBAAoB,EAAE,YAAY,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACxC,CAAC;IAED;;;;OAIG,CACI,MAAM,GAA+C;4BAA9C,aAAa,oDAAG,IAAI,qBAAE,gBAAgB,iDAAG,KAAK;YAYpD;QAXJ,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE,CAAC;YACpF,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjD,+BAAQ,CAAC,aAAa,4EAAE,MAAM,EAAE,CAAC;YAC7B,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAE,CAAC;gBACjC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC;QACL,CAAC;QAED,qEAAqE;QACrE,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;QACtC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QACpC,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;QAClC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAExD,UAAU;QACV,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAC5E,CAAC;QAED,aAAa;QACb,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;QAED,6BAA6B;QAC7B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,wBAAwB,CAAE,CAAC;YAC/C,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC;QAED,iBAAiB;QACjB,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtD,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,CAAE,CAAC;oBAC/E,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;oBAC/C,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChB,IAAI,MAAM,CAAC,aAAa,KAAK,GAAA,MAAS,CAAC,aAAa,EAAE,CAAC;wBACnD,cAAc;wBACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;4BAC7D,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;wBACvC,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC3B,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;gBAC3B,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,KAAK,GAAA,MAAS,CAAC,aAAa,EAAE,CAAC;oBAC9D,cAAc;oBACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;wBACxE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;oBAClD,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,SAAS,CAAC,eAAe,CAAC;YAE7D,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;QAC/D,CAAC,MAAM,CAAC;gBAOwB;YAN5B,gBAAgB;YAChB,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEpD,yBAAyB;YACzB,IAAI,CAAC,qCAAqC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEjE,MAAM,mBAAmB,gCAAO,CAAC,aAAa,8EAAE,MAAM,CAAC,CAAC,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;YACnG,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wKAC5B,QAAK,CAAC,uBAAuB,CAAC,uBAAuB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC5F,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,CAAE,CAAC;oBACrF,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;oBAC3D,MAAM,YAAY,GAAG,YAAY,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;oBAEpE,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,YAAY,EAAE,mBAAmB,KAAK,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAC3H,CAAC;wKACD,QAAK,CAAC,qBAAqB,CAAC,uBAAuB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC1F,IAAI,CAAC,SAAS,EAAE,CAAC;YACrB,CAAC;;YAED,IAAI,CAAC,OAAO,CAAC,mBAAmB,sIAAwB,YAAY,6DAAjC,mBAAmB,iBAAkB,SAAS,CAAC,eAAe,CAAC;YAElG,sBAAsB;YACtB,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC;YACxC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,MAAA,CAAA,EAAS,CAAC,CAAA,CAAA,OAAA,EAAA,IAAe,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACxG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACrD,CAAC;YACD,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEhE,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAE,CAAC;gBACxC,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,CAAC;YAED,QAAQ;YACR,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE1C,oDAAoD;YACpD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,CAAE,CAAC;gBAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACrC,CAAC;YAED,iBAAiB;YACjB,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtD,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,CAAE,CAAC;oBAC/E,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC;gBAC9E,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;oBACrB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACzC,CAAC;gBAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YACvF,CAAC;QACL,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,2CAA2C;QAC3C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,iBAAiB,CAAE,CAAC;YACxC,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC;QAED,eAAe;QACf,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEnD,WAAW;QACX,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC5B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC7D,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBACvC,IAAI,IAAI,EAAE,CAAC;oBACP,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnB,CAAC;YACL,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC;IAC7C,CAAC;IAED;;;;OAIG,CACI,eAAe,GAAA;QAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;QAC/B,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,iBAAiB,GAAA;QACpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,CAAC;QACnD,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAE7B,IAAI,CAAC,mBAAmB,GAAG,EAAc,CAAC;QAE1C,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACpD,iJAAiJ;YACjJ,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAE,CAAC;gBAC/C,UAAU,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;gBAC5C,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC;YACrC,CAAC;YACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,eAAe;QACf,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;QACnC,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;QACtC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;QACtC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;QACvC,IAAI,CAAC,mCAAmC,CAAC,OAAO,EAAE,CAAC;QACnD,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;QAE9B,wBAAwB;QACxB,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACpD,KAAK,MAAM,OAAO,IAAI,cAAc,CAAE,CAAC;YACnC,OAAO,CAAC,KAAK,EAAE,CAAC;QACpB,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAEhC,SAAS;QACT,IAAI,CAAC;YACD,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;qKACT,SAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE,CAAC,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,iBAAiB;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;QAE9C,IAAI,MAAM,EAAE,CAAC;YACT,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACvD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;YACxC,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAExC,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE/B,oBAAoB;QACpB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAElC,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAG,CAAD,GAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,CAAG,CAAD,GAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAErE,kBAAkB;QAClB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAE3B,oBAAoB;QACpB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAExC,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEtC,mBAAmB;QACnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEjC,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE5C,cAAc;QACd,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAElC,aAAa;QACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEpC,qBAAqB;QACrB,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC;QAED,qKAAI,cAAW,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;6KACzC,cAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;YACrC,IAAI,WAAW,oKAAG,cAAW,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YACnD,MAAO,WAAW,IAAI,CAAC,CAAE,CAAC;gBACtB,MAAM,MAAM,oKAAG,cAAW,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAClD,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;qLAC3B,cAAW,CAAC,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC9E,MAAM;gBACV,CAAC;gBACD,WAAW,EAAE,CAAC;YAClB,CAAC;QACL,CAAC;QAED,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAElD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,CAAC;QACnD,IAAI,CAAC,oCAAoC,CAAC,KAAK,EAAE,CAAC;QAClD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,wCAAwC,CAAC,KAAK,EAAE,CAAC;QACtD,IAAI,CAAC,uCAAuC,CAAC,KAAK,EAAE,CAAC;QACrD,IAAI,CAAC,oCAAoC,CAAC,KAAK,EAAE,CAAC;QAClD,IAAI,CAAC,mCAAmC,CAAC,KAAK,EAAE,CAAC;QACjD,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;QAC9C,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;QAC9C,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;QAC9C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,2CAA2C,CAAC,KAAK,EAAE,CAAC;QACzD,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,CAAC;QACnD,IAAI,CAAC,mCAAmC,CAAC,KAAK,EAAE,CAAC;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAEO,YAAY,CAAwB,KAAU,EAAE,QAA4B,EAAA;QAChF,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC,QAAQ,8CAAG,QAAQ,GAAK,CAAD,AAAE,IAAI,EAAE,CAAG,CAAD,GAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAClD,KAAK,MAAM,IAAI,IAAI,SAAS,CAAE,CAAC;YAC3B,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC;QACD,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACI,qBAAqB,GAAA;QACxB,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;YAClE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAU,IAAK,CAAC,QAAQ,CAAC;YAEvC,IAAI,QAAQ,EAAE,CAAC;gBACX,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,wBAAwB,GAAA;QAC3B,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACtC,MAAM,MAAM,GAAa,WAAY,CAAC,OAAO,CAAC;YAE9C,IAAI,MAAM,EAAE,CAAC;gBACC,WAAY,CAAC,OAAO,GAAG,IAAI,CAAC;YAC1C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,eAAe,CAAC,eAAiD,EAAA;QACpE,MAAM,GAAG,GAAG,sKAAI,UAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9E,MAAM,GAAG,GAAG,sKAAI,UAAO,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACjF,eAAe,GAAG,eAAe,IAAI,CAAC,GAAG,CAAG,CAAD,GAAK,CAAC,CAAC;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACnD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAE,CAAC;YACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE9B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1E,SAAS;YACb,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAE5C,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;YACrD,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC;YAErD,4KAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;8KACvC,UAAO,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO;YACH,GAAG,EAAE,GAAG;YACR,GAAG,EAAE,GAAG;SACX,CAAC;IACN,CAAC;IAED,UAAU;IAEV,uDAAuD;IACvD;;;;;;;;OAQG,CACI,gBAAgB,CAAC,CAAS,EAAE,CAAS,EAAE,KAAuB,EAAE,MAAwB,EAAyB;8BAAvB,eAAe,kDAAG,KAAK;QACpH,OAAM,4KAAA,AAAW,EAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;;OAUG,CACI,qBAAqB,CACxB,CAAS,EACT,CAAS,EACT,KAAuB,EACvB,MAAW,EACX,MAAwB,EAEI;YAD5B,eAAe,oEAAG,KAAK,yBACvB,oBAAoB,6CAAG,KAAK;QAE5B,qKAAM,cAAA,AAAW,EAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;OAMG,CACI,6BAA6B,CAAC,CAAS,EAAE,CAAS,EAAE,MAAe,EAAA;QACtE,MAAM,6KAAA,AAAW,EAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;;OAOG,CACI,kCAAkC,CAAC,CAAS,EAAE,CAAS,EAAE,MAAW,EAAE,MAAe,EAAA;QACxF,qKAAM,cAAA,AAAW,EAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAKD;;;;;;;;OAQG,CACI,IAAI,CAAC,CAAS,EAAE,CAAS,EAAE,SAAyB,EAAE,SAAmB,EAAE,MAAyB,EAAE,iBAA4C,EAAA;QACrJ,MAAM,IAAI,kKAAG,cAAW,AAAX,EAAY,KAAK,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;qKACP,SAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QACD,6CAA6C;QAC7C,OAAO,wKAAI,cAAW,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG,CACI,oBAAoB,CAAC,CAAS,EAAE,CAAS,EAAE,SAAyB,EAAE,SAAmB,EAAE,MAAyB,EAAA;QACvH,MAAM,IAAI,kKAAG,cAAA,AAAW,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;qKACP,SAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;QACD,6CAA6C;QAC7C,OAAO,wKAAI,cAAW,EAAE,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;;;OAQG,CACI,WAAW,CAAC,GAAQ,EAAE,SAAyB,EAAE,SAAmB,EAAE,iBAA4C,EAAA;QACrH,qKAAM,cAAW,AAAX,EAAY,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;;;;OASG,CACI,SAAS,CAAC,CAAS,EAAE,CAAS,EAAE,SAAyB,EAAE,MAAe,EAAE,iBAA4C,EAAA;QAC3H,qKAAM,cAAW,AAAX,EAAY,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,uDAAuD;IACvD;;;;;;OAMG,CACI,gBAAgB,CAAC,GAAQ,EAAE,SAAyB,EAAE,iBAA4C,EAAA;QACrG,qKAAM,cAAA,AAAW,EAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;OAKG,CACI,kBAAkB,CAAC,IAA4B,EAAE,SAAkB,EAAE,UAAkC,EAAA;QAC1G,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;IACvE,CAAC;IAED;;;OAGG,CACI,kBAAkB,GAAA;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;IACnD,CAAC;IAED,QAAQ;IACR,cAAA,EAAgB,CACT,kBAAkB,GAAA;QACrB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAE,CAAC;YACrC,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpB,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;QACvC,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,CAAE,CAAC;YACvC,SAAS,CAAC,OAAO,EAAE,CAAC;QACxB,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,eAAe,CAAE,CAAC;YACxC,MAAM,CAAC,OAAO,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;gBAC1C,SAAS,CAAC,OAAO,EAAE,CAAC;YACxB,CAAC;QACL,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,gBAAgB,GAAA;QACnB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YAClC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;OAMG,CACK,UAAU,CAAI,IAAS,EAAE,SAAiB,EAAE,MAA6B,EAAA;QAC7E,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC1B,uGAAuG;YACvG,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,IAAK,MAAM,CAAC,IAAI,IAAI,CAAE,CAAC;YACnB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,2JAAI,OAAI,2JAAI,OAAI,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC1E,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG,CACI,eAAe,CAAC,SAAiB,EAAE,MAAwC,EAAA;QAC9E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,SAAiB,EAAE,MAAoC,EAAA;QAC3E,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;OAKG,CACI,eAAe,CAAC,SAAiB,EAAE,MAAkC,EAAA;QACxE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,SAAiB,EAAE,MAAwC,EAAA;QAChF,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAC9H,CAAC;IAED;;;;;OAKG,CACI,uBAAuB,CAAC,SAAiB,EAAE,MAA8C,EAAA;QAC5F,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;;;OAQG,CACI,iBAAiB,CACpB,gBAAwB,EAGqD;kCAF7E,iEAAoE,IAAI,EACxE,0FAAuE,IAAI,6BAC3E,iEAAyE,IAAI;QAE7E,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,wBAAwB,CAAC,CAAC;IACtI,CAAC;IAED;;;;;;;OAOG,CACI,iCAAiC,CAAC,gBAAwB,EAAE,qBAA8B,EAA8B;oBAA5B,KAAK,4DAAG,IAAI,YAAE,OAAO,0DAAG,IAAI;QAC3H,IAAI,CAAC,iBAAiB,CAAC,iCAAiC,CAAC,gBAAgB,EAAE,qBAAqB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACtH,CAAC;IAED;;;;;OAKG,CACI,6BAA6B,CAAC,KAAa,EAAA;QAC9C,OAAO,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;IACvE,CAAC;IAID,cAAA,EAAgB,CACT,iCAAiC,CAAC,KAAc,EAAA;QACnD,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED,0IAAA,EAA4I,CAC5I,IAAW,2BAA2B,GAAA;QAClC,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED,IAAW,2BAA2B,CAAC,KAAc,EAAA;QACjD,IAAI,IAAI,CAAC,4BAA4B,KAAK,KAAK,EAAE,CAAC;YAC9C,OAAO;QACX,CAAC;QAED,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;QAE1C,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,uBAAuB;YACvB,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;QAClE,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,uBAAuB,CAAC,IAAY,EAAE,SAAsC,EAAA;QAC/E,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;YACpC,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,SAAS;YACb,CAAC;YACD,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;IACL,CAAC;IAED;;OAEG,CACI,SAAS,CACZ,SAAwB,EACxB,SAAqE,EACrE,UAAwC,EACxC,iBAA2B,EAC3B,cAAwB,EACxB,OAAmE,EACnE,QAAwC,EAAA;QAExC,MAAM,OAAO,mKAAG,WAAA,AAAQ,EAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACpJ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAkBD;;OAEG,CACI,KAAK,CAAC,cAAc,CACvB,SAAwB,EACxB,UAAgC,EAChC,iBAA2B,EAC3B,cAAwB,EACxB,QAAwC,EAAA;QAExC,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,CAAC,SAAS,CACV,SAAS,EACT,CAAC,IAAI,EAAE,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE;gBACnB,2EAA2E;gBAC3E,MAAM,CAAC,SAAS,CAAC,CAAC;YACtB,CAAC,EACD,QAAQ,CACX,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,YAAY,CACf,GAAW,EACX,SAAqE,EACrE,UAAwC,EACxC,iBAA2B,EAC3B,cAAwB,EACxB,OAA2C,EAC3C,QAAwC,EAAA;QAExC,MAAM,OAAO,mKAAG,cAAA,AAAW,EAAC,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACjJ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG,CACI,KAAK,CAAC,iBAAiB,CAC1B,GAAW,EACX,UAAwC,EACxC,iBAA2B,EAC3B,cAAwB,EACxB,QAAwC,EAAA;QAExC,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,CAAC,YAAY,CACb,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,UAAU,EACV,iBAAiB,EACjB,cAAc,EACd,CAAC,KAAK,EAAE,EAAE;gBACN,MAAM,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC,EACD,QAAQ,CACX,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,SAAS,CACZ,IAAU,EACV,SAA+C,EAC/C,UAAuC,EACvC,cAAwB,EACxB,OAAwC,EAAA;QAExC,MAAM,OAAO,mKAAG,WAAA,AAAQ,EAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QAC/E,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG,CACI,KAAK,CAAC,cAAc,CAAC,IAAU,EAAE,UAAuC,EAAE,cAAwB,EAAA;QACrG,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,CAAC,SAAS,CACV,IAAI,EACJ,CAAC,IAAI,EAAE,EAAE;gBACL,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,UAAU,EACV,cAAc,EACd,CAAC,KAAK,EAAE,EAAE;gBACN,MAAM,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAQD,uDAAuD;IACvD;;;OAGG,CACI,gBAAgB,GAAA;QACnB,qKAAM,cAAA,AAAW,EAAC,iCAAiC,CAAC,CAAC;IACzD,CAAC;IAED,aAAa;IAEb;;;;;OAKG,CACH,gEAAgE;IAChE,mBAAmB,CAAC,EAAU,EAAA;QAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,eAAe,CAAC,EAAU,EAAA;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,mBAAmB,CAAC,EAAU,EAAA;QAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;IAED;;;;;OAKG,CACH,gEAAgE;IAChE,oBAAoB,CAAC,QAAgB,EAAA;QACjC,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,aAAa,CAAC,EAAU,EAAA;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,mBAAmB,CAAC,QAAgB,EAAA;QAChC,OAAO,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC9C,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,WAAW,CAAC,EAAU,EAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,YAAY,CAAC,EAAU,EAAA;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IACjC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,kBAAkB,CAAC,QAAgB,EAAA;QAC/B,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,qBAAqB,CAAC,EAAU,EAAA;QAC5B,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,eAAe,CAAC,EAAU,EAAA;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,WAAW,CAAC,EAAU,EAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,iBAAiB,CAAC,QAAgB,EAAA;QAC9B,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,eAAe,CAAC,EAAU,EAAA;QACtB,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,aAAa,CAAC,EAAU,EAAA;QACpB,OAAO,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,oBAAoB,CAAC,EAAU,EAAA;QAC3B,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;IACzC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,0BAA0B,CAAC,QAAgB,EAAA;QACvC,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;IACrD,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,qBAAqB,CAAC,EAAU,EAAA;QAC5B,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,WAAW,CAAC,EAAU,EAAA;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,gBAAgB,CAAC,EAAU,EAAA;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;IACD;;;;;OAKG,CACH,gEAAgE;IAChE,mBAAmB,CAAC,EAAU,EAAA;QAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,CAAC;IAtyID;;;;OAIG,CACH,YAAY,MAAsB,EAAE,OAAsB,CAAA;QAztD1D,cAAA,EAAgB,CACT,IAAA,CAAA,aAAa,GAAG,8KAAI,eAAY,CAAC,IAAI,CAAC,CAAC;QAE9C,kIAAA,EAAoI,CAC7H,IAAA,CAAA,sBAAsB,GAAqB,IAAI,CAAC;QAEvD,cAAA,EAAgB,CACA,IAAA,CAAA,QAAQ,GAAG,IAAI,CAAC;QAEhC,cAAA,EAAgB,CACT,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG,CACI,IAAA,CAAA,SAAS,GAAG,IAAI,CAAC;QACxB;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,IAAI,CAAC;QAE/B,IAAA,CAAA,WAAW,GAAW,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAE7D;;WAEG,CACI,IAAA,CAAA,6BAA6B,GAAG,iKAAI,aAAU,EAAU,CAAC;QAgBhE;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAY1C;;;;;;;WAOG,CACI,IAAA,CAAA,oBAAoB,GAAW,CAAC,CAAC;QAExC;;;WAGG,CACI,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QAgBhB,IAAA,CAAA,oBAAoB,GAAA,EAAA,+CAAA,GAA+C;QAE3E;;WAEG,CACI,IAAA,CAAA,2CAA2C,GAAG,iKAAI,aAAU,EAA4B,CAAC;QAuCxF,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAgBxB,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAc7B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QA6ClC;;WAEG,CACI,IAAA,CAAA,SAAS,GAAW,EAAE,CAAC;QAE9B;;WAEG,CACI,IAAA,CAAA,OAAO,GAAa,EAAE,CAAC;QAE9B;;;WAGG,CACI,IAAA,CAAA,MAAM,GAAY,EAAE,CAAC;QAE5B;;WAEG,CACI,IAAA,CAAA,MAAM,GAAmB,EAAE,CAAC;QAEnC;;;WAGG,CACI,IAAA,CAAA,SAAS,GAAe,EAAE,CAAC;QAElC;;;WAGG,CACI,IAAA,CAAA,eAAe,GAAsB,EAAE,CAAC;QAO/C;;WAEG,CACI,IAAA,CAAA,UAAU,GAAgB,EAAE,CAAC;QAEpC;;;WAGG,CACI,IAAA,CAAA,eAAe,GAAqB,EAAE,CAAC;QAE9C;;;WAGG,CACI,IAAA,CAAA,cAAc,GAAoB,EAAE,CAAC;QAE5C;;;;;;WAMG,CACI,IAAA,CAAA,SAAS,GAAe,EAAE,CAAC;QAElC;;;WAGG,CACI,IAAA,CAAA,mBAAmB,GAAyB,EAAE,CAAC;QAEtD;;WAEG,CACI,IAAA,CAAA,UAAU,GAAe,EAAE,CAAC;QAEnC;;;;;;WAMG,CACI,IAAA,CAAA,cAAc,GAAoB,EAAE,CAAC;QAE5C;;WAEG,CACI,IAAA,CAAA,cAAc,GAA4B,EAAE,CAAC;QAEpD;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAkB,EAAE,CAAC;QAEpC,cAAA,EAAgB,CACN,IAAA,CAAA,mBAAmB,GAA0B,IAAI,CAAC;QAwB5D;;WAEG,CACI,IAAA,CAAA,aAAa,GAAkB,EAAE,CAAC;QAEzC;;;;WAIG,CACI,IAAA,CAAA,YAAY,GAAuB,EAAE,CAAC;QAE7C;;WAEG,CACI,IAAA,CAAA,MAAM,GAA2B,IAAI,CAAC;QAE7C;;WAEG,CACI,IAAA,CAAA,MAAM,GAAiB,EAAE,CAAC;QAEjC;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAA2B,EAAE,CAAC;QAErD;;;WAGG,CACI,IAAA,CAAA,kBAAkB,GAA6B,EAAE,CAAC;QAiBzD;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAExB,IAAA,CAAA,4BAA4B,GAA0C,IAAI,CAAC;QAanF;;;WAGG,CACI,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QAC7C;;;WAGG,CACI,IAAA,CAAA,gCAAgC,GAAG,KAAK,CAAC;QAEhD;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG,SAAS,CAAC;QAC/B;;WAEG,CACI,IAAA,CAAA,aAAa,GAAW,EAAE,CAAC;QAClC;;WAEG,CACI,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAClC;;;WAGG,CACI,IAAA,CAAA,2BAA2B,GAAG,IAAI,CAAC;QAE1C;;;WAGG,CACI,IAAA,CAAA,yBAAyB,GAAG,IAAI,CAAC;QAExC,WAAW;QACX;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAQ,IAAI,CAAC;QAE5B;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAQ,IAAI,CAAC;QAOrC;;WAEG,CACI,IAAA,CAAA,mCAAmC,GAAa,EAAE,CAAC;QAE1D;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,iKAAI,aAAU,EAAS,CAAC;QAE7C,IAAA,CAAA,kBAAkB,GAA8B,IAAI,CAAC;QAS7D;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,iKAAI,aAAU,EAAS,CAAC;QAElD,IAAA,CAAA,uBAAuB,GAA8B,IAAI,CAAC;QAWlE;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAG,iKAAI,aAAU,EAAS,CAAC;QAEzD;;;WAGG,CACI,IAAA,CAAA,6BAA6B,GAAG,iKAAI,aAAU,EAAU,CAAC;QAExD,IAAA,CAAA,sBAAsB,GAA8B,IAAI,CAAC;QAYjE;;WAEG,CACI,IAAA,CAAA,4BAA4B,GAAG,IAAI,0KAAU,EAAS,CAAC;QAE9D;;WAEG,CACI,IAAA,CAAA,2BAA2B,GAAG,iKAAI,aAAU,EAAS,CAAC;QAE7D;;WAEG,CACI,IAAA,CAAA,2BAA2B,GAAG,iKAAI,aAAU,EAAS,CAAC;QAE7D;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,iKAAI,aAAU,EAAS,CAAC;QAE5D;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAG,iKAAI,aAAU,EAAS,CAAC;QAEnD;;WAEG,CACI,IAAA,CAAA,8BAA8B,GAAG,iKAAI,aAAU,EAAU,CAAC;QAEzD,IAAA,CAAA,6BAA6B,GAA+B,IAAI,CAAC;QAUzE;;;WAGG,CACI,IAAA,CAAA,6BAA6B,GAAG,IAAI,0KAAU,EAAU,CAAC;QAExD,IAAA,CAAA,4BAA4B,GAA+B,IAAI,CAAC;QASxE;;WAEG,CACI,IAAA,CAAA,wCAAwC,GAAG,iKAAI,aAAU,EAAS,CAAC;QAE1E;;WAEG,CACI,IAAA,CAAA,uCAAuC,GAAG,iKAAI,aAAU,EAAS,CAAC;QAEzE;;;WAGG,CACI,IAAA,CAAA,oCAAoC,GAAG,gKAAI,cAAU,EAAS,CAAC;QAEtE;;;WAGG,CACI,IAAA,CAAA,mCAAmC,GAAG,iKAAI,aAAU,EAAS,CAAC;QAErE;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,IAAI,0KAAU,EAAS,CAAC;QAExD;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,iKAAI,aAAU,EAAU,CAAC;QAE7D;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,iKAAI,aAAU,EAAU,CAAC;QAE5D;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,iKAAI,aAAU,EAAS,CAAC;QAE3D;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,iKAAI,aAAU,EAAS,CAAC;QAE1D;;WAEG,CACI,IAAA,CAAA,4BAA4B,GAAG,iKAAI,aAAU,EAAY,CAAC;QAEjE;;WAEG,CACI,IAAA,CAAA,2BAA2B,GAAG,IAAI,0KAAU,EAAY,CAAC;QAEhE;;WAEG,CACI,IAAA,CAAA,iCAAiC,GAAG,iKAAI,aAAU,EAAiB,CAAC;QAE3E;;WAEG,CACI,IAAA,CAAA,gCAAgC,GAAG,iKAAI,aAAU,EAAiB,CAAC;QAE1E;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,iKAAI,aAAU,EAAgB,CAAC;QAEjE;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAG,iKAAI,aAAU,EAAgB,CAAC;QAEhE;;WAEG,CACI,IAAA,CAAA,4BAA4B,GAAG,iKAAI,aAAU,EAAY,CAAC;QAEjE;;WAEG,CACI,IAAA,CAAA,2BAA2B,GAAG,IAAI,0KAAU,EAAY,CAAC;QAEhE;;WAEG,CACI,IAAA,CAAA,kCAAkC,GAAG,iKAAI,aAAU,EAAmB,CAAC;QAE9E;;WAEG,CACI,IAAA,CAAA,iCAAiC,GAAG,iKAAI,aAAU,EAAmB,CAAC;QAE7E;;WAEG,CACI,IAAA,CAAA,kCAAkC,GAAG,iKAAI,aAAU,EAAkB,CAAC;QAE7E;;WAEG,CACI,IAAA,CAAA,iCAAiC,GAAG,iKAAI,aAAU,EAAkB,CAAC;QAE5E;;WAEG,CACI,IAAA,CAAA,4BAA4B,GAAG,IAAI,0KAAU,EAAY,CAAC;QAEjE;;WAEG,CACI,IAAA,CAAA,iCAAiC,GAAG,iKAAI,aAAU,EAAiB,CAAC;QAE3E;;WAEG,CACI,IAAA,CAAA,2BAA2B,GAAG,iKAAI,aAAU,EAAY,CAAC;QAEhE;;WAEG,CACI,IAAA,CAAA,gCAAgC,GAAG,IAAI,0KAAU,EAAiB,CAAC;QAE1E;;WAEG,CACI,IAAA,CAAA,2BAA2B,GAAG,iKAAI,aAAU,EAAe,CAAC;QAEnE;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,iKAAI,aAAU,EAAe,CAAC;QAElE;;WAEG,CACI,IAAA,CAAA,8BAA8B,GAAG,iKAAI,aAAU,EAAc,CAAC;QAErE;;WAEG,CACI,IAAA,CAAA,6BAA6B,GAAG,gKAAI,cAAU,EAAc,CAAC;QAEpE;;WAEG,CACI,IAAA,CAAA,+BAA+B,GAAG,iKAAI,aAAU,EAAe,CAAC;QAEvE;;WAEG,CACI,IAAA,CAAA,8BAA8B,GAAG,gKAAI,cAAU,EAAe,CAAC;QAEtE;;WAEG,CACI,IAAA,CAAA,+BAA+B,GAAG,iKAAI,aAAU,EAAe,CAAC;QAEvE;;WAEG,CACI,IAAA,CAAA,8BAA8B,GAAG,iKAAI,aAAU,EAAe,CAAC;QAEtE;;;WAGG,CACI,IAAA,CAAA,qCAAqC,GAAG,iKAAI,aAAU,EAAS,CAAC;QAEvE;;;WAGG,CACI,IAAA,CAAA,oCAAoC,GAAG,iKAAI,aAAU,EAAS,CAAC;QAEtE;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,iKAAI,aAAU,EAAS,CAAC;QAExD;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAG,IAAI,0KAAU,EAAS,CAAC;QAEvD;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAG,iKAAI,aAAU,EAAS,CAAC;QAEvD;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,IAAI,0KAAU,EAAS,CAAC;QAExD;;;;WAIG,CACI,IAAA,CAAA,gCAAgC,GAAG,iKAAI,aAAU,EAAsB,CAAC;QAE/E;;;;WAIG,CACI,IAAA,CAAA,+BAA+B,GAAG,iKAAI,aAAU,EAAsB,CAAC;QAE9E;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,iKAAI,aAAU,EAAgB,CAAC;QAEjE;;WAEG,CACI,IAAA,CAAA,iCAAiC,GAAG,iKAAI,aAAU,EAAS,CAAC;QAEnE;;WAEG,CACI,IAAA,CAAA,qCAAqC,GAAG,iKAAI,aAAU,EAAyB,CAAC;QAEvF;;WAEG,CACI,IAAA,CAAA,mCAAmC,GAAG,iKAAI,aAAU,EAAuD,CAAC;QAQnH,aAAa;QAEb,cAAA,EAAgB,CACT,IAAA,CAAA,mCAAmC,GAAG,iKAAI,wBAAqB,CAAM,GAAG,CAAC,CAAC;QAEjF,WAAW;QACH,IAAA,CAAA,4BAA4B,GAAG,oLAAI,8BAA2B,EAAE,CAAC;QA6HzE;;;WAGG,CACI,IAAA,CAAA,sBAAsB,GAAG,gKAAI,cAAU,EAAkB,CAAC;QAEjE;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,iKAAI,aAAU,EAAe,CAAC;QAkG3D,WAAW;QAEX;;;WAGG,CACI,IAAA,CAAA,uBAAuB,GAAG,iKAAI,aAAU,EAAmB,CAAC;QAEnE;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,iKAAI,aAAU,EAAgB,CAAC;QAE7D,qBAAqB;QAEb,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAetC,yBAAyB;QACjB,IAAA,CAAA,gBAAgB,GAAW,CAAC,CAAC;QAC7B,IAAA,CAAA,cAAc,GAAW,CAAC,CAAC;QAC3B,IAAA,CAAA,oBAAoB,GAAW,CAAC,CAAC;QA6BzC,MAAM;QAEE,IAAA,CAAA,WAAW,GAAG,IAAI,CAAC;QAiBnB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC;QAsBtC;;;;WAIG,CACI,IAAA,CAAA,QAAQ,GAAG,oKAAI,UAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C;;;;WAIG,CACI,IAAA,CAAA,UAAU,GAAG,GAAG,CAAC;QACxB;;;;WAIG,CACI,IAAA,CAAA,QAAQ,GAAG,CAAC,CAAC;QACpB;;;;WAIG,CACI,IAAA,CAAA,MAAM,GAAG,MAAM,CAAC;QASvB;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QAE1C,SAAS;QACD,IAAA,CAAA,eAAe,GAAG,IAAI,CAAC;QAevB,IAAA,CAAA,cAAc,GAAG,IAAI,CAAC;QAiBtB,IAAA,CAAA,uBAAuB,GAAyB,IAAI,CAAC;QA2D7D,WAAW;QACH,IAAA,CAAA,gBAAgB,GAAG,IAAI,CAAC;QAgBxB,IAAA,CAAA,WAAW,GAAyB,IAAI,CAAC;QA0BjD;;WAEG,CACI,IAAA,CAAA,WAAW,GAAiB,EAAE,CAAC;QAEtC,UAAU;QACV;;WAEG,CACI,IAAA,CAAA,cAAc,GAAG,IAAI,CAAC;QAE7B,YAAY;QACZ;;WAEG,CACI,IAAA,CAAA,gBAAgB,GAAG,IAAI,CAAC;QAE/B,UAAU;QACV;;WAEG,CACI,IAAA,CAAA,cAAc,GAAG,IAAI,CAAC;QAE7B,YAAY;QACJ,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAgBjC,cAAc;QACd;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAEhC,aAAa;QACb;;;WAGG,CACI,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAchC;;;WAGG,CACI,IAAA,CAAA,OAAO,GAAG,IAAI,4KAAO,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAE3C,gBAAgB;QAChB;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAMnC,yBAAyB;QACzB;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QACnC;;;WAGG,CACI,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QACrC;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAA0B,EAAE,CAAC;QAQvD;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAa,EAAE,CAAC;QAE1C,SAAS;QACT;;WAEG,CACI,IAAA,CAAA,aAAa,GAAG,IAAI,CAAC;QAepB,IAAA,CAAA,uBAAuB,GAAG,iKAAI,wBAAqB,CAAe,GAAG,CAAC,CAAC;QAE/E,sBAAsB;QACtB;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,IAAI,CAAC;QAKxC,uBAAuB;QACf,IAAA,CAAA,cAAc,GAAG,kKAAI,cAAW,EAAE,CAAC;QAC3C,cAAA,EAAgB,CACT,IAAA,CAAA,cAAc,GAAG,kKAAI,cAAW,EAAE,CAAC;QAC1C,cAAA,EAAgB,CACT,IAAA,CAAA,gBAAgB,GAAG,kKAAI,cAAW,EAAE,CAAC;QAC5C,cAAA,EAAgB,CACT,IAAA,CAAA,YAAY,GAAG,kKAAI,cAAW,EAAE,CAAC;QAOxC,cAAA,EAAgB,CACT,IAAA,CAAA,cAAc,GAAW,CAAC,CAAC;QAElC;;;WAGG,CACI,IAAA,CAAA,kBAAkB,GAAW,CAAC,CAAC;QAS9B,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QACd,IAAA,CAAA,QAAQ,GAAG,CAAC,CAAC;QACb,IAAA,CAAA,0BAA0B,GAA4C,IAAI,CAAC;QACnF,cAAA,EAAgB,CACT,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC9B,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QAEnC,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC,CAAC;QACrB,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC,CAAC;QAEnC,cAAA,EAAgB,CACT,IAAA,CAAA,aAAa,GAAG,IAAI,KAAK,CAAwB,GAAG,CAAC,CAAC;QACrD,IAAA,CAAA,eAAe,GAAG,IAAI,KAAK,EAAgB,CAAC;QAEpD,cAAA,EAAgB,CACT,IAAA,CAAA,YAAY,GAAG,EAAW,CAAC;QAC1B,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QAE5B;;;WAGG,CACI,IAAA,CAAA,kCAAkC,GAAY,KAAK,CAAC;QACnD,IAAA,CAAA,aAAa,GAAG,iKAAI,aAAU,CAAe,GAAG,CAAC,CAAC;QAClD,IAAA,CAAA,mBAAmB,GAAG,iKAAI,aAAU,CAAW,GAAG,CAAC,CAAC;QACpD,IAAA,CAAA,cAAc,GAAG,iKAAI,wBAAqB,CAAsB,GAAG,CAAC,CAAC;QACrE,IAAA,CAAA,uBAAuB,GAAG,iKAAI,wBAAqB,CAAsB,GAAG,CAAC,CAAC;QACtF,cAAA,EAAgB,CACT,IAAA,CAAA,sBAAsB,GAAG,iKAAI,aAAU,CAAkB,GAAG,CAAC,CAAC;QAC7D,IAAA,CAAA,gBAAgB,GAAG,IAAI,qLAAqB,CAAW,EAAE,CAAC,CAAC;QAC3D,IAAA,CAAA,sBAAsB,GAAG,iKAAI,wBAAqB,CAAO,EAAE,CAAC,CAAC;QAWrE,cAAA,EAAgB,CACT,IAAA,CAAA,kBAAkB,GAAG,IAAI,KAAK,EAAc,CAAC;QAE5C,IAAA,CAAA,gBAAgB,GAAG,2KAAM,CAAC,IAAI,EAAE,CAAC;QAmBzC;;;WAGG,CACI,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAUnC;;;WAGG,CACI,IAAA,CAAA,WAAW,GAAsB,EAAE,CAAC;QAE3C;;;WAGG,CACI,IAAA,CAAA,uBAAuB,GAAkC,EAAE,CAAC;QAEnE;;WAEG,CACK,IAAA,CAAA,oBAAoB,GAAsB,EAAE,CAAC;QA+CrD;;;WAGG,CACI,IAAA,CAAA,wBAAwB,4JAAG,QAAK,CAAC,MAAM,EAAqB,CAAC;QACpE;;;WAGG,CACI,IAAA,CAAA,iBAAiB,4JAAG,QAAK,CAAC,MAAM,EAAqB,CAAC;QAC7D;;;WAGG,CACI,IAAA,CAAA,6BAA6B,4JAAG,QAAK,CAAC,MAAM,EAA2B,CAAC;QAC/E;;;WAGG,CACI,IAAA,CAAA,yBAAyB,4JAAG,QAAK,CAAC,MAAM,EAA4B,CAAC;QAC5E;;;WAGG,CACI,IAAA,CAAA,qCAAqC,4JAAG,QAAK,CAAC,MAAM,EAA4B,CAAC;QACxF;;;WAGG,CACI,IAAA,CAAA,oBAAoB,4JAAG,QAAK,CAAC,MAAM,EAAmB,CAAC;QAC9D;;;WAGG,CACI,IAAA,CAAA,8BAA8B,4JAAG,QAAK,CAAC,MAAM,EAAqB,CAAC;QAC1E;;;WAGG,CACI,IAAA,CAAA,qBAAqB,4JAAG,QAAK,CAAC,MAAM,EAA8B,CAAC;QAC1E;;;WAGG,CACI,IAAA,CAAA,mBAAmB,4JAAG,QAAK,CAAC,MAAM,EAA4B,CAAC;QACtE;;;WAGG,CACI,IAAA,CAAA,4BAA4B,4JAAG,QAAK,CAAC,MAAM,EAAgC,CAAC;QACnF;;;WAGG,CACI,IAAA,CAAA,sBAAsB,4JAAG,QAAK,CAAC,MAAM,EAAqB,CAAC;QAClE;;;WAGG,CACI,IAAA,CAAA,4BAA4B,4JAAG,QAAK,CAAC,MAAM,EAA2B,CAAC;QAC9E;;;WAGG,CACI,IAAA,CAAA,8BAA8B,GAAG,iKAAK,CAAC,MAAM,EAA6B,CAAC;QAClF;;;WAGG,CACI,IAAA,CAAA,yBAAyB,4JAAG,QAAK,CAAC,MAAM,EAA4B,CAAC;QAC5E;;;WAGG,CACI,IAAA,CAAA,wBAAwB,4JAAG,QAAK,CAAC,MAAM,EAA4B,CAAC;QAC3E;;;WAGG,CACI,IAAA,CAAA,6BAA6B,4JAAG,QAAK,CAAC,MAAM,EAA6B,CAAC;QACjF;;;WAGG,CACI,IAAA,CAAA,qBAAqB,4JAAG,QAAK,CAAC,MAAM,EAAqB,CAAC;QACjE;;;WAGG,CACI,IAAA,CAAA,4BAA4B,4JAAG,QAAK,CAAC,MAAM,EAAqB,CAAC;QACxE;;;WAGG,CACI,IAAA,CAAA,2BAA2B,4JAAG,QAAK,CAAC,MAAM,EAA2B,CAAC;QAC7E;;WAEG,CACI,IAAA,CAAA,kCAAkC,4JAAG,QAAK,CAAC,MAAM,EAA2B,CAAC;QACpF;;;WAGG,CACI,IAAA,CAAA,iBAAiB,4JAAG,QAAK,CAAC,MAAM,EAAqB,CAAC;QAC7D;;;WAGG,CACI,IAAA,CAAA,iBAAiB,4JAAG,QAAK,CAAC,MAAM,EAA0B,CAAC;QAClE;;;WAGG,CACI,IAAA,CAAA,iBAAiB,4JAAG,QAAK,CAAC,MAAM,EAA4B,CAAC;QACpE;;;WAGG,CACI,IAAA,CAAA,eAAe,4JAAG,QAAK,CAAC,MAAM,EAA4B,CAAC;QAElE;;WAEG,CACK,IAAA,CAAA,qBAAqB,GAAyD,IAAI,CAAC;QAEnF,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QA6Ed,IAAA,CAAA,sBAAsB,GAAkC;YAC5D,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,CAAC;SACZ,CAAC;QAWM,IAAA,CAAA,yBAAyB,GAA6B;YAC1D,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,CAAC;SACZ,CAAC;QAuqEM,IAAA,CAAA,0CAA0C,GAAG,KAAK,CAAC;QA4F3D,cAAA,EAAgB,CACT,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QACnC,cAAA,EAAgB,CACT,IAAA,CAAA,kCAAkC,GAAG,KAAK,CAAC;QAC1C,IAAA,CAAA,mCAAmC,GAAG,KAAK,CAAC;QA+RpD,cAAA,EAAgB,CACT,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QA2CtC,cAAA,EAAgB,CACT,IAAA,CAAA,2BAA2B,GAAG,IAAI,CAAC;QA6O1C;;;WAGG,CACI,IAAA,CAAA,yBAAyB,GAAiB,GAAG,EAAE;YAClD,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACtC,CAAC,CAAC;QA8uBF,cAAA,EAAgB,CACT,IAAA,CAAA,kBAAkB,GAAW,CAAC,CAAC;QA0P9B,IAAA,CAAA,4BAA4B,GAAG,KAAK,CAAC;QA+L7C;;;WAGG,CACO,IAAA,CAAA,cAAc,GAAyC,IAAI,CAAC;QAjkIlE,IAAI,CAAC,aAAa,GAAG,EAAc,CAAC;QAEpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEpC,MAAM,WAAW,GAAG;YAChB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;YACtB,OAAO,EAAE,KAAK;YACd,GAAG,OAAO;SACb,CAAC;QAEF,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,MAAM,qKAAI,cAAW,CAAC,iBAAiB,CAAC;QAChE,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YACtB,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,MAAM,CAAC;6KACJ,cAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;YACrC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,IAAI,CAAC,iBAAiB,GAAG,4KAAI,mBAAgB,CAAC,IAAI,CAAC,CAAC;QAEpD,kLAAI,qBAAkB,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,GAAG,kLAAI,qBAAkB,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC;QAED,wKAAI,sBAAA,AAAmB,EAAE,GAAE,CAAC;YACxB,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,sCAAsC;QACtC,wLAAI,+BAA4B,EAAE,CAAC;YAC/B,IAAI,CAAC,6BAA6B,GAAG,wLAAI,+BAA4B,EAAE,CAAC;QAC5E,CAAC;QAED,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAEpC,IAAI,WAAW,CAAC,uBAAuB,EAAE,CAAC;YACtC,IAAI,CAAC,qBAAqB,GAAG,CAAA,CAAE,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;QACzD,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;QAErD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC/B,MAAM,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;;AAl0DD,2BAAA,EAA6B,CACN,MAAA,YAAY,GAAG,SAAS,CAAC,YAAY,AAAzB,CAA0B;AAC7D,yDAAA,EAA2D,CACpC,MAAA,WAAW,GAAG,SAAS,CAAC,WAAW,AAAxB,CAAyB;AAC3D,iFAAA,EAAmF,CAC5D,MAAA,YAAY,GAAG,SAAS,CAAC,YAAY,AAAzB,CAA0B;AAC7D,oDAAA,EAAsD,CAC/B,MAAA,cAAc,GAAG,SAAS,CAAC,cAAc,AAA3B,CAA4B;AAEjE;;;GAGG,CACW,MAAA,YAAY,GAAG,GAAG,AAAN,CAAO;AACjC;;;GAGG,CACW,MAAA,YAAY,GAAG,MAAM,AAAT,CAAU;AAYZ,MAAA,+BAA+B,GAAG,KAAK,CAAC,sBAAT,CAAgC;AAmhM3F,sBAAsB;gKACtB,gBAAA,AAAa,EAAC,eAAe,EAAE,KAAK,CAAC,CAAC", "debugId": null}}]}