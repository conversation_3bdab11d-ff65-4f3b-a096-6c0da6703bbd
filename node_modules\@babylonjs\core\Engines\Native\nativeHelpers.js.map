{"version": 3, "file": "nativeHelpers.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Native/nativeHelpers.ts"], "names": [], "mappings": "AAAA,yDAAyD;AACzD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,4BAAwB;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,EAAE,YAAY,EAAE,gCAA4B;AAInD,MAAM,UAAU,sBAAsB,CAAC,MAAc,EAAE,IAAY;IAC/D,QAAQ,MAAM,EAAE,CAAC;QACb,0BAA0B;QAC1B,KAAK,SAAS,CAAC,qBAAqB;YAChC,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,qBAAqB;YAChC,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,8BAA8B;YACzC,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAC/C,KAAK,SAAS,CAAC,2BAA2B;YACtC,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;QAE9C,+BAA+B;QAC/B,KAAK,SAAS,CAAC,wCAAwC;YACnD,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,8CAA8C;YACzD,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;QAC9C,KAAK,SAAS,CAAC,uCAAuC;YAClD,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,uCAAuC;YAClD,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,uCAAuC;YAClD,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,sCAAsC;YACjD,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,sCAAsC;YACjD,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,uCAAuC;YAClD,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;QAC9C,KAAK,SAAS,CAAC,kCAAkC;YAC7C,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;QAC9C,KAAK,SAAS,CAAC,uCAAuC;YAClD,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAE/C,KAAK,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAC/B,QAAQ,IAAI,EAAE,CAAC;gBACX,KAAK,SAAS,CAAC,yBAAyB;oBACpC,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBAC9C,KAAK,SAAS,CAAC,gBAAgB;oBAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,SAAS,CAAC,eAAe;oBAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,SAAS,CAAC,4BAA4B;oBACvC,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;YACnD,CAAC;YACD,MAAM;QACV,CAAC;QACD,KAAK,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAChC,QAAQ,IAAI,EAAE,CAAC;gBACX,KAAK,SAAS,CAAC,yBAAyB;oBACpC,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,SAAS,CAAC,iBAAiB;oBAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;gBACjD,KAAK,SAAS,CAAC,sBAAsB;oBACjC,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;gBACjD,KAAK,SAAS,CAAC,gBAAgB;oBAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC;gBAChD,KAAK,SAAS,CAAC,iBAAiB;oBAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;gBACjD,KAAK,SAAS,CAAC,0BAA0B;oBACrC,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;gBACjD,KAAK,SAAS,CAAC,eAAe;oBAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;gBACjD,KAAK,SAAS,CAAC,4BAA4B;oBACvC,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;YACrD,CAAC;YACD,MAAM;QACV,CAAC;QACD,KAAK,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC;YAC7B,QAAQ,IAAI,EAAE,CAAC;gBACX,KAAK,SAAS,CAAC,yBAAyB;oBACpC,OAAO,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBAC5C,KAAK,SAAS,CAAC,iBAAiB;oBAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBAC9C,KAAK,SAAS,CAAC,sBAAsB;oBACjC,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBAC9C,KAAK,SAAS,CAAC,gBAAgB;oBAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBAC7C,KAAK,SAAS,CAAC,iBAAiB;oBAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBAC9C,KAAK,SAAS,CAAC,0BAA0B;oBACrC,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBAC9C,KAAK,SAAS,CAAC,eAAe;oBAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBAC9C,KAAK,SAAS,CAAC,4BAA4B;oBACvC,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAClD,CAAC;YACD,MAAM;QACV,CAAC;QACD,KAAK,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC9B,QAAQ,IAAI,EAAE,CAAC;gBACX,KAAK,SAAS,CAAC,yBAAyB;oBACpC,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;gBAC7C,KAAK,SAAS,CAAC,iBAAiB;oBAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,SAAS,CAAC,sBAAsB;oBACjC,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,SAAS,CAAC,gBAAgB;oBAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;gBAC9C,KAAK,SAAS,CAAC,iBAAiB;oBAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,SAAS,CAAC,0BAA0B;oBACrC,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,SAAS,CAAC,eAAe;oBAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,SAAS,CAAC,4BAA4B;oBACvC,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;YACnD,CAAC;YACD,MAAM;QACV,CAAC;QACD,KAAK,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAChC,QAAQ,IAAI,EAAE,CAAC;gBACX,KAAK,SAAS,CAAC,yBAAyB;oBACpC,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;YACnD,CAAC;YACD,MAAM;QACV,CAAC;IACL,CAAC;IAED,MAAM,IAAI,YAAY,CAAC,8CAA8C,MAAM,UAAU,IAAI,GAAG,EAAE,UAAU,CAAC,uBAAuB,CAAC,CAAC;AACtI,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,YAAoB;IACtD,QAAQ,YAAY,EAAE,CAAC;QACnB,KAAK,SAAS,CAAC,uBAAuB;YAClC,OAAO,OAAO,CAAC,MAAM,CAAC,uBAAuB,CAAC;QAClD,KAAK,SAAS,CAAC,qBAAqB;YAChC,OAAO,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC;QAChD,KAAK,SAAS,CAAC,+BAA+B;YAC1C,OAAO,OAAO,CAAC,MAAM,CAAC,+BAA+B,CAAC;QAC1D,KAAK,SAAS,CAAC,kCAAkC;YAC7C,OAAO,OAAO,CAAC,MAAM,CAAC,kCAAkC,CAAC;QAC7D,KAAK,SAAS,CAAC,iCAAiC;YAC5C,OAAO,OAAO,CAAC,MAAM,CAAC,iCAAiC,CAAC;QAC5D,KAAK,SAAS,CAAC,gCAAgC;YAC3C,OAAO,OAAO,CAAC,MAAM,CAAC,gCAAgC,CAAC;QAC3D,KAAK,SAAS,CAAC,sBAAsB;YACjC,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,iCAAiC;YAC5C,OAAO,OAAO,CAAC,MAAM,CAAC,iCAAiC,CAAC;QAC5D,KAAK,SAAS,CAAC,iCAAiC;YAC5C,OAAO,OAAO,CAAC,MAAM,CAAC,iCAAiC,CAAC;QAC5D,KAAK,SAAS,CAAC,gCAAgC;YAC3C,OAAO,OAAO,CAAC,MAAM,CAAC,gCAAgC,CAAC;QAC3D,KAAK,SAAS,CAAC,gCAAgC;YAC3C,OAAO,OAAO,CAAC,MAAM,CAAC,gCAAgC,CAAC;QAC3D,KAAK,SAAS,CAAC,sBAAsB;YACjC,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD;YACI,MAAM,IAAI,KAAK,CAAC,8BAA8B,YAAY,GAAG,CAAC,CAAC;IACvE,CAAC;AACL,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,QAAgB;IACjD,QAAQ,QAAQ,EAAE,CAAC;QACf,KAAK,SAAS,CAAC,wBAAwB;YACnC,OAAO,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAC5C,KAAK,SAAS,CAAC,yBAAyB;YACpC,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,0BAA0B;YACrC,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;QAC9C;YACI,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC;IACnE,CAAC;AACL,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,IAAY;IAC7C,QAAQ,IAAI,EAAE,CAAC;QACX,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAC5C,KAAK,SAAS,CAAC,MAAM;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;QAC9C,KAAK,SAAS,CAAC,KAAK;YAChB,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,MAAM;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;QAC9C,KAAK,SAAS,CAAC,OAAO;YAClB,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAC/C,KAAK,SAAS,CAAC,QAAQ;YACnB,OAAO,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC;QAChD,KAAK,SAAS,CAAC,KAAK;YAChB,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,SAAS,CAAC,MAAM;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;QAC9C;YACI,MAAM,IAAI,KAAK,CAAC,kCAAkC,IAAI,GAAG,CAAC,CAAC;IACnE,CAAC;AACL,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAC,MAAc;IACjD,QAAQ,MAAM,EAAE,CAAC;QACb,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,OAAO;YAClB,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpD,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,MAAM;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC;QACnD,KAAK,SAAS,CAAC,SAAS;YACpB,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpD,KAAK,SAAS,CAAC,SAAS;YACpB,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpD;YACI,MAAM,IAAI,KAAK,CAAC,oCAAoC,MAAM,GAAG,CAAC,CAAC;IACvE,CAAC;AACL,CAAC;AAED,MAAM,UAAU,yBAAyB,CAAC,SAAiB;IACvD,QAAQ,SAAS,EAAE,CAAC;QAChB,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,OAAO;YAClB,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpD,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,MAAM;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC;QACnD,KAAK,SAAS,CAAC,SAAS;YACpB,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpD,KAAK,SAAS,CAAC,SAAS;YACpB,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpD;YACI,MAAM,IAAI,KAAK,CAAC,uCAAuC,SAAS,GAAG,CAAC,CAAC;IAC7E,CAAC;AACL,CAAC;AAED,MAAM,UAAU,yBAAyB,CAAC,MAAc;IACpD,QAAQ,MAAM,EAAE,CAAC;QACb,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,OAAO;YAClB,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpD,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,IAAI;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC;QACjD,KAAK,SAAS,CAAC,MAAM;YACjB,OAAO,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC;QACnD,KAAK,SAAS,CAAC,SAAS;YACpB,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpD,KAAK,SAAS,CAAC,SAAS;YACpB,OAAO,OAAO,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACpD;YACI,MAAM,IAAI,KAAK,CAAC,oCAAoC,MAAM,GAAG,CAAC,CAAC;IACvE,CAAC;AACL,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,IAAY;IAC3C,QAAQ,IAAI,EAAE,CAAC;QACX,KAAK,SAAS,CAAC,aAAa;YACxB,OAAO,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC;QACxC,KAAK,SAAS,CAAC,SAAS;YACpB,OAAO,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC;QACpC,KAAK,SAAS,CAAC,aAAa;YACxB,OAAO,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC;QACxC,KAAK,SAAS,CAAC,cAAc;YACzB,OAAO,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC;QACzC,KAAK,SAAS,CAAC,cAAc;YACzB,OAAO,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC;QACzC,KAAK,SAAS,CAAC,eAAe;YAC1B,OAAO,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC;QAC1C,KAAK,SAAS,CAAC,YAAY;YACvB,OAAO,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;QACvC,KAAK,SAAS,CAAC,mBAAmB;YAC9B,OAAO,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAAC;QAC9C,KAAK,SAAS,CAAC,8BAA8B;YACzC,OAAO,OAAO,CAAC,MAAM,CAAC,8BAA8B,CAAC;QACzD,KAAK,SAAS,CAAC,iBAAiB;YAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAC5C,KAAK,SAAS,CAAC,gBAAgB;YAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC3C;YACI,MAAM,IAAI,KAAK,CAAC,2BAA2B,IAAI,GAAG,CAAC,CAAC;IAC5D,CAAC;AACL,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,IAAY;IAC5C,QAAQ,IAAI,EAAE,CAAC;QACX,KAAK,YAAY,CAAC,IAAI;YAClB,OAAO,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC3C,KAAK,YAAY,CAAC,aAAa;YAC3B,OAAO,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAC5C,KAAK,YAAY,CAAC,KAAK;YACnB,OAAO,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAC5C,KAAK,YAAY,CAAC,cAAc;YAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC7C,KAAK,YAAY,CAAC,KAAK;YACnB,OAAO,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC;QAC5C;YACI,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,GAAG,CAAC,CAAC;IAChE,CAAC;AACL,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { ErrorCodes, RuntimeError } from \"core/Misc/error\";\r\nimport { Constants } from \"../constants\";\r\nimport type { INative } from \"./nativeInterfaces\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\n\r\ndeclare const _native: INative;\r\n\r\nexport function getNativeTextureFormat(format: number, type: number): number {\r\n    switch (format) {\r\n        // Depth (type is ignored)\r\n        case Constants.TEXTUREFORMAT_DEPTH16:\r\n            return _native.Engine.TEXTURE_FORMAT_D16;\r\n        case Constants.TEXTUREFORMAT_DEPTH24:\r\n            return _native.Engine.TEXTURE_FORMAT_D24;\r\n        case Constants.TEXTUREFORMAT_DEPTH24_STENCIL8:\r\n            return _native.Engine.TEXTURE_FORMAT_D24S8;\r\n        case Constants.TEXTUREFORMAT_DEPTH32_FLOAT:\r\n            return _native.Engine.TEXTURE_FORMAT_D32F;\r\n\r\n        // Compressed (type is ignored)\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_BPTC_UNORM:\r\n            return _native.Engine.TEXTURE_FORMAT_BC7;\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_SIGNED_FLOAT:\r\n            return _native.Engine.TEXTURE_FORMAT_BC6H;\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT5:\r\n            return _native.Engine.TEXTURE_FORMAT_BC3;\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT3:\r\n            return _native.Engine.TEXTURE_FORMAT_BC2;\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT1:\r\n            return _native.Engine.TEXTURE_FORMAT_BC1;\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGB_S3TC_DXT1:\r\n            return _native.Engine.TEXTURE_FORMAT_BC1;\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_ASTC_4x4:\r\n            return _native.Engine.TEXTURE_FORMAT_ASTC4x4;\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGB_ETC1_WEBGL:\r\n            return _native.Engine.TEXTURE_FORMAT_ETC1;\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGB8_ETC2:\r\n            return _native.Engine.TEXTURE_FORMAT_ETC2;\r\n        case Constants.TEXTUREFORMAT_COMPRESSED_RGBA8_ETC2_EAC:\r\n            return _native.Engine.TEXTURE_FORMAT_ETC2A;\r\n\r\n        case Constants.TEXTUREFORMAT_RGB: {\r\n            switch (type) {\r\n                case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGB8;\r\n                case Constants.TEXTURETYPE_BYTE:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGB8S;\r\n                case Constants.TEXTURETYPE_INT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGB8I;\r\n                case Constants.TEXTURETYPE_UNSIGNED_INTEGER:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGB8U;\r\n            }\r\n            break;\r\n        }\r\n        case Constants.TEXTUREFORMAT_RGBA: {\r\n            switch (type) {\r\n                case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGBA8;\r\n                case Constants.TEXTURETYPE_FLOAT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGBA32F;\r\n                case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGBA16F;\r\n                case Constants.TEXTURETYPE_BYTE:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGBA8S;\r\n                case Constants.TEXTURETYPE_SHORT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGBA16I;\r\n                case Constants.TEXTURETYPE_UNSIGNED_SHORT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGBA16U;\r\n                case Constants.TEXTURETYPE_INT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGBA32I;\r\n                case Constants.TEXTURETYPE_UNSIGNED_INTEGER:\r\n                    return _native.Engine.TEXTURE_FORMAT_RGBA32U;\r\n            }\r\n            break;\r\n        }\r\n        case Constants.TEXTUREFORMAT_R: {\r\n            switch (type) {\r\n                case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                    return _native.Engine.TEXTURE_FORMAT_R8;\r\n                case Constants.TEXTURETYPE_FLOAT:\r\n                    return _native.Engine.TEXTURE_FORMAT_R32F;\r\n                case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                    return _native.Engine.TEXTURE_FORMAT_R16F;\r\n                case Constants.TEXTURETYPE_BYTE:\r\n                    return _native.Engine.TEXTURE_FORMAT_R8S;\r\n                case Constants.TEXTURETYPE_SHORT:\r\n                    return _native.Engine.TEXTURE_FORMAT_R16S;\r\n                case Constants.TEXTURETYPE_UNSIGNED_SHORT:\r\n                    return _native.Engine.TEXTURE_FORMAT_R16U;\r\n                case Constants.TEXTURETYPE_INT:\r\n                    return _native.Engine.TEXTURE_FORMAT_R32I;\r\n                case Constants.TEXTURETYPE_UNSIGNED_INTEGER:\r\n                    return _native.Engine.TEXTURE_FORMAT_R32U;\r\n            }\r\n            break;\r\n        }\r\n        case Constants.TEXTUREFORMAT_RG: {\r\n            switch (type) {\r\n                case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                    return _native.Engine.TEXTURE_FORMAT_RG8;\r\n                case Constants.TEXTURETYPE_FLOAT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RG32F;\r\n                case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RG16F;\r\n                case Constants.TEXTURETYPE_BYTE:\r\n                    return _native.Engine.TEXTURE_FORMAT_RG8S;\r\n                case Constants.TEXTURETYPE_SHORT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RG16S;\r\n                case Constants.TEXTURETYPE_UNSIGNED_SHORT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RG16U;\r\n                case Constants.TEXTURETYPE_INT:\r\n                    return _native.Engine.TEXTURE_FORMAT_RG32I;\r\n                case Constants.TEXTURETYPE_UNSIGNED_INTEGER:\r\n                    return _native.Engine.TEXTURE_FORMAT_RG32U;\r\n            }\r\n            break;\r\n        }\r\n        case Constants.TEXTUREFORMAT_BGRA: {\r\n            switch (type) {\r\n                case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                    return _native.Engine.TEXTURE_FORMAT_BGRA8;\r\n            }\r\n            break;\r\n        }\r\n    }\r\n\r\n    throw new RuntimeError(`Unsupported texture format or type: format ${format}, type ${type}.`, ErrorCodes.UnsupportedTextureError);\r\n}\r\n\r\nexport function getNativeSamplingMode(samplingMode: number): number {\r\n    switch (samplingMode) {\r\n        case Constants.TEXTURE_NEAREST_NEAREST:\r\n            return _native.Engine.TEXTURE_NEAREST_NEAREST;\r\n        case Constants.TEXTURE_LINEAR_LINEAR:\r\n            return _native.Engine.TEXTURE_LINEAR_LINEAR;\r\n        case Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR:\r\n            return _native.Engine.TEXTURE_LINEAR_LINEAR_MIPLINEAR;\r\n        case Constants.TEXTURE_NEAREST_NEAREST_MIPNEAREST:\r\n            return _native.Engine.TEXTURE_NEAREST_NEAREST_MIPNEAREST;\r\n        case Constants.TEXTURE_NEAREST_LINEAR_MIPNEAREST:\r\n            return _native.Engine.TEXTURE_NEAREST_LINEAR_MIPNEAREST;\r\n        case Constants.TEXTURE_NEAREST_LINEAR_MIPLINEAR:\r\n            return _native.Engine.TEXTURE_NEAREST_LINEAR_MIPLINEAR;\r\n        case Constants.TEXTURE_NEAREST_LINEAR:\r\n            return _native.Engine.TEXTURE_NEAREST_LINEAR;\r\n        case Constants.TEXTURE_NEAREST_NEAREST_MIPLINEAR:\r\n            return _native.Engine.TEXTURE_NEAREST_NEAREST_MIPLINEAR;\r\n        case Constants.TEXTURE_LINEAR_NEAREST_MIPNEAREST:\r\n            return _native.Engine.TEXTURE_LINEAR_NEAREST_MIPNEAREST;\r\n        case Constants.TEXTURE_LINEAR_NEAREST_MIPLINEAR:\r\n            return _native.Engine.TEXTURE_LINEAR_NEAREST_MIPLINEAR;\r\n        case Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST:\r\n            return _native.Engine.TEXTURE_LINEAR_LINEAR_MIPNEAREST;\r\n        case Constants.TEXTURE_LINEAR_NEAREST:\r\n            return _native.Engine.TEXTURE_LINEAR_NEAREST;\r\n        default:\r\n            throw new Error(`Unsupported sampling mode: ${samplingMode}.`);\r\n    }\r\n}\r\n\r\nexport function getNativeAddressMode(wrapMode: number): number {\r\n    switch (wrapMode) {\r\n        case Constants.TEXTURE_WRAP_ADDRESSMODE:\r\n            return _native.Engine.ADDRESS_MODE_WRAP;\r\n        case Constants.TEXTURE_CLAMP_ADDRESSMODE:\r\n            return _native.Engine.ADDRESS_MODE_CLAMP;\r\n        case Constants.TEXTURE_MIRROR_ADDRESSMODE:\r\n            return _native.Engine.ADDRESS_MODE_MIRROR;\r\n        default:\r\n            throw new Error(\"Unexpected wrap mode: \" + wrapMode + \".\");\r\n    }\r\n}\r\n\r\nexport function getNativeStencilFunc(func: number): number {\r\n    switch (func) {\r\n        case Constants.LESS:\r\n            return _native.Engine.STENCIL_TEST_LESS;\r\n        case Constants.LEQUAL:\r\n            return _native.Engine.STENCIL_TEST_LEQUAL;\r\n        case Constants.EQUAL:\r\n            return _native.Engine.STENCIL_TEST_EQUAL;\r\n        case Constants.GEQUAL:\r\n            return _native.Engine.STENCIL_TEST_GEQUAL;\r\n        case Constants.GREATER:\r\n            return _native.Engine.STENCIL_TEST_GREATER;\r\n        case Constants.NOTEQUAL:\r\n            return _native.Engine.STENCIL_TEST_NOTEQUAL;\r\n        case Constants.NEVER:\r\n            return _native.Engine.STENCIL_TEST_NEVER;\r\n        case Constants.ALWAYS:\r\n            return _native.Engine.STENCIL_TEST_ALWAYS;\r\n        default:\r\n            throw new Error(`Unsupported stencil func mode: ${func}.`);\r\n    }\r\n}\r\n\r\nexport function getNativeStencilOpFail(opFail: number): number {\r\n    switch (opFail) {\r\n        case Constants.KEEP:\r\n            return _native.Engine.STENCIL_OP_FAIL_S_KEEP;\r\n        case Constants.ZERO:\r\n            return _native.Engine.STENCIL_OP_FAIL_S_ZERO;\r\n        case Constants.REPLACE:\r\n            return _native.Engine.STENCIL_OP_FAIL_S_REPLACE;\r\n        case Constants.INCR:\r\n            return _native.Engine.STENCIL_OP_FAIL_S_INCR;\r\n        case Constants.DECR:\r\n            return _native.Engine.STENCIL_OP_FAIL_S_DECR;\r\n        case Constants.INVERT:\r\n            return _native.Engine.STENCIL_OP_FAIL_S_INVERT;\r\n        case Constants.INCR_WRAP:\r\n            return _native.Engine.STENCIL_OP_FAIL_S_INCRSAT;\r\n        case Constants.DECR_WRAP:\r\n            return _native.Engine.STENCIL_OP_FAIL_S_DECRSAT;\r\n        default:\r\n            throw new Error(`Unsupported stencil OpFail mode: ${opFail}.`);\r\n    }\r\n}\r\n\r\nexport function getNativeStencilDepthFail(depthFail: number): number {\r\n    switch (depthFail) {\r\n        case Constants.KEEP:\r\n            return _native.Engine.STENCIL_OP_FAIL_Z_KEEP;\r\n        case Constants.ZERO:\r\n            return _native.Engine.STENCIL_OP_FAIL_Z_ZERO;\r\n        case Constants.REPLACE:\r\n            return _native.Engine.STENCIL_OP_FAIL_Z_REPLACE;\r\n        case Constants.INCR:\r\n            return _native.Engine.STENCIL_OP_FAIL_Z_INCR;\r\n        case Constants.DECR:\r\n            return _native.Engine.STENCIL_OP_FAIL_Z_DECR;\r\n        case Constants.INVERT:\r\n            return _native.Engine.STENCIL_OP_FAIL_Z_INVERT;\r\n        case Constants.INCR_WRAP:\r\n            return _native.Engine.STENCIL_OP_FAIL_Z_INCRSAT;\r\n        case Constants.DECR_WRAP:\r\n            return _native.Engine.STENCIL_OP_FAIL_Z_DECRSAT;\r\n        default:\r\n            throw new Error(`Unsupported stencil depthFail mode: ${depthFail}.`);\r\n    }\r\n}\r\n\r\nexport function getNativeStencilDepthPass(opPass: number): number {\r\n    switch (opPass) {\r\n        case Constants.KEEP:\r\n            return _native.Engine.STENCIL_OP_PASS_Z_KEEP;\r\n        case Constants.ZERO:\r\n            return _native.Engine.STENCIL_OP_PASS_Z_ZERO;\r\n        case Constants.REPLACE:\r\n            return _native.Engine.STENCIL_OP_PASS_Z_REPLACE;\r\n        case Constants.INCR:\r\n            return _native.Engine.STENCIL_OP_PASS_Z_INCR;\r\n        case Constants.DECR:\r\n            return _native.Engine.STENCIL_OP_PASS_Z_DECR;\r\n        case Constants.INVERT:\r\n            return _native.Engine.STENCIL_OP_PASS_Z_INVERT;\r\n        case Constants.INCR_WRAP:\r\n            return _native.Engine.STENCIL_OP_PASS_Z_INCRSAT;\r\n        case Constants.DECR_WRAP:\r\n            return _native.Engine.STENCIL_OP_PASS_Z_DECRSAT;\r\n        default:\r\n            throw new Error(`Unsupported stencil opPass mode: ${opPass}.`);\r\n    }\r\n}\r\n\r\nexport function getNativeAlphaMode(mode: number): number {\r\n    switch (mode) {\r\n        case Constants.ALPHA_DISABLE:\r\n            return _native.Engine.ALPHA_DISABLE;\r\n        case Constants.ALPHA_ADD:\r\n            return _native.Engine.ALPHA_ADD;\r\n        case Constants.ALPHA_COMBINE:\r\n            return _native.Engine.ALPHA_COMBINE;\r\n        case Constants.ALPHA_SUBTRACT:\r\n            return _native.Engine.ALPHA_SUBTRACT;\r\n        case Constants.ALPHA_MULTIPLY:\r\n            return _native.Engine.ALPHA_MULTIPLY;\r\n        case Constants.ALPHA_MAXIMIZED:\r\n            return _native.Engine.ALPHA_MAXIMIZED;\r\n        case Constants.ALPHA_ONEONE:\r\n            return _native.Engine.ALPHA_ONEONE;\r\n        case Constants.ALPHA_PREMULTIPLIED:\r\n            return _native.Engine.ALPHA_PREMULTIPLIED;\r\n        case Constants.ALPHA_PREMULTIPLIED_PORTERDUFF:\r\n            return _native.Engine.ALPHA_PREMULTIPLIED_PORTERDUFF;\r\n        case Constants.ALPHA_INTERPOLATE:\r\n            return _native.Engine.ALPHA_INTERPOLATE;\r\n        case Constants.ALPHA_SCREENMODE:\r\n            return _native.Engine.ALPHA_SCREENMODE;\r\n        default:\r\n            throw new Error(`Unsupported alpha mode: ${mode}.`);\r\n    }\r\n}\r\n\r\nexport function getNativeAttribType(type: number): number {\r\n    switch (type) {\r\n        case VertexBuffer.BYTE:\r\n            return _native.Engine.ATTRIB_TYPE_INT8;\r\n        case VertexBuffer.UNSIGNED_BYTE:\r\n            return _native.Engine.ATTRIB_TYPE_UINT8;\r\n        case VertexBuffer.SHORT:\r\n            return _native.Engine.ATTRIB_TYPE_INT16;\r\n        case VertexBuffer.UNSIGNED_SHORT:\r\n            return _native.Engine.ATTRIB_TYPE_UINT16;\r\n        case VertexBuffer.FLOAT:\r\n            return _native.Engine.ATTRIB_TYPE_FLOAT;\r\n        default:\r\n            throw new Error(`Unsupported attribute type: ${type}.`);\r\n    }\r\n}\r\n"]}