import type { IShaderProcessor } from "./iShaderProcessor.js";
import type { Nullable } from "../../types.js";
/**
 * Function for custom code generation
 */
export type ShaderCustomProcessingFunction = (shaderType: string, code: string, defines?: string[]) => string;
/** @internal */
export interface _IShaderProcessingContext {
    vertexBufferKindToNumberOfComponents?: {
        [kind: string]: number;
    };
}
/** @internal */
export interface _IProcessingOptions {
    defines: string[];
    indexParameters: any;
    isFragment: boolean;
    shouldUseHighPrecisionShader: boolean;
    supportsUniformBuffers: boolean;
    shadersRepository: string;
    includesShadersStore: {
        [key: string]: string;
    };
    processor: Nullable<IShaderProcessor>;
    version: string;
    platformName: string;
    lookForClosingBracketForUniformBuffer?: boolean;
    processingContext: Nullable<_IShaderProcessingContext>;
    isNDCHalfZRange: boolean;
    useReverseDepthBuffer: boolean;
    processCodeAfterIncludes?: ShaderCustomProcessingFunction;
}
