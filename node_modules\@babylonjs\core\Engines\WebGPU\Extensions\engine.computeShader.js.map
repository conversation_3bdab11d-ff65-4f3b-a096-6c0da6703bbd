{"version": 3, "file": "engine.computeShader.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.computeShader.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,gCAAyB;AAE1C,OAAO,EAAE,aAAa,EAAE,MAAM,gCAAgC,CAAC;AAK/D,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAC/D,OAAO,EAAE,4BAA4B,EAAE,MAAM,iCAAiC,CAAC;AA4B/E,MAAM,qBAAqB,GAA6B,EAAE,CAAC;AAE3D,YAAY,CAAC,SAAS,CAAC,oBAAoB,GAAG;IAC1C,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACtE,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,QAAmE,EAAE,OAAsC;IAC9J,MAAM,OAAO,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,OAAO,CAAC;IAEzJ,MAAM,IAAI,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC;IAC7C,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC;QACrC,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,OAAO,CAAC,UAAU,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YACjD,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IACD,MAAM,MAAM,GAAG,IAAI,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAChE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IAE5C,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,4BAA4B,GAAG;IAClD,OAAO,IAAI,4BAA4B,CAAC,IAAI,CAAC,CAAC;AAClD,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,yBAAyB,GAAG;IAC/C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;QAEjD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,eAAe,GAAG,UACrC,MAAqB,EACrB,OAAwB,EACxB,QAA4B,EAC5B,CAAS,EACT,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC,EACL,eAAuC,EACvC,cAAkC;IAElC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;AACrH,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAC7C,MAAqB,EACrB,OAAwB,EACxB,QAA4B,EAC5B,MAAkB,EAClB,SAAiB,CAAC,EAClB,eAAuC,EACvC,cAAkC;IAElC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;AACvI,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,gBAAgB,GAAG,UACtC,MAAqB,EACrB,OAAwB,EACxB,QAA4B,EAC5B,CAAU,EACV,CAAU,EACV,CAAU,EACV,MAAmB,EACnB,MAAe,EACf,eAAuC,EACvC,cAAkC;IAElC,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAE7B,MAAM,eAAe,GAAG,MAAM,CAAC,gBAAgD,CAAC;IAChF,MAAM,cAAc,GAAG,OAA+B,CAAC;IAEvD,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC;QACnC,eAAe,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;YACjE,MAAM,kDAAqC;YAC3C,OAAO,EAAE,eAAe,CAAC,KAAM;SAClC,CAAC,CAAC;IACP,CAAC;IAED,IAAI,cAAc,EAAE,CAAC;QACjB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAChF,CAAC;IAED,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;IAEhF,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;IAEzD,MAAM,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC,QAAQ,EAAE,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;IAC5G,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS;QACb,CAAC;QACD,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACvB,WAAW,CAAC,0BAA0B,CAAC,MAAM,CAAC,kBAAkB,EAAU,MAAM,CAAC,CAAC;IACtF,CAAC;SAAM,CAAC;QACJ,IAAY,CAAC,GAAW,CAAC,GAAW,CAAC,GAAG,CAAC,EAAE,CAAC;YACxC,WAAW,CAAC,kBAAkB,CAAS,CAAC,EAAU,CAAC,EAAU,CAAC,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IACD,WAAW,CAAC,GAAG,EAAE,CAAC;IAElB,IAAI,cAAc,EAAE,CAAC;QACjB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;QACnE,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;IAC9B,CAAC;AACL,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,qBAAqB,GAAG;IAC3C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC9C,MAAM,4BAA4B,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAkC,CAAC;QAC7H,IAAI,CAAC,6BAA6B,CAAC,4BAA4B,CAAC,CAAC;IACrE,CAAC;IAED,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;AACtC,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,8BAA8B,GAAG,UACpD,eAAwC,EACxC,iBAAyB,EACzB,oBAA4B,EAC5B,OAAyB,EACzB,UAAkB;IAElB,MAAM,aAAa,GAAG,eAA+C,CAAC;IAEtE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,MAAM,CAAC,GAAG,CAAC,OAAQ,CAAC,CAAC;QACrB,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IAClC,CAAC;IAED,aAAa,CAAC,OAAO,GAAG;QACpB,OAAO,EAAE,iBAAiB;QAC1B,UAAU,EAAE,oBAAoB;KACnC,CAAC;IAEF,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,qCAAqC,CAAC,iBAAiB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AAC7G,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,MAAqB;IAC1E,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5C,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEjD,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,kBAAkB,EAAkC,CAAC,CAAC;IACpG,CAAC;AACL,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,sBAAsB,GAAG;IAC5C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;QAEjD,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC/B,MAAM,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACnC,MAAM,CAAC,cAAc,EAAE,CAAC;IAC5B,CAAC;AACL,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,kCAAkC,GAAG,UACxD,eAA6C,EAC7C,MAAgE;IAEhE,mFAAmF;IACnF,eAAe,CAAC,KAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;QAC7D,MAAM,mBAAmB,GAA+B;YACpD,SAAS,EAAE,CAAC;YACZ,QAAQ,EAAE,EAAE;SACf,CAAC;QACF,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,mBAAmB,CAAC,SAAS,EAAE,CAAC;YACpC,CAAC;YACD,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC9B,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,OAAO,CAAC,OAAO;gBACrB,IAAI,EAAE,OAAO,CAAC,OAAO;gBACrB,MAAM,EAAE,OAAO,CAAC,OAAO;gBACvB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;aACzB,CAAC,CAAC;QACP,CAAC;QACD,MAAM,CAAC,mBAAmB,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAAU,eAAwC;IACrG,MAAM,qBAAqB,GAAG,eAA+C,CAAC;IAC9E,IAAI,qBAAqB,EAAE,CAAC;QACxB,eAAe,CAAC,OAAO,EAAE,CAAC;IAC9B,CAAC;AACL,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,qCAAqC,GAAG,UAAU,aAAqB,EAAE,OAAyB,EAAE,UAAkB;IACzI,IAAI,OAAO,EAAE,CAAC;QACV,OAAO,GAAG,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;IAC7D,CAAC;SAAM,CAAC;QACJ,OAAO,GAAG,EAAE,CAAC;IACjB,CAAC;IACD,OAAO;QACH,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;YACpC,IAAI,EAAE,OAAO,GAAG,aAAa;SAChC,CAAC;QACF,UAAU;KACb,CAAC;AACN,CAAC,CAAC", "sourcesContent": ["import { Logger } from \"core/Misc/logger\";\r\nimport type { IComputeEffectCreationOptions, IComputeShaderPath } from \"../../../Compute/computeEffect\";\r\nimport { ComputeEffect } from \"../../../Compute/computeEffect\";\r\nimport type { IComputeContext } from \"../../../Compute/IComputeContext\";\r\nimport type { IComputePipelineContext } from \"../../../Compute/IComputePipelineContext\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport type { ComputeBindingList, ComputeBindingMapping, ComputeCompilationMessages } from \"../../Extensions/engine.computeShader\";\r\nimport { WebGPUEngine } from \"../../webgpuEngine\";\r\nimport { WebGPUComputeContext } from \"../webgpuComputeContext\";\r\nimport { WebGPUComputePipelineContext } from \"../webgpuComputePipelineContext\";\r\nimport * as WebGPUConstants from \"../webgpuConstants\";\r\nimport type { WebGPUPerfCounter } from \"../webgpuPerfCounter\";\r\nimport type { DataBuffer } from \"../../../Buffers/dataBuffer\";\r\n\r\ndeclare module \"../../webgpuEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface WebGPUEngine {\r\n        /** @internal */\r\n        _createComputePipelineStageDescriptor(computeShader: string, defines: Nullable<string>, entryPoint: string): GPUProgrammableStage;\r\n        /** @internal\r\n         * Either all of x,y,z or buffer and offset should be defined.\r\n         */\r\n        _computeDispatch(\r\n            effect: ComputeEffect,\r\n            context: IComputeContext,\r\n            bindings: ComputeBindingList,\r\n            x?: number,\r\n            y?: number,\r\n            z?: number,\r\n            buffer?: DataBuffer,\r\n            offset?: number,\r\n            bindingsMapping?: ComputeBindingMapping,\r\n            gpuPerfCounter?: WebGPUPerfCounter\r\n        ): void;\r\n    }\r\n}\r\n\r\nconst ComputePassDescriptor: GPUComputePassDescriptor = {};\r\n\r\nWebGPUEngine.prototype.createComputeContext = function (): IComputeContext | undefined {\r\n    return new WebGPUComputeContext(this._device, this._cacheSampler);\r\n};\r\n\r\nWebGPUEngine.prototype.createComputeEffect = function (baseName: string | (IComputeShaderPath & { computeToken?: string }), options: IComputeEffectCreationOptions): ComputeEffect {\r\n    const compute = typeof baseName === \"string\" ? baseName : baseName.computeToken || baseName.computeSource || baseName.computeElement || baseName.compute;\r\n\r\n    const name = compute + \"@\" + options.defines;\r\n    if (this._compiledComputeEffects[name]) {\r\n        const compiledEffect = this._compiledComputeEffects[name];\r\n        if (options.onCompiled && compiledEffect.isReady()) {\r\n            options.onCompiled(compiledEffect);\r\n        }\r\n\r\n        return compiledEffect;\r\n    }\r\n    const effect = new ComputeEffect(baseName, options, this, name);\r\n    this._compiledComputeEffects[name] = effect;\r\n\r\n    return effect;\r\n};\r\n\r\nWebGPUEngine.prototype.createComputePipelineContext = function (): IComputePipelineContext {\r\n    return new WebGPUComputePipelineContext(this);\r\n};\r\n\r\nWebGPUEngine.prototype.areAllComputeEffectsReady = function (): boolean {\r\n    for (const key in this._compiledComputeEffects) {\r\n        const effect = this._compiledComputeEffects[key];\r\n\r\n        if (!effect.isReady()) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    return true;\r\n};\r\n\r\nWebGPUEngine.prototype.computeDispatch = function (\r\n    effect: ComputeEffect,\r\n    context: IComputeContext,\r\n    bindings: ComputeBindingList,\r\n    x: number,\r\n    y = 1,\r\n    z = 1,\r\n    bindingsMapping?: ComputeBindingMapping,\r\n    gpuPerfCounter?: WebGPUPerfCounter\r\n): void {\r\n    this._computeDispatch(effect, context, bindings, x, y, z, undefined, undefined, bindingsMapping, gpuPerfCounter);\r\n};\r\n\r\nWebGPUEngine.prototype.computeDispatchIndirect = function (\r\n    effect: ComputeEffect,\r\n    context: IComputeContext,\r\n    bindings: ComputeBindingList,\r\n    buffer: DataBuffer,\r\n    offset: number = 0,\r\n    bindingsMapping?: ComputeBindingMapping,\r\n    gpuPerfCounter?: WebGPUPerfCounter\r\n): void {\r\n    this._computeDispatch(effect, context, bindings, undefined, undefined, undefined, buffer, offset, bindingsMapping, gpuPerfCounter);\r\n};\r\n\r\nWebGPUEngine.prototype._computeDispatch = function (\r\n    effect: ComputeEffect,\r\n    context: IComputeContext,\r\n    bindings: ComputeBindingList,\r\n    x?: number,\r\n    y?: number,\r\n    z?: number,\r\n    buffer?: DataBuffer,\r\n    offset?: number,\r\n    bindingsMapping?: ComputeBindingMapping,\r\n    gpuPerfCounter?: WebGPUPerfCounter\r\n): void {\r\n    this._endCurrentRenderPass();\r\n\r\n    const contextPipeline = effect._pipelineContext as WebGPUComputePipelineContext;\r\n    const computeContext = context as WebGPUComputeContext;\r\n\r\n    if (!contextPipeline.computePipeline) {\r\n        contextPipeline.computePipeline = this._device.createComputePipeline({\r\n            layout: WebGPUConstants.AutoLayoutMode.Auto,\r\n            compute: contextPipeline.stage!,\r\n        });\r\n    }\r\n\r\n    if (gpuPerfCounter) {\r\n        this._timestampQuery.startPass(ComputePassDescriptor, this._timestampIndex);\r\n    }\r\n\r\n    const computePass = this._renderEncoder.beginComputePass(ComputePassDescriptor);\r\n\r\n    computePass.setPipeline(contextPipeline.computePipeline);\r\n\r\n    const bindGroups = computeContext.getBindGroups(bindings, contextPipeline.computePipeline, bindingsMapping);\r\n    for (let i = 0; i < bindGroups.length; ++i) {\r\n        const bindGroup = bindGroups[i];\r\n        if (!bindGroup) {\r\n            continue;\r\n        }\r\n        computePass.setBindGroup(i, bindGroup);\r\n    }\r\n\r\n    if (buffer !== undefined) {\r\n        computePass.dispatchWorkgroupsIndirect(buffer.underlyingResource, <number>offset);\r\n    } else {\r\n        if (<number>x + <number>y + <number>z > 0) {\r\n            computePass.dispatchWorkgroups(<number>x, <number>y, <number>z);\r\n        }\r\n    }\r\n    computePass.end();\r\n\r\n    if (gpuPerfCounter) {\r\n        this._timestampQuery.endPass(this._timestampIndex, gpuPerfCounter);\r\n        this._timestampIndex += 2;\r\n    }\r\n};\r\n\r\nWebGPUEngine.prototype.releaseComputeEffects = function () {\r\n    for (const name in this._compiledComputeEffects) {\r\n        const webGPUPipelineContextCompute = this._compiledComputeEffects[name].getPipelineContext() as WebGPUComputePipelineContext;\r\n        this._deleteComputePipelineContext(webGPUPipelineContextCompute);\r\n    }\r\n\r\n    this._compiledComputeEffects = {};\r\n};\r\n\r\nWebGPUEngine.prototype._prepareComputePipelineContext = function (\r\n    pipelineContext: IComputePipelineContext,\r\n    computeSourceCode: string,\r\n    rawComputeSourceCode: string,\r\n    defines: Nullable<string>,\r\n    entryPoint: string\r\n): void {\r\n    const webGpuContext = pipelineContext as WebGPUComputePipelineContext;\r\n\r\n    if (this.dbgShowShaderCode) {\r\n        Logger.Log(defines!);\r\n        Logger.Log(computeSourceCode);\r\n    }\r\n\r\n    webGpuContext.sources = {\r\n        compute: computeSourceCode,\r\n        rawCompute: rawComputeSourceCode,\r\n    };\r\n\r\n    webGpuContext.stage = this._createComputePipelineStageDescriptor(computeSourceCode, defines, entryPoint);\r\n};\r\n\r\nWebGPUEngine.prototype._releaseComputeEffect = function (effect: ComputeEffect): void {\r\n    if (this._compiledComputeEffects[effect._key]) {\r\n        delete this._compiledComputeEffects[effect._key];\r\n\r\n        this._deleteComputePipelineContext(effect.getPipelineContext() as WebGPUComputePipelineContext);\r\n    }\r\n};\r\n\r\nWebGPUEngine.prototype._rebuildComputeEffects = function (): void {\r\n    for (const key in this._compiledComputeEffects) {\r\n        const effect = this._compiledComputeEffects[key];\r\n\r\n        effect._pipelineContext = null;\r\n        effect._wasPreviouslyReady = false;\r\n        effect._prepareEffect();\r\n    }\r\n};\r\n\r\nWebGPUEngine.prototype._executeWhenComputeStateIsCompiled = function (\r\n    pipelineContext: WebGPUComputePipelineContext,\r\n    action: (messages: Nullable<ComputeCompilationMessages>) => void\r\n): void {\r\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n    pipelineContext.stage!.module.getCompilationInfo().then((info) => {\r\n        const compilationMessages: ComputeCompilationMessages = {\r\n            numErrors: 0,\r\n            messages: [],\r\n        };\r\n        for (const message of info.messages) {\r\n            if (message.type === \"error\") {\r\n                compilationMessages.numErrors++;\r\n            }\r\n            compilationMessages.messages.push({\r\n                type: message.type,\r\n                text: message.message,\r\n                line: message.lineNum,\r\n                column: message.linePos,\r\n                length: message.length,\r\n                offset: message.offset,\r\n            });\r\n        }\r\n        action(compilationMessages);\r\n    });\r\n};\r\n\r\nWebGPUEngine.prototype._deleteComputePipelineContext = function (pipelineContext: IComputePipelineContext): void {\r\n    const webgpuPipelineContext = pipelineContext as WebGPUComputePipelineContext;\r\n    if (webgpuPipelineContext) {\r\n        pipelineContext.dispose();\r\n    }\r\n};\r\n\r\nWebGPUEngine.prototype._createComputePipelineStageDescriptor = function (computeShader: string, defines: Nullable<string>, entryPoint: string): GPUProgrammableStage {\r\n    if (defines) {\r\n        defines = \"//\" + defines.split(\"\\n\").join(\"\\n//\") + \"\\n\";\r\n    } else {\r\n        defines = \"\";\r\n    }\r\n    return {\r\n        module: this._device.createShaderModule({\r\n            code: defines + computeShader,\r\n        }),\r\n        entryPoint,\r\n    };\r\n};\r\n"]}