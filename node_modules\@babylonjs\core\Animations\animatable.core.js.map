{"version": 3, "file": "animatable.core.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Animations/animatable.core.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,8BAA6B;AAGlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,aAAa,EAAE,iCAAgC;AACxD,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,gCAA+B;AAIjF;;GAEG;AACH,MAAM,OAAO,UAAU;IA2CnB;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAAa;QAC3B,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,+BAA+B;YAC/B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YAClB,OAAO;QACX,CAAC;QAED,sCAAsC;QACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAa;QAC/B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAClE,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAEjD,SAAS,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,kFAAkF;QAClF,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC;IACrG,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,YACI,KAAY;IACZ,gCAAgC;IACzB,MAAW;IAClB,2DAA2D;IACpD,YAAoB,CAAC;IAC5B,6DAA6D;IACtD,UAAkB,GAAG;IAC5B,qEAAqE;IAC9D,gBAAyB,KAAK,EACrC,aAAqB,GAAG;IACxB,0EAA0E;IACnE,cAAqC,EAC5C,UAAwB;IACxB,sDAAsD;IAC/C,eAAsC;IAC7C,2EAA2E;IACpE,aAAsB,KAAK;IAClC,wHAAwH;IACjH,YAAY,CAAC;QAhBb,WAAM,GAAN,MAAM,CAAK;QAEX,cAAS,GAAT,SAAS,CAAY;QAErB,YAAO,GAAP,OAAO,CAAc;QAErB,kBAAa,GAAb,aAAa,CAAiB;QAG9B,mBAAc,GAAd,cAAc,CAAuB;QAGrC,oBAAe,GAAf,eAAe,CAAuB;QAEtC,eAAU,GAAV,UAAU,CAAiB;QAE3B,cAAS,GAAT,SAAS,CAAI;QArIhB,sBAAiB,GAAqB,IAAI,CAAC;QAC3C,iBAAY,GAAqB,IAAI,CAAC;QACtC,qBAAgB,GAAqB,IAAI,CAAC;QAClD,cAAc;QACP,uBAAkB,GAAG,IAAI,KAAK,EAAoB,CAAC;QAClD,YAAO,GAAG,KAAK,CAAC;QAEhB,gBAAW,GAAG,CAAC,CAAC;QAChB,YAAO,GAAG,CAAC,GAAG,CAAC;QACf,oBAAe,GAAG,CAAC,GAAG,CAAC;QACvB,cAAS,GAAyB,IAAI,CAAC;QACvC,yBAAoB,GAAqB,IAAI,CAAC;QAC9C,eAAU,GAAqB,IAAI,CAAC;QAE5C;;;WAGG;QACI,iBAAY,GAAG,IAAI,CAAC;QAE3B;;WAEG;QACI,qBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAc,CAAC;QAE/D;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAc,CAAC;QAsG5D,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,UAAU;IACV;;;;;OAKG;IACI,QAAQ,CAAC,IAA0B;QACtC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,IAAI,EAAE,CAAC;YACP,wDAAwD;YACxD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,MAAW,EAAE,UAAuB;QACxD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACrD,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YAEpC,MAAM,mBAAmB,GAAG,IAAI,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACvF,mBAAmB,CAAC,OAAO,GAAG,GAAG,EAAE;gBAC/B,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC3B,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,4BAA4B,CAAC,QAAgB;QAChD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;gBACjE,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,mCAAmC,CAAC,QAAgB;QACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;gBACjE,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,KAAK;QACR,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,aAAqB;QACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC;YACzD,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,aAAa,GAAG,aAAa,CAAC;QACrE,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,KAAK,CAAC;QAC9D,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,KAAa,EAAE,SAAS,GAAG,KAAK;QAC7C,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,IAAI,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC;YAC1D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC3F,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;YACjH,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,CAAC;QACnC,CAAC;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAEO,oBAAoB;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;OAMG;IACI,IAAI,CAAC,aAAsB,EAAE,UAAqC,EAAE,eAAe,GAAG,KAAK,EAAE,kBAAkB,GAAG,KAAK;QAC1H,IAAI,aAAa,IAAI,UAAU,EAAE,CAAC;YAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEzD,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC;gBACX,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAElD,KAAK,IAAI,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBACjE,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;oBAClD,IAAI,aAAa,IAAI,gBAAgB,CAAC,SAAS,CAAC,IAAI,IAAI,aAAa,EAAE,CAAC;wBACpE,SAAS;oBACb,CAAC;oBACD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;wBACrD,SAAS;oBACb,CAAC;oBAED,gBAAgB,CAAC,OAAO,EAAE,CAAC;oBAC3B,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACvC,CAAC;gBAED,IAAI,iBAAiB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBAChC,IAAI,CAAC,eAAe,EAAE,CAAC;wBACnB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;oBAClD,CAAC;oBACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAChC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAE3D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,eAAe,EAAE,CAAC;oBACnB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACpD,CAAC;gBACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAElD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;oBAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;gBACvC,CAAC;gBAED,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEnC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAChC,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,SAAS;QAClB,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAC7B,GAAG,EAAE;gBACD,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,SAAS,EACT,SAAS,EACT,IAAI,EACJ,IAAI,CACP,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,QAAQ,CAAC,KAAa;QACzB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;gBAC7B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC9B,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,iBAAiB,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;YACpD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAC/F,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,CAAC,UAAU,CAAC,wBAAwB,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;YAC3F,0EAA0E;YAC1E,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC;QAEpC,YAAY;QACZ,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAClD,IAAI,KAAa,CAAC;QAElB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACxD,MAAM,SAAS,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACtJ,OAAO,GAAG,OAAO,IAAI,SAAS,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;QAEhC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,iCAAiC;gBACjC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAEhD,iCAAiC;gBACjC,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;oBACxD,iBAAiB,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;gBACvC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;gBACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;;AA7dD;;;;GAIG;AACW,mCAAwB,GAAG,KAAK,AAAR,CAAS;AA2dnD,gBAAgB;AAChB,SAAS,uCAAuC,CAAC,MAMhD;IACG,IAAI,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;QAC/D,OAAO,MAAM,CAAC,aAAa,CAAC;IAChC,CAAC;IAED,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACjD,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,MAAM,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;IAE3C,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;QAC3B,uCAAuC;QACvC,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;QACjC,aAAa,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAC1E,CAAC;SAAM,CAAC;QACJ,UAAU,GAAG,CAAC,CAAC;QACf,mCAAmC;QACnC,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;QAChC,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,UAAU,CAAC;QAC9C,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACb,IAAI,MAAM,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,YAAY,GAAG,IAAI,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACJ,OAAO,iBAAiB,CAAC,YAAY,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAC3F,CAAC;IAED,iCAAiC;IACjC,IAAI,CAAC,YAAY,EAAE,CAAC;QAChB,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACjC,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAClC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEpC,KAAK,IAAI,SAAS,GAAG,UAAU,EAAE,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;YACjF,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACtD,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,SAAS;YACb,CAAC;YAED,KAAK,GAAG,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC;YAC7C,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEnD,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;YAE5F,cAAc,CAAC,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YACrD,iBAAiB,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG,CAAC,eAAe,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;YAC7H,eAAe,CAAC,gBAAgB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;QAC3D,CAAC;QAED,eAAe,CAAC,SAAS,EAAE,CAAC;IAChC,CAAC;IAED,iCAAiC;IACjC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;QAChF,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC9D,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,SAAS;QACb,CAAC;QAED,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,iBAAiB,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAEnD,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;QAC5F,cAAc,CAAC,aAAa,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QAC3D,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE,cAAc,EAAE,gBAAgB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACvF,eAAe,CAAC,aAAa,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;QACpE,UAAU,CAAC,UAAU,CAAC,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QACpG,eAAe,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAC7E,CAAC;IAED,MAAM,SAAS,GAAG,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;IACjH,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;IAC7E,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,gBAAgB;AAChB,SAAS,0CAA0C,CAC/C,MAMC,EACD,aAAyB;IAEzB,IAAI,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;QAC/D,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,MAAM,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;IAC3C,IAAI,oBAAoB,GAAG,aAAa,CAAC;IAEzC,IAAI,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;QAC7D,oBAAoB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;SAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxC,UAAU,CAAC,UAAU,CAAC,aAAa,EAAE,iBAAiB,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,oBAAoB,CAAC,CAAC;QAE9H,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,oBAAoB,CAAC;QAChC,CAAC;IACL,CAAC;SAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtC,iCAAiC;QACjC,IAAI,UAAU,GAAG,GAAG,CAAC;QACrB,IAAI,WAA8B,CAAC;QACnC,IAAI,OAAsB,CAAC;QAE3B,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;YAEvC,WAAW,GAAG,EAAE,CAAC;YACjB,OAAO,GAAG,EAAE,CAAC;YAEb,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC;aAAM,CAAC;YACJ,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,0BAA0B;gBAC1B,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAE7J,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;oBACnC,OAAO,aAAa,CAAC;gBACzB,CAAC;YACL,CAAC;YAED,WAAW,GAAG,EAAE,CAAC;YACjB,OAAO,GAAG,EAAE,CAAC;YACb,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;QACpC,CAAC;QAED,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;YACxE,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACtD,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;QACvD,CAAC;QAED,mGAAmG;QAEnG,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,GAAI,CAAC;YAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;gBAC7I,oBAAoB,GAAG,aAAa,CAAC;gBACrC,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACvD,KAAK,IAAI,CAAC,CAAC;gBACX,SAAS;YACb,CAAC;YACD,gBAAgB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;YACnC,UAAU,CAAC,UAAU,CAAC,oBAAoB,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;YACzH,KAAK,EAAE,CAAC;QACZ,CAAC;IACL,CAAC;IAED,iCAAiC;IACjC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;QAChF,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC9D,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,SAAS;QACb,CAAC;QAED,oBAAoB,CAAC,aAAa,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,UAAU,CAAC,UAAU,CAAC,oBAAoB,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;IACzH,CAAC;IAED,OAAO,oBAAoB,CAAC;AAChC,CAAC;AAED,gBAAgB;AAChB,SAAS,4BAA4B,CAAC,KAAY;IAC9C,IAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,MAAM,EAAE,CAAC;QACpD,OAAO;IACX,CAAC;IACD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,mCAAmC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;QACpF,MAAM,MAAM,GAAG,KAAK,CAAC,mCAAmC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAErE,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,iBAAiB,GAAqB,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;YAC3C,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;gBACxD,SAAS;YACb,CAAC;YACD,MAAM,mBAAmB,GAAG,SAAS,CAAC,oCAAoC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,qBAAqB;YAEpH,IAAI,UAAU,GAAQ,MAAM,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,mBAAmB,EAAE,CAAC;gBACtB,UAAU,GAAG,uCAAuC,CAAC,MAAM,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACJ,MAAM,cAAc,GAAG,aAAa,CAAC,CAAC,KAAK,SAAS,CAAC;gBACrD,IAAI,cAAc,EAAE,CAAC;oBACjB,UAAU,GAAG,0CAA0C,CAAC,MAAM,EAAE,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACzG,CAAC;qBAAM,CAAC;oBACJ,IAAI,UAAU,GAAG,CAAC,CAAC;oBACnB,IAAI,UAAU,GAAG,GAAG,CAAC;oBAErB,MAAM,0CAA0C,GAC5C,iBAAiB,IAAI,iBAAiB,CAAC,eAAe,CAAC,QAAQ,KAAK,SAAS,CAAC,uCAAuC,CAAC;oBAE1H,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;wBAC3B,uCAAuC;wBACvC,IAAI,0CAA0C,EAAE,CAAC;4BAC7C,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC;wBAC7E,CAAC;6BAAM,IAAI,iBAAiB,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;4BAClD,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;wBAC/D,CAAC;6BAAM,IAAI,iBAAiB,EAAE,CAAC;4BAC3B,UAAU,GAAG,aAAa,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;wBAC5D,CAAC;6BAAM,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;4BAC7B,UAAU,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;wBACvC,CAAC;6BAAM,CAAC;4BACJ,UAAU,GAAG,aAAa,CAAC;wBAC/B,CAAC;oBACL,CAAC;yBAAM,IAAI,iBAAiB,EAAE,CAAC;wBAC3B,mCAAmC;wBACnC,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;wBAChC,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,UAAU,CAAC;wBACpD,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;4BACd,IAAI,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;gCACvC,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;4BAC7D,CAAC;iCAAM,CAAC;gCACJ,UAAU,GAAG,iBAAiB,CAAC,YAAY,GAAG,KAAK,CAAC;4BACxD,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACJ,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC;wBAChD,CAAC;wBAED,IAAI,0CAA0C,EAAE,CAAC;4BAC7C,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;gCACtB,UAAU,CAAC,QAAQ,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;4BACnD,CAAC;iCAAM,CAAC;gCACJ,UAAU,IAAI,aAAa,CAAC;4BAChC,CAAC;wBACL,CAAC;wBAED,UAAU,GAAG,CAAC,CAAC;oBACnB,CAAC;oBAED,iCAAiC;oBACjC,KAAK,IAAI,SAAS,GAAG,UAAU,EAAE,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;wBACjF,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;wBACtD,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC;wBAEnD,IAAI,CAAC,KAAK,EAAE,CAAC;4BACT,SAAS;wBACb,CAAC;6BAAM,IAAI,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;4BACxD,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;wBACtE,CAAC;6BAAM,CAAC;4BACJ,UAAU,IAAI,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;wBACxD,CAAC;oBACL,CAAC;oBAED,iCAAiC;oBACjC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;wBAChF,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;wBAC9D,MAAM,KAAK,GAAW,gBAAgB,CAAC,MAAM,CAAC;wBAE9C,IAAI,CAAC,KAAK,EAAE,CAAC;4BACT,SAAS;wBACb,CAAC;6BAAM,IAAI,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;4BACxD,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;wBACtE,CAAC;6BAAM,CAAC;4BACJ,UAAU,IAAI,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;wBACxD,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;QAC9B,CAAC;QAED,MAAM,CAAC,qBAAqB,GAAG,EAAE,CAAC;IACtC,CAAC;IACD,KAAK,CAAC,mCAAmC,CAAC,KAAK,EAAE,CAAC;AACtD,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,qCAAqC,CAAC,KAAY,EAAE,gBAAkC,EAAE,aAAkB;IACtH,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACvC,KAAK,CAAC,mCAAmC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAElE,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;QAChC,MAAM,CAAC,qBAAqB,GAAG,EAAE,CAAC;IACtC,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;QAC7D,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG;YACxD,WAAW,EAAE,CAAC;YACd,mBAAmB,EAAE,CAAC;YACtB,UAAU,EAAE,EAAE;YACd,kBAAkB,EAAE,EAAE;YACtB,aAAa,EAAE,aAAa;SAC/B,CAAC;IACN,CAAC;IAED,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;QAC9B,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpG,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,mBAAmB,IAAI,gBAAgB,CAAC,MAAM,CAAC;IAC7G,CAAC;SAAM,CAAC;QACJ,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5F,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,WAAW,IAAI,gBAAgB,CAAC,MAAM,CAAC;IACrG,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,sBAAsB,CAAC,UAAwB,EAAE,SAAsB;IACnF,IAAI,SAAS,EAAE,CAAC;QACZ,SAAS,CAAC,SAAS,CAAC,kBAAkB,GAAG,UACrC,MAAY,EACZ,SAAiB,EACjB,WAAmB,EACnB,iBAAiB,GAAG,KAAK,EACzB,sBAAyC,IAAI;YAE7C,uFAAuF;YACvF,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnC,CAAC;YAED,yEAAyE;YACzE,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC7D,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;YACD,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;YAC9B,MAAM,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC;YAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAElD,iBAAiB;YACjB,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC;YACvC,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,MAAM,iBAAiB,GAAG,iBAAiB,IAAI,YAAY,IAAI,gBAAgB,IAAI,IAAI,CAAC,MAAM,IAAI,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC;YACnI,MAAM,WAAW,GAAG,iBAAiB,IAAI,MAAM,IAAI,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1G,MAAM,qBAAqB,GACvB,iBAAiB,IAAI,CAAC,MAAM,IAAI,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YAEvJ,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAE9C,wBAAwB;YACxB,IAAI,IAAsC,CAAC;YAC3C,IAAI,eAAwB,CAAC;YAC7B,IAAI,GAAW,CAAC;YAEhB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC;gBAC9D,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;gBACvB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC;oBACzC,IAAI,iBAAiB,EAAE,CAAC;wBACpB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;wBAEzB,oDAAoD;wBACpD,IAAI,iBAAiB,EAAE,CAAC;4BACpB,eAAe,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;4BACvC,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;4BAE9D,8EAA8E;wBAClF,CAAC;6BAAM,IAAI,qBAAqB,IAAI,mBAAmB,EAAE,CAAC;4BACtD,eAAe,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;4BACvC,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,CAAC;4BAEzE,mEAAmE;wBACvE,CAAC;6BAAM,CAAC;4BACJ,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;wBACrB,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;oBACrB,CAAC;oBACD,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;gBACnE,CAAC;YACL,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,GAAG,WAAW,EAAE,EAAE,GAAG,WAAW,CAAC,CAAC;YAChF,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC;IACN,CAAC;IAED,IAAI,CAAC,UAAU,EAAE,CAAC;QACd,OAAO;IACX,CAAC;IAED,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,eAAwB;QAC9D,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QAED,eAAe;QACf,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,OAAO;YACX,CAAC;YACD,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACzK,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAE9B,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAC5C,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,CAAC;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAE1C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACtD,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBACjE,KAAK,EAAE,CAAC,CAAC,oBAAoB;YACjC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG;QACzC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,OAAO,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAC1C,MAAW,EACX,IAAY,EACZ,EAAU,EACV,MAAM,GAAG,GAAG,EACZ,IAAc,EACd,aAAqB,GAAG,EACxB,cAA2B,EAC3B,UAAuB,EACvB,UAAqC,EACrC,eAA4B,EAC5B,UAAU,GAAG,KAAK;QAElB,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;QAC/J,kBAAkB,CAAC,MAAM,GAAG,MAAM,CAAC;QAEnC,OAAO,kBAAkB,CAAC;IAC9B,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,cAAc,GAAG,UAClC,MAAW,EACX,IAAY,EACZ,EAAU,EACV,IAAc,EACd,aAAqB,GAAG,EACxB,cAA2B,EAC3B,UAAuB,EACvB,WAAW,GAAG,IAAI,EAClB,UAAqC,EACrC,eAA4B,EAC5B,UAAU,GAAG,KAAK;QAElB,oEAAoE;QACpE,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACjB,MAAM,GAAG,GAAG,IAAI,CAAC;YACjB,IAAI,GAAG,EAAE,CAAC;YACV,EAAE,GAAG,GAAG,CAAC;YACT,UAAU,GAAG,CAAC,UAAU,CAAC;QAC7B,CAAC;QACD,kCAAkC;QAClC,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;YACZ,UAAU,GAAG,CAAC,UAAU,CAAC;QAC7B,CAAC;QACD,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;QAClI,CAAC;QAED,MAAM,yBAAyB,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzE,mBAAmB;QACnB,IAAI,MAAM,CAAC,UAAU,IAAI,yBAAyB,EAAE,CAAC;YACjD,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QAC3D,CAAC;QAED,sBAAsB;QACtB,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;YAC5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;gBACtD,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC;YAC9I,CAAC;QACL,CAAC;QAED,UAAU,CAAC,KAAK,EAAE,CAAC;QAEnB,OAAO,UAAU,CAAC;IACtB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAC3C,MAAW,EACX,qBAA8B,EAC9B,IAAY,EACZ,EAAU,EACV,IAAc,EACd,aAAqB,GAAG,EACxB,cAA2B,EAC3B,UAAuB,EACvB,WAAW,GAAG,IAAI,EAClB,UAAqC,EACrC,eAA4B,EAC5B,UAAU,GAAG,KAAK;QAElB,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAE9D,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;QACjJ,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;QACpJ,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,UACxC,MAAW,EACX,UAAuB,EACvB,IAAY,EACZ,EAAU,EACV,IAAc,EACd,aAAqB,GAAG,EACxB,cAA2B,EAC3B,eAA4B,EAC5B,UAAU,GAAG,KAAK;QAElB,oEAAoE;QACpE,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACjB,MAAM,GAAG,GAAG,IAAI,CAAC;YACjB,IAAI,GAAG,EAAE,CAAC;YACV,EAAE,GAAG,GAAG,CAAC;YACT,UAAU,GAAG,CAAC,UAAU,CAAC;QAC7B,CAAC;QACD,kCAAkC;QAClC,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;YACZ,UAAU,GAAG,CAAC,UAAU,CAAC;QAC7B,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;QAErI,OAAO,UAAU,CAAC;IACtB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,6BAA6B,GAAG,UACjD,MAAY,EACZ,qBAA8B,EAC9B,UAAuB,EACvB,IAAY,EACZ,EAAU,EACV,IAAc,EACd,UAAmB,EACnB,cAA2B,EAC3B,eAA4B,EAC5B,UAAU,GAAG,KAAK;QAElB,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAE9D,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;QACpI,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;QACvI,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,MAAW;QAC9D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAClE,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBACnD,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,MAAW;QAClE,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAClE,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBACnD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,MAAW,EAAE,aAAsB,EAAE,UAAqC;QACrH,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAE3D,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACnC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAChE,CAAC;YACD,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvC,KAAK,CAAC,IAAI,EAAE,CAAC;QACjB,CAAC;IACL,CAAC,CAAC;AACN,CAAC", "sourcesContent": ["import { Observable } from \"core/Misc/observable\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { Nullable } from \"core/types\";\r\nimport { RuntimeAnimation } from \"./runtimeAnimation\";\r\nimport { Animation } from \"./animation\";\r\nimport { PrecisionDate } from \"core/Misc/precisionDate\";\r\nimport { Matrix, Quaternion, TmpVectors, Vector3 } from \"core/Maths/math.vector\";\r\nimport type { Bone } from \"core/Bones/bone\";\r\nimport type { Node } from \"../node\";\r\n\r\n/**\r\n * Class used to store an actual running animation\r\n */\r\nexport class Animatable {\r\n    /**\r\n     * If true, the animatable will be processed even if it is considered actively paused (weight of 0 and previous weight of 0).\r\n     * This can be used to force the full processing of paused animatables in the animation engine.\r\n     * Default is false.\r\n     */\r\n    public static ProcessPausedAnimatables = false;\r\n\r\n    private _localDelayOffset: Nullable<number> = null;\r\n    private _pausedDelay: Nullable<number> = null;\r\n    private _manualJumpDelay: Nullable<number> = null;\r\n    /** @hidden */\r\n    public _runtimeAnimations = new Array<RuntimeAnimation>();\r\n    private _paused = false;\r\n    private _scene: Scene;\r\n    private _speedRatio = 1;\r\n    private _weight = -1.0;\r\n    private _previousWeight = -1.0;\r\n    private _syncRoot: Nullable<Animatable> = null;\r\n    private _frameToSyncFromJump: Nullable<number> = null;\r\n    private _goToFrame: Nullable<number> = null;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the animatable must be disposed and removed at the end of the animation.\r\n     * This will only apply for non looping animation (default is true)\r\n     */\r\n    public disposeOnEnd = true;\r\n\r\n    /**\r\n     * Gets a boolean indicating if the animation has started\r\n     */\r\n    public animationStarted = false;\r\n\r\n    /**\r\n     * Observer raised when the animation ends\r\n     */\r\n    public onAnimationEndObservable = new Observable<Animatable>();\r\n\r\n    /**\r\n     * Observer raised when the animation loops\r\n     */\r\n    public onAnimationLoopObservable = new Observable<Animatable>();\r\n\r\n    /**\r\n     * Gets the root Animatable used to synchronize and normalize animations\r\n     */\r\n    public get syncRoot(): Nullable<Animatable> {\r\n        return this._syncRoot;\r\n    }\r\n\r\n    /**\r\n     * Gets the current frame of the first RuntimeAnimation\r\n     * Used to synchronize Animatables\r\n     */\r\n    public get masterFrame(): number {\r\n        if (this._runtimeAnimations.length === 0) {\r\n            return 0;\r\n        }\r\n\r\n        return this._runtimeAnimations[0].currentFrame;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the animatable weight (-1.0 by default meaning not weighted)\r\n     */\r\n    public get weight(): number {\r\n        return this._weight;\r\n    }\r\n\r\n    public set weight(value: number) {\r\n        if (value === -1) {\r\n            // -1 is ok and means no weight\r\n            this._weight = -1;\r\n            return;\r\n        }\r\n\r\n        // Else weight must be in [0, 1] range\r\n        this._weight = Math.min(Math.max(value, 0), 1.0);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the speed ratio to apply to the animatable (1.0 by default)\r\n     */\r\n    public get speedRatio(): number {\r\n        return this._speedRatio;\r\n    }\r\n\r\n    public set speedRatio(value: number) {\r\n        for (let index = 0; index < this._runtimeAnimations.length; index++) {\r\n            const animation = this._runtimeAnimations[index];\r\n\r\n            animation._prepareForSpeedRatioChange(value);\r\n        }\r\n        this._speedRatio = value;\r\n\r\n        // Resync _manualJumpDelay in case goToFrame was called before speedRatio was set.\r\n        if (this._goToFrame !== null) {\r\n            this.goToFrame(this._goToFrame);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the elapsed time since the animatable started in milliseconds\r\n     */\r\n    public get elapsedTime(): number {\r\n        return this._localDelayOffset === null ? 0 : this._scene._animationTime - this._localDelayOffset;\r\n    }\r\n\r\n    /**\r\n     * Creates a new Animatable\r\n     * @param scene defines the hosting scene\r\n     * @param target defines the target object\r\n     * @param fromFrame defines the starting frame number (default is 0)\r\n     * @param toFrame defines the ending frame number (default is 100)\r\n     * @param loopAnimation defines if the animation must loop (default is false)\r\n     * @param speedRatio defines the factor to apply to animation speed (default is 1)\r\n     * @param onAnimationEnd defines a callback to call when animation ends if it is not looping\r\n     * @param animations defines a group of animation to add to the new Animatable\r\n     * @param onAnimationLoop defines a callback to call when animation loops\r\n     * @param isAdditive defines whether the animation should be evaluated additively\r\n     * @param playOrder defines the order in which this animatable should be processed in the list of active animatables (default: 0)\r\n     */\r\n    constructor(\r\n        scene: Scene,\r\n        /** defines the target object */\r\n        public target: any,\r\n        /** [0] defines the starting frame number (default is 0) */\r\n        public fromFrame: number = 0,\r\n        /** [100] defines the ending frame number (default is 100) */\r\n        public toFrame: number = 100,\r\n        /** [false] defines if the animation must loop (default is false)  */\r\n        public loopAnimation: boolean = false,\r\n        speedRatio: number = 1.0,\r\n        /** defines a callback to call when animation ends if it is not looping */\r\n        public onAnimationEnd?: Nullable<() => void>,\r\n        animations?: Animation[],\r\n        /** defines a callback to call when animation loops */\r\n        public onAnimationLoop?: Nullable<() => void>,\r\n        /** [false] defines whether the animation should be evaluated additively */\r\n        public isAdditive: boolean = false,\r\n        /** [0] defines the order in which this animatable should be processed in the list of active animatables (default: 0) */\r\n        public playOrder = 0\r\n    ) {\r\n        this._scene = scene;\r\n        if (animations) {\r\n            this.appendAnimations(target, animations);\r\n        }\r\n\r\n        this._speedRatio = speedRatio;\r\n        scene._activeAnimatables.push(this);\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Synchronize and normalize current Animatable with a source Animatable\r\n     * This is useful when using animation weights and when animations are not of the same length\r\n     * @param root defines the root Animatable to synchronize with (null to stop synchronizing)\r\n     * @returns the current Animatable\r\n     */\r\n    public syncWith(root: Nullable<Animatable>): Animatable {\r\n        this._syncRoot = root;\r\n\r\n        if (root) {\r\n            // Make sure this animatable will animate after the root\r\n            const index = this._scene._activeAnimatables.indexOf(this);\r\n            if (index > -1) {\r\n                this._scene._activeAnimatables.splice(index, 1);\r\n                this._scene._activeAnimatables.push(this);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of runtime animations\r\n     * @returns an array of RuntimeAnimation\r\n     */\r\n    public getAnimations(): RuntimeAnimation[] {\r\n        return this._runtimeAnimations;\r\n    }\r\n\r\n    /**\r\n     * Adds more animations to the current animatable\r\n     * @param target defines the target of the animations\r\n     * @param animations defines the new animations to add\r\n     */\r\n    public appendAnimations(target: any, animations: Animation[]): void {\r\n        for (let index = 0; index < animations.length; index++) {\r\n            const animation = animations[index];\r\n\r\n            const newRuntimeAnimation = new RuntimeAnimation(target, animation, this._scene, this);\r\n            newRuntimeAnimation._onLoop = () => {\r\n                this.onAnimationLoopObservable.notifyObservers(this);\r\n                if (this.onAnimationLoop) {\r\n                    this.onAnimationLoop();\r\n                }\r\n            };\r\n\r\n            this._runtimeAnimations.push(newRuntimeAnimation);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the source animation for a specific property\r\n     * @param property defines the property to look for\r\n     * @returns null or the source animation for the given property\r\n     */\r\n    public getAnimationByTargetProperty(property: string): Nullable<Animation> {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            if (runtimeAnimations[index].animation.targetProperty === property) {\r\n                return runtimeAnimations[index].animation;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the runtime animation for a specific property\r\n     * @param property defines the property to look for\r\n     * @returns null or the runtime animation for the given property\r\n     */\r\n    public getRuntimeAnimationByTargetProperty(property: string): Nullable<RuntimeAnimation> {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            if (runtimeAnimations[index].animation.targetProperty === property) {\r\n                return runtimeAnimations[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Resets the animatable to its original state\r\n     */\r\n    public reset(): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].reset(true);\r\n        }\r\n\r\n        this._localDelayOffset = null;\r\n        this._pausedDelay = null;\r\n    }\r\n\r\n    /**\r\n     * Allows the animatable to blend with current running animations\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\r\n     * @param blendingSpeed defines the blending speed to use\r\n     */\r\n    public enableBlending(blendingSpeed: number): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].animation.enableBlending = true;\r\n            runtimeAnimations[index].animation.blendingSpeed = blendingSpeed;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disable animation blending\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\r\n     */\r\n    public disableBlending(): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].animation.enableBlending = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Jump directly to a given frame\r\n     * @param frame defines the frame to jump to\r\n     * @param useWeight defines whether the animation weight should be applied to the image to be jumped to (false by default)\r\n     */\r\n    public goToFrame(frame: number, useWeight = false): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        if (runtimeAnimations[0]) {\r\n            const fps = runtimeAnimations[0].animation.framePerSecond;\r\n            this._frameToSyncFromJump = this._frameToSyncFromJump ?? runtimeAnimations[0].currentFrame;\r\n            const delay = this.speedRatio === 0 ? 0 : (((frame - this._frameToSyncFromJump) / fps) * 1000) / this.speedRatio;\r\n            this._manualJumpDelay = -delay;\r\n        }\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].goToFrame(frame, useWeight ? this._weight : -1);\r\n        }\r\n\r\n        this._goToFrame = frame;\r\n    }\r\n\r\n    /**\r\n     * Returns true if the animations for this animatable are paused\r\n     */\r\n    public get paused() {\r\n        return this._paused;\r\n    }\r\n\r\n    /**\r\n     * Pause the animation\r\n     */\r\n    public pause(): void {\r\n        if (this._paused) {\r\n            return;\r\n        }\r\n        this._paused = true;\r\n    }\r\n\r\n    /**\r\n     * Restart the animation\r\n     */\r\n    public restart(): void {\r\n        this._paused = false;\r\n    }\r\n\r\n    private _raiseOnAnimationEnd() {\r\n        if (this.onAnimationEnd) {\r\n            this.onAnimationEnd();\r\n        }\r\n\r\n        this.onAnimationEndObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Stop and delete the current animation\r\n     * @param animationName defines a string used to only stop some of the runtime animations instead of all\r\n     * @param targetMask a function that determines if the animation should be stopped based on its target (all animations will be stopped if both this and animationName are empty)\r\n     * @param useGlobalSplice if true, the animatables will be removed by the caller of this function (false by default)\r\n     * @param skipOnAnimationEnd defines if the system should not raise onAnimationEnd. Default is false\r\n     */\r\n    public stop(animationName?: string, targetMask?: (target: any) => boolean, useGlobalSplice = false, skipOnAnimationEnd = false): void {\r\n        if (animationName || targetMask) {\r\n            const idx = this._scene._activeAnimatables.indexOf(this);\r\n\r\n            if (idx > -1) {\r\n                const runtimeAnimations = this._runtimeAnimations;\r\n\r\n                for (let index = runtimeAnimations.length - 1; index >= 0; index--) {\r\n                    const runtimeAnimation = runtimeAnimations[index];\r\n                    if (animationName && runtimeAnimation.animation.name != animationName) {\r\n                        continue;\r\n                    }\r\n                    if (targetMask && !targetMask(runtimeAnimation.target)) {\r\n                        continue;\r\n                    }\r\n\r\n                    runtimeAnimation.dispose();\r\n                    runtimeAnimations.splice(index, 1);\r\n                }\r\n\r\n                if (runtimeAnimations.length == 0) {\r\n                    if (!useGlobalSplice) {\r\n                        this._scene._activeAnimatables.splice(idx, 1);\r\n                    }\r\n                    if (!skipOnAnimationEnd) {\r\n                        this._raiseOnAnimationEnd();\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const index = this._scene._activeAnimatables.indexOf(this);\r\n\r\n            if (index > -1) {\r\n                if (!useGlobalSplice) {\r\n                    this._scene._activeAnimatables.splice(index, 1);\r\n                }\r\n                const runtimeAnimations = this._runtimeAnimations;\r\n\r\n                for (let index = 0; index < runtimeAnimations.length; index++) {\r\n                    runtimeAnimations[index].dispose();\r\n                }\r\n\r\n                this._runtimeAnimations.length = 0;\r\n\r\n                if (!skipOnAnimationEnd) {\r\n                    this._raiseOnAnimationEnd();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Wait asynchronously for the animation to end\r\n     * @returns a promise which will be fulfilled when the animation ends\r\n     */\r\n    public async waitAsync(): Promise<Animatable> {\r\n        return await new Promise((resolve) => {\r\n            this.onAnimationEndObservable.add(\r\n                () => {\r\n                    resolve(this);\r\n                },\r\n                undefined,\r\n                undefined,\r\n                this,\r\n                true\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _animate(delay: number): boolean {\r\n        if (this._paused) {\r\n            this.animationStarted = false;\r\n            if (this._pausedDelay === null) {\r\n                this._pausedDelay = delay;\r\n            }\r\n            return true;\r\n        }\r\n\r\n        if (this._localDelayOffset === null) {\r\n            this._localDelayOffset = delay;\r\n            this._pausedDelay = null;\r\n        } else if (this._pausedDelay !== null) {\r\n            this._localDelayOffset += delay - this._pausedDelay;\r\n            this._pausedDelay = null;\r\n        }\r\n\r\n        if (this._manualJumpDelay !== null) {\r\n            this._localDelayOffset += this.speedRatio < 0 ? -this._manualJumpDelay : this._manualJumpDelay;\r\n            this._manualJumpDelay = null;\r\n            this._frameToSyncFromJump = null;\r\n        }\r\n\r\n        this._goToFrame = null;\r\n\r\n        if (!Animatable.ProcessPausedAnimatables && this._weight === 0 && this._previousWeight === 0) {\r\n            // We consider that an animatable with a weight === 0 is \"actively\" paused\r\n            return true;\r\n        }\r\n\r\n        this._previousWeight = this._weight;\r\n\r\n        // Animating\r\n        let running = false;\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n        let index: number;\r\n\r\n        for (index = 0; index < runtimeAnimations.length; index++) {\r\n            const animation = runtimeAnimations[index];\r\n            const isRunning = animation.animate(delay - this._localDelayOffset, this.fromFrame, this.toFrame, this.loopAnimation, this._speedRatio, this._weight);\r\n            running = running || isRunning;\r\n        }\r\n\r\n        this.animationStarted = running;\r\n\r\n        if (!running) {\r\n            if (this.disposeOnEnd) {\r\n                // Remove from active animatables\r\n                index = this._scene._activeAnimatables.indexOf(this);\r\n                this._scene._activeAnimatables.splice(index, 1);\r\n\r\n                // Dispose all runtime animations\r\n                for (index = 0; index < runtimeAnimations.length; index++) {\r\n                    runtimeAnimations[index].dispose();\r\n                }\r\n            }\r\n\r\n            this._raiseOnAnimationEnd();\r\n\r\n            if (this.disposeOnEnd) {\r\n                this.onAnimationEnd = null;\r\n                this.onAnimationLoop = null;\r\n                this.onAnimationLoopObservable.clear();\r\n                this.onAnimationEndObservable.clear();\r\n            }\r\n        }\r\n\r\n        return running;\r\n    }\r\n}\r\n\r\n/** @internal */\r\nfunction ProcessLateAnimationBindingsForMatrices(holder: {\r\n    totalWeight: number;\r\n    totalAdditiveWeight: number;\r\n    animations: RuntimeAnimation[];\r\n    additiveAnimations: RuntimeAnimation[];\r\n    originalValue: Matrix;\r\n}): any {\r\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight === 0) {\r\n        return holder.originalValue;\r\n    }\r\n\r\n    let normalizer = 1.0;\r\n    const finalPosition = TmpVectors.Vector3[0];\r\n    const finalScaling = TmpVectors.Vector3[1];\r\n    const finalQuaternion = TmpVectors.Quaternion[0];\r\n    let startIndex = 0;\r\n    const originalAnimation = holder.animations[0];\r\n    const originalValue = holder.originalValue;\r\n\r\n    let scale = 1;\r\n    let skipOverride = false;\r\n    if (holder.totalWeight < 1.0) {\r\n        // We need to mix the original value in\r\n        scale = 1.0 - holder.totalWeight;\r\n        originalValue.decompose(finalScaling, finalQuaternion, finalPosition);\r\n    } else {\r\n        startIndex = 1;\r\n        // We need to normalize the weights\r\n        normalizer = holder.totalWeight;\r\n        scale = originalAnimation.weight / normalizer;\r\n        if (scale == 1) {\r\n            if (holder.totalAdditiveWeight) {\r\n                skipOverride = true;\r\n            } else {\r\n                return originalAnimation.currentValue;\r\n            }\r\n        }\r\n\r\n        originalAnimation.currentValue.decompose(finalScaling, finalQuaternion, finalPosition);\r\n    }\r\n\r\n    // Add up the override animations\r\n    if (!skipOverride) {\r\n        finalScaling.scaleInPlace(scale);\r\n        finalPosition.scaleInPlace(scale);\r\n        finalQuaternion.scaleInPlace(scale);\r\n\r\n        for (let animIndex = startIndex; animIndex < holder.animations.length; animIndex++) {\r\n            const runtimeAnimation = holder.animations[animIndex];\r\n            if (runtimeAnimation.weight === 0) {\r\n                continue;\r\n            }\r\n\r\n            scale = runtimeAnimation.weight / normalizer;\r\n            const currentPosition = TmpVectors.Vector3[2];\r\n            const currentScaling = TmpVectors.Vector3[3];\r\n            const currentQuaternion = TmpVectors.Quaternion[1];\r\n\r\n            runtimeAnimation.currentValue.decompose(currentScaling, currentQuaternion, currentPosition);\r\n\r\n            currentScaling.scaleAndAddToRef(scale, finalScaling);\r\n            currentQuaternion.scaleAndAddToRef(Quaternion.Dot(finalQuaternion, currentQuaternion) > 0 ? scale : -scale, finalQuaternion);\r\n            currentPosition.scaleAndAddToRef(scale, finalPosition);\r\n        }\r\n\r\n        finalQuaternion.normalize();\r\n    }\r\n\r\n    // Add up the additive animations\r\n    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\r\n        const runtimeAnimation = holder.additiveAnimations[animIndex];\r\n        if (runtimeAnimation.weight === 0) {\r\n            continue;\r\n        }\r\n\r\n        const currentPosition = TmpVectors.Vector3[2];\r\n        const currentScaling = TmpVectors.Vector3[3];\r\n        const currentQuaternion = TmpVectors.Quaternion[1];\r\n\r\n        runtimeAnimation.currentValue.decompose(currentScaling, currentQuaternion, currentPosition);\r\n        currentScaling.multiplyToRef(finalScaling, currentScaling);\r\n        Vector3.LerpToRef(finalScaling, currentScaling, runtimeAnimation.weight, finalScaling);\r\n        finalQuaternion.multiplyToRef(currentQuaternion, currentQuaternion);\r\n        Quaternion.SlerpToRef(finalQuaternion, currentQuaternion, runtimeAnimation.weight, finalQuaternion);\r\n        currentPosition.scaleAndAddToRef(runtimeAnimation.weight, finalPosition);\r\n    }\r\n\r\n    const workValue = originalAnimation ? originalAnimation._animationState.workValue : TmpVectors.Matrix[0].clone();\r\n    Matrix.ComposeToRef(finalScaling, finalQuaternion, finalPosition, workValue);\r\n    return workValue;\r\n}\r\n\r\n/** @internal */\r\nfunction ProcessLateAnimationBindingsForQuaternions(\r\n    holder: {\r\n        totalWeight: number;\r\n        totalAdditiveWeight: number;\r\n        animations: RuntimeAnimation[];\r\n        additiveAnimations: RuntimeAnimation[];\r\n        originalValue: Quaternion;\r\n    },\r\n    refQuaternion: Quaternion\r\n): Quaternion {\r\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight === 0) {\r\n        return refQuaternion;\r\n    }\r\n\r\n    const originalAnimation = holder.animations[0];\r\n    const originalValue = holder.originalValue;\r\n    let cumulativeQuaternion = refQuaternion;\r\n\r\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight > 0) {\r\n        cumulativeQuaternion.copyFrom(originalValue);\r\n    } else if (holder.animations.length === 1) {\r\n        Quaternion.SlerpToRef(originalValue, originalAnimation.currentValue, Math.min(1.0, holder.totalWeight), cumulativeQuaternion);\r\n\r\n        if (holder.totalAdditiveWeight === 0) {\r\n            return cumulativeQuaternion;\r\n        }\r\n    } else if (holder.animations.length > 1) {\r\n        // Add up the override animations\r\n        let normalizer = 1.0;\r\n        let quaternions: Array<Quaternion>;\r\n        let weights: Array<number>;\r\n\r\n        if (holder.totalWeight < 1.0) {\r\n            const scale = 1.0 - holder.totalWeight;\r\n\r\n            quaternions = [];\r\n            weights = [];\r\n\r\n            quaternions.push(originalValue);\r\n            weights.push(scale);\r\n        } else {\r\n            if (holder.animations.length === 2) {\r\n                // Slerp as soon as we can\r\n                Quaternion.SlerpToRef(holder.animations[0].currentValue, holder.animations[1].currentValue, holder.animations[1].weight / holder.totalWeight, refQuaternion);\r\n\r\n                if (holder.totalAdditiveWeight === 0) {\r\n                    return refQuaternion;\r\n                }\r\n            }\r\n\r\n            quaternions = [];\r\n            weights = [];\r\n            normalizer = holder.totalWeight;\r\n        }\r\n\r\n        for (let animIndex = 0; animIndex < holder.animations.length; animIndex++) {\r\n            const runtimeAnimation = holder.animations[animIndex];\r\n            quaternions.push(runtimeAnimation.currentValue);\r\n            weights.push(runtimeAnimation.weight / normalizer);\r\n        }\r\n\r\n        // https://gamedev.stackexchange.com/questions/62354/method-for-interpolation-between-3-quaternions\r\n\r\n        let cumulativeAmount = 0;\r\n        for (let index = 0; index < quaternions.length; ) {\r\n            if (!index) {\r\n                Quaternion.SlerpToRef(quaternions[index], quaternions[index + 1], weights[index + 1] / (weights[index] + weights[index + 1]), refQuaternion);\r\n                cumulativeQuaternion = refQuaternion;\r\n                cumulativeAmount = weights[index] + weights[index + 1];\r\n                index += 2;\r\n                continue;\r\n            }\r\n            cumulativeAmount += weights[index];\r\n            Quaternion.SlerpToRef(cumulativeQuaternion, quaternions[index], weights[index] / cumulativeAmount, cumulativeQuaternion);\r\n            index++;\r\n        }\r\n    }\r\n\r\n    // Add up the additive animations\r\n    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\r\n        const runtimeAnimation = holder.additiveAnimations[animIndex];\r\n        if (runtimeAnimation.weight === 0) {\r\n            continue;\r\n        }\r\n\r\n        cumulativeQuaternion.multiplyToRef(runtimeAnimation.currentValue, TmpVectors.Quaternion[0]);\r\n        Quaternion.SlerpToRef(cumulativeQuaternion, TmpVectors.Quaternion[0], runtimeAnimation.weight, cumulativeQuaternion);\r\n    }\r\n\r\n    return cumulativeQuaternion;\r\n}\r\n\r\n/** @internal */\r\nfunction ProcessLateAnimationBindings(scene: Scene): void {\r\n    if (!scene._registeredForLateAnimationBindings.length) {\r\n        return;\r\n    }\r\n    for (let index = 0; index < scene._registeredForLateAnimationBindings.length; index++) {\r\n        const target = scene._registeredForLateAnimationBindings.data[index];\r\n\r\n        for (const path in target._lateAnimationHolders) {\r\n            const holder = target._lateAnimationHolders[path];\r\n            const originalAnimation: RuntimeAnimation = holder.animations[0];\r\n            const originalValue = holder.originalValue;\r\n            if (originalValue === undefined || originalValue === null) {\r\n                continue;\r\n            }\r\n            const matrixDecomposeMode = Animation.AllowMatrixDecomposeForInterpolation && originalValue.m; // ie. data is matrix\r\n\r\n            let finalValue: any = target[path];\r\n            if (matrixDecomposeMode) {\r\n                finalValue = ProcessLateAnimationBindingsForMatrices(holder);\r\n            } else {\r\n                const quaternionMode = originalValue.w !== undefined;\r\n                if (quaternionMode) {\r\n                    finalValue = ProcessLateAnimationBindingsForQuaternions(holder, finalValue || Quaternion.Identity());\r\n                } else {\r\n                    let startIndex = 0;\r\n                    let normalizer = 1.0;\r\n\r\n                    const originalAnimationIsLoopRelativeFromCurrent =\r\n                        originalAnimation && originalAnimation._animationState.loopMode === Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT;\r\n\r\n                    if (holder.totalWeight < 1.0) {\r\n                        // We need to mix the original value in\r\n                        if (originalAnimationIsLoopRelativeFromCurrent) {\r\n                            finalValue = originalValue.clone ? originalValue.clone() : originalValue;\r\n                        } else if (originalAnimation && originalValue.scale) {\r\n                            finalValue = originalValue.scale(1.0 - holder.totalWeight);\r\n                        } else if (originalAnimation) {\r\n                            finalValue = originalValue * (1.0 - holder.totalWeight);\r\n                        } else if (originalValue.clone) {\r\n                            finalValue = originalValue.clone();\r\n                        } else {\r\n                            finalValue = originalValue;\r\n                        }\r\n                    } else if (originalAnimation) {\r\n                        // We need to normalize the weights\r\n                        normalizer = holder.totalWeight;\r\n                        const scale = originalAnimation.weight / normalizer;\r\n                        if (scale !== 1) {\r\n                            if (originalAnimation.currentValue.scale) {\r\n                                finalValue = originalAnimation.currentValue.scale(scale);\r\n                            } else {\r\n                                finalValue = originalAnimation.currentValue * scale;\r\n                            }\r\n                        } else {\r\n                            finalValue = originalAnimation.currentValue;\r\n                        }\r\n\r\n                        if (originalAnimationIsLoopRelativeFromCurrent) {\r\n                            if (finalValue.addToRef) {\r\n                                finalValue.addToRef(originalValue, finalValue);\r\n                            } else {\r\n                                finalValue += originalValue;\r\n                            }\r\n                        }\r\n\r\n                        startIndex = 1;\r\n                    }\r\n\r\n                    // Add up the override animations\r\n                    for (let animIndex = startIndex; animIndex < holder.animations.length; animIndex++) {\r\n                        const runtimeAnimation = holder.animations[animIndex];\r\n                        const scale = runtimeAnimation.weight / normalizer;\r\n\r\n                        if (!scale) {\r\n                            continue;\r\n                        } else if (runtimeAnimation.currentValue.scaleAndAddToRef) {\r\n                            runtimeAnimation.currentValue.scaleAndAddToRef(scale, finalValue);\r\n                        } else {\r\n                            finalValue += runtimeAnimation.currentValue * scale;\r\n                        }\r\n                    }\r\n\r\n                    // Add up the additive animations\r\n                    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\r\n                        const runtimeAnimation = holder.additiveAnimations[animIndex];\r\n                        const scale: number = runtimeAnimation.weight;\r\n\r\n                        if (!scale) {\r\n                            continue;\r\n                        } else if (runtimeAnimation.currentValue.scaleAndAddToRef) {\r\n                            runtimeAnimation.currentValue.scaleAndAddToRef(scale, finalValue);\r\n                        } else {\r\n                            finalValue += runtimeAnimation.currentValue * scale;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            target[path] = finalValue;\r\n        }\r\n\r\n        target._lateAnimationHolders = {};\r\n    }\r\n    scene._registeredForLateAnimationBindings.reset();\r\n}\r\n\r\n/** @internal */\r\nexport function RegisterTargetForLateAnimationBinding(scene: Scene, runtimeAnimation: RuntimeAnimation, originalValue: any): void {\r\n    const target = runtimeAnimation.target;\r\n    scene._registeredForLateAnimationBindings.pushNoDuplicate(target);\r\n\r\n    if (!target._lateAnimationHolders) {\r\n        target._lateAnimationHolders = {};\r\n    }\r\n\r\n    if (!target._lateAnimationHolders[runtimeAnimation.targetPath]) {\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath] = {\r\n            totalWeight: 0,\r\n            totalAdditiveWeight: 0,\r\n            animations: [],\r\n            additiveAnimations: [],\r\n            originalValue: originalValue,\r\n        };\r\n    }\r\n\r\n    if (runtimeAnimation.isAdditive) {\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].additiveAnimations.push(runtimeAnimation);\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].totalAdditiveWeight += runtimeAnimation.weight;\r\n    } else {\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].animations.push(runtimeAnimation);\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].totalWeight += runtimeAnimation.weight;\r\n    }\r\n}\r\n\r\n/**\r\n * Initialize all the inter dependecies between the animations and Scene and Bone\r\n * @param sceneClass defines the scene prototype to use\r\n * @param boneClass defines the bone prototype to use\r\n */\r\nexport function AddAnimationExtensions(sceneClass: typeof Scene, boneClass: typeof Bone): void {\r\n    if (boneClass) {\r\n        boneClass.prototype.copyAnimationRange = function (\r\n            source: Bone,\r\n            rangeName: string,\r\n            frameOffset: number,\r\n            rescaleAsRequired = false,\r\n            skelDimensionsRatio: Nullable<Vector3> = null\r\n        ): boolean {\r\n            // all animation may be coming from a library skeleton, so may need to create animation\r\n            if (this.animations.length === 0) {\r\n                this.animations.push(new Animation(this.name, \"_matrix\", source.animations[0].framePerSecond, Animation.ANIMATIONTYPE_MATRIX, 0));\r\n                this.animations[0].setKeys([]);\r\n            }\r\n\r\n            // get animation info / verify there is such a range from the source bone\r\n            const sourceRange = source.animations[0].getRange(rangeName);\r\n            if (!sourceRange) {\r\n                return false;\r\n            }\r\n            const from = sourceRange.from;\r\n            const to = sourceRange.to;\r\n            const sourceKeys = source.animations[0].getKeys();\r\n\r\n            // rescaling prep\r\n            const sourceBoneLength = source.length;\r\n            const sourceParent = source.getParent();\r\n            const parent = this.getParent();\r\n            const parentScalingReqd = rescaleAsRequired && sourceParent && sourceBoneLength && this.length && sourceBoneLength !== this.length;\r\n            const parentRatio = parentScalingReqd && parent && sourceParent ? parent.length / sourceParent.length : 1;\r\n\r\n            const dimensionsScalingReqd =\r\n                rescaleAsRequired && !parent && skelDimensionsRatio && (skelDimensionsRatio.x !== 1 || skelDimensionsRatio.y !== 1 || skelDimensionsRatio.z !== 1);\r\n\r\n            const destKeys = this.animations[0].getKeys();\r\n\r\n            // loop vars declaration\r\n            let orig: { frame: number; value: Matrix };\r\n            let origTranslation: Vector3;\r\n            let mat: Matrix;\r\n\r\n            for (let key = 0, nKeys = sourceKeys.length; key < nKeys; key++) {\r\n                orig = sourceKeys[key];\r\n                if (orig.frame >= from && orig.frame <= to) {\r\n                    if (rescaleAsRequired) {\r\n                        mat = orig.value.clone();\r\n\r\n                        // scale based on parent ratio, when bone has parent\r\n                        if (parentScalingReqd) {\r\n                            origTranslation = mat.getTranslation();\r\n                            mat.setTranslation(origTranslation.scaleInPlace(parentRatio));\r\n\r\n                            // scale based on skeleton dimension ratio when root bone, and value is passed\r\n                        } else if (dimensionsScalingReqd && skelDimensionsRatio) {\r\n                            origTranslation = mat.getTranslation();\r\n                            mat.setTranslation(origTranslation.multiplyInPlace(skelDimensionsRatio));\r\n\r\n                            // use original when root bone, and no data for skelDimensionsRatio\r\n                        } else {\r\n                            mat = orig.value;\r\n                        }\r\n                    } else {\r\n                        mat = orig.value;\r\n                    }\r\n                    destKeys.push({ frame: orig.frame + frameOffset, value: mat });\r\n                }\r\n            }\r\n            this.animations[0].createRange(rangeName, from + frameOffset, to + frameOffset);\r\n            return true;\r\n        };\r\n    }\r\n\r\n    if (!sceneClass) {\r\n        return;\r\n    }\r\n\r\n    sceneClass.prototype._animate = function (customDeltaTime?: number): void {\r\n        if (!this.animationsEnabled) {\r\n            return;\r\n        }\r\n\r\n        // Getting time\r\n        const now = PrecisionDate.Now;\r\n        if (!this._animationTimeLast) {\r\n            if (this._pendingData.length > 0) {\r\n                return;\r\n            }\r\n            this._animationTimeLast = now;\r\n        }\r\n\r\n        this.deltaTime = customDeltaTime !== undefined ? customDeltaTime : this.useConstantAnimationDeltaTime ? 16.0 : (now - this._animationTimeLast) * this.animationTimeScale;\r\n        this._animationTimeLast = now;\r\n\r\n        const animatables = this._activeAnimatables;\r\n        if (animatables.length === 0) {\r\n            return;\r\n        }\r\n\r\n        this._animationTime += this.deltaTime;\r\n        const animationTime = this._animationTime;\r\n\r\n        for (let index = 0; index < animatables.length; index++) {\r\n            const animatable = animatables[index];\r\n\r\n            if (!animatable._animate(animationTime) && animatable.disposeOnEnd) {\r\n                index--; // Array was updated\r\n            }\r\n        }\r\n\r\n        // Late animation bindings\r\n        ProcessLateAnimationBindings(this);\r\n    };\r\n\r\n    sceneClass.prototype.sortActiveAnimatables = function (): void {\r\n        this._activeAnimatables.sort((a, b) => {\r\n            return a.playOrder - b.playOrder;\r\n        });\r\n    };\r\n\r\n    sceneClass.prototype.beginWeightedAnimation = function (\r\n        target: any,\r\n        from: number,\r\n        to: number,\r\n        weight = 1.0,\r\n        loop?: boolean,\r\n        speedRatio: number = 1.0,\r\n        onAnimationEnd?: () => void,\r\n        animatable?: Animatable,\r\n        targetMask?: (target: any) => boolean,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable {\r\n        const returnedAnimatable = this.beginAnimation(target, from, to, loop, speedRatio, onAnimationEnd, animatable, false, targetMask, onAnimationLoop, isAdditive);\r\n        returnedAnimatable.weight = weight;\r\n\r\n        return returnedAnimatable;\r\n    };\r\n\r\n    sceneClass.prototype.beginAnimation = function (\r\n        target: any,\r\n        from: number,\r\n        to: number,\r\n        loop?: boolean,\r\n        speedRatio: number = 1.0,\r\n        onAnimationEnd?: () => void,\r\n        animatable?: Animatable,\r\n        stopCurrent = true,\r\n        targetMask?: (target: any) => boolean,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable {\r\n        // get speed speedRatio, to and from, based on the sign and value(s)\r\n        if (speedRatio < 0) {\r\n            const tmp = from;\r\n            from = to;\r\n            to = tmp;\r\n            speedRatio = -speedRatio;\r\n        }\r\n        // if from > to switch speed ratio\r\n        if (from > to) {\r\n            speedRatio = -speedRatio;\r\n        }\r\n        if (stopCurrent) {\r\n            this.stopAnimation(target, undefined, targetMask);\r\n        }\r\n\r\n        if (!animatable) {\r\n            animatable = new Animatable(this, target, from, to, loop, speedRatio, onAnimationEnd, undefined, onAnimationLoop, isAdditive);\r\n        }\r\n\r\n        const shouldRunTargetAnimations = targetMask ? targetMask(target) : true;\r\n        // Local animations\r\n        if (target.animations && shouldRunTargetAnimations) {\r\n            animatable.appendAnimations(target, target.animations);\r\n        }\r\n\r\n        // Children animations\r\n        if (target.getAnimatables) {\r\n            const animatables = target.getAnimatables();\r\n            for (let index = 0; index < animatables.length; index++) {\r\n                this.beginAnimation(animatables[index], from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, onAnimationLoop);\r\n            }\r\n        }\r\n\r\n        animatable.reset();\r\n\r\n        return animatable;\r\n    };\r\n\r\n    sceneClass.prototype.beginHierarchyAnimation = function (\r\n        target: any,\r\n        directDescendantsOnly: boolean,\r\n        from: number,\r\n        to: number,\r\n        loop?: boolean,\r\n        speedRatio: number = 1.0,\r\n        onAnimationEnd?: () => void,\r\n        animatable?: Animatable,\r\n        stopCurrent = true,\r\n        targetMask?: (target: any) => boolean,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable[] {\r\n        const children = target.getDescendants(directDescendantsOnly);\r\n\r\n        const result = [];\r\n        result.push(this.beginAnimation(target, from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, undefined, isAdditive));\r\n        for (const child of children) {\r\n            result.push(this.beginAnimation(child, from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, undefined, isAdditive));\r\n        }\r\n\r\n        return result;\r\n    };\r\n\r\n    sceneClass.prototype.beginDirectAnimation = function (\r\n        target: any,\r\n        animations: Animation[],\r\n        from: number,\r\n        to: number,\r\n        loop?: boolean,\r\n        speedRatio: number = 1.0,\r\n        onAnimationEnd?: () => void,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable {\r\n        // get speed speedRatio, to and from, based on the sign and value(s)\r\n        if (speedRatio < 0) {\r\n            const tmp = from;\r\n            from = to;\r\n            to = tmp;\r\n            speedRatio = -speedRatio;\r\n        }\r\n        // if from > to switch speed ratio\r\n        if (from > to) {\r\n            speedRatio = -speedRatio;\r\n        }\r\n        const animatable = new Animatable(this, target, from, to, loop, speedRatio, onAnimationEnd, animations, onAnimationLoop, isAdditive);\r\n\r\n        return animatable;\r\n    };\r\n\r\n    sceneClass.prototype.beginDirectHierarchyAnimation = function (\r\n        target: Node,\r\n        directDescendantsOnly: boolean,\r\n        animations: Animation[],\r\n        from: number,\r\n        to: number,\r\n        loop?: boolean,\r\n        speedRatio?: number,\r\n        onAnimationEnd?: () => void,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable[] {\r\n        const children = target.getDescendants(directDescendantsOnly);\r\n\r\n        const result = [];\r\n        result.push(this.beginDirectAnimation(target, animations, from, to, loop, speedRatio, onAnimationEnd, onAnimationLoop, isAdditive));\r\n        for (const child of children) {\r\n            result.push(this.beginDirectAnimation(child, animations, from, to, loop, speedRatio, onAnimationEnd, onAnimationLoop, isAdditive));\r\n        }\r\n\r\n        return result;\r\n    };\r\n\r\n    sceneClass.prototype.getAnimatableByTarget = function (target: any): Nullable<Animatable> {\r\n        for (let index = 0; index < this._activeAnimatables.length; index++) {\r\n            if (this._activeAnimatables[index].target === target) {\r\n                return this._activeAnimatables[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    };\r\n\r\n    sceneClass.prototype.getAllAnimatablesByTarget = function (target: any): Array<Animatable> {\r\n        const result = [];\r\n        for (let index = 0; index < this._activeAnimatables.length; index++) {\r\n            if (this._activeAnimatables[index].target === target) {\r\n                result.push(this._activeAnimatables[index]);\r\n            }\r\n        }\r\n\r\n        return result;\r\n    };\r\n\r\n    sceneClass.prototype.stopAnimation = function (target: any, animationName?: string, targetMask?: (target: any) => boolean): void {\r\n        const animatables = this.getAllAnimatablesByTarget(target);\r\n\r\n        for (const animatable of animatables) {\r\n            animatable.stop(animationName, targetMask);\r\n        }\r\n    };\r\n\r\n    sceneClass.prototype.stopAllAnimations = function (): void {\r\n        if (this._activeAnimatables) {\r\n            for (let i = 0; i < this._activeAnimatables.length; i++) {\r\n                this._activeAnimatables[i].stop(undefined, undefined, true);\r\n            }\r\n            this._activeAnimatables.length = 0;\r\n        }\r\n\r\n        for (const group of this.animationGroups) {\r\n            group.stop();\r\n        }\r\n    };\r\n}\r\n"]}