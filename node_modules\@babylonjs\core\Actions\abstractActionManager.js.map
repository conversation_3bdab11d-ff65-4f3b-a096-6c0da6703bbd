{"version": 3, "file": "abstractActionManager.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Actions/abstractActionManager.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAGjD;;;;GAIG;AACH,MAAM,OAAgB,qBAAqB;IAA3C;QAII,iDAAiD;QAC1C,gBAAW,GAAW,EAAE,CAAC;QAEhC,+BAA+B;QACxB,YAAO,GAAc,EAAE,CAAC;QAE/B;;WAEG;QACI,gBAAW,GAAG,KAAK,CAAC;QAE3B;;WAEG;QACI,uBAAkB,GAAG,IAAI,CAAC;IAgHrC,CAAC;IA3CG;;QAEI;IACG,MAAM,KAAK,WAAW;QACzB,KAAK,MAAM,CAAC,IAAI,qBAAqB,CAAC,QAAQ,EAAE,CAAC;YAC7C,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;gBAC1E,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;QAEI;IACG,MAAM,KAAK,eAAe;QAC7B,KAAK,MAAM,CAAC,IAAI,qBAAqB,CAAC,QAAQ,EAAE,CAAC;YAC7C,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;gBAC1E,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,MAAM,IAAI,SAAS,CAAC,oBAAoB,IAAI,MAAM,IAAI,SAAS,CAAC,sBAAsB,EAAE,CAAC;oBACzF,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;QAII;IACG,MAAM,CAAC,kBAAkB,CAAC,OAAe;QAC5C,KAAK,MAAM,CAAC,IAAI,qBAAqB,CAAC,QAAQ,EAAE,CAAC;YAC7C,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC;gBAC1E,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC3B,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;oBACrB,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;;AAhID,uCAAuC;AACzB,8BAAQ,GAA8B,EAAE,AAAhC,CAAiC", "sourcesContent": ["import type { IDisposable } from \"../scene\";\r\nimport type { IActionEvent } from \"./actionEvent\";\r\nimport type { IAction } from \"./action\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { Nullable } from \"../types\";\r\n\r\n/**\r\n * Abstract class used to decouple action Manager from scene and meshes.\r\n * Do not instantiate.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport abstract class AbstractActionManager implements IDisposable {\r\n    /** Gets the list of active triggers */\r\n    public static Triggers: { [key: string]: number } = {};\r\n\r\n    /** Gets the cursor to use when hovering items */\r\n    public hoverCursor: string = \"\";\r\n\r\n    /** Gets the list of actions */\r\n    public actions: IAction[] = [];\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that the manager is recursive meaning that it can trigger action from children\r\n     */\r\n    public isRecursive = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if this ActionManager should be disposed once the last Mesh using it is disposed\r\n     */\r\n    public disposeWhenUnowned = true;\r\n\r\n    /**\r\n     * Releases all associated resources\r\n     */\r\n    public abstract dispose(): void;\r\n\r\n    /**\r\n     * Does this action manager has pointer triggers\r\n     */\r\n    public abstract get hasPointerTriggers(): boolean;\r\n\r\n    /**\r\n     * Does this action manager has pick triggers\r\n     */\r\n    public abstract get hasPickTriggers(): boolean;\r\n\r\n    /**\r\n     * Process a specific trigger\r\n     * @param trigger defines the trigger to process\r\n     * @param evt defines the event details to be processed\r\n     */\r\n    public abstract processTrigger(trigger: number, evt?: IActionEvent): void;\r\n\r\n    /**\r\n     * Does this action manager handles actions of any of the given triggers\r\n     * @param triggers defines the triggers to be tested\r\n     * @returns a boolean indicating whether one (or more) of the triggers is handled\r\n     */\r\n    public abstract hasSpecificTriggers(triggers: number[]): boolean;\r\n\r\n    /**\r\n     * Does this action manager handles actions of any of the given triggers. This function takes two arguments for\r\n     * speed.\r\n     * @param triggerA defines the trigger to be tested\r\n     * @param triggerB defines the trigger to be tested\r\n     * @returns a boolean indicating whether one (or more) of the triggers is handled\r\n     */\r\n    public abstract hasSpecificTriggers2(triggerA: number, triggerB: number): boolean;\r\n\r\n    /**\r\n     * Does this action manager handles actions of a given trigger\r\n     * @param trigger defines the trigger to be tested\r\n     * @param parameterPredicate defines an optional predicate to filter triggers by parameter\r\n     * @returns whether the trigger is handled\r\n     */\r\n    public abstract hasSpecificTrigger(trigger: number, parameterPredicate?: (parameter: any) => boolean): boolean;\r\n\r\n    /**\r\n     * Serialize this manager to a JSON object\r\n     * @param name defines the property name to store this manager\r\n     * @returns a JSON representation of this manager\r\n     */\r\n    public abstract serialize(name: string): any;\r\n\r\n    /**\r\n     * Registers an action to this action manager\r\n     * @param action defines the action to be registered\r\n     * @returns the action amended (prepared) after registration\r\n     */\r\n    public abstract registerAction(action: IAction): Nullable<IAction>;\r\n\r\n    /**\r\n     * Unregisters an action to this action manager\r\n     * @param action defines the action to be unregistered\r\n     * @returns a boolean indicating whether the action has been unregistered\r\n     */\r\n    public abstract unregisterAction(action: IAction): boolean;\r\n\r\n    /**\r\n     * Does exist one action manager with at least one trigger\r\n     **/\r\n    public static get HasTriggers(): boolean {\r\n        for (const t in AbstractActionManager.Triggers) {\r\n            if (Object.prototype.hasOwnProperty.call(AbstractActionManager.Triggers, t)) {\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Does exist one action manager with at least one pick trigger\r\n     **/\r\n    public static get HasPickTriggers(): boolean {\r\n        for (const t in AbstractActionManager.Triggers) {\r\n            if (Object.prototype.hasOwnProperty.call(AbstractActionManager.Triggers, t)) {\r\n                const tAsInt = parseInt(t);\r\n                if (tAsInt >= Constants.ACTION_OnPickTrigger && tAsInt <= Constants.ACTION_OnPickUpTrigger) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Does exist one action manager that handles actions of a given trigger\r\n     * @param trigger defines the trigger to be tested\r\n     * @returns a boolean indicating whether the trigger is handled by at least one action manager\r\n     **/\r\n    public static HasSpecificTrigger(trigger: number): boolean {\r\n        for (const t in AbstractActionManager.Triggers) {\r\n            if (Object.prototype.hasOwnProperty.call(AbstractActionManager.Triggers, t)) {\r\n                const tAsInt = parseInt(t);\r\n                if (tAsInt === trigger) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n}\r\n"]}