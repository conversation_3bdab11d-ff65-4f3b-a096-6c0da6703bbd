(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Defines the kind of connection point for node based material
 */ __turbopack_context__.s({
    "NodeMaterialBlockConnectionPointTypes": ()=>NodeMaterialBlockConnectionPointTypes
});
var NodeMaterialBlockConnectionPointTypes;
(function(NodeMaterialBlockConnectionPointTypes) {
    /** Float */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["Float"] = 1] = "Float";
    /** Int */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["Int"] = 2] = "Int";
    /** Vector2 */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["Vector2"] = 4] = "Vector2";
    /** Vector3 */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["Vector3"] = 8] = "Vector3";
    /** Vector4 */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["Vector4"] = 16] = "Vector4";
    /** Color3 */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["Color3"] = 32] = "Color3";
    /** Color4 */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["Color4"] = 64] = "Color4";
    /** Matrix */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["Matrix"] = 128] = "Matrix";
    /** Custom object */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["Object"] = 256] = "Object";
    /** Detect type based on connection */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["AutoDetect"] = 1024] = "AutoDetect";
    /** Output type that will be defined by input type */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["BasedOnInput"] = 2048] = "BasedOnInput";
    /** Bitmask of all types */ NodeMaterialBlockConnectionPointTypes[NodeMaterialBlockConnectionPointTypes["All"] = 4095] = "All";
})(NodeMaterialBlockConnectionPointTypes || (NodeMaterialBlockConnectionPointTypes = {})); //# sourceMappingURL=nodeMaterialBlockConnectionPointTypes.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Enum used to define the target of a block
 */ __turbopack_context__.s({
    "NodeMaterialBlockTargets": ()=>NodeMaterialBlockTargets
});
var NodeMaterialBlockTargets;
(function(NodeMaterialBlockTargets) {
    /** Vertex shader */ NodeMaterialBlockTargets[NodeMaterialBlockTargets["Vertex"] = 1] = "Vertex";
    /** Fragment shader */ NodeMaterialBlockTargets[NodeMaterialBlockTargets["Fragment"] = 2] = "Fragment";
    /** Neutral */ NodeMaterialBlockTargets[NodeMaterialBlockTargets["Neutral"] = 4] = "Neutral";
    /** Vertex and Fragment */ NodeMaterialBlockTargets[NodeMaterialBlockTargets["VertexAndFragment"] = 3] = "VertexAndFragment";
})(NodeMaterialBlockTargets || (NodeMaterialBlockTargets = {})); //# sourceMappingURL=nodeMaterialBlockTargets.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialModes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Enum used to define the material modes
 */ __turbopack_context__.s({
    "NodeMaterialModes": ()=>NodeMaterialModes
});
var NodeMaterialModes;
(function(NodeMaterialModes) {
    /** Regular material */ NodeMaterialModes[NodeMaterialModes["Material"] = 0] = "Material";
    /** For post process */ NodeMaterialModes[NodeMaterialModes["PostProcess"] = 1] = "PostProcess";
    /** For particle system */ NodeMaterialModes[NodeMaterialModes["Particle"] = 2] = "Particle";
    /** For procedural texture */ NodeMaterialModes[NodeMaterialModes["ProceduralTexture"] = 3] = "ProceduralTexture";
    /** For gaussian splatting */ NodeMaterialModes[NodeMaterialModes["GaussianSplatting"] = 4] = "GaussianSplatting";
    /** For SFE */ NodeMaterialModes[NodeMaterialModes["SFE"] = 5] = "SFE";
})(NodeMaterialModes || (NodeMaterialModes = {})); //# sourceMappingURL=nodeMaterialModes.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialSystemValues.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Enum used to define system values e.g. values automatically provided by the system
 */ __turbopack_context__.s({
    "NodeMaterialSystemValues": ()=>NodeMaterialSystemValues
});
var NodeMaterialSystemValues;
(function(NodeMaterialSystemValues) {
    /** World */ NodeMaterialSystemValues[NodeMaterialSystemValues["World"] = 1] = "World";
    /** View */ NodeMaterialSystemValues[NodeMaterialSystemValues["View"] = 2] = "View";
    /** Projection */ NodeMaterialSystemValues[NodeMaterialSystemValues["Projection"] = 3] = "Projection";
    /** ViewProjection */ NodeMaterialSystemValues[NodeMaterialSystemValues["ViewProjection"] = 4] = "ViewProjection";
    /** WorldView */ NodeMaterialSystemValues[NodeMaterialSystemValues["WorldView"] = 5] = "WorldView";
    /** WorldViewProjection */ NodeMaterialSystemValues[NodeMaterialSystemValues["WorldViewProjection"] = 6] = "WorldViewProjection";
    /** CameraPosition */ NodeMaterialSystemValues[NodeMaterialSystemValues["CameraPosition"] = 7] = "CameraPosition";
    /** Fog Color */ NodeMaterialSystemValues[NodeMaterialSystemValues["FogColor"] = 8] = "FogColor";
    /** Delta time */ NodeMaterialSystemValues[NodeMaterialSystemValues["DeltaTime"] = 9] = "DeltaTime";
    /** Camera parameters */ NodeMaterialSystemValues[NodeMaterialSystemValues["CameraParameters"] = 10] = "CameraParameters";
    /** Material alpha */ NodeMaterialSystemValues[NodeMaterialSystemValues["MaterialAlpha"] = 11] = "MaterialAlpha";
})(NodeMaterialSystemValues || (NodeMaterialSystemValues = {})); //# sourceMappingURL=nodeMaterialSystemValues.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointMode.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Enum defining the mode of a NodeMaterialBlockConnectionPoint
 */ __turbopack_context__.s({
    "NodeMaterialBlockConnectionPointMode": ()=>NodeMaterialBlockConnectionPointMode
});
var NodeMaterialBlockConnectionPointMode;
(function(NodeMaterialBlockConnectionPointMode) {
    /** Value is an uniform */ NodeMaterialBlockConnectionPointMode[NodeMaterialBlockConnectionPointMode["Uniform"] = 0] = "Uniform";
    /** Value is a mesh attribute */ NodeMaterialBlockConnectionPointMode[NodeMaterialBlockConnectionPointMode["Attribute"] = 1] = "Attribute";
    /** Value is a varying between vertex and fragment shaders */ NodeMaterialBlockConnectionPointMode[NodeMaterialBlockConnectionPointMode["Varying"] = 2] = "Varying";
    /** Mode is undefined */ NodeMaterialBlockConnectionPointMode[NodeMaterialBlockConnectionPointMode["Undefined"] = 3] = "Undefined";
})(NodeMaterialBlockConnectionPointMode || (NodeMaterialBlockConnectionPointMode = {})); //# sourceMappingURL=nodeMaterialBlockConnectionPointMode.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Enums/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointMode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointMode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialSystemValues.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialModes.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
;
;
;
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Enums/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointMode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointMode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialSystemValues.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialModes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBuildState.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "NodeMaterialBuildState": ()=>NodeMaterialBuildState
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/shaderStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$Processors$2f$shaderProcessor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/Processors/shaderProcessor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$WebGL$2f$webGLShaderProcessors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/WebGL/webGLShaderProcessors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/logger.js [app-client] (ecmascript)");
;
;
;
;
;
;
class NodeMaterialBuildState {
    /**
     * Gets the current shader language to use
     */ get shaderLanguage() {
        return this.sharedData.nodeMaterial.shaderLanguage;
    }
    /** Gets suffix to add behind type casting */ get fSuffix() {
        return this.shaderLanguage === 1 /* ShaderLanguage.WGSL */  ? "f" : "";
    }
    /**
     * Returns the processed, compiled shader code
     * @param defines defines to use for the shader processing
     * @returns the raw shader code used by the engine
     */ async getProcessedShaderAsync(defines) {
        if (!this._builtCompilationString) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Error("getProcessedShaderAsync: Shader not built yet.");
            return "";
        }
        const engine = this.sharedData.nodeMaterial.getScene().getEngine();
        const options = {
            defines: defines.split("\n"),
            indexParameters: undefined,
            isFragment: this.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment,
            shouldUseHighPrecisionShader: engine._shouldUseHighPrecisionShader,
            processor: engine._getShaderProcessor(this.shaderLanguage),
            supportsUniformBuffers: engine.supportsUniformBuffers,
            shadersRepository: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].GetShadersRepository(this.shaderLanguage),
            includesShadersStore: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].GetIncludesShadersStore(this.shaderLanguage),
            version: (engine.version * 100).toString(),
            platformName: engine.shaderPlatformName,
            processingContext: null,
            isNDCHalfZRange: engine.isNDCHalfZRange,
            useReverseDepthBuffer: engine.useReverseDepthBuffer
        };
        // Export WebGL2 shaders with WebGL1 syntax for max compatibility
        if (!engine.isWebGPU && engine.version > 1.0) {
            options.processor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$WebGL$2f$webGLShaderProcessors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WebGLShaderProcessor"]();
        }
        return await new Promise((resolve)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$Processors$2f$shaderProcessor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Process"])(this._builtCompilationString, options, (migratedCode, _)=>{
                resolve(migratedCode);
            }, engine);
        });
    }
    /**
     * Finalize the compilation strings
     * @param state defines the current compilation state
     */ finalize(state) {
        const emitComments = state.sharedData.emitComments;
        const isFragmentMode = this.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment;
        let entryPointString = "\n".concat(emitComments ? "//Entry point\n" : "");
        if (this._customEntryHeader) {
            entryPointString += this._customEntryHeader;
        } else if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            if (isFragmentMode) {
                entryPointString += "@fragment\nfn main(input: FragmentInputs) -> FragmentOutputs {\n".concat(this.sharedData.varyingInitializationsFragment);
            } else {
                entryPointString += "@vertex\nfn main(input: VertexInputs) -> FragmentInputs{\n";
            }
        } else {
            entryPointString += "void main(void) {\n";
        }
        this.compilationString = entryPointString + this.compilationString;
        if (this._constantDeclaration) {
            this.compilationString = "\n".concat(emitComments ? "//Constants\n" : "").concat(this._constantDeclaration, "\n").concat(this.compilationString);
        }
        let functionCode = "";
        for(const functionName in this.functions){
            functionCode += this.functions[functionName] + "\n";
        }
        this.compilationString = "\n".concat(functionCode, "\n").concat(this.compilationString);
        if (!isFragmentMode && this._varyingTransfer) {
            this.compilationString = "".concat(this.compilationString, "\n").concat(this._varyingTransfer);
        }
        if (this._injectAtEnd) {
            this.compilationString = "".concat(this.compilationString, "\n").concat(this._injectAtEnd);
        }
        this.compilationString = "".concat(this.compilationString, "\n}");
        if (this.sharedData.varyingDeclaration) {
            this.compilationString = "\n".concat(emitComments ? "//Varyings\n" : "").concat(isFragmentMode ? this.sharedData.varyingDeclarationFragment : this.sharedData.varyingDeclaration, "\n").concat(this.compilationString);
        }
        if (this._samplerDeclaration) {
            this.compilationString = "\n".concat(emitComments ? "//Samplers\n" : "").concat(this._samplerDeclaration, "\n").concat(this.compilationString);
        }
        if (this._uniformDeclaration) {
            this.compilationString = "\n".concat(emitComments ? "//Uniforms\n" : "").concat(this._uniformDeclaration, "\n").concat(this.compilationString);
        }
        if (this._attributeDeclaration && !isFragmentMode) {
            this.compilationString = "\n".concat(emitComments ? "//Attributes\n" : "").concat(this._attributeDeclaration, "\n").concat(this.compilationString);
        }
        if (this.shaderLanguage !== 1 /* ShaderLanguage.WGSL */ ) {
            this.compilationString = "precision highp float;\n" + this.compilationString;
            this.compilationString = "#if defined(WEBGL2) || defined(WEBGPU)\nprecision highp sampler2DArray;\n#endif\n" + this.compilationString;
            if (isFragmentMode) {
                this.compilationString = "#if defined(PREPASS)\r\n#extension GL_EXT_draw_buffers : require\r\nlayout(location = 0) out highp vec4 glFragData[SCENE_MRT_COUNT];\r\nhighp vec4 gl_FragColor;\r\n#endif\r\n" + this.compilationString;
            }
            for(const extensionName in this.extensions){
                const extension = this.extensions[extensionName];
                this.compilationString = "\n".concat(extension, "\n").concat(this.compilationString);
            }
        }
        if (this._injectAtTop) {
            this.compilationString = "".concat(this._injectAtTop, "\n").concat(this.compilationString);
        }
        this._builtCompilationString = this.compilationString;
    }
    /** @internal */ get _repeatableContentAnchor() {
        return "###___ANCHOR".concat(this._repeatableContentAnchorIndex++, "___###");
    }
    /**
     * @internal
     */ _getFreeVariableName(prefix) {
        prefix = this.sharedData.formatConfig.formatVariablename(prefix);
        if (this.sharedData.variableNames[prefix] === undefined) {
            this.sharedData.variableNames[prefix] = 0;
            // Check reserved words
            if (prefix === "output" || prefix === "texture") {
                return prefix + this.sharedData.variableNames[prefix];
            }
            return prefix;
        } else {
            this.sharedData.variableNames[prefix]++;
        }
        return prefix + this.sharedData.variableNames[prefix];
    }
    /**
     * @internal
     */ _getFreeDefineName(prefix) {
        if (this.sharedData.defineNames[prefix] === undefined) {
            this.sharedData.defineNames[prefix] = 0;
        } else {
            this.sharedData.defineNames[prefix]++;
        }
        return prefix + this.sharedData.defineNames[prefix];
    }
    /**
     * @internal
     */ _excludeVariableName(name) {
        this.sharedData.variableNames[name] = 0;
    }
    /**
     * @internal
     */ _emit2DSampler(name) {
        let define = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "", force = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false, annotation = arguments.length > 3 ? arguments[3] : void 0, unsignedSampler = arguments.length > 4 ? arguments[4] : void 0, precision = arguments.length > 5 ? arguments[5] : void 0;
        if (this.samplers.indexOf(name) < 0 || force) {
            if (define) {
                this._samplerDeclaration += "#if ".concat(define, "\n");
            }
            if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
                const unsignedSamplerPrefix = unsignedSampler ? "u" : "f";
                this._samplerDeclaration += "var ".concat(name + "Sampler", ": sampler;\n");
                this._samplerDeclaration += "var ".concat(name, ": texture_2d<").concat(unsignedSamplerPrefix, "32>;\n");
            } else {
                const unsignedSamplerPrefix = unsignedSampler ? "u" : "";
                const precisionDecl = precision !== null && precision !== void 0 ? precision : "";
                this._samplerDeclaration += "uniform ".concat(precisionDecl, " ").concat(unsignedSamplerPrefix, "sampler2D ").concat(name, "; ").concat(annotation ? annotation : "", "\n");
            }
            if (define) {
                this._samplerDeclaration += "#endif\n";
            }
            if (!force) {
                this.samplers.push(name);
            }
        }
    }
    /**
     * @internal
     */ _emitCubeSampler(name) {
        let define = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "", force = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        if (this.samplers.indexOf(name) < 0 || force) {
            if (define) {
                this._samplerDeclaration += "#if ".concat(define, "\n");
            }
            if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
                this._samplerDeclaration += "var ".concat(name + "Sampler", ": sampler;\n");
                this._samplerDeclaration += "var ".concat(name, ": texture_cube<f32>;\n");
            } else {
                this._samplerDeclaration += "uniform samplerCube ".concat(name, ";\n");
            }
            if (define) {
                this._samplerDeclaration += "#endif\n";
            }
            if (!force) {
                this.samplers.push(name);
            }
        }
    }
    /**
     * @internal
     */ _emit2DArraySampler(name) {
        if (this.samplers.indexOf(name) < 0) {
            this._samplerDeclaration += "uniform sampler2DArray ".concat(name, ";\n");
            this.samplers.push(name);
        }
    }
    /**
     * @internal
     */ _getGLType(type) {
        switch(type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float:
                return "float";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Int:
                return "int";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2:
                return "vec2";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3:
                return "vec3";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color4:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4:
                return "vec4";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Matrix:
                return "mat4";
        }
        return "";
    }
    /**
     * @internal
     */ _getShaderType(type) {
        const isWGSL = this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ;
        switch(type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float:
                return isWGSL ? "f32" : "float";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Int:
                return isWGSL ? "i32" : "int";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector2:
                return isWGSL ? "vec2f" : "vec2";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3:
                return isWGSL ? "vec3f" : "vec3";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color4:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4:
                return isWGSL ? "vec4f" : "vec4";
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Matrix:
                return isWGSL ? "mat4x4f" : "mat4";
        }
        return "";
    }
    /**
     * @internal
     */ _emitExtension(name, extension) {
        let define = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "";
        if (this.extensions[name]) {
            return;
        }
        if (define) {
            extension = "#if ".concat(define, "\n").concat(extension, "\n#endif");
        }
        this.extensions[name] = extension;
    }
    /**
     * @internal
     */ _emitFunction(name, code, comments) {
        if (this.functions[name]) {
            return;
        }
        if (this.sharedData.emitComments) {
            code = comments + "\n" + code;
        }
        this.functions[name] = code;
    }
    /**
     * @internal
     */ _emitCodeFromInclude(includeName, comments, options) {
        const store = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].GetIncludesShadersStore(this.shaderLanguage);
        if (options && options.repeatKey) {
            return "#include<".concat(includeName, ">").concat(options.substitutionVars ? "(" + options.substitutionVars + ")" : "", "[0..").concat(options.repeatKey, "]\n");
        }
        let code = store[includeName] + "\n";
        if (this.sharedData.emitComments) {
            code = comments + "\n" + code;
        }
        if (!options) {
            return code;
        }
        if (options.replaceStrings) {
            for(let index = 0; index < options.replaceStrings.length; index++){
                const replaceString = options.replaceStrings[index];
                code = code.replace(replaceString.search, replaceString.replace);
            }
        }
        return code;
    }
    /**
     * @internal
     */ _emitFunctionFromInclude(includeName, comments, options) {
        let storeKey = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : "";
        const key = includeName + storeKey;
        if (this.functions[key]) {
            return;
        }
        const store = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$shaderStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShaderStore"].GetIncludesShadersStore(this.shaderLanguage);
        if (!options || !options.removeAttributes && !options.removeUniforms && !options.removeVaryings && !options.removeIfDef && !options.replaceStrings) {
            if (options && options.repeatKey) {
                this.functions[key] = "#include<".concat(includeName, ">").concat(options.substitutionVars ? "(" + options.substitutionVars + ")" : "", "[0..").concat(options.repeatKey, "]\n");
            } else {
                this.functions[key] = "#include<".concat(includeName, ">").concat((options === null || options === void 0 ? void 0 : options.substitutionVars) ? "(" + (options === null || options === void 0 ? void 0 : options.substitutionVars) + ")" : "", "\n");
            }
            if (this.sharedData.emitComments) {
                this.functions[key] = comments + "\n" + this.functions[key];
            }
            return;
        }
        this.functions[key] = store[includeName];
        if (this.sharedData.emitComments) {
            this.functions[key] = comments + "\n" + this.functions[key];
        }
        if (options.removeIfDef) {
            this.functions[key] = this.functions[key].replace(/^\s*?#ifdef.+$/gm, "");
            this.functions[key] = this.functions[key].replace(/^\s*?#endif.*$/gm, "");
            this.functions[key] = this.functions[key].replace(/^\s*?#else.*$/gm, "");
            this.functions[key] = this.functions[key].replace(/^\s*?#elif.*$/gm, "");
        }
        if (options.removeAttributes) {
            this.functions[key] = this.functions[key].replace(/\s*?attribute .+?;/g, "\n");
        }
        if (options.removeUniforms) {
            this.functions[key] = this.functions[key].replace(/\s*?uniform .*?;/g, "\n");
        }
        if (options.removeVaryings) {
            this.functions[key] = this.functions[key].replace(/\s*?(varying|in) .+?;/g, "\n");
        }
        if (options.replaceStrings) {
            for(let index = 0; index < options.replaceStrings.length; index++){
                const replaceString = options.replaceStrings[index];
                this.functions[key] = this.functions[key].replace(replaceString.search, replaceString.replace);
            }
        }
    }
    /**
     * @internal
     */ _registerTempVariable(name) {
        if (this.sharedData.temps.indexOf(name) !== -1) {
            return false;
        }
        this.sharedData.temps.push(name);
        return true;
    }
    /**
     * @internal
     */ _emitVaryingFromString(name, type) {
        let define = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "", notDefine = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
        var _this = this;
        if (this.sharedData.varyings.indexOf(name) !== -1) {
            return false;
        }
        this.sharedData.varyings.push(name);
        const shaderType = this._getShaderType(type);
        const emitCode = function() {
            let forFragment = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
            let code = "";
            if (define) {
                if (define.startsWith("defined(")) {
                    code += "#if ".concat(define, "\n");
                } else {
                    code += "".concat(notDefine ? "#ifndef" : "#ifdef", " ").concat(define, "\n");
                }
            }
            if (_this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
                switch(shaderType){
                    case "mat4x4f":
                        // We can't pass a matrix as a varying in WGSL, so we need to split it into 4 vectors
                        code += "varying ".concat(name, "_r0: vec4f;\n");
                        code += "varying ".concat(name, "_r1: vec4f;\n");
                        code += "varying ".concat(name, "_r2: vec4f;\n");
                        code += "varying ".concat(name, "_r3: vec4f;\n");
                        if (forFragment) {
                            code += "var<private> ".concat(name, ": mat4x4f;\n");
                            _this.sharedData.varyingInitializationsFragment += "".concat(name, " = mat4x4f(fragmentInputs.").concat(name, "_r0, fragmentInputs.").concat(name, "_r1, fragmentInputs.").concat(name, "_r2, fragmentInputs.").concat(name, "_r3);\n");
                        }
                        break;
                    default:
                        code += "varying ".concat(name, ": ").concat(shaderType, ";\n");
                        break;
                }
            } else {
                code += "varying ".concat(shaderType, " ").concat(name, ";\n");
            }
            if (define) {
                code += "#endif\n";
            }
            return code;
        };
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            this.sharedData.varyingDeclaration += emitCode(false);
            this.sharedData.varyingDeclarationFragment += emitCode(true);
        } else {
            const code = emitCode();
            this.sharedData.varyingDeclaration += code;
            this.sharedData.varyingDeclarationFragment += code;
        }
        return true;
    }
    /**
     * @internal
     */ _getVaryingName(name) {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return (this.target !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment ? "vertexOutputs." : "fragmentInputs.") + name;
        }
        return name;
    }
    /**
     * @internal
     */ _emitUniformFromString(name, type) {
        let define = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "", notDefine = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
        if (this.uniforms.indexOf(name) !== -1) {
            return;
        }
        this.uniforms.push(name);
        if (define) {
            if (define.startsWith("defined(")) {
                this._uniformDeclaration += "#if ".concat(define, "\n");
            } else {
                this._uniformDeclaration += "".concat(notDefine ? "#ifndef" : "#ifdef", " ").concat(define, "\n");
            }
        }
        if (this.sharedData.formatConfig.getUniformAnnotation) {
            this._uniformDeclaration += this.sharedData.formatConfig.getUniformAnnotation(name);
        }
        const shaderType = this._getShaderType(type);
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            this._uniformDeclaration += "uniform ".concat(name, ": ").concat(shaderType, ";\n");
        } else {
            this._uniformDeclaration += "uniform ".concat(shaderType, " ").concat(name, ";\n");
        }
        if (define) {
            this._uniformDeclaration += "#endif\n";
        }
    }
    /**
     * @internal
     */ _generateTernary(trueStatement, falseStatement, condition) {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return "select(".concat(falseStatement, ", ").concat(trueStatement, ", ").concat(condition, ")");
        }
        return "(".concat(condition, ") ? ").concat(trueStatement, " : ").concat(falseStatement);
    }
    /**
     * @internal
     */ _emitFloat(value) {
        if (value.toString() === value.toFixed(0)) {
            return "".concat(value, ".0");
        }
        return value.toString();
    }
    /**
     * @internal
     */ _declareOutput(output, isConst) {
        return this._declareLocalVar(output.associatedVariableName, output.type, isConst);
    }
    /**
     * @internal
     */ _declareLocalVar(name, type, isConst) {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return "".concat(isConst ? "const" : "var", " ").concat(name, ": ").concat(this._getShaderType(type));
        } else {
            return "".concat(isConst ? "const " : "").concat(this._getShaderType(type), " ").concat(name);
        }
    }
    /**
     * @internal
     */ _samplerCubeFunc() {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return "textureSample";
        }
        return "textureCube";
    }
    /**
     * @internal
     */ _samplerFunc() {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return "textureSample";
        }
        return "texture2D";
    }
    /**
     * @internal
     */ _samplerLODFunc() {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return "textureSampleLevel";
        }
        return "texture2DLodEXT";
    }
    _toLinearSpace(output) {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            if (output.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3 || output.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3) {
                return "toLinearSpaceVec3(".concat(output.associatedVariableName, ")");
            }
            return "toLinearSpace(".concat(output.associatedVariableName, ")");
        }
        return "toLinearSpace(".concat(output.associatedVariableName, ")");
    }
    /**
     * @internal
     */ _generateTextureSample(uv, samplerName) {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return "".concat(this._samplerFunc(), "(").concat(samplerName, ",").concat(samplerName + "Sampler", ", ").concat(uv, ")");
        }
        return "".concat(this._samplerFunc(), "(").concat(samplerName, ", ").concat(uv, ")");
    }
    /**
     * @internal
     */ _generateTextureSampleLOD(uv, samplerName, lod) {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return "".concat(this._samplerLODFunc(), "(").concat(samplerName, ",").concat(samplerName + "Sampler", ", ").concat(uv, ", ").concat(lod, ")");
        }
        return "".concat(this._samplerLODFunc(), "(").concat(samplerName, ", ").concat(uv, ", ").concat(lod, ")");
    }
    /**
     * @internal
     */ _generateTextureSampleCube(uv, samplerName) {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return "".concat(this._samplerCubeFunc(), "(").concat(samplerName, ",").concat(samplerName + "Sampler", ", ").concat(uv, ")");
        }
        return "".concat(this._samplerCubeFunc(), "(").concat(samplerName, ", ").concat(uv, ")");
    }
    /**
     * @internal
     */ _generateTextureSampleCubeLOD(uv, samplerName, lod) {
        if (this.shaderLanguage === 1 /* ShaderLanguage.WGSL */ ) {
            return "".concat(this._samplerCubeFunc(), "(").concat(samplerName, ",").concat(samplerName + "Sampler", ", ").concat(uv, ", ").concat(lod, ")");
        }
        return "".concat(this._samplerCubeFunc(), "(").concat(samplerName, ", ").concat(uv, ", ").concat(lod, ")");
    }
    _convertVariableDeclarationToWGSL(type, dest, source) {
        return source.replace(new RegExp("(".concat(type, ")\\s+(\\w+)"), "g"), "var $2: ".concat(dest));
    }
    _convertVariableConstructorsToWGSL(type, dest, source) {
        return source.replace(new RegExp("(".concat(type, ")\\("), "g"), " ".concat(dest, "("));
    }
    _convertOutParametersToWGSL(source) {
        return source.replace(new RegExp("out\\s+var\\s+(\\w+)\\s*:\\s*(\\w+)", "g"), "$1: ptr<function, $2>");
    }
    _convertTernaryOperandsToWGSL(source) {
        return source.replace(new RegExp("\\[(.*?)\\?(.*?):(.*)\\]", "g"), (match, condition, trueCase, falseCase)=>"select(".concat(falseCase, ", ").concat(trueCase, ", ").concat(condition, ")"));
    }
    _convertModOperatorsToWGSL(source) {
        return source.replace(new RegExp("mod\\((.+?),\\s*(.+?)\\)", "g"), (match, left, right)=>"((".concat(left, ")%(").concat(right, "))"));
    }
    _convertConstToWGSL(source) {
        return source.replace(new RegExp("const var", "g"), "const");
    }
    _convertInnerFunctionsToWGSL(source) {
        return source.replace(new RegExp("inversesqrt", "g"), "inverseSqrt");
    }
    _convertFunctionsToWGSL(source) {
        const regex = /var\s+(\w+)\s*:\s*(\w+)\((.*)\)/g;
        let match;
        while((match = regex.exec(source)) !== null){
            const funcName = match[1];
            const funcType = match[2];
            const params = match[3]; // All parameters as a single string
            // Processing the parameters to match 'name: type' format
            const formattedParams = params.replace(/var\s/g, "");
            // Constructing the final output string
            source = source.replace(match[0], "fn ".concat(funcName, "(").concat(formattedParams, ") -> ").concat(funcType));
        }
        return source;
    }
    _babylonSLtoWGSL(code) {
        // variable declarations
        code = this._convertVariableDeclarationToWGSL("void", "voidnull", code);
        code = this._convertVariableDeclarationToWGSL("bool", "bool", code);
        code = this._convertVariableDeclarationToWGSL("int", "i32", code);
        code = this._convertVariableDeclarationToWGSL("uint", "u32", code);
        code = this._convertVariableDeclarationToWGSL("float", "f32", code);
        code = this._convertVariableDeclarationToWGSL("vec2", "vec2f", code);
        code = this._convertVariableDeclarationToWGSL("vec3", "vec3f", code);
        code = this._convertVariableDeclarationToWGSL("vec4", "vec4f", code);
        code = this._convertVariableDeclarationToWGSL("mat2", "mat2x2f", code);
        code = this._convertVariableDeclarationToWGSL("mat3", "mat3x3f", code);
        code = this._convertVariableDeclarationToWGSL("mat4", "mat4x4f", code);
        // Type constructors
        code = this._convertVariableConstructorsToWGSL("float", "f32", code);
        code = this._convertVariableConstructorsToWGSL("vec2", "vec2f", code);
        code = this._convertVariableConstructorsToWGSL("vec3", "vec3f", code);
        code = this._convertVariableConstructorsToWGSL("vec4", "vec4f", code);
        code = this._convertVariableConstructorsToWGSL("mat2", "mat2x2f", code);
        code = this._convertVariableConstructorsToWGSL("mat3", "mat3x3f", code);
        code = this._convertVariableConstructorsToWGSL("mat4", "mat4x4f", code);
        // Ternary operands
        code = this._convertTernaryOperandsToWGSL(code);
        // Mod operators
        code = this._convertModOperatorsToWGSL(code);
        // Const
        code = this._convertConstToWGSL(code);
        // Inner functions
        code = this._convertInnerFunctionsToWGSL(code);
        // Out paramters
        code = this._convertOutParametersToWGSL(code);
        code = code.replace(/\[\*\]/g, "*");
        // Functions
        code = this._convertFunctionsToWGSL(code);
        // Remove voidnull
        code = code.replace(/\s->\svoidnull/g, "");
        // Derivatives
        code = code.replace(/dFdx/g, "dpdx");
        code = code.replace(/dFdy/g, "dpdy");
        return code;
    }
    _convertTernaryOperandsToGLSL(source) {
        return source.replace(new RegExp("\\[(.+?)\\?(.+?):(.+)\\]", "g"), (match, condition, trueCase, falseCase)=>"".concat(condition, " ? ").concat(trueCase, " : ").concat(falseCase));
    }
    _babylonSLtoGLSL(code) {
        /** Remove BSL specifics */ code = code.replace(/\[\*\]/g, "");
        code = this._convertTernaryOperandsToGLSL(code);
        return code;
    }
    constructor(){
        /** Gets or sets a boolean indicating if the current state can emit uniform buffers */ this.supportUniformBuffers = false;
        /**
         * Gets the list of emitted attributes
         */ this.attributes = [];
        /**
         * Gets the list of emitted uniforms
         */ this.uniforms = [];
        /**
         * Gets the list of emitted constants
         */ this.constants = [];
        /**
         * Gets the list of emitted samplers
         */ this.samplers = [];
        /**
         * Gets the list of emitted functions
         */ this.functions = {};
        /**
         * Gets the list of emitted extensions
         */ this.extensions = {};
        /**
         * Gets the list of emitted prePass outputs - if using the prepass
         */ this.prePassOutput = {};
        /**
         * Gets the list of emitted counters
         */ this.counters = {};
        /** @internal */ this._terminalBlocks = new Set();
        /** @internal */ this._attributeDeclaration = "";
        /** @internal */ this._uniformDeclaration = "";
        /** @internal */ this._constantDeclaration = "";
        /** @internal */ this._samplerDeclaration = "";
        /** @internal */ this._varyingTransfer = "";
        /** @internal */ this._injectAtEnd = "";
        /** @internal */ this._injectAtTop = "";
        /** @internal */ this._customEntryHeader = "";
        /** @internal */ this._repeatableContentAnchorIndex = 0;
        /** @internal */ this._builtCompilationString = "";
        /**
         * Gets the emitted compilation strings
         */ this.compilationString = "";
    }
} //# sourceMappingURL=nodeMaterialBuildState.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBuildStateSharedData.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "NodeMaterialBuildStateSharedData": ()=>NodeMaterialBuildStateSharedData
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/logger.js [app-client] (ecmascript)");
;
class NodeMaterialBuildStateSharedData {
    /**
     * Push a new error to the build state, avoiding exceptions that can break the build process
     * @param message defines the error message to push
     */ raiseBuildError(message) {
        if (this.checks.customErrors.indexOf(message) !== -1) {
            this.checks.customErrors.push(message);
        }
    }
    /**
     * Emits console errors and exceptions if there is a failing check
     * @returns true if all checks pass
     */ emitErrors() {
        let errorMessage = "";
        if (!this.checks.emitVertex && !this.allowEmptyVertexProgram) {
            errorMessage += "NodeMaterial does not have a vertex output. You need to at least add a block that generates a position value.\n";
        }
        if (!this.checks.emitFragment) {
            errorMessage += "NodeMaterial does not have a fragment output. You need to at least add a block that generates a color value.\n";
        }
        for (const notConnectedInput of this.checks.notConnectedNonOptionalInputs){
            errorMessage += "input ".concat(notConnectedInput.name, " from block ").concat(notConnectedInput.ownerBlock.name, "[").concat(notConnectedInput.ownerBlock.getClassName(), "] is not connected and is not optional.\n");
        }
        for (const customError of this.checks.customErrors){
            errorMessage += customError + "\n";
        }
        if (errorMessage) {
            errorMessage = "Node material build failed: \n" + errorMessage;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Error(errorMessage);
            this.nodeMaterial.onBuildErrorObservable.notifyObservers(errorMessage);
            return false;
        }
        return true;
    }
    /** Creates a new shared data */ constructor(){
        /**
         * Gets the list of emitted varyings
         */ this.temps = [];
        /**
         * Gets the list of emitted varyings
         */ this.varyings = [];
        /**
         * Gets the varying declaration string (for vertex shader)
         */ this.varyingDeclaration = "";
        /**
         * Gets the varying declaration string (for fragment shader)
         * This is potentially different from varyingDeclaration only in WebGPU
         */ this.varyingDeclarationFragment = "";
        /**
         * Gets the varying initialization string (for fragment shader)
         * Only used in WebGPU, to reconstruct the varying values from the vertex shader if their types is mat4x4f
         */ this.varyingInitializationsFragment = "";
        /**
         * Input blocks
         */ this.inputBlocks = [];
        /**
         * Input blocks
         */ this.textureBlocks = [];
        /**
         * Bindable blocks (Blocks that need to set data to the effect)
         */ this.bindableBlocks = [];
        /**
         * Bindable blocks (Blocks that need to set data to the effect) that will always be called (by bindForSubMesh), contrary to bindableBlocks that won't be called if _mustRebind() returns false
         */ this.forcedBindableBlocks = [];
        /**
         * List of blocks that can provide a compilation fallback
         */ this.blocksWithFallbacks = [];
        /**
         * List of blocks that can provide a define update
         */ this.blocksWithDefines = [];
        /**
         * List of blocks that can provide a repeatable content
         */ this.repeatableContentBlocks = [];
        /**
         * List of blocks that can provide a dynamic list of uniforms
         */ this.dynamicUniformBlocks = [];
        /**
         * List of blocks that can block the isReady function for the material
         */ this.blockingBlocks = [];
        /**
         * Gets the list of animated inputs
         */ this.animatedInputs = [];
        /**
         * Configurations used to format the generated code
         */ this.formatConfig = {
            getUniformAnnotation: null,
            formatVariablename: (name)=>name.replace(/[^a-zA-Z_]+/g, "")
        };
        /** List of emitted variables */ this.variableNames = {};
        /** List of emitted defines */ this.defineNames = {};
        /**
         * Gets the compilation hints emitted at compilation time
         */ this.hints = {
            needWorldViewMatrix: false,
            needWorldViewProjectionMatrix: false,
            needAlphaBlending: false,
            needAlphaTesting: false
        };
        /**
         * List of compilation checks
         */ this.checks = {
            emitVertex: false,
            emitFragment: false,
            notConnectedNonOptionalInputs: new Array(),
            customErrors: new Array()
        };
        /**
         * Is vertex program allowed to be empty?
         */ this.allowEmptyVertexProgram = false;
        // Exclude usual attributes from free variable names
        this.variableNames["position"] = 0;
        this.variableNames["normal"] = 0;
        this.variableNames["tangent"] = 0;
        this.variableNames["uv"] = 0;
        this.variableNames["uv2"] = 0;
        this.variableNames["uv3"] = 0;
        this.variableNames["uv4"] = 0;
        this.variableNames["uv5"] = 0;
        this.variableNames["uv6"] = 0;
        this.variableNames["color"] = 0;
        this.variableNames["matricesIndices"] = 0;
        this.variableNames["matricesWeights"] = 0;
        this.variableNames["matricesIndicesExtra"] = 0;
        this.variableNames["matricesWeightsExtra"] = 0;
        this.variableNames["diffuseBase"] = 0;
        this.variableNames["specularBase"] = 0;
        this.variableNames["worldPos"] = 0;
        this.variableNames["shadow"] = 0;
        this.variableNames["view"] = 0;
        // Exclude known varyings
        this.variableNames["vTBN"] = 0;
        // Exclude defines
        this.defineNames["MAINUV0"] = 0;
        this.defineNames["MAINUV1"] = 0;
        this.defineNames["MAINUV2"] = 0;
        this.defineNames["MAINUV3"] = 0;
        this.defineNames["MAINUV4"] = 0;
        this.defineNames["MAINUV5"] = 0;
        this.defineNames["MAINUV6"] = 0;
        this.defineNames["MAINUV7"] = 0;
    }
} //# sourceMappingURL=nodeMaterialBuildStateSharedData.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlockConnectionPoint.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "NodeMaterialConnectionPoint": ()=>NodeMaterialConnectionPoint,
    "NodeMaterialConnectionPointCompatibilityStates": ()=>NodeMaterialConnectionPointCompatibilityStates,
    "NodeMaterialConnectionPointDirection": ()=>NodeMaterialConnectionPointDirection
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$observable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/observable.js [app-client] (ecmascript)");
;
;
;
var NodeMaterialConnectionPointCompatibilityStates;
(function(NodeMaterialConnectionPointCompatibilityStates) {
    /** Points are compatibles */ NodeMaterialConnectionPointCompatibilityStates[NodeMaterialConnectionPointCompatibilityStates["Compatible"] = 0] = "Compatible";
    /** Points are incompatible because of their types */ NodeMaterialConnectionPointCompatibilityStates[NodeMaterialConnectionPointCompatibilityStates["TypeIncompatible"] = 1] = "TypeIncompatible";
    /** Points are incompatible because of their targets (vertex vs fragment) */ NodeMaterialConnectionPointCompatibilityStates[NodeMaterialConnectionPointCompatibilityStates["TargetIncompatible"] = 2] = "TargetIncompatible";
    /** Points are incompatible because they are in the same hierarchy **/ NodeMaterialConnectionPointCompatibilityStates[NodeMaterialConnectionPointCompatibilityStates["HierarchyIssue"] = 3] = "HierarchyIssue";
})(NodeMaterialConnectionPointCompatibilityStates || (NodeMaterialConnectionPointCompatibilityStates = {}));
var NodeMaterialConnectionPointDirection;
(function(NodeMaterialConnectionPointDirection) {
    /** Input */ NodeMaterialConnectionPointDirection[NodeMaterialConnectionPointDirection["Input"] = 0] = "Input";
    /** Output */ NodeMaterialConnectionPointDirection[NodeMaterialConnectionPointDirection["Output"] = 1] = "Output";
})(NodeMaterialConnectionPointDirection || (NodeMaterialConnectionPointDirection = {}));
class NodeMaterialConnectionPoint {
    /**
     * Checks if two types are equivalent
     * @param type1 type 1 to check
     * @param type2 type 2 to check
     * @returns true if both types are equivalent, else false
     */ static AreEquivalentTypes(type1, type2) {
        switch(type1){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3:
                {
                    if (type2 === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3) {
                        return true;
                    }
                    break;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4:
                {
                    if (type2 === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color4) {
                        return true;
                    }
                    break;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color3:
                {
                    if (type2 === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector3) {
                        return true;
                    }
                    break;
                }
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Color4:
                {
                    if (type2 === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Vector4) {
                        return true;
                    }
                    break;
                }
        }
        return false;
    }
    get _connectedPoint() {
        return this._connectedPointBackingField;
    }
    set _connectedPoint(value) {
        var _this__connectedPointTypeChangedObserver;
        if (this._connectedPointBackingField === value) {
            return;
        }
        (_this__connectedPointTypeChangedObserver = this._connectedPointTypeChangedObserver) === null || _this__connectedPointTypeChangedObserver === void 0 ? void 0 : _this__connectedPointTypeChangedObserver.remove();
        this._updateTypeDependentState(()=>this._connectedPointBackingField = value);
        if (this._connectedPointBackingField) {
            this._connectedPointTypeChangedObserver = this._connectedPointBackingField.onTypeChangedObservable.add(()=>{
                this._notifyTypeChanged();
            });
        }
    }
    /** @internal */ get _typeConnectionSource() {
        return this._typeConnectionSourceBackingField;
    }
    /** @internal */ set _typeConnectionSource(value) {
        var _this__typeConnectionSourceTypeChangedObserver;
        if (this._typeConnectionSourceBackingField === value) {
            return;
        }
        (_this__typeConnectionSourceTypeChangedObserver = this._typeConnectionSourceTypeChangedObserver) === null || _this__typeConnectionSourceTypeChangedObserver === void 0 ? void 0 : _this__typeConnectionSourceTypeChangedObserver.remove();
        this._updateTypeDependentState(()=>this._typeConnectionSourceBackingField = value);
        if (this._typeConnectionSourceBackingField) {
            this._typeConnectionSourceTypeChangedObserver = this._typeConnectionSourceBackingField.onTypeChangedObservable.add(()=>{
                this._notifyTypeChanged();
            });
        }
    }
    /** @internal */ get _defaultConnectionPointType() {
        return this._defaultConnectionPointTypeBackingField;
    }
    /** @internal */ set _defaultConnectionPointType(value) {
        this._updateTypeDependentState(()=>this._defaultConnectionPointTypeBackingField = value);
    }
    /** @internal */ get _linkedConnectionSource() {
        return this._linkedConnectionSourceBackingField;
    }
    /** @internal */ set _linkedConnectionSource(value) {
        var _this__linkedConnectionSourceTypeChangedObserver;
        if (this._linkedConnectionSourceBackingField === value) {
            return;
        }
        (_this__linkedConnectionSourceTypeChangedObserver = this._linkedConnectionSourceTypeChangedObserver) === null || _this__linkedConnectionSourceTypeChangedObserver === void 0 ? void 0 : _this__linkedConnectionSourceTypeChangedObserver.remove();
        this._updateTypeDependentState(()=>this._linkedConnectionSourceBackingField = value);
        this._isMainLinkSource = false;
        if (this._linkedConnectionSourceBackingField) {
            this._linkedConnectionSourceTypeChangedObserver = this._linkedConnectionSourceBackingField.onTypeChangedObservable.add(()=>{
                this._notifyTypeChanged();
            });
        }
    }
    /** Gets the direction of the point */ get direction() {
        return this._direction;
    }
    /**
     * Gets the declaration variable name in the shader
     */ get declarationVariableName() {
        if (this._ownerBlock.isInput) {
            return this._ownerBlock.declarationVariableName;
        }
        if ((!this._enforceAssociatedVariableName || !this._associatedVariableName) && this._connectedPoint) {
            return this._connectedPoint.declarationVariableName;
        }
        return this._associatedVariableName;
    }
    /**
     * Gets or sets the associated variable name in the shader
     */ get associatedVariableName() {
        if (this._ownerBlock.isInput) {
            return this._ownerBlock.associatedVariableName;
        }
        if ((!this._enforceAssociatedVariableName || !this._associatedVariableName) && this._connectedPoint) {
            return this._connectedPoint.associatedVariableName;
        }
        return this._associatedVariableName;
    }
    set associatedVariableName(value) {
        this._associatedVariableName = value;
    }
    /** Get the inner type (ie AutoDetect for instance instead of the inferred one) */ get innerType() {
        if (this._linkedConnectionSource && !this._isMainLinkSource && this._linkedConnectionSource.isConnected) {
            return this.type;
        }
        return this._type;
    }
    /**
     * Gets or sets the connection point type (default is float)
     */ get type() {
        if (this._type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].AutoDetect) {
            if (this._ownerBlock.isInput) {
                return this._ownerBlock.type;
            }
            if (this._connectedPoint) {
                return this._connectedPoint.type;
            }
            if (this._linkedConnectionSource) {
                if (this._linkedConnectionSource.isConnected) {
                    if (this._linkedConnectionSource.connectedPoint._redirectedSource && this._linkedConnectionSource.connectedPoint._redirectedSource.isConnected) {
                        return this._linkedConnectionSource.connectedPoint._redirectedSource.type;
                    }
                    return this._linkedConnectionSource.type;
                }
                if (this._linkedConnectionSource._defaultConnectionPointType) {
                    return this._linkedConnectionSource._defaultConnectionPointType;
                }
            }
            if (this._defaultConnectionPointType) {
                return this._defaultConnectionPointType;
            }
        }
        if (this._type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].BasedOnInput) {
            if (this._typeConnectionSource) {
                if (!this._typeConnectionSource.isConnected && this._defaultConnectionPointType) {
                    return this._defaultConnectionPointType;
                }
                return this._typeConnectionSource.type;
            } else if (this._defaultConnectionPointType) {
                return this._defaultConnectionPointType;
            }
        }
        return this._type;
    }
    set type(value) {
        this._updateTypeDependentState(()=>this._type = value);
    }
    /** Gets or sets the target of that connection point */ get target() {
        if (!this._prioritizeVertex || !this._ownerBlock) {
            return this._target;
        }
        if (this._target !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment) {
            return this._target;
        }
        if (this._ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex;
    }
    set target(value) {
        this._target = value;
    }
    /**
     * Gets a boolean indicating that the current point is connected to another NodeMaterialBlock
     */ get isConnected() {
        return this.connectedPoint !== null || this.hasEndpoints;
    }
    /**
     * Gets a boolean indicating that the current point is connected to an input block
     */ get isConnectedToInputBlock() {
        return this.connectedPoint !== null && this.connectedPoint.ownerBlock.isInput;
    }
    /**
     * Gets a the connected input block (if any)
     */ get connectInputBlock() {
        if (!this.isConnectedToInputBlock) {
            return null;
        }
        return this.connectedPoint.ownerBlock;
    }
    /** Get the other side of the connection (if any) */ get connectedPoint() {
        return this._connectedPoint;
    }
    /** Get the block that owns this connection point */ get ownerBlock() {
        return this._ownerBlock;
    }
    /** Get the block connected on the other side of this connection (if any) */ get sourceBlock() {
        if (!this._connectedPoint) {
            return null;
        }
        return this._connectedPoint.ownerBlock;
    }
    /** Get the block connected on the endpoints of this connection (if any) */ get connectedBlocks() {
        if (this._endpoints.length === 0) {
            return [];
        }
        return this._endpoints.map((e)=>e.ownerBlock);
    }
    /** Gets the list of connected endpoints */ get endpoints() {
        return this._endpoints;
    }
    /** Gets a boolean indicating if that output point is connected to at least one input */ get hasEndpoints() {
        return this._endpoints && this._endpoints.length > 0;
    }
    /** Gets a boolean indicating that this connection has a path to the vertex output*/ get isDirectlyConnectedToVertexOutput() {
        if (!this.hasEndpoints) {
            return false;
        }
        for (const endpoint of this._endpoints){
            if (endpoint.ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex) {
                return true;
            }
            if (endpoint.ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Neutral || endpoint.ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment) {
                if (endpoint.ownerBlock.outputs.some((o)=>o.isDirectlyConnectedToVertexOutput)) {
                    return true;
                }
            }
        }
        return false;
    }
    /** Gets a boolean indicating that this connection will be used in the vertex shader */ get isConnectedInVertexShader() {
        if (this.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex) {
            return true;
        }
        if (!this.hasEndpoints) {
            return false;
        }
        for (const endpoint of this._endpoints){
            if (endpoint.ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex) {
                return true;
            }
            if (endpoint.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex) {
                return true;
            }
            if (endpoint.ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Neutral || endpoint.ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment) {
                if (endpoint.ownerBlock.outputs.some((o)=>o.isConnectedInVertexShader)) {
                    return true;
                }
            }
        }
        return false;
    }
    /** Gets a boolean indicating that this connection will be used in the fragment shader */ get isConnectedInFragmentShader() {
        if (this.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            return true;
        }
        if (!this.hasEndpoints) {
            return false;
        }
        for (const endpoint of this._endpoints){
            if (endpoint.ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
                return true;
            }
            if (endpoint.ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Neutral || endpoint.ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment) {
                if (endpoint.ownerBlock.isConnectedInFragmentShader()) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * Creates a block suitable to be used as an input for this input point.
     * If null is returned, a block based on the point type will be created.
     * @returns The returned string parameter is the name of the output point of NodeMaterialBlock (first parameter of the returned array) that can be connected to the input
     */ createCustomInputBlock() {
        return null;
    }
    /**
     * Gets the current class name e.g. "NodeMaterialConnectionPoint"
     * @returns the class name
     */ getClassName() {
        return "NodeMaterialConnectionPoint";
    }
    /**
     * Gets a boolean indicating if the current point can be connected to another point
     * @param connectionPoint defines the other connection point
     * @returns a boolean
     */ canConnectTo(connectionPoint) {
        return this.checkCompatibilityState(connectionPoint) === 0 /* NodeMaterialConnectionPointCompatibilityStates.Compatible */ ;
    }
    /**
     * Gets a number indicating if the current point can be connected to another point
     * @param connectionPoint defines the other connection point
     * @returns a number defining the compatibility state
     */ checkCompatibilityState(connectionPoint) {
        const ownerBlock = this._ownerBlock;
        const otherBlock = connectionPoint.ownerBlock;
        if (ownerBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) {
            // Let's check we are not going reverse
            if (otherBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex) {
                return 2 /* NodeMaterialConnectionPointCompatibilityStates.TargetIncompatible */ ;
            }
            for (const output of otherBlock.outputs){
                if (output.ownerBlock.target != __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Neutral && output.isConnectedInVertexShader) {
                    return 2 /* NodeMaterialConnectionPointCompatibilityStates.TargetIncompatible */ ;
                }
            }
        }
        if (this.type !== connectionPoint.type && connectionPoint.innerType !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].AutoDetect) {
            // Equivalents
            if (NodeMaterialConnectionPoint.AreEquivalentTypes(this.type, connectionPoint.type)) {
                return 0 /* NodeMaterialConnectionPointCompatibilityStates.Compatible */ ;
            }
            // Accepted types
            if (connectionPoint.acceptedConnectionPointTypes && connectionPoint.acceptedConnectionPointTypes.indexOf(this.type) !== -1 || connectionPoint._acceptedConnectionPointType && NodeMaterialConnectionPoint.AreEquivalentTypes(connectionPoint._acceptedConnectionPointType.type, this.type)) {
                return 0 /* NodeMaterialConnectionPointCompatibilityStates.Compatible */ ;
            } else {
                return 1 /* NodeMaterialConnectionPointCompatibilityStates.TypeIncompatible */ ;
            }
        }
        // Excluded
        if (connectionPoint.excludedConnectionPointTypes && connectionPoint.excludedConnectionPointTypes.indexOf(this.type) !== -1) {
            return 1 /* NodeMaterialConnectionPointCompatibilityStates.TypeIncompatible */ ;
        }
        // Check hierarchy
        let targetBlock = otherBlock;
        let sourceBlock = ownerBlock;
        if (this.direction === 0 /* NodeMaterialConnectionPointDirection.Input */ ) {
            targetBlock = ownerBlock;
            sourceBlock = otherBlock;
        }
        if (targetBlock.isAnAncestorOf(sourceBlock)) {
            return 3 /* NodeMaterialConnectionPointCompatibilityStates.HierarchyIssue */ ;
        }
        return 0 /* NodeMaterialConnectionPointCompatibilityStates.Compatible */ ;
    }
    /**
     * Connect this point to another connection point
     * @param connectionPoint defines the other connection point
     * @param ignoreConstraints defines if the system will ignore connection type constraints (default is false)
     * @returns the current connection point
     */ connectTo(connectionPoint) {
        let ignoreConstraints = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
        if (!ignoreConstraints && !this.canConnectTo(connectionPoint)) {
            // eslint-disable-next-line no-throw-literal
            throw 'Cannot connect these two connectors. source: "'.concat(this.ownerBlock.name, '".').concat(this.name, ', target: "').concat(connectionPoint.ownerBlock.name, '".').concat(connectionPoint.name);
        }
        this._endpoints.push(connectionPoint);
        connectionPoint._connectedPoint = this;
        this._enforceAssociatedVariableName = false;
        this.onConnectionObservable.notifyObservers(connectionPoint);
        connectionPoint.onConnectionObservable.notifyObservers(this);
        return this;
    }
    /**
     * Disconnect this point from one of his endpoint
     * @param endpoint defines the other connection point
     * @returns the current connection point
     */ disconnectFrom(endpoint) {
        const index = this._endpoints.indexOf(endpoint);
        if (index === -1) {
            return this;
        }
        this._endpoints.splice(index, 1);
        endpoint._connectedPoint = null;
        this._enforceAssociatedVariableName = false;
        endpoint._enforceAssociatedVariableName = false;
        this.onDisconnectionObservable.notifyObservers(endpoint);
        endpoint.onDisconnectionObservable.notifyObservers(this);
        return this;
    }
    /**
     * Fill the list of excluded connection point types with all types other than those passed in the parameter
     * @param mask Types (ORed values of NodeMaterialBlockConnectionPointTypes) that are allowed, and thus will not be pushed to the excluded list
     */ addExcludedConnectionPointFromAllowedTypes(mask) {
        let bitmask = 1;
        while(bitmask < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].All){
            if (!(mask & bitmask)) {
                this.excludedConnectionPointTypes.push(bitmask);
            }
            bitmask = bitmask << 1;
        }
    }
    /**
     * Serializes this point in a JSON representation
     * @param isInput defines if the connection point is an input (default is true)
     * @returns the serialized point object
     */ serialize() {
        let isInput = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;
        const serializationObject = {};
        serializationObject.name = this.name;
        if (this.displayName) {
            serializationObject.displayName = this.displayName;
        }
        if (isInput && this.connectedPoint) {
            serializationObject.inputName = this.name;
            serializationObject.targetBlockId = this.connectedPoint.ownerBlock.uniqueId;
            serializationObject.targetConnectionName = this.connectedPoint.name;
            serializationObject.isExposedOnFrame = true;
            serializationObject.exposedPortPosition = this.exposedPortPosition;
        }
        if (this.isExposedOnFrame || this.exposedPortPosition >= 0) {
            serializationObject.isExposedOnFrame = true;
            serializationObject.exposedPortPosition = this.exposedPortPosition;
        }
        return serializationObject;
    }
    /**
     * Release resources
     */ dispose() {
        this.onConnectionObservable.clear();
        this.onDisconnectionObservable.clear();
        this.onTypeChangedObservable.clear();
        this._connectedPoint = null;
        this._typeConnectionSource = null;
        this._linkedConnectionSource = null;
    }
    _updateTypeDependentState(update) {
        const previousType = this.type;
        update();
        if (this.type !== previousType) {
            this._notifyTypeChanged();
        }
    }
    _notifyTypeChanged() {
        // Disallow re-entrancy
        if (this._isTypeChangeObservableNotifying) {
            return;
        }
        this._isTypeChangeObservableNotifying = true;
        this.onTypeChangedObservable.notifyObservers(this.type);
        this._isTypeChangeObservableNotifying = false;
    }
    /**
     * Creates a new connection point
     * @param name defines the connection point name
     * @param ownerBlock defines the block hosting this connection point
     * @param direction defines the direction of the connection point
     */ constructor(name, ownerBlock, direction){
        /** @internal */ this._preventBubbleUp = false;
        this._connectedPointBackingField = null;
        this._endpoints = new Array();
        /** @internal */ this._redirectedSource = null;
        this._typeConnectionSourceBackingField = null;
        this._defaultConnectionPointTypeBackingField = null;
        /** @internal */ this._isMainLinkSource = false;
        this._linkedConnectionSourceBackingField = null;
        /** @internal */ this._acceptedConnectionPointType = null;
        this._type = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Float;
        /** @internal */ this._enforceAssociatedVariableName = false;
        /** @internal */ this._forPostBuild = false;
        /** Indicates that this connection point needs dual validation before being connected to another point */ this.needDualDirectionValidation = false;
        /**
         * Gets or sets the additional types supported by this connection point
         */ this.acceptedConnectionPointTypes = [];
        /**
         * Gets or sets the additional types excluded by this connection point
         */ this.excludedConnectionPointTypes = [];
        /**
         * Observable triggered when this point is connected
         */ this.onConnectionObservable = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$observable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Observable"]();
        /**
         * Observable triggered when this point is disconnected
         */ this.onDisconnectionObservable = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$observable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Observable"]();
        /**
         * Observable triggered when the type of the connection point is changed
         */ this.onTypeChangedObservable = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$observable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Observable"]();
        this._isTypeChangeObservableNotifying = false;
        /**
         * Gets or sets a boolean indicating that this connection point is exposed on a frame
         */ this.isExposedOnFrame = false;
        /**
         * Gets or sets number indicating the position that the port is exposed to on a frame
         */ this.exposedPortPosition = -1;
        /** @internal */ this._prioritizeVertex = false;
        this._target = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment;
        this._ownerBlock = ownerBlock;
        this.name = name;
        this._direction = direction;
    }
} //# sourceMappingURL=nodeMaterialBlockConnectionPoint.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "NodeMaterialBlock": ()=>NodeMaterialBlock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockConnectionPointTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlockConnectionPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlockConnectionPoint.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$uniqueIdGenerator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/uniqueIdGenerator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/logger.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$observable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/observable.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
class NodeMaterialBlock {
    /** @internal */ get _isFinalOutputAndActive() {
        return this._isFinalOutput;
    }
    /** @internal */ get _hasPrecedence() {
        return false;
    }
    /**
     * Gets the name of the block
     */ get name() {
        return this._name;
    }
    /**
     * Gets a boolean indicating that this block has is code ready to be used
     */ get codeIsReady() {
        return this._codeIsReady;
    }
    /**
     * Sets the name of the block. Will check if the name is valid.
     */ set name(newName) {
        if (!this.validateBlockName(newName)) {
            return;
        }
        this._name = newName;
    }
    /**
     * Gets a boolean indicating that this block can only be used once per NodeMaterial
     */ get isUnique() {
        return this._isUnique;
    }
    /**
     * Gets a boolean indicating that this block is an end block (e.g. it is generating a system value)
     */ get isFinalMerger() {
        return this._isFinalMerger;
    }
    /**
     * Gets a boolean indicating that this block is an input (e.g. it sends data to the shader)
     */ get isInput() {
        return this._isInput;
    }
    /**
     * Gets a boolean indicating if this block is a teleport out
     */ get isTeleportOut() {
        return this._isTeleportOut;
    }
    /**
     * Gets a boolean indicating if this block is a teleport in
     */ get isTeleportIn() {
        return this._isTeleportIn;
    }
    /**
     * Gets a boolean indicating if this block is a loop
     */ get isLoop() {
        return this._isLoop;
    }
    /**
     * Gets or sets the build Id
     */ get buildId() {
        return this._buildId;
    }
    set buildId(value) {
        this._buildId = value;
    }
    /**
     * Gets or sets the target of the block
     */ get target() {
        return this._target;
    }
    set target(value) {
        if ((this._target & value) !== 0) {
            return;
        }
        this._target = value;
    }
    /**
     * Gets the list of input points
     */ get inputs() {
        return this._inputs;
    }
    /** Gets the list of output points */ get outputs() {
        return this._outputs;
    }
    /**
     * Find an input by its name
     * @param name defines the name of the input to look for
     * @returns the input or null if not found
     */ getInputByName(name) {
        const filter = this._inputs.filter((e)=>e.name === name);
        if (filter.length) {
            return filter[0];
        }
        return null;
    }
    /**
     * Find an output by its name
     * @param name defines the name of the output to look for
     * @returns the output or null if not found
     */ getOutputByName(name) {
        const filter = this._outputs.filter((e)=>e.name === name);
        if (filter.length) {
            return filter[0];
        }
        return null;
    }
    /** @internal */ _setInitialTarget(target) {
        this._target = target;
        // marked as read only
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        this._originalTargetIsNeutral = target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Neutral;
    }
    /**
     * Initialize the block and prepare the context for build
     * @param state defines the state that will be used for the build
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    initialize(state) {
    // Do nothing
    }
    /**
     * Bind data to effect. Will only be called for blocks with isBindable === true
     * @param effect defines the effect to bind data to
     * @param nodeMaterial defines the hosting NodeMaterial
     * @param mesh defines the mesh that will be rendered
     * @param subMesh defines the submesh that will be rendered
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    bind(effect, nodeMaterial, mesh, subMesh) {
    // Do nothing
    }
    _writeVariable(currentPoint) {
        const connectionPoint = currentPoint.connectedPoint;
        if (connectionPoint) {
            return "".concat(currentPoint.associatedVariableName);
        }
        return "0.";
    }
    _writeFloat(value) {
        let stringVersion = value.toString();
        if (stringVersion.indexOf(".") === -1) {
            stringVersion += ".0";
        }
        return "".concat(stringVersion);
    }
    /**
     * Gets the current class name e.g. "NodeMaterialBlock"
     * @returns the class name
     */ getClassName() {
        return "NodeMaterialBlock";
    }
    /** Gets a boolean indicating that this connection will be used in the fragment shader
     * @returns true if connected in fragment shader
     */ isConnectedInFragmentShader() {
        return this.outputs.some((o)=>o.isConnectedInFragmentShader);
    }
    /**
     * Register a new input. Must be called inside a block constructor
     * @param name defines the connection point name
     * @param type defines the connection point type
     * @param isOptional defines a boolean indicating that this input can be omitted
     * @param target defines the target to use to limit the connection point (will be VertexAndFragment by default)
     * @param point an already created connection point. If not provided, create a new one
     * @returns the current block
     */ registerInput(name, type) {
        let isOptional = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false, target = arguments.length > 3 ? arguments[3] : void 0, point = arguments.length > 4 ? arguments[4] : void 0;
        point = point !== null && point !== void 0 ? point : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlockConnectionPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPoint"](name, this, 0 /* NodeMaterialConnectionPointDirection.Input */ );
        point.type = type;
        point.isOptional = isOptional;
        if (target) {
            point.target = target;
        }
        this._inputs.push(point);
        return this;
    }
    /**
     * Register a new output. Must be called inside a block constructor
     * @param name defines the connection point name
     * @param type defines the connection point type
     * @param target defines the target to use to limit the connection point (will be VertexAndFragment by default)
     * @param point an already created connection point. If not provided, create a new one
     * @returns the current block
     */ registerOutput(name, type, target, point) {
        point = point !== null && point !== void 0 ? point : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlockConnectionPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPoint"](name, this, 1 /* NodeMaterialConnectionPointDirection.Output */ );
        point.type = type;
        if (target) {
            point.target = target;
        }
        this._outputs.push(point);
        return this;
    }
    /**
     * Will return the first available input e.g. the first one which is not an uniform or an attribute
     * @param forOutput defines an optional connection point to check compatibility with
     * @returns the first available input or null
     */ getFirstAvailableInput() {
        let forOutput = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;
        for (const input of this._inputs){
            if (!input.connectedPoint) {
                if (!forOutput || forOutput.type === input.type || input.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].AutoDetect || input.acceptedConnectionPointTypes.indexOf(forOutput.type) !== -1) {
                    return input;
                }
            }
        }
        return null;
    }
    /**
     * Will return the first available output e.g. the first one which is not yet connected and not a varying
     * @param forBlock defines an optional block to check compatibility with
     * @returns the first available input or null
     */ getFirstAvailableOutput() {
        let forBlock = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;
        for (const output of this._outputs){
            if (!forBlock || !forBlock.target || forBlock.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Neutral || (forBlock.target & output.target) !== 0) {
                return output;
            }
        }
        return null;
    }
    /**
     * Gets the sibling of the given output
     * @param current defines the current output
     * @returns the next output in the list or null
     */ getSiblingOutput(current) {
        const index = this._outputs.indexOf(current);
        if (index === -1 || index >= this._outputs.length) {
            return null;
        }
        return this._outputs[index + 1];
    }
    /**
     * Checks if the current block is an ancestor of a given block
     * @param block defines the potential descendant block to check
     * @returns true if block is a descendant
     */ isAnAncestorOf(block) {
        for (const output of this._outputs){
            if (!output.hasEndpoints) {
                continue;
            }
            for (const endpoint of output.endpoints){
                if (endpoint.ownerBlock === block) {
                    return true;
                }
                if (endpoint.ownerBlock.isAnAncestorOf(block)) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * Connect current block with another block
     * @param other defines the block to connect with
     * @param options define the various options to help pick the right connections
     * @param options.input
     * @param options.output
     * @param options.outputSwizzle
     * @returns the current block
     */ connectTo(other, options) {
        if (this._outputs.length === 0) {
            return;
        }
        let output = options && options.output ? this.getOutputByName(options.output) : this.getFirstAvailableOutput(other);
        let notFound = true;
        while(notFound){
            const input = options && options.input ? other.getInputByName(options.input) : other.getFirstAvailableInput(output);
            if (output && input && output.canConnectTo(input)) {
                output.connectTo(input);
                notFound = false;
            } else if (!output) {
                // eslint-disable-next-line no-throw-literal
                throw "Unable to find a compatible match";
            } else {
                output = this.getSiblingOutput(output);
            }
        }
        return this;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _buildBlock(state) {
    // Empty. Must be defined by child nodes
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _postBuildBlock(state) {
    // Empty. Must be defined by child nodes
    }
    /**
     * Add uniforms, samplers and uniform buffers at compilation time
     * @param state defines the state to update
     * @param nodeMaterial defines the node material requesting the update
     * @param defines defines the material defines to update
     * @param uniformBuffers defines the list of uniform buffer names
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    updateUniformsAndSamples(state, nodeMaterial, defines, uniformBuffers) {
    // Do nothing
    }
    /**
     * Add potential fallbacks if shader compilation fails
     * @param fallbacks defines the current prioritized list of fallbacks
     * @param mesh defines the mesh to be rendered
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    provideFallbacks(fallbacks, mesh) {
    // Do nothing
    }
    /**
     * Initialize defines for shader compilation
     * @param defines defines the material defines to update
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    initializeDefines(defines) {
    // Do nothing
    }
    /**
     * Update defines for shader compilation
     * @param defines defines the material defines to update
     * @param nodeMaterial defines the node material requesting the update
     * @param mesh defines the mesh to be rendered
     * @param useInstances specifies that instances should be used
     * @param subMesh defines which submesh to render
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    prepareDefines(defines, nodeMaterial, mesh) {
        let useInstances = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false, subMesh = arguments.length > 4 ? arguments[4] : void 0;
    // Do nothing
    }
    /**
     * Lets the block try to connect some inputs automatically
     * @param material defines the hosting NodeMaterial
     * @param additionalFilteringInfo optional additional filtering condition when looking for compatible blocks
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    autoConfigure(material) {
        let additionalFilteringInfo = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : ()=>true;
    // Do nothing
    }
    /**
     * Function called when a block is declared as repeatable content generator
     * @param vertexShaderState defines the current compilation state for the vertex shader
     * @param defines defines the material defines to update
     * @param mesh defines the mesh to be rendered
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    replaceRepeatableContent(vertexShaderState, defines, mesh) {
    // Do nothing
    }
    /** Gets a boolean indicating that the code of this block will be promoted to vertex shader even if connected to fragment output */ get willBeGeneratedIntoVertexShaderFromFragmentShader() {
        if (this.isInput || this.isFinalMerger) {
            return false;
        }
        if (this._outputs.some((o)=>o.isDirectlyConnectedToVertexOutput)) {
            return false;
        }
        if (this.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex) {
            return false;
        }
        if (this.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment || this.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Neutral) {
            if (this._outputs.some((o)=>o.isConnectedInVertexShader)) {
                return true;
            }
        }
        return false;
    }
    /**
     * Checks if the block is ready
     * @param mesh defines the mesh to be rendered
     * @param nodeMaterial defines the node material requesting the update
     * @param defines defines the material defines to update
     * @param useInstances specifies that instances should be used
     * @returns true if the block is ready
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isReady(mesh, nodeMaterial, defines) {
        let useInstances = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
        return true;
    }
    _linkConnectionTypes(inputIndex0, inputIndex1) {
        let looseCoupling = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        if (looseCoupling) {
            this._inputs[inputIndex1]._acceptedConnectionPointType = this._inputs[inputIndex0];
        } else {
            this._inputs[inputIndex0]._linkedConnectionSource = this._inputs[inputIndex1];
            this._inputs[inputIndex0]._isMainLinkSource = true;
        }
        this._inputs[inputIndex1]._linkedConnectionSource = this._inputs[inputIndex0];
    }
    _processBuild(block, state, input, activeBlocks) {
        var _block_entryPoint;
        block.build(state, activeBlocks);
        const localBlockIsFragment = state._vertexState != null;
        const otherBlockWasGeneratedInVertexShader = block._buildTarget === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex && block.target !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment;
        if (block.isTeleportOut && ((_block_entryPoint = block.entryPoint) === null || _block_entryPoint === void 0 ? void 0 : _block_entryPoint.isConnectedToUniform)) {
            // In that case, we skip the context switch as the teleport out block is connected to a uniform
            return;
        }
        if (localBlockIsFragment && ((block.target & block._buildTarget) === 0 || (block.target & input.target) === 0 || this.target !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment && otherBlockWasGeneratedInVertexShader)) {
            // context switch! We need a varying
            if (!block.isInput && state.target !== block._buildTarget || block.isInput && block.isAttribute && !block._noContextSwitch // block is an attribute
            ) {
                const connectedPoint = input.connectedPoint;
                if (state._vertexState._emitVaryingFromString("v_" + connectedPoint.declarationVariableName, connectedPoint.type)) {
                    const prefix = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */  ? "vertexOutputs." : "";
                    if (state.shaderLanguage === 1 /* ShaderLanguage.WGSL */  && connectedPoint.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Matrix) {
                        // We can't pass a matrix as a varying in WGSL, so we need to split it into 4 vectors
                        state._vertexState.compilationString += "".concat(prefix).concat("v_" + connectedPoint.declarationVariableName, "_r0 = ").concat(connectedPoint.associatedVariableName, "[0];\n");
                        state._vertexState.compilationString += "".concat(prefix).concat("v_" + connectedPoint.declarationVariableName, "_r1 = ").concat(connectedPoint.associatedVariableName, "[1];\n");
                        state._vertexState.compilationString += "".concat(prefix).concat("v_" + connectedPoint.declarationVariableName, "_r2 = ").concat(connectedPoint.associatedVariableName, "[2];\n");
                        state._vertexState.compilationString += "".concat(prefix).concat("v_" + connectedPoint.declarationVariableName, "_r3 = ").concat(connectedPoint.associatedVariableName, "[3];\n");
                    } else {
                        state._vertexState.compilationString += "".concat(prefix).concat("v_" + connectedPoint.declarationVariableName, " = ").concat(connectedPoint.associatedVariableName, ";\n");
                    }
                }
                const prefix = state.shaderLanguage === 1 /* ShaderLanguage.WGSL */  && connectedPoint.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockConnectionPointTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockConnectionPointTypes"].Matrix ? "fragmentInputs." : "";
                input.associatedVariableName = prefix + "v_" + connectedPoint.declarationVariableName;
                input._enforceAssociatedVariableName = true;
            }
        }
    }
    /**
     * Validates the new name for the block node.
     * @param newName the new name to be given to the node.
     * @returns false if the name is a reserve word, else true.
     */ validateBlockName(newName) {
        const reservedNames = [
            "position",
            "normal",
            "tangent",
            "particle_positionw",
            "uv",
            "uv2",
            "uv3",
            "uv4",
            "uv5",
            "uv6",
            "position2d",
            "particle_uv",
            "postprocess_uv",
            "matricesIndices",
            "matricesWeights",
            "world0",
            "world1",
            "world2",
            "world3",
            "particle_color",
            "particle_texturemask"
        ];
        for (const reservedName of reservedNames){
            if (newName === reservedName) {
                return false;
            }
        }
        return true;
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _customBuildStep(state, activeBlocks) {
    // Must be implemented by children
    }
    /**
     * Compile the current node and generate the shader code
     * @param state defines the current compilation state (uniforms, samplers, current string)
     * @param activeBlocks defines the list of active blocks (i.e. blocks to compile)
     * @returns true if already built
     */ build(state, activeBlocks) {
        if (this._buildId === state.sharedData.buildId) {
            return true;
        }
        if (!this.isInput) {
            /** Prepare outputs */ for (const output of this._outputs){
                if (!output.associatedVariableName) {
                    output.associatedVariableName = state._getFreeVariableName(output.name);
                }
            }
        }
        // Check if "parent" blocks are compiled
        for (const input of this._inputs){
            if (!input.connectedPoint) {
                if (!input.isOptional) {
                    // Emit a warning
                    state.sharedData.checks.notConnectedNonOptionalInputs.push(input);
                }
                continue;
            }
            if (this.target !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Neutral) {
                if ((input.target & this.target) === 0) {
                    continue;
                }
                if ((input.target & state.target) === 0) {
                    continue;
                }
            }
            const block = input.connectedPoint.ownerBlock;
            if (block && block !== this) {
                this._processBuild(block, state, input, activeBlocks);
            }
        }
        this._customBuildStep(state, activeBlocks);
        if (this._buildId === state.sharedData.buildId) {
            return true; // Need to check again as inputs can be connected multiple time to this endpoint
        }
        // Logs
        if (state.sharedData.verbose) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Log("".concat(state.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex ? "Vertex shader" : "Fragment shader", ": Building ").concat(this.name, " [").concat(this.getClassName(), "]"));
        }
        // Checks final outputs
        if (this.isFinalMerger) {
            switch(state.target){
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex:
                    state.sharedData.checks.emitVertex = true;
                    break;
                case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment:
                    state.sharedData.checks.emitFragment = true;
                    break;
            }
        }
        if (!this.isInput && state.sharedData.emitComments) {
            state.compilationString += "\n//".concat(this.name, "\n");
        }
        this._buildBlock(state);
        this._buildId = state.sharedData.buildId;
        this._buildTarget = state.target;
        // Compile connected blocks
        for (const output of this._outputs){
            if (output._forPostBuild) {
                continue;
            }
            if ((output.target & state.target) === 0) {
                continue;
            }
            for (const endpoint of output.endpoints){
                const block = endpoint.ownerBlock;
                if (block) {
                    if ((block.target & state.target) !== 0 && activeBlocks.indexOf(block) !== -1 || state._terminalBlocks.has(block)) {
                        this._processBuild(block, state, endpoint, activeBlocks);
                    }
                }
            }
        }
        this._postBuildBlock(state);
        // Compile post build connected blocks
        for (const output of this._outputs){
            if (!output._forPostBuild) {
                continue;
            }
            if ((output.target & state.target) === 0) {
                continue;
            }
            for (const endpoint of output.endpoints){
                const block = endpoint.ownerBlock;
                if (block && (block.target & state.target) !== 0 && activeBlocks.indexOf(block) !== -1) {
                    this._processBuild(block, state, endpoint, activeBlocks);
                }
            }
        }
        return false;
    }
    _inputRename(name) {
        return name;
    }
    _outputRename(name) {
        return name;
    }
    _dumpPropertiesCode() {
        const variableName = this._codeVariableName;
        return "".concat(variableName, ".visibleInInspector = ").concat(this.visibleInInspector, ";\n").concat(variableName, ".visibleOnFrame = ").concat(this.visibleOnFrame, ";\n").concat(variableName, ".target = ").concat(this.target, ";\n");
    }
    /**
     * @internal
     */ _dumpCode(uniqueNames, alreadyDumped) {
        alreadyDumped.push(this);
        // Get unique name
        const nameAsVariableName = this.name.replace(/[^A-Za-z_]+/g, "");
        this._codeVariableName = nameAsVariableName || "".concat(this.getClassName(), "_").concat(this.uniqueId);
        if (uniqueNames.indexOf(this._codeVariableName) !== -1) {
            let index = 0;
            do {
                index++;
                this._codeVariableName = nameAsVariableName + index;
            }while (uniqueNames.indexOf(this._codeVariableName) !== -1)
        }
        uniqueNames.push(this._codeVariableName);
        // Declaration
        let codeString = "\n// ".concat(this.getClassName(), "\n");
        if (this.comments) {
            codeString += "// ".concat(this.comments, "\n");
        }
        codeString += "var ".concat(this._codeVariableName, " = new BABYLON.").concat(this.getClassName(), '("').concat(this.name, '");\n');
        // Properties
        codeString += this._dumpPropertiesCode();
        // Inputs
        for (const input of this.inputs){
            if (!input.isConnected) {
                continue;
            }
            const connectedOutput = input.connectedPoint;
            const connectedBlock = connectedOutput.ownerBlock;
            if (alreadyDumped.indexOf(connectedBlock) === -1) {
                codeString += connectedBlock._dumpCode(uniqueNames, alreadyDumped);
            }
        }
        // Outputs
        for (const output of this.outputs){
            if (!output.hasEndpoints) {
                continue;
            }
            for (const endpoint of output.endpoints){
                const connectedBlock = endpoint.ownerBlock;
                if (connectedBlock && alreadyDumped.indexOf(connectedBlock) === -1) {
                    codeString += connectedBlock._dumpCode(uniqueNames, alreadyDumped);
                }
            }
        }
        return codeString;
    }
    /**
     * @internal
     */ _dumpCodeForOutputConnections(alreadyDumped) {
        let codeString = "";
        if (alreadyDumped.indexOf(this) !== -1) {
            return codeString;
        }
        alreadyDumped.push(this);
        for (const input of this.inputs){
            if (!input.isConnected) {
                continue;
            }
            const connectedOutput = input.connectedPoint;
            const connectedBlock = connectedOutput.ownerBlock;
            codeString += connectedBlock._dumpCodeForOutputConnections(alreadyDumped);
            codeString += "".concat(connectedBlock._codeVariableName, ".").concat(connectedBlock._outputRename(connectedOutput.name), ".connectTo(").concat(this._codeVariableName, ".").concat(this._inputRename(input.name), ");\n");
        }
        return codeString;
    }
    /**
     * Clone the current block to a new identical block
     * @param scene defines the hosting scene
     * @param rootUrl defines the root URL to use to load textures and relative dependencies
     * @returns a copy of the current block
     */ clone(scene) {
        let rootUrl = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "";
        const serializationObject = this.serialize();
        const blockType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GetClass"])(serializationObject.customType);
        if (blockType) {
            const block = new blockType();
            block._deserialize(serializationObject, scene, rootUrl);
            return block;
        }
        return null;
    }
    /**
     * Serializes this block in a JSON representation
     * @returns the serialized block object
     */ serialize() {
        const serializationObject = {};
        serializationObject.customType = "BABYLON." + this.getClassName();
        serializationObject.id = this.uniqueId;
        serializationObject.name = this.name;
        serializationObject.comments = this.comments;
        serializationObject.visibleInInspector = this.visibleInInspector;
        serializationObject.visibleOnFrame = this.visibleOnFrame;
        serializationObject.target = this.target;
        serializationObject.inputs = [];
        serializationObject.outputs = [];
        for (const input of this.inputs){
            serializationObject.inputs.push(input.serialize());
        }
        for (const output of this.outputs){
            serializationObject.outputs.push(output.serialize(false));
        }
        return serializationObject;
    }
    /**
     * @internal
     */ // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _deserialize(serializationObject, scene, rootUrl, urlRewriter) {
        this.name = serializationObject.name;
        this.comments = serializationObject.comments;
        this.visibleInInspector = !!serializationObject.visibleInInspector;
        this.visibleOnFrame = !!serializationObject.visibleOnFrame;
        var _serializationObject_target;
        this._target = (_serializationObject_target = serializationObject.target) !== null && _serializationObject_target !== void 0 ? _serializationObject_target : this.target;
        this._deserializePortDisplayNamesAndExposedOnFrame(serializationObject);
    }
    _deserializePortDisplayNamesAndExposedOnFrame(serializationObject) {
        const serializedInputs = serializationObject.inputs;
        const serializedOutputs = serializationObject.outputs;
        if (serializedInputs) {
            for(let i = 0; i < serializedInputs.length; i++){
                const port = serializedInputs[i];
                if (port.displayName) {
                    this.inputs[i].displayName = port.displayName;
                }
                if (port.isExposedOnFrame) {
                    this.inputs[i].isExposedOnFrame = port.isExposedOnFrame;
                    this.inputs[i].exposedPortPosition = port.exposedPortPosition;
                }
            }
        }
        if (serializedOutputs) {
            for(let i = 0; i < serializedOutputs.length; i++){
                const port = serializedOutputs[i];
                if (port.displayName) {
                    this.outputs[i].displayName = port.displayName;
                }
                if (port.isExposedOnFrame) {
                    this.outputs[i].isExposedOnFrame = port.isExposedOnFrame;
                    this.outputs[i].exposedPortPosition = port.exposedPortPosition;
                }
            }
        }
    }
    /**
     * Release resources
     */ dispose() {
        this.onCodeIsReadyObservable.clear();
        for (const input of this.inputs){
            input.dispose();
        }
        for (const output of this.outputs){
            output.dispose();
        }
    }
    /**
     * Creates a new NodeMaterialBlock
     * @param name defines the block name
     * @param target defines the target of that block (Vertex by default)
     * @param isFinalMerger defines a boolean indicating that this block is an end block (e.g. it is generating a system value). Default is false
     * @param isFinalOutput defines a boolean indicating that this block is generating a final output and no other block should be generated after
     */ constructor(name, target = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex, isFinalMerger = false, isFinalOutput = false){
        this._isFinalMerger = false;
        this._isInput = false;
        this._isLoop = false;
        this._isTeleportOut = false;
        this._isTeleportIn = false;
        this._name = "";
        this._isUnique = false;
        this._codeIsReady = true;
        /** @internal */ this._isFinalOutput = false;
        /**
         * Observable raised when the block code is ready (if the code loading is async)
         */ this.onCodeIsReadyObservable = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$observable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Observable"]();
        /** Gets or sets a boolean indicating that only one input can be connected at a time */ this.inputsAreExclusive = false;
        /** @internal */ this._codeVariableName = "";
        /** @internal */ this._inputs = new Array();
        /** @internal */ this._outputs = new Array();
        /**
         * Gets or sets the comments associated with this block
         */ this.comments = "";
        /** Gets or sets a boolean indicating that this input can be edited in the Inspector (false by default) */ this.visibleInInspector = false;
        /** Gets or sets a boolean indicating that this input can be edited from a collapsed frame */ this.visibleOnFrame = false;
        this._target = target;
        this._originalTargetIsNeutral = target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Neutral;
        this._isFinalMerger = isFinalMerger;
        this._isFinalOutput = isFinalOutput;
        switch(this.getClassName()){
            case "InputBlock":
                this._isInput = true;
                break;
            case "NodeMaterialTeleportOutBlock":
                this._isTeleportOut = true;
                break;
            case "NodeMaterialTeleportInBlock":
                this._isTeleportIn = true;
                break;
            case "LoopBlock":
                this._isLoop = true;
                break;
        }
        this._name = name;
        this.uniqueId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$uniqueIdGenerator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UniqueIdGenerator"].UniqueId;
    }
} //# sourceMappingURL=nodeMaterialBlock.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterial.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "NodeMaterial": ()=>NodeMaterial,
    "NodeMaterialDefines": ()=>NodeMaterialDefines
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/tslib.es6.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$pushMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/pushMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.vector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.color.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBuildState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBuildState.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/effect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$observable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/observable.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialBlockTargets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBuildStateSharedData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBuildStateSharedData.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialDefines$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/materialDefines.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Buffers$2f$buffer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Buffers/buffer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$tools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/tools.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$smartFilterFragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/smartFilterFragmentOutputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$transformBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/transformBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Vertex$2f$vertexOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Vertex/vertexOutputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$fragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/fragmentOutputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Input/inputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/typeStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/decorators.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/decorators.serialization.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Dual$2f$currentScreenBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/currentScreenBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Particle$2f$particleTextureBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Particle/particleTextureBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Particle$2f$particleRampGradientBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Particle/particleRampGradientBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Particle$2f$particleBlendMultiplyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Particle/particleBlendMultiplyBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effectFallbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/effectFallbacks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$webRequest$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/webRequest.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/PostProcesses/postProcess.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$vectorMergerBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/vectorMergerBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$remapBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/remapBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$multiplyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/multiplyBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialModes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/texture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$baseParticleSystem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Particles/baseParticleSystem.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$colorSplitterBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/colorSplitterBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$timingTools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/timingTools.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$Procedurals$2f$proceduralTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/Procedurals/proceduralTexture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$animatedInputBlockTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Input/animatedInputBlockTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$trigonometryBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/trigonometryBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialSystemValues.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$engineStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/engineStore.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/logger.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/materialHelper.functions.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$abstractEngine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/abstractEngine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$geometryrendering$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/materialHelper.geometryrendering.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const onCreatedEffectParameters = {
    effect: null,
    subMesh: null
};
class NodeMaterialDefines extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialDefines$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaterialDefines"] {
    /**
     * Set the value of a specific key
     * @param name defines the name of the key to set
     * @param value defines the value to set
     * @param markAsUnprocessedIfDirty Flag to indicate to the cache that this value needs processing
     */ setValue(name, value) {
        let markAsUnprocessedIfDirty = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        if (this[name] === undefined) {
            this._keys.push(name);
        }
        if (markAsUnprocessedIfDirty && this[name] !== value) {
            this.markAsUnprocessed();
        }
        this[name] = value;
    }
    /**
     * Creates a new NodeMaterialDefines
     */ constructor(){
        super();
        /** Normal */ this.NORMAL = false;
        /** Tangent */ this.TANGENT = false;
        /** Vertex color */ this.VERTEXCOLOR_NME = false;
        /**  Uv1 **/ this.UV1 = false;
        /** Uv2 **/ this.UV2 = false;
        /** Uv3 **/ this.UV3 = false;
        /** Uv4 **/ this.UV4 = false;
        /** Uv5 **/ this.UV5 = false;
        /** Uv6 **/ this.UV6 = false;
        /** Prepass **/ this.PREPASS = false;
        /** Prepass normal */ this.PREPASS_NORMAL = false;
        /** Prepass normal index */ this.PREPASS_NORMAL_INDEX = -1;
        /** Prepass world normal */ this.PREPASS_WORLD_NORMAL = false;
        /** Prepass world normal index */ this.PREPASS_WORLD_NORMAL_INDEX = -1;
        /** Prepass position */ this.PREPASS_POSITION = false;
        /** Prepass position index */ this.PREPASS_POSITION_INDEX = -1;
        /** Prepass local position */ this.PREPASS_LOCAL_POSITION = false;
        /** Prepass local position index */ this.PREPASS_LOCAL_POSITION_INDEX = -1;
        /** Prepass depth */ this.PREPASS_DEPTH = false;
        /** Prepass depth index */ this.PREPASS_DEPTH_INDEX = -1;
        /** Clip-space depth */ this.PREPASS_SCREENSPACE_DEPTH = false;
        /** Clip-space depth index */ this.PREPASS_SCREENSPACE_DEPTH_INDEX = -1;
        /** Scene MRT count */ this.SCENE_MRT_COUNT = 0;
        /** BONES */ this.NUM_BONE_INFLUENCERS = 0;
        /** Bones per mesh */ this.BonesPerMesh = 0;
        /** Using texture for bone storage */ this.BONETEXTURE = false;
        /** MORPH TARGETS */ this.MORPHTARGETS = false;
        /** Morph target position */ this.MORPHTARGETS_POSITION = false;
        /** Morph target normal */ this.MORPHTARGETS_NORMAL = false;
        /** Morph target tangent */ this.MORPHTARGETS_TANGENT = false;
        /** Morph target uv */ this.MORPHTARGETS_UV = false;
        /** Morph target uv2 */ this.MORPHTARGETS_UV2 = false;
        this.MORPHTARGETS_COLOR = false;
        /** Morph target support positions */ this.MORPHTARGETTEXTURE_HASPOSITIONS = false;
        /** Morph target support normals */ this.MORPHTARGETTEXTURE_HASNORMALS = false;
        /** Morph target support tangents */ this.MORPHTARGETTEXTURE_HASTANGENTS = false;
        /** Morph target support uvs */ this.MORPHTARGETTEXTURE_HASUVS = false;
        /** Morph target support uv2s */ this.MORPHTARGETTEXTURE_HASUV2S = false;
        this.MORPHTARGETTEXTURE_HASCOLORS = false;
        /** Number of morph influencers */ this.NUM_MORPH_INFLUENCERS = 0;
        /** Using a texture to store morph target data */ this.MORPHTARGETS_TEXTURE = false;
        /** IMAGE PROCESSING */ this.IMAGEPROCESSING = false;
        /** Vignette */ this.VIGNETTE = false;
        /** Multiply blend mode for vignette */ this.VIGNETTEBLENDMODEMULTIPLY = false;
        /** Opaque blend mode for vignette */ this.VIGNETTEBLENDMODEOPAQUE = false;
        /** Tone mapping */ this.TONEMAPPING = 0;
        /** Contrast */ this.CONTRAST = false;
        /** Exposure */ this.EXPOSURE = false;
        /** Color curves */ this.COLORCURVES = false;
        /** Color grading */ this.COLORGRADING = false;
        /** 3D color grading */ this.COLORGRADING3D = false;
        /** Sampler green depth */ this.SAMPLER3DGREENDEPTH = false;
        /** Sampler for BGR map */ this.SAMPLER3DBGRMAP = false;
        /** Dithering */ this.DITHER = false;
        /** Using post process for image processing */ this.IMAGEPROCESSINGPOSTPROCESS = false;
        /** Skip color clamp */ this.SKIPFINALCOLORCLAMP = false;
        /** MISC. */ this.BUMPDIRECTUV = 0;
        /** Camera is orthographic */ this.CAMERA_ORTHOGRAPHIC = false;
        /** Camera is perspective */ this.CAMERA_PERSPECTIVE = false;
        this.AREALIGHTSUPPORTED = true;
        this.AREALIGHTNOROUGHTNESS = true;
        this.rebuild();
    }
}
class NodeMaterial extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$pushMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PushMaterial"] {
    /**
     * Checks if a block is a texture block
     * @param block The block to check
     * @returns True if the block is a texture block
     */ static _BlockIsTextureBlock(block) {
        return block.getClassName() === "TextureBlock" || block.getClassName() === "ReflectionTextureBaseBlock" || block.getClassName() === "ReflectionTextureBlock" || block.getClassName() === "ReflectionBlock" || block.getClassName() === "RefractionBlock" || block.getClassName() === "CurrentScreenBlock" || block.getClassName() === "SmartFilterTextureBlock" || block.getClassName() === "ParticleTextureBlock" || block.getClassName() === "ImageSourceBlock" || block.getClassName() === "TriPlanarBlock" || block.getClassName() === "BiPlanarBlock" || block.getClassName() === "PrePassTextureBlock";
    }
    set _glowModeEnabled(value) {
        this._useAdditionalColor = value;
    }
    /** Get the inspector from bundle or global
     * @returns the global NME
     */ _getGlobalNodeMaterialEditor() {
        // UMD Global name detection from Webpack Bundle UMD Name.
        if (typeof NODEEDITOR !== "undefined") {
            return NODEEDITOR;
        }
        // In case of module let's check the global emitted from the editor entry point.
        if (typeof BABYLON !== "undefined" && typeof BABYLON.NodeEditor !== "undefined") {
            return BABYLON;
        }
        return undefined;
    }
    /** Gets or sets the active shader language */ get shaderLanguage() {
        var _this__options;
        return ((_this__options = this._options) === null || _this__options === void 0 ? void 0 : _this__options.shaderLanguage) || NodeMaterial.DefaultShaderLanguage;
    }
    set shaderLanguage(value) {
        this._options.shaderLanguage = value;
    }
    /** Gets or sets options to control the node material overall behavior */ get options() {
        return this._options;
    }
    set options(options) {
        this._options = options;
    }
    /**
     * Gets the image processing configuration used either in this material.
     */ get imageProcessingConfiguration() {
        return this._imageProcessingConfiguration;
    }
    /**
     * Sets the Default image processing configuration used either in the this material.
     *
     * If sets to null, the scene one is in use.
     */ set imageProcessingConfiguration(value) {
        this._attachImageProcessingConfiguration(value);
        // Ensure the effect will be rebuilt.
        this._markAllSubMeshesAsTexturesDirty();
    }
    /**
     * Gets or sets the mode property
     */ get mode() {
        return this._mode;
    }
    set mode(value) {
        this._mode = value;
    }
    /** Gets or sets the unique identifier used to identified the effect associated with the material */ get buildId() {
        return this._buildId;
    }
    set buildId(value) {
        this._buildId = value;
    }
    /**
     * Gets the current class name of the material e.g. "NodeMaterial"
     * @returns the class name
     */ getClassName() {
        return "NodeMaterial";
    }
    /**
     * Attaches a new image processing configuration to the Standard Material.
     * @param configuration
     */ _attachImageProcessingConfiguration(configuration) {
        if (configuration === this._imageProcessingConfiguration) {
            return;
        }
        // Detaches observer.
        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {
            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);
        }
        // Pick the scene configuration if needed.
        if (!configuration) {
            this._imageProcessingConfiguration = this.getScene().imageProcessingConfiguration;
        } else {
            this._imageProcessingConfiguration = configuration;
        }
        // Attaches observer.
        if (this._imageProcessingConfiguration) {
            this._imageProcessingObserver = this._imageProcessingConfiguration.onUpdateParameters.add(()=>{
                this._markAllSubMeshesAsImageProcessingDirty();
            });
        }
    }
    /**
     * Get a block by its name
     * @param name defines the name of the block to retrieve
     * @returns the required block or null if not found
     */ getBlockByName(name) {
        let result = null;
        for (const block of this.attachedBlocks){
            if (block.name === name) {
                if (!result) {
                    result = block;
                } else {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$tools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tools"].Warn("More than one block was found with the name `" + name + "`");
                    return result;
                }
            }
        }
        return result;
    }
    /**
     * Get a block using a predicate
     * @param predicate defines the predicate used to find the good candidate
     * @returns the required block or null if not found
     */ getBlockByPredicate(predicate) {
        for (const block of this.attachedBlocks){
            if (predicate(block)) {
                return block;
            }
        }
        return null;
    }
    /**
     * Get an input block using a predicate
     * @param predicate defines the predicate used to find the good candidate
     * @returns the required input block or null if not found
     */ getInputBlockByPredicate(predicate) {
        for (const block of this.attachedBlocks){
            if (block.isInput && predicate(block)) {
                return block;
            }
        }
        return null;
    }
    /**
     * Gets the list of input blocks attached to this material
     * @returns an array of InputBlocks
     */ getInputBlocks() {
        const blocks = [];
        for (const block of this.attachedBlocks){
            if (block.isInput) {
                blocks.push(block);
            }
        }
        return blocks;
    }
    /**
     * Adds a new optimizer to the list of optimizers
     * @param optimizer defines the optimizers to add
     * @returns the current material
     */ registerOptimizer(optimizer) {
        const index = this._optimizers.indexOf(optimizer);
        if (index > -1) {
            return;
        }
        this._optimizers.push(optimizer);
        return this;
    }
    /**
     * Remove an optimizer from the list of optimizers
     * @param optimizer defines the optimizers to remove
     * @returns the current material
     */ unregisterOptimizer(optimizer) {
        const index = this._optimizers.indexOf(optimizer);
        if (index === -1) {
            return;
        }
        this._optimizers.splice(index, 1);
        return this;
    }
    /**
     * Add a new block to the list of output nodes
     * @param node defines the node to add
     * @returns the current material
     */ addOutputNode(node) {
        if (node.target === null) {
            // eslint-disable-next-line no-throw-literal
            throw "This node is not meant to be an output node. You may want to explicitly set its target value.";
        }
        if ((node.target & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex) !== 0) {
            this._addVertexOutputNode(node);
        }
        if ((node.target & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) !== 0) {
            this._addFragmentOutputNode(node);
        }
        return this;
    }
    /**
     * Remove a block from the list of root nodes
     * @param node defines the node to remove
     * @returns the current material
     */ removeOutputNode(node) {
        if (node.target === null) {
            return this;
        }
        if ((node.target & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex) !== 0) {
            this._removeVertexOutputNode(node);
        }
        if ((node.target & __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment) !== 0) {
            this._removeFragmentOutputNode(node);
        }
        return this;
    }
    _addVertexOutputNode(node) {
        if (this._vertexOutputNodes.indexOf(node) !== -1) {
            return;
        }
        node.target = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex;
        this._vertexOutputNodes.push(node);
        return this;
    }
    _removeVertexOutputNode(node) {
        const index = this._vertexOutputNodes.indexOf(node);
        if (index === -1) {
            return;
        }
        this._vertexOutputNodes.splice(index, 1);
        return this;
    }
    _addFragmentOutputNode(node) {
        if (this._fragmentOutputNodes.indexOf(node) !== -1) {
            return;
        }
        node.target = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment;
        this._fragmentOutputNodes.push(node);
        return this;
    }
    _removeFragmentOutputNode(node) {
        const index = this._fragmentOutputNodes.indexOf(node);
        if (index === -1) {
            return;
        }
        this._fragmentOutputNodes.splice(index, 1);
        return this;
    }
    get _supportGlowLayer() {
        if (this._fragmentOutputNodes.length === 0) {
            return false;
        }
        if (this._fragmentOutputNodes.some((f)=>f.additionalColor && f.additionalColor.isConnected)) {
            return true;
        }
        return false;
    }
    /**
     * Specifies if the material will require alpha blending
     * @returns a boolean specifying if alpha blending is needed
     */ needAlphaBlending() {
        if (this.ignoreAlpha) {
            return false;
        }
        return this.forceAlphaBlending || this.alpha < 1.0 || this._sharedData && this._sharedData.hints.needAlphaBlending;
    }
    /**
     * Specifies if this material should be rendered in alpha test mode
     * @returns a boolean specifying if an alpha test is needed.
     */ needAlphaTesting() {
        return this._sharedData && this._sharedData.hints.needAlphaTesting;
    }
    _processInitializeOnLink(block, state, nodesToProcessForOtherBuildState) {
        let autoConfigure = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : true;
        if (block.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment) {
            nodesToProcessForOtherBuildState.push(block);
        } else if (state.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment && block.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex && block._preparationId !== this._buildId) {
            nodesToProcessForOtherBuildState.push(block);
        }
        this._initializeBlock(block, state, nodesToProcessForOtherBuildState, autoConfigure);
    }
    _attachBlock(node) {
        if (this.attachedBlocks.indexOf(node) === -1) {
            if (node.isUnique) {
                const className = node.getClassName();
                for (const other of this.attachedBlocks){
                    if (other.getClassName() === className) {
                        this._sharedData.raiseBuildError("Cannot have multiple blocks of type ".concat(className, " in the same NodeMaterial"));
                        return;
                    }
                }
            }
            this.attachedBlocks.push(node);
        }
    }
    _initializeBlock(node, state, nodesToProcessForOtherBuildState) {
        let autoConfigure = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : true;
        node.initialize(state);
        if (autoConfigure) {
            node.autoConfigure(this);
        }
        node._preparationId = this._buildId;
        this._attachBlock(node);
        for (const input of node.inputs){
            input.associatedVariableName = "";
            const connectedPoint = input.connectedPoint;
            if (connectedPoint && !connectedPoint._preventBubbleUp) {
                const block = connectedPoint.ownerBlock;
                if (block !== node) {
                    this._processInitializeOnLink(block, state, nodesToProcessForOtherBuildState, autoConfigure);
                }
            }
        }
        // Loop
        if (node.isLoop) {
            // We need to keep the storage write block in the active blocks
            const loopBlock = node;
            if (loopBlock.loopID.hasEndpoints) {
                for (const endpoint of loopBlock.loopID.endpoints){
                    const block = endpoint.ownerBlock;
                    if (block.outputs.length !== 0) {
                        continue;
                    }
                    state._terminalBlocks.add(block); // Attach the storage write only
                    this._processInitializeOnLink(block, state, nodesToProcessForOtherBuildState, autoConfigure);
                }
            }
        } else if (node.isTeleportOut) {
            // Teleportation
            const teleport = node;
            if (teleport.entryPoint) {
                this._processInitializeOnLink(teleport.entryPoint, state, nodesToProcessForOtherBuildState, autoConfigure);
            }
        }
        for (const output of node.outputs){
            output.associatedVariableName = "";
        }
    }
    _resetDualBlocks(node, id) {
        if (node.target === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].VertexAndFragment) {
            node.buildId = id;
        }
        for (const input of node.inputs){
            const connectedPoint = input.connectedPoint;
            if (connectedPoint && !connectedPoint._preventBubbleUp) {
                const block = connectedPoint.ownerBlock;
                if (block !== node) {
                    this._resetDualBlocks(block, id);
                }
            }
        }
        // If this is a teleport out, we need to reset the connected block
        if (node.isTeleportOut) {
            const teleportOut = node;
            if (teleportOut.entryPoint) {
                this._resetDualBlocks(teleportOut.entryPoint, id);
            }
        } else if (node.isLoop) {
            // Loop
            const loopBlock = node;
            if (loopBlock.loopID.hasEndpoints) {
                for (const endpoint of loopBlock.loopID.endpoints){
                    const block = endpoint.ownerBlock;
                    if (block.outputs.length !== 0) {
                        continue;
                    }
                    this._resetDualBlocks(block, id);
                }
            }
        }
    }
    /**
     * Remove a block from the current node material
     * @param block defines the block to remove
     */ removeBlock(block) {
        const attachedBlockIndex = this.attachedBlocks.indexOf(block);
        if (attachedBlockIndex > -1) {
            this.attachedBlocks.splice(attachedBlockIndex, 1);
        }
        if (block.isFinalMerger) {
            this.removeOutputNode(block);
        }
    }
    /**
     * Build the material and generates the inner effect
     * @param verbose defines if the build should log activity
     * @param updateBuildId defines if the internal build Id should be updated (default is true)
     * @param autoConfigure defines if the autoConfigure method should be called when initializing blocks (default is false)
     */ build() {
        let verbose = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false, updateBuildId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true, autoConfigure = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        if (this._buildIsInProgress) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Warn("Build is already in progress, You can use NodeMaterial.onBuildObservable to determine when the build is completed.");
            return;
        }
        this._buildIsInProgress = true;
        // First time?
        if (!this._vertexCompilationState && !autoConfigure) {
            autoConfigure = true;
        }
        this._buildWasSuccessful = false;
        const engine = this.getScene().getEngine();
        const allowEmptyVertexProgram = this._mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].Particle || this._mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].SFE;
        if (this._vertexOutputNodes.length === 0 && !allowEmptyVertexProgram) {
            this.onBuildErrorObservable.notifyObservers("You must define at least one vertexOutputNode");
            this._buildIsInProgress = false;
            return;
        }
        if (this._fragmentOutputNodes.length === 0) {
            this.onBuildErrorObservable.notifyObservers("You must define at least one fragmentOutputNode");
            this._buildIsInProgress = false;
            return;
        }
        // Compilation state
        this._vertexCompilationState = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBuildState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBuildState"]();
        this._vertexCompilationState.supportUniformBuffers = engine.supportsUniformBuffers;
        this._vertexCompilationState.target = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Vertex;
        this._fragmentCompilationState = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBuildState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBuildState"]();
        this._fragmentCompilationState.supportUniformBuffers = engine.supportsUniformBuffers;
        this._fragmentCompilationState.target = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialBlockTargets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBlockTargets"].Fragment;
        // Shared data
        const needToPurgeList = this._fragmentOutputNodes.filter((n)=>n._isFinalOutputAndActive).length > 1;
        let fragmentOutputNodes = this._fragmentOutputNodes;
        if (needToPurgeList) {
            // Get all but the final output nodes
            fragmentOutputNodes = this._fragmentOutputNodes.filter((n)=>!n._isFinalOutputAndActive);
            // Get the first with precedence on
            fragmentOutputNodes.push(this._fragmentOutputNodes.filter((n)=>n._isFinalOutputAndActive && n._hasPrecedence)[0]);
        }
        this._sharedData = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBuildStateSharedData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialBuildStateSharedData"]();
        this._sharedData.nodeMaterial = this;
        this._sharedData.fragmentOutputNodes = fragmentOutputNodes;
        this._vertexCompilationState.sharedData = this._sharedData;
        this._fragmentCompilationState.sharedData = this._sharedData;
        this._sharedData.buildId = this._buildId;
        this._sharedData.emitComments = this._options.emitComments;
        this._sharedData.verbose = verbose;
        this._sharedData.scene = this.getScene();
        this._sharedData.allowEmptyVertexProgram = allowEmptyVertexProgram;
        // Initialize blocks
        const vertexNodes = [];
        const fragmentNodes = [];
        for (const vertexOutputNode of this._vertexOutputNodes){
            vertexNodes.push(vertexOutputNode);
            this._initializeBlock(vertexOutputNode, this._vertexCompilationState, fragmentNodes, autoConfigure);
        }
        for (const fragmentOutputNode of fragmentOutputNodes){
            fragmentNodes.push(fragmentOutputNode);
            this._initializeBlock(fragmentOutputNode, this._fragmentCompilationState, vertexNodes, autoConfigure);
        }
        // Are blocks code ready?
        let waitingNodeCount = 0;
        for (const node of this.attachedBlocks){
            if (!node.codeIsReady) {
                waitingNodeCount++;
                node.onCodeIsReadyObservable.addOnce(()=>{
                    waitingNodeCount--;
                    if (waitingNodeCount === 0) {
                        this._finishBuildProcess(verbose, updateBuildId, vertexNodes, fragmentNodes);
                    }
                });
            }
        }
        if (waitingNodeCount !== 0) {
            return;
        }
        this._finishBuildProcess(verbose, updateBuildId, vertexNodes, fragmentNodes);
    }
    _finishBuildProcess() {
        let verbose = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false, updateBuildId = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true, vertexNodes = arguments.length > 2 ? arguments[2] : void 0, fragmentNodes = arguments.length > 3 ? arguments[3] : void 0;
        // Optimize
        this.optimize();
        // Vertex
        for (const vertexOutputNode of vertexNodes){
            vertexOutputNode.build(this._vertexCompilationState, vertexNodes);
        }
        // Fragment
        this._fragmentCompilationState.uniforms = this._vertexCompilationState.uniforms.slice(0);
        this._fragmentCompilationState._uniformDeclaration = this._vertexCompilationState._uniformDeclaration;
        this._fragmentCompilationState._constantDeclaration = this._vertexCompilationState._constantDeclaration;
        this._fragmentCompilationState._vertexState = this._vertexCompilationState;
        for (const fragmentOutputNode of fragmentNodes){
            this._resetDualBlocks(fragmentOutputNode, this._buildId - 1);
        }
        for (const fragmentOutputNode of fragmentNodes){
            fragmentOutputNode.build(this._fragmentCompilationState, fragmentNodes);
        }
        // Finalize
        this._vertexCompilationState.finalize(this._vertexCompilationState);
        this._fragmentCompilationState.finalize(this._fragmentCompilationState);
        if (updateBuildId) {
            this._buildId = NodeMaterial._BuildIdGenerator++;
        }
        if (verbose) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Log("Vertex shader:");
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Log(this._vertexCompilationState.compilationString);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Log("Fragment shader:");
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Log(this._fragmentCompilationState.compilationString);
        }
        // Errors
        const noError = this._sharedData.emitErrors();
        this._buildIsInProgress = false;
        if (noError) {
            this.onBuildObservable.notifyObservers(this);
            this._buildWasSuccessful = true;
        }
        // Wipe defines
        const meshes = this.getScene().meshes;
        for (const mesh of meshes){
            if (!mesh.subMeshes) {
                continue;
            }
            for (const subMesh of mesh.subMeshes){
                if (subMesh.getMaterial() !== this) {
                    continue;
                }
                if (!subMesh.materialDefines) {
                    continue;
                }
                const defines = subMesh.materialDefines;
                defines.markAllAsDirty();
                defines.reset();
            }
        }
        if (this.prePassTextureInputs.length) {
            this.getScene().enablePrePassRenderer();
        }
        const prePassRenderer = this.getScene().prePassRenderer;
        if (prePassRenderer) {
            prePassRenderer.markAsDirty();
        }
    }
    /**
     * Runs an optimization phase to try to improve the shader code
     */ optimize() {
        for (const optimizer of this._optimizers){
            optimizer.optimize(this._vertexOutputNodes, this._fragmentOutputNodes);
        }
    }
    _prepareDefinesForAttributes(mesh, defines) {
        const oldNormal = defines["NORMAL"];
        const oldTangent = defines["TANGENT"];
        const oldColor = defines["VERTEXCOLOR_NME"];
        defines["NORMAL"] = mesh.isVerticesDataPresent(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Buffers$2f$buffer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VertexBuffer"].NormalKind);
        defines["TANGENT"] = mesh.isVerticesDataPresent(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Buffers$2f$buffer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VertexBuffer"].TangentKind);
        const hasVertexColors = mesh.useVertexColors && mesh.isVerticesDataPresent(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Buffers$2f$buffer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VertexBuffer"].ColorKind);
        defines["VERTEXCOLOR_NME"] = hasVertexColors;
        let uvChanged = false;
        for(let i = 1; i <= 6; ++i){
            const oldUV = defines["UV" + i];
            defines["UV" + i] = mesh.isVerticesDataPresent("uv".concat(i === 1 ? "" : i));
            uvChanged = uvChanged || defines["UV" + i] !== oldUV;
        }
        // PrePass
        const oit = this.needAlphaBlendingForMesh(mesh) && this.getScene().useOrderIndependentTransparency;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PrepareDefinesForPrePass"])(this.getScene(), defines, !oit);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$geometryrendering$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaterialHelperGeometryRendering"].PrepareDefines(this.getScene().getEngine().currentRenderPassId, mesh, defines);
        if (oldNormal !== defines["NORMAL"] || oldTangent !== defines["TANGENT"] || oldColor !== defines["VERTEXCOLOR_NME"] || uvChanged) {
            defines.markAsAttributesDirty();
        }
    }
    /**
     * Can this material render to prepass
     */ get isPrePassCapable() {
        return true;
    }
    /**
     * Outputs written to the prepass
     */ get prePassTextureOutputs() {
        const prePassOutputBlock = this.getBlockByPredicate((block)=>block.getClassName() === "PrePassOutputBlock");
        const result = [
            4
        ];
        if (!prePassOutputBlock) {
            return result;
        }
        // Cannot write to prepass if we alread read from prepass
        if (this.prePassTextureInputs.length) {
            return result;
        }
        if (prePassOutputBlock.viewDepth.isConnected) {
            result.push(5);
        }
        if (prePassOutputBlock.screenDepth.isConnected) {
            result.push(10);
        }
        if (prePassOutputBlock.viewNormal.isConnected) {
            result.push(6);
        }
        if (prePassOutputBlock.worldNormal.isConnected) {
            result.push(8);
        }
        if (prePassOutputBlock.worldPosition.isConnected) {
            result.push(1);
        }
        if (prePassOutputBlock.localPosition.isConnected) {
            result.push(9);
        }
        if (prePassOutputBlock.reflectivity.isConnected) {
            result.push(3);
        }
        if (prePassOutputBlock.velocity.isConnected) {
            result.push(2);
        }
        if (prePassOutputBlock.velocityLinear.isConnected) {
            result.push(11);
        }
        return result;
    }
    /**
     * Gets the list of prepass texture required
     */ get prePassTextureInputs() {
        const prePassTextureBlocks = this.getAllTextureBlocks().filter((block)=>block.getClassName() === "PrePassTextureBlock");
        const result = [];
        for (const block of prePassTextureBlocks){
            if (block.position.isConnected && !result.includes(1)) {
                result.push(1);
            }
            if (block.localPosition.isConnected && !result.includes(9)) {
                result.push(9);
            }
            if (block.depth.isConnected && !result.includes(5)) {
                result.push(5);
            }
            if (block.screenDepth.isConnected && !result.includes(10)) {
                result.push(10);
            }
            if (block.normal.isConnected && !result.includes(6)) {
                result.push(6);
            }
            if (block.worldNormal.isConnected && !result.includes(8)) {
                result.push(8);
            }
        }
        return result;
    }
    /**
     * Sets the required values to the prepass renderer.
     * @param prePassRenderer defines the prepass renderer to set
     * @returns true if the pre pass is needed
     */ setPrePassRenderer(prePassRenderer) {
        const prePassTexturesRequired = this.prePassTextureInputs.concat(this.prePassTextureOutputs);
        if (prePassRenderer && prePassTexturesRequired.length > 1) {
            let cfg = prePassRenderer.getEffectConfiguration("nodeMaterial");
            if (!cfg) {
                cfg = prePassRenderer.addEffectConfiguration({
                    enabled: true,
                    needsImageProcessing: false,
                    name: "nodeMaterial",
                    texturesRequired: []
                });
            }
            for (const prePassTexture of prePassTexturesRequired){
                if (!cfg.texturesRequired.includes(prePassTexture)) {
                    cfg.texturesRequired.push(prePassTexture);
                }
            }
            cfg.enabled = true;
        }
        // COLOR_TEXTURE is always required for prepass, length > 1 means
        // we actually need to write to special prepass textures
        return prePassTexturesRequired.length > 1;
    }
    /**
     * Create a post process from the material
     * @param camera The camera to apply the render pass to.
     * @param options The required width/height ratio to downsize to before computing the render pass. (Use 1.0 for full size)
     * @param samplingMode The sampling mode to be used when computing the pass. (default: 0)
     * @param engine The engine which the post process will be applied. (default: current engine)
     * @param reusable If the post process can be reused on the same frame. (default: false)
     * @param textureType Type of textures used when performing the post process. (default: 0)
     * @param textureFormat Format of textures used when performing the post process. (default: TEXTUREFORMAT_RGBA)
     * @returns the post process created
     */ createPostProcess(camera) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, samplingMode = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1, engine = arguments.length > 3 ? arguments[3] : void 0, reusable = arguments.length > 4 ? arguments[4] : void 0, textureType = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : 0, textureFormat = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : 5;
        if (this.mode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].PostProcess && this.mode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].SFE) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Log("Incompatible material mode");
            return null;
        }
        return this._createEffectForPostProcess(null, camera, options, samplingMode, engine, reusable, textureType, textureFormat);
    }
    /**
     * Create the post process effect from the material
     * @param postProcess The post process to create the effect for
     */ createEffectForPostProcess(postProcess) {
        this._createEffectForPostProcess(postProcess);
    }
    _createEffectForPostProcess(postProcess, camera) {
        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1, samplingMode = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1, engine = arguments.length > 4 ? arguments[4] : void 0, reusable = arguments.length > 5 ? arguments[5] : void 0, textureType = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : 0, textureFormat = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : 5;
        let tempName = this.name + this._buildId;
        const defines = new NodeMaterialDefines();
        let buildId = this._buildId;
        this._processDefines(defines);
        // If no vertex shader emitted, fallback to default postprocess vertex shader
        const vertexCode = this._sharedData.checks.emitVertex ? this._vertexCompilationState._builtCompilationString : undefined;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, vertexCode, this.shaderLanguage);
        if (!postProcess) {
            postProcess = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PostProcess"](this.name + "PostProcess", tempName, this._fragmentCompilationState.uniforms, this._fragmentCompilationState.samplers, options, camera, samplingMode, engine, reusable, defines.toString(), textureType, vertexCode ? tempName : "postprocess", {
                maxSimultaneousLights: this.maxSimultaneousLights
            }, false, textureFormat, this.shaderLanguage);
        } else {
            postProcess.updateEffect(defines.toString(), this._fragmentCompilationState.uniforms, this._fragmentCompilationState.samplers, {
                maxSimultaneousLights: this.maxSimultaneousLights
            }, undefined, undefined, tempName, tempName);
        }
        postProcess.nodeMaterialSource = this;
        postProcess.onApplyObservable.add((effect)=>{
            if (buildId !== this._buildId) {
                delete __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[tempName + "VertexShader"];
                delete __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[tempName + "PixelShader"];
                tempName = this.name + this._buildId;
                defines.markAllAsDirty();
                buildId = this._buildId;
            }
            const result = this._processDefines(defines);
            if (result) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$timingTools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TimingTools"].SetImmediate(()=>postProcess.updateEffect(defines.toString(), this._fragmentCompilationState.uniforms, this._fragmentCompilationState.samplers, {
                        maxSimultaneousLights: this.maxSimultaneousLights
                    }, undefined, undefined, tempName, tempName));
            }
            this._checkInternals(effect);
        });
        return postProcess;
    }
    /**
     * Create a new procedural texture based on this node material
     * @param size defines the size of the texture
     * @param scene defines the hosting scene
     * @returns the new procedural texture attached to this node material
     */ createProceduralTexture(size, scene) {
        if (this.mode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].ProceduralTexture) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Log("Incompatible material mode");
            return null;
        }
        let tempName = this.name + this._buildId;
        const proceduralTexture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$Procedurals$2f$proceduralTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProceduralTexture"](tempName, size, null, scene);
        const defines = new NodeMaterialDefines();
        const result = this._processDefines(defines);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString, this.shaderLanguage);
        let effect = this.getScene().getEngine().createEffect({
            vertexElement: tempName,
            fragmentElement: tempName
        }, [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Buffers$2f$buffer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VertexBuffer"].PositionKind
        ], this._fragmentCompilationState.uniforms, this._fragmentCompilationState.samplers, defines.toString(), result === null || result === void 0 ? void 0 : result.fallbacks, undefined, undefined, undefined, this.shaderLanguage);
        proceduralTexture.nodeMaterialSource = this;
        proceduralTexture._setEffect(effect);
        let buildId = this._buildId;
        const refreshEffect = ()=>{
            if (buildId !== this._buildId) {
                delete __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[tempName + "VertexShader"];
                delete __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[tempName + "PixelShader"];
                tempName = this.name + this._buildId;
                defines.markAllAsDirty();
                buildId = this._buildId;
            }
            const result = this._processDefines(defines);
            if (result) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, this._vertexCompilationState._builtCompilationString, this.shaderLanguage);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$timingTools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TimingTools"].SetImmediate(()=>{
                    effect = this.getScene().getEngine().createEffect({
                        vertexElement: tempName,
                        fragmentElement: tempName
                    }, [
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Buffers$2f$buffer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VertexBuffer"].PositionKind
                    ], this._fragmentCompilationState.uniforms, this._fragmentCompilationState.samplers, defines.toString(), result === null || result === void 0 ? void 0 : result.fallbacks, undefined);
                    proceduralTexture._setEffect(effect);
                });
            }
            this._checkInternals(effect);
        };
        proceduralTexture.onBeforeGenerationObservable.add(()=>{
            refreshEffect();
        });
        // This is needed if the procedural texture is not set to refresh automatically
        this.onBuildObservable.add(()=>{
            refreshEffect();
        });
        return proceduralTexture;
    }
    _createEffectForParticles(particleSystem, blendMode, onCompiled, onError, effect, defines) {
        let particleSystemDefinesJoined = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : "";
        let tempName = this.name + this._buildId + "_" + blendMode;
        if (!defines) {
            defines = new NodeMaterialDefines();
        }
        let buildId = this._buildId;
        const particleSystemDefines = [];
        let join = particleSystemDefinesJoined;
        if (!effect) {
            const result = this._processDefines(defines);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, undefined, this.shaderLanguage);
            particleSystem.fillDefines(particleSystemDefines, blendMode, false);
            join = particleSystemDefines.join("\n");
            effect = this.getScene().getEngine().createEffectForParticles(tempName, this._fragmentCompilationState.uniforms, this._fragmentCompilationState.samplers, defines.toString() + "\n" + join, result === null || result === void 0 ? void 0 : result.fallbacks, onCompiled, onError, particleSystem, this.shaderLanguage);
            particleSystem.setCustomEffect(effect, blendMode);
        }
        effect.onBindObservable.add((effect)=>{
            if (buildId !== this._buildId) {
                delete __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].ShadersStore[tempName + "PixelShader"];
                tempName = this.name + this._buildId + "_" + blendMode;
                defines.markAllAsDirty();
                buildId = this._buildId;
            }
            particleSystemDefines.length = 0;
            particleSystem.fillDefines(particleSystemDefines, blendMode, false);
            const particleSystemDefinesJoinedCurrent = particleSystemDefines.join("\n");
            if (particleSystemDefinesJoinedCurrent !== join) {
                defines.markAllAsDirty();
                join = particleSystemDefinesJoinedCurrent;
            }
            const result = this._processDefines(defines);
            if (result) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Effect"].RegisterShader(tempName, this._fragmentCompilationState._builtCompilationString, undefined, this.shaderLanguage);
                effect = this.getScene().getEngine().createEffectForParticles(tempName, this._fragmentCompilationState.uniforms, this._fragmentCompilationState.samplers, defines.toString() + "\n" + join, result === null || result === void 0 ? void 0 : result.fallbacks, onCompiled, onError, particleSystem);
                particleSystem.setCustomEffect(effect, blendMode);
                this._createEffectForParticles(particleSystem, blendMode, onCompiled, onError, effect, defines, particleSystemDefinesJoined); // add the effect.onBindObservable observer
                return;
            }
            this._checkInternals(effect);
        });
    }
    _checkInternals(effect) {
        // Animated blocks
        if (this._sharedData.animatedInputs) {
            const scene = this.getScene();
            const frameId = scene.getFrameId();
            if (this._animationFrame !== frameId) {
                for (const input of this._sharedData.animatedInputs){
                    input.animate(scene);
                }
                this._animationFrame = frameId;
            }
        }
        // Bindable blocks
        for (const block of this._sharedData.bindableBlocks){
            block.bind(effect, this);
        }
        // Connection points
        for (const inputBlock of this._sharedData.inputBlocks){
            inputBlock._transmit(effect, this.getScene(), this);
        }
    }
    /**
     * Create the effect to be used as the custom effect for a particle system
     * @param particleSystem Particle system to create the effect for
     * @param onCompiled defines a function to call when the effect creation is successful
     * @param onError defines a function to call when the effect creation has failed
     */ createEffectForParticles(particleSystem, onCompiled, onError) {
        if (this.mode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].Particle) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Log("Incompatible material mode");
            return;
        }
        this._createEffectForParticles(particleSystem, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$baseParticleSystem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseParticleSystem"].BLENDMODE_ONEONE, onCompiled, onError);
        this._createEffectForParticles(particleSystem, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$baseParticleSystem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseParticleSystem"].BLENDMODE_MULTIPLY, onCompiled, onError);
    }
    /**
     * Use this material as the shadow depth wrapper of a target material
     * @param targetMaterial defines the target material
     */ createAsShadowDepthWrapper(targetMaterial) {
        if (this.mode !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].Material) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$logger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Logger"].Log("Incompatible material mode");
            return;
        }
        targetMaterial.shadowDepthWrapper = new BABYLON.ShadowDepthWrapper(this, this.getScene());
    }
    _processDefines(defines, mesh) {
        let useInstances = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false, subMesh = arguments.length > 3 ? arguments[3] : void 0;
        let result = null;
        // Global defines
        const scene = this.getScene();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$materialHelper$2e$functions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PrepareDefinesForCamera"])(scene, defines)) {
            defines.markAsMiscDirty();
        }
        // Shared defines
        for (const b of this._sharedData.blocksWithDefines){
            b.initializeDefines(defines);
        }
        for (const b of this._sharedData.blocksWithDefines){
            b.prepareDefines(defines, this, mesh, useInstances, subMesh);
        }
        // Need to recompile?
        if (defines.isDirty) {
            const lightDisposed = defines._areLightsDisposed;
            defines.markAsProcessed();
            // Repeatable content generators
            this._vertexCompilationState.compilationString = this._vertexCompilationState._builtCompilationString;
            this._fragmentCompilationState.compilationString = this._fragmentCompilationState._builtCompilationString;
            for (const b of this._sharedData.repeatableContentBlocks){
                b.replaceRepeatableContent(this._vertexCompilationState, defines, mesh);
            }
            // Uniforms
            const uniformBuffers = [];
            for (const b of this._sharedData.dynamicUniformBlocks){
                b.updateUniformsAndSamples(this._vertexCompilationState, this, defines, uniformBuffers);
            }
            const mergedUniforms = this._vertexCompilationState.uniforms;
            for (const u of this._fragmentCompilationState.uniforms){
                const index = mergedUniforms.indexOf(u);
                if (index === -1) {
                    mergedUniforms.push(u);
                }
            }
            // Samplers
            const mergedSamplers = this._vertexCompilationState.samplers;
            for (const s of this._fragmentCompilationState.samplers){
                const index = mergedSamplers.indexOf(s);
                if (index === -1) {
                    mergedSamplers.push(s);
                }
            }
            const fallbacks = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effectFallbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EffectFallbacks"]();
            for (const b of this._sharedData.blocksWithFallbacks){
                b.provideFallbacks(fallbacks, mesh);
            }
            result = {
                lightDisposed,
                uniformBuffers,
                mergedUniforms,
                mergedSamplers,
                fallbacks
            };
        }
        return result;
    }
    /**
     * Get if the submesh is ready to be used and all its information available.
     * Child classes can use it to update shaders
     * @param mesh defines the mesh to check
     * @param subMesh defines which submesh to check
     * @param useInstances specifies that instances should be used
     * @returns a boolean indicating that the submesh is ready or not
     */ isReadyForSubMesh(mesh, subMesh) {
        let useInstances = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        if (!this._buildWasSuccessful) {
            return false;
        }
        const scene = this.getScene();
        if (this._sharedData.animatedInputs) {
            const frameId = scene.getFrameId();
            if (this._animationFrame !== frameId) {
                for (const input of this._sharedData.animatedInputs){
                    input.animate(scene);
                }
                this._animationFrame = frameId;
            }
        }
        const drawWrapper = subMesh._drawWrapper;
        if (drawWrapper.effect && this.isFrozen) {
            if (drawWrapper._wasPreviouslyReady && drawWrapper._wasPreviouslyUsingInstances === useInstances) {
                return true;
            }
        }
        if (!subMesh.materialDefines || typeof subMesh.materialDefines === "string") {
            subMesh.materialDefines = new NodeMaterialDefines();
        }
        const defines = subMesh.materialDefines;
        if (this._isReadyForSubMesh(subMesh)) {
            return true;
        }
        const engine = scene.getEngine();
        this._prepareDefinesForAttributes(mesh, defines);
        // Check if blocks are ready
        if (this._sharedData.blockingBlocks.some((b)=>!b.isReady(mesh, this, defines, useInstances))) {
            return false;
        }
        const result = this._processDefines(defines, mesh, useInstances, subMesh);
        if (result) {
            const previousEffect = subMesh.effect;
            // Compilation
            const join = defines.toString();
            let effect = engine.createEffect({
                vertex: "nodeMaterial" + this._buildId,
                fragment: "nodeMaterial" + this._buildId,
                vertexSource: this._vertexCompilationState.compilationString,
                fragmentSource: this._fragmentCompilationState.compilationString
            }, {
                attributes: this._vertexCompilationState.attributes,
                uniformsNames: result.mergedUniforms,
                uniformBuffersNames: result.uniformBuffers,
                samplers: result.mergedSamplers,
                defines: join,
                fallbacks: result.fallbacks,
                onCompiled: this.onCompiled,
                onError: this.onError,
                multiTarget: defines.PREPASS,
                indexParameters: {
                    maxSimultaneousLights: this.maxSimultaneousLights,
                    maxSimultaneousMorphTargets: defines.NUM_MORPH_INFLUENCERS
                },
                shaderLanguage: this.shaderLanguage
            }, engine);
            if (effect) {
                if (this._onEffectCreatedObservable) {
                    onCreatedEffectParameters.effect = effect;
                    onCreatedEffectParameters.subMesh = subMesh;
                    this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);
                }
                // Use previous effect while new one is compiling
                if (this.allowShaderHotSwapping && previousEffect && !effect.isReady()) {
                    effect = previousEffect;
                    defines.markAsUnprocessed();
                    if (result.lightDisposed) {
                        // re register in case it takes more than one frame.
                        defines._areLightsDisposed = true;
                        return false;
                    }
                } else {
                    scene.resetCachedMaterial();
                    subMesh.setEffect(effect, defines, this._materialContext);
                }
            }
        }
        // Check if Area Lights have LTC texture.
        if (defines["AREALIGHTUSED"]) {
            for(let index = 0; index < mesh.lightSources.length; index++){
                if (!mesh.lightSources[index]._isReady()) {
                    return false;
                }
            }
        }
        if (!subMesh.effect || !subMesh.effect.isReady()) {
            return false;
        }
        defines._renderId = scene.getRenderId();
        drawWrapper._wasPreviouslyReady = true;
        drawWrapper._wasPreviouslyUsingInstances = useInstances;
        this._checkScenePerformancePriority();
        return true;
    }
    /**
     * Get a string representing the shaders built by the current node graph
     */ get compiledShaders() {
        if (!this._buildWasSuccessful) {
            this.build();
        }
        return "// Vertex shader\n".concat(this._vertexCompilationState.compilationString, "\n\n// Fragment shader\n").concat(this._fragmentCompilationState.compilationString);
    }
    /**
     * Get a string representing the fragment shader used by the engine for the current node graph
     * @internal
     */ async _getProcessedFragmentAsync() {
        if (!this._buildWasSuccessful) {
            this.build();
        }
        const defines = new NodeMaterialDefines();
        this._processDefines(defines);
        let processingDefines = defines.toString();
        if (this.mode === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].SFE) {
            processingDefines += "#define ".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$smartFilterFragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SfeModeDefine"], "\n");
        }
        return await this._fragmentCompilationState.getProcessedShaderAsync(processingDefines);
    }
    /**
     * Binds the world matrix to the material
     * @param world defines the world transformation matrix
     */ bindOnlyWorldMatrix(world) {
        const scene = this.getScene();
        if (!this._activeEffect) {
            return;
        }
        const hints = this._sharedData.hints;
        if (hints.needWorldViewMatrix) {
            world.multiplyToRef(scene.getViewMatrix(), this._cachedWorldViewMatrix);
        }
        if (hints.needWorldViewProjectionMatrix) {
            world.multiplyToRef(scene.getTransformMatrix(), this._cachedWorldViewProjectionMatrix);
        }
        // Connection points
        for (const inputBlock of this._sharedData.inputBlocks){
            inputBlock._transmitWorld(this._activeEffect, world, this._cachedWorldViewMatrix, this._cachedWorldViewProjectionMatrix);
        }
    }
    /**
     * Binds the submesh to this material by preparing the effect and shader to draw
     * @param world defines the world transformation matrix
     * @param mesh defines the mesh containing the submesh
     * @param subMesh defines the submesh to bind the material to
     */ bindForSubMesh(world, mesh, subMesh) {
        const scene = this.getScene();
        const effect = subMesh.effect;
        if (!effect) {
            return;
        }
        this._activeEffect = effect;
        // Matrices
        this.bindOnlyWorldMatrix(world);
        const mustRebind = this._mustRebind(scene, effect, subMesh, mesh.visibility);
        const sharedData = this._sharedData;
        if (mustRebind) {
            // Bindable blocks
            for (const block of sharedData.bindableBlocks){
                block.bind(effect, this, mesh, subMesh);
            }
            for (const block of sharedData.forcedBindableBlocks){
                block.bind(effect, this, mesh, subMesh);
            }
            // Connection points
            for (const inputBlock of sharedData.inputBlocks){
                inputBlock._transmit(effect, scene, this);
            }
        } else if (!this.isFrozen) {
            for (const block of sharedData.forcedBindableBlocks){
                block.bind(effect, this, mesh, subMesh);
            }
        }
        this._afterBind(mesh, this._activeEffect, subMesh);
    }
    /**
     * Gets the active textures from the material
     * @returns an array of textures
     */ getActiveTextures() {
        const activeTextures = super.getActiveTextures();
        if (this._sharedData) {
            activeTextures.push(...this._sharedData.textureBlocks.filter((tb)=>tb.texture).map((tb)=>tb.texture));
        }
        return activeTextures;
    }
    /**
     * Gets the list of texture blocks
     * Note that this method will only return blocks that are reachable from the final block(s) and only after the material has been built!
     * @returns an array of texture blocks
     */ getTextureBlocks() {
        if (!this._sharedData) {
            return [];
        }
        return this._sharedData.textureBlocks;
    }
    /**
     * Gets the list of all texture blocks
     * Note that this method will scan all attachedBlocks and return blocks that are texture blocks
     * @returns
     */ getAllTextureBlocks() {
        const textureBlocks = [];
        for (const block of this.attachedBlocks){
            if (NodeMaterial._BlockIsTextureBlock(block)) {
                textureBlocks.push(block);
            }
        }
        return textureBlocks;
    }
    /**
     * Specifies if the material uses a texture
     * @param texture defines the texture to check against the material
     * @returns a boolean specifying if the material uses the texture
     */ hasTexture(texture) {
        if (super.hasTexture(texture)) {
            return true;
        }
        if (!this._sharedData) {
            return false;
        }
        for (const t of this._sharedData.textureBlocks){
            if (t.texture === texture) {
                return true;
            }
        }
        return false;
    }
    /**
     * Disposes the material
     * @param forceDisposeEffect specifies if effects should be forcefully disposed
     * @param forceDisposeTextures specifies if textures should be forcefully disposed
     * @param notBoundToMesh specifies if the material that is being disposed is known to be not bound to any mesh
     */ dispose(forceDisposeEffect, forceDisposeTextures, notBoundToMesh) {
        if (forceDisposeTextures) {
            for (const texture of this.getTextureBlocks().filter((tb)=>tb.texture).map((tb)=>tb.texture)){
                texture.dispose();
            }
        }
        for (const block of this.attachedBlocks){
            block.dispose();
        }
        this.attachedBlocks.length = 0;
        this._sharedData = null;
        this._vertexCompilationState = null;
        this._fragmentCompilationState = null;
        this.onBuildObservable.clear();
        this.onBuildErrorObservable.clear();
        if (this._imageProcessingObserver) {
            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);
            this._imageProcessingObserver = null;
        }
        super.dispose(forceDisposeEffect, forceDisposeTextures, notBoundToMesh);
    }
    /** Creates the node editor window.
     * @param additionalConfig Define the configuration of the editor
     */ _createNodeEditor(additionalConfig) {
        const nodeEditorConfig = {
            nodeMaterial: this,
            ...additionalConfig
        };
        this.BJSNODEMATERIALEDITOR.NodeEditor.Show(nodeEditorConfig);
    }
    /**
     * Launch the node material editor
     * @param config Define the configuration of the editor
     * @returns a promise fulfilled when the node editor is visible
     */ async edit(config) {
        return await new Promise((resolve)=>{
            this.BJSNODEMATERIALEDITOR = this.BJSNODEMATERIALEDITOR || this._getGlobalNodeMaterialEditor();
            if (typeof this.BJSNODEMATERIALEDITOR == "undefined") {
                const editorUrl = config && config.editorURL ? config.editorURL : NodeMaterial.EditorURL;
                // Load editor and add it to the DOM
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$tools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tools"].LoadBabylonScript(editorUrl, ()=>{
                    this.BJSNODEMATERIALEDITOR = this.BJSNODEMATERIALEDITOR || this._getGlobalNodeMaterialEditor();
                    this._createNodeEditor(config === null || config === void 0 ? void 0 : config.nodeEditorConfig);
                    resolve();
                });
            } else {
                // Otherwise creates the editor
                this._createNodeEditor(config === null || config === void 0 ? void 0 : config.nodeEditorConfig);
                resolve();
            }
        });
    }
    /**
     * Clear the current material
     */ clear() {
        this._vertexOutputNodes.length = 0;
        this._fragmentOutputNodes.length = 0;
        this.attachedBlocks.length = 0;
        this._buildIsInProgress = false;
    }
    /**
     * Clear the current material and set it to a default state
     */ setToDefault() {
        this.clear();
        this.editorData = null;
        const positionInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Position");
        positionInput.setAsAttribute("position");
        const worldInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("World");
        worldInput.setAsSystemValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].World);
        const worldPos = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$transformBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TransformBlock"]("WorldPos");
        positionInput.connectTo(worldPos);
        worldInput.connectTo(worldPos);
        const viewProjectionInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("ViewProjection");
        viewProjectionInput.setAsSystemValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].ViewProjection);
        const worldPosdMultipliedByViewProjection = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$transformBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TransformBlock"]("WorldPos * ViewProjectionTransform");
        worldPos.connectTo(worldPosdMultipliedByViewProjection);
        viewProjectionInput.connectTo(worldPosdMultipliedByViewProjection);
        const vertexOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Vertex$2f$vertexOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VertexOutputBlock"]("VertexOutput");
        worldPosdMultipliedByViewProjection.connectTo(vertexOutput);
        // Pixel
        const pixelColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("color");
        pixelColor.value = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color4"](0.8, 0.8, 0.8, 1);
        const fragmentOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$fragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FragmentOutputBlock"]("FragmentOutput");
        pixelColor.connectTo(fragmentOutput);
        // Add to nodes
        this.addOutputNode(vertexOutput);
        this.addOutputNode(fragmentOutput);
        this._mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].Material;
    }
    /**
     * Clear the current material and set it to a default state for post process
     */ setToDefaultPostProcess() {
        this.clear();
        this.editorData = null;
        const position = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Position");
        position.setAsAttribute("position2d");
        const const1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Constant1");
        const1.isConstant = true;
        const1.value = 1;
        const vmerger = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$vectorMergerBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VectorMergerBlock"]("Position3D");
        position.connectTo(vmerger);
        const1.connectTo(vmerger, {
            input: "w"
        });
        const vertexOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Vertex$2f$vertexOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VertexOutputBlock"]("VertexOutput");
        vmerger.connectTo(vertexOutput);
        // Pixel
        const scale = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Scale");
        scale.visibleInInspector = true;
        scale.value = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector2"](1, 1);
        const uv0 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$remapBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RemapBlock"]("uv0");
        position.connectTo(uv0);
        const uv = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$multiplyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MultiplyBlock"]("UV scale");
        uv0.connectTo(uv);
        scale.connectTo(uv);
        const currentScreen = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Dual$2f$currentScreenBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CurrentScreenBlock"]("CurrentScreen");
        uv.connectTo(currentScreen);
        const textureUrl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$tools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tools"].GetAssetUrl("https://assets.babylonjs.com/core/nme/currentScreenPostProcess.png");
        currentScreen.texture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"](textureUrl, this.getScene());
        const fragmentOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$fragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FragmentOutputBlock"]("FragmentOutput");
        currentScreen.connectTo(fragmentOutput, {
            output: "rgba"
        });
        // Add to nodes
        this.addOutputNode(vertexOutput);
        this.addOutputNode(fragmentOutput);
        this._mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].PostProcess;
    }
    /**
     * Clear the current material and set it to a default state for procedural texture
     */ setToDefaultProceduralTexture() {
        this.clear();
        this.editorData = null;
        const position = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Position");
        position.setAsAttribute("position2d");
        const const1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Constant1");
        const1.isConstant = true;
        const1.value = 1;
        const vmerger = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$vectorMergerBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VectorMergerBlock"]("Position3D");
        position.connectTo(vmerger);
        const1.connectTo(vmerger, {
            input: "w"
        });
        const vertexOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Vertex$2f$vertexOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VertexOutputBlock"]("VertexOutput");
        vmerger.connectTo(vertexOutput);
        // Pixel
        const time = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Time");
        time.value = 0;
        time.min = 0;
        time.max = 0;
        time.isBoolean = false;
        time.matrixMode = 0;
        time.animationType = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$animatedInputBlockTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatedInputBlockTypes"].Time;
        time.isConstant = false;
        const color = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Color3");
        color.value = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](1, 1, 1);
        color.isConstant = false;
        const fragmentOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$fragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FragmentOutputBlock"]("FragmentOutput");
        const vectorMerger = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$vectorMergerBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VectorMergerBlock"]("VectorMerger");
        vectorMerger.visibleInInspector = false;
        const cos = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$trigonometryBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TrigonometryBlock"]("Cos");
        cos.operation = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$trigonometryBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TrigonometryBlockOperations"].Cos;
        position.connectTo(vectorMerger);
        time.output.connectTo(cos.input);
        cos.output.connectTo(vectorMerger.z);
        vectorMerger.xyzOut.connectTo(fragmentOutput.rgb);
        // Add to nodes
        this.addOutputNode(vertexOutput);
        this.addOutputNode(fragmentOutput);
        this._mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].ProceduralTexture;
    }
    /**
     * Clear the current material and set it to a default state for particle
     */ setToDefaultParticle() {
        this.clear();
        this.editorData = null;
        // Pixel
        const uv = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("uv");
        uv.setAsAttribute("particle_uv");
        const texture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Particle$2f$particleTextureBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ParticleTextureBlock"]("ParticleTexture");
        uv.connectTo(texture);
        const color = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Color");
        color.setAsAttribute("particle_color");
        const multiply = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$multiplyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MultiplyBlock"]("Texture * Color");
        texture.connectTo(multiply);
        color.connectTo(multiply);
        const rampGradient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Particle$2f$particleRampGradientBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ParticleRampGradientBlock"]("ParticleRampGradient");
        multiply.connectTo(rampGradient);
        const cSplitter = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$colorSplitterBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ColorSplitterBlock"]("ColorSplitter");
        color.connectTo(cSplitter);
        const blendMultiply = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Particle$2f$particleBlendMultiplyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ParticleBlendMultiplyBlock"]("ParticleBlendMultiply");
        rampGradient.connectTo(blendMultiply);
        texture.connectTo(blendMultiply, {
            output: "a"
        });
        cSplitter.connectTo(blendMultiply, {
            output: "a"
        });
        const fragmentOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$fragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FragmentOutputBlock"]("FragmentOutput");
        blendMultiply.connectTo(fragmentOutput);
        // Add to nodes
        this.addOutputNode(fragmentOutput);
        this._mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].Particle;
    }
    /**
     * Loads the current Node Material from a url pointing to a file save by the Node Material Editor
     * @deprecated Please use NodeMaterial.ParseFromFileAsync instead
     * @param url defines the url to load from
     * @param rootUrl defines the root URL for nested url in the node material
     * @returns a promise that will fulfil when the material is fully loaded
     */ async loadAsync(url) {
        let rootUrl = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "";
        return await NodeMaterial.ParseFromFileAsync("", url, this.getScene(), rootUrl, true, this);
    }
    _gatherBlocks(rootNode, list) {
        if (list.indexOf(rootNode) !== -1) {
            return;
        }
        list.push(rootNode);
        for (const input of rootNode.inputs){
            const connectedPoint = input.connectedPoint;
            if (connectedPoint) {
                const block = connectedPoint.ownerBlock;
                if (block !== rootNode) {
                    this._gatherBlocks(block, list);
                }
            }
        }
        // Teleportation
        if (rootNode.isTeleportOut) {
            const block = rootNode;
            if (block.entryPoint) {
                this._gatherBlocks(block.entryPoint, list);
            }
        }
    }
    /**
     * Generate a string containing the code declaration required to create an equivalent of this material
     * @returns a string
     */ generateCode() {
        let alreadyDumped = [];
        const vertexBlocks = [];
        const uniqueNames = [
            "const",
            "var",
            "let"
        ];
        // Gets active blocks
        for (const outputNode of this._vertexOutputNodes){
            this._gatherBlocks(outputNode, vertexBlocks);
        }
        const fragmentBlocks = [];
        for (const outputNode of this._fragmentOutputNodes){
            this._gatherBlocks(outputNode, fragmentBlocks);
        }
        // Generate vertex shader
        let codeString = 'var nodeMaterial = new BABYLON.NodeMaterial("'.concat(this.name || "node material", '");\n');
        codeString += "nodeMaterial.mode = BABYLON.NodeMaterialModes.".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"][this.mode], ";\n");
        for (const node of vertexBlocks){
            if (node.isInput && alreadyDumped.indexOf(node) === -1) {
                codeString += node._dumpCode(uniqueNames, alreadyDumped);
            }
        }
        // Generate fragment shader
        for (const node of fragmentBlocks){
            if (node.isInput && alreadyDumped.indexOf(node) === -1) {
                codeString += node._dumpCode(uniqueNames, alreadyDumped);
            }
        }
        // Connections
        alreadyDumped = [];
        codeString += "\n// Connections\n";
        for (const node of this._vertexOutputNodes){
            codeString += node._dumpCodeForOutputConnections(alreadyDumped);
        }
        for (const node of this._fragmentOutputNodes){
            codeString += node._dumpCodeForOutputConnections(alreadyDumped);
        }
        // Output nodes
        codeString += "\n// Output nodes\n";
        for (const node of this._vertexOutputNodes){
            codeString += "nodeMaterial.addOutputNode(".concat(node._codeVariableName, ");\n");
        }
        for (const node of this._fragmentOutputNodes){
            codeString += "nodeMaterial.addOutputNode(".concat(node._codeVariableName, ");\n");
        }
        codeString += "nodeMaterial.build();\n";
        return codeString;
    }
    /**
     * Serializes this material in a JSON representation
     * @param selectedBlocks defines an optional list of blocks to serialize
     * @returns the serialized material object
     */ serialize(selectedBlocks) {
        const serializationObject = selectedBlocks ? {} : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SerializationHelper"].Serialize(this);
        serializationObject.editorData = JSON.parse(JSON.stringify(this.editorData)); // Copy
        let blocks = [];
        if (selectedBlocks) {
            blocks = selectedBlocks;
        } else {
            serializationObject.customType = "BABYLON.NodeMaterial";
            serializationObject.outputNodes = [];
            // Outputs
            for (const outputNode of this._vertexOutputNodes){
                this._gatherBlocks(outputNode, blocks);
                serializationObject.outputNodes.push(outputNode.uniqueId);
            }
            for (const outputNode of this._fragmentOutputNodes){
                this._gatherBlocks(outputNode, blocks);
                if (serializationObject.outputNodes.indexOf(outputNode.uniqueId) === -1) {
                    serializationObject.outputNodes.push(outputNode.uniqueId);
                }
            }
        }
        // Blocks
        serializationObject.blocks = [];
        for (const block of blocks){
            serializationObject.blocks.push(block.serialize());
        }
        if (!selectedBlocks) {
            for (const block of this.attachedBlocks){
                if (blocks.indexOf(block) !== -1) {
                    continue;
                }
                serializationObject.blocks.push(block.serialize());
            }
        }
        serializationObject.uniqueId = this.uniqueId;
        return serializationObject;
    }
    _restoreConnections(block, source, map) {
        for (const outputPoint of block.outputs){
            for (const candidate of source.blocks){
                const target = map[candidate.id];
                if (!target) {
                    continue;
                }
                for (const input of candidate.inputs){
                    if (map[input.targetBlockId] === block && input.targetConnectionName === outputPoint.name) {
                        const inputPoint = target.getInputByName(input.inputName);
                        if (!inputPoint || inputPoint.isConnected) {
                            continue;
                        }
                        outputPoint.connectTo(inputPoint, true);
                        this._restoreConnections(target, source, map);
                        continue;
                    }
                }
            }
        }
    }
    /**
     * Clear the current graph and load a new one from a serialization object
     * @param source defines the JSON representation of the material
     * @param rootUrl defines the root URL to use to load textures and relative dependencies
     * @param merge defines whether or not the source must be merged or replace the current content
     * @param urlRewriter defines a function used to rewrite urls
     */ parseSerializedObject(source) {
        let rootUrl = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "", merge = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false, urlRewriter = arguments.length > 3 ? arguments[3] : void 0;
        if (!merge) {
            this.clear();
        }
        const map = {};
        // Create blocks
        for (const parsedBlock of source.blocks){
            const blockType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GetClass"])(parsedBlock.customType);
            if (blockType) {
                const block = new blockType();
                block._deserialize(parsedBlock, this.getScene(), rootUrl, urlRewriter);
                map[parsedBlock.id] = block;
                this.attachedBlocks.push(block);
            }
        }
        // Reconnect teleportation
        for (const block of this.attachedBlocks){
            if (block.isTeleportOut) {
                const teleportOut = block;
                const id = teleportOut._tempEntryPointUniqueId;
                if (id) {
                    const source = map[id];
                    source.attachToEndpoint(teleportOut);
                }
            }
        }
        // Connections - Starts with input blocks only (except if in "merge" mode where we scan all blocks)
        for(let blockIndex = 0; blockIndex < source.blocks.length; blockIndex++){
            const parsedBlock = source.blocks[blockIndex];
            const block = map[parsedBlock.id];
            if (!block) {
                continue;
            }
            if (block.inputs.length && !merge) {
                continue;
            }
            this._restoreConnections(block, source, map);
        }
        // Outputs
        if (source.outputNodes) {
            for (const outputNodeId of source.outputNodes){
                this.addOutputNode(map[outputNodeId]);
            }
        }
        // UI related info
        if (source.locations || source.editorData && source.editorData.locations) {
            const locations = source.locations || source.editorData.locations;
            for (const location of locations){
                if (map[location.blockId]) {
                    location.blockId = map[location.blockId].uniqueId;
                }
            }
            if (merge && this.editorData && this.editorData.locations) {
                locations.concat(this.editorData.locations);
            }
            if (source.locations) {
                this.editorData = {
                    locations: locations
                };
            } else {
                this.editorData = source.editorData;
                this.editorData.locations = locations;
            }
            const blockMap = [];
            for(const key in map){
                blockMap[key] = map[key].uniqueId;
            }
            this.editorData.map = blockMap;
        }
        this.comment = source.comment;
        if (source.forceAlphaBlending !== undefined) {
            this.forceAlphaBlending = source.forceAlphaBlending;
        }
        if (source.alphaMode !== undefined) {
            this.alphaMode = source.alphaMode;
        }
        if (!merge) {
            var _source_mode;
            this._mode = (_source_mode = source.mode) !== null && _source_mode !== void 0 ? _source_mode : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].Material;
        }
    }
    /**
     * Clear the current graph and load a new one from a serialization object
     * @param source defines the JSON representation of the material
     * @param rootUrl defines the root URL to use to load textures and relative dependencies
     * @param merge defines whether or not the source must be merged or replace the current content
     * @deprecated Please use the parseSerializedObject method instead
     */ loadFromSerialization(source) {
        let rootUrl = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : "", merge = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        this.parseSerializedObject(source, rootUrl, merge);
    }
    /**
     * Makes a duplicate of the current material.
     * @param name defines the name to use for the new material
     * @param shareEffect defines if the clone material should share the same effect (default is false)
     * @returns the cloned material
     */ clone(name) {
        let shareEffect = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
        const serializationObject = this.serialize();
        const clone = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SerializationHelper"].Clone(()=>new NodeMaterial(name, this.getScene(), this.options), this);
        clone.id = name;
        clone.name = name;
        clone.parseSerializedObject(serializationObject);
        clone._buildId = this._buildId;
        clone.build(false, !shareEffect);
        return clone;
    }
    /**
     * Awaits for all the material textures to be ready before resolving the returned promise.
     * @returns A promise that resolves when the textures are ready.
     */ // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax
    whenTexturesReadyAsync() {
        // Ensures all textures are ready to render.
        const textureReadyPromises = [];
        const activeTextures = this.getActiveTextures();
        for (const texture of activeTextures){
            const internalTexture = texture.getInternalTexture();
            if (internalTexture && !internalTexture.isReady) {
                textureReadyPromises.push(new Promise((textureResolve, textureReject)=>{
                    internalTexture.onLoadedObservable.addOnce(()=>{
                        textureResolve();
                    });
                    internalTexture.onErrorObservable.addOnce((e)=>{
                        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
                        textureReject(e);
                    });
                }));
            }
        }
        return Promise.all(textureReadyPromises);
    }
    /**
     * Creates a node material from parsed material data
     * @param source defines the JSON representation of the material
     * @param scene defines the hosting scene
     * @param rootUrl defines the root URL to use to load textures and relative dependencies
     * @param shaderLanguage defines the language to use (GLSL by default)
     * @returns a new node material
     */ static Parse(source, scene) {
        let rootUrl = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "", shaderLanguage = arguments.length > 3 && arguments[3] !== void 0 /* ShaderLanguage.GLSL */  ? arguments[3] : 0;
        const nodeMaterial = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SerializationHelper"].Parse(()=>new NodeMaterial(source.name, scene, {
                shaderLanguage: shaderLanguage
            }), source, scene, rootUrl);
        nodeMaterial.parseSerializedObject(source, rootUrl);
        nodeMaterial.build();
        return nodeMaterial;
    }
    /**
     * Creates a node material from a snippet saved in a remote file
     * @param name defines the name of the material to create
     * @param url defines the url to load from
     * @param scene defines the hosting scene
     * @param rootUrl defines the root URL for nested url in the node material
     * @param skipBuild defines whether to build the node material
     * @param targetMaterial defines a material to use instead of creating a new one
     * @param urlRewriter defines a function used to rewrite urls
     * @param options defines options to be used with the node material
     * @returns a promise that will resolve to the new node material
     */ static async ParseFromFileAsync(name, url, scene) {
        let rootUrl = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : "", skipBuild = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : false, targetMaterial = arguments.length > 5 ? arguments[5] : void 0, urlRewriter = arguments.length > 6 ? arguments[6] : void 0, options = arguments.length > 7 ? arguments[7] : void 0;
        const material = targetMaterial !== null && targetMaterial !== void 0 ? targetMaterial : new NodeMaterial(name, scene, options);
        const data = await scene._loadFileAsync(url);
        const serializationObject = JSON.parse(data);
        material.parseSerializedObject(serializationObject, rootUrl, undefined, urlRewriter);
        if (!skipBuild) {
            material.build();
        }
        return material;
    }
    /**
     * Creates a node material from a snippet saved by the node material editor
     * @param snippetId defines the snippet to load
     * @param scene defines the hosting scene
     * @param rootUrl defines the root URL to use to load textures and relative dependencies
     * @param nodeMaterial defines a node material to update (instead of creating a new one)
     * @param skipBuild defines whether to build the node material
     * @param waitForTextureReadyness defines whether to wait for texture readiness resolving the promise (default: false)
     * @param urlRewriter defines a function used to rewrite urls
     * @param options defines options to be used with the node material
     * @returns a promise that will resolve to the new node material
     */ // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax
    static ParseFromSnippetAsync(snippetId) {
        let scene = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$engineStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EngineStore"].LastCreatedScene, rootUrl = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "", nodeMaterial = arguments.length > 3 ? arguments[3] : void 0, skipBuild = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : false, waitForTextureReadyness = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : false, urlRewriter = arguments.length > 6 ? arguments[6] : void 0, options = arguments.length > 7 ? arguments[7] : void 0;
        if (snippetId === "_BLANK") {
            return Promise.resolve(NodeMaterial.CreateDefault("blank", scene));
        }
        return new Promise((resolve, reject)=>{
            const request = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$webRequest$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WebRequest"]();
            request.addEventListener("readystatechange", ()=>{
                if (request.readyState == 4) {
                    if (request.status == 200) {
                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);
                        const serializationObject = JSON.parse(snippet.nodeMaterial);
                        if (!nodeMaterial) {
                            nodeMaterial = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$serialization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SerializationHelper"].Parse(()=>new NodeMaterial(snippetId, scene, options), serializationObject, scene, rootUrl);
                            nodeMaterial.uniqueId = scene.getUniqueId();
                        }
                        nodeMaterial.parseSerializedObject(serializationObject, undefined, undefined, urlRewriter);
                        nodeMaterial.snippetId = snippetId;
                        // We reset sideOrientation to default value
                        nodeMaterial.sideOrientation = null;
                        try {
                            if (!skipBuild) {
                                nodeMaterial.build();
                            }
                        } catch (err) {
                            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
                            reject(err);
                        }
                        if (waitForTextureReadyness) {
                            nodeMaterial.whenTexturesReadyAsync()// eslint-disable-next-line github/no-then
                            .then(()=>{
                                resolve(nodeMaterial);
                            })// eslint-disable-next-line github/no-then
                            .catch((err)=>{
                                // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
                                reject(err);
                            });
                        } else {
                            resolve(nodeMaterial);
                        }
                    } else {
                        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
                        reject("Unable to load the snippet " + snippetId);
                    }
                }
            });
            request.open("GET", this.SnippetUrl + "/" + snippetId.replace(/#/g, "/"));
            request.send();
        });
    }
    /**
     * Creates a new node material set to default basic configuration
     * @param name defines the name of the material
     * @param scene defines the hosting scene
     * @returns a new NodeMaterial
     */ static CreateDefault(name, scene) {
        const newMaterial = new NodeMaterial(name, scene);
        newMaterial.setToDefault();
        newMaterial.build();
        return newMaterial;
    }
    /**
     * Create a new node based material
     * @param name defines the material name
     * @param scene defines the hosting scene
     * @param options defines creation option
     */ constructor(name, scene, options = {}){
        super(name, scene || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$engineStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EngineStore"].LastCreatedScene);
        this._buildId = NodeMaterial._BuildIdGenerator++;
        this._buildWasSuccessful = false;
        this._cachedWorldViewMatrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Matrix"]();
        this._cachedWorldViewProjectionMatrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Matrix"]();
        this._optimizers = new Array();
        this._animationFrame = -1;
        this._buildIsInProgress = false;
        this.BJSNODEMATERIALEDITOR = this._getGlobalNodeMaterialEditor();
        /** @internal */ this._useAdditionalColor = false;
        /**
         * Gets or sets data used by visual editor
         * @see https://nme.babylonjs.com
         */ this.editorData = null;
        /**
         * Gets or sets a boolean indicating that alpha value must be ignored (This will turn alpha blending off even if an alpha value is produced by the material)
         */ this.ignoreAlpha = false;
        /**
         * Defines the maximum number of lights that can be used in the material
         */ this.maxSimultaneousLights = 4;
        /**
         * Observable raised when the material is built
         */ this.onBuildObservable = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$observable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Observable"]();
        /**
         * Observable raised when an error is detected
         */ this.onBuildErrorObservable = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$observable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Observable"]();
        /**
         * Gets or sets the root nodes of the material vertex shader
         */ this._vertexOutputNodes = new Array();
        /**
         * Gets or sets the root nodes of the material fragment (pixel) shader
         */ this._fragmentOutputNodes = new Array();
        /**
         * Gets an array of blocks that needs to be serialized even if they are not yet connected
         */ this.attachedBlocks = [];
        /**
         * Specifies the mode of the node material
         * @internal
         */ this._mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].Material;
        /**
         * Gets or sets a boolean indicating that alpha blending must be enabled no matter what alpha value or alpha channel of the FragmentBlock are
         */ this.forceAlphaBlending = false;
        if (!NodeMaterial.UseNativeShaderLanguageOfEngine && options && options.shaderLanguage === 1 /* ShaderLanguage.WGSL */  && !this.getScene().getEngine().isWebGPU) {
            throw new Error("WebGPU shader language is only supported with WebGPU engine");
        }
        this._options = {
            emitComments: false,
            shaderLanguage: NodeMaterial.DefaultShaderLanguage,
            ...options
        };
        if (NodeMaterial.UseNativeShaderLanguageOfEngine) {
            this._options.shaderLanguage = this.getScene().getEngine().isWebGPU ? 1 /* ShaderLanguage.WGSL */  : 0 /* ShaderLanguage.GLSL */ ;
        }
        // Setup the default processing configuration to the scene.
        this._attachImageProcessingConfiguration(null);
    }
}
NodeMaterial._BuildIdGenerator = 0;
/** Define the Url to load node editor script */ NodeMaterial.EditorURL = "".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$tools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tools"]._DefaultCdnUrl, "/v").concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$abstractEngine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AbstractEngine"].Version, "/nodeEditor/babylon.nodeEditor.js");
/** Define the Url to load snippets */ NodeMaterial.SnippetUrl = "https://snippet.babylonjs.com";
/** Gets or sets a boolean indicating that node materials should not deserialize textures from json / snippet content */ NodeMaterial.IgnoreTexturesAtLoadTime = false;
/** Defines default shader language when no option is defined */ NodeMaterial.DefaultShaderLanguage = 0 /* ShaderLanguage.GLSL */ ;
/** If true, the node material will use GLSL if the engine is WebGL and WGSL if it's WebGPU. It takes priority over DefaultShaderLanguage if it's true */ NodeMaterial.UseNativeShaderLanguageOfEngine = false;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])()
], NodeMaterial.prototype, "ignoreAlpha", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])()
], NodeMaterial.prototype, "maxSimultaneousLights", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])("mode")
], NodeMaterial.prototype, "_mode", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])("comment")
], NodeMaterial.prototype, "comment", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$tslib$2e$es6$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["__decorate"])([
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$decorators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serialize"])()
], NodeMaterial.prototype, "forceAlphaBlending", void 0);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$typeStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RegisterClass"])("BABYLON.NodeMaterial", NodeMaterial); //# sourceMappingURL=nodeMaterial.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "NodeMaterialConnectionPointCustomObject": ()=>NodeMaterialConnectionPointCustomObject
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlockConnectionPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlockConnectionPoint.js [app-client] (ecmascript)");
;
class NodeMaterialConnectionPointCustomObject extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlockConnectionPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialConnectionPoint"] {
    /**
     * Gets a number indicating if the current point can be connected to another point
     * @param connectionPoint defines the other connection point
     * @returns a number defining the compatibility state
     */ checkCompatibilityState(connectionPoint) {
        return connectionPoint instanceof NodeMaterialConnectionPointCustomObject && connectionPoint._blockName === this._blockName ? 0 /* NodeMaterialConnectionPointCompatibilityStates.Compatible */  : 1 /* NodeMaterialConnectionPointCompatibilityStates.TypeIncompatible */ ;
    }
    /**
     * Creates a block suitable to be used as an input for this input point.
     * If null is returned, a block based on the point type will be created.
     * @returns The returned string parameter is the name of the output point of NodeMaterialBlock (first parameter of the returned array) that can be connected to the input
     */ createCustomInputBlock() {
        return [
            new this._blockType(this._blockName),
            this.name
        ];
    }
    /**
     * Creates a new connection point
     * @param name defines the connection point name
     * @param ownerBlock defines the block hosting this connection point
     * @param direction defines the direction of the connection point
     * @param _blockType
     * @param _blockName
     */ constructor(name, ownerBlock, direction, // @internal
    _blockType, _blockName){
        super(name, ownerBlock, direction);
        this._blockType = _blockType;
        this._blockName = _blockName;
        this.needDualDirectionValidation = true;
    }
} //# sourceMappingURL=nodeMaterialConnectionPointCustomObject.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialDefault.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "SetToDefaultGaussianSplatting": ()=>SetToDefaultGaussianSplatting,
    "SetToDefaultSFE": ()=>SetToDefaultSFE
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$transformBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/transformBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Vertex$2f$vertexOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Vertex/vertexOutputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$fragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/fragmentOutputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Input/inputBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$GaussianSplatting$2f$gaussianSplattingBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/GaussianSplatting/gaussianSplattingBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$GaussianSplatting$2f$gaussianBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/GaussianSplatting/gaussianBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$GaussianSplatting$2f$splatReaderBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/GaussianSplatting/splatReaderBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialModes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/nodeMaterialSystemValues.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$multiplyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/multiplyBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/texture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$tools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Misc/tools.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Dual$2f$smartFilterTextureBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/smartFilterTextureBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.color.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$addBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/addBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$smartFilterFragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/smartFilterFragmentOutputBlock.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function SetToDefaultGaussianSplatting(nodeMaterial) {
    nodeMaterial.clear();
    nodeMaterial.editorData = null;
    // reading splat datas
    const splatIndex = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("SplatIndex");
    splatIndex.setAsAttribute("splatIndex");
    const splatReader = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$GaussianSplatting$2f$splatReaderBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SplatReaderBlock"]("SplatReader");
    splatIndex.connectTo(splatReader);
    // transforming datas into renderable positions
    const gs = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$GaussianSplatting$2f$gaussianSplattingBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GaussianSplattingBlock"]("GaussianSplatting");
    splatReader.connectTo(gs);
    // world transformation
    const worldInput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("World");
    worldInput.setAsSystemValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].World);
    const worldPos = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$transformBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TransformBlock"]("WorldPos");
    splatReader.connectTo(worldPos);
    worldInput.connectTo(worldPos);
    worldPos.connectTo(gs, {
        output: "xyz",
        input: "splatPosition"
    });
    // view and projections
    const view = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("view");
    view.setAsSystemValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].View);
    const projection = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Projection");
    projection.setAsSystemValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialSystemValues$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialSystemValues"].Projection);
    worldInput.connectTo(gs, {
        input: "world"
    });
    view.connectTo(gs, {
        input: "view"
    });
    projection.connectTo(gs, {
        input: "projection"
    });
    const addBlock = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$addBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AddBlock"]("Add SH");
    // from color to gaussian color
    const gaussian = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$GaussianSplatting$2f$gaussianBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GaussianBlock"]("Gaussian");
    splatReader.connectTo(gaussian, {
        input: "splatColor",
        output: "splatColor"
    });
    // fragment and vertex outputs
    const fragmentOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$fragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FragmentOutputBlock"]("FragmentOutput");
    gs.SH.connectTo(addBlock.left);
    gaussian.rgb.connectTo(addBlock.right);
    addBlock.output.connectTo(fragmentOutput.rgb);
    gaussian.alpha.connectTo(fragmentOutput.a);
    const vertexOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Vertex$2f$vertexOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VertexOutputBlock"]("VertexOutput");
    gs.connectTo(vertexOutput);
    // Add to nodes
    nodeMaterial.addOutputNode(vertexOutput);
    nodeMaterial.addOutputNode(fragmentOutput);
    nodeMaterial._mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].GaussianSplatting;
}
function SetToDefaultSFE(nodeMaterial) {
    nodeMaterial.clear();
    nodeMaterial.editorData = null;
    const uv = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("uv");
    uv.setAsAttribute("postprocess_uv");
    uv.comments = "Normalized screen position to sample our texture with.";
    const currentScreen = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Dual$2f$smartFilterTextureBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SmartFilterTextureBlock"]("Input Texture");
    currentScreen.comments = "A placeholder that represents the input texture to compose.";
    uv.connectTo(currentScreen);
    const textureUrl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Misc$2f$tools$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tools"].GetAssetUrl("https://assets.babylonjs.com/core/nme/currentScreenPostProcess.png");
    currentScreen.texture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$texture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Texture"](textureUrl, nodeMaterial.getScene());
    const color = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Input$2f$inputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InputBlock"]("Color4");
    color.value = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color4"](1, 0, 0, 1);
    const multiply = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$multiplyBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MultiplyBlock"]("Multiply");
    color.connectTo(multiply);
    currentScreen.connectTo(multiply);
    const fragmentOutput = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$Fragment$2f$smartFilterFragmentOutputBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SmartFilterFragmentOutputBlock"]("FragmentOutput");
    multiply.connectTo(fragmentOutput);
    nodeMaterial.addOutputNode(fragmentOutput);
    nodeMaterial._mode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$nodeMaterialModes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NodeMaterialModes"].SFE;
} //# sourceMappingURL=nodeMaterialDefault.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/nodeMaterialOptimizer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Root class for all node material optimizers
 */ __turbopack_context__.s({
    "NodeMaterialOptimizer": ()=>NodeMaterialOptimizer
});
class NodeMaterialOptimizer {
    /**
     * Function used to optimize a NodeMaterial graph
     * @param _vertexOutputNodes defines the list of output nodes for the vertex shader
     * @param _fragmentOutputNodes defines the list of output nodes for the fragment shader
     */ optimize(_vertexOutputNodes, _fragmentOutputNodes) {
    // Do nothing by default
    }
} //# sourceMappingURL=nodeMaterialOptimizer.js.map
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Optimizers$2f$nodeMaterialOptimizer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/nodeMaterialOptimizer.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
;
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Optimizers$2f$nodeMaterialOptimizer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/nodeMaterialOptimizer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Optimizers$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/* eslint-disable @typescript-eslint/no-restricted-imports */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlockConnectionPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlockConnectionPoint.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialDefault$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialDefault.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Optimizers$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Decorators/nodeDecorator.js [app-client] (ecmascript)"); //# sourceMappingURL=index.js.map
;
;
;
;
;
;
;
;
;
}),
"[project]/node_modules/@babylonjs/core/Materials/Node/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Enums$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Enums/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialConnectionPointCustomObject$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialConnectionPointCustomObject.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlockConnectionPoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlockConnectionPoint.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialBlock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialBlock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterialDefault$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterialDefault.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$nodeMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/nodeMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Optimizers$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Optimizers/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Decorators$2f$nodeDecorator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Decorators/nodeDecorator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/index.js [app-client] (ecmascript) <locals>");
}),
}]);

//# sourceMappingURL=node_modules_%40babylonjs_core_Materials_Node_8f84abb7._.js.map