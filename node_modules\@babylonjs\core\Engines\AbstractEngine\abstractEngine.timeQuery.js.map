{"version": 3, "file": "abstractEngine.timeQuery.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.timeQuery.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAqBrD,cAAc,CAAC,SAAS,CAAC,sBAAsB,GAAG;IAC9C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC;IAC3C,CAAC;IACD,OAAO,IAAI,CAAC,aAAa,CAAC;AAC9B,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,KAAc;IACnE,mDAAmD;AACvD,CAAC,CAAC", "sourcesContent": ["import { AbstractEngine } from \"../abstractEngine\";\r\nimport { PerfCounter } from \"../../Misc/perfCounter\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /** @internal */\r\n        _gpuFrameTime: Nullable<PerfCounter>;\r\n        /**\r\n         * Get the performance counter associated with the frame time computation\r\n         * @returns the perf counter\r\n         */\r\n        getGPUFrameTimeCounter(): PerfCounter;\r\n        /**\r\n         * Enable or disable the GPU frame time capture\r\n         * @param value True to enable, false to disable\r\n         */\r\n        captureGPUFrameTime(value: boolean): void;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype.getGPUFrameTimeCounter = function () {\r\n    if (!this._gpuFrameTime) {\r\n        this._gpuFrameTime = new PerfCounter();\r\n    }\r\n    return this._gpuFrameTime;\r\n};\r\n\r\nAbstractEngine.prototype.captureGPUFrameTime = function (value: boolean): void {\r\n    // Do nothing. Must be implemented by child classes\r\n};\r\n"]}