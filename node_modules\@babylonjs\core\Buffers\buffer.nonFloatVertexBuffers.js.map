{"version": 3, "file": "buffer.nonFloatVertexBuffers.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Buffers/buffer.nonFloatVertexBuffers.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAEhD,MAAM,qCAAqC,GAAgC;IACvE,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,IAAI;IACjC,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,IAAI;IAC/B,CAAC,YAAY,CAAC,WAAW,CAAC,EAAE,IAAI;IAChC,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI;IAC3B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,IAAI;IAC5B,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,IAAI;IAC9B,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE,IAAI;IACtC,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE,IAAI;IACxC,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE,IAAI;IACxC,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE,IAAI;IAC7C,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE,IAAI;CAChD,CAAC;AAEF;;;;GAIG;AACH,SAAS,YAAY,CAAC,IAAY;IAC9B,QAAQ,IAAI,EAAE,CAAC;QACX,KAAK,YAAY,CAAC,IAAI,CAAC;QACvB,KAAK,YAAY,CAAC,KAAK,CAAC;QACxB,KAAK,YAAY,CAAC,GAAG,CAAC;QACtB,KAAK,YAAY,CAAC,KAAK;YACnB,OAAO,IAAI,CAAC;QAChB,KAAK,YAAY,CAAC,aAAa,CAAC;QAChC,KAAK,YAAY,CAAC,cAAc,CAAC;QACjC,KAAK,YAAY,CAAC,YAAY;YAC1B,OAAO,KAAK,CAAC;QACjB;YACI,MAAM,IAAI,KAAK,CAAC,iBAAiB,IAAI,GAAG,CAAC,CAAC;IAClD,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,gEAAgE;AAChE,MAAM,UAAU,0BAA0B,CAAC,aAAwD,EAAE,MAAc;IAC/G,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IAClC,MAAM,eAAe,GAAG,MAAM,CAAC,gBAAgB,CAAC;IAEhD,IAAI,CAAC,eAAe,EAAE,sBAAsB,EAAE,CAAC;QAC3C,OAAO;IACX,CAAC;IAED,IAAI,uBAAuB,GAAwC,IAAI,CAAC;IAExE,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;QAC/B,MAAM,mBAAmB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAEhD,IAAI,CAAC,mBAAmB,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,EAAE,CAAC;YACvE,SAAS;QACb,CAAC;QAED,MAAM,uBAAuB,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC;QAC/G,MAAM,gBAAgB,GAAG,eAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAEtE,IACI,CAAC,uBAAuB,KAAK,YAAY,CAAC,KAAK,IAAI,gBAAgB,KAAK,SAAS,CAAC;YAClF,CAAC,gBAAgB,KAAK,SAAS,IAAI,gBAAgB,KAAK,uBAAuB,CAAC,EAClF,CAAC;YACC,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC3B,uBAAuB,GAAG,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,cAAc,EAAE,KAAK,CAAE,CAAC;YAChG,CAAC;YACD,eAAe,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,uBAAuB,CAAC;YACvE,IAAI,uBAAuB,KAAK,YAAY,CAAC,KAAK,EAAE,CAAC;gBACjD,uBAAuB,CAAC,oCAAqC,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACtG,IAAI,YAAY,CAAC,uBAAuB,CAAC,EAAE,CAAC;oBACxC,uBAAuB,CAAC,oCAAqC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC9E,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAI,uBAAuB,EAAE,CAAC;QAC1B,6LAA6L;QAC7L,kGAAkG;QAClG,MAAM,qBAAqB,GAAG,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC;QACjE,MAAM,CAAC,KAAK,CAAC,qBAAqB,GAAG,SAAS,CAAC;QAE/C,mEAAmE;QACnE,MAAM,CAAC,uBAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,sDAAsD,EAAE,uBAAuB,CAAC,CAAC;QAEvI,MAAM,CAAC,KAAK,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;IAC/D,CAAC;AACL,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { _IShaderProcessingContext } from \"../Engines/Processors/shaderProcessingOptions\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { VertexBuffer } from \"../Meshes/buffer\";\r\n\r\nconst VertexBufferKindForNonFloatProcessing: { [kind: string]: boolean } = {\r\n    [VertexBuffer.PositionKind]: true,\r\n    [VertexBuffer.NormalKind]: true,\r\n    [VertexBuffer.TangentKind]: true,\r\n    [VertexBuffer.UVKind]: true,\r\n    [VertexBuffer.UV2Kind]: true,\r\n    [VertexBuffer.UV3Kind]: true,\r\n    [VertexBuffer.UV4Kind]: true,\r\n    [VertexBuffer.UV5Kind]: true,\r\n    [VertexBuffer.UV6Kind]: true,\r\n    [VertexBuffer.ColorKind]: true,\r\n    [VertexBuffer.ColorInstanceKind]: true,\r\n    [VertexBuffer.MatricesIndicesKind]: true,\r\n    [VertexBuffer.MatricesWeightsKind]: true,\r\n    [VertexBuffer.MatricesIndicesExtraKind]: true,\r\n    [VertexBuffer.MatricesWeightsExtraKind]: true,\r\n};\r\n\r\n/**\r\n * Indicates if the type is a signed or unsigned type\r\n * @param type Type to check\r\n * @returns True if it is a signed type\r\n */\r\nfunction IsSignedType(type: number): boolean {\r\n    switch (type) {\r\n        case VertexBuffer.BYTE:\r\n        case VertexBuffer.SHORT:\r\n        case VertexBuffer.INT:\r\n        case VertexBuffer.FLOAT:\r\n            return true;\r\n        case VertexBuffer.UNSIGNED_BYTE:\r\n        case VertexBuffer.UNSIGNED_SHORT:\r\n        case VertexBuffer.UNSIGNED_INT:\r\n            return false;\r\n        default:\r\n            throw new Error(`Invalid type '${type}'`);\r\n    }\r\n}\r\n\r\n/**\r\n * Checks whether some vertex buffers that should be of type float are of a different type (int, byte...).\r\n * If so, trigger a shader recompilation to give the shader processor the opportunity to update the code accordingly.\r\n * @param vertexBuffers List of vertex buffers to check\r\n * @param effect The effect (shaders) that should be recompiled if needed\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport function checkNonFloatVertexBuffers(vertexBuffers: { [key: string]: Nullable<VertexBuffer> }, effect: Effect): void {\r\n    const engine = effect.getEngine();\r\n    const pipelineContext = effect._pipelineContext;\r\n\r\n    if (!pipelineContext?.vertexBufferKindToType) {\r\n        return;\r\n    }\r\n\r\n    let shaderProcessingContext: Nullable<_IShaderProcessingContext> = null;\r\n\r\n    for (const kind in vertexBuffers) {\r\n        const currentVertexBuffer = vertexBuffers[kind];\r\n\r\n        if (!currentVertexBuffer || !VertexBufferKindForNonFloatProcessing[kind]) {\r\n            continue;\r\n        }\r\n\r\n        const currentVertexBufferType = currentVertexBuffer.normalized ? VertexBuffer.FLOAT : currentVertexBuffer.type;\r\n        const vertexBufferType = pipelineContext.vertexBufferKindToType[kind];\r\n\r\n        if (\r\n            (currentVertexBufferType !== VertexBuffer.FLOAT && vertexBufferType === undefined) ||\r\n            (vertexBufferType !== undefined && vertexBufferType !== currentVertexBufferType)\r\n        ) {\r\n            if (!shaderProcessingContext) {\r\n                shaderProcessingContext = engine._getShaderProcessingContext(effect.shaderLanguage, false)!;\r\n            }\r\n            pipelineContext.vertexBufferKindToType[kind] = currentVertexBufferType;\r\n            if (currentVertexBufferType !== VertexBuffer.FLOAT) {\r\n                shaderProcessingContext.vertexBufferKindToNumberOfComponents![kind] = VertexBuffer.DeduceStride(kind);\r\n                if (IsSignedType(currentVertexBufferType)) {\r\n                    shaderProcessingContext.vertexBufferKindToNumberOfComponents![kind] *= -1;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    if (shaderProcessingContext) {\r\n        // We temporarily disable parallel compilation of shaders because we want new shaders to be compiled after the _processShaderCode call, so that they are in effect for the rest of the frame.\r\n        // There is no additional call to async so the _processShaderCodeAsync will execute synchronously.\r\n        const parallelShaderCompile = engine._caps.parallelShaderCompile;\r\n        engine._caps.parallelShaderCompile = undefined;\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        effect._processShaderCodeAsync(null, engine._features._checkNonFloatVertexBuffersDontRecreatePipelineContext, shaderProcessingContext);\r\n\r\n        engine._caps.parallelShaderCompile = parallelShaderCompile;\r\n    }\r\n}\r\n"]}