import type { Nullable } from "../types.js";
import type { Scene } from "../scene.js";
/**
 * Enum defining the type of properties that can be edited in the property pages in the node editor
 */
export declare enum PropertyTypeForEdition {
    /** property is a boolean */
    Boolean = 0,
    /** property is a float */
    Float = 1,
    /** property is a int */
    Int = 2,
    /** property is a Vector2 */
    Vector2 = 3,
    /** property is a list of values */
    List = 4,
    /** property is a Color4 */
    Color4 = 5,
    /** property (int) should be edited as a combo box with a list of sampling modes */
    SamplingMode = 6,
    /** property (int) should be edited as a combo box with a list of texture formats */
    TextureFormat = 7,
    /** property (int) should be edited as a combo box with a list of texture types */
    TextureType = 8
}
/**
 * Interface that defines an option in a variable of type list
 */
export interface IEditablePropertyListOption {
    /** label of the option */
    label: string;
    /** value of the option */
    value: number;
}
/**
 * Interface that defines the options available for an editable property
 */
export interface IEditablePropertyOption {
    /**
     * Define if the property is displayed inside the source block or in a separate property tab
     */
    embedded?: boolean;
    /** min value */
    min?: number;
    /** max value */
    max?: number;
    /** notifiers: indicates which actions to take when the property is changed */
    notifiers?: {
        /** the entity should be rebuilt */
        rebuild?: boolean;
        /** the preview should be updated */
        update?: boolean;
        /** the onPreviewCommandActivated observer of the preview manager should be triggered */
        activatePreviewCommand?: boolean;
        /** a callback to trigger */
        callback?: (scene: Nullable<Scene>, block: any) => boolean | undefined | void;
        /** a callback to validate the property. Returns true if the property is ok, else false. If false, the rebuild/update/callback events won't be called */
        onValidation?: (block: any, propertyName: string) => boolean;
    };
    /** list of the options for a variable of type list */
    options?: IEditablePropertyListOption[];
}
/**
 * Interface that describes an editable property
 */
export interface IPropertyDescriptionForEdition {
    /** name of the property */
    propertyName: string;
    /** display name of the property */
    displayName: string;
    /** type of the property */
    type: PropertyTypeForEdition;
    /** group of the property - all properties with the same group value will be displayed in a specific section */
    groupName: string;
    /** options for the property */
    options: IEditablePropertyOption;
    /** name of the class that contains the property */
    className: string;
}
/**
 * Decorator that flags a property in a node block as being editable
 * @param displayName the display name of the property
 * @param propertyType the type of the property
 * @param groupName the group name of the property
 * @param options the options of the property
 * @returns the decorator
 */
export declare function editableInPropertyPage(displayName: string, propertyType?: PropertyTypeForEdition, groupName?: string, options?: IEditablePropertyOption): (target: any, propertyKey: string) => void;
