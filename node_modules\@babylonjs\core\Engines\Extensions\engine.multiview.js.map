{"version": 3, "file": "engine.multiview.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.multiview.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAGlG,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAC7D,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAE,qBAAqB,EAAE,MAAM,gDAAgD,CAAC;AACvF,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;AA8BnD,MAAM,CAAC,SAAS,CAAC,kCAAkC,GAAG,UAAU,KAAa,EAAE,MAAc,EAAE,YAA2B,EAAE,mBAAkC;IAC1J,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC;QAC5B,4CAA4C;QAC5C,MAAM,4BAA4B,CAAC;IACvC,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAA6B,CAAC;IAEvH,SAAS,CAAC,YAAY,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAEhD,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,yCAAiC,IAAI,CAAC,CAAC;IACvF,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;IAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;IAChC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC;IAEnC,IAAI,CAAC,YAAY,EAAE,CAAC;QAChB,YAAY,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;QAClC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QACjD,EAAU,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACjF,CAAC;IAED,SAAS,CAAC,kBAAkB,GAAG,YAAY,CAAC;IAE5C,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACvB,mBAAmB,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;QACzC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;QACxD,EAAU,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,EAAG,EAAU,CAAC,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACrG,CAAC;IAED,SAAS,CAAC,yBAAyB,GAAG,mBAAmB,CAAC;IAE1D,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;IAE/B,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;IACvC,SAAS,CAAC,oBAAoB,GAAG,eAAe,CAAC;IAEjD,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,iBAAsC;IACxF,MAAM,gBAAgB,GAAG,iBAA6C,CAAC;IAEvE,MAAM,EAAE,GAAQ,IAAI,CAAC,GAAG,CAAC;IACzB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC;IAEvE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC9E,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,YAAY,CAAC,CAAC;IACvE,IAAI,gBAAgB,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;QACpF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC;YACjC,GAAG,CAAC,yCAAyC,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjK,GAAG,CAAC,yCAAyC,CACzC,EAAE,CAAC,gBAAgB,EACnB,EAAE,CAAC,wBAAwB,EAC3B,gBAAgB,CAAC,yBAAyB,EAC1C,CAAC,EACD,gBAAgB,CAAC,OAAO,EACxB,CAAC,EACD,CAAC,CACJ,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,GAAG,CAAC,8BAA8B,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5H,GAAG,CAAC,8BAA8B,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,wBAAwB,EAAE,gBAAgB,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9I,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,4CAA4C;QAC5C,MAAM,gCAAgC,CAAC;IAC3C,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,iBAAsC;IACxF,MAAM,gBAAgB,GAAG,iBAA6C,CAAC;IAEvE,MAAM,EAAE,GAAQ,IAAI,CAAC,GAAG,CAAC;IACzB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC;IAEvE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC9E,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,YAAY,CAAC,CAAC;IACvE,IAAI,gBAAgB,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;QACpF,GAAG,CAAC,8BAA8B,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5H,GAAG,CAAC,8BAA8B,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtI,CAAC;SAAM,CAAC;QACJ,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtD,CAAC;AACL,CAAC,CAAC;AAgCF,MAAM,CAAC,SAAS,CAAC,yBAAyB,GAAG,KAAK,CAAC;AAEnD,MAAM,CAAC,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAE1C,MAAM,CAAC,SAAS,CAAC,+BAA+B,GAAG,UAAU,KAAa,EAAE,MAAc;IACtF,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;IAC1G,CAAC;SAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,IAAI,KAAK,IAAI,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,IAAI,MAAM,EAAE,CAAC;QAChH,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;IAC1G,CAAC;AACL,CAAC,CAAC;AAkBF,SAAS,kBAAkB,CAAC,MAAsB,EAAE,IAAa;IAC7D,MAAM,GAAG,GAAG,IAAI,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7D,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;IACrC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IACtC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAC3B,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;IACjC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;IAClC,OAAO,GAAG,CAAC;AACf,CAAC;AAED,MAAM,+BAA+B,GAAG,KAAK,CAAC,SAAS,CAAC,wBAAwB,CAAC;AAEjF,KAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAClD,KAAK,CAAC,SAAS,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAC1C,KAAK,CAAC,SAAS,CAAC,mBAAmB,GAAG;IAClC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,iBAAiB,CAAC,CAAC;AACtF,CAAC,CAAC;AACF,KAAK,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,IAAa;IAC9D,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,OAAO,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IACD,OAAO,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5D,CAAC,CAAC;AACF,KAAK,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,KAAc,EAAE,WAAoB;IAChF,IAAI,KAAK,IAAI,WAAW,EAAE,CAAC;QACvB,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,KAAK,IAAI,WAAW,EAAE,CAAC;QACvB,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mDAAmD;IACjI,CAAC;IAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChF,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/D,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC/E,CAAC;AACL,CAAC,CAAC;AACF,KAAK,CAAC,SAAS,CAAC,4BAA4B,GAAG,UAAU,MAAc;IACnE,0EAA0E;IAC1E,+EAA+E;IAC/E,wGAAwG;IAExG,gCAAgC;IAChC,MAAM,CAAC,+BAA+B,CAClC,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAC3J,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CACjK,CAAC;IACF,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IACD,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;IACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC9B,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;IAEjC,8DAA8D;IAC9D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC/C,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;YACxC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { Engine } from \"../../Engines/engine\";\r\nimport { Scene } from \"../../scene\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport { Matrix, TmpVectors } from \"../../Maths/math.vector\";\r\nimport { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport { MultiviewRenderTarget } from \"../../Materials/Textures/MultiviewRenderTarget\";\r\nimport { Frustum } from \"../../Maths/math.frustum\";\r\nimport type { WebGLRenderTargetWrapper } from \"../WebGL/webGLRenderTargetWrapper\";\r\nimport type { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { AbstractEngine } from \"../abstractEngine\";\r\n\r\ndeclare module \"../../Engines/engine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Engine {\r\n        /**\r\n         * Creates a new multiview render target\r\n         * @param width defines the width of the texture\r\n         * @param height defines the height of the texture\r\n         * @returns the created multiview render target wrapper\r\n         */\r\n        createMultiviewRenderTargetTexture(width: number, height: number, colorTexture?: WebGLTexture, depthStencilTexture?: WebGLTexture): RenderTargetWrapper;\r\n\r\n        /**\r\n         * Binds a multiview render target wrapper to be drawn to\r\n         * @param multiviewTexture render target wrapper to bind\r\n         */\r\n        bindMultiviewFramebuffer(multiviewTexture: RenderTargetWrapper): void;\r\n\r\n        /**\r\n         * Binds a Space Warp render target wrapper to be drawn to\r\n         * @param spaceWarpTexture render target wrapper to bind\r\n         */\r\n        bindSpaceWarpFramebuffer(spaceWarpTexture: RenderTargetWrapper): void;\r\n    }\r\n}\r\n\r\nEngine.prototype.createMultiviewRenderTargetTexture = function (width: number, height: number, colorTexture?: WebGLTexture, depthStencilTexture?: WebGLTexture) {\r\n    const gl = this._gl;\r\n\r\n    if (!this.getCaps().multiview) {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Multiview is not supported\";\r\n    }\r\n\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(false, false, { width, height }) as WebGLRenderTargetWrapper;\r\n\r\n    rtWrapper._framebuffer = gl.createFramebuffer();\r\n\r\n    const internalTexture = new InternalTexture(this, InternalTextureSource.Unknown, true);\r\n    internalTexture.width = width;\r\n    internalTexture.height = height;\r\n    internalTexture.isMultiview = true;\r\n\r\n    if (!colorTexture) {\r\n        colorTexture = gl.createTexture();\r\n        gl.bindTexture(gl.TEXTURE_2D_ARRAY, colorTexture);\r\n        (gl as any).texStorage3D(gl.TEXTURE_2D_ARRAY, 1, gl.RGBA8, width, height, 2);\r\n    }\r\n\r\n    rtWrapper._colorTextureArray = colorTexture;\r\n\r\n    if (!depthStencilTexture) {\r\n        depthStencilTexture = gl.createTexture();\r\n        gl.bindTexture(gl.TEXTURE_2D_ARRAY, depthStencilTexture);\r\n        (gl as any).texStorage3D(gl.TEXTURE_2D_ARRAY, 1, (gl as any).DEPTH24_STENCIL8, width, height, 2);\r\n    }\r\n\r\n    rtWrapper._depthStencilTextureArray = depthStencilTexture;\r\n\r\n    internalTexture.isReady = true;\r\n\r\n    rtWrapper.setTextures(internalTexture);\r\n    rtWrapper._depthStencilTexture = internalTexture;\r\n\r\n    return rtWrapper;\r\n};\r\n\r\nEngine.prototype.bindMultiviewFramebuffer = function (_multiviewTexture: RenderTargetWrapper) {\r\n    const multiviewTexture = _multiviewTexture as WebGLRenderTargetWrapper;\r\n\r\n    const gl: any = this._gl;\r\n    const ext = this.getCaps().oculusMultiview || this.getCaps().multiview;\r\n\r\n    this.bindFramebuffer(multiviewTexture, undefined, undefined, undefined, true);\r\n    gl.bindFramebuffer(gl.DRAW_FRAMEBUFFER, multiviewTexture._framebuffer);\r\n    if (multiviewTexture._colorTextureArray && multiviewTexture._depthStencilTextureArray) {\r\n        if (this.getCaps().oculusMultiview) {\r\n            ext.framebufferTextureMultisampleMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.COLOR_ATTACHMENT0, multiviewTexture._colorTextureArray, 0, multiviewTexture.samples, 0, 2);\r\n            ext.framebufferTextureMultisampleMultiviewOVR(\r\n                gl.DRAW_FRAMEBUFFER,\r\n                gl.DEPTH_STENCIL_ATTACHMENT,\r\n                multiviewTexture._depthStencilTextureArray,\r\n                0,\r\n                multiviewTexture.samples,\r\n                0,\r\n                2\r\n            );\r\n        } else {\r\n            ext.framebufferTextureMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.COLOR_ATTACHMENT0, multiviewTexture._colorTextureArray, 0, 0, 2);\r\n            ext.framebufferTextureMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.DEPTH_STENCIL_ATTACHMENT, multiviewTexture._depthStencilTextureArray, 0, 0, 2);\r\n        }\r\n    } else {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Invalid multiview frame buffer\";\r\n    }\r\n};\r\n\r\nEngine.prototype.bindSpaceWarpFramebuffer = function (_spaceWarpTexture: RenderTargetWrapper) {\r\n    const spaceWarpTexture = _spaceWarpTexture as WebGLRenderTargetWrapper;\r\n\r\n    const gl: any = this._gl;\r\n    const ext = this.getCaps().oculusMultiview || this.getCaps().multiview;\r\n\r\n    this.bindFramebuffer(spaceWarpTexture, undefined, undefined, undefined, true);\r\n    gl.bindFramebuffer(gl.DRAW_FRAMEBUFFER, spaceWarpTexture._framebuffer);\r\n    if (spaceWarpTexture._colorTextureArray && spaceWarpTexture._depthStencilTextureArray) {\r\n        ext.framebufferTextureMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.COLOR_ATTACHMENT0, spaceWarpTexture._colorTextureArray, 0, 0, 2);\r\n        ext.framebufferTextureMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.DEPTH_ATTACHMENT, spaceWarpTexture._depthStencilTextureArray, 0, 0, 2);\r\n    } else {\r\n        throw new Error(\"Invalid Space Warp framebuffer\");\r\n    }\r\n};\r\n\r\ndeclare module \"../../Cameras/camera\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Camera {\r\n        /**\r\n         * @internal\r\n         * For cameras that cannot use multiview images to display directly. (e.g. webVR camera will render to multiview texture, then copy to each eye texture and go from there)\r\n         */\r\n        _useMultiviewToSingleView: boolean;\r\n        /**\r\n         * @internal\r\n         * For cameras that cannot use multiview images to display directly. (e.g. webVR camera will render to multiview texture, then copy to each eye texture and go from there)\r\n         */\r\n        _multiviewTexture: Nullable<RenderTargetTexture>;\r\n\r\n        /**\r\n         * @internal\r\n         * For WebXR cameras that are rendering to multiview texture arrays.\r\n         */\r\n        _renderingMultiview: boolean;\r\n\r\n        /**\r\n         * @internal\r\n         * ensures the multiview texture of the camera exists and has the specified width/height\r\n         * @param width height to set on the multiview texture\r\n         * @param height width to set on the multiview texture\r\n         */\r\n        _resizeOrCreateMultiviewTexture(width: number, height: number): void;\r\n    }\r\n}\r\n\r\nCamera.prototype._useMultiviewToSingleView = false;\r\n\r\nCamera.prototype._multiviewTexture = null;\r\n\r\nCamera.prototype._resizeOrCreateMultiviewTexture = function (width: number, height: number) {\r\n    if (!this._multiviewTexture) {\r\n        this._multiviewTexture = new MultiviewRenderTarget(this.getScene(), { width: width, height: height });\r\n    } else if (this._multiviewTexture.getRenderWidth() != width || this._multiviewTexture.getRenderHeight() != height) {\r\n        this._multiviewTexture.dispose();\r\n        this._multiviewTexture = new MultiviewRenderTarget(this.getScene(), { width: width, height: height });\r\n    }\r\n};\r\n\r\ndeclare module \"../../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /** @internal */\r\n        _transformMatrixR: Matrix;\r\n        /** @internal */\r\n        _multiviewSceneUbo: Nullable<UniformBuffer>;\r\n        /** @internal */\r\n        _createMultiviewUbo(): void;\r\n        /** @internal */\r\n        _updateMultiviewUbo(viewR?: Matrix, projectionR?: Matrix): void;\r\n        /** @internal */\r\n        _renderMultiviewToSingleView(camera: Camera): void;\r\n    }\r\n}\r\n\r\nfunction CreateMultiviewUbo(engine: AbstractEngine, name?: string) {\r\n    const ubo = new UniformBuffer(engine, undefined, true, name);\r\n    ubo.addUniform(\"viewProjection\", 16);\r\n    ubo.addUniform(\"viewProjectionR\", 16);\r\n    ubo.addUniform(\"view\", 16);\r\n    ubo.addUniform(\"projection\", 16);\r\n    ubo.addUniform(\"vEyePosition\", 4);\r\n    return ubo;\r\n}\r\n\r\nconst CurrentCreateSceneUniformBuffer = Scene.prototype.createSceneUniformBuffer;\r\n\r\nScene.prototype._transformMatrixR = Matrix.Zero();\r\nScene.prototype._multiviewSceneUbo = null;\r\nScene.prototype._createMultiviewUbo = function () {\r\n    this._multiviewSceneUbo = CreateMultiviewUbo(this.getEngine(), \"scene_multiview\");\r\n};\r\nScene.prototype.createSceneUniformBuffer = function (name?: string): UniformBuffer {\r\n    if (this._multiviewSceneUbo) {\r\n        return CreateMultiviewUbo(this.getEngine(), name);\r\n    }\r\n    return CurrentCreateSceneUniformBuffer.bind(this)(name);\r\n};\r\nScene.prototype._updateMultiviewUbo = function (viewR?: Matrix, projectionR?: Matrix) {\r\n    if (viewR && projectionR) {\r\n        viewR.multiplyToRef(projectionR, this._transformMatrixR);\r\n    }\r\n\r\n    if (viewR && projectionR) {\r\n        viewR.multiplyToRef(projectionR, TmpVectors.Matrix[0]);\r\n        Frustum.GetRightPlaneToRef(TmpVectors.Matrix[0], this._frustumPlanes[3]); // Replace right plane by second camera right plane\r\n    }\r\n\r\n    if (this._multiviewSceneUbo) {\r\n        this._multiviewSceneUbo.updateMatrix(\"viewProjection\", this.getTransformMatrix());\r\n        this._multiviewSceneUbo.updateMatrix(\"viewProjectionR\", this._transformMatrixR);\r\n        this._multiviewSceneUbo.updateMatrix(\"view\", this._viewMatrix);\r\n        this._multiviewSceneUbo.updateMatrix(\"projection\", this._projectionMatrix);\r\n    }\r\n};\r\nScene.prototype._renderMultiviewToSingleView = function (camera: Camera) {\r\n    // Multiview is only able to be displayed directly for API's such as webXR\r\n    // This displays a multiview image by rendering to the multiview image and then\r\n    // copying the result into the sub cameras instead of rendering them and proceeding as normal from there\r\n\r\n    // Render to a multiview texture\r\n    camera._resizeOrCreateMultiviewTexture(\r\n        camera._rigPostProcess && camera._rigPostProcess && camera._rigPostProcess.width > 0 ? camera._rigPostProcess.width : this.getEngine().getRenderWidth(true),\r\n        camera._rigPostProcess && camera._rigPostProcess && camera._rigPostProcess.height > 0 ? camera._rigPostProcess.height : this.getEngine().getRenderHeight(true)\r\n    );\r\n    if (!this._multiviewSceneUbo) {\r\n        this._createMultiviewUbo();\r\n    }\r\n    camera.outputRenderTarget = camera._multiviewTexture;\r\n    this._renderForCamera(camera);\r\n    camera.outputRenderTarget = null;\r\n\r\n    // Consume the multiview texture through a shader for each eye\r\n    for (let index = 0; index < camera._rigCameras.length; index++) {\r\n        const engine = this.getEngine();\r\n        this._activeCamera = camera._rigCameras[index];\r\n        engine.setViewport(this._activeCamera.viewport);\r\n        if (this.postProcessManager) {\r\n            this.postProcessManager._prepareFrame();\r\n            this.postProcessManager._finalizeFrame(this._activeCamera.isIntermediate);\r\n        }\r\n    }\r\n};\r\n"]}