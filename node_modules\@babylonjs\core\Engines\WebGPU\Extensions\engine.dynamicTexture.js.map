{"version": 3, "file": "engine.dynamicTexture.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.dynamicTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,6CAA6C,CAAC;AAErG,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAElD,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAsCjE,YAAY,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,KAAa,EAAE,MAAc,EAAE,eAAwB,EAAE,YAAoB;IACjI,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,wCAAgC,CAAC;IACzE,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;IAE5B,IAAI,eAAe,EAAE,CAAC;QAClB,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC1F,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACjG,CAAC;IAED,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;IACxB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IAEpC,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAEtD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;QAClB,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACnF,CAAC;IAED,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAC1C,OAAkC,EAClC,MAAmB,EACnB,OAAgB,EAChB,cAAuB,KAAK,EAC5B,MAAe,EACf,gBAA0B,EAC1B,oBAA8B;IAE9B,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,OAAO;IACX,CAAC;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,EACtB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IAE3B,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;IAE1E,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,CAAC;QAChD,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACvG,CAAC;IAED,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,oBAAoB,CAAC,CAAC;IACnK,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,CAAC,qBAAqB,GAAG,MAAM,CAAC;IACvC,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACnC,OAAO,CAAC,OAAO,GAAG,OAAO,IAAI,KAAK,CAAC;IACnC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC", "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../../Materials/Textures/internalTexture\";\r\nimport type { ImageSource, Nullable } from \"../../../types\";\r\nimport { WebGPUEngine } from \"../../webgpuEngine\";\r\nimport type { WebGPUHardwareTexture } from \"../webgpuHardwareTexture\";\r\nimport { GetExponentOfTwo } from \"../../../Misc/tools.functions\";\r\nimport type { ICanvas } from \"../../../Engines/ICanvas\";\r\n\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a dynamic texture\r\n         * @param width defines the width of the texture\r\n         * @param height defines the height of the texture\r\n         * @param generateMipMaps defines if the engine should generate the mip levels\r\n         * @param samplingMode defines the required sampling mode (Texture.NEAREST_SAMPLINGMODE by default)\r\n         * @returns the dynamic texture inside an InternalTexture\r\n         */\r\n        createDynamicTexture(width: number, height: number, generateMipMaps: boolean, samplingMode: number): InternalTexture;\r\n\r\n        /**\r\n         * Update the content of a dynamic texture\r\n         * @param texture defines the texture to update\r\n         * @param source defines the source containing the data\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param premulAlpha defines if alpha is stored as premultiplied\r\n         * @param format defines the format of the data\r\n         * @param forceBindTexture if the texture should be forced to be bound eg. after a graphics context loss (Default: false)\r\n         * @param allowGPUOptimization true to allow some specific GPU optimizations (subject to engine feature \"allowGPUOptimizationsForGUI\" being true)\r\n         */\r\n        updateDynamicTexture(\r\n            texture: Nullable<InternalTexture>,\r\n            source: ImageSource | ICanvas,\r\n            invertY?: boolean,\r\n            premulAlpha?: boolean,\r\n            format?: number,\r\n            forceBindTexture?: boolean,\r\n            allowGPUOptimization?: boolean\r\n        ): void;\r\n    }\r\n}\r\n\r\nWebGPUEngine.prototype.createDynamicTexture = function (width: number, height: number, generateMipMaps: boolean, samplingMode: number): InternalTexture {\r\n    const texture = new InternalTexture(this, InternalTextureSource.Dynamic);\r\n    texture.baseWidth = width;\r\n    texture.baseHeight = height;\r\n\r\n    if (generateMipMaps) {\r\n        width = this.needPOTTextures ? GetExponentOfTwo(width, this._caps.maxTextureSize) : width;\r\n        height = this.needPOTTextures ? GetExponentOfTwo(height, this._caps.maxTextureSize) : height;\r\n    }\r\n\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.isReady = false;\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n\r\n    this.updateTextureSamplingMode(samplingMode, texture);\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    if (width && height) {\r\n        this._textureHelper.createGPUTextureForInternalTexture(texture, width, height);\r\n    }\r\n\r\n    return texture;\r\n};\r\n\r\nWebGPUEngine.prototype.updateDynamicTexture = function (\r\n    texture: Nullable<InternalTexture>,\r\n    source: ImageSource,\r\n    invertY: boolean,\r\n    premulAlpha: boolean = false,\r\n    format?: number,\r\n    forceBindTexture?: boolean,\r\n    allowGPUOptimization?: boolean\r\n): void {\r\n    if (!texture) {\r\n        return;\r\n    }\r\n\r\n    const width = source.width,\r\n        height = source.height;\r\n\r\n    let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n    if (!texture._hardwareTexture?.underlyingResource) {\r\n        gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture, width, height);\r\n    }\r\n\r\n    this._textureHelper.updateTexture(source, texture, width, height, texture.depth, gpuTextureWrapper.format, 0, 0, invertY, premulAlpha, 0, 0, allowGPUOptimization);\r\n    if (texture.generateMipMaps) {\r\n        this._generateMipmaps(texture);\r\n    }\r\n\r\n    texture._dynamicTextureSource = source;\r\n    texture._premulAlpha = premulAlpha;\r\n    texture.invertY = invertY || false;\r\n    texture.isReady = true;\r\n};\r\n"]}