{"version": 3, "file": "abstractEngine.stencil.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.stencil.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAEnD,OAAO,wBAAwB,CAAC;AA+KhC,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG;IACxC,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;AAC1C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,MAAe;IACjE,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,MAAM,CAAC;AAC5C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG;IACtC,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;AAC1C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,IAAY;IAC5D,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC;AAC1C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG;IAC1C,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;AAC1C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,sBAAsB,GAAG;IAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;AAC9C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,2BAA2B,GAAG;IACnD,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;AAC7C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,sBAAsB,GAAG;IAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC;AAC9C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,WAAmB;IACvE,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC;AACjD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAAU,WAAmB;IAC3E,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,WAAW,CAAC;AACrD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAU,SAAiB;IAC9E,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,SAAS,CAAC;AAClD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAAU,IAAY;IACpE,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,IAAI,CAAC;AAC9C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,uBAAuB,GAAG;IAC/C,OAAO,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;AACnD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,2BAA2B,GAAG;IACnD,OAAO,IAAI,CAAC,aAAa,CAAC,wBAAwB,CAAC;AACvD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,4BAA4B,GAAG;IACpD,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;AACjD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,gCAAgC,GAAG;IACxD,OAAO,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC;AACrD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,uBAAuB,GAAG;IAC/C,OAAO,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC;AACxD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,2BAA2B,GAAG;IACnD,OAAO,IAAI,CAAC,aAAa,CAAC,6BAA6B,CAAC;AAC5D,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,SAAiB;IAC1E,IAAI,CAAC,aAAa,CAAC,oBAAoB,GAAG,SAAS,CAAC;AACxD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAU,SAAiB;IAC9E,IAAI,CAAC,aAAa,CAAC,wBAAwB,GAAG,SAAS,CAAC;AAC5D,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,4BAA4B,GAAG,UAAU,SAAiB;IAC/E,IAAI,CAAC,aAAa,CAAC,kBAAkB,GAAG,SAAS,CAAC;AACtD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,gCAAgC,GAAG,UAAU,SAAiB;IACnF,IAAI,CAAC,aAAa,CAAC,sBAAsB,GAAG,SAAS,CAAC;AAC1D,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,SAAiB;IAC1E,IAAI,CAAC,aAAa,CAAC,yBAAyB,GAAG,SAAS,CAAC;AAC7D,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAU,SAAiB;IAC9E,IAAI,CAAC,aAAa,CAAC,6BAA6B,GAAG,SAAS,CAAC;AACjE,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,iBAAiB,GAAG;IACzC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;IACpD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACxD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IAChD,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;IAClE,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;IAClE,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;IAC5E,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AACtE,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,mBAAmB,GAAG;IAC3C,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACrD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACjD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC/D,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC/D,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IACzE,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACnE,CAAC,CAAC", "sourcesContent": ["import { AbstractEngine } from \"../abstractEngine\";\r\n\r\nimport \"./abstractEngine.alpha\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /** @internal */\r\n        _cachedStencilBuffer: boolean;\r\n        /** @internal */\r\n        _cachedStencilFunction: number;\r\n        /** @internal */\r\n        _cachedStencilMask: number;\r\n        /** @internal */\r\n        _cachedStencilOperationPass: number;\r\n        /** @internal */\r\n        _cachedStencilOperationFail: number;\r\n        /** @internal */\r\n        _cachedStencilOperationDepthFail: number;\r\n        /** @internal */\r\n        _cachedStencilReference: number;\r\n\r\n        /**\r\n         * Gets the current stencil operation when stencil passes\r\n         * @returns a number defining stencil operation to use when stencil passes\r\n         */\r\n        getStencilOperationPass(): number;\r\n\r\n        /**\r\n         * Gets the current back stencil operation when stencil passes\r\n         * @returns a number defining back stencil operation to use when stencil passes\r\n         */\r\n        getStencilBackOperationPass(): number;\r\n\r\n        /**\r\n         * Gets a boolean indicating if stencil buffer is enabled\r\n         * @returns the current stencil buffer state\r\n         */\r\n        getStencilBuffer(): boolean;\r\n\r\n        /**\r\n         * Enable or disable the stencil buffer\r\n         * @param enable defines if the stencil buffer must be enabled or disabled\r\n         */\r\n        setStencilBuffer(enable: boolean): void;\r\n\r\n        /**\r\n         * Gets the current stencil mask\r\n         * @returns a number defining the new stencil mask to use\r\n         */\r\n        getStencilMask(): number;\r\n        /**\r\n         * Sets the current stencil mask\r\n         * @param mask defines the new stencil mask to use\r\n         */\r\n        setStencilMask(mask: number): void;\r\n\r\n        /**\r\n         * Gets the current stencil function\r\n         * @returns a number defining the stencil function to use\r\n         */\r\n        getStencilFunction(): number;\r\n\r\n        /**\r\n         * Gets the current back stencil function\r\n         * @returns a number defining the back stencil function to use\r\n         */\r\n        getStencilBackFunction(): number;\r\n\r\n        /**\r\n         * Gets the current stencil reference value\r\n         * @returns a number defining the stencil reference value to use\r\n         */\r\n        getStencilFunctionReference(): number;\r\n\r\n        /**\r\n         * Gets the current stencil mask\r\n         * @returns a number defining the stencil mask to use\r\n         */\r\n        getStencilFunctionMask(): number;\r\n\r\n        /**\r\n         * Sets the current stencil function\r\n         * @param stencilFunc defines the new stencil function to use\r\n         */\r\n        setStencilFunction(stencilFunc: number): void;\r\n\r\n        /**\r\n         * Sets the current back stencil function\r\n         * @param stencilFunc defines the new back stencil function to use\r\n         */\r\n        setStencilBackFunction(stencilFunc: number): void;\r\n\r\n        /**\r\n         * Sets the current stencil reference\r\n         * @param reference defines the new stencil reference to use\r\n         */\r\n        setStencilFunctionReference(reference: number): void;\r\n\r\n        /**\r\n         * Sets the current stencil mask\r\n         * @param mask defines the new stencil mask to use\r\n         */\r\n        setStencilFunctionMask(mask: number): void;\r\n\r\n        /**\r\n         * Gets the current stencil operation when stencil fails\r\n         * @returns a number defining stencil operation to use when stencil fails\r\n         */\r\n        getStencilOperationFail(): number;\r\n\r\n        /**\r\n         * Gets the current back stencil operation when stencil fails\r\n         * @returns a number defining back stencil operation to use when stencil fails\r\n         */\r\n        getStencilBackOperationFail(): number;\r\n\r\n        /**\r\n         * Gets the current stencil operation when depth fails\r\n         * @returns a number defining stencil operation to use when depth fails\r\n         */\r\n        getStencilOperationDepthFail(): number;\r\n\r\n        /**\r\n         * Gets the current back stencil operation when depth fails\r\n         * @returns a number defining back stencil operation to use when depth fails\r\n         */\r\n        getStencilBackOperationDepthFail(): number;\r\n\r\n        /**\r\n         * Sets the stencil operation to use when stencil fails\r\n         * @param operation defines the stencil operation to use when stencil fails\r\n         */\r\n        setStencilOperationFail(operation: number): void;\r\n\r\n        /**\r\n         * Sets the back stencil operation to use when stencil fails\r\n         * @param operation defines the back stencil operation to use when stencil fails\r\n         */\r\n        setStencilBackOperationFail(operation: number): void;\r\n\r\n        /**\r\n         * Sets the stencil operation to use when depth fails\r\n         * @param operation defines the stencil operation to use when depth fails\r\n         */\r\n        setStencilOperationDepthFail(operation: number): void;\r\n\r\n        /**\r\n         * Sets the back stencil operation to use when depth fails\r\n         * @param operation defines the back stencil operation to use when depth fails\r\n         */\r\n        setStencilBackOperationDepthFail(operation: number): void;\r\n\r\n        /**\r\n         * Sets the stencil operation to use when stencil passes\r\n         * @param operation defines the stencil operation to use when stencil passes\r\n         */\r\n        setStencilOperationPass(operation: number): void;\r\n\r\n        /**\r\n         * Sets the back stencil operation to use when stencil passes\r\n         * @param operation defines the back stencil operation to use when stencil passes\r\n         */\r\n        setStencilBackOperationPass(operation: number): void;\r\n\r\n        /**\r\n         * Caches the state of the stencil buffer\r\n         */\r\n        cacheStencilState(): void;\r\n\r\n        /**\r\n         * Restores the state of the stencil buffer\r\n         */\r\n        restoreStencilState(): void;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype.getStencilBuffer = function (): boolean {\r\n    return this._stencilState.stencilTest;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilBuffer = function (enable: boolean): void {\r\n    this._stencilState.stencilTest = enable;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilMask = function (): number {\r\n    return this._stencilState.stencilMask;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilMask = function (mask: number): void {\r\n    this._stencilState.stencilMask = mask;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilFunction = function (): number {\r\n    return this._stencilState.stencilFunc;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilBackFunction = function (): number {\r\n    return this._stencilState.stencilBackFunc;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilFunctionReference = function (): number {\r\n    return this._stencilState.stencilFuncRef;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilFunctionMask = function (): number {\r\n    return this._stencilState.stencilFuncMask;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilFunction = function (stencilFunc: number) {\r\n    this._stencilState.stencilFunc = stencilFunc;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilBackFunction = function (stencilFunc: number) {\r\n    this._stencilState.stencilBackFunc = stencilFunc;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilFunctionReference = function (reference: number): void {\r\n    this._stencilState.stencilFuncRef = reference;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilFunctionMask = function (mask: number): void {\r\n    this._stencilState.stencilFuncMask = mask;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilOperationFail = function (): number {\r\n    return this._stencilState.stencilOpStencilFail;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilBackOperationFail = function (): number {\r\n    return this._stencilState.stencilBackOpStencilFail;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilOperationDepthFail = function (): number {\r\n    return this._stencilState.stencilOpDepthFail;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilBackOperationDepthFail = function (): number {\r\n    return this._stencilState.stencilBackOpDepthFail;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilOperationPass = function (): number {\r\n    return this._stencilState.stencilOpStencilDepthPass;\r\n};\r\n\r\nAbstractEngine.prototype.getStencilBackOperationPass = function (): number {\r\n    return this._stencilState.stencilBackOpStencilDepthPass;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilOperationFail = function (operation: number): void {\r\n    this._stencilState.stencilOpStencilFail = operation;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilBackOperationFail = function (operation: number): void {\r\n    this._stencilState.stencilBackOpStencilFail = operation;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilOperationDepthFail = function (operation: number): void {\r\n    this._stencilState.stencilOpDepthFail = operation;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilBackOperationDepthFail = function (operation: number): void {\r\n    this._stencilState.stencilBackOpDepthFail = operation;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilOperationPass = function (operation: number): void {\r\n    this._stencilState.stencilOpStencilDepthPass = operation;\r\n};\r\n\r\nAbstractEngine.prototype.setStencilBackOperationPass = function (operation: number): void {\r\n    this._stencilState.stencilBackOpStencilDepthPass = operation;\r\n};\r\n\r\nAbstractEngine.prototype.cacheStencilState = function (): void {\r\n    this._cachedStencilBuffer = this.getStencilBuffer();\r\n    this._cachedStencilFunction = this.getStencilFunction();\r\n    this._cachedStencilMask = this.getStencilMask();\r\n    this._cachedStencilOperationPass = this.getStencilOperationPass();\r\n    this._cachedStencilOperationFail = this.getStencilOperationFail();\r\n    this._cachedStencilOperationDepthFail = this.getStencilOperationDepthFail();\r\n    this._cachedStencilReference = this.getStencilFunctionReference();\r\n};\r\n\r\nAbstractEngine.prototype.restoreStencilState = function (): void {\r\n    this.setStencilFunction(this._cachedStencilFunction);\r\n    this.setStencilMask(this._cachedStencilMask);\r\n    this.setStencilBuffer(this._cachedStencilBuffer);\r\n    this.setStencilOperationPass(this._cachedStencilOperationPass);\r\n    this.setStencilOperationFail(this._cachedStencilOperationFail);\r\n    this.setStencilOperationDepthFail(this._cachedStencilOperationDepthFail);\r\n    this.setStencilFunctionReference(this._cachedStencilReference);\r\n};\r\n"]}