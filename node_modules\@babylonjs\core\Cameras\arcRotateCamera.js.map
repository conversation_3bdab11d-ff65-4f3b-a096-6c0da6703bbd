{"version": 3, "file": "arcRotateCamera.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Cameras/arcRotateCamera.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AACjH,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACxF,OAAO,EAAE,KAAK,EAAE,MAAM,gCAAgC,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC;AACjF,OAAO,EAAE,gBAAgB,EAAE,MAAM,uCAAuC,CAAC;AACzE,OAAO,EAAE,eAAe,EAAE,MAAM,sCAAsC,CAAC;AACvE,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAI9C,OAAO,EAAE,4BAA4B,EAAE,MAAM,yCAAyC,CAAC;AACvF,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAKlD,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACvD,OAAO,GAAG,EAAE,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC7E,CAAC,CAAC,CAAC;AAEH;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,MAAe;IACxC,wGAAwG;IACxG,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IACxB,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QACtC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3F,CAAC;IAED,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;QACf,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;IAChC,CAAC;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CAAC,cAAsB,EAAE,MAAc;IAC9D,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED,sEAAsE;AACtE,SAAS,QAAQ,CAAC,KAAa,EAAE,QAAgB;IAC7C,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;AAC3C,CAAC;AAED;;;;;;GAMG;AACH,MAAM,OAAO,eAAgB,SAAQ,YAAY;IA+B7C;;;OAGG;IACH,IAAoB,MAAM;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IACD,IAAoB,MAAM,CAAC,KAAc;QACrC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD,IAAW,UAAU,CAAC,KAA8B;QAChD,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;IAED;;;OAGG;IACa,SAAS;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAoB,QAAQ;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAoB,QAAQ,CAAC,WAAoB;QAC7C,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAClC,CAAC;IAMD;;;;OAIG;IACH,IAAa,QAAQ,CAAC,GAAY;QAC9B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;YAEjC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACpC,CAAC;QAED,GAAG,CAAC,SAAS,EAAE,CAAC;QAChB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;IACpB,CAAC;IAED,IAAa,QAAQ;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,kDAAkD;QAClD,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAEjF,0DAA0D;QAC1D,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACrF,CAAC;IAiHD,2DAA2D;IAE3D;;OAEG;IACH,IAAW,mBAAmB;QAC1B,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,mBAAmB,CAAC;QACxC,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAa;QACxC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACzC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,mBAAmB;QAC1B,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,mBAAmB,CAAC;QACxC,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAa;QACxC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACzC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,cAAc,CAAC;QACnC,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,cAAc,CAAC,KAAa;QACnC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB;QAC3B,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,oBAAoB,CAAC;QACzC,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAa;QACzC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,IAAW,mBAAmB;QAC1B,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,mBAAmB,CAAC;QACxC,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAc;QACzC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACzC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,kBAAkB,CAAC;QACvC,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAa;QACvC,MAAM,QAAQ,GAAiC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACxC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,MAAM,CAAC;QAC3B,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,MAAM,CAAC,KAAe;QAC7B,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;QAC5B,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,QAAQ,CAAC;QAC7B,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,QAAQ,CAAC,KAAe;QAC/B,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC9B,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,QAAQ,CAAC;QAC7B,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,QAAQ,CAAC,KAAe;QAC/B,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC9B,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO,QAAQ,CAAC,SAAS,CAAC;QAC9B,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED,IAAW,SAAS,CAAC,KAAe;QAChC,MAAM,QAAQ,GAAqC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpF,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;QAC/B,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE,CAAC;YACb,OAAO,UAAU,CAAC,cAAc,CAAC;QACrC,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,cAAc,CAAC,KAAa;QACnC,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC;QACtC,CAAC;IACL,CAAC;IAED;;;OAGG;IAEH,IAAW,mBAAmB;QAC1B,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE,CAAC;YACb,OAAO,UAAU,CAAC,mBAAmB,CAAC;QAC1C,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAc;QACzC,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,mBAAmB,GAAG,KAAK,CAAC;QAC3C,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,IAAW,oBAAoB;QAC3B,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE,CAAC;YACb,OAAO,UAAU,CAAC,oBAAoB,CAAC;QAC3C,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAW,oBAAoB,CAAC,KAAa;QACzC,MAAM,UAAU,GAAmC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACtF,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAC5C,CAAC;IACL,CAAC;IAqED;;OAEG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC;IAC1C,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAc;QACzC,IAAI,KAAK,KAAK,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,iBAAiB,GAAG,IAAI,gBAAgB,EAAE,CAAC;YAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7C,CAAC;aAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAChC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC5C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAClC,CAAC;IACL,CAAC;IAID;;;OAGG;IACH,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC;IACzC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAc;QACxC,IAAI,KAAK,KAAK,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,gBAAgB,GAAG,IAAI,eAAe,EAAE,CAAC;YAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC/B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC3C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;IACL,CAAC;IAID;;;OAGG;IACH,IAAW,oBAAoB;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;;OAGG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC;IAC9C,CAAC;IAED,IAAW,uBAAuB,CAAC,KAAc;QAC7C,IAAI,KAAK,KAAK,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACzC,OAAO;QACX,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,qBAAqB,GAAG,IAAI,oBAAoB,EAAE,CAAC;YACxD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAChD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QACtC,CAAC;IACL,CAAC;IAuCD;;;;;;;;;OASG;IACH,YAAY,IAAY,EAAE,KAAa,EAAE,IAAY,EAAE,MAAc,EAAE,MAAe,EAAE,KAAa,EAAE,4BAA4B,GAAG,IAAI;QACtI,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,4BAA4B,CAAC,CAAC;QA1kBrE;;;WAGG;QAEI,wBAAmB,GAAG,CAAC,CAAC;QAE/B;;;WAGG;QAEI,uBAAkB,GAAG,CAAC,CAAC;QAE9B;;;WAGG;QAEI,yBAAoB,GAAG,CAAC,CAAC;QAEhC;;;WAGG;QAEI,oBAAe,GAAqB,IAAI,CAAC;QAEhD;;;WAGG;QAEI,oBAAe,GAAqB,IAAI,CAAC;QAEhD;;;WAGG;QAEI,mBAAc,GAAqB,IAAI,CAAC;QAE/C;;;WAGG;QAEI,mBAAc,GAAqB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;QAEzD;;;WAGG;QAEI,qBAAgB,GAAqB,IAAI,CAAC;QAEjD;;;WAGG;QAEI,qBAAgB,GAAqB,IAAI,CAAC;QAEjD;;;WAGG;QAGI,sBAAiB,GAAW,CAAC,QAAQ,CAAC;QAE7C;;WAEG;QAEI,qBAAgB,GAAW,CAAC,CAAC;QAEpC;;WAEG;QAEI,qBAAgB,GAAW,CAAC,CAAC;QAEpC;;;;WAIG;QAEI,0BAAqB,GAAW,EAAE,CAAC;QAE1C;;;WAGG;QAEI,yBAAoB,GAAqB,IAAI,CAAC;QAErD;;WAEG;QAEI,wBAAmB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAErD;;;WAGG;QAEI,mBAAc,GAAG,GAAG,CAAC;QAqQ5B,yDAAyD;QAEzD;;WAEG;QAEI,iBAAY,GAAG,CAAC,CAAC;QAExB;;WAEG;QAEI,uBAAkB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE3C;;;WAGG;QAEI,oBAAe,GAAG,IAAI,CAAC;QAE9B;;WAEG;QAEI,2BAAsB,GAAG,IAAI,CAAC;QAErC;;WAEG;QAEI,oCAA+B,GAAG,CAAC,CAAC;QAEnC,gCAA2B,GAAG,CAAC,CAAC;QAExC,gBAAgB;QACA,gBAAW,GAAG,IAAI,MAAM,EAAE,CAAC;QAc3C;;WAEG;QACI,gBAAW,GAAY,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,0BAAqB,GAAY,IAAI,OAAO,EAAE,CAAC;QAEzD;;WAEG;QACI,eAAU,GAAY,KAAK,CAAC;QAKnC,4IAA4I;QACpI,qBAAgB,GAAG,KAAK,CAAC;QAuGjC;;WAEG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAA2B,CAAC;QAOjF;;;WAGG;QACI,oBAAe,GAAG,KAAK,CAAC;QAE/B;;;;WAIG;QACI,oBAAe,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAG1C,sBAAiB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACnC,uBAAkB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACpC,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAShC,uBAAkB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QAgF7C,eAAU,GAAG,GAAG,CAAC;QACjB,cAAS,GAAG,GAAG,CAAC;QAChB,gBAAW,GAAG,GAAG,CAAC;QACT,gBAAW,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACzC,4BAAuB,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAijBvD,+BAA0B,GAAG,CAAC,WAAmB,EAAE,WAAoB,EAAE,eAAuC,IAAI,EAAE,EAAE;YAC9H,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpD,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAE9B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;oBACjB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;gBACjC,CAAC;YACL,CAAC;YAED,mCAAmC;YACnC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;gBACb,IAAI,GAAG,MAAM,CAAC;YAClB,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACzC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;YACjH,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE3C,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YACvB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBACxC,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;gBAChB,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;YACrB,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YACpD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAE3D,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACrC,CAAC,CAAC;QA3pBE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,IAAI,4BAA4B,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,aAAa,EAAE,CAAC,WAAW,EAAE,CAAC;IAC5D,CAAC;IAED,QAAQ;IACR,gBAAgB;IACA,UAAU;QACtB,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACxF,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACa,YAAY,CAAC,iBAA2B;QACpD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,KAAK,CAAC,YAAY,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACrE,CAAC;IAES,kBAAkB;QACxB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAC3D,MAAM,GAAG,GAAY,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAC5D,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3D,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE7D,IAAI,oBAAoB,EAAE,CAAC;YACvB,OAAO,oBAAoB,CAAC;QAChC,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAcD;;;OAGG;IACa,UAAU;QACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,CAAC;QACvD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEjE,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACa,mBAAmB;QAC/B,IAAI,IAAI,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,+BAA+B,GAAG,OAAO,IAAI,IAAI,CAAC,+BAA+B,GAAG,CAAC,EAAE,CAAC;YACtH,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,+BAA+B,CAAC,CAAC;YACtK,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;QAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QAEjE,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;;;;OAUG;IACI,aAAa,CAChB,KAAK,GAAG,IAAI,CAAC,KAAK,EAClB,IAAI,GAAG,IAAI,CAAC,IAAI,EAChB,MAAM,GAAG,IAAI,CAAC,MAAM,EACpB,MAAM,GAAG,IAAI,CAAC,MAAM,EACpB,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,EAC5C,mBAA4B;QAE5B,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,IAAI,mBAAmB,IAAI,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,2BAA2B,GAAG,mBAAmB,CAAC;QAC3D,CAAC;aAAM,IAAI,IAAI,CAAC,+BAA+B,KAAK,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC,+BAA+B,CAAC;QAC5E,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,2BAA2B,GAAG,GAAG,CAAC;QAC3C,CAAC;QAED,qEAAqE;QACrE,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACnD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7I,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjK,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,IAAI,QAAQ,CAAC,CAAC;QAC9G,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,IAAI,QAAQ,CAAC,CAAC;QAC1G,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,IAAI,QAAQ,CAAC,CAAC;QAClH,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE9F,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACjC,CAAC;IAED,eAAe;IACf,gBAAgB;IACA,yBAAyB;QACrC,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,CACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACrD,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK;YAChC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;YAC9B,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM;YAClC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CACjE,CAAC;IACN,CAAC;IAiCD;;;;;;OAMG;IACa,aAAa,CAAC,OAAY,EAAE,gBAA0B,EAAE,oBAAsC,IAAI,EAAE,qBAA6B,CAAC;QAC9I,MAAM,IAAI,GAAG,SAAS,CAAC;QAEvB,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,kBAAkB,GAAG,iBAA4B,CAAC;QACvD,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,0BAA0B;QAC1B,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YAC/B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAE5C,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;YACf,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;YAC5B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAC1B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAC9B,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACa,aAAa;QACzB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;QAE5B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC;IACL,CAAC;IAED,gBAAgB;IACA,YAAY;QACxB,8HAA8H;QAC9H,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE1B,IAAI,mBAAmB,GAAG,KAAK,CAAC;QAEhC,UAAU;QACV,IAAI,IAAI,CAAC,mBAAmB,KAAK,CAAC,IAAI,IAAI,CAAC,kBAAkB,KAAK,CAAC,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,EAAE,CAAC;YACrG,mBAAmB,GAAG,IAAI,CAAC;YAE3B,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,MAAM,oBAAoB,GAAG,IAAI,CAAC,8BAA8B,EAAE,CAAC;YACnE,IAAI,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,GAAG,oBAAoB,CAAC;YAE1E,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAChB,mBAAmB,IAAI,CAAC,CAAC,CAAC;YAC9B,CAAC;YAED,IAAI,CAAC,KAAK,IAAI,mBAAmB,GAAG,iBAAiB,CAAC;YACtD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;YAEzD,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,CAAC;YACzC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,CAAC;YACzC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,OAAO,CAAC;YACxC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,OAAO,CAAC;YAC1C,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,OAAO,EAAE,CAAC;gBAC/C,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;YACjC,CAAC;YACD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,OAAO,EAAE,CAAC;gBAC9C,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;YAChC,CAAC;YACD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;gBAC7D,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;YAClC,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC7D,mBAAmB,GAAG,IAAI,CAAC;YAE3B,MAAM,cAAc,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAExG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1D,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACjD,OAAO,CAAC,oBAAoB,CAAC,cAAc,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAEtG,0EAA0E;YAC1E,iDAAiD;YACjD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;gBACzB,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAC7F,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC9D,CAAC;iBAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACpB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC5B,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACpD,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;oBACtG,IAAI,eAAe,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC3E,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBACtD,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;wBACd,MAAM,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBAC/B,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBACvD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;wBACpB,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBACjG,CAAC;oBACD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACxD,CAAC;YACL,CAAC;YAED,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,cAAc,CAAC;YAC7C,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,cAAc,CAAC;YAE7C,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;gBACzD,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAC9B,CAAC;YACD,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;gBACzD,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,IAAI,mBAAmB,EAAE,CAAC;YACtB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC/B,IAAI,eAAe,GAAG,KAAK,CAAC;YAC5B,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC;YACzD,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAElE,4IAA4I;YAE5I,6GAA6G;YAC7G,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAE3D,6DAA6D;YAC7D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzF,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CACxC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAC5C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAC5C,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAC/C,CAAC;gBACF,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAE5D,uFAAuF;gBACvF,mGAAmG;gBACnG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,GAAG,OAAO,EAAE,CAAC;oBAC1E,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;oBACpC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;oBACjC,6EAA6E;oBAC7E,oIAAoI;oBACpI,yHAAyH;oBACzH,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnD,CAAC;qBAAM,CAAC;oBACJ,eAAe,GAAG,IAAI,CAAC;gBAC3B,CAAC;YACL,CAAC;YAED,+DAA+D;YAC/D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;gBACpD,2EAA2E;gBAC3E,MAAM,YAAY,GAAG,UAAU,CAAC,2BAA2B,CACvD,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,EACrC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,EACnC,CAAC,EACD,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAC3B,CAAC;gBACF,MAAM,eAAe,GAAG,UAAU,CAAC,2BAA2B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnH,MAAM,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC,eAAe,EAAE,YAAY,EAAE,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtG,WAAW,CAAC,SAAS,EAAE,CAAC;gBACxB,MAAM,iBAAiB,GAAG,WAAW,CAAC,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnF,IAAI,CAAC,KAAK,GAAG,iBAAiB,CAAC,CAAC,CAAC;gBACjC,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC;gBAEhC,4GAA4G;gBAC5G,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC;oBAClD,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;oBACtB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;oBACrB,MAAM,kBAAkB,GAAG,YAAY,CAAC,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrF,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,CAAC,CAAC;oBAClC,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC,CAAC,CAAC;gBACrC,CAAC;qBAAM,CAAC;oBACJ,eAAe,GAAG,IAAI,CAAC;gBAC3B,CAAC;YACL,CAAC;YAED,6DAA6D;YAC7D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAE9C,qJAAqJ;gBACrJ,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC;oBACnD,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;oBACvB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACJ,eAAe,GAAG,IAAI,CAAC;gBAC3B,CAAC;YACL,CAAC;YAED,2EAA2E;YAC3E,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC;gBACnF,MAAM,sBAAsB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CACpD,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,EACnE,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CACtE,CAAC;gBACF,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,sBAAsB,EAAE,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAE/F,gJAAgJ;gBAChJ,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,GAAG,OAAO,EAAE,CAAC;oBAC9E,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;oBAC3C,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;gBAC7D,CAAC;qBAAM,CAAC;oBACJ,eAAe,GAAG,IAAI,CAAC;gBAC3B,CAAC;YACL,CAAC;YAED,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QAC5C,CAAC;QAED,SAAS;QACT,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,KAAK,CAAC,YAAY,EAAE,CAAC;IACzB,CAAC;IAES,YAAY;QAClB,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACpE,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC9C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;YACxC,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;YACpC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACpE,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBAC/C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;YACxC,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC;YACpC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;QACtC,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACrE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC;QACtC,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;YACpC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAClC,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC;YACpC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACpE,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEjF,4DAA4D;QAC5D,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/E,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3G,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;QAE/C,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,iCAAiC;QAC3D,CAAC;QAED,iBAAiB;QACjB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnD,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhE,4EAA4E;QAC5E,MAAM,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACxF,yFAAyF;QACzF,IAAI,CAAC,KAAK,IAAI,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAEnD,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,QAAiB;QAChC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAElC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;;;;;;;OAQG;IACa,SAAS,CAAC,MAA+B,EAAE,gBAAgB,GAAG,KAAK,EAAE,iBAAiB,GAAG,KAAK,EAAE,oBAAoB,GAAG,KAAK;QACxI,oBAAoB,GAAG,IAAI,CAAC,4BAA4B,IAAI,oBAAoB,CAAC;QAEjF,IAAK,MAAwB,CAAC,kBAAkB,EAAE,CAAC;YAC/C,IAAI,gBAAgB,IAAU,MAAO,CAAC,eAAe,EAAE,CAAC;gBACpD,IAAI,CAAC,qBAAqB,GAAS,MAAO,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACjG,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YACtC,CAAC;YACe,MAAO,CAAC,kBAAkB,EAAE,CAAC;YAC7C,IAAI,CAAC,WAAW,GAAkB,MAAM,CAAC;YACzC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAEzC,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACzE,CAAC;aAAM,CAAC;YACJ,MAAM,SAAS,GAAY,MAAM,CAAC;YAClC,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAChD,IAAI,aAAa,IAAI,CAAC,iBAAiB,IAAI,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBACzE,OAAO;YACX,CAAC;YACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;YACzB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YAClC,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAClC,CAAC;IACL,CAAC;IAED,gBAAgB;IACA,cAAc;QAC1B,UAAU;QACV,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACb,IAAI,GAAG,MAAM,CAAC;QAClB,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,iCAAiC;QAC3D,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACzC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QAEjH,gCAAgC;QAChC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/E,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3G,CAAC;QAED,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,iBAAiB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5D,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC;YACzD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAClB,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;YAClD,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAChC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjJ,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE3C,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YACvB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;gBACnC,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;YACrB,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YAEpD,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAyCD;;;;OAIG;IACI,MAAM,CAAC,MAAuB,EAAE,eAAe,GAAG,KAAK;QAC1D,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;QAE1C,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,QAAQ,GAAG,IAAI,CAAC,4CAA4C,CAAC,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC;QAErG,iEAAiE;QACjE,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC;QAC/G,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;QAE3C,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,mBAAmB,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACrE,MAAM,WAAW,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACvD,IAAI,CAAC,SAAS,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;YAC5C,IAAI,CAAC,UAAU,GAAG,WAAW,GAAG,WAAW,CAAC;YAC5C,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,CAAC;YAChC,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,eAAe,CAAC,CAAC;IACxG,CAAC;IAED;;;;;OAKG;IACI,OAAO,CAAC,+BAAkG,EAAE,eAAe,GAAG,KAAK;QACtI,IAAI,oBAAoD,CAAC;QACzD,IAAI,QAAgB,CAAC;QAErB,IAAU,+BAAgC,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;YAC3D,SAAS;YACT,MAAM,MAAM,GAAmB,+BAA+B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;YACzF,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC3C,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,GAAG,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACpF,CAAC;aAAM,CAAC;YACJ,2BAA2B;YAC3B,MAAM,uBAAuB,GAAQ,+BAA+B,CAAC;YACrE,oBAAoB,GAAG,uBAAuB,CAAC;YAC/C,QAAQ,GAAG,uBAAuB,CAAC,QAAQ,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAEjD,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC7B,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACa,eAAe,CAAC,IAAY,EAAE,WAAmB;QAC7D,IAAI,UAAU,GAAW,CAAC,CAAC;QAC3B,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,KAAK,MAAM,CAAC,8BAA8B,CAAC;YAC3C,KAAK,MAAM,CAAC,yCAAyC,CAAC;YACtD,KAAK,MAAM,CAAC,+BAA+B,CAAC;YAC5C,KAAK,MAAM,CAAC,gCAAgC,CAAC;YAC7C,KAAK,MAAM,CAAC,WAAW;gBACnB,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClF,MAAM;YACV,KAAK,MAAM,CAAC,0CAA0C;gBAClD,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,CAAC,WAAW,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClF,MAAM;QACd,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,GAAG,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzH,MAAM,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7B,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEhC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACxB,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACtC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEhC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACa,iBAAiB;QAC7B,MAAM,OAAO,GAAoB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAoB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEtD,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAEzC,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,KAAK,MAAM,CAAC,8BAA8B,CAAC;YAC3C,KAAK,MAAM,CAAC,yCAAyC,CAAC;YACtD,KAAK,MAAM,CAAC,+BAA+B,CAAC;YAC5C,KAAK,MAAM,CAAC,gCAAgC,CAAC;YAC7C,KAAK,MAAM,CAAC,WAAW;gBACnB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBACnE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBACpE,MAAM;YACV,KAAK,MAAM,CAAC,0CAA0C;gBAClD,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBACnE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBACpE,MAAM;QACd,CAAC;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,4CAA4C,CAAC,YAAqB,EAAE,YAAqB,EAAE,cAAsB,CAAC;QACrH,MAAM,uBAAuB,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QAE7E,uDAAuD;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;QAC3C,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAC7C,MAAM,aAAa,GAAG,aAAa,GAAG,WAAW,CAAC;QAElD,+BAA+B;QAC/B,6FAA6F;QAC7F,MAAM,oBAAoB,GAAG,uBAAuB,GAAG,GAAG,CAAC;QAE3D,mBAAmB;QACnB,MAAM,MAAM,GAAG,oBAAoB,GAAG,WAAW,CAAC;QAClD,MAAM,4BAA4B,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC;QACrG,MAAM,0BAA0B,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC;QACnG,OAAO,IAAI,CAAC,GAAG,CAAC,4BAA4B,EAAE,0BAA0B,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACa,OAAO;QACnB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACpB,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;;OAGG;IACa,YAAY;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;CACJ;AAj/CU;IADN,SAAS,EAAE;8CACS;AAMd;IADN,SAAS,EAAE;6CACQ;AAMb;IADN,SAAS,EAAE;+CACU;AAOf;IADN,SAAS,EAAE;qEAC2C;AAG7C;IADT,kBAAkB,CAAC,QAAQ,CAAC;gDACF;AAEjB;IADT,wBAAwB,CAAC,YAAY,CAAC;oDACQ;AAwFxC;IADN,SAAS,EAAE;4DACmB;AAOxB;IADN,SAAS,EAAE;2DACkB;AAOvB;IADN,SAAS,EAAE;6DACoB;AAOzB;IADN,SAAS,EAAE;wDACoC;AAOzC;IADN,SAAS,EAAE;wDACoC;AAOzC;IADN,SAAS,EAAE;uDACmC;AAOxC;IADN,SAAS,EAAE;uDAC6C;AAOlD;IADN,SAAS,EAAE;yDACqC;AAO1C;IADN,SAAS,EAAE;yDACqC;AAQ1C;IAFN,SAAS,EAAE;IACZ,gEAAgE;0DACnB;AAMtC;IADN,SAAS,EAAE;yDACwB;AAM7B;IADN,SAAS,EAAE;yDACwB;AAQ7B;IADN,SAAS,EAAE;8DAC8B;AAOnC;IADN,SAAS,EAAE;6DACyC;AAM9C;IADN,kBAAkB,EAAE;4DACgC;AAO9C;IADN,SAAS,EAAE;uDACgB;AAgO5B;IADC,SAAS,EAAE;0DAQX;AAoCM;IADN,SAAS,EAAE;qDACY;AAMjB;IADN,kBAAkB,EAAE;2DACsB;AAOpC;IADN,SAAS,EAAE;wDACkB;AAMvB;IADN,SAAS,EAAE;+DACyB;AAM9B;IADN,SAAS,EAAE;wEAC+B;AAu/B/C,sBAAsB;AACtB,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "sourcesContent": ["import { serialize, serializeAsVector3, serializeAsMeshReference, serializeAsVector2 } from \"../Misc/decorators\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, Vector3, Vector2, TmpVectors, Quaternion } from \"../Maths/math.vector\";\r\nimport { Clamp } from \"../Maths/math.scalar.functions\";\r\nimport { Node } from \"../node\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { AutoRotationBehavior } from \"../Behaviors/Cameras/autoRotationBehavior\";\r\nimport { BouncingBehavior } from \"../Behaviors/Cameras/bouncingBehavior\";\r\nimport { FramingBehavior } from \"../Behaviors/Cameras/framingBehavior\";\r\nimport { Camera } from \"./camera\";\r\nimport { TargetCamera } from \"./targetCamera\";\r\nimport type { ArcRotateCameraPointersInput } from \"../Cameras/Inputs/arcRotateCameraPointersInput\";\r\nimport type { ArcRotateCameraKeyboardMoveInput } from \"../Cameras/Inputs/arcRotateCameraKeyboardMoveInput\";\r\nimport type { ArcRotateCameraMouseWheelInput } from \"../Cameras/Inputs/arcRotateCameraMouseWheelInput\";\r\nimport { ArcRotateCameraInputsManager } from \"../Cameras/arcRotateCameraInputsManager\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\nimport type { Collider } from \"../Collisions/collider\";\r\nimport type { TransformNode } from \"core/Meshes/transformNode\";\r\n\r\nNode.AddNodeConstructor(\"ArcRotateCamera\", (name, scene) => {\r\n    return () => new ArcRotateCamera(name, 0, 0, 1.0, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * Computes the alpha angle based on the source position and the target position.\r\n * @param offset The directional offset between the source position and the target position\r\n * @returns The alpha angle in radians\r\n */\r\nexport function ComputeAlpha(offset: Vector3): number {\r\n    // Default alpha to π/2 to handle the edge case where x and z are both zero (when looking along up axis)\r\n    let alpha = Math.PI / 2;\r\n    if (!(offset.x === 0 && offset.z === 0)) {\r\n        alpha = Math.acos(offset.x / Math.sqrt(Math.pow(offset.x, 2) + Math.pow(offset.z, 2)));\r\n    }\r\n\r\n    if (offset.z < 0) {\r\n        alpha = 2 * Math.PI - alpha;\r\n    }\r\n\r\n    return alpha;\r\n}\r\n\r\n/**\r\n * Computes the beta angle based on the source position and the target position.\r\n * @param verticalOffset The y value of the directional offset between the source position and the target position\r\n * @param radius The distance between the source position and the target position\r\n * @returns The beta angle in radians\r\n */\r\nexport function ComputeBeta(verticalOffset: number, radius: number): number {\r\n    return Math.acos(verticalOffset / radius);\r\n}\r\n\r\n// Returns the value if not NaN, otherwise returns the fallback value.\r\nfunction CheckNaN(value: number, fallback: number): number {\r\n    return isNaN(value) ? fallback : value;\r\n}\r\n\r\n/**\r\n * This represents an orbital type of camera.\r\n *\r\n * This camera always points towards a given target position and can be rotated around that target with the target as the centre of rotation. It can be controlled with cursors and mouse, or with touch events.\r\n * Think of this camera as one orbiting its target position, or more imaginatively as a spy satellite orbiting the earth. Its position relative to the target (earth) can be set by three parameters, alpha (radians) the longitudinal rotation, beta (radians) the latitudinal rotation and radius the distance from the target position.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#arc-rotate-camera\r\n */\r\nexport class ArcRotateCamera extends TargetCamera {\r\n    /**\r\n     * Defines the rotation angle of the camera along the longitudinal axis.\r\n     */\r\n    @serialize()\r\n    public alpha: number;\r\n\r\n    /**\r\n     * Defines the rotation angle of the camera along the latitudinal axis.\r\n     */\r\n    @serialize()\r\n    public beta: number;\r\n\r\n    /**\r\n     * Defines the radius of the camera from its target point.\r\n     */\r\n    @serialize()\r\n    public radius: number;\r\n\r\n    /**\r\n     * Defines an override value to use as the parameter to setTarget.\r\n     * This allows the parameter to be specified when animating the target (e.g. using FramingBehavior).\r\n     */\r\n    @serialize()\r\n    public overrideCloneAlphaBetaRadius: Nullable<boolean>;\r\n\r\n    @serializeAsVector3(\"target\")\r\n    protected _target: Vector3;\r\n    @serializeAsMeshReference(\"targetHost\")\r\n    protected _targetHost: Nullable<TransformNode>;\r\n\r\n    /**\r\n     * Defines the target point of the camera.\r\n     * The camera looks towards it from the radius distance.\r\n     */\r\n    public override get target(): Vector3 {\r\n        return this._target;\r\n    }\r\n    public override set target(value: Vector3) {\r\n        this.setTarget(value);\r\n    }\r\n\r\n    /**\r\n     * Defines the target transform node of the camera.\r\n     * The camera looks towards it from the radius distance.\r\n     * Please note that setting a target host will disable panning.\r\n     */\r\n    public get targetHost(): Nullable<TransformNode> {\r\n        return this._targetHost;\r\n    }\r\n    public set targetHost(value: Nullable<TransformNode>) {\r\n        if (value) {\r\n            this.setTarget(value);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Return the current target position of the camera. This value is expressed in local space.\r\n     * @returns the target position\r\n     */\r\n    public override getTarget(): Vector3 {\r\n        return this.target;\r\n    }\r\n\r\n    /**\r\n     * Define the current local position of the camera in the scene\r\n     */\r\n    public override get position(): Vector3 {\r\n        return this._position;\r\n    }\r\n\r\n    public override set position(newPosition: Vector3) {\r\n        this.setPosition(newPosition);\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected _upToYMatrix: Matrix;\r\n    protected _yToUpMatrix: Matrix;\r\n\r\n    /**\r\n     * The vector the camera should consider as up. (default is Vector3(0, 1, 0) as returned by Vector3.Up())\r\n     * Setting this will copy the given vector to the camera's upVector, and set rotation matrices to and from Y up.\r\n     * DO NOT set the up vector using copyFrom or copyFromFloats, as this bypasses setting the above matrices.\r\n     */\r\n    override set upVector(vec: Vector3) {\r\n        if (!this._upToYMatrix) {\r\n            this._yToUpMatrix = new Matrix();\r\n            this._upToYMatrix = new Matrix();\r\n\r\n            this._upVector = Vector3.Zero();\r\n        }\r\n\r\n        vec.normalize();\r\n        this._upVector.copyFrom(vec);\r\n        this.setMatUp();\r\n    }\r\n\r\n    override get upVector() {\r\n        return this._upVector;\r\n    }\r\n\r\n    /**\r\n     * Sets the Y-up to camera up-vector rotation matrix, and the up-vector to Y-up rotation matrix.\r\n     */\r\n    public setMatUp() {\r\n        // from y-up to custom-up (used in _getViewMatrix)\r\n        Matrix.RotationAlignToRef(Vector3.UpReadOnly, this._upVector, this._yToUpMatrix);\r\n\r\n        // from custom-up to y-up (used in rebuildAnglesAndRadius)\r\n        Matrix.RotationAlignToRef(this._upVector, Vector3.UpReadOnly, this._upToYMatrix);\r\n    }\r\n\r\n    /**\r\n     * Current inertia value on the longitudinal axis.\r\n     * The bigger this number the longer it will take for the camera to stop.\r\n     */\r\n    @serialize()\r\n    public inertialAlphaOffset = 0;\r\n\r\n    /**\r\n     * Current inertia value on the latitudinal axis.\r\n     * The bigger this number the longer it will take for the camera to stop.\r\n     */\r\n    @serialize()\r\n    public inertialBetaOffset = 0;\r\n\r\n    /**\r\n     * Current inertia value on the radius axis.\r\n     * The bigger this number the longer it will take for the camera to stop.\r\n     */\r\n    @serialize()\r\n    public inertialRadiusOffset = 0;\r\n\r\n    /**\r\n     * Minimum allowed angle on the longitudinal axis.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public lowerAlphaLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Maximum allowed angle on the longitudinal axis.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public upperAlphaLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Minimum allowed angle on the latitudinal axis.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public lowerBetaLimit: Nullable<number> = 0.01;\r\n\r\n    /**\r\n     * Maximum allowed angle on the latitudinal axis.\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public upperBetaLimit: Nullable<number> = Math.PI - 0.01;\r\n\r\n    /**\r\n     * Minimum allowed distance of the camera to the target (The camera can not get closer).\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public lowerRadiusLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Maximum allowed distance of the camera to the target (The camera can not get further).\r\n     * This can help limiting how the Camera is able to move in the scene.\r\n     */\r\n    @serialize()\r\n    public upperRadiusLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Minimum allowed vertical target position of the camera.\r\n     * Use this setting in combination with `upperRadiusLimit` to set a global limit for the Cameras vertical position.\r\n     */\r\n    @serialize()\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public lowerTargetYLimit: number = -Infinity;\r\n\r\n    /**\r\n     * Defines the current inertia value used during panning of the camera along the X axis.\r\n     */\r\n    @serialize()\r\n    public inertialPanningX: number = 0;\r\n\r\n    /**\r\n     * Defines the current inertia value used during panning of the camera along the Y axis.\r\n     */\r\n    @serialize()\r\n    public inertialPanningY: number = 0;\r\n\r\n    /**\r\n     * Defines the distance used to consider the camera in pan mode vs pinch/zoom.\r\n     * Basically if your fingers moves away from more than this distance you will be considered\r\n     * in pinch mode.\r\n     */\r\n    @serialize()\r\n    public pinchToPanMaxDistance: number = 20;\r\n\r\n    /**\r\n     * Defines the maximum distance the camera can pan.\r\n     * This could help keeping the camera always in your scene.\r\n     */\r\n    @serialize()\r\n    public panningDistanceLimit: Nullable<number> = null;\r\n\r\n    /**\r\n     * Defines the target of the camera before panning.\r\n     */\r\n    @serializeAsVector3()\r\n    public panningOriginTarget: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * Defines the value of the inertia used during panning.\r\n     * 0 would mean stop inertia and one would mean no deceleration at all.\r\n     */\r\n    @serialize()\r\n    public panningInertia = 0.9;\r\n\r\n    //-- begin properties for backward compatibility for inputs\r\n\r\n    /**\r\n     * Gets or Set the pointer angular sensibility  along the X axis or how fast is the camera rotating.\r\n     */\r\n    public get angularSensibilityX(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.angularSensibilityX;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set angularSensibilityX(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.angularSensibilityX = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer angular sensibility along the Y axis or how fast is the camera rotating.\r\n     */\r\n    public get angularSensibilityY(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.angularSensibilityY;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set angularSensibilityY(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.angularSensibilityY = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer pinch precision or how fast is the camera zooming.\r\n     */\r\n    public get pinchPrecision(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.pinchPrecision;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set pinchPrecision(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.pinchPrecision = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer pinch delta percentage or how fast is the camera zooming.\r\n     * It will be used instead of pinchPrecision if different from 0.\r\n     * It defines the percentage of current camera.radius to use as delta when pinch zoom is used.\r\n     */\r\n    public get pinchDeltaPercentage(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.pinchDeltaPercentage;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set pinchDeltaPercentage(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.pinchDeltaPercentage = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer use natural pinch zoom to override the pinch precision\r\n     * and pinch delta percentage.\r\n     * When useNaturalPinchZoom is true, multi touch zoom will zoom in such\r\n     * that any object in the plane at the camera's target point will scale\r\n     * perfectly with finger motion.\r\n     */\r\n    public get useNaturalPinchZoom(): boolean {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.useNaturalPinchZoom;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public set useNaturalPinchZoom(value: boolean) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.useNaturalPinchZoom = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the pointer panning sensibility or how fast is the camera moving.\r\n     */\r\n    public get panningSensibility(): number {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            return pointers.panningSensibility;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set panningSensibility(value: number) {\r\n        const pointers = <ArcRotateCameraPointersInput>this.inputs.attached[\"pointers\"];\r\n        if (pointers) {\r\n            pointers.panningSensibility = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control beta angle in a positive direction.\r\n     */\r\n    public get keysUp(): number[] {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysUp;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysUp(value: number[]) {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysUp = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control beta angle in a negative direction.\r\n     */\r\n    public get keysDown(): number[] {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysDown;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysDown(value: number[]) {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysDown = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control alpha angle in a negative direction.\r\n     */\r\n    public get keysLeft(): number[] {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysLeft;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysLeft(value: number[]) {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysLeft = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the list of keyboard keys used to control alpha angle in a positive direction.\r\n     */\r\n    public get keysRight(): number[] {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            return keyboard.keysRight;\r\n        }\r\n\r\n        return [];\r\n    }\r\n\r\n    public set keysRight(value: number[]) {\r\n        const keyboard = <ArcRotateCameraKeyboardMoveInput>this.inputs.attached[\"keyboard\"];\r\n        if (keyboard) {\r\n            keyboard.keysRight = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the mouse wheel precision or how fast is the camera zooming.\r\n     */\r\n    public get wheelPrecision(): number {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            return mousewheel.wheelPrecision;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set wheelPrecision(value: number) {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            mousewheel.wheelPrecision = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the boolean value that controls whether or not the mouse wheel\r\n     * zooms to the location of the mouse pointer or not.  The default is false.\r\n     */\r\n    @serialize()\r\n    public get zoomToMouseLocation(): boolean {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            return mousewheel.zoomToMouseLocation;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public set zoomToMouseLocation(value: boolean) {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            mousewheel.zoomToMouseLocation = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or Set the mouse wheel delta percentage or how fast is the camera zooming.\r\n     * It will be used instead of wheelPrecision if different from 0.\r\n     * It defines the percentage of current camera.radius to use as delta when wheel zoom is used.\r\n     */\r\n    public get wheelDeltaPercentage(): number {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            return mousewheel.wheelDeltaPercentage;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    public set wheelDeltaPercentage(value: number) {\r\n        const mousewheel = <ArcRotateCameraMouseWheelInput>this.inputs.attached[\"mousewheel\"];\r\n        if (mousewheel) {\r\n            mousewheel.wheelDeltaPercentage = value;\r\n        }\r\n    }\r\n\r\n    //-- end properties for backward compatibility for inputs\r\n\r\n    /**\r\n     * Defines how much the radius should be scaled while zooming on a particular mesh (through the zoomOn function)\r\n     */\r\n    @serialize()\r\n    public zoomOnFactor = 1;\r\n\r\n    /**\r\n     * Defines a screen offset for the camera position.\r\n     */\r\n    @serializeAsVector2()\r\n    public targetScreenOffset = Vector2.Zero();\r\n\r\n    /**\r\n     * Allows the camera to be completely reversed.\r\n     * If false the camera can not arrive upside down.\r\n     */\r\n    @serialize()\r\n    public allowUpsideDown = true;\r\n\r\n    /**\r\n     * Define if double tap/click is used to restore the previously saved state of the camera.\r\n     */\r\n    @serialize()\r\n    public useInputToRestoreState = true;\r\n\r\n    /**\r\n     * Factor for restoring information interpolation. default is 0 = off. Any value \\< 0 or \\> 1 will disable interpolation.\r\n     */\r\n    @serialize()\r\n    public restoreStateInterpolationFactor = 0;\r\n\r\n    private _currentInterpolationFactor = 0;\r\n\r\n    /** @internal */\r\n    public override _viewMatrix = new Matrix();\r\n    /** @internal */\r\n    public _useCtrlForPanning: boolean;\r\n    /** @internal */\r\n    public _panningMouseButton: number;\r\n\r\n    /**\r\n     * Defines the input associated to the camera.\r\n     */\r\n    public override inputs: ArcRotateCameraInputsManager;\r\n\r\n    /** @internal */\r\n    public override _reset: () => void;\r\n\r\n    /**\r\n     * Defines the allowed panning axis.\r\n     */\r\n    public panningAxis: Vector3 = new Vector3(1, 1, 0);\r\n    protected _transformedDirection: Vector3 = new Vector3();\r\n\r\n    /**\r\n     * Defines if camera will eliminate transform on y axis.\r\n     */\r\n    public mapPanning: boolean = false;\r\n\r\n    // Behaviors\r\n    private _bouncingBehavior: Nullable<BouncingBehavior>;\r\n\r\n    // This is redundant with all _goal* properties being NaN, but we track it anyway because we check for active interpolation in the hot path.\r\n    private _isInterpolating = false;\r\n\r\n    /**\r\n     * If true, indicates the camera is currently interpolating to a new pose.\r\n     */\r\n    public get isInterpolating(): boolean {\r\n        return this._isInterpolating;\r\n    }\r\n\r\n    /**\r\n     * Gets the bouncing behavior of the camera if it has been enabled.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#bouncing-behavior\r\n     */\r\n    public get bouncingBehavior(): Nullable<BouncingBehavior> {\r\n        return this._bouncingBehavior;\r\n    }\r\n\r\n    /**\r\n     * Defines if the bouncing behavior of the camera is enabled on the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#bouncing-behavior\r\n     */\r\n    public get useBouncingBehavior(): boolean {\r\n        return this._bouncingBehavior != null;\r\n    }\r\n\r\n    public set useBouncingBehavior(value: boolean) {\r\n        if (value === this.useBouncingBehavior) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this._bouncingBehavior = new BouncingBehavior();\r\n            this.addBehavior(this._bouncingBehavior);\r\n        } else if (this._bouncingBehavior) {\r\n            this.removeBehavior(this._bouncingBehavior);\r\n            this._bouncingBehavior = null;\r\n        }\r\n    }\r\n\r\n    private _framingBehavior: Nullable<FramingBehavior>;\r\n\r\n    /**\r\n     * Gets the framing behavior of the camera if it has been enabled.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#framing-behavior\r\n     */\r\n    public get framingBehavior(): Nullable<FramingBehavior> {\r\n        return this._framingBehavior;\r\n    }\r\n\r\n    /**\r\n     * Defines if the framing behavior of the camera is enabled on the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#framing-behavior\r\n     */\r\n    public get useFramingBehavior(): boolean {\r\n        return this._framingBehavior != null;\r\n    }\r\n\r\n    public set useFramingBehavior(value: boolean) {\r\n        if (value === this.useFramingBehavior) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this._framingBehavior = new FramingBehavior();\r\n            this.addBehavior(this._framingBehavior);\r\n        } else if (this._framingBehavior) {\r\n            this.removeBehavior(this._framingBehavior);\r\n            this._framingBehavior = null;\r\n        }\r\n    }\r\n\r\n    private _autoRotationBehavior: Nullable<AutoRotationBehavior>;\r\n\r\n    /**\r\n     * Gets the auto rotation behavior of the camera if it has been enabled.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#autorotation-behavior\r\n     */\r\n    public get autoRotationBehavior(): Nullable<AutoRotationBehavior> {\r\n        return this._autoRotationBehavior;\r\n    }\r\n\r\n    /**\r\n     * Defines if the auto rotation behavior of the camera is enabled on the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#autorotation-behavior\r\n     */\r\n    public get useAutoRotationBehavior(): boolean {\r\n        return this._autoRotationBehavior != null;\r\n    }\r\n\r\n    public set useAutoRotationBehavior(value: boolean) {\r\n        if (value === this.useAutoRotationBehavior) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this._autoRotationBehavior = new AutoRotationBehavior();\r\n            this.addBehavior(this._autoRotationBehavior);\r\n        } else if (this._autoRotationBehavior) {\r\n            this.removeBehavior(this._autoRotationBehavior);\r\n            this._autoRotationBehavior = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Observable triggered when the transform node target has been changed on the camera.\r\n     */\r\n    public onMeshTargetChangedObservable = new Observable<Nullable<TransformNode>>();\r\n\r\n    /**\r\n     * Event raised when the camera is colliding with a mesh.\r\n     */\r\n    public onCollide: (collidedMesh: AbstractMesh) => void;\r\n\r\n    /**\r\n     * Defines whether the camera should check collision with the objects oh the scene.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions#how-can-i-do-this-\r\n     */\r\n    public checkCollisions = false;\r\n\r\n    /**\r\n     * Defines the collision radius of the camera.\r\n     * This simulates a sphere around the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_collisions#arcrotatecamera\r\n     */\r\n    public collisionRadius = new Vector3(0.5, 0.5, 0.5);\r\n\r\n    protected _collider: Collider;\r\n    protected _previousPosition = Vector3.Zero();\r\n    protected _collisionVelocity = Vector3.Zero();\r\n    protected _newPosition = Vector3.Zero();\r\n    protected _previousAlpha: number;\r\n    protected _previousBeta: number;\r\n    protected _previousRadius: number;\r\n    //due to async collision inspection\r\n    protected _collisionTriggered: boolean;\r\n\r\n    protected _targetBoundingCenter: Nullable<Vector3>;\r\n\r\n    private _computationVector: Vector3 = Vector3.Zero();\r\n\r\n    /**\r\n     * Instantiates a new ArcRotateCamera in a given scene\r\n     * @param name Defines the name of the camera\r\n     * @param alpha Defines the camera rotation along the longitudinal axis\r\n     * @param beta Defines the camera rotation along the latitudinal axis\r\n     * @param radius Defines the camera distance from its target\r\n     * @param target Defines the camera target\r\n     * @param scene Defines the scene the camera belongs to\r\n     * @param setActiveOnSceneIfNoneActive Defines whether the camera should be marked as active if not other active cameras have been defined\r\n     */\r\n    constructor(name: string, alpha: number, beta: number, radius: number, target: Vector3, scene?: Scene, setActiveOnSceneIfNoneActive = true) {\r\n        super(name, Vector3.Zero(), scene, setActiveOnSceneIfNoneActive);\r\n\r\n        this._target = Vector3.Zero();\r\n        if (target) {\r\n            this.setTarget(target);\r\n        }\r\n\r\n        this.alpha = alpha;\r\n        this.beta = beta;\r\n        this.radius = radius;\r\n\r\n        this.getViewMatrix();\r\n        this.inputs = new ArcRotateCameraInputsManager(this);\r\n        this.inputs.addKeyboard().addMouseWheel().addPointers();\r\n    }\r\n\r\n    // Cache\r\n    /** @internal */\r\n    public override _initCache(): void {\r\n        super._initCache();\r\n        this._cache._target = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        this._cache.alpha = undefined;\r\n        this._cache.beta = undefined;\r\n        this._cache.radius = undefined;\r\n        this._cache.targetScreenOffset = Vector2.Zero();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override _updateCache(ignoreParentClass?: boolean): void {\r\n        if (!ignoreParentClass) {\r\n            super._updateCache();\r\n        }\r\n\r\n        this._cache._target.copyFrom(this._getTargetPosition());\r\n        this._cache.alpha = this.alpha;\r\n        this._cache.beta = this.beta;\r\n        this._cache.radius = this.radius;\r\n        this._cache.targetScreenOffset.copyFrom(this.targetScreenOffset);\r\n    }\r\n\r\n    protected _getTargetPosition(): Vector3 {\r\n        if (this._targetHost && this._targetHost.getAbsolutePosition) {\r\n            const pos: Vector3 = this._targetHost.getAbsolutePosition();\r\n            if (this._targetBoundingCenter) {\r\n                pos.addToRef(this._targetBoundingCenter, this._target);\r\n            } else {\r\n                this._target.copyFrom(pos);\r\n            }\r\n        }\r\n\r\n        const lockedTargetPosition = this._getLockedTargetPosition();\r\n\r\n        if (lockedTargetPosition) {\r\n            return lockedTargetPosition;\r\n        }\r\n\r\n        return this._target;\r\n    }\r\n\r\n    private _storedAlpha: number;\r\n    private _storedBeta: number;\r\n    private _storedRadius: number;\r\n    private _storedTarget: Vector3;\r\n    private _storedTargetScreenOffset: Vector2;\r\n\r\n    private _goalAlpha = NaN;\r\n    private _goalBeta = NaN;\r\n    private _goalRadius = NaN;\r\n    private readonly _goalTarget = new Vector3(NaN, NaN, NaN);\r\n    private readonly _goalTargetScreenOffset = new Vector2(NaN, NaN);\r\n\r\n    /**\r\n     * Stores the current state of the camera (alpha, beta, radius and target)\r\n     * @returns the camera itself\r\n     */\r\n    public override storeState(): Camera {\r\n        this._storedAlpha = this.alpha;\r\n        this._storedBeta = this.beta;\r\n        this._storedRadius = this.radius;\r\n        this._storedTarget = this._getTargetPosition().clone();\r\n        this._storedTargetScreenOffset = this.targetScreenOffset.clone();\r\n\r\n        return super.storeState();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Restored camera state. You must call storeState() first\r\n     */\r\n    public override _restoreStateValues(): boolean {\r\n        if (this.hasStateStored() && this.restoreStateInterpolationFactor > Epsilon && this.restoreStateInterpolationFactor < 1) {\r\n            this.interpolateTo(this._storedAlpha, this._storedBeta, this._storedRadius, this._storedTarget, this._storedTargetScreenOffset, this.restoreStateInterpolationFactor);\r\n            return true;\r\n        }\r\n        if (!super._restoreStateValues()) {\r\n            return false;\r\n        }\r\n\r\n        this.setTarget(this._storedTarget.clone());\r\n        this.alpha = this._storedAlpha;\r\n        this.beta = this._storedBeta;\r\n        this.radius = this._storedRadius;\r\n        this.targetScreenOffset = this._storedTargetScreenOffset.clone();\r\n\r\n        this.inertialAlphaOffset = 0;\r\n        this.inertialBetaOffset = 0;\r\n        this.inertialRadiusOffset = 0;\r\n        this.inertialPanningX = 0;\r\n        this.inertialPanningY = 0;\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Stops any in-progress interpolation.\r\n     */\r\n    public stopInterpolation(): void {\r\n        this._goalAlpha = NaN;\r\n        this._goalBeta = NaN;\r\n        this._goalRadius = NaN;\r\n        this._goalTarget.set(NaN, NaN, NaN);\r\n        this._goalTargetScreenOffset.set(NaN, NaN);\r\n    }\r\n\r\n    /**\r\n     * Interpolates the camera to a goal state.\r\n     * @param alpha Defines the goal alpha.\r\n     * @param beta Defines the goal beta.\r\n     * @param radius Defines the goal radius.\r\n     * @param target Defines the goal target.\r\n     * @param targetScreenOffset Defines the goal target screen offset.\r\n     * @param interpolationFactor A value  between 0 and 1 that determines the speed of the interpolation.\r\n     * @remarks Passing undefined for any of the parameters will use the current value (effectively stopping any in-progress interpolation for that parameter).\r\n     *          Passing NaN will not start or stop any interpolation for that parameter (effectively allowing multiple interpolations of different parameters to overlap).\r\n     */\r\n    public interpolateTo(\r\n        alpha = this.alpha,\r\n        beta = this.beta,\r\n        radius = this.radius,\r\n        target = this.target,\r\n        targetScreenOffset = this.targetScreenOffset,\r\n        interpolationFactor?: number\r\n    ): void {\r\n        this.inertialAlphaOffset = 0;\r\n        this.inertialBetaOffset = 0;\r\n        this.inertialRadiusOffset = 0;\r\n        this.inertialPanningX = 0;\r\n        this.inertialPanningY = 0;\r\n\r\n        if (interpolationFactor != null) {\r\n            this._currentInterpolationFactor = interpolationFactor;\r\n        } else if (this.restoreStateInterpolationFactor !== 0) {\r\n            this._currentInterpolationFactor = this.restoreStateInterpolationFactor;\r\n        } else {\r\n            this._currentInterpolationFactor = 0.1;\r\n        }\r\n\r\n        // If NaN is passed in for a goal value, keep the current goal value.\r\n        this._goalAlpha = CheckNaN(alpha, this._goalAlpha);\r\n        this._goalBeta = CheckNaN(beta, this._goalBeta);\r\n        this._goalRadius = CheckNaN(radius, this._goalRadius);\r\n        this._goalTarget.set(CheckNaN(target.x, this._goalTarget.x), CheckNaN(target.y, this._goalTarget.y), CheckNaN(target.z, this._goalTarget.z));\r\n        this._goalTargetScreenOffset.set(CheckNaN(targetScreenOffset.x, this._goalTargetScreenOffset.x), CheckNaN(targetScreenOffset.y, this._goalTargetScreenOffset.y));\r\n\r\n        this._goalAlpha = Clamp(this._goalAlpha, this.lowerAlphaLimit ?? -Infinity, this.upperAlphaLimit ?? Infinity);\r\n        this._goalBeta = Clamp(this._goalBeta, this.lowerBetaLimit ?? -Infinity, this.upperBetaLimit ?? Infinity);\r\n        this._goalRadius = Clamp(this._goalRadius, this.lowerRadiusLimit ?? -Infinity, this.upperRadiusLimit ?? Infinity);\r\n        this._goalTarget.y = Clamp(this._goalTarget.y, this.lowerTargetYLimit ?? -Infinity, Infinity);\r\n\r\n        this._isInterpolating = true;\r\n    }\r\n\r\n    // Synchronized\r\n    /** @internal */\r\n    public override _isSynchronizedViewMatrix(): boolean {\r\n        if (!super._isSynchronizedViewMatrix()) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            this._cache._target.equals(this._getTargetPosition()) &&\r\n            this._cache.alpha === this.alpha &&\r\n            this._cache.beta === this.beta &&\r\n            this._cache.radius === this.radius &&\r\n            this._cache.targetScreenOffset.equals(this.targetScreenOffset)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public override attachControl(noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public override attachControl(ignored: any, noPreventDefault?: boolean): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * @param useCtrlForPanning  Defines whether ctrl is used for panning within the controls\r\n     */\r\n    public override attachControl(noPreventDefault: boolean, useCtrlForPanning: boolean): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * @param useCtrlForPanning  Defines whether ctrl is used for panning within the controls\r\n     */\r\n    public override attachControl(ignored: any, noPreventDefault: boolean, useCtrlForPanning: boolean): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * @param useCtrlForPanning  Defines whether ctrl is used for panning within the controls\r\n     * @param panningMouseButton Defines whether panning is allowed through mouse click button\r\n     */\r\n    public override attachControl(noPreventDefault: boolean, useCtrlForPanning: boolean, panningMouseButton: number): void;\r\n    /**\r\n     * Attached controls to the current camera.\r\n     * @param ignored defines an ignored parameter kept for backward compatibility.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     * @param useCtrlForPanning  Defines whether ctrl is used for panning within the controls\r\n     * @param panningMouseButton Defines whether panning is allowed through mouse click button\r\n     */\r\n    public override attachControl(ignored: any, noPreventDefault?: boolean, useCtrlForPanning: boolean | number = true, panningMouseButton: number = 2): void {\r\n        const args = arguments;\r\n\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(args);\r\n        this._useCtrlForPanning = useCtrlForPanning as boolean;\r\n        this._panningMouseButton = panningMouseButton;\r\n        // backwards compatibility\r\n        if (typeof args[0] === \"boolean\") {\r\n            if (args.length > 1) {\r\n                this._useCtrlForPanning = args[1];\r\n            }\r\n            if (args.length > 2) {\r\n                this._panningMouseButton = args[2];\r\n            }\r\n        }\r\n\r\n        this.inputs.attachElement(noPreventDefault);\r\n\r\n        this._reset = () => {\r\n            this.inertialAlphaOffset = 0;\r\n            this.inertialBetaOffset = 0;\r\n            this.inertialRadiusOffset = 0;\r\n            this.inertialPanningX = 0;\r\n            this.inertialPanningY = 0;\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public override detachControl(): void {\r\n        this.inputs.detachElement();\r\n\r\n        if (this._reset) {\r\n            this._reset();\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public override _checkInputs(): void {\r\n        //if (async) collision inspection was triggered, don't update the camera's position - until the collision callback was called.\r\n        if (this._collisionTriggered) {\r\n            return;\r\n        }\r\n\r\n        this.inputs.checkInputs();\r\n\r\n        let hasUserInteractions = false;\r\n\r\n        // Inertia\r\n        if (this.inertialAlphaOffset !== 0 || this.inertialBetaOffset !== 0 || this.inertialRadiusOffset !== 0) {\r\n            hasUserInteractions = true;\r\n\r\n            const directionModifier = this.invertRotation ? -1 : 1;\r\n            const handednessMultiplier = this._calculateHandednessMultiplier();\r\n            let inertialAlphaOffset = this.inertialAlphaOffset * handednessMultiplier;\r\n\r\n            if (this.beta < 0) {\r\n                inertialAlphaOffset *= -1;\r\n            }\r\n\r\n            this.alpha += inertialAlphaOffset * directionModifier;\r\n            this.beta += this.inertialBetaOffset * directionModifier;\r\n\r\n            this.radius -= this.inertialRadiusOffset;\r\n            this.inertialAlphaOffset *= this.inertia;\r\n            this.inertialBetaOffset *= this.inertia;\r\n            this.inertialRadiusOffset *= this.inertia;\r\n            if (Math.abs(this.inertialAlphaOffset) < Epsilon) {\r\n                this.inertialAlphaOffset = 0;\r\n            }\r\n            if (Math.abs(this.inertialBetaOffset) < Epsilon) {\r\n                this.inertialBetaOffset = 0;\r\n            }\r\n            if (Math.abs(this.inertialRadiusOffset) < this.speed * Epsilon) {\r\n                this.inertialRadiusOffset = 0;\r\n            }\r\n        }\r\n\r\n        // Panning inertia\r\n        if (this.inertialPanningX !== 0 || this.inertialPanningY !== 0) {\r\n            hasUserInteractions = true;\r\n\r\n            const localDirection = new Vector3(this.inertialPanningX, this.inertialPanningY, this.inertialPanningY);\r\n\r\n            this._viewMatrix.invertToRef(this._cameraTransformMatrix);\r\n            localDirection.multiplyInPlace(this.panningAxis);\r\n            Vector3.TransformNormalToRef(localDirection, this._cameraTransformMatrix, this._transformedDirection);\r\n\r\n            // If mapPanning is enabled, we need to take the upVector into account and\r\n            // make sure we're not panning in the y direction\r\n            if (this.mapPanning) {\r\n                const up = this.upVector;\r\n                const right = Vector3.CrossToRef(this._transformedDirection, up, this._transformedDirection);\r\n                Vector3.CrossToRef(up, right, this._transformedDirection);\r\n            } else if (!this.panningAxis.y) {\r\n                this._transformedDirection.y = 0;\r\n            }\r\n\r\n            if (!this._targetHost) {\r\n                if (this.panningDistanceLimit) {\r\n                    this._transformedDirection.addInPlace(this._target);\r\n                    const distanceSquared = Vector3.DistanceSquared(this._transformedDirection, this.panningOriginTarget);\r\n                    if (distanceSquared <= this.panningDistanceLimit * this.panningDistanceLimit) {\r\n                        this._target.copyFrom(this._transformedDirection);\r\n                    }\r\n                } else {\r\n                    if (this.parent) {\r\n                        const m = TmpVectors.Matrix[0];\r\n                        this.parent.getWorldMatrix().getRotationMatrixToRef(m);\r\n                        m.transposeToRef(m);\r\n                        Vector3.TransformCoordinatesToRef(this._transformedDirection, m, this._transformedDirection);\r\n                    }\r\n                    this._target.addInPlace(this._transformedDirection);\r\n                }\r\n            }\r\n\r\n            this.inertialPanningX *= this.panningInertia;\r\n            this.inertialPanningY *= this.panningInertia;\r\n\r\n            if (Math.abs(this.inertialPanningX) < this.speed * Epsilon) {\r\n                this.inertialPanningX = 0;\r\n            }\r\n            if (Math.abs(this.inertialPanningY) < this.speed * Epsilon) {\r\n                this.inertialPanningY = 0;\r\n            }\r\n        }\r\n\r\n        if (hasUserInteractions) {\r\n            this.stopInterpolation();\r\n        } else if (this._isInterpolating) {\r\n            let isInterpolating = false;\r\n            const dt = this._scene.getEngine().getDeltaTime() / 1000;\r\n            const t = 1 - Math.pow(2, -dt / this._currentInterpolationFactor);\r\n\r\n            // NOTE: If the goal is NaN, it means we are not interpolating to a new value, so we can use the current value. Hence the calls to checkNaN.\r\n\r\n            // Get the goal radius immediately as we'll need it for determining interpolation termination for the target.\r\n            const goalRadius = CheckNaN(this._goalRadius, this.radius);\r\n\r\n            // Interpolate the target if we haven't reached the goal yet.\r\n            if (!isNaN(this._goalTarget.x) || !isNaN(this._goalTarget.y) || !isNaN(this._goalTarget.z)) {\r\n                const goalTarget = TmpVectors.Vector3[0].set(\r\n                    CheckNaN(this._goalTarget.x, this._target.x),\r\n                    CheckNaN(this._goalTarget.y, this._target.y),\r\n                    CheckNaN(this._goalTarget.z, this._target.z)\r\n                );\r\n                Vector3.LerpToRef(this.target, goalTarget, t, this._target);\r\n\r\n                // Terminate the target interpolation if we the target is close relative to the radius.\r\n                // This is when visually (regardless of scale) the target appears close to its final goal position.\r\n                if ((Vector3.Distance(this.target, goalTarget) * 10) / goalRadius < Epsilon) {\r\n                    this._goalTarget.set(NaN, NaN, NaN);\r\n                    this.target.copyFrom(goalTarget);\r\n                    // Call setTarget to trigger side effects like onMeshTargetChangedObservable.\r\n                    // NOTE: We pass in true for allowSamePosition because we already checked that the goal target is different from the current target,\r\n                    // but since we are updating the existing target Vector3 instance, it will otherwise look like the value has not changed.\r\n                    this.setTarget(this.target, false, true, true);\r\n                } else {\r\n                    isInterpolating = true;\r\n                }\r\n            }\r\n\r\n            // Interpolate the rotation if we haven't reached the goal yet.\r\n            if (!isNaN(this._goalAlpha) || !isNaN(this._goalBeta)) {\r\n                // Using quaternion for smoother interpolation (and no Euler angles modulo)\r\n                const goalRotation = Quaternion.RotationAlphaBetaGammaToRef(\r\n                    CheckNaN(this._goalAlpha, this.alpha),\r\n                    CheckNaN(this._goalBeta, this.beta),\r\n                    0,\r\n                    TmpVectors.Quaternion[0]\r\n                );\r\n                const currentRotation = Quaternion.RotationAlphaBetaGammaToRef(this.alpha, this.beta, 0, TmpVectors.Quaternion[1]);\r\n                const newRotation = Quaternion.SlerpToRef(currentRotation, goalRotation, t, TmpVectors.Quaternion[2]);\r\n                newRotation.normalize();\r\n                const newAlphaBetaGamma = newRotation.toAlphaBetaGammaToRef(TmpVectors.Vector3[0]);\r\n                this.alpha = newAlphaBetaGamma.x;\r\n                this.beta = newAlphaBetaGamma.y;\r\n\r\n                // Terminate the rotation interpolation when the rotation appears visually close to the final goal rotation.\r\n                if (newRotation.isApprox(goalRotation, Epsilon / 5)) {\r\n                    this._goalAlpha = NaN;\r\n                    this._goalBeta = NaN;\r\n                    const goalAlphaBetaGamma = goalRotation.toAlphaBetaGammaToRef(TmpVectors.Vector3[0]);\r\n                    this.alpha = goalAlphaBetaGamma.x;\r\n                    this.beta = goalAlphaBetaGamma.y;\r\n                } else {\r\n                    isInterpolating = true;\r\n                }\r\n            }\r\n\r\n            // Interpolate the radius if we haven't reached the goal yet.\r\n            if (!isNaN(this._goalRadius)) {\r\n                this.radius += (goalRadius - this.radius) * t;\r\n\r\n                // Terminate the radius interpolation when we are 99.9% of the way to the goal radius, at which point it is visually indistinguishable from the goal.\r\n                if (Math.abs(goalRadius / this.radius - 1) < Epsilon) {\r\n                    this._goalRadius = NaN;\r\n                    this.radius = goalRadius;\r\n                } else {\r\n                    isInterpolating = true;\r\n                }\r\n            }\r\n\r\n            // Interpolate the target screen offset if we haven't reached the goal yet.\r\n            if (!isNaN(this._goalTargetScreenOffset.x) || !isNaN(this._goalTargetScreenOffset.y)) {\r\n                const goalTargetScreenOffset = TmpVectors.Vector2[0].set(\r\n                    CheckNaN(this._goalTargetScreenOffset.x, this.targetScreenOffset.x),\r\n                    CheckNaN(this._goalTargetScreenOffset.y, this.targetScreenOffset.y)\r\n                );\r\n                Vector2.LerpToRef(this.targetScreenOffset, goalTargetScreenOffset, t, this.targetScreenOffset);\r\n\r\n                // Terminate the target screen offset interpolation when the target screen offset appears visually close to the final goal target screen offset.\r\n                if (Vector2.Distance(this.targetScreenOffset, goalTargetScreenOffset) < Epsilon) {\r\n                    this._goalTargetScreenOffset.set(NaN, NaN);\r\n                    this.targetScreenOffset.copyFrom(goalTargetScreenOffset);\r\n                } else {\r\n                    isInterpolating = true;\r\n                }\r\n            }\r\n\r\n            this._isInterpolating = isInterpolating;\r\n        }\r\n\r\n        // Limits\r\n        this._checkLimits();\r\n\r\n        super._checkInputs();\r\n    }\r\n\r\n    protected _checkLimits() {\r\n        if (this.lowerBetaLimit === null || this.lowerBetaLimit === undefined) {\r\n            if (this.allowUpsideDown && this.beta > Math.PI) {\r\n                this.beta = this.beta - 2 * Math.PI;\r\n            }\r\n        } else {\r\n            if (this.beta < this.lowerBetaLimit) {\r\n                this.beta = this.lowerBetaLimit;\r\n            }\r\n        }\r\n\r\n        if (this.upperBetaLimit === null || this.upperBetaLimit === undefined) {\r\n            if (this.allowUpsideDown && this.beta < -Math.PI) {\r\n                this.beta = this.beta + 2 * Math.PI;\r\n            }\r\n        } else {\r\n            if (this.beta > this.upperBetaLimit) {\r\n                this.beta = this.upperBetaLimit;\r\n            }\r\n        }\r\n\r\n        if (this.lowerAlphaLimit !== null && this.alpha < this.lowerAlphaLimit) {\r\n            this.alpha = this.lowerAlphaLimit;\r\n        }\r\n        if (this.upperAlphaLimit !== null && this.alpha > this.upperAlphaLimit) {\r\n            this.alpha = this.upperAlphaLimit;\r\n        }\r\n\r\n        if (this.lowerRadiusLimit !== null && this.radius < this.lowerRadiusLimit) {\r\n            this.radius = this.lowerRadiusLimit;\r\n            this.inertialRadiusOffset = 0;\r\n        }\r\n        if (this.upperRadiusLimit !== null && this.radius > this.upperRadiusLimit) {\r\n            this.radius = this.upperRadiusLimit;\r\n            this.inertialRadiusOffset = 0;\r\n        }\r\n\r\n        this.target.y = Math.max(this.target.y, this.lowerTargetYLimit);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds angles (alpha, beta) and radius from the give position and target\r\n     */\r\n    public rebuildAnglesAndRadius(): void {\r\n        this._position.subtractToRef(this._getTargetPosition(), this._computationVector);\r\n\r\n        // need to rotate to Y up equivalent if up vector not Axis.Y\r\n        if (this._upVector.x !== 0 || this._upVector.y !== 1.0 || this._upVector.z !== 0) {\r\n            Vector3.TransformCoordinatesToRef(this._computationVector, this._upToYMatrix, this._computationVector);\r\n        }\r\n\r\n        this.radius = this._computationVector.length();\r\n\r\n        if (this.radius === 0) {\r\n            this.radius = 0.0001; // Just to avoid division by zero\r\n        }\r\n\r\n        // Alpha and Beta\r\n        const previousAlpha = this.alpha;\r\n        this.alpha = ComputeAlpha(this._computationVector);\r\n        this.beta = ComputeBeta(this._computationVector.y, this.radius);\r\n\r\n        // Calculate the number of revolutions between the new and old alpha values.\r\n        const alphaCorrectionTurns = Math.round((previousAlpha - this.alpha) / (2.0 * Math.PI));\r\n        // Adjust alpha so that its numerical representation is the closest one to the old value.\r\n        this.alpha += alphaCorrectionTurns * 2.0 * Math.PI;\r\n\r\n        this._checkLimits();\r\n    }\r\n\r\n    /**\r\n     * Use a position to define the current camera related information like alpha, beta and radius\r\n     * @param position Defines the position to set the camera at\r\n     */\r\n    public setPosition(position: Vector3): void {\r\n        if (this._position.equals(position)) {\r\n            return;\r\n        }\r\n        this._position.copyFrom(position);\r\n\r\n        this.rebuildAnglesAndRadius();\r\n    }\r\n\r\n    /**\r\n     * Defines the target the camera should look at.\r\n     * This will automatically adapt alpha beta and radius to fit within the new target.\r\n     * Please note that setting a target as a mesh will disable panning.\r\n     * @param target Defines the new target as a Vector or a transform node\r\n     * @param toBoundingCenter In case of a mesh target, defines whether to target the mesh position or its bounding information center\r\n     * @param allowSamePosition If false, prevents reapplying the new computed position if it is identical to the current one (optim)\r\n     * @param cloneAlphaBetaRadius If true, replicate the current setup (alpha, beta, radius) on the new target\r\n     */\r\n    public override setTarget(target: TransformNode | Vector3, toBoundingCenter = false, allowSamePosition = false, cloneAlphaBetaRadius = false): void {\r\n        cloneAlphaBetaRadius = this.overrideCloneAlphaBetaRadius ?? cloneAlphaBetaRadius;\r\n\r\n        if ((target as TransformNode).computeWorldMatrix) {\r\n            if (toBoundingCenter && (<any>target).getBoundingInfo) {\r\n                this._targetBoundingCenter = (<any>target).getBoundingInfo().boundingBox.centerWorld.clone();\r\n            } else {\r\n                this._targetBoundingCenter = null;\r\n            }\r\n            (<TransformNode>target).computeWorldMatrix();\r\n            this._targetHost = <TransformNode>target;\r\n            this._target = this._getTargetPosition();\r\n\r\n            this.onMeshTargetChangedObservable.notifyObservers(this._targetHost);\r\n        } else {\r\n            const newTarget = <Vector3>target;\r\n            const currentTarget = this._getTargetPosition();\r\n            if (currentTarget && !allowSamePosition && currentTarget.equals(newTarget)) {\r\n                return;\r\n            }\r\n            this._targetHost = null;\r\n            this._target = newTarget;\r\n            this._targetBoundingCenter = null;\r\n            this.onMeshTargetChangedObservable.notifyObservers(null);\r\n        }\r\n\r\n        if (!cloneAlphaBetaRadius) {\r\n            this.rebuildAnglesAndRadius();\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public override _getViewMatrix(): Matrix {\r\n        // Compute\r\n        const cosa = Math.cos(this.alpha);\r\n        const sina = Math.sin(this.alpha);\r\n        const cosb = Math.cos(this.beta);\r\n        let sinb = Math.sin(this.beta);\r\n\r\n        if (sinb === 0) {\r\n            sinb = 0.0001;\r\n        }\r\n\r\n        if (this.radius === 0) {\r\n            this.radius = 0.0001; // Just to avoid division by zero\r\n        }\r\n\r\n        const target = this._getTargetPosition();\r\n        this._computationVector.copyFromFloats(this.radius * cosa * sinb, this.radius * cosb, this.radius * sina * sinb);\r\n\r\n        // Rotate according to up vector\r\n        if (this._upVector.x !== 0 || this._upVector.y !== 1.0 || this._upVector.z !== 0) {\r\n            Vector3.TransformCoordinatesToRef(this._computationVector, this._yToUpMatrix, this._computationVector);\r\n        }\r\n\r\n        target.addToRef(this._computationVector, this._newPosition);\r\n        if (this.getScene().collisionsEnabled && this.checkCollisions) {\r\n            const coordinator = this.getScene().collisionCoordinator;\r\n            if (!this._collider) {\r\n                this._collider = coordinator.createCollider();\r\n            }\r\n            this._collider._radius = this.collisionRadius;\r\n            this._newPosition.subtractToRef(this._position, this._collisionVelocity);\r\n            this._collisionTriggered = true;\r\n            coordinator.getNewPosition(this._position, this._collisionVelocity, this._collider, 3, null, this._onCollisionPositionChange, this.uniqueId);\r\n        } else {\r\n            this._position.copyFrom(this._newPosition);\r\n\r\n            let up = this.upVector;\r\n            if (this.allowUpsideDown && sinb < 0) {\r\n                up = up.negate();\r\n            }\r\n\r\n            this._computeViewMatrix(this._position, target, up);\r\n\r\n            this._viewMatrix.addAtIndex(12, this.targetScreenOffset.x);\r\n            this._viewMatrix.addAtIndex(13, this.targetScreenOffset.y);\r\n        }\r\n        this._currentTarget.copyFrom(target);\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    protected _onCollisionPositionChange = (collisionId: number, newPosition: Vector3, collidedMesh: Nullable<AbstractMesh> = null) => {\r\n        if (!collidedMesh) {\r\n            this._previousPosition.copyFrom(this._position);\r\n        } else {\r\n            this.setPosition(newPosition);\r\n\r\n            if (this.onCollide) {\r\n                this.onCollide(collidedMesh);\r\n            }\r\n        }\r\n\r\n        // Recompute because of constraints\r\n        const cosa = Math.cos(this.alpha);\r\n        const sina = Math.sin(this.alpha);\r\n        const cosb = Math.cos(this.beta);\r\n        let sinb = Math.sin(this.beta);\r\n\r\n        if (sinb === 0) {\r\n            sinb = 0.0001;\r\n        }\r\n\r\n        const target = this._getTargetPosition();\r\n        this._computationVector.copyFromFloats(this.radius * cosa * sinb, this.radius * cosb, this.radius * sina * sinb);\r\n        target.addToRef(this._computationVector, this._newPosition);\r\n        this._position.copyFrom(this._newPosition);\r\n\r\n        let up = this.upVector;\r\n        if (this.allowUpsideDown && this.beta < 0) {\r\n            up = up.clone();\r\n            up = up.negate();\r\n        }\r\n\r\n        this._computeViewMatrix(this._position, target, up);\r\n        this._viewMatrix.addAtIndex(12, this.targetScreenOffset.x);\r\n        this._viewMatrix.addAtIndex(13, this.targetScreenOffset.y);\r\n\r\n        this._collisionTriggered = false;\r\n    };\r\n\r\n    /**\r\n     * Zooms on a mesh to be at the min distance where we could see it fully in the current viewport.\r\n     * @param meshes Defines the mesh to zoom on\r\n     * @param doNotUpdateMaxZ Defines whether or not maxZ should be updated whilst zooming on the mesh (this can happen if the mesh is big and the maxradius pretty small for instance)\r\n     */\r\n    public zoomOn(meshes?: AbstractMesh[], doNotUpdateMaxZ = false): void {\r\n        meshes = meshes || this.getScene().meshes;\r\n\r\n        const minMaxVector = Mesh.MinMax(meshes);\r\n        let distance = this._calculateLowerRadiusFromModelBoundingSphere(minMaxVector.min, minMaxVector.max);\r\n\r\n        // If there are defined limits, we need to take them into account\r\n        distance = Math.max(Math.min(distance, this.upperRadiusLimit || Number.MAX_VALUE), this.lowerRadiusLimit || 0);\r\n        this.radius = distance * this.zoomOnFactor;\r\n\r\n        if (this.mode === Camera.ORTHOGRAPHIC_CAMERA) {\r\n            const aspectRatio = this.getScene().getEngine().getAspectRatio(this);\r\n            const orthoExtent = (distance * this.zoomOnFactor) / 2;\r\n            this.orthoLeft = -orthoExtent * aspectRatio;\r\n            this.orthoRight = orthoExtent * aspectRatio;\r\n            this.orthoBottom = -orthoExtent;\r\n            this.orthoTop = orthoExtent;\r\n        }\r\n\r\n        this.focusOn({ min: minMaxVector.min, max: minMaxVector.max, distance: distance }, doNotUpdateMaxZ);\r\n    }\r\n\r\n    /**\r\n     * Focus on a mesh or a bounding box. This adapts the target and maxRadius if necessary but does not update the current radius.\r\n     * The target will be changed but the radius\r\n     * @param meshesOrMinMaxVectorAndDistance Defines the mesh or bounding info to focus on\r\n     * @param doNotUpdateMaxZ Defines whether or not maxZ should be updated whilst zooming on the mesh (this can happen if the mesh is big and the maxradius pretty small for instance)\r\n     */\r\n    public focusOn(meshesOrMinMaxVectorAndDistance: AbstractMesh[] | { min: Vector3; max: Vector3; distance: number }, doNotUpdateMaxZ = false): void {\r\n        let meshesOrMinMaxVector: { min: Vector3; max: Vector3 };\r\n        let distance: number;\r\n\r\n        if ((<any>meshesOrMinMaxVectorAndDistance).min === undefined) {\r\n            // meshes\r\n            const meshes = <AbstractMesh[]>meshesOrMinMaxVectorAndDistance || this.getScene().meshes;\r\n            meshesOrMinMaxVector = Mesh.MinMax(meshes);\r\n            distance = Vector3.Distance(meshesOrMinMaxVector.min, meshesOrMinMaxVector.max);\r\n        } else {\r\n            //minMaxVector and distance\r\n            const minMaxVectorAndDistance = <any>meshesOrMinMaxVectorAndDistance;\r\n            meshesOrMinMaxVector = minMaxVectorAndDistance;\r\n            distance = minMaxVectorAndDistance.distance;\r\n        }\r\n\r\n        this._target = Mesh.Center(meshesOrMinMaxVector);\r\n\r\n        if (!doNotUpdateMaxZ) {\r\n            this.maxZ = distance * 2;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @override\r\n     * Override Camera.createRigCamera\r\n     * @param name the name of the camera\r\n     * @param cameraIndex the index of the camera in the rig cameras array\r\n     */\r\n    public override createRigCamera(name: string, cameraIndex: number): Camera {\r\n        let alphaShift: number = 0;\r\n        switch (this.cameraRigMode) {\r\n            case Camera.RIG_MODE_STEREOSCOPIC_ANAGLYPH:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_OVERUNDER:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_INTERLACED:\r\n            case Camera.RIG_MODE_VR:\r\n                alphaShift = this._cameraRigParams.stereoHalfAngle * (cameraIndex === 0 ? 1 : -1);\r\n                break;\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED:\r\n                alphaShift = this._cameraRigParams.stereoHalfAngle * (cameraIndex === 0 ? -1 : 1);\r\n                break;\r\n        }\r\n        const rigCam = new ArcRotateCamera(name, this.alpha + alphaShift, this.beta, this.radius, this._target, this.getScene());\r\n        rigCam._cameraRigParams = {};\r\n        rigCam.isRigCamera = true;\r\n        rigCam.rigParent = this;\r\n        rigCam.upVector = this.upVector;\r\n\r\n        rigCam.mode = this.mode;\r\n        rigCam.orthoLeft = this.orthoLeft;\r\n        rigCam.orthoRight = this.orthoRight;\r\n        rigCam.orthoBottom = this.orthoBottom;\r\n        rigCam.orthoTop = this.orthoTop;\r\n\r\n        return rigCam;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * @override\r\n     * Override Camera._updateRigCameras\r\n     */\r\n    public override _updateRigCameras() {\r\n        const camLeft = <ArcRotateCamera>this._rigCameras[0];\r\n        const camRight = <ArcRotateCamera>this._rigCameras[1];\r\n\r\n        camLeft.beta = camRight.beta = this.beta;\r\n\r\n        switch (this.cameraRigMode) {\r\n            case Camera.RIG_MODE_STEREOSCOPIC_ANAGLYPH:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_OVERUNDER:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_INTERLACED:\r\n            case Camera.RIG_MODE_VR:\r\n                camLeft.alpha = this.alpha - this._cameraRigParams.stereoHalfAngle;\r\n                camRight.alpha = this.alpha + this._cameraRigParams.stereoHalfAngle;\r\n                break;\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED:\r\n                camLeft.alpha = this.alpha + this._cameraRigParams.stereoHalfAngle;\r\n                camRight.alpha = this.alpha - this._cameraRigParams.stereoHalfAngle;\r\n                break;\r\n        }\r\n        super._updateRigCameras();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _calculateLowerRadiusFromModelBoundingSphere(minimumWorld: Vector3, maximumWorld: Vector3, radiusScale: number = 1): number {\r\n        const boxVectorGlobalDiagonal = Vector3.Distance(minimumWorld, maximumWorld);\r\n\r\n        // Get aspect ratio in order to calculate frustum slope\r\n        const engine = this.getScene().getEngine();\r\n        const aspectRatio = engine.getAspectRatio(this);\r\n        const frustumSlopeY = Math.tan(this.fov / 2);\r\n        const frustumSlopeX = frustumSlopeY * aspectRatio;\r\n\r\n        // Formula for setting distance\r\n        // (Good explanation: http://stackoverflow.com/questions/2866350/move-camera-to-fit-3d-scene)\r\n        const radiusWithoutFraming = boxVectorGlobalDiagonal * 0.5;\r\n\r\n        // Horizon distance\r\n        const radius = radiusWithoutFraming * radiusScale;\r\n        const distanceForHorizontalFrustum = radius * Math.sqrt(1.0 + 1.0 / (frustumSlopeX * frustumSlopeX));\r\n        const distanceForVerticalFrustum = radius * Math.sqrt(1.0 + 1.0 / (frustumSlopeY * frustumSlopeY));\r\n        return Math.max(distanceForHorizontalFrustum, distanceForVerticalFrustum);\r\n    }\r\n\r\n    /**\r\n     * Destroy the camera and release the current resources hold by it.\r\n     */\r\n    public override dispose(): void {\r\n        this.inputs.clear();\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"ArcRotateCamera\";\r\n    }\r\n}\r\n\r\n// Register Class Name\r\nRegisterClass(\"BABYLON.ArcRotateCamera\", ArcRotateCamera);\r\n"]}