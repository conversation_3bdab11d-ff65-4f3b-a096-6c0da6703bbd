{"version": 3, "file": "nativeShaderProcessors.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Native/nativeShaderProcessors.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,2BAA2B,EAAE,MAAM,mCAAmC,CAAC;AAEhF,MAAM,YAAY,GAAG,0BAA0B,CAAC;AAEhD,gBAAgB;AAChB,MAAM,OAAO,qBAAqB;IAAlC;QACW,mBAAc,+BAAuB;IA+EhD,CAAC;IA3EU,iBAAiB,CAAC,iBAAsD;QAC3E,IAAI,CAAC,wBAAwB,GAAG,iBAA4D,CAAC;QAC7F,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,GAAG,EAAE,CAAC;YAC1D,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC1D,CAAC;IACL,CAAC;IAEM,kBAAkB,CAAC,SAAiB;QACvC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjC,OAAO,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,WAAW,GAAG,2CAA2C,CAAC;QAChE,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,oCAAoC,CAAC,IAAI,CAAC,CAAC;YAC/F,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gBAC9B,oGAAoG;gBACpG,MAAM,OAAO,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,aAAa,CAAC;gBACrJ,MAAM,OAAO,GAAG,QAAQ,IAAI,GAAG,CAAC;gBAEhC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,OAAO,IAAI,OAAO,KAAK,aAAa,IAAI,IAAI,GAAG,CAAC,CAAC;gBAE/F,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,IAAI,GAAG,IAAI,MAAM,aAAa,IAAI,OAAO,MAAM,CAAC;gBAChG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;YACzE,CAAC;iBAAM,CAAC;gBACJ,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,aAAa,IAAI,IAAI,GAAG,CAAC,CAAC;YAC5E,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEM,YAAY,CAAC,OAAe,EAAE,WAAoB;QACrD,OAAO,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAEM,gBAAgB,CAAC,OAAe,EAAE,UAAmB;QACxD,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACjE,CAAC;IAEM,aAAa,CAAC,IAAY,EAAE,OAAiB,EAAE,UAAmB;QACrE,MAAM,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,0CAA0C,CAAC,KAAK,CAAC,CAAC,CAAC;QAE/F,oBAAoB;QACpB,MAAM,KAAK,GAAG,gJAAgJ,CAAC;QAC/J,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAE/B,uBAAuB;QACvB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;QACnD,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,KAAK,CAAC,CAAC,CAAC;YAEzE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;YAC5D,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;YACrD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;YACxD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;YACpD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAClD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,uBAAuB,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,8CAA8C,CAAC,GAAG,YAAY,CAAC,CAAC;QAC3J,CAAC;aAAM,CAAC;YACJ,IAAI,IAAI,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,CAAC;gBACpD,IAAI,GAAG,2BAA2B,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,CAAC,CAAC;YAC5G,CAAC;YACD,MAAM,qBAAqB,GAAG,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1E,IAAI,qBAAqB,EAAE,CAAC;gBACxB,OAAO,sEAAsE,GAAG,IAAI,CAAC;YACzF,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["/* eslint-disable babylonjs/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport type { Nullable } from \"core/types\";\r\nimport type { IShaderProcessor } from \"../Processors/iShaderProcessor\";\r\nimport type { NativeShaderProcessingContext } from \"./nativeShaderProcessingContext\";\r\nimport type { _IShaderProcessingContext } from \"../Processors/shaderProcessingOptions\";\r\nimport { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\nimport { InjectStartingAndEndingCode } from \"../../Misc/codeStringParsingTools\";\r\n\r\nconst VaryingRegex = /(flat\\s)?\\s*varying\\s*.*/;\r\n\r\n/** @internal */\r\nexport class NativeShaderProcessor implements IShaderProcessor {\r\n    public shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    protected _nativeProcessingContext: Nullable<NativeShaderProcessingContext>;\r\n\r\n    public initializeShaders(processingContext: Nullable<_IShaderProcessingContext>): void {\r\n        this._nativeProcessingContext = processingContext as Nullable<NativeShaderProcessingContext>;\r\n        if (this._nativeProcessingContext) {\r\n            this._nativeProcessingContext.remappedAttributeNames = {};\r\n            this._nativeProcessingContext.injectInVertexMain = \"\";\r\n        }\r\n    }\r\n\r\n    public attributeProcessor(attribute: string) {\r\n        if (!this._nativeProcessingContext) {\r\n            return attribute.replace(\"attribute\", \"in\");\r\n        }\r\n\r\n        const attribRegex = /\\s*(?:attribute|in)\\s+(\\S+)\\s+(\\S+)\\s*;/gm;\r\n        const match = attribRegex.exec(attribute);\r\n        if (match !== null) {\r\n            const attributeType = match[1];\r\n            const name = match[2];\r\n\r\n            const numComponents = this._nativeProcessingContext.vertexBufferKindToNumberOfComponents[name];\r\n            if (numComponents !== undefined) {\r\n                // Special case for an int/ivecX vertex buffer that is used as a float/vecX attribute in the shader.\r\n                const newType = numComponents < 0 ? (numComponents === -1 ? \"int\" : \"ivec\" + -numComponents) : numComponents === 1 ? \"uint\" : \"uvec\" + numComponents;\r\n                const newName = `_int_${name}_`;\r\n\r\n                attribute = attribute.replace(match[0], `in ${newType} ${newName}; ${attributeType} ${name};`);\r\n\r\n                this._nativeProcessingContext.injectInVertexMain += `${name} = ${attributeType}(${newName});\\n`;\r\n                this._nativeProcessingContext.remappedAttributeNames[name] = newName;\r\n            } else {\r\n                attribute = attribute.replace(match[0], `in ${attributeType} ${name};`);\r\n            }\r\n        }\r\n        return attribute;\r\n    }\r\n\r\n    public varyingCheck(varying: string, _isFragment: boolean) {\r\n        return VaryingRegex.test(varying);\r\n    }\r\n\r\n    public varyingProcessor(varying: string, isFragment: boolean) {\r\n        return varying.replace(\"varying\", isFragment ? \"in\" : \"out\");\r\n    }\r\n\r\n    public postProcessor(code: string, defines: string[], isFragment: boolean) {\r\n        const hasDrawBuffersExtension = code.search(/#extension.+GL_EXT_draw_buffers.+require/) !== -1;\r\n\r\n        // Remove extensions\r\n        const regex = /#extension.+(GL_OVR_multiview2|GL_OES_standard_derivatives|GL_EXT_shader_texture_lod|GL_EXT_frag_depth|GL_EXT_draw_buffers).+(enable|require)/g;\r\n        code = code.replace(regex, \"\");\r\n\r\n        // Replace instructions\r\n        code = code.replace(/texture2D\\s*\\(/g, \"texture(\");\r\n        if (isFragment) {\r\n            const hasOutput = code.search(/layout *\\(location *= *0\\) *out/g) !== -1;\r\n\r\n            code = code.replace(/texture2DLodEXT\\s*\\(/g, \"textureLod(\");\r\n            code = code.replace(/textureCubeLodEXT\\s*\\(/g, \"textureLod(\");\r\n            code = code.replace(/textureCube\\s*\\(/g, \"texture(\");\r\n            code = code.replace(/gl_FragDepthEXT/g, \"gl_FragDepth\");\r\n            code = code.replace(/gl_FragColor/g, \"glFragColor\");\r\n            code = code.replace(/gl_FragData/g, \"glFragData\");\r\n            code = code.replace(/void\\s+?main\\s*\\(/g, (hasDrawBuffersExtension || hasOutput ? \"\" : \"layout(location = 0) out vec4 glFragColor;\\n\") + \"void main(\");\r\n        } else {\r\n            if (this._nativeProcessingContext?.injectInVertexMain) {\r\n                code = InjectStartingAndEndingCode(code, \"void main\", this._nativeProcessingContext.injectInVertexMain);\r\n            }\r\n            const hasMultiviewExtension = defines.indexOf(\"#define MULTIVIEW\") !== -1;\r\n            if (hasMultiviewExtension) {\r\n                return \"#extension GL_OVR_multiview2 : require\\nlayout (num_views = 2) in;\\n\" + code;\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n}\r\n"]}