{"version": 3, "file": "webGLHardwareTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGL/webGLHardwareTexture.ts"], "names": [], "mappings": "AAGA,gBAAgB;AAChB,MAAM,OAAO,oBAAoB;IAS7B,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,YAAY,kBAA0C,IAAI,EAAE,OAA8B;QAT1F,oHAAoH;QACpH,kGAAkG;QAClG,gEAAgE;QACxD,uBAAkB,GAAkC,IAAI,CAAC;QAO7D,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,eAAe,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1C,IAAI,CAAC,eAAe,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACtD,CAAC;QACL,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC9B,CAAC;IAEM,QAAQ,KAAU,CAAC;IAEnB,GAAG,CAAC,eAA6B;QACpC,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;IACzC,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,aAAa,GAAG,IAAW,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAEM,mBAAmB,CAAC,MAAyB;QAChD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAEM,wBAAwB;QAC3B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3C,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAC7C,CAAC;YACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACnC,CAAC;IACL,CAAC;IAEM,mBAAmB,CAAC,KAAK,GAAG,CAAC;QAChC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IACpD,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;CACJ", "sourcesContent": ["import type { IHardwareTextureWrapper } from \"../../Materials/Textures/hardwareTextureWrapper\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/** @internal */\r\nexport class WebGLHardwareTexture implements IHardwareTextureWrapper {\r\n    private _webGLTexture: WebGLTexture;\r\n    private _context: WebGLRenderingContext;\r\n\r\n    // There can be multiple buffers for a single WebGL texture because different layers of a 2DArrayTexture / 3DTexture\r\n    // or different faces of a cube texture can be bound to different render targets at the same time.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private _MSAARenderBuffers: Nullable<WebGLRenderbuffer[]> = null;\r\n\r\n    public get underlyingResource(): Nullable<WebGLTexture> {\r\n        return this._webGLTexture;\r\n    }\r\n\r\n    constructor(existingTexture: Nullable<WebGLTexture> = null, context: WebGLRenderingContext) {\r\n        this._context = context;\r\n        if (!existingTexture) {\r\n            existingTexture = context.createTexture();\r\n            if (!existingTexture) {\r\n                throw new Error(\"Unable to create webGL texture\");\r\n            }\r\n        }\r\n        this.set(existingTexture);\r\n    }\r\n\r\n    public setUsage(): void {}\r\n\r\n    public set(hardwareTexture: WebGLTexture) {\r\n        this._webGLTexture = hardwareTexture;\r\n    }\r\n\r\n    public reset() {\r\n        this._webGLTexture = null as any;\r\n        this._MSAARenderBuffers = null;\r\n    }\r\n\r\n    public addMSAARenderBuffer(buffer: WebGLRenderbuffer) {\r\n        if (!this._MSAARenderBuffers) {\r\n            this._MSAARenderBuffers = [];\r\n        }\r\n        this._MSAARenderBuffers.push(buffer);\r\n    }\r\n\r\n    public releaseMSAARenderBuffers() {\r\n        if (this._MSAARenderBuffers) {\r\n            for (const buffer of this._MSAARenderBuffers) {\r\n                this._context.deleteRenderbuffer(buffer);\r\n            }\r\n            this._MSAARenderBuffers = null;\r\n        }\r\n    }\r\n\r\n    public getMSAARenderBuffer(index = 0) {\r\n        return this._MSAARenderBuffers?.[index] ?? null;\r\n    }\r\n\r\n    public release() {\r\n        this.releaseMSAARenderBuffers();\r\n\r\n        if (this._webGLTexture) {\r\n            this._context.deleteTexture(this._webGLTexture);\r\n        }\r\n        this.reset();\r\n    }\r\n}\r\n"]}