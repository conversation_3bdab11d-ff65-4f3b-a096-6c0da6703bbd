{"version": 3, "file": "abstractEngine.renderPass.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.renderPass.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAgCnD,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG;IAC1C,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,wBAAwB,GAAG;IAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC3D,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,IAAa;IACjE,+DAA+D;IAC/D,MAAM,EAAE,GAAG,EAAE,cAAc,CAAC,oBAAoB,CAAC;IACjD,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,IAAI,IAAI,QAAQ,CAAC;IAC7C,OAAO,EAAE,CAAC;AACd,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,EAAU;IAC/D,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,GAAG,SAAgB,CAAC;IAE7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;oBAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAClC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;gBACnC,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import { AbstractEngine } from \"../abstractEngine\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Gets the names of the render passes that are currently created\r\n         * @returns list of the render pass names\r\n         */\r\n        getRenderPassNames(): string[];\r\n\r\n        /**\r\n         * Gets the name of the current render pass\r\n         * @returns name of the current render pass\r\n         */\r\n        getCurrentRenderPassName(): string;\r\n\r\n        /**\r\n         * Creates a render pass id\r\n         * @param name Name of the render pass (for debug purpose only)\r\n         * @returns the id of the new render pass\r\n         */\r\n        createRenderPassId(name?: string): number;\r\n\r\n        /**\r\n         * Releases a render pass id\r\n         * @param id id of the render pass to release\r\n         */\r\n        releaseRenderPassId(id: number): void;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype.getRenderPassNames = function (): string[] {\r\n    return this._renderPassNames;\r\n};\r\n\r\nAbstractEngine.prototype.getCurrentRenderPassName = function (): string {\r\n    return this._renderPassNames[this.currentRenderPassId];\r\n};\r\n\r\nAbstractEngine.prototype.createRenderPassId = function (name?: string): number {\r\n    // Note: render pass id == 0 is always for the main render pass\r\n    const id = ++AbstractEngine._RenderPassIdCounter;\r\n    this._renderPassNames[id] = name ?? \"NONAME\";\r\n    return id;\r\n};\r\n\r\nAbstractEngine.prototype.releaseRenderPassId = function (id: number): void {\r\n    this._renderPassNames[id] = undefined as any;\r\n\r\n    for (let s = 0; s < this.scenes.length; ++s) {\r\n        const scene = this.scenes[s];\r\n        for (let m = 0; m < scene.meshes.length; ++m) {\r\n            const mesh = scene.meshes[m];\r\n            if (mesh.subMeshes) {\r\n                for (let b = 0; b < mesh.subMeshes.length; ++b) {\r\n                    const subMesh = mesh.subMeshes[b];\r\n                    subMesh._removeDrawWrapper(id);\r\n                }\r\n            }\r\n        }\r\n    }\r\n};\r\n"]}