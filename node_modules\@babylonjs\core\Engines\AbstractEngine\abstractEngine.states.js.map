{"version": 3, "file": "abstractEngine.states.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.states.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,wBAAwB,CAAC;AA2EhC,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG;IACvC,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG;IACxC,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;AAC7C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,SAAiB;IACnE,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;AAClD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,yBAAyB,GAAG;IACjD,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC7C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,gCAAgC,GAAG;IACxD,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,sBAAsB,GAAG;IAC9C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC1C,CAAC,CAAC;AACF,cAAc,CAAC,SAAS,CAAC,6BAA6B,GAAG;IACrD,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,aAAa,GAAG;IACrC,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;AAC7C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,MAAe;IAC9D,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,MAAM,CAAC;AAC/C,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;IAC7F,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,WAAW,GAAG,CAAC;IAC7D,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AACxC,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,WAAW,GAAG,CAAC;IACjE,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAC5C,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { AbstractEngine } from \"../abstractEngine\";\r\nimport { Constants } from \"../constants\";\r\n\r\nimport \"./abstractEngine.alpha\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Gets the current depth function\r\n         * @returns a number defining the depth function\r\n         */\r\n        getDepthFunction(): Nullable<number>;\r\n\r\n        /**\r\n         * Sets the current depth function\r\n         * @param depthFunc defines the function to use\r\n         */\r\n        setDepthFunction(depthFunc: number): void;\r\n\r\n        /**\r\n         * Sets the current depth function to GREATER\r\n         */\r\n        setDepthFunctionToGreater(): void;\r\n\r\n        /**\r\n         * Sets the current depth function to GEQUAL\r\n         */\r\n        setDepthFunctionToGreaterOrEqual(): void;\r\n\r\n        /**\r\n         * Sets the current depth function to LESS\r\n         */\r\n        setDepthFunctionToLess(): void;\r\n\r\n        /**\r\n         * Sets the current depth function to LEQUAL\r\n         */\r\n        setDepthFunctionToLessOrEqual(): void;\r\n\r\n        /**\r\n         * Gets a boolean indicating if depth writing is enabled\r\n         * @returns the current depth writing state\r\n         */\r\n        getDepthWrite(): boolean;\r\n\r\n        /**\r\n         * Enable or disable depth writing\r\n         * @param enable defines the state to set\r\n         */\r\n        setDepthWrite(enable: boolean): void;\r\n\r\n        /**\r\n         * Sets alpha constants used by some alpha blending modes\r\n         * @param r defines the red component\r\n         * @param g defines the green component\r\n         * @param b defines the blue component\r\n         * @param a defines the alpha component\r\n         */\r\n        setAlphaConstants(r: number, g: number, b: number, a: number): void;\r\n\r\n        /**\r\n         * Gets the current alpha mode\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/transparent_rendering\r\n         * @param targetIndex defines the index of the target to get the alpha mode for (default is 0)\r\n         * @returns the current alpha mode\r\n         */\r\n        getAlphaMode(targetIndex?: number): number;\r\n\r\n        /**\r\n         * Gets the current alpha equation.\r\n         * @param targetIndex defines the index of the target to get the alpha equation for (default is 0)\r\n         * @returns the current alpha equation\r\n         */\r\n        getAlphaEquation(targetIndex?: number): number;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype.getInputElement = function (): Nullable<HTMLElement> {\r\n    return this._renderingCanvas;\r\n};\r\n\r\nAbstractEngine.prototype.getDepthFunction = function (): Nullable<number> {\r\n    return this._depthCullingState.depthFunc;\r\n};\r\n\r\nAbstractEngine.prototype.setDepthFunction = function (depthFunc: number) {\r\n    this._depthCullingState.depthFunc = depthFunc;\r\n};\r\n\r\nAbstractEngine.prototype.setDepthFunctionToGreater = function (): void {\r\n    this.setDepthFunction(Constants.GREATER);\r\n};\r\n\r\nAbstractEngine.prototype.setDepthFunctionToGreaterOrEqual = function (): void {\r\n    this.setDepthFunction(Constants.GEQUAL);\r\n};\r\n\r\nAbstractEngine.prototype.setDepthFunctionToLess = function (): void {\r\n    this.setDepthFunction(Constants.LESS);\r\n};\r\nAbstractEngine.prototype.setDepthFunctionToLessOrEqual = function (): void {\r\n    this.setDepthFunction(Constants.LEQUAL);\r\n};\r\n\r\nAbstractEngine.prototype.getDepthWrite = function (): boolean {\r\n    return this._depthCullingState.depthMask;\r\n};\r\n\r\nAbstractEngine.prototype.setDepthWrite = function (enable: boolean): void {\r\n    this._depthCullingState.depthMask = enable;\r\n};\r\n\r\nAbstractEngine.prototype.setAlphaConstants = function (r: number, g: number, b: number, a: number): void {\r\n    this._alphaState.setAlphaBlendConstants(r, g, b, a);\r\n};\r\n\r\nAbstractEngine.prototype.getAlphaMode = function (targetIndex = 0): number {\r\n    return this._alphaMode[targetIndex];\r\n};\r\n\r\nAbstractEngine.prototype.getAlphaEquation = function (targetIndex = 0): number {\r\n    return this._alphaEquation[targetIndex];\r\n};\r\n"]}