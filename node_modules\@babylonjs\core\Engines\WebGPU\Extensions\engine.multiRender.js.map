{"version": 3, "file": "engine.multiRender.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.multiRender.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,6CAA6C,CAAC;AAErG,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAE9C,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAG5C,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AA2ElD,YAAY,CAAC,SAAS,CAAC,qCAAqC,GAAG,UAC3D,SAA8B,EAC9B,yBAAkC,KAAK,EACvC,cAA2B;IAE3B,IAAI,cAAc,EAAE,CAAC;QACjB,cAAc,EAAE,CAAC;IACrB,CAAC;IAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAE7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC1B,IAAI,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAEjC,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;IAC1B,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IACrC,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACtE,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,0BAA0B,GAAG,UAAU,IAAiB,EAAE,OAAkC,EAAE,iBAA2B;IAC5I,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAI,mBAAmB,GAAG,IAAI,CAAC;IAC/B,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAClC,IAAI,oBAAoB,GAAG,KAAK,CAAC;IACjC,IAAI,kBAAkB,GAAG,SAAS,CAAC,qBAAqB,CAAC;IACzD,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,MAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC;IACxD,MAAM,mBAAmB,GAAG,SAAS,CAAC,8BAA8B,CAAC;IACrE,MAAM,oBAAoB,GAAG,KAAK,CAAC;IACnC,MAAM,aAAa,GAAG,SAAS,CAAC,kBAAkB,CAAC;IACnD,MAAM,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC;IAE3C,IAAI,KAAK,GAAa,EAAE,CAAC;IACzB,IAAI,aAAa,GAAa,EAAE,CAAC;IACjC,IAAI,cAAc,GAAc,EAAE,CAAC;IACnC,IAAI,OAAO,GAAa,EAAE,CAAC;IAC3B,IAAI,OAAO,GAAa,EAAE,CAAC;IAC3B,IAAI,SAAS,GAAa,EAAE,CAAC;IAC7B,IAAI,UAAU,GAAa,EAAE,CAAC;IAC9B,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,IAAI,aAAa,GAAa,EAAE,CAAC;IACjC,IAAI,kBAAkB,GAAG,KAAK,CAAC;IAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAA8B,CAAC;IAE1G,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QACxB,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,KAAK,CAAC;QACnD,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC;QAC1D,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,IAAI,KAAK,CAAC;QAC/D,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,KAAK,CAAC;QAC7D,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;QACzC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,SAAS,CAAC,qBAAqB,CAAC;QACnF,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;QAC/B,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC;QACvD,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC;QAC1D,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC;QACrC,OAAO,GAAG,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC;QACzC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC;QAC3C,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC;QAC9C,MAAM,GAAG,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC;QACvC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC;QAClC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC;QACvD,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC;QACrC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,KAAK,CAAC;IAC7D,CAAC;IAED,MAAM,KAAK,GAAuC,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;IAC9E,MAAM,MAAM,GAAuC,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;IAEhF,MAAM,QAAQ,GAAsB,EAAE,CAAC;IACvC,MAAM,WAAW,GAAa,EAAE,CAAC;IACjC,MAAM,kBAAkB,GAAa,EAAE,CAAC;IAExC,SAAS,CAAC,KAAK,GAAG,OAAO,EAAE,KAAK,IAAI,0BAA0B,CAAC;IAC/D,SAAS,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;IACrD,SAAS,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;IACzD,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;IACrC,SAAS,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IAEnD,IAAI,mBAAmB,GAA8B,IAAI,CAAC;IAC1D,IAAI,CAAC,mBAAmB,IAAI,qBAAqB,IAAI,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAChG,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACxB,mGAAmG;YACnG,oDAAoD;YACpD,IAAI,mBAAmB,IAAI,qBAAqB,EAAE,CAAC;gBAC/C,kBAAkB,GAAG,SAAS,CAAC,8BAA8B,CAAC;YAClE,CAAC;iBAAM,IAAI,mBAAmB,EAAE,CAAC;gBAC7B,kBAAkB,GAAG,SAAS,CAAC,2BAA2B,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACJ,kBAAkB,GAAG,SAAS,CAAC,sBAAsB,CAAC;YAC1D,CAAC;QACL,CAAC;QACD,mBAAmB,GAAG,SAAS,CAAC,yBAAyB,CAAC,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,EAAE,kBAAkB,EAAE,SAAS,CAAC,KAAK,GAAG,eAAe,CAAC,CAAC;IACzJ,CAAC;IAED,MAAM,mBAAmB,GAAG,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,eAAe,CAAC;IAE9H,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,IAAI,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC;QAC3D,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC;QAEnC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC;QAC3C,MAAM,aAAa,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;QAEnG,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC;QAC3C,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAClC,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAEtC,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC;YAClF,sEAAsE;YACtE,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QAC1D,CAAC;aAAM,IAAI,IAAI,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC;YAClG,2EAA2E;YAC3E,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YACnE,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;QAC5G,CAAC;QAED,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACxB,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAErE,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;YACtC,SAAS;QACb,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,kDAA0C,CAAC;QACnF,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QAEtB,QAAQ,MAAM,EAAE,CAAC;YACb,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;gBACtB,MAAM;YACV,KAAK,SAAS,CAAC,UAAU;gBACrB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;gBACpB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC;gBAC/C,MAAM;YACV,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;gBACzB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC;gBAC/C,MAAM;QACd,CAAC;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;QACpB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;QAC3D,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;QAC3D,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;QACvC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;QAE9D,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,IAAI,mBAAmB,EAAE,CAAC;YACtB,0LAA0L;YAC1L,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAErH,IAAI,mBAAmB,EAAE,CAAC;YACtB,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;QACpC,CAAC;IACL,CAAC;IAED,IAAI,mBAAmB,EAAE,CAAC;QACtB,mBAAmB,CAAC,mBAAmB,EAAE,CAAC;QAC1C,QAAQ,CAAC,YAAY,CAAC,GAAG,mBAAmB,CAAC;QAC7C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC1D,CAAC;IAED,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAChC,SAAS,CAAC,sBAAsB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAExD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtB,IAAI,CAAC,4CAA4C,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC1E,CAAC;SAAM,CAAC;QACJ,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;IACjC,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,4CAA4C,GAAG,UAAU,SAAwC,EAAE,OAAe;IACrI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;QACpH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;IAExC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;QACd,OAAO,CAAC,CAAC;IACb,CAAC;IAED,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC;IAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAmD,CAAC;QAEtF,iBAAiB,EAAE,kBAAkB,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,2IAA2I;IAC3I,MAAM,yBAAyB,GAAG,SAAS,CAAC,oBAAoB,KAAK,SAAS,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IAEnG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/F,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,CAAC;IAED,+IAA+I;IAC/I,oJAAoJ;IACpJ,oFAAoF;IACpF,IAAI,SAAS,CAAC,oBAAoB,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAC/D,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;QAC/E,SAAS,CAAC,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC;IACrD,CAAC;IAED,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;IAE7B,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,+BAA+B,GAAG,UAAU,OAA4B;IAC3F,MAAM,SAAS,GAAG,OAAoC,CAAC;IAEvD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACrB,OAAO;IACX,CAAC;IAED,MAAM,WAAW,GAAG,SAAS,CAAC,YAAa,CAAC;IAC5C,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC;IAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,OAAO,CAAC,eAAe,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAC9D,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;IACL,CAAC;AACL,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,QAA6B;IACpF,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;AACjF,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,WAAqB;IACpE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACzD,OAAO;IACX,CAAC;IAED,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;IACnC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,qHAAqH;QACrH,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;IAC7D,CAAC;SAAM,CAAC;QACJ,uLAAuL;IAC3L,CAAC;AACL,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,aAAwB;IAC1E,MAAM,MAAM,GAAG,EAAE,CAAC;IAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,uBAAuB,GAAG;IAC7C,mKAAmK;AACvK,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,CAAC,sCAAsC,GAAG;IAC5D,oJAAoJ;AACxJ,CAAC,CAAC", "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../../Materials/Textures/internalTexture\";\r\nimport type { IMultiRenderTargetOptions } from \"../../../Materials/Textures/multiRenderTarget\";\r\nimport { Logger } from \"../../../Misc/logger\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { Constants } from \"../../constants\";\r\nimport type { TextureSize } from \"../../../Materials/Textures/textureCreationOptions\";\r\nimport type { RenderTargetWrapper } from \"../../renderTargetWrapper\";\r\nimport { WebGPUEngine } from \"../../webgpuEngine\";\r\nimport type { WebGPURenderTargetWrapper } from \"../webgpuRenderTargetWrapper\";\r\nimport type { WebGPUHardwareTexture } from \"../webgpuHardwareTexture\";\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Unbind a list of render target textures from the webGL context\r\n         * This is used only when drawBuffer extension or webGL2 are active\r\n         * @param rtWrapper defines the render target wrapper to unbind\r\n         * @param disableGenerateMipMaps defines a boolean indicating that mipmaps must not be generated\r\n         * @param onBeforeUnbind defines a function which will be called before the effective unbind\r\n         */\r\n        unBindMultiColorAttachmentFramebuffer(rtWrapper: RenderTargetWrapper, disableGenerateMipMaps: boolean, onBeforeUnbind?: () => void): void;\r\n\r\n        /**\r\n         * Create a multi render target texture\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#multiple-render-target\r\n         * @param size defines the size of the texture\r\n         * @param options defines the creation options\r\n         * @param initializeBuffers if set to true, the engine will make an initializing call of drawBuffers\r\n         * @returns a new render target wrapper ready to render textures\r\n         */\r\n        createMultipleRenderTarget(size: TextureSize, options: IMultiRenderTargetOptions, initializeBuffers?: boolean): RenderTargetWrapper;\r\n\r\n        /**\r\n         * Update the sample count for a given multiple render target texture\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#multisample-render-targets\r\n         * @param rtWrapper defines the render target wrapper to update\r\n         * @param samples defines the sample count to set\r\n         * @param initializeBuffers if set to true, the engine will make an initializing call of drawBuffers\r\n         * @returns the effective sample count (could be 0 if multisample render targets are not supported)\r\n         */\r\n        updateMultipleRenderTargetTextureSampleCount(rtWrapper: Nullable<RenderTargetWrapper>, samples: number, initializeBuffers?: boolean): number;\r\n\r\n        /**\r\n         * Generates mipmaps for the texture of the (multi) render target\r\n         * @param texture The render target containing the textures to generate the mipmaps for\r\n         */\r\n        generateMipMapsMultiFramebuffer(texture: RenderTargetWrapper): void;\r\n\r\n        /**\r\n         * Resolves the MSAA textures of the (multi) render target into their non-MSAA version.\r\n         * Note that if \"texture\" is not a MSAA render target, no resolve is performed.\r\n         * @param texture The render target texture containing the MSAA textures to resolve\r\n         */\r\n        resolveMultiFramebuffer(texture: RenderTargetWrapper): void;\r\n\r\n        /**\r\n         * Select a subsets of attachments to draw to.\r\n         * @param attachments gl attachments\r\n         */\r\n        bindAttachments(attachments: number[]): void;\r\n\r\n        /**\r\n         * Creates a layout object to draw/clear on specific textures in a MRT\r\n         * @param textureStatus textureStatus[i] indicates if the i-th is active\r\n         * @returns A layout to be fed to the engine, calling `bindAttachments`.\r\n         */\r\n        buildTextureLayout(textureStatus: boolean[]): number[];\r\n\r\n        /**\r\n         * Restores the webgl state to only draw on the main color attachment\r\n         * when the frame buffer associated is the canvas frame buffer\r\n         */\r\n        restoreSingleAttachment(): void;\r\n\r\n        /**\r\n         * Restores the webgl state to only draw on the main color attachment\r\n         * when the frame buffer associated is not the canvas frame buffer\r\n         */\r\n        restoreSingleAttachmentForRenderTarget(): void;\r\n    }\r\n}\r\n\r\nWebGPUEngine.prototype.unBindMultiColorAttachmentFramebuffer = function (\r\n    rtWrapper: RenderTargetWrapper,\r\n    disableGenerateMipMaps: boolean = false,\r\n    onBeforeUnbind?: () => void\r\n): void {\r\n    if (onBeforeUnbind) {\r\n        onBeforeUnbind();\r\n    }\r\n\r\n    this._endCurrentRenderPass();\r\n\r\n    if (!disableGenerateMipMaps) {\r\n        this.generateMipMapsMultiFramebuffer(rtWrapper);\r\n    }\r\n\r\n    this._currentRenderTarget = null;\r\n\r\n    this._mrtAttachments = [];\r\n    this._cacheRenderPipeline.setMRT([]);\r\n    this._cacheRenderPipeline.setMRTAttachments(this._mrtAttachments);\r\n};\r\n\r\nWebGPUEngine.prototype.createMultipleRenderTarget = function (size: TextureSize, options: IMultiRenderTargetOptions, initializeBuffers?: boolean): RenderTargetWrapper {\r\n    let generateMipMaps = false;\r\n    let generateDepthBuffer = true;\r\n    let generateStencilBuffer = false;\r\n    let generateDepthTexture = false;\r\n    let depthTextureFormat = Constants.TEXTUREFORMAT_DEPTH16;\r\n    let textureCount = 1;\r\n    let samples = 1;\r\n\r\n    const defaultType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n    const defaultSamplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n    const defaultUseSRGBBuffer = false;\r\n    const defaultFormat = Constants.TEXTUREFORMAT_RGBA;\r\n    const defaultTarget = Constants.TEXTURE_2D;\r\n\r\n    let types: number[] = [];\r\n    let samplingModes: number[] = [];\r\n    let useSRGBBuffers: boolean[] = [];\r\n    let formats: number[] = [];\r\n    let targets: number[] = [];\r\n    let faceIndex: number[] = [];\r\n    let layerIndex: number[] = [];\r\n    let layers: number[] = [];\r\n    let labels: string[] = [];\r\n    let creationFlags: number[] = [];\r\n    let dontCreateTextures = false;\r\n\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(true, false, size) as WebGPURenderTargetWrapper;\r\n\r\n    if (options !== undefined) {\r\n        generateMipMaps = options.generateMipMaps ?? false;\r\n        generateDepthBuffer = options.generateDepthBuffer ?? true;\r\n        generateStencilBuffer = options.generateStencilBuffer ?? false;\r\n        generateDepthTexture = options.generateDepthTexture ?? false;\r\n        textureCount = options.textureCount ?? 1;\r\n        depthTextureFormat = options.depthTextureFormat ?? Constants.TEXTUREFORMAT_DEPTH16;\r\n        types = options.types || types;\r\n        samplingModes = options.samplingModes || samplingModes;\r\n        useSRGBBuffers = options.useSRGBBuffers || useSRGBBuffers;\r\n        formats = options.formats || formats;\r\n        targets = options.targetTypes || targets;\r\n        faceIndex = options.faceIndex || faceIndex;\r\n        layerIndex = options.layerIndex || layerIndex;\r\n        layers = options.layerCounts || layers;\r\n        labels = options.labels || labels;\r\n        creationFlags = options.creationFlags || creationFlags;\r\n        samples = options.samples ?? samples;\r\n        dontCreateTextures = options.dontCreateTextures ?? false;\r\n    }\r\n\r\n    const width = (<{ width: number; height: number }>size).width ?? <number>size;\r\n    const height = (<{ width: number; height: number }>size).height ?? <number>size;\r\n\r\n    const textures: InternalTexture[] = [];\r\n    const attachments: number[] = [];\r\n    const defaultAttachments: number[] = [];\r\n\r\n    rtWrapper.label = options?.label ?? \"MultiRenderTargetWrapper\";\r\n    rtWrapper._generateDepthBuffer = generateDepthBuffer;\r\n    rtWrapper._generateStencilBuffer = generateStencilBuffer;\r\n    rtWrapper._attachments = attachments;\r\n    rtWrapper._defaultAttachments = defaultAttachments;\r\n\r\n    let depthStencilTexture: Nullable<InternalTexture> = null;\r\n    if ((generateDepthBuffer || generateStencilBuffer || generateDepthTexture) && !dontCreateTextures) {\r\n        if (!generateDepthTexture) {\r\n            // The caller doesn't want a depth texture, so we are free to use the depth texture format we want.\r\n            // So, we will align with what the WebGL engine does\r\n            if (generateDepthBuffer && generateStencilBuffer) {\r\n                depthTextureFormat = Constants.TEXTUREFORMAT_DEPTH24_STENCIL8;\r\n            } else if (generateDepthBuffer) {\r\n                depthTextureFormat = Constants.TEXTUREFORMAT_DEPTH32_FLOAT;\r\n            } else {\r\n                depthTextureFormat = Constants.TEXTUREFORMAT_STENCIL8;\r\n            }\r\n        }\r\n        depthStencilTexture = rtWrapper.createDepthStencilTexture(0, false, generateStencilBuffer, 1, depthTextureFormat, rtWrapper.label + \"-DepthStencil\");\r\n    }\r\n\r\n    const mipmapsCreationOnly = options !== undefined && typeof options === \"object\" && options.createMipMaps && !generateMipMaps;\r\n\r\n    for (let i = 0; i < textureCount; i++) {\r\n        let samplingMode = samplingModes[i] || defaultSamplingMode;\r\n        let type = types[i] || defaultType;\r\n\r\n        const format = formats[i] || defaultFormat;\r\n        const useSRGBBuffer = (useSRGBBuffers[i] || defaultUseSRGBBuffer) && this._caps.supportSRGBBuffers;\r\n\r\n        const target = targets[i] || defaultTarget;\r\n        const layerCount = layers[i] ?? 1;\r\n        const creationFlag = creationFlags[i];\r\n\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n            // if floating point linear (FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        } else if (type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n            // if floating point linear (HALF_FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n            type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n            Logger.Warn(\"Float textures are not supported. Render target forced to TEXTURETYPE_UNSIGNED_BYTE type\");\r\n        }\r\n\r\n        attachments.push(i + 1);\r\n        defaultAttachments.push(initializeBuffers ? i + 1 : i === 0 ? 1 : 0);\r\n\r\n        if (target === -1 || dontCreateTextures) {\r\n            continue;\r\n        }\r\n\r\n        const texture = new InternalTexture(this, InternalTextureSource.MultiRenderTarget);\r\n        textures[i] = texture;\r\n\r\n        switch (target) {\r\n            case Constants.TEXTURE_CUBE_MAP:\r\n                texture.isCube = true;\r\n                break;\r\n            case Constants.TEXTURE_3D:\r\n                texture.is3D = true;\r\n                texture.baseDepth = texture.depth = layerCount;\r\n                break;\r\n            case Constants.TEXTURE_2D_ARRAY:\r\n                texture.is2DArray = true;\r\n                texture.baseDepth = texture.depth = layerCount;\r\n                break;\r\n        }\r\n\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.isReady = true;\r\n        texture.samples = 1;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        texture.type = type;\r\n        texture._cachedWrapU = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n        texture._cachedWrapV = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n        texture._useSRGBBuffer = useSRGBBuffer;\r\n        texture.format = format;\r\n        texture.label = labels[i] ?? rtWrapper.label + \"-Texture\" + i;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        if (mipmapsCreationOnly) {\r\n            // createGPUTextureForInternalTexture will only create a texture with mipmaps if generateMipMaps is true, as InternalTexture has no createMipMaps property, separate from generateMipMaps.\r\n            texture.generateMipMaps = true;\r\n        }\r\n\r\n        this._textureHelper.createGPUTextureForInternalTexture(texture, undefined, undefined, undefined, creationFlag, true);\r\n\r\n        if (mipmapsCreationOnly) {\r\n            texture.generateMipMaps = false;\r\n        }\r\n    }\r\n\r\n    if (depthStencilTexture) {\r\n        depthStencilTexture.incrementReferences();\r\n        textures[textureCount] = depthStencilTexture;\r\n        this._internalTexturesCache.push(depthStencilTexture);\r\n    }\r\n\r\n    rtWrapper.setTextures(textures);\r\n    rtWrapper.setLayerAndFaceIndices(layerIndex, faceIndex);\r\n\r\n    if (!dontCreateTextures) {\r\n        this.updateMultipleRenderTargetTextureSampleCount(rtWrapper, samples);\r\n    } else {\r\n        rtWrapper._samples = samples;\r\n    }\r\n\r\n    return rtWrapper;\r\n};\r\n\r\nWebGPUEngine.prototype.updateMultipleRenderTargetTextureSampleCount = function (rtWrapper: Nullable<RenderTargetWrapper>, samples: number): number {\r\n    if (!rtWrapper || !rtWrapper.textures || rtWrapper.textures.length === 0 || rtWrapper.textures[0].samples === samples) {\r\n        return samples;\r\n    }\r\n\r\n    const count = rtWrapper.textures.length;\r\n\r\n    if (count === 0) {\r\n        return 1;\r\n    }\r\n\r\n    samples = Math.min(samples, this.getCaps().maxMSAASamples);\r\n\r\n    for (let i = 0; i < count; ++i) {\r\n        const texture = rtWrapper.textures[i];\r\n        const gpuTextureWrapper = texture._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n\r\n        gpuTextureWrapper?.releaseMSAATexture(rtWrapper.getBaseArrayLayer(i));\r\n    }\r\n\r\n    // Note that rtWrapper.textures can't have null textures, lastTextureIsDepthTexture can't be true if rtWrapper._depthStencilTexture is null\r\n    const lastTextureIsDepthTexture = rtWrapper._depthStencilTexture === rtWrapper.textures[count - 1];\r\n\r\n    for (let i = 0; i < count; ++i) {\r\n        const texture = rtWrapper.textures[i];\r\n        this._textureHelper.createMSAATexture(texture, samples, false, rtWrapper.getBaseArrayLayer(i));\r\n        texture.samples = samples;\r\n    }\r\n\r\n    // Note that the last texture of textures is the depth texture if the depth texture has been generated by the MRT class and so the MSAA texture\r\n    // will be recreated for this texture by the loop above: in that case, there's no need to create the MSAA texture for rtWrapper._depthStencilTexture\r\n    // because rtWrapper._depthStencilTexture is the same texture than the depth texture\r\n    if (rtWrapper._depthStencilTexture && !lastTextureIsDepthTexture) {\r\n        this._textureHelper.createMSAATexture(rtWrapper._depthStencilTexture, samples);\r\n        rtWrapper._depthStencilTexture.samples = samples;\r\n    }\r\n\r\n    rtWrapper._samples = samples;\r\n\r\n    return samples;\r\n};\r\n\r\nWebGPUEngine.prototype.generateMipMapsMultiFramebuffer = function (texture: RenderTargetWrapper): void {\r\n    const rtWrapper = texture as WebGPURenderTargetWrapper;\r\n\r\n    if (!rtWrapper.isMulti) {\r\n        return;\r\n    }\r\n\r\n    const attachments = rtWrapper._attachments!;\r\n    const count = attachments.length;\r\n\r\n    for (let i = 0; i < count; i++) {\r\n        const texture = rtWrapper.textures![i];\r\n        if (texture.generateMipMaps && !texture.isCube && !texture.is3D) {\r\n            this._generateMipmaps(texture);\r\n        }\r\n    }\r\n};\r\n\r\nWebGPUEngine.prototype.resolveMultiFramebuffer = function (_texture: RenderTargetWrapper): void {\r\n    throw new Error(\"resolveMultiFramebuffer is not yet implemented in WebGPU!\");\r\n};\r\n\r\nWebGPUEngine.prototype.bindAttachments = function (attachments: number[]): void {\r\n    if (attachments.length === 0 || !this._currentRenderTarget) {\r\n        return;\r\n    }\r\n\r\n    this._mrtAttachments = attachments;\r\n    if (this._currentRenderPass) {\r\n        // the render pass has already been created, we need to call setMRTAttachments to update the state of the attachments\r\n        this._cacheRenderPipeline.setMRTAttachments(attachments);\r\n    } else {\r\n        // the render pass is not created yet so we don't need to call setMRTAttachments: it will be called as part of the render pass creation (see WebGPUEngine._startRenderTargetRenderPass)\r\n    }\r\n};\r\n\r\nWebGPUEngine.prototype.buildTextureLayout = function (textureStatus: boolean[]): number[] {\r\n    const result = [];\r\n\r\n    for (let i = 0; i < textureStatus.length; i++) {\r\n        if (textureStatus[i]) {\r\n            result.push(i + 1);\r\n        } else {\r\n            result.push(0);\r\n        }\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nWebGPUEngine.prototype.restoreSingleAttachment = function (): void {\r\n    // not sure what to do, probably nothing... This function and restoreSingleAttachmentForRenderTarget are not called in Babylon.js so it's hard to know the use case\r\n};\r\n\r\nWebGPUEngine.prototype.restoreSingleAttachmentForRenderTarget = function (): void {\r\n    // not sure what to do, probably nothing... This function and restoreSingleAttachment are not called in Babylon.js so it's hard to know the use case\r\n};\r\n"]}