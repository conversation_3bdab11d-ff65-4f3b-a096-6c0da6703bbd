{"version": 3, "file": "baseSixDofDragBehavior.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Behaviors/Meshes/baseSixDofDragBehavior.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAGpC,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAE1E,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAsB9C;;;;GAIG;AACH,MAAM,OAAO,sBAAsB;IAAnC;QAGY,uBAAkB,GAAY,KAAK,CAAC;QAClC,uBAAkB,GAExB,EAAE,CAAC;QAEC,eAAU,GAAY,IAAI,OAAO,EAAE,CAAC;QACpC,mBAAc,GAAe,IAAI,UAAU,EAAE,CAAC;QAE5C,cAAS,GAAG;YAClB,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,oBAAoB,EAAE,CAAC;YACvB,SAAS,EAAE,CAAC;SACf,CAAC;QAGQ,YAAO,GAAG,KAAK,CAAC;QAEhB,cAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAE1C;;;WAGG;QACI,oBAAe,GAA6B,IAAI,CAAC;QAExD;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QAevB;;WAEG;QACI,8BAAyB,GAAa,EAAE,CAAC;QAchD;;;WAGG;QACI,yBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG;QACI,0BAAqB,GAAG,IAAI,UAAU,EAAyB,CAAC;QACvE;;WAEG;QACI,qBAAgB,GAAG,IAAI,UAAU,EAAgE,CAAC;QACzG;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAW,CAAC;QAEvD;;WAEG;QACI,sBAAiB,GAAY,IAAI,CAAC;IA2X7C,CAAC;IAhbG;;OAEG;IACH,IAAW,wBAAwB;QAC/B,IAAI,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED,IAAW,wBAAwB,CAAC,KAAa;QAC7C,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAC9C,CAAC;IAOD;;;OAGG;IACH,gEAAgE;IAChE,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IACD,gEAAgE;IAChE,IAAW,wBAAwB,CAAC,wBAAgC;QAChE,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;IAC7D,CAAC;IAyBD;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,IAAI,KAAI,CAAC;IAEhB;;OAEG;IACH,IAAY,cAAc;QACtB,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAC9C,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QACpC,CAAC;IACL,CAAC;IAEO,sBAAsB;QAC1B,mFAAmF;QAEnF,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,EAAE,EAAE,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC7E,QAAQ,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QAC/C,MAAM,UAAU,GAAG,IAAI,aAAa,CAAC,EAAE,EAAE,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC/E,UAAU,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC,EAAE,EAAE,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC9E,SAAS,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;QAEhD,OAAO;YACH,QAAQ,EAAE,KAAK;YACf,MAAM,EAAE,KAAK;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,qBAAqB,EAAE,IAAI,OAAO,EAAE;YACpC,wBAAwB,EAAE,IAAI,UAAU,EAAE;YAC1C,gBAAgB,EAAE,IAAI,OAAO,EAAE;YAC/B,mBAAmB,EAAE,IAAI,UAAU,EAAE;YACrC,kBAAkB,EAAE,IAAI,OAAO,EAAE;YACjC,gBAAgB,EAAE,IAAI,OAAO,EAAE;SAClC,CAAC;IACN,CAAC;IAES,2BAA2B;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7D,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC;YAChI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAmB,CAAC,CAAC;YACvI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CACrF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAChF,CAAC;YACF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CACxF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAmB,CAC3F,CAAC;YACF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnK,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,QAAQ,CACnF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAmB,CAC1F,CAAC;QACN,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,GAAQ,EAAE,SAAiB,EAAE,WAAmB;QACrE,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YAChK,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YACxD,WAAW,GAAG,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE7D,yDAAyD;QACzD,MAAM,oBAAoB,GAAG,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACpD,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;QACrF,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1D,MAAM,yBAAyB,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;QAEpF,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAClE,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAEnE,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,yBAAyB,EAAE,WAAW,CAAC,CAAC;QACvF,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,yBAAyB,EAAE,WAAW,CAAC,CAAC;QAExF,iCAAiC;QACjC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACtC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC3C,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE5C,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACrE,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC1E,CAAC;IAEO,gBAAgB,CAAC,sBAAqC,EAAE,uBAAgD,EAAE,SAAiB,EAAE,WAAmB;QACpJ,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC7D,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAChF,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,uBAAuB,EAAE,CAAC;YACzE,iBAAiB,CAAC,UAAU,CAAC,kBAAmB,CAAC,QAAQ,CAAC,uBAAuB,CAAC,kBAAmB,CAAC,CAAC;QAC3G,CAAC;aAAM,CAAC;YACJ,iBAAiB,CAAC,UAAU,CAAC,kBAAmB,CAAC,QAAQ,CAAC,sBAAsB,CAAC,kBAAmB,CAAC,CAAC;QAC1G,CAAC;QAED,iBAAiB,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACrD,iBAAiB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAEpD,kBAAkB;QAClB,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACpB,+EAA+E;YAC/E,MAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,mBAAmB,GAAG,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnD,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAe,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,CAAC;YAC1E,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;YAC/G,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACrF,MAAM,sBAAsB,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAC5D,mBAAmB,CAAC,SAAS,EAAE,CAAC;YAEhC,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC5C,MAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/C,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,cAAe,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAC7G,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YACnH,MAAM,wBAAwB,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC3D,YAAY,CAAC,SAAS,EAAE,CAAC;YACzB,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAE7B,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;YAChJ,IAAI,cAAc,GAAG,qBAAqB,GAAG,WAAW,GAAG,sBAAsB,GAAG,wBAAwB,CAAC;YAE7G,kDAAkD;YAClD,MAAM,mCAAmC,GAAG,IAAI,CAAC;YACjD,IAAI,cAAc,GAAG,CAAC,IAAI,mCAAmC,GAAG,wBAAwB,GAAG,cAAc,EAAE,CAAC;gBACxG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,mCAAmC,GAAG,wBAAwB,EAAE,CAAC,CAAC,CAAC;YACjG,CAAC;YACD,gBAAgB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YAE9C,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACzF,iBAAiB,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjE,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACxF,iBAAiB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,SAAwB;QAClC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,CAAC;YACxC,sBAAsB,CAAC,aAAa,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7F,sBAAsB,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACzD,CAAC;QAED,MAAM,aAAa,GAAG,CAAC,CAAe,EAAE,EAAE;YACtC,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7I,CAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACxE,MAAM,SAAS,GAAmB,WAAW,CAAC,KAAM,CAAC,SAAS,CAAC;YAC/D,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACvE,CAAC;YACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAC7D,MAAM,WAAW,GAAmB,WAAW,CAAC,KAAM,CAAC,WAAW,KAAK,SAAS,IAAoB,WAAW,CAAC,KAAM,CAAC,WAAW,KAAK,IAAI,CAAC;YAC5I,MAAM,eAAe,GAAmB,WAAW,CAAC,KAAM,CAAC,WAAW,KAAK,SAAS,CAAC;YACrF,IAAI,WAAW,CAAC,IAAI,IAAI,iBAAiB,CAAC,WAAW,EAAE,CAAC;gBACpD,IACI,CAAC,iBAAiB,CAAC,QAAQ;oBAC3B,WAAW,CAAC,QAAQ;oBACpB,WAAW,CAAC,QAAQ,CAAC,GAAG;oBACxB,WAAW,CAAC,QAAQ,CAAC,UAAU;oBAC/B,WAAW,CAAC,QAAQ,CAAC,WAAW;oBAChC,WAAW,CAAC,QAAQ,CAAC,GAAG;oBACxB,CAAC,CAAC,eAAe,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC;oBACvD,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAChD,CAAC;oBACC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,IAAI,WAAW,CAAC,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACxF,OAAO;oBACX,CAAC;oBAED,IACI,IAAI,CAAC,cAAc;wBACnB,IAAI,CAAC,cAAc,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa;wBAC1D,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa;wBAClC,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,EACrC,CAAC;wBACC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;oBACjF,CAAC;oBAED,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBACzC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBAE7D,IAAI,WAAW,EAAE,CAAC;wBACd,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;wBAClH,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAa,CAAC,QAAQ,CAAC,CAAC;wBAC5F,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,WAAW,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;4BACpF,iBAAiB,CAAC,UAAU,CAAC,kBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAmB,CAAC,CAAC;wBACtH,CAAC;6BAAM,CAAC;4BACJ,iBAAiB,CAAC,UAAU,CAAC,kBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAa,CAAC,kBAAmB,CAAC,CAAC;wBACtH,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;wBACrC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpF,CAAC;oBAED,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;oBAErF,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBAC/E,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBAE9E,iBAAiB,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC;oBACvF,iBAAiB,CAAC,SAAS,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;oBAErG,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBACjF,iBAAiB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;oBACvF,iBAAiB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,kBAAmB,CAAC,CAAC;oBAC/F,iBAAiB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,kBAAmB,CAAC,CAAC;oBAErG,IAAI,eAAe,EAAE,CAAC;wBAClB,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAClE,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;oBACvE,CAAC;yBAAM,CAAC;wBACJ,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAC7E,CAAC;oBAED,eAAe;oBACf,iBAAiB,CAAC,QAAQ,GAAG,IAAI,CAAC;oBAElC,IAAI,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBAC3D,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACnD,CAAC;oBAED,yBAAyB;oBACzB,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;wBACtF,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;4BAC7E,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;4BACpC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;wBACnC,CAAC;6BAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAChF,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;wBACpC,CAAC;oBACL,CAAC;oBAED,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,SAAS,CAAC,QAAQ,EAAE,iBAAiB,CAAC,SAAS,CAAC,kBAAmB,EAAE,SAAS,CAAC,CAAC;oBACxH,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,iBAAiB,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACnG,CAAC;YACL,CAAC;iBAAM,IAAI,WAAW,CAAC,IAAI,IAAI,iBAAiB,CAAC,SAAS,IAAI,WAAW,CAAC,IAAI,IAAI,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;gBACnH,MAAM,sBAAsB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAEjF,eAAe;gBACf,iBAAiB,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAEnC,IAAI,sBAAsB,KAAK,CAAC,CAAC,EAAE,CAAC;oBAChC,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC;oBACjE,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC9C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;wBACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;wBAErC,2BAA2B;wBAC3B,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;4BACjH,IAAI,CAAC,uBAAuB,EAAE,CAAC;4BAC/B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;wBACpC,CAAC;oBACL,CAAC;oBAED,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBACrE,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;oBACtE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;oBAC/B,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;gBACjD,CAAC;YACL,CAAC;iBAAM,IAAI,WAAW,CAAC,IAAI,IAAI,iBAAiB,CAAC,WAAW,EAAE,CAAC;gBAC3D,MAAM,sBAAsB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAEjF,IAAI,sBAAsB,KAAK,CAAC,CAAC,IAAI,iBAAiB,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBACzJ,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;oBAEnC,6DAA6D;oBAC7D,8BAA8B;oBAC9B,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;wBAC/E,WAAW,GAAG,CAAC,CAAC;oBACpB,CAAC;oBAED,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBACzC,IAAI,CAAC,eAAe,EAAE,CAAC;wBACnB,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAI,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;oBAC7E,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAa,EAAE,WAAW,CAAC,QAAQ,CAAC,aAAa,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;oBAC1H,CAAC;oBAED,yBAAyB;oBACzB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC;oBACzE,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;oBAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;oBAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;oBAC/C,iBAAiB,CAAC,SAAS,CAAC,0BAA0B,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;oBAC/G,iBAAiB,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBAErH,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,iBAAiB,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAElJ,yCAAyC;oBACzC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;oBAClE,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;oBAEzF,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,IAAmB,EAAE,yBAAiC,EAAE,WAAmB;QAC7F,2IAA2I;QAC3I,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,yBAAyB,GAAG,WAAW,CAAC,CAAC,CAAC,yBAAyB,GAAG,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7I,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;IACL,CAAC;IAED,6DAA6D;IACnD,gBAAgB,CAAC,aAAsB,EAAE,aAAyB,EAAE,SAAiB;QAC3F,oCAAoC;IACxC,CAAC;IAES,WAAW,CAAC,kBAA2B,EAAE,kBAA8B,EAAE,SAAiB;QAChG,oCAAoC;IACxC,CAAC;IAES,cAAc,CAAC,SAAiB;QACtC,oCAAoC;IACxC,CAAC;IAES,uBAAuB;QAC7B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,6EAA6E;YAC7E,2BAA2B;YAC3B,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,iBAAiB,EAAE,CAAC;gBAC3D,MAAM,eAAe,GAAG,IAAI,CAAC,cAAiC,CAAC;gBAC/D,eAAe,CAAC,aAAa,CACzB,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,EACvE,eAAe,CAAC,kBAAkB,EAClC,eAAe,CAAC,mBAAmB,CACtC,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,iEAAiE;gBACjE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvH,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;gBACjH,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxD,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;IACvC,CAAC;CACJ", "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { Vector3, Quaternion, TmpVectors } from \"../../Maths/math.vector\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { PickingInfo } from \"../../Collisions/pickingInfo\";\r\nimport { Camera } from \"../../Cameras/camera\";\r\nimport type { Ray } from \"../../Culling/ray\";\r\nimport type { IPointerEvent } from \"../../Events/deviceInputEvents\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\n\r\n/**\r\n * Data store to track virtual pointers movement\r\n */\r\ntype VirtualMeshInfo = {\r\n    dragging: boolean;\r\n    moving: boolean;\r\n    dragMesh: TransformNode;\r\n    originMesh: TransformNode;\r\n    pivotMesh: TransformNode;\r\n    startingPivotPosition: Vector3;\r\n    startingPivotOrientation: Quaternion;\r\n    startingPosition: Vector3;\r\n    startingOrientation: Quaternion;\r\n    lastOriginPosition: Vector3;\r\n    lastDragPosition: Vector3;\r\n};\r\n\r\n/**\r\n * Base behavior for six degrees of freedom interactions in XR experiences.\r\n * Creates virtual meshes that are dragged around\r\n * And observables for position/rotation changes\r\n */\r\nexport class BaseSixDofDragBehavior implements Behavior<Mesh> {\r\n    protected static _VirtualScene: Scene;\r\n    private _pointerObserver: Nullable<Observer<PointerInfo>>;\r\n    private _attachedToElement: boolean = false;\r\n    protected _virtualMeshesInfo: {\r\n        [id: number]: VirtualMeshInfo;\r\n    } = {};\r\n\r\n    private _tmpVector: Vector3 = new Vector3();\r\n    private _tmpQuaternion: Quaternion = new Quaternion();\r\n\r\n    protected _dragType = {\r\n        NONE: 0,\r\n        DRAG: 1,\r\n        DRAG_WITH_CONTROLLER: 2,\r\n        NEAR_DRAG: 3,\r\n    };\r\n\r\n    protected _scene: Scene;\r\n    protected _moving = false;\r\n    protected _ownerNode: TransformNode;\r\n    protected _dragging = this._dragType.NONE;\r\n\r\n    /**\r\n     * The list of child meshes that can receive drag events\r\n     * If `null`, all child meshes will receive drag event\r\n     */\r\n    public draggableMeshes: Nullable<AbstractMesh[]> = null;\r\n\r\n    /**\r\n     * How much faster the object should move when the controller is moving towards it. This is useful to bring objects that are far away from the user to them faster. Set this to 0 to avoid any speed increase. (Default: 3)\r\n     */\r\n    public zDragFactor = 3;\r\n    /**\r\n     * The id of the pointer that is currently interacting with the behavior (-1 when no pointer is active)\r\n     */\r\n    public get currentDraggingPointerId() {\r\n        if (this.currentDraggingPointerIds[0] !== undefined) {\r\n            return this.currentDraggingPointerIds[0];\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    public set currentDraggingPointerId(value: number) {\r\n        this.currentDraggingPointerIds[0] = value;\r\n    }\r\n\r\n    /**\r\n     * In case of multipointer interaction, all pointer ids currently active are stored here\r\n     */\r\n    public currentDraggingPointerIds: number[] = [];\r\n\r\n    /**\r\n     * Get or set the currentDraggingPointerId\r\n     * @deprecated Please use currentDraggingPointerId instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get currentDraggingPointerID(): number {\r\n        return this.currentDraggingPointerId;\r\n    }\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public set currentDraggingPointerID(currentDraggingPointerID: number) {\r\n        this.currentDraggingPointerId = currentDraggingPointerID;\r\n    }\r\n    /**\r\n    /**\r\n     * If camera controls should be detached during the drag\r\n     */\r\n    public detachCameraControls = true;\r\n\r\n    /**\r\n     * Fires each time a drag starts\r\n     */\r\n    public onDragStartObservable = new Observable<{ position: Vector3 }>();\r\n    /**\r\n     * Fires each time a drag happens\r\n     */\r\n    public onDragObservable = new Observable<{ delta: Vector3; position: Vector3; pickInfo: PickingInfo }>();\r\n    /**\r\n     *  Fires each time a drag ends (eg. mouse release after drag)\r\n     */\r\n    public onDragEndObservable = new Observable<unknown>();\r\n\r\n    /**\r\n     * Should the behavior allow simultaneous pointers to interact with the owner node.\r\n     */\r\n    public allowMultiPointer: boolean = true;\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"BaseSixDofDrag\";\r\n    }\r\n\r\n    /**\r\n     *  Returns true if the attached mesh is currently moving with this behavior\r\n     */\r\n    public get isMoving(): boolean {\r\n        return this._moving;\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    /**\r\n     * In the case of multiple active cameras, the cameraToUseForPointers should be used if set instead of active camera\r\n     */\r\n    private get _pointerCamera() {\r\n        if (this._scene.cameraToUseForPointers) {\r\n            return this._scene.cameraToUseForPointers;\r\n        } else {\r\n            return this._scene.activeCamera;\r\n        }\r\n    }\r\n\r\n    private _createVirtualMeshInfo() {\r\n        // Setup virtual meshes to be used for dragging without dirtying the existing scene\r\n\r\n        const dragMesh = new TransformNode(\"\", BaseSixDofDragBehavior._VirtualScene);\r\n        dragMesh.rotationQuaternion = new Quaternion();\r\n        const originMesh = new TransformNode(\"\", BaseSixDofDragBehavior._VirtualScene);\r\n        originMesh.rotationQuaternion = new Quaternion();\r\n        const pivotMesh = new TransformNode(\"\", BaseSixDofDragBehavior._VirtualScene);\r\n        pivotMesh.rotationQuaternion = new Quaternion();\r\n\r\n        return {\r\n            dragging: false,\r\n            moving: false,\r\n            dragMesh,\r\n            originMesh,\r\n            pivotMesh,\r\n            startingPivotPosition: new Vector3(),\r\n            startingPivotOrientation: new Quaternion(),\r\n            startingPosition: new Vector3(),\r\n            startingOrientation: new Quaternion(),\r\n            lastOriginPosition: new Vector3(),\r\n            lastDragPosition: new Vector3(),\r\n        };\r\n    }\r\n\r\n    protected _resetVirtualMeshesPosition() {\r\n        for (let i = 0; i < this.currentDraggingPointerIds.length; i++) {\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].pivotMesh.position.copyFrom(this._ownerNode.getAbsolutePivotPoint());\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].pivotMesh.rotationQuaternion!.copyFrom(this._ownerNode.rotationQuaternion!);\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].startingPivotPosition.copyFrom(\r\n                this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].pivotMesh.position\r\n            );\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].startingPivotOrientation.copyFrom(\r\n                this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].pivotMesh.rotationQuaternion!\r\n            );\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].startingPosition.copyFrom(this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].dragMesh.position);\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].startingOrientation.copyFrom(\r\n                this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].dragMesh.rotationQuaternion!\r\n            );\r\n        }\r\n    }\r\n\r\n    private _pointerUpdate2D(ray: Ray, pointerId: number, zDragFactor: number) {\r\n        if (this._pointerCamera && this._pointerCamera.cameraRigMode == Camera.RIG_MODE_NONE && !this._pointerCamera._isLeftCamera && !this._pointerCamera._isRightCamera) {\r\n            ray.origin.copyFrom(this._pointerCamera.globalPosition);\r\n            zDragFactor = 0;\r\n        }\r\n\r\n        const virtualMeshesInfo = this._virtualMeshesInfo[pointerId];\r\n\r\n        // Calculate controller drag distance in controller space\r\n        const originDragDifference = TmpVectors.Vector3[11];\r\n        ray.origin.subtractToRef(virtualMeshesInfo.lastOriginPosition, originDragDifference);\r\n        virtualMeshesInfo.lastOriginPosition.copyFrom(ray.origin);\r\n        const localOriginDragDifference = -Vector3.Dot(originDragDifference, ray.direction);\r\n\r\n        virtualMeshesInfo.originMesh.addChild(virtualMeshesInfo.dragMesh);\r\n        virtualMeshesInfo.originMesh.addChild(virtualMeshesInfo.pivotMesh);\r\n\r\n        this._applyZOffset(virtualMeshesInfo.dragMesh, localOriginDragDifference, zDragFactor);\r\n        this._applyZOffset(virtualMeshesInfo.pivotMesh, localOriginDragDifference, zDragFactor);\r\n\r\n        // Update the controller position\r\n        virtualMeshesInfo.originMesh.position.copyFrom(ray.origin);\r\n        const lookAt = TmpVectors.Vector3[10];\r\n        ray.origin.addToRef(ray.direction, lookAt);\r\n        virtualMeshesInfo.originMesh.lookAt(lookAt);\r\n\r\n        virtualMeshesInfo.originMesh.removeChild(virtualMeshesInfo.dragMesh);\r\n        virtualMeshesInfo.originMesh.removeChild(virtualMeshesInfo.pivotMesh);\r\n    }\r\n\r\n    private _pointerUpdateXR(controllerAimTransform: TransformNode, controllerGripTransform: Nullable<TransformNode>, pointerId: number, zDragFactor: number) {\r\n        const virtualMeshesInfo = this._virtualMeshesInfo[pointerId];\r\n        virtualMeshesInfo.originMesh.position.copyFrom(controllerAimTransform.position);\r\n        if (this._dragging === this._dragType.NEAR_DRAG && controllerGripTransform) {\r\n            virtualMeshesInfo.originMesh.rotationQuaternion!.copyFrom(controllerGripTransform.rotationQuaternion!);\r\n        } else {\r\n            virtualMeshesInfo.originMesh.rotationQuaternion!.copyFrom(controllerAimTransform.rotationQuaternion!);\r\n        }\r\n\r\n        virtualMeshesInfo.pivotMesh.computeWorldMatrix(true);\r\n        virtualMeshesInfo.dragMesh.computeWorldMatrix(true);\r\n\r\n        // Z scaling logic\r\n        if (zDragFactor !== 0) {\r\n            // Camera.getForwardRay modifies TmpVectors.Vector[0-3], so cache it in advance\r\n            const cameraForwardVec = TmpVectors.Vector3[10];\r\n            const originDragDirection = TmpVectors.Vector3[11];\r\n            cameraForwardVec.copyFrom(this._pointerCamera!.getForwardRay().direction);\r\n            virtualMeshesInfo.originMesh.position.subtractToRef(virtualMeshesInfo.lastOriginPosition, originDragDirection);\r\n            virtualMeshesInfo.lastOriginPosition.copyFrom(virtualMeshesInfo.originMesh.position);\r\n            const controllerDragDistance = originDragDirection.length();\r\n            originDragDirection.normalize();\r\n\r\n            const cameraToDrag = TmpVectors.Vector3[12];\r\n            const controllerToDrag = TmpVectors.Vector3[9];\r\n            virtualMeshesInfo.dragMesh.absolutePosition.subtractToRef(this._pointerCamera!.globalPosition, cameraToDrag);\r\n            virtualMeshesInfo.dragMesh.absolutePosition.subtractToRef(virtualMeshesInfo.originMesh.position, controllerToDrag);\r\n            const controllerToDragDistance = controllerToDrag.length();\r\n            cameraToDrag.normalize();\r\n            controllerToDrag.normalize();\r\n\r\n            const controllerDragScaling = Math.abs(Vector3.Dot(originDragDirection, controllerToDrag)) * Vector3.Dot(originDragDirection, cameraForwardVec);\r\n            let zOffsetScaling = controllerDragScaling * zDragFactor * controllerDragDistance * controllerToDragDistance;\r\n\r\n            // Prevent pulling the mesh through the controller\r\n            const minDistanceFromControllerToDragMesh = 0.01;\r\n            if (zOffsetScaling < 0 && minDistanceFromControllerToDragMesh - controllerToDragDistance > zOffsetScaling) {\r\n                zOffsetScaling = Math.min(minDistanceFromControllerToDragMesh - controllerToDragDistance, 0);\r\n            }\r\n            controllerToDrag.scaleInPlace(zOffsetScaling);\r\n\r\n            controllerToDrag.addToRef(virtualMeshesInfo.pivotMesh.absolutePosition, this._tmpVector);\r\n            virtualMeshesInfo.pivotMesh.setAbsolutePosition(this._tmpVector);\r\n            controllerToDrag.addToRef(virtualMeshesInfo.dragMesh.absolutePosition, this._tmpVector);\r\n            virtualMeshesInfo.dragMesh.setAbsolutePosition(this._tmpVector);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attaches the scale behavior the passed in mesh\r\n     * @param ownerNode The mesh that will be scaled around once attached\r\n     */\r\n    public attach(ownerNode: TransformNode): void {\r\n        this._ownerNode = ownerNode;\r\n        this._scene = this._ownerNode.getScene();\r\n        if (!BaseSixDofDragBehavior._VirtualScene) {\r\n            BaseSixDofDragBehavior._VirtualScene = new Scene(this._scene.getEngine(), { virtual: true });\r\n            BaseSixDofDragBehavior._VirtualScene.detachControl();\r\n        }\r\n\r\n        const pickPredicate = (m: AbstractMesh) => {\r\n            return this._ownerNode === m || (m.isDescendantOf(this._ownerNode) && (!this.draggableMeshes || this.draggableMeshes.indexOf(m) !== -1));\r\n        };\r\n\r\n        this._pointerObserver = this._scene.onPointerObservable.add((pointerInfo) => {\r\n            const pointerId = (<IPointerEvent>pointerInfo.event).pointerId;\r\n            if (!this._virtualMeshesInfo[pointerId]) {\r\n                this._virtualMeshesInfo[pointerId] = this._createVirtualMeshInfo();\r\n            }\r\n            const virtualMeshesInfo = this._virtualMeshesInfo[pointerId];\r\n            const isXRPointer = (<IPointerEvent>pointerInfo.event).pointerType === \"xr-near\" || (<IPointerEvent>pointerInfo.event).pointerType === \"xr\";\r\n            const isNearXRPointer = (<IPointerEvent>pointerInfo.event).pointerType === \"xr-near\";\r\n            if (pointerInfo.type == PointerEventTypes.POINTERDOWN) {\r\n                if (\r\n                    !virtualMeshesInfo.dragging &&\r\n                    pointerInfo.pickInfo &&\r\n                    pointerInfo.pickInfo.hit &&\r\n                    pointerInfo.pickInfo.pickedMesh &&\r\n                    pointerInfo.pickInfo.pickedPoint &&\r\n                    pointerInfo.pickInfo.ray &&\r\n                    (!isNearXRPointer || pointerInfo.pickInfo.aimTransform) &&\r\n                    pickPredicate(pointerInfo.pickInfo.pickedMesh)\r\n                ) {\r\n                    if ((!this.allowMultiPointer || isXRPointer) && this.currentDraggingPointerIds.length > 0) {\r\n                        return;\r\n                    }\r\n\r\n                    if (\r\n                        this._pointerCamera &&\r\n                        this._pointerCamera.cameraRigMode === Camera.RIG_MODE_NONE &&\r\n                        !this._pointerCamera._isLeftCamera &&\r\n                        !this._pointerCamera._isRightCamera\r\n                    ) {\r\n                        pointerInfo.pickInfo.ray.origin.copyFrom(this._pointerCamera.globalPosition);\r\n                    }\r\n\r\n                    this._ownerNode.computeWorldMatrix(true);\r\n                    const virtualMeshesInfo = this._virtualMeshesInfo[pointerId];\r\n\r\n                    if (isXRPointer) {\r\n                        this._dragging = pointerInfo.pickInfo.originMesh ? this._dragType.NEAR_DRAG : this._dragType.DRAG_WITH_CONTROLLER;\r\n                        virtualMeshesInfo.originMesh.position.copyFrom(pointerInfo.pickInfo.aimTransform!.position);\r\n                        if (this._dragging === this._dragType.NEAR_DRAG && pointerInfo.pickInfo.gripTransform) {\r\n                            virtualMeshesInfo.originMesh.rotationQuaternion!.copyFrom(pointerInfo.pickInfo.gripTransform.rotationQuaternion!);\r\n                        } else {\r\n                            virtualMeshesInfo.originMesh.rotationQuaternion!.copyFrom(pointerInfo.pickInfo.aimTransform!.rotationQuaternion!);\r\n                        }\r\n                    } else {\r\n                        this._dragging = this._dragType.DRAG;\r\n                        virtualMeshesInfo.originMesh.position.copyFrom(pointerInfo.pickInfo.ray.origin);\r\n                    }\r\n\r\n                    virtualMeshesInfo.lastOriginPosition.copyFrom(virtualMeshesInfo.originMesh.position);\r\n\r\n                    virtualMeshesInfo.dragMesh.position.copyFrom(pointerInfo.pickInfo.pickedPoint);\r\n                    virtualMeshesInfo.lastDragPosition.copyFrom(pointerInfo.pickInfo.pickedPoint);\r\n\r\n                    virtualMeshesInfo.pivotMesh.position.copyFrom(this._ownerNode.getAbsolutePivotPoint());\r\n                    virtualMeshesInfo.pivotMesh.rotationQuaternion!.copyFrom(this._ownerNode.absoluteRotationQuaternion);\r\n\r\n                    virtualMeshesInfo.startingPosition.copyFrom(virtualMeshesInfo.dragMesh.position);\r\n                    virtualMeshesInfo.startingPivotPosition.copyFrom(virtualMeshesInfo.pivotMesh.position);\r\n                    virtualMeshesInfo.startingOrientation.copyFrom(virtualMeshesInfo.dragMesh.rotationQuaternion!);\r\n                    virtualMeshesInfo.startingPivotOrientation.copyFrom(virtualMeshesInfo.pivotMesh.rotationQuaternion!);\r\n\r\n                    if (isNearXRPointer) {\r\n                        virtualMeshesInfo.originMesh.addChild(virtualMeshesInfo.dragMesh);\r\n                        virtualMeshesInfo.originMesh.addChild(virtualMeshesInfo.pivotMesh);\r\n                    } else {\r\n                        virtualMeshesInfo.originMesh.lookAt(virtualMeshesInfo.dragMesh.position);\r\n                    }\r\n\r\n                    // Update state\r\n                    virtualMeshesInfo.dragging = true;\r\n\r\n                    if (this.currentDraggingPointerIds.indexOf(pointerId) === -1) {\r\n                        this.currentDraggingPointerIds.push(pointerId);\r\n                    }\r\n\r\n                    // Detach camera controls\r\n                    if (this.detachCameraControls && this._pointerCamera && !this._pointerCamera.leftCamera) {\r\n                        if (this._pointerCamera.inputs && this._pointerCamera.inputs.attachedToElement) {\r\n                            this._pointerCamera.detachControl();\r\n                            this._attachedToElement = true;\r\n                        } else if (!this.allowMultiPointer || this.currentDraggingPointerIds.length === 0) {\r\n                            this._attachedToElement = false;\r\n                        }\r\n                    }\r\n\r\n                    this._targetDragStart(virtualMeshesInfo.pivotMesh.position, virtualMeshesInfo.pivotMesh.rotationQuaternion!, pointerId);\r\n                    this.onDragStartObservable.notifyObservers({ position: virtualMeshesInfo.pivotMesh.position });\r\n                }\r\n            } else if (pointerInfo.type == PointerEventTypes.POINTERUP || pointerInfo.type == PointerEventTypes.POINTERDOUBLETAP) {\r\n                const registeredPointerIndex = this.currentDraggingPointerIds.indexOf(pointerId);\r\n\r\n                // Update state\r\n                virtualMeshesInfo.dragging = false;\r\n\r\n                if (registeredPointerIndex !== -1) {\r\n                    this.currentDraggingPointerIds.splice(registeredPointerIndex, 1);\r\n                    if (this.currentDraggingPointerIds.length === 0) {\r\n                        this._moving = false;\r\n                        this._dragging = this._dragType.NONE;\r\n\r\n                        // Reattach camera controls\r\n                        if (this.detachCameraControls && this._attachedToElement && this._pointerCamera && !this._pointerCamera.leftCamera) {\r\n                            this._reattachCameraControls();\r\n                            this._attachedToElement = false;\r\n                        }\r\n                    }\r\n\r\n                    virtualMeshesInfo.originMesh.removeChild(virtualMeshesInfo.dragMesh);\r\n                    virtualMeshesInfo.originMesh.removeChild(virtualMeshesInfo.pivotMesh);\r\n                    this._targetDragEnd(pointerId);\r\n                    this.onDragEndObservable.notifyObservers({});\r\n                }\r\n            } else if (pointerInfo.type == PointerEventTypes.POINTERMOVE) {\r\n                const registeredPointerIndex = this.currentDraggingPointerIds.indexOf(pointerId);\r\n\r\n                if (registeredPointerIndex !== -1 && virtualMeshesInfo.dragging && pointerInfo.pickInfo && (pointerInfo.pickInfo.ray || pointerInfo.pickInfo.aimTransform)) {\r\n                    let zDragFactor = this.zDragFactor;\r\n\r\n                    // 2 pointer interaction should not have a z axis drag factor\r\n                    // as well as near interaction\r\n                    if (this.currentDraggingPointerIds.length > 1 || pointerInfo.pickInfo.originMesh) {\r\n                        zDragFactor = 0;\r\n                    }\r\n\r\n                    this._ownerNode.computeWorldMatrix(true);\r\n                    if (!isNearXRPointer) {\r\n                        this._pointerUpdate2D(pointerInfo.pickInfo.ray!, pointerId, zDragFactor);\r\n                    } else {\r\n                        this._pointerUpdateXR(pointerInfo.pickInfo.aimTransform!, pointerInfo.pickInfo.gripTransform, pointerId, zDragFactor);\r\n                    }\r\n\r\n                    // Get change in rotation\r\n                    this._tmpQuaternion.copyFrom(virtualMeshesInfo.startingPivotOrientation);\r\n                    this._tmpQuaternion.x = -this._tmpQuaternion.x;\r\n                    this._tmpQuaternion.y = -this._tmpQuaternion.y;\r\n                    this._tmpQuaternion.z = -this._tmpQuaternion.z;\r\n                    virtualMeshesInfo.pivotMesh.absoluteRotationQuaternion.multiplyToRef(this._tmpQuaternion, this._tmpQuaternion);\r\n                    virtualMeshesInfo.pivotMesh.absolutePosition.subtractToRef(virtualMeshesInfo.startingPivotPosition, this._tmpVector);\r\n\r\n                    this.onDragObservable.notifyObservers({ delta: this._tmpVector, position: virtualMeshesInfo.pivotMesh.position, pickInfo: pointerInfo.pickInfo });\r\n\r\n                    // Notify herited methods and observables\r\n                    this._targetDrag(this._tmpVector, this._tmpQuaternion, pointerId);\r\n                    virtualMeshesInfo.lastDragPosition.copyFrom(virtualMeshesInfo.dragMesh.absolutePosition);\r\n\r\n                    this._moving = true;\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    private _applyZOffset(node: TransformNode, localOriginDragDifference: number, zDragFactor: number) {\r\n        // Determine how much the controller moved to/away towards the dragged object and use this to move the object further when its further away\r\n        node.position.z -= node.position.z < 1 ? localOriginDragDifference * zDragFactor : localOriginDragDifference * zDragFactor * node.position.z;\r\n        if (node.position.z < 0) {\r\n            node.position.z = 0;\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _targetDragStart(worldPosition: Vector3, worldRotation: Quaternion, pointerId: number) {\r\n        // Herited classes can override that\r\n    }\r\n\r\n    protected _targetDrag(worldDeltaPosition: Vector3, worldDeltaRotation: Quaternion, pointerId: number) {\r\n        // Herited classes can override that\r\n    }\r\n\r\n    protected _targetDragEnd(pointerId: number) {\r\n        // Herited classes can override that\r\n    }\r\n\r\n    protected _reattachCameraControls() {\r\n        if (this._pointerCamera) {\r\n            // If the camera is an ArcRotateCamera, preserve the settings from the camera\r\n            // when reattaching control\r\n            if (this._pointerCamera.getClassName() === \"ArcRotateCamera\") {\r\n                const arcRotateCamera = this._pointerCamera as ArcRotateCamera;\r\n                arcRotateCamera.attachControl(\r\n                    arcRotateCamera.inputs ? arcRotateCamera.inputs.noPreventDefault : true,\r\n                    arcRotateCamera._useCtrlForPanning,\r\n                    arcRotateCamera._panningMouseButton\r\n                );\r\n            } else {\r\n                // preserve the settings from the camera when reattaching control\r\n                this._pointerCamera.attachControl(this._pointerCamera.inputs ? this._pointerCamera.inputs.noPreventDefault : true);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        if (this._scene) {\r\n            if (this.detachCameraControls && this._attachedToElement && this._pointerCamera && !this._pointerCamera.leftCamera) {\r\n                this._reattachCameraControls();\r\n                this._attachedToElement = false;\r\n            }\r\n            this._scene.onPointerObservable.remove(this._pointerObserver);\r\n        }\r\n\r\n        for (const pointerId in this._virtualMeshesInfo) {\r\n            this._virtualMeshesInfo[pointerId].originMesh.dispose();\r\n            this._virtualMeshesInfo[pointerId].dragMesh.dispose();\r\n        }\r\n\r\n        this.onDragEndObservable.clear();\r\n        this.onDragObservable.clear();\r\n        this.onDragStartObservable.clear();\r\n    }\r\n}\r\n"]}