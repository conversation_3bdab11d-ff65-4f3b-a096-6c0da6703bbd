{"version": 3, "file": "webAudioAnalyzerSubNode.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/webAudio/subNodes/webAudioAnalyzerSubNode.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,qBAAqB,EAAE,MAAM,mDAAmD,CAAC;AAE1F,OAAO,EAAE,0BAA0B,EAAE,2BAA2B,EAAE,MAAM,iDAAiD,CAAC;AAI1H,gBAAgB;AAChB,4DAA4D;AAC5D,MAAM,CAAC,KAAK,UAAU,gCAAgC,CAAC,MAAuB;IAC1E,OAAO,IAAI,wBAAwB,CAAC,MAAM,CAAC,CAAC;AAChD,CAAC;AAED,gBAAgB;AAChB,MAAM,OAAO,wBAAyB,SAAQ,qBAAqB;IAK/D,gBAAgB;IAChB,YAAmB,MAAuB;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QALV,uBAAkB,GAAyB,IAAI,CAAC;QAChD,wBAAmB,GAA2B,IAAI,CAAC;QAMvD,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAChE,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,OAAmC,CAAC;IAClE,CAAC;IAED,IAAW,OAAO,CAAC,KAA+B;QAC9C,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YACvC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK,CAAC;QAEnC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;IAC1C,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;IAC1C,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED,gBAAgB;IAChB,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;IACpD,CAAC;IAED,IAAW,SAAS,CAAC,KAAa;QAC9B,IAAI,CAAC,aAAa,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACrD,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;IACpC,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,0BAA0B,CAAC;IACtC,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QACnF,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,gBAAgB;IACT,qBAAqB;QACxB,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrE,IAAI,CAAC,mBAAmB,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QACtF,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAEO,YAAY;QAChB,IAAI,CAAC,kBAAkB,EAAE,GAAG,CAAC,0BAA0B,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC,mBAAmB,EAAE,GAAG,CAAC,2BAA2B,EAAE,CAAC,CAAC;IACjE,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport { _AudioAnalyzerSubNode } from \"../../abstractAudio/subNodes/audioAnalyzerSubNode\";\nimport type { AudioAnalyzerFFTSizeType } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport { _GetEmptyByteFrequencyData, _GetEmptyFloatFrequencyData } from \"../../abstractAudio/subProperties/audioAnalyzer\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\nimport type { IWebAudioInNode } from \"../webAudioNode\";\n\n/** @internal */\n// eslint-disable-next-line @typescript-eslint/require-await\nexport async function _CreateAudioAnalyzerSubNodeAsync(engine: _WebAudioEngine): Promise<_AudioAnalyzerSubNode> {\n    return new _WebAudioAnalyzerSubNode(engine);\n}\n\n/** @internal */\nexport class _WebAudioAnalyzerSubNode extends _AudioAnalyzerSubNode implements IWebAudioInNode {\n    private readonly _analyzerNode: AnalyserNode;\n    private _byteFrequencyData: Nullable<Uint8Array> = null;\n    private _floatFrequencyData: Nullable<Float32Array> = null;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n\n        this._analyzerNode = new AnalyserNode(engine._audioContext);\n    }\n\n    /** @internal */\n    public get fftSize(): AudioAnalyzerFFTSizeType {\n        return this._analyzerNode.fftSize as AudioAnalyzerFFTSizeType;\n    }\n\n    public set fftSize(value: AudioAnalyzerFFTSizeType) {\n        if (value === this._analyzerNode.fftSize) {\n            return;\n        }\n\n        this._analyzerNode.fftSize = value;\n\n        this._clearArrays();\n    }\n\n    /** @internal */\n    public get _inNode(): AudioNode {\n        return this._analyzerNode;\n    }\n\n    /** @internal */\n    public get minDecibels(): number {\n        return this._analyzerNode.minDecibels;\n    }\n\n    public set minDecibels(value: number) {\n        this._analyzerNode.minDecibels = value;\n    }\n\n    /** @internal */\n    public get maxDecibels(): number {\n        return this._analyzerNode.maxDecibels;\n    }\n\n    public set maxDecibels(value: number) {\n        this._analyzerNode.maxDecibels = value;\n    }\n\n    /** @internal */\n    public get smoothing(): number {\n        return this._analyzerNode.smoothingTimeConstant;\n    }\n\n    public set smoothing(value: number) {\n        this._analyzerNode.smoothingTimeConstant = value;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._clearArrays();\n        this._byteFrequencyData = null;\n        this._floatFrequencyData = null;\n\n        this._analyzerNode.disconnect();\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioAnalyzerSubNode\";\n    }\n\n    /** @internal */\n    public getByteFrequencyData(): Uint8Array {\n        if (!this._byteFrequencyData || this._byteFrequencyData.length === 0) {\n            this._byteFrequencyData = new Uint8Array(this._analyzerNode.frequencyBinCount);\n        }\n        this._analyzerNode.getByteFrequencyData(this._byteFrequencyData);\n        return this._byteFrequencyData;\n    }\n\n    /** @internal */\n    public getFloatFrequencyData(): Float32Array {\n        if (!this._floatFrequencyData || this._floatFrequencyData.length === 0) {\n            this._floatFrequencyData = new Float32Array(this._analyzerNode.frequencyBinCount);\n        }\n        this._analyzerNode.getFloatFrequencyData(this._floatFrequencyData);\n        return this._floatFrequencyData;\n    }\n\n    private _clearArrays(): void {\n        this._byteFrequencyData?.set(_GetEmptyByteFrequencyData());\n        this._floatFrequencyData?.set(_GetEmptyFloatFrequencyData());\n    }\n}\n"]}