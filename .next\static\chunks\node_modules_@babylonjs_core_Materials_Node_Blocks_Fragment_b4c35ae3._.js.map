{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/fragmentOutputBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/fragmentOutputBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport type { NodeMaterialDefines, NodeMaterial } from \"../../nodeMaterial\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport { BindLogDepth } from \"../../../materialHelper.functions\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Color spaces supported by the fragment output block\r\n */\r\nexport enum FragmentOutputBlockColorSpace {\r\n    /** Unspecified */\r\n    NoColorSpace,\r\n    /** Gamma */\r\n    Gamma,\r\n    /** Linear */\r\n    Linear,\r\n}\r\n\r\n/**\r\n * Block used to output the final color\r\n */\r\nexport class FragmentOutputBlock extends NodeMaterialBlock {\r\n    private _linearDefineName: string;\r\n    private _gammaDefineName: string;\r\n    private _additionalColorDefineName: string;\r\n    protected _outputString: string;\r\n\r\n    /**\r\n     * Create a new FragmentOutputBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment, true, true);\r\n\r\n        this.registerInput(\"rgba\", NodeMaterialBlockConnectionPointTypes.Color4, true);\r\n        this.registerInput(\"rgb\", NodeMaterialBlockConnectionPointTypes.Color3, true);\r\n        this.registerInput(\"a\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"glow\", NodeMaterialBlockConnectionPointTypes.Color3, true);\r\n\r\n        this.rgb.acceptedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        this.rgb.acceptedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Float);\r\n\r\n        this.additionalColor.acceptedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        this.additionalColor.acceptedConnectionPointTypes.push(NodeMaterialBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if content needs to be converted to gamma space */\r\n    public convertToGammaSpace = false;\r\n\r\n    /** Gets or sets a boolean indicating if content needs to be converted to linear space */\r\n    public convertToLinearSpace = false;\r\n\r\n    /** Gets or sets a boolean indicating if logarithmic depth should be used */\r\n    @editableInPropertyPage(\"Use logarithmic depth\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { embedded: true })\r\n    public useLogarithmicDepth = false;\r\n\r\n    /**\r\n     * Gets or sets the color space used for the block\r\n     */\r\n    @editableInPropertyPage(\"Color space\", PropertyTypeForEdition.List, \"ADVANCED\", {\r\n        notifiers: { rebuild: true },\r\n        embedded: true,\r\n        options: [\r\n            { label: \"No color space\", value: FragmentOutputBlockColorSpace.NoColorSpace },\r\n            { label: \"Gamma\", value: FragmentOutputBlockColorSpace.Gamma },\r\n            { label: \"Linear\", value: FragmentOutputBlockColorSpace.Linear },\r\n        ],\r\n    })\r\n    public get colorSpace() {\r\n        if (this.convertToGammaSpace) {\r\n            return FragmentOutputBlockColorSpace.Gamma;\r\n        }\r\n        if (this.convertToLinearSpace) {\r\n            return FragmentOutputBlockColorSpace.Linear;\r\n        }\r\n        return FragmentOutputBlockColorSpace.NoColorSpace;\r\n    }\r\n\r\n    public set colorSpace(value: FragmentOutputBlockColorSpace) {\r\n        this.convertToGammaSpace = value === FragmentOutputBlockColorSpace.Gamma;\r\n        this.convertToLinearSpace = value === FragmentOutputBlockColorSpace.Linear;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"FragmentOutputBlock\";\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"logarithmicDepthConstant\");\r\n        state._excludeVariableName(\"vFragmentDepth\");\r\n    }\r\n\r\n    /**\r\n     * Gets the rgba input component\r\n     */\r\n    public get rgba(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgb input component\r\n     */\r\n    public get rgb(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the a input component\r\n     */\r\n    public get a(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the additionalColor input component (named glow in the UI for now)\r\n     */\r\n    public get additionalColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    protected _getOutputString(state: NodeMaterialBuildState): string {\r\n        return state.shaderLanguage === ShaderLanguage.WGSL ? \"fragmentOutputsColor\" : \"gl_FragColor\";\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines, nodeMaterial: NodeMaterial) {\r\n        defines.setValue(this._linearDefineName, this.convertToLinearSpace, true);\r\n        defines.setValue(this._gammaDefineName, this.convertToGammaSpace, true);\r\n        defines.setValue(this._additionalColorDefineName, this.additionalColor.connectedPoint && nodeMaterial._useAdditionalColor, true);\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        if ((this.useLogarithmicDepth || nodeMaterial.useLogarithmicDepth) && mesh) {\r\n            BindLogDepth(undefined, effect, mesh.getScene());\r\n        }\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const rgba = this.rgba;\r\n        const rgb = this.rgb;\r\n        const a = this.a;\r\n        const additionalColor = this.additionalColor;\r\n\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        state.sharedData.hints.needAlphaBlending = rgba.isConnected || a.isConnected;\r\n        state.sharedData.blocksWithDefines.push(this);\r\n\r\n        if (this.useLogarithmicDepth || state.sharedData.nodeMaterial.useLogarithmicDepth) {\r\n            state._emitUniformFromString(\"logarithmicDepthConstant\", NodeMaterialBlockConnectionPointTypes.Float);\r\n            state._emitVaryingFromString(\"vFragmentDepth\", NodeMaterialBlockConnectionPointTypes.Float);\r\n            state.sharedData.bindableBlocks.push(this);\r\n        }\r\n\r\n        if (additionalColor.connectedPoint) {\r\n            state._excludeVariableName(\"useAdditionalColor\");\r\n            state._emitUniformFromString(\"useAdditionalColor\", NodeMaterialBlockConnectionPointTypes.Float);\r\n            this._additionalColorDefineName = state._getFreeDefineName(\"USEADDITIONALCOLOR\");\r\n        }\r\n\r\n        this._linearDefineName = state._getFreeDefineName(\"CONVERTTOLINEAR\");\r\n        this._gammaDefineName = state._getFreeDefineName(\"CONVERTTOGAMMA\");\r\n\r\n        const comments = `//${this.name}`;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n\r\n        const outputString = this._getOutputString(state);\r\n        if (state.shaderLanguage === ShaderLanguage.WGSL) {\r\n            state.compilationString += `var ${outputString} : vec4<f32>;\\r\\n`;\r\n        }\r\n\r\n        const vec4 = state._getShaderType(NodeMaterialBlockConnectionPointTypes.Vector4);\r\n\r\n        if (additionalColor.connectedPoint) {\r\n            let aValue = \"1.0\";\r\n\r\n            if (a.connectedPoint) {\r\n                aValue = a.associatedVariableName;\r\n            }\r\n            state.compilationString += `#ifdef ${this._additionalColorDefineName}\\n`;\r\n            if (additionalColor.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Float) {\r\n                state.compilationString += `${outputString}  = ${vec4}(${additionalColor.associatedVariableName}, ${additionalColor.associatedVariableName}, ${additionalColor.associatedVariableName}, ${aValue});\\n`;\r\n            } else {\r\n                state.compilationString += `${outputString}  = ${vec4}(${additionalColor.associatedVariableName}, ${aValue});\\n`;\r\n            }\r\n            state.compilationString += `#else\\n`;\r\n        }\r\n\r\n        if (rgba.connectedPoint) {\r\n            if (a.isConnected) {\r\n                state.compilationString += `${outputString} = ${vec4}(${rgba.associatedVariableName}.rgb, ${a.associatedVariableName});\\n`;\r\n            } else {\r\n                state.compilationString += `${outputString}  = ${rgba.associatedVariableName};\\n`;\r\n            }\r\n        } else if (rgb.connectedPoint) {\r\n            let aValue = \"1.0\";\r\n\r\n            if (a.connectedPoint) {\r\n                aValue = a.associatedVariableName;\r\n            }\r\n\r\n            if (rgb.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Float) {\r\n                state.compilationString += `${outputString}  = ${vec4}(${rgb.associatedVariableName}, ${rgb.associatedVariableName}, ${rgb.associatedVariableName}, ${aValue});\\n`;\r\n            } else {\r\n                state.compilationString += `${outputString}  = ${vec4}(${rgb.associatedVariableName}, ${aValue});\\n`;\r\n            }\r\n        } else {\r\n            state.sharedData.checks.notConnectedNonOptionalInputs.push(rgba);\r\n        }\r\n\r\n        if (additionalColor.connectedPoint) {\r\n            state.compilationString += `#endif\\n`;\r\n        }\r\n\r\n        state.compilationString += `#ifdef ${this._linearDefineName}\\n`;\r\n        state.compilationString += `${outputString}  = toLinearSpace(${outputString});\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n\r\n        state.compilationString += `#ifdef ${this._gammaDefineName}\\n`;\r\n        state.compilationString += `${outputString}  = toGammaSpace(${outputString});\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n\r\n        if (state.shaderLanguage === ShaderLanguage.WGSL) {\r\n            state.compilationString += `#if !defined(PREPASS)\\r\\n`;\r\n            state.compilationString += `fragmentOutputs.color = ${outputString};\\r\\n`;\r\n            state.compilationString += `#endif\\r\\n`;\r\n        }\r\n\r\n        if (this.useLogarithmicDepth || state.sharedData.nodeMaterial.useLogarithmicDepth) {\r\n            const fragDepth = isWebGPU ? \"input.vFragmentDepth\" : \"vFragmentDepth\";\r\n            const uniformP = isWebGPU ? \"uniforms.\" : \"\";\r\n            const output = isWebGPU ? \"fragmentOutputs.fragDepth\" : \"gl_FragDepthEXT\";\r\n\r\n            state.compilationString += `${output} = log2(${fragDepth}) * ${uniformP}logarithmicDepthConstant * 0.5;\\n`;\r\n        }\r\n\r\n        state.compilationString += `#if defined(PREPASS)\\r\\n`;\r\n        state.compilationString += `${isWebGPU ? \"fragmentOutputs.fragData0\" : \"gl_FragData[0]\"} = ${outputString};\\r\\n`;\r\n        state.compilationString += `#endif\\r\\n`;\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n        codeString += `${this._codeVariableName}.convertToGammaSpace = ${this.convertToGammaSpace};\\n`;\r\n        codeString += `${this._codeVariableName}.convertToLinearSpace = ${this.convertToLinearSpace};\\n`;\r\n        codeString += `${this._codeVariableName}.useLogarithmicDepth = ${this.useLogarithmicDepth};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.convertToGammaSpace = this.convertToGammaSpace;\r\n        serializationObject.convertToLinearSpace = this.convertToLinearSpace;\r\n        serializationObject.useLogarithmicDepth = this.useLogarithmicDepth;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.convertToGammaSpace = !!serializationObject.convertToGammaSpace;\r\n        this.convertToLinearSpace = !!serializationObject.convertToLinearSpace;\r\n        this.useLogarithmicDepth = serializationObject.useLogarithmicDepth ?? false;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.FragmentOutputBlock\", FragmentOutputBlock);\r\n"], "names": [], "mappings": ";;;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAG3D,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AAGtG,OAAO,EAAE,YAAY,EAAE,MAAM,mCAAmC,CAAC;;;;;;;;AAMjE,IAAY,6BAOX;AAPD,CAAA,SAAY,6BAA6B;IACrC,gBAAA,EAAkB,CAClB,6BAAA,CAAA,6BAAA,CAAA,eAAA,GAAA,EAAA,GAAA,cAAY,CAAA;IACZ,UAAA,EAAY,CACZ,6BAAA,CAAA,6BAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;IACL,WAAA,EAAa,CACb,6BAAA,CAAA,6BAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAM,CAAA;AACV,CAAC,EAPW,6BAA6B,IAAA,CAA7B,6BAA6B,GAAA,CAAA,CAAA,GAOxC;AAKK,MAAO,mBAAoB,0LAAQ,oBAAiB;IAmCtD;;OAEG,CAUH,IAAW,UAAU,GAAA;QACjB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO,6BAA6B,CAAC,KAAK,CAAC;QAC/C,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO,6BAA6B,CAAC,MAAM,CAAC;QAChD,CAAC;QACD,OAAO,6BAA6B,CAAC,YAAY,CAAC;IACtD,CAAC;IAED,IAAW,UAAU,CAAC,KAAoC,EAAA;QACtD,IAAI,CAAC,mBAAmB,GAAG,KAAK,KAAK,6BAA6B,CAAC,KAAK,CAAC;QACzE,IAAI,CAAC,oBAAoB,GAAG,KAAK,KAAK,6BAA6B,CAAC,MAAM,CAAC;IAC/E,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,qBAAqB,CAAC;IACjC,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;QACvD,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAES,gBAAgB,CAAC,KAA6B,EAAA;QACpD,OAAO,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,sBAAsB,CAAC,CAAC,CAAC,cAAc,CAAC;IAClG,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAE,YAA0B,EAAA;QACnF,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QAC1E,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QACxE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,IAAI,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;IACrI,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,YAAY,CAAC,mBAAmB,CAAC,IAAI,IAAI,EAAE,CAAC;mMACzE,eAAA,AAAY,EAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;QACjB,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE7C,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,CAAC;QAC7E,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;YAChF,KAAK,CAAC,sBAAsB,CAAC,0BAA0B,gNAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;YACtG,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;YAC5F,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,eAAe,CAAC,cAAc,EAAE,CAAC;YACjC,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;YACjD,KAAK,CAAC,sBAAsB,CAAC,oBAAoB,gNAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;YAChG,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;QACrE,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;QAEnE,MAAM,QAAQ,GAAG,KAAc,CAAE,CAAC,KAAZ,IAAI,CAAC,IAAI;QAC/B,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,KAAK,CAAC,iBAAiB,IAAI,OAAmB,OAAZ,YAAY,EAAA,kBAAmB,CAAC;QACtE,CAAC;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,+MAAC,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAEjF,IAAI,eAAe,CAAC,cAAc,EAAE,CAAC;YACjC,IAAI,MAAM,GAAG,KAAK,CAAC;YAEnB,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,MAAM,GAAG,CAAC,CAAC,sBAAsB,CAAC;YACtC,CAAC;YACD,KAAK,CAAC,iBAAiB,IAAI,UAAyC,OAA/B,IAAI,CAAC,0BAA0B,EAAA,GAAI,CAAC;YACzE,IAAI,eAAe,CAAC,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,KAAK,EAAE,CAAC;gBACtF,KAAK,CAAC,iBAAiB,IAAI,UAAG,YAAY,EAAA,eAAO,IAAI,EAAA,YAAI,eAAe,CAAC,sBAAsB,EAAA,MAAgD,OAA3C,QAA0D,OAA3C,CAAC,sBAAsB,EAAA,6BAAqB,sBAAsB,EAAA,MAAW,OAAN,MAAM,EAAA,KAAM,CAAC;YAC3M,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,iBAAiB,IAAI,UAAG,YAAY,EAAA,QAAe,OAAR,IAAI,EAAA,EAAmB,0BAAC,sBAAsB,EAAA,MAAW,OAAN,MAAM,EAAA,KAAM,CAAC;YACrH,CAAC;YACD,KAAK,CAAC,iBAAiB,IAAI,QAAS,CAAC;QACzC,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;gBAChB,KAAK,CAAC,iBAAiB,IAAI,UAAG,YAAY,EAAA,cAAM,IAAI,EAAA,YAAI,IAAI,CAAC,sBAAsB,EAAA,UAAiC,OAAxB,CAAC,CAAC,sBAAsB,EAAA,KAAM,CAAC;YAC/H,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,iBAAiB,IAAI,UAAG,YAAY,EAAA,QAAkC,OAA3B,IAAI,CAAC,sBAAsB,EAAA,IAAK,CAAC;YACtF,CAAC;QACL,CAAC,MAAM,IAAI,GAAG,CAAC,cAAc,EAAE,CAAC;YAC5B,IAAI,MAAM,GAAG,KAAK,CAAC;YAEnB,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,MAAM,GAAG,CAAC,CAAC,sBAAsB,CAAC;YACtC,CAAC;YAED,IAAI,GAAG,CAAC,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,KAAK,EAAE,CAAC;gBAC1E,KAAK,CAAC,iBAAiB,IAAI,UAAG,YAAY,EAAA,QAAe,GAAG,IAAX,IAAI,EAAA,gBAAQ,sBAAsB,EAAA,aAAK,GAAG,CAAC,sBAAsB,EAAA,MAAoC,MAAM,CAArC,GAAG,CAAC,sBAAsB,EAAA,MAAW,KAAM,CAAC,SAAP;YAChK,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,iBAAiB,IAAI,UAAG,YAAY,EAAA,eAAO,IAAI,EAAA,YAAI,GAAG,CAAC,sBAAsB,EAAA,MAAW,OAAN,MAAM,EAAA,KAAM,CAAC;YACzG,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,eAAe,CAAC,cAAc,EAAE,CAAC;YACjC,KAAK,CAAC,iBAAiB,IAAI,SAAU,CAAC;QAC1C,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,UAAgC,GAAI,CAAC,GAA3B,IAAI,CAAC,iBAAiB,EAAA;QAC3D,KAAK,CAAC,iBAAiB,IAAI,UAAG,YAAY,EAAA,sBAAiC,OAAZ,YAAY,EAAA,KAAM,CAAC;QAClF,KAAK,CAAC,iBAAiB,IAAI,SAAU,CAAC;QAEtC,KAAK,CAAC,iBAAiB,IAAI,UAA+B,OAArB,IAAI,CAAC,gBAAgB,EAAA,GAAI,CAAC;QAC/D,KAAK,CAAC,iBAAiB,IAAI,UAAG,YAAY,EAAA,qBAAgC,OAAZ,YAAY,EAAA,KAAM,CAAC;QACjF,KAAK,CAAC,iBAAiB,IAAI,SAAU,CAAC;QAEtC,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,KAAK,CAAC,iBAAiB,IAAI,0BAA2B,CAAC;YACvD,KAAK,CAAC,iBAAiB,IAAI,2BAAuC,MAAO,CAAnB,AAAoB,YAAR,EAAA;YAClE,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QAC5C,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC;YAChF,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,gBAAgB,CAAC;YACvE,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,iBAAiB,CAAC;YAE1E,KAAK,CAAC,iBAAiB,IAAI,UAAG,MAAM,EAAA,mBAAW,SAAS,EAAA,QAAe,OAAR,QAAQ,EAAA,kCAAmC,CAAC;QAC/G,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,yBAA0B,CAAC;QACtD,KAAK,CAAC,iBAAiB,IAAI,UAAG,QAAQ,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,gBAAgB,EAAA,OAAkB,OAAZ,YAAY,EAAA,MAAO,CAAC;QACjH,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAC7C,UAAU,IAAI,UAAG,IAAI,CAAC,iBAAiB,EAAA,2BAAkD,IAAK,CAAC,EAA9B,IAAI,CAAC,mBAAmB,EAAA;QACzF,UAAU,IAAI,UAAG,IAAI,CAAC,iBAAiB,EAAA,4BAAoD,OAAzB,IAAI,CAAC,oBAAoB,EAAA,IAAK,CAAC;QACjG,UAAU,IAAI,GAAmD,OAAhD,IAAI,CAAC,iBAAiB,EAAA,2BAAkD,WAApB,CAAC,mBAAmB,EAAA,IAAK,CAAC;QAE/F,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEnE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;QACrE,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;;QACvE,IAAI,CAAC,mBAAmB,+CAAG,mBAAmB,CAAC,mBAAmB,+GAAI,KAAK,CAAC;IAChF,CAAC;IAzPD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAc/D,sFAAA,EAAwF,CACjF,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAEnC,uFAAA,EAAyF,CAClF,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAEpC,0EAAA,EAA4E,CAErE,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QApB/B,IAAI,CAAC,aAAa,CAAC,MAAM,gNAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,KAAK,gNAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9E,IAAI,CAAC,aAAa,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,aAAa,CAAC,MAAM,gNAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE/E,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,IAAI,+MAAC,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC1F,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC,IAAI,+MAAC,wCAAqC,CAAC,KAAK,CAAC,CAAC;QAExF,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,IAAI,+MAAC,wCAAqC,CAAC,OAAO,CAAC,CAAC;QACtG,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,IAAI,+MAAC,wCAAqC,CAAC,KAAK,CAAC,CAAC;IACxG,CAAC;CAyOJ;AA/NU,wKAAA,EAAA;8KADN,yBAAA,AAAsB,EAAC,uBAAuB,EAAA,EAAA,kCAAA,KAAkC,YAAY,EAAE;QAAE,QAAQ,EAAE,IAAI;IAAA,CAAE,CAAC;gEAC/E;2JAcnC,aAAA,EAAA;8KATC,yBAAA,AAAsB,EAAC,aAAa,EAAA,EAAA,+BAAA,KAA+B,UAAU,EAAE;QAC5E,SAAS,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE;QAC5B,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE;YACL;gBAAE,KAAK,EAAE,gBAAgB;gBAAE,KAAK,EAAE,6BAA6B,CAAC,YAAY;YAAA,CAAE;YAC9E;gBAAE,KAAK,EAAE,OAAO;gBAAE,KAAK,EAAE,6BAA6B,CAAC,KAAK;YAAA,CAAE;YAC9D;gBAAE,KAAK,EAAE,QAAQ;gBAAE,KAAK,EAAE,6BAA6B,CAAC,MAAM;YAAA,CAAE;SACnE;KACJ,CAAC;qDASD;gKA2ML,gBAAA,AAAa,EAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/screenSizeBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/screenSizeBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { ShaderLanguage } from \"../../../../Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to get the screen sizes\r\n */\r\nexport class ScreenSizeBlock extends NodeMaterialBlock {\r\n    private _varName: string;\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * Name of the variable in the shader that holds the screen size\r\n     */\r\n    public get associatedVariableName(): string {\r\n        return this._varName;\r\n    }\r\n\r\n    /**\r\n     * Creates a new ScreenSizeBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\"xy\", NodeMaterialBlockConnectionPointTypes.Vector2, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"x\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"y\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ScreenSizeBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component\r\n     */\r\n    public get xy(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the x component\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the y component\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    public override bind(effect: Effect) {\r\n        const engine = this._scene.getEngine();\r\n\r\n        effect.setFloat2(this._varName, engine.getRenderWidth(), engine.getRenderHeight());\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected writeOutputs(state: NodeMaterialBuildState, varName: string): string {\r\n        let code = \"\";\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                code += `${state._declareOutput(output)} = ${varName}.${output.name};\\n`;\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this._scene = state.sharedData.scene;\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex) {\r\n            state.sharedData.raiseBuildError(\"ScreenSizeBlock must only be used in a fragment shader\");\r\n            return this;\r\n        }\r\n\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        this._varName = state._getFreeVariableName(\"screenSize\");\r\n        state._emitUniformFromString(this._varName, NodeMaterialBlockConnectionPointTypes.Vector2);\r\n\r\n        const prefix = state.shaderLanguage === ShaderLanguage.WGSL ? \"uniforms.\" : \"\";\r\n        state.compilationString += this.writeOutputs(state, prefix + this._varName);\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ScreenSizeBlock\", ScreenSizeBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;;;;;AAQrD,MAAO,eAAgB,0LAAQ,oBAAiB;IAIlD;;OAEG,CACH,IAAW,sBAAsB,GAAA;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAcD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,IAAI,CAAC,MAAc,EAAA;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,cAAc,EAAE,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;IACvF,CAAC;IAED,gEAAgE;IACtD,YAAY,CAAC,KAA6B,EAAE,OAAe,EAAA;QACjE,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,IAAI,UAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,EAAA,cAAM,OAAO,EAAA,KAAe,OAAX,MAAM,CAAC,IAAI,EAAA,IAAK,CAAC;YAC7E,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;QAErC,IAAI,KAAK,CAAC,MAAM,sMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;YACnD,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,wDAAwD,CAAC,CAAC;YAC3F,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QACzD,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAE3F,MAAM,MAAM,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/E,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE5E,OAAO,IAAI,CAAC;IAChB,CAAC;IA/ED;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,IAAI,+MAAE,yCAAqC,CAAC,OAAO,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,sPAAqC,CAAC,KAAK,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;IAC7G,CAAC;CAsEJ;gKAED,gBAAA,AAAa,EAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/smartFilterFragmentOutputBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/smartFilterFragmentOutputBlock.ts"], "sourcesContent": ["import { FragmentOutputBlock } from \"./fragmentOutputBlock\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialModes } from \"../../Enums/nodeMaterialModes\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { ScreenSizeBlock } from \"../Fragment/screenSizeBlock\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/** @internal */\r\nexport const SfeModeDefine = \"USE_SFE_FRAMEWORK\";\r\n\r\n/**\r\n * Block used to output the final color with Smart Filters structural support.\r\n */\r\nexport class SmartFilterFragmentOutputBlock extends FragmentOutputBlock {\r\n    /**\r\n     * Create a new SmartFilterFragmentOutputBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"SmartFilterFragmentOutputBlock\";\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        super.initialize(state);\r\n\r\n        if (state.sharedData.nodeMaterial.mode !== NodeMaterialModes.SFE) {\r\n            state.sharedData.raiseBuildError(\"SmartFilterFragmentOutputBlock should not be used outside of SFE mode.\");\r\n        }\r\n\r\n        if (state.sharedData.nodeMaterial.shaderLanguage !== ShaderLanguage.GLSL) {\r\n            state.sharedData.raiseBuildError(\"WebGPU is not supported in SmartFilters mode.\");\r\n        }\r\n\r\n        // Annotate uniforms of InputBlocks and bindable blocks with their current values\r\n        if (!state.sharedData.formatConfig.getUniformAnnotation) {\r\n            state.sharedData.formatConfig.getUniformAnnotation = (name: string) => {\r\n                for (const block of state.sharedData.nodeMaterial.attachedBlocks) {\r\n                    if (block instanceof InputBlock && block.isUniform && block.associatedVariableName === name) {\r\n                        return this._generateInputBlockAnnotation(block);\r\n                    }\r\n                    if (block instanceof ScreenSizeBlock && block.associatedVariableName === name) {\r\n                        return this._generateScreenSizeBlockAnnotation();\r\n                    }\r\n                }\r\n                return \"\";\r\n            };\r\n        }\r\n\r\n        // Do our best to clean up variable names, as they will be used as display names.\r\n        state.sharedData.formatConfig.formatVariablename = (n: string) => {\r\n            let name = n;\r\n\r\n            const hasUnderscoredPrefix = name.length > 1 && name[1] === \"_\";\r\n            if (hasUnderscoredPrefix) {\r\n                name = name.substring(2);\r\n            }\r\n\r\n            return name.replace(/[^a-zA-Z]+/g, \"\");\r\n        };\r\n    }\r\n\r\n    private _generateInputBlockAnnotation(inputBlock: InputBlock): string {\r\n        const value = inputBlock.valueCallback ? inputBlock.valueCallback() : inputBlock.value;\r\n        return `// { \"default\": ${JSON.stringify(value)} }\\n`;\r\n    }\r\n\r\n    private _generateScreenSizeBlockAnnotation(): string {\r\n        return `// { \"autoBind\": \"outputResolution\" }\\n`;\r\n    }\r\n\r\n    private _getMainUvName(state: NodeMaterialBuildState): string {\r\n        // Get the ScreenUVBlock's name, which is required for SFE and should be vUV.\r\n        // NOTE: In the future, when we move to vertex shaders, update this to check for the nearest vec2 varying output.\r\n        const screenUv = state.sharedData.nodeMaterial.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"postprocess_uv\");\r\n        if (!screenUv || !screenUv.isAnAncestorOf(this)) {\r\n            return \"\";\r\n        }\r\n        return screenUv.associatedVariableName;\r\n    }\r\n\r\n    protected override _getOutputString(): string {\r\n        return \"outColor\";\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const outputString = this._getOutputString();\r\n\r\n        state._injectAtTop = `// { \"smartFilterBlockType\": \"${state.sharedData.nodeMaterial.name}\", \"namespace\": \"Babylon.NME.Exports\" }`;\r\n\r\n        state._customEntryHeader += `#ifdef ${SfeModeDefine}\\n`;\r\n        state._customEntryHeader += `vec4 nmeMain(vec2 ${this._getMainUvName(state)}) { // main\\n`;\r\n        state._customEntryHeader += `#else\\n`;\r\n        state._customEntryHeader += `void main(void) {\\n`;\r\n        state._customEntryHeader += `#endif\\n`;\r\n        state._customEntryHeader += `vec4 ${outputString} = vec4(0.0);\\n`;\r\n\r\n        state.compilationString += `\\n#ifndef ${SfeModeDefine}\\n`;\r\n        state.compilationString += `gl_FragColor = ${outputString};\\n`;\r\n        state.compilationString += `#else\\n`;\r\n        state.compilationString += `return ${outputString};\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SmartFilterFragmentOutputBlock\", SmartFilterFragmentOutputBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAE5D,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,sCAA4B;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;;;;;;AAIvD,MAAM,aAAa,GAAG,mBAAmB,CAAC;AAK3C,MAAO,8BAA+B,kNAAQ,sBAAmB;IASnE;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,gCAAgC,CAAC;IAC5C,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAExB,IAAI,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,+LAAK,oBAAiB,CAAC,GAAG,EAAE,CAAC;YAC/D,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,wEAAwE,CAAC,CAAC;QAC/G,CAAC;QAED,IAAI,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACvE,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,+CAA+C,CAAC,CAAC;QACtF,CAAC;QAED,iFAAiF;QACjF,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,oBAAoB,EAAE,CAAC;YACtD,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,oBAAoB,GAAG,CAAC,IAAY,EAAE,EAAE;gBAClE,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,cAAc,CAAE,CAAC;oBAC/D,IAAI,KAAK,yMAAY,aAAU,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,sBAAsB,KAAK,IAAI,EAAE,CAAC;wBAC1F,OAAO,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;oBACrD,CAAC;oBACD,IAAI,KAAK,iNAAY,kBAAe,IAAI,KAAK,CAAC,sBAAsB,KAAK,IAAI,EAAE,CAAC;wBAC5E,OAAO,IAAI,CAAC,kCAAkC,EAAE,CAAC;oBACrD,CAAC;gBACL,CAAC;gBACD,OAAO,EAAE,CAAC;YACd,CAAC,CAAC;QACN,CAAC;QAED,iFAAiF;QACjF,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,kBAAkB,GAAG,CAAC,CAAS,EAAE,EAAE;YAC7D,IAAI,IAAI,GAAG,CAAC,CAAC;YAEb,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;YAChE,IAAI,oBAAoB,EAAE,CAAC;gBACvB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC;YAED,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;QAC3C,CAAC,CAAC;IACN,CAAC;IAEO,6BAA6B,CAAC,UAAsB,EAAA;QACxD,MAAM,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC;QACvF,OAAO,mBAAwC,KAAM,CAAC,CAA5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAA;IACnD,CAAC;IAEO,kCAAkC,GAAA;QACtC,OAAO,wCAAyC,CAAC;IACrD,CAAC;IAEO,cAAc,CAAC,KAA6B,EAAA;QAChD,6EAA6E;QAC7E,iHAAiH;QACjH,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAC;QAC7H,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,OAAO,EAAE,CAAC;QACd,CAAC;QACD,OAAO,QAAQ,CAAC,sBAAsB,CAAC;IAC3C,CAAC;IAEkB,gBAAgB,GAAA;QAC/B,OAAO,UAAU,CAAC;IACtB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE7C,KAAK,CAAC,YAAY,GAAG,iCAAmE,OAAlC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,GAA6C,CAAzC,AAA0C,EAA1C;QAExF,KAAK,CAAC,kBAAkB,IAAI,UAAuB,OAAb,aAAa,EAAA,GAAI,CAAC;QACxD,KAAK,CAAC,kBAAkB,IAAI,qBAA+C,OAA1B,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAA,cAAe,CAAC;QAC3F,KAAK,CAAC,kBAAkB,IAAI,QAAS,CAAC;QACtC,KAAK,CAAC,kBAAkB,IAAI,oBAAqB,CAAC;QAClD,KAAK,CAAC,kBAAkB,IAAI,SAAU,CAAC;QACvC,KAAK,CAAC,kBAAkB,IAAI,QAAoB,OAAZ,YAAY,EAAA,gBAAiB,CAAC;QAElE,KAAK,CAAC,iBAAiB,IAAI,aAA0B,OAAb,aAAa,EAAA,GAAI,CAAC;QAC1D,KAAK,CAAC,iBAAiB,IAAI,kBAA8B,IAAK,CAAC,EAAlB,YAAY,EAAA;QACzD,KAAK,CAAC,iBAAiB,IAAI,QAAS,CAAC;QACrC,KAAK,CAAC,iBAAiB,IAAI,UAAsB,OAAZ,YAAY,EAAA,IAAK,CAAC;QACvD,KAAK,CAAC,iBAAiB,IAAI,SAAU,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAvGD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;CAkGJ;gKAED,gBAAA,AAAa,EAAC,wCAAwC,EAAE,8BAA8B,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/imageProcessingBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/imageProcessingBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\n\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to add image processing support to fragment shader\r\n */\r\nexport class ImageProcessingBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new ImageProcessingBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerInput(\"color\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Color4);\r\n        this.registerOutput(\"rgb\", NodeMaterialBlockConnectionPointTypes.Color3);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 |\r\n                NodeMaterialBlockConnectionPointTypes.Color4 |\r\n                NodeMaterialBlockConnectionPointTypes.Vector3 |\r\n                NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Defines if the input should be converted to linear space (default: true)\r\n     */\r\n    @editableInPropertyPage(\"Convert input to linear space\", PropertyTypeForEdition.Boolean, \"ADVANCED\")\r\n    public convertInputToLinearSpace: boolean = true;\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ImageProcessingBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the color input component\r\n     */\r\n    public get color(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgb component\r\n     */\r\n    public get rgb(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"exposureLinear\");\r\n        state._excludeVariableName(\"contrast\");\r\n        state._excludeVariableName(\"vInverseScreenSize\");\r\n        state._excludeVariableName(\"vignetteSettings1\");\r\n        state._excludeVariableName(\"vignetteSettings2\");\r\n        state._excludeVariableName(\"vCameraColorCurveNegative\");\r\n        state._excludeVariableName(\"vCameraColorCurveNeutral\");\r\n        state._excludeVariableName(\"vCameraColorCurvePositive\");\r\n        state._excludeVariableName(\"txColorTransform\");\r\n        state._excludeVariableName(\"colorTransformSettings\");\r\n        state._excludeVariableName(\"ditherIntensity\");\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync(state.shaderLanguage);\r\n    }\r\n\r\n    private async _initShaderSourceAsync(shaderLanguage: ShaderLanguage) {\r\n        this._codeIsReady = false;\r\n\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            await Promise.all([\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/helperFunctions\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/imageProcessingDeclaration\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/imageProcessingFunctions\"),\r\n            ]);\r\n        } else {\r\n            await Promise.all([\r\n                import(\"../../../../Shaders/ShadersInclude/helperFunctions\"),\r\n                import(\"../../../../Shaders/ShadersInclude/imageProcessingDeclaration\"),\r\n                import(\"../../../../Shaders/ShadersInclude/imageProcessingFunctions\"),\r\n            ]);\r\n        }\r\n\r\n        this._codeIsReady = true;\r\n        this.onCodeIsReadyObservable.notifyObservers(this);\r\n    }\r\n\r\n    public override isReady(mesh: AbstractMesh, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines) {\r\n        if (defines._areImageProcessingDirty && nodeMaterial.imageProcessingConfiguration) {\r\n            if (!nodeMaterial.imageProcessingConfiguration.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines, nodeMaterial: NodeMaterial) {\r\n        if (defines._areImageProcessingDirty && nodeMaterial.imageProcessingConfiguration) {\r\n            nodeMaterial.imageProcessingConfiguration.prepareDefines(defines);\r\n        }\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        if (!nodeMaterial.imageProcessingConfiguration) {\r\n            return;\r\n        }\r\n\r\n        nodeMaterial.imageProcessingConfiguration.bind(effect);\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        // Register for defines\r\n        state.sharedData.blocksWithDefines.push(this);\r\n\r\n        // Register for blocking\r\n        state.sharedData.blockingBlocks.push(this);\r\n\r\n        // Register for binding\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        // Uniforms\r\n        state.uniforms.push(\"exposureLinear\");\r\n        state.uniforms.push(\"contrast\");\r\n        state.uniforms.push(\"vInverseScreenSize\");\r\n        state.uniforms.push(\"vignetteSettings1\");\r\n        state.uniforms.push(\"vignetteSettings2\");\r\n        state.uniforms.push(\"vCameraColorCurveNegative\");\r\n        state.uniforms.push(\"vCameraColorCurveNeutral\");\r\n        state.uniforms.push(\"vCameraColorCurvePositive\");\r\n        state.uniforms.push(\"txColorTransform\");\r\n        state.uniforms.push(\"colorTransformSettings\");\r\n        state.uniforms.push(\"ditherIntensity\");\r\n\r\n        // Emit code\r\n        const color = this.color;\r\n        const output = this._outputs[0];\r\n        const comments = `//${this.name}`;\r\n        const overrideText = state.shaderLanguage === ShaderLanguage.WGSL ? \"Vec3\" : \"\";\r\n\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n        state._emitFunctionFromInclude(\"imageProcessingDeclaration\", comments);\r\n        state._emitFunctionFromInclude(\"imageProcessingFunctions\", comments);\r\n\r\n        if (color.connectedPoint?.isConnected) {\r\n            if (color.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Color4 || color.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4) {\r\n                state.compilationString += `${state._declareOutput(output)} = ${color.associatedVariableName};\\n`;\r\n            } else {\r\n                state.compilationString += `${state._declareOutput(output)} = vec4${state.fSuffix}(${color.associatedVariableName}, 1.0);\\n`;\r\n            }\r\n            state.compilationString += `#ifdef IMAGEPROCESSINGPOSTPROCESS\\n`;\r\n            if (this.convertInputToLinearSpace) {\r\n                state.compilationString += `${output.associatedVariableName} = vec4${state.fSuffix}(toLinearSpace${overrideText}(${color.associatedVariableName}.rgb), ${color.associatedVariableName}.a);\\n`;\r\n            }\r\n            state.compilationString += `#else\\n`;\r\n            state.compilationString += `#ifdef IMAGEPROCESSING\\n`;\r\n            if (this.convertInputToLinearSpace) {\r\n                state.compilationString += `${output.associatedVariableName} = vec4${state.fSuffix}(toLinearSpace${overrideText}(${color.associatedVariableName}.rgb), ${color.associatedVariableName}.a);\\n`;\r\n            }\r\n            state.compilationString += `${output.associatedVariableName} = applyImageProcessing(${output.associatedVariableName});\\n`;\r\n            state.compilationString += `#endif\\n`;\r\n            state.compilationString += `#endif\\n`;\r\n\r\n            if (this.rgb.hasEndpoints) {\r\n                state.compilationString += state._declareOutput(this.rgb) + ` = ${this.output.associatedVariableName}.xyz;\\n`;\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        codeString += `${this._codeVariableName}.convertInputToLinearSpace = ${this.convertInputToLinearSpace};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.convertInputToLinearSpace = this.convertInputToLinearSpace;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.convertInputToLinearSpace = serializationObject.convertInputToLinearSpace ?? true;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ImageProcessingBlock\", ImageProcessingBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAMhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;;;;;;;AAOhG,MAAO,oBAAqB,0LAAQ,oBAAiB;IA0BvD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACvC,KAAK,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;QACjD,KAAK,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;QAChD,KAAK,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;QAChD,KAAK,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,CAAC;QACxD,KAAK,CAAC,oBAAoB,CAAC,0BAA0B,CAAC,CAAC;QACvD,KAAK,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,CAAC;QACxD,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC/C,KAAK,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;QACrD,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC9C,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,cAA8B,EAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC;;;;aAIjB,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC;;;;aAIjB,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAEe,OAAO,CAAC,IAAkB,EAAE,YAA0B,EAAE,OAA4B,EAAA;QAChG,IAAI,OAAO,CAAC,wBAAwB,IAAI,YAAY,CAAC,4BAA4B,EAAE,CAAC;YAChF,IAAI,CAAC,YAAY,CAAC,4BAA4B,CAAC,OAAO,EAAE,EAAE,CAAC;gBACvD,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAE,YAA0B,EAAA;QACnF,IAAI,OAAO,CAAC,wBAAwB,IAAI,YAAY,CAAC,4BAA4B,EAAE,CAAC;YAChF,YAAY,CAAC,4BAA4B,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,4BAA4B,EAAE,CAAC;YAC7C,OAAO;QACX,CAAC;QAED,YAAY,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3D,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;YAmCpD,KAAK;QAlCT,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,uBAAuB;QACvB,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9C,wBAAwB;QACxB,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,uBAAuB;QACvB,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,WAAW;QACX,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACtC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC1C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAChD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAC9C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEvC,YAAY;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,QAAQ,GAAG,KAAc,CAAE,CAAC,KAAZ,IAAI,CAAC,IAAI;QAC/B,MAAM,YAAY,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;QAEhF,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAC5D,KAAK,CAAC,wBAAwB,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;QACvE,KAAK,CAAC,wBAAwB,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QAErE,mCAAU,cAAc,gFAAE,WAAW,EAAE,CAAC;YACpC,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,kNAAK,yCAAqC,CAAC,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,OAAO,EAAE,CAAC;gBAC5J,KAAK,CAAC,iBAAiB,IAAI,UAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,EAAA,OAAkC,OAA5B,KAAK,CAAC,sBAAsB,EAAA,IAAK,CAAC;YACtG,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,iBAAiB,IAAI,UAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,EAAA,WAA2B,KAAK,EAAtB,KAAK,CAAC,OAAO,EAAA,KAAgC,aAAtB,sBAAsB,EAAA,UAAW,CAAC;YACjI,CAAC;YACD,KAAK,CAAC,iBAAiB,IAAI,oCAAqC,CAAC;YACjE,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACjC,KAAK,CAAC,iBAAiB,IAAI,UAAG,MAAM,CAAC,sBAAsB,EAAA,kBAAU,KAAK,CAAC,OAAO,EAAA,kBAAiC,KAAK,EAArB,YAAY,EAAA,kBAAU,sBAAsB,EAAA,WAAsC,OAA5B,AAAoC,CAAC,IAAhC,CAAC,sBAAsB,EAAA;YACzL,CAAC;YACD,KAAK,CAAC,iBAAiB,IAAI,QAAS,CAAC;YACrC,KAAK,CAAC,iBAAiB,IAAI,yBAA0B,CAAC;YACtD,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACjC,KAAK,CAAC,iBAAiB,IAAI,GAA0C,KAAK,EAA5C,MAAM,CAAC,sBAAsB,EAAA,WAAwC,YAAY,CAApC,OAAO,EAAA,uCAA6B,YAAI,KAAK,CAAC,sBAAsB,EAAA,WAAsC,OAA5B,KAAK,CAAC,sBAAsB,EAAA,OAAQ,CAAC;YAClM,CAAC;YACD,KAAK,CAAC,iBAAiB,IAAI,GAA2D,MAAM,CAA9D,MAAM,CAAC,sBAAsB,EAAA,4BAAwD,cAAtB,sBAAsB,EAAA,KAAM,CAAC;YAC1H,KAAK,CAAC,iBAAiB,IAAI,SAAU,CAAC;YACtC,KAAK,CAAC,iBAAiB,IAAI,SAAU,CAAC;YAEtC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;gBACxB,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAwC,OAAlC,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAA,QAAS,CAAC;YAClH,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,UAAU,IAAI,UAAG,IAAI,CAAC,iBAAiB,EAAA,iCAA8D,OAA9B,IAAI,CAAC,yBAAyB,EAAA,IAAK,CAAC;QAE3G,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAE/E,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;;QAExD,IAAI,CAAC,yBAAyB,yEAAuB,yBAAyB,0EAA7C,mBAAmB,8BAA8B,IAAI,CAAC;IAC3F,CAAC;IA5MD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAcnD;;WAEG,CAEI,IAAA,CAAA,yBAAyB,GAAY,IAAI,CAAC;QAhB7C,IAAI,CAAC,aAAa,CAAC,OAAO,gNAAE,wCAAqC,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,CAAC,cAAc,CAAC,KAAK,gNAAE,wCAAqC,CAAC,MAAM,CAAC,CAAC;QAEzE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MACtD,wCAAqC,CAAC,MAAM,iNACxC,wCAAqC,CAAC,MAAM,iNAC5C,wCAAqC,CAAC,OAAO,iNAC7C,wCAAqC,CAAC,OAAO,CACpD,CAAC;IACN,CAAC;CA4LJ;2JAtLU,aAAA,EAAA;8KADN,yBAAA,AAAsB,EAAC,+BAA+B,EAAA,EAAA,kCAAA,KAAkC,UAAU,CAAC;uEACnD;gKAwLrD,gBAAA,AAAa,EAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/TBNBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/TBNBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport { ShaderLanguage } from \"../../../../Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to implement TBN matrix\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class TBNBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new TBNBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment, true);\r\n\r\n        this.registerInput(\"normal\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false);\r\n        this.normal.addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color4 | NodeMaterialBlockConnectionPointTypes.Vector4 | NodeMaterialBlockConnectionPointTypes.Vector3\r\n        );\r\n        this.registerInput(\"tangent\", NodeMaterialBlockConnectionPointTypes.Vector4, false);\r\n        this.registerInput(\"world\", NodeMaterialBlockConnectionPointTypes.Matrix, false);\r\n\r\n        this.registerOutput(\r\n            \"TBN\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.Fragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"TBN\", this, NodeMaterialConnectionPointDirection.Output, TBNBlock, \"TBNBlock\")\r\n        );\r\n\r\n        this.registerOutput(\"row0\", NodeMaterialBlockConnectionPointTypes.Vector3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"row1\", NodeMaterialBlockConnectionPointTypes.Vector3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"row2\", NodeMaterialBlockConnectionPointTypes.Vector3, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"TBNBlock\";\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"tbnNormal\");\r\n        state._excludeVariableName(\"tbnTangent\");\r\n        state._excludeVariableName(\"tbnBitangent\");\r\n        state._excludeVariableName(\"TBN\");\r\n    }\r\n\r\n    /**\r\n     * Gets the normal input component\r\n     */\r\n    public get normal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the tangent input component\r\n     */\r\n    public get tangent(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the world matrix input component\r\n     */\r\n    public get world(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the TBN output component\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get TBN(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the row0 of the output matrix\r\n     */\r\n    public get row0(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the row1 of the output matrix\r\n     */\r\n    public get row1(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the row2 of the output matrix\r\n     */\r\n    public get row2(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    public override get target() {\r\n        return NodeMaterialBlockTargets.Fragment;\r\n    }\r\n\r\n    public override set target(value: NodeMaterialBlockTargets) {}\r\n\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.world.isConnected) {\r\n            let worldInput = material.getInputBlockByPredicate((b) => b.isSystemValue && b.systemValue === NodeMaterialSystemValues.World && additionalFilteringInfo(b));\r\n\r\n            if (!worldInput) {\r\n                worldInput = new InputBlock(\"world\");\r\n                worldInput.setAsSystemValue(NodeMaterialSystemValues.World);\r\n            }\r\n            worldInput.output.connectTo(this.world);\r\n        }\r\n\r\n        if (!this.normal.isConnected) {\r\n            let normalInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"normal\" && additionalFilteringInfo(b));\r\n\r\n            if (!normalInput) {\r\n                normalInput = new InputBlock(\"normal\");\r\n                normalInput.setAsAttribute(\"normal\");\r\n            }\r\n            normalInput.output.connectTo(this.normal);\r\n        }\r\n\r\n        if (!this.tangent.isConnected) {\r\n            let tangentInput = material.getInputBlockByPredicate(\r\n                (b) => b.isAttribute && b.name === \"tangent\" && b.type === NodeMaterialBlockConnectionPointTypes.Vector4 && additionalFilteringInfo(b)\r\n            );\r\n\r\n            if (!tangentInput) {\r\n                tangentInput = new InputBlock(\"tangent\");\r\n                tangentInput.setAsAttribute(\"tangent\");\r\n            }\r\n            tangentInput.output.connectTo(this.tangent);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines, nodeMaterial: NodeMaterial, mesh?: AbstractMesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        const normal = this.normal;\r\n        const tangent = this.tangent;\r\n\r\n        let normalAvailable = normal.isConnected;\r\n        if (normal.connectInputBlock?.isAttribute && !mesh.isVerticesDataPresent(normal.connectInputBlock?.name)) {\r\n            normalAvailable = false;\r\n        }\r\n\r\n        let tangentAvailable = tangent.isConnected;\r\n        if (tangent.connectInputBlock?.isAttribute && !mesh.isVerticesDataPresent(tangent.connectInputBlock?.name)) {\r\n            tangentAvailable = false;\r\n        }\r\n\r\n        const useTBNBlock = normalAvailable && tangentAvailable;\r\n\r\n        defines.setValue(\"TBNBLOCK\", useTBNBlock, true);\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const normal = this.normal;\r\n        const tangent = this.tangent;\r\n        const world = this.world;\r\n        const tbn = this.TBN;\r\n        const row0 = this.row0;\r\n        const row1 = this.row1;\r\n        const row2 = this.row2;\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        const mat3 = isWebGPU ? \"mat3x3f\" : \"mat3\";\r\n        const fSuffix = isWebGPU ? \"f\" : \"\";\r\n\r\n        // Fragment\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `\r\n                // ${this.name}\r\n                ${state._declareLocalVar(\"tbnNormal\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${normal.associatedVariableName}).xyz;\r\n                ${state._declareLocalVar(\"tbnTangent\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${tangent.associatedVariableName}.xyz);\r\n                ${state._declareLocalVar(\"tbnBitangent\", NodeMaterialBlockConnectionPointTypes.Vector3)} = cross(tbnNormal, tbnTangent) * ${tangent.associatedVariableName}.w;\r\n                ${isWebGPU ? \"var\" : \"mat3\"} ${tbn.associatedVariableName} = ${mat3}(${world.associatedVariableName}[0].xyz, ${world.associatedVariableName}[1].xyz, ${world.associatedVariableName}[2].xyz) * ${mat3}(tbnTangent, tbnBitangent, tbnNormal);\r\n            `;\r\n\r\n            if (row0.hasEndpoints) {\r\n                state.compilationString +=\r\n                    state._declareOutput(row0) +\r\n                    ` = vec3${fSuffix}(${tbn.associatedVariableName}[0][0], ${tbn.associatedVariableName}[0][1], ${tbn.associatedVariableName}[0][2]);\\n`;\r\n            }\r\n            if (row1.hasEndpoints) {\r\n                state.compilationString +=\r\n                    state._declareOutput(row1) +\r\n                    ` = vec3${fSuffix}(${tbn.associatedVariableName}[1[0], ${tbn.associatedVariableName}[1][1], ${tbn.associatedVariableName}[1][2]);\\n`;\r\n            }\r\n            if (row2.hasEndpoints) {\r\n                state.compilationString +=\r\n                    state._declareOutput(row2) +\r\n                    ` = vec3${fSuffix}(${tbn.associatedVariableName}[2][0], ${tbn.associatedVariableName}[2][1], ${tbn.associatedVariableName}[2][2]);\\n`;\r\n            }\r\n\r\n            state.sharedData.blocksWithDefines.push(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TBNBlock\", TBNBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAGhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AAExG,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;;;;;;;;AAQ3C,MAAO,QAAS,0LAAQ,oBAAiB;IA2B3C;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACxC,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QACzC,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,gEAAgE;IAChE,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED,IAAoB,MAAM,GAAA;QACtB,wMAAO,2BAAwB,CAAC,QAAQ,CAAC;IAC7C,CAAC;IAED,IAAoB,MAAM,CAAC,KAA+B,EAAA,CAAG,CAAC;IAE9C,aAAa,CAAC,QAAsB,EAA4E;sCAA1E,iEAAgE,GAAG,CAAG,CAAD,GAAK;QAC5H,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC1B,IAAI,UAAU,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,aAAa,IAAI,CAAC,CAAC,WAAW,sMAAK,2BAAwB,CAAC,KAAK,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE7J,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,UAAU,GAAG,gMAAI,cAAU,CAAC,OAAO,CAAC,CAAC;gBACrC,UAAU,CAAC,gBAAgB,kMAAC,2BAAwB,CAAC,KAAK,CAAC,CAAC;YAChE,CAAC;YACD,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC3B,IAAI,WAAW,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/H,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,WAAW,GAAG,iMAAI,aAAU,CAAC,QAAQ,CAAC,CAAC;gBACvC,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC;YACD,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5B,IAAI,YAAY,GAAG,QAAQ,CAAC,wBAAwB,CAChD,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,IAAI,KAAK,sPAAqC,CAAC,OAAO,IAAI,uBAAuB,CAAC,CAAC,CAAC,CACzI,CAAC;YAEF,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,YAAY,GAAG,iMAAI,aAAU,CAAC,SAAS,CAAC,CAAC;gBACzC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;YACD,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAE,YAA0B,EAAE,IAAmB,EAAA;;QACxG,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAE7B,IAAI,eAAe,GAAG,MAAM,CAAC,WAAW,CAAC;QACzC,yCAAW,iBAAiB,8DAAxB,MAAM,oBAAoB,WAAW,KAAI,CAAC,IAAI,CAAC,qBAAqB,sCAAQ,iBAAiB,+DAAxB,MAAM,qBAAoB,IAAI,CAAC,EAAE,CAAC;YACvG,eAAe,GAAG,KAAK,CAAC;QAC5B,CAAC;QAED,IAAI,gBAAgB,GAAG,OAAO,CAAC,WAAW,CAAC;QAC3C,2CAAY,iBAAiB,cAAzB,OAAO,qEAAoB,WAAW,KAAI,CAAC,IAAI,CAAC,qBAAqB,gCAAC,OAAO,CAAC,iBAAiB,4FAAE,IAAI,CAAC,EAAE,CAAC;YACzG,gBAAgB,GAAG,KAAK,CAAC;QAC7B,CAAC;QAED,MAAM,WAAW,GAAG,eAAe,IAAI,gBAAgB,CAAC;QAExD,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;QAC3C,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAEpC,WAAW;QACX,IAAI,KAAK,CAAC,MAAM,sMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,KAAK,CAAC,iBAAiB,IAAI,wBAErB,KAAK,EADF,IAAI,CAAC,IAAI,EAAA,sBACsF,MAAM,OAAlG,gBAAgB,CAAC,WAAW,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,+BAAuB,sBAAsB,EAAA,mCAC/H,KAAK,CAAC,gBAAgB,CAAC,YAAY,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,wBAAgB,OAAO,CAAC,sBAAsB,EAAA,mCACjI,KAAK,CAAC,gBAAgB,CAAC,cAAc,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,6CAAqC,OAAO,CAAC,sBAAsB,EAAA,gCACxJ,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAA,YAAI,GAAG,CAAC,sBAAsB,EAAA,cAAM,IAAI,EAAA,YAAI,KAAK,CAAC,sBAAsB,EAAA,aAAoD,KAAK,EAA7C,KAAK,CAAC,sBAAsB,EAAA,aAAsD,IAAI,SAAxC,sBAAsB,EAAA,eAAkB,aAAA;YAGzM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,KAAK,CAAC,iBAAiB,IACnB,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAC1B,UAAqB,GAAG,IAAd,OAAO,EAAA,gBAAQ,sBAAsB,EAAA,YAAgD,GAAG,IAAxC,GAAG,CAAC,sBAAsB,EAAA,YAAqC,WAAtB,AAAkC,CAAC,qBAAb,EAAA;YACjI,CAAC;YACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,KAAK,CAAC,iBAAiB,IACnB,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAC1B,iBAAU,OAAO,EAAA,YAAI,GAAG,CAAC,sBAAsB,EAAA,kBAAU,GAAG,CAAC,sBAAsB,EAAA,YAAqC,OAA1B,GAAG,CAAC,AAAkC,CAAC,qBAAb,EAAA;YAChI,CAAC;YACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,KAAK,CAAC,iBAAiB,IACnB,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAC1B,iBAAU,OAAO,EAAA,YAAI,GAAG,CAAC,sBAAsB,EAAA,YAAgD,GAAG,IAAxC,GAAG,CAAC,sBAAsB,EAAA,YAAqC,WAAtB,AAAkC,CAAC,qBAAb,EAAA;YACjI,CAAC;YAED,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IA1MD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,aAAa,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACtF,IAAI,CAAC,MAAM,CAAC,0CAA0C,8MAClD,yCAAqC,CAAC,MAAM,iNAAG,wCAAqC,CAAC,OAAO,iNAAG,wCAAqC,CAAC,OAAO,CAC/I,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,SAAS,gNAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpF,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,sPAAqC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAEjF,IAAI,CAAC,cAAc,CACf,KAAK,gNACL,wCAAqC,CAAC,MAAM,mMAC5C,2BAAwB,CAAC,QAAQ,EACjC,2MAAI,0CAAuC,CAAC,KAAK,EAAE,IAAI,EAAA,EAAA,+CAAA,KAA+C,QAAQ,EAAE,UAAU,CAAC,CAC9H,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,MAAM,gNAAE,wCAAqC,CAAC,OAAO,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9G,IAAI,CAAC,cAAc,CAAC,MAAM,gNAAE,wCAAqC,CAAC,OAAO,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9G,IAAI,CAAC,cAAc,CAAC,MAAM,gNAAE,wCAAqC,CAAC,OAAO,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;IAClH,CAAC;CAmLJ;gKAED,gBAAA,AAAa,EAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/perturbNormalBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/perturbNormalBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { TextureBlock } from \"../Dual/textureBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { TBNBlock } from \"./TBNBlock\";\r\n\r\nimport { ShaderLanguage } from \"../../../../Materials/shaderLanguage\";\r\nimport { Constants } from \"../../../../Engines/constants\";\r\n\r\n/**\r\n * Block used to perturb normals based on a normal map\r\n */\r\nexport class PerturbNormalBlock extends NodeMaterialBlock {\r\n    private _tangentSpaceParameterName = \"\";\r\n    private _tangentCorrectionFactorName = \"\";\r\n    private _worldMatrixName = \"\";\r\n\r\n    /** Gets or sets a boolean indicating that normal should be inverted on X axis */\r\n    @editableInPropertyPage(\"Invert X axis\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { embedded: true, notifiers: { update: true } })\r\n    public invertX = false;\r\n    /** Gets or sets a boolean indicating that normal should be inverted on Y axis */\r\n    @editableInPropertyPage(\"Invert Y axis\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { embedded: true, notifiers: { update: true } })\r\n    public invertY = false;\r\n    /** Gets or sets a boolean indicating that parallax occlusion should be enabled */\r\n    @editableInPropertyPage(\"Use parallax occlusion\", PropertyTypeForEdition.Boolean, undefined, { embedded: true })\r\n    public useParallaxOcclusion = false;\r\n    /** Gets or sets a boolean indicating that sampling mode is in Object space */\r\n    @editableInPropertyPage(\"Object Space Mode\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { embedded: true, notifiers: { update: true } })\r\n    public useObjectSpaceNormalMap = false;\r\n\r\n    /**\r\n     * Create a new PerturbNormalBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        // Vertex\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.Vector4, false);\r\n        this.registerInput(\"worldTangent\", NodeMaterialBlockConnectionPointTypes.Vector4, true);\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.Vector2, false);\r\n        this.registerInput(\"normalMapColor\", NodeMaterialBlockConnectionPointTypes.Color3, false);\r\n        this.registerInput(\"strength\", NodeMaterialBlockConnectionPointTypes.Float, false);\r\n        this.registerInput(\"viewDirection\", NodeMaterialBlockConnectionPointTypes.Vector3, true);\r\n        this.registerInput(\"parallaxScale\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"parallaxHeight\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\r\n            \"TBN\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"TBN\", this, NodeMaterialConnectionPointDirection.Input, TBNBlock, \"TBNBlock\")\r\n        );\r\n        this.registerInput(\"world\", NodeMaterialBlockConnectionPointTypes.Matrix, true);\r\n\r\n        // Fragment\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.registerOutput(\"uvOffset\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"PerturbNormalBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the world tangent input component\r\n     */\r\n    public get worldTangent(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the normal map color input component\r\n     */\r\n    public get normalMapColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the strength input component\r\n     */\r\n    public get strength(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the view direction input component\r\n     */\r\n    public get viewDirection(): NodeMaterialConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the parallax scale input component\r\n     */\r\n    public get parallaxScale(): NodeMaterialConnectionPoint {\r\n        return this._inputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the parallax height input component\r\n     */\r\n    public get parallaxHeight(): NodeMaterialConnectionPoint {\r\n        return this._inputs[8];\r\n    }\r\n\r\n    /**\r\n     * Gets the TBN input component\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get TBN(): NodeMaterialConnectionPoint {\r\n        return this._inputs[9];\r\n    }\r\n\r\n    /**\r\n     * Gets the World input component\r\n     */\r\n    public get world(): NodeMaterialConnectionPoint {\r\n        return this._inputs[10];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the uv offset output component\r\n     */\r\n    public get uvOffset(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync(state.shaderLanguage);\r\n    }\r\n\r\n    private async _initShaderSourceAsync(shaderLanguage: ShaderLanguage) {\r\n        this._codeIsReady = false;\r\n\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            await Promise.all([\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/bumpFragment\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/bumpFragmentMainFunctions\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/bumpFragmentFunctions\"),\r\n            ]);\r\n        } else {\r\n            await Promise.all([\r\n                import(\"../../../../Shaders/ShadersInclude/bumpFragment\"),\r\n                import(\"../../../../Shaders/ShadersInclude/bumpFragmentMainFunctions\"),\r\n                import(\"../../../../Shaders/ShadersInclude/bumpFragmentFunctions\"),\r\n            ]);\r\n        }\r\n\r\n        this._codeIsReady = true;\r\n        this.onCodeIsReadyObservable.notifyObservers(this);\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines, nodeMaterial: NodeMaterial) {\r\n        const normalSamplerName = (this.normalMapColor.connectedPoint!._ownerBlock as TextureBlock).samplerName;\r\n        const useParallax = this.viewDirection.isConnected && ((this.useParallaxOcclusion && normalSamplerName) || (!this.useParallaxOcclusion && this.parallaxHeight.isConnected));\r\n\r\n        defines.setValue(\"BUMP\", true);\r\n        defines.setValue(\"PARALLAX\", useParallax, true);\r\n        defines.setValue(\"PARALLAX_RHS\", nodeMaterial.getScene().useRightHandedSystem, true);\r\n        defines.setValue(\"PARALLAXOCCLUSION\", this.useParallaxOcclusion, true);\r\n        defines.setValue(\"OBJECTSPACE_NORMALMAP\", this.useObjectSpaceNormalMap, true);\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        if (nodeMaterial.getScene()._mirroredCameraPosition) {\r\n            effect.setFloat2(this._tangentSpaceParameterName, this.invertX ? 1.0 : -1.0, this.invertY ? 1.0 : -1.0);\r\n        } else {\r\n            effect.setFloat2(this._tangentSpaceParameterName, this.invertX ? -1.0 : 1.0, this.invertY ? -1.0 : 1.0);\r\n        }\r\n\r\n        if (mesh) {\r\n            effect.setFloat(this._tangentCorrectionFactorName, mesh.getWorldMatrix().determinant() < 0 ? -1 : 1);\r\n\r\n            if (this.useObjectSpaceNormalMap && !this.world.isConnected) {\r\n                // World default to the mesh world matrix\r\n                effect.setMatrix(this._worldMatrixName, mesh.getWorldMatrix());\r\n            }\r\n        }\r\n    }\r\n\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.uv.isConnected) {\r\n            let uvInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"uv\" && additionalFilteringInfo(b));\r\n\r\n            if (!uvInput) {\r\n                uvInput = new InputBlock(\"uv\");\r\n                uvInput.setAsAttribute();\r\n            }\r\n            uvInput.output.connectTo(this.uv);\r\n        }\r\n\r\n        if (!this.strength.isConnected) {\r\n            const strengthInput = new InputBlock(\"strength\");\r\n            strengthInput.value = 1.0;\r\n            strengthInput.output.connectTo(this.strength);\r\n        }\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const comments = `//${this.name}`;\r\n        const uv = this.uv;\r\n        const worldPosition = this.worldPosition;\r\n        const worldNormal = this.worldNormal;\r\n        const worldTangent = this.worldTangent;\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        const mat3 = isWebGPU ? \"mat3x3f\" : \"mat3\";\r\n        const fSuffix = isWebGPU ? \"f\" : \"\";\r\n        const uniformPrefix = isWebGPU ? \"uniforms.\" : \"\";\r\n        const fragmentInputsPrefix = isWebGPU ? \"fragmentInputs.\" : \"\";\r\n\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        this._tangentSpaceParameterName = state._getFreeDefineName(\"tangentSpaceParameter\");\r\n\r\n        state._emitUniformFromString(this._tangentSpaceParameterName, NodeMaterialBlockConnectionPointTypes.Vector2);\r\n\r\n        this._tangentCorrectionFactorName = state._getFreeDefineName(\"tangentCorrectionFactor\");\r\n\r\n        state._emitUniformFromString(this._tangentCorrectionFactorName, NodeMaterialBlockConnectionPointTypes.Float);\r\n\r\n        this._worldMatrixName = state._getFreeDefineName(\"perturbNormalWorldMatrix\");\r\n\r\n        state._emitUniformFromString(this._worldMatrixName, NodeMaterialBlockConnectionPointTypes.Matrix);\r\n\r\n        let normalSamplerName = null;\r\n        if (this.normalMapColor.connectedPoint) {\r\n            normalSamplerName = (this.normalMapColor.connectedPoint._ownerBlock as TextureBlock).samplerName;\r\n        }\r\n        const useParallax = this.viewDirection.isConnected && ((this.useParallaxOcclusion && normalSamplerName) || (!this.useParallaxOcclusion && this.parallaxHeight.isConnected));\r\n\r\n        const replaceForParallaxInfos = !this.parallaxScale.isConnectedToInputBlock\r\n            ? \"0.05\"\r\n            : this.parallaxScale.connectInputBlock!.isConstant\r\n              ? state._emitFloat(this.parallaxScale.connectInputBlock!.value)\r\n              : this.parallaxScale.associatedVariableName;\r\n\r\n        const replaceForBumpInfos =\r\n            this.strength.isConnectedToInputBlock && this.strength.connectInputBlock!.isConstant\r\n                ? `\\n#if !defined(NORMALXYSCALE)\\n1.0/\\n#endif\\n${state._emitFloat(this.strength.connectInputBlock!.value)}`\r\n                : `\\n#if !defined(NORMALXYSCALE)\\n1.0/\\n#endif\\n${this.strength.associatedVariableName}`;\r\n\r\n        if (!isWebGPU) {\r\n            state._emitExtension(\"derivatives\", \"#extension GL_OES_standard_derivatives : enable\");\r\n        }\r\n\r\n        const tangentReplaceString = { search: /defined\\(TANGENT\\)/g, replace: worldTangent.isConnected ? \"defined(TANGENT)\" : \"defined(IGNORE)\" };\r\n        const tbnVarying = { search: /varying mat3 vTBN;/g, replace: \"\" };\r\n        const normalMatrixReplaceString = { search: isWebGPU ? /uniform normalMatrix: mat4x4f;/g : /uniform mat4 normalMatrix;/g, replace: \"\" };\r\n\r\n        const tbn = this.TBN;\r\n        if (tbn.isConnected) {\r\n            state.compilationString += `\r\n            #ifdef TBNBLOCK\r\n            ${isWebGPU ? \"var\" : \"mat3\"} vTBN = ${tbn.associatedVariableName};\r\n            #endif\r\n            `;\r\n        } else if (worldTangent.isConnected) {\r\n            state.compilationString += `${state._declareLocalVar(\"tbnNormal\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${worldNormal.associatedVariableName}.xyz);\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"tbnTangent\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${worldTangent.associatedVariableName}.xyz);\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"tbnBitangent\", NodeMaterialBlockConnectionPointTypes.Vector3)} = cross(tbnNormal, tbnTangent) * ${uniformPrefix}${this._tangentCorrectionFactorName};\\n`;\r\n            state.compilationString += `${isWebGPU ? \"var\" : \"mat3\"} vTBN = ${mat3}(tbnTangent, tbnBitangent, tbnNormal);\\n`;\r\n        }\r\n\r\n        let replaceStrings = [tangentReplaceString, tbnVarying, normalMatrixReplaceString];\r\n\r\n        if (isWebGPU) {\r\n            replaceStrings.push({ search: /varying vTBN0: vec3f;/g, replace: \"\" });\r\n            replaceStrings.push({ search: /varying vTBN1: vec3f;/g, replace: \"\" });\r\n            replaceStrings.push({ search: /varying vTBN2: vec3f;/g, replace: \"\" });\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"bumpFragmentMainFunctions\", comments, {\r\n            replaceStrings: replaceStrings,\r\n        });\r\n\r\n        const replaceString0 = isWebGPU\r\n            ? \"fn parallaxOcclusion(vViewDirCoT: vec3f, vNormalCoT: vec3f, texCoord: vec2f, parallaxScale:f32, bumpSampler: texture_2d<f32>, bumpSamplerSampler: sampler)\"\r\n            : \"#define inline\\nvec2 parallaxOcclusion(vec3 vViewDirCoT, vec3 vNormalCoT, vec2 texCoord, float parallaxScale, sampler2D bumpSampler)\";\r\n\r\n        const searchExp0 = isWebGPU\r\n            ? /fn parallaxOcclusion\\(vViewDirCoT: vec3f,vNormalCoT: vec3f,texCoord: vec2f,parallaxScale: f32\\)/g\r\n            : /vec2 parallaxOcclusion\\(vec3 vViewDirCoT,vec3 vNormalCoT,vec2 texCoord,float parallaxScale\\)/g;\r\n\r\n        const replaceString1 = isWebGPU\r\n            ? \"fn parallaxOffset(viewDir: vec3f, heightScale: f32, height_: f32)\"\r\n            : \"vec2 parallaxOffset(vec3 viewDir, float heightScale, float height_)\";\r\n\r\n        const searchExp1 = isWebGPU ? /fn parallaxOffset\\(viewDir: vec3f,heightScale: f32\\)/g : /vec2 parallaxOffset\\(vec3 viewDir,float heightScale\\)/g;\r\n\r\n        state._emitFunctionFromInclude(\"bumpFragmentFunctions\", comments, {\r\n            replaceStrings: [\r\n                { search: /#include<samplerFragmentDeclaration>\\(_DEFINENAME_,BUMP,_VARYINGNAME_,Bump,_SAMPLERNAME_,bump\\)/g, replace: \"\" },\r\n                { search: /uniform sampler2D bumpSampler;/g, replace: \"\" },\r\n                {\r\n                    search: searchExp0,\r\n                    replace: replaceString0,\r\n                },\r\n                { search: searchExp1, replace: replaceString1 },\r\n                { search: /texture.+?bumpSampler,.*?vBumpUV\\)\\.w/g, replace: \"height_\" },\r\n            ],\r\n        });\r\n\r\n        const normalRead = isWebGPU ? `textureSample(${normalSamplerName}, ${normalSamplerName + Constants.AUTOSAMPLERSUFFIX}` : `texture2D(${normalSamplerName}`;\r\n\r\n        const uvForPerturbNormal = !useParallax || !normalSamplerName ? this.normalMapColor.associatedVariableName : `${normalRead}, ${uv.associatedVariableName} + uvOffset).xyz`;\r\n\r\n        const tempOutput = state._getFreeVariableName(\"tempOutput\");\r\n        state.compilationString += state._declareLocalVar(tempOutput, NodeMaterialBlockConnectionPointTypes.Vector3) + ` = vec3${fSuffix}(0.);\\n`;\r\n\r\n        replaceStrings = [\r\n            { search: new RegExp(`texture.+?bumpSampler${isWebGPU ? \"Sampler,fragmentInputs.\" : \",\"}vBumpUV\\\\)`, \"g\"), replace: `${uvForPerturbNormal}` },\r\n            {\r\n                search: /#define CUSTOM_FRAGMENT_BUMP_FRAGMENT/g,\r\n                replace: `${state._declareLocalVar(\"normalMatrix\", NodeMaterialBlockConnectionPointTypes.Matrix)} = toNormalMatrix(${this.world.isConnected ? this.world.associatedVariableName : uniformPrefix + this._worldMatrixName});`,\r\n            },\r\n            {\r\n                search: new RegExp(\r\n                    `perturbNormal\\\\(TBN,texture.+?bumpSampler${isWebGPU ? \"Sampler,fragmentInputs.\" : \",\"}vBumpUV\\\\+uvOffset\\\\).xyz,${uniformPrefix}vBumpInfos.y\\\\)`,\r\n                    \"g\"\r\n                ),\r\n                replace: `perturbNormal(TBN, ${uvForPerturbNormal}, ${uniformPrefix}vBumpInfos.y)`,\r\n            },\r\n            {\r\n                search: /parallaxOcclusion\\(invTBN\\*-viewDirectionW,invTBN\\*normalW,(fragmentInputs\\.)?vBumpUV,(uniforms\\.)?vBumpInfos.z\\)/g,\r\n                replace: `parallaxOcclusion((invTBN * -viewDirectionW), (invTBN * normalW), ${fragmentInputsPrefix}vBumpUV, ${uniformPrefix}vBumpInfos.z, ${\r\n                    isWebGPU\r\n                        ? useParallax && this.useParallaxOcclusion\r\n                            ? `${normalSamplerName}, ${normalSamplerName + Constants.AUTOSAMPLERSUFFIX}`\r\n                            : \"bump, bumpSampler\"\r\n                        : useParallax && this.useParallaxOcclusion\r\n                          ? normalSamplerName\r\n                          : \"bumpSampler\"\r\n                })`,\r\n            },\r\n            {\r\n                search: /parallaxOffset\\(invTBN\\*viewDirectionW,vBumpInfos\\.z\\)/g,\r\n                replace: `parallaxOffset(invTBN * viewDirectionW, ${uniformPrefix}vBumpInfos.z, ${useParallax ? this.parallaxHeight.associatedVariableName : \"0.\"})`,\r\n            },\r\n            { search: isWebGPU ? /uniforms.vBumpInfos.y/g : /vBumpInfos.y/g, replace: replaceForBumpInfos },\r\n            { search: isWebGPU ? /uniforms.vBumpInfos.z/g : /vBumpInfos.z/g, replace: replaceForParallaxInfos },\r\n            { search: /normalW=/g, replace: tempOutput + \" = \" },\r\n            isWebGPU\r\n                ? {\r\n                      search: /mat3x3f\\(uniforms\\.normalMatrix\\[0\\].xyz,uniforms\\.normalMatrix\\[1\\]\\.xyz,uniforms\\.normalMatrix\\[2\\].xyz\\)\\*normalW/g,\r\n                      replace: `${mat3}(normalMatrix[0].xyz, normalMatrix[1].xyz, normalMatrix[2].xyz) * ` + tempOutput,\r\n                  }\r\n                : {\r\n                      search: /mat3\\(normalMatrix\\)\\*normalW/g,\r\n                      replace: `${mat3}(normalMatrix) * ` + tempOutput,\r\n                  },\r\n            { search: /normalW/g, replace: worldNormal.associatedVariableName + \".xyz\" },\r\n            { search: /viewDirectionW/g, replace: useParallax ? this.viewDirection.associatedVariableName : `vec3${fSuffix}(0.)` },\r\n            tangentReplaceString,\r\n        ];\r\n\r\n        if (isWebGPU) {\r\n            replaceStrings.push({ search: /fragmentInputs.vBumpUV/g, replace: uv.associatedVariableName });\r\n            replaceStrings.push({ search: /input.vPositionW/g, replace: worldPosition.associatedVariableName + \".xyz\" });\r\n            replaceStrings.push({ search: /uniforms.vTangentSpaceParams/g, replace: uniformPrefix + this._tangentSpaceParameterName });\r\n            replaceStrings.push({ search: /var TBN: mat3x3f=mat3x3<f32>\\(input.vTBN0,input.vTBN1,input.vTBN2\\);/g, replace: `var TBN = vTBN;` });\r\n        } else {\r\n            replaceStrings.push({ search: /vBumpUV/g, replace: uv.associatedVariableName });\r\n            replaceStrings.push({ search: /vPositionW/g, replace: worldPosition.associatedVariableName + \".xyz\" });\r\n            replaceStrings.push({ search: /vTangentSpaceParams/g, replace: uniformPrefix + this._tangentSpaceParameterName });\r\n        }\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"bumpFragment\", comments, {\r\n            replaceStrings: replaceStrings,\r\n        });\r\n\r\n        state.compilationString += state._declareOutput(this.output) + ` = vec4${fSuffix}(${tempOutput}, 0.);\\n`;\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode() + `${this._codeVariableName}.invertX = ${this.invertX};\\n`;\r\n\r\n        codeString += `${this._codeVariableName}.invertY = ${this.invertY};\\n`;\r\n        codeString += `${this._codeVariableName}.useParallaxOcclusion = ${this.useParallaxOcclusion};\\n`;\r\n        codeString += `${this._codeVariableName}.useObjectSpaceNormalMap = ${this.useObjectSpaceNormalMap};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.invertX = this.invertX;\r\n        serializationObject.invertY = this.invertY;\r\n        serializationObject.useParallaxOcclusion = this.useParallaxOcclusion;\r\n        serializationObject.useObjectSpaceNormalMap = this.useObjectSpaceNormalMap;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.invertX = serializationObject.invertX;\r\n        this.invertY = serializationObject.invertY;\r\n        this.useParallaxOcclusion = !!serializationObject.useParallaxOcclusion;\r\n        this.useObjectSpaceNormalMap = !!serializationObject.useObjectSpaceNormalMap;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PerturbNormalBlock\", PerturbNormalBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAGhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAG3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AAEtG,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;;;;;;;;;;AAQhC,MAAO,kBAAmB,0LAAQ,oBAAiB;IAmDrD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,gEAAgE;IAChE,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,UAAU,CAAC,KAA6B,EAAA;QACpD,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,cAA8B,EAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC;;;;aAIjB,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC;;;;aAIjB,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAE,YAA0B,EAAA;QACnF,MAAM,iBAAiB,GAAI,IAAI,CAAC,cAAc,CAAC,cAAe,CAAC,WAA4B,CAAC,WAAW,CAAC;QACxG,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,AAAC,IAAI,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,GAAK,CAAD,AAAE,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,AAAC,CAAC,CAAC;QAE5K,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC/B,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAChD,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,YAAY,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACrF,OAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QACvE,OAAO,CAAC,QAAQ,CAAC,uBAAuB,EAAE,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;IAClF,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC,uBAAuB,EAAE,CAAC;YAClD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5G,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5G,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAErG,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC1D,yCAAyC;gBACzC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;IACL,CAAC;IAEe,aAAa,CAAC,QAAsB,EAA4E;sCAA1E,iEAAgE,GAAG,CAAG,CAAD,GAAK;QAC5H,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,OAAO,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEvH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO,GAAG,iMAAI,aAAU,CAAC,IAAI,CAAC,CAAC;gBAC/B,OAAO,CAAC,cAAc,EAAE,CAAC;YAC7B,CAAC;YACD,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAG,iMAAI,aAAU,CAAC,UAAU,CAAC,CAAC;YACjD,aAAa,CAAC,KAAK,GAAG,GAAG,CAAC;YAC1B,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;IACL,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,QAAQ,GAAG,KAAc,CAAE,CAAC,KAAZ,IAAI,CAAC,IAAI;QAC/B,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;QAC3C,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACpC,MAAM,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,MAAM,oBAAoB,GAAG,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;QAE/D,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC,kBAAkB,CAAC,uBAAuB,CAAC,CAAC;QAEpF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,0BAA0B,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAE7G,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QAExF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,4BAA4B,+MAAE,yCAAqC,CAAC,KAAK,CAAC,CAAC;QAE7G,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;QAE7E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,MAAM,CAAC,CAAC;QAElG,IAAI,iBAAiB,GAAG,IAAI,CAAC;QAC7B,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YACrC,iBAAiB,GAAI,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,WAA4B,CAAC,WAAW,CAAC;QACrG,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,AAAC,IAAI,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,GAAK,CAAC,AAAF,IAAM,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,AAAC,CAAC,CAAC;QAE5K,MAAM,uBAAuB,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,uBAAuB,GACrE,MAAM,GACN,IAAI,CAAC,aAAa,CAAC,iBAAkB,CAAC,UAAU,GAC9C,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAkB,CAAC,KAAK,CAAC,GAC7D,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC;QAElD,MAAM,mBAAmB,GACrB,IAAI,CAAC,QAAQ,CAAC,uBAAuB,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,UAAU,GAC9E,gDAAwG,CAAE,MAA1D,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAkB,CAAC,KAAK,CAAC,IACxG,gDAAoF,CAAE,CAAC,KAAvC,IAAI,CAAC,QAAQ,CAAC,sBAAsB;QAE9F,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,iDAAiD,CAAC,CAAC;QAC3F,CAAC;QAED,MAAM,oBAAoB,GAAG;YAAE,MAAM,EAAE,qBAAqB;YAAE,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB;QAAA,CAAE,CAAC;QAC3I,MAAM,UAAU,GAAG;YAAE,MAAM,EAAE,qBAAqB;YAAE,OAAO,EAAE,EAAE;QAAA,CAAE,CAAC;QAClE,MAAM,yBAAyB,GAAG;YAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,iCAAiC,CAAC,CAAC,CAAC,6BAA6B;YAAE,OAAO,EAAE,EAAE;QAAA,CAAE,CAAC;QAExI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACrB,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;YAClB,KAAK,CAAC,iBAAiB,IAAI,qDAEzB,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAA,YAAqC,OAA1B,GAAG,CAAC,sBAAsB,EAAA;QAGpE,CAAC,MAAM,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;YAClC,KAAK,CAAC,iBAAiB,IAAI,UAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,iBAAkD,OAAlC,WAAW,CAAC,sBAAsB,EAAA,SAAU,CAAC;YAC7K,KAAK,CAAC,iBAAiB,IAAI,GAAsG,OAAnG,KAAK,AAA0G,CAAzG,gBAAgB,CAAC,YAAY,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,iBAAmD,oBAAtB,sBAAsB,EAAA,SAAU,CAAC;YAC/K,KAAK,CAAC,iBAAiB,IAAI,UAAG,KAAK,CAAC,gBAAgB,CAAC,cAAc,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,6CAAqC,aAAa,EAAoC,OAAjC,IAAI,CAAC,4BAA4B,EAAA,IAAK,CAAC;YAC/M,KAAK,CAAC,iBAAiB,IAAI,GAAuC,IAAI,GAAxC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAA,YAAe,aAAA,yCAA0C,CAAC;QACrH,CAAC;QAED,IAAI,cAAc,GAAG;YAAC,oBAAoB;YAAE,UAAU;YAAE,yBAAyB;SAAC,CAAC;QAEnF,IAAI,QAAQ,EAAE,CAAC;YACX,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,wBAAwB;gBAAE,OAAO,EAAE,EAAE;YAAA,CAAE,CAAC,CAAC;YACvE,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,wBAAwB;gBAAE,OAAO,EAAE,EAAE;YAAA,CAAE,CAAC,CAAC;YACvE,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,wBAAwB;gBAAE,OAAO,EAAE,EAAE;YAAA,CAAE,CAAC,CAAC;QAC3E,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,2BAA2B,EAAE,QAAQ,EAAE;YAClE,cAAc,EAAE,cAAc;SACjC,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,QAAQ,GACzB,4JAA4J,GAC5J,sIAAsI,CAAC;QAE7I,MAAM,UAAU,GAAG,QAAQ,GACrB,kGAAkG,GAClG,+FAA+F,CAAC;QAEtG,MAAM,cAAc,GAAG,QAAQ,GACzB,mEAAmE,GACnE,qEAAqE,CAAC;QAE5E,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,uDAAuD,CAAC,CAAC,CAAC,wDAAwD,CAAC;QAEjJ,KAAK,CAAC,wBAAwB,CAAC,uBAAuB,EAAE,QAAQ,EAAE;YAC9D,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,kGAAkG;oBAAE,OAAO,EAAE,EAAE;gBAAA,CAAE;gBAC3H;oBAAE,MAAM,EAAE,iCAAiC;oBAAE,OAAO,EAAE,EAAE;gBAAA,CAAE;gBAC1D;oBACI,MAAM,EAAE,UAAU;oBAClB,OAAO,EAAE,cAAc;iBAC1B;gBACD;oBAAE,MAAM,EAAE,UAAU;oBAAE,OAAO,EAAE,cAAc;gBAAA,CAAE;gBAC/C;oBAAE,MAAM,EAAE,wCAAwC;oBAAE,OAAO,EAAE,SAAS;gBAAA,CAAE;aAC3E;SACJ,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,iBAAuC,OAAtB,UAAuC,GAAG,IAAzB,EAAA,MAAmC,2BAAV,QAAS,KAAC,aAAmC,OAAlB,EAAE,CAAC,CAAC,CAAC,KAA8B,EAAE,CAAC;QAE1J,MAAM,kBAAkB,GAAG,CAAC,WAAW,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAG,UAAU,EAAA,MAA8B,OAAzB,EAAE,CAAC,sBAAsB,EAAA,iBAAkB,CAAC;QAE3K,MAAM,UAAU,GAAG,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAC5D,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,gBAAgB,CAAC,UAAU,gNAAE,wCAAqC,CAAC,OAAO,CAAC,GAAG,UAAiB,OAAP,OAAO,EAAA,QAAS,CAAC;QAE1I,cAAc,GAAG;YACb;gBAAE,MAAM,EAAE,IAAI,MAAM,CAAC,wBAAkE,OAA1C,IAAsD,IAA9C,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,EAAA,eAAc,GAAG,CAAC;gBAAE,OAAO,EAAE,GAAqB,CAAE,MAApB,kBAAkB;YAAE,CAAE;YAC7I;gBACI,MAAM,EAAE,wCAAwC;gBAChD,OAAO,EAAE,UAAG,KAAK,CAAC,gBAAgB,CAAC,cAAc,+MAAE,yCAAqC,CAAC,MAAM,CAAC,EAAA,sBAAuH,OAAlG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAA,GAAI;aAC9N;YACD;gBACI,MAAM,EAAE,IAAI,MAAM,CACd,mDAA4C,QAAQ,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,EAAA,8BAA0C,OAAb,aAAa,EAAA,gBAAiB,IACjJ,GAAG,CACN;gBACD,OAAO,EAAE,6BAAsB,kBAAkB,EAAA,MAAkB,OAAb,aAAa,EAAA,cAAe;aACrF;YACD;gBACI,MAAM,EAAE,oHAAoH;gBAC5H,OAAO,EAAE,4EAAqE,oBAAoB,EAAA,oBAAY,aAAa,EAAA,kBAQ3H,OAPI,QAAQ,GACF,WAAW,IAAI,IAAI,CAAC,oBAAoB,GACpC,UAAG,iBAAiB,EAAA,MAAmC,OAA9B,UAA+C,EAAE,KAAhC,GAAG,QAAS,KACtD,mBAAmB,GACvB,WAAW,IAAI,IAAI,CAAC,oBAAoB,GACtC,iBAAiB,GACjB,aACZ,EAAA,EAAG;aACN;YACD;gBACI,MAAM,EAAE,yDAAyD;gBACjE,OAAO,EAAE,2CAAyE,OAA9B,IAAyC,CAAC,CAAC,OAA9B,EAAA,kBAAgF,qBAAjD,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,EAAA,EAAG;aACvJ;YACD;gBAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,eAAe;gBAAE,OAAO,EAAE,mBAAmB;YAAA,CAAE;YAC/F;gBAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,eAAe;gBAAE,OAAO,EAAE,uBAAuB;YAAA,CAAE;YACnG;gBAAE,MAAM,EAAE,WAAW;gBAAE,OAAO,EAAE,UAAU,GAAG,KAAK;YAAA,CAAE;YACpD,QAAQ,GACF;gBACI,MAAM,EAAE,uHAAuH;gBAC/H,OAAO,EAAE,GAAO,OAAJ,IAAI,EAAA,mEAAoE,KAAG,UAAU;aACpG,GACD;gBACI,MAAM,EAAE,gCAAgC;gBACxC,OAAO,EAAE,GAAO,OAAJ,IAAI,EAAA,kBAAmB,KAAG,UAAU;aACnD;YACP;gBAAE,MAAM,EAAE,UAAU;gBAAE,OAAO,EAAE,WAAW,CAAC,sBAAsB,GAAG,MAAM;YAAA,CAAE;YAC5E;gBAAE,MAAM,EAAE,iBAAiB;gBAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAAc,OAAP,OAAO,EAAA,KAAM;YAAA,CAAE;YACtH,oBAAoB;SACvB,CAAC;QAEF,IAAI,QAAQ,EAAE,CAAC;YACX,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,yBAAyB;gBAAE,OAAO,EAAE,EAAE,CAAC,sBAAsB;YAAA,CAAE,CAAC,CAAC;YAC/F,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,mBAAmB;gBAAE,OAAO,EAAE,aAAa,CAAC,sBAAsB,GAAG,MAAM;YAAA,CAAE,CAAC,CAAC;YAC7G,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,+BAA+B;gBAAE,OAAO,EAAE,aAAa,GAAG,IAAI,CAAC,0BAA0B;YAAA,CAAE,CAAC,CAAC;YAC3H,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,uEAAuE;gBAAE,OAAO,EAAE,gBAAiB;YAAA,CAAE,CAAC,CAAC;QACzI,CAAC,MAAM,CAAC;YACJ,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,UAAU;gBAAE,OAAO,EAAE,EAAE,CAAC,sBAAsB;YAAA,CAAE,CAAC,CAAC;YAChF,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,aAAa;gBAAE,OAAO,EAAE,aAAa,CAAC,sBAAsB,GAAG,MAAM;YAAA,CAAE,CAAC,CAAC;YACvG,cAAc,CAAC,IAAI,CAAC;gBAAE,MAAM,EAAE,sBAAsB;gBAAE,OAAO,EAAE,aAAa,GAAG,IAAI,CAAC,0BAA0B;YAAA,CAAE,CAAC,CAAC;QACtH,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,cAAc,EAAE,QAAQ,EAAE;YAC5E,cAAc,EAAE,cAAc;SACjC,CAAC,CAAC;QAEH,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,iBAAU,OAAO,EAAA,KAAc,OAAV,UAAU,EAAA,SAAU,CAAC;QAEzG,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,GAAG,GAAuC,OAApC,IAAI,CAAC,iBAAiB,EAAA,eAA0B,IAAK,CAAC,MAAd,CAAC,OAAO,EAAA;QAElG,UAAU,IAAI,GAAuC,OAApC,IAAI,CAAC,iBAAiB,EAAA,eAA0B,WAAR,CAAC,OAAO,EAAA,IAAK,CAAC;QACvE,UAAU,IAAI,UAAG,IAAI,CAAC,iBAAiB,EAAA,4BAAoD,OAAzB,IAAI,CAAC,oBAAoB,EAAA,IAAK,CAAC;QACjG,UAAU,IAAI,UAAG,IAAI,CAAC,iBAAiB,EAAA,+BAA0D,OAA5B,IAAI,CAAC,uBAAuB,EAAA,IAAK,CAAC;QAEvG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAE3E,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;QACvE,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC,mBAAmB,CAAC,uBAAuB,CAAC;IACjF,CAAC;IA7ZD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAtB3C,IAAA,CAAA,0BAA0B,GAAG,EAAE,CAAC;QAChC,IAAA,CAAA,4BAA4B,GAAG,EAAE,CAAC;QAClC,IAAA,CAAA,gBAAgB,GAAG,EAAE,CAAC;QAE9B,+EAAA,EAAiF,CAE1E,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QACvB,+EAAA,EAAiF,CAE1E,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QACvB,gFAAA,EAAkF,CAE3E,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QACpC,4EAAA,EAA8E,CAEvE,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QASnC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,SAAS;QACT,IAAI,CAAC,aAAa,CAAC,eAAe,gNAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,aAAa,gNAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,CAAC,cAAc,gNAAE,wCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,CAAC,IAAI,gNAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,UAAU,gNAAE,wCAAqC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,eAAe,gNAAE,wCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzF,IAAI,CAAC,aAAa,CAAC,eAAe,gNAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACvF,IAAI,CAAC,aAAa,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACxF,IAAI,CAAC,aAAa,CACd,KAAK,gNACL,wCAAqC,CAAC,MAAM,EAC5C,IAAI,mMACJ,2BAAwB,CAAC,iBAAiB,EAC1C,2MAAI,0CAAuC,CAAC,KAAK,EAAE,IAAI,EAAA,EAAA,8CAAA,mMAA8C,WAAQ,EAAE,UAAU,CAAC,CAC7H,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,OAAO,gNAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAEhF,WAAW;QACX,IAAI,CAAC,cAAc,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,UAAU,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;IACnF,CAAC;CA+XJ;CAzaU,uKAAA,EAAA;8KADN,yBAAA,AAAsB,EAAC,eAAe,EAAA,EAAA,kCAAA,KAAkC,YAAY,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;mDAChH;2JAGhB,aAAA,EAAA;QADN,+LAAA,AAAsB,EAAC,eAAe,EAAA,EAAA,kCAAA,KAAkC,YAAY,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;mDAChH;2JAGhB,aAAA,EAAA;8KADN,yBAAA,AAAsB,EAAC,wBAAwB,EAAA,EAAA,kCAAA,KAAkC,SAAS,EAAE;QAAE,QAAQ,EAAE,IAAI;IAAA,CAAE,CAAC;gEAC5E;2JAG7B,aAAA,EAAA;8KADN,yBAAA,AAAsB,EAAC,mBAAmB,EAAA,EAAA,kCAAA,KAAkC,YAAY,EAAE;QAAE,QAAQ,EAAE,IAAI;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;mEACpG;gKAka3C,gBAAA,AAAa,EAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/discardBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/discardBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\n/**\r\n * Block used to discard a pixel if a value is smaller than a cutoff\r\n */\r\nexport class DiscardBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new DiscardBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment, true);\r\n\r\n        this.registerInput(\"value\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"cutoff\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"DiscardBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the color input component\r\n     */\r\n    public get value(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the cutoff input component\r\n     */\r\n    public get cutoff(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        state.sharedData.hints.needAlphaTesting = true;\r\n\r\n        if (!this.cutoff.isConnected || !this.value.isConnected) {\r\n            return;\r\n        }\r\n\r\n        state.compilationString += `if (${this.value.associatedVariableName} < ${this.cutoff.associatedVariableName}) { discard; }\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.DiscardBlock\", DiscardBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;;;;;AAIrD,MAAO,YAAa,0LAAQ,oBAAiB;IAY/C;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACtD,OAAO;QACX,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,cAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAA,OAAwC,OAAlC,IAAI,CAAC,KAA+C,CAAzC,AAA0C,CAAzC,sBAAsB,EAAA;QAE3G,OAAO,IAAI,CAAC;IAChB,CAAC;IA7CD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,aAAa,CAAC,OAAO,gNAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACpF,CAAC;CAqCJ;gKAED,gBAAA,AAAa,EAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/frontFacingBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/frontFacingBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { ShaderLanguage } from \"../../../../Materials/shaderLanguage\";\r\n/**\r\n * Block used to test if the fragment shader is front facing\r\n */\r\nexport class FrontFacingBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new FrontFacingBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"FrontFacingBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex) {\r\n            state.sharedData.raiseBuildError(\"FrontFacingBlock must only be used in a fragment shader\");\r\n            return this;\r\n        }\r\n\r\n        const output = this._outputs[0];\r\n\r\n        state.compilationString +=\r\n            state._declareOutput(output) +\r\n            ` = ${state._generateTernary(\"1.0\", \"0.0\", state.shaderLanguage === ShaderLanguage.GLSL ? \"gl_FrontFacing\" : \"fragmentInputs.frontFacing\")};\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.FrontFacingBlock\", FrontFacingBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;;;;;AAKrD,MAAO,gBAAiB,0LAAQ,oBAAiB;IAWnD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,CAAC,MAAM,KAAK,4NAAwB,CAAC,MAAM,EAAE,CAAC;YACnD,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,yDAAyD,CAAC,CAAC;YAC5F,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,iBAAiB,IACnB,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,GAC5B,MAA0I,OAApI,KAAK,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,gBAAgB,CAAC,CAAC,CAAC,4BAA4B,CAAC,EAAA,IAAK,CAAC;QAEpJ,OAAO,IAAI,CAAC;IAChB,CAAC;IAxCD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,kMAAE,4BAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,KAAK,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;IAClH,CAAC;CAiCJ;gKAED,gBAAA,AAAa,EAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/derivativeBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/derivativeBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to get the derivative value on x and y of a given input\r\n */\r\nexport class DerivativeBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new DerivativeBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false);\r\n        this.registerOutput(\"dx\", NodeMaterialBlockConnectionPointTypes.BasedOnInput);\r\n        this.registerOutput(\"dy\", NodeMaterialBlockConnectionPointTypes.BasedOnInput);\r\n\r\n        this._outputs[0]._typeConnectionSource = this._inputs[0];\r\n        this._outputs[1]._typeConnectionSource = this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"DerivativeBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the derivative output on x\r\n     */\r\n    public get dx(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the derivative output on y\r\n     */\r\n    public get dy(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const dx = this._outputs[0];\r\n        const dy = this._outputs[1];\r\n\r\n        state._emitExtension(\"derivatives\", \"#extension GL_OES_standard_derivatives : enable\");\r\n        let dpdx = \"dFdx\";\r\n        let dpdy = \"dFdy\";\r\n\r\n        if (state.shaderLanguage === ShaderLanguage.WGSL) {\r\n            dpdx = \"dpdx\";\r\n            dpdy = \"dpdy\";\r\n        }\r\n\r\n        if (dx.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(dx) + ` = ${dpdx}(${this.input.associatedVariableName});\\n`;\r\n        }\r\n\r\n        if (dy.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(dy) + ` = ${dpdy}(${this.input.associatedVariableName});\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.DerivativeBlock\", DerivativeBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;;;;;AAMrD,MAAO,eAAgB,0LAAQ,oBAAiB;IAgBlD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5B,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5B,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,iDAAiD,CAAC,CAAC;QACvF,IAAI,IAAI,GAAG,MAAM,CAAC;QAClB,IAAI,IAAI,GAAG,MAAM,CAAC;QAElB,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,IAAI,GAAG,MAAM,CAAC;YACd,IAAI,GAAG,MAAM,CAAC;QAClB,CAAC;QAED,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC;YAClB,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,MAAc,OAAR,IAAI,EAAA,KAAqC,WAA7B,CAAC,KAAK,CAAC,sBAAsB,EAAA,KAAM,CAAC;QAChH,CAAC;QAED,IAAI,EAAE,CAAC,YAAY,EAAE,CAAC;YAClB,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,aAAM,IAAI,EAAA,KAAqC,KAAM,CAAC,CAAxC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAA;QACzG,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IApED;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,kMAAE,4BAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,aAAa,CAAC,OAAO,gNAAE,wCAAqC,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACrF,IAAI,CAAC,cAAc,CAAC,IAAI,gNAAE,wCAAqC,CAAC,YAAY,CAAC,CAAC;QAC9E,IAAI,CAAC,cAAc,CAAC,IAAI,gNAAE,wCAAqC,CAAC,YAAY,CAAC,CAAC;QAE9E,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC;CAwDJ;gKAED,gBAAA,AAAa,EAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1383, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/fragCoordBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/fragCoordBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to make gl_FragCoord available\r\n */\r\nexport class FragCoordBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new FragCoordBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\"xy\", NodeMaterialBlockConnectionPointTypes.Vector2, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"xyz\", NodeMaterialBlockConnectionPointTypes.Vector3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"xyzw\", NodeMaterialBlockConnectionPointTypes.Vector4, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"x\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"y\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"z\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"w\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"FragCoordBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the xy component\r\n     */\r\n    public get xy(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz component\r\n     */\r\n    public get xyz(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyzw component\r\n     */\r\n    public get xyzw(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the x component\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the y component\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the z component\r\n     */\r\n    public get z(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the w component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[6];\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected writeOutputs(state: NodeMaterialBuildState): string {\r\n        let code = \"\";\r\n\r\n        const coord = state.shaderLanguage === ShaderLanguage.WGSL ? \"fragmentInputs.position\" : \"gl_FragCoord\";\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                code += `${state._declareOutput(output)} = ${coord}.${output.name};\\n`;\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex) {\r\n            state.sharedData.raiseBuildError(\"FragCoordBlock must only be used in a fragment shader\");\r\n            return this;\r\n        }\r\n\r\n        state.compilationString += this.writeOutputs(state);\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.FragCoordBlock\", FragCoordBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;;;;;AAMrD,MAAO,cAAe,0LAAQ,oBAAiB;IAiBjD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED,gEAAgE;IACtD,YAAY,CAAC,KAA6B,EAAA;QAChD,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,yBAAyB,CAAC,CAAC,CAAC,cAAc,CAAC;QAExG,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,IAAI,UAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,EAAA,OAAe,MAAM,CAAf,KAAK,EAAA,KAAe,cAAJ,IAAI,EAAA,IAAK,CAAC;YAC3E,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,CAAC,MAAM,sMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;YACnD,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,uDAAuD,CAAC,CAAC;YAC1F,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEpD,OAAO,IAAI,CAAC;IAChB,CAAC;IAnGD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,IAAI,gNAAE,wCAAqC,CAAC,OAAO,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,KAAK,gNAAE,wCAAqC,CAAC,OAAO,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC7G,IAAI,CAAC,cAAc,CAAC,MAAM,gNAAE,wCAAqC,CAAC,OAAO,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9G,IAAI,CAAC,cAAc,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,kMAAE,4BAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;IAC7G,CAAC;CAsFJ;gKAED,gBAAA,AAAa,EAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/screenSpaceBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/screenSpaceBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { NodeMaterial } from \"../../nodeMaterial\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\n\r\n/**\r\n * Block used to transform a vector3 or a vector4 into screen space\r\n */\r\nexport class ScreenSpaceBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new ScreenSpaceBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerInput(\"vector\", NodeMaterialBlockConnectionPointTypes.AutoDetect);\r\n        this.registerInput(\"worldViewProjection\", NodeMaterialBlockConnectionPointTypes.Matrix);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"x\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"y\", NodeMaterialBlockConnectionPointTypes.Float);\r\n\r\n        this.inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ScreenSpaceBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the vector input\r\n     */\r\n    public get vector(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the worldViewProjection transform input\r\n     */\r\n    public get worldViewProjection(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the x output component\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the y output component\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.worldViewProjection.isConnected) {\r\n            let worldViewProjectionInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.WorldViewProjection && additionalFilteringInfo(b));\r\n\r\n            if (!worldViewProjectionInput) {\r\n                worldViewProjectionInput = new InputBlock(\"worldViewProjection\");\r\n                worldViewProjectionInput.setAsSystemValue(NodeMaterialSystemValues.WorldViewProjection);\r\n            }\r\n            worldViewProjectionInput.output.connectTo(this.worldViewProjection);\r\n        }\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const vector = this.vector;\r\n        const worldViewProjection = this.worldViewProjection;\r\n\r\n        if (!vector.connectedPoint) {\r\n            return;\r\n        }\r\n\r\n        const worldViewProjectionName = worldViewProjection.associatedVariableName;\r\n\r\n        const tempVariableName = state._getFreeVariableName(\"screenSpaceTemp\");\r\n\r\n        switch (vector.connectedPoint.type) {\r\n            case NodeMaterialBlockConnectionPointTypes.Vector3:\r\n                state.compilationString += `${state._declareLocalVar(tempVariableName, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${worldViewProjectionName} * vec4${state.fSuffix}(${vector.associatedVariableName}, 1.0);\\n`;\r\n                break;\r\n            case NodeMaterialBlockConnectionPointTypes.Vector4:\r\n                state.compilationString += `${state._declareLocalVar(tempVariableName, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${worldViewProjectionName} * ${vector.associatedVariableName};\\n`;\r\n                break;\r\n        }\r\n\r\n        state.compilationString += `${tempVariableName} = vec4${state.fSuffix}(${tempVariableName}.xy / ${tempVariableName}.w, ${tempVariableName}.zw);`;\r\n        state.compilationString += `${tempVariableName} = vec4${state.fSuffix}(${tempVariableName}.xy * 0.5 + vec2${state.fSuffix}(0.5, 0.5), ${tempVariableName}.zw);`;\r\n\r\n        if (this.output.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(this.output) + ` = ${tempVariableName}.xy;\\n`;\r\n        }\r\n        if (this.x.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(this.x) + ` = ${tempVariableName}.x;\\n`;\r\n        }\r\n        if (this.y.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(this.y) + ` = ${tempVariableName}.y;\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ScreenSpaceBlock\", ScreenSpaceBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;;;;;;;AAK3C,MAAO,gBAAiB,0LAAQ,oBAAiB;IAmBnD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,aAAa,CAAC,QAAsB,EAA4E;sCAA1E,iEAAgE,GAAG,CAAG,CAAD,GAAK;QAC5H,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACxC,IAAI,wBAAwB,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAC,AAAF,CAAG,WAAW,sMAAK,2BAAwB,CAAC,mBAAmB,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEtK,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAC5B,wBAAwB,GAAG,iMAAI,aAAU,CAAC,qBAAqB,CAAC,CAAC;gBACjE,wBAAwB,CAAC,gBAAgB,kMAAC,2BAAwB,CAAC,mBAAmB,CAAC,CAAC;YAC5F,CAAC;YACD,wBAAwB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACxE,CAAC;IACL,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAErD,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,MAAM,uBAAuB,GAAG,mBAAmB,CAAC,sBAAsB,CAAC;QAE3E,MAAM,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAEvE,OAAQ,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;YACjC,mNAAK,wCAAqC,CAAC,OAAO;gBAC9C,KAAK,CAAC,iBAAiB,IAAI,UAAG,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,cAAM,uBAAuB,EAAA,kBAAU,KAAK,CAAC,OAAO,EAAA,KAAiC,OAA7B,MAAM,CAAC,sBAAsB,EAAA,UAAW,CAAC;gBACtN,MAAM;YACV,mNAAK,wCAAqC,CAAC,OAAO;gBAC9C,KAAK,CAAC,iBAAiB,IAAI,GAAgG,OAA7F,KAAK,CAAC,UAA8G,MAA9F,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,uCAA6B,OAAmC,OAA7B,MAAM,CAAC,sBAAsB,EAAA,IAAK,CAAC;gBAC3L,MAAM;QACd,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,UAAG,gBAAgB,EAAA,kBAAU,KAAK,CAAC,OAAO,EAAA,YAAI,gBAAgB,EAAA,iBAAS,gBAAgB,EAAA,QAAuB,OAAhB,gBAAgB,EAAA,MAAO,CAAC;QACjJ,KAAK,CAAC,iBAAiB,IAAI,UAAG,gBAAgB,EAAA,WAA2B,OAAjB,KAAK,CAAC,GAA2B,IAApB,EAAA,8BAAoB,2BAAmB,KAAK,CAAC,OAAO,EAAA,gBAA+B,OAAhB,gBAAgB,EAAA,MAAO,CAAC;QAEhK,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3B,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAsB,OAAhB,gBAAgB,EAAA,OAAQ,CAAC;QAClG,CAAC;QACD,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YACtB,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAsB,MAAO,CAAvB,AAAwB,gBAAR,EAAA;QACpF,CAAC;QACD,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YACtB,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAsB,OAAhB,gBAAgB,EAAA,MAAO,CAAC;QAC5F,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IA9GD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,aAAa,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,qBAAqB,gNAAE,wCAAqC,CAAC,MAAM,CAAC,CAAC;QACxF,IAAI,CAAC,cAAc,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MACrD,wCAAqC,CAAC,MAAM,iNAAG,wCAAqC,CAAC,OAAO,iNAAG,wCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;CA+FJ;gKAED,gBAAA,AAAa,EAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/twirlBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/twirlBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { Vector2 } from \"../../../../Maths/math.vector\";\r\n\r\n/**\r\n * Block used to generate a twirl\r\n */\r\nexport class TwirlBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new TwirlBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerInput(\"strength\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerInput(\"center\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerInput(\"offset\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        this.registerOutput(\"x\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerOutput(\"y\", NodeMaterialBlockConnectionPointTypes.Float);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"TwirlBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the strength component\r\n     */\r\n    public get strength(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the center component\r\n     */\r\n    public get center(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the offset component\r\n     */\r\n    public get offset(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the x output component\r\n     */\r\n    public get x(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the y output component\r\n     */\r\n    public get y(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    public override autoConfigure() {\r\n        if (!this.center.isConnected) {\r\n            const centerInput = new InputBlock(\"center\");\r\n            centerInput.value = new Vector2(0.5, 0.5);\r\n\r\n            centerInput.output.connectTo(this.center);\r\n        }\r\n\r\n        if (!this.strength.isConnected) {\r\n            const strengthInput = new InputBlock(\"strength\");\r\n            strengthInput.value = 1.0;\r\n\r\n            strengthInput.output.connectTo(this.strength);\r\n        }\r\n\r\n        if (!this.offset.isConnected) {\r\n            const offsetInput = new InputBlock(\"offset\");\r\n            offsetInput.value = new Vector2(0, 0);\r\n\r\n            offsetInput.output.connectTo(this.offset);\r\n        }\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const tempDelta = state._getFreeVariableName(\"delta\");\r\n        const tempAngle = state._getFreeVariableName(\"angle\");\r\n        const tempX = state._getFreeVariableName(\"x\");\r\n        const tempY = state._getFreeVariableName(\"y\");\r\n        const tempResult = state._getFreeVariableName(\"result\");\r\n\r\n        state.compilationString += `        \r\n            ${state._declareLocalVar(tempDelta, NodeMaterialBlockConnectionPointTypes.Vector2)} = ${this.input.associatedVariableName} - ${this.center.associatedVariableName};\r\n            ${state._declareLocalVar(tempAngle, NodeMaterialBlockConnectionPointTypes.Float)} = ${this.strength.associatedVariableName} * length(${tempDelta});\r\n            ${state._declareLocalVar(tempX, NodeMaterialBlockConnectionPointTypes.Float)} = cos(${tempAngle}) * ${tempDelta}.x - sin(${tempAngle}) * ${tempDelta}.y;\r\n            ${state._declareLocalVar(tempY, NodeMaterialBlockConnectionPointTypes.Float)} = sin(${tempAngle}) * ${tempDelta}.x + cos(${tempAngle}) * ${tempDelta}.y;\r\n            ${state._declareLocalVar(tempResult, NodeMaterialBlockConnectionPointTypes.Vector2)} = vec2(${tempX} + ${this.center.associatedVariableName}.x + ${this.offset.associatedVariableName}.x, ${tempY} + ${this.center.associatedVariableName}.y + ${this.offset.associatedVariableName}.y);\r\n        `;\r\n\r\n        if (this.output.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(this.output) + ` = ${tempResult};\\n`;\r\n        }\r\n\r\n        if (this.x.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(this.x) + ` = ${tempResult}.x;\\n`;\r\n        }\r\n\r\n        if (this.y.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(this.y) + ` = ${tempResult}.y;\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TwirlBlock\", TwirlBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;;;;;;;AAKlD,MAAO,UAAW,0LAAQ,oBAAiB;IAiB7C;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,aAAa,GAAA;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,iMAAI,aAAU,CAAC,QAAQ,CAAC,CAAC;YAC7C,WAAW,CAAC,KAAK,GAAG,sKAAI,UAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAE1C,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAG,IAAI,0MAAU,CAAC,UAAU,CAAC,CAAC;YACjD,aAAa,CAAC,KAAK,GAAG,GAAG,CAAC;YAE1B,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,iMAAI,aAAU,CAAC,QAAQ,CAAC,CAAC;YAC7C,WAAW,CAAC,KAAK,GAAG,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEtC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,SAAS,GAAG,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACtD,MAAM,SAAS,GAAG,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACtD,MAAM,KAAK,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,UAAU,GAAG,KAAK,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAExD,KAAK,CAAC,iBAAiB,IAAI,yBACiE,OAAtF,KAAK,CAAC,gBAAgB,CAAC,SAAS,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,OAA6C,WAAnC,CAAC,KAAK,CAAC,sBAAsB,EAAA,kBAAU,CAAC,MAAM,CAAC,sBAAsB,EAAA,0BAC/J,KAAK,CAAC,gBAAgB,CAAC,SAAS,gNAAE,wCAAqC,CAAC,KAAK,CAAC,EAAA,cAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB,EAAA,qBAAa,SAAS,EAAA,oBAC1D,OAApF,EAA6F,GAAxF,CAAC,gBAAgB,CAAC,KAAK,gNAAE,wCAAqC,CAAC,KAAK,CAAC,EAAA,6BAAmB,eAAO,SAAS,EAAA,oBAAY,SAAS,EAAA,eAAO,SAAS,EAAA,4BAClJ,KAAK,CAAC,gBAAgB,CAAC,KAAK,gNAAE,wCAAqC,CAAC,KAAK,CAAC,EAAA,kBAAU,SAAS,EAAA,eAAO,SAAS,EAAA,aAA4B,OAAhB,EAAyB,OAAhB,EAAA,0BAAgB,qBACtD,KAAK,EAAjG,KAAK,CAAC,gBAAgB,CAAC,UAAU,EAAE,sPAAqC,CAAC,OAAO,CAAC,EAAA,0BAAgB,OAAgD,OAA1C,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAA,SAAiD,KAAK,MAA1C,CAAC,MAAM,CAAC,sBAAsB,EAAA,sBAAY,OAAgD,OAA1C,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAA,SAA0C,WAA9B,CAAC,MAAM,CAAC,sBAAsB,EAAA;QAGvR,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3B,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAgB,OAAV,UAAU,EAAA,IAAK,CAAC;QACzF,CAAC;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YACtB,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAgB,OAAV,UAAU,EAAA,MAAO,CAAC;QACtF,CAAC;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC;YACtB,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAgB,OAAV,UAAU,EAAA,MAAO,CAAC;QACtF,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IA9HD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,kMAAE,4BAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,aAAa,CAAC,OAAO,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC3E,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,sPAAqC,CAAC,KAAK,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC5E,IAAI,CAAC,aAAa,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC5E,IAAI,CAAC,cAAc,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;IAC1E,CAAC;CAiHJ;gKAED,gBAAA,AAAa,EAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1693, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/heightToNormalBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/heightToNormalBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { ShaderLanguage } from \"../../../../Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to convert a height vector to a normal\r\n */\r\nexport class HeightToNormalBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Creates a new HeightToNormalBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.Float);\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        this.registerInput(\"worldTangent\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.registerOutput(\"xyz\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        this._inputs[3].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Defines if the output should be generated in world or tangent space.\r\n     * Note that in tangent space the result is also scaled by 0.5 and offsetted by 0.5 so that it can directly be used as a PerturbNormal.normalMapColor input\r\n     */\r\n    @editableInPropertyPage(\"Generate in world space instead of tangent space\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { notifiers: { update: true } })\r\n    public generateInWorldSpace: boolean = false;\r\n\r\n    /**\r\n     * Defines that the worldNormal input will be normalized by the HeightToNormal block before being used\r\n     */\r\n    @editableInPropertyPage(\"Force normalization for the worldNormal input\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { notifiers: { update: true } })\r\n    public automaticNormalizationNormal: boolean = true;\r\n\r\n    /**\r\n     * Defines that the worldTangent input will be normalized by the HeightToNormal block before being used\r\n     */\r\n    @editableInPropertyPage(\"Force normalization for the worldTangent input\", PropertyTypeForEdition.Boolean, \"PROPERTIES\", { notifiers: { update: true } })\r\n    public automaticNormalizationTangent: boolean = true;\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"HeightToNormalBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the input component\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the position component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the normal component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the tangent component\r\n     */\r\n    public get worldTangent(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the xyz component\r\n     */\r\n    public get xyz(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const output = this._outputs[0];\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        const fPrefix = state.fSuffix;\r\n\r\n        if (!this.generateInWorldSpace && !this.worldTangent.isConnected) {\r\n            state.sharedData.raiseBuildError(`You must connect the 'worldTangent' input of the ${this.name} block!`);\r\n        }\r\n\r\n        const startCode = this.generateInWorldSpace\r\n            ? \"\"\r\n            : `\r\n            vec3 biTangent = cross(norm, tgt);\r\n            mat3 TBN = mat3(tgt, biTangent, norm);\r\n            `;\r\n\r\n        const endCode = this.generateInWorldSpace\r\n            ? \"\"\r\n            : `\r\n            result = TBN * result;\r\n            result = result * vec3(0.5) + vec3(0.5);\r\n            `;\r\n\r\n        let heightToNormal = `\r\n            vec4 heightToNormal(float height, vec3 position, vec3 tangent, vec3 normal) {\r\n                vec3 tgt = ${this.automaticNormalizationTangent ? \"normalize(tangent);\" : \"tangent;\"}\r\n                vec3 norm = ${this.automaticNormalizationNormal ? \"normalize(normal);\" : \"normal;\"}\r\n                ${startCode}\r\n                vec3 worlddX = dFdx(position);\r\n                vec3 worlddY = dFdy(position);\r\n                vec3 crossX = cross(norm, worlddX);\r\n                vec3 crossY = cross(worlddY, norm);\r\n                float d = abs(dot(crossY, worlddX));\r\n                vec3 inToNormal = vec3(((((height + dFdx(height)) - height) * crossY) + (((height + dFdy(height)) - height) * crossX)) * sign(d));\r\n                inToNormal.y *= -1.0;\r\n                vec3 result = normalize((d * norm) - inToNormal);\r\n                ${endCode}\r\n                return vec4(result, 0.);\r\n            }`;\r\n\r\n        if (isWebGPU) {\r\n            heightToNormal = state._babylonSLtoWGSL(heightToNormal);\r\n        } else {\r\n            state._emitExtension(\"derivatives\", \"#extension GL_OES_standard_derivatives : enable\");\r\n        }\r\n        state._emitFunction(\"heightToNormal\", heightToNormal, \"// heightToNormal\");\r\n        state.compilationString +=\r\n            state._declareOutput(output) +\r\n            ` = heightToNormal(${this.input.associatedVariableName}, ${this.worldPosition.associatedVariableName}, ${\r\n                this.worldTangent.isConnected ? this.worldTangent.associatedVariableName : `vec3${fPrefix}(0.)`\r\n            }.xyz, ${this.worldNormal.associatedVariableName});\\n`;\r\n\r\n        if (this.xyz.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(this.xyz) + ` = ${this.output.associatedVariableName}.xyz;\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n        codeString += `${this._codeVariableName}.generateInWorldSpace = ${this.generateInWorldSpace};\\n`;\r\n        codeString += `${this._codeVariableName}.automaticNormalizationNormal = ${this.automaticNormalizationNormal};\\n`;\r\n        codeString += `${this._codeVariableName}.automaticNormalizationTangent = ${this.automaticNormalizationTangent};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.generateInWorldSpace = this.generateInWorldSpace;\r\n        serializationObject.automaticNormalizationNormal = this.automaticNormalizationNormal;\r\n        serializationObject.automaticNormalizationTangent = this.automaticNormalizationTangent;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.generateInWorldSpace = serializationObject.generateInWorldSpace;\r\n        this.automaticNormalizationNormal = serializationObject.automaticNormalizationNormal;\r\n        this.automaticNormalizationTangent = serializationObject.automaticNormalizationTangent;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.HeightToNormalBlock\", HeightToNormalBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAG1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;;;;;;;AAOhG,MAAO,mBAAoB,0LAAQ,oBAAiB;IAuCtD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,qBAAqB,CAAC;IACjC,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YAC/D,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,oDAA6D,OAAT,IAAI,CAAC,IAAI,EAAA,QAAS,CAAC,CAAC;QAC7G,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,GACrC,EAAE,GACF;QAKN,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,GACnC,EAAE,GACF;QAKN,IAAI,cAAc,GAAG,2HAGC,OADD,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,UAAU,EAAA,6CAClE,CAAC,4BAA4B,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,EAAA,6BAChF,SAAS,EAAA,ohBASF,OAAP,OAAO,EAAA;QAIjB,IAAI,QAAQ,EAAE,CAAC;YACX,cAAc,GAAG,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAC5D,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,cAAc,CAAC,aAAa,EAAE,iDAAiD,CAAC,CAAC;QAC3F,CAAC;QACD,KAAK,CAAC,aAAa,CAAC,gBAAgB,EAAE,cAAc,EAAE,mBAAmB,CAAC,CAAC;QAC3E,KAAK,CAAC,iBAAiB,IACnB,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,GAC5B,4BAAqB,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAA,aAAK,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAA,aAChG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAAc,OAAP,OAAO,EAAA,KAC7F,IAAA,UAAgD,OAAvC,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAA,KAAM,CAAC;QAE3D,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;YACxB,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAwC,OAAlC,CAA2C,CAAC,EAAxC,CAAC,MAAM,CAAC,sBAAsB,EAAA;QACxG,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAC7C,UAAU,IAAI,UAAG,IAAI,CAAC,iBAAiB,EAAA,4BAAoD,OAAzB,IAAI,CAAC,oBAAoB,EAAA,IAAK,CAAC;QACjG,UAAU,IAAI,GAA4D,OAAzD,IAAI,CAAC,iBAAiB,EAAA,oCAAoE,WAA7B,CAAC,4BAA4B,EAAA,IAAK,CAAC;QACjH,UAAU,IAAI,UAAG,IAAI,CAAC,iBAAiB,EAAA,qCAAsE,IAAK,CAAC,EAAxC,IAAI,CAAC,6BAA6B,EAAA;QAE7G,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,4BAA4B,GAAG,IAAI,CAAC,4BAA4B,CAAC;QACrF,mBAAmB,CAAC,6BAA6B,GAAG,IAAI,CAAC,6BAA6B,CAAC;QAEvF,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,oBAAoB,CAAC;QACrE,IAAI,CAAC,4BAA4B,GAAG,mBAAmB,CAAC,4BAA4B,CAAC;QACrF,IAAI,CAAC,6BAA6B,GAAG,mBAAmB,CAAC,6BAA6B,CAAC;IAC3F,CAAC;IA9KD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAcnD;;;WAGG,CAEI,IAAA,CAAA,oBAAoB,GAAY,KAAK,CAAC;QAE7C;;WAEG,CAEI,IAAA,CAAA,4BAA4B,GAAY,IAAI,CAAC;QAEpD;;WAEG,CAEI,IAAA,CAAA,6BAA6B,GAAY,IAAI,CAAC;QA7BjD,IAAI,CAAC,aAAa,CAAC,OAAO,gNAAE,wCAAqC,CAAC,KAAK,CAAC,CAAC;QACzE,IAAI,CAAC,aAAa,CAAC,eAAe,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,aAAa,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QACjF,IAAI,CAAC,aAAa,CAAC,cAAc,gNAAE,wCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC3F,IAAI,CAAC,cAAc,CAAC,QAAQ,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,KAAK,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAE1E,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MACtD,wCAAqC,CAAC,MAAM,gNAAG,yCAAqC,CAAC,OAAO,iNAAG,wCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;CA8JJ;CAvJU,uKAAA,EAAA;8KADN,yBAAA,AAAsB,EAAC,kDAAkD,EAAA,EAAA,kCAAA,KAAkC,YAAY,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;iEAC7G;2JAMtC,aAAA,EAAA;IADN,mMAAA,AAAsB,EAAC,+CAA+C,EAAA,EAAA,kCAAA,KAAkC,YAAY,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;yEACnG;2JAM7C,aAAA,EAAA;8KADN,yBAAA,AAAsB,EAAC,gDAAgD,EAAA,EAAA,kCAAA,KAAkC,YAAY,EAAE;QAAE,SAAS,EAAE;YAAE,MAAM,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;0EACnG;gKA6IzD,gBAAA,AAAa,EAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1838, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/fragDepthBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/fragDepthBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n/**\r\n * Block used to write the fragment depth\r\n */\r\nexport class FragDepthBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new FragDepthBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment, true);\r\n\r\n        this.registerInput(\"depth\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"worldPos\", NodeMaterialBlockConnectionPointTypes.Vector4, true);\r\n        this.registerInput(\"viewProjection\", NodeMaterialBlockConnectionPointTypes.Matrix, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"FragDepthBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the depth input component\r\n     */\r\n    public get depth(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the worldPos input component\r\n     */\r\n    public get worldPos(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the viewProjection input component\r\n     */\r\n    public get viewProjection(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const fragDepth = state.shaderLanguage === ShaderLanguage.GLSL ? \"gl_FragDepth\" : \"fragmentOutputs.fragDepth\";\r\n\r\n        if (this.depth.isConnected) {\r\n            state.compilationString += `${fragDepth} = ${this.depth.associatedVariableName};\\n`;\r\n        } else if (this.worldPos.isConnected && this.viewProjection.isConnected) {\r\n            state.compilationString += `\r\n                ${state._declareLocalVar(\"p\", NodeMaterialBlockConnectionPointTypes.Vector4)} = ${this.viewProjection.associatedVariableName} * ${this.worldPos.associatedVariableName};\r\n                ${state._declareLocalVar(\"v\", NodeMaterialBlockConnectionPointTypes.Float)} = p.z / p.w;\r\n                #ifndef IS_NDC_HALF_ZRANGE\r\n                    v = v * 0.5 + 0.5;\r\n                #endif\r\n                ${fragDepth} = v;\r\n    \r\n            `;\r\n        } else {\r\n            Logger.Warn(\"FragDepthBlock: either the depth input or both the worldPos and viewProjection inputs must be connected!\");\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.FragDepthBlock\", FragDepthBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,mCAAyB;;;;;;AAKpC,MAAO,cAAe,0LAAQ,oBAAiB;IAajD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,cAAc,CAAC,CAAC,CAAC,2BAA2B,CAAC;QAE9G,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACzB,KAAK,CAAC,iBAAiB,IAAI,UAAG,SAAS,EAAA,OAAuC,OAAjC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAA,IAAK,CAAC;QACxF,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YACtE,KAAK,CAAC,iBAAiB,IAAI,qBAC2D,OAAhF,KAAK,CAAC,gBAAgB,CAAC,GAAG,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,OAAsD,WAA5C,CAAC,cAAc,CAAC,sBAAsB,EAAA,OAC1H,KAAK,MAD+H,CAAC,QAAQ,CAAC,sBAAsB,EAAA,uBAKpK,SAAS,IAJH,gBAAgB,CAAC,GAAG,gNAAE,wCAAqC,CAAC,KAAK,CAAC,EAAA,+IAI/D,kBAAA;QAGnB,CAAC,MAAM,CAAC;qKACJ,SAAM,CAAC,IAAI,CAAC,0GAA0G,CAAC,CAAC;QAC5H,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IA/DD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,aAAa,CAAC,OAAO,gNAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,UAAU,gNAAE,wCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACpF,IAAI,CAAC,aAAa,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7F,CAAC;CAsDJ;gKAED,gBAAA,AAAa,EAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1900, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/shadowMapBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/shadowMapBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to output the depth to a shadow map\r\n */\r\nexport class ShadowMapBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new ShadowMapBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false);\r\n        this.registerInput(\"viewProjection\", NodeMaterialBlockConnectionPointTypes.Matrix, false);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerOutput(\"depth\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        this.worldNormal.addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ShadowMapBlock\";\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"vPositionWSM\");\r\n        state._excludeVariableName(\"lightDataSM\");\r\n        state._excludeVariableName(\"biasAndScaleSM\");\r\n        state._excludeVariableName(\"depthValuesSM\");\r\n        state._excludeVariableName(\"clipPos\");\r\n        state._excludeVariableName(\"worldPos\");\r\n        state._excludeVariableName(\"zSM\");\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync(state.shaderLanguage);\r\n    }\r\n\r\n    private async _initShaderSourceAsync(shaderLanguage: ShaderLanguage) {\r\n        this._codeIsReady = false;\r\n\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            await Promise.all([\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/shadowMapVertexMetric\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/packingFunctions\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/shadowMapFragment\"),\r\n            ]);\r\n        } else {\r\n            await Promise.all([\r\n                import(\"../../../../Shaders/ShadersInclude/shadowMapVertexMetric\"),\r\n                import(\"../../../../Shaders/ShadersInclude/packingFunctions\"),\r\n                import(\"../../../../Shaders/ShadersInclude/shadowMapFragment\"),\r\n            ]);\r\n        }\r\n\r\n        this._codeIsReady = true;\r\n        this.onCodeIsReadyObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the view x projection input component\r\n     */\r\n    public get viewProjection(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the depth output component\r\n     */\r\n    public get depth(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const comments = `//${this.name}`;\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n\r\n        state._emitUniformFromString(\"biasAndScaleSM\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        state._emitUniformFromString(\"lightDataSM\", NodeMaterialBlockConnectionPointTypes.Vector3);\r\n        state._emitUniformFromString(\"depthValuesSM\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n\r\n        state._emitFunctionFromInclude(\"packingFunctions\", comments);\r\n\r\n        state.compilationString += `${state._declareLocalVar(\"worldPos\", NodeMaterialBlockConnectionPointTypes.Vector4)} = ${this.worldPosition.associatedVariableName};\\n`;\r\n        state.compilationString += `${state._declareLocalVar(\"vPositionWSM\", NodeMaterialBlockConnectionPointTypes.Vector3)};\\n`;\r\n        state.compilationString += `${state._declareLocalVar(\"vDepthMetricSM\", NodeMaterialBlockConnectionPointTypes.Float)} = 0.0;\\n`;\r\n        state.compilationString += `${state._declareLocalVar(\"zSM\", NodeMaterialBlockConnectionPointTypes.Float)};\\n`;\r\n\r\n        if (this.worldNormal.isConnected) {\r\n            state.compilationString += `${state._declareLocalVar(\"vNormalW\", NodeMaterialBlockConnectionPointTypes.Vector3)} = ${this.worldNormal.associatedVariableName}.xyz;\\n`;\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowMapVertexNormalBias\", comments);\r\n        }\r\n\r\n        state.compilationString += `${state._declareLocalVar(\"clipPos\", NodeMaterialBlockConnectionPointTypes.Vector4)} = ${this.viewProjection.associatedVariableName} * worldPos;\\n`;\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"shadowMapVertexMetric\", comments, {\r\n            replaceStrings: [\r\n                {\r\n                    search: /gl_Position/g,\r\n                    replace: \"clipPos\",\r\n                },\r\n                {\r\n                    search: /vertexOutputs.position/g,\r\n                    replace: \"clipPos\",\r\n                },\r\n                {\r\n                    search: /vertexOutputs\\.vDepthMetricSM/g,\r\n                    replace: \"vDepthMetricSM\",\r\n                },\r\n            ],\r\n        });\r\n\r\n        state.compilationString += state._emitCodeFromInclude(\"shadowMapFragment\", comments, {\r\n            replaceStrings: [\r\n                {\r\n                    search: /return;/g,\r\n                    replace: \"\",\r\n                },\r\n                {\r\n                    search: /fragmentInputs\\.vDepthMetricSM/g,\r\n                    replace: \"vDepthMetricSM\",\r\n                },\r\n            ],\r\n        });\r\n        const output = isWebGPU ? \"fragmentOutputs.fragDepth\" : \"gl_FragDepth\";\r\n        state.compilationString += `\r\n            #if SM_DEPTHTEXTURE == 1\r\n                #ifdef IS_NDC_HALF_ZRANGE\r\n                    ${output} = (clipPos.z / clipPos.w);\r\n                #else\r\n                    ${output} = (clipPos.z / clipPos.w) * 0.5 + 0.5;\r\n                #endif\r\n            #endif\r\n        `;\r\n\r\n        state.compilationString += `${state._declareOutput(this.depth)} = vec3${state.fSuffix}(depthSM, 1., 1.);\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ShadowMapBlock\", ShadowMapBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;;;;;AAMrD,MAAO,cAAe,0LAAQ,oBAAiB;IAkBjD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAC3C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC5C,KAAK,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACtC,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACvC,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAClC,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,cAA8B,EAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC;;;;aAIjB,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC;;;;aAIjB,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,QAAQ,GAAG,KAAc,CAAE,CAAC,KAAZ,IAAI,CAAC,IAAI;QAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAE9D,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC9F,KAAK,CAAC,sBAAsB,CAAC,aAAa,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC3F,KAAK,CAAC,sBAAsB,CAAC,eAAe,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAE7F,KAAK,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAE7D,KAAK,CAAC,iBAAiB,IAAI,GAA0F,OAAvF,KAAK,CAAC,gBAAgB,CAAC,UAAU,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,OAA+C,IAAK,CAAC,MAA3C,CAAC,aAAa,CAAC,sBAAsB,EAAA;QAC9J,KAAK,CAAC,iBAAiB,IAAI,GAAwF,IAAK,CAAC,EAA3F,KAAK,CAAC,gBAAgB,CAAC,cAAc,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA;QACnH,KAAK,CAAC,iBAAiB,IAAI,GAAwF,OAArF,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,+MAAE,yCAAqC,CAAC,KAAK,CAAC,EAAA,UAAW,CAAC;QAC/H,KAAK,CAAC,iBAAiB,IAAI,GAA6E,OAA1E,KAAK,CAAC,gBAAgB,CAAC,KAAK,gNAAE,wCAAqC,CAAC,KAAK,CAAC,EAAA,IAAK,CAAC;QAE9G,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC/B,KAAK,CAAC,iBAAiB,IAAI,UAAG,KAAK,CAAC,gBAAgB,CAAC,UAAU,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,OAA6C,OAAvC,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAA,QAAS,CAAC;YACtK,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,2BAA2B,EAAE,QAAQ,CAAC,CAAC;QACjG,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,UAAG,KAAK,CAAC,gBAAgB,CAAC,SAAS,gNAAE,wCAAqC,CAAC,OAAO,CAAC,EAAA,OAAgD,OAA1C,IAAI,CAAC,GAAqD,CAAC,UAAxC,CAAC,sBAAsB,EAAA;QAE9J,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,uBAAuB,EAAE,QAAQ,EAAE;YACrF,cAAc,EAAE;gBACZ;oBACI,MAAM,EAAE,cAAc;oBACtB,OAAO,EAAE,SAAS;iBACrB;gBACD;oBACI,MAAM,EAAE,yBAAyB;oBACjC,OAAO,EAAE,SAAS;iBACrB;gBACD;oBACI,MAAM,EAAE,gCAAgC;oBACxC,OAAO,EAAE,gBAAgB;iBAC5B;aACJ;SACJ,CAAC,CAAC;QAEH,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,QAAQ,EAAE;YACjF,cAAc,EAAE;gBACZ;oBACI,MAAM,EAAE,UAAU;oBAClB,OAAO,EAAE,EAAE;iBACd;gBACD;oBACI,MAAM,EAAE,iCAAiC;oBACzC,OAAO,EAAE,gBAAgB;iBAC5B;aACJ;SACJ,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,cAAc,CAAC;QACvE,KAAK,CAAC,iBAAiB,IAAI,0GAKb,MAAM,CAFN,MAAM,EAAA,4EAEA,eAAA;QAKpB,KAAK,CAAC,iBAAiB,IAAI,GAA6C,KAAK,EAA/C,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAA,WAAuB,aAAP,OAAO,EAAA,qBAAsB,CAAC;QAE5G,OAAO,IAAI,CAAC;IAChB,CAAC;IA7JD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/C,IAAI,CAAC,aAAa,CAAC,eAAe,gNAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,aAAa,gNAAE,wCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC1F,IAAI,CAAC,cAAc,CAAC,OAAO,gNAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAE5E,IAAI,CAAC,WAAW,CAAC,0CAA0C,8MACvD,yCAAqC,CAAC,MAAM,gNAAG,yCAAqC,CAAC,OAAO,iNAAG,wCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;CA+IJ;gKAED,gBAAA,AAAa,EAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2037, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/prePassOutputBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/prePassOutputBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to output values on the prepass textures\r\n * @see https://playground.babylonjs.com/#WW65SN#9\r\n */\r\nexport class PrePassOutputBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new PrePassOutputBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.Fragment, true);\r\n\r\n        this.registerInput(\"viewDepth\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"screenDepth\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerInput(\"localPosition\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerInput(\"viewNormal\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerInput(\"reflectivity\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerInput(\"velocity\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n        this.registerInput(\"velocityLinear\", NodeMaterialBlockConnectionPointTypes.AutoDetect, true);\r\n\r\n        this.inputs[2].addExcludedConnectionPointFromAllowedTypes(NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.inputs[3].addExcludedConnectionPointFromAllowedTypes(NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.inputs[4].addExcludedConnectionPointFromAllowedTypes(NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.inputs[5].addExcludedConnectionPointFromAllowedTypes(NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.inputs[6].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Vector3 |\r\n                NodeMaterialBlockConnectionPointTypes.Vector4 |\r\n                NodeMaterialBlockConnectionPointTypes.Color3 |\r\n                NodeMaterialBlockConnectionPointTypes.Color4\r\n        );\r\n        this.inputs[7].addExcludedConnectionPointFromAllowedTypes(NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        this.inputs[8].addExcludedConnectionPointFromAllowedTypes(NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"PrePassOutputBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the view depth component\r\n     */\r\n    public get viewDepth(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the screen depth component\r\n     */\r\n    public get screenDepth(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the world position component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the position in local space component\r\n     */\r\n    public get localPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the view normal component\r\n     */\r\n    public get viewNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the reflectivity component\r\n     */\r\n    public get reflectivity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the velocity component\r\n     */\r\n    public get velocity(): NodeMaterialConnectionPoint {\r\n        return this._inputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the linear velocity component\r\n     */\r\n    public get velocityLinear(): NodeMaterialConnectionPoint {\r\n        return this._inputs[8];\r\n    }\r\n\r\n    private _getFragData(isWebGPU: boolean, index: number) {\r\n        return isWebGPU ? `fragmentOutputs.fragData${index}` : `gl_FragData[${index}]`;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const worldPosition = this.worldPosition;\r\n        const localPosition = this.localPosition;\r\n        const viewNormal = this.viewNormal;\r\n        const worldNormal = this.worldNormal;\r\n        const viewDepth = this.viewDepth;\r\n        const reflectivity = this.reflectivity;\r\n        const screenDepth = this.screenDepth;\r\n        const velocity = this.velocity;\r\n        const velocityLinear = this.velocityLinear;\r\n\r\n        state.sharedData.blocksWithDefines.push(this);\r\n\r\n        const comments = `//${this.name}`;\r\n        const vec4 = state._getShaderType(NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n\r\n        state.compilationString += `#if defined(PREPASS)\\r\\n`;\r\n        state.compilationString += isWebGPU ? `var fragData: array<vec4<f32>, SCENE_MRT_COUNT>;\\r\\n` : `vec4 fragData[SCENE_MRT_COUNT];\\r\\n`;\r\n\r\n        state.compilationString += `#ifdef PREPASS_DEPTH\\r\\n`;\r\n        if (viewDepth.connectedPoint) {\r\n            state.compilationString += ` fragData[PREPASS_DEPTH_INDEX] = ${vec4}(${viewDepth.associatedVariableName}, 0.0, 0.0, 1.0);\\r\\n`;\r\n        } else {\r\n            // We have to write something on the viewDepth output or it will raise a gl error\r\n            state.compilationString += ` fragData[PREPASS_DEPTH_INDEX] = ${vec4}(0.0, 0.0, 0.0, 0.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_SCREENSPACE_DEPTH\\r\\n`;\r\n        if (screenDepth.connectedPoint) {\r\n            state.compilationString += ` gl_FragData[PREPASS_SCREENSPACE_DEPTH_INDEX] = vec4(${screenDepth.associatedVariableName}, 0.0, 0.0, 1.0);\\r\\n`;\r\n        } else {\r\n            // We have to write something on the viewDepth output or it will raise a gl error\r\n            state.compilationString += ` gl_FragData[PREPASS_SCREENSPACE_DEPTH_INDEX] = vec4(0.0, 0.0, 0.0, 0.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_POSITION\\r\\n`;\r\n        if (worldPosition.connectedPoint) {\r\n            state.compilationString += `fragData[PREPASS_POSITION_INDEX] = ${vec4}(${worldPosition.associatedVariableName}.rgb, ${\r\n                worldPosition.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? worldPosition.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the position output or it will raise a gl error\r\n            state.compilationString += ` fragData[PREPASS_POSITION_INDEX] = ${vec4}(0.0, 0.0, 0.0, 0.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_LOCAL_POSITION\\r\\n`;\r\n        if (localPosition.connectedPoint) {\r\n            state.compilationString += ` gl_FragData[PREPASS_LOCAL_POSITION_INDEX] = vec4(${localPosition.associatedVariableName}.rgb, ${\r\n                localPosition.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? localPosition.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the position output or it will raise a gl error\r\n            state.compilationString += ` gl_FragData[PREPASS_LOCAL_POSITION_INDEX] = vec4(0.0, 0.0, 0.0, 0.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_NORMAL\\r\\n`;\r\n        if (viewNormal.connectedPoint) {\r\n            state.compilationString += ` fragData[PREPASS_NORMAL_INDEX] = ${vec4}(${viewNormal.associatedVariableName}.rgb, ${\r\n                viewNormal.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? viewNormal.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the normal output or it will raise a gl error\r\n            state.compilationString += ` fragData[PREPASS_NORMAL_INDEX] = ${vec4}(0.0, 0.0, 0.0, 0.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_WORLD_NORMAL\\r\\n`;\r\n        if (worldNormal.connectedPoint) {\r\n            state.compilationString += ` gl_FragData[PREPASS_WORLD_NORMAL_INDEX] = vec4(${worldNormal.associatedVariableName}.rgb, ${\r\n                worldNormal.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? worldNormal.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the normal output or it will raise a gl error\r\n            state.compilationString += ` gl_FragData[PREPASS_WORLD_NORMAL_INDEX] = vec4(0.0, 0.0, 0.0, 0.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_REFLECTIVITY\\r\\n`;\r\n        if (reflectivity.connectedPoint) {\r\n            state.compilationString += ` fragData[PREPASS_REFLECTIVITY_INDEX] = ${vec4}(${reflectivity.associatedVariableName}.rgb, ${\r\n                reflectivity.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? reflectivity.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the reflectivity output or it will raise a gl error\r\n            state.compilationString += ` fragData[PREPASS_REFLECTIVITY_INDEX] = ${vec4}(0.0, 0.0, 0.0, 1.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_VELOCITY\\r\\n`;\r\n        if (velocity.connectedPoint) {\r\n            state.compilationString += ` fragData[PREPASS_VELOCITY_INDEX] = ${vec4}(${velocity.associatedVariableName}.rgb, ${\r\n                velocity.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? velocity.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the reflectivity output or it will raise a gl error\r\n            state.compilationString += ` fragData[PREPASS_VELOCITY_INDEX] = ${vec4}(0.0, 0.0, 0.0, 1.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#ifdef PREPASS_VELOCITY_LINEAR\\r\\n`;\r\n        if (velocityLinear.connectedPoint) {\r\n            state.compilationString += ` fragData[PREPASS_VELOCITY_LINEAR_INDEX] = ${vec4}(${velocityLinear.associatedVariableName}.rgb, ${\r\n                velocityLinear.connectedPoint.type === NodeMaterialBlockConnectionPointTypes.Vector4 ? velocityLinear.associatedVariableName + \".a\" : \"1.0\"\r\n            });\\r\\n`;\r\n        } else {\r\n            // We have to write something on the reflectivity output or it will raise a gl error\r\n            state.compilationString += ` fragData[PREPASS_VELOCITY_LINEAR_INDEX] = ${vec4}(0.0, 0.0, 0.0, 1.0);\\r\\n`;\r\n        }\r\n        state.compilationString += `#endif\\r\\n`;\r\n\r\n        state.compilationString += `#if SCENE_MRT_COUNT > 1\\r\\n`;\r\n        state.compilationString += `${this._getFragData(isWebGPU, 1)} = fragData[1];\\r\\n`;\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#if SCENE_MRT_COUNT > 2\\r\\n`;\r\n        state.compilationString += `${this._getFragData(isWebGPU, 2)} = fragData[2];\\r\\n`;\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#if SCENE_MRT_COUNT > 3\\r\\n`;\r\n        state.compilationString += `${this._getFragData(isWebGPU, 3)} = fragData[3];\\r\\n`;\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#if SCENE_MRT_COUNT > 4\\r\\n`;\r\n        state.compilationString += `${this._getFragData(isWebGPU, 4)} = fragData[4];\\r\\n`;\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#if SCENE_MRT_COUNT > 5\\r\\n`;\r\n        state.compilationString += `${this._getFragData(isWebGPU, 5)} = fragData[5];\\r\\n`;\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#if SCENE_MRT_COUNT > 6\\r\\n`;\r\n        state.compilationString += `${this._getFragData(isWebGPU, 6)} = fragData[6];\\r\\n`;\r\n        state.compilationString += `#endif\\r\\n`;\r\n        state.compilationString += `#if SCENE_MRT_COUNT > 7\\r\\n`;\r\n        state.compilationString += `${this._getFragData(isWebGPU, 7)} = fragData[7];\\r\\n`;\r\n        state.compilationString += `#endif\\r\\n`;\r\n\r\n        state.compilationString += `#endif\\r\\n`;\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PrePassOutputBlock\", PrePassOutputBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;;;;;AAOrD,MAAO,kBAAmB,0LAAQ,oBAAiB;IAgCrD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAEO,YAAY,CAAC,QAAiB,EAAE,KAAa,EAAA;QACjD,OAAO,QAAQ,CAAC,CAAC,CAAC,2BAAgC,CAAE,CAAC,CAAC,IAAT,KAAK,IAAK,eAAoB,OAAL,KAAK,EAAA,EAAG,CAAC;IACnF,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAE3C,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9C,MAAM,QAAQ,GAAG,KAAc,CAAE,CAAC,KAAZ,IAAI,CAAC,IAAI;QAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,+MAAC,wCAAqC,CAAC,OAAO,CAAC,CAAC;QACjF,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,KAAK,CAAC,iBAAiB,IAAI,yBAA0B,CAAC;QACtD,KAAK,CAAC,iBAAiB,IAAI,QAAQ,CAAC,CAAC,CAAC,qDAAsD,CAAC,CAAC,CAAC,CAAA,oCAAqC,CAAC;QAErI,KAAK,CAAC,iBAAiB,IAAI,yBAA0B,CAAC;QACtD,IAAI,SAAS,CAAC,cAAc,EAAE,CAAC;YAC3B,KAAK,CAAC,iBAAiB,IAAI,2CAAoC,IAAI,EAAA,KAAoC,OAAhC,SAAS,CAAC,KAA6C,CAAC,gBAAxB,EAAA;QAC3G,CAAC,MAAM,CAAC;YACJ,iFAAiF;YACjF,KAAK,CAAC,iBAAiB,IAAI,oCAAwC,OAAJ,IAAI,EAAA,0BAA2B,CAAC;QACnG,CAAC;QACD,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,qCAAsC,CAAC;QAClE,IAAI,WAAW,CAAC,cAAc,EAAE,CAAC;YAC7B,KAAK,CAAC,iBAAiB,IAAI,wDAA0F,OAAlC,WAAW,CAAC,sBAAsB,EAAA,sBAAuB,CAAC;QACjJ,CAAC,MAAM,CAAC;YACJ,iFAAiF;YACjF,KAAK,CAAC,iBAAiB,IAAI,8EAA+E,CAAC;QAC/G,CAAC;QACD,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,4BAA6B,CAAC;QACzD,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;YAC/B,KAAK,CAAC,iBAAiB,IAAI,6CAAsC,IAAI,EAAA,KACjE,OADqE,MACxD,OADqE,CAAC,sBAAsB,EAAA,UAE7G,OAAQ,CAAC,aADS,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KACxI,EAAA;QACJ,CAAC,MAAM,CAAC;YACJ,gFAAgF;YAChF,KAAK,CAAC,iBAAiB,IAAI,uCAA2C,OAAJ,IAAI,EAAA,0BAA2B,CAAC;QACtG,CAAC;QACD,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,kCAAmC,CAAC;QAC/D,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;YAC/B,KAAK,CAAC,iBAAiB,IAAI,4DAAqD,aAAa,CAAC,sBAAsB,EAAA,UAEpH,OADI,aAAa,CAAC,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KACxI,EAAA,OAAQ,CAAC;QACb,CAAC,MAAM,CAAC;YACJ,gFAAgF;YAChF,KAAK,CAAC,iBAAiB,IAAI,2EAA4E,CAAC;QAC5G,CAAC;QACD,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,0BAA2B,CAAC;QACvD,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;YAC5B,KAAK,CAAC,iBAAiB,IAAI,4CAAqC,IAAI,EAAA,KAChE,OADoE,GAC1D,OADoE,CAAC,sBAAsB,EAAA,UAEzG,OAAQ,CAAC,UADM,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KAClI,EAAA;QACJ,CAAC,MAAM,CAAC;YACJ,8EAA8E;YAC9E,KAAK,CAAC,iBAAiB,IAAI,qCAAyC,OAAJ,IAAI,EAAA,0BAA2B,CAAC;QACpG,CAAC;QACD,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,gCAAiC,CAAC;QAC7D,IAAI,WAAW,CAAC,cAAc,EAAE,CAAC;YAC7B,KAAK,CAAC,iBAAiB,IAAI,0DAAmD,WAAW,CAAC,sBAAsB,EAAA,UAEhH,OADI,WAAW,CAAC,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KACpI,EAAA,OAAQ,CAAC;QACb,CAAC,MAAM,CAAC;YACJ,8EAA8E;YAC9E,KAAK,CAAC,iBAAiB,IAAI,yEAA0E,CAAC;QAC1G,CAAC;QACD,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,gCAAiC,CAAC;QAC7D,IAAI,YAAY,CAAC,cAAc,EAAE,CAAC;YAC9B,KAAK,CAAC,iBAAiB,IAAI,kDAA2C,IAAI,EAAA,KACtE,OAD0E,KAC9D,OAD0E,CAAC,sBAAsB,EAAA,UAEjH,oBADiB,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KACtI,EAAA,OAAQ,CAAC;QACb,CAAC,MAAM,CAAC;YACJ,oFAAoF;YACpF,KAAK,CAAC,iBAAiB,IAAI,2CAA+C,OAAJ,IAAI,EAAA,0BAA2B,CAAC;QAC1G,CAAC;QACD,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,4BAA6B,CAAC;QACzD,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC1B,KAAK,CAAC,iBAAiB,IAAI,uCAA+C,OAAR,CAAgB,GAAZ,EAAA,qBAAa,sBAAsB,EAAA,UAEzG,OADI,AACI,CAAC,OADG,CAAC,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KAC9H,EAAA;QACJ,CAAC,MAAM,CAAC;YACJ,oFAAoF;YACpF,KAAK,CAAC,iBAAiB,IAAI,uCAA2C,OAAJ,IAAI,EAAA,0BAA2B,CAAC;QACtG,CAAC;QACD,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,mCAAoC,CAAC;QAChE,IAAI,cAAc,CAAC,cAAc,EAAE,CAAC;YAChC,KAAK,CAAC,iBAAiB,IAAI,qDAA8C,IAAI,EAAA,KACzE,OAD6E,OAC/D,OAD6E,CAAC,sBAAsB,EAAA,UAEtH,sBADmB,cAAc,CAAC,IAAI,mNAAK,wCAAqC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC,CAAC,KAC1I,EAAA,OAAQ,CAAC;QACb,CAAC,MAAM,CAAC;YACJ,oFAAoF;YACpF,KAAK,CAAC,iBAAiB,IAAI,8CAAkD,OAAJ,IAAI,EAAA,aAA2B,CAAC;QAC7G,CAAC;QACD,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QAExC,KAAK,CAAC,iBAAiB,IAAI,4BAA6B,CAAC;QACzD,KAAK,CAAC,iBAAiB,IAAI,GAAiC,OAA9B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAA,oBAAqB,CAAC;QAClF,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,4BAA6B,CAAC;QACzD,KAAK,CAAC,iBAAiB,IAAI,GAAiC,OAA9B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAA,oBAAqB,CAAC;QAClF,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,4BAA6B,CAAC;QACzD,KAAK,CAAC,iBAAiB,IAAI,GAAiC,OAA9B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAA,oBAAqB,CAAC;QAClF,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,4BAA6B,CAAC;QACzD,KAAK,CAAC,iBAAiB,IAAI,GAAiC,OAA9B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAA,oBAAqB,CAAC;QAClF,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,4BAA6B,CAAC;QACzD,KAAK,CAAC,iBAAiB,IAAI,GAAiC,OAA9B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAA,oBAAqB,CAAC;QAClF,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,4BAA6B,CAAC;QACzD,KAAK,CAAC,iBAAiB,IAAI,GAAiC,OAA9B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAA,oBAAqB,CAAC;QAClF,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QACxC,KAAK,CAAC,iBAAiB,IAAI,4BAA6B,CAAC;QACzD,KAAK,CAAC,iBAAiB,IAAI,GAAiC,OAA9B,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAA,oBAAqB,CAAC;QAClF,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QAExC,KAAK,CAAC,iBAAiB,IAAI,WAAY,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAjPD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,mMAAE,2BAAwB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,aAAa,CAAC,WAAW,gNAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,aAAa,gNAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACrF,IAAI,CAAC,aAAa,CAAC,eAAe,gNAAE,wCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5F,IAAI,CAAC,aAAa,CAAC,eAAe,gNAAE,wCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5F,IAAI,CAAC,aAAa,CAAC,YAAY,gNAAE,wCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACzF,IAAI,CAAC,aAAa,CAAC,aAAa,gNAAE,wCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,cAAc,gNAAE,wCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC3F,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,sPAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACvF,IAAI,CAAC,aAAa,CAAC,gBAAgB,gNAAE,wCAAqC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAE7F,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MAAC,wCAAqC,CAAC,OAAO,GAAG,sPAAqC,CAAC,OAAO,CAAC,CAAC;QACzJ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MAAC,wCAAqC,CAAC,OAAO,iNAAG,wCAAqC,CAAC,OAAO,CAAC,CAAC;QACzJ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MAAC,wCAAqC,CAAC,OAAO,iNAAG,wCAAqC,CAAC,OAAO,CAAC,CAAC;QACzJ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MAAC,wCAAqC,CAAC,OAAO,GAAG,sPAAqC,CAAC,OAAO,CAAC,CAAC;QACzJ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MACrD,wCAAqC,CAAC,OAAO,iNACzC,wCAAqC,CAAC,OAAO,GAC7C,sPAAqC,CAAC,MAAM,iNAC5C,wCAAqC,CAAC,MAAM,CACnD,CAAC;QACF,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MAAC,wCAAqC,CAAC,OAAO,iNAAG,wCAAqC,CAAC,OAAO,CAAC,CAAC;QACzJ,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C,+MAAC,wCAAqC,CAAC,OAAO,iNAAG,wCAAqC,CAAC,OAAO,CAAC,CAAC;IAC7J,CAAC;CAqNJ;gKAED,gBAAA,AAAa,EAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Fragment/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Fragment/index.ts"], "sourcesContent": ["export * from \"./fragmentOutputBlock\";\r\nexport * from \"./smartFilterFragmentOutputBlock\";\r\nexport * from \"./imageProcessingBlock\";\r\nexport * from \"./perturbNormalBlock\";\r\nexport * from \"./discardBlock\";\r\nexport * from \"./frontFacingBlock\";\r\nexport * from \"./derivativeBlock\";\r\nexport * from \"./fragCoordBlock\";\r\nexport * from \"./screenSizeBlock\";\r\nexport * from \"./screenSpaceBlock\";\r\nexport * from \"./twirlBlock\";\r\nexport * from \"./TBNBlock\";\r\nexport * from \"./heightToNormalBlock\";\r\nexport * from \"./fragDepthBlock\";\r\nexport * from \"./shadowMapBlock\";\r\nexport * from \"./prePassOutputBlock\";\r\n\r\n// async-loaded shaders\r\n\r\n// imageProcessingBlock\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/helperFunctions\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/imageProcessingDeclaration\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/imageProcessingFunctions\";\r\nexport * from \"../../../../Shaders/ShadersInclude/helperFunctions\";\r\nexport * from \"../../../../Shaders/ShadersInclude/imageProcessingDeclaration\";\r\nexport * from \"../../../../Shaders/ShadersInclude/imageProcessingFunctions\";\r\n\r\n// perturbNormalBlock\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/bumpFragment\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/bumpFragmentMainFunctions\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/bumpFragmentFunctions\";\r\nexport * from \"../../../../Shaders/ShadersInclude/bumpFragment\";\r\nexport * from \"../../../../Shaders/ShadersInclude/bumpFragmentMainFunctions\";\r\nexport * from \"../../../../Shaders/ShadersInclude/bumpFragmentFunctions\";\r\n\r\n// shadowMapBlock\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/shadowMapVertexMetric\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/packingFunctions\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/shadowMapFragment\";\r\nexport * from \"../../../../Shaders/ShadersInclude/shadowMapVertexMetric\";\r\nexport * from \"../../../../Shaders/ShadersInclude/packingFunctions\";\r\nexport * from \"../../../../Shaders/ShadersInclude/shadowMapFragment\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,uBAAuB,CAAC;AACtC,cAAc,kCAAkC,CAAC;AACjD,cAAc,wBAAwB,CAAC;AACvC,cAAc,sBAAsB,CAAC;AACrC,cAAc,gBAAgB,CAAC;AAC/B,cAAc,oBAAoB,CAAC;AACnC,cAAc,mBAAmB,CAAC;AAClC,cAAc,kBAAkB,CAAC;AACjC,cAAc,mBAAmB,CAAC;AAClC,cAAc,oBAAoB,CAAC;AACnC,cAAc,cAAc,CAAC;AAC7B,cAAc,YAAY,CAAC;AAC3B,cAAc,uBAAuB,CAAC;AACtC,cAAc,kBAAkB,CAAC;AACjC,cAAc,kBAAkB,CAAC;AACjC,cAAc,sBAAsB,CAAC;AAErC,uBAAuB;AAEvB,uBAAuB;AACvB,cAAc,wDAAwD,CAAC;AACvE,cAAc,mEAAmE,CAAC;AAClF,cAAc,iEAAiE,CAAC;AAChF,cAAc,oDAAoD,CAAC;AACnE,cAAc,+DAA+D,CAAC;AAC9E,cAAc,6DAA6D,CAAC;AAE5E,qBAAqB;AACrB,cAAc,qDAAqD,CAAC;AACpE,cAAc,kEAAkE,CAAC;AACjF,cAAc,8DAA8D,CAAC;AAC7E,cAAc,iDAAiD,CAAC;AAChE,cAAc,8DAA8D,CAAC;AAC7E,cAAc,0DAA0D,CAAC;AAEzE,iBAAiB;AACjB,cAAc,8DAA8D,CAAC;AAC7E,cAAc,yDAAyD,CAAC;AACxE,cAAc,0DAA0D,CAAC;AACzE,cAAc,0DAA0D,CAAC;AACzE,cAAc,qDAAqD,CAAC;AACpE,cAAc,sDAAsD,CAAC", "debugId": null}}]}