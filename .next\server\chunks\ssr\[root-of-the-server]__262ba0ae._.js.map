{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/Footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/BabylonBackground.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/BabylonBackground.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/BabylonBackground.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/BabylonBackground.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/BabylonBackground.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/BabylonBackground.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ProjectCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProjectCard.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProjectCard.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ProjectCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ProjectCard.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProjectCard.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/SkillCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/SkillCard.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SkillCard.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/SkillCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/SkillCard.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/SkillCard.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport BabylonBackground from \"@/components/BabylonBackground\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport ProjectCard from \"@/components/ProjectCard\";\nimport SkillCard from \"@/components/SkillCard\";\n\nexport default function Home() {\n  // Sample data - in a real app, these would be fetched from your content system\n  const skills = [\n    {\n      name: \"Accounting & Finance\",\n      level: 90,\n      category: \"accounting\" as const,\n    },\n    { name: \"Data Analysis\", level: 85, category: \"data\" as const },\n    { name: \"Graphic Design\", level: 80, category: \"design\" as const },\n    { name: \"Web Development\", level: 75, category: \"web\" as const },\n  ];\n\n  const projects = [\n    {\n      id: \"cyberpunk-portfolio\",\n      title: \"Cyberpunk Portfolio\",\n      description:\n        \"Interactive portfolio website with 3D graphics and cyberpunk aesthetics built with Next.js and Babylon.js\",\n      image: \"/assets/Coming Soon.png\",\n      category: \"web\" as const,\n      tags: [\"Next.js\", \"Babylon.js\", \"TypeScript\", \"Tailwind CSS\"],\n      links: {\n        demo: \"#\",\n        github: \"https://github.com/trinanda\",\n        live: \"#\",\n      },\n    },\n  ];\n\n  return (\n    <>\n      <BabylonBackground\n        particleCount={1000}\n        enableInteraction={true}\n        colorScheme=\"cyberpunk\"\n      />\n      <Header />\n\n      <main id=\"main-content\">\n        {/* Hero Section */}\n        <section\n          id=\"home\"\n          className=\"min-h-screen flex items-center justify-center relative overflow-hidden pt-20\"\n          aria-labelledby=\"hero-heading\"\n        >\n          <div className=\"container mx-auto px-6 py-20 relative z-10\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n              {/* Hero Content */}\n              <div className=\"space-y-8\">\n                <div className=\"space-y-6\">\n                  <div\n                    className=\"text-sm text-cyan-400 tracking-widest uppercase font-orbitron\"\n                    aria-label=\"Welcome message\"\n                  >\n                    &gt; WELCOME TO THE MATRIX\n                  </div>\n\n                  <h1\n                    id=\"hero-heading\"\n                    className=\"text-4xl md:text-6xl lg:text-7xl font-bold leading-tight\"\n                  >\n                    <span className=\"text-white\">I&apos;M </span>\n                    <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-pink-400 to-cyan-400 neon-glow glitch font-orbitron\">\n                      TRINANDA\n                    </span>\n                  </h1>\n\n                  <div className=\"space-y-2\">\n                    <p className=\"text-xl md:text-2xl text-gray-300 font-light\">\n                      Sharia Accounting Student\n                    </p>\n                    <p className=\"text-lg text-pink-400 font-mono\">\n                      &lt; Accountant | Designer | Data Analyst /&gt;\n                    </p>\n                  </div>\n\n                  <p className=\"text-gray-400 text-lg leading-relaxed max-w-2xl\">\n                    Passionate about merging traditional accounting principles\n                    with cutting-edge technology. Specializing in data analysis,\n                    graphic design, and creating digital solutions for financial\n                    challenges.\n                  </p>\n                </div>\n\n                <div className=\"flex flex-col sm:flex-row gap-4\">\n                  <Button\n                    size=\"lg\"\n                    className=\"bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold px-8 py-3 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-cyan-400/25\"\n                  >\n                    View My Work\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"lg\"\n                    className=\"border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black font-semibold px-8 py-3 rounded-lg transition-all duration-300\"\n                  >\n                    Download CV\n                  </Button>\n                </div>\n              </div>\n\n              {/* Hero Image */}\n              <div className=\"relative\">\n                <div className=\"relative w-full max-w-md mx-auto\">\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-cyan-400 to-pink-400 rounded-full blur-3xl opacity-20 animate-pulse\"></div>\n                  <div className=\"relative bg-gradient-to-br from-gray-800 to-gray-900 rounded-full p-8 border border-cyan-400/30\">\n                    <Image\n                      src=\"/assets/My Profile.png\"\n                      alt=\"Muhammad Trinanda Profile\"\n                      width={400}\n                      height={400}\n                      className=\"rounded-full w-full h-full object-cover\"\n                      priority\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Skills Section */}\n        <section id=\"skills\" className=\"py-20 relative\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-4 font-orbitron\">\n                <span className=\"text-cyan-400\">&lt;</span>\n                SKILLS\n                <span className=\"text-pink-400\">/&gt;</span>\n              </h2>\n              <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n                My expertise spans across multiple domains, combining\n                traditional accounting knowledge with modern technology skills.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {skills.map((skill, index) => (\n                <SkillCard key={skill.name} skill={skill} index={index} />\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Projects Section */}\n        <section id=\"projects\" className=\"py-20 relative\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-4 font-orbitron\">\n                <span className=\"text-cyan-400\">&lt;</span>\n                PROJECTS\n                <span className=\"text-pink-400\">/&gt;</span>\n              </h2>\n              <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n                Showcasing my latest work in web development, data analysis, and\n                design projects.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {projects.map((project, index) => (\n                <ProjectCard key={project.id} project={project} index={index} />\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Contact Section */}\n        <section id=\"contact\" className=\"py-20 relative\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-4 font-orbitron\">\n                <span className=\"text-cyan-400\">&lt;</span>\n                CONTACT\n                <span className=\"text-pink-400\">/&gt;</span>\n              </h2>\n              <p className=\"text-gray-400 text-lg max-w-2xl mx-auto\">\n                Ready to collaborate? Let&apos;s create something amazing\n                together.\n              </p>\n            </div>\n\n            <Card className=\"max-w-2xl mx-auto bg-gray-900/50 border-cyan-500/30\">\n              <CardContent className=\"p-8\">\n                <div className=\"text-center space-y-6\">\n                  <div className=\"text-6xl mb-4\">🚀</div>\n                  <h3 className=\"text-2xl font-bold text-white\">\n                    Let&apos;s Connect!\n                  </h3>\n                  <p className=\"text-gray-400\">\n                    I&apos;m always open to discussing new opportunities,\n                    collaborations, or just having a chat about technology and\n                    innovation.\n                  </p>\n                  <Button\n                    size=\"lg\"\n                    className=\"bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-400 hover:to-rose-400 text-white font-semibold px-8 py-3 rounded-lg transition-all duration-300 hover:shadow-lg hover:shadow-pink-400/25\"\n                  >\n                    Get In Touch\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </section>\n      </main>\n\n      <Footer />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,+EAA+E;IAC/E,MAAM,SAAS;QACb;YACE,MAAM;YACN,OAAO;YACP,UAAU;QACZ;QACA;YAAE,MAAM;YAAiB,OAAO;YAAI,UAAU;QAAgB;QAC9D;YAAE,MAAM;YAAkB,OAAO;YAAI,UAAU;QAAkB;QACjE;YAAE,MAAM;YAAmB,OAAO;YAAI,UAAU;QAAe;KAChE;IAED,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,OAAO;YACP,UAAU;YACV,MAAM;gBAAC;gBAAW;gBAAc;gBAAc;aAAe;YAC7D,OAAO;gBACL,MAAM;gBACN,QAAQ;gBACR,MAAM;YACR;QACF;KACD;IAED,qBACE;;0BACE,8OAAC,uIAAA,CAAA,UAAiB;gBAChB,eAAe;gBACf,mBAAmB;gBACnB,aAAY;;;;;;0BAEd,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,IAAG;;kCAEP,8OAAC;wBACC,IAAG;wBACH,WAAU;wBACV,mBAAgB;kCAEhB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAU;wDACV,cAAW;kEACZ;;;;;;kEAID,8OAAC;wDACC,IAAG;wDACH,WAAU;;0EAEV,8OAAC;gEAAK,WAAU;0EAAa;;;;;;0EAC7B,8OAAC;gEAAK,WAAU;0EAAuH;;;;;;;;;;;;kEAKzI,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA+C;;;;;;0EAG5D,8OAAC;gEAAE,WAAU;0EAAkC;;;;;;;;;;;;kEAKjD,8OAAC;wDAAE,WAAU;kEAAkD;;;;;;;;;;;;0DAQjE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,WAAU;kEACX;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;kDAOL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,WAAU;wDACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUtB,8OAAC;wBAAQ,IAAG;wBAAS,WAAU;kCAC7B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAW;8DAE3C,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAMzD,8OAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,+HAAA,CAAA,UAAS;4CAAkB,OAAO;4CAAO,OAAO;2CAAjC,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;kCAOlC,8OAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAW;8DAE3C,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAMzD,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,iIAAA,CAAA,UAAW;4CAAkB,SAAS;4CAAS,OAAO;2CAArC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;kCAOpC,8OAAC;wBAAQ,IAAG;wBAAU,WAAU;kCAC9B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAW;8DAE3C,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAMzD,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAG9C,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAK7B,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUb,8OAAC,4HAAA,CAAA,UAAM;;;;;;;AAGb", "debugId": null}}]}