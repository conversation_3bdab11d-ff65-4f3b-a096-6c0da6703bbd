{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Bones/bone.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Bones/bone.ts"], "sourcesContent": ["import type { Skeleton } from \"./skeleton\";\r\nimport { Vector3, Quaternion, Matrix, TmpVectors } from \"../Maths/math.vector\";\r\nimport { BuildArray } from \"../Misc/arrayTools\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Node } from \"../node\";\r\nimport { Space } from \"../Maths/math.axis\";\r\n\r\nimport type { Animation } from \"../Animations/animation\";\r\nimport type { AnimationPropertiesOverride } from \"../Animations/animationPropertiesOverride\";\r\n\r\n/**\r\n * Class used to store bone information\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons\r\n */\r\nexport class Bone extends Node {\r\n    private static _TmpVecs: Vector3[] = BuildArray(2, Vector3.Zero);\r\n    private static _TmpQuat = Quaternion.Identity();\r\n    private static _TmpMats: Matrix[] = BuildArray(5, Matrix.Identity);\r\n\r\n    /**\r\n     * Gets the list of child bones\r\n     */\r\n    public children: Bone[] = [];\r\n\r\n    /** Gets the animations associated with this bone */\r\n    public override animations: Animation[] = [];\r\n\r\n    /**\r\n     * Gets or sets bone length\r\n     */\r\n    public length: number;\r\n\r\n    /**\r\n     * @internal Internal only\r\n     * Set this value to map this bone to a different index in the transform matrices\r\n     * Set this value to -1 to exclude the bone from the transform matrices\r\n     */\r\n    public _index: Nullable<number> = null;\r\n\r\n    private _skeleton: Skeleton;\r\n    private _localMatrix: Matrix; // transformation of the bone, in local space\r\n    private _absoluteMatrix: Matrix; // transformation of the bone, in world space (relative to the skeleton root)\r\n    private _bindMatrix: Matrix; // the bind matrix, in local space\r\n    private _absoluteBindMatrix: Matrix; // the bind matrix, in world space (relative to the skeleton root)\r\n    private _absoluteInverseBindMatrix: Matrix; // the inverse of the bind matrix, in world space (relative to the skeleton root)\r\n    private _finalMatrix: Matrix; // the final matrix used to transform vertices of the mesh according to the bone, in world space (relative to the skeleton root). It is the multiplication of _absoluteInverseBindMatrix with _absoluteMatrix.\r\n    private _restMatrix: Matrix; // a matrix for the exclusive use of the end user (not used internally by the framework), in local space\r\n    private _scalingDeterminant = 1;\r\n\r\n    private _localScaling: Vector3;\r\n    private _localRotation: Quaternion;\r\n    private _localPosition: Vector3;\r\n    private _needToDecompose = true;\r\n    private _needToCompose = false;\r\n\r\n    /** @internal */\r\n    public _linkedTransformNode: Nullable<TransformNode> = null;\r\n\r\n    /** @internal */\r\n    public _waitingTransformNodeId: Nullable<string> = null;\r\n\r\n    /** @internal */\r\n    get _matrix(): Matrix {\r\n        this._compose();\r\n        return this._localMatrix;\r\n    }\r\n\r\n    /** @internal */\r\n    set _matrix(value: Matrix) {\r\n        // skip if the matrices are the same\r\n        if (value.updateFlag === this._localMatrix.updateFlag && !this._needToCompose) {\r\n            return;\r\n        }\r\n\r\n        this._needToCompose = false; // in case there was a pending compose\r\n\r\n        this._localMatrix.copyFrom(value);\r\n        this._markAsDirtyAndDecompose();\r\n    }\r\n\r\n    /**\r\n     * Create a new bone\r\n     * @param name defines the bone name\r\n     * @param skeleton defines the parent skeleton\r\n     * @param parentBone defines the parent (can be null if the bone is the root)\r\n     * @param localMatrix defines the local matrix (default: identity)\r\n     * @param restMatrix defines the rest matrix (default: localMatrix)\r\n     * @param bindMatrix defines the bind matrix (default: localMatrix)\r\n     * @param index defines index of the bone in the hierarchy (default: null)\r\n     */\r\n    constructor(\r\n        /**\r\n         * defines the bone name\r\n         */\r\n        public override name: string,\r\n        skeleton: Skeleton,\r\n        parentBone: Nullable<Bone> = null,\r\n        localMatrix: Nullable<Matrix> = null,\r\n        restMatrix: Nullable<Matrix> = null,\r\n        bindMatrix: Nullable<Matrix> = null,\r\n        index: Nullable<number> = null\r\n    ) {\r\n        super(name, skeleton.getScene(), false);\r\n        this._skeleton = skeleton;\r\n        this._localMatrix = localMatrix?.clone() ?? Matrix.Identity();\r\n        this._restMatrix = restMatrix ?? this._localMatrix.clone();\r\n        this._bindMatrix = bindMatrix ?? this._localMatrix.clone();\r\n        this._index = index;\r\n\r\n        this._absoluteMatrix = new Matrix();\r\n        this._absoluteBindMatrix = new Matrix();\r\n        this._absoluteInverseBindMatrix = new Matrix();\r\n        this._finalMatrix = new Matrix();\r\n\r\n        skeleton.bones.push(this);\r\n\r\n        this.setParent(parentBone, false);\r\n\r\n        this._updateAbsoluteBindMatrices();\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"Bone\";\r\n    }\r\n\r\n    // Members\r\n\r\n    /**\r\n     * Gets the parent skeleton\r\n     * @returns a skeleton\r\n     */\r\n    public getSkeleton(): Skeleton {\r\n        return this._skeleton;\r\n    }\r\n\r\n    public override get parent(): Bone {\r\n        return this._parentNode as Bone;\r\n    }\r\n\r\n    /**\r\n     * Gets parent bone\r\n     * @returns a bone or null if the bone is the root of the bone hierarchy\r\n     */\r\n    public getParent(): Nullable<Bone> {\r\n        return this.parent;\r\n    }\r\n\r\n    /**\r\n     * Returns an array containing the children of the bone\r\n     * @returns an array containing the children of the bone (can be empty if the bone has no children)\r\n     */\r\n    public override getChildren(): Array<Bone> {\r\n        return this.children;\r\n    }\r\n\r\n    /**\r\n     * Gets the node index in matrix array generated for rendering\r\n     * @returns the node index\r\n     */\r\n    public getIndex(): number {\r\n        return this._index === null ? this.getSkeleton().bones.indexOf(this) : this._index;\r\n    }\r\n\r\n    public override set parent(newParent: Nullable<Bone>) {\r\n        this.setParent(newParent);\r\n    }\r\n\r\n    /**\r\n     * Sets the parent bone\r\n     * @param parent defines the parent (can be null if the bone is the root)\r\n     * @param updateAbsoluteBindMatrices defines if the absolute bind and absolute inverse bind matrices must be updated\r\n     */\r\n    public setParent(parent: Nullable<Bone>, updateAbsoluteBindMatrices: boolean = true): void {\r\n        if (this.parent === parent) {\r\n            return;\r\n        }\r\n\r\n        if (this.parent) {\r\n            const index = this.parent.children.indexOf(this);\r\n            if (index !== -1) {\r\n                this.parent.children.splice(index, 1);\r\n            }\r\n        }\r\n\r\n        this._parentNode = parent;\r\n\r\n        if (this.parent) {\r\n            this.parent.children.push(this);\r\n        }\r\n\r\n        if (updateAbsoluteBindMatrices) {\r\n            this._updateAbsoluteBindMatrices();\r\n        }\r\n\r\n        this.markAsDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets the local matrix\r\n     * @returns the local matrix\r\n     */\r\n    public getLocalMatrix(): Matrix {\r\n        this._compose();\r\n        return this._localMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the bind matrix\r\n     * @returns the bind matrix\r\n     */\r\n    public getBindMatrix(): Matrix {\r\n        return this._bindMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the bind matrix.\r\n     * @returns the bind matrix\r\n     * @deprecated Please use getBindMatrix instead\r\n     */\r\n    public getBaseMatrix(): Matrix {\r\n        return this.getBindMatrix();\r\n    }\r\n\r\n    /**\r\n     * Gets the rest matrix\r\n     * @returns the rest matrix\r\n     */\r\n    public getRestMatrix(): Matrix {\r\n        return this._restMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the rest matrix\r\n     * @returns the rest matrix\r\n     * @deprecated Please use getRestMatrix instead\r\n     */\r\n    public getRestPose(): Matrix {\r\n        return this.getRestMatrix();\r\n    }\r\n\r\n    /**\r\n     * Sets the rest matrix\r\n     * @param matrix the local-space rest matrix to set for this bone\r\n     */\r\n    public setRestMatrix(matrix: Matrix): void {\r\n        this._restMatrix.copyFrom(matrix);\r\n    }\r\n\r\n    /**\r\n     * Sets the rest matrix\r\n     * @param matrix the local-space rest to set for this bone\r\n     * @deprecated Please use setRestMatrix instead\r\n     */\r\n    public setRestPose(matrix: Matrix): void {\r\n        this.setRestMatrix(matrix);\r\n    }\r\n\r\n    /**\r\n     * Gets the bind matrix\r\n     * @returns the bind matrix\r\n     * @deprecated Please use getBindMatrix instead\r\n     */\r\n    public getBindPose(): Matrix {\r\n        return this.getBindMatrix();\r\n    }\r\n\r\n    /**\r\n     * Sets the bind matrix\r\n     * This will trigger a recomputation of the absolute bind and absolute inverse bind matrices for this bone and its children\r\n     * Note that the local matrix will also be set with the matrix passed in parameter!\r\n     * @param matrix the local-space bind matrix to set for this bone\r\n     */\r\n    public setBindMatrix(matrix: Matrix): void {\r\n        this.updateMatrix(matrix);\r\n    }\r\n\r\n    /**\r\n     * Sets the bind matrix\r\n     * @param matrix the local-space bind to set for this bone\r\n     * @deprecated Please use setBindMatrix instead\r\n     */\r\n    public setBindPose(matrix: Matrix): void {\r\n        this.setBindMatrix(matrix);\r\n    }\r\n\r\n    /**\r\n     * Gets the matrix used to store the final world transformation of the bone (ie. the matrix sent to shaders)\r\n     * @returns the final world matrix\r\n     */\r\n    public getFinalMatrix(): Matrix {\r\n        return this._finalMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the matrix used to store the final world transformation of the bone (ie. the matrix sent to shaders)\r\n     * @deprecated Please use getFinalMatrix instead\r\n     * @returns the final world matrix\r\n     */\r\n    public override getWorldMatrix(): Matrix {\r\n        return this.getFinalMatrix();\r\n    }\r\n\r\n    /**\r\n     * Sets the local matrix to the rest matrix\r\n     */\r\n    public returnToRest(): void {\r\n        if (this._linkedTransformNode) {\r\n            const localScaling = TmpVectors.Vector3[0];\r\n            const localRotation = TmpVectors.Quaternion[0];\r\n            const localPosition = TmpVectors.Vector3[1];\r\n\r\n            this.getRestMatrix().decompose(localScaling, localRotation, localPosition);\r\n\r\n            this._linkedTransformNode.position.copyFrom(localPosition);\r\n            this._linkedTransformNode.rotationQuaternion = this._linkedTransformNode.rotationQuaternion ?? Quaternion.Identity();\r\n            this._linkedTransformNode.rotationQuaternion.copyFrom(localRotation);\r\n            this._linkedTransformNode.scaling.copyFrom(localScaling);\r\n        } else {\r\n            this._matrix = this._restMatrix;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the inverse of the bind matrix, in world space (relative to the skeleton root)\r\n     * @returns the inverse bind matrix, in world space\r\n     */\r\n    public getAbsoluteInverseBindMatrix(): Matrix {\r\n        return this._absoluteInverseBindMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the inverse of the bind matrix, in world space (relative to the skeleton root)\r\n     * @returns the inverse bind matrix, in world space\r\n     * @deprecated Please use getAbsoluteInverseBindMatrix instead\r\n     */\r\n    public getInvertedAbsoluteTransform(): Matrix {\r\n        return this.getAbsoluteInverseBindMatrix();\r\n    }\r\n\r\n    /**\r\n     * Gets the bone matrix, in world space (relative to the skeleton root)\r\n     * @returns the bone matrix, in world space\r\n     */\r\n    public getAbsoluteMatrix(): Matrix {\r\n        return this._absoluteMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the bone matrix, in world space (relative to the skeleton root)\r\n     * @returns the bone matrix, in world space\r\n     * @deprecated Please use getAbsoluteMatrix instead\r\n     */\r\n    public getAbsoluteTransform(): Matrix {\r\n        return this._absoluteMatrix;\r\n    }\r\n\r\n    /**\r\n     * Links with the given transform node.\r\n     * The local matrix of this bone is overwritten by the transform of the node every frame.\r\n     * @param transformNode defines the transform node to link to\r\n     */\r\n    public linkTransformNode(transformNode: Nullable<TransformNode>): void {\r\n        if (this._linkedTransformNode) {\r\n            this._skeleton._numBonesWithLinkedTransformNode--;\r\n        }\r\n\r\n        this._linkedTransformNode = transformNode;\r\n\r\n        if (this._linkedTransformNode) {\r\n            this._skeleton._numBonesWithLinkedTransformNode++;\r\n        }\r\n    }\r\n\r\n    // Properties (matches TransformNode properties)\r\n\r\n    /**\r\n     * Gets the node used to drive the bone's transformation\r\n     * @returns a transform node or null\r\n     */\r\n    public getTransformNode() {\r\n        return this._linkedTransformNode;\r\n    }\r\n\r\n    /** Gets or sets current position (in local space) */\r\n    public get position(): Vector3 {\r\n        this._decompose();\r\n        return this._localPosition;\r\n    }\r\n\r\n    public set position(newPosition: Vector3) {\r\n        this._decompose();\r\n        this._localPosition.copyFrom(newPosition);\r\n\r\n        this._markAsDirtyAndCompose();\r\n    }\r\n\r\n    /** Gets or sets current rotation (in local space) */\r\n    public get rotation(): Vector3 {\r\n        return this.getRotation();\r\n    }\r\n\r\n    public set rotation(newRotation: Vector3) {\r\n        this.setRotation(newRotation);\r\n    }\r\n\r\n    /** Gets or sets current rotation quaternion (in local space) */\r\n    public get rotationQuaternion() {\r\n        this._decompose();\r\n        return this._localRotation;\r\n    }\r\n\r\n    public set rotationQuaternion(newRotation: Quaternion) {\r\n        this.setRotationQuaternion(newRotation);\r\n    }\r\n\r\n    /** Gets or sets current scaling (in local space) */\r\n    public get scaling(): Vector3 {\r\n        return this.getScale();\r\n    }\r\n\r\n    public set scaling(newScaling: Vector3) {\r\n        this.setScale(newScaling);\r\n    }\r\n\r\n    /**\r\n     * Gets the animation properties override\r\n     */\r\n    public override get animationPropertiesOverride(): Nullable<AnimationPropertiesOverride> {\r\n        return this._skeleton.animationPropertiesOverride;\r\n    }\r\n\r\n    // Methods\r\n    private _decompose() {\r\n        if (!this._needToDecompose) {\r\n            return;\r\n        }\r\n\r\n        this._needToDecompose = false;\r\n\r\n        if (!this._localScaling) {\r\n            this._localScaling = Vector3.Zero();\r\n            this._localRotation = Quaternion.Zero();\r\n            this._localPosition = Vector3.Zero();\r\n        }\r\n        this._localMatrix.decompose(this._localScaling, this._localRotation, this._localPosition);\r\n    }\r\n\r\n    private _compose() {\r\n        if (!this._needToCompose) {\r\n            return;\r\n        }\r\n\r\n        if (!this._localScaling) {\r\n            this._needToCompose = false;\r\n            return;\r\n        }\r\n\r\n        this._needToCompose = false;\r\n        Matrix.ComposeToRef(this._localScaling, this._localRotation, this._localPosition, this._localMatrix);\r\n    }\r\n\r\n    /**\r\n     * Update the bind (and optionally the local) matrix\r\n     * @param bindMatrix defines the new matrix to set to the bind/local matrix, in local space\r\n     * @param updateAbsoluteBindMatrices defines if the absolute bind and absolute inverse bind matrices must be recomputed (default: true)\r\n     * @param updateLocalMatrix defines if the local matrix should also be updated with the matrix passed in parameter (default: true)\r\n     */\r\n    public updateMatrix(bindMatrix: Matrix, updateAbsoluteBindMatrices = true, updateLocalMatrix = true): void {\r\n        this._bindMatrix.copyFrom(bindMatrix);\r\n\r\n        if (updateAbsoluteBindMatrices) {\r\n            this._updateAbsoluteBindMatrices();\r\n        }\r\n\r\n        if (updateLocalMatrix) {\r\n            this._matrix = bindMatrix;\r\n        } else {\r\n            this.markAsDirty();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _updateAbsoluteBindMatrices(bindMatrix?: Matrix, updateChildren = true): void {\r\n        if (!bindMatrix) {\r\n            bindMatrix = this._bindMatrix;\r\n        }\r\n\r\n        if (this.parent) {\r\n            bindMatrix.multiplyToRef(this.parent._absoluteBindMatrix, this._absoluteBindMatrix);\r\n        } else {\r\n            this._absoluteBindMatrix.copyFrom(bindMatrix);\r\n        }\r\n\r\n        this._absoluteBindMatrix.invertToRef(this._absoluteInverseBindMatrix);\r\n\r\n        if (updateChildren) {\r\n            for (let index = 0; index < this.children.length; index++) {\r\n                this.children[index]._updateAbsoluteBindMatrices();\r\n            }\r\n        }\r\n\r\n        this._scalingDeterminant = this._absoluteBindMatrix.determinant() < 0 ? -1 : 1;\r\n    }\r\n\r\n    /**\r\n     * Flag the bone as dirty (Forcing it to update everything)\r\n     * @returns this bone\r\n     */\r\n    public override markAsDirty(): Bone {\r\n        this._currentRenderId++;\r\n        this._childUpdateId++;\r\n        this._skeleton._markAsDirty();\r\n        return this;\r\n    }\r\n\r\n    /** @internal */\r\n    public _markAsDirtyAndCompose() {\r\n        this.markAsDirty();\r\n        this._needToCompose = true;\r\n    }\r\n\r\n    private _markAsDirtyAndDecompose() {\r\n        this.markAsDirty();\r\n        this._needToDecompose = true;\r\n    }\r\n\r\n    private _updatePosition(vec: Vector3, space = Space.LOCAL, tNode?: TransformNode, translationMode = true): void {\r\n        const lm = this.getLocalMatrix();\r\n\r\n        if (space == Space.LOCAL) {\r\n            if (translationMode) {\r\n                lm.addAtIndex(12, vec.x);\r\n                lm.addAtIndex(13, vec.y);\r\n                lm.addAtIndex(14, vec.z);\r\n            } else {\r\n                lm.setTranslationFromFloats(vec.x, vec.y, vec.z);\r\n            }\r\n        } else {\r\n            let wm: Nullable<Matrix> = null;\r\n\r\n            //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\r\n            if (tNode) {\r\n                wm = tNode.getWorldMatrix();\r\n            }\r\n\r\n            this._skeleton.computeAbsoluteMatrices();\r\n\r\n            const tmat = Bone._TmpMats[0];\r\n            const tvec = Bone._TmpVecs[0];\r\n\r\n            if (this.parent) {\r\n                if (tNode && wm) {\r\n                    tmat.copyFrom(this.parent.getAbsoluteMatrix());\r\n                    tmat.multiplyToRef(wm, tmat);\r\n                } else {\r\n                    tmat.copyFrom(this.parent.getAbsoluteMatrix());\r\n                }\r\n            } else {\r\n                Matrix.IdentityToRef(tmat);\r\n            }\r\n\r\n            if (translationMode) {\r\n                tmat.setTranslationFromFloats(0, 0, 0);\r\n            }\r\n            tmat.invert();\r\n            Vector3.TransformCoordinatesToRef(vec, tmat, tvec);\r\n\r\n            if (translationMode) {\r\n                lm.addAtIndex(12, tvec.x);\r\n                lm.addAtIndex(13, tvec.y);\r\n                lm.addAtIndex(14, tvec.z);\r\n            } else {\r\n                lm.setTranslationFromFloats(tvec.x, tvec.y, tvec.z);\r\n            }\r\n        }\r\n\r\n        this._markAsDirtyAndDecompose();\r\n    }\r\n\r\n    /**\r\n     * Translate the bone in local or world space\r\n     * @param vec The amount to translate the bone\r\n     * @param space The space that the translation is in (default: Space.LOCAL)\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     */\r\n    public translate(vec: Vector3, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        this._updatePosition(vec, space, tNode, true);\r\n    }\r\n\r\n    /**\r\n     * Set the position of the bone in local or world space\r\n     * @param position The position to set the bone\r\n     * @param space The space that the position is in (default: Space.LOCAL)\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     */\r\n    public setPosition(position: Vector3, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        this._updatePosition(position, space, tNode, false);\r\n    }\r\n\r\n    /**\r\n     * Set the absolute position of the bone (world space)\r\n     * @param position The position to set the bone\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     */\r\n    public setAbsolutePosition(position: Vector3, tNode?: TransformNode) {\r\n        this.setPosition(position, Space.WORLD, tNode);\r\n    }\r\n\r\n    /**\r\n     * Scale the bone on the x, y and z axes (in local space)\r\n     * @param x The amount to scale the bone on the x axis\r\n     * @param y The amount to scale the bone on the y axis\r\n     * @param z The amount to scale the bone on the z axis\r\n     * @param scaleChildren sets this to true if children of the bone should be scaled as well (false by default)\r\n     */\r\n    public scale(x: number, y: number, z: number, scaleChildren = false): void {\r\n        const locMat = this.getLocalMatrix();\r\n\r\n        // Apply new scaling on top of current local matrix\r\n        const scaleMat = Bone._TmpMats[0];\r\n        Matrix.ScalingToRef(x, y, z, scaleMat);\r\n        scaleMat.multiplyToRef(locMat, locMat);\r\n\r\n        // Invert scaling matrix and apply the inverse to all children\r\n        scaleMat.invert();\r\n\r\n        for (const child of this.children) {\r\n            const cm = child.getLocalMatrix();\r\n            cm.multiplyToRef(scaleMat, cm);\r\n            cm.multiplyAtIndex(12, x);\r\n            cm.multiplyAtIndex(13, y);\r\n            cm.multiplyAtIndex(14, z);\r\n\r\n            child._markAsDirtyAndDecompose();\r\n        }\r\n\r\n        this._markAsDirtyAndDecompose();\r\n\r\n        if (scaleChildren) {\r\n            for (const child of this.children) {\r\n                child.scale(x, y, z, scaleChildren);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the bone scaling in local space\r\n     * @param scale defines the scaling vector\r\n     */\r\n    public setScale(scale: Vector3): void {\r\n        this._decompose();\r\n        this._localScaling.copyFrom(scale);\r\n        this._markAsDirtyAndCompose();\r\n    }\r\n\r\n    /**\r\n     * Gets the current scaling in local space\r\n     * @returns the current scaling vector\r\n     */\r\n    public getScale(): Vector3 {\r\n        this._decompose();\r\n        return this._localScaling;\r\n    }\r\n\r\n    /**\r\n     * Gets the current scaling in local space and stores it in a target vector\r\n     * @param result defines the target vector\r\n     */\r\n    public getScaleToRef(result: Vector3) {\r\n        this._decompose();\r\n        result.copyFrom(this._localScaling);\r\n    }\r\n\r\n    /**\r\n     * Set the yaw, pitch, and roll of the bone in local or world space\r\n     * @param yaw The rotation of the bone on the y axis\r\n     * @param pitch The rotation of the bone on the x axis\r\n     * @param roll The rotation of the bone on the z axis\r\n     * @param space The space that the axes of rotation are in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     */\r\n    public setYawPitchRoll(yaw: number, pitch: number, roll: number, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        if (space === Space.LOCAL) {\r\n            const quat = Bone._TmpQuat;\r\n            Quaternion.RotationYawPitchRollToRef(yaw, pitch, roll, quat);\r\n            this.setRotationQuaternion(quat, space, tNode);\r\n            return;\r\n        }\r\n\r\n        const rotMatInv = Bone._TmpMats[0];\r\n        if (!this._getAbsoluteInverseMatrixUnscaledToRef(rotMatInv, tNode)) {\r\n            return;\r\n        }\r\n\r\n        const rotMat = Bone._TmpMats[1];\r\n        Matrix.RotationYawPitchRollToRef(yaw, pitch, roll, rotMat);\r\n\r\n        rotMatInv.multiplyToRef(rotMat, rotMat);\r\n        this._rotateWithMatrix(rotMat, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Add a rotation to the bone on an axis in local or world space\r\n     * @param axis The axis to rotate the bone on\r\n     * @param amount The amount to rotate the bone\r\n     * @param space The space that the axis is in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     */\r\n    public rotate(axis: Vector3, amount: number, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        const rmat = Bone._TmpMats[0];\r\n        rmat.setTranslationFromFloats(0, 0, 0);\r\n        Matrix.RotationAxisToRef(axis, amount, rmat);\r\n        this._rotateWithMatrix(rmat, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Set the rotation of the bone to a particular axis angle in local or world space\r\n     * @param axis The axis to rotate the bone on\r\n     * @param angle The angle that the bone should be rotated to\r\n     * @param space The space that the axis is in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     */\r\n    public setAxisAngle(axis: Vector3, angle: number, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        if (space === Space.LOCAL) {\r\n            const quat = Bone._TmpQuat;\r\n            Quaternion.RotationAxisToRef(axis, angle, quat);\r\n\r\n            this.setRotationQuaternion(quat, space, tNode);\r\n            return;\r\n        }\r\n\r\n        const rotMatInv = Bone._TmpMats[0];\r\n        if (!this._getAbsoluteInverseMatrixUnscaledToRef(rotMatInv, tNode)) {\r\n            return;\r\n        }\r\n\r\n        const rotMat = Bone._TmpMats[1];\r\n        Matrix.RotationAxisToRef(axis, angle, rotMat);\r\n\r\n        rotMatInv.multiplyToRef(rotMat, rotMat);\r\n        this._rotateWithMatrix(rotMat, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Set the euler rotation of the bone in local or world space\r\n     * @param rotation The euler rotation that the bone should be set to\r\n     * @param space The space that the rotation is in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     */\r\n    public setRotation(rotation: Vector3, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        this.setYawPitchRoll(rotation.y, rotation.x, rotation.z, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Set the quaternion rotation of the bone in local or world space\r\n     * @param quat The quaternion rotation that the bone should be set to\r\n     * @param space The space that the rotation is in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     */\r\n    public setRotationQuaternion(quat: Quaternion, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        if (space === Space.LOCAL) {\r\n            this._decompose();\r\n            this._localRotation.copyFrom(quat);\r\n\r\n            this._markAsDirtyAndCompose();\r\n\r\n            return;\r\n        }\r\n\r\n        const rotMatInv = Bone._TmpMats[0];\r\n        if (!this._getAbsoluteInverseMatrixUnscaledToRef(rotMatInv, tNode)) {\r\n            return;\r\n        }\r\n\r\n        const rotMat = Bone._TmpMats[1];\r\n        Matrix.FromQuaternionToRef(quat, rotMat);\r\n\r\n        rotMatInv.multiplyToRef(rotMat, rotMat);\r\n\r\n        this._rotateWithMatrix(rotMat, space, tNode);\r\n    }\r\n\r\n    /**\r\n     * Set the rotation matrix of the bone in local or world space\r\n     * @param rotMat The rotation matrix that the bone should be set to\r\n     * @param space The space that the rotation is in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     */\r\n    public setRotationMatrix(rotMat: Matrix, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        if (space === Space.LOCAL) {\r\n            const quat = Bone._TmpQuat;\r\n            Quaternion.FromRotationMatrixToRef(rotMat, quat);\r\n            this.setRotationQuaternion(quat, space, tNode);\r\n            return;\r\n        }\r\n\r\n        const rotMatInv = Bone._TmpMats[0];\r\n        if (!this._getAbsoluteInverseMatrixUnscaledToRef(rotMatInv, tNode)) {\r\n            return;\r\n        }\r\n\r\n        const rotMat2 = Bone._TmpMats[1];\r\n        rotMat2.copyFrom(rotMat);\r\n\r\n        rotMatInv.multiplyToRef(rotMat, rotMat2);\r\n\r\n        this._rotateWithMatrix(rotMat2, space, tNode);\r\n    }\r\n\r\n    private _rotateWithMatrix(rmat: Matrix, space = Space.LOCAL, tNode?: TransformNode): void {\r\n        const lmat = this.getLocalMatrix();\r\n        const lx = lmat.m[12];\r\n        const ly = lmat.m[13];\r\n        const lz = lmat.m[14];\r\n        const parent = this.getParent();\r\n        const parentScale = Bone._TmpMats[3];\r\n        const parentScaleInv = Bone._TmpMats[4];\r\n\r\n        if (parent && space == Space.WORLD) {\r\n            if (tNode) {\r\n                parentScale.copyFrom(tNode.getWorldMatrix());\r\n                parent.getAbsoluteMatrix().multiplyToRef(parentScale, parentScale);\r\n            } else {\r\n                parentScale.copyFrom(parent.getAbsoluteMatrix());\r\n            }\r\n            parentScaleInv.copyFrom(parentScale);\r\n            parentScaleInv.invert();\r\n            lmat.multiplyToRef(parentScale, lmat);\r\n            lmat.multiplyToRef(rmat, lmat);\r\n            lmat.multiplyToRef(parentScaleInv, lmat);\r\n        } else {\r\n            if (space == Space.WORLD && tNode) {\r\n                parentScale.copyFrom(tNode.getWorldMatrix());\r\n                parentScaleInv.copyFrom(parentScale);\r\n                parentScaleInv.invert();\r\n                lmat.multiplyToRef(parentScale, lmat);\r\n                lmat.multiplyToRef(rmat, lmat);\r\n                lmat.multiplyToRef(parentScaleInv, lmat);\r\n            } else {\r\n                lmat.multiplyToRef(rmat, lmat);\r\n            }\r\n        }\r\n\r\n        lmat.setTranslationFromFloats(lx, ly, lz);\r\n\r\n        this.computeAbsoluteMatrices();\r\n        this._markAsDirtyAndDecompose();\r\n    }\r\n\r\n    private _getAbsoluteInverseMatrixUnscaledToRef(rotMatInv: Matrix, tNode?: TransformNode): boolean {\r\n        const scaleMatrix = Bone._TmpMats[2];\r\n        rotMatInv.copyFrom(this.getAbsoluteMatrix());\r\n\r\n        if (tNode) {\r\n            rotMatInv.multiplyToRef(tNode.getWorldMatrix(), rotMatInv);\r\n            Matrix.ScalingToRef(tNode.scaling.x, tNode.scaling.y, tNode.scaling.z, scaleMatrix);\r\n        } else {\r\n            Matrix.IdentityToRef(scaleMatrix);\r\n        }\r\n\r\n        rotMatInv.invert();\r\n        if (isNaN(rotMatInv.m[0])) {\r\n            // Matrix failed to invert.\r\n            // This can happen if scale is zero for example.\r\n            return false;\r\n        }\r\n\r\n        scaleMatrix.multiplyAtIndex(0, this._scalingDeterminant);\r\n        rotMatInv.multiplyToRef(scaleMatrix, rotMatInv);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Get the position of the bone in local or world space\r\n     * @param space The space that the returned position is in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @returns The position of the bone\r\n     */\r\n    public getPosition(space = Space.LOCAL, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const pos = Vector3.Zero();\r\n\r\n        this.getPositionToRef(space, tNode, pos);\r\n\r\n        return pos;\r\n    }\r\n\r\n    /**\r\n     * Copy the position of the bone to a vector3 in local or world space\r\n     * @param space The space that the returned position is in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @param result The vector3 to copy the position to\r\n     */\r\n    public getPositionToRef(space = Space.LOCAL, tNode: Nullable<TransformNode>, result: Vector3): void {\r\n        if (space == Space.LOCAL) {\r\n            const lm = this.getLocalMatrix();\r\n\r\n            result.x = lm.m[12];\r\n            result.y = lm.m[13];\r\n            result.z = lm.m[14];\r\n        } else {\r\n            let wm: Nullable<Matrix> = null;\r\n\r\n            //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\r\n            if (tNode) {\r\n                wm = tNode.getWorldMatrix();\r\n            }\r\n\r\n            this._skeleton.computeAbsoluteMatrices();\r\n\r\n            let tmat = Bone._TmpMats[0];\r\n\r\n            if (tNode && wm) {\r\n                tmat.copyFrom(this.getAbsoluteMatrix());\r\n                tmat.multiplyToRef(wm, tmat);\r\n            } else {\r\n                tmat = this.getAbsoluteMatrix();\r\n            }\r\n\r\n            result.x = tmat.m[12];\r\n            result.y = tmat.m[13];\r\n            result.z = tmat.m[14];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the absolute position of the bone (world space)\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @returns The absolute position of the bone\r\n     */\r\n    public getAbsolutePosition(tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const pos = Vector3.Zero();\r\n\r\n        this.getPositionToRef(Space.WORLD, tNode, pos);\r\n\r\n        return pos;\r\n    }\r\n\r\n    /**\r\n     * Copy the absolute position of the bone (world space) to the result param\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @param result The vector3 to copy the absolute position to\r\n     */\r\n    public getAbsolutePositionToRef(tNode: TransformNode, result: Vector3) {\r\n        this.getPositionToRef(Space.WORLD, tNode, result);\r\n    }\r\n\r\n    /**\r\n     * Compute the absolute matrices of this bone and its children\r\n     */\r\n    public computeAbsoluteMatrices(): void {\r\n        this._compose();\r\n\r\n        if (this.parent) {\r\n            this._localMatrix.multiplyToRef(this.parent._absoluteMatrix, this._absoluteMatrix);\r\n        } else {\r\n            this._absoluteMatrix.copyFrom(this._localMatrix);\r\n\r\n            const poseMatrix = this._skeleton.getPoseMatrix();\r\n\r\n            if (poseMatrix) {\r\n                this._absoluteMatrix.multiplyToRef(poseMatrix, this._absoluteMatrix);\r\n            }\r\n        }\r\n\r\n        const children = this.children;\r\n        const len = children.length;\r\n\r\n        for (let i = 0; i < len; i++) {\r\n            children[i].computeAbsoluteMatrices();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Compute the absolute matrices of this bone and its children\r\n     * @deprecated Please use computeAbsoluteMatrices instead\r\n     */\r\n    public computeAbsoluteTransforms(): void {\r\n        this.computeAbsoluteMatrices();\r\n    }\r\n\r\n    /**\r\n     * Get the world direction from an axis that is in the local space of the bone\r\n     * @param localAxis The local direction that is used to compute the world direction\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @returns The world direction\r\n     */\r\n    public getDirection(localAxis: Vector3, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getDirectionToRef(localAxis, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Copy the world direction to a vector3 from an axis that is in the local space of the bone\r\n     * @param localAxis The local direction that is used to compute the world direction\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @param result The vector3 that the world direction will be copied to\r\n     */\r\n    public getDirectionToRef(localAxis: Vector3, tNode: Nullable<TransformNode> = null, result: Vector3): void {\r\n        let wm: Nullable<Matrix> = null;\r\n\r\n        //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\r\n        if (tNode) {\r\n            wm = tNode.getWorldMatrix();\r\n        }\r\n\r\n        this._skeleton.computeAbsoluteMatrices();\r\n\r\n        const mat = Bone._TmpMats[0];\r\n\r\n        mat.copyFrom(this.getAbsoluteMatrix());\r\n\r\n        if (tNode && wm) {\r\n            mat.multiplyToRef(wm, mat);\r\n        }\r\n\r\n        Vector3.TransformNormalToRef(localAxis, mat, result);\r\n\r\n        result.normalize();\r\n    }\r\n\r\n    /**\r\n     * Get the euler rotation of the bone in local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @returns The euler rotation\r\n     */\r\n    public getRotation(space = Space.LOCAL, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getRotationToRef(space, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Copy the euler rotation of the bone to a vector3.  The rotation can be in either local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @param result The vector3 that the rotation should be copied to\r\n     */\r\n    public getRotationToRef(space = Space.LOCAL, tNode: Nullable<TransformNode> = null, result: Vector3): void {\r\n        const quat = Bone._TmpQuat;\r\n\r\n        this.getRotationQuaternionToRef(space, tNode, quat);\r\n\r\n        quat.toEulerAnglesToRef(result);\r\n    }\r\n\r\n    /**\r\n     * Get the quaternion rotation of the bone in either local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @returns The quaternion rotation\r\n     */\r\n    public getRotationQuaternion(space = Space.LOCAL, tNode: Nullable<TransformNode> = null): Quaternion {\r\n        const result = Quaternion.Identity();\r\n\r\n        this.getRotationQuaternionToRef(space, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Copy the quaternion rotation of the bone to a quaternion.  The rotation can be in either local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @param result The quaternion that the rotation should be copied to\r\n     */\r\n    public getRotationQuaternionToRef(space = Space.LOCAL, tNode: Nullable<TransformNode> = null, result: Quaternion): void {\r\n        if (space == Space.LOCAL) {\r\n            this._decompose();\r\n            result.copyFrom(this._localRotation);\r\n        } else {\r\n            const mat = Bone._TmpMats[0];\r\n            const amat = this.getAbsoluteMatrix();\r\n\r\n            if (tNode) {\r\n                amat.multiplyToRef(tNode.getWorldMatrix(), mat);\r\n            } else {\r\n                mat.copyFrom(amat);\r\n            }\r\n\r\n            mat.multiplyAtIndex(0, this._scalingDeterminant);\r\n            mat.multiplyAtIndex(1, this._scalingDeterminant);\r\n            mat.multiplyAtIndex(2, this._scalingDeterminant);\r\n\r\n            mat.decompose(undefined, result, undefined);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the rotation matrix of the bone in local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @returns The rotation matrix\r\n     */\r\n    public getRotationMatrix(space = Space.LOCAL, tNode: TransformNode): Matrix {\r\n        const result = Matrix.Identity();\r\n\r\n        this.getRotationMatrixToRef(space, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Copy the rotation matrix of the bone to a matrix.  The rotation can be in either local or world space\r\n     * @param space The space that the rotation should be in\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @param result The quaternion that the rotation should be copied to\r\n     */\r\n    public getRotationMatrixToRef(space = Space.LOCAL, tNode: TransformNode, result: Matrix): void {\r\n        if (space == Space.LOCAL) {\r\n            this.getLocalMatrix().getRotationMatrixToRef(result);\r\n        } else {\r\n            const mat = Bone._TmpMats[0];\r\n            const amat = this.getAbsoluteMatrix();\r\n\r\n            if (tNode) {\r\n                amat.multiplyToRef(tNode.getWorldMatrix(), mat);\r\n            } else {\r\n                mat.copyFrom(amat);\r\n            }\r\n\r\n            mat.multiplyAtIndex(0, this._scalingDeterminant);\r\n            mat.multiplyAtIndex(1, this._scalingDeterminant);\r\n            mat.multiplyAtIndex(2, this._scalingDeterminant);\r\n\r\n            mat.getRotationMatrixToRef(result);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the world position of a point that is in the local space of the bone\r\n     * @param position The local position\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @returns The world position\r\n     */\r\n    public getAbsolutePositionFromLocal(position: Vector3, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getAbsolutePositionFromLocalToRef(position, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Get the world position of a point that is in the local space of the bone and copy it to the result param\r\n     * @param position The local position\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @param result The vector3 that the world position should be copied to\r\n     */\r\n    public getAbsolutePositionFromLocalToRef(position: Vector3, tNode: Nullable<TransformNode> = null, result: Vector3): void {\r\n        let wm: Nullable<Matrix> = null;\r\n\r\n        //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\r\n        if (tNode) {\r\n            wm = tNode.getWorldMatrix();\r\n        }\r\n\r\n        this._skeleton.computeAbsoluteMatrices();\r\n\r\n        const tmat = Bone._TmpMats[0];\r\n\r\n        tmat.copyFrom(this.getAbsoluteMatrix());\r\n\r\n        if (tNode && wm) {\r\n            tmat.multiplyToRef(wm, tmat);\r\n        }\r\n\r\n        Vector3.TransformCoordinatesToRef(position, tmat, result);\r\n    }\r\n\r\n    /**\r\n     * Get the local position of a point that is in world space\r\n     * @param position The world position\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @returns The local position\r\n     */\r\n    public getLocalPositionFromAbsolute(position: Vector3, tNode: Nullable<TransformNode> = null): Vector3 {\r\n        const result = Vector3.Zero();\r\n\r\n        this.getLocalPositionFromAbsoluteToRef(position, tNode, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Get the local position of a point that is in world space and copy it to the result param\r\n     * @param position The world position\r\n     * @param tNode A TransformNode whose world matrix is to be applied to the calculated absolute matrix. In most cases, you'll want to pass the mesh associated with the skeleton from which this bone comes. Used only when space=Space.WORLD\r\n     * @param result The vector3 that the local position should be copied to\r\n     */\r\n    public getLocalPositionFromAbsoluteToRef(position: Vector3, tNode: Nullable<TransformNode> = null, result: Vector3): void {\r\n        let wm: Nullable<Matrix> = null;\r\n\r\n        //tNode.getWorldMatrix() needs to be called before skeleton.computeAbsoluteMatrices()\r\n        if (tNode) {\r\n            wm = tNode.getWorldMatrix();\r\n        }\r\n\r\n        this._skeleton.computeAbsoluteMatrices();\r\n\r\n        const tmat = Bone._TmpMats[0];\r\n\r\n        tmat.copyFrom(this.getAbsoluteMatrix());\r\n\r\n        if (tNode && wm) {\r\n            tmat.multiplyToRef(wm, tmat);\r\n        }\r\n\r\n        tmat.invert();\r\n\r\n        Vector3.TransformCoordinatesToRef(position, tmat, result);\r\n    }\r\n\r\n    /**\r\n     * Set the current local matrix as the restMatrix for this bone.\r\n     */\r\n    public setCurrentPoseAsRest(): void {\r\n        this.setRestMatrix(this.getLocalMatrix());\r\n    }\r\n\r\n    /**\r\n     * Releases associated resources\r\n     */\r\n    public override dispose(): void {\r\n        this._linkedTransformNode = null;\r\n\r\n        const index = this._skeleton.bones.indexOf(this);\r\n        if (index !== -1) {\r\n            this._skeleton.bones.splice(index, 1);\r\n        }\r\n\r\n        if (this._parentNode && (this._parentNode as Bone).children) {\r\n            const children = (this._parentNode as Bone).children;\r\n            const index = children.indexOf(this);\r\n            if (index !== -1) {\r\n                children.splice(index, 1);\r\n            }\r\n        }\r\n\r\n        super.dispose();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAC/E,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;;;;AAUzB,MAAO,IAAK,SAAQ,mJAAI;IA+C1B,cAAA,EAAgB,CAChB,IAAI,OAAO,GAAA;QACP,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAI,OAAO,CAAC,KAAa,EAAA;QACrB,oCAAoC;QACpC,IAAI,KAAK,CAAC,UAAU,KAAK,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5E,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC,sCAAsC;QAEnE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED;;;;;;;;;OASG,CACH,YACI;;OAEG,CACa,IAAY,EAC5B,QAAkB,EAClB,aAA6B,IAAI,EACjC,cAAgC,IAAI,EACpC,aAA+B,IAAI,EACnC,aAA+B,IAAI,EACnC,QAA0B,IAAI,CAAA;QAE9B,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QARxB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QA3EhC;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAW,EAAE,CAAC;QAE7B,kDAAA,EAAoD,CACpC,IAAA,CAAA,UAAU,GAAgB,EAAE,CAAC;QAO7C;;;;WAIG,CACI,IAAA,CAAA,MAAM,GAAqB,IAAI,CAAC;QAU/B,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC;QAKxB,IAAA,CAAA,gBAAgB,GAAG,IAAI,CAAC;QACxB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAE/B,cAAA,EAAgB,CACT,IAAA,CAAA,oBAAoB,GAA4B,IAAI,CAAC;QAE5D,cAAA,EAAgB,CACT,IAAA,CAAA,uBAAuB,GAAqB,IAAI,CAAC;QA4CpD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,WAAW,EAAE,KAAK,EAAE,mKAAI,SAAM,CAAC,QAAQ,EAAE,CAAC;QAC9D,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC3D,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC3D,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,eAAe,GAAG,mKAAI,SAAM,EAAE,CAAC;QACpC,IAAI,CAAC,mBAAmB,GAAG,mKAAI,SAAM,EAAE,CAAC;QACxC,IAAI,CAAC,0BAA0B,GAAG,mKAAI,SAAM,EAAE,CAAC;QAC/C,IAAI,CAAC,YAAY,GAAG,mKAAI,SAAM,EAAE,CAAC;QAEjC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAElC,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,UAAU;IAEV;;;OAGG,CACI,WAAW,GAAA;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAoB,MAAM,GAAA;QACtB,OAAO,IAAI,CAAC,WAAmB,CAAC;IACpC,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;;OAGG,CACa,WAAW,GAAA;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;OAGG,CACI,QAAQ,GAAA;QACX,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACvF,CAAC;IAED,IAAoB,MAAM,CAAC,SAAyB,EAAA;QAChD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG,CACI,SAAS,CAAC,MAAsB,EAAE,6BAAsC,IAAI,EAAA;QAC/E,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAE1B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,0BAA0B,EAAE,CAAC;YAC7B,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG,CACI,aAAa,GAAA;QAChB,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG,CACI,WAAW,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG,CACI,aAAa,CAAC,MAAc,EAAA;QAC/B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,MAAc,EAAA;QAC7B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAED;;;;OAIG,CACI,WAAW,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC;IAChC,CAAC;IAED;;;;;OAKG,CACI,aAAa,CAAC,MAAc,EAAA;QAC/B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,MAAc,EAAA;QAC7B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC/B,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG,CACa,cAAc,GAAA;QAC1B,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG,CACI,YAAY,GAAA;QACf,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,MAAM,YAAY,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC3C,MAAM,aAAa,kKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,aAAa,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAE5C,IAAI,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,YAAY,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;YAE3E,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC3D,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,mKAAI,aAAU,CAAC,QAAQ,EAAE,CAAC;YACrH,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YACrE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC7D,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,4BAA4B,GAAA;QAC/B,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED;;;;OAIG,CACI,4BAA4B,GAAA;QAC/B,OAAO,IAAI,CAAC,4BAA4B,EAAE,CAAC;IAC/C,CAAC;IAED;;;OAGG,CACI,iBAAiB,GAAA;QACpB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;;OAIG,CACI,oBAAoB,GAAA;QACvB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,aAAsC,EAAA;QAC3D,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,gCAAgC,EAAE,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC;QAE1C,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,gCAAgC,EAAE,CAAC;QACtD,CAAC;IACL,CAAC;IAED,gDAAgD;IAEhD;;;OAGG,CACI,gBAAgB,GAAA;QACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,mDAAA,EAAqD,CACrD,IAAW,QAAQ,GAAA;QACf,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,QAAQ,CAAC,WAAoB,EAAA;QACpC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAE1C,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED,mDAAA,EAAqD,CACrD,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC9B,CAAC;IAED,IAAW,QAAQ,CAAC,WAAoB,EAAA;QACpC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAClC,CAAC;IAED,8DAAA,EAAgE,CAChE,IAAW,kBAAkB,GAAA;QACzB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,kBAAkB,CAAC,WAAuB,EAAA;QACjD,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;IAC5C,CAAC;IAED,kDAAA,EAAoD,CACpD,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC3B,CAAC;IAED,IAAW,OAAO,CAAC,UAAmB,EAAA;QAClC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAoB,2BAA2B,GAAA;QAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC;IACtD,CAAC;IAED,UAAU;IACF,UAAU,GAAA;QACd,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,cAAc,kKAAG,aAAU,CAAC,IAAI,EAAE,CAAC;YACxC,IAAI,CAAC,cAAc,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC9F,CAAC;IAEO,QAAQ,GAAA;QACZ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;uKAC5B,SAAM,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACzG,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,UAAkB,EAAE,0BAA0B,GAAG,IAAI,EAAE,iBAAiB,GAAG,IAAI,EAAA;QAC/F,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEtC,IAAI,0BAA0B,EAAE,CAAC;YAC7B,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC;QAED,IAAI,iBAAiB,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;QAC9B,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;IACL,CAAC;IAED;;OAEG,CACI,2BAA2B,CAAC,UAAmB,EAAE,cAAc,GAAG,IAAI,EAAA;QACzE,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACxF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAEtE,IAAI,cAAc,EAAE,CAAC;YACjB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACxD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,2BAA2B,EAAE,CAAC;YACvD,CAAC;QACL,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC;IAED;;;OAGG,CACa,WAAW,GAAA;QACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACT,sBAAsB,GAAA;QACzB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEO,wBAAwB,GAAA;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACjC,CAAC;IAEO,eAAe,CAAC,GAAY,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAE,eAAe,GAAG,IAAI,EAAA;QACpG,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEjC,IAAI,KAAK,IAAA,EAAA,eAAA,EAAe,GAAE,CAAC;YACvB,IAAI,eAAe,EAAE,CAAC;gBAClB,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzB,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;gBACzB,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,MAAM,CAAC;gBACJ,EAAE,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,EAAE,GAAqB,IAAI,CAAC;YAEhC,qFAAqF;YACrF,IAAI,KAAK,EAAE,CAAC;gBACR,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAChC,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,uBAAuB,EAAE,CAAC;YAEzC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE9B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;oBACd,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;oBAC/C,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;gBACjC,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;gBACnD,CAAC;YACL,CAAC,MAAM,CAAC;+KACJ,SAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;YAED,IAAI,eAAe,EAAE,CAAC;gBAClB,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC;YACD,IAAI,CAAC,MAAM,EAAE,CAAC;2KACd,UAAO,CAAC,yBAAyB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAEnD,IAAI,eAAe,EAAE,CAAC;gBAClB,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,MAAM,CAAC;gBACJ,EAAE,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED;;;;;OAKG,CACI,SAAS,CAAC,GAAY,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAA;QACrE,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAED;;;;;OAKG,CACI,WAAW,CAAC,QAAiB,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAA;QAC5E,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACxD,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,QAAiB,EAAE,KAAqB,EAAA;QAC/D,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAA,EAAA,eAAA,KAAe,KAAK,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;OAMG,CACI,KAAK,CAAC,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,aAAa,GAAG,KAAK,EAAA;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAErC,mDAAmD;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;uKAClC,SAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;QACvC,QAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAEvC,8DAA8D;QAC9D,QAAQ,CAAC,MAAM,EAAE,CAAC;QAElB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YAChC,MAAM,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAClC,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAC/B,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC1B,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAC1B,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YAE1B,KAAK,CAAC,wBAAwB,EAAE,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;gBAChC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;YACxC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,QAAQ,CAAC,KAAc,EAAA;QAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG,CACI,QAAQ,GAAA;QACX,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACI,aAAa,CAAC,MAAe,EAAA;QAChC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;OAOG,CACI,eAAe,CAAC,GAAW,EAAE,KAAa,EAAE,IAAY,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAA;QACvG,IAAI,KAAK,KAAA,EAAA,eAAA,EAAgB,GAAE,CAAC;YACxB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;0KAC3B,cAAU,CAAC,yBAAyB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC7D,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,sCAAsC,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;YACjE,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,wKAAM,CAAC,yBAAyB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAE3D,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,IAAa,EAAE,MAAc,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAA;QACnF,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,wBAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;uKACvC,SAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;OAMG,CACI,YAAY,CAAC,IAAa,EAAE,KAAa,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAA;QACxF,IAAI,KAAK,KAAA,EAAA,eAAA,EAAgB,GAAE,CAAC;YACxB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;2KAC3B,aAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAEhD,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,sCAAsC,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;YACjE,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;uKAChC,SAAM,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE9C,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG,CACI,WAAW,CAAC,QAAiB,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAA;QAC5E,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;OAKG,CACI,qBAAqB,CAAC,IAAgB,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAA;QACrF,IAAI,KAAK,KAAA,EAAA,eAAA,EAAgB,GAAE,CAAC;YACxB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEnC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAE9B,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,sCAAsC,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;YACjE,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChC,wKAAM,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEzC,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAExC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,MAAc,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAA;QAC/E,IAAI,KAAK,KAAA,EAAA,eAAA,EAAgB,GAAE,CAAC;YACxB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;2KAC3B,aAAU,CAAC,uBAAuB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACjD,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,sCAAsC,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;YACjE,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEzB,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAEO,iBAAiB,CAAC,IAAY,EAAE,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAqB,EAAA;QAC9E,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACnC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACtB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACtB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAExC,IAAI,MAAM,IAAI,KAAK,IAAA,EAAA,eAAA,EAAe,GAAE,CAAC;YACjC,IAAI,KAAK,EAAE,CAAC;gBACR,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;gBAC7C,MAAM,CAAC,iBAAiB,EAAE,CAAC,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACvE,CAAC,MAAM,CAAC;gBACJ,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACrD,CAAC;YACD,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACrC,cAAc,CAAC,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAC7C,CAAC,MAAM,CAAC;YACJ,IAAI,KAAK,IAAA,EAAA,eAAA,EAAe,KAAI,KAAK,EAAE,CAAC;gBAChC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;gBAC7C,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACrC,cAAc,CAAC,MAAM,EAAE,CAAC;gBACxB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBACtC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC/B,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;YAC7C,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACnC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAE1C,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAEO,sCAAsC,CAAC,SAAiB,EAAE,KAAqB,EAAA;QACnF,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAE7C,IAAI,KAAK,EAAE,CAAC;YACR,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,SAAS,CAAC,CAAC;2KAC3D,SAAM,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QACxF,CAAC,MAAM,CAAC;2KACJ,SAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACtC,CAAC;QAED,SAAS,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxB,2BAA2B;YAC3B,gDAAgD;YAChD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,WAAW,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzD,SAAS,CAAC,aAAa,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAEhD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,WAAW,CAAC,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,QAAiC,IAAI,EAAA;QACzE,MAAM,GAAG,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE3B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QAEzC,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAA8B,EAAE,MAAe,EAAA;QACxF,IAAI,KAAK,IAAA,EAAA,eAAA,EAAe,GAAE,CAAC;YACvB,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAEjC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACpB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACpB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC,MAAM,CAAC;YACJ,IAAI,EAAE,GAAqB,IAAI,CAAC;YAEhC,qFAAqF;YACrF,IAAI,KAAK,EAAE,CAAC;gBACR,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;YAChC,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,uBAAuB,EAAE,CAAC;YAEzC,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE5B,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;gBACd,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;YACjC,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACpC,CAAC;YAED,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtB,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtB,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,QAAiC,IAAI,EAAA;QAC5D,MAAM,GAAG,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE3B,IAAI,CAAC,gBAAgB,CAAA,EAAA,eAAA,KAAc,KAAK,EAAE,GAAG,CAAC,CAAC;QAE/C,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;OAIG,CACI,wBAAwB,CAAC,KAAoB,EAAE,MAAe,EAAA;QACjE,IAAI,CAAC,gBAAgB,CAAA,EAAA,eAAA,KAAc,KAAK,EAAE,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG,CACI,uBAAuB,GAAA;QAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACvF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEjD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;YAElD,IAAI,UAAU,EAAE,CAAC;gBACb,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YACzE,CAAC;QACL,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAE5B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE,CAAC;YAC3B,QAAQ,CAAC,CAAC,CAAC,CAAC,uBAAuB,EAAE,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,yBAAyB,GAAA;QAC5B,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,SAAkB,EAAE,QAAiC,IAAI,EAAA;QACzE,MAAM,MAAM,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEjD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,SAAkB,EAAE,QAAiC,IAAI,EAAE,MAAe,EAAA;QAC/F,IAAI,EAAE,GAAqB,IAAI,CAAC;QAEhC,qFAAqF;QACrF,IAAI,KAAK,EAAE,CAAC;YACR,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,uBAAuB,EAAE,CAAC;QAEzC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE7B,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAEvC,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;YACd,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QAC/B,CAAC;uKAED,UAAO,CAAC,oBAAoB,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;QAErD,MAAM,CAAC,SAAS,EAAE,CAAC;IACvB,CAAC;IAED;;;;;OAKG,CACI,WAAW,CAAC,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,QAAiC,IAAI,EAAA;QACzE,MAAM,MAAM,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAE5C,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,QAAiC,IAAI,EAAE,MAAe,EAAA;QAC/F,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE3B,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEpD,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG,CACI,qBAAqB,CAAC,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,QAAiC,IAAI,EAAA;QACnF,MAAM,MAAM,kKAAG,aAAU,CAAC,QAAQ,EAAE,CAAC;QAErC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEtD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG,CACI,0BAA0B,CAAC,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,QAAiC,IAAI,EAAE,MAAkB,EAAA;QAC5G,IAAI,KAAK,IAAA,EAAA,eAAA,EAAe,GAAE,CAAC;YACvB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACzC,CAAC,MAAM,CAAC;YACJ,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEtC,IAAI,KAAK,EAAE,CAAC;gBACR,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC;YACpD,CAAC,MAAM,CAAC;gBACJ,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC;YAED,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEjD,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAoB,EAAA;QAC9D,MAAM,MAAM,GAAG,wKAAM,CAAC,QAAQ,EAAE,CAAC;QAEjC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAElD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG,CACI,sBAAsB,CAAC,KAAK,GAAA,EAAA,eAAA,EAAc,CAAd,EAAgB,KAAoB,EAAE,MAAc,EAAA;QACnF,IAAI,KAAK,IAAA,EAAA,eAAA,EAAe,GAAE,CAAC;YACvB,IAAI,CAAC,cAAc,EAAE,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC,MAAM,CAAC;YACJ,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEtC,IAAI,KAAK,EAAE,CAAC;gBACR,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,CAAC;YACpD,CAAC,MAAM,CAAC;gBACJ,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC;YAED,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjD,GAAG,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEjD,GAAG,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,4BAA4B,CAAC,QAAiB,EAAE,QAAiC,IAAI,EAAA;QACxF,MAAM,MAAM,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,iCAAiC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEhE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG,CACI,iCAAiC,CAAC,QAAiB,EAAE,QAAiC,IAAI,EAAE,MAAe,EAAA;QAC9G,IAAI,EAAE,GAAqB,IAAI,CAAC;QAEhC,qFAAqF;QACrF,IAAI,KAAK,EAAE,CAAC;YACR,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,uBAAuB,EAAE,CAAC;QAEzC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAExC,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;YACd,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC;uKAED,UAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG,CACI,4BAA4B,CAAC,QAAiB,EAAE,QAAiC,IAAI,EAAA;QACxF,MAAM,MAAM,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE9B,IAAI,CAAC,iCAAiC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEhE,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;OAKG,CACI,iCAAiC,CAAC,QAAiB,EAAE,QAAiC,IAAI,EAAE,MAAe,EAAA;QAC9G,IAAI,EAAE,GAAqB,IAAI,CAAC;QAEhC,qFAAqF;QACrF,IAAI,KAAK,EAAE,CAAC;YACR,EAAE,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,uBAAuB,EAAE,CAAC;QAEzC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAExC,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;YACd,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;uKAEd,UAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG,CACI,oBAAoB,GAAA;QACvB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAK,IAAI,CAAC,WAAoB,CAAC,QAAQ,EAAE,CAAC;YAC1D,MAAM,QAAQ,GAAI,IAAI,CAAC,WAAoB,CAAC,QAAQ,CAAC;YACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBACf,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;;AArtCc,KAAA,QAAQ,iKAAc,aAAU,AAAV,EAAW,CAAC,iKAAE,UAAO,CAAC,IAAI,CAAC,AAAzC,CAA0C;AAClD,KAAA,QAAQ,kKAAG,aAAU,CAAC,QAAQ,EAAE,AAAxB,CAAyB;AACjC,KAAA,QAAQ,iKAAa,aAAA,AAAU,EAAC,CAAC,iKAAE,SAAM,CAAC,QAAQ,CAAC,AAA3C,CAA4C", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Bones/skeleton.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Bones/skeleton.ts"], "sourcesContent": ["import { Bone } from \"./bone\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector3, Matrix, TmpVectors } from \"../Maths/math.vector\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { RawTexture } from \"../Materials/Textures/rawTexture\";\r\nimport type { Animatable } from \"../Animations/animatable.core\";\r\nimport type { AnimationPropertiesOverride } from \"../Animations/animationPropertiesOverride\";\r\nimport { Animation } from \"../Animations/animation\";\r\nimport { AnimationRange } from \"../Animations/animationRange\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { DeepCopier } from \"../Misc/deepCopier\";\r\nimport type { IInspectable } from \"../Misc/iInspectable\";\r\nimport type { IAnimatable } from \"../Animations/animatable.interface\";\r\nimport type { IAssetContainer } from \"core/IAssetContainer\";\r\n\r\n/**\r\n * Class used to handle skinning animations\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons\r\n */\r\nexport class Skeleton implements IAnimatable {\r\n    /**\r\n     * Defines the list of child bones\r\n     */\r\n    public bones: Bone[] = [];\r\n    /**\r\n     * Defines an estimate of the dimension of the skeleton at rest\r\n     */\r\n    public dimensionsAtRest: Vector3;\r\n    /**\r\n     * Defines a boolean indicating if the root matrix is provided by meshes or by the current skeleton (this is the default value)\r\n     */\r\n    public needInitialSkinMatrix = false;\r\n\r\n    /**\r\n     * Gets the list of animations attached to this skeleton\r\n     */\r\n    public animations: Array<Animation>;\r\n\r\n    private _scene: Scene;\r\n    private _isDirty = true;\r\n    private _transformMatrices: Float32Array;\r\n    private _transformMatrixTexture: Nullable<RawTexture>;\r\n    private _meshesWithPoseMatrix = new Array<AbstractMesh>();\r\n    private _animatables: IAnimatable[];\r\n    private _identity = Matrix.Identity();\r\n    private _synchronizedWithMesh: AbstractMesh;\r\n    private _currentRenderId = -1;\r\n\r\n    private _ranges: { [name: string]: Nullable<AnimationRange> } = {};\r\n\r\n    private _absoluteTransformIsDirty = true;\r\n\r\n    private _canUseTextureForBones = false;\r\n    private _uniqueId = 0;\r\n\r\n    /** @internal */\r\n    public _numBonesWithLinkedTransformNode = 0;\r\n\r\n    /** @internal */\r\n    public _hasWaitingData: Nullable<boolean> = null;\r\n\r\n    /** @internal */\r\n    public _parentContainer: Nullable<IAssetContainer> = null;\r\n\r\n    /**\r\n     * Specifies if the skeleton should be serialized\r\n     */\r\n    public doNotSerialize = false;\r\n\r\n    private _useTextureToStoreBoneMatrices = true;\r\n    /**\r\n     * Gets or sets a boolean indicating that bone matrices should be stored as a texture instead of using shader uniforms (default is true).\r\n     * Please note that this option is not available if the hardware does not support it\r\n     */\r\n    public get useTextureToStoreBoneMatrices(): boolean {\r\n        return this._useTextureToStoreBoneMatrices;\r\n    }\r\n\r\n    public set useTextureToStoreBoneMatrices(value: boolean) {\r\n        this._useTextureToStoreBoneMatrices = value;\r\n        this._markAsDirty();\r\n    }\r\n\r\n    private _animationPropertiesOverride: Nullable<AnimationPropertiesOverride> = null;\r\n\r\n    /**\r\n     * Gets or sets the animation properties override\r\n     */\r\n    public get animationPropertiesOverride(): Nullable<AnimationPropertiesOverride> {\r\n        if (!this._animationPropertiesOverride) {\r\n            return this._scene.animationPropertiesOverride;\r\n        }\r\n        return this._animationPropertiesOverride;\r\n    }\r\n\r\n    public set animationPropertiesOverride(value: Nullable<AnimationPropertiesOverride>) {\r\n        this._animationPropertiesOverride = value;\r\n    }\r\n\r\n    /**\r\n     * List of inspectable custom properties (used by the Inspector)\r\n     * @see https://doc.babylonjs.com/toolsAndResources/inspector#extensibility\r\n     */\r\n    public inspectableCustomProperties: IInspectable[];\r\n\r\n    // Events\r\n\r\n    /**\r\n     * An observable triggered before computing the skeleton's matrices\r\n     */\r\n    public onBeforeComputeObservable = new Observable<Skeleton>();\r\n\r\n    /**\r\n     * Gets a boolean indicating that the skeleton effectively stores matrices into a texture\r\n     */\r\n    public get isUsingTextureForMatrices() {\r\n        return this.useTextureToStoreBoneMatrices && this._canUseTextureForBones;\r\n    }\r\n\r\n    /**\r\n     * Gets the unique ID of this skeleton\r\n     */\r\n    public get uniqueId(): number {\r\n        return this._uniqueId;\r\n    }\r\n\r\n    /**\r\n     * Creates a new skeleton\r\n     * @param name defines the skeleton name\r\n     * @param id defines the skeleton Id\r\n     * @param scene defines the hosting scene\r\n     */\r\n    constructor(\r\n        /** defines the skeleton name */\r\n        public name: string,\r\n        /** defines the skeleton Id */\r\n        public id: string,\r\n        scene: Scene\r\n    ) {\r\n        this.bones = [];\r\n\r\n        this._scene = scene || EngineStore.LastCreatedScene;\r\n        this._uniqueId = this._scene.getUniqueId();\r\n\r\n        this._scene.addSkeleton(this);\r\n\r\n        //make sure it will recalculate the matrix next time prepare is called.\r\n        this._isDirty = true;\r\n\r\n        const engineCaps = this._scene.getEngine().getCaps();\r\n        this._canUseTextureForBones = engineCaps.textureFloat && engineCaps.maxVertexTextureImageUnits > 0;\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"Skeleton\";\r\n    }\r\n\r\n    /**\r\n     * Returns an array containing the root bones\r\n     * @returns an array containing the root bones\r\n     */\r\n    public getChildren(): Array<Bone> {\r\n        return this.bones.filter((b) => !b.getParent());\r\n    }\r\n\r\n    // Members\r\n    /**\r\n     * Gets the list of transform matrices to send to shaders (one matrix per bone)\r\n     * @param mesh defines the mesh to use to get the root matrix (if needInitialSkinMatrix === true)\r\n     * @returns a Float32Array containing matrices data\r\n     */\r\n    public getTransformMatrices(mesh: Nullable<AbstractMesh>): Float32Array {\r\n        if (this.needInitialSkinMatrix) {\r\n            if (!mesh) {\r\n                throw new Error(\"getTransformMatrices: When using the needInitialSkinMatrix flag, a mesh must be provided\");\r\n            }\r\n            if (!mesh._bonesTransformMatrices) {\r\n                this.prepare(true);\r\n            }\r\n\r\n            return mesh._bonesTransformMatrices!;\r\n        }\r\n\r\n        if (!this._transformMatrices || this._isDirty) {\r\n            this.prepare(!this._transformMatrices);\r\n        }\r\n\r\n        return this._transformMatrices;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of transform matrices to send to shaders inside a texture (one matrix per bone)\r\n     * @param mesh defines the mesh to use to get the root matrix (if needInitialSkinMatrix === true)\r\n     * @returns a raw texture containing the data\r\n     */\r\n    public getTransformMatrixTexture(mesh: AbstractMesh): Nullable<RawTexture> {\r\n        if (this.needInitialSkinMatrix && mesh._transformMatrixTexture) {\r\n            return mesh._transformMatrixTexture;\r\n        }\r\n\r\n        return this._transformMatrixTexture;\r\n    }\r\n\r\n    /**\r\n     * Gets the current hosting scene\r\n     * @returns a scene object\r\n     */\r\n    public getScene(): Scene {\r\n        return this._scene;\r\n    }\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Gets a string representing the current skeleton data\r\n     * @param fullDetails defines a boolean indicating if we want a verbose version\r\n     * @returns a string representing the current skeleton data\r\n     */\r\n    public toString(fullDetails?: boolean): string {\r\n        let ret = `Name: ${this.name}, nBones: ${this.bones.length}`;\r\n        ret += `, nAnimationRanges: ${this._ranges ? Object.keys(this._ranges).length : \"none\"}`;\r\n        if (fullDetails) {\r\n            ret += \", Ranges: {\";\r\n            let first = true;\r\n            for (const name in this._ranges) {\r\n                if (first) {\r\n                    ret += \", \";\r\n                    first = false;\r\n                }\r\n                ret += name;\r\n            }\r\n            ret += \"}\";\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Get bone's index searching by name\r\n     * @param name defines bone's name to search for\r\n     * @returns the indice of the bone. Returns -1 if not found\r\n     */\r\n    public getBoneIndexByName(name: string): number {\r\n        for (let boneIndex = 0, cache = this.bones.length; boneIndex < cache; boneIndex++) {\r\n            if (this.bones[boneIndex].name === name) {\r\n                return boneIndex;\r\n            }\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    /**\r\n     * Create a new animation range\r\n     * @param name defines the name of the range\r\n     * @param from defines the start key\r\n     * @param to defines the end key\r\n     */\r\n    public createAnimationRange(name: string, from: number, to: number): void {\r\n        // check name not already in use\r\n        if (!this._ranges[name]) {\r\n            this._ranges[name] = new AnimationRange(name, from, to);\r\n            for (let i = 0, nBones = this.bones.length; i < nBones; i++) {\r\n                if (this.bones[i].animations[0]) {\r\n                    this.bones[i].animations[0].createRange(name, from, to);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Delete a specific animation range\r\n     * @param name defines the name of the range\r\n     * @param deleteFrames defines if frames must be removed as well\r\n     */\r\n    public deleteAnimationRange(name: string, deleteFrames = true): void {\r\n        for (let i = 0, nBones = this.bones.length; i < nBones; i++) {\r\n            if (this.bones[i].animations[0]) {\r\n                this.bones[i].animations[0].deleteRange(name, deleteFrames);\r\n            }\r\n        }\r\n        this._ranges[name] = null; // said much faster than 'delete this._range[name]'\r\n    }\r\n\r\n    /**\r\n     * Gets a specific animation range\r\n     * @param name defines the name of the range to look for\r\n     * @returns the requested animation range or null if not found\r\n     */\r\n    public getAnimationRange(name: string): Nullable<AnimationRange> {\r\n        return this._ranges[name] || null;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of all animation ranges defined on this skeleton\r\n     * @returns an array\r\n     */\r\n    public getAnimationRanges(): Nullable<AnimationRange>[] {\r\n        const animationRanges: Nullable<AnimationRange>[] = [];\r\n        let name: string;\r\n        for (name in this._ranges) {\r\n            animationRanges.push(this._ranges[name]);\r\n        }\r\n        return animationRanges;\r\n    }\r\n\r\n    /**\r\n     * Copy animation range from a source skeleton.\r\n     * This is not for a complete retargeting, only between very similar skeleton's with only possible bone length differences\r\n     * @param source defines the source skeleton\r\n     * @param name defines the name of the range to copy\r\n     * @param rescaleAsRequired defines if rescaling must be applied if required\r\n     * @returns true if operation was successful\r\n     */\r\n    public copyAnimationRange(source: Skeleton, name: string, rescaleAsRequired = false): boolean {\r\n        if (this._ranges[name] || !source.getAnimationRange(name)) {\r\n            return false;\r\n        }\r\n        let ret = true;\r\n        const frameOffset = this._getHighestAnimationFrame() + 1;\r\n\r\n        // make a dictionary of source skeleton's bones, so exact same order or doubly nested loop is not required\r\n        const boneDict: { [key: string]: Bone } = {};\r\n        const sourceBones = source.bones;\r\n        let nBones: number;\r\n        let i: number;\r\n        for (i = 0, nBones = sourceBones.length; i < nBones; i++) {\r\n            boneDict[sourceBones[i].name] = sourceBones[i];\r\n        }\r\n\r\n        if (this.bones.length !== sourceBones.length) {\r\n            Logger.Warn(`copyAnimationRange: this rig has ${this.bones.length} bones, while source as ${sourceBones.length}`);\r\n            ret = false;\r\n        }\r\n\r\n        const skelDimensionsRatio = rescaleAsRequired && this.dimensionsAtRest && source.dimensionsAtRest ? this.dimensionsAtRest.divide(source.dimensionsAtRest) : null;\r\n\r\n        for (i = 0, nBones = this.bones.length; i < nBones; i++) {\r\n            const boneName = this.bones[i].name;\r\n            const sourceBone = boneDict[boneName];\r\n            if (sourceBone) {\r\n                ret = ret && this.bones[i].copyAnimationRange(sourceBone, name, frameOffset, rescaleAsRequired, skelDimensionsRatio);\r\n            } else {\r\n                Logger.Warn(\"copyAnimationRange: not same rig, missing source bone \" + boneName);\r\n                ret = false;\r\n            }\r\n        }\r\n        // do not call createAnimationRange(), since it also is done to bones, which was already done\r\n        const range = source.getAnimationRange(name);\r\n        if (range) {\r\n            this._ranges[name] = new AnimationRange(name, range.from + frameOffset, range.to + frameOffset);\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Forces the skeleton to go to rest pose\r\n     */\r\n    public returnToRest(): void {\r\n        for (const bone of this.bones) {\r\n            if (bone._index !== -1) {\r\n                bone.returnToRest();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _getHighestAnimationFrame(): number {\r\n        let ret = 0;\r\n        for (let i = 0, nBones = this.bones.length; i < nBones; i++) {\r\n            if (this.bones[i].animations[0]) {\r\n                const highest = this.bones[i].animations[0].getHighestFrame();\r\n                if (ret < highest) {\r\n                    ret = highest;\r\n                }\r\n            }\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Begin a specific animation range\r\n     * @param name defines the name of the range to start\r\n     * @param loop defines if looping must be turned on (false by default)\r\n     * @param speedRatio defines the speed ratio to apply (1 by default)\r\n     * @param onAnimationEnd defines a callback which will be called when animation will end\r\n     * @returns a new animatable\r\n     */\r\n    public beginAnimation(name: string, loop?: boolean, speedRatio?: number, onAnimationEnd?: () => void): Nullable<Animatable> {\r\n        const range = this.getAnimationRange(name);\r\n\r\n        if (!range) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.beginAnimation(this, range.from, range.to, loop, speedRatio, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Convert the keyframes for a range of animation on a skeleton to be relative to a given reference frame.\r\n     * @param skeleton defines the Skeleton containing the animation range to convert\r\n     * @param referenceFrame defines the frame that keyframes in the range will be relative to\r\n     * @param range defines the name of the AnimationRange belonging to the Skeleton to convert\r\n     * @returns the original skeleton\r\n     */\r\n    public static MakeAnimationAdditive(skeleton: Skeleton, referenceFrame = 0, range: string): Nullable<Skeleton> {\r\n        const rangeValue = skeleton.getAnimationRange(range);\r\n\r\n        // We can't make a range additive if it doesn't exist\r\n        if (!rangeValue) {\r\n            return null;\r\n        }\r\n\r\n        // Find any current scene-level animatable belonging to the target that matches the range\r\n        const sceneAnimatables = skeleton._scene.getAllAnimatablesByTarget(skeleton);\r\n        let rangeAnimatable: Nullable<Animatable> = null;\r\n\r\n        for (let index = 0; index < sceneAnimatables.length; index++) {\r\n            const sceneAnimatable = sceneAnimatables[index];\r\n\r\n            if (sceneAnimatable.fromFrame === rangeValue?.from && sceneAnimatable.toFrame === rangeValue?.to) {\r\n                rangeAnimatable = sceneAnimatable;\r\n                break;\r\n            }\r\n        }\r\n\r\n        // Convert the animations belonging to the skeleton to additive keyframes\r\n        const animatables = skeleton.getAnimatables();\r\n\r\n        for (let index = 0; index < animatables.length; index++) {\r\n            const animatable = animatables[index];\r\n            const animations = animatable.animations;\r\n\r\n            if (!animations) {\r\n                continue;\r\n            }\r\n\r\n            for (let animIndex = 0; animIndex < animations.length; animIndex++) {\r\n                Animation.MakeAnimationAdditive(animations[animIndex], referenceFrame, range);\r\n            }\r\n        }\r\n\r\n        // Mark the scene-level animatable as additive\r\n        if (rangeAnimatable) {\r\n            rangeAnimatable.isAdditive = true;\r\n        }\r\n\r\n        return skeleton;\r\n    }\r\n\r\n    /** @internal */\r\n    public _markAsDirty(): void {\r\n        this._isDirty = true;\r\n        this._absoluteTransformIsDirty = true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _registerMeshWithPoseMatrix(mesh: AbstractMesh): void {\r\n        this._meshesWithPoseMatrix.push(mesh);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _unregisterMeshWithPoseMatrix(mesh: AbstractMesh): void {\r\n        const index = this._meshesWithPoseMatrix.indexOf(mesh);\r\n\r\n        if (index > -1) {\r\n            this._meshesWithPoseMatrix.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    private _computeTransformMatrices(targetMatrix: Float32Array, initialSkinMatrix: Nullable<Matrix>): void {\r\n        this.onBeforeComputeObservable.notifyObservers(this);\r\n\r\n        for (let index = 0; index < this.bones.length; index++) {\r\n            const bone = this.bones[index];\r\n            bone._childUpdateId++;\r\n            const parentBone = bone.getParent();\r\n\r\n            if (parentBone) {\r\n                bone.getLocalMatrix().multiplyToRef(parentBone.getFinalMatrix(), bone.getFinalMatrix());\r\n            } else {\r\n                if (initialSkinMatrix) {\r\n                    bone.getLocalMatrix().multiplyToRef(initialSkinMatrix, bone.getFinalMatrix());\r\n                } else {\r\n                    bone.getFinalMatrix().copyFrom(bone.getLocalMatrix());\r\n                }\r\n            }\r\n\r\n            if (bone._index !== -1) {\r\n                const mappedIndex = bone._index === null ? index : bone._index;\r\n                bone.getAbsoluteInverseBindMatrix().multiplyToArray(bone.getFinalMatrix(), targetMatrix, mappedIndex * 16);\r\n            }\r\n        }\r\n\r\n        this._identity.copyToArray(targetMatrix, this.bones.length * 16);\r\n    }\r\n\r\n    /**\r\n     * Build all resources required to render a skeleton\r\n     * @param dontCheckFrameId defines a boolean indicating if prepare should be run without checking first the current frame id (default: false)\r\n     */\r\n    public prepare(dontCheckFrameId = false): void {\r\n        if (!dontCheckFrameId) {\r\n            const currentRenderId = this.getScene().getRenderId();\r\n            if (this._currentRenderId === currentRenderId) {\r\n                return;\r\n            }\r\n            this._currentRenderId = currentRenderId;\r\n        }\r\n\r\n        // Update the local matrix of bones with linked transform nodes.\r\n        if (this._numBonesWithLinkedTransformNode > 0) {\r\n            for (const bone of this.bones) {\r\n                if (bone._linkedTransformNode) {\r\n                    const node = bone._linkedTransformNode;\r\n                    bone.position = node.position;\r\n                    if (node.rotationQuaternion) {\r\n                        bone.rotationQuaternion = node.rotationQuaternion;\r\n                    } else {\r\n                        bone.rotation = node.rotation;\r\n                    }\r\n                    bone.scaling = node.scaling;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this.needInitialSkinMatrix) {\r\n            for (const mesh of this._meshesWithPoseMatrix) {\r\n                const poseMatrix = mesh.getPoseMatrix();\r\n\r\n                let needsUpdate = this._isDirty;\r\n                if (!mesh._bonesTransformMatrices || mesh._bonesTransformMatrices.length !== 16 * (this.bones.length + 1)) {\r\n                    mesh._bonesTransformMatrices = new Float32Array(16 * (this.bones.length + 1));\r\n                    needsUpdate = true;\r\n                }\r\n\r\n                if (!needsUpdate) {\r\n                    continue;\r\n                }\r\n\r\n                if (this._synchronizedWithMesh !== mesh) {\r\n                    this._synchronizedWithMesh = mesh;\r\n\r\n                    // Prepare bones\r\n                    for (const bone of this.bones) {\r\n                        if (!bone.getParent()) {\r\n                            const matrix = bone.getBindMatrix();\r\n                            matrix.multiplyToRef(poseMatrix, TmpVectors.Matrix[1]);\r\n                            bone._updateAbsoluteBindMatrices(TmpVectors.Matrix[1]);\r\n                        }\r\n                    }\r\n\r\n                    if (this.isUsingTextureForMatrices) {\r\n                        const textureWidth = (this.bones.length + 1) * 4;\r\n                        if (!mesh._transformMatrixTexture || mesh._transformMatrixTexture.getSize().width !== textureWidth) {\r\n                            if (mesh._transformMatrixTexture) {\r\n                                mesh._transformMatrixTexture.dispose();\r\n                            }\r\n\r\n                            mesh._transformMatrixTexture = RawTexture.CreateRGBATexture(\r\n                                mesh._bonesTransformMatrices,\r\n                                (this.bones.length + 1) * 4,\r\n                                1,\r\n                                this._scene,\r\n                                false,\r\n                                false,\r\n                                Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n                                Constants.TEXTURETYPE_FLOAT\r\n                            );\r\n                        }\r\n                    }\r\n                }\r\n\r\n                this._computeTransformMatrices(mesh._bonesTransformMatrices, poseMatrix);\r\n\r\n                if (this.isUsingTextureForMatrices && mesh._transformMatrixTexture) {\r\n                    mesh._transformMatrixTexture.update(mesh._bonesTransformMatrices);\r\n                }\r\n            }\r\n        } else {\r\n            if (!this._isDirty) {\r\n                return;\r\n            }\r\n\r\n            if (!this._transformMatrices || this._transformMatrices.length !== 16 * (this.bones.length + 1)) {\r\n                this._transformMatrices = new Float32Array(16 * (this.bones.length + 1));\r\n\r\n                if (this.isUsingTextureForMatrices) {\r\n                    if (this._transformMatrixTexture) {\r\n                        this._transformMatrixTexture.dispose();\r\n                    }\r\n\r\n                    this._transformMatrixTexture = RawTexture.CreateRGBATexture(\r\n                        this._transformMatrices,\r\n                        (this.bones.length + 1) * 4,\r\n                        1,\r\n                        this._scene,\r\n                        false,\r\n                        false,\r\n                        Constants.TEXTURE_NEAREST_SAMPLINGMODE,\r\n                        Constants.TEXTURETYPE_FLOAT\r\n                    );\r\n                }\r\n            }\r\n\r\n            this._computeTransformMatrices(this._transformMatrices, null);\r\n\r\n            if (this.isUsingTextureForMatrices && this._transformMatrixTexture) {\r\n                this._transformMatrixTexture.update(this._transformMatrices);\r\n            }\r\n        }\r\n\r\n        this._isDirty = false;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of animatables currently running for this skeleton\r\n     * @returns an array of animatables\r\n     */\r\n    public getAnimatables(): IAnimatable[] {\r\n        if (!this._animatables || this._animatables.length !== this.bones.length) {\r\n            this._animatables = [];\r\n\r\n            for (let index = 0; index < this.bones.length; index++) {\r\n                this._animatables.push(this.bones[index]);\r\n            }\r\n        }\r\n\r\n        return this._animatables;\r\n    }\r\n\r\n    /**\r\n     * Clone the current skeleton\r\n     * @param name defines the name of the new skeleton\r\n     * @param id defines the id of the new skeleton\r\n     * @returns the new skeleton\r\n     */\r\n    public clone(name: string, id?: string): Skeleton {\r\n        const result = new Skeleton(name, id || name, this._scene);\r\n\r\n        result.needInitialSkinMatrix = this.needInitialSkinMatrix;\r\n\r\n        for (let index = 0; index < this.bones.length; index++) {\r\n            const source = this.bones[index];\r\n            let parentBone = null;\r\n\r\n            const parent = source.getParent();\r\n            if (parent) {\r\n                const parentIndex = this.bones.indexOf(parent);\r\n                parentBone = result.bones[parentIndex];\r\n            }\r\n\r\n            const bone = new Bone(source.name, result, parentBone, source.getBindMatrix().clone(), source.getRestMatrix().clone());\r\n            bone._index = source._index;\r\n\r\n            if (source._linkedTransformNode) {\r\n                bone.linkTransformNode(source._linkedTransformNode);\r\n            }\r\n\r\n            DeepCopier.DeepCopy(source.animations, bone.animations);\r\n        }\r\n\r\n        if (this._ranges) {\r\n            result._ranges = {};\r\n            for (const rangeName in this._ranges) {\r\n                const range = this._ranges[rangeName];\r\n\r\n                if (range) {\r\n                    result._ranges[rangeName] = range.clone();\r\n                }\r\n            }\r\n        }\r\n\r\n        this._isDirty = true;\r\n\r\n        result.prepare(true);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Enable animation blending for this skeleton\r\n     * @param blendingSpeed defines the blending speed to apply\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\r\n     */\r\n    public enableBlending(blendingSpeed = 0.01) {\r\n        for (const bone of this.bones) {\r\n            for (const animation of bone.animations) {\r\n                animation.enableBlending = true;\r\n                animation.blendingSpeed = blendingSpeed;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Releases all resources associated with the current skeleton\r\n     */\r\n    public dispose() {\r\n        this._meshesWithPoseMatrix.length = 0;\r\n\r\n        // Animations\r\n        this.getScene().stopAnimation(this);\r\n\r\n        // Remove from scene\r\n        this.getScene().removeSkeleton(this);\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.skeletons.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.skeletons.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        if (this._transformMatrixTexture) {\r\n            this._transformMatrixTexture.dispose();\r\n            this._transformMatrixTexture = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serialize the skeleton in a JSON object\r\n     * @returns a JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.id = this.id;\r\n\r\n        if (this.dimensionsAtRest) {\r\n            serializationObject.dimensionsAtRest = this.dimensionsAtRest.asArray();\r\n        }\r\n\r\n        serializationObject.bones = [];\r\n\r\n        serializationObject.needInitialSkinMatrix = this.needInitialSkinMatrix;\r\n\r\n        for (let index = 0; index < this.bones.length; index++) {\r\n            const bone = this.bones[index];\r\n            const parent = bone.getParent();\r\n\r\n            const serializedBone: any = {\r\n                parentBoneIndex: parent ? this.bones.indexOf(parent) : -1,\r\n                index: bone.getIndex(),\r\n                name: bone.name,\r\n                id: bone.id,\r\n                matrix: bone.getBindMatrix().asArray(),\r\n                rest: bone.getRestMatrix().asArray(),\r\n                linkedTransformNodeId: bone.getTransformNode()?.id,\r\n            };\r\n\r\n            serializationObject.bones.push(serializedBone);\r\n\r\n            if (bone.length) {\r\n                serializedBone.length = bone.length;\r\n            }\r\n\r\n            if (bone.metadata) {\r\n                serializedBone.metadata = bone.metadata;\r\n            }\r\n\r\n            if (bone.animations && bone.animations.length > 0) {\r\n                serializedBone.animation = bone.animations[0].serialize();\r\n            }\r\n\r\n            serializationObject.ranges = [];\r\n            for (const name in this._ranges) {\r\n                const source = this._ranges[name];\r\n\r\n                if (!source) {\r\n                    continue;\r\n                }\r\n\r\n                const range: any = {};\r\n                range.name = name;\r\n                range.from = source.from;\r\n                range.to = source.to;\r\n                serializationObject.ranges.push(range);\r\n            }\r\n        }\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Creates a new skeleton from serialized data\r\n     * @param parsedSkeleton defines the serialized data\r\n     * @param scene defines the hosting scene\r\n     * @returns a new skeleton\r\n     */\r\n    public static Parse(parsedSkeleton: any, scene: Scene): Skeleton {\r\n        const skeleton = new Skeleton(parsedSkeleton.name, parsedSkeleton.id, scene);\r\n        if (parsedSkeleton.dimensionsAtRest) {\r\n            skeleton.dimensionsAtRest = Vector3.FromArray(parsedSkeleton.dimensionsAtRest);\r\n        }\r\n\r\n        skeleton.needInitialSkinMatrix = parsedSkeleton.needInitialSkinMatrix;\r\n\r\n        let index: number;\r\n        for (index = 0; index < parsedSkeleton.bones.length; index++) {\r\n            const parsedBone = parsedSkeleton.bones[index];\r\n            const parsedBoneIndex = parsedSkeleton.bones[index].index;\r\n            let parentBone = null;\r\n            if (parsedBone.parentBoneIndex > -1) {\r\n                parentBone = skeleton.bones[parsedBone.parentBoneIndex];\r\n            }\r\n\r\n            const rest: Nullable<Matrix> = parsedBone.rest ? Matrix.FromArray(parsedBone.rest) : null;\r\n            const bone = new Bone(parsedBone.name, skeleton, parentBone, Matrix.FromArray(parsedBone.matrix), rest, null, parsedBoneIndex);\r\n\r\n            if (parsedBone.id !== undefined && parsedBone.id !== null) {\r\n                bone.id = parsedBone.id;\r\n            }\r\n\r\n            if (parsedBone.length) {\r\n                bone.length = parsedBone.length;\r\n            }\r\n\r\n            if (parsedBone.metadata) {\r\n                bone.metadata = parsedBone.metadata;\r\n            }\r\n\r\n            if (parsedBone.animation) {\r\n                bone.animations.push(Animation.Parse(parsedBone.animation));\r\n            }\r\n\r\n            if (parsedBone.linkedTransformNodeId !== undefined && parsedBone.linkedTransformNodeId !== null) {\r\n                skeleton._hasWaitingData = true;\r\n                bone._waitingTransformNodeId = parsedBone.linkedTransformNodeId;\r\n            }\r\n        }\r\n\r\n        // placed after bones, so createAnimationRange can cascade down\r\n        if (parsedSkeleton.ranges) {\r\n            for (index = 0; index < parsedSkeleton.ranges.length; index++) {\r\n                const data = parsedSkeleton.ranges[index];\r\n                skeleton.createAnimationRange(data.name, data.from, data.to);\r\n            }\r\n        }\r\n        return skeleton;\r\n    }\r\n\r\n    /**\r\n     * Compute all node absolute matrices\r\n     * @param forceUpdate defines if computation must be done even if cache is up to date\r\n     */\r\n    public computeAbsoluteMatrices(forceUpdate = false): void {\r\n        if (this._absoluteTransformIsDirty || forceUpdate) {\r\n            this.bones[0].computeAbsoluteMatrices();\r\n            this._absoluteTransformIsDirty = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Compute all node absolute matrices\r\n     * @param forceUpdate defines if computation must be done even if cache is up to date\r\n     * @deprecated Please use computeAbsoluteMatrices instead\r\n     */\r\n    public computeAbsoluteTransforms(forceUpdate = false): void {\r\n        this.computeAbsoluteMatrices(forceUpdate);\r\n    }\r\n\r\n    /**\r\n     * Gets the root pose matrix\r\n     * @returns a matrix\r\n     */\r\n    public getPoseMatrix(): Nullable<Matrix> {\r\n        let poseMatrix: Nullable<Matrix> = null;\r\n\r\n        if (this._meshesWithPoseMatrix.length > 0) {\r\n            poseMatrix = this._meshesWithPoseMatrix[0].getPoseMatrix();\r\n        }\r\n\r\n        return poseMatrix;\r\n    }\r\n\r\n    /**\r\n     * Sorts bones per internal index\r\n     */\r\n    public sortBones(): void {\r\n        const bones: Bone[] = [];\r\n        const visited = new Array<boolean>(this.bones.length);\r\n        for (let index = 0; index < this.bones.length; index++) {\r\n            this._sortBones(index, bones, visited);\r\n        }\r\n\r\n        this.bones = bones;\r\n    }\r\n\r\n    private _sortBones(index: number, bones: Bone[], visited: boolean[]): void {\r\n        if (visited[index]) {\r\n            return;\r\n        }\r\n\r\n        visited[index] = true;\r\n\r\n        const bone = this.bones[index];\r\n        if (!bone) {\r\n            return;\r\n        }\r\n\r\n        if (bone._index === undefined) {\r\n            bone._index = index;\r\n        }\r\n\r\n        const parentBone = bone.getParent();\r\n        if (parentBone) {\r\n            this._sortBones(this.bones.indexOf(parentBone), bones, visited);\r\n        }\r\n\r\n        bones.push(bone);\r\n    }\r\n\r\n    /**\r\n     * Set the current local matrix as the restPose for all bones in the skeleton.\r\n     */\r\n    public setCurrentPoseAsRest(): void {\r\n        for (const b of this.bones) {\r\n            b.setCurrentPoseAsRest();\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAInE,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAG9D,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;;;AAS1C,MAAO,QAAQ;IAmDjB;;;OAGG,CACH,IAAW,6BAA6B,GAAA;QACpC,OAAO,IAAI,CAAC,8BAA8B,CAAC;IAC/C,CAAC;IAED,IAAW,6BAA6B,CAAC,KAAc,EAAA;QACnD,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAC5C,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAID;;OAEG,CACH,IAAW,2BAA2B,GAAA;QAClC,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,MAAM,CAAC,2BAA2B,CAAC;QACnD,CAAC;QACD,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED,IAAW,2BAA2B,CAAC,KAA4C,EAAA;QAC/E,IAAI,CAAC,4BAA4B,GAAG,KAAK,CAAC;IAC9C,CAAC;IAeD;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,sBAAsB,CAAC;IAC7E,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;;;OAKG,CACH,YACI,8BAAA,EAAgC,CACzB,IAAY,EACnB,4BAAA,EAA8B,CACvB,EAAU,EACjB,KAAY,CAAA;QAHL,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QAEZ,IAAA,CAAA,EAAE,GAAF,EAAE,CAAQ;QApHrB;;WAEG,CACI,IAAA,CAAA,KAAK,GAAW,EAAE,CAAC;QAK1B;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAQ7B,IAAA,CAAA,QAAQ,GAAG,IAAI,CAAC;QAGhB,IAAA,CAAA,qBAAqB,GAAG,IAAI,KAAK,EAAgB,CAAC;QAElD,IAAA,CAAA,SAAS,kKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAEtB,IAAA,CAAA,OAAO,GAAiD,CAAA,CAAE,CAAC;QAE3D,IAAA,CAAA,yBAAyB,GAAG,IAAI,CAAC;QAEjC,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QAEtB,cAAA,EAAgB,CACT,IAAA,CAAA,gCAAgC,GAAG,CAAC,CAAC;QAE5C,cAAA,EAAgB,CACT,IAAA,CAAA,eAAe,GAAsB,IAAI,CAAC;QAEjD,cAAA,EAAgB,CACT,IAAA,CAAA,gBAAgB,GAA8B,IAAI,CAAC;QAE1D;;WAEG,CACI,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAEtB,IAAA,CAAA,8BAA8B,GAAG,IAAI,CAAC;QActC,IAAA,CAAA,4BAA4B,GAA0C,IAAI,CAAC;QAsBnF,SAAS;QAET;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,8JAAI,aAAU,EAAY,CAAC;QA6B1D,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAEhB,IAAI,CAAC,MAAM,GAAG,KAAK,kKAAI,cAAW,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE3C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE9B,uEAAuE;QACvE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;QACrD,IAAI,CAAC,sBAAsB,GAAG,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,0BAA0B,GAAG,CAAC,CAAC;IACvG,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;OAGG,CACI,WAAW,GAAA;QACd,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,UAAU;IACV;;;;OAIG,CACI,oBAAoB,CAAC,IAA4B,EAAA;QACpD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM,IAAI,KAAK,CAAC,0FAA0F,CAAC,CAAC;YAChH,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC;YAED,OAAO,IAAI,CAAC,uBAAwB,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;;OAIG,CACI,yBAAyB,CAAC,IAAkB,EAAA;QAC/C,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC7D,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACxC,CAAC;QAED,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;OAGG,CACI,QAAQ,GAAA;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,UAAU;IAEV;;;;OAIG,CACI,QAAQ,CAAC,WAAqB,EAAA;QACjC,IAAI,GAAG,GAAG,CAAA,MAAA,EAAS,IAAI,CAAC,IAAI,CAAA,UAAA,EAAa,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAC7D,GAAG,IAAI,CAAA,oBAAA,EAAuB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;QACzF,IAAI,WAAW,EAAE,CAAC;YACd,GAAG,IAAI,aAAa,CAAC;YACrB,IAAI,KAAK,GAAG,IAAI,CAAC;YACjB,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAC9B,IAAI,KAAK,EAAE,CAAC;oBACR,GAAG,IAAI,IAAI,CAAC;oBACZ,KAAK,GAAG,KAAK,CAAC;gBAClB,CAAC;gBACD,GAAG,IAAI,IAAI,CAAC;YAChB,CAAC;YACD,GAAG,IAAI,GAAG,CAAC;QACf,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,IAAY,EAAA;QAClC,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,GAAG,KAAK,EAAE,SAAS,EAAE,CAAE,CAAC;YAChF,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACtC,OAAO,SAAS,CAAC;YACrB,CAAC;QACL,CAAC;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;OAKG,CACI,oBAAoB,CAAC,IAAY,EAAE,IAAY,EAAE,EAAU,EAAA;QAC9D,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,wKAAI,iBAAc,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;YACxD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC1D,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC9B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;gBAC5D,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,IAAY,EAAE,YAAY,GAAG,IAAI,EAAA;QACzD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC1D,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,mDAAmD;IAClF,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,IAAY,EAAA;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACtC,CAAC;IAED;;;OAGG,CACI,kBAAkB,GAAA;QACrB,MAAM,eAAe,GAA+B,EAAE,CAAC;QACvD,IAAI,IAAY,CAAC;QACjB,IAAK,IAAI,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;;;OAOG,CACI,kBAAkB,CAAC,MAAgB,EAAE,IAAY,EAAE,iBAAiB,GAAG,KAAK,EAAA;QAC/E,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,GAAG,GAAG,IAAI,CAAC;QACf,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;QAEzD,0GAA0G;QAC1G,MAAM,QAAQ,GAA4B,CAAA,CAAE,CAAC;QAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;QACjC,IAAI,MAAc,CAAC;QACnB,IAAI,CAAS,CAAC;QACd,IAAK,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACvD,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM,EAAE,CAAC;kKAC3C,SAAM,CAAC,IAAI,CAAC,CAAA,iCAAA,EAAoC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA,wBAAA,EAA2B,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;YAClH,GAAG,GAAG,KAAK,CAAC;QAChB,CAAC;QAED,MAAM,mBAAmB,GAAG,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAEjK,IAAK,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACpC,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACtC,IAAI,UAAU,EAAE,CAAC;gBACb,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;YACzH,CAAC,MAAM,CAAC;sKACJ,SAAM,CAAC,IAAI,CAAC,wDAAwD,GAAG,QAAQ,CAAC,CAAC;gBACjF,GAAG,GAAG,KAAK,CAAC;YAChB,CAAC;QACL,CAAC;QACD,6FAA6F;QAC7F,MAAM,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,wKAAI,iBAAc,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,GAAG,WAAW,EAAE,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,CAAC;QACpG,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;OAEG,CACI,YAAY,GAAA;QACf,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;gBACrB,IAAI,CAAC,YAAY,EAAE,CAAC;YACxB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,yBAAyB,GAAA;QAC7B,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC1D,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC;gBAC9D,IAAI,GAAG,GAAG,OAAO,EAAE,CAAC;oBAChB,GAAG,GAAG,OAAO,CAAC;gBAClB,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;;;OAOG,CACI,cAAc,CAAC,IAAY,EAAE,IAAc,EAAE,UAAmB,EAAE,cAA2B,EAAA;QAChG,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACpG,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,qBAAqB,CAAC,QAAkB,EAAE,cAAc,GAAG,CAAC,EAAE,KAAa,EAAA;QACrF,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAErD,qDAAqD;QACrD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,yFAAyF;QACzF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QAC7E,IAAI,eAAe,GAAyB,IAAI,CAAC;QAEjD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC3D,MAAM,eAAe,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAEhD,IAAI,eAAe,CAAC,SAAS,KAAK,UAAU,EAAE,IAAI,IAAI,eAAe,CAAC,OAAO,KAAK,UAAU,EAAE,EAAE,EAAE,CAAC;gBAC/F,eAAe,GAAG,eAAe,CAAC;gBAClC,MAAM;YACV,CAAC;QACL,CAAC;QAED,yEAAyE;QACzE,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,EAAE,CAAC;QAE9C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;YAEzC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,SAAS;YACb,CAAC;YAED,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;+KACjE,YAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;YAClF,CAAC;QACL,CAAC;QAED,8CAA8C;QAC9C,IAAI,eAAe,EAAE,CAAC;YAClB,eAAe,CAAC,UAAU,GAAG,IAAI,CAAC;QACtC,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;IAC1C,CAAC;IAED;;OAEG,CACI,2BAA2B,CAAC,IAAkB,EAAA;QACjD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG,CACI,6BAA6B,CAAC,IAAkB,EAAA;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,YAA0B,EAAE,iBAAmC,EAAA;QAC7F,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAErD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACrD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAEpC,IAAI,UAAU,EAAE,CAAC;gBACb,IAAI,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YAC5F,CAAC,MAAM,CAAC;gBACJ,IAAI,iBAAiB,EAAE,CAAC;oBACpB,IAAI,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;gBAClF,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;gBAC1D,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;gBACrB,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC/D,IAAI,CAAC,4BAA4B,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,YAAY,EAAE,WAAW,GAAG,EAAE,CAAC,CAAC;YAC/G,CAAC;QACL,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG,CACI,OAAO,CAAC,gBAAgB,GAAG,KAAK,EAAA;QACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;YACtD,IAAI,IAAI,CAAC,gBAAgB,KAAK,eAAe,EAAE,CAAC;gBAC5C,OAAO;YACX,CAAC;YACD,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QAC5C,CAAC;QAED,gEAAgE;QAChE,IAAI,IAAI,CAAC,gCAAgC,GAAG,CAAC,EAAE,CAAC;YAC5C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAE,CAAC;gBAC5B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC;oBACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC9B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;oBACtD,CAAC,MAAM,CAAC;wBACJ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,CAAC;oBACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;gBAChC,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAE,CAAC;gBAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBAExC,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAChC,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;oBACxG,IAAI,CAAC,uBAAuB,GAAG,IAAI,YAAY,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC9E,WAAW,GAAG,IAAI,CAAC;gBACvB,CAAC;gBAED,IAAI,CAAC,WAAW,EAAE,CAAC;oBACf,SAAS;gBACb,CAAC;gBAED,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE,CAAC;oBACtC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;oBAElC,gBAAgB;oBAChB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAE,CAAC;wBAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;4BACpB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;4BACpC,MAAM,CAAC,aAAa,CAAC,UAAU,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;4BACvD,IAAI,CAAC,2BAA2B,gKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3D,CAAC;oBACL,CAAC;oBAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;wBACjC,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;wBACjD,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK,YAAY,EAAE,CAAC;4BACjG,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gCAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;4BAC3C,CAAC;4BAED,IAAI,CAAC,uBAAuB,8KAAG,aAAU,CAAC,iBAAiB,CACvD,IAAI,CAAC,uBAAuB,EAC5B,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAC3B,CAAC,EACD,IAAI,CAAC,MAAM,EACX,KAAK,EACL,KAAK,EACL,GAAA,MAAS,CAAC,4BAA4B,EACtC,SAAS,CAAC,iBAAiB,CAC9B,CAAC;wBACN,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;gBAEzE,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBACjE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBACtE,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjB,OAAO;YACX,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC9F,IAAI,CAAC,kBAAkB,GAAG,IAAI,YAAY,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBAEzE,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACjC,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;wBAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;oBAC3C,CAAC;oBAED,IAAI,CAAC,uBAAuB,8KAAG,aAAU,CAAC,iBAAiB,CACvD,IAAI,CAAC,kBAAkB,EACvB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAC3B,CAAC,EACD,IAAI,CAAC,MAAM,EACX,KAAK,EACL,KAAK,EACL,GAAA,MAAS,CAAC,4BAA4B,EACtC,SAAS,CAAC,iBAAiB,CAC9B,CAAC;gBACN,CAAC;YACL,CAAC;YAED,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;YAE9D,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACjE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACjE,CAAC;QACL,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACvE,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YAEvB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;;OAKG,CACI,KAAK,CAAC,IAAY,EAAE,EAAW,EAAA;QAClC,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAE3D,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAE1D,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACrD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACjC,IAAI,UAAU,GAAG,IAAI,CAAC;YAEtB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAClC,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC/C,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC3C,CAAC;YAED,MAAM,IAAI,GAAG,yJAAI,OAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;YACvH,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;YAE5B,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC;gBAC9B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;YACxD,CAAC;sKAED,aAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,CAAC,OAAO,GAAG,CAAA,CAAE,CAAC;YACpB,IAAK,MAAM,SAAS,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAEtC,IAAI,KAAK,EAAE,CAAC;oBACR,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC9C,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAErB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,aAAa,GAAG,IAAI,EAAA;QACtC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAE,CAAC;YAC5B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,CAAE,CAAC;gBACtC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC;gBAChC,SAAS,CAAC,aAAa,GAAG,aAAa,CAAC;YAC5C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEtC,aAAa;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEpC,oBAAoB;QACpB,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACrD,CAAC;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,GAAQ,CAAA,CAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAC3E,CAAC;QAED,mBAAmB,CAAC,KAAK,GAAG,EAAE,CAAC;QAE/B,mBAAmB,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAEvE,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACrD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAEhC,MAAM,cAAc,GAAQ;gBACxB,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;gBACtB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE;gBACtC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE;gBACpC,qBAAqB,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAE;aACrD,CAAC;YAEF,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAE/C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YACxC,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC5C,CAAC;YAED,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;YAC9D,CAAC;YAED,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;YAChC,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAElC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,SAAS;gBACb,CAAC;gBAED,MAAM,KAAK,GAAQ,CAAA,CAAE,CAAC;gBACtB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;gBAClB,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;gBACzB,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;gBACrB,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC;QACD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,KAAK,CAAC,cAAmB,EAAE,KAAY,EAAA;QACjD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAC7E,IAAI,cAAc,CAAC,gBAAgB,EAAE,CAAC;YAClC,QAAQ,CAAC,gBAAgB,kKAAG,UAAO,CAAC,SAAS,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QACnF,CAAC;QAED,QAAQ,CAAC,qBAAqB,GAAG,cAAc,CAAC,qBAAqB,CAAC;QAEtE,IAAI,KAAa,CAAC;QAClB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC3D,MAAM,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/C,MAAM,eAAe,GAAG,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;YAC1D,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,IAAI,UAAU,CAAC,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC;gBAClC,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAC5D,CAAC;YAED,MAAM,IAAI,GAAqB,UAAU,CAAC,IAAI,CAAC,CAAC,gKAAC,SAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1F,MAAM,IAAI,GAAG,yJAAI,OAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,iKAAE,SAAM,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;YAE/H,IAAI,UAAU,CAAC,EAAE,KAAK,SAAS,IAAI,UAAU,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;gBACxD,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;YAC5B,CAAC;YAED,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YACpC,CAAC;YAED,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACtB,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;YACxC,CAAC;YAED,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;gBACvB,IAAI,CAAC,UAAU,CAAC,IAAI,gKAAC,YAAS,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,UAAU,CAAC,qBAAqB,KAAK,SAAS,IAAI,UAAU,CAAC,qBAAqB,KAAK,IAAI,EAAE,CAAC;gBAC9F,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;gBAChC,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC,qBAAqB,CAAC;YACpE,CAAC;QACL,CAAC;QAED,+DAA+D;QAC/D,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YACxB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC5D,MAAM,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC1C,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACjE,CAAC;QACL,CAAC;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG,CACI,uBAAuB,CAAC,WAAW,GAAG,KAAK,EAAA;QAC9C,IAAI,IAAI,CAAC,yBAAyB,IAAI,WAAW,EAAE,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,uBAAuB,EAAE,CAAC;YACxC,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;QAC3C,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,yBAAyB,CAAC,WAAW,GAAG,KAAK,EAAA;QAChD,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,IAAI,UAAU,GAAqB,IAAI,CAAC;QAExC,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;QAC/D,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG,CACI,SAAS,GAAA;QACZ,MAAM,KAAK,GAAW,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,IAAI,KAAK,CAAU,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACtD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAEO,UAAU,CAAC,KAAa,EAAE,KAAa,EAAE,OAAkB,EAAA;QAC/D,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QAEtB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACxB,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACpC,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACpE,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAED;;OAEG,CACI,oBAAoB,GAAA;QACvB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,CAAE,CAAC;YACzB,CAAC,CAAC,oBAAoB,EAAE,CAAC;QAC7B,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 1651, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Bones/boneIKController.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Bones/boneIKController.ts"], "sourcesContent": ["import type { Bone } from \"./bone\";\r\nimport { Vector3, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from \"../Maths/math.vector\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Space } from \"../Maths/math.axis\";\r\nimport { Logger } from \"../Misc/logger\";\r\n\r\n/**\r\n * Class used to apply inverse kinematics to bones\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons#boneikcontroller\r\n */\r\nexport class BoneIKController {\r\n    private static _TmpVecs: Vector3[] = [Vector3.Zero(), Vector3.Zero(), Vector3.Zero(), Vector3.Zero(), Vector3.Zero(), Vector3.Zero()];\r\n    private static _TmpQuat = Quaternion.Identity();\r\n    private static _TmpMats: Matrix[] = [Matrix.Identity(), Matrix.Identity()];\r\n\r\n    /**\r\n     * Gets or sets the target TransformNode\r\n     * Name kept as mesh for back compatibility\r\n     */\r\n    public targetMesh: TransformNode;\r\n\r\n    /** Gets or sets the mesh used as pole */\r\n    public poleTargetMesh: TransformNode;\r\n\r\n    /**\r\n     * Gets or sets the bone used as pole\r\n     */\r\n    public poleTargetBone: Nullable<Bone>;\r\n\r\n    /**\r\n     * Gets or sets the target position\r\n     */\r\n    public targetPosition = Vector3.Zero();\r\n\r\n    /**\r\n     * Gets or sets the pole target position\r\n     */\r\n    public poleTargetPosition = Vector3.Zero();\r\n\r\n    /**\r\n     * Gets or sets the pole target local offset\r\n     */\r\n    public poleTargetLocalOffset = Vector3.Zero();\r\n\r\n    /**\r\n     * Gets or sets the pole angle\r\n     */\r\n    public poleAngle = 0;\r\n\r\n    /**\r\n     * Gets or sets the TransformNode associated with the controller\r\n     * Name kept as mesh for back compatibility\r\n     */\r\n    public mesh: TransformNode;\r\n\r\n    /**\r\n     * The amount to slerp (spherical linear interpolation) to the target.  Set this to a value between 0 and 1 (a value of 1 disables slerp)\r\n     */\r\n    public slerpAmount = 1;\r\n\r\n    private _bone1Quat = Quaternion.Identity();\r\n    private _bone1Mat = Matrix.Identity();\r\n    private _bone2Ang = Math.PI;\r\n\r\n    private _bone1: Bone;\r\n    private _bone2: Bone;\r\n    private _bone1Length: number;\r\n    private _bone2Length: number;\r\n    private _maxAngle = Math.PI;\r\n    private _maxReach: number;\r\n\r\n    private _rightHandedSystem = false;\r\n\r\n    private _bendAxis = Vector3.Right();\r\n    private _slerping = false;\r\n\r\n    private _adjustRoll = 0;\r\n\r\n    private _notEnoughInformation = false;\r\n\r\n    /**\r\n     * Gets or sets maximum allowed angle\r\n     */\r\n    public get maxAngle(): number {\r\n        return this._maxAngle;\r\n    }\r\n\r\n    public set maxAngle(value: number) {\r\n        this._setMaxAngle(value);\r\n    }\r\n\r\n    /**\r\n     * Creates a new BoneIKController\r\n     * @param mesh defines the TransformNode to control\r\n     * @param bone defines the bone to control. The bone needs to have a parent bone. It also needs to have a length greater than 0 or a children we can use to infer its length.\r\n     * @param options defines options to set up the controller\r\n     * @param options.targetMesh\r\n     * @param options.poleTargetMesh\r\n     * @param options.poleTargetBone\r\n     * @param options.poleTargetLocalOffset\r\n     * @param options.poleAngle\r\n     * @param options.bendAxis\r\n     * @param options.maxAngle\r\n     * @param options.slerpAmount\r\n     */\r\n    constructor(\r\n        mesh: TransformNode,\r\n        bone: Bone,\r\n        options?: {\r\n            targetMesh?: TransformNode;\r\n            poleTargetMesh?: TransformNode;\r\n            poleTargetBone?: Bone;\r\n            poleTargetLocalOffset?: Vector3;\r\n            poleAngle?: number;\r\n            bendAxis?: Vector3;\r\n            maxAngle?: number;\r\n            slerpAmount?: number;\r\n        }\r\n    ) {\r\n        this._bone2 = bone;\r\n        const bone1 = bone.getParent();\r\n\r\n        if (!bone1) {\r\n            this._notEnoughInformation = true;\r\n            Logger.Error(\"BoneIKController: bone must have a parent for IK to work.\");\r\n            return;\r\n        }\r\n        this._bone1 = bone1;\r\n\r\n        if (this._bone2.children.length === 0 && !this._bone2.length) {\r\n            this._notEnoughInformation = true;\r\n            Logger.Error(\"BoneIKController: bone must not be a leaf or it should have a length for IK to work.\");\r\n            return;\r\n        }\r\n\r\n        this.mesh = mesh;\r\n\r\n        bone.getSkeleton().computeAbsoluteMatrices();\r\n\r\n        const bonePos = bone.getPosition();\r\n\r\n        if (bone.getAbsoluteMatrix().determinant() > 0) {\r\n            this._rightHandedSystem = true;\r\n            this._bendAxis.x = 0;\r\n            this._bendAxis.y = 0;\r\n            this._bendAxis.z = -1;\r\n\r\n            if (bonePos.x > bonePos.y && bonePos.x > bonePos.z) {\r\n                this._adjustRoll = Math.PI * 0.5;\r\n                this._bendAxis.z = 1;\r\n            }\r\n        }\r\n\r\n        if (this._bone1.length && this._bone2.length) {\r\n            const boneScale1 = this._bone1.getScale();\r\n            const boneScale2 = this._bone2.getScale();\r\n\r\n            this._bone1Length = this._bone1.length * boneScale1.y * this.mesh.scaling.y;\r\n            this._bone2Length = this._bone2.length * boneScale2.y * this.mesh.scaling.y;\r\n        } else if (this._bone2.children[0]) {\r\n            mesh.computeWorldMatrix(true);\r\n\r\n            const pos1 = this._bone2.children[0].getAbsolutePosition(mesh);\r\n            const pos2 = this._bone2.getAbsolutePosition(mesh);\r\n            const pos3 = this._bone1.getAbsolutePosition(mesh);\r\n\r\n            this._bone2Length = Vector3.Distance(pos1, pos2);\r\n            this._bone1Length = Vector3.Distance(pos2, pos3);\r\n        } else {\r\n            mesh.computeWorldMatrix(true);\r\n\r\n            const boneScale2 = this._bone2.getScale();\r\n            this._bone2Length = this._bone2.length * boneScale2.y * this.mesh.scaling.y;\r\n\r\n            const pos2 = this._bone2.getAbsolutePosition(mesh);\r\n            const pos3 = this._bone1.getAbsolutePosition(mesh);\r\n\r\n            this._bone1Length = Vector3.Distance(pos2, pos3);\r\n        }\r\n\r\n        this._bone1.getRotationMatrixToRef(Space.WORLD, mesh, this._bone1Mat);\r\n        this.maxAngle = Math.PI;\r\n\r\n        if (options) {\r\n            if (options.targetMesh) {\r\n                this.targetMesh = options.targetMesh;\r\n                this.targetMesh.computeWorldMatrix(true);\r\n            }\r\n\r\n            if (options.poleTargetMesh) {\r\n                this.poleTargetMesh = options.poleTargetMesh;\r\n                this.poleTargetMesh.computeWorldMatrix(true);\r\n            } else if (options.poleTargetBone) {\r\n                this.poleTargetBone = options.poleTargetBone;\r\n            } else if (this._bone1.getParent()) {\r\n                this.poleTargetBone = this._bone1.getParent();\r\n            }\r\n\r\n            if (options.poleTargetLocalOffset) {\r\n                this.poleTargetLocalOffset.copyFrom(options.poleTargetLocalOffset);\r\n            }\r\n\r\n            if (options.poleAngle) {\r\n                this.poleAngle = options.poleAngle;\r\n            }\r\n\r\n            if (options.bendAxis) {\r\n                this._bendAxis.copyFrom(options.bendAxis);\r\n            }\r\n\r\n            if (options.maxAngle) {\r\n                this.maxAngle = options.maxAngle;\r\n            }\r\n\r\n            if (options.slerpAmount) {\r\n                this.slerpAmount = options.slerpAmount;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _setMaxAngle(ang: number): void {\r\n        if (ang < 0) {\r\n            ang = 0;\r\n        }\r\n\r\n        if (ang > Math.PI || ang == undefined) {\r\n            ang = Math.PI;\r\n        }\r\n\r\n        this._maxAngle = ang;\r\n\r\n        const a = this._bone1Length;\r\n        const b = this._bone2Length;\r\n\r\n        this._maxReach = Math.sqrt(a * a + b * b - 2 * a * b * Math.cos(ang));\r\n    }\r\n\r\n    /**\r\n     * Force the controller to update the bones\r\n     */\r\n    public update(): void {\r\n        if (this._notEnoughInformation) {\r\n            return;\r\n        }\r\n\r\n        const target = this.targetPosition;\r\n        const poleTarget = this.poleTargetPosition;\r\n\r\n        const mat1 = BoneIKController._TmpMats[0];\r\n        const mat2 = BoneIKController._TmpMats[1];\r\n\r\n        if (this.targetMesh) {\r\n            target.copyFrom(this.targetMesh.getAbsolutePosition());\r\n        }\r\n\r\n        if (this.poleTargetBone) {\r\n            this.poleTargetBone.getAbsolutePositionFromLocalToRef(this.poleTargetLocalOffset, this.mesh, poleTarget);\r\n        } else if (this.poleTargetMesh) {\r\n            Vector3.TransformCoordinatesToRef(this.poleTargetLocalOffset, this.poleTargetMesh.getWorldMatrix(), poleTarget);\r\n        }\r\n\r\n        const bonePos = BoneIKController._TmpVecs[0];\r\n        const zaxis = BoneIKController._TmpVecs[1];\r\n        const xaxis = BoneIKController._TmpVecs[2];\r\n        const yaxis = BoneIKController._TmpVecs[3];\r\n        const upAxis = BoneIKController._TmpVecs[4];\r\n\r\n        const tmpQuat = BoneIKController._TmpQuat;\r\n\r\n        this._bone1.getAbsolutePositionToRef(this.mesh, bonePos);\r\n\r\n        poleTarget.subtractToRef(bonePos, upAxis);\r\n\r\n        if (upAxis.x == 0 && upAxis.y == 0 && upAxis.z == 0) {\r\n            upAxis.y = 1;\r\n        } else {\r\n            upAxis.normalize();\r\n        }\r\n\r\n        target.subtractToRef(bonePos, yaxis);\r\n        yaxis.normalize();\r\n\r\n        Vector3.CrossToRef(yaxis, upAxis, zaxis);\r\n        zaxis.normalize();\r\n\r\n        Vector3.CrossToRef(yaxis, zaxis, xaxis);\r\n        xaxis.normalize();\r\n\r\n        Matrix.FromXYZAxesToRef(xaxis, yaxis, zaxis, mat1);\r\n\r\n        const a = this._bone1Length;\r\n        const b = this._bone2Length;\r\n\r\n        let c = Vector3.Distance(bonePos, target);\r\n\r\n        if (this._maxReach > 0) {\r\n            c = Math.min(this._maxReach, c);\r\n        }\r\n\r\n        let acosa = (b * b + c * c - a * a) / (2 * b * c);\r\n        let acosb = (c * c + a * a - b * b) / (2 * c * a);\r\n\r\n        if (acosa > 1) {\r\n            acosa = 1;\r\n        }\r\n\r\n        if (acosb > 1) {\r\n            acosb = 1;\r\n        }\r\n\r\n        if (acosa < -1) {\r\n            acosa = -1;\r\n        }\r\n\r\n        if (acosb < -1) {\r\n            acosb = -1;\r\n        }\r\n\r\n        const angA = Math.acos(acosa);\r\n        const angB = Math.acos(acosb);\r\n\r\n        let angC = -angA - angB;\r\n\r\n        if (this._rightHandedSystem) {\r\n            Matrix.RotationYawPitchRollToRef(0, 0, this._adjustRoll, mat2);\r\n            mat2.multiplyToRef(mat1, mat1);\r\n\r\n            Matrix.RotationAxisToRef(this._bendAxis, angB, mat2);\r\n            mat2.multiplyToRef(mat1, mat1);\r\n        } else {\r\n            const _tmpVec = BoneIKController._TmpVecs[5];\r\n\r\n            _tmpVec.copyFrom(this._bendAxis);\r\n            _tmpVec.x *= -1;\r\n\r\n            Matrix.RotationAxisToRef(_tmpVec, -angB, mat2);\r\n            mat2.multiplyToRef(mat1, mat1);\r\n        }\r\n\r\n        if (this.poleAngle) {\r\n            Matrix.RotationAxisToRef(yaxis, this.poleAngle, mat2);\r\n            mat1.multiplyToRef(mat2, mat1);\r\n        }\r\n\r\n        if (this._bone1) {\r\n            if (this.slerpAmount < 1) {\r\n                if (!this._slerping) {\r\n                    Quaternion.FromRotationMatrixToRef(this._bone1Mat, this._bone1Quat);\r\n                }\r\n                Quaternion.FromRotationMatrixToRef(mat1, tmpQuat);\r\n                Quaternion.SlerpToRef(this._bone1Quat, tmpQuat, this.slerpAmount, this._bone1Quat);\r\n                angC = this._bone2Ang * (1.0 - this.slerpAmount) + angC * this.slerpAmount;\r\n\r\n                this._bone1.setRotationQuaternion(this._bone1Quat, Space.WORLD, this.mesh);\r\n                this._slerping = true;\r\n            } else {\r\n                this._bone1.setRotationMatrix(mat1, Space.WORLD, this.mesh);\r\n                this._bone1Mat.copyFrom(mat1);\r\n                this._slerping = false;\r\n            }\r\n            this._updateLinkedTransformRotation(this._bone1);\r\n        }\r\n\r\n        this._bone2.setAxisAngle(this._bendAxis, angC, Space.LOCAL);\r\n        this._updateLinkedTransformRotation(this._bone2);\r\n        this._bone2Ang = angC;\r\n    }\r\n\r\n    private _updateLinkedTransformRotation(bone: Bone): void {\r\n        if (bone._linkedTransformNode) {\r\n            if (!bone._linkedTransformNode.rotationQuaternion) {\r\n                bone._linkedTransformNode.rotationQuaternion = new Quaternion();\r\n            }\r\n            bone.getRotationQuaternionToRef(Space.LOCAL, null, bone._linkedTransformNode.rotationQuaternion);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAInE,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;;;AAMlC,MAAO,gBAAgB;IAsEzB;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa,EAAA;QAC7B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;;;;OAaG,CACH,YACI,IAAmB,EACnB,IAAU,EACV,OASC,CAAA;QAxFL;;WAEG,CACI,IAAA,CAAA,cAAc,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAEvC;;WAEG,CACI,IAAA,CAAA,kBAAkB,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE3C;;WAEG,CACI,IAAA,CAAA,qBAAqB,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE9C;;WAEG,CACI,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QAQrB;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAEf,IAAA,CAAA,UAAU,kKAAG,aAAU,CAAC,QAAQ,EAAE,CAAC;QACnC,IAAA,CAAA,SAAS,kKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAA,CAAA,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC;QAMpB,IAAA,CAAA,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC;QAGpB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,SAAS,kKAAG,UAAO,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAElB,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAEhB,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAyClC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE/B,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;kKAClC,SAAM,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC1E,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAC3D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;kKAClC,SAAM,CAAC,KAAK,CAAC,sFAAsF,CAAC,CAAC;YACrG,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,IAAI,CAAC,WAAW,EAAE,CAAC,uBAAuB,EAAE,CAAC;QAE7C,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAEtB,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC;gBACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;gBACjC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAE1C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5E,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAChF,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAEnD,IAAI,CAAC,YAAY,kKAAG,UAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACjD,IAAI,CAAC,YAAY,kKAAG,UAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACrD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAE9B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;YAE5E,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACnD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAEnD,IAAI,CAAC,YAAY,kKAAG,UAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAA,EAAA,eAAA,KAAc,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACtE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;gBACrC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBACzB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;gBAC7C,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC,MAAM,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAChC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;YACjD,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC;gBACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAClD,CAAC;YAED,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;gBAChC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YACvE,CAAC;YAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;YACvC,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YACrC,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACtB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YAC3C,CAAC;QACL,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,GAAW,EAAA;QAC5B,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;YACV,GAAG,GAAG,CAAC,CAAC;QACZ,CAAC;QAED,IAAI,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,SAAS,EAAE,CAAC;YACpC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QAErB,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;QAC5B,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;QAE5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAE3C,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,CAAC,iCAAiC,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC7G,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;2KAC7B,UAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,EAAE,UAAU,CAAC,CAAC;QACpH,CAAC;QAED,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,MAAM,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5C,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC;QAE1C,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEzD,UAAU,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAE1C,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;YAClD,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QACjB,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,SAAS,EAAE,CAAC;QACvB,CAAC;QAED,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,KAAK,CAAC,SAAS,EAAE,CAAC;uKAElB,UAAO,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACzC,KAAK,CAAC,SAAS,EAAE,CAAC;uKAElB,UAAO,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACxC,KAAK,CAAC,SAAS,EAAE,CAAC;uKAElB,SAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEnD,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;QAC5B,MAAM,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;QAE5B,IAAI,CAAC,kKAAG,UAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YACrB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAElD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACZ,KAAK,GAAG,CAAC,CAAC;QACd,CAAC;QAED,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACZ,KAAK,GAAG,CAAC,CAAC;QACd,CAAC;QAED,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,KAAK,GAAG,CAAC,CAAC,CAAC;QACf,CAAC;QAED,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,KAAK,GAAG,CAAC,CAAC,CAAC;QACf,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE9B,IAAI,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAExB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;2KAC1B,SAAM,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC/D,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;2KAE/B,SAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE7C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;2KAEhB,SAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;2KACjB,SAAM,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;gBACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;mLAClB,aAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACxE,CAAC;+KACD,aAAU,CAAC,uBAAuB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;+KAClD,aAAU,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACnF,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;gBAE3E,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAA,EAAA,eAAA,KAAe,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YAC1B,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,EAAA,EAAA,eAAA,KAAe,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAC3B,CAAC;YACD,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAA,EAAA,eAAA,GAAc,CAAC;QAC5D,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAC1B,CAAC;IAEO,8BAA8B,CAAC,IAAU,EAAA;QAC7C,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,CAAC;gBAChD,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,GAAG,mKAAI,aAAU,EAAE,CAAC;YACpE,CAAC;YACD,IAAI,CAAC,0BAA0B,CAAA,EAAA,eAAA,KAAc,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QACrG,CAAC;IACL,CAAC;;AA5Wc,iBAAA,QAAQ,GAAc;mKAAC,UAAO,CAAC,IAAI,EAAE;mKAAE,UAAO,CAAC,IAAI,EAAE;mKAAE,UAAO,CAAC,IAAI,EAAE;mKAAE,UAAO,CAAC,IAAI,EAAE;mKAAE,UAAO,CAAC,IAAI,EAAE;mKAAE,UAAO,CAAC,IAAI,EAAE;CAA7G,CAA+G;AACvH,iBAAA,QAAQ,kKAAG,aAAU,CAAC,QAAQ,EAAE,AAAxB,CAAyB;AACjC,iBAAA,QAAQ,GAAa;mKAAC,SAAM,CAAC,QAAQ,EAAE;mKAAE,SAAM,CAAC,QAAQ,EAAE;CAAlD,CAAoD", "debugId": null}}, {"offset": {"line": 1919, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Bones/boneLookController.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Bones/boneLookController.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { BuildArray } from \"../Misc/arrayTools\";\r\nimport { Vector3, Quaternion, Matrix } from \"../Maths/math.vector\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Bone } from \"./bone\";\r\nimport { Space, Axis } from \"../Maths/math.axis\";\r\n\r\n/**\r\n * Class used to make a bone look toward a point in space\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/bonesSkeletons#bonelookcontroller\r\n */\r\nexport class BoneLookController {\r\n    private static _TmpVecs: Vector3[] = BuildArray(10, Vector3.Zero);\r\n    private static _TmpQuat = Quaternion.Identity();\r\n    private static _TmpMats: Matrix[] = BuildArray(5, Matrix.Identity);\r\n\r\n    /**\r\n     * The target Vector3 that the bone will look at\r\n     */\r\n    public target: Vector3;\r\n\r\n    /**\r\n     * The TransformNode that the bone is attached to\r\n     * Name kept as mesh for back compatibility\r\n     */\r\n    public mesh: TransformNode;\r\n\r\n    /**\r\n     * The bone that will be looking to the target\r\n     */\r\n    public bone: Bone;\r\n\r\n    /**\r\n     * The up axis of the coordinate system that is used when the bone is rotated\r\n     */\r\n    public upAxis: Vector3 = Vector3.Up();\r\n\r\n    /**\r\n     * The space that the up axis is in - Space.BONE, Space.LOCAL (default), or Space.WORLD\r\n     */\r\n    public upAxisSpace: Space = Space.LOCAL;\r\n\r\n    /**\r\n     * Used to make an adjustment to the yaw of the bone\r\n     */\r\n    public adjustYaw = 0;\r\n\r\n    /**\r\n     * Used to make an adjustment to the pitch of the bone\r\n     */\r\n    public adjustPitch = 0;\r\n\r\n    /**\r\n     * Used to make an adjustment to the roll of the bone\r\n     */\r\n    public adjustRoll = 0;\r\n\r\n    /**\r\n     * The amount to slerp (spherical linear interpolation) to the target.  Set this to a value between 0 and 1 (a value of 1 disables slerp)\r\n     */\r\n    public slerpAmount = 1;\r\n\r\n    private _minYaw: number;\r\n    private _maxYaw: number;\r\n    private _minPitch: number;\r\n    private _maxPitch: number;\r\n    private _minYawSin: number;\r\n    private _minYawCos: number;\r\n    private _maxYawSin: number;\r\n    private _maxYawCos: number;\r\n    private _midYawConstraint: number;\r\n    private _minPitchTan: number;\r\n    private _maxPitchTan: number;\r\n\r\n    private _boneQuat: Quaternion = Quaternion.Identity();\r\n    private _slerping = false;\r\n    private _transformYawPitch: Matrix;\r\n    private _transformYawPitchInv: Matrix;\r\n    private _firstFrameSkipped = false;\r\n    private _yawRange: number;\r\n    private _fowardAxis: Vector3 = Vector3.Forward();\r\n\r\n    /**\r\n     * Gets or sets the minimum yaw angle that the bone can look to\r\n     */\r\n    get minYaw(): number {\r\n        return this._minYaw;\r\n    }\r\n\r\n    set minYaw(value: number) {\r\n        this._minYaw = value;\r\n        this._minYawSin = Math.sin(value);\r\n        this._minYawCos = Math.cos(value);\r\n        if (this._maxYaw != null) {\r\n            this._midYawConstraint = this._getAngleDiff(this._minYaw, this._maxYaw) * 0.5 + this._minYaw;\r\n            this._yawRange = this._maxYaw - this._minYaw;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the maximum yaw angle that the bone can look to\r\n     */\r\n    get maxYaw(): number {\r\n        return this._maxYaw;\r\n    }\r\n\r\n    set maxYaw(value: number) {\r\n        this._maxYaw = value;\r\n        this._maxYawSin = Math.sin(value);\r\n        this._maxYawCos = Math.cos(value);\r\n        if (this._minYaw != null) {\r\n            this._midYawConstraint = this._getAngleDiff(this._minYaw, this._maxYaw) * 0.5 + this._minYaw;\r\n            this._yawRange = this._maxYaw - this._minYaw;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Use the absolute value for yaw when checking the min/max constraints\r\n     */\r\n    public useAbsoluteValueForYaw = false;\r\n\r\n    /**\r\n     * Gets or sets the minimum pitch angle that the bone can look to\r\n     */\r\n    get minPitch(): number {\r\n        return this._minPitch;\r\n    }\r\n\r\n    set minPitch(value: number) {\r\n        this._minPitch = value;\r\n        this._minPitchTan = Math.tan(value);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the maximum pitch angle that the bone can look to\r\n     */\r\n    get maxPitch(): number {\r\n        return this._maxPitch;\r\n    }\r\n\r\n    set maxPitch(value: number) {\r\n        this._maxPitch = value;\r\n        this._maxPitchTan = Math.tan(value);\r\n    }\r\n\r\n    /**\r\n     * Create a BoneLookController\r\n     * @param mesh the TransformNode that the bone belongs to\r\n     * @param bone the bone that will be looking to the target\r\n     * @param target the target Vector3 to look at\r\n     * @param options optional settings:\r\n     * * maxYaw: the maximum angle the bone will yaw to\r\n     * * minYaw: the minimum angle the bone will yaw to\r\n     * * maxPitch: the maximum angle the bone will pitch to\r\n     * * minPitch: the minimum angle the bone will yaw to\r\n     * * slerpAmount: set the between 0 and 1 to make the bone slerp to the target.\r\n     * * upAxis: the up axis of the coordinate system\r\n     * * upAxisSpace: the space that the up axis is in - Space.BONE, Space.LOCAL (default), or Space.WORLD.\r\n     * * yawAxis: set yawAxis if the bone does not yaw on the y axis\r\n     * * pitchAxis: set pitchAxis if the bone does not pitch on the x axis\r\n     * * adjustYaw: used to make an adjustment to the yaw of the bone\r\n     * * adjustPitch: used to make an adjustment to the pitch of the bone\r\n     * * adjustRoll: used to make an adjustment to the roll of the bone\r\n     * @param options.maxYaw\r\n     * @param options.minYaw\r\n     * @param options.maxPitch\r\n     * @param options.minPitch\r\n     * @param options.slerpAmount\r\n     * @param options.upAxis\r\n     * @param options.upAxisSpace\r\n     * @param options.yawAxis\r\n     * @param options.pitchAxis\r\n     * @param options.adjustYaw\r\n     * @param options.adjustPitch\r\n     * @param options.adjustRoll\r\n     **/\r\n    constructor(\r\n        mesh: TransformNode,\r\n        bone: Bone,\r\n        target: Vector3,\r\n        options?: {\r\n            maxYaw?: number;\r\n            minYaw?: number;\r\n            maxPitch?: number;\r\n            minPitch?: number;\r\n            slerpAmount?: number;\r\n            upAxis?: Vector3;\r\n            upAxisSpace?: Space;\r\n            yawAxis?: Vector3;\r\n            pitchAxis?: Vector3;\r\n            adjustYaw?: number;\r\n            adjustPitch?: number;\r\n            adjustRoll?: number;\r\n            useAbsoluteValueForYaw?: boolean;\r\n        }\r\n    ) {\r\n        this.mesh = mesh;\r\n        this.bone = bone;\r\n        this.target = target;\r\n\r\n        if (options) {\r\n            if (options.adjustYaw) {\r\n                this.adjustYaw = options.adjustYaw;\r\n            }\r\n\r\n            if (options.adjustPitch) {\r\n                this.adjustPitch = options.adjustPitch;\r\n            }\r\n\r\n            if (options.adjustRoll) {\r\n                this.adjustRoll = options.adjustRoll;\r\n            }\r\n\r\n            if (options.maxYaw != null) {\r\n                this.maxYaw = options.maxYaw;\r\n            } else {\r\n                this.maxYaw = Math.PI;\r\n            }\r\n\r\n            if (options.minYaw != null) {\r\n                this.minYaw = options.minYaw;\r\n            } else {\r\n                this.minYaw = -Math.PI;\r\n            }\r\n\r\n            if (options.maxPitch != null) {\r\n                this.maxPitch = options.maxPitch;\r\n            } else {\r\n                this.maxPitch = Math.PI;\r\n            }\r\n\r\n            if (options.minPitch != null) {\r\n                this.minPitch = options.minPitch;\r\n            } else {\r\n                this.minPitch = -Math.PI;\r\n            }\r\n\r\n            if (options.slerpAmount != null) {\r\n                this.slerpAmount = options.slerpAmount;\r\n            }\r\n\r\n            if (options.upAxis != null) {\r\n                this.upAxis = options.upAxis;\r\n            }\r\n\r\n            if (options.upAxisSpace != null) {\r\n                this.upAxisSpace = options.upAxisSpace;\r\n            }\r\n\r\n            if (options.yawAxis != null || options.pitchAxis != null) {\r\n                let newYawAxis = Axis.Y;\r\n                let newPitchAxis = Axis.X;\r\n\r\n                if (options.yawAxis != null) {\r\n                    newYawAxis = options.yawAxis.clone();\r\n                    newYawAxis.normalize();\r\n                }\r\n\r\n                if (options.pitchAxis != null) {\r\n                    newPitchAxis = options.pitchAxis.clone();\r\n                    newPitchAxis.normalize();\r\n                }\r\n\r\n                const newRollAxis = Vector3.Cross(newPitchAxis, newYawAxis);\r\n\r\n                this._transformYawPitch = Matrix.Identity();\r\n                Matrix.FromXYZAxesToRef(newPitchAxis, newYawAxis, newRollAxis, this._transformYawPitch);\r\n\r\n                this._transformYawPitchInv = this._transformYawPitch.clone();\r\n                this._transformYawPitch.invert();\r\n            }\r\n\r\n            if (options.useAbsoluteValueForYaw !== undefined) {\r\n                this.useAbsoluteValueForYaw = options.useAbsoluteValueForYaw;\r\n            }\r\n        }\r\n\r\n        if (!bone.getParent() && this.upAxisSpace == Space.BONE) {\r\n            this.upAxisSpace = Space.LOCAL;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the bone to look at the target.  This should be called before the scene is rendered (use scene.registerBeforeRender())\r\n     */\r\n    public update(): void {\r\n        //skip the first frame when slerping so that the TransformNode rotation is correct\r\n        if (this.slerpAmount < 1 && !this._firstFrameSkipped) {\r\n            this._firstFrameSkipped = true;\r\n            return;\r\n        }\r\n\r\n        const bone = this.bone;\r\n        const bonePos = BoneLookController._TmpVecs[0];\r\n        bone.getAbsolutePositionToRef(this.mesh, bonePos);\r\n\r\n        let target = this.target;\r\n        const _tmpMat1 = BoneLookController._TmpMats[0];\r\n        const _tmpMat2 = BoneLookController._TmpMats[1];\r\n\r\n        const mesh = this.mesh;\r\n        const parentBone = bone.getParent();\r\n\r\n        const upAxis = BoneLookController._TmpVecs[1];\r\n        upAxis.copyFrom(this.upAxis);\r\n\r\n        if (this.upAxisSpace == Space.BONE && parentBone) {\r\n            if (this._transformYawPitch) {\r\n                Vector3.TransformCoordinatesToRef(upAxis, this._transformYawPitchInv, upAxis);\r\n            }\r\n            parentBone.getDirectionToRef(upAxis, this.mesh, upAxis);\r\n        } else if (this.upAxisSpace == Space.LOCAL) {\r\n            mesh.getDirectionToRef(upAxis, upAxis);\r\n            if (mesh.scaling.x != 1 || mesh.scaling.y != 1 || mesh.scaling.z != 1) {\r\n                upAxis.normalize();\r\n            }\r\n        }\r\n\r\n        let checkYaw = false;\r\n        let checkPitch = false;\r\n\r\n        if (this._maxYaw != Math.PI || this._minYaw != -Math.PI) {\r\n            checkYaw = true;\r\n        }\r\n        if (this._maxPitch != Math.PI || this._minPitch != -Math.PI) {\r\n            checkPitch = true;\r\n        }\r\n\r\n        if (checkYaw || checkPitch) {\r\n            const spaceMat = BoneLookController._TmpMats[2];\r\n            const spaceMatInv = BoneLookController._TmpMats[3];\r\n\r\n            if (this.upAxisSpace == Space.BONE && upAxis.y == 1 && parentBone) {\r\n                parentBone.getRotationMatrixToRef(Space.WORLD, this.mesh, spaceMat);\r\n            } else if (this.upAxisSpace == Space.LOCAL && upAxis.y == 1 && !parentBone) {\r\n                spaceMat.copyFrom(mesh.getWorldMatrix());\r\n            } else {\r\n                let forwardAxis = BoneLookController._TmpVecs[2];\r\n                forwardAxis.copyFrom(this._fowardAxis);\r\n\r\n                if (this._transformYawPitch) {\r\n                    Vector3.TransformCoordinatesToRef(forwardAxis, this._transformYawPitchInv, forwardAxis);\r\n                }\r\n\r\n                if (parentBone) {\r\n                    parentBone.getDirectionToRef(forwardAxis, this.mesh, forwardAxis);\r\n                } else {\r\n                    mesh.getDirectionToRef(forwardAxis, forwardAxis);\r\n                }\r\n\r\n                const rightAxis = Vector3.Cross(upAxis, forwardAxis);\r\n                rightAxis.normalize();\r\n                forwardAxis = Vector3.Cross(rightAxis, upAxis);\r\n\r\n                Matrix.FromXYZAxesToRef(rightAxis, upAxis, forwardAxis, spaceMat);\r\n            }\r\n\r\n            spaceMat.invertToRef(spaceMatInv);\r\n\r\n            let xzlen: Nullable<number> = null;\r\n\r\n            if (checkPitch) {\r\n                const localTarget = BoneLookController._TmpVecs[3];\r\n                target.subtractToRef(bonePos, localTarget);\r\n                Vector3.TransformCoordinatesToRef(localTarget, spaceMatInv, localTarget);\r\n\r\n                xzlen = Math.sqrt(localTarget.x * localTarget.x + localTarget.z * localTarget.z);\r\n                const pitch = Math.atan2(localTarget.y, xzlen);\r\n                let newPitch = pitch;\r\n\r\n                if (pitch > this._maxPitch) {\r\n                    localTarget.y = this._maxPitchTan * xzlen;\r\n                    newPitch = this._maxPitch;\r\n                } else if (pitch < this._minPitch) {\r\n                    localTarget.y = this._minPitchTan * xzlen;\r\n                    newPitch = this._minPitch;\r\n                }\r\n\r\n                if (pitch != newPitch) {\r\n                    Vector3.TransformCoordinatesToRef(localTarget, spaceMat, localTarget);\r\n                    localTarget.addInPlace(bonePos);\r\n                    target = localTarget;\r\n                }\r\n            }\r\n\r\n            if (checkYaw) {\r\n                const localTarget = BoneLookController._TmpVecs[4];\r\n                target.subtractToRef(bonePos, localTarget);\r\n                Vector3.TransformCoordinatesToRef(localTarget, spaceMatInv, localTarget);\r\n\r\n                const yaw = Math.atan2(localTarget.x, localTarget.z);\r\n                const yawCheck = this.useAbsoluteValueForYaw ? Math.abs(yaw) : yaw;\r\n                let newYaw = yaw;\r\n\r\n                if (yawCheck > this._maxYaw || yawCheck < this._minYaw) {\r\n                    if (xzlen == null) {\r\n                        xzlen = Math.sqrt(localTarget.x * localTarget.x + localTarget.z * localTarget.z);\r\n                    }\r\n\r\n                    if (this._yawRange > Math.PI) {\r\n                        if (this._isAngleBetween(yaw, this._maxYaw, this._midYawConstraint)) {\r\n                            localTarget.z = this._maxYawCos * xzlen;\r\n                            localTarget.x = this._maxYawSin * xzlen;\r\n                            newYaw = this._maxYaw;\r\n                        } else if (this._isAngleBetween(yaw, this._midYawConstraint, this._minYaw)) {\r\n                            localTarget.z = this._minYawCos * xzlen;\r\n                            localTarget.x = this._minYawSin * xzlen;\r\n                            newYaw = this._minYaw;\r\n                        }\r\n                    } else {\r\n                        if (yawCheck > this._maxYaw) {\r\n                            localTarget.z = this._maxYawCos * xzlen;\r\n                            localTarget.x = this._maxYawSin * xzlen;\r\n                            if (yaw < 0 && this.useAbsoluteValueForYaw) {\r\n                                localTarget.x *= -1;\r\n                            }\r\n                            newYaw = this._maxYaw;\r\n                        } else if (yawCheck < this._minYaw) {\r\n                            localTarget.z = this._minYawCos * xzlen;\r\n                            localTarget.x = this._minYawSin * xzlen;\r\n                            if (yaw < 0 && this.useAbsoluteValueForYaw) {\r\n                                localTarget.x *= -1;\r\n                            }\r\n                            newYaw = this._minYaw;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if (this._slerping && this._yawRange > Math.PI) {\r\n                    //are we going to be crossing into the min/max region?\r\n                    const boneFwd = BoneLookController._TmpVecs[8];\r\n                    boneFwd.copyFrom(Axis.Z);\r\n                    if (this._transformYawPitch) {\r\n                        Vector3.TransformCoordinatesToRef(boneFwd, this._transformYawPitchInv, boneFwd);\r\n                    }\r\n\r\n                    const boneRotMat = BoneLookController._TmpMats[4];\r\n                    this._boneQuat.toRotationMatrix(boneRotMat);\r\n                    this.mesh.getWorldMatrix().multiplyToRef(boneRotMat, boneRotMat);\r\n                    Vector3.TransformCoordinatesToRef(boneFwd, boneRotMat, boneFwd);\r\n                    Vector3.TransformCoordinatesToRef(boneFwd, spaceMatInv, boneFwd);\r\n\r\n                    const boneYaw = Math.atan2(boneFwd.x, boneFwd.z);\r\n                    const angBtwTar = this._getAngleBetween(boneYaw, yaw);\r\n                    const angBtwMidYaw = this._getAngleBetween(boneYaw, this._midYawConstraint);\r\n\r\n                    if (angBtwTar > angBtwMidYaw) {\r\n                        if (xzlen == null) {\r\n                            xzlen = Math.sqrt(localTarget.x * localTarget.x + localTarget.z * localTarget.z);\r\n                        }\r\n\r\n                        const angBtwMax = this._getAngleBetween(boneYaw, this._maxYaw);\r\n                        const angBtwMin = this._getAngleBetween(boneYaw, this._minYaw);\r\n\r\n                        if (angBtwMin < angBtwMax) {\r\n                            newYaw = boneYaw + Math.PI * 0.75;\r\n                            localTarget.z = Math.cos(newYaw) * xzlen;\r\n                            localTarget.x = Math.sin(newYaw) * xzlen;\r\n                        } else {\r\n                            newYaw = boneYaw - Math.PI * 0.75;\r\n                            localTarget.z = Math.cos(newYaw) * xzlen;\r\n                            localTarget.x = Math.sin(newYaw) * xzlen;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if (yaw != newYaw) {\r\n                    Vector3.TransformCoordinatesToRef(localTarget, spaceMat, localTarget);\r\n                    localTarget.addInPlace(bonePos);\r\n                    target = localTarget;\r\n                }\r\n            }\r\n        }\r\n\r\n        const zaxis = BoneLookController._TmpVecs[5];\r\n        const xaxis = BoneLookController._TmpVecs[6];\r\n        const yaxis = BoneLookController._TmpVecs[7];\r\n        const tmpQuat = BoneLookController._TmpQuat;\r\n        const boneScaling = BoneLookController._TmpVecs[9];\r\n\r\n        target.subtractToRef(bonePos, zaxis);\r\n        zaxis.normalize();\r\n        Vector3.CrossToRef(upAxis, zaxis, xaxis);\r\n        xaxis.normalize();\r\n        Vector3.CrossToRef(zaxis, xaxis, yaxis);\r\n        yaxis.normalize();\r\n        Matrix.FromXYZAxesToRef(xaxis, yaxis, zaxis, _tmpMat1);\r\n\r\n        if (xaxis.x === 0 && xaxis.y === 0 && xaxis.z === 0) {\r\n            return;\r\n        }\r\n\r\n        if (yaxis.x === 0 && yaxis.y === 0 && yaxis.z === 0) {\r\n            return;\r\n        }\r\n\r\n        if (zaxis.x === 0 && zaxis.y === 0 && zaxis.z === 0) {\r\n            return;\r\n        }\r\n\r\n        if (this.adjustYaw || this.adjustPitch || this.adjustRoll) {\r\n            Matrix.RotationYawPitchRollToRef(this.adjustYaw, this.adjustPitch, this.adjustRoll, _tmpMat2);\r\n            _tmpMat2.multiplyToRef(_tmpMat1, _tmpMat1);\r\n        }\r\n\r\n        boneScaling.copyFrom(this.bone.getScale());\r\n\r\n        if (this.slerpAmount < 1) {\r\n            if (!this._slerping) {\r\n                this.bone.getRotationQuaternionToRef(Space.WORLD, this.mesh, this._boneQuat);\r\n            }\r\n            if (this._transformYawPitch) {\r\n                this._transformYawPitch.multiplyToRef(_tmpMat1, _tmpMat1);\r\n            }\r\n            Quaternion.FromRotationMatrixToRef(_tmpMat1, tmpQuat);\r\n            Quaternion.SlerpToRef(this._boneQuat, tmpQuat, this.slerpAmount, this._boneQuat);\r\n\r\n            this.bone.setRotationQuaternion(this._boneQuat, Space.WORLD, this.mesh);\r\n            this._slerping = true;\r\n        } else {\r\n            if (this._transformYawPitch) {\r\n                this._transformYawPitch.multiplyToRef(_tmpMat1, _tmpMat1);\r\n            }\r\n            this.bone.setRotationMatrix(_tmpMat1, Space.WORLD, this.mesh);\r\n            this._slerping = false;\r\n        }\r\n\r\n        this.bone.setScale(boneScaling);\r\n\r\n        this._updateLinkedTransformRotation();\r\n    }\r\n\r\n    private _getAngleDiff(ang1: number, ang2: number): number {\r\n        let angDiff = ang2 - ang1;\r\n        angDiff %= Math.PI * 2;\r\n\r\n        if (angDiff > Math.PI) {\r\n            angDiff -= Math.PI * 2;\r\n        } else if (angDiff < -Math.PI) {\r\n            angDiff += Math.PI * 2;\r\n        }\r\n\r\n        return angDiff;\r\n    }\r\n\r\n    private _getAngleBetween(ang1: number, ang2: number): number {\r\n        ang1 %= 2 * Math.PI;\r\n        ang1 = ang1 < 0 ? ang1 + 2 * Math.PI : ang1;\r\n\r\n        ang2 %= 2 * Math.PI;\r\n        ang2 = ang2 < 0 ? ang2 + 2 * Math.PI : ang2;\r\n\r\n        let ab = 0;\r\n\r\n        if (ang1 < ang2) {\r\n            ab = ang2 - ang1;\r\n        } else {\r\n            ab = ang1 - ang2;\r\n        }\r\n\r\n        if (ab > Math.PI) {\r\n            ab = Math.PI * 2 - ab;\r\n        }\r\n\r\n        return ab;\r\n    }\r\n\r\n    private _isAngleBetween(ang: number, ang1: number, ang2: number): boolean {\r\n        ang %= 2 * Math.PI;\r\n        ang = ang < 0 ? ang + 2 * Math.PI : ang;\r\n        ang1 %= 2 * Math.PI;\r\n        ang1 = ang1 < 0 ? ang1 + 2 * Math.PI : ang1;\r\n        ang2 %= 2 * Math.PI;\r\n        ang2 = ang2 < 0 ? ang2 + 2 * Math.PI : ang2;\r\n\r\n        if (ang1 < ang2) {\r\n            if (ang > ang1 && ang < ang2) {\r\n                return true;\r\n            }\r\n        } else {\r\n            if (ang > ang2 && ang < ang1) {\r\n                return true;\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    private _updateLinkedTransformRotation(): void {\r\n        const bone = this.bone;\r\n        if (bone._linkedTransformNode) {\r\n            if (!bone._linkedTransformNode.rotationQuaternion) {\r\n                bone._linkedTransformNode.rotationQuaternion = new Quaternion();\r\n            }\r\n            bone.getRotationQuaternionToRef(Space.LOCAL, null, bone._linkedTransformNode.rotationQuaternion);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAGnE,OAAO,EAAS,IAAI,EAAE,MAAM,oBAAoB,CAAC;;;;AAM3C,MAAO,kBAAkB;IAuE3B;;OAEG,CACH,IAAI,MAAM,GAAA;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,MAAM,CAAC,KAAa,EAAA;QACpB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QACjD,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAI,MAAM,GAAA;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,MAAM,CAAC,KAAa,EAAA;QACpB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;YAC7F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QACjD,CAAC;IACL,CAAC;IAOD;;OAEG,CACH,IAAI,QAAQ,GAAA;QACR,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAI,QAAQ,CAAC,KAAa,EAAA;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG,CACH,IAAI,QAAQ,GAAA;QACR,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAI,QAAQ,CAAC,KAAa,EAAA;QACtB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA8BI,CACJ,YACI,IAAmB,EACnB,IAAU,EACV,MAAe,EACf,OAcC,CAAA;QAlKL;;WAEG,CACI,IAAA,CAAA,MAAM,kKAAY,UAAO,CAAC,EAAE,EAAE,CAAC;QAEtC;;WAEG,CACI,IAAA,CAAA,WAAW,GAAA,EAAA,eAAA,GAAsB;QAExC;;WAEG,CACI,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QAErB;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAEvB;;WAEG,CACI,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QAEtB;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAcf,IAAA,CAAA,SAAS,kKAAe,aAAU,CAAC,QAAQ,EAAE,CAAC;QAC9C,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAGlB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,WAAW,kKAAY,UAAO,CAAC,OAAO,EAAE,CAAC;QAoCjD;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QA6ElC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;YACvC,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACtB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YAC3C,CAAC;YAED,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;YACzC,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACjC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC;YAC1B,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACjC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;gBAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YACrC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC;YAC5B,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;gBAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YACrC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;gBAC9B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YAC3C,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACjC,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;gBAC9B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YAC3C,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;gBACvD,IAAI,UAAU,gKAAG,OAAI,CAAC,CAAC,CAAC;gBACxB,IAAI,YAAY,+JAAG,QAAI,CAAC,CAAC,CAAC;gBAE1B,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;oBAC1B,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;oBACrC,UAAU,CAAC,SAAS,EAAE,CAAC;gBAC3B,CAAC;gBAED,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;oBAC5B,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;oBACzC,YAAY,CAAC,SAAS,EAAE,CAAC;gBAC7B,CAAC;gBAED,MAAM,WAAW,kKAAG,UAAO,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAE5D,IAAI,CAAC,kBAAkB,GAAG,wKAAM,CAAC,QAAQ,EAAE,CAAC;+KAC5C,SAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAExF,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;gBAC7D,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;YACrC,CAAC;YAED,IAAI,OAAO,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;gBAC/C,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;YACjE,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,WAAW,IAAA,EAAA,cAAA,EAAc,GAAE,CAAC;YACtD,IAAI,CAAC,WAAW,GAAA,EAAA,eAAA,EAAc,CAAC;QACnC,CAAC;IACL,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,kFAAkF;QAClF,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACnD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,OAAO;QACX,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAElD,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEhD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEpC,MAAM,MAAM,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE7B,IAAI,IAAI,CAAC,WAAW,IAAA,EAAA,cAAA,EAAc,KAAI,UAAU,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;+KAC1B,UAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;YAClF,CAAC;YACD,UAAU,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5D,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,IAAA,EAAA,eAAA,EAAe,GAAE,CAAC;YACzC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpE,MAAM,CAAC,SAAS,EAAE,CAAC;YACvB,CAAC;QACL,CAAC;QAED,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YACtD,QAAQ,GAAG,IAAI,CAAC;QACpB,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YAC1D,UAAU,GAAG,IAAI,CAAC;QACtB,CAAC;QAED,IAAI,QAAQ,IAAI,UAAU,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAChD,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEnD,IAAI,IAAI,CAAC,WAAW,IAAA,EAAA,cAAA,EAAc,KAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,UAAU,EAAE,CAAC;gBAChE,UAAU,CAAC,sBAAsB,CAAA,EAAA,eAAA,KAAc,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACxE,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,IAAA,EAAA,eAAA,EAAe,KAAI,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACzE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YAC7C,CAAC,MAAM,CAAC;gBACJ,IAAI,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACjD,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAEvC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;mLAC1B,UAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;gBAC5F,CAAC;gBAED,IAAI,UAAU,EAAE,CAAC;oBACb,UAAU,CAAC,iBAAiB,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBACtE,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;gBACrD,CAAC;gBAED,MAAM,SAAS,kKAAG,UAAO,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACrD,SAAS,CAAC,SAAS,EAAE,CAAC;gBACtB,WAAW,GAAG,yKAAO,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;+KAE/C,SAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;YACtE,CAAC;YAED,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAElC,IAAI,KAAK,GAAqB,IAAI,CAAC;YAEnC,IAAI,UAAU,EAAE,CAAC;gBACb,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;+KAC3C,UAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;gBAEzE,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;gBACjF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC/C,IAAI,QAAQ,GAAG,KAAK,CAAC;gBAErB,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBACzB,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1C,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC9B,CAAC,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBAChC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC1C,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC9B,CAAC;gBAED,IAAI,KAAK,IAAI,QAAQ,EAAE,CAAC;mLACpB,UAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;oBACtE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAChC,MAAM,GAAG,WAAW,CAAC;gBACzB,CAAC;YACL,CAAC;YAED,IAAI,QAAQ,EAAE,CAAC;gBACX,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACnD,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;+KAC3C,UAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;gBAEzE,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;gBACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;gBACnE,IAAI,MAAM,GAAG,GAAG,CAAC;gBAEjB,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;oBACrD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;wBAChB,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;oBACrF,CAAC;oBAED,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;wBAC3B,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;4BAClE,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC1B,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;4BACzE,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC1B,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;4BAC1B,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;gCACzC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;4BACxB,CAAC;4BACD,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC1B,CAAC,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;4BACjC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;4BACxC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;gCACzC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;4BACxB,CAAC;4BACD,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC1B,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;oBAC7C,sDAAsD;oBACtD,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAC/C,OAAO,CAAC,QAAQ,8JAAC,OAAI,CAAC,CAAC,CAAC,CAAC;oBACzB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;uLAC1B,UAAO,CAAC,yBAAyB,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;oBACpF,CAAC;oBAED,MAAM,UAAU,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;oBAClD,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBAC5C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;mLACjE,UAAO,CAAC,yBAAyB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;mLAChE,UAAO,CAAC,yBAAyB,CAAC,OAAO,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;oBAEjE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;oBACjD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;oBACtD,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAE5E,IAAI,SAAS,GAAG,YAAY,EAAE,CAAC;wBAC3B,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;4BAChB,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;wBACrF,CAAC;wBAED,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;wBAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;wBAE/D,IAAI,SAAS,GAAG,SAAS,EAAE,CAAC;4BACxB,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;4BAClC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;4BACzC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;wBAC7C,CAAC,MAAM,CAAC;4BACJ,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;4BAClC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;4BACzC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;wBAC7C,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,IAAI,GAAG,IAAI,MAAM,EAAE,CAAC;mLAChB,UAAO,CAAC,yBAAyB,CAAC,WAAW,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;oBACtE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAChC,MAAM,GAAG,WAAW,CAAC;gBACzB,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,OAAO,GAAG,kBAAkB,CAAC,QAAQ,CAAC;QAC5C,MAAM,WAAW,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEnD,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,KAAK,CAAC,SAAS,EAAE,CAAC;sKAClB,WAAO,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACzC,KAAK,CAAC,SAAS,EAAE,CAAC;uKAClB,UAAO,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACxC,KAAK,CAAC,SAAS,EAAE,CAAC;uKAClB,SAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAEvD,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YAClD,OAAO;QACX,CAAC;QAED,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YAClD,OAAO;QACX,CAAC;QAED,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YAClD,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;2KACxD,SAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC9F,QAAQ,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAED,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAClB,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAA,EAAA,eAAA,KAAc,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACjF,CAAC;YACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC9D,CAAC;2KACD,aAAU,CAAC,uBAAuB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;2KACtD,aAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAEjF,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,EAAA,EAAA,eAAA,KAAe,IAAI,CAAC,IAAI,CAAC,CAAC;YACxE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC9D,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAA,EAAA,eAAA,KAAe,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAEhC,IAAI,CAAC,8BAA8B,EAAE,CAAC;IAC1C,CAAC;IAEO,aAAa,CAAC,IAAY,EAAE,IAAY,EAAA;QAC5C,IAAI,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;QAC1B,OAAO,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAEvB,IAAI,OAAO,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;YACpB,OAAO,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3B,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;YAC5B,OAAO,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,IAAY,EAAE,IAAY,EAAA;QAC/C,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACpB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAE5C,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACpB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAE5C,IAAI,EAAE,GAAG,CAAC,CAAC;QAEX,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;YACd,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;QACrB,CAAC,MAAM,CAAC;YACJ,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;QACrB,CAAC;QAED,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;YACf,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,GAAW,EAAE,IAAY,EAAE,IAAY,EAAA;QAC3D,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QACxC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACpB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5C,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACpB,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAE5C,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC;YACd,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,8BAA8B,GAAA;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,CAAC;gBAChD,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,GAAG,IAAI,4KAAU,EAAE,CAAC;YACpE,CAAC;YACD,IAAI,CAAC,0BAA0B,CAAA,EAAA,eAAA,KAAc,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QACrG,CAAC;IACL,CAAC;;AAvkBc,mBAAA,QAAQ,GAAc,2KAAA,AAAU,EAAC,EAAE,iKAAE,UAAO,CAAC,IAAI,CAAC,AAA1C,CAA2C;AACnD,mBAAA,QAAQ,kKAAG,aAAU,CAAC,QAAQ,EAAE,AAAxB,CAAyB;AACjC,mBAAA,QAAQ,iKAAa,aAAA,AAAU,EAAC,CAAC,iKAAE,SAAM,CAAC,QAAQ,CAAC,AAA3C,CAA4C", "debugId": null}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Bones/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Bones/index.ts"], "sourcesContent": ["export * from \"./bone\";\r\nexport * from \"./boneIKController\";\r\nexport * from \"./boneLookController\";\r\nexport * from \"./skeleton\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,QAAQ,CAAC;AACvB,cAAc,oBAAoB,CAAC;AACnC,cAAc,sBAAsB,CAAC;AACrC,cAAc,YAAY,CAAC", "debugId": null}}]}