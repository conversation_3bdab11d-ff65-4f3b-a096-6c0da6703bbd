{"version": 3, "file": "computeShader.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Compute/computeShader.ts"], "names": [], "mappings": ";AAIA,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAKlD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAG9D,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,cAAc,EAAE,MAAM,sCAAsC,CAAC;AAItE,OAAO,EAAE,iBAAiB,EAAE,+CAA8C;AAE1E,OAAO,EAAE,kBAAkB,EAAE,+BAA8B;AA+B3D;;GAEG;AACH,MAAM,OAAO,aAAa;IAsBtB;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAyBD;;;;;;;;;;OAUG;IACH,YAAY,IAAY,EAAE,MAAsB,EAAE,UAAuC,EAAE,UAA0C,EAAE;QAhE/H,cAAS,GAA+B,EAAE,CAAC;QAC3C,cAAS,GAAsC,EAAE,CAAC;QAElD,oBAAe,GAAG,KAAK,CAAC;QA2BhC;;;WAGG;QAEI,aAAQ,GAAG,KAAK,CAAC;QAExB;;WAEG;QACI,eAAU,GAA8C,IAAI,CAAC;QAEpE;;WAEG;QACI,YAAO,GAA8D,IAAI,CAAC;QAoB7E,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;QAC3C,IAAK,MAAuB,CAAC,2BAA2B,EAAE,CAAC;YACvD,IAAI,CAAC,cAAc,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC9D,OAAO;QACX,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;YACjH,OAAO;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,oBAAoB,EAAG,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG;YACZ,eAAe,EAAE,EAAE;YACnB,OAAO,EAAE,EAAE;YACX,GAAG,OAAO;SACb,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,YAAY;QACf,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,IAAY,EAAE,OAAoB,EAAE,WAAW,GAAG,IAAI;QACpE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;YACnB,IAAI,EAAE,WAAW,CAAC,CAAC,oCAA4B,CAAC,iDAAyC;YACzF,MAAM,EAAE,OAAO;YACf,mBAAmB,EAAE,OAAO,EAAE,mBAAmB;SACpD,CAAC;QAEF,IAAI,CAAC,eAAe,KAApB,IAAI,CAAC,eAAe,GAAK,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,EAAC;IAClH,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,IAAY,EAAE,OAAoB;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC,eAAe,KAApB,IAAI,CAAC,eAAe,GAAK,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,EAAC;QAEhE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;YACnB,IAAI,2CAAmC;YACvC,MAAM,EAAE,OAAO;YACf,mBAAmB,EAAE,OAAO,EAAE,mBAAmB;SACpD,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,kBAAkB,CAAC,IAAY,EAAE,OAAwB;QAC5D,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC,eAAe,KAApB,IAAI,CAAC,eAAe,GAAK,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,EAAC;QAEhE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;YACnB,IAAI,4CAAoC;YACxC,MAAM,EAAE,OAAO;YACf,mBAAmB,EAAE,OAAO,EAAE,mBAAmB;SACpD,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,IAAY,EAAE,OAAqB;QACtD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,IAAY,EAAE,MAAkC;QACpE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC,eAAe,KAApB,IAAI,CAAC,eAAe,GAAK,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAC;QAE/D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;YACnB,IAAI,EAAE,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,uCAA+B,CAAC,yCAAiC;YAClH,MAAM,EAAE,MAAM;YACd,mBAAmB,EAAE,OAAO,EAAE,mBAAmB;SACpD,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,IAAY,EAAE,MAAkC;QACpE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC,eAAe,KAApB,IAAI,CAAC,eAAe,GAAK,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAC;QAE/D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;YACnB,IAAI,EAAE,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,uCAA+B,CAAC,yCAAiC;YAClH,MAAM,EAAE,MAAM;YACd,mBAAmB,EAAE,OAAO,EAAE,mBAAmB;SACpD,CAAC;IACN,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,IAAY,EAAE,OAAuB;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC,eAAe,KAApB,IAAI,CAAC,eAAe,GAAK,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,EAAC;QAE7E,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;YACnB,IAAI,oCAA4B;YAChC,MAAM,EAAE,OAAO;YACf,mBAAmB,EAAE,OAAO,EAAE,mBAAmB;SACpD,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAE1B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAC/B,IAAI,GAAG,OAAO,CAAC,IAAI,EACnB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAE5B,QAAQ,IAAI,EAAE,CAAC;gBACX,wCAAgC;gBAChC,sDAA8C;gBAC9C,8CAAsC,CAAC,CAAC,CAAC;oBACrC,MAAM,OAAO,GAAG,MAAqB,CAAC;oBACtC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;wBACrB,OAAO,KAAK,CAAC;oBACjB,CAAC;oBACD,MAAM;gBACV,CAAC;gBACD,+CAAuC,CAAC,CAAC,CAAC;oBACtC,MAAM,OAAO,GAAG,MAAyB,CAAC;oBAC1C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;wBACrB,OAAO,KAAK,CAAC;oBACjB,CAAC;oBACD,MAAM;gBACV,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAEpC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;gBAChE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YAC/C,CAAC;QACL,CAAC;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAE3B,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,UAAU,EAAiC;gBACjF,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;gBACpC,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;aACxB,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,QAAQ,CAAC,CAAS,EAAE,CAAU,EAAE,CAAU;QAC7C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvI,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,MAAkC,EAAE,SAAiB,CAAC;QAC1E,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,MAAM,UAAU,GAAG,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAC3F,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1J,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,aAAa;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YAClB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,8JAA8J;QAC9J,0JAA0J;QAC1J,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAEpC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI,GAAG,6DAA6D,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAChI,CAAC;YAED,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACnB,uCAA+B,CAAC,CAAC,CAAC;oBAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;oBACpC,MAAM,OAAO,GAAG,OAAO,CAAC,MAAqB,CAAC;oBAE9C,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC7E,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,cAAc,EAAE,CAAC,aAAa,CACpD,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,yBAAyB,EACjC,OAAO,CAAC,QAAS,CAAC,YAAY,EAC9B,OAAO,CAAC,QAAQ,EAAE,mBAAmB,CACxC,CAAC;wBACF,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAChC,CAAC;oBACD,MAAM;gBACV,CAAC;gBACD,+CAAuC,CAAC,CAAC,CAAC;oBACtC,4IAA4I;oBAC5I,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,MAAM;gBACV,CAAC;gBACD,6CAAqC,CAAC,CAAC,CAAC;oBACpC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAuB,CAAC;oBAC5C,IAAI,GAAG,CAAC,SAAS,EAAE,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;wBACrC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;wBACjC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAChC,CAAC;oBACD,MAAM;gBACV,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC1B,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG;IACH,gEAAgE;IACzD,KAAK,CAAC,iBAAiB,CAAC,CAAS,EAAE,CAAU,EAAE,CAAU,EAAE,KAAK,GAAG,EAAE;QACxE,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,kBAAkB,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAChF,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEhE,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAClD,mBAAmB,CAAC,QAAQ,GAAG,EAAE,CAAC;QAClC,mBAAmB,CAAC,QAAQ,GAAG,EAAE,CAAC;QAElC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACpC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YAE9B,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACnB,wCAAgC;gBAChC,sDAA8C;gBAC9C,8CAAsC,CAAC,CAAC,CAAC;oBACrC,MAAM,cAAc,GAAI,MAAsB,CAAC,SAAS,EAAE,CAAC;oBAC3D,IAAI,cAAc,EAAE,CAAC;wBACjB,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;wBACnD,mBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG;4BAChC,IAAI,EAAE,OAAO,CAAC,IAAI;yBACrB,CAAC;oBACN,CAAC;oBACD,MAAM;gBACV,CAAC;gBAED,6CAAqC,CAAC,CAAC,CAAC;oBACpC,MAAM;gBACV,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe;QAC1D,MAAM,OAAO,GAAG,mBAAmB,CAAC,KAAK,CACrC,GAAG,EAAE,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,EAAkB,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,EAC1G,MAAM,EACN,KAAK,EACL,OAAO,CACV,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACrC,MAAM,OAAO,GAAY,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAE7E,IAAI,OAAO,CAAC,IAAI,uCAA+B,EAAE,CAAC;gBAC9C,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACrC,CAAC;iBAAM,IAAI,OAAO,CAAC,IAAI,qDAA6C,EAAE,CAAC;gBACnE,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAES,MAAM,CAAC,mBAAmB,CAAC,MAAkD;QACnF,OAAQ,MAAqB,CAAC,kBAAkB,KAAK,SAAS,CAAC;IACnE,CAAC;CACJ;AAzbU;IADN,SAAS,EAAE;2CACQ;AAqBb;IADN,SAAS,EAAE;+CACY;AAsa5B,aAAa,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC", "sourcesContent": ["import type { UniformBuffer } from \"../Materials/uniformBuffer\";\r\nimport type { WebGPUEngine } from \"../Engines/webgpuEngine\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport { serialize } from \"../Misc/decorators\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport type { ComputeEffect, IComputeEffectCreationOptions, IComputeShaderPath } from \"./computeEffect\";\r\nimport type { ComputeBindingMapping } from \"../Engines/Extensions/engine.computeShader\";\r\nimport { ComputeBindingType } from \"../Engines/Extensions/engine.computeShader\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { UniqueIdGenerator } from \"../Misc/uniqueIdGenerator\";\r\nimport type { IComputeContext } from \"./IComputeContext\";\r\nimport type { StorageBuffer } from \"../Buffers/storageBuffer\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { TextureSampler } from \"../Materials/Textures/textureSampler\";\r\nimport type { DataBuffer } from \"core/Buffers/dataBuffer\";\r\nimport type { ExternalTexture } from \"core/Materials/Textures/externalTexture\";\r\nimport type { VideoTexture } from \"core/Materials/Textures/videoTexture\";\r\nimport { WebGPUPerfCounter } from \"core/Engines/WebGPU/webgpuPerfCounter\";\r\nimport type { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport { _RetryWithInterval } from \"core/Misc/timingTools\";\r\n\r\n/**\r\n * Defines the options associated with the creation of a compute shader.\r\n */\r\nexport interface IComputeShaderOptions {\r\n    /**\r\n     * list of bindings mapping (key is property name, value is binding location)\r\n     * Must be provided because browsers don't support reflection for wgsl shaders yet (so there's no way to query the binding/group from a variable name)\r\n     * TODO: remove this when browsers support reflection for wgsl shaders\r\n     */\r\n    bindingsMapping: ComputeBindingMapping;\r\n\r\n    /**\r\n     * The list of defines used in the shader\r\n     */\r\n    defines?: string[];\r\n\r\n    /**\r\n     * The name of the entry point in the shader source (default: \"main\")\r\n     */\r\n    entryPoint?: string;\r\n\r\n    /**\r\n     * If provided, will be called with the shader code so that this code can be updated before it is compiled by the GPU\r\n     */\r\n    processFinalCode?: Nullable<(code: string) => string>;\r\n}\r\n\r\ntype ComputeBindingListInternal = { [key: string]: { type: ComputeBindingType; object: any; indexInGroupEntries?: number; buffer?: Nullable<DataBuffer> } };\r\n\r\n/**\r\n * The ComputeShader object lets you execute a compute shader on your GPU (if supported by the engine)\r\n */\r\nexport class ComputeShader {\r\n    private _engine: AbstractEngine;\r\n    private _shaderPath: IComputeShaderPath | string;\r\n    private _options: IComputeShaderOptions;\r\n    private _effect: ComputeEffect;\r\n    private _cachedDefines: string;\r\n    private _bindings: ComputeBindingListInternal = {};\r\n    private _samplers: { [key: string]: TextureSampler } = {};\r\n    private _context: IComputeContext;\r\n    private _contextIsDirty = false;\r\n\r\n    /**\r\n     * Gets the unique id of the compute shader\r\n     */\r\n    public readonly uniqueId: number;\r\n\r\n    /**\r\n     * The name of the shader\r\n     */\r\n    @serialize()\r\n    public name: string;\r\n\r\n    /**\r\n     * The options used to create the shader\r\n     */\r\n    public get options() {\r\n        return this._options;\r\n    }\r\n\r\n    /**\r\n     * The shaderPath used to create the shader\r\n     */\r\n    public get shaderPath() {\r\n        return this._shaderPath;\r\n    }\r\n\r\n    /**\r\n     * When set to true, dispatch won't call isReady anymore and won't check if the underlying GPU resources should be (re)created because of a change in the inputs (texture, uniform buffer, etc.)\r\n     * If you know that your inputs did not change since last time dispatch was called and that isReady() returns true, set this flag to true to improve performance\r\n     */\r\n    @serialize()\r\n    public fastMode = false;\r\n\r\n    /**\r\n     * Callback triggered when the shader is compiled\r\n     */\r\n    public onCompiled: Nullable<(effect: ComputeEffect) => void> = null;\r\n\r\n    /**\r\n     * Callback triggered when an error occurs\r\n     */\r\n    public onError: Nullable<(effect: ComputeEffect, errors: string) => void> = null;\r\n\r\n    /**\r\n     * Gets the GPU time spent running the compute shader for the last frame rendered (in nanoseconds).\r\n     * You have to enable the \"timestamp-query\" extension in the engine constructor options and set engine.enableGPUTimingMeasurements = true.\r\n     */\r\n    public readonly gpuTimeInFrame?: WebGPUPerfCounter;\r\n\r\n    /**\r\n     * Instantiates a new compute shader.\r\n     * @param name Defines the name of the compute shader in the scene\r\n     * @param engine Defines the engine the compute shader belongs to\r\n     * @param shaderPath Defines the route to the shader code in one of three ways:\r\n     *  * object: \\{ compute: \"custom\" \\}, used with ShaderStore.ShadersStoreWGSL[\"customComputeShader\"]\r\n     *  * object: \\{ computeElement: \"HTMLElementId\" \\}, used with shader code in script tags\r\n     *  * object: \\{ computeSource: \"compute shader code string\" \\}, where the string contains the shader code\r\n     *  * string: try first to find the code in ShaderStore.ShadersStoreWGSL[shaderPath + \"ComputeShader\"]. If not, assumes it is a file with name shaderPath.compute.fx in index.html folder.\r\n     * @param options Define the options used to create the shader\r\n     */\r\n    constructor(name: string, engine: AbstractEngine, shaderPath: IComputeShaderPath | string, options: Partial<IComputeShaderOptions> = {}) {\r\n        this.name = name;\r\n        this._engine = engine;\r\n        this.uniqueId = UniqueIdGenerator.UniqueId;\r\n        if ((engine as WebGPUEngine).enableGPUTimingMeasurements) {\r\n            this.gpuTimeInFrame = new WebGPUPerfCounter();\r\n        }\r\n\r\n        if (!this._engine.getCaps().supportComputeShaders) {\r\n            Logger.Error(\"This engine does not support compute shaders!\");\r\n            return;\r\n        }\r\n        if (!options.bindingsMapping) {\r\n            Logger.Error(\"You must provide the binding mappings as browsers don't support reflection for wgsl shaders yet!\");\r\n            return;\r\n        }\r\n\r\n        this._context = engine.createComputeContext()!;\r\n        this._shaderPath = shaderPath;\r\n        this._options = {\r\n            bindingsMapping: {},\r\n            defines: [],\r\n            ...options,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name of the material e.g. \"ComputeShader\"\r\n     * Mainly use in serialization.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ComputeShader\";\r\n    }\r\n\r\n    /**\r\n     * Binds a texture to the shader\r\n     * @param name Binding name of the texture\r\n     * @param texture Texture to bind\r\n     * @param bindSampler Bind the sampler corresponding to the texture (default: true). The sampler will be bound just before the binding index of the texture\r\n     */\r\n    public setTexture(name: string, texture: BaseTexture, bindSampler = true): void {\r\n        const current = this._bindings[name];\r\n\r\n        this._bindings[name] = {\r\n            type: bindSampler ? ComputeBindingType.Texture : ComputeBindingType.TextureWithoutSampler,\r\n            object: texture,\r\n            indexInGroupEntries: current?.indexInGroupEntries,\r\n        };\r\n\r\n        this._contextIsDirty ||= !current || current.object !== texture || current.type !== this._bindings[name].type;\r\n    }\r\n\r\n    /**\r\n     * Binds a storage texture to the shader\r\n     * @param name Binding name of the texture\r\n     * @param texture Texture to bind\r\n     */\r\n    public setStorageTexture(name: string, texture: BaseTexture): void {\r\n        const current = this._bindings[name];\r\n\r\n        this._contextIsDirty ||= !current || current.object !== texture;\r\n\r\n        this._bindings[name] = {\r\n            type: ComputeBindingType.StorageTexture,\r\n            object: texture,\r\n            indexInGroupEntries: current?.indexInGroupEntries,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Binds an external texture to the shader\r\n     * @param name Binding name of the texture\r\n     * @param texture Texture to bind\r\n     */\r\n    public setExternalTexture(name: string, texture: ExternalTexture): void {\r\n        const current = this._bindings[name];\r\n\r\n        this._contextIsDirty ||= !current || current.object !== texture;\r\n\r\n        this._bindings[name] = {\r\n            type: ComputeBindingType.ExternalTexture,\r\n            object: texture,\r\n            indexInGroupEntries: current?.indexInGroupEntries,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Binds a video texture to the shader (by binding the external texture attached to this video)\r\n     * @param name Binding name of the texture\r\n     * @param texture Texture to bind\r\n     * @returns true if the video texture was successfully bound, else false. false will be returned if the current engine does not support external textures\r\n     */\r\n    public setVideoTexture(name: string, texture: VideoTexture) {\r\n        if (texture.externalTexture) {\r\n            this.setExternalTexture(name, texture.externalTexture);\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Binds a uniform buffer to the shader\r\n     * @param name Binding name of the buffer\r\n     * @param buffer Buffer to bind\r\n     */\r\n    public setUniformBuffer(name: string, buffer: UniformBuffer | DataBuffer): void {\r\n        const current = this._bindings[name];\r\n\r\n        this._contextIsDirty ||= !current || current.object !== buffer;\r\n\r\n        this._bindings[name] = {\r\n            type: ComputeShader._BufferIsDataBuffer(buffer) ? ComputeBindingType.DataBuffer : ComputeBindingType.UniformBuffer,\r\n            object: buffer,\r\n            indexInGroupEntries: current?.indexInGroupEntries,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Binds a storage buffer to the shader\r\n     * @param name Binding name of the buffer\r\n     * @param buffer Buffer to bind\r\n     */\r\n    public setStorageBuffer(name: string, buffer: StorageBuffer | DataBuffer): void {\r\n        const current = this._bindings[name];\r\n\r\n        this._contextIsDirty ||= !current || current.object !== buffer;\r\n\r\n        this._bindings[name] = {\r\n            type: ComputeShader._BufferIsDataBuffer(buffer) ? ComputeBindingType.DataBuffer : ComputeBindingType.StorageBuffer,\r\n            object: buffer,\r\n            indexInGroupEntries: current?.indexInGroupEntries,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Binds a texture sampler to the shader\r\n     * @param name Binding name of the sampler\r\n     * @param sampler Sampler to bind\r\n     */\r\n    public setTextureSampler(name: string, sampler: TextureSampler): void {\r\n        const current = this._bindings[name];\r\n\r\n        this._contextIsDirty ||= !current || !sampler.compareSampler(current.object);\r\n\r\n        this._bindings[name] = {\r\n            type: ComputeBindingType.Sampler,\r\n            object: sampler,\r\n            indexInGroupEntries: current?.indexInGroupEntries,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Specifies that the compute shader is ready to be executed (the compute effect and all the resources are ready)\r\n     * @returns true if the compute shader is ready to be executed\r\n     */\r\n    public isReady(): boolean {\r\n        let effect = this._effect;\r\n\r\n        for (const key in this._bindings) {\r\n            const binding = this._bindings[key],\r\n                type = binding.type,\r\n                object = binding.object;\r\n\r\n            switch (type) {\r\n                case ComputeBindingType.Texture:\r\n                case ComputeBindingType.TextureWithoutSampler:\r\n                case ComputeBindingType.StorageTexture: {\r\n                    const texture = object as BaseTexture;\r\n                    if (!texture.isReady()) {\r\n                        return false;\r\n                    }\r\n                    break;\r\n                }\r\n                case ComputeBindingType.ExternalTexture: {\r\n                    const texture = object as ExternalTexture;\r\n                    if (!texture.isReady()) {\r\n                        return false;\r\n                    }\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        const defines = [];\r\n\r\n        const shaderName = this._shaderPath;\r\n\r\n        if (this._options.defines) {\r\n            for (let index = 0; index < this._options.defines.length; index++) {\r\n                defines.push(this._options.defines[index]);\r\n            }\r\n        }\r\n\r\n        const join = defines.join(\"\\n\");\r\n\r\n        if (this._cachedDefines !== join) {\r\n            this._cachedDefines = join;\r\n\r\n            effect = this._engine.createComputeEffect(shaderName, <IComputeEffectCreationOptions>{\r\n                defines: join,\r\n                entryPoint: this._options.entryPoint,\r\n                onCompiled: this.onCompiled,\r\n                onError: this.onError,\r\n            });\r\n\r\n            this._effect = effect;\r\n        }\r\n\r\n        if (!effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispatches (executes) the compute shader\r\n     * @param x Number of workgroups to execute on the X dimension\r\n     * @param y Number of workgroups to execute on the Y dimension (default: 1)\r\n     * @param z Number of workgroups to execute on the Z dimension (default: 1)\r\n     * @returns True if the dispatch could be done, else false (meaning either the compute effect or at least one of the bound resources was not ready)\r\n     */\r\n    public dispatch(x: number, y?: number, z?: number): boolean {\r\n        if (!this.fastMode && !this._checkContext()) {\r\n            return false;\r\n        }\r\n        this._engine.computeDispatch(this._effect, this._context, this._bindings, x, y, z, this._options.bindingsMapping, this.gpuTimeInFrame);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Dispatches (executes) the compute shader.\r\n     * @param buffer Buffer containing the number of workgroups to execute on the X, Y and Z dimensions\r\n     * @param offset Offset in the buffer where the workgroup counts are stored (default: 0)\r\n     * @returns True if the dispatch could be done, else false (meaning either the compute effect or at least one of the bound resources was not ready)\r\n     */\r\n    public dispatchIndirect(buffer: StorageBuffer | DataBuffer, offset: number = 0): boolean {\r\n        if (!this.fastMode && !this._checkContext()) {\r\n            return false;\r\n        }\r\n        const dataBuffer = ComputeShader._BufferIsDataBuffer(buffer) ? buffer : buffer.getBuffer();\r\n        this._engine.computeDispatchIndirect(this._effect, this._context, this._bindings, dataBuffer, offset, this._options.bindingsMapping, this.gpuTimeInFrame);\r\n        return true;\r\n    }\r\n\r\n    private _checkContext(): boolean {\r\n        if (!this.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        // If the sampling parameters of a texture bound to the shader have changed, we must clear the compute context so that it is recreated with the updated values\r\n        // Also, if the actual (gpu) buffer used by a uniform buffer has changed, we must clear the compute context so that it is recreated with the updated value\r\n        for (const key in this._bindings) {\r\n            const binding = this._bindings[key];\r\n\r\n            if (!this._options.bindingsMapping[key]) {\r\n                throw new Error(\"ComputeShader ('\" + this.name + \"'): No binding mapping has been provided for the property '\" + key + \"'\");\r\n            }\r\n\r\n            switch (binding.type) {\r\n                case ComputeBindingType.Texture: {\r\n                    const sampler = this._samplers[key];\r\n                    const texture = binding.object as BaseTexture;\r\n\r\n                    if (!sampler || !texture._texture || !sampler.compareSampler(texture._texture)) {\r\n                        this._samplers[key] = new TextureSampler().setParameters(\r\n                            texture.wrapU,\r\n                            texture.wrapV,\r\n                            texture.wrapR,\r\n                            texture.anisotropicFilteringLevel,\r\n                            texture._texture!.samplingMode,\r\n                            texture._texture?._comparisonFunction\r\n                        );\r\n                        this._contextIsDirty = true;\r\n                    }\r\n                    break;\r\n                }\r\n                case ComputeBindingType.ExternalTexture: {\r\n                    // we must recreate the bind groups each time if there's an external texture, because device.importExternalTexture must be called each frame\r\n                    this._contextIsDirty = true;\r\n                    break;\r\n                }\r\n                case ComputeBindingType.UniformBuffer: {\r\n                    const ubo = binding.object as UniformBuffer;\r\n                    if (ubo.getBuffer() !== binding.buffer) {\r\n                        binding.buffer = ubo.getBuffer();\r\n                        this._contextIsDirty = true;\r\n                    }\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (this._contextIsDirty) {\r\n            this._contextIsDirty = false;\r\n            this._context.clear();\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Waits for the compute shader to be ready and executes it\r\n     * @param x Number of workgroups to execute on the X dimension\r\n     * @param y Number of workgroups to execute on the Y dimension (default: 1)\r\n     * @param z Number of workgroups to execute on the Z dimension (default: 1)\r\n     * @param delay Delay between the retries while the shader is not ready (in milliseconds - 10 by default)\r\n     * @returns A promise that is resolved once the shader has been sent to the GPU. Note that it does not mean that the shader execution itself is finished!\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public async dispatchWhenReady(x: number, y?: number, z?: number, delay = 10): Promise<void> {\r\n        return await new Promise((resolve) => {\r\n            _RetryWithInterval(() => this.dispatch(x, y, z), resolve, undefined, delay);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Serializes this compute shader in a JSON representation\r\n     * @returns the serialized compute shader object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n\r\n        serializationObject.options = this._options;\r\n        serializationObject.shaderPath = this._shaderPath;\r\n        serializationObject.bindings = {};\r\n        serializationObject.textures = {};\r\n\r\n        for (const key in this._bindings) {\r\n            const binding = this._bindings[key];\r\n            const object = binding.object;\r\n\r\n            switch (binding.type) {\r\n                case ComputeBindingType.Texture:\r\n                case ComputeBindingType.TextureWithoutSampler:\r\n                case ComputeBindingType.StorageTexture: {\r\n                    const serializedData = (object as BaseTexture).serialize();\r\n                    if (serializedData) {\r\n                        serializationObject.textures[key] = serializedData;\r\n                        serializationObject.bindings[key] = {\r\n                            type: binding.type,\r\n                        };\r\n                    }\r\n                    break;\r\n                }\r\n\r\n                case ComputeBindingType.UniformBuffer: {\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Creates a compute shader from parsed compute shader data\r\n     * @param source defines the JSON representation of the compute shader\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a new compute shader\r\n     */\r\n    public static Parse(source: any, scene: Scene, rootUrl: string): ComputeShader {\r\n        const compute = SerializationHelper.Parse(\r\n            () => new ComputeShader(source.name, scene.getEngine() as WebGPUEngine, source.shaderPath, source.options),\r\n            source,\r\n            scene,\r\n            rootUrl\r\n        );\r\n\r\n        for (const key in source.textures) {\r\n            const binding = source.bindings[key];\r\n            const texture = <Texture>Texture.Parse(source.textures[key], scene, rootUrl);\r\n\r\n            if (binding.type === ComputeBindingType.Texture) {\r\n                compute.setTexture(key, texture);\r\n            } else if (binding.type === ComputeBindingType.TextureWithoutSampler) {\r\n                compute.setTexture(key, texture, false);\r\n            } else {\r\n                compute.setStorageTexture(key, texture);\r\n            }\r\n        }\r\n\r\n        return compute;\r\n    }\r\n\r\n    protected static _BufferIsDataBuffer(buffer: UniformBuffer | StorageBuffer | DataBuffer): buffer is DataBuffer {\r\n        return (buffer as DataBuffer).underlyingResource !== undefined;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ComputeShader\", ComputeShader);\r\n"]}