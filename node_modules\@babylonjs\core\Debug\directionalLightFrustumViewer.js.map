{"version": 3, "file": "directionalLightFrustumViewer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Debug/directionalLightFrustumViewer.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAE9D,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,OAAO,EAAE,SAAS,EAAE,gCAA+B;AAEnD,OAAO,EAAE,eAAe,EAAE,yCAAwC;AAElE;;;;GAIG;AACH,MAAM,OAAO,6BAA6B;IAmCtC;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAAa;QACjC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAS,CAAC,KAAK,GAAG,KAAK,CAAC;QAC9D,CAAC;IACL,CAAC;IAGD;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,IAAa;QAC9B,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAGD;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,IAAa;QAC/B,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,YAAY,KAAuB,EAAE,SAA2B,IAAI;QAlE5D,iBAAY,GAAY,IAAI,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QACxE,kBAAa,GAAY,IAAI,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QASzE,kBAAa,GAAG,GAAG,CAAC;QAepB,eAAU,GAAG,IAAI,CAAC;QAkBlB,gBAAW,GAAG,IAAI,CAAC;QAwBvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC5C,IAAI,CAAC,yBAAyB,GAAG,EAAE,CAAC;QACpC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnD,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC;QACD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAEO,sBAAsB,CAAC,UAAsB;QACjD,MAAM,cAAc,GAAG,eAAe,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,cAAc,IAAI,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACrD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBAChD,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;IACL,CAAC;IAEO,2BAA2B,CAAC,UAAsB;QACtD,MAAM,cAAc,GAAG,eAAe,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAC1E,IAAI,cAAc,IAAI,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACrD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBAChD,MAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC,MAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACtD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,MAAM;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QAED,IACI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YAChD,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,MAAM,CAAC,qBAAqB;YACvD,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU;YACxC,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU;YACxC,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS;YAC5C,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU;YAC9C,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC1C,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,MAAM,CAAC,WAAW,EAClD,CAAC;YACC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;QACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;QAC3C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QAE/C,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CACrB,IAAI,CAAC,MAAM,CAAC,SAAS,EACrB,IAAI,CAAC,MAAM,CAAC,WAAW,EACvB,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC,CAC/G,CAAC,CAAC,oBAAoB;QACvB,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CACrB,IAAI,CAAC,MAAM,CAAC,UAAU,EACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC,CAC/G,CAAC,CAAC,oBAAoB;QAEvB,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAEjD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QACtH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QACtH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QACtH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QAEtH,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;QACvG,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;QACvG,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;QACvG,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ;QAEvG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QACtH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QACtH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QACtH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QAEtH,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;QACtG,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;QACtG,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;QACtG,OAAO,CAAC,yBAAyB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO;QAEtG,WAAW,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEpJ,WAAW,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAElJ,WAAW,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhJ,WAAW,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhJ,WAAW,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhJ,WAAW,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhJ,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAE/G,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QAE9G,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QAC3D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QAC3D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QAC3D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QAC3D,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QAEhH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAE/G,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QAE/G,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAC5D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAC5D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAC5D,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAC5D,IAAI,CAAC,yBAAyB,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,0BAA0B,CAAC,UAAU,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;IACtH,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC;QACD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAChD,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAES,eAAe;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,aAAa,CAAC,6BAA6B,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAClG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAE3C,IAAI,CAAC,gBAAgB,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5I,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5G,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,SAAS,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAE1C,IAAI,CAAC,eAAe,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3I,MAAM,QAAQ,GAAG,WAAW,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACzG,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,QAAQ,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAEzC,IAAI,CAAC,cAAc,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACvE,MAAM,OAAO,GAAG,WAAW,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,cAAc,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACtE,MAAM,OAAO,GAAG,WAAW,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,cAAc,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,MAAM,OAAO,GAAG,WAAW,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,cAAc,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,MAAM,OAAO,GAAG,WAAW,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACtG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;QAExC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAE7F,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,KAAa,EAAE,SAAmB,EAAE,EAAE;YACnE,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,GAAG,GAAG,IAAI,gBAAgB,CAAC,IAAI,GAAG,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAEjE,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC;YACrB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9B,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAAC;YAEtC,GAAG,CAAC,aAAa,GAAG,KAAK,CAAC;YAC1B,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;YAC9B,GAAG,CAAC,eAAe,GAAG,KAAK,CAAC;YAC5B,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;YAE3B,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAEnC,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;YAEpC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;YACjC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAE7B,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAEpC,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC;QAEF,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChE,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjE,SAAS,CAAC,MAAM,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAChE,SAAS,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChE,SAAS,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAClE,SAAS,CAAC,MAAM,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAClE,SAAS,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC9D,SAAS,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEtE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEjD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEhD,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAES,oBAAoB;QAC1B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzI,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { DirectionalLight } from \"../Lights/directionalLight\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { Matrix, TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport { CreateLines } from \"../Meshes/Builders/linesBuilder\";\r\nimport type { LinesMesh } from \"../Meshes/linesMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { VertexData } from \"../Meshes/mesh.vertexData\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport type { FrameGraph } from \"core/FrameGraph/frameGraph\";\r\nimport { FrameGraphUtils } from \"core/FrameGraph/frameGraphUtils\";\r\n\r\n/**\r\n * Class used to render a debug view of the frustum for a directional light\r\n * @see https://playground.babylonjs.com/#7EFGSG#4\r\n * @since 5.0.0\r\n */\r\nexport class DirectionalLightFrustumViewer {\r\n    private _scene: Scene;\r\n    private _light: DirectionalLight;\r\n    private _camera: Nullable<Camera>;\r\n    private _inverseViewMatrix: Matrix;\r\n    private _visible: boolean;\r\n\r\n    private _rootNode: TransformNode;\r\n    private _lightHelperFrustumMeshes: Mesh[];\r\n\r\n    private _nearLinesPoints: Vector3[];\r\n    private _farLinesPoints: Vector3[];\r\n    private _trLinesPoints: Vector3[];\r\n    private _brLinesPoints: Vector3[];\r\n    private _tlLinesPoints: Vector3[];\r\n    private _blLinesPoints: Vector3[];\r\n\r\n    private _nearPlaneVertices: number[];\r\n    private _farPlaneVertices: number[];\r\n    private _rightPlaneVertices: number[];\r\n    private _leftPlaneVertices: number[];\r\n    private _topPlaneVertices: number[];\r\n    private _bottomPlaneVertices: number[];\r\n\r\n    private _oldPosition: Vector3 = new Vector3(Number.NaN, Number.NaN, Number.NaN);\r\n    private _oldDirection: Vector3 = new Vector3(Number.NaN, Number.NaN, Number.NaN);\r\n    private _oldAutoCalc: boolean;\r\n    private _oldMinZ: number;\r\n    private _oldMaxZ: number;\r\n    private _oldOrthoLeft: number;\r\n    private _oldOrthoRight: number;\r\n    private _oldOrthoTop: number;\r\n    private _oldOrthoBottom: number;\r\n\r\n    private _transparency = 0.3;\r\n    /**\r\n     * Gets or sets the transparency of the frustum planes\r\n     */\r\n    public get transparency(): number {\r\n        return this._transparency;\r\n    }\r\n\r\n    public set transparency(alpha: number) {\r\n        this._transparency = alpha;\r\n        for (let i = 6; i < 12; ++i) {\r\n            this._lightHelperFrustumMeshes[i].material!.alpha = alpha;\r\n        }\r\n    }\r\n\r\n    private _showLines = true;\r\n    /**\r\n     * true to display the edges of the frustum\r\n     */\r\n    public get showLines(): boolean {\r\n        return this._showLines;\r\n    }\r\n\r\n    public set showLines(show: boolean) {\r\n        if (this._showLines === show) {\r\n            return;\r\n        }\r\n        this._showLines = show;\r\n        for (let i = 0; i < 6; ++i) {\r\n            this._lightHelperFrustumMeshes[i].setEnabled(show);\r\n        }\r\n    }\r\n\r\n    private _showPlanes = true;\r\n    /**\r\n     * true to display the planes of the frustum\r\n     */\r\n    public get showPlanes(): boolean {\r\n        return this._showPlanes;\r\n    }\r\n\r\n    public set showPlanes(show: boolean) {\r\n        if (this._showPlanes === show) {\r\n            return;\r\n        }\r\n        this._showPlanes = show;\r\n        for (let i = 6; i < 12; ++i) {\r\n            this._lightHelperFrustumMeshes[i].setEnabled(show);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a new frustum viewer\r\n     * @param light directional light to display the frustum for\r\n     * @param camera camera used to retrieve the minZ / maxZ values if the shadowMinZ/shadowMaxZ values of the light are not setup\r\n     */\r\n    constructor(light: DirectionalLight, camera: Nullable<Camera> = null) {\r\n        this._scene = light.getScene();\r\n        this._light = light;\r\n        this._camera = camera;\r\n        this._inverseViewMatrix = Matrix.Identity();\r\n        this._lightHelperFrustumMeshes = [];\r\n        this._createGeometry();\r\n        this.show();\r\n        this.update();\r\n    }\r\n\r\n    /**\r\n     * Shows the frustum\r\n     */\r\n    public show() {\r\n        if (this._scene.frameGraph) {\r\n            this._removeMeshesFromFrameGraph(this._scene.frameGraph);\r\n            this._addMeshesToFrameGraph(this._scene.frameGraph);\r\n        }\r\n        this._lightHelperFrustumMeshes.forEach((mesh, index) => {\r\n            mesh.setEnabled((index < 6 && this._showLines) || (index >= 6 && this._showPlanes));\r\n        });\r\n        this._oldPosition.set(Number.NaN, Number.NaN, Number.NaN);\r\n        this._visible = true;\r\n    }\r\n\r\n    /**\r\n     * Hides the frustum\r\n     */\r\n    public hide() {\r\n        if (this._scene.frameGraph) {\r\n            this._removeMeshesFromFrameGraph(this._scene.frameGraph);\r\n        }\r\n        for (const mesh of this._lightHelperFrustumMeshes) {\r\n            mesh.setEnabled(false);\r\n        }\r\n        this._visible = false;\r\n    }\r\n\r\n    private _addMeshesToFrameGraph(frameGraph: FrameGraph) {\r\n        const objectRenderer = FrameGraphUtils.FindMainObjectRenderer(frameGraph);\r\n        if (objectRenderer && objectRenderer.objectList.meshes) {\r\n            for (const mesh of this._lightHelperFrustumMeshes) {\r\n                objectRenderer.objectList.meshes.push(mesh);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _removeMeshesFromFrameGraph(frameGraph: FrameGraph) {\r\n        const objectRenderer = FrameGraphUtils.FindMainObjectRenderer(frameGraph);\r\n        if (objectRenderer && objectRenderer.objectList.meshes) {\r\n            for (const mesh of this._lightHelperFrustumMeshes) {\r\n                const index = objectRenderer.objectList.meshes!.indexOf(mesh);\r\n                if (index !== -1) {\r\n                    objectRenderer.objectList.meshes.splice(index, 1);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the frustum.\r\n     * Call this method to update the frustum view if the light has changed position/direction\r\n     */\r\n    public update() {\r\n        if (!this._visible) {\r\n            return;\r\n        }\r\n\r\n        if (\r\n            this._oldPosition.equals(this._light.position) &&\r\n            this._oldDirection.equals(this._light.direction) &&\r\n            this._oldAutoCalc === this._light.autoCalcShadowZBounds &&\r\n            this._oldMinZ === this._light.shadowMinZ &&\r\n            this._oldMaxZ === this._light.shadowMaxZ &&\r\n            this._oldOrthoLeft === this._light.orthoLeft &&\r\n            this._oldOrthoRight === this._light.orthoRight &&\r\n            this._oldOrthoTop === this._light.orthoTop &&\r\n            this._oldOrthoBottom === this._light.orthoBottom\r\n        ) {\r\n            return;\r\n        }\r\n\r\n        this._oldPosition.copyFrom(this._light.position);\r\n        this._oldDirection.copyFrom(this._light.direction);\r\n        this._oldAutoCalc = this._light.autoCalcShadowZBounds;\r\n        this._oldMinZ = this._light.shadowMinZ;\r\n        this._oldMaxZ = this._light.shadowMaxZ;\r\n        this._oldOrthoLeft = this._light.orthoLeft;\r\n        this._oldOrthoRight = this._light.orthoRight;\r\n        this._oldOrthoTop = this._light.orthoTop;\r\n        this._oldOrthoBottom = this._light.orthoBottom;\r\n\r\n        TmpVectors.Vector3[0].set(\r\n            this._light.orthoLeft,\r\n            this._light.orthoBottom,\r\n            this._light.shadowMinZ !== undefined ? this._light.shadowMinZ : (this._camera?.minZ ?? Constants.ShadowMinZ)\r\n        ); // min light extents\r\n        TmpVectors.Vector3[1].set(\r\n            this._light.orthoRight,\r\n            this._light.orthoTop,\r\n            this._light.shadowMaxZ !== undefined ? this._light.shadowMaxZ : (this._camera?.maxZ ?? Constants.ShadowMaxZ)\r\n        ); // max light extents\r\n\r\n        const invLightView = this._getInvertViewMatrix();\r\n\r\n        TmpVectors.Vector3[2].copyFromFloats(TmpVectors.Vector3[1].x, TmpVectors.Vector3[1].y, TmpVectors.Vector3[0].z); // n1\r\n        TmpVectors.Vector3[3].copyFromFloats(TmpVectors.Vector3[1].x, TmpVectors.Vector3[0].y, TmpVectors.Vector3[0].z); // n2\r\n        TmpVectors.Vector3[4].copyFromFloats(TmpVectors.Vector3[0].x, TmpVectors.Vector3[0].y, TmpVectors.Vector3[0].z); // n3\r\n        TmpVectors.Vector3[5].copyFromFloats(TmpVectors.Vector3[0].x, TmpVectors.Vector3[1].y, TmpVectors.Vector3[0].z); // n4\r\n\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[2], invLightView, TmpVectors.Vector3[2]); // near1\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[3], invLightView, TmpVectors.Vector3[3]); // near2\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[4], invLightView, TmpVectors.Vector3[4]); // near3\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[5], invLightView, TmpVectors.Vector3[5]); // near4\r\n\r\n        TmpVectors.Vector3[6].copyFromFloats(TmpVectors.Vector3[1].x, TmpVectors.Vector3[1].y, TmpVectors.Vector3[1].z); // f1\r\n        TmpVectors.Vector3[7].copyFromFloats(TmpVectors.Vector3[1].x, TmpVectors.Vector3[0].y, TmpVectors.Vector3[1].z); // f2\r\n        TmpVectors.Vector3[8].copyFromFloats(TmpVectors.Vector3[0].x, TmpVectors.Vector3[0].y, TmpVectors.Vector3[1].z); // f3\r\n        TmpVectors.Vector3[9].copyFromFloats(TmpVectors.Vector3[0].x, TmpVectors.Vector3[1].y, TmpVectors.Vector3[1].z); // f4\r\n\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[6], invLightView, TmpVectors.Vector3[6]); // far1\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[7], invLightView, TmpVectors.Vector3[7]); // far2\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[8], invLightView, TmpVectors.Vector3[8]); // far3\r\n        Vector3.TransformCoordinatesToRef(TmpVectors.Vector3[9], invLightView, TmpVectors.Vector3[9]); // far4\r\n\r\n        CreateLines(\"nearlines\", { updatable: true, points: this._nearLinesPoints, instance: this._lightHelperFrustumMeshes[0] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"farlines\", { updatable: true, points: this._farLinesPoints, instance: this._lightHelperFrustumMeshes[1] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"trlines\", { updatable: true, points: this._trLinesPoints, instance: this._lightHelperFrustumMeshes[2] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"brlines\", { updatable: true, points: this._brLinesPoints, instance: this._lightHelperFrustumMeshes[3] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"tllines\", { updatable: true, points: this._tlLinesPoints, instance: this._lightHelperFrustumMeshes[4] as LinesMesh }, this._scene);\r\n\r\n        CreateLines(\"bllines\", { updatable: true, points: this._blLinesPoints, instance: this._lightHelperFrustumMeshes[5] as LinesMesh }, this._scene);\r\n\r\n        TmpVectors.Vector3[2].toArray(this._nearPlaneVertices, 0);\r\n        TmpVectors.Vector3[3].toArray(this._nearPlaneVertices, 3);\r\n        TmpVectors.Vector3[4].toArray(this._nearPlaneVertices, 6);\r\n        TmpVectors.Vector3[5].toArray(this._nearPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[6].geometry?.updateVerticesDataDirectly(\"position\", this._nearPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[6].toArray(this._farPlaneVertices, 0);\r\n        TmpVectors.Vector3[7].toArray(this._farPlaneVertices, 3);\r\n        TmpVectors.Vector3[8].toArray(this._farPlaneVertices, 6);\r\n        TmpVectors.Vector3[9].toArray(this._farPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[7].geometry?.updateVerticesDataDirectly(\"position\", this._farPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[2].toArray(this._rightPlaneVertices, 0);\r\n        TmpVectors.Vector3[6].toArray(this._rightPlaneVertices, 3);\r\n        TmpVectors.Vector3[7].toArray(this._rightPlaneVertices, 6);\r\n        TmpVectors.Vector3[3].toArray(this._rightPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[8].geometry?.updateVerticesDataDirectly(\"position\", this._rightPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[5].toArray(this._leftPlaneVertices, 0);\r\n        TmpVectors.Vector3[9].toArray(this._leftPlaneVertices, 3);\r\n        TmpVectors.Vector3[8].toArray(this._leftPlaneVertices, 6);\r\n        TmpVectors.Vector3[4].toArray(this._leftPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[9].geometry?.updateVerticesDataDirectly(\"position\", this._leftPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[2].toArray(this._topPlaneVertices, 0);\r\n        TmpVectors.Vector3[6].toArray(this._topPlaneVertices, 3);\r\n        TmpVectors.Vector3[9].toArray(this._topPlaneVertices, 6);\r\n        TmpVectors.Vector3[5].toArray(this._topPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[10].geometry?.updateVerticesDataDirectly(\"position\", this._topPlaneVertices, 0);\r\n\r\n        TmpVectors.Vector3[3].toArray(this._bottomPlaneVertices, 0);\r\n        TmpVectors.Vector3[7].toArray(this._bottomPlaneVertices, 3);\r\n        TmpVectors.Vector3[8].toArray(this._bottomPlaneVertices, 6);\r\n        TmpVectors.Vector3[4].toArray(this._bottomPlaneVertices, 9);\r\n        this._lightHelperFrustumMeshes[11].geometry?.updateVerticesDataDirectly(\"position\", this._bottomPlaneVertices, 0);\r\n    }\r\n\r\n    /**\r\n     * Dispose of the class / remove the frustum view\r\n     */\r\n    public dispose() {\r\n        if (this._scene.frameGraph) {\r\n            this._removeMeshesFromFrameGraph(this._scene.frameGraph);\r\n        }\r\n        for (const mesh of this._lightHelperFrustumMeshes) {\r\n            mesh.material?.dispose();\r\n            mesh.dispose();\r\n        }\r\n        this._rootNode.dispose();\r\n    }\r\n\r\n    protected _createGeometry() {\r\n        this._rootNode = new TransformNode(\"directionalLightHelperRoot_\" + this._light.name, this._scene);\r\n        this._rootNode.parent = this._light.parent;\r\n\r\n        this._nearLinesPoints = [TmpVectors.Vector3[0], TmpVectors.Vector3[1], TmpVectors.Vector3[2], TmpVectors.Vector3[3], TmpVectors.Vector3[4]];\r\n        const nearLines = CreateLines(\"nearlines\", { updatable: true, points: this._nearLinesPoints }, this._scene);\r\n        nearLines.parent = this._rootNode;\r\n        nearLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._farLinesPoints = [TmpVectors.Vector3[5], TmpVectors.Vector3[6], TmpVectors.Vector3[7], TmpVectors.Vector3[8], TmpVectors.Vector3[9]];\r\n        const farLines = CreateLines(\"farlines\", { updatable: true, points: this._farLinesPoints }, this._scene);\r\n        farLines.parent = this._rootNode;\r\n        farLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._trLinesPoints = [TmpVectors.Vector3[10], TmpVectors.Vector3[11]];\r\n        const trLines = CreateLines(\"trlines\", { updatable: true, points: this._trLinesPoints }, this._scene);\r\n        trLines.parent = this._rootNode;\r\n        trLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._brLinesPoints = [TmpVectors.Vector3[12], TmpVectors.Vector3[0]];\r\n        const brLines = CreateLines(\"brlines\", { updatable: true, points: this._brLinesPoints }, this._scene);\r\n        brLines.parent = this._rootNode;\r\n        brLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._tlLinesPoints = [TmpVectors.Vector3[1], TmpVectors.Vector3[2]];\r\n        const tlLines = CreateLines(\"tllines\", { updatable: true, points: this._tlLinesPoints }, this._scene);\r\n        tlLines.parent = this._rootNode;\r\n        tlLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._blLinesPoints = [TmpVectors.Vector3[3], TmpVectors.Vector3[4]];\r\n        const blLines = CreateLines(\"bllines\", { updatable: true, points: this._blLinesPoints }, this._scene);\r\n        blLines.parent = this._rootNode;\r\n        blLines.alwaysSelectAsActiveMesh = true;\r\n\r\n        this._lightHelperFrustumMeshes.push(nearLines, farLines, trLines, brLines, tlLines, blLines);\r\n\r\n        const makePlane = (name: string, color: Color3, positions: number[]) => {\r\n            const plane = new Mesh(name + \"plane\", this._scene);\r\n            const mat = new StandardMaterial(name + \"PlaneMat\", this._scene);\r\n\r\n            plane.material = mat;\r\n            plane.parent = this._rootNode;\r\n            plane.alwaysSelectAsActiveMesh = true;\r\n\r\n            mat.emissiveColor = color;\r\n            mat.alpha = this.transparency;\r\n            mat.backFaceCulling = false;\r\n            mat.disableLighting = true;\r\n\r\n            const indices = [0, 1, 2, 0, 2, 3];\r\n\r\n            const vertexData = new VertexData();\r\n\r\n            vertexData.positions = positions;\r\n            vertexData.indices = indices;\r\n\r\n            vertexData.applyToMesh(plane, true);\r\n\r\n            this._lightHelperFrustumMeshes.push(plane);\r\n        };\r\n\r\n        this._nearPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._farPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._rightPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._leftPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._topPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n        this._bottomPlaneVertices = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\r\n\r\n        makePlane(\"near\", new Color3(1, 0, 0), this._nearPlaneVertices);\r\n        makePlane(\"far\", new Color3(0.3, 0, 0), this._farPlaneVertices);\r\n        makePlane(\"right\", new Color3(0, 1, 0), this._rightPlaneVertices);\r\n        makePlane(\"left\", new Color3(0, 0.3, 0), this._leftPlaneVertices);\r\n        makePlane(\"top\", new Color3(0, 0, 1), this._topPlaneVertices);\r\n        makePlane(\"bottom\", new Color3(0, 0, 0.3), this._bottomPlaneVertices);\r\n\r\n        this._nearLinesPoints[0] = TmpVectors.Vector3[2];\r\n        this._nearLinesPoints[1] = TmpVectors.Vector3[3];\r\n        this._nearLinesPoints[2] = TmpVectors.Vector3[4];\r\n        this._nearLinesPoints[3] = TmpVectors.Vector3[5];\r\n        this._nearLinesPoints[4] = TmpVectors.Vector3[2];\r\n\r\n        this._farLinesPoints[0] = TmpVectors.Vector3[6];\r\n        this._farLinesPoints[1] = TmpVectors.Vector3[7];\r\n        this._farLinesPoints[2] = TmpVectors.Vector3[8];\r\n        this._farLinesPoints[3] = TmpVectors.Vector3[9];\r\n        this._farLinesPoints[4] = TmpVectors.Vector3[6];\r\n\r\n        this._trLinesPoints[0] = TmpVectors.Vector3[2];\r\n        this._trLinesPoints[1] = TmpVectors.Vector3[6];\r\n\r\n        this._brLinesPoints[0] = TmpVectors.Vector3[3];\r\n        this._brLinesPoints[1] = TmpVectors.Vector3[7];\r\n\r\n        this._tlLinesPoints[0] = TmpVectors.Vector3[4];\r\n        this._tlLinesPoints[1] = TmpVectors.Vector3[8];\r\n\r\n        this._blLinesPoints[0] = TmpVectors.Vector3[5];\r\n        this._blLinesPoints[1] = TmpVectors.Vector3[9];\r\n    }\r\n\r\n    protected _getInvertViewMatrix(): Matrix {\r\n        Matrix.LookAtLHToRef(this._light.position, this._light.position.add(this._light.direction), Vector3.UpReadOnly, this._inverseViewMatrix);\r\n        this._inverseViewMatrix.invertToRef(this._inverseViewMatrix);\r\n        return this._inverseViewMatrix;\r\n    }\r\n}\r\n"]}