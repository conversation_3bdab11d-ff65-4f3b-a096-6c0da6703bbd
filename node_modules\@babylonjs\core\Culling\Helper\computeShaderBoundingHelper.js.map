{"version": 3, "file": "computeShaderBoundingHelper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Culling/Helper/computeShaderBoundingHelper.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,aAAa,EAAE,uCAAmC;AAC3D,OAAO,EAAE,aAAa,EAAE,uCAAmC;AAI3D,OAAO,EAAE,YAAY,EAAE,gCAA4B;AACnD,OAAO,EAAE,OAAO,EAAE,mCAA+B;AACjD,OAAO,EAAE,aAAa,EAAE,yCAAqC;AAI7D,OAAO,wCAAwC,CAAC;AAChD,OAAO,EAAE,kBAAkB,EAAE,kCAA8B;AAE3D,gBAAgB;AAChB,MAAM,OAAO,2BAA2B;IAiBpC;;;OAGG;IACH,YAAY,MAAsB;QAnB1B,yBAAoB,GAAqC,EAAE,CAAC;QAC5D,qBAAgB,GAAqC,EAAE,CAAC;QACxD,kBAAa,GAAqC,EAAE,CAAC;QACrD,mBAAc,GAAqC,EAAE,CAAC;QACtD,uBAAkB,GAAqC,EAAE,CAAC;QAC1D,wBAAmB,GAAqC,EAAE,CAAC;QAC3D,iCAA4B,GAAqC,EAAE,CAAC;QACpE,oCAA+B,GAAqC,EAAE,CAAC;QACvE,UAAK,GAAoB,EAAE,CAAC;QAC5B,cAAS,GAAW,CAAC,CAAC;QACtB,qBAAgB,GAAmB,EAAE,CAAC;QACtC,oBAAe,GAAsB,EAAE,CAAC;QACxC,0BAAqB,GAAuB,IAAI,GAAG,EAAE,CAAC;QACtD,mBAAc,GAAoB,EAAE,CAAC;QAOzC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAEO,iBAAiB,CAAC,OAAiB,EAAE,QAAiB,EAAE,SAAkB;QAC9E,IAAI,aAA4B,CAAC;QACjC,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,MAAM,eAAe,GAA0B;gBAC3C,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;gBACxC,YAAY,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;gBACtC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;aACrC,CAAC;YAEF,IAAI,QAAQ,EAAE,CAAC;gBACX,eAAe,CAAC,WAAW,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;gBACvD,eAAe,CAAC,WAAW,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;gBACvD,eAAe,CAAC,YAAY,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;gBACxD,eAAe,CAAC,gBAAgB,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;gBAC5D,eAAe,CAAC,iBAAiB,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;YACjE,CAAC;YACD,IAAI,SAAS,EAAE,CAAC;gBACZ,eAAe,CAAC,YAAY,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;gBACxD,eAAe,CAAC,qBAAqB,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;gBACjE,eAAe,CAAC,yBAAyB,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;YAC1E,CAAC;YAED,aAAa,GAAG,IAAI,aAAa,CAAC,sBAAsB,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE;gBAC3I,eAAe;gBACf,OAAO,EAAE,OAAO;aACnB,CAAC,CAAC;YACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;QACpD,CAAC;aAAM,CAAC;YACJ,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,OAAO;QACX,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,GAAG,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjD,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;YACtC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IACxC,CAAC;IAEO,mBAAmB,CAAC,aAA4B,EAAE,IAAU,EAAE,IAAY,EAAE,MAAc,EAAE,IAAY,EAAE,WAA6C;QAC3J,IAAI,MAAqB,CAAC;QAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;YACxE,MAAM,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAuB,EAAE,YAAY,CAAC,iBAAiB,GAAG,WAAW,GAAG,MAAM,CAAC,CAAC;YAChH,MAAM,CAAC,MAAM,CAAC,SAAU,CAAC,CAAC;YAE1B,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;QACxC,CAAC;aAAM,CAAC;YACJ,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAED,aAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAEO,eAAe,CAAC,aAA4B,EAAE,IAAY,EAAE,EAAU,EAAE,WAA6C,EAAE,cAAsB,EAAE,IAAkB;QACrK,IAAI,MAAqB,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;YACnB,MAAM,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAuB,EAAE,YAAY,CAAC,iBAAiB,GAAG,cAAc,CAAC,CAAC;YAE1G,WAAW,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QAC7B,CAAC;aAAM,CAAC;YACJ,MAAM,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEpB,aAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,YAAY,CAAC,MAAqC;QAC3D,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAC;IAC9C,CAAC;IAED,gBAAgB;IAChB,2FAA2F;IACpF,qBAAqB,CAAC,MAAqC;QAC9D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE5C,IAAI,WAAW,KAAK,CAAC,IAAI,CAAE,IAAa,CAAC,eAAe,IAAI,CAAE,IAAa,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;gBACrH,SAAS;YACb,CAAC;YAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEjC,MAAM,OAAO,GAAU,IAAK,CAAC,kBAAkB,CAAC;YAChD,IAAI,OAAO,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACvC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;YACxE,CAAC;QACL,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;YAEnB,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1E,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxE,QAAQ,GAAG,IAAI,CAAC;YACpB,CAAC;YAED,MAAM,yBAAyB,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEnF,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;YAE1D,MAAM,OAAO,GAAU,IAAK,CAAC,kBAAkB,CAAC;YAChD,IAAI,OAAO,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACvC,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,gCAAgC,GAAG,iBAAiB,CAAC,CAAC;gBAEnE,MAAM,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAE/E,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;gBACvD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,sBAAsB,CAAC,CAAC,CAAC;YACnF,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,yBAAyB,CAAC,CAAC,CAAC;YACtF,CAAC;YAED,uEAAuE;YACvE,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3B,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YAEjC,GAAG,CAAC,MAAM,EAAE,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,kBAAkB,CAAC,GAAG,EAAE;gBACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC;gBACnD,KAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;oBACvE,MAAM,aAAa,GAAG,GAAG,CAAC,KAAK,CAAC;oBAChC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;wBAC3B,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC,EAAE,OAAO,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAEnB,MAAM,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QACxD,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,cAAc,CAAC,CAAC;QAEpD,MAAM,YAAY,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,OAAuB,EAAE,YAAY,CAAC,iBAAiB,GAAG,cAAc,CAAC,CAAC;QACtH,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YAEjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;YACjD,UAAU,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC;QACrD,CAAC;QAED,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE5C,MAAM,CAAC,yBAAyB,EAAE,sBAAsB,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAEpF,MAAM,OAAO,GAAU,IAAK,CAAC,kBAAkB,CAAC;YAChD,MAAM,SAAS,GAAG,OAAO,IAAI,OAAO,CAAC,cAAc,GAAG,CAAC,IAAI,OAAO,CAAC,iBAAiB,CAAC;YACrF,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,yBAAyB,CAAC;YAErF,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,EAAE,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE7H,QAAQ;YACR,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,6BAA6B,EAAE,CAAC;gBACzH,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,EAAE,YAAY,CAAC,mBAAmB,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC9H,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,EAAE,YAAY,CAAC,mBAAmB,EAAE,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAChI,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;gBAClE,aAAa,CAAC,UAAU,CAAC,aAAa,EAAE,WAAY,EAAE,KAAK,CAAC,CAAC;gBAC7D,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;oBAC9B,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,EAAE,YAAY,CAAC,wBAAwB,EAAE,CAAC,EAAE,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAC7I,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAY,EAAE,YAAY,CAAC,wBAAwB,EAAE,CAAC,EAAE,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACnJ,CAAC;YACL,CAAC;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAE3B,SAAS;YACT,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB,CAAC;gBACjD,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,YAAa,EAAE,KAAK,CAAC,CAAC;gBAE/D,IAAI,CAAC,eAAe,CAAC,aAAa,EAAE,uBAAuB,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,4BAA4B,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC3J,IAAI,CAAC,eAAe,CAChB,aAAa,EACb,2BAA2B,EAC3B,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,+BAA+B,EACpC,OAAO,CAAC,cAAc,EACtB,OAAO,CAAC,0BAA0B,CACrC,CAAC;gBAEF,GAAG,CAAC,YAAY,CAAC,wBAAwB,EAAE,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;gBACxH,GAAG,CAAC,WAAW,CAAC,kBAAkB,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;gBAC5D,GAAG,CAAC,MAAM,EAAE,CAAC;YACjB,CAAC;YAED,aAAa,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAE7D,aAAa,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;YAEhD,WAAW;YACX,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC;YAErD,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACpC,CAAC;IACL,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,4BAA4B;QACrC,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,MAAM,OAAO,GAAiB,EAAE,CAAC;YACjC,IAAI,IAAI,GAAG,CAAC,CAAC;YACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;gBAClD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrB,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC;YAC5B,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,GAAG,YAAY,CAAC,iBAAiB,CAAC,CAAC;YAE3E,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YAE/B,MAAM,MAAM,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;YAEpC,mFAAmF;YAClF,IAAI,CAAC,OAAwB,CAAC,8BAA8B,CAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC7G,IAAI,gBAAgB,GAAG,CAAC,CAAC;gBACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBACpD,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;wBAEtC,OAAO,CAAC,cAAc,CAAC,UAAU,EAAE,gBAAgB,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;wBACtE,OAAO,CAAC,cAAc,CAAC,UAAU,EAAE,gBAAgB,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;wBAE1E,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;4BACR,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC;4BACxD,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,OAAO,CAAC,CAAC;wBAC5D,CAAC;wBAED,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;oBAC5C,CAAC;oBAED,gBAAgB,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACzD,CAAC;gBAED,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBAC7C,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC3B,CAAC;gBAED,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;gBACzB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;gBAEnB,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,WAA6C;QAC/D,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;YAC5B,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QAC/B,CAAC;IACL,CAAC;IAEO,mBAAmB;QACvB,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAC7C,YAAY,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,EAAE,CAAC;IAC3C,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1C,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxC,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACtD,IAAI,CAAC,4BAA4B,GAAG,EAAE,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACzD,IAAI,CAAC,+BAA+B,GAAG,EAAE,CAAC;QAC1C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC3B,GAAG,CAAC,OAAO,EAAE,CAAC;QAClB,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,SAAU,CAAC;QAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;CACJ", "sourcesContent": ["import type { IBoundingInfoHelperPlatform } from \"./IBoundingInfoHelperPlatform\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport { ComputeShader } from \"core/Compute/computeShader\";\r\nimport { StorageBuffer } from \"core/Buffers/storageBuffer\";\r\nimport type { WebGPUEngine } from \"core/Engines/webgpuEngine\";\r\nimport type { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { VertexBuffer } from \"core/Buffers/buffer\";\r\nimport { Vector3 } from \"core/Maths/math.vector\";\r\nimport { UniformBuffer } from \"core/Materials/uniformBuffer\";\r\nimport type { DataBuffer } from \"core/Buffers/dataBuffer\";\r\nimport type { ComputeBindingMapping } from \"core/Engines/Extensions/engine.computeShader\";\r\n\r\nimport \"../../ShadersWGSL/boundingInfo.compute\";\r\nimport { _RetryWithInterval } from \"core/Misc/timingTools\";\r\n\r\n/** @internal */\r\nexport class ComputeShaderBoundingHelper implements IBoundingInfoHelperPlatform {\r\n    private _engine: AbstractEngine;\r\n    private _computeShadersCache: { [key: string]: ComputeShader } = {};\r\n    private _positionBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _indexBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _weightBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _indexExtraBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _weightExtraBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _morphTargetInfluenceBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _morphTargetTextureIndexBuffers: { [key: number]: StorageBuffer } = {};\r\n    private _ubos: UniformBuffer[] = [];\r\n    private _uboIndex: number = 0;\r\n    private _processedMeshes: AbstractMesh[] = [];\r\n    private _computeShaders: ComputeShader[][] = [];\r\n    private _uniqueComputeShaders: Set<ComputeShader> = new Set();\r\n    private _resultBuffers: StorageBuffer[] = [];\r\n\r\n    /**\r\n     * Creates a new ComputeShaderBoundingHelper\r\n     * @param engine defines the engine to use\r\n     */\r\n    constructor(engine: AbstractEngine) {\r\n        this._engine = engine;\r\n    }\r\n\r\n    private _getComputeShader(defines: string[], hasBones: boolean, hasMorphs: boolean) {\r\n        let computeShader: ComputeShader;\r\n        const join = defines.join(\"\\n\");\r\n\r\n        if (!this._computeShadersCache[join]) {\r\n            const bindingsMapping: ComputeBindingMapping = {\r\n                positionBuffer: { group: 0, binding: 0 },\r\n                resultBuffer: { group: 0, binding: 1 },\r\n                settings: { group: 0, binding: 7 },\r\n            };\r\n\r\n            if (hasBones) {\r\n                bindingsMapping.boneSampler = { group: 0, binding: 2 };\r\n                bindingsMapping.indexBuffer = { group: 0, binding: 3 };\r\n                bindingsMapping.weightBuffer = { group: 0, binding: 4 };\r\n                bindingsMapping.indexExtraBuffer = { group: 0, binding: 5 };\r\n                bindingsMapping.weightExtraBuffer = { group: 0, binding: 6 };\r\n            }\r\n            if (hasMorphs) {\r\n                bindingsMapping.morphTargets = { group: 0, binding: 8 };\r\n                bindingsMapping.morphTargetInfluences = { group: 0, binding: 9 };\r\n                bindingsMapping.morphTargetTextureIndices = { group: 0, binding: 10 };\r\n            }\r\n\r\n            computeShader = new ComputeShader(`boundingInfoCompute${hasBones ? \"_bones\" : \"\"}${hasMorphs ? \"_morphs\" : \"\"}`, this._engine, \"boundingInfo\", {\r\n                bindingsMapping,\r\n                defines: defines,\r\n            });\r\n            this._computeShadersCache[join] = computeShader;\r\n        } else {\r\n            computeShader = this._computeShadersCache[join];\r\n        }\r\n\r\n        return computeShader;\r\n    }\r\n\r\n    private _getUBO() {\r\n        if (this._uboIndex >= this._ubos.length) {\r\n            const ubo = new UniformBuffer(this._engine);\r\n            ubo.addFloat3(\"morphTargetTextureInfo\", 0, 0, 0);\r\n            ubo.addUniform(\"morphTargetCount\", 1);\r\n            ubo.addUniform(\"indexResult\", 1);\r\n            this._ubos.push(ubo);\r\n        }\r\n\r\n        return this._ubos[this._uboIndex++];\r\n    }\r\n\r\n    private _extractDataAndLink(computeShader: ComputeShader, mesh: Mesh, kind: string, stride: number, name: string, storageUnit: { [key: number]: StorageBuffer }) {\r\n        let buffer: StorageBuffer;\r\n        const vertexCount = mesh.getTotalVertices();\r\n        if (!storageUnit[mesh.uniqueId]) {\r\n            const dataArray = mesh.getVertexBuffer(kind)?.getFloatData(vertexCount);\r\n            buffer = new StorageBuffer(this._engine as WebGPUEngine, Float32Array.BYTES_PER_ELEMENT * vertexCount * stride);\r\n            buffer.update(dataArray!);\r\n\r\n            storageUnit[mesh.uniqueId] = buffer;\r\n        } else {\r\n            buffer = storageUnit[mesh.uniqueId];\r\n        }\r\n\r\n        computeShader.setStorageBuffer(name, buffer);\r\n    }\r\n\r\n    private _prepareStorage(computeShader: ComputeShader, name: string, id: number, storageUnit: { [key: number]: StorageBuffer }, numInfluencers: number, data: Float32Array) {\r\n        let buffer: StorageBuffer;\r\n        if (!storageUnit[id]) {\r\n            buffer = new StorageBuffer(this._engine as WebGPUEngine, Float32Array.BYTES_PER_ELEMENT * numInfluencers);\r\n\r\n            storageUnit[id] = buffer;\r\n        } else {\r\n            buffer = storageUnit[id];\r\n        }\r\n        buffer.update(data);\r\n\r\n        computeShader.setStorageBuffer(name, buffer);\r\n    }\r\n\r\n    /** @internal */\r\n    public async processAsync(meshes: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        await this.registerMeshListAsync(meshes);\r\n        this.processMeshList();\r\n        await this.fetchResultsForMeshListAsync();\r\n    }\r\n\r\n    /** @internal */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public registerMeshListAsync(meshes: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        this._disposeForMeshList();\r\n\r\n        if (!Array.isArray(meshes)) {\r\n            meshes = [meshes];\r\n        }\r\n\r\n        let maxNumInfluencers = 0;\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const mesh = meshes[i];\r\n            const vertexCount = mesh.getTotalVertices();\r\n\r\n            if (vertexCount === 0 || !(mesh as Mesh).getVertexBuffer || !(mesh as Mesh).getVertexBuffer(VertexBuffer.PositionKind)) {\r\n                continue;\r\n            }\r\n\r\n            this._processedMeshes.push(mesh);\r\n\r\n            const manager = (<Mesh>mesh).morphTargetManager;\r\n            if (manager && manager.supportsPositions) {\r\n                maxNumInfluencers = Math.max(maxNumInfluencers, manager.numTargets);\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < this._processedMeshes.length; i++) {\r\n            const mesh = this._processedMeshes[i];\r\n            let defines = [\"\"];\r\n\r\n            let hasBones = false;\r\n            if (mesh && mesh.useBones && mesh.computeBonesUsingShaders && mesh.skeleton) {\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n                hasBones = true;\r\n            }\r\n\r\n            const computeShaderWithoutMorph = this._getComputeShader(defines, hasBones, false);\r\n\r\n            this._uniqueComputeShaders.add(computeShaderWithoutMorph);\r\n\r\n            const manager = (<Mesh>mesh).morphTargetManager;\r\n            if (manager && manager.supportsPositions) {\r\n                defines = defines.slice();\r\n                defines.push(\"#define MORPHTARGETS\");\r\n                defines.push(\"#define NUM_MORPH_INFLUENCERS \" + maxNumInfluencers);\r\n\r\n                const computeShaderWithMorph = this._getComputeShader(defines, hasBones, true);\r\n\r\n                this._uniqueComputeShaders.add(computeShaderWithMorph);\r\n                this._computeShaders.push([computeShaderWithoutMorph, computeShaderWithMorph]);\r\n            } else {\r\n                this._computeShaders.push([computeShaderWithoutMorph, computeShaderWithoutMorph]);\r\n            }\r\n\r\n            // Pre-build the ubos, as they won't change if there's no morph targets\r\n            const ubo = this._getUBO();\r\n            ubo.updateUInt(\"indexResult\", i);\r\n\r\n            ubo.update();\r\n        }\r\n\r\n        return new Promise((resolve) => {\r\n            _RetryWithInterval(() => {\r\n                const iterator = this._uniqueComputeShaders.keys();\r\n                for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                    const computeShader = key.value;\r\n                    if (!computeShader.isReady()) {\r\n                        return false;\r\n                    }\r\n                }\r\n                return true;\r\n            }, resolve);\r\n        });\r\n    }\r\n\r\n    /** @internal */\r\n    public processMeshList(): void {\r\n        if (this._processedMeshes.length === 0) {\r\n            return;\r\n        }\r\n\r\n        this._uboIndex = 0;\r\n\r\n        const resultDataSize = 8 * this._processedMeshes.length;\r\n        const resultData = new Float32Array(resultDataSize);\r\n\r\n        const resultBuffer = new StorageBuffer(this._engine as WebGPUEngine, Float32Array.BYTES_PER_ELEMENT * resultDataSize);\r\n        this._resultBuffers.push(resultBuffer);\r\n\r\n        for (let i = 0; i < this._processedMeshes.length; i++) {\r\n            resultData[i * 8 + 0] = Number.POSITIVE_INFINITY;\r\n            resultData[i * 8 + 1] = Number.POSITIVE_INFINITY;\r\n            resultData[i * 8 + 2] = Number.POSITIVE_INFINITY;\r\n\r\n            resultData[i * 8 + 3] = Number.NEGATIVE_INFINITY;\r\n            resultData[i * 8 + 4] = Number.NEGATIVE_INFINITY;\r\n            resultData[i * 8 + 5] = Number.NEGATIVE_INFINITY;\r\n        }\r\n\r\n        resultBuffer.update(resultData);\r\n\r\n        for (let i = 0; i < this._processedMeshes.length; i++) {\r\n            const mesh = this._processedMeshes[i];\r\n            const vertexCount = mesh.getTotalVertices();\r\n\r\n            const [computeShaderWithoutMorph, computeShaderWithMorph] = this._computeShaders[i];\r\n\r\n            const manager = (<Mesh>mesh).morphTargetManager;\r\n            const hasMorphs = manager && manager.numInfluencers > 0 && manager.supportsPositions;\r\n            const computeShader = hasMorphs ? computeShaderWithMorph : computeShaderWithoutMorph;\r\n\r\n            this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.PositionKind, 3, \"positionBuffer\", this._positionBuffers);\r\n\r\n            // Bones\r\n            if (mesh && mesh.useBones && mesh.computeBonesUsingShaders && mesh.skeleton && mesh.skeleton.useTextureToStoreBoneMatrices) {\r\n                this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.MatricesIndicesKind, 4, \"indexBuffer\", this._indexBuffers);\r\n                this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.MatricesWeightsKind, 4, \"weightBuffer\", this._weightBuffers);\r\n                const boneSampler = mesh.skeleton.getTransformMatrixTexture(mesh);\r\n                computeShader.setTexture(\"boneSampler\", boneSampler!, false);\r\n                if (mesh.numBoneInfluencers > 4) {\r\n                    this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.MatricesIndicesExtraKind, 4, \"indexExtraBuffer\", this._indexExtraBuffers);\r\n                    this._extractDataAndLink(computeShader, mesh as Mesh, VertexBuffer.MatricesWeightsExtraKind, 4, \"weightExtraBuffer\", this._weightExtraBuffers);\r\n                }\r\n            }\r\n\r\n            const ubo = this._getUBO();\r\n\r\n            // Morphs\r\n            if (hasMorphs) {\r\n                const morphTargets = manager._targetStoreTexture;\r\n                computeShader.setTexture(\"morphTargets\", morphTargets!, false);\r\n\r\n                this._prepareStorage(computeShader, \"morphTargetInfluences\", mesh.uniqueId, this._morphTargetInfluenceBuffers, manager.numInfluencers, manager.influences);\r\n                this._prepareStorage(\r\n                    computeShader,\r\n                    \"morphTargetTextureIndices\",\r\n                    mesh.uniqueId,\r\n                    this._morphTargetTextureIndexBuffers,\r\n                    manager.numInfluencers,\r\n                    manager._morphTargetTextureIndices\r\n                );\r\n\r\n                ubo.updateFloat3(\"morphTargetTextureInfo\", manager._textureVertexStride, manager._textureWidth, manager._textureHeight);\r\n                ubo.updateFloat(\"morphTargetCount\", manager.numInfluencers);\r\n                ubo.update();\r\n            }\r\n\r\n            computeShader.setStorageBuffer(\"resultBuffer\", resultBuffer);\r\n\r\n            computeShader.setUniformBuffer(\"settings\", ubo);\r\n\r\n            // Dispatch\r\n            computeShader.dispatch(Math.ceil(vertexCount / 256));\r\n\r\n            this._engine.flushFramebuffer();\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public async fetchResultsForMeshListAsync(): Promise<void> {\r\n        return await new Promise((resolve) => {\r\n            const buffers: DataBuffer[] = [];\r\n            let size = 0;\r\n            for (let i = 0; i < this._resultBuffers.length; i++) {\r\n                const buffer = this._resultBuffers[i].getBuffer();\r\n                buffers.push(buffer);\r\n                size += buffer.capacity;\r\n            }\r\n\r\n            const resultData = new Float32Array(size / Float32Array.BYTES_PER_ELEMENT);\r\n\r\n            const minimum = Vector3.Zero();\r\n            const maximum = Vector3.Zero();\r\n\r\n            const minmax = { minimum, maximum };\r\n\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n            (this._engine as WebGPUEngine).readFromMultipleStorageBuffers(buffers, 0, undefined, resultData, true).then(() => {\r\n                let resultDataOffset = 0;\r\n                for (let j = 0; j < this._resultBuffers.length; j++) {\r\n                    for (let i = 0; i < this._processedMeshes.length; i++) {\r\n                        const mesh = this._processedMeshes[i];\r\n\r\n                        Vector3.FromArrayToRef(resultData, resultDataOffset + i * 8, minimum);\r\n                        Vector3.FromArrayToRef(resultData, resultDataOffset + i * 8 + 3, maximum);\r\n\r\n                        if (j > 0) {\r\n                            minimum.minimizeInPlace(mesh.getBoundingInfo().minimum);\r\n                            maximum.maximizeInPlace(mesh.getBoundingInfo().maximum);\r\n                        }\r\n\r\n                        mesh._refreshBoundingInfoDirect(minmax);\r\n                    }\r\n\r\n                    resultDataOffset += 8 * this._processedMeshes.length;\r\n                }\r\n\r\n                for (const resultBuffer of this._resultBuffers) {\r\n                    resultBuffer.dispose();\r\n                }\r\n\r\n                this._resultBuffers = [];\r\n                this._uboIndex = 0;\r\n\r\n                resolve();\r\n            });\r\n        });\r\n    }\r\n\r\n    private _disposeCache(storageUnit: { [key: number]: StorageBuffer }) {\r\n        for (const key in storageUnit) {\r\n            storageUnit[key].dispose();\r\n        }\r\n    }\r\n\r\n    private _disposeForMeshList() {\r\n        for (const resultBuffer of this._resultBuffers) {\r\n            resultBuffer.dispose();\r\n        }\r\n        this._resultBuffers = [];\r\n        this._processedMeshes = [];\r\n        this._computeShaders = [];\r\n        this._uniqueComputeShaders = new Set();\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose(): void {\r\n        this._disposeCache(this._positionBuffers);\r\n        this._positionBuffers = {};\r\n        this._disposeCache(this._indexBuffers);\r\n        this._indexBuffers = {};\r\n        this._disposeCache(this._weightBuffers);\r\n        this._weightBuffers = {};\r\n        this._disposeCache(this._morphTargetInfluenceBuffers);\r\n        this._morphTargetInfluenceBuffers = {};\r\n        this._disposeCache(this._morphTargetTextureIndexBuffers);\r\n        this._morphTargetTextureIndexBuffers = {};\r\n        for (const ubo of this._ubos) {\r\n            ubo.dispose();\r\n        }\r\n        this._ubos = [];\r\n        this._computeShadersCache = {};\r\n        this._engine = undefined!;\r\n        this._disposeForMeshList();\r\n    }\r\n}\r\n"]}