{"version": 3, "file": "engine.alpha.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.alpha.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAgBzC,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,IAAY,EAAE,qBAA8B,KAAK,EAAE,cAAsB,CAAC;IACpH,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC;QACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,mKAAmK;YACnK,MAAM,SAAS,GAAG,IAAI,KAAK,SAAS,CAAC,aAAa,CAAC;YACnD,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACjD,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC;YACjD,CAAC;QACL,CAAC;QACD,OAAO;IACX,CAAC;IAED,MAAM,kBAAkB,GAAG,IAAI,KAAK,SAAS,CAAC,aAAa,CAAC;IAE5D,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;IACjE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAEjD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtB,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,kBAAkB,CAAC;IAC1D,CAAC;IACD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;AACxC,CAAC,CAAC", "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { Constants } from \"../constants\";\r\n\r\ndeclare module \"../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Sets the current alpha mode\r\n         * @param mode defines the mode to use (one of the Engine.ALPHA_XXX)\r\n         * @param noDepthWriteChange defines if depth writing state should remains unchanged (false by default)\r\n         * @param targetIndex defines the index of the target to set the alpha mode for (default is 0)\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/transparent_rendering\r\n         */\r\n        setAlphaMode(mode: number, noDepthWriteChange?: boolean, targetIndex?: number): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.setAlphaMode = function (mode: number, noDepthWriteChange: boolean = false, targetIndex: number = 0): void {\r\n    if (this._alphaMode[targetIndex] === mode) {\r\n        if (!noDepthWriteChange) {\r\n            // Make sure we still have the correct depth mask according to the alpha mode (a transparent material could have forced writting to the depth buffer, for instance)\r\n            const depthMask = mode === Constants.ALPHA_DISABLE;\r\n            if (this.depthCullingState.depthMask !== depthMask) {\r\n                this.depthCullingState.depthMask = depthMask;\r\n            }\r\n        }\r\n        return;\r\n    }\r\n\r\n    const alphaBlendDisabled = mode === Constants.ALPHA_DISABLE;\r\n\r\n    this._alphaState.setAlphaBlend(!alphaBlendDisabled, targetIndex);\r\n    this._alphaState.setAlphaMode(mode, targetIndex);\r\n\r\n    if (!noDepthWriteChange) {\r\n        this.depthCullingState.depthMask = alphaBlendDisabled;\r\n    }\r\n    this._alphaMode[targetIndex] = mode;\r\n};\r\n"]}