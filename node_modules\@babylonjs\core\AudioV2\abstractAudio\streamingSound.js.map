{"version": 3, "file": "streamingSound.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/abstractAudio/streamingSound.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AA4BhD;;;;;;;;;;;;GAYG;AACH,MAAM,OAAgB,cAAe,SAAQ,aAAa;IAKtD,YAAsB,IAAY,EAAE,MAAqB;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QALhB,wBAAmB,GAAG,IAAI,KAAK,EAA2B,CAAC;IAMnE,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACH,2FAA2F;IACpF,oBAAoB;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAExC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAErC,OAAO,QAAQ,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,qBAAqB,CAAC,KAAa;QAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,mEAAmE;YACnE,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,CAAC,MAAM,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACzG,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,UAA+C,EAAE;QACzD,IAAI,IAAI,CAAC,KAAK,8BAAsB,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,IAAI,QAAiC,CAAC;QAEtC,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,EAAE,CAAC;YACjC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YACvC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACxC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACJ,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACtC,CAAC;QAED,MAAM,sBAAsB,GAAG,GAAG,EAAE;YAChC,IAAI,QAAQ,CAAC,KAAK,+BAAuB,EAAE,CAAC;gBACxC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,QAAQ,CAAC,wBAAwB,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC,CAAC;QACF,QAAQ,CAAC,wBAAwB,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAE9D,OAAO,CAAC,WAAW,KAAnB,OAAO,CAAC,WAAW,GAAK,IAAI,CAAC,WAAW,EAAC;QACzC,OAAO,CAAC,IAAI,KAAZ,OAAO,CAAC,IAAI,GAAK,IAAI,CAAC,IAAI,EAAC;QAC3B,OAAO,CAAC,MAAM,KAAd,OAAO,CAAC,MAAM,GAAK,CAAC,EAAC;QAErB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3B,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,CAAC,SAAS,4BAAoB,CAAC;QAEnC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACjD,QAAQ,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC;IACL,CAAC;IAIO,qBAAqB,CAAC,QAAiC;QAC3D,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAEO,wBAAwB,CAAC,QAAiC;QAC9D,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["import { SoundState } from \"../soundState\";\nimport type { IAbstractSoundOptions, IAbstractSoundPlayOptions, IAbstractSoundStoredOptions } from \"./abstractSound\";\nimport { AbstractSound } from \"./abstractSound\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { _StreamingSoundInstance } from \"./streamingSoundInstance\";\n\n/** @internal */\nexport interface IStreamingSoundOptionsBase {\n    /**\n     * The number of instances to preload. Defaults to 1.\n     * */\n    preloadCount: number;\n}\n\n/**\n * Options for creating a streaming sound.\n */\nexport interface IStreamingSoundOptions extends IAbstractSoundOptions, IStreamingSoundOptionsBase {}\n\n/**\n * Options for playing a streaming sound.\n */\nexport interface IStreamingSoundPlayOptions extends IAbstractSoundPlayOptions {}\n\n/**\n * Options stored in a streaming sound.\n * @internal\n */\nexport interface IStreamingSoundStoredOptions extends IAbstractSoundStoredOptions, IStreamingSoundOptionsBase {}\n\n/**\n * Abstract class representing a streaming sound.\n *\n * A streaming sound has a sound buffer that is loaded into memory in chunks as it is played. This allows it to be played\n * more quickly than a static sound, but it also means that it cannot have loop points or playback rate changes.\n *\n * Due to the way streaming sounds are typically implemented, there can be a significant delay when attempting to play\n * a streaming sound for the first time. To prevent this delay, it is recommended to preload instances of the sound\n * using the {@link IStreamingSoundStoredOptions.preloadCount} options, or the {@link preloadInstanceAsync} and\n * {@link preloadInstancesAsync} methods before calling the `play` method.\n *\n * Streaming sounds are created by the {@link CreateStreamingSoundAsync} function.\n */\nexport abstract class StreamingSound extends AbstractSound {\n    private _preloadedInstances = new Array<_StreamingSoundInstance>();\n\n    protected abstract override readonly _options: IStreamingSoundStoredOptions;\n\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine);\n    }\n\n    /**\n     * The number of instances to preload. Defaults to `1`.\n     */\n    public get preloadCount(): number {\n        return this._options.preloadCount ?? 1;\n    }\n\n    /**\n     * Returns the number of instances that have been preloaded.\n     */\n    public get preloadCompletedCount(): number {\n        return this._preloadedInstances.length;\n    }\n\n    /**\n     * Preloads an instance of the sound.\n     * @returns A promise that resolves when the instance is preloaded.\n     */\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\n    public preloadInstanceAsync(): Promise<void> {\n        const instance = this._createInstance();\n\n        this._addPreloadedInstance(instance);\n\n        return instance.preloadedPromise;\n    }\n\n    /**\n     * Preloads the given number of instances of the sound.\n     * @param count - The number of instances to preload.\n     * @returns A promise that resolves when all instances are preloaded.\n     */\n    public async preloadInstancesAsync(count: number): Promise<void> {\n        for (let i = 0; i < count; i++) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.preloadInstanceAsync();\n        }\n\n        await Promise.all(this._preloadedInstances.map(async (instance) => await instance.preloadedPromise));\n    }\n\n    /**\n     * Plays the sound.\n     * - Triggers `onEndedObservable` if played for the full duration and the `loop` option is not set.\n     * @param options The options to use when playing the sound. Options set here override the sound's options.\n     */\n    public play(options: Partial<IStreamingSoundPlayOptions> = {}): void {\n        if (this.state === SoundState.Paused) {\n            this.resume();\n            return;\n        }\n\n        let instance: _StreamingSoundInstance;\n\n        if (this.preloadCompletedCount > 0) {\n            instance = this._preloadedInstances[0];\n            instance.startOffset = this.startOffset;\n            this._removePreloadedInstance(instance);\n        } else {\n            instance = this._createInstance();\n        }\n\n        const onInstanceStateChanged = () => {\n            if (instance.state === SoundState.Started) {\n                this._stopExcessInstances();\n                instance.onStateChangedObservable.removeCallback(onInstanceStateChanged);\n            }\n        };\n        instance.onStateChangedObservable.add(onInstanceStateChanged);\n\n        options.startOffset ??= this.startOffset;\n        options.loop ??= this.loop;\n        options.volume ??= 1;\n\n        this._beforePlay(instance);\n        instance.play(options);\n        this._afterPlay(instance);\n    }\n\n    /**\n     * Stops the sound.\n     */\n    public stop(): void {\n        this._setState(SoundState.Stopped);\n\n        if (!this._instances) {\n            return;\n        }\n\n        for (const instance of Array.from(this._instances)) {\n            instance.stop();\n        }\n    }\n\n    protected abstract override _createInstance(): _StreamingSoundInstance;\n\n    private _addPreloadedInstance(instance: _StreamingSoundInstance): void {\n        if (!this._preloadedInstances.includes(instance)) {\n            this._preloadedInstances.push(instance);\n        }\n    }\n\n    private _removePreloadedInstance(instance: _StreamingSoundInstance): void {\n        const index = this._preloadedInstances.indexOf(instance);\n        if (index !== -1) {\n            this._preloadedInstances.splice(index, 1);\n        }\n    }\n}\n"]}