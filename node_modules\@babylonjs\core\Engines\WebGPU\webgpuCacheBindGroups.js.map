{"version": 3, "file": "webgpuCacheBindGroups.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuCacheBindGroups.ts"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,wCAAwC;AACxC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAU3C;;GAEG;AACH,MAAM,aAAa,GAAG,CAAC,IAAI,EAAE,CAAC;AAE9B;;;;;GAKG;AACH,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,CAAC;AAE/B,MAAM,wBAAwB;IAI1B;QACI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACrB,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,qBAAqB;IAkBvB,MAAM,KAAK,UAAU;QACxB,OAAO;YACH,YAAY,EAAE,qBAAqB,CAAC,yBAAyB;YAC7D,gBAAgB,EAAE,qBAAqB,CAAC,6BAA6B;YACrE,eAAe,EAAE,qBAAqB,CAAC,4BAA4B;YACnE,iBAAiB,EAAE,qBAAqB,CAAC,8BAA8B;SAC1E,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,UAAU;QACpB,qBAAqB,CAAC,MAAM,GAAG,IAAI,wBAAwB,EAAE,CAAC;QAC9D,qBAAqB,CAAC,yBAAyB,GAAG,CAAC,CAAC;QACpD,qBAAqB,CAAC,6BAA6B,GAAG,CAAC,CAAC;QACxD,qBAAqB,CAAC,4BAA4B,GAAG,CAAC,CAAC;QACvD,qBAAqB,CAAC,8BAA8B,GAAG,CAAC,CAAC;QACzD,qBAAqB,CAAC,iCAAiC,GAAG,CAAC,CAAC;QAC5D,qBAAqB,CAAC,gCAAgC,GAAG,CAAC,CAAC;QAC3D,qBAAqB,CAAC,kCAAkC,GAAG,CAAC,CAAC;IACjE,CAAC;IAED,YAAY,MAAiB,EAAE,YAAgC,EAAE,MAAoB;QAtB9E,aAAQ,GAAG,KAAK,CAAC;QAuBpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAEM,QAAQ;QACX,qBAAqB,CAAC,6BAA6B,GAAG,qBAAqB,CAAC,iCAAiC,CAAC;QAC9G,qBAAqB,CAAC,4BAA4B,GAAG,qBAAqB,CAAC,gCAAgC,CAAC;QAC5G,qBAAqB,CAAC,8BAA8B,GAAG,qBAAqB,CAAC,kCAAkC,CAAC;QAChH,qBAAqB,CAAC,iCAAiC,GAAG,CAAC,CAAC;QAC5D,qBAAqB,CAAC,gCAAgC,GAAG,CAAC,CAAC;QAC3D,qBAAqB,CAAC,kCAAkC,GAAG,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;;;OAQG;IACI,aAAa,CAAC,qBAA4C,EAAE,WAA8B,EAAE,eAAsC;QACrI,IAAI,UAAU,GAA+B,SAAS,CAAC;QACvD,IAAI,IAAI,GAAG,qBAAqB,CAAC,MAAM,CAAC;QAExC,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,IAAI,eAAe,CAAC,sBAAsB,CAAC;QAChF,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC7E,qBAAqB,CAAC,kCAAkC,EAAE,CAAC;gBAC3D,OAAO,WAAW,CAAC,UAAW,CAAC;YACnC,CAAC;YAED,KAAK,MAAM,UAAU,IAAI,qBAAqB,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;gBACjF,MAAM,KAAK,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,IAAI,CAAC,CAAC,GAAG,aAAa,CAAC;gBAC/E,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAClC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,QAAQ,GAAG,IAAI,wBAAwB,EAAE,CAAC;oBAC1C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;gBAClC,CAAC;gBACD,IAAI,GAAG,QAAQ,CAAC;YACpB,CAAC;YAED,KAAK,MAAM,WAAW,IAAI,qBAAqB,CAAC,uBAAuB,CAAC,YAAY,EAAE,CAAC;gBACnF,MAAM,eAAe,GAAG,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,QAAQ,IAAI,CAAC,CAAC;gBAC7E,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;gBAC5C,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,QAAQ,GAAG,IAAI,wBAAwB,EAAE,CAAC;oBAC1C,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,QAAQ,CAAC;gBAC5C,CAAC;gBACD,IAAI,GAAG,QAAQ,CAAC;YACpB,CAAC;YAED,KAAK,MAAM,WAAW,IAAI,qBAAqB,CAAC,uBAAuB,CAAC,YAAY,EAAE,CAAC;gBACnF,MAAM,SAAS,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAC,GAAG,cAAc,CAAC;gBACnG,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBACtC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,QAAQ,GAAG,IAAI,wBAAwB,EAAE,CAAC;oBAC1C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC;gBACtC,CAAC;gBACD,IAAI,GAAG,QAAQ,CAAC;YACpB,CAAC;YAED,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,CAAC;QAED,WAAW,CAAC,YAAY,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACnD,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;QAEhC,IAAI,UAAU,EAAE,CAAC;YACb,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC;YACpC,qBAAqB,CAAC,gCAAgC,EAAE,CAAC;YACzD,OAAO,UAAU,CAAC;QACtB,CAAC;QAED,UAAU,GAAG,EAAE,CAAC;QAChB,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC;QAEpC,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QACjC,CAAC;QAED,qBAAqB,CAAC,yBAAyB,EAAE,CAAC;QAClD,qBAAqB,CAAC,iCAAiC,EAAE,CAAC;QAE1D,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAC9F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACnG,MAAM,aAAa,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;YAE9F,MAAM,OAAO,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YAClF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5C,MAAM,KAAK,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzF,MAAM,SAAS,GAAG,qBAAqB,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC3G,MAAM,IAAI,GAAG,SAAS,CAAC,oBAAoB,IAAI,SAAS,CAAC,IAAI,CAAC;gBAE9D,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;oBAChB,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACnD,IAAI,WAAW,EAAE,CAAC;wBACd,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;wBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;4BACX,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;gCAC/B,MAAM,CAAC,KAAK,CACR,wCAAwC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,IAAI,iBAAiB,IAAI,CAAC,SAAS,CACtG,WAAW,EACX,CAAC,GAAW,EAAE,KAAU,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CACzE,8BAA8B,eAAe,CAAC,QAAQ,EAAE,EACzD,EAAE,CACL,CAAC;4BACN,CAAC;4BACD,SAAS;wBACb,CAAC;wBACD,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC7G,CAAC;yBAAM,CAAC;wBACJ,MAAM,CAAC,KAAK,CACR,YAAY,IAAI,sEAAsE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,SAAS,CAC1I,eAAe,EACf,CAAC,GAAW,EAAE,KAAU,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAC9F,EAAE,EACH,EAAE,CACL,CAAC;oBACN,CAAC;gBACL,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;oBAC/C,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACnD,IAAI,WAAW,EAAE,CAAC;wBACd,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,WAAW,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;4BAC/D,MAAM,CAAC,KAAK,CACR,wCAAwC,IAAI,YAAY,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAC,SAAS,CACxG,WAAW,EACX,CAAC,GAAW,EAAE,KAAU,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CACzE,8BAA8B,eAAe,CAAC,QAAQ,EAAE,EACzD,EAAE,CACL,CAAC;4BACF,SAAS;wBACb,CAAC;wBACD,MAAM,eAAe,GAAI,WAAW,CAAC,OAA2B,CAAC,gBAAyC,CAAC;wBAE3G,IACI,IAAI,CAAC,OAAO,CAAC,eAAe;4BAC5B,CAAC,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAC7H,CAAC;4BACC,MAAM,CAAC,KAAK,CACR,oDAAoD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,IAAI,iBAAiB,IAAI,CAAC,SAAS,CAClH,WAAW,EACX,CAAC,GAAW,EAAE,KAAU,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CACzE,aAAa,WAAW,CAAC,OAAO,EAAE,OAAO,8BAA8B,eAAe,CAAC,QAAQ,EAAE,EAClG,EAAE,CACL,CAAC;4BACF,SAAS;wBACb,CAAC;wBAED,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,cAAe,CAAC,CAAC,CAAC,eAAe,CAAC,IAAK,CAAC;oBACzG,CAAC;yBAAM,CAAC;wBACJ,MAAM,CAAC,KAAK,CACR,YAAY,IAAI,kGAAkG,IAAI,uBAAuB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,SAAS,CACjM,eAAe,EACf,CAAC,GAAW,EAAE,KAAU,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAC9F,EAAE,EACH,EAAE,CACL,CAAC;oBACN,CAAC;gBACL,CAAC;qBAAM,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;oBAC/B,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACnD,IAAI,WAAW,EAAE,CAAC;wBACd,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,WAAW,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;4BAC/D,MAAM,CAAC,KAAK,CACR,iDAAiD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,IAAI,iBAAiB,IAAI,CAAC,SAAS,CAC/G,WAAW,EACX,CAAC,GAAW,EAAE,KAAU,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CACzE,8BAA8B,eAAe,CAAC,QAAQ,EAAE,EACzD,EAAE,CACL,CAAC;4BACF,SAAS;wBACb,CAAC;wBACD,MAAM,eAAe,GAAI,WAAW,CAAC,OAA2B,CAAC,kBAAkB,CAAC;wBAEpF,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,CAAC,eAAe,EAAE,CAAC;4BACnD,MAAM,CAAC,KAAK,CACR,qDAAqD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,IAAI,iBAAiB,IAAI,CAAC,SAAS,CACnH,WAAW,EACX,CAAC,GAAW,EAAE,KAAU,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CACzE,aAAa,WAAW,CAAC,OAAO,EAAE,OAAO,8BAA8B,eAAe,CAAC,QAAQ,EAAE,EAClG,EAAE,CACL,CAAC;4BACF,SAAS;wBACb,CAAC;wBAED,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC;oBAC1F,CAAC;yBAAM,CAAC;wBACJ,MAAM,CAAC,KAAK,CACR,qBAAqB,IAAI,sEAAsE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,SAAS,CACnJ,eAAe,EACf,CAAC,GAAW,EAAE,KAAU,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAC9F,EAAE,EACH,EAAE,CACL,CAAC;oBACN,CAAC;gBACL,CAAC;qBAAM,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;oBACtB,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAC7C,IAAI,UAAU,EAAE,CAAC;wBACb,MAAM,YAAY,GAAG,UAAU,CAAC,kBAA+B,CAAC;wBAC/D,OAAO,CAAC,CAAC,CAAC,CAAC,QAA6B,CAAC,MAAM,GAAG,YAAY,CAAC;wBAC/D,OAAO,CAAC,CAAC,CAAC,CAAC,QAA6B,CAAC,IAAI,GAAG,UAAU,CAAC,QAAQ,CAAC;oBACzE,CAAC;yBAAM,CAAC;wBACJ,MAAM,CAAC,KAAK,CACR,sBAAsB,IAAI,wDAAwD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,0BACnJ,WAAW,CAAC,QAChB,EAAE,EACF,EAAE,CACL,CAAC;oBACN,CAAC;gBACL,CAAC;YACL,CAAC;YAED,MAAM,WAAW,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACxC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;gBACzC,MAAM,EAAE,WAAW;gBACnB,OAAO;aACV,CAAC,CAAC;QACP,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;;AApQa,+CAAyB,GAAG,CAAC,AAAJ,CAAK;AAC9B,mDAA6B,GAAG,CAAC,AAAJ,CAAK;AAClC,kDAA4B,GAAG,CAAC,AAAJ,CAAK;AACjC,oDAA8B,GAAG,CAAC,AAAJ,CAAK;AAElC,4BAAM,GAA6B,IAAI,wBAAwB,EAAE,AAA3D,CAA4D;AAElE,uDAAiC,GAAG,CAAC,AAAJ,CAAK;AACtC,sDAAgC,GAAG,CAAC,AAAJ,CAAK;AACrC,wDAAkC,GAAG,CAAC,AAAJ,CAAK", "sourcesContent": ["/* eslint-disable baby<PERSON><PERSON>s/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { WebGPUCacheSampler } from \"./webgpuCacheSampler\";\r\nimport type { WebGPUMaterialContext } from \"./webgpuMaterialContext\";\r\nimport type { WebGPUPipelineContext } from \"./webgpuPipelineContext\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\nimport type { WebGPUHardwareTexture } from \"./webgpuHardwareTexture\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { ExternalTexture } from \"../../Materials/Textures/externalTexture\";\r\nimport type { WebGPUDrawContext } from \"./webgpuDrawContext\";\r\n\r\n/**\r\n * Sampler hash codes are 19 bits long, so using a start value of 2^20 for buffer ids will ensure we can't have any collision with the sampler hash codes\r\n */\r\nconst BufferIdStart = 1 << 20;\r\n\r\n/**\r\n * textureIdStart is added to texture ids to ensure we can't have any collision with the buffer ids / sampler hash codes.\r\n * 2^35 for textureIdStart means we can have:\r\n * - 2^(35-20) = 2^15 = 32768 possible buffer ids\r\n * - 2^(53-35) = 2^18 = 524288 possible texture ids\r\n */\r\nconst TextureIdStart = 2 ** 35;\r\n\r\nclass WebGPUBindGroupCacheNode {\r\n    public values: { [id: number]: WebGPUBindGroupCacheNode };\r\n    public bindGroups: GPUBindGroup[];\r\n\r\n    constructor() {\r\n        this.values = {};\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPUCacheBindGroups {\r\n    public static NumBindGroupsCreatedTotal = 0;\r\n    public static NumBindGroupsCreatedLastFrame = 0;\r\n    public static NumBindGroupsLookupLastFrame = 0;\r\n    public static NumBindGroupsNoLookupLastFrame = 0;\r\n\r\n    private static _Cache: WebGPUBindGroupCacheNode = new WebGPUBindGroupCacheNode();\r\n\r\n    private static _NumBindGroupsCreatedCurrentFrame = 0;\r\n    private static _NumBindGroupsLookupCurrentFrame = 0;\r\n    private static _NumBindGroupsNoLookupCurrentFrame = 0;\r\n\r\n    private _device: GPUDevice;\r\n    private _cacheSampler: WebGPUCacheSampler;\r\n    private _engine: WebGPUEngine;\r\n\r\n    public disabled = false;\r\n\r\n    public static get Statistics() {\r\n        return {\r\n            totalCreated: WebGPUCacheBindGroups.NumBindGroupsCreatedTotal,\r\n            lastFrameCreated: WebGPUCacheBindGroups.NumBindGroupsCreatedLastFrame,\r\n            lookupLastFrame: WebGPUCacheBindGroups.NumBindGroupsLookupLastFrame,\r\n            noLookupLastFrame: WebGPUCacheBindGroups.NumBindGroupsNoLookupLastFrame,\r\n        };\r\n    }\r\n\r\n    public static ResetCache() {\r\n        WebGPUCacheBindGroups._Cache = new WebGPUBindGroupCacheNode();\r\n        WebGPUCacheBindGroups.NumBindGroupsCreatedTotal = 0;\r\n        WebGPUCacheBindGroups.NumBindGroupsCreatedLastFrame = 0;\r\n        WebGPUCacheBindGroups.NumBindGroupsLookupLastFrame = 0;\r\n        WebGPUCacheBindGroups.NumBindGroupsNoLookupLastFrame = 0;\r\n        WebGPUCacheBindGroups._NumBindGroupsCreatedCurrentFrame = 0;\r\n        WebGPUCacheBindGroups._NumBindGroupsLookupCurrentFrame = 0;\r\n        WebGPUCacheBindGroups._NumBindGroupsNoLookupCurrentFrame = 0;\r\n    }\r\n\r\n    constructor(device: GPUDevice, cacheSampler: WebGPUCacheSampler, engine: WebGPUEngine) {\r\n        this._device = device;\r\n        this._cacheSampler = cacheSampler;\r\n        this._engine = engine;\r\n    }\r\n\r\n    public endFrame(): void {\r\n        WebGPUCacheBindGroups.NumBindGroupsCreatedLastFrame = WebGPUCacheBindGroups._NumBindGroupsCreatedCurrentFrame;\r\n        WebGPUCacheBindGroups.NumBindGroupsLookupLastFrame = WebGPUCacheBindGroups._NumBindGroupsLookupCurrentFrame;\r\n        WebGPUCacheBindGroups.NumBindGroupsNoLookupLastFrame = WebGPUCacheBindGroups._NumBindGroupsNoLookupCurrentFrame;\r\n        WebGPUCacheBindGroups._NumBindGroupsCreatedCurrentFrame = 0;\r\n        WebGPUCacheBindGroups._NumBindGroupsLookupCurrentFrame = 0;\r\n        WebGPUCacheBindGroups._NumBindGroupsNoLookupCurrentFrame = 0;\r\n    }\r\n\r\n    /**\r\n     * Cache is currently based on the uniform/storage buffers, samplers and textures used by the binding groups.\r\n     * Note that all uniform buffers have an offset of 0 in Babylon and we don't have a use case where we would have the same buffer used with different capacity values:\r\n     * that means we don't need to factor in the offset/size of the buffer in the cache, only the id\r\n     * @param webgpuPipelineContext\r\n     * @param drawContext\r\n     * @param materialContext\r\n     * @returns a bind group array\r\n     */\r\n    public getBindGroups(webgpuPipelineContext: WebGPUPipelineContext, drawContext: WebGPUDrawContext, materialContext: WebGPUMaterialContext): GPUBindGroup[] {\r\n        let bindGroups: GPUBindGroup[] | undefined = undefined;\r\n        let node = WebGPUCacheBindGroups._Cache;\r\n\r\n        const cacheIsDisabled = this.disabled || materialContext.forceBindGroupCreation;\r\n        if (!cacheIsDisabled) {\r\n            if (!drawContext.isDirty(materialContext.updateId) && !materialContext.isDirty) {\r\n                WebGPUCacheBindGroups._NumBindGroupsNoLookupCurrentFrame++;\r\n                return drawContext.bindGroups!;\r\n            }\r\n\r\n            for (const bufferName of webgpuPipelineContext.shaderProcessingContext.bufferNames) {\r\n                const uboId = (drawContext.buffers[bufferName]?.uniqueId ?? 0) + BufferIdStart;\r\n                let nextNode = node.values[uboId];\r\n                if (!nextNode) {\r\n                    nextNode = new WebGPUBindGroupCacheNode();\r\n                    node.values[uboId] = nextNode;\r\n                }\r\n                node = nextNode;\r\n            }\r\n\r\n            for (const samplerName of webgpuPipelineContext.shaderProcessingContext.samplerNames) {\r\n                const samplerHashCode = materialContext.samplers[samplerName]?.hashCode ?? 0;\r\n                let nextNode = node.values[samplerHashCode];\r\n                if (!nextNode) {\r\n                    nextNode = new WebGPUBindGroupCacheNode();\r\n                    node.values[samplerHashCode] = nextNode;\r\n                }\r\n                node = nextNode;\r\n            }\r\n\r\n            for (const textureName of webgpuPipelineContext.shaderProcessingContext.textureNames) {\r\n                const textureId = (materialContext.textures[textureName]?.texture?.uniqueId ?? 0) + TextureIdStart;\r\n                let nextNode = node.values[textureId];\r\n                if (!nextNode) {\r\n                    nextNode = new WebGPUBindGroupCacheNode();\r\n                    node.values[textureId] = nextNode;\r\n                }\r\n                node = nextNode;\r\n            }\r\n\r\n            bindGroups = node.bindGroups;\r\n        }\r\n\r\n        drawContext.resetIsDirty(materialContext.updateId);\r\n        materialContext.isDirty = false;\r\n\r\n        if (bindGroups) {\r\n            drawContext.bindGroups = bindGroups;\r\n            WebGPUCacheBindGroups._NumBindGroupsLookupCurrentFrame++;\r\n            return bindGroups;\r\n        }\r\n\r\n        bindGroups = [];\r\n        drawContext.bindGroups = bindGroups;\r\n\r\n        if (!cacheIsDisabled) {\r\n            node.bindGroups = bindGroups;\r\n        }\r\n\r\n        WebGPUCacheBindGroups.NumBindGroupsCreatedTotal++;\r\n        WebGPUCacheBindGroups._NumBindGroupsCreatedCurrentFrame++;\r\n\r\n        const bindGroupLayouts = webgpuPipelineContext.bindGroupLayouts[materialContext.textureState];\r\n        for (let i = 0; i < webgpuPipelineContext.shaderProcessingContext.bindGroupLayoutEntries.length; i++) {\r\n            const setDefinition = webgpuPipelineContext.shaderProcessingContext.bindGroupLayoutEntries[i];\r\n\r\n            const entries = webgpuPipelineContext.shaderProcessingContext.bindGroupEntries[i];\r\n            for (let j = 0; j < setDefinition.length; j++) {\r\n                const entry = webgpuPipelineContext.shaderProcessingContext.bindGroupLayoutEntries[i][j];\r\n                const entryInfo = webgpuPipelineContext.shaderProcessingContext.bindGroupLayoutEntryInfo[i][entry.binding];\r\n                const name = entryInfo.nameInArrayOfTexture ?? entryInfo.name;\r\n\r\n                if (entry.sampler) {\r\n                    const bindingInfo = materialContext.samplers[name];\r\n                    if (bindingInfo) {\r\n                        const sampler = bindingInfo.sampler;\r\n                        if (!sampler) {\r\n                            if (this._engine.dbgSanityChecks) {\r\n                                Logger.Error(\r\n                                    `Trying to bind a null sampler! entry=${JSON.stringify(entry)}, name=${name}, bindingInfo=${JSON.stringify(\r\n                                        bindingInfo,\r\n                                        (key: string, value: any) => (key === \"texture\" ? \"<no dump>\" : value)\r\n                                    )}, materialContext.uniqueId=${materialContext.uniqueId}`,\r\n                                    50\r\n                                );\r\n                            }\r\n                            continue;\r\n                        }\r\n                        entries[j].resource = this._cacheSampler.getSampler(sampler, false, bindingInfo.hashCode, sampler.label);\r\n                    } else {\r\n                        Logger.Error(\r\n                            `Sampler \"${name}\" not found in the material context. Make sure you bound it. entry=${JSON.stringify(entry)}, materialContext=${JSON.stringify(\r\n                                materialContext,\r\n                                (key: string, value: any) => (key === \"texture\" || key === \"sampler\" ? \"<no dump>\" : value)\r\n                            )}`,\r\n                            50\r\n                        );\r\n                    }\r\n                } else if (entry.texture || entry.storageTexture) {\r\n                    const bindingInfo = materialContext.textures[name];\r\n                    if (bindingInfo) {\r\n                        if (this._engine.dbgSanityChecks && bindingInfo.texture === null) {\r\n                            Logger.Error(\r\n                                `Trying to bind a null texture! name=\"${name}\", entry=${JSON.stringify(entry)}, bindingInfo=${JSON.stringify(\r\n                                    bindingInfo,\r\n                                    (key: string, value: any) => (key === \"texture\" ? \"<no dump>\" : value)\r\n                                )}, materialContext.uniqueId=${materialContext.uniqueId}`,\r\n                                50\r\n                            );\r\n                            continue;\r\n                        }\r\n                        const hardwareTexture = (bindingInfo.texture as InternalTexture)._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n                        if (\r\n                            this._engine.dbgSanityChecks &&\r\n                            (!hardwareTexture || (entry.texture && !hardwareTexture.view) || (entry.storageTexture && !hardwareTexture.viewForWriting))\r\n                        ) {\r\n                            Logger.Error(\r\n                                `Trying to bind a null gpu texture or view! entry=${JSON.stringify(entry)}, name=${name}, bindingInfo=${JSON.stringify(\r\n                                    bindingInfo,\r\n                                    (key: string, value: any) => (key === \"texture\" ? \"<no dump>\" : value)\r\n                                )}, isReady=${bindingInfo.texture?.isReady}, materialContext.uniqueId=${materialContext.uniqueId}`,\r\n                                50\r\n                            );\r\n                            continue;\r\n                        }\r\n\r\n                        entries[j].resource = entry.storageTexture ? hardwareTexture.viewForWriting! : hardwareTexture.view!;\r\n                    } else {\r\n                        Logger.Error(\r\n                            `Texture \"${name}\" not found in the material context. Make sure you bound it (something like effect.setTexture(\"${name}\", texture)). entry=${JSON.stringify(entry)}, materialContext=${JSON.stringify(\r\n                                materialContext,\r\n                                (key: string, value: any) => (key === \"texture\" || key === \"sampler\" ? \"<no dump>\" : value)\r\n                            )}`,\r\n                            50\r\n                        );\r\n                    }\r\n                } else if (entry.externalTexture) {\r\n                    const bindingInfo = materialContext.textures[name];\r\n                    if (bindingInfo) {\r\n                        if (this._engine.dbgSanityChecks && bindingInfo.texture === null) {\r\n                            Logger.Error(\r\n                                `Trying to bind a null external texture! entry=${JSON.stringify(entry)}, name=${name}, bindingInfo=${JSON.stringify(\r\n                                    bindingInfo,\r\n                                    (key: string, value: any) => (key === \"texture\" ? \"<no dump>\" : value)\r\n                                )}, materialContext.uniqueId=${materialContext.uniqueId}`,\r\n                                50\r\n                            );\r\n                            continue;\r\n                        }\r\n                        const externalTexture = (bindingInfo.texture as ExternalTexture).underlyingResource;\r\n\r\n                        if (this._engine.dbgSanityChecks && !externalTexture) {\r\n                            Logger.Error(\r\n                                `Trying to bind a null gpu external texture! entry=${JSON.stringify(entry)}, name=${name}, bindingInfo=${JSON.stringify(\r\n                                    bindingInfo,\r\n                                    (key: string, value: any) => (key === \"texture\" ? \"<no dump>\" : value)\r\n                                )}, isReady=${bindingInfo.texture?.isReady}, materialContext.uniqueId=${materialContext.uniqueId}`,\r\n                                50\r\n                            );\r\n                            continue;\r\n                        }\r\n\r\n                        entries[j].resource = this._device.importExternalTexture({ source: externalTexture });\r\n                    } else {\r\n                        Logger.Error(\r\n                            `External texture \"${name}\" not found in the material context. Make sure you bound it. entry=${JSON.stringify(entry)}, materialContext=${JSON.stringify(\r\n                                materialContext,\r\n                                (key: string, value: any) => (key === \"texture\" || key === \"sampler\" ? \"<no dump>\" : value)\r\n                            )}`,\r\n                            50\r\n                        );\r\n                    }\r\n                } else if (entry.buffer) {\r\n                    const dataBuffer = drawContext.buffers[name];\r\n                    if (dataBuffer) {\r\n                        const webgpuBuffer = dataBuffer.underlyingResource as GPUBuffer;\r\n                        (entries[j].resource as GPUBufferBinding).buffer = webgpuBuffer;\r\n                        (entries[j].resource as GPUBufferBinding).size = dataBuffer.capacity;\r\n                    } else {\r\n                        Logger.Error(\r\n                            `Can't find buffer \"${name}\" in the draw context. Make sure you bound it. entry=${JSON.stringify(entry)}, buffers=${JSON.stringify(drawContext.buffers)}, drawContext.uniqueId=${\r\n                                drawContext.uniqueId\r\n                            }`,\r\n                            50\r\n                        );\r\n                    }\r\n                }\r\n            }\r\n\r\n            const groupLayout = bindGroupLayouts[i];\r\n            bindGroups[i] = this._device.createBindGroup({\r\n                layout: groupLayout,\r\n                entries,\r\n            });\r\n        }\r\n\r\n        return bindGroups;\r\n    }\r\n}\r\n"]}