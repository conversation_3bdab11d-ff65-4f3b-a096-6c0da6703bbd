{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/node_modules/%40swc/helpers/esm/_define_property.js"], "sourcesContent": ["function _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });\n    } else obj[key] = value;\n\n    return obj;\n}\nexport { _define_property as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,iBAAiB,GAAG,EAAE,GAAG,EAAE,KAAK;IACrC,IAAI,OAAO,KAAK;QACZ,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IACzG,OAAO,GAAG,CAAC,IAAI,GAAG;IAElB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/materials/custom/customMaterial.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/materials/src/custom/customMaterial.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Texture } from \"core/Materials/Textures/texture\";\r\nimport { Effect } from \"core/Materials/effect\";\r\nimport type { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { StandardMaterial } from \"core/Materials/standardMaterial\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\nimport { Color3, Color4 } from \"core/Maths/math.color\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { SubMesh } from \"core/Meshes/subMesh\";\r\n\r\nimport \"core/Shaders/default.vertex\";\r\nimport \"core/Shaders/default.fragment\";\r\n\r\n/**\r\n * Structure of a custom shader\r\n */\r\nexport class CustomShaderStructure {\r\n    /**\r\n     * Fragment store\r\n     */\r\n    public FragmentStore: string;\r\n    /**\r\n     * Vertex store\r\n     */\r\n    public VertexStore: string;\r\n\r\n    constructor() {}\r\n}\r\n\r\n/**\r\n * Parts of a shader\r\n */\r\nexport class ShaderSpecialParts {\r\n    constructor() {}\r\n\r\n    /**\r\n     * Beginning of the fragment shader\r\n     */\r\n    public Fragment_Begin: string;\r\n    /**\r\n     * Variable definitions of the fragment shader\r\n     */\r\n    public Fragment_Definitions: string;\r\n    /**\r\n     * Beginning of the fragment main function\r\n     */\r\n    public Fragment_MainBegin: string;\r\n    /**\r\n     * End of the fragment main function\r\n     */\r\n    public Fragment_MainEnd: string;\r\n\r\n    /**\r\n     * Diffuse color calculation\r\n     */\r\n    public Fragment_Custom_Diffuse: string;\r\n    /**\r\n     * Before lightning computations\r\n     */\r\n    public Fragment_Before_Lights: string;\r\n    /**\r\n     * Before fog computations\r\n     */\r\n    public Fragment_Before_Fog: string;\r\n    /**\r\n     * Alpha calculations\r\n     */\r\n    public Fragment_Custom_Alpha: string;\r\n    /**\r\n     * Before frag color is assigned\r\n     */\r\n    public Fragment_Before_FragColor: string;\r\n    /**\r\n     * Beginning of the vertex shader\r\n     */\r\n    public Vertex_Begin: string;\r\n    /**\r\n     * Variable definitions of the vertex shader\r\n     */\r\n    public Vertex_Definitions: string;\r\n    /**\r\n     * Start of the main function of the vertex shader\r\n     */\r\n    public Vertex_MainBegin: string;\r\n\r\n    /**\r\n     * Before the world position computation\r\n     */\r\n    public Vertex_Before_PositionUpdated: string;\r\n\r\n    /**\r\n     * Before the normal computation\r\n     */\r\n    public Vertex_Before_NormalUpdated: string;\r\n\r\n    /**\r\n     * After the world position has been computed\r\n     */\r\n    public Vertex_After_WorldPosComputed: string;\r\n\r\n    /**\r\n     * Main end of the vertex shader\r\n     */\r\n    public Vertex_MainEnd: string;\r\n}\r\n\r\n/**\r\n * Customized material\r\n */\r\nexport class CustomMaterial extends StandardMaterial {\r\n    /**\r\n     * Index for each created shader\r\n     */\r\n    public static ShaderIndexer = 1;\r\n    /**\r\n     * Custom shader structure\r\n     */\r\n    public CustomParts: ShaderSpecialParts;\r\n    /**\r\n     * Name of the shader\r\n     */\r\n    public _createdShaderName: string;\r\n    /**\r\n     * List of custom uniforms\r\n     */\r\n    public _customUniform: string[];\r\n    /**\r\n     * Names of the new uniforms\r\n     */\r\n    public _newUniforms: string[];\r\n    /**\r\n     * Instances of the new uniform objects\r\n     */\r\n    public _newUniformInstances: { [name: string]: any };\r\n    /**\r\n     * Instances of the new sampler objects\r\n     */\r\n    public _newSamplerInstances: { [name: string]: Texture };\r\n    /**\r\n     * List of the custom attributes\r\n     */\r\n    public _customAttributes: string[];\r\n\r\n    /**\r\n     * Fragment shader string\r\n     */\r\n    public FragmentShader: string;\r\n    /**\r\n     * Vertex shader string\r\n     */\r\n    public VertexShader: string;\r\n\r\n    /**\r\n     * Runs after the material is bound to a mesh\r\n     * @param mesh mesh bound\r\n     * @param effect bound effect used to render\r\n     */\r\n    public AttachAfterBind(mesh: Mesh | undefined, effect: Effect) {\r\n        if (this._newUniformInstances) {\r\n            for (const el in this._newUniformInstances) {\r\n                const ea = el.toString().split(\"-\");\r\n                if (ea[0] == \"vec2\") {\r\n                    effect.setVector2(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"vec3\") {\r\n                    if (this._newUniformInstances[el] instanceof Color3) {\r\n                        effect.setColor3(ea[1], this._newUniformInstances[el]);\r\n                    } else {\r\n                        effect.setVector3(ea[1], this._newUniformInstances[el]);\r\n                    }\r\n                } else if (ea[0] == \"vec4\") {\r\n                    if (this._newUniformInstances[el] instanceof Color4) {\r\n                        effect.setDirectColor4(ea[1], this._newUniformInstances[el]);\r\n                    } else {\r\n                        effect.setVector4(ea[1], this._newUniformInstances[el]);\r\n                    }\r\n                    effect.setVector4(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"mat4\") {\r\n                    effect.setMatrix(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"float\") {\r\n                    effect.setFloat(ea[1], this._newUniformInstances[el]);\r\n                }\r\n            }\r\n        }\r\n        if (this._newSamplerInstances) {\r\n            for (const el in this._newSamplerInstances) {\r\n                const ea = el.toString().split(\"-\");\r\n                if (ea[0] == \"sampler2D\" && this._newSamplerInstances[el].isReady && this._newSamplerInstances[el].isReady()) {\r\n                    effect.setTexture(ea[1], this._newSamplerInstances[el]);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public ReviewUniform(name: string, arr: string[]): string[] {\r\n        if (name == \"uniform\" && this._newUniforms) {\r\n            for (let ind = 0; ind < this._newUniforms.length; ind++) {\r\n                if (this._customUniform[ind].indexOf(\"sampler\") == -1) {\r\n                    arr.push(this._newUniforms[ind].replace(/\\[\\d*\\]/g, \"\"));\r\n                }\r\n            }\r\n        }\r\n        if (name == \"sampler\" && this._newUniforms) {\r\n            for (let ind = 0; ind < this._newUniforms.length; ind++) {\r\n                if (this._customUniform[ind].indexOf(\"sampler\") != -1) {\r\n                    arr.push(this._newUniforms[ind].replace(/\\[\\d*\\]/g, \"\"));\r\n                }\r\n            }\r\n        }\r\n        return arr;\r\n    }\r\n\r\n    /**\r\n     * Builds the material\r\n     * @param shaderName name of the shader\r\n     * @param uniforms list of uniforms\r\n     * @param uniformBuffers list of uniform buffers\r\n     * @param samplers list of samplers\r\n     * @param defines list of defines\r\n     * @param attributes list of attributes\r\n     * @returns the shader name\r\n     */\r\n    public Builder(shaderName: string, uniforms: string[], uniformBuffers: string[], samplers: string[], defines: MaterialDefines | string[], attributes?: string[]): string {\r\n        if (attributes && this._customAttributes && this._customAttributes.length > 0) {\r\n            attributes.push(...this._customAttributes);\r\n        }\r\n\r\n        this.ReviewUniform(\"uniform\", uniforms);\r\n        this.ReviewUniform(\"sampler\", samplers);\r\n\r\n        const name = this._createdShaderName;\r\n\r\n        if (Effect.ShadersStore[name + \"VertexShader\"] && Effect.ShadersStore[name + \"PixelShader\"]) {\r\n            return name;\r\n        }\r\n        Effect.ShadersStore[name + \"VertexShader\"] = this._injectCustomCode(this.VertexShader, \"vertex\");\r\n        Effect.ShadersStore[name + \"PixelShader\"] = this._injectCustomCode(this.FragmentShader, \"fragment\");\r\n\r\n        return name;\r\n    }\r\n\r\n    protected _injectCustomCode(code: string, shaderType: string): string {\r\n        const customCode = this._getCustomCode(shaderType);\r\n\r\n        for (const point in customCode) {\r\n            const injectedCode = customCode[point];\r\n\r\n            if (injectedCode && injectedCode.length > 0) {\r\n                const fullPointName = \"#define \" + point;\r\n                code = code.replace(fullPointName, \"\\n\" + injectedCode + \"\\n\" + fullPointName);\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _getCustomCode(shaderType: string): { [pointName: string]: string } {\r\n        if (shaderType === \"vertex\") {\r\n            return {\r\n                CUSTOM_VERTEX_BEGIN: this.CustomParts.Vertex_Begin,\r\n                CUSTOM_VERTEX_DEFINITIONS: (this._customUniform?.join(\"\\n\") || \"\") + (this.CustomParts.Vertex_Definitions || \"\"),\r\n                CUSTOM_VERTEX_MAIN_BEGIN: this.CustomParts.Vertex_MainBegin,\r\n                CUSTOM_VERTEX_UPDATE_POSITION: this.CustomParts.Vertex_Before_PositionUpdated,\r\n                CUSTOM_VERTEX_UPDATE_NORMAL: this.CustomParts.Vertex_Before_NormalUpdated,\r\n                CUSTOM_VERTEX_MAIN_END: this.CustomParts.Vertex_MainEnd,\r\n                CUSTOM_VERTEX_UPDATE_WORLDPOS: this.CustomParts.Vertex_After_WorldPosComputed,\r\n            };\r\n        }\r\n        return {\r\n            CUSTOM_FRAGMENT_BEGIN: this.CustomParts.Fragment_Begin,\r\n            CUSTOM_FRAGMENT_DEFINITIONS: (this._customUniform?.join(\"\\n\") || \"\") + (this.CustomParts.Fragment_Definitions || \"\"),\r\n            CUSTOM_FRAGMENT_MAIN_BEGIN: this.CustomParts.Fragment_MainBegin,\r\n            CUSTOM_FRAGMENT_UPDATE_DIFFUSE: this.CustomParts.Fragment_Custom_Diffuse,\r\n            CUSTOM_FRAGMENT_UPDATE_ALPHA: this.CustomParts.Fragment_Custom_Alpha,\r\n            CUSTOM_FRAGMENT_BEFORE_LIGHTS: this.CustomParts.Fragment_Before_Lights,\r\n            CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR: this.CustomParts.Fragment_Before_FragColor,\r\n            CUSTOM_FRAGMENT_MAIN_END: this.CustomParts.Fragment_MainEnd,\r\n            CUSTOM_FRAGMENT_BEFORE_FOG: this.CustomParts.Fragment_Before_Fog,\r\n        };\r\n    }\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene, true);\r\n        this.CustomParts = new ShaderSpecialParts();\r\n        this.customShaderNameResolve = this.Builder;\r\n\r\n        this.FragmentShader = Effect.ShadersStore[\"defaultPixelShader\"];\r\n        this.VertexShader = Effect.ShadersStore[\"defaultVertexShader\"];\r\n\r\n        CustomMaterial.ShaderIndexer++;\r\n        this._createdShaderName = \"custom_\" + CustomMaterial.ShaderIndexer;\r\n    }\r\n\r\n    protected override _afterBind(mesh?: Mesh, effect: Nullable<Effect> = null, subMesh?: SubMesh): void {\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this.AttachAfterBind(mesh, effect);\r\n        try {\r\n            super._afterBind(mesh, effect, subMesh);\r\n        } catch (e) {}\r\n    }\r\n\r\n    /**\r\n     * Adds a new uniform to the shader\r\n     * @param name the name of the uniform to add\r\n     * @param kind the type of the uniform to add\r\n     * @param param the value of the uniform to add\r\n     * @returns the current material\r\n     */\r\n    public AddUniform(name: string, kind: string, param: any): CustomMaterial {\r\n        if (!this._customUniform) {\r\n            this._customUniform = [];\r\n            this._newUniforms = [];\r\n            this._newSamplerInstances = {};\r\n            this._newUniformInstances = {};\r\n        }\r\n        if (param) {\r\n            if (kind.indexOf(\"sampler\") != -1) {\r\n                (<any>this._newSamplerInstances)[kind + \"-\" + name] = param;\r\n            } else {\r\n                (<any>this._newUniformInstances)[kind + \"-\" + name] = param;\r\n            }\r\n        }\r\n        this._customUniform.push(\"uniform \" + kind + \" \" + name + \";\");\r\n        this._newUniforms.push(name);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a custom attribute\r\n     * @param name the name of the attribute\r\n     * @returns the current material\r\n     */\r\n    public AddAttribute(name: string): CustomMaterial {\r\n        if (!this._customAttributes) {\r\n            this._customAttributes = [];\r\n        }\r\n\r\n        this._customAttributes.push(name);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Begin portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Begin(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Begin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Definitions portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Definitions(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Definitions = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_MainBegin portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_MainBegin(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_MainBegin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_MainEnd portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_MainEnd(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_MainEnd = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Custom_Diffuse portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Custom_Diffuse(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Custom_Diffuse = shaderPart.replace(\"result\", \"diffuseColor\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Custom_Alpha portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Custom_Alpha(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Custom_Alpha = shaderPart.replace(\"result\", \"alpha\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Before_Lights portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Before_Lights(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Before_Lights = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Before_Fog portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Before_Fog(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Before_Fog = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Before_FragColor portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Before_FragColor(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Fragment_Before_FragColor = shaderPart.replace(\"result\", \"color\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_Begin portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_Begin(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_Begin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_Definitions portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_Definitions(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_Definitions = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_MainBegin portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_MainBegin(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_MainBegin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_Before_PositionUpdated portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_Before_PositionUpdated(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_Before_PositionUpdated = shaderPart.replace(\"result\", \"positionUpdated\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_Before_NormalUpdated portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_Before_NormalUpdated(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_Before_NormalUpdated = shaderPart.replace(\"result\", \"normalUpdated\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_After_WorldPosComputed portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_After_WorldPosComputed(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_After_WorldPosComputed = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_MainEnd portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_MainEnd(shaderPart: string): CustomMaterial {\r\n        this.CustomParts.Vertex_MainEnd = shaderPart;\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.CustomMaterial\", CustomMaterial);\r\n"], "names": [], "mappings": ";;;;;AAEA,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAE/C,OAAO,EAAE,gBAAgB,EAAE,sDAAwC;AAGnE,OAAO,EAAE,aAAa,EAAE,0CAA4B;AACpD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,4CAA8B;AAIvD,mDAAqC;AACrC,qDAAuC;;;;;;;AAKjC,MAAO,qBAAqB;IAU9B,aAAA,CAAe,CAAC;CACnB;AAKK,MAAO,kBAAkB;IAC3B,aAAA,CAAe,CAAC;CAuEnB;AAKK,MAAO,cAAe,iLAAQ,mBAAgB;IA2ChD;;;;OAIG,CACI,eAAe,CAAC,IAAsB,EAAE,MAAc,EAAA;QACzD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAK,MAAM,EAAE,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;gBACzC,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;oBAClB,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,6KAAY,SAAM,EAAE,CAAC;wBAClD,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC3D,CAAC,MAAM,CAAC;wBACJ,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC5D,CAAC;gBACL,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,6KAAY,SAAM,EAAE,CAAC;wBAClD,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjE,CAAC,MAAM,CAAC;wBACJ,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC5D,CAAC;oBACD,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;oBACzB,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3D,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE,CAAC;oBAC1B,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1D,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAK,MAAM,EAAE,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;gBACzC,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,WAAW,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;oBAC3G,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,aAAa,CAAC,IAAY,EAAE,GAAa,EAAA;QAC5C,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,CAAE,CAAC;gBACtD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oBACpD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,CAAE,CAAC;gBACtD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oBACpD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;;;;;OASG,CACI,OAAO,CAAC,UAAkB,EAAE,QAAkB,EAAE,cAAwB,EAAE,QAAkB,EAAE,OAAmC,EAAE,UAAqB,EAAA;QAC3J,IAAI,UAAU,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5E,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAExC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAErC,IAAI,uKAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,kKAAI,SAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,EAAE,CAAC;YAC1F,OAAO,IAAI,CAAC;QAChB,CAAC;sKACD,SAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;sKACjG,SAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAEpG,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,iBAAiB,CAAC,IAAY,EAAE,UAAkB,EAAA;QACxD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAEnD,IAAK,MAAM,KAAK,IAAI,UAAU,CAAE,CAAC;YAC7B,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YAEvC,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,MAAM,aAAa,GAAG,UAAU,GAAG,KAAK,CAAC;gBACzC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,GAAG,YAAY,GAAG,IAAI,GAAG,aAAa,CAAC,CAAC;YACnF,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,cAAc,CAAC,UAAkB,EAAA;YAcL;QAblC,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;;YAC1B,OAAO;gBACH,mBAAmB,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY;gBAClD,yBAAyB,EAAE,2BAAC,IAAI,CAAC,cAAc,gFAAE,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC;gBAChH,wBAAwB,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB;gBAC3D,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,6BAA6B;gBAC7E,2BAA2B,EAAE,IAAI,CAAC,WAAW,CAAC,2BAA2B;gBACzE,sBAAsB,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc;gBACvD,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,6BAA6B;aAChF,CAAC;QACN,CAAC;QACD,OAAO;YACH,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc;YACtD,2BAA2B,EAAE,8BAAK,CAAC,cAAc,8EAAE,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAoB,IAAI,EAAE,CAAC;YACpH,0BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC,kBAAkB;YAC/D,8BAA8B,EAAE,IAAI,CAAC,WAAW,CAAC,uBAAuB;YACxE,4BAA4B,EAAE,IAAI,CAAC,WAAW,CAAC,qBAAqB;YACpE,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,sBAAsB;YACtE,gCAAgC,EAAE,IAAI,CAAC,WAAW,CAAC,yBAAyB;YAC5E,wBAAwB,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB;YAC3D,0BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC,mBAAmB;SACnE,CAAC;IACN,CAAC;IAckB,UAAU,CAAC,IAAW,EAAoD;qBAAlD,iEAA2B,IAAI,EAAE,OAAiB;QACzF,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC;YACD,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC;IAED;;;;;;OAMG,CACI,UAAU,CAAC,IAAY,EAAE,IAAY,EAAE,KAAU,EAAA;QACpD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;YACzB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,GAAG,CAAA,CAAE,CAAC;YAC/B,IAAI,CAAC,oBAAoB,GAAG,CAAA,CAAE,CAAC;QACnC,CAAC;QACD,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC,oBAAqB,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;YAChE,CAAC,MAAM,CAAC;gBACE,IAAI,CAAC,oBAAqB,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;YAChE,CAAC;QACL,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,IAAY,EAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,UAAkB,EAAA;QACpC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,UAAU,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,UAAkB,EAAA;QAC1C,IAAI,CAAC,WAAW,CAAC,oBAAoB,GAAG,UAAU,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,UAAkB,EAAA;QACxC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,UAAkB,EAAA;QACtC,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,uBAAuB,CAAC,UAAkB,EAAA;QAC7C,IAAI,CAAC,WAAW,CAAC,uBAAuB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QACxF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,UAAkB,EAAA;QAC3C,IAAI,CAAC,WAAW,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,sBAAsB,CAAC,UAAkB,EAAA;QAC5C,IAAI,CAAC,WAAW,CAAC,sBAAsB,GAAG,UAAU,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,UAAkB,EAAA;QACzC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,UAAU,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,yBAAyB,CAAC,UAAkB,EAAA;QAC/C,IAAI,CAAC,WAAW,CAAC,yBAAyB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,UAAkB,EAAA;QAClC,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,UAAU,CAAC;QAC3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,UAAkB,EAAA;QACxC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,UAAkB,EAAA;QACtC,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,6BAA6B,CAAC,UAAkB,EAAA;QACnD,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QACjG,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,2BAA2B,CAAC,UAAkB,EAAA;QACjD,IAAI,CAAC,WAAW,CAAC,2BAA2B,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC7F,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,6BAA6B,CAAC,UAAkB,EAAA;QACnD,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,UAAU,CAAC;QAC5D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,UAAkB,EAAA;QACpC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,UAAU,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IA9ND,YAAY,IAAY,EAAE,KAAa,CAAA;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC5C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5C,IAAI,CAAC,cAAc,iKAAG,SAAM,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;QAChE,IAAI,CAAC,YAAY,iKAAG,SAAM,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;QAE/D,cAAc,CAAC,aAAa,EAAE,CAAC;QAC/B,IAAI,CAAC,kBAAkB,GAAG,SAAS,GAAG,cAAc,CAAC,aAAa,CAAC;IACvE,CAAC;;AAvLD;;GAEG,CACW,eAAA,aAAa,GAAG,CAAC,CAAC;gKA2YpC,gBAAA,AAAa,EAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/materials/custom/pbrCustomMaterial.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/materials/src/custom/pbrCustomMaterial.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Texture } from \"core/Materials/Textures/texture\";\r\nimport { Effect } from \"core/Materials/effect\";\r\nimport type { MaterialDefines } from \"core/Materials/materialDefines\";\r\nimport { PBRMaterial } from \"core/Materials/PBR/pbrMaterial\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { Scene } from \"core/scene\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\nimport { ShaderCodeInliner } from \"core/Engines/Processors/shaderCodeInliner\";\r\nimport type { ICustomShaderNameResolveOptions } from \"core/Materials/material\";\r\nimport { Color3, Color4 } from \"core/Maths/math.color\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { SubMesh } from \"core/Meshes/subMesh\";\r\n\r\nimport \"core/Shaders/pbr.vertex\";\r\nimport \"core/Shaders/pbr.fragment\";\r\n\r\n/**\r\n * Albedo parts of the shader\r\n */\r\nexport class ShaderAlbedoParts {\r\n    constructor() {}\r\n\r\n    /**\r\n     * Beginning of the fragment shader\r\n     */\r\n    public Fragment_Begin: string;\r\n    /**\r\n     * Fragment definitions\r\n     */\r\n    public Fragment_Definitions: string;\r\n    /**\r\n     * Beginning of the main function\r\n     */\r\n    public Fragment_MainBegin: string;\r\n    /**\r\n     * End of main function\r\n     */\r\n    public Fragment_MainEnd: string;\r\n\r\n    /**\r\n     * Albedo color\r\n     */\r\n    public Fragment_Custom_Albedo: string;\r\n    /**\r\n     * Lights\r\n     */\r\n    public Fragment_Before_Lights: string;\r\n    /**\r\n     * Metallic and roughness\r\n     */\r\n    public Fragment_Custom_MetallicRoughness: string;\r\n    /**\r\n     * Microsurface\r\n     */\r\n    public Fragment_Custom_MicroSurface: string;\r\n    /**\r\n     * Fog computations\r\n     */\r\n    public Fragment_Before_Fog: string;\r\n    /**\r\n     * Alpha\r\n     */\r\n    public Fragment_Custom_Alpha: string;\r\n    /**\r\n     * Color composition\r\n     */\r\n    public Fragment_Before_FinalColorComposition: string;\r\n    /**\r\n     * Fragment color\r\n     */\r\n    public Fragment_Before_FragColor: string;\r\n\r\n    /**\r\n     * Beginning of vertex shader\r\n     */\r\n    public Vertex_Begin: string;\r\n    /**\r\n     * Vertex definitions\r\n     */\r\n    public Vertex_Definitions: string;\r\n    /**\r\n     * Vertex main begin\r\n     */\r\n    public Vertex_MainBegin: string;\r\n\r\n    /**\r\n     * Vertex before position updated\r\n     */\r\n    public Vertex_Before_PositionUpdated: string;\r\n\r\n    /**\r\n     * Vertex before normal updated\r\n     */\r\n    public Vertex_Before_NormalUpdated: string;\r\n\r\n    /**\r\n     * Vertex after world pos computed\r\n     */\r\n    public Vertex_After_WorldPosComputed: string;\r\n\r\n    /**\r\n     * Vertex main end\r\n     */\r\n    public Vertex_MainEnd: string;\r\n}\r\n\r\n/**\r\n * @deprecated use ShaderAlbedoParts instead.\r\n */\r\nexport const ShaderAlebdoParts = ShaderAlbedoParts;\r\n\r\nexport class PBRCustomMaterial extends PBRMaterial {\r\n    /**\r\n     * Index for each created shader\r\n     */\r\n    public static ShaderIndexer = 1;\r\n    /**\r\n     * Custom shader structure\r\n     */\r\n    public CustomParts: ShaderAlbedoParts;\r\n    /**\r\n     * Name of the shader\r\n     */\r\n    _createdShaderName: string;\r\n    /**\r\n     * List of custom uniforms\r\n     */\r\n    _customUniform: string[];\r\n    /**\r\n     * Names of the new uniforms\r\n     */\r\n    _newUniforms: string[];\r\n    /**\r\n     * Instances of the new uniform objects\r\n     */\r\n    _newUniformInstances: { [name: string]: any };\r\n    /**\r\n     * Instances of the new sampler objects\r\n     */\r\n    _newSamplerInstances: { [name: string]: Texture };\r\n    /**\r\n     * List of the custom attributes\r\n     */\r\n    _customAttributes: string[];\r\n\r\n    /**\r\n     * Fragment shader string\r\n     */\r\n    public FragmentShader: string;\r\n    /**\r\n     * Vertex shader string\r\n     */\r\n    public VertexShader: string;\r\n\r\n    /**\r\n     * Runs after the material is bound to a mesh\r\n     * @param mesh mesh bound\r\n     * @param effect bound effect used to render\r\n     */\r\n    public AttachAfterBind(mesh: Mesh | undefined, effect: Effect) {\r\n        if (this._newUniformInstances) {\r\n            for (const el in this._newUniformInstances) {\r\n                const ea = el.toString().split(\"-\");\r\n                if (ea[0] == \"vec2\") {\r\n                    effect.setVector2(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"vec3\") {\r\n                    if (this._newUniformInstances[el] instanceof Color3) {\r\n                        effect.setColor3(ea[1], this._newUniformInstances[el]);\r\n                    } else {\r\n                        effect.setVector3(ea[1], this._newUniformInstances[el]);\r\n                    }\r\n                } else if (ea[0] == \"vec4\") {\r\n                    if (this._newUniformInstances[el] instanceof Color4) {\r\n                        effect.setDirectColor4(ea[1], this._newUniformInstances[el]);\r\n                    } else {\r\n                        effect.setVector4(ea[1], this._newUniformInstances[el]);\r\n                    }\r\n                    effect.setVector4(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"mat4\") {\r\n                    effect.setMatrix(ea[1], this._newUniformInstances[el]);\r\n                } else if (ea[0] == \"float\") {\r\n                    effect.setFloat(ea[1], this._newUniformInstances[el]);\r\n                }\r\n            }\r\n        }\r\n        if (this._newSamplerInstances) {\r\n            for (const el in this._newSamplerInstances) {\r\n                const ea = el.toString().split(\"-\");\r\n                if (ea[0] == \"sampler2D\" && this._newSamplerInstances[el].isReady && this._newSamplerInstances[el].isReady()) {\r\n                    effect.setTexture(ea[1], this._newSamplerInstances[el]);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public ReviewUniform(name: string, arr: string[]): string[] {\r\n        if (name == \"uniform\" && this._newUniforms) {\r\n            for (let ind = 0; ind < this._newUniforms.length; ind++) {\r\n                if (this._customUniform[ind].indexOf(\"sampler\") == -1) {\r\n                    arr.push(this._newUniforms[ind].replace(/\\[\\d*\\]/g, \"\"));\r\n                }\r\n            }\r\n        }\r\n        if (name == \"sampler\" && this._newUniforms) {\r\n            for (let ind = 0; ind < this._newUniforms.length; ind++) {\r\n                if (this._customUniform[ind].indexOf(\"sampler\") != -1) {\r\n                    arr.push(this._newUniforms[ind].replace(/\\[\\d*\\]/g, \"\"));\r\n                }\r\n            }\r\n        }\r\n        return arr;\r\n    }\r\n\r\n    /**\r\n     * Builds the material\r\n     * @param shaderName name of the shader\r\n     * @param uniforms list of uniforms\r\n     * @param uniformBuffers list of uniform buffers\r\n     * @param samplers list of samplers\r\n     * @param defines list of defines\r\n     * @param attributes list of attributes\r\n     * @param options options to compile the shader\r\n     * @returns the shader name\r\n     */\r\n    public Builder(\r\n        shaderName: string,\r\n        uniforms: string[],\r\n        uniformBuffers: string[],\r\n        samplers: string[],\r\n        defines: MaterialDefines | string[],\r\n        attributes?: string[],\r\n        options?: ICustomShaderNameResolveOptions\r\n    ): string {\r\n        if (options) {\r\n            const currentProcessing = options.processFinalCode;\r\n            options.processFinalCode = (type: string, code: string) => {\r\n                if (type === \"vertex\") {\r\n                    return currentProcessing ? currentProcessing(type, code) : code;\r\n                }\r\n                const sci = new ShaderCodeInliner(code);\r\n                sci.inlineToken = \"#define pbr_inline\";\r\n                sci.processCode();\r\n                return currentProcessing ? currentProcessing(type, sci.code) : sci.code;\r\n            };\r\n        }\r\n\r\n        if (attributes && this._customAttributes && this._customAttributes.length > 0) {\r\n            attributes.push(...this._customAttributes);\r\n        }\r\n\r\n        this.ReviewUniform(\"uniform\", uniforms);\r\n        this.ReviewUniform(\"sampler\", samplers);\r\n\r\n        const name = this._createdShaderName;\r\n\r\n        if (Effect.ShadersStore[name + \"VertexShader\"] && Effect.ShadersStore[name + \"PixelShader\"]) {\r\n            return name;\r\n        }\r\n        Effect.ShadersStore[name + \"VertexShader\"] = this._injectCustomCode(this.VertexShader, \"vertex\");\r\n        Effect.ShadersStore[name + \"PixelShader\"] = this._injectCustomCode(this.FragmentShader, \"fragment\");\r\n\r\n        return name;\r\n    }\r\n\r\n    protected _injectCustomCode(code: string, shaderType: string): string {\r\n        const customCode = this._getCustomCode(shaderType);\r\n\r\n        for (const point in customCode) {\r\n            const injectedCode = customCode[point];\r\n\r\n            if (injectedCode && injectedCode.length > 0) {\r\n                const fullPointName = \"#define \" + point;\r\n                code = code.replace(fullPointName, \"\\n\" + injectedCode + \"\\n\" + fullPointName);\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _getCustomCode(shaderType: string): { [pointName: string]: string } {\r\n        if (shaderType === \"vertex\") {\r\n            return {\r\n                CUSTOM_VERTEX_BEGIN: this.CustomParts.Vertex_Begin,\r\n                CUSTOM_VERTEX_DEFINITIONS: (this._customUniform?.join(\"\\n\") || \"\") + (this.CustomParts.Vertex_Definitions || \"\"),\r\n                CUSTOM_VERTEX_MAIN_BEGIN: this.CustomParts.Vertex_MainBegin,\r\n                CUSTOM_VERTEX_UPDATE_POSITION: this.CustomParts.Vertex_Before_PositionUpdated,\r\n                CUSTOM_VERTEX_UPDATE_NORMAL: this.CustomParts.Vertex_Before_NormalUpdated,\r\n                CUSTOM_VERTEX_MAIN_END: this.CustomParts.Vertex_MainEnd,\r\n                CUSTOM_VERTEX_UPDATE_WORLDPOS: this.CustomParts.Vertex_After_WorldPosComputed,\r\n            };\r\n        }\r\n        return {\r\n            CUSTOM_FRAGMENT_BEGIN: this.CustomParts.Fragment_Begin,\r\n            CUSTOM_FRAGMENT_MAIN_BEGIN: this.CustomParts.Fragment_MainBegin,\r\n            CUSTOM_FRAGMENT_DEFINITIONS: (this._customUniform?.join(\"\\n\") || \"\") + (this.CustomParts.Fragment_Definitions || \"\"),\r\n            CUSTOM_FRAGMENT_UPDATE_ALBEDO: this.CustomParts.Fragment_Custom_Albedo,\r\n            CUSTOM_FRAGMENT_UPDATE_ALPHA: this.CustomParts.Fragment_Custom_Alpha,\r\n            CUSTOM_FRAGMENT_BEFORE_LIGHTS: this.CustomParts.Fragment_Before_Lights,\r\n            CUSTOM_FRAGMENT_UPDATE_METALLICROUGHNESS: this.CustomParts.Fragment_Custom_MetallicRoughness,\r\n            CUSTOM_FRAGMENT_UPDATE_MICROSURFACE: this.CustomParts.Fragment_Custom_MicroSurface,\r\n            CUSTOM_FRAGMENT_BEFORE_FINALCOLORCOMPOSITION: this.CustomParts.Fragment_Before_FinalColorComposition,\r\n            CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR: this.CustomParts.Fragment_Before_FragColor,\r\n            CUSTOM_FRAGMENT_MAIN_END: this.CustomParts.Fragment_MainEnd,\r\n            CUSTOM_FRAGMENT_BEFORE_FOG: this.CustomParts.Fragment_Before_Fog,\r\n        };\r\n    }\r\n\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene, true);\r\n        this.CustomParts = new ShaderAlbedoParts();\r\n        this.customShaderNameResolve = this.Builder;\r\n\r\n        this.FragmentShader = Effect.ShadersStore[\"pbrPixelShader\"];\r\n        this.VertexShader = Effect.ShadersStore[\"pbrVertexShader\"];\r\n\r\n        this.FragmentShader = this.FragmentShader.replace(/#include<pbrBlockAlbedoOpacity>/g, Effect.IncludesShadersStore[\"pbrBlockAlbedoOpacity\"]);\r\n        this.FragmentShader = this.FragmentShader.replace(/#include<pbrBlockReflectivity>/g, Effect.IncludesShadersStore[\"pbrBlockReflectivity\"]);\r\n        this.FragmentShader = this.FragmentShader.replace(/#include<pbrBlockFinalColorComposition>/g, Effect.IncludesShadersStore[\"pbrBlockFinalColorComposition\"]);\r\n\r\n        PBRCustomMaterial.ShaderIndexer++;\r\n        this._createdShaderName = \"custompbr_\" + PBRCustomMaterial.ShaderIndexer;\r\n    }\r\n\r\n    protected override _afterBind(mesh?: Mesh, effect: Nullable<Effect> = null, subMesh?: SubMesh): void {\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this.AttachAfterBind(mesh, effect);\r\n        try {\r\n            super._afterBind(mesh, effect, subMesh);\r\n        } catch (e) {}\r\n    }\r\n\r\n    /**\r\n     * Adds a new uniform to the shader\r\n     * @param name the name of the uniform to add\r\n     * @param kind the type of the uniform to add\r\n     * @param param the value of the uniform to add\r\n     * @returns the current material\r\n     */\r\n    public AddUniform(name: string, kind: string, param: any): PBRCustomMaterial {\r\n        if (!this._customUniform) {\r\n            this._customUniform = [];\r\n            this._newUniforms = [];\r\n            this._newSamplerInstances = {};\r\n            this._newUniformInstances = {};\r\n        }\r\n        if (param) {\r\n            if (kind.indexOf(\"sampler\") != -1) {\r\n                (<any>this._newSamplerInstances)[kind + \"-\" + name] = param;\r\n            } else {\r\n                (<any>this._newUniformInstances)[kind + \"-\" + name] = param;\r\n            }\r\n        }\r\n        this._customUniform.push(\"uniform \" + kind + \" \" + name + \";\");\r\n        this._newUniforms.push(name);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Adds a custom attribute\r\n     * @param name the name of the attribute\r\n     * @returns the current material\r\n     */\r\n    public AddAttribute(name: string): PBRCustomMaterial {\r\n        if (!this._customAttributes) {\r\n            this._customAttributes = [];\r\n        }\r\n\r\n        this._customAttributes.push(name);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Begin portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Begin(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Begin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Definitions portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Definitions(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Definitions = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_MainBegin portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_MainBegin(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_MainBegin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Custom_Albedo portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Custom_Albedo(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Custom_Albedo = shaderPart.replace(\"result\", \"surfaceAlbedo\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Custom_Alpha portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Custom_Alpha(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Custom_Alpha = shaderPart.replace(\"result\", \"alpha\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Before_Lights portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Before_Lights(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Before_Lights = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Custom_MetallicRoughness portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Custom_MetallicRoughness(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Custom_MetallicRoughness = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Custom_MicroSurface portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Custom_MicroSurface(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Custom_MicroSurface = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Before_Fog portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Before_Fog(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Before_Fog = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Before_FinalColorComposition portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Before_FinalColorComposition(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Before_FinalColorComposition = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_Before_FragColor portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_Before_FragColor(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_Before_FragColor = shaderPart.replace(\"result\", \"color\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Fragment_MainEnd portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Fragment_MainEnd(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Fragment_MainEnd = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_Begin portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_Begin(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_Begin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_Definitions portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_Definitions(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_Definitions = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_MainBegin portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_MainBegin(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_MainBegin = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_Before_PositionUpdated portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_Before_PositionUpdated(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_Before_PositionUpdated = shaderPart.replace(\"result\", \"positionUpdated\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_Before_NormalUpdated portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_Before_NormalUpdated(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_Before_NormalUpdated = shaderPart.replace(\"result\", \"normalUpdated\");\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_After_WorldPosComputed portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_After_WorldPosComputed(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_After_WorldPosComputed = shaderPart;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the code on Vertex_MainEnd portion\r\n     * @param shaderPart the code string\r\n     * @returns the current material\r\n     */\r\n    public Vertex_MainEnd(shaderPart: string): PBRCustomMaterial {\r\n        this.CustomParts.Vertex_MainEnd = shaderPart;\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRCustomMaterial\", PBRCustomMaterial);\r\n"], "names": [], "mappings": ";;;;;AAEA,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAE/C,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAG7D,OAAO,EAAE,aAAa,EAAE,0CAA4B;AACpD,OAAO,EAAE,iBAAiB,EAAE,gEAAkD;AAE9E,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,4CAA8B;AAIvD,+CAAiC;AACjC,iDAAmC;;;;;;;;AAK7B,MAAO,iBAAiB;IAC1B,aAAA,CAAe,CAAC;CAoFnB;AAKM,MAAM,iBAAiB,GAAG,iBAAiB,CAAC;AAE7C,MAAO,iBAAkB,mLAAQ,cAAW;IA2C9C;;;;OAIG,CACI,eAAe,CAAC,IAAsB,EAAE,MAAc,EAAA;QACzD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAK,MAAM,EAAE,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;gBACzC,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;oBAClB,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,6KAAY,SAAM,EAAE,CAAC;wBAClD,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC3D,CAAC,MAAM,CAAC;wBACJ,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC5D,CAAC;gBACL,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;oBACzB,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,6KAAY,SAAM,EAAE,CAAC;wBAClD,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;oBACjE,CAAC,MAAM,CAAC;wBACJ,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC5D,CAAC;oBACD,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;oBACzB,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3D,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE,CAAC;oBAC1B,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC1D,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAK,MAAM,EAAE,IAAI,IAAI,CAAC,oBAAoB,CAAE,CAAC;gBACzC,MAAM,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACpC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,WAAW,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;oBAC3G,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC5D,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,aAAa,CAAC,IAAY,EAAE,GAAa,EAAA;QAC5C,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,CAAE,CAAC;gBACtD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oBACpD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,CAAE,CAAC;gBACtD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oBACpD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;;;;;;OAUG,CACI,OAAO,CACV,UAAkB,EAClB,QAAkB,EAClB,cAAwB,EACxB,QAAkB,EAClB,OAAmC,EACnC,UAAqB,EACrB,OAAyC,EAAA;QAEzC,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAgB,CAAC;YACnD,OAAO,CAAC,gBAAgB,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,EAAE;gBACtD,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACpB,OAAO,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACpE,CAAC;gBACD,MAAM,GAAG,GAAG,IAAI,yMAAiB,CAAC,IAAI,CAAC,CAAC;gBACxC,GAAG,CAAC,WAAW,GAAG,oBAAoB,CAAC;gBACvC,GAAG,CAAC,WAAW,EAAE,CAAC;gBAClB,OAAO,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;YAC5E,CAAC,CAAC;QACN,CAAC;QAED,IAAI,UAAU,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5E,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAExC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAErC,kKAAI,SAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,kKAAI,SAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,EAAE,CAAC;YAC1F,OAAO,IAAI,CAAC;QAChB,CAAC;sKACD,SAAM,CAAC,YAAY,CAAC,IAAI,GAAG,cAAc,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;sKACjG,SAAM,CAAC,YAAY,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QAEpG,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,iBAAiB,CAAC,IAAY,EAAE,UAAkB,EAAA;QACxD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAEnD,IAAK,MAAM,KAAK,IAAI,UAAU,CAAE,CAAC;YAC7B,MAAM,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YAEvC,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,MAAM,aAAa,GAAG,UAAU,GAAG,KAAK,CAAC;gBACzC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,GAAG,YAAY,GAAG,IAAI,GAAG,aAAa,CAAC,CAAC;YACnF,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,cAAc,CAAC,UAAkB,EAAA;;QACvC,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;;YAC1B,OAAO;gBACH,mBAAmB,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY;gBAClD,yBAAyB,EAAE,+BAAK,CAAC,cAAc,0DAAnB,sBAAqB,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC;gBAChH,wBAAwB,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB;gBAC3D,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,6BAA6B;gBAC7E,2BAA2B,EAAE,IAAI,CAAC,WAAW,CAAC,2BAA2B;gBACzE,sBAAsB,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc;gBACvD,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,6BAA6B;aAChF,CAAC;QACN,CAAC;QACD,OAAO;YACH,qBAAqB,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc;YACtD,0BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC,kBAAkB;YAC/D,2BAA2B,EAAE,GAAC,2BAAI,CAAC,cAAc,8EAAE,IAAI,CAAC,IAAI,CAAC,KAAI,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,oBAAoB,IAAI,EAAE,CAAC;YACpH,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,sBAAsB;YACtE,4BAA4B,EAAE,IAAI,CAAC,WAAW,CAAC,qBAAqB;YACpE,6BAA6B,EAAE,IAAI,CAAC,WAAW,CAAC,sBAAsB;YACtE,wCAAwC,EAAE,IAAI,CAAC,WAAW,CAAC,iCAAiC;YAC5F,mCAAmC,EAAE,IAAI,CAAC,WAAW,CAAC,4BAA4B;YAClF,4CAA4C,EAAE,IAAI,CAAC,WAAW,CAAC,qCAAqC;YACpG,gCAAgC,EAAE,IAAI,CAAC,WAAW,CAAC,yBAAyB;YAC5E,wBAAwB,EAAE,IAAI,CAAC,WAAW,CAAC,gBAAgB;YAC3D,0BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC,mBAAmB;SACnE,CAAC;IACN,CAAC;IAkBkB,UAAU,CAAC,IAAW,EAAoD;qBAAlD,iEAA2B,IAAI,EAAE,OAAiB;QACzF,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC;YACD,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC;IAED;;;;;;OAMG,CACI,UAAU,CAAC,IAAY,EAAE,IAAY,EAAE,KAAU,EAAA;QACpD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;YACzB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,GAAG,CAAA,CAAE,CAAC;YAC/B,IAAI,CAAC,oBAAoB,GAAG,CAAA,CAAE,CAAC;QACnC,CAAC;QACD,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC,oBAAqB,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;YAChE,CAAC,MAAM,CAAC;gBACE,IAAI,CAAC,oBAAqB,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC;YAChE,CAAC;QACL,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,IAAY,EAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,UAAkB,EAAA;QACpC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,UAAU,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,UAAkB,EAAA;QAC1C,IAAI,CAAC,WAAW,CAAC,oBAAoB,GAAG,UAAU,CAAC;QACnD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,UAAkB,EAAA;QACxC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,sBAAsB,CAAC,UAAkB,EAAA;QAC5C,IAAI,CAAC,WAAW,CAAC,sBAAsB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QACxF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,UAAkB,EAAA;QAC3C,IAAI,CAAC,WAAW,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC/E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,sBAAsB,CAAC,UAAkB,EAAA;QAC5C,IAAI,CAAC,WAAW,CAAC,sBAAsB,GAAG,UAAU,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,iCAAiC,CAAC,UAAkB,EAAA;QACvD,IAAI,CAAC,WAAW,CAAC,iCAAiC,GAAG,UAAU,CAAC;QAChE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,4BAA4B,CAAC,UAAkB,EAAA;QAClD,IAAI,CAAC,WAAW,CAAC,4BAA4B,GAAG,UAAU,CAAC;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,UAAkB,EAAA;QACzC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,UAAU,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,qCAAqC,CAAC,UAAkB,EAAA;QAC3D,IAAI,CAAC,WAAW,CAAC,qCAAqC,GAAG,UAAU,CAAC;QACpE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,yBAAyB,CAAC,UAAkB,EAAA;QAC/C,IAAI,CAAC,WAAW,CAAC,yBAAyB,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,UAAkB,EAAA;QACtC,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,UAAkB,EAAA;QAClC,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,UAAU,CAAC;QAC3C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,UAAkB,EAAA;QACxC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,UAAU,CAAC;QACjD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,UAAkB,EAAA;QACtC,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,UAAU,CAAC;QAC/C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,6BAA6B,CAAC,UAAkB,EAAA;QACnD,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QACjG,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,2BAA2B,CAAC,UAAkB,EAAA;QACjD,IAAI,CAAC,WAAW,CAAC,2BAA2B,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QAC7F,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,6BAA6B,CAAC,UAAkB,EAAA;QACnD,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,UAAU,CAAC;QAC5D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,UAAkB,EAAA;QACpC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,UAAU,CAAC;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAhQD,YAAY,IAAY,EAAE,KAAa,CAAA;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAC3C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5C,IAAI,CAAC,cAAc,iKAAG,SAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAC5D,IAAI,CAAC,YAAY,GAAG,uKAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QAE3D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,kCAAkC,gKAAE,SAAM,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,CAAC,CAAC;QAC5I,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,iCAAiC,EAAE,uKAAM,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC,CAAC;QAC1I,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,0CAA0C,EAAE,uKAAM,CAAC,oBAAoB,CAAC,+BAA+B,CAAC,CAAC,CAAC;QAE5J,iBAAiB,CAAC,aAAa,EAAE,CAAC;QAClC,IAAI,CAAC,kBAAkB,GAAG,YAAY,GAAG,iBAAiB,CAAC,aAAa,CAAC;IAC7E,CAAC;;AApND;;GAEG,CACW,kBAAA,aAAa,GAAG,CAAC,CAAC;gKAscpC,gBAAA,AAAa,EAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/materials/custom/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/materials/src/custom/index.ts"], "sourcesContent": ["export * from \"./customMaterial\";\r\nexport * from \"./pbrCustomMaterial\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,kBAAkB,CAAC;AACjC,cAAc,qBAAqB,CAAC", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/asciiArt/asciiart.fragment.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/asciiArt/asciiart.fragment.ts"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\n\nconst name = \"asciiartPixelShader\";\nconst shader = `varying vec2 vUV;uniform sampler2D textureSampler;uniform sampler2D asciiArtFont;uniform vec4 asciiArtFontInfos;uniform vec4 asciiArtOptions;float getLuminance(vec3 color)\n{return clamp(dot(color,vec3(0.2126,0.7152,0.0722)),0.,1.);}\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) \n{float caracterSize=asciiArtFontInfos.x;float numChar=asciiArtFontInfos.y-1.0;float fontx=asciiArtFontInfos.z;float fonty=asciiArtFontInfos.w;float screenx=asciiArtOptions.x;float screeny=asciiArtOptions.y;float tileX=float(floor((gl_FragCoord.x)/caracterSize))*caracterSize/screenx;float tileY=float(floor((gl_FragCoord.y)/caracterSize))*caracterSize/screeny;vec2 tileUV=vec2(tileX,tileY);vec4 tileColor=texture2D(textureSampler,tileUV);vec4 baseColor=texture2D(textureSampler,vUV);float tileLuminance=getLuminance(tileColor.rgb);float offsetx=(float(floor(tileLuminance*numChar)))*caracterSize/fontx;float offsety=0.0;float x=float(mod(gl_FragCoord.x,caracterSize))/fontx;float y=float(mod(gl_FragCoord.y,caracterSize))/fonty;vec4 finalColor= texture2D(asciiArtFont,vec2(offsetx+x,offsety+(caracterSize/fonty-y)));finalColor.rgb*=tileColor.rgb;finalColor.a=1.0;finalColor= mix(finalColor,tileColor,asciiArtOptions.w);finalColor= mix(finalColor,baseColor,asciiArtOptions.z);gl_FragColor=finalColor;}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const asciiartPixelShader = { name, shader };\n"], "names": [], "mappings": "AAAA,eAAe;;;;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;;AAEvD,MAAM,IAAI,GAAG,qBAAqB,CAAC;AACnC,MAAM,MAAM,GAAG;AAKf,aAAa;AACb,IAAI,kKAAC,cAAW,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;qKAClC,cAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC5C,CAAC;AAEM,MAAM,mBAAmB,GAAG;IAAE,IAAI;IAAE,MAAM;AAAA,CAAE,CAAC", "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/asciiArt/asciiArtPostProcess.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/asciiArt/asciiArtPostProcess.ts"], "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { serialize } from \"core/Misc/decorators\";\r\nimport { SerializationHelper } from \"core/Misc/decorators.serialization\";\r\nimport type { Camera } from \"core/Cameras/camera\";\r\nimport { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\nimport type { Effect } from \"core/Materials/effect\";\r\nimport { PostProcess } from \"core/PostProcesses/postProcess\";\r\nimport type { Scene } from \"core/scene\";\r\nimport \"core/Engines/Extensions/engine.dynamicTexture\";\r\nimport \"./asciiart.fragment\";\r\n\r\n/**\r\n * AsciiArtFontTexture is the helper class used to easily create your ascii art font texture.\r\n *\r\n * It basically takes care rendering the font front the given font size to a texture.\r\n * This is used later on in the postprocess.\r\n */\r\nexport class AsciiArtFontTexture extends BaseTexture {\r\n    @serialize(\"font\")\r\n    private _font: string;\r\n\r\n    @serialize(\"text\")\r\n    private _text: string;\r\n\r\n    private _charSize: number;\r\n\r\n    /**\r\n     * Gets the size of one char in the texture (each char fits in size * size space in the texture).\r\n     */\r\n    public get charSize(): number {\r\n        return this._charSize;\r\n    }\r\n\r\n    /**\r\n     * Create a new instance of the Ascii Art FontTexture class\r\n     * @param name the name of the texture\r\n     * @param font the font to use, use the W3C CSS notation\r\n     * @param text the caracter set to use in the rendering.\r\n     * @param scene the scene that owns the texture\r\n     */\r\n    constructor(name: string, font: string, text: string, scene: Nullable<Scene> = null) {\r\n        super(scene);\r\n\r\n        scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        this.name = name;\r\n        this._text == text;\r\n        this._font == font;\r\n\r\n        this.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        //this.anisotropicFilteringLevel = 1;\r\n\r\n        // Get the font specific info.\r\n        const maxCharHeight = this._getFontHeight(font);\r\n        const maxCharWidth = this._getFontWidth(font);\r\n\r\n        this._charSize = Math.max(maxCharHeight.height, maxCharWidth);\r\n\r\n        // This is an approximate size, but should always be able to fit at least the maxCharCount.\r\n        const textureWidth = Math.ceil(this._charSize * text.length);\r\n        const textureHeight = this._charSize;\r\n\r\n        // Create the texture that will store the font characters.\r\n        this._texture = scene.getEngine().createDynamicTexture(textureWidth, textureHeight, false, Texture.NEAREST_SAMPLINGMODE);\r\n        //scene.getEngine().setclamp\r\n        const textureSize = this.getSize();\r\n\r\n        // Create a canvas with the final size: the one matching the texture.\r\n        const canvas = document.createElement(\"canvas\");\r\n        canvas.width = textureSize.width;\r\n        canvas.height = textureSize.height;\r\n        const context = <CanvasRenderingContext2D>canvas.getContext(\"2d\");\r\n        context.textBaseline = \"top\";\r\n        context.font = font;\r\n        context.fillStyle = \"white\";\r\n        context.imageSmoothingEnabled = false;\r\n\r\n        // Sets the text in the texture.\r\n        for (let i = 0; i < text.length; i++) {\r\n            context.fillText(text[i], i * this._charSize, -maxCharHeight.offset);\r\n        }\r\n\r\n        // Flush the text in the dynamic texture.\r\n\r\n        scene.getEngine().updateDynamicTexture(this._texture, canvas, false, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the max char width of a font.\r\n     * @param font the font to use, use the W3C CSS notation\r\n     * @returns the max char width\r\n     */\r\n    private _getFontWidth(font: string): number {\r\n        const fontDraw = document.createElement(\"canvas\");\r\n        const ctx = <CanvasRenderingContext2D>fontDraw.getContext(\"2d\");\r\n        ctx.fillStyle = \"white\";\r\n        ctx.font = font;\r\n\r\n        return ctx.measureText(\"W\").width;\r\n    }\r\n\r\n    // More info here: https://videlais.com/2014/03/16/the-many-and-varied-problems-with-measuring-font-height-for-html5-canvas/\r\n    /**\r\n     * Gets the max char height of a font.\r\n     * @param font the font to use, use the W3C CSS notation\r\n     * @returns the max char height\r\n     */\r\n    private _getFontHeight(font: string): { height: number; offset: number } {\r\n        const fontDraw = document.createElement(\"canvas\");\r\n        const ctx = <CanvasRenderingContext2D>fontDraw.getContext(\"2d\");\r\n        ctx.fillRect(0, 0, fontDraw.width, fontDraw.height);\r\n        ctx.textBaseline = \"top\";\r\n        ctx.fillStyle = \"white\";\r\n        ctx.font = font;\r\n        ctx.fillText(\"jH|\", 0, 0);\r\n        const pixels = ctx.getImageData(0, 0, fontDraw.width, fontDraw.height).data;\r\n        let start = -1;\r\n        let end = -1;\r\n        for (let row = 0; row < fontDraw.height; row++) {\r\n            for (let column = 0; column < fontDraw.width; column++) {\r\n                const index = (row * fontDraw.width + column) * 4;\r\n                if (pixels[index] === 0) {\r\n                    if (column === fontDraw.width - 1 && start !== -1) {\r\n                        end = row;\r\n                        row = fontDraw.height;\r\n                        break;\r\n                    }\r\n                    continue;\r\n                } else {\r\n                    if (start === -1) {\r\n                        start = row;\r\n                    }\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        return { height: end - start + 1, offset: start - 1 };\r\n    }\r\n\r\n    /**\r\n     * Clones the current AsciiArtTexture.\r\n     * @returns the clone of the texture.\r\n     */\r\n    public override clone(): AsciiArtFontTexture {\r\n        return new AsciiArtFontTexture(this.name, this._font, this._text, this.getScene());\r\n    }\r\n\r\n    /**\r\n     * Parses a json object representing the texture and returns an instance of it.\r\n     * @param source the source JSON representation\r\n     * @param scene the scene to create the texture for\r\n     * @returns the parsed texture\r\n     */\r\n    public static Parse(source: any, scene: Scene): AsciiArtFontTexture {\r\n        const texture = SerializationHelper.Parse(() => new AsciiArtFontTexture(source.name, source.font, source.text, scene), source, scene, null);\r\n\r\n        return texture;\r\n    }\r\n}\r\n\r\n/**\r\n * Option available in the Ascii Art Post Process.\r\n */\r\nexport interface IAsciiArtPostProcessOptions {\r\n    /**\r\n     * The font to use following the w3c font definition.\r\n     */\r\n    font?: string;\r\n\r\n    /**\r\n     * The character set to use in the postprocess.\r\n     */\r\n    characterSet?: string;\r\n\r\n    /**\r\n     * This defines the amount you want to mix the \"tile\" or caracter space colored in the ascii art.\r\n     * This number is defined between 0 and 1;\r\n     */\r\n    mixToTile?: number;\r\n\r\n    /**\r\n     * This defines the amount you want to mix the normal rendering pass in the ascii art.\r\n     * This number is defined between 0 and 1;\r\n     */\r\n    mixToNormal?: number;\r\n}\r\n\r\n/**\r\n * AsciiArtPostProcess helps rendering everithing in Ascii Art.\r\n *\r\n * Simmply add it to your scene and let the nerd that lives in you have fun.\r\n * Example usage: var pp = new AsciiArtPostProcess(\"myAscii\", \"20px Monospace\", camera);\r\n */\r\nexport class AsciiArtPostProcess extends PostProcess {\r\n    /**\r\n     * The font texture used to render the char in the post process.\r\n     */\r\n    private _asciiArtFontTexture: AsciiArtFontTexture;\r\n\r\n    /**\r\n     * This defines the amount you want to mix the \"tile\" or caracter space colored in the ascii art.\r\n     * This number is defined between 0 and 1;\r\n     */\r\n    public mixToTile: number = 0;\r\n\r\n    /**\r\n     * This defines the amount you want to mix the normal rendering pass in the ascii art.\r\n     * This number is defined between 0 and 1;\r\n     */\r\n    public mixToNormal: number = 0;\r\n\r\n    /**\r\n     * Instantiates a new Ascii Art Post Process.\r\n     * @param name the name to give to the postprocess\r\n     * @camera the camera to apply the post process to.\r\n     * @param camera\r\n     * @param options can either be the font name or an option object following the IAsciiArtPostProcessOptions format\r\n     */\r\n    constructor(name: string, camera: Nullable<Camera>, options?: string | IAsciiArtPostProcessOptions) {\r\n        super(name, \"asciiart\", [\"asciiArtFontInfos\", \"asciiArtOptions\"], [\"asciiArtFont\"], 1, camera, Texture.TRILINEAR_SAMPLINGMODE, undefined, true);\r\n\r\n        // Default values.\r\n        let font = \"40px Monospace\";\r\n        let characterSet = \" `-.'_:,\\\"=^;<+!*?/cL\\\\zrs7TivJtC{3F)Il(xZfY5S2eajo14[nuyE]P6V9kXpKwGhqAUbOd8#HRDB0$mgMW&Q%N@\";\r\n\r\n        // Use options.\r\n        if (options) {\r\n            if (typeof options === \"string\") {\r\n                font = options;\r\n            } else {\r\n                font = options.font || font;\r\n                characterSet = options.characterSet || characterSet;\r\n                this.mixToTile = options.mixToTile || this.mixToTile;\r\n                this.mixToNormal = options.mixToNormal || this.mixToNormal;\r\n            }\r\n        }\r\n\r\n        const scene = camera?.getScene() || this._scene;\r\n        this._asciiArtFontTexture = new AsciiArtFontTexture(name, font, characterSet, scene);\r\n        const textureSize = this._asciiArtFontTexture.getSize();\r\n\r\n        this.onApply = (effect: Effect) => {\r\n            effect.setTexture(\"asciiArtFont\", this._asciiArtFontTexture);\r\n\r\n            effect.setFloat4(\"asciiArtFontInfos\", this._asciiArtFontTexture.charSize, characterSet.length, textureSize.width, textureSize.height);\r\n\r\n            effect.setFloat4(\"asciiArtOptions\", this.width, this.height, this.mixToNormal, this.mixToTile);\r\n        };\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA,OAAO,EAAE,SAAS,EAAE,2CAA6B;AACjD,OAAO,EAAE,mBAAmB,EAAE,yDAA2C;AAEzE,OAAO,EAAE,WAAW,EAAE,0DAA4C;AAClE,OAAO,EAAE,OAAO,EAAE,sDAAwC;AAE1D,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAE7D,qEAAuD;AACvD,OAAO,qBAAqB,CAAC;;;;;;;;;AAQvB,MAAO,mBAAoB,wLAAQ,cAAW;IAShD;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IA6DD;;;;OAIG,CACK,aAAa,CAAC,IAAY,EAAA;QAC9B,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAClD,MAAM,GAAG,GAA6B,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAChE,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC;QACxB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAEhB,OAAO,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;IACtC,CAAC;IAED,4HAA4H;IAC5H;;;;OAIG,CACK,cAAc,CAAC,IAAY,EAAA;QAC/B,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAClD,MAAM,GAAG,GAA6B,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAChE,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QACpD,GAAG,CAAC,YAAY,GAAG,KAAK,CAAC;QACzB,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC;QACxB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;QAC5E,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;QACf,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QACb,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE,CAAE,CAAC;YAC7C,IAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAE,CAAC;gBACrD,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClD,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBACtB,IAAI,MAAM,KAAK,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;wBAChD,GAAG,GAAG,GAAG,CAAC;wBACV,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;wBACtB,MAAM;oBACV,CAAC;oBACD,SAAS;gBACb,CAAC,MAAM,CAAC;oBACJ,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;wBACf,KAAK,GAAG,GAAG,CAAC;oBAChB,CAAC;oBACD,MAAM;gBACV,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO;YAAE,MAAM,EAAE,GAAG,GAAG,KAAK,GAAG,CAAC;YAAE,MAAM,EAAE,KAAK,GAAG,CAAC;QAAA,CAAE,CAAC;IAC1D,CAAC;IAED;;;OAGG,CACa,KAAK,GAAA;QACjB,OAAO,IAAI,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACvF,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAA;QACzC,MAAM,OAAO,iLAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,mBAAmB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAE5I,OAAO,OAAO,CAAC;IACnB,CAAC;IAjID;;;;;;OAMG,CACH,YAAY,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,QAAyB,IAAI,CAAA;QAC/E,KAAK,CAAC,KAAK,CAAC,CAAC;QAEb,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAExB,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;QACnB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;QAEnB,IAAI,CAAC,KAAK,6KAAG,WAAO,CAAC,iBAAiB,CAAC;QACvC,IAAI,CAAC,KAAK,8KAAG,UAAO,CAAC,iBAAiB,CAAC;QACvC,qCAAqC;QAErC,8BAA8B;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAE9D,2FAA2F;QAC3F,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;QAErC,0DAA0D;QAC1D,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,oBAAoB,CAAC,YAAY,EAAE,aAAa,EAAE,KAAK,6KAAE,UAAO,CAAC,oBAAoB,CAAC,CAAC;QACzH,4BAA4B;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAEnC,qEAAqE;QACrE,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QACjC,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACnC,MAAM,OAAO,GAA6B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAClE,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;QAC7B,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC;QAC5B,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAEtC,gCAAgC;QAChC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACnC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACzE,CAAC;QAED,yCAAyC;QAEzC,KAAK,CAAC,SAAS,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/E,CAAC;CAyEJ;2JAhJW,aAAA,EAAA;qKADP,YAAS,AAAT,EAAU,MAAM,CAAC;kDACI;2JAGd,aAAA,EAAA;qKADP,YAAA,AAAS,EAAC,MAAM,CAAC;kDACI;AAgLpB,MAAO,mBAAoB,gLAAQ,cAAW;IAkBhD;;;;;;OAMG,CACH,YAAY,IAAY,EAAE,MAAwB,EAAE,OAA8C,CAAA;QAC9F,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE;YAAC,mBAAmB;YAAE,iBAAiB;SAAC,EAAE;YAAC,cAAc;SAAC,EAAE,CAAC,EAAE,MAAM,6KAAE,UAAO,CAAC,sBAAsB,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QApBpJ;;;WAGG,CACI,IAAA,CAAA,SAAS,GAAW,CAAC,CAAC;QAE7B;;;WAGG,CACI,IAAA,CAAA,WAAW,GAAW,CAAC,CAAC;QAY3B,kBAAkB;QAClB,IAAI,IAAI,GAAG,gBAAgB,CAAC;QAC5B,IAAI,YAAY,GAAG,+FAA+F,CAAC;QAEnH,eAAe;QACf,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAC9B,IAAI,GAAG,OAAO,CAAC;YACnB,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC;gBAC5B,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,YAAY,CAAC;gBACpD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;gBACrD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAC/D,CAAC;QACL,CAAC;QAED,MAAM,KAAK,oDAAG,MAAM,CAAE,QAAQ,EAAE,KAAI,IAAI,CAAC,MAAM,CAAC;QAChD,IAAI,CAAC,oBAAoB,GAAG,IAAI,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;QACrF,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;QAExD,IAAI,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAE7D,MAAM,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,YAAY,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAEtI,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACnG,CAAC,CAAC;IACN,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/asciiArt/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/asciiArt/index.ts"], "sourcesContent": ["export * from \"./asciiArtPostProcess\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,uBAAuB,CAAC", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/digitalRain/digitalrain.fragment.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/digitalRain/digitalrain.fragment.ts"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\n\nconst name = \"digitalrainPixelShader\";\nconst shader = `varying vec2 vUV;uniform sampler2D textureSampler;uniform sampler2D digitalRainFont;uniform vec4 digitalRainFontInfos;uniform vec4 digitalRainOptions;uniform mat4 matrixSpeed;uniform float cosTimeZeroOne;float getLuminance(vec3 color)\n{return clamp(dot(color,vec3(0.2126,0.7152,0.0722)),0.,1.);}\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) \n{float caracterSize=digitalRainFontInfos.x;float numChar=digitalRainFontInfos.y-1.0;float fontx=digitalRainFontInfos.z;float fonty=digitalRainFontInfos.w;float screenx=digitalRainOptions.x;float screeny=digitalRainOptions.y;float ratio=screeny/fonty;float columnx=float(floor((gl_FragCoord.x)/caracterSize));float tileX=float(floor((gl_FragCoord.x)/caracterSize))*caracterSize/screenx;float tileY=float(floor((gl_FragCoord.y)/caracterSize))*caracterSize/screeny;vec2 tileUV=vec2(tileX,tileY);vec4 tileColor=texture2D(textureSampler,tileUV);vec4 baseColor=texture2D(textureSampler,vUV);float tileLuminance=getLuminance(tileColor.rgb);int st=int(mod(columnx,4.0));float speed=cosTimeZeroOne*(sin(tileX*314.5)*0.5+0.6); \nfloat x=float(mod(gl_FragCoord.x,caracterSize))/fontx;float y=float(mod(speed+gl_FragCoord.y/screeny,1.0));y*=ratio;vec4 finalColor= texture2D(digitalRainFont,vec2(x,1.0-y));vec3 high=finalColor.rgb*(vec3(1.2,1.2,1.2)*pow(1.0-y,30.0));finalColor.rgb*=vec3(pow(tileLuminance,5.0),pow(tileLuminance,1.5),pow(tileLuminance,3.0));finalColor.rgb+=high;finalColor.rgb=clamp(finalColor.rgb,0.,1.);finalColor.a=1.0;finalColor= mix(finalColor,tileColor,digitalRainOptions.w);finalColor= mix(finalColor,baseColor,digitalRainOptions.z);gl_FragColor=finalColor;}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const digitalrainPixelShader = { name, shader };\n"], "names": [], "mappings": "AAAA,eAAe;;;;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;;AAEvD,MAAM,IAAI,GAAG,wBAAwB,CAAC;AACtC,MAAM,MAAM,GAAG;AAMf,aAAa;AACb,IAAI,kKAAC,cAAW,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;qKAClC,cAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC5C,CAAC;AAEM,MAAM,sBAAsB,GAAG;IAAE,IAAI;IAAE,MAAM;AAAA,CAAE,CAAC", "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/digitalRain/digitalRainPostProcess.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/digitalRain/digitalRainPostProcess.ts"], "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { serialize } from \"core/Misc/decorators\";\r\nimport { SerializationHelper } from \"core/Misc/decorators.serialization\";\r\nimport { Matrix } from \"core/Maths/math.vector\";\r\nimport type { Camera } from \"core/Cameras/camera\";\r\nimport { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\nimport type { Effect } from \"core/Materials/effect\";\r\nimport { PostProcess } from \"core/PostProcesses/postProcess\";\r\nimport type { Scene } from \"core/scene\";\r\nimport \"core/Engines/Extensions/engine.dynamicTexture\";\r\nimport \"./digitalrain.fragment\";\r\n\r\n/**\r\n * DigitalRainFontTexture is the helper class used to easily create your digital rain font texture.\r\n *\r\n * It basically takes care rendering the font front the given font size to a texture.\r\n * This is used later on in the postprocess.\r\n */\r\nexport class DigitalRainFontTexture extends BaseTexture {\r\n    @serialize(\"font\")\r\n    private _font: string;\r\n\r\n    @serialize(\"text\")\r\n    private _text: string;\r\n\r\n    private _charSize: number;\r\n\r\n    /**\r\n     * Gets the size of one char in the texture (each char fits in size * size space in the texture).\r\n     */\r\n    public get charSize(): number {\r\n        return this._charSize;\r\n    }\r\n\r\n    /**\r\n     * Create a new instance of the Digital Rain FontTexture class\r\n     * @param name the name of the texture\r\n     * @param font the font to use, use the W3C CSS notation\r\n     * @param text the caracter set to use in the rendering.\r\n     * @param scene the scene that owns the texture\r\n     */\r\n    constructor(name: string, font: string, text: string, scene: Nullable<Scene> = null) {\r\n        super(scene);\r\n\r\n        scene = this.getScene();\r\n\r\n        if (!scene) {\r\n            return;\r\n        }\r\n\r\n        this.name = name;\r\n        this._text == text;\r\n        this._font == font;\r\n\r\n        this.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n\r\n        // Get the font specific info.\r\n        const maxCharHeight = this._getFontHeight(font);\r\n        const maxCharWidth = this._getFontWidth(font);\r\n\r\n        this._charSize = Math.max(maxCharHeight.height, maxCharWidth);\r\n\r\n        // This is an approximate size, but should always be able to fit at least the maxCharCount.\r\n        const textureWidth = this._charSize;\r\n        const textureHeight = Math.ceil(this._charSize * text.length);\r\n\r\n        // Create the texture that will store the font characters.\r\n        this._texture = scene.getEngine().createDynamicTexture(textureWidth, textureHeight, false, Texture.NEAREST_SAMPLINGMODE);\r\n        //scene.getEngine().setclamp\r\n        const textureSize = this.getSize();\r\n\r\n        // Create a canvas with the final size: the one matching the texture.\r\n        const canvas = document.createElement(\"canvas\");\r\n        canvas.width = textureSize.width;\r\n        canvas.height = textureSize.height;\r\n        const context = <CanvasRenderingContext2D>canvas.getContext(\"2d\");\r\n        context.textBaseline = \"top\";\r\n        context.font = font;\r\n        context.fillStyle = \"white\";\r\n        context.imageSmoothingEnabled = false;\r\n\r\n        // Sets the text in the texture.\r\n        for (let i = 0; i < text.length; i++) {\r\n            context.fillText(text[i], 0, i * this._charSize - maxCharHeight.offset);\r\n        }\r\n\r\n        // Flush the text in the dynamic texture.\r\n        scene.getEngine().updateDynamicTexture(this._texture, canvas, false, true);\r\n    }\r\n\r\n    /**\r\n     * Gets the max char width of a font.\r\n     * @param font the font to use, use the W3C CSS notation\r\n     * @returns the max char width\r\n     */\r\n    private _getFontWidth(font: string): number {\r\n        const fontDraw = document.createElement(\"canvas\");\r\n        const ctx = <CanvasRenderingContext2D>fontDraw.getContext(\"2d\");\r\n        ctx.fillStyle = \"white\";\r\n        ctx.font = font;\r\n\r\n        return ctx.measureText(\"W\").width;\r\n    }\r\n\r\n    // More info here: https://videlais.com/2014/03/16/the-many-and-varied-problems-with-measuring-font-height-for-html5-canvas/\r\n    /**\r\n     * Gets the max char height of a font.\r\n     * @param font the font to use, use the W3C CSS notation\r\n     * @returns the max char height\r\n     */\r\n    private _getFontHeight(font: string): { height: number; offset: number } {\r\n        const fontDraw = document.createElement(\"canvas\");\r\n        const ctx = <CanvasRenderingContext2D>fontDraw.getContext(\"2d\");\r\n        ctx.fillRect(0, 0, fontDraw.width, fontDraw.height);\r\n        ctx.textBaseline = \"top\";\r\n        ctx.fillStyle = \"white\";\r\n        ctx.font = font;\r\n        ctx.fillText(\"jH|\", 0, 0);\r\n        const pixels = ctx.getImageData(0, 0, fontDraw.width, fontDraw.height).data;\r\n        let start = -1;\r\n        let end = -1;\r\n        for (let row = 0; row < fontDraw.height; row++) {\r\n            for (let column = 0; column < fontDraw.width; column++) {\r\n                const index = (row * fontDraw.width + column) * 4;\r\n                if (pixels[index] === 0) {\r\n                    if (column === fontDraw.width - 1 && start !== -1) {\r\n                        end = row;\r\n                        row = fontDraw.height;\r\n                        break;\r\n                    }\r\n                    continue;\r\n                } else {\r\n                    if (start === -1) {\r\n                        start = row;\r\n                    }\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        return { height: end - start + 1, offset: start - 1 };\r\n    }\r\n\r\n    /**\r\n     * Clones the current DigitalRainFontTexture.\r\n     * @returns the clone of the texture.\r\n     */\r\n    public override clone(): DigitalRainFontTexture {\r\n        return new DigitalRainFontTexture(this.name, this._font, this._text, this.getScene());\r\n    }\r\n\r\n    /**\r\n     * Parses a json object representing the texture and returns an instance of it.\r\n     * @param source the source JSON representation\r\n     * @param scene the scene to create the texture for\r\n     * @returns the parsed texture\r\n     */\r\n    public static Parse(source: any, scene: Scene): DigitalRainFontTexture {\r\n        const texture = SerializationHelper.Parse(() => new DigitalRainFontTexture(source.name, source.font, source.text, scene), source, scene, null);\r\n\r\n        return texture;\r\n    }\r\n}\r\n\r\n/**\r\n * Option available in the Digital Rain Post Process.\r\n */\r\nexport interface IDigitalRainPostProcessOptions {\r\n    /**\r\n     * The font to use following the w3c font definition.\r\n     */\r\n    font?: string;\r\n\r\n    /**\r\n     * This defines the amount you want to mix the \"tile\" or caracter space colored in the digital rain.\r\n     * This number is defined between 0 and 1;\r\n     */\r\n    mixToTile?: number;\r\n\r\n    /**\r\n     * This defines the amount you want to mix the normal rendering pass in the digital rain.\r\n     * This number is defined between 0 and 1;\r\n     */\r\n    mixToNormal?: number;\r\n}\r\n\r\n/**\r\n * DigitalRainPostProcess helps rendering everithing in digital rain.\r\n *\r\n * Simmply add it to your scene and let the nerd that lives in you have fun.\r\n * Example usage: var pp = new DigitalRainPostProcess(\"digitalRain\", \"20px Monospace\", camera);\r\n */\r\nexport class DigitalRainPostProcess extends PostProcess {\r\n    /**\r\n     * The font texture used to render the char in the post process.\r\n     */\r\n    private _digitalRainFontTexture: DigitalRainFontTexture;\r\n\r\n    /**\r\n     * This defines the amount you want to mix the \"tile\" or caracter space colored in the digital rain.\r\n     * This number is defined between 0 and 1;\r\n     */\r\n    public mixToTile: number = 0;\r\n\r\n    /**\r\n     * This defines the amount you want to mix the normal rendering pass in the digital rain.\r\n     * This number is defined between 0 and 1;\r\n     */\r\n    public mixToNormal: number = 0;\r\n\r\n    /**\r\n     * Speed of the effect\r\n     */\r\n    public speed: number = 0.003;\r\n\r\n    /**\r\n     * Instantiates a new Digital Rain Post Process.\r\n     * @param name the name to give to the postprocess\r\n     * @camera the camera to apply the post process to.\r\n     * @param camera\r\n     * @param options can either be the font name or an option object following the IDigitalRainPostProcessOptions format\r\n     */\r\n    constructor(name: string, camera: Nullable<Camera>, options?: string | IDigitalRainPostProcessOptions) {\r\n        super(\r\n            name,\r\n            \"digitalrain\",\r\n            [\"digitalRainFontInfos\", \"digitalRainOptions\", \"cosTimeZeroOne\", \"matrixSpeed\"],\r\n            [\"digitalRainFont\"],\r\n            1.0,\r\n            camera,\r\n            Texture.TRILINEAR_SAMPLINGMODE,\r\n            undefined,\r\n            true\r\n        );\r\n\r\n        // Default values.\r\n        let font = \"15px Monospace\";\r\n        const characterSet =\r\n            \"古池や蛙飛び込む水の音ふるいけやかわずとびこむみずのおと初しぐれ猿も小蓑をほしげ也はつしぐれさるもこみのをほしげなり江戸の雨何石呑んだ時鳥えどのあめなんごくのんだほととぎす\";\r\n\r\n        // Use options.\r\n        if (options) {\r\n            if (typeof options === \"string\") {\r\n                font = options;\r\n            } else {\r\n                font = options.font || font;\r\n                this.mixToTile = options.mixToTile || this.mixToTile;\r\n                this.mixToNormal = options.mixToNormal || this.mixToNormal;\r\n            }\r\n        }\r\n\r\n        const scene = camera?.getScene() || null;\r\n        this._digitalRainFontTexture = new DigitalRainFontTexture(name, font, characterSet, scene);\r\n        const textureSize = this._digitalRainFontTexture.getSize();\r\n\r\n        let alpha = 0.0;\r\n        let cosTimeZeroOne = 0.0;\r\n        const matrix = Matrix.FromValues(\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random(),\r\n            Math.random()\r\n        );\r\n\r\n        this.onApply = (effect: Effect) => {\r\n            effect.setTexture(\"digitalRainFont\", this._digitalRainFontTexture);\r\n\r\n            effect.setFloat4(\"digitalRainFontInfos\", this._digitalRainFontTexture.charSize, characterSet.length, textureSize.width, textureSize.height);\r\n\r\n            effect.setFloat4(\"digitalRainOptions\", this.width, this.height, this.mixToNormal, this.mixToTile);\r\n\r\n            effect.setMatrix(\"matrixSpeed\", matrix);\r\n\r\n            alpha += this.speed;\r\n            cosTimeZeroOne = alpha;\r\n            effect.setFloat(\"cosTimeZeroOne\", cosTimeZeroOne);\r\n        };\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA,OAAO,EAAE,SAAS,EAAE,2CAA6B;AACjD,OAAO,EAAE,mBAAmB,EAAE,yDAA2C;AACzE,OAAO,EAAE,MAAM,EAAE,6CAA+B;AAEhD,OAAO,EAAE,WAAW,EAAE,0DAA4C;AAClE,OAAO,EAAE,OAAO,EAAE,sDAAwC;AAE1D,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAE7D,qEAAuD;AACvD,OAAO,wBAAwB,CAAC;;;;;;;;;;AAQ1B,MAAO,sBAAuB,wLAAQ,cAAW;IASnD;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IA2DD;;;;OAIG,CACK,aAAa,CAAC,IAAY,EAAA;QAC9B,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAClD,MAAM,GAAG,GAA6B,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAChE,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC;QACxB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAEhB,OAAO,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;IACtC,CAAC;IAED,4HAA4H;IAC5H;;;;OAIG,CACK,cAAc,CAAC,IAAY,EAAA;QAC/B,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAClD,MAAM,GAAG,GAA6B,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAChE,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QACpD,GAAG,CAAC,YAAY,GAAG,KAAK,CAAC;QACzB,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC;QACxB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1B,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;QAC5E,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;QACf,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QACb,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE,CAAE,CAAC;YAC7C,IAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAE,CAAC;gBACrD,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClD,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;oBACtB,IAAI,MAAM,KAAK,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;wBAChD,GAAG,GAAG,GAAG,CAAC;wBACV,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;wBACtB,MAAM;oBACV,CAAC;oBACD,SAAS;gBACb,CAAC,MAAM,CAAC;oBACJ,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;wBACf,KAAK,GAAG,GAAG,CAAC;oBAChB,CAAC;oBACD,MAAM;gBACV,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO;YAAE,MAAM,EAAE,GAAG,GAAG,KAAK,GAAG,CAAC;YAAE,MAAM,EAAE,KAAK,GAAG,CAAC;QAAA,CAAE,CAAC;IAC1D,CAAC;IAED;;;OAGG,CACa,KAAK,GAAA;QACjB,OAAO,IAAI,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,KAAK,CAAC,MAAW,EAAE,KAAY,EAAA;QACzC,MAAM,OAAO,iLAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,sBAAsB,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAE/I,OAAO,OAAO,CAAC;IACnB,CAAC;IA/HD;;;;;;OAMG,CACH,YAAY,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,QAAyB,IAAI,CAAA;QAC/E,KAAK,CAAC,KAAK,CAAC,CAAC;QAEb,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAExB,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;QACnB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;QAEnB,IAAI,CAAC,KAAK,8KAAG,UAAO,CAAC,iBAAiB,CAAC;QACvC,IAAI,CAAC,KAAK,8KAAG,UAAO,CAAC,iBAAiB,CAAC;QAEvC,8BAA8B;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE9C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAE9D,2FAA2F;QAC3F,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC;QACpC,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAE9D,0DAA0D;QAC1D,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,oBAAoB,CAAC,YAAY,EAAE,aAAa,EAAE,KAAK,EAAE,qLAAO,CAAC,oBAAoB,CAAC,CAAC;QACzH,4BAA4B;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAEnC,qEAAqE;QACrE,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QACjC,MAAM,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACnC,MAAM,OAAO,GAA6B,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAClE,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;QAC7B,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC;QAC5B,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAEtC,gCAAgC;QAChC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACnC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QAC5E,CAAC;QAED,yCAAyC;QACzC,KAAK,CAAC,SAAS,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/E,CAAC;CAyEJ;2JA9IW,aAAA,EAAA;qKADP,YAAA,AAAS,EAAC,MAAM,CAAC;qDACI;CAGd,uKAAA,EAAA;qKADP,YAAA,AAAS,EAAC,MAAM,CAAC;qDACI;AAyKpB,MAAO,sBAAuB,gLAAQ,cAAW;IAuBnD;;;;;;OAMG,CACH,YAAY,IAAY,EAAE,MAAwB,EAAE,OAAiD,CAAA;QACjG,KAAK,CACD,IAAI,EACJ,aAAa,EACb;YAAC,sBAAsB;YAAE,oBAAoB;YAAE,gBAAgB;YAAE,aAAa;SAAC,EAC/E;YAAC,iBAAiB;SAAC,EACnB,GAAG,EACH,MAAM,6KACN,UAAO,CAAC,sBAAsB,EAC9B,SAAS,EACT,IAAI,CACP,CAAC;QAnCN;;;WAGG,CACI,IAAA,CAAA,SAAS,GAAW,CAAC,CAAC;QAE7B;;;WAGG,CACI,IAAA,CAAA,WAAW,GAAW,CAAC,CAAC;QAE/B;;WAEG,CACI,IAAA,CAAA,KAAK,GAAW,KAAK,CAAC;QAsBzB,kBAAkB;QAClB,IAAI,IAAI,GAAG,gBAAgB,CAAC;QAC5B,MAAM,YAAY,GACd,wFAAwF,CAAC;QAE7F,eAAe;QACf,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAC9B,IAAI,GAAG,OAAO,CAAC;YACnB,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC;gBAC5B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;gBACrD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAC/D,CAAC;QACL,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,kDAAE,QAAQ,EAAE,KAAI,IAAI,CAAC;QACzC,IAAI,CAAC,uBAAuB,GAAG,IAAI,sBAAsB,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;QAC3F,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;QAE3D,IAAI,KAAK,GAAG,GAAG,CAAC;QAChB,IAAI,cAAc,GAAG,GAAG,CAAC;QACzB,MAAM,MAAM,qKAAG,SAAM,CAAC,UAAU,CAC5B,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,EACb,IAAI,CAAC,MAAM,EAAE,CAChB,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;YAC9B,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAEnE,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,YAAY,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAE5I,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAElG,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;YAExC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;YACpB,cAAc,GAAG,KAAK,CAAC;YACvB,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;QACtD,CAAC,CAAC;IACN,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/digitalRain/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/digitalRain/index.ts"], "sourcesContent": ["export * from \"./digitalRainPostProcess\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,0BAA0B,CAAC", "debugId": null}}, {"offset": {"line": 1216, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/edgeDetection/edgeDetection.fragment.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/edgeDetection/edgeDetection.fragment.ts"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"core/Engines/shaderStore\";\n\nconst name = \"edgeDetectionPixelShader\";\nconst shader = `precision highp float;varying vec2 vUV;uniform sampler2D textureSampler;uniform sampler2D normalSampler;uniform sampler2D depthSampler;uniform float width;uniform float height;uniform vec3 edgeColor;uniform float edgeIntensity;uniform float edgeWidth;uniform int renderMode; \nvec3 boxBlur(sampler2D sampler,vec2 uv,vec2 texelSize) {vec3 result=vec3(0.0);for (int x=-1; x<=1; x++) {for (int y=-1; y<=1; y++) {vec2 offset=vec2(float(x),float(y))*texelSize;result+=texture2D(sampler,uv+offset).rgb;}}\nreturn result/9.0;}\nvoid main(void) {vec2 texelSize=vec2(1.0/width,1.0/height);vec3 originalColor=texture2D(textureSampler,vUV).rgb;if (renderMode==1 || renderMode==2 || renderMode==3) {if (length(originalColor)==0.0) {originalColor=vec3(1.0,1.0,1.0); }\nif (originalColor.r==1.0 && originalColor.g==0.0 && originalColor.b==0.0) {originalColor=vec3(1.0,1.0,1.0); }}\nvec3 normal=texture2D(normalSampler,vUV).rgb;float depth=texture2D(depthSampler,vUV).r;float edgeStrength=0.0;int range=int(edgeWidth*8.0); \nfor (int x=-range; x<=range; x++) {for (int y=-range; y<=range; y++) {if (x==0 && y==0) {continue;}\nvec3 neighborNormal=texture2D(normalSampler,vUV+texelSize*vec2(float(x),float(y))).rgb;float neighborDepth=texture2D(depthSampler,vUV+texelSize*vec2(float(x),float(y))).r;float normalDiff=length(neighborNormal-normal);float depthDiff=abs(neighborDepth-depth);edgeStrength=max(edgeStrength,max(normalDiff,depthDiff));}}\nedgeStrength=smoothstep(edgeWidth,edgeWidth+edgeIntensity,edgeStrength);vec3 finalColor=mix(originalColor,edgeColor,edgeStrength);gl_FragColor=vec4(finalColor,1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const edgeDetectionPixelShader = { name, shader };\n"], "names": [], "mappings": "AAAA,eAAe;;;;AACf,OAAO,EAAE,WAAW,EAAE,+CAAiC;;AAEvD,MAAM,IAAI,GAAG,0BAA0B,CAAC;AACxC,MAAM,MAAM,GAAG;AASf,aAAa;AACb,IAAI,kKAAC,cAAW,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;qKAClC,cAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAC5C,CAAC;AAEM,MAAM,wBAAwB,GAAG;IAAE,IAAI;IAAE,MAAM;AAAA,CAAE,CAAC", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/edgeDetection/edgeDetectionPostProcess.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/edgeDetection/edgeDetectionPostProcess.ts"], "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport type { Camera } from \"core/Cameras/camera\";\r\nimport type { Effect } from \"core/Materials/effect\";\r\nimport type { PostProcessOptions } from \"core/PostProcesses/postProcess\";\r\nimport { PostProcess } from \"core/PostProcesses/postProcess\";\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport \"core/Rendering/geometryBufferRendererSceneComponent\";\r\nimport type { GeometryBufferRenderer } from \"core/Rendering/geometryBufferRenderer\";\r\nimport { Color3 } from \"core/Maths/math.color\";\r\nimport { serialize } from \"core/Misc/decorators\";\r\nimport { SerializationHelper } from \"core/Misc/decorators.serialization\";\r\nimport { RegisterClass } from \"core/Misc/typeStore\";\r\nimport { EngineStore } from \"core/Engines/engineStore\";\r\nimport { RenderTargetTexture } from \"core/Materials/Textures/renderTargetTexture\";\r\nimport type { Scene } from \"core/scene\";\r\nimport \"./edgeDetection.fragment\";\r\n\r\n/**\r\n * The Edge Detection effect highlights the edges of objects in the scene like a toon.\r\n * This can be used for stylized rendering, outlining, or visual effects that require edge enhancement.\r\n */\r\nexport class EdgeDetectionPostProcess extends PostProcess {\r\n    /**\r\n     * Defines the color of the detected edges.\r\n     */\r\n    @serialize()\r\n    public edgeColor: Color3 = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * Defines the intensity of the detected edges.\r\n     * Higher values result in more pronounced edges.\r\n     * default: 0.2  (min:0, max:1)\r\n     */\r\n    @serialize()\r\n    public edgeIntensity: number = 0.2;\r\n\r\n    /**\r\n     * Defines the width of the detected edges.\r\n     * Higher values result in thicker edges.\r\n     * default: 0.2 (min:0.125, max:1)\r\n     */\r\n    @serialize()\r\n    public edgeWidth: number = 0.2;\r\n\r\n    /**\r\n     * Defines the render mode.\r\n     * default: 0\r\n     * 0: general, 1: normal, 2: depth, 3: outline only\r\n     */\r\n    @serialize()\r\n    public renderMode: number = 0;\r\n\r\n    private _geometryBufferRenderer: Nullable<GeometryBufferRenderer>;\r\n\r\n    /**\r\n     * Get the current class name of the current effect\r\n     * @returns \"EdgeDetectionPostProcess\"\r\n     */\r\n    public override getClassName(): string {\r\n        return \"EdgeDetectionPostProcess\";\r\n    }\r\n\r\n    /**\r\n     * Creates a new instance of EdgeDetectionPostProcess.\r\n     * @param name The name of the effect.\r\n     * @param scene The scene where the edge detection post-process will be applied.\r\n     * @param options The required width/height ratio or specific options for the post-process.\r\n     * @param camera The camera to apply the post-process to.\r\n     * @param samplingMode The sampling mode to be used when computing the pass. (default: TEXTURE_NEAREST_NEAREST)\r\n     * @param reusable If the post-process can be reused on the same frame. (default: false)\r\n     * @param textureType The type of textures used when performing the post-process. (default: TEXTURETYPE_HALF_FLOAT)\r\n     */\r\n    constructor(\r\n        name: string,\r\n        scene: Scene,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera>,\r\n        samplingMode?: number,\r\n        reusable?: boolean,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE\r\n    ) {\r\n        super(\r\n            name,\r\n            \"edgeDetection\",\r\n            [\"width\", \"height\", \"edgeColor\", \"edgeIntensity\", \"edgeWidth\", \"renderMode\"],\r\n            [\"normalSampler\", \"depthSampler\"],\r\n            options,\r\n            camera,\r\n            samplingMode,\r\n            scene.getEngine(),\r\n            reusable,\r\n            null,\r\n            textureType\r\n        );\r\n\r\n        this._geometryBufferRenderer = scene.enableGeometryBufferRenderer();\r\n\r\n        if (!this._geometryBufferRenderer) {\r\n            // Geometry buffer renderer is not supported. So, work as a passthrough.\r\n            Logger.Error(\"Geometry Buffer Renderer support is required for this post-process.\");\r\n        } else {\r\n            const h1 = new RenderTargetTexture(\"h1\", { width: this.width, height: this.height }, scene, {\r\n                samplingMode: Constants.TEXTURE_NEAREST_NEAREST,\r\n                generateMipMaps: false,\r\n                generateDepthBuffer: false,\r\n                type: Constants.TEXTURETYPE_HALF_FLOAT,\r\n            });\r\n\r\n            // Geometry buffer renderer is supported.\r\n            this.onApply = (effect: Effect) => {\r\n                effect.setFloat(\"width\", this.width);\r\n                effect.setFloat(\"height\", this.height);\r\n                effect.setFloat(\"edgeIntensity\", this.edgeIntensity);\r\n                effect.setFloat(\"edgeWidth\", this.edgeWidth);\r\n                effect.setColor3(\"edgeColor\", this.edgeColor);\r\n\r\n                const normalTexture = this._geometryBufferRenderer!.getGBuffer().textures[1];\r\n                const depthTexture = this._geometryBufferRenderer!.getGBuffer().textures[0];\r\n\r\n                effect.setTexture(\"normalSampler\", normalTexture);\r\n                effect.setTexture(\"depthSampler\", depthTexture);\r\n\r\n                switch (this.renderMode) {\r\n                    case 0:\r\n                        break;\r\n                    case 1:\r\n                        effect.setTexture(\"textureSampler\", this._geometryBufferRenderer!.getGBuffer().textures[1]);\r\n                        effect.setFloat(\"edgeWidth\", 0);\r\n                        break;\r\n                    case 2:\r\n                        effect.setTexture(\"textureSampler\", this._geometryBufferRenderer!.getGBuffer().textures[0]);\r\n                        effect.setFloat(\"edgeWidth\", 0);\r\n                        break;\r\n                    case 3:\r\n                        effect.setTexture(\"textureSampler\", h1);\r\n                        break;\r\n                }\r\n                effect.setInt(\"renderMode\", this.renderMode);\r\n            };\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Support test.\r\n     */\r\n    public static get IsSupported(): boolean {\r\n        const engine = EngineStore.LastCreatedEngine;\r\n        if (!engine) {\r\n            return false;\r\n        }\r\n\r\n        return engine.getCaps().drawBuffersExtension;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static override _Parse(parsedPostProcess: any, targetCamera: Camera, scene: Scene, rootUrl: string) {\r\n        return SerializationHelper.Parse(\r\n            () =>\r\n                new EdgeDetectionPostProcess(\r\n                    parsedPostProcess.name,\r\n                    scene,\r\n                    parsedPostProcess.options,\r\n                    targetCamera,\r\n                    parsedPostProcess.renderTargetSamplingMode,\r\n                    parsedPostProcess.textureType,\r\n                    parsedPostProcess.reusable\r\n                ),\r\n            parsedPostProcess,\r\n            scene,\r\n            rootUrl\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.EdgeDetectionPostProcess\", EdgeDetectionPostProcess);\r\n"], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,MAAM,EAAE,uCAAyB;AAI1C,OAAO,EAAE,WAAW,EAAE,qDAAuC;AAC7D,OAAO,EAAE,SAAS,EAAE,6CAA+B;AACnD,2EAA6D;AAE7D,OAAO,EAAE,MAAM,EAAE,4CAA8B;AAC/C,OAAO,EAAE,SAAS,EAAE,2CAA6B;AACjD,OAAO,EAAE,mBAAmB,EAAE,yDAA2C;AACzE,OAAO,EAAE,aAAa,EAAE,0CAA4B;AACpD,OAAO,EAAE,WAAW,EAAE,+CAAiC;AACvD,OAAO,EAAE,mBAAmB,EAAE,kEAAoD;AAElF,OAAO,0BAA0B,CAAC;;;;;;;;;;;;;AAM5B,MAAO,wBAAyB,gLAAQ,cAAW;IAiCrD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,0BAA0B,CAAC;IACtC,CAAC;IAkFD;;OAEG,CACI,MAAM,KAAK,WAAW,GAAA;QACzB,MAAM,MAAM,oKAAG,cAAW,CAAC,iBAAiB,CAAC;QAC7C,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC,oBAAoB,CAAC;IACjD,CAAC;IAED;;OAEG,CACI,MAAM,CAAU,MAAM,CAAC,iBAAsB,EAAE,YAAoB,EAAE,KAAY,EAAE,OAAe,EAAA;QACrG,OAAO,oMAAmB,CAAC,KAAK,CAC5B,GAAG,CACC,CADC,GACG,wBAAwB,CACxB,iBAAiB,CAAC,IAAI,EACtB,KAAK,EACL,iBAAiB,CAAC,OAAO,EACzB,YAAY,EACZ,iBAAiB,CAAC,wBAAwB,EAC1C,iBAAiB,CAAC,WAAW,EAC7B,iBAAiB,CAAC,QAAQ,CAC7B,EACL,iBAAiB,EACjB,KAAK,EACL,OAAO,CACV,CAAC;IACN,CAAC;IA/GD;;;;;;;;;OASG,CACH,YACI,IAAY,EACZ,KAAY,EACZ,OAAoC,EACpC,MAAwB,EACxB,YAAqB,EACrB,QAAkB,EAClB,6KAAsB,YAAS,CAAC,yBAAyB,CAAA;QAEzD,KAAK,CACD,IAAI,EACJ,eAAe,EACf;YAAC,OAAO;YAAE,QAAQ;YAAE,WAAW;YAAE,eAAe;YAAE,WAAW;YAAE,YAAY;SAAC,EAC5E;YAAC,eAAe;YAAE,cAAc;SAAC,EACjC,OAAO,EACP,MAAM,EACN,YAAY,EACZ,KAAK,CAAC,SAAS,EAAE,EACjB,QAAQ,EACR,IAAI,EACJ,WAAW,CACd,CAAC;QAvEN;;WAEG,CAEI,IAAA,CAAA,SAAS,GAAW,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE/C;;;;WAIG,CAEI,IAAA,CAAA,aAAa,GAAW,GAAG,CAAC;QAEnC;;;;WAIG,CAEI,IAAA,CAAA,SAAS,GAAW,GAAG,CAAC;QAE/B;;;;WAIG,CAEI,IAAA,CAAA,UAAU,GAAW,CAAC,CAAC;QA6C1B,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC,4BAA4B,EAAE,CAAC;QAEpE,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,wEAAwE;qKACxE,SAAM,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC;QACxF,CAAC,MAAM,CAAC;YACJ,MAAM,EAAE,GAAG,2LAAI,sBAAmB,CAAC,IAAI,EAAE;gBAAE,KAAK,EAAE,IAAI,CAAC,KAAK;gBAAE,MAAM,EAAE,IAAI,CAAC,MAAM;YAAA,CAAE,EAAE,KAAK,EAAE;gBACxF,YAAY,iKAAE,YAAS,CAAC,uBAAuB;gBAC/C,eAAe,EAAE,KAAK;gBACtB,mBAAmB,EAAE,KAAK;gBAC1B,IAAI,iKAAE,YAAS,CAAC,sBAAsB;aACzC,CAAC,CAAC;YAEH,yCAAyC;YACzC,IAAI,CAAC,OAAO,GAAG,CAAC,MAAc,EAAE,EAAE;gBAC9B,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvC,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBACrD,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7C,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBAE9C,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAwB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC7E,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAwB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAE5E,MAAM,CAAC,UAAU,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;gBAClD,MAAM,CAAC,UAAU,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;gBAEhD,OAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;oBACtB,KAAK,CAAC;wBACF,MAAM;oBACV,KAAK,CAAC;wBACF,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAwB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5F,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;wBAChC,MAAM;oBACV,KAAK,CAAC;wBACF,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,uBAAwB,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5F,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;wBAChC,MAAM;oBACV,KAAK,CAAC;wBACF,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;wBACxC,MAAM;gBACd,CAAC;gBACD,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACjD,CAAC,CAAC;QACN,CAAC;IACL,CAAC;CAkCJ;AApJU,wKAAA,EAAA;qKADN,YAAA,AAAS,EAAE;2DACmC;AAQxC,wKAAA,EAAA;qKADN,YAAA,AAAS,EAAE;+DACuB;2JAQ5B,aAAA,EAAA;qKADN,YAAA,AAAS,EAAE;2DACmB;2JAQxB,aAAA,EAAA;IADN,6KAAA,AAAS,EAAE;4DACkB;gKA8HlC,gBAAA,AAAa,EAAC,kCAAkC,EAAE,wBAAwB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/edgeDetection/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/edgeDetection/index.ts"], "sourcesContent": ["export * from \"./edgeDetectionPostProcess\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,4BAA4B,CAAC", "debugId": null}}, {"offset": {"line": 1399, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/post-processes/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/postProcesses/src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-restricted-imports */\r\nexport * from \"./asciiArt/index\";\r\nexport * from \"./digitalRain/index\";\r\nexport * from \"./edgeDetection/index\";\r\n"], "names": [], "mappings": "AAAA,2DAAA,EAA6D;AAC7D,cAAc,kBAAkB,CAAC;AACjC,cAAc,qBAAqB,CAAC;AACpC,cAAc,uBAAuB,CAAC", "debugId": null}}]}