{"version": 3, "file": "engine.videoTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.videoTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAGtD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAgBzC,UAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,OAAkC,EAAE,KAAuB,EAAE,OAAgB;IAC7H,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QAClC,OAAO;IACX,CAAC;IAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACzD,MAAM,cAAc,GAAG,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,yBAAyB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IAEnH,MAAM,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACzF,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,mCAAmC;IAEhE,IAAI,CAAC;QACD,gCAAgC;QAChC,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAC5C,iCAAiC;YACjC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAEpB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAErG,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACvC,CAAC;QACL,CAAC;QAED,kFAAkF;QAClF,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC1B,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC1E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAExD,IAAI,CAAC,OAAO,EAAE,CAAC;oBACX,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAChD,CAAC;gBAED,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC;gBAClC,OAAO,CAAC,cAAc,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACnD,CAAC;YAED,OAAO,CAAC,eAAgB,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACxE,OAAO,CAAC,eAAgB,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAE1H,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,cAAgC,CAAC,CAAC;QAC5I,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACzG,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC;QACD,+BAA+B;QAC/B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IAC3B,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACV,uBAAuB;QACvB,4BAA4B;QAC5B,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAC/B,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Constants } from \"../constants\";\r\nimport type { ExternalTexture } from \"../../Materials/Textures/externalTexture\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Update a video texture\r\n         * @param texture defines the texture to update\r\n         * @param video defines the video element to use\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateVideoTexture(texture: Nullable<InternalTexture>, video: HTMLVideoElement | Nullable<ExternalTexture>, invertY: boolean): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.updateVideoTexture = function (texture: Nullable<InternalTexture>, video: HTMLVideoElement, invertY: boolean): void {\r\n    if (!texture || texture._isDisabled) {\r\n        return;\r\n    }\r\n\r\n    const glformat = this._getInternalFormat(texture.format);\r\n    const internalFormat = this._getRGBABufferInternalSizedFormat(Constants.TEXTURETYPE_UNSIGNED_BYTE, texture.format);\r\n\r\n    const wasPreviouslyBound = this._bindTextureDirectly(this._gl.TEXTURE_2D, texture, true);\r\n    this._unpackFlipY(!invertY); // Video are upside down by default\r\n\r\n    try {\r\n        // Testing video texture support\r\n        if (this._videoTextureSupported === undefined) {\r\n            // clear old errors just in case.\r\n            this._gl.getError();\r\n\r\n            this._gl.texImage2D(this._gl.TEXTURE_2D, 0, internalFormat, glformat, this._gl.UNSIGNED_BYTE, video);\r\n\r\n            if (this._gl.getError() !== 0) {\r\n                this._videoTextureSupported = false;\r\n            } else {\r\n                this._videoTextureSupported = true;\r\n            }\r\n        }\r\n\r\n        // Copy video through the current working canvas if video texture is not supported\r\n        if (!this._videoTextureSupported) {\r\n            if (!texture._workingCanvas) {\r\n                texture._workingCanvas = this.createCanvas(texture.width, texture.height);\r\n                const context = texture._workingCanvas.getContext(\"2d\");\r\n\r\n                if (!context) {\r\n                    throw new Error(\"Unable to get 2d context\");\r\n                }\r\n\r\n                texture._workingContext = context;\r\n                texture._workingCanvas.width = texture.width;\r\n                texture._workingCanvas.height = texture.height;\r\n            }\r\n\r\n            texture._workingContext!.clearRect(0, 0, texture.width, texture.height);\r\n            texture._workingContext!.drawImage(video, 0, 0, video.videoWidth, video.videoHeight, 0, 0, texture.width, texture.height);\r\n\r\n            this._gl.texImage2D(this._gl.TEXTURE_2D, 0, internalFormat, glformat, this._gl.UNSIGNED_BYTE, texture._workingCanvas as TexImageSource);\r\n        } else {\r\n            this._gl.texImage2D(this._gl.TEXTURE_2D, 0, internalFormat, glformat, this._gl.UNSIGNED_BYTE, video);\r\n        }\r\n\r\n        if (texture.generateMipMaps) {\r\n            this._gl.generateMipmap(this._gl.TEXTURE_2D);\r\n        }\r\n\r\n        if (!wasPreviouslyBound) {\r\n            this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n        }\r\n        //    this.resetTextureCache();\r\n        texture.isReady = true;\r\n    } catch (ex) {\r\n        // Something unexpected\r\n        // Let's disable the texture\r\n        texture._isDisabled = true;\r\n    }\r\n};\r\n"]}