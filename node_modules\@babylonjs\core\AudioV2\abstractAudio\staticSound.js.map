{"version": 3, "file": "staticSound.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/abstractAudio/staticSound.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AA0FhD;;;;;;;;;;;GAWG;AACH,MAAM,OAAgB,WAAY,SAAQ,aAAa;IAWnD,YAAsB,IAAY,EAAE,MAAqB;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa;QAC7B,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IACnC,CAAC;IAED,IAAW,SAAS,CAAC,KAAa;QAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;IACpC,CAAC;IAED;;;OAGG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;IACjC,CAAC;IAED,IAAW,OAAO,CAAC,KAAa;QAC5B,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;IAC/B,CAAC;IAED,IAAW,KAAK,CAAC,KAAa;QAC1B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;QAE5B,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,KAAK,IAAI,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;YAClE,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;IACtC,CAAC;IAED,IAAW,YAAY,CAAC,KAAa;QACjC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;QAEnC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,KAAK,IAAI,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;YAClE,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;QACxC,CAAC;IACL,CAAC;IAQD;;;;OAIG;IACI,IAAI,CAAC,UAA4C,EAAE;QACtD,IAAI,IAAI,CAAC,KAAK,8BAAsB,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,OAAO,CAAC,QAAQ,KAAhB,OAAO,CAAC,QAAQ,GAAK,IAAI,CAAC,QAAQ,EAAC;QACnC,OAAO,CAAC,IAAI,KAAZ,OAAO,CAAC,IAAI,GAAK,IAAI,CAAC,IAAI,EAAC;QAC3B,OAAO,CAAC,SAAS,KAAjB,OAAO,CAAC,SAAS,GAAK,IAAI,CAAC,SAAS,EAAC;QACrC,OAAO,CAAC,OAAO,KAAf,OAAO,CAAC,OAAO,GAAK,IAAI,CAAC,OAAO,EAAC;QACjC,OAAO,CAAC,WAAW,KAAnB,OAAO,CAAC,WAAW,GAAK,IAAI,CAAC,WAAW,EAAC;QACzC,OAAO,CAAC,MAAM,KAAd,OAAO,CAAC,MAAM,GAAK,CAAC,EAAC;QACrB,OAAO,CAAC,QAAQ,KAAhB,OAAO,CAAC,QAAQ,GAAK,CAAC,EAAC;QAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACxC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3B,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,UAA4C,EAAE;QACtD,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC3C,IAAI,CAAC,SAAS,6BAAqB,CAAC;QACxC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,SAAS,4BAAoB,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACjD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;IACL,CAAC;CAGJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport { SoundState } from \"../soundState\";\nimport type { IAbstractSoundOptions, IAbstractSoundPlayOptions, IAbstractSoundStoredOptions } from \"./abstractSound\";\nimport { AbstractSound } from \"./abstractSound\";\nimport type { PrimaryAudioBus } from \"./audioBus\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { IStaticSoundBufferOptions, StaticSoundBuffer } from \"./staticSoundBuffer\";\nimport type { _StaticSoundInstance } from \"./staticSoundInstance\";\n\n/** @internal */\nexport interface IStaticSoundOptionsBase {\n    /**\n     * The amount of time to play the sound for, in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the sound plays for its full duration.\n     */\n    duration: number;\n    /**\n     * The end of the loop range in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the loop plays for the sound's full duration.\n     * - Has no effect if {@link loop} is `false`.\n     */\n    loopEnd: number;\n    /**\n     * The start of the loop range in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the loop starts at the beginning of the sound.\n     * - Has no effect if {@link loop} is `false`.\n     *\n     */\n    loopStart: number;\n}\n\n/**\n * Options stored in a static sound.\n * @internal\n */\nexport interface IStaticSoundStoredOptions extends IAbstractSoundStoredOptions, IStaticSoundOptionsBase {\n    /**\n     * The pitch of the sound, in cents. Defaults to `0`.\n     * - Can be combined with {@link playbackRate}.\n     */\n    pitch: number;\n    /**\n     * The playback rate of the sound. Defaults to `1`.\n     * - Can be combined with {@link pitch}.\n     */\n    playbackRate: number;\n}\n\n/**\n * Options for creating a static sound.\n */\nexport interface IStaticSoundOptions extends IAbstractSoundOptions, IStaticSoundBufferOptions, IStaticSoundStoredOptions {}\n\n/**\n * Options for playing a static sound.\n */\nexport interface IStaticSoundPlayOptions extends IAbstractSoundPlayOptions, IStaticSoundOptionsBase {\n    /**\n     * The time to wait before playing the sound, in seconds. Defaults to `0`.\n     */\n    waitTime: number;\n}\n\n/**\n * Options for stopping a static sound.\n */\nexport interface IStaticSoundStopOptions {\n    /**\n     * The time to wait before stopping the sound, in seconds. Defaults to `0`.\n     */\n    waitTime: number;\n}\n\n/**\n * Options for cloning a static sound.\n * - @see {@link StaticSound.clone}.\n */\nexport interface IStaticSoundCloneOptions {\n    /**\n     * Whether to clone the sound buffer when cloning the sound. Defaults to `false`.\n     * - If `true`, the original sound's buffer is cloned, and the cloned sound will use its own copy.\n     * - If `false`, the sound buffer is shared with the original sound.\n     */\n    cloneBuffer: boolean;\n\n    /**\n     * The output bus for the cloned sound. Defaults to `null`.\n     * - If not set or `null`, the cloned sound uses the original sound's `outBus`.\n     * @see {@link AudioEngineV2.defaultMainBus}\n     */\n    outBus: Nullable<PrimaryAudioBus>;\n}\n\n/**\n * Abstract class representing a static sound.\n *\n * A static sound has a sound buffer that is loaded into memory all at once. This allows it to have more capabilities\n * than a streaming sound, such as loop points and playback rate changes, but it also means that the sound must be\n * fully downloaded and decoded before it can be played, which may take a long time for sounds with long durations.\n *\n * To prevent downloading and decoding a sound multiple times, a sound's buffer can be shared with other sounds.\n * See {@link CreateSoundBufferAsync}, {@link StaticSoundBuffer} and {@link StaticSound.buffer} for more information.\n *\n * Static sounds are created by the {@link CreateSoundAsync} function.\n */\nexport abstract class StaticSound extends AbstractSound {\n    protected override _instances: Set<_StaticSoundInstance>;\n    protected abstract override readonly _options: IStaticSoundStoredOptions;\n\n    /**\n     * The sound buffer that the sound uses.\n     *\n     * This buffer can be shared with other static sounds.\n     */\n    public abstract readonly buffer: StaticSoundBuffer;\n\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine);\n    }\n\n    /**\n     * The amount of time to play the sound for, in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the sound plays for its full duration.\n     */\n    public get duration(): number {\n        return this._options.duration;\n    }\n\n    public set duration(value: number) {\n        this._options.duration = value;\n    }\n\n    /**\n     * The start of the loop range, in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the loop starts at the beginning of the sound.\n     */\n    public get loopStart(): number {\n        return this._options.loopStart;\n    }\n\n    public set loopStart(value: number) {\n        this._options.loopStart = value;\n    }\n\n    /**\n     * The end of the loop range, in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the loop plays for the sound's full duration.\n     */\n    public get loopEnd(): number {\n        return this._options.loopEnd;\n    }\n\n    public set loopEnd(value: number) {\n        this._options.loopEnd = value;\n    }\n\n    /**\n     * The pitch of the sound, in cents. Defaults to `0`.\n     * - Gets combined with {@link playbackRate} to determine the final pitch.\n     */\n    public get pitch(): number {\n        return this._options.pitch;\n    }\n\n    public set pitch(value: number) {\n        this._options.pitch = value;\n\n        const it = this._instances.values();\n        for (let instance = it.next(); !instance.done; instance = it.next()) {\n            instance.value.pitch = value;\n        }\n    }\n\n    /**\n     * The playback rate of the sound. Defaults to `1`.\n     * - Gets combined with {@link pitch} to determine the final playback rate.\n     */\n    public get playbackRate(): number {\n        return this._options.playbackRate;\n    }\n\n    public set playbackRate(value: number) {\n        this._options.playbackRate = value;\n\n        const it = this._instances.values();\n        for (let instance = it.next(); !instance.done; instance = it.next()) {\n            instance.value.playbackRate = value;\n        }\n    }\n\n    /**\n     * Clones the sound.\n     * @param options Options for cloning the sound.\n     */\n    public abstract cloneAsync(options?: Partial<IStaticSoundCloneOptions>): Promise<StaticSound>;\n\n    /**\n     * Plays the sound.\n     * - Triggers `onEndedObservable` if played for the full duration and the `loop` option is not set.\n     * @param options The options to use when playing the sound. Options set here override the sound's options.\n     */\n    public play(options: Partial<IStaticSoundPlayOptions> = {}): void {\n        if (this.state === SoundState.Paused) {\n            this.resume();\n            return;\n        }\n\n        options.duration ??= this.duration;\n        options.loop ??= this.loop;\n        options.loopStart ??= this.loopStart;\n        options.loopEnd ??= this.loopEnd;\n        options.startOffset ??= this.startOffset;\n        options.volume ??= 1;\n        options.waitTime ??= 0;\n\n        const instance = this._createInstance();\n        this._beforePlay(instance);\n        instance.play(options);\n        this._afterPlay(instance);\n\n        this._stopExcessInstances();\n    }\n\n    /**\n     * Stops the sound.\n     * - Triggers `onEndedObservable` if the sound is playing.\n     * @param options - The options to use when stopping the sound.\n     */\n    public stop(options: Partial<IStaticSoundStopOptions> = {}): void {\n        if (options.waitTime && 0 < options.waitTime) {\n            this._setState(SoundState.Stopping);\n        } else {\n            this._setState(SoundState.Stopped);\n        }\n\n        if (!this._instances) {\n            return;\n        }\n\n        for (const instance of Array.from(this._instances)) {\n            instance.stop(options);\n        }\n    }\n\n    protected abstract override _createInstance(): _StaticSoundInstance;\n}\n"]}