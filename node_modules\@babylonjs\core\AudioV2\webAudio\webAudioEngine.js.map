{"version": 3, "file": "webAudioEngine.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/webAudio/webAudioEngine.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAMnD,OAAO,EAAE,aAAa,EAAE,MAAM,gCAAgC,CAAC;AAM/D,OAAO,EAAE,+BAA+B,EAAE,MAAM,6DAA6D,CAAC;AAG9G,OAAO,EAAE,2BAA2B,EAAE,MAAM,yCAAyC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AACrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;AAgCvD;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB,CAAC,UAA2C,EAAE;IACtF,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAC5C,MAAM,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,eAAe,GAA8B;IAC/C,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,WAAW;IAChB,IAAI,EAAE,YAAY;IAClB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,0BAA0B;IAC/B,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,4BAA4B;IACjC,GAAG,EAAE,WAAW;IAChB,IAAI,EAAE,6BAA6B;CACtC,CAAC;AAEF,gBAAgB;AAChB,MAAM,OAAO,eAAgB,SAAQ,aAAa;IAsC9C,gBAAgB;IAChB,YAAmB,UAA2C,EAAE;QAC5D,KAAK,CAAC,OAAO,CAAC,CAAC;QAvCX,yBAAoB,GAAG,KAAK,CAAC;QAC7B,qBAAgB,GAAwB,IAAI,CAAC;QAC7C,oBAAe,GAAG,IAAI,GAAG,EAAU,CAAC;QACpC,gBAAW,GAAG,KAAK,CAAC;QACpB,cAAS,GAAoC,IAAI,CAAC;QACzC,wBAAmB,GAAY,IAAI,CAAC;QACpC,2BAAsB,GAAW,CAAC,CAAC;QAE5C,iBAAY,GAAG,KAAK,CAAC;QACrB,yBAAoB,GAAG,IAAI,CAAC;QAC5B,mBAAc,GAAG,IAAI,CAAC;QACtB,gCAA2B,GAAG,IAAI,CAAC;QACnC,0BAAqB,GAAQ,IAAI,CAAC;QAClC,mBAAc,GAA4B,IAAI,CAAC;QAC/C,qBAAgB,GAA+B,IAAI,CAAC;QACpD,cAAS,GAAgC,IAAI,CAAC;QAC9C,sBAAiB,GAA+B,IAAI,CAAC;QAC5C,kBAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QAC3C,YAAO,GAAG,CAAC,CAAC;QAKpB,gBAAgB;QACA,gCAA2B,GAAY,KAAK,CAAC;QAE7D,gBAAgB;QACA,mBAAc,GAAkB,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACpE,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,gBAAgB;QACT,2BAAsB,GAAuB,IAAI,UAAU,EAAE,CAAC;QAErE,gBAAgB;QACT,0BAAqB,GAAqB,IAAI,UAAU,EAAE,CAAC;QAkU1D,2BAAsB,GAAwB,KAAK,IAAI,EAAE;YAC7D,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAEpF,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAEpC,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC,CAAC;QAEM,+BAA0B,GAAG,GAAG,EAAE;YACtC,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC3B,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAC1C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC/B,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,IAAI,CAAC,KAAK,KAAK,aAAa,EAAE,CAAC;gBAC7D,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;oBACzE,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBAE1C,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC,GAAG,EAAE;wBAC1C,mEAAmE;wBACnE,IAAI,CAAC,WAAW,EAAE,CAAC;oBACvB,CAAC,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBACzC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC;QAEM,wBAAmB,GAAe,KAAK,IAAI,EAAE;YACjD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACtC,CAAC;YAED,mEAAmE;YACnE,+FAA+F;YAC/F,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACzB,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAExD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBACpC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACvB,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;gBACvB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;gBAElB,2CAA2C;gBAC3C,KAAK,CAAC,GAAG,GAAG,oGAAoG,CAAC;gBAEjH,mEAAmE;gBACnE,KAAK,CAAC,IAAI,EAAE,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,EAAE,CAAC;QACjD,CAAC,CAAC;QAIM,mBAAc,GAAG,GAAG,EAAE;YAC1B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,OAAO;YACX,CAAC;YAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAExB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;iBAAM,CAAC;gBACJ,MAAM,QAAQ,GAAG,GAAG,EAAE;oBAClB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;wBAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;wBACf,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBACzD,CAAC;gBACL,CAAC,CAAC;gBAEF,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC,CAAC;QAEM,YAAO,GAAG,GAAS,EAAE;YACzB,IAAI,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,EAAE,CAAC;gBACzC,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,CAAC;gBACzC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YAC7B,CAAC;QACL,CAAC,CAAC;QAhZE,IAAI,OAAO,OAAO,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;YAClD,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAC1D,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,qBAAqB,KAAK,QAAQ,EAAE,CAAC;YACpD,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC,qBAAqB,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;QAEnC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,2BAA2B,GAAG,OAAO,CAAC,YAAY,YAAY,mBAAmB,CAAC;YACvF,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;QAC9C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,UAAU,CAAC,OAAwC;QAC5D,IAAI,CAAC,oBAAoB,GAAG,OAAO,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC;QAClH,IAAI,CAAC,cAAc,GAAG,OAAO,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC;QAChG,IAAI,CAAC,2BAA2B,GAAG,OAAO,CAAC,0BAA0B,IAAI,IAAI,CAAC;QAE9E,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE7D,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEpC,IAAI,+BAA+B,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1G,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;IAC1C,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;IACzI,CAAC;IAED,gBAAgB;IAChB,IAAW,KAAK;QACZ,6GAA6G;QAC7G,OAAO,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;IACnF,CAAC;IAED,gBAAgB;IAChB,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,gBAAgB;IAChB,IAAW,MAAM,CAAC,KAAa;QAC3B,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;SAGK;IACL,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IACpH,CAAC;IAED,IAAW,iBAAiB,CAAC,KAA0B;QACnD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3D,CAAC;IAED,IAAW,gBAAgB,CAAC,KAAc;QACtC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;QACnC,CAAC;IACL,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,cAAc,CAAC,IAAY,EAAE,UAAqC,EAAE;QAC7E,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;QAE7C,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACzD,MAAM,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE9B,OAAO,GAAG,CAAC;IACf,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,kBAAkB,CAAC,IAAY,EAAE,UAAyC,EAAE;QACrF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAEjD,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACpD,MAAM,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE9B,OAAO,GAAG,CAAC;IACf,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,gCAAgC,CAAC,IAAY,EAAE,OAAsC;QAC9F,IAAI,WAAwB,CAAC;QAE7B,IAAI,CAAC;YACD,WAAW,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,0BAA0B,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE;YAChH,iBAAiB,EAAE,KAAK;YACxB,GAAG,OAAO;SACb,CAAC,CAAC;IACP,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,gBAAgB,CACzB,IAAY,EACZ,MAAyE,EACzE,UAAwC,EAAE;QAE1C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;QAErD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACnE,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAExC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,sBAAsB,CAC/B,MAAyE,EACzE,UAA8C,EAAE;QAEhD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;QAErD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAE9C,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,sBAAsB,CAAC,IAAY,EAAE,MAAiB,EAAE,UAAwC,EAAE;QAC3G,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;QAErD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACjF,MAAM,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEtC,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,yBAAyB,CAAC,IAAY,EAAE,MAA4C,EAAE,UAA2C,EAAE;QAC5I,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;QAExD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACtE,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAExC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,gEAAgE;QAChE,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAC7E,mEAAmE;YACnE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC/B,CAAC;QAED,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAChE,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAEvF,IAAI,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;QAEhC,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAE9B,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;IACxC,CAAC;IAED,gBAAgB;IACT,iBAAiB,CAAC,MAAc;QACnC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED,gBAAgB;IACT,aAAa,CAAC,MAAc;QAC/B,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAC1B,IAAI,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACjC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACA,KAAK,CAAC,UAAU;QAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAEnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED,gBAAgB;IAChB,2FAA2F;IAC3E,WAAW;QACvB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAClD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,gBAAgB;IACT,SAAS,CAAC,KAAa,EAAE,UAAyD,IAAI;QACzF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAED,gBAAgB;IACA,WAAW,CAAC,OAAqB;QAC7C,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED,gBAAgB;IACA,cAAc,CAAC,OAAqB;QAChD,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAED,gBAAgB;IACA,QAAQ,CAAC,IAA4B;QACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED,gBAAgB;IACA,WAAW,CAAC,IAA4B;QACpD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED,gBAAgB;IACT,kBAAkB,CAAC,QAAoB;QAC1C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,UAAU,EAAQ,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAEM,qBAAqB,CAAC,QAAoB;QAC7C,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;CAuFJ", "sourcesContent": ["import { Observable } from \"../../Misc/observable\";\nimport type { Nullable } from \"../../types\";\nimport type { AbstractNamedAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type { AbstractSoundSource, ISoundSourceOptions } from \"../abstractAudio/abstractSoundSource\";\nimport type { AudioBus, IAudioBusOptions } from \"../abstractAudio/audioBus\";\nimport type { AudioEngineV2State, IAudioEngineV2Options } from \"../abstractAudio/audioEngineV2\";\nimport { AudioEngineV2 } from \"../abstractAudio/audioEngineV2\";\nimport type { IMainAudioBusOptions, MainAudioBus } from \"../abstractAudio/mainAudioBus\";\nimport type { IStaticSoundOptions, StaticSound } from \"../abstractAudio/staticSound\";\nimport type { IStaticSoundBufferOptions, StaticSoundBuffer } from \"../abstractAudio/staticSoundBuffer\";\nimport type { IStreamingSoundOptions, StreamingSound } from \"../abstractAudio/streamingSound\";\nimport type { AbstractSpatialAudioListener } from \"../abstractAudio/subProperties/abstractSpatialAudioListener\";\nimport { _HasSpatialAudioListenerOptions } from \"../abstractAudio/subProperties/abstractSpatialAudioListener\";\nimport type { _SpatialAudioListener } from \"../abstractAudio/subProperties/spatialAudioListener\";\nimport type { IAudioParameterRampOptions } from \"../audioParameter\";\nimport { _CreateSpatialAudioListener } from \"./subProperties/spatialWebAudioListener\";\nimport { _WebAudioMainOut } from \"./webAudioMainOut\";\nimport { _WebAudioUnmuteUI } from \"./webAudioUnmuteUI\";\n\n/**\n * Options for creating a v2 audio engine that uses the WebAudio API.\n */\nexport interface IWebAudioEngineOptions extends IAudioEngineV2Options {\n    /**\n     * The audio context to be used by the engine.\n     */\n    audioContext: AudioContext;\n    /**\n     * The default UI's parent element. Defaults to the last created graphics engine's canvas if it exists; otherwise the HTML document's body.\n     */\n    defaultUIParentElement?: HTMLElement;\n    /**\n     * Set to `true` to disable the default UI. Defaults to `false`.\n     */\n    disableDefaultUI?: boolean;\n    /**\n     * Set to `true` to automatically resume the audio context when the user interacts with the page. Defaults to `true`.\n     */\n    resumeOnInteraction: boolean;\n    /**\n     * Set to `true` to automatically resume the audio context when the browser pauses audio playback. Defaults to `true`.\n     */\n    resumeOnPause: boolean;\n    /**\n     * The interval in milliseconds to try resuming audio playback when `resumeOnPause` is `true`. Defaults to `1000`.\n     */\n    resumeOnPauseRetryInterval: number;\n}\n\n/**\n * Creates a new v2 audio engine that uses the WebAudio API.\n * @param options - The options for creating the audio engine.\n * @returns A promise that resolves with the created audio engine.\n */\nexport async function CreateAudioEngineAsync(options: Partial<IWebAudioEngineOptions> = {}): Promise<AudioEngineV2> {\n    const engine = new _WebAudioEngine(options);\n    await engine._initAsync(options);\n    return engine;\n}\n\nconst FormatMimeTypes: { [key: string]: string } = {\n    aac: \"audio/aac\",\n    ac3: \"audio/ac3\",\n    flac: \"audio/flac\",\n    m4a: \"audio/mp4\",\n    mp3: 'audio/mpeg; codecs=\"mp3\"',\n    mp4: \"audio/mp4\",\n    ogg: 'audio/ogg; codecs=\"vorbis\"',\n    wav: \"audio/wav\",\n    webm: 'audio/webm; codecs=\"vorbis\"',\n};\n\n/** @internal */\nexport class _WebAudioEngine extends AudioEngineV2 {\n    private _audioContextStarted = false;\n    private _destinationNode: Nullable<AudioNode> = null;\n    private _invalidFormats = new Set<string>();\n    private _isUpdating = false;\n    private _listener: Nullable<_SpatialAudioListener> = null;\n    private readonly _listenerAutoUpdate: boolean = true;\n    private readonly _listenerMinUpdateTime: number = 0;\n    private _mainOut: _WebAudioMainOut;\n    private _pauseCalled = false;\n    private _resumeOnInteraction = true;\n    private _resumeOnPause = true;\n    private _resumeOnPauseRetryInterval = 1000;\n    private _resumeOnPauseTimerId: any = null;\n    private _resumePromise: Nullable<Promise<void>> = null;\n    private _silentHtmlAudio: Nullable<HTMLAudioElement> = null;\n    private _unmuteUI: Nullable<_WebAudioUnmuteUI> = null;\n    private _updateObservable: Nullable<Observable<void>> = null;\n    private readonly _validFormats = new Set<string>();\n    private _volume = 1;\n\n    /** @internal */\n    public readonly _audioContext: AudioContext;\n\n    /** @internal */\n    public readonly _isUsingOfflineAudioContext: boolean = false;\n\n    /** @internal */\n    public readonly isReadyPromise: Promise<void> = new Promise((resolve) => {\n        this._resolveIsReadyPromise = resolve;\n    });\n\n    /** @internal */\n    public stateChangedObservable: Observable<string> = new Observable();\n\n    /** @internal */\n    public userGestureObservable: Observable<void> = new Observable();\n\n    /** @internal */\n    public constructor(options: Partial<IWebAudioEngineOptions> = {}) {\n        super(options);\n\n        if (typeof options.listenerAutoUpdate === \"boolean\") {\n            this._listenerAutoUpdate = options.listenerAutoUpdate;\n        }\n\n        if (typeof options.listenerMinUpdateTime === \"number\") {\n            this._listenerMinUpdateTime = options.listenerMinUpdateTime;\n        }\n\n        this._volume = options.volume ?? 1;\n\n        if (options.audioContext) {\n            this._isUsingOfflineAudioContext = options.audioContext instanceof OfflineAudioContext;\n            this._audioContext = options.audioContext;\n        } else {\n            this._audioContext = new AudioContext();\n        }\n\n        if (!options.disableDefaultUI) {\n            this._unmuteUI = new _WebAudioUnmuteUI(this, options.defaultUIParentElement);\n        }\n    }\n\n    /** @internal */\n    public async _initAsync(options: Partial<IWebAudioEngineOptions>): Promise<void> {\n        this._resumeOnInteraction = typeof options.resumeOnInteraction === \"boolean\" ? options.resumeOnInteraction : true;\n        this._resumeOnPause = typeof options.resumeOnPause === \"boolean\" ? options.resumeOnPause : true;\n        this._resumeOnPauseRetryInterval = options.resumeOnPauseRetryInterval ?? 1000;\n\n        document.addEventListener(\"click\", this._onUserGestureAsync);\n\n        await this._initAudioContextAsync();\n\n        if (_HasSpatialAudioListenerOptions(options)) {\n            this._listener = _CreateSpatialAudioListener(this, this._listenerAutoUpdate, this._listenerMinUpdateTime);\n            this._listener.setOptions(options);\n        }\n\n        this._resolveIsReadyPromise();\n    }\n\n    /** @internal */\n    public get currentTime(): number {\n        return this._audioContext.currentTime ?? 0;\n    }\n\n    /** @internal */\n    public get _inNode(): AudioNode {\n        return this._audioContext.destination;\n    }\n\n    /** @internal */\n    public get mainOut(): _WebAudioMainOut {\n        return this._mainOut;\n    }\n\n    /** @internal */\n    public get listener(): AbstractSpatialAudioListener {\n        return this._listener ?? (this._listener = _CreateSpatialAudioListener(this, this._listenerAutoUpdate, this._listenerMinUpdateTime));\n    }\n\n    /** @internal */\n    public get state(): AudioEngineV2State {\n        // Always return \"running\" for OfflineAudioContext so sound `play` calls work while the context is suspended.\n        return this._isUsingOfflineAudioContext ? \"running\" : this._audioContext.state;\n    }\n\n    /** @internal */\n    public get volume(): number {\n        return this._volume;\n    }\n\n    /** @internal */\n    public set volume(value: number) {\n        if (this._volume === value) {\n            return;\n        }\n\n        this._volume = value;\n\n        if (this._mainOut) {\n            this._mainOut.volume = value;\n        }\n    }\n\n    /**\n     * This property should only be used by the legacy audio engine.\n     * @internal\n     * */\n    public get _audioDestination(): AudioNode {\n        return this._destinationNode ? this._destinationNode : (this._destinationNode = this._audioContext.destination);\n    }\n\n    public set _audioDestination(value: Nullable<AudioNode>) {\n        this._destinationNode = value;\n    }\n\n    /**\n     * This property should only be used by the legacy audio engine.\n     * @internal\n     */\n    public get _unmuteUIEnabled(): boolean {\n        return this._unmuteUI ? this._unmuteUI.enabled : false;\n    }\n\n    public set _unmuteUIEnabled(value: boolean) {\n        if (this._unmuteUI) {\n            this._unmuteUI.enabled = value;\n        }\n    }\n\n    /** @internal */\n    public async createBusAsync(name: string, options: Partial<IAudioBusOptions> = {}): Promise<AudioBus> {\n        const module = await import(\"./webAudioBus\");\n\n        const bus = new module._WebAudioBus(name, this, options);\n        await bus._initAsync(options);\n\n        return bus;\n    }\n\n    /** @internal */\n    public async createMainBusAsync(name: string, options: Partial<IMainAudioBusOptions> = {}): Promise<MainAudioBus> {\n        const module = await import(\"./webAudioMainBus\");\n\n        const bus = new module._WebAudioMainBus(name, this);\n        await bus._initAsync(options);\n\n        return bus;\n    }\n\n    /** @internal */\n    public async createMicrophoneSoundSourceAsync(name: string, options?: Partial<ISoundSourceOptions>): Promise<AbstractSoundSource> {\n        let mediaStream: MediaStream;\n\n        try {\n            mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });\n        } catch (e) {\n            throw new Error(\"Unable to access microphone: \" + e);\n        }\n\n        return await this.createSoundSourceAsync(name, new MediaStreamAudioSourceNode(this._audioContext, { mediaStream }), {\n            outBusAutoDefault: false,\n            ...options,\n        });\n    }\n\n    /** @internal */\n    public async createSoundAsync(\n        name: string,\n        source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n        options: Partial<IStaticSoundOptions> = {}\n    ): Promise<StaticSound> {\n        const module = await import(\"./webAudioStaticSound\");\n\n        const sound = new module._WebAudioStaticSound(name, this, options);\n        await sound._initAsync(source, options);\n\n        return sound;\n    }\n\n    /** @internal */\n    public async createSoundBufferAsync(\n        source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n        options: Partial<IStaticSoundBufferOptions> = {}\n    ): Promise<StaticSoundBuffer> {\n        const module = await import(\"./webAudioStaticSound\");\n\n        const soundBuffer = new module._WebAudioStaticSoundBuffer(this);\n        await soundBuffer._initAsync(source, options);\n\n        return soundBuffer;\n    }\n\n    /** @internal */\n    public async createSoundSourceAsync(name: string, source: AudioNode, options: Partial<ISoundSourceOptions> = {}): Promise<AbstractSoundSource> {\n        const module = await import(\"./webAudioSoundSource\");\n\n        const soundSource = new module._WebAudioSoundSource(name, source, this, options);\n        await soundSource._initAsync(options);\n\n        return soundSource;\n    }\n\n    /** @internal */\n    public async createStreamingSoundAsync(name: string, source: HTMLMediaElement | string | string[], options: Partial<IStreamingSoundOptions> = {}): Promise<StreamingSound> {\n        const module = await import(\"./webAudioStreamingSound\");\n\n        const sound = new module._WebAudioStreamingSound(name, this, options);\n        await sound._initAsync(source, options);\n\n        return sound;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._listener?.dispose();\n        this._listener = null;\n\n        // Note that OfflineAudioContext does not have a `close` method.\n        if (this._audioContext.state !== \"closed\" && !this._isUsingOfflineAudioContext) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this._audioContext.close();\n        }\n\n        document.removeEventListener(\"click\", this._onUserGestureAsync);\n        this._audioContext.removeEventListener(\"statechange\", this._onAudioContextStateChange);\n\n        this._silentHtmlAudio?.remove();\n\n        this._updateObservable?.clear();\n        this._updateObservable = null;\n\n        this._unmuteUI?.dispose();\n        this._unmuteUI = null;\n\n        this.stateChangedObservable.clear();\n    }\n\n    /** @internal */\n    public flagInvalidFormat(format: string): void {\n        this._invalidFormats.add(format);\n    }\n\n    /** @internal */\n    public isFormatValid(format: string): boolean {\n        if (this._validFormats.has(format)) {\n            return true;\n        }\n\n        if (this._invalidFormats.has(format)) {\n            return false;\n        }\n\n        const mimeType = FormatMimeTypes[format];\n        if (mimeType === undefined) {\n            return false;\n        }\n\n        const audio = new Audio();\n        if (audio.canPlayType(mimeType) === \"\") {\n            this._invalidFormats.add(format);\n            return false;\n        }\n\n        this._validFormats.add(format);\n\n        return true;\n    }\n\n    /** @internal */\n    public override async pauseAsync(): Promise<void> {\n        await this._audioContext.suspend();\n\n        this._pauseCalled = true;\n    }\n\n    /** @internal */\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\n    public override resumeAsync(): Promise<void> {\n        this._pauseCalled = false;\n\n        if (this._resumePromise) {\n            return this._resumePromise;\n        }\n\n        this._resumePromise = this._audioContext.resume();\n        return this._resumePromise;\n    }\n\n    /** @internal */\n    public setVolume(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        if (this._mainOut) {\n            this._mainOut.setVolume(value, options);\n        } else {\n            throw new Error(\"Main output not initialized yet.\");\n        }\n    }\n\n    /** @internal */\n    public override _addMainBus(mainBus: MainAudioBus): void {\n        super._addMainBus(mainBus);\n    }\n\n    /** @internal */\n    public override _removeMainBus(mainBus: MainAudioBus): void {\n        super._removeMainBus(mainBus);\n    }\n\n    /** @internal */\n    public override _addNode(node: AbstractNamedAudioNode): void {\n        super._addNode(node);\n    }\n\n    /** @internal */\n    public override _removeNode(node: AbstractNamedAudioNode): void {\n        super._removeNode(node);\n    }\n\n    /** @internal */\n    public _addUpdateObserver(callback: () => void): void {\n        if (!this._updateObservable) {\n            this._updateObservable = new Observable<void>();\n        }\n\n        this._updateObservable.add(callback);\n        this._startUpdating();\n    }\n\n    public _removeUpdateObserver(callback: () => void): void {\n        if (this._updateObservable) {\n            this._updateObservable.removeCallback(callback);\n        }\n    }\n\n    private _initAudioContextAsync: () => Promise<void> = async () => {\n        this._audioContext.addEventListener(\"statechange\", this._onAudioContextStateChange);\n\n        this._mainOut = new _WebAudioMainOut(this);\n        this._mainOut.volume = this._volume;\n\n        await this.createMainBusAsync(\"default\");\n    };\n\n    private _onAudioContextStateChange = () => {\n        if (this.state === \"running\") {\n            clearInterval(this._resumeOnPauseTimerId);\n            this._audioContextStarted = true;\n            this._resumePromise = null;\n        }\n        if (this.state === \"suspended\" || this.state === \"interrupted\") {\n            if (this._audioContextStarted && this._resumeOnPause && !this._pauseCalled) {\n                clearInterval(this._resumeOnPauseTimerId);\n\n                this._resumeOnPauseTimerId = setInterval(() => {\n                    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n                    this.resumeAsync();\n                }, this._resumeOnPauseRetryInterval);\n            }\n        }\n\n        this.stateChangedObservable.notifyObservers(this.state);\n    };\n\n    private _onUserGestureAsync: () => void = async () => {\n        if (this._resumeOnInteraction) {\n            await this._audioContext.resume();\n        }\n\n        // On iOS the ringer switch must be turned on for WebAudio to play.\n        // This gets WebAudio to play with the ringer switch turned off by playing an HTMLAudioElement.\n        if (!this._silentHtmlAudio) {\n            this._silentHtmlAudio = document.createElement(\"audio\");\n\n            const audio = this._silentHtmlAudio;\n            audio.controls = false;\n            audio.preload = \"auto\";\n            audio.loop = true;\n\n            // Wave data for 0.0001 seconds of silence.\n            audio.src = \"data:audio/wav;base64,UklGRjAAAABXQVZFZm10IBAAAAABAAEAgLsAAAB3AQACABAAZGF0YQwAAAAAAAEA/v8CAP//AQA=\";\n\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            audio.play();\n        }\n\n        this.userGestureObservable.notifyObservers();\n    };\n\n    private _resolveIsReadyPromise: () => void;\n\n    private _startUpdating = () => {\n        if (this._isUpdating) {\n            return;\n        }\n\n        this._isUpdating = true;\n\n        if (this.state === \"running\") {\n            this._update();\n        } else {\n            const callback = () => {\n                if (this.state === \"running\") {\n                    this._update();\n                    this.stateChangedObservable.removeCallback(callback);\n                }\n            };\n\n            this.stateChangedObservable.add(callback);\n        }\n    };\n\n    private _update = (): void => {\n        if (this._updateObservable?.hasObservers()) {\n            this._updateObservable.notifyObservers();\n            requestAnimationFrame(this._update);\n        } else {\n            this._isUpdating = false;\n        }\n    };\n}\n"]}