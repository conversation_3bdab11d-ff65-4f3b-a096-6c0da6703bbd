{"version": 3, "file": "arcRotateCameraMouseWheelInput.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/Inputs/arcRotateCameraMouseWheelInput.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAIlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mCAAmC,CAAC;AAErE,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,KAAK,EAAE,MAAM,wBAAwB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAErD,OAAO,EAAE,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAChE,OAAO,EAAE,KAAK,EAAE,MAAM,mCAAmC,CAAC;AAC1D,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC;;;;;;GAMG;AACH,MAAM,YAAY,GAAG,EAAE,CAAC;AAExB;;;GAGG;AACH,MAAM,OAAO,8BAA8B;IAA3C;QAMI;;WAEG;QAEI,mBAAc,GAAG,GAAG,CAAC;QAE5B;;;WAGG;QAEI,wBAAmB,GAAG,KAAK,CAAC;QAEnC;;;WAGG;QAEI,yBAAoB,GAAG,CAAC,CAAC;QAEhC;;WAEG;QACI,qCAAgC,GAAwG,IAAI,CAAC;QAK5I,gBAAW,GAAY,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C,kBAAa,GAAY,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAgL9C,qBAAgB,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;IA8CvD,CAAC;IA5Na,sCAAsC,CAAC,eAAuB,EAAE,MAAc;QACpF,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,UAAU,GAAG,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC;QAC/E,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;YACtB,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACJ,KAAK,GAAG,UAAU,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,gBAA0B;QAC3C,gBAAgB,GAAG,KAAK,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE;YAChB,qDAAqD;YACrD,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,CAAC,YAAY,EAAE,CAAC;gBAC5C,OAAO;YACX,CAAC;YACD,MAAM,KAAK,GAAgB,CAAC,CAAC,KAAK,CAAC;YACnC,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,MAAM,aAAa,GAAG,KAAK,CAAC,SAAS,KAAK,cAAc,CAAC,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kEAAkE;YAE9J,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,CAAC;YAEnD,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;gBACxC,KAAK,GAAG,IAAI,CAAC,gCAAgC,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACJ,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC5B,KAAK,GAAG,IAAI,CAAC,sCAAsC,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAEpF,0FAA0F;oBAC1F,gFAAgF;oBAChF,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;wBACZ,IAAI,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;wBAC/C,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,GAAG,KAAK,CAAC;wBAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;4BAC1B,6CAA6C;4BAC7C,IAAI,qBAAqB,IAAI,aAAa,EAAE,CAAC;gCACzC,gEAAgE;gCAChE,MAAM;4BACV,CAAC;4BACD,IAAI,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,CAAC;gCACxD,8EAA8E;gCAC9E,MAAM;4BACV,CAAC;4BACD,qBAAqB,IAAI,aAAa,CAAC;4BACvC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;wBACzC,CAAC;wBACD,qBAAqB,GAAG,KAAK,CAAC,qBAAqB,EAAE,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;wBAC1E,KAAK,GAAG,IAAI,CAAC,sCAAsC,CAAC,UAAU,EAAE,qBAAqB,CAAC,CAAC;oBAC3F,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,KAAK,GAAG,UAAU,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,CAAC;gBACpD,CAAC;YACL,CAAC;YAED,IAAI,KAAK,EAAE,CAAC;gBACR,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,kIAAkI;oBAClI,yHAAyH;oBACzH,qDAAqD;oBACrD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;wBAClB,IAAI,CAAC,eAAe,EAAE,CAAC;oBAC3B,CAAC;oBAED,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAC7B,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,KAAK,CAAC;gBAC9C,CAAC;YACL,CAAC;YAED,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;gBAC3B,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAE7H,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,4BAA4B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAClF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACvB,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,WAAW;QACd,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,mBAAmB,GAAG,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,oBAAoB,CAAC;QAC1G,IAAI,MAAM,EAAE,CAAC;YACT,oFAAoF;YACpF,iBAAiB;YACjB,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,4FAA4F;YAC5F,yFAAyF;YACzF,kEAAkE;YAClE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChD,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACnD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gCAAgC,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,aAAa;QAChB,OAAO,YAAY,CAAC;IACxB,CAAC;IAEO,eAAe;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,qBAAqB,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAC3E,CAAC;IAED,gCAAgC;IACxB,YAAY;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAEhC,uFAAuF;QACvF,mFAAmF;QACnF,8CAA8C;QAC9C,MAAM,GAAG,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QACrG,0GAA0G;QAC1G,kFAAkF;QAClF,IAAI,MAAM,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YACzE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClF,MAAM,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;YAClE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,sBAAsB,CAAC,CAAC;YAC9F,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,QAAQ,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC;QAED,kEAAkE;QAClE,OAAO,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;IACvE,CAAC;IAIO,YAAY,CAAC,KAAa;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC;QACvC,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC;YAChD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,oBAAoB,GAAG,KAAK,CAAC,GAAG,WAAW,GAAG,UAAU,EAAE,CAAC;gBACnF,KAAK,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,WAAW,GAAG,MAAM,CAAC,oBAAoB,CAAC;YACrF,CAAC;QACL,CAAC;QACD,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,IAAI,CAAC,CAAC;YAChD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,oBAAoB,GAAG,KAAK,CAAC,GAAG,WAAW,GAAG,UAAU,EAAE,CAAC;gBACnF,KAAK,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,WAAW,GAAG,MAAM,CAAC,oBAAoB,CAAC;YACrF,CAAC;QACL,CAAC;QAED,MAAM,YAAY,GAAG,KAAK,GAAG,WAAW,CAAC;QACzC,MAAM,KAAK,GAAG,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEhC,mEAAmE;QACnE,gEAAgE;QAEhE,MAAM,uBAAuB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtD,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,uBAAuB,CAAC,CAAC;QAC1D,uBAAuB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC5C,uBAAuB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC;QAE1D,MAAM,CAAC,oBAAoB,IAAI,KAAK,CAAC;IACzC,CAAC;IAED,kEAAkE;IAC1D,YAAY,CAAC,GAAY;QAC7B,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC;YAC5B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC;YAC5B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC;YAC5B,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,CAAC;IACL,CAAC;CACJ;AAvPU;IADN,SAAS,EAAE;sEACgB;AAOrB;IADN,SAAS,EAAE;2EACuB;AAO5B;IADN,SAAS,EAAE;4EACoB;AA2O9B,gBAAiB,CAAC,gCAAgC,CAAC,GAAG,8BAA8B,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { serialize } from \"../../Misc/decorators\";\r\nimport type { EventState, Observer } from \"../../Misc/observable\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport type { ICameraInput } from \"../../Cameras/cameraInputsManager\";\r\nimport { CameraInputTypes } from \"../../Cameras/cameraInputsManager\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { Plane } from \"../../Maths/math.plane\";\r\nimport { Vector3, Matrix, TmpVectors } from \"../../Maths/math.vector\";\r\nimport { Epsilon } from \"../../Maths/math.constants\";\r\nimport type { IWheelEvent } from \"../../Events/deviceInputEvents\";\r\nimport { EventConstants } from \"../../Events/deviceInputEvents\";\r\nimport { Clamp } from \"../../Maths/math.scalar.functions\";\r\nimport { Tools } from \"../../Misc/tools\";\r\n\r\n/**\r\n * Firefox uses a different scheme to report scroll distances to other\r\n * browsers. Rather than use complicated methods to calculate the exact\r\n * multiple we need to apply, let's just cheat and use a constant.\r\n * https://developer.mozilla.org/en-US/docs/Web/API/WheelEvent/deltaMode\r\n * https://stackoverflow.com/questions/20110224/what-is-the-height-of-a-line-in-a-wheel-event-deltamode-dom-delta-line\r\n */\r\nconst FfMultiplier = 40;\r\n\r\n/**\r\n * Manage the mouse wheel inputs to control an arc rotate camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs\r\n */\r\nexport class ArcRotateCameraMouseWheelInput implements ICameraInput<ArcRotateCamera> {\r\n    /**\r\n     * Defines the camera the input is attached to.\r\n     */\r\n    public camera: ArcRotateCamera;\r\n\r\n    /**\r\n     * Gets or Set the mouse wheel precision or how fast is the camera zooming.\r\n     */\r\n    @serialize()\r\n    public wheelPrecision = 3.0;\r\n\r\n    /**\r\n     * Gets or Set the boolean value that controls whether or not the mouse wheel\r\n     * zooms to the location of the mouse pointer or not.  The default is false.\r\n     */\r\n    @serialize()\r\n    public zoomToMouseLocation = false;\r\n\r\n    /**\r\n     * wheelDeltaPercentage will be used instead of wheelPrecision if different from 0.\r\n     * It defines the percentage of current camera.radius to use as delta when wheel is used.\r\n     */\r\n    @serialize()\r\n    public wheelDeltaPercentage = 0;\r\n\r\n    /**\r\n     * If set, this function will be used to set the radius delta that will be added to the current camera radius\r\n     */\r\n    public customComputeDeltaFromMouseWheel: Nullable<(wheelDelta: number, input: ArcRotateCameraMouseWheelInput, event: IWheelEvent) => number> = null;\r\n\r\n    private _wheel: Nullable<(p: PointerInfo, s: EventState) => void>;\r\n    private _observer: Nullable<Observer<PointerInfo>>;\r\n    private _hitPlane: Nullable<Plane>;\r\n    private _viewOffset: Vector3 = new Vector3(0, 0, 0);\r\n    private _globalOffset: Vector3 = new Vector3(0, 0, 0);\r\n\r\n    protected _computeDeltaFromMouseWheelLegacyEvent(mouseWheelDelta: number, radius: number) {\r\n        let delta = 0;\r\n        const wheelDelta = mouseWheelDelta * 0.01 * this.wheelDeltaPercentage * radius;\r\n        if (mouseWheelDelta > 0) {\r\n            delta = wheelDelta / (1.0 + this.wheelDeltaPercentage);\r\n        } else {\r\n            delta = wheelDelta * (1.0 + this.wheelDeltaPercentage);\r\n        }\r\n        return delta;\r\n    }\r\n\r\n    /**\r\n     * Attach the input controls to a specific dom element to get the input from.\r\n     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)\r\n     */\r\n    public attachControl(noPreventDefault?: boolean): void {\r\n        noPreventDefault = Tools.BackCompatCameraNoPreventDefault(arguments);\r\n        this._wheel = (p) => {\r\n            //sanity check - this should be a PointerWheel event.\r\n            if (p.type !== PointerEventTypes.POINTERWHEEL) {\r\n                return;\r\n            }\r\n            const event = <IWheelEvent>p.event;\r\n            let delta = 0;\r\n            const platformScale = event.deltaMode === EventConstants.DOM_DELTA_LINE ? FfMultiplier : 1; // If this happens to be set to DOM_DELTA_LINE, adjust accordingly\r\n\r\n            const wheelDelta = -(event.deltaY * platformScale);\r\n\r\n            if (this.customComputeDeltaFromMouseWheel) {\r\n                delta = this.customComputeDeltaFromMouseWheel(wheelDelta, this, event);\r\n            } else {\r\n                if (this.wheelDeltaPercentage) {\r\n                    delta = this._computeDeltaFromMouseWheelLegacyEvent(wheelDelta, this.camera.radius);\r\n\r\n                    // If zooming in, estimate the target radius and use that to compute the delta for inertia\r\n                    // this will stop multiple scroll events zooming in from adding too much inertia\r\n                    if (delta > 0) {\r\n                        let estimatedTargetRadius = this.camera.radius;\r\n                        let targetInertia = this.camera.inertialRadiusOffset + delta;\r\n                        for (let i = 0; i < 20; i++) {\r\n                            // 20 iterations should be enough to converge\r\n                            if (estimatedTargetRadius <= targetInertia) {\r\n                                // We do not want a negative radius, so we break out of the loop\r\n                                break;\r\n                            }\r\n                            if (Math.abs(targetInertia * this.camera.inertia) < 0.001) {\r\n                                // We do not want to go below a certain threshold, so we break out of the loop\r\n                                break;\r\n                            }\r\n                            estimatedTargetRadius -= targetInertia;\r\n                            targetInertia *= this.camera.inertia;\r\n                        }\r\n                        estimatedTargetRadius = Clamp(estimatedTargetRadius, 0, Number.MAX_VALUE);\r\n                        delta = this._computeDeltaFromMouseWheelLegacyEvent(wheelDelta, estimatedTargetRadius);\r\n                    }\r\n                } else {\r\n                    delta = wheelDelta / (this.wheelPrecision * 40);\r\n                }\r\n            }\r\n\r\n            if (delta) {\r\n                if (this.zoomToMouseLocation) {\r\n                    // If we are zooming to the mouse location, then we need to get the hit plane at the start of the zoom gesture if it doesn't exist\r\n                    // The hit plane is normally calculated after the first motion and each time there's motion so if we don't do this first,\r\n                    // the first zoom will be to the center of the screen\r\n                    if (!this._hitPlane) {\r\n                        this._updateHitPlane();\r\n                    }\r\n\r\n                    this._zoomToMouse(delta);\r\n                } else {\r\n                    this.camera.inertialRadiusOffset += delta;\r\n                }\r\n            }\r\n\r\n            if (event.preventDefault) {\r\n                if (!noPreventDefault) {\r\n                    event.preventDefault();\r\n                }\r\n            }\r\n        };\r\n\r\n        this._observer = this.camera.getScene()._inputManager._addCameraPointerObserver(this._wheel, PointerEventTypes.POINTERWHEEL);\r\n\r\n        if (this.zoomToMouseLocation) {\r\n            this._inertialPanning.setAll(0);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detach the current controls from the specified dom element.\r\n     */\r\n    public detachControl(): void {\r\n        if (this._observer) {\r\n            this.camera.getScene()._inputManager._removeCameraPointerObserver(this._observer);\r\n            this._observer = null;\r\n            this._wheel = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the current camera state depending on the inputs that have been used this frame.\r\n     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.\r\n     */\r\n    public checkInputs(): void {\r\n        if (!this.zoomToMouseLocation) {\r\n            return;\r\n        }\r\n\r\n        const camera = this.camera;\r\n        const motion = 0.0 + camera.inertialAlphaOffset + camera.inertialBetaOffset + camera.inertialRadiusOffset;\r\n        if (motion) {\r\n            // if zooming is still happening as a result of inertia, then we also need to update\r\n            // the hit plane.\r\n            this._updateHitPlane();\r\n\r\n            // Note we cannot  use arcRotateCamera.inertialPlanning here because arcRotateCamera panning\r\n            // uses a different panningInertia which could cause this panning to get out of sync with\r\n            // the zooming, and for this to work they must be exactly in sync.\r\n            camera.target.addInPlace(this._inertialPanning);\r\n            this._inertialPanning.scaleInPlace(camera.inertia);\r\n            this._zeroIfClose(this._inertialPanning);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the current input.\r\n     * @returns the class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"ArcRotateCameraMouseWheelInput\";\r\n    }\r\n\r\n    /**\r\n     * Get the friendly name associated with the input class.\r\n     * @returns the input friendly name\r\n     */\r\n    public getSimpleName(): string {\r\n        return \"mousewheel\";\r\n    }\r\n\r\n    private _updateHitPlane() {\r\n        const camera = this.camera;\r\n        const direction = camera.target.subtract(camera.position);\r\n        this._hitPlane = Plane.FromPositionAndNormal(camera.target, direction);\r\n    }\r\n\r\n    // Get position on the hit plane\r\n    private _getPosition(): Vector3 {\r\n        const camera = this.camera;\r\n        const scene = camera.getScene();\r\n\r\n        // since the _hitPlane is always updated to be orthogonal to the camera position vector\r\n        // we don't have to worry about this ray shooting off to infinity. This ray creates\r\n        // a vector defining where we want to zoom to.\r\n        const ray = scene.createPickingRay(scene.pointerX, scene.pointerY, Matrix.Identity(), camera, false);\r\n        // Since the camera is the origin of the picking ray, we need to offset it by the camera's offset manually\r\n        // Because the offset is in view space, we need to convert it to world space first\r\n        if (camera.targetScreenOffset.x !== 0 || camera.targetScreenOffset.y !== 0) {\r\n            this._viewOffset.set(camera.targetScreenOffset.x, camera.targetScreenOffset.y, 0);\r\n            camera.getViewMatrix().invertToRef(camera._cameraTransformMatrix);\r\n            this._globalOffset = Vector3.TransformNormal(this._viewOffset, camera._cameraTransformMatrix);\r\n            ray.origin.addInPlace(this._globalOffset);\r\n        }\r\n\r\n        let distance = 0;\r\n        if (this._hitPlane) {\r\n            distance = ray.intersectsPlane(this._hitPlane) ?? 0;\r\n        }\r\n\r\n        // not using this ray again, so modifying its vectors here is fine\r\n        return ray.origin.addInPlace(ray.direction.scaleInPlace(distance));\r\n    }\r\n\r\n    private _inertialPanning: Vector3 = Vector3.Zero();\r\n\r\n    private _zoomToMouse(delta: number) {\r\n        const camera = this.camera;\r\n        const inertiaComp = 1 - camera.inertia;\r\n        if (camera.lowerRadiusLimit) {\r\n            const lowerLimit = camera.lowerRadiusLimit ?? 0;\r\n            if (camera.radius - (camera.inertialRadiusOffset + delta) / inertiaComp < lowerLimit) {\r\n                delta = (camera.radius - lowerLimit) * inertiaComp - camera.inertialRadiusOffset;\r\n            }\r\n        }\r\n        if (camera.upperRadiusLimit) {\r\n            const upperLimit = camera.upperRadiusLimit ?? 0;\r\n            if (camera.radius - (camera.inertialRadiusOffset + delta) / inertiaComp > upperLimit) {\r\n                delta = (camera.radius - upperLimit) * inertiaComp - camera.inertialRadiusOffset;\r\n            }\r\n        }\r\n\r\n        const zoomDistance = delta / inertiaComp;\r\n        const ratio = zoomDistance / camera.radius;\r\n        const vec = this._getPosition();\r\n\r\n        // Now this vector tells us how much we also need to pan the camera\r\n        // so the targeted mouse location becomes the center of zooming.\r\n\r\n        const directionToZoomLocation = TmpVectors.Vector3[6];\r\n        vec.subtractToRef(camera.target, directionToZoomLocation);\r\n        directionToZoomLocation.scaleInPlace(ratio);\r\n        directionToZoomLocation.scaleInPlace(inertiaComp);\r\n        this._inertialPanning.addInPlace(directionToZoomLocation);\r\n\r\n        camera.inertialRadiusOffset += delta;\r\n    }\r\n\r\n    // Sets x y or z of passed in vector to zero if less than Epsilon.\r\n    private _zeroIfClose(vec: Vector3) {\r\n        if (Math.abs(vec.x) < Epsilon) {\r\n            vec.x = 0;\r\n        }\r\n        if (Math.abs(vec.y) < Epsilon) {\r\n            vec.y = 0;\r\n        }\r\n        if (Math.abs(vec.z) < Epsilon) {\r\n            vec.z = 0;\r\n        }\r\n    }\r\n}\r\n\r\n(<any>CameraInputTypes)[\"ArcRotateCameraMouseWheelInput\"] = ArcRotateCameraMouseWheelInput;\r\n"]}