{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/audioEngineV2.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/audioEngineV2.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { IAudioParameterRampOptions } from \"../audioParameter\";\nimport type { AbstractAudioNode, AbstractNamedAudioNode } from \"./abstractAudioNode\";\nimport type { AbstractSoundSource, ISoundSourceOptions } from \"./abstractSoundSource\";\nimport type { AudioBus, IAudioBusOptions } from \"./audioBus\";\nimport type { IMainAudioBusOptions, MainAudioBus } from \"./mainAudioBus\";\nimport type { IStaticSoundOptions, StaticSound } from \"./staticSound\";\nimport type { IStaticSoundBufferOptions, StaticSoundBuffer } from \"./staticSoundBuffer\";\nimport type { IStreamingSoundOptions, StreamingSound } from \"./streamingSound\";\nimport type { AbstractSpatialAudioListener, ISpatialAudioListenerOptions } from \"./subProperties/abstractSpatialAudioListener\";\n\nconst Instances: AudioEngineV2[] = [];\n\n/**\n * Gets the most recently created v2 audio engine.\n * @returns The most recently created v2 audio engine.\n */\nexport function LastCreatedAudioEngine(): Nullable<AudioEngineV2> {\n    if (Instances.length === 0) {\n        return null;\n    }\n\n    return Instances[Instances.length - 1];\n}\n\n/**\n * Options for creating a v2 audio engine.\n */\nexport interface IAudioEngineV2Options extends ISpatialAudioListenerOptions {\n    /**\n     * The smoothing duration to use when changing audio parameters, in seconds. Defaults to `0.01` (10 milliseconds).\n     */\n    parameterRampDuration: number;\n    /**\n     * The initial output volume of the audio engine. Defaults to `1`.\n     */\n    volume: number;\n}\n\n/**\n * The state of a v2 audio engine.\n * @see {@link AudioEngineV2.state}\n */\nexport type AudioEngineV2State = \"closed\" | \"interrupted\" | \"running\" | \"suspended\";\n\n/**\n * Abstract base class for v2 audio engines.\n *\n * A v2 audio engine based on the WebAudio API can be created with the {@link CreateAudioEngineAsync} function.\n */\nexport abstract class AudioEngineV2 {\n    /** Not owned, but all items should be in `_nodes` container, too, which is owned. */\n    private readonly _mainBuses = new Set<MainAudioBus>();\n\n    /** Owned top-level sound and bus nodes. */\n    private readonly _nodes = new Set<AbstractNamedAudioNode>();\n\n    private _defaultMainBus: Nullable<MainAudioBus> = null;\n\n    private _parameterRampDuration: number = 0.01;\n\n    protected constructor(options: Partial<IAudioEngineV2Options>) {\n        Instances.push(this);\n\n        if (typeof options.parameterRampDuration === \"number\") {\n            this.parameterRampDuration = options.parameterRampDuration;\n        }\n    }\n\n    /**\n     * The elapsed time since the audio engine was started, in seconds.\n     */\n    public abstract readonly currentTime: number;\n\n    /**\n     * The default main bus that will be used for audio buses and sounds if their `outBus` option is not set.\n     * @see {@link IAudioBusOptions.outBus}\n     * @see {@link IAbstractSoundOptions.outBus}\n     */\n    public get defaultMainBus(): Nullable<MainAudioBus> {\n        if (this._mainBuses.size === 0) {\n            return null;\n        }\n\n        if (!this._defaultMainBus) {\n            this._defaultMainBus = Array.from(this._mainBuses)[0];\n        }\n\n        return this._defaultMainBus;\n    }\n\n    /**\n     * The spatial audio listener properties for the audio engine.\n     * - Each audio engine has exactly one listener.\n     */\n    public abstract readonly listener: AbstractSpatialAudioListener;\n\n    /**\n     * The main output node.\n     * - This is the last node in the audio graph before the audio is sent to the speakers.\n     */\n    public abstract readonly mainOut: AbstractAudioNode;\n\n    /**\n     * The current state of the audio engine.\n     *\n     * Possible values are:\n     * - `\"closed\"`: The audio engine has been closed.\n     * - `\"interrupted\"`: The audio engine has been interrupted and is not running.\n     * - `\"running\"`: The audio engine is running normally.\n     * - `\"suspended\"`: The audio engine is suspended and is not running.\n     */\n    public abstract readonly state: AudioEngineV2State;\n\n    /**\n     * The output volume of the audio engine.\n     */\n    public abstract volume: number;\n\n    /**\n     * The smoothing duration to use when changing audio parameters, in seconds. Defaults to `0.01` (10 milliseconds).\n     *\n     * Due to limitations in some browsers, it is not recommended to set this value to longer than `0.01` seconds.\n     *\n     * Setting this value to longer than `0.01` seconds may result in errors being throw when setting audio parameters.\n     */\n    public get parameterRampDuration(): number {\n        return this._parameterRampDuration;\n    }\n\n    public set parameterRampDuration(value: number) {\n        this._parameterRampDuration = Math.max(0, value);\n    }\n\n    /**\n     * Creates a new audio bus.\n     * @param name - The name of the audio bus.\n     * @param options - The options to use when creating the audio bus.\n     * @returns A promise that resolves with the created audio bus.\n     */\n    public abstract createBusAsync(name: string, options?: Partial<IAudioBusOptions>): Promise<AudioBus>;\n\n    /**\n     * Creates a new main audio bus.\n     * @param name - The name of the main audio bus.\n     * @param options - The options to use when creating the main audio bus.\n     * @returns A promise that resolves with the created main audio bus.\n     */\n    public abstract createMainBusAsync(name: string, options?: Partial<IMainAudioBusOptions>): Promise<MainAudioBus>;\n\n    /**\n     * Creates a new microphone sound source.\n     * @param name - The name of the sound.\n     * @param options - The options for the sound source.\n     * @returns A promise that resolves to the created sound source.\n     */\n    public abstract createMicrophoneSoundSourceAsync(name: string, options?: Partial<ISoundSourceOptions>): Promise<AbstractSoundSource>;\n\n    /**\n     * Creates a new static sound.\n     * @param name - The name of the sound.\n     * @param source - The source of the sound.\n     * @param options - The options for the static sound.\n     * @returns A promise that resolves to the created static sound.\n     */\n    public abstract createSoundAsync(\n        name: string,\n        source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n        options?: Partial<IStaticSoundOptions>\n    ): Promise<StaticSound>;\n\n    /**\n     * Creates a new static sound buffer.\n     * @param source - The source of the sound buffer.\n     * @param options - The options for the static sound buffer.\n     * @returns A promise that resolves to the created static sound buffer.\n     */\n    public abstract createSoundBufferAsync(\n        source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n        options?: Partial<IStaticSoundBufferOptions>\n    ): Promise<StaticSoundBuffer>;\n\n    /**\n     * Creates a new sound source.\n     * @param name - The name of the sound.\n     * @param source - The source of the sound.\n     * @param options - The options for the sound source.\n     * @returns A promise that resolves to the created sound source.\n     */\n    public abstract createSoundSourceAsync(name: string, source: AudioNode, options?: Partial<ISoundSourceOptions>): Promise<AbstractSoundSource>;\n\n    /**\n     * Creates a new streaming sound.\n     * @param name - The name of the sound.\n     * @param source - The source of the sound.\n     * @param options - The options for the streaming sound.\n     * @returns A promise that resolves to the created streaming sound.\n     */\n    public abstract createStreamingSoundAsync(name: string, source: HTMLMediaElement | string | string[], options?: Partial<IStreamingSoundOptions>): Promise<StreamingSound>;\n\n    /**\n     * Releases associated resources.\n     */\n    public dispose(): void {\n        if (Instances.includes(this)) {\n            Instances.splice(Instances.indexOf(this), 1);\n        }\n\n        const nodeIt = this._nodes.values();\n        for (let next = nodeIt.next(); !next.done; next = nodeIt.next()) {\n            next.value.dispose();\n        }\n\n        this._mainBuses.clear();\n        this._nodes.clear();\n\n        this._defaultMainBus = null;\n    }\n\n    /**\n     * Checks if the specified format is valid.\n     * @param format The format to check as an audio file extension like \"mp3\" or \"wav\".\n     * @returns `true` if the format is valid; otherwise `false`.\n     */\n    public abstract isFormatValid(format: string): boolean;\n\n    /**\n     * Pauses the audio engine if it is running.\n     * @returns A promise that resolves when the audio engine is paused.\n     */\n    public abstract pauseAsync(): Promise<void>;\n\n    /**\n     * Resumes the audio engine if it is not running.\n     * @returns A promise that resolves when the audio engine is running.\n     */\n    public abstract resumeAsync(): Promise<void>;\n\n    /**\n     * Sets the audio output volume with optional ramping.\n     * If the duration is 0 then the volume is set immediately, otherwise it is ramped to the new value over the given duration using the given shape.\n     * If a ramp is already in progress then the volume is not set and an error is thrown.\n     * @param value The value to set the volume to.\n     * @param options The options to use for ramping the volume change.\n     */\n    public abstract setVolume(value: number, options?: Partial<IAudioParameterRampOptions>): void;\n\n    /**\n     * Unlocks the audio engine if it is locked.\n     * - Note that the returned promise may already be resolved if the audio engine is already unlocked.\n     * @returns A promise that is resolved when the audio engine is unlocked.\n     */\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\n    public unlockAsync(): Promise<void> {\n        return this.resumeAsync();\n    }\n\n    protected _addMainBus(mainBus: MainAudioBus): void {\n        this._mainBuses.add(mainBus);\n\n        this._addNode(mainBus);\n    }\n\n    protected _removeMainBus(mainBus: MainAudioBus): void {\n        this._mainBuses.delete(mainBus);\n        this._defaultMainBus = null;\n\n        this._removeNode(mainBus);\n    }\n\n    protected _addNode(node: AbstractNamedAudioNode): void {\n        this._nodes.add(node);\n    }\n\n    protected _removeNode(node: AbstractNamedAudioNode): void {\n        this._nodes.delete(node);\n    }\n}\n\n/**\n * @internal\n * @param engine - The given audio engine. If `null` then the last created audio engine is used.\n * @returns the given audio engine or the last created audio engine.\n * @throws An error if the resulting engine is `null`.\n */\nexport function _GetAudioEngine(engine: Nullable<AudioEngineV2>): AudioEngineV2 {\n    if (!engine) {\n        engine = LastCreatedAudioEngine();\n    }\n\n    if (engine) {\n        return engine;\n    }\n\n    throw new Error(\"No audio engine.\");\n}\n\n/**\n * Creates a new audio bus.\n * @param name - The name of the audio bus.\n * @param options - The options to use when creating the audio bus.\n * @param engine - The audio engine.\n * @returns A promise that resolves with the created audio bus.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateAudioBusAsync(name: string, options: Partial<IAudioBusOptions> = {}, engine: Nullable<AudioEngineV2> = null): Promise<AudioBus> {\n    engine = _GetAudioEngine(engine);\n    return engine.createBusAsync(name, options);\n}\n\n/**\n * Creates a new main audio bus.\n * @param name - The name of the main audio bus.\n * @param options - The options to use when creating the main audio bus.\n * @param engine - The audio engine.\n * @returns A promise that resolves with the created main audio bus.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateMainAudioBusAsync(name: string, options: Partial<IMainAudioBusOptions> = {}, engine: Nullable<AudioEngineV2> = null): Promise<MainAudioBus> {\n    engine = _GetAudioEngine(engine);\n    return engine.createMainBusAsync(name, options);\n}\n\n/**\n * Creates a new microphone sound source.\n * @param name - The name of the sound.\n * @param options - The options for the sound source.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created sound source.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateMicrophoneSoundSourceAsync(name: string, options: Partial<ISoundSourceOptions> = {}, engine: Nullable<AudioEngineV2> = null): Promise<AbstractSoundSource> {\n    engine = _GetAudioEngine(engine);\n    return engine.createMicrophoneSoundSourceAsync(name, options);\n}\n\n/**\n * Creates a new static sound.\n * @param name - The name of the sound.\n * @param source - The source of the sound.\n * @param options - The options for the static sound.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created static sound.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateSoundAsync(\n    name: string,\n    source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n    options: Partial<IStaticSoundOptions> = {},\n    engine: Nullable<AudioEngineV2> = null\n): Promise<StaticSound> {\n    engine = _GetAudioEngine(engine);\n    return engine.createSoundAsync(name, source, options);\n}\n\n/**\n * Creates a new static sound buffer.\n * @param source - The source of the sound buffer.\n * @param options - The options for the static sound buffer.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created static sound buffer.\n */\nexport async function CreateSoundBufferAsync(\n    source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n    options: Partial<IStaticSoundBufferOptions> = {},\n    engine: Nullable<AudioEngineV2> = null\n): Promise<StaticSoundBuffer> {\n    engine = _GetAudioEngine(engine);\n    return await engine.createSoundBufferAsync(source, options);\n}\n\n/**\n * Creates a new sound source.\n * @param name - The name of the sound.\n * @param source - The source of the sound.\n * @param options - The options for the sound source.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created sound source.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateSoundSourceAsync(\n    name: string,\n    source: AudioNode,\n    options: Partial<ISoundSourceOptions> = {},\n    engine: Nullable<AudioEngineV2> = null\n): Promise<AbstractSoundSource> {\n    engine = _GetAudioEngine(engine);\n    return engine.createSoundSourceAsync(name, source, options);\n}\n\n/**\n * Creates a new streaming sound.\n * @param name - The name of the sound.\n * @param source - The source of the sound.\n * @param options - The options for the streaming sound.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created streaming sound.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateStreamingSoundAsync(\n    name: string,\n    source: HTMLMediaElement | string | string[],\n    options: Partial<IStreamingSoundOptions> = {},\n    engine: Nullable<AudioEngineV2> = null\n): Promise<StreamingSound> {\n    engine = _GetAudioEngine(engine);\n    return engine.createStreamingSoundAsync(name, source, options);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAWA,MAAM,SAAS,GAAoB,EAAE,CAAC;AAMhC,SAAU,sBAAsB;IAClC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,OAAO,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AA2BK,MAAgB,aAAa;IAwB/B;;;;OAIG,CACH,IAAW,cAAc,GAAA;QACrB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IA8BD;;;;;;OAMG,CACH,IAAW,qBAAqB,GAAA;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED,IAAW,qBAAqB,CAAC,KAAa,EAAA;QAC1C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAoED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACpC,IAAK,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,CAAE,CAAC;YAC9D,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAEpB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IA8BD;;;;OAIG,CACH,2FAA2F;IACpF,WAAW,GAAA;QACd,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC9B,CAAC;IAES,WAAW,CAAC,OAAqB,EAAA;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE7B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAES,cAAc,CAAC,OAAqB,EAAA;QAC1C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAES,QAAQ,CAAC,IAA4B,EAAA;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAES,WAAW,CAAC,IAA4B,EAAA;QAC9C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAvND,YAAsB,OAAuC,CAAA;QAV7D,mFAAA,EAAqF,CACpE,IAAA,CAAA,UAAU,GAAG,IAAI,GAAG,EAAgB,CAAC;QAEtD,yCAAA,EAA2C,CAC1B,IAAA,CAAA,MAAM,GAAG,IAAI,GAAG,EAA0B,CAAC;QAEpD,IAAA,CAAA,eAAe,GAA2B,IAAI,CAAC;QAE/C,IAAA,CAAA,sBAAsB,GAAW,IAAI,CAAC;QAG1C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErB,IAAI,OAAO,OAAO,CAAC,qBAAqB,KAAK,QAAQ,EAAE,CAAC;YACpD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAC;QAC/D,CAAC;IACL,CAAC;CAkNJ;AAQK,SAAU,eAAe,CAAC,MAA+B;IAC3D,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,MAAM,GAAG,sBAAsB,EAAE,CAAC;IACtC,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACT,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACxC,CAAC;AAUK,SAAU,mBAAmB,CAAC,IAAY;kBAAE,iEAAqC,CAAA,CAAE,WAAE,iEAAkC,IAAI;IAC7H,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;AAUK,SAAU,uBAAuB,CAAC,IAAY;kBAAE,iEAAyC,CAAA,CAAE,WAAE,iEAAkC,IAAI;IACrI,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACpD,CAAC;AAUK,SAAU,gCAAgC,CAAC,IAAY;kBAAE,iEAAwC,CAAA,CAAE,WAAE,iEAAkC,IAAI;IAC7I,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,gCAAgC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC;AAWK,SAAU,gBAAgB,CAC5B,IAAY,EACZ,MAAyE;kBACzE,iEAAwC,CAAA,CAAE,WAC1C,iEAAkC,IAAI;IAEtC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC1D,CAAC;AASM,KAAK,UAAU,sBAAsB,CACxC,MAAyE;kBACzE,iEAA8C,CAAA,CAAE,WAChD,iEAAkC,IAAI;IAEtC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,MAAM,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC;AAWK,SAAU,sBAAsB,CAClC,IAAY,EACZ,MAAiB;kBACjB,iEAAwC,CAAA,CAAE,EAC1C,0EAAkC,IAAI;IAEtC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC;AAWK,SAAU,yBAAyB,CACrC,IAAY,EACZ,MAA4C;kBAC5C,iEAA2C,CAAA,CAAE,WAC7C,iEAAkC,IAAI;IAEtC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,yBAAyB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACnE,CAAC", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subProperties/abstractSpatialAudioListener.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subProperties/abstractSpatialAudioListener.ts"], "sourcesContent": ["import { Quaternion, Vector3 } from \"../../../Maths/math.vector\";\nimport type { Node } from \"../../../node\";\nimport type { Nullable } from \"../../../types\";\nimport type { SpatialAudioAttachmentType } from \"../../spatialAudioAttachmentType\";\n\nexport const _SpatialAudioListenerDefaults = {\n    position: Vector3.Zero(),\n    rotation: Vector3.Zero(),\n    rotationQuaternion: new Quaternion(),\n} as const;\n\n/**\n * Options for spatial audio.\n */\nexport interface ISpatialAudioListenerOptions {\n    /**\n     * Whether to automatically update the position and rotation of the listener. Defaults to `true`.\n     */\n    listenerAutoUpdate: boolean;\n    /**\n     * Set to `true` to enable the listener. Defaults to `false`.\n     */\n    listenerEnabled: boolean;\n    /**\n     * The minimum update time in seconds of the listener if it is attached to a mesh, scene or transform node. Defaults to `0`.\n     * - The listener's position and rotation will not update faster than this time, but they may update slower depending on the frame rate.\n     */\n    listenerMinUpdateTime: number;\n    /**\n     * The listener position. Defaults to (0, 0, 0).\n     */\n    listenerPosition: Vector3;\n    /**\n     * The listener rotation, as Euler angles. Defaults to (0, 0, 0).\n     */\n    listenerRotation: Vector3;\n    /**\n     * The listener rotation, as a quaternion. Defaults to (0, 0, 0, 1).\n     */\n    listenerRotationQuaternion: Quaternion;\n}\n\n/**\n * @param options The spatial audio listener options to check.\n * @returns `true` if spatial audio listener options are defined, otherwise `false`.\n */\nexport function _HasSpatialAudioListenerOptions(options: Partial<ISpatialAudioListenerOptions>): boolean {\n    return (\n        options.listenerEnabled ||\n        options.listenerMinUpdateTime !== undefined ||\n        options.listenerPosition !== undefined ||\n        options.listenerRotation !== undefined ||\n        options.listenerRotationQuaternion !== undefined\n    );\n}\n\n/**\n * Abstract class representing the spatial audio `listener` property on an audio engine.\n *\n * @see {@link AudioEngineV2.listener}\n */\nexport abstract class AbstractSpatialAudioListener {\n    /**\n     * Whether the listener is attached to a camera, mesh or transform node.\n     */\n    public abstract isAttached: boolean;\n\n    /**\n     * The minimum update time in seconds of the listener if it is attached to a mesh, scene or transform node. Defaults to `0`.\n     * - The listener's position and rotation will not update faster than this time, but they may update slower depending on the frame rate.\n     */\n    public abstract minUpdateTime: number;\n\n    /**\n     * The listener position. Defaults to (0, 0, 0).\n     */\n    public abstract position: Vector3;\n\n    /**\n     * The listener rotation, as Euler angles. Defaults to (0, 0, 0).\n     */\n    public abstract rotation: Vector3;\n\n    /**\n     * The listener rotation, as a quaternion. Defaults to (0, 0, 0, 1).\n     */\n    public abstract rotationQuaternion: Quaternion;\n\n    /**\n     * Attaches to a scene node.\n     *\n     * Detaches automatically before attaching to the given scene node.\n     * If `sceneNode` is `null` it is the same as calling `detach()`.\n     *\n     * @param sceneNode The scene node to attach to, or `null` to detach.\n     * @param useBoundingBox Whether to use the bounding box of the node for positioning. Defaults to `false`.\n     * @param attachmentType Whether to attach to the node's position and/or rotation. Defaults to `PositionAndRotation`.\n     */\n    public abstract attach(sceneNode: Nullable<Node>, useBoundingBox?: boolean, attachmentType?: SpatialAudioAttachmentType): void;\n\n    /**\n     * Detaches from the scene node if attached.\n     */\n    public abstract detach(): void;\n\n    /**\n     * Updates the position and rotation of the associated audio engine object in the audio rendering graph.\n     *\n     * This is called automatically by default and only needs to be called manually if automatic updates are disabled.\n     */\n    public abstract update(): void;\n}\n"], "names": [], "mappings": ";;;;;AAAA,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;;AAK1D,MAAM,6BAA6B,GAAG;IACzC,QAAQ,oKAAE,UAAO,CAAC,IAAI,EAAE;IACxB,QAAQ,oKAAE,UAAO,CAAC,IAAI,EAAE;IACxB,kBAAkB,EAAE,sKAAI,aAAU,EAAE;CAC9B,CAAC;AAqCL,SAAU,+BAA+B,CAAC,OAA8C;IAC1F,OAAO,AACH,OAAO,CAAC,eAAe,IACvB,OAAO,CAAC,qBAAqB,KAAK,SAAS,IAC3C,OAAO,CAAC,gBAAgB,KAAK,SAAS,IACtC,OAAO,CAAC,gBAAgB,KAAK,SAAS,IACtC,OAAO,CAAC,0BAA0B,KAAK,SAAS,CACnD,CAAC;AACN,CAAC;AAOK,MAAgB,4BAA4B;CAkDjD", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/components/spatialAudioAttacherComponent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/components/spatialAudioAttacherComponent.ts"], "sourcesContent": ["import { Quaternion, Vector3 } from \"../../../Maths/math.vector\";\nimport type { AbstractMesh } from \"../../../Meshes/abstractMesh\";\nimport type { Node } from \"../../../node\";\nimport type { Nullable } from \"../../../types\";\nimport { SpatialAudioAttachmentType } from \"../../spatialAudioAttachmentType\";\nimport type { _SpatialAudioSubNode } from \"../subNodes/spatialAudioSubNode\";\nimport type { _SpatialAudioListener } from \"../subProperties/spatialAudioListener\";\n\n/**\n * Provides a common interface for attaching an audio listener or source to a specific entity, ensuring only one entity\n * is attached at a time.\n * @internal\n */\nexport class _SpatialAudioAttacherComponent {\n    /** @internal */\n    private _attachmentType: SpatialAudioAttachmentType = SpatialAudioAttachmentType.PositionAndRotation;\n    private readonly _position = new Vector3();\n    private readonly _rotationQuaternion = new Quaternion();\n    private _sceneNode: Nullable<Node> = null;\n    private readonly _spatialAudioNode: _SpatialAudioSubNode | _SpatialAudioListener;\n    private _useBoundingBox: boolean = false;\n\n    /** @internal */\n    public constructor(spatialAudioNode: _SpatialAudioSubNode | _SpatialAudioListener) {\n        this._spatialAudioNode = spatialAudioNode;\n    }\n\n    /**\n     * Returns `true` if attached to a scene node; otherwise returns `false`.\n     */\n    public get isAttached(): boolean {\n        return this._sceneNode !== null;\n    }\n\n    /**\n     * Attaches to a scene node.\n     *\n     * Detaches automatically before attaching to the given scene node.\n     * If `sceneNode` is `null` it is the same as calling `detach()`.\n     *\n     * @param sceneNode The scene node to attach to, or `null` to detach.\n     * @param useBoundingBox Whether to use the scene node's bounding box for positioning. Defaults to `false`.\n     * @param attachmentType Whether to attach to the scene node's position and/or rotation. Defaults to `PositionAndRotation`.\n     */\n    public attach(sceneNode: Nullable<Node>, useBoundingBox: boolean, attachmentType: SpatialAudioAttachmentType): void {\n        if (this._sceneNode === sceneNode) {\n            return;\n        }\n\n        this.detach();\n\n        if (!sceneNode) {\n            return;\n        }\n\n        this._attachmentType = attachmentType;\n\n        this._sceneNode = sceneNode;\n        this._sceneNode.onDisposeObservable.add(this.dispose);\n\n        this._useBoundingBox = useBoundingBox;\n    }\n\n    /**\n     * Detaches from the scene node if attached.\n     */\n    public detach() {\n        this._sceneNode?.onDisposeObservable.removeCallback(this.dispose);\n        this._sceneNode = null;\n    }\n\n    /**\n     * Releases associated resources.\n     */\n    public dispose = () => {\n        this.detach();\n    };\n\n    /**\n     * Updates the position and rotation of the associated audio engine object in the audio rendering graph.\n     *\n     * This is called automatically by default and only needs to be called manually if automatic updates are disabled.\n     */\n    public update() {\n        if (this._attachmentType & SpatialAudioAttachmentType.Position) {\n            if (this._useBoundingBox && (this._sceneNode as AbstractMesh).getBoundingInfo) {\n                this._position.copyFrom((this._sceneNode as AbstractMesh).getBoundingInfo().boundingBox.centerWorld);\n            } else {\n                this._sceneNode?.getWorldMatrix().getTranslationToRef(this._position);\n            }\n\n            this._spatialAudioNode.position.copyFrom(this._position);\n            this._spatialAudioNode._updatePosition();\n        }\n\n        if (this._attachmentType & SpatialAudioAttachmentType.Rotation) {\n            this._sceneNode?.getWorldMatrix().decompose(undefined, this._rotationQuaternion);\n\n            this._spatialAudioNode.rotationQuaternion.copyFrom(this._rotationQuaternion);\n            this._spatialAudioNode._updateRotation();\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;;AAa3D,MAAO,8BAA8B;IAcvC;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC;IACpC,CAAC;IAED;;;;;;;;;OASG,CACI,MAAM,CAAC,SAAyB,EAAE,cAAuB,EAAE,cAA0C,EAAA;QACxG,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO;QACX,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QAEtC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IAC1C,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;;SACT,uBAAI,CAAC,UAAU,sEAAE,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IASD;;;;OAIG,CACI,MAAM,GAAA;QACT,IAAI,IAAI,CAAC,eAAe,GAAA,EAAA,uCAAA,EAAsC,GAAE,CAAC;YAC7D,IAAI,IAAI,CAAC,eAAe,IAAK,IAAI,CAAC,UAA2B,CAAC,eAAe,EAAE,CAAC;gBAC5E,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAE,IAAI,CAAC,UAA2B,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACzG,CAAC,MAAM,CAAC;;wCACA,CAAC,UAAU,qDAAf,iBAAiB,cAAc,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1E,CAAC;YAED,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,GAAA,EAAA,uCAAA,EAAsC,GAAE,CAAC;;qCACzD,CAAC,UAAU,sDAAf,kBAAiB,cAAc,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEjF,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC7E,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,CAAC;QAC7C,CAAC;IACL,CAAC;IA/ED,cAAA,EAAgB,CAChB,YAAmB,gBAA8D,CAAA;QATjF,cAAA,EAAgB,CACR,IAAA,CAAA,eAAe,GAAA,EAAA,kDAAA,GAA8E;QACpF,IAAA,CAAA,SAAS,GAAG,qKAAI,WAAO,EAAE,CAAC;QAC1B,IAAA,CAAA,mBAAmB,GAAG,sKAAI,aAAU,EAAE,CAAC;QAChD,IAAA,CAAA,UAAU,GAAmB,IAAI,CAAC;QAElC,IAAA,CAAA,eAAe,GAAY,KAAK,CAAC;QAmDzC;;WAEG,CACI,IAAA,CAAA,OAAO,GAAG,GAAG,EAAE;YAClB,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC,CAAC;QApDE,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IAC9C,CAAC;CA6EJ", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subProperties/spatialAudioListener.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subProperties/spatialAudioListener.ts"], "sourcesContent": ["import type { Node } from \"../../../node\";\nimport type { Nullable } from \"../../../types\";\nimport { SpatialAudioAttachmentType } from \"../../spatialAudioAttachmentType\";\nimport { _SpatialAudioAttacherComponent } from \"../components/spatialAudioAttacherComponent\";\nimport type { ISpatialAudioListenerOptions } from \"./abstractSpatialAudioListener\";\nimport { _SpatialAudioListenerDefaults, AbstractSpatialAudioListener } from \"./abstractSpatialAudioListener\";\n\n/** @internal */\nexport abstract class _SpatialAudioListener extends AbstractSpatialAudioListener {\n    protected _attacherComponent: Nullable<_SpatialAudioAttacherComponent> = null;\n\n    protected constructor() {\n        super();\n\n        this._attacherComponent = new _SpatialAudioAttacherComponent(this);\n    }\n\n    /** @internal */\n    public get isAttached(): boolean {\n        return this._attacherComponent !== null && this._attacherComponent.isAttached;\n    }\n\n    /**\n     * Attaches to a scene node.\n     *\n     * Detaches automatically before attaching to the given scene node.\n     * If `sceneNode` is `null` it is the same as calling `detach()`.\n     *\n     * @param sceneNode The scene node to attach to, or `null` to detach.\n     * @param useBoundingBox Whether to use the bounding box of the node for positioning. Defaults to `false`.\n     * @param attachmentType Whether to attach to the node's position and/or rotation. Defaults to `PositionAndRotation`.\n     */\n    public attach(sceneNode: Nullable<Node>, useBoundingBox: boolean = false, attachmentType: SpatialAudioAttachmentType = SpatialAudioAttachmentType.PositionAndRotation): void {\n        if (!this._attacherComponent) {\n            this._attacherComponent = new _SpatialAudioAttacherComponent(this);\n        }\n        this._attacherComponent.attach(sceneNode, useBoundingBox, attachmentType);\n    }\n\n    /**\n     * Detaches from the scene node if attached.\n     */\n    public detach(): void {\n        this._attacherComponent?.detach();\n    }\n\n    /** @internal */\n    public dispose(): void {\n        this._attacherComponent?.dispose();\n        this._attacherComponent = null;\n    }\n\n    /** @internal */\n    public setOptions(options: Partial<ISpatialAudioListenerOptions>): void {\n        if (options.listenerMinUpdateTime !== undefined) {\n            this.minUpdateTime = options.listenerMinUpdateTime;\n        }\n\n        if (options.listenerPosition) {\n            this.position = options.listenerPosition.clone();\n        }\n\n        if (options.listenerRotationQuaternion) {\n            this.rotationQuaternion = options.listenerRotationQuaternion.clone();\n        } else if (options.listenerRotation) {\n            this.rotation = options.listenerRotation.clone();\n        } else {\n            this.rotationQuaternion = _SpatialAudioListenerDefaults.rotationQuaternion.clone();\n        }\n\n        this.update();\n    }\n\n    public abstract _updatePosition(): void;\n    public abstract _updateRotation(): void;\n}\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,8BAA8B,EAAE,MAAM,6CAA6C,CAAC;AAE7F,OAAO,EAAE,6BAA6B,EAAE,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;;;AAGvG,MAAgB,qBAAsB,6NAAQ,+BAA4B;IAS5E,cAAA,EAAgB,CAChB,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,kBAAkB,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;IAClF,CAAC;IAED;;;;;;;;;OASG,CACI,MAAM,CAAC,SAAyB,EAA8H;6BAA5H,iEAA0B,KAAK,mBAAE,gDAAA,kDAAA,EAA2F,mBAA3F;QACtE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,GAAG,sNAAI,iCAA8B,CAAC,IAAI,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;;wCACL,CAAC,kBAAkB,6DAAvB,yBAAyB,MAAM,EAAE,CAAC;IACtC,CAAC;IAED,cAAA,EAAgB,CACT,OAAO,GAAA;;oCACV,IAAI,CAAC,kBAAkB,sFAAE,OAAO,EAAE,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CACT,UAAU,CAAC,OAA8C,EAAA;QAC5D,IAAI,OAAO,CAAC,qBAAqB,KAAK,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,qBAAqB,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACrD,CAAC;QAED,IAAI,OAAO,CAAC,0BAA0B,EAAE,CAAC;YACrC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACzE,CAAC,MAAM,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACrD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,uNAAG,gCAA6B,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QACvF,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IA5DD,aAAA;QACI,KAAK,EAAE,CAAC;QAHF,IAAA,CAAA,kBAAkB,GAA6C,IAAI,CAAC;QAK1E,IAAI,CAAC,kBAAkB,GAAG,sNAAI,iCAA8B,CAAC,IAAI,CAAC,CAAC;IACvE,CAAC;CA4DJ", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/components/spatialWebAudioUpdaterComponent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/components/spatialWebAudioUpdaterComponent.ts"], "sourcesContent": ["import { PrecisionDate } from \"../../../Misc/precisionDate\";\n\n/** @internal */\nexport class _SpatialWebAudioUpdaterComponent {\n    private _autoUpdate = true;\n    private _lastUpdateTime: number = 0;\n\n    /** @internal */\n    public minUpdateTime = 0;\n\n    /** @internal */\n    public constructor(parent: { update: () => void }, autoUpdate: boolean, minUpdateTime: number) {\n        if (!autoUpdate) {\n            return;\n        }\n\n        this.minUpdateTime = minUpdateTime;\n\n        const update = () => {\n            if (!this._autoUpdate) {\n                return;\n            }\n\n            let skipUpdate = false;\n\n            if (0 < this.minUpdateTime) {\n                const now = PrecisionDate.Now;\n                if (this._lastUpdateTime && now - this._lastUpdateTime < this.minUpdateTime) {\n                    skipUpdate = true;\n                }\n                this._lastUpdateTime = now;\n            }\n\n            if (!skipUpdate) {\n                parent.update();\n            }\n\n            requestAnimationFrame(update);\n        };\n        requestAnimationFrame(update);\n    }\n\n    /** @internal */\n    public dispose(): void {\n        this._autoUpdate = false;\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;;AAGtD,MAAO,gCAAgC;IAuCzC,cAAA,EAAgB,CACT,OAAO,GAAA;QACV,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAnCD,cAAA,EAAgB,CAChB,YAAmB,MAA8B,EAAE,UAAmB,EAAE,aAAqB,CAAA;QAPrF,IAAA,CAAA,WAAW,GAAG,IAAI,CAAC;QACnB,IAAA,CAAA,eAAe,GAAW,CAAC,CAAC;QAEpC,cAAA,EAAgB,CACT,IAAA,CAAA,aAAa,GAAG,CAAC,CAAC;QAIrB,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,MAAM,MAAM,GAAG,GAAG,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACpB,OAAO;YACX,CAAC;YAED,IAAI,UAAU,GAAG,KAAK,CAAC;YAEvB,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACzB,MAAM,GAAG,mKAAG,gBAAa,CAAC,GAAG,CAAC;gBAC9B,IAAI,IAAI,CAAC,eAAe,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;oBAC1E,UAAU,GAAG,IAAI,CAAC;gBACtB,CAAC;gBACD,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;YAC/B,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,MAAM,CAAC,MAAM,EAAE,CAAC;YACpB,CAAC;YAED,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC,CAAC;QACF,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;CAMJ", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/audioUtils.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/audioUtils.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\nimport { AudioParameterRampShape } from \"./audioParameter\";\n\nexport const _FileExtensionRegex = new RegExp(\"\\\\.(\\\\w{3,4})($|\\\\?)\");\n\nconst CurveLength = 100;\n\nconst TmpLineValues = new Float32Array([0, 0]);\nlet TmpCurveValues: Nullable<Float32Array> = null;\n\nlet ExpCurve: Nullable<Float32Array> = null;\nlet LogCurve: Nullable<Float32Array> = null;\n\n/**\n * @returns A Float32Array representing an exponential ramp from (0, 0) to (1, 1).\n */\nfunction GetExpCurve(): Float32Array {\n    if (!ExpCurve) {\n        ExpCurve = new Float32Array(CurveLength);\n\n        const increment = 1 / (CurveLength - 1);\n        let x = increment;\n        for (let i = 1; i < CurveLength; i++) {\n            ExpCurve[i] = Math.exp(-11.512925464970227 * (1 - x));\n            x += increment;\n        }\n    }\n\n    return ExpCurve;\n}\n\n/**\n * @returns A Float32Array representing a logarithmic ramp from (0, 0) to (1, 1).\n */\nfunction GetLogCurve(): Float32Array {\n    if (!LogCurve) {\n        LogCurve = new Float32Array(CurveLength);\n\n        const increment = 1 / CurveLength;\n        let x = increment;\n        for (let i = 0; i < CurveLength; i++) {\n            LogCurve[i] = 1 + Math.log10(x) / Math.log10(CurveLength);\n            x += increment;\n        }\n    }\n\n    return LogCurve;\n}\n\n/** @internal */\nexport function _GetAudioParamCurveValues(shape: AudioParameterRampShape, from: number, to: number): Float32Array {\n    if (!TmpCurveValues) {\n        TmpCurveValues = new Float32Array(CurveLength);\n    }\n\n    let normalizedCurve: Float32Array;\n\n    if (shape === AudioParameterRampShape.Linear) {\n        TmpLineValues[0] = from;\n        TmpLineValues[1] = to;\n        return TmpLineValues;\n    } else if (shape === AudioParameterRampShape.Exponential) {\n        normalizedCurve = GetExpCurve();\n    } else if (shape === AudioParameterRampShape.Logarithmic) {\n        normalizedCurve = GetLogCurve();\n    } else {\n        throw new Error(`Unknown ramp shape: ${shape}`);\n    }\n\n    const direction = Math.sign(to - from);\n    const range = Math.abs(to - from);\n\n    if (direction === 1) {\n        for (let i = 0; i < normalizedCurve.length; i++) {\n            TmpCurveValues[i] = from + range * normalizedCurve[i];\n        }\n    } else {\n        let j = CurveLength - 1;\n        for (let i = 0; i < normalizedCurve.length; i++, j--) {\n            TmpCurveValues[i] = from - range * (1 - normalizedCurve[j]);\n        }\n    }\n\n    return TmpCurveValues;\n}\n\n/** @internal */\nexport function _CleanUrl(url: string) {\n    return url.replace(/#/gm, \"%23\");\n}\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,mBAAmB,GAAG,IAAI,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAEtE,MAAM,WAAW,GAAG,GAAG,CAAC;AAExB,MAAM,aAAa,GAAG,IAAI,YAAY,CAAC;IAAC,CAAC;IAAE,CAAC;CAAC,CAAC,CAAC;AAC/C,IAAI,cAAc,GAA2B,IAAI,CAAC;AAElD,IAAI,QAAQ,GAA2B,IAAI,CAAC;AAC5C,IAAI,QAAQ,GAA2B,IAAI,CAAC;AAE5C;;GAEG,CACH,SAAS,WAAW;IAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,QAAQ,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;QAEzC,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,GAAG,SAAS,CAAC;QAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC;YACnC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,IAAI,SAAS,CAAC;QACnB,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;GAEG,CACH,SAAS,WAAW;IAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,QAAQ,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;QAEzC,MAAM,SAAS,GAAG,CAAC,GAAG,WAAW,CAAC;QAClC,IAAI,CAAC,GAAG,SAAS,CAAC;QAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC;YACnC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC1D,CAAC,IAAI,SAAS,CAAC;QACnB,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC;AAGK,SAAU,yBAAyB,CAAC,KAA8B,EAAE,IAAY,EAAE,EAAU;IAC9F,IAAI,CAAC,cAAc,EAAE,CAAC;QAClB,cAAc,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,eAA6B,CAAC;IAElC,IAAI,KAAK,KAAA,SAAA,kCAAA,EAAmC,GAAE,CAAC;QAC3C,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QACxB,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACtB,OAAO,aAAa,CAAC;IACzB,CAAC,MAAM,IAAI,KAAK,KAAA,cAAA,uCAAA,EAAwC,GAAE,CAAC;QACvD,eAAe,GAAG,WAAW,EAAE,CAAC;IACpC,CAAC,MAAM,IAAI,KAAK,KAAA,cAAA,uCAAA,EAAwC,GAAE,CAAC;QACvD,eAAe,GAAG,WAAW,EAAE,CAAC;IACpC,CAAC,MAAM,CAAC;QACJ,MAAM,IAAI,KAAK,CAAC,uBAA4B,CAAE,CAAC,CAAC,IAAT,KAAK;IAChD,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;IACvC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;IAElC,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;QAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC9C,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC,MAAM,CAAC;QACJ,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC;QACxB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC;YACnD,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAED,OAAO,cAAc,CAAC;AAC1B,CAAC;AAGK,SAAU,SAAS,CAAC,GAAW;IACjC,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACrC,CAAC", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/components/webAudioParameterComponent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/components/webAudioParameterComponent.ts"], "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { IAudioParameterRampOptions } from \"../../audioParameter\";\nimport { AudioParameterRampShape } from \"../../audioParameter\";\nimport { _GetAudioParamCurveValues } from \"../../audioUtils\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\n\n/**\n * Maximum time in seconds to wait for an active ramp to finish before starting a new ramp.\n *\n * New ramps will throw an error if the active ramp has more than this amount of time remaining.\n *\n * This is needed because short ramps are used to avoid pops and clicks when setting audio parameters, and we\n * don't want to throw an error if a short ramp is active.\n *\n * This constant is set to 11 milliseconds, which is short enough to avoid perceptual differences in most cases, but\n * long enough to allow for short ramps to be completed in a reasonable time frame.\n */\nconst MaxWaitTime = 0.011;\n\n/**\n * Minimum duration in seconds for a ramp to be considered valid.\n *\n * If the duration is less than this value, the value will be set immediately instead of being ramped smoothly since\n * there is no perceptual difference for such short durations, so a ramp is not needed.\n */\nconst MinRampDuration = 0.000001;\n\n/** @internal */\nexport class _WebAudioParameterComponent {\n    private _deferredRampOptions = {\n        duration: 0,\n        shape: AudioParameterRampShape.Linear,\n    };\n    private _deferredTargetValue = -1;\n    private _isObservingUpdates = false;\n    private _rampEndTime: number = 0;\n    private _engine: _WebAudioEngine;\n    private _param: AudioParam;\n    private _targetValue: number;\n\n    /** @internal */\n    constructor(engine: _WebAudioEngine, param: AudioParam) {\n        this._engine = engine;\n        this._param = param;\n        this._targetValue = param.value;\n    }\n\n    /** @internal */\n    public get isRamping(): boolean {\n        return this._engine.currentTime < this._rampEndTime;\n    }\n\n    /** @internal */\n    public get targetValue(): number {\n        return this._targetValue;\n    }\n\n    public set targetValue(value: number) {\n        this.setTargetValue(value);\n    }\n\n    /** @internal */\n    public get value(): number {\n        return this._param.value;\n    }\n\n    /** @internal */\n    public dispose(): void {\n        this._clearDeferredRamp();\n\n        this._param = null!;\n        this._engine = null!;\n    }\n\n    /**\n     * Sets the target value of the audio parameter with an optional ramping duration and shape.\n     *\n     * If a ramp is close to finishing, it will wait for the ramp to finish before setting the new value; otherwise it\n     * will throw an error because of a bug in Firefox that prevents active ramps from being cancelled with\n     * `cancelScheduledValues`. See https://bugzilla.mozilla.org/show_bug.cgi?id=1752775. Other browsers do not have\n     * this issue, but we throw an error in all browsers to ensure consistent behavior.\n     *\n     * There are other similar WebAudio APIs for ramping parameters, (e.g. `linearRampToValueAtTime` and\n     * `exponentialRampToValueAtTime`) but they don't work in Firefox and Meta Quest Chrome.\n     *\n     * It may be better in the long run to implement our own ramping logic with a WASM audio worklet instead of using\n     * `setValueCurveAtTime`. Another alternative is to use `setValueAtTime` wtih a custom shape, but that will\n     * probably be a performance hit to maintain quality at audio rates.\n     *\n     * @internal\n     */\n    public setTargetValue(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        if (this._targetValue === value) {\n            return;\n        }\n\n        const shape = typeof options?.shape === \"string\" ? options.shape : AudioParameterRampShape.Linear;\n\n        let duration = typeof options?.duration === \"number\" ? Math.max(options.duration, this._engine.parameterRampDuration) : this._engine.parameterRampDuration;\n        const startTime = this._engine.currentTime;\n\n        if (startTime < this._rampEndTime) {\n            const timeLeft = this._rampEndTime - startTime;\n\n            if (MaxWaitTime < timeLeft) {\n                throw new Error(\"Audio parameter not set. Wait for current ramp to finish.\");\n            } else {\n                this._deferRamp(value, duration, shape);\n                return;\n            }\n        }\n\n        if ((duration = Math.max(this._engine.parameterRampDuration, duration)) < MinRampDuration) {\n            this._param.setValueAtTime((this._targetValue = value), startTime);\n            return;\n        }\n\n        this._param.cancelScheduledValues(startTime);\n        this._param.setValueCurveAtTime(_GetAudioParamCurveValues(shape, this._targetValue, (this._targetValue = value)), startTime, duration);\n\n        this._clearDeferredRamp();\n\n        this._rampEndTime = startTime + duration;\n    }\n\n    private _deferRamp(value: number, duration: number, shape: AudioParameterRampShape): void {\n        this._deferredRampOptions.duration = duration;\n        this._deferredRampOptions.shape = shape;\n        this._deferredTargetValue = value;\n\n        if (!this._isObservingUpdates) {\n            this._engine._addUpdateObserver(this._applyDeferredRamp);\n            this._isObservingUpdates = true;\n        }\n    }\n\n    private _applyDeferredRamp = () => {\n        if (0 < this._deferredRampOptions.duration && this._rampEndTime < this._engine.currentTime) {\n            this.setTargetValue(this._deferredTargetValue, this._deferredRampOptions);\n        }\n    };\n\n    private _clearDeferredRamp(): void {\n        this._deferredRampOptions.duration = 0;\n\n        if (this._isObservingUpdates) {\n            this._engine._removeUpdateObserver(this._applyDeferredRamp);\n            this._isObservingUpdates = false;\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,yBAAyB,EAAE,MAAM,kBAAkB,CAAC;;AAG7D;;;;;;;;;;GAUG,CACH,MAAM,WAAW,GAAG,KAAK,CAAC;AAE1B;;;;;GAKG,CACH,MAAM,eAAe,GAAG,QAAQ,CAAC;AAG3B,MAAO,2BAA2B;IAmBpC,cAAA,EAAgB,CAChB,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;IACxD,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED,cAAA,EAAgB,CACT,OAAO,GAAA;QACV,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,CAAC,MAAM,GAAG,IAAK,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,IAAK,CAAC;IACzB,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG,CACI,cAAc,CAAC,KAAa,EAA+D;sBAA7D,iEAAyD,IAAI;QAC9F,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,4BAAO,OAAO,+BAAE,KAAK,MAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA,SAAA,kCAAA,EAA+B,CAAC;QAElG,IAAI,QAAQ,GAAG,0DAAO,OAAO,CAAE,QAAQ,MAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;QAC3J,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;QAE3C,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAE/C,IAAI,WAAW,GAAG,QAAQ,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;YACjF,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBACxC,OAAO;YACX,CAAC;QACL,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC,GAAG,eAAe,EAAE,CAAC;YACxF,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,AAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAE,SAAS,CAAC,CAAC;YACnE,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,mBAAmB,qKAAC,4BAAyB,AAAzB,EAA0B,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,AAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,CAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAEvI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,CAAC,YAAY,GAAG,SAAS,GAAG,QAAQ,CAAC;IAC7C,CAAC;IAEO,UAAU,CAAC,KAAa,EAAE,QAAgB,EAAE,KAA8B,EAAA;QAC9E,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC9C,IAAI,CAAC,oBAAoB,CAAC,KAAK,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;IACL,CAAC;IAQO,kBAAkB,GAAA;QACtB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,CAAC,CAAC;QAEvC,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC5D,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACrC,CAAC;IACL,CAAC;IA7GD,cAAA,EAAgB,CAChB,YAAY,MAAuB,EAAE,KAAiB,CAAA;QAZ9C,IAAA,CAAA,oBAAoB,GAAG;YAC3B,QAAQ,EAAE,CAAC;YACX,KAAK,EAAA,SAAA,kCAAA,EAAgC;SACxC,CAAC;QACM,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,YAAY,GAAW,CAAC,CAAC;QAqGzB,IAAA,CAAA,kBAAkB,GAAG,GAAG,EAAE;YAC9B,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBACzF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC,CAAC;QAlGE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC;IACpC,CAAC;CAyGJ", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/subProperties/spatialWebAudioListener.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/subProperties/spatialWebAudioListener.ts"], "sourcesContent": ["import { Matrix, Quaternion, Vector3 } from \"../../../Maths/math.vector\";\nimport { _SpatialAudioListener } from \"../../abstractAudio/subProperties/spatialAudioListener\";\nimport { _SpatialWebAudioUpdaterComponent } from \"../components/spatialWebAudioUpdaterComponent\";\nimport { _WebAudioParameterComponent } from \"../components/webAudioParameterComponent\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\n\nconst TmpMatrix = Matrix.Zero();\nconst TmpQuaternion = new Quaternion();\nconst TmpVector1 = Vector3.Zero();\nconst TmpVector2 = Vector3.Zero();\n\n/** @internal */\nexport function _CreateSpatialAudioListener(engine: _WebAudioEngine, autoUpdate: boolean, minUpdateTime: number): _SpatialAudioListener {\n    const listener = engine._audioContext.listener;\n    if (\n        listener.forwardX &&\n        listener.forwardY &&\n        listener.forwardZ &&\n        listener.positionX &&\n        listener.positionY &&\n        listener.positionZ &&\n        listener.upX &&\n        listener.upY &&\n        listener.upZ\n    ) {\n        return new _SpatialWebAudioListener(engine, autoUpdate, minUpdateTime);\n    } else {\n        return new _SpatialWebAudioListenerFallback(engine, autoUpdate, minUpdateTime);\n    }\n}\n\nabstract class _AbstractSpatialWebAudioListener extends _SpatialAudioListener {\n    protected readonly _listener: AudioListener;\n\n    protected _lastPosition: Vector3 = Vector3.Zero();\n    protected _lastRotation: Vector3 = Vector3.Zero();\n    protected _lastRotationQuaternion: Quaternion = new Quaternion();\n    protected _updaterComponent: _SpatialWebAudioUpdaterComponent;\n\n    /** @internal */\n    public readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public readonly position: Vector3 = Vector3.Zero();\n    /** @internal */\n    public readonly rotation: Vector3 = Vector3.Zero();\n    /** @internal */\n    public readonly rotationQuaternion: Quaternion = new Quaternion();\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine, autoUpdate: boolean, minUpdateTime: number) {\n        super();\n\n        this._listener = engine._audioContext.listener;\n        this.engine = engine;\n\n        this._updaterComponent = new _SpatialWebAudioUpdaterComponent(this, autoUpdate, minUpdateTime);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._updaterComponent.dispose();\n        this._updaterComponent = null!;\n    }\n\n    /** @internal */\n    public get minUpdateTime(): number {\n        return this._updaterComponent.minUpdateTime;\n    }\n\n    /** @internal */\n    public set minUpdateTime(value: number) {\n        this._updaterComponent.minUpdateTime = value;\n    }\n\n    /** @internal */\n    public update(): void {\n        if (this.isAttached) {\n            this._attacherComponent?.update();\n        } else {\n            this._updatePosition();\n            this._updateRotation();\n        }\n    }\n\n    public _updatePosition(): void {\n        if (this._lastPosition.equalsWithEpsilon(this.position)) {\n            return;\n        }\n\n        this._setWebAudioPosition(this.position);\n\n        this._lastPosition.copyFrom(this.position);\n    }\n\n    public _updateRotation(): void {\n        if (!this._lastRotationQuaternion.equalsWithEpsilon(this.rotationQuaternion)) {\n            TmpQuaternion.copyFrom(this.rotationQuaternion);\n            this._lastRotationQuaternion.copyFrom(this.rotationQuaternion);\n        } else if (!this._lastRotation.equalsWithEpsilon(this.rotation)) {\n            Quaternion.FromEulerAnglesToRef(this.rotation.x, this.rotation.y, this.rotation.z, TmpQuaternion);\n            this._lastRotation.copyFrom(this.rotation);\n        } else {\n            return;\n        }\n\n        Matrix.FromQuaternionToRef(TmpQuaternion, TmpMatrix);\n\n        // NB: The WebAudio API is right-handed.\n        Vector3.TransformNormalToRef(Vector3.RightHandedForwardReadOnly, TmpMatrix, TmpVector1);\n        Vector3.TransformNormalToRef(Vector3.Up(), TmpMatrix, TmpVector2);\n\n        this._setWebAudioOrientation(TmpVector1, TmpVector2);\n    }\n\n    protected abstract _setWebAudioPosition(position: Vector3): void;\n    protected abstract _setWebAudioOrientation(forward: Vector3, up: Vector3): void;\n}\n\n/**\n * Full-featured spatial audio listener for the Web Audio API.\n *\n * Used in browsers that support the `forwardX/Y/Z`, `positionX/Y/Z`, and `upX/Y/Z` properties on the AudioContext listener.\n *\n * NB: Firefox falls back to using this implementation.\n *\n * @see _SpatialWebAudioListenerFallback for the implementation used if only `setPosition` and `setOrientation` are available.\n *\n * NB: This sub property is not backed by a sub node and all properties are set directly on the audio context listener.\n *\n * @internal\n */\nclass _SpatialWebAudioListener extends _AbstractSpatialWebAudioListener {\n    private _forwardX: _WebAudioParameterComponent;\n    private _forwardY: _WebAudioParameterComponent;\n    private _forwardZ: _WebAudioParameterComponent;\n    private _positionX: _WebAudioParameterComponent;\n    private _positionY: _WebAudioParameterComponent;\n    private _positionZ: _WebAudioParameterComponent;\n    private _upX: _WebAudioParameterComponent;\n    private _upY: _WebAudioParameterComponent;\n    private _upZ: _WebAudioParameterComponent;\n\n    public constructor(engine: _WebAudioEngine, autoUpdate: boolean, minUpdateTime: number) {\n        super(engine, autoUpdate, minUpdateTime);\n\n        const listener = engine._audioContext.listener;\n        this._forwardX = new _WebAudioParameterComponent(engine, listener.forwardX);\n        this._forwardY = new _WebAudioParameterComponent(engine, listener.forwardY);\n        this._forwardZ = new _WebAudioParameterComponent(engine, listener.forwardZ);\n        this._positionX = new _WebAudioParameterComponent(engine, listener.positionX);\n        this._positionY = new _WebAudioParameterComponent(engine, listener.positionY);\n        this._positionZ = new _WebAudioParameterComponent(engine, listener.positionZ);\n        this._upX = new _WebAudioParameterComponent(engine, listener.upX);\n        this._upY = new _WebAudioParameterComponent(engine, listener.upY);\n        this._upZ = new _WebAudioParameterComponent(engine, listener.upZ);\n    }\n\n    protected override _setWebAudioPosition(position: Vector3): void {\n        // If attached and there is a ramp in progress, we assume another update is coming soon that we can wait for.\n        // We don't do this for unattached nodes because there may not be another update coming.\n        if (this.isAttached && (this._positionX.isRamping || this._positionY.isRamping || this._positionZ.isRamping)) {\n            return;\n        }\n\n        this._positionX.targetValue = position.x;\n        this._positionY.targetValue = position.y;\n        this._positionZ.targetValue = position.z;\n    }\n\n    protected override _setWebAudioOrientation(forward: Vector3, up: Vector3): void {\n        // If attached and there is a ramp in progress, we assume another update is coming soon that we can wait for.\n        // We don't do this for unattached nodes because there may not be another update coming.\n        if (\n            this.isAttached &&\n            (this._forwardX.isRamping || this._forwardY.isRamping || this._forwardZ.isRamping || this._upX.isRamping || this._upY.isRamping || this._upZ.isRamping)\n        ) {\n            return;\n        }\n\n        this._forwardX.targetValue = forward.x;\n        this._forwardY.targetValue = forward.y;\n        this._forwardZ.targetValue = forward.z;\n\n        this._upX.targetValue = up.x;\n        this._upY.targetValue = up.y;\n        this._upZ.targetValue = up.z;\n    }\n}\n\n/**\n * Fallback spatial audio listener for the Web Audio API.\n *\n * Used in browsers that do not support the `forwardX/Y/Z`, `positionX/Y/Z`, and `upX/Y/Z` properties on the\n * AudioContext listener.\n *\n * @see _SpatialWebAudioListener for the implementation used if the `forwardX/Y/Z`, `positionX/Y/Z`, and `upX/Y/Z`\n * properties are available.\n *\n * NB: This sub property is not backed by a sub node and all properties are set directly on the audio context listener.\n *\n * @internal\n */\nclass _SpatialWebAudioListenerFallback extends _AbstractSpatialWebAudioListener {\n    protected override _setWebAudioPosition(position: Vector3): void {\n        this._listener.setPosition(position.x, position.y, position.z);\n    }\n\n    protected override _setWebAudioOrientation(forward: Vector3, up: Vector3): void {\n        this._listener.setOrientation(forward.x, forward.y, forward.z, up.x, up.y, up.z);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AACzE,OAAO,EAAE,qBAAqB,EAAE,MAAM,wDAAwD,CAAC;AAC/F,OAAO,EAAE,gCAAgC,EAAE,MAAM,+CAA+C,CAAC;AACjG,OAAO,EAAE,2BAA2B,EAAE,MAAM,0CAA0C,CAAC;;;;;AAGvF,MAAM,SAAS,qKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;AAChC,MAAM,aAAa,GAAG,sKAAI,aAAU,EAAE,CAAC;AACvC,MAAM,UAAU,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;AAClC,MAAM,UAAU,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;AAG5B,SAAU,2BAA2B,CAAC,MAAuB,EAAE,UAAmB,EAAE,aAAqB;IAC3G,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IACI,QAAQ,CAAC,QAAQ,IACjB,QAAQ,CAAC,QAAQ,IACjB,QAAQ,CAAC,QAAQ,IACjB,QAAQ,CAAC,SAAS,IAClB,QAAQ,CAAC,SAAS,IAClB,QAAQ,CAAC,SAAS,IAClB,QAAQ,CAAC,GAAG,IACZ,QAAQ,CAAC,GAAG,IACZ,QAAQ,CAAC,GAAG,EACd,CAAC;QACC,OAAO,IAAI,wBAAwB,CAAC,MAAM,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IAC3E,CAAC,MAAM,CAAC;QACJ,OAAO,IAAI,gCAAgC,CAAC,MAAM,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IACnF,CAAC;AACL,CAAC;AAED,MAAe,gCAAiC,SAAQ,oOAAqB;IA4BzE,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,IAAK,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;IAChD,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,aAAa,CAAC,KAAa,EAAA;QAClC,IAAI,CAAC,iBAAiB,CAAC,aAAa,GAAG,KAAK,CAAC;IACjD,CAAC;IAED,cAAA,EAAgB,CACT,MAAM,GAAA;QACT,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;;4CACd,CAAC,kBAAkB,6DAAvB,yBAAyB,MAAM,EAAE,CAAC;QACtC,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC;IACL,CAAC;IAEM,eAAe,GAAA;QAClB,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEzC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAEM,eAAe,GAAA;QAClB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC3E,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnE,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;8KAC9D,aAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;YAClG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,MAAM,CAAC;YACJ,OAAO;QACX,CAAC;0KAED,SAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAErD,wCAAwC;0KACxC,UAAO,CAAC,oBAAoB,mKAAC,UAAO,CAAC,0BAA0B,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;0KACxF,UAAO,CAAC,oBAAoB,kKAAC,WAAO,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAElE,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACzD,CAAC;IAlED,cAAA,EAAgB,CAChB,YAAmB,MAAuB,EAAE,UAAmB,EAAE,aAAqB,CAAA;QAClF,KAAK,EAAE,CAAC;QAjBF,IAAA,CAAA,aAAa,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACxC,IAAA,CAAA,aAAa,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACxC,IAAA,CAAA,uBAAuB,GAAe,sKAAI,aAAU,EAAE,CAAC;QAMjE,cAAA,EAAgB,CACA,IAAA,CAAA,QAAQ,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACnD,cAAA,EAAgB,CACA,IAAA,CAAA,QAAQ,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACnD,cAAA,EAAgB,CACA,IAAA,CAAA,kBAAkB,GAAe,sKAAI,aAAU,EAAE,CAAC;QAM9D,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,iBAAiB,GAAG,mNAAI,mCAAgC,CAAC,IAAI,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IACnG,CAAC;CA8DJ;AAED;;;;;;;;;;;;GAYG,CACH,MAAM,wBAAyB,SAAQ,gCAAgC;IA0BhD,oBAAoB,CAAC,QAAiB,EAAA;QACrD,6GAA6G;QAC7G,wFAAwF;QACxF,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3G,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;IAC7C,CAAC;IAEkB,uBAAuB,CAAC,OAAgB,EAAE,EAAW,EAAA;QACpE,6GAA6G;QAC7G,wFAAwF;QACxF,IACI,IAAI,CAAC,UAAU,IACf,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EACzJ,CAAC;YACC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC;QAEvC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;IA5CD,YAAmB,MAAuB,EAAE,UAAmB,EAAE,aAAqB,CAAA;QAClF,KAAK,CAAC,MAAM,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;QAEzC,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,UAAU,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC9E,IAAI,CAAC,UAAU,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC9E,IAAI,CAAC,UAAU,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC9E,IAAI,CAAC,IAAI,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;IACtE,CAAC;CAgCJ;AAED;;;;;;;;;;;;GAYG,CACH,MAAM,gCAAiC,SAAQ,gCAAgC;IACxD,oBAAoB,CAAC,QAAiB,EAAA;QACrD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC;IAEkB,uBAAuB,CAAC,OAAgB,EAAE,EAAW,EAAA;QACpE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACrF,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/abstractAudioNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/abstractAudioNode.ts"], "sourcesContent": ["import { Observable } from \"../../Misc/observable\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\n\nexport const enum AudioNodeType {\n    HAS_INPUTS = 1,\n    HAS_OUTPUTS = 2,\n    HAS_INPUTS_AND_OUTPUTS = 3,\n}\n\n/**\n * Abstract class for an audio node.\n *\n * An audio node is a processing unit that can receive audio data from an upstream node and/or send audio data to a\n * downstream node.\n *\n * Nodes can be connected to other nodes to create an audio graph. The audio graph represents the flow of audio data.\n *\n * There are 3 types of audio nodes:\n * 1. Input: Receives audio data from upstream nodes.\n * 2. Output: Sends audio data to downstream nodes.\n * 3. Input/Output: Receives audio data from upstream nodes and sends audio data to downstream nodes.\n */\nexport abstract class AbstractAudioNode {\n    /**\n     * The connected downstream audio nodes.\n     * - Undefined for input nodes.\n     */\n    protected readonly _downstreamNodes?: Set<AbstractAudioNode>;\n\n    /**\n     * The connected upstream audio nodes.\n     * - Undefined for output nodes.\n     */\n    protected readonly _upstreamNodes?: Set<AbstractAudioNode>;\n\n    /**\n     * The audio engine this node belongs to.\n     */\n    public readonly engine: AudioEngineV2;\n\n    /**\n     * Observable for when the audio node is disposed.\n     */\n    public readonly onDisposeObservable = new Observable<AbstractAudioNode>();\n\n    protected constructor(engine: AudioEngineV2, nodeType: AudioNodeType) {\n        this.engine = engine;\n\n        if (nodeType & AudioNodeType.HAS_INPUTS) {\n            this._upstreamNodes = new Set<AbstractAudioNode>();\n        }\n\n        if (nodeType & AudioNodeType.HAS_OUTPUTS) {\n            this._downstreamNodes = new Set<AbstractAudioNode>();\n        }\n    }\n\n    /**\n     * Releases associated resources.\n     * - Triggers `onDisposeObservable`.\n     * @see {@link onDisposeObservable}\n     */\n    public dispose(): void {\n        if (this._downstreamNodes) {\n            for (const node of Array.from(this._downstreamNodes)) {\n                if (!this._disconnect(node)) {\n                    throw new Error(\"Disconnect failed\");\n                }\n            }\n            this._downstreamNodes.clear();\n        }\n\n        if (this._upstreamNodes) {\n            for (const node of Array.from(this._upstreamNodes)) {\n                if (!node._disconnect(this)) {\n                    throw new Error(\"Disconnect failed\");\n                }\n            }\n            this._upstreamNodes.clear();\n        }\n\n        this.onDisposeObservable.notifyObservers(this);\n        this.onDisposeObservable.clear();\n    }\n\n    /**\n     * Gets a string identifying the name of the class\n     * @returns the class's name as a string\n     */\n    public abstract getClassName(): string;\n\n    /**\n     * Connect to a downstream audio input node.\n     * @param node - The downstream audio input node to connect\n     * @returns `true` if the node is successfully connected; otherwise `false`\n     */\n    protected _connect(node: AbstractAudioNode): boolean {\n        if (!this._downstreamNodes) {\n            return false;\n        }\n\n        if (this._downstreamNodes.has(node)) {\n            return false;\n        }\n\n        if (!node._onConnect(this)) {\n            return false;\n        }\n\n        this._downstreamNodes.add(node);\n\n        return true;\n    }\n\n    /**\n     * Disconnects a downstream audio input node.\n     * @param node - The downstream audio input node to disconnect\n     * @returns `true` if the node is successfully disconnected; otherwise `false`\n     */\n    protected _disconnect(node: AbstractAudioNode): boolean {\n        if (!this._downstreamNodes) {\n            return false;\n        }\n\n        if (!this._downstreamNodes.delete(node)) {\n            return false;\n        }\n\n        return node._onDisconnect(this);\n    }\n\n    /**\n     * Called when an upstream audio output node is connecting.\n     * @param node - The connecting upstream audio node\n     * @returns `true` if the node is successfully connected; otherwise `false`\n     */\n    private _onConnect(node: AbstractAudioNode): boolean {\n        if (!this._upstreamNodes) {\n            return false;\n        }\n\n        if (this._upstreamNodes.has(node)) {\n            return false;\n        }\n\n        this._upstreamNodes.add(node);\n\n        return true;\n    }\n\n    /**\n     * Called when an upstream audio output node disconnects.\n     * @param node - The disconnecting upstream audio node\n     * @returns `true` if node is sucessfully disconnected; otherwise `false`\n     */\n    private _onDisconnect(node: AbstractAudioNode): boolean {\n        return this._upstreamNodes?.delete(node) ?? false;\n    }\n}\n\n/**\n * Abstract class for a named audio node.\n */\nexport abstract class AbstractNamedAudioNode extends AbstractAudioNode {\n    private _name: string;\n\n    /**\n     * Observable for when the audio node is renamed.\n     */\n    public readonly onNameChangedObservable = new Observable<{ newName: string; oldName: string; node: AbstractNamedAudioNode }>();\n\n    protected constructor(name: string, engine: AudioEngineV2, nodeType: AudioNodeType) {\n        super(engine, nodeType);\n\n        this._name = name;\n    }\n\n    /**\n     * The name of the audio node.\n     * - Triggers `onNameChangedObservable` when changed.\n     * @see {@link onNameChangedObservable}\n     */\n    public get name(): string {\n        return this._name;\n    }\n\n    public set name(newName: string) {\n        if (this._name === newName) {\n            return;\n        }\n\n        const oldName = this._name;\n\n        this._name = newName;\n\n        this.onNameChangedObservable.notifyObservers({ newName, oldName, node: this });\n    }\n\n    public override dispose(): void {\n        super.dispose();\n\n        this.onNameChangedObservable.clear();\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;;AAGnD,IAAkB,aAIjB;AAJD,CAAA,SAAkB,aAAa;IAC3B,aAAA,CAAA,aAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAc,CAAA;IACd,aAAA,CAAA,aAAA,CAAA,cAAA,GAAA,EAAA,GAAA,aAAe,CAAA;IACf,aAAA,CAAA,aAAA,CAAA,yBAAA,GAAA,EAAA,GAAA,wBAA0B,CAAA;AAC9B,CAAC,EAJiB,aAAa,IAAA,CAAb,aAAa,GAAA,CAAA,CAAA,GAI9B;AAeK,MAAgB,iBAAiB;IAmCnC;;;;OAIG,CACI,OAAO,GAAA;QACV,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAE,CAAC;gBACnD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC1B,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACzC,CAAC;YACL,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAE,CAAC;gBACjD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC1B,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACzC,CAAC;YACL,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAQD;;;;OAIG,CACO,QAAQ,CAAC,IAAuB,EAAA;QACtC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEhC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACO,WAAW,CAAC,IAAuB,EAAA;QACzC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG,CACK,UAAU,CAAC,IAAuB,EAAA;QACtC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACK,aAAa,CAAC,IAAuB,EAAA;;;QACzC,8DAAO,IAAI,CAAC,cAAc,8EAAE,MAAM,CAAC,IAAI,CAAC,qFAAI,KAAK,CAAC;IACtD,CAAC;IAhHD,YAAsB,MAAqB,EAAE,QAAuB,CAAA;QALpE;;WAEG,CACa,IAAA,CAAA,mBAAmB,GAAG,iKAAI,aAAU,EAAqB,CAAC;QAGtE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,QAAQ,GAAA,EAAA,4BAAA,EAA2B,GAAE,CAAC;YACtC,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAqB,CAAC;QACvD,CAAC;QAED,IAAI,QAAQ,GAAA,EAAA,6BAAA,EAA4B,GAAE,CAAC;YACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAqB,CAAC;QACzD,CAAC;IACL,CAAC;CAuGJ;AAKK,MAAgB,sBAAuB,SAAQ,iBAAiB;IAclE;;;;OAIG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,OAAe,EAAA;QAC3B,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;QAE3B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;QAErB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC;YAAE,OAAO;YAAE,OAAO;YAAE,IAAI,EAAE,IAAI;QAAA,CAAE,CAAC,CAAC;IACnF,CAAC;IAEe,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC;IA/BD,YAAsB,IAAY,EAAE,MAAqB,EAAE,QAAuB,CAAA;QAC9E,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAN5B;;WAEG,CACa,IAAA,CAAA,uBAAuB,GAAG,iKAAI,aAAU,EAAsE,CAAC;QAK3H,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;CA4BJ", "debugId": null}}, {"offset": {"line": 845, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/mainAudioOut.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/mainAudioOut.ts"], "sourcesContent": ["import { AudioNodeType, AbstractAudioNode } from \"./abstractAudioNode\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\n\n/**\n * Abstract class for the main audio output node.\n *\n * A main audio output is the last audio node in the audio graph before the audio is sent to the speakers.\n *\n * @see {@link AudioEngineV2.mainOut}\n * @internal\n */\nexport abstract class _MainAudioOut extends AbstractAudioNode {\n    protected constructor(engine: AudioEngineV2) {\n        super(engine, AudioNodeType.HAS_INPUTS);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAiB,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;;AAWjE,MAAgB,aAAc,iMAAQ,oBAAiB;IACzD,YAAsB,MAAqB,CAAA;QACvC,KAAK,CAAC,MAAM,EAAA,EAAA,4BAAA,GAA2B,CAAC;IAC5C,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioMainOut.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/webAudioMainOut.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport { _MainAudioOut } from \"../abstractAudio/mainAudioOut\";\nimport type { IAudioParameterRampOptions } from \"../audioParameter\";\nimport { _WebAudioParameterComponent } from \"./components/webAudioParameterComponent\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode } from \"./webAudioNode\";\n\n/** @internal */\nexport class _WebAudioMainOut extends _MainAudioOut implements IWebAudioInNode {\n    private _gainNode: GainNode;\n    private _volume: _WebAudioParameterComponent;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n\n        this._setGainNode(new GainNode(engine._audioContext));\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._volume.dispose();\n        this._gainNode.disconnect();\n        this._destinationNode.disconnect();\n    }\n\n    /** @internal */\n    public get _inNode(): GainNode {\n        return this._gainNode;\n    }\n\n    public set _inNode(value: GainNode) {\n        if (this._gainNode === value) {\n            return;\n        }\n\n        this._setGainNode(value);\n    }\n\n    /** @internal */\n    public get volume(): number {\n        return this._volume.targetValue;\n    }\n\n    /** @internal */\n    public set volume(value: number) {\n        this._volume.targetValue = value;\n    }\n\n    private get _destinationNode(): AudioNode {\n        return this.engine._audioDestination;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioMainOut\";\n    }\n\n    /** @internal */\n    public setVolume(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        this._volume.setTargetValue(value, options);\n    }\n\n    private _setGainNode(gainNode: GainNode): void {\n        if (this._gainNode === gainNode) {\n            return;\n        }\n\n        this._gainNode?.disconnect();\n        gainNode.connect(this._destinationNode);\n\n        this._volume = new _WebAudioParameterComponent(this.engine, gainNode.gain);\n\n        this._gainNode = gainNode;\n    }\n}\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAE9D,OAAO,EAAE,2BAA2B,EAAE,MAAM,yCAAyC,CAAC;;;AAKhF,MAAO,gBAAiB,4LAAQ,gBAAa;IAc/C,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC5B,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;IACvC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,OAAO,CAAC,KAAe,EAAA;QAC9B,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;IACpC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;IACrC,CAAC;IAED,IAAY,gBAAgB,GAAA;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;IACzC,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED,cAAA,EAAgB,CACT,SAAS,CAAC,KAAa,EAA+D;sBAA7D,iEAAyD,IAAI;QACzF,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAEO,YAAY,CAAC,QAAkB,EAAA;;QACnC,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;+BAEG,CAAC,SAAS,oDAAd,gBAAgB,UAAU,EAAE,CAAC;QAC7B,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAExC,IAAI,CAAC,OAAO,GAAG,8MAAI,8BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE3E,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAhED,cAAA,EAAgB,CAChB,YAAmB,MAAuB,CAAA;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEd,IAAI,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;IAC1D,CAAC;CA4DJ", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioUnmuteUI.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/webAudioUnmuteUI.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport { EngineStore } from \"../../Engines/engineStore\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\n\n/**\n * Adds a UI button that starts the audio engine's underlying audio context when the user presses it.\n * @internal\n */\nexport class _WebAudioUnmuteUI {\n    private _button: Nullable<HTMLButtonElement> = null;\n    private _enabled: boolean = true;\n    private _engine: _WebAudioEngine;\n    private _style: Nullable<HTMLStyleElement> = null;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine, parentElement?: HTMLElement) {\n        this._engine = engine;\n        const parent = parentElement || EngineStore.LastCreatedEngine?.getInputElement()?.parentElement || document.body;\n        const top = (parent?.offsetTop || 0) + 20;\n\n        this._style = document.createElement(\"style\");\n        this._style.appendChild(\n            document.createTextNode(\n                `.babylonUnmute{position:absolute;top:${top}px;margin-left:20px;height:40px;width:60px;background-color:rgba(51,51,51,0.7);background-image:url(\"data:image/svg+xml;charset=UTF-8,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2239%22%20height%3D%2232%22%20viewBox%3D%220%200%2039%2032%22%3E%3Cpath%20fill%3D%22white%22%20d%3D%22M9.625%2018.938l-0.031%200.016h-4.953q-0.016%200-0.031-0.016v-12.453q0-0.016%200.031-0.016h4.953q0.031%200%200.031%200.016v12.453zM12.125%207.688l8.719-8.703v27.453l-8.719-8.719-0.016-0.047v-9.938zM23.359%207.875l1.406-1.406%204.219%204.203%204.203-4.203%201.422%201.406-4.219%204.219%204.219%204.203-1.484%201.359-4.141-4.156-4.219%204.219-1.406-1.422%204.219-4.203z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E\");background-size:80%;background-repeat:no-repeat;background-position:center;background-position-y:4px;border:none;outline:none;transition:transform 0.125s ease-out;cursor:pointer;z-index:9999;}.babylonUnmute:hover{transform:scale(1.05)}`\n            )\n        );\n        document.head.appendChild(this._style);\n\n        this._button = document.createElement(\"button\");\n        this._button.className = \"babylonUnmute\";\n        this._button.id = \"babylonUnmuteButton\";\n\n        this._button.addEventListener(\"click\", () => {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this._engine.unlockAsync();\n        });\n\n        parent.appendChild(this._button);\n\n        this._engine.stateChangedObservable.add(this._onStateChanged);\n    }\n\n    /** @internal */\n    public dispose(): void {\n        this._button?.remove();\n        this._button = null;\n\n        this._style?.remove();\n        this._style = null;\n\n        this._engine.stateChangedObservable.removeCallback(this._onStateChanged);\n    }\n\n    /** @internal */\n    public get enabled(): boolean {\n        return this._enabled;\n    }\n\n    public set enabled(value: boolean) {\n        this._enabled = value;\n        if (value) {\n            if (this._engine.state !== \"running\") {\n                this._show();\n            }\n        } else {\n            this._hide();\n        }\n    }\n\n    private _show(): void {\n        if (!this._button) {\n            return;\n        }\n\n        this._button.style.display = \"block\";\n    }\n\n    private _hide(): void {\n        if (!this._button) {\n            return;\n        }\n\n        this._button.style.display = \"none\";\n    }\n\n    private _onStateChanged = () => {\n        if (!this._button) {\n            return;\n        }\n\n        if (this._engine.state === \"running\") {\n            this._hide();\n        } else {\n            this._show();\n        }\n    };\n}\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;;AAOlD,MAAO,iBAAiB;IAkC1B,cAAA,EAAgB,CACT,OAAO,GAAA;;6BACN,CAAC,OAAO,kDAAZ,cAAc,MAAM,EAAE,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;4BAEhB,CAAC,MAAM,iDAAX,aAAa,MAAM,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC7E,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAc,EAAA;QAC7B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACnC,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;IACL,CAAC;IAEO,KAAK,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;IACzC,CAAC;IAEO,KAAK,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,CAAC;IArED,cAAA,EAAgB,CAChB,YAAmB,MAAuB,EAAE,aAA2B,CAAA;4DAEnC;QAR5B,IAAA,CAAA,OAAO,GAAgC,IAAI,CAAC;QAC5C,IAAA,CAAA,QAAQ,GAAY,IAAI,CAAC;QAEzB,IAAA,CAAA,MAAM,GAA+B,IAAI,CAAC;QAyE1C,IAAA,CAAA,eAAe,GAAG,GAAG,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,OAAO;YACX,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACnC,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;QACL,CAAC,CAAC;QA/EE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,MAAM,MAAM,GAAG,aAAa,sNAAe,CAAC,iBAAiB,oJAAE,eAAe,EAAE,kIAAE,aAAa,KAAI,QAAQ,CAAC,IAAI,CAAC;QACjH,MAAM,GAAG,GAAG,EAAC,MAAM,iDAAE,SAAS,KAAI,CAAC,CAAC,GAAG,EAAE,CAAC;QAE1C,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,CAAC,WAAW,CACnB,QAAQ,CAAC,cAAc,CACnB,wCAA2C,OAAH,GAAG,EAAA,s8BAAu8B,CACr/B,CACJ,CAAC;QACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEvC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,eAAe,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,qBAAqB,CAAC;QAExC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACxC,mEAAmE;YACnE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEjC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAClE,CAAC;CAwDJ", "debugId": null}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioEngine.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/webAudioEngine.ts"], "sourcesContent": ["import { Observable } from \"../../Misc/observable\";\nimport type { Nullable } from \"../../types\";\nimport type { AbstractNamedAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type { AbstractSoundSource, ISoundSourceOptions } from \"../abstractAudio/abstractSoundSource\";\nimport type { AudioBus, IAudioBusOptions } from \"../abstractAudio/audioBus\";\nimport type { AudioEngineV2State, IAudioEngineV2Options } from \"../abstractAudio/audioEngineV2\";\nimport { AudioEngineV2 } from \"../abstractAudio/audioEngineV2\";\nimport type { IMainAudioBusOptions, MainAudioBus } from \"../abstractAudio/mainAudioBus\";\nimport type { IStaticSoundOptions, StaticSound } from \"../abstractAudio/staticSound\";\nimport type { IStaticSoundBufferOptions, StaticSoundBuffer } from \"../abstractAudio/staticSoundBuffer\";\nimport type { IStreamingSoundOptions, StreamingSound } from \"../abstractAudio/streamingSound\";\nimport type { AbstractSpatialAudioListener } from \"../abstractAudio/subProperties/abstractSpatialAudioListener\";\nimport { _HasSpatialAudioListenerOptions } from \"../abstractAudio/subProperties/abstractSpatialAudioListener\";\nimport type { _SpatialAudioListener } from \"../abstractAudio/subProperties/spatialAudioListener\";\nimport type { IAudioParameterRampOptions } from \"../audioParameter\";\nimport { _CreateSpatialAudioListener } from \"./subProperties/spatialWebAudioListener\";\nimport { _WebAudioMainOut } from \"./webAudioMainOut\";\nimport { _WebAudioUnmuteUI } from \"./webAudioUnmuteUI\";\n\n/**\n * Options for creating a v2 audio engine that uses the WebAudio API.\n */\nexport interface IWebAudioEngineOptions extends IAudioEngineV2Options {\n    /**\n     * The audio context to be used by the engine.\n     */\n    audioContext: AudioContext;\n    /**\n     * The default UI's parent element. Defaults to the last created graphics engine's canvas if it exists; otherwise the HTML document's body.\n     */\n    defaultUIParentElement?: HTMLElement;\n    /**\n     * Set to `true` to disable the default UI. Defaults to `false`.\n     */\n    disableDefaultUI?: boolean;\n    /**\n     * Set to `true` to automatically resume the audio context when the user interacts with the page. Defaults to `true`.\n     */\n    resumeOnInteraction: boolean;\n    /**\n     * Set to `true` to automatically resume the audio context when the browser pauses audio playback. Defaults to `true`.\n     */\n    resumeOnPause: boolean;\n    /**\n     * The interval in milliseconds to try resuming audio playback when `resumeOnPause` is `true`. Defaults to `1000`.\n     */\n    resumeOnPauseRetryInterval: number;\n}\n\n/**\n * Creates a new v2 audio engine that uses the WebAudio API.\n * @param options - The options for creating the audio engine.\n * @returns A promise that resolves with the created audio engine.\n */\nexport async function CreateAudioEngineAsync(options: Partial<IWebAudioEngineOptions> = {}): Promise<AudioEngineV2> {\n    const engine = new _WebAudioEngine(options);\n    await engine._initAsync(options);\n    return engine;\n}\n\nconst FormatMimeTypes: { [key: string]: string } = {\n    aac: \"audio/aac\",\n    ac3: \"audio/ac3\",\n    flac: \"audio/flac\",\n    m4a: \"audio/mp4\",\n    mp3: 'audio/mpeg; codecs=\"mp3\"',\n    mp4: \"audio/mp4\",\n    ogg: 'audio/ogg; codecs=\"vorbis\"',\n    wav: \"audio/wav\",\n    webm: 'audio/webm; codecs=\"vorbis\"',\n};\n\n/** @internal */\nexport class _WebAudioEngine extends AudioEngineV2 {\n    private _audioContextStarted = false;\n    private _destinationNode: Nullable<AudioNode> = null;\n    private _invalidFormats = new Set<string>();\n    private _isUpdating = false;\n    private _listener: Nullable<_SpatialAudioListener> = null;\n    private readonly _listenerAutoUpdate: boolean = true;\n    private readonly _listenerMinUpdateTime: number = 0;\n    private _mainOut: _WebAudioMainOut;\n    private _pauseCalled = false;\n    private _resumeOnInteraction = true;\n    private _resumeOnPause = true;\n    private _resumeOnPauseRetryInterval = 1000;\n    private _resumeOnPauseTimerId: any = null;\n    private _resumePromise: Nullable<Promise<void>> = null;\n    private _silentHtmlAudio: Nullable<HTMLAudioElement> = null;\n    private _unmuteUI: Nullable<_WebAudioUnmuteUI> = null;\n    private _updateObservable: Nullable<Observable<void>> = null;\n    private readonly _validFormats = new Set<string>();\n    private _volume = 1;\n\n    /** @internal */\n    public readonly _audioContext: AudioContext;\n\n    /** @internal */\n    public readonly _isUsingOfflineAudioContext: boolean = false;\n\n    /** @internal */\n    public readonly isReadyPromise: Promise<void> = new Promise((resolve) => {\n        this._resolveIsReadyPromise = resolve;\n    });\n\n    /** @internal */\n    public stateChangedObservable: Observable<string> = new Observable();\n\n    /** @internal */\n    public userGestureObservable: Observable<void> = new Observable();\n\n    /** @internal */\n    public constructor(options: Partial<IWebAudioEngineOptions> = {}) {\n        super(options);\n\n        if (typeof options.listenerAutoUpdate === \"boolean\") {\n            this._listenerAutoUpdate = options.listenerAutoUpdate;\n        }\n\n        if (typeof options.listenerMinUpdateTime === \"number\") {\n            this._listenerMinUpdateTime = options.listenerMinUpdateTime;\n        }\n\n        this._volume = options.volume ?? 1;\n\n        if (options.audioContext) {\n            this._isUsingOfflineAudioContext = options.audioContext instanceof OfflineAudioContext;\n            this._audioContext = options.audioContext;\n        } else {\n            this._audioContext = new AudioContext();\n        }\n\n        if (!options.disableDefaultUI) {\n            this._unmuteUI = new _WebAudioUnmuteUI(this, options.defaultUIParentElement);\n        }\n    }\n\n    /** @internal */\n    public async _initAsync(options: Partial<IWebAudioEngineOptions>): Promise<void> {\n        this._resumeOnInteraction = typeof options.resumeOnInteraction === \"boolean\" ? options.resumeOnInteraction : true;\n        this._resumeOnPause = typeof options.resumeOnPause === \"boolean\" ? options.resumeOnPause : true;\n        this._resumeOnPauseRetryInterval = options.resumeOnPauseRetryInterval ?? 1000;\n\n        document.addEventListener(\"click\", this._onUserGestureAsync);\n\n        await this._initAudioContextAsync();\n\n        if (_HasSpatialAudioListenerOptions(options)) {\n            this._listener = _CreateSpatialAudioListener(this, this._listenerAutoUpdate, this._listenerMinUpdateTime);\n            this._listener.setOptions(options);\n        }\n\n        this._resolveIsReadyPromise();\n    }\n\n    /** @internal */\n    public get currentTime(): number {\n        return this._audioContext.currentTime ?? 0;\n    }\n\n    /** @internal */\n    public get _inNode(): AudioNode {\n        return this._audioContext.destination;\n    }\n\n    /** @internal */\n    public get mainOut(): _WebAudioMainOut {\n        return this._mainOut;\n    }\n\n    /** @internal */\n    public get listener(): AbstractSpatialAudioListener {\n        return this._listener ?? (this._listener = _CreateSpatialAudioListener(this, this._listenerAutoUpdate, this._listenerMinUpdateTime));\n    }\n\n    /** @internal */\n    public get state(): AudioEngineV2State {\n        // Always return \"running\" for OfflineAudioContext so sound `play` calls work while the context is suspended.\n        return this._isUsingOfflineAudioContext ? \"running\" : this._audioContext.state;\n    }\n\n    /** @internal */\n    public get volume(): number {\n        return this._volume;\n    }\n\n    /** @internal */\n    public set volume(value: number) {\n        if (this._volume === value) {\n            return;\n        }\n\n        this._volume = value;\n\n        if (this._mainOut) {\n            this._mainOut.volume = value;\n        }\n    }\n\n    /**\n     * This property should only be used by the legacy audio engine.\n     * @internal\n     * */\n    public get _audioDestination(): AudioNode {\n        return this._destinationNode ? this._destinationNode : (this._destinationNode = this._audioContext.destination);\n    }\n\n    public set _audioDestination(value: Nullable<AudioNode>) {\n        this._destinationNode = value;\n    }\n\n    /**\n     * This property should only be used by the legacy audio engine.\n     * @internal\n     */\n    public get _unmuteUIEnabled(): boolean {\n        return this._unmuteUI ? this._unmuteUI.enabled : false;\n    }\n\n    public set _unmuteUIEnabled(value: boolean) {\n        if (this._unmuteUI) {\n            this._unmuteUI.enabled = value;\n        }\n    }\n\n    /** @internal */\n    public async createBusAsync(name: string, options: Partial<IAudioBusOptions> = {}): Promise<AudioBus> {\n        const module = await import(\"./webAudioBus\");\n\n        const bus = new module._WebAudioBus(name, this, options);\n        await bus._initAsync(options);\n\n        return bus;\n    }\n\n    /** @internal */\n    public async createMainBusAsync(name: string, options: Partial<IMainAudioBusOptions> = {}): Promise<MainAudioBus> {\n        const module = await import(\"./webAudioMainBus\");\n\n        const bus = new module._WebAudioMainBus(name, this);\n        await bus._initAsync(options);\n\n        return bus;\n    }\n\n    /** @internal */\n    public async createMicrophoneSoundSourceAsync(name: string, options?: Partial<ISoundSourceOptions>): Promise<AbstractSoundSource> {\n        let mediaStream: MediaStream;\n\n        try {\n            mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });\n        } catch (e) {\n            throw new Error(\"Unable to access microphone: \" + e);\n        }\n\n        return await this.createSoundSourceAsync(name, new MediaStreamAudioSourceNode(this._audioContext, { mediaStream }), {\n            outBusAutoDefault: false,\n            ...options,\n        });\n    }\n\n    /** @internal */\n    public async createSoundAsync(\n        name: string,\n        source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n        options: Partial<IStaticSoundOptions> = {}\n    ): Promise<StaticSound> {\n        const module = await import(\"./webAudioStaticSound\");\n\n        const sound = new module._WebAudioStaticSound(name, this, options);\n        await sound._initAsync(source, options);\n\n        return sound;\n    }\n\n    /** @internal */\n    public async createSoundBufferAsync(\n        source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n        options: Partial<IStaticSoundBufferOptions> = {}\n    ): Promise<StaticSoundBuffer> {\n        const module = await import(\"./webAudioStaticSound\");\n\n        const soundBuffer = new module._WebAudioStaticSoundBuffer(this);\n        await soundBuffer._initAsync(source, options);\n\n        return soundBuffer;\n    }\n\n    /** @internal */\n    public async createSoundSourceAsync(name: string, source: AudioNode, options: Partial<ISoundSourceOptions> = {}): Promise<AbstractSoundSource> {\n        const module = await import(\"./webAudioSoundSource\");\n\n        const soundSource = new module._WebAudioSoundSource(name, source, this, options);\n        await soundSource._initAsync(options);\n\n        return soundSource;\n    }\n\n    /** @internal */\n    public async createStreamingSoundAsync(name: string, source: HTMLMediaElement | string | string[], options: Partial<IStreamingSoundOptions> = {}): Promise<StreamingSound> {\n        const module = await import(\"./webAudioStreamingSound\");\n\n        const sound = new module._WebAudioStreamingSound(name, this, options);\n        await sound._initAsync(source, options);\n\n        return sound;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._listener?.dispose();\n        this._listener = null;\n\n        // Note that OfflineAudioContext does not have a `close` method.\n        if (this._audioContext.state !== \"closed\" && !this._isUsingOfflineAudioContext) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this._audioContext.close();\n        }\n\n        document.removeEventListener(\"click\", this._onUserGestureAsync);\n        this._audioContext.removeEventListener(\"statechange\", this._onAudioContextStateChange);\n\n        this._silentHtmlAudio?.remove();\n\n        this._updateObservable?.clear();\n        this._updateObservable = null;\n\n        this._unmuteUI?.dispose();\n        this._unmuteUI = null;\n\n        this.stateChangedObservable.clear();\n    }\n\n    /** @internal */\n    public flagInvalidFormat(format: string): void {\n        this._invalidFormats.add(format);\n    }\n\n    /** @internal */\n    public isFormatValid(format: string): boolean {\n        if (this._validFormats.has(format)) {\n            return true;\n        }\n\n        if (this._invalidFormats.has(format)) {\n            return false;\n        }\n\n        const mimeType = FormatMimeTypes[format];\n        if (mimeType === undefined) {\n            return false;\n        }\n\n        const audio = new Audio();\n        if (audio.canPlayType(mimeType) === \"\") {\n            this._invalidFormats.add(format);\n            return false;\n        }\n\n        this._validFormats.add(format);\n\n        return true;\n    }\n\n    /** @internal */\n    public override async pauseAsync(): Promise<void> {\n        await this._audioContext.suspend();\n\n        this._pauseCalled = true;\n    }\n\n    /** @internal */\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\n    public override resumeAsync(): Promise<void> {\n        this._pauseCalled = false;\n\n        if (this._resumePromise) {\n            return this._resumePromise;\n        }\n\n        this._resumePromise = this._audioContext.resume();\n        return this._resumePromise;\n    }\n\n    /** @internal */\n    public setVolume(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        if (this._mainOut) {\n            this._mainOut.setVolume(value, options);\n        } else {\n            throw new Error(\"Main output not initialized yet.\");\n        }\n    }\n\n    /** @internal */\n    public override _addMainBus(mainBus: MainAudioBus): void {\n        super._addMainBus(mainBus);\n    }\n\n    /** @internal */\n    public override _removeMainBus(mainBus: MainAudioBus): void {\n        super._removeMainBus(mainBus);\n    }\n\n    /** @internal */\n    public override _addNode(node: AbstractNamedAudioNode): void {\n        super._addNode(node);\n    }\n\n    /** @internal */\n    public override _removeNode(node: AbstractNamedAudioNode): void {\n        super._removeNode(node);\n    }\n\n    /** @internal */\n    public _addUpdateObserver(callback: () => void): void {\n        if (!this._updateObservable) {\n            this._updateObservable = new Observable<void>();\n        }\n\n        this._updateObservable.add(callback);\n        this._startUpdating();\n    }\n\n    public _removeUpdateObserver(callback: () => void): void {\n        if (this._updateObservable) {\n            this._updateObservable.removeCallback(callback);\n        }\n    }\n\n    private _initAudioContextAsync: () => Promise<void> = async () => {\n        this._audioContext.addEventListener(\"statechange\", this._onAudioContextStateChange);\n\n        this._mainOut = new _WebAudioMainOut(this);\n        this._mainOut.volume = this._volume;\n\n        await this.createMainBusAsync(\"default\");\n    };\n\n    private _onAudioContextStateChange = () => {\n        if (this.state === \"running\") {\n            clearInterval(this._resumeOnPauseTimerId);\n            this._audioContextStarted = true;\n            this._resumePromise = null;\n        }\n        if (this.state === \"suspended\" || this.state === \"interrupted\") {\n            if (this._audioContextStarted && this._resumeOnPause && !this._pauseCalled) {\n                clearInterval(this._resumeOnPauseTimerId);\n\n                this._resumeOnPauseTimerId = setInterval(() => {\n                    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n                    this.resumeAsync();\n                }, this._resumeOnPauseRetryInterval);\n            }\n        }\n\n        this.stateChangedObservable.notifyObservers(this.state);\n    };\n\n    private _onUserGestureAsync: () => void = async () => {\n        if (this._resumeOnInteraction) {\n            await this._audioContext.resume();\n        }\n\n        // On iOS the ringer switch must be turned on for WebAudio to play.\n        // This gets WebAudio to play with the ringer switch turned off by playing an HTMLAudioElement.\n        if (!this._silentHtmlAudio) {\n            this._silentHtmlAudio = document.createElement(\"audio\");\n\n            const audio = this._silentHtmlAudio;\n            audio.controls = false;\n            audio.preload = \"auto\";\n            audio.loop = true;\n\n            // Wave data for 0.0001 seconds of silence.\n            audio.src = \"data:audio/wav;base64,UklGRjAAAABXQVZFZm10IBAAAAABAAEAgLsAAAB3AQACABAAZGF0YQwAAAAAAAEA/v8CAP//AQA=\";\n\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            audio.play();\n        }\n\n        this.userGestureObservable.notifyObservers();\n    };\n\n    private _resolveIsReadyPromise: () => void;\n\n    private _startUpdating = () => {\n        if (this._isUpdating) {\n            return;\n        }\n\n        this._isUpdating = true;\n\n        if (this.state === \"running\") {\n            this._update();\n        } else {\n            const callback = () => {\n                if (this.state === \"running\") {\n                    this._update();\n                    this.stateChangedObservable.removeCallback(callback);\n                }\n            };\n\n            this.stateChangedObservable.add(callback);\n        }\n    };\n\n    private _update = (): void => {\n        if (this._updateObservable?.hasObservers()) {\n            this._updateObservable.notifyObservers();\n            requestAnimationFrame(this._update);\n        } else {\n            this._isUpdating = false;\n        }\n    };\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAMnD,OAAO,EAAE,aAAa,EAAE,MAAM,gCAAgC,CAAC;AAM/D,OAAO,EAAE,+BAA+B,EAAE,MAAM,6DAA6D,CAAC;AAG9G,OAAO,EAAE,2BAA2B,EAAE,MAAM,yCAAyC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AACrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,oBAAoB,CAAC;;;;;;;AAqChD,KAAK,UAAU,sBAAsB;kBAAC,iEAA2C,CAAA,CAAE;IACtF,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;IAC5C,MAAM,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,eAAe,GAA8B;IAC/C,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,WAAW;IAChB,IAAI,EAAE,YAAY;IAClB,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,0BAA0B;IAC/B,GAAG,EAAE,WAAW;IAChB,GAAG,EAAE,4BAA4B;IACjC,GAAG,EAAE,WAAW;IAChB,IAAI,EAAE,6BAA6B;CACtC,CAAC;AAGI,MAAO,eAAgB,6LAAQ,gBAAa;IAgE9C,cAAA,EAAgB,CACT,KAAK,CAAC,UAAU,CAAC,OAAwC,EAAA;QAC5D,IAAI,CAAC,oBAAoB,GAAG,OAAO,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC;QAClH,IAAI,CAAC,cAAc,GAAG,OAAO,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC;;QAChG,IAAI,CAAC,2BAA2B,IAAG,OAAO,uCAAC,0BAA0B,qGAAI,IAAI,CAAC;QAE9E,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE7D,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEpC,4NAAI,kCAAA,AAA+B,EAAC,OAAO,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,SAAS,iNAAG,8BAAA,AAA2B,EAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1G,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;;QAClB,8CAAW,CAAC,aAAa,CAAC,WAAW,2DAA9B,kCAAkC,CAAC,CAAC;IAC/C,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;IAC1C,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;;QACf,QAAO,sBAAI,CAAC,SAAS,IAAI,yDAAC,IAAI,CAAC,SAAS,iNAAG,8BAAA,AAA2B,EAAC,IAAI,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;IACzI,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,KAAK,GAAA;QACZ,6GAA6G;QAC7G,OAAO,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;IACnF,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;SAGK,CACL,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IACpH,CAAC;IAED,IAAW,iBAAiB,CAAC,KAA0B,EAAA;QACnD,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;;OAGG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3D,CAAC;IAED,IAAW,gBAAgB,CAAC,KAAc,EAAA;QACtC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;QACnC,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,cAAc,CAAC,IAAY,EAAyC;sBAAvC,iEAAqC,CAAA,CAAE;QAC7E,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;QAE7C,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACzD,MAAM,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE9B,OAAO,GAAG,CAAC;IACf,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,kBAAkB,CAAC,IAAY,EAA6C;sBAA3C,iEAAyC,CAAA,CAAE;QACrF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAEjD,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACpD,MAAM,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE9B,OAAO,GAAG,CAAC;IACf,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,gCAAgC,CAAC,IAAY,EAAE,OAAsC,EAAA;QAC9F,IAAI,WAAwB,CAAC;QAE7B,IAAI,CAAC;YACD,WAAW,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;gBAAE,KAAK,EAAE,IAAI;YAAA,CAAE,CAAC,CAAC;QAC7E,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,0BAA0B,CAAC,IAAI,CAAC,aAAa,EAAE;YAAE,WAAW;QAAA,CAAE,CAAC,EAAE;YAChH,iBAAiB,EAAE,KAAK;YACxB,GAAG,OAAO;SACb,CAAC,CAAC;IACP,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,gBAAgB,CACzB,IAAY,EACZ,MAAyE,EAC/B;sBAA1C,iEAAwC,CAAA,CAAE;QAE1C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;QAErD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACnE,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAExC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,sBAAsB,CAC/B,MAAyE,EACzB;sBAAhD,iEAA8C,CAAA,CAAE;QAEhD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;QAErD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAChE,MAAM,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAE9C,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,sBAAsB,CAAC,IAAY,EAAE,MAAiB,EAA4C;sBAA1C,iEAAwC,CAAA,CAAE;QAC3G,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC;QAErD,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACjF,MAAM,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEtC,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,yBAAyB,CAAC,IAAY,EAAE,MAA4C,EAA+C;sBAA7C,iEAA2C,CAAA,CAAE;QAC5I,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;QAExD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACtE,MAAM,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAExC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;6BAenB,wBAEA;QAhBA,KAAK,CAAC,OAAO,EAAE,CAAC;+BAEZ,CAAC,SAAS,oDAAd,gBAAgB,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,gEAAgE;QAChE,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAC7E,mEAAmE;YACnE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC/B,CAAC;QAED,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAChE,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;sCAEnF,CAAC,gBAAgB,kFAAE,MAAM,EAAE,CAAC;uCAE5B,CAAC,iBAAiB,oFAAE,KAAK,EAAE,CAAC;QAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;2BAE9B,IAAI,CAAC,SAAS,oEAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;IACxC,CAAC;IAED,cAAA,EAAgB,CACT,iBAAiB,CAAC,MAAc,EAAA;QACnC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED,cAAA,EAAgB,CACT,aAAa,CAAC,MAAc,EAAA;QAC/B,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAC1B,IAAI,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC;YACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACjC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE/B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACA,KAAK,CAAC,UAAU,GAAA;QAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAEnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED,cAAA,EAAgB,CAChB,2FAA2F;IAC3E,WAAW,GAAA;QACvB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAClD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CACT,SAAS,CAAC,KAAa,EAA+D;sBAA7D,iEAAyD,IAAI;QACzF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC,MAAM,CAAC;YACJ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACA,WAAW,CAAC,OAAqB,EAAA;QAC7C,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CACA,cAAc,CAAC,OAAqB,EAAA;QAChD,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CACA,QAAQ,CAAC,IAA4B,EAAA;QACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED,cAAA,EAAgB,CACA,WAAW,CAAC,IAA4B,EAAA;QACpD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED,cAAA,EAAgB,CACT,kBAAkB,CAAC,QAAoB,EAAA;QAC1C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,GAAG,iKAAI,aAAU,EAAQ,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAEM,qBAAqB,CAAC,QAAoB,EAAA;QAC7C,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IA9TD,cAAA,EAAgB,CAChB,YAAmB,UAA2C,CAAA,CAAE,CAAA;QAC5D,KAAK,CAAC,OAAO,CAAC,CAAC;QAvCX,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,gBAAgB,GAAwB,IAAI,CAAC;QAC7C,IAAA,CAAA,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;QACpC,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,SAAS,GAAoC,IAAI,CAAC;QACzC,IAAA,CAAA,mBAAmB,GAAY,IAAI,CAAC;QACpC,IAAA,CAAA,sBAAsB,GAAW,CAAC,CAAC;QAE5C,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAC5B,IAAA,CAAA,cAAc,GAAG,IAAI,CAAC;QACtB,IAAA,CAAA,2BAA2B,GAAG,IAAI,CAAC;QACnC,IAAA,CAAA,qBAAqB,GAAQ,IAAI,CAAC;QAClC,IAAA,CAAA,cAAc,GAA4B,IAAI,CAAC;QAC/C,IAAA,CAAA,gBAAgB,GAA+B,IAAI,CAAC;QACpD,IAAA,CAAA,SAAS,GAAgC,IAAI,CAAC;QAC9C,IAAA,CAAA,iBAAiB,GAA+B,IAAI,CAAC;QAC5C,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QAC3C,IAAA,CAAA,OAAO,GAAG,CAAC,CAAC;QAKpB,cAAA,EAAgB,CACA,IAAA,CAAA,2BAA2B,GAAY,KAAK,CAAC;QAE7D,cAAA,EAAgB,CACA,IAAA,CAAA,cAAc,GAAkB,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACpE,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,cAAA,EAAgB,CACT,IAAA,CAAA,sBAAsB,GAAuB,iKAAI,aAAU,EAAE,CAAC;QAErE,cAAA,EAAgB,CACT,IAAA,CAAA,qBAAqB,GAAqB,iKAAI,aAAU,EAAE,CAAC;QAkU1D,IAAA,CAAA,sBAAsB,GAAwB,KAAK,IAAI,EAAE;YAC7D,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAEpF,IAAI,CAAC,QAAQ,GAAG,qLAAI,mBAAgB,CAAC,IAAI,CAAC,CAAC;YAC3C,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAEpC,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC,CAAC;QAEM,IAAA,CAAA,0BAA0B,GAAG,GAAG,EAAE;YACtC,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC3B,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAC1C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC/B,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,IAAI,IAAI,CAAC,KAAK,KAAK,aAAa,EAAE,CAAC;gBAC7D,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;oBACzE,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBAE1C,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC,GAAG,EAAE;wBAC1C,mEAAmE;wBACnE,IAAI,CAAC,WAAW,EAAE,CAAC;oBACvB,CAAC,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBACzC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC,CAAC;QAEM,IAAA,CAAA,mBAAmB,GAAe,KAAK,IAAI,EAAE;YACjD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YACtC,CAAC;YAED,mEAAmE;YACnE,+FAA+F;YAC/F,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACzB,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAExD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBACpC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACvB,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;gBACvB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;gBAElB,2CAA2C;gBAC3C,KAAK,CAAC,GAAG,GAAG,oGAAoG,CAAC;gBAEjH,mEAAmE;gBACnE,KAAK,CAAC,IAAI,EAAE,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,EAAE,CAAC;QACjD,CAAC,CAAC;QAIM,IAAA,CAAA,cAAc,GAAG,GAAG,EAAE;YAC1B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,OAAO;YACX,CAAC;YAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAExB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC,MAAM,CAAC;gBACJ,MAAM,QAAQ,GAAG,GAAG,EAAE;oBAClB,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;wBAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;wBACf,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBACzD,CAAC;gBACL,CAAC,CAAC;gBAEF,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC,CAAC;QAEM,IAAA,CAAA,OAAO,GAAG,GAAS,EAAE;;YACzB,mCAAQ,CAAC,iBAAiB,4DAAtB,wBAAwB,YAAY,EAAE,EAAE,CAAC;gBACzC,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,CAAC;gBACzC,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YAC7B,CAAC;QACL,CAAC,CAAC;QAhZE,IAAI,OAAO,OAAO,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;YAClD,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAC1D,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,qBAAqB,KAAK,QAAQ,EAAE,CAAC;YACpD,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC,qBAAqB,CAAC;QAChE,CAAC;;QAED,IAAI,CAAC,OAAO,8BAAW,MAAM,2CAAd,OAAO,WAAW,CAAC,CAAC;QAEnC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,2BAA2B,GAAG,OAAO,CAAC,YAAY,YAAY,mBAAmB,CAAC;YACvF,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;QAC9C,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,sLAAI,oBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;CA6XJ", "debugId": null}}, {"offset": {"line": 1350, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subNodes/abstractAudioSubNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subNodes/abstractAudioSubNode.ts"], "sourcesContent": ["import { AudioNodeType, AbstractNamedAudioNode } from \"../abstractAudioNode\";\nimport type { AudioEngineV2 } from \"../audioEngineV2\";\n\n/** @internal */\nexport abstract class _AbstractAudioSubNode extends AbstractNamedAudioNode {\n    /** @internal */\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine, AudioNodeType.HAS_INPUTS_AND_OUTPUTS);\n    }\n\n    /** @internal */\n    public connect(node: _AbstractAudioSubNode): void {\n        if (!this._connect(node)) {\n            throw new Error(\"Connect failed\");\n        }\n    }\n\n    /** @internal */\n    public disconnect(node: _AbstractAudioSubNode): void {\n        if (!this._disconnect(node)) {\n            throw new Error(\"Disconnect failed\");\n        }\n    }\n\n    /** @internal */\n    public disconnectAll(): void {\n        if (!this._downstreamNodes) {\n            throw new Error(\"Disconnect failed\");\n        }\n\n        const it = this._downstreamNodes.values();\n\n        for (let next = it.next(); !next.done; next = it.next()) {\n            if (!this._disconnect(next.value)) {\n                throw new Error(\"Disconnect failed\");\n            }\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAiB,sBAAsB,EAAE,MAAM,sBAAsB,CAAC;;AAIvE,MAAgB,qBAAsB,iMAAQ,yBAAsB;IAMtE,cAAA,EAAgB,CACT,OAAO,CAAC,IAA2B,EAAA;QACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,UAAU,CAAC,IAA2B,EAAA;QACzC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,aAAa,GAAA;QAChB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;QAE1C,IAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAE,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;IACL,CAAC;IAhCD,cAAA,EAAgB,CAChB,YAAsB,IAAY,EAAE,MAAqB,CAAA;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,EAAA,EAAA,wCAAA,GAAuC,CAAC;IAC9D,CAAC;CA8BJ", "debugId": null}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subNodes/volumeAudioSubNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subNodes/volumeAudioSubNode.ts"], "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { IAudioParameterRampOptions } from \"../../audioParameter\";\nimport type { AudioEngineV2 } from \"../audioEngineV2\";\nimport { _AbstractAudioSubNode } from \"../subNodes/abstractAudioSubNode\";\nimport { AudioSubNode } from \"../subNodes/audioSubNode\";\nimport type { _AbstractAudioSubGraph } from \"./abstractAudioSubGraph\";\n\n/** @internal */\nexport const _VolumeAudioDefaults = {\n    volume: 1 as number,\n} as const;\n\n/**\n * Volume options.\n */\nexport interface IVolumeAudioOptions {\n    /**\n     * The volume/gain. Defaults to 1.\n     */\n    volume: number;\n}\n\n/** @internal */\nexport abstract class _VolumeAudioSubNode extends _AbstractAudioSubNode {\n    protected constructor(engine: AudioEngineV2) {\n        super(AudioSubNode.VOLUME, engine);\n    }\n\n    public abstract volume: number;\n\n    /** @internal */\n    public setOptions(options: Partial<IVolumeAudioOptions>): void {\n        this.volume = options.volume ?? _VolumeAudioDefaults.volume;\n    }\n\n    /** @internal */\n    public abstract setVolume(value: number, options?: Nullable<Partial<IAudioParameterRampOptions>>): void;\n}\n\n/** @internal */\nexport function _GetVolumeAudioSubNode(subGraph: _AbstractAudioSubGraph): Nullable<_VolumeAudioSubNode> {\n    return subGraph.getSubNode<_VolumeAudioSubNode>(AudioSubNode.VOLUME);\n}\n\n/** @internal */\nexport function _GetVolumeAudioProperty<K extends keyof typeof _VolumeAudioDefaults>(subGraph: _AbstractAudioSubGraph, property: K): (typeof _VolumeAudioDefaults)[K] {\n    return _GetVolumeAudioSubNode(subGraph)?.[property] ?? _VolumeAudioDefaults[property];\n}\n"], "names": [], "mappings": ";;;;;;AAGA,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;;AAKlE,MAAM,oBAAoB,GAAG;IAChC,MAAM,EAAE,CAAW;CACb,CAAC;AAaL,MAAgB,mBAAoB,gNAAQ,wBAAqB;IAOnE,cAAA,EAAgB,CACT,UAAU,CAAC,OAAqC,EAAA;;QACnD,IAAI,CAAC,MAAM,sBAAG,OAAO,CAAC,MAAM,6DAAI,oBAAoB,CAAC,MAAM,CAAC;IAChE,CAAC;IATD,YAAsB,MAAqB,CAAA;QACvC,KAAK,CAAA,SAAA,uBAAA,KAAsB,MAAM,CAAC,CAAC;IACvC,CAAC;CAWJ;AAGK,SAAU,sBAAsB,CAAC,QAAgC;IACnE,OAAO,QAAQ,CAAC,UAAU,CAAA,SAAA,uBAAA,GAA0C,CAAC;AACzE,CAAC;AAGK,SAAU,uBAAuB,CAA8C,QAAgC,EAAE,QAAW;;;IAC9H,4FAA8B,QAAQ,CAAC,mFAAE,CAAC,QAAQ,CAAC,2DAA5C,sBAAsB,YAA0B,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAC1F,CAAC", "debugId": null}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subProperties/abstractAudioAnalyzer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subProperties/abstractAudioAnalyzer.ts"], "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/naming-convention\nexport type AudioAnalyzerFFTSizeType = 32 | 64 | 128 | 256 | 512 | 1024 | 2048 | 4096 | 8192 | 16384 | 32768;\n\nexport const _AudioAnalyzerDefaults = {\n    fftSize: 2048 as AudioAnalyzerFFTSizeType,\n    minDecibels: -100 as number,\n    maxDecibels: -30 as number,\n    smoothing: 0.8 as number,\n} as const;\n\n/**\n * Options for the AudioAnalyzer\n */\nexport interface IAudioAnalyzerOptions {\n    /**\n     * Enable the audio analyzer. Defaults to false.\n     */\n    analyzerEnabled: boolean;\n    /**\n     * The size of the FFT (fast fourier transform) to use when converting time-domain data to frequency-domain data. Default is 2048.\n     */\n    analyzerFFTSize: AudioAnalyzerFFTSizeType;\n\n    /**\n     * The minimum decibel value for the range of the analyzer. Default is -100.\n     */\n    analyzerMinDecibels: number;\n\n    /**\n     * The maximum decibel value for the range of the analyzer. Default is -30.\n     */\n    analyzerMaxDecibels: number;\n\n    /**\n     * A number between 0 and 1 that determines how quickly the analyzer's value changes. Default is 0.8.\n     */\n    analyzerSmoothing: number;\n}\n\n/**\n * @param options The audio analyzer options to check.\n * @returns `true` if audio analyzer options are defined, otherwise `false`.\n */\nexport function _HasAudioAnalyzerOptions(options: Partial<IAudioAnalyzerOptions>): boolean {\n    return (\n        options.analyzerEnabled ||\n        options.analyzerFFTSize !== undefined ||\n        options.analyzerMinDecibels !== undefined ||\n        options.analyzerMaxDecibels !== undefined ||\n        options.analyzerSmoothing !== undefined\n    );\n}\n\n/**\n * An AudioAnalyzer converts time-domain audio data into the frequency-domain.\n */\nexport abstract class AbstractAudioAnalyzer {\n    /**\n     * The size of the FFT (fast fourier transform) to use when converting time-domain data to frequency-domain data. Default is 2048.\n     */\n    public abstract fftSize: AudioAnalyzerFFTSizeType;\n\n    /**\n     * The number of data values that will be returned when calling getByteFrequencyData() or getFloatFrequencyData(). This is always half the `fftSize`.\n     */\n    public get frequencyBinCount(): number {\n        return this.fftSize / 2;\n    }\n\n    /**\n     * Whether the analyzer is enabled or not.\n     * - The `getByteFrequencyData` and `getFloatFrequencyData` functions return `null` if the analyzer is not enabled.\n     * @see {@link enableAsync}\n     */\n    public abstract isEnabled: boolean;\n\n    /**\n     * The minimum decibel value for the range of the analyzer. Default is -100.\n     */\n    public abstract minDecibels: number;\n\n    /**\n     * The maximum decibel value for the range of the analyzer. Default is -30.\n     */\n    public abstract maxDecibels: number;\n\n    /**\n     * A number between 0 and 1 that determines how quickly the analyzer's value changes. Default is 0.8.\n     */\n    public abstract smoothing: number;\n\n    /**\n     * Releases associated resources.\n     */\n    public abstract dispose(): void;\n\n    /**\n     * Enables the analyzer\n     */\n    public abstract enableAsync(): Promise<void>;\n\n    /**\n     * Gets the current frequency data as a byte array\n     * @returns a Uint8Array if the analyzer is enabled, otherwise `null`\n     */\n    public abstract getByteFrequencyData(): Uint8Array;\n\n    /**\n     * Gets the current frequency data as a float array\n     * @returns a Float32Array if the analyzer is enabled, otherwise `null`\n     */\n    public abstract getFloatFrequencyData(): Float32Array;\n}\n"], "names": [], "mappings": ";;;;;AAGO,MAAM,sBAAsB,GAAG;IAClC,OAAO,EAAE,IAAgC;IACzC,WAAW,EAAE,CAAC,GAAa;IAC3B,WAAW,EAAE,CAAC,EAAY;IAC1B,SAAS,EAAE,GAAa;CAClB,CAAC;AAmCL,SAAU,wBAAwB,CAAC,OAAuC;IAC5E,OAAO,AACH,OAAO,CAAC,eAAe,IACvB,OAAO,CAAC,eAAe,KAAK,SAAS,IACrC,OAAO,CAAC,mBAAmB,KAAK,SAAS,IACzC,OAAO,CAAC,mBAAmB,KAAK,SAAS,IACzC,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAC1C,CAAC;AACN,CAAC;AAKK,MAAgB,qBAAqB;IAMvC;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IAC5B,CAAC;CA6CJ", "debugId": null}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subNodes/audioAnalyzerSubNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subNodes/audioAnalyzerSubNode.ts"], "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { AudioEngineV2 } from \"../audioEngineV2\";\nimport type { AudioAnalyzerFFTSizeType, IAudioAnalyzerOptions } from \"../subProperties/abstractAudioAnalyzer\";\nimport { _AudioAnalyzerDefaults } from \"../subProperties/abstractAudioAnalyzer\";\nimport type { _AbstractAudioSubGraph } from \"./abstractAudioSubGraph\";\nimport { _AbstractAudioSubNode } from \"./abstractAudioSubNode\";\nimport { AudioSubNode } from \"./audioSubNode\";\n\n/** @internal */\nexport abstract class _AudioAnalyzerSubNode extends _AbstractAudioSubNode {\n    protected constructor(engine: AudioEngineV2) {\n        super(AudioSubNode.ANALYZER, engine);\n    }\n\n    public abstract fftSize: AudioAnalyzerFFTSizeType;\n    public abstract minDecibels: number;\n    public abstract maxDecibels: number;\n    public abstract smoothing: number;\n\n    public abstract getByteFrequencyData(): Uint8Array;\n    public abstract getFloatFrequencyData(): Float32Array;\n\n    /** @internal */\n    public setOptions(options: Partial<IAudioAnalyzerOptions>): void {\n        this.fftSize = options.analyzerFFTSize ?? _AudioAnalyzerDefaults.fftSize;\n        this.minDecibels = options.analyzerMinDecibels ?? _AudioAnalyzerDefaults.minDecibels;\n        this.maxDecibels = options.analyzerMaxDecibels ?? _AudioAnalyzerDefaults.maxDecibels;\n        this.smoothing = options.analyzerSmoothing ?? _AudioAnalyzerDefaults.smoothing;\n    }\n}\n\n/** @internal */\nexport function _GetAudioAnalyzerSubNode(subGraph: _AbstractAudioSubGraph): Nullable<_AudioAnalyzerSubNode> {\n    return subGraph.getSubNode<_AudioAnalyzerSubNode>(AudioSubNode.ANALYZER);\n}\n\n/** @internal */\nexport function _SetAudioAnalyzerProperty<K extends keyof typeof _AudioAnalyzerDefaults>(subGraph: _AbstractAudioSubGraph, property: K, value: _AudioAnalyzerSubNode[K]): void {\n    subGraph.callOnSubNode<_AudioAnalyzerSubNode>(AudioSubNode.ANALYZER, (node) => {\n        node[property] = value;\n    });\n}\n"], "names": [], "mappings": ";;;;;AAGA,OAAO,EAAE,sBAAsB,EAAE,MAAM,wCAAwC,CAAC;AAEhF,OAAO,EAAE,qBAAqB,EAAE,MAAM,wBAAwB,CAAC;;;AAIzD,MAAgB,qBAAsB,gNAAQ,wBAAqB;IAarE,cAAA,EAAgB,CACT,UAAU,CAAC,OAAuC,EAAA;YACtC,OAAO;QAAtB,IAAI,CAAC,OAAO,uCAAW,eAAe,4RAAI,yBAAsB,CAAC,OAAO,CAAC;;QACzE,IAAI,CAAC,WAAW,2CAAW,mBAAmB,cAA3B,OAAO,+QAAwB,yBAAsB,CAAC,WAAW,CAAC;;QACrF,IAAI,CAAC,WAAW,mCAAG,OAAO,CAAC,mBAAmB,oSAAI,yBAAsB,CAAC,WAAW,CAAC;;QACrF,IAAI,CAAC,SAAS,yCAAW,iBAAiB,sDAAzB,OAAO,mOAAsB,yBAAsB,CAAC,SAAS,CAAC;IACnF,CAAC;IAlBD,YAAsB,MAAqB,CAAA;QACvC,KAAK,CAAA,WAAA,yBAAA,KAAwB,MAAM,CAAC,CAAC;IACzC,CAAC;CAiBJ;AAGK,SAAU,wBAAwB,CAAC,QAAgC;IACrE,OAAO,QAAQ,CAAC,UAAU,CAAA,WAAA,yBAAA,GAA8C,CAAC;AAC7E,CAAC;AAGK,SAAU,yBAAyB,CAAgD,QAAgC,EAAE,QAAW,EAAE,KAA+B;IACnK,QAAQ,CAAC,aAAa,CAAA,WAAA,yBAAA,KAA+C,CAAC,IAAI,EAAE,EAAE;QAC1E,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;IAC3B,CAAC,CAAC,CAAC;AACP,CAAC", "debugId": null}}, {"offset": {"line": 1478, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subProperties/audioAnalyzer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subProperties/audioAnalyzer.ts"], "sourcesContent": ["import { Logger } from \"../../../Misc/logger\";\nimport type { Nullable } from \"../../../types\";\nimport type { AudioAnalyzerFFTSizeType } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport { _AudioAnalyzerDefaults, AbstractAudioAnalyzer } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport type { _AbstractAudioSubGraph } from \"../subNodes/abstractAudioSubGraph\";\nimport { _GetAudioAnalyzerSubNode, _SetAudioAnalyzerProperty } from \"../subNodes/audioAnalyzerSubNode\";\nimport { AudioSubNode } from \"../subNodes/audioSubNode\";\n\nlet EmptyByteFrequencyData: Nullable<Uint8Array> = null;\nlet EmptyFloatFrequencyData: Nullable<Float32Array> = null;\n\n/** @internal */\nexport function _GetEmptyByteFrequencyData(): Uint8Array {\n    if (!EmptyByteFrequencyData) {\n        EmptyByteFrequencyData = new Uint8Array();\n    }\n    return EmptyByteFrequencyData;\n}\n\n/** @internal */\nexport function _GetEmptyFloatFrequencyData(): Float32Array {\n    if (!EmptyFloatFrequencyData) {\n        EmptyFloatFrequencyData = new Float32Array();\n    }\n    return EmptyFloatFrequencyData;\n}\n\n/** @internal */\nexport class _AudioAnalyzer extends AbstractAudioAnalyzer {\n    private _fftSize: AudioAnalyzerFFTSizeType = _AudioAnalyzerDefaults.fftSize;\n    private _maxDecibels: number = _AudioAnalyzerDefaults.maxDecibels;\n    private _minDecibels: number = _AudioAnalyzerDefaults.minDecibels;\n    private _smoothing: number = _AudioAnalyzerDefaults.smoothing;\n    private _subGraph: _AbstractAudioSubGraph;\n\n    /** @internal */\n    public constructor(subGraph: _AbstractAudioSubGraph) {\n        super();\n        this._subGraph = subGraph;\n    }\n\n    /** @internal */\n    public get fftSize(): AudioAnalyzerFFTSizeType {\n        return this._fftSize;\n    }\n\n    public set fftSize(value: AudioAnalyzerFFTSizeType) {\n        this._fftSize = value;\n        _SetAudioAnalyzerProperty(this._subGraph, \"fftSize\", value);\n    }\n\n    /** @internal */\n    public get isEnabled(): boolean {\n        return _GetAudioAnalyzerSubNode(this._subGraph) !== null;\n    }\n\n    /** @internal */\n    public get minDecibels(): number {\n        return this._minDecibels;\n    }\n\n    public set minDecibels(value: number) {\n        this._minDecibels = value;\n        _SetAudioAnalyzerProperty(this._subGraph, \"minDecibels\", value);\n    }\n\n    /** @internal */\n    public get maxDecibels(): number {\n        return this._maxDecibels;\n    }\n\n    public set maxDecibels(value: number) {\n        this._maxDecibels = value;\n        _SetAudioAnalyzerProperty(this._subGraph, \"maxDecibels\", value);\n    }\n\n    /** @internal */\n    public get smoothing(): number {\n        return this._smoothing;\n    }\n\n    public set smoothing(value: number) {\n        this._smoothing = value;\n        _SetAudioAnalyzerProperty(this._subGraph, \"smoothing\", value);\n    }\n\n    /** @internal */\n    public dispose(): void {\n        const subNode = _GetAudioAnalyzerSubNode(this._subGraph);\n        if (subNode) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this._subGraph.removeSubNodeAsync(subNode);\n            subNode.dispose();\n        }\n    }\n\n    /** @internal */\n    public async enableAsync(): Promise<void> {\n        const subNode = _GetAudioAnalyzerSubNode(this._subGraph);\n        if (!subNode) {\n            await this._subGraph.createAndAddSubNodeAsync(AudioSubNode.ANALYZER);\n        }\n    }\n\n    /** @internal */\n    public getByteFrequencyData(): Uint8Array {\n        const subNode = _GetAudioAnalyzerSubNode(this._subGraph);\n        if (!subNode) {\n            Logger.Warn(\"AudioAnalyzer not enabled\");\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.enableAsync();\n            return _GetEmptyByteFrequencyData();\n        }\n        return subNode.getByteFrequencyData();\n    }\n\n    /** @internal */\n    public getFloatFrequencyData(): Float32Array {\n        const subNode = _GetAudioAnalyzerSubNode(this._subGraph);\n        if (!subNode) {\n            Logger.Warn(\"AudioAnalyzer not enabled\");\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.enableAsync();\n            return _GetEmptyFloatFrequencyData();\n        }\n        return subNode.getFloatFrequencyData();\n    }\n}\n"], "names": [], "mappings": ";;;;;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAG9C,OAAO,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,MAAM,yDAAyD,CAAC;AAExH,OAAO,EAAE,wBAAwB,EAAE,yBAAyB,EAAE,MAAM,kCAAkC,CAAC;;;;AAGvG,IAAI,sBAAsB,GAAyB,IAAI,CAAC;AACxD,IAAI,uBAAuB,GAA2B,IAAI,CAAC;AAGrD,SAAU,0BAA0B;IACtC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC1B,sBAAsB,GAAG,IAAI,UAAU,EAAE,CAAC;IAC9C,CAAC;IACD,OAAO,sBAAsB,CAAC;AAClC,CAAC;AAGK,SAAU,2BAA2B;IACvC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC3B,uBAAuB,GAAG,IAAI,YAAY,EAAE,CAAC;IACjD,CAAC;IACD,OAAO,uBAAuB,CAAC;AACnC,CAAC;AAGK,MAAO,cAAe,qNAAQ,yBAAqB;IAarD,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAA+B,EAAA;QAC9C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;mNACtB,4BAAA,AAAyB,EAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,SAAS,GAAA;QAChB,kNAAO,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC;IAC7D,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;mNAC1B,4BAAA,AAAyB,EAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;mNAC1B,4BAAA,AAAyB,EAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAa,EAAA;QAC9B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;mNACxB,4BAAA,AAAyB,EAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IAED,cAAA,EAAgB,CACT,OAAO,GAAA;QACV,MAAM,OAAO,8MAAG,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,OAAO,EAAE,CAAC;YACV,mEAAmE;YACnE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC3C,OAAO,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,WAAW,GAAA;QACpB,MAAM,OAAO,8MAAG,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAA,WAAA,yBAAA,GAAuB,CAAC;QACzE,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,oBAAoB,GAAA;QACvB,MAAM,OAAO,8MAAG,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,kKAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,mEAAmE;YACnE,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,0BAA0B,EAAE,CAAC;QACxC,CAAC;QACD,OAAO,OAAO,CAAC,oBAAoB,EAAE,CAAC;IAC1C,CAAC;IAED,cAAA,EAAgB,CACT,qBAAqB,GAAA;QACxB,MAAM,OAAO,8MAAG,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,EAAE,CAAC;qKACX,SAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,mEAAmE;YACnE,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,2BAA2B,EAAE,CAAC;QACzC,CAAC;QACD,OAAO,OAAO,CAAC,qBAAqB,EAAE,CAAC;IAC3C,CAAC;IA3FD,cAAA,EAAgB,CAChB,YAAmB,QAAgC,CAAA;QAC/C,KAAK,EAAE,CAAC;QARJ,IAAA,CAAA,QAAQ,gNAA6B,yBAAsB,CAAC,OAAO,CAAC;QACpE,IAAA,CAAA,YAAY,gNAAW,yBAAsB,CAAC,WAAW,CAAC;QAC1D,IAAA,CAAA,YAAY,gNAAW,yBAAsB,CAAC,WAAW,CAAC;QAC1D,IAAA,CAAA,UAAU,gNAAW,yBAAsB,CAAC,SAAS,CAAC;QAM1D,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;CAwFJ", "debugId": null}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/abstractAudioOutNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/abstractAudioOutNode.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { IAudioParameterRampOptions } from \"../audioParameter\";\nimport type { AudioNodeType } from \"./abstractAudioNode\";\nimport { AbstractNamedAudioNode } from \"./abstractAudioNode\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { _AbstractAudioSubGraph } from \"./subNodes/abstractAudioSubGraph\";\nimport type { IVolumeAudioOptions } from \"./subNodes/volumeAudioSubNode\";\nimport { _GetVolumeAudioProperty, _GetVolumeAudioSubNode } from \"./subNodes/volumeAudioSubNode\";\nimport type { AbstractAudioAnalyzer, IAudioAnalyzerOptions } from \"./subProperties/abstractAudioAnalyzer\";\nimport { _AudioAnalyzer } from \"./subProperties/audioAnalyzer\";\n\n/** @internal */\nexport interface IAbstractAudioOutNodeOptions extends IAudioAnalyzerOptions, IVolumeAudioOptions {}\n\n/**\n * Abstract class representing and audio output node with an analyzer and volume control.\n */\nexport abstract class AbstractAudioOutNode extends AbstractNamedAudioNode {\n    private _analyzer: Nullable<AbstractAudioAnalyzer> = null;\n\n    protected abstract _subGraph: _AbstractAudioSubGraph;\n\n    protected constructor(name: string, engine: AudioEngineV2, nodeType: AudioNodeType) {\n        super(name, engine, nodeType);\n    }\n\n    /**\n     * The analyzer features of the bus.\n     */\n    public get analyzer(): AbstractAudioAnalyzer {\n        return this._analyzer ?? (this._analyzer = new _AudioAnalyzer(this._subGraph));\n    }\n\n    /**\n     * The audio output volume.\n     */\n\n    public get volume(): number {\n        return _GetVolumeAudioProperty(this._subGraph, \"volume\");\n    }\n\n    public set volume(value: number) {\n        // The volume subnode is created on initialization and should always exist.\n        const node = _GetVolumeAudioSubNode(this._subGraph);\n        if (!node) {\n            throw new Error(\"No volume subnode\");\n        }\n\n        node.volume = value;\n    }\n\n    /**\n     * Releases associated resources.\n     */\n    public override dispose(): void {\n        super.dispose();\n\n        this._analyzer?.dispose();\n        this._analyzer = null;\n\n        this._subGraph.dispose();\n    }\n\n    /**\n     * Sets the audio output volume with optional ramping.\n     * If the duration is 0 then the volume is set immediately, otherwise it is ramped to the new value over the given duration using the given shape.\n     * If a ramp is already in progress then the volume is not set and an error is thrown.\n     * @param value The value to set the volume to.\n     * @param options The options to use for ramping the volume change.\n     */\n    public setVolume(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        const node = _GetVolumeAudioSubNode(this._subGraph);\n        if (!node) {\n            throw new Error(\"No volume subnode\");\n        }\n\n        node.setVolume(value, options);\n    }\n}\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAI7D,OAAO,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,MAAM,+BAA+B,CAAC;AAEhG,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;;;;AAQzD,MAAgB,oBAAqB,iMAAQ,yBAAsB;IASrE;;OAEG,CACH,IAAW,QAAQ,GAAA;;QACf,0BAAO,IAAI,CAAC,SAAS,IAAI,yDAAC,IAAI,CAAC,SAAS,GAAG,yMAAI,iBAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG,CAEH,IAAW,MAAM,GAAA;QACb,gNAAO,0BAAA,AAAuB,EAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,2EAA2E;QAC3E,MAAM,IAAI,4MAAG,yBAAA,AAAsB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;SAEhB,sBAAI,CAAC,SAAS,oEAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;OAMG,CACI,SAAS,CAAC,KAAa,EAA+D;sBAA7D,iEAAyD,IAAI;QACzF,MAAM,IAAI,4MAAG,yBAAA,AAAsB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACnC,CAAC;IAvDD,YAAsB,IAAY,EAAE,MAAqB,EAAE,QAAuB,CAAA;QAC9E,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAL1B,IAAA,CAAA,SAAS,GAAoC,IAAI,CAAC;IAM1D,CAAC;CAsDJ", "debugId": null}}, {"offset": {"line": 1643, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/abstractAudioBus.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/abstractAudioBus.ts"], "sourcesContent": ["import { AudioNodeType } from \"./abstractAudioNode\";\nimport type { IAbstractAudioOutNodeOptions } from \"./abstractAudioOutNode\";\nimport { AbstractAudioOutNode } from \"./abstractAudioOutNode\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\n\n/** @internal */\nexport interface IAbstractAudioBusOptions extends IAbstractAudioOutNodeOptions {}\n\n/**\n * Abstract class representing an audio bus with volume control.\n *\n * An audio bus is a node in the audio graph that can have multiple inputs and outputs. It is typically used to group\n * sounds together and apply effects to them.\n */\nexport abstract class AbstractAudioBus extends AbstractAudioOutNode {\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine, AudioNodeType.HAS_INPUTS_AND_OUTPUTS);\n    }\n}\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;;AAYxD,MAAgB,gBAAiB,oMAAQ,uBAAoB;IAC/D,YAAsB,IAAY,EAAE,MAAqB,CAAA;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,EAAA,EAAA,wCAAA,GAAuC,CAAC;IAC9D,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/abstractSoundSource.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/abstractSoundSource.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport { AudioNodeType } from \"./abstractAudioNode\";\nimport type { IAbstractAudioOutNodeOptions } from \"./abstractAudioOutNode\";\nimport { AbstractAudioOutNode } from \"./abstractAudioOutNode\";\nimport type { PrimaryAudioBus } from \"./audioBus\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { AbstractSpatialAudio, ISpatialAudioOptions } from \"./subProperties/abstractSpatialAudio\";\nimport type { AbstractStereoAudio, IStereoAudioOptions } from \"./subProperties/abstractStereoAudio\";\n\n/**\n * Options for creating a sound source.\n */\nexport interface ISoundSourceOptions extends IAbstractAudioOutNodeOptions, ISpatialAudioOptions, IStereoAudioOptions {\n    /**\n     * The output bus for the sound source. Defaults to `null`.\n     * - If not set or `null`, and `outBusAutoDefault` is `true`, then the sound source is automatically connected to the audio engine's default main bus.\n     * @see {@link AudioEngineV2.defaultMainBus}\n     */\n    outBus: Nullable<PrimaryAudioBus>;\n\n    /**\n     * Whether the sound's `outBus` should default to the audio engine's main bus. Defaults to `true` for all sound sources except microphones.\n     */\n    outBusAutoDefault: boolean;\n}\n\n/**\n * Abstract class representing a sound in the audio engine.\n */\nexport abstract class AbstractSoundSource extends AbstractAudioOutNode {\n    private _outBus: Nullable<PrimaryAudioBus> = null;\n\n    protected constructor(name: string, engine: AudioEngineV2, nodeType: AudioNodeType = AudioNodeType.HAS_OUTPUTS) {\n        super(name, engine, nodeType);\n    }\n\n    /**\n     * The output bus for the sound.\n     * @see {@link AudioEngineV2.defaultMainBus}\n     */\n    public get outBus(): Nullable<PrimaryAudioBus> {\n        return this._outBus;\n    }\n\n    public set outBus(outBus: Nullable<PrimaryAudioBus>) {\n        if (this._outBus === outBus) {\n            return;\n        }\n\n        if (this._outBus) {\n            this._outBus.onDisposeObservable.removeCallback(this._onOutBusDisposed);\n            if (!this._disconnect(this._outBus)) {\n                throw new Error(\"Disconnect failed\");\n            }\n        }\n\n        this._outBus = outBus;\n\n        if (this._outBus) {\n            this._outBus.onDisposeObservable.add(this._onOutBusDisposed);\n            if (!this._connect(this._outBus)) {\n                throw new Error(\"Connect failed\");\n            }\n        }\n    }\n\n    /**\n     * The spatial features of the sound.\n     */\n    public abstract spatial: AbstractSpatialAudio;\n\n    /**\n     * The stereo features of the sound.\n     */\n    public abstract stereo: AbstractStereoAudio;\n\n    /**\n     * Releases associated resources.\n     */\n    public override dispose(): void {\n        super.dispose();\n\n        this._outBus = null;\n    }\n\n    private _onOutBusDisposed = () => {\n        this._outBus = null;\n    };\n}\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;;AA0BxD,MAAgB,mBAAoB,oMAAQ,uBAAoB;IAOlE;;;OAGG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,MAAiC,EAAA;QAC/C,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACxE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;IACL,CAAC;IAYD;;OAEG,CACa,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAnDD,YAAsB,IAAY,EAAE,MAAqB,EAAE,WAAA,EAAA,6BAAA,EAAmD,CAAnD,CAAmD;QAC1G,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAH1B,IAAA,CAAA,OAAO,GAA8B,IAAI,CAAC;QAuD1C,IAAA,CAAA,iBAAiB,GAAG,GAAG,EAAE;YAC7B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,CAAC,CAAC;IArDF,CAAC;CAsDJ", "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/abstractSound.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/abstractSound.ts"], "sourcesContent": ["import { Observable } from \"../../Misc/observable\";\nimport type { Nullable } from \"../../types\";\nimport { SoundState } from \"../soundState\";\nimport { AudioNodeType } from \"./abstractAudioNode\";\nimport type { _AbstractSoundInstance } from \"./abstractSoundInstance\";\nimport { AbstractSoundSource, type ISoundSourceOptions } from \"./abstractSoundSource\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { IVolumeAudioOptions } from \"./subNodes/volumeAudioSubNode\";\n\n/** @internal */\nexport interface IAbstractSoundOptionsBase {\n    /**\n     * Whether the sound should start playing automatically. Defaults to `false`.\n     */\n    autoplay: boolean;\n    /**\n     * The maximum number of instances that can play at the same time. Defaults to `Infinity`.\n     */\n    maxInstances: number;\n}\n\n/** @internal */\nexport interface IAbstractSoundPlayOptionsBase {\n    /**\n     * Whether the sound should loop. Defaults to `false`.\n     */\n    loop: boolean;\n    /**\n     * The time within the sound buffer to start playing at, in seconds. Defaults to `0`.\n     */\n    startOffset: number;\n}\n\n/**\n * Options for creating a sound.\n */\nexport interface IAbstractSoundOptions extends IAbstractSoundOptionsBase, IAbstractSoundPlayOptions, ISoundSourceOptions {}\n\n/**\n * Options for playing a sound.\n */\nexport interface IAbstractSoundPlayOptions extends IAbstractSoundPlayOptionsBase, IVolumeAudioOptions {}\n\n/**\n * Options stored in a sound.\n * @internal\n */\nexport interface IAbstractSoundStoredOptions extends IAbstractSoundOptionsBase, IAbstractSoundPlayOptionsBase {}\n\n/**\n * Abstract class representing a sound in the audio engine.\n */\nexport abstract class AbstractSound extends AbstractSoundSource {\n    private _newestInstance: Nullable<_AbstractSoundInstance> = null;\n    private _privateInstances = new Set<_AbstractSoundInstance>();\n    private _state: SoundState = SoundState.Stopped;\n\n    protected _instances: ReadonlySet<_AbstractSoundInstance> = this._privateInstances;\n    protected abstract readonly _options: IAbstractSoundStoredOptions;\n\n    /**\n     * Observable for when the sound stops playing.\n     */\n    public readonly onEndedObservable = new Observable<AbstractSound>();\n\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine, AudioNodeType.HAS_INPUTS_AND_OUTPUTS); // Inputs are for instances.\n    }\n\n    /**\n     * Whether the sound should start playing automatically. Defaults to `false`.\n     */\n    public get autoplay(): boolean {\n        return this._options.autoplay;\n    }\n\n    /**\n     * The current playback time of the sound, in seconds.\n     */\n    public get currentTime(): number {\n        const instance = this._getNewestInstance();\n        return instance ? instance.currentTime : 0;\n    }\n\n    public set currentTime(value: number) {\n        this.startOffset = value;\n\n        const instance = this._getNewestInstance();\n        if (instance) {\n            instance.currentTime = value;\n        }\n    }\n\n    /**\n     * Whether the sound should loop. Defaults to `false`.\n     */\n    public get loop(): boolean {\n        return this._options.loop;\n    }\n\n    public set loop(value: boolean) {\n        this._options.loop = value;\n    }\n\n    /**\n     * The maximum number of instances that can play at the same time. Defaults to `Infinity`.\n     */\n    public get maxInstances(): number {\n        return this._options.maxInstances;\n    }\n\n    public set maxInstances(value: number) {\n        this._options.maxInstances = value;\n    }\n\n    /**\n     * The time within the sound buffer to start playing at, in seconds. Defaults to `0`.\n     */\n    public get startOffset(): number {\n        return this._options.startOffset;\n    }\n\n    public set startOffset(value: number) {\n        this._options.startOffset = value;\n    }\n\n    /**\n     * The state of the sound.\n     */\n    public get state(): SoundState {\n        return this._state;\n    }\n\n    /**\n     * Releases associated resources.\n     */\n    public override dispose(): void {\n        super.dispose();\n\n        this.stop();\n\n        this._newestInstance = null;\n\n        this._privateInstances.clear();\n        this.onEndedObservable.clear();\n    }\n\n    /**\n     * Plays the sound.\n     * - Triggers `onEndedObservable` if played for the full duration and the `loop` option is not set.\n     * @param options The options to use when playing the sound. Options set here override the sound's options.\n     */\n    public abstract play(options?: Partial<IAbstractSoundPlayOptions>): void;\n\n    /**\n     * Pauses the sound.\n     */\n    public pause(): void {\n        const it = this._instances.values();\n        for (let next = it.next(); !next.done; next = it.next()) {\n            next.value.pause();\n        }\n\n        this._state = SoundState.Paused;\n    }\n\n    /**\n     * Resumes the sound.\n     */\n    public resume(): void {\n        if (this._state !== SoundState.Paused) {\n            return;\n        }\n\n        const it = this._instances.values();\n        for (let next = it.next(); !next.done; next = it.next()) {\n            next.value.resume();\n        }\n\n        this._state = SoundState.Started;\n    }\n\n    /**\n     * Stops the sound.\n     * - Triggers `onEndedObservable` if the sound is playing.\n     */\n    public abstract stop(): void;\n\n    protected _beforePlay(instance: _AbstractSoundInstance): void {\n        if (this.state === SoundState.Paused && this._instances.size > 0) {\n            this.resume();\n            return;\n        }\n\n        instance.onEndedObservable.addOnce(this._onInstanceEnded);\n        this._privateInstances.add(instance);\n        this._newestInstance = instance;\n    }\n\n    protected _afterPlay(instance: _AbstractSoundInstance): void {\n        this._state = instance.state;\n    }\n\n    protected _getNewestInstance(): Nullable<_AbstractSoundInstance> {\n        if (this._instances.size === 0) {\n            return null;\n        }\n\n        if (!this._newestInstance) {\n            const it = this._instances.values();\n            for (let next = it.next(); !next.done; next = it.next()) {\n                this._newestInstance = next.value;\n            }\n        }\n\n        return this._newestInstance;\n    }\n\n    protected _setState(state: SoundState): void {\n        this._state = state;\n    }\n\n    protected abstract _createInstance(): _AbstractSoundInstance;\n\n    protected _stopExcessInstances(): void {\n        if (this.maxInstances < Infinity) {\n            const numberOfInstancesToStop = Array.from(this._instances).filter((instance) => instance.state === SoundState.Started).length - this.maxInstances;\n            const it = this._instances.values();\n\n            for (let i = 0; i < numberOfInstancesToStop; i++) {\n                const instance = it.next().value;\n                instance.stop();\n            }\n        }\n    }\n\n    private _onInstanceEnded: (instance: _AbstractSoundInstance) => void = (instance) => {\n        if (this._newestInstance === instance) {\n            this._newestInstance = null;\n        }\n\n        this._privateInstances.delete(instance);\n\n        if (this._instances.size === 0) {\n            this._state = SoundState.Stopped;\n            this.onEndedObservable.notifyObservers(this);\n        }\n    };\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAKnD,OAAO,EAAE,mBAAmB,EAA4B,MAAM,uBAAuB,CAAC;;;AA+ChF,MAAgB,aAAc,mMAAQ,sBAAmB;IAiB3D;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3C,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3C,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,IAAW,IAAI,CAAC,KAAc,EAAA;QAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;IACtC,CAAC;IAED,IAAW,YAAY,CAAC,KAAa,EAAA;QACjC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;IACvC,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IACrC,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;IACtC,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;IASD;;OAEG,CACI,KAAK,GAAA;QACR,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,IAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAE,CAAC;YACtD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,MAAM,GAAA,EAAA,qBAAA,EAAoB,CAAC;IACpC,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,qBAAA,EAAsB,GAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,IAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAE,CAAC;YACtD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,MAAM,GAAA,EAAA,sBAAA,EAAqB,CAAC;IACrC,CAAC;IAQS,WAAW,CAAC,QAAgC,EAAA;QAClD,IAAI,IAAI,CAAC,KAAK,KAAA,EAAA,qBAAA,EAAsB,KAAI,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC/D,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;IACpC,CAAC;IAES,UAAU,CAAC,QAAgC,EAAA;QACjD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;IACjC,CAAC;IAES,kBAAkB,GAAA;QACxB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACpC,IAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAE,CAAC;gBACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC;YACtC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAES,SAAS,CAAC,KAAiB,EAAA;QACjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAIS,oBAAoB,GAAA;QAC1B,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,EAAE,CAAC;YAC/B,MAAM,uBAAuB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAG,CAAD,OAAS,CAAC,KAAK,KAAA,EAAA,sBAAA,EAAuB,CAAC,EAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;YACnJ,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAEpC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,uBAAuB,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC/C,MAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;gBACjC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;IACL,CAAC;IAzKD,YAAsB,IAAY,EAAE,MAAqB,CAAA;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,EAAA,EAAA,wCAAA,GAAuC,CAAC,EAAC,4BAA4B;QAbnF,IAAA,CAAA,eAAe,GAAqC,IAAI,CAAC;QACzD,IAAA,CAAA,iBAAiB,GAAG,IAAI,GAAG,EAA0B,CAAC;QACtD,IAAA,CAAA,MAAM,GAAA,EAAA,sBAAA,GAAkC;QAEtC,IAAA,CAAA,UAAU,GAAwC,IAAI,CAAC,iBAAiB,CAAC;QAGnF;;WAEG,CACa,IAAA,CAAA,iBAAiB,GAAG,iKAAI,aAAU,EAAiB,CAAC;QA6K5D,IAAA,CAAA,gBAAgB,GAA+C,CAAC,QAAQ,EAAE,EAAE;YAChF,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;gBACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAChC,CAAC;YAED,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAExC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,GAAA,EAAA,sBAAA,EAAqB,CAAC;gBACjC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC;QACL,CAAC,CAAC;IApLF,CAAC;CAqLJ", "debugId": null}}, {"offset": {"line": 1852, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/audioBus.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/audioBus.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { IAbstractAudioBusOptions } from \"./abstractAudioBus\";\nimport { AbstractAudioBus } from \"./abstractAudioBus\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { MainAudioBus } from \"./mainAudioBus\";\nimport type { AbstractSpatialAudio, ISpatialAudioOptions } from \"./subProperties/abstractSpatialAudio\";\nimport type { AbstractStereoAudio, IStereoAudioOptions } from \"./subProperties/abstractStereoAudio\";\n\n// NB: Secondary audio buses will be added later.\nexport type PrimaryAudioBus = MainAudioBus | AudioBus;\n\n/**\n * Options for creating an audio bus.\n */\nexport interface IAudioBusOptions extends IAbstractAudioBusOptions, ISpatialAudioOptions, IStereoAudioOptions {\n    /**\n     * The output bus of the audio bus. Defaults to the audio engine's default main bus.\n     * @see {@link AudioEngineV2.defaultMainBus}\n     */\n    outBus: PrimaryAudioBus;\n}\n\n/**\n * Abstract class for an audio bus that has spatial audio and stereo output capabilities.\n *\n * Instances of this class can be connected to other audio buses.\n *\n * Audio buses are created by the {@link CreateAudioBusAsync} function.\n */\nexport abstract class AudioBus extends AbstractAudioBus {\n    private _outBus: Nullable<PrimaryAudioBus> = null;\n\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine);\n    }\n\n    /**\n     * The output bus of the audio bus. Defaults to the audio engine's default main bus.\n     */\n    public get outBus(): Nullable<PrimaryAudioBus> {\n        return this._outBus;\n    }\n\n    public set outBus(outBus: Nullable<PrimaryAudioBus>) {\n        if (this._outBus === outBus) {\n            return;\n        }\n\n        if (this._outBus) {\n            this._outBus.onDisposeObservable.removeCallback(this._onOutBusDisposed);\n\n            if (!this._disconnect(this._outBus)) {\n                throw new Error(\"Disconnect failed\");\n            }\n        }\n\n        this._outBus = outBus;\n\n        if (this._outBus) {\n            this._outBus.onDisposeObservable.add(this._onOutBusDisposed);\n\n            if (!this._connect(this._outBus)) {\n                throw new Error(\"Connect failed\");\n            }\n        }\n    }\n\n    /**\n     * The spatial features of the audio bus.\n     */\n    public abstract readonly spatial: AbstractSpatialAudio;\n\n    /**\n     * The stereo features of the audio bus.\n     */\n    public abstract readonly stereo: AbstractStereoAudio;\n\n    /**\n     * Releases associated resources.\n     */\n    public override dispose(): void {\n        super.dispose();\n        this._outBus = null;\n    }\n\n    private _onOutBusDisposed = () => {\n        this.outBus = this.engine.defaultMainBus;\n    };\n}\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;;AA2BhD,MAAgB,QAAS,gMAAQ,mBAAgB;IAOnD;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,MAAiC,EAAA;QAC/C,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAExE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAE7D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;IACL,CAAC;IAYD;;OAEG,CACa,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAnDD,YAAsB,IAAY,EAAE,MAAqB,CAAA;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAHhB,IAAA,CAAA,OAAO,GAA8B,IAAI,CAAC;QAuD1C,IAAA,CAAA,iBAAiB,GAAG,GAAG,EAAE;YAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,CAAC,CAAC;IArDF,CAAC;CAsDJ", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/mainAudioBus.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/mainAudioBus.ts"], "sourcesContent": ["import { AbstractAudioBus } from \"./abstractAudioBus\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { IAbstractAudioBusOptions } from \"./abstractAudioBus\";\n\n/**\n * Options for creating a main audio bus.\n */\nexport interface IMainAudioBusOptions extends IAbstractAudioBusOptions {}\n\n/**\n * Abstract class representing a main audio bus.\n *\n * Main audio buses are the last bus in the audio graph.\n *\n * Unlike {@link AudioBus} instances, `MainAudioBus` instances have no spatial audio and stereo output capabilities,\n * and they cannot be connected downstream to another audio bus. They only connect downstream to the audio engine's\n * main output.\n *\n * Main audio buses are created by the {@link CreateMainAudioBusAsync} function.\n */\nexport abstract class MainAudioBus extends AbstractAudioBus {\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;;AAoBhD,MAAgB,YAAa,gMAAQ,mBAAgB;IACvD,YAAsB,IAAY,EAAE,MAAqB,CAAA;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACxB,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 1913, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/staticSound.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/staticSound.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport { SoundState } from \"../soundState\";\nimport type { IAbstractSoundOptions, IAbstractSoundPlayOptions, IAbstractSoundStoredOptions } from \"./abstractSound\";\nimport { AbstractSound } from \"./abstractSound\";\nimport type { PrimaryAudioBus } from \"./audioBus\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { IStaticSoundBufferOptions, StaticSoundBuffer } from \"./staticSoundBuffer\";\nimport type { _StaticSoundInstance } from \"./staticSoundInstance\";\n\n/** @internal */\nexport interface IStaticSoundOptionsBase {\n    /**\n     * The amount of time to play the sound for, in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the sound plays for its full duration.\n     */\n    duration: number;\n    /**\n     * The end of the loop range in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the loop plays for the sound's full duration.\n     * - Has no effect if {@link loop} is `false`.\n     */\n    loopEnd: number;\n    /**\n     * The start of the loop range in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the loop starts at the beginning of the sound.\n     * - Has no effect if {@link loop} is `false`.\n     *\n     */\n    loopStart: number;\n}\n\n/**\n * Options stored in a static sound.\n * @internal\n */\nexport interface IStaticSoundStoredOptions extends IAbstractSoundStoredOptions, IStaticSoundOptionsBase {\n    /**\n     * The pitch of the sound, in cents. Defaults to `0`.\n     * - Can be combined with {@link playbackRate}.\n     */\n    pitch: number;\n    /**\n     * The playback rate of the sound. Defaults to `1`.\n     * - Can be combined with {@link pitch}.\n     */\n    playbackRate: number;\n}\n\n/**\n * Options for creating a static sound.\n */\nexport interface IStaticSoundOptions extends IAbstractSoundOptions, IStaticSoundBufferOptions, IStaticSoundStoredOptions {}\n\n/**\n * Options for playing a static sound.\n */\nexport interface IStaticSoundPlayOptions extends IAbstractSoundPlayOptions, IStaticSoundOptionsBase {\n    /**\n     * The time to wait before playing the sound, in seconds. Defaults to `0`.\n     */\n    waitTime: number;\n}\n\n/**\n * Options for stopping a static sound.\n */\nexport interface IStaticSoundStopOptions {\n    /**\n     * The time to wait before stopping the sound, in seconds. Defaults to `0`.\n     */\n    waitTime: number;\n}\n\n/**\n * Options for cloning a static sound.\n * - @see {@link StaticSound.clone}.\n */\nexport interface IStaticSoundCloneOptions {\n    /**\n     * Whether to clone the sound buffer when cloning the sound. Defaults to `false`.\n     * - If `true`, the original sound's buffer is cloned, and the cloned sound will use its own copy.\n     * - If `false`, the sound buffer is shared with the original sound.\n     */\n    cloneBuffer: boolean;\n\n    /**\n     * The output bus for the cloned sound. Defaults to `null`.\n     * - If not set or `null`, the cloned sound uses the original sound's `outBus`.\n     * @see {@link AudioEngineV2.defaultMainBus}\n     */\n    outBus: Nullable<PrimaryAudioBus>;\n}\n\n/**\n * Abstract class representing a static sound.\n *\n * A static sound has a sound buffer that is loaded into memory all at once. This allows it to have more capabilities\n * than a streaming sound, such as loop points and playback rate changes, but it also means that the sound must be\n * fully downloaded and decoded before it can be played, which may take a long time for sounds with long durations.\n *\n * To prevent downloading and decoding a sound multiple times, a sound's buffer can be shared with other sounds.\n * See {@link CreateSoundBufferAsync}, {@link StaticSoundBuffer} and {@link StaticSound.buffer} for more information.\n *\n * Static sounds are created by the {@link CreateSoundAsync} function.\n */\nexport abstract class StaticSound extends AbstractSound {\n    protected override _instances: Set<_StaticSoundInstance>;\n    protected abstract override readonly _options: IStaticSoundStoredOptions;\n\n    /**\n     * The sound buffer that the sound uses.\n     *\n     * This buffer can be shared with other static sounds.\n     */\n    public abstract readonly buffer: StaticSoundBuffer;\n\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine);\n    }\n\n    /**\n     * The amount of time to play the sound for, in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the sound plays for its full duration.\n     */\n    public get duration(): number {\n        return this._options.duration;\n    }\n\n    public set duration(value: number) {\n        this._options.duration = value;\n    }\n\n    /**\n     * The start of the loop range, in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the loop starts at the beginning of the sound.\n     */\n    public get loopStart(): number {\n        return this._options.loopStart;\n    }\n\n    public set loopStart(value: number) {\n        this._options.loopStart = value;\n    }\n\n    /**\n     * The end of the loop range, in seconds. Defaults to `0`.\n     * - If less than or equal to `0`, the loop plays for the sound's full duration.\n     */\n    public get loopEnd(): number {\n        return this._options.loopEnd;\n    }\n\n    public set loopEnd(value: number) {\n        this._options.loopEnd = value;\n    }\n\n    /**\n     * The pitch of the sound, in cents. Defaults to `0`.\n     * - Gets combined with {@link playbackRate} to determine the final pitch.\n     */\n    public get pitch(): number {\n        return this._options.pitch;\n    }\n\n    public set pitch(value: number) {\n        this._options.pitch = value;\n\n        const it = this._instances.values();\n        for (let instance = it.next(); !instance.done; instance = it.next()) {\n            instance.value.pitch = value;\n        }\n    }\n\n    /**\n     * The playback rate of the sound. Defaults to `1`.\n     * - Gets combined with {@link pitch} to determine the final playback rate.\n     */\n    public get playbackRate(): number {\n        return this._options.playbackRate;\n    }\n\n    public set playbackRate(value: number) {\n        this._options.playbackRate = value;\n\n        const it = this._instances.values();\n        for (let instance = it.next(); !instance.done; instance = it.next()) {\n            instance.value.playbackRate = value;\n        }\n    }\n\n    /**\n     * Clones the sound.\n     * @param options Options for cloning the sound.\n     */\n    public abstract cloneAsync(options?: Partial<IStaticSoundCloneOptions>): Promise<StaticSound>;\n\n    /**\n     * Plays the sound.\n     * - Triggers `onEndedObservable` if played for the full duration and the `loop` option is not set.\n     * @param options The options to use when playing the sound. Options set here override the sound's options.\n     */\n    public play(options: Partial<IStaticSoundPlayOptions> = {}): void {\n        if (this.state === SoundState.Paused) {\n            this.resume();\n            return;\n        }\n\n        options.duration ??= this.duration;\n        options.loop ??= this.loop;\n        options.loopStart ??= this.loopStart;\n        options.loopEnd ??= this.loopEnd;\n        options.startOffset ??= this.startOffset;\n        options.volume ??= 1;\n        options.waitTime ??= 0;\n\n        const instance = this._createInstance();\n        this._beforePlay(instance);\n        instance.play(options);\n        this._afterPlay(instance);\n\n        this._stopExcessInstances();\n    }\n\n    /**\n     * Stops the sound.\n     * - Triggers `onEndedObservable` if the sound is playing.\n     * @param options - The options to use when stopping the sound.\n     */\n    public stop(options: Partial<IStaticSoundStopOptions> = {}): void {\n        if (options.waitTime && 0 < options.waitTime) {\n            this._setState(SoundState.Stopping);\n        } else {\n            this._setState(SoundState.Stopped);\n        }\n\n        if (!this._instances) {\n            return;\n        }\n\n        for (const instance of Array.from(this._instances)) {\n            instance.stop(options);\n        }\n    }\n\n    protected abstract override _createInstance(): _StaticSoundInstance;\n}\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;;AAsG1C,MAAgB,WAAY,6LAAQ,gBAAa;IAenD;;;OAGG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa,EAAA;QAC7B,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;;OAGG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IACnC,CAAC;IAED,IAAW,SAAS,CAAC,KAAa,EAAA;QAC9B,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;IACpC,CAAC;IAED;;;OAGG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;IACjC,CAAC;IAED,IAAW,OAAO,CAAC,KAAa,EAAA;QAC5B,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;;OAGG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;IAC/B,CAAC;IAED,IAAW,KAAK,CAAC,KAAa,EAAA;QAC1B,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;QAE5B,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,IAAK,IAAI,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,CAAE,CAAC;YAClE,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;OAGG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;IACtC,CAAC;IAED,IAAW,YAAY,CAAC,KAAa,EAAA;QACjC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;QAEnC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,IAAK,IAAI,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,CAAE,CAAC;YAClE,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;QACxC,CAAC;IACL,CAAC;IAQD;;;;OAIG,CACI,IAAI,GAA+C;sBAA9C,iEAA4C,CAAA,CAAE;QACtD,IAAI,IAAI,CAAC,KAAK,KAAA,EAAA,qBAAA,EAAsB,GAAE,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO;QACX,CAAC;;qCAEO,QAAQ,6CAAhB,OAAO,aAAP,OAAO,CAAC,QAAQ,GAAK,IAAI,CAAC,QAAQ,EAAC;;iCAC3B,IAAI,yCAAZ,OAAO,SAAP,OAAO,CAAC,IAAI,GAAK,IAAI,CAAC,IAAI,EAAC;;sCACnB,SAAS,cAAjB,OAAO,8CAAP,OAAO,CAAC,SAAS,GAAK,IAAI,CAAC,SAAS,EAAC;;oCAC7B,OAAO,cAAf,OAAO,0CAAP,OAAO,CAAC,OAAO,GAAK,IAAI,CAAC,OAAO,EAAC;;wCACzB,WAAW,gDAAnB,OAAO,gBAAP,OAAO,CAAC,WAAW,GAAK,IAAI,CAAC,WAAW,EAAC;;mCACjC,MAAM,2CAAd,OAAO,WAAP,OAAO,CAAC,MAAM,GAAK,CAAC,EAAC;YACrB,OAAO;qCAAC,QAAQ,iEAAhB,OAAO,CAAC,QAAQ,GAAK,CAAC,EAAC;QAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACxC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3B,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE1B,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;;;OAIG,CACI,IAAI,GAA+C;YAA9C,2EAA4C,CAAA,CAAE;QACtD,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC3C,IAAI,CAAC,SAAS,CAAA,EAAA,uBAAA,GAAqB,CAAC;QACxC,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,SAAS,CAAA,EAAA,sBAAA,GAAoB,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAE,CAAC;YACjD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3B,CAAC;IACL,CAAC;IA9HD,YAAsB,IAAY,EAAE,MAAqB,CAAA;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACxB,CAAC;CA+HJ", "debugId": null}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/staticSoundBuffer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/staticSoundBuffer.ts"], "sourcesContent": ["import type { AudioEngineV2 } from \"./audioEngineV2\";\n\nlet StaticSoundBufferId = 1;\n\n/**\n * Options for creating a static sound buffer.\n */\nexport interface IStaticSoundBufferOptions {\n    /**\n     * Whether to skip codec checking before attempting to load each source URL when `source` is a string array. Defaults to `false`.\n     * - Has no effect if the sound's source is not a string array.\n     * @see {@link CreateSoundAsync} `source` parameter.\n     */\n    skipCodecCheck: boolean;\n}\n\n/**\n * Options for cloning a static sound buffer.\n * - @see {@link StaticSoundBuffer.clone}.\n */\nexport interface IStaticSoundBufferCloneOptions {\n    /**\n     * The name of the cloned sound buffer. Defaults to `StaticSoundBuffer #${id}`.\n     */\n    name: string;\n}\n\n/**\n * Abstract class representing a static sound buffer.\n *\n * A static sound buffer is a fully downloaded and decoded array of audio data that is ready to be played.\n *\n * Static sound buffers can be reused multiple times by different {@link StaticSound} instances.\n *\n * Static sound buffers are created by the {@link CreateSoundBufferAsync} function.\n *\n * @see {@link StaticSound.buffer}\n */\nexport abstract class StaticSoundBuffer {\n    /**\n     * The engine that the sound buffer belongs to.\n     */\n    public readonly engine: AudioEngineV2;\n\n    /**\n     * The name of the sound buffer.\n     */\n    public name: string = `StaticSoundBuffer #${StaticSoundBufferId++}`;\n\n    protected constructor(engine: AudioEngineV2) {\n        this.engine = engine;\n    }\n\n    /**\n     * The sample rate of the sound buffer.\n     */\n    public abstract readonly sampleRate: number;\n\n    /**\n     * The length of the sound buffer, in sample frames.\n     */\n    public abstract readonly length: number;\n\n    /**\n     * The duration of the sound buffer, in seconds.\n     */\n    public abstract readonly duration: number;\n\n    /**\n     * The number of channels in the sound buffer.\n     */\n    public abstract readonly channelCount: number;\n\n    /**\n     * Clones the sound buffer.\n     * @param options Options for cloning the sound buffer.\n     */\n    public abstract clone(options?: Partial<IStaticSoundBufferCloneOptions>): StaticSoundBuffer;\n}\n"], "names": [], "mappings": ";;;AAEA,IAAI,mBAAmB,GAAG,CAAC,CAAC;AAoCtB,MAAgB,iBAAiB;IAWnC,YAAsB,MAAqB,CAAA;QAL3C;;WAEG,CACI,IAAA,CAAA,IAAI,GAAW,sBAA2C,CAAE,CAAC,KAAxB,mBAAmB,EAAE;QAG7D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;CA2BJ", "debugId": null}}, {"offset": {"line": 2044, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/streamingSound.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/streamingSound.ts"], "sourcesContent": ["import { SoundState } from \"../soundState\";\nimport type { IAbstractSoundOptions, IAbstractSoundPlayOptions, IAbstractSoundStoredOptions } from \"./abstractSound\";\nimport { AbstractSound } from \"./abstractSound\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { _StreamingSoundInstance } from \"./streamingSoundInstance\";\n\n/** @internal */\nexport interface IStreamingSoundOptionsBase {\n    /**\n     * The number of instances to preload. Defaults to 1.\n     * */\n    preloadCount: number;\n}\n\n/**\n * Options for creating a streaming sound.\n */\nexport interface IStreamingSoundOptions extends IAbstractSoundOptions, IStreamingSoundOptionsBase {}\n\n/**\n * Options for playing a streaming sound.\n */\nexport interface IStreamingSoundPlayOptions extends IAbstractSoundPlayOptions {}\n\n/**\n * Options stored in a streaming sound.\n * @internal\n */\nexport interface IStreamingSoundStoredOptions extends IAbstractSoundStoredOptions, IStreamingSoundOptionsBase {}\n\n/**\n * Abstract class representing a streaming sound.\n *\n * A streaming sound has a sound buffer that is loaded into memory in chunks as it is played. This allows it to be played\n * more quickly than a static sound, but it also means that it cannot have loop points or playback rate changes.\n *\n * Due to the way streaming sounds are typically implemented, there can be a significant delay when attempting to play\n * a streaming sound for the first time. To prevent this delay, it is recommended to preload instances of the sound\n * using the {@link IStreamingSoundStoredOptions.preloadCount} options, or the {@link preloadInstanceAsync} and\n * {@link preloadInstancesAsync} methods before calling the `play` method.\n *\n * Streaming sounds are created by the {@link CreateStreamingSoundAsync} function.\n */\nexport abstract class StreamingSound extends AbstractSound {\n    private _preloadedInstances = new Array<_StreamingSoundInstance>();\n\n    protected abstract override readonly _options: IStreamingSoundStoredOptions;\n\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine);\n    }\n\n    /**\n     * The number of instances to preload. Defaults to `1`.\n     */\n    public get preloadCount(): number {\n        return this._options.preloadCount ?? 1;\n    }\n\n    /**\n     * Returns the number of instances that have been preloaded.\n     */\n    public get preloadCompletedCount(): number {\n        return this._preloadedInstances.length;\n    }\n\n    /**\n     * Preloads an instance of the sound.\n     * @returns A promise that resolves when the instance is preloaded.\n     */\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\n    public preloadInstanceAsync(): Promise<void> {\n        const instance = this._createInstance();\n\n        this._addPreloadedInstance(instance);\n\n        return instance.preloadedPromise;\n    }\n\n    /**\n     * Preloads the given number of instances of the sound.\n     * @param count - The number of instances to preload.\n     * @returns A promise that resolves when all instances are preloaded.\n     */\n    public async preloadInstancesAsync(count: number): Promise<void> {\n        for (let i = 0; i < count; i++) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.preloadInstanceAsync();\n        }\n\n        await Promise.all(this._preloadedInstances.map(async (instance) => await instance.preloadedPromise));\n    }\n\n    /**\n     * Plays the sound.\n     * - Triggers `onEndedObservable` if played for the full duration and the `loop` option is not set.\n     * @param options The options to use when playing the sound. Options set here override the sound's options.\n     */\n    public play(options: Partial<IStreamingSoundPlayOptions> = {}): void {\n        if (this.state === SoundState.Paused) {\n            this.resume();\n            return;\n        }\n\n        let instance: _StreamingSoundInstance;\n\n        if (this.preloadCompletedCount > 0) {\n            instance = this._preloadedInstances[0];\n            instance.startOffset = this.startOffset;\n            this._removePreloadedInstance(instance);\n        } else {\n            instance = this._createInstance();\n        }\n\n        const onInstanceStateChanged = () => {\n            if (instance.state === SoundState.Started) {\n                this._stopExcessInstances();\n                instance.onStateChangedObservable.removeCallback(onInstanceStateChanged);\n            }\n        };\n        instance.onStateChangedObservable.add(onInstanceStateChanged);\n\n        options.startOffset ??= this.startOffset;\n        options.loop ??= this.loop;\n        options.volume ??= 1;\n\n        this._beforePlay(instance);\n        instance.play(options);\n        this._afterPlay(instance);\n    }\n\n    /**\n     * Stops the sound.\n     */\n    public stop(): void {\n        this._setState(SoundState.Stopped);\n\n        if (!this._instances) {\n            return;\n        }\n\n        for (const instance of Array.from(this._instances)) {\n            instance.stop();\n        }\n    }\n\n    protected abstract override _createInstance(): _StreamingSoundInstance;\n\n    private _addPreloadedInstance(instance: _StreamingSoundInstance): void {\n        if (!this._preloadedInstances.includes(instance)) {\n            this._preloadedInstances.push(instance);\n        }\n    }\n\n    private _removePreloadedInstance(instance: _StreamingSoundInstance): void {\n        const index = this._preloadedInstances.indexOf(instance);\n        if (index !== -1) {\n            this._preloadedInstances.splice(index, 1);\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;;AAyC1C,MAAgB,cAAe,6LAAQ,gBAAa;IAStD;;OAEG,CACH,IAAW,YAAY,GAAA;;QACnB,0CAAW,CAAC,QAAQ,CAAC,YAAY,uDAA1B,8BAA8B,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG,CACH,IAAW,qBAAqB,GAAA;QAC5B,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;IAC3C,CAAC;IAED;;;OAGG,CACH,2FAA2F;IACpF,oBAAoB,GAAA;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAExC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAErC,OAAO,QAAQ,CAAC,gBAAgB,CAAC;IACrC,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,qBAAqB,CAAC,KAAa,EAAA;QAC5C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7B,mEAAmE;YACnE,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAG,CAAD,KAAO,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACzG,CAAC;IAED;;;;OAIG,CACI,IAAI,GAAkD;sBAAjD,iEAA+C,CAAA,CAAE;QACzD,IAAI,IAAI,CAAC,KAAK,KAAA,EAAA,qBAAA,EAAsB,GAAE,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,IAAI,QAAiC,CAAC;QAEtC,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,EAAE,CAAC;YACjC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YACvC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACxC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC,MAAM,CAAC;YACJ,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACtC,CAAC;QAED,MAAM,sBAAsB,GAAG,GAAG,EAAE;YAChC,IAAI,QAAQ,CAAC,KAAK,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;gBACxC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,QAAQ,CAAC,wBAAwB,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC,CAAC;QACF,QAAQ,CAAC,wBAAwB,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;;SAE9D,OAAO,wBAAC,WAAW,uEAAnB,OAAO,CAAC,WAAW,GAAK,IAAI,CAAC,WAAW,EAAC;;iCACjC,IAAI,yCAAZ,OAAO,SAAP,OAAO,CAAC,IAAI,GAAK,IAAI,CAAC,IAAI,EAAC;;mCACnB,MAAM,2CAAd,OAAO,WAAP,OAAO,CAAC,MAAM,GAAK,CAAC,EAAC;QAErB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC3B,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG,CACI,IAAI,GAAA;QACP,IAAI,CAAC,SAAS,CAAA,EAAA,sBAAA,GAAoB,CAAC;QAEnC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAE,CAAC;YACjD,QAAQ,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC;IACL,CAAC;IAIO,qBAAqB,CAAC,QAAiC,EAAA;QAC3D,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAEO,wBAAwB,CAAC,QAAiC,EAAA;QAC9D,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IA/GD,YAAsB,IAAY,EAAE,MAAqB,CAAA;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QALhB,IAAA,CAAA,mBAAmB,GAAG,IAAI,KAAK,EAA2B,CAAC;IAMnE,CAAC;CA8GJ", "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subProperties/abstractSpatialAudio.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subProperties/abstractSpatialAudio.ts"], "sourcesContent": ["import { Quaternion, Vector3 } from \"../../../Maths/math.vector\";\nimport type { Node } from \"../../../node\";\nimport type { Nullable } from \"../../../types\";\nimport type { SpatialAudioAttachmentType } from \"../../spatialAudioAttachmentType\";\n\nexport const _SpatialAudioDefaults = {\n    coneInnerAngle: 6.28318530718 as number,\n    coneOuterAngle: 6.28318530718 as number,\n    coneOuterVolume: 0 as number,\n    distanceModel: \"linear\" as DistanceModelType,\n    maxDistance: 10000 as number,\n    minDistance: 1 as number,\n    panningModel: \"equalpower\" as PanningModelType,\n    position: Vector3.Zero(),\n    rolloffFactor: 1 as number,\n    rotation: Vector3.Zero(),\n    rotationQuaternion: new Quaternion(),\n} as const;\n\n/**\n * Options for spatial audio.\n */\nexport interface ISpatialAudioOptions {\n    /**\n     * Whether to automatically update the spatial properties of the audio node. Defaults to `true`.\n     */\n    spatialAutoUpdate: boolean;\n    /**\n     * The spatial cone inner angle, in radians. Defaults to 2π.\n     * - When the listener is inside the cone inner angle, the volume is at its maximum.\n     */\n    spatialConeInnerAngle: number;\n    /**\n     * The spatial cone outer angle, in radians. Defaults to 2π.\n     * - When the listener is between the the cone inner and outer angles, the volume fades to its minimum as the listener approaches the outer angle.\n     * - When the listener is outside the cone outer angle, the volume is at its minimum.\n     */\n    spatialConeOuterAngle: number;\n    /**\n     * The amount of volume reduction outside the {@link spatialConeOuterAngle}. Defaults to 0.\n     */\n    spatialConeOuterVolume: number;\n    /**\n     * The algorithm to use to reduce the volume of the audio source as it moves away from the listener. Defaults to \"inverse\".\n     *\n     * Possible values are:\n     * - `\"linear\"`: The volume is reduced linearly as the source moves away from the listener.\n     * - `\"inverse\"`: The volume is reduced inversely as the source moves away from the listener.\n     * - `\"exponential\"`: The volume is reduced exponentially as the source moves away from the listener.\n     *\n     * @see {@link spatialMaxDistance}\n     * @see {@link spatialMinDistance}\n     * @see {@link spatialRolloffFactor}\n     */\n    spatialDistanceModel: \"linear\" | \"inverse\" | \"exponential\";\n    /**\n     * Enable spatial audio. Defaults to false.\n     *\n     * When set to `true`, the audio node's spatial properties will be initialized on creation and there will be no\n     * delay when setting the first spatial value.\n     *\n     * When not specified, or set to `false`, the audio node's spatial properties will not be initialized on creation\n     * and there will be a small delay when setting the first spatial value.\n     *\n     * - This option is ignored if any other spatial options are set.\n     */\n    spatialEnabled: boolean;\n    /**\n     * The maximum distance between the audio source and the listener, after which the volume is not reduced any further. Defaults to 10000.\n     * - This value is used only when the {@link spatialDistanceModel} is set to `\"linear\"`.\n     * @see {@link spatialDistanceModel}\n     */\n    spatialMaxDistance: number;\n    /**\n     * The minimum update time in seconds of the spatialization if it is attached to a mesh or transform node. Defaults to `0`.\n     * - The spatialization's position and rotation will not update faster than this time, but they may update slower depending on the frame rate.\n     */\n    spatialMinUpdateTime: number;\n    /**\n     * Possible values are:\n     * - `\"equalpower\"`: Represents the equal-power panning algorithm, generally regarded as simple and efficient.\n     * - `\"HRTF\"`: Renders a stereo output of higher quality than `\"equalpower\"` — it uses a convolution with measured impulse responses from human subjects.\n     */\n    spatialPanningModel: \"equalpower\" | \"HRTF\";\n    /**\n     * The spatial position. Defaults to (0, 0, 0).\n     */\n    spatialPosition: Vector3;\n    /**\n     * The distance for reducing volume as the audio source moves away from the listener – i.e. the distance the volume reduction starts at. Defaults to 1.\n     * - This value is used by all distance models.\n     * @see {@link spatialDistanceModel}\n     */\n    spatialMinDistance: number;\n    /**\n     * How quickly the volume is reduced as the source moves away from the listener. Defaults to 1.\n     * - This value is used by all distance models.\n     * @see {@link spatialDistanceModel}\n     */\n    spatialRolloffFactor: number;\n    /**\n     * The spatial rotation, as Euler angles. Defaults to (0, 0, 0).\n     */\n    spatialRotation: Vector3;\n    /**\n     * The spatial rotation, as a quaternion. Defaults to (0, 0, 0, 1).\n     */\n    spatialRotationQuaternion: Quaternion;\n}\n\n/**\n * @param options The spatial audio options to check.\n * @returns `true` if spatial audio options are defined, otherwise `false`.\n */\nexport function _HasSpatialAudioOptions(options: Partial<ISpatialAudioOptions>): boolean {\n    return (\n        options.spatialEnabled ||\n        options.spatialAutoUpdate !== undefined ||\n        options.spatialConeInnerAngle !== undefined ||\n        options.spatialConeOuterAngle !== undefined ||\n        options.spatialConeOuterVolume !== undefined ||\n        options.spatialDistanceModel !== undefined ||\n        options.spatialMaxDistance !== undefined ||\n        options.spatialMinDistance !== undefined ||\n        options.spatialMinUpdateTime !== undefined ||\n        options.spatialPanningModel !== undefined ||\n        options.spatialPosition !== undefined ||\n        options.spatialRolloffFactor !== undefined ||\n        options.spatialRotation !== undefined ||\n        options.spatialRotationQuaternion !== undefined\n    );\n}\n\n/**\n * Abstract class representing the `spatial` audio property on a sound or audio bus.\n *\n * @see {@link AudioEngineV2.listener}\n */\nexport abstract class AbstractSpatialAudio {\n    /**\n     * The spatial cone inner angle, in radians. Defaults to 2π.\n     * - When the listener is inside the cone inner angle, the volume is at its maximum.\n     */\n    public abstract coneInnerAngle: number;\n\n    /**\n     * The spatial cone outer angle, in radians. Defaults to 2π.\n     * - When the listener is between the the cone inner and outer angles, the volume fades to its minimum as the listener approaches the outer angle.\n     * - When the listener is outside the cone outer angle, the volume is at its minimum.\n     */\n    public abstract coneOuterAngle: number;\n\n    /**\n     * The amount of volume reduction outside the {@link coneOuterAngle}. Defaults to 0.\n     */\n    public abstract coneOuterVolume: number;\n\n    /**\n     * The algorithm to use to reduce the volume of the audio source as it moves away from the listener. Defaults to \"inverse\".\n     *\n     * Possible values are:\n     * - `\"linear\"`: The volume is reduced linearly as the source moves away from the listener.\n     * - `\"inverse\"`: The volume is reduced inversely as the source moves away from the listener.\n     * - `\"exponential\"`: The volume is reduced exponentially as the source moves away from the listener.\n     *\n     * @see {@link spatialMaxDistance}\n     * @see {@link spatialMinDistance}\n     * @see {@link spatialRolloffFactor}\n     */\n    public abstract distanceModel: \"linear\" | \"inverse\" | \"exponential\";\n\n    /**\n     * Whether the audio source is attached to a mesh or transform node.\n     */\n    public abstract isAttached: boolean;\n\n    /**\n     * The maximum distance between the audio source and the listener, after which the volume is not reduced any further. Defaults to 10000.\n     * - This value is used only when the {@link distanceModel} is set to `\"linear\"`.\n     * @see {@link distanceModel}\n     */\n    public abstract maxDistance: number;\n\n    /**\n     * The distance for reducing volume as the audio source moves away from the listener – i.e. the distance the volume reduction starts at. Defaults to 1.\n     * - This value is used by all distance models.\n     * @see {@link distanceModel}\n     */\n    public abstract minDistance: number;\n\n    /**\n     * The minimum update time in seconds of the spatialization if it is attached to a mesh or transform node. Defaults to `0`.\n     * - The spatialization's position and rotation will not update faster than this time, but they may update slower depending on the frame rate.\n     */\n    public abstract minUpdateTime: number;\n\n    /**\n     * The spatial panning model. Defaults to \"equalpower\".\n     *\n     * Possible values are:\n     * - `\"equalpower\"`: Represents the equal-power panning algorithm, generally regarded as simple and efficient.\n     * - `\"HRTF\"`:Renders a stereo output of higher quality than `\"equalpower\"` — it uses a convolution with measured impulse responses from human subjects.\n     */\n    public abstract panningModel: \"equalpower\" | \"HRTF\";\n\n    /**\n     * The spatial position. Defaults to (0, 0, 0).\n     */\n    public abstract position: Vector3;\n\n    /**\n     * How quickly the volume is reduced as the source moves away from the listener. Defaults to 1.\n     * - This value is used by all distance models.\n     * @see {@link distanceModel}\n     */\n    public abstract rolloffFactor: number;\n\n    /**\n     * The spatial rotation. Defaults to (0, 0, 0).\n     */\n    public abstract rotation: Vector3;\n\n    /**\n     * The spatial rotation quaternion. Defaults to (0, 0, 0, 1).\n     */\n    public abstract rotationQuaternion: Quaternion;\n\n    /**\n     * Attaches to a scene node.\n     *\n     * Detaches automatically before attaching to the given scene node.\n     * If `sceneNode` is `null` it is the same as calling `detach()`.\n     *\n     * @param sceneNode The scene node to attach to, or `null` to detach.\n     * @param useBoundingBox Whether to use the bounding box of the node for positioning. Defaults to `false`.\n     * @param attachmentType Whether to attach to the node's position and/or rotation. Defaults to `PositionAndRotation`.\n     */\n    public abstract attach(sceneNode: Nullable<Node>, useBoundingBox?: boolean, attachmentType?: SpatialAudioAttachmentType): void;\n\n    /**\n     * Detaches from the scene node if attached.\n     */\n    public abstract detach(): void;\n\n    /**\n     * Updates the position and rotation of the associated audio engine object in the audio rendering graph.\n     *\n     * This is called automatically by default and only needs to be called manually if automatic updates are disabled.\n     */\n    public abstract update(): void;\n}\n"], "names": [], "mappings": ";;;;;AAAA,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;;AAK1D,MAAM,qBAAqB,GAAG;IACjC,cAAc,EAAE,aAAuB;IACvC,cAAc,EAAE,aAAuB;IACvC,eAAe,EAAE,CAAW;IAC5B,aAAa,EAAE,QAA6B;IAC5C,WAAW,EAAE,KAAe;IAC5B,WAAW,EAAE,CAAW;IACxB,YAAY,EAAE,YAAgC;IAC9C,QAAQ,oKAAE,UAAO,CAAC,IAAI,EAAE;IACxB,aAAa,EAAE,CAAW;IAC1B,QAAQ,oKAAE,UAAO,CAAC,IAAI,EAAE;IACxB,kBAAkB,EAAE,sKAAI,aAAU,EAAE;CAC9B,CAAC;AAiGL,SAAU,uBAAuB,CAAC,OAAsC;IAC1E,OAAO,AACH,OAAO,CAAC,cAAc,IACtB,OAAO,CAAC,iBAAiB,KAAK,SAAS,IACvC,OAAO,CAAC,qBAAqB,KAAK,SAAS,IAC3C,OAAO,CAAC,qBAAqB,KAAK,SAAS,IAC3C,OAAO,CAAC,sBAAsB,KAAK,SAAS,IAC5C,OAAO,CAAC,oBAAoB,KAAK,SAAS,IAC1C,OAAO,CAAC,kBAAkB,KAAK,SAAS,IACxC,OAAO,CAAC,kBAAkB,KAAK,SAAS,IACxC,OAAO,CAAC,oBAAoB,KAAK,SAAS,IAC1C,OAAO,CAAC,mBAAmB,KAAK,SAAS,IACzC,OAAO,CAAC,eAAe,KAAK,SAAS,IACrC,OAAO,CAAC,oBAAoB,KAAK,SAAS,IAC1C,OAAO,CAAC,eAAe,KAAK,SAAS,IACrC,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAClD,CAAC;AACN,CAAC;AAOK,MAAgB,oBAAoB;CAgHzC", "debugId": null}}, {"offset": {"line": 2176, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subProperties/abstractStereoAudio.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subProperties/abstractStereoAudio.ts"], "sourcesContent": ["export const _StereoAudioDefaults = {\n    pan: 0 as number,\n} as const;\n\n/** */\nexport interface IStereoAudioOptions {\n    /**\n     * Enable stereo. Defaults to false.\n     *\n     * When set to `true`, the audio node's stereo properties will be initialized on creation and there will be no\n     * delay when setting the first stereo value.\n     *\n     * When not specified, or set to `false`, the audio node's stereo properties will not be initialized on creation\n     * and there will be a small delay when setting the first stereo value.\n     *\n     * - This option is ignored if any other stereo options are set.\n     */\n    stereoEnabled: boolean;\n    /**\n     * The stereo pan from -1 (left) to 1 (right). Defaults to 0.\n     */\n    stereoPan: number;\n}\n\n/**\n * @param options The stereo audio options to check.\n * @returns `true` if stereo audio options are defined, otherwise `false`.\n */\nexport function _HasStereoAudioOptions(options: Partial<IStereoAudioOptions>): boolean {\n    return options.stereoEnabled || options.stereoPan !== undefined;\n}\n\n/**\n * Abstract class representing the `stereo` audio property on a sound or audio bus.\n *\n * @see {@link AudioEngineV2.listener}\n */\nexport abstract class AbstractStereoAudio {\n    /**\n     * The stereo pan from -1 (left) to 1 (right). Defaults to 0.\n     */\n    public abstract pan: number;\n}\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,oBAAoB,GAAG;IAChC,GAAG,EAAE,CAAW;CACV,CAAC;AA0BL,SAAU,sBAAsB,CAAC,OAAqC;IACxE,OAAO,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;AACpE,CAAC;AAOK,MAAgB,mBAAmB;CAKxC", "debugId": null}}, {"offset": {"line": 2193, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subProperties/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subProperties/index.ts"], "sourcesContent": ["export * from \"./abstractAudioAnalyzer\";\nexport * from \"./abstractSpatialAudio\";\nexport * from \"./abstractSpatialAudioListener\";\nexport * from \"./abstractStereoAudio\";\n"], "names": [], "mappings": ";AAAA,cAAc,yBAAyB,CAAC;AACxC,cAAc,wBAAwB,CAAC;AACvC,cAAc,gCAAgC,CAAC;AAC/C,cAAc,uBAAuB,CAAC", "debugId": null}}, {"offset": {"line": 2216, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-restricted-imports */\nexport * from \"./abstractAudioBus\";\nexport * from \"./abstractAudioNode\";\nexport * from \"./abstractAudioOutNode\";\nexport * from \"./abstractSound\";\nexport * from \"./audioBus\";\nexport * from \"./audioEngineV2\";\nexport * from \"./mainAudioBus\";\nexport * from \"./staticSound\";\nexport * from \"./staticSoundBuffer\";\nexport * from \"./streamingSound\";\nexport * from \"./subProperties/index\";\n"], "names": [], "mappings": "AAAA,2DAAA,EAA6D;AAC7D,cAAc,oBAAoB,CAAC;AACnC,cAAc,qBAAqB,CAAC;AACpC,cAAc,wBAAwB,CAAC;AACvC,cAAc,iBAAiB,CAAC;AAChC,cAAc,YAAY,CAAC;AAC3B,cAAc,iBAAiB,CAAC;AAChC,cAAc,gBAAgB,CAAC;AAC/B,cAAc,eAAe,CAAC;AAC9B,cAAc,qBAAqB,CAAC;AACpC,cAAc,kBAAkB,CAAC;AACjC,cAAc,uBAAuB,CAAC", "debugId": null}}, {"offset": {"line": 2260, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/audioParameter.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/audioParameter.ts"], "sourcesContent": ["/**\n * The shape of the audio ramp used to set an audio parameter's value, such as a sound's volume.\n */\nexport const enum AudioParameterRampShape {\n    /**\n     * The ramp is linear.\n     */\n    Linear = \"linear\",\n    /**\n     * The ramp is exponential.\n     */\n    Exponential = \"exponential\",\n    /**\n     * The ramp is logarithmic.\n     */\n    Logarithmic = \"logarithmic\",\n}\n\n/**\n * Options for ramping an audio parameter's value.\n */\nexport interface IAudioParameterRampOptions {\n    /**\n     * The ramp time, in seconds. Must be greater than 0. Defaults to 0.01 seconds.\n     * The audio parameter's value will reach the target value at the end of the duration.\n     */\n    duration: number;\n    /**\n     * The shape of the ramp to use for the parameter change. Defaults to {@link AudioParameterRampShape.Linear}.\n     */\n    shape: AudioParameterRampShape;\n}\n"], "names": [], "mappings": "AAAA;;GAEG;;;AACH,IAAkB,uBAajB;AAbD,CAAA,SAAkB,uBAAuB;IACrC;;OAEG,CACH,uBAAA,CAAA,SAAA,GAAA,QAAiB,CAAA;IACjB;;OAEG,CACH,uBAAA,CAAA,cAAA,GAAA,aAA2B,CAAA;IAC3B;;OAEG,CACH,uBAAA,CAAA,cAAA,GAAA,aAA2B,CAAA;AAC/B,CAAC,EAbiB,uBAAuB,IAAA,CAAvB,uBAAuB,GAAA,CAAA,CAAA,GAaxC", "debugId": null}}, {"offset": {"line": 2281, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/soundState.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/soundState.ts"], "sourcesContent": ["/**\n * The state of a sound.\n */\nexport const enum SoundState {\n    /**\n     * The sound is waiting for its instances to stop.\n     */\n    Stopping,\n    /**\n     * The sound is stopped.\n     */\n    Stopped,\n    /**\n     * The sound is waiting for its instances to start.\n     */\n    Starting,\n    /**\n     * The sound has started playing.\n     */\n    Started,\n    /**\n     * The sound failed to start, most likely due to the user not interacting with the page, yet.\n     */\n    FailedToStart,\n    /**\n     * The sound is paused.\n     */\n    Paused,\n}\n"], "names": [], "mappings": "AAAA;;GAEG;;;AACH,IAAkB,UAyBjB;AAzBD,CAAA,SAAkB,UAAU;IACxB;;OAEG,CACH,UAAA,CAAA,UAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAQ,CAAA;IACR;;OAEG,CACH,UAAA,CAAA,UAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAO,CAAA;IACP;;OAEG,CACH,UAAA,CAAA,UAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAQ,CAAA;IACR;;OAEG,CACH,UAAA,CAAA,UAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAO,CAAA;IACP;;OAEG,CACH,UAAA,CAAA,UAAA,CAAA,gBAAA,GAAA,EAAA,GAAA,eAAa,CAAA;IACb;;OAEG,CACH,UAAA,CAAA,UAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAM,CAAA;AACV,CAAC,EAzBiB,UAAU,IAAA,CAAV,UAAU,GAAA,CAAA,CAAA,GAyB3B", "debugId": null}}, {"offset": {"line": 2311, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/spatialAudioAttachmentType.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/spatialAudioAttachmentType.ts"], "sourcesContent": ["export const enum SpatialAudioAttachmentType {\n    Position = 1,\n    Rotation = 2,\n    PositionAndRotation = 3,\n}\n"], "names": [], "mappings": ";;;AAAA,IAAkB,0BAIjB;AAJD,CAAA,SAAkB,0BAA0B;IACxC,0BAAA,CAAA,0BAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IACZ,0BAAA,CAAA,0BAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IACZ,0BAAA,CAAA,0BAAA,CAAA,sBAAA,GAAA,EAAA,GAAA,qBAAuB,CAAA;AAC3B,CAAC,EAJiB,0BAA0B,IAAA,CAA1B,0BAA0B,GAAA,CAAA,CAAA,GAI3C", "debugId": null}}, {"offset": {"line": 2324, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subNodes/stereoAudioSubNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subNodes/stereoAudioSubNode.ts"], "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { AudioEngineV2 } from \"../../abstractAudio/audioEngineV2\";\nimport { _AbstractAudioSubNode } from \"../../abstractAudio/subNodes/abstractAudioSubNode\";\nimport { AudioSubNode } from \"../../abstractAudio/subNodes/audioSubNode\";\nimport type { IStereoAudioOptions } from \"../../abstractAudio/subProperties/abstractStereoAudio\";\nimport { _StereoAudioDefaults } from \"../../abstractAudio/subProperties/abstractStereoAudio\";\nimport type { _AbstractAudioSubGraph } from \"./abstractAudioSubGraph\";\n\n/** @internal */\nexport abstract class _StereoAudioSubNode extends _AbstractAudioSubNode {\n    protected constructor(engine: AudioEngineV2) {\n        super(AudioSubNode.STEREO, engine);\n    }\n\n    public abstract pan: number;\n\n    /** @internal */\n    public setOptions(options: Partial<IStereoAudioOptions>): void {\n        this.pan = options.stereoPan ?? _StereoAudioDefaults.pan;\n    }\n}\n\n/** @internal */\nexport function _GetStereoAudioSubNode(subGraph: _AbstractAudioSubGraph): Nullable<_StereoAudioSubNode> {\n    return subGraph.getSubNode<_StereoAudioSubNode>(AudioSubNode.STEREO);\n}\n\n/** @internal */\nexport function _SetStereoAudioProperty<K extends keyof typeof _StereoAudioDefaults>(subGraph: _AbstractAudioSubGraph, property: K, value: _StereoAudioSubNode[K]): void {\n    subGraph.callOnSubNode<_StereoAudioSubNode>(AudioSubNode.STEREO, (node) => {\n        node[property] = value;\n    });\n}\n"], "names": [], "mappings": ";;;;;AAEA,OAAO,EAAE,qBAAqB,EAAE,MAAM,mDAAmD,CAAC;AAG1F,OAAO,EAAE,oBAAoB,EAAE,MAAM,uDAAuD,CAAC;;;AAIvF,MAAgB,mBAAoB,gNAAQ,wBAAqB;IAOnE,cAAA,EAAgB,CACT,UAAU,CAAC,OAAqC,EAAA;;QACnD,IAAI,CAAC,GAAG,iCAAW,SAAS,8CAAjB,OAAO,yNAAc,uBAAoB,CAAC,GAAG,CAAC;IAC7D,CAAC;IATD,YAAsB,MAAqB,CAAA;QACvC,KAAK,CAAA,SAAA,uBAAA,KAAsB,MAAM,CAAC,CAAC;IACvC,CAAC;CAQJ;AAGK,SAAU,sBAAsB,CAAC,QAAgC;IACnE,OAAO,QAAQ,CAAC,UAAU,CAAA,SAAA,uBAAA,GAA0C,CAAC;AACzE,CAAC;AAGK,SAAU,uBAAuB,CAA8C,QAAgC,EAAE,QAAW,EAAE,KAA6B;IAC7J,QAAQ,CAAC,aAAa,CAAA,SAAA,uBAAA,KAA2C,CAAC,IAAI,EAAE,EAAE;QACtE,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;IAC3B,CAAC,CAAC,CAAC;AACP,CAAC", "debugId": null}}, {"offset": {"line": 2354, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subProperties/stereoAudio.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subProperties/stereoAudio.ts"], "sourcesContent": ["import { _StereoAudioDefaults, AbstractStereoAudio } from \"../../abstractAudio/subProperties/abstractStereoAudio\";\nimport type { _AbstractAudioSubGraph } from \"../subNodes/abstractAudioSubGraph\";\nimport { _SetStereoAudioProperty } from \"../subNodes/stereoAudioSubNode\";\n\n/** @internal */\nexport class _StereoAudio extends AbstractStereoAudio {\n    private _pan: number = _StereoAudioDefaults.pan;\n    private _subGraph: _AbstractAudioSubGraph;\n\n    /** @internal */\n    public constructor(subGraph: _AbstractAudioSubGraph) {\n        super();\n        this._subGraph = subGraph;\n    }\n\n    /** @internal */\n    public get pan(): number {\n        return this._pan;\n    }\n\n    public set pan(value: number) {\n        this._pan = value;\n        _SetStereoAudioProperty(this._subGraph, \"pan\", value);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,MAAM,uDAAuD,CAAC;AAElH,OAAO,EAAE,uBAAuB,EAAE,MAAM,gCAAgC,CAAC;;;AAGnE,MAAO,YAAa,oNAAQ,sBAAmB;IAUjD,cAAA,EAAgB,CAChB,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,IAAW,GAAG,CAAC,KAAa,EAAA;QACxB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;iNAClB,0BAAA,AAAuB,EAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IAdD,cAAA,EAAgB,CAChB,YAAmB,QAAgC,CAAA;QAC/C,KAAK,EAAE,CAAC;QALJ,IAAA,CAAA,IAAI,8MAAW,uBAAoB,CAAC,GAAG,CAAC;QAM5C,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;CAWJ", "debugId": null}}, {"offset": {"line": 2379, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subNodes/spatialAudioSubNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subNodes/spatialAudioSubNode.ts"], "sourcesContent": ["import type { Quaternion, Vector3 } from \"../../../Maths/math.vector\";\nimport type { Node } from \"../../../node\";\nimport type { Nullable } from \"../../../types\";\nimport type { SpatialAudioAttachmentType } from \"../../spatialAudioAttachmentType\";\nimport type { AudioEngineV2 } from \"../audioEngineV2\";\nimport { _SpatialAudioAttacherComponent } from \"../components/spatialAudioAttacherComponent\";\nimport type { ISpatialAudioOptions } from \"../subProperties/abstractSpatialAudio\";\nimport { _SpatialAudioDefaults } from \"../subProperties/abstractSpatialAudio\";\nimport type { _AbstractAudioSubGraph } from \"./abstractAudioSubGraph\";\nimport { _AbstractAudioSubNode } from \"./abstractAudioSubNode\";\nimport { AudioSubNode } from \"./audioSubNode\";\n\n/** @internal */\nexport abstract class _SpatialAudioSubNode extends _AbstractAudioSubNode {\n    private _attacherComponent: Nullable<_SpatialAudioAttacherComponent> = null;\n\n    protected constructor(engine: AudioEngineV2) {\n        super(AudioSubNode.SPATIAL, engine);\n    }\n\n    public abstract coneInnerAngle: number;\n    public abstract coneOuterAngle: number;\n    public abstract coneOuterVolume: number;\n    public abstract distanceModel: DistanceModelType;\n    public abstract maxDistance: number;\n    public abstract minDistance: number;\n    public abstract panningModel: PanningModelType;\n    public abstract position: Vector3;\n    public abstract rolloffFactor: number;\n    public abstract rotation: Vector3;\n    public abstract rotationQuaternion: Quaternion;\n    public abstract _inNode: AudioNode;\n\n    /** @internal */\n    public get isAttached(): boolean {\n        return this._attacherComponent !== null && this._attacherComponent.isAttached;\n    }\n\n    /** @internal */\n    public attach(sceneNode: Nullable<Node>, useBoundingBox: boolean, attachmentType: SpatialAudioAttachmentType): void {\n        this.detach();\n\n        if (!this._attacherComponent) {\n            this._attacherComponent = new _SpatialAudioAttacherComponent(this);\n        }\n\n        this._attacherComponent.attach(sceneNode, useBoundingBox, attachmentType);\n    }\n\n    /** @internal */\n    public detach(): void {\n        this._attacherComponent?.detach();\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._attacherComponent?.dispose();\n        this._attacherComponent = null;\n    }\n\n    /** @internal */\n    public setOptions(options: Partial<ISpatialAudioOptions>): void {\n        this.coneInnerAngle = options.spatialConeInnerAngle ?? _SpatialAudioDefaults.coneInnerAngle;\n        this.coneOuterAngle = options.spatialConeOuterAngle ?? _SpatialAudioDefaults.coneOuterAngle;\n        this.coneOuterVolume = options.spatialConeOuterVolume ?? _SpatialAudioDefaults.coneOuterVolume;\n        this.distanceModel = options.spatialDistanceModel ?? _SpatialAudioDefaults.distanceModel;\n        this.maxDistance = options.spatialMaxDistance ?? _SpatialAudioDefaults.maxDistance;\n        this.minDistance = options.spatialMinDistance ?? _SpatialAudioDefaults.minDistance;\n        this.panningModel = options.spatialPanningModel ?? _SpatialAudioDefaults.panningModel;\n        this.rolloffFactor = options.spatialRolloffFactor ?? _SpatialAudioDefaults.rolloffFactor;\n\n        if (options.spatialPosition) {\n            this.position = options.spatialPosition.clone();\n        }\n\n        if (options.spatialRotationQuaternion) {\n            this.rotationQuaternion = options.spatialRotationQuaternion.clone();\n        } else if (options.spatialRotation) {\n            this.rotation = options.spatialRotation.clone();\n        } else {\n            this.rotationQuaternion = _SpatialAudioDefaults.rotationQuaternion.clone();\n        }\n\n        this.update();\n    }\n\n    /** @internal */\n    public update(): void {\n        if (this.isAttached) {\n            this._attacherComponent?.update();\n        } else {\n            this._updatePosition();\n            this._updateRotation();\n        }\n    }\n\n    public abstract _updatePosition(): void;\n    public abstract _updateRotation(): void;\n}\n\n/** @internal */\nexport function _GetSpatialAudioSubNode(subGraph: _AbstractAudioSubGraph): Nullable<_SpatialAudioSubNode> {\n    return subGraph.getSubNode<_SpatialAudioSubNode>(AudioSubNode.SPATIAL);\n}\n\n/** @internal */\nexport function _SetSpatialAudioProperty<K extends keyof typeof _SpatialAudioDefaults>(subGraph: _AbstractAudioSubGraph, property: K, value: _SpatialAudioSubNode[K]): void {\n    subGraph.callOnSubNode<_SpatialAudioSubNode>(AudioSubNode.SPATIAL, (node) => {\n        node[property] = value;\n    });\n}\n"], "names": [], "mappings": ";;;;;AAKA,OAAO,EAAE,8BAA8B,EAAE,MAAM,6CAA6C,CAAC;AAE7F,OAAO,EAAE,qBAAqB,EAAE,MAAM,uCAAuC,CAAC;AAE9E,OAAO,EAAE,qBAAqB,EAAE,MAAM,wBAAwB,CAAC;;;;AAIzD,MAAgB,oBAAqB,gNAAQ,wBAAqB;IAoBpE,cAAA,EAAgB,CAChB,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,kBAAkB,KAAK,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;IAClF,CAAC;IAED,cAAA,EAAgB,CACT,MAAM,CAAC,SAAyB,EAAE,cAAuB,EAAE,cAA0C,EAAA;QACxG,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,GAAG,sNAAI,iCAA8B,CAAC,IAAI,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAC9E,CAAC;IAED,cAAA,EAAgB,CACT,MAAM,GAAA;YACT;wCAAI,CAAC,kBAAkB,sFAAE,MAAM,EAAE,CAAC;IACtC,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;wCAEZ,CAAC,kBAAkB,cAAvB,wEAAyB,OAAO,EAAE,CAAC;QACnC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CACT,UAAU,CAAC,OAAsC,EAAA;;QACpD,IAAI,CAAC,cAAc,GAAG,OAAO,mCAAC,qBAAqB,uSAAI,wBAAqB,CAAC,cAAc,CAAC;;QAC5F,IAAI,CAAC,cAAc,6CAAW,qBAAqB,0DAA7B,OAAO,sOAA0B,wBAAqB,CAAC,cAAc,CAAC;;QAC5F,IAAI,CAAC,eAAe,8CAAW,sBAAsB,2DAA9B,OAAO,uOAA2B,wBAAqB,CAAC,eAAe,CAAC;;QAC/F,IAAI,CAAC,aAAa,GAAG,OAAO,kCAAC,oBAAoB,qSAAI,wBAAqB,CAAC,aAAa,CAAC;YACtE,OAAO;QAA1B,IAAI,CAAC,WAAW,0CAAW,kBAAkB,qFAAI,oOAAqB,CAAC,WAAW,CAAC;;QACnF,IAAI,CAAC,WAAW,IAAG,OAAO,+BAAC,kBAAkB,iSAAI,wBAAqB,CAAC,WAAW,CAAC;;QACnF,IAAI,CAAC,YAAY,2CAAW,mBAAmB,wDAA3B,OAAO,oOAAwB,wBAAqB,CAAC,YAAY,CAAC;YACjE,OAAO;QAA5B,IAAI,CAAC,aAAa,4CAAW,oBAAoB,qSAAI,wBAAqB,CAAC,aAAa,CAAC;QAEzF,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,yBAAyB,EAAE,CAAC;YACpC,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACxE,CAAC,MAAM,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACpD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,+MAAG,wBAAqB,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;IAClB,CAAC;IAED,cAAA,EAAgB,CACT,MAAM,GAAA;QACT,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;;4CACd,CAAC,kBAAkB,6DAAvB,yBAAyB,MAAM,EAAE,CAAC;QACtC,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC;IACL,CAAC;IAhFD,YAAsB,MAAqB,CAAA;QACvC,KAAK,CAAA,UAAA,wBAAA,KAAuB,MAAM,CAAC,CAAC;QAHhC,IAAA,CAAA,kBAAkB,GAA6C,IAAI,CAAC;IAI5E,CAAC;CAkFJ;AAGK,SAAU,uBAAuB,CAAC,QAAgC;IACpE,OAAO,QAAQ,CAAC,UAAU,CAAA,UAAA,wBAAA,GAA4C,CAAC;AAC3E,CAAC;AAGK,SAAU,wBAAwB,CAA+C,QAAgC,EAAE,QAAW,EAAE,KAA8B;IAChK,QAAQ,CAAC,aAAa,CAAA,UAAA,wBAAA,KAA6C,CAAC,IAAI,EAAE,EAAE;QACxE,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;IAC3B,CAAC,CAAC,CAAC;AACP,CAAC", "debugId": null}}, {"offset": {"line": 2466, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/subNodes/spatialWebAudioSubNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/subNodes/spatialWebAudioSubNode.ts"], "sourcesContent": ["import { Matrix, Quaternion, Vector3 } from \"../../../Maths/math.vector\";\r\nimport { _SpatialAudioSubNode } from \"../../abstractAudio/subNodes/spatialAudioSubNode\";\r\nimport { _SpatialAudioDefaults } from \"../../abstractAudio/subProperties/abstractSpatialAudio\";\r\nimport { _WebAudioParameterComponent } from \"../components/webAudioParameterComponent\";\r\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\r\nimport type { IWebAudioInNode } from \"../webAudioNode\";\r\n\r\nconst TmpMatrix = Matrix.Zero();\r\nconst TmpQuaternion = new Quaternion();\r\nconst TmpVector = Vector3.Zero();\r\n\r\nfunction D2r(degrees: number): number {\r\n    return (degrees * Math.PI) / 180;\r\n}\r\n\r\nfunction R2d(radians: number): number {\r\n    return (radians * 180) / Math.PI;\r\n}\r\n\r\n/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/require-await\r\nexport async function _CreateSpatialAudioSubNodeAsync(engine: _WebAudioEngine): Promise<_SpatialAudioSubNode> {\r\n    return new _SpatialWebAudioSubNode(engine);\r\n}\r\n\r\n/** @internal */\r\nexport class _SpatialWebAudioSubNode extends _SpatialAudioSubNode {\r\n    private _lastPosition: Vector3 = Vector3.Zero();\r\n    private _lastRotation: Vector3 = Vector3.Zero();\r\n    private _lastRotationQuaternion: Quaternion = new Quaternion();\r\n    private _orientationX: _WebAudioParameterComponent;\r\n    private _orientationY: _WebAudioParameterComponent;\r\n    private _orientationZ: _WebAudioParameterComponent;\r\n    private _positionX: _WebAudioParameterComponent;\r\n    private _positionY: _WebAudioParameterComponent;\r\n    private _positionZ: _WebAudioParameterComponent;\r\n\r\n    /** @internal */\r\n    public override readonly engine: _WebAudioEngine;\r\n\r\n    /** @internal */\r\n    public readonly position = _SpatialAudioDefaults.position.clone();\r\n    /** @internal */\r\n    public readonly rotation: Vector3 = _SpatialAudioDefaults.rotation.clone();\r\n    /** @internal */\r\n    public readonly rotationQuaternion: Quaternion = _SpatialAudioDefaults.rotationQuaternion.clone();\r\n\r\n    /** @internal */\r\n    public readonly node: PannerNode;\r\n\r\n    /** @internal */\r\n    public constructor(engine: _WebAudioEngine) {\r\n        super(engine);\r\n\r\n        this.node = new PannerNode(engine._audioContext);\r\n\r\n        this._orientationX = new _WebAudioParameterComponent(engine, this.node.orientationX);\r\n        this._orientationY = new _WebAudioParameterComponent(engine, this.node.orientationY);\r\n        this._orientationZ = new _WebAudioParameterComponent(engine, this.node.orientationZ);\r\n\r\n        this._positionX = new _WebAudioParameterComponent(engine, this.node.positionX);\r\n        this._positionY = new _WebAudioParameterComponent(engine, this.node.positionY);\r\n        this._positionZ = new _WebAudioParameterComponent(engine, this.node.positionZ);\r\n    }\r\n\r\n    /** @internal */\r\n    public override dispose(): void {\r\n        super.dispose();\r\n\r\n        this._orientationX.dispose();\r\n        this._orientationY.dispose();\r\n        this._orientationZ.dispose();\r\n        this._positionX.dispose();\r\n        this._positionY.dispose();\r\n        this._positionZ.dispose();\r\n\r\n        this.node.disconnect();\r\n    }\r\n\r\n    /** @internal */\r\n    public get coneInnerAngle(): number {\r\n        return D2r(this.node.coneInnerAngle);\r\n    }\r\n\r\n    public set coneInnerAngle(value: number) {\r\n        this.node.coneInnerAngle = R2d(value);\r\n    }\r\n\r\n    /** @internal */\r\n    public get coneOuterAngle(): number {\r\n        return D2r(this.node.coneOuterAngle);\r\n    }\r\n\r\n    public set coneOuterAngle(value: number) {\r\n        this.node.coneOuterAngle = R2d(value);\r\n    }\r\n\r\n    /** @internal */\r\n    public get coneOuterVolume(): number {\r\n        return this.node.coneOuterGain;\r\n    }\r\n\r\n    public set coneOuterVolume(value: number) {\r\n        this.node.coneOuterGain = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get distanceModel(): \"linear\" | \"inverse\" | \"exponential\" {\r\n        return this.node.distanceModel;\r\n    }\r\n\r\n    public set distanceModel(value: \"linear\" | \"inverse\" | \"exponential\") {\r\n        this.node.distanceModel = value;\r\n\r\n        // Wiggle the max distance to make the change take effect.\r\n        const maxDistance = this.node.maxDistance;\r\n        this.node.maxDistance = maxDistance + 0.001;\r\n        this.node.maxDistance = maxDistance;\r\n    }\r\n\r\n    /** @internal */\r\n    public get minDistance(): number {\r\n        return this.node.refDistance;\r\n    }\r\n\r\n    public set minDistance(value: number) {\r\n        this.node.refDistance = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get maxDistance(): number {\r\n        return this.node.maxDistance;\r\n    }\r\n\r\n    public set maxDistance(value: number) {\r\n        this.node.maxDistance = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get panningModel(): \"equalpower\" | \"HRTF\" {\r\n        return this.node.panningModel;\r\n    }\r\n\r\n    public set panningModel(value: \"equalpower\" | \"HRTF\") {\r\n        this.node.panningModel = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get rolloffFactor(): number {\r\n        return this.node.rolloffFactor;\r\n    }\r\n\r\n    public set rolloffFactor(value: number) {\r\n        this.node.rolloffFactor = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _inNode(): AudioNode {\r\n        return this.node;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _outNode(): AudioNode {\r\n        return this.node;\r\n    }\r\n\r\n    /** @internal */\r\n    public _updatePosition(): void {\r\n        if (this._lastPosition.equalsWithEpsilon(this.position)) {\r\n            return;\r\n        }\r\n\r\n        // If attached and there is a ramp in progress, we assume another update is coming soon that we can wait for.\r\n        // We don't do this for unattached nodes because there may not be another update coming.\r\n        if (this.isAttached && (this._positionX.isRamping || this._positionY.isRamping || this._positionZ.isRamping)) {\r\n            return;\r\n        }\r\n\r\n        this._positionX.targetValue = this.position.x;\r\n        this._positionY.targetValue = this.position.y;\r\n        this._positionZ.targetValue = this.position.z;\r\n\r\n        this._lastPosition.copyFrom(this.position);\r\n    }\r\n\r\n    /** @internal */\r\n    public _updateRotation(): void {\r\n        // If attached and there is a ramp in progress, we assume another update is coming soon that we can wait for.\r\n        // We don't do this for unattached nodes because there may not be another update coming.\r\n        if (this.isAttached && (this._orientationX.isRamping || this._orientationY.isRamping || this._orientationZ.isRamping)) {\r\n            return;\r\n        }\r\n\r\n        if (!this._lastRotationQuaternion.equalsWithEpsilon(this.rotationQuaternion)) {\r\n            TmpQuaternion.copyFrom(this.rotationQuaternion);\r\n            this._lastRotationQuaternion.copyFrom(this.rotationQuaternion);\r\n        } else if (!this._lastRotation.equalsWithEpsilon(this.rotation)) {\r\n            Quaternion.FromEulerAnglesToRef(this.rotation.x, this.rotation.y, this.rotation.z, TmpQuaternion);\r\n            this._lastRotation.copyFrom(this.rotation);\r\n        } else {\r\n            return;\r\n        }\r\n\r\n        Matrix.FromQuaternionToRef(TmpQuaternion, TmpMatrix);\r\n        Vector3.TransformNormalToRef(Vector3.RightReadOnly, TmpMatrix, TmpVector);\r\n\r\n        this._orientationX.targetValue = TmpVector.x;\r\n        this._orientationY.targetValue = TmpVector.y;\r\n        this._orientationZ.targetValue = TmpVector.z;\r\n    }\r\n\r\n    protected override _connect(node: IWebAudioInNode): boolean {\r\n        const connected = super._connect(node);\r\n\r\n        if (!connected) {\r\n            return false;\r\n        }\r\n\r\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\r\n        if (node._inNode) {\r\n            this.node.connect(node._inNode);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    protected override _disconnect(node: IWebAudioInNode): boolean {\r\n        const disconnected = super._disconnect(node);\r\n\r\n        if (!disconnected) {\r\n            return false;\r\n        }\r\n\r\n        if (node._inNode) {\r\n            this.node.disconnect(node._inNode);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /** @internal */\r\n    public getClassName(): string {\r\n        return \"_SpatialWebAudioSubNode\";\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AACzE,OAAO,EAAE,oBAAoB,EAAE,MAAM,kDAAkD,CAAC;AACxF,OAAO,EAAE,qBAAqB,EAAE,MAAM,wDAAwD,CAAC;AAC/F,OAAO,EAAE,2BAA2B,EAAE,MAAM,0CAA0C,CAAC;;;;;AAIvF,MAAM,SAAS,qKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;AAChC,MAAM,aAAa,GAAG,sKAAI,aAAU,EAAE,CAAC;AACvC,MAAM,SAAS,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;AAEjC,SAAS,GAAG,CAAC,OAAe;IACxB,OAAQ,AAAD,OAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,EAAG,GAAG,CAAC;AACrC,CAAC;AAED,SAAS,GAAG,CAAC,OAAe;IACxB,OAAQ,AAAD,OAAQ,GAAG,GAAG,CAAC,EAAG,IAAI,CAAC,EAAE,CAAC;AACrC,CAAC;AAIM,KAAK,UAAU,+BAA+B,CAAC,MAAuB;IACzE,OAAO,IAAI,uBAAuB,CAAC,MAAM,CAAC,CAAC;AAC/C,CAAC;AAGK,MAAO,uBAAwB,+MAAQ,uBAAoB;IAuC7D,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,cAAc,GAAA;QACrB,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACzC,CAAC;IAED,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,cAAc,GAAA;QACrB,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACzC,CAAC;IAED,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACnC,CAAC;IAED,IAAW,eAAe,CAAC,KAAa,EAAA;QACpC,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IACpC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACnC,CAAC;IAED,IAAW,aAAa,CAAC,KAA2C,EAAA;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAEhC,0DAA0D;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,GAAG,KAAK,CAAC;QAC5C,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACxC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IACjC,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IACjC,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;IAClC,CAAC;IAED,IAAW,YAAY,CAAC,KAA4B,EAAA;QAChD,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACnC,CAAC;IAED,IAAW,aAAa,CAAC,KAAa,EAAA;QAClC,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IACpC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,cAAA,EAAgB,CACT,eAAe,GAAA;QAClB,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtD,OAAO;QACX,CAAC;QAED,6GAA6G;QAC7G,wFAAwF;QACxF,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3G,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED,cAAA,EAAgB,CACT,eAAe,GAAA;QAClB,6GAA6G;QAC7G,wFAAwF;QACxF,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;YACpH,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC3E,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnE,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;8KAC9D,aAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;YAClG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,MAAM,CAAC;YACJ,OAAO;QACX,CAAC;0KAED,SAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACrD,4KAAO,CAAC,oBAAoB,mKAAC,UAAO,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAE1E,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC;IACjD,CAAC;IAEkB,QAAQ,CAAC,IAAqB,EAAA;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB,EAAA;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,yBAAyB,CAAC;IACrC,CAAC;IAjMD,cAAA,EAAgB,CAChB,YAAmB,MAAuB,CAAA;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QAzBV,IAAA,CAAA,aAAa,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACxC,IAAA,CAAA,aAAa,qKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACxC,IAAA,CAAA,uBAAuB,GAAe,sKAAI,aAAU,EAAE,CAAC;QAW/D,cAAA,EAAgB,CACA,IAAA,CAAA,QAAQ,GAAG,oOAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAClE,cAAA,EAAgB,CACA,IAAA,CAAA,QAAQ,+MAAY,wBAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC3E,cAAA,EAAgB,CACA,IAAA,CAAA,kBAAkB,GAAe,oOAAqB,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAS9F,IAAI,CAAC,IAAI,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEjD,IAAI,CAAC,aAAa,GAAG,IAAI,wOAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrF,IAAI,CAAC,aAAa,GAAG,IAAI,wOAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrF,IAAI,CAAC,aAAa,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAErF,IAAI,CAAC,UAAU,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/E,IAAI,CAAC,UAAU,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/E,IAAI,CAAC,UAAU,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnF,CAAC;CAqLJ", "debugId": null}}, {"offset": {"line": 2639, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/subNodes/stereoWebAudioSubNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/subNodes/stereoWebAudioSubNode.ts"], "sourcesContent": ["import { _StereoAudioSubNode } from \"../../abstractAudio/subNodes/stereoAudioSubNode\";\nimport { _WebAudioParameterComponent } from \"../components/webAudioParameterComponent\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\nimport type { IWebAudioInNode } from \"../webAudioNode\";\n\n/** @internal */\n// eslint-disable-next-line @typescript-eslint/require-await\nexport async function _CreateStereoAudioSubNodeAsync(engine: _WebAudioEngine): Promise<_StereoAudioSubNode> {\n    return new _StereoWebAudioSubNode(engine);\n}\n\n/** @internal */\nexport class _StereoWebAudioSubNode extends _StereoAudioSubNode {\n    private _pan: _WebAudioParameterComponent;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public readonly node: StereoPannerNode;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n\n        this.node = new StereoPannerNode(engine._audioContext);\n\n        this._pan = new _WebAudioParameterComponent(engine, this.node.pan);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._pan.dispose();\n    }\n\n    /** @internal */\n    public get pan(): number {\n        return this._pan.targetValue;\n    }\n\n    /** @internal */\n    public set pan(value: number) {\n        this._pan.targetValue = value;\n    }\n\n    /** @internal */\n    public get _inNode(): AudioNode {\n        return this.node;\n    }\n\n    /** @internal */\n    public get _outNode(): AudioNode {\n        return this.node;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_StereoWebAudioSubNode\";\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this.node.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this.node.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,iDAAiD,CAAC;AACtF,OAAO,EAAE,2BAA2B,EAAE,MAAM,0CAA0C,CAAC;;;AAMhF,KAAK,UAAU,8BAA8B,CAAC,MAAuB;IACxE,OAAO,IAAI,sBAAsB,CAAC,MAAM,CAAC,CAAC;AAC9C,CAAC;AAGK,MAAO,sBAAuB,8MAAQ,sBAAmB;IAkB3D,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IACjC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,GAAG,CAAC,KAAa,EAAA;QACxB,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAEkB,QAAQ,CAAC,IAAqB,EAAA;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB,EAAA;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IApED,cAAA,EAAgB,CAChB,YAAmB,MAAuB,CAAA;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEd,IAAI,CAAC,IAAI,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEvD,IAAI,CAAC,IAAI,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;CA8DJ", "debugId": null}}, {"offset": {"line": 2701, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subNodes/abstractAudioSubGraph.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subNodes/abstractAudioSubGraph.ts"], "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { AbstractAudioNode, AbstractNamedAudioNode } from \"../abstractAudioNode\";\nimport type { _AbstractAudioSubNode } from \"./abstractAudioSubNode\";\nimport type { AudioSubNode } from \"./audioSubNode\";\n\n/**\n * Adds common sub graph functionality to an audio node.\n *\n * Audio nodes such as static sounds, streaming sounds, and buses can use audio sub graphs to process audio internally\n * before sending it to connected downstream audio nodes. This is useful for applying effects, spatial audio, and other\n * audio processing tasks common to multiple audio node classes.\n *\n * A key feature of audio sub graphs is their audio sub nodes are created asynchronously on demand so the minimum set\n * of sub nodes are used at all times to save memory and CPU resources. The tradeoff is a small delay when first\n * setting a property backed by a sub node. This delay is avoided by using the appropriate options to initialize the\n * sub node on creation, e.g. `spatialEnabled` and `stereoEnabled`, or by setting any creation option backed by the\n * sub node, e.g. `spatialPosition` and `stereoPan`.\n *\n * @internal\n */\nexport abstract class _AbstractAudioSubGraph {\n    private _createSubNodePromises: { [key: string]: Promise<_AbstractAudioSubNode> } = {};\n    private _isDisposed = false;\n    private _subNodes: { [key: string]: _AbstractAudioSubNode } = {};\n\n    /**\n     * Executes the given callback with the named sub node, creating the sub node if needed.\n     *\n     * @param name The name of the sub node\n     * @param callback The function to call with the named sub node\n     *\n     * @internal\n     */\n    public callOnSubNode<T extends _AbstractAudioSubNode>(name: AudioSubNode, callback: (node: T) => void): void {\n        const node = this.getSubNode(name);\n        if (node) {\n            callback(node as T);\n            return;\n        }\n\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\n        this._createSubNodePromisesResolvedAsync().then(() => {\n            const node = this.getSubNode(name);\n            if (node) {\n                callback(node as T);\n                return;\n            }\n\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\n            this.createAndAddSubNodeAsync(name).then((node) => {\n                callback(node as T);\n            });\n        });\n    }\n\n    /**\n     * Creates the named subnode and adds it to the sub graph.\n     *\n     * @param name The name of the sub node.\n     * @returns A promise that resolves to the created sub node.\n     *\n     * @internal\n     */\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\n    public createAndAddSubNodeAsync(name: AudioSubNode): Promise<_AbstractAudioSubNode> {\n        // eslint-disable-next-line github/no-then\n        this._createSubNodePromises[name] ||= this._createSubNode(name).then((node) => {\n            this._addSubNode(node);\n            return node;\n        });\n\n        return this._createSubNodePromises[name];\n    }\n\n    /**\n     * Releases associated resources.\n     *\n     * @internal\n     */\n    public dispose() {\n        this._isDisposed = true;\n\n        const subNodes = Object.values(this._subNodes);\n        for (const subNode of subNodes) {\n            subNode.dispose();\n        }\n\n        this._subNodes = {};\n        this._createSubNodePromises = {};\n    }\n\n    /**\n     * Gets a previously created sub node.\n     *\n     * @param name - The name of the sub node\n     * @returns The named sub node, or `null` if it has not been created, yet\n     *\n     * @internal\n     * */\n    public getSubNode<T extends _AbstractAudioSubNode>(name: string): Nullable<T> {\n        return (this._subNodes[name] as T) ?? null;\n    }\n\n    /**\n     * Removes a sub node from the sub graph.\n     *\n     * @param subNode - The sub node to remove\n     * @returns A promise that resolves when the sub node is removed\n     *\n     * @internal\n     */\n    public async removeSubNodeAsync(subNode: _AbstractAudioSubNode): Promise<void> {\n        await this._createSubNodePromisesResolvedAsync();\n\n        const name = subNode.name;\n        if (this._subNodes[name]) {\n            delete this._subNodes[name];\n        }\n\n        delete this._createSubNodePromises[name];\n\n        this._onSubNodesChanged();\n    }\n\n    protected abstract _createSubNode(name: string): Promise<_AbstractAudioSubNode>;\n\n    /**\n     * Called when sub-nodes are added or removed.\n     * - Override this to connect and reconnect sub-nodes as needed.\n     */\n    protected abstract _onSubNodesChanged(): void;\n\n    protected async _createSubNodePromisesResolvedAsync(): Promise<_AbstractAudioSubNode[]> {\n        return await Promise.all(Object.values(this._createSubNodePromises));\n    }\n\n    private _addSubNode(node: _AbstractAudioSubNode): void {\n        if (this._isDisposed) {\n            node.dispose();\n            return;\n        }\n\n        this._subNodes[node.name] = node;\n\n        node.onDisposeObservable.addOnce(this._onSubNodeDisposed);\n\n        this._onSubNodesChanged();\n    }\n\n    private _onSubNodeDisposed = (node: AbstractAudioNode) => {\n        const subNode = node as AbstractNamedAudioNode;\n\n        delete this._subNodes[subNode.name];\n\n        this._onSubNodesChanged();\n    };\n}\n"], "names": [], "mappings": "AAKA;;;;;;;;;;;;;;GAcG;;;AACG,MAAgB,sBAAsB;IAKxC;;;;;;;OAOG,CACI,aAAa,CAAkC,IAAkB,EAAE,QAA2B,EAAA;QACjG,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,IAAI,EAAE,CAAC;YACP,QAAQ,CAAC,IAAS,CAAC,CAAC;YACpB,OAAO;QACX,CAAC;QAED,mFAAmF;QACnF,IAAI,CAAC,mCAAmC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YACjD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,IAAI,EAAE,CAAC;gBACP,QAAQ,CAAC,IAAS,CAAC,CAAC;gBACpB,OAAO;YACX,CAAC;YAED,mFAAmF;YACnF,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC9C,QAAQ,CAAC,IAAS,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG,CACH,2FAA2F;IACpF,wBAAwB,CAAC,IAAkB,EAAA;;QAC9C,0CAA0C;QAC1C,CAAA,KAAA,IAAI,CAAC,sBAAsB,CAAA,CAAC,IAAI,CAAA,IAAA,CAAA,EAAA,CAAJ,IAAI,CAAA,GAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1E,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,EAAC;QAEH,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/C,KAAK,MAAM,OAAO,IAAI,QAAQ,CAAE,CAAC;YAC7B,OAAO,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,CAAA,CAAE,CAAC;QACpB,IAAI,CAAC,sBAAsB,GAAG,CAAA,CAAE,CAAC;IACrC,CAAC;IAED;;;;;;;SAOK,CACE,UAAU,CAAkC,IAAY,EAAA;;QAC3D,mCAAY,CAAC,SAAS,CAAC,IAAI,CAAO,cAA1B,yDAA8B,IAAI,CAAC;IAC/C,CAAC;IAED;;;;;;;OAOG,CACI,KAAK,CAAC,kBAAkB,CAAC,OAA8B,EAAA;QAC1D,MAAM,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAEjD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAEzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAUS,KAAK,CAAC,mCAAmC,GAAA;QAC/C,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;IACzE,CAAC;IAEO,WAAW,CAAC,IAA2B,EAAA;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAEjC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE1D,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IA/HL,aAAA;QACY,IAAA,CAAA,sBAAsB,GAAsD,CAAA,CAAE,CAAC;QAC/E,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,SAAS,GAA6C,CAAA,CAAE,CAAC;QA8HzD,IAAA,CAAA,kBAAkB,GAAG,CAAC,IAAuB,EAAE,EAAE;YACrD,MAAM,OAAO,GAAG,IAA8B,CAAC;YAE/C,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEpC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC,CAAC;IACN,CAAC;CAAA", "debugId": null}}, {"offset": {"line": 2829, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/subNodes/volumeWebAudioSubNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/subNodes/volumeWebAudioSubNode.ts"], "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport { _VolumeAudioSubNode } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport type { IAudioParameterRampOptions } from \"../../audioParameter\";\nimport { _WebAudioParameterComponent } from \"../components/webAudioParameterComponent\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioSubNode } from \"../webAudioNode\";\n\n/** @internal */\n// eslint-disable-next-line @typescript-eslint/require-await\nexport async function _CreateVolumeAudioSubNodeAsync(engine: _WebAudioEngine): Promise<_VolumeAudioSubNode> {\n    return new _VolumeWebAudioSubNode(engine);\n}\n\n/** @internal */\nexport class _VolumeWebAudioSubNode extends _VolumeAudioSubNode implements IWebAudioSubNode {\n    private _volume: _WebAudioParameterComponent;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public readonly node: AudioNode;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n\n        const gainNode = (this.node = new GainNode(engine._audioContext));\n        this._volume = new _WebAudioParameterComponent(engine, gainNode.gain);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._volume.dispose();\n    }\n\n    /** @internal */\n    public get volume(): number {\n        return this._volume.value;\n    }\n\n    /** @internal */\n    public set volume(value: number) {\n        this.setVolume(value);\n    }\n\n    /** @internal */\n    public get _inNode(): AudioNode {\n        return this.node;\n    }\n\n    /** @internal */\n    public get _outNode(): AudioNode {\n        return this.node;\n    }\n\n    /** @internal */\n    public setVolume(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        this._volume.setTargetValue(value, options);\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this.node.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this.node.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_VolumeWebAudioSubNode\";\n    }\n}\n"], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,mBAAmB,EAAE,MAAM,iDAAiD,CAAC;AAEtF,OAAO,EAAE,2BAA2B,EAAE,MAAM,0CAA0C,CAAC;;;AAMhF,KAAK,UAAU,8BAA8B,CAAC,MAAuB;IACxE,OAAO,IAAI,sBAAsB,CAAC,MAAM,CAAC,CAAC;AAC9C,CAAC;AAGK,MAAO,sBAAuB,8MAAQ,sBAAmB;IAiB3D,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,cAAA,EAAgB,CACT,SAAS,CAAC,KAAa,EAA+D;YAA7D,2EAAyD,IAAI;QACzF,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAEkB,QAAQ,CAAC,IAAqB,EAAA;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB,EAAA;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAxED,cAAA,EAAgB,CAChB,YAAmB,MAAuB,CAAA;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEd,MAAM,QAAQ,GAAG,AAAC,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,OAAO,GAAG,8MAAI,8BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC1E,CAAC;CAmEJ", "debugId": null}}, {"offset": {"line": 2895, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/subNodes/webAudioAnalyzerSubNode.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/subNodes/webAudioAnalyzerSubNode.ts"], "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport { _AudioAnalyzerSubNode } from \"../../abstractAudio/subNodes/audioAnalyzerSubNode\";\nimport type { AudioAnalyzerFFTSizeType } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport { _GetEmptyByteFrequencyData, _GetEmptyFloatFrequencyData } from \"../../abstractAudio/subProperties/audioAnalyzer\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\nimport type { IWebAudioInNode } from \"../webAudioNode\";\n\n/** @internal */\n// eslint-disable-next-line @typescript-eslint/require-await\nexport async function _CreateAudioAnalyzerSubNodeAsync(engine: _WebAudioEngine): Promise<_AudioAnalyzerSubNode> {\n    return new _WebAudioAnalyzerSubNode(engine);\n}\n\n/** @internal */\nexport class _WebAudioAnalyzerSubNode extends _AudioAnalyzerSubNode implements IWebAudioInNode {\n    private readonly _analyzerNode: AnalyserNode;\n    private _byteFrequencyData: Nullable<Uint8Array> = null;\n    private _floatFrequencyData: Nullable<Float32Array> = null;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n\n        this._analyzerNode = new AnalyserNode(engine._audioContext);\n    }\n\n    /** @internal */\n    public get fftSize(): AudioAnalyzerFFTSizeType {\n        return this._analyzerNode.fftSize as AudioAnalyzerFFTSizeType;\n    }\n\n    public set fftSize(value: AudioAnalyzerFFTSizeType) {\n        if (value === this._analyzerNode.fftSize) {\n            return;\n        }\n\n        this._analyzerNode.fftSize = value;\n\n        this._clearArrays();\n    }\n\n    /** @internal */\n    public get _inNode(): AudioNode {\n        return this._analyzerNode;\n    }\n\n    /** @internal */\n    public get minDecibels(): number {\n        return this._analyzerNode.minDecibels;\n    }\n\n    public set minDecibels(value: number) {\n        this._analyzerNode.minDecibels = value;\n    }\n\n    /** @internal */\n    public get maxDecibels(): number {\n        return this._analyzerNode.maxDecibels;\n    }\n\n    public set maxDecibels(value: number) {\n        this._analyzerNode.maxDecibels = value;\n    }\n\n    /** @internal */\n    public get smoothing(): number {\n        return this._analyzerNode.smoothingTimeConstant;\n    }\n\n    public set smoothing(value: number) {\n        this._analyzerNode.smoothingTimeConstant = value;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._clearArrays();\n        this._byteFrequencyData = null;\n        this._floatFrequencyData = null;\n\n        this._analyzerNode.disconnect();\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioAnalyzerSubNode\";\n    }\n\n    /** @internal */\n    public getByteFrequencyData(): Uint8Array {\n        if (!this._byteFrequencyData || this._byteFrequencyData.length === 0) {\n            this._byteFrequencyData = new Uint8Array(this._analyzerNode.frequencyBinCount);\n        }\n        this._analyzerNode.getByteFrequencyData(this._byteFrequencyData);\n        return this._byteFrequencyData;\n    }\n\n    /** @internal */\n    public getFloatFrequencyData(): Float32Array {\n        if (!this._floatFrequencyData || this._floatFrequencyData.length === 0) {\n            this._floatFrequencyData = new Float32Array(this._analyzerNode.frequencyBinCount);\n        }\n        this._analyzerNode.getFloatFrequencyData(this._floatFrequencyData);\n        return this._floatFrequencyData;\n    }\n\n    private _clearArrays(): void {\n        this._byteFrequencyData?.set(_GetEmptyByteFrequencyData());\n        this._floatFrequencyData?.set(_GetEmptyFloatFrequencyData());\n    }\n}\n"], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,qBAAqB,EAAE,MAAM,mDAAmD,CAAC;AAE1F,OAAO,EAAE,0BAA0B,EAAE,2BAA2B,EAAE,MAAM,iDAAiD,CAAC;;;AAMnH,KAAK,UAAU,gCAAgC,CAAC,MAAuB;IAC1E,OAAO,IAAI,wBAAwB,CAAC,MAAM,CAAC,CAAC;AAChD,CAAC;AAGK,MAAO,wBAAyB,gNAAQ,wBAAqB;IAY/D,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,OAAmC,CAAC;IAClE,CAAC;IAED,IAAW,OAAO,CAAC,KAA+B,EAAA;QAC9C,IAAI,KAAK,KAAK,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YACvC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK,CAAC;QAEnC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;IAC1C,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;IAC1C,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC;IACpD,CAAC;IAED,IAAW,SAAS,CAAC,KAAa,EAAA;QAC9B,IAAI,CAAC,aAAa,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACrD,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;IACpC,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,0BAA0B,CAAC;IACtC,CAAC;IAED,cAAA,EAAgB,CACT,oBAAoB,GAAA;QACvB,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnE,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QACnF,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CACT,qBAAqB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrE,IAAI,CAAC,mBAAmB,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QACtF,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAEO,YAAY,GAAA;sCAEhB;oCADA,IAAI,CAAC,kBAAkB,sFAAE,GAAG,KAAC,kOAAA,AAA0B,EAAE,CAAC,CAAC;yCACvD,CAAC,mBAAmB,wFAAE,GAAG,yMAAC,+BAAA,AAA2B,EAAE,CAAC,CAAC;IACjE,CAAC;IA3FD,cAAA,EAAgB,CAChB,YAAmB,MAAuB,CAAA;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QALV,IAAA,CAAA,kBAAkB,GAAyB,IAAI,CAAC;QAChD,IAAA,CAAA,mBAAmB,GAA2B,IAAI,CAAC;QAMvD,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAChE,CAAC;CAuFJ", "debugId": null}}, {"offset": {"line": 2978, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/subNodes/webAudioBaseSubGraph.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/subNodes/webAudioBaseSubGraph.ts"], "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { AbstractAudioNode } from \"../../abstractAudio/abstractAudioNode\";\nimport { _AbstractAudioSubGraph } from \"../../abstractAudio/subNodes/abstractAudioSubGraph\";\nimport type { _AbstractAudioSubNode } from \"../../abstractAudio/subNodes/abstractAudioSubNode\";\nimport { _GetAudioAnalyzerSubNode } from \"../../abstractAudio/subNodes/audioAnalyzerSubNode\";\nimport { AudioSubNode } from \"../../abstractAudio/subNodes/audioSubNode\";\nimport type { IVolumeAudioOptions } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport { _GetVolumeAudioSubNode } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport type { IAudioAnalyzerOptions } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport { _HasAudioAnalyzerOptions } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport type { IWebAudioInNode, IWebAudioSuperNode } from \"../webAudioNode\";\nimport type { _VolumeWebAudioSubNode } from \"./volumeWebAudioSubNode\";\nimport { _CreateVolumeAudioSubNodeAsync } from \"./volumeWebAudioSubNode\";\nimport { _CreateAudioAnalyzerSubNodeAsync } from \"./webAudioAnalyzerSubNode\";\n\n/**\n * Options for creating a WebAudioBaseSubGraph.\n */\nexport interface IWebAudioBaseSubGraphOptions extends IAudioAnalyzerOptions, IVolumeAudioOptions {}\n\n/** @internal */\nexport abstract class _WebAudioBaseSubGraph extends _AbstractAudioSubGraph {\n    protected _owner: IWebAudioSuperNode;\n    protected _outputNode: Nullable<AudioNode> = null;\n\n    /** @internal */\n    public constructor(owner: IWebAudioSuperNode) {\n        super();\n\n        this._owner = owner;\n    }\n\n    /** @internal */\n    public async initAsync(options: Partial<IWebAudioBaseSubGraphOptions>): Promise<void> {\n        const hasAnalyzerOptions = _HasAudioAnalyzerOptions(options);\n\n        if (hasAnalyzerOptions) {\n            await this.createAndAddSubNodeAsync(AudioSubNode.ANALYZER);\n        }\n\n        await this.createAndAddSubNodeAsync(AudioSubNode.VOLUME);\n\n        await this._createSubNodePromisesResolvedAsync();\n\n        if (hasAnalyzerOptions) {\n            const analyzerNode = _GetAudioAnalyzerSubNode(this);\n            if (!analyzerNode) {\n                throw new Error(\"No analyzer subnode.\");\n            }\n\n            analyzerNode.setOptions(options);\n        }\n\n        const volumeNode = _GetVolumeAudioSubNode(this);\n        if (!volumeNode) {\n            throw new Error(\"No volume subnode.\");\n        }\n\n        volumeNode.setOptions(options);\n\n        if (volumeNode.getClassName() !== \"_VolumeWebAudioSubNode\") {\n            throw new Error(\"Not a WebAudio subnode.\");\n        }\n\n        this._outputNode = (volumeNode as _VolumeWebAudioSubNode).node;\n\n        // Connect the new wrapped WebAudio node to the wrapped downstream WebAudio nodes.\n        // The wrapper nodes are unaware of this change.\n        if (this._outputNode && this._downstreamNodes) {\n            const it = this._downstreamNodes.values();\n            for (let next = it.next(); !next.done; next = it.next()) {\n                const inNode = (next.value as IWebAudioInNode)._inNode;\n                if (inNode) {\n                    this._outputNode.connect(inNode);\n                }\n            }\n        }\n    }\n\n    protected abstract readonly _downstreamNodes: Nullable<Set<AbstractAudioNode>>;\n\n    /** @internal */\n    public get _inNode(): Nullable<AudioNode> {\n        return this._outputNode;\n    }\n\n    /** @internal */\n    public get _outNode(): Nullable<AudioNode> {\n        return this._outputNode;\n    }\n\n    // Function is async, but throws synchronously. Avoiding breaking changes.\n    // eslint-disable-next-line @typescript-eslint/promise-function-async\n    protected _createSubNode(name: string): Promise<_AbstractAudioSubNode> {\n        switch (name) {\n            case AudioSubNode.ANALYZER:\n                return _CreateAudioAnalyzerSubNodeAsync(this._owner.engine);\n            case AudioSubNode.VOLUME:\n                return _CreateVolumeAudioSubNodeAsync(this._owner.engine);\n            default:\n                throw new Error(`Unknown subnode name: ${name}`);\n        }\n    }\n\n    protected _onSubNodesChanged(): void {\n        const analyzerNode = _GetAudioAnalyzerSubNode(this);\n        const volumeNode = _GetVolumeAudioSubNode(this);\n\n        if (analyzerNode && volumeNode) {\n            volumeNode.connect(analyzerNode);\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,sBAAsB,EAAE,MAAM,oDAAoD,CAAC;AAE5F,OAAO,EAAE,wBAAwB,EAAE,MAAM,mDAAmD,CAAC;AAG7F,OAAO,EAAE,sBAAsB,EAAE,MAAM,iDAAiD,CAAC;AAEzF,OAAO,EAAE,wBAAwB,EAAE,MAAM,yDAAyD,CAAC;AAGnG,OAAO,EAAE,8BAA8B,EAAE,MAAM,yBAAyB,CAAC;AACzE,OAAO,EAAE,gCAAgC,EAAE,MAAM,2BAA2B,CAAC;;;;;;;AAQvE,MAAgB,qBAAsB,iNAAQ,yBAAsB;IAWtE,cAAA,EAAgB,CACT,KAAK,CAAC,SAAS,CAAC,OAA8C,EAAA;QACjE,MAAM,kBAAkB,IAAG,2OAAA,AAAwB,EAAC,OAAO,CAAC,CAAC;QAE7D,IAAI,kBAAkB,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,wBAAwB,CAAA,WAAA,yBAAA,GAAuB,CAAC;QAC/D,CAAC;QAED,MAAM,IAAI,CAAC,wBAAwB,CAAA,SAAA,uBAAA,GAAqB,CAAC;QAEzD,MAAM,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAEjD,IAAI,kBAAkB,EAAE,CAAC;YACrB,MAAM,YAAY,8MAAG,2BAAA,AAAwB,EAAC,IAAI,CAAC,CAAC;YACpD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAC5C,CAAC;YAED,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,UAAU,4MAAG,yBAAA,AAAsB,EAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC1C,CAAC;QAED,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE/B,IAAI,UAAU,CAAC,YAAY,EAAE,KAAK,wBAAwB,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,WAAW,GAAI,UAAqC,CAAC,IAAI,CAAC;QAE/D,kFAAkF;QAClF,gDAAgD;QAChD,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC1C,IAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAE,CAAC;gBACtD,MAAM,MAAM,GAAI,IAAI,CAAC,KAAyB,CAAC,OAAO,CAAC;gBACvD,IAAI,MAAM,EAAE,CAAC;oBACT,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrC,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAID,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,0EAA0E;IAC1E,qEAAqE;IAC3D,cAAc,CAAC,IAAY,EAAA;QACjC,OAAQ,IAAI,EAAE,CAAC;YACX,KAAA,WAAA,yBAAA;gBACI,OAAO,4OAAA,AAAgC,EAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAChE,KAAA,SAAA,uBAAA;gBACI,QAAO,uOAAA,AAA8B,EAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9D;gBACI,MAAM,IAAI,KAAK,CAAC,yBAA6B,CAAE,CAAC,CAAC,IAAR,IAAI;QACrD,CAAC;IACL,CAAC;IAES,kBAAkB,GAAA;QACxB,MAAM,YAAY,6MAAG,4BAAA,AAAwB,EAAC,IAAI,CAAC,CAAC;QACpD,MAAM,UAAU,4MAAG,yBAAsB,AAAtB,EAAuB,IAAI,CAAC,CAAC;QAEhD,IAAI,YAAY,IAAI,UAAU,EAAE,CAAC;YAC7B,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAtFD,cAAA,EAAgB,CAChB,YAAmB,KAAyB,CAAA;QACxC,KAAK,EAAE,CAAC;QAJF,IAAA,CAAA,WAAW,GAAwB,IAAI,CAAC;QAM9C,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;CAkFJ", "debugId": null}}, {"offset": {"line": 3064, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/subNodes/webAudioBusAndSoundSubGraph.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/subNodes/webAudioBusAndSoundSubGraph.ts"], "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { AbstractAudioNode } from \"../../abstractAudio/abstractAudioNode\";\nimport type { _AbstractAudioSubNode } from \"../../abstractAudio/subNodes/abstractAudioSubNode\";\nimport { AudioSubNode } from \"../../abstractAudio/subNodes/audioSubNode\";\nimport { _GetSpatialAudioSubNode } from \"../../abstractAudio/subNodes/spatialAudioSubNode\";\nimport { _GetStereoAudioSubNode } from \"../../abstractAudio/subNodes/stereoAudioSubNode\";\nimport type { IVolumeAudioOptions } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport { _GetVolumeAudioSubNode } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport type { ISpatialAudioOptions } from \"../../abstractAudio/subProperties/abstractSpatialAudio\";\nimport { _HasSpatialAudioOptions } from \"../../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { IStereoAudioOptions } from \"../../abstractAudio/subProperties/abstractStereoAudio\";\nimport { _HasStereoAudioOptions } from \"../../abstractAudio/subProperties/abstractStereoAudio\";\nimport type { IWebAudioOutNode, IWebAudioSubNode } from \"../webAudioNode\";\nimport type { _SpatialWebAudioSubNode } from \"./spatialWebAudioSubNode\";\nimport { _CreateSpatialAudioSubNodeAsync } from \"./spatialWebAudioSubNode\";\nimport type { _StereoWebAudioSubNode } from \"./stereoWebAudioSubNode\";\nimport { _CreateStereoAudioSubNodeAsync } from \"./stereoWebAudioSubNode\";\nimport type { _VolumeWebAudioSubNode } from \"./volumeWebAudioSubNode\";\nimport { _WebAudioBaseSubGraph } from \"./webAudioBaseSubGraph\";\n\n/** @internal */\nexport interface IWebAudioBusAndSoundSubGraphOptions extends ISpatialAudioOptions, IStereoAudioOptions, IVolumeAudioOptions {}\n\n/** @internal */\nexport abstract class _WebAudioBusAndSoundSubGraph extends _WebAudioBaseSubGraph {\n    private _rootNode: Nullable<GainNode> = null;\n    protected abstract readonly _upstreamNodes: Nullable<Set<AbstractAudioNode>>;\n\n    protected _inputNode: Nullable<AudioNode> = null;\n\n    /** @internal */\n    public override async initAsync(options: Partial<IWebAudioBusAndSoundSubGraphOptions>): Promise<void> {\n        await super.initAsync(options);\n\n        let hasSpatialOptions = false;\n        let hasStereoOptions = false;\n\n        if ((hasSpatialOptions = _HasSpatialAudioOptions(options))) {\n            await this.createAndAddSubNodeAsync(AudioSubNode.SPATIAL);\n        }\n        if ((hasStereoOptions = _HasStereoAudioOptions(options))) {\n            await this.createAndAddSubNodeAsync(AudioSubNode.STEREO);\n        }\n\n        await this._createSubNodePromisesResolvedAsync();\n\n        if (hasSpatialOptions) {\n            _GetSpatialAudioSubNode(this)?.setOptions(options);\n        }\n        if (hasStereoOptions) {\n            _GetStereoAudioSubNode(this)?.setOptions(options);\n        }\n    }\n\n    /** @internal */\n    public override get _inNode(): Nullable<AudioNode> {\n        return this._inputNode;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/promise-function-async\n    protected override _createSubNode(name: string): Promise<_AbstractAudioSubNode> {\n        try {\n            const node = super._createSubNode(name);\n            return node;\n        } catch (e) {}\n\n        switch (name) {\n            case AudioSubNode.SPATIAL:\n                return _CreateSpatialAudioSubNodeAsync(this._owner.engine);\n            case AudioSubNode.STEREO:\n                return _CreateStereoAudioSubNodeAsync(this._owner.engine);\n            default:\n                throw new Error(`Unknown subnode name: ${name}`);\n        }\n    }\n\n    protected override _onSubNodesChanged(): void {\n        super._onSubNodesChanged();\n\n        const spatialNode = _GetSpatialAudioSubNode(this);\n        const stereoNode = _GetStereoAudioSubNode(this);\n        const volumeNode = _GetVolumeAudioSubNode(this);\n\n        if (spatialNode && spatialNode.getClassName() !== \"_SpatialWebAudioSubNode\") {\n            throw new Error(\"Not a WebAudio subnode.\");\n        }\n        if (stereoNode && stereoNode.getClassName() !== \"_StereoWebAudioSubNode\") {\n            throw new Error(\"Not a WebAudio subnode.\");\n        }\n        if (volumeNode && volumeNode.getClassName() !== \"_VolumeWebAudioSubNode\") {\n            throw new Error(\"Not a WebAudio subnode.\");\n        }\n\n        if (spatialNode) {\n            spatialNode.disconnectAll();\n\n            if (volumeNode) {\n                spatialNode.connect(volumeNode);\n            }\n        }\n\n        if (stereoNode) {\n            stereoNode.disconnectAll();\n\n            if (volumeNode) {\n                stereoNode.connect(volumeNode);\n            }\n        }\n\n        if (spatialNode && stereoNode) {\n            this._rootNode = new GainNode(this._owner.engine._audioContext);\n            this._rootNode.connect((spatialNode as _SpatialWebAudioSubNode)._outNode);\n            this._rootNode.connect((stereoNode as _StereoWebAudioSubNode)._outNode);\n        } else {\n            this._rootNode?.disconnect();\n            this._rootNode = null;\n        }\n\n        let inSubNode: Nullable<IWebAudioSubNode> = null;\n\n        let inNode: Nullable<AudioNode> = null;\n\n        if (this._rootNode) {\n            inNode = this._rootNode;\n        } else {\n            if (spatialNode) {\n                inSubNode = spatialNode as _SpatialWebAudioSubNode;\n            } else if (stereoNode) {\n                inSubNode = stereoNode as _StereoWebAudioSubNode;\n            } else if (volumeNode) {\n                inSubNode = volumeNode as _VolumeWebAudioSubNode;\n            }\n\n            inNode = inSubNode?.node ?? null;\n        }\n\n        if (this._inputNode !== inNode) {\n            // Disconnect the wrapped upstream WebAudio nodes from the old wrapped WebAudio node.\n            // The wrapper nodes are unaware of this change.\n            if (this._inputNode && this._upstreamNodes) {\n                const it = this._upstreamNodes.values();\n                for (let next = it.next(); !next.done; next = it.next()) {\n                    (next.value as IWebAudioOutNode)._outNode?.disconnect(this._inputNode);\n                }\n            }\n\n            this._inputNode = inNode;\n\n            // Connect the wrapped upstream WebAudio nodes to the new wrapped WebAudio node.\n            // The wrapper nodes are unaware of this change.\n            if (inNode && this._upstreamNodes) {\n                const it = this._upstreamNodes.values();\n                for (let next = it.next(); !next.done; next = it.next()) {\n                    (next.value as IWebAudioOutNode)._outNode?.connect(inNode);\n                }\n            }\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAIA,OAAO,EAAE,uBAAuB,EAAE,MAAM,kDAAkD,CAAC;AAC3F,OAAO,EAAE,sBAAsB,EAAE,MAAM,iDAAiD,CAAC;AAEzF,OAAO,EAAE,sBAAsB,EAAE,MAAM,iDAAiD,CAAC;AAEzF,OAAO,EAAE,uBAAuB,EAAE,MAAM,wDAAwD,CAAC;AAEjG,OAAO,EAAE,sBAAsB,EAAE,MAAM,uDAAuD,CAAC;AAG/F,OAAO,EAAE,+BAA+B,EAAE,MAAM,0BAA0B,CAAC;AAE3E,OAAO,EAAE,8BAA8B,EAAE,MAAM,yBAAyB,CAAC;AAEzE,OAAO,EAAE,qBAAqB,EAAE,MAAM,wBAAwB,CAAC;;;;;;;;;AAMzD,MAAgB,4BAA6B,2MAAQ,wBAAqB;IAM5E,cAAA,EAAgB,CACA,KAAK,CAAC,SAAS,CAAC,OAAqD,EAAA;QACjF,MAAM,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAE/B,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAE7B,IAAI,AAAC,iBAAiB,mNAAG,0BAAA,AAAuB,EAAC,OAAO,CAAC,CAAC,CAAE,CAAC;YACzD,MAAM,IAAI,CAAC,wBAAwB,CAAA,UAAA,wBAAA,GAAsB,CAAC;QAC9D,CAAC;QACD,IAAI,AAAC,gBAAgB,kNAAG,yBAAA,AAAsB,EAAC,OAAO,CAAC,CAAC,CAAE,CAAC;YACvD,MAAM,IAAI,CAAC,wBAAwB,CAAA,SAAA,uBAAA,GAAqB,CAAC;QAC7D,CAAC;QAED,MAAM,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAEjD,IAAI,iBAAiB,EAAE,CAAC;;iPACpB,0BAAA,AAAuB,EAAC,IAAI,CAAC,oFAAE,UAAU,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,gBAAgB,EAAE,CAAC;;+OACnB,yBAAA,AAAsB,EAAC,IAAI,CAAC,kFAAE,UAAU,CAAC,OAAO,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CAChB,IAAoB,OAAO,GAAA;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,qEAAqE;IAClD,cAAc,CAAC,IAAY,EAAA;QAC1C,IAAI,CAAC;YACD,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEd,OAAQ,IAAI,EAAE,CAAC;YACX,KAAA,UAAA,wBAAA;gBACI,+MAAO,kCAAA,AAA+B,EAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC/D,KAAA,SAAA,uBAAA;gBACI,8MAAO,iCAAA,AAA8B,EAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9D;gBACI,MAAM,IAAI,KAAK,CAAC,yBAA6B,CAAE,CAAC,CAAC,IAAR,IAAI;QACrD,CAAC;IACL,CAAC;IAEkB,kBAAkB,GAAA;QACjC,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,MAAM,WAAW,6MAAG,0BAAuB,AAAvB,EAAwB,IAAI,CAAC,CAAC;QAClD,MAAM,UAAU,2MAAG,0BAAA,AAAsB,EAAC,IAAI,CAAC,CAAC;QAChD,MAAM,UAAU,4MAAG,yBAAA,AAAsB,EAAC,IAAI,CAAC,CAAC;QAEhD,IAAI,WAAW,IAAI,WAAW,CAAC,YAAY,EAAE,KAAK,yBAAyB,EAAE,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,UAAU,IAAI,UAAU,CAAC,YAAY,EAAE,KAAK,wBAAwB,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,UAAU,IAAI,UAAU,CAAC,YAAY,EAAE,KAAK,wBAAwB,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,aAAa,EAAE,CAAC;YAE5B,IAAI,UAAU,EAAE,CAAC;gBACb,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,aAAa,EAAE,CAAC;YAE3B,IAAI,UAAU,EAAE,CAAC;gBACb,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACnC,CAAC;QACL,CAAC;QAED,IAAI,WAAW,IAAI,UAAU,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAChE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAE,WAAuC,CAAC,QAAQ,CAAC,CAAC;YAC1E,IAAI,CAAC,SAAS,CAAC,OAAO,CAAE,UAAqC,CAAC,QAAQ,CAAC,CAAC;QAC5E,CAAC,MAAM,CAAC;;mCACA,CAAC,SAAS,oDAAd,gBAAgB,UAAU,EAAE,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC;QAED,IAAI,SAAS,GAA+B,IAAI,CAAC;QAEjD,IAAI,MAAM,GAAwB,IAAI,CAAC;QAEvC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAC5B,CAAC,MAAM,CAAC;YACJ,IAAI,WAAW,EAAE,CAAC;gBACd,SAAS,GAAG,WAAsC,CAAC;YACvD,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;gBACpB,SAAS,GAAG,UAAoC,CAAC;YACrD,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;gBACpB,SAAS,GAAG,UAAoC,CAAC;YACrD,CAAC;;YAED,MAAM,sFAAc,IAAI,2CAAf,SAAS,SAAU,IAAI,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YAC7B,qFAAqF;YACrF,gDAAgD;YAChD,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzC,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;gBACxC,IAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAE,CAAC;;iDAChD,KAA0B,CAAC,QAAQ,yDAAxC,IAAI,iBAAsC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC3E,CAAC;YACL,CAAC;YAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;YAEzB,gFAAgF;YAChF,gDAAgD;YAChD,IAAI,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBAChC,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;gBACxC,IAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAE,CAAC;;kDAChD,KAA0B,CAAC,QAAQ,0DAAxC,IAAI,kBAAsC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IArIL,aAAA;;QACY,IAAA,CAAA,SAAS,GAAuB,IAAI,CAAC;QAGnC,IAAA,CAAA,UAAU,GAAwB,IAAI,CAAC;IAkIrD,CAAC;CAAA", "debugId": null}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/subProperties/spatialAudio.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/subProperties/spatialAudio.ts"], "sourcesContent": ["import type { Quaternion, Vector3 } from \"../../../Maths/math.vector\";\nimport type { Node } from \"../../../node\";\nimport type { Nullable } from \"../../../types\";\nimport { SpatialAudioAttachmentType } from \"../../spatialAudioAttachmentType\";\nimport type { _AbstractAudioSubGraph } from \"../subNodes/abstractAudioSubGraph\";\nimport { AudioSubNode } from \"../subNodes/audioSubNode\";\nimport type { _SpatialAudioSubNode } from \"../subNodes/spatialAudioSubNode\";\nimport { _GetSpatialAudioSubNode, _SetSpatialAudioProperty } from \"../subNodes/spatialAudioSubNode\";\nimport { _SpatialAudioDefaults, AbstractSpatialAudio } from \"./abstractSpatialAudio\";\n\n/** @internal */\nexport abstract class _SpatialAudio extends AbstractSpatialAudio {\n    private _coneInnerAngle: number = _SpatialAudioDefaults.coneInnerAngle;\n    private _coneOuterAngle: number = _SpatialAudioDefaults.coneOuterAngle;\n    private _coneOuterVolume: number = _SpatialAudioDefaults.coneOuterVolume;\n    private _distanceModel: DistanceModelType = _SpatialAudioDefaults.distanceModel;\n    private _maxDistance: number = _SpatialAudioDefaults.maxDistance;\n    private _minDistance: number = _SpatialAudioDefaults.minDistance;\n    private _panningModel: PanningModelType = _SpatialAudioDefaults.panningModel;\n    private _position: Vector3;\n    private _rolloffFactor: number = _SpatialAudioDefaults.rolloffFactor;\n    private _rotation: Vector3;\n    private _rotationQuaternion: Quaternion;\n    private _subGraph: _AbstractAudioSubGraph;\n\n    /** @internal */\n    public constructor(subGraph: _AbstractAudioSubGraph) {\n        super();\n\n        const subNode = _GetSpatialAudioSubNode(subGraph);\n        if (subNode) {\n            this._position = subNode.position.clone();\n            this._rotation = subNode.rotation.clone();\n            this._rotationQuaternion = subNode.rotationQuaternion.clone();\n        } else {\n            this._position = _SpatialAudioDefaults.position.clone();\n            this._rotation = _SpatialAudioDefaults.rotation.clone();\n            this._rotationQuaternion = _SpatialAudioDefaults.rotationQuaternion.clone();\n\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            subGraph.createAndAddSubNodeAsync(AudioSubNode.SPATIAL);\n        }\n\n        this._subGraph = subGraph;\n    }\n\n    /** @internal */\n    public get coneInnerAngle(): number {\n        return this._coneInnerAngle;\n    }\n\n    public set coneInnerAngle(value: number) {\n        this._coneInnerAngle = value;\n        _SetSpatialAudioProperty(this._subGraph, \"coneInnerAngle\", value);\n    }\n\n    /** @internal */\n    public get coneOuterAngle(): number {\n        return this._coneOuterAngle;\n    }\n\n    public set coneOuterAngle(value: number) {\n        this._coneOuterAngle = value;\n        _SetSpatialAudioProperty(this._subGraph, \"coneOuterAngle\", value);\n    }\n\n    /** @internal */\n    public get coneOuterVolume(): number {\n        return this._coneOuterVolume;\n    }\n\n    public set coneOuterVolume(value: number) {\n        this._coneOuterVolume = value;\n        _SetSpatialAudioProperty(this._subGraph, \"coneOuterVolume\", value);\n    }\n\n    /** @internal */\n    public get distanceModel(): DistanceModelType {\n        return this._distanceModel;\n    }\n\n    public set distanceModel(value: DistanceModelType) {\n        this._distanceModel = value;\n        _SetSpatialAudioProperty(this._subGraph, \"distanceModel\", value);\n    }\n\n    /** @internal */\n    public get isAttached(): boolean {\n        return this._subGraph.getSubNode<_SpatialAudioSubNode>(AudioSubNode.SPATIAL)?.isAttached ?? false;\n    }\n\n    /** @internal */\n    public get maxDistance(): number {\n        return this._maxDistance;\n    }\n\n    public set maxDistance(value: number) {\n        if (value <= 0) {\n            value = 0.000001;\n        }\n\n        this._maxDistance = value;\n        _SetSpatialAudioProperty(this._subGraph, \"maxDistance\", value);\n    }\n\n    /** @internal */\n    public get minDistance(): number {\n        return this._minDistance;\n    }\n\n    public set minDistance(value: number) {\n        this._minDistance = value;\n        _SetSpatialAudioProperty(this._subGraph, \"minDistance\", value);\n    }\n\n    /** @internal */\n    public get panningModel(): PanningModelType {\n        return this._panningModel;\n    }\n\n    public set panningModel(value: PanningModelType) {\n        this._panningModel = value;\n        _SetSpatialAudioProperty(this._subGraph, \"panningModel\", value);\n    }\n\n    /** @internal */\n    public get position(): Vector3 {\n        return this._position;\n    }\n\n    public set position(value: Vector3) {\n        this._position = value;\n        this._updatePosition();\n    }\n\n    /** @internal */\n    public get rolloffFactor(): number {\n        return this._rolloffFactor;\n    }\n\n    public set rolloffFactor(value: number) {\n        this._rolloffFactor = value;\n        _SetSpatialAudioProperty(this._subGraph, \"rolloffFactor\", value);\n    }\n\n    /** @internal */\n    public get rotation(): Vector3 {\n        return this._rotation;\n    }\n\n    public set rotation(value: Vector3) {\n        this._rotation = value;\n        this._updateRotation();\n    }\n\n    /** @internal */\n    public get rotationQuaternion(): Quaternion {\n        return this._rotationQuaternion;\n    }\n\n    public set rotationQuaternion(value: Quaternion) {\n        this._rotationQuaternion = value;\n        this._updateRotation();\n    }\n\n    /**\n     * Attaches to a scene node.\n     *\n     * Detaches automatically before attaching to the given scene node.\n     * If `sceneNode` is `null` it is the same as calling `detach()`.\n     *\n     * @param sceneNode The scene node to attach to, or `null` to detach.\n     * @param useBoundingBox Whether to use the bounding box of the node for positioning. Defaults to `false`.\n     * @param attachmentType Whether to attach to the node's position and/or rotation. Defaults to `PositionAndRotation`.\n     */\n    public attach(sceneNode: Nullable<Node>, useBoundingBox: boolean = false, attachmentType: SpatialAudioAttachmentType = SpatialAudioAttachmentType.PositionAndRotation): void {\n        _GetSpatialAudioSubNode(this._subGraph)?.attach(sceneNode, useBoundingBox, attachmentType);\n    }\n\n    /**\n     * Detaches from the scene node if attached.\n     */\n    public detach(): void {\n        _GetSpatialAudioSubNode(this._subGraph)?.detach();\n    }\n\n    /** @internal */\n    public update(): void {\n        const subNode = _GetSpatialAudioSubNode(this._subGraph);\n\n        if (!subNode) {\n            return;\n        }\n\n        if (subNode.isAttached) {\n            subNode.update();\n        } else {\n            this._updatePosition(subNode);\n            this._updateRotation(subNode);\n        }\n    }\n\n    private _updatePosition(subNode: Nullable<_SpatialAudioSubNode> = null): void {\n        if (!subNode) {\n            subNode = _GetSpatialAudioSubNode(this._subGraph);\n\n            if (!subNode) {\n                return;\n            }\n        }\n\n        const position = subNode.position;\n        if (!position.equalsWithEpsilon(this._position)) {\n            subNode.position.copyFrom(this._position);\n            subNode._updatePosition();\n        }\n    }\n\n    private _updateRotation(subNode: Nullable<_SpatialAudioSubNode> = null): void {\n        if (!subNode) {\n            subNode = _GetSpatialAudioSubNode(this._subGraph);\n\n            if (!subNode) {\n                return;\n            }\n        }\n\n        if (!subNode.rotationQuaternion.equalsWithEpsilon(this._rotationQuaternion)) {\n            subNode.rotationQuaternion.copyFrom(this._rotationQuaternion);\n            subNode._updateRotation();\n        } else if (!subNode.rotation.equalsWithEpsilon(this._rotation)) {\n            subNode.rotation.copyFrom(this._rotation);\n            subNode._updateRotation();\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAOA,OAAO,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,MAAM,iCAAiC,CAAC;AACpG,OAAO,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;;;AAG/E,MAAgB,aAAc,SAAQ,mOAAoB;IAmC5D,cAAA,EAAgB,CAChB,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;iNAC7B,4BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACtE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;kNAC7B,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACtE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,IAAW,eAAe,CAAC,KAAa,EAAA;QACpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;kNAC9B,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAwB,EAAA;QAC7C,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;kNAC5B,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,UAAU,GAAA;;;QACjB,kFAAW,CAAC,SAAS,CAAC,UAAU,CAAA,UAAA,wBAAA,GAA4C,2FAAE,UAAU,iEAAjF,wCAAqF,KAAK,CAAC;IACtG,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACb,KAAK,GAAG,QAAQ,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;kNAC1B,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;kNAC1B,2BAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAAuB,EAAA;QAC3C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;SAC3B,oOAAA,AAAwB,EAAC,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAc,EAAA;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAa,EAAA;QAClC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;kNAC5B,2BAAwB,AAAxB,EAAyB,IAAI,CAAC,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAc,EAAA;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAiB,EAAA;QAC3C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;;;;;;;;OASG,CACI,MAAM,CAAC,SAAyB,EAA8H;6BAA5H,iEAA0B,KAAK,mBAAE,gDAAA,kDAAA,EAA2F,mBAA3F;;uQAC/C,AAAvB,EAAwB,IAAI,CAAC,SAAS,CAAC,cAAvC,sEAAyC,MAAM,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;;uQACT,AAAuB,EAAC,IAAI,CAAC,SAAS,CAAC,4DAAvC,wBAAyC,MAAM,EAAE,CAAC;IACtD,CAAC;IAED,cAAA,EAAgB,CACT,MAAM,GAAA;QACT,MAAM,OAAO,6MAAG,0BAAA,AAAuB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAExD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO;QACX,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO,CAAC,MAAM,EAAE,CAAC;QACrB,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAEO,eAAe,GAA+C;sBAA9C,iEAA0C,IAAI;QAClE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,6MAAG,0BAAA,AAAuB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAElD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO;YACX,CAAC;QACL,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1C,OAAO,CAAC,eAAe,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;IAEO,eAAe,GAA+C;sBAA9C,iEAA0C,IAAI;QAClE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,IAAG,mOAAA,AAAuB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAElD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO;YACX,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC1E,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC9D,OAAO,CAAC,eAAe,EAAE,CAAC;QAC9B,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7D,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1C,OAAO,CAAC,eAAe,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;IAjND,cAAA,EAAgB,CAChB,YAAmB,QAAgC,CAAA;QAC/C,KAAK,EAAE,CAAC;QAfJ,IAAA,CAAA,eAAe,+MAAW,wBAAqB,CAAC,cAAc,CAAC;QAC/D,IAAA,CAAA,eAAe,GAAW,oOAAqB,CAAC,cAAc,CAAC;QAC/D,IAAA,CAAA,gBAAgB,+MAAW,wBAAqB,CAAC,eAAe,CAAC;QACjE,IAAA,CAAA,cAAc,+MAAsB,wBAAqB,CAAC,aAAa,CAAC;QACxE,IAAA,CAAA,YAAY,+MAAW,wBAAqB,CAAC,WAAW,CAAC;QACzD,IAAA,CAAA,YAAY,+MAAW,wBAAqB,CAAC,WAAW,CAAC;QACzD,IAAA,CAAA,aAAa,+MAAqB,wBAAqB,CAAC,YAAY,CAAC;QAErE,IAAA,CAAA,cAAc,+MAAW,wBAAqB,CAAC,aAAa,CAAC;QASjE,MAAM,OAAO,6MAAG,0BAAA,AAAuB,EAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAClE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,SAAS,+MAAG,wBAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACxD,IAAI,CAAC,SAAS,GAAG,oOAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACxD,IAAI,CAAC,mBAAmB,+MAAG,wBAAqB,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAE5E,mEAAmE;YACnE,QAAQ,CAAC,wBAAwB,CAAA,UAAA,wBAAA,GAAsB,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;CA+LJ", "debugId": null}}, {"offset": {"line": 3388, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/subProperties/spatialWebAudio.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/subProperties/spatialWebAudio.ts"], "sourcesContent": ["import type { _AbstractAudioSubGraph } from \"../../abstractAudio/subNodes/abstractAudioSubGraph\";\nimport { _SpatialAudio } from \"../../abstractAudio/subProperties/spatialAudio\";\nimport { _SpatialWebAudioUpdaterComponent } from \"../components/spatialWebAudioUpdaterComponent\";\n\n/** @internal */\nexport class _SpatialWebAudio extends _SpatialAudio {\n    private _updaterComponent: _SpatialWebAudioUpdaterComponent;\n\n    /** @internal */\n    public constructor(subGraph: _AbstractAudioSubGraph, autoUpdate: boolean, minUpdateTime: number) {\n        super(subGraph);\n\n        this._updaterComponent = new _SpatialWebAudioUpdaterComponent(this, autoUpdate, minUpdateTime);\n    }\n\n    /** @internal */\n    public get minUpdateTime(): number {\n        return this._updaterComponent.minUpdateTime;\n    }\n\n    /** @internal */\n    public set minUpdateTime(value: number) {\n        this._updaterComponent.minUpdateTime = value;\n    }\n\n    /** @internal */\n    public dispose(): void {\n        this._updaterComponent.dispose();\n        this._updaterComponent = null!;\n    }\n}\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,aAAa,EAAE,MAAM,gDAAgD,CAAC;AAC/E,OAAO,EAAE,gCAAgC,EAAE,MAAM,+CAA+C,CAAC;;;AAG3F,MAAO,gBAAiB,6MAAQ,gBAAa;IAU/C,cAAA,EAAgB,CAChB,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;IAChD,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,aAAa,CAAC,KAAa,EAAA;QAClC,IAAI,CAAC,iBAAiB,CAAC,aAAa,GAAG,KAAK,CAAC;IACjD,CAAC;IAED,cAAA,EAAgB,CACT,OAAO,GAAA;QACV,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,IAAK,CAAC;IACnC,CAAC;IArBD,cAAA,EAAgB,CAChB,YAAmB,QAAgC,EAAE,UAAmB,EAAE,aAAqB,CAAA;QAC3F,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEhB,IAAI,CAAC,iBAAiB,GAAG,mNAAI,mCAAgC,CAAC,IAAI,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IACnG,CAAC;CAiBJ", "debugId": null}}, {"offset": {"line": 3415, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioBus.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/webAudioBus.ts"], "sourcesContent": ["import type { Nullable } from \"core/types\";\nimport type { AbstractAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type { IAudioBusOptions } from \"../abstractAudio/audioBus\";\nimport { AudioBus } from \"../abstractAudio/audioBus\";\nimport { _HasSpatialAudioOptions } from \"../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { _SpatialAudio } from \"../abstractAudio/subProperties/spatialAudio\";\nimport { _StereoAudio } from \"../abstractAudio/subProperties/stereoAudio\";\nimport { _WebAudioBusAndSoundSubGraph } from \"./subNodes/webAudioBusAndSoundSubGraph\";\nimport { _SpatialWebAudio } from \"./subProperties/spatialWebAudio\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioSuperNode } from \"./webAudioNode\";\n\n/** @internal */\nexport class _WebAudioBus extends AudioBus implements IWebAudioSuperNode {\n    private _spatial: Nullable<_SpatialAudio> = null;\n    private readonly _spatialAutoUpdate: boolean = true;\n    private readonly _spatialMinUpdateTime: number = 0;\n    private _stereo: Nullable<_StereoAudio> = null;\n\n    protected _subGraph: _WebAudioBusAndSoundSubGraph;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(name: string, engine: _WebAudioEngine, options: Partial<IAudioBusOptions>) {\n        super(name, engine);\n\n        if (typeof options.spatialAutoUpdate === \"boolean\") {\n            this._spatialAutoUpdate = options.spatialAutoUpdate;\n        }\n\n        if (typeof options.spatialMinUpdateTime === \"number\") {\n            this._spatialMinUpdateTime = options.spatialMinUpdateTime;\n        }\n\n        this._subGraph = new _WebAudioBus._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(options: Partial<IAudioBusOptions>): Promise<void> {\n        if (options.outBus) {\n            this.outBus = options.outBus;\n        } else {\n            await this.engine.isReadyPromise;\n            this.outBus = this.engine.defaultMainBus;\n        }\n\n        await this._subGraph.initAsync(options);\n\n        if (_HasSpatialAudioOptions(options)) {\n            this._initSpatialProperty();\n        }\n\n        this.engine._addNode(this);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._spatial = null;\n        this._stereo = null;\n\n        this.engine._removeNode(this);\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._subGraph._inNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    /** @internal */\n    public override get spatial(): _SpatialAudio {\n        if (this._spatial) {\n            return this._spatial;\n        }\n        return this._initSpatialProperty();\n    }\n\n    /** @internal */\n    public override get stereo(): _StereoAudio {\n        return this._stereo ?? (this._stereo = new _StereoAudio(this._subGraph));\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioBus\";\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initSpatialProperty(): _SpatialAudio {\n        if (!this._spatial) {\n            this._spatial = new _SpatialWebAudio(this._subGraph, this._spatialAutoUpdate, this._spatialMinUpdateTime);\n        }\n\n        return this._spatial;\n    }\n\n    private static _SubGraph = class extends _WebAudioBusAndSoundSubGraph {\n        protected override _owner: _WebAudioBus;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n\n        protected get _upstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._upstreamNodes ?? null;\n        }\n    };\n}\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAE,uBAAuB,EAAE,MAAM,qDAAqD,CAAC;AAE9F,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;;;;;;AAK7D,MAAO,YAAa,wLAAQ,WAAQ;IA0BtC,cAAA,EAAgB,CACT,KAAK,CAAC,UAAU,CAAC,OAAkC,EAAA;QACtD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,CAAC,MAAM,CAAC;YACJ,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,oNAAI,0BAAA,AAAuB,EAAC,OAAO,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAoB,OAAO,GAAA;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAoB,MAAM,GAAA;;QACtB,4BAAW,CAAC,OAAO,IAAI,qCAAhB,gBAAiB,IAAI,CAAC,OAAO,GAAG,uMAAI,eAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEkB,QAAQ,CAAC,IAAqB,EAAA;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACX,CAAC,QAAQ,mDAAb,eAAe,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB,EAAA;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACX,CAAC,QAAQ,mDAAb,eAAe,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,sMAAI,mBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9G,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAzGD,cAAA,EAAgB,CAChB,YAAmB,IAAY,EAAE,MAAuB,EAAE,OAAkC,CAAA;QACxF,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAZhB,IAAA,CAAA,QAAQ,GAA4B,IAAI,CAAC;QAChC,IAAA,CAAA,kBAAkB,GAAY,IAAI,CAAC;QACnC,IAAA,CAAA,qBAAqB,GAAW,CAAC,CAAC;QAC3C,IAAA,CAAA,OAAO,GAA2B,IAAI,CAAC;QAW3C,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;;AA8Fc,aAAA,SAAS,GAAG,KAAM,kNAAQ,+BAA4B;IAGjE,IAAc,gBAAgB,GAAA;;QAC1B,4CAAW,CAAC,MAAM,CAAC,gBAAgB,yDAA5B,gCAAgC,IAAI,CAAC;IAChD,CAAC;IAED,IAAc,cAAc,GAAA;;QACxB,0CAAW,CAAC,MAAM,CAAC,cAAc,uDAA1B,8BAA8B,IAAI,CAAC;IAC9C,CAAC;CACJ,AAVuB,CAUtB", "debugId": null}}, {"offset": {"line": 3524, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioMainBus.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/webAudioMainBus.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { AbstractAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type { IMainAudioBusOptions } from \"../abstractAudio/mainAudioBus\";\nimport { MainAudioBus } from \"../abstractAudio/mainAudioBus\";\nimport { _WebAudioBaseSubGraph } from \"./subNodes/webAudioBaseSubGraph\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioSuperNode } from \"./webAudioNode\";\n\n/** @internal */\nexport class _WebAudioMainBus extends MainAudioBus implements IWebAudioSuperNode {\n    protected _subGraph: _WebAudioBaseSubGraph;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(name: string, engine: _WebAudioEngine) {\n        super(name, engine);\n\n        this._subGraph = new _WebAudioMainBus._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(options: Partial<IMainAudioBusOptions>): Promise<void> {\n        await this._subGraph.initAsync(options);\n\n        if (this.engine.mainOut) {\n            if (!this._connect(this.engine.mainOut)) {\n                throw new Error(\"Connect failed\");\n            }\n        }\n\n        this.engine._addMainBus(this);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this.engine._removeMainBus(this);\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._subGraph._inNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioMainBus\";\n    }\n\n    private static _SubGraph = class extends _WebAudioBaseSubGraph {\n        protected override _owner: _WebAudioMainBus;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n    };\n}\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAC7D,OAAO,EAAE,qBAAqB,EAAE,MAAM,iCAAiC,CAAC;;;AAKlE,MAAO,gBAAiB,4LAAQ,eAAY;IAa9C,cAAA,EAAgB,CACT,KAAK,CAAC,UAAU,CAAC,OAAsC,EAAA;QAC1D,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAEkB,QAAQ,CAAC,IAAqB,EAAA;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACX,CAAC,QAAQ,mDAAb,eAAe,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB,EAAA;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACX,CAAC,QAAQ,mDAAb,eAAe,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IApED,cAAA,EAAgB,CAChB,YAAmB,IAAY,EAAE,MAAuB,CAAA;QACpD,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEpB,IAAI,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;;AAiEc,iBAAA,SAAS,GAAG,KAAM,2MAAQ,wBAAqB;IAG1D,IAAc,gBAAgB,GAAA;;QAC1B,4CAAW,CAAC,MAAM,CAAC,gBAAgB,yDAA5B,gCAAgC,IAAI,CAAC;IAChD,CAAC;CACJ,CAAC", "debugId": null}}, {"offset": {"line": 3591, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioSoundSource.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/webAudioSoundSource.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { AbstractAudioNode } from \"../abstractAudio\";\nimport type { ISoundSourceOptions } from \"../abstractAudio/abstractSoundSource\";\nimport { AbstractSoundSource } from \"../abstractAudio/abstractSoundSource\";\nimport { _HasSpatialAudioOptions } from \"../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { _SpatialAudio } from \"../abstractAudio/subProperties/spatialAudio\";\nimport { _StereoAudio } from \"../abstractAudio/subProperties/stereoAudio\";\nimport { _WebAudioBusAndSoundSubGraph } from \"./subNodes/webAudioBusAndSoundSubGraph\";\nimport { _SpatialWebAudio } from \"./subProperties/spatialWebAudio\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode } from \"./webAudioNode\";\n\n/** @internal */\nexport class _WebAudioSoundSource extends AbstractSoundSource {\n    private _spatial: Nullable<_SpatialWebAudio> = null;\n    private readonly _spatialAutoUpdate: boolean = true;\n    private readonly _spatialMinUpdateTime: number = 0;\n    private _stereo: Nullable<_StereoAudio> = null;\n\n    protected _subGraph: _WebAudioBusAndSoundSubGraph;\n    protected _webAudioNode: AudioNode;\n\n    /** @internal */\n    public _audioContext: AudioContext | OfflineAudioContext;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(name: string, webAudioNode: AudioNode, engine: _WebAudioEngine, options: Partial<ISoundSourceOptions>) {\n        super(name, engine);\n\n        if (typeof options.spatialAutoUpdate === \"boolean\") {\n            this._spatialAutoUpdate = options.spatialAutoUpdate;\n        }\n\n        if (typeof options.spatialMinUpdateTime === \"number\") {\n            this._spatialMinUpdateTime = options.spatialMinUpdateTime;\n        }\n\n        this._audioContext = this.engine._audioContext;\n        this._webAudioNode = webAudioNode;\n\n        this._subGraph = new _WebAudioSoundSource._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(options: Partial<ISoundSourceOptions>): Promise<void> {\n        if (options.outBus) {\n            this.outBus = options.outBus;\n        } else if (options.outBusAutoDefault !== false) {\n            await this.engine.isReadyPromise;\n            this.outBus = this.engine.defaultMainBus;\n        }\n\n        await this._subGraph.initAsync(options);\n\n        if (_HasSpatialAudioOptions(options)) {\n            this._initSpatialProperty();\n        }\n\n        this.engine._addNode(this);\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._webAudioNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    /** @internal */\n    public override get spatial(): _SpatialAudio {\n        if (this._spatial) {\n            return this._spatial;\n        }\n        return this._initSpatialProperty();\n    }\n\n    /** @internal */\n    public override get stereo(): _StereoAudio {\n        return this._stereo ?? (this._stereo = new _StereoAudio(this._subGraph));\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._spatial?.dispose();\n        this._spatial = null;\n\n        this._stereo = null;\n\n        this._subGraph.dispose();\n\n        this.engine._removeNode(this);\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioSoundSource\";\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initSpatialProperty(): _SpatialAudio {\n        if (!this._spatial) {\n            this._spatial = new _SpatialWebAudio(this._subGraph, this._spatialAutoUpdate, this._spatialMinUpdateTime);\n        }\n\n        return this._spatial;\n    }\n\n    private static _SubGraph = class extends _WebAudioBusAndSoundSubGraph {\n        protected override _owner: _WebAudioSoundSource;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n\n        protected get _upstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._upstreamNodes ?? null;\n        }\n\n        protected override _onSubNodesChanged(): void {\n            super._onSubNodesChanged();\n\n            this._owner._inNode.disconnect();\n\n            if (this._owner._subGraph._inNode) {\n                this._owner._inNode.connect(this._owner._subGraph._inNode);\n            }\n        }\n    };\n}\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,mBAAmB,EAAE,MAAM,sCAAsC,CAAC;AAC3E,OAAO,EAAE,uBAAuB,EAAE,MAAM,qDAAqD,CAAC;AAE9F,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;;;;;;AAK7D,MAAO,oBAAqB,mMAAQ,sBAAmB;IAiCzD,cAAA,EAAgB,CACT,KAAK,CAAC,UAAU,CAAC,OAAqC,EAAA;QACzD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,CAAC,MAAM,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,oNAAI,0BAAA,AAAuB,EAAC,OAAO,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAoB,OAAO,GAAA;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAoB,MAAM,GAAA;YACf;QAAP,4BAAW,CAAC,OAAO,IAAI,qDAAC,IAAI,CAAC,OAAO,GAAG,uMAAI,eAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;8BAEZ,CAAC,QAAQ,mDAAb,eAAe,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAEkB,QAAQ,CAAC,IAAqB,EAAA;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACX,CAAC,QAAQ,mDAAb,eAAe,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB,EAAA;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACX,CAAC,QAAQ,mDAAb,eAAe,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,qMAAI,oBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9G,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAjHD,cAAA,EAAgB,CAChB,YAAmB,IAAY,EAAE,YAAuB,EAAE,MAAuB,EAAE,OAAqC,CAAA;QACpH,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAhBhB,IAAA,CAAA,QAAQ,GAA+B,IAAI,CAAC;QACnC,IAAA,CAAA,kBAAkB,GAAY,IAAI,CAAC;QACnC,IAAA,CAAA,qBAAqB,GAAW,CAAC,CAAC;QAC3C,IAAA,CAAA,OAAO,GAA2B,IAAI,CAAC;QAe3C,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;;AAmGc,qBAAA,SAAS,GAAG,KAAM,kNAAQ,+BAA4B;IAGjE,IAAc,gBAAgB,GAAA;;QAC1B,4CAAW,CAAC,MAAM,CAAC,gBAAgB,yDAA5B,gCAAgC,IAAI,CAAC;IAChD,CAAC;IAED,IAAc,cAAc,GAAA;;QACxB,0CAAW,CAAC,MAAM,CAAC,cAAc,uDAA1B,8BAA8B,IAAI,CAAC;IAC9C,CAAC;IAEkB,kBAAkB,GAAA;QACjC,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC;CACJ,AApBuB,CAoBtB", "debugId": null}}, {"offset": {"line": 3713, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/abstractSoundInstance.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/abstractSoundInstance.ts"], "sourcesContent": ["import { Observable } from \"../../Misc/observable\";\nimport { SoundState } from \"../soundState\";\nimport { AbstractAudioNode, AudioNodeType } from \"./abstractAudioNode\";\nimport type { AbstractSound, IAbstractSoundPlayOptions, IAbstractSoundPlayOptionsBase } from \"./abstractSound\";\n\n/**\n * Options for creating a sound instance.\n * @internal\n * */\nexport interface IAbstractSoundInstanceOptions extends IAbstractSoundPlayOptionsBase {}\n\n/** @internal */\nexport abstract class _AbstractSoundInstance extends AbstractAudioNode {\n    protected _sound: AbstractSound;\n    protected _state: SoundState = SoundState.Stopped;\n\n    /** Observable triggered when the sound instance's playback ends */\n    public readonly onEndedObservable = new Observable<_AbstractSoundInstance>();\n\n    /** Observable triggered if the sound instance encounters an error and can not be played */\n    public readonly onErrorObservable = new Observable<any>();\n\n    /** Observable triggered when the sound instance's state changes */\n    public readonly onStateChangedObservable = new Observable<_AbstractSoundInstance>();\n\n    protected abstract readonly _options: IAbstractSoundInstanceOptions;\n\n    protected constructor(sound: AbstractSound) {\n        super(sound.engine, AudioNodeType.HAS_OUTPUTS);\n\n        this._sound = sound;\n    }\n\n    public abstract currentTime: number;\n\n    public abstract readonly startTime: number;\n\n    /** The playback state of the sound instance */\n    public get state(): SoundState {\n        return this._state;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n        this.stop();\n        this.onEndedObservable.clear();\n        this.onStateChangedObservable.clear();\n    }\n\n    public abstract play(options: Partial<IAbstractSoundPlayOptions>): void;\n    public abstract pause(): void;\n    public abstract resume(): void;\n    public abstract stop(): void;\n\n    protected _setState(value: SoundState) {\n        if (this._state === value) {\n            return;\n        }\n\n        this._state = value;\n        this.onStateChangedObservable.notifyObservers(this);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,iBAAiB,EAAiB,MAAM,qBAAqB,CAAC;;;AAUjE,MAAgB,sBAAuB,iMAAQ,oBAAiB;IAyBlE,6CAAA,EAA+C,CAC/C,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;IAC1C,CAAC;IAOS,SAAS,CAAC,KAAiB,EAAA;QACjC,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAnCD,YAAsB,KAAoB,CAAA;QACtC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAA,EAAA,6BAAA,GAA4B,CAAC;QAdzC,IAAA,CAAA,MAAM,GAAA,EAAA,sBAAA,GAAkC;QAElD,iEAAA,EAAmE,CACnD,IAAA,CAAA,iBAAiB,GAAG,iKAAI,aAAU,EAA0B,CAAC;QAE7E,yFAAA,EAA2F,CAC3E,IAAA,CAAA,iBAAiB,GAAG,iKAAI,aAAU,EAAO,CAAC;QAE1D,iEAAA,EAAmE,CACnD,IAAA,CAAA,wBAAwB,GAAG,gKAAI,cAAU,EAA0B,CAAC;QAOhF,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;CAgCJ", "debugId": null}}, {"offset": {"line": 3750, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/staticSoundInstance.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/staticSoundInstance.ts"], "sourcesContent": ["import type { IAbstractSoundInstanceOptions } from \"./abstractSoundInstance\";\nimport { _AbstractSoundInstance } from \"./abstractSoundInstance\";\nimport type { IStaticSoundOptionsBase, IStaticSoundPlayOptions, IStaticSoundStopOptions } from \"./staticSound\";\n\n/**\n * Options for creating a static sound instance.\n * @internal\n */\nexport interface IStaticSoundInstanceOptions extends IAbstractSoundInstanceOptions, IStaticSoundOptionsBase {}\n\n/** @internal */\nexport abstract class _StaticSoundInstance extends _AbstractSoundInstance {\n    protected abstract override readonly _options: IStaticSoundInstanceOptions;\n\n    public abstract pitch: number;\n    public abstract playbackRate: number;\n\n    public abstract override play(options: Partial<IStaticSoundPlayOptions>): void;\n    public abstract override stop(options?: Partial<IStaticSoundStopOptions>): void;\n}\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,sBAAsB,EAAE,MAAM,yBAAyB,CAAC;;AAU3D,MAAgB,oBAAqB,qMAAQ,yBAAsB;CAQxE", "debugId": null}}, {"offset": {"line": 3761, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioStaticSound.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/webAudioStaticSound.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { AbstractAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type { IStaticSoundCloneOptions, IStaticSoundOptions, IStaticSoundPlayOptions, IStaticSoundStopOptions, IStaticSoundStoredOptions } from \"../abstractAudio/staticSound\";\nimport { StaticSound } from \"../abstractAudio/staticSound\";\nimport type { IStaticSoundBufferCloneOptions, IStaticSoundBufferOptions } from \"../abstractAudio/staticSoundBuffer\";\nimport { StaticSoundBuffer } from \"../abstractAudio/staticSoundBuffer\";\nimport type { IStaticSoundInstanceOptions } from \"../abstractAudio/staticSoundInstance\";\nimport { _StaticSoundInstance } from \"../abstractAudio/staticSoundInstance\";\nimport { _HasSpatialAudioOptions } from \"../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { _SpatialAudio } from \"../abstractAudio/subProperties/spatialAudio\";\nimport { _StereoAudio } from \"../abstractAudio/subProperties/stereoAudio\";\nimport { _CleanUrl, _FileExtensionRegex } from \"../audioUtils\";\nimport { SoundState } from \"../soundState\";\nimport { _WebAudioParameterComponent } from \"./components/webAudioParameterComponent\";\nimport { _WebAudioBusAndSoundSubGraph } from \"./subNodes/webAudioBusAndSoundSubGraph\";\nimport { _SpatialWebAudio } from \"./subProperties/spatialWebAudio\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioOutNode, IWebAudioSuperNode } from \"./webAudioNode\";\n\ntype StaticSoundSourceType = ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[];\n\n/** @internal */\nexport class _WebAudioStaticSound extends StaticSound implements IWebAudioSuperNode {\n    private _buffer: _WebAudioStaticSoundBuffer;\n    private _spatial: Nullable<_SpatialWebAudio> = null;\n    private readonly _spatialAutoUpdate: boolean = true;\n    private readonly _spatialMinUpdateTime: number = 0;\n    private _stereo: Nullable<_StereoAudio> = null;\n\n    protected override readonly _options: IStaticSoundStoredOptions;\n    protected _subGraph: _WebAudioBusAndSoundSubGraph;\n\n    /** @internal */\n    public _audioContext: AudioContext | OfflineAudioContext;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(name: string, engine: _WebAudioEngine, options: Partial<IStaticSoundOptions>) {\n        super(name, engine);\n\n        if (typeof options.spatialAutoUpdate === \"boolean\") {\n            this._spatialAutoUpdate = options.spatialAutoUpdate;\n        }\n\n        if (typeof options.spatialMinUpdateTime === \"number\") {\n            this._spatialMinUpdateTime = options.spatialMinUpdateTime;\n        }\n\n        this._options = {\n            autoplay: options.autoplay ?? false,\n            duration: options.duration ?? 0,\n            loop: options.loop ?? false,\n            loopEnd: options.loopEnd ?? 0,\n            loopStart: options.loopStart ?? 0,\n            maxInstances: options.maxInstances ?? Infinity,\n            pitch: options.pitch ?? 0,\n            playbackRate: options.playbackRate ?? 1,\n            startOffset: options.startOffset ?? 0,\n        };\n\n        this._subGraph = new _WebAudioStaticSound._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(source: StaticSoundSourceType, options: Partial<IStaticSoundOptions>): Promise<void> {\n        this._audioContext = this.engine._audioContext;\n\n        if (source instanceof _WebAudioStaticSoundBuffer) {\n            this._buffer = source;\n        } else if (typeof source === \"string\" || Array.isArray(source) || source instanceof ArrayBuffer || source instanceof AudioBuffer) {\n            this._buffer = (await this.engine.createSoundBufferAsync(source, options)) as _WebAudioStaticSoundBuffer;\n        }\n\n        if (options.outBus) {\n            this.outBus = options.outBus;\n        } else if (options.outBusAutoDefault !== false) {\n            await this.engine.isReadyPromise;\n            this.outBus = this.engine.defaultMainBus;\n        }\n\n        await this._subGraph.initAsync(options);\n\n        if (_HasSpatialAudioOptions(options)) {\n            this._initSpatialProperty();\n        }\n\n        if (options.autoplay) {\n            this.play();\n        }\n\n        this.engine._addNode(this);\n    }\n\n    /** @internal */\n    public get buffer(): _WebAudioStaticSoundBuffer {\n        return this._buffer;\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._subGraph._inNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    /** @internal */\n    public override get spatial(): _SpatialAudio {\n        if (this._spatial) {\n            return this._spatial;\n        }\n        return this._initSpatialProperty();\n    }\n\n    /** @internal */\n    public override get stereo(): _StereoAudio {\n        return this._stereo ?? (this._stereo = new _StereoAudio(this._subGraph));\n    }\n\n    /** @internal */\n    public override async cloneAsync(options: Nullable<Partial<IStaticSoundCloneOptions>> = null): Promise<StaticSound> {\n        const clone = await this.engine.createSoundAsync(this.name, options?.cloneBuffer ? this.buffer.clone() : this.buffer, this._options);\n\n        clone.outBus = options?.outBus ? options.outBus : this.outBus;\n\n        return clone;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._spatial?.dispose();\n        this._spatial = null;\n\n        this._stereo = null;\n\n        this._subGraph.dispose();\n\n        this.engine._removeNode(this);\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioStaticSound\";\n    }\n\n    protected _createInstance(): _WebAudioStaticSoundInstance {\n        return new _WebAudioStaticSoundInstance(this, this._options);\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initSpatialProperty(): _SpatialAudio {\n        if (!this._spatial) {\n            this._spatial = new _SpatialWebAudio(this._subGraph, this._spatialAutoUpdate, this._spatialMinUpdateTime);\n        }\n\n        return this._spatial;\n    }\n\n    private static _SubGraph = class extends _WebAudioBusAndSoundSubGraph {\n        protected override _owner: _WebAudioStaticSound;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n\n        protected get _upstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._upstreamNodes ?? null;\n        }\n    };\n}\n\n/** @internal */\nexport class _WebAudioStaticSoundBuffer extends StaticSoundBuffer {\n    /** @internal */\n    public _audioBuffer: AudioBuffer;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n    }\n\n    public async _initAsync(source: StaticSoundSourceType, options: Partial<IStaticSoundBufferOptions>): Promise<void> {\n        if (source instanceof AudioBuffer) {\n            this._audioBuffer = source;\n        } else if (typeof source === \"string\") {\n            await this._initFromUrlAsync(source);\n        } else if (Array.isArray(source)) {\n            await this._initFromUrlsAsync(source, options.skipCodecCheck ?? false);\n        } else if (source instanceof ArrayBuffer) {\n            await this._initFromArrayBufferAsync(source);\n        }\n    }\n\n    /** @internal */\n    public get channelCount(): number {\n        return this._audioBuffer.numberOfChannels;\n    }\n\n    /** @internal */\n    public get duration(): number {\n        return this._audioBuffer.duration;\n    }\n\n    /** @internal */\n    public get length(): number {\n        return this._audioBuffer.length;\n    }\n\n    /** @internal */\n    public get sampleRate(): number {\n        return this._audioBuffer.sampleRate;\n    }\n\n    /** @internal */\n    public override clone(options: Nullable<Partial<IStaticSoundBufferCloneOptions>> = null): StaticSoundBuffer {\n        const audioBuffer = new AudioBuffer({\n            length: this._audioBuffer.length,\n            numberOfChannels: this._audioBuffer.numberOfChannels,\n            sampleRate: this._audioBuffer.sampleRate,\n        });\n\n        for (let i = 0; i < this._audioBuffer.numberOfChannels; i++) {\n            audioBuffer.copyToChannel(this._audioBuffer.getChannelData(i), i);\n        }\n\n        const buffer = new _WebAudioStaticSoundBuffer(this.engine);\n        buffer._audioBuffer = audioBuffer;\n        buffer.name = options?.name ? options.name : this.name;\n\n        return buffer;\n    }\n\n    private async _initFromArrayBufferAsync(arrayBuffer: ArrayBuffer): Promise<void> {\n        this._audioBuffer = await this.engine._audioContext.decodeAudioData(arrayBuffer);\n    }\n\n    private async _initFromUrlAsync(url: string): Promise<void> {\n        url = _CleanUrl(url);\n        await this._initFromArrayBufferAsync(await (await fetch(url)).arrayBuffer());\n    }\n\n    private async _initFromUrlsAsync(urls: string[], skipCodecCheck: boolean): Promise<void> {\n        for (const url of urls) {\n            if (skipCodecCheck) {\n                // eslint-disable-next-line no-await-in-loop\n                await this._initFromUrlAsync(url);\n            } else {\n                const matches = url.match(_FileExtensionRegex);\n                const format = matches?.at(1);\n                if (format && this.engine.isFormatValid(format)) {\n                    try {\n                        // eslint-disable-next-line no-await-in-loop\n                        await this._initFromUrlAsync(url);\n                    } catch {\n                        if (format && 0 < format.length) {\n                            this.engine.flagInvalidFormat(format);\n                        }\n                    }\n                }\n            }\n\n            if (this._audioBuffer) {\n                break;\n            }\n        }\n    }\n}\n\n/** @internal */\nclass _WebAudioStaticSoundInstance extends _StaticSoundInstance implements IWebAudioOutNode {\n    private _enginePlayTime: number = 0;\n    private _enginePauseTime: number = 0;\n    private _isConnected: boolean = false;\n    private _pitch: Nullable<_WebAudioParameterComponent> = null;\n    private _playbackRate: Nullable<_WebAudioParameterComponent> = null;\n    private _sourceNode: Nullable<AudioBufferSourceNode> = null;\n    private _volumeNode: GainNode;\n\n    protected override readonly _options: IStaticSoundInstanceOptions;\n    protected override _sound: _WebAudioStaticSound;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    public constructor(sound: _WebAudioStaticSound, options: IStaticSoundInstanceOptions) {\n        super(sound);\n\n        this._options = options;\n\n        this._volumeNode = new GainNode(sound._audioContext);\n        this._initSourceNode();\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._pitch?.dispose();\n        this._playbackRate?.dispose();\n\n        this._sourceNode = null;\n\n        this.stop();\n\n        this._deinitSourceNode();\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    }\n\n    /** @internal */\n    public get currentTime(): number {\n        if (this._state === SoundState.Stopped) {\n            return 0;\n        }\n\n        const timeSinceLastStart = this._state === SoundState.Paused ? 0 : this.engine.currentTime - this._enginePlayTime;\n        return this._enginePauseTime + timeSinceLastStart + this._options.startOffset;\n    }\n\n    public set currentTime(value: number) {\n        const restart = this._state === SoundState.Starting || this._state === SoundState.Started;\n\n        if (restart) {\n            this.stop();\n            this._deinitSourceNode();\n        }\n\n        this._options.startOffset = value;\n\n        if (restart) {\n            this.play();\n        }\n    }\n\n    public get _outNode(): Nullable<AudioNode> {\n        return this._volumeNode;\n    }\n\n    /** @internal */\n    public set pitch(value: number) {\n        this._pitch?.setTargetValue(value);\n    }\n\n    /** @internal */\n    public set playbackRate(value: number) {\n        this._playbackRate?.setTargetValue(value);\n    }\n\n    /** @internal */\n    public get startTime(): number {\n        if (this._state === SoundState.Stopped) {\n            return 0;\n        }\n\n        return this._enginePlayTime;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioStaticSoundInstance\";\n    }\n\n    /** @internal */\n    public play(options: Partial<IStaticSoundPlayOptions> = {}): void {\n        if (this._state === SoundState.Started) {\n            return;\n        }\n\n        if (options.duration !== undefined) {\n            this._options.duration = options.duration;\n        }\n        if (options.loop !== undefined) {\n            this._options.loop = options.loop;\n        }\n        if (options.loopStart !== undefined) {\n            this._options.loopStart = options.loopStart;\n        }\n        if (options.loopEnd !== undefined) {\n            this._options.loopEnd = options.loopEnd;\n        }\n        if (options.startOffset !== undefined) {\n            this._options.startOffset = options.startOffset;\n        }\n\n        let startOffset = this._options.startOffset;\n\n        if (this._state === SoundState.Paused) {\n            startOffset += this.currentTime;\n            startOffset %= this._sound.buffer.duration;\n        }\n\n        this._enginePlayTime = this.engine.currentTime + (options.waitTime ?? 0);\n\n        this._volumeNode.gain.value = options.volume ?? 1;\n\n        this._initSourceNode();\n\n        if (this.engine.state === \"running\") {\n            this._setState(SoundState.Started);\n            this._sourceNode?.start(this._enginePlayTime, startOffset, this._options.duration > 0 ? this._options.duration : undefined);\n        } else if (this._options.loop) {\n            this._setState(SoundState.Starting);\n            this.engine.stateChangedObservable.add(this._onEngineStateChanged);\n        }\n    }\n\n    /** @internal */\n    public pause(): void {\n        if (this._state === SoundState.Paused) {\n            return;\n        }\n\n        this._setState(SoundState.Paused);\n        this._enginePauseTime += this.engine.currentTime - this._enginePlayTime;\n\n        this._sourceNode?.stop();\n        this._deinitSourceNode();\n    }\n\n    /** @internal */\n    public resume(): void {\n        if (this._state === SoundState.Paused) {\n            this.play();\n        }\n    }\n\n    /** @internal */\n    public stop(options: Partial<IStaticSoundStopOptions> = {}): void {\n        if (this._state === SoundState.Stopped) {\n            return;\n        }\n\n        this._setState(SoundState.Stopped);\n\n        const engineStopTime = this.engine.currentTime + (options.waitTime ?? 0);\n        this._sourceNode?.stop(engineStopTime);\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    }\n\n    protected override _connect(node: AbstractAudioNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the sound's subgraph.\n        if (node instanceof _WebAudioStaticSound && node._inNode) {\n            this._outNode?.connect(node._inNode);\n            this._isConnected = true;\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: AbstractAudioNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node instanceof _WebAudioStaticSound && node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n            this._isConnected = false;\n        }\n\n        return true;\n    }\n\n    protected _onEnded = () => {\n        this._enginePlayTime = 0;\n\n        this.onEndedObservable.notifyObservers(this);\n        this._deinitSourceNode();\n    };\n\n    private _deinitSourceNode(): void {\n        if (!this._sourceNode) {\n            return;\n        }\n\n        if (this._isConnected && !this._disconnect(this._sound)) {\n            throw new Error(\"Disconnect failed\");\n        }\n\n        this._sourceNode.disconnect(this._volumeNode);\n        this._sourceNode.removeEventListener(\"ended\", this._onEnded);\n\n        this._sourceNode = null;\n    }\n\n    private _initSourceNode(): void {\n        if (!this._sourceNode) {\n            this._sourceNode = new AudioBufferSourceNode(this._sound._audioContext, { buffer: this._sound.buffer._audioBuffer });\n\n            this._sourceNode.addEventListener(\"ended\", this._onEnded, { once: true });\n            this._sourceNode.connect(this._volumeNode);\n\n            if (!this._connect(this._sound)) {\n                throw new Error(\"Connect failed\");\n            }\n\n            this._pitch = new _WebAudioParameterComponent(this.engine, this._sourceNode.detune);\n            this._playbackRate = new _WebAudioParameterComponent(this.engine, this._sourceNode.playbackRate);\n        }\n\n        const node = this._sourceNode;\n        node.detune.value = this._sound.pitch;\n        node.loop = this._options.loop;\n        node.loopEnd = this._options.loopEnd;\n        node.loopStart = this._options.loopStart;\n        node.playbackRate.value = this._sound.playbackRate;\n    }\n\n    private _onEngineStateChanged = () => {\n        if (this.engine.state !== \"running\") {\n            return;\n        }\n\n        if (this._options.loop && this.state === SoundState.Starting) {\n            this.play();\n        }\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    };\n}\n"], "names": [], "mappings": ";;;;AAGA,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAE3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAC;AAEvE,OAAO,EAAE,oBAAoB,EAAE,MAAM,sCAAsC,CAAC;AAC5E,OAAO,EAAE,uBAAuB,EAAE,MAAM,qDAAqD,CAAC;AAE9F,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,EAAE,SAAS,EAAE,mBAAmB,EAAE,MAAM,eAAe,CAAC;AAE/D,OAAO,EAAE,2BAA2B,EAAE,MAAM,yCAAyC,CAAC;AACtF,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;;;;;;;;;;AAO7D,MAAO,oBAAqB,2LAAQ,cAAW;IA2CjD,cAAA,EAAgB,CACT,KAAK,CAAC,UAAU,CAAC,MAA6B,EAAE,OAAqC,EAAA;QACxF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAE/C,IAAI,MAAM,YAAY,0BAA0B,EAAE,CAAC;YAC/C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAC1B,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,MAAM,YAAY,WAAW,IAAI,MAAM,YAAY,WAAW,EAAE,CAAC;YAC/H,IAAI,CAAC,OAAO,GAAI,AAAD,MAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,CAAC,CAA+B,CAAC;QAC7G,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,CAAC,MAAM,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,oNAAI,0BAAA,AAAuB,EAAC,OAAO,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAoB,OAAO,GAAA;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAoB,MAAM,GAAA;;QACtB,QAAO,oBAAI,CAAC,OAAO,IAAI,qDAAC,IAAI,CAAC,OAAO,GAAG,sMAAI,gBAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,cAAA,EAAgB,CACA,KAAK,CAAC,UAAU,GAA4D;sBAA3D,iEAAuD,IAAI;QACxF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,qDAAE,OAAO,CAAE,WAAW,CAAC,CAAC,EAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAErI,KAAK,CAAC,MAAM,sDAAG,OAAO,CAAE,MAAM,CAAC,CAAC,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAE9D,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;8BAEZ,CAAC,QAAQ,mDAAb,eAAe,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAES,eAAe,GAAA;QACrB,OAAO,IAAI,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjE,CAAC;IAEkB,QAAQ,CAAC,IAAqB,EAAA;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACX,CAAC,QAAQ,mDAAb,eAAe,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB,EAAA;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;8BACf,IAAI,CAAC,QAAQ,kEAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,sMAAI,mBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9G,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAxJD,cAAA,EAAgB,CAChB,YAAmB,IAAY,EAAE,MAAuB,EAAE,OAAqC,CAAA;QAC3F,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAhBhB,IAAA,CAAA,QAAQ,GAA+B,IAAI,CAAC;QACnC,IAAA,CAAA,kBAAkB,GAAY,IAAI,CAAC;QACnC,IAAA,CAAA,qBAAqB,GAAW,CAAC,CAAC;QAC3C,IAAA,CAAA,OAAO,GAA2B,IAAI,CAAC;QAe3C,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAC9D,CAAC;+BAIa,OAAO,YACX,OAAO,QACJ,OAAO,+BAEF,OAAO,gBACd,OAAO,SACA,OAAO;QARzB,IAAI,CAAC,QAAQ,GAAG;YACZ,QAAQ,+BAAU,QAAQ,6CAAhB,OAAO,aAAa,KAAK;YACnC,QAAQ,+BAAU,QAAQ,iEAAI,CAAC;YAC/B,IAAI,2BAAU,IAAI,yDAAI,KAAK;YAC3B,OAAO,8BAAU,OAAO,+DAAI,CAAC;YAC7B,SAAS,wBAAE,OAAO,CAAC,SAAS,mEAAI,CAAC;YACjC,YAAY,mCAAU,YAAY,yEAAI,QAAQ;YAC9C,KAAK,4BAAU,KAAK,2DAAI,CAAC;YACzB,YAAY,mCAAU,YAAY,yEAAI,CAAC;YACvC,WAAW,kCAAU,WAAW,cAAnB,OAAO,kDAAgB,CAAC;SACxC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;;AAiIc,qBAAA,SAAS,GAAG,KAAM,kNAAQ,+BAA4B;IAGjE,IAAc,gBAAgB,GAAA;;QAC1B,OAAO,qCAAI,CAAC,MAAM,CAAC,gBAAgB,yFAAI,IAAI,CAAC;IAChD,CAAC;IAED,IAAc,cAAc,GAAA;;QACxB,0CAAW,CAAC,MAAM,CAAC,cAAc,uDAA1B,8BAA8B,IAAI,CAAC;IAC9C,CAAC;CATmB,AAUvB,CAAC;AAIA,MAAO,0BAA2B,iMAAQ,oBAAiB;IAYtD,KAAK,CAAC,UAAU,CAAC,MAA6B,EAAE,OAA2C,EAAA;QAC9F,IAAI,MAAM,YAAY,WAAW,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC/B,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBACO,OAAO;YAA7C,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,qCAAU,cAAc,6EAAI,KAAK,CAAC,CAAC;QAC3E,CAAC,MAAM,IAAI,MAAM,YAAY,WAAW,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC;IAC9C,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;IACpC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;IACxC,CAAC;IAED,cAAA,EAAgB,CACA,KAAK,GAAkE;sBAAjE,iEAA6D,IAAI;QACnF,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC;YAChC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM;YAChC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB;YACpD,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,UAAU;SAC3C,CAAC,CAAC;QAEH,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAE,CAAC;YAC1D,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,CAAC,YAAY,GAAG,WAAW,CAAC;QAClC,MAAM,CAAC,IAAI,sDAAG,OAAO,CAAE,IAAI,CAAC,CAAC,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAEvD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,WAAwB,EAAA;QAC5D,IAAI,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IACrF,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,GAAW,EAAA;QACvC,GAAG,uKAAG,YAAA,AAAS,EAAC,GAAG,CAAC,CAAC;QACrB,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;IACjF,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,IAAc,EAAE,cAAuB,EAAA;QACpE,KAAK,MAAM,GAAG,IAAI,IAAI,CAAE,CAAC;YACrB,IAAI,cAAc,EAAE,CAAC;gBACjB,4CAA4C;gBAC5C,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC,MAAM,CAAC;gBACJ,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,iKAAC,sBAAmB,CAAC,CAAC;gBAC/C,MAAM,MAAM,uBAAG,OAAO,+BAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC9B,IAAI,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC9C,IAAI,CAAC;wBACD,4CAA4C;wBAC5C,MAAM,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;oBACtC,CAAC,CAAC,UAAM,CAAC;wBACL,IAAI,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;4BAC9B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;wBAC1C,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;IAzFD,cAAA,EAAgB,CAChB,YAAmB,MAAuB,CAAA;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;CAuFJ;AAED,cAAA,EAAgB,CAChB,MAAM,4BAA6B,mMAAQ,uBAAoB;IAwB3D,cAAA,EAAgB,CACA,OAAO,GAAA;;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;4BAEZ,CAAC,MAAM,cAAX,gDAAa,OAAO,EAAE,CAAC;SACvB,0BAAI,CAAC,aAAa,4EAAE,OAAO,EAAE,CAAC;QAE9B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAClF,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,KAAA,EAAA,qBAAA,EAAsB,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QAClH,OAAO,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IAClF,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,KAAA,EAAA,uBAAA,EAAwB,KAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,CAAC;QAE1F,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;QAElC,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;IACL,CAAC;IAED,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,KAAK,CAAC,KAAa,EAAA;;SAC1B,mBAAI,CAAC,MAAM,8DAAE,cAAc,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,YAAY,CAAC,KAAa,EAAA;;SACjC,0BAAI,CAAC,aAAa,4EAAE,cAAc,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,SAAS,GAAA;QAChB,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,8BAA8B,CAAC;IAC1C,CAAC;IAED,cAAA,EAAgB,CACT,IAAI,GAA+C;sBAA9C,iEAA4C,CAAA,CAAE;QACtD,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC9C,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAChD,CAAC;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC5C,CAAC;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACpD,CAAC;QAED,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAE5C,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,qBAAA,EAAsB,GAAE,CAAC;YACpC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAChC,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;QAC/C,CAAC;;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,8BAAS,QAAQ,6CAAhB,OAAO,aAAa,CAAC,CAAC,CAAC;;QAEzE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,8BAAW,MAAM,2CAAd,OAAO,WAAW,CAAC,CAAC;QAElD,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;;YAClC,IAAI,CAAC,SAAS,CAAA,EAAA,sBAAA,GAAoB,CAAC;qCAC/B,CAAC,WAAW,sDAAhB,kBAAkB,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAChI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAA,EAAA,uBAAA,GAAqB,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,GAAA;YAQR;QAPA,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,qBAAA,EAAsB,GAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAA,EAAA,qBAAA,GAAmB,CAAC;QAClC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;iCAEpE,CAAC,WAAW,wEAAE,IAAI,EAAE,CAAC;QACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED,cAAA,EAAgB,CACT,MAAM,GAAA;QACT,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,qBAAA,EAAsB,GAAE,CAAC;YACpC,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,IAAI,GAA+C;sBAA9C,iEAA4C,CAAA,CAAE;;QACtD,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAA,EAAA,sBAAA,GAAoB,CAAC;YAEe,OAAO;QAAzD,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,8BAAS,QAAQ,iEAAI,CAAC,CAAC,CAAC;iCACrE,CAAC,WAAW,sDAAhB,kBAAkB,IAAI,CAAC,cAAc,CAAC,CAAC;QAEvC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAClF,CAAC;IAEkB,QAAQ,CAAC,IAAuB,EAAA;QAC/C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,gGAAgG;QAChG,IAAI,IAAI,YAAY,oBAAoB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACnD,CAAC,QAAQ,mDAAb,eAAe,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAuB,EAAA;QAClD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,YAAY,oBAAoB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACnD,CAAC,QAAQ,mDAAb,eAAe,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IASO,iBAAiB,GAAA;QACrB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE7D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAEO,eAAe,GAAA;QACnB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;gBAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY;YAAA,CAAE,CAAC,CAAC;YAErH,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;gBAAE,IAAI,EAAE,IAAI;YAAA,CAAE,CAAC,CAAC;YAC1E,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE3C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACtC,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,8MAAI,8BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACpF,IAAI,CAAC,aAAa,GAAG,8MAAI,8BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QACrG,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IACvD,CAAC;IAtOD,YAAmB,KAA2B,EAAE,OAAoC,CAAA;QAChF,KAAK,CAAC,KAAK,CAAC,CAAC;QAfT,IAAA,CAAA,eAAe,GAAW,CAAC,CAAC;QAC5B,IAAA,CAAA,gBAAgB,GAAW,CAAC,CAAC;QAC7B,IAAA,CAAA,YAAY,GAAY,KAAK,CAAC;QAC9B,IAAA,CAAA,MAAM,GAA0C,IAAI,CAAC;QACrD,IAAA,CAAA,aAAa,GAA0C,IAAI,CAAC;QAC5D,IAAA,CAAA,WAAW,GAAoC,IAAI,CAAC;QAoMlD,IAAA,CAAA,QAAQ,GAAG,GAAG,EAAE;YACtB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YAEzB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,CAAC;QAwCM,IAAA,CAAA,qBAAqB,GAAG,GAAG,EAAE;YACjC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAClC,OAAO;YACX,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;gBAC3D,IAAI,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAClF,CAAC,CAAC;QA/OE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,WAAW,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;CA4OJ", "debugId": null}}, {"offset": {"line": 4191, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/abstractAudio/streamingSoundInstance.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/abstractAudio/streamingSoundInstance.ts"], "sourcesContent": ["import { Observable } from \"../../Misc/observable\";\nimport type { IAbstractSoundInstanceOptions } from \"./abstractSoundInstance\";\nimport { _AbstractSoundInstance } from \"./abstractSoundInstance\";\nimport type { IStreamingSoundOptionsBase, StreamingSound } from \"./streamingSound\";\n\n/**\n * Options for creating streaming sound instance.\n * @internal\n */\nexport interface IStreamingSoundInstanceOptions extends IAbstractSoundInstanceOptions, IStreamingSoundOptionsBase {}\n\n/** @internal */\nexport abstract class _StreamingSoundInstance extends _AbstractSoundInstance {\n    private _rejectPreloadedProimse: (reason?: any) => void;\n    private _resolvePreloadedPromise: () => void;\n\n    protected abstract override readonly _options: IStreamingSoundInstanceOptions;\n\n    /** @internal */\n    public readonly onReadyObservable = new Observable<_StreamingSoundInstance>();\n\n    /** @internal */\n    public readonly preloadedPromise = new Promise<void>((resolve, reject) => {\n        this._rejectPreloadedProimse = reject;\n        this._resolvePreloadedPromise = resolve;\n    });\n\n    protected constructor(sound: StreamingSound) {\n        super(sound);\n\n        this.onErrorObservable.add(this._rejectPreloadedProimse);\n        this.onReadyObservable.add(this._resolvePreloadedPromise);\n    }\n\n    /** @internal */\n    public set startOffset(value: number) {\n        this._options.startOffset = value;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this.onErrorObservable.clear();\n        this.onReadyObservable.clear();\n\n        this._resolvePreloadedPromise();\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,sBAAsB,EAAE,MAAM,yBAAyB,CAAC;;;AAU3D,MAAgB,uBAAwB,qMAAQ,yBAAsB;IAsBxE,cAAA,EAAgB,CAChB,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;IACtC,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAE/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IApBD,YAAsB,KAAqB,CAAA;QACvC,KAAK,CAAC,KAAK,CAAC,CAAC;QAVjB,cAAA,EAAgB,CACA,IAAA,CAAA,iBAAiB,GAAG,iKAAI,aAAU,EAA2B,CAAC;QAE9E,cAAA,EAAgB,CACA,IAAA,CAAA,gBAAgB,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrE,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC;YACtC,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC;QAC5C,CAAC,CAAC,CAAC;QAKC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACzD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC;CAgBJ", "debugId": null}}, {"offset": {"line": 4223, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioStreamingSound.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/webAudioStreamingSound.ts"], "sourcesContent": ["import { Logger } from \"../../Misc/logger\";\nimport { Tools } from \"../../Misc/tools\";\nimport type { Nullable } from \"../../types\";\nimport type { AbstractAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type {} from \"../abstractAudio/abstractSound\";\nimport type { IStreamingSoundOptions, IStreamingSoundPlayOptions, IStreamingSoundStoredOptions } from \"../abstractAudio/streamingSound\";\nimport { StreamingSound } from \"../abstractAudio/streamingSound\";\nimport { _StreamingSoundInstance } from \"../abstractAudio/streamingSoundInstance\";\nimport { _HasSpatialAudioOptions } from \"../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { _SpatialAudio } from \"../abstractAudio/subProperties/spatialAudio\";\nimport { _StereoAudio } from \"../abstractAudio/subProperties/stereoAudio\";\nimport { _CleanUrl } from \"../audioUtils\";\nimport { SoundState } from \"../soundState\";\nimport { _WebAudioBusAndSoundSubGraph } from \"./subNodes/webAudioBusAndSoundSubGraph\";\nimport { _SpatialWebAudio } from \"./subProperties/spatialWebAudio\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioOutNode, IWebAudioSuperNode } from \"./webAudioNode\";\n\ntype StreamingSoundSourceType = HTMLMediaElement | string | string[];\n\n/** @internal */\nexport class _WebAudioStreamingSound extends StreamingSound implements IWebAudioSuperNode {\n    private _spatial: Nullable<_SpatialAudio> = null;\n    private readonly _spatialAutoUpdate: boolean = true;\n    private readonly _spatialMinUpdateTime: number = 0;\n    private _stereo: Nullable<_StereoAudio> = null;\n\n    protected override readonly _options: IStreamingSoundStoredOptions;\n    protected _subGraph: _WebAudioBusAndSoundSubGraph;\n\n    /** @internal */\n    public _audioContext: AudioContext;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public _source: StreamingSoundSourceType;\n\n    /** @internal */\n    public constructor(name: string, engine: _WebAudioEngine, options: Partial<IStreamingSoundOptions>) {\n        super(name, engine);\n\n        if (typeof options.spatialAutoUpdate === \"boolean\") {\n            this._spatialAutoUpdate = options.spatialAutoUpdate;\n        }\n\n        if (typeof options.spatialMinUpdateTime === \"number\") {\n            this._spatialMinUpdateTime = options.spatialMinUpdateTime;\n        }\n\n        this._options = {\n            autoplay: options.autoplay ?? false,\n            loop: options.loop ?? false,\n            maxInstances: options.maxInstances ?? Infinity,\n            preloadCount: options.preloadCount ?? 1,\n            startOffset: options.startOffset ?? 0,\n        };\n\n        this._subGraph = new _WebAudioStreamingSound._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(source: StreamingSoundSourceType, options: Partial<IStreamingSoundOptions>): Promise<void> {\n        const audioContext = this.engine._audioContext;\n\n        if (!(audioContext instanceof AudioContext)) {\n            throw new Error(\"Unsupported audio context type.\");\n        }\n\n        this._audioContext = audioContext;\n        this._source = source;\n\n        if (options.outBus) {\n            this.outBus = options.outBus;\n        } else if (options.outBusAutoDefault !== false) {\n            await this.engine.isReadyPromise;\n            this.outBus = this.engine.defaultMainBus;\n        }\n\n        await this._subGraph.initAsync(options);\n\n        if (_HasSpatialAudioOptions(options)) {\n            this._initSpatialProperty();\n        }\n\n        if (this.preloadCount) {\n            await this.preloadInstancesAsync(this.preloadCount);\n        }\n\n        if (options.autoplay) {\n            this.play(options);\n        }\n\n        this.engine._addNode(this);\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._subGraph._inNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    /** @internal */\n    public override get spatial(): _SpatialAudio {\n        if (this._spatial) {\n            return this._spatial;\n        }\n        return this._initSpatialProperty();\n    }\n\n    /** @internal */\n    public override get stereo(): _StereoAudio {\n        return this._stereo ?? (this._stereo = new _StereoAudio(this._subGraph));\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._spatial = null;\n        this._stereo = null;\n\n        this._subGraph.dispose();\n\n        this.engine._removeNode(this);\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioStreamingSound\";\n    }\n\n    protected _createInstance(): _WebAudioStreamingSoundInstance {\n        return new _WebAudioStreamingSoundInstance(this, this._options);\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initSpatialProperty(): _SpatialAudio {\n        if (!this._spatial) {\n            this._spatial = new _SpatialWebAudio(this._subGraph, this._spatialAutoUpdate, this._spatialMinUpdateTime);\n        }\n\n        return this._spatial;\n    }\n\n    private static _SubGraph = class extends _WebAudioBusAndSoundSubGraph {\n        protected override _owner: _WebAudioStreamingSound;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n\n        protected get _upstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._upstreamNodes ?? null;\n        }\n    };\n}\n\n/** @internal */\nclass _WebAudioStreamingSoundInstance extends _StreamingSoundInstance implements IWebAudioOutNode {\n    private _currentTimeChangedWhilePaused = false;\n    private _enginePlayTime: number = Infinity;\n    private _enginePauseTime: number = 0;\n    private _isReady: boolean = false;\n    private _isReadyPromise: Promise<HTMLMediaElement> = new Promise((resolve, reject) => {\n        this._resolveIsReadyPromise = resolve;\n        this._rejectIsReadyPromise = reject;\n    });\n    private _mediaElement: HTMLMediaElement;\n    private _sourceNode: Nullable<MediaElementAudioSourceNode>;\n    private _volumeNode: GainNode;\n\n    protected override readonly _options: IStreamingSoundStoredOptions;\n    protected override _sound: _WebAudioStreamingSound;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    public constructor(sound: _WebAudioStreamingSound, options: IStreamingSoundStoredOptions) {\n        super(sound);\n\n        this._options = options;\n        this._volumeNode = new GainNode(sound._audioContext);\n\n        if (typeof sound._source === \"string\") {\n            this._initFromUrl(sound._source);\n        } else if (Array.isArray(sound._source)) {\n            this._initFromUrls(sound._source);\n        } else if (sound._source instanceof HTMLMediaElement) {\n            this._initFromMediaElement(sound._source);\n        }\n    }\n\n    /** @internal */\n    public get currentTime(): number {\n        if (this._state === SoundState.Stopped) {\n            return 0;\n        }\n\n        const timeSinceLastStart = this._state === SoundState.Paused ? 0 : this.engine.currentTime - this._enginePlayTime;\n        return this._enginePauseTime + timeSinceLastStart + this._options.startOffset;\n    }\n\n    public set currentTime(value: number) {\n        const restart = this._state === SoundState.Starting || this._state === SoundState.Started;\n\n        if (restart) {\n            this._mediaElement.pause();\n            this._setState(SoundState.Stopped);\n        }\n\n        this._options.startOffset = value;\n\n        if (restart) {\n            this.play({ startOffset: value });\n        } else if (this._state === SoundState.Paused) {\n            this._currentTimeChangedWhilePaused = true;\n        }\n    }\n\n    public get _outNode(): Nullable<AudioNode> {\n        return this._volumeNode;\n    }\n\n    /** @internal */\n    public get startTime(): number {\n        if (this._state === SoundState.Stopped) {\n            return 0;\n        }\n\n        return this._enginePlayTime;\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this.stop();\n\n        this._sourceNode?.disconnect(this._volumeNode);\n        this._sourceNode = null;\n\n        this._mediaElement.removeEventListener(\"error\", this._onError);\n        this._mediaElement.removeEventListener(\"ended\", this._onEnded);\n        this._mediaElement.removeEventListener(\"canplaythrough\", this._onCanPlayThrough);\n\n        for (const source of Array.from(this._mediaElement.children)) {\n            this._mediaElement.removeChild(source);\n        }\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n        this.engine.userGestureObservable.removeCallback(this._onUserGesture);\n    }\n\n    /** @internal */\n    public play(options: Partial<IStreamingSoundPlayOptions> = {}): void {\n        if (this._state === SoundState.Started) {\n            return;\n        }\n\n        if (options.loop !== undefined) {\n            this._options.loop = options.loop;\n        }\n        this._mediaElement.loop = this._options.loop;\n\n        let startOffset = options.startOffset;\n\n        if (this._currentTimeChangedWhilePaused) {\n            startOffset = this._options.startOffset;\n            this._currentTimeChangedWhilePaused = false;\n        } else if (this._state === SoundState.Paused) {\n            startOffset = this.currentTime + this._options.startOffset;\n        }\n\n        if (startOffset && startOffset > 0) {\n            this._mediaElement.currentTime = startOffset;\n        }\n\n        this._volumeNode.gain.value = options.volume ?? 1;\n\n        this._play();\n    }\n\n    /** @internal */\n    public pause(): void {\n        if (this._state !== SoundState.Starting && this._state !== SoundState.Started) {\n            return;\n        }\n\n        this._setState(SoundState.Paused);\n        this._enginePauseTime += this.engine.currentTime - this._enginePlayTime;\n\n        this._mediaElement.pause();\n    }\n\n    /** @internal */\n    public resume(): void {\n        if (this._state === SoundState.Paused) {\n            this.play();\n        } else if (this._currentTimeChangedWhilePaused) {\n            this.play();\n        }\n    }\n\n    /** @internal */\n    public override stop(): void {\n        if (this._state === SoundState.Stopped) {\n            return;\n        }\n\n        this._stop();\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioStreamingSoundInstance\";\n    }\n\n    protected override _connect(node: AbstractAudioNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the sound's subgraph.\n        if (node instanceof _WebAudioStreamingSound && node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: AbstractAudioNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node instanceof _WebAudioStreamingSound && node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initFromMediaElement(mediaElement: HTMLMediaElement): void {\n        Tools.SetCorsBehavior(mediaElement.currentSrc, mediaElement);\n\n        mediaElement.controls = false;\n        mediaElement.loop = this._options.loop;\n        mediaElement.preload = \"auto\";\n\n        mediaElement.addEventListener(\"canplaythrough\", this._onCanPlayThrough, { once: true });\n        mediaElement.addEventListener(\"ended\", this._onEnded, { once: true });\n        mediaElement.addEventListener(\"error\", this._onError, { once: true });\n\n        mediaElement.load();\n\n        this._sourceNode = new MediaElementAudioSourceNode(this._sound._audioContext, { mediaElement: mediaElement });\n        this._sourceNode.connect(this._volumeNode);\n\n        if (!this._connect(this._sound)) {\n            throw new Error(\"Connect failed\");\n        }\n\n        this._mediaElement = mediaElement;\n    }\n\n    private _initFromUrl(url: string): void {\n        const audio = new Audio(_CleanUrl(url));\n        this._initFromMediaElement(audio);\n    }\n\n    private _initFromUrls(urls: string[]): void {\n        const audio = new Audio();\n\n        for (const url of urls) {\n            const source = document.createElement(\"source\");\n            source.src = _CleanUrl(url);\n            audio.appendChild(source);\n        }\n\n        this._initFromMediaElement(audio);\n    }\n\n    private _onCanPlayThrough: () => void = () => {\n        this._isReady = true;\n        this._resolveIsReadyPromise(this._mediaElement);\n        this.onReadyObservable.notifyObservers(this);\n    };\n\n    private _onEnded: () => void = () => {\n        this.onEndedObservable.notifyObservers(this);\n        this.dispose();\n    };\n\n    private _onError: (reason: any) => void = (reason: any) => {\n        this._setState(SoundState.FailedToStart);\n        this.onErrorObservable.notifyObservers(reason);\n        this._rejectIsReadyPromise(reason);\n        this.dispose();\n    };\n\n    private _onEngineStateChanged = () => {\n        if (this.engine.state !== \"running\") {\n            return;\n        }\n\n        if (this._options.loop && this.state === SoundState.Starting) {\n            this.play();\n        }\n\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    };\n\n    private _onUserGesture = () => {\n        this.play();\n    };\n\n    private _play(): void {\n        this._setState(SoundState.Starting);\n\n        if (!this._isReady) {\n            this._playWhenReady();\n            return;\n        }\n\n        if (this._state !== SoundState.Starting) {\n            return;\n        }\n\n        if (this.engine.state === \"running\") {\n            const result = this._mediaElement.play();\n\n            this._enginePlayTime = this.engine.currentTime;\n            this._setState(SoundState.Started);\n\n            // It's possible that the play() method fails on Safari, even if the audio engine's state is \"running\".\n            // This occurs when the audio context is paused by the system and resumed automatically by the audio engine\n            // without a user interaction (e.g. when the Vision Pro exits and reenters immersive mode).\n            // eslint-disable-next-line github/no-then\n            result.catch(() => {\n                this._setState(SoundState.FailedToStart);\n\n                if (this._options.loop) {\n                    this.engine.userGestureObservable.addOnce(this._onUserGesture);\n                }\n            });\n        } else if (this._options.loop) {\n            this.engine.stateChangedObservable.add(this._onEngineStateChanged);\n        } else {\n            this.stop();\n            this._setState(SoundState.FailedToStart);\n        }\n    }\n\n    private _playWhenReady(): void {\n        this._isReadyPromise\n            // eslint-disable-next-line github/no-then\n            .then(() => {\n                this._play();\n            })\n            // eslint-disable-next-line github/no-then\n            .catch(() => {\n                Logger.Error(\"Streaming sound instance failed to play\");\n                this._setState(SoundState.FailedToStart);\n            });\n    }\n\n    private _rejectIsReadyPromise: (reason?: any) => void;\n    private _resolveIsReadyPromise: (mediaElement: HTMLMediaElement) => void;\n\n    private _stop(): void {\n        this._mediaElement.pause();\n        this._setState(SoundState.Stopped);\n        this._onEnded();\n        this.engine.stateChangedObservable.removeCallback(this._onEngineStateChanged);\n    }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAKzC,OAAO,EAAE,cAAc,EAAE,MAAM,iCAAiC,CAAC;AACjE,OAAO,EAAE,uBAAuB,EAAE,MAAM,yCAAyC,CAAC;AAClF,OAAO,EAAE,uBAAuB,EAAE,MAAM,qDAAqD,CAAC;AAE9F,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAE1C,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;;;;;;;;;;AAO7D,MAAO,uBAAwB,8LAAQ,iBAAc;IAyCvD,cAAA,EAAgB,CACT,KAAK,CAAC,UAAU,CAAC,MAAgC,EAAE,OAAwC,EAAA;QAC9F,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAE/C,IAAI,CAAC,CAAC,YAAY,YAAY,YAAY,CAAC,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,CAAC,MAAM,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,oNAAI,0BAAuB,AAAvB,EAAwB,OAAO,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAoB,OAAO,GAAA;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED,cAAA,EAAgB,CAChB,IAAoB,MAAM,GAAA;;QACtB,4BAAW,CAAC,OAAO,IAAI,qCAAhB,gBAAiB,IAAI,CAAC,OAAO,GAAG,IAAI,kNAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,yBAAyB,CAAC;IACrC,CAAC;IAES,eAAe,GAAA;QACrB,OAAO,IAAI,+BAA+B,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpE,CAAC;IAEkB,QAAQ,CAAC,IAAqB,EAAA;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACX,CAAC,QAAQ,mDAAb,eAAe,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB,EAAA;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACX,CAAC,QAAQ,mDAAb,eAAe,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,qNAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9G,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAzID,cAAA,EAAgB,CAChB,YAAmB,IAAY,EAAE,MAAuB,EAAE,OAAwC,CAAA;QAC9F,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAnBhB,IAAA,CAAA,QAAQ,GAA4B,IAAI,CAAC;QAChC,IAAA,CAAA,kBAAkB,GAAY,IAAI,CAAC;QACnC,IAAA,CAAA,qBAAqB,GAAW,CAAC,CAAC;QAC3C,IAAA,CAAA,OAAO,GAA2B,IAAI,CAAC;QAkB3C,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAC9D,CAAC;;QAED,IAAI,CAAC,QAAQ,GAAG;YACZ,QAAQ,+BAAU,QAAQ,6CAAhB,OAAO,aAAa,KAAK;YACnC,IAAI,2BAAU,IAAI,yCAAZ,OAAO,SAAS,KAAK;YAC3B,YAAY,mCAAU,YAAY,iDAApB,OAAO,iBAAiB,QAAQ;YAC9C,YAAY,mCAAU,YAAY,cAApB,OAAO,oDAAiB,CAAC;YACvC,WAAW,kCAAU,WAAW,cAAnB,OAAO,kDAAgB,CAAC;SACxC,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,uBAAuB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;;AAsHc,wBAAA,SAAS,GAAG,KAAM,iNAAQ,gCAA4B;IAGjE,IAAc,gBAAgB,GAAA;;QAC1B,4CAAW,CAAC,MAAM,CAAC,gBAAgB,yDAA5B,gCAAgC,IAAI,CAAC;IAChD,CAAC;IAED,IAAc,cAAc,GAAA;YACjB;QAAP,0CAAW,CAAC,MAAM,CAAC,cAAc,qFAAI,IAAI,CAAC;IAC9C,CAAC;CACJ,AAVuB,CAUtB;AAGN,cAAA,EAAgB,CAChB,MAAM,+BAAgC,sMAAQ,0BAAuB;IAkCjE,cAAA,EAAgB,CAChB,IAAW,WAAW,GAAA;QAClB,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,KAAA,EAAA,qBAAA,EAAsB,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QAClH,OAAO,IAAI,CAAC,gBAAgB,GAAG,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IAClF,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,KAAA,EAAA,uBAAA,EAAwB,KAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,CAAC;QAE1F,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAA,EAAA,sBAAA,GAAoB,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;QAElC,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,CAAC;gBAAE,WAAW,EAAE,KAAK;YAAA,CAAE,CAAC,CAAC;QACtC,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,qBAAA,EAAsB,GAAE,CAAC;YAC3C,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;QAC/C,CAAC;IACL,CAAC;IAED,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,cAAA,EAAgB,CAChB,IAAW,SAAS,GAAA;QAChB,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;YACrC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,cAAA,EAAgB,CACA,OAAO,GAAA;;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,IAAI,EAAE,CAAC;iCAER,CAAC,WAAW,sDAAhB,kBAAkB,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEjF,KAAK,MAAM,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAE,CAAC;YAC3D,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9E,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC1E,CAAC;IAED,cAAA,EAAgB,CACT,IAAI,GAAkD;sBAAjD,iEAA+C,CAAA,CAAE;QACzD,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAE7C,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAEtC,IAAI,IAAI,CAAC,8BAA8B,EAAE,CAAC;YACtC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YACxC,IAAI,CAAC,8BAA8B,GAAG,KAAK,CAAC;QAChD,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,qBAAA,EAAsB,GAAE,CAAC;YAC3C,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC/D,CAAC;QAED,IAAI,WAAW,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC;QACjD,CAAC;;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,8BAAW,MAAM,2CAAd,OAAO,WAAW,CAAC,CAAC;QAElD,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,GAAA;QACR,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,uBAAA,EAAwB,KAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;YAC5E,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAA,EAAA,qBAAA,GAAmB,CAAC;QAClC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QAExE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CACT,MAAM,GAAA;QACT,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,qBAAA,EAAsB,GAAE,CAAC;YACpC,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC,MAAM,IAAI,IAAI,CAAC,8BAA8B,EAAE,CAAC;YAC7C,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACA,IAAI,GAAA;QAChB,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,sBAAA,EAAuB,GAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,GAAA;QACf,OAAO,iCAAiC,CAAC;IAC7C,CAAC;IAEkB,QAAQ,CAAC,IAAuB,EAAA;QAC/C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,gGAAgG;QAChG,IAAI,IAAI,YAAY,uBAAuB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACtD,CAAC,QAAQ,mDAAb,eAAe,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAuB,EAAA;QAClD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,YAAY,uBAAuB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;;kCACtD,CAAC,QAAQ,mDAAb,eAAe,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,qBAAqB,CAAC,YAA8B,EAAA;gKACxD,QAAK,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAE7D,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC9B,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACvC,YAAY,CAAC,OAAO,GAAG,MAAM,CAAC;QAE9B,YAAY,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAAE,IAAI,EAAE,IAAI;QAAA,CAAE,CAAC,CAAC;QACxF,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;YAAE,IAAI,EAAE,IAAI;QAAA,CAAE,CAAC,CAAC;QACtE,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;YAAE,IAAI,EAAE,IAAI;QAAA,CAAE,CAAC,CAAC;QAEtE,YAAY,CAAC,IAAI,EAAE,CAAC;QAEpB,IAAI,CAAC,WAAW,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;YAAE,YAAY,EAAE,YAAY;QAAA,CAAE,CAAC,CAAC;QAC9G,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE3C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;IACtC,CAAC;IAEO,YAAY,CAAC,GAAW,EAAA;QAC5B,MAAM,KAAK,GAAG,IAAI,KAAK,qKAAC,YAAA,AAAS,EAAC,GAAG,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAEO,aAAa,CAAC,IAAc,EAAA;QAChC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAE1B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAE,CAAC;YACrB,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,GAAG,uKAAG,YAAA,AAAS,EAAC,GAAG,CAAC,CAAC;YAC5B,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAoCO,KAAK,GAAA;QACT,IAAI,CAAC,SAAS,CAAA,EAAA,uBAAA,GAAqB,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACtC,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAEzC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;YAC/C,IAAI,CAAC,SAAS,CAAA,EAAA,sBAAA,GAAoB,CAAC;YAEnC,uGAAuG;YACvG,2GAA2G;YAC3G,2FAA2F;YAC3F,0CAA0C;YAC1C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,SAAS,CAAA,EAAA,4BAAA,GAA0B,CAAC;gBAEzC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACrB,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACnE,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACvE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAA,EAAA,4BAAA,GAA0B,CAAC;QAC7C,CAAC;IACL,CAAC;IAEO,cAAc,GAAA;QAClB,IAAI,CAAC,eAAe,AAChB,0CAA0C;SACzC,IAAI,CAAC,GAAG,EAAE;YACP,IAAI,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC,CAAC,AACF,0CAA0C;SACzC,KAAK,CAAC,GAAG,EAAE;qKACR,SAAM,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;YACxD,IAAI,CAAC,SAAS,CAAA,EAAA,4BAAA,GAA0B,CAAC;QAC7C,CAAC,CAAC,CAAC;IACX,CAAC;IAKO,KAAK,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAA,EAAA,sBAAA,GAAoB,CAAC;QACnC,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAClF,CAAC;IA7SD,YAAmB,KAA8B,EAAE,OAAqC,CAAA;QACpF,KAAK,CAAC,KAAK,CAAC,CAAC;QAnBT,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QACvC,IAAA,CAAA,eAAe,GAAW,QAAQ,CAAC;QACnC,IAAA,CAAA,gBAAgB,GAAW,CAAC,CAAC;QAC7B,IAAA,CAAA,QAAQ,GAAY,KAAK,CAAC;QAC1B,IAAA,CAAA,eAAe,GAA8B,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACjF,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC;YACtC,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC;QACxC,CAAC,CAAC,CAAC;QA4NK,IAAA,CAAA,iBAAiB,GAAe,GAAG,EAAE;YACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACjD,CAAC,CAAC;QAEM,IAAA,CAAA,QAAQ,GAAe,GAAG,EAAE;YAChC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC;QAEM,IAAA,CAAA,QAAQ,GAA0B,CAAC,MAAW,EAAE,EAAE;YACtD,IAAI,CAAC,SAAS,CAAA,EAAA,4BAAA,GAA0B,CAAC;YACzC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC;QAEM,IAAA,CAAA,qBAAqB,GAAG,GAAG,EAAE;YACjC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAClC,OAAO;YACX,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;gBAC3D,IAAI,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAClF,CAAC,CAAC;QAEM,IAAA,CAAA,cAAc,GAAG,GAAG,EAAE;YAC1B,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC,CAAC;QA9OE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAErD,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACpC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,YAAY,gBAAgB,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;CAiSJ", "debugId": null}}, {"offset": {"line": 4613, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/webAudio/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/webAudio/index.ts"], "sourcesContent": ["export * from \"./webAudioBus\";\nexport * from \"./webAudioEngine\";\nexport * from \"./webAudioMainBus\";\nexport * from \"./webAudioSoundSource\";\nexport * from \"./webAudioStaticSound\";\nexport * from \"./webAudioStreamingSound\";\n"], "names": [], "mappings": ";AAAA,cAAc,eAAe,CAAC;AAC9B,cAAc,kBAAkB,CAAC;AACjC,cAAc,mBAAmB,CAAC;AAClC,cAAc,uBAAuB,CAAC;AACtC,cAAc,uBAAuB,CAAC;AACtC,cAAc,0BAA0B,CAAC", "debugId": null}}, {"offset": {"line": 4642, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/AudioV2/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/AudioV2/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-restricted-imports */\nexport * from \"./abstractAudio/index\";\nexport * from \"./audioParameter\";\nexport * from \"./soundState\";\nexport * from \"./spatialAudioAttachmentType\";\nexport * from \"./webAudio/index\";\n"], "names": [], "mappings": "AAAA,2DAAA,EAA6D;AAC7D,cAAc,uBAAuB,CAAC;AACtC,cAAc,kBAAkB,CAAC;AACjC,cAAc,cAAc,CAAC;AAC7B,cAAc,8BAA8B,CAAC;AAC7C,cAAc,kBAAkB,CAAC", "debugId": null}}]}