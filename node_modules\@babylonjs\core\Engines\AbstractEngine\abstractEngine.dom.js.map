{"version": 3, "file": "abstractEngine.dom.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.dom.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAwEnD,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG;IACvC,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,4BAA4B,GAAG;IACpD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,OAAO,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;AACzD,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,yBAAyB,GAAG;IACjD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,OAAO,IAAI,CAAC,eAAe,EAAG,CAAC,qBAAqB,EAAE,CAAC;AAC3D,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,aAAiC,EAAE,SAAS,GAAG,KAAK;IACpG,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;IACxC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AACnH,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,oBAAoB,GAAG;IAC5C,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAClE,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG;IAC1C,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;AAClC,CAAC,CAAC", "sourcesContent": ["import type { IViewportLike } from \"../../Maths/math.like\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { AbstractEngine } from \"../abstractEngine\";\r\n\r\n/**\r\n * Defines the interface used by objects containing a viewport (like a camera)\r\n */\r\ninterface IViewportOwnerLike {\r\n    /**\r\n     * Gets or sets the viewport\r\n     */\r\n    viewport: IViewportLike;\r\n}\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Gets the HTML element used to attach event listeners\r\n         * @returns a HTML element\r\n         */\r\n        getInputElement(): Nullable<HTMLElement>;\r\n\r\n        /**\r\n         * Gets the client rect of the HTML canvas attached with the current webGL context\r\n         * @returns a client rectangle\r\n         */\r\n        getRenderingCanvasClientRect(): Nullable<ClientRect>;\r\n\r\n        /**\r\n         * Gets the client rect of the HTML element used for events\r\n         * @returns a client rectangle\r\n         */\r\n        getInputElementClientRect(): Nullable<ClientRect>;\r\n\r\n        /**\r\n         * Gets current aspect ratio\r\n         * @param viewportOwner defines the camera to use to get the aspect ratio\r\n         * @param useScreen defines if screen size must be used (or the current render target if any)\r\n         * @returns a number defining the aspect ratio\r\n         */\r\n        getAspectRatio(viewportOwner: IViewportOwnerLike, useScreen?: boolean): number;\r\n\r\n        /**\r\n         * Gets current screen aspect ratio\r\n         * @returns a number defining the aspect ratio\r\n         */\r\n        getScreenAspectRatio(): number;\r\n\r\n        /**\r\n         * Toggle full screen mode\r\n         * @param requestPointerLock defines if a pointer lock should be requested from the user\r\n         */\r\n        switchFullscreen(requestPointerLock: boolean): void;\r\n\r\n        /**\r\n         * Enters full screen mode\r\n         * @param requestPointerLock defines if a pointer lock should be requested from the user\r\n         */\r\n        enterFullscreen(requestPointerLock: boolean): void;\r\n\r\n        /**\r\n         * Exits full screen mode\r\n         */\r\n        exitFullscreen(): void;\r\n\r\n        /** @internal */\r\n        _onPointerLockChange: () => void;\r\n\r\n        /** @internal */\r\n        _verifyPointerLock(): void;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype.getInputElement = function (): Nullable<HTMLElement> {\r\n    return this._renderingCanvas;\r\n};\r\n\r\nAbstractEngine.prototype.getRenderingCanvasClientRect = function (): Nullable<ClientRect> {\r\n    if (!this._renderingCanvas) {\r\n        return null;\r\n    }\r\n    return this._renderingCanvas.getBoundingClientRect();\r\n};\r\n\r\nAbstractEngine.prototype.getInputElementClientRect = function (): Nullable<ClientRect> {\r\n    if (!this._renderingCanvas) {\r\n        return null;\r\n    }\r\n    return this.getInputElement()!.getBoundingClientRect();\r\n};\r\n\r\nAbstractEngine.prototype.getAspectRatio = function (viewportOwner: IViewportOwnerLike, useScreen = false): number {\r\n    const viewport = viewportOwner.viewport;\r\n    return (this.getRenderWidth(useScreen) * viewport.width) / (this.getRenderHeight(useScreen) * viewport.height);\r\n};\r\n\r\nAbstractEngine.prototype.getScreenAspectRatio = function (): number {\r\n    return this.getRenderWidth(true) / this.getRenderHeight(true);\r\n};\r\n\r\nAbstractEngine.prototype._verifyPointerLock = function (): void {\r\n    this._onPointerLockChange?.();\r\n};\r\n"]}