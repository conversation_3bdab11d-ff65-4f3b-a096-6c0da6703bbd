{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/gizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/gizmo.ts"], "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport { Quaternion, Vector3, Matrix, TmpVectors } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { Camera } from \"../Cameras/camera\";\r\nimport type { TargetCamera } from \"../Cameras/targetCamera\";\r\nimport type { Node } from \"../node\";\r\nimport type { Bone } from \"../Bones/bone\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../Events/pointerEvents\";\r\nimport type { LinesMesh } from \"../Meshes/linesMesh\";\r\nimport type { PointerDragBehavior } from \"../Behaviors/Meshes/pointerDragBehavior\";\r\nimport type { ShadowLight } from \"../Lights/shadowLight\";\r\nimport { Light } from \"../Lights/light\";\r\n\r\n/**\r\n * Cache built by each axis. Used for managing state between all elements of gizmo for enhanced UI\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface GizmoAxisCache {\r\n    /** Mesh used to render the Gizmo */\r\n    gizmoMeshes: Mesh[];\r\n    /** Mesh used to detect user interaction with Gizmo */\r\n    colliderMeshes: Mesh[];\r\n    /** Material used to indicate color of gizmo mesh */\r\n    material: StandardMaterial;\r\n    /** Material used to indicate hover state of the Gizmo */\r\n    hoverMaterial: StandardMaterial;\r\n    /** Material used to indicate disabled state of the Gizmo */\r\n    disableMaterial: StandardMaterial;\r\n    /** Used to indicate Active state of the Gizmo */\r\n    active: boolean;\r\n    /** DragBehavior */\r\n    dragBehavior: PointerDragBehavior;\r\n}\r\n\r\n/**\r\n * Anchor options where the Gizmo can be positioned in relation to its anchored node\r\n */\r\nexport const enum GizmoAnchorPoint {\r\n    /** The origin of the attached node */\r\n    Origin,\r\n    /** The pivot point of the attached node*/\r\n    Pivot,\r\n}\r\n\r\n/**\r\n * Coordinates mode: Local or World. Defines how axis is aligned: either on world axis or transform local axis\r\n */\r\nexport const enum GizmoCoordinatesMode {\r\n    World,\r\n    Local,\r\n}\r\n\r\n/**\r\n * Interface for basic gizmo\r\n */\r\nexport interface IGizmo extends IDisposable {\r\n    /** True when the mouse pointer is hovered a gizmo mesh */\r\n    readonly isHovered: boolean;\r\n    /** The root mesh of the gizmo */\r\n    _rootMesh: Mesh;\r\n    /** Ratio for the scale of the gizmo */\r\n    scaleRatio: number;\r\n    /**\r\n     * Mesh that the gizmo will be attached to. (eg. on a drag gizmo the mesh that will be dragged)\r\n     * * When set, interactions will be enabled\r\n     */\r\n    attachedMesh: Nullable<AbstractMesh>;\r\n    /**\r\n     * Node that the gizmo will be attached to. (eg. on a drag gizmo the mesh, bone or NodeTransform that will be dragged)\r\n     * * When set, interactions will be enabled\r\n     */\r\n    attachedNode: Nullable<Node>;\r\n    /**\r\n     * If set the gizmo's rotation will be updated to match the attached mesh each frame (Default: true)\r\n     */\r\n    updateGizmoRotationToMatchAttachedMesh: boolean;\r\n    /** The utility layer the gizmo will be added to */\r\n    gizmoLayer: UtilityLayerRenderer;\r\n    /**\r\n     * If set the gizmo's position will be updated to match the attached mesh each frame (Default: true)\r\n     */\r\n    updateGizmoPositionToMatchAttachedMesh: boolean;\r\n    /**\r\n     * Defines where the gizmo will be positioned if `updateGizmoPositionToMatchAttachedMesh` is enabled.\r\n     * (Default: GizmoAnchorPoint.Origin)\r\n     */\r\n    anchorPoint: GizmoAnchorPoint;\r\n\r\n    /**\r\n     * Set the coordinate mode to use. By default it's local.\r\n     */\r\n    coordinatesMode: GizmoCoordinatesMode;\r\n\r\n    /**\r\n     * When set, the gizmo will always appear the same size no matter where the camera is (default: true)\r\n     */\r\n    updateScale: boolean;\r\n    /**\r\n     * posture that the gizmo will be display\r\n     * When set null, default value will be used (Quaternion(0, 0, 0, 1))\r\n     */\r\n    customRotationQuaternion: Nullable<Quaternion>;\r\n    /**\r\n     * Disposes and replaces the current meshes in the gizmo with the specified mesh\r\n     * @param mesh The mesh to replace the default mesh of the gizmo\r\n     */\r\n    setCustomMesh(mesh: Mesh): void;\r\n\r\n    /**\r\n     * Additional transform applied to the gizmo.\r\n     * It's useful when the gizmo is attached to a bone: if the bone is part of a skeleton attached to a mesh, you should define the mesh as additionalTransformNode if you want the gizmo to be displayed at the bone's correct location.\r\n     * Otherwise, as the gizmo is relative to the skeleton root, the mesh transformation will not be taken into account.\r\n     */\r\n    additionalTransformNode?: TransformNode | undefined;\r\n}\r\n/**\r\n * Renders gizmos on top of an existing scene which provide controls for position, rotation, etc.\r\n */\r\nexport class Gizmo implements IGizmo {\r\n    /**\r\n     * The root mesh of the gizmo\r\n     */\r\n    public _rootMesh: Mesh;\r\n    protected _attachedMesh: Nullable<AbstractMesh> = null;\r\n    protected _attachedNode: Nullable<Node> = null;\r\n    protected _customRotationQuaternion: Nullable<Quaternion> = null;\r\n    protected _additionalTransformNode?: TransformNode;\r\n    /**\r\n     * Ratio for the scale of the gizmo (Default: 1)\r\n     */\r\n    protected _scaleRatio = 1;\r\n\r\n    /**\r\n     * boolean updated by pointermove when a gizmo mesh is hovered\r\n     */\r\n    protected _isHovered = false;\r\n\r\n    /**\r\n     * When enabled, any gizmo operation will perserve scaling sign. Default is off.\r\n     * Only valid for TransformNode derived classes (Mesh, AbstractMesh, ...)\r\n     */\r\n    public static PreserveScaling = false;\r\n\r\n    /**\r\n     * There are 2 ways to preserve scaling: using mesh scaling or absolute scaling. Depending of hierarchy, non uniform scaling and LH or RH coordinates. One is preferable than the other.\r\n     * If the scaling to be preserved is the local scaling, then set this value to false.\r\n     * Default is true which means scaling to be preserved is absolute one (with hierarchy applied)\r\n     */\r\n    public static UseAbsoluteScaling = true;\r\n\r\n    /**\r\n     * Ratio for the scale of the gizmo (Default: 1)\r\n     */\r\n    public set scaleRatio(value: number) {\r\n        this._scaleRatio = value;\r\n    }\r\n\r\n    public get scaleRatio() {\r\n        return this._scaleRatio;\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is hovered a gizmo mesh\r\n     */\r\n    public get isHovered() {\r\n        return this._isHovered;\r\n    }\r\n\r\n    /**\r\n     * If a custom mesh has been set (Default: false)\r\n     */\r\n    protected _customMeshSet = false;\r\n    /**\r\n     * Mesh that the gizmo will be attached to. (eg. on a drag gizmo the mesh that will be dragged)\r\n     * * When set, interactions will be enabled\r\n     */\r\n    public get attachedMesh() {\r\n        return this._attachedMesh;\r\n    }\r\n    public set attachedMesh(value) {\r\n        this._attachedMesh = value;\r\n        if (value) {\r\n            this._attachedNode = value;\r\n        }\r\n        this._rootMesh.setEnabled(value ? true : false);\r\n        this._attachedNodeChanged(value);\r\n    }\r\n    /**\r\n     * Node that the gizmo will be attached to. (eg. on a drag gizmo the mesh, bone or NodeTransform that will be dragged)\r\n     * * When set, interactions will be enabled\r\n     */\r\n    public get attachedNode() {\r\n        return this._attachedNode;\r\n    }\r\n    public set attachedNode(value) {\r\n        this._attachedNode = value;\r\n        this._attachedMesh = null;\r\n        this._rootMesh.setEnabled(value ? true : false);\r\n        this._attachedNodeChanged(value);\r\n    }\r\n\r\n    /**\r\n     * Disposes and replaces the current meshes in the gizmo with the specified mesh\r\n     * @param mesh The mesh to replace the default mesh of the gizmo\r\n     */\r\n    public setCustomMesh(mesh: Mesh) {\r\n        if (mesh.getScene() != this.gizmoLayer.utilityLayerScene) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"When setting a custom mesh on a gizmo, the custom meshes scene must be the same as the gizmos (eg. gizmo.gizmoLayer.utilityLayerScene)\";\r\n        }\r\n        const children = this._rootMesh.getChildMeshes();\r\n        for (const c of children) {\r\n            c.dispose();\r\n        }\r\n        mesh.parent = this._rootMesh;\r\n        this._customMeshSet = true;\r\n    }\r\n\r\n    /**\r\n     * Additional transform applied to the gizmo.\r\n     * It's useful when the gizmo is attached to a bone: if the bone is part of a skeleton attached to a mesh, you should define the mesh as additionalTransformNode if you want the gizmo to be displayed at the bone's correct location.\r\n     * Otherwise, as the gizmo is relative to the skeleton root, the mesh transformation will not be taken into account.\r\n     */\r\n    public get additionalTransformNode() {\r\n        return this._additionalTransformNode;\r\n    }\r\n\r\n    public set additionalTransformNode(value: TransformNode | undefined) {\r\n        this._additionalTransformNode = value;\r\n    }\r\n\r\n    protected _updateGizmoRotationToMatchAttachedMesh = true;\r\n    protected _updateGizmoPositionToMatchAttachedMesh = true;\r\n    protected _anchorPoint = GizmoAnchorPoint.Origin;\r\n    protected _updateScale = true;\r\n    protected _coordinatesMode = GizmoCoordinatesMode.Local;\r\n\r\n    /**\r\n     * If set the gizmo's rotation will be updated to match the attached mesh each frame (Default: true)\r\n     * NOTE: This is only possible for meshes with uniform scaling, as otherwise it's not possible to decompose the rotation\r\n     */\r\n    public set updateGizmoRotationToMatchAttachedMesh(value: boolean) {\r\n        this._updateGizmoRotationToMatchAttachedMesh = value;\r\n    }\r\n    public get updateGizmoRotationToMatchAttachedMesh() {\r\n        return this._updateGizmoRotationToMatchAttachedMesh;\r\n    }\r\n    /**\r\n     * If set the gizmo's position will be updated to match the attached mesh each frame (Default: true)\r\n     */\r\n    public set updateGizmoPositionToMatchAttachedMesh(value: boolean) {\r\n        this._updateGizmoPositionToMatchAttachedMesh = value;\r\n    }\r\n    public get updateGizmoPositionToMatchAttachedMesh() {\r\n        return this._updateGizmoPositionToMatchAttachedMesh;\r\n    }\r\n\r\n    /**\r\n     * Defines where the gizmo will be positioned if `updateGizmoPositionToMatchAttachedMesh` is enabled.\r\n     * (Default: GizmoAnchorPoint.Origin)\r\n     */\r\n    public set anchorPoint(value: GizmoAnchorPoint) {\r\n        this._anchorPoint = value;\r\n    }\r\n    public get anchorPoint() {\r\n        return this._anchorPoint;\r\n    }\r\n\r\n    /**\r\n     * Set the coordinate system to use. By default it's local.\r\n     * But it's possible for a user to tweak so its local for translation and world for rotation.\r\n     * In that case, setting the coordinate system will change `updateGizmoRotationToMatchAttachedMesh` and `updateGizmoPositionToMatchAttachedMesh`\r\n     */\r\n    public set coordinatesMode(coordinatesMode: GizmoCoordinatesMode) {\r\n        this._coordinatesMode = coordinatesMode;\r\n        const local = coordinatesMode == GizmoCoordinatesMode.Local;\r\n        this.updateGizmoRotationToMatchAttachedMesh = local;\r\n        this.updateGizmoPositionToMatchAttachedMesh = true;\r\n    }\r\n\r\n    public get coordinatesMode() {\r\n        return this._coordinatesMode;\r\n    }\r\n\r\n    /**\r\n     * When set, the gizmo will always appear the same size no matter where the camera is (default: true)\r\n     */\r\n\r\n    public set updateScale(value: boolean) {\r\n        this._updateScale = value;\r\n    }\r\n    public get updateScale() {\r\n        return this._updateScale;\r\n    }\r\n    protected _interactionsEnabled = true;\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _attachedNodeChanged(value: Nullable<Node>) {}\r\n\r\n    protected _beforeRenderObserver: Nullable<Observer<Scene>>;\r\n    private _rightHandtoLeftHandMatrix = Matrix.RotationY(Math.PI);\r\n\r\n    /**\r\n     * Creates a gizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     */\r\n    constructor(\r\n        /** [Object] The utility layer the gizmo will be added to */\r\n        public gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer\r\n    ) {\r\n        this._rootMesh = new Mesh(\"gizmoRootNode\", gizmoLayer.utilityLayerScene);\r\n        this._rootMesh.rotationQuaternion = Quaternion.Identity();\r\n\r\n        this._beforeRenderObserver = this.gizmoLayer.utilityLayerScene.onBeforeRenderObservable.add(() => {\r\n            this._update();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * posture that the gizmo will be display\r\n     * When set null, default value will be used (Quaternion(0, 0, 0, 1))\r\n     */\r\n    public get customRotationQuaternion(): Nullable<Quaternion> {\r\n        return this._customRotationQuaternion;\r\n    }\r\n\r\n    public set customRotationQuaternion(customRotationQuaternion: Nullable<Quaternion>) {\r\n        this._customRotationQuaternion = customRotationQuaternion;\r\n    }\r\n\r\n    /**\r\n     * Updates the gizmo to match the attached mesh's position/rotation\r\n     */\r\n    protected _update() {\r\n        if (this.attachedNode) {\r\n            let effectiveNode = this.attachedNode;\r\n            if (this.attachedMesh) {\r\n                effectiveNode = this.attachedMesh || this.attachedNode;\r\n            }\r\n\r\n            // Position\r\n            if (this.updateGizmoPositionToMatchAttachedMesh) {\r\n                if (this.anchorPoint == GizmoAnchorPoint.Pivot && (<TransformNode>effectiveNode).getAbsolutePivotPoint) {\r\n                    const position = (<TransformNode>effectiveNode).getAbsolutePivotPoint();\r\n                    this._rootMesh.position.copyFrom(position);\r\n                } else {\r\n                    const row = effectiveNode.getWorldMatrix().getRow(3);\r\n                    const position = row ? row.toVector3() : new Vector3(0, 0, 0);\r\n                    this._rootMesh.position.copyFrom(position);\r\n                }\r\n            }\r\n\r\n            // Rotation\r\n            if (this.updateGizmoRotationToMatchAttachedMesh) {\r\n                const supportedNode =\r\n                    (<Mesh>effectiveNode)._isMesh ||\r\n                    effectiveNode.getClassName() === \"AbstractMesh\" ||\r\n                    effectiveNode.getClassName() === \"TransformNode\" ||\r\n                    effectiveNode.getClassName() === \"InstancedMesh\";\r\n                const transformNode = supportedNode ? (effectiveNode as TransformNode) : undefined;\r\n                effectiveNode.getWorldMatrix().decompose(undefined, this._rootMesh.rotationQuaternion!, undefined, Gizmo.PreserveScaling ? transformNode : undefined);\r\n                this._rootMesh.rotationQuaternion!.normalize();\r\n            } else {\r\n                if (this._customRotationQuaternion) {\r\n                    this._rootMesh.rotationQuaternion!.copyFrom(this._customRotationQuaternion);\r\n                } else {\r\n                    this._rootMesh.rotationQuaternion!.set(0, 0, 0, 1);\r\n                }\r\n            }\r\n\r\n            // Scale\r\n            if (this.updateScale) {\r\n                const activeCamera = this.gizmoLayer.utilityLayerScene.activeCamera!;\r\n                const cameraPosition = activeCamera.globalPosition;\r\n                this._rootMesh.position.subtractToRef(cameraPosition, TmpVectors.Vector3[0]);\r\n                let scale = this.scaleRatio;\r\n                if (activeCamera.mode == Camera.ORTHOGRAPHIC_CAMERA) {\r\n                    if (activeCamera.orthoTop && activeCamera.orthoBottom) {\r\n                        const orthoHeight = activeCamera.orthoTop - activeCamera.orthoBottom;\r\n                        scale *= orthoHeight;\r\n                    }\r\n                } else {\r\n                    const camForward = activeCamera.getScene().useRightHandedSystem ? Vector3.RightHandedForwardReadOnly : Vector3.LeftHandedForwardReadOnly;\r\n                    const direction = activeCamera.getDirection(camForward);\r\n                    scale *= Vector3.Dot(TmpVectors.Vector3[0], direction);\r\n                }\r\n                this._rootMesh.scaling.setAll(scale);\r\n\r\n                // Account for handedness, similar to Matrix.decompose\r\n                if (effectiveNode._getWorldMatrixDeterminant() < 0 && !Gizmo.PreserveScaling) {\r\n                    this._rootMesh.scaling.y *= -1;\r\n                }\r\n            } else {\r\n                this._rootMesh.scaling.setAll(this.scaleRatio);\r\n            }\r\n        }\r\n\r\n        if (this.additionalTransformNode) {\r\n            this._rootMesh.computeWorldMatrix(true);\r\n            this._rootMesh.getWorldMatrix().multiplyToRef(this.additionalTransformNode.getWorldMatrix(), TmpVectors.Matrix[0]);\r\n            TmpVectors.Matrix[0].decompose(this._rootMesh.scaling, this._rootMesh.rotationQuaternion!, this._rootMesh.position);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * if transform has a pivot and is not using PostMultiplyPivotMatrix, then the worldMatrix contains the pivot matrix (it's not cancelled at the end)\r\n     * so, when extracting the world matrix component, the translation (and other components) is containing the pivot translation.\r\n     * And the pivot is applied each frame. Removing it anyway here makes it applied only in computeWorldMatrix.\r\n     * @param transform local transform that needs to be transform by the pivot inverse matrix\r\n     * @param localMatrix local matrix that needs to be transform by the pivot inverse matrix\r\n     * @param result resulting matrix transformed by pivot inverse if the transform node is using pivot without using post Multiply Pivot Matrix\r\n     */\r\n    protected _handlePivotMatrixInverse(transform: TransformNode, localMatrix: Matrix, result: Matrix): void {\r\n        if (transform.isUsingPivotMatrix() && !transform.isUsingPostMultiplyPivotMatrix()) {\r\n            transform.getPivotMatrix().invertToRef(TmpVectors.Matrix[5]);\r\n            TmpVectors.Matrix[5].multiplyToRef(localMatrix, result);\r\n            return;\r\n        }\r\n        result.copyFrom(localMatrix);\r\n    }\r\n    /**\r\n     * computes the rotation/scaling/position of the transform once the Node world matrix has changed.\r\n     */\r\n    protected _matrixChanged() {\r\n        if (!this._attachedNode) {\r\n            return;\r\n        }\r\n\r\n        if ((<Camera>this._attachedNode)._isCamera) {\r\n            const camera = this._attachedNode as Camera;\r\n            let worldMatrix;\r\n            let worldMatrixUc;\r\n            if (camera.parent) {\r\n                const parentInv = TmpVectors.Matrix[1];\r\n                camera.parent._worldMatrix.invertToRef(parentInv);\r\n                this._attachedNode._worldMatrix.multiplyToRef(parentInv, TmpVectors.Matrix[0]);\r\n                worldMatrix = TmpVectors.Matrix[0];\r\n            } else {\r\n                worldMatrix = this._attachedNode._worldMatrix;\r\n            }\r\n\r\n            if (camera.getScene().useRightHandedSystem) {\r\n                // avoid desync with RH matrix computation. Otherwise, rotation of PI around Y axis happens each frame resulting in axis flipped because worldMatrix is computed as inverse of viewMatrix.\r\n                this._rightHandtoLeftHandMatrix.multiplyToRef(worldMatrix, TmpVectors.Matrix[1]);\r\n                worldMatrixUc = TmpVectors.Matrix[1];\r\n            } else {\r\n                worldMatrixUc = worldMatrix;\r\n            }\r\n\r\n            worldMatrixUc.decompose(TmpVectors.Vector3[1], TmpVectors.Quaternion[0], TmpVectors.Vector3[0]);\r\n\r\n            const inheritsTargetCamera =\r\n                this._attachedNode.getClassName() === \"FreeCamera\" ||\r\n                this._attachedNode.getClassName() === \"FlyCamera\" ||\r\n                this._attachedNode.getClassName() === \"ArcFollowCamera\" ||\r\n                this._attachedNode.getClassName() === \"TargetCamera\" ||\r\n                this._attachedNode.getClassName() === \"TouchCamera\" ||\r\n                this._attachedNode.getClassName() === \"UniversalCamera\";\r\n\r\n            if (inheritsTargetCamera) {\r\n                const targetCamera = this._attachedNode as TargetCamera;\r\n                targetCamera.rotation = TmpVectors.Quaternion[0].toEulerAngles();\r\n\r\n                if (targetCamera.rotationQuaternion) {\r\n                    targetCamera.rotationQuaternion.copyFrom(TmpVectors.Quaternion[0]);\r\n                    targetCamera.rotationQuaternion.normalize();\r\n                }\r\n            }\r\n\r\n            camera.position.copyFrom(TmpVectors.Vector3[0]);\r\n        } else if (\r\n            (<Mesh>this._attachedNode)._isMesh ||\r\n            this._attachedNode.getClassName() === \"AbstractMesh\" ||\r\n            this._attachedNode.getClassName() === \"TransformNode\" ||\r\n            this._attachedNode.getClassName() === \"InstancedMesh\"\r\n        ) {\r\n            const transform = this._attachedNode as TransformNode;\r\n            if (transform.parent) {\r\n                const parentInv = TmpVectors.Matrix[0];\r\n                const localMat = TmpVectors.Matrix[1];\r\n                transform.parent.getWorldMatrix().invertToRef(parentInv);\r\n                this._attachedNode.getWorldMatrix().multiplyToRef(parentInv, localMat);\r\n                const matrixToDecompose = TmpVectors.Matrix[4];\r\n                this._handlePivotMatrixInverse(transform, localMat, matrixToDecompose);\r\n                matrixToDecompose.decompose(\r\n                    TmpVectors.Vector3[0],\r\n                    TmpVectors.Quaternion[0],\r\n                    transform.position,\r\n                    Gizmo.PreserveScaling ? transform : undefined,\r\n                    Gizmo.UseAbsoluteScaling\r\n                );\r\n                TmpVectors.Quaternion[0].normalize();\r\n                if (transform.isUsingPivotMatrix()) {\r\n                    // Calculate the local matrix without the translation.\r\n                    // Copied from TranslateNode.computeWorldMatrix\r\n                    const r = TmpVectors.Quaternion[1];\r\n                    Quaternion.RotationYawPitchRollToRef(transform.rotation.y, transform.rotation.x, transform.rotation.z, r);\r\n\r\n                    const scaleMatrix = TmpVectors.Matrix[2];\r\n                    Matrix.ScalingToRef(transform.scaling.x, transform.scaling.y, transform.scaling.z, scaleMatrix);\r\n\r\n                    const rotationMatrix = TmpVectors.Matrix[2];\r\n                    r.toRotationMatrix(rotationMatrix);\r\n\r\n                    const pivotMatrix = transform.getPivotMatrix();\r\n                    const invPivotMatrix = TmpVectors.Matrix[3];\r\n                    pivotMatrix.invertToRef(invPivotMatrix);\r\n\r\n                    pivotMatrix.multiplyToRef(scaleMatrix, TmpVectors.Matrix[4]);\r\n                    TmpVectors.Matrix[4].multiplyToRef(rotationMatrix, TmpVectors.Matrix[5]);\r\n                    TmpVectors.Matrix[5].multiplyToRef(invPivotMatrix, TmpVectors.Matrix[6]);\r\n\r\n                    TmpVectors.Matrix[6].getTranslationToRef(TmpVectors.Vector3[1]);\r\n\r\n                    transform.position.subtractInPlace(TmpVectors.Vector3[1]);\r\n                }\r\n            } else {\r\n                const matrixToDecompose = TmpVectors.Matrix[4];\r\n                this._handlePivotMatrixInverse(transform, this._attachedNode._worldMatrix, matrixToDecompose);\r\n                matrixToDecompose.decompose(\r\n                    TmpVectors.Vector3[0],\r\n                    TmpVectors.Quaternion[0],\r\n                    transform.position,\r\n                    Gizmo.PreserveScaling ? transform : undefined,\r\n                    Gizmo.UseAbsoluteScaling\r\n                );\r\n            }\r\n            TmpVectors.Vector3[0].scaleInPlace(1.0 / transform.scalingDeterminant);\r\n            transform.scaling.copyFrom(TmpVectors.Vector3[0]);\r\n            if (!transform.billboardMode) {\r\n                if (transform.rotationQuaternion) {\r\n                    transform.rotationQuaternion.copyFrom(TmpVectors.Quaternion[0]);\r\n                    transform.rotationQuaternion.normalize();\r\n                } else {\r\n                    transform.rotation = TmpVectors.Quaternion[0].toEulerAngles();\r\n                }\r\n            }\r\n        } else if (this._attachedNode.getClassName() === \"Bone\") {\r\n            const bone = this._attachedNode as Bone;\r\n            const parent = bone.getParent();\r\n\r\n            if (parent) {\r\n                const invParent = TmpVectors.Matrix[0];\r\n                const boneLocalMatrix = TmpVectors.Matrix[1];\r\n                parent.getFinalMatrix().invertToRef(invParent);\r\n                bone.getFinalMatrix().multiplyToRef(invParent, boneLocalMatrix);\r\n                const lmat = bone.getLocalMatrix();\r\n                lmat.copyFrom(boneLocalMatrix);\r\n            } else {\r\n                const lmat = bone.getLocalMatrix();\r\n                lmat.copyFrom(bone.getFinalMatrix());\r\n            }\r\n            bone.markAsDirty();\r\n        } else {\r\n            const light = this._attachedNode as ShadowLight;\r\n            if (light.getTypeID) {\r\n                const type = light.getTypeID();\r\n                if (type === Light.LIGHTTYPEID_DIRECTIONALLIGHT || type === Light.LIGHTTYPEID_SPOTLIGHT || type === Light.LIGHTTYPEID_POINTLIGHT) {\r\n                    const parent = light.parent;\r\n\r\n                    if (parent) {\r\n                        const invParent = TmpVectors.Matrix[0];\r\n                        const nodeLocalMatrix = TmpVectors.Matrix[1];\r\n                        parent.getWorldMatrix().invertToRef(invParent);\r\n                        light.getWorldMatrix().multiplyToRef(invParent, nodeLocalMatrix);\r\n                        nodeLocalMatrix.decompose(undefined, TmpVectors.Quaternion[0], TmpVectors.Vector3[0]);\r\n                    } else {\r\n                        this._attachedNode._worldMatrix.decompose(undefined, TmpVectors.Quaternion[0], TmpVectors.Vector3[0]);\r\n                    }\r\n                    // setter doesn't copy values. Need a new Vector3\r\n                    light.position = new Vector3(TmpVectors.Vector3[0].x, TmpVectors.Vector3[0].y, TmpVectors.Vector3[0].z);\r\n                    if (light.direction) {\r\n                        light.direction = new Vector3(light.direction.x, light.direction.y, light.direction.z);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * refresh gizmo mesh material\r\n     * @param gizmoMeshes\r\n     * @param material material to apply\r\n     */\r\n    protected _setGizmoMeshMaterial(gizmoMeshes: Mesh[], material: StandardMaterial) {\r\n        if (gizmoMeshes) {\r\n            for (const m of gizmoMeshes) {\r\n                m.material = material;\r\n                if ((<LinesMesh>m).color) {\r\n                    (<LinesMesh>m).color = material.diffuseColor;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Subscribes to pointer up, down, and hover events. Used for responsive gizmos.\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param gizmoAxisCache Gizmo axis definition used for reactive gizmo UI\r\n     * @returns {Observer<PointerInfo>} pointerObserver\r\n     */\r\n    public static GizmoAxisPointerObserver(gizmoLayer: UtilityLayerRenderer, gizmoAxisCache: Map<Mesh, GizmoAxisCache>): Observer<PointerInfo> {\r\n        let dragging = false;\r\n        let activeDragButton = -1;\r\n        let forcePointerUp = false;\r\n\r\n        const pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (pointerInfo.pickInfo) {\r\n                // If we are dragging and the user presses another button, end the drag.\r\n                // Otherwise, tracking when the drag should end becomes very complex.\r\n                // pointerDragBehavior.ts has similar logic.\r\n                forcePointerUp = dragging && pointerInfo.event.button !== -1 && pointerInfo.event.button !== activeDragButton;\r\n\r\n                if (forcePointerUp || pointerInfo.type === PointerEventTypes.POINTERUP) {\r\n                    // On Mouse Up\r\n\r\n                    gizmoAxisCache.forEach((cache) => {\r\n                        cache.active = false;\r\n                        dragging = false;\r\n                        activeDragButton = -1;\r\n                        for (const m of cache.gizmoMeshes) {\r\n                            m.material = cache.dragBehavior.enabled ? cache.material : cache.disableMaterial;\r\n                            if ((m as LinesMesh).color) {\r\n                                (m as LinesMesh).color = cache.material.diffuseColor;\r\n                            }\r\n                        }\r\n                    });\r\n                } else if (pointerInfo.type === PointerEventTypes.POINTERMOVE) {\r\n                    // On Hover Logic\r\n                    if (dragging) {\r\n                        return;\r\n                    }\r\n                    gizmoAxisCache.forEach((cache) => {\r\n                        if (cache.colliderMeshes && cache.gizmoMeshes) {\r\n                            const isHovered = cache.colliderMeshes?.indexOf(pointerInfo?.pickInfo?.pickedMesh as Mesh) != -1;\r\n                            const material = cache.dragBehavior.enabled ? (isHovered || cache.active ? cache.hoverMaterial : cache.material) : cache.disableMaterial;\r\n                            for (const m of cache.gizmoMeshes) {\r\n                                m.material = material;\r\n                                if ((m as LinesMesh).color) {\r\n                                    (m as LinesMesh).color = material.diffuseColor;\r\n                                }\r\n                            }\r\n                        }\r\n                    });\r\n                } else if (pointerInfo.type === PointerEventTypes.POINTERDOWN) {\r\n                    // On Mouse Down\r\n                    // If user Clicked Gizmo\r\n                    if (gizmoAxisCache.has(pointerInfo.pickInfo.pickedMesh?.parent as Mesh)) {\r\n                        dragging = true;\r\n                        activeDragButton = pointerInfo.event.button;\r\n                        const statusMap = gizmoAxisCache.get(pointerInfo.pickInfo.pickedMesh?.parent as Mesh);\r\n                        statusMap!.active = true;\r\n                        gizmoAxisCache.forEach((cache) => {\r\n                            const isHovered = cache.colliderMeshes?.indexOf(pointerInfo?.pickInfo?.pickedMesh as Mesh) != -1;\r\n                            const material = (isHovered || cache.active) && cache.dragBehavior.enabled ? cache.hoverMaterial : cache.disableMaterial;\r\n                            for (const m of cache.gizmoMeshes) {\r\n                                m.material = material;\r\n                                if ((m as LinesMesh).color) {\r\n                                    (m as LinesMesh).color = material.diffuseColor;\r\n                                }\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            }\r\n        });\r\n\r\n        return pointerObserver;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public dispose() {\r\n        this._rootMesh.dispose();\r\n        if (this._beforeRenderObserver) {\r\n            this.gizmoLayer.utilityLayerScene.onBeforeRenderObservable.remove(this._beforeRenderObserver);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAE/E,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAI3C,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAIzE,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAI5D,OAAO,EAAE,KAAK,EAAE,MAAM,iBAAiB,CAAC;;;;;;;AA0BxC,IAAkB,gBAKjB;AALD,CAAA,SAAkB,gBAAgB;IAC9B,oCAAA,EAAsC,CACtC,gBAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAM,CAAA;IACN,wCAAA,EAA0C,CAC1C,gBAAA,CAAA,gBAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;AACT,CAAC,EALiB,gBAAgB,IAAA,CAAhB,gBAAgB,GAAA,CAAA,CAAA,GAKjC;AAKD,IAAkB,oBAGjB;AAHD,CAAA,SAAkB,oBAAoB;IAClC,oBAAA,CAAA,oBAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;IACL,oBAAA,CAAA,oBAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;AACT,CAAC,EAHiB,oBAAoB,IAAA,CAApB,oBAAoB,GAAA,CAAA,CAAA,GAGrC;AAoEK,MAAO,KAAK;IAgCd;;OAEG,CACH,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAMD;;;OAGG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAW,YAAY,CAAC,KAAK,EAAA;QACzB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IACD;;;OAGG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAW,YAAY,CAAC,KAAK,EAAA;QACzB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;;OAGG,CACI,aAAa,CAAC,IAAU,EAAA;QAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACvD,4CAA4C;YAC5C,MAAM,wIAAwI,CAAC;QACnJ,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;QACjD,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;YACvB,CAAC,CAAC,OAAO,EAAE,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED;;;;OAIG,CACH,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAW,uBAAuB,CAAC,KAAgC,EAAA;QAC/D,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;IAC1C,CAAC;IAQD;;;OAGG,CACH,IAAW,sCAAsC,CAAC,KAAc,EAAA;QAC5D,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC;IACzD,CAAC;IACD,IAAW,sCAAsC,GAAA;QAC7C,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IACD;;OAEG,CACH,IAAW,sCAAsC,CAAC,KAAc,EAAA;QAC5D,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC;IACzD,CAAC;IACD,IAAW,sCAAsC,GAAA;QAC7C,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED;;;OAGG,CACH,IAAW,WAAW,CAAC,KAAuB,EAAA;QAC1C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IACD,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG,CACH,IAAW,eAAe,CAAC,eAAqC,EAAA;QAC5D,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,MAAM,KAAK,GAAG,eAAe,IAAA,EAAA,8BAAA,EAA8B,CAAC;QAC5D,IAAI,CAAC,sCAAsC,GAAG,KAAK,CAAC;QACpD,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;IACvD,CAAC;IAED,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG,CAEH,IAAW,WAAW,CAAC,KAAc,EAAA;QACjC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IACD,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,6DAA6D;IACnD,oBAAoB,CAAC,KAAqB,EAAA,CAAG,CAAC;IAqBxD;;;OAGG,CACH,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED,IAAW,wBAAwB,CAAC,wBAA8C,EAAA;QAC9E,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;IAC9D,CAAC;IAED;;OAEG,CACO,OAAO,GAAA;QACb,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC;YACtC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,aAAa,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;YAC3D,CAAC;YAED,WAAW;YACX,IAAI,IAAI,CAAC,sCAAsC,EAAE,CAAC;gBAC9C,IAAI,IAAI,CAAC,WAAW,IAAA,EAAA,0BAAA,EAA0B,KAAoB,aAAc,CAAC,qBAAqB,EAAE,CAAC;oBACrG,MAAM,QAAQ,GAAmB,aAAc,CAAC,qBAAqB,EAAE,CAAC;oBACxE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC/C,CAAC,MAAM,CAAC;oBACJ,MAAM,GAAG,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACrD,MAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC9D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC/C,CAAC;YACL,CAAC;YAED,WAAW;YACX,IAAI,IAAI,CAAC,sCAAsC,EAAE,CAAC;gBAC9C,MAAM,aAAa,GACR,aAAc,CAAC,OAAO,IAC7B,aAAa,CAAC,YAAY,EAAE,KAAK,cAAc,IAC/C,aAAa,CAAC,YAAY,EAAE,KAAK,eAAe,IAChD,aAAa,CAAC,YAAY,EAAE,KAAK,eAAe,CAAC;gBACrD,MAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAE,aAA+B,CAAC,CAAC,CAAC,SAAS,CAAC;gBACnF,aAAa,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAmB,EAAE,SAAS,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBACtJ,IAAI,CAAC,SAAS,CAAC,kBAAmB,CAAC,SAAS,EAAE,CAAC;YACnD,CAAC,MAAM,CAAC;gBACJ,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACjC,IAAI,CAAC,SAAS,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAChF,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,SAAS,CAAC,kBAAmB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvD,CAAC;YACL,CAAC;YAED,QAAQ;YACR,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAa,CAAC;gBACrE,MAAM,cAAc,GAAG,YAAY,CAAC,cAAc,CAAC;gBACnD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,cAAc,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7E,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;gBAC5B,IAAI,YAAY,CAAC,IAAI,gKAAI,SAAM,CAAC,mBAAmB,EAAE,CAAC;oBAClD,IAAI,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;wBACpD,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC,WAAW,CAAC;wBACrE,KAAK,IAAI,WAAW,CAAC;oBACzB,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,MAAM,UAAU,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC,mKAAC,UAAO,CAAC,0BAA0B,CAAC,CAAC,mKAAC,UAAO,CAAC,yBAAyB,CAAC;oBACzI,MAAM,SAAS,GAAG,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;oBACxD,KAAK,sKAAI,UAAO,CAAC,GAAG,CAAC,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;gBAC3D,CAAC;gBACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAErC,sDAAsD;gBACtD,IAAI,aAAa,CAAC,0BAA0B,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;oBAC3E,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACnC,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACnD,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,EAAE,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;8KACnH,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxH,CAAC;IACL,CAAC;IAED;;;;;;;OAOG,CACO,yBAAyB,CAAC,SAAwB,EAAE,WAAmB,EAAE,MAAc,EAAA;QAC7F,IAAI,SAAS,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;YAChF,SAAS,CAAC,cAAc,EAAE,CAAC,WAAW,mKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;8KAC7D,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YACxD,OAAO;QACX,CAAC;QACD,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACjC,CAAC;IACD;;OAEG,CACO,cAAc,GAAA;QACpB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QAED,IAAa,IAAI,CAAC,aAAc,CAAC,SAAS,EAAE,CAAC;YACzC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAuB,CAAC;YAC5C,IAAI,WAAW,CAAC;YAChB,IAAI,aAAa,CAAC;YAClB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM,SAAS,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBAClD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,aAAa,CAAC,SAAS,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/E,WAAW,GAAG,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,MAAM,CAAC;gBACJ,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;YAClD,CAAC;YAED,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,CAAC;gBACzC,0LAA0L;gBAC1L,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,WAAW,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjF,aAAa,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,MAAM,CAAC;gBACJ,aAAa,GAAG,WAAW,CAAC;YAChC,CAAC;YAED,aAAa,CAAC,SAAS,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,mKAAE,cAAU,CAAC,UAAU,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAEhG,MAAM,oBAAoB,GACtB,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,YAAY,IAClD,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,WAAW,IACjD,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,iBAAiB,IACvD,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,cAAc,IACpD,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,aAAa,IACnD,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,iBAAiB,CAAC;YAE5D,IAAI,oBAAoB,EAAE,CAAC;gBACvB,MAAM,YAAY,GAAG,IAAI,CAAC,aAA6B,CAAC;gBACxD,YAAY,CAAC,QAAQ,qKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;gBAEjE,IAAI,YAAY,CAAC,kBAAkB,EAAE,CAAC;oBAClC,YAAY,CAAC,kBAAkB,CAAC,QAAQ,mKAAC,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnE,YAAY,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;gBAChD,CAAC;YACL,CAAC;YAED,MAAM,CAAC,QAAQ,CAAC,QAAQ,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,MAAM,IACI,IAAI,CAAC,aAAc,CAAC,OAAO,IAClC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,cAAc,IACpD,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,eAAe,IACrD,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,eAAe,EACvD,CAAC;YACC,MAAM,SAAS,GAAG,IAAI,CAAC,aAA8B,CAAC;YACtD,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,SAAS,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,QAAQ,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACtC,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBACzD,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBACvE,MAAM,iBAAiB,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAC;gBACvE,iBAAiB,CAAC,SAAS,mKACvB,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,oKACrB,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EACxB,SAAS,CAAC,QAAQ,EAClB,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAC7C,KAAK,CAAC,kBAAkB,CAC3B,CAAC;kLACF,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;gBACrC,IAAI,SAAS,CAAC,kBAAkB,EAAE,EAAE,CAAC;oBACjC,sDAAsD;oBACtD,+CAA+C;oBAC/C,MAAM,CAAC,qKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;sLACnC,aAAU,CAAC,yBAAyB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAE1G,MAAM,WAAW,oKAAG,cAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;sLACzC,SAAM,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;oBAEhG,MAAM,cAAc,oKAAG,cAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC5C,CAAC,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;oBAEnC,MAAM,WAAW,GAAG,SAAS,CAAC,cAAc,EAAE,CAAC;oBAC/C,MAAM,cAAc,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC5C,WAAW,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;oBAExC,WAAW,CAAC,aAAa,CAAC,WAAW,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;sLAC7D,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,cAAc,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;sLACzE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,cAAc,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;sLAEzE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,mBAAmB,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEhE,SAAS,CAAC,QAAQ,CAAC,eAAe,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,MAAM,iBAAiB,GAAG,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;gBAC9F,iBAAiB,CAAC,SAAS,mKACvB,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,oKACrB,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EACxB,SAAS,CAAC,QAAQ,EAClB,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAC7C,KAAK,CAAC,kBAAkB,CAC3B,CAAC;YACN,CAAC;8KACD,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC,kBAAkB,CAAC,CAAC;YACvE,SAAS,CAAC,OAAO,CAAC,QAAQ,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;gBAC3B,IAAI,SAAS,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,SAAS,CAAC,kBAAkB,CAAC,QAAQ,mKAAC,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChE,SAAS,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;gBAC7C,CAAC,MAAM,CAAC;oBACJ,SAAS,CAAC,QAAQ,oKAAG,cAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;gBAClE,CAAC;YACL,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,KAAK,MAAM,EAAE,CAAC;YACtD,MAAM,IAAI,GAAG,IAAI,CAAC,aAAqB,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAEhC,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,SAAS,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,eAAe,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC7C,MAAM,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBAC/C,IAAI,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;gBAChE,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YACnC,CAAC,MAAM,CAAC;gBACJ,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YACzC,CAAC;YACD,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC,MAAM,CAAC;YACJ,MAAM,KAAK,GAAG,IAAI,CAAC,aAA4B,CAAC;YAChD,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBAClB,MAAM,IAAI,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC/B,IAAI,IAAI,+JAAK,QAAK,CAAC,4BAA4B,IAAI,IAAI,+JAAK,QAAK,CAAC,qBAAqB,IAAI,IAAI,+JAAK,QAAK,CAAC,sBAAsB,EAAE,CAAC;oBAC/H,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;oBAE5B,IAAI,MAAM,EAAE,CAAC;wBACT,MAAM,SAAS,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBACvC,MAAM,eAAe,qKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBAC7C,MAAM,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;wBAC/C,KAAK,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;wBACjE,eAAe,CAAC,SAAS,CAAC,SAAS,oKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1F,CAAC,MAAM,CAAC;wBACJ,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,oKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1G,CAAC;oBACD,iDAAiD;oBACjD,KAAK,CAAC,QAAQ,GAAG,sKAAI,UAAO,CAAC,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxG,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;wBAClB,KAAK,CAAC,SAAS,GAAG,sKAAI,UAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC3F,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACO,qBAAqB,CAAC,WAAmB,EAAE,QAA0B,EAAA;QAC3E,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,MAAM,CAAC,IAAI,WAAW,CAAE,CAAC;gBAC1B,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBACtB,IAAgB,CAAE,CAAC,KAAK,EAAE,CAAC;oBACX,CAAE,CAAC,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC;gBACjD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,wBAAwB,CAAC,UAAgC,EAAE,cAAyC,EAAA;QAC9G,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAI,cAAc,GAAG,KAAK,CAAC;QAE3B,MAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACzF,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBACvB,wEAAwE;gBACxE,qEAAqE;gBACrE,4CAA4C;gBAC5C,cAAc,GAAG,QAAQ,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,gBAAgB,CAAC;gBAE9G,IAAI,cAAc,IAAI,WAAW,CAAC,IAAI,uKAAK,oBAAiB,CAAC,SAAS,EAAE,CAAC;oBACrE,cAAc;oBAEd,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;wBAC7B,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;wBACrB,QAAQ,GAAG,KAAK,CAAC;wBACjB,gBAAgB,GAAG,CAAC,CAAC,CAAC;wBACtB,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,WAAW,CAAE,CAAC;4BAChC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC;4BACjF,IAAK,CAAe,CAAC,KAAK,EAAE,CAAC;gCACxB,CAAe,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC;4BACzD,CAAC;wBACL,CAAC;oBACL,CAAC,CAAC,CAAC;gBACP,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,uKAAK,oBAAiB,CAAC,WAAW,EAAE,CAAC;oBAC5D,iBAAiB;oBACjB,IAAI,QAAQ,EAAE,CAAC;wBACX,OAAO;oBACX,CAAC;oBACD,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;wBAC7B,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;;4BAC5C,MAAM,SAAS,mCAAS,cAAc,0DAApB,KAAK,iBAAiB,OAAO,gGAAc,QAAQ,0DAArB,WAAW,WAAY,UAAkB,CAAC,KAAI,CAAC,CAAC,CAAC;4BACjG,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,AAAC,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,AAAC,KAAK,CAAC,eAAe,CAAC;4BACzI,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,WAAW,CAAE,CAAC;gCAChC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;gCACtB,IAAK,CAAe,CAAC,KAAK,EAAE,CAAC;oCACxB,CAAe,CAAC,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC;gCACnD,CAAC;4BACL,CAAC;wBACL,CAAC;oBACL,CAAC,CAAC,CAAC;gBACP,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,uKAAK,oBAAiB,CAAC,WAAW,EAAE,CAAC;;oBAC5D,gBAAgB;oBAChB,wBAAwB;oBACxB,IAAI,cAAc,CAAC,GAAG,iDAAa,QAAQ,CAAC,UAAU,cAA/B,WAAW,6EAAsB,MAAc,CAAC,EAAE,CAAC;4BAGjC,WAAW;wBAFhD,QAAQ,GAAG,IAAI,CAAC;wBAChB,gBAAgB,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC;wBAC5C,MAAM,SAAS,GAAG,cAAc,CAAC,GAAG,kDAAa,QAAQ,CAAC,UAAU,wGAAE,MAAc,CAAC,CAAC;wBACtF,SAAU,CAAC,MAAM,GAAG,IAAI,CAAC;wBACzB,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;;4BAC7B,MAAM,SAAS,6BAAG,KAAK,CAAC,cAAc,gFAAE,OAAO,4DAAC,WAAW,yBAAE,QAAQ,gFAAE,UAAkB,CAAC,KAAI,CAAC,CAAC,CAAC;4BACjG,MAAM,QAAQ,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC;4BACzH,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,WAAW,CAAE,CAAC;gCAChC,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;gCACtB,IAAK,CAAe,CAAC,KAAK,EAAE,CAAC;oCACxB,CAAe,CAAC,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC;gCACnD,CAAC;4BACL,CAAC;wBACL,CAAC,CAAC,CAAC;oBACP,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAClG,CAAC;IACL,CAAC;IAxXD;;;OAGG,CACH,YACI,0DAAA,EAA4D,CACrD,yLAAmC,uBAAoB,CAAC,mBAAmB,CAAA;QAA3E,IAAA,CAAA,UAAU,GAAV,UAAU,CAAiE;QAxL5E,IAAA,CAAA,aAAa,GAA2B,IAAI,CAAC;QAC7C,IAAA,CAAA,aAAa,GAAmB,IAAI,CAAC;QACrC,IAAA,CAAA,yBAAyB,GAAyB,IAAI,CAAC;QAEjE;;WAEG,CACO,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAE1B;;WAEG,CACO,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAiC7B;;WAEG,CACO,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QA4DvB,IAAA,CAAA,uCAAuC,GAAG,IAAI,CAAC;QAC/C,IAAA,CAAA,uCAAuC,GAAG,IAAI,CAAC;QAC/C,IAAA,CAAA,YAAY,GAAA,EAAA,2BAAA,GAA2B;QACvC,IAAA,CAAA,YAAY,GAAG,IAAI,CAAC;QACpB,IAAA,CAAA,gBAAgB,GAAA,EAAA,8BAAA,GAA8B;QA2D9C,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAK9B,IAAA,CAAA,0BAA0B,qKAAG,SAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAU3D,IAAI,CAAC,SAAS,GAAG,IAAI,gKAAI,CAAC,eAAe,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC,kBAAkB,qKAAG,aAAU,CAAC,QAAQ,EAAE,CAAC;QAE1D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC7F,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;;AAlLD;;;GAGG,CACW,MAAA,eAAe,GAAG,KAAK,AAAR,CAAS;AAEtC;;;;GAIG,CACW,MAAA,kBAAkB,GAAG,IAAI,AAAP,CAAQ", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/axisDragGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/axisDragGizmo.ts"], "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport type { Vector3 } from \"../Maths/math.vector\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Node } from \"../node\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { CreateCylinder } from \"../Meshes/Builders/cylinderBuilder\";\r\nimport { PointerDragBehavior } from \"../Behaviors/Meshes/pointerDragBehavior\";\r\nimport type { GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { PositionGizmo } from \"./positionGizmo\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { TmpVectors } from \"../Maths/math.vector\";\r\n\r\n/**\r\n * Interface for axis drag gizmo\r\n */\r\nexport interface IAxisDragGizmo extends IGizmo {\r\n    /** Drag behavior responsible for the gizmos dragging interactions */\r\n    dragBehavior: PointerDragBehavior;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    onSnapObservable: Observable<{ snapDistance: number }>;\r\n    /** If the gizmo is enabled */\r\n    isEnabled: boolean;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    coloredMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    hoverMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    disableMaterial: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Single axis drag gizmo\r\n */\r\nexport class AxisDragGizmo extends Gizmo implements IAxisDragGizmo {\r\n    /**\r\n     * Drag behavior responsible for the gizmos dragging interactions\r\n     */\r\n    public dragBehavior: PointerDragBehavior;\r\n    protected _pointerObserver: Nullable<Observer<PointerInfo>> = null;\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public snapDistance = 0;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    public onSnapObservable = new Observable<{ snapDistance: number }>();\r\n\r\n    protected _isEnabled: boolean = true;\r\n    protected _parent: Nullable<PositionGizmo> = null;\r\n\r\n    protected _gizmoMesh: Mesh;\r\n    protected _coloredMaterial: StandardMaterial;\r\n    protected _hoverMaterial: StandardMaterial;\r\n    protected _disableMaterial: StandardMaterial;\r\n    protected _dragging: boolean = false;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    public get coloredMaterial() {\r\n        return this._coloredMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    public get hoverMaterial() {\r\n        return this._hoverMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    public get disableMaterial() {\r\n        return this._disableMaterial;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _CreateArrow(scene: Scene, material: StandardMaterial, thickness: number = 1, isCollider = false): TransformNode {\r\n        const arrow = new TransformNode(\"arrow\", scene);\r\n        const cylinder = CreateCylinder(\r\n            \"cylinder\",\r\n            {\r\n                diameterTop: 0,\r\n                height: 0.075,\r\n                diameterBottom: 0.0375 * (1 + (thickness - 1) / 4),\r\n                tessellation: 96,\r\n            },\r\n            scene\r\n        );\r\n        const line = CreateCylinder(\r\n            \"cylinder\",\r\n            {\r\n                diameterTop: 0.005 * thickness,\r\n                height: 0.275,\r\n                diameterBottom: 0.005 * thickness,\r\n                tessellation: 96,\r\n            },\r\n            scene\r\n        );\r\n\r\n        // Position arrow pointing in its drag axis\r\n        cylinder.parent = arrow;\r\n        cylinder.material = material;\r\n        cylinder.rotation.x = Math.PI / 2;\r\n        cylinder.position.z += 0.3;\r\n\r\n        line.parent = arrow;\r\n        line.material = material;\r\n        line.position.z += 0.275 / 2;\r\n        line.rotation.x = Math.PI / 2;\r\n\r\n        if (isCollider) {\r\n            line.visibility = 0;\r\n            cylinder.visibility = 0;\r\n        }\r\n        return arrow;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _CreateArrowInstance(scene: Scene, arrow: TransformNode): TransformNode {\r\n        const instance = new TransformNode(\"arrow\", scene);\r\n        for (const mesh of arrow.getChildMeshes()) {\r\n            const childInstance = (mesh as Mesh).createInstance(mesh.name);\r\n            childInstance.parent = instance;\r\n        }\r\n        return instance;\r\n    }\r\n\r\n    /**\r\n     * Creates an AxisDragGizmo\r\n     * @param dragAxis The axis which the gizmo will be able to drag on\r\n     * @param color The color of the gizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param parent\r\n     * @param thickness display gizmo axis thickness\r\n     * @param hoverColor The color of the gizmo when hovering over and dragging\r\n     * @param disableColor The Color of the gizmo when its disabled\r\n     */\r\n    constructor(\r\n        dragAxis: Vector3,\r\n        color: Color3 = Color3.Gray(),\r\n        gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer,\r\n        parent: Nullable<PositionGizmo> = null,\r\n        thickness: number = 1,\r\n        hoverColor: Color3 = Color3.Yellow(),\r\n        disableColor: Color3 = Color3.Gray()\r\n    ) {\r\n        super(gizmoLayer);\r\n        this._parent = parent;\r\n\r\n        // Create Material\r\n        this._coloredMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._coloredMaterial.diffuseColor = color;\r\n        this._coloredMaterial.specularColor = color.subtract(new Color3(0.1, 0.1, 0.1));\r\n\r\n        this._hoverMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._hoverMaterial.diffuseColor = hoverColor;\r\n\r\n        this._disableMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._disableMaterial.diffuseColor = disableColor;\r\n        this._disableMaterial.alpha = 0.4;\r\n\r\n        // Build Mesh + Collider\r\n        const arrow = AxisDragGizmo._CreateArrow(gizmoLayer.utilityLayerScene, this._coloredMaterial, thickness);\r\n        const collider = AxisDragGizmo._CreateArrow(gizmoLayer.utilityLayerScene, this._coloredMaterial, thickness + 4, true);\r\n\r\n        // Add to Root Node\r\n        this._gizmoMesh = new Mesh(\"\", gizmoLayer.utilityLayerScene);\r\n        this._gizmoMesh.addChild(arrow as Mesh);\r\n        this._gizmoMesh.addChild(collider as Mesh);\r\n\r\n        this._gizmoMesh.lookAt(this._rootMesh.position.add(dragAxis));\r\n        this._gizmoMesh.scaling.scaleInPlace(1 / 3);\r\n        this._gizmoMesh.parent = this._rootMesh;\r\n\r\n        let currentSnapDragDistance = 0;\r\n        const tmpSnapEvent = { snapDistance: 0 };\r\n        // Add drag behavior to handle events when the gizmo is dragged\r\n        this.dragBehavior = new PointerDragBehavior({ dragAxis: dragAxis });\r\n        this.dragBehavior.moveAttached = false;\r\n        this.dragBehavior.updateDragPlane = false;\r\n        this._rootMesh.addBehavior(this.dragBehavior);\r\n\r\n        this.dragBehavior.onDragObservable.add((event) => {\r\n            if (this.attachedNode) {\r\n                // Keep world translation and use it to update world transform\r\n                // if the node has parent, the local transform properties (position, rotation, scale)\r\n                // will be recomputed in _matrixChanged function\r\n\r\n                let matrixChanged: boolean = false;\r\n                // Snapping logic\r\n                if (this.snapDistance == 0) {\r\n                    this.attachedNode.getWorldMatrix().getTranslationToRef(TmpVectors.Vector3[2]);\r\n                    TmpVectors.Vector3[2].addInPlace(event.delta);\r\n                    if (this.dragBehavior.validateDrag(TmpVectors.Vector3[2])) {\r\n                        if ((this.attachedNode as any).position) {\r\n                            // Required for nodes like lights\r\n                            (this.attachedNode as any).position.addInPlaceFromFloats(event.delta.x, event.delta.y, event.delta.z);\r\n                        }\r\n\r\n                        // use _worldMatrix to not force a matrix update when calling GetWorldMatrix especially with Cameras\r\n                        this.attachedNode.getWorldMatrix().addTranslationFromFloats(event.delta.x, event.delta.y, event.delta.z);\r\n                        this.attachedNode.updateCache();\r\n                        matrixChanged = true;\r\n                    }\r\n                } else {\r\n                    currentSnapDragDistance += event.dragDistance;\r\n                    if (Math.abs(currentSnapDragDistance) > this.snapDistance) {\r\n                        const dragSteps = Math.floor(Math.abs(currentSnapDragDistance) / this.snapDistance);\r\n                        currentSnapDragDistance = currentSnapDragDistance % this.snapDistance;\r\n                        event.delta.normalizeToRef(TmpVectors.Vector3[1]);\r\n                        TmpVectors.Vector3[1].scaleInPlace(this.snapDistance * dragSteps);\r\n\r\n                        this.attachedNode.getWorldMatrix().getTranslationToRef(TmpVectors.Vector3[2]);\r\n                        TmpVectors.Vector3[2].addInPlace(TmpVectors.Vector3[1]);\r\n                        if (this.dragBehavior.validateDrag(TmpVectors.Vector3[2])) {\r\n                            this.attachedNode.getWorldMatrix().addTranslationFromFloats(TmpVectors.Vector3[1].x, TmpVectors.Vector3[1].y, TmpVectors.Vector3[1].z);\r\n                            this.attachedNode.updateCache();\r\n                            tmpSnapEvent.snapDistance = this.snapDistance * dragSteps * Math.sign(currentSnapDragDistance);\r\n                            this.onSnapObservable.notifyObservers(tmpSnapEvent);\r\n                            matrixChanged = true;\r\n                        }\r\n                    }\r\n                }\r\n                if (matrixChanged) {\r\n                    this._matrixChanged();\r\n                }\r\n            }\r\n        });\r\n        this.dragBehavior.onDragStartObservable.add(() => {\r\n            this._dragging = true;\r\n        });\r\n        this.dragBehavior.onDragEndObservable.add(() => {\r\n            this._dragging = false;\r\n        });\r\n\r\n        const light = gizmoLayer._getSharedGizmoLight();\r\n        light.includedOnlyMeshes = light.includedOnlyMeshes.concat(this._rootMesh.getChildMeshes(false));\r\n\r\n        const cache: GizmoAxisCache = {\r\n            gizmoMeshes: arrow.getChildMeshes(),\r\n            colliderMeshes: collider.getChildMeshes(),\r\n            material: this._coloredMaterial,\r\n            hoverMaterial: this._hoverMaterial,\r\n            disableMaterial: this._disableMaterial,\r\n            active: false,\r\n            dragBehavior: this.dragBehavior,\r\n        };\r\n        this._parent?.addToAxisCache(collider as Mesh, cache);\r\n\r\n        this._pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (this._customMeshSet) {\r\n                return;\r\n            }\r\n            this._isHovered = !!(cache.colliderMeshes.indexOf(<Mesh>pointerInfo?.pickInfo?.pickedMesh) != -1);\r\n            if (!this._parent) {\r\n                const material = this.dragBehavior.enabled ? (this._isHovered || this._dragging ? this._hoverMaterial : this._coloredMaterial) : this._disableMaterial;\r\n                this._setGizmoMeshMaterial(cache.gizmoMeshes, material);\r\n            }\r\n        });\r\n\r\n        this.dragBehavior.onEnabledObservable.add((newState) => {\r\n            this._setGizmoMeshMaterial(cache.gizmoMeshes, newState ? cache.material : cache.disableMaterial);\r\n        });\r\n    }\r\n\r\n    protected override _attachedNodeChanged(value: Nullable<Node>) {\r\n        if (this.dragBehavior) {\r\n            this.dragBehavior.enabled = value ? true : false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the gizmo is enabled\r\n     */\r\n    public set isEnabled(value: boolean) {\r\n        this._isEnabled = value;\r\n        if (!value) {\r\n            this.attachedMesh = null;\r\n            this.attachedNode = null;\r\n        } else {\r\n            if (this._parent) {\r\n                this.attachedMesh = this._parent.attachedMesh;\r\n                this.attachedNode = this._parent.attachedNode;\r\n            }\r\n        }\r\n    }\r\n\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public override dispose() {\r\n        this.onSnapObservable.clear();\r\n        this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(this._pointerObserver);\r\n        this.dragBehavior.detach();\r\n        if (this._gizmoMesh) {\r\n            this._gizmoMesh.dispose();\r\n        }\r\n        const mats = [this._coloredMaterial, this._hoverMaterial, this._disableMaterial];\r\n        for (const matl of mats) {\r\n            if (matl) {\r\n                matl.dispose();\r\n            }\r\n        }\r\n        super.dispose();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAIhD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,cAAc,EAAE,MAAM,oCAAoC,CAAC;AACpE,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAE9E,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAGjE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;;;;;;;;;;;AA6B5C,MAAO,aAAc,mKAAQ,QAAK;IAyBpC,0EAAA,EAA4E,CAC5E,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,4DAAA,EAA8D,CAC9D,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,mEAAA,EAAqE,CACrE,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG,CACI,MAAM,CAAC,YAAY,CAAC,KAAY,EAAE,QAA0B,EAA2C;wBAAzC,iEAAoB,CAAC,eAAE,UAAU,uDAAG,KAAK;QAC1G,MAAM,KAAK,GAAG,sKAAI,gBAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,QAAQ,uLAAG,iBAAA,AAAc,EAC3B,UAAU,EACV;YACI,WAAW,EAAE,CAAC;YACd,MAAM,EAAE,KAAK;YACb,cAAc,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAClD,YAAY,EAAE,EAAE;SACnB,EACD,KAAK,CACR,CAAC;QACF,MAAM,IAAI,uLAAG,iBAAA,AAAc,EACvB,UAAU,EACV;YACI,WAAW,EAAE,KAAK,GAAG,SAAS;YAC9B,MAAM,EAAE,KAAK;YACb,cAAc,EAAE,KAAK,GAAG,SAAS;YACjC,YAAY,EAAE,EAAE;SACnB,EACD,KAAK,CACR,CAAC;QAEF,2CAA2C;QAC3C,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;QACxB,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC7B,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAClC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,GAAG,CAAC;QAE3B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAE9B,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpB,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;QAC5B,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG,CACI,MAAM,CAAC,oBAAoB,CAAC,KAAY,EAAE,KAAoB,EAAA;QACjE,MAAM,QAAQ,GAAG,sKAAI,gBAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACnD,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,cAAc,EAAE,CAAE,CAAC;YACxC,MAAM,aAAa,GAAI,IAAa,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/D,aAAa,CAAC,MAAM,GAAG,QAAQ,CAAC;QACpC,CAAC;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IA4IkB,oBAAoB,CAAC,KAAqB,EAAA;QACzD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACrD,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,CAAC,KAAc,EAAA;QAC/B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;gBAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;YAClD,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QACD,MAAM,IAAI,GAAG;YAAC,IAAI,CAAC,gBAAgB;YAAE,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,gBAAgB;SAAC,CAAC;QACjF,KAAK,MAAM,IAAI,IAAI,IAAI,CAAE,CAAC;YACtB,IAAI,IAAI,EAAE,CAAC;gBACP,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACL,CAAC;QACD,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IArLD;;;;;;;;;OASG,CACH,YACI,QAAiB,EACjB,QAAgB,0KAAM,CAAC,IAAI,EAAE,EAC7B,yLAAmC,uBAAoB,CAAC,mBAAmB,EAC3E,SAAkC,IAAI,EACtC,YAAoB,CAAC,EACrB,8KAAqB,SAAM,CAAC,MAAM,EAAE,EACpC,gLAAuB,SAAM,CAAC,IAAI,EAAE,CAAA;YAuGpC;QArGA,KAAK,CAAC,UAAU,CAAC,CAAC;QA9GZ,IAAA,CAAA,gBAAgB,GAAoC,IAAI,CAAC;QACnE;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACxB;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAAG,iKAAI,aAAU,EAA4B,CAAC;QAE3D,IAAA,CAAA,UAAU,GAAY,IAAI,CAAC;QAC3B,IAAA,CAAA,OAAO,GAA4B,IAAI,CAAC;QAMxC,IAAA,CAAA,SAAS,GAAY,KAAK,CAAC;QA6FjC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,kBAAkB;QAClB,IAAI,CAAC,gBAAgB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAEhF,IAAI,CAAC,cAAc,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,UAAU,CAAC;QAE9C,IAAI,CAAC,gBAAgB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,YAAY,CAAC;QAClD,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,GAAG,CAAC;QAElC,wBAAwB;QACxB,MAAM,KAAK,GAAG,aAAa,CAAC,YAAY,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;QACzG,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;QAEtH,mBAAmB;QACnB,IAAI,CAAC,UAAU,GAAG,IAAI,gKAAI,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAa,CAAC,CAAC;QACxC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAgB,CAAC,CAAC;QAE3C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAExC,IAAI,uBAAuB,GAAG,CAAC,CAAC;QAChC,MAAM,YAAY,GAAG;YAAE,YAAY,EAAE,CAAC;QAAA,CAAE,CAAC;QACzC,+DAA+D;QAC/D,IAAI,CAAC,YAAY,GAAG,yLAAI,sBAAmB,CAAC;YAAE,QAAQ,EAAE,QAAQ;QAAA,CAAE,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,KAAK,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9C,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,8DAA8D;gBAC9D,qFAAqF;gBACrF,gDAAgD;gBAEhD,IAAI,aAAa,GAAY,KAAK,CAAC;gBACnC,iBAAiB;gBACjB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;oBACzB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,mBAAmB,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9E,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC9C,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACxD,IAAK,IAAI,CAAC,YAAoB,CAAC,QAAQ,EAAE,CAAC;4BACtC,iCAAiC;4BAChC,IAAI,CAAC,YAAoB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC1G,CAAC;wBAED,oGAAoG;wBACpG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,wBAAwB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBACzG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;wBAChC,aAAa,GAAG,IAAI,CAAC;oBACzB,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,uBAAuB,IAAI,KAAK,CAAC,YAAY,CAAC;oBAC9C,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBACxD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;wBACpF,uBAAuB,GAAG,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC;wBACtE,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;0LAClD,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,CAAC;wBAElE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,mBAAmB,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;0LAC9E,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxD,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;4BACxD,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,wBAAwB,kKAAC,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACvI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;4BAChC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;4BAC/F,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;4BACpD,aAAa,GAAG,IAAI,CAAC;wBACzB,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,IAAI,aAAa,EAAE,CAAC;oBAChB,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC1B,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC3C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAChD,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QAEjG,MAAM,KAAK,GAAmB;YAC1B,WAAW,EAAE,KAAK,CAAC,cAAc,EAAE;YACnC,cAAc,EAAE,QAAQ,CAAC,cAAc,EAAE;YACzC,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,IAAI,CAAC,YAAY;SAClC,CAAC;6BACE,CAAC,OAAO,gEAAE,cAAc,CAAC,QAAgB,EAAE,KAAK,CAAC,CAAC;QAEtD,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;;YACzF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,OAAO;YACX,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,gGAAoB,QAAQ,0DAArB,WAAW,WAAY,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,gBAAgB,CAAC;gBACvJ,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;IACP,CAAC;CA8CJ", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/axisScaleGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/axisScaleGizmo.ts"], "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { Vector3, Matrix, TmpVectors } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Node } from \"../node\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport type { LinesMesh } from \"../Meshes/linesMesh\";\r\nimport { CreateBox } from \"../Meshes/Builders/boxBuilder\";\r\nimport { CreateCylinder } from \"../Meshes/Builders/cylinderBuilder\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { PointerDragBehavior } from \"../Behaviors/Meshes/pointerDragBehavior\";\r\nimport type { GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport type { ScaleGizmo } from \"./scaleGizmo\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\n\r\n/**\r\n * Interface for axis scale gizmo\r\n */\r\nexport interface IAxisScaleGizmo extends IGizmo {\r\n    /** Drag behavior responsible for the gizmos dragging interactions */\r\n    dragBehavior: PointerDragBehavior;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /** Incremental snap scaling. When true, with a snapDistance of 0.1, scaling will be 1.1,1.2,1.3 instead of, when false: 1.1,1.21,1.33,... */\r\n    incrementalSnap: boolean;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    onSnapObservable: Observable<{ snapDistance: number }>;\r\n    /** If the scaling operation should be done on all axis */\r\n    uniformScaling: boolean;\r\n    /** Custom sensitivity value for the drag strength */\r\n    sensitivity: number;\r\n    /** The magnitude of the drag strength (scaling factor) */\r\n    dragScale: number;\r\n    /** If the gizmo is enabled */\r\n    isEnabled: boolean;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    coloredMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    hoverMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    disableMaterial: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Single axis scale gizmo\r\n */\r\nexport class AxisScaleGizmo extends Gizmo implements IAxisScaleGizmo {\r\n    /**\r\n     * Drag behavior responsible for the gizmos dragging interactions\r\n     */\r\n    public dragBehavior: PointerDragBehavior;\r\n    protected _pointerObserver: Nullable<Observer<PointerInfo>> = null;\r\n    /**\r\n     * Scale distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public snapDistance = 0;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    public onSnapObservable = new Observable<{ snapDistance: number }>();\r\n    /**\r\n     * If the scaling operation should be done on all axis (default: false)\r\n     */\r\n    public uniformScaling = false;\r\n    /**\r\n     * Custom sensitivity value for the drag strength\r\n     */\r\n    public sensitivity = 1;\r\n    /**\r\n     * The magnitude of the drag strength (scaling factor)\r\n     */\r\n    public dragScale = 1;\r\n\r\n    /**\r\n     * The minimal absolute scale per component. can be positive or negative but never smaller.\r\n     */\r\n    public static MinimumAbsoluteScale = Epsilon;\r\n\r\n    /**\r\n     * Incremental snap scaling (default is false). When true, with a snapDistance of 0.1, scaling will be 1.1,1.2,1.3 instead of, when false: 1.1,1.21,1.33,...\r\n     */\r\n    public incrementalSnap = false;\r\n\r\n    protected _isEnabled: boolean = true;\r\n    protected _parent: Nullable<ScaleGizmo> = null;\r\n\r\n    protected _gizmoMesh: Mesh;\r\n    protected _coloredMaterial: StandardMaterial;\r\n    protected _hoverMaterial: StandardMaterial;\r\n    protected _disableMaterial: StandardMaterial;\r\n    protected _dragging: boolean = false;\r\n    private _tmpVector = new Vector3(0, 0, 0);\r\n    private _incrementalStartupValue = Vector3.Zero();\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    public get coloredMaterial() {\r\n        return this._coloredMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    public get hoverMaterial() {\r\n        return this._hoverMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    public get disableMaterial() {\r\n        return this._disableMaterial;\r\n    }\r\n\r\n    /**\r\n     * Creates an AxisScaleGizmo\r\n     * @param dragAxis The axis which the gizmo will be able to scale on\r\n     * @param color The color of the gizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param parent\r\n     * @param thickness display gizmo axis thickness\r\n     * @param hoverColor The color of the gizmo when hovering over and dragging\r\n     * @param disableColor The Color of the gizmo when its disabled\r\n     */\r\n    constructor(\r\n        dragAxis: Vector3,\r\n        color: Color3 = Color3.Gray(),\r\n        gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer,\r\n        parent: Nullable<ScaleGizmo> = null,\r\n        thickness: number = 1,\r\n        hoverColor: Color3 = Color3.Yellow(),\r\n        disableColor: Color3 = Color3.Gray()\r\n    ) {\r\n        super(gizmoLayer);\r\n        this._parent = parent;\r\n        // Create Material\r\n        this._coloredMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._coloredMaterial.diffuseColor = color;\r\n        this._coloredMaterial.specularColor = color.subtract(new Color3(0.1, 0.1, 0.1));\r\n\r\n        this._hoverMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._hoverMaterial.diffuseColor = hoverColor;\r\n\r\n        this._disableMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._disableMaterial.diffuseColor = disableColor;\r\n        this._disableMaterial.alpha = 0.4;\r\n\r\n        // Build mesh + Collider\r\n        this._gizmoMesh = new Mesh(\"axis\", gizmoLayer.utilityLayerScene);\r\n        const { arrowMesh, arrowTail } = this._createGizmoMesh(this._gizmoMesh, thickness);\r\n        const collider = this._createGizmoMesh(this._gizmoMesh, thickness + 4, true);\r\n\r\n        this._gizmoMesh.lookAt(this._rootMesh.position.add(dragAxis));\r\n        this._rootMesh.addChild(this._gizmoMesh, Gizmo.PreserveScaling);\r\n        this._gizmoMesh.scaling.scaleInPlace(1 / 3);\r\n\r\n        // Closure of initial prop values for resetting\r\n        const nodePosition = arrowMesh.position.clone();\r\n        const linePosition = arrowTail.position.clone();\r\n        const lineScale = arrowTail.scaling.clone();\r\n\r\n        const increaseGizmoMesh = (dragDistance: number) => {\r\n            const dragStrength = dragDistance * (3 / this._rootMesh.scaling.length()) * 6;\r\n\r\n            arrowMesh.position.z += dragStrength / 3.5;\r\n            arrowTail.scaling.y += dragStrength;\r\n            this.dragScale = arrowTail.scaling.y;\r\n            arrowTail.position.z = arrowMesh.position.z / 2;\r\n        };\r\n\r\n        const resetGizmoMesh = () => {\r\n            arrowMesh.position.set(nodePosition.x, nodePosition.y, nodePosition.z);\r\n            arrowTail.position.set(linePosition.x, linePosition.y, linePosition.z);\r\n            arrowTail.scaling.set(lineScale.x, lineScale.y, lineScale.z);\r\n            this.dragScale = arrowTail.scaling.y;\r\n            this._dragging = false;\r\n        };\r\n\r\n        // Add drag behavior to handle events when the gizmo is dragged\r\n        this.dragBehavior = new PointerDragBehavior({ dragAxis: dragAxis });\r\n        this.dragBehavior.moveAttached = false;\r\n        this.dragBehavior.updateDragPlane = false;\r\n        this._rootMesh.addBehavior(this.dragBehavior);\r\n\r\n        let currentSnapDragDistance = 0;\r\n        let currentSnapDragDistanceIncremental = 0;\r\n\r\n        const tmpSnapEvent = { snapDistance: 0 };\r\n        this.dragBehavior.onDragObservable.add((event) => {\r\n            if (this.attachedNode) {\r\n                // Drag strength is modified by the scale of the gizmo (eg. for small objects like boombox the strength will be increased to match the behavior of larger objects)\r\n                const dragStrength = this.sensitivity * event.dragDistance * ((this.scaleRatio * 3) / this._rootMesh.scaling.length());\r\n                const tmpVector = this._tmpVector;\r\n                // Snapping logic\r\n                let snapped = false;\r\n                let dragSteps = 0;\r\n                if (this.uniformScaling) {\r\n                    tmpVector.setAll(0.57735); // 1 / sqrt(3)\r\n                } else {\r\n                    tmpVector.copyFrom(dragAxis);\r\n                }\r\n                if (this.snapDistance == 0) {\r\n                    tmpVector.scaleToRef(dragStrength, tmpVector);\r\n                } else {\r\n                    currentSnapDragDistance += dragStrength;\r\n                    currentSnapDragDistanceIncremental += dragStrength;\r\n                    const currentSnap = this.incrementalSnap ? currentSnapDragDistanceIncremental : currentSnapDragDistance;\r\n                    if (Math.abs(currentSnap) > this.snapDistance) {\r\n                        dragSteps = Math.floor(Math.abs(currentSnap) / this.snapDistance);\r\n\r\n                        if (currentSnap < 0) {\r\n                            dragSteps *= -1;\r\n                        }\r\n                        currentSnapDragDistance = currentSnapDragDistance % this.snapDistance;\r\n                        tmpVector.scaleToRef(this.snapDistance * dragSteps, tmpVector);\r\n                        snapped = true;\r\n                    } else {\r\n                        tmpVector.scaleInPlace(0);\r\n                    }\r\n                }\r\n\r\n                tmpVector.addInPlaceFromFloats(1, 1, 1);\r\n                // can't use Math.sign here because Math.sign(0) is 0 and it needs to be positive\r\n                tmpVector.x = Math.abs(tmpVector.x) < AxisScaleGizmo.MinimumAbsoluteScale ? AxisScaleGizmo.MinimumAbsoluteScale * (tmpVector.x < 0 ? -1 : 1) : tmpVector.x;\r\n                tmpVector.y = Math.abs(tmpVector.y) < AxisScaleGizmo.MinimumAbsoluteScale ? AxisScaleGizmo.MinimumAbsoluteScale * (tmpVector.y < 0 ? -1 : 1) : tmpVector.y;\r\n                tmpVector.z = Math.abs(tmpVector.z) < AxisScaleGizmo.MinimumAbsoluteScale ? AxisScaleGizmo.MinimumAbsoluteScale * (tmpVector.z < 0 ? -1 : 1) : tmpVector.z;\r\n\r\n                const transformNode = (<Mesh>this.attachedNode)._isMesh ? (this.attachedNode as TransformNode) : undefined;\r\n                if (Math.abs(this.snapDistance) > 0 && this.incrementalSnap) {\r\n                    // get current scaling\r\n                    this.attachedNode.getWorldMatrix().decompose(undefined, TmpVectors.Quaternion[0], TmpVectors.Vector3[2], Gizmo.PreserveScaling ? transformNode : undefined);\r\n                    // apply incrementaly, without taking care of current scaling value\r\n                    tmpVector.addInPlace(this._incrementalStartupValue);\r\n                    tmpVector.addInPlaceFromFloats(-1, -1, -1);\r\n                    // keep same sign or stretching close to 0 will change orientation at each drag and scaling will oscilate around 0\r\n                    tmpVector.x = Math.abs(tmpVector.x) * (this._incrementalStartupValue.x > 0 ? 1 : -1);\r\n                    tmpVector.y = Math.abs(tmpVector.y) * (this._incrementalStartupValue.y > 0 ? 1 : -1);\r\n                    tmpVector.z = Math.abs(tmpVector.z) * (this._incrementalStartupValue.z > 0 ? 1 : -1);\r\n                    Matrix.ComposeToRef(tmpVector, TmpVectors.Quaternion[0], TmpVectors.Vector3[2], TmpVectors.Matrix[1]);\r\n                } else {\r\n                    Matrix.ScalingToRef(tmpVector.x, tmpVector.y, tmpVector.z, TmpVectors.Matrix[2]);\r\n                    TmpVectors.Matrix[2].multiplyToRef(this.attachedNode.getWorldMatrix(), TmpVectors.Matrix[1]);\r\n                }\r\n\r\n                // check scaling are not out of bounds. If not, copy resulting temp matrix to node world matrix\r\n                TmpVectors.Matrix[1].decompose(TmpVectors.Vector3[1], undefined, undefined, Gizmo.PreserveScaling ? transformNode : undefined);\r\n\r\n                const maxScale = 100000;\r\n                if (Math.abs(TmpVectors.Vector3[1].x) < maxScale && Math.abs(TmpVectors.Vector3[1].y) < maxScale && Math.abs(TmpVectors.Vector3[1].z) < maxScale) {\r\n                    this.attachedNode.getWorldMatrix().copyFrom(TmpVectors.Matrix[1]);\r\n                }\r\n\r\n                // notify observers\r\n                if (snapped) {\r\n                    tmpSnapEvent.snapDistance = this.snapDistance * dragSteps;\r\n                    this.onSnapObservable.notifyObservers(tmpSnapEvent);\r\n                }\r\n                this._matrixChanged();\r\n            }\r\n        });\r\n        // On Drag Listener: to move gizmo mesh with user action\r\n        this.dragBehavior.onDragStartObservable.add(() => {\r\n            this._dragging = true;\r\n            const transformNode = (<Mesh>this.attachedNode)._isMesh ? (this.attachedNode as TransformNode) : undefined;\r\n            this.attachedNode?.getWorldMatrix().decompose(this._incrementalStartupValue, undefined, undefined, Gizmo.PreserveScaling ? transformNode : undefined);\r\n            currentSnapDragDistance = 0;\r\n            currentSnapDragDistanceIncremental = 0;\r\n        });\r\n        this.dragBehavior.onDragObservable.add((e) => increaseGizmoMesh(e.dragDistance));\r\n        this.dragBehavior.onDragEndObservable.add(resetGizmoMesh);\r\n\r\n        // Listeners for Universal Scalar\r\n        parent?.uniformScaleGizmo?.dragBehavior?.onDragObservable?.add((e) => increaseGizmoMesh(e.delta.y));\r\n        parent?.uniformScaleGizmo?.dragBehavior?.onDragEndObservable?.add(resetGizmoMesh);\r\n\r\n        const cache: GizmoAxisCache = {\r\n            gizmoMeshes: [arrowMesh, arrowTail],\r\n            colliderMeshes: [collider.arrowMesh, collider.arrowTail],\r\n            material: this._coloredMaterial,\r\n            hoverMaterial: this._hoverMaterial,\r\n            disableMaterial: this._disableMaterial,\r\n            active: false,\r\n            dragBehavior: this.dragBehavior,\r\n        };\r\n        this._parent?.addToAxisCache(this._gizmoMesh, cache);\r\n\r\n        this._pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (this._customMeshSet) {\r\n                return;\r\n            }\r\n            // axis mesh cache\r\n            let meshCache = this._parent?.getAxisCache(this._gizmoMesh);\r\n            this._isHovered = !!meshCache && !!(meshCache.colliderMeshes.indexOf(<Mesh>pointerInfo?.pickInfo?.pickedMesh) != -1);\r\n            // uniform mesh cache\r\n            meshCache = this._parent?.getAxisCache(this._rootMesh);\r\n            this._isHovered ||= !!meshCache && !!(meshCache.colliderMeshes.indexOf(<Mesh>pointerInfo?.pickInfo?.pickedMesh) != -1);\r\n            if (!this._parent) {\r\n                const material = this.dragBehavior.enabled ? (this._isHovered || this._dragging ? this._hoverMaterial : this._coloredMaterial) : this._disableMaterial;\r\n                this._setGizmoMeshMaterial(cache.gizmoMeshes, material);\r\n            }\r\n        });\r\n\r\n        this.dragBehavior.onEnabledObservable.add((newState) => {\r\n            this._setGizmoMeshMaterial(cache.gizmoMeshes, newState ? this._coloredMaterial : this._disableMaterial);\r\n        });\r\n\r\n        const light = gizmoLayer._getSharedGizmoLight();\r\n        light.includedOnlyMeshes = light.includedOnlyMeshes.concat(this._rootMesh.getChildMeshes());\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Create Geometry for Gizmo\r\n     * @param parentMesh\r\n     * @param thickness\r\n     * @param isCollider\r\n     * @returns the gizmo mesh\r\n     */\r\n    protected _createGizmoMesh(parentMesh: AbstractMesh, thickness: number, isCollider = false) {\r\n        const arrowMesh = CreateBox(\"yPosMesh\", { size: 0.4 * (1 + (thickness - 1) / 4) }, this.gizmoLayer.utilityLayerScene);\r\n        const arrowTail = CreateCylinder(\r\n            \"cylinder\",\r\n            { diameterTop: 0.005 * thickness, height: 0.275, diameterBottom: 0.005 * thickness, tessellation: 96 },\r\n            this.gizmoLayer.utilityLayerScene\r\n        );\r\n\r\n        // Position arrow pointing in its drag axis\r\n        arrowMesh.scaling.scaleInPlace(0.1);\r\n        arrowMesh.material = this._coloredMaterial;\r\n        arrowMesh.rotation.x = Math.PI / 2;\r\n        arrowMesh.position.z += 0.3;\r\n\r\n        arrowTail.material = this._coloredMaterial;\r\n        arrowTail.position.z += 0.275 / 2;\r\n        arrowTail.rotation.x = Math.PI / 2;\r\n\r\n        if (isCollider) {\r\n            arrowMesh.visibility = 0;\r\n            arrowTail.visibility = 0;\r\n        }\r\n\r\n        parentMesh.addChild(arrowMesh);\r\n        parentMesh.addChild(arrowTail);\r\n\r\n        return { arrowMesh, arrowTail };\r\n    }\r\n\r\n    protected override _attachedNodeChanged(value: Nullable<Node>) {\r\n        if (this.dragBehavior) {\r\n            this.dragBehavior.enabled = value ? true : false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the gizmo is enabled\r\n     */\r\n    public set isEnabled(value: boolean) {\r\n        this._isEnabled = value;\r\n        if (!value) {\r\n            this.attachedMesh = null;\r\n            this.attachedNode = null;\r\n        } else {\r\n            if (this._parent) {\r\n                this.attachedMesh = this._parent.attachedMesh;\r\n                this.attachedNode = this._parent.attachedNode;\r\n            }\r\n        }\r\n    }\r\n\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public override dispose() {\r\n        this.onSnapObservable.clear();\r\n        this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(this._pointerObserver);\r\n        this.dragBehavior.detach();\r\n        if (this._gizmoMesh) {\r\n            this._gizmoMesh.dispose();\r\n        }\r\n        const mats = [this._coloredMaterial, this._hoverMaterial, this._disableMaterial];\r\n        for (const matl of mats) {\r\n            if (matl) {\r\n                matl.dispose();\r\n            }\r\n        }\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * Disposes and replaces the current meshes in the gizmo with the specified mesh\r\n     * @param mesh The mesh to replace the default mesh of the gizmo\r\n     * @param useGizmoMaterial If the gizmo's default material should be used (default: false)\r\n     */\r\n    public override setCustomMesh(mesh: Mesh, useGizmoMaterial: boolean = false) {\r\n        super.setCustomMesh(mesh);\r\n        if (useGizmoMaterial) {\r\n            const childMeshes = this._gizmoMesh.getChildMeshes();\r\n            for (const m of childMeshes) {\r\n                m.material = this._coloredMaterial;\r\n                if ((<LinesMesh>m).color) {\r\n                    (<LinesMesh>m).color = this._coloredMaterial.diffuseColor;\r\n                }\r\n            }\r\n            this._customMeshSet = false;\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAGnE,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAEtC,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,EAAE,cAAc,EAAE,MAAM,oCAAoC,CAAC;AACpE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAE9E,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;;;;;;;;;;;;AAqC5C,MAAO,cAAe,mKAAQ,QAAK;IAiDrC,0EAAA,EAA4E,CAC5E,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,4DAAA,EAA8D,CAC9D,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,mEAAA,EAAqE,CACrE,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAsMD;;;;;;;OAOG,CACO,gBAAgB,CAAC,UAAwB,EAAE,SAAiB,EAAoB;yBAAlB,UAAU,uDAAG,KAAK;QACtF,MAAM,SAAS,IAAG,0LAAA,AAAS,EAAC,UAAU,EAAE;YAAE,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAAA,CAAE,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACtH,MAAM,SAAS,uLAAG,iBAAA,AAAc,EAC5B,UAAU,EACV;YAAE,WAAW,EAAE,KAAK,GAAG,SAAS;YAAE,MAAM,EAAE,KAAK;YAAE,cAAc,EAAE,KAAK,GAAG,SAAS;YAAE,YAAY,EAAE,EAAE;QAAA,CAAE,EACtG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CACpC,CAAC;QAEF,2CAA2C;QAC3C,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACpC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC3C,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACnC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,GAAG,CAAC;QAE5B,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC3C,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC;QAClC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAEnC,IAAI,UAAU,EAAE,CAAC;YACb,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;YACzB,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;QAC7B,CAAC;QAED,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC/B,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAE/B,OAAO;YAAE,SAAS;YAAE,SAAS;QAAA,CAAE,CAAC;IACpC,CAAC;IAEkB,oBAAoB,CAAC,KAAqB,EAAA;QACzD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACrD,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,CAAC,KAAc,EAAA;QAC/B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;gBAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;YAClD,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QACD,MAAM,IAAI,GAAG;YAAC,IAAI,CAAC,gBAAgB;YAAE,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,gBAAgB;SAAC,CAAC;QACjF,KAAK,MAAM,IAAI,IAAI,IAAI,CAAE,CAAC;YACtB,IAAI,IAAI,EAAE,CAAC;gBACP,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACL,CAAC;QACD,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;;;OAIG,CACa,aAAa,CAAC,IAAU,EAAmC;+BAAjC,iEAA4B,KAAK;QACvE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,gBAAgB,EAAE,CAAC;YACnB,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YACrD,KAAK,MAAM,CAAC,IAAI,WAAW,CAAE,CAAC;gBAC1B,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBACnC,IAAgB,CAAE,CAAC,KAAK,EAAE,CAAC;oBACX,CAAE,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;gBAC9D,CAAC;YACL,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAChC,CAAC;IACL,CAAC;IAvSD;;;;;;;;;OASG,CACH,YACI,QAAiB,EACjB,QAAgB,0KAAM,CAAC,IAAI,EAAE,EAC7B,yLAAmC,uBAAoB,CAAC,mBAAmB,EAC3E,SAA+B,IAAI,EACnC,YAAoB,CAAC,EACrB,8KAAqB,SAAM,CAAC,MAAM,EAAE,EACpC,gLAAuB,SAAM,CAAC,IAAI,EAAE,CAAA;YA4IpC,iCAAiC;oIAEjC,MAAM,2HAWN;QAvJA,KAAK,CAAC,UAAU,CAAC,CAAC;QA9EZ,IAAA,CAAA,gBAAgB,GAAoC,IAAI,CAAC;QACnE;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACxB;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAAG,iKAAI,aAAU,EAA4B,CAAC;QACrE;;WAEG,CACI,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAC9B;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QACvB;;WAEG,CACI,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QAOrB;;WAEG,CACI,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAErB,IAAA,CAAA,UAAU,GAAY,IAAI,CAAC;QAC3B,IAAA,CAAA,OAAO,GAAyB,IAAI,CAAC;QAMrC,IAAA,CAAA,SAAS,GAAY,KAAK,CAAC;QAC7B,IAAA,CAAA,UAAU,GAAG,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,IAAA,CAAA,wBAAwB,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAqC9C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,kBAAkB;QAClB,IAAI,CAAC,gBAAgB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAEhF,IAAI,CAAC,cAAc,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,UAAU,CAAC;QAE9C,IAAI,CAAC,gBAAgB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,YAAY,CAAC;QAClD,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,GAAG,CAAC;QAElC,wBAAwB;QACxB,IAAI,CAAC,UAAU,GAAG,6JAAI,OAAI,CAAC,MAAM,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACjE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QACnF,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;QAE7E,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,4JAAE,QAAK,CAAC,eAAe,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE5C,+CAA+C;QAC/C,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAChD,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAChD,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QAE5C,MAAM,iBAAiB,GAAG,CAAC,YAAoB,EAAE,EAAE;YAC/C,MAAM,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC;YAE9E,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,YAAY,GAAG,GAAG,CAAC;YAC3C,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,YAAY,CAAC;YACpC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACrC,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACpD,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,GAAG,EAAE;YACxB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;YACvE,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;YACvE,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;YAC7D,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC;QAEF,+DAA+D;QAC/D,IAAI,CAAC,YAAY,GAAG,yLAAI,sBAAmB,CAAC;YAAE,QAAQ,EAAE,QAAQ;QAAA,CAAE,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,KAAK,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9C,IAAI,uBAAuB,GAAG,CAAC,CAAC;QAChC,IAAI,kCAAkC,GAAG,CAAC,CAAC;QAE3C,MAAM,YAAY,GAAG;YAAE,YAAY,EAAE,CAAC;QAAA,CAAE,CAAC;QACzC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,kKAAkK;gBAClK,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,YAAY,GAAG,CAAC,AAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,EAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACvH,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;gBAClC,iBAAiB;gBACjB,IAAI,OAAO,GAAG,KAAK,CAAC;gBACpB,IAAI,SAAS,GAAG,CAAC,CAAC;gBAClB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc;gBAC7C,CAAC,MAAM,CAAC;oBACJ,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACjC,CAAC;gBACD,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;oBACzB,SAAS,CAAC,UAAU,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;gBAClD,CAAC,MAAM,CAAC;oBACJ,uBAAuB,IAAI,YAAY,CAAC;oBACxC,kCAAkC,IAAI,YAAY,CAAC;oBACnD,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC,uBAAuB,CAAC;oBACxG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBAC5C,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;wBAElE,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;4BAClB,SAAS,IAAI,CAAC,CAAC,CAAC;wBACpB,CAAC;wBACD,uBAAuB,GAAG,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC;wBACtE,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,SAAS,EAAE,SAAS,CAAC,CAAC;wBAC/D,OAAO,GAAG,IAAI,CAAC;oBACnB,CAAC,MAAM,CAAC;wBACJ,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC;gBACL,CAAC;gBAED,SAAS,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxC,iFAAiF;gBACjF,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAC,CAAC,cAAc,CAAC,oBAAoB,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC3J,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAC,CAAC,cAAc,CAAC,oBAAoB,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBAC3J,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAC,CAAC,cAAc,CAAC,oBAAoB,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gBAE3J,MAAM,aAAa,GAAU,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC,CAAC,CAAE,IAAI,CAAC,YAA8B,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC3G,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBAC1D,sBAAsB;oBACtB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,SAAS,oKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,4JAAE,QAAK,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;oBAC5J,mEAAmE;oBACnE,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;oBACpD,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oBAC3C,kHAAkH;oBAClH,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrF,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrF,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sLACrF,SAAM,CAAC,YAAY,CAAC,SAAS,oKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1G,CAAC,MAAM,CAAC;sLACJ,SAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjF,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjG,CAAC;gBAED,+FAA+F;kLAC/F,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,kKAAK,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;gBAE/H,MAAM,QAAQ,GAAG,MAAM,CAAC;gBACxB,IAAI,IAAI,CAAC,GAAG,kKAAC,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI,CAAC,GAAG,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI,CAAC,GAAG,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,EAAE,CAAC;oBAC/I,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,QAAQ,mKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtE,CAAC;gBAED,mBAAmB;gBACnB,IAAI,OAAO,EAAE,CAAC;oBACV,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;oBAC1D,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;gBACxD,CAAC;gBACD,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1B,CAAC;QACL,CAAC,CAAC,CAAC;QACH,wDAAwD;QACxD,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;;YAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,MAAM,aAAa,GAAU,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC,CAAC,CAAE,IAAI,CAAC,YAA8B,CAAC,CAAC,CAAC,SAAS,CAAC;sCACvG,CAAC,YAAY,uDAAjB,mBAAmB,cAAc,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,wBAAwB,EAAE,SAAS,EAAE,SAAS,4JAAE,QAAK,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACtJ,uBAAuB,GAAG,CAAC,CAAC;YAC5B,kCAAkC,GAAG,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,gBAAkB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;QACjF,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;4FAGlD,iBAAiB,kIAAE,YAAY,6KAAE,gBAAgB,4FAAzD,MAAM,kDAAqD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,gBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;6FAC5F,iBAAiB,qIAAE,YAAY,kLAAE,mBAAmB,0JAAE,GAAG,CAAC,cAAc,CAAC,CAAC;QAElF,MAAM,KAAK,GAAmB;YAC1B,WAAW,EAAE;gBAAC,SAAS;gBAAE,SAAS;aAAC;YACnC,cAAc,EAAE;gBAAC,QAAQ,CAAC,SAAS;gBAAE,QAAQ,CAAC,SAAS;aAAC;YACxD,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,IAAI,CAAC,YAAY;SAClC,CAAC;6BACE,CAAC,OAAO,gEAAE,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAErD,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;gBAKzE,eAC2D,WAAW,YAE1E,gBACiE,WAAW;YARxF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,OAAO;YACX,CAAC;YACD,kBAAkB;YAClB,IAAI,SAAS,wBAAO,CAAC,OAAO,gEAAE,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC5D,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,gGAAoB,QAAQ,gFAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrH,qBAAqB;YACrB,SAAS,yBAAO,CAAC,OAAO,kEAAE,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACvD,IAAI,CAAC,UAAU,IAAA,CAAf,IAAI,CAAC,UAAU,GAAK,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,iGAAoB,QAAQ,kFAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC;YACvH,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,gBAAgB,CAAC;gBACvJ,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5G,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAChD,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC;IAChG,CAAC;;AAtOD;;GAEG,CACW,eAAA,oBAAoB,wKAAG,UAAH,CAAW", "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/boundingBoxGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/boundingBoxGizmo.ts"], "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Quaternion, Matrix, Vector3, TmpVectors } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { CreateBox } from \"../Meshes/Builders/boxBuilder\";\r\nimport { CreateLines } from \"../Meshes/Builders/linesBuilder\";\r\nimport { PointerDragBehavior } from \"../Behaviors/Meshes/pointerDragBehavior\";\r\nimport type { IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport { PivotTools } from \"../Misc/pivotTools\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { LinesMesh } from \"../Meshes/linesMesh\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\nimport type { IPointerEvent } from \"../Events/deviceInputEvents\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\n\r\n/**\r\n * Interface for bounding box gizmo\r\n */\r\nexport interface IBoundingBoxGizmo extends IGizmo {\r\n    /**\r\n     * If child meshes should be ignored when calculating the bounding box. This should be set to true to avoid perf hits with heavily nested meshes.\r\n     */\r\n    ignoreChildren: boolean;\r\n    /**\r\n     * Returns true if a descendant should be included when computing the bounding box. When null, all descendants are included. If ignoreChildren is set this will be ignored.\r\n     */\r\n    includeChildPredicate: Nullable<(abstractMesh: AbstractMesh) => boolean>;\r\n    /** The size of the rotation anchors attached to the bounding box */\r\n    rotationSphereSize: number;\r\n    /** The size of the scale boxes attached to the bounding box */\r\n    scaleBoxSize: number;\r\n    /**\r\n     * If set, the rotation anchors and scale boxes will increase in size based on the distance away from the camera to have a consistent screen size\r\n     * Note : fixedDragMeshScreenSize takes precedence over fixedDragMeshBoundsSize if both are true\r\n     */\r\n    fixedDragMeshScreenSize: boolean;\r\n    /**\r\n     * If set, the rotation anchors and scale boxes will increase in size based on the size of the bounding box\r\n     * Note : fixedDragMeshScreenSize takes precedence over fixedDragMeshBoundsSize if both are true\r\n     */\r\n    fixedDragMeshBoundsSize: boolean;\r\n    /**\r\n     * The distance away from the object which the draggable meshes should appear world sized when fixedDragMeshScreenSize is set to true\r\n     */\r\n    fixedDragMeshScreenSizeDistanceFactor: number;\r\n    /** True when a rotation anchor or scale box or a attached mesh is dragged */\r\n    readonly isDragging: boolean;\r\n    /** Fired when a rotation anchor or scale box is dragged */\r\n    onDragStartObservable: Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>;\r\n    /** Fired when the gizmo mesh hovering starts*/\r\n    onHoverStartObservable: Observable<void>;\r\n    /** Fired when the gizmo mesh hovering ends*/\r\n    onHoverEndObservable: Observable<void>;\r\n    /** Fired when a scale box is dragged */\r\n    onScaleBoxDragObservable: Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>;\r\n    /** Fired when a scale box drag is ended */\r\n    onScaleBoxDragEndObservable: Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>;\r\n    /** Fired when a rotation anchor is dragged */\r\n    onRotationSphereDragObservable: Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>;\r\n    /** Fired when a rotation anchor drag is ended */\r\n    onRotationSphereDragEndObservable: Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>;\r\n    /** Relative bounding box pivot used when scaling the attached node. */\r\n    scalePivot: Nullable<Vector3>;\r\n    /** Scale factor vector used for masking some axis */\r\n    axisFactor: Vector3;\r\n    /** Scale factor scalar affecting all axes' drag speed */\r\n    scaleDragSpeed: number;\r\n    /**\r\n     * Sets the color of the bounding box gizmo\r\n     * @param color the color to set\r\n     */\r\n    setColor(color: Color3): void;\r\n    /** Returns an array containing all boxes used for scaling (in increasing x, y and z orders) */\r\n    getScaleBoxes(): AbstractMesh[];\r\n    /** Updates the bounding box information for the Gizmo */\r\n    updateBoundingBox(): void;\r\n    /**\r\n     * Enables rotation on the specified axis and disables rotation on the others\r\n     * @param axis The list of axis that should be enabled (eg. \"xy\" or \"xyz\")\r\n     */\r\n    setEnabledRotationAxis(axis: string): void;\r\n    /**\r\n     * Enables/disables scaling\r\n     * @param enable if scaling should be enabled\r\n     * @param homogeneousScaling defines if scaling should only be homogeneous\r\n     */\r\n    setEnabledScaling(enable: boolean, homogeneousScaling?: boolean): void;\r\n    /** Enables a pointer drag behavior on the bounding box of the gizmo */\r\n    enableDragBehavior(): void;\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    releaseDrag(): void;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    coloredMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    hoverMaterial: StandardMaterial;\r\n\r\n    /** Drag distance in babylon units that the gizmo will snap scaling to when dragged */\r\n    scalingSnapDistance: number;\r\n    /** Drag distance in babylon units that the gizmo will snap rotation to when dragged */\r\n    rotationSnapDistance: number;\r\n}\r\n\r\n/**\r\n * Dragging operation in observable\r\n */\r\nexport const enum DragOperation {\r\n    Rotation,\r\n    Scaling,\r\n}\r\n\r\n/**\r\n * Bounding box gizmo\r\n */\r\nexport class BoundingBoxGizmo extends Gizmo implements IBoundingBoxGizmo {\r\n    protected _lineBoundingBox: TransformNode;\r\n    protected _rotateAnchorsParent: TransformNode;\r\n    protected _scaleBoxesParent: TransformNode;\r\n    protected _boundingDimensions = new Vector3(1, 1, 1);\r\n    protected _renderObserver: Nullable<Observer<Scene>> = null;\r\n    protected _pointerObserver: Nullable<Observer<PointerInfo>> = null;\r\n    protected _scaleDragSpeed = 0.2;\r\n    protected _rotateAnchorsDragBehaviors: Array<PointerDragBehavior> = [];\r\n    protected _scaleBoxesDragBehaviors: Array<PointerDragBehavior> = [];\r\n    /**\r\n     * boolean updated when a rotation anchor or scale box is dragged\r\n     */\r\n    protected _dragging = false;\r\n\r\n    private _tmpQuaternion = new Quaternion();\r\n    private _tmpVector = new Vector3(0, 0, 0);\r\n    private _tmpRotationMatrix = new Matrix();\r\n    private _incrementalStartupValue = Vector3.Zero();\r\n    private _incrementalAnchorStartupValue = Vector3.Zero();\r\n\r\n    /**\r\n     * If child meshes should be ignored when calculating the bounding box. This should be set to true to avoid perf hits with heavily nested meshes (Default: false)\r\n     */\r\n    public ignoreChildren = false;\r\n    /**\r\n     * Returns true if a descendant should be included when computing the bounding box. When null, all descendants are included. If ignoreChildren is set this will be ignored. (Default: null)\r\n     */\r\n    public includeChildPredicate: Nullable<(abstractMesh: AbstractMesh) => boolean> = null;\r\n\r\n    /**\r\n     * The size of the rotation anchors attached to the bounding box (Default: 0.1)\r\n     */\r\n    public rotationSphereSize = 0.1;\r\n    /**\r\n     * The size of the scale boxes attached to the bounding box (Default: 0.1)\r\n     */\r\n    public scaleBoxSize = 0.1;\r\n    /**\r\n     * If set, the rotation anchors and scale boxes will increase in size based on the distance away from the camera to have a consistent screen size (Default: false)\r\n     * Note : fixedDragMeshScreenSize takes precedence over fixedDragMeshBoundsSize if both are true\r\n     */\r\n    public fixedDragMeshScreenSize = false;\r\n    /**\r\n     * If set, the rotation anchors and scale boxes will increase in size based on the size of the bounding box\r\n     * Note : fixedDragMeshScreenSize takes precedence over fixedDragMeshBoundsSize if both are true\r\n     */\r\n    public fixedDragMeshBoundsSize = false;\r\n    /**\r\n     * The distance away from the object which the draggable meshes should appear world sized when fixedDragMeshScreenSize is set to true (default: 10)\r\n     */\r\n    public fixedDragMeshScreenSizeDistanceFactor = 10;\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap scaling to when dragged\r\n     */\r\n    public scalingSnapDistance = 0;\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap rotation to when dragged\r\n     */\r\n    public rotationSnapDistance = 0;\r\n    /**\r\n     * Fired when a rotation anchor or scale box is dragged\r\n     */\r\n    public onDragStartObservable = new Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>();\r\n    /**\r\n     * Fired when the gizmo mesh hovering starts\r\n     */\r\n    public onHoverStartObservable = new Observable<void>();\r\n    /**\r\n     * Fired when the gizmo mesh hovering ends\r\n     */\r\n    public onHoverEndObservable = new Observable<void>();\r\n    /**\r\n     * Fired when a scale box is dragged\r\n     */\r\n    public onScaleBoxDragObservable = new Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>();\r\n    /**\r\n     * Fired when a scale box drag is ended\r\n     */\r\n    public onScaleBoxDragEndObservable = new Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>();\r\n    /**\r\n     * Fired when a rotation anchor is dragged\r\n     */\r\n    public onRotationSphereDragObservable = new Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>();\r\n    /**\r\n     * Fired when a rotation anchor drag is ended\r\n     */\r\n    public onRotationSphereDragEndObservable = new Observable<{ dragOperation: DragOperation; dragAxis: Vector3 }>();\r\n    /**\r\n     * Relative bounding box pivot used when scaling the attached node. When null object with scale from the opposite corner. 0.5,0.5,0.5 for center and 0.5,0,0.5 for bottom (Default: null)\r\n     */\r\n    public scalePivot: Nullable<Vector3> = null;\r\n    /**\r\n     * Scale factor used for masking some axis\r\n     */\r\n    protected _axisFactor = new Vector3(1, 1, 1);\r\n\r\n    /**\r\n     * Incremental snap scaling (default is false). When true, with a snapDistance of 0.1, scaling will be 1.1,1.2,1.3 instead of, when false: 1.1,1.21,1.33,...\r\n     */\r\n    public incrementalSnap = false;\r\n\r\n    /**\r\n     * Sets the axis factor\r\n     * @param factor the Vector3 value\r\n     */\r\n    public set axisFactor(factor: Vector3) {\r\n        this._axisFactor = factor;\r\n        // update scale cube visibility\r\n        const scaleBoxes = this._scaleBoxesParent.getChildMeshes();\r\n        let index = 0;\r\n        for (let i = 0; i < 3; i++) {\r\n            for (let j = 0; j < 3; j++) {\r\n                for (let k = 0; k < 3; k++) {\r\n                    const zeroAxisCount = (i === 1 ? 1 : 0) + (j === 1 ? 1 : 0) + (k === 1 ? 1 : 0);\r\n                    if (zeroAxisCount === 1 || zeroAxisCount === 3) {\r\n                        continue;\r\n                    }\r\n                    if (scaleBoxes[index]) {\r\n                        const dragAxis = new Vector3(i - 1, j - 1, k - 1);\r\n                        dragAxis.multiplyInPlace(this._axisFactor);\r\n                        scaleBoxes[index].setEnabled(dragAxis.lengthSquared() > Epsilon);\r\n                    }\r\n                    index++;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the axis factor\r\n     * @returns the Vector3 factor value\r\n     */\r\n    public get axisFactor(): Vector3 {\r\n        return this._axisFactor;\r\n    }\r\n\r\n    /**\r\n     * Sets scale drag speed value\r\n     * @param value the new speed value\r\n     */\r\n    public set scaleDragSpeed(value: number) {\r\n        this._scaleDragSpeed = value;\r\n    }\r\n\r\n    /**\r\n     * Gets scale drag speed\r\n     * @returns the scale speed number\r\n     */\r\n    public get scaleDragSpeed(): number {\r\n        return this._scaleDragSpeed;\r\n    }\r\n\r\n    /**\r\n     * Mesh used as a pivot to rotate the attached node\r\n     */\r\n    protected _anchorMesh: TransformNode;\r\n\r\n    protected _existingMeshScale = new Vector3();\r\n\r\n    // Dragging\r\n    protected _dragMesh: Nullable<Mesh> = null;\r\n    protected _pointerDragBehavior = new PointerDragBehavior();\r\n\r\n    protected _coloredMaterial: StandardMaterial;\r\n    protected _hoverColoredMaterial: StandardMaterial;\r\n\r\n    // HL2 style corner mesh\r\n    protected _cornerMesh: Nullable<Mesh> = null;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    public get coloredMaterial() {\r\n        return this._coloredMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    public get hoverMaterial() {\r\n        return this._hoverColoredMaterial;\r\n    }\r\n    /**\r\n     * Get the pointerDragBehavior\r\n     */\r\n    public get pointerDragBehavior(): PointerDragBehavior {\r\n        return this._pointerDragBehavior;\r\n    }\r\n\r\n    /** True when a rotation anchor or scale box or a attached mesh is dragged */\r\n    public get isDragging() {\r\n        return this._dragging || this._pointerDragBehavior.dragging;\r\n    }\r\n\r\n    /**\r\n     * Sets the color of the bounding box gizmo\r\n     * @param color the color to set\r\n     */\r\n    public setColor(color: Color3) {\r\n        this._coloredMaterial.emissiveColor = color;\r\n        this._hoverColoredMaterial.emissiveColor = color.clone().add(new Color3(0.3, 0.3, 0.3));\r\n        const children = this._lineBoundingBox.getChildren();\r\n        for (const l of children) {\r\n            if ((l as LinesMesh).color) {\r\n                (l as LinesMesh).color = color;\r\n            }\r\n        }\r\n    }\r\n    /**\r\n     * Creates an BoundingBoxGizmo\r\n     * @param color The color of the gizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     */\r\n    constructor(color: Color3 = Color3.Gray(), gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultKeepDepthUtilityLayer) {\r\n        super(gizmoLayer);\r\n\r\n        // Do not update the gizmo's scale so it has a fixed size to the object its attached to\r\n        this.updateScale = false;\r\n\r\n        this._anchorMesh = new TransformNode(\"anchor\", gizmoLayer.utilityLayerScene);\r\n        // Create Materials\r\n        this._coloredMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._coloredMaterial.disableLighting = true;\r\n        this._hoverColoredMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._hoverColoredMaterial.disableLighting = true;\r\n\r\n        // Build bounding box out of lines\r\n        this._lineBoundingBox = new TransformNode(\"\", gizmoLayer.utilityLayerScene);\r\n        this._lineBoundingBox.rotationQuaternion = new Quaternion();\r\n        const lines = [];\r\n        lines.push(CreateLines(\"lines\", { points: [new Vector3(0, 0, 0), new Vector3(this._boundingDimensions.x, 0, 0)] }, gizmoLayer.utilityLayerScene));\r\n        lines.push(CreateLines(\"lines\", { points: [new Vector3(0, 0, 0), new Vector3(0, this._boundingDimensions.y, 0)] }, gizmoLayer.utilityLayerScene));\r\n        lines.push(CreateLines(\"lines\", { points: [new Vector3(0, 0, 0), new Vector3(0, 0, this._boundingDimensions.z)] }, gizmoLayer.utilityLayerScene));\r\n        lines.push(\r\n            CreateLines(\r\n                \"lines\",\r\n                { points: [new Vector3(this._boundingDimensions.x, 0, 0), new Vector3(this._boundingDimensions.x, this._boundingDimensions.y, 0)] },\r\n                gizmoLayer.utilityLayerScene\r\n            )\r\n        );\r\n        lines.push(\r\n            CreateLines(\r\n                \"lines\",\r\n                { points: [new Vector3(this._boundingDimensions.x, 0, 0), new Vector3(this._boundingDimensions.x, 0, this._boundingDimensions.z)] },\r\n                gizmoLayer.utilityLayerScene\r\n            )\r\n        );\r\n        lines.push(\r\n            CreateLines(\r\n                \"lines\",\r\n                { points: [new Vector3(0, this._boundingDimensions.y, 0), new Vector3(this._boundingDimensions.x, this._boundingDimensions.y, 0)] },\r\n                gizmoLayer.utilityLayerScene\r\n            )\r\n        );\r\n        lines.push(\r\n            CreateLines(\r\n                \"lines\",\r\n                { points: [new Vector3(0, this._boundingDimensions.y, 0), new Vector3(0, this._boundingDimensions.y, this._boundingDimensions.z)] },\r\n                gizmoLayer.utilityLayerScene\r\n            )\r\n        );\r\n        lines.push(\r\n            CreateLines(\r\n                \"lines\",\r\n                { points: [new Vector3(0, 0, this._boundingDimensions.z), new Vector3(this._boundingDimensions.x, 0, this._boundingDimensions.z)] },\r\n                gizmoLayer.utilityLayerScene\r\n            )\r\n        );\r\n        lines.push(\r\n            CreateLines(\r\n                \"lines\",\r\n                { points: [new Vector3(0, 0, this._boundingDimensions.z), new Vector3(0, this._boundingDimensions.y, this._boundingDimensions.z)] },\r\n                gizmoLayer.utilityLayerScene\r\n            )\r\n        );\r\n        lines.push(\r\n            CreateLines(\r\n                \"lines\",\r\n                {\r\n                    points: [\r\n                        new Vector3(this._boundingDimensions.x, this._boundingDimensions.y, this._boundingDimensions.z),\r\n                        new Vector3(0, this._boundingDimensions.y, this._boundingDimensions.z),\r\n                    ],\r\n                },\r\n                gizmoLayer.utilityLayerScene\r\n            )\r\n        );\r\n        lines.push(\r\n            CreateLines(\r\n                \"lines\",\r\n                {\r\n                    points: [\r\n                        new Vector3(this._boundingDimensions.x, this._boundingDimensions.y, this._boundingDimensions.z),\r\n                        new Vector3(this._boundingDimensions.x, 0, this._boundingDimensions.z),\r\n                    ],\r\n                },\r\n                gizmoLayer.utilityLayerScene\r\n            )\r\n        );\r\n        lines.push(\r\n            CreateLines(\r\n                \"lines\",\r\n                {\r\n                    points: [\r\n                        new Vector3(this._boundingDimensions.x, this._boundingDimensions.y, this._boundingDimensions.z),\r\n                        new Vector3(this._boundingDimensions.x, this._boundingDimensions.y, 0),\r\n                    ],\r\n                },\r\n                gizmoLayer.utilityLayerScene\r\n            )\r\n        );\r\n        for (const l of lines) {\r\n            l.color = color;\r\n            l.position.addInPlace(new Vector3(-this._boundingDimensions.x / 2, -this._boundingDimensions.y / 2, -this._boundingDimensions.z / 2));\r\n            l.isPickable = false;\r\n            this._lineBoundingBox.addChild(l);\r\n        }\r\n        this._rootMesh.addChild(this._lineBoundingBox);\r\n\r\n        this.setColor(color);\r\n\r\n        // Create rotation anchors\r\n        this._rotateAnchorsParent = new TransformNode(\"\", gizmoLayer.utilityLayerScene);\r\n        this._rotateAnchorsParent.rotationQuaternion = new Quaternion();\r\n        for (let i = 0; i < 12; i++) {\r\n            const anchor = CreateBox(\"\", { width: i < 4 || i >= 8 ? 1.6 : 0.4, height: i >= 4 && i < 8 ? 1.6 : 0.4, depth: 0.4 }, gizmoLayer.utilityLayerScene);\r\n            anchor.rotation.x = i < 4 || i >= 8 ? Math.PI * 0.25 : 0;\r\n            anchor.rotation.y = i >= 4 && i < 8 ? Math.PI * 0.25 : 0;\r\n            anchor.bakeTransformIntoVertices(anchor.computeWorldMatrix(true));\r\n            anchor.rotationQuaternion = new Quaternion();\r\n            anchor.material = this._coloredMaterial;\r\n            anchor.isNearGrabbable = true;\r\n\r\n            // Drag behavior\r\n            const rotateAnchorsDragBehavior = new PointerDragBehavior({});\r\n            rotateAnchorsDragBehavior.moveAttached = false;\r\n            rotateAnchorsDragBehavior.updateDragPlane = false;\r\n            anchor.addBehavior(rotateAnchorsDragBehavior);\r\n            const startingTurnDirection = new Vector3(1, 0, 0);\r\n            let totalTurnAmountOfDrag = 0;\r\n            let previousProjectDist = 0;\r\n            rotateAnchorsDragBehavior.onDragStartObservable.add(() => {\r\n                startingTurnDirection.copyFrom(anchor.forward);\r\n                totalTurnAmountOfDrag = 0;\r\n                previousProjectDist = 0;\r\n            });\r\n            const computeAxis = function () {\r\n                const dragAxisIndex = Math.floor(i / 4);\r\n                TmpVectors.Vector3[0].set(dragAxisIndex == 0 ? 1 : 0, dragAxisIndex == 1 ? 1 : 0, dragAxisIndex == 2 ? 1 : 0);\r\n                return TmpVectors.Vector3[0];\r\n            };\r\n            rotateAnchorsDragBehavior.onDragObservable.add((event) => {\r\n                this.onRotationSphereDragObservable.notifyObservers({ dragOperation: DragOperation.Rotation, dragAxis: computeAxis().clone() });\r\n                if (this.attachedMesh) {\r\n                    const originalParent = this.attachedMesh.parent;\r\n                    if (originalParent && (originalParent as Mesh).scaling && (originalParent as Mesh).scaling.isNonUniformWithinEpsilon(0.001)) {\r\n                        Logger.Warn(\"BoundingBoxGizmo controls are not supported on child meshes with non-uniform parent scaling\");\r\n                        return;\r\n                    }\r\n                    PivotTools._RemoveAndStorePivotPoint(this.attachedMesh);\r\n\r\n                    const worldDragDirection = startingTurnDirection;\r\n\r\n                    // Project the world right on to the drag plane\r\n                    const toSub = event.dragPlaneNormal.scale(Vector3.Dot(event.dragPlaneNormal, worldDragDirection));\r\n                    const dragAxis = worldDragDirection.subtract(toSub).normalizeToNew();\r\n\r\n                    // project drag delta on to the resulting drag axis and rotate based on that\r\n                    let projectDist = Vector3.Dot(dragAxis, event.delta) < 0 ? Math.abs(event.delta.length()) : -Math.abs(event.delta.length());\r\n\r\n                    // Make rotation relative to size of mesh.\r\n                    projectDist = (projectDist / this._boundingDimensions.length()) * this._anchorMesh.scaling.length();\r\n\r\n                    // Rotate based on axis\r\n                    if (!this.attachedMesh.rotationQuaternion) {\r\n                        this.attachedMesh.rotationQuaternion = Quaternion.RotationYawPitchRoll(\r\n                            this.attachedMesh.rotation.y,\r\n                            this.attachedMesh.rotation.x,\r\n                            this.attachedMesh.rotation.z\r\n                        );\r\n                    }\r\n                    if (!this._anchorMesh.rotationQuaternion) {\r\n                        this._anchorMesh.rotationQuaternion = Quaternion.RotationYawPitchRoll(\r\n                            this._anchorMesh.rotation.y,\r\n                            this._anchorMesh.rotation.x,\r\n                            this._anchorMesh.rotation.z\r\n                        );\r\n                    }\r\n\r\n                    // Do not allow the object to turn more than a full circle\r\n                    totalTurnAmountOfDrag += projectDist;\r\n                    if (Math.abs(totalTurnAmountOfDrag) <= 2 * Math.PI) {\r\n                        if (this.rotationSnapDistance > 0) {\r\n                            const dragSteps = Math.floor(Math.abs(totalTurnAmountOfDrag) / this.rotationSnapDistance) * (totalTurnAmountOfDrag < 0 ? -1 : 1);\r\n                            const angle = this.rotationSnapDistance * dragSteps;\r\n                            projectDist = angle - previousProjectDist;\r\n                            previousProjectDist = angle;\r\n                        }\r\n                        if (i >= 8) {\r\n                            Quaternion.RotationYawPitchRollToRef(0, 0, projectDist, this._tmpQuaternion);\r\n                        } else if (i >= 4) {\r\n                            Quaternion.RotationYawPitchRollToRef(projectDist, 0, 0, this._tmpQuaternion);\r\n                        } else {\r\n                            Quaternion.RotationYawPitchRollToRef(0, projectDist, 0, this._tmpQuaternion);\r\n                        }\r\n\r\n                        // if using pivot, move anchor so mesh will be at relative (0,0,0) when parented\r\n                        if (this.attachedMesh.isUsingPivotMatrix()) {\r\n                            this._anchorMesh.position.copyFrom(this.attachedMesh.position);\r\n                        }\r\n                        // Rotate around center of bounding box\r\n                        this._anchorMesh.addChild(this.attachedMesh);\r\n                        if (this._anchorMesh.getScene().useRightHandedSystem) {\r\n                            this._tmpQuaternion.conjugateInPlace();\r\n                        }\r\n                        this._tmpQuaternion.normalize();\r\n                        this._anchorMesh.rotationQuaternion.multiplyToRef(this._tmpQuaternion, this._anchorMesh.rotationQuaternion);\r\n                        this._anchorMesh.rotationQuaternion.normalize();\r\n                        this._anchorMesh.removeChild(this.attachedMesh);\r\n                        this.attachedMesh.setParent(originalParent);\r\n                    }\r\n                    this.updateBoundingBox();\r\n\r\n                    PivotTools._RestorePivotPoint(this.attachedMesh);\r\n                }\r\n                this._updateDummy();\r\n            });\r\n\r\n            // Selection/deselection\r\n            rotateAnchorsDragBehavior.onDragStartObservable.add(() => {\r\n                this.onDragStartObservable.notifyObservers({ dragOperation: DragOperation.Rotation, dragAxis: computeAxis().clone() });\r\n                this._dragging = true;\r\n                this._selectNode(anchor);\r\n            });\r\n            rotateAnchorsDragBehavior.onDragEndObservable.add((event) => {\r\n                this.onRotationSphereDragEndObservable.notifyObservers({ dragOperation: DragOperation.Rotation, dragAxis: computeAxis().clone() });\r\n                this._dragging = false;\r\n                this._selectNode(null);\r\n                this._updateDummy();\r\n                this._unhoverMeshOnTouchUp(event.pointerInfo, anchor);\r\n            });\r\n\r\n            this._rotateAnchorsDragBehaviors.push(rotateAnchorsDragBehavior);\r\n\r\n            this._rotateAnchorsParent.addChild(anchor);\r\n        }\r\n        this._rootMesh.addChild(this._rotateAnchorsParent);\r\n\r\n        // Create scale cubes\r\n        this._scaleBoxesParent = new TransformNode(\"\", gizmoLayer.utilityLayerScene);\r\n        this._scaleBoxesParent.rotationQuaternion = new Quaternion();\r\n        for (let i = 0; i < 3; i++) {\r\n            for (let j = 0; j < 3; j++) {\r\n                for (let k = 0; k < 3; k++) {\r\n                    // create box for relevant axis\r\n                    const zeroAxisCount = (i === 1 ? 1 : 0) + (j === 1 ? 1 : 0) + (k === 1 ? 1 : 0);\r\n                    if (zeroAxisCount === 1 || zeroAxisCount === 3) {\r\n                        continue;\r\n                    }\r\n\r\n                    const box = zeroAxisCount === 2 ? CreateBox(\"\", { size: 1 }, gizmoLayer.utilityLayerScene) : this._getCornerMesh(gizmoLayer);\r\n                    if (zeroAxisCount === 0) {\r\n                        box.rotationQuaternion = Quaternion.FromEulerAngles(j * 0.25 * Math.PI, (k + 3 * i - i * k) * 0.25 * Math.PI, 0);\r\n                    }\r\n\r\n                    box.material = this._coloredMaterial;\r\n                    box._internalMetadata = zeroAxisCount === 2; // None homogenous scale handle\r\n                    box.isNearGrabbable = true;\r\n\r\n                    // box is oriented so, transform world desired axis to local one\r\n                    TmpVectors.Vector3[0].set(i - 1, j - 1, k - 1);\r\n                    TmpVectors.Vector3[0].normalize();\r\n                    box.computeWorldMatrix(true).invertToRef(TmpVectors.Matrix[0]);\r\n                    const dragAxis = Vector3.TransformCoordinates(TmpVectors.Vector3[0], TmpVectors.Matrix[0]);\r\n                    dragAxis.normalize();\r\n\r\n                    // Dragging logic\r\n                    const scaleBoxesDragBehavior = new PointerDragBehavior({ dragAxis: dragAxis });\r\n                    scaleBoxesDragBehavior.updateDragPlane = false;\r\n                    scaleBoxesDragBehavior.moveAttached = false;\r\n                    let totalRelativeDragDistance = 0;\r\n                    let previousScale = 0;\r\n                    box.addBehavior(scaleBoxesDragBehavior);\r\n                    scaleBoxesDragBehavior.onDragObservable.add((event) => {\r\n                        this.onScaleBoxDragObservable.notifyObservers({ dragOperation: DragOperation.Scaling, dragAxis: new Vector3(i - 1, j - 1, k - 1) });\r\n                        if (this.attachedMesh) {\r\n                            const originalParent = this.attachedMesh.parent;\r\n                            if (originalParent && (originalParent as Mesh).scaling && (originalParent as Mesh).scaling.isNonUniformWithinEpsilon(0.001)) {\r\n                                Logger.Warn(\"BoundingBoxGizmo controls are not supported on child meshes with non-uniform parent scaling\");\r\n                                return;\r\n                            }\r\n                            PivotTools._RemoveAndStorePivotPoint(this.attachedMesh);\r\n                            let relativeDragDistance = (event.dragDistance / this._boundingDimensions.length()) * this._anchorMesh.scaling.length();\r\n                            totalRelativeDragDistance += relativeDragDistance;\r\n                            if (this.scalingSnapDistance > 0) {\r\n                                const dragSteps = Math.floor(Math.abs(totalRelativeDragDistance) / this.scalingSnapDistance) * (totalRelativeDragDistance < 0 ? -1 : 1);\r\n                                const scale = this.scalingSnapDistance * dragSteps;\r\n                                relativeDragDistance = scale - previousScale;\r\n                                previousScale = scale;\r\n                            }\r\n\r\n                            const deltaScale = new Vector3(relativeDragDistance, relativeDragDistance, relativeDragDistance);\r\n                            const fullScale = new Vector3(previousScale, previousScale, previousScale);\r\n\r\n                            if (zeroAxisCount === 2) {\r\n                                // scale on 1 axis when using the anchor box in the face middle\r\n                                deltaScale.x *= Math.abs(dragAxis.x);\r\n                                deltaScale.y *= Math.abs(dragAxis.y);\r\n                                deltaScale.z *= Math.abs(dragAxis.z);\r\n                            }\r\n\r\n                            deltaScale.scaleInPlace(this._scaleDragSpeed);\r\n                            deltaScale.multiplyInPlace(this._axisFactor);\r\n\r\n                            fullScale.scaleInPlace(this._scaleDragSpeed);\r\n                            fullScale.multiplyInPlace(this._axisFactor);\r\n                            fullScale.addInPlace(this._incrementalStartupValue);\r\n\r\n                            this.updateBoundingBox();\r\n                            if (this.scalePivot) {\r\n                                this.attachedMesh.getWorldMatrix().getRotationMatrixToRef(this._tmpRotationMatrix);\r\n                                // Move anchor to desired pivot point (Bottom left corner + dimension/2)\r\n                                this._boundingDimensions.scaleToRef(0.5, this._tmpVector);\r\n                                Vector3.TransformCoordinatesToRef(this._tmpVector, this._tmpRotationMatrix, this._tmpVector);\r\n                                this._anchorMesh.position.subtractInPlace(this._tmpVector);\r\n                                this._boundingDimensions.multiplyToRef(this.scalePivot, this._tmpVector);\r\n                                Vector3.TransformCoordinatesToRef(this._tmpVector, this._tmpRotationMatrix, this._tmpVector);\r\n                                this._anchorMesh.position.addInPlace(this._tmpVector);\r\n                            } else {\r\n                                // Scale from the position of the opposite corner\r\n                                box.absolutePosition.subtractToRef(this._anchorMesh.position, this._tmpVector);\r\n                                this._anchorMesh.position.subtractInPlace(this._tmpVector);\r\n                                if (this.attachedMesh.isUsingPivotMatrix()) {\r\n                                    this._anchorMesh.position.subtractInPlace(this.attachedMesh.getPivotPoint());\r\n                                }\r\n                            }\r\n\r\n                            this._anchorMesh.addChild(this.attachedMesh);\r\n                            if (this.incrementalSnap) {\r\n                                fullScale.x /= Math.abs(this._incrementalStartupValue.x) < Epsilon ? 1 : this._incrementalStartupValue.x;\r\n                                fullScale.y /= Math.abs(this._incrementalStartupValue.y) < Epsilon ? 1 : this._incrementalStartupValue.y;\r\n                                fullScale.z /= Math.abs(this._incrementalStartupValue.z) < Epsilon ? 1 : this._incrementalStartupValue.z;\r\n\r\n                                fullScale.x = Math.max(this._incrementalAnchorStartupValue.x * fullScale.x, this.scalingSnapDistance);\r\n                                fullScale.y = Math.max(this._incrementalAnchorStartupValue.y * fullScale.y, this.scalingSnapDistance);\r\n                                fullScale.z = Math.max(this._incrementalAnchorStartupValue.z * fullScale.z, this.scalingSnapDistance);\r\n\r\n                                this._anchorMesh.scaling.x += (fullScale.x - this._anchorMesh.scaling.x) * Math.abs(dragAxis.x);\r\n                                this._anchorMesh.scaling.y += (fullScale.y - this._anchorMesh.scaling.y) * Math.abs(dragAxis.y);\r\n                                this._anchorMesh.scaling.z += (fullScale.z - this._anchorMesh.scaling.z) * Math.abs(dragAxis.z);\r\n                            } else {\r\n                                this._anchorMesh.scaling.addInPlace(deltaScale);\r\n                                if (this._anchorMesh.scaling.x < 0 || this._anchorMesh.scaling.y < 0 || this._anchorMesh.scaling.z < 0) {\r\n                                    this._anchorMesh.scaling.subtractInPlace(deltaScale);\r\n                                }\r\n                            }\r\n                            this._anchorMesh.removeChild(this.attachedMesh);\r\n                            this.attachedMesh.setParent(originalParent);\r\n                            PivotTools._RestorePivotPoint(this.attachedMesh);\r\n                        }\r\n                        this._updateDummy();\r\n                    });\r\n\r\n                    // Selection/deselection\r\n                    scaleBoxesDragBehavior.onDragStartObservable.add(() => {\r\n                        this.onDragStartObservable.notifyObservers({ dragOperation: DragOperation.Scaling, dragAxis: new Vector3(i - 1, j - 1, k - 1) });\r\n                        this._dragging = true;\r\n                        this._selectNode(box);\r\n                        totalRelativeDragDistance = 0;\r\n                        previousScale = 0;\r\n                        this._incrementalStartupValue.copyFrom(this.attachedMesh!.scaling);\r\n                        this._incrementalAnchorStartupValue.copyFrom(this._anchorMesh.scaling);\r\n                    });\r\n                    scaleBoxesDragBehavior.onDragEndObservable.add((event) => {\r\n                        this.onScaleBoxDragEndObservable.notifyObservers({ dragOperation: DragOperation.Scaling, dragAxis: new Vector3(i - 1, j - 1, k - 1) });\r\n                        this._dragging = false;\r\n                        this._selectNode(null);\r\n                        this._updateDummy();\r\n                        this._unhoverMeshOnTouchUp(event.pointerInfo, box);\r\n                    });\r\n\r\n                    this._scaleBoxesParent.addChild(box);\r\n                    this._scaleBoxesDragBehaviors.push(scaleBoxesDragBehavior);\r\n                }\r\n            }\r\n        }\r\n        this._rootMesh.addChild(this._scaleBoxesParent);\r\n\r\n        // Hover color change\r\n        const pointerIds: AbstractMesh[] = [];\r\n        this._pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (!pointerIds[(<IPointerEvent>pointerInfo.event).pointerId]) {\r\n                const meshes = this._rotateAnchorsParent.getChildMeshes().concat(this._scaleBoxesParent.getChildMeshes());\r\n\r\n                for (const mesh of meshes) {\r\n                    if (pointerInfo.pickInfo && pointerInfo.pickInfo.pickedMesh == mesh) {\r\n                        pointerIds[(<IPointerEvent>pointerInfo.event).pointerId] = mesh;\r\n                        mesh.material = this._hoverColoredMaterial;\r\n                        this.onHoverStartObservable.notifyObservers();\r\n                        this._isHovered = true;\r\n                    }\r\n                }\r\n            } else {\r\n                if (pointerInfo.pickInfo && pointerInfo.pickInfo.pickedMesh != pointerIds[(<IPointerEvent>pointerInfo.event).pointerId]) {\r\n                    pointerIds[(<IPointerEvent>pointerInfo.event).pointerId].material = this._coloredMaterial;\r\n                    pointerIds.splice((<IPointerEvent>pointerInfo.event).pointerId, 1);\r\n                    this.onHoverEndObservable.notifyObservers();\r\n                    this._isHovered = false;\r\n                }\r\n            }\r\n        });\r\n\r\n        // Update bounding box positions\r\n        this._renderObserver = this.gizmoLayer.originalScene.onBeforeRenderObservable.add(() => {\r\n            // Only update the bounding box if scaling has changed\r\n            if (this.attachedMesh && !this._existingMeshScale.equals(this.attachedMesh.scaling)) {\r\n                this.updateBoundingBox();\r\n            } else if (this.fixedDragMeshScreenSize || this.fixedDragMeshBoundsSize) {\r\n                this._updateRotationAnchors();\r\n                this._updateScaleBoxes();\r\n            }\r\n\r\n            // If drag mesh is enabled and dragging, update the attached mesh pose to match the drag mesh\r\n            if (this._dragMesh && this.attachedMesh && this._pointerDragBehavior.dragging) {\r\n                this._lineBoundingBox.position.rotateByQuaternionToRef(this._rootMesh.rotationQuaternion!, this._tmpVector);\r\n                this.attachedMesh.setAbsolutePosition(this._dragMesh.position.add(this._tmpVector.scale(-1)));\r\n            }\r\n        });\r\n        this.updateBoundingBox();\r\n    }\r\n\r\n    protected _getCornerMesh(gizmoLayer: UtilityLayerRenderer): Mesh {\r\n        if (!this._cornerMesh) {\r\n            const boxZ = CreateBox(\"\", { width: 0.4, height: 0.4, depth: 1.6 }, gizmoLayer.utilityLayerScene);\r\n            boxZ.position.z = 0.6;\r\n            const boxY = CreateBox(\"\", { width: 0.4, height: 1.6, depth: 0.4 }, gizmoLayer.utilityLayerScene);\r\n            boxY.position.y = 0.6;\r\n            const boxX = CreateBox(\"\", { width: 1.6, height: 0.4, depth: 0.4 }, gizmoLayer.utilityLayerScene);\r\n            boxX.position.x = 0.6;\r\n            this._cornerMesh = Mesh.MergeMeshes([boxX, boxY, boxZ], true);\r\n            return this._cornerMesh!;\r\n        }\r\n\r\n        return this._cornerMesh.clone();\r\n    }\r\n\r\n    /**\r\n     * returns true if the combination of non uniform scaling and rotation of the attached mesh is not supported\r\n     * In that case, the matrix is skewed and the bounding box gizmo will not work correctly\r\n     * @returns True if the combination is not supported, otherwise false.\r\n     */\r\n    protected _hasInvalidNonUniformScaling() {\r\n        return (\r\n            this._attachedMesh?.parent instanceof TransformNode &&\r\n            this._attachedMesh?.parent.absoluteScaling.isNonUniformWithinEpsilon(0.001) &&\r\n            ((this._attachedMesh?.rotationQuaternion && !this._attachedMesh?.rotationQuaternion.equalsWithEpsilon(Quaternion.Identity(), Epsilon)) ||\r\n                this._attachedMesh?.rotation.equalsWithEpsilon(Vector3.Zero(), Epsilon) === false)\r\n        );\r\n    }\r\n    protected override _attachedNodeChanged(value: Nullable<AbstractMesh>) {\r\n        if (value) {\r\n            if (this._hasInvalidNonUniformScaling()) {\r\n                Logger.Warn(\"BoundingBoxGizmo controls are not supported on meshes with non-uniform scaling and rotation\");\r\n                return;\r\n            }\r\n            // Reset anchor mesh to match attached mesh's scale\r\n            // This is needed to avoid invalid box/anchor position on first drag\r\n            this._anchorMesh.scaling.setAll(1);\r\n            PivotTools._RemoveAndStorePivotPoint(value);\r\n            const originalParent = value.parent;\r\n            this._anchorMesh.addChild(value);\r\n            this._anchorMesh.removeChild(value);\r\n            value.setParent(originalParent);\r\n            PivotTools._RestorePivotPoint(value);\r\n            this.updateBoundingBox();\r\n            const children = value.getChildMeshes(false);\r\n            for (const m of children) {\r\n                m.markAsDirty(\"scaling\");\r\n            }\r\n\r\n            this.gizmoLayer.utilityLayerScene.onAfterRenderObservable.addOnce(() => {\r\n                this._updateDummy();\r\n            });\r\n        }\r\n    }\r\n\r\n    protected _selectNode(selectedMesh: Nullable<Mesh>) {\r\n        const meshes = this._rotateAnchorsParent.getChildMeshes().concat(this._scaleBoxesParent.getChildMeshes());\r\n\r\n        for (const m of meshes) {\r\n            m.isVisible = !selectedMesh || m == selectedMesh;\r\n        }\r\n    }\r\n\r\n    protected _unhoverMeshOnTouchUp(pointerInfo: Nullable<PointerInfo>, selectedMesh: AbstractMesh) {\r\n        // force unhover mesh if not a mouse event\r\n        if (pointerInfo?.event instanceof PointerEvent && pointerInfo?.event.pointerType === \"touch\") {\r\n            selectedMesh.material = this._coloredMaterial;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * returns an array containing all boxes used for scaling (in increasing x, y and z orders)\r\n     * @returns array of scaling boxes\r\n     */\r\n    public getScaleBoxes() {\r\n        return this._scaleBoxesParent.getChildMeshes();\r\n    }\r\n\r\n    /**\r\n     * Updates the bounding box information for the Gizmo\r\n     */\r\n    public updateBoundingBox() {\r\n        if (this.attachedMesh && !this._hasInvalidNonUniformScaling()) {\r\n            PivotTools._RemoveAndStorePivotPoint(this.attachedMesh);\r\n\r\n            // Store original parent\r\n            const originalParent = this.attachedMesh.parent;\r\n            this.attachedMesh.setParent(null);\r\n\r\n            this._update();\r\n\r\n            // Rotate based on axis\r\n            if (!this.attachedMesh.rotationQuaternion) {\r\n                this.attachedMesh.rotationQuaternion = Quaternion.RotationYawPitchRoll(this.attachedMesh.rotation.y, this.attachedMesh.rotation.x, this.attachedMesh.rotation.z);\r\n            }\r\n            if (!this._anchorMesh.rotationQuaternion) {\r\n                this._anchorMesh.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._anchorMesh.rotation.y, this._anchorMesh.rotation.x, this._anchorMesh.rotation.z);\r\n            }\r\n            this._anchorMesh.rotationQuaternion.copyFrom(this.attachedMesh.rotationQuaternion);\r\n\r\n            // Store original position and reset mesh to origin before computing the bounding box\r\n            this._tmpQuaternion.copyFrom(this.attachedMesh.rotationQuaternion);\r\n            this._tmpVector.copyFrom(this.attachedMesh.position);\r\n            this.attachedMesh.rotationQuaternion.set(0, 0, 0, 1);\r\n            this.attachedMesh.position.set(0, 0, 0);\r\n\r\n            // Update bounding dimensions/positions\r\n            const boundingMinMax = this.attachedMesh.getHierarchyBoundingVectors(!this.ignoreChildren, this.includeChildPredicate);\r\n            boundingMinMax.max.subtractToRef(boundingMinMax.min, this._boundingDimensions);\r\n\r\n            // Update gizmo to match bounding box scaling and rotation\r\n            // The position set here is the offset from the origin for the boundingbox when the attached mesh is at the origin\r\n            // The position of the gizmo is then set to the attachedMesh in gizmo._update\r\n            this._lineBoundingBox.scaling.copyFrom(this._boundingDimensions);\r\n            this._lineBoundingBox.position.set(\r\n                (boundingMinMax.max.x + boundingMinMax.min.x) / 2,\r\n                (boundingMinMax.max.y + boundingMinMax.min.y) / 2,\r\n                (boundingMinMax.max.z + boundingMinMax.min.z) / 2\r\n            );\r\n            this._rotateAnchorsParent.position.copyFrom(this._lineBoundingBox.position);\r\n            this._scaleBoxesParent.position.copyFrom(this._lineBoundingBox.position);\r\n            this._lineBoundingBox.computeWorldMatrix();\r\n            this._anchorMesh.position.copyFrom(this._lineBoundingBox.absolutePosition);\r\n\r\n            // Restore position/rotation values\r\n            this.attachedMesh.rotationQuaternion.copyFrom(this._tmpQuaternion);\r\n            this.attachedMesh.position.copyFrom(this._tmpVector);\r\n\r\n            // Restore original parent\r\n            this.attachedMesh.setParent(originalParent);\r\n        }\r\n\r\n        this._updateRotationAnchors();\r\n        this._updateScaleBoxes();\r\n\r\n        if (this.attachedMesh) {\r\n            this._existingMeshScale.copyFrom(this.attachedMesh.scaling);\r\n            PivotTools._RestorePivotPoint(this.attachedMesh);\r\n        }\r\n    }\r\n\r\n    protected _updateRotationAnchors() {\r\n        const rotateAnchors = this._rotateAnchorsParent.getChildMeshes();\r\n        for (let i = 0; i < 3; i++) {\r\n            for (let j = 0; j < 2; j++) {\r\n                for (let k = 0; k < 2; k++) {\r\n                    const index = i * 4 + j * 2 + k;\r\n                    rotateAnchors[index].position.normalizeToRef(TmpVectors.Vector3[0]);\r\n                    if (i == 0) {\r\n                        rotateAnchors[index].position.set(0, this._boundingDimensions.y * (j - 0.5), this._boundingDimensions.z * (k - 0.5));\r\n                        TmpVectors.Vector3[1].set(1, 0, 0);\r\n                    }\r\n                    if (i == 1) {\r\n                        rotateAnchors[index].position.set(this._boundingDimensions.x * (j - 0.5), 0, this._boundingDimensions.z * (k - 0.5));\r\n                        TmpVectors.Vector3[1].set(0, 1, 0);\r\n                    }\r\n                    if (i == 2) {\r\n                        rotateAnchors[index].position.set(this._boundingDimensions.x * (j - 0.5), this._boundingDimensions.y * (k - 0.5), 0);\r\n                        TmpVectors.Vector3[1].set(0, 0, 1);\r\n                    }\r\n                    const target = TmpVectors.Vector3[2];\r\n                    Vector3.CrossToRef(TmpVectors.Vector3[0], TmpVectors.Vector3[1], target);\r\n                    target.normalize();\r\n                    target.addInPlace(rotateAnchors[index].position);\r\n                    rotateAnchors[index].lookAt(target);\r\n\r\n                    if (this.fixedDragMeshScreenSize && this.gizmoLayer.utilityLayerScene.activeCamera) {\r\n                        rotateAnchors[index].absolutePosition.subtractToRef(this.gizmoLayer.utilityLayerScene.activeCamera.position, this._tmpVector);\r\n                        const distanceFromCamera = (this.rotationSphereSize * this._tmpVector.length()) / this.fixedDragMeshScreenSizeDistanceFactor;\r\n                        rotateAnchors[index].scaling.set(distanceFromCamera, distanceFromCamera, distanceFromCamera);\r\n                    } else if (this.fixedDragMeshBoundsSize) {\r\n                        rotateAnchors[index].scaling.set(\r\n                            this.rotationSphereSize * this._boundingDimensions.x,\r\n                            this.rotationSphereSize * this._boundingDimensions.y,\r\n                            this.rotationSphereSize * this._boundingDimensions.z\r\n                        );\r\n                    } else {\r\n                        rotateAnchors[index].scaling.set(this.rotationSphereSize, this.rotationSphereSize, this.rotationSphereSize);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _updateScaleBoxes() {\r\n        const scaleBoxes = this._scaleBoxesParent.getChildMeshes();\r\n        let index = 0;\r\n        for (let i = 0; i < 3; i++) {\r\n            for (let j = 0; j < 3; j++) {\r\n                for (let k = 0; k < 3; k++) {\r\n                    const zeroAxisCount = (i === 1 ? 1 : 0) + (j === 1 ? 1 : 0) + (k === 1 ? 1 : 0);\r\n                    if (zeroAxisCount === 1 || zeroAxisCount === 3) {\r\n                        continue;\r\n                    }\r\n                    if (scaleBoxes[index]) {\r\n                        scaleBoxes[index].position.set(this._boundingDimensions.x * (i / 2), this._boundingDimensions.y * (j / 2), this._boundingDimensions.z * (k / 2));\r\n                        scaleBoxes[index].position.addInPlace(new Vector3(-this._boundingDimensions.x / 2, -this._boundingDimensions.y / 2, -this._boundingDimensions.z / 2));\r\n                        if (this.fixedDragMeshScreenSize && this.gizmoLayer.utilityLayerScene.activeCamera) {\r\n                            scaleBoxes[index].absolutePosition.subtractToRef(this.gizmoLayer.utilityLayerScene.activeCamera.globalPosition, this._tmpVector);\r\n                            const distanceFromCamera = (this.scaleBoxSize * this._tmpVector.length()) / this.fixedDragMeshScreenSizeDistanceFactor;\r\n                            scaleBoxes[index].scaling.set(distanceFromCamera, distanceFromCamera, distanceFromCamera);\r\n                        } else if (this.fixedDragMeshBoundsSize) {\r\n                            scaleBoxes[index].scaling.set(\r\n                                this.scaleBoxSize * this._boundingDimensions.x,\r\n                                this.scaleBoxSize * this._boundingDimensions.y,\r\n                                this.scaleBoxSize * this._boundingDimensions.z\r\n                            );\r\n                        } else {\r\n                            scaleBoxes[index].scaling.set(this.scaleBoxSize, this.scaleBoxSize, this.scaleBoxSize);\r\n                        }\r\n                    }\r\n                    index++;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enables rotation on the specified axis and disables rotation on the others\r\n     * @param axis The list of axis that should be enabled (eg. \"xy\" or \"xyz\")\r\n     */\r\n    public setEnabledRotationAxis(axis: string) {\r\n        const meshes = this._rotateAnchorsParent.getChildMeshes();\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const m = meshes[i] as Mesh;\r\n            if (i < 4) {\r\n                m.setEnabled(axis.indexOf(\"x\") != -1);\r\n            } else if (i < 8) {\r\n                m.setEnabled(axis.indexOf(\"y\") != -1);\r\n            } else {\r\n                m.setEnabled(axis.indexOf(\"z\") != -1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enables/disables scaling\r\n     * @param enable if scaling should be enabled\r\n     * @param homogeneousScaling defines if scaling should only be homogeneous\r\n     */\r\n    public setEnabledScaling(enable: boolean, homogeneousScaling = false) {\r\n        const meshes = this._scaleBoxesParent.getChildMeshes();\r\n        for (const m of meshes) {\r\n            let enableMesh = enable;\r\n            // Disable heterogeneous scale handles if requested.\r\n            if (homogeneousScaling && m._internalMetadata === true) {\r\n                enableMesh = false;\r\n            }\r\n            m.setEnabled(enableMesh);\r\n        }\r\n    }\r\n\r\n    protected _updateDummy() {\r\n        if (this._dragMesh) {\r\n            this._dragMesh.position.copyFrom(this._lineBoundingBox.getAbsolutePosition());\r\n            this._dragMesh.scaling.copyFrom(this._lineBoundingBox.scaling);\r\n            this._dragMesh.rotationQuaternion!.copyFrom(this._rootMesh.rotationQuaternion!);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enables a pointer drag behavior on the bounding box of the gizmo\r\n     */\r\n    public enableDragBehavior() {\r\n        this._dragMesh = CreateBox(\"dummy\", { size: 1 }, this.gizmoLayer.utilityLayerScene);\r\n        this._dragMesh.visibility = 0;\r\n        this._dragMesh.rotationQuaternion = new Quaternion();\r\n        this._pointerDragBehavior.useObjectOrientationForDragging = false;\r\n        this._dragMesh.addBehavior(this._pointerDragBehavior);\r\n    }\r\n\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    public releaseDrag() {\r\n        for (const dragBehavior of this._scaleBoxesDragBehaviors) {\r\n            dragBehavior.releaseDrag();\r\n        }\r\n        for (const dragBehavior of this._rotateAnchorsDragBehaviors) {\r\n            dragBehavior.releaseDrag();\r\n        }\r\n        this._pointerDragBehavior.releaseDrag();\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public override dispose() {\r\n        this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(this._pointerObserver);\r\n        this.gizmoLayer.originalScene.onBeforeRenderObservable.remove(this._renderObserver);\r\n        this._lineBoundingBox.dispose();\r\n        this._rotateAnchorsParent.dispose();\r\n        this._scaleBoxesParent.dispose();\r\n        if (this._dragMesh) {\r\n            this._dragMesh.dispose();\r\n        }\r\n        this._scaleBoxesDragBehaviors.length = 0;\r\n        this._rotateAnchorsDragBehaviors.length = 0;\r\n        this.onDragStartObservable.clear();\r\n        this.onHoverStartObservable.clear();\r\n        this.onHoverEndObservable.clear();\r\n        this.onScaleBoxDragObservable.clear();\r\n        this.onScaleBoxDragEndObservable.clear();\r\n        this.onRotationSphereDragObservable.clear();\r\n        this.onRotationSphereDragEndObservable.clear();\r\n\r\n        this._coloredMaterial.dispose();\r\n        this._hoverColoredMaterial.dispose();\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * Makes a mesh not pickable and wraps the mesh inside of a bounding box mesh that is pickable. (This is useful to avoid picking within complex geometry)\r\n     * @param mesh the mesh to wrap in the bounding box mesh and make not pickable\r\n     * @returns the bounding box mesh with the passed in mesh as a child\r\n     */\r\n    public static MakeNotPickableAndWrapInBoundingBox(mesh: Mesh): Mesh {\r\n        const makeNotPickable = (root: AbstractMesh) => {\r\n            root.isPickable = false;\r\n            const children = root.getChildMeshes();\r\n            for (const c of children) {\r\n                makeNotPickable(c);\r\n            }\r\n        };\r\n        makeNotPickable(mesh);\r\n\r\n        // Reset position to get bounding box from origin with no rotation\r\n        if (!mesh.rotationQuaternion) {\r\n            mesh.rotationQuaternion = Quaternion.RotationYawPitchRoll(mesh.rotation.y, mesh.rotation.x, mesh.rotation.z);\r\n        }\r\n        const oldPos = mesh.position.clone();\r\n        const oldRot = mesh.rotationQuaternion.clone();\r\n        mesh.rotationQuaternion.set(0, 0, 0, 1);\r\n        mesh.position.set(0, 0, 0);\r\n\r\n        // Update bounding dimensions/positions\r\n        const box = CreateBox(\"box\", { size: 1 }, mesh.getScene());\r\n        const boundingMinMax = mesh.getHierarchyBoundingVectors();\r\n        boundingMinMax.max.subtractToRef(boundingMinMax.min, box.scaling);\r\n\r\n        // Adjust scale to avoid undefined behavior when adding child\r\n        if (box.scaling.y === 0) {\r\n            box.scaling.y = Epsilon;\r\n        }\r\n        if (box.scaling.x === 0) {\r\n            box.scaling.x = Epsilon;\r\n        }\r\n        if (box.scaling.z === 0) {\r\n            box.scaling.z = Epsilon;\r\n        }\r\n\r\n        box.position.set((boundingMinMax.max.x + boundingMinMax.min.x) / 2, (boundingMinMax.max.y + boundingMinMax.min.y) / 2, (boundingMinMax.max.z + boundingMinMax.min.z) / 2);\r\n\r\n        // Restore original positions\r\n        mesh.addChild(box);\r\n        mesh.rotationQuaternion.copyFrom(oldRot);\r\n        mesh.position.copyFrom(oldPos);\r\n\r\n        // Reverse parenting\r\n        mesh.removeChild(box);\r\n\r\n        box.addChild(mesh);\r\n        box.visibility = 0;\r\n        return box;\r\n    }\r\n    /**\r\n     * CustomMeshes are not supported by this gizmo\r\n     */\r\n    public override setCustomMesh() {\r\n        Logger.Error(\"Custom meshes are not supported on this gizmo\");\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAIxC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAE/E,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAE9E,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AACjE,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;;;;;;;;;;;;;;;AA+FxD,IAAkB,aAGjB;AAHD,CAAA,SAAkB,aAAa;IAC3B,aAAA,CAAA,aAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAQ,CAAA;IACR,aAAA,CAAA,aAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAO,CAAA;AACX,CAAC,EAHiB,aAAa,IAAA,CAAb,aAAa,GAAA,CAAA,CAAA,GAG9B;AAKK,MAAO,gBAAiB,mKAAQ,QAAK;IAsGvC;;;OAGG,CACH,IAAW,UAAU,CAAC,MAAe,EAAA;QACjC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC;QAC1B,+BAA+B;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC;QAC3D,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;YACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;gBACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oBACzB,MAAM,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChF,IAAI,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;wBAC7C,SAAS;oBACb,CAAC;oBACD,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;wBACpB,MAAM,QAAQ,GAAG,sKAAI,UAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;wBAClD,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAC3C,UAAU,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,EAAE,wKAAG,UAAO,CAAC,CAAC;oBACrE,CAAC;oBACD,KAAK,EAAE,CAAC;gBACZ,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACH,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IACjC,CAAC;IAED;;;OAGG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAmBD,0EAAA,EAA4E,CAC5E,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,4DAAA,EAA8D,CAC9D,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IACD;;OAEG,CACH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,2EAAA,EAA6E,CAC7E,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC;IAChE,CAAC;IAED;;;OAGG,CACI,QAAQ,CAAC,KAAa,EAAA;QACzB,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC;QAC5C,IAAI,CAAC,qBAAqB,CAAC,aAAa,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,0KAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACxF,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;QACrD,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;YACvB,IAAK,CAAe,CAAC,KAAK,EAAE,CAAC;gBACxB,CAAe,CAAC,KAAK,GAAG,KAAK,CAAC;YACnC,CAAC;QACL,CAAC;IACL,CAAC;IAwaS,cAAc,CAAC,UAAgC,EAAA;QACrD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,IAAI,kLAAG,YAAA,AAAS,EAAC,EAAE,EAAE;gBAAE,KAAK,EAAE,GAAG;gBAAE,MAAM,EAAE,GAAG;gBAAE,KAAK,EAAE,GAAG;YAAA,CAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAClG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;YACtB,MAAM,IAAI,IAAG,0LAAA,AAAS,EAAC,EAAE,EAAE;gBAAE,KAAK,EAAE,GAAG;gBAAE,MAAM,EAAE,GAAG;gBAAE,KAAK,EAAE,GAAG;YAAA,CAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAClG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;YACtB,MAAM,IAAI,kLAAG,YAAS,AAAT,EAAU,EAAE,EAAE;gBAAE,KAAK,EAAE,GAAG;gBAAE,MAAM,EAAE,GAAG;gBAAE,KAAK,EAAE,GAAG;YAAA,CAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAClG,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;YACtB,IAAI,CAAC,WAAW,2JAAG,QAAI,CAAC,WAAW,CAAC;gBAAC,IAAI;gBAAE,IAAI;gBAAE,IAAI;aAAC,EAAE,IAAI,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,WAAY,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IACpC,CAAC;IAED;;;;OAIG,CACO,4BAA4B,GAAA;YAE9B,uFAGI;QAJR,OAAO,4BACC,CAAC,aAAa,4EAAE,MAAM,+KAAY,gBAAa,6BACnD,IAAI,CAAC,aAAa,8EAAE,MAAM,CAAC,eAAe,CAAC,yBAAyB,CAAC,KAAK,CAAC,KAC3E,CAAC,6BAAK,CAAC,aAAa,yDAAlB,qBAAoB,kBAAkB,KAAI,8BAAK,CAAC,aAAa,yDAAlB,qBAAoB,kBAAkB,CAAC,iBAAiB,mKAAC,aAAU,CAAC,QAAQ,EAAE,uKAAE,UAAO,CAAC,CAAC,iCAC9H,CAAC,aAAa,8EAAE,QAAQ,CAAC,iBAAiB,mKAAC,UAAO,CAAC,IAAI,EAAE,uKAAE,UAAO,CAAC,MAAK,KAAK,CAAC,CACzF,CAAC;IACN,CAAC;IACkB,oBAAoB,CAAC,KAA6B,EAAA;QACjE,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,IAAI,CAAC,4BAA4B,EAAE,EAAE,CAAC;yKACtC,SAAM,CAAC,IAAI,CAAC,6FAA6F,CAAC,CAAC;gBAC3G,OAAO;YACX,CAAC;YACD,mDAAmD;YACnD,oEAAoE;YACpE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;yKACnC,aAAU,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC;YACpC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACjC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACpC,KAAK,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;yKAChC,aAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC7C,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;gBACvB,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAC7B,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE;gBACnE,IAAI,CAAC,YAAY,EAAE,CAAC;YACxB,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAES,WAAW,CAAC,YAA4B,EAAA;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,CAAC;QAE1G,KAAK,MAAM,CAAC,IAAI,MAAM,CAAE,CAAC;YACrB,CAAC,CAAC,SAAS,GAAG,CAAC,YAAY,IAAI,CAAC,IAAI,YAAY,CAAC;QACrD,CAAC;IACL,CAAC;IAES,qBAAqB,CAAC,WAAkC,EAAE,YAA0B,EAAA;QAC1F,0CAA0C;QAC1C,IAAI,WAAW,4DAAE,KAAK,aAAY,YAAY,+DAAI,WAAW,CAAE,KAAK,CAAC,WAAW,MAAK,OAAO,EAAE,CAAC;YAC3F,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAClD,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG,CACI,iBAAiB,GAAA;QACpB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,CAAC;wKAC5D,cAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAExD,wBAAwB;YACxB,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAElC,IAAI,CAAC,OAAO,EAAE,CAAC;YAEf,uBAAuB;YACvB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;gBACxC,IAAI,CAAC,YAAY,CAAC,kBAAkB,qKAAG,aAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACrK,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;gBACvC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,+KAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACjK,CAAC;YACD,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YAEnF,qFAAqF;YACrF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YACnE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAExC,uCAAuC;YACvC,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACvH,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAE/E,0DAA0D;YAC1D,kHAAkH;YAClH,6EAA6E;YAC7E,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAC9B,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EACjD,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EACjD,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CACpD,CAAC;YACF,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC5E,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YACzE,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;YAC3C,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAE3E,mCAAmC;YACnC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAErD,0BAA0B;YAC1B,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;yKAC5D,aAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAES,sBAAsB,GAAA;QAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,CAAC;QACjE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;YACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;gBACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oBACzB,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAChC,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,cAAc,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACT,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;yLACrH,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACvC,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACT,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;0LACrH,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACvC,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACT,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;wBACrH,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACvC,CAAC;oBACD,MAAM,MAAM,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;sLACrC,UAAO,CAAC,UAAU,kKAAC,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;oBACzE,MAAM,CAAC,SAAS,EAAE,CAAC;oBACnB,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC;oBACjD,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAEpC,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;wBACjF,aAAa,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC9H,MAAM,kBAAkB,GAAG,AAAC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAG,IAAI,CAAC,qCAAqC,CAAC;wBAC7H,aAAa,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;oBACjG,CAAC,MAAM,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;wBACtC,aAAa,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EACpD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EACpD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CACvD,CAAC;oBACN,CAAC,MAAM,CAAC;wBACJ,aAAa,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAChH,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAES,iBAAiB,GAAA;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC;QAC3D,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;YACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;gBACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oBACzB,MAAM,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChF,IAAI,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;wBAC7C,SAAS;oBACb,CAAC;oBACD,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;wBACpB,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBACjJ,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,sKAAI,UAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBACtJ,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;4BACjF,UAAU,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;4BACjI,MAAM,kBAAkB,GAAG,AAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAG,IAAI,CAAC,qCAAqC,CAAC;4BACvH,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;wBAC9F,CAAC,MAAM,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;4BACtC,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CACjD,CAAC;wBACN,CAAC,MAAM,CAAC;4BACJ,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC3F,CAAC;oBACL,CAAC;oBACD,KAAK,EAAE,CAAC;gBACZ,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,sBAAsB,CAAC,IAAY,EAAA;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,CAAC;QAC1D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACrC,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAS,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACR,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACf,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,MAAM,CAAC;gBACJ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,MAAe,EAA4B;iCAA1B,kBAAkB,+CAAG,KAAK;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC;QACvD,KAAK,MAAM,CAAC,IAAI,MAAM,CAAE,CAAC;YACrB,IAAI,UAAU,GAAG,MAAM,CAAC;YACxB,oDAAoD;YACpD,IAAI,kBAAkB,IAAI,CAAC,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;gBACrD,UAAU,GAAG,KAAK,CAAC;YACvB,CAAC;YACD,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC7B,CAAC;IACL,CAAC;IAES,YAAY,GAAA;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAC9E,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAC/D,IAAI,CAAC,SAAS,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAmB,CAAC,CAAC;QACpF,CAAC;IACL,CAAC;IAED;;OAEG,CACI,kBAAkB,GAAA;QACrB,IAAI,CAAC,SAAS,kLAAG,YAAA,AAAS,EAAC,OAAO,EAAE;YAAE,IAAI,EAAE,CAAC;QAAA,CAAE,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACpF,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,sKAAI,aAAU,EAAE,CAAC;QACrD,IAAI,CAAC,oBAAoB,CAAC,+BAA+B,GAAG,KAAK,CAAC;QAClE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG,CACI,WAAW,GAAA;QACd,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,wBAAwB,CAAE,CAAC;YACvD,YAAY,CAAC,WAAW,EAAE,CAAC;QAC/B,CAAC;QACD,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,2BAA2B,CAAE,CAAC;YAC1D,YAAY,CAAC,WAAW,EAAE,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC;IAC5C,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACpF,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;QACpC,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,2BAA2B,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QACzC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAE/C,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;QACrC,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;;;OAIG,CACI,MAAM,CAAC,mCAAmC,CAAC,IAAU,EAAA;QACxD,MAAM,eAAe,GAAG,CAAC,IAAkB,EAAE,EAAE;YAC3C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACvC,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;gBACvB,eAAe,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC;QACL,CAAC,CAAC;QACF,eAAe,CAAC,IAAI,CAAC,CAAC;QAEtB,kEAAkE;QAClE,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,qKAAG,aAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjH,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,uCAAuC;QACvC,MAAM,GAAG,kLAAG,YAAA,AAAS,EAAC,KAAK,EAAE;YAAE,IAAI,EAAE,CAAC;QAAA,CAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3D,MAAM,cAAc,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAC1D,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;QAElE,6DAA6D;QAC7D,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YACtB,GAAG,CAAC,OAAO,CAAC,CAAC,wKAAG,UAAO,CAAC;QAC5B,CAAC;QACD,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YACtB,GAAG,CAAC,OAAO,CAAC,CAAC,wKAAG,UAAO,CAAC;QAC5B,CAAC;QACD,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YACtB,GAAG,CAAC,OAAO,CAAC,CAAC,wKAAG,UAAO,CAAC;QAC5B,CAAC;QAED,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE1K,6BAA6B;QAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE/B,oBAAoB;QACpB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAEtB,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnB,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;QACnB,OAAO,GAAG,CAAC;IACf,CAAC;IACD;;OAEG,CACa,aAAa,GAAA;iKACzB,SAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAClE,CAAC;IAzxBD;;;;OAIG,CACH,YAAY,yKAAgB,SAAM,CAAC,IAAI,EAAE,EAAE,yLAAmC,uBAAoB,CAAC,4BAA4B,CAAA;QAC3H,KAAK,CAAC,UAAU,CAAC,CAAC;QA/MZ,IAAA,CAAA,mBAAmB,GAAG,IAAI,4KAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3C,IAAA,CAAA,eAAe,GAA8B,IAAI,CAAC;QAClD,IAAA,CAAA,gBAAgB,GAAoC,IAAI,CAAC;QACzD,IAAA,CAAA,eAAe,GAAG,GAAG,CAAC;QACtB,IAAA,CAAA,2BAA2B,GAA+B,EAAE,CAAC;QAC7D,IAAA,CAAA,wBAAwB,GAA+B,EAAE,CAAC;QACpE;;WAEG,CACO,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAEpB,IAAA,CAAA,cAAc,GAAG,IAAI,+KAAU,EAAE,CAAC;QAClC,IAAA,CAAA,UAAU,GAAG,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,IAAA,CAAA,kBAAkB,GAAG,IAAI,2KAAM,EAAE,CAAC;QAClC,IAAA,CAAA,wBAAwB,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC1C,IAAA,CAAA,8BAA8B,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAExD;;WAEG,CACI,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAC9B;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAsD,IAAI,CAAC;QAEvF;;WAEG,CACI,IAAA,CAAA,kBAAkB,GAAG,GAAG,CAAC;QAChC;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG,GAAG,CAAC;QAC1B;;;WAGG,CACI,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QACvC;;;WAGG,CACI,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QACvC;;WAEG,CACI,IAAA,CAAA,qCAAqC,GAAG,EAAE,CAAC;QAClD;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC;QAC/B;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC;QAChC;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAG,iKAAI,aAAU,EAAuD,CAAC;QACrG;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,iKAAI,aAAU,EAAQ,CAAC;QACvD;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,iKAAI,aAAU,EAAQ,CAAC;QACrD;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,iKAAI,aAAU,EAAuD,CAAC;QACxG;;WAEG,CACI,IAAA,CAAA,2BAA2B,GAAG,IAAI,0KAAU,EAAuD,CAAC;QAC3G;;WAEG,CACI,IAAA,CAAA,8BAA8B,GAAG,iKAAI,aAAU,EAAuD,CAAC;QAC9G;;WAEG,CACI,IAAA,CAAA,iCAAiC,GAAG,iKAAI,aAAU,EAAuD,CAAC;QACjH;;WAEG,CACI,IAAA,CAAA,UAAU,GAAsB,IAAI,CAAC;QAC5C;;WAEG,CACO,IAAA,CAAA,WAAW,GAAG,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7C;;WAEG,CACI,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QA0DrB,IAAA,CAAA,kBAAkB,GAAG,IAAI,4KAAO,EAAE,CAAC;QAE7C,WAAW;QACD,IAAA,CAAA,SAAS,GAAmB,IAAI,CAAC;QACjC,IAAA,CAAA,oBAAoB,GAAG,yLAAI,sBAAmB,EAAE,CAAC;QAK3D,wBAAwB;QACd,IAAA,CAAA,WAAW,GAAmB,IAAI,CAAC;QA6CzC,uFAAuF;QACvF,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,IAAI,CAAC,WAAW,GAAG,sKAAI,gBAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7E,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,IAAI,CAAC;QAC7C,IAAI,CAAC,qBAAqB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACpF,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,IAAI,CAAC;QAElD,kCAAkC;QAClC,IAAI,CAAC,gBAAgB,GAAG,sKAAI,gBAAa,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC5E,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,GAAG,sKAAI,aAAU,EAAE,CAAC;QAC5D,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,CAAC,IAAI,kLAAC,cAAA,AAAW,EAAC,OAAO,EAAE;YAAE,MAAM,EAAE;gBAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;aAAC;QAAA,CAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAClJ,KAAK,CAAC,IAAI,kLAAC,cAAW,AAAX,EAAY,OAAO,EAAE;YAAE,MAAM,EAAE;gBAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,sKAAI,UAAO,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;aAAC;QAAA,CAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAClJ,KAAK,CAAC,IAAI,CAAC,+LAAA,AAAW,EAAC,OAAO,EAAE;YAAE,MAAM,EAAE;gBAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;aAAC;QAAA,CAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAClJ,KAAK,CAAC,IAAI,kLACN,cAAW,AAAX,EACI,OAAO,EACP;YAAE,MAAM,EAAE;gBAAC,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;aAAC;QAAA,CAAE,EACnI,UAAU,CAAC,iBAAiB,CAC/B,CACJ,CAAC;QACF,KAAK,CAAC,IAAI,kLACN,cAAA,AAAW,EACP,OAAO,EACP;YAAE,MAAM,EAAE;gBAAC,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAAE,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;aAAC;QAAA,CAAE,EACnI,UAAU,CAAC,iBAAiB,CAC/B,CACJ,CAAC;QACF,KAAK,CAAC,IAAI,kLACN,cAAA,AAAW,EACP,OAAO,EACP;YAAE,MAAM,EAAE;gBAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;gBAAE,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;aAAC;QAAA,CAAE,EACnI,UAAU,CAAC,iBAAiB,CAC/B,CACJ,CAAC;QACF,KAAK,CAAC,IAAI,kLACN,cAAA,AAAW,EACP,OAAO,EACP;YAAE,MAAM,EAAE;gBAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;gBAAE,sKAAI,UAAO,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;aAAC;QAAA,CAAE,EACnI,UAAU,CAAC,iBAAiB,CAC/B,CACJ,CAAC;QACF,KAAK,CAAC,IAAI,CACN,+LAAA,AAAW,EACP,OAAO,EACP;YAAE,MAAM,EAAE;gBAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBAAE,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;aAAC;QAAA,CAAE,EACnI,UAAU,CAAC,iBAAiB,CAC/B,CACJ,CAAC;QACF,KAAK,CAAC,IAAI,CACN,+LAAA,AAAW,EACP,OAAO,EACP;YAAE,MAAM,EAAE;gBAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBAAE,sKAAI,UAAO,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;aAAC;QAAA,CAAE,EACnI,UAAU,CAAC,iBAAiB,CAC/B,CACJ,CAAC;QACF,KAAK,CAAC,IAAI,kLACN,cAAA,AAAW,EACP,OAAO,EACP;YACI,MAAM,EAAE;gBACJ,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBAC/F,sKAAI,UAAO,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;aACzE;SACJ,EACD,UAAU,CAAC,iBAAiB,CAC/B,CACJ,CAAC;QACF,KAAK,CAAC,IAAI,kLACN,cAAA,AAAW,EACP,OAAO,EACP;YACI,MAAM,EAAE;gBACJ,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBAC/F,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;aACzE;SACJ,EACD,UAAU,CAAC,iBAAiB,CAC/B,CACJ,CAAC;QACF,KAAK,CAAC,IAAI,kLACN,cAAA,AAAW,EACP,OAAO,EACP;YACI,MAAM,EAAE;gBACJ,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBAC/F,sKAAI,UAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,CAAC;aACzE;SACJ,EACD,UAAU,CAAC,iBAAiB,CAC/B,CACJ,CAAC;QACF,KAAK,MAAM,CAAC,IAAI,KAAK,CAAE,CAAC;YACpB,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;YAChB,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,sKAAI,UAAO,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtI,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE/C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAErB,0BAA0B;QAC1B,IAAI,CAAC,oBAAoB,GAAG,sKAAI,gBAAa,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAChF,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,GAAG,qKAAI,cAAU,EAAE,CAAC;QAChE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC;YAC1B,MAAM,MAAM,kLAAG,YAAA,AAAS,EAAC,EAAE,EAAE;gBAAE,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;gBAAE,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;gBAAE,KAAK,EAAE,GAAG;YAAA,CAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;YACpJ,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,yBAAyB,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,kBAAkB,GAAG,qKAAI,cAAU,EAAE,CAAC;YAC7C,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;YACxC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC;YAE9B,gBAAgB;YAChB,MAAM,yBAAyB,GAAG,yLAAI,sBAAmB,CAAC,CAAA,CAAE,CAAC,CAAC;YAC9D,yBAAyB,CAAC,YAAY,GAAG,KAAK,CAAC;YAC/C,yBAAyB,CAAC,eAAe,GAAG,KAAK,CAAC;YAClD,MAAM,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC;YAC9C,MAAM,qBAAqB,GAAG,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACnD,IAAI,qBAAqB,GAAG,CAAC,CAAC;YAC9B,IAAI,mBAAmB,GAAG,CAAC,CAAC;YAC5B,yBAAyB,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACrD,qBAAqB,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAC/C,qBAAqB,GAAG,CAAC,CAAC;gBAC1B,mBAAmB,GAAG,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;YACH,MAAM,WAAW,GAAG;gBAChB,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;kLACxC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9G,yKAAO,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC;YACF,yBAAyB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACrD,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC;oBAAE,aAAa,EAAA,EAAA,0BAAA,EAAwB;oBAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,KAAK,EAAE;gBAAA,CAAE,CAAC,CAAC;gBAChI,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;oBAChD,IAAI,cAAc,IAAK,cAAuB,CAAC,OAAO,IAAK,cAAuB,CAAC,OAAO,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE,CAAC;gLAC1H,UAAM,CAAC,IAAI,CAAC,6FAA6F,CAAC,CAAC;wBAC3G,OAAO;oBACX,CAAC;iLACD,aAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAExD,MAAM,kBAAkB,GAAG,qBAAqB,CAAC;oBAEjD,+CAA+C;oBAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,4KAAO,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC,CAAC;oBAClG,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,cAAc,EAAE,CAAC;oBAErE,4EAA4E;oBAC5E,IAAI,WAAW,qKAAG,UAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;oBAE5H,0CAA0C;oBAC1C,WAAW,GAAG,AAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;oBAEpG,uBAAuB;oBACvB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;wBACxC,IAAI,CAAC,YAAY,CAAC,kBAAkB,qKAAG,aAAU,CAAC,oBAAoB,CAClE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAC5B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,EAC5B,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAC/B,CAAC;oBACN,CAAC;oBACD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;wBACvC,IAAI,CAAC,WAAW,CAAC,kBAAkB,qKAAG,aAAU,CAAC,oBAAoB,CACjE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAC3B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAC3B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAC9B,CAAC;oBACN,CAAC;oBAED,0DAA0D;oBAC1D,qBAAqB,IAAI,WAAW,CAAC;oBACrC,IAAI,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC;wBACjD,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;4BAChC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACjI,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;4BACpD,WAAW,GAAG,KAAK,GAAG,mBAAmB,CAAC;4BAC1C,mBAAmB,GAAG,KAAK,CAAC;wBAChC,CAAC;wBACD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;8LACT,aAAU,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;wBACjF,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;8LAChB,aAAU,CAAC,yBAAyB,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;wBACjF,CAAC,MAAM,CAAC;8LACJ,aAAU,CAAC,yBAAyB,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;wBACjF,CAAC;wBAED,gFAAgF;wBAChF,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,EAAE,CAAC;4BACzC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;wBACnE,CAAC;wBACD,uCAAuC;wBACvC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC7C,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,CAAC;4BACnD,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;wBAC3C,CAAC;wBACD,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;wBAChC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;wBAC5G,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC;wBAChD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAChD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;oBAChD,CAAC;oBACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;iLAEzB,aAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACrD,CAAC;gBACD,IAAI,CAAC,YAAY,EAAE,CAAC;YACxB,CAAC,CAAC,CAAC;YAEH,wBAAwB;YACxB,yBAAyB,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACrD,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC;oBAAE,aAAa,EAAA,EAAA,0BAAA,EAAwB;oBAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,KAAK,EAAE;gBAAA,CAAE,CAAC,CAAC;gBACvH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;YACH,yBAAyB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACxD,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC;oBAAE,aAAa,EAAA,EAAA,0BAAA,EAAwB;oBAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,KAAK,EAAE;gBAAA,CAAE,CAAC,CAAC;gBACnI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACvB,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YAEjE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEnD,qBAAqB;QACrB,IAAI,CAAC,iBAAiB,GAAG,qKAAI,iBAAa,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7E,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,GAAG,sKAAI,aAAU,EAAE,CAAC;QAC7D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;YACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;gBACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;oBACzB,+BAA+B;oBAC/B,MAAM,aAAa,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChF,IAAI,aAAa,KAAK,CAAC,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;wBAC7C,SAAS;oBACb,CAAC;oBAED,MAAM,GAAG,GAAG,aAAa,KAAK,CAAC,CAAC,CAAC,gLAAC,YAAA,AAAS,EAAC,EAAE,EAAE;wBAAE,IAAI,EAAE,CAAC;oBAAA,CAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;oBAC7H,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;wBACtB,GAAG,CAAC,kBAAkB,oKAAG,cAAU,CAAC,eAAe,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;oBACrH,CAAC;oBAED,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;oBACrC,GAAG,CAAC,iBAAiB,GAAG,aAAa,KAAK,CAAC,CAAC,CAAC,+BAA+B;oBAC5E,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;oBAE3B,gEAAgE;sLAChE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC/C,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;oBAClC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,WAAW,mKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/D,MAAM,QAAQ,qKAAG,UAAO,CAAC,oBAAoB,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3F,QAAQ,CAAC,SAAS,EAAE,CAAC;oBAErB,iBAAiB;oBACjB,MAAM,sBAAsB,GAAG,yLAAI,sBAAmB,CAAC;wBAAE,QAAQ,EAAE,QAAQ;oBAAA,CAAE,CAAC,CAAC;oBAC/E,sBAAsB,CAAC,eAAe,GAAG,KAAK,CAAC;oBAC/C,sBAAsB,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC5C,IAAI,yBAAyB,GAAG,CAAC,CAAC;oBAClC,IAAI,aAAa,GAAG,CAAC,CAAC;oBACtB,GAAG,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;oBACxC,sBAAsB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;wBAClD,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC;4BAAE,aAAa,EAAA,EAAA,yBAAA,EAAuB;4BAAE,QAAQ,EAAE,sKAAI,UAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;wBAAA,CAAE,CAAC,CAAC;wBACpI,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;4BACpB,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;4BAChD,IAAI,cAAc,IAAK,cAAuB,CAAC,OAAO,IAAK,cAAuB,CAAC,OAAO,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE,CAAC;yLAC1H,SAAM,CAAC,IAAI,CAAC,6FAA6F,CAAC,CAAC;gCAC3G,OAAO;4BACX,CAAC;yLACD,aAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;4BACxD,IAAI,oBAAoB,GAAG,AAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;4BACxH,yBAAyB,IAAI,oBAAoB,CAAC;4BAClD,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;gCAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,yBAAyB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCACxI,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;gCACnD,oBAAoB,GAAG,KAAK,GAAG,aAAa,CAAC;gCAC7C,aAAa,GAAG,KAAK,CAAC;4BAC1B,CAAC;4BAED,MAAM,UAAU,GAAG,sKAAI,UAAO,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,CAAC,CAAC;4BACjG,MAAM,SAAS,GAAG,sKAAI,UAAO,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;4BAE3E,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;gCACtB,+DAA+D;gCAC/D,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gCACrC,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gCACrC,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;4BACzC,CAAC;4BAED,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;4BAC9C,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;4BAE7C,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;4BAC7C,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;4BAC5C,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;4BAEpD,IAAI,CAAC,iBAAiB,EAAE,CAAC;4BACzB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gCAClB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gCACnF,wEAAwE;gCACxE,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;kMAC1D,UAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gCAC7F,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gCAC3D,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;kMACzE,UAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gCAC7F,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;4BAC1D,CAAC,MAAM,CAAC;gCACJ,iDAAiD;gCACjD,GAAG,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gCAC/E,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gCAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,EAAE,CAAC;oCACzC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;gCACjF,CAAC;4BACL,CAAC;4BAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;4BAC7C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gCACvB,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,wKAAG,UAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;gCACzG,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,wKAAG,UAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;gCACzG,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,GAAG,+KAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;gCAEzG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gCACtG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gCACtG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gCAEtG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gCAChG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gCAChG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;4BACpG,CAAC,MAAM,CAAC;gCACJ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gCAChD,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;oCACrG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;gCACzD,CAAC;4BACL,CAAC;4BACD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;4BAChD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;yLAC5C,aAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBACrD,CAAC;wBACD,IAAI,CAAC,YAAY,EAAE,CAAC;oBACxB,CAAC,CAAC,CAAC;oBAEH,wBAAwB;oBACxB,sBAAsB,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;wBAClD,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC;4BAAE,aAAa,EAAA,EAAA,yBAAA,EAAuB;4BAAE,QAAQ,EAAE,sKAAI,UAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;wBAAA,CAAE,CAAC,CAAC;wBACjI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;wBACtB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;wBACtB,yBAAyB,GAAG,CAAC,CAAC;wBAC9B,aAAa,GAAG,CAAC,CAAC;wBAClB,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC,CAAC;wBACnE,IAAI,CAAC,8BAA8B,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;oBAC3E,CAAC,CAAC,CAAC;oBACH,sBAAsB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;wBACrD,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC;4BAAE,aAAa,EAAA,EAAA,yBAAA,EAAuB;4BAAE,QAAQ,EAAE,sKAAI,UAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;wBAAA,CAAE,CAAC,CAAC;wBACvI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;wBACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;wBACvB,IAAI,CAAC,YAAY,EAAE,CAAC;wBACpB,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;oBACvD,CAAC,CAAC,CAAC;oBAEH,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBACrC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEhD,qBAAqB;QACrB,MAAM,UAAU,GAAmB,EAAE,CAAC;QACtC,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACzF,IAAI,CAAC,UAAU,CAAiB,WAAW,CAAC,KAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAC,CAAC;gBAE1G,KAAK,MAAM,IAAI,IAAI,MAAM,CAAE,CAAC;oBACxB,IAAI,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC;wBAClE,UAAU,CAAiB,WAAW,CAAC,KAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;wBAChE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC;wBAC3C,IAAI,CAAC,sBAAsB,CAAC,eAAe,EAAE,CAAC;wBAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC3B,CAAC;gBACL,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,IAAI,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,IAAI,UAAU,CAAiB,WAAW,CAAC,KAAM,CAAC,SAAS,CAAC,EAAE,CAAC;oBACtH,UAAU,CAAiB,WAAW,CAAC,KAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;oBAC1F,UAAU,CAAC,MAAM,CAAiB,WAAW,CAAC,KAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;oBACnE,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;oBAC5C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBAC5B,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,gCAAgC;QAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACnF,sDAAsD;YACtD,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClF,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,MAAM,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACtE,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC;YAED,6FAA6F;YAC7F,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;gBAC5E,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAmB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC5G,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClG,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;CAqXJ", "debugId": null}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/planeRotationGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/planeRotationGizmo.ts"], "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { Quaternion, Matrix, Vector3, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport \"../Meshes/Builders/linesBuilder\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport type { Node } from \"../node\";\r\nimport { PointerDragBehavior } from \"../Behaviors/Meshes/pointerDragBehavior\";\r\nimport type { GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { RotationGizmo } from \"./rotationGizmo\";\r\nimport { ShaderMaterial } from \"../Materials/shaderMaterial\";\r\nimport { Effect } from \"../Materials/effect\";\r\nimport { CreatePlane } from \"../Meshes/Builders/planeBuilder\";\r\nimport { CreateTorus } from \"../Meshes/Builders/torusBuilder\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\n\r\n/**\r\n * Interface for plane rotation gizmo\r\n */\r\nexport interface IPlaneRotationGizmo extends IGizmo {\r\n    /** Drag behavior responsible for the gizmos dragging interactions */\r\n    dragBehavior: PointerDragBehavior;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /** Sensitivity factor for dragging */\r\n    sensitivity: number;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    onSnapObservable: Observable<{ snapDistance: number }>;\r\n    /** Accumulated relative angle value for rotation on the axis. */\r\n    angle: number;\r\n    /** If the gizmo is enabled */\r\n    isEnabled: boolean;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    coloredMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is hovered with mouse */\r\n    hoverMaterial: StandardMaterial;\r\n    /** Color used to render the drag angle sector when gizmo is rotated with mouse */\r\n    rotationColor: Color3;\r\n    /** Material used to render when gizmo is disabled. typically grey. */\r\n    disableMaterial: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Single plane rotation gizmo\r\n */\r\nexport class PlaneRotationGizmo extends Gizmo implements IPlaneRotationGizmo {\r\n    /**\r\n     * Drag behavior responsible for the gizmos dragging interactions\r\n     */\r\n    public dragBehavior: PointerDragBehavior;\r\n    protected _pointerObserver: Nullable<Observer<PointerInfo>> = null;\r\n\r\n    /**\r\n     * Rotation distance in radians that the gizmo will snap to (Default: 0)\r\n     */\r\n    public snapDistance = 0;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    public onSnapObservable = new Observable<{ snapDistance: number }>();\r\n\r\n    /**\r\n     * The maximum angle between the camera and the rotation allowed for interaction\r\n     * If a rotation plane appears 'flat', a lower value allows interaction.\r\n     */\r\n    public static MaxDragAngle: number = (Math.PI * 9) / 20;\r\n\r\n    /**\r\n     * Accumulated relative angle value for rotation on the axis. Reset to 0 when a dragStart occurs\r\n     */\r\n    public angle: number = 0;\r\n\r\n    /**\r\n     * Custom sensitivity value for the drag strength\r\n     */\r\n    public sensitivity = 1;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    public get coloredMaterial() {\r\n        return this._coloredMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is hovered with mouse */\r\n    public get hoverMaterial() {\r\n        return this._hoverMaterial;\r\n    }\r\n\r\n    /** Color used to render the drag angle sector when gizmo is rotated with mouse */\r\n    public set rotationColor(color: Color3) {\r\n        this._rotationShaderMaterial.setColor3(\"rotationColor\", color);\r\n    }\r\n\r\n    /** Material used to render when gizmo is disabled. typically grey. */\r\n    public get disableMaterial() {\r\n        return this._disableMaterial;\r\n    }\r\n\r\n    protected _isEnabled: boolean = true;\r\n    protected _parent: Nullable<RotationGizmo> = null;\r\n    protected _coloredMaterial: StandardMaterial;\r\n    protected _hoverMaterial: StandardMaterial;\r\n    protected _disableMaterial: StandardMaterial;\r\n    protected _gizmoMesh: Mesh;\r\n    protected _rotationDisplayPlane: Mesh;\r\n    protected _dragging: boolean = false;\r\n    protected _angles = new Vector3();\r\n\r\n    protected static _RotationGizmoVertexShader = `\r\n        precision highp float;\r\n        attribute vec3 position;\r\n        attribute vec2 uv;\r\n        uniform mat4 worldViewProjection;\r\n        varying vec3 vPosition;\r\n        varying vec2 vUV;\r\n\r\n        void main(void) {\r\n            gl_Position = worldViewProjection * vec4(position, 1.0);\r\n            vUV = uv;\r\n        }`;\r\n\r\n    protected static _RotationGizmoFragmentShader = `\r\n        precision highp float;\r\n        varying vec2 vUV;\r\n        varying vec3 vPosition;\r\n        uniform vec3 angles;\r\n        uniform vec3 rotationColor;\r\n\r\n        #define twopi 6.283185307\r\n\r\n        void main(void) {\r\n            vec2 uv = vUV - vec2(0.5);\r\n            float angle = atan(uv.y, uv.x) + 3.141592;\r\n            float delta = gl_FrontFacing ? angles.y : -angles.y;\r\n            float begin = angles.x - delta * angles.z;\r\n            float start = (begin < (begin + delta)) ? begin : (begin + delta);\r\n            float end = (begin > (begin + delta)) ? begin : (begin + delta);\r\n            float len = sqrt(dot(uv,uv));\r\n            float opacity = 1. - step(0.5, len);\r\n\r\n            float base = abs(floor(start / twopi)) * twopi;\r\n            start += base;\r\n            end += base;\r\n\r\n            float intensity = 0.;\r\n            for (int i = 0; i < 5; i++)\r\n            {\r\n                intensity += max(step(start, angle) - step(end, angle), 0.);\r\n                angle += twopi;\r\n            }\r\n            gl_FragColor = vec4(rotationColor, min(intensity * 0.25, 0.8)) * opacity;\r\n        }\r\n    `;\r\n\r\n    protected _rotationShaderMaterial: ShaderMaterial;\r\n\r\n    /**\r\n     * Creates a PlaneRotationGizmo\r\n     * @param planeNormal The normal of the plane which the gizmo will be able to rotate on\r\n     * @param color The color of the gizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param tessellation Amount of tessellation to be used when creating rotation circles\r\n     * @param parent\r\n     * @param useEulerRotation Use and update Euler angle instead of quaternion\r\n     * @param thickness display gizmo axis thickness\r\n     * @param hoverColor The color of the gizmo when hovering over and dragging\r\n     * @param disableColor The Color of the gizmo when its disabled\r\n     */\r\n    constructor(\r\n        planeNormal: Vector3,\r\n        color: Color3 = Color3.Gray(),\r\n        gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer,\r\n        tessellation = 32,\r\n        parent: Nullable<RotationGizmo> = null,\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        useEulerRotation = false,\r\n        thickness: number = 1,\r\n        hoverColor: Color3 = Color3.Yellow(),\r\n        disableColor: Color3 = Color3.Gray()\r\n    ) {\r\n        super(gizmoLayer);\r\n        this._parent = parent;\r\n        // Create Material\r\n        this._coloredMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._coloredMaterial.diffuseColor = color;\r\n        this._coloredMaterial.specularColor = color.subtract(new Color3(0.1, 0.1, 0.1));\r\n\r\n        this._hoverMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._hoverMaterial.diffuseColor = hoverColor;\r\n        this._hoverMaterial.specularColor = hoverColor;\r\n\r\n        this._disableMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._disableMaterial.diffuseColor = disableColor;\r\n        this._disableMaterial.alpha = 0.4;\r\n\r\n        // Build mesh on root node\r\n        this._gizmoMesh = new Mesh(\"\", gizmoLayer.utilityLayerScene);\r\n        const { rotationMesh, collider } = this._createGizmoMesh(this._gizmoMesh, thickness, tessellation);\r\n\r\n        // Setup Rotation Circle\r\n        this._rotationDisplayPlane = CreatePlane(\r\n            \"rotationDisplay\",\r\n            {\r\n                size: 0.6,\r\n                updatable: false,\r\n            },\r\n            this.gizmoLayer.utilityLayerScene\r\n        );\r\n        this._rotationDisplayPlane.rotation.z = Math.PI * 0.5;\r\n        this._rotationDisplayPlane.parent = this._gizmoMesh;\r\n        this._rotationDisplayPlane.setEnabled(false);\r\n\r\n        Effect.ShadersStore[\"rotationGizmoVertexShader\"] = PlaneRotationGizmo._RotationGizmoVertexShader;\r\n        Effect.ShadersStore[\"rotationGizmoFragmentShader\"] = PlaneRotationGizmo._RotationGizmoFragmentShader;\r\n        this._rotationShaderMaterial = new ShaderMaterial(\r\n            \"shader\",\r\n            this.gizmoLayer.utilityLayerScene,\r\n            {\r\n                vertex: \"rotationGizmo\",\r\n                fragment: \"rotationGizmo\",\r\n            },\r\n            {\r\n                attributes: [\"position\", \"uv\"],\r\n                uniforms: [\"worldViewProjection\", \"angles\", \"rotationColor\"],\r\n            }\r\n        );\r\n        this._rotationShaderMaterial.backFaceCulling = false;\r\n        this.rotationColor = hoverColor;\r\n\r\n        this._rotationDisplayPlane.material = this._rotationShaderMaterial;\r\n        this._rotationDisplayPlane.visibility = 0.999;\r\n\r\n        this._gizmoMesh.lookAt(this._rootMesh.position.add(planeNormal));\r\n        this._rootMesh.addChild(this._gizmoMesh, Gizmo.PreserveScaling);\r\n        this._gizmoMesh.scaling.scaleInPlace(1 / 3);\r\n        // Add drag behavior to handle events when the gizmo is dragged\r\n        this.dragBehavior = new PointerDragBehavior({ dragPlaneNormal: planeNormal });\r\n        this.dragBehavior.moveAttached = false;\r\n        this.dragBehavior.maxDragAngle = PlaneRotationGizmo.MaxDragAngle;\r\n        this.dragBehavior._useAlternatePickedPointAboveMaxDragAngle = true;\r\n        this._rootMesh.addBehavior(this.dragBehavior);\r\n\r\n        // Closures for drag logic\r\n        const lastDragPosition = new Vector3();\r\n\r\n        const rotationMatrix = new Matrix();\r\n        const planeNormalTowardsCamera = new Vector3();\r\n        let localPlaneNormalTowardsCamera = new Vector3();\r\n\r\n        this.dragBehavior.onDragStartObservable.add((e) => {\r\n            if (this.attachedNode) {\r\n                lastDragPosition.copyFrom(e.dragPlanePoint);\r\n                this._rotationDisplayPlane.setEnabled(true);\r\n\r\n                this._rotationDisplayPlane.getWorldMatrix().invertToRef(rotationMatrix);\r\n                Vector3.TransformCoordinatesToRef(e.dragPlanePoint, rotationMatrix, lastDragPosition);\r\n\r\n                this._angles.x = Math.atan2(lastDragPosition.y, lastDragPosition.x) + Math.PI;\r\n                this._angles.y = 0;\r\n                this._angles.z = this.updateGizmoRotationToMatchAttachedMesh ? 1 : 0;\r\n                this._dragging = true;\r\n                lastDragPosition.copyFrom(e.dragPlanePoint);\r\n                this._rotationShaderMaterial.setVector3(\"angles\", this._angles);\r\n                this.angle = 0;\r\n            }\r\n        });\r\n\r\n        this.dragBehavior.onDragEndObservable.add(() => {\r\n            this._dragging = false;\r\n            this._rotationDisplayPlane.setEnabled(false);\r\n        });\r\n\r\n        const tmpSnapEvent = { snapDistance: 0 };\r\n        let currentSnapDragDistance = 0;\r\n        const tmpMatrix = new Matrix();\r\n        const amountToRotate = new Quaternion();\r\n        this.dragBehavior.onDragObservable.add((event) => {\r\n            if (this.attachedNode) {\r\n                // Calc angle over full 360 degree (https://stackoverflow.com/questions/43493711/the-angle-between-two-3d-vectors-with-a-result-range-0-360)\r\n                const nodeScale = new Vector3(1, 1, 1);\r\n                const nodeQuaternion = new Quaternion(0, 0, 0, 1);\r\n                const nodeTranslation = new Vector3(0, 0, 0);\r\n                const attachedNodeTransform = this._attachedNode as TransformNode;\r\n                // check there is an active pivot for the TransformNode attached\r\n                if (attachedNodeTransform && attachedNodeTransform.isUsingPivotMatrix && attachedNodeTransform.isUsingPivotMatrix() && attachedNodeTransform.position) {\r\n                    // When a TransformNode has an active pivot, even without parenting,\r\n                    // translation from the world matrix is different from TransformNode.position.\r\n                    // Pivot works like a virtual parent that's using the node orientation.\r\n                    // As the world matrix is transformed by the gizmo and then decomposed to TRS\r\n                    // its translation part must be set to the Node's position.\r\n                    attachedNodeTransform.getWorldMatrix().setTranslation(attachedNodeTransform.position);\r\n                }\r\n\r\n                this.attachedNode.getWorldMatrix().decompose(nodeScale, nodeQuaternion, nodeTranslation);\r\n                // uniform scaling of absolute value of components\r\n                // (-1,1,1) is uniform but (1,1.001,1) is not\r\n                const uniformScaling = Math.abs(Math.abs(nodeScale.x) - Math.abs(nodeScale.y)) <= Epsilon && Math.abs(Math.abs(nodeScale.x) - Math.abs(nodeScale.z)) <= Epsilon;\r\n                if (!uniformScaling && this.updateGizmoRotationToMatchAttachedMesh) {\r\n                    Logger.Warn(\r\n                        \"Unable to use a rotation gizmo matching mesh rotation with non uniform scaling. Use uniform scaling or set updateGizmoRotationToMatchAttachedMesh to false.\"\r\n                    );\r\n                    return;\r\n                }\r\n                nodeQuaternion.normalize();\r\n\r\n                const nodeTranslationForOperation = this.updateGizmoPositionToMatchAttachedMesh ? nodeTranslation : this._rootMesh.absolutePosition;\r\n                const newVector = event.dragPlanePoint.subtract(nodeTranslationForOperation).normalize();\r\n                const originalVector = lastDragPosition.subtract(nodeTranslationForOperation).normalize();\r\n                const cross = Vector3.Cross(newVector, originalVector);\r\n                const dot = Vector3.Dot(newVector, originalVector);\r\n                let angle = Math.atan2(cross.length(), dot) * this.sensitivity;\r\n                planeNormalTowardsCamera.copyFrom(planeNormal);\r\n                localPlaneNormalTowardsCamera.copyFrom(planeNormal);\r\n                if (this.updateGizmoRotationToMatchAttachedMesh) {\r\n                    nodeQuaternion.toRotationMatrix(rotationMatrix);\r\n                    localPlaneNormalTowardsCamera = Vector3.TransformCoordinates(planeNormalTowardsCamera, rotationMatrix);\r\n                }\r\n                // Flip up vector depending on which side the camera is on\r\n                let cameraFlipped = false;\r\n                if (gizmoLayer.utilityLayerScene.activeCamera) {\r\n                    const camVec = gizmoLayer.utilityLayerScene.activeCamera.position.subtract(nodeTranslationForOperation).normalize();\r\n                    if (Vector3.Dot(camVec, localPlaneNormalTowardsCamera) > 0) {\r\n                        planeNormalTowardsCamera.scaleInPlace(-1);\r\n                        localPlaneNormalTowardsCamera.scaleInPlace(-1);\r\n                        cameraFlipped = true;\r\n                    }\r\n                }\r\n                const halfCircleSide = Vector3.Dot(localPlaneNormalTowardsCamera, cross) > 0.0;\r\n                if (halfCircleSide) {\r\n                    angle = -angle;\r\n                }\r\n\r\n                TmpVectors.Vector3[0].set(angle, 0, 0);\r\n                if (!this.dragBehavior.validateDrag(TmpVectors.Vector3[0])) {\r\n                    angle = 0;\r\n                }\r\n\r\n                // Snapping logic\r\n                let snapped = false;\r\n                if (this.snapDistance != 0) {\r\n                    currentSnapDragDistance += angle;\r\n                    if (Math.abs(currentSnapDragDistance) > this.snapDistance) {\r\n                        let dragSteps = Math.floor(Math.abs(currentSnapDragDistance) / this.snapDistance);\r\n                        if (currentSnapDragDistance < 0) {\r\n                            dragSteps *= -1;\r\n                        }\r\n                        currentSnapDragDistance = currentSnapDragDistance % this.snapDistance;\r\n                        angle = this.snapDistance * dragSteps;\r\n                        snapped = true;\r\n                    } else {\r\n                        angle = 0;\r\n                    }\r\n                }\r\n\r\n                // Convert angle and axis to quaternion (http://www.euclideanspace.com/maths/geometry/rotations/conversions/angleToQuaternion/index.htm)\r\n                const quaternionCoefficient = Math.sin(angle / 2);\r\n                amountToRotate.set(\r\n                    planeNormalTowardsCamera.x * quaternionCoefficient,\r\n                    planeNormalTowardsCamera.y * quaternionCoefficient,\r\n                    planeNormalTowardsCamera.z * quaternionCoefficient,\r\n                    Math.cos(angle / 2)\r\n                );\r\n\r\n                // If the meshes local scale is inverted (eg. loaded gltf file parent with z scale of -1) the rotation needs to be inverted on the y axis\r\n                if (tmpMatrix.determinant() > 0) {\r\n                    const tmpVector = new Vector3();\r\n                    amountToRotate.toEulerAnglesToRef(tmpVector);\r\n                    Quaternion.RotationYawPitchRollToRef(tmpVector.y, -tmpVector.x, -tmpVector.z, amountToRotate);\r\n                }\r\n\r\n                if (this.updateGizmoRotationToMatchAttachedMesh) {\r\n                    // Rotate selected mesh quaternion over fixed axis\r\n                    nodeQuaternion.multiplyToRef(amountToRotate, nodeQuaternion);\r\n                    nodeQuaternion.normalize();\r\n                    // recompose matrix\r\n                    Matrix.ComposeToRef(nodeScale, nodeQuaternion, nodeTranslation, this.attachedNode.getWorldMatrix());\r\n                } else {\r\n                    // Rotate selected mesh quaternion over rotated axis\r\n                    amountToRotate.toRotationMatrix(TmpVectors.Matrix[0]);\r\n                    const translation = this.attachedNode.getWorldMatrix().getTranslation();\r\n                    this.attachedNode.getWorldMatrix().multiplyToRef(TmpVectors.Matrix[0], this.attachedNode.getWorldMatrix());\r\n                    this.attachedNode.getWorldMatrix().setTranslation(translation);\r\n                }\r\n\r\n                lastDragPosition.copyFrom(event.dragPlanePoint);\r\n                if (snapped) {\r\n                    tmpSnapEvent.snapDistance = angle;\r\n                    this.onSnapObservable.notifyObservers(tmpSnapEvent);\r\n                }\r\n                this._angles.y += gizmoLayer.utilityLayerScene.useRightHandedSystem ? -angle : angle;\r\n                this.angle += cameraFlipped ? -angle : angle;\r\n                this._rotationShaderMaterial.setVector3(\"angles\", this._angles);\r\n                this._matrixChanged();\r\n            }\r\n        });\r\n\r\n        const light = gizmoLayer._getSharedGizmoLight();\r\n        light.includedOnlyMeshes = light.includedOnlyMeshes.concat(this._rootMesh.getChildMeshes(false));\r\n\r\n        const cache: GizmoAxisCache = {\r\n            colliderMeshes: [collider],\r\n            gizmoMeshes: [rotationMesh],\r\n            material: this._coloredMaterial,\r\n            hoverMaterial: this._hoverMaterial,\r\n            disableMaterial: this._disableMaterial,\r\n            active: false,\r\n            dragBehavior: this.dragBehavior,\r\n        };\r\n        this._parent?.addToAxisCache(this._gizmoMesh, cache);\r\n\r\n        this._pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (this._customMeshSet) {\r\n                return;\r\n            }\r\n            // updating here the maxangle because ondragstart is too late (value already used) and the updated value is not taken into account\r\n            this.dragBehavior.maxDragAngle = PlaneRotationGizmo.MaxDragAngle;\r\n            this._isHovered = !!(cache.colliderMeshes.indexOf(<Mesh>pointerInfo?.pickInfo?.pickedMesh) != -1);\r\n            if (!this._parent) {\r\n                const material = cache.dragBehavior.enabled ? (this._isHovered || this._dragging ? this._hoverMaterial : this._coloredMaterial) : this._disableMaterial;\r\n                this._setGizmoMeshMaterial(cache.gizmoMeshes, material);\r\n            }\r\n        });\r\n\r\n        this.dragBehavior.onEnabledObservable.add((newState) => {\r\n            this._setGizmoMeshMaterial(cache.gizmoMeshes, newState ? this._coloredMaterial : this._disableMaterial);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Create Geometry for Gizmo\r\n     * @param parentMesh\r\n     * @param thickness\r\n     * @param tessellation\r\n     * @returns\r\n     */\r\n    protected _createGizmoMesh(parentMesh: AbstractMesh, thickness: number, tessellation: number) {\r\n        const collider = CreateTorus(\r\n            \"ignore\",\r\n            {\r\n                diameter: 0.6,\r\n                thickness: 0.03 * thickness,\r\n                tessellation,\r\n            },\r\n            this.gizmoLayer.utilityLayerScene\r\n        );\r\n        collider.visibility = 0;\r\n        const rotationMesh = CreateTorus(\r\n            \"\",\r\n            {\r\n                diameter: 0.6,\r\n                thickness: 0.005 * thickness,\r\n                tessellation,\r\n            },\r\n            this.gizmoLayer.utilityLayerScene\r\n        );\r\n        rotationMesh.material = this._coloredMaterial;\r\n\r\n        // Position arrow pointing in its drag axis\r\n        rotationMesh.rotation.x = Math.PI / 2;\r\n        collider.rotation.x = Math.PI / 2;\r\n\r\n        parentMesh.addChild(rotationMesh, Gizmo.PreserveScaling);\r\n        parentMesh.addChild(collider, Gizmo.PreserveScaling);\r\n        return { rotationMesh, collider };\r\n    }\r\n\r\n    protected override _attachedNodeChanged(value: Nullable<Node>) {\r\n        if (this.dragBehavior) {\r\n            this.dragBehavior.enabled = value ? true : false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the gizmo is enabled\r\n     */\r\n    public set isEnabled(value: boolean) {\r\n        this._isEnabled = value;\r\n        if (!value) {\r\n            this.attachedMesh = null;\r\n        } else {\r\n            if (this._parent) {\r\n                this.attachedMesh = this._parent.attachedMesh;\r\n            }\r\n        }\r\n    }\r\n\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public override dispose() {\r\n        this.onSnapObservable.clear();\r\n        this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(this._pointerObserver);\r\n        this.dragBehavior.detach();\r\n        if (this._gizmoMesh) {\r\n            this._gizmoMesh.dispose();\r\n        }\r\n        if (this._rotationDisplayPlane) {\r\n            this._rotationDisplayPlane.dispose();\r\n        }\r\n        if (this._rotationShaderMaterial) {\r\n            this._rotationShaderMaterial.dispose();\r\n        }\r\n        const materials = [this._coloredMaterial, this._hoverMaterial, this._disableMaterial];\r\n        for (const matl of materials) {\r\n            if (matl) {\r\n                matl.dispose();\r\n            }\r\n        }\r\n        super.dispose();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAC/E,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,iCAAiC,CAAC;AAEzC,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAEtC,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAE9E,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAEjE,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;;;;;;;;;;;;;;;;AAoClC,MAAO,kBAAmB,mKAAQ,QAAK;IAiCzC,0EAAA,EAA4E,CAC5E,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,6DAAA,EAA+D,CAC/D,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,gFAAA,EAAkF,CAClF,IAAW,aAAa,CAAC,KAAa,EAAA;QAClC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED,oEAAA,EAAsE,CACtE,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IA4UD;;;;;;;OAOG,CACO,gBAAgB,CAAC,UAAwB,EAAE,SAAiB,EAAE,YAAoB,EAAA;QACxF,MAAM,QAAQ,IAAG,8LAAA,AAAW,EACxB,QAAQ,EACR;YACI,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,IAAI,GAAG,SAAS;YAC3B,YAAY;SACf,EACD,IAAI,CAAC,UAAU,CAAC,iBAAiB,CACpC,CAAC;QACF,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC;QACxB,MAAM,YAAY,oLAAG,cAAA,AAAW,EAC5B,EAAE,EACF;YACI,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,KAAK,GAAG,SAAS;YAC5B,YAAY;SACf,EACD,IAAI,CAAC,UAAU,CAAC,iBAAiB,CACpC,CAAC;QACF,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE9C,2CAA2C;QAC3C,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACtC,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAElC,UAAU,CAAC,QAAQ,CAAC,YAAY,4JAAE,QAAK,CAAC,eAAe,CAAC,CAAC;QACzD,UAAU,CAAC,QAAQ,CAAC,QAAQ,4JAAE,QAAK,CAAC,eAAe,CAAC,CAAC;QACrD,OAAO;YAAE,YAAY;YAAE,QAAQ;QAAA,CAAE,CAAC;IACtC,CAAC;IAEkB,oBAAoB,CAAC,KAAqB,EAAA;QACzD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACrD,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,CAAC,KAAc,EAAA;QAC/B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;YAClD,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;QAC3C,CAAC;QACD,MAAM,SAAS,GAAG;YAAC,IAAI,CAAC,gBAAgB;YAAE,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,gBAAgB;SAAC,CAAC;QACtF,KAAK,MAAM,IAAI,IAAI,SAAS,CAAE,CAAC;YAC3B,IAAI,IAAI,EAAE,CAAC;gBACP,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACL,CAAC;QACD,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAtWD;;;;;;;;;;;OAWG,CACH,YACI,WAAoB,EACpB,yKAAgB,SAAM,CAAC,IAAI,EAAE,EAC7B,yLAAmC,uBAAoB,CAAC,mBAAmB,EAC3E,YAAY,GAAG,EAAE,EACjB,SAAkC,IAAI,EACtC,6DAA6D;IAC7D,gBAAgB,GAAG,KAAK,EACxB,YAAoB,CAAC,EACrB,8KAAqB,SAAM,CAAC,MAAM,EAAE,EACpC,gLAAuB,SAAM,CAAC,IAAI,EAAE,CAAA;YAsOpC;QApOA,KAAK,CAAC,UAAU,CAAC,CAAC;QAlIZ,IAAA,CAAA,gBAAgB,GAAoC,IAAI,CAAC;QAEnE;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACxB;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAAG,iKAAI,aAAU,EAA4B,CAAC;QAQrE;;WAEG,CACI,IAAA,CAAA,KAAK,GAAW,CAAC,CAAC;QAEzB;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAsBb,IAAA,CAAA,UAAU,GAAY,IAAI,CAAC;QAC3B,IAAA,CAAA,OAAO,GAA4B,IAAI,CAAC;QAMxC,IAAA,CAAA,SAAS,GAAY,KAAK,CAAC;QAC3B,IAAA,CAAA,OAAO,GAAG,sKAAI,UAAO,EAAE,CAAC;QA2E9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,kBAAkB;QAClB,IAAI,CAAC,gBAAgB,GAAG,IAAI,2LAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,0KAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAEhF,IAAI,CAAC,cAAc,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,UAAU,CAAC;QAC9C,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,UAAU,CAAC;QAE/C,IAAI,CAAC,gBAAgB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,YAAY,CAAC;QAClD,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,GAAG,CAAC;QAElC,0BAA0B;QAC1B,IAAI,CAAC,UAAU,GAAG,6JAAI,OAAI,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7D,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAEnG,wBAAwB;QACxB,IAAI,CAAC,qBAAqB,oLAAG,cAAA,AAAW,EACpC,iBAAiB,EACjB;YACI,IAAI,EAAE,GAAG;YACT,SAAS,EAAE,KAAK;SACnB,EACD,IAAI,CAAC,UAAU,CAAC,iBAAiB,CACpC,CAAC;QACF,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QACtD,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QACpD,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;sKAE7C,SAAM,CAAC,YAAY,CAAC,2BAA2B,CAAC,GAAG,kBAAkB,CAAC,0BAA0B,CAAC;sKACjG,SAAM,CAAC,YAAY,CAAC,6BAA6B,CAAC,GAAG,kBAAkB,CAAC,4BAA4B,CAAC;QACrG,IAAI,CAAC,uBAAuB,GAAG,0KAAI,iBAAc,CAC7C,QAAQ,EACR,IAAI,CAAC,UAAU,CAAC,iBAAiB,EACjC;YACI,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,eAAe;SAC5B,EACD;YACI,UAAU,EAAE;gBAAC,UAAU;gBAAE,IAAI;aAAC;YAC9B,QAAQ,EAAE;gBAAC,qBAAqB;gBAAE,QAAQ;gBAAE,eAAe;aAAC;SAC/D,CACJ,CAAC;QACF,IAAI,CAAC,uBAAuB,CAAC,eAAe,GAAG,KAAK,CAAC;QACrD,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC;QAEhC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC;QACnE,IAAI,CAAC,qBAAqB,CAAC,UAAU,GAAG,KAAK,CAAC;QAE9C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,kKAAK,CAAC,eAAe,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,+DAA+D;QAC/D,IAAI,CAAC,YAAY,GAAG,yLAAI,sBAAmB,CAAC;YAAE,eAAe,EAAE,WAAW;QAAA,CAAE,CAAC,CAAC;QAC9E,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC;QACjE,IAAI,CAAC,YAAY,CAAC,yCAAyC,GAAG,IAAI,CAAC;QACnE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9C,0BAA0B;QAC1B,MAAM,gBAAgB,GAAG,sKAAI,UAAO,EAAE,CAAC;QAEvC,MAAM,cAAc,GAAG,sKAAI,SAAM,EAAE,CAAC;QACpC,MAAM,wBAAwB,GAAG,sKAAI,UAAO,EAAE,CAAC;QAC/C,IAAI,6BAA6B,GAAG,sKAAI,UAAO,EAAE,CAAC;QAElD,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YAC9C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;gBAC5C,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAE5C,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;kLACxE,UAAO,CAAC,yBAAyB,CAAC,CAAC,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;gBAEtF,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;gBAC9E,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;gBACnB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;gBAC5C,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YACnB,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC3C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG;YAAE,YAAY,EAAE,CAAC;QAAA,CAAE,CAAC;QACzC,IAAI,uBAAuB,GAAG,CAAC,CAAC;QAChC,MAAM,SAAS,GAAG,sKAAI,SAAM,EAAE,CAAC;QAC/B,MAAM,cAAc,GAAG,qKAAI,cAAU,EAAE,CAAC;QACxC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,4IAA4I;gBAC5I,MAAM,SAAS,GAAG,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvC,MAAM,cAAc,GAAG,IAAI,+KAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAClD,MAAM,eAAe,GAAG,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7C,MAAM,qBAAqB,GAAG,IAAI,CAAC,aAA8B,CAAC;gBAClE,gEAAgE;gBAChE,IAAI,qBAAqB,IAAI,qBAAqB,CAAC,kBAAkB,IAAI,qBAAqB,CAAC,kBAAkB,EAAE,IAAI,qBAAqB,CAAC,QAAQ,EAAE,CAAC;oBACpJ,oEAAoE;oBACpE,8EAA8E;oBAC9E,uEAAuE;oBACvE,6EAA6E;oBAC7E,2DAA2D;oBAC3D,qBAAqB,CAAC,cAAc,EAAE,CAAC,cAAc,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;gBAC1F,CAAC;gBAED,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;gBACzF,kDAAkD;gBAClD,6CAA6C;gBAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,yKAAI,UAAO,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,yKAAI,UAAO,CAAC;gBAChK,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,sCAAsC,EAAE,CAAC;6KACjE,SAAM,CAAC,IAAI,CACP,6JAA6J,CAChK,CAAC;oBACF,OAAO;gBACX,CAAC;gBACD,cAAc,CAAC,SAAS,EAAE,CAAC;gBAE3B,MAAM,2BAA2B,GAAG,IAAI,CAAC,sCAAsC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;gBACpI,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC,SAAS,EAAE,CAAC;gBACzF,MAAM,cAAc,GAAG,gBAAgB,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC,SAAS,EAAE,CAAC;gBAC1F,MAAM,KAAK,qKAAG,UAAO,CAAC,KAAK,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;gBACvD,MAAM,GAAG,qKAAG,UAAO,CAAC,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;gBACnD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;gBAC/D,wBAAwB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAC/C,6BAA6B,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACpD,IAAI,IAAI,CAAC,sCAAsC,EAAE,CAAC;oBAC9C,cAAc,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;oBAChD,6BAA6B,oKAAG,WAAO,CAAC,oBAAoB,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC;gBAC3G,CAAC;gBACD,0DAA0D;gBAC1D,IAAI,aAAa,GAAG,KAAK,CAAC;gBAC1B,IAAI,UAAU,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;oBAC5C,MAAM,MAAM,GAAG,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC,SAAS,EAAE,CAAC;oBACpH,sKAAI,UAAO,CAAC,GAAG,CAAC,MAAM,EAAE,6BAA6B,CAAC,GAAG,CAAC,EAAE,CAAC;wBACzD,wBAAwB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1C,6BAA6B,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/C,aAAa,GAAG,IAAI,CAAC;oBACzB,CAAC;gBACL,CAAC;gBACD,MAAM,cAAc,GAAG,4KAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC;gBAC/E,IAAI,cAAc,EAAE,CAAC;oBACjB,KAAK,GAAG,CAAC,KAAK,CAAC;gBACnB,CAAC;kLAED,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACzD,KAAK,GAAG,CAAC,CAAC;gBACd,CAAC;gBAED,iBAAiB;gBACjB,IAAI,OAAO,GAAG,KAAK,CAAC;gBACpB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;oBACzB,uBAAuB,IAAI,KAAK,CAAC;oBACjC,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBACxD,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;wBAClF,IAAI,uBAAuB,GAAG,CAAC,EAAE,CAAC;4BAC9B,SAAS,IAAI,CAAC,CAAC,CAAC;wBACpB,CAAC;wBACD,uBAAuB,GAAG,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC;wBACtE,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;wBACtC,OAAO,GAAG,IAAI,CAAC;oBACnB,CAAC,MAAM,CAAC;wBACJ,KAAK,GAAG,CAAC,CAAC;oBACd,CAAC;gBACL,CAAC;gBAED,wIAAwI;gBACxI,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAClD,cAAc,CAAC,GAAG,CACd,wBAAwB,CAAC,CAAC,GAAG,qBAAqB,EAClD,wBAAwB,CAAC,CAAC,GAAG,qBAAqB,EAClD,wBAAwB,CAAC,CAAC,GAAG,qBAAqB,EAClD,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CACtB,CAAC;gBAEF,yIAAyI;gBACzI,IAAI,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC;oBAC9B,MAAM,SAAS,GAAG,sKAAI,UAAO,EAAE,CAAC;oBAChC,cAAc,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;qLAC7C,cAAU,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;gBAClG,CAAC;gBAED,IAAI,IAAI,CAAC,sCAAsC,EAAE,CAAC;oBAC9C,kDAAkD;oBAClD,cAAc,CAAC,aAAa,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;oBAC7D,cAAc,CAAC,SAAS,EAAE,CAAC;oBAC3B,mBAAmB;sLACnB,SAAM,CAAC,YAAY,CAAC,SAAS,EAAE,cAAc,EAAE,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC;gBACxG,CAAC,MAAM,CAAC;oBACJ,oDAAoD;oBACpD,cAAc,CAAC,gBAAgB,mKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,cAAc,EAAE,CAAC;oBACxE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,aAAa,CAAC,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC;oBAC3G,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;gBACnE,CAAC;gBAED,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAChD,IAAI,OAAO,EAAE,CAAC;oBACV,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;oBAClC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;gBACxD,CAAC;gBACD,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,UAAU,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;gBACrF,IAAI,CAAC,KAAK,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1B,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAChD,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QAEjG,MAAM,KAAK,GAAmB;YAC1B,cAAc,EAAE;gBAAC,QAAQ;aAAC;YAC1B,WAAW,EAAE;gBAAC,YAAY;aAAC;YAC3B,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,IAAI,CAAC,YAAY;SAClC,CAAC;6BACE,CAAC,OAAO,gEAAE,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QAErD,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;gBAMjC,WAAW;YALnE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,OAAO;YACX,CAAC;YACD,kIAAkI;YAClI,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC;YACjE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,gGAAoB,QAAQ,gFAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAE,AAAD,IAAK,CAAC,gBAAgB,CAAC;gBACxJ,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5G,CAAC,CAAC,CAAC;IACP,CAAC;;AA5WD;;;GAGG,CACW,mBAAA,YAAY,GAAW,AAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAG,EAAE,AAA7B,CAA8B;AA0CvC,mBAAA,0BAA0B,GAAG;AAa7B,mBAAA,4BAA4B,GAAG", "debugId": null}}, {"offset": {"line": 2390, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/rotationGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/rotationGizmo.ts"], "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Quaternion } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { GizmoAnchorPoint, GizmoCoordinatesMode, GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport type { IPlaneRotationGizmo } from \"./planeRotationGizmo\";\r\nimport { PlaneRotationGizmo } from \"./planeRotationGizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport type { Node } from \"../node\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { GizmoManager } from \"./gizmoManager\";\r\n\r\n/**\r\n * Interface for rotation gizmo\r\n */\r\nexport interface IRotationGizmo extends IGizmo {\r\n    /** True when the mouse pointer is dragging a gizmo mesh */\r\n    readonly isDragging: boolean;\r\n    /** Internal gizmo used for interactions on the x axis */\r\n    xGizmo: IPlaneRotationGizmo;\r\n    /** Internal gizmo used for interactions on the y axis */\r\n    yGizmo: IPlaneRotationGizmo;\r\n    /** Internal gizmo used for interactions on the z axis */\r\n    zGizmo: IPlaneRotationGizmo;\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    onDragStartObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    onDragObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    onDragEndObservable: Observable<unknown>;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /** Custom sensitivity value for the drag strength */\r\n    sensitivity: number;\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    addToAxisCache(mesh: Mesh, cache: GizmoAxisCache): void;\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    releaseDrag(): void;\r\n}\r\n\r\n/**\r\n * Options for each individual plane rotation gizmo contained within RotationGizmo\r\n * @since 5.0.0\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface PlaneRotationGizmoOptions {\r\n    /**\r\n     * Color to use for the plane rotation gizmo\r\n     */\r\n    color?: Color3;\r\n}\r\n\r\n/**\r\n * Additional options for each rotation gizmo\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface RotationGizmoOptions {\r\n    /**\r\n     * When set, the gizmo will always appear the same size no matter where the camera is (default: true)\r\n     */\r\n    updateScale?: boolean;\r\n\r\n    /**\r\n     * Specific options for xGizmo\r\n     */\r\n    xOptions?: PlaneRotationGizmoOptions;\r\n\r\n    /**\r\n     * Specific options for yGizmo\r\n     */\r\n    yOptions?: PlaneRotationGizmoOptions;\r\n\r\n    /**\r\n     * Specific options for zGizmo\r\n     */\r\n    zOptions?: PlaneRotationGizmoOptions;\r\n\r\n    /**\r\n     * Additional transform applied to the gizmo.\r\n     * @See Gizmo.additionalTransformNode for more detail\r\n     */\r\n    additionalTransformNode?: TransformNode;\r\n}\r\n\r\n/**\r\n * Gizmo that enables rotating a mesh along 3 axis\r\n */\r\nexport class RotationGizmo extends Gizmo implements IRotationGizmo {\r\n    /**\r\n     * Internal gizmo used for interactions on the x axis\r\n     */\r\n    public xGizmo: IPlaneRotationGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the y axis\r\n     */\r\n    public yGizmo: IPlaneRotationGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the z axis\r\n     */\r\n    public zGizmo: IPlaneRotationGizmo;\r\n\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    public onDragStartObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    public onDragObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    public onDragEndObservable = new Observable();\r\n\r\n    protected _meshAttached: Nullable<AbstractMesh>;\r\n    protected _nodeAttached: Nullable<Node>;\r\n    protected _observables: Observer<PointerInfo>[] = [];\r\n    protected _sensitivity: number = 1;\r\n\r\n    /** Node Caching for quick lookup */\r\n    protected _gizmoAxisCache: Map<Mesh, GizmoAxisCache> = new Map();\r\n\r\n    public override get attachedMesh() {\r\n        return this._meshAttached;\r\n    }\r\n    public override set attachedMesh(mesh: Nullable<AbstractMesh>) {\r\n        this._meshAttached = mesh;\r\n        this._nodeAttached = mesh;\r\n        this._checkBillboardTransform();\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedMesh = mesh;\r\n            } else {\r\n                gizmo.attachedMesh = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    public override get attachedNode() {\r\n        return this._nodeAttached;\r\n    }\r\n    public override set attachedNode(node: Nullable<Node>) {\r\n        this._meshAttached = null;\r\n        this._nodeAttached = node;\r\n        this._checkBillboardTransform();\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedNode = node;\r\n            } else {\r\n                gizmo.attachedNode = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _checkBillboardTransform() {\r\n        if (this._nodeAttached && (<TransformNode>this._nodeAttached).billboardMode) {\r\n            Logger.Log(\"Rotation Gizmo will not work with transforms in billboard mode.\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sensitivity factor for dragging (Default: 1)\r\n     */\r\n    public set sensitivity(value: number) {\r\n        this._sensitivity = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.sensitivity = value;\r\n            }\r\n        }\r\n    }\r\n    public get sensitivity() {\r\n        return this._sensitivity;\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is hovering a gizmo mesh\r\n     */\r\n    public override get isHovered() {\r\n        return this.xGizmo.isHovered || this.yGizmo.isHovered || this.zGizmo.isHovered;\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is dragging a gizmo mesh\r\n     */\r\n    public get isDragging() {\r\n        return this.xGizmo.dragBehavior.dragging || this.yGizmo.dragBehavior.dragging || this.zGizmo.dragBehavior.dragging;\r\n    }\r\n\r\n    public override get additionalTransformNode() {\r\n        return this._additionalTransformNode;\r\n    }\r\n\r\n    public override set additionalTransformNode(transformNode: TransformNode | undefined) {\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.additionalTransformNode = transformNode;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a RotationGizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param tessellation Amount of tessellation to be used when creating rotation circles\r\n     * @param useEulerRotation Use and update Euler angle instead of quaternion\r\n     * @param thickness display gizmo axis thickness\r\n     * @param gizmoManager Gizmo manager\r\n     * @param options More options\r\n     */\r\n    constructor(\r\n        gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer,\r\n        tessellation = 32,\r\n        useEulerRotation = false,\r\n        thickness: number = 1,\r\n        gizmoManager?: GizmoManager,\r\n        options?: RotationGizmoOptions\r\n    ) {\r\n        super(gizmoLayer);\r\n        const xColor = options && options.xOptions && options.xOptions.color ? options.xOptions.color : Color3.Red().scale(0.5);\r\n        const yColor = options && options.yOptions && options.yOptions.color ? options.yOptions.color : Color3.Green().scale(0.5);\r\n        const zColor = options && options.zOptions && options.zOptions.color ? options.zOptions.color : Color3.Blue().scale(0.5);\r\n        this.xGizmo = new PlaneRotationGizmo(new Vector3(1, 0, 0), xColor, gizmoLayer, tessellation, this, useEulerRotation, thickness);\r\n        this.yGizmo = new PlaneRotationGizmo(new Vector3(0, 1, 0), yColor, gizmoLayer, tessellation, this, useEulerRotation, thickness);\r\n        this.zGizmo = new PlaneRotationGizmo(new Vector3(0, 0, 1), zColor, gizmoLayer, tessellation, this, useEulerRotation, thickness);\r\n\r\n        this.additionalTransformNode = options?.additionalTransformNode;\r\n\r\n        // Relay drag events and set update scale\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo];\r\n        for (const gizmo of gizmos) {\r\n            //must set updateScale on each gizmo, as setting it on root RotationGizmo doesnt prevent individual gizmos from updating\r\n            //currently updateScale is a property with no getter/setter, so no good way to override behavior at runtime, so we will at least set it on startup\r\n            if (options && options.updateScale != undefined) {\r\n                gizmo.updateScale = options.updateScale;\r\n            }\r\n            gizmo.dragBehavior.onDragStartObservable.add(() => {\r\n                this.onDragStartObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragObservable.add(() => {\r\n                this.onDragObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragEndObservable.add(() => {\r\n                this.onDragEndObservable.notifyObservers({});\r\n            });\r\n        }\r\n\r\n        this.attachedMesh = null;\r\n        this.attachedNode = null;\r\n\r\n        if (gizmoManager) {\r\n            gizmoManager.addToAxisCache(this._gizmoAxisCache);\r\n        } else {\r\n            // Only subscribe to pointer event if gizmoManager isnt\r\n            Gizmo.GizmoAxisPointerObserver(gizmoLayer, this._gizmoAxisCache);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If set the gizmo's rotation will be updated to match the attached mesh each frame (Default: true)\r\n     * NOTE: This is only possible for meshes with uniform scaling, as otherwise it's not possible to decompose the rotation\r\n     */\r\n    public override set updateGizmoRotationToMatchAttachedMesh(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n            this.yGizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n            this.zGizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n        }\r\n    }\r\n    public override get updateGizmoRotationToMatchAttachedMesh() {\r\n        return this.xGizmo.updateGizmoRotationToMatchAttachedMesh;\r\n    }\r\n\r\n    public override set updateGizmoPositionToMatchAttachedMesh(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateGizmoPositionToMatchAttachedMesh = value;\r\n            this.yGizmo.updateGizmoPositionToMatchAttachedMesh = value;\r\n            this.zGizmo.updateGizmoPositionToMatchAttachedMesh = value;\r\n        }\r\n    }\r\n    public override get updateGizmoPositionToMatchAttachedMesh() {\r\n        return this.xGizmo.updateGizmoPositionToMatchAttachedMesh;\r\n    }\r\n\r\n    public override set anchorPoint(value: GizmoAnchorPoint) {\r\n        this._anchorPoint = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.anchorPoint = value;\r\n        }\r\n    }\r\n    public override get anchorPoint() {\r\n        return this._anchorPoint;\r\n    }\r\n\r\n    /**\r\n     * Set the coordinate system to use. By default it's local.\r\n     * But it's possible for a user to tweak so its local for translation and world for rotation.\r\n     * In that case, setting the coordinate system will change `updateGizmoRotationToMatchAttachedMesh` and `updateGizmoPositionToMatchAttachedMesh`\r\n     */\r\n    public override set coordinatesMode(coordinatesMode: GizmoCoordinatesMode) {\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.coordinatesMode = coordinatesMode;\r\n        }\r\n    }\r\n\r\n    public override set updateScale(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateScale = value;\r\n            this.yGizmo.updateScale = value;\r\n            this.zGizmo.updateScale = value;\r\n        }\r\n    }\r\n    public override get updateScale() {\r\n        return this.xGizmo.updateScale;\r\n    }\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public set snapDistance(value: number) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.snapDistance = value;\r\n            this.yGizmo.snapDistance = value;\r\n            this.zGizmo.snapDistance = value;\r\n        }\r\n    }\r\n    public get snapDistance() {\r\n        return this.xGizmo.snapDistance;\r\n    }\r\n\r\n    /**\r\n     * Ratio for the scale of the gizmo (Default: 1)\r\n     */\r\n    public override set scaleRatio(value: number) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.scaleRatio = value;\r\n            this.yGizmo.scaleRatio = value;\r\n            this.zGizmo.scaleRatio = value;\r\n        }\r\n    }\r\n    public override get scaleRatio() {\r\n        return this.xGizmo.scaleRatio;\r\n    }\r\n\r\n    /**\r\n     * posture that the gizmo will be display\r\n     * When set null, default value will be used (Quaternion(0, 0, 0, 1))\r\n     */\r\n    public override get customRotationQuaternion(): Nullable<Quaternion> {\r\n        return this._customRotationQuaternion;\r\n    }\r\n\r\n    public override set customRotationQuaternion(customRotationQuaternion: Nullable<Quaternion>) {\r\n        this._customRotationQuaternion = customRotationQuaternion;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.customRotationQuaternion = customRotationQuaternion;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    public addToAxisCache(mesh: Mesh, cache: GizmoAxisCache) {\r\n        this._gizmoAxisCache.set(mesh, cache);\r\n    }\r\n\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    public releaseDrag() {\r\n        this.xGizmo.dragBehavior.releaseDrag();\r\n        this.yGizmo.dragBehavior.releaseDrag();\r\n        this.zGizmo.dragBehavior.releaseDrag();\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public override dispose() {\r\n        this.xGizmo.dispose();\r\n        this.yGizmo.dispose();\r\n        this.zGizmo.dispose();\r\n        this.onDragStartObservable.clear();\r\n        this.onDragObservable.clear();\r\n        this.onDragEndObservable.clear();\r\n        for (const obs of this._observables) {\r\n            this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(obs);\r\n        }\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * CustomMeshes are not supported by this gizmo\r\n     */\r\n    public override setCustomMesh() {\r\n        Logger.Error(\r\n            \"Custom meshes are not supported on this gizmo, please set the custom meshes on the gizmos contained within this one (gizmo.xGizmo, gizmo.yGizmo, gizmo.zGizmo)\"\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAI7C,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;;;;;;;;AAuFnE,MAAO,aAAc,mKAAQ,QAAK;IA6BpC,IAAoB,YAAY,GAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAoB,YAAY,CAAC,IAA4B,EAAA;QACzD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;SAAC,CAAC;QACvD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBAClB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAoB,YAAY,GAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAoB,YAAY,CAAC,IAAoB,EAAA;QACjD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;SAAC,CAAC;QACvD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBAClB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IAES,wBAAwB,GAAA;QAC9B,IAAI,IAAI,CAAC,aAAa,IAAoB,IAAI,CAAC,aAAc,CAAC,aAAa,EAAE,CAAC;oKAC1E,UAAM,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;QAClF,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;SAAC,CAAC;QACvD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAoB,SAAS,GAAA;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IACnF,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;IACvH,CAAC;IAED,IAAoB,uBAAuB,GAAA;QACvC,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAoB,uBAAuB,CAAC,aAAwC,EAAA;QAChF,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;SAAC,CAAC;QACvD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,uBAAuB,GAAG,aAAa,CAAC;QAClD,CAAC;IACL,CAAC;IA2DD;;;OAGG,CACH,IAAoB,sCAAsC,CAAC,KAAc,EAAA;QACrE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;QAC/D,CAAC;IACL,CAAC;IACD,IAAoB,sCAAsC,GAAA;QACtD,OAAO,IAAI,CAAC,MAAM,CAAC,sCAAsC,CAAC;IAC9D,CAAC;IAED,IAAoB,sCAAsC,CAAC,KAAc,EAAA;QACrE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,sCAAsC,GAAG,KAAK,CAAC;QAC/D,CAAC;IACL,CAAC;IACD,IAAoB,sCAAsC,GAAA;QACtD,OAAO,IAAI,CAAC,MAAM,CAAC,sCAAsC,CAAC;IAC9D,CAAC;IAED,IAAoB,WAAW,CAAC,KAAuB,EAAA;QACnD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;SAAC,CAAC;QACvD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QAC9B,CAAC;IACL,CAAC;IACD,IAAoB,WAAW,GAAA;QAC3B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG,CACH,IAAoB,eAAe,CAAC,eAAqC,EAAA;QACrE,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;SAAC,CAAC;QACvD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;QAC5C,CAAC;IACL,CAAC;IAED,IAAoB,WAAW,CAAC,KAAc,EAAA;QAC1C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;QACpC,CAAC;IACL,CAAC;IACD,IAAoB,WAAW,GAAA;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACnC,CAAC;IACD;;OAEG,CACH,IAAW,YAAY,CAAC,KAAa,EAAA;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC;QACrC,CAAC;IACL,CAAC;IACD,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAoB,UAAU,CAAC,KAAa,EAAA;QACxC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;QACnC,CAAC;IACL,CAAC;IACD,IAAoB,UAAU,GAAA;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;IAClC,CAAC;IAED;;;OAGG,CACH,IAAoB,wBAAwB,GAAA;QACxC,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED,IAAoB,wBAAwB,CAAC,wBAA8C,EAAA;QACvF,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;QAC1D,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;SAAC,CAAC;QACvD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;YAC9D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,IAAU,EAAE,KAAqB,EAAA;QACnD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG,CACI,WAAW,GAAA;QACd,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAE,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACtE,CAAC;QACD,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;OAEG,CACa,aAAa,GAAA;iKACzB,SAAM,CAAC,KAAK,CACR,gKAAgK,CACnK,CAAC;IACN,CAAC;IA3MD;;;;;;;;OAQG,CACH,YACI,yLAAmC,uBAAoB,CAAC,mBAAmB,EAC3E,YAAY,GAAG,EAAE,EACjB,gBAAgB,GAAG,KAAK,EACxB,YAAoB,CAAC,EACrB,YAA2B,EAC3B,OAA8B,CAAA;QAE9B,KAAK,CAAC,UAAU,CAAC,CAAC;QAjHtB,2DAAA,EAA6D,CACtD,IAAA,CAAA,qBAAqB,GAAG,IAAI,0KAAU,EAAE,CAAC;QAChD,iEAAA,EAAmE,CAC5D,IAAA,CAAA,gBAAgB,GAAG,iKAAI,aAAU,EAAE,CAAC;QAC3C,0EAAA,EAA4E,CACrE,IAAA,CAAA,mBAAmB,GAAG,iKAAI,aAAU,EAAE,CAAC;QAIpC,IAAA,CAAA,YAAY,GAA4B,EAAE,CAAC;QAC3C,IAAA,CAAA,YAAY,GAAW,CAAC,CAAC;QAEnC,kCAAA,EAAoC,CAC1B,IAAA,CAAA,eAAe,GAA8B,IAAI,GAAG,EAAE,CAAC;QAqG7D,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,kKAAC,SAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxH,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,kKAAC,SAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1H,MAAM,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,kKAAC,SAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzH,IAAI,CAAC,MAAM,GAAG,2KAAI,qBAAkB,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAChI,IAAI,CAAC,MAAM,GAAG,IAAI,4LAAkB,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAChI,IAAI,CAAC,MAAM,GAAG,2KAAI,qBAAkB,CAAC,qKAAI,WAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;QAEhI,IAAI,CAAC,uBAAuB,qDAAG,OAAO,CAAE,uBAAuB,CAAC;QAEhE,yCAAyC;QACzC,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;SAAC,CAAC;QACvD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,wHAAwH;YACxH,kJAAkJ;YAClJ,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,IAAI,SAAS,EAAE,CAAC;gBAC9C,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;YAC5C,CAAC;YACD,KAAK,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,YAAY,EAAE,CAAC;YACf,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC,MAAM,CAAC;YACJ,uDAAuD;qKACvD,SAAK,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;CAqJJ", "debugId": null}}, {"offset": {"line": 2690, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/planeDragGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/planeDragGizmo.ts"], "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Node } from \"../node\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { CreatePlane } from \"../Meshes/Builders/planeBuilder\";\r\nimport { PointerDragBehavior } from \"../Behaviors/Meshes/pointerDragBehavior\";\r\nimport type { GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { PositionGizmo } from \"./positionGizmo\";\r\n\r\n/**\r\n * Interface for plane drag gizmo\r\n */\r\nexport interface IPlaneDragGizmo extends IGizmo {\r\n    /** Drag behavior responsible for the gizmos dragging interactions */\r\n    dragBehavior: PointerDragBehavior;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    onSnapObservable: Observable<{ snapDistance: number }>;\r\n    /** If the gizmo is enabled */\r\n    isEnabled: boolean;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    coloredMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is hovered with mouse */\r\n    hoverMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is disabled. typically grey. */\r\n    disableMaterial: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Single plane drag gizmo\r\n */\r\nexport class PlaneDragGizmo extends Gizmo implements IPlaneDragGizmo {\r\n    /**\r\n     * Drag behavior responsible for the gizmos dragging interactions\r\n     */\r\n    public dragBehavior: PointerDragBehavior;\r\n    protected _pointerObserver: Nullable<Observer<PointerInfo>> = null;\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public snapDistance = 0;\r\n    /**\r\n     * Event that fires each time the gizmo snaps to a new location.\r\n     * * snapDistance is the change in distance\r\n     */\r\n    public onSnapObservable = new Observable<{ snapDistance: number }>();\r\n\r\n    protected _gizmoMesh: TransformNode;\r\n    protected _coloredMaterial: StandardMaterial;\r\n    protected _hoverMaterial: StandardMaterial;\r\n    protected _disableMaterial: StandardMaterial;\r\n\r\n    protected _isEnabled: boolean = false;\r\n    protected _parent: Nullable<PositionGizmo> = null;\r\n    protected _dragging: boolean = false;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    public get coloredMaterial() {\r\n        return this._coloredMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    public get hoverMaterial() {\r\n        return this._hoverMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    public get disableMaterial() {\r\n        return this._disableMaterial;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _CreatePlane(scene: Scene, material: StandardMaterial): TransformNode {\r\n        const plane = new TransformNode(\"plane\", scene);\r\n\r\n        //make sure plane is double sided\r\n        const dragPlane = CreatePlane(\"dragPlane\", { width: 0.1375, height: 0.1375, sideOrientation: 2 }, scene);\r\n        dragPlane.material = material;\r\n        dragPlane.parent = plane;\r\n        return plane;\r\n    }\r\n\r\n    /**\r\n     * Creates a PlaneDragGizmo\r\n     * @param dragPlaneNormal The axis normal to which the gizmo will be able to drag on\r\n     * @param color The color of the gizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param parent\r\n     * @param hoverColor The color of the gizmo when hovering over and dragging\r\n     * @param disableColor The Color of the gizmo when its disabled\r\n     */\r\n    constructor(\r\n        dragPlaneNormal: Vector3,\r\n        color: Color3 = Color3.Gray(),\r\n        gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer,\r\n        parent: Nullable<PositionGizmo> = null,\r\n        hoverColor: Color3 = Color3.Yellow(),\r\n        disableColor: Color3 = Color3.Gray()\r\n    ) {\r\n        super(gizmoLayer);\r\n        this._parent = parent;\r\n        // Create Material\r\n        this._coloredMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._coloredMaterial.diffuseColor = color;\r\n        this._coloredMaterial.specularColor = color.subtract(new Color3(0.1, 0.1, 0.1));\r\n\r\n        this._hoverMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._hoverMaterial.diffuseColor = hoverColor;\r\n\r\n        this._disableMaterial = new StandardMaterial(\"\", gizmoLayer.utilityLayerScene);\r\n        this._disableMaterial.diffuseColor = disableColor;\r\n        this._disableMaterial.alpha = 0.4;\r\n\r\n        // Build plane mesh on root node\r\n        this._gizmoMesh = PlaneDragGizmo._CreatePlane(gizmoLayer.utilityLayerScene, this._coloredMaterial);\r\n\r\n        this._gizmoMesh.lookAt(this._rootMesh.position.add(dragPlaneNormal));\r\n        this._gizmoMesh.scaling.scaleInPlace(1 / 3);\r\n        this._gizmoMesh.parent = this._rootMesh;\r\n\r\n        let currentSnapDragDistance = 0;\r\n        const tmpVector = new Vector3();\r\n        const tmpSnapEvent = { snapDistance: 0 };\r\n        // Add dragPlaneNormal drag behavior to handle events when the gizmo is dragged\r\n        this.dragBehavior = new PointerDragBehavior({ dragPlaneNormal: dragPlaneNormal });\r\n        this.dragBehavior.moveAttached = false;\r\n        this._rootMesh.addBehavior(this.dragBehavior);\r\n\r\n        this.dragBehavior.onDragObservable.add((event) => {\r\n            if (this.attachedNode) {\r\n                // Keep world translation and use it to update world transform\r\n                // if the node has parent, the local transform properties (position, rotation, scale)\r\n                // will be recomputed in _matrixChanged function\r\n\r\n                // Snapping logic\r\n                if (this.snapDistance == 0) {\r\n                    this.attachedNode.getWorldMatrix().getTranslationToRef(TmpVectors.Vector3[0]);\r\n                    TmpVectors.Vector3[0].addToRef(event.delta, TmpVectors.Vector3[0]);\r\n                    if (this.dragBehavior.validateDrag(TmpVectors.Vector3[0])) {\r\n                        this.attachedNode.getWorldMatrix().addTranslationFromFloats(event.delta.x, event.delta.y, event.delta.z);\r\n                    }\r\n                } else {\r\n                    currentSnapDragDistance += event.dragDistance;\r\n                    if (Math.abs(currentSnapDragDistance) > this.snapDistance) {\r\n                        const dragSteps = Math.floor(Math.abs(currentSnapDragDistance) / this.snapDistance);\r\n                        currentSnapDragDistance = currentSnapDragDistance % this.snapDistance;\r\n                        event.delta.normalizeToRef(tmpVector);\r\n                        tmpVector.scaleInPlace(this.snapDistance * dragSteps);\r\n                        this.attachedNode.getWorldMatrix().getTranslationToRef(TmpVectors.Vector3[0]);\r\n                        TmpVectors.Vector3[0].addToRef(tmpVector, TmpVectors.Vector3[0]);\r\n                        if (this.dragBehavior.validateDrag(TmpVectors.Vector3[0])) {\r\n                            this.attachedNode.getWorldMatrix().addTranslationFromFloats(tmpVector.x, tmpVector.y, tmpVector.z);\r\n                            tmpSnapEvent.snapDistance = this.snapDistance * dragSteps;\r\n                            this.onSnapObservable.notifyObservers(tmpSnapEvent);\r\n                        }\r\n                    }\r\n                }\r\n                this._matrixChanged();\r\n            }\r\n        });\r\n        this.dragBehavior.onDragStartObservable.add(() => {\r\n            this._dragging = true;\r\n        });\r\n        this.dragBehavior.onDragEndObservable.add(() => {\r\n            this._dragging = false;\r\n        });\r\n\r\n        const light = gizmoLayer._getSharedGizmoLight();\r\n        light.includedOnlyMeshes = light.includedOnlyMeshes.concat(this._rootMesh.getChildMeshes(false));\r\n\r\n        const cache: GizmoAxisCache = {\r\n            gizmoMeshes: this._gizmoMesh.getChildMeshes(),\r\n            colliderMeshes: this._gizmoMesh.getChildMeshes(),\r\n            material: this._coloredMaterial,\r\n            hoverMaterial: this._hoverMaterial,\r\n            disableMaterial: this._disableMaterial,\r\n            active: false,\r\n            dragBehavior: this.dragBehavior,\r\n        };\r\n        this._parent?.addToAxisCache(this._gizmoMesh as Mesh, cache);\r\n\r\n        this._pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (this._customMeshSet) {\r\n                return;\r\n            }\r\n            this._isHovered = !!(cache.colliderMeshes.indexOf(<Mesh>pointerInfo?.pickInfo?.pickedMesh) != -1);\r\n            if (!this._parent) {\r\n                const material = cache.dragBehavior.enabled ? (this._isHovered || this._dragging ? this._hoverMaterial : this._coloredMaterial) : this._disableMaterial;\r\n                this._setGizmoMeshMaterial(cache.gizmoMeshes, material);\r\n            }\r\n        });\r\n\r\n        this.dragBehavior.onEnabledObservable.add((newState) => {\r\n            this._setGizmoMeshMaterial(cache.gizmoMeshes, newState ? this._coloredMaterial : this._disableMaterial);\r\n        });\r\n    }\r\n\r\n    protected override _attachedNodeChanged(value: Nullable<Node>) {\r\n        if (this.dragBehavior) {\r\n            this.dragBehavior.enabled = value ? true : false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the gizmo is enabled\r\n     */\r\n    public set isEnabled(value: boolean) {\r\n        this._isEnabled = value;\r\n        if (!value) {\r\n            this.attachedNode = null;\r\n        } else {\r\n            if (this._parent) {\r\n                this.attachedNode = this._parent.attachedNode;\r\n            }\r\n        }\r\n    }\r\n\r\n    public get isEnabled(): boolean {\r\n        return this._isEnabled;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public override dispose() {\r\n        this.onSnapObservable.clear();\r\n        this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(this._pointerObserver);\r\n        this.dragBehavior.detach();\r\n        super.dispose();\r\n        if (this._gizmoMesh) {\r\n            this._gizmoMesh.dispose();\r\n        }\r\n        const materials = [this._coloredMaterial, this._hoverMaterial, this._disableMaterial];\r\n        for (const matl of materials) {\r\n            if (matl) {\r\n                matl.dispose();\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAGxD,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAE9E,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;;;;;;;;;;AA+B3D,MAAO,cAAe,SAAQ,kKAAK;IAyBrC,0EAAA,EAA4E,CAC5E,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,4DAAA,EAA8D,CAC9D,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,mEAAA,EAAqE,CACrE,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG,CACI,MAAM,CAAC,YAAY,CAAC,KAAY,EAAE,QAA0B,EAAA;QAC/D,MAAM,KAAK,GAAG,sKAAI,gBAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEhD,iCAAiC;QACjC,MAAM,SAAS,GAAG,+LAAA,AAAW,EAAC,WAAW,EAAE;YAAE,KAAK,EAAE,MAAM;YAAE,MAAM,EAAE,MAAM;YAAE,eAAe,EAAE,CAAC;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QACzG,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC9B,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;QACzB,OAAO,KAAK,CAAC;IACjB,CAAC;IAqHkB,oBAAoB,CAAC,KAAqB,EAAA;QACzD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACrD,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,CAAC,KAAc,EAAA;QAC/B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;YAClD,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAC3B,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QACD,MAAM,SAAS,GAAG;YAAC,IAAI,CAAC,gBAAgB;YAAE,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,gBAAgB;SAAC,CAAC;QACtF,KAAK,MAAM,IAAI,IAAI,SAAS,CAAE,CAAC;YAC3B,IAAI,IAAI,EAAE,CAAC;gBACP,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACL,CAAC;IACL,CAAC;IA5JD;;;;;;;;OAQG,CACH,YACI,eAAwB,EACxB,yKAAgB,SAAM,CAAC,IAAI,EAAE,EAC7B,yLAAmC,uBAAoB,CAAC,mBAAmB,EAC3E,SAAkC,IAAI,EACtC,8KAAqB,SAAM,CAAC,MAAM,EAAE,EACpC,eAAuB,0KAAM,CAAC,IAAI,EAAE,CAAA;;QAEpC,KAAK,CAAC,UAAU,CAAC,CAAC;QAjEZ,IAAA,CAAA,gBAAgB,GAAoC,IAAI,CAAC;QACnE;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACxB;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAAG,iKAAI,aAAU,EAA4B,CAAC;QAO3D,IAAA,CAAA,UAAU,GAAY,KAAK,CAAC;QAC5B,IAAA,CAAA,OAAO,GAA4B,IAAI,CAAC;QACxC,IAAA,CAAA,SAAS,GAAY,KAAK,CAAC;QAgDjC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,kBAAkB;QAClB,IAAI,CAAC,gBAAgB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAEhF,IAAI,CAAC,cAAc,GAAG,IAAI,2LAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC7E,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,UAAU,CAAC;QAE9C,IAAI,CAAC,gBAAgB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAC/E,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,YAAY,CAAC;QAClD,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,GAAG,CAAC;QAElC,gCAAgC;QAChC,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEnG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAExC,IAAI,uBAAuB,GAAG,CAAC,CAAC;QAChC,MAAM,SAAS,GAAG,IAAI,4KAAO,EAAE,CAAC;QAChC,MAAM,YAAY,GAAG;YAAE,YAAY,EAAE,CAAC;QAAA,CAAE,CAAC;QACzC,+EAA+E;QAC/E,IAAI,CAAC,YAAY,GAAG,yLAAI,sBAAmB,CAAC;YAAE,eAAe,EAAE,eAAe;QAAA,CAAE,CAAC,CAAC;QAClF,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9C,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,8DAA8D;gBAC9D,qFAAqF;gBACrF,gDAAgD;gBAEhD,iBAAiB;gBACjB,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC;oBACzB,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,mBAAmB,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;sLAC9E,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnE,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACxD,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,wBAAwB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC7G,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,uBAAuB,IAAI,KAAK,CAAC,YAAY,CAAC;oBAC9C,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBACxD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;wBACpF,uBAAuB,GAAG,uBAAuB,GAAG,IAAI,CAAC,YAAY,CAAC;wBACtE,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;wBACtC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,CAAC;wBACtD,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,mBAAmB,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;0LAC9E,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjE,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;4BACxD,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;4BACnG,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;4BAC1D,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;wBACxD,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1B,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC3C,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAChD,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QAEjG,MAAM,KAAK,GAAmB;YAC1B,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;YAC7C,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;YAChD,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,IAAI,CAAC,YAAY;SAClC,CAAC;6BACE,CAAC,OAAO,kDAAZ,cAAc,cAAc,CAAC,IAAI,CAAC,UAAkB,EAAE,KAAK,CAAC,CAAC;QAE7D,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;;YACzF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,OAAO;YACX,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,gGAAoB,QAAQ,0DAArB,WAAW,WAAY,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,gBAAgB,CAAC;gBACxJ,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACnD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5G,CAAC,CAAC,CAAC;IACP,CAAC;CA4CJ", "debugId": null}}, {"offset": {"line": 2893, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/positionGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/positionGizmo.ts"], "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Quaternion } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Node } from \"../node\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { GizmoAnchorPoint, GizmoCoordinatesMode, GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport type { IAxisDragGizmo } from \"./axisDragGizmo\";\r\nimport { AxisDragGizmo } from \"./axisDragGizmo\";\r\nimport type { IPlaneDragGizmo } from \"./planeDragGizmo\";\r\nimport { PlaneDragGizmo } from \"./planeDragGizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport type { GizmoManager } from \"./gizmoManager\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\n\r\n/**\r\n * Interface for position gizmo\r\n */\r\nexport interface IPositionGizmo extends IGizmo {\r\n    /** Internal gizmo used for interactions on the x axis */\r\n    xGizmo: IAxisDragGizmo;\r\n    /** Internal gizmo used for interactions on the y axis */\r\n    yGizmo: IAxisDragGizmo;\r\n    /** Internal gizmo used for interactions on the z axis */\r\n    zGizmo: IAxisDragGizmo;\r\n    /** Internal gizmo used for interactions on the yz plane */\r\n    xPlaneGizmo: IPlaneDragGizmo;\r\n    /** Internal gizmo used for interactions on the xz plane */\r\n    yPlaneGizmo: IPlaneDragGizmo;\r\n    /** Internal gizmo used for interactions on the xy plane */\r\n    zPlaneGizmo: IPlaneDragGizmo;\r\n    /** True when the mouse pointer is dragging a gizmo mesh */\r\n    readonly isDragging: boolean;\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    onDragStartObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    onDragObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    onDragEndObservable: Observable<unknown>;\r\n    /**\r\n     * If the planar drag gizmo is enabled\r\n     * setting this will enable/disable XY, XZ and YZ planes regardless of individual gizmo settings.\r\n     */\r\n    planarGizmoEnabled: boolean;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    addToAxisCache(mesh: Mesh, cache: GizmoAxisCache): void;\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    releaseDrag(): void;\r\n}\r\n\r\n/**\r\n * Additional options for the position gizmo\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface PositionGizmoOptions {\r\n    /**\r\n     * Additional transform applied to the gizmo.\r\n     * @See Gizmo.additionalTransformNode for more detail\r\n     */\r\n    additionalTransformNode?: TransformNode;\r\n}\r\n\r\n/**\r\n * Gizmo that enables dragging a mesh along 3 axis\r\n */\r\nexport class PositionGizmo extends Gizmo implements IPositionGizmo {\r\n    /**\r\n     * Internal gizmo used for interactions on the x axis\r\n     */\r\n    public xGizmo: IAxisDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the y axis\r\n     */\r\n    public yGizmo: IAxisDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the z axis\r\n     */\r\n    public zGizmo: IAxisDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the yz plane\r\n     */\r\n    public xPlaneGizmo: IPlaneDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the xz plane\r\n     */\r\n    public yPlaneGizmo: IPlaneDragGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the xy plane\r\n     */\r\n    public zPlaneGizmo: IPlaneDragGizmo;\r\n\r\n    /**\r\n     * protected variables\r\n     */\r\n    protected _meshAttached: Nullable<AbstractMesh> = null;\r\n    protected _nodeAttached: Nullable<Node> = null;\r\n    protected _snapDistance: number;\r\n    protected _observables: Observer<PointerInfo>[] = [];\r\n\r\n    /** Node Caching for quick lookup */\r\n    protected _gizmoAxisCache: Map<Mesh, GizmoAxisCache> = new Map();\r\n\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    public onDragStartObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    public onDragObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    public onDragEndObservable = new Observable();\r\n\r\n    /**\r\n     * If set to true, planar drag is enabled\r\n     */\r\n    protected _planarGizmoEnabled = false;\r\n\r\n    public override get attachedMesh() {\r\n        return this._meshAttached;\r\n    }\r\n    public override set attachedMesh(mesh: Nullable<AbstractMesh>) {\r\n        this._meshAttached = mesh;\r\n        this._nodeAttached = mesh;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedMesh = mesh;\r\n            } else {\r\n                gizmo.attachedMesh = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    public override get attachedNode() {\r\n        return this._nodeAttached;\r\n    }\r\n    public override set attachedNode(node: Nullable<Node>) {\r\n        this._meshAttached = null;\r\n        this._nodeAttached = node;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedNode = node;\r\n            } else {\r\n                gizmo.attachedNode = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is hovering a gizmo mesh\r\n     */\r\n    public override get isHovered() {\r\n        return this.xGizmo.isHovered || this.yGizmo.isHovered || this.zGizmo.isHovered || this.xPlaneGizmo.isHovered || this.yPlaneGizmo.isHovered || this.zPlaneGizmo.isHovered;\r\n    }\r\n\r\n    public get isDragging() {\r\n        return (\r\n            this.xGizmo.dragBehavior.dragging ||\r\n            this.yGizmo.dragBehavior.dragging ||\r\n            this.zGizmo.dragBehavior.dragging ||\r\n            this.xPlaneGizmo.dragBehavior.dragging ||\r\n            this.yPlaneGizmo.dragBehavior.dragging ||\r\n            this.zPlaneGizmo.dragBehavior.dragging\r\n        );\r\n    }\r\n\r\n    public override get additionalTransformNode() {\r\n        return this._additionalTransformNode;\r\n    }\r\n\r\n    public override set additionalTransformNode(transformNode: TransformNode | undefined) {\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.additionalTransformNode = transformNode;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a PositionGizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param thickness display gizmo axis thickness\r\n     * @param gizmoManager\r\n     * @param options More options\r\n     */\r\n    constructor(gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer, thickness: number = 1, gizmoManager?: GizmoManager, options?: PositionGizmoOptions) {\r\n        super(gizmoLayer);\r\n        this.xGizmo = new AxisDragGizmo(new Vector3(1, 0, 0), Color3.Red().scale(0.5), gizmoLayer, this, thickness);\r\n        this.yGizmo = new AxisDragGizmo(new Vector3(0, 1, 0), Color3.Green().scale(0.5), gizmoLayer, this, thickness);\r\n        this.zGizmo = new AxisDragGizmo(new Vector3(0, 0, 1), Color3.Blue().scale(0.5), gizmoLayer, this, thickness);\r\n\r\n        this.xPlaneGizmo = new PlaneDragGizmo(new Vector3(1, 0, 0), Color3.Red().scale(0.5), this.gizmoLayer, this);\r\n        this.yPlaneGizmo = new PlaneDragGizmo(new Vector3(0, 1, 0), Color3.Green().scale(0.5), this.gizmoLayer, this);\r\n        this.zPlaneGizmo = new PlaneDragGizmo(new Vector3(0, 0, 1), Color3.Blue().scale(0.5), this.gizmoLayer, this);\r\n\r\n        this.additionalTransformNode = options?.additionalTransformNode;\r\n\r\n        // Relay drag events\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.dragBehavior.onDragStartObservable.add(() => {\r\n                this.onDragStartObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragObservable.add(() => {\r\n                this.onDragObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragEndObservable.add(() => {\r\n                this.onDragEndObservable.notifyObservers({});\r\n            });\r\n        }\r\n\r\n        this.attachedMesh = null;\r\n\r\n        if (gizmoManager) {\r\n            gizmoManager.addToAxisCache(this._gizmoAxisCache);\r\n        } else {\r\n            // Only subscribe to pointer event if gizmoManager isnt\r\n            Gizmo.GizmoAxisPointerObserver(gizmoLayer, this._gizmoAxisCache);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If the planar drag gizmo is enabled\r\n     * setting this will enable/disable XY, XZ and YZ planes regardless of individual gizmo settings.\r\n     */\r\n    public set planarGizmoEnabled(value: boolean) {\r\n        this._planarGizmoEnabled = value;\r\n        const gizmos = [this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.isEnabled = value;\r\n                if (value) {\r\n                    if (gizmo.attachedMesh) {\r\n                        gizmo.attachedMesh = this.attachedMesh;\r\n                    } else {\r\n                        gizmo.attachedNode = this.attachedNode;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    public get planarGizmoEnabled(): boolean {\r\n        return this._planarGizmoEnabled;\r\n    }\r\n\r\n    /**\r\n     * posture that the gizmo will be display\r\n     * When set null, default value will be used (Quaternion(0, 0, 0, 1))\r\n     */\r\n    public override get customRotationQuaternion(): Nullable<Quaternion> {\r\n        return this._customRotationQuaternion;\r\n    }\r\n\r\n    public override set customRotationQuaternion(customRotationQuaternion: Nullable<Quaternion>) {\r\n        this._customRotationQuaternion = customRotationQuaternion;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.customRotationQuaternion = customRotationQuaternion;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If set the gizmo's rotation will be updated to match the attached mesh each frame (Default: true)\r\n     * NOTE: This is only possible for meshes with uniform scaling, as otherwise it's not possible to decompose the rotation\r\n     */\r\n    public override set updateGizmoRotationToMatchAttachedMesh(value: boolean) {\r\n        this._updateGizmoRotationToMatchAttachedMesh = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n            }\r\n        }\r\n    }\r\n    public override get updateGizmoRotationToMatchAttachedMesh() {\r\n        return this._updateGizmoRotationToMatchAttachedMesh;\r\n    }\r\n\r\n    public override set updateGizmoPositionToMatchAttachedMesh(value: boolean) {\r\n        this._updateGizmoPositionToMatchAttachedMesh = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.updateGizmoPositionToMatchAttachedMesh = value;\r\n            }\r\n        }\r\n    }\r\n    public override get updateGizmoPositionToMatchAttachedMesh() {\r\n        return this._updateGizmoPositionToMatchAttachedMesh;\r\n    }\r\n\r\n    public override set anchorPoint(value: GizmoAnchorPoint) {\r\n        this._anchorPoint = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.anchorPoint = value;\r\n        }\r\n    }\r\n    public override get anchorPoint() {\r\n        return this._anchorPoint;\r\n    }\r\n\r\n    /**\r\n     * Set the coordinate system to use. By default it's local.\r\n     * But it's possible for a user to tweak so its local for translation and world for rotation.\r\n     * In that case, setting the coordinate system will change `updateGizmoRotationToMatchAttachedMesh` and `updateGizmoPositionToMatchAttachedMesh`\r\n     */\r\n    public override set coordinatesMode(coordinatesMode: GizmoCoordinatesMode) {\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.coordinatesMode = coordinatesMode;\r\n        }\r\n    }\r\n\r\n    public override set updateScale(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateScale = value;\r\n            this.yGizmo.updateScale = value;\r\n            this.zGizmo.updateScale = value;\r\n        }\r\n    }\r\n    public override get updateScale() {\r\n        return this.xGizmo.updateScale;\r\n    }\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public set snapDistance(value: number) {\r\n        this._snapDistance = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.snapDistance = value;\r\n            }\r\n        }\r\n    }\r\n    public get snapDistance() {\r\n        return this._snapDistance;\r\n    }\r\n\r\n    /**\r\n     * Ratio for the scale of the gizmo (Default: 1)\r\n     */\r\n    public override set scaleRatio(value: number) {\r\n        this._scaleRatio = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.scaleRatio = value;\r\n            }\r\n        }\r\n    }\r\n    public override get scaleRatio() {\r\n        return this._scaleRatio;\r\n    }\r\n\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    public addToAxisCache(mesh: Mesh, cache: GizmoAxisCache) {\r\n        this._gizmoAxisCache.set(mesh, cache);\r\n    }\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    public releaseDrag() {\r\n        this.xGizmo.dragBehavior.releaseDrag();\r\n        this.yGizmo.dragBehavior.releaseDrag();\r\n        this.zGizmo.dragBehavior.releaseDrag();\r\n        this.xPlaneGizmo.dragBehavior.releaseDrag();\r\n        this.yPlaneGizmo.dragBehavior.releaseDrag();\r\n        this.zPlaneGizmo.dragBehavior.releaseDrag();\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public override dispose() {\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.xPlaneGizmo, this.yPlaneGizmo, this.zPlaneGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.dispose();\r\n            }\r\n        }\r\n        for (const obs of this._observables) {\r\n            this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(obs);\r\n        }\r\n        this.onDragStartObservable.clear();\r\n        this.onDragObservable.clear();\r\n        this.onDragEndObservable.clear();\r\n        super.dispose();\r\n    }\r\n\r\n    /**\r\n     * CustomMeshes are not supported by this gizmo\r\n     */\r\n    public override setCustomMesh() {\r\n        Logger.Error(\r\n            \"Custom meshes are not supported on this gizmo, please set the custom meshes on the gizmos contained within this one (gizmo.xGizmo, gizmo.yGizmo, gizmo.zGizmo,gizmo.xPlaneGizmo, gizmo.yPlaneGizmo, gizmo.zPlaneGizmo)\"\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAK7C,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;;;;;;;;;AA+DnE,MAAO,aAAc,mKAAQ,QAAK;IAiDpC,IAAoB,YAAY,GAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAoB,YAAY,CAAC,IAA4B,EAAA;QACzD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBAClB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAoB,YAAY,GAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAoB,YAAY,CAAC,IAAoB,EAAA;QACjD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBAClB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAoB,SAAS,GAAA;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;IAC7K,CAAC;IAED,IAAW,UAAU,GAAA;QACjB,OACI,AADG,IACC,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IACjC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IACjC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IACjC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,IACtC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,IACtC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,CACzC,CAAC;IACN,CAAC;IAED,IAAoB,uBAAuB,GAAA;QACvC,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAoB,uBAAuB,CAAC,aAAwC,EAAA;QAChF,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,uBAAuB,GAAG,aAAa,CAAC;QAClD,CAAC;IACL,CAAC;IA6CD;;;OAGG,CACH,IAAW,kBAAkB,CAAC,KAAc,EAAA;QACxC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QACtE,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;gBACxB,IAAI,KAAK,EAAE,CAAC;oBACR,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;wBACrB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;oBAC3C,CAAC,MAAM,CAAC;wBACJ,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;oBAC3C,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;;OAGG,CACH,IAAoB,wBAAwB,GAAA;QACxC,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED,IAAoB,wBAAwB,CAAC,wBAA8C,EAAA;QACvF,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;QAC1D,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;YAC9D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACH,IAAoB,sCAAsC,CAAC,KAAc,EAAA;QACrE,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC;QACrD,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,sCAAsC,GAAG,KAAK,CAAC;YACzD,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAoB,sCAAsC,GAAA;QACtD,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED,IAAoB,sCAAsC,CAAC,KAAc,EAAA;QACrE,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC;QACrD,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,sCAAsC,GAAG,KAAK,CAAC;YACzD,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAoB,sCAAsC,GAAA;QACtD,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED,IAAoB,WAAW,CAAC,KAAuB,EAAA;QACnD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QAC9B,CAAC;IACL,CAAC;IACD,IAAoB,WAAW,GAAA;QAC3B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG,CACH,IAAoB,eAAe,CAAC,eAAqC,EAAA;QACrE,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;QAC5C,CAAC;IACL,CAAC;IAED,IAAoB,WAAW,CAAC,KAAc,EAAA;QAC1C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;QACpC,CAAC;IACL,CAAC;IACD,IAAoB,WAAW,GAAA;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACnC,CAAC;IACD;;OAEG,CACH,IAAW,YAAY,CAAC,KAAa,EAAA;QACjC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAoB,UAAU,CAAC,KAAa,EAAA;QACxC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;YAC7B,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAoB,UAAU,GAAA;QAC1B,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,IAAU,EAAE,KAAqB,EAAA;QACnD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IACD;;OAEG,CACI,WAAW,GAAA;QACd,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;QACD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAE,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACtE,CAAC;QACD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAED;;OAEG,CACa,aAAa,GAAA;iKACzB,SAAM,CAAC,KAAK,CACR,wNAAwN,CAC3N,CAAC;IACN,CAAC;IAlOD;;;;;;OAMG,CACH,YAAY,yLAAmC,uBAAoB,CAAC,mBAAmB,EAAE,YAAoB,CAAC,EAAE,YAA2B,EAAE,OAA8B,CAAA;QACvK,KAAK,CAAC,UAAU,CAAC,CAAC;QA5FtB;;WAEG,CACO,IAAA,CAAA,aAAa,GAA2B,IAAI,CAAC;QAC7C,IAAA,CAAA,aAAa,GAAmB,IAAI,CAAC;QAErC,IAAA,CAAA,YAAY,GAA4B,EAAE,CAAC;QAErD,kCAAA,EAAoC,CAC1B,IAAA,CAAA,eAAe,GAA8B,IAAI,GAAG,EAAE,CAAC;QAEjE,2DAAA,EAA6D,CACtD,IAAA,CAAA,qBAAqB,GAAG,iKAAI,aAAU,EAAE,CAAC;QAChD,iEAAA,EAAmE,CAC5D,IAAA,CAAA,gBAAgB,GAAG,iKAAI,aAAU,EAAE,CAAC;QAC3C,0EAAA,EAA4E,CACrE,IAAA,CAAA,mBAAmB,GAAG,iKAAI,aAAU,EAAE,CAAC;QAE9C;;WAEG,CACO,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAwElC,IAAI,CAAC,MAAM,GAAG,IAAI,kLAAa,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,mKAAE,SAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC5G,IAAI,CAAC,MAAM,GAAG,sKAAI,gBAAa,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,mKAAE,SAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC9G,IAAI,CAAC,MAAM,GAAG,sKAAI,gBAAa,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,0KAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAE7G,IAAI,CAAC,WAAW,GAAG,uKAAI,iBAAc,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,kKAAE,UAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC5G,IAAI,CAAC,WAAW,GAAG,uKAAI,iBAAc,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,kKAAE,UAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC9G,IAAI,CAAC,WAAW,GAAG,uKAAI,iBAAc,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,kKAAE,UAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAE7G,IAAI,CAAC,uBAAuB,qDAAG,OAAO,CAAE,uBAAuB,CAAC;QAEhE,oBAAoB;QACpB,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC7G,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,YAAY,EAAE,CAAC;YACf,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC,MAAM,CAAC;YACJ,uDAAuD;sKACvD,QAAK,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;CA0LJ", "debugId": null}}, {"offset": {"line": 3262, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/scaleGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/scaleGizmo.ts"], "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Quaternion } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { CreatePolyhedron } from \"../Meshes/Builders/polyhedronBuilder\";\r\nimport type { GizmoAnchorPoint, GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { GizmoCoordinatesMode, Gizmo } from \"./gizmo\";\r\nimport type { IAxisScaleGizmo } from \"./axisScaleGizmo\";\r\nimport { AxisScaleGizmo } from \"./axisScaleGizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { Node } from \"../node\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { GizmoManager } from \"./gizmoManager\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\n\r\n/**\r\n * Interface for scale gizmo\r\n */\r\nexport interface IScaleGizmo extends IGizmo {\r\n    /** True when the mouse pointer is dragging a gizmo mesh */\r\n    readonly isDragging: boolean;\r\n    /** Internal gizmo used for interactions on the x axis */\r\n    xGizmo: IAxisScaleGizmo;\r\n    /** Internal gizmo used for interactions on the y axis */\r\n    yGizmo: IAxisScaleGizmo;\r\n    /** Internal gizmo used for interactions on the z axis */\r\n    zGizmo: IAxisScaleGizmo;\r\n    /** Internal gizmo used to scale all axis equally*/\r\n    uniformScaleGizmo: IAxisScaleGizmo;\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    onDragStartObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    onDragObservable: Observable<unknown>;\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    onDragEndObservable: Observable<unknown>;\r\n    /** Drag distance in babylon units that the gizmo will snap to when dragged */\r\n    snapDistance: number;\r\n    /** Incremental snap scaling. When true, with a snapDistance of 0.1, scaling will be 1.1,1.2,1.3 instead of, when false: 1.1,1.21,1.33,... */\r\n    incrementalSnap: boolean;\r\n    /** Sensitivity factor for dragging */\r\n    sensitivity: number;\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    addToAxisCache(mesh: Mesh, cache: GizmoAxisCache): void;\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    releaseDrag(): void;\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    coloredMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    hoverMaterial: StandardMaterial;\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    disableMaterial: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Additional options for the scale gizmo\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface ScaleGizmoOptions {\r\n    /**\r\n     * Additional transform applied to the gizmo.\r\n     * @See Gizmo.additionalTransformNode for more detail\r\n     */\r\n    additionalTransformNode?: TransformNode;\r\n}\r\n\r\n/**\r\n * Gizmo that enables scaling a mesh along 3 axis\r\n */\r\nexport class ScaleGizmo extends Gizmo implements IScaleGizmo {\r\n    /**\r\n     * Internal gizmo used for interactions on the x axis\r\n     */\r\n    public xGizmo: IAxisScaleGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the y axis\r\n     */\r\n    public yGizmo: IAxisScaleGizmo;\r\n    /**\r\n     * Internal gizmo used for interactions on the z axis\r\n     */\r\n    public zGizmo: IAxisScaleGizmo;\r\n\r\n    /**\r\n     * Internal gizmo used to scale all axis equally\r\n     */\r\n    public uniformScaleGizmo: IAxisScaleGizmo;\r\n\r\n    protected _meshAttached: Nullable<AbstractMesh> = null;\r\n    protected _nodeAttached: Nullable<Node> = null;\r\n    protected _snapDistance: number;\r\n    protected _incrementalSnap: boolean = false;\r\n    protected _uniformScalingMesh: Mesh;\r\n    protected _octahedron: Mesh;\r\n    protected _sensitivity: number = 1;\r\n    protected _coloredMaterial: StandardMaterial;\r\n    protected _hoverMaterial: StandardMaterial;\r\n    protected _disableMaterial: StandardMaterial;\r\n    protected _observables: Observer<PointerInfo>[] = [];\r\n\r\n    /** Node Caching for quick lookup */\r\n    protected _gizmoAxisCache: Map<Mesh, GizmoAxisCache> = new Map();\r\n\r\n    /** Default material used to render when gizmo is not disabled or hovered */\r\n    public get coloredMaterial() {\r\n        return this._coloredMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is hovered with mouse*/\r\n    public get hoverMaterial() {\r\n        return this._hoverMaterial;\r\n    }\r\n\r\n    /** Material used to render when gizmo is disabled. typically grey.*/\r\n    public get disableMaterial() {\r\n        return this._disableMaterial;\r\n    }\r\n    /** Fires an event when any of it's sub gizmos are dragged */\r\n    public onDragStartObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are being dragged */\r\n    public onDragObservable = new Observable();\r\n    /** Fires an event when any of it's sub gizmos are released from dragging */\r\n    public onDragEndObservable = new Observable();\r\n\r\n    public override get attachedMesh() {\r\n        return this._meshAttached;\r\n    }\r\n    public override set attachedMesh(mesh: Nullable<AbstractMesh>) {\r\n        this._meshAttached = mesh;\r\n        this._nodeAttached = mesh;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedMesh = mesh;\r\n            } else {\r\n                gizmo.attachedMesh = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    public override get attachedNode() {\r\n        return this._nodeAttached;\r\n    }\r\n    public override set attachedNode(node: Nullable<Node>) {\r\n        this._meshAttached = null;\r\n        this._nodeAttached = node;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo.isEnabled) {\r\n                gizmo.attachedNode = node;\r\n            } else {\r\n                gizmo.attachedNode = null;\r\n            }\r\n        }\r\n    }\r\n\r\n    public override set updateScale(value: boolean) {\r\n        if (this.xGizmo) {\r\n            this.xGizmo.updateScale = value;\r\n            this.yGizmo.updateScale = value;\r\n            this.zGizmo.updateScale = value;\r\n        }\r\n    }\r\n    public override get updateScale() {\r\n        return this.xGizmo.updateScale;\r\n    }\r\n    /**\r\n     * True when the mouse pointer is hovering a gizmo mesh\r\n     */\r\n    public override get isHovered() {\r\n        return this.xGizmo.isHovered || this.yGizmo.isHovered || this.zGizmo.isHovered || this.uniformScaleGizmo.isHovered;\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is dragging a gizmo mesh\r\n     */\r\n    public get isDragging() {\r\n        return this.xGizmo.dragBehavior.dragging || this.yGizmo.dragBehavior.dragging || this.zGizmo.dragBehavior.dragging || this.uniformScaleGizmo.dragBehavior.dragging;\r\n    }\r\n\r\n    public override get additionalTransformNode() {\r\n        return this._additionalTransformNode;\r\n    }\r\n\r\n    public override set additionalTransformNode(transformNode: TransformNode | undefined) {\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.additionalTransformNode = transformNode;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a ScaleGizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param thickness display gizmo axis thickness\r\n     * @param gizmoManager\r\n     * @param options More options\r\n     */\r\n    constructor(gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer, thickness: number = 1, gizmoManager?: GizmoManager, options?: ScaleGizmoOptions) {\r\n        super(gizmoLayer);\r\n        this.uniformScaleGizmo = this._createUniformScaleMesh();\r\n        this.xGizmo = new AxisScaleGizmo(new Vector3(1, 0, 0), Color3.Red().scale(0.5), gizmoLayer, this, thickness);\r\n        this.yGizmo = new AxisScaleGizmo(new Vector3(0, 1, 0), Color3.Green().scale(0.5), gizmoLayer, this, thickness);\r\n        this.zGizmo = new AxisScaleGizmo(new Vector3(0, 0, 1), Color3.Blue().scale(0.5), gizmoLayer, this, thickness);\r\n\r\n        this.additionalTransformNode = options?.additionalTransformNode;\r\n\r\n        // Relay drag events\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.dragBehavior.onDragStartObservable.add(() => {\r\n                this.onDragStartObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragObservable.add(() => {\r\n                this.onDragObservable.notifyObservers({});\r\n            });\r\n            gizmo.dragBehavior.onDragEndObservable.add(() => {\r\n                this.onDragEndObservable.notifyObservers({});\r\n            });\r\n        }\r\n\r\n        this.attachedMesh = null;\r\n        this.attachedNode = null;\r\n\r\n        if (gizmoManager) {\r\n            gizmoManager.addToAxisCache(this._gizmoAxisCache);\r\n        } else {\r\n            // Only subscribe to pointer event if gizmoManager isnt\r\n            Gizmo.GizmoAxisPointerObserver(gizmoLayer, this._gizmoAxisCache);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Create Geometry for Gizmo\r\n     */\r\n    protected _createUniformScaleMesh(): AxisScaleGizmo {\r\n        this._coloredMaterial = new StandardMaterial(\"\", this.gizmoLayer.utilityLayerScene);\r\n        this._coloredMaterial.diffuseColor = Color3.Gray();\r\n\r\n        this._hoverMaterial = new StandardMaterial(\"\", this.gizmoLayer.utilityLayerScene);\r\n        this._hoverMaterial.diffuseColor = Color3.Yellow();\r\n\r\n        this._disableMaterial = new StandardMaterial(\"\", this.gizmoLayer.utilityLayerScene);\r\n        this._disableMaterial.diffuseColor = Color3.Gray();\r\n        this._disableMaterial.alpha = 0.4;\r\n\r\n        const uniformScaleGizmo = new AxisScaleGizmo(new Vector3(0, 1, 0), Color3.Gray().scale(0.5), this.gizmoLayer, this);\r\n        uniformScaleGizmo.updateGizmoRotationToMatchAttachedMesh = false;\r\n        uniformScaleGizmo.uniformScaling = true;\r\n        this._uniformScalingMesh = CreatePolyhedron(\"uniform\", { type: 1 }, uniformScaleGizmo.gizmoLayer.utilityLayerScene);\r\n        this._uniformScalingMesh.scaling.scaleInPlace(0.01);\r\n        this._uniformScalingMesh.visibility = 0;\r\n        this._octahedron = CreatePolyhedron(\"\", { type: 1 }, uniformScaleGizmo.gizmoLayer.utilityLayerScene);\r\n        this._octahedron.scaling.scaleInPlace(0.007);\r\n        this._uniformScalingMesh.addChild(this._octahedron);\r\n        uniformScaleGizmo.setCustomMesh(this._uniformScalingMesh, true);\r\n        const light = this.gizmoLayer._getSharedGizmoLight();\r\n        light.includedOnlyMeshes = light.includedOnlyMeshes.concat(this._octahedron);\r\n\r\n        const cache: GizmoAxisCache = {\r\n            gizmoMeshes: [this._octahedron, this._uniformScalingMesh],\r\n            colliderMeshes: [this._octahedron, this._uniformScalingMesh],\r\n            material: this._coloredMaterial,\r\n            hoverMaterial: this._hoverMaterial,\r\n            disableMaterial: this._disableMaterial,\r\n            active: false,\r\n            dragBehavior: uniformScaleGizmo.dragBehavior,\r\n        };\r\n\r\n        this.addToAxisCache(uniformScaleGizmo._rootMesh, cache);\r\n\r\n        return uniformScaleGizmo;\r\n    }\r\n\r\n    public override set updateGizmoRotationToMatchAttachedMesh(value: boolean) {\r\n        if (!value) {\r\n            Logger.Warn(\"Setting updateGizmoRotationToMatchAttachedMesh = false on scaling gizmo is not supported.\");\r\n        } else {\r\n            this._updateGizmoRotationToMatchAttachedMesh = value;\r\n            const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n            for (const gizmo of gizmos) {\r\n                if (gizmo) {\r\n                    gizmo.updateGizmoRotationToMatchAttachedMesh = value;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    public override get updateGizmoRotationToMatchAttachedMesh() {\r\n        return this._updateGizmoRotationToMatchAttachedMesh;\r\n    }\r\n\r\n    public override set anchorPoint(value: GizmoAnchorPoint) {\r\n        this._anchorPoint = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.anchorPoint = value;\r\n            }\r\n        }\r\n    }\r\n    public override get anchorPoint() {\r\n        return this._anchorPoint;\r\n    }\r\n\r\n    /**\r\n     * posture that the gizmo will be display\r\n     * When set null, default value will be used (Quaternion(0, 0, 0, 1))\r\n     */\r\n    public override get customRotationQuaternion(): Nullable<Quaternion> {\r\n        return this._customRotationQuaternion;\r\n    }\r\n\r\n    public override set customRotationQuaternion(customRotationQuaternion: Nullable<Quaternion>) {\r\n        this._customRotationQuaternion = customRotationQuaternion;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.customRotationQuaternion = customRotationQuaternion;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the coordinate system to use. By default it's local.\r\n     * But it's possible for a user to tweak so its local for translation and world for rotation.\r\n     * In that case, setting the coordinate system will change `updateGizmoRotationToMatchAttachedMesh` and `updateGizmoPositionToMatchAttachedMesh`\r\n     */\r\n    public override set coordinatesMode(coordinatesMode: GizmoCoordinatesMode) {\r\n        if (coordinatesMode == GizmoCoordinatesMode.World) {\r\n            Logger.Warn(\"Setting coordinates Mode to world on scaling gizmo is not supported.\");\r\n        }\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo.coordinatesMode = GizmoCoordinatesMode.Local;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Drag distance in babylon units that the gizmo will snap to when dragged (Default: 0)\r\n     */\r\n    public set snapDistance(value: number) {\r\n        this._snapDistance = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.snapDistance = value;\r\n            }\r\n        }\r\n    }\r\n    public get snapDistance() {\r\n        return this._snapDistance;\r\n    }\r\n\r\n    /**\r\n     * Incremental snap scaling (default is false). When true, with a snapDistance of 0.1, scaling will be 1.1,1.2,1.3 instead of, when false: 1.1,1.21,1.33,...\r\n     */\r\n    public set incrementalSnap(value: boolean) {\r\n        this._incrementalSnap = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.incrementalSnap = value;\r\n            }\r\n        }\r\n    }\r\n    public get incrementalSnap() {\r\n        return this._incrementalSnap;\r\n    }\r\n    /**\r\n     * Ratio for the scale of the gizmo (Default: 1)\r\n     */\r\n    public override set scaleRatio(value: number) {\r\n        this._scaleRatio = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.scaleRatio = value;\r\n            }\r\n        }\r\n    }\r\n    public override get scaleRatio() {\r\n        return this._scaleRatio;\r\n    }\r\n\r\n    /**\r\n     * Sensitivity factor for dragging (Default: 1)\r\n     */\r\n    public set sensitivity(value: number) {\r\n        this._sensitivity = value;\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.sensitivity = value;\r\n            }\r\n        }\r\n    }\r\n    public get sensitivity() {\r\n        return this._sensitivity;\r\n    }\r\n\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param mesh Axis gizmo mesh\r\n     * @param cache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    public addToAxisCache(mesh: Mesh, cache: GizmoAxisCache) {\r\n        this._gizmoAxisCache.set(mesh, cache);\r\n    }\r\n\r\n    /**\r\n     * Get the cache set with addToAxisCache for a specific mesh\r\n     * @param mesh Axis gizmo mesh\r\n     * @returns Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    public getAxisCache(mesh: Mesh): GizmoAxisCache | undefined {\r\n        return this._gizmoAxisCache.get(mesh);\r\n    }\r\n\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    public releaseDrag() {\r\n        this.xGizmo.dragBehavior.releaseDrag();\r\n        this.yGizmo.dragBehavior.releaseDrag();\r\n        this.zGizmo.dragBehavior.releaseDrag();\r\n        this.uniformScaleGizmo.dragBehavior.releaseDrag();\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo\r\n     */\r\n    public override dispose() {\r\n        const gizmos = [this.xGizmo, this.yGizmo, this.zGizmo, this.uniformScaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.dispose();\r\n            }\r\n        }\r\n        for (const obs of this._observables) {\r\n            this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(obs);\r\n        }\r\n        this.onDragStartObservable.clear();\r\n        this.onDragObservable.clear();\r\n        this.onDragEndObservable.clear();\r\n        const meshes = [this._uniformScalingMesh, this._octahedron];\r\n        for (const msh of meshes) {\r\n            if (msh) {\r\n                msh.dispose();\r\n            }\r\n        }\r\n        const materials = [this._coloredMaterial, this._hoverMaterial, this._disableMaterial];\r\n        for (const matl of materials) {\r\n            if (matl) {\r\n                matl.dispose();\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,gBAAgB,EAAE,MAAM,sCAAsC,CAAC;AAExE,OAAO,EAAwB,KAAK,EAAE,MAAM,SAAS,CAAC;AAEtD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAIzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;;;;;;;;;;AAgE3D,MAAO,UAAW,mKAAQ,QAAK;IAkCjC,0EAAA,EAA4E,CAC5E,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,4DAAA,EAA8D,CAC9D,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,mEAAA,EAAqE,CACrE,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAQD,IAAoB,YAAY,GAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAoB,YAAY,CAAC,IAA4B,EAAA;QACzD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBAClB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAoB,YAAY,GAAA;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,IAAoB,YAAY,CAAC,IAAoB,EAAA;QACjD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBAClB,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAoB,WAAW,CAAC,KAAc,EAAA;QAC1C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;QACpC,CAAC;IACL,CAAC;IACD,IAAoB,WAAW,GAAA;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACnC,CAAC;IACD;;OAEG,CACH,IAAoB,SAAS,GAAA;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;IACvH,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC;IACvK,CAAC;IAED,IAAoB,uBAAuB,GAAA;QACvC,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAoB,uBAAuB,CAAC,aAAwC,EAAA;QAChF,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,uBAAuB,GAAG,aAAa,CAAC;QAClD,CAAC;IACL,CAAC;IA2CD;;;OAGG,CACO,uBAAuB,GAAA;QAC7B,IAAI,CAAC,gBAAgB,GAAG,2KAAI,oBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACpF,IAAI,CAAC,gBAAgB,CAAC,YAAY,oKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;QAEnD,IAAI,CAAC,cAAc,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAClF,IAAI,CAAC,cAAc,CAAC,YAAY,oKAAG,SAAM,CAAC,MAAM,EAAE,CAAC;QAEnD,IAAI,CAAC,gBAAgB,GAAG,4KAAI,mBAAgB,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACpF,IAAI,CAAC,gBAAgB,CAAC,YAAY,oKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;QACnD,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,GAAG,CAAC;QAElC,MAAM,iBAAiB,GAAG,IAAI,oLAAc,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,mKAAE,SAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACpH,iBAAiB,CAAC,sCAAsC,GAAG,KAAK,CAAC;QACjE,iBAAiB,CAAC,cAAc,GAAG,IAAI,CAAC;QACxC,IAAI,CAAC,mBAAmB,yLAAG,mBAAA,AAAgB,EAAC,SAAS,EAAE;YAAE,IAAI,EAAE,CAAC;QAAA,CAAE,EAAE,iBAAiB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACpH,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,mBAAmB,CAAC,UAAU,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW,yLAAG,mBAAA,AAAgB,EAAC,EAAE,EAAE;YAAE,IAAI,EAAE,CAAC;QAAA,CAAE,EAAE,iBAAiB,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACrG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpD,iBAAiB,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAChE,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;QACrD,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE7E,MAAM,KAAK,GAAmB;YAC1B,WAAW,EAAE;gBAAC,IAAI,CAAC,WAAW;gBAAE,IAAI,CAAC,mBAAmB;aAAC;YACzD,cAAc,EAAE;gBAAC,IAAI,CAAC,WAAW;gBAAE,IAAI,CAAC,mBAAmB;aAAC;YAC5D,QAAQ,EAAE,IAAI,CAAC,gBAAgB;YAC/B,aAAa,EAAE,IAAI,CAAC,cAAc;YAClC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,iBAAiB,CAAC,YAAY;SAC/C,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAExD,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED,IAAoB,sCAAsC,CAAC,KAAc,EAAA;QACrE,IAAI,CAAC,KAAK,EAAE,CAAC;qKACT,SAAM,CAAC,IAAI,CAAC,2FAA2F,CAAC,CAAC;QAC7G,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC;YACrD,MAAM,MAAM,GAAG;gBAAC,IAAI,CAAC,MAAM;gBAAE,IAAI,CAAC,MAAM;gBAAE,IAAI,CAAC,MAAM;gBAAE,IAAI,CAAC,iBAAiB;aAAC,CAAC;YAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;gBACzB,IAAI,KAAK,EAAE,CAAC;oBACR,KAAK,CAAC,sCAAsC,GAAG,KAAK,CAAC;gBACzD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAoB,sCAAsC,GAAA;QACtD,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED,IAAoB,WAAW,CAAC,KAAuB,EAAA;QACnD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAoB,WAAW,GAAA;QAC3B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACH,IAAoB,wBAAwB,GAAA;QACxC,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IAED,IAAoB,wBAAwB,CAAC,wBAA8C,EAAA;QACvF,IAAI,CAAC,yBAAyB,GAAG,wBAAwB,CAAC;QAC1D,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;YAC9D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACH,IAAoB,eAAe,CAAC,eAAqC,EAAA;QACrE,IAAI,eAAe,IAAA,EAAA,8BAAA,EAA8B,GAAE,CAAC;qKAChD,SAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAC;QACxF,CAAC;QACD,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,eAAe,GAAA,EAAA,8BAAA,EAA6B,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,CAAC,KAAa,EAAA;QACjC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,eAAe,CAAC,KAAc,EAAA;QACrC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;YAClC,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IACD;;OAEG,CACH,IAAoB,UAAU,CAAC,KAAa,EAAA;QACxC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;YAC7B,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAoB,UAAU,GAAA;QAC1B,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,IAAU,EAAE,KAAqB,EAAA;QACnD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,IAAU,EAAA;QAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG,CACI,WAAW,GAAA;QACd,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;QACD,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAE,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACtE,CAAC;QACD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,mBAAmB;YAAE,IAAI,CAAC,WAAW;SAAC,CAAC;QAC5D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAE,CAAC;YACvB,IAAI,GAAG,EAAE,CAAC;gBACN,GAAG,CAAC,OAAO,EAAE,CAAC;YAClB,CAAC;QACL,CAAC;QACD,MAAM,SAAS,GAAG;YAAC,IAAI,CAAC,gBAAgB;YAAE,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,gBAAgB;SAAC,CAAC;QACtF,KAAK,MAAM,IAAI,IAAI,SAAS,CAAE,CAAC;YAC3B,IAAI,IAAI,EAAE,CAAC;gBACP,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACL,CAAC;IACL,CAAC;IA1QD;;;;;;OAMG,CACH,YAAY,aAAmC,mMAAoB,CAAC,mBAAmB,EAAE,YAAoB,CAAC,EAAE,YAA2B,EAAE,OAA2B,CAAA;QACpK,KAAK,CAAC,UAAU,CAAC,CAAC;QA/GZ,IAAA,CAAA,aAAa,GAA2B,IAAI,CAAC;QAC7C,IAAA,CAAA,aAAa,GAAmB,IAAI,CAAC;QAErC,IAAA,CAAA,gBAAgB,GAAY,KAAK,CAAC;QAGlC,IAAA,CAAA,YAAY,GAAW,CAAC,CAAC;QAIzB,IAAA,CAAA,YAAY,GAA4B,EAAE,CAAC;QAErD,kCAAA,EAAoC,CAC1B,IAAA,CAAA,eAAe,GAA8B,IAAI,GAAG,EAAE,CAAC;QAgBjE,2DAAA,EAA6D,CACtD,IAAA,CAAA,qBAAqB,GAAG,iKAAI,aAAU,EAAE,CAAC;QAChD,iEAAA,EAAmE,CAC5D,IAAA,CAAA,gBAAgB,GAAG,iKAAI,aAAU,EAAE,CAAC;QAC3C,0EAAA,EAA4E,CACrE,IAAA,CAAA,mBAAmB,GAAG,iKAAI,aAAU,EAAE,CAAC;QA8E1C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACxD,IAAI,CAAC,MAAM,GAAG,uKAAI,iBAAc,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,mKAAE,SAAM,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC7G,IAAI,CAAC,MAAM,GAAG,uKAAI,iBAAc,CAAC,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,mKAAE,SAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAC/G,IAAI,CAAC,MAAM,GAAG,uKAAI,iBAAc,CAAC,IAAI,4KAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,mKAAE,SAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QAE9G,IAAI,CAAC,uBAAuB,uBAAG,OAAO,+BAAE,uBAAuB,CAAC;QAEhE,oBAAoB;QACpB,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,MAAM;YAAE,IAAI,CAAC,iBAAiB;SAAC,CAAC;QAC/E,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACzC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YACH,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,YAAY,EAAE,CAAC;YACf,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtD,CAAC,MAAM,CAAC;YACJ,uDAAuD;sKACvD,QAAK,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;CAoOJ", "debugId": null}}, {"offset": {"line": 3680, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/gizmoManager.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/gizmoManager.ts"], "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../Events/pointerEvents\";\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport type { Node } from \"../node\";\r\nimport { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { SixDofDragBehavior } from \"../Behaviors/Meshes/sixDofDragBehavior\";\r\nimport type { GizmoAxisCache, IGizmo } from \"./gizmo\";\r\nimport { Gizmo, GizmoCoordinatesMode } from \"./gizmo\";\r\nimport type { IRotationGizmo } from \"./rotationGizmo\";\r\nimport { RotationGizmo } from \"./rotationGizmo\";\r\nimport type { IPositionGizmo } from \"./positionGizmo\";\r\nimport { PositionGizmo } from \"./positionGizmo\";\r\nimport type { IScaleGizmo } from \"./scaleGizmo\";\r\nimport { ScaleGizmo } from \"./scaleGizmo\";\r\nimport type { IBoundingBoxGizmo } from \"./boundingBoxGizmo\";\r\nimport { BoundingBoxGizmo } from \"./boundingBoxGizmo\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\n\r\n/**\r\n * Helps setup gizmo's in the scene to rotate/scale/position nodes\r\n */\r\nexport class GizmoManager implements IDisposable {\r\n    /**\r\n     * Gizmo's created by the gizmo manager, gizmo will be null until gizmo has been enabled for the first time\r\n     */\r\n    public gizmos: {\r\n        positionGizmo: Nullable<IPositionGizmo>;\r\n        rotationGizmo: Nullable<IRotationGizmo>;\r\n        scaleGizmo: Nullable<IScaleGizmo>;\r\n        boundingBoxGizmo: Nullable<IBoundingBoxGizmo>;\r\n    };\r\n\r\n    /** When true, the gizmo will be detached from the current object when a pointer down occurs with an empty picked mesh */\r\n    public clearGizmoOnEmptyPointerEvent = false;\r\n\r\n    /** When true (default), picking to attach a new mesh is enabled. This works in sync with inspector autopicking. */\r\n    public enableAutoPicking = true;\r\n\r\n    /** Fires an event when the manager is attached to a mesh */\r\n    public onAttachedToMeshObservable = new Observable<Nullable<AbstractMesh>>();\r\n\r\n    /** Fires an event when the manager is attached to a node */\r\n    public onAttachedToNodeObservable = new Observable<Nullable<Node>>();\r\n\r\n    protected _gizmosEnabled = { positionGizmo: false, rotationGizmo: false, scaleGizmo: false, boundingBoxGizmo: false };\r\n    protected _pointerObservers: Observer<PointerInfo>[] = [];\r\n    protected _attachedMesh: Nullable<AbstractMesh> = null;\r\n    protected _attachedNode: Nullable<Node> = null;\r\n    protected _boundingBoxColor = Color3.FromHexString(\"#0984e3\");\r\n    protected _defaultUtilityLayer: UtilityLayerRenderer;\r\n    protected _defaultKeepDepthUtilityLayer: UtilityLayerRenderer;\r\n    protected _thickness: number = 1;\r\n    protected _scaleRatio: number = 1;\r\n    protected _coordinatesMode = GizmoCoordinatesMode.Local;\r\n    protected _additionalTransformNode?: TransformNode;\r\n\r\n    /** Node Caching for quick lookup */\r\n    private _gizmoAxisCache: Map<Mesh, GizmoAxisCache> = new Map();\r\n    /**\r\n     * When bounding box gizmo is enabled, this can be used to track drag/end events\r\n     */\r\n    public boundingBoxDragBehavior = new SixDofDragBehavior();\r\n    /**\r\n     * Array of meshes which will have the gizmo attached when a pointer selected them. If null, all meshes are attachable. (Default: null)\r\n     */\r\n    public attachableMeshes: Nullable<Array<AbstractMesh>> = null;\r\n    /**\r\n     * Array of nodes which will have the gizmo attached when a pointer selected them. If null, all nodes are attachable. (Default: null)\r\n     */\r\n    public attachableNodes: Nullable<Array<Node>> = null;\r\n    /**\r\n     * If pointer events should perform attaching/detaching a gizmo, if false this can be done manually via attachToMesh/attachToNode. (Default: true)\r\n     */\r\n    public usePointerToAttachGizmos = true;\r\n\r\n    /**\r\n     * Utility layer that the bounding box gizmo belongs to\r\n     */\r\n    public get keepDepthUtilityLayer() {\r\n        return this._defaultKeepDepthUtilityLayer;\r\n    }\r\n\r\n    /**\r\n     * Utility layer that all gizmos besides bounding box belong to\r\n     */\r\n    public get utilityLayer() {\r\n        return this._defaultUtilityLayer;\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is hovering a gizmo mesh\r\n     */\r\n    public get isHovered() {\r\n        let hovered = false;\r\n        for (const key in this.gizmos) {\r\n            const gizmo = <Nullable<IGizmo>>(<any>this.gizmos)[key];\r\n            if (gizmo && gizmo.isHovered) {\r\n                hovered = true;\r\n                break;\r\n            }\r\n        }\r\n        return hovered;\r\n    }\r\n\r\n    /**\r\n     * True when the mouse pointer is dragging a gizmo mesh\r\n     */\r\n    public get isDragging() {\r\n        let dragging = false;\r\n\r\n        const gizmos = [this.gizmos.positionGizmo, this.gizmos.rotationGizmo, this.gizmos.scaleGizmo, this.gizmos.boundingBoxGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo && gizmo.isDragging) {\r\n                dragging = true;\r\n            }\r\n        }\r\n\r\n        return dragging;\r\n    }\r\n\r\n    /**\r\n     * Ratio for the scale of the gizmo (Default: 1)\r\n     */\r\n    public set scaleRatio(value: number) {\r\n        this._scaleRatio = value;\r\n        const gizmos = [this.gizmos.positionGizmo, this.gizmos.rotationGizmo, this.gizmos.scaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.scaleRatio = value;\r\n            }\r\n        }\r\n    }\r\n    public get scaleRatio() {\r\n        return this._scaleRatio;\r\n    }\r\n\r\n    /**\r\n     * Set the coordinate system to use. By default it's local.\r\n     * But it's possible for a user to tweak so its local for translation and world for rotation.\r\n     * In that case, setting the coordinate system will change `updateGizmoRotationToMatchAttachedMesh` and `updateGizmoPositionToMatchAttachedMesh`\r\n     */\r\n    public set coordinatesMode(coordinatesMode: GizmoCoordinatesMode) {\r\n        this._coordinatesMode = coordinatesMode;\r\n        const gizmos = [this.gizmos.positionGizmo, this.gizmos.rotationGizmo, this.gizmos.scaleGizmo];\r\n        for (const gizmo of gizmos) {\r\n            if (gizmo) {\r\n                gizmo.coordinatesMode = coordinatesMode;\r\n            }\r\n        }\r\n    }\r\n\r\n    public get coordinatesMode(): GizmoCoordinatesMode {\r\n        return this._coordinatesMode;\r\n    }\r\n\r\n    /**\r\n     * The mesh the gizmo's is attached to\r\n     */\r\n    public get attachedMesh() {\r\n        return this._attachedMesh;\r\n    }\r\n\r\n    /**\r\n     * The node the gizmo's is attached to\r\n     */\r\n    public get attachedNode() {\r\n        return this._attachedNode;\r\n    }\r\n\r\n    /**\r\n     * Additional transform node that will be used to transform all the gizmos\r\n     */\r\n    public get additionalTransformNode() {\r\n        return this._additionalTransformNode;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a gizmo manager\r\n     * @param _scene the scene to overlay the gizmos on top of\r\n     * @param thickness display gizmo axis thickness\r\n     * @param utilityLayer the layer where gizmos are rendered\r\n     * @param keepDepthUtilityLayer the layer where occluded gizmos are rendered\r\n     */\r\n    constructor(\r\n        private _scene: Scene,\r\n        thickness: number = 1,\r\n        utilityLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer,\r\n        keepDepthUtilityLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultKeepDepthUtilityLayer\r\n    ) {\r\n        this._defaultUtilityLayer = utilityLayer;\r\n        this._defaultKeepDepthUtilityLayer = keepDepthUtilityLayer;\r\n        this._defaultKeepDepthUtilityLayer.utilityLayerScene.autoClearDepthAndStencil = false;\r\n        this._thickness = thickness;\r\n        this.gizmos = { positionGizmo: null, rotationGizmo: null, scaleGizmo: null, boundingBoxGizmo: null };\r\n\r\n        const attachToMeshPointerObserver = this._attachToMeshPointerObserver(_scene);\r\n        const gizmoAxisPointerObserver = Gizmo.GizmoAxisPointerObserver(this._defaultUtilityLayer, this._gizmoAxisCache);\r\n        this._pointerObservers = [attachToMeshPointerObserver, gizmoAxisPointerObserver];\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Subscribes to pointer down events, for attaching and detaching mesh\r\n     * @param scene The scene layer the observer will be added to\r\n     * @returns the pointer observer\r\n     */\r\n    private _attachToMeshPointerObserver(scene: Scene): Observer<PointerInfo> {\r\n        // Instantiate/dispose gizmos based on pointer actions\r\n        const pointerObserver = scene.onPointerObservable.add((pointerInfo) => {\r\n            if (!this.usePointerToAttachGizmos) {\r\n                return;\r\n            }\r\n            if (pointerInfo.type == PointerEventTypes.POINTERDOWN) {\r\n                if (pointerInfo.pickInfo && pointerInfo.pickInfo.pickedMesh) {\r\n                    if (this.enableAutoPicking) {\r\n                        let node: Nullable<Node> = pointerInfo.pickInfo.pickedMesh;\r\n                        if (this.attachableMeshes == null) {\r\n                            // Attach to the most parent node\r\n                            while (node && node.parent != null) {\r\n                                node = node.parent;\r\n                            }\r\n                        } else {\r\n                            // Attach to the parent node that is an attachableMesh\r\n                            let found = false;\r\n                            for (const mesh of this.attachableMeshes) {\r\n                                if (node && (node == mesh || node.isDescendantOf(mesh))) {\r\n                                    node = mesh;\r\n                                    found = true;\r\n                                }\r\n                            }\r\n                            if (!found) {\r\n                                node = null;\r\n                            }\r\n                        }\r\n                        if (node instanceof AbstractMesh) {\r\n                            if (this._attachedMesh != node) {\r\n                                this.attachToMesh(node);\r\n                            }\r\n                        } else {\r\n                            if (this.clearGizmoOnEmptyPointerEvent) {\r\n                                this.attachToMesh(null);\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    if (this.clearGizmoOnEmptyPointerEvent) {\r\n                        this.attachToMesh(null);\r\n                    }\r\n                }\r\n            }\r\n        });\r\n        return pointerObserver;\r\n    }\r\n\r\n    /**\r\n     * Attaches a set of gizmos to the specified mesh\r\n     * @param mesh The mesh the gizmo's should be attached to\r\n     */\r\n    public attachToMesh(mesh: Nullable<AbstractMesh>) {\r\n        if (this._attachedMesh) {\r\n            this._attachedMesh.removeBehavior(this.boundingBoxDragBehavior);\r\n        }\r\n        if (this._attachedNode) {\r\n            this._attachedNode.removeBehavior(this.boundingBoxDragBehavior);\r\n        }\r\n        this._attachedMesh = mesh;\r\n        this._attachedNode = null;\r\n        for (const key in this.gizmos) {\r\n            const gizmo = <Nullable<IGizmo>>(<any>this.gizmos)[key];\r\n            if (gizmo && (<any>this._gizmosEnabled)[key]) {\r\n                gizmo.attachedMesh = mesh;\r\n            }\r\n        }\r\n        if (this.boundingBoxGizmoEnabled && this._attachedMesh) {\r\n            this._attachedMesh.addBehavior(this.boundingBoxDragBehavior);\r\n        }\r\n        this.onAttachedToMeshObservable.notifyObservers(mesh);\r\n    }\r\n\r\n    /**\r\n     * Attaches a set of gizmos to the specified node\r\n     * @param node The node the gizmo's should be attached to\r\n     */\r\n    public attachToNode(node: Nullable<Node>) {\r\n        if (this._attachedMesh) {\r\n            this._attachedMesh.removeBehavior(this.boundingBoxDragBehavior);\r\n        }\r\n        if (this._attachedNode) {\r\n            this._attachedNode.removeBehavior(this.boundingBoxDragBehavior);\r\n        }\r\n        this._attachedMesh = null;\r\n        this._attachedNode = node;\r\n        for (const key in this.gizmos) {\r\n            const gizmo = <Nullable<IGizmo>>(<any>this.gizmos)[key];\r\n            if (gizmo && (<any>this._gizmosEnabled)[key]) {\r\n                gizmo.attachedNode = node;\r\n            }\r\n        }\r\n        if (this.boundingBoxGizmoEnabled && this._attachedNode) {\r\n            this._attachedNode.addBehavior(this.boundingBoxDragBehavior);\r\n        }\r\n        this.onAttachedToNodeObservable.notifyObservers(node);\r\n    }\r\n\r\n    /**\r\n     * If the position gizmo is enabled\r\n     */\r\n    public set positionGizmoEnabled(value: boolean) {\r\n        if (value) {\r\n            if (!this.gizmos.positionGizmo) {\r\n                this.gizmos.positionGizmo = new PositionGizmo(this._defaultUtilityLayer, this._thickness, this);\r\n            }\r\n            if (this._attachedNode) {\r\n                this.gizmos.positionGizmo.attachedNode = this._attachedNode;\r\n            } else {\r\n                this.gizmos.positionGizmo.attachedMesh = this._attachedMesh;\r\n            }\r\n        } else if (this.gizmos.positionGizmo) {\r\n            this.gizmos.positionGizmo.attachedNode = null;\r\n        }\r\n        this._gizmosEnabled.positionGizmo = value;\r\n        this._setAdditionalTransformNode();\r\n    }\r\n    public get positionGizmoEnabled(): boolean {\r\n        return this._gizmosEnabled.positionGizmo;\r\n    }\r\n    /**\r\n     * If the rotation gizmo is enabled\r\n     */\r\n    public set rotationGizmoEnabled(value: boolean) {\r\n        if (value) {\r\n            if (!this.gizmos.rotationGizmo) {\r\n                this.gizmos.rotationGizmo = new RotationGizmo(this._defaultUtilityLayer, 32, false, this._thickness, this);\r\n            }\r\n            if (this._attachedNode) {\r\n                this.gizmos.rotationGizmo.attachedNode = this._attachedNode;\r\n            } else {\r\n                this.gizmos.rotationGizmo.attachedMesh = this._attachedMesh;\r\n            }\r\n        } else if (this.gizmos.rotationGizmo) {\r\n            this.gizmos.rotationGizmo.attachedNode = null;\r\n        }\r\n        this._gizmosEnabled.rotationGizmo = value;\r\n        this._setAdditionalTransformNode();\r\n    }\r\n    public get rotationGizmoEnabled(): boolean {\r\n        return this._gizmosEnabled.rotationGizmo;\r\n    }\r\n    /**\r\n     * If the scale gizmo is enabled\r\n     */\r\n    public set scaleGizmoEnabled(value: boolean) {\r\n        if (value) {\r\n            this.gizmos.scaleGizmo = this.gizmos.scaleGizmo || new ScaleGizmo(this._defaultUtilityLayer, this._thickness, this);\r\n            if (this._attachedNode) {\r\n                this.gizmos.scaleGizmo.attachedNode = this._attachedNode;\r\n            } else {\r\n                this.gizmos.scaleGizmo.attachedMesh = this._attachedMesh;\r\n            }\r\n        } else if (this.gizmos.scaleGizmo) {\r\n            this.gizmos.scaleGizmo.attachedNode = null;\r\n        }\r\n        this._gizmosEnabled.scaleGizmo = value;\r\n        this._setAdditionalTransformNode();\r\n    }\r\n    public get scaleGizmoEnabled(): boolean {\r\n        return this._gizmosEnabled.scaleGizmo;\r\n    }\r\n    /**\r\n     * If the boundingBox gizmo is enabled\r\n     */\r\n    public set boundingBoxGizmoEnabled(value: boolean) {\r\n        if (value) {\r\n            this.gizmos.boundingBoxGizmo = this.gizmos.boundingBoxGizmo || new BoundingBoxGizmo(this._boundingBoxColor, this._defaultKeepDepthUtilityLayer);\r\n            if (this._attachedMesh) {\r\n                this.gizmos.boundingBoxGizmo.attachedMesh = this._attachedMesh;\r\n            } else {\r\n                this.gizmos.boundingBoxGizmo.attachedNode = this._attachedNode;\r\n            }\r\n\r\n            if (this._attachedMesh) {\r\n                this._attachedMesh.removeBehavior(this.boundingBoxDragBehavior);\r\n                this._attachedMesh.addBehavior(this.boundingBoxDragBehavior);\r\n            } else if (this._attachedNode) {\r\n                this._attachedNode.removeBehavior(this.boundingBoxDragBehavior);\r\n                this._attachedNode.addBehavior(this.boundingBoxDragBehavior);\r\n            }\r\n        } else if (this.gizmos.boundingBoxGizmo) {\r\n            if (this._attachedMesh) {\r\n                this._attachedMesh.removeBehavior(this.boundingBoxDragBehavior);\r\n            } else if (this._attachedNode) {\r\n                this._attachedNode.removeBehavior(this.boundingBoxDragBehavior);\r\n            }\r\n            this.gizmos.boundingBoxGizmo.attachedNode = null;\r\n        }\r\n        this._gizmosEnabled.boundingBoxGizmo = value;\r\n        this._setAdditionalTransformNode();\r\n    }\r\n    public get boundingBoxGizmoEnabled(): boolean {\r\n        return this._gizmosEnabled.boundingBoxGizmo;\r\n    }\r\n\r\n    /**\r\n     * Sets the additional transform applied to all the gizmos.\r\n     * @See Gizmo.additionalTransformNode for more detail\r\n     */\r\n    public set additionalTransformNode(node: TransformNode | undefined) {\r\n        this._additionalTransformNode = node;\r\n        this._setAdditionalTransformNode();\r\n    }\r\n\r\n    private _setAdditionalTransformNode() {\r\n        for (const key in this.gizmos) {\r\n            const gizmo = <Nullable<IGizmo>>(<any>this.gizmos)[key];\r\n            if (gizmo && (<any>this._gizmosEnabled)[key]) {\r\n                gizmo.additionalTransformNode = this._additionalTransformNode;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Builds Gizmo Axis Cache to enable features such as hover state preservation and graying out other axis during manipulation\r\n     * @param gizmoAxisCache Gizmo axis definition used for reactive gizmo UI\r\n     */\r\n    public addToAxisCache(gizmoAxisCache: Map<Mesh, GizmoAxisCache>) {\r\n        if (gizmoAxisCache.size > 0) {\r\n            gizmoAxisCache.forEach((v, k) => {\r\n                this._gizmoAxisCache.set(k, v);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Force release the drag action by code\r\n     */\r\n    public releaseDrag() {\r\n        const gizmos = [this.gizmos.positionGizmo, this.gizmos.rotationGizmo, this.gizmos.scaleGizmo, this.gizmos.boundingBoxGizmo];\r\n        for (const gizmo of gizmos) {\r\n            gizmo?.releaseDrag();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes of the gizmo manager\r\n     */\r\n    public dispose() {\r\n        for (const observer of this._pointerObservers) {\r\n            this._scene.onPointerObservable.remove(observer);\r\n        }\r\n        for (const key in this.gizmos) {\r\n            const gizmo = <Nullable<IGizmo>>(<any>this.gizmos)[key];\r\n            if (gizmo) {\r\n                gizmo.dispose();\r\n            }\r\n        }\r\n        if (this._defaultKeepDepthUtilityLayer !== UtilityLayerRenderer._DefaultKeepDepthUtilityLayer) {\r\n            this._defaultKeepDepthUtilityLayer?.dispose();\r\n        }\r\n        if (this._defaultUtilityLayer !== UtilityLayerRenderer._DefaultUtilityLayer) {\r\n            this._defaultUtilityLayer?.dispose();\r\n        }\r\n        this.boundingBoxDragBehavior.detach();\r\n        this.onAttachedToMeshObservable.clear();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAG5D,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAEtD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,wCAAwC,CAAC;AAE5E,OAAO,EAAE,KAAK,EAAwB,MAAM,SAAS,CAAC;AAEtD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;;;;;AAMhD,MAAO,YAAY;IAsDrB;;OAEG,CACH,IAAW,qBAAqB,GAAA;QAC5B,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC5B,MAAM,KAAK,GAA2B,IAAI,CAAC,MAAO,CAAC,GAAG,CAAC,CAAC;YACxD,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBAC3B,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACV,CAAC;QACL,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM,CAAC,aAAa;YAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;SAAC,CAAC;QAC5H,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC5B,QAAQ,GAAG,IAAI,CAAC;YACpB,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM,CAAC,aAAa;YAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YAAE,IAAI,CAAC,MAAM,CAAC,UAAU;SAAC,CAAC;QAC9F,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;YAC7B,CAAC;QACL,CAAC;IACL,CAAC;IACD,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;OAIG,CACH,IAAW,eAAe,CAAC,eAAqC,EAAA;QAC5D,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM,CAAC,aAAa;YAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YAAE,IAAI,CAAC,MAAM,CAAC,UAAU;SAAC,CAAC;QAC9F,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC;YAC5C,CAAC;QACL,CAAC;IACL,CAAC;IAED,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IA0BD;;;;;OAKG,CACK,4BAA4B,CAAC,KAAY,EAAA;QAC7C,sDAAsD;QACtD,MAAM,eAAe,GAAG,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YAClE,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBACjC,OAAO;YACX,CAAC;YACD,IAAI,WAAW,CAAC,IAAI,sKAAI,oBAAiB,CAAC,WAAW,EAAE,CAAC;gBACpD,IAAI,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;oBAC1D,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACzB,IAAI,IAAI,GAAmB,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC;wBAC3D,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;4BAChC,iCAAiC;4BACjC,MAAO,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAE,CAAC;gCACjC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;4BACvB,CAAC;wBACL,CAAC,MAAM,CAAC;4BACJ,sDAAsD;4BACtD,IAAI,KAAK,GAAG,KAAK,CAAC;4BAClB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAE,CAAC;gCACvC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oCACtD,IAAI,GAAG,IAAI,CAAC;oCACZ,KAAK,GAAG,IAAI,CAAC;gCACjB,CAAC;4BACL,CAAC;4BACD,IAAI,CAAC,KAAK,EAAE,CAAC;gCACT,IAAI,GAAG,IAAI,CAAC;4BAChB,CAAC;wBACL,CAAC;wBACD,IAAI,IAAI,6KAAY,eAAY,EAAE,CAAC;4BAC/B,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;gCAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;4BAC5B,CAAC;wBACL,CAAC,MAAM,CAAC;4BACJ,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;gCACrC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;4BAC5B,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;wBACrC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBAC5B,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACI,YAAY,CAAC,IAA4B,EAAA;QAC5C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC5B,MAAM,KAAK,GAA2B,IAAI,CAAC,MAAO,CAAC,GAAG,CAAC,CAAC;YACxD,IAAI,KAAK,IAAU,IAAI,CAAC,cAAe,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3C,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;;OAGG,CACI,YAAY,CAAC,IAAoB,EAAA;QACpC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC5B,MAAM,KAAK,GAA2B,IAAI,CAAC,MAAO,CAAC,GAAG,CAAC,CAAC;YACxD,IAAI,KAAK,IAAU,IAAI,CAAC,cAAe,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3C,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YAC9B,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,CAAC,KAAc,EAAA;QAC1C,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,sKAAI,gBAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YACpG,CAAC;YACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YAChE,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YAChE,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC;QAClD,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,KAAK,CAAC;QAC1C,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IACD,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;IAC7C,CAAC;IACD;;OAEG,CACH,IAAW,oBAAoB,CAAC,KAAc,EAAA;QAC1C,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,sKAAI,gBAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAC/G,CAAC;YACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YAChE,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YAChE,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC;QAClD,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,KAAK,CAAC;QAC1C,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IACD,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;IAC7C,CAAC;IACD;;OAEG,CACH,IAAW,iBAAiB,CAAC,KAAc,EAAA;QACvC,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,mKAAI,aAAU,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YACpH,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YAC7D,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YAC7D,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;QAC/C,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IACD,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;IAC1C,CAAC;IACD;;OAEG,CACH,IAAW,uBAAuB,CAAC,KAAc,EAAA;QAC7C,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,wKAAI,oBAAgB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAChJ,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YACnE,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YACnE,CAAC;YAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAChE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACjE,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC5B,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAChE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACjE,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACtC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACpE,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC5B,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACpE,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,CAAC;QACrD,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC7C,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IACD,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC;IAChD,CAAC;IAED;;;OAGG,CACH,IAAW,uBAAuB,CAAC,IAA+B,EAAA;QAC9D,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAEO,2BAA2B,GAAA;QAC/B,IAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC5B,MAAM,KAAK,GAA2B,IAAI,CAAC,MAAO,CAAC,GAAG,CAAC,CAAC;YACxD,IAAI,KAAK,IAAU,IAAI,CAAC,cAAe,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3C,KAAK,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,CAAC;YAClE,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,cAAc,CAAC,cAAyC,EAAA;QAC3D,IAAI,cAAc,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC1B,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;OAEG,CACI,WAAW,GAAA;QACd,MAAM,MAAM,GAAG;YAAC,IAAI,CAAC,MAAM,CAAC,aAAa;YAAE,IAAI,CAAC,MAAM,CAAC,aAAa;YAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB;SAAC,CAAC;QAC5H,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;0DACzB,KAAK,CAAE,WAAW,EAAE,CAAC;QACzB,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC;QACD,IAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAE,CAAC;YAC5B,MAAM,KAAK,GAA2B,IAAI,CAAC,MAAO,CAAC,GAAG,CAAC,CAAC;YACxD,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,6BAA6B,iLAAK,uBAAoB,CAAC,6BAA6B,EAAE,CAAC;;uDACxF,CAAC,6BAA6B,wEAAlC,oCAAoC,OAAO,EAAE,CAAC;QAClD,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,iLAAK,uBAAoB,CAAC,oBAAoB,EAAE,CAAC;;8CACtE,CAAC,oBAAoB,+DAAzB,2BAA2B,OAAO,EAAE,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,CAAC;QACtC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;IAC5C,CAAC;IA/RD;;;;;;OAMG,CACH,YACY,MAAa,EACrB,YAAoB,CAAC,EACrB,2LAAqC,uBAAoB,CAAC,mBAAmB,EAC7E,oMAA8C,uBAAoB,CAAC,4BAA4B,CAAA;QAHvF,IAAA,CAAA,MAAM,GAAN,MAAM,CAAO;QAxJzB,uHAAA,EAAyH,CAClH,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QAE7C,iHAAA,EAAmH,CAC5G,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAEhC,0DAAA,EAA4D,CACrD,IAAA,CAAA,0BAA0B,GAAG,gKAAI,cAAU,EAA0B,CAAC;QAE7E,0DAAA,EAA4D,CACrD,IAAA,CAAA,0BAA0B,GAAG,iKAAI,aAAU,EAAkB,CAAC;QAE3D,IAAA,CAAA,cAAc,GAAG;YAAE,aAAa,EAAE,KAAK;YAAE,aAAa,EAAE,KAAK;YAAE,UAAU,EAAE,KAAK;YAAE,gBAAgB,EAAE,KAAK;QAAA,CAAE,CAAC;QAC5G,IAAA,CAAA,iBAAiB,GAA4B,EAAE,CAAC;QAChD,IAAA,CAAA,aAAa,GAA2B,IAAI,CAAC;QAC7C,IAAA,CAAA,aAAa,GAAmB,IAAI,CAAC;QACrC,IAAA,CAAA,iBAAiB,oKAAG,SAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAGpD,IAAA,CAAA,UAAU,GAAW,CAAC,CAAC;QACvB,IAAA,CAAA,WAAW,GAAW,CAAC,CAAC;QACxB,IAAA,CAAA,gBAAgB,GAAA,EAAA,8BAAA,GAA8B;QAGxD,kCAAA,EAAoC,CAC5B,IAAA,CAAA,eAAe,GAA8B,IAAI,GAAG,EAAE,CAAC;QAC/D;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAG,uLAAI,sBAAkB,EAAE,CAAC;QAC1D;;WAEG,CACI,IAAA,CAAA,gBAAgB,GAAkC,IAAI,CAAC;QAC9D;;WAEG,CACI,IAAA,CAAA,eAAe,GAA0B,IAAI,CAAC;QACrD;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,IAAI,CAAC;QAoHnC,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;QACzC,IAAI,CAAC,6BAA6B,GAAG,qBAAqB,CAAC;QAC3D,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,CAAC,wBAAwB,GAAG,KAAK,CAAC;QACtF,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG;YAAE,aAAa,EAAE,IAAI;YAAE,aAAa,EAAE,IAAI;YAAE,UAAU,EAAE,IAAI;YAAE,gBAAgB,EAAE,IAAI;QAAA,CAAE,CAAC;QAErG,MAAM,2BAA2B,GAAG,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;QAC9E,MAAM,wBAAwB,4JAAG,SAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACjH,IAAI,CAAC,iBAAiB,GAAG;YAAC,2BAA2B;YAAE,wBAAwB;SAAC,CAAC;IACrF,CAAC;CA0QJ", "debugId": null}}, {"offset": {"line": 4110, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/lightGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/lightGizmo.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Vector3, <PERSON>uatern<PERSON>, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport type { IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport type { Node } from \"../node\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { Light } from \"../Lights/light\";\r\nimport type { Scene } from \"../scene\";\r\nimport { HemisphericLight } from \"../Lights/hemisphericLight\";\r\nimport { DirectionalLight } from \"../Lights/directionalLight\";\r\nimport { CreateSphere } from \"../Meshes/Builders/sphereBuilder\";\r\nimport { CreateHemisphere } from \"../Meshes/Builders/hemisphereBuilder\";\r\nimport { SpotLight } from \"../Lights/spotLight\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../Events/pointerEvents\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { CreateCylinder } from \"../Meshes/Builders/cylinderBuilder\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Interface for light gizmo\r\n */\r\nexport interface ILightGizmo extends IGizmo {\r\n    /** Event that fires each time the gizmo is clicked */\r\n    onClickedObservable: Observable<Light>;\r\n    /** The light that the gizmo is attached to */\r\n    light: Nullable<Light>;\r\n    /** The material used to render the light gizmo */\r\n    readonly material: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Gizmo that enables viewing a light\r\n */\r\nexport class LightGizmo extends Gizmo implements ILightGizmo {\r\n    protected _lightMesh: Mesh;\r\n    protected _material: StandardMaterial;\r\n    protected _cachedPosition = new Vector3();\r\n    protected _cachedForward = new Vector3(0, 0, 1);\r\n    protected _attachedMeshParent: TransformNode;\r\n    protected _pointerObserver: Nullable<Observer<PointerInfo>> = null;\r\n\r\n    /**\r\n     * Event that fires each time the gizmo is clicked\r\n     */\r\n    public onClickedObservable = new Observable<Light>();\r\n\r\n    /**\r\n     * Creates a LightGizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     */\r\n    constructor(gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer) {\r\n        super(gizmoLayer);\r\n        this.attachedMesh = new Mesh(\"\", this.gizmoLayer.utilityLayerScene);\r\n        this._attachedMeshParent = new TransformNode(\"parent\", this.gizmoLayer.utilityLayerScene);\r\n\r\n        this.attachedMesh.parent = this._attachedMeshParent;\r\n        this._material = new StandardMaterial(\"light\", this.gizmoLayer.utilityLayerScene);\r\n        this._material.diffuseColor = new Color3(0.5, 0.5, 0.5);\r\n        this._material.specularColor = new Color3(0.1, 0.1, 0.1);\r\n\r\n        this._pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (!this._light) {\r\n                return;\r\n            }\r\n\r\n            this._isHovered = !!(pointerInfo.pickInfo && this._rootMesh.getChildMeshes().indexOf(<Mesh>pointerInfo.pickInfo.pickedMesh) != -1);\r\n            if (this._isHovered && pointerInfo.event.button === 0) {\r\n                this.onClickedObservable.notifyObservers(this._light);\r\n            }\r\n        }, PointerEventTypes.POINTERDOWN);\r\n    }\r\n    protected _light: Nullable<Light> = null;\r\n\r\n    /**\r\n     * Override attachedNode because lightgizmo only support attached mesh\r\n     * It will return the attached mesh (if any) and setting an attached node will log\r\n     * a warning\r\n     */\r\n    public override get attachedNode() {\r\n        return this.attachedMesh;\r\n    }\r\n    public override set attachedNode(value: Nullable<Node>) {\r\n        Logger.Warn(\"Nodes cannot be attached to LightGizmo. Attach to a mesh instead.\");\r\n    }\r\n\r\n    /**\r\n     * The light that the gizmo is attached to\r\n     */\r\n    public set light(light: Nullable<Light>) {\r\n        this._light = light;\r\n        if (light) {\r\n            // Create the mesh for the given light type\r\n            if (this._lightMesh) {\r\n                this._lightMesh.dispose();\r\n            }\r\n\r\n            if (light instanceof HemisphericLight) {\r\n                this._lightMesh = LightGizmo._CreateHemisphericLightMesh(this.gizmoLayer.utilityLayerScene);\r\n            } else if (light instanceof DirectionalLight) {\r\n                this._lightMesh = LightGizmo._CreateDirectionalLightMesh(this.gizmoLayer.utilityLayerScene);\r\n            } else if (light instanceof SpotLight) {\r\n                this._lightMesh = LightGizmo._CreateSpotLightMesh(this.gizmoLayer.utilityLayerScene);\r\n            } else {\r\n                this._lightMesh = LightGizmo._CreatePointLightMesh(this.gizmoLayer.utilityLayerScene);\r\n            }\r\n            const children = this._lightMesh.getChildMeshes(false);\r\n            for (const m of children) {\r\n                m.material = this._material;\r\n            }\r\n            this._lightMesh.parent = this._rootMesh;\r\n\r\n            // Add lighting to the light gizmo\r\n            const gizmoLight = this.gizmoLayer._getSharedGizmoLight();\r\n            gizmoLight.includedOnlyMeshes = gizmoLight.includedOnlyMeshes.concat(this._lightMesh.getChildMeshes(false));\r\n\r\n            this._lightMesh.rotationQuaternion = new Quaternion();\r\n\r\n            if (!this.attachedMesh!.reservedDataStore) {\r\n                this.attachedMesh!.reservedDataStore = {};\r\n            }\r\n            this.attachedMesh!.reservedDataStore.lightGizmo = this;\r\n\r\n            if (light.parent) {\r\n                this._attachedMeshParent.freezeWorldMatrix(light.parent.getWorldMatrix());\r\n            }\r\n\r\n            // Get update position and direction if the light has it\r\n            if ((light as any).position) {\r\n                this.attachedMesh!.position.copyFrom((light as any).position);\r\n                this.attachedMesh!.computeWorldMatrix(true);\r\n                this._cachedPosition.copyFrom(this.attachedMesh!.position);\r\n            }\r\n            if ((light as any).direction) {\r\n                this.attachedMesh!.setDirection((light as any).direction);\r\n                this.attachedMesh!.computeWorldMatrix(true);\r\n                const forward = this._getMeshForward();\r\n                this._cachedForward.copyFrom(forward);\r\n            }\r\n\r\n            this._update();\r\n        }\r\n    }\r\n    public get light() {\r\n        return this._light;\r\n    }\r\n\r\n    /**\r\n     * Gets the material used to render the light gizmo\r\n     */\r\n    public get material() {\r\n        return this._material;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * returns mesh forward\r\n     */\r\n    protected _getMeshForward(): Vector3 {\r\n        let forward = this.attachedMesh!.forward;\r\n        if (this.attachedMesh!.getScene().useRightHandedSystem) {\r\n            forward.negateToRef(TmpVectors.Vector3[0]);\r\n            forward = TmpVectors.Vector3[0];\r\n        }\r\n        return forward;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Updates the gizmo to match the attached mesh's position/rotation\r\n     */\r\n    protected override _update() {\r\n        super._update();\r\n        if (!this._light) {\r\n            return;\r\n        }\r\n\r\n        if (this._light.parent) {\r\n            this._attachedMeshParent.freezeWorldMatrix(this._light.parent.getWorldMatrix());\r\n        }\r\n\r\n        // For light position and direction, a dirty flag is set to true in the setter\r\n        // It means setting values individually or copying values will not call setter and\r\n        // dirty flag will not be set to true. Hence creating a new Vector3.\r\n        if ((this._light as any).position) {\r\n            // If the gizmo is moved update the light otherwise update the gizmo to match the light\r\n            if (!this.attachedMesh!.position.equals(this._cachedPosition)) {\r\n                // update light to match gizmo\r\n                const position = this.attachedMesh!.position;\r\n                (this._light as any).position = new Vector3(position.x, position.y, position.z);\r\n                this._cachedPosition.copyFrom(this.attachedMesh!.position);\r\n            } else {\r\n                // update gizmo to match light\r\n                this.attachedMesh!.position.copyFrom((this._light as any).position);\r\n                this.attachedMesh!.computeWorldMatrix(true);\r\n                this._cachedPosition.copyFrom(this.attachedMesh!.position);\r\n            }\r\n        }\r\n        if ((this._light as any).direction) {\r\n            // If the gizmo is moved update the light otherwise update the gizmo to match the light\r\n            const forward = this._getMeshForward();\r\n            if (Vector3.DistanceSquared(forward, this._cachedForward) > 0.0001) {\r\n                // update light to match gizmo\r\n                const direction = forward;\r\n                (this._light as any).direction = new Vector3(direction.x, direction.y, direction.z);\r\n                this._cachedForward.copyFrom(forward);\r\n            } else if (Vector3.DistanceSquared(forward, (this._light as any).direction) > 0.0001) {\r\n                // update gizmo to match light\r\n                this.attachedMesh!.setDirection((this._light as any).direction);\r\n                this.attachedMesh!.computeWorldMatrix(true);\r\n                this._cachedForward.copyFrom(forward);\r\n            }\r\n        }\r\n    }\r\n\r\n    // Static helper methods\r\n    private static _Scale = 0.007;\r\n\r\n    /**\r\n     * Creates the lines for a light mesh\r\n     * @param levels\r\n     * @param scene\r\n     * @returns the light lines mesh\r\n     */\r\n    private static _CreateLightLines = (levels: number, scene: Scene) => {\r\n        const distFromSphere = 1.2;\r\n\r\n        const root = new Mesh(\"root\", scene);\r\n        root.rotation.x = Math.PI / 2;\r\n\r\n        // Create the top line, this will be cloned for all other lines\r\n        const linePivot = new Mesh(\"linePivot\", scene);\r\n        linePivot.parent = root;\r\n        const line = CreateCylinder(\r\n            \"line\",\r\n            {\r\n                updatable: false,\r\n                height: 2,\r\n                diameterTop: 0.2,\r\n                diameterBottom: 0.3,\r\n                tessellation: 6,\r\n                subdivisions: 1,\r\n            },\r\n            scene\r\n        );\r\n        line.position.y = line.scaling.y / 2 + distFromSphere;\r\n        line.parent = linePivot;\r\n\r\n        if (levels < 2) {\r\n            return linePivot;\r\n        }\r\n        for (let i = 0; i < 4; i++) {\r\n            const l = linePivot.clone(\"lineParentClone\");\r\n            l.rotation.z = Math.PI / 4;\r\n            l.rotation.y = Math.PI / 2 + (Math.PI / 2) * i;\r\n\r\n            l.getChildMeshes()[0].scaling.y = 0.5;\r\n            l.getChildMeshes()[0].scaling.x = l.getChildMeshes()[0].scaling.z = 0.8;\r\n            l.getChildMeshes()[0].position.y = l.getChildMeshes()[0].scaling.y / 2 + distFromSphere;\r\n        }\r\n\r\n        if (levels < 3) {\r\n            return root;\r\n        }\r\n        for (let i = 0; i < 4; i++) {\r\n            const l = linePivot.clone(\"linePivotClone\");\r\n            l.rotation.z = Math.PI / 2;\r\n            l.rotation.y = (Math.PI / 2) * i;\r\n        }\r\n\r\n        if (levels < 4) {\r\n            return root;\r\n        }\r\n        for (let i = 0; i < 4; i++) {\r\n            const l = linePivot.clone(\"linePivotClone\");\r\n            l.rotation.z = Math.PI + Math.PI / 4;\r\n            l.rotation.y = Math.PI / 2 + (Math.PI / 2) * i;\r\n\r\n            l.getChildMeshes()[0].scaling.y = 0.5;\r\n            l.getChildMeshes()[0].scaling.x = l.getChildMeshes()[0].scaling.z = 0.8;\r\n            l.getChildMeshes()[0].position.y = l.getChildMeshes()[0].scaling.y / 2 + distFromSphere;\r\n        }\r\n\r\n        if (levels < 5) {\r\n            return root;\r\n        }\r\n        const l = linePivot.clone(\"linePivotClone\");\r\n        l.rotation.z = Math.PI;\r\n\r\n        return root;\r\n    };\r\n\r\n    /**\r\n     * Disposes of the light gizmo\r\n     */\r\n    public override dispose() {\r\n        this.onClickedObservable.clear();\r\n        this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(this._pointerObserver);\r\n        this._material.dispose();\r\n        super.dispose();\r\n        this._attachedMeshParent.dispose();\r\n    }\r\n\r\n    private static _CreateHemisphericLightMesh(scene: Scene) {\r\n        const root = new Mesh(\"hemisphereLight\", scene);\r\n        const hemisphere = CreateHemisphere(root.name, { segments: 10, diameter: 1 }, scene);\r\n        hemisphere.position.z = -0.15;\r\n        hemisphere.rotation.x = Math.PI / 2;\r\n        hemisphere.parent = root;\r\n\r\n        const lines = this._CreateLightLines(3, scene);\r\n        lines.parent = root;\r\n\r\n        root.scaling.scaleInPlace(LightGizmo._Scale);\r\n        root.rotation.x = Math.PI / 2;\r\n\r\n        return root;\r\n    }\r\n\r\n    private static _CreatePointLightMesh(scene: Scene) {\r\n        const root = new Mesh(\"pointLight\", scene);\r\n        const sphere = CreateSphere(root.name, { segments: 10, diameter: 1 }, scene);\r\n        sphere.rotation.x = Math.PI / 2;\r\n        sphere.parent = root;\r\n\r\n        const lines = this._CreateLightLines(5, scene);\r\n        lines.parent = root;\r\n        root.scaling.scaleInPlace(LightGizmo._Scale);\r\n        root.rotation.x = Math.PI / 2;\r\n\r\n        return root;\r\n    }\r\n\r\n    private static _CreateSpotLightMesh(scene: Scene) {\r\n        const root = new Mesh(\"spotLight\", scene);\r\n        const sphere = CreateSphere(root.name, { segments: 10, diameter: 1 }, scene);\r\n        sphere.parent = root;\r\n\r\n        const hemisphere = CreateHemisphere(root.name, { segments: 10, diameter: 2 }, scene);\r\n        hemisphere.parent = root;\r\n        hemisphere.rotation.x = -Math.PI / 2;\r\n\r\n        const lines = this._CreateLightLines(2, scene);\r\n        lines.parent = root;\r\n        root.scaling.scaleInPlace(LightGizmo._Scale);\r\n        root.rotation.x = Math.PI / 2;\r\n\r\n        return root;\r\n    }\r\n\r\n    private static _CreateDirectionalLightMesh(scene: Scene) {\r\n        const root = new Mesh(\"directionalLight\", scene);\r\n\r\n        const mesh = new Mesh(root.name, scene);\r\n        mesh.parent = root;\r\n        const sphere = CreateSphere(root.name, { diameter: 1.2, segments: 10 }, scene);\r\n        sphere.parent = mesh;\r\n\r\n        const line = CreateCylinder(\r\n            root.name,\r\n            {\r\n                updatable: false,\r\n                height: 6,\r\n                diameterTop: 0.3,\r\n                diameterBottom: 0.3,\r\n                tessellation: 6,\r\n                subdivisions: 1,\r\n            },\r\n            scene\r\n        );\r\n        line.parent = mesh;\r\n\r\n        let left = line.clone(root.name);\r\n        left.scaling.y = 0.5;\r\n        left.position.x += 1.25;\r\n\r\n        let right = line.clone(root.name);\r\n        right.scaling.y = 0.5;\r\n        right.position.x += -1.25;\r\n\r\n        const arrowHead = CreateCylinder(\r\n            root.name,\r\n            {\r\n                updatable: false,\r\n                height: 1,\r\n                diameterTop: 0,\r\n                diameterBottom: 0.6,\r\n                tessellation: 6,\r\n                subdivisions: 1,\r\n            },\r\n            scene\r\n        );\r\n        arrowHead.position.y += 3;\r\n        arrowHead.parent = mesh;\r\n\r\n        left = arrowHead.clone(root.name);\r\n        left.position.y = 1.5;\r\n        left.position.x += 1.25;\r\n\r\n        right = arrowHead.clone(root.name);\r\n        right.position.y = 1.5;\r\n        right.position.x += -1.25;\r\n\r\n        mesh.scaling.scaleInPlace(LightGizmo._Scale);\r\n        mesh.rotation.z = Math.PI / 2;\r\n        mesh.rotation.y = Math.PI / 2;\r\n        return root;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACvE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAEtC,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAGjE,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAC9D,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAChE,OAAO,EAAE,gBAAgB,EAAE,MAAM,sCAAsC,CAAC;AACxE,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,MAAM,oCAAoC,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,0BAAyB;;;;;;;;;;;;;;;;;AAiBpC,MAAO,UAAW,mKAAQ,QAAK;IAwCjC;;;;OAIG,CACH,IAAoB,YAAY,GAAA;QAC5B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD,IAAoB,YAAY,CAAC,KAAqB,EAAA;iKAClD,SAAM,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;IACrF,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,CAAC,KAAsB,EAAA;QACnC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,KAAK,EAAE,CAAC;YACR,2CAA2C;YAC3C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC;YAED,IAAI,KAAK,iLAAY,mBAAgB,EAAE,CAAC;gBACpC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,2BAA2B,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAChG,CAAC,MAAM,IAAI,KAAK,iLAAY,mBAAgB,EAAE,CAAC;gBAC3C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,2BAA2B,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAChG,CAAC,MAAM,IAAI,KAAK,0KAAY,YAAS,EAAE,CAAC;gBACpC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YACzF,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAC1F,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACvD,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;gBACvB,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;YAExC,kCAAkC;YAClC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAC1D,UAAU,CAAC,kBAAkB,GAAG,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;YAE5G,IAAI,CAAC,UAAU,CAAC,kBAAkB,GAAG,sKAAI,aAAU,EAAE,CAAC;YAEtD,IAAI,CAAC,IAAI,CAAC,YAAa,CAAC,iBAAiB,EAAE,CAAC;gBACxC,IAAI,CAAC,YAAa,CAAC,iBAAiB,GAAG,CAAA,CAAE,CAAC;YAC9C,CAAC;YACD,IAAI,CAAC,YAAa,CAAC,iBAAiB,CAAC,UAAU,GAAG,IAAI,CAAC;YAEvD,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;gBACf,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;YAC9E,CAAC;YAED,wDAAwD;YACxD,IAAK,KAAa,CAAC,QAAQ,EAAE,CAAC;gBAC1B,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,QAAQ,CAAE,KAAa,CAAC,QAAQ,CAAC,CAAC;gBAC9D,IAAI,CAAC,YAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,CAAC;YAC/D,CAAC;YACD,IAAK,KAAa,CAAC,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,YAAa,CAAC,YAAY,CAAE,KAAa,CAAC,SAAS,CAAC,CAAC;gBAC1D,IAAI,CAAC,YAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC1C,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACL,CAAC;IACD,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG,CACO,eAAe,GAAA;QACrB,IAAI,OAAO,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC;QACzC,IAAI,IAAI,CAAC,YAAa,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,CAAC;YACrD,OAAO,CAAC,WAAW,mKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,OAAO,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG,CACgB,OAAO,GAAA;QACtB,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACrB,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC;QACpF,CAAC;QAED,8EAA8E;QAC9E,kFAAkF;QAClF,oEAAoE;QACpE,IAAK,IAAI,CAAC,MAAc,CAAC,QAAQ,EAAE,CAAC;YAChC,uFAAuF;YACvF,IAAI,CAAC,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC5D,8BAA8B;gBAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC;gBAC5C,IAAI,CAAC,MAAc,CAAC,QAAQ,GAAG,IAAI,4KAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAChF,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,CAAC;YAC/D,CAAC,MAAM,CAAC;gBACJ,8BAA8B;gBAC9B,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,QAAQ,CAAE,IAAI,CAAC,MAAc,CAAC,QAAQ,CAAC,CAAC;gBACpE,IAAI,CAAC,YAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC,CAAC;YAC/D,CAAC;QACL,CAAC;QACD,IAAK,IAAI,CAAC,MAAc,CAAC,SAAS,EAAE,CAAC;YACjC,uFAAuF;YACvF,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YACvC,sKAAI,UAAO,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,MAAM,EAAE,CAAC;gBACjE,8BAA8B;gBAC9B,MAAM,SAAS,GAAG,OAAO,CAAC;gBACzB,IAAI,CAAC,MAAc,CAAC,SAAS,GAAG,qKAAI,WAAO,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;gBACpF,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC1C,CAAC,MAAM,qKAAI,WAAO,CAAC,eAAe,CAAC,OAAO,EAAG,IAAI,CAAC,MAAc,CAAC,SAAS,CAAC,GAAG,MAAM,EAAE,CAAC;gBACnF,8BAA8B;gBAC9B,IAAI,CAAC,YAAa,CAAC,YAAY,CAAE,IAAI,CAAC,MAAc,CAAC,SAAS,CAAC,CAAC;gBAChE,IAAI,CAAC,YAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC5C,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;IACL,CAAC;IA+ED;;OAEG,CACa,OAAO,GAAA;QACnB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;IACvC,CAAC;IAEO,MAAM,CAAC,2BAA2B,CAAC,KAAY,EAAA;QACnD,MAAM,IAAI,GAAG,6JAAI,OAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,UAAU,yLAAG,mBAAgB,AAAhB,EAAiB,IAAI,CAAC,IAAI,EAAE;YAAE,QAAQ,EAAE,EAAE;YAAE,QAAQ,EAAE,CAAC;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QACrF,UAAU,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;QAC9B,UAAU,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACpC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;QAEzB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAE9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,KAAY,EAAA;QAC7C,MAAM,IAAI,GAAG,6JAAI,OAAI,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAC3C,MAAM,MAAM,qLAAG,eAAA,AAAY,EAAC,IAAI,CAAC,IAAI,EAAE;YAAE,QAAQ,EAAE,EAAE;YAAE,QAAQ,EAAE,CAAC;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QAC7E,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAChC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QAErB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAE9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,KAAY,EAAA;QAC5C,MAAM,IAAI,GAAG,6JAAI,OAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAC1C,MAAM,MAAM,qLAAG,eAAY,AAAZ,EAAa,IAAI,CAAC,IAAI,EAAE;YAAE,QAAQ,EAAE,EAAE;YAAE,QAAQ,EAAE,CAAC;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QAC7E,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QAErB,MAAM,UAAU,yLAAG,mBAAA,AAAgB,EAAC,IAAI,CAAC,IAAI,EAAE;YAAE,QAAQ,EAAE,EAAE;YAAE,QAAQ,EAAE,CAAC;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QACrF,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;QACzB,UAAU,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAErC,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAC/C,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAE9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,2BAA2B,CAAC,KAAY,EAAA;QACnD,MAAM,IAAI,GAAG,IAAI,gKAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QAEjD,MAAM,IAAI,GAAG,6JAAI,OAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,MAAM,MAAM,qLAAG,eAAA,AAAY,EAAC,IAAI,CAAC,IAAI,EAAE;YAAE,QAAQ,EAAE,GAAG;YAAE,QAAQ,EAAE,EAAE;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QAC/E,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;QAErB,MAAM,IAAI,OAAG,iMAAA,AAAc,EACvB,IAAI,CAAC,IAAI,EACT;YACI,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,CAAC;YACT,WAAW,EAAE,GAAG;YAChB,cAAc,EAAE,GAAG;YACnB,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;SAClB,EACD,KAAK,CACR,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC;QACrB,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC;QAExB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC;QACtB,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAE1B,MAAM,SAAS,uLAAG,iBAAA,AAAc,EAC5B,IAAI,CAAC,IAAI,EACT;YACI,SAAS,EAAE,KAAK;YAChB,MAAM,EAAE,CAAC;YACT,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,GAAG;YACnB,YAAY,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;SAClB,EACD,KAAK,CACR,CAAC;QACF,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;QAC1B,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;QAExB,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC;QAExB,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;QACvB,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAE1B,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAxWD;;;OAGG,CACH,YAAY,yLAAmC,uBAAoB,CAAC,mBAAmB,CAAA;QACnF,KAAK,CAAC,UAAU,CAAC,CAAC;QAfZ,IAAA,CAAA,eAAe,GAAG,sKAAI,UAAO,EAAE,CAAC;QAChC,IAAA,CAAA,cAAc,GAAG,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtC,IAAA,CAAA,gBAAgB,GAAoC,IAAI,CAAC;QAEnE;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,iKAAI,aAAU,EAAS,CAAC;QA2B3C,IAAA,CAAA,MAAM,GAAoB,IAAI,CAAC;QAnBrC,IAAI,CAAC,YAAY,GAAG,6JAAI,OAAI,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QACpE,IAAI,CAAC,mBAAmB,GAAG,sKAAI,gBAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAE1F,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACpD,IAAI,CAAC,SAAS,GAAG,4KAAI,mBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAClF,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAEzD,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACzF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACf,OAAO;YACX,CAAC;YAED,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,OAAO,CAAO,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnI,IAAI,IAAI,CAAC,UAAU,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpD,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1D,CAAC;QACL,CAAC,oKAAE,oBAAiB,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;;AAgJD,wBAAwB;AACT,WAAA,MAAM,GAAG,KAAK,AAAR,CAAS;AAE9B;;;;;GAKG,CACY,WAAA,iBAAiB,GAAG,CAAC,MAAc,EAAE,KAAY,EAAE,EAAE;IAChE,MAAM,cAAc,GAAG,GAAG,CAAC;IAE3B,MAAM,IAAI,GAAG,6JAAI,OAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACrC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;IAE9B,+DAA+D;IAC/D,MAAM,SAAS,GAAG,IAAI,gKAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAC/C,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;IACxB,MAAM,IAAI,uLAAG,iBAAc,AAAd,EACT,MAAM,EACN;QACI,SAAS,EAAE,KAAK;QAChB,MAAM,EAAE,CAAC;QACT,WAAW,EAAE,GAAG;QAChB,cAAc,EAAE,GAAG;QACnB,YAAY,EAAE,CAAC;QACf,YAAY,EAAE,CAAC;KAClB,EACD,KAAK,CACR,CAAC;IACF,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;IACtD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAExB,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;QACb,OAAO,SAAS,CAAC;IACrB,CAAC;IACD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;QACzB,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC7C,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3B,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAG,CAAC,CAAC;QAE/C,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC;QACtC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC;QACxE,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;IAC5F,CAAC;IAED,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;QACzB,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QAC3B,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAI,AAAD,IAAK,CAAC,EAAE,GAAG,CAAC,CAAC,EAAG,CAAC,CAAC;IACrC,CAAC;IAED,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;QACzB,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QACrC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAG,CAAC,CAAC;QAE/C,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC;QACtC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC;QACxE,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;IAC5F,CAAC;IAED,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IAEvB,OAAO,IAAI,CAAC;AAChB,CAAC,AAlE+B,CAkE9B", "debugId": null}}, {"offset": {"line": 4464, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/cameraGizmo.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/cameraGizmo.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport type { IGizmo } from \"./gizmo\";\r\nimport { Gizmo } from \"./gizmo\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { CreateBox } from \"../Meshes/Builders/boxBuilder\";\r\nimport { CreateCylinder } from \"../Meshes/Builders/cylinderBuilder\";\r\nimport { Matrix } from \"../Maths/math\";\r\nimport { CreateLines } from \"../Meshes/Builders/linesBuilder\";\r\nimport type { PointerInfo } from \"../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../Events/pointerEvents\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\n\r\n/**\r\n * Interface for camera gizmo\r\n */\r\nexport interface ICameraGizmo extends IGizmo {\r\n    /** Event that fires each time the gizmo is clicked */\r\n    onClickedObservable: Observable<Camera>;\r\n    /** A boolean indicating if frustum lines must be rendered */\r\n    displayFrustum: boolean;\r\n    /** The camera that the gizmo is attached to */\r\n    camera: Nullable<Camera>;\r\n    /** The material used to render the camera gizmo */\r\n    readonly material: StandardMaterial;\r\n}\r\n\r\n/**\r\n * Gizmo that enables viewing a camera\r\n */\r\nexport class CameraGizmo extends Gizmo implements ICameraGizmo {\r\n    protected _cameraMesh: Mesh;\r\n    protected _cameraLinesMesh: Mesh;\r\n    protected _material: StandardMaterial;\r\n    protected _pointerObserver: Nullable<Observer<PointerInfo>> = null;\r\n    private _frustumLinesColor?: Color3;\r\n\r\n    /**\r\n     * Event that fires each time the gizmo is clicked\r\n     */\r\n    public onClickedObservable = new Observable<Camera>();\r\n\r\n    /**\r\n     * Creates a CameraGizmo\r\n     * @param gizmoLayer The utility layer the gizmo will be added to\r\n     * @param gizmoColor Camera mesh color. Default is Gray\r\n     * @param frustumLinesColor Frustum lines color. Default is White\r\n     */\r\n    constructor(gizmoLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer, gizmoColor?: Color3, frustumLinesColor?: Color3) {\r\n        super(gizmoLayer);\r\n\r\n        this._material = new StandardMaterial(\"cameraGizmoMaterial\", this.gizmoLayer.utilityLayerScene);\r\n        this._frustumLinesColor = frustumLinesColor;\r\n\r\n        this._material.diffuseColor = gizmoColor ?? new Color3(0.5, 0.5, 0.5);\r\n        this._material.specularColor = new Color3(0.1, 0.1, 0.1);\r\n\r\n        this._pointerObserver = gizmoLayer.utilityLayerScene.onPointerObservable.add((pointerInfo) => {\r\n            if (!this._camera) {\r\n                return;\r\n            }\r\n\r\n            this._isHovered = !!(pointerInfo.pickInfo && this._rootMesh.getChildMeshes().indexOf(<Mesh>pointerInfo.pickInfo.pickedMesh) != -1);\r\n            if (this._isHovered && pointerInfo.event.button === 0) {\r\n                this.onClickedObservable.notifyObservers(this._camera);\r\n            }\r\n        }, PointerEventTypes.POINTERDOWN);\r\n    }\r\n    protected _camera: Nullable<Camera> = null;\r\n\r\n    /** Gets or sets a boolean indicating if frustum lines must be rendered (true by default)) */\r\n    public get displayFrustum() {\r\n        return this._cameraLinesMesh.isEnabled();\r\n    }\r\n    public set displayFrustum(value) {\r\n        this._cameraLinesMesh.setEnabled(value);\r\n    }\r\n\r\n    /**\r\n     * The camera that the gizmo is attached to\r\n     */\r\n    public set camera(camera: Nullable<Camera>) {\r\n        this._camera = camera;\r\n        this.attachedNode = camera;\r\n        if (camera) {\r\n            // Create the mesh for the given camera\r\n            if (!this._customMeshSet) {\r\n                if (this._cameraMesh) {\r\n                    this._cameraMesh.dispose();\r\n                }\r\n                this._cameraMesh = CameraGizmo._CreateCameraMesh(this.gizmoLayer.utilityLayerScene);\r\n\r\n                const childMeshes = this._cameraMesh.getChildMeshes(false);\r\n                for (const m of childMeshes) {\r\n                    m.material = this._material;\r\n                }\r\n                this._cameraMesh.parent = this._rootMesh;\r\n            }\r\n\r\n            if (this._cameraLinesMesh) {\r\n                this._cameraLinesMesh.dispose();\r\n            }\r\n            const linesColor = this._frustumLinesColor?.toColor4(1) ?? new Color4(1, 1, 1, 1);\r\n            this._cameraLinesMesh = CameraGizmo._CreateCameraFrustum(this.gizmoLayer.utilityLayerScene, linesColor);\r\n            this._cameraLinesMesh.parent = this._rootMesh;\r\n\r\n            if (\r\n                this.gizmoLayer.utilityLayerScene.activeCamera &&\r\n                this.gizmoLayer.utilityLayerScene.activeCamera != camera &&\r\n                this.gizmoLayer.utilityLayerScene.activeCamera.maxZ < camera.maxZ\r\n            ) {\r\n                this.gizmoLayer.utilityLayerScene.activeCamera.maxZ = camera.maxZ;\r\n            }\r\n\r\n            if (!this.attachedNode!.reservedDataStore) {\r\n                this.attachedNode!.reservedDataStore = {};\r\n            }\r\n            this.attachedNode!.reservedDataStore.cameraGizmo = this;\r\n\r\n            // Add lighting to the camera gizmo\r\n            const gizmoLight = this.gizmoLayer._getSharedGizmoLight();\r\n            gizmoLight.includedOnlyMeshes = gizmoLight.includedOnlyMeshes.concat(this._cameraMesh.getChildMeshes(false));\r\n\r\n            this._update();\r\n        }\r\n    }\r\n\r\n    public get camera() {\r\n        return this._camera;\r\n    }\r\n\r\n    /**\r\n     * Gets the material used to render the camera gizmo\r\n     */\r\n    public get material() {\r\n        return this._material;\r\n    }\r\n    /**\r\n     * @internal\r\n     * Updates the gizmo to match the attached mesh's position/rotation\r\n     */\r\n\r\n    protected override _update() {\r\n        super._update();\r\n        if (!this._camera) {\r\n            return;\r\n        }\r\n\r\n        // frustum matrix\r\n        this._camera.getProjectionMatrix().invertToRef(this._invProjection);\r\n        this._cameraLinesMesh.setPivotMatrix(this._invProjection, false);\r\n\r\n        this._cameraLinesMesh.scaling.x = 1 / this._rootMesh.scaling.x;\r\n        this._cameraLinesMesh.scaling.y = 1 / this._rootMesh.scaling.y;\r\n        this._cameraLinesMesh.scaling.z = 1 / this._rootMesh.scaling.z;\r\n\r\n        // take care of coordinate system in camera scene to properly display the mesh with the good Y axis orientation in this scene\r\n        this._cameraMesh.parent = null;\r\n        this._cameraMesh.rotation.y = Math.PI * 0.5 * (this._camera.getScene().useRightHandedSystem ? 1 : -1);\r\n        this._cameraMesh.parent = this._rootMesh;\r\n    }\r\n\r\n    // Static helper methods\r\n    private static _Scale = 0.05;\r\n    private _invProjection = new Matrix();\r\n\r\n    /**\r\n     * Disposes and replaces the current camera mesh in the gizmo with the specified mesh\r\n     * @param mesh The mesh to replace the default mesh of the camera gizmo\r\n     */\r\n    public override setCustomMesh(mesh: Mesh) {\r\n        if (mesh.getScene() != this.gizmoLayer.utilityLayerScene) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"When setting a custom mesh on a gizmo, the custom meshes scene must be the same as the gizmos (eg. gizmo.gizmoLayer.utilityLayerScene)\";\r\n        }\r\n        if (this._cameraMesh) {\r\n            this._cameraMesh.dispose();\r\n        }\r\n        this._cameraMesh = mesh;\r\n        this._cameraMesh.parent = this._rootMesh;\r\n        this._customMeshSet = true;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the camera gizmo\r\n     */\r\n    public override dispose() {\r\n        this.onClickedObservable.clear();\r\n        this.gizmoLayer.utilityLayerScene.onPointerObservable.remove(this._pointerObserver);\r\n        if (this._cameraMesh) {\r\n            this._cameraMesh.dispose();\r\n        }\r\n        if (this._cameraLinesMesh) {\r\n            this._cameraLinesMesh.dispose();\r\n        }\r\n        this._material.dispose();\r\n        super.dispose();\r\n    }\r\n\r\n    private static _CreateCameraMesh(scene: Scene) {\r\n        const root = new Mesh(\"rootCameraGizmo\", scene);\r\n\r\n        const mesh = new Mesh(root.name, scene);\r\n        mesh.parent = root;\r\n\r\n        const box = CreateBox(root.name, { width: 1.0, height: 0.8, depth: 0.5 }, scene);\r\n        box.parent = mesh;\r\n\r\n        const cyl1 = CreateCylinder(root.name, { height: 0.5, diameterTop: 0.8, diameterBottom: 0.8 }, scene);\r\n        cyl1.parent = mesh;\r\n        cyl1.position.y = 0.3;\r\n        cyl1.position.x = -0.6;\r\n        cyl1.rotation.x = Math.PI * 0.5;\r\n\r\n        const cyl2 = CreateCylinder(root.name, { height: 0.5, diameterTop: 0.6, diameterBottom: 0.6 }, scene);\r\n        cyl2.parent = mesh;\r\n        cyl2.position.y = 0.5;\r\n        cyl2.position.x = 0.4;\r\n        cyl2.rotation.x = Math.PI * 0.5;\r\n\r\n        const cyl3 = CreateCylinder(root.name, { height: 0.5, diameterTop: 0.5, diameterBottom: 0.5 }, scene);\r\n        cyl3.parent = mesh;\r\n        cyl3.position.y = 0.0;\r\n        cyl3.position.x = 0.6;\r\n        cyl3.rotation.z = Math.PI * 0.5;\r\n\r\n        root.scaling.scaleInPlace(CameraGizmo._Scale);\r\n        mesh.position.x = -0.9;\r\n\r\n        return root;\r\n    }\r\n\r\n    private static _CreateCameraFrustum(scene: Scene, linesColor: Color4) {\r\n        const root = new Mesh(\"rootCameraGizmo\", scene);\r\n        const mesh = new Mesh(root.name, scene);\r\n        mesh.parent = root;\r\n\r\n        for (let y = 0; y < 4; y += 2) {\r\n            for (let x = 0; x < 4; x += 2) {\r\n                let line = CreateLines(\"lines\", { points: [new Vector3(-1 + x, -1 + y, -1), new Vector3(-1 + x, -1 + y, 1)], colors: [linesColor, linesColor] }, scene);\r\n                line.parent = mesh;\r\n                line.alwaysSelectAsActiveMesh = true;\r\n                line.isPickable = false;\r\n                line = CreateLines(\"lines\", { points: [new Vector3(-1, -1 + x, -1 + y), new Vector3(1, -1 + x, -1 + y)], colors: [linesColor, linesColor] }, scene);\r\n                line.parent = mesh;\r\n                line.alwaysSelectAsActiveMesh = true;\r\n                line.isPickable = false;\r\n                line = CreateLines(\"lines\", { points: [new Vector3(-1 + x, -1, -1 + y), new Vector3(-1 + x, 1, -1 + y)], colors: [linesColor, linesColor] }, scene);\r\n                line.parent = mesh;\r\n                line.alwaysSelectAsActiveMesh = true;\r\n                line.isPickable = false;\r\n            }\r\n        }\r\n\r\n        return root;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAEtC,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAGjE,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,EAAE,cAAc,EAAE,MAAM,oCAAoC,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AACvC,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAE9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;;;;;;AAmB1C,MAAO,WAAY,mKAAQ,QAAK;IAwClC,2FAAA,EAA6F,CAC7F,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;IAC7C,CAAC;IACD,IAAW,cAAc,CAAC,KAAK,EAAA;QAC3B,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,CAAC,MAAwB,EAAA;QACtC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,MAAM,EAAE,CAAC;;YACT,uCAAuC;YACvC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC/B,CAAC;gBACD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBAEpF,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC3D,KAAK,MAAM,CAAC,IAAI,WAAW,CAAE,CAAC;oBAC1B,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAChC,CAAC;gBACD,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;YAC7C,CAAC;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;YACpC,CAAC;;YACD,MAAM,UAAU,wEAAO,CAAC,kBAAkB,sFAAE,QAAQ,CAAC,CAAC,CAAC,6DAApC,oCAAwC,IAAI,0KAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAClF,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;YACxG,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;YAE9C,IACI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,IAC9C,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,IAAI,MAAM,IACxD,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,EACnE,CAAC;gBACC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;YACtE,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,YAAa,CAAC,iBAAiB,EAAE,CAAC;gBACxC,IAAI,CAAC,YAAa,CAAC,iBAAiB,GAAG,CAAA,CAAE,CAAC;YAC9C,CAAC;YACD,IAAI,CAAC,YAAa,CAAC,iBAAiB,CAAC,WAAW,GAAG,IAAI,CAAC;YAExD,mCAAmC;YACnC,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;YAC1D,UAAU,CAAC,kBAAkB,GAAG,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;YAE7G,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACL,CAAC;IAED,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;;OAGG,CAEgB,OAAO,GAAA;QACtB,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpE,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAEjE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QAE/D,6HAA6H;QAC7H,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;IAC7C,CAAC;IAMD;;;OAGG,CACa,aAAa,CAAC,IAAU,EAAA;QACpC,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACvD,4CAA4C;YAC5C,MAAM,wIAAwI,CAAC;QACnJ,CAAC;QACD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpF,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC/B,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,KAAY,EAAA;QACzC,MAAM,IAAI,GAAG,6JAAI,OAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAEhD,MAAM,IAAI,GAAG,6JAAI,OAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,MAAM,GAAG,kLAAG,YAAA,AAAS,EAAC,IAAI,CAAC,IAAI,EAAE;YAAE,KAAK,EAAE,GAAG;YAAE,MAAM,EAAE,GAAG;YAAE,KAAK,EAAE,GAAG;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QACjF,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC;QAElB,MAAM,IAAI,uLAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,IAAI,EAAE;YAAE,MAAM,EAAE,GAAG;YAAE,WAAW,EAAE,GAAG;YAAE,cAAc,EAAE,GAAG;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QACtG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAEhC,MAAM,IAAI,uLAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,IAAI,EAAE;YAAE,MAAM,EAAE,GAAG;YAAE,WAAW,EAAE,GAAG;YAAE,cAAc,EAAE,GAAG;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QACtG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAEhC,MAAM,IAAI,IAAG,oMAAA,AAAc,EAAC,IAAI,CAAC,IAAI,EAAE;YAAE,MAAM,EAAE,GAAG;YAAE,WAAW,EAAE,GAAG;YAAE,cAAc,EAAE,GAAG;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QACtG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAEhC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAEvB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,KAAY,EAAE,UAAkB,EAAA;QAChE,MAAM,IAAI,GAAG,IAAI,gKAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,IAAI,GAAG,6JAAI,OAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;YAC5B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;gBAC5B,IAAI,IAAI,GAAG,+LAAA,AAAW,EAAC,OAAO,EAAE;oBAAE,MAAM,EAAE;wBAAC,sKAAI,UAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;wBAAE,sKAAI,UAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;qBAAC;oBAAE,MAAM,EAAE;wBAAC,UAAU;wBAAE,UAAU;qBAAC;gBAAA,CAAE,EAAE,KAAK,CAAC,CAAC;gBACxJ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;gBACrC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBACxB,IAAI,oLAAG,cAAA,AAAW,EAAC,OAAO,EAAE;oBAAE,MAAM,EAAE;wBAAC,sKAAI,UAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;wBAAE,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;qBAAC;oBAAE,MAAM,EAAE;wBAAC,UAAU;wBAAE,UAAU;qBAAC;gBAAA,CAAE,EAAE,KAAK,CAAC,CAAC;gBACpJ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;gBACrC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBACxB,IAAI,oLAAG,cAAA,AAAW,EAAC,OAAO,EAAE;oBAAE,MAAM,EAAE;wBAAC,sKAAI,UAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;wBAAE,sKAAI,UAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;qBAAC;oBAAE,MAAM,EAAE;wBAAC,UAAU;wBAAE,UAAU;qBAAC;gBAAA,CAAE,EAAE,KAAK,CAAC,CAAC;gBACpJ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;gBACrC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YAC5B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IArND;;;;;OAKG,CACH,YAAY,yLAAmC,uBAAoB,CAAC,mBAAmB,EAAE,UAAmB,EAAE,iBAA0B,CAAA;QACpI,KAAK,CAAC,UAAU,CAAC,CAAC;QAfZ,IAAA,CAAA,gBAAgB,GAAoC,IAAI,CAAC;QAGnE;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,iKAAI,aAAU,EAAU,CAAC;QA4B5C,IAAA,CAAA,OAAO,GAAqB,IAAI,CAAC;QAgGnC,IAAA,CAAA,cAAc,GAAG,IAAI,2KAAM,EAAE,CAAC;QAjHlC,IAAI,CAAC,SAAS,GAAG,4KAAI,mBAAgB,CAAC,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAChG,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAE5C,IAAI,CAAC,SAAS,CAAC,YAAY,kDAAG,UAAU,GAAI,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACtE,IAAI,CAAC,SAAS,CAAC,aAAa,GAAG,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAEzD,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACzF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,OAAO;YACX,CAAC;YAED,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,OAAO,CAAO,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACnI,IAAI,IAAI,CAAC,UAAU,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpD,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC,oKAAE,oBAAiB,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;;AA+FD,wBAAwB;AACT,YAAA,MAAM,GAAG,IAAI,AAAP,CAAQ", "debugId": null}}, {"offset": {"line": 4717, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Gizmos/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Gizmos/index.ts"], "sourcesContent": ["export * from \"./axisDragGizmo\";\r\nexport * from \"./axisScaleGizmo\";\r\nexport * from \"./boundingBoxGizmo\";\r\nexport * from \"./gizmo\";\r\nexport * from \"./gizmoManager\";\r\nexport * from \"./planeRotationGizmo\";\r\nexport * from \"./positionGizmo\";\r\nexport * from \"./rotationGizmo\";\r\nexport * from \"./scaleGizmo\";\r\nexport * from \"./lightGizmo\";\r\nexport * from \"./cameraGizmo\";\r\nexport * from \"./planeDragGizmo\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,iBAAiB,CAAC;AAChC,cAAc,kBAAkB,CAAC;AACjC,cAAc,oBAAoB,CAAC;AACnC,cAAc,SAAS,CAAC;AACxB,cAAc,gBAAgB,CAAC;AAC/B,cAAc,sBAAsB,CAAC;AACrC,cAAc,iBAAiB,CAAC;AAChC,cAAc,iBAAiB,CAAC;AAChC,cAAc,cAAc,CAAC;AAC7B,cAAc,cAAc,CAAC;AAC7B,cAAc,eAAe,CAAC;AAC9B,cAAc,kBAAkB,CAAC", "debugId": null}}]}