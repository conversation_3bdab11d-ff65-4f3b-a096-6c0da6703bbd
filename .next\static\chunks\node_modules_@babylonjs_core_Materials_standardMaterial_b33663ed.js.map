{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/standardMaterial.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/standardMaterial.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serialize, serializeAsColor3, expandToProperty, serializeAsFresnelParameters, serializeAsTexture } from \"../Misc/decorators\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { SmartArray } from \"../Misc/smartArray\";\r\nimport type { IAnimatable } from \"../Animations/animatable.interface\";\r\n\r\nimport type { Nullable } from \"../types\";\r\nimport { Scene } from \"../scene\";\r\nimport type { Matrix } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { PrePassConfiguration } from \"./prePassConfiguration\";\r\n\r\nimport type { IImageProcessingConfigurationDefines } from \"./imageProcessingConfiguration.defines\";\r\nimport { ImageProcessingConfiguration } from \"./imageProcessingConfiguration\";\r\nimport type { ColorCurves } from \"./colorCurves\";\r\nimport type { FresnelParameters } from \"./fresnelParameters\";\r\nimport type { ICustomShaderNameResolveOptions } from \"../Materials/material\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { MaterialPluginEvent } from \"./materialPluginEvent\";\r\nimport { MaterialDefines } from \"../Materials/materialDefines\";\r\nimport { PushMaterial } from \"./pushMaterial\";\r\n\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { CubeTexture } from \"../Materials/Textures/cubeTexture\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport { MaterialFlags } from \"./materialFlags\";\r\n\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { EffectFallbacks } from \"./effectFallbacks\";\r\nimport type { Effect, IEffectCreationOptions } from \"./effect\";\r\nimport { DetailMapConfiguration } from \"./material.detailMapConfiguration\";\r\nimport { AddClipPlaneUniforms, BindClipPlane } from \"./clipPlaneMaterialHelper\";\r\nimport {\r\n    BindBonesParameters,\r\n    BindFogParameters,\r\n    BindLights,\r\n    BindLogDepth,\r\n    BindMorphTargetParameters,\r\n    BindTextureMatrix,\r\n    HandleFallbacksForShadows,\r\n    PrepareAttributesForBakedVertexAnimation,\r\n    PrepareAttributesForBones,\r\n    PrepareAttributesForInstances,\r\n    PrepareAttributesForMorphTargets,\r\n    PrepareDefinesForAttributes,\r\n    PrepareDefinesForFrameBoundValues,\r\n    PrepareDefinesForLights,\r\n    PrepareDefinesForMergedUV,\r\n    PrepareDefinesForMisc,\r\n    PrepareDefinesForMultiview,\r\n    PrepareDefinesForOIT,\r\n    PrepareDefinesForPrePass,\r\n    PrepareUniformsAndSamplersList,\r\n} from \"./materialHelper.functions\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { ShaderLanguage } from \"./shaderLanguage\";\r\nimport { MaterialHelperGeometryRendering } from \"./materialHelper.geometryrendering\";\r\n\r\nconst onCreatedEffectParameters = { effect: null as unknown as Effect, subMesh: null as unknown as Nullable<SubMesh> };\r\n\r\n/** @internal */\r\nexport class StandardMaterialDefines extends MaterialDefines implements IImageProcessingConfigurationDefines {\r\n    public MAINUV1 = false;\r\n    public MAINUV2 = false;\r\n    public MAINUV3 = false;\r\n    public MAINUV4 = false;\r\n    public MAINUV5 = false;\r\n    public MAINUV6 = false;\r\n    public DIFFUSE = false;\r\n    public DIFFUSEDIRECTUV = 0;\r\n    public BAKED_VERTEX_ANIMATION_TEXTURE = false;\r\n    public AMBIENT = false;\r\n    public AMBIENTDIRECTUV = 0;\r\n    public OPACITY = false;\r\n    public OPACITYDIRECTUV = 0;\r\n    public OPACITYRGB = false;\r\n    public REFLECTION = false;\r\n    public EMISSIVE = false;\r\n    public EMISSIVEDIRECTUV = 0;\r\n    public SPECULAR = false;\r\n    public SPECULARDIRECTUV = 0;\r\n    public BUMP = false;\r\n    public BUMPDIRECTUV = 0;\r\n    public PARALLAX = false;\r\n    public PARALLAX_RHS = false;\r\n    public PARALLAXOCCLUSION = false;\r\n    public SPECULAROVERALPHA = false;\r\n    public CLIPPLANE = false;\r\n    public CLIPPLANE2 = false;\r\n    public CLIPPLANE3 = false;\r\n    public CLIPPLANE4 = false;\r\n    public CLIPPLANE5 = false;\r\n    public CLIPPLANE6 = false;\r\n    public ALPHATEST = false;\r\n    public DEPTHPREPASS = false;\r\n    public ALPHAFROMDIFFUSE = false;\r\n    public POINTSIZE = false;\r\n    public FOG = false;\r\n    public SPECULARTERM = false;\r\n    public DIFFUSEFRESNEL = false;\r\n    public OPACITYFRESNEL = false;\r\n    public REFLECTIONFRESNEL = false;\r\n    public REFRACTIONFRESNEL = false;\r\n    public EMISSIVEFRESNEL = false;\r\n    public FRESNEL = false;\r\n    public NORMAL = false;\r\n    public TANGENT = false;\r\n    public UV1 = false;\r\n    public UV2 = false;\r\n    public UV3 = false;\r\n    public UV4 = false;\r\n    public UV5 = false;\r\n    public UV6 = false;\r\n    public VERTEXCOLOR = false;\r\n    public VERTEXALPHA = false;\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    public BonesPerMesh = 0;\r\n    public BONETEXTURE = false;\r\n    public BONES_VELOCITY_ENABLED = false;\r\n    public INSTANCES = false;\r\n    public THIN_INSTANCES = false;\r\n    public INSTANCESCOLOR = false;\r\n    public GLOSSINESS = false;\r\n    public ROUGHNESS = false;\r\n    public EMISSIVEASILLUMINATION = false;\r\n    public LINKEMISSIVEWITHDIFFUSE = false;\r\n    public REFLECTIONFRESNELFROMSPECULAR = false;\r\n    public LIGHTMAP = false;\r\n    public LIGHTMAPDIRECTUV = 0;\r\n    public OBJECTSPACE_NORMALMAP = false;\r\n    public USELIGHTMAPASSHADOWMAP = false;\r\n    public REFLECTIONMAP_3D = false;\r\n    public REFLECTIONMAP_SPHERICAL = false;\r\n    public REFLECTIONMAP_PLANAR = false;\r\n    public REFLECTIONMAP_CUBIC = false;\r\n    public USE_LOCAL_REFLECTIONMAP_CUBIC = false;\r\n    public USE_LOCAL_REFRACTIONMAP_CUBIC = false;\r\n    public REFLECTIONMAP_PROJECTION = false;\r\n    public REFLECTIONMAP_SKYBOX = false;\r\n    public REFLECTIONMAP_EXPLICIT = false;\r\n    public REFLECTIONMAP_EQUIRECTANGULAR = false;\r\n    public REFLECTIONMAP_EQUIRECTANGULAR_FIXED = false;\r\n    public REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = false;\r\n    public REFLECTIONMAP_OPPOSITEZ = false;\r\n    public INVERTCUBICMAP = false;\r\n    public LOGARITHMICDEPTH = false;\r\n    public REFRACTION = false;\r\n    public REFRACTIONMAP_3D = false;\r\n    public REFLECTIONOVERALPHA = false;\r\n    public TWOSIDEDLIGHTING = false;\r\n    public SHADOWFLOAT = false;\r\n    public MORPHTARGETS = false;\r\n    public MORPHTARGETS_POSITION = false;\r\n    public MORPHTARGETS_NORMAL = false;\r\n    public MORPHTARGETS_TANGENT = false;\r\n    public MORPHTARGETS_UV = false;\r\n    public MORPHTARGETS_UV2 = false;\r\n    public MORPHTARGETS_COLOR = false;\r\n    public MORPHTARGETTEXTURE_HASPOSITIONS = false;\r\n    public MORPHTARGETTEXTURE_HASNORMALS = false;\r\n    public MORPHTARGETTEXTURE_HASTANGENTS = false;\r\n    public MORPHTARGETTEXTURE_HASUVS = false;\r\n    public MORPHTARGETTEXTURE_HASUV2S = false;\r\n    public MORPHTARGETTEXTURE_HASCOLORS = false;\r\n    public NUM_MORPH_INFLUENCERS = 0;\r\n    public MORPHTARGETS_TEXTURE = false;\r\n    public NONUNIFORMSCALING = false; // https://playground.babylonjs.com#V6DWIH\r\n    public PREMULTIPLYALPHA = false; // https://playground.babylonjs.com#LNVJJ7\r\n    public ALPHATEST_AFTERALLALPHACOMPUTATIONS = false;\r\n    public ALPHABLEND = true;\r\n\r\n    public PREPASS = false;\r\n    public PREPASS_COLOR = false;\r\n    public PREPASS_COLOR_INDEX = -1;\r\n    public PREPASS_IRRADIANCE = false;\r\n    public PREPASS_IRRADIANCE_INDEX = -1;\r\n    public PREPASS_ALBEDO = false;\r\n    public PREPASS_ALBEDO_INDEX = -1;\r\n    public PREPASS_ALBEDO_SQRT = false;\r\n    public PREPASS_ALBEDO_SQRT_INDEX = -1;\r\n    public PREPASS_DEPTH = false;\r\n    public PREPASS_DEPTH_INDEX = -1;\r\n    public PREPASS_SCREENSPACE_DEPTH = false;\r\n    public PREPASS_SCREENSPACE_DEPTH_INDEX = -1;\r\n    public PREPASS_NORMALIZED_VIEW_DEPTH = false;\r\n    public PREPASS_NORMALIZED_VIEW_DEPTH_INDEX = -1;\r\n    public PREPASS_NORMAL = false;\r\n    public PREPASS_NORMAL_INDEX = -1;\r\n    public PREPASS_NORMAL_WORLDSPACE = false;\r\n    public PREPASS_WORLD_NORMAL = false;\r\n    public PREPASS_WORLD_NORMAL_INDEX = -1;\r\n    public PREPASS_POSITION = false;\r\n    public PREPASS_POSITION_INDEX = -1;\r\n    public PREPASS_LOCAL_POSITION = false;\r\n    public PREPASS_LOCAL_POSITION_INDEX = -1;\r\n    public PREPASS_VELOCITY = false;\r\n    public PREPASS_VELOCITY_INDEX = -1;\r\n    public PREPASS_VELOCITY_LINEAR = false;\r\n    public PREPASS_VELOCITY_LINEAR_INDEX = -1;\r\n    public PREPASS_REFLECTIVITY = false;\r\n    public PREPASS_REFLECTIVITY_INDEX = -1;\r\n    public SCENE_MRT_COUNT = 0;\r\n\r\n    public RGBDLIGHTMAP = false;\r\n    public RGBDREFLECTION = false;\r\n    public RGBDREFRACTION = false;\r\n\r\n    public IMAGEPROCESSING = false;\r\n    public VIGNETTE = false;\r\n    public VIGNETTEBLENDMODEMULTIPLY = false;\r\n    public VIGNETTEBLENDMODEOPAQUE = false;\r\n    public TONEMAPPING = 0;\r\n    public CONTRAST = false;\r\n    public COLORCURVES = false;\r\n    public COLORGRADING = false;\r\n    public COLORGRADING3D = false;\r\n    public SAMPLER3DGREENDEPTH = false;\r\n    public SAMPLER3DBGRMAP = false;\r\n    public DITHER = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n    public MULTIVIEW = false;\r\n    public ORDER_INDEPENDENT_TRANSPARENCY = false;\r\n    public ORDER_INDEPENDENT_TRANSPARENCY_16BITS = false;\r\n    public CAMERA_ORTHOGRAPHIC = false;\r\n    public CAMERA_PERSPECTIVE = false;\r\n    public AREALIGHTSUPPORTED = true;\r\n\r\n    /**\r\n     * If the reflection texture on this material is in linear color space\r\n     * @internal\r\n     */\r\n    public IS_REFLECTION_LINEAR = false;\r\n    /**\r\n     * If the refraction texture on this material is in linear color space\r\n     * @internal\r\n     */\r\n    public IS_REFRACTION_LINEAR = false;\r\n    public EXPOSURE = false;\r\n\r\n    public DECAL_AFTER_DETAIL = false;\r\n\r\n    /**\r\n     * Initializes the Standard Material defines.\r\n     * @param externalProperties The external properties\r\n     */\r\n    constructor(externalProperties?: { [name: string]: { type: string; default: any } }) {\r\n        super(externalProperties);\r\n        this.rebuild();\r\n    }\r\n\r\n    public setReflectionMode(modeToEnable: string) {\r\n        const modes = [\r\n            \"REFLECTIONMAP_CUBIC\",\r\n            \"REFLECTIONMAP_EXPLICIT\",\r\n            \"REFLECTIONMAP_PLANAR\",\r\n            \"REFLECTIONMAP_PROJECTION\",\r\n            \"REFLECTIONMAP_PROJECTION\",\r\n            \"REFLECTIONMAP_SKYBOX\",\r\n            \"REFLECTIONMAP_SPHERICAL\",\r\n            \"REFLECTIONMAP_EQUIRECTANGULAR\",\r\n            \"REFLECTIONMAP_EQUIRECTANGULAR_FIXED\",\r\n            \"REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED\",\r\n        ];\r\n\r\n        for (const mode of modes) {\r\n            (<any>this)[mode] = mode === modeToEnable;\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * This is the default material used in Babylon. It is the best trade off between quality\r\n * and performances.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction\r\n */\r\nexport class StandardMaterial extends PushMaterial {\r\n    /**\r\n     * Force all the standard materials to compile to glsl even on WebGPU engines.\r\n     * False by default. This is mostly meant for backward compatibility.\r\n     */\r\n    public static ForceGLSL = false;\r\n\r\n    @serializeAsTexture(\"diffuseTexture\")\r\n    private _diffuseTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * The basic texture of the material as viewed under a light.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public diffuseTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"ambientTexture\")\r\n    private _ambientTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * AKA Occlusion Texture in other nomenclature, it helps adding baked shadows into your material.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public ambientTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"opacityTexture\")\r\n    private _opacityTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Define the transparency of the material from a texture.\r\n     * The final alpha value can be read either from the red channel (if texture.getAlphaFromRGB is false)\r\n     * or from the luminance or the current texel (if texture.getAlphaFromRGB is true)\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public opacityTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"reflectionTexture\")\r\n    private _reflectionTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Define the texture used to display the reflection.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#how-to-obtain-reflections-and-refractions\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectionTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"emissiveTexture\")\r\n    private _emissiveTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Define texture of the material as if self lit.\r\n     * This will be mixed in the final result even in the absence of light.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"specularTexture\")\r\n    private _specularTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Define how the color and intensity of the highlight given by the light in the material.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public specularTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"bumpTexture\")\r\n    private _bumpTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Bump mapping is a technique to simulate bump and dents on a rendered surface.\r\n     * These are made by creating a normal map from an image. The means to do this can be found on the web, a search for 'normal map generator' will bring up free and paid for methods of doing this.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/moreMaterials#bump-map\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public bumpTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"lightmapTexture\")\r\n    private _lightmapTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Complex lighting can be computationally expensive to compute at runtime.\r\n     * To save on computation, lightmaps may be used to store calculated lighting in a texture which will be applied to a given mesh.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction#lightmaps\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public lightmapTexture: Nullable<BaseTexture>;\r\n\r\n    @serializeAsTexture(\"refractionTexture\")\r\n    private _refractionTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Define the texture used to display the refraction.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#how-to-obtain-reflections-and-refractions\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public refractionTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * The color of the material lit by the environmental background lighting.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction#ambient-color-example\r\n     */\r\n    @serializeAsColor3(\"ambient\")\r\n    public ambientColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * The basic color of the material as viewed under a light.\r\n     */\r\n    @serializeAsColor3(\"diffuse\")\r\n    public diffuseColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * Define how the color and intensity of the highlight given by the light in the material.\r\n     */\r\n    @serializeAsColor3(\"specular\")\r\n    public specularColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * Define the color of the material as if self lit.\r\n     * This will be mixed in the final result even in the absence of light.\r\n     */\r\n    @serializeAsColor3(\"emissive\")\r\n    public emissiveColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * Defines how sharp are the highlights in the material.\r\n     * The bigger the value the sharper giving a more glossy feeling to the result.\r\n     * Reversely, the smaller the value the blurrier giving a more rough feeling to the result.\r\n     */\r\n    @serialize()\r\n    public specularPower = 64;\r\n\r\n    @serialize(\"useAlphaFromDiffuseTexture\")\r\n    private _useAlphaFromDiffuseTexture = false;\r\n    /**\r\n     * Does the transparency come from the diffuse texture alpha channel.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public useAlphaFromDiffuseTexture: boolean;\r\n\r\n    @serialize(\"useEmissiveAsIllumination\")\r\n    private _useEmissiveAsIllumination = false;\r\n    /**\r\n     * If true, the emissive value is added into the end result, otherwise it is multiplied in.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useEmissiveAsIllumination: boolean;\r\n\r\n    @serialize(\"linkEmissiveWithDiffuse\")\r\n    private _linkEmissiveWithDiffuse = false;\r\n    /**\r\n     * If true, some kind of energy conservation will prevent the end result to be more than 1 by reducing\r\n     * the emissive level when the final color is close to one.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public linkEmissiveWithDiffuse: boolean;\r\n\r\n    @serialize(\"useSpecularOverAlpha\")\r\n    private _useSpecularOverAlpha = false;\r\n    /**\r\n     * Specifies that the material will keep the specular highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good exemple of that. When sun reflects on it you can not see what is behind.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useSpecularOverAlpha: boolean;\r\n\r\n    @serialize(\"useReflectionOverAlpha\")\r\n    private _useReflectionOverAlpha = false;\r\n    /**\r\n     * Specifies that the material will keeps the reflection highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good exemple of that. When the street lights reflects on it you can not see what is behind.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useReflectionOverAlpha: boolean;\r\n\r\n    @serialize(\"disableLighting\")\r\n    private _disableLighting = false;\r\n    /**\r\n     * Does lights from the scene impacts this material.\r\n     * It can be a nice trick for performance to disable lighting on a fully emissive material.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public disableLighting: boolean;\r\n\r\n    @serialize(\"useObjectSpaceNormalMap\")\r\n    private _useObjectSpaceNormalMap = false;\r\n    /**\r\n     * Allows using an object space normal map (instead of tangent space).\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useObjectSpaceNormalMap: boolean;\r\n\r\n    @serialize(\"useParallax\")\r\n    private _useParallax = false;\r\n    /**\r\n     * Is parallax enabled or not.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/parallaxMapping\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useParallax: boolean;\r\n\r\n    @serialize(\"useParallaxOcclusion\")\r\n    private _useParallaxOcclusion = false;\r\n    /**\r\n     * Is parallax occlusion enabled or not.\r\n     * If true, the outcome is way more realistic than traditional Parallax but you can expect a performance hit that worthes consideration.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/parallaxMapping\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useParallaxOcclusion: boolean;\r\n\r\n    /**\r\n     * Apply a scaling factor that determine which \"depth\" the height map should reprensent. A value between 0.05 and 0.1 is reasonnable in Parallax, you can reach 0.2 using Parallax Occlusion.\r\n     */\r\n    @serialize()\r\n    public parallaxScaleBias = 0.05;\r\n\r\n    @serialize(\"roughness\")\r\n    private _roughness = 0;\r\n    /**\r\n     * Helps to define how blurry the reflections should appears in the material.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public roughness: number;\r\n\r\n    /**\r\n     * In case of refraction, define the value of the index of refraction.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#how-to-obtain-reflections-and-refractions\r\n     */\r\n    @serialize()\r\n    public indexOfRefraction = 0.98;\r\n\r\n    /**\r\n     * Invert the refraction texture alongside the y axis.\r\n     * It can be useful with procedural textures or probe for instance.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/reflectionTexture#how-to-obtain-reflections-and-refractions\r\n     */\r\n    @serialize()\r\n    public invertRefractionY = true;\r\n\r\n    /**\r\n     * Defines the alpha limits in alpha test mode.\r\n     */\r\n    @serialize()\r\n    public alphaCutOff = 0.4;\r\n\r\n    @serialize(\"useLightmapAsShadowmap\")\r\n    private _useLightmapAsShadowmap = false;\r\n    /**\r\n     * In case of light mapping, define whether the map contains light or shadow informations.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useLightmapAsShadowmap: boolean;\r\n\r\n    // Fresnel\r\n    @serializeAsFresnelParameters(\"diffuseFresnelParameters\")\r\n    private _diffuseFresnelParameters: FresnelParameters;\r\n    /**\r\n     * Define the diffuse fresnel parameters of the material.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/fresnelParameters\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\r\n    public diffuseFresnelParameters: FresnelParameters;\r\n\r\n    @serializeAsFresnelParameters(\"opacityFresnelParameters\")\r\n    private _opacityFresnelParameters: FresnelParameters;\r\n    /**\r\n     * Define the opacity fresnel parameters of the material.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/fresnelParameters\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsFresnelAndMiscDirty\")\r\n    public opacityFresnelParameters: FresnelParameters;\r\n\r\n    @serializeAsFresnelParameters(\"reflectionFresnelParameters\")\r\n    private _reflectionFresnelParameters: FresnelParameters;\r\n    /**\r\n     * Define the reflection fresnel parameters of the material.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/fresnelParameters\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\r\n    public reflectionFresnelParameters: FresnelParameters;\r\n\r\n    @serializeAsFresnelParameters(\"refractionFresnelParameters\")\r\n    private _refractionFresnelParameters: FresnelParameters;\r\n    /**\r\n     * Define the refraction fresnel parameters of the material.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/fresnelParameters\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\r\n    public refractionFresnelParameters: FresnelParameters;\r\n\r\n    @serializeAsFresnelParameters(\"emissiveFresnelParameters\")\r\n    private _emissiveFresnelParameters: FresnelParameters;\r\n    /**\r\n     * Define the emissive fresnel parameters of the material.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/fresnelParameters\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\r\n    public emissiveFresnelParameters: FresnelParameters;\r\n\r\n    @serialize(\"useReflectionFresnelFromSpecular\")\r\n    private _useReflectionFresnelFromSpecular = false;\r\n    /**\r\n     * If true automatically deducts the fresnels values from the material specularity.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/fresnelParameters\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsFresnelDirty\")\r\n    public useReflectionFresnelFromSpecular: boolean;\r\n\r\n    @serialize(\"useGlossinessFromSpecularMapAlpha\")\r\n    private _useGlossinessFromSpecularMapAlpha = false;\r\n    /**\r\n     * Defines if the glossiness/roughness of the material should be read from the specular map alpha channel\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useGlossinessFromSpecularMapAlpha: boolean;\r\n\r\n    @serialize(\"maxSimultaneousLights\")\r\n    private _maxSimultaneousLights = 4;\r\n    /**\r\n     * Defines the maximum number of lights that can be used in the material\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public maxSimultaneousLights: number;\r\n\r\n    @serialize(\"invertNormalMapX\")\r\n    private _invertNormalMapX = false;\r\n    /**\r\n     * If sets to true, x component of normal map value will invert (x = 1.0 - x).\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapX: boolean;\r\n\r\n    @serialize(\"invertNormalMapY\")\r\n    private _invertNormalMapY = false;\r\n    /**\r\n     * If sets to true, y component of normal map value will invert (y = 1.0 - y).\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapY: boolean;\r\n\r\n    @serialize(\"twoSidedLighting\")\r\n    private _twoSidedLighting = false;\r\n    /**\r\n     * If sets to true and backfaceCulling is false, normals will be flipped on the backside.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public twoSidedLighting: boolean;\r\n\r\n    @serialize(\"applyDecalMapAfterDetailMap\")\r\n    private _applyDecalMapAfterDetailMap = false;\r\n    /**\r\n     * If sets to true, the decal map will be applied after the detail map. Else, it is applied before (default: false)\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public applyDecalMapAfterDetailMap: boolean;\r\n\r\n    /**\r\n     * Default configuration related to image processing available in the standard Material.\r\n     */\r\n    protected _imageProcessingConfiguration: ImageProcessingConfiguration;\r\n\r\n    /**\r\n     * Gets the image processing configuration used either in this material.\r\n     */\r\n    public get imageProcessingConfiguration(): ImageProcessingConfiguration {\r\n        return this._imageProcessingConfiguration;\r\n    }\r\n\r\n    /**\r\n     * Sets the Default image processing configuration used either in the this material.\r\n     *\r\n     * If sets to null, the scene one is in use.\r\n     */\r\n    public set imageProcessingConfiguration(value: ImageProcessingConfiguration) {\r\n        this._attachImageProcessingConfiguration(value);\r\n\r\n        // Ensure the effect will be rebuilt.\r\n        this._markAllSubMeshesAsImageProcessingDirty();\r\n    }\r\n\r\n    /**\r\n     * Keep track of the image processing observer to allow dispose and replace.\r\n     */\r\n    private _imageProcessingObserver: Nullable<Observer<ImageProcessingConfiguration>>;\r\n\r\n    /**\r\n     * Attaches a new image processing configuration to the Standard Material.\r\n     * @param configuration\r\n     */\r\n    protected _attachImageProcessingConfiguration(configuration: Nullable<ImageProcessingConfiguration>): void {\r\n        if (configuration === this._imageProcessingConfiguration) {\r\n            return;\r\n        }\r\n\r\n        // Detaches observer\r\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n        }\r\n\r\n        // Pick the scene configuration if needed\r\n        if (!configuration) {\r\n            this._imageProcessingConfiguration = this.getScene().imageProcessingConfiguration;\r\n        } else {\r\n            this._imageProcessingConfiguration = configuration;\r\n        }\r\n\r\n        // Attaches observer\r\n        if (this._imageProcessingConfiguration) {\r\n            this._imageProcessingObserver = this._imageProcessingConfiguration.onUpdateParameters.add(() => {\r\n                this._markAllSubMeshesAsImageProcessingDirty();\r\n            });\r\n        }\r\n    }\r\n\r\n    private _shadersLoaded = false;\r\n\r\n    /**\r\n     * Defines additional PrePass parameters for the material.\r\n     */\r\n    public readonly prePassConfiguration: PrePassConfiguration;\r\n\r\n    /**\r\n     * Can this material render to prepass\r\n     */\r\n    public override get isPrePassCapable(): boolean {\r\n        return !this.disableDepthWrite;\r\n    }\r\n\r\n    /**\r\n     * Gets whether the color curves effect is enabled.\r\n     */\r\n    public get cameraColorCurvesEnabled(): boolean {\r\n        return this.imageProcessingConfiguration.colorCurvesEnabled;\r\n    }\r\n    /**\r\n     * Sets whether the color curves effect is enabled.\r\n     */\r\n    public set cameraColorCurvesEnabled(value: boolean) {\r\n        this.imageProcessingConfiguration.colorCurvesEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Gets whether the color grading effect is enabled.\r\n     */\r\n    public get cameraColorGradingEnabled(): boolean {\r\n        return this.imageProcessingConfiguration.colorGradingEnabled;\r\n    }\r\n    /**\r\n     * Gets whether the color grading effect is enabled.\r\n     */\r\n    public set cameraColorGradingEnabled(value: boolean) {\r\n        this.imageProcessingConfiguration.colorGradingEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Gets whether tonemapping is enabled or not.\r\n     */\r\n    public get cameraToneMappingEnabled(): boolean {\r\n        return this._imageProcessingConfiguration.toneMappingEnabled;\r\n    }\r\n    /**\r\n     * Sets whether tonemapping is enabled or not\r\n     */\r\n    public set cameraToneMappingEnabled(value: boolean) {\r\n        this._imageProcessingConfiguration.toneMappingEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * The camera exposure used on this material.\r\n     * This property is here and not in the camera to allow controlling exposure without full screen post process.\r\n     * This corresponds to a photographic exposure.\r\n     */\r\n    public get cameraExposure(): number {\r\n        return this._imageProcessingConfiguration.exposure;\r\n    }\r\n    /**\r\n     * The camera exposure used on this material.\r\n     * This property is here and not in the camera to allow controlling exposure without full screen post process.\r\n     * This corresponds to a photographic exposure.\r\n     */\r\n    public set cameraExposure(value: number) {\r\n        this._imageProcessingConfiguration.exposure = value;\r\n    }\r\n\r\n    /**\r\n     * Gets The camera contrast used on this material.\r\n     */\r\n    public get cameraContrast(): number {\r\n        return this._imageProcessingConfiguration.contrast;\r\n    }\r\n\r\n    /**\r\n     * Sets The camera contrast used on this material.\r\n     */\r\n    public set cameraContrast(value: number) {\r\n        this._imageProcessingConfiguration.contrast = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the Color Grading 2D Lookup Texture.\r\n     */\r\n    public get cameraColorGradingTexture(): Nullable<BaseTexture> {\r\n        return this._imageProcessingConfiguration.colorGradingTexture;\r\n    }\r\n    /**\r\n     * Sets the Color Grading 2D Lookup Texture.\r\n     */\r\n    public set cameraColorGradingTexture(value: Nullable<BaseTexture>) {\r\n        this._imageProcessingConfiguration.colorGradingTexture = value;\r\n    }\r\n\r\n    /**\r\n     * The color grading curves provide additional color adjustmnent that is applied after any color grading transform (3D LUT).\r\n     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.\r\n     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;\r\n     * corresponding to low luminance, medium luminance, and high luminance areas respectively.\r\n     */\r\n    public get cameraColorCurves(): Nullable<ColorCurves> {\r\n        return this._imageProcessingConfiguration.colorCurves;\r\n    }\r\n    /**\r\n     * The color grading curves provide additional color adjustment that is applied after any color grading transform (3D LUT).\r\n     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.\r\n     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;\r\n     * corresponding to low luminance, medium luminance, and high luminance areas respectively.\r\n     */\r\n    public set cameraColorCurves(value: Nullable<ColorCurves>) {\r\n        this._imageProcessingConfiguration.colorCurves = value;\r\n    }\r\n\r\n    /**\r\n     * Can this material render to several textures at once\r\n     */\r\n    public override get canRenderToMRT() {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Defines the detail map parameters for the material.\r\n     */\r\n    public readonly detailMap: DetailMapConfiguration;\r\n\r\n    protected _renderTargets = new SmartArray<RenderTargetTexture>(16);\r\n    protected _globalAmbientColor = new Color3(0, 0, 0);\r\n    protected _cacheHasRenderTargetTextures = false;\r\n\r\n    /**\r\n     * Instantiates a new standard material.\r\n     * This is the default material used in Babylon. It is the best trade off between quality\r\n     * and performances.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction\r\n     * @param name Define the name of the material in the scene\r\n     * @param scene Define the scene the material belong to\r\n     * @param forceGLSL Use the GLSL code generation for the shader (even on WebGPU). Default is false\r\n     */\r\n    constructor(name: string, scene?: Scene, forceGLSL = false) {\r\n        super(name, scene, undefined, forceGLSL || StandardMaterial.ForceGLSL);\r\n\r\n        this.detailMap = new DetailMapConfiguration(this);\r\n\r\n        // Setup the default processing configuration to the scene.\r\n        this._attachImageProcessingConfiguration(null);\r\n        this.prePassConfiguration = new PrePassConfiguration();\r\n\r\n        this.getRenderTargetTextures = (): SmartArray<RenderTargetTexture> => {\r\n            this._renderTargets.reset();\r\n\r\n            if (StandardMaterial.ReflectionTextureEnabled && this._reflectionTexture && this._reflectionTexture.isRenderTarget) {\r\n                this._renderTargets.push(<RenderTargetTexture>this._reflectionTexture);\r\n            }\r\n\r\n            if (StandardMaterial.RefractionTextureEnabled && this._refractionTexture && this._refractionTexture.isRenderTarget) {\r\n                this._renderTargets.push(<RenderTargetTexture>this._refractionTexture);\r\n            }\r\n\r\n            this._eventInfo.renderTargets = this._renderTargets;\r\n            this._callbackPluginEventFillRenderTargetTextures(this._eventInfo);\r\n\r\n            return this._renderTargets;\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that current material needs to register RTT\r\n     */\r\n    public override get hasRenderTargetTextures(): boolean {\r\n        if (StandardMaterial.ReflectionTextureEnabled && this._reflectionTexture && this._reflectionTexture.isRenderTarget) {\r\n            return true;\r\n        }\r\n\r\n        if (StandardMaterial.RefractionTextureEnabled && this._refractionTexture && this._refractionTexture.isRenderTarget) {\r\n            return true;\r\n        }\r\n\r\n        return this._cacheHasRenderTargetTextures;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name of the material e.g. \"StandardMaterial\"\r\n     * Mainly use in serialization.\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"StandardMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Specifies if the material will require alpha blending\r\n     * @returns a boolean specifying if alpha blending is needed\r\n     */\r\n    public override needAlphaBlending(): boolean {\r\n        if (this._hasTransparencyMode) {\r\n            return this._transparencyModeIsBlend;\r\n        }\r\n\r\n        if (this._disableAlphaBlending) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            this.alpha < 1.0 ||\r\n            this._opacityTexture != null ||\r\n            this._shouldUseAlphaFromDiffuseTexture() ||\r\n            (this._opacityFresnelParameters && this._opacityFresnelParameters.isEnabled)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Specifies if this material should be rendered in alpha test mode\r\n     * @returns a boolean specifying if an alpha test is needed.\r\n     */\r\n    public override needAlphaTesting(): boolean {\r\n        if (this._hasTransparencyMode) {\r\n            return this._transparencyModeIsTest;\r\n        }\r\n\r\n        return this._hasAlphaChannel() && (this._transparencyMode == null || this._transparencyMode === Material.MATERIAL_ALPHATEST);\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not the alpha value of the diffuse texture should be used for alpha blending.\r\n     */\r\n    protected _shouldUseAlphaFromDiffuseTexture(): boolean {\r\n        return this._diffuseTexture != null && this._diffuseTexture.hasAlpha && this._useAlphaFromDiffuseTexture && this._transparencyMode !== Material.MATERIAL_OPAQUE;\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not there is a usable alpha channel for transparency.\r\n     */\r\n    protected _hasAlphaChannel(): boolean {\r\n        return (this._diffuseTexture != null && this._diffuseTexture.hasAlpha) || this._opacityTexture != null;\r\n    }\r\n\r\n    /**\r\n     * Get the texture used for alpha test purpose.\r\n     * @returns the diffuse texture in case of the standard material.\r\n     */\r\n    public override getAlphaTestTexture(): Nullable<BaseTexture> {\r\n        return this._diffuseTexture;\r\n    }\r\n\r\n    /**\r\n     * Get if the submesh is ready to be used and all its information available.\r\n     * Child classes can use it to update shaders\r\n     * @param mesh defines the mesh to check\r\n     * @param subMesh defines which submesh to check\r\n     * @param useInstances specifies that instances should be used\r\n     * @returns a boolean indicating that the submesh is ready or not\r\n     */\r\n    public override isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances: boolean = false): boolean {\r\n        if (!this._uniformBufferLayoutBuilt) {\r\n            this.buildUniformLayout();\r\n        }\r\n\r\n        const drawWrapper = subMesh._drawWrapper;\r\n\r\n        if (drawWrapper.effect && this.isFrozen) {\r\n            if (drawWrapper._wasPreviouslyReady && drawWrapper._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            this._callbackPluginEventGeneric(MaterialPluginEvent.GetDefineNames, this._eventInfo);\r\n            subMesh.materialDefines = new StandardMaterialDefines(this._eventInfo.defineNames);\r\n        }\r\n\r\n        const scene = this.getScene();\r\n        const defines = <StandardMaterialDefines>subMesh.materialDefines;\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        // Lights\r\n        defines._needNormals = PrepareDefinesForLights(scene, mesh, defines, true, this._maxSimultaneousLights, this._disableLighting);\r\n\r\n        // Multiview\r\n        PrepareDefinesForMultiview(scene, defines);\r\n\r\n        // PrePass\r\n        const oit = this.needAlphaBlendingForMesh(mesh) && this.getScene().useOrderIndependentTransparency;\r\n        PrepareDefinesForPrePass(scene, defines, this.canRenderToMRT && !oit);\r\n\r\n        // Order independant transparency\r\n        PrepareDefinesForOIT(scene, defines, oit);\r\n\r\n        MaterialHelperGeometryRendering.PrepareDefines(engine.currentRenderPassId, mesh, defines);\r\n\r\n        // Textures\r\n        if (defines._areTexturesDirty) {\r\n            this._eventInfo.hasRenderTargetTextures = false;\r\n            this._callbackPluginEventHasRenderTargetTextures(this._eventInfo);\r\n            this._cacheHasRenderTargetTextures = this._eventInfo.hasRenderTargetTextures;\r\n            defines._needUVs = false;\r\n            for (let i = 1; i <= Constants.MAX_SUPPORTED_UV_SETS; ++i) {\r\n                defines[\"MAINUV\" + i] = false;\r\n            }\r\n            if (scene.texturesEnabled) {\r\n                defines.DIFFUSEDIRECTUV = 0;\r\n                defines.BUMPDIRECTUV = 0;\r\n                defines.AMBIENTDIRECTUV = 0;\r\n                defines.OPACITYDIRECTUV = 0;\r\n                defines.EMISSIVEDIRECTUV = 0;\r\n                defines.SPECULARDIRECTUV = 0;\r\n                defines.LIGHTMAPDIRECTUV = 0;\r\n\r\n                if (this._diffuseTexture && StandardMaterial.DiffuseTextureEnabled) {\r\n                    if (!this._diffuseTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    } else {\r\n                        PrepareDefinesForMergedUV(this._diffuseTexture, defines, \"DIFFUSE\");\r\n                    }\r\n                } else {\r\n                    defines.DIFFUSE = false;\r\n                }\r\n\r\n                if (this._ambientTexture && StandardMaterial.AmbientTextureEnabled) {\r\n                    if (!this._ambientTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    } else {\r\n                        PrepareDefinesForMergedUV(this._ambientTexture, defines, \"AMBIENT\");\r\n                    }\r\n                } else {\r\n                    defines.AMBIENT = false;\r\n                }\r\n\r\n                if (this._opacityTexture && StandardMaterial.OpacityTextureEnabled) {\r\n                    if (!this._opacityTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    } else {\r\n                        PrepareDefinesForMergedUV(this._opacityTexture, defines, \"OPACITY\");\r\n                        defines.OPACITYRGB = this._opacityTexture.getAlphaFromRGB;\r\n                    }\r\n                } else {\r\n                    defines.OPACITY = false;\r\n                }\r\n\r\n                if (this._reflectionTexture && StandardMaterial.ReflectionTextureEnabled) {\r\n                    if (!this._reflectionTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    } else {\r\n                        defines._needNormals = true;\r\n                        defines.REFLECTION = true;\r\n\r\n                        defines.ROUGHNESS = this._roughness > 0;\r\n                        defines.REFLECTIONOVERALPHA = this._useReflectionOverAlpha;\r\n                        defines.INVERTCUBICMAP = this._reflectionTexture.coordinatesMode === Texture.INVCUBIC_MODE;\r\n                        defines.REFLECTIONMAP_3D = this._reflectionTexture.isCube;\r\n                        defines.REFLECTIONMAP_OPPOSITEZ =\r\n                            defines.REFLECTIONMAP_3D && this.getScene().useRightHandedSystem ? !this._reflectionTexture.invertZ : this._reflectionTexture.invertZ;\r\n                        defines.RGBDREFLECTION = this._reflectionTexture.isRGBD;\r\n\r\n                        switch (this._reflectionTexture.coordinatesMode) {\r\n                            case Texture.EXPLICIT_MODE:\r\n                                defines.setReflectionMode(\"REFLECTIONMAP_EXPLICIT\");\r\n                                break;\r\n                            case Texture.PLANAR_MODE:\r\n                                defines.setReflectionMode(\"REFLECTIONMAP_PLANAR\");\r\n                                break;\r\n                            case Texture.PROJECTION_MODE:\r\n                                defines.setReflectionMode(\"REFLECTIONMAP_PROJECTION\");\r\n                                break;\r\n                            case Texture.SKYBOX_MODE:\r\n                                defines.setReflectionMode(\"REFLECTIONMAP_SKYBOX\");\r\n                                break;\r\n                            case Texture.SPHERICAL_MODE:\r\n                                defines.setReflectionMode(\"REFLECTIONMAP_SPHERICAL\");\r\n                                break;\r\n                            case Texture.EQUIRECTANGULAR_MODE:\r\n                                defines.setReflectionMode(\"REFLECTIONMAP_EQUIRECTANGULAR\");\r\n                                break;\r\n                            case Texture.FIXED_EQUIRECTANGULAR_MODE:\r\n                                defines.setReflectionMode(\"REFLECTIONMAP_EQUIRECTANGULAR_FIXED\");\r\n                                break;\r\n                            case Texture.FIXED_EQUIRECTANGULAR_MIRRORED_MODE:\r\n                                defines.setReflectionMode(\"REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED\");\r\n                                break;\r\n                            case Texture.CUBIC_MODE:\r\n                            case Texture.INVCUBIC_MODE:\r\n                            default:\r\n                                defines.setReflectionMode(\"REFLECTIONMAP_CUBIC\");\r\n                                break;\r\n                        }\r\n\r\n                        defines.USE_LOCAL_REFLECTIONMAP_CUBIC = (<any>this._reflectionTexture).boundingBoxSize ? true : false;\r\n                    }\r\n                } else {\r\n                    defines.REFLECTION = false;\r\n                    defines.REFLECTIONMAP_OPPOSITEZ = false;\r\n                }\r\n\r\n                if (this._emissiveTexture && StandardMaterial.EmissiveTextureEnabled) {\r\n                    if (!this._emissiveTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    } else {\r\n                        PrepareDefinesForMergedUV(this._emissiveTexture, defines, \"EMISSIVE\");\r\n                    }\r\n                } else {\r\n                    defines.EMISSIVE = false;\r\n                }\r\n\r\n                if (this._lightmapTexture && StandardMaterial.LightmapTextureEnabled) {\r\n                    if (!this._lightmapTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    } else {\r\n                        PrepareDefinesForMergedUV(this._lightmapTexture, defines, \"LIGHTMAP\");\r\n                        defines.USELIGHTMAPASSHADOWMAP = this._useLightmapAsShadowmap;\r\n                        defines.RGBDLIGHTMAP = this._lightmapTexture.isRGBD;\r\n                    }\r\n                } else {\r\n                    defines.LIGHTMAP = false;\r\n                }\r\n\r\n                if (this._specularTexture && StandardMaterial.SpecularTextureEnabled) {\r\n                    if (!this._specularTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    } else {\r\n                        PrepareDefinesForMergedUV(this._specularTexture, defines, \"SPECULAR\");\r\n                        defines.GLOSSINESS = this._useGlossinessFromSpecularMapAlpha;\r\n                    }\r\n                } else {\r\n                    defines.SPECULAR = false;\r\n                }\r\n\r\n                if (scene.getEngine().getCaps().standardDerivatives && this._bumpTexture && StandardMaterial.BumpTextureEnabled) {\r\n                    // Bump texture can not be not blocking.\r\n                    if (!this._bumpTexture.isReady()) {\r\n                        return false;\r\n                    } else {\r\n                        PrepareDefinesForMergedUV(this._bumpTexture, defines, \"BUMP\");\r\n\r\n                        defines.PARALLAX = this._useParallax;\r\n                        defines.PARALLAX_RHS = scene.useRightHandedSystem;\r\n                        defines.PARALLAXOCCLUSION = this._useParallaxOcclusion;\r\n                    }\r\n\r\n                    defines.OBJECTSPACE_NORMALMAP = this._useObjectSpaceNormalMap;\r\n                } else {\r\n                    defines.BUMP = false;\r\n                    defines.PARALLAX = false;\r\n                    defines.PARALLAX_RHS = false;\r\n                    defines.PARALLAXOCCLUSION = false;\r\n                }\r\n\r\n                if (this._refractionTexture && StandardMaterial.RefractionTextureEnabled) {\r\n                    if (!this._refractionTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    } else {\r\n                        defines._needUVs = true;\r\n                        defines.REFRACTION = true;\r\n\r\n                        defines.REFRACTIONMAP_3D = this._refractionTexture.isCube;\r\n                        defines.RGBDREFRACTION = this._refractionTexture.isRGBD;\r\n                        defines.USE_LOCAL_REFRACTIONMAP_CUBIC = (<any>this._refractionTexture).boundingBoxSize ? true : false;\r\n                    }\r\n                } else {\r\n                    defines.REFRACTION = false;\r\n                }\r\n\r\n                defines.TWOSIDEDLIGHTING = !this._backFaceCulling && this._twoSidedLighting;\r\n            } else {\r\n                defines.DIFFUSE = false;\r\n                defines.AMBIENT = false;\r\n                defines.OPACITY = false;\r\n                defines.REFLECTION = false;\r\n                defines.EMISSIVE = false;\r\n                defines.LIGHTMAP = false;\r\n                defines.BUMP = false;\r\n                defines.REFRACTION = false;\r\n            }\r\n\r\n            defines.ALPHAFROMDIFFUSE = this._shouldUseAlphaFromDiffuseTexture();\r\n\r\n            defines.EMISSIVEASILLUMINATION = this._useEmissiveAsIllumination;\r\n\r\n            defines.LINKEMISSIVEWITHDIFFUSE = this._linkEmissiveWithDiffuse;\r\n\r\n            defines.SPECULAROVERALPHA = this._useSpecularOverAlpha;\r\n\r\n            defines.PREMULTIPLYALPHA = this.alphaMode === Constants.ALPHA_PREMULTIPLIED || this.alphaMode === Constants.ALPHA_PREMULTIPLIED_PORTERDUFF;\r\n\r\n            defines.ALPHATEST_AFTERALLALPHACOMPUTATIONS = this.transparencyMode !== null;\r\n\r\n            defines.ALPHABLEND = this.transparencyMode === null || this.needAlphaBlendingForMesh(mesh); // check on null for backward compatibility\r\n        }\r\n\r\n        this._eventInfo.isReadyForSubMesh = true;\r\n        this._eventInfo.defines = defines;\r\n        this._eventInfo.subMesh = subMesh;\r\n        this._callbackPluginEventIsReadyForSubMesh(this._eventInfo);\r\n\r\n        if (!this._eventInfo.isReadyForSubMesh) {\r\n            return false;\r\n        }\r\n\r\n        if (defines._areImageProcessingDirty && this._imageProcessingConfiguration) {\r\n            if (!this._imageProcessingConfiguration.isReady()) {\r\n                return false;\r\n            }\r\n\r\n            this._imageProcessingConfiguration.prepareDefines(defines);\r\n\r\n            defines.IS_REFLECTION_LINEAR = this.reflectionTexture != null && !this.reflectionTexture.gammaSpace;\r\n            defines.IS_REFRACTION_LINEAR = this.refractionTexture != null && !this.refractionTexture.gammaSpace;\r\n        }\r\n\r\n        if (defines._areFresnelDirty) {\r\n            if (StandardMaterial.FresnelEnabled) {\r\n                // Fresnel\r\n                if (\r\n                    this._diffuseFresnelParameters ||\r\n                    this._opacityFresnelParameters ||\r\n                    this._emissiveFresnelParameters ||\r\n                    this._refractionFresnelParameters ||\r\n                    this._reflectionFresnelParameters\r\n                ) {\r\n                    defines.DIFFUSEFRESNEL = this._diffuseFresnelParameters && this._diffuseFresnelParameters.isEnabled;\r\n\r\n                    defines.OPACITYFRESNEL = this._opacityFresnelParameters && this._opacityFresnelParameters.isEnabled;\r\n\r\n                    defines.REFLECTIONFRESNEL = this._reflectionFresnelParameters && this._reflectionFresnelParameters.isEnabled;\r\n\r\n                    defines.REFLECTIONFRESNELFROMSPECULAR = this._useReflectionFresnelFromSpecular;\r\n\r\n                    defines.REFRACTIONFRESNEL = this._refractionFresnelParameters && this._refractionFresnelParameters.isEnabled;\r\n\r\n                    defines.EMISSIVEFRESNEL = this._emissiveFresnelParameters && this._emissiveFresnelParameters.isEnabled;\r\n\r\n                    defines._needNormals = true;\r\n                    defines.FRESNEL = true;\r\n                }\r\n            } else {\r\n                defines.FRESNEL = false;\r\n            }\r\n        }\r\n\r\n        // Check if Area Lights have LTC texture.\r\n        if (defines[\"AREALIGHTUSED\"]) {\r\n            for (let index = 0; index < mesh.lightSources.length; index++) {\r\n                if (!mesh.lightSources[index]._isReady()) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Misc.\r\n        PrepareDefinesForMisc(\r\n            mesh,\r\n            scene,\r\n            this._useLogarithmicDepth,\r\n            this.pointsCloud,\r\n            this.fogEnabled,\r\n            this.needAlphaTestingForMesh(mesh),\r\n            defines,\r\n            this._applyDecalMapAfterDetailMap\r\n        );\r\n\r\n        // Values that need to be evaluated on every frame\r\n        PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances, null, subMesh.getRenderingMesh().hasThinInstances);\r\n\r\n        // External config\r\n        this._eventInfo.defines = defines;\r\n        this._eventInfo.mesh = mesh;\r\n        this._callbackPluginEventPrepareDefinesBeforeAttributes(this._eventInfo);\r\n\r\n        // Attribs\r\n        PrepareDefinesForAttributes(mesh, defines, true, true, true);\r\n\r\n        // External config\r\n        this._callbackPluginEventPrepareDefines(this._eventInfo);\r\n\r\n        // Get correct effect\r\n        let forceWasNotReadyPreviously = false;\r\n\r\n        if (defines.isDirty) {\r\n            const lightDisposed = defines._areLightsDisposed;\r\n            defines.markAsProcessed();\r\n\r\n            // Fallbacks\r\n            const fallbacks = new EffectFallbacks();\r\n            if (defines.REFLECTION) {\r\n                fallbacks.addFallback(0, \"REFLECTION\");\r\n            }\r\n\r\n            if (defines.SPECULAR) {\r\n                fallbacks.addFallback(0, \"SPECULAR\");\r\n            }\r\n\r\n            if (defines.BUMP) {\r\n                fallbacks.addFallback(0, \"BUMP\");\r\n            }\r\n\r\n            if (defines.PARALLAX) {\r\n                fallbacks.addFallback(1, \"PARALLAX\");\r\n            }\r\n\r\n            if (defines.PARALLAX_RHS) {\r\n                fallbacks.addFallback(1, \"PARALLAX_RHS\");\r\n            }\r\n\r\n            if (defines.PARALLAXOCCLUSION) {\r\n                fallbacks.addFallback(0, \"PARALLAXOCCLUSION\");\r\n            }\r\n\r\n            if (defines.SPECULAROVERALPHA) {\r\n                fallbacks.addFallback(0, \"SPECULAROVERALPHA\");\r\n            }\r\n\r\n            if (defines.FOG) {\r\n                fallbacks.addFallback(1, \"FOG\");\r\n            }\r\n\r\n            if (defines.POINTSIZE) {\r\n                fallbacks.addFallback(0, \"POINTSIZE\");\r\n            }\r\n\r\n            if (defines.LOGARITHMICDEPTH) {\r\n                fallbacks.addFallback(0, \"LOGARITHMICDEPTH\");\r\n            }\r\n\r\n            HandleFallbacksForShadows(defines, fallbacks, this._maxSimultaneousLights);\r\n\r\n            if (defines.SPECULARTERM) {\r\n                fallbacks.addFallback(0, \"SPECULARTERM\");\r\n            }\r\n\r\n            if (defines.DIFFUSEFRESNEL) {\r\n                fallbacks.addFallback(1, \"DIFFUSEFRESNEL\");\r\n            }\r\n\r\n            if (defines.OPACITYFRESNEL) {\r\n                fallbacks.addFallback(2, \"OPACITYFRESNEL\");\r\n            }\r\n\r\n            if (defines.REFLECTIONFRESNEL) {\r\n                fallbacks.addFallback(3, \"REFLECTIONFRESNEL\");\r\n            }\r\n\r\n            if (defines.EMISSIVEFRESNEL) {\r\n                fallbacks.addFallback(4, \"EMISSIVEFRESNEL\");\r\n            }\r\n\r\n            if (defines.FRESNEL) {\r\n                fallbacks.addFallback(4, \"FRESNEL\");\r\n            }\r\n\r\n            if (defines.MULTIVIEW) {\r\n                fallbacks.addFallback(0, \"MULTIVIEW\");\r\n            }\r\n\r\n            //Attributes\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            if (defines.NORMAL) {\r\n                attribs.push(VertexBuffer.NormalKind);\r\n            }\r\n\r\n            if (defines.TANGENT) {\r\n                attribs.push(VertexBuffer.TangentKind);\r\n            }\r\n\r\n            for (let i = 1; i <= Constants.MAX_SUPPORTED_UV_SETS; ++i) {\r\n                if (defines[\"UV\" + i]) {\r\n                    attribs.push(`uv${i === 1 ? \"\" : i}`);\r\n                }\r\n            }\r\n\r\n            if (defines.VERTEXCOLOR) {\r\n                attribs.push(VertexBuffer.ColorKind);\r\n            }\r\n\r\n            PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\r\n            PrepareAttributesForInstances(attribs, defines);\r\n            PrepareAttributesForMorphTargets(attribs, mesh, defines);\r\n            PrepareAttributesForBakedVertexAnimation(attribs, mesh, defines);\r\n\r\n            let shaderName = \"default\";\r\n\r\n            const uniforms = [\r\n                \"world\",\r\n                \"view\",\r\n                \"viewProjection\",\r\n                \"vEyePosition\",\r\n                \"vLightsType\",\r\n                \"vAmbientColor\",\r\n                \"vDiffuseColor\",\r\n                \"vSpecularColor\",\r\n                \"vEmissiveColor\",\r\n                \"visibility\",\r\n                \"vFogInfos\",\r\n                \"vFogColor\",\r\n                \"pointSize\",\r\n                \"vDiffuseInfos\",\r\n                \"vAmbientInfos\",\r\n                \"vOpacityInfos\",\r\n                \"vReflectionInfos\",\r\n                \"vEmissiveInfos\",\r\n                \"vSpecularInfos\",\r\n                \"vBumpInfos\",\r\n                \"vLightmapInfos\",\r\n                \"vRefractionInfos\",\r\n                \"mBones\",\r\n                \"diffuseMatrix\",\r\n                \"ambientMatrix\",\r\n                \"opacityMatrix\",\r\n                \"reflectionMatrix\",\r\n                \"emissiveMatrix\",\r\n                \"specularMatrix\",\r\n                \"bumpMatrix\",\r\n                \"normalMatrix\",\r\n                \"lightmapMatrix\",\r\n                \"refractionMatrix\",\r\n                \"diffuseLeftColor\",\r\n                \"diffuseRightColor\",\r\n                \"opacityParts\",\r\n                \"reflectionLeftColor\",\r\n                \"reflectionRightColor\",\r\n                \"emissiveLeftColor\",\r\n                \"emissiveRightColor\",\r\n                \"refractionLeftColor\",\r\n                \"refractionRightColor\",\r\n                \"vReflectionPosition\",\r\n                \"vReflectionSize\",\r\n                \"vRefractionPosition\",\r\n                \"vRefractionSize\",\r\n                \"logarithmicDepthConstant\",\r\n                \"vTangentSpaceParams\",\r\n                \"alphaCutOff\",\r\n                \"boneTextureWidth\",\r\n                \"morphTargetTextureInfo\",\r\n                \"morphTargetTextureIndices\",\r\n                \"cameraInfo\",\r\n            ];\r\n\r\n            const samplers = [\r\n                \"diffuseSampler\",\r\n                \"ambientSampler\",\r\n                \"opacitySampler\",\r\n                \"reflectionCubeSampler\",\r\n                \"reflection2DSampler\",\r\n                \"emissiveSampler\",\r\n                \"specularSampler\",\r\n                \"bumpSampler\",\r\n                \"lightmapSampler\",\r\n                \"refractionCubeSampler\",\r\n                \"refraction2DSampler\",\r\n                \"boneSampler\",\r\n                \"morphTargets\",\r\n                \"oitDepthSampler\",\r\n                \"oitFrontColorSampler\",\r\n                \"areaLightsLTC1Sampler\",\r\n                \"areaLightsLTC2Sampler\",\r\n            ];\r\n\r\n            const uniformBuffers = [\"Material\", \"Scene\", \"Mesh\"];\r\n\r\n            const indexParameters = { maxSimultaneousLights: this._maxSimultaneousLights, maxSimultaneousMorphTargets: defines.NUM_MORPH_INFLUENCERS };\r\n\r\n            this._eventInfo.fallbacks = fallbacks;\r\n            this._eventInfo.fallbackRank = 0;\r\n            this._eventInfo.defines = defines;\r\n            this._eventInfo.uniforms = uniforms;\r\n            this._eventInfo.attributes = attribs;\r\n            this._eventInfo.samplers = samplers;\r\n            this._eventInfo.uniformBuffersNames = uniformBuffers;\r\n            this._eventInfo.customCode = undefined;\r\n            this._eventInfo.mesh = mesh;\r\n            this._eventInfo.indexParameters = indexParameters;\r\n            this._callbackPluginEventGeneric(MaterialPluginEvent.PrepareEffect, this._eventInfo);\r\n\r\n            MaterialHelperGeometryRendering.AddUniformsAndSamplers(uniforms, samplers);\r\n\r\n            PrePassConfiguration.AddUniforms(uniforms);\r\n            PrePassConfiguration.AddSamplers(samplers);\r\n\r\n            if (ImageProcessingConfiguration) {\r\n                ImageProcessingConfiguration.PrepareUniforms(uniforms, defines);\r\n                ImageProcessingConfiguration.PrepareSamplers(samplers, defines);\r\n            }\r\n\r\n            PrepareUniformsAndSamplersList(<IEffectCreationOptions>{\r\n                uniformsNames: uniforms,\r\n                uniformBuffersNames: uniformBuffers,\r\n                samplers: samplers,\r\n                defines: defines,\r\n                maxSimultaneousLights: this._maxSimultaneousLights,\r\n            });\r\n\r\n            AddClipPlaneUniforms(uniforms);\r\n\r\n            const csnrOptions: ICustomShaderNameResolveOptions = {};\r\n\r\n            if (this.customShaderNameResolve) {\r\n                shaderName = this.customShaderNameResolve(shaderName, uniforms, uniformBuffers, samplers, defines, attribs, csnrOptions);\r\n            }\r\n\r\n            const join = defines.toString();\r\n\r\n            const previousEffect = subMesh.effect;\r\n            let effect = scene.getEngine().createEffect(\r\n                shaderName,\r\n                <IEffectCreationOptions>{\r\n                    attributes: attribs,\r\n                    uniformsNames: uniforms,\r\n                    uniformBuffersNames: uniformBuffers,\r\n                    samplers: samplers,\r\n                    defines: join,\r\n                    fallbacks: fallbacks,\r\n                    onCompiled: this.onCompiled,\r\n                    onError: this.onError,\r\n                    indexParameters,\r\n                    processFinalCode: csnrOptions.processFinalCode,\r\n                    processCodeAfterIncludes: this._eventInfo.customCode,\r\n                    multiTarget: defines.PREPASS,\r\n                    shaderLanguage: this._shaderLanguage,\r\n                    extraInitializationsAsync: this._shadersLoaded\r\n                        ? undefined\r\n                        : async () => {\r\n                              if (this._shaderLanguage === ShaderLanguage.WGSL) {\r\n                                  await Promise.all([import(\"../ShadersWGSL/default.vertex\"), import(\"../ShadersWGSL/default.fragment\")]);\r\n                              } else {\r\n                                  await Promise.all([import(\"../Shaders/default.vertex\"), import(\"../Shaders/default.fragment\")]);\r\n                              }\r\n                              this._shadersLoaded = true;\r\n                          },\r\n                },\r\n                engine\r\n            );\r\n\r\n            this._eventInfo.customCode = undefined;\r\n\r\n            if (effect) {\r\n                if (this._onEffectCreatedObservable) {\r\n                    onCreatedEffectParameters.effect = effect;\r\n                    onCreatedEffectParameters.subMesh = subMesh;\r\n                    this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);\r\n                }\r\n\r\n                // Use previous effect while new one is compiling\r\n                if (this.allowShaderHotSwapping && previousEffect && !effect.isReady()) {\r\n                    effect = previousEffect;\r\n                    defines.markAsUnprocessed();\r\n\r\n                    forceWasNotReadyPreviously = this.isFrozen;\r\n\r\n                    if (lightDisposed) {\r\n                        // re register in case it takes more than one frame.\r\n                        defines._areLightsDisposed = true;\r\n                        return false;\r\n                    }\r\n                } else {\r\n                    scene.resetCachedMaterial();\r\n                    subMesh.setEffect(effect, defines, this._materialContext);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        drawWrapper._wasPreviouslyReady = forceWasNotReadyPreviously ? false : true;\r\n        drawWrapper._wasPreviouslyUsingInstances = useInstances;\r\n\r\n        this._checkScenePerformancePriority();\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Builds the material UBO layouts.\r\n     * Used internally during the effect preparation.\r\n     */\r\n    public override buildUniformLayout(): void {\r\n        // Order is important !\r\n        const ubo = this._uniformBuffer;\r\n        ubo.addUniform(\"diffuseLeftColor\", 4);\r\n        ubo.addUniform(\"diffuseRightColor\", 4);\r\n        ubo.addUniform(\"opacityParts\", 4);\r\n        ubo.addUniform(\"reflectionLeftColor\", 4);\r\n        ubo.addUniform(\"reflectionRightColor\", 4);\r\n        ubo.addUniform(\"refractionLeftColor\", 4);\r\n        ubo.addUniform(\"refractionRightColor\", 4);\r\n        ubo.addUniform(\"emissiveLeftColor\", 4);\r\n        ubo.addUniform(\"emissiveRightColor\", 4);\r\n\r\n        ubo.addUniform(\"vDiffuseInfos\", 2);\r\n        ubo.addUniform(\"vAmbientInfos\", 2);\r\n        ubo.addUniform(\"vOpacityInfos\", 2);\r\n        ubo.addUniform(\"vReflectionInfos\", 2);\r\n        ubo.addUniform(\"vReflectionPosition\", 3);\r\n        ubo.addUniform(\"vReflectionSize\", 3);\r\n        ubo.addUniform(\"vEmissiveInfos\", 2);\r\n        ubo.addUniform(\"vLightmapInfos\", 2);\r\n        ubo.addUniform(\"vSpecularInfos\", 2);\r\n        ubo.addUniform(\"vBumpInfos\", 3);\r\n\r\n        ubo.addUniform(\"diffuseMatrix\", 16);\r\n        ubo.addUniform(\"ambientMatrix\", 16);\r\n        ubo.addUniform(\"opacityMatrix\", 16);\r\n        ubo.addUniform(\"reflectionMatrix\", 16);\r\n        ubo.addUniform(\"emissiveMatrix\", 16);\r\n        ubo.addUniform(\"lightmapMatrix\", 16);\r\n        ubo.addUniform(\"specularMatrix\", 16);\r\n        ubo.addUniform(\"bumpMatrix\", 16);\r\n        ubo.addUniform(\"vTangentSpaceParams\", 2);\r\n        ubo.addUniform(\"pointSize\", 1);\r\n        ubo.addUniform(\"alphaCutOff\", 1);\r\n        ubo.addUniform(\"refractionMatrix\", 16);\r\n        ubo.addUniform(\"vRefractionInfos\", 4);\r\n        ubo.addUniform(\"vRefractionPosition\", 3);\r\n        ubo.addUniform(\"vRefractionSize\", 3);\r\n        ubo.addUniform(\"vSpecularColor\", 4);\r\n        ubo.addUniform(\"vEmissiveColor\", 3);\r\n        ubo.addUniform(\"vDiffuseColor\", 4);\r\n        ubo.addUniform(\"vAmbientColor\", 3);\r\n        ubo.addUniform(\"cameraInfo\", 4);\r\n\r\n        super.buildUniformLayout();\r\n    }\r\n\r\n    /**\r\n     * Binds the submesh to this material by preparing the effect and shader to draw\r\n     * @param world defines the world transformation matrix\r\n     * @param mesh defines the mesh containing the submesh\r\n     * @param subMesh defines the submesh to bind the material to\r\n     */\r\n    public override bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <StandardMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n        if (!effect) {\r\n            return;\r\n        }\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices Mesh.\r\n        mesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n        mesh.transferToEffect(world);\r\n\r\n        // Binding unconditionally\r\n        this._uniformBuffer.bindToEffect(effect, \"Material\");\r\n\r\n        this.prePassConfiguration.bindForSubMesh(this._activeEffect, scene, mesh, world, this.isFrozen);\r\n\r\n        MaterialHelperGeometryRendering.Bind(scene.getEngine().currentRenderPassId, this._activeEffect, mesh, world, this);\r\n\r\n        const camera = scene.activeCamera;\r\n        if (camera) {\r\n            this._uniformBuffer.updateFloat4(\"cameraInfo\", camera.minZ, camera.maxZ, 0, 0);\r\n        } else {\r\n            this._uniformBuffer.updateFloat4(\"cameraInfo\", 0, 0, 0, 0);\r\n        }\r\n\r\n        this._eventInfo.subMesh = subMesh;\r\n        this._callbackPluginEventHardBindForSubMesh(this._eventInfo);\r\n\r\n        // Normal Matrix\r\n        if (defines.OBJECTSPACE_NORMALMAP) {\r\n            world.toNormalMatrix(this._normalMatrix);\r\n            this.bindOnlyNormalMatrix(this._normalMatrix);\r\n        }\r\n\r\n        const mustRebind = this._mustRebind(scene, effect, subMesh, mesh.visibility);\r\n\r\n        // Bones\r\n        BindBonesParameters(mesh, effect);\r\n        const ubo = this._uniformBuffer;\r\n        if (mustRebind) {\r\n            this.bindViewProjection(effect);\r\n            if (!ubo.useUbo || !this.isFrozen || !ubo.isSync || subMesh._drawWrapper._forceRebindOnNextCall) {\r\n                if (StandardMaterial.FresnelEnabled && defines.FRESNEL) {\r\n                    // Fresnel\r\n                    if (this.diffuseFresnelParameters && this.diffuseFresnelParameters.isEnabled) {\r\n                        ubo.updateColor4(\"diffuseLeftColor\", this.diffuseFresnelParameters.leftColor, this.diffuseFresnelParameters.power);\r\n                        ubo.updateColor4(\"diffuseRightColor\", this.diffuseFresnelParameters.rightColor, this.diffuseFresnelParameters.bias);\r\n                    }\r\n\r\n                    if (this.opacityFresnelParameters && this.opacityFresnelParameters.isEnabled) {\r\n                        ubo.updateColor4(\r\n                            \"opacityParts\",\r\n                            new Color3(\r\n                                this.opacityFresnelParameters.leftColor.toLuminance(),\r\n                                this.opacityFresnelParameters.rightColor.toLuminance(),\r\n                                this.opacityFresnelParameters.bias\r\n                            ),\r\n                            this.opacityFresnelParameters.power\r\n                        );\r\n                    }\r\n\r\n                    if (this.reflectionFresnelParameters && this.reflectionFresnelParameters.isEnabled) {\r\n                        ubo.updateColor4(\"reflectionLeftColor\", this.reflectionFresnelParameters.leftColor, this.reflectionFresnelParameters.power);\r\n                        ubo.updateColor4(\"reflectionRightColor\", this.reflectionFresnelParameters.rightColor, this.reflectionFresnelParameters.bias);\r\n                    }\r\n\r\n                    if (this.refractionFresnelParameters && this.refractionFresnelParameters.isEnabled) {\r\n                        ubo.updateColor4(\"refractionLeftColor\", this.refractionFresnelParameters.leftColor, this.refractionFresnelParameters.power);\r\n                        ubo.updateColor4(\"refractionRightColor\", this.refractionFresnelParameters.rightColor, this.refractionFresnelParameters.bias);\r\n                    }\r\n\r\n                    if (this.emissiveFresnelParameters && this.emissiveFresnelParameters.isEnabled) {\r\n                        ubo.updateColor4(\"emissiveLeftColor\", this.emissiveFresnelParameters.leftColor, this.emissiveFresnelParameters.power);\r\n                        ubo.updateColor4(\"emissiveRightColor\", this.emissiveFresnelParameters.rightColor, this.emissiveFresnelParameters.bias);\r\n                    }\r\n                }\r\n\r\n                // Textures\r\n                if (scene.texturesEnabled) {\r\n                    if (this._diffuseTexture && StandardMaterial.DiffuseTextureEnabled) {\r\n                        ubo.updateFloat2(\"vDiffuseInfos\", this._diffuseTexture.coordinatesIndex, this._diffuseTexture.level);\r\n                        BindTextureMatrix(this._diffuseTexture, ubo, \"diffuse\");\r\n                    }\r\n\r\n                    if (this._ambientTexture && StandardMaterial.AmbientTextureEnabled) {\r\n                        ubo.updateFloat2(\"vAmbientInfos\", this._ambientTexture.coordinatesIndex, this._ambientTexture.level);\r\n                        BindTextureMatrix(this._ambientTexture, ubo, \"ambient\");\r\n                    }\r\n\r\n                    if (this._opacityTexture && StandardMaterial.OpacityTextureEnabled) {\r\n                        ubo.updateFloat2(\"vOpacityInfos\", this._opacityTexture.coordinatesIndex, this._opacityTexture.level);\r\n                        BindTextureMatrix(this._opacityTexture, ubo, \"opacity\");\r\n                    }\r\n\r\n                    if (this._hasAlphaChannel()) {\r\n                        ubo.updateFloat(\"alphaCutOff\", this.alphaCutOff);\r\n                    }\r\n\r\n                    if (this._reflectionTexture && StandardMaterial.ReflectionTextureEnabled) {\r\n                        ubo.updateFloat2(\"vReflectionInfos\", this._reflectionTexture.level, this.roughness);\r\n                        ubo.updateMatrix(\"reflectionMatrix\", this._reflectionTexture.getReflectionTextureMatrix());\r\n\r\n                        if ((<any>this._reflectionTexture).boundingBoxSize) {\r\n                            const cubeTexture = <CubeTexture>this._reflectionTexture;\r\n\r\n                            ubo.updateVector3(\"vReflectionPosition\", cubeTexture.boundingBoxPosition);\r\n                            ubo.updateVector3(\"vReflectionSize\", cubeTexture.boundingBoxSize);\r\n                        }\r\n                    } else {\r\n                        ubo.updateFloat2(\"vReflectionInfos\", 0.0, this.roughness);\r\n                    }\r\n\r\n                    if (this._emissiveTexture && StandardMaterial.EmissiveTextureEnabled) {\r\n                        ubo.updateFloat2(\"vEmissiveInfos\", this._emissiveTexture.coordinatesIndex, this._emissiveTexture.level);\r\n                        BindTextureMatrix(this._emissiveTexture, ubo, \"emissive\");\r\n                    }\r\n\r\n                    if (this._lightmapTexture && StandardMaterial.LightmapTextureEnabled) {\r\n                        ubo.updateFloat2(\"vLightmapInfos\", this._lightmapTexture.coordinatesIndex, this._lightmapTexture.level);\r\n                        BindTextureMatrix(this._lightmapTexture, ubo, \"lightmap\");\r\n                    }\r\n\r\n                    if (this._specularTexture && StandardMaterial.SpecularTextureEnabled) {\r\n                        ubo.updateFloat2(\"vSpecularInfos\", this._specularTexture.coordinatesIndex, this._specularTexture.level);\r\n                        BindTextureMatrix(this._specularTexture, ubo, \"specular\");\r\n                    }\r\n\r\n                    if (this._bumpTexture && scene.getEngine().getCaps().standardDerivatives && StandardMaterial.BumpTextureEnabled) {\r\n                        ubo.updateFloat3(\"vBumpInfos\", this._bumpTexture.coordinatesIndex, 1.0 / this._bumpTexture.level, this.parallaxScaleBias);\r\n                        BindTextureMatrix(this._bumpTexture, ubo, \"bump\");\r\n\r\n                        if (scene._mirroredCameraPosition) {\r\n                            ubo.updateFloat2(\"vTangentSpaceParams\", this._invertNormalMapX ? 1.0 : -1.0, this._invertNormalMapY ? 1.0 : -1.0);\r\n                        } else {\r\n                            ubo.updateFloat2(\"vTangentSpaceParams\", this._invertNormalMapX ? -1.0 : 1.0, this._invertNormalMapY ? -1.0 : 1.0);\r\n                        }\r\n                    }\r\n\r\n                    if (this._refractionTexture && StandardMaterial.RefractionTextureEnabled) {\r\n                        let depth = 1.0;\r\n                        if (!this._refractionTexture.isCube) {\r\n                            ubo.updateMatrix(\"refractionMatrix\", this._refractionTexture.getReflectionTextureMatrix());\r\n\r\n                            if ((<any>this._refractionTexture).depth) {\r\n                                depth = (<any>this._refractionTexture).depth;\r\n                            }\r\n                        }\r\n                        ubo.updateFloat4(\"vRefractionInfos\", this._refractionTexture.level, this.indexOfRefraction, depth, this.invertRefractionY ? -1 : 1);\r\n\r\n                        if ((<any>this._refractionTexture).boundingBoxSize) {\r\n                            const cubeTexture = <CubeTexture>this._refractionTexture;\r\n\r\n                            ubo.updateVector3(\"vRefractionPosition\", cubeTexture.boundingBoxPosition);\r\n                            ubo.updateVector3(\"vRefractionSize\", cubeTexture.boundingBoxSize);\r\n                        }\r\n                    }\r\n                }\r\n\r\n                // Point size\r\n                if (this.pointsCloud) {\r\n                    ubo.updateFloat(\"pointSize\", this.pointSize);\r\n                }\r\n\r\n                ubo.updateColor4(\"vSpecularColor\", this.specularColor, this.specularPower);\r\n\r\n                ubo.updateColor3(\"vEmissiveColor\", StandardMaterial.EmissiveTextureEnabled ? this.emissiveColor : Color3.BlackReadOnly);\r\n                ubo.updateColor4(\"vDiffuseColor\", this.diffuseColor, this.alpha);\r\n\r\n                scene.ambientColor.multiplyToRef(this.ambientColor, this._globalAmbientColor);\r\n                ubo.updateColor3(\"vAmbientColor\", this._globalAmbientColor);\r\n            }\r\n\r\n            // Textures\r\n            if (scene.texturesEnabled) {\r\n                if (this._diffuseTexture && StandardMaterial.DiffuseTextureEnabled) {\r\n                    effect.setTexture(\"diffuseSampler\", this._diffuseTexture);\r\n                }\r\n\r\n                if (this._ambientTexture && StandardMaterial.AmbientTextureEnabled) {\r\n                    effect.setTexture(\"ambientSampler\", this._ambientTexture);\r\n                }\r\n\r\n                if (this._opacityTexture && StandardMaterial.OpacityTextureEnabled) {\r\n                    effect.setTexture(\"opacitySampler\", this._opacityTexture);\r\n                }\r\n\r\n                if (this._reflectionTexture && StandardMaterial.ReflectionTextureEnabled) {\r\n                    if (this._reflectionTexture.isCube) {\r\n                        effect.setTexture(\"reflectionCubeSampler\", this._reflectionTexture);\r\n                    } else {\r\n                        effect.setTexture(\"reflection2DSampler\", this._reflectionTexture);\r\n                    }\r\n                }\r\n\r\n                if (this._emissiveTexture && StandardMaterial.EmissiveTextureEnabled) {\r\n                    effect.setTexture(\"emissiveSampler\", this._emissiveTexture);\r\n                }\r\n\r\n                if (this._lightmapTexture && StandardMaterial.LightmapTextureEnabled) {\r\n                    effect.setTexture(\"lightmapSampler\", this._lightmapTexture);\r\n                }\r\n\r\n                if (this._specularTexture && StandardMaterial.SpecularTextureEnabled) {\r\n                    effect.setTexture(\"specularSampler\", this._specularTexture);\r\n                }\r\n\r\n                if (this._bumpTexture && scene.getEngine().getCaps().standardDerivatives && StandardMaterial.BumpTextureEnabled) {\r\n                    effect.setTexture(\"bumpSampler\", this._bumpTexture);\r\n                }\r\n\r\n                if (this._refractionTexture && StandardMaterial.RefractionTextureEnabled) {\r\n                    if (this._refractionTexture.isCube) {\r\n                        effect.setTexture(\"refractionCubeSampler\", this._refractionTexture);\r\n                    } else {\r\n                        effect.setTexture(\"refraction2DSampler\", this._refractionTexture);\r\n                    }\r\n                }\r\n            }\r\n\r\n            // OIT with depth peeling\r\n            if (this.getScene().useOrderIndependentTransparency && this.needAlphaBlendingForMesh(mesh)) {\r\n                this.getScene().depthPeelingRenderer!.bind(effect);\r\n            }\r\n\r\n            this._eventInfo.subMesh = subMesh;\r\n            this._callbackPluginEventBindForSubMesh(this._eventInfo);\r\n\r\n            // Clip plane\r\n            BindClipPlane(effect, this, scene);\r\n\r\n            // Colors\r\n            this.bindEyePosition(effect);\r\n        } else if (scene.getEngine()._features.needToAlwaysBindUniformBuffers) {\r\n            this._needToBindSceneUbo = true;\r\n        }\r\n\r\n        if (mustRebind || !this.isFrozen) {\r\n            // Lights\r\n            if (scene.lightsEnabled && !this._disableLighting) {\r\n                BindLights(scene, mesh, effect, defines, this._maxSimultaneousLights);\r\n            }\r\n\r\n            // View\r\n            if (\r\n                (scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) ||\r\n                this._reflectionTexture ||\r\n                this._refractionTexture ||\r\n                mesh.receiveShadows ||\r\n                defines.PREPASS\r\n            ) {\r\n                this.bindView(effect);\r\n            }\r\n\r\n            // Fog\r\n            BindFogParameters(scene, mesh, effect);\r\n\r\n            // Morph targets\r\n            if (defines.NUM_MORPH_INFLUENCERS) {\r\n                BindMorphTargetParameters(mesh, effect);\r\n            }\r\n\r\n            if (defines.BAKED_VERTEX_ANIMATION_TEXTURE) {\r\n                mesh.bakedVertexAnimationManager?.bind(effect, defines.INSTANCES);\r\n            }\r\n\r\n            // Log. depth\r\n            if (this.useLogarithmicDepth) {\r\n                BindLogDepth(defines, effect, scene);\r\n            }\r\n\r\n            // image processing\r\n            if (this._imageProcessingConfiguration && !this._imageProcessingConfiguration.applyByPostProcess) {\r\n                this._imageProcessingConfiguration.bind(this._activeEffect);\r\n            }\r\n        }\r\n\r\n        this._afterBind(mesh, this._activeEffect, subMesh);\r\n        ubo.update();\r\n    }\r\n\r\n    /**\r\n     * Get the list of animatables in the material.\r\n     * @returns the list of animatables object used in the material\r\n     */\r\n    public override getAnimatables(): IAnimatable[] {\r\n        const results = super.getAnimatables();\r\n\r\n        if (this._diffuseTexture && this._diffuseTexture.animations && this._diffuseTexture.animations.length > 0) {\r\n            results.push(this._diffuseTexture);\r\n        }\r\n\r\n        if (this._ambientTexture && this._ambientTexture.animations && this._ambientTexture.animations.length > 0) {\r\n            results.push(this._ambientTexture);\r\n        }\r\n\r\n        if (this._opacityTexture && this._opacityTexture.animations && this._opacityTexture.animations.length > 0) {\r\n            results.push(this._opacityTexture);\r\n        }\r\n\r\n        if (this._reflectionTexture && this._reflectionTexture.animations && this._reflectionTexture.animations.length > 0) {\r\n            results.push(this._reflectionTexture);\r\n        }\r\n\r\n        if (this._emissiveTexture && this._emissiveTexture.animations && this._emissiveTexture.animations.length > 0) {\r\n            results.push(this._emissiveTexture);\r\n        }\r\n\r\n        if (this._specularTexture && this._specularTexture.animations && this._specularTexture.animations.length > 0) {\r\n            results.push(this._specularTexture);\r\n        }\r\n\r\n        if (this._bumpTexture && this._bumpTexture.animations && this._bumpTexture.animations.length > 0) {\r\n            results.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._lightmapTexture && this._lightmapTexture.animations && this._lightmapTexture.animations.length > 0) {\r\n            results.push(this._lightmapTexture);\r\n        }\r\n\r\n        if (this._refractionTexture && this._refractionTexture.animations && this._refractionTexture.animations.length > 0) {\r\n            results.push(this._refractionTexture);\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Gets the active textures from the material\r\n     * @returns an array of textures\r\n     */\r\n    public override getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        if (this._diffuseTexture) {\r\n            activeTextures.push(this._diffuseTexture);\r\n        }\r\n\r\n        if (this._ambientTexture) {\r\n            activeTextures.push(this._ambientTexture);\r\n        }\r\n\r\n        if (this._opacityTexture) {\r\n            activeTextures.push(this._opacityTexture);\r\n        }\r\n\r\n        if (this._reflectionTexture) {\r\n            activeTextures.push(this._reflectionTexture);\r\n        }\r\n\r\n        if (this._emissiveTexture) {\r\n            activeTextures.push(this._emissiveTexture);\r\n        }\r\n\r\n        if (this._specularTexture) {\r\n            activeTextures.push(this._specularTexture);\r\n        }\r\n\r\n        if (this._bumpTexture) {\r\n            activeTextures.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._lightmapTexture) {\r\n            activeTextures.push(this._lightmapTexture);\r\n        }\r\n\r\n        if (this._refractionTexture) {\r\n            activeTextures.push(this._refractionTexture);\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    /**\r\n     * Specifies if the material uses a texture\r\n     * @param texture defines the texture to check against the material\r\n     * @returns a boolean specifying if the material uses the texture\r\n     */\r\n    public override hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        if (this._diffuseTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._ambientTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._opacityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._reflectionTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._emissiveTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._specularTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._bumpTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._lightmapTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._refractionTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Disposes the material\r\n     * @param forceDisposeEffect specifies if effects should be forcefully disposed\r\n     * @param forceDisposeTextures specifies if textures should be forcefully disposed\r\n     */\r\n    public override dispose(forceDisposeEffect?: boolean, forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            this._diffuseTexture?.dispose();\r\n            this._ambientTexture?.dispose();\r\n            this._opacityTexture?.dispose();\r\n            this._reflectionTexture?.dispose();\r\n            this._emissiveTexture?.dispose();\r\n            this._specularTexture?.dispose();\r\n            this._bumpTexture?.dispose();\r\n            this._lightmapTexture?.dispose();\r\n            this._refractionTexture?.dispose();\r\n        }\r\n\r\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect, forceDisposeTextures);\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the material, and gives it a new name\r\n     * @param name defines the new name for the duplicated material\r\n     * @param cloneTexturesOnlyOnce - if a texture is used in more than one channel (e.g diffuse and opacity), only clone it once and reuse it on the other channels. Default false.\r\n     * @param rootUrl defines the root URL to use to load textures\r\n     * @returns the cloned material\r\n     */\r\n    public override clone(name: string, cloneTexturesOnlyOnce: boolean = true, rootUrl = \"\"): StandardMaterial {\r\n        const result = SerializationHelper.Clone(() => new StandardMaterial(name, this.getScene()), this, { cloneTexturesOnlyOnce });\r\n\r\n        result.name = name;\r\n        result.id = name;\r\n\r\n        this.stencil.copyTo(result.stencil);\r\n\r\n        this._clonePlugins(result, rootUrl);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Creates a standard material from parsed material data\r\n     * @param source defines the JSON representation of the material\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a new standard material\r\n     */\r\n    public static override Parse(source: any, scene: Scene, rootUrl: string): StandardMaterial {\r\n        const material = SerializationHelper.Parse(() => new StandardMaterial(source.name, scene), source, scene, rootUrl);\r\n\r\n        if (source.stencil) {\r\n            material.stencil.parse(source.stencil, scene, rootUrl);\r\n        }\r\n\r\n        Material._ParsePlugins(source, material, scene, rootUrl);\r\n\r\n        return material;\r\n    }\r\n\r\n    // Flags used to enable or disable a type of texture for all Standard Materials\r\n    /**\r\n     * Are diffuse textures enabled in the application.\r\n     */\r\n    public static get DiffuseTextureEnabled(): boolean {\r\n        return MaterialFlags.DiffuseTextureEnabled;\r\n    }\r\n    public static set DiffuseTextureEnabled(value: boolean) {\r\n        MaterialFlags.DiffuseTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are detail textures enabled in the application.\r\n     */\r\n    public static get DetailTextureEnabled(): boolean {\r\n        return MaterialFlags.DetailTextureEnabled;\r\n    }\r\n    public static set DetailTextureEnabled(value: boolean) {\r\n        MaterialFlags.DetailTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are ambient textures enabled in the application.\r\n     */\r\n    public static get AmbientTextureEnabled(): boolean {\r\n        return MaterialFlags.AmbientTextureEnabled;\r\n    }\r\n    public static set AmbientTextureEnabled(value: boolean) {\r\n        MaterialFlags.AmbientTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are opacity textures enabled in the application.\r\n     */\r\n    public static get OpacityTextureEnabled(): boolean {\r\n        return MaterialFlags.OpacityTextureEnabled;\r\n    }\r\n    public static set OpacityTextureEnabled(value: boolean) {\r\n        MaterialFlags.OpacityTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are reflection textures enabled in the application.\r\n     */\r\n    public static get ReflectionTextureEnabled(): boolean {\r\n        return MaterialFlags.ReflectionTextureEnabled;\r\n    }\r\n    public static set ReflectionTextureEnabled(value: boolean) {\r\n        MaterialFlags.ReflectionTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are emissive textures enabled in the application.\r\n     */\r\n    public static get EmissiveTextureEnabled(): boolean {\r\n        return MaterialFlags.EmissiveTextureEnabled;\r\n    }\r\n    public static set EmissiveTextureEnabled(value: boolean) {\r\n        MaterialFlags.EmissiveTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are specular textures enabled in the application.\r\n     */\r\n    public static get SpecularTextureEnabled(): boolean {\r\n        return MaterialFlags.SpecularTextureEnabled;\r\n    }\r\n    public static set SpecularTextureEnabled(value: boolean) {\r\n        MaterialFlags.SpecularTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are bump textures enabled in the application.\r\n     */\r\n    public static get BumpTextureEnabled(): boolean {\r\n        return MaterialFlags.BumpTextureEnabled;\r\n    }\r\n    public static set BumpTextureEnabled(value: boolean) {\r\n        MaterialFlags.BumpTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are lightmap textures enabled in the application.\r\n     */\r\n    public static get LightmapTextureEnabled(): boolean {\r\n        return MaterialFlags.LightmapTextureEnabled;\r\n    }\r\n    public static set LightmapTextureEnabled(value: boolean) {\r\n        MaterialFlags.LightmapTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are refraction textures enabled in the application.\r\n     */\r\n    public static get RefractionTextureEnabled(): boolean {\r\n        return MaterialFlags.RefractionTextureEnabled;\r\n    }\r\n    public static set RefractionTextureEnabled(value: boolean) {\r\n        MaterialFlags.RefractionTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are color grading textures enabled in the application.\r\n     */\r\n    public static get ColorGradingTextureEnabled(): boolean {\r\n        return MaterialFlags.ColorGradingTextureEnabled;\r\n    }\r\n    public static set ColorGradingTextureEnabled(value: boolean) {\r\n        MaterialFlags.ColorGradingTextureEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Are fresnels enabled in the application.\r\n     */\r\n    public static get FresnelEnabled(): boolean {\r\n        return MaterialFlags.FresnelEnabled;\r\n    }\r\n    public static set FresnelEnabled(value: boolean) {\r\n        MaterialFlags.FresnelEnabled = value;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.StandardMaterial\", StandardMaterial);\r\n\r\nScene.DefaultMaterialFactory = (scene: Scene) => {\r\n    return new StandardMaterial(\"default material\", scene);\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA,uDAAA,EAAyD,CACzD,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,4BAA4B,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAEtI,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAIhD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAEjC,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAIjD,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAG9D,OAAO,EAAE,4BAA4B,EAAE,MAAM,gCAAgC,CAAC;AAI9E,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAEjD,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAG9C,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAGxD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAGhD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAC;AAC3E,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAChF,OAAO,EACH,mBAAmB,EACnB,iBAAiB,EACjB,UAAU,EACV,YAAY,EACZ,yBAAyB,EACzB,iBAAiB,EACjB,yBAAyB,EACzB,wCAAwC,EACxC,yBAAyB,EACzB,6BAA6B,EAC7B,gCAAgC,EAChC,2BAA2B,EAC3B,iCAAiC,EACjC,uBAAuB,EACvB,yBAAyB,EACzB,qBAAqB,EACrB,0BAA0B,EAC1B,oBAAoB,EACpB,wBAAwB,EACxB,8BAA8B,GACjC,MAAM,4BAA4B,CAAC;AACpC,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAEvE,OAAO,EAAE,+BAA+B,EAAE,MAAM,oCAAoC,CAAC;;;;;;;;;;;;;;;;;;;;;AAErF,MAAM,yBAAyB,GAAG;IAAE,MAAM,EAAE,IAAyB;IAAE,OAAO,EAAE,IAAoC;AAAA,CAAE,CAAC;AAGjH,MAAO,uBAAwB,gLAAQ,kBAAe;IA8LjD,iBAAiB,CAAC,YAAoB,EAAA;QACzC,MAAM,KAAK,GAAG;YACV,qBAAqB;YACrB,wBAAwB;YACxB,sBAAsB;YACtB,0BAA0B;YAC1B,0BAA0B;YAC1B,sBAAsB;YACtB,yBAAyB;YACzB,+BAA+B;YAC/B,qCAAqC;YACrC,6CAA6C;SAChD,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,KAAK,CAAE,CAAC;YACjB,IAAK,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,YAAY,CAAC;QAC9C,CAAC;IACL,CAAC;IA1BD;;;OAGG,CACH,YAAY,kBAAuE,CAAA;QAC/E,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAzLvB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QACpB,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QACvC,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QACpB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QACpB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;QACrB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;QACrB,IAAA,CAAA,IAAI,GAAG,KAAK,CAAC;QACb,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACjB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,MAAM,GAAG,KAAK,CAAC;QACf,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC;QACzB,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACjB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;QACrB,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAC9B,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QACjC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,mCAAmC,GAAG,KAAK,CAAC;QAC5C,IAAA,CAAA,2CAA2C,GAAG,KAAK,CAAC;QACpD,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAC9B,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,+BAA+B,GAAG,KAAK,CAAC;QACxC,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QACvC,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAClC,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QACnC,IAAA,CAAA,4BAA4B,GAAG,KAAK,CAAC;QACrC,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC;QAC1B,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC,CAAC,0CAA0C;QACrE,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC,CAAC,0CAA0C;QACpE,IAAA,CAAA,mCAAmC,GAAG,KAAK,CAAC;QAC5C,IAAA,CAAA,UAAU,GAAG,IAAI,CAAC;QAElB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QACtB,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC,CAAC;QACzB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,wBAAwB,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,yBAAyB,GAAG,CAAC,CAAC,CAAC;QAC/B,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QACtB,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC,CAAC;QACzB,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAClC,IAAA,CAAA,+BAA+B,GAAG,CAAC,CAAC,CAAC;QACrC,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,mCAAmC,GAAG,CAAC,CAAC,CAAC;QACzC,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAClC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,0BAA0B,GAAG,CAAC,CAAC,CAAC;QAChC,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,sBAAsB,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,4BAA4B,GAAG,CAAC,CAAC,CAAC;QAClC,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,sBAAsB,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,6BAA6B,GAAG,CAAC,CAAC,CAAC;QACnC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,0BAA0B,GAAG,CAAC,CAAC,CAAC;QAChC,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QAEpB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAEvB,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAClC,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAChB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,MAAM,GAAG,KAAK,CAAC;QACf,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QACnC,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QACvC,IAAA,CAAA,qCAAqC,GAAG,KAAK,CAAC;QAC9C,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,kBAAkB,GAAG,IAAI,CAAC;QAEjC;;;WAGG,CACI,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QACpC;;;WAGG,CACI,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QAEjB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAQ9B,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;CAoBJ;AAOK,MAAO,gBAAiB,6KAAQ,eAAY;IAgW9C;;OAEG,CACH,IAAW,4BAA4B,GAAA;QACnC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;;;OAIG,CACH,IAAW,4BAA4B,CAAC,KAAmC,EAAA;QACvE,IAAI,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC;QAEhD,qCAAqC;QACrC,IAAI,CAAC,uCAAuC,EAAE,CAAC;IACnD,CAAC;IAOD;;;OAGG,CACO,mCAAmC,CAAC,aAAqD,EAAA;QAC/F,IAAI,aAAa,KAAK,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACvD,OAAO;QACX,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACtE,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAChG,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,4BAA4B,CAAC;QACtF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,6BAA6B,GAAG,aAAa,CAAC;QACvD,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3F,IAAI,CAAC,uCAAuC,EAAE,CAAC;YACnD,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IASD;;OAEG,CACH,IAAoB,gBAAgB,GAAA;QAChC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACnC,CAAC;IAED;;OAEG,CACH,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;IAChE,CAAC;IACD;;OAEG,CACH,IAAW,wBAAwB,CAAC,KAAc,EAAA;QAC9C,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,CAAC;IACjE,CAAC;IACD;;OAEG,CACH,IAAW,yBAAyB,CAAC,KAAc,EAAA;QAC/C,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;OAEG,CACH,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC;IACjE,CAAC;IACD;;OAEG,CACH,IAAW,wBAAwB,CAAC,KAAc,EAAA;QAC9C,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;;;OAIG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC;IACvD,CAAC;IACD;;;;OAIG,CACH,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,6BAA6B,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxD,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC;IACvD,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,6BAA6B,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxD,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,CAAC;IAClE,CAAC;IACD;;OAEG,CACH,IAAW,yBAAyB,CAAC,KAA4B,EAAA;QAC7D,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;;;;OAKG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,6BAA6B,CAAC,WAAW,CAAC;IAC1D,CAAC;IACD;;;;;OAKG,CACH,IAAW,iBAAiB,CAAC,KAA4B,EAAA;QACrD,IAAI,CAAC,6BAA6B,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3D,CAAC;IAED;;OAEG,CACH,IAAoB,cAAc,GAAA;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IA+CD;;OAEG,CACH,IAAoB,uBAAuB,GAAA;QACvC,IAAI,gBAAgB,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;YACjH,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,gBAAgB,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;YACjH,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;;;OAIG,CACa,YAAY,GAAA;QACxB,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACa,iBAAiB,GAAA;QAC7B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,wBAAwB,CAAC;QACzC,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,AACH,IAAI,CAAC,KAAK,GAAG,GAAG,IAChB,IAAI,CAAC,eAAe,IAAI,IAAI,IAC5B,IAAI,CAAC,iCAAiC,EAAE,IACvC,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAC/E,CAAC;IACN,CAAC;IAED;;;OAGG,CACa,gBAAgB,GAAA;QAC5B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACxC,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,IAAI,CAAC,iBAAiB,qKAAK,WAAQ,CAAC,kBAAkB,CAAC,CAAC;IACjI,CAAC;IAED;;OAEG,CACO,iCAAiC,GAAA;QACvC,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,iBAAiB,oKAAK,YAAQ,CAAC,eAAe,CAAC;IACpK,CAAC;IAED;;OAEG,CACO,gBAAgB,GAAA;QACtB,OAAO,AAAC,IAAI,CAAC,eAAe,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC;IAC3G,CAAC;IAED;;;OAGG,CACa,mBAAmB,GAAA;QAC/B,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;;;;;OAOG,CACa,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAA+B;2BAA7B,iEAAwB,KAAK;QACjG,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;QAEzC,IAAI,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtC,IAAI,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,4BAA4B,KAAK,YAAY,EAAE,CAAC;gBAC/F,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC3B,IAAI,CAAC,2BAA2B,CAAA,EAAA,sCAAA,KAAqC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtF,OAAO,CAAC,eAAe,GAAG,IAAI,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QACvF,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,OAAO,GAA4B,OAAO,CAAC,eAAe,CAAC;QACjE,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,SAAS;QACT,OAAO,CAAC,YAAY,0LAAG,0BAAA,AAAuB,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE/H,YAAY;+LACZ,6BAAA,AAA0B,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE3C,UAAU;QACV,MAAM,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,+BAA+B,CAAC;QACnG,kNAAA,AAAwB,EAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC,GAAG,CAAC,CAAC;QAEtE,iCAAiC;+LACjC,uBAAA,AAAoB,EAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;mMAE1C,kCAA+B,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAE1F,WAAW;QACX,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC,UAAU,CAAC,uBAAuB,GAAG,KAAK,CAAC;YAChD,IAAI,CAAC,2CAA2C,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClE,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC;YAC7E,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAA,EAAA,EAAA,CAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC;gBACxD,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAClC,CAAC;YACD,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;gBAC5B,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;gBACzB,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;gBAC5B,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;gBAC5B,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAC7B,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAC7B,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAE7B,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;oBACjE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC/C,OAAO,KAAK,CAAC;oBACjB,CAAC,MAAM,CAAC;wBACJ,mNAAA,AAAyB,EAAC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;oBACxE,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;gBAC5B,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;oBACjE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC/C,OAAO,KAAK,CAAC;oBACjB,CAAC,MAAM,CAAC;wBACJ,mNAAyB,AAAzB,EAA0B,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;oBACxE,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;gBAC5B,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;oBACjE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC/C,OAAO,KAAK,CAAC;oBACjB,CAAC,MAAM,CAAC;+MACJ,4BAAA,AAAyB,EAAC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;wBACpE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;oBAC9D,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;gBAC5B,CAAC;gBAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;oBACvE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAClD,OAAO,KAAK,CAAC;oBACjB,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;wBAC5B,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;wBAE1B,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;wBACxC,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,CAAC;wBAC3D,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,gLAAK,UAAO,CAAC,aAAa,CAAC;wBAC3F,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;wBAC1D,OAAO,CAAC,uBAAuB,GAC3B,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;wBAC1I,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;wBAExD,OAAQ,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;4BAC9C,gLAAK,UAAO,CAAC,aAAa;gCACtB,OAAO,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC;gCACpD,MAAM;4BACV,KAAK,qLAAO,CAAC,WAAW;gCACpB,OAAO,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;gCAClD,MAAM;4BACV,gLAAK,UAAO,CAAC,eAAe;gCACxB,OAAO,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,CAAC;gCACtD,MAAM;4BACV,gLAAK,UAAO,CAAC,WAAW;gCACpB,OAAO,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,CAAC;gCAClD,MAAM;4BACV,gLAAK,UAAO,CAAC,cAAc;gCACvB,OAAO,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,CAAC;gCACrD,MAAM;4BACV,gLAAK,UAAO,CAAC,oBAAoB;gCAC7B,OAAO,CAAC,iBAAiB,CAAC,+BAA+B,CAAC,CAAC;gCAC3D,MAAM;4BACV,gLAAK,UAAO,CAAC,0BAA0B;gCACnC,OAAO,CAAC,iBAAiB,CAAC,qCAAqC,CAAC,CAAC;gCACjE,MAAM;4BACV,gLAAK,UAAO,CAAC,mCAAmC;gCAC5C,OAAO,CAAC,iBAAiB,CAAC,6CAA6C,CAAC,CAAC;gCACzE,MAAM;4BACV,+KAAK,WAAO,CAAC,UAAU,CAAC;4BACxB,gLAAK,UAAO,CAAC,aAAa,CAAC;4BAC3B;gCACI,OAAO,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;gCACjD,MAAM;wBACd,CAAC;wBAED,OAAO,CAAC,6BAA6B,GAAS,IAAI,CAAC,kBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;oBAC1G,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;oBAC3B,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;gBAC5C,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;oBACnE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAChD,OAAO,KAAK,CAAC;oBACjB,CAAC,MAAM,CAAC;wBACJ,mNAAA,AAAyB,EAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;oBAC1E,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAC7B,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;oBACnE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAChD,OAAO,KAAK,CAAC;oBACjB,CAAC,MAAM,CAAC;+MACJ,4BAAA,AAAyB,EAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;wBACtE,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC;wBAC9D,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;oBACxD,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAC7B,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;oBACnE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAChD,OAAO,KAAK,CAAC;oBACjB,CAAC,MAAM,CAAC;8MACJ,6BAAA,AAAyB,EAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;wBACtE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,kCAAkC,CAAC;oBACjE,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAC7B,CAAC;gBAED,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;oBAC9G,wCAAwC;oBACxC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;wBAC/B,OAAO,KAAK,CAAC;oBACjB,CAAC,MAAM,CAAC;+MACJ,4BAAA,AAAyB,EAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;wBAE9D,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;wBACrC,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC;wBAClD,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC;oBAC3D,CAAC;oBAED,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAClE,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;oBACrB,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACzB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC7B,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;gBACtC,CAAC;gBAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;oBACvE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAClD,OAAO,KAAK,CAAC;oBACjB,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;wBACxB,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;wBAE1B,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;wBAC1D,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;wBACxD,OAAO,CAAC,6BAA6B,GAAS,IAAI,CAAC,kBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;oBAC1G,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;gBAC/B,CAAC;gBAED,OAAO,CAAC,gBAAgB,GAAG,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,CAAC;YAChF,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;gBACxB,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;gBACxB,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;gBACxB,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;gBAC3B,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACzB,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACzB,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;gBACrB,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;YAC/B,CAAC;YAED,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,iCAAiC,EAAE,CAAC;YAEpE,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,0BAA0B,CAAC;YAEjE,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,CAAC;YAEhE,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAEvD,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,KAAK,KAAA,IAAS,CAAC,SAAA,KAAA,KAAmB,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,8BAA8B,CAAC;YAE3I,OAAO,CAAC,mCAAmC,GAAG,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC;YAE7E,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,2CAA2C;QAC3I,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE5D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACzE,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,EAAE,CAAC;gBAChD,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAE3D,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;YACpG,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;QACxG,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,gBAAgB,CAAC,cAAc,EAAE,CAAC;gBAClC,UAAU;gBACV,IACI,IAAI,CAAC,yBAAyB,IAC9B,IAAI,CAAC,yBAAyB,IAC9B,IAAI,CAAC,0BAA0B,IAC/B,IAAI,CAAC,4BAA4B,IACjC,IAAI,CAAC,4BAA4B,EACnC,CAAC;oBACC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC;oBAEpG,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC;oBAEpG,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC;oBAE7G,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC,iCAAiC,CAAC;oBAE/E,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC;oBAE7G,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC;oBAEvG,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC5B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;gBAC3B,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;YAC5B,CAAC;QACL,CAAC;QAED,yCAAyC;QACzC,IAAI,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC5D,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;oBACvC,OAAO,KAAK,CAAC;gBACjB,CAAC;YACL,CAAC;QACL,CAAC;QAED,QAAQ;+LACR,wBAAA,AAAqB,EACjB,IAAI,EACJ,KAAK,EACL,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAClC,OAAO,EACP,IAAI,CAAC,4BAA4B,CACpC,CAAC;QAEF,kDAAkD;+LAClD,oCAAiC,AAAjC,EAAkC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAEjI,kBAAkB;QAClB,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,kDAAkD,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzE,UAAU;+LACV,8BAAA,AAA2B,EAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAE7D,kBAAkB;QAClB,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzD,qBAAqB;QACrB,IAAI,0BAA0B,GAAG,KAAK,CAAC;QAEvC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,aAAa,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACjD,OAAO,CAAC,eAAe,EAAE,CAAC;YAE1B,YAAY;YACZ,MAAM,SAAS,GAAG,2KAAI,kBAAe,EAAE,CAAC;YACxC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBACrB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;YAC3C,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACnB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YACzC,CAAC;YAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACf,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YACrC,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACnB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YACzC,CAAC;YAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACvB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBACd,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YACpC,CAAC;YAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;YAC1C,CAAC;YAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;YACjD,CAAC;mMAED,4BAAA,AAAyB,EAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAE3E,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACvB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBACzB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBACzB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC1B,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YACxC,CAAC;YAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;YAC1C,CAAC;YAED,YAAY;YACZ,MAAM,OAAO,GAAG;4KAAC,eAAY,CAAC,YAAY;aAAC,CAAC;YAE5C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,6JAAC,eAAY,CAAC,UAAU,CAAC,CAAC;YAC1C,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClB,OAAO,CAAC,IAAI,4JAAC,gBAAY,CAAC,WAAW,CAAC,CAAC;YAC3C,CAAC;YAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAA,EAAA,EAAA,CAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC;gBACxD,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;oBACpB,OAAO,CAAC,IAAI,CAAC,KAAqB,CAAE,CAAC,CAAC,IAApB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACtC,CAAC;YACL,CAAC;YAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACtB,OAAO,CAAC,IAAI,6JAAC,eAAY,CAAC,SAAS,CAAC,CAAC;YACzC,CAAC;mMAED,4BAAA,AAAyB,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;mMAC7D,gCAAA,AAA6B,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAChD,0NAAA,AAAgC,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;mMACzD,2CAAA,AAAwC,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAEjE,IAAI,UAAU,GAAG,SAAS,CAAC;YAE3B,MAAM,QAAQ,GAAG;gBACb,OAAO;gBACP,MAAM;gBACN,gBAAgB;gBAChB,cAAc;gBACd,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,gBAAgB;gBAChB,gBAAgB;gBAChB,YAAY;gBACZ,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,kBAAkB;gBAClB,gBAAgB;gBAChB,gBAAgB;gBAChB,YAAY;gBACZ,gBAAgB;gBAChB,kBAAkB;gBAClB,QAAQ;gBACR,eAAe;gBACf,eAAe;gBACf,eAAe;gBACf,kBAAkB;gBAClB,gBAAgB;gBAChB,gBAAgB;gBAChB,YAAY;gBACZ,cAAc;gBACd,gBAAgB;gBAChB,kBAAkB;gBAClB,kBAAkB;gBAClB,mBAAmB;gBACnB,cAAc;gBACd,qBAAqB;gBACrB,sBAAsB;gBACtB,mBAAmB;gBACnB,oBAAoB;gBACpB,qBAAqB;gBACrB,sBAAsB;gBACtB,qBAAqB;gBACrB,iBAAiB;gBACjB,qBAAqB;gBACrB,iBAAiB;gBACjB,0BAA0B;gBAC1B,qBAAqB;gBACrB,aAAa;gBACb,kBAAkB;gBAClB,wBAAwB;gBACxB,2BAA2B;gBAC3B,YAAY;aACf,CAAC;YAEF,MAAM,QAAQ,GAAG;gBACb,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,uBAAuB;gBACvB,qBAAqB;gBACrB,iBAAiB;gBACjB,iBAAiB;gBACjB,aAAa;gBACb,iBAAiB;gBACjB,uBAAuB;gBACvB,qBAAqB;gBACrB,aAAa;gBACb,cAAc;gBACd,iBAAiB;gBACjB,sBAAsB;gBACtB,uBAAuB;gBACvB,uBAAuB;aAC1B,CAAC;YAEF,MAAM,cAAc,GAAG;gBAAC,UAAU;gBAAE,OAAO;gBAAE,MAAM;aAAC,CAAC;YAErD,MAAM,eAAe,GAAG;gBAAE,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;gBAAE,2BAA2B,EAAE,OAAO,CAAC,qBAAqB;YAAA,CAAE,CAAC;YAE3I,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC,CAAC;YACjC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACpC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,OAAO,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACpC,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,cAAc,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,SAAS,CAAC;YACvC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,eAAe,CAAC;YAClD,IAAI,CAAC,2BAA2B,CAAA,IAAA,qCAAA,KAAoC,IAAI,CAAC,UAAU,CAAC,CAAC;uMAErF,kCAA+B,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;wLAE3E,uBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;wLAC3C,uBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAE3C,uLAAI,gCAA4B,EAAE,CAAC;mMAC/B,gCAA4B,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oMAChE,+BAA4B,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACpE,CAAC;mMAED,iCAAA,AAA8B,EAAyB;gBACnD,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,OAAO;gBAChB,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;aACrD,CAAC,CAAC;+LAEH,uBAAA,AAAoB,EAAC,QAAQ,CAAC,CAAC;YAE/B,MAAM,WAAW,GAAoC,CAAA,CAAE,CAAC;YAExD,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YAC7H,CAAC;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;YAEhC,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;YACtC,IAAI,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,CACvC,UAAU,EACc;gBACpB,UAAU,EAAE,OAAO;gBACnB,aAAa,EAAE,QAAQ;gBACvB,mBAAmB,EAAE,cAAc;gBACnC,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,SAAS;gBACpB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,eAAe;gBACf,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;gBAC9C,wBAAwB,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU;gBACpD,WAAW,EAAE,OAAO,CAAC,OAAO;gBAC5B,cAAc,EAAE,IAAI,CAAC,eAAe;gBACpC,yBAAyB,EAAE,IAAI,CAAC,cAAc,GACxC,SAAS,GACT,KAAK,IAAI,EAAE;oBACP,IAAI,IAAI,CAAC,eAAe,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;wBAC/C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,+BAA+B,CAAC,EAAE,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC;;;yBAAA;oBAC5G,CAAC,MAAM,CAAC;wBACJ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,EAAE,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC;;;yBAAA;oBACpG,CAAC;oBACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC/B,CAAC;aACV,EACD,MAAM,CACT,CAAC;YAEF,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,SAAS,CAAC;YAEvC,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;oBAClC,yBAAyB,CAAC,MAAM,GAAG,MAAM,CAAC;oBAC1C,yBAAyB,CAAC,OAAO,GAAG,OAAO,CAAC;oBAC5C,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC;gBAC/E,CAAC;gBAED,iDAAiD;gBACjD,IAAI,IAAI,CAAC,sBAAsB,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;oBACrE,MAAM,GAAG,cAAc,CAAC;oBACxB,OAAO,CAAC,iBAAiB,EAAE,CAAC;oBAE5B,0BAA0B,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAE3C,IAAI,aAAa,EAAE,CAAC;wBAChB,oDAAoD;wBACpD,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;wBAClC,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,KAAK,CAAC,mBAAmB,EAAE,CAAC;oBAC5B,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,WAAW,CAAC,mBAAmB,GAAG,0BAA0B,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5E,WAAW,CAAC,4BAA4B,GAAG,YAAY,CAAC;QAExD,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACa,kBAAkB,GAAA;QAC9B,uBAAuB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;QAChC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACtC,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACvC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC;QAC1C,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC;QAC1C,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACvC,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAExC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACtC,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAEhC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QACvC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAC/B,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QACvC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACtC,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,kBAAkB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;OAKG,CACa,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB,EAAA;QACtE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAA4B,OAAO,CAAC,eAAe,CAAC;QACjE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,iBAAiB;QACjB,IAAI,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7B,0BAA0B;QAC1B,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAErD,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;mMAEhG,kCAA+B,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,mBAAmB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEnH,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC;QAClC,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,sCAAsC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7D,gBAAgB;QAChB,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7E,QAAQ;+LACR,sBAAA,AAAmB,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;QAChC,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAChC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,CAAC,sBAAsB,EAAE,CAAC;gBAC9F,IAAI,gBAAgB,CAAC,cAAc,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;oBACrD,UAAU;oBACV,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,CAAC;wBAC3E,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;wBACnH,GAAG,CAAC,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;oBACxH,CAAC;oBAED,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,CAAC;wBAC3E,GAAG,CAAC,YAAY,CACZ,cAAc,EACd,qKAAI,SAAM,CACN,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,EAAE,EACrD,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,WAAW,EAAE,EACtD,IAAI,CAAC,wBAAwB,CAAC,IAAI,CACrC,EACD,IAAI,CAAC,wBAAwB,CAAC,KAAK,CACtC,CAAC;oBACN,CAAC;oBAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,CAAC;wBACjF,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;wBAC5H,GAAG,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;oBACjI,CAAC;oBAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,CAAC;wBACjF,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;wBAC5H,GAAG,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;oBACjI,CAAC;oBAED,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,CAAC;wBAC7E,GAAG,CAAC,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;wBACtH,GAAG,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;oBAC3H,CAAC;gBACL,CAAC;gBAED,WAAW;gBACX,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;oBACxB,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;wBACjE,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;+MACrG,oBAAA,AAAiB,EAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;oBAC5D,CAAC;oBAED,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;wBACjE,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;+MACrG,oBAAA,AAAiB,EAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;oBAC5D,CAAC;oBAED,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;wBACjE,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;+MACrG,oBAAA,AAAiB,EAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;oBAC5D,CAAC;oBAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE,CAAC;wBAC1B,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;oBACrD,CAAC;oBAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;wBACvE,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;wBACpF,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,CAAC,CAAC;wBAE3F,IAAU,IAAI,CAAC,kBAAmB,CAAC,eAAe,EAAE,CAAC;4BACjD,MAAM,WAAW,GAAgB,IAAI,CAAC,kBAAkB,CAAC;4BAEzD,GAAG,CAAC,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;4BAC1E,GAAG,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;wBACtE,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC9D,CAAC;oBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;wBACnE,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;+MACxG,oBAAiB,AAAjB,EAAkB,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;oBAC9D,CAAC;oBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;wBACnE,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;yBACxG,0MAAA,AAAiB,EAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;oBAC9D,CAAC;oBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;wBACnE,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;+MACxG,oBAAA,AAAiB,EAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;oBAC9D,CAAC;oBAED,IAAI,IAAI,CAAC,YAAY,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;wBAC9G,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;+MAC1H,oBAAiB,AAAjB,EAAkB,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;wBAElD,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;4BAChC,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBACtH,CAAC,MAAM,CAAC;4BACJ,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBACtH,CAAC;oBACL,CAAC;oBAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;wBACvE,IAAI,KAAK,GAAG,GAAG,CAAC;wBAChB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;4BAClC,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,0BAA0B,EAAE,CAAC,CAAC;4BAE3F,IAAU,IAAI,CAAC,kBAAmB,CAAC,KAAK,EAAE,CAAC;gCACvC,KAAK,GAAS,IAAI,CAAC,kBAAmB,CAAC,KAAK,CAAC;4BACjD,CAAC;wBACL,CAAC;wBACD,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAEpI,IAAU,IAAI,CAAC,kBAAmB,CAAC,eAAe,EAAE,CAAC;4BACjD,MAAM,WAAW,GAAgB,IAAI,CAAC,kBAAkB,CAAC;4BAEzD,GAAG,CAAC,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;4BAC1E,GAAG,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;wBACtE,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,aAAa;gBACb,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACjD,CAAC;gBAED,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBAE3E,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,iKAAC,UAAM,CAAC,aAAa,CAAC,CAAC;gBACxH,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEjE,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAC9E,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAChE,CAAC;YAED,WAAW;YACX,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;oBACjE,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC9D,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;oBACjE,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC9D,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;oBACjE,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC9D,CAAC;gBAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;oBACvE,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;wBACjC,MAAM,CAAC,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACxE,CAAC,MAAM,CAAC;wBACJ,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACtE,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;oBACnE,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAChE,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;oBACnE,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAChE,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;oBACnE,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAChE,CAAC;gBAED,IAAI,IAAI,CAAC,YAAY,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;oBAC9G,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBACxD,CAAC;gBAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,CAAC;oBACvE,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;wBACjC,MAAM,CAAC,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACxE,CAAC,MAAM,CAAC;wBACJ,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACtE,CAAC;gBACL,CAAC;YACL,CAAC;YAED,yBAAyB;YACzB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,+BAA+B,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzF,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAClC,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzD,aAAa;+LACb,gBAAA,AAAa,EAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAEnC,SAAS;YACT,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,8BAA8B,EAAE,CAAC;YACpE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/B,SAAS;YACT,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;uMAChD,aAAA,AAAU,EAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1E,CAAC;YAED,OAAO;YACP,IACI,AAAC,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,qJAAK,QAAK,CAAC,YAAY,CAAC,GAC3E,IAAI,CAAC,kBAAkB,IACvB,IAAI,CAAC,kBAAkB,IACvB,IAAI,CAAC,cAAc,IACnB,OAAO,CAAC,OAAO,EACjB,CAAC;gBACC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC1B,CAAC;YAED,MAAM;mMACN,oBAAA,AAAiB,EAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;YAEvC,gBAAgB;YAChB,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;uMAChC,4BAAA,AAAyB,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC5C,CAAC;YAED,IAAI,OAAO,CAAC,8BAA8B,EAAE,CAAC;oBACzC,IAAI;0DAAC,2BAA2B,wGAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YACtE,CAAC;YAED,aAAa;YACb,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;uMAC3B,eAAA,AAAY,EAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACzC,CAAC;YAED,mBAAmB;YACnB,IAAI,IAAI,CAAC,6BAA6B,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,EAAE,CAAC;gBAC/F,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACnD,GAAG,CAAC,MAAM,EAAE,CAAC;IACjB,CAAC;IAED;;;OAGG,CACa,cAAc,GAAA;QAC1B,MAAM,OAAO,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvC,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3G,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3G,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/F,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3G,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG,CACa,iBAAiB,GAAA;QAC7B,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;OAIG,CACa,UAAU,CAAC,OAAoB,EAAA;QAC3C,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACa,OAAO,CAAC,kBAA4B,EAAE,oBAA8B,EAAA;QAChF,IAAI,oBAAoB,EAAE,CAAC;+JAOvB,oBACA;yCAPI,CAAC,eAAe,0DAApB,sBAAsB,OAAO,EAAE,CAAC;yCAC5B,CAAC,eAAe,0DAApB,sBAAsB,OAAO,EAAE,CAAC;yCAC5B,CAAC,eAAe,0DAApB,sBAAsB,OAAO,EAAE,CAAC;4CAC5B,CAAC,kBAAkB,6DAAvB,yBAAyB,OAAO,EAAE,CAAC;0CAC/B,CAAC,gBAAgB,2DAArB,uBAAuB,OAAO,EAAE,CAAC;0CAC7B,CAAC,gBAAgB,2DAArB,uBAAuB,OAAO,EAAE,CAAC;sCAC7B,CAAC,YAAY,0EAAE,OAAO,EAAE,CAAC;0CACzB,CAAC,gBAAgB,kFAAE,OAAO,EAAE,CAAC;wCACjC,IAAI,CAAC,kBAAkB,sFAAE,OAAO,EAAE,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACtE,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAChG,CAAC;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;OAMG,CACa,KAAK,CAAC,IAAY,EAAqD;oCAAnD,iEAAiC,IAAI,YAAE,OAAO,0DAAG,EAAE;QACnF,MAAM,MAAM,iLAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE;YAAE,qBAAqB;QAAA,CAAE,CAAC,CAAC;QAE7H,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC;QAEjB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEpC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEpC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAU,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe,EAAA;QACnE,MAAM,QAAQ,iLAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEnH,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC3D,CAAC;wKAED,WAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEzD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,+EAA+E;IAC/E;;OAEG,CACI,MAAM,KAAK,qBAAqB,GAAA;QACnC,4KAAO,gBAAa,CAAC,qBAAqB,CAAC;IAC/C,CAAC;IACM,MAAM,KAAK,qBAAqB,CAAC,KAAc,EAAA;6KAClD,gBAAa,CAAC,qBAAqB,GAAG,KAAK,CAAC;IAChD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,oBAAoB,GAAA;QAClC,4KAAO,gBAAa,CAAC,oBAAoB,CAAC;IAC9C,CAAC;IACM,MAAM,KAAK,oBAAoB,CAAC,KAAc,EAAA;6KACjD,gBAAa,CAAC,oBAAoB,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,qBAAqB,GAAA;QACnC,4KAAO,gBAAa,CAAC,qBAAqB,CAAC;IAC/C,CAAC;IACM,MAAM,KAAK,qBAAqB,CAAC,KAAc,EAAA;6KAClD,gBAAa,CAAC,qBAAqB,GAAG,KAAK,CAAC;IAChD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,qBAAqB,GAAA;QACnC,4KAAO,gBAAa,CAAC,qBAAqB,CAAC;IAC/C,CAAC;IACM,MAAM,KAAK,qBAAqB,CAAC,KAAc,EAAA;6KAClD,gBAAa,CAAC,qBAAqB,GAAG,KAAK,CAAC;IAChD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,wBAAwB,GAAA;QACtC,4KAAO,gBAAa,CAAC,wBAAwB,CAAC;IAClD,CAAC;IACM,MAAM,KAAK,wBAAwB,CAAC,KAAc,EAAA;6KACrD,gBAAa,CAAC,wBAAwB,GAAG,KAAK,CAAC;IACnD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,sBAAsB,GAAA;QACpC,4KAAO,gBAAa,CAAC,sBAAsB,CAAC;IAChD,CAAC;IACM,MAAM,KAAK,sBAAsB,CAAC,KAAc,EAAA;6KACnD,gBAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACjD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,sBAAsB,GAAA;QACpC,4KAAO,gBAAa,CAAC,sBAAsB,CAAC;IAChD,CAAC;IACM,MAAM,KAAK,sBAAsB,CAAC,KAAc,EAAA;6KACnD,gBAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACjD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,kBAAkB,GAAA;QAChC,4KAAO,gBAAa,CAAC,kBAAkB,CAAC;IAC5C,CAAC;IACM,MAAM,KAAK,kBAAkB,CAAC,KAAc,EAAA;6KAC/C,gBAAa,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAC7C,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,sBAAsB,GAAA;QACpC,2KAAO,iBAAa,CAAC,sBAAsB,CAAC;IAChD,CAAC;IACM,MAAM,KAAK,sBAAsB,CAAC,KAAc,EAAA;6KACnD,gBAAa,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACjD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,wBAAwB,GAAA;QACtC,4KAAO,gBAAa,CAAC,wBAAwB,CAAC;IAClD,CAAC;IACM,MAAM,KAAK,wBAAwB,CAAC,KAAc,EAAA;6KACrD,gBAAa,CAAC,wBAAwB,GAAG,KAAK,CAAC;IACnD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,0BAA0B,GAAA;QACxC,4KAAO,gBAAa,CAAC,0BAA0B,CAAC;IACpD,CAAC;IACM,MAAM,KAAK,0BAA0B,CAAC,KAAc,EAAA;6KACvD,gBAAa,CAAC,0BAA0B,GAAG,KAAK,CAAC;IACrD,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,cAAc,GAAA;QAC5B,4KAAO,gBAAa,CAAC,cAAc,CAAC;IACxC,CAAC;IACM,MAAM,KAAK,cAAc,CAAC,KAAc,EAAA;6KAC3C,gBAAa,CAAC,cAAc,GAAG,KAAK,CAAC;IACzC,CAAC;IAv4CD;;;;;;;;OAQG,CACH,YAAY,IAAY,EAAE,KAAa,EAAE,SAAS,GAAG,KAAK,CAAA;QACtD,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,IAAI,gBAAgB,CAAC,SAAS,CAAC,CAAC;QA3hBnE,IAAA,CAAA,eAAe,GAA0B,IAAI,CAAC;QAQ9C,IAAA,CAAA,eAAe,GAA0B,IAAI,CAAC;QAQ9C,IAAA,CAAA,eAAe,GAA0B,IAAI,CAAC;QAU9C,IAAA,CAAA,kBAAkB,GAA0B,IAAI,CAAC;QASjD,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAS/C,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAQ/C,IAAA,CAAA,YAAY,GAA0B,IAAI,CAAC;QAU3C,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAU/C,IAAA,CAAA,kBAAkB,GAA0B,IAAI,CAAC;QAQzD;;;WAGG,CAEI,IAAA,CAAA,YAAY,GAAG,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C;;WAEG,CAEI,IAAA,CAAA,YAAY,GAAG,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C;;WAEG,CAEI,IAAA,CAAA,aAAa,GAAG,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C;;;WAGG,CAEI,IAAA,CAAA,aAAa,GAAG,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C;;;;WAIG,CAEI,IAAA,CAAA,aAAa,GAAG,EAAE,CAAC;QAGlB,IAAA,CAAA,2BAA2B,GAAG,KAAK,CAAC;QAQpC,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QAQnC,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QASjC,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAS9B,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAShC,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QASzB,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QAQjC,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QASrB,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAStC;;WAEG,CAEI,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAGxB,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QAOvB;;;WAGG,CAEI,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAEhC;;;;WAIG,CAEI,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAEhC;;WAEG,CAEI,IAAA,CAAA,WAAW,GAAG,GAAG,CAAC;QAGjB,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAsDhC,IAAA,CAAA,iCAAiC,GAAG,KAAK,CAAC;QAS1C,IAAA,CAAA,kCAAkC,GAAG,KAAK,CAAC;QAQ3C,IAAA,CAAA,sBAAsB,GAAG,CAAC,CAAC;QAQ3B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAQ1B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAQ1B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAQ1B,IAAA,CAAA,4BAA4B,GAAG,KAAK,CAAC;QAiErC,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAgIrB,IAAA,CAAA,cAAc,GAAG,iKAAI,aAAU,CAAsB,EAAE,CAAC,CAAC;QACzD,IAAA,CAAA,mBAAmB,GAAG,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1C,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QAc5C,IAAI,CAAC,SAAS,GAAG,8LAAI,yBAAsB,CAAC,IAAI,CAAC,CAAC;QAElD,2DAA2D;QAC3D,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,oBAAoB,GAAG,gLAAI,uBAAoB,EAAE,CAAC;QAEvD,IAAI,CAAC,uBAAuB,GAAG,GAAoC,EAAE;YACjE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAE5B,IAAI,gBAAgB,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;gBACjH,IAAI,CAAC,cAAc,CAAC,IAAI,CAAsB,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3E,CAAC;YAED,IAAI,gBAAgB,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;gBACjH,IAAI,CAAC,cAAc,CAAC,IAAI,CAAsB,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3E,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;YACpD,IAAI,CAAC,4CAA4C,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEnE,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B,CAAC,CAAC;IACN,CAAC;;AA1jBD;;;GAGG,CACW,iBAAA,SAAS,GAAG,KAAH,AAAQ,CAAC;0JAGxB,cAAA,EAAA;qKADP,qBAAA,AAAkB,EAAC,gBAAgB,CAAC;yDACiB;2JAK/C,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,yCAAyC,CAAC;wDACf;2JAGrC,aAAA,EAAA;KADP,qLAAkB,AAAlB,EAAmB,gBAAgB,CAAC;yDACiB;2JAK/C,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;wDACR;2JAGrC,aAAA,EAAA;qKADP,qBAAA,AAAkB,EAAC,gBAAgB,CAAC;yDACiB;AAO/C,wKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,yCAAyC,CAAC;wDACf;AAGrC,wKAAA,EAAA;qKADP,qBAAA,AAAkB,EAAC,mBAAmB,CAAC;4DACiB;CAMlD,uKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;2DACL;IAGxC,oKAAA,EAAA;qKADP,qBAAA,AAAkB,EAAC,iBAAiB,CAAC;0DACiB;CAMhD,uKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;yDACP;AAGtC,wKAAA,EAAA;qKADP,qBAAA,AAAkB,EAAC,iBAAiB,CAAC;0DACiB;AAKhD,wKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;yDACP;2JAGtC,aAAA,EAAA;qKADP,qBAAA,AAAkB,EAAC,aAAa,CAAC;sDACiB;IAO5C,oKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;qDACX;2JAGlC,aAAA,EAAA;qKADP,qBAAA,AAAkB,EAAC,iBAAiB,CAAC;0DACiB;2JAOhD,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;yDACP;2JAGtC,aAAA,EAAA;qKADP,qBAAA,AAAkB,EAAC,mBAAmB,CAAC;4DACiB;IAMlD,oKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;2DACL;2JAOzC,aAAA,EAAA;qKADN,oBAAiB,AAAjB,EAAkB,SAAS,CAAC;sDACa;2JAMnC,aAAA,EAAA;qKADN,oBAAA,AAAiB,EAAC,SAAS,CAAC;sDACa;2JAMnC,aAAA,EAAA;qKADN,oBAAA,AAAiB,EAAC,UAAU,CAAC;uDACa;IAOpC,oKAAA,EAAA;qKADN,oBAAA,AAAiB,EAAC,UAAU,CAAC;uDACa;IAQpC,oKAAA,EAAA;qKADN,YAAA,AAAS,EAAE;uDACc;2JAGlB,aAAA,EAAA;qKADP,YAAA,AAAS,EAAC,4BAA4B,CAAC;qEACI;2JAKrC,aAAA,EAAA;KADN,mLAAA,AAAgB,EAAC,yCAAyC,CAAC;oEACjB;AAGnC,wKAAA,EAAA;qKADP,YAAA,AAAS,EAAC,2BAA2B,CAAC;oEACI;0JAKpC,cAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;mEACX;IAGlC,oKAAA,EAAA;qKADP,YAAS,AAAT,EAAU,yBAAyB,CAAC;kEACI;2JAMlC,aAAA,EAAA;KADN,mLAAA,AAAgB,EAAC,kCAAkC,CAAC;iEACb;2JAGhC,aAAA,EAAA;IADP,6KAAA,AAAS,EAAC,sBAAsB,CAAC;+DACI;AAM/B,wKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8DAChB;CAG7B,uKAAA,EAAA;qKADP,YAAA,AAAS,EAAC,wBAAwB,CAAC;iEACI;2JAMjC,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;gEACd;2JAG/B,aAAA,EAAA;qKADP,YAAA,AAAS,EAAC,iBAAiB,CAAC;0DACI;2JAM1B,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,gCAAgC,CAAC;yDACnB;2JAGxB,aAAA,EAAA;qKADP,YAAS,AAAT,EAAU,yBAAyB,CAAC;kEACI;AAKlC,wKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;iEACb;AAGhC,wKAAA,EAAA;qKADP,YAAA,AAAS,EAAC,aAAa,CAAC;sDACI;AAMtB,wKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;qDACzB;2JAGpB,aAAA,EAAA;qKADP,YAAA,AAAS,EAAC,sBAAsB,CAAC;+DACI;2JAO/B,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8DAChB;2JAM9B,aAAA,EAAA;qKADN,YAAA,AAAS,EAAE;2DACoB;2JAGxB,aAAA,EAAA;IADP,6KAAS,AAAT,EAAU,WAAW,CAAC;oDACA;2JAKhB,aAAA,EAAA;KADN,mLAAA,AAAgB,EAAC,kCAAkC,CAAC;mDAC5B;2JAOlB,aAAA,EAAA;QADN,yKAAA,AAAS,EAAE;2DACoB;2JAQzB,aAAA,EAAA;qKADN,YAAA,AAAS,EAAE;2DACoB;2JAMzB,aAAA,EAAA;qKADN,YAAA,AAAS,EAAE;qDACa;AAGjB,wKAAA,EAAA;qKADP,YAAA,AAAS,EAAC,wBAAwB,CAAC;iEACI;2JAKjC,aAAA,EAAA;QADN,gLAAA,AAAgB,EAAC,kCAAkC,CAAC;gEACd;2JAI/B,aAAA,EAAA;oKADP,gCAAA,AAA4B,EAAC,0BAA0B,CAAC;mEACJ;2JAM9C,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,iCAAiC,CAAC;kEACD;2JAG3C,aAAA,EAAA;qKADP,+BAAA,AAA4B,EAAC,0BAA0B,CAAC;mEACJ;2JAM9C,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,wCAAwC,CAAC;kEACR;2JAG3C,aAAA,EAAA;qKADP,+BAAA,AAA4B,EAAC,6BAA6B,CAAC;sEACJ;2JAMjD,aAAA,EAAA;QADN,gLAAA,AAAgB,EAAC,iCAAiC,CAAC;qEACE;2JAG9C,aAAA,EAAA;IADP,gMAAA,AAA4B,EAAC,6BAA6B,CAAC;sEACJ;2JAMjD,aAAA,EAAA;QADN,gLAAA,AAAgB,EAAC,iCAAiC,CAAC;qEACE;2JAG9C,aAAA,EAAA;KADP,+LAAA,AAA4B,EAAC,2BAA2B,CAAC;oEACJ;IAM/C,oKAAA,EAAA;qKADN,mBAAgB,AAAhB,EAAiB,iCAAiC,CAAC;mEACA;CAG5C,uKAAA,EAAA;qKADP,YAAA,AAAS,EAAC,kCAAkC,CAAC;2EACI;2JAM3C,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,iCAAiC,CAAC;0EACH;2JAGzC,aAAA,EAAA;qKADP,YAAA,AAAS,EAAC,mCAAmC,CAAC;4EACI;CAK5C,uKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;2EACH;0JAG1C,cAAA,EAAA;qKADP,YAAA,AAAS,EAAC,uBAAuB,CAAC;gEACA;IAK5B,oKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,gCAAgC,CAAC;+DACd;2JAG7B,aAAA,EAAA;qKADP,YAAA,AAAS,EAAC,kBAAkB,CAAC;2DACI;2JAK3B,aAAA,EAAA;qKADN,mBAAgB,AAAhB,EAAiB,kCAAkC,CAAC;0DACpB;2JAGzB,aAAA,EAAA;KADP,4KAAA,AAAS,EAAC,kBAAkB,CAAC;2DACI;2JAK3B,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;0DACpB;2JAGzB,aAAA,EAAA;qKADP,YAAA,AAAS,EAAC,kBAAkB,CAAC;2DACI;AAK3B,wKAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;0DACpB;2JAGzB,aAAA,EAAA;KADP,4KAAA,AAAS,EAAC,6BAA6B,CAAC;sEACI;2JAKtC,aAAA,EAAA;qKADN,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;qEACL;gKA0kDhD,gBAAA,AAAa,EAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC;gJAE5D,QAAK,CAAC,sBAAsB,GAAG,CAAC,KAAY,EAAE,EAAE;IAC5C,OAAO,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AAC3D,CAAC,CAAC", "debugId": null}}]}