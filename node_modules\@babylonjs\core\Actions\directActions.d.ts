import { Action } from "./action.js";
import type { Condition } from "./condition.js";
import type { ActionEvent } from "./actionEvent.js";
/**
 * This defines an action responsible to toggle a boolean once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class SwitchBooleanAction extends Action {
    /**
     * The path to the boolean property in the target object
     */
    propertyPath: string;
    private _target;
    private _effectiveTarget;
    private _property;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param target defines the object containing the boolean
     * @param propertyPath defines the path to the boolean property in the target object
     * @param condition defines the trigger related conditions
     */
    constructor(triggerOptions: any, target: any, propertyPath: string, condition?: Condition);
    /** @internal */
    _prepare(): void;
    /**
     * Execute the action toggle the boolean value.
     */
    execute(): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
/**
 * This defines an action responsible to set a the state field of the target
 *  to a desired value once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class SetStateAction extends Action {
    /**
     * The value to store in the state field.
     */
    value: string;
    private _target;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param target defines the object containing the state property
     * @param value defines the value to store in the state field
     * @param condition defines the trigger related conditions
     */
    constructor(triggerOptions: any, target: any, value: string, condition?: Condition);
    /**
     * Execute the action and store the value on the target state property.
     */
    execute(): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
/**
 * This defines an action responsible to set a property of the target
 *  to a desired value once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class SetValueAction extends Action {
    /**
     * The path of the property to set in the target.
     */
    propertyPath: string;
    /**
     * The value to set in the property
     */
    value: any;
    private _target;
    private _effectiveTarget;
    private _property;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param target defines the object containing the property
     * @param propertyPath defines the path of the property to set in the target
     * @param value defines the value to set in the property
     * @param condition defines the trigger related conditions
     */
    constructor(triggerOptions: any, target: any, propertyPath: string, value: any, condition?: Condition);
    /** @internal */
    _prepare(): void;
    /**
     * Execute the action and set the targeted property to the desired value.
     */
    execute(): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
/**
 * This defines an action responsible to increment the target value
 *  to a desired value once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class IncrementValueAction extends Action {
    /**
     * The path of the property to increment in the target.
     */
    propertyPath: string;
    /**
     * The value we should increment the property by.
     */
    value: any;
    private _target;
    private _effectiveTarget;
    private _property;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param target defines the object containing the property
     * @param propertyPath defines the path of the property to increment in the target
     * @param value defines the value value we should increment the property by
     * @param condition defines the trigger related conditions
     */
    constructor(triggerOptions: any, target: any, propertyPath: string, value: any, condition?: Condition);
    /** @internal */
    _prepare(): void;
    /**
     * Execute the action and increment the target of the value amount.
     */
    execute(): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
/**
 * This defines an action responsible to start an animation once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class PlayAnimationAction extends Action {
    /**
     * Where the animation should start (animation frame)
     */
    from: number;
    /**
     * Where the animation should stop (animation frame)
     */
    to: number;
    /**
     * Define if the animation should loop or stop after the first play.
     */
    loop?: boolean;
    private _target;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param target defines the target animation or animation name
     * @param from defines from where the animation should start (animation frame)
     * @param to defines where the animation should stop (animation frame)
     * @param loop defines if the animation should loop or stop after the first play
     * @param condition defines the trigger related conditions
     */
    constructor(triggerOptions: any, target: any, from: number, to: number, loop?: boolean, condition?: Condition);
    /** @internal */
    _prepare(): void;
    /**
     * Execute the action and play the animation.
     */
    execute(): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
/**
 * This defines an action responsible to stop an animation once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class StopAnimationAction extends Action {
    private _target;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param target defines the target animation or animation name
     * @param condition defines the trigger related conditions
     */
    constructor(triggerOptions: any, target: any, condition?: Condition);
    /** @internal */
    _prepare(): void;
    /**
     * Execute the action and stop the animation.
     */
    execute(): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
/**
 * This defines an action responsible that does nothing once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class DoNothingAction extends Action {
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param condition defines the trigger related conditions
     */
    constructor(triggerOptions?: any, condition?: Condition);
    /**
     * Execute the action and do nothing.
     */
    execute(): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
/**
 * This defines an action responsible to trigger several actions once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class CombineAction extends Action {
    /**
     * The list of aggregated animations to run.
     */
    children: Action[];
    /**
     * defines if the children actions conditions should be check before execution
     */
    enableChildrenConditions: boolean;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param children defines the list of aggregated animations to run
     * @param condition defines the trigger related conditions
     * @param enableChildrenConditions defines if the children actions conditions should be check before execution
     */
    constructor(triggerOptions: any, children: Action[], condition?: Condition, enableChildrenConditions?: boolean);
    /** @internal */
    _prepare(): void;
    /**
     * Execute the action and executes all the aggregated actions.
     * @param evt event to execute
     */
    execute(evt: ActionEvent): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
/**
 * This defines an action responsible to run code (external event) once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class ExecuteCodeAction extends Action {
    /**
     * The callback function to run.
     */
    func: (evt: ActionEvent) => void;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param func defines the callback function to run
     * @param condition defines the trigger related conditions
     */
    constructor(triggerOptions: any, func: (evt: ActionEvent) => void, condition?: Condition);
    /**
     * Execute the action and run the attached code.
     * @param evt event to execute
     */
    execute(evt: ActionEvent): void;
}
/**
 * This defines an action responsible to set the parent property of the target once triggered.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare class SetParentAction extends Action {
    private _parent;
    private _target;
    /**
     * Instantiate the action
     * @param triggerOptions defines the trigger options
     * @param target defines the target containing the parent property
     * @param parent defines from where the animation should start (animation frame)
     * @param condition defines the trigger related conditions
     */
    constructor(triggerOptions: any, target: any, parent: any, condition?: Condition);
    /** @internal */
    _prepare(): void;
    /**
     * Execute the action and set the parent property.
     */
    execute(): void;
    /**
     * Serializes the actions and its related information.
     * @param parent defines the object to serialize in
     * @returns the serialized object
     */
    serialize(parent: any): any;
}
