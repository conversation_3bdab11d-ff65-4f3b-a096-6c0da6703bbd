{"version": 3, "file": "collider.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Collisions/collider.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAE/C,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAE5C,MAAM,oBAAoB,GAAG,CAAC,MAAe,EAAE,MAAe,EAAE,YAAqB,EAAE,YAAoB,EAAW,EAAE;IACpH,IAAI,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,YAAY,CAAC,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,YAAY,CAAC,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,YAAY,CAAC,CAAC,GAAG,YAAY,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC;QAC3C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,aAAa,GAAwF,CAAC;IACxG,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IACzC,OAAO,UAAU,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,IAAY;QAC1D,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;QAChB,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;QACrB,MAAM,WAAW,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YAClB,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAClC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAElC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;YACV,MAAM,IAAI,GAAG,EAAE,CAAC;YAChB,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,IAAI,CAAC;QACd,CAAC;QAED,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC;YACjB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;YACpB,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC;YACjB,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;YACpB,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACN,CAAC,CAAC,EAAE,CAAC;AAEL,gBAAgB;AAChB,MAAM,OAAO,QAAQ;IAArB;QACI,yGAAyG;QACzG,yDAAyD;QAoBjD,oBAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACjC,4BAAuB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACzC,gBAAW,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC7B,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,iBAAY,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9B,UAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACvB,kBAAa,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/B,sBAAiB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACnC,sBAAiB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACnC,wBAAmB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE7C,gBAAgB;QACT,YAAO,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QAC/B,gBAAgB;QACT,WAAM,GAAG,CAAC,CAAC;QAMlB,gBAAgB;QACT,oBAAe,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAChC,mBAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAChC,wBAAmB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAOrC,mBAAc,GAAG,CAAC,CAAC,CAAC;IAyZhC,CAAC;IArZG,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,IAAY;QACjC,IAAI,CAAC,cAAc,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,IAAW,gBAAgB;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED,UAAU;IACV;;OAEG;IACI,WAAW,CAAC,MAAe,EAAE,GAAY,EAAE,CAAS;QACvD,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QACrB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;QAC7D,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACnD,IAAI,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC;aAAM,CAAC;YACJ,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;QAEzB,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACzD,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAErD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QAEzD,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,qBAAqB,CAAC,KAAc,EAAE,EAAW,EAAE,EAAW,EAAE,EAAW,EAAE,CAAU;QAC1F,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC1C,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAE3C,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3E,IAAI,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAC1C,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACR,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3C,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5E,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACR,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3E,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,YAAqB,EAAE,YAAoB,EAAE,MAAe,EAAE,MAAe;QAChG,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAEtE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAErE,IAAI,QAAQ,GAAG,IAAI,CAAC,oBAAoB,GAAG,GAAG,GAAG,YAAY,EAAE,CAAC;YAC5D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,oBAAoB,GAAG,GAAG,CAAC,EAAE,CAAC;YAC/F,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,SAAiB,EAAE,kBAAgC,EAAE,EAAW,EAAE,EAAW,EAAE,EAAW,EAAE,WAAoB,EAAE,QAAsB;QACzJ,IAAI,EAAE,CAAC;QACP,IAAI,eAAe,GAAG,KAAK,CAAC;QAE5B,6CAA6C;QAC7C,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,kBAAkB,GAAG,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;YACjC,kBAAkB,CAAC,SAAS,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACtD,kBAAkB,CAAC,SAAS,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,aAAa,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAEpD,IAAI,CAAC,WAAW,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE,CAAC;YAC9E,OAAO;QACX,CAAC;QAED,MAAM,yBAAyB,GAAG,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClF,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAE5E,uFAAuF;QACvF,kEAAkE;QAClE,IAAI,QAAQ,CAAC,gBAAgB,IAAI,iBAAiB,GAAG,MAAM,EAAE,CAAC;YAC1D,OAAO;QACX,CAAC;QAED,IAAI,iBAAiB,IAAI,CAAC,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,GAAG,EAAE,CAAC;gBAC7C,OAAO;YACX,CAAC;YACD,eAAe,GAAG,IAAI,CAAC;YACvB,EAAE,GAAG,CAAC,CAAC;QACX,CAAC;aAAM,CAAC;YACJ,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,yBAAyB,CAAC,GAAG,iBAAiB,CAAC;YAC5D,IAAI,EAAE,GAAG,CAAC,GAAG,GAAG,yBAAyB,CAAC,GAAG,iBAAiB,CAAC;YAE/D,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;gBACV,MAAM,IAAI,GAAG,EAAE,CAAC;gBAChB,EAAE,GAAG,EAAE,CAAC;gBACR,EAAE,GAAG,IAAI,CAAC;YACd,CAAC;YAED,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;gBACvB,OAAO;YACX,CAAC;YAED,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;gBACT,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;YACD,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;gBACX,EAAE,GAAG,GAAG,CAAC;YACb,CAAC;QACL,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7C,IAAI,KAAK,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,GAAG,GAAG,CAAC;QAEZ,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAClF,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAE1D,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC7F,KAAK,GAAG,IAAI,CAAC;gBACb,CAAC,GAAG,EAAE,CAAC;gBACP,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAEpC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,IAAI,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,GAAG,GAAG,CAAC;YAE/C,IAAI,UAAU,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC;gBACpB,KAAK,GAAG,IAAI,CAAC;gBACb,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACxD,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,GAAG,GAAG,CAAC;YAE3C,UAAU,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC;gBACpB,KAAK,GAAG,IAAI,CAAC;gBACb,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC,GAAG,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACxD,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,GAAG,GAAG,CAAC;YAE3C,UAAU,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC;gBACpB,KAAK,GAAG,IAAI,CAAC;gBACb,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC;YAED,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,IAAI,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YACnD,IAAI,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9D,IAAI,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAEtE,CAAC,GAAG,iBAAiB,GAAG,CAAC,IAAI,CAAC,sBAAsB,GAAG,eAAe,GAAG,eAAe,CAAC;YACzF,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,eAAe,GAAG,mBAAmB,CAAC,CAAC;YACtH,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,GAAG,mBAAmB,GAAG,mBAAmB,CAAC;YAE/G,UAAU,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,MAAM,CAAC,GAAG,CAAC,eAAe,GAAG,UAAU,CAAC,IAAI,GAAG,mBAAmB,CAAC,GAAG,iBAAiB,CAAC;gBAExF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;oBACvB,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC;oBACpB,KAAK,GAAG,IAAI,CAAC;oBACb,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC3B,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAClD,CAAC;YACL,CAAC;YAED,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAC/C,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1D,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAElE,CAAC,GAAG,iBAAiB,GAAG,CAAC,IAAI,CAAC,sBAAsB,GAAG,eAAe,GAAG,eAAe,CAAC;YACzF,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,eAAe,GAAG,mBAAmB,CAAC,CAAC;YACtH,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,GAAG,mBAAmB,GAAG,mBAAmB,CAAC;YAC/G,UAAU,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,MAAM,CAAC,GAAG,CAAC,eAAe,GAAG,UAAU,CAAC,IAAI,GAAG,mBAAmB,CAAC,GAAG,iBAAiB,CAAC;gBAExF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;oBACvB,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC;oBACpB,KAAK,GAAG,IAAI,CAAC;oBACb,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC3B,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAClD,CAAC;YACL,CAAC;YAED,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACjC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACtD,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAC/C,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1D,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAElE,CAAC,GAAG,iBAAiB,GAAG,CAAC,IAAI,CAAC,sBAAsB,GAAG,eAAe,GAAG,eAAe,CAAC;YACzF,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,eAAe,GAAG,mBAAmB,CAAC,CAAC;YACtH,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC,GAAG,mBAAmB,GAAG,mBAAmB,CAAC;YAE/G,UAAU,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,MAAM,CAAC,GAAG,CAAC,eAAe,GAAG,UAAU,CAAC,IAAI,GAAG,mBAAmB,CAAC,GAAG,iBAAiB,CAAC;gBAExF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;oBACvB,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC;oBACpB,KAAK,GAAG,IAAI,CAAC;oBACb,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC3B,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAClD,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,MAAM,sBAAsB,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;YAEnE,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAChF,4FAA4F;gBAC5F,2DAA2D;gBAC3D,4CAA4C;gBAC5C,IAAI,QAAQ,CAAC,iBAAiB,EAAE,CAAC;oBAC7B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;oBAC1D,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC1D,CAAC;oBACD,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;oBACtD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;oBAC1D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC/B,CAAC;gBACD,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;YACjC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACI,QAAQ,CACX,kBAAgC,EAChC,GAAc,EACd,OAAqB,EACrB,UAAkB,EAClB,QAAgB,EAChB,KAAa,EACb,WAAoB,EACpB,QAAsB,EACtB,eAAyB,EACzB,gBAAyB,KAAK;QAE9B,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;oBACzC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBAClB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACtB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAEtB,8DAA8D;oBAC9D,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;wBACpB,SAAS;oBACb,CAAC;oBACD,6CAA6C;oBAC7C,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;wBACpC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;oBACjF,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;oBACjF,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChD,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;oBAC1B,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAE9B,IAAI,MAAM,KAAK,UAAU,EAAE,CAAC;wBACxB,CAAC,IAAI,CAAC,CAAC;wBACP,SAAS;oBACb,CAAC;oBAED,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;oBACvB,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;oBACvB,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;oBAEvB,8DAA8D;oBAC9D,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;wBACpB,SAAS;oBACb,CAAC;oBAED,6CAA6C;oBAC7C,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;wBACpC,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;oBACjF,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;oBACjF,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBAEtB,IAAI,eAAe,EAAE,CAAC;oBAClB,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACjF,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACjF,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC5C,MAAM,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;gBACnC,MAAM,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;gBACvC,MAAM,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;gBAEvC,IAAI,eAAe,EAAE,CAAC;oBAClB,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACjF,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACjF,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,GAAY,EAAE,GAAY,EAAE,cAAuB;QACnE,2CAA2C;QAE3C,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAE1C,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,gEAAgE;YAChE,mEAAmE;YACnE,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACzE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACnC,OAAO;QACX,CAAC;aAAM,CAAC;YACJ,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACvD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACvC,CAAC;QAED,uCAAuC;QAEvC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAClE,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;QACnC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE3E,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACzC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE5D,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,CAAC,0CAA0C,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAC9J,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAE/D,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;IACtE,CAAC;;AA5bD;;GAEG;AACW,yBAAgB,GAAG,KAAK,AAAR,CAAS", "sourcesContent": ["import type { Nullable, IndicesArray } from \"../types\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Plane } from \"../Maths/math.plane\";\r\n\r\nconst IntersectBoxAaSphere = (boxMin: Vector3, boxMax: Vector3, sphereCenter: Vector3, sphereRadius: number): boolean => {\r\n    if (boxMin.x > sphereCenter.x + sphereRadius) {\r\n        return false;\r\n    }\r\n\r\n    if (sphereCenter.x - sphereRadius > boxMax.x) {\r\n        return false;\r\n    }\r\n\r\n    if (boxMin.y > sphereCenter.y + sphereRadius) {\r\n        return false;\r\n    }\r\n\r\n    if (sphereCenter.y - sphereRadius > boxMax.y) {\r\n        return false;\r\n    }\r\n\r\n    if (boxMin.z > sphereCenter.z + sphereRadius) {\r\n        return false;\r\n    }\r\n\r\n    if (sphereCenter.z - sphereRadius > boxMax.z) {\r\n        return false;\r\n    }\r\n\r\n    return true;\r\n};\r\n\r\nconst GetLowestRoot: (a: number, b: number, c: number, maxR: number) => { root: number; found: boolean } = (function () {\r\n    const result = { root: 0, found: false };\r\n    return function (a: number, b: number, c: number, maxR: number) {\r\n        result.root = 0;\r\n        result.found = false;\r\n        const determinant = b * b - 4.0 * a * c;\r\n        if (determinant < 0) {\r\n            return result;\r\n        }\r\n\r\n        const sqrtD = Math.sqrt(determinant);\r\n        let r1 = (-b - sqrtD) / (2.0 * a);\r\n        let r2 = (-b + sqrtD) / (2.0 * a);\r\n\r\n        if (r1 > r2) {\r\n            const temp = r2;\r\n            r2 = r1;\r\n            r1 = temp;\r\n        }\r\n\r\n        if (r1 > 0 && r1 < maxR) {\r\n            result.root = r1;\r\n            result.found = true;\r\n            return result;\r\n        }\r\n\r\n        if (r2 > 0 && r2 < maxR) {\r\n            result.root = r2;\r\n            result.found = true;\r\n            return result;\r\n        }\r\n\r\n        return result;\r\n    };\r\n})();\r\n\r\n/** @internal */\r\nexport class Collider {\r\n    // Implementation of the \"Improved Collision detection and Response\" algorithm proposed by Kasper Fauerby\r\n    // https://www.peroxide.dk/papers/collision/collision.pdf\r\n\r\n    /** Define if a collision was found */\r\n    public collisionFound: boolean;\r\n\r\n    /**\r\n     * Define last intersection point in local space\r\n     */\r\n    public intersectionPoint: Vector3;\r\n\r\n    /**\r\n     * Define last collided mesh\r\n     */\r\n    public collidedMesh: Nullable<AbstractMesh>;\r\n\r\n    /**\r\n     * If true, it check for double sided faces and only returns 1 collision instead of 2\r\n     */\r\n    public static DoubleSidedCheck = false;\r\n\r\n    private _collisionPoint = Vector3.Zero();\r\n    private _planeIntersectionPoint = Vector3.Zero();\r\n    private _tempVector = Vector3.Zero();\r\n    private _tempVector2 = Vector3.Zero();\r\n    private _tempVector3 = Vector3.Zero();\r\n    private _tempVector4 = Vector3.Zero();\r\n    private _edge = Vector3.Zero();\r\n    private _baseToVertex = Vector3.Zero();\r\n    private _destinationPoint = Vector3.Zero();\r\n    private _slidePlaneNormal = Vector3.Zero();\r\n    private _displacementVector = Vector3.Zero();\r\n\r\n    /** @internal */\r\n    public _radius = Vector3.One();\r\n    /** @internal */\r\n    public _retry = 0;\r\n    private _velocity: Vector3;\r\n    private _basePoint: Vector3;\r\n    private _epsilon: number;\r\n    /** @internal */\r\n    public _velocityWorldLength: number;\r\n    /** @internal */\r\n    public _basePointWorld = Vector3.Zero();\r\n    private _velocityWorld = Vector3.Zero();\r\n    private _normalizedVelocity = Vector3.Zero();\r\n    /** @internal */\r\n    public _initialVelocity: Vector3;\r\n    /** @internal */\r\n    public _initialPosition: Vector3;\r\n    private _nearestDistance: number;\r\n\r\n    private _collisionMask = -1;\r\n    private _velocitySquaredLength: number;\r\n    private _nearestDistanceSquared: number;\r\n\r\n    public get collisionMask(): number {\r\n        return this._collisionMask;\r\n    }\r\n\r\n    public set collisionMask(mask: number) {\r\n        this._collisionMask = !isNaN(mask) ? mask : -1;\r\n    }\r\n\r\n    /**\r\n     * Gets the plane normal used to compute the sliding response (in local space)\r\n     */\r\n    public get slidePlaneNormal(): Vector3 {\r\n        return this._slidePlaneNormal;\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * @internal\r\n     */\r\n    public _initialize(source: Vector3, dir: Vector3, e: number): void {\r\n        this._velocity = dir;\r\n        this._velocitySquaredLength = this._velocity.lengthSquared();\r\n        const len = Math.sqrt(this._velocitySquaredLength);\r\n        if (len === 0 || len === 1.0) {\r\n            this._normalizedVelocity.copyFromFloats(dir._x, dir._y, dir._z);\r\n        } else {\r\n            dir.scaleToRef(1.0 / len, this._normalizedVelocity);\r\n        }\r\n        this._basePoint = source;\r\n\r\n        source.multiplyToRef(this._radius, this._basePointWorld);\r\n        dir.multiplyToRef(this._radius, this._velocityWorld);\r\n\r\n        this._velocityWorldLength = this._velocityWorld.length();\r\n\r\n        this._epsilon = e;\r\n        this.collisionFound = false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _checkPointInTriangle(point: Vector3, pa: Vector3, pb: Vector3, pc: Vector3, n: Vector3): boolean {\r\n        pa.subtractToRef(point, this._tempVector);\r\n        pb.subtractToRef(point, this._tempVector2);\r\n\r\n        Vector3.CrossToRef(this._tempVector, this._tempVector2, this._tempVector4);\r\n        let d = Vector3.Dot(this._tempVector4, n);\r\n        if (d < 0) {\r\n            return false;\r\n        }\r\n\r\n        pc.subtractToRef(point, this._tempVector3);\r\n        Vector3.CrossToRef(this._tempVector2, this._tempVector3, this._tempVector4);\r\n        d = Vector3.Dot(this._tempVector4, n);\r\n        if (d < 0) {\r\n            return false;\r\n        }\r\n\r\n        Vector3.CrossToRef(this._tempVector3, this._tempVector, this._tempVector4);\r\n        d = Vector3.Dot(this._tempVector4, n);\r\n        return d >= 0;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _canDoCollision(sphereCenter: Vector3, sphereRadius: number, vecMin: Vector3, vecMax: Vector3): boolean {\r\n        const distance = Vector3.Distance(this._basePointWorld, sphereCenter);\r\n\r\n        const max = Math.max(this._radius.x, this._radius.y, this._radius.z);\r\n\r\n        if (distance > this._velocityWorldLength + max + sphereRadius) {\r\n            return false;\r\n        }\r\n\r\n        if (!IntersectBoxAaSphere(vecMin, vecMax, this._basePointWorld, this._velocityWorldLength + max)) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _testTriangle(faceIndex: number, trianglePlaneArray: Array<Plane>, p1: Vector3, p2: Vector3, p3: Vector3, hasMaterial: boolean, hostMesh: AbstractMesh): void {\r\n        let t0;\r\n        let embeddedInPlane = false;\r\n\r\n        //defensive programming, actually not needed.\r\n        if (!trianglePlaneArray) {\r\n            trianglePlaneArray = [];\r\n        }\r\n\r\n        if (!trianglePlaneArray[faceIndex]) {\r\n            trianglePlaneArray[faceIndex] = new Plane(0, 0, 0, 0);\r\n            trianglePlaneArray[faceIndex].copyFromPoints(p1, p2, p3);\r\n        }\r\n\r\n        const trianglePlane = trianglePlaneArray[faceIndex];\r\n\r\n        if (!hasMaterial && !trianglePlane.isFrontFacingTo(this._normalizedVelocity, 0)) {\r\n            return;\r\n        }\r\n\r\n        const signedDistToTrianglePlane = trianglePlane.signedDistanceTo(this._basePoint);\r\n        const normalDotVelocity = Vector3.Dot(trianglePlane.normal, this._velocity);\r\n\r\n        // if DoubleSidedCheck is false(default), a double sided face will be consided 2 times.\r\n        // if true, it discard the faces having normal not facing velocity\r\n        if (Collider.DoubleSidedCheck && normalDotVelocity > 0.0001) {\r\n            return;\r\n        }\r\n\r\n        if (normalDotVelocity == 0) {\r\n            if (Math.abs(signedDistToTrianglePlane) >= 1.0) {\r\n                return;\r\n            }\r\n            embeddedInPlane = true;\r\n            t0 = 0;\r\n        } else {\r\n            t0 = (-1.0 - signedDistToTrianglePlane) / normalDotVelocity;\r\n            let t1 = (1.0 - signedDistToTrianglePlane) / normalDotVelocity;\r\n\r\n            if (t0 > t1) {\r\n                const temp = t1;\r\n                t1 = t0;\r\n                t0 = temp;\r\n            }\r\n\r\n            if (t0 > 1.0 || t1 < 0.0) {\r\n                return;\r\n            }\r\n\r\n            if (t0 < 0) {\r\n                t0 = 0;\r\n            }\r\n            if (t0 > 1.0) {\r\n                t0 = 1.0;\r\n            }\r\n        }\r\n\r\n        this._collisionPoint.copyFromFloats(0, 0, 0);\r\n\r\n        let found = false;\r\n        let t = 1.0;\r\n\r\n        if (!embeddedInPlane) {\r\n            this._basePoint.subtractToRef(trianglePlane.normal, this._planeIntersectionPoint);\r\n            this._velocity.scaleToRef(t0, this._tempVector);\r\n            this._planeIntersectionPoint.addInPlace(this._tempVector);\r\n\r\n            if (this._checkPointInTriangle(this._planeIntersectionPoint, p1, p2, p3, trianglePlane.normal)) {\r\n                found = true;\r\n                t = t0;\r\n                this._collisionPoint.copyFrom(this._planeIntersectionPoint);\r\n            }\r\n        }\r\n\r\n        if (!found) {\r\n            let a = this._velocitySquaredLength;\r\n\r\n            this._basePoint.subtractToRef(p1, this._tempVector);\r\n            let b = 2.0 * Vector3.Dot(this._velocity, this._tempVector);\r\n            let c = this._tempVector.lengthSquared() - 1.0;\r\n\r\n            let lowestRoot = GetLowestRoot(a, b, c, t);\r\n            if (lowestRoot.found) {\r\n                t = lowestRoot.root;\r\n                found = true;\r\n                this._collisionPoint.copyFrom(p1);\r\n            }\r\n\r\n            this._basePoint.subtractToRef(p2, this._tempVector);\r\n            b = 2.0 * Vector3.Dot(this._velocity, this._tempVector);\r\n            c = this._tempVector.lengthSquared() - 1.0;\r\n\r\n            lowestRoot = GetLowestRoot(a, b, c, t);\r\n            if (lowestRoot.found) {\r\n                t = lowestRoot.root;\r\n                found = true;\r\n                this._collisionPoint.copyFrom(p2);\r\n            }\r\n\r\n            this._basePoint.subtractToRef(p3, this._tempVector);\r\n            b = 2.0 * Vector3.Dot(this._velocity, this._tempVector);\r\n            c = this._tempVector.lengthSquared() - 1.0;\r\n\r\n            lowestRoot = GetLowestRoot(a, b, c, t);\r\n            if (lowestRoot.found) {\r\n                t = lowestRoot.root;\r\n                found = true;\r\n                this._collisionPoint.copyFrom(p3);\r\n            }\r\n\r\n            p2.subtractToRef(p1, this._edge);\r\n            p1.subtractToRef(this._basePoint, this._baseToVertex);\r\n            let edgeSquaredLength = this._edge.lengthSquared();\r\n            let edgeDotVelocity = Vector3.Dot(this._edge, this._velocity);\r\n            let edgeDotBaseToVertex = Vector3.Dot(this._edge, this._baseToVertex);\r\n\r\n            a = edgeSquaredLength * -this._velocitySquaredLength + edgeDotVelocity * edgeDotVelocity;\r\n            b = 2 * (edgeSquaredLength * Vector3.Dot(this._velocity, this._baseToVertex) - edgeDotVelocity * edgeDotBaseToVertex);\r\n            c = edgeSquaredLength * (1.0 - this._baseToVertex.lengthSquared()) + edgeDotBaseToVertex * edgeDotBaseToVertex;\r\n\r\n            lowestRoot = GetLowestRoot(a, b, c, t);\r\n            if (lowestRoot.found) {\r\n                const f = (edgeDotVelocity * lowestRoot.root - edgeDotBaseToVertex) / edgeSquaredLength;\r\n\r\n                if (f >= 0.0 && f <= 1.0) {\r\n                    t = lowestRoot.root;\r\n                    found = true;\r\n                    this._edge.scaleInPlace(f);\r\n                    p1.addToRef(this._edge, this._collisionPoint);\r\n                }\r\n            }\r\n\r\n            p3.subtractToRef(p2, this._edge);\r\n            p2.subtractToRef(this._basePoint, this._baseToVertex);\r\n            edgeSquaredLength = this._edge.lengthSquared();\r\n            edgeDotVelocity = Vector3.Dot(this._edge, this._velocity);\r\n            edgeDotBaseToVertex = Vector3.Dot(this._edge, this._baseToVertex);\r\n\r\n            a = edgeSquaredLength * -this._velocitySquaredLength + edgeDotVelocity * edgeDotVelocity;\r\n            b = 2 * (edgeSquaredLength * Vector3.Dot(this._velocity, this._baseToVertex) - edgeDotVelocity * edgeDotBaseToVertex);\r\n            c = edgeSquaredLength * (1.0 - this._baseToVertex.lengthSquared()) + edgeDotBaseToVertex * edgeDotBaseToVertex;\r\n            lowestRoot = GetLowestRoot(a, b, c, t);\r\n            if (lowestRoot.found) {\r\n                const f = (edgeDotVelocity * lowestRoot.root - edgeDotBaseToVertex) / edgeSquaredLength;\r\n\r\n                if (f >= 0.0 && f <= 1.0) {\r\n                    t = lowestRoot.root;\r\n                    found = true;\r\n                    this._edge.scaleInPlace(f);\r\n                    p2.addToRef(this._edge, this._collisionPoint);\r\n                }\r\n            }\r\n\r\n            p1.subtractToRef(p3, this._edge);\r\n            p3.subtractToRef(this._basePoint, this._baseToVertex);\r\n            edgeSquaredLength = this._edge.lengthSquared();\r\n            edgeDotVelocity = Vector3.Dot(this._edge, this._velocity);\r\n            edgeDotBaseToVertex = Vector3.Dot(this._edge, this._baseToVertex);\r\n\r\n            a = edgeSquaredLength * -this._velocitySquaredLength + edgeDotVelocity * edgeDotVelocity;\r\n            b = 2 * (edgeSquaredLength * Vector3.Dot(this._velocity, this._baseToVertex) - edgeDotVelocity * edgeDotBaseToVertex);\r\n            c = edgeSquaredLength * (1.0 - this._baseToVertex.lengthSquared()) + edgeDotBaseToVertex * edgeDotBaseToVertex;\r\n\r\n            lowestRoot = GetLowestRoot(a, b, c, t);\r\n            if (lowestRoot.found) {\r\n                const f = (edgeDotVelocity * lowestRoot.root - edgeDotBaseToVertex) / edgeSquaredLength;\r\n\r\n                if (f >= 0.0 && f <= 1.0) {\r\n                    t = lowestRoot.root;\r\n                    found = true;\r\n                    this._edge.scaleInPlace(f);\r\n                    p3.addToRef(this._edge, this._collisionPoint);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (found) {\r\n            const distToCollisionSquared = t * t * this._velocitySquaredLength;\r\n\r\n            if (!this.collisionFound || distToCollisionSquared < this._nearestDistanceSquared) {\r\n                // if collisionResponse is false, collision is not found but the collidedMesh is set anyway.\r\n                // onCollide observable are triggered if collideMesh is set\r\n                // this allow trigger volumes to be created.\r\n                if (hostMesh.collisionResponse) {\r\n                    if (!this.intersectionPoint) {\r\n                        this.intersectionPoint = this._collisionPoint.clone();\r\n                    } else {\r\n                        this.intersectionPoint.copyFrom(this._collisionPoint);\r\n                    }\r\n                    this._nearestDistanceSquared = distToCollisionSquared;\r\n                    this._nearestDistance = Math.sqrt(distToCollisionSquared);\r\n                    this.collisionFound = true;\r\n                }\r\n                this.collidedMesh = hostMesh;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _collide(\r\n        trianglePlaneArray: Array<Plane>,\r\n        pts: Vector3[],\r\n        indices: IndicesArray,\r\n        indexStart: number,\r\n        indexEnd: number,\r\n        decal: number,\r\n        hasMaterial: boolean,\r\n        hostMesh: AbstractMesh,\r\n        invertTriangles?: boolean,\r\n        triangleStrip: boolean = false\r\n    ): void {\r\n        if (triangleStrip) {\r\n            if (!indices || indices.length === 0) {\r\n                for (let i = 0; i < pts.length - 2; i += 1) {\r\n                    const p1 = pts[i];\r\n                    const p2 = pts[i + 1];\r\n                    const p3 = pts[i + 2];\r\n\r\n                    // stay defensive and don't check against undefined positions.\r\n                    if (!p1 || !p2 || !p3) {\r\n                        continue;\r\n                    }\r\n                    // Handles strip faces one on two is reversed\r\n                    if ((invertTriangles ? 1 : 0) ^ i % 2) {\r\n                        this._testTriangle(i, trianglePlaneArray, p1, p2, p3, hasMaterial, hostMesh);\r\n                    } else {\r\n                        this._testTriangle(i, trianglePlaneArray, p2, p1, p3, hasMaterial, hostMesh);\r\n                    }\r\n                }\r\n            } else {\r\n                for (let i = indexStart; i < indexEnd - 2; i += 1) {\r\n                    const indexA = indices[i];\r\n                    const indexB = indices[i + 1];\r\n                    const indexC = indices[i + 2];\r\n\r\n                    if (indexC === 0xffffffff) {\r\n                        i += 2;\r\n                        continue;\r\n                    }\r\n\r\n                    const p1 = pts[indexA];\r\n                    const p2 = pts[indexB];\r\n                    const p3 = pts[indexC];\r\n\r\n                    // stay defensive and don't check against undefined positions.\r\n                    if (!p1 || !p2 || !p3) {\r\n                        continue;\r\n                    }\r\n\r\n                    // Handles strip faces one on two is reversed\r\n                    if ((invertTriangles ? 1 : 0) ^ i % 2) {\r\n                        this._testTriangle(i, trianglePlaneArray, p1, p2, p3, hasMaterial, hostMesh);\r\n                    } else {\r\n                        this._testTriangle(i, trianglePlaneArray, p2, p1, p3, hasMaterial, hostMesh);\r\n                    }\r\n                }\r\n            }\r\n        } else if (!indices || indices.length === 0) {\r\n            for (let i = 0; i < pts.length; i += 3) {\r\n                const p1 = pts[i];\r\n                const p2 = pts[i + 1];\r\n                const p3 = pts[i + 2];\r\n\r\n                if (invertTriangles) {\r\n                    this._testTriangle(i, trianglePlaneArray, p1, p2, p3, hasMaterial, hostMesh);\r\n                } else {\r\n                    this._testTriangle(i, trianglePlaneArray, p3, p2, p1, hasMaterial, hostMesh);\r\n                }\r\n            }\r\n        } else {\r\n            for (let i = indexStart; i < indexEnd; i += 3) {\r\n                const p1 = pts[indices[i] - decal];\r\n                const p2 = pts[indices[i + 1] - decal];\r\n                const p3 = pts[indices[i + 2] - decal];\r\n\r\n                if (invertTriangles) {\r\n                    this._testTriangle(i, trianglePlaneArray, p1, p2, p3, hasMaterial, hostMesh);\r\n                } else {\r\n                    this._testTriangle(i, trianglePlaneArray, p3, p2, p1, hasMaterial, hostMesh);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getResponse(pos: Vector3, vel: Vector3, slideOnCollide: boolean): void {\r\n        // Handle straight movement up to collision\r\n\r\n        pos.addToRef(vel, this._destinationPoint);\r\n\r\n        if (!slideOnCollide) {\r\n            // Move to one \"close distance\" less than the collision point to\r\n            // prevent any collision penetration from floating point inaccuracy\r\n            vel.scaleInPlace((this._nearestDistance - this._epsilon) / vel.length());\r\n            this._basePoint.addToRef(vel, pos);\r\n            return;\r\n        } else {\r\n            vel.scaleInPlace(this._nearestDistance / vel.length());\r\n            this._basePoint.addToRef(vel, pos);\r\n        }\r\n\r\n        // Handle slide movement past collision\r\n\r\n        pos.subtractToRef(this.intersectionPoint, this._slidePlaneNormal);\r\n        this._slidePlaneNormal.normalize();\r\n        this._slidePlaneNormal.scaleToRef(this._epsilon, this._displacementVector);\r\n\r\n        pos.addInPlace(this._displacementVector);\r\n        this.intersectionPoint.addInPlace(this._displacementVector);\r\n\r\n        this._slidePlaneNormal.scaleInPlace(Plane.SignedDistanceToPlaneFromPositionAndNormal(this.intersectionPoint, this._slidePlaneNormal, this._destinationPoint));\r\n        this._destinationPoint.subtractInPlace(this._slidePlaneNormal);\r\n\r\n        this._destinationPoint.subtractToRef(this.intersectionPoint, vel);\r\n    }\r\n}\r\n"]}