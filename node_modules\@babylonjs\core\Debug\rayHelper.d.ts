import type { Nullable } from "../types.js";
import type { Ray } from "../Culling/ray.js";
import { Vector3 } from "../Maths/math.vector.js";
import type { Color3 } from "../Maths/math.color.js";
import type { Scene } from "../scene.js";
import type { AbstractMesh } from "../Meshes/abstractMesh.js";
/**
 * As raycast might be hard to debug, the RayHelper can help rendering the different rays
 * in order to better appreciate the issue one might have.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/picking_collisions#debugging
 */
export declare class RayHelper {
    /**
     * Defines the ray we are currently trying to visualize.
     */
    ray: Nullable<Ray>;
    private _renderPoints;
    private _renderLine;
    private _renderFunction;
    private _scene;
    private _onAfterRenderObserver;
    private _onAfterStepObserver;
    private _attachedToMesh;
    private _meshSpaceDirection;
    private _meshSpaceOrigin;
    /**
     * Helper function to create a colored helper in a scene in one line.
     * @param ray Defines the ray we are currently trying to visualize
     * @param scene Defines the scene the ray is used in
     * @param color Defines the color we want to see the ray in
     * @returns The newly created ray helper.
     */
    static CreateAndShow(ray: Ray, scene: Scene, color: Color3): RayHelper;
    /**
     * Instantiate a new ray helper.
     * As raycast might be hard to debug, the RayHelper can help rendering the different rays
     * in order to better appreciate the issue one might have.
     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/picking_collisions#debugging
     * @param ray Defines the ray we are currently trying to visualize
     */
    constructor(ray: Ray);
    /**
     * Shows the ray we are willing to debug.
     * @param scene Defines the scene the ray needs to be rendered in
     * @param color Defines the color the ray needs to be rendered in
     */
    show(scene: Scene, color?: Color3): void;
    /**
     * Hides the ray we are debugging.
     */
    hide(): void;
    private _render;
    /**
     * Attach a ray helper to a mesh so that we can easily see its orientation for instance or information like its normals.
     * @param mesh Defines the mesh we want the helper attached to
     * @param meshSpaceDirection Defines the direction of the Ray in mesh space (local space of the mesh node)
     * @param meshSpaceOrigin Defines the origin of the Ray in mesh space (local space of the mesh node)
     * @param length Defines the length of the ray
     */
    attachToMesh(mesh: AbstractMesh, meshSpaceDirection?: Vector3, meshSpaceOrigin?: Vector3, length?: number): void;
    /**
     * Detach the ray helper from the mesh it has previously been attached to.
     */
    detachFromMesh(): void;
    private _updateToMesh;
    /**
     * Dispose the helper and release its associated resources.
     */
    dispose(): void;
}
