{"version": 3, "file": "volumeAudioSubNode.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/abstractAudio/subNodes/volumeAudioSubNode.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,qBAAqB,EAAE,MAAM,kCAAkC,CAAC;AAIzE,gBAAgB;AAChB,MAAM,CAAC,MAAM,oBAAoB,GAAG;IAChC,MAAM,EAAE,CAAW;CACb,CAAC;AAYX,gBAAgB;AAChB,MAAM,OAAgB,mBAAoB,SAAQ,qBAAqB;IACnE,YAAsB,MAAqB;QACvC,KAAK,qCAAsB,MAAM,CAAC,CAAC;IACvC,CAAC;IAID,gBAAgB;IACT,UAAU,CAAC,OAAqC;QACnD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,oBAAoB,CAAC,MAAM,CAAC;IAChE,CAAC;CAIJ;AAED,gBAAgB;AAChB,MAAM,UAAU,sBAAsB,CAAC,QAAgC;IACnE,OAAO,QAAQ,CAAC,UAAU,oCAA0C,CAAC;AACzE,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,uBAAuB,CAA8C,QAAgC,EAAE,QAAW;IAC9H,OAAO,sBAAsB,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAC1F,CAAC", "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { IAudioParameterRampOptions } from \"../../audioParameter\";\nimport type { AudioEngineV2 } from \"../audioEngineV2\";\nimport { _AbstractAudioSubNode } from \"../subNodes/abstractAudioSubNode\";\nimport { AudioSubNode } from \"../subNodes/audioSubNode\";\nimport type { _AbstractAudioSubGraph } from \"./abstractAudioSubGraph\";\n\n/** @internal */\nexport const _VolumeAudioDefaults = {\n    volume: 1 as number,\n} as const;\n\n/**\n * Volume options.\n */\nexport interface IVolumeAudioOptions {\n    /**\n     * The volume/gain. Defaults to 1.\n     */\n    volume: number;\n}\n\n/** @internal */\nexport abstract class _VolumeAudioSubNode extends _AbstractAudioSubNode {\n    protected constructor(engine: AudioEngineV2) {\n        super(AudioSubNode.VOLUME, engine);\n    }\n\n    public abstract volume: number;\n\n    /** @internal */\n    public setOptions(options: Partial<IVolumeAudioOptions>): void {\n        this.volume = options.volume ?? _VolumeAudioDefaults.volume;\n    }\n\n    /** @internal */\n    public abstract setVolume(value: number, options?: Nullable<Partial<IAudioParameterRampOptions>>): void;\n}\n\n/** @internal */\nexport function _GetVolumeAudioSubNode(subGraph: _AbstractAudioSubGraph): Nullable<_VolumeAudioSubNode> {\n    return subGraph.getSubNode<_VolumeAudioSubNode>(AudioSubNode.VOLUME);\n}\n\n/** @internal */\nexport function _GetVolumeAudioProperty<K extends keyof typeof _VolumeAudioDefaults>(subGraph: _AbstractAudioSubGraph, property: K): (typeof _VolumeAudioDefaults)[K] {\n    return _GetVolumeAudioSubNode(subGraph)?.[property] ?? _VolumeAudioDefaults[property];\n}\n"]}