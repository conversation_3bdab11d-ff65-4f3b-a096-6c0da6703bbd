{"version": 3, "file": "shaderCodeNode.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderCodeNode.ts"], "names": [], "mappings": "AAEA,MAAM,2BAA2B,GAAG,WAAW,CAAC;AAChD,MAAM,yBAAyB,GAAG,SAAS,CAAC;AAE5C,gBAAgB;AAChB,MAAM,OAAO,cAAc;IAA3B;QAEI,aAAQ,GAAqB,EAAE,CAAC;IAqFpC,CAAC;IAjFG,6DAA6D;IAC7D,OAAO,CAAC,aAAwC;QAC5C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,OAAO,CAAC,aAAwC,EAAE,OAA4B,EAAE,qBAAgD;QAC5H,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,KAAK,GAAW,IAAI,CAAC,IAAI,CAAC;YAC9B,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;YACpC,IAAI,SAAS,EAAE,CAAC;gBACZ,+GAA+G;gBAC/G,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;oBAC1B,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;gBAC1F,CAAC;gBAED,MAAM,gBAAgB,GAAG,OAAO,CAAC,SAAS,EAAE,oBAAoB,IAAI,2BAA2B,CAAC;gBAChG,MAAM,cAAc,GAChB,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,SAAS,EAAE,0BAA0B;oBAC/D,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,0BAA0B;oBAC/C,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,SAAS,EAAE,wBAAwB;wBAClE,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,wBAAwB;wBAC7C,CAAC,CAAC,yBAAyB,CAAC;gBAEtC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,SAAS,CAAC,kBAAkB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;oBAChG,KAAK,GAAG,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;gBAC9F,CAAC;qBAAM,IACH,SAAS,CAAC,gBAAgB;oBAC1B,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,EAChI,CAAC;oBACC,KAAK,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;gBAChH,CAAC;qBAAM,IAAI,SAAS,CAAC,gBAAgB,IAAI,SAAS,CAAC,aAAa,IAAI,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC1G,IAAI,CAAC,OAAO,CAAC,qCAAqC,EAAE,CAAC;wBACjD,KAAK,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;oBAChH,CAAC;gBACL,CAAC;qBAAM,IAAI,SAAS,CAAC,sBAAsB,IAAI,SAAS,CAAC,mBAAmB,IAAI,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5H,IAAI,CAAC,OAAO,CAAC,qCAAqC,EAAE,CAAC;wBACjD,KAAK,GAAG,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;wBACnG,OAAO,CAAC,qCAAqC,GAAG,IAAI,CAAC;oBACzD,CAAC;gBACL,CAAC;qBAAM,IAAI,SAAS,CAAC,gBAAgB,IAAI,SAAS,CAAC,aAAa,IAAI,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC1G,KAAK,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;gBAChH,CAAC;qBAAM,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,SAAS,CAAC,sBAAsB,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,qCAAqC,EAAE,CAAC;oBAC/J,MAAM,KAAK,GAAG,wDAAwD,CAAC;oBAEvE,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;wBACxB,UAAU;wBACV,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;4BAC7B,KAAK,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,aAAa,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;wBAChH,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,iBAAiB;wBACjB,IAAI,SAAS,CAAC,sBAAsB,EAAE,CAAC;4BACnC,KAAK,GAAG,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;4BACnG,OAAO,CAAC,qCAAqC,GAAG,IAAI,CAAC;wBACzD,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,IAAI,OAAO,CAAC,qCAAqC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACjF,OAAO,CAAC,qCAAqC,GAAG,KAAK,CAAC;oBACtD,IAAI,SAAS,CAAC,2BAA2B,EAAE,CAAC;wBACxC,KAAK,GAAG,SAAS,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;oBAC5G,CAAC;gBACL,CAAC;YACL,CAAC;YAED,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC;QAC3B,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,IAAI,CAAC,qBAAqB,IAAI,MAAM,CAAC;YAC/E,qBAAqB,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC9F,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ", "sourcesContent": ["import type { _IProcessingOptions } from \"./shaderProcessingOptions\";\r\n\r\nconst DefaultAttributeKeywordName = \"attribute\";\r\nconst DefaultVaryingKeywordName = \"varying\";\r\n\r\n/** @internal */\r\nexport class ShaderCodeNode {\r\n    line: string;\r\n    children: ShaderCodeNode[] = [];\r\n    additionalDefineKey?: string;\r\n    additionalDefineValue?: string;\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    isValid(preprocessors: { [key: string]: string }): boolean {\r\n        return true;\r\n    }\r\n\r\n    process(preprocessors: { [key: string]: string }, options: _IProcessingOptions, preProcessorsFromCode: { [key: string]: string }): string {\r\n        let result = \"\";\r\n        if (this.line) {\r\n            let value: string = this.line;\r\n            const processor = options.processor;\r\n            if (processor) {\r\n                // This must be done before other replacements to avoid mistakenly changing something that was already changed.\r\n                if (processor.lineProcessor) {\r\n                    value = processor.lineProcessor(value, options.isFragment, options.processingContext);\r\n                }\r\n\r\n                const attributeKeyword = options.processor?.attributeKeywordName ?? DefaultAttributeKeywordName;\r\n                const varyingKeyword =\r\n                    options.isFragment && options.processor?.varyingFragmentKeywordName\r\n                        ? options.processor?.varyingFragmentKeywordName\r\n                        : !options.isFragment && options.processor?.varyingVertexKeywordName\r\n                          ? options.processor?.varyingVertexKeywordName\r\n                          : DefaultVaryingKeywordName;\r\n\r\n                if (!options.isFragment && processor.attributeProcessor && this.line.startsWith(attributeKeyword)) {\r\n                    value = processor.attributeProcessor(this.line, preprocessors, options.processingContext);\r\n                } else if (\r\n                    processor.varyingProcessor &&\r\n                    (processor.varyingCheck?.(this.line, options.isFragment) || (!processor.varyingCheck && this.line.startsWith(varyingKeyword)))\r\n                ) {\r\n                    value = processor.varyingProcessor(this.line, options.isFragment, preprocessors, options.processingContext);\r\n                } else if (processor.uniformProcessor && processor.uniformRegexp && processor.uniformRegexp.test(this.line)) {\r\n                    if (!options.lookForClosingBracketForUniformBuffer) {\r\n                        value = processor.uniformProcessor(this.line, options.isFragment, preprocessors, options.processingContext);\r\n                    }\r\n                } else if (processor.uniformBufferProcessor && processor.uniformBufferRegexp && processor.uniformBufferRegexp.test(this.line)) {\r\n                    if (!options.lookForClosingBracketForUniformBuffer) {\r\n                        value = processor.uniformBufferProcessor(this.line, options.isFragment, options.processingContext);\r\n                        options.lookForClosingBracketForUniformBuffer = true;\r\n                    }\r\n                } else if (processor.textureProcessor && processor.textureRegexp && processor.textureRegexp.test(this.line)) {\r\n                    value = processor.textureProcessor(this.line, options.isFragment, preprocessors, options.processingContext);\r\n                } else if ((processor.uniformProcessor || processor.uniformBufferProcessor) && this.line.startsWith(\"uniform\") && !options.lookForClosingBracketForUniformBuffer) {\r\n                    const regex = /uniform\\s+(?:(?:highp)?|(?:lowp)?)\\s*(\\S+)\\s+(\\S+)\\s*;/;\r\n\r\n                    if (regex.test(this.line)) {\r\n                        // uniform\r\n                        if (processor.uniformProcessor) {\r\n                            value = processor.uniformProcessor(this.line, options.isFragment, preprocessors, options.processingContext);\r\n                        }\r\n                    } else {\r\n                        // Uniform buffer\r\n                        if (processor.uniformBufferProcessor) {\r\n                            value = processor.uniformBufferProcessor(this.line, options.isFragment, options.processingContext);\r\n                            options.lookForClosingBracketForUniformBuffer = true;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if (options.lookForClosingBracketForUniformBuffer && this.line.indexOf(\"}\") !== -1) {\r\n                    options.lookForClosingBracketForUniformBuffer = false;\r\n                    if (processor.endOfUniformBufferProcessor) {\r\n                        value = processor.endOfUniformBufferProcessor(this.line, options.isFragment, options.processingContext);\r\n                    }\r\n                }\r\n            }\r\n\r\n            result += value + \"\\n\";\r\n        }\r\n\r\n        for (const child of this.children) {\r\n            result += child.process(preprocessors, options, preProcessorsFromCode);\r\n        }\r\n\r\n        if (this.additionalDefineKey) {\r\n            preprocessors[this.additionalDefineKey] = this.additionalDefineValue || \"true\";\r\n            preProcessorsFromCode[this.additionalDefineKey] = preprocessors[this.additionalDefineKey];\r\n        }\r\n\r\n        return result;\r\n    }\r\n}\r\n"]}