{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/lightConstants.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/lightConstants.ts"], "sourcesContent": ["/** Defines the cross module constantsused by lights to avoid circular dependencies */\r\nexport class LightConstants {\r\n    /**\r\n     * Falloff Default: light is falling off following the material specification:\r\n     * standard material is using standard falloff whereas pbr material can request special falloff per materials.\r\n     */\r\n    public static readonly FALLOFF_DEFAULT = 0;\r\n\r\n    /**\r\n     * Falloff Physical: light is falling off following the inverse squared distance law.\r\n     */\r\n    public static readonly FALLOFF_PHYSICAL = 1;\r\n\r\n    /**\r\n     * Falloff gltf: light is falling off as described in the gltf moving to PBR document\r\n     * to enhance interoperability with other engines.\r\n     */\r\n    public static readonly FALLOFF_GLTF = 2;\r\n\r\n    /**\r\n     * Falloff Standard: light is falling off like in the standard material\r\n     * to enhance interoperability with other materials.\r\n     */\r\n    public static readonly FALLOFF_STANDARD = 3;\r\n\r\n    //lightmapMode Consts\r\n    /**\r\n     * If every light affecting the material is in this lightmapMode,\r\n     * material.lightmapTexture adds or multiplies\r\n     * (depends on material.useLightmapAsShadowmap)\r\n     * after every other light calculations.\r\n     */\r\n    public static readonly LIGHTMAP_DEFAULT = 0;\r\n    /**\r\n     * material.lightmapTexture as only diffuse lighting from this light\r\n     * adds only specular lighting from this light\r\n     * adds dynamic shadows\r\n     */\r\n    public static readonly LIGHTMAP_SPECULAR = 1;\r\n    /**\r\n     * material.lightmapTexture as only lighting\r\n     * no light calculation from this light\r\n     * only adds dynamic shadows from this light\r\n     */\r\n    public static readonly LIGHTMAP_SHADOWSONLY = 2;\r\n\r\n    // Intensity Mode Consts\r\n    /**\r\n     * Each light type uses the default quantity according to its type:\r\n     *      point/spot lights use luminous intensity\r\n     *      directional lights use illuminance\r\n     */\r\n    public static readonly INTENSITYMODE_AUTOMATIC = 0;\r\n    /**\r\n     * lumen (lm)\r\n     */\r\n    public static readonly INTENSITYMODE_LUMINOUSPOWER = 1;\r\n    /**\r\n     * candela (lm/sr)\r\n     */\r\n    public static readonly INTENSITYMODE_LUMINOUSINTENSITY = 2;\r\n    /**\r\n     * lux (lm/m^2)\r\n     */\r\n    public static readonly INTENSITYMODE_ILLUMINANCE = 3;\r\n    /**\r\n     * nit (cd/m^2)\r\n     */\r\n    public static readonly INTENSITYMODE_LUMINANCE = 4;\r\n\r\n    // Light types ids const.\r\n    /**\r\n     * Light type const id of the point light.\r\n     */\r\n    public static readonly LIGHTTYPEID_POINTLIGHT = 0;\r\n    /**\r\n     * Light type const id of the directional light.\r\n     */\r\n    public static readonly LIGHTTYPEID_DIRECTIONALLIGHT = 1;\r\n    /**\r\n     * Light type const id of the spot light.\r\n     */\r\n    public static readonly LIGHTTYPEID_SPOTLIGHT = 2;\r\n    /**\r\n     * Light type const id of the hemispheric light.\r\n     */\r\n    public static readonly LIGHTTYPEID_HEMISPHERICLIGHT = 3;\r\n\r\n    /**\r\n     * Light type const id of the area light.\r\n     */\r\n    public static readonly LIGHTTYPEID_RECT_AREALIGHT = 4;\r\n\r\n    /**\r\n     * Sort function to order lights for rendering.\r\n     * @param a First Light object to compare to second.\r\n     * @param b Second Light object to compare first.\r\n     * @returns -1 to reduce's a's index relative to be, 0 for no change, 1 to increase a's index relative to b.\r\n     */\r\n    public static CompareLightsPriority(a: ISortableLight, b: ISortableLight): number {\r\n        //shadow-casting lights have priority over non-shadow-casting lights\r\n        //the renderPriority is a secondary sort criterion\r\n        if (a.shadowEnabled !== b.shadowEnabled) {\r\n            return (b.shadowEnabled ? 1 : 0) - (a.shadowEnabled ? 1 : 0);\r\n        }\r\n        return b.renderPriority - a.renderPriority;\r\n    }\r\n}\r\n\r\n/**\r\n * Defines the common interface of sortable lights\r\n */\r\nexport interface ISortableLight {\r\n    /**\r\n     * Gets or sets whether or not the shadows are enabled for this light. This can help turning off/on shadow without detaching\r\n     * the current shadow generator.\r\n     */\r\n    shadowEnabled: boolean;\r\n    /**\r\n     * Defines the rendering priority of the lights. It can help in case of fallback or number of lights\r\n     * exceeding the number allowed of the materials.\r\n     */\r\n    renderPriority: number;\r\n}\r\n"], "names": [], "mappings": "AAAA,oFAAA,EAAsF;;;AAChF,MAAO,cAAc;IA4FvB;;;;;OAKG,CACI,MAAM,CAAC,qBAAqB,CAAC,CAAiB,EAAE,CAAiB,EAAA;QACpE,oEAAoE;QACpE,kDAAkD;QAClD,IAAI,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,aAAa,EAAE,CAAC;YACtC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,cAAc,CAAC;IAC/C,CAAC;;AAxGD;;;GAGG,CACoB,eAAA,eAAe,GAAG,CAAC,CAAC;AAE3C;;GAEG,CACoB,eAAA,gBAAgB,GAAG,CAAC,CAAC;AAE5C;;;GAGG,CACoB,eAAA,YAAY,GAAG,CAAC,CAAC;AAExC;;;GAGG,CACoB,eAAA,gBAAgB,GAAG,CAAC,CAAC;AAE5C,qBAAqB;AACrB;;;;;GAKG,CACoB,eAAA,gBAAgB,GAAG,CAAC,CAAC;AAC5C;;;;GAIG,CACoB,eAAA,iBAAiB,GAAG,CAAC,CAAC;AAC7C;;;;GAIG,CACoB,eAAA,oBAAoB,GAAG,CAAC,CAAC;AAEhD,wBAAwB;AACxB;;;;GAIG,CACoB,eAAA,uBAAuB,GAAG,CAAC,CAAC;AACnD;;GAEG,CACoB,eAAA,2BAA2B,GAAG,CAAC,CAAC;AACvD;;GAEG,CACoB,eAAA,+BAA+B,GAAG,CAAC,CAAC;AAC3D;;GAEG,CACoB,eAAA,yBAAyB,GAAG,CAAC,CAAC;AACrD;;GAEG,CACoB,eAAA,uBAAuB,GAAG,CAAC,CAAC;AAEnD,yBAAyB;AACzB;;GAEG,CACoB,eAAA,sBAAsB,GAAG,CAAC,CAAC;AAClD;;GAEG,CACoB,eAAA,4BAA4B,GAAG,CAAC,CAAC;AACxD;;GAEG,CACoB,eAAA,qBAAqB,GAAG,CAAC,CAAC;AACjD;;GAEG,CACoB,eAAA,4BAA4B,GAAG,CAAC,CAAC;AAExD;;GAEG,CACoB,eAAA,0BAA0B,GAAG,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/light.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/light.ts"], "sourcesContent": ["import { serialize, serializeAsColor3, expandToProperty } from \"../Misc/decorators\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { Matrix } from \"../Maths/math.vector\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Color3, TmpColors } from \"../Maths/math.color\";\r\nimport { Node } from \"../node\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { UniformBuffer } from \"../Materials/uniformBuffer\";\r\nimport type { IShadowGenerator } from \"./Shadows/shadowGenerator\";\r\nimport { GetClass } from \"../Misc/typeStore\";\r\nimport type { ISortableLight } from \"./lightConstants\";\r\nimport { LightConstants } from \"./lightConstants\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\n/**\r\n * Base class of all the lights in Babylon. It groups all the generic information about lights.\r\n * Lights are used, as you would expect, to affect how meshes are seen, in terms of both illumination and colour.\r\n * All meshes allow light to pass through them unless shadow generation is activated. The default number of lights allowed is four but this can be increased.\r\n */\r\nexport abstract class Light extends Node implements ISortableLight {\r\n    /**\r\n     * Falloff Default: light is falling off following the material specification:\r\n     * standard material is using standard falloff whereas pbr material can request special falloff per materials.\r\n     */\r\n    public static readonly FALLOFF_DEFAULT = LightConstants.FALLOFF_DEFAULT;\r\n\r\n    /**\r\n     * Falloff Physical: light is falling off following the inverse squared distance law.\r\n     */\r\n    public static readonly FALLOFF_PHYSICAL = LightConstants.FALLOFF_PHYSICAL;\r\n\r\n    /**\r\n     * Falloff gltf: light is falling off as described in the gltf moving to PBR document\r\n     * to enhance interoperability with other engines.\r\n     */\r\n    public static readonly FALLOFF_GLTF = LightConstants.FALLOFF_GLTF;\r\n\r\n    /**\r\n     * Falloff Standard: light is falling off like in the standard material\r\n     * to enhance interoperability with other materials.\r\n     */\r\n    public static readonly FALLOFF_STANDARD = LightConstants.FALLOFF_STANDARD;\r\n\r\n    //lightmapMode Consts\r\n    /**\r\n     * If every light affecting the material is in this lightmapMode,\r\n     * material.lightmapTexture adds or multiplies\r\n     * (depends on material.useLightmapAsShadowmap)\r\n     * after every other light calculations.\r\n     */\r\n    public static readonly LIGHTMAP_DEFAULT = LightConstants.LIGHTMAP_DEFAULT;\r\n    /**\r\n     * material.lightmapTexture as only diffuse lighting from this light\r\n     * adds only specular lighting from this light\r\n     * adds dynamic shadows\r\n     */\r\n    public static readonly LIGHTMAP_SPECULAR = LightConstants.LIGHTMAP_SPECULAR;\r\n    /**\r\n     * material.lightmapTexture as only lighting\r\n     * no light calculation from this light\r\n     * only adds dynamic shadows from this light\r\n     */\r\n    public static readonly LIGHTMAP_SHADOWSONLY = LightConstants.LIGHTMAP_SHADOWSONLY;\r\n\r\n    // Intensity Mode Consts\r\n    /**\r\n     * Each light type uses the default quantity according to its type:\r\n     *      point/spot lights use luminous intensity\r\n     *      directional lights use illuminance\r\n     */\r\n    public static readonly INTENSITYMODE_AUTOMATIC = LightConstants.INTENSITYMODE_AUTOMATIC;\r\n    /**\r\n     * lumen (lm)\r\n     */\r\n    public static readonly INTENSITYMODE_LUMINOUSPOWER = LightConstants.INTENSITYMODE_LUMINOUSPOWER;\r\n    /**\r\n     * candela (lm/sr)\r\n     */\r\n    public static readonly INTENSITYMODE_LUMINOUSINTENSITY = LightConstants.INTENSITYMODE_LUMINOUSINTENSITY;\r\n    /**\r\n     * lux (lm/m^2)\r\n     */\r\n    public static readonly INTENSITYMODE_ILLUMINANCE = LightConstants.INTENSITYMODE_ILLUMINANCE;\r\n    /**\r\n     * nit (cd/m^2)\r\n     */\r\n    public static readonly INTENSITYMODE_LUMINANCE = LightConstants.INTENSITYMODE_LUMINANCE;\r\n\r\n    // Light types ids const.\r\n    /**\r\n     * Light type const id of the point light.\r\n     */\r\n    public static readonly LIGHTTYPEID_POINTLIGHT = LightConstants.LIGHTTYPEID_POINTLIGHT;\r\n    /**\r\n     * Light type const id of the directional light.\r\n     */\r\n    public static readonly LIGHTTYPEID_DIRECTIONALLIGHT = LightConstants.LIGHTTYPEID_DIRECTIONALLIGHT;\r\n    /**\r\n     * Light type const id of the spot light.\r\n     */\r\n    public static readonly LIGHTTYPEID_SPOTLIGHT = LightConstants.LIGHTTYPEID_SPOTLIGHT;\r\n    /**\r\n     * Light type const id of the hemispheric light.\r\n     */\r\n    public static readonly LIGHTTYPEID_HEMISPHERICLIGHT = LightConstants.LIGHTTYPEID_HEMISPHERICLIGHT;\r\n\r\n    /**\r\n     * Light type const id of the area light.\r\n     */\r\n    public static readonly LIGHTTYPEID_RECT_AREALIGHT = LightConstants.LIGHTTYPEID_RECT_AREALIGHT;\r\n\r\n    /**\r\n     * Diffuse gives the basic color to an object.\r\n     */\r\n    @serializeAsColor3()\r\n    public diffuse = new Color3(1.0, 1.0, 1.0);\r\n\r\n    /**\r\n     * Specular produces a highlight color on an object.\r\n     * Note: This is not affecting PBR materials.\r\n     */\r\n    @serializeAsColor3()\r\n    public specular = new Color3(1.0, 1.0, 1.0);\r\n\r\n    /**\r\n     * Defines the falloff type for this light. This lets overriding how punctual light are\r\n     * falling off base on range or angle.\r\n     * This can be set to any values in Light.FALLOFF_x.\r\n     *\r\n     * Note: This is only useful for PBR Materials at the moment. This could be extended if required to\r\n     * other types of materials.\r\n     */\r\n    @serialize()\r\n    public falloffType = Light.FALLOFF_DEFAULT;\r\n\r\n    /**\r\n     * Strength of the light.\r\n     * Note: By default it is define in the framework own unit.\r\n     * Note: In PBR materials the intensityMode can be use to chose what unit the intensity is defined in.\r\n     */\r\n    @serialize()\r\n    public intensity = 1.0;\r\n\r\n    private _range = Number.MAX_VALUE;\r\n    protected _inverseSquaredRange = 0;\r\n\r\n    /**\r\n     * Defines how far from the source the light is impacting in scene units.\r\n     * Note: Unused in PBR material as the distance light falloff is defined following the inverse squared falloff.\r\n     */\r\n    @serialize()\r\n    public get range(): number {\r\n        return this._range;\r\n    }\r\n    /**\r\n     * Defines how far from the source the light is impacting in scene units.\r\n     * Note: Unused in PBR material as the distance light falloff is defined following the inverse squared falloff.\r\n     */\r\n    public set range(value: number) {\r\n        this._range = value;\r\n        this._inverseSquaredRange = 1.0 / (this.range * this.range);\r\n    }\r\n\r\n    /**\r\n     * Cached photometric scale default to 1.0 as the automatic intensity mode defaults to 1.0 for every type\r\n     * of light.\r\n     */\r\n    private _photometricScale = 1.0;\r\n\r\n    private _intensityMode: number = Light.INTENSITYMODE_AUTOMATIC;\r\n    /**\r\n     * Gets the photometric scale used to interpret the intensity.\r\n     * This is only relevant with PBR Materials where the light intensity can be defined in a physical way.\r\n     */\r\n    @serialize()\r\n    public get intensityMode(): number {\r\n        return this._intensityMode;\r\n    }\r\n    /**\r\n     * Sets the photometric scale used to interpret the intensity.\r\n     * This is only relevant with PBR Materials where the light intensity can be defined in a physical way.\r\n     */\r\n    public set intensityMode(value: number) {\r\n        this._intensityMode = value;\r\n        this._computePhotometricScale();\r\n    }\r\n\r\n    private _radius = 0.00001;\r\n    /**\r\n     * Gets the light radius used by PBR Materials to simulate soft area lights.\r\n     */\r\n    @serialize()\r\n    public get radius(): number {\r\n        return this._radius;\r\n    }\r\n    /**\r\n     * sets the light radius used by PBR Materials to simulate soft area lights.\r\n     */\r\n    public set radius(value: number) {\r\n        this._radius = value;\r\n        this._computePhotometricScale();\r\n    }\r\n\r\n    @serialize()\r\n    private _renderPriority: number;\r\n    /**\r\n     * Defines the rendering priority of the lights. It can help in case of fallback or number of lights\r\n     * exceeding the number allowed of the materials.\r\n     */\r\n    @expandToProperty(\"_reorderLightsInScene\")\r\n    public renderPriority: number = 0;\r\n\r\n    @serialize(\"shadowEnabled\")\r\n    private _shadowEnabled: boolean = true;\r\n    /**\r\n     * Gets whether or not the shadows are enabled for this light. This can help turning off/on shadow without detaching\r\n     * the current shadow generator.\r\n     */\r\n    public get shadowEnabled(): boolean {\r\n        return this._shadowEnabled;\r\n    }\r\n    /**\r\n     * Sets whether or not the shadows are enabled for this light. This can help turning off/on shadow without detaching\r\n     * the current shadow generator.\r\n     */\r\n    public set shadowEnabled(value: boolean) {\r\n        if (this._shadowEnabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._shadowEnabled = value;\r\n        this._markMeshesAsLightDirty();\r\n    }\r\n\r\n    private _includedOnlyMeshes: AbstractMesh[];\r\n    /**\r\n     * Gets the only meshes impacted by this light.\r\n     */\r\n    public get includedOnlyMeshes(): AbstractMesh[] {\r\n        return this._includedOnlyMeshes;\r\n    }\r\n    /**\r\n     * Sets the only meshes impacted by this light.\r\n     */\r\n    public set includedOnlyMeshes(value: AbstractMesh[]) {\r\n        this._includedOnlyMeshes = value;\r\n        this._hookArrayForIncludedOnly(value);\r\n    }\r\n\r\n    private _excludedMeshes: AbstractMesh[];\r\n    /**\r\n     * Gets the meshes not impacted by this light.\r\n     */\r\n    public get excludedMeshes(): AbstractMesh[] {\r\n        return this._excludedMeshes;\r\n    }\r\n    /**\r\n     * Sets the meshes not impacted by this light.\r\n     */\r\n    public set excludedMeshes(value: AbstractMesh[]) {\r\n        this._excludedMeshes = value;\r\n        this._hookArrayForExcluded(value);\r\n    }\r\n\r\n    @serialize(\"excludeWithLayerMask\")\r\n    private _excludeWithLayerMask = 0;\r\n    /**\r\n     * Gets the layer id use to find what meshes are not impacted by the light.\r\n     * Inactive if 0\r\n     */\r\n    public get excludeWithLayerMask(): number {\r\n        return this._excludeWithLayerMask;\r\n    }\r\n    /**\r\n     * Sets the layer id use to find what meshes are not impacted by the light.\r\n     * Inactive if 0\r\n     */\r\n    public set excludeWithLayerMask(value: number) {\r\n        this._excludeWithLayerMask = value;\r\n        this._resyncMeshes();\r\n    }\r\n\r\n    @serialize(\"includeOnlyWithLayerMask\")\r\n    private _includeOnlyWithLayerMask = 0;\r\n    /**\r\n     * Gets the layer id use to find what meshes are impacted by the light.\r\n     * Inactive if 0\r\n     */\r\n    public get includeOnlyWithLayerMask(): number {\r\n        return this._includeOnlyWithLayerMask;\r\n    }\r\n    /**\r\n     * Sets the layer id use to find what meshes are impacted by the light.\r\n     * Inactive if 0\r\n     */\r\n    public set includeOnlyWithLayerMask(value: number) {\r\n        this._includeOnlyWithLayerMask = value;\r\n        this._resyncMeshes();\r\n    }\r\n\r\n    @serialize(\"lightmapMode\")\r\n    private _lightmapMode = 0;\r\n    /**\r\n     * Gets the lightmap mode of this light (should be one of the constants defined by Light.LIGHTMAP_x)\r\n     */\r\n    public get lightmapMode(): number {\r\n        return this._lightmapMode;\r\n    }\r\n    /**\r\n     * Sets the lightmap mode of this light (should be one of the constants defined by Light.LIGHTMAP_x)\r\n     */\r\n    public set lightmapMode(value: number) {\r\n        if (this._lightmapMode === value) {\r\n            return;\r\n        }\r\n\r\n        this._lightmapMode = value;\r\n        this._markMeshesAsLightDirty();\r\n    }\r\n\r\n    /**\r\n     * Returns the view matrix.\r\n     * @param _faceIndex The index of the face for which we want to extract the view matrix. Only used for point light types.\r\n     * @returns The view matrix. Can be null, if a view matrix cannot be defined for the type of light considered (as for a hemispherical light, for example).\r\n     */\r\n    public getViewMatrix(_faceIndex?: number): Nullable<Matrix> {\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Returns the projection matrix.\r\n     * Note that viewMatrix and renderList are optional and are only used by lights that calculate the projection matrix from a list of meshes (e.g. directional lights with automatic extents calculation).\r\n     * @param _viewMatrix The view transform matrix of the light (optional).\r\n     * @param _renderList The list of meshes to take into account when calculating the projection matrix (optional).\r\n     * @returns The projection matrix. Can be null, if a projection matrix cannot be defined for the type of light considered (as for a hemispherical light, for example).\r\n     */\r\n    public getProjectionMatrix(_viewMatrix?: Matrix, _renderList?: Array<AbstractMesh>): Nullable<Matrix> {\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Shadow generators associated to the light.\r\n     * @internal Internal use only.\r\n     */\r\n    public _shadowGenerators: Nullable<Map<Nullable<Camera>, IShadowGenerator>> = null;\r\n\r\n    /**\r\n     * @internal Internal use only.\r\n     */\r\n    public _excludedMeshesIds = new Array<string>();\r\n\r\n    /**\r\n     * @internal Internal use only.\r\n     */\r\n    public _includedOnlyMeshesIds = new Array<string>();\r\n\r\n    /**\r\n     * The current light uniform buffer.\r\n     * @internal Internal use only.\r\n     */\r\n    public _uniformBuffer: UniformBuffer;\r\n\r\n    /** @internal */\r\n    public _renderId: number;\r\n\r\n    private _lastUseSpecular: boolean;\r\n\r\n    /**\r\n     * Creates a Light object in the scene.\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n     * @param name The friendly name of the light\r\n     * @param scene The scene the light belongs too\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene, false);\r\n        this.getScene().addLight(this);\r\n        this._uniformBuffer = new UniformBuffer(this.getScene().getEngine(), undefined, undefined, name);\r\n        this._buildUniformLayout();\r\n\r\n        this.includedOnlyMeshes = [] as AbstractMesh[];\r\n        this.excludedMeshes = [] as AbstractMesh[];\r\n\r\n        this._resyncMeshes();\r\n    }\r\n\r\n    protected abstract _buildUniformLayout(): void;\r\n\r\n    /**\r\n     * Sets the passed Effect \"effect\" with the Light information.\r\n     * @param effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The light\r\n     */\r\n    public abstract transferToEffect(effect: Effect, lightIndex: string): Light;\r\n\r\n    /**\r\n     * Sets the passed Effect \"effect\" with the Light textures.\r\n     * @param effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The light\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public transferTexturesToEffect(effect: Effect, lightIndex: string): Light {\r\n        // Do nothing by default.\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Binds the lights information from the scene to the effect for the given mesh.\r\n     * @param lightIndex Light index\r\n     * @param scene The scene where the light belongs to\r\n     * @param effect The effect we are binding the data to\r\n     * @param useSpecular Defines if specular is supported\r\n     * @param receiveShadows Defines if the effect (mesh) we bind the light for receives shadows\r\n     */\r\n    public _bindLight(lightIndex: number, scene: Scene, effect: Effect, useSpecular: boolean, receiveShadows = true): void {\r\n        const iAsString = lightIndex.toString();\r\n        let needUpdate = false;\r\n\r\n        this._uniformBuffer.bindToEffect(effect, \"Light\" + iAsString);\r\n\r\n        if (this._renderId !== scene.getRenderId() || this._lastUseSpecular !== useSpecular || !this._uniformBuffer.useUbo) {\r\n            this._renderId = scene.getRenderId();\r\n            this._lastUseSpecular = useSpecular;\r\n\r\n            const scaledIntensity = this.getScaledIntensity();\r\n\r\n            this.transferToEffect(effect, iAsString);\r\n\r\n            this.diffuse.scaleToRef(scaledIntensity, TmpColors.Color3[0]);\r\n            this._uniformBuffer.updateColor4(\"vLightDiffuse\", TmpColors.Color3[0], this.range, iAsString);\r\n            if (useSpecular) {\r\n                this.specular.scaleToRef(scaledIntensity, TmpColors.Color3[1]);\r\n                this._uniformBuffer.updateColor4(\"vLightSpecular\", TmpColors.Color3[1], this.radius, iAsString);\r\n            }\r\n            needUpdate = true;\r\n        }\r\n\r\n        // Textures might still need to be rebound.\r\n        this.transferTexturesToEffect(effect, iAsString);\r\n\r\n        // Shadows\r\n        if (scene.shadowsEnabled && this.shadowEnabled && receiveShadows) {\r\n            const shadowGenerator = this.getShadowGenerator(scene.activeCamera) ?? this.getShadowGenerator();\r\n            if (shadowGenerator) {\r\n                shadowGenerator.bindShadowLight(iAsString, effect);\r\n                needUpdate = true;\r\n            }\r\n        }\r\n\r\n        if (needUpdate) {\r\n            this._uniformBuffer.update();\r\n        } else {\r\n            this._uniformBuffer.bindUniformBuffer();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Effect \"effect\" with the Light information.\r\n     * @param effect The effect to update\r\n     * @param lightDataUniformName The uniform used to store light data (position or direction)\r\n     * @returns The light\r\n     */\r\n    public abstract transferToNodeMaterialEffect(effect: Effect, lightDataUniformName: string): Light;\r\n\r\n    /**\r\n     * Returns the string \"Light\".\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"Light\";\r\n    }\r\n\r\n    /** @internal */\r\n    public readonly _isLight = true;\r\n\r\n    /**\r\n     * Converts the light information to a readable string for debug purpose.\r\n     * @param fullDetails Supports for multiple levels of logging within scene loading\r\n     * @returns the human readable light info\r\n     */\r\n    public override toString(fullDetails?: boolean): string {\r\n        let ret = \"Name: \" + this.name;\r\n        ret += \", type: \" + [\"Point\", \"Directional\", \"Spot\", \"Hemispheric\"][this.getTypeID()];\r\n        if (this.animations) {\r\n            for (let i = 0; i < this.animations.length; i++) {\r\n                ret += \", animation[0]: \" + this.animations[i].toString(fullDetails);\r\n            }\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /** @internal */\r\n    protected override _syncParentEnabledState() {\r\n        super._syncParentEnabledState();\r\n        if (!this.isDisposed()) {\r\n            this._resyncMeshes();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the enabled state of this node.\r\n     * @param value - the new enabled state\r\n     */\r\n    public override setEnabled(value: boolean): void {\r\n        super.setEnabled(value);\r\n\r\n        this._resyncMeshes();\r\n    }\r\n\r\n    /**\r\n     * Returns the Light associated shadow generator if any.\r\n     * @param camera Camera for which the shadow generator should be retrieved (default: null). If null, retrieves the default shadow generator\r\n     * @returns the associated shadow generator.\r\n     */\r\n    public getShadowGenerator(camera: Nullable<Camera> = null): Nullable<IShadowGenerator> {\r\n        if (this._shadowGenerators === null) {\r\n            return null;\r\n        }\r\n\r\n        return this._shadowGenerators.get(camera) ?? null;\r\n    }\r\n\r\n    /**\r\n     * Returns all the shadow generators associated to this light\r\n     * @returns\r\n     */\r\n    public getShadowGenerators(): Nullable<Map<Nullable<Camera>, IShadowGenerator>> {\r\n        return this._shadowGenerators;\r\n    }\r\n\r\n    /**\r\n     * Returns a Vector3, the absolute light position in the World.\r\n     * @returns the world space position of the light\r\n     */\r\n    public getAbsolutePosition(): Vector3 {\r\n        return Vector3.Zero();\r\n    }\r\n\r\n    /**\r\n     * Specifies if the light will affect the passed mesh.\r\n     * @param mesh The mesh to test against the light\r\n     * @returns true the mesh is affected otherwise, false.\r\n     */\r\n    public canAffectMesh(mesh: AbstractMesh): boolean {\r\n        if (!mesh) {\r\n            return true;\r\n        }\r\n\r\n        if (this.includedOnlyMeshes && this.includedOnlyMeshes.length > 0 && this.includedOnlyMeshes.indexOf(mesh) === -1) {\r\n            return false;\r\n        }\r\n\r\n        if (this.excludedMeshes && this.excludedMeshes.length > 0 && this.excludedMeshes.indexOf(mesh) !== -1) {\r\n            return false;\r\n        }\r\n\r\n        if (this.includeOnlyWithLayerMask !== 0 && (this.includeOnlyWithLayerMask & mesh.layerMask) === 0) {\r\n            return false;\r\n        }\r\n\r\n        if (this.excludeWithLayerMask !== 0 && this.excludeWithLayerMask & mesh.layerMask) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Releases resources associated with this node.\r\n     * @param doNotRecurse Set to true to not recurse into each children (recurse into each children by default)\r\n     * @param disposeMaterialAndTextures Set to true to also dispose referenced materials and textures (false by default)\r\n     */\r\n    public override dispose(doNotRecurse?: boolean, disposeMaterialAndTextures = false): void {\r\n        if (this._shadowGenerators) {\r\n            const iterator = this._shadowGenerators.values();\r\n            for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                const shadowGenerator = key.value;\r\n                shadowGenerator.dispose();\r\n            }\r\n            this._shadowGenerators = null;\r\n        }\r\n\r\n        // Animations\r\n        this.getScene().stopAnimation(this);\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.lights.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.lights.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        // Remove from meshes\r\n        for (const mesh of this.getScene().meshes) {\r\n            mesh._removeLightSource(this, true);\r\n        }\r\n\r\n        this._uniformBuffer.dispose();\r\n\r\n        // Remove from scene\r\n        this.getScene().removeLight(this);\r\n        super.dispose(doNotRecurse, disposeMaterialAndTextures);\r\n    }\r\n\r\n    /**\r\n     * Returns the light type ID (integer).\r\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public getTypeID(): number {\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Returns the intensity scaled by the Photometric Scale according to the light type and intensity mode.\r\n     * @returns the scaled intensity in intensity mode unit\r\n     */\r\n    public getScaledIntensity() {\r\n        return this._photometricScale * this.intensity;\r\n    }\r\n\r\n    /**\r\n     * Returns a new Light object, named \"name\", from the current one.\r\n     * @param name The name of the cloned light\r\n     * @param newParent The parent of this light, if it has one\r\n     * @returns the new created light\r\n     */\r\n    public override clone(name: string, newParent: Nullable<Node> = null): Nullable<Light> {\r\n        const constructor = Light.GetConstructorFromName(this.getTypeID(), name, this.getScene());\r\n\r\n        if (!constructor) {\r\n            return null;\r\n        }\r\n        const clonedLight = SerializationHelper.Clone(constructor, this);\r\n        if (name) {\r\n            clonedLight.name = name;\r\n        }\r\n        if (newParent) {\r\n            clonedLight.parent = newParent;\r\n        }\r\n        clonedLight.setEnabled(this.isEnabled());\r\n\r\n        this.onClonedObservable.notifyObservers(clonedLight);\r\n\r\n        return clonedLight;\r\n    }\r\n\r\n    /**\r\n     * Serializes the current light into a Serialization object.\r\n     * @returns the serialized object.\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.uniqueId = this.uniqueId;\r\n\r\n        // Type\r\n        serializationObject.type = this.getTypeID();\r\n\r\n        // Parent\r\n        if (this.parent) {\r\n            this.parent._serializeAsParent(serializationObject);\r\n        }\r\n\r\n        // Inclusion / exclusions\r\n        if (this.excludedMeshes.length > 0) {\r\n            serializationObject.excludedMeshesIds = [];\r\n            for (const mesh of this.excludedMeshes) {\r\n                serializationObject.excludedMeshesIds.push(mesh.id);\r\n            }\r\n        }\r\n\r\n        if (this.includedOnlyMeshes.length > 0) {\r\n            serializationObject.includedOnlyMeshesIds = [];\r\n            for (const mesh of this.includedOnlyMeshes) {\r\n                serializationObject.includedOnlyMeshesIds.push(mesh.id);\r\n            }\r\n        }\r\n\r\n        // Animations\r\n        SerializationHelper.AppendSerializedAnimations(this, serializationObject);\r\n        serializationObject.ranges = this.serializeAnimationRanges();\r\n\r\n        serializationObject.isEnabled = this.isEnabled();\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Creates a new typed light from the passed type (integer) : point light = 0, directional light = 1, spot light = 2, hemispheric light = 3.\r\n     * This new light is named \"name\" and added to the passed scene.\r\n     * @param type Type according to the types available in Light.LIGHTTYPEID_x\r\n     * @param name The friendly name of the light\r\n     * @param scene The scene the new light will belong to\r\n     * @returns the constructor function\r\n     */\r\n    static GetConstructorFromName(type: number, name: string, scene: Scene): Nullable<() => Light> {\r\n        const constructorFunc = Node.Construct(\"Light_Type_\" + type, name, scene);\r\n\r\n        if (constructorFunc) {\r\n            return <() => Light>constructorFunc;\r\n        }\r\n\r\n        // Default to no light for none present once.\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Parses the passed \"parsedLight\" and returns a new instanced Light from this parsing.\r\n     * @param parsedLight The JSON representation of the light\r\n     * @param scene The scene to create the parsed light in\r\n     * @returns the created light after parsing\r\n     */\r\n    public static Parse(parsedLight: any, scene: Scene): Nullable<Light> {\r\n        const constructor = Light.GetConstructorFromName(parsedLight.type, parsedLight.name, scene);\r\n\r\n        if (!constructor) {\r\n            return null;\r\n        }\r\n\r\n        const light = SerializationHelper.Parse(constructor, parsedLight, scene);\r\n\r\n        // Inclusion / exclusions\r\n        if (parsedLight.excludedMeshesIds) {\r\n            light._excludedMeshesIds = parsedLight.excludedMeshesIds;\r\n        }\r\n\r\n        if (parsedLight.includedOnlyMeshesIds) {\r\n            light._includedOnlyMeshesIds = parsedLight.includedOnlyMeshesIds;\r\n        }\r\n\r\n        // Parent\r\n        if (parsedLight.parentId !== undefined) {\r\n            light._waitingParentId = parsedLight.parentId;\r\n        }\r\n\r\n        if (parsedLight.parentInstanceIndex !== undefined) {\r\n            light._waitingParentInstanceIndex = parsedLight.parentInstanceIndex;\r\n        }\r\n\r\n        // Falloff\r\n        if (parsedLight.falloffType !== undefined) {\r\n            light.falloffType = parsedLight.falloffType;\r\n        }\r\n\r\n        // Lightmaps\r\n        if (parsedLight.lightmapMode !== undefined) {\r\n            light.lightmapMode = parsedLight.lightmapMode;\r\n        }\r\n\r\n        // Animations\r\n        if (parsedLight.animations) {\r\n            for (let animationIndex = 0; animationIndex < parsedLight.animations.length; animationIndex++) {\r\n                const parsedAnimation = parsedLight.animations[animationIndex];\r\n                const internalClass = GetClass(\"BABYLON.Animation\");\r\n                if (internalClass) {\r\n                    light.animations.push(internalClass.Parse(parsedAnimation));\r\n                }\r\n            }\r\n            Node.ParseAnimationRanges(light, parsedLight, scene);\r\n        }\r\n\r\n        if (parsedLight.autoAnimate) {\r\n            scene.beginAnimation(light, parsedLight.autoAnimateFrom, parsedLight.autoAnimateTo, parsedLight.autoAnimateLoop, parsedLight.autoAnimateSpeed || 1.0);\r\n        }\r\n\r\n        // Check if isEnabled is defined to be back compatible with prior serialized versions.\r\n        if (parsedLight.isEnabled !== undefined) {\r\n            light.setEnabled(parsedLight.isEnabled);\r\n        }\r\n\r\n        return light;\r\n    }\r\n\r\n    private _hookArrayForExcluded(array: AbstractMesh[]): void {\r\n        const oldPush = array.push;\r\n        array.push = (...items: AbstractMesh[]) => {\r\n            const result = oldPush.apply(array, items);\r\n\r\n            for (const item of items) {\r\n                item._resyncLightSource(this);\r\n            }\r\n\r\n            return result;\r\n        };\r\n\r\n        const oldSplice = array.splice;\r\n        array.splice = (index: number, deleteCount?: number) => {\r\n            const deleted = oldSplice.apply(array, [index, deleteCount]);\r\n\r\n            for (const item of deleted) {\r\n                item._resyncLightSource(this);\r\n            }\r\n\r\n            return deleted;\r\n        };\r\n\r\n        for (const item of array) {\r\n            item._resyncLightSource(this);\r\n        }\r\n    }\r\n\r\n    private _hookArrayForIncludedOnly(array: AbstractMesh[]): void {\r\n        const oldPush = array.push;\r\n        array.push = (...items: AbstractMesh[]) => {\r\n            const result = oldPush.apply(array, items);\r\n\r\n            this._resyncMeshes();\r\n\r\n            return result;\r\n        };\r\n\r\n        const oldSplice = array.splice;\r\n        array.splice = (index: number, deleteCount?: number) => {\r\n            const deleted = oldSplice.apply(array, [index, deleteCount]);\r\n\r\n            this._resyncMeshes();\r\n\r\n            return deleted;\r\n        };\r\n\r\n        this._resyncMeshes();\r\n    }\r\n\r\n    private _resyncMeshes() {\r\n        for (const mesh of this.getScene().meshes) {\r\n            mesh._resyncLightSource(this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Forces the meshes to update their light related information in their rendering used effects\r\n     * @internal Internal Use Only\r\n     */\r\n    public _markMeshesAsLightDirty() {\r\n        for (const mesh of this.getScene().meshes) {\r\n            if (mesh.lightSources.indexOf(this) !== -1) {\r\n                mesh._markSubMeshesAsLightDirty();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Recomputes the cached photometric scale if needed.\r\n     */\r\n    private _computePhotometricScale(): void {\r\n        this._photometricScale = this._getPhotometricScale();\r\n        this.getScene().resetCachedMaterial();\r\n    }\r\n\r\n    /**\r\n     * @returns the Photometric Scale according to the light type and intensity mode.\r\n     */\r\n    private _getPhotometricScale() {\r\n        let photometricScale = 0.0;\r\n        const lightTypeID = this.getTypeID();\r\n\r\n        //get photometric mode\r\n        let photometricMode = this.intensityMode;\r\n        if (photometricMode === Light.INTENSITYMODE_AUTOMATIC) {\r\n            if (lightTypeID === Light.LIGHTTYPEID_DIRECTIONALLIGHT) {\r\n                photometricMode = Light.INTENSITYMODE_ILLUMINANCE;\r\n            } else {\r\n                photometricMode = Light.INTENSITYMODE_LUMINOUSINTENSITY;\r\n            }\r\n        }\r\n\r\n        //compute photometric scale\r\n        switch (lightTypeID) {\r\n            case Light.LIGHTTYPEID_POINTLIGHT:\r\n            case Light.LIGHTTYPEID_SPOTLIGHT:\r\n                switch (photometricMode) {\r\n                    case Light.INTENSITYMODE_LUMINOUSPOWER:\r\n                        photometricScale = 1.0 / (4.0 * Math.PI);\r\n                        break;\r\n                    case Light.INTENSITYMODE_LUMINOUSINTENSITY:\r\n                        photometricScale = 1.0;\r\n                        break;\r\n                    case Light.INTENSITYMODE_LUMINANCE:\r\n                        photometricScale = this.radius * this.radius;\r\n                        break;\r\n                }\r\n                break;\r\n\r\n            case Light.LIGHTTYPEID_DIRECTIONALLIGHT:\r\n                switch (photometricMode) {\r\n                    case Light.INTENSITYMODE_ILLUMINANCE:\r\n                        photometricScale = 1.0;\r\n                        break;\r\n                    case Light.INTENSITYMODE_LUMINANCE: {\r\n                        // When radius (and therefore solid angle) is non-zero a directional lights brightness can be specified via central (peak) luminance.\r\n                        // For a directional light the 'radius' defines the angular radius (in radians) rather than world-space radius (e.g. in metres).\r\n                        let apexAngleRadians = this.radius;\r\n                        // Impose a minimum light angular size to avoid the light becoming an infinitely small angular light source (i.e. a dirac delta function).\r\n                        apexAngleRadians = Math.max(apexAngleRadians, 0.001);\r\n                        const solidAngle = 2.0 * Math.PI * (1.0 - Math.cos(apexAngleRadians));\r\n                        photometricScale = solidAngle;\r\n                        break;\r\n                    }\r\n                }\r\n                break;\r\n\r\n            case Light.LIGHTTYPEID_HEMISPHERICLIGHT:\r\n                // No fall off in hemispheric light.\r\n                photometricScale = 1.0;\r\n                break;\r\n        }\r\n        return photometricScale;\r\n    }\r\n\r\n    /**\r\n     * Reorder the light in the scene according to their defined priority.\r\n     * @internal Internal Use Only\r\n     */\r\n    public _reorderLightsInScene(): void {\r\n        const scene = this.getScene();\r\n        if (this._renderPriority != 0) {\r\n            scene.requireLightSorting = true;\r\n        }\r\n        this.getScene().sortLightsByPriority();\r\n    }\r\n\r\n    /**\r\n     * Prepares the list of defines specific to the light type.\r\n     * @param defines the list of defines\r\n     * @param lightIndex defines the index of the light for the effect\r\n     */\r\n    public abstract prepareLightSpecificDefines(defines: any, lightIndex: number): void;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _isReady() {\r\n        return true;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAIpF,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAG/B,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;;;;;;;;;;AAMjE,MAAgB,KAAM,qJAAQ,OAAI;IA+HpC;;;OAGG,CAEH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IACD;;;OAGG,CACH,IAAW,KAAK,CAAC,KAAa,EAAA;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,oBAAoB,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAChE,CAAC;IASD;;;OAGG,CAEH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD;;;OAGG,CACH,IAAW,aAAa,CAAC,KAAa,EAAA;QAClC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAGD;;OAEG,CAEH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IACD;;OAEG,CACH,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAaD;;;OAGG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD;;;OAGG,CACH,IAAW,aAAa,CAAC,KAAc,EAAA;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAGD;;OAEG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IACD;;OAEG,CACH,IAAW,kBAAkB,CAAC,KAAqB,EAAA;QAC/C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAGD;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IACD;;OAEG,CACH,IAAW,cAAc,CAAC,KAAqB,EAAA;QAC3C,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAID;;;OAGG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IACD;;;OAGG,CACH,IAAW,oBAAoB,CAAC,KAAa,EAAA;QACzC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAID;;;OAGG,CACH,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IACD;;;OAGG,CACH,IAAW,wBAAwB,CAAC,KAAa,EAAA;QAC7C,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAID;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD;;OAEG,CACH,IAAW,YAAY,CAAC,KAAa,EAAA;QACjC,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE,CAAC;YAC/B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACnC,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,UAAmB,EAAA;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,mBAAmB,CAAC,WAAoB,EAAE,WAAiC,EAAA;QAC9E,OAAO,IAAI,CAAC;IAChB,CAAC;IA6BD;;;;;OAKG,CACH,YAAY,IAAY,EAAE,KAAa,CAAA;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAvQ9B;;WAEG,CAEI,IAAA,CAAA,OAAO,GAAG,kKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAE3C;;;WAGG,CAEI,IAAA,CAAA,QAAQ,GAAG,kKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAE5C;;;;;;;WAOG,CAEI,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC,eAAe,CAAC;QAE3C;;;;WAIG,CAEI,IAAA,CAAA,SAAS,GAAG,GAAG,CAAC;QAEf,IAAA,CAAA,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;QACxB,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC;QAmBnC;;;WAGG,CACK,IAAA,CAAA,iBAAiB,GAAG,GAAG,CAAC;QAExB,IAAA,CAAA,cAAc,GAAW,KAAK,CAAC,uBAAuB,CAAC;QAkBvD,IAAA,CAAA,OAAO,GAAG,OAAO,CAAC;QAkB1B;;;WAGG,CAEI,IAAA,CAAA,cAAc,GAAW,CAAC,CAAC;QAG1B,IAAA,CAAA,cAAc,GAAY,IAAI,CAAC;QAoD/B,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC;QAkB1B,IAAA,CAAA,yBAAyB,GAAG,CAAC,CAAC;QAkB9B,IAAA,CAAA,aAAa,GAAG,CAAC,CAAC;QAuC1B;;;WAGG,CACI,IAAA,CAAA,iBAAiB,GAAsD,IAAI,CAAC;QAEnF;;WAEG,CACI,IAAA,CAAA,kBAAkB,GAAG,IAAI,KAAK,EAAU,CAAC;QAEhD;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,IAAI,KAAK,EAAU,CAAC;QAuHpD,cAAA,EAAgB,CACA,IAAA,CAAA,QAAQ,GAAG,IAAI,CAAC;QAnG5B,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,GAAG,sKAAI,gBAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;QACjG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,kBAAkB,GAAG,EAAoB,CAAC;QAC/C,IAAI,CAAC,cAAc,GAAG,EAAoB,CAAC;QAE3C,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAYD;;;;;OAKG,CACH,6DAA6D;IACtD,wBAAwB,CAAC,MAAc,EAAE,UAAkB,EAAA;QAC9D,yBAAyB;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG,CACI,UAAU,CAAC,UAAkB,EAAE,KAAY,EAAE,MAAc,EAAE,WAAoB,EAAE,cAAc,GAAG,IAAI,EAAA;QAC3G,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxC,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,GAAG,SAAS,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,gBAAgB,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YACjH,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;YAEpC,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAElD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAEzC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,eAAe,gKAAE,YAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,eAAe,gKAAE,YAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAC9F,IAAI,WAAW,EAAE,CAAC;gBACd,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe,gKAAE,YAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/D,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,gBAAgB,EAAE,0KAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YACpG,CAAC;YACD,UAAU,GAAG,IAAI,CAAC;QACtB,CAAC;QAED,2CAA2C;QAC3C,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAEjD,UAAU;QACV,IAAI,KAAK,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,cAAc,EAAE,CAAC;YAC/D,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACjG,IAAI,eAAe,EAAE,CAAC;gBAClB,eAAe,CAAC,eAAe,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACnD,UAAU,GAAG,IAAI,CAAC;YACtB,CAAC;QACL,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;QACjC,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;QAC5C,CAAC;IACL,CAAC;IAUD;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,OAAO,CAAC;IACnB,CAAC;IAKD;;;;OAIG,CACa,QAAQ,CAAC,WAAqB,EAAA;QAC1C,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,GAAG,IAAI,UAAU,GAAG;YAAC,OAAO;YAAE,aAAa;YAAE,MAAM;YAAE,aAAa;SAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACtF,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC9C,GAAG,IAAI,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACzE,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED,cAAA,EAAgB,CACG,uBAAuB,GAAA;QACtC,KAAK,CAAC,uBAAuB,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC;IACL,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAAc,EAAA;QACrC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAExB,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,SAA2B,IAAI,EAAA;QACrD,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IACtD,CAAC;IAED;;;OAGG,CACI,mBAAmB,GAAA;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG,CACI,mBAAmB,GAAA;QACtB,sKAAO,UAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED;;;;OAIG,CACI,aAAa,CAAC,IAAkB,EAAA;QACnC,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAChH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACpG,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,wBAAwB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAChG,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChF,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACa,OAAO,CAAC,YAAsB,EAAE,0BAA0B,GAAG,KAAK,EAAA;QAC9E,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YACjD,IAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAE,CAAC;gBACvE,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC;gBAClC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC9B,CAAC;YACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAClC,CAAC;QAED,aAAa;QACb,IAAI,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACzD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAClD,CAAC;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,qBAAqB;QACrB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAE,CAAC;YACxC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAE9B,oBAAoB;QACpB,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAClC,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAC;IAC5D,CAAC;IAED;;;OAGG,CACH,gEAAgE;IACzD,SAAS,GAAA;QACZ,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG,CACI,kBAAkB,GAAA;QACrB,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;IACnD,CAAC;IAED;;;;;OAKG,CACa,KAAK,CAAC,IAAY,EAAE,YAA4B,IAAI,EAAA;QAChE,MAAM,WAAW,GAAG,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE1F,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,WAAW,8KAAG,sBAAmB,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACjE,IAAI,IAAI,EAAE,CAAC;YACP,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;QAC5B,CAAC;QACD,IAAI,SAAS,EAAE,CAAC;YACZ,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;QACnC,CAAC;QACD,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAEzC,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAErD,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,8KAAG,sBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,OAAO;QACP,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE5C,SAAS;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;QACxD,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,mBAAmB,CAAC,iBAAiB,GAAG,EAAE,CAAC;YAC3C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;gBACrC,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,mBAAmB,CAAC,qBAAqB,GAAG,EAAE,CAAC;YAC/C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,kBAAkB,CAAE,CAAC;gBACzC,mBAAmB,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC;QAED,aAAa;mLACb,sBAAmB,CAAC,0BAA0B,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;QAC1E,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE7D,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEjD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;;OAOG,CACH,MAAM,CAAC,sBAAsB,CAAC,IAAY,EAAE,IAAY,EAAE,KAAY,EAAA;QAClE,MAAM,eAAe,+IAAG,OAAI,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAE1E,IAAI,eAAe,EAAE,CAAC;YAClB,OAAoB,eAAe,CAAC;QACxC,CAAC;QAED,6CAA6C;QAC7C,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,KAAK,CAAC,WAAgB,EAAE,KAAY,EAAA;QAC9C,MAAM,WAAW,GAAG,KAAK,CAAC,sBAAsB,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAE5F,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAG,iMAAmB,CAAC,KAAK,CAAC,WAAW,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;QAEzE,yBAAyB;QACzB,IAAI,WAAW,CAAC,iBAAiB,EAAE,CAAC;YAChC,KAAK,CAAC,kBAAkB,GAAG,WAAW,CAAC,iBAAiB,CAAC;QAC7D,CAAC;QAED,IAAI,WAAW,CAAC,qBAAqB,EAAE,CAAC;YACpC,KAAK,CAAC,sBAAsB,GAAG,WAAW,CAAC,qBAAqB,CAAC;QACrE,CAAC;QAED,SAAS;QACT,IAAI,WAAW,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACrC,KAAK,CAAC,gBAAgB,GAAG,WAAW,CAAC,QAAQ,CAAC;QAClD,CAAC;QAED,IAAI,WAAW,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YAChD,KAAK,CAAC,2BAA2B,GAAG,WAAW,CAAC,mBAAmB,CAAC;QACxE,CAAC;QAED,UAAU;QACV,IAAI,WAAW,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACxC,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;QAChD,CAAC;QAED,YAAY;QACZ,IAAI,WAAW,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACzC,KAAK,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;QAClD,CAAC;QAED,aAAa;QACb,IAAI,WAAW,CAAC,UAAU,EAAE,CAAC;YACzB,IAAK,IAAI,cAAc,GAAG,CAAC,EAAE,cAAc,GAAG,WAAW,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE,CAAE,CAAC;gBAC5F,MAAM,eAAe,GAAG,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBAC/D,MAAM,aAAa,gKAAG,WAAA,AAAQ,EAAC,mBAAmB,CAAC,CAAC;gBACpD,IAAI,aAAa,EAAE,CAAC;oBAChB,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC;gBAChE,CAAC;YACL,CAAC;wJACD,OAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;YAC1B,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,eAAe,EAAE,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,eAAe,EAAE,WAAW,CAAC,gBAAgB,IAAI,GAAG,CAAC,CAAC;QAC1J,CAAC;QAED,sFAAsF;QACtF,IAAI,WAAW,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACtC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,qBAAqB,CAAC,KAAqB,EAAA;QAC/C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;QAC3B,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,KAAqB,EAAE,EAAE;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAE3C,KAAK,MAAM,IAAI,IAAI,KAAK,CAAE,CAAC;gBACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;YAED,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC;QAEF,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,KAAK,CAAC,MAAM,GAAG,CAAC,KAAa,EAAE,WAAoB,EAAE,EAAE;YACnD,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE;gBAAC,KAAK;gBAAE,WAAW;aAAC,CAAC,CAAC;YAE7D,KAAK,MAAM,IAAI,IAAI,OAAO,CAAE,CAAC;gBACzB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;YAED,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,KAAK,CAAE,CAAC;YACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,KAAqB,EAAA;QACnD,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;QAC3B,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,KAAqB,EAAE,EAAE;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAE3C,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,OAAO,MAAM,CAAC;QAClB,CAAC,CAAC;QAEF,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,KAAK,CAAC,MAAM,GAAG,CAAC,KAAa,EAAE,WAAoB,EAAE,EAAE;YACnD,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE;gBAAC,KAAK;gBAAE,WAAW;aAAC,CAAC,CAAC;YAE7D,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC;QAEF,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,aAAa,GAAA;QACjB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAE,CAAC;YACxC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,uBAAuB,GAAA;QAC1B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAE,CAAC;YACxC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACzC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACtC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACK,wBAAwB,GAAA;QAC5B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACrD,IAAI,CAAC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG,CACK,oBAAoB,GAAA;QACxB,IAAI,gBAAgB,GAAG,GAAG,CAAC;QAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAErC,sBAAsB;QACtB,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,eAAe,KAAK,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACpD,IAAI,WAAW,KAAK,KAAK,CAAC,4BAA4B,EAAE,CAAC;gBACrD,eAAe,GAAG,KAAK,CAAC,yBAAyB,CAAC;YACtD,CAAC,MAAM,CAAC;gBACJ,eAAe,GAAG,KAAK,CAAC,+BAA+B,CAAC;YAC5D,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,OAAQ,WAAW,EAAE,CAAC;YAClB,KAAK,KAAK,CAAC,sBAAsB,CAAC;YAClC,KAAK,KAAK,CAAC,qBAAqB;gBAC5B,OAAQ,eAAe,EAAE,CAAC;oBACtB,KAAK,KAAK,CAAC,2BAA2B;wBAClC,gBAAgB,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;wBACzC,MAAM;oBACV,KAAK,KAAK,CAAC,+BAA+B;wBACtC,gBAAgB,GAAG,GAAG,CAAC;wBACvB,MAAM;oBACV,KAAK,KAAK,CAAC,uBAAuB;wBAC9B,gBAAgB,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;wBAC7C,MAAM;gBACd,CAAC;gBACD,MAAM;YAEV,KAAK,KAAK,CAAC,4BAA4B;gBACnC,OAAQ,eAAe,EAAE,CAAC;oBACtB,KAAK,KAAK,CAAC,yBAAyB;wBAChC,gBAAgB,GAAG,GAAG,CAAC;wBACvB,MAAM;oBACV,KAAK,KAAK,CAAC,uBAAuB,CAAC;wBAAC,CAAC;4BACjC,qIAAqI;4BACrI,gIAAgI;4BAChI,IAAI,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC;4BACnC,0IAA0I;4BAC1I,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;4BACrD,MAAM,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;4BACtE,gBAAgB,GAAG,UAAU,CAAC;4BAC9B,MAAM;wBACV,CAAC;gBACL,CAAC;gBACD,MAAM;YAEV,KAAK,KAAK,CAAC,4BAA4B;gBACnC,oCAAoC;gBACpC,gBAAgB,GAAG,GAAG,CAAC;gBACvB,MAAM;QACd,CAAC;QACD,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACI,qBAAqB,GAAA;QACxB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,EAAE,CAAC;YAC5B,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,CAAC;IAC3C,CAAC;IASD;;OAEG,CACI,QAAQ,GAAA;QACX,OAAO,IAAI,CAAC;IAChB,CAAC;;AAn5BD;;;GAGG,CACoB,MAAA,eAAe,mKAAG,iBAAc,CAAC,eAAlB,CAAkC;AAExE;;GAEG,CACoB,MAAA,gBAAgB,mKAAG,iBAAc,CAAC,gBAAlB,CAAmC;AAE1E;;;GAGG,CACoB,MAAA,YAAY,mKAAG,iBAAc,CAAC,YAAlB,CAA+B;AAElE;;;GAGG,CACoB,MAAA,gBAAgB,mKAAG,iBAAc,CAAC,gBAAlB,CAAmC;AAE1E,qBAAqB;AACrB;;;;;GAKG,CACoB,MAAA,gBAAgB,mKAAG,iBAAc,CAAC,gBAAlB,CAAmC;AAC1E;;;;GAIG,CACoB,MAAA,iBAAiB,mKAAG,iBAAc,CAAC,iBAAlB,CAAoC;AAC5E;;;;GAIG,CACoB,MAAA,oBAAoB,mKAAG,iBAAc,CAAC,oBAAlB,CAAuC;AAElF,wBAAwB;AACxB;;;;GAIG,CACoB,MAAA,uBAAuB,mKAAG,iBAAc,CAAC,uBAAlB,CAA0C;AACxF;;GAEG,CACoB,MAAA,2BAA2B,mKAAG,iBAAc,CAAC,2BAAlB,CAA8C;AAChG;;GAEG,CACoB,MAAA,+BAA+B,mKAAG,iBAAc,CAAC,+BAAlB,CAAkD;AACxG;;GAEG,CACoB,MAAA,yBAAyB,mKAAG,iBAAc,CAAC,yBAAlB,CAA4C;AAC5F;;GAEG,CACoB,MAAA,uBAAuB,mKAAG,iBAAc,CAAC,uBAAlB,CAA0C;AAExF,yBAAyB;AACzB;;GAEG,CACoB,MAAA,sBAAsB,mKAAG,iBAAc,CAAC,sBAAlB,CAAyC;AACtF;;GAEG,CACoB,MAAA,4BAA4B,kKAAG,kBAAc,CAAC,4BAAlB,CAA+C;AAClG;;GAEG,CACoB,MAAA,qBAAqB,mKAAG,iBAAc,CAAC,qBAAlB,CAAwC;AACpF;;GAEG,CACoB,MAAA,4BAA4B,mKAAG,iBAAc,CAAC,4BAAlB,CAA+C;AAElG;;GAEG,CACoB,MAAA,0BAA0B,GAAG,iLAAc,CAAC,0BAAlB,CAA6C;wJAMvF,aAAA,EAAA;kKADN,oBAAA,AAAiB,EAAE;sCACuB;CAOpC,oKAAA,EAAA;kKADN,oBAAA,AAAiB,EAAE;uCACwB;wJAWrC,aAAA,EAAA;KADN,yKAAA,AAAS,EAAE;0CAC+B;wJAQpC,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;wCACW;wJAUvB,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;kCAGX;IAsBD,iKAAA,EAAA;kKADC,YAAA,AAAS,EAAE;0CAGX;wJAeD,aAAA,EAAA;KADC,yKAAA,AAAS,EAAE;mCAGX;wJAUO,aAAA,EAAA;kKADP,YAAA,AAAS,EAAE;8CACoB;wJAMzB,aAAA,EAAA;kKADN,mBAAA,AAAgB,EAAC,uBAAuB,CAAC;6CACR;CAG1B,oKAAA,EAAA;kKADP,YAAA,AAAS,EAAC,eAAe,CAAC;6CACY;AAoD/B,qKAAA,EAAA;kKADP,YAAA,AAAS,EAAC,sBAAsB,CAAC;oDACA;CAkB1B,oKAAA,EAAA;kKADP,YAAA,AAAS,EAAC,0BAA0B,CAAC;wDACA;wJAkB9B,aAAA,EAAA;kKADP,YAAA,AAAS,EAAC,cAAc,CAAC;4CACA", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/hemisphericLight.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/hemisphericLight.ts"], "sourcesContent": ["import { serializeAsColor3, serializeAsVector3 } from \"../Misc/decorators\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport { Node } from \"../node\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Light } from \"./light\";\r\nimport type { IShadowGenerator } from \"./Shadows/shadowGenerator\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\nNode.AddNodeConstructor(\"Light_Type_3\", (name, scene) => {\r\n    return () => new HemisphericLight(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * The HemisphericLight simulates the ambient environment light,\r\n * so the passed direction is the light reflection direction, not the incoming direction.\r\n */\r\nexport class HemisphericLight extends Light {\r\n    /**\r\n     * The groundColor is the light in the opposite direction to the one specified during creation.\r\n     * You can think of the diffuse and specular light as coming from the centre of the object in the given direction and the groundColor light in the opposite direction.\r\n     */\r\n    @serializeAsColor3()\r\n    public groundColor = new Color3(0.0, 0.0, 0.0);\r\n\r\n    /**\r\n     * The light reflection direction, not the incoming direction.\r\n     */\r\n    @serializeAsVector3()\r\n    public direction: Vector3;\r\n\r\n    /**\r\n     * Creates a HemisphericLight object in the scene according to the passed direction (Vector3).\r\n     * The HemisphericLight simulates the ambient environment light, so the passed direction is the light reflection direction, not the incoming direction.\r\n     * The HemisphericLight can't cast shadows.\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n     * @param name The friendly name of the light\r\n     * @param direction The direction of the light reflection\r\n     * @param scene The scene the light belongs to\r\n     */\r\n    constructor(name: string, direction: Vector3, scene?: Scene) {\r\n        super(name, scene);\r\n        this.direction = direction || Vector3.Up();\r\n    }\r\n\r\n    protected _buildUniformLayout(): void {\r\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightGround\", 3);\r\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\r\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\r\n        this._uniformBuffer.create();\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"HemisphericLight\".\r\n     * @returns The class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"HemisphericLight\";\r\n    }\r\n\r\n    /**\r\n     * Sets the HemisphericLight direction towards the passed target (Vector3).\r\n     * Returns the updated direction.\r\n     * @param target The target the direction should point to\r\n     * @returns The computed direction\r\n     */\r\n    public setDirectionToTarget(target: Vector3): Vector3 {\r\n        this.direction = Vector3.Normalize(target.subtract(Vector3.Zero()));\r\n        return this.direction;\r\n    }\r\n\r\n    /**\r\n     * Returns the shadow generator associated to the light.\r\n     * @returns Always null for hemispheric lights because it does not support shadows.\r\n     */\r\n    public override getShadowGenerator(): Nullable<IShadowGenerator> {\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Effect object with the HemisphericLight normalized direction and color and the passed name (string).\r\n     * @param _effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The hemispheric light\r\n     */\r\n    public transferToEffect(_effect: Effect, lightIndex: string): HemisphericLight {\r\n        const normalizeDirection = Vector3.Normalize(this.direction);\r\n        this._uniformBuffer.updateFloat4(\"vLightData\", normalizeDirection.x, normalizeDirection.y, normalizeDirection.z, 0.0, lightIndex);\r\n        this._uniformBuffer.updateColor3(\"vLightGround\", this.groundColor.scale(this.intensity), lightIndex);\r\n        return this;\r\n    }\r\n\r\n    public transferToNodeMaterialEffect(effect: Effect, lightDataUniformName: string) {\r\n        const normalizeDirection = Vector3.Normalize(this.direction);\r\n        effect.setFloat3(lightDataUniformName, normalizeDirection.x, normalizeDirection.y, normalizeDirection.z);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Computes the world matrix of the node\r\n     * @returns the world matrix\r\n     */\r\n    public override computeWorldMatrix(): Matrix {\r\n        if (!this._worldMatrix) {\r\n            this._worldMatrix = Matrix.Identity();\r\n        }\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    /**\r\n     * Returns the integer 3.\r\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public override getTypeID(): number {\r\n        return Light.LIGHTTYPEID_HEMISPHERICLIGHT;\r\n    }\r\n\r\n    /**\r\n     * Prepares the list of defines specific to the light type.\r\n     * @param defines the list of defines\r\n     * @param lightIndex defines the index of the light for the effect\r\n     */\r\n    public prepareLightSpecificDefines(defines: any, lightIndex: number): void {\r\n        defines[\"HEMILIGHT\" + lightIndex] = true;\r\n    }\r\n}\r\n\r\n// Register Class Name\r\nRegisterClass(\"BABYLON.HemisphericLight\", HemisphericLight);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAG3E,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;;;;;;;;4IAElD,OAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,CAAG,CAAD,GAAK,gBAAgB,CAAC,IAAI,iKAAE,UAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC;AAMG,MAAO,gBAAiB,gKAAQ,QAAK;IAcvC;;;;;;;;OAQG,CACH,YAAY,IAAY,EAAE,SAAkB,EAAE,KAAa,CAAA;QACvD,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAvBvB;;;WAGG,CAEI,IAAA,CAAA,WAAW,GAAG,kKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAmB3C,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,yKAAO,CAAC,EAAE,EAAE,CAAC;IAC/C,CAAC;IAES,mBAAmB,GAAA;QACzB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;;;;OAKG,CACI,oBAAoB,CAAC,MAAe,EAAA;QACvC,IAAI,CAAC,SAAS,GAAG,yKAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,gKAAC,UAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG,CACa,kBAAkB,GAAA;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,OAAe,EAAE,UAAkB,EAAA;QACvD,MAAM,kBAAkB,kKAAG,UAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7D,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;QAClI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,CAAC;QACrG,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4BAA4B,CAAC,MAAc,EAAE,oBAA4B,EAAA;QAC5E,MAAM,kBAAkB,kKAAG,UAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7D,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACzG,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACa,kBAAkB,GAAA;QAC9B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,IAAI,CAAC,YAAY,iKAAG,UAAM,CAAC,QAAQ,EAAE,CAAC;QAC1C,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACH,gEAAgE;IAChD,SAAS,GAAA;QACrB,OAAO,+JAAK,CAAC,4BAA4B,CAAC;IAC9C,CAAC;IAED;;;;OAIG,CACI,2BAA2B,CAAC,OAAY,EAAE,UAAkB,EAAA;QAC/D,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;IAC7C,CAAC;CACJ;AA1GU,qKAAA,EAAA;kKADN,oBAAA,AAAiB,EAAE;qDAC2B;wJAMxC,aAAA,EAAA;kKADN,qBAAA,AAAkB,EAAE;mDACK;AAsG9B,sBAAsB;6JACtB,gBAAA,AAAa,EAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/Shadows/shadowGenerator.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/Shadows/shadowGenerator.ts"], "sourcesContent": ["import type { SmartArray } from \"../../Misc/smartArray\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Matrix, Vector3, Vector2 } from \"../../Maths/math.vector\";\r\nimport { Color4 } from \"../../Maths/math.color\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\n\r\nimport type { IShadowLight } from \"../../Lights/shadowLight\";\r\nimport { Light } from \"../../Lights/light\";\r\nimport type { MaterialDefines } from \"../../Materials/materialDefines\";\r\nimport type { Effect, IEffectCreationOptions } from \"../../Materials/effect\";\r\nimport { Texture } from \"../../Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\n\r\nimport { PostProcess } from \"../../PostProcesses/postProcess\";\r\nimport { BlurPostProcess } from \"../../PostProcesses/blurPostProcess\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { _WarnImport } from \"../../Misc/devTools\";\r\nimport { EffectFallbacks } from \"../../Materials/effectFallbacks\";\r\nimport { RenderingManager } from \"../../Rendering/renderingManager\";\r\nimport { DrawWrapper } from \"../../Materials/drawWrapper\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\n\r\nimport { AddClipPlaneUniforms, BindClipPlane, PrepareStringDefinesForClipPlanes } from \"../../Materials/clipPlaneMaterialHelper\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport {\r\n    BindMorphTargetParameters,\r\n    BindSceneUniformBuffer,\r\n    PrepareDefinesAndAttributesForMorphTargets,\r\n    PushAttributesForInstances,\r\n} from \"../../Materials/materialHelper.functions\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Defines the options associated with the creation of a custom shader for a shadow generator.\r\n */\r\nexport interface ICustomShaderOptions {\r\n    /**\r\n     * Gets or sets the custom shader name to use\r\n     */\r\n    shaderName: string;\r\n\r\n    /**\r\n     * The list of attribute names used in the shader\r\n     */\r\n    attributes?: string[];\r\n\r\n    /**\r\n     * The list of uniform names used in the shader\r\n     */\r\n    uniforms?: string[];\r\n\r\n    /**\r\n     * The list of sampler names used in the shader\r\n     */\r\n    samplers?: string[];\r\n\r\n    /**\r\n     * The list of defines used in the shader\r\n     */\r\n    defines?: string[];\r\n}\r\n\r\n/**\r\n * Interface to implement to create a shadow generator compatible with BJS.\r\n */\r\nexport interface IShadowGenerator {\r\n    /** Gets or set the id of the shadow generator. It will be the one from the light if not defined */\r\n    id: string;\r\n\r\n    /**\r\n     * Specifies if the `ShadowGenerator` should be serialized, `true` to skip serialization.\r\n     * Note a `ShadowGenerator` will not be serialized if its light has `doNotSerialize=true`\r\n     */\r\n    doNotSerialize?: boolean;\r\n\r\n    /**\r\n     * Gets the main RTT containing the shadow map (usually storing depth from the light point of view).\r\n     * @returns The render target texture if present otherwise, null\r\n     */\r\n    getShadowMap(): Nullable<RenderTargetTexture>;\r\n\r\n    /**\r\n     * Determine whether the shadow generator is ready or not (mainly all effects and related post processes needs to be ready).\r\n     * @param subMesh The submesh we want to render in the shadow map\r\n     * @param useInstances Defines whether will draw in the map using instances\r\n     * @param isTransparent Indicates that isReady is called for a transparent subMesh\r\n     * @returns true if ready otherwise, false\r\n     */\r\n    isReady(subMesh: SubMesh, useInstances: boolean, isTransparent: boolean): boolean;\r\n\r\n    /**\r\n     * Prepare all the defines in a material relying on a shadow map at the specified light index.\r\n     * @param defines Defines of the material we want to update\r\n     * @param lightIndex Index of the light in the enabled light list of the material\r\n     */\r\n    prepareDefines(defines: MaterialDefines, lightIndex: number): void;\r\n    /**\r\n     * Binds the shadow related information inside of an effect (information like near, far, darkness...\r\n     * defined in the generator but impacting the effect).\r\n     * It implies the uniforms available on the materials are the standard BJS ones.\r\n     * @param lightIndex Index of the light in the enabled light list of the material owning the effect\r\n     * @param effect The effect we are binding the information for\r\n     */\r\n    bindShadowLight(lightIndex: string, effect: Effect): void;\r\n    /**\r\n     * Gets the transformation matrix used to project the meshes into the map from the light point of view.\r\n     * (eq to shadow projection matrix * light transform matrix)\r\n     * @returns The transform matrix used to create the shadow map\r\n     */\r\n    getTransformMatrix(): Matrix;\r\n\r\n    /**\r\n     * Recreates the shadow map dependencies like RTT and post processes. This can be used during the switch between\r\n     * Cube and 2D textures for instance.\r\n     */\r\n    recreateShadowMap(): void;\r\n\r\n    /**\r\n     * Forces all the attached effect to compile to enable rendering only once ready vs. lazily compiling effects.\r\n     * @param onCompiled Callback triggered at the and of the effects compilation\r\n     * @param options Sets of optional options forcing the compilation with different modes\r\n     */\r\n    forceCompilation(onCompiled?: (generator: IShadowGenerator) => void, options?: Partial<{ useInstances: boolean }>): void;\r\n\r\n    /**\r\n     * Forces all the attached effect to compile to enable rendering only once ready vs. lazily compiling effects.\r\n     * @param options Sets of optional options forcing the compilation with different modes\r\n     * @returns A promise that resolves when the compilation completes\r\n     */\r\n    forceCompilationAsync(options?: Partial<{ useInstances: boolean }>): Promise<void>;\r\n\r\n    /**\r\n     * Serializes the shadow generator setup to a json object.\r\n     * @returns The serialized JSON object\r\n     */\r\n    serialize(): any;\r\n\r\n    /**\r\n     * Disposes the Shadow map and related Textures and effects.\r\n     */\r\n    dispose(): void;\r\n}\r\n\r\n/**\r\n * Default implementation IShadowGenerator.\r\n * This is the main object responsible of generating shadows in the framework.\r\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/shadows\r\n * @see [WebGL](https://playground.babylonjs.com/#IFYDRS#0)\r\n * @see [WebGPU](https://playground.babylonjs.com/#IFYDRS#835)\r\n */\r\nexport class ShadowGenerator implements IShadowGenerator {\r\n    /**\r\n     * Name of the shadow generator class\r\n     */\r\n    public static CLASSNAME = \"ShadowGenerator\";\r\n\r\n    /**\r\n     * Force all the shadow generators to compile to glsl even on WebGPU engines.\r\n     * False by default. This is mostly meant for backward compatibility.\r\n     */\r\n    public static ForceGLSL = false;\r\n\r\n    /**\r\n     * Shadow generator mode None: no filtering applied.\r\n     */\r\n    public static readonly FILTER_NONE = 0;\r\n    /**\r\n     * Shadow generator mode ESM: Exponential Shadow Mapping.\r\n     * (http://developer.download.nvidia.com/presentations/2008/GDC/GDC08_SoftShadowMapping.pdf)\r\n     */\r\n    public static readonly FILTER_EXPONENTIALSHADOWMAP = 1;\r\n    /**\r\n     * Shadow generator mode Poisson Sampling: Percentage Closer Filtering.\r\n     * (Multiple Tap around evenly distributed around the pixel are used to evaluate the shadow strength)\r\n     */\r\n    public static readonly FILTER_POISSONSAMPLING = 2;\r\n    /**\r\n     * Shadow generator mode ESM: Blurred Exponential Shadow Mapping.\r\n     * (http://developer.download.nvidia.com/presentations/2008/GDC/GDC08_SoftShadowMapping.pdf)\r\n     */\r\n    public static readonly FILTER_BLUREXPONENTIALSHADOWMAP = 3;\r\n    /**\r\n     * Shadow generator mode ESM: Exponential Shadow Mapping using the inverse of the exponential preventing\r\n     * edge artifacts on steep falloff.\r\n     * (http://developer.download.nvidia.com/presentations/2008/GDC/GDC08_SoftShadowMapping.pdf)\r\n     */\r\n    public static readonly FILTER_CLOSEEXPONENTIALSHADOWMAP = 4;\r\n    /**\r\n     * Shadow generator mode ESM: Blurred Exponential Shadow Mapping using the inverse of the exponential preventing\r\n     * edge artifacts on steep falloff.\r\n     * (http://developer.download.nvidia.com/presentations/2008/GDC/GDC08_SoftShadowMapping.pdf)\r\n     */\r\n    public static readonly FILTER_BLURCLOSEEXPONENTIALSHADOWMAP = 5;\r\n    /**\r\n     * Shadow generator mode PCF: Percentage Closer Filtering\r\n     * benefits from Webgl 2 shadow samplers. Fallback to Poisson Sampling in Webgl 1\r\n     * (https://developer.nvidia.com/gpugems/GPUGems/gpugems_ch11.html)\r\n     */\r\n    public static readonly FILTER_PCF = 6;\r\n    /**\r\n     * Shadow generator mode PCSS: Percentage Closering Soft Shadow.\r\n     * benefits from Webgl 2 shadow samplers. Fallback to Poisson Sampling in Webgl 1\r\n     * Contact Hardening\r\n     */\r\n    public static readonly FILTER_PCSS = 7;\r\n\r\n    /**\r\n     * Reserved for PCF and PCSS\r\n     * Highest Quality.\r\n     *\r\n     * Execute PCF on a 5*5 kernel improving a lot the shadow aliasing artifacts.\r\n     *\r\n     * Execute PCSS with 32 taps blocker search and 64 taps PCF.\r\n     */\r\n    public static readonly QUALITY_HIGH = 0;\r\n    /**\r\n     * Reserved for PCF and PCSS\r\n     * Good tradeoff for quality/perf cross devices\r\n     *\r\n     * Execute PCF on a 3*3 kernel.\r\n     *\r\n     * Execute PCSS with 16 taps blocker search and 32 taps PCF.\r\n     */\r\n    public static readonly QUALITY_MEDIUM = 1;\r\n    /**\r\n     * Reserved for PCF and PCSS\r\n     * The lowest quality but the fastest.\r\n     *\r\n     * Execute PCF on a 1*1 kernel.\r\n     *\r\n     * Execute PCSS with 16 taps blocker search and 16 taps PCF.\r\n     */\r\n    public static readonly QUALITY_LOW = 2;\r\n\r\n    /**\r\n     * Defines the default alpha cutoff value used for transparent alpha tested materials.\r\n     */\r\n    public static DEFAULT_ALPHA_CUTOFF = 0.5;\r\n\r\n    /** Gets or set the id of the shadow generator. It will be the one from the light if not defined */\r\n    public id: string;\r\n\r\n    /** Gets or sets the custom shader name to use */\r\n    public customShaderOptions: ICustomShaderOptions;\r\n\r\n    /** Gets or sets a custom function to allow/disallow rendering a sub mesh in the shadow map */\r\n    public customAllowRendering: (subMesh: SubMesh) => boolean;\r\n\r\n    /**\r\n     * Observable triggered before the shadow is rendered. Can be used to update internal effect state\r\n     */\r\n    public onBeforeShadowMapRenderObservable = new Observable<Effect>();\r\n\r\n    /**\r\n     * Observable triggered after the shadow is rendered. Can be used to restore internal effect state\r\n     */\r\n    public onAfterShadowMapRenderObservable = new Observable<Effect>();\r\n\r\n    /**\r\n     * Observable triggered before a mesh is rendered in the shadow map.\r\n     * Can be used to update internal effect state (that you can get from the onBeforeShadowMapRenderObservable)\r\n     */\r\n    public onBeforeShadowMapRenderMeshObservable = new Observable<Mesh>();\r\n\r\n    /**\r\n     * Observable triggered after a mesh is rendered in the shadow map.\r\n     * Can be used to update internal effect state (that you can get from the onAfterShadowMapRenderObservable)\r\n     */\r\n    public onAfterShadowMapRenderMeshObservable = new Observable<Mesh>();\r\n\r\n    /**\r\n     * Specifies if the `ShadowGenerator` should be serialized, `true` to skip serialization.\r\n     * Note a `ShadowGenerator` will not be serialized if its light has `doNotSerialize=true`\r\n     */\r\n    public doNotSerialize = false;\r\n\r\n    protected _bias = 0.00005;\r\n    /**\r\n     * Gets the bias: offset applied on the depth preventing acnea (in light direction).\r\n     */\r\n    public get bias(): number {\r\n        return this._bias;\r\n    }\r\n    /**\r\n     * Sets the bias: offset applied on the depth preventing acnea (in light direction).\r\n     */\r\n    public set bias(bias: number) {\r\n        this._bias = bias;\r\n    }\r\n\r\n    protected _normalBias = 0;\r\n    /**\r\n     * Gets the normalBias: offset applied on the depth preventing acnea (along side the normal direction and proportional to the light/normal angle).\r\n     */\r\n    public get normalBias(): number {\r\n        return this._normalBias;\r\n    }\r\n    /**\r\n     * Sets the normalBias: offset applied on the depth preventing acnea (along side the normal direction and proportional to the light/normal angle).\r\n     */\r\n    public set normalBias(normalBias: number) {\r\n        this._normalBias = normalBias;\r\n    }\r\n\r\n    protected _blurBoxOffset = 1;\r\n    /**\r\n     * Gets the blur box offset: offset applied during the blur pass.\r\n     * Only useful if useKernelBlur = false\r\n     */\r\n    public get blurBoxOffset(): number {\r\n        return this._blurBoxOffset;\r\n    }\r\n    /**\r\n     * Sets the blur box offset: offset applied during the blur pass.\r\n     * Only useful if useKernelBlur = false\r\n     */\r\n    public set blurBoxOffset(value: number) {\r\n        if (this._blurBoxOffset === value) {\r\n            return;\r\n        }\r\n\r\n        this._blurBoxOffset = value;\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _blurScale = 2;\r\n    /**\r\n     * Gets the blur scale: scale of the blurred texture compared to the main shadow map.\r\n     * 2 means half of the size.\r\n     */\r\n    public get blurScale(): number {\r\n        return this._blurScale;\r\n    }\r\n    /**\r\n     * Sets the blur scale: scale of the blurred texture compared to the main shadow map.\r\n     * 2 means half of the size.\r\n     */\r\n    public set blurScale(value: number) {\r\n        if (this._blurScale === value) {\r\n            return;\r\n        }\r\n\r\n        this._blurScale = value;\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _blurKernel = 1;\r\n    /**\r\n     * Gets the blur kernel: kernel size of the blur pass.\r\n     * Only useful if useKernelBlur = true\r\n     */\r\n    public get blurKernel(): number {\r\n        return this._blurKernel;\r\n    }\r\n    /**\r\n     * Sets the blur kernel: kernel size of the blur pass.\r\n     * Only useful if useKernelBlur = true\r\n     */\r\n    public set blurKernel(value: number) {\r\n        if (this._blurKernel === value) {\r\n            return;\r\n        }\r\n\r\n        this._blurKernel = value;\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _useKernelBlur = false;\r\n    /**\r\n     * Gets whether the blur pass is a kernel blur (if true) or box blur.\r\n     * Only useful in filtered mode (useBlurExponentialShadowMap...)\r\n     */\r\n    public get useKernelBlur(): boolean {\r\n        return this._useKernelBlur;\r\n    }\r\n    /**\r\n     * Sets whether the blur pass is a kernel blur (if true) or box blur.\r\n     * Only useful in filtered mode (useBlurExponentialShadowMap...)\r\n     */\r\n    public set useKernelBlur(value: boolean) {\r\n        if (this._useKernelBlur === value) {\r\n            return;\r\n        }\r\n\r\n        this._useKernelBlur = value;\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _depthScale: number;\r\n    /**\r\n     * Gets the depth scale used in ESM mode.\r\n     */\r\n    public get depthScale(): number {\r\n        return this._depthScale !== undefined ? this._depthScale : this._light.getDepthScale();\r\n    }\r\n    /**\r\n     * Sets the depth scale used in ESM mode.\r\n     * This can override the scale stored on the light.\r\n     */\r\n    public set depthScale(value: number) {\r\n        this._depthScale = value;\r\n    }\r\n\r\n    protected _validateFilter(filter: number): number {\r\n        return filter;\r\n    }\r\n\r\n    protected _filter = ShadowGenerator.FILTER_NONE;\r\n    /**\r\n     * Gets the current mode of the shadow generator (normal, PCF, ESM...).\r\n     * The returned value is a number equal to one of the available mode defined in ShadowMap.FILTER_x like _FILTER_NONE\r\n     */\r\n    public get filter(): number {\r\n        return this._filter;\r\n    }\r\n    /**\r\n     * Sets the current mode of the shadow generator (normal, PCF, ESM...).\r\n     * The returned value is a number equal to one of the available mode defined in ShadowMap.FILTER_x like _FILTER_NONE\r\n     */\r\n    public set filter(value: number) {\r\n        value = this._validateFilter(value);\r\n\r\n        // Blurring the cubemap is going to be too expensive. Reverting to unblurred version\r\n        if (this._light.needCube()) {\r\n            if (value === ShadowGenerator.FILTER_BLUREXPONENTIALSHADOWMAP) {\r\n                this.useExponentialShadowMap = true;\r\n                return;\r\n            } else if (value === ShadowGenerator.FILTER_BLURCLOSEEXPONENTIALSHADOWMAP) {\r\n                this.useCloseExponentialShadowMap = true;\r\n                return;\r\n            }\r\n            // PCF on cubemap would also be expensive\r\n            else if (value === ShadowGenerator.FILTER_PCF || value === ShadowGenerator.FILTER_PCSS) {\r\n                this.usePoissonSampling = true;\r\n                return;\r\n            }\r\n        }\r\n\r\n        // Weblg1 fallback for PCF.\r\n        if (value === ShadowGenerator.FILTER_PCF || value === ShadowGenerator.FILTER_PCSS) {\r\n            if (!this._scene.getEngine()._features.supportShadowSamplers) {\r\n                this.usePoissonSampling = true;\r\n                return;\r\n            }\r\n        }\r\n\r\n        if (this._filter === value) {\r\n            return;\r\n        }\r\n\r\n        this._filter = value;\r\n        this._disposeBlurPostProcesses();\r\n        this._applyFilterValues();\r\n        this._light._markMeshesAsLightDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to Poisson Sampling.\r\n     */\r\n    public get usePoissonSampling(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_POISSONSAMPLING;\r\n    }\r\n    /**\r\n     * Sets the current filter to Poisson Sampling.\r\n     */\r\n    public set usePoissonSampling(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_POISSONSAMPLING);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_POISSONSAMPLING) {\r\n            return;\r\n        }\r\n\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to ESM.\r\n     */\r\n    public get useExponentialShadowMap(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_EXPONENTIALSHADOWMAP;\r\n    }\r\n    /**\r\n     * Sets the current filter is to ESM.\r\n     */\r\n    public set useExponentialShadowMap(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_EXPONENTIALSHADOWMAP);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_EXPONENTIALSHADOWMAP) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to filtered ESM.\r\n     */\r\n    public get useBlurExponentialShadowMap(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_BLUREXPONENTIALSHADOWMAP;\r\n    }\r\n    /**\r\n     * Gets if the current filter is set to filtered  ESM.\r\n     */\r\n    public set useBlurExponentialShadowMap(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_BLUREXPONENTIALSHADOWMAP);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_BLUREXPONENTIALSHADOWMAP) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to \"close ESM\" (using the inverse of the\r\n     * exponential to prevent steep falloff artifacts).\r\n     */\r\n    public get useCloseExponentialShadowMap(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_CLOSEEXPONENTIALSHADOWMAP;\r\n    }\r\n    /**\r\n     * Sets the current filter to \"close ESM\" (using the inverse of the\r\n     * exponential to prevent steep falloff artifacts).\r\n     */\r\n    public set useCloseExponentialShadowMap(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_CLOSEEXPONENTIALSHADOWMAP);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_CLOSEEXPONENTIALSHADOWMAP) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to filtered \"close ESM\" (using the inverse of the\r\n     * exponential to prevent steep falloff artifacts).\r\n     */\r\n    public get useBlurCloseExponentialShadowMap(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_BLURCLOSEEXPONENTIALSHADOWMAP;\r\n    }\r\n    /**\r\n     * Sets the current filter to filtered \"close ESM\" (using the inverse of the\r\n     * exponential to prevent steep falloff artifacts).\r\n     */\r\n    public set useBlurCloseExponentialShadowMap(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_BLURCLOSEEXPONENTIALSHADOWMAP);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_BLURCLOSEEXPONENTIALSHADOWMAP) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to \"PCF\" (percentage closer filtering).\r\n     */\r\n    public get usePercentageCloserFiltering(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_PCF;\r\n    }\r\n    /**\r\n     * Sets the current filter to \"PCF\" (percentage closer filtering).\r\n     */\r\n    public set usePercentageCloserFiltering(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_PCF);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_PCF) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    protected _filteringQuality = ShadowGenerator.QUALITY_HIGH;\r\n    /**\r\n     * Gets the PCF or PCSS Quality.\r\n     * Only valid if usePercentageCloserFiltering or usePercentageCloserFiltering is true.\r\n     */\r\n    public get filteringQuality(): number {\r\n        return this._filteringQuality;\r\n    }\r\n    /**\r\n     * Sets the PCF or PCSS Quality.\r\n     * Only valid if usePercentageCloserFiltering or usePercentageCloserFiltering is true.\r\n     */\r\n    public set filteringQuality(filteringQuality: number) {\r\n        if (this._filteringQuality === filteringQuality) {\r\n            return;\r\n        }\r\n\r\n        this._filteringQuality = filteringQuality;\r\n\r\n        this._disposeBlurPostProcesses();\r\n        this._applyFilterValues();\r\n        this._light._markMeshesAsLightDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets if the current filter is set to \"PCSS\" (contact hardening).\r\n     */\r\n    public get useContactHardeningShadow(): boolean {\r\n        return this.filter === ShadowGenerator.FILTER_PCSS;\r\n    }\r\n    /**\r\n     * Sets the current filter to \"PCSS\" (contact hardening).\r\n     */\r\n    public set useContactHardeningShadow(value: boolean) {\r\n        const filter = this._validateFilter(ShadowGenerator.FILTER_PCSS);\r\n\r\n        if (!value && this.filter !== ShadowGenerator.FILTER_PCSS) {\r\n            return;\r\n        }\r\n        this.filter = value ? filter : ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    protected _contactHardeningLightSizeUVRatio = 0.1;\r\n    /**\r\n     * Gets the Light Size (in shadow map uv unit) used in PCSS to determine the blocker search area and the penumbra size.\r\n     * Using a ratio helps keeping shape stability independently of the map size.\r\n     *\r\n     * It does not account for the light projection as it was having too much\r\n     * instability during the light setup or during light position changes.\r\n     *\r\n     * Only valid if useContactHardeningShadow is true.\r\n     */\r\n    public get contactHardeningLightSizeUVRatio(): number {\r\n        return this._contactHardeningLightSizeUVRatio;\r\n    }\r\n    /**\r\n     * Sets the Light Size (in shadow map uv unit) used in PCSS to determine the blocker search area and the penumbra size.\r\n     * Using a ratio helps keeping shape stability independently of the map size.\r\n     *\r\n     * It does not account for the light projection as it was having too much\r\n     * instability during the light setup or during light position changes.\r\n     *\r\n     * Only valid if useContactHardeningShadow is true.\r\n     */\r\n    public set contactHardeningLightSizeUVRatio(contactHardeningLightSizeUVRatio: number) {\r\n        this._contactHardeningLightSizeUVRatio = contactHardeningLightSizeUVRatio;\r\n    }\r\n\r\n    protected _darkness = 0;\r\n\r\n    /** Gets or sets the actual darkness of a shadow */\r\n    public get darkness() {\r\n        return this._darkness;\r\n    }\r\n\r\n    public set darkness(value: number) {\r\n        this.setDarkness(value);\r\n    }\r\n\r\n    /**\r\n     * Returns the darkness value (float). This can only decrease the actual darkness of a shadow.\r\n     * 0 means strongest and 1 would means no shadow.\r\n     * @returns the darkness.\r\n     */\r\n    public getDarkness(): number {\r\n        return this._darkness;\r\n    }\r\n    /**\r\n     * Sets the darkness value (float). This can only decrease the actual darkness of a shadow.\r\n     * @param darkness The darkness value 0 means strongest and 1 would means no shadow.\r\n     * @returns the shadow generator allowing fluent coding.\r\n     */\r\n    public setDarkness(darkness: number): ShadowGenerator {\r\n        if (darkness >= 1.0) {\r\n            this._darkness = 1.0;\r\n        } else if (darkness <= 0.0) {\r\n            this._darkness = 0.0;\r\n        } else {\r\n            this._darkness = darkness;\r\n        }\r\n        return this;\r\n    }\r\n\r\n    protected _transparencyShadow = false;\r\n\r\n    /** Gets or sets the ability to have transparent shadow */\r\n    public get transparencyShadow() {\r\n        return this._transparencyShadow;\r\n    }\r\n\r\n    public set transparencyShadow(value: boolean) {\r\n        this.setTransparencyShadow(value);\r\n    }\r\n\r\n    /**\r\n     * Sets the ability to have transparent shadow (boolean).\r\n     * @param transparent True if transparent else False\r\n     * @returns the shadow generator allowing fluent coding\r\n     */\r\n    public setTransparencyShadow(transparent: boolean): ShadowGenerator {\r\n        this._transparencyShadow = transparent;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Enables or disables shadows with varying strength based on the transparency\r\n     * When it is enabled, the strength of the shadow is taken equal to mesh.visibility\r\n     * If you enabled an alpha texture on your material, the alpha value red from the texture is also combined to compute the strength:\r\n     *          mesh.visibility * alphaTexture.a\r\n     * The texture used is the diffuse by default, but it can be set to the opacity by setting useOpacityTextureForTransparentShadow\r\n     * Note that by definition transparencyShadow must be set to true for enableSoftTransparentShadow to work!\r\n     */\r\n    public enableSoftTransparentShadow: boolean = false;\r\n\r\n    /**\r\n     * If this is true, use the opacity texture's alpha channel for transparent shadows instead of the diffuse one\r\n     */\r\n    public useOpacityTextureForTransparentShadow: boolean = false;\r\n\r\n    protected _shadowMap: Nullable<RenderTargetTexture>;\r\n    protected _shadowMap2: Nullable<RenderTargetTexture>;\r\n\r\n    /**\r\n     * Gets the main RTT containing the shadow map (usually storing depth from the light point of view).\r\n     * @returns The render target texture if present otherwise, null\r\n     */\r\n    public getShadowMap(): Nullable<RenderTargetTexture> {\r\n        return this._shadowMap;\r\n    }\r\n\r\n    /**\r\n     * Gets the RTT used during rendering (can be a blurred version of the shadow map or the shadow map itself).\r\n     * @returns The render target texture if the shadow map is present otherwise, null\r\n     */\r\n    public getShadowMapForRendering(): Nullable<RenderTargetTexture> {\r\n        if (this._shadowMap2) {\r\n            return this._shadowMap2;\r\n        }\r\n\r\n        return this._shadowMap;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of that object\r\n     * @returns \"ShadowGenerator\"\r\n     */\r\n    public getClassName(): string {\r\n        return ShadowGenerator.CLASSNAME;\r\n    }\r\n\r\n    /**\r\n     * Helper function to add a mesh and its descendants to the list of shadow casters.\r\n     * @param mesh Mesh to add\r\n     * @param includeDescendants boolean indicating if the descendants should be added. Default to true\r\n     * @returns the Shadow Generator itself\r\n     */\r\n    public addShadowCaster(mesh: AbstractMesh, includeDescendants = true): ShadowGenerator {\r\n        if (!this._shadowMap) {\r\n            return this;\r\n        }\r\n\r\n        if (!this._shadowMap.renderList) {\r\n            this._shadowMap.renderList = [];\r\n        }\r\n\r\n        if (this._shadowMap.renderList.indexOf(mesh) === -1) {\r\n            this._shadowMap.renderList.push(mesh);\r\n        }\r\n\r\n        if (includeDescendants) {\r\n            for (const childMesh of mesh.getChildMeshes()) {\r\n                if (this._shadowMap.renderList.indexOf(childMesh) === -1) {\r\n                    this._shadowMap.renderList.push(childMesh);\r\n                }\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Helper function to remove a mesh and its descendants from the list of shadow casters\r\n     * @param mesh Mesh to remove\r\n     * @param includeDescendants boolean indicating if the descendants should be removed. Default to true\r\n     * @returns the Shadow Generator itself\r\n     */\r\n    public removeShadowCaster(mesh: AbstractMesh, includeDescendants = true): ShadowGenerator {\r\n        if (!this._shadowMap || !this._shadowMap.renderList) {\r\n            return this;\r\n        }\r\n\r\n        const index = this._shadowMap.renderList.indexOf(mesh);\r\n\r\n        if (index !== -1) {\r\n            this._shadowMap.renderList.splice(index, 1);\r\n        }\r\n\r\n        if (includeDescendants) {\r\n            for (const child of mesh.getChildren()) {\r\n                this.removeShadowCaster(<any>child);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Controls the extent to which the shadows fade out at the edge of the frustum\r\n     */\r\n    public frustumEdgeFalloff = 0;\r\n\r\n    protected _light: IShadowLight;\r\n    /**\r\n     * Returns the associated light object.\r\n     * @returns the light generating the shadow\r\n     */\r\n    public getLight(): IShadowLight {\r\n        return this._light;\r\n    }\r\n\r\n    /** Shader language used by the generator */\r\n    protected _shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    /**\r\n     * Gets the shader language used in this generator.\r\n     */\r\n    public get shaderLanguage(): ShaderLanguage {\r\n        return this._shaderLanguage;\r\n    }\r\n\r\n    /**\r\n     * If true the shadow map is generated by rendering the back face of the mesh instead of the front face.\r\n     * This can help with self-shadowing as the geometry making up the back of objects is slightly offset.\r\n     * It might on the other hand introduce peter panning.\r\n     */\r\n    public forceBackFacesOnly = false;\r\n\r\n    protected _camera: Nullable<Camera>;\r\n\r\n    protected _getCamera() {\r\n        return this._camera ?? this._scene.activeCamera;\r\n    }\r\n\r\n    protected _scene: Scene;\r\n    protected _useRedTextureType: boolean;\r\n    protected _lightDirection = Vector3.Zero();\r\n\r\n    protected _viewMatrix = Matrix.Zero();\r\n    protected _projectionMatrix = Matrix.Zero();\r\n    protected _transformMatrix = Matrix.Zero();\r\n    protected _cachedPosition: Vector3 = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n    protected _cachedDirection: Vector3 = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n    protected _cachedDefines: string;\r\n    protected _currentRenderId: number;\r\n    protected _boxBlurPostprocess: Nullable<PostProcess>;\r\n    protected _kernelBlurXPostprocess: Nullable<PostProcess>;\r\n    protected _kernelBlurYPostprocess: Nullable<PostProcess>;\r\n    protected _blurPostProcesses: PostProcess[];\r\n    protected _mapSize: number;\r\n    protected _currentFaceIndex = 0;\r\n    protected _currentFaceIndexCache = 0;\r\n    protected _textureType: number;\r\n    protected _defaultTextureMatrix = Matrix.Identity();\r\n    protected _storedUniqueId: Nullable<number>;\r\n    protected _useUBO: boolean;\r\n    protected _sceneUBOs: UniformBuffer[];\r\n    protected _currentSceneUBO: UniformBuffer;\r\n    protected _opacityTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _SceneComponentInitialization: (scene: Scene) => void = (_) => {\r\n        throw _WarnImport(\"ShadowGeneratorSceneComponent\");\r\n    };\r\n\r\n    /**\r\n     * Gets or sets the size of the texture what stores the shadows\r\n     */\r\n    public get mapSize(): number {\r\n        return this._mapSize;\r\n    }\r\n\r\n    public set mapSize(size: number) {\r\n        this._mapSize = size;\r\n        this._light._markMeshesAsLightDirty();\r\n        this.recreateShadowMap();\r\n    }\r\n\r\n    /**\r\n     * Creates a ShadowGenerator object.\r\n     * A ShadowGenerator is the required tool to use the shadows.\r\n     * Each light casting shadows needs to use its own ShadowGenerator.\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/shadows\r\n     * @param mapSize The size of the texture what stores the shadows. Example : 1024.\r\n     * @param light The light object generating the shadows.\r\n     * @param usefullFloatFirst By default the generator will try to use half float textures but if you need precision (for self shadowing for instance), you can use this option to enforce full float texture.\r\n     * @param camera Camera associated with this shadow generator (default: null). If null, takes the scene active camera at the time we need to access it\r\n     * @param useRedTextureType Forces the generator to use a Red instead of a RGBA type for the shadow map texture format (default: false)\r\n     * @param forceGLSL defines a boolean indicating if the shader must be compiled in GLSL even if we are using WebGPU\r\n     */\r\n    constructor(mapSize: number, light: IShadowLight, usefullFloatFirst?: boolean, camera?: Nullable<Camera>, useRedTextureType?: boolean, forceGLSL = false) {\r\n        this._mapSize = mapSize;\r\n        this._light = light;\r\n        this._scene = light.getScene();\r\n        this._camera = camera ?? null;\r\n        this._useRedTextureType = !!useRedTextureType;\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync(forceGLSL);\r\n\r\n        let shadowGenerators = light._shadowGenerators;\r\n        if (!shadowGenerators) {\r\n            shadowGenerators = light._shadowGenerators = new Map();\r\n        }\r\n        shadowGenerators.set(this._camera, this);\r\n        this.id = light.id;\r\n        this._useUBO = this._scene.getEngine().supportsUniformBuffers;\r\n\r\n        if (this._useUBO) {\r\n            this._sceneUBOs = [];\r\n            this._sceneUBOs.push(this._scene.createSceneUniformBuffer(`Scene for Shadow Generator (light \"${this._light.name}\")`));\r\n        }\r\n\r\n        ShadowGenerator._SceneComponentInitialization(this._scene);\r\n\r\n        // Texture type fallback from float to int if not supported.\r\n        const caps = this._scene.getEngine().getCaps();\r\n\r\n        if (!usefullFloatFirst) {\r\n            if (caps.textureHalfFloatRender && caps.textureHalfFloatLinearFiltering) {\r\n                this._textureType = Constants.TEXTURETYPE_HALF_FLOAT;\r\n            } else if (caps.textureFloatRender && caps.textureFloatLinearFiltering) {\r\n                this._textureType = Constants.TEXTURETYPE_FLOAT;\r\n            } else {\r\n                this._textureType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n            }\r\n        } else {\r\n            if (caps.textureFloatRender && caps.textureFloatLinearFiltering) {\r\n                this._textureType = Constants.TEXTURETYPE_FLOAT;\r\n            } else if (caps.textureHalfFloatRender && caps.textureHalfFloatLinearFiltering) {\r\n                this._textureType = Constants.TEXTURETYPE_HALF_FLOAT;\r\n            } else {\r\n                this._textureType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n            }\r\n        }\r\n\r\n        this._initializeGenerator();\r\n        this._applyFilterValues();\r\n    }\r\n\r\n    protected _initializeGenerator(): void {\r\n        this._light._markMeshesAsLightDirty();\r\n        this._initializeShadowMap();\r\n    }\r\n\r\n    protected _createTargetRenderTexture(): void {\r\n        const engine = this._scene.getEngine();\r\n        if (engine._features.supportDepthStencilTexture) {\r\n            this._shadowMap = new RenderTargetTexture(\r\n                this._light.name + \"_shadowMap\",\r\n                this._mapSize,\r\n                this._scene,\r\n                false,\r\n                true,\r\n                this._textureType,\r\n                this._light.needCube(),\r\n                undefined,\r\n                false,\r\n                false,\r\n                undefined,\r\n                this._useRedTextureType ? Constants.TEXTUREFORMAT_RED : Constants.TEXTUREFORMAT_RGBA\r\n            );\r\n            this._shadowMap.createDepthStencilTexture(\r\n                engine.useReverseDepthBuffer ? Constants.GREATER : Constants.LESS,\r\n                true,\r\n                undefined,\r\n                undefined,\r\n                undefined,\r\n                `DepthStencilForShadowGenerator-${this._light.name}`\r\n            );\r\n        } else {\r\n            this._shadowMap = new RenderTargetTexture(this._light.name + \"_shadowMap\", this._mapSize, this._scene, false, true, this._textureType, this._light.needCube());\r\n        }\r\n        this._shadowMap.noPrePassRenderer = true;\r\n    }\r\n\r\n    protected _initializeShadowMap(): void {\r\n        this._createTargetRenderTexture();\r\n\r\n        if (this._shadowMap === null) {\r\n            return;\r\n        }\r\n\r\n        this._shadowMap.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this._shadowMap.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        this._shadowMap.anisotropicFilteringLevel = 1;\r\n        this._shadowMap.updateSamplingMode(Texture.BILINEAR_SAMPLINGMODE);\r\n        this._shadowMap.renderParticles = false;\r\n        this._shadowMap.ignoreCameraViewport = true;\r\n        if (this._storedUniqueId) {\r\n            this._shadowMap.uniqueId = this._storedUniqueId;\r\n        }\r\n\r\n        // Custom render function.\r\n        this._shadowMap.customRenderFunction = (\r\n            opaqueSubMeshes: SmartArray<SubMesh>,\r\n            alphaTestSubMeshes: SmartArray<SubMesh>,\r\n            transparentSubMeshes: SmartArray<SubMesh>,\r\n            depthOnlySubMeshes: SmartArray<SubMesh>\r\n        ) => this._renderForShadowMap(opaqueSubMeshes, alphaTestSubMeshes, transparentSubMeshes, depthOnlySubMeshes);\r\n\r\n        // When preWarm is false, forces the mesh is ready function to true as we are double checking it\r\n        // in the custom render function. Also it prevents side effects and useless\r\n        // shader variations in DEPTHPREPASS mode.\r\n        this._shadowMap.customIsReadyFunction = (mesh: AbstractMesh, _refreshRate: number, preWarm?: boolean): boolean => {\r\n            if (!preWarm || !mesh.subMeshes) {\r\n                return true;\r\n            }\r\n\r\n            let isReady = true;\r\n            for (const subMesh of mesh.subMeshes) {\r\n                const renderingMesh = subMesh.getRenderingMesh();\r\n                const scene = this._scene;\r\n                const engine = scene.getEngine();\r\n                const material = subMesh.getMaterial();\r\n\r\n                if (!material || subMesh.verticesCount === 0 || (this.customAllowRendering && !this.customAllowRendering(subMesh))) {\r\n                    continue;\r\n                }\r\n\r\n                const batch = renderingMesh._getInstancesRenderList(subMesh._id, !!subMesh.getReplacementMesh());\r\n                if (batch.mustReturn) {\r\n                    continue;\r\n                }\r\n\r\n                const hardwareInstancedRendering =\r\n                    engine.getCaps().instancedArrays &&\r\n                    ((batch.visibleInstances[subMesh._id] !== null && batch.visibleInstances[subMesh._id] !== undefined) || renderingMesh.hasThinInstances);\r\n\r\n                const isTransparent = material.needAlphaBlendingForMesh(renderingMesh);\r\n\r\n                isReady = this.isReady(subMesh, hardwareInstancedRendering, isTransparent) && isReady;\r\n            }\r\n\r\n            return isReady;\r\n        };\r\n\r\n        const engine = this._scene.getEngine();\r\n\r\n        this._shadowMap.onBeforeBindObservable.add(() => {\r\n            this._currentSceneUBO = this._scene.getSceneUniformBuffer();\r\n            engine._debugPushGroup?.(`shadow map generation for pass id ${engine.currentRenderPassId}`, 1);\r\n        });\r\n\r\n        // Record Face Index before render.\r\n        this._shadowMap.onBeforeRenderObservable.add((faceIndex: number) => {\r\n            if (this._sceneUBOs) {\r\n                this._scene.setSceneUniformBuffer(this._sceneUBOs[0]);\r\n            }\r\n            this._currentFaceIndex = faceIndex;\r\n            if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n                engine.setColorWrite(false);\r\n            }\r\n            this.getTransformMatrix(); // generate the view/projection matrix\r\n            this._scene.setTransformMatrix(this._viewMatrix, this._projectionMatrix);\r\n            if (this._useUBO) {\r\n                this._scene.getSceneUniformBuffer().unbindEffect();\r\n                this._scene.finalizeSceneUbo();\r\n            }\r\n        });\r\n\r\n        // Blur if required after render.\r\n        this._shadowMap.onAfterUnbindObservable.add(() => {\r\n            if (this._sceneUBOs) {\r\n                this._scene.setSceneUniformBuffer(this._currentSceneUBO);\r\n            }\r\n            this._scene.updateTransformMatrix(); // restore the view/projection matrices of the active camera\r\n\r\n            if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n                engine.setColorWrite(true);\r\n            }\r\n            if (!this.useBlurExponentialShadowMap && !this.useBlurCloseExponentialShadowMap) {\r\n                engine._debugPopGroup?.(1);\r\n                return;\r\n            }\r\n            const shadowMap = this.getShadowMapForRendering();\r\n\r\n            if (shadowMap) {\r\n                this._scene.postProcessManager.directRender(this._blurPostProcesses, shadowMap.renderTarget, true);\r\n                engine.unBindFramebuffer(shadowMap.renderTarget!, true);\r\n            }\r\n\r\n            engine._debugPopGroup?.(1);\r\n        });\r\n\r\n        // Clear according to the chosen filter.\r\n        const clearZero = new Color4(0, 0, 0, 0);\r\n        const clearOne = new Color4(1.0, 1.0, 1.0, 1.0);\r\n        this._shadowMap.onClearObservable.add((engine) => {\r\n            if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n                engine.clear(clearOne, false, true, false);\r\n            } else if (this.useExponentialShadowMap || this.useBlurExponentialShadowMap) {\r\n                engine.clear(clearZero, true, true, false);\r\n            } else {\r\n                engine.clear(clearOne, true, true, false);\r\n            }\r\n        });\r\n\r\n        // Recreate on resize.\r\n        this._shadowMap.onResizeObservable.add((rtt) => {\r\n            this._storedUniqueId = this._shadowMap!.uniqueId;\r\n            this._mapSize = rtt.getRenderSize();\r\n            this._light._markMeshesAsLightDirty();\r\n            this.recreateShadowMap();\r\n        });\r\n\r\n        // Ensures rendering groupids do not erase the depth buffer\r\n        // or we would lose the shadows information.\r\n        for (let i = RenderingManager.MIN_RENDERINGGROUPS; i < RenderingManager.MAX_RENDERINGGROUPS; i++) {\r\n            this._shadowMap.setRenderingAutoClearDepthStencil(i, false);\r\n        }\r\n    }\r\n\r\n    private _shadersLoaded = false;\r\n    private async _initShaderSourceAsync(forceGLSL = false) {\r\n        const engine = this._scene.getEngine();\r\n\r\n        if (engine.isWebGPU && !forceGLSL && !ShadowGenerator.ForceGLSL) {\r\n            this._shaderLanguage = ShaderLanguage.WGSL;\r\n\r\n            await Promise.all([\r\n                import(\"../../ShadersWGSL/shadowMap.fragment\"),\r\n                import(\"../../ShadersWGSL/shadowMap.vertex\"),\r\n                import(\"../../ShadersWGSL/depthBoxBlur.fragment\"),\r\n                import(\"../../ShadersWGSL/ShadersInclude/shadowMapFragmentSoftTransparentShadow\"),\r\n            ]);\r\n        } else {\r\n            await Promise.all([\r\n                import(\"../../Shaders/shadowMap.fragment\"),\r\n                import(\"../../Shaders/shadowMap.vertex\"),\r\n                import(\"../../Shaders/depthBoxBlur.fragment\"),\r\n                import(\"../../Shaders/ShadersInclude/shadowMapFragmentSoftTransparentShadow\"),\r\n            ]);\r\n        }\r\n\r\n        this._shadersLoaded = true;\r\n    }\r\n\r\n    protected _initializeBlurRTTAndPostProcesses(): void {\r\n        const engine = this._scene.getEngine();\r\n        const targetSize = this._mapSize / this.blurScale;\r\n\r\n        if (!this.useKernelBlur || this.blurScale !== 1.0) {\r\n            this._shadowMap2 = new RenderTargetTexture(this._light.name + \"_shadowMap2\", targetSize, this._scene, false, true, this._textureType, undefined, undefined, false);\r\n            this._shadowMap2.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n            this._shadowMap2.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n            this._shadowMap2.updateSamplingMode(Texture.BILINEAR_SAMPLINGMODE);\r\n        }\r\n\r\n        if (this.useKernelBlur) {\r\n            this._kernelBlurXPostprocess = new BlurPostProcess(\r\n                this._light.name + \"KernelBlurX\",\r\n                new Vector2(1, 0),\r\n                this.blurKernel,\r\n                1.0,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                engine,\r\n                false,\r\n                this._textureType\r\n            );\r\n            this._kernelBlurXPostprocess.width = targetSize;\r\n            this._kernelBlurXPostprocess.height = targetSize;\r\n            this._kernelBlurXPostprocess.externalTextureSamplerBinding = true;\r\n            this._kernelBlurXPostprocess.onApplyObservable.add((effect) => {\r\n                effect.setTexture(\"textureSampler\", this._shadowMap);\r\n            });\r\n\r\n            this._kernelBlurYPostprocess = new BlurPostProcess(\r\n                this._light.name + \"KernelBlurY\",\r\n                new Vector2(0, 1),\r\n                this.blurKernel,\r\n                1.0,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                engine,\r\n                false,\r\n                this._textureType\r\n            );\r\n\r\n            this._kernelBlurXPostprocess.autoClear = false;\r\n            this._kernelBlurYPostprocess.autoClear = false;\r\n\r\n            if (this._textureType === Constants.TEXTURETYPE_UNSIGNED_BYTE) {\r\n                (<BlurPostProcess>this._kernelBlurXPostprocess).packedFloat = true;\r\n                (<BlurPostProcess>this._kernelBlurYPostprocess).packedFloat = true;\r\n            }\r\n\r\n            this._blurPostProcesses = [this._kernelBlurXPostprocess, this._kernelBlurYPostprocess];\r\n        } else {\r\n            this._boxBlurPostprocess = new PostProcess(\r\n                this._light.name + \"DepthBoxBlur\",\r\n                \"depthBoxBlur\",\r\n                [\"screenSize\", \"boxOffset\"],\r\n                [],\r\n                1.0,\r\n                null,\r\n                Texture.BILINEAR_SAMPLINGMODE,\r\n                engine,\r\n                false,\r\n                \"#define OFFSET \" + this._blurBoxOffset,\r\n                this._textureType,\r\n                undefined,\r\n                undefined,\r\n                undefined,\r\n                undefined,\r\n                this._shaderLanguage\r\n            );\r\n            this._boxBlurPostprocess.externalTextureSamplerBinding = true;\r\n            this._boxBlurPostprocess.onApplyObservable.add((effect) => {\r\n                effect.setFloat2(\"screenSize\", targetSize, targetSize);\r\n                effect.setTexture(\"textureSampler\", this._shadowMap);\r\n            });\r\n\r\n            this._boxBlurPostprocess.autoClear = false;\r\n\r\n            this._blurPostProcesses = [this._boxBlurPostprocess];\r\n        }\r\n    }\r\n\r\n    protected _renderForShadowMap(\r\n        opaqueSubMeshes: SmartArray<SubMesh>,\r\n        alphaTestSubMeshes: SmartArray<SubMesh>,\r\n        transparentSubMeshes: SmartArray<SubMesh>,\r\n        depthOnlySubMeshes: SmartArray<SubMesh>\r\n    ): void {\r\n        let index: number;\r\n\r\n        if (depthOnlySubMeshes.length) {\r\n            for (index = 0; index < depthOnlySubMeshes.length; index++) {\r\n                this._renderSubMeshForShadowMap(depthOnlySubMeshes.data[index]);\r\n            }\r\n        }\r\n\r\n        for (index = 0; index < opaqueSubMeshes.length; index++) {\r\n            this._renderSubMeshForShadowMap(opaqueSubMeshes.data[index]);\r\n        }\r\n\r\n        for (index = 0; index < alphaTestSubMeshes.length; index++) {\r\n            this._renderSubMeshForShadowMap(alphaTestSubMeshes.data[index]);\r\n        }\r\n\r\n        if (this._transparencyShadow) {\r\n            for (index = 0; index < transparentSubMeshes.length; index++) {\r\n                this._renderSubMeshForShadowMap(transparentSubMeshes.data[index], true);\r\n            }\r\n        } else {\r\n            for (index = 0; index < transparentSubMeshes.length; index++) {\r\n                transparentSubMeshes.data[index].getEffectiveMesh()._internalAbstractMeshDataInfo._isActiveIntermediate = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _bindCustomEffectForRenderSubMeshForShadowMap(subMesh: SubMesh, effect: Effect, mesh: AbstractMesh): void {\r\n        effect.setMatrix(\"viewProjection\", this.getTransformMatrix());\r\n    }\r\n\r\n    protected _renderSubMeshForShadowMap(subMesh: SubMesh, isTransparent: boolean = false): void {\r\n        const renderingMesh = subMesh.getRenderingMesh();\r\n        const effectiveMesh = subMesh.getEffectiveMesh();\r\n        const scene = this._scene;\r\n        const engine = scene.getEngine();\r\n        const material = subMesh.getMaterial();\r\n\r\n        effectiveMesh._internalAbstractMeshDataInfo._isActiveIntermediate = false;\r\n\r\n        if (!material || subMesh.verticesCount === 0 || subMesh._renderId === scene.getRenderId()) {\r\n            return;\r\n        }\r\n\r\n        // Culling\r\n        // Note:\r\n        // In rhs mode, we assume that meshes will be rendered in right-handed space (i.e. with an RHS camera), so the default value of material.sideOrientation is updated accordingly (see material constructor).\r\n        // However, when generating a shadow map, we render from the point of view of the light, whose view/projection matrices are always in lhs mode.\r\n        // We therefore need to \"undo\" the sideOrientation inversion that was previously performed when constructing the material.\r\n        const useRHS = scene.useRightHandedSystem;\r\n        const detNeg = effectiveMesh._getWorldMatrixDeterminant() < 0;\r\n        let sideOrientation = material._getEffectiveOrientation(renderingMesh);\r\n\r\n        if ((detNeg && !useRHS) || (!detNeg && useRHS)) {\r\n            sideOrientation =\r\n                sideOrientation === Constants.MATERIAL_ClockWiseSideOrientation ? Constants.MATERIAL_CounterClockWiseSideOrientation : Constants.MATERIAL_ClockWiseSideOrientation;\r\n        }\r\n        const reverseSideOrientation = sideOrientation === Constants.MATERIAL_ClockWiseSideOrientation;\r\n\r\n        engine.setState(material.backFaceCulling, undefined, undefined, reverseSideOrientation, material.cullBackFaces);\r\n\r\n        // Managing instances\r\n        const batch = renderingMesh._getInstancesRenderList(subMesh._id, !!subMesh.getReplacementMesh());\r\n        if (batch.mustReturn) {\r\n            return;\r\n        }\r\n\r\n        const hardwareInstancedRendering =\r\n            engine.getCaps().instancedArrays &&\r\n            ((batch.visibleInstances[subMesh._id] !== null && batch.visibleInstances[subMesh._id] !== undefined) || renderingMesh.hasThinInstances);\r\n\r\n        if (this.customAllowRendering && !this.customAllowRendering(subMesh)) {\r\n            return;\r\n        }\r\n\r\n        if (this.isReady(subMesh, hardwareInstancedRendering, isTransparent)) {\r\n            subMesh._renderId = scene.getRenderId();\r\n\r\n            const shadowDepthWrapper = material.shadowDepthWrapper;\r\n\r\n            const drawWrapper = shadowDepthWrapper?.getEffect(subMesh, this, engine.currentRenderPassId) ?? subMesh._getDrawWrapper()!;\r\n            const effect = DrawWrapper.GetEffect(drawWrapper)!;\r\n\r\n            engine.enableEffect(drawWrapper);\r\n\r\n            if (!hardwareInstancedRendering) {\r\n                renderingMesh._bind(subMesh, effect, material.fillMode);\r\n            }\r\n\r\n            this.getTransformMatrix(); // make sure _cachedDirection et _cachedPosition are up to date\r\n\r\n            effect.setFloat3(\"biasAndScaleSM\", this.bias, this.normalBias, this.depthScale);\r\n\r\n            if (this.getLight().getTypeID() === Light.LIGHTTYPEID_DIRECTIONALLIGHT) {\r\n                effect.setVector3(\"lightDataSM\", this._cachedDirection);\r\n            } else {\r\n                effect.setVector3(\"lightDataSM\", this._cachedPosition);\r\n            }\r\n\r\n            const camera = this._getCamera();\r\n            effect.setFloat2(\"depthValuesSM\", this.getLight().getDepthMinZ(camera), this.getLight().getDepthMinZ(camera) + this.getLight().getDepthMaxZ(camera));\r\n\r\n            if (isTransparent && this.enableSoftTransparentShadow) {\r\n                effect.setFloat2(\"softTransparentShadowSM\", effectiveMesh.visibility * material.alpha, this._opacityTexture?.getAlphaFromRGB ? 1 : 0);\r\n            }\r\n\r\n            if (shadowDepthWrapper) {\r\n                subMesh._setMainDrawWrapperOverride(drawWrapper);\r\n                if (shadowDepthWrapper.standalone) {\r\n                    shadowDepthWrapper.baseMaterial.bindForSubMesh(effectiveMesh.getWorldMatrix(), renderingMesh, subMesh);\r\n                } else {\r\n                    material.bindForSubMesh(effectiveMesh.getWorldMatrix(), renderingMesh, subMesh);\r\n                }\r\n                subMesh._setMainDrawWrapperOverride(null);\r\n            } else {\r\n                // Alpha test\r\n                if (this._opacityTexture) {\r\n                    effect.setTexture(\"diffuseSampler\", this._opacityTexture);\r\n                    effect.setMatrix(\"diffuseMatrix\", this._opacityTexture.getTextureMatrix() || this._defaultTextureMatrix);\r\n                }\r\n\r\n                // Bones\r\n                if (renderingMesh.useBones && renderingMesh.computeBonesUsingShaders && renderingMesh.skeleton) {\r\n                    const skeleton = renderingMesh.skeleton;\r\n\r\n                    if (skeleton.isUsingTextureForMatrices) {\r\n                        const boneTexture = skeleton.getTransformMatrixTexture(renderingMesh);\r\n\r\n                        if (!boneTexture) {\r\n                            return;\r\n                        }\r\n\r\n                        effect.setTexture(\"boneSampler\", boneTexture);\r\n                        effect.setFloat(\"boneTextureWidth\", 4.0 * (skeleton.bones.length + 1));\r\n                    } else {\r\n                        effect.setMatrices(\"mBones\", skeleton.getTransformMatrices(renderingMesh));\r\n                    }\r\n                }\r\n\r\n                // Morph targets\r\n                BindMorphTargetParameters(renderingMesh, effect);\r\n                if (renderingMesh.morphTargetManager && renderingMesh.morphTargetManager.isUsingTextureForTargets) {\r\n                    renderingMesh.morphTargetManager._bind(effect);\r\n                }\r\n\r\n                // Baked vertex animations\r\n                const bvaManager = subMesh.getMesh().bakedVertexAnimationManager;\r\n                if (bvaManager && bvaManager.isEnabled) {\r\n                    bvaManager.bind(effect, hardwareInstancedRendering);\r\n                }\r\n\r\n                // Clip planes\r\n                BindClipPlane(effect, material, scene);\r\n            }\r\n\r\n            if (!this._useUBO && !shadowDepthWrapper) {\r\n                this._bindCustomEffectForRenderSubMeshForShadowMap(subMesh, effect, effectiveMesh);\r\n            }\r\n\r\n            BindSceneUniformBuffer(effect, this._scene.getSceneUniformBuffer());\r\n            this._scene.getSceneUniformBuffer().bindUniformBuffer();\r\n\r\n            const world = effectiveMesh.getWorldMatrix();\r\n\r\n            // In the non hardware instanced mode, the Mesh ubo update is done by the callback passed to renderingMesh._processRendering (see below)\r\n            if (hardwareInstancedRendering) {\r\n                effectiveMesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n                effectiveMesh.transferToEffect(world);\r\n            }\r\n\r\n            if (this.forceBackFacesOnly) {\r\n                engine.setState(true, 0, false, true, material.cullBackFaces);\r\n            }\r\n\r\n            // Observables\r\n            this.onBeforeShadowMapRenderMeshObservable.notifyObservers(renderingMesh);\r\n            this.onBeforeShadowMapRenderObservable.notifyObservers(effect);\r\n\r\n            // Draw\r\n            renderingMesh._processRendering(effectiveMesh, subMesh, effect, material.fillMode, batch, hardwareInstancedRendering, (isInstance, worldOverride) => {\r\n                if (effectiveMesh !== renderingMesh && !isInstance) {\r\n                    renderingMesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n                    renderingMesh.transferToEffect(worldOverride);\r\n                } else {\r\n                    effectiveMesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n                    effectiveMesh.transferToEffect(isInstance ? worldOverride : world);\r\n                }\r\n            });\r\n\r\n            if (this.forceBackFacesOnly) {\r\n                engine.setState(true, 0, false, false, material.cullBackFaces);\r\n            }\r\n\r\n            // Observables\r\n            this.onAfterShadowMapRenderObservable.notifyObservers(effect);\r\n            this.onAfterShadowMapRenderMeshObservable.notifyObservers(renderingMesh);\r\n        } else {\r\n            // Need to reset refresh rate of the shadowMap\r\n            if (this._shadowMap) {\r\n                this._shadowMap.resetRefreshCounter();\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _applyFilterValues(): void {\r\n        if (!this._shadowMap) {\r\n            return;\r\n        }\r\n\r\n        if (this.filter === ShadowGenerator.FILTER_NONE || this.filter === ShadowGenerator.FILTER_PCSS) {\r\n            this._shadowMap.updateSamplingMode(Texture.NEAREST_SAMPLINGMODE);\r\n        } else {\r\n            this._shadowMap.updateSamplingMode(Texture.BILINEAR_SAMPLINGMODE);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Forces all the attached effect to compile to enable rendering only once ready vs. lazily compiling effects.\r\n     * @param onCompiled Callback triggered at the and of the effects compilation\r\n     * @param options Sets of optional options forcing the compilation with different modes\r\n     */\r\n    public forceCompilation(onCompiled?: (generator: IShadowGenerator) => void, options?: Partial<{ useInstances: boolean }>): void {\r\n        const localOptions = {\r\n            useInstances: false,\r\n            ...options,\r\n        };\r\n\r\n        const shadowMap = this.getShadowMap();\r\n        if (!shadowMap) {\r\n            if (onCompiled) {\r\n                onCompiled(this);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const renderList = shadowMap.renderList;\r\n        if (!renderList) {\r\n            if (onCompiled) {\r\n                onCompiled(this);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const subMeshes: SubMesh[] = [];\r\n        for (const mesh of renderList) {\r\n            subMeshes.push(...mesh.subMeshes);\r\n        }\r\n        if (subMeshes.length === 0) {\r\n            if (onCompiled) {\r\n                onCompiled(this);\r\n            }\r\n            return;\r\n        }\r\n\r\n        let currentIndex = 0;\r\n\r\n        const checkReady = () => {\r\n            if (!this._scene || !this._scene.getEngine()) {\r\n                return;\r\n            }\r\n\r\n            while (\r\n                this.isReady(\r\n                    subMeshes[currentIndex],\r\n                    localOptions.useInstances,\r\n                    subMeshes[currentIndex].getMaterial()?.needAlphaBlendingForMesh(subMeshes[currentIndex].getMesh()) ?? false\r\n                )\r\n            ) {\r\n                currentIndex++;\r\n                if (currentIndex >= subMeshes.length) {\r\n                    if (onCompiled) {\r\n                        onCompiled(this);\r\n                    }\r\n                    return;\r\n                }\r\n            }\r\n            setTimeout(checkReady, 16);\r\n        };\r\n\r\n        checkReady();\r\n    }\r\n\r\n    /**\r\n     * Forces all the attached effect to compile to enable rendering only once ready vs. lazily compiling effects.\r\n     * @param options Sets of optional options forcing the compilation with different modes\r\n     * @returns A promise that resolves when the compilation completes\r\n     */\r\n    public async forceCompilationAsync(options?: Partial<{ useInstances: boolean }>): Promise<void> {\r\n        return await new Promise((resolve) => {\r\n            this.forceCompilation(() => {\r\n                resolve();\r\n            }, options);\r\n        });\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _isReadyCustomDefines(defines: any, subMesh: SubMesh, useInstances: boolean): void {}\r\n\r\n    private _prepareShadowDefines(subMesh: SubMesh, useInstances: boolean, defines: string[], isTransparent: boolean): string[] {\r\n        defines.push(\"#define SM_LIGHTTYPE_\" + this._light.getClassName().toUpperCase());\r\n\r\n        defines.push(\"#define SM_FLOAT \" + (this._textureType !== Constants.TEXTURETYPE_UNSIGNED_BYTE ? \"1\" : \"0\"));\r\n\r\n        defines.push(\"#define SM_ESM \" + (this.useExponentialShadowMap || this.useBlurExponentialShadowMap ? \"1\" : \"0\"));\r\n\r\n        defines.push(\"#define SM_DEPTHTEXTURE \" + (this.usePercentageCloserFiltering || this.useContactHardeningShadow ? \"1\" : \"0\"));\r\n\r\n        const mesh = subMesh.getMesh();\r\n\r\n        // Normal bias.\r\n        defines.push(\"#define SM_NORMALBIAS \" + (this.normalBias && mesh.isVerticesDataPresent(VertexBuffer.NormalKind) ? \"1\" : \"0\"));\r\n        defines.push(\"#define SM_DIRECTIONINLIGHTDATA \" + (this.getLight().getTypeID() === Light.LIGHTTYPEID_DIRECTIONALLIGHT ? \"1\" : \"0\"));\r\n\r\n        // Point light\r\n        defines.push(\"#define SM_USEDISTANCE \" + (this._light.needCube() ? \"1\" : \"0\"));\r\n\r\n        // Soft transparent shadows\r\n        defines.push(\"#define SM_SOFTTRANSPARENTSHADOW \" + (this.enableSoftTransparentShadow && isTransparent ? \"1\" : \"0\"));\r\n\r\n        this._isReadyCustomDefines(defines, subMesh, useInstances);\r\n\r\n        return defines;\r\n    }\r\n\r\n    /**\r\n     * Determine whether the shadow generator is ready or not (mainly all effects and related post processes needs to be ready).\r\n     * @param subMesh The submesh we want to render in the shadow map\r\n     * @param useInstances Defines whether will draw in the map using instances\r\n     * @param isTransparent Indicates that isReady is called for a transparent subMesh\r\n     * @returns true if ready otherwise, false\r\n     */\r\n    public isReady(subMesh: SubMesh, useInstances: boolean, isTransparent: boolean): boolean {\r\n        if (!this._shadersLoaded) {\r\n            return false;\r\n        }\r\n\r\n        const material = subMesh.getMaterial(),\r\n            shadowDepthWrapper = material?.shadowDepthWrapper;\r\n\r\n        this._opacityTexture = null;\r\n\r\n        if (!material) {\r\n            return false;\r\n        }\r\n\r\n        const defines: string[] = [];\r\n\r\n        this._prepareShadowDefines(subMesh, useInstances, defines, isTransparent);\r\n\r\n        if (shadowDepthWrapper) {\r\n            if (!shadowDepthWrapper.isReadyForSubMesh(subMesh, defines, this, useInstances, this._scene.getEngine().currentRenderPassId)) {\r\n                return false;\r\n            }\r\n        } else {\r\n            const subMeshEffect = subMesh._getDrawWrapper(undefined, true)!;\r\n\r\n            let effect = subMeshEffect.effect!;\r\n            let cachedDefines = subMeshEffect.defines;\r\n\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            const mesh = subMesh.getMesh();\r\n\r\n            let useNormal = false;\r\n            let uv1 = false;\r\n            let uv2 = false;\r\n            const color = false;\r\n\r\n            // Normal bias.\r\n            if (this.normalBias && mesh.isVerticesDataPresent(VertexBuffer.NormalKind)) {\r\n                attribs.push(VertexBuffer.NormalKind);\r\n                defines.push(\"#define NORMAL\");\r\n                useNormal = true;\r\n                if (mesh.nonUniformScaling) {\r\n                    defines.push(\"#define NONUNIFORMSCALING\");\r\n                }\r\n            }\r\n\r\n            // Alpha test\r\n            const needAlphaTesting = material.needAlphaTestingForMesh(mesh);\r\n\r\n            if (needAlphaTesting || material.needAlphaBlendingForMesh(mesh)) {\r\n                if (this.useOpacityTextureForTransparentShadow) {\r\n                    this._opacityTexture = (material as any).opacityTexture;\r\n                } else {\r\n                    this._opacityTexture = material.getAlphaTestTexture();\r\n                }\r\n                if (this._opacityTexture) {\r\n                    if (!this._opacityTexture.isReady()) {\r\n                        return false;\r\n                    }\r\n\r\n                    const alphaCutOff = (material as any).alphaCutOff ?? ShadowGenerator.DEFAULT_ALPHA_CUTOFF;\r\n\r\n                    defines.push(\"#define ALPHATEXTURE\");\r\n                    if (needAlphaTesting) {\r\n                        defines.push(`#define ALPHATESTVALUE ${alphaCutOff}${alphaCutOff % 1 === 0 ? \".\" : \"\"}`);\r\n                    }\r\n                    if (mesh.isVerticesDataPresent(VertexBuffer.UVKind)) {\r\n                        attribs.push(VertexBuffer.UVKind);\r\n                        defines.push(\"#define UV1\");\r\n                        uv1 = true;\r\n                    }\r\n                    if (mesh.isVerticesDataPresent(VertexBuffer.UV2Kind)) {\r\n                        if (this._opacityTexture.coordinatesIndex === 1) {\r\n                            attribs.push(VertexBuffer.UV2Kind);\r\n                            defines.push(\"#define UV2\");\r\n                            uv2 = true;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Bones\r\n            const fallbacks = new EffectFallbacks();\r\n            if (mesh.useBones && mesh.computeBonesUsingShaders && mesh.skeleton) {\r\n                attribs.push(VertexBuffer.MatricesIndicesKind);\r\n                attribs.push(VertexBuffer.MatricesWeightsKind);\r\n                if (mesh.numBoneInfluencers > 4) {\r\n                    attribs.push(VertexBuffer.MatricesIndicesExtraKind);\r\n                    attribs.push(VertexBuffer.MatricesWeightsExtraKind);\r\n                }\r\n                const skeleton = mesh.skeleton;\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n                if (mesh.numBoneInfluencers > 0) {\r\n                    fallbacks.addCPUSkinningFallback(0, mesh);\r\n                }\r\n\r\n                if (skeleton.isUsingTextureForMatrices) {\r\n                    defines.push(\"#define BONETEXTURE\");\r\n                } else {\r\n                    defines.push(\"#define BonesPerMesh \" + (skeleton.bones.length + 1));\r\n                }\r\n            } else {\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS 0\");\r\n            }\r\n\r\n            // Morph targets\r\n            const numMorphInfluencers = mesh.morphTargetManager\r\n                ? PrepareDefinesAndAttributesForMorphTargets(\r\n                      mesh.morphTargetManager,\r\n                      defines,\r\n                      attribs,\r\n                      mesh,\r\n                      true, // usePositionMorph\r\n                      useNormal, // useNormalMorph\r\n                      false, // useTangentMorph\r\n                      uv1, // useUVMorph\r\n                      uv2, // useUV2Morph\r\n                      color // useColorMorph\r\n                  )\r\n                : 0;\r\n\r\n            // ClipPlanes\r\n            PrepareStringDefinesForClipPlanes(material, this._scene, defines);\r\n\r\n            // Instances\r\n            if (useInstances) {\r\n                defines.push(\"#define INSTANCES\");\r\n                PushAttributesForInstances(attribs);\r\n                if (subMesh.getRenderingMesh().hasThinInstances) {\r\n                    defines.push(\"#define THIN_INSTANCES\");\r\n                }\r\n            }\r\n\r\n            if (this.customShaderOptions) {\r\n                if (this.customShaderOptions.defines) {\r\n                    for (const define of this.customShaderOptions.defines) {\r\n                        if (defines.indexOf(define) === -1) {\r\n                            defines.push(define);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Baked vertex animations\r\n            const bvaManager = mesh.bakedVertexAnimationManager;\r\n            if (bvaManager && bvaManager.isEnabled) {\r\n                defines.push(\"#define BAKED_VERTEX_ANIMATION_TEXTURE\");\r\n                if (useInstances) {\r\n                    attribs.push(\"bakedVertexAnimationSettingsInstanced\");\r\n                }\r\n            }\r\n\r\n            // Get correct effect\r\n            const join = defines.join(\"\\n\");\r\n            if (cachedDefines !== join) {\r\n                cachedDefines = join;\r\n\r\n                let shaderName = \"shadowMap\";\r\n                const uniforms = [\r\n                    \"world\",\r\n                    \"mBones\",\r\n                    \"viewProjection\",\r\n                    \"diffuseMatrix\",\r\n                    \"lightDataSM\",\r\n                    \"depthValuesSM\",\r\n                    \"biasAndScaleSM\",\r\n                    \"morphTargetInfluences\",\r\n                    \"morphTargetCount\",\r\n                    \"boneTextureWidth\",\r\n                    \"softTransparentShadowSM\",\r\n                    \"morphTargetTextureInfo\",\r\n                    \"morphTargetTextureIndices\",\r\n                    \"bakedVertexAnimationSettings\",\r\n                    \"bakedVertexAnimationTextureSizeInverted\",\r\n                    \"bakedVertexAnimationTime\",\r\n                    \"bakedVertexAnimationTexture\",\r\n                ];\r\n                const samplers = [\"diffuseSampler\", \"boneSampler\", \"morphTargets\", \"bakedVertexAnimationTexture\"];\r\n                const uniformBuffers = [\"Scene\", \"Mesh\"];\r\n\r\n                AddClipPlaneUniforms(uniforms);\r\n\r\n                // Custom shader?\r\n                if (this.customShaderOptions) {\r\n                    shaderName = this.customShaderOptions.shaderName;\r\n\r\n                    if (this.customShaderOptions.attributes) {\r\n                        for (const attrib of this.customShaderOptions.attributes) {\r\n                            if (attribs.indexOf(attrib) === -1) {\r\n                                attribs.push(attrib);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (this.customShaderOptions.uniforms) {\r\n                        for (const uniform of this.customShaderOptions.uniforms) {\r\n                            if (uniforms.indexOf(uniform) === -1) {\r\n                                uniforms.push(uniform);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (this.customShaderOptions.samplers) {\r\n                        for (const sampler of this.customShaderOptions.samplers) {\r\n                            if (samplers.indexOf(sampler) === -1) {\r\n                                samplers.push(sampler);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                const engine = this._scene.getEngine();\r\n\r\n                effect = engine.createEffect(\r\n                    shaderName,\r\n                    <IEffectCreationOptions>{\r\n                        attributes: attribs,\r\n                        uniformsNames: uniforms,\r\n                        uniformBuffersNames: uniformBuffers,\r\n                        samplers: samplers,\r\n                        defines: join,\r\n                        fallbacks: fallbacks,\r\n                        onCompiled: null,\r\n                        onError: null,\r\n                        indexParameters: { maxSimultaneousMorphTargets: numMorphInfluencers },\r\n                        shaderLanguage: this._shaderLanguage,\r\n                    },\r\n                    engine\r\n                );\r\n\r\n                subMeshEffect.setEffect(effect, cachedDefines);\r\n            }\r\n\r\n            if (!effect.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (this.useBlurExponentialShadowMap || this.useBlurCloseExponentialShadowMap) {\r\n            if (!this._blurPostProcesses || !this._blurPostProcesses.length) {\r\n                this._initializeBlurRTTAndPostProcesses();\r\n            }\r\n        }\r\n\r\n        if (this._kernelBlurXPostprocess && !this._kernelBlurXPostprocess.isReady()) {\r\n            return false;\r\n        }\r\n        if (this._kernelBlurYPostprocess && !this._kernelBlurYPostprocess.isReady()) {\r\n            return false;\r\n        }\r\n        if (this._boxBlurPostprocess && !this._boxBlurPostprocess.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Prepare all the defines in a material relying on a shadow map at the specified light index.\r\n     * @param defines Defines of the material we want to update\r\n     * @param lightIndex Index of the light in the enabled light list of the material\r\n     */\r\n    public prepareDefines(defines: any, lightIndex: number): void {\r\n        const scene = this._scene;\r\n        const light = this._light;\r\n\r\n        if (!scene.shadowsEnabled || !light.shadowEnabled) {\r\n            return;\r\n        }\r\n\r\n        defines[\"SHADOW\" + lightIndex] = true;\r\n\r\n        if (this.useContactHardeningShadow) {\r\n            defines[\"SHADOWPCSS\" + lightIndex] = true;\r\n            if (this._filteringQuality === ShadowGenerator.QUALITY_LOW) {\r\n                defines[\"SHADOWLOWQUALITY\" + lightIndex] = true;\r\n            } else if (this._filteringQuality === ShadowGenerator.QUALITY_MEDIUM) {\r\n                defines[\"SHADOWMEDIUMQUALITY\" + lightIndex] = true;\r\n            }\r\n            // else default to high.\r\n        } else if (this.usePercentageCloserFiltering) {\r\n            defines[\"SHADOWPCF\" + lightIndex] = true;\r\n            if (this._filteringQuality === ShadowGenerator.QUALITY_LOW) {\r\n                defines[\"SHADOWLOWQUALITY\" + lightIndex] = true;\r\n            } else if (this._filteringQuality === ShadowGenerator.QUALITY_MEDIUM) {\r\n                defines[\"SHADOWMEDIUMQUALITY\" + lightIndex] = true;\r\n            }\r\n            // else default to high.\r\n        } else if (this.usePoissonSampling) {\r\n            defines[\"SHADOWPOISSON\" + lightIndex] = true;\r\n        } else if (this.useExponentialShadowMap || this.useBlurExponentialShadowMap) {\r\n            defines[\"SHADOWESM\" + lightIndex] = true;\r\n        } else if (this.useCloseExponentialShadowMap || this.useBlurCloseExponentialShadowMap) {\r\n            defines[\"SHADOWCLOSEESM\" + lightIndex] = true;\r\n        }\r\n\r\n        if (light.needCube()) {\r\n            defines[\"SHADOWCUBE\" + lightIndex] = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the shadow related information inside of an effect (information like near, far, darkness...\r\n     * defined in the generator but impacting the effect).\r\n     * @param lightIndex Index of the light in the enabled light list of the material owning the effect\r\n     * @param effect The effect we are binding the information for\r\n     */\r\n    public bindShadowLight(lightIndex: string, effect: Effect): void {\r\n        const light = this._light;\r\n        const scene = this._scene;\r\n\r\n        if (!scene.shadowsEnabled || !light.shadowEnabled) {\r\n            return;\r\n        }\r\n\r\n        const camera = this._getCamera();\r\n        const shadowMap = this.getShadowMap();\r\n\r\n        if (!shadowMap) {\r\n            return;\r\n        }\r\n\r\n        if (!light.needCube()) {\r\n            effect.setMatrix(\"lightMatrix\" + lightIndex, this.getTransformMatrix());\r\n        }\r\n\r\n        // Only PCF uses depth stencil texture.\r\n        const shadowMapForRendering = this.getShadowMapForRendering();\r\n        if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n            effect.setDepthStencilTexture(\"shadowTexture\" + lightIndex, shadowMapForRendering);\r\n            light._uniformBuffer.updateFloat4(\"shadowsInfo\", this.getDarkness(), shadowMap.getSize().width, 1 / shadowMap.getSize().width, this.frustumEdgeFalloff, lightIndex);\r\n        } else if (this._filter === ShadowGenerator.FILTER_PCSS) {\r\n            effect.setDepthStencilTexture(\"shadowTexture\" + lightIndex, shadowMapForRendering);\r\n            effect.setTexture(\"depthTexture\" + lightIndex, shadowMapForRendering);\r\n            light._uniformBuffer.updateFloat4(\r\n                \"shadowsInfo\",\r\n                this.getDarkness(),\r\n                1 / shadowMap.getSize().width,\r\n                this._contactHardeningLightSizeUVRatio * shadowMap.getSize().width,\r\n                this.frustumEdgeFalloff,\r\n                lightIndex\r\n            );\r\n        } else {\r\n            effect.setTexture(\"shadowTexture\" + lightIndex, shadowMapForRendering);\r\n            light._uniformBuffer.updateFloat4(\"shadowsInfo\", this.getDarkness(), this.blurScale / shadowMap.getSize().width, this.depthScale, this.frustumEdgeFalloff, lightIndex);\r\n        }\r\n\r\n        light._uniformBuffer.updateFloat2(\r\n            \"depthValues\",\r\n            this.getLight().getDepthMinZ(camera),\r\n            this.getLight().getDepthMinZ(camera) + this.getLight().getDepthMaxZ(camera),\r\n            lightIndex\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the view matrix used to render the shadow map.\r\n     */\r\n    public get viewMatrix() {\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the projection matrix used to render the shadow map.\r\n     */\r\n    public get projectionMatrix() {\r\n        return this._projectionMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the transformation matrix used to project the meshes into the map from the light point of view.\r\n     * (eq to shadow projection matrix * light transform matrix)\r\n     * @returns The transform matrix used to create the shadow map\r\n     */\r\n    public getTransformMatrix(): Matrix {\r\n        const scene = this._scene;\r\n        if (this._currentRenderId === scene.getRenderId() && this._currentFaceIndexCache === this._currentFaceIndex) {\r\n            return this._transformMatrix;\r\n        }\r\n\r\n        this._currentRenderId = scene.getRenderId();\r\n        this._currentFaceIndexCache = this._currentFaceIndex;\r\n\r\n        let lightPosition = this._light.position;\r\n        if (this._light.computeTransformedInformation()) {\r\n            lightPosition = this._light.transformedPosition;\r\n        }\r\n\r\n        Vector3.NormalizeToRef(this._light.getShadowDirection(this._currentFaceIndex), this._lightDirection);\r\n        if (Math.abs(Vector3.Dot(this._lightDirection, Vector3.Up())) === 1.0) {\r\n            this._lightDirection.z = 0.0000000000001; // Required to avoid perfectly perpendicular light\r\n        }\r\n\r\n        if (\r\n            this._light.needProjectionMatrixCompute() ||\r\n            !this._cachedPosition ||\r\n            !this._cachedDirection ||\r\n            !lightPosition.equals(this._cachedPosition) ||\r\n            !this._lightDirection.equals(this._cachedDirection)\r\n        ) {\r\n            this._cachedPosition.copyFrom(lightPosition);\r\n            this._cachedDirection.copyFrom(this._lightDirection);\r\n\r\n            Matrix.LookAtLHToRef(lightPosition, lightPosition.add(this._lightDirection), Vector3.Up(), this._viewMatrix);\r\n\r\n            const shadowMap = this.getShadowMap();\r\n\r\n            if (shadowMap) {\r\n                const renderList = shadowMap.renderList;\r\n\r\n                if (renderList) {\r\n                    this._light.setShadowProjectionMatrix(this._projectionMatrix, this._viewMatrix, renderList);\r\n                }\r\n            }\r\n\r\n            this._viewMatrix.multiplyToRef(this._projectionMatrix, this._transformMatrix);\r\n        }\r\n\r\n        return this._transformMatrix;\r\n    }\r\n\r\n    /**\r\n     * Recreates the shadow map dependencies like RTT and post processes. This can be used during the switch between\r\n     * Cube and 2D textures for instance.\r\n     */\r\n    public recreateShadowMap(): void {\r\n        const shadowMap = this._shadowMap;\r\n        if (!shadowMap) {\r\n            return;\r\n        }\r\n\r\n        // Track render list.\r\n        const renderList = shadowMap.renderList;\r\n        // Clean up existing data.\r\n        this._disposeRTTandPostProcesses();\r\n        // Reinitializes.\r\n        this._initializeGenerator();\r\n        // Reaffect the filter to ensure a correct fallback if necessary.\r\n        this.filter = this._filter;\r\n        // Reaffect the filter.\r\n        this._applyFilterValues();\r\n        // Reaffect Render List.\r\n        if (renderList) {\r\n            // Note: don't do this._shadowMap!.renderList = renderList;\r\n            // The renderList hooked array is accessing the old RenderTargetTexture (see RenderTargetTexture._hookArray), which is disposed at this point (by the call to _disposeRTTandPostProcesses)\r\n            if (!this._shadowMap!.renderList) {\r\n                this._shadowMap!.renderList = [];\r\n            }\r\n            for (const mesh of renderList) {\r\n                this._shadowMap!.renderList.push(mesh);\r\n            }\r\n        } else {\r\n            this._shadowMap!.renderList = null;\r\n        }\r\n    }\r\n\r\n    protected _disposeBlurPostProcesses(): void {\r\n        if (this._shadowMap2) {\r\n            this._shadowMap2.dispose();\r\n            this._shadowMap2 = null;\r\n        }\r\n\r\n        if (this._boxBlurPostprocess) {\r\n            this._boxBlurPostprocess.dispose();\r\n            this._boxBlurPostprocess = null;\r\n        }\r\n\r\n        if (this._kernelBlurXPostprocess) {\r\n            this._kernelBlurXPostprocess.dispose();\r\n            this._kernelBlurXPostprocess = null;\r\n        }\r\n\r\n        if (this._kernelBlurYPostprocess) {\r\n            this._kernelBlurYPostprocess.dispose();\r\n            this._kernelBlurYPostprocess = null;\r\n        }\r\n\r\n        this._blurPostProcesses = [];\r\n    }\r\n\r\n    protected _disposeRTTandPostProcesses(): void {\r\n        if (this._shadowMap) {\r\n            this._shadowMap.dispose();\r\n            this._shadowMap = null;\r\n        }\r\n\r\n        this._disposeBlurPostProcesses();\r\n    }\r\n\r\n    protected _disposeSceneUBOs(): void {\r\n        if (this._sceneUBOs) {\r\n            for (const ubo of this._sceneUBOs) {\r\n                ubo.dispose();\r\n            }\r\n            this._sceneUBOs = [];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes the ShadowGenerator.\r\n     * Returns nothing.\r\n     */\r\n    public dispose(): void {\r\n        this._disposeRTTandPostProcesses();\r\n\r\n        this._disposeSceneUBOs();\r\n\r\n        if (this._light) {\r\n            if (this._light._shadowGenerators) {\r\n                const iterator = this._light._shadowGenerators.entries();\r\n                for (let entry = iterator.next(); entry.done !== true; entry = iterator.next()) {\r\n                    const [camera, shadowGenerator] = entry.value;\r\n                    if (shadowGenerator === this) {\r\n                        this._light._shadowGenerators.delete(camera);\r\n                    }\r\n                }\r\n                if (this._light._shadowGenerators.size === 0) {\r\n                    this._light._shadowGenerators = null;\r\n                }\r\n            }\r\n            this._light._markMeshesAsLightDirty();\r\n        }\r\n\r\n        this.onBeforeShadowMapRenderMeshObservable.clear();\r\n        this.onBeforeShadowMapRenderObservable.clear();\r\n        this.onAfterShadowMapRenderMeshObservable.clear();\r\n        this.onAfterShadowMapRenderObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Serializes the shadow generator setup to a json object.\r\n     * @returns The serialized JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        const shadowMap = this.getShadowMap();\r\n\r\n        if (!shadowMap) {\r\n            return serializationObject;\r\n        }\r\n\r\n        serializationObject.className = this.getClassName();\r\n        serializationObject.lightId = this._light.id;\r\n        serializationObject.cameraId = this._camera?.id;\r\n        serializationObject.id = this.id;\r\n        serializationObject.mapSize = shadowMap.getRenderSize();\r\n        serializationObject.forceBackFacesOnly = this.forceBackFacesOnly;\r\n        serializationObject.darkness = this.getDarkness();\r\n        serializationObject.transparencyShadow = this._transparencyShadow;\r\n        serializationObject.frustumEdgeFalloff = this.frustumEdgeFalloff;\r\n        serializationObject.bias = this.bias;\r\n        serializationObject.normalBias = this.normalBias;\r\n        serializationObject.usePercentageCloserFiltering = this.usePercentageCloserFiltering;\r\n        serializationObject.useContactHardeningShadow = this.useContactHardeningShadow;\r\n        serializationObject.contactHardeningLightSizeUVRatio = this.contactHardeningLightSizeUVRatio;\r\n        serializationObject.filteringQuality = this.filteringQuality;\r\n        serializationObject.useExponentialShadowMap = this.useExponentialShadowMap;\r\n        serializationObject.useBlurExponentialShadowMap = this.useBlurExponentialShadowMap;\r\n        serializationObject.useCloseExponentialShadowMap = this.useBlurExponentialShadowMap;\r\n        serializationObject.useBlurCloseExponentialShadowMap = this.useBlurExponentialShadowMap;\r\n        serializationObject.usePoissonSampling = this.usePoissonSampling;\r\n        serializationObject.depthScale = this.depthScale;\r\n        serializationObject.blurBoxOffset = this.blurBoxOffset;\r\n        serializationObject.blurKernel = this.blurKernel;\r\n        serializationObject.blurScale = this.blurScale;\r\n        serializationObject.useKernelBlur = this.useKernelBlur;\r\n\r\n        serializationObject.renderList = [];\r\n        if (shadowMap.renderList) {\r\n            for (let meshIndex = 0; meshIndex < shadowMap.renderList.length; meshIndex++) {\r\n                const mesh = shadowMap.renderList[meshIndex];\r\n\r\n                serializationObject.renderList.push(mesh.id);\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a serialized ShadowGenerator and returns a new ShadowGenerator.\r\n     * @param parsedShadowGenerator The JSON object to parse\r\n     * @param scene The scene to create the shadow map for\r\n     * @param constr A function that builds a shadow generator or undefined to create an instance of the default shadow generator\r\n     * @returns The parsed shadow generator\r\n     */\r\n    public static Parse(parsedShadowGenerator: any, scene: Scene, constr?: (mapSize: number, light: IShadowLight, camera: Nullable<Camera>) => ShadowGenerator): ShadowGenerator {\r\n        const light = <IShadowLight>scene.getLightById(parsedShadowGenerator.lightId);\r\n        const camera: Nullable<Camera> = parsedShadowGenerator.cameraId !== undefined ? scene.getCameraById(parsedShadowGenerator.cameraId) : null;\r\n        const shadowGenerator = constr ? constr(parsedShadowGenerator.mapSize, light, camera) : new ShadowGenerator(parsedShadowGenerator.mapSize, light, undefined, camera);\r\n        const shadowMap = shadowGenerator.getShadowMap();\r\n\r\n        for (let meshIndex = 0; meshIndex < parsedShadowGenerator.renderList.length; meshIndex++) {\r\n            const meshes = scene.getMeshesById(parsedShadowGenerator.renderList[meshIndex]);\r\n            for (const mesh of meshes) {\r\n                if (!shadowMap) {\r\n                    continue;\r\n                }\r\n                if (!shadowMap.renderList) {\r\n                    shadowMap.renderList = [];\r\n                }\r\n                shadowMap.renderList.push(mesh);\r\n            }\r\n        }\r\n\r\n        if (parsedShadowGenerator.id !== undefined) {\r\n            shadowGenerator.id = parsedShadowGenerator.id;\r\n        }\r\n\r\n        shadowGenerator.forceBackFacesOnly = !!parsedShadowGenerator.forceBackFacesOnly;\r\n\r\n        if (parsedShadowGenerator.darkness !== undefined) {\r\n            shadowGenerator.setDarkness(parsedShadowGenerator.darkness);\r\n        }\r\n\r\n        if (parsedShadowGenerator.transparencyShadow) {\r\n            shadowGenerator.setTransparencyShadow(true);\r\n        }\r\n\r\n        if (parsedShadowGenerator.frustumEdgeFalloff !== undefined) {\r\n            shadowGenerator.frustumEdgeFalloff = parsedShadowGenerator.frustumEdgeFalloff;\r\n        }\r\n\r\n        if (parsedShadowGenerator.bias !== undefined) {\r\n            shadowGenerator.bias = parsedShadowGenerator.bias;\r\n        }\r\n\r\n        if (parsedShadowGenerator.normalBias !== undefined) {\r\n            shadowGenerator.normalBias = parsedShadowGenerator.normalBias;\r\n        }\r\n\r\n        if (parsedShadowGenerator.usePercentageCloserFiltering) {\r\n            shadowGenerator.usePercentageCloserFiltering = true;\r\n        } else if (parsedShadowGenerator.useContactHardeningShadow) {\r\n            shadowGenerator.useContactHardeningShadow = true;\r\n        } else if (parsedShadowGenerator.usePoissonSampling) {\r\n            shadowGenerator.usePoissonSampling = true;\r\n        } else if (parsedShadowGenerator.useExponentialShadowMap) {\r\n            shadowGenerator.useExponentialShadowMap = true;\r\n        } else if (parsedShadowGenerator.useBlurExponentialShadowMap) {\r\n            shadowGenerator.useBlurExponentialShadowMap = true;\r\n        } else if (parsedShadowGenerator.useCloseExponentialShadowMap) {\r\n            shadowGenerator.useCloseExponentialShadowMap = true;\r\n        } else if (parsedShadowGenerator.useBlurCloseExponentialShadowMap) {\r\n            shadowGenerator.useBlurCloseExponentialShadowMap = true;\r\n        }\r\n        // Backward compat\r\n        else if (parsedShadowGenerator.useVarianceShadowMap) {\r\n            shadowGenerator.useExponentialShadowMap = true;\r\n        } else if (parsedShadowGenerator.useBlurVarianceShadowMap) {\r\n            shadowGenerator.useBlurExponentialShadowMap = true;\r\n        }\r\n\r\n        if (parsedShadowGenerator.contactHardeningLightSizeUVRatio !== undefined) {\r\n            shadowGenerator.contactHardeningLightSizeUVRatio = parsedShadowGenerator.contactHardeningLightSizeUVRatio;\r\n        }\r\n\r\n        if (parsedShadowGenerator.filteringQuality !== undefined) {\r\n            shadowGenerator.filteringQuality = parsedShadowGenerator.filteringQuality;\r\n        }\r\n\r\n        if (parsedShadowGenerator.depthScale) {\r\n            shadowGenerator.depthScale = parsedShadowGenerator.depthScale;\r\n        }\r\n\r\n        if (parsedShadowGenerator.blurScale) {\r\n            shadowGenerator.blurScale = parsedShadowGenerator.blurScale;\r\n        }\r\n\r\n        if (parsedShadowGenerator.blurBoxOffset) {\r\n            shadowGenerator.blurBoxOffset = parsedShadowGenerator.blurBoxOffset;\r\n        }\r\n\r\n        if (parsedShadowGenerator.useKernelBlur) {\r\n            shadowGenerator.useKernelBlur = parsedShadowGenerator.useKernelBlur;\r\n        }\r\n\r\n        if (parsedShadowGenerator.blurKernel) {\r\n            shadowGenerator.blurKernel = parsedShadowGenerator.blurKernel;\r\n        }\r\n\r\n        return shadowGenerator;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACnE,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAMpD,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAG3C,OAAO,EAAE,OAAO,EAAE,MAAM,kCAAkC,CAAC;AAC3D,OAAO,EAAE,mBAAmB,EAAE,MAAM,8CAA8C,CAAC;AAEnF,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,qCAAqC,CAAC;AAEtE,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACpE,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAI1D,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,iCAAiC,EAAE,MAAM,yCAAyC,CAAC;AAEjI,OAAO,EACH,yBAAyB,EACzB,sBAAsB,EACtB,0CAA0C,EAC1C,0BAA0B,GAC7B,MAAM,0CAA0C,CAAC;;;;;;;;;;;;;;;;AAyH5C,MAAO,eAAe;IA+HxB;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IACD;;OAEG,CACH,IAAW,IAAI,CAAC,IAAY,EAAA;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAGD;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;OAEG,CACH,IAAW,UAAU,CAAC,UAAkB,EAAA;QACpC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAClC,CAAC;IAGD;;;OAGG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD;;;OAGG,CACH,IAAW,aAAa,CAAC,KAAa,EAAA;QAClC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAGD;;;OAGG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD;;;OAGG,CACH,IAAW,SAAS,CAAC,KAAa,EAAA;QAC9B,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAGD;;;OAGG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;;OAGG,CACH,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAGD;;;OAGG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD;;;OAGG,CACH,IAAW,aAAa,CAAC,KAAc,EAAA;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAGD;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;IAC3F,CAAC;IACD;;;OAGG,CACH,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAES,eAAe,CAAC,MAAc,EAAA;QACpC,OAAO,MAAM,CAAC;IAClB,CAAC;IAGD;;;OAGG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IACD;;;OAGG,CACH,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAEpC,oFAAoF;QACpF,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;YACzB,IAAI,KAAK,KAAK,eAAe,CAAC,+BAA+B,EAAE,CAAC;gBAC5D,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACpC,OAAO;YACX,CAAC,MAAM,IAAI,KAAK,KAAK,eAAe,CAAC,oCAAoC,EAAE,CAAC;gBACxE,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;gBACzC,OAAO;YACX,CAAC,MAEI,IAAI,KAAK,KAAK,eAAe,CAAC,UAAU,IAAI,KAAK,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;gBACrF,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,OAAO;YACX,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IAAI,KAAK,KAAK,eAAe,CAAC,UAAU,IAAI,KAAK,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;YAChF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;gBAC3D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,OAAO;YACX,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,sBAAsB,CAAC;IAClE,CAAC;IACD;;OAEG,CACH,IAAW,kBAAkB,CAAC,KAAc,EAAA;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC;QAE5E,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,sBAAsB,EAAE,CAAC;YACnE,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;OAEG,CACH,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,2BAA2B,CAAC;IACvE,CAAC;IACD;;OAEG,CACH,IAAW,uBAAuB,CAAC,KAAc,EAAA;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,2BAA2B,CAAC,CAAC;QAEjF,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,2BAA2B,EAAE,CAAC;YACxE,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;OAEG,CACH,IAAW,2BAA2B,GAAA;QAClC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,+BAA+B,CAAC;IAC3E,CAAC;IACD;;OAEG,CACH,IAAW,2BAA2B,CAAC,KAAc,EAAA;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,+BAA+B,CAAC,CAAC;QAErF,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,+BAA+B,EAAE,CAAC;YAC5E,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;;OAGG,CACH,IAAW,4BAA4B,GAAA;QACnC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,gCAAgC,CAAC;IAC5E,CAAC;IACD;;;OAGG,CACH,IAAW,4BAA4B,CAAC,KAAc,EAAA;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,gCAAgC,CAAC,CAAC;QAEtF,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,gCAAgC,EAAE,CAAC;YAC7E,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;;OAGG,CACH,IAAW,gCAAgC,GAAA;QACvC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,oCAAoC,CAAC;IAChF,CAAC;IACD;;;OAGG,CACH,IAAW,gCAAgC,CAAC,KAAc,EAAA;QACtD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,oCAAoC,CAAC,CAAC;QAE1F,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,oCAAoC,EAAE,CAAC;YACjF,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAED;;OAEG,CACH,IAAW,4BAA4B,GAAA;QACnC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,UAAU,CAAC;IACtD,CAAC;IACD;;OAEG,CACH,IAAW,4BAA4B,CAAC,KAAc,EAAA;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEhE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,UAAU,EAAE,CAAC;YACvD,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAGD;;;OAGG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;;OAGG,CACH,IAAW,gBAAgB,CAAC,gBAAwB,EAAA;QAChD,IAAI,IAAI,CAAC,iBAAiB,KAAK,gBAAgB,EAAE,CAAC;YAC9C,OAAO;QACX,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAE1C,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,WAAW,CAAC;IACvD,CAAC;IACD;;OAEG,CACH,IAAW,yBAAyB,CAAC,KAAc,EAAA;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAEjE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;YACxD,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC;IAC/D,CAAC;IAGD;;;;;;;;OAQG,CACH,IAAW,gCAAgC,GAAA;QACvC,OAAO,IAAI,CAAC,iCAAiC,CAAC;IAClD,CAAC;IACD;;;;;;;;OAQG,CACH,IAAW,gCAAgC,CAAC,gCAAwC,EAAA;QAChF,IAAI,CAAC,iCAAiC,GAAG,gCAAgC,CAAC;IAC9E,CAAC;IAID,iDAAA,EAAmD,CACnD,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAa,EAAA;QAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG,CACI,WAAW,GAAA;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;;;OAIG,CACI,WAAW,CAAC,QAAgB,EAAA;QAC/B,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QACzB,CAAC,MAAM,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QACzB,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC9B,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAID,wDAAA,EAA0D,CAC1D,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAc,EAAA;QACxC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,WAAoB,EAAA;QAC7C,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC;QACvC,OAAO,IAAI,CAAC;IAChB,CAAC;IAoBD;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACI,wBAAwB,GAAA;QAC3B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,WAAW,CAAC;QAC5B,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,eAAe,CAAC,SAAS,CAAC;IACrC,CAAC;IAED;;;;;OAKG,CACI,eAAe,CAAC,IAAkB,EAAE,kBAAkB,GAAG,IAAI,EAAA;QAChE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YAC9B,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAClD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,kBAAkB,EAAE,CAAC;YACrB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,CAAE,CAAC;gBAC5C,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACvD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC/C,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,kBAAkB,CAAC,IAAkB,EAAE,kBAAkB,GAAG,IAAI,EAAA;QACnE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAEvD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,kBAAkB,EAAE,CAAC;YACrB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,WAAW,EAAE,CAAE,CAAC;gBACrC,IAAI,CAAC,kBAAkB,CAAM,KAAK,CAAC,CAAC;YACxC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAQD;;;OAGG,CACI,QAAQ,GAAA;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAKD;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAWS,UAAU,GAAA;QAChB,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IACpD,CAAC;IAmCD;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,IAAY,EAAA;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACtC,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;;;;;;OAWG,CACH,YAAY,OAAe,EAAE,KAAmB,EAAE,iBAA2B,EAAE,MAAyB,EAAE,iBAA2B,EAAE,SAAS,GAAG,KAAK,CAAA;QAloBxJ;;WAEG,CACI,IAAA,CAAA,iCAAiC,GAAG,IAAI,uKAAU,EAAU,CAAC;QAEpE;;WAEG,CACI,IAAA,CAAA,gCAAgC,GAAG,8JAAI,aAAU,EAAU,CAAC;QAEnE;;;WAGG,CACI,IAAA,CAAA,qCAAqC,GAAG,6JAAI,cAAU,EAAQ,CAAC;QAEtE;;;WAGG,CACI,IAAA,CAAA,oCAAoC,GAAG,8JAAI,aAAU,EAAQ,CAAC;QAErE;;;WAGG,CACI,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAEpB,IAAA,CAAA,KAAK,GAAG,OAAO,CAAC;QAchB,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAchB,IAAA,CAAA,cAAc,GAAG,CAAC,CAAC;QAqBnB,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QAqBf,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAqBhB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAwCvB,IAAA,CAAA,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC;QAkKtC,IAAA,CAAA,iBAAiB,GAAG,eAAe,CAAC,YAAY,CAAC;QA0CjD,IAAA,CAAA,iCAAiC,GAAG,GAAG,CAAC;QA0BxC,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QAmCd,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAqBtC;;;;;;;WAOG,CACI,IAAA,CAAA,2BAA2B,GAAY,KAAK,CAAC;QAEpD;;WAEG,CACI,IAAA,CAAA,qCAAqC,GAAY,KAAK,CAAC;QAyF9D;;WAEG,CACI,IAAA,CAAA,kBAAkB,GAAG,CAAC,CAAC;QAW9B,0CAAA,EAA4C,CAClC,IAAA,CAAA,eAAe,GAAA,EAAA,uBAAA,GAAuB;QAShD;;;;WAIG,CACI,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAUxB,IAAA,CAAA,eAAe,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAEjC,IAAA,CAAA,WAAW,kKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;QAC5B,IAAA,CAAA,iBAAiB,kKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;QAClC,IAAA,CAAA,gBAAgB,kKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;QACjC,IAAA,CAAA,eAAe,GAAY,mKAAI,UAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC7F,IAAA,CAAA,gBAAgB,GAAY,mKAAI,UAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAQ9F,IAAA,CAAA,iBAAiB,GAAG,CAAC,CAAC;QACtB,IAAA,CAAA,sBAAsB,GAAG,CAAC,CAAC;QAE3B,IAAA,CAAA,qBAAqB,GAAG,wKAAM,CAAC,QAAQ,EAAE,CAAC;QAsQ5C,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QA9N3B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,iBAAiB,CAAC;QAE9C,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;QAEvC,IAAI,gBAAgB,GAAG,KAAK,CAAC,iBAAiB,CAAC;QAC/C,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,gBAAgB,GAAG,KAAK,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3D,CAAC;QACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,sBAAsB,CAAC;QAE9D,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAA,mCAAA,EAAsC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAA,EAAA,CAAI,CAAC,CAAC,CAAC;QAC3H,CAAC;QAED,eAAe,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE3D,4DAA4D;QAC5D,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC;QAE/C,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,+BAA+B,EAAE,CAAC;gBACtE,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,sBAAsB,CAAC;YACzD,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACrE,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,iBAAiB,CAAC;YACpD,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;YAC5D,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBAC9D,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,iBAAiB,CAAC;YACpD,CAAC,MAAM,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,+BAA+B,EAAE,CAAC;gBAC7E,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,sBAAsB,CAAC;YACzD,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;YAC5D,CAAC;QACL,CAAC;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAES,oBAAoB,GAAA;QAC1B,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACtC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAES,0BAA0B,GAAA;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,IAAI,MAAM,CAAC,SAAS,CAAC,0BAA0B,EAAE,CAAC;YAC9C,IAAI,CAAC,UAAU,GAAG,wLAAI,sBAAmB,CACrC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,YAAY,EAC/B,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,MAAM,EACX,KAAK,EACL,IAAI,EACJ,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,EACtB,SAAS,EACT,KAAK,EACL,KAAK,EACL,SAAS,EACT,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CACvF,CAAC;YACF,IAAI,CAAC,UAAU,CAAC,yBAAyB,CACrC,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,MAAA,GAAS,CAAC,CAAA,MAAO,CAAC,CAAC,CAAC,QAAA,CAAS,CAAC,IAAI,EACjE,GAAA,CAAI,EACJ,QAAA,CAAS,EACT,SAAS,EACT,SAAS,EACT,OAAA,EAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,OAAkC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CACvD,CAAC;QACN,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,UAAU,GAAG,IAAI,0MAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACnK,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAC7C,CAAC;IAES,oBAAoB,GAAA;QAC1B,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;QAClD,IAAI,CAAC,UAAU,CAAC,yBAAyB,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,kBAAkB,yKAAC,UAAO,CAAC,qBAAqB,CAAC,CAAC;QAClE,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,UAAU,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAC5C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC;QACpD,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,UAAU,CAAC,oBAAoB,GAAG,CACnC,eAAoC,EACpC,kBAAuC,EACvC,oBAAyC,EACzC,kBAAuC,EACzC,CAAG,CAAD,GAAK,CAAC,mBAAmB,CAAC,eAAe,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;QAE7G,gGAAgG;QAChG,2EAA2E;QAC3E,0CAA0C;QAC1C,IAAI,CAAC,UAAU,CAAC,qBAAqB,GAAG,CAAC,IAAkB,EAAE,YAAoB,EAAE,OAAiB,EAAW,EAAE;YAC7G,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,OAAO,GAAG,IAAI,CAAC;YACnB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;gBACnC,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBACjD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;gBACjC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBAEvC,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,IAAI,AAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAE,CAAC;oBACjH,SAAS;gBACb,CAAC;gBAED,MAAM,KAAK,GAAG,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBACjG,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;oBACnB,SAAS;gBACb,CAAC;gBAED,MAAM,0BAA0B,GAC5B,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe,IAChC,CAAE,AAAD,KAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,GAAI,aAAa,CAAC,gBAAgB,CAAC,CAAC;gBAE5I,MAAM,aAAa,GAAG,QAAQ,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;gBAEvE,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,0BAA0B,EAAE,aAAa,CAAC,IAAI,OAAO,CAAC;YAC1F,CAAC;YAED,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAC5D,MAAM,CAAC,eAAe,EAAE,CAAC,CAAA,kCAAA,EAAqC,MAAM,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,SAAiB,EAAE,EAAE;YAC/D,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,CAAC;YACD,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;YACnC,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,UAAU,EAAE,CAAC;gBAC9C,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,sCAAsC;YACjE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzE,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,YAAY,EAAE,CAAC;gBACnD,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACnC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC7C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC7D,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,CAAC,4DAA4D;YAEjG,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,UAAU,EAAE,CAAC;gBAC9C,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,2BAA2B,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,CAAC;gBAC9E,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC3B,OAAO;YACX,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAElD,IAAI,SAAS,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;gBACnG,MAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC,YAAa,EAAE,IAAI,CAAC,CAAC;YAC5D,CAAC;YAED,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,wCAAwC;QACxC,MAAM,SAAS,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,MAAM,QAAQ,GAAG,kKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,UAAU,EAAE,CAAC;gBAC9C,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC/C,CAAC,MAAM,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBAC1E,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC/C,CAAC,MAAM,CAAC;gBACJ,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YAC3C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAW,CAAC,QAAQ,CAAC;YACjD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,aAAa,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACtC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,2DAA2D;QAC3D,4CAA4C;QAC5C,IAAK,IAAI,CAAC,wKAAG,mBAAgB,CAAC,mBAAmB,EAAE,CAAC,wKAAG,mBAAgB,CAAC,mBAAmB,EAAE,CAAC,EAAE,CAAE,CAAC;YAC/F,IAAI,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAGO,KAAK,CAAC,sBAAsB,CAAC,SAAS,GAAG,KAAK,EAAA;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;YAC9D,IAAI,CAAC,eAAe,GAAA,EAAA,uBAAA,EAAsB,CAAC;YAE3C,MAAM,OAAO,CAAC,GAAG,CAAC;;;;;aAKjB,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC;;;;;aAKjB,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAES,kCAAkC,GAAA;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAElD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,SAAS,KAAK,GAAG,EAAE,CAAC;YAChD,IAAI,CAAC,WAAW,GAAG,wLAAI,sBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YACnK,IAAI,CAAC,WAAW,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,kBAAkB,yKAAC,UAAO,CAAC,qBAAqB,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,uBAAuB,GAAG,4KAAI,kBAAe,CAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,aAAa,EAChC,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACjB,IAAI,CAAC,UAAU,EACf,GAAG,EACH,IAAI,0KACJ,UAAO,CAAC,qBAAqB,EAC7B,MAAM,EACN,KAAK,EACL,IAAI,CAAC,YAAY,CACpB,CAAC;YACF,IAAI,CAAC,uBAAuB,CAAC,KAAK,GAAG,UAAU,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,UAAU,CAAC;YACjD,IAAI,CAAC,uBAAuB,CAAC,6BAA6B,GAAG,IAAI,CAAC;YAClE,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC1D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,uBAAuB,GAAG,4KAAI,kBAAe,CAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,aAAa,EAChC,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EACjB,IAAI,CAAC,UAAU,EACf,GAAG,EACH,IAAI,0KACJ,UAAO,CAAC,qBAAqB,EAC7B,MAAM,EACN,KAAK,EACL,IAAI,CAAC,YAAY,CACpB,CAAC;YAEF,IAAI,CAAC,uBAAuB,CAAC,SAAS,GAAG,KAAK,CAAC;YAC/C,IAAI,CAAC,uBAAuB,CAAC,SAAS,GAAG,KAAK,CAAC;YAE/C,IAAI,IAAI,CAAC,YAAY,KAAK,GAAA,MAAS,CAAC,yBAAyB,EAAE,CAAC;gBAC1C,IAAI,CAAC,uBAAwB,CAAC,WAAW,GAAG,IAAI,CAAC;gBACjD,IAAI,CAAC,uBAAwB,CAAC,WAAW,GAAG,IAAI,CAAC;YACvE,CAAC;YAED,IAAI,CAAC,kBAAkB,GAAG;gBAAC,IAAI,CAAC,uBAAuB;gBAAE,IAAI,CAAC,uBAAuB;aAAC,CAAC;QAC3F,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,mBAAmB,GAAG,wKAAI,cAAW,CACtC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,cAAc,EACjC,cAAc,EACd;gBAAC,YAAY;gBAAE,WAAW;aAAC,EAC3B,EAAE,EACF,GAAG,EACH,IAAI,0KACJ,UAAO,CAAC,qBAAqB,EAC7B,MAAM,EACN,KAAK,EACL,iBAAiB,GAAG,IAAI,CAAC,cAAc,EACvC,IAAI,CAAC,YAAY,EACjB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,CAAC,eAAe,CACvB,CAAC;YACF,IAAI,CAAC,mBAAmB,CAAC,6BAA6B,GAAG,IAAI,CAAC;YAC9D,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACtD,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;gBACvD,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,KAAK,CAAC;YAE3C,IAAI,CAAC,kBAAkB,GAAG;gBAAC,IAAI,CAAC,mBAAmB;aAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAES,mBAAmB,CACzB,eAAoC,EACpC,kBAAuC,EACvC,oBAAyC,EACzC,kBAAuC,EAAA;QAEvC,IAAI,KAAa,CAAC;QAElB,IAAI,kBAAkB,CAAC,MAAM,EAAE,CAAC;YAC5B,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACzD,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACpE,CAAC;QACL,CAAC;QAED,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACzD,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC3D,IAAI,CAAC,0BAA0B,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;YAC5E,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC3D,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACpH,CAAC;QACL,CAAC;IACL,CAAC;IAED,6DAA6D;IACnD,6CAA6C,CAAC,OAAgB,EAAE,MAAc,EAAE,IAAkB,EAAA;QACxG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;IAClE,CAAC;IAES,0BAA0B,CAAC,OAAgB,EAAE,gBAAyB,KAAK,EAAA;QACjF,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjD,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAEvC,aAAa,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAE1E,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;YACxF,OAAO;QACX,CAAC;QAED,UAAU;QACV,QAAQ;QACR,2MAA2M;QAC3M,+IAA+I;QAC/I,0HAA0H;QAC1H,MAAM,MAAM,GAAG,KAAK,CAAC,oBAAoB,CAAC;QAC1C,MAAM,MAAM,GAAG,aAAa,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;QAC9D,IAAI,eAAe,GAAG,QAAQ,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;QAEvE,IAAI,AAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAK,CAAD,AAAE,MAAM,IAAI,MAAM,CAAC,CAAE,CAAC;YAC7C,eAAe,GACX,eAAe,KAAK,IAAA,IAAA,CAAS,CAAC,iCAAiC,CAAC,CAAC,CAAC,SAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC;QAC3K,CAAC;QACD,MAAM,sBAAsB,GAAG,eAAe,KAAK,SAAS,CAAC,iCAAiC,CAAC;QAE/F,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,sBAAsB,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;QAEhH,qBAAqB;QACrB,MAAM,KAAK,GAAG,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACjG,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,MAAM,0BAA0B,GAC5B,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe,IAChC,CAAC,AAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,CAAC,GAAI,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAE5I,IAAI,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnE,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,0BAA0B,EAAE,aAAa,CAAC,EAAE,CAAC;YACnE,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YAExC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,kBAAkB,CAAC;YAEvD,MAAM,WAAW,GAAG,kBAAkB,EAAE,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,mBAAmB,CAAC,IAAI,OAAO,CAAC,eAAe,EAAG,CAAC;YAC3H,MAAM,MAAM,mKAAG,cAAW,CAAC,SAAS,CAAC,WAAW,CAAE,CAAC;YAEnD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEjC,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAC9B,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,+DAA+D;YAE1F,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAEhF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,4JAAK,QAAK,CAAC,4BAA4B,EAAE,CAAC;gBACrE,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5D,CAAC,MAAM,CAAC;gBACJ,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACjC,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;YAErJ,IAAI,aAAa,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACpD,MAAM,CAAC,SAAS,CAAC,yBAAyB,EAAE,aAAa,CAAC,UAAU,GAAG,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1I,CAAC;YAED,IAAI,kBAAkB,EAAE,CAAC;gBACrB,OAAO,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;gBACjD,IAAI,kBAAkB,CAAC,UAAU,EAAE,CAAC;oBAChC,kBAAkB,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;gBAC3G,CAAC,MAAM,CAAC;oBACJ,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;gBACpF,CAAC;gBACD,OAAO,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC,MAAM,CAAC;gBACJ,aAAa;gBACb,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC1D,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAC7G,CAAC;gBAED,QAAQ;gBACR,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,wBAAwB,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC7F,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;oBAExC,IAAI,QAAQ,CAAC,yBAAyB,EAAE,CAAC;wBACrC,MAAM,WAAW,GAAG,QAAQ,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC;wBAEtE,IAAI,CAAC,WAAW,EAAE,CAAC;4BACf,OAAO;wBACX,CAAC;wBAED,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;wBAC9C,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC3E,CAAC,MAAM,CAAC;wBACJ,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC;oBAC/E,CAAC;gBACL,CAAC;gBAED,gBAAgB;oMAChB,4BAAA,AAAyB,EAAC,aAAa,EAAE,MAAM,CAAC,CAAC;gBACjD,IAAI,aAAa,CAAC,kBAAkB,IAAI,aAAa,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;oBAChG,aAAa,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACnD,CAAC;gBAED,0BAA0B;gBAC1B,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC,2BAA2B,CAAC;gBACjE,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;oBACrC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,0BAA0B,CAAC,CAAC;gBACxD,CAAC;gBAED,cAAc;gBACd,gMAAA,AAAa,EAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACvC,IAAI,CAAC,6CAA6C,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;YACvF,CAAC;aAED,4MAAA,AAAsB,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,CAAC;YACpE,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,iBAAiB,EAAE,CAAC;YAExD,MAAM,KAAK,GAAG,aAAa,CAAC,cAAc,EAAE,CAAC;YAE7C,wIAAwI;YACxI,IAAI,0BAA0B,EAAE,CAAC;gBAC7B,aAAa,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAClE,aAAa,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC;YAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;YAClE,CAAC;YAED,cAAc;YACd,IAAI,CAAC,qCAAqC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;YAC1E,IAAI,CAAC,iCAAiC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE/D,OAAO;YACP,aAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,EAAE;gBAChJ,IAAI,aAAa,KAAK,aAAa,IAAI,CAAC,UAAU,EAAE,CAAC;oBACjD,aAAa,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAClE,aAAa,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAClD,CAAC,MAAM,CAAC;oBACJ,aAAa,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;oBAClE,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBACvE,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;YACnE,CAAC;YAED,cAAc;YACd,IAAI,CAAC,gCAAgC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC9D,IAAI,CAAC,oCAAoC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QAC7E,CAAC,MAAM,CAAC;YACJ,8CAA8C;YAC9C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC;YAC1C,CAAC;QACL,CAAC;IACL,CAAC;IAES,kBAAkB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;YAC7F,IAAI,CAAC,UAAU,CAAC,kBAAkB,yKAAC,UAAO,CAAC,oBAAoB,CAAC,CAAC;QACrE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,kLAAO,CAAC,qBAAqB,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,UAAkD,EAAE,OAA4C,EAAA;QACpH,MAAM,YAAY,GAAG;YACjB,YAAY,EAAE,KAAK;YACnB,GAAG,OAAO;SACb,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,IAAI,UAAU,EAAE,CAAC;gBACb,UAAU,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;QACxC,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,IAAI,UAAU,EAAE,CAAC;gBACb,UAAU,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAc,EAAE,CAAC;QAChC,KAAK,MAAM,IAAI,IAAI,UAAU,CAAE,CAAC;YAC5B,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,UAAU,EAAE,CAAC;gBACb,UAAU,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;YACD,OAAO;QACX,CAAC;QAED,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,MAAM,UAAU,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC;gBAC3C,OAAO;YACX,CAAC;YAED,MACI,IAAI,CAAC,OAAO,CACR,SAAS,CAAC,YAAY,CAAC,EACvB,YAAY,CAAC,YAAY,EACzB,SAAS,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,EAAE,wBAAwB,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,KAAK,CAC9G,CACH,CAAC;gBACC,YAAY,EAAE,CAAC;gBACf,IAAI,YAAY,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;oBACnC,IAAI,UAAU,EAAE,CAAC;wBACb,UAAU,CAAC,IAAI,CAAC,CAAC;oBACrB,CAAC;oBACD,OAAO;gBACX,CAAC;YACL,CAAC;YACD,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC;QAEF,UAAU,EAAE,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,qBAAqB,CAAC,OAA4C,EAAA;QAC3E,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACvB,OAAO,EAAE,CAAC;YACd,CAAC,EAAE,OAAO,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,6DAA6D;IACnD,qBAAqB,CAAC,OAAY,EAAE,OAAgB,EAAE,YAAqB,EAAA,CAAS,CAAC;IAEvF,qBAAqB,CAAC,OAAgB,EAAE,YAAqB,EAAE,OAAiB,EAAE,aAAsB,EAAA;QAC5G,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QAEjF,OAAO,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,YAAY,KAAK,IAAA,KAAS,CAAC,GAAA,sBAAyB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE5G,OAAO,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEjH,OAAO,CAAC,IAAI,CAAC,0BAA0B,GAAG,CAAC,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE7H,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QAE/B,eAAe;QACf,OAAO,CAAC,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,CAAC,wKAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9H,OAAO,CAAC,IAAI,CAAC,kCAAkC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,4JAAK,QAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEpI,cAAc;QACd,OAAO,CAAC,IAAI,CAAC,yBAAyB,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE/E,2BAA2B;QAC3B,OAAO,CAAC,IAAI,CAAC,mCAAmC,GAAG,CAAC,IAAI,CAAC,2BAA2B,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAEpH,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAE3D,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;OAMG,CACI,OAAO,CAAC,OAAgB,EAAE,YAAqB,EAAE,aAAsB,EAAA;QAC1E,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,EAClC,kBAAkB,GAAG,QAAQ,EAAE,kBAAkB,CAAC;QAEtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;QAE1E,IAAI,kBAAkB,EAAE,CAAC;YACrB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC3H,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,MAAM,aAAa,GAAG,OAAO,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAE,CAAC;YAEhE,IAAI,MAAM,GAAG,aAAa,CAAC,MAAO,CAAC;YACnC,IAAI,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC;YAE1C,MAAM,OAAO,GAAG;yKAAC,eAAY,CAAC,YAAY;aAAC,CAAC;YAE5C,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;YAE/B,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,IAAI,GAAG,GAAG,KAAK,CAAC;YAChB,IAAI,GAAG,GAAG,KAAK,CAAC;YAChB,MAAM,KAAK,GAAG,KAAK,CAAC;YAEpB,eAAe;YACf,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzE,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,UAAU,CAAC,CAAC;gBACtC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC/B,SAAS,GAAG,IAAI,CAAC;gBACjB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;gBAC9C,CAAC;YACL,CAAC;YAED,aAAa;YACb,MAAM,gBAAgB,GAAG,QAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAEhE,IAAI,gBAAgB,IAAI,QAAQ,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9D,IAAI,IAAI,CAAC,qCAAqC,EAAE,CAAC;oBAC7C,IAAI,CAAC,eAAe,GAAI,QAAgB,CAAC,cAAc,CAAC;gBAC5D,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,mBAAmB,EAAE,CAAC;gBAC1D,CAAC;gBACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;wBAClC,OAAO,KAAK,CAAC;oBACjB,CAAC;oBAED,MAAM,WAAW,GAAI,QAAgB,CAAC,WAAW,IAAI,eAAe,CAAC,oBAAoB,CAAC;oBAE1F,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;oBACrC,IAAI,gBAAgB,EAAE,CAAC;wBACnB,OAAO,CAAC,IAAI,CAAC,CAAA,uBAAA,EAA0B,WAAW,GAAG,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC7F,CAAC;oBACD,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,MAAM,CAAC,EAAE,CAAC;wBAClD,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,MAAM,CAAC,CAAC;wBAClC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;wBAC5B,GAAG,GAAG,IAAI,CAAC;oBACf,CAAC;oBACD,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,OAAO,CAAC,EAAE,CAAC;wBACnD,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;4BAC9C,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,OAAO,CAAC,CAAC;4BACnC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;4BAC5B,GAAG,GAAG,IAAI,CAAC;wBACf,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,QAAQ;YACR,MAAM,SAAS,GAAG,wKAAI,kBAAe,EAAE,CAAC;YACxC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClE,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,OAAO,CAAC,IAAI,CAAC,wKAAY,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;oBAC9B,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,wBAAwB,CAAC,CAAC;oBACpD,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,wBAAwB,CAAC,CAAC;gBACxD,CAAC;gBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxE,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;oBAC9B,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC9C,CAAC;gBAED,IAAI,QAAQ,CAAC,yBAAyB,EAAE,CAAC;oBACrC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACxC,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBACxE,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACnD,CAAC;YAED,gBAAgB;YAChB,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,uLAC7C,6CAAA,AAA0C,EACtC,IAAI,CAAC,kBAAkB,EACvB,OAAO,EACP,OAAO,EACP,IAAI,EACJ,IAAI,EAAE,AACN,SAAS,EAAE,AACX,KAAK,EAAE,AACP,CAHyB,EAGtB,EAAE,AACL,GAAG,EAHyB,AAGvB,AACL,KAAK,CAAC,EAHmB,AACP,MACC,QACG;gBAE1B,CAAC,CAAC;YAER,aAAa;4LACb,oCAAiC,AAAjC,EAAkC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAElE,YAAY;YACZ,IAAI,YAAY,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;oMAClC,6BAAA,AAA0B,EAAC,OAAO,CAAC,CAAC;gBACpC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,CAAC;oBAC9C,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;gBAC3C,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;oBACnC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAE,CAAC;wBACpD,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;4BACjC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBACzB,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,0BAA0B;YAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,2BAA2B,CAAC;YACpD,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACvD,IAAI,YAAY,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;gBAC1D,CAAC;YACL,CAAC;YAED,qBAAqB;YACrB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;gBACzB,aAAa,GAAG,IAAI,CAAC;gBAErB,IAAI,UAAU,GAAG,WAAW,CAAC;gBAC7B,MAAM,QAAQ,GAAG;oBACb,OAAO;oBACP,QAAQ;oBACR,gBAAgB;oBAChB,eAAe;oBACf,aAAa;oBACb,eAAe;oBACf,gBAAgB;oBAChB,uBAAuB;oBACvB,kBAAkB;oBAClB,kBAAkB;oBAClB,yBAAyB;oBACzB,wBAAwB;oBACxB,2BAA2B;oBAC3B,8BAA8B;oBAC9B,yCAAyC;oBACzC,0BAA0B;oBAC1B,6BAA6B;iBAChC,CAAC;gBACF,MAAM,QAAQ,GAAG;oBAAC,gBAAgB;oBAAE,aAAa;oBAAE,cAAc;oBAAE,6BAA6B;iBAAC,CAAC;gBAClG,MAAM,cAAc,GAAG;oBAAC,OAAO;oBAAE,MAAM;iBAAC,CAAC;iBAEzC,sMAAA,AAAoB,EAAC,QAAQ,CAAC,CAAC;gBAE/B,iBAAiB;gBACjB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3B,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;oBAEjD,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,CAAC;wBACtC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAE,CAAC;4BACvD,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gCACjC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BACzB,CAAC;wBACL,CAAC;oBACL,CAAC;oBAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;wBACpC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAE,CAAC;4BACtD,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gCACnC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;4BAC3B,CAAC;wBACL,CAAC;oBACL,CAAC;oBAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;wBACpC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAE,CAAC;4BACtD,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gCACnC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;4BAC3B,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAEvC,MAAM,GAAG,MAAM,CAAC,YAAY,CACxB,UAAU,EACc;oBACpB,UAAU,EAAE,OAAO;oBACnB,aAAa,EAAE,QAAQ;oBACvB,mBAAmB,EAAE,cAAc;oBACnC,QAAQ,EAAE,QAAQ;oBAClB,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,SAAS;oBACpB,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,IAAI;oBACb,eAAe,EAAE;wBAAE,2BAA2B,EAAE,mBAAmB;oBAAA,CAAE;oBACrE,cAAc,EAAE,IAAI,CAAC,eAAe;iBACvC,EACD,MAAM,CACT,CAAC;gBAEF,aAAa,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpB,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAC5E,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBAC9D,IAAI,CAAC,kCAAkC,EAAE,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1E,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC1E,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,OAAY,EAAE,UAAkB,EAAA;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAChD,OAAO;QACX,CAAC;QAED,OAAO,CAAC,QAAQ,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QAEtC,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;YAC1C,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;gBACzD,OAAO,CAAC,kBAAkB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;YACpD,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,cAAc,EAAE,CAAC;gBACnE,OAAO,CAAC,qBAAqB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;YACvD,CAAC;QACD,wBAAwB;QAC5B,CAAC,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC3C,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;YACzC,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;gBACzD,OAAO,CAAC,kBAAkB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;YACpD,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,cAAc,EAAE,CAAC;gBACnE,OAAO,CAAC,qBAAqB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;YACvD,CAAC;QACD,wBAAwB;QAC5B,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACjC,OAAO,CAAC,eAAe,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QACjD,CAAC,MAAM,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAC1E,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QAC7C,CAAC,MAAM,IAAI,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;YACpF,OAAO,CAAC,gBAAgB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QAClD,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;YACnB,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QAC9C,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,eAAe,CAAC,UAAkB,EAAE,MAAc,EAAA;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAChD,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEtC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;YACpB,MAAM,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC5E,CAAC;QAED,uCAAuC;QACvC,MAAM,qBAAqB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC9D,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,UAAU,EAAE,CAAC;YAC9C,MAAM,CAAC,sBAAsB,CAAC,eAAe,GAAG,UAAU,EAAE,qBAAqB,CAAC,CAAC;YACnF,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QACxK,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;YACtD,MAAM,CAAC,sBAAsB,CAAC,eAAe,GAAG,UAAU,EAAE,qBAAqB,CAAC,CAAC;YACnF,MAAM,CAAC,UAAU,CAAC,cAAc,GAAG,UAAU,EAAE,qBAAqB,CAAC,CAAC;YACtE,KAAK,CAAC,cAAc,CAAC,YAAY,CAC7B,aAAa,EACb,IAAI,CAAC,WAAW,EAAE,EAClB,CAAC,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAC7B,IAAI,CAAC,iCAAiC,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAClE,IAAI,CAAC,kBAAkB,EACvB,UAAU,CACb,CAAC;QACN,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,UAAU,CAAC,eAAe,GAAG,UAAU,EAAE,qBAAqB,CAAC,CAAC;YACvE,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QAC3K,CAAC;QAED,KAAK,CAAC,cAAc,CAAC,YAAY,CAC7B,aAAa,EACb,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EACpC,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EAC3E,UAAU,CACb,CAAC;IACN,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;;OAIG,CACI,kBAAkB,GAAA;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1G,OAAO,IAAI,CAAC,gBAAgB,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAC5C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAErD,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACzC,IAAI,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,EAAE,CAAC;YAC9C,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;QACpD,CAAC;QAED,yKAAO,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACrG,IAAI,IAAI,CAAC,GAAG,gKAAC,UAAO,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,iKAAE,UAAO,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACpE,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,kDAAkD;QAChG,CAAC;QAED,IACI,IAAI,CAAC,MAAM,CAAC,2BAA2B,EAAE,IACzC,CAAC,IAAI,CAAC,eAAe,IACrB,CAAC,IAAI,CAAC,gBAAgB,IACtB,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAC3C,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,EACrD,CAAC;YACC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;2KAErD,SAAM,CAAC,aAAa,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,iKAAE,UAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YAE7G,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAEtC,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;gBAExC,IAAI,UAAU,EAAE,CAAC;oBACb,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;gBAChG,CAAC;YACL,CAAC;YAED,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClF,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;;OAGG,CACI,iBAAiB,GAAA;QACpB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO;QACX,CAAC;QAED,qBAAqB;QACrB,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;QACxC,0BAA0B;QAC1B,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,iBAAiB;QACjB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,iEAAiE;QACjE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,uBAAuB;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,wBAAwB;QACxB,IAAI,UAAU,EAAE,CAAC;YACb,2DAA2D;YAC3D,0LAA0L;YAC1L,IAAI,CAAC,IAAI,CAAC,UAAW,CAAC,UAAU,EAAE,CAAC;gBAC/B,IAAI,CAAC,UAAW,CAAC,UAAU,GAAG,EAAE,CAAC;YACrC,CAAC;YACD,KAAK,MAAM,IAAI,IAAI,UAAU,CAAE,CAAC;gBAC5B,IAAI,CAAC,UAAW,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,UAAW,CAAC,UAAU,GAAG,IAAI,CAAC;QACvC,CAAC;IACL,CAAC;IAES,yBAAyB,GAAA;QAC/B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAC5B,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;YACnC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;YACvC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;IACjC,CAAC;IAES,2BAA2B,GAAA;QACjC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAES,iBAAiB,GAAA;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,CAAE,CAAC;gBAChC,GAAG,CAAC,OAAO,EAAE,CAAC;YAClB,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACzB,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAEnC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;gBACzD,IAAK,IAAI,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAE,CAAC;oBAC7E,MAAM,CAAC,MAAM,EAAE,eAAe,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;oBAC9C,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;wBAC3B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACjD,CAAC;gBACL,CAAC;gBACD,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;oBAC3C,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBACzC,CAAC;YACL,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,CAAC;QACnD,IAAI,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,CAAC,oCAAoC,CAAC,KAAK,EAAE,CAAC;QAClD,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;IAClD,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,GAAQ,CAAA,CAAE,CAAC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEtC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,mBAAmB,CAAC;QAC/B,CAAC;QAED,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACpD,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QAC7C,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;QAChD,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACjC,mBAAmB,CAAC,OAAO,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;QACxD,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAClD,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAClE,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,4BAA4B,GAAG,IAAI,CAAC,4BAA4B,CAAC;QACrF,mBAAmB,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAC/E,mBAAmB,CAAC,gCAAgC,GAAG,IAAI,CAAC,gCAAgC,CAAC;QAC7F,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC7D,mBAAmB,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QAC3E,mBAAmB,CAAC,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,CAAC;QACnF,mBAAmB,CAAC,4BAA4B,GAAG,IAAI,CAAC,2BAA2B,CAAC;QACpF,mBAAmB,CAAC,gCAAgC,GAAG,IAAI,CAAC,2BAA2B,CAAC;QACxF,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACjE,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEvD,mBAAmB,CAAC,UAAU,GAAG,EAAE,CAAC;QACpC,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACvB,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;gBAC3E,MAAM,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;gBAE7C,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjD,CAAC;QACL,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,KAAK,CAAC,qBAA0B,EAAE,KAAY,EAAE,MAA4F,EAAA;QACtJ,MAAM,KAAK,GAAiB,KAAK,CAAC,YAAY,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC9E,MAAM,MAAM,GAAqB,qBAAqB,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3I,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,eAAe,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QACrK,MAAM,SAAS,GAAG,eAAe,CAAC,YAAY,EAAE,CAAC;QAEjD,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,qBAAqB,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;YACvF,MAAM,MAAM,GAAG,KAAK,CAAC,aAAa,CAAC,qBAAqB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;YAChF,KAAK,MAAM,IAAI,IAAI,MAAM,CAAE,CAAC;gBACxB,IAAI,CAAC,SAAS,EAAE,CAAC;oBACb,SAAS;gBACb,CAAC;gBACD,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;oBACxB,SAAS,CAAC,UAAU,GAAG,EAAE,CAAC;gBAC9B,CAAC;gBACD,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,IAAI,qBAAqB,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;YACzC,eAAe,CAAC,EAAE,GAAG,qBAAqB,CAAC,EAAE,CAAC;QAClD,CAAC;QAED,eAAe,CAAC,kBAAkB,GAAG,CAAC,CAAC,qBAAqB,CAAC,kBAAkB,CAAC;QAEhF,IAAI,qBAAqB,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC/C,eAAe,CAAC,WAAW,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,qBAAqB,CAAC,kBAAkB,EAAE,CAAC;YAC3C,eAAe,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,qBAAqB,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;YACzD,eAAe,CAAC,kBAAkB,GAAG,qBAAqB,CAAC,kBAAkB,CAAC;QAClF,CAAC;QAED,IAAI,qBAAqB,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC3C,eAAe,CAAC,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC;QACtD,CAAC;QAED,IAAI,qBAAqB,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACjD,eAAe,CAAC,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC;QAClE,CAAC;QAED,IAAI,qBAAqB,CAAC,4BAA4B,EAAE,CAAC;YACrD,eAAe,CAAC,4BAA4B,GAAG,IAAI,CAAC;QACxD,CAAC,MAAM,IAAI,qBAAqB,CAAC,yBAAyB,EAAE,CAAC;YACzD,eAAe,CAAC,yBAAyB,GAAG,IAAI,CAAC;QACrD,CAAC,MAAM,IAAI,qBAAqB,CAAC,kBAAkB,EAAE,CAAC;YAClD,eAAe,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC9C,CAAC,MAAM,IAAI,qBAAqB,CAAC,uBAAuB,EAAE,CAAC;YACvD,eAAe,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACnD,CAAC,MAAM,IAAI,qBAAqB,CAAC,2BAA2B,EAAE,CAAC;YAC3D,eAAe,CAAC,2BAA2B,GAAG,IAAI,CAAC;QACvD,CAAC,MAAM,IAAI,qBAAqB,CAAC,4BAA4B,EAAE,CAAC;YAC5D,eAAe,CAAC,4BAA4B,GAAG,IAAI,CAAC;QACxD,CAAC,MAAM,IAAI,qBAAqB,CAAC,gCAAgC,EAAE,CAAC;YAChE,eAAe,CAAC,gCAAgC,GAAG,IAAI,CAAC;QAC5D,CAAC,MAEI,IAAI,qBAAqB,CAAC,oBAAoB,EAAE,CAAC;YAClD,eAAe,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACnD,CAAC,MAAM,IAAI,qBAAqB,CAAC,wBAAwB,EAAE,CAAC;YACxD,eAAe,CAAC,2BAA2B,GAAG,IAAI,CAAC;QACvD,CAAC;QAED,IAAI,qBAAqB,CAAC,gCAAgC,KAAK,SAAS,EAAE,CAAC;YACvE,eAAe,CAAC,gCAAgC,GAAG,qBAAqB,CAAC,gCAAgC,CAAC;QAC9G,CAAC;QAED,IAAI,qBAAqB,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACvD,eAAe,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,gBAAgB,CAAC;QAC9E,CAAC;QAED,IAAI,qBAAqB,CAAC,UAAU,EAAE,CAAC;YACnC,eAAe,CAAC,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC;QAClE,CAAC;QAED,IAAI,qBAAqB,CAAC,SAAS,EAAE,CAAC;YAClC,eAAe,CAAC,SAAS,GAAG,qBAAqB,CAAC,SAAS,CAAC;QAChE,CAAC;QAED,IAAI,qBAAqB,CAAC,aAAa,EAAE,CAAC;YACtC,eAAe,CAAC,aAAa,GAAG,qBAAqB,CAAC,aAAa,CAAC;QACxE,CAAC;QAED,IAAI,qBAAqB,CAAC,aAAa,EAAE,CAAC;YACtC,eAAe,CAAC,aAAa,GAAG,qBAAqB,CAAC,aAAa,CAAC;QACxE,CAAC;QAED,IAAI,qBAAqB,CAAC,UAAU,EAAE,CAAC;YACnC,eAAe,CAAC,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC;QAClE,CAAC;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;;AA1iED;;GAEG,CACW,gBAAA,SAAS,GAAG,iBAAiB,AAApB,CAAqB;AAE5C;;;GAGG,CACW,gBAAA,SAAS,GAAG,KAAK,AAAR,CAAS;AAEhC;;GAEG,CACoB,gBAAA,WAAW,GAAG,CAAC,AAAJ,CAAK;AACvC;;;GAGG,CACoB,gBAAA,2BAA2B,GAAG,CAAC,AAAJ,CAAK;AACvD;;;GAGG,CACoB,gBAAA,sBAAsB,GAAG,CAAC,AAAJ,CAAK;AAClD;;;GAGG,CACoB,gBAAA,+BAA+B,GAAG,CAAC,AAAJ,CAAK;AAC3D;;;;GAIG,CACoB,gBAAA,gCAAgC,GAAG,CAAC,AAAJ,CAAK;AAC5D;;;;GAIG,CACoB,gBAAA,oCAAoC,GAAG,CAAC,AAAJ,CAAK;AAChE;;;;GAIG,CACoB,gBAAA,UAAU,GAAG,CAAH,AAAI,CAAC;AACtC;;;;GAIG,CACoB,gBAAA,WAAW,GAAG,CAAC,AAAJ,CAAK;AAEvC;;;;;;;GAOG,CACoB,gBAAA,YAAY,GAAG,CAAC,AAAJ,CAAK;AACxC;;;;;;;GAOG,CACoB,gBAAA,cAAc,GAAG,CAAC,AAAJ,CAAK;AAC1C;;;;;;;GAOG,CACoB,gBAAA,WAAW,GAAG,CAAH,AAAI,CAAC;AAEvC;;GAEG,CACW,gBAAA,oBAAoB,GAAG,GAAG,AAAN,CAAO;AA6mBzC;;GAEG,CACW,gBAAA,6BAA6B,GAA2B,CAAC,CAAC,EAAE,EAAE;IACxE,kKAAM,cAAA,AAAW,EAAC,+BAA+B,CAAC,CAAC;AACvD,CAAC,AAF0C,CAEzC", "debugId": null}}, {"offset": {"line": 2593, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/Shadows/cascadedShadowGenerator.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/Shadows/cascadedShadowGenerator.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Matrix, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\n\r\nimport type { IShadowLight } from \"../../Lights/shadowLight\";\r\nimport type { Effect } from \"../../Materials/effect\";\r\nimport { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\n\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { _WarnImport } from \"../../Misc/devTools\";\r\nimport { ShadowGenerator } from \"./shadowGenerator\";\r\nimport type { DirectionalLight } from \"../directionalLight\";\r\n\r\nimport { BoundingInfo } from \"../../Culling/boundingInfo\";\r\nimport type { DepthRenderer } from \"../../Rendering/depthRenderer\";\r\nimport { DepthReducer } from \"../../Misc/depthReducer\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { EngineStore } from \"../../Engines/engineStore\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\n\r\ninterface ICascade {\r\n    prevBreakDistance: number;\r\n    breakDistance: number;\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nconst UpDir = Vector3.Up();\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nconst ZeroVec = Vector3.Zero();\r\n\r\nconst Tmpv1 = new Vector3();\r\nconst Tmpv2 = new Vector3();\r\nconst TmpMatrix = new Matrix();\r\n\r\n/**\r\n * A CSM implementation allowing casting shadows on large scenes.\r\n * Documentation : https://doc.babylonjs.com/babylon101/cascadedShadows\r\n * Based on: https://github.com/TheRealMJP/Shadows and https://johanmedestrom.wordpress.com/2016/03/18/opengl-cascaded-shadow-maps/\r\n */\r\nexport class CascadedShadowGenerator extends ShadowGenerator {\r\n    private static readonly _FrustumCornersNdcSpace = [\r\n        new Vector3(-1.0, +1.0, -1.0),\r\n        new Vector3(+1.0, +1.0, -1.0),\r\n        new Vector3(+1.0, -1.0, -1.0),\r\n        new Vector3(-1.0, -1.0, -1.0),\r\n        new Vector3(-1.0, +1.0, +1.0),\r\n        new Vector3(+1.0, +1.0, +1.0),\r\n        new Vector3(+1.0, -1.0, +1.0),\r\n        new Vector3(-1.0, -1.0, +1.0),\r\n    ];\r\n\r\n    /**\r\n     * Name of the CSM class\r\n     */\r\n    public static override CLASSNAME = \"CascadedShadowGenerator\";\r\n\r\n    /**\r\n     * Defines the default number of cascades used by the CSM.\r\n     */\r\n    public static readonly DEFAULT_CASCADES_COUNT = 4;\r\n    /**\r\n     * Defines the minimum number of cascades used by the CSM.\r\n     */\r\n    public static MIN_CASCADES_COUNT = 2;\r\n    /**\r\n     * Defines the maximum number of cascades used by the CSM.\r\n     */\r\n    public static MAX_CASCADES_COUNT = 4;\r\n\r\n    protected override _validateFilter(filter: number): number {\r\n        if (filter === ShadowGenerator.FILTER_NONE || filter === ShadowGenerator.FILTER_PCF || filter === ShadowGenerator.FILTER_PCSS) {\r\n            return filter;\r\n        }\r\n\r\n        Logger.Error('Unsupported filter \"' + filter + '\"!');\r\n\r\n        return ShadowGenerator.FILTER_NONE;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the actual darkness of the soft shadows while using PCSS filtering (value between 0. and 1.)\r\n     */\r\n    public penumbraDarkness: number;\r\n\r\n    private _numCascades: number;\r\n\r\n    /**\r\n     * Gets or set the number of cascades used by the CSM.\r\n     */\r\n    public get numCascades(): number {\r\n        return this._numCascades;\r\n    }\r\n\r\n    public set numCascades(value: number) {\r\n        value = Math.min(Math.max(value, CascadedShadowGenerator.MIN_CASCADES_COUNT), CascadedShadowGenerator.MAX_CASCADES_COUNT);\r\n        if (value === this._numCascades) {\r\n            return;\r\n        }\r\n\r\n        this._numCascades = value;\r\n        this.recreateShadowMap();\r\n        this._recreateSceneUBOs();\r\n    }\r\n\r\n    /**\r\n     * Sets this to true if you want that the edges of the shadows don't \"swimm\" / \"shimmer\" when rotating the camera.\r\n     * The trade off is that you lose some precision in the shadow rendering when enabling this setting.\r\n     */\r\n    public stabilizeCascades: boolean;\r\n\r\n    private _freezeShadowCastersBoundingInfo: boolean;\r\n    private _freezeShadowCastersBoundingInfoObservable: Nullable<Observer<Scene>>;\r\n\r\n    /**\r\n     * Enables or disables the shadow casters bounding info computation.\r\n     * If your shadow casters don't move, you can disable this feature.\r\n     * If it is enabled, the bounding box computation is done every frame.\r\n     */\r\n    public get freezeShadowCastersBoundingInfo(): boolean {\r\n        return this._freezeShadowCastersBoundingInfo;\r\n    }\r\n\r\n    public set freezeShadowCastersBoundingInfo(freeze: boolean) {\r\n        if (this._freezeShadowCastersBoundingInfoObservable && freeze) {\r\n            this._scene.onBeforeRenderObservable.remove(this._freezeShadowCastersBoundingInfoObservable);\r\n            this._freezeShadowCastersBoundingInfoObservable = null;\r\n        }\r\n\r\n        if (!this._freezeShadowCastersBoundingInfoObservable && !freeze) {\r\n            this._freezeShadowCastersBoundingInfoObservable = this._scene.onBeforeRenderObservable.add(() => this._computeShadowCastersBoundingInfo());\r\n        }\r\n\r\n        this._freezeShadowCastersBoundingInfo = freeze;\r\n\r\n        if (freeze) {\r\n            this._computeShadowCastersBoundingInfo();\r\n        }\r\n    }\r\n\r\n    private _scbiMin: Vector3;\r\n    private _scbiMax: Vector3;\r\n\r\n    protected _computeShadowCastersBoundingInfo(): void {\r\n        this._scbiMin.copyFromFloats(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        this._scbiMax.copyFromFloats(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\r\n\r\n        if (this._shadowMap && this._shadowMap.renderList) {\r\n            const renderList = this._shadowMap.renderList;\r\n            for (let meshIndex = 0; meshIndex < renderList.length; meshIndex++) {\r\n                const mesh = renderList[meshIndex];\r\n\r\n                if (!mesh) {\r\n                    continue;\r\n                }\r\n\r\n                const boundingInfo = mesh.getBoundingInfo(),\r\n                    boundingBox = boundingInfo.boundingBox;\r\n\r\n                this._scbiMin.minimizeInPlace(boundingBox.minimumWorld);\r\n                this._scbiMax.maximizeInPlace(boundingBox.maximumWorld);\r\n            }\r\n        }\r\n\r\n        this._shadowCastersBoundingInfo.reConstruct(this._scbiMin, this._scbiMax);\r\n    }\r\n\r\n    protected _shadowCastersBoundingInfo: BoundingInfo;\r\n\r\n    /**\r\n     * Gets or sets the shadow casters bounding info.\r\n     * If you provide your own shadow casters bounding info, first enable freezeShadowCastersBoundingInfo\r\n     * so that the system won't overwrite the bounds you provide\r\n     */\r\n    public get shadowCastersBoundingInfo(): BoundingInfo {\r\n        return this._shadowCastersBoundingInfo;\r\n    }\r\n\r\n    public set shadowCastersBoundingInfo(boundingInfo: BoundingInfo) {\r\n        this._shadowCastersBoundingInfo = boundingInfo;\r\n    }\r\n\r\n    protected _breaksAreDirty: boolean;\r\n\r\n    protected _minDistance: number;\r\n    protected _maxDistance: number;\r\n\r\n    /**\r\n     * Sets the minimal and maximal distances to use when computing the cascade breaks.\r\n     *\r\n     * The values of min / max are typically the depth zmin and zmax values of your scene, for a given frame.\r\n     * If you don't know these values, simply leave them to their defaults and don't call this function.\r\n     * @param min minimal distance for the breaks (default to 0.)\r\n     * @param max maximal distance for the breaks (default to 1.)\r\n     */\r\n    public setMinMaxDistance(min: number, max: number): void {\r\n        if (this._minDistance === min && this._maxDistance === max) {\r\n            return;\r\n        }\r\n\r\n        if (min > max) {\r\n            min = 0;\r\n            max = 1;\r\n        }\r\n\r\n        if (min < 0) {\r\n            min = 0;\r\n        }\r\n\r\n        if (max > 1) {\r\n            max = 1;\r\n        }\r\n\r\n        this._minDistance = min;\r\n        this._maxDistance = max;\r\n        this._breaksAreDirty = true;\r\n    }\r\n\r\n    /** Gets the minimal distance used in the cascade break computation */\r\n    public get minDistance(): number {\r\n        return this._minDistance;\r\n    }\r\n\r\n    /** Gets the maximal distance used in the cascade break computation */\r\n    public get maxDistance(): number {\r\n        return this._maxDistance;\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of that object\r\n     * @returns \"CascadedShadowGenerator\"\r\n     */\r\n    public override getClassName(): string {\r\n        return CascadedShadowGenerator.CLASSNAME;\r\n    }\r\n\r\n    private _cascadeMinExtents: Array<Vector3>;\r\n    private _cascadeMaxExtents: Array<Vector3>;\r\n\r\n    /**\r\n     * Gets a cascade minimum extents\r\n     * @param cascadeIndex index of the cascade\r\n     * @returns the minimum cascade extents\r\n     */\r\n    public getCascadeMinExtents(cascadeIndex: number): Nullable<Vector3> {\r\n        return cascadeIndex >= 0 && cascadeIndex < this._numCascades ? this._cascadeMinExtents[cascadeIndex] : null;\r\n    }\r\n\r\n    /**\r\n     * Gets a cascade maximum extents\r\n     * @param cascadeIndex index of the cascade\r\n     * @returns the maximum cascade extents\r\n     */\r\n    public getCascadeMaxExtents(cascadeIndex: number): Nullable<Vector3> {\r\n        return cascadeIndex >= 0 && cascadeIndex < this._numCascades ? this._cascadeMaxExtents[cascadeIndex] : null;\r\n    }\r\n\r\n    private _cascades: Array<ICascade>;\r\n    private _currentLayer: number;\r\n    private _viewSpaceFrustumsZ: Array<number>;\r\n    private _viewMatrices: Array<Matrix>;\r\n    private _projectionMatrices: Array<Matrix>;\r\n    private _transformMatrices: Array<Matrix>;\r\n    private _transformMatricesAsArray: Float32Array;\r\n    private _frustumLengths: Array<number>;\r\n    private _lightSizeUVCorrection: Array<number>;\r\n    private _depthCorrection: Array<number>;\r\n    private _frustumCornersWorldSpace: Array<Array<Vector3>>;\r\n    private _frustumCenter: Array<Vector3>;\r\n    private _shadowCameraPos: Array<Vector3>;\r\n\r\n    private _shadowMaxZ: number;\r\n    /**\r\n     * Gets the shadow max z distance. It's the limit beyond which shadows are not displayed.\r\n     * It defaults to camera.maxZ\r\n     */\r\n    public get shadowMaxZ(): number {\r\n        if (!this._getCamera()) {\r\n            return 0;\r\n        }\r\n        return this._shadowMaxZ;\r\n    }\r\n    /**\r\n     * Sets the shadow max z distance.\r\n     */\r\n    public set shadowMaxZ(value: number) {\r\n        const camera = this._getCamera();\r\n        if (!camera) {\r\n            this._shadowMaxZ = value;\r\n            return;\r\n        }\r\n        if (this._shadowMaxZ === value || value < camera.minZ || (value > camera.maxZ && camera.maxZ !== 0)) {\r\n            return;\r\n        }\r\n        this._shadowMaxZ = value;\r\n        this._light._markMeshesAsLightDirty();\r\n        this._breaksAreDirty = true;\r\n    }\r\n\r\n    protected _debug: boolean;\r\n\r\n    /**\r\n     * Gets or sets the debug flag.\r\n     * When enabled, the cascades are materialized by different colors on the screen.\r\n     */\r\n    public get debug(): boolean {\r\n        return this._debug;\r\n    }\r\n\r\n    public set debug(dbg: boolean) {\r\n        this._debug = dbg;\r\n        this._light._markMeshesAsLightDirty();\r\n    }\r\n\r\n    private _depthClamp: boolean;\r\n\r\n    /**\r\n     * Gets or sets the depth clamping value.\r\n     *\r\n     * When enabled, it improves the shadow quality because the near z plane of the light frustum don't need to be adjusted\r\n     * to account for the shadow casters far away.\r\n     *\r\n     * Note that this property is incompatible with PCSS filtering, so it won't be used in that case.\r\n     */\r\n    public get depthClamp(): boolean {\r\n        return this._depthClamp;\r\n    }\r\n\r\n    public set depthClamp(value: boolean) {\r\n        this._depthClamp = value;\r\n    }\r\n\r\n    private _cascadeBlendPercentage: number;\r\n\r\n    /**\r\n     * Gets or sets the percentage of blending between two cascades (value between 0. and 1.).\r\n     * It defaults to 0.1 (10% blending).\r\n     */\r\n    public get cascadeBlendPercentage(): number {\r\n        return this._cascadeBlendPercentage;\r\n    }\r\n\r\n    public set cascadeBlendPercentage(value: number) {\r\n        this._cascadeBlendPercentage = value;\r\n        this._light._markMeshesAsLightDirty();\r\n    }\r\n\r\n    private _lambda: number;\r\n\r\n    /**\r\n     * Gets or set the lambda parameter.\r\n     * This parameter is used to split the camera frustum and create the cascades.\r\n     * It's a value between 0. and 1.: If 0, the split is a uniform split of the frustum, if 1 it is a logarithmic split.\r\n     * For all values in-between, it's a linear combination of the uniform and logarithm split algorithm.\r\n     */\r\n    public get lambda(): number {\r\n        return this._lambda;\r\n    }\r\n\r\n    public set lambda(value: number) {\r\n        const lambda = Math.min(Math.max(value, 0), 1);\r\n        if (this._lambda == lambda) {\r\n            return;\r\n        }\r\n        this._lambda = lambda;\r\n        this._breaksAreDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Gets the view matrix corresponding to a given cascade\r\n     * @param cascadeNum cascade to retrieve the view matrix from\r\n     * @returns the cascade view matrix\r\n     */\r\n    public getCascadeViewMatrix(cascadeNum: number): Nullable<Matrix> {\r\n        return cascadeNum >= 0 && cascadeNum < this._numCascades ? this._viewMatrices[cascadeNum] : null;\r\n    }\r\n\r\n    /**\r\n     * Gets the projection matrix corresponding to a given cascade\r\n     * @param cascadeNum cascade to retrieve the projection matrix from\r\n     * @returns the cascade projection matrix\r\n     */\r\n    public getCascadeProjectionMatrix(cascadeNum: number): Nullable<Matrix> {\r\n        return cascadeNum >= 0 && cascadeNum < this._numCascades ? this._projectionMatrices[cascadeNum] : null;\r\n    }\r\n\r\n    /**\r\n     * Gets the transformation matrix corresponding to a given cascade\r\n     * @param cascadeNum cascade to retrieve the transformation matrix from\r\n     * @returns the cascade transformation matrix\r\n     */\r\n    public getCascadeTransformMatrix(cascadeNum: number): Nullable<Matrix> {\r\n        return cascadeNum >= 0 && cascadeNum < this._numCascades ? this._transformMatrices[cascadeNum] : null;\r\n    }\r\n\r\n    private _depthRenderer: Nullable<DepthRenderer>;\r\n    /**\r\n     * Sets the depth renderer to use when autoCalcDepthBounds is enabled.\r\n     *\r\n     * Note that if no depth renderer is set, a new one will be automatically created internally when necessary.\r\n     *\r\n     * You should call this function if you already have a depth renderer enabled in your scene, to avoid\r\n     * doing multiple depth rendering each frame. If you provide your own depth renderer, make sure it stores linear depth!\r\n     * @param depthRenderer The depth renderer to use when autoCalcDepthBounds is enabled. If you pass null or don't call this function at all, a depth renderer will be automatically created\r\n     */\r\n    public setDepthRenderer(depthRenderer: Nullable<DepthRenderer>): void {\r\n        this._depthRenderer = depthRenderer;\r\n\r\n        if (this._depthReducer) {\r\n            this._depthReducer.setDepthRenderer(this._depthRenderer);\r\n        }\r\n    }\r\n\r\n    private _depthReducer: Nullable<DepthReducer>;\r\n    private _autoCalcDepthBounds: boolean;\r\n\r\n    /**\r\n     * Gets or sets the autoCalcDepthBounds property.\r\n     *\r\n     * When enabled, a depth rendering pass is first performed (with an internally created depth renderer or with the one\r\n     * you provide by calling setDepthRenderer). Then, a min/max reducing is applied on the depth map to compute the\r\n     * minimal and maximal depth of the map and those values are used as inputs for the setMinMaxDistance() function.\r\n     * It can greatly enhance the shadow quality, at the expense of more GPU works.\r\n     * When using this option, you should increase the value of the lambda parameter, and even set it to 1 for best results.\r\n     */\r\n    public get autoCalcDepthBounds(): boolean {\r\n        return this._autoCalcDepthBounds;\r\n    }\r\n\r\n    public set autoCalcDepthBounds(value: boolean) {\r\n        const camera = this._getCamera();\r\n\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        this._autoCalcDepthBounds = value;\r\n\r\n        if (!value) {\r\n            if (this._depthReducer) {\r\n                this._depthReducer.deactivate();\r\n            }\r\n            this.setMinMaxDistance(0, 1);\r\n            return;\r\n        }\r\n\r\n        if (!this._depthReducer) {\r\n            this._depthReducer = new DepthReducer(camera);\r\n            this._depthReducer.onAfterReductionPerformed.add((minmax: { min: number; max: number }) => {\r\n                let min = minmax.min,\r\n                    max = minmax.max;\r\n                if (min >= max) {\r\n                    min = 0;\r\n                    max = 1;\r\n                }\r\n                if (min != this._minDistance || max != this._maxDistance) {\r\n                    this.setMinMaxDistance(min, max);\r\n                }\r\n            });\r\n            this._depthReducer.setDepthRenderer(this._depthRenderer);\r\n        }\r\n\r\n        this._depthReducer.activate();\r\n    }\r\n\r\n    /**\r\n     * Defines the refresh rate of the min/max computation used when autoCalcDepthBounds is set to true\r\n     * Use 0 to compute just once, 1 to compute on every frame, 2 to compute every two frames and so on...\r\n     * Note that if you provided your own depth renderer through a call to setDepthRenderer, you are responsible\r\n     * for setting the refresh rate on the renderer yourself!\r\n     */\r\n    public get autoCalcDepthBoundsRefreshRate(): number {\r\n        return this._depthReducer?.depthRenderer?.getDepthMap().refreshRate ?? -1;\r\n    }\r\n\r\n    public set autoCalcDepthBoundsRefreshRate(value: number) {\r\n        if (this._depthReducer?.depthRenderer) {\r\n            this._depthReducer.depthRenderer.getDepthMap().refreshRate = value;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Create the cascade breaks according to the lambda, shadowMaxZ and min/max distance properties, as well as the camera near and far planes.\r\n     * This function is automatically called when updating lambda, shadowMaxZ and min/max distances, however you should call it yourself if\r\n     * you change the camera near/far planes!\r\n     */\r\n    public splitFrustum(): void {\r\n        this._breaksAreDirty = true;\r\n    }\r\n\r\n    private _splitFrustum(): void {\r\n        const camera = this._getCamera();\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        const near = camera.minZ,\r\n            far = camera.maxZ || this._shadowMaxZ, // account for infinite far plane (ie. maxZ = 0)\r\n            cameraRange = far - near,\r\n            minDistance = this._minDistance,\r\n            maxDistance = this._shadowMaxZ < far && this._shadowMaxZ >= near ? Math.min((this._shadowMaxZ - near) / (far - near), this._maxDistance) : this._maxDistance;\r\n\r\n        const minZ = near + minDistance * cameraRange,\r\n            maxZ = near + maxDistance * cameraRange;\r\n\r\n        const range = maxZ - minZ,\r\n            ratio = maxZ / minZ;\r\n\r\n        for (let cascadeIndex = 0; cascadeIndex < this._cascades.length; ++cascadeIndex) {\r\n            const p = (cascadeIndex + 1) / this._numCascades,\r\n                log = minZ * ratio ** p,\r\n                uniform = minZ + range * p;\r\n\r\n            const d = this._lambda * (log - uniform) + uniform;\r\n\r\n            this._cascades[cascadeIndex].prevBreakDistance = cascadeIndex === 0 ? minDistance : this._cascades[cascadeIndex - 1].breakDistance;\r\n            this._cascades[cascadeIndex].breakDistance = (d - near) / cameraRange;\r\n\r\n            this._viewSpaceFrustumsZ[cascadeIndex] = d;\r\n            this._frustumLengths[cascadeIndex] = (this._cascades[cascadeIndex].breakDistance - this._cascades[cascadeIndex].prevBreakDistance) * cameraRange;\r\n        }\r\n\r\n        this._breaksAreDirty = false;\r\n    }\r\n\r\n    private _computeMatrices(): void {\r\n        const scene = this._scene;\r\n\r\n        const camera = this._getCamera();\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        Vector3.NormalizeToRef(this._light.getShadowDirection(0), this._lightDirection);\r\n        if (Math.abs(Vector3.Dot(this._lightDirection, Vector3.Up())) === 1.0) {\r\n            this._lightDirection.z = 0.0000000000001; // Required to avoid perfectly perpendicular light\r\n        }\r\n\r\n        this._cachedDirection.copyFrom(this._lightDirection);\r\n\r\n        const useReverseDepthBuffer = scene.getEngine().useReverseDepthBuffer;\r\n\r\n        for (let cascadeIndex = 0; cascadeIndex < this._numCascades; ++cascadeIndex) {\r\n            this._computeFrustumInWorldSpace(cascadeIndex);\r\n            this._computeCascadeFrustum(cascadeIndex);\r\n\r\n            this._cascadeMaxExtents[cascadeIndex].subtractToRef(this._cascadeMinExtents[cascadeIndex], Tmpv1); // tmpv1 = cascadeExtents\r\n\r\n            // Get position of the shadow camera\r\n            this._frustumCenter[cascadeIndex].addToRef(this._lightDirection.scale(this._cascadeMinExtents[cascadeIndex].z), this._shadowCameraPos[cascadeIndex]);\r\n\r\n            // Come up with a new orthographic camera for the shadow caster\r\n            Matrix.LookAtLHToRef(this._shadowCameraPos[cascadeIndex], this._frustumCenter[cascadeIndex], UpDir, this._viewMatrices[cascadeIndex]);\r\n\r\n            // Z extents of the current cascade, in cascade view coordinate system\r\n            let viewMinZ = 0,\r\n                viewMaxZ = Tmpv1.z;\r\n\r\n            // Try to tighten minZ and maxZ based on the bounding box of the shadow casters\r\n            const boundingInfo = this._shadowCastersBoundingInfo;\r\n\r\n            boundingInfo.update(this._viewMatrices[cascadeIndex]);\r\n            // Note that after the call to update, the boundingInfo properties that are identified as \"world\" coordinates are in fact view coordinates for the current cascade!\r\n            // This is because the boundingInfo properties that are identifed as \"local\" are in fact world coordinates (see _computeShadowCastersBoundingInfo()), and we multiply them by the current cascade view matrix when we call update.\r\n\r\n            const castersViewMinZ = boundingInfo.boundingBox.minimumWorld.z;\r\n            const castersViewMaxZ = boundingInfo.boundingBox.maximumWorld.z;\r\n\r\n            if (castersViewMinZ > viewMaxZ) {\r\n                // Do nothing, keep the current z extents.\r\n                // All the casters are too far from the light to have an impact on the current cascade.\r\n                // Possible optimization: skip the rendering of the shadow map for this cascade, as all the casters will be clipped by the GPU anyway.\r\n            } else {\r\n                if (!this._depthClamp || this.filter === ShadowGenerator.FILTER_PCSS) {\r\n                    // If we don't use depth clamping, we must define minZ so that all shadow casters are in the cascade frustum\r\n                    viewMinZ = Math.min(viewMinZ, castersViewMinZ);\r\n\r\n                    if (this.filter !== ShadowGenerator.FILTER_PCSS) {\r\n                        // We do not need the actual distance between the currently shaded pixel and the occluder when generating shadows, so we can lower the far plane to increase the accuracy of the shadow map.\r\n                        viewMaxZ = Math.min(viewMaxZ, castersViewMaxZ);\r\n                    }\r\n                } else {\r\n                    // If we use depth clamping (but not PCSS!), we can adjust minZ/maxZ to reduce the range [minZ, maxZ] (and obtain additional precision in the shadow map)\r\n                    viewMaxZ = Math.min(viewMaxZ, castersViewMaxZ);\r\n\r\n                    // Thanks to depth clamping, casters won't be Z clipped even if they fall outside the [-1,1] range, so we can move the near plane to 0 if castersViewMinZ < 0.\r\n                    // We will generate negative Z values in the shadow map, but that's okay (they will be clamped to the 0..1 range anyway), except in PCSS case\r\n                    // where we need the actual distance between the currently shader pixel and the occluder: that's why we don't use depth clamping in PCSS case.\r\n                    viewMinZ = Math.max(viewMinZ, castersViewMinZ);\r\n\r\n                    // If all the casters are behind the near plane of the cascade, minZ = 0 due to the previous line, and maxZ < 0 at this point.\r\n                    // We need to make sure that maxZ > minZ, so in this case we set maxZ a little higher than minZ. As we are using depth clamping, the casters won't be Z clipped, so we just need to make sure that we have a valid Z range for the cascade.\r\n                    // Having a 0 range is not ok, due to undefined behavior in the calculation in this case.\r\n                    viewMaxZ = Math.max(viewMinZ + 1.0, viewMaxZ);\r\n                }\r\n            }\r\n\r\n            Matrix.OrthoOffCenterLHToRef(\r\n                this._cascadeMinExtents[cascadeIndex].x,\r\n                this._cascadeMaxExtents[cascadeIndex].x,\r\n                this._cascadeMinExtents[cascadeIndex].y,\r\n                this._cascadeMaxExtents[cascadeIndex].y,\r\n                useReverseDepthBuffer ? viewMaxZ : viewMinZ,\r\n                useReverseDepthBuffer ? viewMinZ : viewMaxZ,\r\n                this._projectionMatrices[cascadeIndex],\r\n                scene.getEngine().isNDCHalfZRange\r\n            );\r\n\r\n            this._cascadeMinExtents[cascadeIndex].z = viewMinZ;\r\n            this._cascadeMaxExtents[cascadeIndex].z = viewMaxZ;\r\n\r\n            this._viewMatrices[cascadeIndex].multiplyToRef(this._projectionMatrices[cascadeIndex], this._transformMatrices[cascadeIndex]);\r\n\r\n            // Create the rounding matrix, by projecting the world-space origin and determining\r\n            // the fractional offset in texel space\r\n            Vector3.TransformCoordinatesToRef(ZeroVec, this._transformMatrices[cascadeIndex], Tmpv1); // tmpv1 = shadowOrigin\r\n            Tmpv1.scaleInPlace(this._mapSize / 2);\r\n\r\n            Tmpv2.copyFromFloats(Math.round(Tmpv1.x), Math.round(Tmpv1.y), Math.round(Tmpv1.z)); // tmpv2 = roundedOrigin\r\n            Tmpv2.subtractInPlace(Tmpv1).scaleInPlace(2 / this._mapSize); // tmpv2 = roundOffset\r\n\r\n            Matrix.TranslationToRef(Tmpv2.x, Tmpv2.y, 0.0, TmpMatrix);\r\n\r\n            this._projectionMatrices[cascadeIndex].multiplyToRef(TmpMatrix, this._projectionMatrices[cascadeIndex]);\r\n            this._viewMatrices[cascadeIndex].multiplyToRef(this._projectionMatrices[cascadeIndex], this._transformMatrices[cascadeIndex]);\r\n\r\n            this._transformMatrices[cascadeIndex].copyToArray(this._transformMatricesAsArray, cascadeIndex * 16);\r\n        }\r\n    }\r\n\r\n    // Get the 8 points of the view frustum in world space\r\n    private _computeFrustumInWorldSpace(cascadeIndex: number): void {\r\n        const camera = this._getCamera();\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        const prevSplitDist = this._cascades[cascadeIndex].prevBreakDistance,\r\n            splitDist = this._cascades[cascadeIndex].breakDistance;\r\n\r\n        const isNDCHalfZRange = this._scene.getEngine().isNDCHalfZRange;\r\n\r\n        camera.getViewMatrix(); // make sure the transformation matrix we get when calling 'getTransformationMatrix()' is calculated with an up to date view matrix\r\n\r\n        const cameraInfiniteFarPlane = camera.maxZ === 0;\r\n        const saveCameraMaxZ = camera.maxZ;\r\n\r\n        if (cameraInfiniteFarPlane) {\r\n            camera.maxZ = this._shadowMaxZ;\r\n            camera.getProjectionMatrix(true);\r\n        }\r\n\r\n        const invViewProj = Matrix.Invert(camera.getTransformationMatrix());\r\n\r\n        if (cameraInfiniteFarPlane) {\r\n            camera.maxZ = saveCameraMaxZ;\r\n            camera.getProjectionMatrix(true);\r\n        }\r\n\r\n        const cornerIndexOffset = this._scene.getEngine().useReverseDepthBuffer ? 4 : 0;\r\n        for (let cornerIndex = 0; cornerIndex < CascadedShadowGenerator._FrustumCornersNdcSpace.length; ++cornerIndex) {\r\n            Tmpv1.copyFrom(CascadedShadowGenerator._FrustumCornersNdcSpace[(cornerIndex + cornerIndexOffset) % CascadedShadowGenerator._FrustumCornersNdcSpace.length]);\r\n            if (isNDCHalfZRange && Tmpv1.z === -1) {\r\n                Tmpv1.z = 0;\r\n            }\r\n            Vector3.TransformCoordinatesToRef(Tmpv1, invViewProj, this._frustumCornersWorldSpace[cascadeIndex][cornerIndex]);\r\n        }\r\n\r\n        // Get the corners of the current cascade slice of the view frustum\r\n        for (let cornerIndex = 0; cornerIndex < CascadedShadowGenerator._FrustumCornersNdcSpace.length / 2; ++cornerIndex) {\r\n            Tmpv1.copyFrom(this._frustumCornersWorldSpace[cascadeIndex][cornerIndex + 4]).subtractInPlace(this._frustumCornersWorldSpace[cascadeIndex][cornerIndex]);\r\n            Tmpv2.copyFrom(Tmpv1).scaleInPlace(prevSplitDist); // near corner ray\r\n            Tmpv1.scaleInPlace(splitDist); // far corner ray\r\n\r\n            Tmpv1.addInPlace(this._frustumCornersWorldSpace[cascadeIndex][cornerIndex]);\r\n\r\n            this._frustumCornersWorldSpace[cascadeIndex][cornerIndex + 4].copyFrom(Tmpv1);\r\n            this._frustumCornersWorldSpace[cascadeIndex][cornerIndex].addInPlace(Tmpv2);\r\n        }\r\n    }\r\n\r\n    private _computeCascadeFrustum(cascadeIndex: number): void {\r\n        this._cascadeMinExtents[cascadeIndex].copyFromFloats(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        this._cascadeMaxExtents[cascadeIndex].copyFromFloats(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\r\n        this._frustumCenter[cascadeIndex].copyFromFloats(0, 0, 0);\r\n\r\n        const camera = this._getCamera();\r\n\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        // Calculate the centroid of the view frustum slice\r\n        for (let cornerIndex = 0; cornerIndex < this._frustumCornersWorldSpace[cascadeIndex].length; ++cornerIndex) {\r\n            this._frustumCenter[cascadeIndex].addInPlace(this._frustumCornersWorldSpace[cascadeIndex][cornerIndex]);\r\n        }\r\n\r\n        this._frustumCenter[cascadeIndex].scaleInPlace(1 / this._frustumCornersWorldSpace[cascadeIndex].length);\r\n\r\n        if (this.stabilizeCascades) {\r\n            // Calculate the radius of a bounding sphere surrounding the frustum corners\r\n            let sphereRadius = 0;\r\n            for (let cornerIndex = 0; cornerIndex < this._frustumCornersWorldSpace[cascadeIndex].length; ++cornerIndex) {\r\n                const dist = this._frustumCornersWorldSpace[cascadeIndex][cornerIndex].subtractToRef(this._frustumCenter[cascadeIndex], Tmpv1).length();\r\n                sphereRadius = Math.max(sphereRadius, dist);\r\n            }\r\n\r\n            sphereRadius = Math.ceil(sphereRadius * 16) / 16;\r\n\r\n            this._cascadeMaxExtents[cascadeIndex].copyFromFloats(sphereRadius, sphereRadius, sphereRadius);\r\n            this._cascadeMinExtents[cascadeIndex].copyFromFloats(-sphereRadius, -sphereRadius, -sphereRadius);\r\n        } else {\r\n            // Create a temporary view matrix for the light\r\n            const lightCameraPos = this._frustumCenter[cascadeIndex];\r\n\r\n            this._frustumCenter[cascadeIndex].addToRef(this._lightDirection, Tmpv1); // tmpv1 = look at\r\n\r\n            Matrix.LookAtLHToRef(lightCameraPos, Tmpv1, UpDir, TmpMatrix); // matrix = lightView\r\n\r\n            // Calculate an AABB around the frustum corners\r\n            for (let cornerIndex = 0; cornerIndex < this._frustumCornersWorldSpace[cascadeIndex].length; ++cornerIndex) {\r\n                Vector3.TransformCoordinatesToRef(this._frustumCornersWorldSpace[cascadeIndex][cornerIndex], TmpMatrix, Tmpv1);\r\n\r\n                this._cascadeMinExtents[cascadeIndex].minimizeInPlace(Tmpv1);\r\n                this._cascadeMaxExtents[cascadeIndex].maximizeInPlace(Tmpv1);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _recreateSceneUBOs(): void {\r\n        this._disposeSceneUBOs();\r\n        if (this._sceneUBOs) {\r\n            for (let i = 0; i < this._numCascades; ++i) {\r\n                this._sceneUBOs.push(this._scene.createSceneUniformBuffer(`Scene for CSM Shadow Generator (light \"${this._light.name}\" cascade #${i})`));\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     *  Support test.\r\n     */\r\n    public static get IsSupported(): boolean {\r\n        const engine = EngineStore.LastCreatedEngine;\r\n        if (!engine) {\r\n            return false;\r\n        }\r\n        return engine._features.supportCSM;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static override _SceneComponentInitialization: (scene: Scene) => void = (_) => {\r\n        throw _WarnImport(\"ShadowGeneratorSceneComponent\");\r\n    };\r\n\r\n    /**\r\n     * Creates a Cascaded Shadow Generator object.\r\n     * A ShadowGenerator is the required tool to use the shadows.\r\n     * Each directional light casting shadows needs to use its own ShadowGenerator.\r\n     * Documentation : https://doc.babylonjs.com/babylon101/cascadedShadows\r\n     * @param mapSize The size of the texture what stores the shadows. Example : 1024.\r\n     * @param light The directional light object generating the shadows.\r\n     * @param usefulFloatFirst By default the generator will try to use half float textures but if you need precision (for self shadowing for instance), you can use this option to enforce full float texture.\r\n     * @param camera Camera associated with this shadow generator (default: null). If null, takes the scene active camera at the time we need to access it\r\n     * @param useRedTextureType Forces the generator to use a Red instead of a RGBA type for the shadow map texture format (default: true)\r\n     */\r\n    constructor(mapSize: number, light: DirectionalLight, usefulFloatFirst?: boolean, camera?: Nullable<Camera>, useRedTextureType = true) {\r\n        if (!CascadedShadowGenerator.IsSupported) {\r\n            Logger.Error(\"CascadedShadowMap is not supported by the current engine.\");\r\n            return;\r\n        }\r\n\r\n        super(mapSize, light, usefulFloatFirst, camera, useRedTextureType);\r\n\r\n        this.usePercentageCloserFiltering = true;\r\n    }\r\n\r\n    protected override _initializeGenerator(): void {\r\n        this.penumbraDarkness = this.penumbraDarkness ?? 1.0;\r\n        this._numCascades = this._numCascades ?? CascadedShadowGenerator.DEFAULT_CASCADES_COUNT;\r\n        this.stabilizeCascades = this.stabilizeCascades ?? false;\r\n        this._freezeShadowCastersBoundingInfoObservable = this._freezeShadowCastersBoundingInfoObservable ?? null;\r\n        this.freezeShadowCastersBoundingInfo = this.freezeShadowCastersBoundingInfo ?? false;\r\n        this._scbiMin = this._scbiMin ?? new Vector3(0, 0, 0);\r\n        this._scbiMax = this._scbiMax ?? new Vector3(0, 0, 0);\r\n        this._shadowCastersBoundingInfo = this._shadowCastersBoundingInfo ?? new BoundingInfo(new Vector3(0, 0, 0), new Vector3(0, 0, 0));\r\n        this._breaksAreDirty = this._breaksAreDirty ?? true;\r\n        this._minDistance = this._minDistance ?? 0;\r\n        this._maxDistance = this._maxDistance ?? 1;\r\n        this._currentLayer = this._currentLayer ?? 0;\r\n        this._shadowMaxZ = this._shadowMaxZ ?? this._getCamera()?.maxZ ?? 10000;\r\n        this._debug = this._debug ?? false;\r\n        this._depthClamp = this._depthClamp ?? true;\r\n        this._cascadeBlendPercentage = this._cascadeBlendPercentage ?? 0.1;\r\n        this._lambda = this._lambda ?? 0.5;\r\n        this._autoCalcDepthBounds = this._autoCalcDepthBounds ?? false;\r\n\r\n        this._recreateSceneUBOs();\r\n\r\n        super._initializeGenerator();\r\n    }\r\n\r\n    protected override _createTargetRenderTexture(): void {\r\n        const engine = this._scene.getEngine();\r\n        const size = { width: this._mapSize, height: this._mapSize, layers: this.numCascades };\r\n        this._shadowMap = new RenderTargetTexture(\r\n            this._light.name + \"_CSMShadowMap\",\r\n            size,\r\n            this._scene,\r\n            false,\r\n            true,\r\n            this._textureType,\r\n            false,\r\n            undefined,\r\n            false,\r\n            false,\r\n            undefined,\r\n            this._useRedTextureType ? Constants.TEXTUREFORMAT_RED : Constants.TEXTUREFORMAT_RGBA\r\n        );\r\n        this._shadowMap.createDepthStencilTexture(\r\n            engine.useReverseDepthBuffer ? Constants.GREATER : Constants.LESS,\r\n            true,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            `DepthStencilForCSMShadowGenerator-${this._light.name}`\r\n        );\r\n        this._shadowMap.noPrePassRenderer = true;\r\n    }\r\n\r\n    protected override _initializeShadowMap(): void {\r\n        super._initializeShadowMap();\r\n\r\n        if (this._shadowMap === null) {\r\n            return;\r\n        }\r\n\r\n        this._transformMatricesAsArray = new Float32Array(this._numCascades * 16);\r\n        this._viewSpaceFrustumsZ = new Array(this._numCascades);\r\n        this._frustumLengths = new Array(this._numCascades);\r\n        this._lightSizeUVCorrection = new Array(this._numCascades * 2);\r\n        this._depthCorrection = new Array(this._numCascades);\r\n\r\n        this._cascades = [];\r\n        this._viewMatrices = [];\r\n        this._projectionMatrices = [];\r\n        this._transformMatrices = [];\r\n        this._cascadeMinExtents = [];\r\n        this._cascadeMaxExtents = [];\r\n        this._frustumCenter = [];\r\n        this._shadowCameraPos = [];\r\n        this._frustumCornersWorldSpace = [];\r\n\r\n        for (let cascadeIndex = 0; cascadeIndex < this._numCascades; ++cascadeIndex) {\r\n            this._cascades[cascadeIndex] = {\r\n                prevBreakDistance: 0,\r\n                breakDistance: 0,\r\n            };\r\n\r\n            this._viewMatrices[cascadeIndex] = Matrix.Zero();\r\n            this._projectionMatrices[cascadeIndex] = Matrix.Zero();\r\n            this._transformMatrices[cascadeIndex] = Matrix.Zero();\r\n            this._cascadeMinExtents[cascadeIndex] = new Vector3();\r\n            this._cascadeMaxExtents[cascadeIndex] = new Vector3();\r\n            this._frustumCenter[cascadeIndex] = new Vector3();\r\n            this._shadowCameraPos[cascadeIndex] = new Vector3();\r\n            this._frustumCornersWorldSpace[cascadeIndex] = new Array(CascadedShadowGenerator._FrustumCornersNdcSpace.length);\r\n\r\n            for (let i = 0; i < CascadedShadowGenerator._FrustumCornersNdcSpace.length; ++i) {\r\n                this._frustumCornersWorldSpace[cascadeIndex][i] = new Vector3();\r\n            }\r\n        }\r\n\r\n        const engine = this._scene.getEngine();\r\n\r\n        this._shadowMap.onBeforeBindObservable.clear();\r\n        this._shadowMap.onBeforeRenderObservable.clear();\r\n\r\n        this._shadowMap.onBeforeRenderObservable.add((layer: number) => {\r\n            if (this._sceneUBOs) {\r\n                this._scene.setSceneUniformBuffer(this._sceneUBOs[layer]);\r\n            }\r\n            this._currentLayer = layer;\r\n            if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n                engine.setColorWrite(false);\r\n            }\r\n            this._scene.setTransformMatrix(this.getCascadeViewMatrix(layer)!, this.getCascadeProjectionMatrix(layer)!);\r\n            if (this._useUBO) {\r\n                this._scene.getSceneUniformBuffer().unbindEffect();\r\n                this._scene.finalizeSceneUbo();\r\n            }\r\n        });\r\n\r\n        this._shadowMap.onBeforeBindObservable.add(() => {\r\n            this._currentSceneUBO = this._scene.getSceneUniformBuffer();\r\n            engine._debugPushGroup?.(`cascaded shadow map generation for pass id ${engine.currentRenderPassId}`, 1);\r\n            if (this._breaksAreDirty) {\r\n                this._splitFrustum();\r\n            }\r\n            this._computeMatrices();\r\n        });\r\n\r\n        this._splitFrustum();\r\n    }\r\n\r\n    protected override _bindCustomEffectForRenderSubMeshForShadowMap(subMesh: SubMesh, effect: Effect): void {\r\n        effect.setMatrix(\"viewProjection\", this.getCascadeTransformMatrix(this._currentLayer)!);\r\n    }\r\n\r\n    protected override _isReadyCustomDefines(defines: any): void {\r\n        defines.push(\"#define SM_DEPTHCLAMP \" + (this._depthClamp && this._filter !== ShadowGenerator.FILTER_PCSS ? \"1\" : \"0\"));\r\n    }\r\n\r\n    /**\r\n     * Prepare all the defines in a material relying on a shadow map at the specified light index.\r\n     * @param defines Defines of the material we want to update\r\n     * @param lightIndex Index of the light in the enabled light list of the material\r\n     */\r\n    public override prepareDefines(defines: any, lightIndex: number): void {\r\n        super.prepareDefines(defines, lightIndex);\r\n\r\n        const scene = this._scene;\r\n        const light = this._light;\r\n\r\n        if (!scene.shadowsEnabled || !light.shadowEnabled) {\r\n            return;\r\n        }\r\n\r\n        defines[\"SHADOWCSM\" + lightIndex] = true;\r\n        defines[\"SHADOWCSMDEBUG\" + lightIndex] = this.debug;\r\n        defines[\"SHADOWCSMNUM_CASCADES\" + lightIndex] = this.numCascades;\r\n        defines[\"SHADOWCSM_RIGHTHANDED\" + lightIndex] = scene.useRightHandedSystem;\r\n\r\n        const camera = this._getCamera();\r\n\r\n        if (camera && this._shadowMaxZ <= (camera.maxZ || this._shadowMaxZ)) {\r\n            defines[\"SHADOWCSMUSESHADOWMAXZ\" + lightIndex] = true;\r\n        }\r\n\r\n        if (this.cascadeBlendPercentage === 0) {\r\n            defines[\"SHADOWCSMNOBLEND\" + lightIndex] = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the shadow related information inside of an effect (information like near, far, darkness...\r\n     * defined in the generator but impacting the effect).\r\n     * @param lightIndex Index of the light in the enabled light list of the material owning the effect\r\n     * @param effect The effect we are binfing the information for\r\n     */\r\n    public override bindShadowLight(lightIndex: string, effect: Effect): void {\r\n        const light = this._light;\r\n        const scene = this._scene;\r\n\r\n        if (!scene.shadowsEnabled || !light.shadowEnabled) {\r\n            return;\r\n        }\r\n\r\n        const camera = this._getCamera();\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        const shadowMap = this.getShadowMap();\r\n        if (!shadowMap) {\r\n            return;\r\n        }\r\n\r\n        const width = shadowMap.getSize().width;\r\n\r\n        effect.setMatrices(\"lightMatrix\" + lightIndex, this._transformMatricesAsArray);\r\n        effect.setArray(\"viewFrustumZ\" + lightIndex, this._viewSpaceFrustumsZ);\r\n        effect.setFloat(\"cascadeBlendFactor\" + lightIndex, this.cascadeBlendPercentage === 0 ? 10000 : 1 / this.cascadeBlendPercentage);\r\n        effect.setArray(\"frustumLengths\" + lightIndex, this._frustumLengths);\r\n\r\n        // Only PCF uses depth stencil texture.\r\n        if (this._filter === ShadowGenerator.FILTER_PCF) {\r\n            effect.setDepthStencilTexture(\"shadowTexture\" + lightIndex, shadowMap);\r\n            light._uniformBuffer.updateFloat4(\"shadowsInfo\", this.getDarkness(), width, 1 / width, this.frustumEdgeFalloff, lightIndex);\r\n        } else if (this._filter === ShadowGenerator.FILTER_PCSS) {\r\n            for (let cascadeIndex = 0; cascadeIndex < this._numCascades; ++cascadeIndex) {\r\n                this._lightSizeUVCorrection[cascadeIndex * 2 + 0] =\r\n                    cascadeIndex === 0\r\n                        ? 1\r\n                        : (this._cascadeMaxExtents[0].x - this._cascadeMinExtents[0].x) / (this._cascadeMaxExtents[cascadeIndex].x - this._cascadeMinExtents[cascadeIndex].x); // x correction\r\n                this._lightSizeUVCorrection[cascadeIndex * 2 + 1] =\r\n                    cascadeIndex === 0\r\n                        ? 1\r\n                        : (this._cascadeMaxExtents[0].y - this._cascadeMinExtents[0].y) / (this._cascadeMaxExtents[cascadeIndex].y - this._cascadeMinExtents[cascadeIndex].y); // y correction\r\n                this._depthCorrection[cascadeIndex] =\r\n                    cascadeIndex === 0\r\n                        ? 1\r\n                        : (this._cascadeMaxExtents[cascadeIndex].z - this._cascadeMinExtents[cascadeIndex].z) / (this._cascadeMaxExtents[0].z - this._cascadeMinExtents[0].z);\r\n            }\r\n            effect.setDepthStencilTexture(\"shadowTexture\" + lightIndex, shadowMap);\r\n            effect.setTexture(\"depthTexture\" + lightIndex, shadowMap);\r\n\r\n            effect.setArray2(\"lightSizeUVCorrection\" + lightIndex, this._lightSizeUVCorrection);\r\n            effect.setArray(\"depthCorrection\" + lightIndex, this._depthCorrection);\r\n            effect.setFloat(\"penumbraDarkness\" + lightIndex, this.penumbraDarkness);\r\n            light._uniformBuffer.updateFloat4(\"shadowsInfo\", this.getDarkness(), 1 / width, this._contactHardeningLightSizeUVRatio * width, this.frustumEdgeFalloff, lightIndex);\r\n        } else {\r\n            effect.setTexture(\"shadowTexture\" + lightIndex, shadowMap);\r\n            light._uniformBuffer.updateFloat4(\"shadowsInfo\", this.getDarkness(), width, 1 / width, this.frustumEdgeFalloff, lightIndex);\r\n        }\r\n\r\n        light._uniformBuffer.updateFloat2(\r\n            \"depthValues\",\r\n            this.getLight().getDepthMinZ(camera),\r\n            this.getLight().getDepthMinZ(camera) + this.getLight().getDepthMaxZ(camera),\r\n            lightIndex\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the transformation matrix of the first cascade used to project the meshes into the map from the light point of view.\r\n     * (eq to view projection * shadow projection matrices)\r\n     * @returns The transform matrix used to create the shadow map\r\n     */\r\n    public override getTransformMatrix(): Matrix {\r\n        return this.getCascadeTransformMatrix(0)!;\r\n    }\r\n\r\n    /**\r\n     * Disposes the ShadowGenerator.\r\n     * Returns nothing.\r\n     */\r\n    public override dispose(): void {\r\n        super.dispose();\r\n\r\n        if (this._freezeShadowCastersBoundingInfoObservable) {\r\n            this._scene.onBeforeRenderObservable.remove(this._freezeShadowCastersBoundingInfoObservable);\r\n            this._freezeShadowCastersBoundingInfoObservable = null;\r\n        }\r\n\r\n        if (this._depthReducer) {\r\n            this._depthReducer.dispose();\r\n            this._depthReducer = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes the shadow generator setup to a json object.\r\n     * @returns The serialized JSON object\r\n     */\r\n    public override serialize(): any {\r\n        const serializationObject: any = super.serialize();\r\n        const shadowMap = this.getShadowMap();\r\n\r\n        if (!shadowMap) {\r\n            return serializationObject;\r\n        }\r\n\r\n        serializationObject.numCascades = this._numCascades;\r\n        serializationObject.debug = this._debug;\r\n        serializationObject.stabilizeCascades = this.stabilizeCascades;\r\n        serializationObject.lambda = this._lambda;\r\n        serializationObject.cascadeBlendPercentage = this.cascadeBlendPercentage;\r\n        serializationObject.depthClamp = this._depthClamp;\r\n        serializationObject.autoCalcDepthBounds = this.autoCalcDepthBounds;\r\n        serializationObject.shadowMaxZ = this._shadowMaxZ;\r\n        serializationObject.penumbraDarkness = this.penumbraDarkness;\r\n\r\n        serializationObject.freezeShadowCastersBoundingInfo = this._freezeShadowCastersBoundingInfo;\r\n        serializationObject.minDistance = this.minDistance;\r\n        serializationObject.maxDistance = this.maxDistance;\r\n\r\n        serializationObject.renderList = [];\r\n        if (shadowMap.renderList) {\r\n            for (let meshIndex = 0; meshIndex < shadowMap.renderList.length; meshIndex++) {\r\n                const mesh = shadowMap.renderList[meshIndex];\r\n\r\n                serializationObject.renderList.push(mesh.id);\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a serialized ShadowGenerator and returns a new ShadowGenerator.\r\n     * @param parsedShadowGenerator The JSON object to parse\r\n     * @param scene The scene to create the shadow map for\r\n     * @returns The parsed shadow generator\r\n     */\r\n    public static override Parse(parsedShadowGenerator: any, scene: Scene): ShadowGenerator {\r\n        const shadowGenerator = ShadowGenerator.Parse(\r\n            parsedShadowGenerator,\r\n            scene,\r\n            (mapSize: number, light: IShadowLight, camera: Nullable<Camera>) => new CascadedShadowGenerator(mapSize, <DirectionalLight>light, undefined, camera)\r\n        ) as CascadedShadowGenerator;\r\n\r\n        if (parsedShadowGenerator.numCascades !== undefined) {\r\n            shadowGenerator.numCascades = parsedShadowGenerator.numCascades;\r\n        }\r\n\r\n        if (parsedShadowGenerator.debug !== undefined) {\r\n            shadowGenerator.debug = parsedShadowGenerator.debug;\r\n        }\r\n\r\n        if (parsedShadowGenerator.stabilizeCascades !== undefined) {\r\n            shadowGenerator.stabilizeCascades = parsedShadowGenerator.stabilizeCascades;\r\n        }\r\n\r\n        if (parsedShadowGenerator.lambda !== undefined) {\r\n            shadowGenerator.lambda = parsedShadowGenerator.lambda;\r\n        }\r\n\r\n        if (parsedShadowGenerator.cascadeBlendPercentage !== undefined) {\r\n            shadowGenerator.cascadeBlendPercentage = parsedShadowGenerator.cascadeBlendPercentage;\r\n        }\r\n\r\n        if (parsedShadowGenerator.depthClamp !== undefined) {\r\n            shadowGenerator.depthClamp = parsedShadowGenerator.depthClamp;\r\n        }\r\n\r\n        if (parsedShadowGenerator.autoCalcDepthBounds !== undefined) {\r\n            shadowGenerator.autoCalcDepthBounds = parsedShadowGenerator.autoCalcDepthBounds;\r\n        }\r\n\r\n        if (parsedShadowGenerator.shadowMaxZ !== undefined) {\r\n            shadowGenerator.shadowMaxZ = parsedShadowGenerator.shadowMaxZ;\r\n        }\r\n\r\n        if (parsedShadowGenerator.penumbraDarkness !== undefined) {\r\n            shadowGenerator.penumbraDarkness = parsedShadowGenerator.penumbraDarkness;\r\n        }\r\n\r\n        if (parsedShadowGenerator.freezeShadowCastersBoundingInfo !== undefined) {\r\n            shadowGenerator.freezeShadowCastersBoundingInfo = parsedShadowGenerator.freezeShadowCastersBoundingInfo;\r\n        }\r\n\r\n        if (parsedShadowGenerator.minDistance !== undefined && parsedShadowGenerator.maxDistance !== undefined) {\r\n            shadowGenerator.setMinMaxDistance(parsedShadowGenerator.minDistance, parsedShadowGenerator.maxDistance);\r\n        }\r\n\r\n        return shadowGenerator;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAK1D,OAAO,EAAE,mBAAmB,EAAE,MAAM,8CAA8C,CAAC;AAInF,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAGpD,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAE1D,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;;;;;;;;;AAQxD,gEAAgE;AAChE,MAAM,KAAK,kKAAG,UAAO,CAAC,EAAE,EAAE,CAAC;AAC3B,gEAAgE;AAChE,MAAM,OAAO,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;AAE/B,MAAM,KAAK,GAAG,mKAAI,UAAO,EAAE,CAAC;AAC5B,MAAM,KAAK,GAAG,kKAAI,WAAO,EAAE,CAAC;AAC5B,MAAM,SAAS,GAAG,mKAAI,SAAM,EAAE,CAAC;AAOzB,MAAO,uBAAwB,qLAAQ,kBAAe;IA8BrC,eAAe,CAAC,MAAc,EAAA;QAC7C,IAAI,MAAM,iLAAK,kBAAe,CAAC,WAAW,IAAI,MAAM,iLAAK,kBAAe,CAAC,UAAU,IAAI,MAAM,iLAAK,kBAAe,CAAC,WAAW,EAAE,CAAC;YAC5H,OAAO,MAAM,CAAC;QAClB,CAAC;8JAED,SAAM,CAAC,KAAK,CAAC,sBAAsB,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;QAErD,mLAAO,kBAAe,CAAC,WAAW,CAAC;IACvC,CAAC;IASD;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,uBAAuB,CAAC,kBAAkB,CAAC,EAAE,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;QAC1H,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAWD;;;;OAIG,CACH,IAAW,+BAA+B,GAAA;QACtC,OAAO,IAAI,CAAC,gCAAgC,CAAC;IACjD,CAAC;IAED,IAAW,+BAA+B,CAAC,MAAe,EAAA;QACtD,IAAI,IAAI,CAAC,0CAA0C,IAAI,MAAM,EAAE,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAC7F,IAAI,CAAC,0CAA0C,GAAG,IAAI,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,0CAA0C,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9D,IAAI,CAAC,0CAA0C,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAG,CAAD,GAAK,CAAC,iCAAiC,EAAE,CAAC,CAAC;QAC/I,CAAC;QAED,IAAI,CAAC,gCAAgC,GAAG,MAAM,CAAC;QAE/C,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,iCAAiC,EAAE,CAAC;QAC7C,CAAC;IACL,CAAC;IAKS,iCAAiC,GAAA;QACvC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACnF,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEtF,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;YAC9C,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;gBACjE,MAAM,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;gBAEnC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,SAAS;gBACb,CAAC;gBAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,EACvC,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;gBAE3C,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;gBACxD,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC;QAED,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAID;;;;OAIG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED,IAAW,yBAAyB,CAAC,YAA0B,EAAA;QAC3D,IAAI,CAAC,0BAA0B,GAAG,YAAY,CAAC;IACnD,CAAC;IAOD;;;;;;;OAOG,CACI,iBAAiB,CAAC,GAAW,EAAE,GAAW,EAAA;QAC7C,IAAI,IAAI,CAAC,YAAY,KAAK,GAAG,IAAI,IAAI,CAAC,YAAY,KAAK,GAAG,EAAE,CAAC;YACzD,OAAO;QACX,CAAC;QAED,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;YACZ,GAAG,GAAG,CAAC,CAAC;YACR,GAAG,GAAG,CAAC,CAAC;QACZ,CAAC;QAED,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;YACV,GAAG,GAAG,CAAC,CAAC;QACZ,CAAC;QAED,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;YACV,GAAG,GAAG,CAAC,CAAC;QACZ,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,oEAAA,EAAsE,CACtE,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,oEAAA,EAAsE,CACtE,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,uBAAuB,CAAC,SAAS,CAAC;IAC7C,CAAC;IAKD;;;;OAIG,CACI,oBAAoB,CAAC,YAAoB,EAAA;QAC5C,OAAO,YAAY,IAAI,CAAC,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAChH,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,YAAoB,EAAA;QAC5C,OAAO,YAAY,IAAI,CAAC,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAChH,CAAC;IAiBD;;;OAGG,CACH,IAAW,UAAU,GAAA;QACjB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACrB,OAAO,CAAC,CAAC;QACb,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;OAEG,CACH,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,OAAO;QACX,CAAC;QACD,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,IAAI,AAAC,KAAK,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,CAAE,CAAC;YAClG,OAAO;QACX,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACtC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAID;;;OAGG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,IAAW,KAAK,CAAC,GAAY,EAAA;QACzB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;QAClB,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;IAC1C,CAAC;IAID;;;;;;;OAOG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAc,EAAA;QAChC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAID;;;OAGG,CACH,IAAW,sBAAsB,GAAA;QAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED,IAAW,sBAAsB,CAAC,KAAa,EAAA;QAC3C,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;IAC1C,CAAC;IAID;;;;;OAKG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,IAAI,IAAI,CAAC,OAAO,IAAI,MAAM,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,UAAkB,EAAA;QAC1C,OAAO,UAAU,IAAI,CAAC,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrG,CAAC;IAED;;;;OAIG,CACI,0BAA0B,CAAC,UAAkB,EAAA;QAChD,OAAO,UAAU,IAAI,CAAC,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3G,CAAC;IAED;;;;OAIG,CACI,yBAAyB,CAAC,UAAkB,EAAA;QAC/C,OAAO,UAAU,IAAI,CAAC,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC1G,CAAC;IAGD;;;;;;;;OAQG,CACI,gBAAgB,CAAC,aAAsC,EAAA;QAC1D,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAKD;;;;;;;;OAQG,CACH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAc,EAAA;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAEjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,gKAAI,eAAY,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,MAAoC,EAAE,EAAE;gBACtF,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,EAChB,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;gBACrB,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;oBACb,GAAG,GAAG,CAAC,CAAC;oBACR,GAAG,GAAG,CAAC,CAAC;gBACZ,CAAC;gBACD,IAAI,GAAG,IAAI,IAAI,CAAC,YAAY,IAAI,GAAG,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACvD,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBACrC,CAAC;YACL,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;IAClC,CAAC;IAED;;;;;OAKG,CACH,IAAW,8BAA8B,GAAA;QACrC,OAAO,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED,IAAW,8BAA8B,CAAC,KAAa,EAAA;QACnD,IAAI,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC;QACvE,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,YAAY,GAAA;QACf,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAEO,aAAa,GAAA;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,EACpB,GAAG,GAAG,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,AACvC,WAAW,GAAG,GAAG,GAAG,IAAI,EACxB,WAAW,GAAG,IAAI,CAAC,GAFoE,SAExD,EAC/B,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;QAEjK,MAAM,IAAI,GAAG,IAAI,GAAG,WAAW,GAAG,WAAW,EACzC,IAAI,GAAG,IAAI,GAAG,WAAW,GAAG,WAAW,CAAC;QAE5C,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,EACrB,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;QAExB,IAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,YAAY,CAAE,CAAC;YAC9E,MAAM,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,EAC5C,GAAG,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,EACvB,OAAO,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;YAE/B,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;YAEnD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,iBAAiB,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC;YACnI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,WAAW,CAAC;YAEtE,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,GAAG,WAAW,CAAC;QACrJ,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IACjC,CAAC;IAEO,gBAAgB,GAAA;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;uKAED,UAAO,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAChF,IAAI,IAAI,CAAC,GAAG,gKAAC,UAAO,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,iKAAE,UAAO,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACpE,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,kDAAkD;QAChG,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAErD,MAAM,qBAAqB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAAC;QAEtE,IAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,EAAE,YAAY,CAAE,CAAC;YAC1E,IAAI,CAAC,2BAA2B,CAAC,YAAY,CAAC,CAAC;YAC/C,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;YAE1C,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,yBAAyB;YAE5H,oCAAoC;YACpC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;YAErJ,+DAA+D;2KAC/D,SAAM,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC;YAEtI,sEAAsE;YACtE,IAAI,QAAQ,GAAG,CAAC,EACZ,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC;YAEvB,+EAA+E;YAC/E,MAAM,YAAY,GAAG,IAAI,CAAC,0BAA0B,CAAC;YAErD,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC;YACtD,mKAAmK;YACnK,kOAAkO;YAElO,MAAM,eAAe,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;YAChE,MAAM,eAAe,GAAG,YAAY,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;YAEhE,IAAI,eAAe,GAAG,QAAQ,EAAE,CAAC;YAC7B,0CAA0C;YAC1C,uFAAuF;YACvF,sIAAsI;YAC1I,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,8LAAe,CAAC,WAAW,EAAE,CAAC;oBACnE,4GAA4G;oBAC5G,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;oBAE/C,IAAI,IAAI,CAAC,MAAM,iLAAK,kBAAe,CAAC,WAAW,EAAE,CAAC;wBAC9C,4LAA4L;wBAC5L,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;oBACnD,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,yJAAyJ;oBACzJ,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;oBAE/C,8JAA8J;oBAC9J,6IAA6I;oBAC7I,8IAA8I;oBAC9I,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;oBAE/C,8HAA8H;oBAC9H,2OAA2O;oBAC3O,yFAAyF;oBACzF,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,EAAE,QAAQ,CAAC,CAAC;gBAClD,CAAC;YACL,CAAC;2KAED,SAAM,CAAC,qBAAqB,CACxB,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,EACvC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,EACvC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,EACvC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,EACvC,qBAAqB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAC3C,qBAAqB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAC3C,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EACtC,KAAK,CAAC,SAAS,EAAE,CAAC,eAAe,CACpC,CAAC;YAEF,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;YACnD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;YAEnD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;YAE9H,mFAAmF;YACnF,uCAAuC;2KACvC,UAAO,CAAC,yBAAyB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,uBAAuB;YACjH,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;YAEtC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAC7G,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB;2KAEpF,SAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;YAE1D,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC;YACxG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC;YAE9H,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,yBAAyB,EAAE,YAAY,GAAG,EAAE,CAAC,CAAC;QACzG,CAAC;IACL,CAAC;IAED,sDAAsD;IAC9C,2BAA2B,CAAC,YAAoB,EAAA;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,iBAAiB,EAChE,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC;QAE3D,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC;QAEhE,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,mIAAmI;QAE3J,MAAM,sBAAsB,GAAG,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC;QAEnC,IAAI,sBAAsB,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;YAC/B,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,WAAW,kKAAG,SAAM,CAAC,MAAM,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC,CAAC;QAEpE,IAAI,sBAAsB,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,GAAG,cAAc,CAAC;YAC7B,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChF,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,uBAAuB,CAAC,uBAAuB,CAAC,MAAM,EAAE,EAAE,WAAW,CAAE,CAAC;YAC5G,KAAK,CAAC,QAAQ,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,CAAC,WAAW,GAAG,iBAAiB,CAAC,GAAG,uBAAuB,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,CAAC;YAC5J,IAAI,eAAe,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACpC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;YAChB,CAAC;2KACD,UAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;QACrH,CAAC;QAED,mEAAmE;QACnE,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,uBAAuB,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,WAAW,CAAE,CAAC;YAChH,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YACzJ,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,kBAAkB;YACrE,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB;YAEhD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;YAE5E,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9E,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,YAAoB,EAAA;QAC/C,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC3G,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9G,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAEjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,mDAAmD;QACnD,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,EAAE,WAAW,CAAE,CAAC;YACzG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;QAC5G,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC;QAExG,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,4EAA4E;YAC5E,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,EAAE,WAAW,CAAE,CAAC;gBACzG,MAAM,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;gBACxI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;YAED,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;YAEjD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;YAC/F,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,CAAC,YAAY,EAAE,CAAC,YAAY,EAAE,CAAC,YAAY,CAAC,CAAC;QACtG,CAAC,MAAM,CAAC;YACJ,+CAA+C;YAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAEzD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC,kBAAkB;0KAE3F,UAAM,CAAC,aAAa,CAAC,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,qBAAqB;YAEpF,+CAA+C;YAC/C,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,EAAE,WAAW,CAAE,CAAC;gBACzG,yKAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;gBAE/G,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC7D,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACjE,CAAC;QACL,CAAC;IACL,CAAC;IAES,kBAAkB,GAAA;QACxB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAE,CAAC;gBACzC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAA,uCAAA,EAA0C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAA,WAAA,EAAc,CAAC,CAAA,CAAA,CAAG,CAAC,CAAC,CAAC;YAC7I,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,WAAW,GAAA;QACzB,MAAM,MAAM,iKAAG,cAAW,CAAC,iBAAiB,CAAC;QAC7C,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;IACvC,CAAC;IASD;;;;;;;;;;OAUG,CACH,YAAY,OAAe,EAAE,KAAuB,EAAE,gBAA0B,EAAE,MAAyB,EAAE,iBAAiB,GAAG,IAAI,CAAA;QACjI,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;kKACvC,SAAM,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;YAC1E,OAAO;QACX,CAAC;QAED,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAEnE,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;IAC7C,CAAC;IAEkB,oBAAoB,GAAA;QACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,IAAI,GAAG,CAAC;QACrD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,uBAAuB,CAAC,sBAAsB,CAAC;QACxF,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAC;QACzD,IAAI,CAAC,0CAA0C,GAAG,IAAI,CAAC,0CAA0C,IAAI,IAAI,CAAC;QAC1G,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,+BAA+B,IAAI,KAAK,CAAC;QACrF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,0BAA0B,IAAI,mKAAI,eAAY,CAAC,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,yKAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC;QACpD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,IAAI,KAAK,CAAC;QACxE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;QAC5C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,IAAI,GAAG,CAAC;QACnE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,GAAG,CAAC;QACnC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,KAAK,CAAC;QAE/D,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,KAAK,CAAC,oBAAoB,EAAE,CAAC;IACjC,CAAC;IAEkB,0BAA0B,GAAA;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG;YAAE,KAAK,EAAE,IAAI,CAAC,QAAQ;YAAE,MAAM,EAAE,IAAI,CAAC,QAAQ;YAAE,MAAM,EAAE,IAAI,CAAC,WAAW;QAAA,CAAE,CAAC;QACvF,IAAI,CAAC,UAAU,GAAG,wLAAI,sBAAmB,CACrC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,eAAe,EAClC,IAAI,EACJ,IAAI,CAAC,MAAM,EACX,KAAK,EACL,IAAI,EACJ,IAAI,CAAC,YAAY,EACjB,KAAK,EACL,SAAS,EACT,KAAK,EACL,KAAK,EACL,SAAS,EACT,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CACvF,CAAC;QACF,IAAI,CAAC,UAAU,CAAC,yBAAyB,CACrC,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,MAAA,GAAS,CAAC,CAAA,MAAO,CAAC,CAAC,CAAC,QAAA,CAAS,CAAC,IAAI,EACjE,GAAA,CAAI,EACJ,QAAA,CAAS,EACT,SAAS,EACT,SAAS,EACT,UAAA,EAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,OAAqC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAC1D,CAAC;QACF,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAC7C,CAAC;IAEkB,oBAAoB,GAAA;QACnC,KAAK,CAAC,oBAAoB,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,yBAAyB,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,mBAAmB,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxD,IAAI,CAAC,eAAe,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,sBAAsB,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,gBAAgB,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAErD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,yBAAyB,GAAG,EAAE,CAAC;QAEpC,IAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,EAAE,YAAY,CAAE,CAAC;YAC1E,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG;gBAC3B,iBAAiB,EAAE,CAAC;gBACpB,aAAa,EAAE,CAAC;aACnB,CAAC;YAEF,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,iKAAG,UAAM,CAAC,IAAI,EAAE,CAAC;YACjD,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,kKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,kKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;YACtD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,GAAG,mKAAI,UAAO,EAAE,CAAC;YACtD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,GAAG,mKAAI,UAAO,EAAE,CAAC;YACtD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,yKAAO,EAAE,CAAC;YAClD,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,GAAG,mKAAI,UAAO,EAAE,CAAC;YACpD,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,GAAG,IAAI,KAAK,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAEjH,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,uBAAuB,CAAC,uBAAuB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;gBAC9E,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,mKAAI,UAAO,EAAE,CAAC;YACpE,CAAC;QACL,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QAEjD,IAAI,CAAC,UAAU,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,KAAa,EAAE,EAAE;YAC3D,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9D,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,IAAI,CAAC,OAAO,iLAAK,kBAAe,CAAC,UAAU,EAAE,CAAC;gBAC9C,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAE,EAAE,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAE,CAAC,CAAC;YAC3G,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,YAAY,EAAE,CAAC;gBACnD,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACnC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAC5D,MAAM,CAAC,eAAe,EAAE,CAAC,CAAA,2CAAA,EAA8C,MAAM,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC,CAAC;YACxG,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,CAAC;YACD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEkB,6CAA6C,CAAC,OAAgB,EAAE,MAAc,EAAA;QAC7F,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,CAAE,CAAC,CAAC;IAC5F,CAAC;IAEkB,qBAAqB,CAAC,OAAY,EAAA;QACjD,OAAO,CAAC,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,KAAK,8LAAe,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5H,CAAC;IAED;;;;OAIG,CACa,cAAc,CAAC,OAAY,EAAE,UAAkB,EAAA;QAC3D,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAE1C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAChD,OAAO;QACX,CAAC;QAED,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QACzC,OAAO,CAAC,gBAAgB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QACpD,OAAO,CAAC,uBAAuB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;QACjE,OAAO,CAAC,uBAAuB,GAAG,UAAU,CAAC,GAAG,KAAK,CAAC,oBAAoB,CAAC;QAE3E,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAEjC,IAAI,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YAClE,OAAO,CAAC,wBAAwB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,sBAAsB,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,CAAC,kBAAkB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QACpD,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACa,eAAe,CAAC,UAAkB,EAAE,MAAc,EAAA;QAC9D,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAChD,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;QAExC,MAAM,CAAC,WAAW,CAAC,aAAa,GAAG,UAAU,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;QAC/E,MAAM,CAAC,QAAQ,CAAC,cAAc,GAAG,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACvE,MAAM,CAAC,QAAQ,CAAC,oBAAoB,GAAG,UAAU,EAAE,IAAI,CAAC,sBAAsB,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,CAAC;QAChI,MAAM,CAAC,QAAQ,CAAC,gBAAgB,GAAG,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAErE,uCAAuC;QACvC,IAAI,IAAI,CAAC,OAAO,KAAK,8LAAe,CAAC,UAAU,EAAE,CAAC;YAC9C,MAAM,CAAC,sBAAsB,CAAC,eAAe,GAAG,UAAU,EAAE,SAAS,CAAC,CAAC;YACvE,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QAChI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,iLAAK,kBAAe,CAAC,WAAW,EAAE,CAAC;YACtD,IAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,EAAE,YAAY,CAAE,CAAC;gBAC1E,IAAI,CAAC,sBAAsB,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,GAC7C,YAAY,KAAK,CAAC,GACZ,CAAC,GACD,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe;gBAC9K,IAAI,CAAC,sBAAsB,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,GAC7C,YAAY,KAAK,CAAC,GACZ,CAAC,GACD,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe;gBAC9K,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,GAC/B,YAAY,KAAK,CAAC,GACZ,CAAC,GACD,CAAC,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClK,CAAC;YACD,MAAM,CAAC,sBAAsB,CAAC,eAAe,GAAG,UAAU,EAAE,SAAS,CAAC,CAAC;YACvE,MAAM,CAAC,UAAU,CAAC,cAAc,GAAG,UAAU,EAAE,SAAS,CAAC,CAAC;YAE1D,MAAM,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACpF,MAAM,CAAC,QAAQ,CAAC,iBAAiB,GAAG,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACvE,MAAM,CAAC,QAAQ,CAAC,kBAAkB,GAAG,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACxE,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,iCAAiC,GAAG,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QACzK,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,UAAU,CAAC,eAAe,GAAG,UAAU,EAAE,SAAS,CAAC,CAAC;YAC3D,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QAChI,CAAC;QAED,KAAK,CAAC,cAAc,CAAC,YAAY,CAC7B,aAAa,EACb,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EACpC,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EAC3E,UAAU,CACb,CAAC;IACN,CAAC;IAED;;;;OAIG,CACa,kBAAkB,GAAA;QAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAE,CAAC;IAC9C,CAAC;IAED;;;OAGG,CACa,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,0CAA0C,EAAE,CAAC;YAClD,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;YAC7F,IAAI,CAAC,0CAA0C,GAAG,IAAI,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,CAAC;IACL,CAAC;IAED;;;OAGG,CACa,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAQ,KAAK,CAAC,SAAS,EAAE,CAAC;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEtC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,mBAAmB,CAAC;QAC/B,CAAC;QAED,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QACpD,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QACxC,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC1C,mBAAmB,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QACzE,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAClD,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnE,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAClD,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE7D,mBAAmB,CAAC,+BAA+B,GAAG,IAAI,CAAC,gCAAgC,CAAC;QAC5F,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnD,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAEnD,mBAAmB,CAAC,UAAU,GAAG,EAAE,CAAC;QACpC,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACvB,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;gBAC3E,MAAM,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;gBAE7C,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjD,CAAC;QACL,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAU,KAAK,CAAC,qBAA0B,EAAE,KAAY,EAAA;QACjE,MAAM,eAAe,+KAAG,kBAAe,CAAC,KAAK,CACzC,qBAAqB,EACrB,KAAK,EACL,CAAC,OAAe,EAAE,KAAmB,EAAE,MAAwB,EAAE,CAAG,CAAD,GAAK,uBAAuB,CAAC,OAAO,EAAoB,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAC5H,CAAC;QAE7B,IAAI,qBAAqB,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAClD,eAAe,CAAC,WAAW,GAAG,qBAAqB,CAAC,WAAW,CAAC;QACpE,CAAC;QAED,IAAI,qBAAqB,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC5C,eAAe,CAAC,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC;QACxD,CAAC;QAED,IAAI,qBAAqB,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACxD,eAAe,CAAC,iBAAiB,GAAG,qBAAqB,CAAC,iBAAiB,CAAC;QAChF,CAAC;QAED,IAAI,qBAAqB,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC7C,eAAe,CAAC,MAAM,GAAG,qBAAqB,CAAC,MAAM,CAAC;QAC1D,CAAC;QAED,IAAI,qBAAqB,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAC7D,eAAe,CAAC,sBAAsB,GAAG,qBAAqB,CAAC,sBAAsB,CAAC;QAC1F,CAAC;QAED,IAAI,qBAAqB,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACjD,eAAe,CAAC,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC;QAClE,CAAC;QAED,IAAI,qBAAqB,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YAC1D,eAAe,CAAC,mBAAmB,GAAG,qBAAqB,CAAC,mBAAmB,CAAC;QACpF,CAAC;QAED,IAAI,qBAAqB,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACjD,eAAe,CAAC,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC;QAClE,CAAC;QAED,IAAI,qBAAqB,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACvD,eAAe,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,gBAAgB,CAAC;QAC9E,CAAC;QAED,IAAI,qBAAqB,CAAC,+BAA+B,KAAK,SAAS,EAAE,CAAC;YACtE,eAAe,CAAC,+BAA+B,GAAG,qBAAqB,CAAC,+BAA+B,CAAC;QAC5G,CAAC;QAED,IAAI,qBAAqB,CAAC,WAAW,KAAK,SAAS,IAAI,qBAAqB,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACrG,eAAe,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,WAAW,EAAE,qBAAqB,CAAC,WAAW,CAAC,CAAC;QAC5G,CAAC;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;;AAzkCuB,wBAAA,uBAAuB,GAAG;IAC9C,kKAAI,WAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;IAC7B,mKAAI,UAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;IAC7B,kKAAI,WAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;IAC7B,mKAAI,UAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;IAC7B,mKAAI,UAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;IAC7B,mKAAI,UAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;IAC7B,mKAAI,UAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;IAC7B,kKAAI,WAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;CAChC,CAAC;AAEF;;GAEG,CACoB,wBAAA,SAAS,GAAG,yBAAyB,CAAC;AAE7D;;GAEG,CACoB,wBAAA,sBAAsB,GAAG,CAAC,CAAC;AAClD;;GAEG,CACW,wBAAA,kBAAkB,GAAG,CAAC,CAAC;AACrC;;GAEG,CACW,wBAAA,kBAAkB,GAAG,CAAC,CAAC;AAyqBrC;;GAEG,CACoB,wBAAA,6BAA6B,GAA2B,CAAC,CAAC,EAAE,EAAE;IACjF,kKAAM,cAAA,AAAW,EAAC,+BAA+B,CAAC,CAAC;AACvD,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 3400, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/shadowLight.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/shadowLight.ts"], "sourcesContent": ["import { serialize, serializeAsVector3 } from \"../Misc/decorators\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Light } from \"./light\";\r\nimport { Axis } from \"../Maths/math.axis\";\r\nimport type { Nullable } from \"core/types\";\r\nimport { Constants } from \"core/Engines/constants\";\r\n/**\r\n * Interface describing all the common properties and methods a shadow light needs to implement.\r\n * This helps both the shadow generator and materials to generate the corresponding shadow maps\r\n * as well as binding the different shadow properties to the effects.\r\n */\r\nexport interface IShadowLight extends Light {\r\n    /**\r\n     * The light id in the scene (used in scene.getLightById for instance)\r\n     */\r\n    id: string;\r\n    /**\r\n     * The position the shadow will be casted from.\r\n     */\r\n    position: Vector3;\r\n    /**\r\n     * In 2d mode (needCube being false), the direction used to cast the shadow.\r\n     */\r\n    direction: Vector3;\r\n    /**\r\n     * The transformed position. Position of the light in world space taking parenting in account.\r\n     */\r\n    transformedPosition: Vector3;\r\n    /**\r\n     * The transformed direction. Direction of the light in world space taking parenting in account.\r\n     */\r\n    transformedDirection: Vector3;\r\n    /**\r\n     * The friendly name of the light in the scene.\r\n     */\r\n    name: string;\r\n    /**\r\n     * Defines the shadow projection clipping minimum z value.\r\n     */\r\n    shadowMinZ: number;\r\n    /**\r\n     * Defines the shadow projection clipping maximum z value.\r\n     */\r\n    shadowMaxZ: number;\r\n\r\n    /**\r\n     * Computes the transformed information (transformedPosition and transformedDirection in World space) of the current light\r\n     * @returns true if the information has been computed, false if it does not need to (no parenting)\r\n     */\r\n    computeTransformedInformation(): boolean;\r\n\r\n    /**\r\n     * Gets the scene the light belongs to.\r\n     * @returns The scene\r\n     */\r\n    getScene(): Scene;\r\n\r\n    /**\r\n     * Callback defining a custom Projection Matrix Builder.\r\n     * This can be used to override the default projection matrix computation.\r\n     */\r\n    customProjectionMatrixBuilder: (viewMatrix: Matrix, renderList: Array<AbstractMesh>, result: Matrix) => void;\r\n\r\n    /**\r\n     * Sets the shadow projection matrix in parameter to the generated projection matrix.\r\n     * @param matrix The matrix to update with the projection information\r\n     * @param viewMatrix The transform matrix of the light\r\n     * @param renderList The list of mesh to render in the map\r\n     * @returns The current light\r\n     */\r\n    setShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): IShadowLight;\r\n\r\n    /**\r\n     * Gets the current depth scale used in ESM.\r\n     * @returns The scale\r\n     */\r\n    getDepthScale(): number;\r\n\r\n    /**\r\n     * Returns whether or not the shadow generation require a cube texture or a 2d texture.\r\n     * @returns true if a cube texture needs to be use\r\n     */\r\n    needCube(): boolean;\r\n\r\n    /**\r\n     * Detects if the projection matrix requires to be recomputed this frame.\r\n     * @returns true if it requires to be recomputed otherwise, false.\r\n     */\r\n    needProjectionMatrixCompute(): boolean;\r\n\r\n    /**\r\n     * Forces the shadow generator to recompute the projection matrix even if position and direction did not changed.\r\n     */\r\n    forceProjectionMatrixCompute(): void;\r\n\r\n    /**\r\n     * Get the direction to use to render the shadow map. In case of cube texture, the face index can be passed.\r\n     * @param faceIndex The index of the face we are computed the direction to generate shadow\r\n     * @returns The set direction in 2d mode otherwise the direction to the cubemap face if needCube() is true\r\n     */\r\n    getShadowDirection(faceIndex?: number): Vector3;\r\n\r\n    /**\r\n     * Gets the minZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the min for\r\n     * @returns the depth min z\r\n     */\r\n    getDepthMinZ(activeCamera: Nullable<Camera>): number;\r\n\r\n    /**\r\n     * Gets the maxZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the max for\r\n     * @returns the depth max z\r\n     */\r\n    getDepthMaxZ(activeCamera: Nullable<Camera>): number;\r\n}\r\n\r\n/**\r\n * Base implementation IShadowLight\r\n * It groups all the common behaviour in order to reduce duplication and better follow the DRY pattern.\r\n */\r\nexport abstract class ShadowLight extends Light implements IShadowLight {\r\n    protected abstract _setDefaultShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): void;\r\n\r\n    protected _position: Vector3;\r\n    protected _setPosition(value: Vector3) {\r\n        this._position = value;\r\n    }\r\n    /**\r\n     * Sets the position the shadow will be casted from. Also use as the light position for both\r\n     * point and spot lights.\r\n     */\r\n    @serializeAsVector3()\r\n    public get position(): Vector3 {\r\n        return this._position;\r\n    }\r\n    /**\r\n     * Sets the position the shadow will be casted from. Also use as the light position for both\r\n     * point and spot lights.\r\n     */\r\n    public set position(value: Vector3) {\r\n        this._setPosition(value);\r\n    }\r\n\r\n    protected _direction: Vector3;\r\n    protected _setDirection(value: Vector3) {\r\n        this._direction = value;\r\n    }\r\n    /**\r\n     * In 2d mode (needCube being false), gets the direction used to cast the shadow.\r\n     * Also use as the light direction on spot and directional lights.\r\n     */\r\n    @serializeAsVector3()\r\n    public get direction(): Vector3 {\r\n        return this._direction;\r\n    }\r\n    /**\r\n     * In 2d mode (needCube being false), sets the direction used to cast the shadow.\r\n     * Also use as the light direction on spot and directional lights.\r\n     */\r\n    public set direction(value: Vector3) {\r\n        this._setDirection(value);\r\n    }\r\n\r\n    protected _shadowMinZ: number;\r\n    /**\r\n     * Gets the shadow projection clipping minimum z value.\r\n     */\r\n    @serialize()\r\n    public get shadowMinZ(): number {\r\n        return this._shadowMinZ;\r\n    }\r\n    /**\r\n     * Sets the shadow projection clipping minimum z value.\r\n     */\r\n    public set shadowMinZ(value: number) {\r\n        this._shadowMinZ = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    protected _shadowMaxZ: number;\r\n    /**\r\n     * Sets the shadow projection clipping maximum z value.\r\n     */\r\n    @serialize()\r\n    public get shadowMaxZ(): number {\r\n        return this._shadowMaxZ;\r\n    }\r\n    /**\r\n     * Gets the shadow projection clipping maximum z value.\r\n     */\r\n    public set shadowMaxZ(value: number) {\r\n        this._shadowMaxZ = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    /**\r\n     * Callback defining a custom Projection Matrix Builder.\r\n     * This can be used to override the default projection matrix computation.\r\n     */\r\n    public customProjectionMatrixBuilder: (viewMatrix: Matrix, renderList: Array<AbstractMesh>, result: Matrix) => void;\r\n\r\n    /**\r\n     * The transformed position. Position of the light in world space taking parenting in account. Needs to be computed by calling computeTransformedInformation.\r\n     */\r\n    public transformedPosition: Vector3;\r\n\r\n    /**\r\n     * The transformed direction. Direction of the light in world space taking parenting in account.\r\n     */\r\n    public transformedDirection: Vector3;\r\n\r\n    private _needProjectionMatrixCompute: boolean = true;\r\n\r\n    /**\r\n     * Computes the transformed information (transformedPosition and transformedDirection in World space) of the current light\r\n     * @returns true if the information has been computed, false if it does not need to (no parenting)\r\n     */\r\n    public computeTransformedInformation(): boolean {\r\n        if (this.parent && this.parent.getWorldMatrix) {\r\n            if (!this.transformedPosition) {\r\n                this.transformedPosition = Vector3.Zero();\r\n            }\r\n            Vector3.TransformCoordinatesToRef(this.position, this.parent.getWorldMatrix(), this.transformedPosition);\r\n\r\n            // In case the direction is present.\r\n            if (this.direction) {\r\n                if (!this.transformedDirection) {\r\n                    this.transformedDirection = Vector3.Zero();\r\n                }\r\n                Vector3.TransformNormalToRef(this.direction, this.parent.getWorldMatrix(), this.transformedDirection);\r\n            }\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Return the depth scale used for the shadow map.\r\n     * @returns the depth scale.\r\n     */\r\n    public getDepthScale(): number {\r\n        return 50.0;\r\n    }\r\n\r\n    /**\r\n     * Get the direction to use to render the shadow map. In case of cube texture, the face index can be passed.\r\n     * @param faceIndex The index of the face we are computed the direction to generate shadow\r\n     * @returns The set direction in 2d mode otherwise the direction to the cubemap face if needCube() is true\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public getShadowDirection(faceIndex?: number): Vector3 {\r\n        return this.transformedDirection ? this.transformedDirection : this.direction;\r\n    }\r\n\r\n    /**\r\n     * If computeTransformedInformation has been called, returns the ShadowLight absolute position in the world. Otherwise, returns the local position.\r\n     * @returns the position vector in world space\r\n     */\r\n    public override getAbsolutePosition(): Vector3 {\r\n        return this.transformedPosition ? this.transformedPosition : this.position;\r\n    }\r\n\r\n    /**\r\n     * Sets the ShadowLight direction toward the passed target.\r\n     * @param target The point to target in local space\r\n     * @returns the updated ShadowLight direction\r\n     */\r\n    public setDirectionToTarget(target: Vector3): Vector3 {\r\n        this.direction = Vector3.Normalize(target.subtract(this.position));\r\n        return this.direction;\r\n    }\r\n\r\n    /**\r\n     * Returns the light rotation in euler definition.\r\n     * @returns the x y z rotation in local space.\r\n     */\r\n    public getRotation(): Vector3 {\r\n        this.direction.normalize();\r\n        const xaxis = Vector3.Cross(this.direction, Axis.Y);\r\n        const yaxis = Vector3.Cross(xaxis, this.direction);\r\n        return Vector3.RotationFromAxis(xaxis, yaxis, this.direction);\r\n    }\r\n\r\n    /**\r\n     * Returns whether or not the shadow generation require a cube texture or a 2d texture.\r\n     * @returns true if a cube texture needs to be use\r\n     */\r\n    public needCube(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Detects if the projection matrix requires to be recomputed this frame.\r\n     * @returns true if it requires to be recomputed otherwise, false.\r\n     */\r\n    public needProjectionMatrixCompute(): boolean {\r\n        return this._needProjectionMatrixCompute;\r\n    }\r\n\r\n    /**\r\n     * Forces the shadow generator to recompute the projection matrix even if position and direction did not changed.\r\n     */\r\n    public forceProjectionMatrixCompute(): void {\r\n        this._needProjectionMatrixCompute = true;\r\n    }\r\n\r\n    /** @internal */\r\n    public override _initCache() {\r\n        super._initCache();\r\n\r\n        this._cache.position = Vector3.Zero();\r\n    }\r\n\r\n    /** @internal */\r\n    public override _isSynchronized(): boolean {\r\n        if (!this._cache.position.equals(this.position)) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Computes the world matrix of the node\r\n     * @param force defines if the cache version should be invalidated forcing the world matrix to be created from scratch\r\n     * @returns the world matrix\r\n     */\r\n    public override computeWorldMatrix(force?: boolean): Matrix {\r\n        if (!force && this.isSynchronized()) {\r\n            this._currentRenderId = this.getScene().getRenderId();\r\n            return this._worldMatrix;\r\n        }\r\n\r\n        this._updateCache();\r\n        this._cache.position.copyFrom(this.position);\r\n\r\n        if (!this._worldMatrix) {\r\n            this._worldMatrix = Matrix.Identity();\r\n        }\r\n\r\n        Matrix.TranslationToRef(this.position.x, this.position.y, this.position.z, this._worldMatrix);\r\n\r\n        if (this.parent && this.parent.getWorldMatrix) {\r\n            this._worldMatrix.multiplyToRef(this.parent.getWorldMatrix(), this._worldMatrix);\r\n\r\n            this._markSyncedWithParent();\r\n        }\r\n\r\n        // Cache the determinant\r\n        this._worldMatrixDeterminantIsDirty = true;\r\n\r\n        return this._worldMatrix;\r\n    }\r\n\r\n    /**\r\n     * Gets the minZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the min for\r\n     * @returns the depth min z\r\n     */\r\n    public getDepthMinZ(activeCamera: Nullable<Camera>): number {\r\n        return this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera?.minZ || Constants.ShadowMinZ;\r\n    }\r\n\r\n    /**\r\n     * Gets the maxZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the max for\r\n     * @returns the depth max z\r\n     */\r\n    public getDepthMaxZ(activeCamera: Nullable<Camera>): number {\r\n        return this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera?.maxZ || Constants.ShadowMaxZ;\r\n    }\r\n\r\n    /**\r\n     * Sets the shadow projection matrix in parameter to the generated projection matrix.\r\n     * @param matrix The matrix to updated with the projection information\r\n     * @param viewMatrix The transform matrix of the light\r\n     * @param renderList The list of mesh to render in the map\r\n     * @returns The current light\r\n     */\r\n    public setShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): IShadowLight {\r\n        if (this.customProjectionMatrixBuilder) {\r\n            this.customProjectionMatrixBuilder(viewMatrix, renderList, matrix);\r\n        } else {\r\n            this._setDefaultShadowProjectionMatrix(matrix, viewMatrix, renderList);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /** @internal */\r\n    protected override _syncParentEnabledState() {\r\n        super._syncParentEnabledState();\r\n        if (!this.parent || !this.parent.getWorldMatrix) {\r\n            (this.transformedPosition as any) = null;\r\n            (this.transformedDirection as any) = null;\r\n        }\r\n    }\r\n\r\n    protected _viewMatrix: Matrix = Matrix.Identity();\r\n    protected _projectionMatrix: Matrix = Matrix.Identity();\r\n\r\n    /**\r\n     * Returns the view matrix.\r\n     * @param faceIndex The index of the face for which we want to extract the view matrix. Only used for point light types.\r\n     * @returns The view matrix. Can be null, if a view matrix cannot be defined for the type of light considered (as for a hemispherical light, for example).\r\n     */\r\n    public override getViewMatrix(faceIndex?: number): Nullable<Matrix> {\r\n        const lightDirection = TmpVectors.Vector3[0];\r\n\r\n        let lightPosition = this.position;\r\n        if (this.computeTransformedInformation()) {\r\n            lightPosition = this.transformedPosition;\r\n        }\r\n\r\n        Vector3.NormalizeToRef(this.getShadowDirection(faceIndex), lightDirection);\r\n        if (Math.abs(Vector3.Dot(lightDirection, Vector3.Up())) === 1.0) {\r\n            lightDirection.z = 0.0000000000001; // Required to avoid perfectly perpendicular light\r\n        }\r\n\r\n        const lightTarget = TmpVectors.Vector3[1];\r\n        lightPosition.addToRef(lightDirection, lightTarget);\r\n\r\n        Matrix.LookAtLHToRef(lightPosition, lightTarget, Vector3.Up(), this._viewMatrix);\r\n\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    /**\r\n     * Returns the projection matrix.\r\n     * Note that viewMatrix and renderList are optional and are only used by lights that calculate the projection matrix from a list of meshes (e.g. directional lights with automatic extents calculation).\r\n     * @param viewMatrix The view transform matrix of the light (optional).\r\n     * @param renderList The list of meshes to take into account when calculating the projection matrix (optional).\r\n     * @returns The projection matrix. Can be null, if a projection matrix cannot be defined for the type of light considered (as for a hemispherical light, for example).\r\n     */\r\n    public override getProjectionMatrix(viewMatrix?: Matrix, renderList?: Array<AbstractMesh>): Nullable<Matrix> {\r\n        this.setShadowProjectionMatrix(this._projectionMatrix, viewMatrix ?? this._viewMatrix, renderList ?? []);\r\n\r\n        return this._projectionMatrix;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAGnE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAEnE,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;;;;;;AAsHpC,MAAgB,WAAY,gKAAQ,QAAK;IAA/C,aAAA;;QA2FY,IAAA,CAAA,4BAA4B,GAAY,IAAI,CAAC;QA0L3C,IAAA,CAAA,WAAW,kKAAW,SAAM,CAAC,QAAQ,EAAE,CAAC;QACxC,IAAA,CAAA,iBAAiB,kKAAW,SAAM,CAAC,QAAQ,EAAE,CAAC;IAwC5D,CAAC;IA1Ta,YAAY,CAAC,KAAc,EAAA;QACjC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IAC3B,CAAC;IACD;;;OAGG,CAEH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD;;;OAGG,CACH,IAAW,QAAQ,CAAC,KAAc,EAAA;QAC9B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAGS,aAAa,CAAC,KAAc,EAAA;QAClC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IACD;;;OAGG,CAEH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD;;;OAGG,CACH,IAAW,SAAS,CAAC,KAAc,EAAA;QAC/B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAGD;;OAEG,CAEH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;OAEG,CACH,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IAGD;;OAEG,CAEH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;OAEG,CACH,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IAoBD;;;OAGG,CACI,6BAA6B,GAAA;QAChC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC5B,IAAI,CAAC,mBAAmB,GAAG,yKAAO,CAAC,IAAI,EAAE,CAAC;YAC9C,CAAC;2KACD,UAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEzG,oCAAoC;YACpC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC7B,IAAI,CAAC,oBAAoB,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;gBAC/C,CAAC;8KACD,WAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC1G,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACH,6DAA6D;IACtD,kBAAkB,CAAC,SAAkB,EAAA;QACxC,OAAO,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;IAClF,CAAC;IAED;;;OAGG,CACa,mBAAmB,GAAA;QAC/B,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;IAC/E,CAAC;IAED;;;;OAIG,CACI,oBAAoB,CAAC,MAAe,EAAA;QACvC,IAAI,CAAC,SAAS,GAAG,yKAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG,CACI,WAAW,GAAA;QACd,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC3B,MAAM,KAAK,kKAAG,UAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,+JAAE,OAAI,CAAC,CAAC,CAAC,CAAC;QACpD,MAAM,KAAK,kKAAG,UAAO,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACnD,sKAAO,UAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAClE,CAAC;IAED;;;OAGG,CACI,QAAQ,GAAA;QACX,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACI,2BAA2B,GAAA;QAC9B,OAAO,IAAI,CAAC,4BAA4B,CAAC;IAC7C,CAAC;IAED;;OAEG,CACI,4BAA4B,GAAA;QAC/B,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;IAC7C,CAAC;IAED,cAAA,EAAgB,CACA,UAAU,GAAA;QACtB,KAAK,CAAC,UAAU,EAAE,CAAC;QAEnB,IAAI,CAAC,MAAM,CAAC,QAAQ,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED,cAAA,EAAgB,CACA,eAAe,GAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACa,kBAAkB,CAAC,KAAe,EAAA;QAC9C,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;YACtD,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,IAAI,CAAC,YAAY,kKAAG,SAAM,CAAC,QAAQ,EAAE,CAAC;QAC1C,CAAC;QAED,wKAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9F,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC5C,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAEjF,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACjC,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,8BAA8B,GAAG,IAAI,CAAC;QAE3C,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,YAA8B,EAAA;QAC9C,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC;IACxG,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,YAA8B,EAAA;QAC9C,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC;IACxG,CAAC;IAED;;;;;;OAMG,CACI,yBAAyB,CAAC,MAAc,EAAE,UAAkB,EAAE,UAA+B,EAAA;QAChG,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,IAAI,CAAC,6BAA6B,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;QACvE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,iCAAiC,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAC3E,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACG,uBAAuB,GAAA;QACtC,KAAK,CAAC,uBAAuB,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC7C,IAAI,CAAC,mBAA2B,GAAG,IAAI,CAAC;YACxC,IAAI,CAAC,oBAA4B,GAAG,IAAI,CAAC;QAC9C,CAAC;IACL,CAAC;IAKD;;;;OAIG,CACa,aAAa,CAAC,SAAkB,EAAA;QAC5C,MAAM,cAAc,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE7C,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;QAClC,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACvC,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC7C,CAAC;uKAED,UAAO,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,cAAc,CAAC,CAAC;QAC3E,IAAI,IAAI,CAAC,GAAG,gKAAC,UAAO,CAAC,GAAG,CAAC,cAAc,iKAAE,UAAO,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC9D,cAAc,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,kDAAkD;QAC1F,CAAC;QAED,MAAM,WAAW,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C,aAAa,CAAC,QAAQ,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;uKAEpD,SAAM,CAAC,aAAa,CAAC,aAAa,EAAE,WAAW,gKAAE,WAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEjF,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;;;;;OAMG,CACa,mBAAmB,CAAC,UAAmB,EAAE,UAAgC,EAAA;QACrF,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,EAAE,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;QAEzG,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;CACJ;CAlTG,oKAAA,EAAA;kKADC,qBAAA,AAAkB,EAAE;2CAGpB;AAkBD,qKAAA,EAAA;kKADC,qBAAA,AAAkB,EAAE;4CAGpB;CAcD,oKAAA,EAAA;kKADC,YAAA,AAAS,EAAE;6CAGX;wJAcD,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;6CAGX", "debugId": null}}, {"offset": {"line": 3660, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/directionalLight.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/directionalLight.ts"], "sourcesContent": ["import { serialize } from \"../Misc/decorators\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Light } from \"./light\";\r\nimport { ShadowLight } from \"./shadowLight\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Constants } from \"core/Engines/constants\";\r\n\r\nNode.AddNodeConstructor(\"Light_Type_1\", (name, scene) => {\r\n    return () => new DirectionalLight(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * A directional light is defined by a direction (what a surprise!).\r\n * The light is emitted from everywhere in the specified direction, and has an infinite range.\r\n * An example of a directional light is when a distance planet is lit by the apparently parallel lines of light from its sun. Light in a downward direction will light the top of an object.\r\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n */\r\nexport class DirectionalLight extends ShadowLight {\r\n    private _shadowFrustumSize = 0;\r\n    /**\r\n     * Fix frustum size for the shadow generation. This is disabled if the value is 0.\r\n     */\r\n    @serialize()\r\n    public get shadowFrustumSize(): number {\r\n        return this._shadowFrustumSize;\r\n    }\r\n    /**\r\n     * Specifies a fix frustum size for the shadow generation.\r\n     */\r\n    public set shadowFrustumSize(value: number) {\r\n        this._shadowFrustumSize = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    private _shadowOrthoScale = 0.1;\r\n    /**\r\n     * Gets the shadow projection scale against the optimal computed one.\r\n     * 0.1 by default which means that the projection window is increase by 10% from the optimal size.\r\n     * This does not impact in fixed frustum size (shadowFrustumSize being set)\r\n     */\r\n    @serialize()\r\n    public get shadowOrthoScale(): number {\r\n        return this._shadowOrthoScale;\r\n    }\r\n    /**\r\n     * Sets the shadow projection scale against the optimal computed one.\r\n     * 0.1 by default which means that the projection window is increase by 10% from the optimal size.\r\n     * This does not impact in fixed frustum size (shadowFrustumSize being set)\r\n     */\r\n    public set shadowOrthoScale(value: number) {\r\n        this._shadowOrthoScale = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    /**\r\n     * Automatically compute the projection matrix to best fit (including all the casters)\r\n     * on each frame.\r\n     */\r\n    @serialize()\r\n    public autoUpdateExtends = true;\r\n\r\n    /**\r\n     * Automatically compute the shadowMinZ and shadowMaxZ for the projection matrix to best fit (including all the casters)\r\n     * on each frame. autoUpdateExtends must be set to true for this to work\r\n     */\r\n    @serialize()\r\n    public autoCalcShadowZBounds = false;\r\n\r\n    // Cache\r\n    @serialize(\"orthoLeft\")\r\n    private _orthoLeft = Number.MAX_VALUE;\r\n    @serialize(\"orthoRight\")\r\n    private _orthoRight = Number.MIN_VALUE;\r\n    @serialize(\"orthoTop\")\r\n    private _orthoTop = Number.MIN_VALUE;\r\n    @serialize(\"orthoBottom\")\r\n    private _orthoBottom = Number.MAX_VALUE;\r\n\r\n    /**\r\n     * Gets or sets the orthoLeft property used to build the light frustum\r\n     */\r\n    public get orthoLeft(): number {\r\n        return this._orthoLeft;\r\n    }\r\n\r\n    public set orthoLeft(left: number) {\r\n        this._orthoLeft = left;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the orthoRight property used to build the light frustum\r\n     */\r\n    public get orthoRight(): number {\r\n        return this._orthoRight;\r\n    }\r\n\r\n    public set orthoRight(right: number) {\r\n        this._orthoRight = right;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the orthoTop property used to build the light frustum\r\n     */\r\n    public get orthoTop(): number {\r\n        return this._orthoTop;\r\n    }\r\n\r\n    public set orthoTop(top: number) {\r\n        this._orthoTop = top;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the orthoBottom property used to build the light frustum\r\n     */\r\n    public get orthoBottom(): number {\r\n        return this._orthoBottom;\r\n    }\r\n\r\n    public set orthoBottom(bottom: number) {\r\n        this._orthoBottom = bottom;\r\n    }\r\n\r\n    /**\r\n     * Creates a DirectionalLight object in the scene, oriented towards the passed direction (Vector3).\r\n     * The directional light is emitted from everywhere in the given direction.\r\n     * It can cast shadows.\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n     * @param name The friendly name of the light\r\n     * @param direction The direction of the light\r\n     * @param scene The scene the light belongs to\r\n     */\r\n    constructor(name: string, direction: Vector3, scene?: Scene) {\r\n        super(name, scene);\r\n        this.position = direction.scale(-1.0);\r\n        this.direction = direction;\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"DirectionalLight\".\r\n     * @returns The class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"DirectionalLight\";\r\n    }\r\n\r\n    /**\r\n     * Returns the integer 1.\r\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public override getTypeID(): number {\r\n        return Light.LIGHTTYPEID_DIRECTIONALLIGHT;\r\n    }\r\n\r\n    /**\r\n     * Sets the passed matrix \"matrix\" as projection matrix for the shadows cast by the light according to the passed view matrix.\r\n     * Returns the DirectionalLight Shadow projection matrix.\r\n     * @param matrix\r\n     * @param viewMatrix\r\n     * @param renderList\r\n     */\r\n    protected _setDefaultShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): void {\r\n        if (this.shadowFrustumSize > 0) {\r\n            this._setDefaultFixedFrustumShadowProjectionMatrix(matrix);\r\n        } else {\r\n            this._setDefaultAutoExtendShadowProjectionMatrix(matrix, viewMatrix, renderList);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the passed matrix \"matrix\" as fixed frustum projection matrix for the shadows cast by the light according to the passed view matrix.\r\n     * Returns the DirectionalLight Shadow projection matrix.\r\n     * @param matrix\r\n     */\r\n    protected _setDefaultFixedFrustumShadowProjectionMatrix(matrix: Matrix): void {\r\n        const activeCamera = this.getScene().activeCamera;\r\n\r\n        if (!activeCamera) {\r\n            return;\r\n        }\r\n\r\n        Matrix.OrthoLHToRef(\r\n            this.shadowFrustumSize,\r\n            this.shadowFrustumSize,\r\n            this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ,\r\n            this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ,\r\n            matrix,\r\n            this.getScene().getEngine().isNDCHalfZRange\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Sets the passed matrix \"matrix\" as auto extend projection matrix for the shadows cast by the light according to the passed view matrix.\r\n     * Returns the DirectionalLight Shadow projection matrix.\r\n     * @param matrix\r\n     * @param viewMatrix\r\n     * @param renderList\r\n     */\r\n    protected _setDefaultAutoExtendShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): void {\r\n        const activeCamera = this.getScene().activeCamera;\r\n\r\n        // Check extends\r\n        if (this.autoUpdateExtends || this._orthoLeft === Number.MAX_VALUE) {\r\n            const tempVector3 = Vector3.Zero();\r\n\r\n            this._orthoLeft = Number.MAX_VALUE;\r\n            this._orthoRight = -Number.MAX_VALUE;\r\n            this._orthoTop = -Number.MAX_VALUE;\r\n            this._orthoBottom = Number.MAX_VALUE;\r\n\r\n            let shadowMinZ = Number.MAX_VALUE;\r\n            let shadowMaxZ = -Number.MAX_VALUE;\r\n\r\n            for (let meshIndex = 0; meshIndex < renderList.length; meshIndex++) {\r\n                const mesh = renderList[meshIndex];\r\n\r\n                if (!mesh) {\r\n                    continue;\r\n                }\r\n\r\n                const boundingInfo = mesh.getBoundingInfo();\r\n                const boundingBox = boundingInfo.boundingBox;\r\n\r\n                for (let index = 0; index < boundingBox.vectorsWorld.length; index++) {\r\n                    Vector3.TransformCoordinatesToRef(boundingBox.vectorsWorld[index], viewMatrix, tempVector3);\r\n\r\n                    if (tempVector3.x < this._orthoLeft) {\r\n                        this._orthoLeft = tempVector3.x;\r\n                    }\r\n                    if (tempVector3.y < this._orthoBottom) {\r\n                        this._orthoBottom = tempVector3.y;\r\n                    }\r\n\r\n                    if (tempVector3.x > this._orthoRight) {\r\n                        this._orthoRight = tempVector3.x;\r\n                    }\r\n                    if (tempVector3.y > this._orthoTop) {\r\n                        this._orthoTop = tempVector3.y;\r\n                    }\r\n                    if (this.autoCalcShadowZBounds) {\r\n                        if (tempVector3.z < shadowMinZ) {\r\n                            shadowMinZ = tempVector3.z;\r\n                        }\r\n                        if (tempVector3.z > shadowMaxZ) {\r\n                            shadowMaxZ = tempVector3.z;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (this.autoCalcShadowZBounds) {\r\n                this._shadowMinZ = shadowMinZ;\r\n                this._shadowMaxZ = shadowMaxZ;\r\n            }\r\n        }\r\n\r\n        const xOffset = this._orthoRight - this._orthoLeft;\r\n        const yOffset = this._orthoTop - this._orthoBottom;\r\n\r\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera?.minZ || Constants.ShadowMinZ;\r\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera?.maxZ || Constants.ShadowMaxZ;\r\n\r\n        const useReverseDepthBuffer = this.getScene().getEngine().useReverseDepthBuffer;\r\n\r\n        Matrix.OrthoOffCenterLHToRef(\r\n            this._orthoLeft - xOffset * this.shadowOrthoScale,\r\n            this._orthoRight + xOffset * this.shadowOrthoScale,\r\n            this._orthoBottom - yOffset * this.shadowOrthoScale,\r\n            this._orthoTop + yOffset * this.shadowOrthoScale,\r\n            useReverseDepthBuffer ? maxZ : minZ,\r\n            useReverseDepthBuffer ? minZ : maxZ,\r\n            matrix,\r\n            this.getScene().getEngine().isNDCHalfZRange\r\n        );\r\n    }\r\n\r\n    protected _buildUniformLayout(): void {\r\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\r\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\r\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\r\n        this._uniformBuffer.create();\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Effect object with the DirectionalLight transformed position (or position if not parented) and the passed name.\r\n     * @param effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The directional light\r\n     */\r\n    public transferToEffect(effect: Effect, lightIndex: string): DirectionalLight {\r\n        if (this.computeTransformedInformation()) {\r\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.transformedDirection.x, this.transformedDirection.y, this.transformedDirection.z, 1, lightIndex);\r\n            return this;\r\n        }\r\n        this._uniformBuffer.updateFloat4(\"vLightData\", this.direction.x, this.direction.y, this.direction.z, 1, lightIndex);\r\n        return this;\r\n    }\r\n\r\n    public transferToNodeMaterialEffect(effect: Effect, lightDataUniformName: string): Light {\r\n        if (this.computeTransformedInformation()) {\r\n            effect.setFloat3(lightDataUniformName, this.transformedDirection.x, this.transformedDirection.y, this.transformedDirection.z);\r\n            return this;\r\n        }\r\n\r\n        effect.setFloat3(lightDataUniformName, this.direction.x, this.direction.y, this.direction.z);\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the minZ used for shadow according to both the scene and the light.\r\n     *\r\n     * Values are fixed on directional lights as it relies on an ortho projection hence the need to convert being\r\n     * -1 and 1 to 0 and 1 doing (depth + min) / (min + max) -> (depth + 1) / (1 + 1) -> (depth * 0.5) + 0.5.\r\n     * (when not using reverse depth buffer / NDC half Z range)\r\n     * @param _activeCamera The camera we are returning the min for (not used)\r\n     * @returns the depth min z\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public override getDepthMinZ(_activeCamera: Nullable<Camera>): number {\r\n        const engine = this._scene.getEngine();\r\n        return !engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? 0 : 1;\r\n    }\r\n\r\n    /**\r\n     * Gets the maxZ used for shadow according to both the scene and the light.\r\n     *\r\n     * Values are fixed on directional lights as it relies on an ortho projection hence the need to convert being\r\n     * -1 and 1 to 0 and 1 doing (depth + min) / (min + max) -> (depth + 1) / (1 + 1) -> (depth * 0.5) + 0.5.\r\n     * (when not using reverse depth buffer / NDC half Z range)\r\n     * @param _activeCamera The camera we are returning the max for\r\n     * @returns the depth max z\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public override getDepthMaxZ(_activeCamera: Nullable<Camera>): number {\r\n        const engine = this._scene.getEngine();\r\n        return engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? 0 : 1;\r\n    }\r\n\r\n    /**\r\n     * Prepares the list of defines specific to the light type.\r\n     * @param defines the list of defines\r\n     * @param lightIndex defines the index of the light for the effect\r\n     */\r\n    public prepareLightSpecificDefines(defines: any, lightIndex: number): void {\r\n        defines[\"DIRLIGHT\" + lightIndex] = true;\r\n    }\r\n}\r\n\r\n// Register Class Name\r\nRegisterClass(\"BABYLON.DirectionalLight\", DirectionalLight);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAG/C,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;;;;;;;;4IAIlD,OAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,CAAG,CAAD,GAAK,gBAAgB,CAAC,IAAI,iKAAE,UAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC;AAQG,MAAO,gBAAiB,sKAAQ,cAAW;IAE7C;;OAEG,CAEH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACD;;OAEG,CACH,IAAW,iBAAiB,CAAC,KAAa,EAAA;QACtC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IAGD;;;;OAIG,CAEH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;;;OAIG,CACH,IAAW,gBAAgB,CAAC,KAAa,EAAA;QACrC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IA0BD;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,IAAY,EAAA;QAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,GAAW,EAAA;QAC3B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;IACzB,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,MAAc,EAAA;QACjC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;IAC/B,CAAC;IAED;;;;;;;;OAQG,CACH,YAAY,IAAY,EAAE,SAAkB,EAAE,KAAa,CAAA;QACvD,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAlHf,IAAA,CAAA,kBAAkB,GAAG,CAAC,CAAC;QAgBvB,IAAA,CAAA,iBAAiB,GAAG,GAAG,CAAC;QAoBhC;;;WAGG,CAEI,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAEhC;;;WAGG,CAEI,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAErC,QAAQ;QAEA,IAAA,CAAA,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;QAE9B,IAAA,CAAA,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;QAE/B,IAAA,CAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QAE7B,IAAA,CAAA,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC;QAyDpC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACH,gEAAgE;IAChD,SAAS,GAAA;QACrB,8JAAO,QAAK,CAAC,4BAA4B,CAAC;IAC9C,CAAC;IAED;;;;;;OAMG,CACO,iCAAiC,CAAC,MAAc,EAAE,UAAkB,EAAE,UAA+B,EAAA;QAC3G,IAAI,IAAI,CAAC,iBAAiB,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,6CAA6C,CAAC,MAAM,CAAC,CAAC;QAC/D,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,2CAA2C,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACrF,CAAC;IACL,CAAC;IAED;;;;OAIG,CACO,6CAA6C,CAAC,MAAc,EAAA;QAClE,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;QAElD,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;uKAED,SAAM,CAAC,YAAY,CACf,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,EACnE,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,EACnE,MAAM,EACN,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,eAAe,CAC9C,CAAC;IACN,CAAC;IAED;;;;;;OAMG,CACO,2CAA2C,CAAC,MAAc,EAAE,UAAkB,EAAE,UAA+B,EAAA;QACrH,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;QAElD,gBAAgB;QAChB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;YACjE,MAAM,WAAW,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;YAEnC,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;YACnC,IAAI,CAAC,WAAW,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;YACrC,IAAI,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;YACnC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC;YAErC,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;YAClC,IAAI,UAAU,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;YAEnC,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;gBACjE,MAAM,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;gBAEnC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,SAAS;gBACb,CAAC;gBAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC5C,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;gBAE7C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;mLACnE,UAAO,CAAC,yBAAyB,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;oBAE5F,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;wBAClC,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC;oBACpC,CAAC;oBACD,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBACpC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC;oBACtC,CAAC;oBAED,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;wBACnC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC;oBACrC,CAAC;oBACD,IAAI,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;wBACjC,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC;oBACnC,CAAC;oBACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;wBAC7B,IAAI,WAAW,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC;4BAC7B,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC;wBAC/B,CAAC;wBACD,IAAI,WAAW,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC;4BAC7B,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC;wBAC/B,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;YAClC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;QAEnD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC;QAC1G,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC;QAE1G,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAAC;uKAEhF,SAAM,CAAC,qBAAqB,CACxB,IAAI,CAAC,UAAU,GAAG,OAAO,GAAG,IAAI,CAAC,gBAAgB,EACjD,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAClD,IAAI,CAAC,YAAY,GAAG,OAAO,GAAG,IAAI,CAAC,gBAAgB,EACnD,IAAI,CAAC,SAAS,GAAG,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAChD,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,MAAM,EACN,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,eAAe,CAC9C,CAAC;IACN,CAAC;IAES,mBAAmB,GAAA;QACzB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,MAAc,EAAE,UAAkB,EAAA;QACtD,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;YACrJ,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QACpH,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4BAA4B,CAAC,MAAc,EAAE,oBAA4B,EAAA;QAC5E,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;YAC9H,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7F,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG,CACH,6DAA6D;IAC7C,YAAY,CAAC,aAA+B,EAAA;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,OAAO,CAAC,MAAM,CAAC,qBAAqB,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;;;;OAQG,CACH,6DAA6D;IAC7C,YAAY,CAAC,aAA+B,EAAA;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC,qBAAqB,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;;;OAIG,CACI,2BAA2B,CAAC,OAAY,EAAE,UAAkB,EAAA;QAC/D,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;IAC5C,CAAC;CACJ;IArUG,iKAAA,EAAA;kKADC,YAAS,AAAT,EAAW;yDAGX;wJAgBD,aAAA,EAAA;IADC,0KAAA,AAAS,EAAE;wDAGX;wJAgBM,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;2DACoB;wJAOzB,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;+DACyB;AAI7B,qKAAA,EAAA;kKADP,YAAA,AAAS,EAAC,WAAW,CAAC;oDACe;CAE9B,oKAAA,EAAA;kKADP,YAAA,AAAS,EAAC,YAAY,CAAC;qDACe;wJAE/B,aAAA,EAAA;KADP,yKAAA,AAAS,EAAC,UAAU,CAAC;mDACe;wJAE7B,aAAA,EAAA;kKADP,YAAA,AAAS,EAAC,aAAa,CAAC;sDACe;AAkR5C,sBAAsB;6JACtB,gBAAA,AAAa,EAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 3957, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/spotLight.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/spotLight.ts"], "sourcesContent": ["import { serialize, serializeAsTexture } from \"../Misc/decorators\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { Light } from \"./light\";\r\nimport { ShadowLight } from \"./shadowLight\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { ProceduralTexture } from \"../Materials/Textures/Procedurals/proceduralTexture\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport { Constants } from \"core/Engines/constants\";\r\n\r\nNode.AddNodeConstructor(\"Light_Type_2\", (name, scene) => {\r\n    return () => new SpotLight(name, Vector3.Zero(), Vector3.Zero(), 0, 0, scene);\r\n});\r\n\r\n/**\r\n * A spot light is defined by a position, a direction, an angle, and an exponent.\r\n * These values define a cone of light starting from the position, emitting toward the direction.\r\n * The angle, in radians, defines the size (field of illumination) of the spotlight's conical beam,\r\n * and the exponent defines the speed of the decay of the light with distance (reach).\r\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n */\r\nexport class SpotLight extends ShadowLight {\r\n    /*\r\n        upVector , rightVector and direction will form the coordinate system for this spot light.\r\n        These three vectors will be used as projection matrix when doing texture projection.\r\n\r\n        Also we have the following rules always holds:\r\n        direction cross up   = right\r\n        right cross direction = up\r\n        up cross right       = forward\r\n\r\n        light_near and light_far will control the range of the texture projection. If a plane is\r\n        out of the range in spot light space, there is no texture projection.\r\n    */\r\n\r\n    private _angle: number;\r\n    private _innerAngle: number = 0;\r\n    private _cosHalfAngle: number;\r\n\r\n    private _lightAngleScale: number;\r\n    private _lightAngleOffset: number;\r\n\r\n    private _iesProfileTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Gets or sets the IES profile texture used to create the spotlight\r\n     * @see https://playground.babylonjs.com/#UIAXAU#1\r\n     */\r\n    public get iesProfileTexture(): Nullable<BaseTexture> {\r\n        return this._iesProfileTexture;\r\n    }\r\n\r\n    public set iesProfileTexture(value: Nullable<BaseTexture>) {\r\n        if (this._iesProfileTexture === value) {\r\n            return;\r\n        }\r\n\r\n        this._iesProfileTexture = value;\r\n\r\n        if (this._iesProfileTexture && SpotLight._IsTexture(this._iesProfileTexture)) {\r\n            this._iesProfileTexture.onLoadObservable.addOnce(() => {\r\n                this._markMeshesAsLightDirty();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the cone angle of the spot light in Radians.\r\n     */\r\n    @serialize()\r\n    public get angle(): number {\r\n        return this._angle;\r\n    }\r\n    /**\r\n     * Sets the cone angle of the spot light in Radians.\r\n     */\r\n    public set angle(value: number) {\r\n        this._angle = value;\r\n        this._cosHalfAngle = Math.cos(value * 0.5);\r\n        this._projectionTextureProjectionLightDirty = true;\r\n        this.forceProjectionMatrixCompute();\r\n        this._computeAngleValues();\r\n    }\r\n\r\n    /**\r\n     * Only used in gltf falloff mode, this defines the angle where\r\n     * the directional falloff will start before cutting at angle which could be seen\r\n     * as outer angle.\r\n     */\r\n    @serialize()\r\n    public get innerAngle(): number {\r\n        return this._innerAngle;\r\n    }\r\n    /**\r\n     * Only used in gltf falloff mode, this defines the angle where\r\n     * the directional falloff will start before cutting at angle which could be seen\r\n     * as outer angle.\r\n     */\r\n    public set innerAngle(value: number) {\r\n        this._innerAngle = value;\r\n        this._computeAngleValues();\r\n    }\r\n\r\n    private _shadowAngleScale: number;\r\n    /**\r\n     * Allows scaling the angle of the light for shadow generation only.\r\n     */\r\n    @serialize()\r\n    public get shadowAngleScale(): number {\r\n        return this._shadowAngleScale;\r\n    }\r\n    /**\r\n     * Allows scaling the angle of the light for shadow generation only.\r\n     */\r\n    public set shadowAngleScale(value: number) {\r\n        this._shadowAngleScale = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    /**\r\n     * The light decay speed with the distance from the emission spot.\r\n     */\r\n    @serialize()\r\n    public exponent: number;\r\n\r\n    private _projectionTextureMatrix = Matrix.Zero();\r\n    /**\r\n     * Allows reading the projection texture\r\n     */\r\n    public get projectionTextureMatrix(): Matrix {\r\n        return this._projectionTextureMatrix;\r\n    }\r\n\r\n    protected _projectionTextureLightNear: number = 1e-6;\r\n    /**\r\n     * Gets the near clip of the Spotlight for texture projection.\r\n     */\r\n    @serialize()\r\n    public get projectionTextureLightNear(): number {\r\n        return this._projectionTextureLightNear;\r\n    }\r\n    /**\r\n     * Sets the near clip of the Spotlight for texture projection.\r\n     */\r\n    public set projectionTextureLightNear(value: number) {\r\n        this._projectionTextureLightNear = value;\r\n        this._projectionTextureProjectionLightDirty = true;\r\n    }\r\n\r\n    protected _projectionTextureLightFar: number = 1000.0;\r\n    /**\r\n     * Gets the far clip of the Spotlight for texture projection.\r\n     */\r\n    @serialize()\r\n    public get projectionTextureLightFar(): number {\r\n        return this._projectionTextureLightFar;\r\n    }\r\n    /**\r\n     * Sets the far clip of the Spotlight for texture projection.\r\n     */\r\n    public set projectionTextureLightFar(value: number) {\r\n        this._projectionTextureLightFar = value;\r\n        this._projectionTextureProjectionLightDirty = true;\r\n    }\r\n\r\n    protected _projectionTextureUpDirection: Vector3 = Vector3.Up();\r\n    /**\r\n     * Gets the Up vector of the Spotlight for texture projection.\r\n     */\r\n    @serialize()\r\n    public get projectionTextureUpDirection(): Vector3 {\r\n        return this._projectionTextureUpDirection;\r\n    }\r\n    /**\r\n     * Sets the Up vector of the Spotlight for texture projection.\r\n     */\r\n    public set projectionTextureUpDirection(value: Vector3) {\r\n        this._projectionTextureUpDirection = value;\r\n        this._projectionTextureProjectionLightDirty = true;\r\n    }\r\n\r\n    @serializeAsTexture(\"projectedLightTexture\")\r\n    private _projectionTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Gets the projection texture of the light.\r\n     */\r\n    public get projectionTexture(): Nullable<BaseTexture> {\r\n        return this._projectionTexture;\r\n    }\r\n    /**\r\n     * Sets the projection texture of the light.\r\n     */\r\n    public set projectionTexture(value: Nullable<BaseTexture>) {\r\n        if (this._projectionTexture === value) {\r\n            return;\r\n        }\r\n        this._projectionTexture = value;\r\n        this._projectionTextureDirty = true;\r\n        if (this._projectionTexture && !this._projectionTexture.isReady()) {\r\n            if (SpotLight._IsProceduralTexture(this._projectionTexture)) {\r\n                this._projectionTexture.getEffect().executeWhenCompiled(() => {\r\n                    this._markMeshesAsLightDirty();\r\n                });\r\n            } else if (SpotLight._IsTexture(this._projectionTexture)) {\r\n                this._projectionTexture.onLoadObservable.addOnce(() => {\r\n                    this._markMeshesAsLightDirty();\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _IsProceduralTexture(texture: BaseTexture): texture is ProceduralTexture {\r\n        return (texture as ProceduralTexture).onGeneratedObservable !== undefined;\r\n    }\r\n\r\n    private static _IsTexture(texture: BaseTexture): texture is Texture {\r\n        return (texture as Texture).onLoadObservable !== undefined;\r\n    }\r\n\r\n    private _projectionTextureViewLightDirty = true;\r\n    private _projectionTextureProjectionLightDirty = true;\r\n    private _projectionTextureDirty = true;\r\n    private _projectionTextureViewTargetVector = Vector3.Zero();\r\n    private _projectionTextureViewLightMatrix = Matrix.Zero();\r\n\r\n    private _projectionTextureProjectionLightMatrix = Matrix.Zero();\r\n    /**\r\n     * Gets or sets the light projection matrix as used by the projection texture\r\n     */\r\n    public get projectionTextureProjectionLightMatrix(): Matrix {\r\n        return this._projectionTextureProjectionLightMatrix;\r\n    }\r\n\r\n    public set projectionTextureProjectionLightMatrix(projection: Matrix) {\r\n        this._projectionTextureProjectionLightMatrix = projection;\r\n        this._projectionTextureProjectionLightDirty = false;\r\n        this._projectionTextureDirty = true;\r\n    }\r\n\r\n    private _projectionTextureScalingMatrix = Matrix.FromValues(0.5, 0.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.5, 0.5, 0.5, 1.0);\r\n\r\n    /**\r\n     * Creates a SpotLight object in the scene. A spot light is a simply light oriented cone.\r\n     * It can cast shadows.\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n     * @param name The light friendly name\r\n     * @param position The position of the spot light in the scene\r\n     * @param direction The direction of the light in the scene\r\n     * @param angle The cone angle of the light in Radians\r\n     * @param exponent The light decay speed with the distance from the emission spot\r\n     * @param scene The scene the lights belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, direction: Vector3, angle: number, exponent: number, scene?: Scene) {\r\n        super(name, scene);\r\n\r\n        this.position = position;\r\n        this.direction = direction;\r\n        this.angle = angle;\r\n        this.exponent = exponent;\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"SpotLight\".\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"SpotLight\";\r\n    }\r\n\r\n    /**\r\n     * Returns the integer 2.\r\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public override getTypeID(): number {\r\n        return Light.LIGHTTYPEID_SPOTLIGHT;\r\n    }\r\n\r\n    /**\r\n     * Overrides the direction setter to recompute the projection texture view light Matrix.\r\n     * @param value\r\n     */\r\n    protected override _setDirection(value: Vector3) {\r\n        super._setDirection(value);\r\n        this._projectionTextureViewLightDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Overrides the position setter to recompute the projection texture view light Matrix.\r\n     * @param value\r\n     */\r\n    protected override _setPosition(value: Vector3) {\r\n        super._setPosition(value);\r\n        this._projectionTextureViewLightDirty = true;\r\n    }\r\n\r\n    /**\r\n     * Sets the passed matrix \"matrix\" as perspective projection matrix for the shadows and the passed view matrix with the fov equal to the SpotLight angle and and aspect ratio of 1.0.\r\n     * Returns the SpotLight.\r\n     * @param matrix\r\n     * @param viewMatrix\r\n     * @param renderList\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _setDefaultShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): void {\r\n        const activeCamera = this.getScene().activeCamera;\r\n\r\n        if (!activeCamera) {\r\n            return;\r\n        }\r\n\r\n        this._shadowAngleScale = this._shadowAngleScale || 1;\r\n        const angle = this._shadowAngleScale * this._angle;\r\n\r\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ;\r\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ;\r\n\r\n        const useReverseDepthBuffer = this.getScene().getEngine().useReverseDepthBuffer;\r\n\r\n        Matrix.PerspectiveFovLHToRef(\r\n            angle,\r\n            1.0,\r\n            useReverseDepthBuffer ? maxZ : minZ,\r\n            useReverseDepthBuffer ? minZ : maxZ,\r\n            matrix,\r\n            true,\r\n            this._scene.getEngine().isNDCHalfZRange,\r\n            undefined,\r\n            useReverseDepthBuffer\r\n        );\r\n    }\r\n\r\n    protected _computeProjectionTextureViewLightMatrix(): void {\r\n        this._projectionTextureViewLightDirty = false;\r\n        this._projectionTextureDirty = true;\r\n\r\n        this.getAbsolutePosition().addToRef(this.getShadowDirection(), this._projectionTextureViewTargetVector);\r\n        Matrix.LookAtLHToRef(this.getAbsolutePosition(), this._projectionTextureViewTargetVector, this._projectionTextureUpDirection, this._projectionTextureViewLightMatrix);\r\n    }\r\n\r\n    protected _computeProjectionTextureProjectionLightMatrix(): void {\r\n        this._projectionTextureProjectionLightDirty = false;\r\n        this._projectionTextureDirty = true;\r\n\r\n        const lightFar = this.projectionTextureLightFar;\r\n        const lightNear = this.projectionTextureLightNear;\r\n\r\n        const p = lightFar / (lightFar - lightNear);\r\n        const q = -p * lightNear;\r\n        const s = 1.0 / Math.tan(this._angle / 2.0);\r\n        const a = 1.0;\r\n\r\n        Matrix.FromValuesToRef(s / a, 0.0, 0.0, 0.0, 0.0, s, 0.0, 0.0, 0.0, 0.0, p, 1.0, 0.0, 0.0, q, 0.0, this._projectionTextureProjectionLightMatrix);\r\n    }\r\n\r\n    /**\r\n     * Main function for light texture projection matrix computing.\r\n     */\r\n    protected _computeProjectionTextureMatrix(): void {\r\n        this._projectionTextureDirty = false;\r\n\r\n        this._projectionTextureViewLightMatrix.multiplyToRef(this._projectionTextureProjectionLightMatrix, this._projectionTextureMatrix);\r\n        if (this._projectionTexture instanceof Texture) {\r\n            const u = this._projectionTexture.uScale / 2.0;\r\n            const v = this._projectionTexture.vScale / 2.0;\r\n            Matrix.FromValuesToRef(u, 0.0, 0.0, 0.0, 0.0, v, 0.0, 0.0, 0.0, 0.0, 0.5, 0.0, 0.5, 0.5, 0.5, 1.0, this._projectionTextureScalingMatrix);\r\n        }\r\n        this._projectionTextureMatrix.multiplyToRef(this._projectionTextureScalingMatrix, this._projectionTextureMatrix);\r\n    }\r\n\r\n    protected _buildUniformLayout(): void {\r\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightDirection\", 3);\r\n        this._uniformBuffer.addUniform(\"vLightFalloff\", 4);\r\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\r\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\r\n        this._uniformBuffer.create();\r\n    }\r\n\r\n    private _computeAngleValues(): void {\r\n        this._lightAngleScale = 1.0 / Math.max(0.001, Math.cos(this._innerAngle * 0.5) - this._cosHalfAngle);\r\n        this._lightAngleOffset = -this._cosHalfAngle * this._lightAngleScale;\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Effect \"effect\" with the Light textures.\r\n     * @param effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The light\r\n     */\r\n    public override transferTexturesToEffect(effect: Effect, lightIndex: string): Light {\r\n        if (this.projectionTexture && this.projectionTexture.isReady()) {\r\n            if (this._projectionTextureViewLightDirty) {\r\n                this._computeProjectionTextureViewLightMatrix();\r\n            }\r\n            if (this._projectionTextureProjectionLightDirty) {\r\n                this._computeProjectionTextureProjectionLightMatrix();\r\n            }\r\n            if (this._projectionTextureDirty) {\r\n                this._computeProjectionTextureMatrix();\r\n            }\r\n            effect.setMatrix(\"textureProjectionMatrix\" + lightIndex, this._projectionTextureMatrix);\r\n            effect.setTexture(\"projectionLightTexture\" + lightIndex, this.projectionTexture);\r\n        }\r\n\r\n        if (this._iesProfileTexture && this._iesProfileTexture.isReady()) {\r\n            effect.setTexture(\"iesLightTexture\" + lightIndex, this._iesProfileTexture);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Effect object with the SpotLight transformed position (or position if not parented) and normalized direction.\r\n     * @param effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The spot light\r\n     */\r\n    public transferToEffect(effect: Effect, lightIndex: string): SpotLight {\r\n        let normalizeDirection;\r\n\r\n        if (this.computeTransformedInformation()) {\r\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.transformedPosition.x, this.transformedPosition.y, this.transformedPosition.z, this.exponent, lightIndex);\r\n\r\n            normalizeDirection = Vector3.Normalize(this.transformedDirection);\r\n        } else {\r\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.position.x, this.position.y, this.position.z, this.exponent, lightIndex);\r\n\r\n            normalizeDirection = Vector3.Normalize(this.direction);\r\n        }\r\n\r\n        this._uniformBuffer.updateFloat4(\"vLightDirection\", normalizeDirection.x, normalizeDirection.y, normalizeDirection.z, this._cosHalfAngle, lightIndex);\r\n\r\n        this._uniformBuffer.updateFloat4(\"vLightFalloff\", this.range, this._inverseSquaredRange, this._lightAngleScale, this._lightAngleOffset, lightIndex);\r\n        return this;\r\n    }\r\n\r\n    public transferToNodeMaterialEffect(effect: Effect, lightDataUniformName: string) {\r\n        let normalizeDirection;\r\n\r\n        if (this.computeTransformedInformation()) {\r\n            normalizeDirection = Vector3.Normalize(this.transformedDirection);\r\n        } else {\r\n            normalizeDirection = Vector3.Normalize(this.direction);\r\n        }\r\n\r\n        if (this.getScene().useRightHandedSystem) {\r\n            effect.setFloat3(lightDataUniformName, -normalizeDirection.x, -normalizeDirection.y, -normalizeDirection.z);\r\n        } else {\r\n            effect.setFloat3(lightDataUniformName, normalizeDirection.x, normalizeDirection.y, normalizeDirection.z);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Disposes the light and the associated resources.\r\n     */\r\n    public override dispose(): void {\r\n        super.dispose();\r\n        if (this._projectionTexture) {\r\n            this._projectionTexture.dispose();\r\n        }\r\n        if (this._iesProfileTexture) {\r\n            this._iesProfileTexture.dispose();\r\n            this._iesProfileTexture = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the minZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the min for\r\n     * @returns the depth min z\r\n     */\r\n    public override getDepthMinZ(activeCamera: Nullable<Camera>): number {\r\n        const engine = this._scene.getEngine();\r\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : (activeCamera?.minZ ?? Constants.ShadowMinZ);\r\n\r\n        return engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? minZ : this._scene.getEngine().isNDCHalfZRange ? 0 : minZ;\r\n    }\r\n\r\n    /**\r\n     * Gets the maxZ used for shadow according to both the scene and the light.\r\n     * @param activeCamera The camera we are returning the max for\r\n     * @returns the depth max z\r\n     */\r\n    public override getDepthMaxZ(activeCamera: Nullable<Camera>): number {\r\n        const engine = this._scene.getEngine();\r\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : (activeCamera?.maxZ ?? Constants.ShadowMaxZ);\r\n\r\n        return engine.useReverseDepthBuffer && engine.isNDCHalfZRange ? 0 : maxZ;\r\n    }\r\n\r\n    /**\r\n     * Prepares the list of defines specific to the light type.\r\n     * @param defines the list of defines\r\n     * @param lightIndex defines the index of the light for the effect\r\n     */\r\n    public prepareLightSpecificDefines(defines: any, lightIndex: number): void {\r\n        defines[\"SPOTLIGHT\" + lightIndex] = true;\r\n        defines[\"PROJECTEDLIGHTTEXTURE\" + lightIndex] = this.projectionTexture && this.projectionTexture.isReady() ? true : false;\r\n        defines[\"IESLIGHTTEXTURE\" + lightIndex] = this._iesProfileTexture && this._iesProfileTexture.isReady() ? true : false;\r\n    }\r\n}\r\n\r\n// Register Class Name\r\nRegisterClass(\"BABYLON.SpotLight\", SpotLight);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAGnE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAI/B,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAGxD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;;;;;;;;;4IAGlD,OAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,CAAG,CAAD,GAAK,SAAS,CAAC,IAAI,iKAAE,UAAO,CAAC,IAAI,EAAE,iKAAE,UAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAClF,CAAC,CAAC,CAAC;AASG,MAAO,SAAU,sKAAQ,cAAW;IAuBtC;;;OAGG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,IAAW,iBAAiB,CAAC,KAA4B,EAAA;QACrD,IAAI,IAAI,CAAC,kBAAkB,KAAK,KAAK,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEhC,IAAI,IAAI,CAAC,kBAAkB,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC3E,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,EAAE;gBAClD,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;OAEG,CAEH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IACD;;OAEG,CACH,IAAW,KAAK,CAAC,KAAa,EAAA;QAC1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;QACnD,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG,CAEH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD;;;;OAIG,CACH,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAGD;;OAEG,CAEH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;OAEG,CACH,IAAW,gBAAgB,CAAC,KAAa,EAAA;QACrC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IASD;;OAEG,CACH,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAGD;;OAEG,CAEH,IAAW,0BAA0B,GAAA;QACjC,OAAO,IAAI,CAAC,2BAA2B,CAAC;IAC5C,CAAC;IACD;;OAEG,CACH,IAAW,0BAA0B,CAAC,KAAa,EAAA;QAC/C,IAAI,CAAC,2BAA2B,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;IACvD,CAAC;IAGD;;OAEG,CAEH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IACD;;OAEG,CACH,IAAW,yBAAyB,CAAC,KAAa,EAAA;QAC9C,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;IACvD,CAAC;IAGD;;OAEG,CAEH,IAAW,4BAA4B,GAAA;QACnC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IACD;;OAEG,CACH,IAAW,4BAA4B,CAAC,KAAc,EAAA;QAClD,IAAI,CAAC,6BAA6B,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,sCAAsC,GAAG,IAAI,CAAC;IACvD,CAAC;IAKD;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACD;;OAEG,CACH,IAAW,iBAAiB,CAAC,KAA4B,EAAA;QACrD,IAAI,IAAI,CAAC,kBAAkB,KAAK,KAAK,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC1D,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE;oBACzD,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACnC,CAAC,CAAC,CAAC;YACP,CAAC,MAAM,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,EAAE;oBAClD,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACnC,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,OAAoB,EAAA;QACpD,OAAQ,OAA6B,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC9E,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,OAAoB,EAAA;QAC1C,OAAQ,OAAmB,CAAC,gBAAgB,KAAK,SAAS,CAAC;IAC/D,CAAC;IASD;;OAEG,CACH,IAAW,sCAAsC,GAAA;QAC7C,OAAO,IAAI,CAAC,uCAAuC,CAAC;IACxD,CAAC;IAED,IAAW,sCAAsC,CAAC,UAAkB,EAAA;QAChE,IAAI,CAAC,uCAAuC,GAAG,UAAU,CAAC;QAC1D,IAAI,CAAC,sCAAsC,GAAG,KAAK,CAAC;QACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAID;;;;;;;;;;OAUG,CACH,YAAY,IAAY,EAAE,QAAiB,EAAE,SAAkB,EAAE,KAAa,EAAE,QAAgB,EAAE,KAAa,CAAA;QAC3G,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QA1Nf,IAAA,CAAA,WAAW,GAAW,CAAC,CAAC;QAMxB,IAAA,CAAA,kBAAkB,GAA0B,IAAI,CAAC;QAmFjD,IAAA,CAAA,wBAAwB,GAAG,wKAAM,CAAC,IAAI,EAAE,CAAC;QAQvC,IAAA,CAAA,2BAA2B,GAAW,IAAI,CAAC;QAgB3C,IAAA,CAAA,0BAA0B,GAAW,MAAM,CAAC;QAgB5C,IAAA,CAAA,6BAA6B,kKAAY,UAAO,CAAC,EAAE,EAAE,CAAC;QAuDxD,IAAA,CAAA,gCAAgC,GAAG,IAAI,CAAC;QACxC,IAAA,CAAA,sCAAsC,GAAG,IAAI,CAAC;QAC9C,IAAA,CAAA,uBAAuB,GAAG,IAAI,CAAC;QAC/B,IAAA,CAAA,kCAAkC,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QACpD,IAAA,CAAA,iCAAiC,kKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;QAElD,IAAA,CAAA,uCAAuC,kKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;QAcxD,IAAA,CAAA,+BAA+B,iKAAG,UAAM,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAgBxI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;OAGG,CACH,gEAAgE;IAChD,SAAS,GAAA;QACrB,8JAAO,QAAK,CAAC,qBAAqB,CAAC;IACvC,CAAC;IAED;;;OAGG,CACgB,aAAa,CAAC,KAAc,EAAA;QAC3C,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC3B,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;IACjD,CAAC;IAED;;;OAGG,CACgB,YAAY,CAAC,KAAc,EAAA;QAC1C,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC;IACjD,CAAC;IAED;;;;;;OAMG,CACH,6DAA6D;IACnD,iCAAiC,CAAC,MAAc,EAAE,UAAkB,EAAE,UAA+B,EAAA;QAC3G,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;QAElD,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC;QAEnD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QACjF,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QAEjF,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAAC;uKAEhF,SAAM,CAAC,qBAAqB,CACxB,KAAK,EACL,GAAG,EACH,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,MAAM,EACN,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EACvC,SAAS,EACT,qBAAqB,CACxB,CAAC;IACN,CAAC;IAES,wCAAwC,GAAA;QAC9C,IAAI,CAAC,gCAAgC,GAAG,KAAK,CAAC;QAC9C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QAEpC,IAAI,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,IAAI,CAAC,kCAAkC,CAAC,CAAC;uKACxG,SAAM,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,IAAI,CAAC,kCAAkC,EAAE,IAAI,CAAC,6BAA6B,EAAE,IAAI,CAAC,iCAAiC,CAAC,CAAC;IAC1K,CAAC;IAES,8CAA8C,GAAA;QACpD,IAAI,CAAC,sCAAsC,GAAG,KAAK,CAAC;QACpD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QAEpC,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,0BAA0B,CAAC;QAElD,MAAM,CAAC,GAAG,QAAQ,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;QAC5C,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;QACzB,MAAM,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;QAC5C,MAAM,CAAC,GAAG,GAAG,CAAC;uKAEd,SAAM,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,uCAAuC,CAAC,CAAC;IACrJ,CAAC;IAED;;OAEG,CACO,+BAA+B,GAAA;QACrC,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;QAErC,IAAI,CAAC,iCAAiC,CAAC,aAAa,CAAC,IAAI,CAAC,uCAAuC,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAClI,IAAI,IAAI,CAAC,kBAAkB,oLAAY,UAAO,EAAE,CAAC;YAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,GAAG,CAAC;YAC/C,MAAM,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,GAAG,CAAC;0KAC/C,UAAM,CAAC,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC7I,CAAC;QACD,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACrH,CAAC;IAES,mBAAmB,GAAA;QACzB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAEO,mBAAmB,GAAA;QACvB,IAAI,CAAC,gBAAgB,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC;QACrG,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC;IACzE,CAAC;IAED;;;;;OAKG,CACa,wBAAwB,CAAC,MAAc,EAAE,UAAkB,EAAA;QACvE,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;gBACxC,IAAI,CAAC,wCAAwC,EAAE,CAAC;YACpD,CAAC;YACD,IAAI,IAAI,CAAC,sCAAsC,EAAE,CAAC;gBAC9C,IAAI,CAAC,8CAA8C,EAAE,CAAC;YAC1D,CAAC;YACD,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,IAAI,CAAC,+BAA+B,EAAE,CAAC;YAC3C,CAAC;YACD,MAAM,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACxF,MAAM,CAAC,UAAU,CAAC,wBAAwB,GAAG,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/D,MAAM,CAAC,UAAU,CAAC,iBAAiB,GAAG,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC/E,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,MAAc,EAAE,UAAkB,EAAA;QACtD,IAAI,kBAAkB,CAAC;QAEvB,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAE9J,kBAAkB,kKAAG,UAAO,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACtE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAE7H,kBAAkB,kKAAG,UAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAEtJ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;QACpJ,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4BAA4B,CAAC,MAAc,EAAE,oBAA4B,EAAA;QAC5E,IAAI,kBAAkB,CAAC;QAEvB,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACvC,kBAAkB,kKAAG,UAAO,CAAC,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACtE,CAAC,MAAM,CAAC;YACJ,kBAAkB,kKAAG,UAAO,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAChH,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAC7G,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACnC,CAAC;IACL,CAAC;IAED;;;;OAIG,CACa,YAAY,CAAC,YAA8B,EAAA;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,YAAY,EAAE,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC;QAE5G,OAAO,MAAM,CAAC,qBAAqB,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9H,CAAC;IAED;;;;OAIG,CACa,YAAY,CAAC,YAA8B,EAAA;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,YAAY,EAAE,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC;QAE5G,OAAO,MAAM,CAAC,qBAAqB,IAAI,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,CAAC;IAED;;;;OAIG,CACI,2BAA2B,CAAC,OAAY,EAAE,UAAkB,EAAA;QAC/D,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QACzC,OAAO,CAAC,uBAAuB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAC1H,OAAO,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAC1H,CAAC;CACJ;wJAnbG,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;sCAGX;wJAkBD,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;2CAGX;wJAgBD,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;iDAGX;wJAaM,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;2CACY;wJAexB,aAAA,EAAA;IADC,0KAAS,AAAT,EAAW;2DAGX;wJAcD,aAAA,EAAA;KADC,yKAAA,AAAS,EAAE;0DAGX;wJAcD,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;6DAGX;CAUO,oKAAA,EAAA;kKADP,qBAAA,AAAkB,EAAC,uBAAuB,CAAC;qDACM;AAqUtD,sBAAsB;6JACtB,gBAAA,AAAa,EAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 4361, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/Shadows/shadowGeneratorSceneComponent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/Shadows/shadowGeneratorSceneComponent.ts"], "sourcesContent": ["import type { SmartArrayNoDuplicate } from \"../../Misc/smartArray\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport { ShadowGenerator } from \"./shadowGenerator\";\r\nimport { CascadedShadowGenerator } from \"./cascadedShadowGenerator\";\r\nimport type { ISceneSerializableComponent } from \"../../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../../sceneComponent\";\r\nimport { AddParser } from \"core/Loading/Plugins/babylonFileParser.function\";\r\nimport type { IAssetContainer } from \"core/IAssetContainer\";\r\n\r\n// Adds the parser to the scene parsers.\r\nAddParser(SceneComponentConstants.NAME_SHADOWGENERATOR, (parsedData: any, scene: Scene) => {\r\n    // Shadows\r\n    if (parsedData.shadowGenerators !== undefined && parsedData.shadowGenerators !== null) {\r\n        for (let index = 0, cache = parsedData.shadowGenerators.length; index < cache; index++) {\r\n            const parsedShadowGenerator = parsedData.shadowGenerators[index];\r\n            if (parsedShadowGenerator.className === CascadedShadowGenerator.CLASSNAME) {\r\n                CascadedShadowGenerator.Parse(parsedShadowGenerator, scene);\r\n            } else {\r\n                ShadowGenerator.Parse(parsedShadowGenerator, scene);\r\n            }\r\n            // SG would be available on their associated lights\r\n        }\r\n    }\r\n});\r\n\r\n/**\r\n * Defines the shadow generator component responsible to manage any shadow generators\r\n * in a given scene.\r\n */\r\nexport class ShadowGeneratorSceneComponent implements ISceneSerializableComponent {\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_SHADOWGENERATOR;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene: Scene) {\r\n        this.scene = scene;\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._gatherRenderTargetsStage.registerStep(SceneComponentConstants.STEP_GATHERRENDERTARGETS_SHADOWGENERATOR, this, this._gatherRenderTargets);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing To Do Here.\r\n    }\r\n\r\n    /**\r\n     * Serializes the component data to the specified json object\r\n     * @param serializationObject The object to serialize to\r\n     */\r\n    public serialize(serializationObject: any): void {\r\n        // Shadows\r\n        serializationObject.shadowGenerators = [];\r\n        const lights = this.scene.lights;\r\n        for (const light of lights) {\r\n            if (light.doNotSerialize) {\r\n                continue;\r\n            }\r\n            const shadowGenerators = light.getShadowGenerators();\r\n            if (shadowGenerators) {\r\n                const iterator = shadowGenerators.values();\r\n                for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                    const shadowGenerator = key.value;\r\n                    if (shadowGenerator.doNotSerialize) {\r\n                        continue;\r\n                    }\r\n                    serializationObject.shadowGenerators.push(shadowGenerator.serialize());\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds all the elements from the container to the scene\r\n     * @param container the container holding the elements\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public addFromContainer(container: IAssetContainer): void {\r\n        // Nothing To Do Here. (directly attached to a light)\r\n    }\r\n\r\n    /**\r\n     * Removes all the elements in the container from the scene\r\n     * @param container contains the elements to remove\r\n     * @param dispose if the removed element should be disposed (default: false)\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public removeFromContainer(container: IAssetContainer, dispose?: boolean): void {\r\n        // Nothing To Do Here. (directly attached to a light)\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public dispose(): void {\r\n        // Nothing To Do Here.\r\n    }\r\n\r\n    private _gatherRenderTargets(renderTargets: SmartArrayNoDuplicate<RenderTargetTexture>): void {\r\n        // Shadows\r\n        const scene = this.scene;\r\n        if (this.scene.shadowsEnabled) {\r\n            for (let lightIndex = 0; lightIndex < scene.lights.length; lightIndex++) {\r\n                const light = scene.lights[lightIndex];\r\n                const shadowGenerators = light.getShadowGenerators();\r\n\r\n                if (light.isEnabled() && light.shadowEnabled && shadowGenerators) {\r\n                    const iterator = shadowGenerators.values();\r\n                    for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                        const shadowGenerator = key.value;\r\n                        const shadowMap = <RenderTargetTexture>shadowGenerator.getShadowMap();\r\n                        if (scene.textures.indexOf(shadowMap) !== -1) {\r\n                            renderTargets.push(shadowMap);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nShadowGenerator._SceneComponentInitialization = (scene: Scene) => {\r\n    let component = scene._getComponent(SceneComponentConstants.NAME_SHADOWGENERATOR);\r\n    if (!component) {\r\n        component = new ShadowGeneratorSceneComponent(scene);\r\n        scene._addComponent(component);\r\n    }\r\n};\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAEpE,OAAO,EAAE,uBAAuB,EAAE,MAAM,sBAAsB,CAAC;AAC/D,OAAO,EAAE,SAAS,EAAE,4DAAwD;;;;;AAG5E,wCAAwC;+LACxC,YAAA,AAAS,wJAAC,0BAAuB,CAAC,oBAAoB,EAAE,CAAC,UAAe,EAAE,KAAY,EAAE,EAAE;IACtF,UAAU;IACV,IAAI,UAAU,CAAC,gBAAgB,KAAK,SAAS,IAAI,UAAU,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;QACpF,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YACrF,MAAM,qBAAqB,GAAG,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACjE,IAAI,qBAAqB,CAAC,SAAS,yLAAK,0BAAuB,CAAC,SAAS,EAAE,CAAC;oMACxE,0BAAuB,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAChE,CAAC,MAAM,CAAC;4LACJ,kBAAe,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC;QACD,mDAAmD;QACvD,CAAC;IACL,CAAC;AACL,CAAC,CAAC,CAAC;AAMG,MAAO,6BAA6B;IAWtC;;;OAGG,CACH,YAAY,KAAY,CAAA;QAdxB;;WAEG,CACa,IAAA,CAAA,IAAI,yJAAG,0BAAuB,CAAC,oBAAoB,CAAC;QAYhE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG,CACI,QAAQ,GAAA;QACX,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,YAAY,CAAC,gLAAuB,CAAC,wCAAwC,EAAE,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IACzJ,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;IACV,sBAAsB;IAC1B,CAAC;IAED;;;OAGG,CACI,SAAS,CAAC,mBAAwB,EAAA;QACrC,UAAU;QACV,mBAAmB,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QACjC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;YACD,MAAM,gBAAgB,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;YACrD,IAAI,gBAAgB,EAAE,CAAC;gBACnB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC;gBAC3C,IAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAE,CAAC;oBACvE,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC;oBAClC,IAAI,eAAe,CAAC,cAAc,EAAE,CAAC;wBACjC,SAAS;oBACb,CAAC;oBACD,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC3E,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACH,6DAA6D;IACtD,gBAAgB,CAAC,SAA0B,EAAA;IAC9C,qDAAqD;IACzD,CAAC;IAED;;;;OAIG,CACH,6DAA6D;IACtD,mBAAmB,CAAC,SAA0B,EAAE,OAAiB,EAAA;IACpE,qDAAqD;IACzD,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;IACV,sBAAsB;IAC1B,CAAC;IAEO,oBAAoB,CAAC,aAAyD,EAAA;QAClF,UAAU;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC5B,IAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,CAAE,CAAC;gBACtE,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACvC,MAAM,gBAAgB,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBAErD,IAAI,KAAK,CAAC,SAAS,EAAE,IAAI,KAAK,CAAC,aAAa,IAAI,gBAAgB,EAAE,CAAC;oBAC/D,MAAM,QAAQ,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC;oBAC3C,IAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAE,CAAC;wBACvE,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC;wBAClC,MAAM,SAAS,GAAwB,eAAe,CAAC,YAAY,EAAE,CAAC;wBACtE,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;4BAC3C,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAClC,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;CACJ;4KAED,kBAAe,CAAC,6BAA6B,GAAG,CAAC,KAAY,EAAE,EAAE;IAC7D,IAAI,SAAS,GAAG,KAAK,CAAC,aAAa,CAAC,gLAAuB,CAAC,oBAAoB,CAAC,CAAC;IAClF,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,SAAS,GAAG,IAAI,6BAA6B,CAAC,KAAK,CAAC,CAAC;QACrD,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;AACL,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 4485, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/Shadows/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/Shadows/index.ts"], "sourcesContent": ["export * from \"./shadowGenerator\";\r\nexport * from \"./cascadedShadowGenerator\";\r\nexport * from \"./shadowGeneratorSceneComponent\";\r\n\r\n// Blur postprocess\r\nexport * from \"../../ShadersWGSL/shadowMap.fragment\";\r\nexport * from \"../../ShadersWGSL/shadowMap.vertex\";\r\nexport * from \"../../ShadersWGSL/depthBoxBlur.fragment\";\r\nexport * from \"../../ShadersWGSL/ShadersInclude/shadowMapFragmentSoftTransparentShadow\";\r\nexport * from \"../../Shaders/shadowMap.fragment\";\r\nexport * from \"../../Shaders/shadowMap.vertex\";\r\nexport * from \"../../Shaders/depthBoxBlur.fragment\";\r\nexport * from \"../../Shaders/ShadersInclude/shadowMapFragmentSoftTransparentShadow\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,mBAAmB,CAAC;AAClC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,iCAAiC,CAAC;AAEhD,mBAAmB;AACnB,cAAc,sCAAsC,CAAC;AACrD,cAAc,oCAAoC,CAAC;AACnD,cAAc,yCAAyC,CAAC;AACxD,cAAc,yEAAyE,CAAC;AACxF,cAAc,kCAAkC,CAAC;AACjD,cAAc,gCAAgC,CAAC;AAC/C,cAAc,qCAAqC,CAAC;AACpD,cAAc,qEAAqE,CAAC", "debugId": null}}, {"offset": {"line": 4530, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/pointLight.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/pointLight.ts"], "sourcesContent": ["import { serialize } from \"../Misc/decorators\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport { Node } from \"../node\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Light } from \"./light\";\r\nimport { ShadowLight } from \"./shadowLight\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\nNode.AddNodeConstructor(\"Light_Type_0\", (name, scene) => {\r\n    return () => new PointLight(name, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * A point light is a light defined by an unique point in world space.\r\n * The light is emitted in every direction from this point.\r\n * A good example of a point light is a standard light bulb.\r\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n */\r\nexport class PointLight extends ShadowLight {\r\n    private _shadowAngle = Math.PI / 2;\r\n    /**\r\n     * Getter: In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\r\n     * This specifies what angle the shadow will use to be created.\r\n     *\r\n     * It default to 90 degrees to work nicely with the cube texture generation for point lights shadow maps.\r\n     */\r\n    @serialize()\r\n    public get shadowAngle(): number {\r\n        return this._shadowAngle;\r\n    }\r\n    /**\r\n     * Setter: In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\r\n     * This specifies what angle the shadow will use to be created.\r\n     *\r\n     * It default to 90 degrees to work nicely with the cube texture generation for point lights shadow maps.\r\n     */\r\n    public set shadowAngle(value: number) {\r\n        this._shadowAngle = value;\r\n        this.forceProjectionMatrixCompute();\r\n    }\r\n\r\n    /**\r\n     * Gets the direction if it has been set.\r\n     * In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\r\n     */\r\n    public override get direction(): Vector3 {\r\n        return this._direction;\r\n    }\r\n\r\n    /**\r\n     * In case of direction provided, the shadow will not use a cube texture but simulate a spot shadow as a fallback\r\n     */\r\n    public override set direction(value: Vector3) {\r\n        const previousNeedCube = this.needCube();\r\n        this._direction = value;\r\n        if (this.needCube() !== previousNeedCube && this._shadowGenerators) {\r\n            const iterator = this._shadowGenerators.values();\r\n            for (let key = iterator.next(); key.done !== true; key = iterator.next()) {\r\n                const shadowGenerator = key.value;\r\n                shadowGenerator.recreateShadowMap();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Creates a PointLight object from the passed name and position (Vector3) and adds it in the scene.\r\n     * A PointLight emits the light in every direction.\r\n     * It can cast shadows.\r\n     * If the scene camera is already defined and you want to set your PointLight at the camera position, just set it :\r\n     * ```javascript\r\n     * var pointLight = new PointLight(\"pl\", camera.position, scene);\r\n     * ```\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n     * @param name The light friendly name\r\n     * @param position The position of the point light in the scene\r\n     * @param scene The scene the lights belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene) {\r\n        super(name, scene);\r\n        this.position = position;\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"PointLight\"\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"PointLight\";\r\n    }\r\n\r\n    /**\r\n     * Returns the integer 0.\r\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public override getTypeID(): number {\r\n        return Light.LIGHTTYPEID_POINTLIGHT;\r\n    }\r\n\r\n    /**\r\n     * Specifies whether or not the shadowmap should be a cube texture.\r\n     * @returns true if the shadowmap needs to be a cube texture.\r\n     */\r\n    public override needCube(): boolean {\r\n        return !this.direction;\r\n    }\r\n\r\n    /**\r\n     * Returns a new Vector3 aligned with the PointLight cube system according to the passed cube face index (integer).\r\n     * @param faceIndex The index of the face we are computed the direction to generate shadow\r\n     * @returns The set direction in 2d mode otherwise the direction to the cubemap face if needCube() is true\r\n     */\r\n    public override getShadowDirection(faceIndex?: number): Vector3 {\r\n        if (this.direction) {\r\n            return super.getShadowDirection(faceIndex);\r\n        } else {\r\n            switch (faceIndex) {\r\n                case 0:\r\n                    return new Vector3(1.0, 0.0, 0.0);\r\n                case 1:\r\n                    return new Vector3(-1.0, 0.0, 0.0);\r\n                case 2:\r\n                    return new Vector3(0.0, -1.0, 0.0);\r\n                case 3:\r\n                    return new Vector3(0.0, 1.0, 0.0);\r\n                case 4:\r\n                    return new Vector3(0.0, 0.0, 1.0);\r\n                case 5:\r\n                    return new Vector3(0.0, 0.0, -1.0);\r\n            }\r\n        }\r\n\r\n        return Vector3.Zero();\r\n    }\r\n\r\n    /**\r\n     * Sets the passed matrix \"matrix\" as a left-handed perspective projection matrix with the following settings :\r\n     * - fov = PI / 2\r\n     * - aspect ratio : 1.0\r\n     * - z-near and far equal to the active camera minZ and maxZ.\r\n     * Returns the PointLight.\r\n     * @param matrix\r\n     * @param viewMatrix\r\n     * @param renderList\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _setDefaultShadowProjectionMatrix(matrix: Matrix, viewMatrix: Matrix, renderList: Array<AbstractMesh>): void {\r\n        const activeCamera = this.getScene().activeCamera;\r\n\r\n        if (!activeCamera) {\r\n            return;\r\n        }\r\n\r\n        const minZ = this.shadowMinZ !== undefined ? this.shadowMinZ : activeCamera.minZ;\r\n        const maxZ = this.shadowMaxZ !== undefined ? this.shadowMaxZ : activeCamera.maxZ;\r\n\r\n        const useReverseDepthBuffer = this.getScene().getEngine().useReverseDepthBuffer;\r\n\r\n        Matrix.PerspectiveFovLHToRef(\r\n            this.shadowAngle,\r\n            1.0,\r\n            useReverseDepthBuffer ? maxZ : minZ,\r\n            useReverseDepthBuffer ? minZ : maxZ,\r\n            matrix,\r\n            true,\r\n            this._scene.getEngine().isNDCHalfZRange,\r\n            undefined,\r\n            useReverseDepthBuffer\r\n        );\r\n    }\r\n\r\n    protected _buildUniformLayout(): void {\r\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\r\n        this._uniformBuffer.addUniform(\"vLightFalloff\", 4);\r\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\r\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\r\n        this._uniformBuffer.create();\r\n    }\r\n\r\n    /**\r\n     * Sets the passed Effect \"effect\" with the PointLight transformed position (or position, if none) and passed name (string).\r\n     * @param effect The effect to update\r\n     * @param lightIndex The index of the light in the effect to update\r\n     * @returns The point light\r\n     */\r\n    public transferToEffect(effect: Effect, lightIndex: string): PointLight {\r\n        if (this.computeTransformedInformation()) {\r\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.transformedPosition.x, this.transformedPosition.y, this.transformedPosition.z, 0.0, lightIndex);\r\n        } else {\r\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.position.x, this.position.y, this.position.z, 0, lightIndex);\r\n        }\r\n\r\n        this._uniformBuffer.updateFloat4(\"vLightFalloff\", this.range, this._inverseSquaredRange, 0, 0, lightIndex);\r\n        return this;\r\n    }\r\n\r\n    public transferToNodeMaterialEffect(effect: Effect, lightDataUniformName: string) {\r\n        if (this.computeTransformedInformation()) {\r\n            effect.setFloat3(lightDataUniformName, this.transformedPosition.x, this.transformedPosition.y, this.transformedPosition.z);\r\n        } else {\r\n            effect.setFloat3(lightDataUniformName, this.position.x, this.position.y, this.position.z);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Prepares the list of defines specific to the light type.\r\n     * @param defines the list of defines\r\n     * @param lightIndex defines the index of the light for the effect\r\n     */\r\n    public prepareLightSpecificDefines(defines: any, lightIndex: number): void {\r\n        defines[\"POINTLIGHT\" + lightIndex] = true;\r\n    }\r\n}\r\n\r\n// Register Class Name\r\nRegisterClass(\"BABYLON.PointLight\", PointLight);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAE/C,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;;;;;;;;4IAElD,OAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,CAAG,CAAD,GAAK,UAAU,CAAC,IAAI,iKAAE,UAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC;AAQG,MAAO,UAAW,sKAAQ,cAAW;IAEvC;;;;;OAKG,CAEH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACD;;;;;OAKG,CACH,IAAW,WAAW,CAAC,KAAa,EAAA;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IAED;;;OAGG,CACH,IAAoB,SAAS,GAAA;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAoB,SAAS,CAAC,KAAc,EAAA;QACxC,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACjE,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;YACjD,IAAK,IAAI,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAE,CAAC;gBACvE,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC;gBAClC,eAAe,CAAC,iBAAiB,EAAE,CAAC;YACxC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG,CACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,CAAA;QACtD,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QA3Df,IAAA,CAAA,YAAY,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;QA4D/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;OAGG,CACH,gEAAgE;IAChD,SAAS,GAAA;QACrB,8JAAO,QAAK,CAAC,sBAAsB,CAAC;IACxC,CAAC;IAED;;;OAGG,CACa,QAAQ,GAAA;QACpB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;IAC3B,CAAC;IAED;;;;OAIG,CACa,kBAAkB,CAAC,SAAkB,EAAA;QACjD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC/C,CAAC,MAAM,CAAC;YACJ,OAAQ,SAAS,EAAE,CAAC;gBAChB,KAAK,CAAC;oBACF,OAAO,mKAAI,UAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACtC,KAAK,CAAC;oBACF,OAAO,mKAAI,UAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,KAAK,CAAC;oBACF,OAAO,mKAAI,UAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBACvC,KAAK,CAAC;oBACF,OAAO,IAAI,yKAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACtC,KAAK,CAAC;oBACF,OAAO,mKAAI,UAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACtC,KAAK,CAAC;oBACF,OAAO,mKAAI,UAAO,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC;QAED,sKAAO,UAAO,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED;;;;;;;;;OASG,CACH,6DAA6D;IACnD,iCAAiC,CAAC,MAAc,EAAE,UAAkB,EAAE,UAA+B,EAAA;QAC3G,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC;QAElD,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QACjF,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;QAEjF,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,qBAAqB,CAAC;uKAEhF,SAAM,CAAC,qBAAqB,CACxB,IAAI,CAAC,WAAW,EAChB,GAAG,EACH,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACnC,MAAM,EACN,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,EACvC,SAAS,EACT,qBAAqB,CACxB,CAAC;IACN,CAAC;IAES,mBAAmB,GAAA;QACzB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,MAAc,EAAE,UAAkB,EAAA;QACtD,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;QACxJ,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QACrH,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;QAC3G,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4BAA4B,CAAC,MAAc,EAAE,oBAA4B,EAAA;QAC5E,IAAI,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QAC/H,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9F,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,2BAA2B,CAAC,OAAY,EAAE,UAAkB,EAAA;QAC/D,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;IAC9C,CAAC;CACJ;wJA7LG,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;6CAGX;AA6LL,sBAAsB;6JACtB,gBAAA,AAAa,EAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 4716, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/LTC/ltcTextureTool.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/LTC/ltcTextureTool.ts"], "sourcesContent": ["import type { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport { Tools } from \"core/Misc/tools\";\r\nimport type { Tuple } from \"core/types\";\r\n\r\n/**\r\n * Linearly transformed cosine textures that are used in the Area Lights shaders.\r\n */\r\nexport type ILTCTextures = {\r\n    /**\r\n     * Linearly transformed cosine texture BRDF Approximation.\r\n     */\r\n    LTC1: BaseTexture;\r\n\r\n    /**\r\n     * Linearly transformed cosine texture Fresnel Approximation.\r\n     */\r\n    LTC2: BaseTexture;\r\n};\r\n\r\n/**\r\n * Loads LTC texture data from Babylon.js CDN.\r\n * @returns Promise with data for LTC1 and LTC2 textures for area lights.\r\n */\r\nexport async function DecodeLTCTextureDataAsync(): Promise<Tuple<Uint16Array, 2>> {\r\n    const ltc1 = new Uint16Array(64 * 64 * 4);\r\n    const ltc2 = new Uint16Array(64 * 64 * 4);\r\n    const file = await Tools.LoadFileAsync(Tools.GetAssetUrl(\"https://assets.babylonjs.com/core/areaLights/areaLightsLTC.bin\"));\r\n    const ltcEncoded = new Uint16Array(file);\r\n\r\n    const pixelCount = ltcEncoded.length / 8;\r\n\r\n    for (let pixelIndex = 0; pixelIndex < pixelCount; pixelIndex++) {\r\n        ltc1[pixelIndex * 4] = ltcEncoded[pixelIndex * 8];\r\n        ltc1[pixelIndex * 4 + 1] = ltcEncoded[pixelIndex * 8 + 1];\r\n        ltc1[pixelIndex * 4 + 2] = ltcEncoded[pixelIndex * 8 + 2];\r\n        ltc1[pixelIndex * 4 + 3] = ltcEncoded[pixelIndex * 8 + 3];\r\n\r\n        ltc2[pixelIndex * 4] = ltcEncoded[pixelIndex * 8 + 4];\r\n        ltc2[pixelIndex * 4 + 1] = ltcEncoded[pixelIndex * 8 + 5];\r\n        ltc2[pixelIndex * 4 + 2] = ltcEncoded[pixelIndex * 8 + 6];\r\n        ltc2[pixelIndex * 4 + 3] = ltcEncoded[pixelIndex * 8 + 7];\r\n    }\r\n\r\n    return [ltc1, ltc2];\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,KAAK,EAAE,4BAAwB;;AAsBjC,KAAK,UAAU,yBAAyB;IAC3C,MAAM,IAAI,GAAG,IAAI,WAAW,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1C,MAAM,IAAI,GAAG,IAAI,WAAW,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1C,MAAM,IAAI,GAAG,2JAAM,QAAK,CAAC,aAAa,sJAAC,QAAK,CAAC,WAAW,CAAC,gEAAgE,CAAC,CAAC,CAAC;IAC5H,MAAM,UAAU,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;IAEzC,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;IAEzC,IAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,UAAU,EAAE,UAAU,EAAE,CAAE,CAAC;QAC7D,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAE1D,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED,OAAO;QAAC,IAAI;QAAE,IAAI;KAAC,CAAC;AACxB,CAAC", "debugId": null}}, {"offset": {"line": 4746, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/areaLight.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/areaLight.ts"], "sourcesContent": ["import type { Vector3 } from \"core/Maths/math.vector\";\r\nimport { RawTexture } from \"core/Materials/Textures/rawTexture\";\r\nimport { Texture } from \"core/Materials/Textures/texture\";\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport { Light } from \"core/Lights/light\";\r\nimport type { Effect } from \"core/Materials/effect\";\r\nimport type { ILTCTextures } from \"core/Lights/LTC/ltcTextureTool\";\r\nimport { DecodeLTCTextureDataAsync } from \"core/Lights/LTC/ltcTextureTool\";\r\nimport type { Scene } from \"core/scene\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\ndeclare module \"../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /**\r\n         * @internal\r\n         */\r\n        _ltcTextures?: ILTCTextures;\r\n    }\r\n}\r\n\r\nfunction CreateSceneLTCTextures(scene: Scene): void {\r\n    const useDelayedTextureLoading = scene.useDelayedTextureLoading;\r\n    scene.useDelayedTextureLoading = false;\r\n\r\n    const previousState = scene._blockEntityCollection;\r\n    scene._blockEntityCollection = false;\r\n\r\n    scene._ltcTextures = {\r\n        LTC1: RawTexture.CreateRGBATexture(null, 64, 64, scene.getEngine(), false, false, Constants.TEXTURE_LINEAR_LINEAR, Constants.TEXTURETYPE_HALF_FLOAT, 0, false, true),\r\n        LTC2: RawTexture.CreateRGBATexture(null, 64, 64, scene.getEngine(), false, false, Constants.TEXTURE_LINEAR_LINEAR, Constants.TEXTURETYPE_HALF_FLOAT, 0, false, true),\r\n    };\r\n\r\n    scene._blockEntityCollection = previousState;\r\n\r\n    scene._ltcTextures.LTC1.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n    scene._ltcTextures.LTC1.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n\r\n    scene._ltcTextures.LTC2.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n    scene._ltcTextures.LTC2.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n\r\n    scene.useDelayedTextureLoading = useDelayedTextureLoading;\r\n\r\n    DecodeLTCTextureDataAsync()\r\n        // eslint-disable-next-line github/no-then\r\n        .then((textureData) => {\r\n            if (scene._ltcTextures) {\r\n                const ltc1 = scene._ltcTextures?.LTC1 as RawTexture;\r\n                ltc1.update(textureData[0]);\r\n\r\n                const ltc2 = scene._ltcTextures?.LTC2 as RawTexture;\r\n                ltc2.update(textureData[1]);\r\n\r\n                scene.onDisposeObservable.addOnce(() => {\r\n                    scene._ltcTextures?.LTC1.dispose();\r\n                    scene._ltcTextures?.LTC2.dispose();\r\n                });\r\n            }\r\n        })\r\n        // eslint-disable-next-line github/no-then\r\n        .catch((error) => {\r\n            Logger.Error(`Area Light fail to get LTC textures data. Error: ${error}`);\r\n        });\r\n}\r\n\r\n/**\r\n * Abstract Area Light class that servers as parent for all Area Lights implementations.\r\n * The light is emitted from the area in the -Z direction.\r\n */\r\nexport abstract class AreaLight extends Light {\r\n    /**\r\n     * Area Light position.\r\n     */\r\n    public position: Vector3;\r\n\r\n    /**\r\n     * Creates a area light object.\r\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\r\n     * @param name The friendly name of the light\r\n     * @param position The position of the area light.\r\n     * @param scene The scene the light belongs to\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene) {\r\n        super(name, scene);\r\n        this.position = position;\r\n\r\n        if (!this._scene._ltcTextures) {\r\n            CreateSceneLTCTextures(this._scene);\r\n        }\r\n    }\r\n\r\n    public override transferTexturesToEffect(effect: Effect): Light {\r\n        if (this._scene._ltcTextures) {\r\n            effect.setTexture(\"areaLightsLTC1Sampler\", this._scene._ltcTextures.LTC1);\r\n            effect.setTexture(\"areaLightsLTC2Sampler\", this._scene._ltcTextures.LTC2);\r\n        }\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Prepares the list of defines specific to the light type.\r\n     * @param defines the list of defines\r\n     * @param lightIndex defines the index of the light for the effect\r\n     */\r\n    public prepareLightSpecificDefines(defines: any, lightIndex: number): void {\r\n        defines[\"AREALIGHT\" + lightIndex] = true;\r\n        defines[\"AREALIGHTUSED\"] = true;\r\n    }\r\n\r\n    public override _isReady(): boolean {\r\n        if (this._scene._ltcTextures) {\r\n            return this._scene._ltcTextures.LTC1.isReady() && this._scene._ltcTextures.LTC2.isReady();\r\n        }\r\n\r\n        return false;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,4CAA2C;AAChE,OAAO,EAAE,OAAO,EAAE,yCAAwC;AAE1D,OAAO,EAAE,KAAK,EAAE,mBAA0B;AAG1C,OAAO,EAAE,yBAAyB,EAAE,gCAAuC;AAE3E,OAAO,EAAE,MAAM,EAAE,0BAAyB;;;;;;AAY1C,SAAS,sBAAsB,CAAC,KAAY;IACxC,MAAM,wBAAwB,GAAG,KAAK,CAAC,wBAAwB,CAAC;IAChE,KAAK,CAAC,wBAAwB,GAAG,KAAK,CAAC;IAEvC,MAAM,aAAa,GAAG,KAAK,CAAC,sBAAsB,CAAC;IACnD,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;IAErC,KAAK,CAAC,YAAY,GAAG;QACjB,IAAI,6KAAE,aAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,GAAA,GAAA,GAAS,CAAC,MAAA,eAAqB,EAAE,SAAS,CAAC,sBAAsB,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;QACpK,IAAI,6KAAE,aAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,GAAA,GAAA,GAAS,CAAC,MAAA,eAAqB,EAAE,SAAS,CAAC,sBAAsB,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;KACvK,CAAC;IAEF,KAAK,CAAC,sBAAsB,GAAG,aAAa,CAAC;IAE7C,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,GAAG,kLAAO,CAAC,iBAAiB,CAAC;IAC1D,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;IAE1D,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,0KAAG,WAAO,CAAC,iBAAiB,CAAC;IAC1D,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;IAE1D,KAAK,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;QAE1D,mMAAA,AAAyB,EAAE,CACvB,0CAA0C;KACzC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;QAClB,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACrB,MAAM,IAAI,GAAG,KAAK,CAAC,YAAY,EAAE,IAAkB,CAAC;YACpD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5B,MAAM,IAAI,GAAG,KAAK,CAAC,YAAY,EAAE,IAAkB,CAAC;YACpD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5B,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;gBACnC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;YACvC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC,CAAC,AACF,0CAA0C;KACzC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;8JACb,SAAM,CAAC,KAAK,CAAC,CAAA,iDAAA,EAAoD,KAAK,EAAE,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC;AACX,CAAC;AAMK,MAAgB,SAAU,gKAAQ,QAAK;IAMzC;;;;;;OAMG,CACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,CAAA;QACtD,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC5B,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC;IACL,CAAC;IAEe,wBAAwB,CAAC,MAAc,EAAA;QACnD,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3B,MAAM,CAAC,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC1E,MAAM,CAAC,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC9E,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,2BAA2B,CAAC,OAAY,EAAE,UAAkB,EAAA;QAC/D,OAAO,CAAC,WAAW,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;QACzC,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;IACpC,CAAC;IAEe,QAAQ,GAAA;QACpB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QAC9F,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 4831, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/rectAreaLight.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/rectAreaLight.ts"], "sourcesContent": ["import { Vector3 } from \"../Maths/math.vector\";\nimport { Node } from \"../node\";\nimport { Light } from \"./light\";\nimport type { Effect } from \"core/Materials/effect\";\nimport { RegisterClass } from \"core/Misc/typeStore\";\nimport { serialize } from \"../Misc/decorators\";\nimport type { Scene } from \"core/scene\";\nimport { AreaLight } from \"./areaLight\";\n\nNode.AddNodeConstructor(\"Light_Type_4\", (name, scene) => {\n    return () => new RectAreaLight(name, Vector3.Zero(), 1, 1, scene);\n});\n\n/**\n * A rectangular area light defined by an unique point in world space, a width and a height.\n * The light is emitted from the rectangular area in the -Z direction.\n */\nexport class RectAreaLight extends AreaLight {\n    private readonly _width: Vector3;\n    private readonly _height: Vector3;\n    protected readonly _pointTransformedPosition: Vector3;\n    protected readonly _pointTransformedWidth: Vector3;\n    protected readonly _pointTransformedHeight: Vector3;\n\n    /**\n     * Rect Area Light width.\n     */\n    @serialize()\n    public get width(): number {\n        return this._width.x;\n    }\n    /**\n     * Rect Area Light width.\n     */\n    public set width(value: number) {\n        this._width.x = value;\n    }\n\n    /**\n     * Rect Area Light height.\n     */\n    @serialize()\n    public get height(): number {\n        return this._height.y;\n    }\n    /**\n     * Rect Area Light height.\n     */\n    public set height(value: number) {\n        this._height.y = value;\n    }\n\n    /**\n     * Creates a rectangular area light object.\n     * Documentation : https://doc.babylonjs.com/features/featuresDeepDive/lights/lights_introduction\n     * @param name The friendly name of the light\n     * @param position The position of the area light.\n     * @param width The width of the area light.\n     * @param height The height of the area light.\n     * @param scene The scene the light belongs to\n     */\n    constructor(name: string, position: Vector3, width: number, height: number, scene?: Scene) {\n        super(name, position, scene);\n        this._width = new Vector3(width, 0, 0);\n        this._height = new Vector3(0, height, 0);\n        this._pointTransformedPosition = Vector3.Zero();\n        this._pointTransformedWidth = Vector3.Zero();\n        this._pointTransformedHeight = Vector3.Zero();\n    }\n\n    /**\n     * Returns the string \"RectAreaLight\"\n     * @returns the class name\n     */\n    public override getClassName(): string {\n        return \"RectAreaLight\";\n    }\n\n    /**\n     * Returns the integer 4.\n     * @returns The light Type id as a constant defines in Light.LIGHTTYPEID_x\n     */\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    public override getTypeID(): number {\n        return Light.LIGHTTYPEID_RECT_AREALIGHT;\n    }\n\n    protected _buildUniformLayout(): void {\n        this._uniformBuffer.addUniform(\"vLightData\", 4);\n        this._uniformBuffer.addUniform(\"vLightDiffuse\", 4);\n        this._uniformBuffer.addUniform(\"vLightSpecular\", 4);\n        this._uniformBuffer.addUniform(\"vLightWidth\", 4);\n        this._uniformBuffer.addUniform(\"vLightHeight\", 4);\n        this._uniformBuffer.addUniform(\"shadowsInfo\", 3);\n        this._uniformBuffer.addUniform(\"depthValues\", 2);\n        this._uniformBuffer.create();\n    }\n\n    protected _computeTransformedInformation(): boolean {\n        if (this.parent && this.parent.getWorldMatrix) {\n            Vector3.TransformCoordinatesToRef(this.position, this.parent.getWorldMatrix(), this._pointTransformedPosition);\n            Vector3.TransformNormalToRef(this._width, this.parent.getWorldMatrix(), this._pointTransformedWidth);\n            Vector3.TransformNormalToRef(this._height, this.parent.getWorldMatrix(), this._pointTransformedHeight);\n            return true;\n        }\n\n        return false;\n    }\n\n    /**\n     * Sets the passed Effect \"effect\" with the PointLight transformed position (or position, if none) and passed name (string).\n     * @param effect The effect to update\n     * @param lightIndex The index of the light in the effect to update\n     * @returns The point light\n     */\n    public transferToEffect(effect: Effect, lightIndex: string): RectAreaLight {\n        if (this._computeTransformedInformation()) {\n            this._uniformBuffer.updateFloat4(\"vLightData\", this._pointTransformedPosition.x, this._pointTransformedPosition.y, this._pointTransformedPosition.z, 0, lightIndex);\n            this._uniformBuffer.updateFloat4(\"vLightWidth\", this._pointTransformedWidth.x / 2, this._pointTransformedWidth.y / 2, this._pointTransformedWidth.z / 2, 0, lightIndex);\n            this._uniformBuffer.updateFloat4(\n                \"vLightHeight\",\n                this._pointTransformedHeight.x / 2,\n                this._pointTransformedHeight.y / 2,\n                this._pointTransformedHeight.z / 2,\n                0,\n                lightIndex\n            );\n        } else {\n            this._uniformBuffer.updateFloat4(\"vLightData\", this.position.x, this.position.y, this.position.z, 0, lightIndex);\n            this._uniformBuffer.updateFloat4(\"vLightWidth\", this._width.x / 2, this._width.y / 2, this._width.z / 2, 0.0, lightIndex);\n            this._uniformBuffer.updateFloat4(\"vLightHeight\", this._height.x / 2, this._height.y / 2, this._height.z / 2, 0.0, lightIndex);\n        }\n        return this;\n    }\n\n    public transferToNodeMaterialEffect(effect: Effect, lightDataUniformName: string) {\n        if (this._computeTransformedInformation()) {\n            effect.setFloat3(lightDataUniformName, this._pointTransformedPosition.x, this._pointTransformedPosition.y, this._pointTransformedPosition.z);\n        } else {\n            effect.setFloat3(lightDataUniformName, this.position.x, this.position.y, this.position.z);\n        }\n        return this;\n    }\n}\n\n// Register Class Name\nRegisterClass(\"BABYLON.RectAreaLight\", RectAreaLight);\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAC/B,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,OAAO,EAAE,aAAa,EAAE,6BAA4B;AACpD,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAE/C,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;;;;;;;;4IAExC,OAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,CAAG,CAAD,GAAK,aAAa,CAAC,IAAI,iKAAE,UAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACtE,CAAC,CAAC,CAAC;AAMG,MAAO,aAAc,oKAAQ,YAAS;IAOxC;;OAEG,CAEH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACzB,CAAC;IACD;;OAEG,CACH,IAAW,KAAK,CAAC,KAAa,EAAA;QAC1B,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED;;OAEG,CAEH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC1B,CAAC;IACD;;OAEG,CACH,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED;;;;;;;;OAQG,CACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,MAAc,EAAE,KAAa,CAAA;QACrF,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,yKAAO,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,yBAAyB,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAChD,IAAI,CAAC,sBAAsB,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC7C,IAAI,CAAC,uBAAuB,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;IAClD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACH,gEAAgE;IAChD,SAAS,GAAA;QACrB,OAAO,+JAAK,CAAC,0BAA0B,CAAC;IAC5C,CAAC;IAES,mBAAmB,GAAA;QACzB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;IACjC,CAAC;IAES,8BAA8B,GAAA;QACpC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;2KAC5C,UAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;2KAC/G,UAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;2KACrG,UAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACvG,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,MAAc,EAAE,UAAkB,EAAA;QACtD,IAAI,IAAI,CAAC,8BAA8B,EAAE,EAAE,CAAC;YACxC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;YACpK,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;YACxK,IAAI,CAAC,cAAc,CAAC,YAAY,CAC5B,cAAc,EACd,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,EAClC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,EAClC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,EAClC,CAAC,EACD,UAAU,CACb,CAAC;QACN,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;YACjH,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;YAC1H,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;QAClI,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,4BAA4B,CAAC,MAAc,EAAE,oBAA4B,EAAA;QAC5E,IAAI,IAAI,CAAC,8BAA8B,EAAE,EAAE,CAAC;YACxC,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QACjJ,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,SAAS,CAAC,oBAAoB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9F,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;wJAnHG,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;0CAGX;AAYD,qKAAA,EAAA;kKADC,YAAA,AAAS,EAAE;2CAGX;AAqGL,sBAAsB;6JACtB,gBAAA,AAAa,EAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 4958, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/IES/iesLoader.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/IES/iesLoader.ts"], "sourcesContent": ["import { Lerp } from \"core/Maths/math.scalar.functions\";\r\n\r\ninterface IIESData {\r\n    version: string;\r\n    tilt?: {};\r\n    numberOfLights?: number;\r\n    lumensPerLamp?: number;\r\n    candelaMultiplier?: number;\r\n    numberOfVerticalAngles: number;\r\n    numberOfHorizontalAngles: number;\r\n    horizontalAngles: number[];\r\n    verticalAngles: number[];\r\n    photometricType?: number;\r\n    unitsType?: number;\r\n    width?: number;\r\n    length?: number;\r\n    height?: number;\r\n    ballastFactor?: number;\r\n    fileGenerationType?: number;\r\n    inputWatts?: number;\r\n    candelaValues: number[][];\r\n}\r\n\r\ninterface IDataPointer {\r\n    lines: string[];\r\n    index: number;\r\n}\r\n\r\nfunction LineToArray(line: string): number[] {\r\n    return line\r\n        .split(\" \")\r\n        .filter((x) => x !== \"\")\r\n        .map((x) => parseFloat(x));\r\n}\r\n\r\nfunction ReadArray(dataPointer: IDataPointer, count: number, targetArray: number[]) {\r\n    while (targetArray.length !== count) {\r\n        const line = LineToArray(dataPointer.lines[dataPointer.index++]);\r\n        targetArray.push(...line);\r\n    }\r\n}\r\n\r\nfunction InterpolateCandelaValues(data: IIESData, phi: number, theta: number): number {\r\n    let phiIndex = 0;\r\n    let thetaIndex = 0;\r\n    let startTheta = 0;\r\n    let endTheta = 0;\r\n    let startPhi = 0;\r\n    let endPhi = 0;\r\n\r\n    // Check if the angle is outside the range\r\n    for (let index = 0; index < data.numberOfHorizontalAngles - 1; index++) {\r\n        if (theta < data.horizontalAngles[index + 1] || index === data.numberOfHorizontalAngles - 2) {\r\n            thetaIndex = index;\r\n            startTheta = data.horizontalAngles[index];\r\n            endTheta = data.horizontalAngles[index + 1];\r\n\r\n            break;\r\n        }\r\n    }\r\n\r\n    for (let index = 0; index < data.numberOfVerticalAngles - 1; index++) {\r\n        if (phi < data.verticalAngles[index + 1] || index === data.numberOfVerticalAngles - 2) {\r\n            phiIndex = index;\r\n            startPhi = data.verticalAngles[index];\r\n            endPhi = data.verticalAngles[index + 1];\r\n\r\n            break;\r\n        }\r\n    }\r\n\r\n    const deltaTheta = endTheta - startTheta;\r\n    const deltaPhi = endPhi - startPhi;\r\n\r\n    if (deltaPhi === 0) {\r\n        return 0;\r\n    }\r\n\r\n    // Interpolate\r\n    const t1 = deltaTheta === 0 ? 0 : (theta - startTheta) / deltaTheta;\r\n    const t2 = (phi - startPhi) / deltaPhi;\r\n\r\n    const nextThetaIndex = deltaTheta === 0 ? thetaIndex : thetaIndex + 1;\r\n\r\n    const v1 = Lerp(data.candelaValues[thetaIndex][phiIndex], data.candelaValues[nextThetaIndex][phiIndex], t1);\r\n    const v2 = Lerp(data.candelaValues[thetaIndex][phiIndex + 1], data.candelaValues[nextThetaIndex][phiIndex + 1], t1);\r\n    const v = Lerp(v1, v2, t2);\r\n\r\n    return v;\r\n}\r\n/**\r\n * Interface for IES texture data.\r\n */\r\nexport interface IIESTextureData {\r\n    /** The width of the texture */\r\n    width: number;\r\n    /** The height of the texture */\r\n    height: number;\r\n    /** The data of the texture */\r\n    data: Float32Array;\r\n}\r\n\r\n/**\r\n * Generates IES data buffer from a string representing the IES data.\r\n * @param uint8Array defines the IES data\r\n * @returns the IES data buffer\r\n * @see https://ieslibrary.com/browse\r\n * @see https://playground.babylonjs.com/#UQGPDT#1\r\n */\r\nexport function LoadIESData(uint8Array: Uint8Array): IIESTextureData {\r\n    const decoder = new TextDecoder(\"utf-8\");\r\n    const source = decoder.decode(uint8Array);\r\n\r\n    // Read data\r\n    const dataPointer: IDataPointer = {\r\n        lines: source.split(\"\\n\"),\r\n        index: 0,\r\n    };\r\n    const data: IIESData = { version: dataPointer.lines[0], candelaValues: [], horizontalAngles: [], verticalAngles: [], numberOfHorizontalAngles: 0, numberOfVerticalAngles: 0 };\r\n\r\n    // Skip metadata\r\n    dataPointer.index = 1;\r\n    while (dataPointer.lines.length > 0 && !dataPointer.lines[dataPointer.index].includes(\"TILT=\")) {\r\n        dataPointer.index++;\r\n    }\r\n\r\n    // Process tilt data?\r\n    if (dataPointer.lines[dataPointer.index].includes(\"INCLUDE\")) {\r\n        // Not supported yet as I did not manage to find an example :)\r\n    }\r\n    dataPointer.index++;\r\n\r\n    // Header\r\n    const header = LineToArray(dataPointer.lines[dataPointer.index++]);\r\n    data.numberOfLights = header[0];\r\n    data.lumensPerLamp = header[1];\r\n    data.candelaMultiplier = header[2];\r\n    data.numberOfVerticalAngles = header[3];\r\n    data.numberOfHorizontalAngles = header[4];\r\n    data.photometricType = header[5]; // We ignore cylindrical type for now. Will add support later if needed\r\n    data.unitsType = header[6];\r\n    data.width = header[7];\r\n    data.length = header[8];\r\n    data.height = header[9];\r\n\r\n    // Additional data\r\n    const additionalData = LineToArray(dataPointer.lines[dataPointer.index++]);\r\n    data.ballastFactor = additionalData[0];\r\n    data.fileGenerationType = additionalData[1];\r\n    data.inputWatts = additionalData[2];\r\n\r\n    // Prepare arrays\r\n    for (let index = 0; index < data.numberOfHorizontalAngles; index++) {\r\n        data.candelaValues[index] = [];\r\n    }\r\n\r\n    // Vertical angles\r\n    ReadArray(dataPointer, data.numberOfVerticalAngles, data.verticalAngles);\r\n\r\n    // Horizontal angles\r\n    ReadArray(dataPointer, data.numberOfHorizontalAngles, data.horizontalAngles);\r\n\r\n    // Candela values\r\n    for (let index = 0; index < data.numberOfHorizontalAngles; index++) {\r\n        ReadArray(dataPointer, data.numberOfVerticalAngles, data.candelaValues[index]);\r\n    }\r\n\r\n    // Evaluate candela values\r\n    let maxCandela = -1;\r\n    for (let index = 0; index < data.numberOfHorizontalAngles; index++) {\r\n        for (let subIndex = 0; subIndex < data.numberOfVerticalAngles; subIndex++) {\r\n            data.candelaValues[index][subIndex] *= data.candelaValues[index][subIndex] * data.candelaMultiplier * data.ballastFactor * data.fileGenerationType;\r\n            maxCandela = Math.max(maxCandela, data.candelaValues[index][subIndex]);\r\n        }\r\n    }\r\n\r\n    // Normalize candela values\r\n    if (maxCandela > 0) {\r\n        for (let index = 0; index < data.numberOfHorizontalAngles; index++) {\r\n            for (let subIndex = 0; subIndex < data.numberOfVerticalAngles; subIndex++) {\r\n                data.candelaValues[index][subIndex] /= maxCandela;\r\n            }\r\n        }\r\n    }\r\n\r\n    // Create the cylindrical texture\r\n    const height = 180;\r\n    const width = height * 2;\r\n    const size = width * height;\r\n    const arrayBuffer = new Float32Array(width * height);\r\n\r\n    // Fill the texture\r\n    const startTheta = data.horizontalAngles[0];\r\n    const endTheta = data.horizontalAngles[data.numberOfHorizontalAngles - 1];\r\n    for (let index = 0; index < size; index++) {\r\n        let theta = index % width;\r\n        const phi = Math.floor(index / width);\r\n\r\n        // Symmetry\r\n        if (endTheta - startTheta !== 0 && (theta < startTheta || theta >= endTheta)) {\r\n            theta %= endTheta * 2;\r\n            if (theta > endTheta) {\r\n                theta = endTheta * 2 - theta;\r\n            }\r\n        }\r\n\r\n        arrayBuffer[phi + theta * height] = InterpolateCandelaValues(data, phi, theta);\r\n    }\r\n\r\n    // So far we only need the first half of the first row of the texture as we only support IES for spot light. We can add support for other types later.\r\n    return {\r\n        width: width / 2,\r\n        height: 1,\r\n        data: arrayBuffer,\r\n    };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,IAAI,EAAE,6CAAyC;;AA4BxD,SAAS,WAAW,CAAC,IAAY;IAC7B,OAAO,IAAI,CACN,KAAK,CAAC,GAAG,CAAC,CACV,MAAM,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,KAAK,EAAE,CAAC,CACvB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,SAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC;AAED,SAAS,SAAS,CAAC,WAAyB,EAAE,KAAa,EAAE,WAAqB;IAC9E,MAAO,WAAW,CAAC,MAAM,KAAK,KAAK,CAAE,CAAC;QAClC,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjE,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC;AACL,CAAC;AAED,SAAS,wBAAwB,CAAC,IAAc,EAAE,GAAW,EAAE,KAAa;IACxE,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,0CAA0C;IAC1C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,wBAAwB,GAAG,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;QACrE,IAAI,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;YAC1F,UAAU,GAAG,KAAK,CAAC;YACnB,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC1C,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAE5C,MAAM;QACV,CAAC;IACL,CAAC;IAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,sBAAsB,GAAG,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;QACnE,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YACpF,QAAQ,GAAG,KAAK,CAAC;YACjB,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAExC,MAAM;QACV,CAAC;IACL,CAAC;IAED,MAAM,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC;IACzC,MAAM,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC;IAEnC,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;QACjB,OAAO,CAAC,CAAC;IACb,CAAC;IAED,cAAc;IACd,MAAM,EAAE,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC;IACpE,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;IAEvC,MAAM,cAAc,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC;IAEtE,MAAM,EAAE,GAAG,uLAAA,AAAI,EAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;IAC5G,MAAM,EAAE,IAAG,sLAAA,AAAI,EAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACpH,MAAM,CAAC,mLAAG,OAAA,AAAI,EAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAE3B,OAAO,CAAC,CAAC;AACb,CAAC;AAoBK,SAAU,WAAW,CAAC,UAAsB;IAC9C,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IACzC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAE1C,YAAY;IACZ,MAAM,WAAW,GAAiB;QAC9B,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;QACzB,KAAK,EAAE,CAAC;KACX,CAAC;IACF,MAAM,IAAI,GAAa;QAAE,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QAAE,aAAa,EAAE,EAAE;QAAE,gBAAgB,EAAE,EAAE;QAAE,cAAc,EAAE,EAAE;QAAE,wBAAwB,EAAE,CAAC;QAAE,sBAAsB,EAAE,CAAC;IAAA,CAAE,CAAC;IAE9K,gBAAgB;IAChB,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;IACtB,MAAO,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAE,CAAC;QAC7F,WAAW,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED,qBAAqB;IACrB,IAAI,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;IAC3D,8DAA8D;IAClE,CAAC;IACD,WAAW,CAAC,KAAK,EAAE,CAAC;IAEpB,SAAS;IACT,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACnE,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAChC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACnC,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACxC,IAAI,CAAC,wBAAwB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,uEAAuE;IACzG,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAExB,kBAAkB;IAClB,MAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC3E,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IACvC,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;IAEpC,iBAAiB;IACjB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE,KAAK,EAAE,CAAE,CAAC;QACjE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;IACnC,CAAC;IAED,kBAAkB;IAClB,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAEzE,oBAAoB;IACpB,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAE7E,iBAAiB;IACjB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE,KAAK,EAAE,CAAE,CAAC;QACjE,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACnF,CAAC;IAED,0BAA0B;IAC1B,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC;IACpB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE,KAAK,EAAE,CAAE,CAAC;QACjE,IAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,QAAQ,EAAE,CAAE,CAAC;YACxE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACnJ,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3E,CAAC;IACL,CAAC;IAED,2BAA2B;IAC3B,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;QACjB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,wBAAwB,EAAE,KAAK,EAAE,CAAE,CAAC;YACjE,IAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,QAAQ,EAAE,CAAE,CAAC;gBACxE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC;YACtD,CAAC;QACL,CAAC;IACL,CAAC;IAED,iCAAiC;IACjC,MAAM,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;IACzB,MAAM,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC;IAC5B,MAAM,WAAW,GAAG,IAAI,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;IAErD,mBAAmB;IACnB,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC,CAAC;IAC1E,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,EAAE,CAAE,CAAC;QACxC,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;QAEtC,WAAW;QACX,IAAI,QAAQ,GAAG,UAAU,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,UAAU,IAAI,KAAK,IAAI,QAAQ,CAAC,EAAE,CAAC;YAC3E,KAAK,IAAI,QAAQ,GAAG,CAAC,CAAC;YACtB,IAAI,KAAK,GAAG,QAAQ,EAAE,CAAC;gBACnB,KAAK,GAAG,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC;YACjC,CAAC;QACL,CAAC;QAED,WAAW,CAAC,GAAG,GAAG,KAAK,GAAG,MAAM,CAAC,GAAG,wBAAwB,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IACnF,CAAC;IAED,sJAAsJ;IACtJ,OAAO;QACH,KAAK,EAAE,KAAK,GAAG,CAAC;QAChB,MAAM,EAAE,CAAC;QACT,IAAI,EAAE,WAAW;KACpB,CAAC;AACN,CAAC", "debugId": null}}, {"offset": {"line": 5112, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Lights/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Lights/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-restricted-imports */\r\nexport * from \"./light\";\r\nexport * from \"./shadowLight\";\r\nexport * from \"./Shadows/index\";\r\nexport * from \"./directionalLight\";\r\nexport * from \"./hemisphericLight\";\r\nexport * from \"./pointLight\";\r\nexport * from \"./spotLight\";\r\nexport * from \"./areaLight\";\r\nexport * from \"./rectAreaLight\";\r\nexport * from \"./IES/iesLoader\";\r\n"], "names": [], "mappings": "AAAA,2DAAA,EAA6D;AAC7D,cAAc,SAAS,CAAC;AACxB,cAAc,eAAe,CAAC;AAC9B,cAAc,iBAAiB,CAAC;AAChC,cAAc,oBAAoB,CAAC;AACnC,cAAc,oBAAoB,CAAC;AACnC,cAAc,cAAc,CAAC;AAC7B,cAAc,aAAa,CAAC;AAC5B,cAAc,aAAa,CAAC;AAC5B,cAAc,iBAAiB,CAAC;AAChC,cAAc,iBAAiB,CAAC", "debugId": null}}]}