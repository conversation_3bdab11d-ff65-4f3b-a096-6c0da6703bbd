"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[971],{30207:(e,t,s)=>{s.d(t,{Ez:()=>x,e:()=>w});var i=s(89048),n=s(94500),r=s(3513),a=s(28488),o=s(78057),h=s(12582),l=s(1743),d=s(98595),c=s(31892),u=s(29250),f=s(60665),g=s(47862),_=s(93349),m=s(56441),I=s(15669),p=s(94485),M=s(78071),D=s(53897),y=s(93625),v=s(20482),b=s(35047),S=s(12663),B=s(16400);class x{}class P{constructor(){this.batchCache=new O(this),this.batchCacheReplacementModeInFrozenMode=new O(this),this.instancesBufferSize=2048}}class R{constructor(){this.renderPasses={}}}class O{constructor(e){this.parent=e,this.mustReturn=!1,this.visibleInstances=[],this.renderSelf=[],this.hardwareInstancedRendering=[]}}class A{constructor(){this.instancesCount=0,this.matrixBuffer=null,this.previousMatrixBuffer=null,this.matrixBufferSize=512,this.matrixData=null,this.boundingVectors=[],this.worldMatrices=null}}class C{constructor(){this._areNormalsFrozen=!1,this._source=null,this.meshMap=null,this._preActivateId=-1,this._LODLevels=[],this._useLODScreenCoverage=!1,this._effectiveMaterial=null,this._forcedInstanceCount=0,this._overrideRenderingFillMode=null}}let V={source:null,parent:null,doNotCloneChildren:!1,clonePhysicsImpostor:!0,cloneThinInstances:!1};class w extends _.u{static _GetDefaultSideOrientation(e){return e||w.FRONTSIDE}get useLODScreenCoverage(){return this._internalMeshDataInfo._useLODScreenCoverage}set useLODScreenCoverage(e){this._internalMeshDataInfo._useLODScreenCoverage=e,this._sortLODLevels()}get computeBonesUsingShaders(){return this._internalAbstractMeshDataInfo._computeBonesUsingShaders}set computeBonesUsingShaders(e){this._internalAbstractMeshDataInfo._computeBonesUsingShaders!==e&&(e&&this._internalMeshDataInfo._sourcePositions&&(this.setVerticesData(u.R.PositionKind,this._internalMeshDataInfo._sourcePositions,!0),this._internalMeshDataInfo._sourceNormals&&this.setVerticesData(u.R.NormalKind,this._internalMeshDataInfo._sourceNormals,!0),this._internalMeshDataInfo._sourcePositions=null,this._internalMeshDataInfo._sourceNormals=null),this._internalAbstractMeshDataInfo._computeBonesUsingShaders=e,this._markSubMeshesAsAttributesDirty())}get onBeforeRenderObservable(){return this._internalMeshDataInfo._onBeforeRenderObservable||(this._internalMeshDataInfo._onBeforeRenderObservable=new i.cP),this._internalMeshDataInfo._onBeforeRenderObservable}get onBeforeBindObservable(){return this._internalMeshDataInfo._onBeforeBindObservable||(this._internalMeshDataInfo._onBeforeBindObservable=new i.cP),this._internalMeshDataInfo._onBeforeBindObservable}get onAfterRenderObservable(){return this._internalMeshDataInfo._onAfterRenderObservable||(this._internalMeshDataInfo._onAfterRenderObservable=new i.cP),this._internalMeshDataInfo._onAfterRenderObservable}get onBetweenPassObservable(){return this._internalMeshDataInfo._onBetweenPassObservable||(this._internalMeshDataInfo._onBetweenPassObservable=new i.cP),this._internalMeshDataInfo._onBetweenPassObservable}get onBeforeDrawObservable(){return this._internalMeshDataInfo._onBeforeDrawObservable||(this._internalMeshDataInfo._onBeforeDrawObservable=new i.cP),this._internalMeshDataInfo._onBeforeDrawObservable}set onBeforeDraw(e){this._onBeforeDrawObserver&&this.onBeforeDrawObservable.remove(this._onBeforeDrawObserver),this._onBeforeDrawObserver=this.onBeforeDrawObservable.add(e)}get hasInstances(){return this.instances.length>0}get hasThinInstances(){return(this.forcedInstanceCount||this._thinInstanceDataStorage.instancesCount||0)>0}get forcedInstanceCount(){return this._internalMeshDataInfo._forcedInstanceCount}set forcedInstanceCount(e){this._internalMeshDataInfo._forcedInstanceCount=e}get sideOrientation(){return this._internalMeshDataInfo._sideOrientation}set sideOrientation(e){this._internalMeshDataInfo._sideOrientation=e,this._internalAbstractMeshDataInfo._sideOrientationHint=this._scene.useRightHandedSystem&&1===e||!this._scene.useRightHandedSystem&&0===e}get _effectiveSideOrientation(){return this._internalMeshDataInfo._effectiveSideOrientation}get overrideMaterialSideOrientation(){return this.sideOrientation}set overrideMaterialSideOrientation(e){this.sideOrientation=e,this.material&&(this.material.sideOrientation=null)}get overrideRenderingFillMode(){return this._internalMeshDataInfo._overrideRenderingFillMode}set overrideRenderingFillMode(e){this._internalMeshDataInfo._overrideRenderingFillMode=e}get material(){return this._internalAbstractMeshDataInfo._material}set material(e){e&&(this.material&&null===this.material.sideOrientation||this._internalAbstractMeshDataInfo._sideOrientationHint)&&(e.sideOrientation=null),this._setMaterial(e)}get source(){return this._internalMeshDataInfo._source}get cloneMeshMap(){return this._internalMeshDataInfo.meshMap}get isUnIndexed(){return this._unIndexed}set isUnIndexed(e){this._unIndexed!==e&&(this._unIndexed=e,this._markSubMeshesAsAttributesDirty())}get worldMatrixInstancedBuffer(){let e=this._instanceDataStorage.renderPasses[this._instanceDataStorage.engine.isWebGPU?this._instanceDataStorage.engine.currentRenderPassId:0];return e?e.instancesData:void 0}get previousWorldMatrixInstancedBuffer(){let e=this._instanceDataStorage.renderPasses[this._instanceDataStorage.engine.isWebGPU?this._instanceDataStorage.engine.currentRenderPassId:0];return e?e.instancesPreviousData:void 0}get manualUpdateOfWorldMatrixInstancedBuffer(){return this._instanceDataStorage.manualUpdate}set manualUpdateOfWorldMatrixInstancedBuffer(e){this._instanceDataStorage.manualUpdate=e}get manualUpdateOfPreviousWorldMatrixInstancedBuffer(){return this._instanceDataStorage.previousManualUpdate}set manualUpdateOfPreviousWorldMatrixInstancedBuffer(e){this._instanceDataStorage.previousManualUpdate=e}get forceWorldMatrixInstancedBufferUpdate(){return this._instanceDataStorage.forceMatrixUpdates}set forceWorldMatrixInstancedBufferUpdate(e){this._instanceDataStorage.forceMatrixUpdates=e}_copySource(e,t,s=!0,i=!1){let n=this.getScene();if(e._geometry&&e._geometry.applyToMesh(this),r.r.DeepCopy(e,this,["name","material","skeleton","instances","parent","uniqueId","source","metadata","morphTargetManager","hasInstances","worldMatrixInstancedBuffer","previousWorldMatrixInstancedBuffer","hasLODLevels","geometry","isBlocked","areNormalsFrozen","facetNb","isFacetDataEnabled","lightSources","useBones","isAnInstance","collider","edgesRenderer","forward","up","right","absolutePosition","absoluteScaling","absoluteRotationQuaternion","isWorldMatrixFrozen","nonUniformScaling","behaviors","worldMatrixFromCache","hasThinInstances","cloneMeshMap","hasBoundingInfo","physicsBody","physicsImpostor"],["_poseMatrix"]),this._internalMeshDataInfo._source=e,n.useClonedMeshMap&&(e._internalMeshDataInfo.meshMap||(e._internalMeshDataInfo.meshMap={}),e._internalMeshDataInfo.meshMap[this.uniqueId]=this),this._originalBuilderSideOrientation=e._originalBuilderSideOrientation,this._creationDataStorage=e._creationDataStorage,e._ranges){let t=e._ranges;for(let e in t)Object.prototype.hasOwnProperty.call(t,e)&&t[e]&&this.createAnimationRange(e,t[e].from,t[e].to)}if(e.metadata&&e.metadata.clone?this.metadata=e.metadata.clone():this.metadata=e.metadata,this._internalMetadata=e._internalMetadata,a.Y&&a.Y.HasTags(e)&&a.Y.AddTagsTo(this,a.Y.GetTags(e,!0)),this.setEnabled(e.isEnabled(!1)),this.parent=e.parent,this.setPivotMatrix(e.getPivotMatrix(),this._postMultiplyPivotMatrix),this.id=this.name+"."+e.id,this.material=e.material,!t){let n=e.getDescendants(!0);for(let e=0;e<n.length;e++){let r=n[e];r._isMesh?(V.parent=this,V.doNotCloneChildren=t,V.clonePhysicsImpostor=s,V.cloneThinInstances=i,r.clone(this.name+"."+r.name,V)):r.clone&&r.clone(this.name+"."+r.name,this)}}if(e.morphTargetManager&&(this.morphTargetManager=e.morphTargetManager),n.getPhysicsEngine){let t=n.getPhysicsEngine();if(s&&t)if(1===t.getPluginVersion()){let s=t.getImpostorForPhysicsObject(e);s&&(this.physicsImpostor=s.clone(this))}else 2===t.getPluginVersion()&&e.physicsBody&&e.physicsBody.clone(this)}for(let t=0;t<n.particleSystems.length;t++){let s=n.particleSystems[t];s.emitter===e&&s.clone(s.name,this)}if(this.skeleton=e.skeleton,i&&(e._thinInstanceDataStorage.matrixData?(this.thinInstanceSetBuffer("matrix",new Float32Array(e._thinInstanceDataStorage.matrixData),16,!e._thinInstanceDataStorage.matrixBuffer.isUpdatable()),this._thinInstanceDataStorage.matrixBufferSize=e._thinInstanceDataStorage.matrixBufferSize,this._thinInstanceDataStorage.instancesCount=e._thinInstanceDataStorage.instancesCount):this._thinInstanceDataStorage.matrixBufferSize=e._thinInstanceDataStorage.matrixBufferSize,e._userThinInstanceBuffersStorage)){let t=e._userThinInstanceBuffersStorage;for(let e in t.data)this.thinInstanceSetBuffer(e,new Float32Array(t.data[e]),t.strides[e],!t.vertexBuffers?.[e]?.isUpdatable()),this._userThinInstanceBuffersStorage.sizes[e]=t.sizes[e]}this.refreshBoundingInfo(!0,!0),this.computeWorldMatrix(!0)}constructor(e,t=null,s=null,n=null,r,a=!0){super(e,t),this._internalMeshDataInfo=new C,this.delayLoadState=0,this.instances=[],this._creationDataStorage=null,this._geometry=null,this._thinInstanceDataStorage=new A,this._shouldGenerateFlatShading=!1,this._originalBuilderSideOrientation=w.DEFAULTSIDE,this.ignoreCameraMaxZ=!1,t=this.getScene(),this._instanceDataStorage=new R,this._instanceDataStorage.engine=t.getEngine(),this._scene.useRightHandedSystem?this.sideOrientation=0:this.sideOrientation=1,this._onBeforeDraw=(e,t,s)=>{e&&s&&(this._uniformBuffer?this.transferToEffect(t):s.bindOnlyWorldMatrix(t))};let o=null,h=!1;s&&void 0===s._addToSceneRootNodes?(o=s.parent??null,n=s.source??null,r=s.doNotCloneChildren??!1,a=s.clonePhysicsImpostor??!0,h=s.cloneThinInstances??!1):o=s,n&&this._copySource(n,r,a,h),null!==o&&(this.parent=o),this._instanceDataStorage.hardwareInstancedRendering=this.getEngine().getCaps().instancedArrays,this._internalMeshDataInfo._onMeshReadyObserverAdded=e=>{e.unregisterOnNextCall=!0,this.isReady(!0)?this.onMeshReadyObservable.notifyObservers(this):this._internalMeshDataInfo._checkReadinessObserver||(this._internalMeshDataInfo._checkReadinessObserver=this._scene.onBeforeRenderObservable.add(()=>{this.isReady(!0)&&(this._scene.onBeforeRenderObservable.remove(this._internalMeshDataInfo._checkReadinessObserver),this._internalMeshDataInfo._checkReadinessObserver=null,this.onMeshReadyObservable.notifyObservers(this))}))},this.onMeshReadyObservable=new i.cP(this._internalMeshDataInfo._onMeshReadyObserverAdded),n&&n.onClonedObservable.notifyObservers(this)}instantiateHierarchy(e=null,t,s){let i=0===this.getTotalVertices()||t&&t.doNotInstantiate&&(!0===t.doNotInstantiate||t.doNotInstantiate(this))?this.clone("Clone of "+(this.name||this.id),e||this.parent,!0):this.createInstance("instance of "+(this.name||this.id));for(let n of(i.parent=e||this.parent,i.position=this.position.clone(),i.scaling=this.scaling.clone(),this.rotationQuaternion?i.rotationQuaternion=this.rotationQuaternion.clone():i.rotation=this.rotation.clone(),s&&s(this,i),this.getChildTransformNodes(!0)))"InstancedMesh"===n.getClassName()&&"Mesh"===i.getClassName()&&n.sourceMesh===this?n.instantiateHierarchy(i,{doNotInstantiate:t&&t.doNotInstantiate||!1,newSourcedMesh:i},s):n.instantiateHierarchy(i,t,s);return i}getClassName(){return"Mesh"}get _isMesh(){return!0}toString(e){let t=super.toString(e);if(t+=", n vertices: "+this.getTotalVertices(),t+=", parent: "+(this._waitingParentId?this._waitingParentId:this.parent?this.parent.name:"NONE"),this.animations)for(let s=0;s<this.animations.length;s++)t+=", animation[0]: "+this.animations[s].toString(e);if(e)if(this._geometry){let e=this.getIndices(),s=this.getVerticesData(u.R.PositionKind);s&&e&&(t+=", flat shading: "+(s.length/3===e.length?"YES":"NO"))}else t+=", flat shading: UNKNOWN";return t}_unBindEffect(){for(let e of(super._unBindEffect(),this.instances))e._unBindEffect()}get hasLODLevels(){return this._internalMeshDataInfo._LODLevels.length>0}getLODLevels(){return this._internalMeshDataInfo._LODLevels}_sortLODLevels(){let e=this._internalMeshDataInfo._useLODScreenCoverage?-1:1;this._internalMeshDataInfo._LODLevels.sort((t,s)=>t.distanceOrScreenCoverage<s.distanceOrScreenCoverage?e:t.distanceOrScreenCoverage>s.distanceOrScreenCoverage?-e:0)}addLODLevel(e,t){if(t&&t._masterMesh)return y.V.Warn("You cannot use a mesh as LOD level twice"),this;let s=new B.L(e,t);return this._internalMeshDataInfo._LODLevels.push(s),t&&(t._masterMesh=this),this._sortLODLevels(),this}getLODLevelAtDistance(e){let t=this._internalMeshDataInfo;for(let s=0;s<t._LODLevels.length;s++){let i=t._LODLevels[s];if(i.distanceOrScreenCoverage===e)return i.mesh}return null}removeLODLevel(e){let t=this._internalMeshDataInfo;for(let s=0;s<t._LODLevels.length;s++)t._LODLevels[s].mesh===e&&(t._LODLevels.splice(s,1),e&&(e._masterMesh=null));return this._sortLODLevels(),this}getLOD(e,t){let s=this._internalMeshDataInfo;if(!s._LODLevels||0===s._LODLevels.length)return this;let i=t||this.getBoundingInfo().boundingSphere,n=e.mode===h.i.ORTHOGRAPHIC_CAMERA?e.minZ:i.centerWorld.subtract(e.globalPosition).length(),r=n,a=1;if(s._useLODScreenCoverage){let t=e.screenArea,s=i.radiusWorld*e.minZ/n;r=(s=s*s*Math.PI)/t,a=-1}if(a*s._LODLevels[s._LODLevels.length-1].distanceOrScreenCoverage>a*r)return this.onLODLevelSelection&&this.onLODLevelSelection(r,this,this),this;for(let e=0;e<s._LODLevels.length;e++){let t=s._LODLevels[e];if(a*t.distanceOrScreenCoverage<a*r){if(t.mesh){if(4===t.mesh.delayLoadState)return t.mesh._checkDelayState(),this;if(2===t.mesh.delayLoadState)return this;t.mesh._preActivate(),t.mesh._updateSubMeshesBoundingInfo(this.worldMatrixFromCache)}return this.onLODLevelSelection&&this.onLODLevelSelection(r,this,t.mesh),t.mesh}}return this.onLODLevelSelection&&this.onLODLevelSelection(r,this,this),this}get geometry(){return this._geometry}getTotalVertices(){return null===this._geometry||void 0===this._geometry?0:this._geometry.getTotalVertices()}getVerticesData(e,t,s,i){if(!this._geometry)return null;let n=i?void 0:this._userInstancedBuffersStorage?.vertexBuffers[e]?.getFloatData(this.instances.length+1,s||t&&1!==this._geometry.meshes.length);return n||(n=this._geometry.getVerticesData(e,t,s)),n}copyVerticesData(e,t){this._geometry&&this._geometry.copyVerticesData(e,t)}getVertexBuffer(e,t){return this._geometry?(t?void 0:this._userInstancedBuffersStorage?.vertexBuffers[e])??this._geometry.getVertexBuffer(e):null}isVerticesDataPresent(e,t){return this._geometry?!t&&this._userInstancedBuffersStorage?.vertexBuffers[e]!==void 0||this._geometry.isVerticesDataPresent(e):!!this._delayInfo&&-1!==this._delayInfo.indexOf(e)}isVertexBufferUpdatable(e,t){if(!this._geometry)return!!this._delayInfo&&-1!==this._delayInfo.indexOf(e);if(!t){let t=this._userInstancedBuffersStorage?.vertexBuffers[e];if(t)return t.isUpdatable()}return this._geometry.isVertexBufferUpdatable(e)}getVerticesDataKinds(e){if(!this._geometry){let e=[];if(this._delayInfo)for(let t of this._delayInfo)e.push(t);return e}let t=this._geometry.getVerticesDataKinds();if(!e&&this._userInstancedBuffersStorage)for(let e in this._userInstancedBuffersStorage.vertexBuffers)-1===t.indexOf(e)&&t.push(e);return t}getTotalIndices(){return this._geometry?this._geometry.getTotalIndices():0}getIndices(e,t){return this._geometry?this._geometry.getIndices(e,t):[]}get isBlocked(){return null!==this._masterMesh&&void 0!==this._masterMesh}isReady(e=!1,t=!1){if(2===this.delayLoadState||!super.isReady(e))return!1;if(!this.subMeshes||0===this.subMeshes.length||!e)return!0;let s=this.getEngine(),i=this.getScene(),n=t||s.getCaps().instancedArrays&&(this.instances.length>0||this.hasThinInstances);this.computeWorldMatrix();let r=this.material||i.defaultMaterial;if(r){if(r._storeEffectOnSubMeshes)for(let e of this.subMeshes){let t=e.getMaterial();if(t){if(t._storeEffectOnSubMeshes){if(!t.isReadyForSubMesh(this,e,n))return!1}else if(!t.isReady(this,n))return!1}}else if(!r.isReady(this,n))return!1}let a=s.currentRenderPassId;for(let e of this.lightSources){let t=e.getShadowGenerators();if(!t)continue;let i=t.values();for(let e=i.next();!0!==e.done;e=i.next()){let t=e.value;if(t&&(!t.getShadowMap()?.renderList||t.getShadowMap()?.renderList&&t.getShadowMap()?.renderList?.indexOf(this)!==-1)){let e=t.getShadowMap().renderPassIds??[s.currentRenderPassId];for(let i=0;i<e.length;++i)for(let r of(s.currentRenderPassId=e[i],this.subMeshes))if(!t.isReady(r,n,r.getMaterial()?.needAlphaBlendingForMesh(this)??!1))return s.currentRenderPassId=a,!1;s.currentRenderPassId=a}}}for(let e of this._internalMeshDataInfo._LODLevels)if(e.mesh&&!e.mesh.isReady(n))return!1;return!0}get areNormalsFrozen(){return this._internalMeshDataInfo._areNormalsFrozen}freezeNormals(){return this._internalMeshDataInfo._areNormalsFrozen=!0,this}unfreezeNormals(){return this._internalMeshDataInfo._areNormalsFrozen=!1,this}set overridenInstanceCount(e){this._instanceDataStorage.overridenInstanceCount=e}_getInstanceDataStorage(){let e=this._instanceDataStorage.engine.isWebGPU?this._instanceDataStorage.engine.currentRenderPassId:0,t=this._instanceDataStorage.renderPasses[e];return t||(t=new P,this._instanceDataStorage.renderPasses[e]=t),t}_preActivate(){let e=this._internalMeshDataInfo,t=this.getScene().getRenderId();return e._preActivateId===t||(e._preActivateId=t,this.hasInstances&&(this._getInstanceDataStorage().visibleInstances=null)),this}_preActivateForIntermediateRendering(e){if(!this.hasInstances)return this;let t=this._getInstanceDataStorage();return t.visibleInstances&&(t.visibleInstances.intermediateDefaultRenderId=e),this}_registerInstanceForRenderId(e,t){let s=this._getInstanceDataStorage();return s.visibleInstances||(s.visibleInstances={defaultRenderId:t,selfDefaultRenderId:this._renderId,intermediateDefaultRenderId:-1}),s.visibleInstances[t]||(void 0!==s.previousRenderId&&this._instanceDataStorage.isFrozen&&(s.visibleInstances[s.previousRenderId]=null),s.previousRenderId=t,s.visibleInstances[t]=[]),s.visibleInstances[t].push(e),this}_afterComputeWorldMatrix(){super._afterComputeWorldMatrix(),this.hasThinInstances&&(this.doNotSyncBoundingInfo||this.thinInstanceRefreshBoundingInfo(!1))}_postActivate(){this.edgesShareWithInstances&&this.edgesRenderer&&this.edgesRenderer.isEnabled&&this._renderingGroup&&(this._renderingGroup._edgesRenderers.pushNoDuplicate(this.edgesRenderer),this.edgesRenderer.customInstances.push(this.getWorldMatrix()))}refreshBoundingInfo(e=!1,t=!1){if(this.hasBoundingInfo&&this.getBoundingInfo().isLocked)return this;let s=this.geometry?this.geometry.boundingBias:null;return this._refreshBoundingInfo(this._getData("object"==typeof e?e:{applySkeleton:e,applyMorph:t},null,u.R.PositionKind),s),this}_createGlobalSubMesh(e){let t=this.getTotalVertices();if(!t||!this.getIndices())return null;if(this.subMeshes&&this.subMeshes.length>0){let s=this.getIndices();if(!s)return null;let i=s.length,n=!1;if(e)n=!0;else for(let e of this.subMeshes)if(e.indexStart+e.indexCount>i||e.verticesStart+e.verticesCount>t){n=!0;break}if(!n)return this.subMeshes[0]}return this.releaseSubMeshes(),new m.K(0,0,t,0,this.getTotalIndices()||t,this)}subdivide(e){if(e<1)return;let t=this.getTotalIndices(),s=t/e|0,i=0;for(;s%3!=0;)s++;this.releaseSubMeshes();for(let n=0;n<e&&!(i>=t);n++)m.K.CreateFromIndices(0,i,n===e-1?t-i:s,this,void 0,!1),i+=s;this.refreshBoundingInfo(),this.synchronizeInstances()}setVerticesData(e,t,s=!1,i){if(this._geometry)this._geometry.setVerticesData(e,t,s,i);else{let i=new f.P;i.set(t,e);let n=this.getScene();new g.V(g.V.RandomId(),n,i,s,this)}return this}removeVerticesData(e){this._geometry&&this._geometry.removeVerticesData(e)}markVerticesDataAsUpdatable(e,t=!0){let s=this.getVertexBuffer(e);s&&s.isUpdatable()!==t&&this.setVerticesData(e,this.getVerticesData(e),t)}setVerticesBuffer(e,t=!0){return this._geometry||(this._geometry=g.V.CreateGeometryForMesh(this)),this._geometry.setVerticesBuffer(e,null,t),this}updateVerticesData(e,t,s,i){return this._geometry&&(i?(this.makeGeometryUnique(),this.updateVerticesData(e,t,s,!1)):this._geometry.updateVerticesData(e,t,s)),this}updateMeshPositions(e,t=!0){let s=this.getVerticesData(u.R.PositionKind);if(!s)return this;if(e(s),this.updateVerticesData(u.R.PositionKind,s,!1,!1),t){let e=this.getIndices(),t=this.getVerticesData(u.R.NormalKind);if(!t)return this;f.P.ComputeNormals(s,e,t),this.updateVerticesData(u.R.NormalKind,t,!1,!1)}return this}makeGeometryUnique(){if(!this._geometry||1===this._geometry.meshes.length)return this;let e=this._geometry,t=this._geometry.copy(g.V.RandomId());return e.releaseForMesh(this,!0),t.applyToMesh(this),this}setIndexBuffer(e,t,s,i=null){let n=this._geometry;n||(n=new g.V(g.V.RandomId(),this.getScene(),void 0,void 0,this)),n.setIndexBuffer(e,t,s,i)}setIndices(e,t=null,s=!1,i=!1){if(this._geometry)this._geometry.setIndices(e,t,s,i);else{let t=new f.P;t.indices=e;let i=this.getScene();new g.V(g.V.RandomId(),i,t,s,this)}return this}updateIndices(e,t,s=!1){return this._geometry&&this._geometry.updateIndices(e,t,s),this}toLeftHanded(){return this._geometry&&this._geometry.toLeftHanded(),this}_bind(e,t,s,i=!0){let n;if(!this._geometry)return this;let r=this.getScene().getEngine();if(this._unIndexed)n=this._getRenderingFillMode(s)===I.i.WireFrameFillMode?e._getLinesIndexBuffer(this.getIndices(),r):null;else switch(this._getRenderingFillMode(s)){case I.i.PointFillMode:n=null;break;case I.i.WireFrameFillMode:n=e._getLinesIndexBuffer(this.getIndices(),r);break;default:case I.i.TriangleFillMode:n=this._geometry.getIndexBuffer()}return this._bindDirect(t,n,i)}_bindDirect(e,t,s=!0){if(!this._geometry)return this;if(this.morphTargetManager&&this.morphTargetManager.isUsingTextureForTargets&&this.morphTargetManager._bind(e),s&&this._userInstancedBuffersStorage&&!this.hasThinInstances){if(this._instanceDataStorage.engine.isWebGPU&&this._userInstancedBuffersStorage.renderPasses&&this._userInstancedBuffersStorage.renderPasses[this._instanceDataStorage.engine.currentRenderPassId]){let e=this._userInstancedBuffersStorage.renderPasses[this._instanceDataStorage.engine.currentRenderPassId];for(let t in e)this._userInstancedBuffersStorage.vertexBuffers[t]=e[t]}this._geometry._bind(e,t,this._userInstancedBuffersStorage.vertexBuffers,this._userInstancedBuffersStorage.vertexArrayObjects)}else this._geometry._bind(e,t);return this}_draw(e,t,s){if(!this._geometry||!this._geometry.getVertexBuffers()||!this._unIndexed&&!this._geometry.getIndexBuffer())return this;this._internalMeshDataInfo._onBeforeDrawObservable&&this._internalMeshDataInfo._onBeforeDrawObservable.notifyObservers(this);let i=this.getScene().getEngine();return this._unIndexed&&t!==I.i.WireFrameFillMode||t==I.i.PointFillMode?i.drawArraysType(t,e.verticesStart,e.verticesCount,this.forcedInstanceCount||s):t==I.i.WireFrameFillMode?i.drawElementsType(t,0,e._linesIndexCount,this.forcedInstanceCount||s):i.drawElementsType(t,e.indexStart,e.indexCount,this.forcedInstanceCount||s),this}registerBeforeRender(e){return this.onBeforeRenderObservable.add(e),this}unregisterBeforeRender(e){return this.onBeforeRenderObservable.removeCallback(e),this}registerAfterRender(e){return this.onAfterRenderObservable.add(e),this}unregisterAfterRender(e){return this.onAfterRenderObservable.removeCallback(e),this}_getInstancesRenderList(e,t=!1){let s=this._getInstanceDataStorage();if(this._instanceDataStorage.isFrozen){if(t)return s.batchCacheReplacementModeInFrozenMode.hardwareInstancedRendering[e]=!1,s.batchCacheReplacementModeInFrozenMode.renderSelf[e]=!0,s.batchCacheReplacementModeInFrozenMode;if(s.previousBatch)return s.previousBatch}let i=this.getScene(),n=i._isInIntermediateRendering(),r=n?this._internalAbstractMeshDataInfo._onlyForInstancesIntermediate:this._internalAbstractMeshDataInfo._onlyForInstances,a=s.batchCache;if(a.mustReturn=!1,a.renderSelf[e]=t||!r&&this.isEnabled()&&this.isVisible,a.visibleInstances[e]=null,s.visibleInstances&&!t){let t=s.visibleInstances,r=i.getRenderId(),o=n?t.intermediateDefaultRenderId:t.defaultRenderId;a.visibleInstances[e]=t[r],!a.visibleInstances[e]&&o&&(a.visibleInstances[e]=t[o])}return a.hardwareInstancedRendering[e]=!t&&this._instanceDataStorage.hardwareInstancedRendering&&null!==a.visibleInstances[e]&&void 0!==a.visibleInstances[e],s.previousBatch=a,a}_updateInstancedBuffers(e,t,s,i,n,r){let a=t.visibleInstances[e._id],o=a?a.length:0,h=t.parent,d=this._instanceDataStorage,c=h.instancesBuffer,f=h.instancesPreviousBuffer,g=0,_=0,m=t.renderSelf[e._id],I=!c||s!==h.instancesBufferSize||this._scene.needsPreviousWorldMatrices&&!h.instancesPreviousBuffer;if(this._instanceDataStorage.manualUpdate||d.isFrozen&&!I)_=+!!m+o;else{let t=this.getWorldMatrix();if(m&&(this._scene.needsPreviousWorldMatrices&&(d.masterMeshPreviousWorldMatrix?(d.masterMeshPreviousWorldMatrix.copyToArray(h.instancesPreviousData,g),d.masterMeshPreviousWorldMatrix.copyFrom(t)):(d.masterMeshPreviousWorldMatrix=t.clone(),d.masterMeshPreviousWorldMatrix.copyToArray(h.instancesPreviousData,g))),t.copyToArray(h.instancesData,g),g+=16,_++),a){if(w.INSTANCEDMESH_SORT_TRANSPARENT&&this._scene.activeCamera&&e.getMaterial()?.needAlphaBlendingForMesh(e.getRenderingMesh())){let e=this._scene.activeCamera.globalPosition;for(let t=0;t<a.length;t++){let s=a[t];s._distanceToCamera=l.Pq.Distance(s.getBoundingInfo().boundingSphere.centerWorld,e)}a.sort((e,t)=>e._distanceToCamera>t._distanceToCamera?-1:+(e._distanceToCamera<t._distanceToCamera))}for(let e=0;e<a.length;e++){let t=a[e],s=t.getWorldMatrix();s.copyToArray(h.instancesData,g),this._scene.needsPreviousWorldMatrices&&(t._previousWorldMatrix?(t._previousWorldMatrix.copyToArray(h.instancesPreviousData,g),t._previousWorldMatrix.copyFrom(s)):(t._previousWorldMatrix=s.clone(),t._previousWorldMatrix.copyToArray(h.instancesPreviousData,g))),g+=16,_++}}}if(I){let e;if(c&&c.dispose(),f&&f.dispose(),c=new u.h(i,h.instancesData,!0,16,!1,!0),h.instancesBuffer=c,this._userInstancedBuffersStorage||(this._userInstancedBuffersStorage={data:{},vertexBuffers:{},strides:{},sizes:{},vertexArrayObjects:this.getEngine().getCaps().vertexArrayObject?{}:void 0}),this._instanceDataStorage.engine.isWebGPU){this._userInstancedBuffersStorage.renderPasses||(this._userInstancedBuffersStorage.renderPasses={});let t=this._instanceDataStorage.engine.currentRenderPassId;(e=this._userInstancedBuffersStorage.renderPasses[t])||(this._userInstancedBuffersStorage.renderPasses[t]=e={})}else e=this._userInstancedBuffersStorage.vertexBuffers;e.world0=c.createVertexBuffer("world0",0,4),e.world1=c.createVertexBuffer("world1",4,4),e.world2=c.createVertexBuffer("world2",8,4),e.world3=c.createVertexBuffer("world3",12,4),this._scene.needsPreviousWorldMatrices&&(f=new u.h(i,h.instancesPreviousData,!0,16,!1,!0),h.instancesPreviousBuffer=f,e.previousWorld0=f.createVertexBuffer("previousWorld0",0,4),e.previousWorld1=f.createVertexBuffer("previousWorld1",4,4),e.previousWorld2=f.createVertexBuffer("previousWorld2",8,4),e.previousWorld3=f.createVertexBuffer("previousWorld3",12,4)),this._invalidateInstanceVertexArrayObject()}else(!this._instanceDataStorage.isFrozen||this._instanceDataStorage.forceMatrixUpdates)&&(c.updateDirectly(h.instancesData,0,_),this._scene.needsPreviousWorldMatrices&&(!this._instanceDataStorage.manualUpdate||this._instanceDataStorage.previousManualUpdate)&&f.updateDirectly(h.instancesPreviousData,0,_));this._processInstancedBuffers(a,m),r&&void 0!==n&&(this.getScene()._activeIndices.addCount(e.indexCount*_,!1),i._currentDrawContext&&(i._currentDrawContext.useInstancing=!0),this._bind(e,r,n),this._draw(e,n,_)),this._scene.needsPreviousWorldMatrices&&!I&&this._instanceDataStorage.manualUpdate&&(!this._instanceDataStorage.isFrozen||this._instanceDataStorage.forceMatrixUpdates)&&!this._instanceDataStorage.previousManualUpdate&&f.updateDirectly(h.instancesData,0,_)}_renderWithInstances(e,t,s,i,n){let r=s.visibleInstances[e._id],a=r?r.length:0,o=s.parent,h=o.instancesBufferSize,l=16*(a+1)*4;for(;o.instancesBufferSize<l;)o.instancesBufferSize*=2;return o.instancesData&&h==o.instancesBufferSize||(o.instancesData=new Float32Array(o.instancesBufferSize/4)),(this._scene.needsPreviousWorldMatrices&&!o.instancesPreviousData||h!=o.instancesBufferSize)&&(o.instancesPreviousData=new Float32Array(o.instancesBufferSize/4)),this._updateInstancedBuffers(e,s,h,n,t,i),n.unbindInstanceAttributes(),this}_renderWithThinInstances(e,t,s,i){let n=this._thinInstanceDataStorage?.instancesCount??0;this.getScene()._activeIndices.addCount(e.indexCount*n,!1),i._currentDrawContext&&(i._currentDrawContext.useInstancing=!0),this._bind(e,s,t),this._draw(e,t,n),this._scene.needsPreviousWorldMatrices&&!this._thinInstanceDataStorage.previousMatrixData&&this._thinInstanceDataStorage.matrixData&&(this._thinInstanceDataStorage.previousMatrixBuffer?this._thinInstanceDataStorage.previousMatrixBuffer.updateDirectly(this._thinInstanceDataStorage.matrixData,0,n):this._thinInstanceDataStorage.previousMatrixBuffer=this._thinInstanceCreateMatrixBuffer("previousWorld",this._thinInstanceDataStorage.matrixData,!1)),i.unbindInstanceAttributes()}_processInstancedBuffers(e,t){}_processRendering(e,t,s,i,n,r,a,o){let h=this.getScene(),l=h.getEngine();if(i=this._getRenderingFillMode(i),r&&t.getRenderingMesh().hasThinInstances)return this._renderWithThinInstances(t,i,s,l),this;if(r)this._renderWithInstances(t,i,n,s,l);else{l._currentDrawContext&&(l._currentDrawContext.useInstancing=!1);let s=0;n.renderSelf[t._id]&&(a&&a(!1,e.getWorldMatrix(),o),s++,this._draw(t,i,this._instanceDataStorage.overridenInstanceCount));let r=n.visibleInstances[t._id];if(r){let e=r.length;s+=e;for(let s=0;s<e;s++){let e=r[s].getWorldMatrix();a&&a(!0,e,o),this._draw(t,i)}}h._activeIndices.addCount(t.indexCount*s,!1)}return this}_rebuild(e=!1){for(let t in this._instanceDataStorage.renderPasses){let s=this._instanceDataStorage.renderPasses[t];s.instancesBuffer&&(e&&s.instancesBuffer.dispose(),s.instancesBuffer=null)}if(this._userInstancedBuffersStorage){for(let t in this._userInstancedBuffersStorage.vertexBuffers){let s=this._userInstancedBuffersStorage.vertexBuffers[t];s&&(e&&s.dispose(),this._userInstancedBuffersStorage.vertexBuffers[t]=null)}this._userInstancedBuffersStorage.vertexArrayObjects&&(this._userInstancedBuffersStorage.vertexArrayObjects={})}this._internalMeshDataInfo._effectiveMaterial=null,super._rebuild(e)}_freeze(){if(this.subMeshes){for(let e=0;e<this.subMeshes.length;e++)this._getInstancesRenderList(e);this._internalMeshDataInfo._effectiveMaterial=null,this._instanceDataStorage.isFrozen=!0}}_unFreeze(){for(let e in this._instanceDataStorage.isFrozen=!1,this._instanceDataStorage.renderPasses)this._instanceDataStorage.renderPasses[e].previousBatch=null}renderWithRenderPassId(e,t,s,i,n=!0){let r=this._scene.getEngine(),a=r.currentRenderPassId;if(void 0!==e&&(r.currentRenderPassId=e),i)(!n||n&&i.isInFrustum(this._scene._frustumPlanes))&&this.render(i,!!t,s);else for(let e=0;e<this.subMeshes.length;e++){let i=this.subMeshes[e];(!n||n&&i.isInFrustum(this._scene._frustumPlanes))&&this.render(i,!!t,s)}return void 0!==e&&(r.currentRenderPassId=a),this}directRender(){if(!this.subMeshes)return this;for(let e of this.subMeshes)this.render(e,!1);return this}render(e,t,s){let i,n,r=this.getScene();this._internalAbstractMeshDataInfo._isActiveIntermediate?this._internalAbstractMeshDataInfo._isActiveIntermediate=!1:this._internalAbstractMeshDataInfo._isActive=!1;let a=r.activeCameras?.length??0;if((a>1&&r.activeCamera===r.activeCameras[0]||a<=1)&&this._checkOcclusionQuery()&&!this._occlusionDataStorage.forceRenderingWhenOccluded)return this;let o=this._getInstancesRenderList(e._id,!!s);if(o.mustReturn||!this._geometry||!this._geometry.getVertexBuffers()||!this._unIndexed&&!this._geometry.getIndexBuffer())return this;let h=r.getEngine(),l=0,d=null;this.ignoreCameraMaxZ&&r.activeCamera&&!r._isInIntermediateRendering()&&(l=r.activeCamera.maxZ,d=r.activeCamera,r.activeCamera.maxZ=0,r.updateTransformMatrix(!0)),this._internalMeshDataInfo._onBeforeRenderObservable&&this._internalMeshDataInfo._onBeforeRenderObservable.notifyObservers(this);let c=e.getRenderingMesh(),u=o.hardwareInstancedRendering[e._id]||c.hasThinInstances||!!this._userInstancedBuffersStorage&&!e.getMesh()._internalAbstractMeshDataInfo._actAsRegularMesh,f=this._instanceDataStorage,g=e.getMaterial();if(!g)return d&&(d.maxZ=l,r.updateTransformMatrix(!0)),this;if(f.isFrozen&&this._internalMeshDataInfo._effectiveMaterial&&this._internalMeshDataInfo._effectiveMaterial===g){if(g._storeEffectOnSubMeshes&&!e._drawWrapper?._wasPreviouslyReady||!g._storeEffectOnSubMeshes&&!g._getDrawWrapper()._wasPreviouslyReady)return d&&(d.maxZ=l,r.updateTransformMatrix(!0)),this}else{if(g._storeEffectOnSubMeshes){if(!g.isReadyForSubMesh(this,e,u))return d&&(d.maxZ=l,r.updateTransformMatrix(!0)),this}else if(!g.isReady(this,u))return d&&(d.maxZ=l,r.updateTransformMatrix(!0)),this;this._internalMeshDataInfo._effectiveMaterial=g}if(t){let e=this._internalMeshDataInfo._effectiveMaterial;if(1===e.alphaModes.length)h.setAlphaMode(e.alphaMode);else for(let t=0;t<e.alphaModes.length;t++){let s=e.alphaModes[t];h.setAlphaMode(void 0!==s?s:2,!1,t)}}i=this._internalMeshDataInfo._effectiveMaterial._storeEffectOnSubMeshes?e._drawWrapper:this._internalMeshDataInfo._effectiveMaterial._getDrawWrapper();let _=i?.effect??null;for(let t of r._beforeRenderingMeshStage)t.action(this,e,o,_);if(!i||!_)return d&&(d.maxZ=l,r.updateTransformMatrix(!0)),this;let m=s||this;if(!f.isFrozen&&(this._internalMeshDataInfo._effectiveMaterial.backFaceCulling||null!==this._internalMeshDataInfo._effectiveMaterial.sideOrientation||this._internalMeshDataInfo._effectiveMaterial.twoSidedLighting)){let e=m._getWorldMatrixDeterminant();n=this._internalMeshDataInfo._effectiveMaterial._getEffectiveOrientation(this),e<0&&(n=n===I.i.ClockWiseSideOrientation?I.i.CounterClockWiseSideOrientation:I.i.ClockWiseSideOrientation),this._internalMeshDataInfo._effectiveSideOrientation=n}else this.hasInstances&&(n=this._internalMeshDataInfo._effectiveSideOrientation);let p=this._internalMeshDataInfo._effectiveMaterial._preBind(i,this._internalMeshDataInfo._effectiveSideOrientation);this._internalMeshDataInfo._effectiveMaterial.forceDepthWrite&&h.setDepthWrite(!0);let M=this._internalMeshDataInfo._effectiveMaterial,D=M.fillMode;this._internalMeshDataInfo._onBeforeBindObservable&&this._internalMeshDataInfo._onBeforeBindObservable.notifyObservers(this),u||this._bind(e,_,D,!1);let y=m.getWorldMatrix();for(let t of(M._storeEffectOnSubMeshes?M.bindForSubMesh(y,this,e):M.bind(y,this),!M.backFaceCulling&&M.separateCullingPass&&(h.setState(!0,M.zOffset,!1,!p,M.cullBackFaces,M.stencil,M.zOffsetUnits),this._processRendering(this,e,_,D,o,u,this._onBeforeDraw,this._internalMeshDataInfo._effectiveMaterial),h.setState(!0,M.zOffset,!1,p,M.cullBackFaces,M.stencil,M.zOffsetUnits),this._internalMeshDataInfo._onBetweenPassObservable&&this._internalMeshDataInfo._onBetweenPassObservable.notifyObservers(e)),this._processRendering(this,e,_,D,o,u,this._onBeforeDraw,this._internalMeshDataInfo._effectiveMaterial),this._internalMeshDataInfo._effectiveMaterial.unbind(),r._afterRenderingMeshStage))t.action(this,e,o,_);return this._internalMeshDataInfo._onAfterRenderObservable&&this._internalMeshDataInfo._onAfterRenderObservable.notifyObservers(this),d&&(d.maxZ=l,r.updateTransformMatrix(!0)),2!==r.performancePriority||f.isFrozen||this._freeze(),this}cleanMatrixWeights(){this.isVerticesDataPresent(u.R.MatricesWeightsKind)&&(this.isVerticesDataPresent(u.R.MatricesWeightsExtraKind)?this._normalizeSkinWeightsAndExtra():this._normalizeSkinFourWeights())}_normalizeSkinFourWeights(){let e=this.getVerticesData(u.R.MatricesWeightsKind),t=e.length;for(let s=0;s<t;s+=4){let t=e[s]+e[s+1]+e[s+2]+e[s+3];if(0===t)e[s]=1;else{let i=1/t;e[s]*=i,e[s+1]*=i,e[s+2]*=i,e[s+3]*=i}}this.setVerticesData(u.R.MatricesWeightsKind,e)}_normalizeSkinWeightsAndExtra(){let e=this.getVerticesData(u.R.MatricesWeightsExtraKind),t=this.getVerticesData(u.R.MatricesWeightsKind),s=t.length;for(let i=0;i<s;i+=4){let s=t[i]+t[i+1]+t[i+2]+t[i+3];if(0===(s+=e[i]+e[i+1]+e[i+2]+e[i+3]))t[i]=1;else{let n=1/s;t[i]*=n,t[i+1]*=n,t[i+2]*=n,t[i+3]*=n,e[i]*=n,e[i+1]*=n,e[i+2]*=n,e[i+3]*=n}}this.setVerticesData(u.R.MatricesWeightsKind,t),this.setVerticesData(u.R.MatricesWeightsKind,e)}validateSkinning(){let e=this.getVerticesData(u.R.MatricesWeightsExtraKind),t=this.getVerticesData(u.R.MatricesWeightsKind);if(null===t||null==this.skeleton)return{skinned:!1,valid:!0,report:"not skinned"};let s=t.length,i=0,n=0,r=0,a=0,o=null===e?4:8,h=[];for(let e=0;e<=o;e++)h[e]=0;for(let l=0;l<s;l+=4){let s=t[l],d=s,c=+(0!==d);for(let n=1;n<o;n++){let r=n<4?t[l+n]:e[l+n-4];r>s&&i++,0!==r&&c++,d+=r,s=r}if(h[c]++,c>r&&(r=c),0===d)n++;else{let s=1/d,i=0;for(let n=0;n<o;n++)n<4?i+=Math.abs(t[l+n]-t[l+n]*s):i+=Math.abs(e[l+n-4]-e[l+n-4]*s);i>.001&&a++}}let l=this.skeleton.bones.length,d=this.getVerticesData(u.R.MatricesIndicesKind),c=this.getVerticesData(u.R.MatricesIndicesExtraKind),f=0;for(let e=0;e<s;e+=4)for(let t=0;t<o;t++){let s=t<4?d[e+t]:c[e+t-4];(s>=l||s<0)&&f++}let g="Number of Weights = "+s/4+"\nMaximum influences = "+r+"\nMissing Weights = "+n+"\nNot Sorted = "+i+"\nNot Normalized = "+a+"\nWeightCounts = ["+h+"]\nNumber of bones = "+l+"\nBad Bone Indices = "+f;return{skinned:!0,valid:0===n&&0===a&&0===f,report:g}}_checkDelayState(){let e=this.getScene();return this._geometry?this._geometry.load(e):4===this.delayLoadState&&(this.delayLoadState=2,this._queueLoad(e)),this}_queueLoad(e){e.addPendingData(this);let t=-1!==this.delayLoadingFile.indexOf(".babylonbinarymeshdata");return n.S0.LoadFile(this.delayLoadingFile,t=>{for(let e of(t instanceof ArrayBuffer?this._delayLoadingFunction(t,this):this._delayLoadingFunction(JSON.parse(t),this),this.instances))e.refreshBoundingInfo(),e._syncSubMeshes();this.delayLoadState=1,e.removePendingData(this)},()=>{},e.offlineProvider,t),this}isInFrustum(e){return 2!==this.delayLoadState&&!!super.isInFrustum(e)&&(this._checkDelayState(),!0)}setMaterialById(e){let t,s=this.getScene().materials;for(t=s.length-1;t>-1;t--)if(s[t].id===e)return this.material=s[t],this;let i=this.getScene().multiMaterials;for(t=i.length-1;t>-1;t--)if(i[t].id===e){this.material=i[t];break}return this}getAnimatables(){let e=[];return this.material&&e.push(this.material),this.skeleton&&e.push(this.skeleton),e}bakeTransformIntoVertices(e){let t;if(!this.isVerticesDataPresent(u.R.PositionKind))return this;let s=this.subMeshes.splice(0);this._resetPointsArrayCache();let i=this.getVerticesData(u.R.PositionKind),n=l.Pq.Zero();for(t=0;t<i.length;t+=3)l.Pq.TransformCoordinatesFromFloatsToRef(i[t],i[t+1],i[t+2],e,n).toArray(i,t);if(this.setVerticesData(u.R.PositionKind,i,this.getVertexBuffer(u.R.PositionKind).isUpdatable()),this.isVerticesDataPresent(u.R.NormalKind)){for(t=0,i=this.getVerticesData(u.R.NormalKind);t<i.length;t+=3)l.Pq.TransformNormalFromFloatsToRef(i[t],i[t+1],i[t+2],e,n).normalize().toArray(i,t);this.setVerticesData(u.R.NormalKind,i,this.getVertexBuffer(u.R.NormalKind).isUpdatable())}if(this.isVerticesDataPresent(u.R.TangentKind)){for(t=0,i=this.getVerticesData(u.R.TangentKind);t<i.length;t+=4)l.Pq.TransformNormalFromFloatsToRef(i[t],i[t+1],i[t+2],e,n).normalize().toArray(i,t);this.setVerticesData(u.R.TangentKind,i,this.getVertexBuffer(u.R.TangentKind).isUpdatable())}return 0>e.determinant()&&this.flipFaces(),this.releaseSubMeshes(),this.subMeshes=s,this}bakeCurrentTransformIntoVertices(e=!0,t=!1){return t&&this.makeGeometryUnique(),this.bakeTransformIntoVertices(this.computeWorldMatrix(!0)),this.resetLocalMatrix(e),this}get _positions(){return this._internalAbstractMeshDataInfo._positions||this._geometry&&this._geometry._positions||null}_resetPointsArrayCache(){return this._geometry&&this._geometry._resetPointsArrayCache(),this}_generatePointsArray(){return!!this._geometry&&this._geometry._generatePointsArray()}clone(e="",t=null,s,i=!0){return t&&void 0===t._addToSceneRootNodes?(V.source=this,V.doNotCloneChildren=t.doNotCloneChildren,V.clonePhysicsImpostor=t.clonePhysicsImpostor,V.cloneThinInstances=t.cloneThinInstances,new w(e,this.getScene(),V)):new w(e,this.getScene(),t,this,s,i)}dispose(e,t=!1){this.morphTargetManager=null,this._geometry&&this._geometry.releaseForMesh(this,!0);let s=this._internalMeshDataInfo;if(s._onBeforeDrawObservable&&s._onBeforeDrawObservable.clear(),s._onBeforeBindObservable&&s._onBeforeBindObservable.clear(),s._onBeforeRenderObservable&&s._onBeforeRenderObservable.clear(),s._onAfterRenderObservable&&s._onAfterRenderObservable.clear(),s._onBetweenPassObservable&&s._onBetweenPassObservable.clear(),this._scene.useClonedMeshMap){if(s.meshMap)for(let e in s.meshMap){let t=s.meshMap[e];t&&(t._internalMeshDataInfo._source=null,s.meshMap[e]=void 0)}s._source&&s._source._internalMeshDataInfo.meshMap&&(s._source._internalMeshDataInfo.meshMap[this.uniqueId]=void 0)}else for(let e of this.getScene().meshes)e._internalMeshDataInfo&&e._internalMeshDataInfo._source&&e._internalMeshDataInfo._source===this&&(e._internalMeshDataInfo._source=null);s._source=null,this._disposeInstanceSpecificData(),this._disposeThinInstanceSpecificData(),this._internalMeshDataInfo._checkReadinessObserver&&this._scene.onBeforeRenderObservable.remove(this._internalMeshDataInfo._checkReadinessObserver),super.dispose(e,t)}_disposeInstanceSpecificData(){}_disposeThinInstanceSpecificData(){}_invalidateInstanceVertexArrayObject(){}applyDisplacementMap(e,t,s,i,r,a,o=!1,h){let l=this.getScene(),d=e=>{let n=e.width,h=e.height,l=this.getEngine().createCanvas(n,h).getContext("2d");l.drawImage(e,0,0);let d=l.getImageData(0,0,n,h).data;this.applyDisplacementMapFromBuffer(d,n,h,t,s,r,a,o),i&&i(this)};return n.S0.LoadImage(e,d,h||(()=>{}),l.offlineProvider),this}applyDisplacementMapFromBuffer(e,t,s,i,n,r,a,o=!1){if(!this.isVerticesDataPresent(u.R.PositionKind)||!this.isVerticesDataPresent(u.R.NormalKind)||!this.isVerticesDataPresent(u.R.UVKind))return y.V.Warn("Cannot call applyDisplacementMap: Given mesh is not complete. Position, Normal or UV are missing"),this;let h=this.getVerticesData(u.R.PositionKind,!0,!0),d=this.getVerticesData(u.R.NormalKind),c=this.getVerticesData(u.R.UVKind),g=l.Pq.Zero(),_=l.Pq.Zero(),m=l.I9.Zero();r=r||l.I9.Zero(),a=a||new l.I9(1,1);for(let o=0;o<h.length;o+=3){l.Pq.FromArrayToRef(h,o,g),l.Pq.FromArrayToRef(d,o,_),l.I9.FromArrayToRef(c,o/3*2,m);let u=((Math.abs(m.x*a.x+r.x%1)*(t-1)%t|0)+(Math.abs(m.y*a.y+r.y%1)*(s-1)%s|0)*t)*4,f=e[u]/255,I=.3*f+.59*(e[u+1]/255)+.11*(e[u+2]/255);_.normalize(),_.scaleInPlace(i+(n-i)*I),(g=g.add(_)).toArray(h,o)}return f.P.ComputeNormals(h,this.getIndices(),d),o?(this.setVerticesData(u.R.PositionKind,h),this.setVerticesData(u.R.NormalKind,d),this.setVerticesData(u.R.UVKind,c)):(this.updateVerticesData(u.R.PositionKind,h),this.updateVerticesData(u.R.NormalKind,d)),this}_getFlattenedNormals(e,t){let s=new Float32Array(3*e.length),i=0,n=this.sideOrientation===+!!this._scene.useRightHandedSystem;for(let r=0;r<e.length;r+=3){let a=l.Pq.FromArray(t,3*e[r]),o=l.Pq.FromArray(t,3*e[r+1]),h=l.Pq.FromArray(t,3*e[r+2]),d=a.subtract(o),c=h.subtract(o),u=l.Pq.Normalize(l.Pq.Cross(d,c));n&&u.scaleInPlace(-1);for(let e=0;e<3;e++)s[i++]=u.x,s[i++]=u.y,s[i++]=u.z}return s}_convertToUnIndexedMesh(e=!1){let t=this.getVerticesDataKinds().filter(e=>!this.getVertexBuffer(e)?.getIsInstanced()),s=this.getIndices(),i={},n=(e,t)=>{let i=new Float32Array(s.length*t),n=0;for(let r=0;r<s.length;r++)for(let a=0;a<t;a++)i[n++]=e[s[r]*t+a];return i},r=this.getBoundingInfo(),a=this.geometry?this.subMeshes.slice(0):[];for(let e of t)i[e]=this.getVerticesData(e);for(let r of t){let t=this.getVertexBuffer(r),a=t.getSize();if(e&&r===u.R.NormalKind){let e=this._getFlattenedNormals(s,i[u.R.PositionKind]);this.setVerticesData(u.R.NormalKind,e,t.isUpdatable(),a)}else this.setVerticesData(r,n(i[r],a),t.isUpdatable(),a)}if(this.morphTargetManager){for(let t=0;t<this.morphTargetManager.numTargets;t++){let i=this.morphTargetManager.getTarget(t),r=i.getPositions();i.setPositions(n(r,3));let a=i.getNormals();a&&i.setNormals(e?this._getFlattenedNormals(s,r):n(a,3));let o=i.getTangents();o&&i.setTangents(n(o,3));let h=i.getUVs();h&&i.setUVs(n(h,2));let l=i.getColors();l&&i.setColors(n(l,4))}this.morphTargetManager.synchronize()}for(let e=0;e<s.length;e++)s[e]=e;for(let e of(this.setIndices(s),this._unIndexed=!0,this.releaseSubMeshes(),a)){let t=e.getBoundingInfo();m.K.AddToMesh(e.materialIndex,e.indexStart,e.indexCount,e.indexStart,e.indexCount,this).setBoundingInfo(t)}return this.setBoundingInfo(r),this.synchronizeInstances(),this}convertToFlatShadedMesh(){return this._convertToUnIndexedMesh(!0)}convertToUnIndexedMesh(){return this._convertToUnIndexedMesh()}flipFaces(e=!1){let t,s=f.P.ExtractFromMesh(this);if(e&&this.isVerticesDataPresent(u.R.NormalKind)&&s.normals){for(t=0;t<s.normals.length;t++)s.normals[t]*=-1;this.setVerticesData(u.R.NormalKind,s.normals,this.isVertexBufferUpdatable(u.R.NormalKind))}if(s.indices){let e;for(t=0;t<s.indices.length;t+=3)e=s.indices[t+1],s.indices[t+1]=s.indices[t+2],s.indices[t+2]=e;this.setIndices(s.indices,null,this.isVertexBufferUpdatable(u.R.PositionKind),!0)}return this}increaseVertices(e=1){let t=f.P.ExtractFromMesh(this),s=t.indices&&!Array.isArray(t.indices)&&Array.from?Array.from(t.indices):t.indices,i=t.positions&&!Array.isArray(t.positions)&&Array.from?Array.from(t.positions):t.positions,n=t.uvs&&!Array.isArray(t.uvs)&&Array.from?Array.from(t.uvs):t.uvs,r=t.normals&&!Array.isArray(t.normals)&&Array.from?Array.from(t.normals):t.normals;if(s&&i){let a,o,h,d,c;t.indices=s,t.positions=i,n&&(t.uvs=n),r&&(t.normals=r);let f=e+1,g=[];for(let e=0;e<f+1;e++)g[e]=[];let _=new l.Pq(0,0,0),m=new l.Pq(0,0,0),I=new l.I9(0,0),p=[],M=[],D=[],y=i.length;n&&(d=n.length),r&&(c=r.length);for(let e=0;e<s.length;e+=3){M[0]=s[e],M[1]=s[e+1],M[2]=s[e+2];for(let e=0;e<3;e++)if(a=M[e],o=M[(e+1)%3],void 0===D[a]&&void 0===D[o]?(D[a]=[],D[o]=[]):(void 0===D[a]&&(D[a]=[]),void 0===D[o]&&(D[o]=[])),void 0===D[a][o]&&void 0===D[o][a]){D[a][o]=[],_.x=(i[3*o]-i[3*a])/f,_.y=(i[3*o+1]-i[3*a+1])/f,_.z=(i[3*o+2]-i[3*a+2])/f,r&&(m.x=(r[3*o]-r[3*a])/f,m.y=(r[3*o+1]-r[3*a+1])/f,m.z=(r[3*o+2]-r[3*a+2])/f),n&&(I.x=(n[2*o]-n[2*a])/f,I.y=(n[2*o+1]-n[2*a+1])/f),D[a][o].push(a);for(let e=1;e<f;e++)D[a][o].push(i.length/3),i[y++]=i[3*a]+e*_.x,i[y++]=i[3*a+1]+e*_.y,i[y++]=i[3*a+2]+e*_.z,r&&(r[c++]=r[3*a]+e*m.x,r[c++]=r[3*a+1]+e*m.y,r[c++]=r[3*a+2]+e*m.z),n&&(n[d++]=n[2*a]+e*I.x,n[d++]=n[2*a+1]+e*I.y);D[a][o].push(o),D[o][a]=[],h=D[a][o].length;for(let e=0;e<h;e++)D[o][a][e]=D[a][o][h-1-e]}g[0][0]=s[e],g[1][0]=D[s[e]][s[e+1]][1],g[1][1]=D[s[e]][s[e+2]][1];for(let t=2;t<f;t++){g[t][0]=D[s[e]][s[e+1]][t],g[t][t]=D[s[e]][s[e+2]][t],_.x=(i[3*g[t][t]]-i[3*g[t][0]])/t,_.y=(i[3*g[t][t]+1]-i[3*g[t][0]+1])/t,_.z=(i[3*g[t][t]+2]-i[3*g[t][0]+2])/t,r&&(m.x=(r[3*g[t][t]]-r[3*g[t][0]])/t,m.y=(r[3*g[t][t]+1]-r[3*g[t][0]+1])/t,m.z=(r[3*g[t][t]+2]-r[3*g[t][0]+2])/t),n&&(I.x=(n[2*g[t][t]]-n[2*g[t][0]])/t,I.y=(n[2*g[t][t]+1]-n[2*g[t][0]+1])/t);for(let e=1;e<t;e++)g[t][e]=i.length/3,i[y++]=i[3*g[t][0]]+e*_.x,i[y++]=i[3*g[t][0]+1]+e*_.y,i[y++]=i[3*g[t][0]+2]+e*_.z,r&&(r[c++]=r[3*g[t][0]]+e*m.x,r[c++]=r[3*g[t][0]+1]+e*m.y,r[c++]=r[3*g[t][0]+2]+e*m.z),n&&(n[d++]=n[2*g[t][0]]+e*I.x,n[d++]=n[2*g[t][0]+1]+e*I.y)}g[f]=D[s[e+1]][s[e+2]],p.push(g[0][0],g[1][0],g[1][1]);for(let e=1;e<f;e++){let t;for(t=0;t<e;t++)p.push(g[e][t],g[e+1][t],g[e+1][t+1]),p.push(g[e][t],g[e+1][t+1],g[e][t+1]);p.push(g[e][t],g[e+1][t],g[e+1][t+1])}}t.indices=p,t.applyToMesh(this,this.isVertexBufferUpdatable(u.R.PositionKind))}else y.V.Warn("Couldn't increase number of vertices : VertexData must contain at least indices and positions")}forceSharedVertices(){let e=f.P.ExtractFromMesh(this),t=e.uvs,s=e.indices,i=e.positions,n=e.colors,r=e.matricesIndices,a=e.matricesWeights,o=e.matricesIndicesExtra,h=e.matricesWeightsExtra;if(void 0===s||void 0===i||null===s||null===i)y.V.Warn("VertexData contains empty entries");else{let l,d,c=[],g=[],_=[],m=[],I=[],p=[],M=[],D=[],y=[],v=0,b={};for(let e=0;e<s.length;e+=3){d=[s[e],s[e+1],s[e+2]],y=[];for(let e=0;e<3;e++){y[e]="";for(let t=0;t<3;t++)1e-8>Math.abs(i[3*d[e]+t])&&(i[3*d[e]+t]=0),y[e]+=i[3*d[e]+t]+"|"}if(y[0]!=y[1]&&y[0]!=y[2]&&y[1]!=y[2])for(let e=0;e<3;e++){if(void 0===(l=b[y[e]])){b[y[e]]=v,l=v++;for(let t=0;t<3;t++)c.push(i[3*d[e]+t]);if(null!=n)for(let t=0;t<4;t++)m.push(n[4*d[e]+t]);if(null!=t)for(let s=0;s<2;s++)_.push(t[2*d[e]+s]);if(null!=r)for(let t=0;t<4;t++)I.push(r[4*d[e]+t]);if(null!=a)for(let t=0;t<4;t++)p.push(a[4*d[e]+t]);if(null!=o)for(let t=0;t<4;t++)M.push(o[4*d[e]+t]);if(null!=h)for(let t=0;t<4;t++)D.push(h[4*d[e]+t])}g.push(l)}}let S=[];f.P.ComputeNormals(c,g,S),e.positions=c,e.indices=g,e.normals=S,null!=t&&(e.uvs=_),null!=n&&(e.colors=m),null!=r&&(e.matricesIndices=I),null!=a&&(e.matricesWeights=p),null!=o&&(e.matricesIndicesExtra=M),null!=a&&(e.matricesWeightsExtra=D),e.applyToMesh(this,this.isVertexBufferUpdatable(u.R.PositionKind))}}static _instancedMeshFactory(e,t){throw(0,b.n)("InstancedMesh")}static _PhysicsImpostorParser(e,t,s){throw(0,b.n)("PhysicsImpostor")}createInstance(e){let t=w._instancedMeshFactory(e,this);return t.parent=this.parent,t}synchronizeInstances(){for(let e=0;e<this.instances.length;e++)this.instances[e]._syncSubMeshes();return this}optimizeIndices(e){let t=this.getIndices(),s=this.getVerticesData(u.R.PositionKind);if(!s||!t)return this;let i=[];for(let e=0;e<s.length;e+=3)i.push(l.Pq.FromArray(s,e));let r=[];return n.LV.SyncAsyncForLoop(i.length,40,e=>{let t=i.length-1-e,s=i[t];for(let e=0;e<t;++e){let n=i[e];if(s.equals(n)){r[t]=e;break}}},()=>{for(let e=0;e<t.length;++e)t[e]=r[t[e]]||t[e];let s=this.subMeshes.slice(0);this.setIndices(t),this.subMeshes=s,e&&e(this)}),this}serialize(e={}){e.name=this.name,e.id=this.id,e.uniqueId=this.uniqueId,e.type=this.getClassName(),a.Y&&a.Y.HasTags(this)&&(e.tags=a.Y.GetTags(this)),e.position=this.position.asArray(),this.rotationQuaternion?e.rotationQuaternion=this.rotationQuaternion.asArray():this.rotation&&(e.rotation=this.rotation.asArray()),e.scaling=this.scaling.asArray(),this._postMultiplyPivotMatrix?e.pivotMatrix=this.getPivotMatrix().asArray():e.localMatrix=this.getPivotMatrix().asArray(),e.isEnabled=this.isEnabled(!1),e.isVisible=this.isVisible,e.infiniteDistance=this.infiniteDistance,e.pickable=this.isPickable,e.receiveShadows=this.receiveShadows,e.billboardMode=this.billboardMode,e.visibility=this.visibility,e.alwaysSelectAsActiveMesh=this.alwaysSelectAsActiveMesh,e.checkCollisions=this.checkCollisions,e.ellipsoid=this.ellipsoid.asArray(),e.ellipsoidOffset=this.ellipsoidOffset.asArray(),e.doNotSyncBoundingInfo=this.doNotSyncBoundingInfo,e.isBlocker=this.isBlocker,e.sideOrientation=this.sideOrientation,this.parent&&this.parent._serializeAsParent(e),e.isUnIndexed=this.isUnIndexed;let t=this._geometry;if(t&&this.subMeshes){e.geometryUniqueId=t.uniqueId,e.geometryId=t.id,e.subMeshes=[];for(let t=0;t<this.subMeshes.length;t++){let s=this.subMeshes[t];e.subMeshes.push({materialIndex:s.materialIndex,verticesStart:s.verticesStart,verticesCount:s.verticesCount,indexStart:s.indexStart,indexCount:s.indexCount})}}if(this.material?this.material.doNotSerialize||(e.materialUniqueId=this.material.uniqueId,e.materialId=this.material.id):(this.material=null,e.materialUniqueId=this._scene.defaultMaterial.uniqueId,e.materialId=this._scene.defaultMaterial.id),this.morphTargetManager&&(e.morphTargetManagerId=this.morphTargetManager.uniqueId),this.skeleton&&(e.skeletonId=this.skeleton.id,e.numBoneInfluencers=this.numBoneInfluencers),this.getScene()._getComponent(S.v.NAME_PHYSICSENGINE)){let t=this.getPhysicsImpostor();t&&(e.physicsMass=t.getParam("mass"),e.physicsFriction=t.getParam("friction"),e.physicsRestitution=t.getParam("mass"),e.physicsImpostor=t.type)}this.metadata&&(e.metadata=this.metadata),e.instances=[];for(let t=0;t<this.instances.length;t++){let s=this.instances[t];if(s.doNotSerialize)continue;let i={name:s.name,id:s.id,isEnabled:s.isEnabled(!1),isVisible:s.isVisible,isPickable:s.isPickable,checkCollisions:s.checkCollisions,position:s.position.asArray(),scaling:s.scaling.asArray()};if(s.parent&&s.parent._serializeAsParent(i),s.rotationQuaternion?i.rotationQuaternion=s.rotationQuaternion.asArray():s.rotation&&(i.rotation=s.rotation.asArray()),this.getScene()._getComponent(S.v.NAME_PHYSICSENGINE)){let e=s.getPhysicsImpostor();e&&(i.physicsMass=e.getParam("mass"),i.physicsFriction=e.getParam("friction"),i.physicsRestitution=e.getParam("mass"),i.physicsImpostor=e.type)}s.metadata&&(i.metadata=s.metadata),s.actionManager&&(i.actions=s.actionManager.serialize(s.name)),e.instances.push(i),D.p.AppendSerializedAnimations(s,i),i.ranges=s.serializeAnimationRanges()}if(this._thinInstanceDataStorage.instancesCount&&this._thinInstanceDataStorage.matrixData&&(e.thinInstances={instancesCount:this._thinInstanceDataStorage.instancesCount,matrixData:Array.from(this._thinInstanceDataStorage.matrixData),matrixBufferSize:this._thinInstanceDataStorage.matrixBufferSize,enablePicking:this.thinInstanceEnablePicking},this._userThinInstanceBuffersStorage)){let t={data:{},sizes:{},strides:{}};for(let e in this._userThinInstanceBuffersStorage.data)t.data[e]=Array.from(this._userThinInstanceBuffersStorage.data[e]),t.sizes[e]=this._userThinInstanceBuffersStorage.sizes[e],t.strides[e]=this._userThinInstanceBuffersStorage.strides[e];e.thinInstances.userThinInstance=t}return D.p.AppendSerializedAnimations(this,e),e.ranges=this.serializeAnimationRanges(),e.layerMask=this.layerMask,e.alphaIndex=this.alphaIndex,e.hasVertexAlpha=this.hasVertexAlpha,e.overlayAlpha=this.overlayAlpha,e.overlayColor=this.overlayColor.asArray(),e.renderOverlay=this.renderOverlay,e.applyFog=this.applyFog,this.actionManager&&(e.actions=this.actionManager.serialize(this.name)),e}_syncGeometryWithMorphTargetManager(){if(!this.geometry)return;this._markSubMeshesAsAttributesDirty();let e=this._internalAbstractMeshDataInfo._morphTargetManager;if(e&&e.vertexCount){if(e.vertexCount!==this.getTotalVertices()){y.V.Error("Mesh is incompatible with morph targets. Targets and mesh must all have the same vertices count."),this.morphTargetManager=null;return}if(e.isUsingTextureForTargets)return;for(let t=0;t<e.numInfluencers;t++){let s=e.getActiveTarget(t),i=s.getPositions();if(!i)return void y.V.Error("Invalid morph target. Target must have positions.");this.geometry.setVerticesData(u.R.PositionKind+t,i,!1,3);let n=s.getNormals();n&&this.geometry.setVerticesData(u.R.NormalKind+t,n,!1,3);let r=s.getTangents();r&&this.geometry.setVerticesData(u.R.TangentKind+t,r,!1,3);let a=s.getUVs();a&&this.geometry.setVerticesData(u.R.UVKind+"_"+t,a,!1,2);let o=s.getUV2s();o&&this.geometry.setVerticesData(u.R.UV2Kind+"_"+t,o,!1,2);let h=s.getColors();h&&this.geometry.setVerticesData(u.R.ColorKind+t,h,!1,4)}}else{let e=0;for(;this.geometry.isVerticesDataPresent(u.R.PositionKind+e);)this.geometry.removeVerticesData(u.R.PositionKind+e),this.geometry.isVerticesDataPresent(u.R.NormalKind+e)&&this.geometry.removeVerticesData(u.R.NormalKind+e),this.geometry.isVerticesDataPresent(u.R.TangentKind+e)&&this.geometry.removeVerticesData(u.R.TangentKind+e),this.geometry.isVerticesDataPresent(u.R.UVKind+e)&&this.geometry.removeVerticesData(u.R.UVKind+"_"+e),this.geometry.isVerticesDataPresent(u.R.UV2Kind+e)&&this.geometry.removeVerticesData(u.R.UV2Kind+"_"+e),this.geometry.isVerticesDataPresent(u.R.ColorKind+e)&&this.geometry.removeVerticesData(u.R.ColorKind+e),e++}}static Parse(e,t,s){let i;if((i=e.type&&"LinesMesh"===e.type?w._LinesMeshParser(e,t):e.type&&"GroundMesh"===e.type?w._GroundMeshParser(e,t):e.type&&"GoldbergMesh"===e.type?w._GoldbergMeshParser(e,t):e.type&&"GreasedLineMesh"===e.type?w._GreasedLineMeshParser(e,t):e.type&&"TrailMesh"===e.type?w._TrailMeshParser(e,t):new w(e.name,t)).id=e.id,i._waitingParsedUniqueId=e.uniqueId,a.Y&&a.Y.AddTagsTo(i,e.tags),i.position=l.Pq.FromArray(e.position),void 0!==e.metadata&&(i.metadata=e.metadata),e.rotationQuaternion?i.rotationQuaternion=l.PT.FromArray(e.rotationQuaternion):e.rotation&&(i.rotation=l.Pq.FromArray(e.rotation)),i.scaling=l.Pq.FromArray(e.scaling),e.localMatrix?i.setPreTransformMatrix(l.uq.FromArray(e.localMatrix)):e.pivotMatrix&&i.setPivotMatrix(l.uq.FromArray(e.pivotMatrix)),i.setEnabled(e.isEnabled),i.isVisible=e.isVisible,i.infiniteDistance=e.infiniteDistance,i.alwaysSelectAsActiveMesh=!!e.alwaysSelectAsActiveMesh,i.showBoundingBox=e.showBoundingBox,i.showSubMeshesBoundingBox=e.showSubMeshesBoundingBox,void 0!==e.applyFog&&(i.applyFog=e.applyFog),void 0!==e.pickable&&(i.isPickable=e.pickable),void 0!==e.alphaIndex&&(i.alphaIndex=e.alphaIndex),i.receiveShadows=e.receiveShadows,void 0!==e.billboardMode&&(i.billboardMode=e.billboardMode),void 0!==e.visibility&&(i.visibility=e.visibility),i.checkCollisions=e.checkCollisions,i.doNotSyncBoundingInfo=!!e.doNotSyncBoundingInfo,e.ellipsoid&&(i.ellipsoid=l.Pq.FromArray(e.ellipsoid)),e.ellipsoidOffset&&(i.ellipsoidOffset=l.Pq.FromArray(e.ellipsoidOffset)),null!=e.overrideMaterialSideOrientation&&(i.sideOrientation=e.overrideMaterialSideOrientation),void 0!==e.sideOrientation&&(i.sideOrientation=e.sideOrientation),void 0!==e.isBlocker&&(i.isBlocker=e.isBlocker),i._shouldGenerateFlatShading=e.useFlatShading,e.freezeWorldMatrix&&(i._waitingData.freezeWorldMatrix=e.freezeWorldMatrix),void 0!==e.parentId&&(i._waitingParentId=e.parentId),void 0!==e.parentInstanceIndex&&(i._waitingParentInstanceIndex=e.parentInstanceIndex),void 0!==e.actions&&(i._waitingData.actions=e.actions),void 0!==e.overlayAlpha&&(i.overlayAlpha=e.overlayAlpha),void 0!==e.overlayColor&&(i.overlayColor=d.v9.FromArray(e.overlayColor)),void 0!==e.renderOverlay&&(i.renderOverlay=e.renderOverlay),i.isUnIndexed=!!e.isUnIndexed,i.hasVertexAlpha=e.hasVertexAlpha,e.delayLoadingFile?(i.delayLoadState=4,i.delayLoadingFile=s+e.delayLoadingFile,i.buildBoundingInfo(l.Pq.FromArray(e.boundingBoxMinimum),l.Pq.FromArray(e.boundingBoxMaximum)),e._binaryInfo&&(i._binaryInfo=e._binaryInfo),i._delayInfo=[],e.hasUVs&&i._delayInfo.push(u.R.UVKind),e.hasUVs2&&i._delayInfo.push(u.R.UV2Kind),e.hasUVs3&&i._delayInfo.push(u.R.UV3Kind),e.hasUVs4&&i._delayInfo.push(u.R.UV4Kind),e.hasUVs5&&i._delayInfo.push(u.R.UV5Kind),e.hasUVs6&&i._delayInfo.push(u.R.UV6Kind),e.hasColors&&i._delayInfo.push(u.R.ColorKind),e.hasMatricesIndices&&i._delayInfo.push(u.R.MatricesIndicesKind),e.hasMatricesWeights&&i._delayInfo.push(u.R.MatricesWeightsKind),i._delayLoadingFunction=g.V._ImportGeometry,M.B.ForceFullSceneLoadingForIncremental&&i._checkDelayState()):g.V._ImportGeometry(e,i),e.materialUniqueId?i._waitingMaterialId=e.materialUniqueId:e.materialId&&(i._waitingMaterialId=e.materialId),e.morphTargetManagerId>-1&&(i._waitingMorphTargetManagerId=e.morphTargetManagerId),void 0!==e.skeletonId&&null!==e.skeletonId&&(i.skeleton=t.getLastSkeletonById(e.skeletonId),e.numBoneInfluencers&&(i.numBoneInfluencers=e.numBoneInfluencers)),e.animations){for(let t=0;t<e.animations.length;t++){let s=e.animations[t],n=(0,v.n9)("BABYLON.Animation");n&&i.animations.push(n.Parse(s))}c.b.ParseAnimationRanges(i,e,t)}if(e.autoAnimate&&t.beginAnimation(i,e.autoAnimateFrom,e.autoAnimateTo,e.autoAnimateLoop,e.autoAnimateSpeed||1),e.layerMask&&!isNaN(e.layerMask)?i.layerMask=Math.abs(parseInt(e.layerMask)):i.layerMask=0xfffffff,e.physicsImpostor&&(i.physicsImpostor=w._PhysicsImpostorParser(t,i,e)),e.lodMeshIds&&(i._waitingData.lods={ids:e.lodMeshIds,distances:e.lodDistances?e.lodDistances:null,coverages:e.lodCoverages?e.lodCoverages:null}),e.instances)for(let s=0;s<e.instances.length;s++){let n=e.instances[s],r=i.createInstance(n.name);if(n.id&&(r.id=n.id),a.Y&&(n.tags?a.Y.AddTagsTo(r,n.tags):a.Y.AddTagsTo(r,e.tags)),r.position=l.Pq.FromArray(n.position),void 0!==n.metadata&&(r.metadata=n.metadata),void 0!==n.parentId&&(r._waitingParentId=n.parentId),void 0!==n.parentInstanceIndex&&(r._waitingParentInstanceIndex=n.parentInstanceIndex),void 0!==n.isEnabled&&null!==n.isEnabled&&r.setEnabled(n.isEnabled),void 0!==n.isVisible&&null!==n.isVisible&&(r.isVisible=n.isVisible),void 0!==n.isPickable&&null!==n.isPickable&&(r.isPickable=n.isPickable),n.rotationQuaternion?r.rotationQuaternion=l.PT.FromArray(n.rotationQuaternion):n.rotation&&(r.rotation=l.Pq.FromArray(n.rotation)),r.scaling=l.Pq.FromArray(n.scaling),void 0!=n.checkCollisions&&null!=n.checkCollisions&&(r.checkCollisions=n.checkCollisions),void 0!=n.pickable&&null!=n.pickable&&(r.isPickable=n.pickable),void 0!=n.showBoundingBox&&null!=n.showBoundingBox&&(r.showBoundingBox=n.showBoundingBox),void 0!=n.showSubMeshesBoundingBox&&null!=n.showSubMeshesBoundingBox&&(r.showSubMeshesBoundingBox=n.showSubMeshesBoundingBox),void 0!=n.alphaIndex&&null!=n.showSubMeshesBoundingBox&&(r.alphaIndex=n.alphaIndex),n.physicsImpostor&&(r.physicsImpostor=w._PhysicsImpostorParser(t,r,n)),void 0!==n.actions&&(r._waitingData.actions=n.actions),n.animations){for(let e=0;e<n.animations.length;e++){let t=n.animations[e],s=(0,v.n9)("BABYLON.Animation");s&&r.animations.push(s.Parse(t))}c.b.ParseAnimationRanges(r,n,t),n.autoAnimate&&t.beginAnimation(r,n.autoAnimateFrom,n.autoAnimateTo,n.autoAnimateLoop,n.autoAnimateSpeed||1)}}if(e.thinInstances){let t=e.thinInstances;if(i.thinInstanceEnablePicking=!!t.enablePicking,t.matrixData?(i.thinInstanceSetBuffer("matrix",new Float32Array(t.matrixData),16,!1),i._thinInstanceDataStorage.matrixBufferSize=t.matrixBufferSize,i._thinInstanceDataStorage.instancesCount=t.instancesCount):i._thinInstanceDataStorage.matrixBufferSize=t.matrixBufferSize,e.thinInstances.userThinInstance){let t=e.thinInstances.userThinInstance;for(let e in t.data)i.thinInstanceSetBuffer(e,new Float32Array(t.data[e]),t.strides[e],!1),i._userThinInstanceBuffersStorage.sizes[e]=t.sizes[e]}}return i}setPositionsForCPUSkinning(){let e=this._internalMeshDataInfo;if(!e._sourcePositions){let t=this.getVerticesData(u.R.PositionKind);if(!t)return e._sourcePositions;e._sourcePositions=new Float32Array(t),this.isVertexBufferUpdatable(u.R.PositionKind)||this.setVerticesData(u.R.PositionKind,t,!0)}return e._sourcePositions}setNormalsForCPUSkinning(){let e=this._internalMeshDataInfo;if(!e._sourceNormals){let t=this.getVerticesData(u.R.NormalKind);if(!t)return e._sourceNormals;e._sourceNormals=new Float32Array(t),this.isVertexBufferUpdatable(u.R.NormalKind)||this.setVerticesData(u.R.NormalKind,t,!0)}return e._sourceNormals}applySkeleton(e){let t;if(!this.geometry||this.geometry._softwareSkinningFrameId==this.getScene().getFrameId()||(this.geometry._softwareSkinningFrameId=this.getScene().getFrameId(),!this.isVerticesDataPresent(u.R.PositionKind)||!this.isVerticesDataPresent(u.R.MatricesIndicesKind)||!this.isVerticesDataPresent(u.R.MatricesWeightsKind)))return this;let s=this.isVerticesDataPresent(u.R.NormalKind),i=this._internalMeshDataInfo;if(!i._sourcePositions){let e=this.subMeshes.slice();this.setPositionsForCPUSkinning(),this.subMeshes=e}s&&!i._sourceNormals&&this.setNormalsForCPUSkinning();let n=this.getVerticesData(u.R.PositionKind);if(!n)return this;n instanceof Float32Array||(n=new Float32Array(n));let r=this.getVerticesData(u.R.NormalKind);if(s){if(!r)return this;r instanceof Float32Array||(r=new Float32Array(r))}let a=this.getVerticesData(u.R.MatricesIndicesKind),o=this.getVerticesData(u.R.MatricesWeightsKind);if(!o||!a)return this;let h=this.numBoneInfluencers>4,d=h?this.getVerticesData(u.R.MatricesIndicesExtraKind):null,c=h?this.getVerticesData(u.R.MatricesWeightsExtraKind):null,f=e.getTransformMatrices(this),g=l.Pq.Zero(),_=new l.uq,m=new l.uq,I=0;for(let e=0;e<n.length;e+=3,I+=4){let u;for(t=0;t<4;t++)(u=o[I+t])>0&&(l.uq.FromFloat32ArrayToRefScaled(f,Math.floor(16*a[I+t]),u,m),_.addToSelf(m));if(h)for(t=0;t<4;t++)(u=c[I+t])>0&&(l.uq.FromFloat32ArrayToRefScaled(f,Math.floor(16*d[I+t]),u,m),_.addToSelf(m));l.Pq.TransformCoordinatesFromFloatsToRef(i._sourcePositions[e],i._sourcePositions[e+1],i._sourcePositions[e+2],_,g),g.toArray(n,e),s&&(l.Pq.TransformNormalFromFloatsToRef(i._sourceNormals[e],i._sourceNormals[e+1],i._sourceNormals[e+2],_,g),g.toArray(r,e)),_.reset()}return this.updateVerticesData(u.R.PositionKind,n),s&&this.updateVerticesData(u.R.NormalKind,r),this}static MinMax(e){let t=null,s=null;for(let i of e){let e=i.getBoundingInfo().boundingBox;t&&s?(t.minimizeInPlace(e.minimumWorld),s.maximizeInPlace(e.maximumWorld)):(t=e.minimumWorld,s=e.maximumWorld)}return t&&s?{min:t,max:s}:{min:l.Pq.Zero(),max:l.Pq.Zero()}}static Center(e){let t=e instanceof Array?w.MinMax(e):e;return l.Pq.Center(t.min,t.max)}static MergeMeshes(e,t=!0,s,i,n,r){return(0,o.V1)(w._MergeMeshesCoroutine(e,t,s,i,n,r,!1))}static async MergeMeshesAsync(e,t=!0,s,i,n,r){return await (0,o.kj)(w._MergeMeshesCoroutine(e,t,s,i,n,r,!0),(0,o.VP)())}static*_MergeMeshesCoroutine(e,t=!0,s,i,n,r,a){let o;if(0===(e=e.filter(Boolean)).length)return null;if(!s){let t=0;for(o=0;o<e.length;o++)if((t+=e[o].getTotalVertices())>=65536)return y.V.Warn("Cannot merge meshes because resulting mesh will have more than 65536 vertices. Please use allow32BitsIndices = true to use 32 bits indices"),null}r&&(n=!1);let h=[],l=[],d=[],c=e[0].sideOrientation;for(o=0;o<e.length;o++){let t=e[o];if(t.isAnInstance)return y.V.Warn("Cannot merge instance meshes."),null;if(c!==t.sideOrientation)return y.V.Warn("Cannot merge meshes with different sideOrientation values."),null;if(n&&d.push(t.getTotalIndices()),r)if(t.material){let e=t.material;if(e instanceof p.F){for(let t=0;t<e.subMaterials.length;t++)0>h.indexOf(e.subMaterials[t])&&h.push(e.subMaterials[t]);for(let s=0;s<t.subMeshes.length;s++)l.push(h.indexOf(e.subMaterials[t.subMeshes[s].materialIndex])),d.push(t.subMeshes[s].indexCount)}else{0>h.indexOf(e)&&h.push(e);for(let s=0;s<t.subMeshes.length;s++)l.push(h.indexOf(e)),d.push(t.subMeshes[s].indexCount)}}else for(let e=0;e<t.subMeshes.length;e++)l.push(0),d.push(t.subMeshes[e].indexCount)}let u=e[0],g=e=>{let t=e.computeWorldMatrix(!0);return{vertexData:f.P.ExtractFromMesh(e,!1,!1),transform:t}},{vertexData:_,transform:I}=g(u);a&&(yield);let M=Array(e.length-1);for(let t=1;t<e.length;t++)M[t-1]=g(e[t]),a&&(yield);let D=_._mergeCoroutine(I,M,s,a,!t),v=D.next();for(;!v.done;)a&&(yield),v=D.next();let b=v.value;i||(i=new w(u.name+"_merged",u.getScene()));let S=b._applyToCoroutine(i,void 0,a),B=S.next();for(;!B.done;)a&&(yield),B=S.next();if(i.checkCollisions=u.checkCollisions,i.sideOrientation=u.sideOrientation,t)for(o=0;o<e.length;o++)e[o].dispose();if(n||r){i.releaseSubMeshes(),o=0;let e=0;for(;o<d.length;)m.K.CreateFromIndices(0,e,d[o],i,void 0,!1),e+=d[o],o++;for(let e of i.subMeshes)e.refreshBoundingInfo();i.computeWorldMatrix(!0)}if(r){let e=new p.F(u.name+"_merged",u.getScene());e.subMaterials=h;for(let e=0;e<i.subMeshes.length;e++)i.subMeshes[e].materialIndex=l[e];i.material=e}else i.material=u.material;return i}addInstance(e){e._indexInSourceMeshInstanceArray=this.instances.length,this.instances.push(e)}removeInstance(e){let t=e._indexInSourceMeshInstanceArray;if(-1!=t){if(t!==this.instances.length-1){let e=this.instances[this.instances.length-1];this.instances[t]=e,e._indexInSourceMeshInstanceArray=t}e._indexInSourceMeshInstanceArray=-1,this.instances.pop()}}_shouldConvertRHS(){return this._scene.useRightHandedSystem&&this.sideOrientation===I.i.CounterClockWiseSideOrientation}_getRenderingFillMode(e){let t=this.getScene();return t.forcePointsCloud?I.i.PointFillMode:t.forceWireframe?I.i.WireFrameFillMode:this.overrideRenderingFillMode??e}setMaterialByID(e){return this.setMaterialById(e)}static CreateRibbon(e,t,s,i,n,r,a,o,h){throw Error("Import MeshBuilder to populate this function")}static CreateDisc(e,t,s,i,n,r){throw Error("Import MeshBuilder to populate this function")}static CreateBox(e,t,s,i,n){throw Error("Import MeshBuilder to populate this function")}static CreateSphere(e,t,s,i,n,r){throw Error("Import MeshBuilder to populate this function")}static CreateHemisphere(e,t,s,i){throw Error("Import MeshBuilder to populate this function")}static CreateCylinder(e,t,s,i,n,r,a,o,h){throw Error("Import MeshBuilder to populate this function")}static CreateTorus(e,t,s,i,n,r,a){throw Error("Import MeshBuilder to populate this function")}static CreateTorusKnot(e,t,s,i,n,r,a,o,h,l){throw Error("Import MeshBuilder to populate this function")}static CreateLines(e,t,s,i,n){throw Error("Import MeshBuilder to populate this function")}static CreateDashedLines(e,t,s,i,n,r,a,o){throw Error("Import MeshBuilder to populate this function")}static CreatePolygon(e,t,s,i,n,r,a){throw Error("Import MeshBuilder to populate this function")}static ExtrudePolygon(e,t,s,i,n,r,a,o){throw Error("Import MeshBuilder to populate this function")}static ExtrudeShape(e,t,s,i,n,r,a,o,h,l){throw Error("Import MeshBuilder to populate this function")}static ExtrudeShapeCustom(e,t,s,i,n,r,a,o,h,l,d,c){throw Error("Import MeshBuilder to populate this function")}static CreateLathe(e,t,s,i,n,r,a){throw Error("Import MeshBuilder to populate this function")}static CreatePlane(e,t,s,i,n){throw Error("Import MeshBuilder to populate this function")}static CreateGround(e,t,s,i,n,r){throw Error("Import MeshBuilder to populate this function")}static CreateTiledGround(e,t,s,i,n,r,a,o,h){throw Error("Import MeshBuilder to populate this function")}static CreateGroundFromHeightMap(e,t,s,i,n,r,a,o,h,l,d){throw Error("Import MeshBuilder to populate this function")}static CreateTube(e,t,s,i,n,r,a,o,h,l){throw Error("Import MeshBuilder to populate this function")}static CreatePolyhedron(e,t,s){throw Error("Import MeshBuilder to populate this function")}static CreateIcoSphere(e,t,s){throw Error("Import MeshBuilder to populate this function")}static CreateDecal(e,t,s,i,n,r){throw Error("Import MeshBuilder to populate this function")}static CreateCapsule(e,t,s){throw Error("Import MeshBuilder to populate this function")}static ExtendToGoldberg(e){throw Error("Import MeshBuilder to populate this function")}}w.FRONTSIDE=f.P.FRONTSIDE,w.BACKSIDE=f.P.BACKSIDE,w.DOUBLESIDE=f.P.DOUBLESIDE,w.DEFAULTSIDE=f.P.DEFAULTSIDE,w.NO_CAP=0,w.CAP_START=1,w.CAP_END=2,w.CAP_ALL=3,w.NO_FLIP=0,w.FLIP_TILE=1,w.ROTATE_TILE=2,w.FLIP_ROW=3,w.ROTATE_ROW=4,w.FLIP_N_ROTATE_TILE=5,w.FLIP_N_ROTATE_ROW=6,w.CENTER=0,w.LEFT=1,w.RIGHT=2,w.TOP=3,w.BOTTOM=4,w.INSTANCEDMESH_SORT_TRANSPARENT=!1,w._GroundMeshParser=(e,t)=>{throw(0,b.n)("GroundMesh")},w._GoldbergMeshParser=(e,t)=>{throw(0,b.n)("GoldbergMesh")},w._LinesMeshParser=(e,t)=>{throw(0,b.n)("LinesMesh")},w._GreasedLineMeshParser=(e,t)=>{throw(0,b.n)("GreasedLineMesh")},w._GreasedLineRibbonMeshParser=(e,t)=>{throw(0,b.n)("GreasedLineRibbonMesh")},w._TrailMeshParser=(e,t)=>{throw(0,b.n)("TrailMesh")},(0,v.Y5)("BABYLON.Mesh",w)}}]);