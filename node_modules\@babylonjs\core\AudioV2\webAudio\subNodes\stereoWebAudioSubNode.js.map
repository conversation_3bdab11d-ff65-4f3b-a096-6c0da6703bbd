{"version": 3, "file": "stereoWebAudioSubNode.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/webAudio/subNodes/stereoWebAudioSubNode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,iDAAiD,CAAC;AACtF,OAAO,EAAE,2BAA2B,EAAE,MAAM,0CAA0C,CAAC;AAIvF,gBAAgB;AAChB,4DAA4D;AAC5D,MAAM,CAAC,KAAK,UAAU,8BAA8B,CAAC,MAAuB;IACxE,OAAO,IAAI,sBAAsB,CAAC,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED,gBAAgB;AAChB,MAAM,OAAO,sBAAuB,SAAQ,mBAAmB;IAS3D,gBAAgB;IAChB,YAAmB,MAAuB;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEd,IAAI,CAAC,IAAI,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEvD,IAAI,CAAC,IAAI,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvE,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAED,gBAAgB;IAChB,IAAW,GAAG;QACV,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IACjC,CAAC;IAED,gBAAgB;IAChB,IAAW,GAAG,CAAC,KAAa;QACxB,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAEkB,QAAQ,CAAC,IAAqB;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["import { _StereoAudioSubNode } from \"../../abstractAudio/subNodes/stereoAudioSubNode\";\nimport { _WebAudioParameterComponent } from \"../components/webAudioParameterComponent\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\nimport type { IWebAudioInNode } from \"../webAudioNode\";\n\n/** @internal */\n// eslint-disable-next-line @typescript-eslint/require-await\nexport async function _CreateStereoAudioSubNodeAsync(engine: _WebAudioEngine): Promise<_StereoAudioSubNode> {\n    return new _StereoWebAudioSubNode(engine);\n}\n\n/** @internal */\nexport class _StereoWebAudioSubNode extends _StereoAudioSubNode {\n    private _pan: _WebAudioParameterComponent;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public readonly node: StereoPannerNode;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n\n        this.node = new StereoPannerNode(engine._audioContext);\n\n        this._pan = new _WebAudioParameterComponent(engine, this.node.pan);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._pan.dispose();\n    }\n\n    /** @internal */\n    public get pan(): number {\n        return this._pan.targetValue;\n    }\n\n    /** @internal */\n    public set pan(value: number) {\n        this._pan.targetValue = value;\n    }\n\n    /** @internal */\n    public get _inNode(): AudioNode {\n        return this.node;\n    }\n\n    /** @internal */\n    public get _outNode(): AudioNode {\n        return this.node;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_StereoWebAudioSubNode\";\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this.node.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this.node.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n}\n"]}