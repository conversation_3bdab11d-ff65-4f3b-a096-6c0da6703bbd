{"version": 3, "file": "webgpuOcclusionQuery.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuOcclusionQuery.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,gBAAgB;AAChB,MAAM,OAAO,oBAAoB;IAc7B,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;IACvE,CAAC;IAEM,aAAa,CAAC,KAAa;QAC9B,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC5G,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,4BAA4B,EAAE,CAAC,oBAAqB,CAAC,iBAAiB,KAAK,SAAS,CAAC;QAEnH,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACrD,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,YAAY,MAAoB,EAAE,MAAiB,EAAE,aAAkC,EAAE,UAAU,GAAG,EAAE,EAAE,cAAc,GAAG,GAAG;QA5BtH,sBAAiB,GAAa,EAAE,CAAC;QAGjC,0BAAqB,GAAG,CAAC,CAAC,CAAC;QAC3B,kBAAa,GAAa,EAAE,CAAC;QAyBjC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QAEtC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAEM,WAAW;QACd,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/B,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;QAEhC,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,WAAW,CAAC,KAAa;QAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;IAClE,CAAC;IAEM,sBAAsB,CAAC,KAAa;QACvC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IACjE,CAAC;IAEM,cAAc,CAAC,KAAa;QAC/B,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;IAEO,oBAAoB;QACxB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACrE,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACjD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YAC7C,mFAAmF;YACnF,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBACzE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,mBAAmB,CAAC,UAAmB;QAC3C,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC;QAEhD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,oBAAoB,IAAI,UAAU,CAAC;QACxC,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,CAC/B,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,oBAAoB,yDAEzB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,cAAc,EACnB,KAAK,EACL,gCAAgC,GAAG,IAAI,CAAC,oBAAoB,CAC/D,CAAC;QAEF,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;IACtD,CAAC;IAEO,qBAAqB;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAChC,IAAI,QAAQ,EAAE,CAAC;YACX,6FAA6F;YAC7F,UAAU,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;IACtC,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\nimport type { WebGPUBufferManager } from \"./webgpuBufferManager\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport { WebGPUQuerySet } from \"./webgpuQuerySet\";\r\n\r\n/** @internal */\r\nexport class WebGPUOcclusionQuery {\r\n    private _engine: WebGPUEngine;\r\n    private _device: GPUDevice;\r\n    private _bufferManager: WebGPUBufferManager;\r\n\r\n    private _currentTotalIndices: number;\r\n    private _countIncrement: number;\r\n    private _querySet: WebGPUQuerySet;\r\n    private _availableIndices: number[] = [];\r\n    private _lastBuffer: Nullable<BigUint64Array>;\r\n    private _frameLastBuffer: number;\r\n    private _frameQuerySetIsDirty = -1;\r\n    private _queryFrameId: number[] = [];\r\n\r\n    public get querySet(): GPUQuerySet {\r\n        return this._querySet.querySet;\r\n    }\r\n\r\n    public get hasQueries(): boolean {\r\n        return this._currentTotalIndices !== this._availableIndices.length;\r\n    }\r\n\r\n    public canBeginQuery(index: number): boolean {\r\n        if (this._frameQuerySetIsDirty === this._engine.frameId || this._queryFrameId[index] === this._engine.frameId) {\r\n            return false;\r\n        }\r\n\r\n        const canBegin = this._engine._getCurrentRenderPassWrapper().renderPassDescriptor!.occlusionQuerySet !== undefined;\r\n\r\n        if (canBegin) {\r\n            this._queryFrameId[index] = this._engine.frameId;\r\n        }\r\n\r\n        return canBegin;\r\n    }\r\n\r\n    constructor(engine: WebGPUEngine, device: GPUDevice, bufferManager: WebGPUBufferManager, startCount = 50, incrementCount = 100) {\r\n        this._engine = engine;\r\n        this._device = device;\r\n        this._bufferManager = bufferManager;\r\n\r\n        this._frameLastBuffer = -1;\r\n        this._currentTotalIndices = 0;\r\n        this._countIncrement = incrementCount;\r\n\r\n        this._allocateNewIndices(startCount);\r\n    }\r\n\r\n    public createQuery(): number {\r\n        if (this._availableIndices.length === 0) {\r\n            this._allocateNewIndices();\r\n        }\r\n\r\n        const index = this._availableIndices[this._availableIndices.length - 1];\r\n        this._availableIndices.length--;\r\n\r\n        return index;\r\n    }\r\n\r\n    public deleteQuery(index: number): void {\r\n        this._availableIndices[this._availableIndices.length] = index;\r\n    }\r\n\r\n    public isQueryResultAvailable(index: number): boolean {\r\n        this._retrieveQueryBuffer();\r\n\r\n        return !!this._lastBuffer && index < this._lastBuffer.length;\r\n    }\r\n\r\n    public getQueryResult(index: number): number {\r\n        return Number(this._lastBuffer?.[index] ?? -1);\r\n    }\r\n\r\n    private _retrieveQueryBuffer(): void {\r\n        if (this._lastBuffer && this._frameLastBuffer === this._engine.frameId) {\r\n            return;\r\n        }\r\n\r\n        if (this._frameLastBuffer !== this._engine.frameId) {\r\n            this._frameLastBuffer = this._engine.frameId;\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n            this._querySet.readValues(0, this._currentTotalIndices).then((arrayBuffer) => {\r\n                this._lastBuffer = arrayBuffer;\r\n            });\r\n        }\r\n    }\r\n\r\n    private _allocateNewIndices(numIndices?: number): void {\r\n        numIndices = numIndices ?? this._countIncrement;\r\n\r\n        this._delayQuerySetDispose();\r\n\r\n        for (let i = 0; i < numIndices; ++i) {\r\n            this._availableIndices.push(this._currentTotalIndices + i);\r\n        }\r\n\r\n        this._currentTotalIndices += numIndices;\r\n        this._querySet = new WebGPUQuerySet(\r\n            this._engine,\r\n            this._currentTotalIndices,\r\n            WebGPUConstants.QueryType.Occlusion,\r\n            this._device,\r\n            this._bufferManager,\r\n            false,\r\n            \"QuerySet_OcclusionQuery_count_\" + this._currentTotalIndices\r\n        );\r\n\r\n        this._frameQuerySetIsDirty = this._engine.frameId;\r\n    }\r\n\r\n    private _delayQuerySetDispose(): void {\r\n        const querySet = this._querySet;\r\n        if (querySet) {\r\n            // Wait a bit before disposing of the queryset, in case some queries are still running for it\r\n            setTimeout(() => querySet.dispose, 1000);\r\n        }\r\n    }\r\n\r\n    public dispose(): void {\r\n        this._querySet?.dispose();\r\n        this._availableIndices.length = 0;\r\n    }\r\n}\r\n"]}