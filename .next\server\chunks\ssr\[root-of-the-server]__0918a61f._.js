module.exports = {

"[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_59fa4ecd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack]/browser/dev/hmr-client/hmr-client.ts [app-ssr] (ecmascript)");
    });
});
}),
"[project]/src/scripts/babylon-particles.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@babylonjs_core_37a6efab._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Misc_8a10be1a._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Maths_5f867789._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Engines_thinEngine_27ab066c.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Engines_Extensions_f8a59c9a._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Engines_WebGPU_be8e2ebe._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Engines_webgpuEngine_97a12dc1.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Engines_d627206d._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Animations_aa11c4a8._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Bones_b4ab8488._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Materials_Textures_57106a0a._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Materials_PBR_5bfcaab0._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Materials_Node_Blocks_Fragment_f5765f1f._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Materials_Node_Blocks_Dual_ddcaf4d8._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Materials_Node_Blocks_PBR_c905572d._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Materials_Node_Blocks_cc13e65d._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Materials_Node_baa6cd47._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Materials_a39bfc4f._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_PostProcesses_94ede247._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Rendering_5fb8f4f8._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Lights_7dda06f5._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_scene_3575b6f1.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Cameras_6d2a043e._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Culling_71364488._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Meshes_mesh_b2dafcf0.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Meshes_Builders_42107ee5._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Meshes_Node_f8d35846._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Meshes_d7592487._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Loading_e41f77ec._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Audio_c8158edf._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_AudioV2_ac8a5486._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Shaders_7e5f6154._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Behaviors_e6d0dbfe._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_XR_fd3bd84e._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_ShadersWGSL_7a083652._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Gizmos_8c13caef._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Debug_0ad2ec08._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Physics_8b6922c4._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_FrameGraph_eb012240._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_FlowGraph_4c4b8dfa._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Layers_0b48c927._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Particles_b0a9fdce._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_Sprites_eebda5be._.js",
  "server/chunks/ssr/node_modules_@babylonjs_core_5df4db51._.js",
  "server/chunks/ssr/src_scripts_babylon-particles_ts_3efdf04c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/scripts/babylon-particles.ts [app-ssr] (ecmascript)");
    });
});
}),

};