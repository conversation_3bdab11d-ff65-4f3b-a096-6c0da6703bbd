{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/lib/content.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport matter from 'gray-matter'\nimport readingTime from 'reading-time'\n\nconst contentDirectory = path.join(process.cwd(), 'src/content')\n\nexport interface BlogPost {\n  slug: string\n  title: string\n  description: string\n  publishedAt: string\n  updatedAt?: string\n  image?: string\n  imageAlt?: string\n  tags: string[]\n  category: string\n  featured: boolean\n  draft: boolean\n  author: string\n  readingTime: number\n  content: string\n}\n\nexport interface Project {\n  slug: string\n  title: string\n  description: string\n  image: string\n  technologies: string[]\n  githubUrl?: string\n  liveUrl?: string\n  featured: boolean\n  status: 'completed' | 'in-progress' | 'planned'\n  startDate: string\n  endDate?: string\n  category: 'web' | 'mobile' | 'desktop' | 'data-analysis' | 'design'\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  teamSize: number\n  highlights: string[]\n  challenges: string[]\n  learnings: string[]\n  content: string\n}\n\nexport interface Skill {\n  id: string\n  name: string\n  category: 'frontend' | 'backend' | 'database' | 'tools' | 'design' | 'finance' | 'soft-skills'\n  level: number\n  icon?: string\n  description?: string\n  experience?: string\n  certifications: string[]\n  projects: string[]\n  lastUsed?: string\n}\n\n// Blog functions\nexport function getAllBlogPosts(): BlogPost[] {\n  const blogDirectory = path.join(contentDirectory, 'blog')\n  const filenames = fs.readdirSync(blogDirectory)\n  \n  const posts = filenames\n    .filter(name => name.endsWith('.md') || name.endsWith('.mdx'))\n    .map(name => {\n      const fullPath = path.join(blogDirectory, name)\n      const fileContents = fs.readFileSync(fullPath, 'utf8')\n      const { data, content } = matter(fileContents)\n      const slug = name.replace(/\\.(md|mdx)$/, '')\n      \n      return {\n        slug,\n        title: data.title,\n        description: data.description,\n        publishedAt: data.publishedAt,\n        updatedAt: data.updatedAt,\n        image: data.image,\n        imageAlt: data.imageAlt,\n        tags: data.tags || [],\n        category: data.category,\n        featured: data.featured || false,\n        draft: data.draft || false,\n        author: data.author || 'Muhammad Trinanda',\n        readingTime: Math.ceil(readingTime(content).minutes),\n        content\n      } as BlogPost\n    })\n    .filter(post => !post.draft)\n    .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime())\n  \n  return posts\n}\n\nexport function getBlogPostBySlug(slug: string): BlogPost | null {\n  try {\n    const fullPath = path.join(contentDirectory, 'blog', `${slug}.md`)\n    const fileContents = fs.readFileSync(fullPath, 'utf8')\n    const { data, content } = matter(fileContents)\n    \n    return {\n      slug,\n      title: data.title,\n      description: data.description,\n      publishedAt: data.publishedAt,\n      updatedAt: data.updatedAt,\n      image: data.image,\n      imageAlt: data.imageAlt,\n      tags: data.tags || [],\n      category: data.category,\n      featured: data.featured || false,\n      draft: data.draft || false,\n      author: data.author || 'Muhammad Trinanda',\n      readingTime: Math.ceil(readingTime(content).minutes),\n      content\n    } as BlogPost\n  } catch (error) {\n    return null\n  }\n}\n\nexport function getFeaturedBlogPosts(): BlogPost[] {\n  return getAllBlogPosts().filter(post => post.featured)\n}\n\nexport function getBlogPostsByCategory(category: string): BlogPost[] {\n  return getAllBlogPosts().filter(post => post.category === category)\n}\n\nexport function getBlogPostsByTag(tag: string): BlogPost[] {\n  return getAllBlogPosts().filter(post => post.tags.includes(tag))\n}\n\n// Project functions\nexport function getAllProjects(): Project[] {\n  const projectsDirectory = path.join(contentDirectory, 'projects')\n  const filenames = fs.readdirSync(projectsDirectory)\n  \n  const projects = filenames\n    .filter(name => name.endsWith('.md') || name.endsWith('.mdx'))\n    .map(name => {\n      const fullPath = path.join(projectsDirectory, name)\n      const fileContents = fs.readFileSync(fullPath, 'utf8')\n      const { data, content } = matter(fileContents)\n      const slug = name.replace(/\\.(md|mdx)$/, '')\n      \n      return {\n        slug,\n        title: data.title,\n        description: data.description,\n        image: data.image,\n        technologies: data.technologies || [],\n        githubUrl: data.githubUrl,\n        liveUrl: data.liveUrl,\n        featured: data.featured || false,\n        status: data.status || 'completed',\n        startDate: data.startDate,\n        endDate: data.endDate,\n        category: data.category,\n        difficulty: data.difficulty || 'intermediate',\n        teamSize: data.teamSize || 1,\n        highlights: data.highlights || [],\n        challenges: data.challenges || [],\n        learnings: data.learnings || [],\n        content\n      } as Project\n    })\n    .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())\n  \n  return projects\n}\n\nexport function getProjectBySlug(slug: string): Project | null {\n  try {\n    const fullPath = path.join(contentDirectory, 'projects', `${slug}.md`)\n    const fileContents = fs.readFileSync(fullPath, 'utf8')\n    const { data, content } = matter(fileContents)\n    \n    return {\n      slug,\n      title: data.title,\n      description: data.description,\n      image: data.image,\n      technologies: data.technologies || [],\n      githubUrl: data.githubUrl,\n      liveUrl: data.liveUrl,\n      featured: data.featured || false,\n      status: data.status || 'completed',\n      startDate: data.startDate,\n      endDate: data.endDate,\n      category: data.category,\n      difficulty: data.difficulty || 'intermediate',\n      teamSize: data.teamSize || 1,\n      highlights: data.highlights || [],\n      challenges: data.challenges || [],\n      learnings: data.learnings || [],\n      content\n    } as Project\n  } catch (error) {\n    return null\n  }\n}\n\nexport function getFeaturedProjects(): Project[] {\n  return getAllProjects().filter(project => project.featured)\n}\n\nexport function getProjectsByCategory(category: string): Project[] {\n  return getAllProjects().filter(project => project.category === category)\n}\n\n// Skills functions\nexport function getAllSkills(): Skill[] {\n  try {\n    const skillsPath = path.join(contentDirectory, 'skills', 'skills.json')\n    const fileContents = fs.readFileSync(skillsPath, 'utf8')\n    return JSON.parse(fileContents)\n  } catch (error) {\n    return []\n  }\n}\n\nexport function getSkillsByCategory(category: string): Skill[] {\n  return getAllSkills().filter(skill => skill.category === category)\n}\n\n// Utility functions\nexport function getAllTags(): string[] {\n  const posts = getAllBlogPosts()\n  const tags = posts.flatMap(post => post.tags)\n  return [...new Set(tags)].sort()\n}\n\nexport function getAllCategories(): string[] {\n  const posts = getAllBlogPosts()\n  const categories = posts.map(post => post.category)\n  return [...new Set(categories)].sort()\n}\n\nexport function getRelatedPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {\n  const allPosts = getAllBlogPosts().filter(post => post.slug !== currentPost.slug)\n  \n  // Score posts based on shared tags and category\n  const scoredPosts = allPosts.map(post => {\n    let score = 0\n    \n    // Same category gets higher score\n    if (post.category === currentPost.category) {\n      score += 3\n    }\n    \n    // Shared tags get points\n    const sharedTags = post.tags.filter(tag => currentPost.tags.includes(tag))\n    score += sharedTags.length\n    \n    return { post, score }\n  })\n  \n  return scoredPosts\n    .sort((a, b) => b.score - a.score)\n    .slice(0, limit)\n    .map(item => item.post)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,mBAAmB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAsD3C,SAAS;IACd,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,kBAAkB;IAClD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC;IAEjC,MAAM,QAAQ,UACX,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC,SACrD,GAAG,CAAC,CAAA;QACH,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,eAAe;QAC1C,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;QAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;QACjC,MAAM,OAAO,KAAK,OAAO,CAAC,eAAe;QAEzC,OAAO;YACL;YACA,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,aAAa,KAAK,WAAW;YAC7B,WAAW,KAAK,SAAS;YACzB,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;YACvB,MAAM,KAAK,IAAI,IAAI,EAAE;YACrB,UAAU,KAAK,QAAQ;YACvB,UAAU,KAAK,QAAQ,IAAI;YAC3B,OAAO,KAAK,KAAK,IAAI;YACrB,QAAQ,KAAK,MAAM,IAAI;YACvB,aAAa,KAAK,IAAI,CAAC,CAAA,GAAA,wIAAA,CAAA,UAAW,AAAD,EAAE,SAAS,OAAO;YACnD;QACF;IACF,GACC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,KAAK,EAC1B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;IAErF,OAAO;AACT;AAEO,SAAS,kBAAkB,IAAY;IAC5C,IAAI;QACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,kBAAkB,QAAQ,GAAG,KAAK,GAAG,CAAC;QACjE,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;QAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;QAEjC,OAAO;YACL;YACA,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,aAAa,KAAK,WAAW;YAC7B,WAAW,KAAK,SAAS;YACzB,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;YACvB,MAAM,KAAK,IAAI,IAAI,EAAE;YACrB,UAAU,KAAK,QAAQ;YACvB,UAAU,KAAK,QAAQ,IAAI;YAC3B,OAAO,KAAK,KAAK,IAAI;YACrB,QAAQ,KAAK,MAAM,IAAI;YACvB,aAAa,KAAK,IAAI,CAAC,CAAA,GAAA,wIAAA,CAAA,UAAW,AAAD,EAAE,SAAS,OAAO;YACnD;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS;IACd,OAAO,kBAAkB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AACvD;AAEO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,kBAAkB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AAC5D;AAEO,SAAS,kBAAkB,GAAW;IAC3C,OAAO,kBAAkB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;AAC7D;AAGO,SAAS;IACd,MAAM,oBAAoB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,kBAAkB;IACtD,MAAM,YAAY,6FAAA,CAAA,UAAE,CAAC,WAAW,CAAC;IAEjC,MAAM,WAAW,UACd,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC,SACrD,GAAG,CAAC,CAAA;QACH,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,mBAAmB;QAC9C,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;QAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;QACjC,MAAM,OAAO,KAAK,OAAO,CAAC,eAAe;QAEzC,OAAO;YACL;YACA,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,OAAO,KAAK,KAAK;YACjB,cAAc,KAAK,YAAY,IAAI,EAAE;YACrC,WAAW,KAAK,SAAS;YACzB,SAAS,KAAK,OAAO;YACrB,UAAU,KAAK,QAAQ,IAAI;YAC3B,QAAQ,KAAK,MAAM,IAAI;YACvB,WAAW,KAAK,SAAS;YACzB,SAAS,KAAK,OAAO;YACrB,UAAU,KAAK,QAAQ;YACvB,YAAY,KAAK,UAAU,IAAI;YAC/B,UAAU,KAAK,QAAQ,IAAI;YAC3B,YAAY,KAAK,UAAU,IAAI,EAAE;YACjC,YAAY,KAAK,UAAU,IAAI,EAAE;YACjC,WAAW,KAAK,SAAS,IAAI,EAAE;YAC/B;QACF;IACF,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IAEjF,OAAO;AACT;AAEO,SAAS,iBAAiB,IAAY;IAC3C,IAAI;QACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,kBAAkB,YAAY,GAAG,KAAK,GAAG,CAAC;QACrE,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,UAAU;QAC/C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAM,AAAD,EAAE;QAEjC,OAAO;YACL;YACA,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,OAAO,KAAK,KAAK;YACjB,cAAc,KAAK,YAAY,IAAI,EAAE;YACrC,WAAW,KAAK,SAAS;YACzB,SAAS,KAAK,OAAO;YACrB,UAAU,KAAK,QAAQ,IAAI;YAC3B,QAAQ,KAAK,MAAM,IAAI;YACvB,WAAW,KAAK,SAAS;YACzB,SAAS,KAAK,OAAO;YACrB,UAAU,KAAK,QAAQ;YACvB,YAAY,KAAK,UAAU,IAAI;YAC/B,UAAU,KAAK,QAAQ,IAAI;YAC3B,YAAY,KAAK,UAAU,IAAI,EAAE;YACjC,YAAY,KAAK,UAAU,IAAI,EAAE;YACjC,WAAW,KAAK,SAAS,IAAI,EAAE;YAC/B;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS;IACd,OAAO,iBAAiB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;AAC5D;AAEO,SAAS,sBAAsB,QAAgB;IACpD,OAAO,iBAAiB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACjE;AAGO,SAAS;IACd,IAAI;QACF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,kBAAkB,UAAU;QACzD,MAAM,eAAe,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,YAAY;QACjD,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,OAAO,EAAE;IACX;AACF;AAEO,SAAS,oBAAoB,QAAgB;IAClD,OAAO,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AAC3D;AAGO,SAAS;IACd,MAAM,QAAQ;IACd,MAAM,OAAO,MAAM,OAAO,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC5C,OAAO;WAAI,IAAI,IAAI;KAAM,CAAC,IAAI;AAChC;AAEO,SAAS;IACd,MAAM,QAAQ;IACd,MAAM,aAAa,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;IAClD,OAAO;WAAI,IAAI,IAAI;KAAY,CAAC,IAAI;AACtC;AAEO,SAAS,gBAAgB,WAAqB,EAAE,QAAgB,CAAC;IACtE,MAAM,WAAW,kBAAkB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,YAAY,IAAI;IAEhF,gDAAgD;IAChD,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA;QAC/B,IAAI,QAAQ;QAEZ,kCAAkC;QAClC,IAAI,KAAK,QAAQ,KAAK,YAAY,QAAQ,EAAE;YAC1C,SAAS;QACX;QAEA,yBAAyB;QACzB,MAAM,aAAa,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,YAAY,IAAI,CAAC,QAAQ,CAAC;QACrE,SAAS,WAAW,MAAM;QAE1B,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO,YACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,OACT,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;AAC1B", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/app/blog/page.tsx"], "sourcesContent": ["import { getAllBlogPosts } from '@/lib/content'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport Link from 'next/link'\nimport { CalendarDays, Clock, User } from 'lucide-react'\n\nexport default function BlogPage() {\n  const posts = getAllBlogPosts()\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-white mb-4\">\n            <span className=\"bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent\">\n              Blog\n            </span>\n          </h1>\n          <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">\n            Thoughts, tutorials, and insights about web development, 3D graphics, and modern technologies.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {posts.map((post) => (\n            <Link key={post.slug} href={`/blog/${post.slug}`}>\n              <Card className=\"h-full bg-slate-800/50 border-slate-700 hover:border-cyan-400/50 transition-all duration-300 hover:scale-105 cursor-pointer\">\n                <CardHeader>\n                  <div className=\"flex flex-wrap gap-2 mb-3\">\n                    {post.tags.map((tag) => (\n                      <Badge key={tag} variant=\"secondary\" className=\"bg-cyan-400/20 text-cyan-300 hover:bg-cyan-400/30\">\n                        {tag}\n                      </Badge>\n                    ))}\n                  </div>\n                  <CardTitle className=\"text-white hover:text-cyan-400 transition-colors\">\n                    {post.title}\n                  </CardTitle>\n                  <CardDescription className=\"text-gray-400\">\n                    {post.description}\n                  </CardDescription>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"flex items-center gap-1\">\n                        <User className=\"w-4 h-4\" />\n                        <span>{post.author}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Clock className=\"w-4 h-4\" />\n                        <span>{post.readingTime} min read</span>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-1\">\n                      <CalendarDays className=\"w-4 h-4\" />\n                      <span>{new Date(post.publishedAt).toLocaleDateString()}</span>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </Link>\n          ))}\n        </div>\n\n        {posts.length === 0 && (\n          <div className=\"text-center py-12\">\n            <p className=\"text-gray-400 text-lg\">No blog posts found.</p>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;AAEe,SAAS;IACtB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD;IAE5B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAA6E;;;;;;;;;;;sCAI/F,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,4JAAA,CAAA,UAAI;4BAAiB,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;sCAC9C,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,8OAAC,iIAAA,CAAA,QAAK;wDAAW,SAAQ;wDAAY,WAAU;kEAC5C;uDADS;;;;;;;;;;0DAKhB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,KAAK,KAAK;;;;;;0DAEb,8OAAC,gIAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,KAAK,WAAW;;;;;;;;;;;;kDAGrB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAM,KAAK,MAAM;;;;;;;;;;;;sEAEpB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;;wEAAM,KAAK,WAAW;wEAAC;;;;;;;;;;;;;;;;;;;8DAG5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC;sEAAM,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA/BnD,KAAK,IAAI;;;;;;;;;;gBAwCvB,MAAM,MAAM,KAAK,mBAChB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}]}