{"version": 3, "file": "webgpuCacheSampler.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuCacheSampler.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAIzC,MAAM,YAAY,GAAG;IACjB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW;IACpC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,yDAAyD;IAClF,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,wDAAwD;IACjF,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,mEAAmE;IAC5F,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,qCAAqC;IAC9D,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,oCAAoC;IAC7D,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,mCAAmC;IAC5D,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,yBAAyB;IAClD,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,oCAAoC;IAC7D,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,oCAAoC;IAC7D,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,mCAAmC;IAC5D,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,mCAAmC;IAC5D,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,yBAAyB;CACrD,CAAC;AAEF,iFAAiF;AACjF,MAAM,wBAAwB,GAAG;IAC7B,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,YAAY;IACvD,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ;IACnD,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO;IAClD,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ;IACnD,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS;IACpD,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,UAAU;IACrD,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW;IACtD,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS;IACpD,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS;CACvD,CAAC;AAEF,MAAM,iBAAiB,GAAG;IACtB,CAAC,IAAI,CAAC,EAAE,WAAW;IACnB,CAAC,IAAI,CAAC,EAAE,yDAAyD;IACjE,CAAC,IAAI,CAAC,EAAE,wDAAwD;IAChE,CAAC,IAAI,CAAC,EAAE,mEAAmE;IAC3E,CAAC,IAAI,CAAC,EAAE,qCAAqC;IAC7C,CAAC,IAAI,CAAC,EAAE,oCAAoC;IAC5C,CAAC,IAAI,CAAC,EAAE,mCAAmC;IAC3C,CAAC,IAAI,CAAC,EAAE,yBAAyB;IACjC,CAAC,IAAI,CAAC,EAAE,oCAAoC;IAC5C,CAAC,IAAI,CAAC,EAAE,oCAAoC;IAC5C,CAAC,IAAI,CAAC,EAAE,mCAAmC;IAC3C,CAAC,IAAI,CAAC,EAAE,mCAAmC;IAC3C,CAAC,IAAI,CAAC,EAAE,yBAAyB;CACpC,CAAC;AAEF,gBAAgB;AAChB,MAAM,OAAO,kBAAkB;IAM3B,YAAY,MAAiB;QALrB,cAAS,GAAmC,EAAE,CAAC;QAMnD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,OAAuB;QACpD,sEAAsE;QACtE,MAAM,UAAU,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3G,MAAM,IAAI,GACN,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC;YAClC,wBAAwB,CAAC,CAAC,OAAO,CAAC,mBAAmB,IAAI,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;YAC9E,iBAAiB,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,oFAAoF;YAC9H,CAAC,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;YACnC,CAAC,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;YACnC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,qGAAqG;YAC5I,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;QAEvB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,MAAM,CAAC,2BAA2B,CACtC,OAAuB,EACvB,UAAkB;QASlB,IAAI,SAAwB,EAAE,SAAwB,EAAE,YAA2B,EAAE,WAA+B,EAAE,WAA+B,CAAC;QACtJ,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACtC,QAAQ,OAAO,CAAC,YAAY,EAAE,CAAC;YAC3B,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,SAAS,mDAAoC,CAAC;gBAC9C,SAAS,mDAAoC,CAAC;gBAC9C,YAAY,qDAAqC,CAAC;gBAClD,IAAI,CAAC,UAAU,EAAE,CAAC;oBACd,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAClC,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,+BAA+B,CAAC;YAC/C,KAAK,SAAS,CAAC,8BAA8B;gBACzC,SAAS,mDAAoC,CAAC;gBAC9C,SAAS,mDAAoC,CAAC;gBAC9C,IAAI,CAAC,UAAU,EAAE,CAAC;oBACd,YAAY,qDAAqC,CAAC;oBAClD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAClC,CAAC;qBAAM,CAAC;oBACJ,YAAY,mDAAoC,CAAC;gBACrD,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,SAAS,qDAAqC,CAAC;gBAC/C,SAAS,qDAAqC,CAAC;gBAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;oBACd,YAAY,qDAAqC,CAAC;oBAClD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAClC,CAAC;qBAAM,CAAC;oBACJ,YAAY,mDAAoC,CAAC;gBACrD,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,SAAS,qDAAqC,CAAC;gBAC/C,SAAS,qDAAqC,CAAC;gBAC/C,YAAY,qDAAqC,CAAC;gBAClD,IAAI,CAAC,UAAU,EAAE,CAAC;oBACd,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAClC,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,SAAS,qDAAqC,CAAC;gBAC/C,SAAS,mDAAoC,CAAC;gBAC9C,YAAY,qDAAqC,CAAC;gBAClD,IAAI,CAAC,UAAU,EAAE,CAAC;oBACd,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAClC,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,SAAS,qDAAqC,CAAC;gBAC/C,SAAS,mDAAoC,CAAC;gBAC9C,IAAI,CAAC,UAAU,EAAE,CAAC;oBACd,YAAY,qDAAqC,CAAC;oBAClD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAClC,CAAC;qBAAM,CAAC;oBACJ,YAAY,mDAAoC,CAAC;gBACrD,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,sBAAsB;gBACjC,SAAS,qDAAqC,CAAC;gBAC/C,SAAS,mDAAoC,CAAC;gBAC9C,YAAY,qDAAqC,CAAC;gBAClD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAC9B,MAAM;YACV,KAAK,SAAS,CAAC,uBAAuB,CAAC;YACvC,KAAK,SAAS,CAAC,4BAA4B;gBACvC,SAAS,qDAAqC,CAAC;gBAC/C,SAAS,qDAAqC,CAAC;gBAC/C,YAAY,qDAAqC,CAAC;gBAClD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAC9B,MAAM;YACV,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,SAAS,mDAAoC,CAAC;gBAC9C,SAAS,qDAAqC,CAAC;gBAC/C,YAAY,qDAAqC,CAAC;gBAClD,IAAI,CAAC,UAAU,EAAE,CAAC;oBACd,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAClC,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,SAAS,mDAAoC,CAAC;gBAC9C,SAAS,qDAAqC,CAAC;gBAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;oBACd,YAAY,qDAAqC,CAAC;oBAClD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAClC,CAAC;qBAAM,CAAC;oBACJ,YAAY,mDAAoC,CAAC;gBACrD,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,qBAAqB,CAAC;YACrC,KAAK,SAAS,CAAC,6BAA6B;gBACxC,SAAS,mDAAoC,CAAC;gBAC9C,SAAS,mDAAoC,CAAC;gBAC9C,sIAAsI;gBACtI,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;oBACjB,YAAY,mDAAoC,CAAC;gBACrD,CAAC;qBAAM,CAAC;oBACJ,YAAY,qDAAqC,CAAC;oBAClD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAClC,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,sBAAsB;gBACjC,SAAS,mDAAoC,CAAC;gBAC9C,SAAS,qDAAqC,CAAC;gBAC/C,YAAY,qDAAqC,CAAC;gBAClD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAC9B,MAAM;YACV;gBACI,SAAS,qDAAqC,CAAC;gBAC/C,SAAS,qDAAqC,CAAC;gBAC/C,YAAY,qDAAqC,CAAC;gBAClD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;gBAC9B,MAAM;QACd,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7D,OAAO;gBACH,SAAS,kDAAmC;gBAC5C,SAAS,kDAAmC;gBAC5C,YAAY,kDAAmC;gBAC/C,iBAAiB,EAAE,IAAI;aAC1B,CAAC;QACN,CAAC;QAED,OAAO;YACH,SAAS;YACT,SAAS;YACT,YAAY;YACZ,WAAW;YACX,WAAW;SACd,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,IAAY;QACxC,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,SAAS,CAAC,wBAAwB;gBACnC,yDAA0C;YAC9C,KAAK,SAAS,CAAC,yBAAyB;gBACpC,qEAA+C;YACnD,KAAK,SAAS,CAAC,0BAA0B;gBACrC,sEAAgD;QACxD,CAAC;QACD,yDAA0C;IAC9C,CAAC;IAEO,MAAM,CAAC,6BAA6B,CAAC,OAAuB;QAKhE,OAAO;YACH,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAa,CAAC;YAC1D,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAa,CAAC;YAC1D,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAa,CAAC;SAC7D,CAAC;IACN,CAAC;IAEO,MAAM,CAAC,qBAAqB,CAAC,OAAuB,EAAE,KAAc;QACxE,4FAA4F;QAC5F,IAAI,UAAU,GACV,CAAC,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,gCAAgC;YACxH,CAAC,CAAC,OAAO,CAAC,gCAAgC;YAC1C,CAAC,CAAC,CAAC,CAAC;QACZ,0CAA0C;QAC1C,IACI,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,gCAAgC;YACnE,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,+BAA+B;YAClE,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,qBAAqB,EAC1D,CAAC;YACC,UAAU,GAAG,CAAC,CAAC;QACnB,CAAC;QACD,MAAM,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC/E,OAAO;YACH,KAAK;YACL,GAAG,gBAAgB;YACnB,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC;YAC9C,OAAO,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS;YACrH,aAAa,EAAE,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SACrE,CAAC;IACN,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,eAAiC;QAC9D,QAAQ,eAAe,EAAE,CAAC;YACtB,KAAK,SAAS,CAAC,MAAM;gBACjB,6DAA8C;YAClD,KAAK,SAAS,CAAC,KAAK;gBAChB,2DAA6C;YACjD,KAAK,SAAS,CAAC,OAAO;gBAClB,+DAA+C;YACnD,KAAK,SAAS,CAAC,MAAM;gBACjB,0EAAoD;YACxD,KAAK,SAAS,CAAC,IAAI;gBACf,yDAA4C;YAChD,KAAK,SAAS,CAAC,MAAM;gBACjB,oEAAiD;YACrD,KAAK,SAAS,CAAC,KAAK;gBAChB,2DAA6C;YACjD,KAAK,SAAS,CAAC,QAAQ;gBACnB,kEAAgD;YACpD;gBACI,yDAA4C;QACpD,CAAC;IACL,CAAC;IAEM,UAAU,CAAC,OAAuB,EAAE,WAAW,GAAG,KAAK,EAAE,IAAI,GAAG,CAAC,EAAE,KAAc;QACpF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAChG,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,GAAG,CAAC,CAAC;QACb,CAAC;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACpB,IAAI,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;YAClG,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;YACtC,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ", "sourcesContent": ["/* eslint-disable babylonjs/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport { Constants } from \"../constants\";\r\nimport type { TextureSampler } from \"../../Materials/Textures/textureSampler\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\nconst FilterToBits = [\r\n    0 | (0 << 1) | (0 << 2), // not used\r\n    0 | (0 << 1) | (0 << 2), // TEXTURE_NEAREST_SAMPLINGMODE / TEXTURE_NEAREST_NEAREST\r\n    1 | (1 << 1) | (0 << 2), // TEXTURE_BILINEAR_SAMPLINGMODE / TEXTURE_LINEAR_LINEAR\r\n    1 | (1 << 1) | (1 << 2), // TEXTURE_TRILINEAR_SAMPLINGMODE / TEXTURE_LINEAR_LINEAR_MIPLINEAR\r\n    0 | (0 << 1) | (0 << 2), // TEXTURE_NEAREST_NEAREST_MIPNEAREST\r\n    0 | (1 << 1) | (0 << 2), // TEXTURE_NEAREST_LINEAR_MIPNEAREST\r\n    0 | (1 << 1) | (1 << 2), // TEXTURE_NEAREST_LINEAR_MIPLINEAR\r\n    0 | (1 << 1) | (0 << 2), // TEXTURE_NEAREST_LINEAR\r\n    0 | (0 << 1) | (1 << 2), // TEXTURE_NEAREST_NEAREST_MIPLINEAR\r\n    1 | (0 << 1) | (0 << 2), // TEXTURE_LINEAR_NEAREST_MIPNEAREST\r\n    1 | (0 << 1) | (1 << 2), // TEXTURE_LINEAR_NEAREST_MIPLINEAR\r\n    1 | (1 << 1) | (0 << 2), // TEXTURE_LINEAR_LINEAR_MIPNEAREST\r\n    1 | (0 << 1) | (0 << 2), // TEXTURE_LINEAR_NEAREST\r\n];\r\n\r\n// subtract 0x01FF from the comparison function value before indexing this array!\r\nconst ComparisonFunctionToBits = [\r\n    (0 << 3) | (0 << 4) | (0 << 5) | (0 << 6), // undefined\r\n    (0 << 3) | (0 << 4) | (0 << 5) | (1 << 6), // NEVER\r\n    (0 << 3) | (0 << 4) | (1 << 5) | (0 << 6), // LESS\r\n    (0 << 3) | (0 << 4) | (1 << 5) | (1 << 6), // EQUAL\r\n    (0 << 3) | (1 << 4) | (0 << 5) | (0 << 6), // LEQUAL\r\n    (0 << 3) | (1 << 4) | (0 << 5) | (1 << 6), // GREATER\r\n    (0 << 3) | (1 << 4) | (1 << 5) | (0 << 6), // NOTEQUAL\r\n    (0 << 3) | (1 << 4) | (1 << 5) | (1 << 6), // GEQUAL\r\n    (1 << 3) | (0 << 4) | (0 << 5) | (0 << 6), // ALWAYS\r\n];\r\n\r\nconst FilterNoMipToBits = [\r\n    0 << 7, // not used\r\n    1 << 7, // TEXTURE_NEAREST_SAMPLINGMODE / TEXTURE_NEAREST_NEAREST\r\n    1 << 7, // TEXTURE_BILINEAR_SAMPLINGMODE / TEXTURE_LINEAR_LINEAR\r\n    0 << 7, // TEXTURE_TRILINEAR_SAMPLINGMODE / TEXTURE_LINEAR_LINEAR_MIPLINEAR\r\n    0 << 7, // TEXTURE_NEAREST_NEAREST_MIPNEAREST\r\n    0 << 7, // TEXTURE_NEAREST_LINEAR_MIPNEAREST\r\n    0 << 7, // TEXTURE_NEAREST_LINEAR_MIPLINEAR\r\n    1 << 7, // TEXTURE_NEAREST_LINEAR\r\n    0 << 7, // TEXTURE_NEAREST_NEAREST_MIPLINEAR\r\n    0 << 7, // TEXTURE_LINEAR_NEAREST_MIPNEAREST\r\n    0 << 7, // TEXTURE_LINEAR_NEAREST_MIPLINEAR\r\n    0 << 7, // TEXTURE_LINEAR_LINEAR_MIPNEAREST\r\n    1 << 7, // TEXTURE_LINEAR_NEAREST\r\n];\r\n\r\n/** @internal */\r\nexport class WebGPUCacheSampler {\r\n    private _samplers: { [hash: number]: GPUSampler } = {};\r\n    private _device: GPUDevice;\r\n\r\n    public disabled: boolean;\r\n\r\n    constructor(device: GPUDevice) {\r\n        this._device = device;\r\n        this.disabled = false;\r\n    }\r\n\r\n    public static GetSamplerHashCode(sampler: TextureSampler): number {\r\n        // The WebGPU spec currently only allows values 1 and 4 for anisotropy\r\n        const anisotropy = sampler._cachedAnisotropicFilteringLevel ? sampler._cachedAnisotropicFilteringLevel : 1;\r\n        const code =\r\n            FilterToBits[sampler.samplingMode] +\r\n            ComparisonFunctionToBits[(sampler._comparisonFunction || 0x0202) - 0x0200 + 1] +\r\n            FilterNoMipToBits[sampler.samplingMode] + // handle the lodMinClamp = lodMaxClamp = 0 case when no filter used for mip mapping\r\n            ((sampler._cachedWrapU ?? 1) << 8) +\r\n            ((sampler._cachedWrapV ?? 1) << 10) +\r\n            ((sampler._cachedWrapR ?? 1) << 12) +\r\n            ((sampler.useMipMaps ? 1 : 0) << 14) + // need to factor this in because _getSamplerFilterDescriptor depends on samplingMode AND useMipMaps!\r\n            (anisotropy << 15);\r\n\r\n        return code;\r\n    }\r\n\r\n    private static _GetSamplerFilterDescriptor(\r\n        sampler: TextureSampler,\r\n        anisotropy: number\r\n    ): {\r\n        magFilter: GPUFilterMode;\r\n        minFilter: GPUFilterMode;\r\n        mipmapFilter: GPUFilterMode;\r\n        lodMinClamp?: number;\r\n        lodMaxClamp?: number;\r\n        anisotropyEnabled?: boolean;\r\n    } {\r\n        let magFilter: GPUFilterMode, minFilter: GPUFilterMode, mipmapFilter: GPUFilterMode, lodMinClamp: number | undefined, lodMaxClamp: number | undefined;\r\n        const useMipMaps = sampler.useMipMaps;\r\n        switch (sampler.samplingMode) {\r\n            case Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST:\r\n                magFilter = WebGPUConstants.FilterMode.Linear;\r\n                minFilter = WebGPUConstants.FilterMode.Linear;\r\n                mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                if (!useMipMaps) {\r\n                    lodMinClamp = lodMaxClamp = 0;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR:\r\n            case Constants.TEXTURE_TRILINEAR_SAMPLINGMODE:\r\n                magFilter = WebGPUConstants.FilterMode.Linear;\r\n                minFilter = WebGPUConstants.FilterMode.Linear;\r\n                if (!useMipMaps) {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                    lodMinClamp = lodMaxClamp = 0;\r\n                } else {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Linear;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_NEAREST_MIPLINEAR:\r\n                magFilter = WebGPUConstants.FilterMode.Nearest;\r\n                minFilter = WebGPUConstants.FilterMode.Nearest;\r\n                if (!useMipMaps) {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                    lodMinClamp = lodMaxClamp = 0;\r\n                } else {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Linear;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_NEAREST_MIPNEAREST:\r\n                magFilter = WebGPUConstants.FilterMode.Nearest;\r\n                minFilter = WebGPUConstants.FilterMode.Nearest;\r\n                mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                if (!useMipMaps) {\r\n                    lodMinClamp = lodMaxClamp = 0;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_LINEAR_MIPNEAREST:\r\n                magFilter = WebGPUConstants.FilterMode.Nearest;\r\n                minFilter = WebGPUConstants.FilterMode.Linear;\r\n                mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                if (!useMipMaps) {\r\n                    lodMinClamp = lodMaxClamp = 0;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_LINEAR_MIPLINEAR:\r\n                magFilter = WebGPUConstants.FilterMode.Nearest;\r\n                minFilter = WebGPUConstants.FilterMode.Linear;\r\n                if (!useMipMaps) {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                    lodMinClamp = lodMaxClamp = 0;\r\n                } else {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Linear;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_LINEAR:\r\n                magFilter = WebGPUConstants.FilterMode.Nearest;\r\n                minFilter = WebGPUConstants.FilterMode.Linear;\r\n                mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                lodMinClamp = lodMaxClamp = 0;\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_NEAREST:\r\n            case Constants.TEXTURE_NEAREST_SAMPLINGMODE:\r\n                magFilter = WebGPUConstants.FilterMode.Nearest;\r\n                minFilter = WebGPUConstants.FilterMode.Nearest;\r\n                mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                lodMinClamp = lodMaxClamp = 0;\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_NEAREST_MIPNEAREST:\r\n                magFilter = WebGPUConstants.FilterMode.Linear;\r\n                minFilter = WebGPUConstants.FilterMode.Nearest;\r\n                mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                if (!useMipMaps) {\r\n                    lodMinClamp = lodMaxClamp = 0;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_NEAREST_MIPLINEAR:\r\n                magFilter = WebGPUConstants.FilterMode.Linear;\r\n                minFilter = WebGPUConstants.FilterMode.Nearest;\r\n                if (!useMipMaps) {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                    lodMinClamp = lodMaxClamp = 0;\r\n                } else {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Linear;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_LINEAR:\r\n            case Constants.TEXTURE_BILINEAR_SAMPLINGMODE:\r\n                magFilter = WebGPUConstants.FilterMode.Linear;\r\n                minFilter = WebGPUConstants.FilterMode.Linear;\r\n                // In WebGL, if sampling mode is TEXTURE_BILINEAR_SAMPLINGMODE and anisotropy is greater than 1, anisotropy is enabled for the sampler\r\n                if (anisotropy > 1) {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Linear;\r\n                } else {\r\n                    mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                    lodMinClamp = lodMaxClamp = 0;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_NEAREST:\r\n                magFilter = WebGPUConstants.FilterMode.Linear;\r\n                minFilter = WebGPUConstants.FilterMode.Nearest;\r\n                mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                lodMinClamp = lodMaxClamp = 0;\r\n                break;\r\n            default:\r\n                magFilter = WebGPUConstants.FilterMode.Nearest;\r\n                minFilter = WebGPUConstants.FilterMode.Nearest;\r\n                mipmapFilter = WebGPUConstants.FilterMode.Nearest;\r\n                lodMinClamp = lodMaxClamp = 0;\r\n                break;\r\n        }\r\n\r\n        if (anisotropy > 1 && (lodMinClamp !== 0 || lodMaxClamp !== 0)) {\r\n            return {\r\n                magFilter: WebGPUConstants.FilterMode.Linear,\r\n                minFilter: WebGPUConstants.FilterMode.Linear,\r\n                mipmapFilter: WebGPUConstants.FilterMode.Linear,\r\n                anisotropyEnabled: true,\r\n            };\r\n        }\r\n\r\n        return {\r\n            magFilter,\r\n            minFilter,\r\n            mipmapFilter,\r\n            lodMinClamp,\r\n            lodMaxClamp,\r\n        };\r\n    }\r\n\r\n    private static _GetWrappingMode(mode: number): GPUAddressMode {\r\n        switch (mode) {\r\n            case Constants.TEXTURE_WRAP_ADDRESSMODE:\r\n                return WebGPUConstants.AddressMode.Repeat;\r\n            case Constants.TEXTURE_CLAMP_ADDRESSMODE:\r\n                return WebGPUConstants.AddressMode.ClampToEdge;\r\n            case Constants.TEXTURE_MIRROR_ADDRESSMODE:\r\n                return WebGPUConstants.AddressMode.MirrorRepeat;\r\n        }\r\n        return WebGPUConstants.AddressMode.Repeat;\r\n    }\r\n\r\n    private static _GetSamplerWrappingDescriptor(sampler: TextureSampler): {\r\n        addressModeU: GPUAddressMode;\r\n        addressModeV: GPUAddressMode;\r\n        addressModeW: GPUAddressMode;\r\n    } {\r\n        return {\r\n            addressModeU: this._GetWrappingMode(sampler._cachedWrapU!),\r\n            addressModeV: this._GetWrappingMode(sampler._cachedWrapV!),\r\n            addressModeW: this._GetWrappingMode(sampler._cachedWrapR!),\r\n        };\r\n    }\r\n\r\n    private static _GetSamplerDescriptor(sampler: TextureSampler, label?: string): GPUSamplerDescriptor {\r\n        // The check with Constants.TEXTURE_LINEAR_LINEAR is to be iso with the WebGL implementation\r\n        let anisotropy =\r\n            (sampler.useMipMaps || sampler.samplingMode === Constants.TEXTURE_LINEAR_LINEAR) && sampler._cachedAnisotropicFilteringLevel\r\n                ? sampler._cachedAnisotropicFilteringLevel\r\n                : 1;\r\n        // To be iso with the WebGL implementation\r\n        if (\r\n            sampler.samplingMode !== Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST &&\r\n            sampler.samplingMode !== Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR &&\r\n            sampler.samplingMode !== Constants.TEXTURE_LINEAR_LINEAR\r\n        ) {\r\n            anisotropy = 1;\r\n        }\r\n        const filterDescriptor = this._GetSamplerFilterDescriptor(sampler, anisotropy);\r\n        return {\r\n            label,\r\n            ...filterDescriptor,\r\n            ...this._GetSamplerWrappingDescriptor(sampler),\r\n            compare: sampler._comparisonFunction ? WebGPUCacheSampler.GetCompareFunction(sampler._comparisonFunction) : undefined,\r\n            maxAnisotropy: filterDescriptor.anisotropyEnabled ? anisotropy : 1,\r\n        };\r\n    }\r\n\r\n    public static GetCompareFunction(compareFunction: Nullable<number>): GPUCompareFunction {\r\n        switch (compareFunction) {\r\n            case Constants.ALWAYS:\r\n                return WebGPUConstants.CompareFunction.Always;\r\n            case Constants.EQUAL:\r\n                return WebGPUConstants.CompareFunction.Equal;\r\n            case Constants.GREATER:\r\n                return WebGPUConstants.CompareFunction.Greater;\r\n            case Constants.GEQUAL:\r\n                return WebGPUConstants.CompareFunction.GreaterEqual;\r\n            case Constants.LESS:\r\n                return WebGPUConstants.CompareFunction.Less;\r\n            case Constants.LEQUAL:\r\n                return WebGPUConstants.CompareFunction.LessEqual;\r\n            case Constants.NEVER:\r\n                return WebGPUConstants.CompareFunction.Never;\r\n            case Constants.NOTEQUAL:\r\n                return WebGPUConstants.CompareFunction.NotEqual;\r\n            default:\r\n                return WebGPUConstants.CompareFunction.Less;\r\n        }\r\n    }\r\n\r\n    public getSampler(sampler: TextureSampler, bypassCache = false, hash = 0, label?: string): GPUSampler {\r\n        if (this.disabled) {\r\n            return this._device.createSampler(WebGPUCacheSampler._GetSamplerDescriptor(sampler, label));\r\n        }\r\n\r\n        if (bypassCache) {\r\n            hash = 0;\r\n        } else if (hash === 0) {\r\n            hash = WebGPUCacheSampler.GetSamplerHashCode(sampler);\r\n        }\r\n\r\n        let gpuSampler = bypassCache ? undefined : this._samplers[hash];\r\n        if (!gpuSampler) {\r\n            gpuSampler = this._device.createSampler(WebGPUCacheSampler._GetSamplerDescriptor(sampler, label));\r\n            if (!bypassCache) {\r\n                this._samplers[hash] = gpuSampler;\r\n            }\r\n        }\r\n\r\n        return gpuSampler;\r\n    }\r\n}\r\n"]}