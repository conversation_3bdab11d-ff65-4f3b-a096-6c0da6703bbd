{"version": 3, "file": "webgpuTimestampQuery.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuTimestampQuery.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAGlD,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAE1C,gBAAgB;AAChB,MAAM,OAAO,oBAAoB;IAU7B,IAAW,mBAAmB;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,YAAY,MAAoB,EAAE,MAAiB,EAAE,aAAkC;QAT/E,aAAQ,GAAG,KAAK,CAAC;QACjB,yBAAoB,GAAgB,IAAI,WAAW,EAAE,CAAC;QAEtD,0BAAqB,GAAG,CAAC,CAAC;QAO9B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;IACxC,CAAC;IAED,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,MAAM,CAAC,KAAc;QAC5B,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;QAC/B,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC;gBACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAAC;YACxI,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACtB,MAAM,CAAC,KAAK,CAAC,oDAAoD,GAAG,CAAC,CAAC,OAAO,GAAG,uEAAuE,CAAC,CAAC;gBACzJ,OAAO;YACX,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QACpC,CAAC;IACL,CAAC;IAEM,UAAU,CAAC,cAAiC;QAC/C,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,qBAAqB,KAAK,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;YAC5C,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;QACnC,CAAC;IACL,CAAC;IAEM,QAAQ,CAAC,cAAiC;QAC7C,IAAI,IAAI,CAAC,qBAAqB,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;YAC/B,mFAAmF;YACnF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACzD,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;oBACrC,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,CAAC;oBAC1C,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACvD,CAAC;gBACD,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEM,SAAS,CAAC,UAA8D,EAAE,KAAa;QAC1F,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;aAAM,CAAC;YACJ,UAAU,CAAC,eAAe,GAAG,SAAS,CAAC;QAC3C,CAAC;IACL,CAAC;IAEM,OAAO,CAAC,KAAa,EAAE,cAAkC;QAC5D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,cAAc,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAE5C,mFAAmF;QACnF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YACrD,cAAc,CAAC,YAAY,CAAC,cAAc,EAAE,SAAS,KAAK,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;IACrC,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,qBAAqB;IAI9B,YAAY,MAAoB,EAAE,MAAiB,EAAE,aAAkC,EAAE,KAAK,GAAG,CAAC,EAAE,aAAsB;QACtH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,KAAK,yDAAuC,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;IACxI,CAAC;IAEM,KAAK,CAAC,OAA0B;QACnC,OAAO,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,OAA0B;QACxC,OAAO,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAErD,OAAO,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,CAAC;IAEM,SAAS,CAAC,UAA8D,EAAE,KAAa;QAC1F,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,6CAA6C,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC;QACjF,CAAC;QAED,UAAU,CAAC,eAAe,GAAG;YACzB,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ;YACjC,yBAAyB,EAAE,KAAK,GAAG,CAAC;YACpC,mBAAmB,EAAE,KAAK,GAAG,CAAC;SACjC,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,KAAa;QAC/B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;IACpE,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;CACJ", "sourcesContent": ["/* eslint-disable babylonjs/available */\r\n/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { WebGPUBufferManager } from \"./webgpuBufferManager\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport { PerfCounter } from \"../../Misc/perfCounter\";\r\nimport { WebGPUQuerySet } from \"./webgpuQuerySet\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\nimport type { WebGPUPerfCounter } from \"./webgpuPerfCounter\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/** @internal */\r\nexport class WebGPUTimestampQuery {\r\n    private _engine: WebGPUEngine;\r\n    private _device: GPUDevice;\r\n    private _bufferManager: WebGPUBufferManager;\r\n\r\n    private _enabled = false;\r\n    private _gpuFrameTimeCounter: PerfCounter = new PerfCounter();\r\n    private _measureDuration: WebGPUDurationMeasure;\r\n    private _measureDurationState = 0;\r\n\r\n    public get gpuFrameTimeCounter() {\r\n        return this._gpuFrameTimeCounter;\r\n    }\r\n\r\n    constructor(engine: WebGPUEngine, device: GPUDevice, bufferManager: WebGPUBufferManager) {\r\n        this._engine = engine;\r\n        this._device = device;\r\n        this._bufferManager = bufferManager;\r\n    }\r\n\r\n    public get enable(): boolean {\r\n        return this._enabled;\r\n    }\r\n\r\n    public set enable(value: boolean) {\r\n        if (this._enabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._enabled = value;\r\n        this._measureDurationState = 0;\r\n        if (value) {\r\n            try {\r\n                this._measureDuration = new WebGPUDurationMeasure(this._engine, this._device, this._bufferManager, 2000, \"QuerySet_TimestampQuery\");\r\n            } catch (e) {\r\n                this._enabled = false;\r\n                Logger.Error(\"Could not create a WebGPUDurationMeasure!\\nError: \" + e.message + \"\\nMake sure timestamp query is supported and enabled in your browser.\");\r\n                return;\r\n            }\r\n        } else {\r\n            this._measureDuration.dispose();\r\n        }\r\n    }\r\n\r\n    public startFrame(commandEncoder: GPUCommandEncoder): void {\r\n        if (this._enabled && this._measureDurationState === 0) {\r\n            this._measureDuration.start(commandEncoder);\r\n            this._measureDurationState = 1;\r\n        }\r\n    }\r\n\r\n    public endFrame(commandEncoder: GPUCommandEncoder): void {\r\n        if (this._measureDurationState === 1) {\r\n            this._measureDurationState = 2;\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n            this._measureDuration.stop(commandEncoder).then((duration) => {\r\n                if (duration !== null && duration >= 0) {\r\n                    this._gpuFrameTimeCounter.fetchNewFrame();\r\n                    this._gpuFrameTimeCounter.addCount(duration, true);\r\n                }\r\n                this._measureDurationState = 0;\r\n            });\r\n        }\r\n    }\r\n\r\n    public startPass(descriptor: GPURenderPassDescriptor | GPUComputePassDescriptor, index: number): void {\r\n        if (this._enabled) {\r\n            this._measureDuration.startPass(descriptor, index);\r\n        } else {\r\n            descriptor.timestampWrites = undefined;\r\n        }\r\n    }\r\n\r\n    public endPass(index: number, gpuPerfCounter?: WebGPUPerfCounter): void {\r\n        if (!this._enabled || !gpuPerfCounter) {\r\n            return;\r\n        }\r\n\r\n        const currentFrameId = this._engine.frameId;\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n        this._measureDuration.stopPass(index).then((duration_) => {\r\n            gpuPerfCounter._addDuration(currentFrameId, duration_ !== null && duration_ > 0 ? duration_ : 0);\r\n        });\r\n    }\r\n\r\n    public dispose() {\r\n        this._measureDuration?.dispose();\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPUDurationMeasure {\r\n    private _querySet: WebGPUQuerySet;\r\n    private _count: number;\r\n\r\n    constructor(engine: WebGPUEngine, device: GPUDevice, bufferManager: WebGPUBufferManager, count = 2, querySetLabel?: string) {\r\n        this._count = count;\r\n        this._querySet = new WebGPUQuerySet(engine, count, WebGPUConstants.QueryType.Timestamp, device, bufferManager, true, querySetLabel);\r\n    }\r\n\r\n    public start(encoder: GPUCommandEncoder): void {\r\n        encoder.writeTimestamp?.(this._querySet.querySet, 0);\r\n    }\r\n\r\n    public async stop(encoder: GPUCommandEncoder): Promise<number | null> {\r\n        encoder.writeTimestamp?.(this._querySet.querySet, 1);\r\n\r\n        return encoder.writeTimestamp ? await this._querySet.readTwoValuesAndSubtract(0) : 0;\r\n    }\r\n\r\n    public startPass(descriptor: GPURenderPassDescriptor | GPUComputePassDescriptor, index: number): void {\r\n        if (index + 3 > this._count) {\r\n            throw new Error(\"WebGPUDurationMeasure: index out of range (\" + index + \")\");\r\n        }\r\n\r\n        descriptor.timestampWrites = {\r\n            querySet: this._querySet.querySet,\r\n            beginningOfPassWriteIndex: index + 2,\r\n            endOfPassWriteIndex: index + 3,\r\n        };\r\n    }\r\n\r\n    public async stopPass(index: number): Promise<number | null> {\r\n        return await this._querySet.readTwoValuesAndSubtract(index + 2);\r\n    }\r\n\r\n    public dispose() {\r\n        this._querySet.dispose();\r\n    }\r\n}\r\n"]}