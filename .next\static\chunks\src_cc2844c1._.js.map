{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { Menu, X } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\ninterface NavItem {\n  href: string\n  label: string\n  ariaLabel: string\n}\n\nconst navItems: NavItem[] = [\n  { href: '#home', label: 'HOME', ariaLabel: 'Navigate to home section' },\n  { href: '#about', label: 'ABOUT', ariaLabel: 'Navigate to about section' },\n  { href: '#skills', label: 'SKILLS', ariaLabel: 'Navigate to skills section' },\n  { href: '#projects', label: 'PROJECTS', ariaLabel: 'Navigate to projects section' },\n  { href: '/blog', label: 'BLOG', ariaLabel: 'Navigate to blog section' },\n  { href: '#playground', label: 'PLAYGROUND', ariaLabel: 'Navigate to code playground section' },\n  { href: '#contact', label: 'CONTACT', ariaLabel: 'Navigate to contact section' },\n]\n\ninterface HeaderProps {\n  className?: string\n}\n\nexport default function Header({ className }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [isScrolled, setIsScrolled] = useState(false)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 100)\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {\n    if (href.startsWith('#')) {\n      e.preventDefault()\n      const target = document.querySelector(href)\n      if (target) {\n        target.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start',\n        })\n        setIsMobileMenuOpen(false)\n      }\n    }\n  }\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 backdrop-blur-md border-b border-cyan-500/30 transition-all duration-300',\n        isScrolled ? 'bg-black/90' : 'bg-black/80',\n        className\n      )}\n    >\n      <nav\n        className=\"container mx-auto px-6 py-4\"\n        role=\"navigation\"\n        aria-label=\"Main navigation\"\n      >\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <div className=\"logo\">\n            <Link\n              href=\"/\"\n              className=\"text-2xl font-bold text-cyan-400 neon-glow glitch focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm font-mono\"\n              aria-label=\"Trinanda - Go to homepage\"\n            >\n              &lt;TRINANDA/&gt;\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <ul className=\"hidden md:flex space-x-8\" role=\"menubar\">\n            {navItems.map((item) => (\n              <li key={item.href} role=\"none\">\n                {item.href.startsWith('#') ? (\n                  <a\n                    href={item.href}\n                    className=\"nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm\"\n                    role=\"menuitem\"\n                    aria-label={item.ariaLabel}\n                    onClick={(e) => handleSmoothScroll(e, item.href)}\n                  >\n                    {item.label}\n                  </a>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className=\"nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm\"\n                    role=\"menuitem\"\n                    aria-label={item.ariaLabel}\n                  >\n                    {item.label}\n                  </Link>\n                )}\n              </li>\n            ))}\n          </ul>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden text-cyan-400 hover:text-pink-400 transition-colors focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm p-1\"\n            aria-label=\"Toggle mobile menu\"\n            aria-expanded={isMobileMenuOpen}\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            type=\"button\"\n          >\n            {isMobileMenuOpen ? (\n              <X className=\"w-6 h-6\" aria-hidden=\"true\" />\n            ) : (\n              <Menu className=\"w-6 h-6\" aria-hidden=\"true\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div\n            className=\"md:hidden mt-4 pb-4\"\n            role=\"menu\"\n            aria-labelledby=\"mobile-menu-btn\"\n          >\n            <ul className=\"space-y-4\">\n              {navItems.map((item) => (\n                <li key={item.href} role=\"none\">\n                  {item.href.startsWith('#') ? (\n                    <a\n                      href={item.href}\n                      className=\"mobile-nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm\"\n                      role=\"menuitem\"\n                      aria-label={item.ariaLabel}\n                      onClick={(e) => handleSmoothScroll(e, item.href)}\n                    >\n                      {item.label}\n                    </a>\n                  ) : (\n                    <Link\n                      href={item.href}\n                      className=\"mobile-nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm\"\n                      role=\"menuitem\"\n                      aria-label={item.ariaLabel}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                    >\n                      {item.label}\n                    </Link>\n                  )}\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n      </nav>\n\n      <style jsx>{`\n        .nav-link {\n          @apply text-gray-300 hover:text-cyan-400 transition-all duration-300 relative;\n          font-family: 'Orbitron', monospace;\n          font-weight: 500;\n          letter-spacing: 1px;\n        }\n\n        .nav-link:hover {\n          text-shadow: 0 0 10px currentColor;\n        }\n\n        .nav-link::after {\n          content: '';\n          position: absolute;\n          bottom: -4px;\n          left: 0;\n          width: 0;\n          height: 2px;\n          background: linear-gradient(90deg, var(--neon-cyan), var(--neon-pink));\n          transition: width 0.3s ease;\n        }\n\n        .nav-link:hover::after {\n          width: 100%;\n        }\n\n        .mobile-nav-link {\n          @apply block text-gray-300 hover:text-cyan-400 transition-colors py-2 border-b border-gray-700/50;\n          font-family: 'Orbitron', monospace;\n          font-weight: 500;\n          letter-spacing: 1px;\n        }\n\n        .mobile-nav-link:hover {\n          text-shadow: 0 0 10px currentColor;\n        }\n\n        .neon-glow {\n          text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;\n        }\n\n        .glitch {\n          position: relative;\n        }\n\n        .glitch:hover::before,\n        .glitch:hover::after {\n          content: attr(data-text);\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n        }\n\n        .glitch:hover::before {\n          animation: glitch-1 0.3s infinite;\n          color: #ff00ff;\n          z-index: -1;\n        }\n\n        .glitch:hover::after {\n          animation: glitch-2 0.3s infinite;\n          color: #00ffff;\n          z-index: -2;\n        }\n\n        @keyframes glitch-1 {\n          0%, 14%, 15%, 49%, 50%, 99%, 100% {\n            transform: translate(0);\n          }\n          15%, 49% {\n            transform: translate(-2px, 0);\n          }\n        }\n\n        @keyframes glitch-2 {\n          0%, 20%, 21%, 62%, 63%, 99%, 100% {\n            transform: translate(0);\n          }\n          21%, 62% {\n            transform: translate(2px, 0);\n          }\n        }\n      `}</style>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;;AAaA,MAAM,WAAsB;IAC1B;QAAE,MAAM;QAAS,OAAO;QAAQ,WAAW;IAA2B;IACtE;QAAE,MAAM;QAAU,OAAO;QAAS,WAAW;IAA4B;IACzE;QAAE,MAAM;QAAW,OAAO;QAAU,WAAW;IAA6B;IAC5E;QAAE,MAAM;QAAa,OAAO;QAAY,WAAW;IAA+B;IAClF;QAAE,MAAM;QAAS,OAAO;QAAQ,WAAW;IAA2B;IACtE;QAAE,MAAM;QAAe,OAAO;QAAc,WAAW;IAAsC;IAC7F;QAAE,MAAM;QAAY,OAAO;QAAW,WAAW;IAA8B;CAChF;AAMc,SAAS,OAAO,KAA0B;QAA1B,EAAE,SAAS,EAAe,GAA1B;;IAC7B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC,GAAwC;QAClE,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,EAAE,cAAc;YAChB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,IAAI,QAAQ;gBACV,OAAO,cAAc,CAAC;oBACpB,UAAU;oBACV,OAAO;gBACT;gBACA,oBAAoB;YACtB;QACF;IACF;IAEA,qBACE,6LAAC;mDACY,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4GACA,aAAa,gBAAgB,eAC7B;;0BAGF,6LAAC;gBAEC,MAAK;gBACL,cAAW;0DAFD;;kCAIV,6LAAC;kEAAc;;0CAEb,6LAAC;0EAAc;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,cAAW;8CACZ;;;;;;;;;;;0CAMH,6LAAC;gCAAwC,MAAK;0EAAhC;0CACX,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;wCAAmB,MAAK;;kDACtB,KAAK,IAAI,CAAC,UAAU,CAAC,qBACpB,6LAAC;4CACC,MAAM,KAAK,IAAI;4CAEf,MAAK;4CACL,cAAY,KAAK,SAAS;4CAC1B,SAAS,CAAC,IAAM,mBAAmB,GAAG,KAAK,IAAI;sFAHrC;sDAKT,KAAK,KAAK;;;;;iEAGb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,MAAK;4CACL,cAAY,KAAK,SAAS;sDAEzB,KAAK,KAAK;;;;;;uCAlBR,KAAK,IAAI;;;;;;;;;;0CA0BtB,6LAAC;gCAEC,cAAW;gCACX,iBAAe;gCACf,SAAS,IAAM,oBAAoB,CAAC;gCACpC,MAAK;0EAJK;0CAMT,iCACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;oCAAU,eAAY;;;;;yDAEnC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;oBAM3C,kCACC,6LAAC;wBAEC,MAAK;wBACL,mBAAgB;kEAFN;kCAIV,cAAA,6LAAC;sEAAa;sCACX,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;oCAAmB,MAAK;;8CACtB,KAAK,IAAI,CAAC,UAAU,CAAC,qBACpB,6LAAC;wCACC,MAAM,KAAK,IAAI;wCAEf,MAAK;wCACL,cAAY,KAAK,SAAS;wCAC1B,SAAS,CAAC,IAAM,mBAAmB,GAAG,KAAK,IAAI;kFAHrC;kDAKT,KAAK,KAAK;;;;;6DAGb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,MAAK;wCACL,cAAY,KAAK,SAAS;wCAC1B,SAAS,IAAM,oBAAoB;kDAElC,KAAK,KAAK;;;;;;mCAnBR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHlC;GA9NwB;KAAA", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/Footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Mail, Linkedin, Instagram, Github } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface FooterProps {\n  className?: string;\n}\n\nconst quickLinks = [\n  { href: \"#about\", label: \"About Me\" },\n  { href: \"#skills\", label: \"Skills\" },\n  { href: \"#projects\", label: \"Projects\" },\n  { href: \"#contact\", label: \"Contact\" },\n];\n\nconst expertise = [\n  { label: \"Accounting & Finance\", color: \"bg-cyan-400\" },\n  { label: \"Graphic Design\", color: \"bg-pink-400\" },\n  { label: \"Data Analysis\", color: \"bg-green-400\" },\n  { label: \"Web Development\", color: \"bg-purple-400\" },\n];\n\nconst socialLinks = [\n  {\n    href: \"mailto:<EMAIL>\",\n    icon: Mail,\n    label: \"Email\",\n  },\n  {\n    href: \"https://www.linkedin.com/in/muhammad-trinanda/\",\n    icon: Linkedin,\n    label: \"LinkedIn\",\n  },\n  {\n    href: \"https://www.instagram.com/trinanda321\",\n    icon: Instagram,\n    label: \"Instagram\",\n  },\n  {\n    href: \"https://github.com/trinanda\",\n    icon: Github,\n    label: \"GitHub\",\n  },\n];\n\nexport default function Footer({ className }: FooterProps) {\n  const handleSmoothScroll = (\n    e: React.MouseEvent<HTMLAnchorElement>,\n    href: string\n  ) => {\n    if (href.startsWith(\"#\")) {\n      e.preventDefault();\n      const target = document.querySelector(href);\n      if (target) {\n        target.scrollIntoView({\n          behavior: \"smooth\",\n          block: \"start\",\n        });\n      }\n    }\n  };\n\n  return (\n    <footer\n      className={cn(\n        \"bg-black/90 border-t border-cyan-500/30 mt-0 mb-0\",\n        className\n      )}\n      style={{\n        marginTop: \"0 !important\",\n        marginBottom: \"0 !important\",\n        paddingBottom: \"0 !important\",\n      }}\n    >\n      <div className=\"container mx-auto px-6 py-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Brand Section */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-2xl font-bold text-cyan-400 neon-glow font-mono\">\n              &lt;TRINANDA/&gt;\n            </h3>\n            <p className=\"text-gray-400 leading-relaxed\">\n              Final-Semester Student of Sharia Accounting, UINSU | Accountant,\n              Graphic Designer, and Business Data Analyst\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <a\n                    key={social.label}\n                    href={social.href}\n                    target={\n                      social.href.startsWith(\"mailto:\") ? undefined : \"_blank\"\n                    }\n                    rel={\n                      social.href.startsWith(\"mailto:\")\n                        ? undefined\n                        : \"noopener noreferrer\"\n                    }\n                    className=\"social-link\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"w-5 h-5\" />\n                  </a>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <h4 className=\"text-lg font-semibold text-pink-400 neon-glow\">\n              Quick Links\n            </h4>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.href}>\n                  <a\n                    href={link.href}\n                    className=\"footer-link\"\n                    onClick={(e) => handleSmoothScroll(e, link.href)}\n                  >\n                    {link.label}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Skills */}\n          <div className=\"space-y-4\">\n            <h4 className=\"text-lg font-semibold text-green-400 neon-glow\">\n              Expertise\n            </h4>\n            <ul className=\"space-y-2 text-gray-400\">\n              {expertise.map((skill, index) => (\n                <li key={index} className=\"flex items-center space-x-2\">\n                  <span\n                    className={cn(\n                      \"w-2 h-2 rounded-full pulse-neon\",\n                      skill.color\n                    )}\n                  />\n                  <span>{skill.label}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-700/50 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; 2025 Muhammad Trinanda. All rights reserved. |{\" \"}\n            <span className=\"text-cyan-400 neon-glow\">Powered by Next.js</span>\n          </p>\n          <p className=\"text-sm text-gray-500 mt-2\">\n            &quot;The future belongs to those who believe in the beauty of their\n            dreams.&quot;\n          </p>\n        </div>\n      </div>\n\n      <style jsx>{`\n        footer {\n          margin-bottom: 0 !important;\n          padding-bottom: 0 !important;\n          margin-top: 0 !important;\n          position: relative;\n          border-top: 1px solid rgba(6, 182, 212, 0.3) !important;\n        }\n\n        footer::after {\n          content: \"\";\n          display: block;\n          height: 0;\n          margin: 0;\n          padding: 0;\n          clear: both;\n        }\n\n        footer::before {\n          content: \"\";\n          display: block;\n          height: 0;\n          margin: 0;\n          padding: 0;\n        }\n\n        #playground + footer,\n        section:last-of-type + footer {\n          margin-top: 0 !important;\n          padding-top: 2rem !important;\n        }\n\n        .social-link {\n          @apply text-gray-400 hover:text-cyan-400 transition-all duration-300 p-2 rounded-lg;\n          background: rgba(0, 255, 255, 0.1);\n          border: 1px solid rgba(0, 255, 255, 0.2);\n        }\n\n        .social-link:hover {\n          background: rgba(0, 255, 255, 0.2);\n          box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);\n          transform: translateY(-2px);\n        }\n\n        .footer-link {\n          @apply text-gray-400 hover:text-cyan-400 transition-colors duration-300;\n          font-family: \"Orbitron\", monospace;\n        }\n\n        .footer-link:hover {\n          text-shadow: 0 0 10px currentColor;\n        }\n\n        .neon-glow {\n          text-shadow:\n            0 0 5px currentColor,\n            0 0 10px currentColor,\n            0 0 15px currentColor;\n        }\n\n        .pulse-neon {\n          animation: pulse-neon 2s infinite;\n        }\n\n        @keyframes pulse-neon {\n          0%,\n          100% {\n            opacity: 1;\n            box-shadow: 0 0 5px currentColor;\n          }\n          50% {\n            opacity: 0.5;\n            box-shadow:\n              0 0 10px currentColor,\n              0 0 15px currentColor;\n          }\n        }\n      `}</style>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AACA;AAHA;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAU,OAAO;IAAW;IACpC;QAAE,MAAM;QAAW,OAAO;IAAS;IACnC;QAAE,MAAM;QAAa,OAAO;IAAW;IACvC;QAAE,MAAM;QAAY,OAAO;IAAU;CACtC;AAED,MAAM,YAAY;IAChB;QAAE,OAAO;QAAwB,OAAO;IAAc;IACtD;QAAE,OAAO;QAAkB,OAAO;IAAc;IAChD;QAAE,OAAO;QAAiB,OAAO;IAAe;IAChD;QAAE,OAAO;QAAmB,OAAO;IAAgB;CACpD;AAED,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,+MAAA,CAAA,YAAS;QACf,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;IACT;CACD;AAEc,SAAS,OAAO,KAA0B;QAA1B,EAAE,SAAS,EAAe,GAA1B;IAC7B,MAAM,qBAAqB,CACzB,GACA;QAEA,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,EAAE,cAAc;YAChB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,IAAI,QAAQ;gBACV,OAAO,cAAc,CAAC;oBACpB,UAAU;oBACV,OAAO;gBACT;YACF;QACF;IACF;IAEA,qBACE,6LAAC;QAKC,OAAO;YACL,WAAW;YACX,cAAc;YACd,eAAe;QACjB;mDARW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;;0BAQF,6LAAC;0DAAc;;kCACb,6LAAC;kEAAc;;0CAEb,6LAAC;0EAAc;;kDACb,6LAAC;kFAAa;kDAAuD;;;;;;kDAGrE,6LAAC;kFAAY;kDAAgC;;;;;;kDAI7C,6LAAC;kFAAc;kDACZ,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,OAAO,OAAO,IAAI;4CACxB,qBACE,6LAAC;gDAEC,MAAM,OAAO,IAAI;gDACjB,QACE,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,YAAY;gDAElD,KACE,OAAO,IAAI,CAAC,UAAU,CAAC,aACnB,YACA;gDAGN,cAAY,OAAO,KAAK;0FADd;0DAGV,cAAA,6LAAC;8FAAe;;;;;;+CAbX,OAAO,KAAK;;;;;wCAgBvB;;;;;;;;;;;;0CAKJ,6LAAC;0EAAc;;kDACb,6LAAC;kFAAa;kDAAgD;;;;;;kDAG9D,6LAAC;kFAAa;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;;0DACC,cAAA,6LAAC;oDACC,MAAM,KAAK,IAAI;oDAEf,SAAS,CAAC,IAAM,mBAAmB,GAAG,KAAK,IAAI;8FADrC;8DAGT,KAAK,KAAK;;;;;;+CANN,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,6LAAC;0EAAc;;kDACb,6LAAC;kFAAa;kDAAiD;;;;;;kDAG/D,6LAAC;kFAAa;kDACX,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,6LAAC;0FAAyB;;kEACxB,6LAAC;mGACY,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mCACA,MAAM,KAAK;;;;;;kEAGf,6LAAC;;kEAAM,MAAM,KAAK;;;;;;;+CAPX;;;;;;;;;;;;;;;;;;;;;;kCAejB,6LAAC;kEAAc;;0CACb,6LAAC;0EAAY;;oCAAgB;oCAC2B;kDACtD,6LAAC;kFAAe;kDAA0B;;;;;;;;;;;;0CAE5C,6LAAC;0EAAY;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFpD;KAvMwB", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/BabylonBackground.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface BabylonParticleSystemInstance {\n  particleSystem?: {\n    emitRate: number;\n  };\n  engine?: {\n    resize: () => void;\n  };\n  setColorScheme: (scheme: string) => void;\n  destroy: () => void;\n}\n\nexport interface BabylonBackgroundProps {\n  particleCount?: number;\n  enableInteraction?: boolean;\n  colorScheme?: \"cyberpunk\" | \"matrix\" | \"neon\";\n  className?: string;\n}\n\nexport default function BabylonBackground({\n  particleCount = 1000,\n  enableInteraction = true,\n  colorScheme = \"cyberpunk\",\n  className,\n}: BabylonBackgroundProps) {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const particleSystemRef = useRef<BabylonParticleSystemInstance | null>(null);\n\n  useEffect(() => {\n    let mounted = true;\n\n    const initializeBabylon = async () => {\n      try {\n        // Dynamic import to avoid SSR issues\n        const { default: BabylonParticleSystem } = await import(\n          \"@/scripts/babylon-particles\"\n        );\n\n        if (!mounted || !containerRef.current || !canvasRef.current) return;\n\n        // Initialize particle system\n        const particles = new BabylonParticleSystem(\n          canvasRef.current\n        ) as BabylonParticleSystemInstance;\n        particleSystemRef.current = particles;\n\n        // Configure particle system\n        if (particles.particleSystem) {\n          particles.particleSystem.emitRate = Math.max(50, particleCount / 20);\n        }\n\n        // Set color scheme\n        particles.setColorScheme(colorScheme);\n\n        // Enable interaction if requested (simplified for now)\n        if (enableInteraction) {\n          console.log(\"Interaction enabled for particle system\");\n        }\n      } catch (error) {\n        console.error(\"Error loading Babylon.js particle system:\", error);\n      }\n    };\n\n    initializeBabylon();\n\n    return () => {\n      mounted = false;\n      if (particleSystemRef.current) {\n        particleSystemRef.current.destroy();\n        particleSystemRef.current = null;\n      }\n    };\n  }, [particleCount, enableInteraction, colorScheme]);\n\n  // Handle window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (particleSystemRef.current && particleSystemRef.current.engine) {\n        particleSystemRef.current.engine.resize();\n      }\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  return (\n    <div\n      ref={containerRef}\n      className={cn(\"fixed inset-0 -z-10 pointer-events-none\", className)}\n    >\n      <canvas\n        ref={canvasRef}\n        className=\"w-full h-full block\"\n        style={{ touchAction: \"none\" }}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAuBe,SAAS,kBAAkB,KAKjB;QALiB,EACxC,gBAAgB,IAAI,EACpB,oBAAoB,IAAI,EACxB,cAAc,WAAW,EACzB,SAAS,EACc,GALiB;;IAMxC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwC;IAEvE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,UAAU;YAEd,MAAM;iEAAoB;oBACxB,IAAI;wBACF,qCAAqC;wBACrC,MAAM,EAAE,SAAS,qBAAqB,EAAE,GAAG;wBAI3C,IAAI,CAAC,WAAW,CAAC,aAAa,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;wBAE7D,6BAA6B;wBAC7B,MAAM,YAAY,IAAI,sBACpB,UAAU,OAAO;wBAEnB,kBAAkB,OAAO,GAAG;wBAE5B,4BAA4B;wBAC5B,IAAI,UAAU,cAAc,EAAE;4BAC5B,UAAU,cAAc,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,IAAI,gBAAgB;wBACnE;wBAEA,mBAAmB;wBACnB,UAAU,cAAc,CAAC;wBAEzB,uDAAuD;wBACvD,IAAI,mBAAmB;4BACrB,QAAQ,GAAG,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,6CAA6C;oBAC7D;gBACF;;YAEA;YAEA;+CAAO;oBACL,UAAU;oBACV,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,kBAAkB,OAAO,CAAC,OAAO;wBACjC,kBAAkB,OAAO,GAAG;oBAC9B;gBACF;;QACF;sCAAG;QAAC;QAAe;QAAmB;KAAY;IAElD,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;4DAAe;oBACnB,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,OAAO,CAAC,MAAM,EAAE;wBACjE,kBAAkB,OAAO,CAAC,MAAM,CAAC,MAAM;oBACzC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;+CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;sCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;kBAEzD,cAAA,6LAAC;YACC,KAAK;YACL,WAAU;YACV,OAAO;gBAAE,aAAa;YAAO;;;;;;;;;;;AAIrC;GAhFwB;KAAA", "debugId": null}}, {"offset": {"line": 713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ProjectCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { ExternalLink, Github, Eye } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { Card, CardContent } from '@/components/ui/card'\n\ninterface ProjectData {\n  id: string\n  title: string\n  description: string\n  image?: string\n  category: 'dashboard' | 'design' | 'analysis' | 'web' | 'accounting' | 'visualization'\n  tags: string[]\n  links: {\n    demo?: string\n    github?: string\n    live?: string\n  }\n}\n\ninterface ProjectCardProps {\n  project: ProjectData\n  index?: number\n  className?: string\n}\n\n// Category colors for projects\nconst categoryColors = {\n  dashboard: 'from-blue-500 to-cyan-500',\n  design: 'from-pink-500 to-rose-500',\n  analysis: 'from-green-500 to-emerald-500',\n  web: 'from-purple-500 to-violet-500',\n  accounting: 'from-yellow-500 to-orange-500',\n  visualization: 'from-indigo-500 to-blue-500'\n} as const\n\nconst categoryEmojis = {\n  dashboard: '📊',\n  design: '🎨',\n  analysis: '📈',\n  web: '💻',\n  accounting: '💰',\n  visualization: '📉'\n} as const\n\nexport default function ProjectCard({ \n  project, \n  index = 0, \n  className \n}: ProjectCardProps) {\n  const [imageError, setImageError] = useState(false)\n  const gradientClass = categoryColors[project.category] || categoryColors.dashboard\n  const emoji = categoryEmojis[project.category] || categoryEmojis.dashboard\n\n  return (\n    <Card \n      className={cn(\n        'project-card group bg-gray-900/50 backdrop-blur-sm border border-cyan-500/30 rounded-xl overflow-hidden hover:border-cyan-400/60 transition-all duration-300 hover:shadow-xl hover:shadow-cyan-400/20 hover:-translate-y-2',\n        className\n      )}\n      style={{ animationDelay: `${index * 0.2}s` }}\n    >\n      {/* Project Image */}\n      <div className=\"relative overflow-hidden h-48 bg-gradient-to-br from-gray-800 to-gray-900\">\n        {project.image && !imageError ? (\n          <Image\n            src={project.image}\n            alt={`${project.title} project screenshot`}\n            fill\n            className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\n            onError={() => setImageError(true)}\n          />\n        ) : (\n          <div className={cn('w-full h-full bg-gradient-to-br flex items-center justify-center', gradientClass)}>\n            <div className=\"text-6xl opacity-20\">\n              {emoji}\n            </div>\n          </div>\n        )}\n        \n        {/* Category Badge */}\n        <div className=\"absolute top-4 left-4\">\n          <span className={cn('px-3 py-1 text-xs font-semibold text-white bg-gradient-to-r rounded-full shadow-lg', gradientClass)}>\n            {project.category.toUpperCase()}\n          </span>\n        </div>\n      </div>\n      \n      {/* Project Content */}\n      <CardContent className=\"p-6 space-y-4\">\n        <header>\n          <h3 \n            id={`project-${project.id}`}\n            className=\"text-xl font-bold text-white group-hover:text-cyan-400 transition-colors\"\n          >\n            {project.title}\n          </h3>\n        </header>\n        \n        <p className=\"text-gray-300 text-sm leading-relaxed line-clamp-3\">\n          {project.description}\n        </p>\n        \n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2\" role=\"list\" aria-label=\"Project technologies\">\n          {project.tags.map((tag) => (\n            <span \n              key={tag}\n              className=\"px-2 py-1 text-xs bg-cyan-400/10 text-cyan-400 rounded border border-cyan-400/20 hover:bg-cyan-400/20 transition-colors\"\n              role=\"listitem\"\n            >\n              {tag}\n            </span>\n          ))}\n        </div>\n        \n        {/* Action Links */}\n        <div className=\"flex gap-3 pt-2\" role=\"group\" aria-label=\"Project links\">\n          {project.links.demo && (\n            <Link \n              href={project.links.demo}\n              className=\"flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium text-center hover:from-cyan-400 hover:to-blue-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-gray-900 flex items-center justify-center gap-2\"\n              aria-label={`View ${project.title} demo`}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <Eye className=\"w-4 h-4\" />\n              View Demo\n            </Link>\n          )}\n          \n          {project.links.github && (\n            <Link \n              href={project.links.github}\n              className=\"px-4 py-2 border border-gray-600 text-gray-300 rounded-lg text-sm font-medium hover:border-gray-500 hover:text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-gray-900 flex items-center gap-2\"\n              aria-label={`View ${project.title} source code on GitHub`}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <Github className=\"w-4 h-4\" />\n              GitHub\n            </Link>\n          )}\n          \n          {project.links.live && (\n            <Link \n              href={project.links.live}\n              className=\"px-4 py-2 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-lg text-sm font-medium hover:from-pink-400 hover:to-rose-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-pink-400 focus:ring-offset-2 focus:ring-offset-gray-900 flex items-center gap-2\"\n              aria-label={`Visit live ${project.title} website`}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <ExternalLink className=\"w-4 h-4\" />\n              Live Site\n            </Link>\n          )}\n        </div>\n      </CardContent>\n\n      <style jsx>{`\n        .project-card {\n          opacity: 0;\n          transform: translateY(30px);\n          animation: fadeInUp 0.8s ease-out forwards;\n        }\n        \n        @keyframes fadeInUp {\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n        \n        .line-clamp-3 {\n          display: -webkit-box;\n          -webkit-line-clamp: 3;\n          -webkit-box-orient: vertical;\n          overflow: hidden;\n        }\n      `}</style>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AAPA;;;;;;;;AA6BA,+BAA+B;AAC/B,MAAM,iBAAiB;IACrB,WAAW;IACX,QAAQ;IACR,UAAU;IACV,KAAK;IACL,YAAY;IACZ,eAAe;AACjB;AAEA,MAAM,iBAAiB;IACrB,WAAW;IACX,QAAQ;IACR,UAAU;IACV,KAAK;IACL,YAAY;IACZ,eAAe;AACjB;AAEe,SAAS,YAAY,KAIjB;QAJiB,EAClC,OAAO,EACP,QAAQ,CAAC,EACT,SAAS,EACQ,GAJiB;;IAKlC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,gBAAgB,cAAc,CAAC,QAAQ,QAAQ,CAAC,IAAI,eAAe,SAAS;IAClF,MAAM,QAAQ,cAAc,CAAC,QAAQ,QAAQ,CAAC,IAAI,eAAe,SAAS;IAE1E,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8NACA;QAEF,OAAO;YAAE,gBAAgB,AAAC,GAAc,OAAZ,QAAQ,KAAI;QAAG;;0BAG3C,6LAAC;0DAAc;;oBACZ,QAAQ,KAAK,IAAI,CAAC,2BACjB,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,AAAC,GAAgB,OAAd,QAAQ,KAAK,EAAC;wBACtB,IAAI;wBACJ,WAAU;wBACV,SAAS,IAAM,cAAc;;;;;6CAG/B,6LAAC;mEAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;kCACrF,cAAA,6LAAC;sEAAc;sCACZ;;;;;;;;;;;kCAMP,6LAAC;kEAAc;kCACb,cAAA,6LAAC;uEAAgB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sFAAsF;sCACvG,QAAQ,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;0BAMnC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;;kCACC,cAAA,6LAAC;4BACC,IAAI,AAAC,WAAqB,OAAX,QAAQ,EAAE;sEACf;sCAET,QAAQ,KAAK;;;;;;;;;;;kCAIlB,6LAAC;kEAAY;kCACV,QAAQ,WAAW;;;;;;kCAItB,6LAAC;wBAAqC,MAAK;wBAAO,cAAW;kEAA9C;kCACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,oBACjB,6LAAC;gCAGC,MAAK;0EADK;0CAGT;+BAJI;;;;;;;;;;kCAUX,6LAAC;wBAAgC,MAAK;wBAAQ,cAAW;kEAA1C;;4BACZ,QAAQ,KAAK,CAAC,IAAI,kBACjB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,QAAQ,KAAK,CAAC,IAAI;gCACxB,WAAU;gCACV,cAAY,AAAC,QAAqB,OAAd,QAAQ,KAAK,EAAC;gCAClC,QAAO;gCACP,KAAI;;kDAEJ,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAK9B,QAAQ,KAAK,CAAC,MAAM,kBACnB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,QAAQ,KAAK,CAAC,MAAM;gCAC1B,WAAU;gCACV,cAAY,AAAC,QAAqB,OAAd,QAAQ,KAAK,EAAC;gCAClC,QAAO;gCACP,KAAI;;kDAEJ,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAKjC,QAAQ,KAAK,CAAC,IAAI,kBACjB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,QAAQ,KAAK,CAAC,IAAI;gCACxB,WAAU;gCACV,cAAY,AAAC,cAA2B,OAAd,QAAQ,KAAK,EAAC;gCACxC,QAAO;gCACP,KAAI;;kDAEJ,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BlD;GAzIwB;KAAA", "debugId": null}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/SkillCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useState } from 'react'\nimport { cn } from '@/lib/utils'\nimport { Card, CardContent } from '@/components/ui/card'\n\ninterface SkillData {\n  name: string\n  level: number\n  category: 'accounting' | 'design' | 'data' | 'web' | 'tools' | 'soft'\n}\n\ninterface SkillCardProps {\n  skill: SkillData\n  index?: number\n  className?: string\n}\n\n// Category colors mapping\nconst categoryColors = {\n  accounting: 'from-green-400 to-emerald-600',\n  design: 'from-pink-400 to-rose-600', \n  data: 'from-blue-400 to-indigo-600',\n  web: 'from-cyan-400 to-teal-600',\n  tools: 'from-yellow-400 to-orange-600',\n  soft: 'from-purple-400 to-violet-600'\n} as const\n\nconst categoryIcons = {\n  accounting: '💰',\n  design: '🎨',\n  data: '📊',\n  web: '💻',\n  tools: '🛠️',\n  soft: '🤝'\n} as const\n\nexport default function SkillCard({ \n  skill, \n  index = 0, \n  className \n}: SkillCardProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [progressWidth, setProgressWidth] = useState(0)\n  const cardRef = useRef<HTMLDivElement>(null)\n  \n  const gradientClass = categoryColors[skill.category] || categoryColors.data\n  const icon = categoryIcons[skill.category] || categoryIcons.data\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            setIsVisible(true)\n            // Animate progress bar after a short delay\n            setTimeout(() => {\n              setProgressWidth(skill.level)\n            }, 200)\n            observer.unobserve(entry.target)\n          }\n        })\n      },\n      {\n        threshold: 0.3,\n        rootMargin: '0px 0px -50px 0px'\n      }\n    )\n\n    if (cardRef.current) {\n      observer.observe(cardRef.current)\n    }\n\n    return () => {\n      if (cardRef.current) {\n        observer.unobserve(cardRef.current)\n      }\n    }\n  }, [skill.level])\n\n  return (\n    <Card \n      ref={cardRef}\n      className={cn(\n        'skill-card bg-gray-900/50 backdrop-blur-sm border border-cyan-500/30 rounded-lg hover:border-cyan-400/60 transition-all duration-300 hover:shadow-lg hover:shadow-cyan-400/20',\n        isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-5',\n        className\n      )}\n      style={{ animationDelay: `${index * 0.1}s` }}\n      role=\"article\"\n      aria-labelledby={`skill-${skill.name.replace(/\\s+/g, '-').toLowerCase()}`}\n    >\n      <CardContent className=\"p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center gap-3\">\n            <span \n              className=\"text-2xl\" \n              role=\"img\" \n              aria-label={`${skill.category} category`}\n            >\n              {icon}\n            </span>\n            <h3 \n              id={`skill-${skill.name.replace(/\\s+/g, '-').toLowerCase()}`}\n              className=\"text-lg font-semibold text-white\"\n            >\n              {skill.name}\n            </h3>\n          </div>\n          <span \n            className=\"text-sm font-mono text-cyan-400 bg-cyan-400/10 px-2 py-1 rounded\"\n            aria-label={`Skill level: ${skill.level} percent`}\n          >\n            {skill.level}%\n          </span>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between text-sm text-gray-400\">\n            <span>Progress</span>\n            <span>{skill.level}%</span>\n          </div>\n          <div \n            className=\"w-full bg-gray-700 rounded-full h-2 overflow-hidden\"\n            role=\"progressbar\"\n            aria-valuenow={skill.level}\n            aria-valuemin={0}\n            aria-valuemax={100}\n            aria-label={`${skill.name} skill progress: ${skill.level}%`}\n          >\n            <div \n              className={cn(\n                'h-full bg-gradient-to-r rounded-full transition-all duration-1000 ease-out',\n                gradientClass\n              )}\n              style={{ width: `${progressWidth}%` }}\n            />\n          </div>\n        </div>\n      </CardContent>\n\n      <style jsx>{`\n        .skill-card {\n          opacity: 0;\n          transform: translateY(20px);\n        }\n        \n        .animate-fade-in-up {\n          animation: fadeInUp 0.6s ease-out forwards;\n        }\n        \n        @keyframes fadeInUp {\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n      `}</style>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;;AAkBA,0BAA0B;AAC1B,MAAM,iBAAiB;IACrB,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,KAAK;IACL,OAAO;IACP,MAAM;AACR;AAEA,MAAM,gBAAgB;IACpB,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,KAAK;IACL,OAAO;IACP,MAAM;AACR;AAEe,SAAS,UAAU,KAIjB;QAJiB,EAChC,KAAK,EACL,QAAQ,CAAC,EACT,SAAS,EACM,GAJiB;;IAKhC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,MAAM,gBAAgB,cAAc,CAAC,MAAM,QAAQ,CAAC,IAAI,eAAe,IAAI;IAC3E,MAAM,OAAO,aAAa,CAAC,MAAM,QAAQ,CAAC,IAAI,cAAc,IAAI;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,WAAW,IAAI;uCACnB,CAAC;oBACC,QAAQ,OAAO;+CAAC,CAAC;4BACf,IAAI,MAAM,cAAc,EAAE;gCACxB,aAAa;gCACb,2CAA2C;gCAC3C;2DAAW;wCACT,iBAAiB,MAAM,KAAK;oCAC9B;0DAAG;gCACH,SAAS,SAAS,CAAC,MAAM,MAAM;4BACjC;wBACF;;gBACF;sCACA;gBACE,WAAW;gBACX,YAAY;YACd;YAGF,IAAI,QAAQ,OAAO,EAAE;gBACnB,SAAS,OAAO,CAAC,QAAQ,OAAO;YAClC;YAEA;uCAAO;oBACL,IAAI,QAAQ,OAAO,EAAE;wBACnB,SAAS,SAAS,CAAC,QAAQ,OAAO;oBACpC;gBACF;;QACF;8BAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iLACA,YAAY,uBAAuB,2BACnC;QAEF,OAAO;YAAE,gBAAgB,AAAC,GAAc,OAAZ,QAAQ,KAAI;QAAG;QAC3C,MAAK;QACL,mBAAiB,AAAC,SAAsD,OAA9C,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW;;0BAErE,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;kDACb,6LAAC;wCAEC,MAAK;wCACL,cAAY,AAAC,GAAiB,OAAf,MAAM,QAAQ,EAAC;kFAFpB;kDAIT;;;;;;kDAEH,6LAAC;wCACC,IAAI,AAAC,SAAsD,OAA9C,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW;kFAC9C;kDAET,MAAM,IAAI;;;;;;;;;;;;0CAGf,6LAAC;gCAEC,cAAY,AAAC,gBAA2B,OAAZ,MAAM,KAAK,EAAC;0EAD9B;;oCAGT,MAAM,KAAK;oCAAC;;;;;;;;;;;;;kCAIjB,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;kDACb,6LAAC;;kDAAK;;;;;;kDACN,6LAAC;;;4CAAM,MAAM,KAAK;4CAAC;;;;;;;;;;;;;0CAErB,6LAAC;gCAEC,MAAK;gCACL,iBAAe,MAAM,KAAK;gCAC1B,iBAAe;gCACf,iBAAe;gCACf,cAAY,AAAC,GAAgC,OAA9B,MAAM,IAAI,EAAC,qBAA+B,OAAZ,MAAM,KAAK,EAAC;0EAL/C;0CAOV,cAAA,6LAAC;oCAKC,OAAO;wCAAE,OAAO,AAAC,GAAgB,OAAd,eAAc;oCAAG;+EAJzB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8EACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BhB;GA3HwB;KAAA", "debugId": null}}]}