{"version": 3, "file": "transformFeedbackBoundingHelper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Culling/Helper/transformFeedbackBoundingHelper.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,gCAA4B;AAE3D,OAAO,EAAE,SAAS,EAAE,mCAA+B;AAGnD,OAAO,EACH,mBAAmB,EACnB,yBAAyB,EACzB,wCAAwC,EACxC,0CAA0C,GAC7C,oDAAgD;AAGjD,OAAO,EAAE,gBAAgB,EAAE,sCAAkC;AAC7D,OAAO,EAAE,OAAO,EAAE,mCAA+B;AAEjD,OAAO,mCAAmC,CAAC;AAC3C,OAAO,qCAAqC,CAAC;AAE7C,gBAAgB;AAChB,MAAM,OAAO,+BAA+B;IAUxC;;;OAGG;IACH,YAAY,MAAkB;QATtB,aAAQ,GAA8B,EAAE,CAAC;QACzC,aAAQ,GAA8B,EAAE,CAAC;QAEzC,qBAAgB,GAAG,CAAC,CAAC;QAOzB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,gBAAgB;IAChB,2FAA2F;IACpF,YAAY,CAAC,MAAqC;QACrD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAE9B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAEO,gBAAgB,CAAC,MAAsB;QAC3C,MAAM,qBAAqB,GAAG,IAAI,CAAC,OAAQ,CAAC,OAAO,EAAE,CAAC,qBAAqB,CAAC;QAE5E,IAAI,CAAC,OAAQ,CAAC,OAAO,EAAE,CAAC,qBAAqB,GAAG,SAAS,CAAC;QAE1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACvB,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE5C,IAAI,WAAW,KAAK,CAAC,IAAI,CAAE,IAAa,CAAC,eAAe,IAAI,CAAE,IAAa,CAAC,eAAe,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;gBACrH,SAAS;YACb,CAAC;YAED,qBAAqB;YACrB,IAAI,aAAqB,CAAC;YAC1B,MAAM,OAAO,GAAa,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;YAE5C,QAAQ;YACR,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC1E,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;oBAC9B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;oBACpD,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;gBACxD,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACxE,OAAO,CAAC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;gBAC/E,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7E,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACnD,CAAC;YAED,QAAQ;YACR,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB;gBAC/C,CAAC,CAAC,0CAA0C,CACtC,IAAI,CAAC,kBAAkB,EACvB,OAAO,EACP,OAAO,EACP,IAAI,EACJ,IAAI,EAAE,mBAAmB;gBACzB,KAAK,EAAE,iBAAiB;gBACxB,KAAK,EAAE,kBAAkB;gBACzB,KAAK,EAAE,aAAa;gBACpB,KAAK,EAAE,cAAc;gBACrB,KAAK,CAAC,gBAAgB;iBACzB;gBACH,CAAC,CAAC,CAAC,CAAC;YAER,yBAAyB;YACzB,MAAM,UAAU,GAAU,IAAK,CAAC,2BAA2B,CAAC;YAC5D,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;gBACrC,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACvD,wCAAwC,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,MAAM,QAAQ,GAAG;oBACb,kBAAkB;oBAClB,QAAQ;oBACR,uBAAuB;oBACvB,kBAAkB;oBAClB,wBAAwB;oBACxB,2BAA2B;oBAC3B,8BAA8B;oBAC9B,yCAAyC;oBACzC,0BAA0B;iBAC7B,CAAC;gBACF,MAAM,QAAQ,GAAG,CAAC,aAAa,EAAE,cAAc,EAAE,6BAA6B,CAAC,CAAC;gBAEhF,MAAM,oBAAoB,GAAG;oBACzB,UAAU,EAAE,OAAO;oBACnB,aAAa,EAAE,QAAQ;oBACvB,mBAAmB,EAAE,EAAE;oBACvB,QAAQ,EAAE,QAAQ;oBAClB,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,IAAI;oBACf,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,IAAI;oBACb,eAAe,EAAE,EAAE,2BAA2B,EAAE,mBAAmB,EAAE;oBACrE,qBAAqB,EAAE,CAAC;oBACxB,yBAAyB,EAAE,CAAC,aAAa,CAAC;iBAC7C,CAAC;gBACF,aAAa,GAAG,IAAI,CAAC,OAAQ,CAAC,YAAY,CAAC,cAAc,EAAE,oBAAoB,EAAE,IAAI,CAAC,OAAQ,CAAC,CAAC;gBAChG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACJ,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,OAAQ,CAAC,OAAO,EAAE,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;IAC1E,CAAC;IAEO,QAAQ,CAAC,IAAkB,EAAE,MAAc;QAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAiB,CAAC;QAEtC,SAAS;QACT,IAAI,YAAoB,CAAC;QACzB,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAE5C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;YACrD,YAAY,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YACjE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC;QAChD,CAAC;aAAM,CAAC;YACJ,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC;QAED,OAAO;QACP,MAAM,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACvC,IAAa,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAE/C,QAAQ;QACR,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAElC,gBAAgB;QAChB,yBAAyB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACxC,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;YAC9E,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;QAED,MAAM;QACN,MAAM,UAAU,GAAU,IAAK,CAAC,2BAA2B,CAAC;QAE5D,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YACrC,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS;QACT,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,EAAmB,CAAC;QAC5D,MAAM,CAAC,2BAA2B,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7D,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACjC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;QACxE,MAAM,CAAC,oBAAoB,EAAE,CAAC;QAC9B,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAChC,MAAM,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;QAEzC,cAAc;QACd,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACJ,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC;YAC9C,MAAM,MAAM,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;YAE7D,+BAA+B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC1F,+BAA+B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE1F,IAAI,CAAC,0BAA0B,CAAC,EAAE,OAAO,EAAE,+BAA+B,CAAC,IAAI,EAAE,OAAO,EAAE,+BAA+B,CAAC,IAAI,EAAE,CAAC,CAAC;QACtI,CAAC;IACL,CAAC;IAED,gBAAgB;IAChB,2FAA2F;IACpF,qBAAqB,CAAC,MAAqC;QAC9D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACtC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED,gBAAgB;IAChB,2FAA2F;IACpF,4BAA4B;QAC/B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAE1B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;;AAlOc,oCAAI,GAAG,IAAI,OAAO,EAAE,AAAhB,CAAiB;AACrB,oCAAI,GAAG,IAAI,OAAO,EAAE,AAAhB,CAAiB", "sourcesContent": ["import type { Effect } from \"core/Materials/effect\";\r\nimport type { ThinEngine } from \"core/Engines/thinEngine\";\r\nimport { VertexBuffer, Buffer } from \"core/Buffers/buffer\";\r\nimport type { Engine } from \"core/Engines/engine\";\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport {\r\n    BindBonesParameters,\r\n    BindMorphTargetParameters,\r\n    PrepareAttributesForBakedVertexAnimation,\r\n    PrepareDefinesAndAttributesForMorphTargets,\r\n} from \"core/Materials/materialHelper.functions\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { IBoundingInfoHelperPlatform } from \"./IBoundingInfoHelperPlatform\";\r\nimport { extractMinAndMax } from \"core/Maths/math.functions\";\r\nimport { Vector3 } from \"core/Maths/math.vector\";\r\n\r\nimport \"../../Shaders/gpuTransform.vertex\";\r\nimport \"../../Shaders/gpuTransform.fragment\";\r\n\r\n/** @internal */\r\nexport class TransformFeedbackBoundingHelper implements IBoundingInfoHelperPlatform {\r\n    private static _Min = new Vector3();\r\n    private static _Max = new Vector3();\r\n\r\n    private _engine: Nullable<ThinEngine>;\r\n    private _buffers: { [key: number]: Buffer } = {};\r\n    private _effects: { [key: string]: Effect } = {};\r\n    private _meshList: AbstractMesh[];\r\n    private _meshListCounter = 0;\r\n\r\n    /**\r\n     * Creates a new TransformFeedbackBoundingHelper\r\n     * @param engine defines the engine to use\r\n     */\r\n    constructor(engine: ThinEngine) {\r\n        this._engine = engine;\r\n    }\r\n\r\n    /** @internal */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public processAsync(meshes: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        if (!Array.isArray(meshes)) {\r\n            meshes = [meshes];\r\n        }\r\n\r\n        this._meshListCounter = 0;\r\n\r\n        this._processMeshList(meshes);\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _processMeshList(meshes: AbstractMesh[]) {\r\n        const parallelShaderCompile = this._engine!.getCaps().parallelShaderCompile;\r\n\r\n        this._engine!.getCaps().parallelShaderCompile = undefined;\r\n\r\n        for (let i = 0; i < meshes.length; ++i) {\r\n            const mesh = meshes[i];\r\n            const vertexCount = mesh.getTotalVertices();\r\n\r\n            if (vertexCount === 0 || !(mesh as Mesh).getVertexBuffer || !(mesh as Mesh).getVertexBuffer(VertexBuffer.PositionKind)) {\r\n                continue;\r\n            }\r\n\r\n            // Get correct effect\r\n            let computeEffect: Effect;\r\n            const defines: string[] = [];\r\n            const attribs = [VertexBuffer.PositionKind];\r\n\r\n            // Bones\r\n            if (mesh && mesh.useBones && mesh.computeBonesUsingShaders && mesh.skeleton) {\r\n                attribs.push(VertexBuffer.MatricesIndicesKind);\r\n                attribs.push(VertexBuffer.MatricesWeightsKind);\r\n                if (mesh.numBoneInfluencers > 4) {\r\n                    attribs.push(VertexBuffer.MatricesIndicesExtraKind);\r\n                    attribs.push(VertexBuffer.MatricesWeightsExtraKind);\r\n                }\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n                defines.push(\"#define BONETEXTURE \" + mesh.skeleton.isUsingTextureForMatrices);\r\n                defines.push(\"#define BonesPerMesh \" + (mesh.skeleton.bones.length + 1));\r\n            } else {\r\n                defines.push(\"#define NUM_BONE_INFLUENCERS 0\");\r\n            }\r\n\r\n            // Morph\r\n            const numMorphInfluencers = mesh.morphTargetManager\r\n                ? PrepareDefinesAndAttributesForMorphTargets(\r\n                      mesh.morphTargetManager,\r\n                      defines,\r\n                      attribs,\r\n                      mesh,\r\n                      true, // usePositionMorph\r\n                      false, // useNormalMorph\r\n                      false, // useTangentMorph\r\n                      false, // useUVMorph\r\n                      false, // useUV2Morph\r\n                      false // useColorMorph\r\n                  )\r\n                : 0;\r\n\r\n            // Baked Vertex Animation\r\n            const bvaManager = (<Mesh>mesh).bakedVertexAnimationManager;\r\n            if (bvaManager && bvaManager.isEnabled) {\r\n                defines.push(\"#define BAKED_VERTEX_ANIMATION_TEXTURE\");\r\n                PrepareAttributesForBakedVertexAnimation(attribs, mesh, defines);\r\n            }\r\n\r\n            const join = defines.join(\"\\n\");\r\n            if (!this._effects[join]) {\r\n                const uniforms = [\r\n                    \"boneTextureWidth\",\r\n                    \"mBones\",\r\n                    \"morphTargetInfluences\",\r\n                    \"morphTargetCount\",\r\n                    \"morphTargetTextureInfo\",\r\n                    \"morphTargetTextureIndices\",\r\n                    \"bakedVertexAnimationSettings\",\r\n                    \"bakedVertexAnimationTextureSizeInverted\",\r\n                    \"bakedVertexAnimationTime\",\r\n                ];\r\n                const samplers = [\"boneSampler\", \"morphTargets\", \"bakedVertexAnimationTexture\"];\r\n\r\n                const computeEffectOptions = {\r\n                    attributes: attribs,\r\n                    uniformsNames: uniforms,\r\n                    uniformBuffersNames: [],\r\n                    samplers: samplers,\r\n                    defines: join,\r\n                    fallbacks: null,\r\n                    onCompiled: null,\r\n                    onError: null,\r\n                    indexParameters: { maxSimultaneousMorphTargets: numMorphInfluencers },\r\n                    maxSimultaneousLights: 0,\r\n                    transformFeedbackVaryings: [\"outPosition\"],\r\n                };\r\n                computeEffect = this._engine!.createEffect(\"gpuTransform\", computeEffectOptions, this._engine!);\r\n                this._effects[join] = computeEffect;\r\n            } else {\r\n                computeEffect = this._effects[join];\r\n            }\r\n\r\n            this._compute(mesh, computeEffect);\r\n        }\r\n\r\n        this._engine!.getCaps().parallelShaderCompile = parallelShaderCompile;\r\n    }\r\n\r\n    private _compute(mesh: AbstractMesh, effect: Effect): void {\r\n        const engine = this._engine as Engine;\r\n\r\n        // Buffer\r\n        let targetBuffer: Buffer;\r\n        const vertexCount = mesh.getTotalVertices();\r\n\r\n        if (!this._buffers[mesh.uniqueId]) {\r\n            const targetData = new Float32Array(vertexCount * 3);\r\n            targetBuffer = new Buffer(mesh.getEngine(), targetData, true, 3);\r\n            this._buffers[mesh.uniqueId] = targetBuffer;\r\n        } else {\r\n            targetBuffer = this._buffers[mesh.uniqueId];\r\n        }\r\n\r\n        // Bind\r\n        effect.getEngine().enableEffect(effect);\r\n        (mesh as Mesh)._bindDirect(effect, null, true);\r\n\r\n        // Bones\r\n        BindBonesParameters(mesh, effect);\r\n\r\n        // Morph targets\r\n        BindMorphTargetParameters(mesh, effect);\r\n        if (mesh.morphTargetManager && mesh.morphTargetManager.isUsingTextureForTargets) {\r\n            mesh.morphTargetManager._bind(effect);\r\n        }\r\n\r\n        // BVA\r\n        const bvaManager = (<Mesh>mesh).bakedVertexAnimationManager;\r\n\r\n        if (bvaManager && bvaManager.isEnabled) {\r\n            mesh.bakedVertexAnimationManager?.bind(effect, false);\r\n        }\r\n\r\n        // Update\r\n        const arrayBuffer = targetBuffer.getData()! as Float32Array;\r\n        engine.bindTransformFeedbackBuffer(targetBuffer.getBuffer());\r\n        engine.setRasterizerState(false);\r\n        engine.beginTransformFeedback(true);\r\n        engine.drawArraysType(Constants.MATERIAL_PointFillMode, 0, vertexCount);\r\n        engine.endTransformFeedback();\r\n        engine.setRasterizerState(true);\r\n        engine.readTransformFeedbackBuffer(arrayBuffer);\r\n        engine.bindTransformFeedbackBuffer(null);\r\n\r\n        // Update mesh\r\n        if (this._meshListCounter === 0) {\r\n            mesh._refreshBoundingInfo(arrayBuffer, null);\r\n        } else {\r\n            const bb = mesh.getBoundingInfo().boundingBox;\r\n            const extend = extractMinAndMax(arrayBuffer, 0, vertexCount);\r\n\r\n            TransformFeedbackBoundingHelper._Min.copyFrom(bb.minimum).minimizeInPlace(extend.minimum);\r\n            TransformFeedbackBoundingHelper._Max.copyFrom(bb.maximum).maximizeInPlace(extend.maximum);\r\n\r\n            mesh._refreshBoundingInfoDirect({ minimum: TransformFeedbackBoundingHelper._Min, maximum: TransformFeedbackBoundingHelper._Max });\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public registerMeshListAsync(meshes: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        if (!Array.isArray(meshes)) {\r\n            meshes = [meshes];\r\n        }\r\n\r\n        this._meshList = meshes;\r\n        this._meshListCounter = 0;\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    /** @internal */\r\n    public processMeshList(): void {\r\n        if (this._meshList.length === 0) {\r\n            return;\r\n        }\r\n\r\n        this._processMeshList(this._meshList);\r\n        this._meshListCounter++;\r\n    }\r\n\r\n    /** @internal */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public fetchResultsForMeshListAsync(): Promise<void> {\r\n        this._meshListCounter = 0;\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    /** @internal */\r\n    public dispose(): void {\r\n        for (const key in this._buffers) {\r\n            this._buffers[key].dispose();\r\n        }\r\n        this._buffers = {};\r\n        this._effects = {};\r\n        this._engine = null;\r\n    }\r\n}\r\n"]}