{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrBRDFConfiguration.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrBRDFConfiguration.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { serialize, expandToProperty } from \"../../Misc/decorators\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialBRDFDefines extends MaterialDefines {\r\n    BRDF_V_HEIGHT_CORRELATED = false;\r\n    MS_BRDF_ENERGY_CONSERVATION = false;\r\n    SPHERICAL_HARMONICS = false;\r\n    SPECULAR_GLOSSINESS_ENERGY_CONSERVATION = false;\r\n    MIX_IBL_RADIANCE_WITH_IRRADIANCE = true;\r\n    LEGACY_SPECULAR_ENERGY_CONSERVATION = false;\r\n    BASE_DIFFUSE_MODEL = 0;\r\n    DIELECTRIC_SPECULAR_MODEL = 0;\r\n    CONDUCTOR_SPECULAR_MODEL = 0;\r\n}\r\n\r\n/**\r\n * Plugin that implements the BRDF component of the PBR material\r\n */\r\nexport class PBRBRDFConfiguration extends MaterialPluginBase {\r\n    /**\r\n     * Default value used for the energy conservation.\r\n     * This should only be changed to adapt to the type of texture in scene.environmentBRDFTexture.\r\n     */\r\n    public static DEFAULT_USE_ENERGY_CONSERVATION = true;\r\n\r\n    /**\r\n     * Default value used for the Smith Visibility Height Correlated mode.\r\n     * This should only be changed to adapt to the type of texture in scene.environmentBRDFTexture.\r\n     */\r\n    public static DEFAULT_USE_SMITH_VISIBILITY_HEIGHT_CORRELATED = true;\r\n\r\n    /**\r\n     * Default value used for the IBL diffuse part.\r\n     * This can help switching back to the polynomials mode globally which is a tiny bit\r\n     * less GPU intensive at the drawback of a lower quality.\r\n     */\r\n    public static DEFAULT_USE_SPHERICAL_HARMONICS = true;\r\n\r\n    /**\r\n     * Default value used for activating energy conservation for the specular workflow.\r\n     * If activated, the albedo color is multiplied with (1. - maxChannel(specular color)).\r\n     * If deactivated, a material is only physically plausible, when (albedo color + specular color) < 1.\r\n     */\r\n    public static DEFAULT_USE_SPECULAR_GLOSSINESS_INPUT_ENERGY_CONSERVATION = true;\r\n\r\n    /**\r\n     * Default value for whether IBL irradiance is used to augment rough radiance.\r\n     * If activated, irradiance is blended into the radiance contribution when the material is rough.\r\n     * This better approximates raytracing results for rough surfaces.\r\n     */\r\n    public static DEFAULT_MIX_IBL_RADIANCE_WITH_IRRADIANCE = true;\r\n\r\n    /**\r\n     * Default value for whether the legacy specular energy conservation is used.\r\n     */\r\n    public static DEFAULT_USE_LEGACY_SPECULAR_ENERGY_CONSERVATION = true;\r\n\r\n    /**\r\n     * Defines the default diffuse model used by the material.\r\n     */\r\n    public static DEFAULT_DIFFUSE_MODEL = Constants.MATERIAL_DIFFUSE_MODEL_E_OREN_NAYAR;\r\n\r\n    /**\r\n     * Defines the default dielectric specular model used by the material.\r\n     */\r\n    public static DEFAULT_DIELECTRIC_SPECULAR_MODEL: number = Constants.MATERIAL_DIELECTRIC_SPECULAR_MODEL_GLTF;\r\n\r\n    /**\r\n     * Defines the default conductor specular model used by the material.\r\n     */\r\n    public static DEFAULT_CONDUCTOR_SPECULAR_MODEL: number = Constants.MATERIAL_CONDUCTOR_SPECULAR_MODEL_GLTF;\r\n\r\n    private _useEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_ENERGY_CONSERVATION;\r\n    /**\r\n     * Defines if the material uses energy conservation.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public useEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_ENERGY_CONSERVATION;\r\n\r\n    private _useSmithVisibilityHeightCorrelated = PBRBRDFConfiguration.DEFAULT_USE_SMITH_VISIBILITY_HEIGHT_CORRELATED;\r\n    /**\r\n     * LEGACY Mode set to false\r\n     * Defines if the material uses height smith correlated visibility term.\r\n     * If you intent to not use our default BRDF, you need to load a separate BRDF Texture for the PBR\r\n     * You can either load https://assets.babylonjs.com/environments/uncorrelatedBRDF.png\r\n     * or https://assets.babylonjs.com/environments/uncorrelatedBRDF.dds to have more precision\r\n     * Not relying on height correlated will also disable energy conservation.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public useSmithVisibilityHeightCorrelated = PBRBRDFConfiguration.DEFAULT_USE_SMITH_VISIBILITY_HEIGHT_CORRELATED;\r\n\r\n    private _useSphericalHarmonics = PBRBRDFConfiguration.DEFAULT_USE_SPHERICAL_HARMONICS;\r\n    /**\r\n     * LEGACY Mode set to false\r\n     * Defines if the material uses spherical harmonics vs spherical polynomials for the\r\n     * diffuse part of the IBL.\r\n     * The harmonics despite a tiny bigger cost has been proven to provide closer results\r\n     * to the ground truth.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public useSphericalHarmonics = PBRBRDFConfiguration.DEFAULT_USE_SPHERICAL_HARMONICS;\r\n\r\n    private _useSpecularGlossinessInputEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_SPECULAR_GLOSSINESS_INPUT_ENERGY_CONSERVATION;\r\n    /**\r\n     * Defines if the material uses energy conservation, when the specular workflow is active.\r\n     * If activated, the albedo color is multiplied with (1. - maxChannel(specular color)).\r\n     * If deactivated, a material is only physically plausible, when (albedo color + specular color) < 1.\r\n     * In the deactivated case, the material author has to ensure energy conservation, for a physically plausible rendering.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public useSpecularGlossinessInputEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_SPECULAR_GLOSSINESS_INPUT_ENERGY_CONSERVATION;\r\n\r\n    private _mixIblRadianceWithIrradiance = PBRBRDFConfiguration.DEFAULT_MIX_IBL_RADIANCE_WITH_IRRADIANCE;\r\n    /**\r\n     * Defines if IBL irradiance is used to augment rough radiance.\r\n     * If activated, irradiance is blended into the radiance contribution when the material is rough.\r\n     * This better approximates raytracing results for rough surfaces.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public mixIblRadianceWithIrradiance = PBRBRDFConfiguration.DEFAULT_MIX_IBL_RADIANCE_WITH_IRRADIANCE;\r\n\r\n    private _useLegacySpecularEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_LEGACY_SPECULAR_ENERGY_CONSERVATION;\r\n    /**\r\n     * Defines if the legacy specular energy conservation is used.\r\n     * If activated, the specular color is multiplied with (1. - maxChannel(albedo color)).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public useLegacySpecularEnergyConservation = PBRBRDFConfiguration.DEFAULT_USE_LEGACY_SPECULAR_ENERGY_CONSERVATION;\r\n\r\n    private _baseDiffuseModel: number = PBRBRDFConfiguration.DEFAULT_DIFFUSE_MODEL;\r\n    /**\r\n     * Defines the base diffuse roughness model of the material.\r\n     */\r\n    @serialize(\"baseDiffuseModel\")\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public baseDiffuseModel: number = PBRBRDFConfiguration.DEFAULT_DIFFUSE_MODEL;\r\n\r\n    private _dielectricSpecularModel: number = PBRBRDFConfiguration.DEFAULT_DIELECTRIC_SPECULAR_MODEL;\r\n    /**\r\n     * The material model to use for specular lighting of dielectric materials.\r\n     */\r\n    @serialize(\"dielectricSpecularModel\")\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public dielectricSpecularModel: number = PBRBRDFConfiguration.DEFAULT_DIELECTRIC_SPECULAR_MODEL;\r\n\r\n    private _conductorSpecularModel: number = PBRBRDFConfiguration.DEFAULT_CONDUCTOR_SPECULAR_MODEL;\r\n    /**\r\n     * The material model to use for specular lighting.\r\n     */\r\n    @serialize(\"conductorSpecularModel\")\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public conductorSpecularModel: number = PBRBRDFConfiguration.DEFAULT_CONDUCTOR_SPECULAR_MODEL;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsMiscDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsMiscDirty(): void {\r\n        this._internalMarkAllSubMeshesAsMiscDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the plugin is compatible with a given shader language.\r\n     * @returns true if the plugin is compatible with the shader language\r\n     */\r\n    public override isCompatible(): boolean {\r\n        return true;\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRBRDF\", 90, new MaterialBRDFDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsMiscDirty = material._dirtyCallbacks[Constants.MATERIAL_MiscDirtyFlag];\r\n        this._enable(true);\r\n    }\r\n\r\n    public override prepareDefines(defines: MaterialBRDFDefines): void {\r\n        defines.BRDF_V_HEIGHT_CORRELATED = this._useSmithVisibilityHeightCorrelated;\r\n        defines.MS_BRDF_ENERGY_CONSERVATION = this._useEnergyConservation && this._useSmithVisibilityHeightCorrelated;\r\n        defines.SPHERICAL_HARMONICS = this._useSphericalHarmonics;\r\n        defines.SPECULAR_GLOSSINESS_ENERGY_CONSERVATION = this._useSpecularGlossinessInputEnergyConservation;\r\n        defines.MIX_IBL_RADIANCE_WITH_IRRADIANCE = this._mixIblRadianceWithIrradiance;\r\n        defines.LEGACY_SPECULAR_ENERGY_CONSERVATION = this._useLegacySpecularEnergyConservation;\r\n        defines.BASE_DIFFUSE_MODEL = this._baseDiffuseModel;\r\n        defines.DIELECTRIC_SPECULAR_MODEL = this._dielectricSpecularModel;\r\n        defines.CONDUCTOR_SPECULAR_MODEL = this._conductorSpecularModel;\r\n    }\r\n\r\n    public override getClassName(): string {\r\n        return \"PBRBRDFConfiguration\";\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA,uDAAA,EAAyD,CAEzD,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACpE,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;;;;;AAMrD,MAAO,mBAAoB,6KAAQ,kBAAe;IAAxD,aAAA;;QACI,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QACjC,IAAA,CAAA,2BAA2B,GAAG,KAAK,CAAC;QACpC,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,uCAAuC,GAAG,KAAK,CAAC;QAChD,IAAA,CAAA,gCAAgC,GAAG,IAAI,CAAC;QACxC,IAAA,CAAA,mCAAmC,GAAG,KAAK,CAAC;QAC5C,IAAA,CAAA,kBAAkB,GAAG,CAAC,CAAC;QACvB,IAAA,CAAA,yBAAyB,GAAG,CAAC,CAAC;QAC9B,IAAA,CAAA,wBAAwB,GAAG,CAAC,CAAC;IACjC,CAAC;CAAA;AAKK,MAAO,oBAAqB,SAAQ,4LAAkB;IAgJxD,cAAA,EAAgB,CACT,4BAA4B,GAAA;QAC/B,IAAI,CAAC,oCAAoC,EAAE,CAAC;IAChD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI,CAAA;QACzD,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,mBAAmB,EAAE,EAAE,eAAe,CAAC,CAAC;QAxGvE,IAAA,CAAA,sBAAsB,GAAG,oBAAoB,CAAC,+BAA+B,CAAC;QACtF;;WAEG,CAGI,IAAA,CAAA,qBAAqB,GAAG,oBAAoB,CAAC,+BAA+B,CAAC;QAE5E,IAAA,CAAA,mCAAmC,GAAG,oBAAoB,CAAC,8CAA8C,CAAC;QAClH;;;;;;;WAOG,CAGI,IAAA,CAAA,kCAAkC,GAAG,oBAAoB,CAAC,8CAA8C,CAAC;QAExG,IAAA,CAAA,sBAAsB,GAAG,oBAAoB,CAAC,+BAA+B,CAAC;QACtF;;;;;;WAMG,CAGI,IAAA,CAAA,qBAAqB,GAAG,oBAAoB,CAAC,+BAA+B,CAAC;QAE5E,IAAA,CAAA,6CAA6C,GAAG,oBAAoB,CAAC,yDAAyD,CAAC;QACvI;;;;;WAKG,CAGI,IAAA,CAAA,4CAA4C,GAAG,oBAAoB,CAAC,yDAAyD,CAAC;QAE7H,IAAA,CAAA,6BAA6B,GAAG,oBAAoB,CAAC,wCAAwC,CAAC;QACtG;;;;WAIG,CAGI,IAAA,CAAA,4BAA4B,GAAG,oBAAoB,CAAC,wCAAwC,CAAC;QAE5F,IAAA,CAAA,oCAAoC,GAAG,oBAAoB,CAAC,+CAA+C,CAAC;QACpH;;;WAGG,CAGI,IAAA,CAAA,mCAAmC,GAAG,oBAAoB,CAAC,+CAA+C,CAAC;QAE1G,IAAA,CAAA,iBAAiB,GAAW,oBAAoB,CAAC,qBAAqB,CAAC;QAC/E;;WAEG,CAGI,IAAA,CAAA,gBAAgB,GAAW,oBAAoB,CAAC,qBAAqB,CAAC;QAErE,IAAA,CAAA,wBAAwB,GAAW,oBAAoB,CAAC,iCAAiC,CAAC;QAClG;;WAEG,CAGI,IAAA,CAAA,uBAAuB,GAAW,oBAAoB,CAAC,iCAAiC,CAAC;QAExF,IAAA,CAAA,uBAAuB,GAAW,oBAAoB,CAAC,gCAAgC,CAAC;QAChG;;WAEG,CAGI,IAAA,CAAA,sBAAsB,GAAW,oBAAoB,CAAC,gCAAgC,CAAC;QAqB1F,IAAI,CAAC,oCAAoC,GAAG,QAAQ,CAAC,eAAe,CAAC,GAAA,MAAS,CAAC,sBAAsB,CAAC,CAAC;QACvG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACvB,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC,mCAAmC,CAAC;QAC5E,OAAO,CAAC,2BAA2B,GAAG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,mCAAmC,CAAC;QAC9G,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC1D,OAAO,CAAC,uCAAuC,GAAG,IAAI,CAAC,6CAA6C,CAAC;QACrG,OAAO,CAAC,gCAAgC,GAAG,IAAI,CAAC,6BAA6B,CAAC;QAC9E,OAAO,CAAC,mCAAmC,GAAG,IAAI,CAAC,oCAAoC,CAAC;QACxF,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACpD,OAAO,CAAC,yBAAyB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAClE,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC,uBAAuB,CAAC;IACpE,CAAC;IAEe,YAAY,GAAA;QACxB,OAAO,sBAAsB,CAAC;IAClC,CAAC;;AAjLD;;;GAGG,CACW,qBAAA,+BAA+B,GAAG,IAAI,AAAP,CAAQ;AAErD;;;GAGG,CACW,qBAAA,8CAA8C,GAAG,IAAI,AAAP,CAAQ;AAEpE;;;;GAIG,CACW,qBAAA,+BAA+B,GAAG,IAAI,AAAP,CAAQ;AAErD;;;;GAIG,CACW,qBAAA,yDAAyD,GAAG,IAAI,AAAP,CAAQ;AAE/E;;;;GAIG,CACW,qBAAA,wCAAwC,GAAG,IAAI,AAAP,CAAQ;AAE9D;;GAEG,CACW,qBAAA,+CAA+C,GAAG,IAAI,AAAP,CAAQ;AAErE;;GAEG,CACW,qBAAA,qBAAqB,GAAG,SAAS,CAAC,mCAAmC,AAAhD,CAAiD;AAEpF;;GAEG,CACW,qBAAA,iCAAiC,GAAW,SAAS,CAAC,uCAAuC,AAA5D,CAA6D;AAE5G;;GAEG,CACW,qBAAA,gCAAgC,GAAW,SAAS,CAAC,sCAAsC,AAA3D,CAA4D;wJAQnG,aAAA,EAAA;iKAFN,aAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;mEACmC;wJAa7E,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;gFAC+D;wJAYzG,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;IACX,iLAAA,AAAgB,EAAC,8BAA8B,CAAC;mEACmC;AAW7E,qKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;0FACoF;wJAU9H,aAAA,EAAA;KAFN,yKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;0EACmD;wJAS7F,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,8BAA8B,CAAC;iFACiE;wJAQ3G,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAC,kBAAkB,CAAC;kKAC7B,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;8DAC4B;wJAQtE,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAC,yBAAyB,CAAC;kKACpC,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;qEAC+C;wJAQzF,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAC,wBAAwB,CAAC;kKACnC,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;oEAC6C", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrClearCoatConfiguration.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrClearCoatConfiguration.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../../types\";\r\nimport { serialize, serializeAsTexture, expandToProperty, serializeAsColor3 } from \"../../Misc/decorators\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { MaterialFlags } from \"../materialFlags\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialClearCoatDefines extends MaterialDefines {\r\n    public CLEARCOAT = false;\r\n    public CLEARCOAT_DEFAULTIOR = false;\r\n    public CLEARCOAT_TEXTURE = false;\r\n    public CLEARCOAT_TEXTURE_ROUGHNESS = false;\r\n    public CLEARCOAT_TEXTUREDIRECTUV = 0;\r\n    public CLEARCOAT_TEXTURE_ROUGHNESSDIRECTUV = 0;\r\n    public CLEARCOAT_BUMP = false;\r\n    public CLEARCOAT_BUMPDIRECTUV = 0;\r\n    public CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE = false;\r\n    public CLEARCOAT_REMAP_F0 = false;\r\n\r\n    public CLEARCOAT_TINT = false;\r\n    public CLEARCOAT_TINT_TEXTURE = false;\r\n    public CLEARCOAT_TINT_TEXTUREDIRECTUV = 0;\r\n    public CLEARCOAT_TINT_GAMMATEXTURE = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the clear coat component of the PBR material\r\n */\r\nexport class PBRClearCoatConfiguration extends MaterialPluginBase {\r\n    protected override _material: PBRBaseMaterial;\r\n\r\n    /**\r\n     * This defaults to 1.5 corresponding to a 0.04 f0 or a 4% reflectance at normal incidence\r\n     * The default fits with a polyurethane material.\r\n     * @internal\r\n     */\r\n    public static readonly _DefaultIndexOfRefraction = 1.5;\r\n\r\n    private _isEnabled = false;\r\n    /**\r\n     * Defines if the clear coat is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isEnabled = false;\r\n\r\n    /**\r\n     * Defines the clear coat layer strength (between 0 and 1) it defaults to 1.\r\n     */\r\n    @serialize()\r\n    public intensity: number = 1;\r\n\r\n    /**\r\n     * Defines the clear coat layer roughness.\r\n     */\r\n    @serialize()\r\n    public roughness: number = 0;\r\n\r\n    private _indexOfRefraction = PBRClearCoatConfiguration._DefaultIndexOfRefraction;\r\n    /**\r\n     * Defines the index of refraction of the clear coat.\r\n     * This defaults to 1.5 corresponding to a 0.04 f0 or a 4% reflectance at normal incidence\r\n     * The default fits with a polyurethane material.\r\n     * Changing the default value is more performance intensive.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public indexOfRefraction = PBRClearCoatConfiguration._DefaultIndexOfRefraction;\r\n\r\n    private _texture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the clear coat values in a texture (red channel is intensity and green channel is roughness)\r\n     * If useRoughnessFromMainTexture is false, the green channel of texture is not used and the green channel of textureRoughness is used instead\r\n     * if textureRoughness is not empty, else no texture roughness is used\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public texture: Nullable<BaseTexture> = null;\r\n\r\n    private _useRoughnessFromMainTexture = true;\r\n    /**\r\n     * Indicates that the green channel of the texture property will be used for roughness (default: true)\r\n     * If false, the green channel from textureRoughness is used for roughness\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRoughnessFromMainTexture = true;\r\n\r\n    private _textureRoughness: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the clear coat roughness in a texture (green channel)\r\n     * Not used if useRoughnessFromMainTexture is true\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public textureRoughness: Nullable<BaseTexture> = null;\r\n\r\n    private _remapF0OnInterfaceChange = true;\r\n    /**\r\n     * Defines if the F0 value should be remapped to account for the interface change in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public remapF0OnInterfaceChange = true;\r\n\r\n    private _bumpTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Define the clear coat specific bump texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public bumpTexture: Nullable<BaseTexture> = null;\r\n\r\n    private _isTintEnabled = false;\r\n    /**\r\n     * Defines if the clear coat tint is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isTintEnabled = false;\r\n\r\n    /**\r\n     * Defines the clear coat tint of the material.\r\n     * This is only use if tint is enabled\r\n     */\r\n    @serializeAsColor3()\r\n    public tintColor = Color3.White();\r\n\r\n    /**\r\n     * Defines the distance at which the tint color should be found in the\r\n     * clear coat media.\r\n     * This is only use if tint is enabled\r\n     */\r\n    @serialize()\r\n    public tintColorAtDistance = 1;\r\n\r\n    /**\r\n     * Defines the clear coat layer thickness.\r\n     * This is only use if tint is enabled\r\n     */\r\n    @serialize()\r\n    public tintThickness: number = 1;\r\n\r\n    private _tintTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the clear tint values in a texture.\r\n     * rgb is tint\r\n     * a is a thickness factor\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public tintTexture: Nullable<BaseTexture> = null;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the plugin is compatible with a given shader language.\r\n     * @returns true if the plugin is compatible with the shader language\r\n     */\r\n    public override isCompatible(): boolean {\r\n        return true;\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRClearCoat\", 100, new MaterialClearCoatDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n    }\r\n\r\n    public override isReadyForSubMesh(defines: MaterialClearCoatDefines, scene: Scene, engine: Engine): boolean {\r\n        if (!this._isEnabled) {\r\n            return true;\r\n        }\r\n\r\n        const disableBumpMap = this._material._disableBumpMap;\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._texture && MaterialFlags.ClearCoatTextureEnabled) {\r\n                    if (!this._texture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._textureRoughness && MaterialFlags.ClearCoatTextureEnabled) {\r\n                    if (!this._textureRoughness.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (engine.getCaps().standardDerivatives && this._bumpTexture && MaterialFlags.ClearCoatBumpTextureEnabled && !disableBumpMap) {\r\n                    // Bump texture cannot be not blocking.\r\n                    if (!this._bumpTexture.isReady()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._isTintEnabled && this._tintTexture && MaterialFlags.ClearCoatTintTextureEnabled) {\r\n                    if (!this._tintTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override prepareDefinesBeforeAttributes(defines: MaterialClearCoatDefines, scene: Scene): void {\r\n        if (this._isEnabled) {\r\n            defines.CLEARCOAT = true;\r\n            defines.CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE = this._useRoughnessFromMainTexture;\r\n            defines.CLEARCOAT_REMAP_F0 = this._remapF0OnInterfaceChange;\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._texture && MaterialFlags.ClearCoatTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._texture, defines, \"CLEARCOAT_TEXTURE\");\r\n                    } else {\r\n                        defines.CLEARCOAT_TEXTURE = false;\r\n                    }\r\n\r\n                    if (this._textureRoughness && MaterialFlags.ClearCoatTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._textureRoughness, defines, \"CLEARCOAT_TEXTURE_ROUGHNESS\");\r\n                    } else {\r\n                        defines.CLEARCOAT_TEXTURE_ROUGHNESS = false;\r\n                    }\r\n\r\n                    if (this._bumpTexture && MaterialFlags.ClearCoatBumpTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._bumpTexture, defines, \"CLEARCOAT_BUMP\");\r\n                    } else {\r\n                        defines.CLEARCOAT_BUMP = false;\r\n                    }\r\n\r\n                    defines.CLEARCOAT_DEFAULTIOR = this._indexOfRefraction === PBRClearCoatConfiguration._DefaultIndexOfRefraction;\r\n\r\n                    if (this._isTintEnabled) {\r\n                        defines.CLEARCOAT_TINT = true;\r\n                        if (this._tintTexture && MaterialFlags.ClearCoatTintTextureEnabled) {\r\n                            PrepareDefinesForMergedUV(this._tintTexture, defines, \"CLEARCOAT_TINT_TEXTURE\");\r\n                            defines.CLEARCOAT_TINT_GAMMATEXTURE = this._tintTexture.gammaSpace;\r\n                        } else {\r\n                            defines.CLEARCOAT_TINT_TEXTURE = false;\r\n                        }\r\n                    } else {\r\n                        defines.CLEARCOAT_TINT = false;\r\n                        defines.CLEARCOAT_TINT_TEXTURE = false;\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            defines.CLEARCOAT = false;\r\n            defines.CLEARCOAT_TEXTURE = false;\r\n            defines.CLEARCOAT_TEXTURE_ROUGHNESS = false;\r\n            defines.CLEARCOAT_BUMP = false;\r\n            defines.CLEARCOAT_TINT = false;\r\n            defines.CLEARCOAT_TINT_TEXTURE = false;\r\n            defines.CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE = false;\r\n            defines.CLEARCOAT_DEFAULTIOR = false;\r\n            defines.CLEARCOAT_TEXTUREDIRECTUV = 0;\r\n            defines.CLEARCOAT_TEXTURE_ROUGHNESSDIRECTUV = 0;\r\n            defines.CLEARCOAT_BUMPDIRECTUV = 0;\r\n            defines.CLEARCOAT_REMAP_F0 = false;\r\n            defines.CLEARCOAT_TINT_TEXTUREDIRECTUV = 0;\r\n            defines.CLEARCOAT_TINT_GAMMATEXTURE = false;\r\n        }\r\n    }\r\n\r\n    public override bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene, engine: Engine, subMesh: SubMesh): void {\r\n        if (!this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const defines = subMesh.materialDefines as unknown as MaterialClearCoatDefines;\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n\r\n        const disableBumpMap = this._material._disableBumpMap;\r\n        const invertNormalMapX = this._material._invertNormalMapX;\r\n        const invertNormalMapY = this._material._invertNormalMapY;\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if ((this._texture || this._textureRoughness) && MaterialFlags.ClearCoatTextureEnabled) {\r\n                uniformBuffer.updateFloat4(\r\n                    \"vClearCoatInfos\",\r\n                    this._texture?.coordinatesIndex ?? 0,\r\n                    this._texture?.level ?? 0,\r\n                    this._textureRoughness?.coordinatesIndex ?? 0,\r\n                    this._textureRoughness?.level ?? 0\r\n                );\r\n                if (this._texture) {\r\n                    BindTextureMatrix(this._texture, uniformBuffer, \"clearCoat\");\r\n                }\r\n                if (this._textureRoughness && !defines.CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE) {\r\n                    BindTextureMatrix(this._textureRoughness, uniformBuffer, \"clearCoatRoughness\");\r\n                }\r\n            }\r\n\r\n            if (this._bumpTexture && engine.getCaps().standardDerivatives && MaterialFlags.ClearCoatTextureEnabled && !disableBumpMap) {\r\n                uniformBuffer.updateFloat2(\"vClearCoatBumpInfos\", this._bumpTexture.coordinatesIndex, this._bumpTexture.level);\r\n                BindTextureMatrix(this._bumpTexture, uniformBuffer, \"clearCoatBump\");\r\n\r\n                if (scene._mirroredCameraPosition) {\r\n                    uniformBuffer.updateFloat2(\"vClearCoatTangentSpaceParams\", invertNormalMapX ? 1.0 : -1.0, invertNormalMapY ? 1.0 : -1.0);\r\n                } else {\r\n                    uniformBuffer.updateFloat2(\"vClearCoatTangentSpaceParams\", invertNormalMapX ? -1.0 : 1.0, invertNormalMapY ? -1.0 : 1.0);\r\n                }\r\n            }\r\n\r\n            if (this._tintTexture && MaterialFlags.ClearCoatTintTextureEnabled) {\r\n                uniformBuffer.updateFloat2(\"vClearCoatTintInfos\", this._tintTexture.coordinatesIndex, this._tintTexture.level);\r\n                BindTextureMatrix(this._tintTexture, uniformBuffer, \"clearCoatTint\");\r\n            }\r\n\r\n            // Clear Coat General params\r\n            uniformBuffer.updateFloat2(\"vClearCoatParams\", this.intensity, this.roughness);\r\n\r\n            // Clear Coat Refraction params\r\n            const a = 1 - this._indexOfRefraction;\r\n            const b = 1 + this._indexOfRefraction;\r\n            const f0 = Math.pow(-a / b, 2); // Schlicks approx: (ior1 - ior2) / (ior1 + ior2) where ior2 for air is close to vacuum = 1.\r\n            const eta = 1 / this._indexOfRefraction;\r\n            uniformBuffer.updateFloat4(\"vClearCoatRefractionParams\", f0, eta, a, b);\r\n\r\n            if (this._isTintEnabled) {\r\n                uniformBuffer.updateFloat4(\"vClearCoatTintParams\", this.tintColor.r, this.tintColor.g, this.tintColor.b, Math.max(0.00001, this.tintThickness));\r\n                uniformBuffer.updateFloat(\"clearCoatColorAtDistance\", Math.max(0.00001, this.tintColorAtDistance));\r\n            }\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._texture && MaterialFlags.ClearCoatTextureEnabled) {\r\n                uniformBuffer.setTexture(\"clearCoatSampler\", this._texture);\r\n            }\r\n\r\n            if (this._textureRoughness && !defines.CLEARCOAT_USE_ROUGHNESS_FROM_MAINTEXTURE && MaterialFlags.ClearCoatTextureEnabled) {\r\n                uniformBuffer.setTexture(\"clearCoatRoughnessSampler\", this._textureRoughness);\r\n            }\r\n\r\n            if (this._bumpTexture && engine.getCaps().standardDerivatives && MaterialFlags.ClearCoatBumpTextureEnabled && !disableBumpMap) {\r\n                uniformBuffer.setTexture(\"clearCoatBumpSampler\", this._bumpTexture);\r\n            }\r\n\r\n            if (this._isTintEnabled && this._tintTexture && MaterialFlags.ClearCoatTintTextureEnabled) {\r\n                uniformBuffer.setTexture(\"clearCoatTintSampler\", this._tintTexture);\r\n            }\r\n        }\r\n    }\r\n\r\n    public override hasTexture(texture: BaseTexture): boolean {\r\n        if (this._texture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._textureRoughness === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._bumpTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._tintTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public override getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._texture) {\r\n            activeTextures.push(this._texture);\r\n        }\r\n\r\n        if (this._textureRoughness) {\r\n            activeTextures.push(this._textureRoughness);\r\n        }\r\n\r\n        if (this._bumpTexture) {\r\n            activeTextures.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._tintTexture) {\r\n            activeTextures.push(this._tintTexture);\r\n        }\r\n    }\r\n\r\n    public override getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._texture && this._texture.animations && this._texture.animations.length > 0) {\r\n            animatables.push(this._texture);\r\n        }\r\n\r\n        if (this._textureRoughness && this._textureRoughness.animations && this._textureRoughness.animations.length > 0) {\r\n            animatables.push(this._textureRoughness);\r\n        }\r\n\r\n        if (this._bumpTexture && this._bumpTexture.animations && this._bumpTexture.animations.length > 0) {\r\n            animatables.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._tintTexture && this._tintTexture.animations && this._tintTexture.animations.length > 0) {\r\n            animatables.push(this._tintTexture);\r\n        }\r\n    }\r\n\r\n    public override dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            this._texture?.dispose();\r\n            this._textureRoughness?.dispose();\r\n            this._bumpTexture?.dispose();\r\n            this._tintTexture?.dispose();\r\n        }\r\n    }\r\n\r\n    public override getClassName(): string {\r\n        return \"PBRClearCoatConfiguration\";\r\n    }\r\n\r\n    public override addFallbacks(defines: MaterialClearCoatDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.CLEARCOAT_BUMP) {\r\n            fallbacks.addFallback(currentRank++, \"CLEARCOAT_BUMP\");\r\n        }\r\n        if (defines.CLEARCOAT_TINT) {\r\n            fallbacks.addFallback(currentRank++, \"CLEARCOAT_TINT\");\r\n        }\r\n        if (defines.CLEARCOAT) {\r\n            fallbacks.addFallback(currentRank++, \"CLEARCOAT\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public override getSamplers(samplers: string[]): void {\r\n        samplers.push(\"clearCoatSampler\", \"clearCoatRoughnessSampler\", \"clearCoatBumpSampler\", \"clearCoatTintSampler\");\r\n    }\r\n\r\n    public override getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vClearCoatParams\", size: 2, type: \"vec2\" },\r\n                { name: \"vClearCoatRefractionParams\", size: 4, type: \"vec4\" },\r\n                { name: \"vClearCoatInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"clearCoatMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"clearCoatRoughnessMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"vClearCoatBumpInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"vClearCoatTangentSpaceParams\", size: 2, type: \"vec2\" },\r\n                { name: \"clearCoatBumpMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"vClearCoatTintParams\", size: 4, type: \"vec4\" },\r\n                { name: \"clearCoatColorAtDistance\", size: 1, type: \"float\" },\r\n                { name: \"vClearCoatTintInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"clearCoatTintMatrix\", size: 16, type: \"mat4\" },\r\n            ],\r\n        };\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAC3G,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAEhD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAMjD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAKrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;;;;;;;;AAKrF,MAAO,wBAAyB,6KAAQ,kBAAe;IAA7D,aAAA;;QACW,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,2BAA2B,GAAG,KAAK,CAAC;QACpC,IAAA,CAAA,yBAAyB,GAAG,CAAC,CAAC;QAC9B,IAAA,CAAA,mCAAmC,GAAG,CAAC,CAAC;QACxC,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,sBAAsB,GAAG,CAAC,CAAC;QAC3B,IAAA,CAAA,wCAAwC,GAAG,KAAK,CAAC;QACjD,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,8BAA8B,GAAG,CAAC,CAAC;QACnC,IAAA,CAAA,2BAA2B,GAAG,KAAK,CAAC;IAC/C,CAAC;CAAA;AAKK,MAAO,yBAA0B,gLAAQ,qBAAkB;IAgI7D,cAAA,EAAgB,CACT,gCAAgC,GAAA;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI,CAAA;QACzD,KAAK,CAAC,QAAQ,EAAE,cAAc,EAAE,GAAG,EAAE,IAAI,wBAAwB,EAAE,EAAE,eAAe,CAAC,CAAC;QArIlF,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAC3B;;WAEG,CAGI,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAEzB;;WAEG,CAEI,IAAA,CAAA,SAAS,GAAW,CAAC,CAAC;QAE7B;;WAEG,CAEI,IAAA,CAAA,SAAS,GAAW,CAAC,CAAC;QAErB,IAAA,CAAA,kBAAkB,GAAG,yBAAyB,CAAC,yBAAyB,CAAC;QACjF;;;;;WAKG,CAGI,IAAA,CAAA,iBAAiB,GAAG,yBAAyB,CAAC,yBAAyB,CAAC;QAEvE,IAAA,CAAA,QAAQ,GAA0B,IAAI,CAAC;QAC/C;;;;WAIG,CAGI,IAAA,CAAA,OAAO,GAA0B,IAAI,CAAC;QAErC,IAAA,CAAA,4BAA4B,GAAG,IAAI,CAAC;QAC5C;;;WAGG,CAGI,IAAA,CAAA,2BAA2B,GAAG,IAAI,CAAC;QAElC,IAAA,CAAA,iBAAiB,GAA0B,IAAI,CAAC;QACxD;;;WAGG,CAGI,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAE9C,IAAA,CAAA,yBAAyB,GAAG,IAAI,CAAC;QACzC;;WAEG,CAGI,IAAA,CAAA,wBAAwB,GAAG,IAAI,CAAC;QAE/B,IAAA,CAAA,YAAY,GAA0B,IAAI,CAAC;QACnD;;WAEG,CAGI,IAAA,CAAA,WAAW,GAA0B,IAAI,CAAC;QAEzC,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAC/B;;WAEG,CAGI,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QAE7B;;;WAGG,CAEI,IAAA,CAAA,SAAS,iKAAG,SAAM,CAAC,KAAK,EAAE,CAAC;QAElC;;;;WAIG,CAEI,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC;QAE/B;;;WAGG,CAEI,IAAA,CAAA,aAAa,GAAW,CAAC,CAAC;QAEzB,IAAA,CAAA,YAAY,GAA0B,IAAI,CAAC;QACnD;;;;WAIG,CAGI,IAAA,CAAA,WAAW,GAA0B,IAAI,CAAC;QAsB7C,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAA,OAAS,CAAC,yBAAyB,CAAC,CAAC;IAClH,CAAC;IAEe,iBAAiB,CAAC,OAAiC,EAAE,KAAY,EAAE,MAAc,EAAA;QAC7F,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QACtD,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,uBAAuB,EAAE,CAAC;oBACzD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBACxC,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,iBAAiB,sKAAI,gBAAa,CAAC,uBAAuB,EAAE,CAAC;oBAClE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBACjD,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,sKAAI,gBAAa,CAAC,2BAA2B,IAAI,CAAC,cAAc,EAAE,CAAC;oBAC5H,uCAAuC;oBACvC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;wBAC/B,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,sKAAI,gBAAa,CAAC,2BAA2B,EAAE,CAAC;oBACxF,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC5C,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,8BAA8B,CAAC,OAAiC,EAAE,KAAY,EAAA;QAC1F,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YACzB,OAAO,CAAC,wCAAwC,GAAG,IAAI,CAAC,4BAA4B,CAAC;YACrF,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC;YAE5D,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;oBACxB,IAAI,IAAI,CAAC,QAAQ,IAAI,kLAAa,CAAC,uBAAuB,EAAE,CAAC;4MACzD,4BAAA,AAAyB,EAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;oBAC3E,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBACtC,CAAC;oBAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,kLAAa,CAAC,uBAAuB,EAAE,CAAC;4MAClE,4BAAA,AAAyB,EAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,6BAA6B,CAAC,CAAC;oBAC9F,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;oBAChD,CAAC;oBAED,IAAI,IAAI,CAAC,YAAY,sKAAI,gBAAa,CAAC,2BAA2B,EAAE,CAAC;yBACjE,+MAAA,AAAyB,EAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;oBAC5E,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBACnC,CAAC;oBAED,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,KAAK,yBAAyB,CAAC,yBAAyB,CAAC;oBAE/G,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;wBACtB,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;wBAC9B,IAAI,IAAI,CAAC,YAAY,sKAAI,gBAAa,CAAC,2BAA2B,EAAE,CAAC;gCACjE,4MAAA,AAAyB,EAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,wBAAwB,CAAC,CAAC;4BAChF,OAAO,CAAC,2BAA2B,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;wBACvE,CAAC,MAAM,CAAC;4BACJ,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;wBAC3C,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;wBAC/B,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;oBAC3C,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;YAC1B,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAClC,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;YAC/B,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;YAC/B,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACvC,OAAO,CAAC,wCAAwC,GAAG,KAAK,CAAC;YACzD,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;YACrC,OAAO,CAAC,yBAAyB,GAAG,CAAC,CAAC;YACtC,OAAO,CAAC,mCAAmC,GAAG,CAAC,CAAC;YAChD,OAAO,CAAC,sBAAsB,GAAG,CAAC,CAAC;YACnC,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,8BAA8B,GAAG,CAAC,CAAC;YAC3C,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;QAChD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,aAA4B,EAAE,KAAY,EAAE,MAAc,EAAE,OAAgB,EAAA;QACvG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,eAAsD,CAAC;QAE/E,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEzC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QACtD,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAC1D,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAE1D,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC9D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,sKAAI,gBAAa,CAAC,uBAAuB,EAAE,CAAC;gBACrF,aAAa,CAAC,YAAY,CACtB,iBAAiB,EACjB,IAAI,CAAC,QAAQ,EAAE,gBAAgB,IAAI,CAAC,EACpC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,EACzB,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,CAAC,EAC7C,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,CACrC,CAAC;gBACF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;uMAChB,qBAAA,AAAiB,EAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;gBACjE,CAAC;gBACD,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,wCAAwC,EAAE,CAAC;wMAC9E,oBAAA,AAAiB,EAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,EAAE,oBAAoB,CAAC,CAAC;gBACnF,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,sKAAI,gBAAa,CAAC,uBAAuB,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxH,aAAa,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;oMAC/G,oBAAA,AAAiB,EAAC,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;gBAErE,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;oBAChC,aAAa,CAAC,YAAY,CAAC,8BAA8B,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC7H,CAAC,MAAM,CAAC;oBACJ,aAAa,CAAC,YAAY,CAAC,8BAA8B,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC7H,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,YAAY,IAAI,kLAAa,CAAC,2BAA2B,EAAE,CAAC;gBACjE,aAAa,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;oMAC/G,oBAAA,AAAiB,EAAC,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,eAAe,CAAC,CAAC;YACzE,CAAC;YAED,4BAA4B;YAC5B,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAE/E,+BAA+B;YAC/B,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACtC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACtC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,4FAA4F;YAC5H,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACxC,aAAa,CAAC,YAAY,CAAC,4BAA4B,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAExE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,aAAa,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;gBAChJ,aAAa,CAAC,WAAW,CAAC,0BAA0B,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACvG,CAAC;QACL,CAAC;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,uBAAuB,EAAE,CAAC;gBACzD,aAAa,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,wCAAwC,sKAAI,gBAAa,CAAC,uBAAuB,EAAE,CAAC;gBACvH,aAAa,CAAC,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAClF,CAAC;YAED,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,sKAAI,gBAAa,CAAC,2BAA2B,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC5H,aAAa,CAAC,UAAU,CAAC,sBAAsB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACxE,CAAC;YAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,sKAAI,gBAAa,CAAC,2BAA2B,EAAE,CAAC;gBACxF,aAAa,CAAC,UAAU,CAAC,sBAAsB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACxE,CAAC;QACL,CAAC;IACL,CAAC;IAEe,UAAU,CAAC,OAAoB,EAAA;QAC3C,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEe,iBAAiB,CAAC,cAA6B,EAAA;QAC3D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,WAA0B,EAAA;QACrD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9G,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/F,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/F,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxC,CAAC;IACL,CAAC;IAEe,OAAO,CAAC,oBAA8B,EAAA;QAClD,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;QACjC,CAAC;IACL,CAAC;IAEe,YAAY,GAAA;QACxB,OAAO,2BAA2B,CAAC;IACvC,CAAC;IAEe,YAAY,CAAC,OAAiC,EAAE,SAA0B,EAAE,WAAmB,EAAA;QAC3G,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAC3D,CAAC;QACD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAC3D,CAAC;QACD,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,WAAW,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEe,WAAW,CAAC,QAAkB,EAAA;QAC1C,QAAQ,CAAC,IAAI,CAAC,kBAAkB,EAAE,2BAA2B,EAAE,sBAAsB,EAAE,sBAAsB,CAAC,CAAC;IACnH,CAAC;IAEe,WAAW,GAAA;QACvB,OAAO;YACH,GAAG,EAAE;gBACD;oBAAE,IAAI,EAAE,kBAAkB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACnD;oBAAE,IAAI,EAAE,4BAA4B;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC7D;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAClD;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACnD;oBAAE,IAAI,EAAE,0BAA0B;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC5D;oBAAE,IAAI,EAAE,qBAAqB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACtD;oBAAE,IAAI,EAAE,8BAA8B;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC/D;oBAAE,IAAI,EAAE,qBAAqB;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACvD;oBAAE,IAAI,EAAE,sBAAsB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACvD;oBAAE,IAAI,EAAE,0BAA0B;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,OAAO;gBAAA,CAAE;gBAC5D;oBAAE,IAAI,EAAE,qBAAqB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACtD;oBAAE,IAAI,EAAE,qBAAqB;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;aAC1D;SACJ,CAAC;IACN,CAAC;;AA5aD;;;;GAIG,CACoB,0BAAA,yBAAyB,GAAG,GAAG,AAAN,CAAO;AAQhD,qKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;4DAC5B;uJAMlB,cAAA,EAAA;kKADN,YAAA,AAAS,EAAE;4DACiB;wJAMtB,aAAA,EAAA;QADN,sKAAA,AAAS,EAAE;4DACiB;wJAWtB,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;KACX,gLAAA,AAAgB,EAAC,kCAAkC,CAAC;oEAC0B;wJAUxE,aAAA,EAAA;KAFN,kLAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;0DACR;wJAStC,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8EACX;AASnC,qKAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;mEACC;wJAQ/C,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;IACX,iLAAA,AAAgB,EAAC,kCAAkC,CAAC;2EACd;wJAQhC,aAAA,EAAA;KAFN,kLAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8DACJ;CAQ1C,oKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;gEACxB;wJAOtB,aAAA,EAAA;kKADN,oBAAA,AAAiB,EAAE;4DACc;wJAQ3B,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;sEACmB;wJAOxB,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;gEACqB;wJAU1B,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8DACJ", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrIridescenceConfiguration.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrIridescenceConfiguration.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../../types\";\r\nimport { serialize, serializeAsTexture, expandToProperty } from \"../../Misc/decorators\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { MaterialFlags } from \"../materialFlags\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialIridescenceDefines extends MaterialDefines {\r\n    public IRIDESCENCE = false;\r\n    public IRIDESCENCE_TEXTURE = false;\r\n    public IRIDESCENCE_TEXTUREDIRECTUV = 0;\r\n    public IRIDESCENCE_THICKNESS_TEXTURE = false;\r\n    public IRIDESCENCE_THICKNESS_TEXTUREDIRECTUV = 0;\r\n}\r\n\r\n/**\r\n * Plugin that implements the iridescence (thin film) component of the PBR material\r\n */\r\nexport class PBRIridescenceConfiguration extends MaterialPluginBase {\r\n    protected override _material: PBRBaseMaterial;\r\n\r\n    /**\r\n     * The default minimum thickness of the thin-film layer given in nanometers (nm).\r\n     * Defaults to 100 nm.\r\n     * @internal\r\n     */\r\n    public static readonly _DefaultMinimumThickness = 100;\r\n\r\n    /**\r\n     * The default maximum thickness of the thin-film layer given in nanometers (nm).\r\n     * Defaults to 400 nm.\r\n     * @internal\r\n     */\r\n    public static readonly _DefaultMaximumThickness = 400;\r\n\r\n    /**\r\n     * The default index of refraction of the thin-film layer.\r\n     * Defaults to 1.3\r\n     * @internal\r\n     */\r\n    public static readonly _DefaultIndexOfRefraction = 1.3;\r\n\r\n    private _isEnabled = false;\r\n    /**\r\n     * Defines if the iridescence is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isEnabled = false;\r\n\r\n    /**\r\n     * Defines the iridescence layer strength (between 0 and 1) it defaults to 1.\r\n     */\r\n    @serialize()\r\n    public intensity: number = 1;\r\n\r\n    /**\r\n     * Defines the minimum thickness of the thin-film layer given in nanometers (nm).\r\n     */\r\n    @serialize()\r\n    public minimumThickness: number = PBRIridescenceConfiguration._DefaultMinimumThickness;\r\n\r\n    /**\r\n     * Defines the maximum thickness of the thin-film layer given in nanometers (nm). This will be the thickness used if not thickness texture has been set.\r\n     */\r\n    @serialize()\r\n    public maximumThickness: number = PBRIridescenceConfiguration._DefaultMaximumThickness;\r\n\r\n    /**\r\n     * Defines the maximum thickness of the thin-film layer given in nanometers (nm).\r\n     */\r\n    @serialize()\r\n    public indexOfRefraction: number = PBRIridescenceConfiguration._DefaultIndexOfRefraction;\r\n\r\n    private _texture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the iridescence intensity in a texture (red channel)\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public texture: Nullable<BaseTexture> = null;\r\n\r\n    private _thicknessTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the iridescence thickness in a texture (green channel)\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public thicknessTexture: Nullable<BaseTexture> = null;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the plugin is compatible with a given shader language.\r\n     * @returns true if the plugin is compatible with the shader language\r\n     */\r\n    public override isCompatible(): boolean {\r\n        return true;\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRIridescence\", 110, new MaterialIridescenceDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n    }\r\n\r\n    public override isReadyForSubMesh(defines: MaterialIridescenceDefines, scene: Scene): boolean {\r\n        if (!this._isEnabled) {\r\n            return true;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._texture && MaterialFlags.IridescenceTextureEnabled) {\r\n                    if (!this._texture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._thicknessTexture && MaterialFlags.IridescenceTextureEnabled) {\r\n                    if (!this._thicknessTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override prepareDefinesBeforeAttributes(defines: MaterialIridescenceDefines, scene: Scene): void {\r\n        if (this._isEnabled) {\r\n            defines.IRIDESCENCE = true;\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._texture && MaterialFlags.IridescenceTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._texture, defines, \"IRIDESCENCE_TEXTURE\");\r\n                    } else {\r\n                        defines.IRIDESCENCE_TEXTURE = false;\r\n                    }\r\n\r\n                    if (this._thicknessTexture && MaterialFlags.IridescenceTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._thicknessTexture, defines, \"IRIDESCENCE_THICKNESS_TEXTURE\");\r\n                    } else {\r\n                        defines.IRIDESCENCE_THICKNESS_TEXTURE = false;\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            defines.IRIDESCENCE = false;\r\n            defines.IRIDESCENCE_TEXTURE = false;\r\n            defines.IRIDESCENCE_THICKNESS_TEXTURE = false;\r\n            defines.IRIDESCENCE_TEXTUREDIRECTUV = 0;\r\n            defines.IRIDESCENCE_THICKNESS_TEXTUREDIRECTUV = 0;\r\n        }\r\n    }\r\n\r\n    public override bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene): void {\r\n        if (!this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if ((this._texture || this._thicknessTexture) && MaterialFlags.IridescenceTextureEnabled) {\r\n                uniformBuffer.updateFloat4(\r\n                    \"vIridescenceInfos\",\r\n                    this._texture?.coordinatesIndex ?? 0,\r\n                    this._texture?.level ?? 0,\r\n                    this._thicknessTexture?.coordinatesIndex ?? 0,\r\n                    this._thicknessTexture?.level ?? 0\r\n                );\r\n                if (this._texture) {\r\n                    BindTextureMatrix(this._texture, uniformBuffer, \"iridescence\");\r\n                }\r\n                if (this._thicknessTexture) {\r\n                    BindTextureMatrix(this._thicknessTexture, uniformBuffer, \"iridescenceThickness\");\r\n                }\r\n            }\r\n\r\n            // Clear Coat General params\r\n            uniformBuffer.updateFloat4(\"vIridescenceParams\", this.intensity, this.indexOfRefraction, this.minimumThickness, this.maximumThickness);\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._texture && MaterialFlags.IridescenceTextureEnabled) {\r\n                uniformBuffer.setTexture(\"iridescenceSampler\", this._texture);\r\n            }\r\n\r\n            if (this._thicknessTexture && MaterialFlags.IridescenceTextureEnabled) {\r\n                uniformBuffer.setTexture(\"iridescenceThicknessSampler\", this._thicknessTexture);\r\n            }\r\n        }\r\n    }\r\n\r\n    public override hasTexture(texture: BaseTexture): boolean {\r\n        if (this._texture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._thicknessTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public override getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._texture) {\r\n            activeTextures.push(this._texture);\r\n        }\r\n\r\n        if (this._thicknessTexture) {\r\n            activeTextures.push(this._thicknessTexture);\r\n        }\r\n    }\r\n\r\n    public override getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._texture && this._texture.animations && this._texture.animations.length > 0) {\r\n            animatables.push(this._texture);\r\n        }\r\n\r\n        if (this._thicknessTexture && this._thicknessTexture.animations && this._thicknessTexture.animations.length > 0) {\r\n            animatables.push(this._thicknessTexture);\r\n        }\r\n    }\r\n\r\n    public override dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            this._texture?.dispose();\r\n            this._thicknessTexture?.dispose();\r\n        }\r\n    }\r\n\r\n    public override getClassName(): string {\r\n        return \"PBRIridescenceConfiguration\";\r\n    }\r\n\r\n    public override addFallbacks(defines: MaterialIridescenceDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.IRIDESCENCE) {\r\n            fallbacks.addFallback(currentRank++, \"IRIDESCENCE\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public override getSamplers(samplers: string[]): void {\r\n        samplers.push(\"iridescenceSampler\", \"iridescenceThicknessSampler\");\r\n    }\r\n\r\n    public override getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vIridescenceParams\", size: 4, type: \"vec4\" },\r\n                { name: \"vIridescenceInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"iridescenceMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"iridescenceThicknessMatrix\", size: 16, type: \"mat4\" },\r\n            ],\r\n        };\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAExF,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAKjD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAIrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;;;;;;;AAKrF,MAAO,0BAA2B,6KAAQ,kBAAe;IAA/D,aAAA;;QACW,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,2BAA2B,GAAG,CAAC,CAAC;QAChC,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,qCAAqC,GAAG,CAAC,CAAC;IACrD,CAAC;CAAA;AAKK,MAAO,2BAA4B,gLAAQ,qBAAkB;IA2E/D,cAAA,EAAgB,CACT,gCAAgC,GAAA;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI,CAAA;QACzD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,EAAE,GAAG,EAAE,IAAI,0BAA0B,EAAE,EAAE,eAAe,CAAC,CAAC;QAlEtF,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAC3B;;WAEG,CAGI,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAEzB;;WAEG,CAEI,IAAA,CAAA,SAAS,GAAW,CAAC,CAAC;QAE7B;;WAEG,CAEI,IAAA,CAAA,gBAAgB,GAAW,2BAA2B,CAAC,wBAAwB,CAAC;QAEvF;;WAEG,CAEI,IAAA,CAAA,gBAAgB,GAAW,2BAA2B,CAAC,wBAAwB,CAAC;QAEvF;;WAEG,CAEI,IAAA,CAAA,iBAAiB,GAAW,2BAA2B,CAAC,yBAAyB,CAAC;QAEjF,IAAA,CAAA,QAAQ,GAA0B,IAAI,CAAC;QAC/C;;WAEG,CAGI,IAAA,CAAA,OAAO,GAA0B,IAAI,CAAC;QAErC,IAAA,CAAA,iBAAiB,GAA0B,IAAI,CAAC;QACxD;;WAEG,CAGI,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAsBlD,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAA,OAAS,CAAC,yBAAyB,CAAC,CAAC;IAClH,CAAC;IAEe,iBAAiB,CAAC,OAAmC,EAAE,KAAY,EAAA;QAC/E,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,yBAAyB,EAAE,CAAC;oBAC3D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBACxC,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,iBAAiB,sKAAI,gBAAa,CAAC,yBAAyB,EAAE,CAAC;oBACpE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBACjD,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,8BAA8B,CAAC,OAAmC,EAAE,KAAY,EAAA;QAC5F,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;YAE3B,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;oBACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,yBAAyB,EAAE,CAAC;4MAC3D,4BAAA,AAAyB,EAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;oBAC7E,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;oBACxC,CAAC;oBAED,IAAI,IAAI,CAAC,iBAAiB,qKAAI,iBAAa,CAAC,yBAAyB,EAAE,CAAC;4MACpE,4BAAA,AAAyB,EAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,+BAA+B,CAAC,CAAC;oBAChG,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;oBAClD,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;YAC5B,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;YAC9C,OAAO,CAAC,2BAA2B,GAAG,CAAC,CAAC;YACxC,OAAO,CAAC,qCAAqC,GAAG,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,aAA4B,EAAE,KAAY,EAAA;QACrE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEzC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC9D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,sKAAI,gBAAa,CAAC,yBAAyB,EAAE,CAAC;gBACvF,aAAa,CAAC,YAAY,CACtB,mBAAmB,EACnB,IAAI,CAAC,QAAQ,EAAE,gBAAgB,IAAI,CAAC,EACpC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,EACzB,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,CAAC,EAC7C,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,CACrC,CAAC;gBACF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wMAChB,oBAAA,AAAiB,EAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;gBACnE,CAAC;gBACD,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,wMAAA,AAAiB,EAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,EAAE,sBAAsB,CAAC,CAAC;gBACrF,CAAC;YACL,CAAC;YAED,4BAA4B;YAC5B,aAAa,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC3I,CAAC;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,yBAAyB,EAAE,CAAC;gBAC3D,aAAa,CAAC,UAAU,CAAC,oBAAoB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,IAAI,CAAC,iBAAiB,sKAAI,gBAAa,CAAC,yBAAyB,EAAE,CAAC;gBACpE,aAAa,CAAC,UAAU,CAAC,6BAA6B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACpF,CAAC;QACL,CAAC;IACL,CAAC;IAEe,UAAU,CAAC,OAAoB,EAAA;QAC3C,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEe,iBAAiB,CAAC,cAA6B,EAAA;QAC3D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,WAA0B,EAAA;QACrD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9G,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAEe,OAAO,CAAC,oBAA8B,EAAA;QAClD,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;QACtC,CAAC;IACL,CAAC;IAEe,YAAY,GAAA;QACxB,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAEe,YAAY,CAAC,OAAmC,EAAE,SAA0B,EAAE,WAAmB,EAAA;QAC7G,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,aAAa,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEe,WAAW,CAAC,QAAkB,EAAA;QAC1C,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,6BAA6B,CAAC,CAAC;IACvE,CAAC;IAEe,WAAW,GAAA;QACvB,OAAO;YACH,GAAG,EAAE;gBACD;oBAAE,IAAI,EAAE,oBAAoB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACrD;oBAAE,IAAI,EAAE,mBAAmB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACpD;oBAAE,IAAI,EAAE,mBAAmB;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACrD;oBAAE,IAAI,EAAE,4BAA4B;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;aACjE;SACJ,CAAC;IACN,CAAC;;AAvPD;;;;GAIG,CACoB,4BAAA,wBAAwB,GAAG,GAAG,AAAN,CAAO;AAEtD;;;;GAIG,CACoB,4BAAA,wBAAwB,GAAG,GAAG,AAAN,CAAO;AAEtD;;;;GAIG,CACoB,4BAAA,yBAAyB,GAAG,GAAG,AAAN,CAAO;wJAQhD,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;8DAC5B;wJAMlB,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;8DACiB;wJAMtB,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;qEAC2E;wJAMhF,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;qEAC2E;wJAMhF,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;sEAC6E;wJAQlF,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;4DACR;wJAQtC,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;qEACC", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrAnisotropicConfiguration.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrAnisotropicConfiguration.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serialize, expandToProperty, serializeAsVector2, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport { Vector2 } from \"../../Maths/math.vector\";\r\nimport { MaterialFlags } from \"../../Materials/materialFlags\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Scene } from \"../../scene\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialAnisotropicDefines extends MaterialDefines {\r\n    public ANISOTROPIC = false;\r\n    public ANISOTROPIC_TEXTURE = false;\r\n    public ANISOTROPIC_TEXTUREDIRECTUV = 0;\r\n    public ANISOTROPIC_LEGACY = false;\r\n    public MAINUV1 = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the anisotropic component of the PBR material\r\n */\r\nexport class PBRAnisotropicConfiguration extends MaterialPluginBase {\r\n    private _isEnabled = false;\r\n    /**\r\n     * Defines if the anisotropy is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isEnabled = false;\r\n\r\n    /**\r\n     * Defines the anisotropy strength (between 0 and 1) it defaults to 1.\r\n     */\r\n    @serialize()\r\n    public intensity: number = 1;\r\n\r\n    /**\r\n     * Defines if the effect is along the tangents, bitangents or in between.\r\n     * By default, the effect is \"stretching\" the highlights along the tangents.\r\n     */\r\n    @serializeAsVector2()\r\n    public direction = new Vector2(1, 0);\r\n\r\n    /**\r\n     * Sets the anisotropy direction as an angle.\r\n     */\r\n    public set angle(value: number) {\r\n        this.direction.x = Math.cos(value);\r\n        this.direction.y = Math.sin(value);\r\n    }\r\n\r\n    /**\r\n     * Gets the anisotropy angle value in radians.\r\n     * @returns the anisotropy angle value in radians.\r\n     */\r\n    public get angle(): number {\r\n        return Math.atan2(this.direction.y, this.direction.x);\r\n    }\r\n\r\n    private _texture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the anisotropy values in a texture.\r\n     * rg is direction (like normal from -1 to 1)\r\n     * b is a intensity\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public texture: Nullable<BaseTexture> = null;\r\n\r\n    private _legacy = false;\r\n    /**\r\n     * Defines if the anisotropy is in legacy mode for backwards compatibility before 6.4.0.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public legacy: boolean = false;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsMiscDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsMiscDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsMiscDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the plugin is compatible with a given shader language.\r\n     * @returns true if the plugin is compatible with the shader language\r\n     */\r\n    public override isCompatible(): boolean {\r\n        return true;\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRAnisotropic\", 110, new MaterialAnisotropicDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n        this._internalMarkAllSubMeshesAsMiscDirty = material._dirtyCallbacks[Constants.MATERIAL_MiscDirtyFlag];\r\n    }\r\n\r\n    public override isReadyForSubMesh(defines: MaterialAnisotropicDefines, scene: Scene): boolean {\r\n        if (!this._isEnabled) {\r\n            return true;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._texture && MaterialFlags.AnisotropicTextureEnabled) {\r\n                    if (!this._texture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override prepareDefinesBeforeAttributes(defines: MaterialAnisotropicDefines, scene: Scene, mesh: AbstractMesh): void {\r\n        if (this._isEnabled) {\r\n            defines.ANISOTROPIC = this._isEnabled;\r\n            if (this._isEnabled && !mesh.isVerticesDataPresent(VertexBuffer.TangentKind)) {\r\n                defines._needUVs = true;\r\n                defines.MAINUV1 = true;\r\n            }\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._texture && MaterialFlags.AnisotropicTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._texture, defines, \"ANISOTROPIC_TEXTURE\");\r\n                    } else {\r\n                        defines.ANISOTROPIC_TEXTURE = false;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (defines._areMiscDirty) {\r\n                defines.ANISOTROPIC_LEGACY = this._legacy;\r\n            }\r\n        } else {\r\n            defines.ANISOTROPIC = false;\r\n            defines.ANISOTROPIC_TEXTURE = false;\r\n            defines.ANISOTROPIC_TEXTUREDIRECTUV = 0;\r\n            defines.ANISOTROPIC_LEGACY = false;\r\n        }\r\n    }\r\n\r\n    public override bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene): void {\r\n        if (!this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if (this._texture && MaterialFlags.AnisotropicTextureEnabled) {\r\n                uniformBuffer.updateFloat2(\"vAnisotropyInfos\", this._texture.coordinatesIndex, this._texture.level);\r\n                BindTextureMatrix(this._texture, uniformBuffer, \"anisotropy\");\r\n            }\r\n\r\n            // Anisotropy\r\n            uniformBuffer.updateFloat3(\"vAnisotropy\", this.direction.x, this.direction.y, this.intensity);\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._texture && MaterialFlags.AnisotropicTextureEnabled) {\r\n                uniformBuffer.setTexture(\"anisotropySampler\", this._texture);\r\n            }\r\n        }\r\n    }\r\n\r\n    public override hasTexture(texture: BaseTexture): boolean {\r\n        if (this._texture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public override getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._texture) {\r\n            activeTextures.push(this._texture);\r\n        }\r\n    }\r\n\r\n    public override getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._texture && this._texture.animations && this._texture.animations.length > 0) {\r\n            animatables.push(this._texture);\r\n        }\r\n    }\r\n\r\n    public override dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            if (this._texture) {\r\n                this._texture.dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public override getClassName(): string {\r\n        return \"PBRAnisotropicConfiguration\";\r\n    }\r\n\r\n    public override addFallbacks(defines: MaterialAnisotropicDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.ANISOTROPIC) {\r\n            fallbacks.addFallback(currentRank++, \"ANISOTROPIC\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public override getSamplers(samplers: string[]): void {\r\n        samplers.push(\"anisotropySampler\");\r\n    }\r\n\r\n    public override getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vAnisotropy\", size: 3, type: \"vec3\" },\r\n                { name: \"vAnisotropyInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"anisotropyMatrix\", size: 16, type: \"mat4\" },\r\n            ],\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Parses a anisotropy Configuration from a serialized object.\r\n     * @param source - Serialized object.\r\n     * @param scene Defines the scene we are parsing for\r\n     * @param rootUrl Defines the rootUrl to load from\r\n     */\r\n    public override parse(source: any, scene: Scene, rootUrl: string): void {\r\n        super.parse(source, scene, rootUrl);\r\n\r\n        // Backward compatibility\r\n        if (source.legacy === undefined) {\r\n            this.legacy = true;\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA,uDAAA,EAAyD,CACzD,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE5G,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AACpD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAK9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE3D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAKrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;;;;;;;;;AAKrF,MAAO,0BAA2B,6KAAQ,kBAAe;IAA/D,aAAA;;QACW,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,2BAA2B,GAAG,CAAC,CAAC;QAChC,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;IAC3B,CAAC;CAAA;AAKK,MAAO,2BAA4B,gLAAQ,qBAAkB;IAsB/D;;OAEG,CACH,IAAW,KAAK,CAAC,KAAa,EAAA;QAC1B,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;IAuBD,cAAA,EAAgB,CACT,gCAAgC,GAAA;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IAKD,cAAA,EAAgB,CACT,4BAA4B,GAAA;QAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,oCAAoC,EAAE,CAAC;IAChD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI,CAAA;QACzD,KAAK,CAAC,QAAQ,EAAE,gBAAgB,EAAE,GAAG,EAAE,IAAI,0BAA0B,EAAE,EAAE,eAAe,CAAC,CAAC;QAlFtF,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAC3B;;WAEG,CAGI,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAEzB;;WAEG,CAEI,IAAA,CAAA,SAAS,GAAW,CAAC,CAAC;QAE7B;;;WAGG,CAEI,IAAA,CAAA,SAAS,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAkB7B,IAAA,CAAA,QAAQ,GAA0B,IAAI,CAAC;QAC/C;;;;WAIG,CAGI,IAAA,CAAA,OAAO,GAA0B,IAAI,CAAC;QAErC,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QACxB;;WAEG,CAGI,IAAA,CAAA,MAAM,GAAY,KAAK,CAAC;QA+B3B,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAA,OAAS,CAAC,yBAAyB,CAAC,CAAC;QAC9G,IAAI,CAAC,oCAAoC,GAAG,QAAQ,CAAC,eAAe,CAAC,GAAA,MAAS,CAAC,sBAAsB,CAAC,CAAC;IAC3G,CAAC;IAEe,iBAAiB,CAAC,OAAmC,EAAE,KAAY,EAAA;QAC/E,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,yBAAyB,EAAE,CAAC;oBAC3D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBACxC,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,8BAA8B,CAAC,OAAmC,EAAE,KAAY,EAAE,IAAkB,EAAA;QAChH,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YACtC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC3E,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;YAC3B,CAAC;YAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;oBACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,yBAAyB,EAAE,CAAC;4MAC3D,4BAAA,AAAyB,EAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;oBAC7E,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;oBACxC,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBACxB,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC;YAC9C,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;YAC5B,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,2BAA2B,GAAG,CAAC,CAAC;YACxC,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACvC,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,aAA4B,EAAE,KAAY,EAAA;QACrE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEzC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC9D,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,yBAAyB,EAAE,CAAC;gBAC3D,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oMACpG,oBAAA,AAAiB,EAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;YAClE,CAAC;YAED,aAAa;YACb,aAAa,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAClG,CAAC;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,QAAQ,qKAAI,iBAAa,CAAC,yBAAyB,EAAE,CAAC;gBAC3D,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjE,CAAC;QACL,CAAC;IACL,CAAC;IAEe,UAAU,CAAC,OAAoB,EAAA;QAC3C,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEe,iBAAiB,CAAC,cAA6B,EAAA;QAC3D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,WAA0B,EAAA;QACrD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;IACL,CAAC;IAEe,OAAO,CAAC,oBAA8B,EAAA;QAClD,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC5B,CAAC;QACL,CAAC;IACL,CAAC;IAEe,YAAY,GAAA;QACxB,OAAO,6BAA6B,CAAC;IACzC,CAAC;IAEe,YAAY,CAAC,OAAmC,EAAE,SAA0B,EAAE,WAAmB,EAAA;QAC7G,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,aAAa,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEe,WAAW,CAAC,QAAkB,EAAA;QAC1C,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACvC,CAAC;IAEe,WAAW,GAAA;QACvB,OAAO;YACH,GAAG,EAAE;gBACD;oBAAE,IAAI,EAAE,aAAa;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC9C;oBAAE,IAAI,EAAE,kBAAkB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACnD;oBAAE,IAAI,EAAE,kBAAkB;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;aACvD;SACJ,CAAC;IACN,CAAC;IAED;;;;;OAKG,CACa,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe,EAAA;QAC5D,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEpC,yBAAyB;QACzB,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACvB,CAAC;IACL,CAAC;CACJ;wJA7NU,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;IACX,iLAAA,AAAgB,EAAC,kCAAkC,CAAC;8DAC5B;wJAMlB,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;8DACiB;wJAOtB,aAAA,EAAA;IADN,mLAAA,AAAkB,EAAE;8DACgB;wJA0B9B,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;4DACR;wJAQtC,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;2DAClB", "debugId": null}}, {"offset": {"line": 1132, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrSheenConfiguration.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrSheenConfiguration.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serialize, expandToProperty, serializeAsColor3, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport { MaterialFlags } from \"../../Materials/materialFlags\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialSheenDefines extends MaterialDefines {\r\n    public SHEEN = false;\r\n    public SHEEN_TEXTURE = false;\r\n    public SHEEN_GAMMATEXTURE = false;\r\n    public SHEEN_TEXTURE_ROUGHNESS = false;\r\n    public SHEEN_TEXTUREDIRECTUV = 0;\r\n    public SHEEN_TEXTURE_ROUGHNESSDIRECTUV = 0;\r\n    public SHEEN_LINKWITHALBEDO = false;\r\n    public SHEEN_ROUGHNESS = false;\r\n    public SHEEN_ALBEDOSCALING = false;\r\n    public SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the sheen component of the PBR material.\r\n */\r\nexport class PBRSheenConfiguration extends MaterialPluginBase {\r\n    private _isEnabled = false;\r\n    /**\r\n     * Defines if the material uses sheen.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isEnabled = false;\r\n\r\n    private _linkSheenWithAlbedo = false;\r\n    /**\r\n     * Defines if the sheen is linked to the sheen color.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public linkSheenWithAlbedo = false;\r\n\r\n    /**\r\n     * Defines the sheen intensity.\r\n     */\r\n    @serialize()\r\n    public intensity = 1;\r\n\r\n    /**\r\n     * Defines the sheen color.\r\n     */\r\n    @serializeAsColor3()\r\n    public color = Color3.White();\r\n\r\n    private _texture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the sheen tint values in a texture.\r\n     * rgb is tint\r\n     * a is a intensity or roughness if the roughness property has been defined and useRoughnessFromTexture is true (in that case, textureRoughness won't be used)\r\n     * If the roughness property has been defined and useRoughnessFromTexture is false then the alpha channel is not used to modulate roughness\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public texture: Nullable<BaseTexture> = null;\r\n\r\n    private _useRoughnessFromMainTexture = true;\r\n    /**\r\n     * Indicates that the alpha channel of the texture property will be used for roughness.\r\n     * Has no effect if the roughness (and texture!) property is not defined\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRoughnessFromMainTexture = true;\r\n\r\n    private _roughness: Nullable<number> = null;\r\n    /**\r\n     * Defines the sheen roughness.\r\n     * It is not taken into account if linkSheenWithAlbedo is true.\r\n     * To stay backward compatible, material roughness is used instead if sheen roughness = null\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public roughness: Nullable<number> = null;\r\n\r\n    private _textureRoughness: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the sheen roughness in a texture.\r\n     * alpha channel is the roughness. This texture won't be used if the texture property is not empty and useRoughnessFromTexture is true\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public textureRoughness: Nullable<BaseTexture> = null;\r\n\r\n    private _albedoScaling = false;\r\n    /**\r\n     * If true, the sheen effect is layered above the base BRDF with the albedo-scaling technique.\r\n     * It allows the strength of the sheen effect to not depend on the base color of the material,\r\n     * making it easier to setup and tweak the effect\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public albedoScaling = false;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the plugin is compatible with a given shader language.\r\n     * @returns true if the plugin is compatible with the shader language\r\n     */\r\n    public override isCompatible(): boolean {\r\n        return true;\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"Sheen\", 120, new MaterialSheenDefines(), addToPluginList);\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n    }\r\n\r\n    public override isReadyForSubMesh(defines: MaterialSheenDefines, scene: Scene): boolean {\r\n        if (!this._isEnabled) {\r\n            return true;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._texture && MaterialFlags.SheenTextureEnabled) {\r\n                    if (!this._texture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._textureRoughness && MaterialFlags.SheenTextureEnabled) {\r\n                    if (!this._textureRoughness.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override prepareDefinesBeforeAttributes(defines: MaterialSheenDefines, scene: Scene): void {\r\n        if (this._isEnabled) {\r\n            defines.SHEEN = true;\r\n            defines.SHEEN_LINKWITHALBEDO = this._linkSheenWithAlbedo;\r\n            defines.SHEEN_ROUGHNESS = this._roughness !== null;\r\n            defines.SHEEN_ALBEDOSCALING = this._albedoScaling;\r\n            defines.SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE = this._useRoughnessFromMainTexture;\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._texture && MaterialFlags.SheenTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._texture, defines, \"SHEEN_TEXTURE\");\r\n                        defines.SHEEN_GAMMATEXTURE = this._texture.gammaSpace;\r\n                    } else {\r\n                        defines.SHEEN_TEXTURE = false;\r\n                    }\r\n\r\n                    if (this._textureRoughness && MaterialFlags.SheenTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._textureRoughness, defines, \"SHEEN_TEXTURE_ROUGHNESS\");\r\n                    } else {\r\n                        defines.SHEEN_TEXTURE_ROUGHNESS = false;\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            defines.SHEEN = false;\r\n            defines.SHEEN_TEXTURE = false;\r\n            defines.SHEEN_TEXTURE_ROUGHNESS = false;\r\n            defines.SHEEN_LINKWITHALBEDO = false;\r\n            defines.SHEEN_ROUGHNESS = false;\r\n            defines.SHEEN_ALBEDOSCALING = false;\r\n            defines.SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE = false;\r\n            defines.SHEEN_GAMMATEXTURE = false;\r\n            defines.SHEEN_TEXTUREDIRECTUV = 0;\r\n            defines.SHEEN_TEXTURE_ROUGHNESSDIRECTUV = 0;\r\n        }\r\n    }\r\n\r\n    public override bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene, engine: Engine, subMesh: SubMesh): void {\r\n        if (!this._isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const defines = subMesh.materialDefines as unknown as MaterialSheenDefines;\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if ((this._texture || this._textureRoughness) && MaterialFlags.SheenTextureEnabled) {\r\n                uniformBuffer.updateFloat4(\r\n                    \"vSheenInfos\",\r\n                    this._texture?.coordinatesIndex ?? 0,\r\n                    this._texture?.level ?? 0,\r\n                    this._textureRoughness?.coordinatesIndex ?? 0,\r\n                    this._textureRoughness?.level ?? 0\r\n                );\r\n                if (this._texture) {\r\n                    BindTextureMatrix(this._texture, uniformBuffer, \"sheen\");\r\n                }\r\n                if (this._textureRoughness && !defines.SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE) {\r\n                    BindTextureMatrix(this._textureRoughness, uniformBuffer, \"sheenRoughness\");\r\n                }\r\n            }\r\n\r\n            // Sheen\r\n            uniformBuffer.updateFloat4(\"vSheenColor\", this.color.r, this.color.g, this.color.b, this.intensity);\r\n\r\n            if (this._roughness !== null) {\r\n                uniformBuffer.updateFloat(\"vSheenRoughness\", this._roughness);\r\n            }\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._texture && MaterialFlags.SheenTextureEnabled) {\r\n                uniformBuffer.setTexture(\"sheenSampler\", this._texture);\r\n            }\r\n\r\n            if (this._textureRoughness && !defines.SHEEN_USE_ROUGHNESS_FROM_MAINTEXTURE && MaterialFlags.SheenTextureEnabled) {\r\n                uniformBuffer.setTexture(\"sheenRoughnessSampler\", this._textureRoughness);\r\n            }\r\n        }\r\n    }\r\n\r\n    public override hasTexture(texture: BaseTexture): boolean {\r\n        if (this._texture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._textureRoughness === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public override getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._texture) {\r\n            activeTextures.push(this._texture);\r\n        }\r\n\r\n        if (this._textureRoughness) {\r\n            activeTextures.push(this._textureRoughness);\r\n        }\r\n    }\r\n\r\n    public override getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._texture && this._texture.animations && this._texture.animations.length > 0) {\r\n            animatables.push(this._texture);\r\n        }\r\n\r\n        if (this._textureRoughness && this._textureRoughness.animations && this._textureRoughness.animations.length > 0) {\r\n            animatables.push(this._textureRoughness);\r\n        }\r\n    }\r\n\r\n    public override dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            this._texture?.dispose();\r\n            this._textureRoughness?.dispose();\r\n        }\r\n    }\r\n\r\n    public override getClassName(): string {\r\n        return \"PBRSheenConfiguration\";\r\n    }\r\n\r\n    public override addFallbacks(defines: MaterialSheenDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.SHEEN) {\r\n            fallbacks.addFallback(currentRank++, \"SHEEN\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public override getSamplers(samplers: string[]): void {\r\n        samplers.push(\"sheenSampler\", \"sheenRoughnessSampler\");\r\n    }\r\n\r\n    public override getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vSheenColor\", size: 4, type: \"vec4\" },\r\n                { name: \"vSheenRoughness\", size: 1, type: \"float\" },\r\n                { name: \"vSheenInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"sheenMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"sheenRoughnessMatrix\", size: 16, type: \"mat4\" },\r\n            ],\r\n        };\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA,uDAAA,EAAyD,CACzD,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE3G,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAO9D,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAKrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;;;;;;;;AAKrF,MAAO,oBAAqB,6KAAQ,kBAAe;IAAzD,aAAA;;QACW,IAAA,CAAA,KAAK,GAAG,KAAK,CAAC;QACd,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QACtB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC;QAC1B,IAAA,CAAA,+BAA+B,GAAG,CAAC,CAAC;QACpC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,oCAAoC,GAAG,KAAK,CAAC;IACxD,CAAC;CAAA;AAKK,MAAO,qBAAsB,gLAAQ,qBAAkB;IAiFzD,cAAA,EAAgB,CACT,gCAAgC,GAAA;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI,CAAA;QACzD,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,oBAAoB,EAAE,EAAE,eAAe,CAAC,CAAC;QA/FvE,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAC3B;;WAEG,CAGI,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAEjB,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QACrC;;WAEG,CAGI,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAEnC;;WAEG,CAEI,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QAErB;;WAEG,CAEI,IAAA,CAAA,KAAK,iKAAG,SAAM,CAAC,KAAK,EAAE,CAAC;QAEtB,IAAA,CAAA,QAAQ,GAA0B,IAAI,CAAC;QAC/C;;;;;WAKG,CAGI,IAAA,CAAA,OAAO,GAA0B,IAAI,CAAC;QAErC,IAAA,CAAA,4BAA4B,GAAG,IAAI,CAAC;QAC5C;;;WAGG,CAGI,IAAA,CAAA,2BAA2B,GAAG,IAAI,CAAC;QAElC,IAAA,CAAA,UAAU,GAAqB,IAAI,CAAC;QAC5C;;;;WAIG,CAGI,IAAA,CAAA,SAAS,GAAqB,IAAI,CAAC;QAElC,IAAA,CAAA,iBAAiB,GAA0B,IAAI,CAAC;QACxD;;;WAGG,CAGI,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAE9C,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAC/B;;;;WAIG,CAGI,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QAsBzB,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAA,OAAS,CAAC,yBAAyB,CAAC,CAAC;IAClH,CAAC;IAEe,iBAAiB,CAAC,OAA6B,EAAE,KAAY,EAAA;QACzE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,mBAAmB,EAAE,CAAC;oBACrD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBACxC,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,iBAAiB,sKAAI,gBAAa,CAAC,mBAAmB,EAAE,CAAC;oBAC9D,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBACjD,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,8BAA8B,CAAC,OAA6B,EAAE,KAAY,EAAA;QACtF,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YACrB,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACzD,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC;YACnD,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC;YAClD,OAAO,CAAC,oCAAoC,GAAG,IAAI,CAAC,4BAA4B,CAAC;YAEjF,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;oBACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,mBAAmB,EAAE,CAAC;4MACrD,4BAAA,AAAyB,EAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;wBACnE,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;oBAC1D,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;oBAClC,CAAC;oBAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,kLAAa,CAAC,mBAAmB,EAAE,CAAC;4MAC9D,4BAAA,AAAyB,EAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,yBAAyB,CAAC,CAAC;oBAC1F,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;oBAC5C,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;YACtB,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;YACxC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;YACrC,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;YAChC,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,oCAAoC,GAAG,KAAK,CAAC;YACrD,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,qBAAqB,GAAG,CAAC,CAAC;YAClC,OAAO,CAAC,+BAA+B,GAAG,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,aAA4B,EAAE,KAAY,EAAE,MAAc,EAAE,OAAgB,EAAA;QACvG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,eAAkD,CAAC;QAE3E,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEzC,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC9D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,CAAC,sKAAI,gBAAa,CAAC,mBAAmB,EAAE,CAAC;gBACjF,aAAa,CAAC,YAAY,CACtB,aAAa,EACb,IAAI,CAAC,QAAQ,EAAE,gBAAgB,IAAI,CAAC,EACpC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,EACzB,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,IAAI,CAAC,EAC7C,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,CACrC,CAAC;gBACF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wMAChB,oBAAA,AAAiB,EAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;gBAC7D,CAAC;gBACD,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,oCAAoC,EAAE,CAAC;wMAC1E,oBAAA,AAAiB,EAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;gBAC/E,CAAC;YACL,CAAC;YAED,QAAQ;YACR,aAAa,CAAC,YAAY,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAEpG,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBAC3B,aAAa,CAAC,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAClE,CAAC;QACL,CAAC;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,QAAQ,sKAAI,gBAAa,CAAC,mBAAmB,EAAE,CAAC;gBACrD,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,oCAAoC,sKAAI,gBAAa,CAAC,mBAAmB,EAAE,CAAC;gBAC/G,aAAa,CAAC,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;IACL,CAAC;IAEe,UAAU,CAAC,OAAoB,EAAA;QAC3C,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEe,iBAAiB,CAAC,cAA6B,EAAA;QAC3D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,WAA0B,EAAA;QACrD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnF,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9G,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAEe,OAAO,CAAC,oBAA8B,EAAA;QAClD,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;QACtC,CAAC;IACL,CAAC;IAEe,YAAY,GAAA;QACxB,OAAO,uBAAuB,CAAC;IACnC,CAAC;IAEe,YAAY,CAAC,OAA6B,EAAE,SAA0B,EAAE,WAAmB,EAAA;QACvG,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,OAAO,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEe,WAAW,CAAC,QAAkB,EAAA;QAC1C,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC;IAC3D,CAAC;IAEe,WAAW,GAAA;QACvB,OAAO;YACH,GAAG,EAAE;gBACD;oBAAE,IAAI,EAAE,aAAa;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC9C;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,OAAO;gBAAA,CAAE;gBACnD;oBAAE,IAAI,EAAE,aAAa;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC9C;oBAAE,IAAI,EAAE,aAAa;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC/C;oBAAE,IAAI,EAAE,sBAAsB;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;aAC3D;SACJ,CAAC;IACN,CAAC;CACJ;CA3QU,oKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;wDAC5B;AAQlB,qKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;iKACX,oBAAA,AAAgB,EAAC,kCAAkC,CAAC;kEAClB;wJAM5B,aAAA,EAAA;QADN,sKAAA,AAAS,EAAE;wDACS;wJAMd,aAAA,EAAA;kKADN,oBAAA,AAAiB,EAAE;oDACU;wJAWvB,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;sDACR;wJAStC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;0EACX;wJAUnC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;KACX,gLAAA,AAAgB,EAAC,kCAAkC,CAAC;wDACX;wJASnC,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;+DACC;wJAU/C,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;4DACxB", "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrSubSurfaceConfiguration.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrSubSurfaceConfiguration.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\nimport { serialize, serializeAsTexture, expandToProperty, serializeAsColor3 } from \"../../Misc/decorators\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport type { SmartArray } from \"../../Misc/smartArray\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport { MaterialFlags } from \"../materialFlags\";\r\nimport type { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport type { EffectFallbacks } from \"../effectFallbacks\";\r\nimport type { CubeTexture } from \"../Textures/cubeTexture\";\r\nimport { TmpVectors } from \"../../Maths/math.vector\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport { MaterialPluginBase } from \"../materialPluginBase\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport { MaterialDefines } from \"../materialDefines\";\r\n\r\nimport type { Engine } from \"../../Engines/engine\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { BindTextureMatrix, PrepareDefinesForMergedUV } from \"../materialHelper.functions\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class MaterialSubSurfaceDefines extends MaterialDefines {\r\n    public SUBSURFACE = false;\r\n\r\n    public SS_REFRACTION = false;\r\n    public SS_REFRACTION_USE_INTENSITY_FROM_THICKNESS = false;\r\n    public SS_TRANSLUCENCY = false;\r\n    public SS_TRANSLUCENCY_USE_INTENSITY_FROM_THICKNESS = false;\r\n    public SS_SCATTERING = false;\r\n    public SS_DISPERSION = false;\r\n\r\n    public SS_THICKNESSANDMASK_TEXTURE = false;\r\n    public SS_THICKNESSANDMASK_TEXTUREDIRECTUV = 0;\r\n    public SS_HAS_THICKNESS = false;\r\n    public SS_REFRACTIONINTENSITY_TEXTURE = false;\r\n    public SS_REFRACTIONINTENSITY_TEXTUREDIRECTUV = 0;\r\n    public SS_TRANSLUCENCYINTENSITY_TEXTURE = false;\r\n    public SS_TRANSLUCENCYINTENSITY_TEXTUREDIRECTUV = 0;\r\n    public SS_TRANSLUCENCYCOLOR_TEXTURE = false;\r\n    public SS_TRANSLUCENCYCOLOR_TEXTUREDIRECTUV = 0;\r\n    public SS_TRANSLUCENCYCOLOR_TEXTURE_GAMMA = false;\r\n\r\n    public SS_REFRACTIONMAP_3D = false;\r\n    public SS_REFRACTIONMAP_OPPOSITEZ = false;\r\n    public SS_LODINREFRACTIONALPHA = false;\r\n    public SS_GAMMAREFRACTION = false;\r\n    public SS_RGBDREFRACTION = false;\r\n    public SS_LINEARSPECULARREFRACTION = false;\r\n    public SS_LINKREFRACTIONTOTRANSPARENCY = false;\r\n    public SS_ALBEDOFORREFRACTIONTINT = false;\r\n    public SS_ALBEDOFORTRANSLUCENCYTINT = false;\r\n    public SS_USE_LOCAL_REFRACTIONMAP_CUBIC = false;\r\n    public SS_USE_THICKNESS_AS_DEPTH = false;\r\n\r\n    public SS_USE_GLTF_TEXTURES = false;\r\n    public SS_APPLY_ALBEDO_AFTER_SUBSURFACE = false;\r\n    public SS_TRANSLUCENCY_LEGACY = false;\r\n}\r\n\r\n/**\r\n * Plugin that implements the sub surface component of the PBR material\r\n */\r\nexport class PBRSubSurfaceConfiguration extends MaterialPluginBase {\r\n    /**\r\n     * Default value used for applyAlbedoAfterSubSurface.\r\n     *\r\n     * This property only exists for backward compatibility reasons.\r\n     * Set it to true if your rendering in 8.0+ is different from that in 7 when you use sub-surface properties (transmission, refraction, etc.). Default is false.\r\n     * Note however that the PBR calculation is wrong when this property is set to true, so only use it if you want to mimic the 7.0 behavior.\r\n     */\r\n    public static DEFAULT_APPLY_ALBEDO_AFTERSUBSURFACE = false;\r\n\r\n    /**\r\n     * Default value used for legacyTranslucency.\r\n     *\r\n     * This property only exists for backward compatibility reasons.\r\n     * Set it to true if your rendering in 8.0+ is different from that in 7 when you use sub-surface translucency. Default is false.\r\n     */\r\n    public static DEFAULT_LEGACY_TRANSLUCENCY = false;\r\n\r\n    protected override _material: PBRBaseMaterial;\r\n\r\n    private _isRefractionEnabled = false;\r\n    /**\r\n     * Defines if the refraction is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isRefractionEnabled = false;\r\n\r\n    private _isTranslucencyEnabled = false;\r\n    /**\r\n     * Defines if the translucency is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isTranslucencyEnabled = false;\r\n\r\n    private _isDispersionEnabled = false;\r\n    /**\r\n     * Defines if dispersion is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public isDispersionEnabled = false;\r\n\r\n    private _isScatteringEnabled = false;\r\n    /**\r\n     * Defines if the sub surface scattering is enabled in the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markScenePrePassDirty\")\r\n    public isScatteringEnabled = false;\r\n\r\n    @serialize()\r\n    private _scatteringDiffusionProfileIndex = 0;\r\n\r\n    /**\r\n     * Diffusion profile for subsurface scattering.\r\n     * Useful for better scattering in the skins or foliages.\r\n     */\r\n    public get scatteringDiffusionProfile(): Nullable<Color3> {\r\n        if (!this._scene.subSurfaceConfiguration) {\r\n            return null;\r\n        }\r\n\r\n        return this._scene.subSurfaceConfiguration.ssDiffusionProfileColors[this._scatteringDiffusionProfileIndex];\r\n    }\r\n\r\n    public set scatteringDiffusionProfile(c: Nullable<Color3>) {\r\n        if (!this._scene.enableSubSurfaceForPrePass()) {\r\n            // Not supported\r\n            return;\r\n        }\r\n\r\n        // addDiffusionProfile automatically checks for doubles\r\n        if (c) {\r\n            this._scatteringDiffusionProfileIndex = this._scene.subSurfaceConfiguration!.addDiffusionProfile(c);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Defines the refraction intensity of the material.\r\n     * The refraction when enabled replaces the Diffuse part of the material.\r\n     * The intensity helps transitioning between diffuse and refraction.\r\n     */\r\n    @serialize()\r\n    public refractionIntensity: number = 1;\r\n\r\n    /**\r\n     * Defines the translucency intensity of the material.\r\n     * When translucency has been enabled, this defines how much of the \"translucency\"\r\n     * is added to the diffuse part of the material.\r\n     */\r\n    @serialize()\r\n    public translucencyIntensity: number = 1;\r\n\r\n    /**\r\n     * When enabled, transparent surfaces will be tinted with the albedo colour (independent of thickness)\r\n     */\r\n    @serialize()\r\n    public useAlbedoToTintRefraction: boolean = false;\r\n\r\n    /**\r\n     * When enabled, translucent surfaces will be tinted with the albedo colour (independent of thickness)\r\n     */\r\n    @serialize()\r\n    public useAlbedoToTintTranslucency: boolean = false;\r\n\r\n    private _thicknessTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the average thickness of a mesh in a texture (The texture is holding the values linearly).\r\n     * The red (or green if useGltfStyleTextures=true) channel of the texture should contain the thickness remapped between 0 and 1.\r\n     * 0 would mean minimumThickness\r\n     * 1 would mean maximumThickness\r\n     * The other channels might be use as a mask to vary the different effects intensity.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public thicknessTexture: Nullable<BaseTexture> = null;\r\n\r\n    private _refractionTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Defines the texture to use for refraction.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public refractionTexture: Nullable<BaseTexture> = null;\r\n\r\n    /** @internal */\r\n    public _indexOfRefraction = 1.5;\r\n    /**\r\n     * Index of refraction of the material base layer.\r\n     * https://en.wikipedia.org/wiki/List_of_refractive_indices\r\n     *\r\n     * This does not only impact refraction but also the Base F0 of Dielectric Materials.\r\n     *\r\n     * From dielectric fresnel rules: F0 = square((iorT - iorI) / (iorT + iorI))\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public indexOfRefraction = 1.5;\r\n\r\n    @serialize()\r\n    private _volumeIndexOfRefraction = -1.0;\r\n\r\n    /**\r\n     * Index of refraction of the material's volume.\r\n     * https://en.wikipedia.org/wiki/List_of_refractive_indices\r\n     *\r\n     * This ONLY impacts refraction. If not provided or given a non-valid value,\r\n     * the volume will use the same IOR as the surface.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public get volumeIndexOfRefraction(): number {\r\n        if (this._volumeIndexOfRefraction >= 1.0) {\r\n            return this._volumeIndexOfRefraction;\r\n        }\r\n        return this._indexOfRefraction;\r\n    }\r\n    public set volumeIndexOfRefraction(value: number) {\r\n        if (value >= 1.0) {\r\n            this._volumeIndexOfRefraction = value;\r\n        } else {\r\n            this._volumeIndexOfRefraction = -1.0;\r\n        }\r\n    }\r\n\r\n    private _invertRefractionY = false;\r\n    /**\r\n     * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertRefractionY = false;\r\n\r\n    /** @internal */\r\n    public _linkRefractionWithTransparency = false;\r\n    /**\r\n     * This parameters will make the material used its opacity to control how much it is refracting against not.\r\n     * Materials half opaque for instance using refraction could benefit from this control.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public linkRefractionWithTransparency = false;\r\n\r\n    /**\r\n     * Defines the minimum thickness stored in the thickness map.\r\n     * If no thickness map is defined, this value will be used to simulate thickness.\r\n     */\r\n    @serialize()\r\n    public minimumThickness: number = 0;\r\n\r\n    /**\r\n     * Defines the maximum thickness stored in the thickness map.\r\n     */\r\n    @serialize()\r\n    public maximumThickness: number = 1;\r\n\r\n    /**\r\n     * Defines that the thickness should be used as a measure of the depth volume.\r\n     */\r\n    @serialize()\r\n    public useThicknessAsDepth = false;\r\n\r\n    /**\r\n     * Defines the volume tint of the material.\r\n     * This is used for both translucency and scattering.\r\n     */\r\n    @serializeAsColor3()\r\n    public tintColor = Color3.White();\r\n\r\n    /**\r\n     * Defines the distance at which the tint color should be found in the media.\r\n     * This is used for refraction only.\r\n     */\r\n    @serialize()\r\n    public tintColorAtDistance = 1;\r\n\r\n    /**\r\n     * Defines the Abbe number for the volume.\r\n     */\r\n    @serialize()\r\n    public dispersion = 0;\r\n\r\n    /**\r\n     * Defines how far each channel transmit through the media.\r\n     * It is defined as a color to simplify it selection.\r\n     */\r\n    @serializeAsColor3()\r\n    public diffusionDistance = Color3.White();\r\n\r\n    private _useMaskFromThicknessTexture = false;\r\n    /**\r\n     * Stores the intensity of the different subsurface effects in the thickness texture.\r\n     * Note that if refractionIntensityTexture and/or translucencyIntensityTexture is provided it takes precedence over thicknessTexture + useMaskFromThicknessTexture\r\n     * * the green (red if useGltfStyleTextures = true) channel is the refraction intensity.\r\n     * * the blue (alpha if useGltfStyleTextures = true) channel is the translucency intensity.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useMaskFromThicknessTexture: boolean = false;\r\n\r\n    private _refractionIntensityTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the intensity of the refraction. If provided, it takes precedence over thicknessTexture + useMaskFromThicknessTexture\r\n     * * the green (red if useGltfStyleTextures = true) channel is the refraction intensity.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public refractionIntensityTexture: Nullable<BaseTexture> = null;\r\n\r\n    private _translucencyIntensityTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Stores the intensity of the translucency. If provided, it takes precedence over thicknessTexture + useMaskFromThicknessTexture\r\n     * * the blue (alpha if useGltfStyleTextures = true) channel is the translucency intensity.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public translucencyIntensityTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Defines the translucency tint of the material.\r\n     * If not set, the tint color will be used instead.\r\n     */\r\n    @serializeAsColor3()\r\n    public translucencyColor: Nullable<Color3> = null;\r\n\r\n    private _translucencyColorTexture: Nullable<BaseTexture> = null;\r\n    /**\r\n     * Defines the translucency tint color of the material as a texture.\r\n     * This is multiplied against the translucency color to add variety and realism to the material.\r\n     * If translucencyColor is not set, the tint color will be used instead.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public translucencyColorTexture: Nullable<BaseTexture> = null;\r\n\r\n    private _useGltfStyleTextures = true;\r\n    /**\r\n     * Use channels layout used by glTF:\r\n     * * thicknessTexture: the green (instead of red) channel is the thickness\r\n     * * thicknessTexture/refractionIntensityTexture: the red (instead of green) channel is the refraction intensity\r\n     * * thicknessTexture/translucencyIntensityTexture: the alpha (instead of blue) channel is the translucency intensity\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useGltfStyleTextures: boolean = true;\r\n\r\n    /**\r\n     * This property only exists for backward compatibility reasons.\r\n     * Set it to true if your rendering in 8.0+ is different from that in 7 when you use sub-surface properties (transmission, refraction, etc.). Default is false.\r\n     * Note however that the PBR calculation is wrong when this property is set to true, so only use it if you want to mimic the 7.0 behavior.\r\n     */\r\n    @serialize()\r\n    public applyAlbedoAfterSubSurface = PBRSubSurfaceConfiguration.DEFAULT_APPLY_ALBEDO_AFTERSUBSURFACE;\r\n\r\n    /**\r\n     * This property only exists for backward compatibility reasons.\r\n     * Set it to true if your rendering in 8.0+ is different from that in 7 when you use sub-surface translucency. Default is false.\r\n     */\r\n    @serialize()\r\n    public legacyTranslucency = PBRSubSurfaceConfiguration.DEFAULT_LEGACY_TRANSLUCENCY;\r\n\r\n    /**\r\n     * Keeping for backward compatibility... Should not be used anymore. It has been replaced by\r\n     * the property with the correct spelling.\r\n     * @see legacyTranslucency\r\n     */\r\n    public get legacyTransluceny(): boolean {\r\n        return this.legacyTranslucency;\r\n    }\r\n    public set legacyTransluceny(value: boolean) {\r\n        this.legacyTranslucency = value;\r\n    }\r\n\r\n    private _scene: Scene;\r\n\r\n    /** @internal */\r\n    private _internalMarkAllSubMeshesAsTexturesDirty: () => void;\r\n    private _internalMarkScenePrePassDirty: () => void;\r\n\r\n    /** @internal */\r\n    public _markAllSubMeshesAsTexturesDirty(): void {\r\n        this._enable(this._isRefractionEnabled || this._isTranslucencyEnabled || this._isScatteringEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n    }\r\n    /** @internal */\r\n    public _markScenePrePassDirty(): void {\r\n        this._enable(this._isRefractionEnabled || this._isTranslucencyEnabled || this._isScatteringEnabled);\r\n        this._internalMarkAllSubMeshesAsTexturesDirty();\r\n        this._internalMarkScenePrePassDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the plugin is compatible with a given shader language.\r\n     * @returns true if the plugin is compatible with the shader language\r\n     */\r\n    public override isCompatible(): boolean {\r\n        return true;\r\n    }\r\n\r\n    constructor(material: PBRBaseMaterial, addToPluginList = true) {\r\n        super(material, \"PBRSubSurface\", 130, new MaterialSubSurfaceDefines(), addToPluginList);\r\n\r\n        this._scene = material.getScene();\r\n        this.registerForExtraEvents = true;\r\n\r\n        this._internalMarkAllSubMeshesAsTexturesDirty = material._dirtyCallbacks[Constants.MATERIAL_TextureDirtyFlag];\r\n        this._internalMarkScenePrePassDirty = material._dirtyCallbacks[Constants.MATERIAL_PrePassDirtyFlag];\r\n    }\r\n\r\n    public override isReadyForSubMesh(defines: MaterialSubSurfaceDefines, scene: Scene): boolean {\r\n        if (!this._isRefractionEnabled && !this._isTranslucencyEnabled && !this._isScatteringEnabled) {\r\n            return true;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            if (scene.texturesEnabled) {\r\n                if (this._thicknessTexture && MaterialFlags.ThicknessTextureEnabled) {\r\n                    if (!this._thicknessTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._refractionIntensityTexture && MaterialFlags.RefractionIntensityTextureEnabled) {\r\n                    if (!this._refractionIntensityTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._translucencyColorTexture && MaterialFlags.TranslucencyColorTextureEnabled) {\r\n                    if (!this._translucencyColorTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._translucencyIntensityTexture && MaterialFlags.TranslucencyIntensityTextureEnabled) {\r\n                    if (!this._translucencyIntensityTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                const refractionTexture = this._getRefractionTexture(scene);\r\n                if (refractionTexture && MaterialFlags.RefractionTextureEnabled) {\r\n                    if (!refractionTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override prepareDefinesBeforeAttributes(defines: MaterialSubSurfaceDefines, scene: Scene): void {\r\n        if (!this._isRefractionEnabled && !this._isTranslucencyEnabled && !this._isScatteringEnabled) {\r\n            defines.SUBSURFACE = false;\r\n            defines.SS_DISPERSION = false;\r\n            defines.SS_TRANSLUCENCY = false;\r\n            defines.SS_SCATTERING = false;\r\n            defines.SS_REFRACTION = false;\r\n            defines.SS_REFRACTION_USE_INTENSITY_FROM_THICKNESS = false;\r\n            defines.SS_TRANSLUCENCY_USE_INTENSITY_FROM_THICKNESS = false;\r\n            defines.SS_THICKNESSANDMASK_TEXTURE = false;\r\n            defines.SS_THICKNESSANDMASK_TEXTUREDIRECTUV = 0;\r\n            defines.SS_HAS_THICKNESS = false;\r\n            defines.SS_REFRACTIONINTENSITY_TEXTURE = false;\r\n            defines.SS_REFRACTIONINTENSITY_TEXTUREDIRECTUV = 0;\r\n            defines.SS_TRANSLUCENCYINTENSITY_TEXTURE = false;\r\n            defines.SS_TRANSLUCENCYINTENSITY_TEXTUREDIRECTUV = 0;\r\n            defines.SS_REFRACTIONMAP_3D = false;\r\n            defines.SS_REFRACTIONMAP_OPPOSITEZ = false;\r\n            defines.SS_LODINREFRACTIONALPHA = false;\r\n            defines.SS_GAMMAREFRACTION = false;\r\n            defines.SS_RGBDREFRACTION = false;\r\n            defines.SS_LINEARSPECULARREFRACTION = false;\r\n            defines.SS_LINKREFRACTIONTOTRANSPARENCY = false;\r\n            defines.SS_ALBEDOFORREFRACTIONTINT = false;\r\n            defines.SS_ALBEDOFORTRANSLUCENCYTINT = false;\r\n            defines.SS_USE_LOCAL_REFRACTIONMAP_CUBIC = false;\r\n            defines.SS_USE_THICKNESS_AS_DEPTH = false;\r\n            defines.SS_USE_GLTF_TEXTURES = false;\r\n            defines.SS_TRANSLUCENCYCOLOR_TEXTURE = false;\r\n            defines.SS_TRANSLUCENCYCOLOR_TEXTUREDIRECTUV = 0;\r\n            defines.SS_TRANSLUCENCYCOLOR_TEXTURE_GAMMA = false;\r\n            defines.SS_APPLY_ALBEDO_AFTER_SUBSURFACE = false;\r\n            return;\r\n        }\r\n\r\n        if (defines._areTexturesDirty) {\r\n            defines.SUBSURFACE = true;\r\n\r\n            defines.SS_DISPERSION = this._isDispersionEnabled;\r\n            defines.SS_TRANSLUCENCY = this._isTranslucencyEnabled;\r\n            defines.SS_TRANSLUCENCY_USE_INTENSITY_FROM_THICKNESS = false;\r\n            defines.SS_TRANSLUCENCY_LEGACY = this.legacyTranslucency;\r\n            defines.SS_SCATTERING = this._isScatteringEnabled;\r\n            defines.SS_THICKNESSANDMASK_TEXTURE = false;\r\n            defines.SS_REFRACTIONINTENSITY_TEXTURE = false;\r\n            defines.SS_TRANSLUCENCYINTENSITY_TEXTURE = false;\r\n            defines.SS_HAS_THICKNESS = false;\r\n            defines.SS_USE_GLTF_TEXTURES = false;\r\n            defines.SS_REFRACTION = false;\r\n            defines.SS_REFRACTION_USE_INTENSITY_FROM_THICKNESS = false;\r\n            defines.SS_REFRACTIONMAP_3D = false;\r\n            defines.SS_GAMMAREFRACTION = false;\r\n            defines.SS_RGBDREFRACTION = false;\r\n            defines.SS_LINEARSPECULARREFRACTION = false;\r\n            defines.SS_REFRACTIONMAP_OPPOSITEZ = false;\r\n            defines.SS_LODINREFRACTIONALPHA = false;\r\n            defines.SS_LINKREFRACTIONTOTRANSPARENCY = false;\r\n            defines.SS_ALBEDOFORREFRACTIONTINT = false;\r\n            defines.SS_ALBEDOFORTRANSLUCENCYTINT = false;\r\n            defines.SS_USE_LOCAL_REFRACTIONMAP_CUBIC = false;\r\n            defines.SS_USE_THICKNESS_AS_DEPTH = false;\r\n            defines.SS_TRANSLUCENCYCOLOR_TEXTURE = false;\r\n            defines.SS_APPLY_ALBEDO_AFTER_SUBSURFACE = this.applyAlbedoAfterSubSurface;\r\n\r\n            if (defines._areTexturesDirty) {\r\n                if (scene.texturesEnabled) {\r\n                    if (this._thicknessTexture && MaterialFlags.ThicknessTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._thicknessTexture, defines, \"SS_THICKNESSANDMASK_TEXTURE\");\r\n                    }\r\n\r\n                    if (this._refractionIntensityTexture && MaterialFlags.RefractionIntensityTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._refractionIntensityTexture, defines, \"SS_REFRACTIONINTENSITY_TEXTURE\");\r\n                    }\r\n\r\n                    if (this._translucencyIntensityTexture && MaterialFlags.TranslucencyIntensityTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._translucencyIntensityTexture, defines, \"SS_TRANSLUCENCYINTENSITY_TEXTURE\");\r\n                    }\r\n\r\n                    if (this._translucencyColorTexture && MaterialFlags.TranslucencyColorTextureEnabled) {\r\n                        PrepareDefinesForMergedUV(this._translucencyColorTexture, defines, \"SS_TRANSLUCENCYCOLOR_TEXTURE\");\r\n                        defines.SS_TRANSLUCENCYCOLOR_TEXTURE_GAMMA = this._translucencyColorTexture.gammaSpace;\r\n                    }\r\n                }\r\n            }\r\n\r\n            defines.SS_HAS_THICKNESS = this.maximumThickness - this.minimumThickness !== 0.0;\r\n            defines.SS_USE_GLTF_TEXTURES = this._useGltfStyleTextures;\r\n            defines.SS_REFRACTION_USE_INTENSITY_FROM_THICKNESS = this._useMaskFromThicknessTexture && !this._refractionIntensityTexture;\r\n            defines.SS_TRANSLUCENCY_USE_INTENSITY_FROM_THICKNESS = this._useMaskFromThicknessTexture && !this._translucencyIntensityTexture;\r\n\r\n            if (this._isRefractionEnabled) {\r\n                if (scene.texturesEnabled) {\r\n                    const refractionTexture = this._getRefractionTexture(scene);\r\n                    if (refractionTexture && MaterialFlags.RefractionTextureEnabled) {\r\n                        defines.SS_REFRACTION = true;\r\n                        defines.SS_REFRACTIONMAP_3D = refractionTexture.isCube;\r\n                        defines.SS_GAMMAREFRACTION = refractionTexture.gammaSpace;\r\n                        defines.SS_RGBDREFRACTION = refractionTexture.isRGBD;\r\n                        defines.SS_LINEARSPECULARREFRACTION = refractionTexture.linearSpecularLOD;\r\n                        defines.SS_REFRACTIONMAP_OPPOSITEZ = this._scene.useRightHandedSystem && refractionTexture.isCube ? !refractionTexture.invertZ : refractionTexture.invertZ;\r\n                        defines.SS_LODINREFRACTIONALPHA = refractionTexture.lodLevelInAlpha;\r\n                        defines.SS_LINKREFRACTIONTOTRANSPARENCY = this._linkRefractionWithTransparency;\r\n                        defines.SS_ALBEDOFORREFRACTIONTINT = this.useAlbedoToTintRefraction;\r\n                        defines.SS_USE_LOCAL_REFRACTIONMAP_CUBIC = refractionTexture.isCube && (<any>refractionTexture).boundingBoxSize;\r\n                        defines.SS_USE_THICKNESS_AS_DEPTH = this.useThicknessAsDepth;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (this._isTranslucencyEnabled) {\r\n                defines.SS_ALBEDOFORTRANSLUCENCYTINT = this.useAlbedoToTintTranslucency;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Binds the material data (this function is called even if mustRebind() returns false)\r\n     * @param uniformBuffer defines the Uniform buffer to fill in.\r\n     * @param scene defines the scene the material belongs to.\r\n     * @param engine defines the engine the material belongs to.\r\n     * @param subMesh the submesh to bind data for\r\n     */\r\n    public override hardBindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene, engine: Engine, subMesh: SubMesh): void {\r\n        if (!this._isRefractionEnabled && !this._isTranslucencyEnabled && !this._isScatteringEnabled) {\r\n            return;\r\n        }\r\n\r\n        // If min/max thickness is 0, avoid decompising to determine the scaled thickness (it's always zero).\r\n        if (this.maximumThickness === 0.0 && this.minimumThickness === 0.0) {\r\n            uniformBuffer.updateFloat2(\"vThicknessParam\", 0, 0);\r\n        } else {\r\n            subMesh.getRenderingMesh().getWorldMatrix().decompose(TmpVectors.Vector3[0]);\r\n            const thicknessScale = Math.max(Math.abs(TmpVectors.Vector3[0].x), Math.abs(TmpVectors.Vector3[0].y), Math.abs(TmpVectors.Vector3[0].z));\r\n            uniformBuffer.updateFloat2(\"vThicknessParam\", this.minimumThickness * thicknessScale, (this.maximumThickness - this.minimumThickness) * thicknessScale);\r\n        }\r\n    }\r\n\r\n    public override bindForSubMesh(uniformBuffer: UniformBuffer, scene: Scene, engine: Engine, subMesh: SubMesh): void {\r\n        if (!this._isRefractionEnabled && !this._isTranslucencyEnabled && !this._isScatteringEnabled) {\r\n            return;\r\n        }\r\n\r\n        const defines = subMesh.materialDefines as unknown as MaterialSubSurfaceDefines;\r\n\r\n        const isFrozen = this._material.isFrozen;\r\n        const realTimeFiltering = this._material.realTimeFiltering;\r\n        const lodBasedMicrosurface = defines.LODBASEDMICROSFURACE;\r\n\r\n        const refractionTexture = this._getRefractionTexture(scene);\r\n\r\n        if (!uniformBuffer.useUbo || !isFrozen || !uniformBuffer.isSync) {\r\n            if (this._thicknessTexture && MaterialFlags.ThicknessTextureEnabled) {\r\n                uniformBuffer.updateFloat2(\"vThicknessInfos\", this._thicknessTexture.coordinatesIndex, this._thicknessTexture.level);\r\n                BindTextureMatrix(this._thicknessTexture, uniformBuffer, \"thickness\");\r\n            }\r\n\r\n            if (this._refractionIntensityTexture && MaterialFlags.RefractionIntensityTextureEnabled && defines.SS_REFRACTIONINTENSITY_TEXTURE) {\r\n                uniformBuffer.updateFloat2(\"vRefractionIntensityInfos\", this._refractionIntensityTexture.coordinatesIndex, this._refractionIntensityTexture.level);\r\n                BindTextureMatrix(this._refractionIntensityTexture, uniformBuffer, \"refractionIntensity\");\r\n            }\r\n\r\n            if (this._translucencyColorTexture && MaterialFlags.TranslucencyColorTextureEnabled && defines.SS_TRANSLUCENCYCOLOR_TEXTURE) {\r\n                uniformBuffer.updateFloat2(\"vTranslucencyColorInfos\", this._translucencyColorTexture.coordinatesIndex, this._translucencyColorTexture.level);\r\n                BindTextureMatrix(this._translucencyColorTexture, uniformBuffer, \"translucencyColor\");\r\n            }\r\n\r\n            if (this._translucencyIntensityTexture && MaterialFlags.TranslucencyIntensityTextureEnabled && defines.SS_TRANSLUCENCYINTENSITY_TEXTURE) {\r\n                uniformBuffer.updateFloat2(\"vTranslucencyIntensityInfos\", this._translucencyIntensityTexture.coordinatesIndex, this._translucencyIntensityTexture.level);\r\n                BindTextureMatrix(this._translucencyIntensityTexture, uniformBuffer, \"translucencyIntensity\");\r\n            }\r\n\r\n            if (refractionTexture && MaterialFlags.RefractionTextureEnabled) {\r\n                uniformBuffer.updateMatrix(\"refractionMatrix\", refractionTexture.getRefractionTextureMatrix());\r\n\r\n                let depth = 1.0;\r\n                if (!refractionTexture.isCube) {\r\n                    if ((<any>refractionTexture).depth) {\r\n                        depth = (<any>refractionTexture).depth;\r\n                    }\r\n                }\r\n\r\n                const width = refractionTexture.getSize().width;\r\n                const refractionIor = this.volumeIndexOfRefraction;\r\n                uniformBuffer.updateFloat4(\"vRefractionInfos\", refractionTexture.level, 1 / refractionIor, depth, this._invertRefractionY ? -1 : 1);\r\n                uniformBuffer.updateFloat4(\r\n                    \"vRefractionMicrosurfaceInfos\",\r\n                    width,\r\n                    refractionTexture.lodGenerationScale,\r\n                    refractionTexture.lodGenerationOffset,\r\n                    1.0 / this.indexOfRefraction\r\n                );\r\n\r\n                if (realTimeFiltering) {\r\n                    uniformBuffer.updateFloat2(\"vRefractionFilteringInfo\", width, Math.log2(width));\r\n                }\r\n\r\n                if ((<any>refractionTexture).boundingBoxSize) {\r\n                    const cubeTexture = <CubeTexture>refractionTexture;\r\n\r\n                    uniformBuffer.updateVector3(\"vRefractionPosition\", cubeTexture.boundingBoxPosition);\r\n                    uniformBuffer.updateVector3(\"vRefractionSize\", cubeTexture.boundingBoxSize);\r\n                }\r\n            }\r\n\r\n            if (this._isScatteringEnabled) {\r\n                uniformBuffer.updateFloat(\"scatteringDiffusionProfile\", this._scatteringDiffusionProfileIndex);\r\n            }\r\n            uniformBuffer.updateColor3(\"vDiffusionDistance\", this.diffusionDistance);\r\n\r\n            uniformBuffer.updateFloat4(\"vTintColor\", this.tintColor.r, this.tintColor.g, this.tintColor.b, Math.max(0.00001, this.tintColorAtDistance));\r\n            uniformBuffer.updateColor4(\"vTranslucencyColor\", this.translucencyColor ?? this.tintColor, 0);\r\n\r\n            uniformBuffer.updateFloat3(\"vSubSurfaceIntensity\", this.refractionIntensity, this.translucencyIntensity, 0);\r\n\r\n            uniformBuffer.updateFloat(\"dispersion\", this.dispersion);\r\n        }\r\n\r\n        // Textures\r\n        if (scene.texturesEnabled) {\r\n            if (this._thicknessTexture && MaterialFlags.ThicknessTextureEnabled) {\r\n                uniformBuffer.setTexture(\"thicknessSampler\", this._thicknessTexture);\r\n            }\r\n\r\n            if (this._refractionIntensityTexture && MaterialFlags.RefractionIntensityTextureEnabled && defines.SS_REFRACTIONINTENSITY_TEXTURE) {\r\n                uniformBuffer.setTexture(\"refractionIntensitySampler\", this._refractionIntensityTexture);\r\n            }\r\n\r\n            if (this._translucencyIntensityTexture && MaterialFlags.TranslucencyIntensityTextureEnabled && defines.SS_TRANSLUCENCYINTENSITY_TEXTURE) {\r\n                uniformBuffer.setTexture(\"translucencyIntensitySampler\", this._translucencyIntensityTexture);\r\n            }\r\n\r\n            if (this._translucencyColorTexture && MaterialFlags.TranslucencyColorTextureEnabled && defines.SS_TRANSLUCENCYCOLOR_TEXTURE) {\r\n                uniformBuffer.setTexture(\"translucencyColorSampler\", this._translucencyColorTexture);\r\n            }\r\n\r\n            if (refractionTexture && MaterialFlags.RefractionTextureEnabled) {\r\n                if (lodBasedMicrosurface) {\r\n                    uniformBuffer.setTexture(\"refractionSampler\", refractionTexture);\r\n                } else {\r\n                    uniformBuffer.setTexture(\"refractionSampler\", refractionTexture._lodTextureMid || refractionTexture);\r\n                    uniformBuffer.setTexture(\"refractionSamplerLow\", refractionTexture._lodTextureLow || refractionTexture);\r\n                    uniformBuffer.setTexture(\"refractionSamplerHigh\", refractionTexture._lodTextureHigh || refractionTexture);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Returns the texture used for refraction or null if none is used.\r\n     * @param scene defines the scene the material belongs to.\r\n     * @returns - Refraction texture if present.  If no refraction texture and refraction\r\n     * is linked with transparency, returns environment texture.  Otherwise, returns null.\r\n     */\r\n    private _getRefractionTexture(scene: Scene): Nullable<BaseTexture> {\r\n        if (this._refractionTexture) {\r\n            return this._refractionTexture;\r\n        }\r\n\r\n        if (this._isRefractionEnabled) {\r\n            return scene.environmentTexture;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Returns true if alpha blending should be disabled.\r\n     */\r\n    public get disableAlphaBlending(): boolean {\r\n        return this._isRefractionEnabled && this._linkRefractionWithTransparency;\r\n    }\r\n\r\n    /**\r\n     * Fills the list of render target textures.\r\n     * @param renderTargets the list of render targets to update\r\n     */\r\n    public override fillRenderTargetTextures(renderTargets: SmartArray<RenderTargetTexture>): void {\r\n        if (MaterialFlags.RefractionTextureEnabled && this._refractionTexture && this._refractionTexture.isRenderTarget) {\r\n            renderTargets.push(<RenderTargetTexture>this._refractionTexture);\r\n        }\r\n    }\r\n\r\n    public override hasTexture(texture: BaseTexture): boolean {\r\n        if (this._thicknessTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._refractionTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._refractionIntensityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._translucencyIntensityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._translucencyColorTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public override hasRenderTargetTextures(): boolean {\r\n        if (MaterialFlags.RefractionTextureEnabled && this._refractionTexture && this._refractionTexture.isRenderTarget) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public override getActiveTextures(activeTextures: BaseTexture[]): void {\r\n        if (this._thicknessTexture) {\r\n            activeTextures.push(this._thicknessTexture);\r\n        }\r\n\r\n        if (this._refractionTexture) {\r\n            activeTextures.push(this._refractionTexture);\r\n        }\r\n\r\n        if (this._refractionIntensityTexture) {\r\n            activeTextures.push(this._refractionIntensityTexture);\r\n        }\r\n\r\n        if (this._translucencyColorTexture) {\r\n            activeTextures.push(this._translucencyColorTexture);\r\n        }\r\n\r\n        if (this._translucencyIntensityTexture) {\r\n            activeTextures.push(this._translucencyIntensityTexture);\r\n        }\r\n    }\r\n\r\n    public override getAnimatables(animatables: IAnimatable[]): void {\r\n        if (this._thicknessTexture && this._thicknessTexture.animations && this._thicknessTexture.animations.length > 0) {\r\n            animatables.push(this._thicknessTexture);\r\n        }\r\n\r\n        if (this._refractionTexture && this._refractionTexture.animations && this._refractionTexture.animations.length > 0) {\r\n            animatables.push(this._refractionTexture);\r\n        }\r\n\r\n        if (this._refractionIntensityTexture && this._refractionIntensityTexture.animations && this._refractionIntensityTexture.animations.length > 0) {\r\n            animatables.push(this._refractionIntensityTexture);\r\n        }\r\n\r\n        if (this._translucencyColorTexture && this._translucencyColorTexture.animations && this._translucencyColorTexture.animations.length > 0) {\r\n            animatables.push(this._translucencyColorTexture);\r\n        }\r\n\r\n        if (this._translucencyIntensityTexture && this._translucencyIntensityTexture.animations && this._translucencyIntensityTexture.animations.length > 0) {\r\n            animatables.push(this._translucencyIntensityTexture);\r\n        }\r\n    }\r\n\r\n    public override dispose(forceDisposeTextures?: boolean): void {\r\n        if (forceDisposeTextures) {\r\n            if (this._thicknessTexture) {\r\n                this._thicknessTexture.dispose();\r\n            }\r\n\r\n            if (this._refractionTexture) {\r\n                this._refractionTexture.dispose();\r\n            }\r\n\r\n            if (this._refractionIntensityTexture) {\r\n                this._refractionIntensityTexture.dispose();\r\n            }\r\n\r\n            if (this._translucencyColorTexture) {\r\n                this._translucencyColorTexture.dispose();\r\n            }\r\n\r\n            if (this._translucencyIntensityTexture) {\r\n                this._translucencyIntensityTexture.dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public override getClassName(): string {\r\n        return \"PBRSubSurfaceConfiguration\";\r\n    }\r\n\r\n    public override addFallbacks(defines: MaterialSubSurfaceDefines, fallbacks: EffectFallbacks, currentRank: number): number {\r\n        if (defines.SS_SCATTERING) {\r\n            fallbacks.addFallback(currentRank++, \"SS_SCATTERING\");\r\n        }\r\n        if (defines.SS_TRANSLUCENCY) {\r\n            fallbacks.addFallback(currentRank++, \"SS_TRANSLUCENCY\");\r\n        }\r\n        return currentRank;\r\n    }\r\n\r\n    public override getSamplers(samplers: string[]): void {\r\n        samplers.push(\r\n            \"thicknessSampler\",\r\n            \"refractionIntensitySampler\",\r\n            \"translucencyIntensitySampler\",\r\n            \"refractionSampler\",\r\n            \"refractionSamplerLow\",\r\n            \"refractionSamplerHigh\",\r\n            \"translucencyColorSampler\"\r\n        );\r\n    }\r\n\r\n    public override getUniforms(): { ubo?: Array<{ name: string; size: number; type: string }>; vertex?: string; fragment?: string } {\r\n        return {\r\n            ubo: [\r\n                { name: \"vRefractionMicrosurfaceInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"vRefractionFilteringInfo\", size: 2, type: \"vec2\" },\r\n                { name: \"vTranslucencyIntensityInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"vRefractionInfos\", size: 4, type: \"vec4\" },\r\n                { name: \"refractionMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"vThicknessInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"vRefractionIntensityInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"thicknessMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"refractionIntensityMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"translucencyIntensityMatrix\", size: 16, type: \"mat4\" },\r\n                { name: \"vThicknessParam\", size: 2, type: \"vec2\" },\r\n                { name: \"vDiffusionDistance\", size: 3, type: \"vec3\" },\r\n                { name: \"vTintColor\", size: 4, type: \"vec4\" },\r\n                { name: \"vSubSurfaceIntensity\", size: 3, type: \"vec3\" },\r\n                { name: \"vRefractionPosition\", size: 3, type: \"vec3\" },\r\n                { name: \"vRefractionSize\", size: 3, type: \"vec3\" },\r\n                { name: \"scatteringDiffusionProfile\", size: 1, type: \"float\" },\r\n                { name: \"dispersion\", size: 1, type: \"float\" },\r\n\r\n                { name: \"vTranslucencyColor\", size: 4, type: \"vec4\" },\r\n                { name: \"vTranslucencyColorInfos\", size: 2, type: \"vec2\" },\r\n                { name: \"translucencyColorMatrix\", size: 16, type: \"mat4\" },\r\n            ],\r\n        };\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,MAAM,uBAAuB,CAAC;AAC3G,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAIhD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAIjD,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE3D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAKrD,OAAO,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;;;;;;;;;AAKrF,MAAO,yBAA0B,4KAAQ,mBAAe;IAA9D,aAAA;;QACW,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAEnB,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QACtB,IAAA,CAAA,0CAA0C,GAAG,KAAK,CAAC;QACnD,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,4CAA4C,GAAG,KAAK,CAAC;QACrD,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QACtB,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QAEtB,IAAA,CAAA,2BAA2B,GAAG,KAAK,CAAC;QACpC,IAAA,CAAA,mCAAmC,GAAG,CAAC,CAAC;QACxC,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QACvC,IAAA,CAAA,sCAAsC,GAAG,CAAC,CAAC;QAC3C,IAAA,CAAA,gCAAgC,GAAG,KAAK,CAAC;QACzC,IAAA,CAAA,wCAAwC,GAAG,CAAC,CAAC;QAC7C,IAAA,CAAA,4BAA4B,GAAG,KAAK,CAAC;QACrC,IAAA,CAAA,oCAAoC,GAAG,CAAC,CAAC;QACzC,IAAA,CAAA,kCAAkC,GAAG,KAAK,CAAC;QAE3C,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QACnC,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,2BAA2B,GAAG,KAAK,CAAC;QACpC,IAAA,CAAA,+BAA+B,GAAG,KAAK,CAAC;QACxC,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QACnC,IAAA,CAAA,4BAA4B,GAAG,KAAK,CAAC;QACrC,IAAA,CAAA,gCAAgC,GAAG,KAAK,CAAC;QACzC,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAElC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,gCAAgC,GAAG,KAAK,CAAC;QACzC,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;IAC1C,CAAC;CAAA;AAKK,MAAO,0BAA2B,gLAAQ,qBAAkB;IAuD9D;;;OAGG,CACH,IAAW,0BAA0B,GAAA;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAC/G,CAAC;IAED,IAAW,0BAA0B,CAAC,CAAmB,EAAA;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE,EAAE,CAAC;YAC5C,gBAAgB;YAChB,OAAO;QACX,CAAC;QAED,uDAAuD;QACvD,IAAI,CAAC,EAAE,CAAC;YACJ,IAAI,CAAC,gCAAgC,GAAG,IAAI,CAAC,MAAM,CAAC,uBAAwB,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACxG,CAAC;IACL,CAAC;IAmED;;;;;;OAMG,CAEH,IAAW,uBAAuB,GAAA;QAC9B,IAAI,IAAI,CAAC,wBAAwB,IAAI,GAAG,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,wBAAwB,CAAC;QACzC,CAAC;QACD,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACD,IAAW,uBAAuB,CAAC,KAAa,EAAA;QAC5C,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QAC1C,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,wBAAwB,GAAG,CAAC,GAAG,CAAC;QACzC,CAAC;IACL,CAAC;IA0ID;;;;OAIG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACD,IAAW,iBAAiB,CAAC,KAAc,EAAA;QACvC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACpC,CAAC;IAQD,cAAA,EAAgB,CACT,gCAAgC,GAAA;QACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACpG,IAAI,CAAC,wCAAwC,EAAE,CAAC;IACpD,CAAC;IACD,cAAA,EAAgB,CACT,sBAAsB,GAAA;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACpG,IAAI,CAAC,wCAAwC,EAAE,CAAC;QAChD,IAAI,CAAC,8BAA8B,EAAE,CAAC;IAC1C,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY,QAAyB,EAAE,eAAe,GAAG,IAAI,CAAA;QACzD,KAAK,CAAC,QAAQ,EAAE,eAAe,EAAE,GAAG,EAAE,IAAI,yBAAyB,EAAE,EAAE,eAAe,CAAC,CAAC;QAjUpF,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QACrC;;WAEG,CAGI,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QACvC;;WAEG,CAGI,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAE7B,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QACrC;;WAEG,CAGI,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QACrC;;WAEG,CAGI,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAG3B,IAAA,CAAA,gCAAgC,GAAG,CAAC,CAAC;QA0B7C;;;;WAIG,CAEI,IAAA,CAAA,mBAAmB,GAAW,CAAC,CAAC;QAEvC;;;;WAIG,CAEI,IAAA,CAAA,qBAAqB,GAAW,CAAC,CAAC;QAEzC;;WAEG,CAEI,IAAA,CAAA,yBAAyB,GAAY,KAAK,CAAC;QAElD;;WAEG,CAEI,IAAA,CAAA,2BAA2B,GAAY,KAAK,CAAC;QAE5C,IAAA,CAAA,iBAAiB,GAA0B,IAAI,CAAC;QACxD;;;;;;WAMG,CAGI,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAE9C,IAAA,CAAA,kBAAkB,GAA0B,IAAI,CAAC;QACzD;;WAEG,CAGI,IAAA,CAAA,iBAAiB,GAA0B,IAAI,CAAC;QAEvD,cAAA,EAAgB,CACT,IAAA,CAAA,kBAAkB,GAAG,GAAG,CAAC;QAChC;;;;;;;WAOG,CAGI,IAAA,CAAA,iBAAiB,GAAG,GAAG,CAAC;QAGvB,IAAA,CAAA,wBAAwB,GAAG,CAAC,GAAG,CAAC;QAwBhC,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QACnC;;WAEG,CAGI,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAEjC,cAAA,EAAgB,CACT,IAAA,CAAA,+BAA+B,GAAG,KAAK,CAAC;QAC/C;;;WAGG,CAGI,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QAE9C;;;WAGG,CAEI,IAAA,CAAA,gBAAgB,GAAW,CAAC,CAAC;QAEpC;;WAEG,CAEI,IAAA,CAAA,gBAAgB,GAAW,CAAC,CAAC;QAEpC;;WAEG,CAEI,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAEnC;;;WAGG,CAEI,IAAA,CAAA,SAAS,iKAAG,SAAM,CAAC,KAAK,EAAE,CAAC;QAElC;;;WAGG,CAEI,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC;QAE/B;;WAEG,CAEI,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QAEtB;;;WAGG,CAEI,IAAA,CAAA,iBAAiB,iKAAG,SAAM,CAAC,KAAK,EAAE,CAAC;QAElC,IAAA,CAAA,4BAA4B,GAAG,KAAK,CAAC;QAC7C;;;;;WAKG,CAGI,IAAA,CAAA,2BAA2B,GAAY,KAAK,CAAC;QAE5C,IAAA,CAAA,2BAA2B,GAA0B,IAAI,CAAC;QAClE;;;WAGG,CAGI,IAAA,CAAA,0BAA0B,GAA0B,IAAI,CAAC;QAExD,IAAA,CAAA,6BAA6B,GAA0B,IAAI,CAAC;QACpE;;;WAGG,CAGI,IAAA,CAAA,4BAA4B,GAA0B,IAAI,CAAC;QAElE;;;WAGG,CAEI,IAAA,CAAA,iBAAiB,GAAqB,IAAI,CAAC;QAE1C,IAAA,CAAA,yBAAyB,GAA0B,IAAI,CAAC;QAChE;;;;WAIG,CAGI,IAAA,CAAA,wBAAwB,GAA0B,IAAI,CAAC;QAEtD,IAAA,CAAA,qBAAqB,GAAG,IAAI,CAAC;QACrC;;;;;WAKG,CAGI,IAAA,CAAA,oBAAoB,GAAY,IAAI,CAAC;QAE5C;;;;WAIG,CAEI,IAAA,CAAA,0BAA0B,GAAG,0BAA0B,CAAC,oCAAoC,CAAC;QAEpG;;;WAGG,CAEI,IAAA,CAAA,kBAAkB,GAAG,0BAA0B,CAAC,2BAA2B,CAAC;QA2C/E,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAClC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QAEnC,IAAI,CAAC,wCAAwC,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAA,OAAS,CAAC,yBAAyB,CAAC,CAAC;QAC9G,IAAI,CAAC,8BAA8B,GAAG,QAAQ,CAAC,eAAe,CAAC,GAAA,MAAS,CAAC,yBAAyB,CAAC,CAAC;IACxG,CAAC;IAEe,iBAAiB,CAAC,OAAkC,EAAE,KAAY,EAAA;QAC9E,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC3F,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,iBAAiB,sKAAI,gBAAa,CAAC,uBAAuB,EAAE,CAAC;oBAClE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBACjD,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,2BAA2B,sKAAI,gBAAa,CAAC,iCAAiC,EAAE,CAAC;oBACtF,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC3D,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,yBAAyB,qKAAI,iBAAa,CAAC,+BAA+B,EAAE,CAAC;oBAClF,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBACzD,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,6BAA6B,IAAI,kLAAa,CAAC,mCAAmC,EAAE,CAAC;oBAC1F,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC7D,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBAC5D,IAAI,iBAAiB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;oBAC9D,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC5C,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,8BAA8B,CAAC,OAAkC,EAAE,KAAY,EAAA;QAC3F,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC3F,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;YAC3B,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;YAChC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,0CAA0C,GAAG,KAAK,CAAC;YAC3D,OAAO,CAAC,4CAA4C,GAAG,KAAK,CAAC;YAC7D,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,mCAAmC,GAAG,CAAC,CAAC;YAChD,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;YACjC,OAAO,CAAC,8BAA8B,GAAG,KAAK,CAAC;YAC/C,OAAO,CAAC,sCAAsC,GAAG,CAAC,CAAC;YACnD,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACjD,OAAO,CAAC,wCAAwC,GAAG,CAAC,CAAC;YACrD,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC3C,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;YACxC,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAClC,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,+BAA+B,GAAG,KAAK,CAAC;YAChD,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC3C,OAAO,CAAC,4BAA4B,GAAG,KAAK,CAAC;YAC7C,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACjD,OAAO,CAAC,yBAAyB,GAAG,KAAK,CAAC;YAC1C,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;YACrC,OAAO,CAAC,4BAA4B,GAAG,KAAK,CAAC;YAC7C,OAAO,CAAC,oCAAoC,GAAG,CAAC,CAAC;YACjD,OAAO,CAAC,kCAAkC,GAAG,KAAK,CAAC;YACnD,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACjD,OAAO;QACX,CAAC;QAED,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;YAE1B,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAClD,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC;YACtD,OAAO,CAAC,4CAA4C,GAAG,KAAK,CAAC;YAC7D,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACzD,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC;YAClD,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,8BAA8B,GAAG,KAAK,CAAC;YAC/C,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACjD,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;YACjC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;YACrC,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;YAC9B,OAAO,CAAC,0CAA0C,GAAG,KAAK,CAAC;YAC3D,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACpC,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACnC,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAClC,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;YAC5C,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC3C,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;YACxC,OAAO,CAAC,+BAA+B,GAAG,KAAK,CAAC;YAChD,OAAO,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC3C,OAAO,CAAC,4BAA4B,GAAG,KAAK,CAAC;YAC7C,OAAO,CAAC,gCAAgC,GAAG,KAAK,CAAC;YACjD,OAAO,CAAC,yBAAyB,GAAG,KAAK,CAAC;YAC1C,OAAO,CAAC,4BAA4B,GAAG,KAAK,CAAC;YAC7C,OAAO,CAAC,gCAAgC,GAAG,IAAI,CAAC,0BAA0B,CAAC;YAE3E,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;oBACxB,IAAI,IAAI,CAAC,iBAAiB,qKAAI,iBAAa,CAAC,uBAAuB,EAAE,CAAC;4MAClE,4BAAA,AAAyB,EAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,6BAA6B,CAAC,CAAC;oBAC9F,CAAC;oBAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,kLAAa,CAAC,iCAAiC,EAAE,CAAC;4MACtF,4BAAA,AAAyB,EAAC,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,gCAAgC,CAAC,CAAC;oBAC3G,CAAC;oBAED,IAAI,IAAI,CAAC,6BAA6B,sKAAI,gBAAa,CAAC,mCAAmC,EAAE,CAAC;4MAC1F,4BAAA,AAAyB,EAAC,IAAI,CAAC,6BAA6B,EAAE,OAAO,EAAE,kCAAkC,CAAC,CAAC;oBAC/G,CAAC;oBAED,IAAI,IAAI,CAAC,yBAAyB,sKAAI,gBAAa,CAAC,+BAA+B,EAAE,CAAC;4MAClF,4BAAA,AAAyB,EAAC,IAAI,CAAC,yBAAyB,EAAE,OAAO,EAAE,8BAA8B,CAAC,CAAC;wBACnG,OAAO,CAAC,kCAAkC,GAAG,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC;oBAC3F,CAAC;gBACL,CAAC;YACL,CAAC;YAED,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,KAAK,GAAG,CAAC;YACjF,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC1D,OAAO,CAAC,0CAA0C,GAAG,IAAI,CAAC,4BAA4B,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC;YAC5H,OAAO,CAAC,4CAA4C,GAAG,IAAI,CAAC,4BAA4B,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC;YAEhI,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;oBACxB,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;oBAC5D,IAAI,iBAAiB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;wBAC9D,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;wBAC7B,OAAO,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,MAAM,CAAC;wBACvD,OAAO,CAAC,kBAAkB,GAAG,iBAAiB,CAAC,UAAU,CAAC;wBAC1D,OAAO,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAAC;wBACrD,OAAO,CAAC,2BAA2B,GAAG,iBAAiB,CAAC,iBAAiB,CAAC;wBAC1E,OAAO,CAAC,0BAA0B,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC;wBAC3J,OAAO,CAAC,uBAAuB,GAAG,iBAAiB,CAAC,eAAe,CAAC;wBACpE,OAAO,CAAC,+BAA+B,GAAG,IAAI,CAAC,+BAA+B,CAAC;wBAC/E,OAAO,CAAC,0BAA0B,GAAG,IAAI,CAAC,yBAAyB,CAAC;wBACpE,OAAO,CAAC,gCAAgC,GAAG,iBAAiB,CAAC,MAAM,IAAU,iBAAkB,CAAC,eAAe,CAAC;wBAChH,OAAO,CAAC,yBAAyB,GAAG,IAAI,CAAC,mBAAmB,CAAC;oBACjE,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9B,OAAO,CAAC,4BAA4B,GAAG,IAAI,CAAC,2BAA2B,CAAC;YAC5E,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACa,kBAAkB,CAAC,aAA4B,EAAE,KAAY,EAAE,MAAc,EAAE,OAAgB,EAAA;QAC3G,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC3F,OAAO;QACX,CAAC;QAED,qGAAqG;QACrG,IAAI,IAAI,CAAC,gBAAgB,KAAK,GAAG,IAAI,IAAI,CAAC,gBAAgB,KAAK,GAAG,EAAE,CAAC;YACjE,aAAa,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACxD,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,gBAAgB,EAAE,CAAC,cAAc,EAAE,CAAC,SAAS,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7E,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzI,aAAa,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,GAAG,cAAc,EAAE,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,cAAc,CAAC,CAAC;QAC5J,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,aAA4B,EAAE,KAAY,EAAE,MAAc,EAAE,OAAgB,EAAA;QACvG,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC3F,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,eAAuD,CAAC;QAEhF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACzC,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAC3D,MAAM,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAE1D,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAE5D,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC9D,IAAI,IAAI,CAAC,iBAAiB,IAAI,kLAAa,CAAC,uBAAuB,EAAE,CAAC;gBAClE,aAAa,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;oMACrH,oBAAA,AAAiB,EAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;YAC1E,CAAC;YAED,IAAI,IAAI,CAAC,2BAA2B,sKAAI,gBAAa,CAAC,iCAAiC,IAAI,OAAO,CAAC,8BAA8B,EAAE,CAAC;gBAChI,aAAa,CAAC,YAAY,CAAC,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;oMACnJ,oBAAA,AAAiB,EAAC,IAAI,CAAC,2BAA2B,EAAE,aAAa,EAAE,qBAAqB,CAAC,CAAC;YAC9F,CAAC;YAED,IAAI,IAAI,CAAC,yBAAyB,IAAI,kLAAa,CAAC,+BAA+B,IAAI,OAAO,CAAC,4BAA4B,EAAE,CAAC;gBAC1H,aAAa,CAAC,YAAY,CAAC,yBAAyB,EAAE,IAAI,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;gBAC7I,wMAAA,AAAiB,EAAC,IAAI,CAAC,yBAAyB,EAAE,aAAa,EAAE,mBAAmB,CAAC,CAAC;YAC1F,CAAC;YAED,IAAI,IAAI,CAAC,6BAA6B,sKAAI,gBAAa,CAAC,mCAAmC,IAAI,OAAO,CAAC,gCAAgC,EAAE,CAAC;gBACtI,aAAa,CAAC,YAAY,CAAC,6BAA6B,EAAE,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,EAAE,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;oMACzJ,oBAAA,AAAiB,EAAC,IAAI,CAAC,6BAA6B,EAAE,aAAa,EAAE,uBAAuB,CAAC,CAAC;YAClG,CAAC;YAED,IAAI,iBAAiB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;gBAC9D,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,CAAC,CAAC;gBAE/F,IAAI,KAAK,GAAG,GAAG,CAAC;gBAChB,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;oBAC5B,IAAU,iBAAkB,CAAC,KAAK,EAAE,CAAC;wBACjC,KAAK,GAAS,iBAAkB,CAAC,KAAK,CAAC;oBAC3C,CAAC;gBACL,CAAC;gBAED,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;gBAChD,MAAM,aAAa,GAAG,IAAI,CAAC,uBAAuB,CAAC;gBACnD,aAAa,CAAC,YAAY,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,KAAK,EAAE,CAAC,GAAG,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpI,aAAa,CAAC,YAAY,CACtB,8BAA8B,EAC9B,KAAK,EACL,iBAAiB,CAAC,kBAAkB,EACpC,iBAAiB,CAAC,mBAAmB,EACrC,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAC/B,CAAC;gBAEF,IAAI,iBAAiB,EAAE,CAAC;oBACpB,aAAa,CAAC,YAAY,CAAC,0BAA0B,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBACpF,CAAC;gBAED,IAAU,iBAAkB,CAAC,eAAe,EAAE,CAAC;oBAC3C,MAAM,WAAW,GAAgB,iBAAiB,CAAC;oBAEnD,aAAa,CAAC,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;oBACpF,aAAa,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;gBAChF,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,aAAa,CAAC,WAAW,CAAC,4BAA4B,EAAE,IAAI,CAAC,gCAAgC,CAAC,CAAC;YACnG,CAAC;YACD,aAAa,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAEzE,aAAa,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAC5I,aAAa,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YAE9F,aAAa,CAAC,YAAY,CAAC,sBAAsB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;YAE5G,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC;QAED,WAAW;QACX,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,iBAAiB,sKAAI,gBAAa,CAAC,uBAAuB,EAAE,CAAC;gBAClE,aAAa,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzE,CAAC;YAED,IAAI,IAAI,CAAC,2BAA2B,sKAAI,gBAAa,CAAC,iCAAiC,IAAI,OAAO,CAAC,8BAA8B,EAAE,CAAC;gBAChI,aAAa,CAAC,UAAU,CAAC,4BAA4B,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC7F,CAAC;YAED,IAAI,IAAI,CAAC,6BAA6B,IAAI,kLAAa,CAAC,mCAAmC,IAAI,OAAO,CAAC,gCAAgC,EAAE,CAAC;gBACtI,aAAa,CAAC,UAAU,CAAC,8BAA8B,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACjG,CAAC;YAED,IAAI,IAAI,CAAC,yBAAyB,sKAAI,gBAAa,CAAC,+BAA+B,IAAI,OAAO,CAAC,4BAA4B,EAAE,CAAC;gBAC1H,aAAa,CAAC,UAAU,CAAC,0BAA0B,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACzF,CAAC;YAED,IAAI,iBAAiB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;gBAC9D,IAAI,oBAAoB,EAAE,CAAC;oBACvB,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;gBACrE,CAAC,MAAM,CAAC;oBACJ,aAAa,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,cAAc,IAAI,iBAAiB,CAAC,CAAC;oBACrG,aAAa,CAAC,UAAU,CAAC,sBAAsB,EAAE,iBAAiB,CAAC,cAAc,IAAI,iBAAiB,CAAC,CAAC;oBACxG,aAAa,CAAC,UAAU,CAAC,uBAAuB,EAAE,iBAAiB,CAAC,eAAe,IAAI,iBAAiB,CAAC,CAAC;gBAC9G,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACK,qBAAqB,CAAC,KAAY,EAAA;QACtC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO,KAAK,CAAC,kBAAkB,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,+BAA+B,CAAC;IAC7E,CAAC;IAED;;;OAGG,CACa,wBAAwB,CAAC,aAA8C,EAAA;QACnF,sKAAI,gBAAa,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;YAC9G,aAAa,CAAC,IAAI,CAAsB,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;IAEe,UAAU,CAAC,OAAoB,EAAA;QAC3C,IAAI,IAAI,CAAC,iBAAiB,KAAK,OAAO,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,KAAK,OAAO,EAAE,CAAC;YAC/C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,KAAK,OAAO,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,KAAK,OAAO,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEe,uBAAuB,GAAA;QACnC,IAAI,kLAAa,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;YAC9G,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEe,iBAAiB,CAAC,cAA6B,EAAA;QAC3D,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAC5D,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,WAA0B,EAAA;QACrD,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9G,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjH,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,2BAA2B,CAAC,UAAU,IAAI,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5I,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,yBAAyB,IAAI,IAAI,CAAC,yBAAyB,CAAC,UAAU,IAAI,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,6BAA6B,CAAC,UAAU,IAAI,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClJ,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAEe,OAAO,CAAC,oBAA8B,EAAA;QAClD,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;YACrC,CAAC;YAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;YACtC,CAAC;YAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACnC,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,CAAC;YAC/C,CAAC;YAED,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACjC,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,CAAC;YAC7C,CAAC;YAED,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBACrC,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,CAAC;YACjD,CAAC;QACL,CAAC;IACL,CAAC;IAEe,YAAY,GAAA;QACxB,OAAO,4BAA4B,CAAC;IACxC,CAAC;IAEe,YAAY,CAAC,OAAkC,EAAE,SAA0B,EAAE,WAAmB,EAAA;QAC5G,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YACxB,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,eAAe,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,iBAAiB,CAAC,CAAC;QAC5D,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAEe,WAAW,CAAC,QAAkB,EAAA;QAC1C,QAAQ,CAAC,IAAI,CACT,kBAAkB,EAClB,4BAA4B,EAC5B,8BAA8B,EAC9B,mBAAmB,EACnB,sBAAsB,EACtB,uBAAuB,EACvB,0BAA0B,CAC7B,CAAC;IACN,CAAC;IAEe,WAAW,GAAA;QACvB,OAAO;YACH,GAAG,EAAE;gBACD;oBAAE,IAAI,EAAE,8BAA8B;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC/D;oBAAE,IAAI,EAAE,0BAA0B;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC3D;oBAAE,IAAI,EAAE,6BAA6B;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC9D;oBAAE,IAAI,EAAE,kBAAkB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACnD;oBAAE,IAAI,EAAE,kBAAkB;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACpD;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAClD;oBAAE,IAAI,EAAE,2BAA2B;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC5D;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACnD;oBAAE,IAAI,EAAE,2BAA2B;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC7D;oBAAE,IAAI,EAAE,6BAA6B;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC/D;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAClD;oBAAE,IAAI,EAAE,oBAAoB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACrD;oBAAE,IAAI,EAAE,YAAY;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC7C;oBAAE,IAAI,EAAE,sBAAsB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACvD;oBAAE,IAAI,EAAE,qBAAqB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACtD;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAClD;oBAAE,IAAI,EAAE,4BAA4B;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,OAAO;gBAAA,CAAE;gBAC9D;oBAAE,IAAI,EAAE,YAAY;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,OAAO;gBAAA,CAAE;gBAE9C;oBAAE,IAAI,EAAE,oBAAoB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBACrD;oBAAE,IAAI,EAAE,yBAAyB;oBAAE,IAAI,EAAE,CAAC;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;gBAC1D;oBAAE,IAAI,EAAE,yBAAyB;oBAAE,IAAI,EAAE,EAAE;oBAAE,IAAI,EAAE,MAAM;gBAAA,CAAE;aAC9D;SACJ,CAAC;IACN,CAAC;;AA3zBD;;;;;;GAMG,CACW,2BAAA,oCAAoC,GAAG,KAAK,AAAR,CAAS;AAE3D;;;;;GAKG,CACW,2BAAA,2BAA2B,GAAG,KAAK,AAAR,CAAS;wJAU3C,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;uEAClB;wJAQ5B,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;yEAChB;wJAQ9B,aAAA,EAAA;KAFN,yKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;uEAClB;wJAQ5B,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;KACX,gLAAA,AAAgB,EAAC,wBAAwB,CAAC;uEACR;wJAG3B,aAAA,EAAA;kKADP,YAAA,AAAS,EAAE;oFACiC;wJAgCtC,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;uEAC2B;wJAQhC,aAAA,EAAA;KADN,yKAAA,AAAS,EAAE;yEAC6B;wJAMlC,aAAA,EAAA;iKADN,aAAA,AAAS,EAAE;6EACsC;wJAM3C,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;+EACwC;wJAY7C,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;oEACC;wJAQ/C,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;QACpB,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;qEACE;wJAchD,aAAA,EAAA;QAFN,sKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;qEACtB;CAGvB,oKAAA,EAAA;kKADP,YAAA,AAAS,EAAE;4EAC4B;wJAUxC,aAAA,EAAA;IADC,iLAAA,AAAgB,EAAC,kCAAkC,CAAC;yEAMpD;wJAeM,aAAA,EAAA;KAFN,yKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;qEACpB;IAU1B,iKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;kFACP;wJAOvC,aAAA,EAAA;IADN,0KAAA,AAAS,EAAE;oEACwB;wJAM7B,aAAA,EAAA;KADN,yKAAA,AAAS,EAAE;oEACwB;CAM7B,oKAAA,EAAA;kKADN,YAAA,AAAS,EAAE;uEACuB;CAO5B,oKAAA,EAAA;kKADN,oBAAA,AAAiB,EAAE;6DACc;uJAO3B,cAAA,EAAA;kKADN,YAAA,AAAS,EAAE;uEACmB;IAMxB,iKAAA,EAAA;kKADN,YAAA,AAAS,EAAE;8DACU;IAOf,iKAAA,EAAA;kKADN,oBAAA,AAAiB,EAAE;qEACsB;CAWnC,oKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;+EACD;wJAS7C,aAAA,EAAA;KAFN,kLAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8EACW;CASzD,oKAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;gFACa;wJAO3D,aAAA,EAAA;QADN,8KAAA,AAAiB,EAAE;qEAC8B;wJAU3C,aAAA,EAAA;KAFN,kLAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;4EACS;wJAWvD,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;KACX,gLAAA,AAAgB,EAAC,kCAAkC,CAAC;wEACT;wJAQrC,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;8EACwF;wJAO7F,aAAA,EAAA;kKADN,YAAA,AAAS,EAAE;sEACuE", "debugId": null}}, {"offset": {"line": 2278, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrBaseMaterial.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrBaseMaterial.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { serializeAsImageProcessingConfiguration, expandToProperty } from \"../../Misc/decorators\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { SmartArray } from \"../../Misc/smartArray\";\r\nimport { GetEnvironmentBRDFTexture } from \"../../Misc/brdfTextureTools\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Scene } from \"../../scene\";\r\nimport type { Matrix } from \"../../Maths/math.vector\";\r\nimport { Vector4 } from \"../../Maths/math.vector\";\r\nimport { VertexBuffer } from \"../../Buffers/buffer\";\r\nimport type { SubMesh } from \"../../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport { PBRBRDFConfiguration } from \"./pbrBRDFConfiguration\";\r\nimport { PrePassConfiguration } from \"../prePassConfiguration\";\r\nimport { Color3, TmpColors } from \"../../Maths/math.color\";\r\n\r\nimport type { IImageProcessingConfigurationDefines } from \"../../Materials/imageProcessingConfiguration.defines\";\r\nimport { ImageProcessingConfiguration } from \"../../Materials/imageProcessingConfiguration\";\r\nimport type { Effect, IEffectCreationOptions } from \"../../Materials/effect\";\r\nimport type { IMaterialCompilationOptions, ICustomShaderNameResolveOptions } from \"../../Materials/material\";\r\nimport { Material } from \"../../Materials/material\";\r\nimport { MaterialPluginEvent } from \"../materialPluginEvent\";\r\nimport { MaterialDefines } from \"../../Materials/materialDefines\";\r\nimport { PushMaterial } from \"../../Materials/pushMaterial\";\r\n\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { Texture } from \"../../Materials/Textures/texture\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport type { CubeTexture } from \"../../Materials/Textures/cubeTexture\";\r\n\r\nimport { MaterialFlags } from \"../materialFlags\";\r\nimport { Constants } from \"../../Engines/constants\";\r\nimport type { IAnimatable } from \"../../Animations/animatable.interface\";\r\n\r\nimport \"../../Materials/Textures/baseTexture.polynomial\";\r\n\r\nimport { EffectFallbacks } from \"../effectFallbacks\";\r\nimport { PBRClearCoatConfiguration } from \"./pbrClearCoatConfiguration\";\r\nimport { PBRIridescenceConfiguration } from \"./pbrIridescenceConfiguration\";\r\nimport { PBRAnisotropicConfiguration } from \"./pbrAnisotropicConfiguration\";\r\nimport { PBRSheenConfiguration } from \"./pbrSheenConfiguration\";\r\nimport { PBRSubSurfaceConfiguration } from \"./pbrSubSurfaceConfiguration\";\r\nimport { DetailMapConfiguration } from \"../material.detailMapConfiguration\";\r\nimport { AddClipPlaneUniforms, BindClipPlane } from \"../clipPlaneMaterialHelper\";\r\nimport {\r\n    BindBonesParameters,\r\n    BindFogParameters,\r\n    BindLights,\r\n    BindLogDepth,\r\n    BindMorphTargetParameters,\r\n    BindTextureMatrix,\r\n    HandleFallbacksForShadows,\r\n    PrepareAttributesForBakedVertexAnimation,\r\n    PrepareAttributesForBones,\r\n    PrepareAttributesForInstances,\r\n    PrepareAttributesForMorphTargets,\r\n    PrepareDefinesForAttributes,\r\n    PrepareDefinesForFrameBoundValues,\r\n    PrepareDefinesForLights,\r\n    PrepareDefinesForMergedUV,\r\n    PrepareDefinesForMisc,\r\n    PrepareDefinesForMultiview,\r\n    PrepareDefinesForOIT,\r\n    PrepareDefinesForPrePass,\r\n    PrepareUniformsAndSamplersList,\r\n} from \"../materialHelper.functions\";\r\nimport { ShaderLanguage } from \"../shaderLanguage\";\r\nimport { MaterialHelperGeometryRendering } from \"../materialHelper.geometryrendering\";\r\n\r\nconst onCreatedEffectParameters = { effect: null as unknown as Effect, subMesh: null as unknown as Nullable<SubMesh> };\r\n\r\n/**\r\n * Manages the defines for the PBR Material.\r\n * @internal\r\n */\r\nexport class PBRMaterialDefines extends MaterialDefines implements IImageProcessingConfigurationDefines {\r\n    public PBR = true;\r\n\r\n    public NUM_SAMPLES = \"0\";\r\n    public REALTIME_FILTERING = false;\r\n    public IBL_CDF_FILTERING = false;\r\n    public MAINUV1 = false;\r\n    public MAINUV2 = false;\r\n    public MAINUV3 = false;\r\n    public MAINUV4 = false;\r\n    public MAINUV5 = false;\r\n    public MAINUV6 = false;\r\n    public UV1 = false;\r\n    public UV2 = false;\r\n    public UV3 = false;\r\n    public UV4 = false;\r\n    public UV5 = false;\r\n    public UV6 = false;\r\n\r\n    public ALBEDO = false;\r\n    public GAMMAALBEDO = false;\r\n    public ALBEDODIRECTUV = 0;\r\n    public VERTEXCOLOR = false;\r\n\r\n    public BASE_WEIGHT = false;\r\n    public BASE_WEIGHTDIRECTUV = 0;\r\n    public BASE_DIFFUSE_ROUGHNESS = false;\r\n    public BASE_DIFFUSE_ROUGHNESSDIRECTUV = 0;\r\n\r\n    public BAKED_VERTEX_ANIMATION_TEXTURE = false;\r\n\r\n    public AMBIENT = false;\r\n    public AMBIENTDIRECTUV = 0;\r\n    public AMBIENTINGRAYSCALE = false;\r\n\r\n    public OPACITY = false;\r\n    public VERTEXALPHA = false;\r\n    public OPACITYDIRECTUV = 0;\r\n    public OPACITYRGB = false;\r\n    public ALPHATEST = false;\r\n    public DEPTHPREPASS = false;\r\n    public ALPHABLEND = false;\r\n    public ALPHAFROMALBEDO = false;\r\n    public ALPHATESTVALUE = \"0.5\";\r\n    public SPECULAROVERALPHA = false;\r\n    public RADIANCEOVERALPHA = false;\r\n    public ALPHAFRESNEL = false;\r\n    public LINEARALPHAFRESNEL = false;\r\n    public PREMULTIPLYALPHA = false;\r\n\r\n    public EMISSIVE = false;\r\n    public EMISSIVEDIRECTUV = 0;\r\n    public GAMMAEMISSIVE = false;\r\n\r\n    public REFLECTIVITY = false;\r\n    public REFLECTIVITY_GAMMA = false;\r\n    public REFLECTIVITYDIRECTUV = 0;\r\n    public SPECULARTERM = false;\r\n\r\n    public MICROSURFACEFROMREFLECTIVITYMAP = false;\r\n    public MICROSURFACEAUTOMATIC = false;\r\n    public LODBASEDMICROSFURACE = false;\r\n    public MICROSURFACEMAP = false;\r\n    public MICROSURFACEMAPDIRECTUV = 0;\r\n\r\n    public METALLICWORKFLOW = false;\r\n    public ROUGHNESSSTOREINMETALMAPALPHA = false;\r\n    public ROUGHNESSSTOREINMETALMAPGREEN = false;\r\n    public METALLNESSSTOREINMETALMAPBLUE = false;\r\n    public AOSTOREINMETALMAPRED = false;\r\n    public METALLIC_REFLECTANCE = false;\r\n    public METALLIC_REFLECTANCE_GAMMA = false;\r\n    public METALLIC_REFLECTANCEDIRECTUV = 0;\r\n    public METALLIC_REFLECTANCE_USE_ALPHA_ONLY = false;\r\n    public REFLECTANCE = false;\r\n    public REFLECTANCE_GAMMA = false;\r\n    public REFLECTANCEDIRECTUV = 0;\r\n\r\n    public ENVIRONMENTBRDF = false;\r\n    public ENVIRONMENTBRDF_RGBD = false;\r\n\r\n    public NORMAL = false;\r\n    public TANGENT = false;\r\n    public BUMP = false;\r\n    public BUMPDIRECTUV = 0;\r\n    public OBJECTSPACE_NORMALMAP = false;\r\n    public PARALLAX = false;\r\n    public PARALLAX_RHS = false;\r\n    public PARALLAXOCCLUSION = false;\r\n    public NORMALXYSCALE = true;\r\n\r\n    public LIGHTMAP = false;\r\n    public LIGHTMAPDIRECTUV = 0;\r\n    public USELIGHTMAPASSHADOWMAP = false;\r\n    public GAMMALIGHTMAP = false;\r\n    public RGBDLIGHTMAP = false;\r\n\r\n    public REFLECTION = false;\r\n    public REFLECTIONMAP_3D = false;\r\n    public REFLECTIONMAP_SPHERICAL = false;\r\n    public REFLECTIONMAP_PLANAR = false;\r\n    public REFLECTIONMAP_CUBIC = false;\r\n    public USE_LOCAL_REFLECTIONMAP_CUBIC = false;\r\n    public REFLECTIONMAP_PROJECTION = false;\r\n    public REFLECTIONMAP_SKYBOX = false;\r\n    public REFLECTIONMAP_EXPLICIT = false;\r\n    public REFLECTIONMAP_EQUIRECTANGULAR = false;\r\n    public REFLECTIONMAP_EQUIRECTANGULAR_FIXED = false;\r\n    public REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = false;\r\n    public INVERTCUBICMAP = false;\r\n    public USESPHERICALFROMREFLECTIONMAP = false;\r\n    public USEIRRADIANCEMAP = false;\r\n    public USE_IRRADIANCE_DOMINANT_DIRECTION = false;\r\n    public USESPHERICALINVERTEX = false;\r\n    public REFLECTIONMAP_OPPOSITEZ = false;\r\n    public LODINREFLECTIONALPHA = false;\r\n    public GAMMAREFLECTION = false;\r\n    public RGBDREFLECTION = false;\r\n    public LINEARSPECULARREFLECTION = false;\r\n    public RADIANCEOCCLUSION = false;\r\n    public HORIZONOCCLUSION = false;\r\n\r\n    public INSTANCES = false;\r\n    public THIN_INSTANCES = false;\r\n    public INSTANCESCOLOR = false;\r\n\r\n    public PREPASS = false;\r\n    public PREPASS_COLOR = false;\r\n    public PREPASS_COLOR_INDEX = -1;\r\n    public PREPASS_IRRADIANCE = false;\r\n    public PREPASS_IRRADIANCE_INDEX = -1;\r\n    public PREPASS_ALBEDO = false;\r\n    public PREPASS_ALBEDO_INDEX = -1;\r\n    public PREPASS_ALBEDO_SQRT = false;\r\n    public PREPASS_ALBEDO_SQRT_INDEX = -1;\r\n    public PREPASS_DEPTH = false;\r\n    public PREPASS_DEPTH_INDEX = -1;\r\n    public PREPASS_SCREENSPACE_DEPTH = false;\r\n    public PREPASS_SCREENSPACE_DEPTH_INDEX = -1;\r\n    public PREPASS_NORMALIZED_VIEW_DEPTH = false;\r\n    public PREPASS_NORMALIZED_VIEW_DEPTH_INDEX = -1;\r\n    public PREPASS_NORMAL = false;\r\n    public PREPASS_NORMAL_INDEX = -1;\r\n    public PREPASS_NORMAL_WORLDSPACE = false;\r\n    public PREPASS_WORLD_NORMAL = false;\r\n    public PREPASS_WORLD_NORMAL_INDEX = -1;\r\n    public PREPASS_POSITION = false;\r\n    public PREPASS_POSITION_INDEX = -1;\r\n    public PREPASS_LOCAL_POSITION = false;\r\n    public PREPASS_LOCAL_POSITION_INDEX = -1;\r\n    public PREPASS_VELOCITY = false;\r\n    public PREPASS_VELOCITY_INDEX = -1;\r\n    public PREPASS_VELOCITY_LINEAR = false;\r\n    public PREPASS_VELOCITY_LINEAR_INDEX = -1;\r\n    public PREPASS_REFLECTIVITY = false;\r\n    public PREPASS_REFLECTIVITY_INDEX = -1;\r\n    public SCENE_MRT_COUNT = 0;\r\n\r\n    public NUM_BONE_INFLUENCERS = 0;\r\n    public BonesPerMesh = 0;\r\n    public BONETEXTURE = false;\r\n    public BONES_VELOCITY_ENABLED = false;\r\n\r\n    public NONUNIFORMSCALING = false;\r\n\r\n    public MORPHTARGETS = false;\r\n    public MORPHTARGETS_POSITION = false;\r\n    public MORPHTARGETS_NORMAL = false;\r\n    public MORPHTARGETS_TANGENT = false;\r\n    public MORPHTARGETS_UV = false;\r\n    public MORPHTARGETS_UV2 = false;\r\n    public MORPHTARGETS_COLOR = false;\r\n    public MORPHTARGETTEXTURE_HASPOSITIONS = false;\r\n    public MORPHTARGETTEXTURE_HASNORMALS = false;\r\n    public MORPHTARGETTEXTURE_HASTANGENTS = false;\r\n    public MORPHTARGETTEXTURE_HASUVS = false;\r\n    public MORPHTARGETTEXTURE_HASUV2S = false;\r\n    public MORPHTARGETTEXTURE_HASCOLORS = false;\r\n    public NUM_MORPH_INFLUENCERS = 0;\r\n    public MORPHTARGETS_TEXTURE = false;\r\n\r\n    public IMAGEPROCESSING = false;\r\n    public VIGNETTE = false;\r\n    public VIGNETTEBLENDMODEMULTIPLY = false;\r\n    public VIGNETTEBLENDMODEOPAQUE = false;\r\n    public TONEMAPPING = 0;\r\n    public CONTRAST = false;\r\n    public COLORCURVES = false;\r\n    public COLORGRADING = false;\r\n    public COLORGRADING3D = false;\r\n    public SAMPLER3DGREENDEPTH = false;\r\n    public SAMPLER3DBGRMAP = false;\r\n    public DITHER = false;\r\n    public IMAGEPROCESSINGPOSTPROCESS = false;\r\n    public SKIPFINALCOLORCLAMP = false;\r\n    public EXPOSURE = false;\r\n    public MULTIVIEW = false;\r\n    public ORDER_INDEPENDENT_TRANSPARENCY = false;\r\n    public ORDER_INDEPENDENT_TRANSPARENCY_16BITS = false;\r\n\r\n    public USEPHYSICALLIGHTFALLOFF = false;\r\n    public USEGLTFLIGHTFALLOFF = false;\r\n    public TWOSIDEDLIGHTING = false;\r\n    public MIRRORED = false;\r\n    public SHADOWFLOAT = false;\r\n    public CLIPPLANE = false;\r\n    public CLIPPLANE2 = false;\r\n    public CLIPPLANE3 = false;\r\n    public CLIPPLANE4 = false;\r\n    public CLIPPLANE5 = false;\r\n    public CLIPPLANE6 = false;\r\n    public POINTSIZE = false;\r\n    public FOG = false;\r\n    public LOGARITHMICDEPTH = false;\r\n    public CAMERA_ORTHOGRAPHIC = false;\r\n    public CAMERA_PERSPECTIVE = false;\r\n    public AREALIGHTSUPPORTED = true;\r\n\r\n    public FORCENORMALFORWARD = false;\r\n\r\n    public SPECULARAA = false;\r\n\r\n    public UNLIT = false;\r\n\r\n    public DECAL_AFTER_DETAIL = false;\r\n\r\n    public DEBUGMODE = 0;\r\n\r\n    /**\r\n     * Initializes the PBR Material defines.\r\n     * @param externalProperties The external properties\r\n     */\r\n    constructor(externalProperties?: { [name: string]: { type: string; default: any } }) {\r\n        super(externalProperties);\r\n        this.rebuild();\r\n    }\r\n\r\n    /**\r\n     * Resets the PBR Material defines.\r\n     */\r\n    public override reset(): void {\r\n        super.reset();\r\n        this.ALPHATESTVALUE = \"0.5\";\r\n        this.PBR = true;\r\n        this.NORMALXYSCALE = true;\r\n    }\r\n}\r\n\r\n/**\r\n * The Physically based material base class of BJS.\r\n *\r\n * This offers the main features of a standard PBR material.\r\n * For more information, please refer to the documentation :\r\n * https://doc.babylonjs.com/features/featuresDeepDive/materials/using/introToPBR\r\n * @see [WebGL](https://playground.babylonjs.com/#CGHTSM#1)\r\n * @see [WebGPU](https://playground.babylonjs.com/#CGHTSM#2)\r\n */\r\nexport abstract class PBRBaseMaterial extends PushMaterial {\r\n    /**\r\n     * PBRMaterialTransparencyMode: No transparency mode, Alpha channel is not use.\r\n     */\r\n    public static readonly PBRMATERIAL_OPAQUE = Material.MATERIAL_OPAQUE;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Alpha Test mode, pixel are discarded below a certain threshold defined by the alpha cutoff value.\r\n     */\r\n    public static readonly PBRMATERIAL_ALPHATEST = Material.MATERIAL_ALPHATEST;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Pixels are blended (according to the alpha mode) with the already drawn pixels in the current frame buffer.\r\n     */\r\n    public static readonly PBRMATERIAL_ALPHABLEND = Material.MATERIAL_ALPHABLEND;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Pixels are blended (according to the alpha mode) with the already drawn pixels in the current frame buffer.\r\n     * They are also discarded below the alpha cutoff threshold to improve performances.\r\n     */\r\n    public static readonly PBRMATERIAL_ALPHATESTANDBLEND = Material.MATERIAL_ALPHATESTANDBLEND;\r\n\r\n    /**\r\n     * Defines the default value of how much AO map is occluding the analytical lights\r\n     * (point spot...).\r\n     */\r\n    public static DEFAULT_AO_ON_ANALYTICAL_LIGHTS = 0;\r\n\r\n    /**\r\n     * PBRMaterialLightFalloff Physical: light is falling off following the inverse squared distance law.\r\n     */\r\n    public static readonly LIGHTFALLOFF_PHYSICAL = 0;\r\n\r\n    /**\r\n     * PBRMaterialLightFalloff gltf: light is falling off as described in the gltf moving to PBR document\r\n     * to enhance interoperability with other engines.\r\n     */\r\n    public static readonly LIGHTFALLOFF_GLTF = 1;\r\n\r\n    /**\r\n     * PBRMaterialLightFalloff Standard: light is falling off like in the standard material\r\n     * to enhance interoperability with other materials.\r\n     */\r\n    public static readonly LIGHTFALLOFF_STANDARD = 2;\r\n\r\n    /**\r\n     * Force all the PBR materials to compile to glsl even on WebGPU engines.\r\n     * False by default. This is mostly meant for backward compatibility.\r\n     */\r\n    public static ForceGLSL = false;\r\n\r\n    /**\r\n     * Intensity of the direct lights e.g. the four lights available in your scene.\r\n     * This impacts both the direct diffuse and specular highlights.\r\n     * @internal\r\n     */\r\n    public _directIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the emissive part of the material.\r\n     * This helps controlling the emissive effect without modifying the emissive color.\r\n     * @internal\r\n     */\r\n    public _emissiveIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the environment e.g. how much the environment will light the object\r\n     * either through harmonics for rough material or through the reflection for shiny ones.\r\n     * @internal\r\n     */\r\n    public _environmentIntensity: number = 1.0;\r\n\r\n    /**\r\n     * This is a special control allowing the reduction of the specular highlights coming from the\r\n     * four lights of the scene. Those highlights may not be needed in full environment lighting.\r\n     * @internal\r\n     */\r\n    public _specularIntensity: number = 1.0;\r\n\r\n    /**\r\n     * This stores the direct, emissive, environment, and specular light intensities into a Vector4.\r\n     */\r\n    private _lightingInfos: Vector4 = new Vector4(this._directIntensity, this._emissiveIntensity, this._environmentIntensity, this._specularIntensity);\r\n\r\n    /**\r\n     * Debug Control allowing disabling the bump map on this material.\r\n     * @internal\r\n     */\r\n    public _disableBumpMap: boolean = false;\r\n\r\n    /**\r\n     * AKA Diffuse Texture in standard nomenclature.\r\n     * @internal\r\n     */\r\n    public _albedoTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Base Weight texture (multiplier to the diffuse and metal lobes).\r\n     * @internal\r\n     */\r\n    public _baseWeightTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Base Diffuse Roughness texture (roughness of the diffuse lobe).\r\n     * @internal\r\n     */\r\n    public _baseDiffuseRoughnessTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * AKA Occlusion Texture in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _ambientTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * AKA Occlusion Texture Intensity in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _ambientTextureStrength: number = 1.0;\r\n\r\n    /**\r\n     * Defines how much the AO map is occluding the analytical lights (point spot...).\r\n     * 1 means it completely occludes it\r\n     * 0 mean it has no impact\r\n     * @internal\r\n     */\r\n    public _ambientTextureImpactOnAnalyticalLights: number = PBRBaseMaterial.DEFAULT_AO_ON_ANALYTICAL_LIGHTS;\r\n\r\n    /**\r\n     * Stores the alpha values in a texture.\r\n     * @internal\r\n     */\r\n    public _opacityTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Stores the reflection values in a texture.\r\n     * @internal\r\n     */\r\n    public _reflectionTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Stores the emissive values in a texture.\r\n     * @internal\r\n     */\r\n    public _emissiveTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * AKA Specular texture in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _reflectivityTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Used to switch from specular/glossiness to metallic/roughness workflow.\r\n     * @internal\r\n     */\r\n    public _metallicTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Specifies the metallic scalar of the metallic/roughness workflow.\r\n     * Can also be used to scale the metalness values of the metallic texture.\r\n     * @internal\r\n     */\r\n    public _metallic: Nullable<number> = null;\r\n\r\n    /**\r\n     * Specifies the roughness scalar of the metallic/roughness workflow.\r\n     * Can also be used to scale the roughness values of the metallic texture.\r\n     * @internal\r\n     */\r\n    public _roughness: Nullable<number> = null;\r\n\r\n    /**\r\n     * In metallic workflow, specifies an F0 factor to help configuring the material F0.\r\n     * By default the indexOfrefraction is used to compute F0;\r\n     *\r\n     * This is used as a factor against the default reflectance at normal incidence to tweak it.\r\n     *\r\n     * F0 = defaultF0 * metallicF0Factor * metallicReflectanceColor;\r\n     * F90 = metallicReflectanceColor;\r\n     * @internal\r\n     */\r\n    public _metallicF0Factor = 1;\r\n\r\n    /**\r\n     * In metallic workflow, specifies an F0 color.\r\n     * By default the F90 is always 1;\r\n     *\r\n     * Please note that this factor is also used as a factor against the default reflectance at normal incidence.\r\n     *\r\n     * F0 = defaultF0_from_IOR * metallicF0Factor * metallicReflectanceColor\r\n     * F90 = metallicF0Factor;\r\n     * @internal\r\n     */\r\n    public _metallicReflectanceColor = Color3.White();\r\n\r\n    /**\r\n     * Specifies that only the A channel from _metallicReflectanceTexture should be used.\r\n     * If false, both RGB and A channels will be used\r\n     * @internal\r\n     */\r\n    public _useOnlyMetallicFromMetallicReflectanceTexture = false;\r\n\r\n    /**\r\n     * Defines to store metallicReflectanceColor in RGB and metallicF0Factor in A\r\n     * This is multiply against the scalar values defined in the material.\r\n     * @internal\r\n     */\r\n    public _metallicReflectanceTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Defines to store reflectanceColor in RGB\r\n     * This is multiplied against the scalar values defined in the material.\r\n     * If both _reflectanceTexture and _metallicReflectanceTexture textures are provided and _useOnlyMetallicFromMetallicReflectanceTexture\r\n     * is false, _metallicReflectanceTexture takes precedence and _reflectanceTexture is not used\r\n     * @internal\r\n     */\r\n    public _reflectanceTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Used to enable roughness/glossiness fetch from a separate channel depending on the current mode.\r\n     * Gray Scale represents roughness in metallic mode and glossiness in specular mode.\r\n     * @internal\r\n     */\r\n    public _microSurfaceTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Stores surface normal data used to displace a mesh in a texture.\r\n     * @internal\r\n     */\r\n    public _bumpTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Stores the pre-calculated light information of a mesh in a texture.\r\n     * @internal\r\n     */\r\n    public _lightmapTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * The color of a material in ambient lighting.\r\n     * @internal\r\n     */\r\n    public _ambientColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * AKA Diffuse Color in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _albedoColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * Base Weight (multiplier to the diffuse and metal lobes).\r\n     * @internal\r\n     */\r\n    public _baseWeight = 1;\r\n\r\n    /**\r\n     * Base Diffuse Roughness (roughness of the diffuse lobe).\r\n     * Can also be used to scale the corresponding texture.\r\n     * @internal\r\n     */\r\n    public _baseDiffuseRoughness: Nullable<number> = null;\r\n\r\n    /**\r\n     * AKA Specular Color in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _reflectivityColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * The color applied when light is reflected from a material.\r\n     * @internal\r\n     */\r\n    public _reflectionColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * The color applied when light is emitted from a material.\r\n     * @internal\r\n     */\r\n    public _emissiveColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * AKA Glossiness in other nomenclature.\r\n     * @internal\r\n     */\r\n    public _microSurface = 0.9;\r\n\r\n    /**\r\n     * Specifies that the material will use the light map as a show map.\r\n     * @internal\r\n     */\r\n    public _useLightmapAsShadowmap = false;\r\n\r\n    /**\r\n     * This parameters will enable/disable Horizon occlusion to prevent normal maps to look shiny when the normal\r\n     * makes the reflect vector face the model (under horizon).\r\n     * @internal\r\n     */\r\n    public _useHorizonOcclusion = true;\r\n\r\n    /**\r\n     * This parameters will enable/disable radiance occlusion by preventing the radiance to lit\r\n     * too much the area relying on ambient texture to define their ambient occlusion.\r\n     * @internal\r\n     */\r\n    public _useRadianceOcclusion = true;\r\n\r\n    /**\r\n     * Specifies that the alpha is coming form the albedo channel alpha channel for alpha blending.\r\n     * @internal\r\n     */\r\n    public _useAlphaFromAlbedoTexture = false;\r\n\r\n    /**\r\n     * Specifies that the material will keeps the specular highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When sun reflects on it you can not see what is behind.\r\n     * @internal\r\n     */\r\n    public _useSpecularOverAlpha = true;\r\n\r\n    /**\r\n     * Specifies if the reflectivity texture contains the glossiness information in its alpha channel.\r\n     * @internal\r\n     */\r\n    public _useMicroSurfaceFromReflectivityMapAlpha = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the roughness information in its alpha channel.\r\n     * @internal\r\n     */\r\n    public _useRoughnessFromMetallicTextureAlpha = true;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the roughness information in its green channel.\r\n     * @internal\r\n     */\r\n    public _useRoughnessFromMetallicTextureGreen = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the metallness information in its blue channel.\r\n     * @internal\r\n     */\r\n    public _useMetallnessFromMetallicTextureBlue = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the ambient occlusion information in its red channel.\r\n     * @internal\r\n     */\r\n    public _useAmbientOcclusionFromMetallicTextureRed = false;\r\n\r\n    /**\r\n     * Specifies if the ambient texture contains the ambient occlusion information in its red channel only.\r\n     * @internal\r\n     */\r\n    public _useAmbientInGrayScale = false;\r\n\r\n    /**\r\n     * In case the reflectivity map does not contain the microsurface information in its alpha channel,\r\n     * The material will try to infer what glossiness each pixel should be.\r\n     * @internal\r\n     */\r\n    public _useAutoMicroSurfaceFromReflectivityMap = false;\r\n\r\n    /**\r\n     * Defines the  falloff type used in this material.\r\n     * It by default is Physical.\r\n     * @internal\r\n     */\r\n    public _lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_PHYSICAL;\r\n\r\n    /**\r\n     * Specifies that the material will keeps the reflection highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When the street lights reflects on it you can not see what is behind.\r\n     * @internal\r\n     */\r\n    public _useRadianceOverAlpha = true;\r\n\r\n    /**\r\n     * Allows using an object space normal map (instead of tangent space).\r\n     * @internal\r\n     */\r\n    public _useObjectSpaceNormalMap = false;\r\n\r\n    /**\r\n     * Allows using the bump map in parallax mode.\r\n     * @internal\r\n     */\r\n    public _useParallax = false;\r\n\r\n    /**\r\n     * Allows using the bump map in parallax occlusion mode.\r\n     * @internal\r\n     */\r\n    public _useParallaxOcclusion = false;\r\n\r\n    /**\r\n     * Controls the scale bias of the parallax mode.\r\n     * @internal\r\n     */\r\n    public _parallaxScaleBias = 0.05;\r\n\r\n    /**\r\n     * If sets to true, disables all the lights affecting the material.\r\n     * @internal\r\n     */\r\n    public _disableLighting = false;\r\n\r\n    /**\r\n     * Number of Simultaneous lights allowed on the material.\r\n     * @internal\r\n     */\r\n    public _maxSimultaneousLights = 4;\r\n\r\n    /**\r\n     * If sets to true, x component of normal map value will be inverted (x = 1.0 - x).\r\n     * @internal\r\n     */\r\n    public _invertNormalMapX = false;\r\n\r\n    /**\r\n     * If sets to true, y component of normal map value will be inverted (y = 1.0 - y).\r\n     * @internal\r\n     */\r\n    public _invertNormalMapY = false;\r\n\r\n    /**\r\n     * If sets to true and backfaceCulling is false, normals will be flipped on the backside.\r\n     * @internal\r\n     */\r\n    public _twoSidedLighting = false;\r\n\r\n    /**\r\n     * Defines the alpha limits in alpha test mode.\r\n     * @internal\r\n     */\r\n    public _alphaCutOff = 0.4;\r\n\r\n    /**\r\n     * A fresnel is applied to the alpha of the model to ensure grazing angles edges are not alpha tested.\r\n     * And/Or occlude the blended part. (alpha is converted to gamma to compute the fresnel)\r\n     * @internal\r\n     */\r\n    public _useAlphaFresnel = false;\r\n\r\n    /**\r\n     * A fresnel is applied to the alpha of the model to ensure grazing angles edges are not alpha tested.\r\n     * And/Or occlude the blended part. (alpha stays linear to compute the fresnel)\r\n     * @internal\r\n     */\r\n    public _useLinearAlphaFresnel = false;\r\n\r\n    /**\r\n     * Specifies the environment BRDF texture used to compute the scale and offset roughness values\r\n     * from cos theta and roughness:\r\n     * http://blog.selfshadow.com/publications/s2013-shading-course/karis/s2013_pbs_epic_notes_v2.pdf\r\n     * @internal\r\n     */\r\n    public _environmentBRDFTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Force the shader to compute irradiance in the fragment shader in order to take bump in account.\r\n     * @internal\r\n     */\r\n    public _forceIrradianceInFragment = false;\r\n\r\n    private _realTimeFiltering: boolean = false;\r\n    /**\r\n     * Enables realtime filtering on the texture.\r\n     */\r\n    public get realTimeFiltering() {\r\n        return this._realTimeFiltering;\r\n    }\r\n    public set realTimeFiltering(b: boolean) {\r\n        this._realTimeFiltering = b;\r\n        this.markAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    private _realTimeFilteringQuality: number = Constants.TEXTURE_FILTERING_QUALITY_LOW;\r\n    /**\r\n     * Quality switch for realtime filtering\r\n     */\r\n    public get realTimeFilteringQuality(): number {\r\n        return this._realTimeFilteringQuality;\r\n    }\r\n    public set realTimeFilteringQuality(n: number) {\r\n        this._realTimeFilteringQuality = n;\r\n        this.markAsDirty(Constants.MATERIAL_TextureDirtyFlag);\r\n    }\r\n\r\n    /**\r\n     * Can this material render to several textures at once\r\n     */\r\n    public override get canRenderToMRT() {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Force normal to face away from face.\r\n     * @internal\r\n     */\r\n    public _forceNormalForward = false;\r\n\r\n    /**\r\n     * Enables specular anti aliasing in the PBR shader.\r\n     * It will both interacts on the Geometry for analytical and IBL lighting.\r\n     * It also prefilter the roughness map based on the bump values.\r\n     * @internal\r\n     */\r\n    public _enableSpecularAntiAliasing = false;\r\n\r\n    /**\r\n     * Default configuration related to image processing available in the PBR Material.\r\n     */\r\n    @serializeAsImageProcessingConfiguration()\r\n    protected _imageProcessingConfiguration: ImageProcessingConfiguration;\r\n\r\n    /**\r\n     * Keep track of the image processing observer to allow dispose and replace.\r\n     */\r\n    private _imageProcessingObserver: Nullable<Observer<ImageProcessingConfiguration>> = null;\r\n\r\n    /**\r\n     * Attaches a new image processing configuration to the PBR Material.\r\n     * @param configuration\r\n     */\r\n    protected _attachImageProcessingConfiguration(configuration: Nullable<ImageProcessingConfiguration>): void {\r\n        if (configuration === this._imageProcessingConfiguration) {\r\n            return;\r\n        }\r\n\r\n        // Detaches observer.\r\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n        }\r\n\r\n        // Pick the scene configuration if needed.\r\n        if (!configuration) {\r\n            this._imageProcessingConfiguration = this.getScene().imageProcessingConfiguration;\r\n        } else {\r\n            this._imageProcessingConfiguration = configuration;\r\n        }\r\n\r\n        // Attaches observer.\r\n        if (this._imageProcessingConfiguration) {\r\n            this._imageProcessingObserver = this._imageProcessingConfiguration.onUpdateParameters.add(() => {\r\n                this._markAllSubMeshesAsImageProcessingDirty();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stores the available render targets.\r\n     */\r\n    private _renderTargets = new SmartArray<RenderTargetTexture>(16);\r\n\r\n    /**\r\n     * Sets the global ambient color for the material used in lighting calculations.\r\n     */\r\n    private _globalAmbientColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * If set to true, no lighting calculations will be applied.\r\n     */\r\n    private _unlit = false;\r\n\r\n    /**\r\n     * If sets to true, the decal map will be applied after the detail map. Else, it is applied before (default: false)\r\n     */\r\n    private _applyDecalMapAfterDetailMap = false;\r\n\r\n    private _debugMode = 0;\r\n\r\n    private _shadersLoaded = false;\r\n    private _breakShaderLoadedCheck = false;\r\n\r\n    /**\r\n     * @internal\r\n     * This is reserved for the inspector.\r\n     * Defines the material debug mode.\r\n     * It helps seeing only some components of the material while troubleshooting.\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public debugMode = 0;\r\n\r\n    /**\r\n     * @internal\r\n     * This is reserved for the inspector.\r\n     * Specify from where on screen the debug mode should start.\r\n     * The value goes from -1 (full screen) to 1 (not visible)\r\n     * It helps with side by side comparison against the final render\r\n     * This defaults to -1\r\n     */\r\n    public debugLimit = -1;\r\n\r\n    /**\r\n     * @internal\r\n     * This is reserved for the inspector.\r\n     * As the default viewing range might not be enough (if the ambient is really small for instance)\r\n     * You can use the factor to better multiply the final value.\r\n     */\r\n    public debugFactor = 1;\r\n\r\n    /**\r\n     * Defines the clear coat layer parameters for the material.\r\n     */\r\n    public readonly clearCoat: PBRClearCoatConfiguration;\r\n\r\n    /**\r\n     * Defines the iridescence layer parameters for the material.\r\n     */\r\n    public readonly iridescence: PBRIridescenceConfiguration;\r\n\r\n    /**\r\n     * Defines the anisotropic parameters for the material.\r\n     */\r\n    public readonly anisotropy: PBRAnisotropicConfiguration;\r\n\r\n    /**\r\n     * Defines the BRDF parameters for the material.\r\n     */\r\n    public readonly brdf: PBRBRDFConfiguration;\r\n\r\n    /**\r\n     * Defines the Sheen parameters for the material.\r\n     */\r\n    public readonly sheen: PBRSheenConfiguration;\r\n\r\n    /**\r\n     * Defines the SubSurface parameters for the material.\r\n     */\r\n    public readonly subSurface: PBRSubSurfaceConfiguration;\r\n\r\n    /**\r\n     * Defines additional PrePass parameters for the material.\r\n     */\r\n    public readonly prePassConfiguration: PrePassConfiguration;\r\n\r\n    /**\r\n     * Defines the detail map parameters for the material.\r\n     */\r\n    public readonly detailMap: DetailMapConfiguration;\r\n\r\n    protected _cacheHasRenderTargetTextures = false;\r\n\r\n    /**\r\n     * Instantiates a new PBRMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     * @param forceGLSL Use the GLSL code generation for the shader (even on WebGPU). Default is false\r\n     */\r\n    constructor(name: string, scene?: Scene, forceGLSL = false) {\r\n        super(name, scene, undefined, forceGLSL || PBRBaseMaterial.ForceGLSL);\r\n\r\n        this.brdf = new PBRBRDFConfiguration(this);\r\n        this.clearCoat = new PBRClearCoatConfiguration(this);\r\n        this.iridescence = new PBRIridescenceConfiguration(this);\r\n        this.anisotropy = new PBRAnisotropicConfiguration(this);\r\n        this.sheen = new PBRSheenConfiguration(this);\r\n        this.subSurface = new PBRSubSurfaceConfiguration(this);\r\n        this.detailMap = new DetailMapConfiguration(this);\r\n\r\n        // Setup the default processing configuration to the scene.\r\n        this._attachImageProcessingConfiguration(null);\r\n\r\n        this.getRenderTargetTextures = (): SmartArray<RenderTargetTexture> => {\r\n            this._renderTargets.reset();\r\n\r\n            if (MaterialFlags.ReflectionTextureEnabled && this._reflectionTexture && this._reflectionTexture.isRenderTarget) {\r\n                this._renderTargets.push(<RenderTargetTexture>this._reflectionTexture);\r\n            }\r\n\r\n            this._eventInfo.renderTargets = this._renderTargets;\r\n            this._callbackPluginEventFillRenderTargetTextures(this._eventInfo);\r\n\r\n            return this._renderTargets;\r\n        };\r\n\r\n        this._environmentBRDFTexture = GetEnvironmentBRDFTexture(this.getScene());\r\n        this.prePassConfiguration = new PrePassConfiguration();\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that current material needs to register RTT\r\n     */\r\n    public override get hasRenderTargetTextures(): boolean {\r\n        if (MaterialFlags.ReflectionTextureEnabled && this._reflectionTexture && this._reflectionTexture.isRenderTarget) {\r\n            return true;\r\n        }\r\n\r\n        return this._cacheHasRenderTargetTextures;\r\n    }\r\n\r\n    /**\r\n     * Can this material render to prepass\r\n     */\r\n    public override get isPrePassCapable(): boolean {\r\n        return !this.disableDepthWrite;\r\n    }\r\n\r\n    /**\r\n     * @returns the name of the material class.\r\n     */\r\n    public override getClassName(): string {\r\n        return \"PBRBaseMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Returns true if alpha blending should be disabled.\r\n     */\r\n    protected override get _disableAlphaBlending(): boolean {\r\n        return (\r\n            this._transparencyMode === PBRBaseMaterial.PBRMATERIAL_OPAQUE ||\r\n            this._transparencyMode === PBRBaseMaterial.PBRMATERIAL_ALPHATEST ||\r\n            this.subSurface?.disableAlphaBlending\r\n        );\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not this material should be rendered in alpha blend mode.\r\n     */\r\n    public override needAlphaBlending(): boolean {\r\n        if (this._hasTransparencyMode) {\r\n            return this._transparencyModeIsBlend;\r\n        }\r\n\r\n        if (this._disableAlphaBlending) {\r\n            return false;\r\n        }\r\n\r\n        return this.alpha < 1.0 || this._opacityTexture != null || this._shouldUseAlphaFromAlbedoTexture();\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not this material should be rendered in alpha test mode.\r\n     */\r\n    public override needAlphaTesting(): boolean {\r\n        if (this._hasTransparencyMode) {\r\n            return this._transparencyModeIsTest;\r\n        }\r\n\r\n        if (this.subSurface?.disableAlphaBlending) {\r\n            return false;\r\n        }\r\n\r\n        return this._hasAlphaChannel() && (this._transparencyMode == null || this._transparencyMode === PBRBaseMaterial.PBRMATERIAL_ALPHATEST);\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not the alpha value of the albedo texture should be used for alpha blending.\r\n     */\r\n    protected _shouldUseAlphaFromAlbedoTexture(): boolean {\r\n        return this._albedoTexture != null && this._albedoTexture.hasAlpha && this._useAlphaFromAlbedoTexture && this._transparencyMode !== PBRBaseMaterial.PBRMATERIAL_OPAQUE;\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not there is a usable alpha channel for transparency.\r\n     */\r\n    protected _hasAlphaChannel(): boolean {\r\n        return (this._albedoTexture != null && this._albedoTexture.hasAlpha) || this._opacityTexture != null;\r\n    }\r\n\r\n    /**\r\n     * @returns the texture used for the alpha test.\r\n     */\r\n    public override getAlphaTestTexture(): Nullable<BaseTexture> {\r\n        return this._albedoTexture;\r\n    }\r\n\r\n    /**\r\n     * Specifies that the submesh is ready to be used.\r\n     * @param mesh - BJS mesh.\r\n     * @param subMesh - A submesh of the BJS mesh.  Used to check if it is ready.\r\n     * @param useInstances - Specifies that instances should be used.\r\n     * @returns - boolean indicating that the submesh is ready or not.\r\n     */\r\n    public override isReadyForSubMesh(mesh: AbstractMesh, subMesh: SubMesh, useInstances?: boolean): boolean {\r\n        if (!this._uniformBufferLayoutBuilt) {\r\n            this.buildUniformLayout();\r\n        }\r\n\r\n        const drawWrapper = subMesh._drawWrapper;\r\n\r\n        if (drawWrapper.effect && this.isFrozen) {\r\n            if (drawWrapper._wasPreviouslyReady && drawWrapper._wasPreviouslyUsingInstances === useInstances) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!subMesh.materialDefines) {\r\n            this._callbackPluginEventGeneric(MaterialPluginEvent.GetDefineNames, this._eventInfo);\r\n            subMesh.materialDefines = new PBRMaterialDefines(this._eventInfo.defineNames);\r\n        }\r\n\r\n        const defines = <PBRMaterialDefines>subMesh.materialDefines;\r\n        if (this._isReadyForSubMesh(subMesh)) {\r\n            return true;\r\n        }\r\n\r\n        const scene = this.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        if (defines._areTexturesDirty) {\r\n            this._eventInfo.hasRenderTargetTextures = false;\r\n            this._callbackPluginEventHasRenderTargetTextures(this._eventInfo);\r\n            this._cacheHasRenderTargetTextures = this._eventInfo.hasRenderTargetTextures;\r\n            if (scene.texturesEnabled) {\r\n                if (this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                    if (!this._albedoTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._baseWeightTexture && MaterialFlags.BaseWeightTextureEnabled) {\r\n                    if (!this._baseWeightTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._baseDiffuseRoughnessTexture && MaterialFlags.BaseDiffuseRoughnessTextureEnabled) {\r\n                    if (!this._baseDiffuseRoughnessTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._ambientTexture && MaterialFlags.AmbientTextureEnabled) {\r\n                    if (!this._ambientTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                    if (!this._opacityTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                const reflectionTexture = this._getReflectionTexture();\r\n                if (reflectionTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    if (!reflectionTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                    if (reflectionTexture.irradianceTexture) {\r\n                        if (!reflectionTexture.irradianceTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    } else {\r\n                        // Not ready until spherical are ready too.\r\n                        if (!reflectionTexture.sphericalPolynomial && reflectionTexture.getInternalTexture()?._sphericalPolynomialPromise) {\r\n                            return false;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if (this._lightmapTexture && MaterialFlags.LightmapTextureEnabled) {\r\n                    if (!this._lightmapTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._emissiveTexture && MaterialFlags.EmissiveTextureEnabled) {\r\n                    if (!this._emissiveTexture.isReadyOrNotBlocking()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (MaterialFlags.SpecularTextureEnabled) {\r\n                    if (this._metallicTexture) {\r\n                        if (!this._metallicTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    } else if (this._reflectivityTexture) {\r\n                        if (!this._reflectivityTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    }\r\n\r\n                    if (this._metallicReflectanceTexture) {\r\n                        if (!this._metallicReflectanceTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    }\r\n\r\n                    if (this._reflectanceTexture) {\r\n                        if (!this._reflectanceTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    }\r\n\r\n                    if (this._microSurfaceTexture) {\r\n                        if (!this._microSurfaceTexture.isReadyOrNotBlocking()) {\r\n                            return false;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                if (engine.getCaps().standardDerivatives && this._bumpTexture && MaterialFlags.BumpTextureEnabled && !this._disableBumpMap) {\r\n                    // Bump texture cannot be not blocking.\r\n                    if (!this._bumpTexture.isReady()) {\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                if (this._environmentBRDFTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    // This is blocking.\r\n                    if (!this._environmentBRDFTexture.isReady()) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this._eventInfo.isReadyForSubMesh = true;\r\n        this._eventInfo.defines = defines;\r\n        this._eventInfo.subMesh = subMesh;\r\n        this._callbackPluginEventIsReadyForSubMesh(this._eventInfo);\r\n\r\n        if (!this._eventInfo.isReadyForSubMesh) {\r\n            return false;\r\n        }\r\n\r\n        if (defines._areImageProcessingDirty && this._imageProcessingConfiguration) {\r\n            if (!this._imageProcessingConfiguration.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        // Check if Area Lights have LTC texture.\r\n        if (defines[\"AREALIGHTUSED\"]) {\r\n            for (let index = 0; index < mesh.lightSources.length; index++) {\r\n                if (!mesh.lightSources[index]._isReady()) {\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!engine.getCaps().standardDerivatives && !mesh.isVerticesDataPresent(VertexBuffer.NormalKind)) {\r\n            mesh.createNormals(true);\r\n            Logger.Warn(\"PBRMaterial: Normals have been created for the mesh: \" + mesh.name);\r\n        }\r\n\r\n        const previousEffect = subMesh.effect;\r\n        const lightDisposed = defines._areLightsDisposed;\r\n        let effect = this._prepareEffect(mesh, defines, this.onCompiled, this.onError, useInstances, null, subMesh.getRenderingMesh().hasThinInstances);\r\n\r\n        let forceWasNotReadyPreviously = false;\r\n\r\n        if (effect) {\r\n            if (this._onEffectCreatedObservable) {\r\n                onCreatedEffectParameters.effect = effect;\r\n                onCreatedEffectParameters.subMesh = subMesh;\r\n                this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);\r\n            }\r\n\r\n            // Use previous effect while new one is compiling\r\n            if (this.allowShaderHotSwapping && previousEffect && !effect.isReady()) {\r\n                effect = previousEffect;\r\n                defines.markAsUnprocessed();\r\n\r\n                forceWasNotReadyPreviously = this.isFrozen;\r\n\r\n                if (lightDisposed) {\r\n                    // re register in case it takes more than one frame.\r\n                    defines._areLightsDisposed = true;\r\n                    return false;\r\n                }\r\n            } else {\r\n                scene.resetCachedMaterial();\r\n                subMesh.setEffect(effect, defines, this._materialContext);\r\n            }\r\n        }\r\n\r\n        if (!subMesh.effect || !subMesh.effect.isReady()) {\r\n            return false;\r\n        }\r\n\r\n        defines._renderId = scene.getRenderId();\r\n        drawWrapper._wasPreviouslyReady = forceWasNotReadyPreviously ? false : true;\r\n        drawWrapper._wasPreviouslyUsingInstances = !!useInstances;\r\n\r\n        this._checkScenePerformancePriority();\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Specifies if the material uses metallic roughness workflow.\r\n     * @returns boolean specifying if the material uses metallic roughness workflow.\r\n     */\r\n    public isMetallicWorkflow(): boolean {\r\n        if (this._metallic != null || this._roughness != null || this._metallicTexture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    private _prepareEffect(\r\n        mesh: AbstractMesh,\r\n        defines: PBRMaterialDefines,\r\n        onCompiled: Nullable<(effect: Effect) => void> = null,\r\n        onError: Nullable<(effect: Effect, errors: string) => void> = null,\r\n        useInstances: Nullable<boolean> = null,\r\n        useClipPlane: Nullable<boolean> = null,\r\n        useThinInstances: boolean\r\n    ): Nullable<Effect> {\r\n        this._prepareDefines(mesh, defines, useInstances, useClipPlane, useThinInstances);\r\n\r\n        if (!defines.isDirty) {\r\n            return null;\r\n        }\r\n\r\n        defines.markAsProcessed();\r\n\r\n        const scene = this.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        // Fallbacks\r\n        const fallbacks = new EffectFallbacks();\r\n        let fallbackRank = 0;\r\n        if (defines.USESPHERICALINVERTEX) {\r\n            fallbacks.addFallback(fallbackRank++, \"USESPHERICALINVERTEX\");\r\n        }\r\n\r\n        if (defines.FOG) {\r\n            fallbacks.addFallback(fallbackRank, \"FOG\");\r\n        }\r\n        if (defines.SPECULARAA) {\r\n            fallbacks.addFallback(fallbackRank, \"SPECULARAA\");\r\n        }\r\n        if (defines.POINTSIZE) {\r\n            fallbacks.addFallback(fallbackRank, \"POINTSIZE\");\r\n        }\r\n        if (defines.LOGARITHMICDEPTH) {\r\n            fallbacks.addFallback(fallbackRank, \"LOGARITHMICDEPTH\");\r\n        }\r\n        if (defines.PARALLAX) {\r\n            fallbacks.addFallback(fallbackRank, \"PARALLAX\");\r\n        }\r\n        if (defines.PARALLAX_RHS) {\r\n            fallbacks.addFallback(fallbackRank, \"PARALLAX_RHS\");\r\n        }\r\n        if (defines.PARALLAXOCCLUSION) {\r\n            fallbacks.addFallback(fallbackRank++, \"PARALLAXOCCLUSION\");\r\n        }\r\n\r\n        if (defines.ENVIRONMENTBRDF) {\r\n            fallbacks.addFallback(fallbackRank++, \"ENVIRONMENTBRDF\");\r\n        }\r\n\r\n        if (defines.TANGENT) {\r\n            fallbacks.addFallback(fallbackRank++, \"TANGENT\");\r\n        }\r\n\r\n        if (defines.BUMP) {\r\n            fallbacks.addFallback(fallbackRank++, \"BUMP\");\r\n        }\r\n\r\n        fallbackRank = HandleFallbacksForShadows(defines, fallbacks, this._maxSimultaneousLights, fallbackRank++);\r\n\r\n        if (defines.SPECULARTERM) {\r\n            fallbacks.addFallback(fallbackRank++, \"SPECULARTERM\");\r\n        }\r\n\r\n        if (defines.USESPHERICALFROMREFLECTIONMAP) {\r\n            fallbacks.addFallback(fallbackRank++, \"USESPHERICALFROMREFLECTIONMAP\");\r\n        }\r\n\r\n        if (defines.USEIRRADIANCEMAP) {\r\n            fallbacks.addFallback(fallbackRank++, \"USEIRRADIANCEMAP\");\r\n        }\r\n\r\n        if (defines.LIGHTMAP) {\r\n            fallbacks.addFallback(fallbackRank++, \"LIGHTMAP\");\r\n        }\r\n\r\n        if (defines.NORMAL) {\r\n            fallbacks.addFallback(fallbackRank++, \"NORMAL\");\r\n        }\r\n\r\n        if (defines.AMBIENT) {\r\n            fallbacks.addFallback(fallbackRank++, \"AMBIENT\");\r\n        }\r\n\r\n        if (defines.EMISSIVE) {\r\n            fallbacks.addFallback(fallbackRank++, \"EMISSIVE\");\r\n        }\r\n\r\n        if (defines.VERTEXCOLOR) {\r\n            fallbacks.addFallback(fallbackRank++, \"VERTEXCOLOR\");\r\n        }\r\n\r\n        if (defines.MORPHTARGETS) {\r\n            fallbacks.addFallback(fallbackRank++, \"MORPHTARGETS\");\r\n        }\r\n\r\n        if (defines.MULTIVIEW) {\r\n            fallbacks.addFallback(0, \"MULTIVIEW\");\r\n        }\r\n\r\n        //Attributes\r\n        const attribs = [VertexBuffer.PositionKind];\r\n\r\n        if (defines.NORMAL) {\r\n            attribs.push(VertexBuffer.NormalKind);\r\n        }\r\n\r\n        if (defines.TANGENT) {\r\n            attribs.push(VertexBuffer.TangentKind);\r\n        }\r\n\r\n        for (let i = 1; i <= Constants.MAX_SUPPORTED_UV_SETS; ++i) {\r\n            if (defines[\"UV\" + i]) {\r\n                attribs.push(`uv${i === 1 ? \"\" : i}`);\r\n            }\r\n        }\r\n\r\n        if (defines.VERTEXCOLOR) {\r\n            attribs.push(VertexBuffer.ColorKind);\r\n        }\r\n\r\n        PrepareAttributesForBones(attribs, mesh, defines, fallbacks);\r\n        PrepareAttributesForInstances(attribs, defines);\r\n        PrepareAttributesForMorphTargets(attribs, mesh, defines);\r\n        PrepareAttributesForBakedVertexAnimation(attribs, mesh, defines);\r\n\r\n        let shaderName = \"pbr\";\r\n\r\n        const uniforms = [\r\n            \"world\",\r\n            \"view\",\r\n            \"viewProjection\",\r\n            \"vEyePosition\",\r\n            \"vLightsType\",\r\n            \"vAmbientColor\",\r\n            \"vAlbedoColor\",\r\n            \"baseWeight\",\r\n            \"baseDiffuseRoughness\",\r\n            \"vReflectivityColor\",\r\n            \"vMetallicReflectanceFactors\",\r\n            \"vEmissiveColor\",\r\n            \"visibility\",\r\n            \"vReflectionColor\",\r\n            \"vFogInfos\",\r\n            \"vFogColor\",\r\n            \"pointSize\",\r\n            \"vAlbedoInfos\",\r\n            \"vBaseWeightInfos\",\r\n            \"vBaseDiffuseRoughnessInfos\",\r\n            \"vAmbientInfos\",\r\n            \"vOpacityInfos\",\r\n            \"vReflectionInfos\",\r\n            \"vReflectionPosition\",\r\n            \"vReflectionSize\",\r\n            \"vEmissiveInfos\",\r\n            \"vReflectivityInfos\",\r\n            \"vReflectionFilteringInfo\",\r\n            \"vMetallicReflectanceInfos\",\r\n            \"vReflectanceInfos\",\r\n            \"vMicroSurfaceSamplerInfos\",\r\n            \"vBumpInfos\",\r\n            \"vLightmapInfos\",\r\n            \"mBones\",\r\n            \"albedoMatrix\",\r\n            \"baseWeightMatrix\",\r\n            \"baseDiffuseRoughnessMatrix\",\r\n            \"ambientMatrix\",\r\n            \"opacityMatrix\",\r\n            \"reflectionMatrix\",\r\n            \"emissiveMatrix\",\r\n            \"reflectivityMatrix\",\r\n            \"normalMatrix\",\r\n            \"microSurfaceSamplerMatrix\",\r\n            \"bumpMatrix\",\r\n            \"lightmapMatrix\",\r\n            \"metallicReflectanceMatrix\",\r\n            \"reflectanceMatrix\",\r\n            \"vLightingIntensity\",\r\n            \"logarithmicDepthConstant\",\r\n            \"vSphericalX\",\r\n            \"vSphericalY\",\r\n            \"vSphericalZ\",\r\n            \"vSphericalXX_ZZ\",\r\n            \"vSphericalYY_ZZ\",\r\n            \"vSphericalZZ\",\r\n            \"vSphericalXY\",\r\n            \"vSphericalYZ\",\r\n            \"vSphericalZX\",\r\n            \"vSphericalL00\",\r\n            \"vSphericalL1_1\",\r\n            \"vSphericalL10\",\r\n            \"vSphericalL11\",\r\n            \"vSphericalL2_2\",\r\n            \"vSphericalL2_1\",\r\n            \"vSphericalL20\",\r\n            \"vSphericalL21\",\r\n            \"vSphericalL22\",\r\n            \"vReflectionMicrosurfaceInfos\",\r\n            \"vReflectionDominantDirection\",\r\n            \"vTangentSpaceParams\",\r\n            \"boneTextureWidth\",\r\n            \"vDebugMode\",\r\n            \"morphTargetTextureInfo\",\r\n            \"morphTargetTextureIndices\",\r\n            \"cameraInfo\",\r\n        ];\r\n\r\n        const samplers = [\r\n            \"albedoSampler\",\r\n            \"baseWeightSampler\",\r\n            \"baseDiffuseRoughnessSampler\",\r\n            \"reflectivitySampler\",\r\n            \"ambientSampler\",\r\n            \"emissiveSampler\",\r\n            \"bumpSampler\",\r\n            \"lightmapSampler\",\r\n            \"opacitySampler\",\r\n            \"reflectionSampler\",\r\n            \"reflectionSamplerLow\",\r\n            \"reflectionSamplerHigh\",\r\n            \"irradianceSampler\",\r\n            \"microSurfaceSampler\",\r\n            \"environmentBrdfSampler\",\r\n            \"boneSampler\",\r\n            \"metallicReflectanceSampler\",\r\n            \"reflectanceSampler\",\r\n            \"morphTargets\",\r\n            \"oitDepthSampler\",\r\n            \"oitFrontColorSampler\",\r\n            \"icdfSampler\",\r\n            \"areaLightsLTC1Sampler\",\r\n            \"areaLightsLTC2Sampler\",\r\n        ];\r\n\r\n        const uniformBuffers = [\"Material\", \"Scene\", \"Mesh\"];\r\n\r\n        const indexParameters = { maxSimultaneousLights: this._maxSimultaneousLights, maxSimultaneousMorphTargets: defines.NUM_MORPH_INFLUENCERS };\r\n\r\n        this._eventInfo.fallbacks = fallbacks;\r\n        this._eventInfo.fallbackRank = fallbackRank;\r\n        this._eventInfo.defines = defines;\r\n        this._eventInfo.uniforms = uniforms;\r\n        this._eventInfo.attributes = attribs;\r\n        this._eventInfo.samplers = samplers;\r\n        this._eventInfo.uniformBuffersNames = uniformBuffers;\r\n        this._eventInfo.customCode = undefined;\r\n        this._eventInfo.mesh = mesh;\r\n        this._eventInfo.indexParameters = indexParameters;\r\n        this._callbackPluginEventGeneric(MaterialPluginEvent.PrepareEffect, this._eventInfo);\r\n\r\n        MaterialHelperGeometryRendering.AddUniformsAndSamplers(uniforms, samplers);\r\n\r\n        PrePassConfiguration.AddUniforms(uniforms);\r\n        PrePassConfiguration.AddSamplers(samplers);\r\n        AddClipPlaneUniforms(uniforms);\r\n\r\n        if (ImageProcessingConfiguration) {\r\n            ImageProcessingConfiguration.PrepareUniforms(uniforms, defines);\r\n            ImageProcessingConfiguration.PrepareSamplers(samplers, defines);\r\n        }\r\n\r\n        PrepareUniformsAndSamplersList(<IEffectCreationOptions>{\r\n            uniformsNames: uniforms,\r\n            uniformBuffersNames: uniformBuffers,\r\n            samplers: samplers,\r\n            defines: defines,\r\n            maxSimultaneousLights: this._maxSimultaneousLights,\r\n        });\r\n\r\n        const csnrOptions: ICustomShaderNameResolveOptions = {};\r\n\r\n        if (this.customShaderNameResolve) {\r\n            shaderName = this.customShaderNameResolve(shaderName, uniforms, uniformBuffers, samplers, defines, attribs, csnrOptions);\r\n        }\r\n\r\n        const join = defines.toString();\r\n        const effect = engine.createEffect(\r\n            shaderName,\r\n            <IEffectCreationOptions>{\r\n                attributes: attribs,\r\n                uniformsNames: uniforms,\r\n                uniformBuffersNames: uniformBuffers,\r\n                samplers: samplers,\r\n                defines: join,\r\n                fallbacks: fallbacks,\r\n                onCompiled: onCompiled,\r\n                onError: onError,\r\n                indexParameters,\r\n                processFinalCode: csnrOptions.processFinalCode,\r\n                processCodeAfterIncludes: this._eventInfo.customCode,\r\n                multiTarget: defines.PREPASS,\r\n                shaderLanguage: this._shaderLanguage,\r\n                extraInitializationsAsync: this._shadersLoaded\r\n                    ? undefined\r\n                    : async () => {\r\n                          if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n                              await Promise.all([import(\"../../ShadersWGSL/pbr.vertex\"), import(\"../../ShadersWGSL/pbr.fragment\")]);\r\n                          } else {\r\n                              await Promise.all([import(\"../../Shaders/pbr.vertex\"), import(\"../../Shaders/pbr.fragment\")]);\r\n                          }\r\n\r\n                          this._shadersLoaded = true;\r\n                      },\r\n            },\r\n            engine\r\n        );\r\n\r\n        this._eventInfo.customCode = undefined;\r\n\r\n        return effect;\r\n    }\r\n\r\n    private _prepareDefines(\r\n        mesh: AbstractMesh,\r\n        defines: PBRMaterialDefines,\r\n        useInstances: Nullable<boolean> = null,\r\n        useClipPlane: Nullable<boolean> = null,\r\n        useThinInstances: boolean = false\r\n    ): void {\r\n        const scene = this.getScene();\r\n        const engine = scene.getEngine();\r\n\r\n        // Lights\r\n        PrepareDefinesForLights(scene, mesh, defines, true, this._maxSimultaneousLights, this._disableLighting);\r\n        defines._needNormals = true;\r\n\r\n        // Multiview\r\n        PrepareDefinesForMultiview(scene, defines);\r\n\r\n        // PrePass\r\n        const oit = this.needAlphaBlendingForMesh(mesh) && this.getScene().useOrderIndependentTransparency;\r\n        PrepareDefinesForPrePass(scene, defines, this.canRenderToMRT && !oit);\r\n\r\n        // Order independant transparency\r\n        PrepareDefinesForOIT(scene, defines, oit);\r\n\r\n        MaterialHelperGeometryRendering.PrepareDefines(engine.currentRenderPassId, mesh, defines);\r\n\r\n        // Textures\r\n        defines.METALLICWORKFLOW = this.isMetallicWorkflow();\r\n        if (defines._areTexturesDirty) {\r\n            defines._needUVs = false;\r\n            for (let i = 1; i <= Constants.MAX_SUPPORTED_UV_SETS; ++i) {\r\n                defines[\"MAINUV\" + i] = false;\r\n            }\r\n            if (scene.texturesEnabled) {\r\n                defines.ALBEDODIRECTUV = 0;\r\n                defines.BASE_WEIGHTDIRECTUV = 0;\r\n                defines.BASE_DIFFUSE_ROUGHNESSDIRECTUV = 0;\r\n                defines.AMBIENTDIRECTUV = 0;\r\n                defines.OPACITYDIRECTUV = 0;\r\n                defines.EMISSIVEDIRECTUV = 0;\r\n                defines.REFLECTIVITYDIRECTUV = 0;\r\n                defines.MICROSURFACEMAPDIRECTUV = 0;\r\n                defines.METALLIC_REFLECTANCEDIRECTUV = 0;\r\n                defines.REFLECTANCEDIRECTUV = 0;\r\n                defines.BUMPDIRECTUV = 0;\r\n                defines.LIGHTMAPDIRECTUV = 0;\r\n\r\n                if (engine.getCaps().textureLOD) {\r\n                    defines.LODBASEDMICROSFURACE = true;\r\n                }\r\n\r\n                if (this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._albedoTexture, defines, \"ALBEDO\");\r\n                    defines.GAMMAALBEDO = this._albedoTexture.gammaSpace;\r\n                } else {\r\n                    defines.ALBEDO = false;\r\n                }\r\n\r\n                if (this._baseWeightTexture && MaterialFlags.BaseWeightTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._baseWeightTexture, defines, \"BASE_WEIGHT\");\r\n                } else {\r\n                    defines.BASE_WEIGHT = false;\r\n                }\r\n\r\n                if (this._baseDiffuseRoughnessTexture && MaterialFlags.BaseDiffuseRoughnessTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._baseDiffuseRoughnessTexture, defines, \"BASE_DIFFUSE_ROUGHNESS\");\r\n                } else {\r\n                    defines.BASE_DIFFUSE_ROUGHNESS = false;\r\n                }\r\n\r\n                if (this._ambientTexture && MaterialFlags.AmbientTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._ambientTexture, defines, \"AMBIENT\");\r\n                    defines.AMBIENTINGRAYSCALE = this._useAmbientInGrayScale;\r\n                } else {\r\n                    defines.AMBIENT = false;\r\n                }\r\n\r\n                if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._opacityTexture, defines, \"OPACITY\");\r\n                    defines.OPACITYRGB = this._opacityTexture.getAlphaFromRGB;\r\n                } else {\r\n                    defines.OPACITY = false;\r\n                }\r\n\r\n                const reflectionTexture = this._getReflectionTexture();\r\n                if (reflectionTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    defines.REFLECTION = true;\r\n                    defines.GAMMAREFLECTION = reflectionTexture.gammaSpace;\r\n                    defines.RGBDREFLECTION = reflectionTexture.isRGBD;\r\n                    defines.LODINREFLECTIONALPHA = reflectionTexture.lodLevelInAlpha;\r\n                    defines.LINEARSPECULARREFLECTION = reflectionTexture.linearSpecularLOD;\r\n                    defines.USEIRRADIANCEMAP = false;\r\n\r\n                    if (this.realTimeFiltering && this.realTimeFilteringQuality > 0) {\r\n                        defines.NUM_SAMPLES = \"\" + this.realTimeFilteringQuality;\r\n                        if (engine._features.needTypeSuffixInShaderConstants) {\r\n                            defines.NUM_SAMPLES = defines.NUM_SAMPLES + \"u\";\r\n                        }\r\n\r\n                        defines.REALTIME_FILTERING = true;\r\n                        if (this.getScene().iblCdfGenerator) {\r\n                            defines.IBL_CDF_FILTERING = true;\r\n                        }\r\n                    } else {\r\n                        defines.REALTIME_FILTERING = false;\r\n                    }\r\n\r\n                    defines.INVERTCUBICMAP = reflectionTexture.coordinatesMode === Texture.INVCUBIC_MODE;\r\n                    defines.REFLECTIONMAP_3D = reflectionTexture.isCube;\r\n                    defines.REFLECTIONMAP_OPPOSITEZ = defines.REFLECTIONMAP_3D && this.getScene().useRightHandedSystem ? !reflectionTexture.invertZ : reflectionTexture.invertZ;\r\n\r\n                    defines.REFLECTIONMAP_CUBIC = false;\r\n                    defines.REFLECTIONMAP_EXPLICIT = false;\r\n                    defines.REFLECTIONMAP_PLANAR = false;\r\n                    defines.REFLECTIONMAP_PROJECTION = false;\r\n                    defines.REFLECTIONMAP_SKYBOX = false;\r\n                    defines.REFLECTIONMAP_SPHERICAL = false;\r\n                    defines.REFLECTIONMAP_EQUIRECTANGULAR = false;\r\n                    defines.REFLECTIONMAP_EQUIRECTANGULAR_FIXED = false;\r\n                    defines.REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = false;\r\n\r\n                    switch (reflectionTexture.coordinatesMode) {\r\n                        case Texture.EXPLICIT_MODE:\r\n                            defines.REFLECTIONMAP_EXPLICIT = true;\r\n                            break;\r\n                        case Texture.PLANAR_MODE:\r\n                            defines.REFLECTIONMAP_PLANAR = true;\r\n                            break;\r\n                        case Texture.PROJECTION_MODE:\r\n                            defines.REFLECTIONMAP_PROJECTION = true;\r\n                            break;\r\n                        case Texture.SKYBOX_MODE:\r\n                            defines.REFLECTIONMAP_SKYBOX = true;\r\n                            break;\r\n                        case Texture.SPHERICAL_MODE:\r\n                            defines.REFLECTIONMAP_SPHERICAL = true;\r\n                            break;\r\n                        case Texture.EQUIRECTANGULAR_MODE:\r\n                            defines.REFLECTIONMAP_EQUIRECTANGULAR = true;\r\n                            break;\r\n                        case Texture.FIXED_EQUIRECTANGULAR_MODE:\r\n                            defines.REFLECTIONMAP_EQUIRECTANGULAR_FIXED = true;\r\n                            break;\r\n                        case Texture.FIXED_EQUIRECTANGULAR_MIRRORED_MODE:\r\n                            defines.REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = true;\r\n                            break;\r\n                        case Texture.CUBIC_MODE:\r\n                        case Texture.INVCUBIC_MODE:\r\n                        default:\r\n                            defines.REFLECTIONMAP_CUBIC = true;\r\n                            defines.USE_LOCAL_REFLECTIONMAP_CUBIC = (<any>reflectionTexture).boundingBoxSize ? true : false;\r\n                            break;\r\n                    }\r\n\r\n                    if (reflectionTexture.coordinatesMode !== Texture.SKYBOX_MODE) {\r\n                        if (reflectionTexture.irradianceTexture) {\r\n                            defines.USEIRRADIANCEMAP = true;\r\n                            defines.USESPHERICALFROMREFLECTIONMAP = false;\r\n                            defines.USESPHERICALINVERTEX = false;\r\n                            if (reflectionTexture.irradianceTexture._dominantDirection) {\r\n                                defines.USE_IRRADIANCE_DOMINANT_DIRECTION = true;\r\n                            }\r\n                        }\r\n                        // Assume using spherical polynomial if the reflection texture is a cube map\r\n                        else if (reflectionTexture.isCube) {\r\n                            defines.USESPHERICALFROMREFLECTIONMAP = true;\r\n                            defines.USEIRRADIANCEMAP = false;\r\n                            defines.USE_IRRADIANCE_DOMINANT_DIRECTION = false;\r\n                            if (\r\n                                this._forceIrradianceInFragment ||\r\n                                this.realTimeFiltering ||\r\n                                this._twoSidedLighting ||\r\n                                engine.getCaps().maxVaryingVectors <= 8 ||\r\n                                this._baseDiffuseRoughnessTexture\r\n                            ) {\r\n                                defines.USESPHERICALINVERTEX = false;\r\n                            } else {\r\n                                defines.USESPHERICALINVERTEX = true;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    defines.REFLECTION = false;\r\n                    defines.REFLECTIONMAP_3D = false;\r\n                    defines.REFLECTIONMAP_SPHERICAL = false;\r\n                    defines.REFLECTIONMAP_PLANAR = false;\r\n                    defines.REFLECTIONMAP_CUBIC = false;\r\n                    defines.USE_LOCAL_REFLECTIONMAP_CUBIC = false;\r\n                    defines.REFLECTIONMAP_PROJECTION = false;\r\n                    defines.REFLECTIONMAP_SKYBOX = false;\r\n                    defines.REFLECTIONMAP_EXPLICIT = false;\r\n                    defines.REFLECTIONMAP_EQUIRECTANGULAR = false;\r\n                    defines.REFLECTIONMAP_EQUIRECTANGULAR_FIXED = false;\r\n                    defines.REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED = false;\r\n                    defines.INVERTCUBICMAP = false;\r\n                    defines.USESPHERICALFROMREFLECTIONMAP = false;\r\n                    defines.USEIRRADIANCEMAP = false;\r\n                    defines.USE_IRRADIANCE_DOMINANT_DIRECTION = false;\r\n                    defines.USESPHERICALINVERTEX = false;\r\n                    defines.REFLECTIONMAP_OPPOSITEZ = false;\r\n                    defines.LODINREFLECTIONALPHA = false;\r\n                    defines.GAMMAREFLECTION = false;\r\n                    defines.RGBDREFLECTION = false;\r\n                    defines.LINEARSPECULARREFLECTION = false;\r\n                }\r\n\r\n                if (this._lightmapTexture && MaterialFlags.LightmapTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._lightmapTexture, defines, \"LIGHTMAP\");\r\n                    defines.USELIGHTMAPASSHADOWMAP = this._useLightmapAsShadowmap;\r\n                    defines.GAMMALIGHTMAP = this._lightmapTexture.gammaSpace;\r\n                    defines.RGBDLIGHTMAP = this._lightmapTexture.isRGBD;\r\n                } else {\r\n                    defines.LIGHTMAP = false;\r\n                }\r\n\r\n                if (this._emissiveTexture && MaterialFlags.EmissiveTextureEnabled) {\r\n                    PrepareDefinesForMergedUV(this._emissiveTexture, defines, \"EMISSIVE\");\r\n                    defines.GAMMAEMISSIVE = this._emissiveTexture.gammaSpace;\r\n                } else {\r\n                    defines.EMISSIVE = false;\r\n                }\r\n\r\n                if (MaterialFlags.SpecularTextureEnabled) {\r\n                    if (this._metallicTexture) {\r\n                        PrepareDefinesForMergedUV(this._metallicTexture, defines, \"REFLECTIVITY\");\r\n                        defines.ROUGHNESSSTOREINMETALMAPALPHA = this._useRoughnessFromMetallicTextureAlpha;\r\n                        defines.ROUGHNESSSTOREINMETALMAPGREEN = !this._useRoughnessFromMetallicTextureAlpha && this._useRoughnessFromMetallicTextureGreen;\r\n                        defines.METALLNESSSTOREINMETALMAPBLUE = this._useMetallnessFromMetallicTextureBlue;\r\n                        defines.AOSTOREINMETALMAPRED = this._useAmbientOcclusionFromMetallicTextureRed;\r\n                        defines.REFLECTIVITY_GAMMA = false;\r\n                    } else if (this._reflectivityTexture) {\r\n                        PrepareDefinesForMergedUV(this._reflectivityTexture, defines, \"REFLECTIVITY\");\r\n                        defines.MICROSURFACEFROMREFLECTIVITYMAP = this._useMicroSurfaceFromReflectivityMapAlpha;\r\n                        defines.MICROSURFACEAUTOMATIC = this._useAutoMicroSurfaceFromReflectivityMap;\r\n                        defines.REFLECTIVITY_GAMMA = this._reflectivityTexture.gammaSpace;\r\n                    } else {\r\n                        defines.REFLECTIVITY = false;\r\n                    }\r\n\r\n                    if (this._metallicReflectanceTexture || this._reflectanceTexture) {\r\n                        defines.METALLIC_REFLECTANCE_USE_ALPHA_ONLY = this._useOnlyMetallicFromMetallicReflectanceTexture;\r\n                        if (this._metallicReflectanceTexture) {\r\n                            PrepareDefinesForMergedUV(this._metallicReflectanceTexture, defines, \"METALLIC_REFLECTANCE\");\r\n                            defines.METALLIC_REFLECTANCE_GAMMA = this._metallicReflectanceTexture.gammaSpace;\r\n                        } else {\r\n                            defines.METALLIC_REFLECTANCE = false;\r\n                        }\r\n                        if (\r\n                            this._reflectanceTexture &&\r\n                            (!this._metallicReflectanceTexture || (this._metallicReflectanceTexture && this._useOnlyMetallicFromMetallicReflectanceTexture))\r\n                        ) {\r\n                            PrepareDefinesForMergedUV(this._reflectanceTexture, defines, \"REFLECTANCE\");\r\n                            defines.REFLECTANCE_GAMMA = this._reflectanceTexture.gammaSpace;\r\n                        } else {\r\n                            defines.REFLECTANCE = false;\r\n                        }\r\n                    } else {\r\n                        defines.METALLIC_REFLECTANCE = false;\r\n                        defines.REFLECTANCE = false;\r\n                    }\r\n\r\n                    if (this._microSurfaceTexture) {\r\n                        PrepareDefinesForMergedUV(this._microSurfaceTexture, defines, \"MICROSURFACEMAP\");\r\n                    } else {\r\n                        defines.MICROSURFACEMAP = false;\r\n                    }\r\n                } else {\r\n                    defines.REFLECTIVITY = false;\r\n                    defines.MICROSURFACEMAP = false;\r\n                }\r\n\r\n                if (engine.getCaps().standardDerivatives && this._bumpTexture && MaterialFlags.BumpTextureEnabled && !this._disableBumpMap) {\r\n                    PrepareDefinesForMergedUV(this._bumpTexture, defines, \"BUMP\");\r\n\r\n                    if (this._useParallax && this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                        defines.PARALLAX = true;\r\n                        defines.PARALLAX_RHS = scene.useRightHandedSystem;\r\n                        defines.PARALLAXOCCLUSION = !!this._useParallaxOcclusion;\r\n                    } else {\r\n                        defines.PARALLAX = false;\r\n                    }\r\n\r\n                    defines.OBJECTSPACE_NORMALMAP = this._useObjectSpaceNormalMap;\r\n                } else {\r\n                    defines.BUMP = false;\r\n                    defines.PARALLAX = false;\r\n                    defines.PARALLAX_RHS = false;\r\n                    defines.PARALLAXOCCLUSION = false;\r\n                    defines.OBJECTSPACE_NORMALMAP = false;\r\n                }\r\n\r\n                if (this._environmentBRDFTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    defines.ENVIRONMENTBRDF = true;\r\n                    defines.ENVIRONMENTBRDF_RGBD = this._environmentBRDFTexture.isRGBD;\r\n                } else {\r\n                    defines.ENVIRONMENTBRDF = false;\r\n                    defines.ENVIRONMENTBRDF_RGBD = false;\r\n                }\r\n\r\n                if (this._shouldUseAlphaFromAlbedoTexture()) {\r\n                    defines.ALPHAFROMALBEDO = true;\r\n                } else {\r\n                    defines.ALPHAFROMALBEDO = false;\r\n                }\r\n            }\r\n\r\n            defines.SPECULAROVERALPHA = this._useSpecularOverAlpha;\r\n\r\n            if (this._lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_STANDARD) {\r\n                defines.USEPHYSICALLIGHTFALLOFF = false;\r\n                defines.USEGLTFLIGHTFALLOFF = false;\r\n            } else if (this._lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_GLTF) {\r\n                defines.USEPHYSICALLIGHTFALLOFF = false;\r\n                defines.USEGLTFLIGHTFALLOFF = true;\r\n            } else {\r\n                defines.USEPHYSICALLIGHTFALLOFF = true;\r\n                defines.USEGLTFLIGHTFALLOFF = false;\r\n            }\r\n\r\n            defines.RADIANCEOVERALPHA = this._useRadianceOverAlpha;\r\n\r\n            if (!this.backFaceCulling && this._twoSidedLighting) {\r\n                defines.TWOSIDEDLIGHTING = true;\r\n            } else {\r\n                defines.TWOSIDEDLIGHTING = false;\r\n            }\r\n\r\n            // We need it to not invert normals in two sided lighting mode (based on the winding of the face)\r\n            defines.MIRRORED = !!scene._mirroredCameraPosition;\r\n\r\n            defines.SPECULARAA = engine.getCaps().standardDerivatives && this._enableSpecularAntiAliasing;\r\n        }\r\n\r\n        if (defines._areTexturesDirty || defines._areMiscDirty) {\r\n            defines.ALPHATESTVALUE = `${this._alphaCutOff}${this._alphaCutOff % 1 === 0 ? \".\" : \"\"}`;\r\n            defines.PREMULTIPLYALPHA = this.alphaMode === Constants.ALPHA_PREMULTIPLIED || this.alphaMode === Constants.ALPHA_PREMULTIPLIED_PORTERDUFF;\r\n            defines.ALPHABLEND = this.needAlphaBlendingForMesh(mesh);\r\n            defines.ALPHAFRESNEL = this._useAlphaFresnel || this._useLinearAlphaFresnel;\r\n            defines.LINEARALPHAFRESNEL = this._useLinearAlphaFresnel;\r\n        }\r\n\r\n        if (defines._areImageProcessingDirty && this._imageProcessingConfiguration) {\r\n            this._imageProcessingConfiguration.prepareDefines(defines);\r\n        }\r\n\r\n        defines.FORCENORMALFORWARD = this._forceNormalForward;\r\n\r\n        defines.RADIANCEOCCLUSION = this._useRadianceOcclusion;\r\n\r\n        defines.HORIZONOCCLUSION = this._useHorizonOcclusion;\r\n\r\n        // Misc.\r\n        if (defines._areMiscDirty) {\r\n            PrepareDefinesForMisc(\r\n                mesh,\r\n                scene,\r\n                this._useLogarithmicDepth,\r\n                this.pointsCloud,\r\n                this.fogEnabled,\r\n                this.needAlphaTestingForMesh(mesh),\r\n                defines,\r\n                this._applyDecalMapAfterDetailMap\r\n            );\r\n            defines.UNLIT = this._unlit || ((this.pointsCloud || this.wireframe) && !mesh.isVerticesDataPresent(VertexBuffer.NormalKind));\r\n            defines.DEBUGMODE = this._debugMode;\r\n        }\r\n\r\n        // Values that need to be evaluated on every frame\r\n        PrepareDefinesForFrameBoundValues(scene, engine, this, defines, useInstances ? true : false, useClipPlane, useThinInstances);\r\n\r\n        // External config\r\n        this._eventInfo.defines = defines;\r\n        this._eventInfo.mesh = mesh;\r\n        this._callbackPluginEventPrepareDefinesBeforeAttributes(this._eventInfo);\r\n\r\n        // Attribs\r\n        PrepareDefinesForAttributes(mesh, defines, true, true, true, this._transparencyMode !== PBRBaseMaterial.PBRMATERIAL_OPAQUE);\r\n\r\n        // External config\r\n        this._callbackPluginEventPrepareDefines(this._eventInfo);\r\n    }\r\n\r\n    /**\r\n     * Force shader compilation\r\n     * @param mesh - Define the mesh we want to force the compilation for\r\n     * @param onCompiled - Define a callback triggered when the compilation completes\r\n     * @param options - Define the options used to create the compilation\r\n     */\r\n    public override forceCompilation(mesh: AbstractMesh, onCompiled?: (material: Material) => void, options?: Partial<IMaterialCompilationOptions>): void {\r\n        const localOptions = {\r\n            clipPlane: false,\r\n            useInstances: false,\r\n            ...options,\r\n        };\r\n\r\n        if (!this._uniformBufferLayoutBuilt) {\r\n            this.buildUniformLayout();\r\n        }\r\n\r\n        this._callbackPluginEventGeneric(MaterialPluginEvent.GetDefineNames, this._eventInfo);\r\n        const checkReady = () => {\r\n            if (this._breakShaderLoadedCheck) {\r\n                return;\r\n            }\r\n            const defines = new PBRMaterialDefines(this._eventInfo.defineNames);\r\n            const effect = this._prepareEffect(mesh, defines, undefined, undefined, localOptions.useInstances, localOptions.clipPlane, mesh.hasThinInstances)!;\r\n            if (this._onEffectCreatedObservable) {\r\n                onCreatedEffectParameters.effect = effect;\r\n                onCreatedEffectParameters.subMesh = null;\r\n                this._onEffectCreatedObservable.notifyObservers(onCreatedEffectParameters);\r\n            }\r\n            if (effect.isReady()) {\r\n                if (onCompiled) {\r\n                    onCompiled(this);\r\n                }\r\n            } else {\r\n                effect.onCompileObservable.add(() => {\r\n                    if (onCompiled) {\r\n                        onCompiled(this);\r\n                    }\r\n                });\r\n            }\r\n        };\r\n        checkReady();\r\n    }\r\n\r\n    /**\r\n     * Initializes the uniform buffer layout for the shader.\r\n     */\r\n    public override buildUniformLayout(): void {\r\n        // Order is important !\r\n        const ubo = this._uniformBuffer;\r\n        ubo.addUniform(\"vAlbedoInfos\", 2);\r\n        ubo.addUniform(\"vBaseWeightInfos\", 2);\r\n        ubo.addUniform(\"vBaseDiffuseRoughnessInfos\", 2);\r\n        ubo.addUniform(\"vAmbientInfos\", 4);\r\n        ubo.addUniform(\"vOpacityInfos\", 2);\r\n        ubo.addUniform(\"vEmissiveInfos\", 2);\r\n        ubo.addUniform(\"vLightmapInfos\", 2);\r\n        ubo.addUniform(\"vReflectivityInfos\", 3);\r\n        ubo.addUniform(\"vMicroSurfaceSamplerInfos\", 2);\r\n        ubo.addUniform(\"vReflectionInfos\", 2);\r\n        ubo.addUniform(\"vReflectionFilteringInfo\", 2);\r\n        ubo.addUniform(\"vReflectionPosition\", 3);\r\n        ubo.addUniform(\"vReflectionSize\", 3);\r\n        ubo.addUniform(\"vBumpInfos\", 3);\r\n        ubo.addUniform(\"albedoMatrix\", 16);\r\n        ubo.addUniform(\"baseWeightMatrix\", 16);\r\n        ubo.addUniform(\"baseDiffuseRoughnessMatrix\", 16);\r\n        ubo.addUniform(\"ambientMatrix\", 16);\r\n        ubo.addUniform(\"opacityMatrix\", 16);\r\n        ubo.addUniform(\"emissiveMatrix\", 16);\r\n        ubo.addUniform(\"lightmapMatrix\", 16);\r\n        ubo.addUniform(\"reflectivityMatrix\", 16);\r\n        ubo.addUniform(\"microSurfaceSamplerMatrix\", 16);\r\n        ubo.addUniform(\"bumpMatrix\", 16);\r\n        ubo.addUniform(\"vTangentSpaceParams\", 2);\r\n        ubo.addUniform(\"reflectionMatrix\", 16);\r\n\r\n        ubo.addUniform(\"vReflectionColor\", 3);\r\n        ubo.addUniform(\"vAlbedoColor\", 4);\r\n        ubo.addUniform(\"baseWeight\", 1);\r\n        ubo.addUniform(\"baseDiffuseRoughness\", 1);\r\n        ubo.addUniform(\"vLightingIntensity\", 4);\r\n\r\n        ubo.addUniform(\"vReflectionMicrosurfaceInfos\", 3);\r\n        ubo.addUniform(\"vReflectionDominantDirection\", 3);\r\n        ubo.addUniform(\"pointSize\", 1);\r\n        ubo.addUniform(\"vReflectivityColor\", 4);\r\n        ubo.addUniform(\"vEmissiveColor\", 3);\r\n        ubo.addUniform(\"vAmbientColor\", 3);\r\n\r\n        ubo.addUniform(\"vDebugMode\", 2);\r\n\r\n        ubo.addUniform(\"vMetallicReflectanceFactors\", 4);\r\n        ubo.addUniform(\"vMetallicReflectanceInfos\", 2);\r\n        ubo.addUniform(\"metallicReflectanceMatrix\", 16);\r\n        ubo.addUniform(\"vReflectanceInfos\", 2);\r\n        ubo.addUniform(\"reflectanceMatrix\", 16);\r\n\r\n        ubo.addUniform(\"vSphericalL00\", 3);\r\n        ubo.addUniform(\"vSphericalL1_1\", 3);\r\n        ubo.addUniform(\"vSphericalL10\", 3);\r\n        ubo.addUniform(\"vSphericalL11\", 3);\r\n        ubo.addUniform(\"vSphericalL2_2\", 3);\r\n        ubo.addUniform(\"vSphericalL2_1\", 3);\r\n        ubo.addUniform(\"vSphericalL20\", 3);\r\n        ubo.addUniform(\"vSphericalL21\", 3);\r\n        ubo.addUniform(\"vSphericalL22\", 3);\r\n\r\n        ubo.addUniform(\"vSphericalX\", 3);\r\n        ubo.addUniform(\"vSphericalY\", 3);\r\n        ubo.addUniform(\"vSphericalZ\", 3);\r\n        ubo.addUniform(\"vSphericalXX_ZZ\", 3);\r\n        ubo.addUniform(\"vSphericalYY_ZZ\", 3);\r\n        ubo.addUniform(\"vSphericalZZ\", 3);\r\n        ubo.addUniform(\"vSphericalXY\", 3);\r\n        ubo.addUniform(\"vSphericalYZ\", 3);\r\n        ubo.addUniform(\"vSphericalZX\", 3);\r\n\r\n        ubo.addUniform(\"cameraInfo\", 4);\r\n\r\n        super.buildUniformLayout();\r\n    }\r\n\r\n    /**\r\n     * Binds the submesh data.\r\n     * @param world - The world matrix.\r\n     * @param mesh - The BJS mesh.\r\n     * @param subMesh - A submesh of the BJS mesh.\r\n     */\r\n    public override bindForSubMesh(world: Matrix, mesh: Mesh, subMesh: SubMesh): void {\r\n        const scene = this.getScene();\r\n\r\n        const defines = <PBRMaterialDefines>subMesh.materialDefines;\r\n        if (!defines) {\r\n            return;\r\n        }\r\n\r\n        const effect = subMesh.effect;\r\n\r\n        if (!effect) {\r\n            return;\r\n        }\r\n\r\n        this._activeEffect = effect;\r\n\r\n        // Matrices Mesh.\r\n        mesh.getMeshUniformBuffer().bindToEffect(effect, \"Mesh\");\r\n        mesh.transferToEffect(world);\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        // Binding unconditionally\r\n        this._uniformBuffer.bindToEffect(effect, \"Material\");\r\n\r\n        this.prePassConfiguration.bindForSubMesh(this._activeEffect, scene, mesh, world, this.isFrozen);\r\n\r\n        MaterialHelperGeometryRendering.Bind(engine.currentRenderPassId, this._activeEffect, mesh, world, this);\r\n\r\n        const camera = scene.activeCamera;\r\n        if (camera) {\r\n            this._uniformBuffer.updateFloat4(\"cameraInfo\", camera.minZ, camera.maxZ, 0, 0);\r\n        } else {\r\n            this._uniformBuffer.updateFloat4(\"cameraInfo\", 0, 0, 0, 0);\r\n        }\r\n\r\n        this._eventInfo.subMesh = subMesh;\r\n        this._callbackPluginEventHardBindForSubMesh(this._eventInfo);\r\n\r\n        // Normal Matrix\r\n        if (defines.OBJECTSPACE_NORMALMAP) {\r\n            world.toNormalMatrix(this._normalMatrix);\r\n            this.bindOnlyNormalMatrix(this._normalMatrix);\r\n        }\r\n\r\n        const mustRebind = this._mustRebind(scene, effect, subMesh, mesh.visibility);\r\n\r\n        // Bones\r\n        BindBonesParameters(mesh, this._activeEffect, this.prePassConfiguration);\r\n\r\n        let reflectionTexture: Nullable<BaseTexture> = null;\r\n        const ubo = this._uniformBuffer;\r\n        if (mustRebind) {\r\n            this.bindViewProjection(effect);\r\n            reflectionTexture = this._getReflectionTexture();\r\n\r\n            if (!ubo.useUbo || !this.isFrozen || !ubo.isSync || subMesh._drawWrapper._forceRebindOnNextCall) {\r\n                // Texture uniforms\r\n                if (scene.texturesEnabled) {\r\n                    if (this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                        ubo.updateFloat2(\"vAlbedoInfos\", this._albedoTexture.coordinatesIndex, this._albedoTexture.level);\r\n                        BindTextureMatrix(this._albedoTexture, ubo, \"albedo\");\r\n                    }\r\n\r\n                    if (this._baseWeightTexture && MaterialFlags.BaseWeightTextureEnabled) {\r\n                        ubo.updateFloat2(\"vBaseWeightInfos\", this._baseWeightTexture.coordinatesIndex, this._baseWeightTexture.level);\r\n                        BindTextureMatrix(this._baseWeightTexture, ubo, \"baseWeight\");\r\n                    }\r\n\r\n                    if (this._baseDiffuseRoughnessTexture && MaterialFlags.BaseDiffuseRoughnessTextureEnabled) {\r\n                        ubo.updateFloat2(\"vBaseDiffuseRoughnessInfos\", this._baseDiffuseRoughnessTexture.coordinatesIndex, this._baseDiffuseRoughnessTexture.level);\r\n                        BindTextureMatrix(this._baseDiffuseRoughnessTexture, ubo, \"baseDiffuseRoughness\");\r\n                    }\r\n\r\n                    if (this._ambientTexture && MaterialFlags.AmbientTextureEnabled) {\r\n                        ubo.updateFloat4(\r\n                            \"vAmbientInfos\",\r\n                            this._ambientTexture.coordinatesIndex,\r\n                            this._ambientTexture.level,\r\n                            this._ambientTextureStrength,\r\n                            this._ambientTextureImpactOnAnalyticalLights\r\n                        );\r\n                        BindTextureMatrix(this._ambientTexture, ubo, \"ambient\");\r\n                    }\r\n\r\n                    if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                        ubo.updateFloat2(\"vOpacityInfos\", this._opacityTexture.coordinatesIndex, this._opacityTexture.level);\r\n                        BindTextureMatrix(this._opacityTexture, ubo, \"opacity\");\r\n                    }\r\n\r\n                    if (reflectionTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                        ubo.updateMatrix(\"reflectionMatrix\", reflectionTexture.getReflectionTextureMatrix());\r\n                        ubo.updateFloat2(\"vReflectionInfos\", reflectionTexture.level * scene.iblIntensity, 0);\r\n\r\n                        if ((<any>reflectionTexture).boundingBoxSize) {\r\n                            const cubeTexture = <CubeTexture>reflectionTexture;\r\n\r\n                            ubo.updateVector3(\"vReflectionPosition\", cubeTexture.boundingBoxPosition);\r\n                            ubo.updateVector3(\"vReflectionSize\", cubeTexture.boundingBoxSize);\r\n                        }\r\n\r\n                        if (this.realTimeFiltering) {\r\n                            const width = reflectionTexture.getSize().width;\r\n                            ubo.updateFloat2(\"vReflectionFilteringInfo\", width, Math.log2(width));\r\n                        }\r\n\r\n                        if (!defines.USEIRRADIANCEMAP) {\r\n                            const polynomials = reflectionTexture.sphericalPolynomial;\r\n                            if (defines.USESPHERICALFROMREFLECTIONMAP && polynomials) {\r\n                                if (defines.SPHERICAL_HARMONICS) {\r\n                                    const preScaledHarmonics = polynomials.preScaledHarmonics;\r\n                                    ubo.updateVector3(\"vSphericalL00\", preScaledHarmonics.l00);\r\n                                    ubo.updateVector3(\"vSphericalL1_1\", preScaledHarmonics.l1_1);\r\n                                    ubo.updateVector3(\"vSphericalL10\", preScaledHarmonics.l10);\r\n                                    ubo.updateVector3(\"vSphericalL11\", preScaledHarmonics.l11);\r\n                                    ubo.updateVector3(\"vSphericalL2_2\", preScaledHarmonics.l2_2);\r\n                                    ubo.updateVector3(\"vSphericalL2_1\", preScaledHarmonics.l2_1);\r\n                                    ubo.updateVector3(\"vSphericalL20\", preScaledHarmonics.l20);\r\n                                    ubo.updateVector3(\"vSphericalL21\", preScaledHarmonics.l21);\r\n                                    ubo.updateVector3(\"vSphericalL22\", preScaledHarmonics.l22);\r\n                                } else {\r\n                                    ubo.updateFloat3(\"vSphericalX\", polynomials.x.x, polynomials.x.y, polynomials.x.z);\r\n                                    ubo.updateFloat3(\"vSphericalY\", polynomials.y.x, polynomials.y.y, polynomials.y.z);\r\n                                    ubo.updateFloat3(\"vSphericalZ\", polynomials.z.x, polynomials.z.y, polynomials.z.z);\r\n                                    ubo.updateFloat3(\r\n                                        \"vSphericalXX_ZZ\",\r\n                                        polynomials.xx.x - polynomials.zz.x,\r\n                                        polynomials.xx.y - polynomials.zz.y,\r\n                                        polynomials.xx.z - polynomials.zz.z\r\n                                    );\r\n                                    ubo.updateFloat3(\r\n                                        \"vSphericalYY_ZZ\",\r\n                                        polynomials.yy.x - polynomials.zz.x,\r\n                                        polynomials.yy.y - polynomials.zz.y,\r\n                                        polynomials.yy.z - polynomials.zz.z\r\n                                    );\r\n                                    ubo.updateFloat3(\"vSphericalZZ\", polynomials.zz.x, polynomials.zz.y, polynomials.zz.z);\r\n                                    ubo.updateFloat3(\"vSphericalXY\", polynomials.xy.x, polynomials.xy.y, polynomials.xy.z);\r\n                                    ubo.updateFloat3(\"vSphericalYZ\", polynomials.yz.x, polynomials.yz.y, polynomials.yz.z);\r\n                                    ubo.updateFloat3(\"vSphericalZX\", polynomials.zx.x, polynomials.zx.y, polynomials.zx.z);\r\n                                }\r\n                            }\r\n                        } else {\r\n                            // If we're using an irradiance map with a dominant direction assigned, set it.\r\n                            if (defines.USEIRRADIANCEMAP && defines.USE_IRRADIANCE_DOMINANT_DIRECTION) {\r\n                                ubo.updateVector3(\"vReflectionDominantDirection\", reflectionTexture.irradianceTexture!._dominantDirection!);\r\n                            }\r\n                        }\r\n\r\n                        ubo.updateFloat3(\r\n                            \"vReflectionMicrosurfaceInfos\",\r\n                            reflectionTexture.getSize().width,\r\n                            reflectionTexture.lodGenerationScale,\r\n                            reflectionTexture.lodGenerationOffset\r\n                        );\r\n                    }\r\n\r\n                    if (this._emissiveTexture && MaterialFlags.EmissiveTextureEnabled) {\r\n                        ubo.updateFloat2(\"vEmissiveInfos\", this._emissiveTexture.coordinatesIndex, this._emissiveTexture.level);\r\n                        BindTextureMatrix(this._emissiveTexture, ubo, \"emissive\");\r\n                    }\r\n\r\n                    if (this._lightmapTexture && MaterialFlags.LightmapTextureEnabled) {\r\n                        ubo.updateFloat2(\"vLightmapInfos\", this._lightmapTexture.coordinatesIndex, this._lightmapTexture.level);\r\n                        BindTextureMatrix(this._lightmapTexture, ubo, \"lightmap\");\r\n                    }\r\n\r\n                    if (MaterialFlags.SpecularTextureEnabled) {\r\n                        if (this._metallicTexture) {\r\n                            ubo.updateFloat3(\"vReflectivityInfos\", this._metallicTexture.coordinatesIndex, this._metallicTexture.level, this._ambientTextureStrength);\r\n                            BindTextureMatrix(this._metallicTexture, ubo, \"reflectivity\");\r\n                        } else if (this._reflectivityTexture) {\r\n                            ubo.updateFloat3(\"vReflectivityInfos\", this._reflectivityTexture.coordinatesIndex, this._reflectivityTexture.level, 1.0);\r\n                            BindTextureMatrix(this._reflectivityTexture, ubo, \"reflectivity\");\r\n                        }\r\n\r\n                        if (this._metallicReflectanceTexture) {\r\n                            ubo.updateFloat2(\"vMetallicReflectanceInfos\", this._metallicReflectanceTexture.coordinatesIndex, this._metallicReflectanceTexture.level);\r\n                            BindTextureMatrix(this._metallicReflectanceTexture, ubo, \"metallicReflectance\");\r\n                        }\r\n\r\n                        if (this._reflectanceTexture && defines.REFLECTANCE) {\r\n                            ubo.updateFloat2(\"vReflectanceInfos\", this._reflectanceTexture.coordinatesIndex, this._reflectanceTexture.level);\r\n                            BindTextureMatrix(this._reflectanceTexture, ubo, \"reflectance\");\r\n                        }\r\n\r\n                        if (this._microSurfaceTexture) {\r\n                            ubo.updateFloat2(\"vMicroSurfaceSamplerInfos\", this._microSurfaceTexture.coordinatesIndex, this._microSurfaceTexture.level);\r\n                            BindTextureMatrix(this._microSurfaceTexture, ubo, \"microSurfaceSampler\");\r\n                        }\r\n                    }\r\n\r\n                    if (this._bumpTexture && engine.getCaps().standardDerivatives && MaterialFlags.BumpTextureEnabled && !this._disableBumpMap) {\r\n                        ubo.updateFloat3(\"vBumpInfos\", this._bumpTexture.coordinatesIndex, this._bumpTexture.level, this._parallaxScaleBias);\r\n                        BindTextureMatrix(this._bumpTexture, ubo, \"bump\");\r\n\r\n                        if (scene._mirroredCameraPosition) {\r\n                            ubo.updateFloat2(\"vTangentSpaceParams\", this._invertNormalMapX ? 1.0 : -1.0, this._invertNormalMapY ? 1.0 : -1.0);\r\n                        } else {\r\n                            ubo.updateFloat2(\"vTangentSpaceParams\", this._invertNormalMapX ? -1.0 : 1.0, this._invertNormalMapY ? -1.0 : 1.0);\r\n                        }\r\n                    }\r\n                }\r\n\r\n                // Point size\r\n                if (this.pointsCloud) {\r\n                    ubo.updateFloat(\"pointSize\", this.pointSize);\r\n                }\r\n\r\n                // Colors\r\n                if (defines.METALLICWORKFLOW) {\r\n                    TmpColors.Color4[0].r = this._metallic === undefined || this._metallic === null ? 1 : this._metallic;\r\n                    TmpColors.Color4[0].g = this._roughness === undefined || this._roughness === null ? 1 : this._roughness;\r\n                    const ior = this.subSurface?._indexOfRefraction ?? 1.5;\r\n                    const outsideIOR = 1; // consider air as clear coat and other layers would remap in the shader.\r\n                    TmpColors.Color4[0].b = ior;\r\n                    // We are here deriving our default reflectance from a common value for none metallic surface.\r\n                    // Based of the schlick fresnel approximation model\r\n                    // for dielectrics.\r\n                    const f0 = Math.pow((ior - outsideIOR) / (ior + outsideIOR), 2);\r\n                    TmpColors.Color4[0].a = f0;\r\n                    ubo.updateDirectColor4(\"vReflectivityColor\", TmpColors.Color4[0]);\r\n                    ubo.updateColor4(\"vMetallicReflectanceFactors\", this._metallicReflectanceColor, this._metallicF0Factor);\r\n                } else {\r\n                    ubo.updateColor4(\"vReflectivityColor\", this._reflectivityColor, this._microSurface);\r\n                }\r\n\r\n                ubo.updateColor3(\"vEmissiveColor\", MaterialFlags.EmissiveTextureEnabled ? this._emissiveColor : Color3.BlackReadOnly);\r\n                ubo.updateColor3(\"vReflectionColor\", this._reflectionColor);\r\n                if (!defines.SS_REFRACTION && this.subSurface?._linkRefractionWithTransparency) {\r\n                    ubo.updateColor4(\"vAlbedoColor\", this._albedoColor, 1);\r\n                } else {\r\n                    ubo.updateColor4(\"vAlbedoColor\", this._albedoColor, this.alpha);\r\n                }\r\n\r\n                ubo.updateFloat(\"baseWeight\", this._baseWeight);\r\n                ubo.updateFloat(\"baseDiffuseRoughness\", this._baseDiffuseRoughness || 0.0);\r\n\r\n                // Misc\r\n                this._lightingInfos.x = this._directIntensity;\r\n                this._lightingInfos.y = this._emissiveIntensity;\r\n                this._lightingInfos.z = this._environmentIntensity * scene.environmentIntensity;\r\n                this._lightingInfos.w = this._specularIntensity;\r\n\r\n                ubo.updateVector4(\"vLightingIntensity\", this._lightingInfos);\r\n\r\n                // Colors\r\n                scene.ambientColor.multiplyToRef(this._ambientColor, this._globalAmbientColor);\r\n\r\n                ubo.updateColor3(\"vAmbientColor\", this._globalAmbientColor);\r\n\r\n                ubo.updateFloat2(\"vDebugMode\", this.debugLimit, this.debugFactor);\r\n            }\r\n\r\n            // Textures\r\n            if (scene.texturesEnabled) {\r\n                if (this._albedoTexture && MaterialFlags.DiffuseTextureEnabled) {\r\n                    ubo.setTexture(\"albedoSampler\", this._albedoTexture);\r\n                }\r\n\r\n                if (this._baseWeightTexture && MaterialFlags.BaseWeightTextureEnabled) {\r\n                    ubo.setTexture(\"baseWeightSampler\", this._baseWeightTexture);\r\n                }\r\n\r\n                if (this._baseDiffuseRoughnessTexture && MaterialFlags.BaseDiffuseRoughnessTextureEnabled) {\r\n                    ubo.setTexture(\"baseDiffuseRoughnessSampler\", this._baseDiffuseRoughnessTexture);\r\n                }\r\n\r\n                if (this._ambientTexture && MaterialFlags.AmbientTextureEnabled) {\r\n                    ubo.setTexture(\"ambientSampler\", this._ambientTexture);\r\n                }\r\n\r\n                if (this._opacityTexture && MaterialFlags.OpacityTextureEnabled) {\r\n                    ubo.setTexture(\"opacitySampler\", this._opacityTexture);\r\n                }\r\n\r\n                if (reflectionTexture && MaterialFlags.ReflectionTextureEnabled) {\r\n                    if (defines.LODBASEDMICROSFURACE) {\r\n                        ubo.setTexture(\"reflectionSampler\", reflectionTexture);\r\n                    } else {\r\n                        ubo.setTexture(\"reflectionSampler\", reflectionTexture._lodTextureMid || reflectionTexture);\r\n                        ubo.setTexture(\"reflectionSamplerLow\", reflectionTexture._lodTextureLow || reflectionTexture);\r\n                        ubo.setTexture(\"reflectionSamplerHigh\", reflectionTexture._lodTextureHigh || reflectionTexture);\r\n                    }\r\n\r\n                    if (defines.USEIRRADIANCEMAP) {\r\n                        ubo.setTexture(\"irradianceSampler\", reflectionTexture.irradianceTexture);\r\n                    }\r\n\r\n                    //if realtime filtering and using CDF maps, set them.\r\n                    const cdfGenerator = this.getScene().iblCdfGenerator;\r\n                    if (this.realTimeFiltering && cdfGenerator) {\r\n                        ubo.setTexture(\"icdfSampler\", cdfGenerator.getIcdfTexture());\r\n                    }\r\n                }\r\n\r\n                if (defines.ENVIRONMENTBRDF) {\r\n                    ubo.setTexture(\"environmentBrdfSampler\", this._environmentBRDFTexture);\r\n                }\r\n\r\n                if (this._emissiveTexture && MaterialFlags.EmissiveTextureEnabled) {\r\n                    ubo.setTexture(\"emissiveSampler\", this._emissiveTexture);\r\n                }\r\n\r\n                if (this._lightmapTexture && MaterialFlags.LightmapTextureEnabled) {\r\n                    ubo.setTexture(\"lightmapSampler\", this._lightmapTexture);\r\n                }\r\n\r\n                if (MaterialFlags.SpecularTextureEnabled) {\r\n                    if (this._metallicTexture) {\r\n                        ubo.setTexture(\"reflectivitySampler\", this._metallicTexture);\r\n                    } else if (this._reflectivityTexture) {\r\n                        ubo.setTexture(\"reflectivitySampler\", this._reflectivityTexture);\r\n                    }\r\n\r\n                    if (this._metallicReflectanceTexture) {\r\n                        ubo.setTexture(\"metallicReflectanceSampler\", this._metallicReflectanceTexture);\r\n                    }\r\n\r\n                    if (this._reflectanceTexture && defines.REFLECTANCE) {\r\n                        ubo.setTexture(\"reflectanceSampler\", this._reflectanceTexture);\r\n                    }\r\n\r\n                    if (this._microSurfaceTexture) {\r\n                        ubo.setTexture(\"microSurfaceSampler\", this._microSurfaceTexture);\r\n                    }\r\n                }\r\n\r\n                if (this._bumpTexture && engine.getCaps().standardDerivatives && MaterialFlags.BumpTextureEnabled && !this._disableBumpMap) {\r\n                    ubo.setTexture(\"bumpSampler\", this._bumpTexture);\r\n                }\r\n            }\r\n\r\n            // OIT with depth peeling\r\n            if (this.getScene().useOrderIndependentTransparency && this.needAlphaBlendingForMesh(mesh)) {\r\n                this.getScene().depthPeelingRenderer!.bind(effect);\r\n            }\r\n\r\n            this._eventInfo.subMesh = subMesh;\r\n            this._callbackPluginEventBindForSubMesh(this._eventInfo);\r\n\r\n            // Clip plane\r\n            BindClipPlane(this._activeEffect, this, scene);\r\n\r\n            this.bindEyePosition(effect);\r\n        } else if (scene.getEngine()._features.needToAlwaysBindUniformBuffers) {\r\n            this._needToBindSceneUbo = true;\r\n        }\r\n\r\n        if (mustRebind || !this.isFrozen) {\r\n            // Lights\r\n            if (scene.lightsEnabled && !this._disableLighting) {\r\n                BindLights(scene, mesh, this._activeEffect, defines, this._maxSimultaneousLights);\r\n            }\r\n\r\n            // View\r\n            if (\r\n                (scene.fogEnabled && mesh.applyFog && scene.fogMode !== Scene.FOGMODE_NONE) ||\r\n                reflectionTexture ||\r\n                this.subSurface.refractionTexture ||\r\n                mesh.receiveShadows ||\r\n                defines.PREPASS\r\n            ) {\r\n                this.bindView(effect);\r\n            }\r\n\r\n            // Fog\r\n            BindFogParameters(scene, mesh, this._activeEffect, true);\r\n\r\n            // Morph targets\r\n            if (defines.NUM_MORPH_INFLUENCERS) {\r\n                BindMorphTargetParameters(mesh, this._activeEffect);\r\n            }\r\n\r\n            if (defines.BAKED_VERTEX_ANIMATION_TEXTURE) {\r\n                mesh.bakedVertexAnimationManager?.bind(effect, defines.INSTANCES);\r\n            }\r\n\r\n            // image processing\r\n            this._imageProcessingConfiguration.bind(this._activeEffect);\r\n\r\n            // Log. depth\r\n            BindLogDepth(defines, this._activeEffect, scene);\r\n        }\r\n\r\n        this._afterBind(mesh, this._activeEffect, subMesh);\r\n\r\n        ubo.update();\r\n    }\r\n\r\n    /**\r\n     * Returns the animatable textures.\r\n     * If material have animatable metallic texture, then reflectivity texture will not be returned, even if it has animations.\r\n     * @returns - Array of animatable textures.\r\n     */\r\n    public override getAnimatables(): IAnimatable[] {\r\n        const results = super.getAnimatables();\r\n\r\n        if (this._albedoTexture && this._albedoTexture.animations && this._albedoTexture.animations.length > 0) {\r\n            results.push(this._albedoTexture);\r\n        }\r\n\r\n        if (this._baseWeightTexture && this._baseWeightTexture.animations && this._baseWeightTexture.animations.length > 0) {\r\n            results.push(this._baseWeightTexture);\r\n        }\r\n\r\n        if (this._baseDiffuseRoughnessTexture && this._baseDiffuseRoughnessTexture.animations && this._baseDiffuseRoughnessTexture.animations.length > 0) {\r\n            results.push(this._baseDiffuseRoughnessTexture);\r\n        }\r\n\r\n        if (this._ambientTexture && this._ambientTexture.animations && this._ambientTexture.animations.length > 0) {\r\n            results.push(this._ambientTexture);\r\n        }\r\n\r\n        if (this._opacityTexture && this._opacityTexture.animations && this._opacityTexture.animations.length > 0) {\r\n            results.push(this._opacityTexture);\r\n        }\r\n\r\n        if (this._reflectionTexture && this._reflectionTexture.animations && this._reflectionTexture.animations.length > 0) {\r\n            results.push(this._reflectionTexture);\r\n        }\r\n\r\n        if (this._emissiveTexture && this._emissiveTexture.animations && this._emissiveTexture.animations.length > 0) {\r\n            results.push(this._emissiveTexture);\r\n        }\r\n\r\n        if (this._metallicTexture && this._metallicTexture.animations && this._metallicTexture.animations.length > 0) {\r\n            results.push(this._metallicTexture);\r\n        } else if (this._reflectivityTexture && this._reflectivityTexture.animations && this._reflectivityTexture.animations.length > 0) {\r\n            results.push(this._reflectivityTexture);\r\n        }\r\n\r\n        if (this._bumpTexture && this._bumpTexture.animations && this._bumpTexture.animations.length > 0) {\r\n            results.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._lightmapTexture && this._lightmapTexture.animations && this._lightmapTexture.animations.length > 0) {\r\n            results.push(this._lightmapTexture);\r\n        }\r\n\r\n        if (this._metallicReflectanceTexture && this._metallicReflectanceTexture.animations && this._metallicReflectanceTexture.animations.length > 0) {\r\n            results.push(this._metallicReflectanceTexture);\r\n        }\r\n\r\n        if (this._reflectanceTexture && this._reflectanceTexture.animations && this._reflectanceTexture.animations.length > 0) {\r\n            results.push(this._reflectanceTexture);\r\n        }\r\n\r\n        if (this._microSurfaceTexture && this._microSurfaceTexture.animations && this._microSurfaceTexture.animations.length > 0) {\r\n            results.push(this._microSurfaceTexture);\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Returns the texture used for reflections.\r\n     * @returns - Reflection texture if present.  Otherwise, returns the environment texture.\r\n     */\r\n    private _getReflectionTexture(): Nullable<BaseTexture> {\r\n        if (this._reflectionTexture) {\r\n            return this._reflectionTexture;\r\n        }\r\n\r\n        return this.getScene().environmentTexture;\r\n    }\r\n\r\n    /**\r\n     * Returns an array of the actively used textures.\r\n     * @returns - Array of BaseTextures\r\n     */\r\n    public override getActiveTextures(): BaseTexture[] {\r\n        const activeTextures = super.getActiveTextures();\r\n\r\n        if (this._albedoTexture) {\r\n            activeTextures.push(this._albedoTexture);\r\n        }\r\n\r\n        if (this._baseWeightTexture) {\r\n            activeTextures.push(this._baseWeightTexture);\r\n        }\r\n\r\n        if (this._baseDiffuseRoughnessTexture) {\r\n            activeTextures.push(this._baseDiffuseRoughnessTexture);\r\n        }\r\n\r\n        if (this._ambientTexture) {\r\n            activeTextures.push(this._ambientTexture);\r\n        }\r\n\r\n        if (this._opacityTexture) {\r\n            activeTextures.push(this._opacityTexture);\r\n        }\r\n\r\n        if (this._reflectionTexture) {\r\n            activeTextures.push(this._reflectionTexture);\r\n        }\r\n\r\n        if (this._emissiveTexture) {\r\n            activeTextures.push(this._emissiveTexture);\r\n        }\r\n\r\n        if (this._reflectivityTexture) {\r\n            activeTextures.push(this._reflectivityTexture);\r\n        }\r\n\r\n        if (this._metallicTexture) {\r\n            activeTextures.push(this._metallicTexture);\r\n        }\r\n\r\n        if (this._metallicReflectanceTexture) {\r\n            activeTextures.push(this._metallicReflectanceTexture);\r\n        }\r\n\r\n        if (this._reflectanceTexture) {\r\n            activeTextures.push(this._reflectanceTexture);\r\n        }\r\n\r\n        if (this._microSurfaceTexture) {\r\n            activeTextures.push(this._microSurfaceTexture);\r\n        }\r\n\r\n        if (this._bumpTexture) {\r\n            activeTextures.push(this._bumpTexture);\r\n        }\r\n\r\n        if (this._lightmapTexture) {\r\n            activeTextures.push(this._lightmapTexture);\r\n        }\r\n\r\n        return activeTextures;\r\n    }\r\n\r\n    /**\r\n     * Checks to see if a texture is used in the material.\r\n     * @param texture - Base texture to use.\r\n     * @returns - Boolean specifying if a texture is used in the material.\r\n     */\r\n    public override hasTexture(texture: BaseTexture): boolean {\r\n        if (super.hasTexture(texture)) {\r\n            return true;\r\n        }\r\n\r\n        if (this._albedoTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._baseWeightTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._baseDiffuseRoughnessTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._ambientTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._opacityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._reflectionTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._emissiveTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._reflectivityTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._metallicTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._metallicReflectanceTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._reflectanceTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._microSurfaceTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._bumpTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        if (this._lightmapTexture === texture) {\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Sets the required values to the prepass renderer.\r\n     * It can't be sets when subsurface scattering of this material is disabled.\r\n     * When scene have ability to enable subsurface prepass effect, it will enable.\r\n     * @returns - If prepass is enabled or not.\r\n     */\r\n    public override setPrePassRenderer(): boolean {\r\n        if (!this.subSurface?.isScatteringEnabled) {\r\n            return false;\r\n        }\r\n\r\n        const subSurfaceConfiguration = this.getScene().enableSubSurfaceForPrePass();\r\n        if (subSurfaceConfiguration) {\r\n            subSurfaceConfiguration.enabled = true;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Disposes the resources of the material.\r\n     * @param forceDisposeEffect - Forces the disposal of effects.\r\n     * @param forceDisposeTextures - Forces the disposal of all textures.\r\n     */\r\n    public override dispose(forceDisposeEffect?: boolean, forceDisposeTextures?: boolean): void {\r\n        this._breakShaderLoadedCheck = true;\r\n        if (forceDisposeTextures) {\r\n            if (this._environmentBRDFTexture && this.getScene().environmentBRDFTexture !== this._environmentBRDFTexture) {\r\n                this._environmentBRDFTexture.dispose();\r\n            }\r\n\r\n            this._albedoTexture?.dispose();\r\n            this._baseWeightTexture?.dispose();\r\n            this._baseDiffuseRoughnessTexture?.dispose();\r\n            this._ambientTexture?.dispose();\r\n            this._opacityTexture?.dispose();\r\n            this._reflectionTexture?.dispose();\r\n            this._emissiveTexture?.dispose();\r\n            this._metallicTexture?.dispose();\r\n            this._reflectivityTexture?.dispose();\r\n            this._bumpTexture?.dispose();\r\n            this._lightmapTexture?.dispose();\r\n            this._metallicReflectanceTexture?.dispose();\r\n            this._reflectanceTexture?.dispose();\r\n            this._microSurfaceTexture?.dispose();\r\n        }\r\n\r\n        this._renderTargets.dispose();\r\n\r\n        if (this._imageProcessingConfiguration && this._imageProcessingObserver) {\r\n            this._imageProcessingConfiguration.onUpdateParameters.remove(this._imageProcessingObserver);\r\n        }\r\n\r\n        super.dispose(forceDisposeEffect, forceDisposeTextures);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA,uDAAA,EAAyD,CACzD,OAAO,EAAE,uCAAuC,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAElG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAExE,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAEpC,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAIpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,oBAAoB,EAAE,MAAM,yBAAyB,CAAC;AAC/D,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AAG3D,OAAO,EAAE,4BAA4B,EAAE,MAAM,8CAA8C,CAAC;AAG5F,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAEpD,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,YAAY,EAAE,MAAM,8BAA8B,CAAC;AAG5D,OAAO,EAAE,OAAO,EAAE,MAAM,kCAAkC,CAAC;AAI3D,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAIjD,OAAO,iDAAiD,CAAC;AAEzD,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACxE,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAC5E,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAC5E,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,0BAA0B,EAAE,MAAM,8BAA8B,CAAC;AAC1E,OAAO,EAAE,sBAAsB,EAAE,MAAM,oCAAoC,CAAC;AAC5E,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AACjF,OAAO,EACH,mBAAmB,EACnB,iBAAiB,EACjB,UAAU,EACV,YAAY,EACZ,yBAAyB,EACzB,iBAAiB,EACjB,yBAAyB,EACzB,wCAAwC,EACxC,yBAAyB,EACzB,6BAA6B,EAC7B,gCAAgC,EAChC,2BAA2B,EAC3B,iCAAiC,EACjC,uBAAuB,EACvB,yBAAyB,EACzB,qBAAqB,EACrB,0BAA0B,EAC1B,oBAAoB,EACpB,wBAAwB,EACxB,8BAA8B,GACjC,MAAM,6BAA6B,CAAC;AAErC,OAAO,EAAE,+BAA+B,EAAE,MAAM,qCAAqC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtF,MAAM,yBAAyB,GAAG;IAAE,MAAM,EAAE,IAAyB;IAAE,OAAO,EAAE,IAAoC;AAAA,CAAE,CAAC;AAMjH,MAAO,kBAAmB,6KAAQ,kBAAe;IAoOnD;;;OAGG,CACH,YAAY,kBAAuE,CAAA;QAC/E,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAxOvB,IAAA,CAAA,GAAG,GAAG,IAAI,CAAC;QAEX,IAAA,CAAA,WAAW,GAAG,GAAG,CAAC;QAClB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QAEZ,IAAA,CAAA,MAAM,GAAG,KAAK,CAAC;QACf,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,cAAc,GAAG,CAAC,CAAC;QACnB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QAEpB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC;QACxB,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,8BAA8B,GAAG,CAAC,CAAC;QAEnC,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QAEvC,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QACpB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QACpB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEzB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;QACrB,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QAEtB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC;QACzB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QAErB,IAAA,CAAA,+BAA+B,GAAG,KAAK,CAAC;QACxC,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAC9B,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,uBAAuB,GAAG,CAAC,CAAC;QAE5B,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QACnC,IAAA,CAAA,4BAA4B,GAAG,CAAC,CAAC;QACjC,IAAA,CAAA,mCAAmC,GAAG,KAAK,CAAC;QAC5C,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC;QAExB,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAE7B,IAAA,CAAA,MAAM,GAAG,KAAK,CAAC;QACf,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,IAAI,GAAG,KAAK,CAAC;QACb,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACjB,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAC9B,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,aAAa,GAAG,IAAI,CAAC;QAErB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;QACrB,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QACtB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QAErB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QACjC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,mCAAmC,GAAG,KAAK,CAAC;QAC5C,IAAA,CAAA,2CAA2C,GAAG,KAAK,CAAC;QACpD,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,iCAAiC,GAAG,KAAK,CAAC;QAC1C,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QACjC,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAC1B,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEzB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAEvB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAChB,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QACtB,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC,CAAC;QACzB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,wBAAwB,GAAG,CAAC,CAAC,CAAC;QAC9B,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,yBAAyB,GAAG,CAAC,CAAC,CAAC;QAC/B,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QACtB,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC,CAAC;QACzB,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAClC,IAAA,CAAA,+BAA+B,GAAG,CAAC,CAAC,CAAC;QACrC,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,mCAAmC,GAAG,CAAC,CAAC,CAAC;QACzC,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAClC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,0BAA0B,GAAG,CAAC,CAAC,CAAC;QAChC,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,sBAAsB,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,4BAA4B,GAAG,CAAC,CAAC,CAAC;QAClC,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,sBAAsB,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,6BAA6B,GAAG,CAAC,CAAC,CAAC;QACnC,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,0BAA0B,GAAG,CAAC,CAAC,CAAC;QAChC,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QAEpB,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC;QACzB,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACjB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAE/B,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAE1B,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAC9B,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,+BAA+B,GAAG,KAAK,CAAC;QACxC,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QACtC,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QACvC,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAClC,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QACnC,IAAA,CAAA,4BAA4B,GAAG,KAAK,CAAC;QACrC,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC;QAC1B,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAE7B,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAClC,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAChB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,MAAM,GAAG,KAAK,CAAC;QACf,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QACnC,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,8BAA8B,GAAG,KAAK,CAAC;QACvC,IAAA,CAAA,qCAAqC,GAAG,KAAK,CAAC;QAE9C,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAChC,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,GAAG,GAAG,KAAK,CAAC;QACZ,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QACzB,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAC3B,IAAA,CAAA,kBAAkB,GAAG,IAAI,CAAC;QAE1B,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAEnB,IAAA,CAAA,KAAK,GAAG,KAAK,CAAC;QAEd,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QAQjB,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED;;OAEG,CACa,KAAK,GAAA;QACjB,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;CACJ;AAWK,MAAgB,eAAgB,0KAAQ,eAAY;IAkdtD;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IACD,IAAW,iBAAiB,CAAC,CAAU,EAAA;QACnC,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IAC1D,CAAC;IAGD;;OAEG,CACH,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IACD,IAAW,wBAAwB,CAAC,CAAS,EAAA;QACzC,IAAI,CAAC,yBAAyB,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG,CACH,IAAoB,cAAc,GAAA;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IA2BD;;;OAGG,CACO,mCAAmC,CAAC,aAAqD,EAAA;QAC/F,IAAI,aAAa,KAAK,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACvD,OAAO;QACX,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACtE,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAChG,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,aAAa,EAAE,CAAC;YACjB,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,4BAA4B,CAAC;QACtF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,6BAA6B,GAAG,aAAa,CAAC;QACvD,CAAC;QAED,qBAAqB;QACrB,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC3F,IAAI,CAAC,uCAAuC,EAAE,CAAC;YACnD,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAgGD;;;;;;OAMG,CACH,YAAY,IAAY,EAAE,KAAa,EAAE,SAAS,GAAG,KAAK,CAAA;QACtD,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,IAAI,eAAe,CAAC,SAAS,CAAC,CAAC;QAzlB1E;;;;WAIG,CACI,IAAA,CAAA,gBAAgB,GAAW,GAAG,CAAC;QAEtC;;;;WAIG,CACI,IAAA,CAAA,kBAAkB,GAAW,GAAG,CAAC;QAExC;;;;WAIG,CACI,IAAA,CAAA,qBAAqB,GAAW,GAAG,CAAC;QAE3C;;;;WAIG,CACI,IAAA,CAAA,kBAAkB,GAAW,GAAG,CAAC;QAExC;;WAEG,CACK,IAAA,CAAA,cAAc,GAAY,mKAAI,UAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAEnJ;;;WAGG,CACI,IAAA,CAAA,eAAe,GAAY,KAAK,CAAC;QAExC;;;WAGG,CACI,IAAA,CAAA,cAAc,GAA0B,IAAI,CAAC;QAEpD;;;WAGG,CACI,IAAA,CAAA,kBAAkB,GAA0B,IAAI,CAAC;QAExD;;;WAGG,CACI,IAAA,CAAA,4BAA4B,GAA0B,IAAI,CAAC;QAElE;;;WAGG,CACI,IAAA,CAAA,eAAe,GAA0B,IAAI,CAAC;QAErD;;;WAGG,CACI,IAAA,CAAA,uBAAuB,GAAW,GAAG,CAAC;QAE7C;;;;;WAKG,CACI,IAAA,CAAA,uCAAuC,GAAW,eAAe,CAAC,+BAA+B,CAAC;QAEzG;;;WAGG,CACI,IAAA,CAAA,eAAe,GAA0B,IAAI,CAAC;QAErD;;;WAGG,CACI,IAAA,CAAA,kBAAkB,GAA0B,IAAI,CAAC;QAExD;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAEtD;;;WAGG,CACI,IAAA,CAAA,oBAAoB,GAA0B,IAAI,CAAC;QAE1D;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAEtD;;;;WAIG,CACI,IAAA,CAAA,SAAS,GAAqB,IAAI,CAAC;QAE1C;;;;WAIG,CACI,IAAA,CAAA,UAAU,GAAqB,IAAI,CAAC;QAE3C;;;;;;;;;WASG,CACI,IAAA,CAAA,iBAAiB,GAAG,CAAC,CAAC;QAE7B;;;;;;;;;WASG,CACI,IAAA,CAAA,yBAAyB,GAAG,uKAAM,CAAC,KAAK,EAAE,CAAC;QAElD;;;;WAIG,CACI,IAAA,CAAA,8CAA8C,GAAG,KAAK,CAAC;QAE9D;;;;WAIG,CACI,IAAA,CAAA,2BAA2B,GAA0B,IAAI,CAAC;QAEjE;;;;;;WAMG,CACI,IAAA,CAAA,mBAAmB,GAA0B,IAAI,CAAC;QAEzD;;;;WAIG,CACI,IAAA,CAAA,oBAAoB,GAA0B,IAAI,CAAC;QAE1D;;;WAGG,CACI,IAAA,CAAA,YAAY,GAA0B,IAAI,CAAC;QAElD;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAA0B,IAAI,CAAC;QAEtD;;;WAGG,CACI,IAAA,CAAA,aAAa,GAAG,IAAI,uKAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C;;;WAGG,CACI,IAAA,CAAA,YAAY,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C;;;WAGG,CACI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAEvB;;;;WAIG,CACI,IAAA,CAAA,qBAAqB,GAAqB,IAAI,CAAC;QAEtD;;;WAGG,CACI,IAAA,CAAA,kBAAkB,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhD;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9C;;;WAGG,CACI,IAAA,CAAA,cAAc,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE5C;;;WAGG,CACI,IAAA,CAAA,aAAa,GAAG,GAAG,CAAC;QAE3B;;;WAGG,CACI,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAEvC;;;;WAIG,CACI,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAEnC;;;;WAIG,CACI,IAAA,CAAA,qBAAqB,GAAG,IAAI,CAAC;QAEpC;;;WAGG,CACI,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QAE1C;;;;WAIG,CACI,IAAA,CAAA,qBAAqB,GAAG,IAAI,CAAC;QAEpC;;;WAGG,CACI,IAAA,CAAA,wCAAwC,GAAG,KAAK,CAAC;QAExD;;;WAGG,CACI,IAAA,CAAA,qCAAqC,GAAG,IAAI,CAAC;QAEpD;;;WAGG,CACI,IAAA,CAAA,qCAAqC,GAAG,KAAK,CAAC;QAErD;;;WAGG,CACI,IAAA,CAAA,qCAAqC,GAAG,KAAK,CAAC;QAErD;;;WAGG,CACI,IAAA,CAAA,0CAA0C,GAAG,KAAK,CAAC;QAE1D;;;WAGG,CACI,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAEtC;;;;WAIG,CACI,IAAA,CAAA,uCAAuC,GAAG,KAAK,CAAC;QAEvD;;;;WAIG,CACI,IAAA,CAAA,aAAa,GAAG,eAAe,CAAC,qBAAqB,CAAC;QAE7D;;;;WAIG,CACI,IAAA,CAAA,qBAAqB,GAAG,IAAI,CAAC;QAEpC;;;WAGG,CACI,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QAExC;;;WAGG,CACI,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QAE5B;;;WAGG,CACI,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAErC;;;WAGG,CACI,IAAA,CAAA,kBAAkB,GAAG,IAAI,CAAC;QAEjC;;;WAGG,CACI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEhC;;;WAGG,CACI,IAAA,CAAA,sBAAsB,GAAG,CAAC,CAAC;QAElC;;;WAGG,CACI,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAEjC;;;WAGG,CACI,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAEjC;;;WAGG,CACI,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAEjC;;;WAGG,CACI,IAAA,CAAA,YAAY,GAAG,GAAG,CAAC;QAE1B;;;;WAIG,CACI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEhC;;;;WAIG,CACI,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAEtC;;;;;WAKG,CACI,IAAA,CAAA,uBAAuB,GAA0B,IAAI,CAAC;QAE7D;;;WAGG,CACI,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QAElC,IAAA,CAAA,kBAAkB,GAAY,KAAK,CAAC;QAYpC,IAAA,CAAA,yBAAyB,GAAW,SAAS,CAAC,6BAA6B,CAAC;QAmBpF;;;WAGG,CACI,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAEnC;;;;;WAKG,CACI,IAAA,CAAA,2BAA2B,GAAG,KAAK,CAAC;QAQ3C;;WAEG,CACK,IAAA,CAAA,wBAAwB,GAAqD,IAAI,CAAC;QA+B1F;;WAEG,CACK,IAAA,CAAA,cAAc,GAAG,8JAAI,aAAU,CAAsB,EAAE,CAAC,CAAC;QAEjE;;WAEG,CACK,IAAA,CAAA,mBAAmB,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAElD;;WAEG,CACK,IAAA,CAAA,MAAM,GAAG,KAAK,CAAC;QAEvB;;WAEG,CACK,IAAA,CAAA,4BAA4B,GAAG,KAAK,CAAC;QAErC,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QAEf,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAExC;;;;;WAKG,CAEI,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QAErB;;;;;;;WAOG,CACI,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC,CAAC;QAEvB;;;;;WAKG,CACI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QA0Cb,IAAA,CAAA,6BAA6B,GAAG,KAAK,CAAC;QAY5C,IAAI,CAAC,IAAI,GAAG,oLAAI,uBAAoB,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,yLAAI,4BAAyB,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,WAAW,GAAG,2LAAI,8BAA2B,CAAC,IAAI,CAAC,CAAC;QACzD,IAAI,CAAC,UAAU,GAAG,2LAAI,8BAA2B,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,KAAK,GAAG,qLAAI,wBAAqB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG,0LAAI,6BAA0B,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,2LAAI,yBAAsB,CAAC,IAAI,CAAC,CAAC;QAElD,2DAA2D;QAC3D,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,uBAAuB,GAAG,GAAoC,EAAE;YACjE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAE5B,sKAAI,gBAAa,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;gBAC9G,IAAI,CAAC,cAAc,CAAC,IAAI,CAAsB,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3E,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;YACpD,IAAI,CAAC,4CAA4C,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEnE,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B,CAAC,CAAC;QAEF,IAAI,CAAC,uBAAuB,uKAAG,4BAAA,AAAyB,EAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,oBAAoB,GAAG,6KAAI,uBAAoB,EAAE,CAAC;IAC3D,CAAC;IAED;;OAEG,CACH,IAAoB,uBAAuB,GAAA;QACvC,sKAAI,gBAAa,CAAC,wBAAwB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC;YAC9G,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;OAEG,CACH,IAAoB,gBAAgB,GAAA;QAChC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;IACnC,CAAC;IAED;;OAEG,CACa,YAAY,GAAA;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAuB,qBAAqB,GAAA;QACxC,OAAO,AACH,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,kBAAkB,IAC7D,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,qBAAqB,IAChE,IAAI,CAAC,UAAU,EAAE,oBAAoB,CACxC,CAAC;IACN,CAAC;IAED;;OAEG,CACa,iBAAiB,GAAA;QAC7B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,wBAAwB,CAAC;QACzC,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;IACvG,CAAC;IAED;;OAEG,CACa,gBAAgB,GAAA;QAC5B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,oBAAoB,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,qBAAqB,CAAC,CAAC;IAC3I,CAAC;IAED;;OAEG,CACO,gCAAgC,GAAA;QACtC,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,kBAAkB,CAAC;IAC3K,CAAC;IAED;;OAEG,CACO,gBAAgB,GAAA;QACtB,OAAO,AAAC,IAAI,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC;IACzG,CAAC;IAED;;OAEG,CACa,mBAAmB,GAAA;QAC/B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG,CACa,iBAAiB,CAAC,IAAkB,EAAE,OAAgB,EAAE,YAAsB,EAAA;QAC1F,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC;QAED,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;QAEzC,IAAI,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtC,IAAI,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,4BAA4B,KAAK,YAAY,EAAE,CAAC;gBAC/F,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC3B,IAAI,CAAC,2BAA2B,CAAA,EAAA,sCAAA,KAAqC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtF,OAAO,CAAC,eAAe,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAClF,CAAC;QAED,MAAM,OAAO,GAAuB,OAAO,CAAC,eAAe,CAAC;QAC5D,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC,UAAU,CAAC,uBAAuB,GAAG,KAAK,CAAC;YAChD,IAAI,CAAC,2CAA2C,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClE,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC;YAC7E,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,cAAc,IAAI,kLAAa,CAAC,qBAAqB,EAAE,CAAC;oBAC7D,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC9C,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,kBAAkB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;oBACpE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAClD,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,4BAA4B,sKAAI,gBAAa,CAAC,kCAAkC,EAAE,CAAC;oBACxF,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC5D,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,sKAAI,gBAAa,CAAC,qBAAqB,EAAE,CAAC;oBAC9D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC/C,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,IAAI,kLAAa,CAAC,qBAAqB,EAAE,CAAC;oBAC9D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC/C,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACvD,IAAI,iBAAiB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;oBAC9D,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAC5C,OAAO,KAAK,CAAC;oBACjB,CAAC;oBACD,IAAI,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;wBACtC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,EAAE,CAAC;4BAC9D,OAAO,KAAK,CAAC;wBACjB,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,2CAA2C;wBAC3C,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,IAAI,iBAAiB,CAAC,kBAAkB,EAAE,EAAE,2BAA2B,EAAE,CAAC;4BAChH,OAAO,KAAK,CAAC;wBACjB,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;oBAChE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAChD,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;oBAChE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE,CAAC;wBAChD,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;oBACvC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACxB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,EAAE,CAAC;4BAChD,OAAO,KAAK,CAAC;wBACjB,CAAC;oBACL,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBACnC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,EAAE,EAAE,CAAC;4BACpD,OAAO,KAAK,CAAC;wBACjB,CAAC;oBACL,CAAC;oBAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;wBACnC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,oBAAoB,EAAE,EAAE,CAAC;4BAC3D,OAAO,KAAK,CAAC;wBACjB,CAAC;oBACL,CAAC;oBAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBAC3B,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,EAAE,CAAC;4BACnD,OAAO,KAAK,CAAC;wBACjB,CAAC;oBACL,CAAC;oBAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC5B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,EAAE,EAAE,CAAC;4BACpD,OAAO,KAAK,CAAC;wBACjB,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,sKAAI,gBAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;oBACzH,uCAAuC;oBACvC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;wBAC/B,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,uBAAuB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;oBACzE,oBAAoB;oBACpB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,EAAE,CAAC;wBAC1C,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE5D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACzE,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,OAAO,EAAE,EAAE,CAAC;gBAChD,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,yCAAyC;QACzC,IAAI,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC5D,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;oBACvC,OAAO,KAAK,CAAC;gBACjB,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,wKAAY,CAAC,UAAU,CAAC,EAAE,CAAC;YAChG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;kKACzB,SAAM,CAAC,IAAI,CAAC,uDAAuD,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;QACrF,CAAC;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC;QACtC,MAAM,aAAa,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACjD,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAEhJ,IAAI,0BAA0B,GAAG,KAAK,CAAC;QAEvC,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAClC,yBAAyB,CAAC,MAAM,GAAG,MAAM,CAAC;gBAC1C,yBAAyB,CAAC,OAAO,GAAG,OAAO,CAAC;gBAC5C,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC;YAC/E,CAAC;YAED,iDAAiD;YACjD,IAAI,IAAI,CAAC,sBAAsB,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;gBACrE,MAAM,GAAG,cAAc,CAAC;gBACxB,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBAE5B,0BAA0B,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAE3C,IAAI,aAAa,EAAE,CAAC;oBAChB,oDAAoD;oBACpD,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBAClC,OAAO,KAAK,CAAC;gBACjB,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,mBAAmB,EAAE,CAAC;gBAC5B,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC9D,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACxC,WAAW,CAAC,mBAAmB,GAAG,0BAA0B,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5E,WAAW,CAAC,4BAA4B,GAAG,CAAC,CAAC,YAAY,CAAC;QAE1D,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,kBAAkB,GAAA;QACrB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC7E,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,cAAc,CAClB,IAAkB,EAClB,OAA2B,EAC3B,aAAiD,IAAI,EACrD,UAA8D,IAAI,EAClE,eAAkC,IAAI,EACtC,eAAkC,IAAI,EACtC,gBAAyB,EAAA;QAEzB,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAElF,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,CAAC,eAAe,EAAE,CAAC;QAE1B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,YAAY;QACZ,MAAM,SAAS,GAAG,wKAAI,kBAAe,EAAE,CAAC;QACxC,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;YAC/B,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,sBAAsB,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;YACd,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACrB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC3B,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACvB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,mBAAmB,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,iBAAiB,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,SAAS,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,MAAM,CAAC,CAAC;QAClD,CAAC;QAED,YAAY,uLAAG,4BAAA,AAAyB,EAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAE,YAAY,EAAE,CAAC,CAAC;QAE1G,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACvB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,cAAc,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,OAAO,CAAC,6BAA6B,EAAE,CAAC;YACxC,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,+BAA+B,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC3B,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,kBAAkB,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,QAAQ,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,SAAS,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,aAAa,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACvB,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,cAAc,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;QAC1C,CAAC;QAED,YAAY;QACZ,MAAM,OAAO,GAAG;oKAAC,gBAAY,CAAC,YAAY;SAAC,CAAC;QAE5C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAA,EAAA,EAAA,CAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC;YACxD,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;gBACpB,OAAO,CAAC,IAAI,CAAC,CAAA,EAAA,EAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;SAED,+MAAA,AAAyB,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;4LAC7D,gCAAA,AAA6B,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;4LAChD,mCAAA,AAAgC,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;4LACzD,2CAAA,AAAwC,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEjE,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,MAAM,QAAQ,GAAG;YACb,OAAO;YACP,MAAM;YACN,gBAAgB;YAChB,cAAc;YACd,aAAa;YACb,eAAe;YACf,cAAc;YACd,YAAY;YACZ,sBAAsB;YACtB,oBAAoB;YACpB,6BAA6B;YAC7B,gBAAgB;YAChB,YAAY;YACZ,kBAAkB;YAClB,WAAW;YACX,WAAW;YACX,WAAW;YACX,cAAc;YACd,kBAAkB;YAClB,4BAA4B;YAC5B,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,qBAAqB;YACrB,iBAAiB;YACjB,gBAAgB;YAChB,oBAAoB;YACpB,0BAA0B;YAC1B,2BAA2B;YAC3B,mBAAmB;YACnB,2BAA2B;YAC3B,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,cAAc;YACd,kBAAkB;YAClB,4BAA4B;YAC5B,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,gBAAgB;YAChB,oBAAoB;YACpB,cAAc;YACd,2BAA2B;YAC3B,YAAY;YACZ,gBAAgB;YAChB,2BAA2B;YAC3B,mBAAmB;YACnB,oBAAoB;YACpB,0BAA0B;YAC1B,aAAa;YACb,aAAa;YACb,aAAa;YACb,iBAAiB;YACjB,iBAAiB;YACjB,cAAc;YACd,cAAc;YACd,cAAc;YACd,cAAc;YACd,eAAe;YACf,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,eAAe;YACf,eAAe;YACf,eAAe;YACf,8BAA8B;YAC9B,8BAA8B;YAC9B,qBAAqB;YACrB,kBAAkB;YAClB,YAAY;YACZ,wBAAwB;YACxB,2BAA2B;YAC3B,YAAY;SACf,CAAC;QAEF,MAAM,QAAQ,GAAG;YACb,eAAe;YACf,mBAAmB;YACnB,6BAA6B;YAC7B,qBAAqB;YACrB,gBAAgB;YAChB,iBAAiB;YACjB,aAAa;YACb,iBAAiB;YACjB,gBAAgB;YAChB,mBAAmB;YACnB,sBAAsB;YACtB,uBAAuB;YACvB,mBAAmB;YACnB,qBAAqB;YACrB,wBAAwB;YACxB,aAAa;YACb,4BAA4B;YAC5B,oBAAoB;YACpB,cAAc;YACd,iBAAiB;YACjB,sBAAsB;YACtB,aAAa;YACb,uBAAuB;YACvB,uBAAuB;SAC1B,CAAC;QAEF,MAAM,cAAc,GAAG;YAAC,UAAU;YAAE,OAAO;YAAE,MAAM;SAAC,CAAC;QAErD,MAAM,eAAe,GAAG;YAAE,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;YAAE,2BAA2B,EAAE,OAAO,CAAC,qBAAqB;QAAA,CAAE,CAAC;QAE3I,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,YAAY,CAAC;QAC5C,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,OAAO,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,cAAc,CAAC;QACrD,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,SAAS,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,eAAe,CAAC;QAClD,IAAI,CAAC,2BAA2B,CAAA,IAAA,qCAAA,KAAoC,IAAI,CAAC,UAAU,CAAC,CAAC;gMAErF,kCAA+B,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE3E,gMAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;iLAC3C,uBAAoB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;wLAC3C,uBAAA,AAAoB,EAAC,QAAQ,CAAC,CAAC;QAE/B,qLAAI,+BAA4B,EAAE,CAAC;6LAC/B,+BAA4B,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;6LAChE,+BAA4B,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACpE,CAAC;4LAED,iCAAA,AAA8B,EAAyB;YACnD,aAAa,EAAE,QAAQ;YACvB,mBAAmB,EAAE,cAAc;YACnC,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,OAAO;YAChB,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;SACrD,CAAC,CAAC;QAEH,MAAM,WAAW,GAAoC,CAAA,CAAE,CAAC;QAExD,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAC7H,CAAC;QAED,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,MAAM,CAAC,YAAY,CAC9B,UAAU,EACc;YACpB,UAAU,EAAE,OAAO;YACnB,aAAa,EAAE,QAAQ;YACvB,mBAAmB,EAAE,cAAc;YACnC,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,UAAU;YACtB,OAAO,EAAE,OAAO;YAChB,eAAe;YACf,gBAAgB,EAAE,WAAW,CAAC,gBAAgB;YAC9C,wBAAwB,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU;YACpD,WAAW,EAAE,OAAO,CAAC,OAAO;YAC5B,cAAc,EAAE,IAAI,CAAC,eAAe;YACpC,yBAAyB,EAAE,IAAI,CAAC,cAAc,GACxC,SAAS,GACT,KAAK,IAAI,EAAE;gBACP,IAAI,IAAI,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;oBAC9C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,8BAA8B,CAAC,EAAE,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC;;;qBAAA;gBAC1G,CAAC,MAAM,CAAC;oBACJ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,0BAA0B,CAAC,EAAE,MAAM,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC;;;qBAAA;gBAClG,CAAC;gBAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC/B,CAAC;SACV,EACD,MAAM,CACT,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,SAAS,CAAC;QAEvC,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,eAAe,CACnB,IAAkB,EAClB,OAA2B,EAC3B,eAAkC,IAAI,EACtC,eAAkC,IAAI,EACtC,mBAA4B,KAAK,EAAA;QAEjC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,SAAS;2LACT,2BAAA,AAAuB,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxG,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;QAE5B,YAAY;4LACZ,6BAAA,AAA0B,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE3C,UAAU;QACV,MAAM,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,+BAA+B,CAAC;4LACnG,2BAAA,AAAwB,EAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC,GAAG,CAAC,CAAC;QAEtE,iCAAiC;4LACjC,uBAAA,AAAoB,EAAC,KAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAE1C,0NAA+B,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAE1F,WAAW;QACX,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACrD,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC5B,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;YACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAA,EAAA,EAAA,CAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAAE,CAAC;gBACxD,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAClC,CAAC;YACD,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC;gBAC3B,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC;gBAChC,OAAO,CAAC,8BAA8B,GAAG,CAAC,CAAC;gBAC3C,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;gBAC5B,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;gBAC5B,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAC7B,OAAO,CAAC,oBAAoB,GAAG,CAAC,CAAC;gBACjC,OAAO,CAAC,uBAAuB,GAAG,CAAC,CAAC;gBACpC,OAAO,CAAC,4BAA4B,GAAG,CAAC,CAAC;gBACzC,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC;gBAChC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;gBACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBAE7B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE,CAAC;oBAC9B,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACxC,CAAC;gBAED,IAAI,IAAI,CAAC,cAAc,sKAAI,gBAAa,CAAC,qBAAqB,EAAE,CAAC;wMAC7D,4BAAA,AAAyB,EAAC,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAClE,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;gBACzD,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;gBAC3B,CAAC;gBAED,IAAI,IAAI,CAAC,kBAAkB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;uMACpE,6BAAA,AAAyB,EAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;gBAC/E,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;gBAChC,CAAC;gBAED,IAAI,IAAI,CAAC,4BAA4B,sKAAI,gBAAa,CAAC,kCAAkC,EAAE,CAAC;wMACxF,4BAAA,AAAyB,EAAC,IAAI,CAAC,4BAA4B,EAAE,OAAO,EAAE,wBAAwB,CAAC,CAAC;gBACpG,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;gBAC3C,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,sKAAI,gBAAa,CAAC,qBAAqB,EAAE,CAAC;wMAC9D,4BAAA,AAAyB,EAAC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;oBACpE,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC;gBAC7D,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;gBAC5B,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,sKAAI,gBAAa,CAAC,qBAAqB,EAAE,CAAC;uMAC9D,6BAAA,AAAyB,EAAC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;oBACpE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;gBAC9D,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;gBAC5B,CAAC;gBAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBACvD,IAAI,iBAAiB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;oBAC9D,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;oBAC1B,OAAO,CAAC,eAAe,GAAG,iBAAiB,CAAC,UAAU,CAAC;oBACvD,OAAO,CAAC,cAAc,GAAG,iBAAiB,CAAC,MAAM,CAAC;oBAClD,OAAO,CAAC,oBAAoB,GAAG,iBAAiB,CAAC,eAAe,CAAC;oBACjE,OAAO,CAAC,wBAAwB,GAAG,iBAAiB,CAAC,iBAAiB,CAAC;oBACvE,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;oBAEjC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,wBAAwB,GAAG,CAAC,EAAE,CAAC;wBAC9D,OAAO,CAAC,WAAW,GAAG,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC;wBACzD,IAAI,MAAM,CAAC,SAAS,CAAC,+BAA+B,EAAE,CAAC;4BACnD,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC;wBACpD,CAAC;wBAED,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;wBAClC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,EAAE,CAAC;4BAClC,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;wBACrC,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;oBACvC,CAAC;oBAED,OAAO,CAAC,cAAc,GAAG,iBAAiB,CAAC,eAAe,6KAAK,UAAO,CAAC,aAAa,CAAC;oBACrF,OAAO,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,MAAM,CAAC;oBACpD,OAAO,CAAC,uBAAuB,GAAG,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC;oBAE5J,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;oBACpC,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;oBACvC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,wBAAwB,GAAG,KAAK,CAAC;oBACzC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;oBACxC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;oBAC9C,OAAO,CAAC,mCAAmC,GAAG,KAAK,CAAC;oBACpD,OAAO,CAAC,2CAA2C,GAAG,KAAK,CAAC;oBAE5D,OAAQ,iBAAiB,CAAC,eAAe,EAAE,CAAC;wBACxC,6KAAK,UAAO,CAAC,aAAa;4BACtB,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC;4BACtC,MAAM;wBACV,6KAAK,UAAO,CAAC,WAAW;4BACpB,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;4BACpC,MAAM;wBACV,KAAK,kLAAO,CAAC,eAAe;4BACxB,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;4BACxC,MAAM;wBACV,6KAAK,UAAO,CAAC,WAAW;4BACpB,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;4BACpC,MAAM;wBACV,6KAAK,UAAO,CAAC,cAAc;4BACvB,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;4BACvC,MAAM;wBACV,KAAK,kLAAO,CAAC,oBAAoB;4BAC7B,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC;4BAC7C,MAAM;wBACV,6KAAK,UAAO,CAAC,0BAA0B;4BACnC,OAAO,CAAC,mCAAmC,GAAG,IAAI,CAAC;4BACnD,MAAM;wBACV,4KAAK,WAAO,CAAC,mCAAmC;4BAC5C,OAAO,CAAC,2CAA2C,GAAG,IAAI,CAAC;4BAC3D,MAAM;wBACV,6KAAK,UAAO,CAAC,UAAU,CAAC;wBACxB,KAAK,kLAAO,CAAC,aAAa,CAAC;wBAC3B;4BACI,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC;4BACnC,OAAO,CAAC,6BAA6B,GAAS,iBAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;4BAChG,MAAM;oBACd,CAAC;oBAED,IAAI,iBAAiB,CAAC,eAAe,6KAAK,UAAO,CAAC,WAAW,EAAE,CAAC;wBAC5D,IAAI,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;4BACtC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;4BAChC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;4BAC9C,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;4BACrC,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC;gCACzD,OAAO,CAAC,iCAAiC,GAAG,IAAI,CAAC;4BACrD,CAAC;wBACL,CAAC,MAEI,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;4BAChC,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC;4BAC7C,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;4BACjC,OAAO,CAAC,iCAAiC,GAAG,KAAK,CAAC;4BAClD,IACI,IAAI,CAAC,0BAA0B,IAC/B,IAAI,CAAC,iBAAiB,IACtB,IAAI,CAAC,iBAAiB,IACtB,MAAM,CAAC,OAAO,EAAE,CAAC,iBAAiB,IAAI,CAAC,IACvC,IAAI,CAAC,4BAA4B,EACnC,CAAC;gCACC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;4BACzC,CAAC,MAAM,CAAC;gCACJ,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;4BACxC,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;oBAC3B,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;oBACjC,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;oBACxC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;oBACpC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;oBAC9C,OAAO,CAAC,wBAAwB,GAAG,KAAK,CAAC;oBACzC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,sBAAsB,GAAG,KAAK,CAAC;oBACvC,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;oBAC9C,OAAO,CAAC,mCAAmC,GAAG,KAAK,CAAC;oBACpD,OAAO,CAAC,2CAA2C,GAAG,KAAK,CAAC;oBAC5D,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBAC/B,OAAO,CAAC,6BAA6B,GAAG,KAAK,CAAC;oBAC9C,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;oBACjC,OAAO,CAAC,iCAAiC,GAAG,KAAK,CAAC;oBAClD,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;oBACxC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;oBACrC,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;oBAChC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBAC/B,OAAO,CAAC,wBAAwB,GAAG,KAAK,CAAC;gBAC7C,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;wMAChE,4BAAA,AAAyB,EAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;oBACtE,OAAO,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC;oBAC9D,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;oBACzD,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACxD,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAC7B,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;wMAChE,4BAAA,AAAyB,EAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;oBACtE,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;gBAC7D,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAC7B,CAAC;gBAED,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;oBACvC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;4MACxB,4BAAA,AAAyB,EAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;wBAC1E,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC,qCAAqC,CAAC;wBACnF,OAAO,CAAC,6BAA6B,GAAG,CAAC,IAAI,CAAC,qCAAqC,IAAI,IAAI,CAAC,qCAAqC,CAAC;wBAClI,OAAO,CAAC,6BAA6B,GAAG,IAAI,CAAC,qCAAqC,CAAC;wBACnF,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,0CAA0C,CAAC;wBAC/E,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;oBACvC,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;4MACnC,4BAAA,AAAyB,EAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;wBAC9E,OAAO,CAAC,+BAA+B,GAAG,IAAI,CAAC,wCAAwC,CAAC;wBACxF,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,uCAAuC,CAAC;wBAC7E,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBACtE,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;oBACjC,CAAC;oBAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBAC/D,OAAO,CAAC,mCAAmC,GAAG,IAAI,CAAC,8CAA8C,CAAC;wBAClG,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;4BACnC,gNAAA,AAAyB,EAAC,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;4BAC7F,OAAO,CAAC,0BAA0B,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC;wBACrF,CAAC,MAAM,CAAC;4BACJ,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;wBACzC,CAAC;wBACD,IACI,IAAI,CAAC,mBAAmB,IACxB,CAAC,CAAC,IAAI,CAAC,2BAA2B,IAAI,AAAC,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,8CAA8C,AAAC,CAAC,EAClI,CAAC;gNACC,4BAAA,AAAyB,EAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;4BAC5E,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;wBACpE,CAAC,MAAM,CAAC;4BACJ,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;wBAChC,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;wBACrC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;oBAChC,CAAC;oBAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;4MAC5B,4BAAA,AAAyB,EAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;oBACrF,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;oBACpC,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC7B,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;gBACpC,CAAC;gBAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,YAAY,sKAAI,gBAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;wMACzH,4BAAA,AAAyB,EAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;oBAE9D,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,IAAI,kLAAa,CAAC,qBAAqB,EAAE,CAAC;wBAClF,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;wBACxB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC;wBAClD,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;oBAC7D,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;oBAC7B,CAAC;oBAED,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAClE,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;oBACrB,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACzB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;oBAC7B,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAClC,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;gBAC1C,CAAC;gBAED,IAAI,IAAI,CAAC,uBAAuB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;oBACzE,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC/B,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;gBACvE,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;oBAChC,OAAO,CAAC,oBAAoB,GAAG,KAAK,CAAC;gBACzC,CAAC;gBAED,IAAI,IAAI,CAAC,gCAAgC,EAAE,EAAE,CAAC;oBAC1C,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;gBACnC,CAAC,MAAM,CAAC;oBACJ,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;gBACpC,CAAC;YACL,CAAC;YAED,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAEvD,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,qBAAqB,EAAE,CAAC;gBAC/D,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;gBACxC,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACxC,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,KAAK,eAAe,CAAC,iBAAiB,EAAE,CAAC;gBAClE,OAAO,CAAC,uBAAuB,GAAG,KAAK,CAAC;gBACxC,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC;YACvC,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACvC,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC;YACxC,CAAC;YAED,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAEvD,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAClD,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACpC,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;YACrC,CAAC;YAED,iGAAiG;YACjG,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,uBAAuB,CAAC;YAEnD,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,IAAI,IAAI,CAAC,2BAA2B,CAAC;QAClG,CAAC;QAED,IAAI,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YACrD,OAAO,CAAC,cAAc,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACzF,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,KAAK,KAAA,IAAS,CAAC,SAAA,KAAA,KAAmB,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC,8BAA8B,CAAC;YAC3I,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;YACzD,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,sBAAsB,CAAC;YAC5E,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QAC7D,CAAC;QAED,IAAI,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACzE,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEtD,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAEvD,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAErD,QAAQ;QACR,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gMACxB,wBAAA,AAAqB,EACjB,IAAI,EACJ,KAAK,EACL,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAClC,OAAO,EACP,IAAI,CAAC,4BAA4B,CACpC,CAAC;YACF,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,IAAI,AAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,UAAU,CAAC,CAAC,CAAC;YAC9H,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QACxC,CAAC;QAED,kDAAkD;4LAClD,oCAAA,AAAiC,EAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAE7H,kBAAkB;QAClB,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,kDAAkD,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzE,UAAU;4LACV,8BAAA,AAA2B,EAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,iBAAiB,KAAK,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAE5H,kBAAkB;QAClB,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;OAKG,CACa,gBAAgB,CAAC,IAAkB,EAAE,UAAyC,EAAE,OAA8C,EAAA;QAC1I,MAAM,YAAY,GAAG;YACjB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,KAAK;YACnB,GAAG,OAAO;SACb,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,2BAA2B,CAAA,EAAA,sCAAA,KAAqC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtF,MAAM,UAAU,GAAG,GAAG,EAAE;YACpB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,OAAO;YACX,CAAC;YACD,MAAM,OAAO,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,gBAAgB,CAAE,CAAC;YACnJ,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAClC,yBAAyB,CAAC,MAAM,GAAG,MAAM,CAAC;gBAC1C,yBAAyB,CAAC,OAAO,GAAG,IAAI,CAAC;gBACzC,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC;YAC/E,CAAC;YACD,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;gBACnB,IAAI,UAAU,EAAE,CAAC;oBACb,UAAU,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;oBAChC,IAAI,UAAU,EAAE,CAAC;wBACb,UAAU,CAAC,IAAI,CAAC,CAAC;oBACrB,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC;QACF,UAAU,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG,CACa,kBAAkB,GAAA;QAC9B,uBAAuB;QACvB,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;QAChC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACtC,GAAG,CAAC,UAAU,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAC;QAChD,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QACxC,GAAG,CAAC,UAAU,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;QAC/C,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACtC,GAAG,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;QAC9C,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QACvC,GAAG,CAAC,UAAU,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;QACjD,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;QAChD,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;QAEvC,GAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QACtC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAChC,GAAG,CAAC,UAAU,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC;QAC1C,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QAExC,GAAG,CAAC,UAAU,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;QAClD,GAAG,CAAC,UAAU,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;QAClD,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAC/B,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;QACxC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QAEnC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAEhC,GAAG,CAAC,UAAU,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;QACjD,GAAG,CAAC,UAAU,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;QAC/C,GAAG,CAAC,UAAU,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;QAChD,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QACvC,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;QAExC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QACnC,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QAEnC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;QACrC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAClC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAElC,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QAEhC,KAAK,CAAC,kBAAkB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;OAKG,CACa,cAAc,CAAC,KAAa,EAAE,IAAU,EAAE,OAAgB,EAAA;QACtE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,OAAO,GAAuB,OAAO,CAAC,eAAe,CAAC;QAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,iBAAiB;QACjB,IAAI,CAAC,oBAAoB,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACzD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,0BAA0B;QAC1B,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAErD,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gMAEhG,kCAA+B,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAExG,MAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC;QAClC,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,sCAAsC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7D,gBAAgB;QAChB,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAChC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7E,QAAQ;4LACR,sBAAA,AAAmB,EAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEzE,IAAI,iBAAiB,GAA0B,IAAI,CAAC;QACpD,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;QAChC,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YAChC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAEjD,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,CAAC,sBAAsB,EAAE,CAAC;gBAC9F,mBAAmB;gBACnB,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;oBACxB,IAAI,IAAI,CAAC,cAAc,sKAAI,gBAAa,CAAC,qBAAqB,EAAE,CAAC;wBAC7D,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;4MAClG,oBAAA,AAAiB,EAAC,IAAI,CAAC,cAAc,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;oBAC1D,CAAC;oBAED,IAAI,IAAI,CAAC,kBAAkB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;wBACpE,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;4MAC9G,oBAAA,AAAiB,EAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC;oBAClE,CAAC;oBAED,IAAI,IAAI,CAAC,4BAA4B,sKAAI,gBAAa,CAAC,kCAAkC,EAAE,CAAC;wBACxF,GAAG,CAAC,YAAY,CAAC,4BAA4B,EAAE,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,EAAE,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC;wBAC5I,wMAAA,AAAiB,EAAC,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE,sBAAsB,CAAC,CAAC;oBACtF,CAAC;oBAED,IAAI,IAAI,CAAC,eAAe,sKAAI,gBAAa,CAAC,qBAAqB,EAAE,CAAC;wBAC9D,GAAG,CAAC,YAAY,CACZ,eAAe,EACf,IAAI,CAAC,eAAe,CAAC,gBAAgB,EACrC,IAAI,CAAC,eAAe,CAAC,KAAK,EAC1B,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,uCAAuC,CAC/C,CAAC;2MACF,qBAAA,AAAiB,EAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;oBAC5D,CAAC;oBAED,IAAI,IAAI,CAAC,eAAe,sKAAI,gBAAa,CAAC,qBAAqB,EAAE,CAAC;wBAC9D,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;4MACrG,oBAAA,AAAiB,EAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;oBAC5D,CAAC;oBAED,IAAI,iBAAiB,sKAAI,gBAAa,CAAC,wBAAwB,EAAE,CAAC;wBAC9D,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,CAAC,CAAC;wBACrF,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;wBAEtF,IAAU,iBAAkB,CAAC,eAAe,EAAE,CAAC;4BAC3C,MAAM,WAAW,GAAgB,iBAAiB,CAAC;4BAEnD,GAAG,CAAC,aAAa,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;4BAC1E,GAAG,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;wBACtE,CAAC;wBAED,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;4BACzB,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC;4BAChD,GAAG,CAAC,YAAY,CAAC,0BAA0B,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;wBAC1E,CAAC;wBAED,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;4BAC5B,MAAM,WAAW,GAAG,iBAAiB,CAAC,mBAAmB,CAAC;4BAC1D,IAAI,OAAO,CAAC,6BAA6B,IAAI,WAAW,EAAE,CAAC;gCACvD,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;oCAC9B,MAAM,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,CAAC;oCAC1D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;oCAC7D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;oCAC7D,GAAG,CAAC,aAAa,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;oCAC7D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;oCAC3D,GAAG,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;gCAC/D,CAAC,MAAM,CAAC;oCACJ,GAAG,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCACnF,GAAG,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCACnF,GAAG,CAAC,YAAY,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCACnF,GAAG,CAAC,YAAY,CACZ,iBAAiB,EACjB,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EACnC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EACnC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CACtC,CAAC;oCACF,GAAG,CAAC,YAAY,CACZ,iBAAiB,EACjB,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EACnC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,EACnC,WAAW,CAAC,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,CACtC,CAAC;oCACF,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oCACvF,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oCACvF,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;oCACvF,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gCAC3F,CAAC;4BACL,CAAC;wBACL,CAAC,MAAM,CAAC;4BACJ,+EAA+E;4BAC/E,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,iCAAiC,EAAE,CAAC;gCACxE,GAAG,CAAC,aAAa,CAAC,8BAA8B,EAAE,iBAAiB,CAAC,iBAAkB,CAAC,kBAAmB,CAAC,CAAC;4BAChH,CAAC;wBACL,CAAC;wBAED,GAAG,CAAC,YAAY,CACZ,8BAA8B,EAC9B,iBAAiB,CAAC,OAAO,EAAE,CAAC,KAAK,EACjC,iBAAiB,CAAC,kBAAkB,EACpC,iBAAiB,CAAC,mBAAmB,CACxC,CAAC;oBACN,CAAC;oBAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,kLAAa,CAAC,sBAAsB,EAAE,CAAC;wBAChE,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;2MACxG,qBAAA,AAAiB,EAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;oBAC9D,CAAC;oBAED,IAAI,IAAI,CAAC,gBAAgB,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;wBAChE,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;4MACxG,oBAAA,AAAiB,EAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;oBAC9D,CAAC;oBAED,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;wBACvC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BACxB,GAAG,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;gNAC1I,oBAAA,AAAiB,EAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;wBAClE,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BACnC,GAAG,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gNACzH,oBAAA,AAAiB,EAAC,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;wBACtE,CAAC;wBAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;4BACnC,GAAG,CAAC,YAAY,CAAC,2BAA2B,EAAE,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;gCACzI,oMAAA,AAAiB,EAAC,IAAI,CAAC,2BAA2B,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;wBACpF,CAAC;wBAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;4BAClD,GAAG,CAAC,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;gNACjH,oBAAA,AAAiB,EAAC,IAAI,CAAC,mBAAmB,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;wBACpE,CAAC;wBAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BAC5B,GAAG,CAAC,YAAY,CAAC,2BAA2B,EAAE,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;gNAC3H,oBAAA,AAAiB,EAAC,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;wBAC7E,CAAC;oBACL,CAAC;oBAED,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,sKAAI,gBAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;wBACzH,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;2MACrH,qBAAA,AAAiB,EAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;wBAElD,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;4BAChC,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBACtH,CAAC,MAAM,CAAC;4BACJ,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBACtH,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,aAAa;gBACb,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACjD,CAAC;gBAED,SAAS;gBACT,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;kLAC3B,YAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;kLACrG,YAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxG,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,kBAAkB,IAAI,GAAG,CAAC;oBACvD,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,yEAAyE;iLAC/F,aAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;oBAC5B,8FAA8F;oBAC9F,mDAAmD;oBACnD,mBAAmB;oBACnB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;kLAChE,YAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;oBAC3B,GAAG,CAAC,kBAAkB,CAAC,oBAAoB,gKAAE,YAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClE,GAAG,CAAC,YAAY,CAAC,6BAA6B,EAAE,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAC5G,CAAC,MAAM,CAAC;oBACJ,GAAG,CAAC,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBACxF,CAAC;gBAED,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,kLAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,+JAAC,SAAM,CAAC,aAAa,CAAC,CAAC;gBACtH,GAAG,CAAC,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC5D,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,UAAU,EAAE,+BAA+B,EAAE,CAAC;oBAC7E,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;gBAC3D,CAAC,MAAM,CAAC;oBACJ,GAAG,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpE,CAAC;gBAED,GAAG,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;gBAChD,GAAG,CAAC,WAAW,CAAC,sBAAsB,EAAE,IAAI,CAAC,qBAAqB,IAAI,GAAG,CAAC,CAAC;gBAE3E,OAAO;gBACP,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;gBAC9C,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAChD,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC;gBAChF,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAEhD,GAAG,CAAC,aAAa,CAAC,oBAAoB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAE7D,SAAS;gBACT,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAE/E,GAAG,CAAC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBAE5D,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACtE,CAAC;YAED,WAAW;YACX,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,IAAI,CAAC,cAAc,sKAAI,gBAAa,CAAC,qBAAqB,EAAE,CAAC;oBAC7D,GAAG,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBACzD,CAAC;gBAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,kLAAa,CAAC,wBAAwB,EAAE,CAAC;oBACpE,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACjE,CAAC;gBAED,IAAI,IAAI,CAAC,4BAA4B,IAAI,kLAAa,CAAC,kCAAkC,EAAE,CAAC;oBACxF,GAAG,CAAC,UAAU,CAAC,6BAA6B,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBACrF,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,sKAAI,gBAAa,CAAC,qBAAqB,EAAE,CAAC;oBAC9D,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC3D,CAAC;gBAED,IAAI,IAAI,CAAC,eAAe,qKAAI,iBAAa,CAAC,qBAAqB,EAAE,CAAC;oBAC9D,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAC3D,CAAC;gBAED,IAAI,iBAAiB,qKAAI,iBAAa,CAAC,wBAAwB,EAAE,CAAC;oBAC9D,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;wBAC/B,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;oBAC3D,CAAC,MAAM,CAAC;wBACJ,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,cAAc,IAAI,iBAAiB,CAAC,CAAC;wBAC3F,GAAG,CAAC,UAAU,CAAC,sBAAsB,EAAE,iBAAiB,CAAC,cAAc,IAAI,iBAAiB,CAAC,CAAC;wBAC9F,GAAG,CAAC,UAAU,CAAC,uBAAuB,EAAE,iBAAiB,CAAC,eAAe,IAAI,iBAAiB,CAAC,CAAC;oBACpG,CAAC;oBAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;wBAC3B,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;oBAC7E,CAAC;oBAED,qDAAqD;oBACrD,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,eAAe,CAAC;oBACrD,IAAI,IAAI,CAAC,iBAAiB,IAAI,YAAY,EAAE,CAAC;wBACzC,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,YAAY,CAAC,cAAc,EAAE,CAAC,CAAC;oBACjE,CAAC;gBACL,CAAC;gBAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;oBAC1B,GAAG,CAAC,UAAU,CAAC,wBAAwB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;gBAC3E,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;oBAChE,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC7D,CAAC;gBAED,IAAI,IAAI,CAAC,gBAAgB,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;oBAChE,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAC7D,CAAC;gBAED,sKAAI,gBAAa,CAAC,sBAAsB,EAAE,CAAC;oBACvC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACxB,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBACjE,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBACnC,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACrE,CAAC;oBAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;wBACnC,GAAG,CAAC,UAAU,CAAC,4BAA4B,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;oBACnF,CAAC;oBAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;wBAClD,GAAG,CAAC,UAAU,CAAC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;oBACnE,CAAC;oBAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC5B,GAAG,CAAC,UAAU,CAAC,qBAAqB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACrE,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC,mBAAmB,sKAAI,gBAAa,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;oBACzH,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBACrD,CAAC;YACL,CAAC;YAED,yBAAyB;YACzB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,+BAA+B,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzF,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;YAClC,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAEzD,aAAa;4LACb,gBAAA,AAAa,EAAC,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAE/C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,8BAA8B,EAAE,CAAC;YACpE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/B,SAAS;YACT,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oMAChD,aAAA,AAAU,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtF,CAAC;YAED,OAAO;YACP,IACI,AAAC,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,kJAAK,QAAK,CAAC,YAAY,CAAC,GAC3E,iBAAiB,IACjB,IAAI,CAAC,UAAU,CAAC,iBAAiB,IACjC,IAAI,CAAC,cAAc,IACnB,OAAO,CAAC,OAAO,EACjB,CAAC;gBACC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC1B,CAAC;YAED,MAAM;aACN,uMAAA,AAAiB,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;YAEzD,gBAAgB;YAChB,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;oMAChC,4BAAA,AAAyB,EAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACxD,CAAC;YAED,IAAI,OAAO,CAAC,8BAA8B,EAAE,CAAC;gBACzC,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;YACtE,CAAC;YAED,mBAAmB;YACnB,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE5D,aAAa;gMACb,eAAA,AAAY,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAEnD,GAAG,CAAC,MAAM,EAAE,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACa,cAAc,GAAA;QAC1B,MAAM,OAAO,GAAG,KAAK,CAAC,cAAc,EAAE,CAAC;QAEvC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,IAAI,IAAI,CAAC,4BAA4B,CAAC,UAAU,IAAI,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/I,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3G,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3G,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxC,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9H,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/F,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3G,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,2BAA2B,CAAC,UAAU,IAAI,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5I,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,IAAI,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvH,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG,CACK,qBAAqB,GAAA;QACzB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnC,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,kBAAkB,CAAC;IAC9C,CAAC;IAED;;;OAGG,CACa,iBAAiB,GAAA;QAC7B,MAAM,cAAc,GAAG,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAEjD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;OAIG,CACa,UAAU,CAAC,OAAoB,EAAA;QAC3C,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,OAAO,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,KAAK,OAAO,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,KAAK,OAAO,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,OAAO,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,KAAK,OAAO,EAAE,CAAC;YAC/C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,KAAK,OAAO,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,oBAAoB,KAAK,OAAO,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,OAAO,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG,CACa,kBAAkB,GAAA;QAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,mBAAmB,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,uBAAuB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,0BAA0B,EAAE,CAAC;QAC7E,IAAI,uBAAuB,EAAE,CAAC;YAC1B,uBAAuB,CAAC,OAAO,GAAG,IAAI,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACa,OAAO,CAAC,kBAA4B,EAAE,oBAA8B,EAAA;QAChF,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACpC,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,sBAAsB,KAAK,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC1G,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;YAC3C,CAAC;YAED,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC;YAC/B,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;YACnC,IAAI,CAAC,4BAA4B,EAAE,OAAO,EAAE,CAAC;YAC7C,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC;YACnC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;YACrC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,2BAA2B,EAAE,OAAO,EAAE,CAAC;YAC5C,IAAI,CAAC,mBAAmB,EAAE,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,oBAAoB,EAAE,OAAO,EAAE,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAE9B,IAAI,IAAI,CAAC,6BAA6B,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACtE,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAChG,CAAC;QAED,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;IAC5D,CAAC;;AA96ED;;GAEG,CACoB,gBAAA,kBAAkB,gKAAG,WAAQ,CAAC,eAAZ,CAA4B;AAErE;;GAEG,CACoB,gBAAA,qBAAqB,gKAAG,WAAQ,CAAC,kBAAZ,CAA+B;AAE3E;;GAEG,CACoB,gBAAA,sBAAsB,gKAAG,WAAQ,CAAC,mBAAZ,CAAgC;AAE7E;;;GAGG,CACoB,gBAAA,6BAA6B,GAAG,wKAAQ,CAAC,0BAAZ,CAAuC;AAE3F;;;GAGG,CACW,gBAAA,+BAA+B,GAAG,CAAC,AAAJ,CAAK;AAElD;;GAEG,CACoB,gBAAA,qBAAqB,GAAG,CAAC,AAAJ,CAAK;AAEjD;;;GAGG,CACoB,gBAAA,iBAAiB,GAAG,CAAC,AAAJ,CAAK;AAE7C;;;GAGG,CACoB,gBAAA,qBAAqB,GAAG,CAAC,AAAJ,CAAK;AAEjD;;;GAGG,CACW,gBAAA,SAAS,GAAG,KAAK,AAAR,CAAS;wJAidtB,aAAA,EAAA;kKADT,0CAAA,AAAuC,EAAE;sEAC4B;wJAoE/D,aAAA,EAAA;kKADN,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;kDAC5B", "debugId": null}}, {"offset": {"line": 4460, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrMaterial.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrMaterial.ts"], "sourcesContent": ["import { serialize, serializeAsColor3, expandToProperty, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport { GetEnvironmentBRDFTexture } from \"../../Misc/brdfTextureTools\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport type { ImageProcessingConfiguration } from \"../../Materials/imageProcessingConfiguration\";\r\nimport type { ColorCurves } from \"../../Materials/colorCurves\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport { RegisterClass } from \"../../Misc/typeStore\";\r\nimport { Material } from \"../material\";\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\n\r\n/**\r\n * The Physically based material of BJS.\r\n *\r\n * This offers the main features of a standard PBR material.\r\n * For more information, please refer to the documentation :\r\n * https://doc.babylonjs.com/features/featuresDeepDive/materials/using/introToPBR\r\n */\r\nexport class PBRMaterial extends PBRBaseMaterial {\r\n    /**\r\n     * PBRMaterialTransparencyMode: No transparency mode, Alpha channel is not use.\r\n     */\r\n    public static override readonly PBRMATERIAL_OPAQUE = PBRBaseMaterial.PBRMATERIAL_OPAQUE;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Alpha Test mode, pixel are discarded below a certain threshold defined by the alpha cutoff value.\r\n     */\r\n    public static override readonly PBRMATERIAL_ALPHATEST = PBRBaseMaterial.PBRMATERIAL_ALPHATEST;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Pixels are blended (according to the alpha mode) with the already drawn pixels in the current frame buffer.\r\n     */\r\n    public static override readonly PBRMATERIAL_ALPHABLEND = PBRBaseMaterial.PBRMATERIAL_ALPHABLEND;\r\n\r\n    /**\r\n     * PBRMaterialTransparencyMode: Pixels are blended (according to the alpha mode) with the already drawn pixels in the current frame buffer.\r\n     * They are also discarded below the alpha cutoff threshold to improve performances.\r\n     */\r\n    public static override readonly PBRMATERIAL_ALPHATESTANDBLEND = PBRBaseMaterial.PBRMATERIAL_ALPHATESTANDBLEND;\r\n\r\n    /**\r\n     * Defines the default value of how much AO map is occluding the analytical lights\r\n     * (point spot...).\r\n     */\r\n    public static override DEFAULT_AO_ON_ANALYTICAL_LIGHTS = PBRBaseMaterial.DEFAULT_AO_ON_ANALYTICAL_LIGHTS;\r\n\r\n    /**\r\n     * Intensity of the direct lights e.g. the four lights available in your scene.\r\n     * This impacts both the direct diffuse and specular highlights.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public directIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the emissive part of the material.\r\n     * This helps controlling the emissive effect without modifying the emissive color.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Intensity of the environment e.g. how much the environment will light the object\r\n     * either through harmonics for rough material or through the reflection for shiny ones.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public environmentIntensity: number = 1.0;\r\n\r\n    /**\r\n     * This is a special control allowing the reduction of the specular highlights coming from the\r\n     * four lights of the scene. Those highlights may not be needed in full environment lighting.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public specularIntensity: number = 1.0;\r\n\r\n    /**\r\n     * Debug Control allowing disabling the bump map on this material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public disableBumpMap: boolean = false;\r\n\r\n    /**\r\n     * AKA Diffuse Texture in standard nomenclature.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public albedoTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * OpenPBR Base Weight texture (multiplier to the diffuse and metal lobes).\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public baseWeightTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * OpenPBR Base Diffuse Roughness texture (roughness of the diffuse lobe).\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public baseDiffuseRoughnessTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * AKA Occlusion Texture in other nomenclature.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public ambientTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * AKA Occlusion Texture Intensity in other nomenclature.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public ambientTextureStrength: number = 1.0;\r\n\r\n    /**\r\n     * Defines how much the AO map is occluding the analytical lights (point spot...).\r\n     * 1 means it completely occludes it\r\n     * 0 mean it has no impact\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public ambientTextureImpactOnAnalyticalLights: number = PBRMaterial.DEFAULT_AO_ON_ANALYTICAL_LIGHTS;\r\n\r\n    /**\r\n     * Stores the alpha values in a texture. Use luminance if texture.getAlphaFromRGB is true.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public opacityTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores the reflection values in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectionTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores the emissive values in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * AKA Specular texture in other nomenclature.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectivityTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Used to switch from specular/glossiness to metallic/roughness workflow.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallicTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Specifies the metallic scalar of the metallic/roughness workflow.\r\n     * Can also be used to scale the metalness values of the metallic texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallic: Nullable<number>;\r\n\r\n    /**\r\n     * Specifies the roughness scalar of the metallic/roughness workflow.\r\n     * Can also be used to scale the roughness values of the metallic texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public roughness: Nullable<number>;\r\n\r\n    /**\r\n     * In metallic workflow, specifies an F0 factor to help configuring the material F0.\r\n     * By default the indexOfrefraction is used to compute F0;\r\n     *\r\n     * This is used as a factor against the default reflectance at normal incidence to tweak it.\r\n     *\r\n     * F0 = defaultF0 * metallicF0Factor * metallicReflectanceColor;\r\n     * F90 = metallicReflectanceColor;\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallicF0Factor = 1;\r\n\r\n    /**\r\n     * In metallic workflow, specifies an F0 color.\r\n     * By default the F90 is always 1;\r\n     *\r\n     * Please note that this factor is also used as a factor against the default reflectance at normal incidence.\r\n     *\r\n     * F0 = defaultF0_from_IOR * metallicF0Factor * metallicReflectanceColor\r\n     * F90 = metallicF0Factor;\r\n     */\r\n    @serializeAsColor3()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallicReflectanceColor = Color3.White();\r\n\r\n    /**\r\n     * Specifies that only the A channel from metallicReflectanceTexture should be used.\r\n     * If false, both RGB and A channels will be used\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useOnlyMetallicFromMetallicReflectanceTexture = false;\r\n\r\n    /**\r\n     * Defines to store metallicReflectanceColor in RGB and metallicF0Factor in A\r\n     * This is multiplied against the scalar values defined in the material.\r\n     * If useOnlyMetallicFromMetallicReflectanceTexture is true, don't use the RGB channels, only A\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallicReflectanceTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Defines to store reflectanceColor in RGB\r\n     * This is multiplied against the scalar values defined in the material.\r\n     * If both reflectanceTexture and metallicReflectanceTexture textures are provided and useOnlyMetallicFromMetallicReflectanceTexture\r\n     * is false, metallicReflectanceTexture takes priority and reflectanceTexture is not used\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectanceTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Used to enable roughness/glossiness fetch from a separate channel depending on the current mode.\r\n     * Gray Scale represents roughness in metallic mode and glossiness in specular mode.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public microSurfaceTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores surface normal data used to displace a mesh in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public bumpTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores the pre-calculated light information of a mesh in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", null)\r\n    public lightmapTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Stores the refracted light information in a texture.\r\n     */\r\n    public get refractionTexture(): Nullable<BaseTexture> {\r\n        return this.subSurface.refractionTexture;\r\n    }\r\n    public set refractionTexture(value: Nullable<BaseTexture>) {\r\n        this.subSurface.refractionTexture = value;\r\n        if (value) {\r\n            this.subSurface.isRefractionEnabled = true;\r\n        } else if (!this.subSurface.linkRefractionWithTransparency) {\r\n            this.subSurface.isRefractionEnabled = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * The color of a material in ambient lighting.\r\n     */\r\n    @serializeAsColor3(\"ambient\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public ambientColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * AKA Diffuse Color in other nomenclature.\r\n     */\r\n    @serializeAsColor3(\"albedo\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public albedoColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * OpenPBR Base Weight (multiplier to the diffuse and metal lobes).\r\n     */\r\n    @serialize(\"baseWeight\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public baseWeight = 1;\r\n\r\n    /**\r\n     * OpenPBR Base Diffuse Roughness (roughness of the diffuse lobe).\r\n     */\r\n    @serialize(\"baseDiffuseRoughness\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public baseDiffuseRoughness: Nullable<number>;\r\n\r\n    /**\r\n     * AKA Specular Color in other nomenclature.\r\n     */\r\n    @serializeAsColor3(\"reflectivity\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectivityColor = new Color3(1, 1, 1);\r\n\r\n    /**\r\n     * The color reflected from the material.\r\n     */\r\n    @serializeAsColor3(\"reflection\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public reflectionColor = new Color3(1.0, 1.0, 1.0);\r\n\r\n    /**\r\n     * The color emitted from the material.\r\n     */\r\n    @serializeAsColor3(\"emissive\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * AKA Glossiness in other nomenclature.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public microSurface = 1.0;\r\n\r\n    /**\r\n     * Index of refraction of the material base layer.\r\n     * https://en.wikipedia.org/wiki/List_of_refractive_indices\r\n     *\r\n     * This does not only impact refraction but also the Base F0 of Dielectric Materials.\r\n     *\r\n     * From dielectric fresnel rules: F0 = square((iorT - iorI) / (iorT + iorI))\r\n     */\r\n    public get indexOfRefraction(): number {\r\n        return this.subSurface.indexOfRefraction;\r\n    }\r\n    public set indexOfRefraction(value: number) {\r\n        this.subSurface.indexOfRefraction = value;\r\n    }\r\n\r\n    /**\r\n     * Controls if refraction needs to be inverted on Y. This could be useful for procedural texture.\r\n     */\r\n    public get invertRefractionY(): boolean {\r\n        return this.subSurface.invertRefractionY;\r\n    }\r\n    public set invertRefractionY(value: boolean) {\r\n        this.subSurface.invertRefractionY = value;\r\n    }\r\n\r\n    /**\r\n     * This parameters will make the material used its opacity to control how much it is refracting against not.\r\n     * Materials half opaque for instance using refraction could benefit from this control.\r\n     */\r\n    public get linkRefractionWithTransparency(): boolean {\r\n        return this.subSurface.linkRefractionWithTransparency;\r\n    }\r\n    public set linkRefractionWithTransparency(value: boolean) {\r\n        this.subSurface.linkRefractionWithTransparency = value;\r\n        if (value) {\r\n            this.subSurface.isRefractionEnabled = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * If true, the light map contains occlusion information instead of lighting info.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useLightmapAsShadowmap = false;\r\n\r\n    /**\r\n     * Specifies that the alpha is coming form the albedo channel alpha channel for alpha blending.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public useAlphaFromAlbedoTexture = false;\r\n\r\n    /**\r\n     * Enforces alpha test in opaque or blend mode in order to improve the performances of some situations.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public forceAlphaTest = false;\r\n\r\n    /**\r\n     * Defines the alpha limits in alpha test mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesAndMiscDirty\")\r\n    public alphaCutOff = 0.4;\r\n\r\n    /**\r\n     * Specifies that the material will keep the specular highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When sun reflects on it you can not see what is behind.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useSpecularOverAlpha = true;\r\n\r\n    /**\r\n     * Specifies if the reflectivity texture contains the glossiness information in its alpha channel.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useMicroSurfaceFromReflectivityMapAlpha = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the roughness information in its alpha channel.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRoughnessFromMetallicTextureAlpha = true;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the roughness information in its green channel.\r\n     * Needs useRoughnessFromMetallicTextureAlpha to be false.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRoughnessFromMetallicTextureGreen = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the metallness information in its blue channel.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useMetallnessFromMetallicTextureBlue = false;\r\n\r\n    /**\r\n     * Specifies if the metallic texture contains the ambient occlusion information in its red channel.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useAmbientOcclusionFromMetallicTextureRed = false;\r\n\r\n    /**\r\n     * Specifies if the ambient texture contains the ambient occlusion information in its red channel only.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useAmbientInGrayScale = false;\r\n\r\n    /**\r\n     * In case the reflectivity map does not contain the microsurface information in its alpha channel,\r\n     * The material will try to infer what glossiness each pixel should be.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useAutoMicroSurfaceFromReflectivityMap = false;\r\n\r\n    /**\r\n     * BJS is using an hardcoded light falloff based on a manually sets up range.\r\n     * In PBR, one way to represents the falloff is to use the inverse squared root algorithm.\r\n     * This parameter can help you switch back to the BJS mode in order to create scenes using both materials.\r\n     */\r\n    @serialize()\r\n    public get usePhysicalLightFalloff(): boolean {\r\n        return this._lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_PHYSICAL;\r\n    }\r\n\r\n    /**\r\n     * BJS is using an hardcoded light falloff based on a manually sets up range.\r\n     * In PBR, one way to represents the falloff is to use the inverse squared root algorithm.\r\n     * This parameter can help you switch back to the BJS mode in order to create scenes using both materials.\r\n     */\r\n    public set usePhysicalLightFalloff(value: boolean) {\r\n        if (value !== this.usePhysicalLightFalloff) {\r\n            // Ensure the effect will be rebuilt.\r\n            this._markAllSubMeshesAsTexturesDirty();\r\n\r\n            if (value) {\r\n                this._lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_PHYSICAL;\r\n            } else {\r\n                this._lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_STANDARD;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * In order to support the falloff compatibility with gltf, a special mode has been added\r\n     * to reproduce the gltf light falloff.\r\n     */\r\n    @serialize()\r\n    public get useGLTFLightFalloff(): boolean {\r\n        return this._lightFalloff === PBRBaseMaterial.LIGHTFALLOFF_GLTF;\r\n    }\r\n\r\n    /**\r\n     * In order to support the falloff compatibility with gltf, a special mode has been added\r\n     * to reproduce the gltf light falloff.\r\n     */\r\n    public set useGLTFLightFalloff(value: boolean) {\r\n        if (value !== this.useGLTFLightFalloff) {\r\n            // Ensure the effect will be rebuilt.\r\n            this._markAllSubMeshesAsTexturesDirty();\r\n\r\n            if (value) {\r\n                this._lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_GLTF;\r\n            } else {\r\n                this._lightFalloff = PBRBaseMaterial.LIGHTFALLOFF_STANDARD;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Specifies that the material will keeps the reflection highlights over a transparent surface (only the most luminous ones).\r\n     * A car glass is a good example of that. When the street lights reflects on it you can not see what is behind.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRadianceOverAlpha = true;\r\n\r\n    /**\r\n     * Allows using an object space normal map (instead of tangent space).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useObjectSpaceNormalMap = false;\r\n\r\n    /**\r\n     * Allows using the bump map in parallax mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useParallax = false;\r\n\r\n    /**\r\n     * Allows using the bump map in parallax occlusion mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useParallaxOcclusion = false;\r\n\r\n    /**\r\n     * Controls the scale bias of the parallax mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public parallaxScaleBias = 0.05;\r\n\r\n    /**\r\n     * If sets to true, disables all the lights affecting the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public disableLighting = false;\r\n\r\n    /**\r\n     * Force the shader to compute irradiance in the fragment shader in order to take bump in account.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public forceIrradianceInFragment = false;\r\n\r\n    /**\r\n     * Number of Simultaneous lights allowed on the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public maxSimultaneousLights = 4;\r\n\r\n    /**\r\n     * If sets to true, x component of normal map value will invert (x = 1.0 - x).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapX = false;\r\n\r\n    /**\r\n     * If sets to true, y component of normal map value will invert (y = 1.0 - y).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapY = false;\r\n\r\n    /**\r\n     * If sets to true and backfaceCulling is false, normals will be flipped on the backside.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public twoSidedLighting = false;\r\n\r\n    /**\r\n     * A fresnel is applied to the alpha of the model to ensure grazing angles edges are not alpha tested.\r\n     * And/Or occlude the blended part. (alpha is converted to gamma to compute the fresnel)\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useAlphaFresnel = false;\r\n\r\n    /**\r\n     * A fresnel is applied to the alpha of the model to ensure grazing angles edges are not alpha tested.\r\n     * And/Or occlude the blended part. (alpha stays linear to compute the fresnel)\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useLinearAlphaFresnel = false;\r\n\r\n    /**\r\n     * Let user defines the brdf lookup texture used for IBL.\r\n     * A default 8bit version is embedded but you could point at :\r\n     * * Default texture: https://assets.babylonjs.com/environments/correlatedMSBRDF_RGBD.png\r\n     * * Default 16bit pixel depth texture: https://assets.babylonjs.com/environments/correlatedMSBRDF.dds\r\n     * * LEGACY Default None correlated https://assets.babylonjs.com/environments/uncorrelatedBRDF_RGBD.png\r\n     * * LEGACY Default None correlated 16bit pixel depth https://assets.babylonjs.com/environments/uncorrelatedBRDF.dds\r\n     */\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public environmentBRDFTexture: Nullable<BaseTexture> = null;\r\n\r\n    /**\r\n     * Force normal to face away from face.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public forceNormalForward = false;\r\n\r\n    /**\r\n     * Enables specular anti aliasing in the PBR shader.\r\n     * It will both interacts on the Geometry for analytical and IBL lighting.\r\n     * It also prefilter the roughness map based on the bump values.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public enableSpecularAntiAliasing = false;\r\n\r\n    /**\r\n     * This parameters will enable/disable Horizon occlusion to prevent normal maps to look shiny when the normal\r\n     * makes the reflect vector face the model (under horizon).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useHorizonOcclusion = true;\r\n\r\n    /**\r\n     * This parameters will enable/disable radiance occlusion by preventing the radiance to lit\r\n     * too much the area relying on ambient texture to define their ambient occlusion.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useRadianceOcclusion = true;\r\n\r\n    /**\r\n     * If set to true, no lighting calculations will be applied.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public unlit = false;\r\n\r\n    /**\r\n     * If sets to true, the decal map will be applied after the detail map. Else, it is applied before (default: false)\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsMiscDirty\")\r\n    public applyDecalMapAfterDetailMap = false;\r\n\r\n    /**\r\n     * Gets the image processing configuration used either in this material.\r\n     */\r\n    public get imageProcessingConfiguration(): ImageProcessingConfiguration {\r\n        return this._imageProcessingConfiguration;\r\n    }\r\n\r\n    /**\r\n     * Sets the Default image processing configuration used either in the this material.\r\n     *\r\n     * If sets to null, the scene one is in use.\r\n     */\r\n    public set imageProcessingConfiguration(value: ImageProcessingConfiguration) {\r\n        this._attachImageProcessingConfiguration(value);\r\n\r\n        // Ensure the effect will be rebuilt.\r\n        this._markAllSubMeshesAsImageProcessingDirty();\r\n    }\r\n\r\n    /**\r\n     * Gets whether the color curves effect is enabled.\r\n     */\r\n    public get cameraColorCurvesEnabled(): boolean {\r\n        return this.imageProcessingConfiguration.colorCurvesEnabled;\r\n    }\r\n    /**\r\n     * Sets whether the color curves effect is enabled.\r\n     */\r\n    public set cameraColorCurvesEnabled(value: boolean) {\r\n        this.imageProcessingConfiguration.colorCurvesEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Gets whether the color grading effect is enabled.\r\n     */\r\n    public get cameraColorGradingEnabled(): boolean {\r\n        return this.imageProcessingConfiguration.colorGradingEnabled;\r\n    }\r\n    /**\r\n     * Gets whether the color grading effect is enabled.\r\n     */\r\n    public set cameraColorGradingEnabled(value: boolean) {\r\n        this.imageProcessingConfiguration.colorGradingEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Gets whether tonemapping is enabled or not.\r\n     */\r\n    public get cameraToneMappingEnabled(): boolean {\r\n        return this._imageProcessingConfiguration.toneMappingEnabled;\r\n    }\r\n    /**\r\n     * Sets whether tonemapping is enabled or not\r\n     */\r\n    public set cameraToneMappingEnabled(value: boolean) {\r\n        this._imageProcessingConfiguration.toneMappingEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * The camera exposure used on this material.\r\n     * This property is here and not in the camera to allow controlling exposure without full screen post process.\r\n     * This corresponds to a photographic exposure.\r\n     */\r\n    public get cameraExposure(): number {\r\n        return this._imageProcessingConfiguration.exposure;\r\n    }\r\n    /**\r\n     * The camera exposure used on this material.\r\n     * This property is here and not in the camera to allow controlling exposure without full screen post process.\r\n     * This corresponds to a photographic exposure.\r\n     */\r\n    public set cameraExposure(value: number) {\r\n        this._imageProcessingConfiguration.exposure = value;\r\n    }\r\n\r\n    /**\r\n     * Gets The camera contrast used on this material.\r\n     */\r\n    public get cameraContrast(): number {\r\n        return this._imageProcessingConfiguration.contrast;\r\n    }\r\n\r\n    /**\r\n     * Sets The camera contrast used on this material.\r\n     */\r\n    public set cameraContrast(value: number) {\r\n        this._imageProcessingConfiguration.contrast = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the Color Grading 2D Lookup Texture.\r\n     */\r\n    public get cameraColorGradingTexture(): Nullable<BaseTexture> {\r\n        return this._imageProcessingConfiguration.colorGradingTexture;\r\n    }\r\n    /**\r\n     * Sets the Color Grading 2D Lookup Texture.\r\n     */\r\n    public set cameraColorGradingTexture(value: Nullable<BaseTexture>) {\r\n        this._imageProcessingConfiguration.colorGradingTexture = value;\r\n    }\r\n\r\n    /**\r\n     * The color grading curves provide additional color adjustment that is applied after any color grading transform (3D LUT).\r\n     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.\r\n     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;\r\n     * corresponding to low luminance, medium luminance, and high luminance areas respectively.\r\n     */\r\n    public get cameraColorCurves(): Nullable<ColorCurves> {\r\n        return this._imageProcessingConfiguration.colorCurves;\r\n    }\r\n    /**\r\n     * The color grading curves provide additional color adjustment that is applied after any color grading transform (3D LUT).\r\n     * They allow basic adjustment of saturation and small exposure adjustments, along with color filter tinting to provide white balance adjustment or more stylistic effects.\r\n     * These are similar to controls found in many professional imaging or colorist software. The global controls are applied to the entire image. For advanced tuning, extra controls are provided to adjust the shadow, midtone and highlight areas of the image;\r\n     * corresponding to low luminance, medium luminance, and high luminance areas respectively.\r\n     */\r\n    public set cameraColorCurves(value: Nullable<ColorCurves>) {\r\n        this._imageProcessingConfiguration.colorCurves = value;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new PBRMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     * @param forceGLSL Use the GLSL code generation for the shader (even on WebGPU). Default is false\r\n     */\r\n    constructor(name: string, scene?: Scene, forceGLSL = false) {\r\n        super(name, scene, forceGLSL);\r\n\r\n        this._environmentBRDFTexture = GetEnvironmentBRDFTexture(this.getScene());\r\n    }\r\n\r\n    /**\r\n     * @returns the name of this material class.\r\n     */\r\n    public override getClassName(): string {\r\n        return \"PBRMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current material.\r\n     * @param name - name to use for the new material.\r\n     * @param cloneTexturesOnlyOnce - if a texture is used in more than one channel (e.g diffuse and opacity), only clone it once and reuse it on the other channels. Default false.\r\n     * @param rootUrl defines the root URL to use to load textures\r\n     * @returns cloned material instance\r\n     */\r\n    public override clone(name: string, cloneTexturesOnlyOnce: boolean = true, rootUrl = \"\"): PBRMaterial {\r\n        const clone = SerializationHelper.Clone(() => new PBRMaterial(name, this.getScene()), this, { cloneTexturesOnlyOnce });\r\n\r\n        clone.id = name;\r\n        clone.name = name;\r\n\r\n        this.stencil.copyTo(clone.stencil);\r\n\r\n        this._clonePlugins(clone, rootUrl);\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Serializes this PBR Material.\r\n     * @returns - An object with the serialized material.\r\n     */\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n        serializationObject.customType = \"BABYLON.PBRMaterial\";\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Parses a PBR Material from a serialized object.\r\n     * @param source - Serialized object.\r\n     * @param scene - BJS scene instance.\r\n     * @param rootUrl - url for the scene object\r\n     * @returns - PBRMaterial\r\n     */\r\n    public static override Parse(source: any, scene: Scene, rootUrl: string): PBRMaterial {\r\n        const material = SerializationHelper.Parse(() => new PBRMaterial(source.name, scene), source, scene, rootUrl);\r\n\r\n        if (source.stencil) {\r\n            material.stencil.parse(source.stencil, scene, rootUrl);\r\n        }\r\n\r\n        Material._ParsePlugins(source, material, scene, rootUrl);\r\n\r\n        // The code block below ensures backward compatibility with serialized materials before plugins are automatically serialized.\r\n        if (source.clearCoat) {\r\n            material.clearCoat.parse(source.clearCoat, scene, rootUrl);\r\n        }\r\n        if (source.anisotropy) {\r\n            material.anisotropy.parse(source.anisotropy, scene, rootUrl);\r\n        }\r\n        if (source.brdf) {\r\n            material.brdf.parse(source.brdf, scene, rootUrl);\r\n        }\r\n        if (source.sheen) {\r\n            material.sheen.parse(source.sheen, scene, rootUrl);\r\n        }\r\n        if (source.subSurface) {\r\n            material.subSurface.parse(source.subSurface, scene, rootUrl);\r\n        }\r\n        if (source.iridescence) {\r\n            material.iridescence.parse(source.iridescence, scene, rootUrl);\r\n        }\r\n\r\n        return material;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRMaterial\", PBRMaterial);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3G,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAGxE,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAIhD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AACrD,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AACvC,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;;;;;;;;;AASpE,MAAO,WAAY,oLAAQ,kBAAe;IA6O5C;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;IAC7C,CAAC;IACD,IAAW,iBAAiB,CAAC,KAA4B,EAAA;QACrD,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC1C,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC/C,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,8BAA8B,EAAE,CAAC;YACzD,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,KAAK,CAAC;QAChD,CAAC;IACL,CAAC;IA0DD;;;;;;;OAOG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;IAC7C,CAAC;IACD,IAAW,iBAAiB,CAAC,KAAa,EAAA;QACtC,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;IAC7C,CAAC;IACD,IAAW,iBAAiB,CAAC,KAAc,EAAA;QACvC,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED;;;OAGG,CACH,IAAW,8BAA8B,GAAA;QACrC,OAAO,IAAI,CAAC,UAAU,CAAC,8BAA8B,CAAC;IAC1D,CAAC;IACD,IAAW,8BAA8B,CAAC,KAAc,EAAA;QACpD,IAAI,CAAC,UAAU,CAAC,8BAA8B,GAAG,KAAK,CAAC;QACvD,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,UAAU,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC/C,CAAC;IACL,CAAC;IAyFD;;;;OAIG,CAEH,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,aAAa,gLAAK,kBAAe,CAAC,qBAAqB,CAAC;IACxE,CAAC;IAED;;;;OAIG,CACH,IAAW,uBAAuB,CAAC,KAAc,EAAA;QAC7C,IAAI,KAAK,KAAK,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACzC,qCAAqC;YACrC,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAExC,IAAI,KAAK,EAAE,CAAC;gBACR,IAAI,CAAC,aAAa,8KAAG,kBAAe,CAAC,qBAAqB,CAAC;YAC/D,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,8KAAG,kBAAe,CAAC,qBAAqB,CAAC;YAC/D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CAEH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,aAAa,gLAAK,kBAAe,CAAC,iBAAiB,CAAC;IACpE,CAAC;IAED;;;OAGG,CACH,IAAW,mBAAmB,CAAC,KAAc,EAAA;QACzC,IAAI,KAAK,KAAK,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACrC,qCAAqC;YACrC,IAAI,CAAC,gCAAgC,EAAE,CAAC;YAExC,IAAI,KAAK,EAAE,CAAC;gBACR,IAAI,CAAC,aAAa,8KAAG,kBAAe,CAAC,iBAAiB,CAAC;YAC3D,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,8KAAG,kBAAe,CAAC,qBAAqB,CAAC;YAC/D,CAAC;QACL,CAAC;IACL,CAAC;IAyJD;;OAEG,CACH,IAAW,4BAA4B,GAAA;QACnC,OAAO,IAAI,CAAC,6BAA6B,CAAC;IAC9C,CAAC;IAED;;;;OAIG,CACH,IAAW,4BAA4B,CAAC,KAAmC,EAAA;QACvE,IAAI,CAAC,mCAAmC,CAAC,KAAK,CAAC,CAAC;QAEhD,qCAAqC;QACrC,IAAI,CAAC,uCAAuC,EAAE,CAAC;IACnD,CAAC;IAED;;OAEG,CACH,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,CAAC;IAChE,CAAC;IACD;;OAEG,CACH,IAAW,wBAAwB,CAAC,KAAc,EAAA;QAC9C,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,CAAC;IACjE,CAAC;IACD;;OAEG,CACH,IAAW,yBAAyB,CAAC,KAAc,EAAA;QAC/C,IAAI,CAAC,4BAA4B,CAAC,mBAAmB,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;OAEG,CACH,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,CAAC;IACjE,CAAC;IACD;;OAEG,CACH,IAAW,wBAAwB,CAAC,KAAc,EAAA;QAC9C,IAAI,CAAC,6BAA6B,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;;;OAIG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC;IACvD,CAAC;IACD;;;;OAIG,CACH,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,6BAA6B,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxD,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,6BAA6B,CAAC,QAAQ,CAAC;IACvD,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,6BAA6B,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxD,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,CAAC;IAClE,CAAC;IACD;;OAEG,CACH,IAAW,yBAAyB,CAAC,KAA4B,EAAA;QAC7D,IAAI,CAAC,6BAA6B,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACnE,CAAC;IAED;;;;;OAKG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,6BAA6B,CAAC,WAAW,CAAC;IAC1D,CAAC;IACD;;;;;OAKG,CACH,IAAW,iBAAiB,CAAC,KAA4B,EAAA;QACrD,IAAI,CAAC,6BAA6B,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3D,CAAC;IAED;;;;;;OAMG,CACH,YAAY,IAAY,EAAE,KAAa,EAAE,SAAS,GAAG,KAAK,CAAA;QACtD,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QApuBlC;;;WAGG,CAGI,IAAA,CAAA,eAAe,GAAW,GAAG,CAAC;QAErC;;;WAGG,CAGI,IAAA,CAAA,iBAAiB,GAAW,GAAG,CAAC;QAEvC;;;WAGG,CAGI,IAAA,CAAA,oBAAoB,GAAW,GAAG,CAAC;QAE1C;;;WAGG,CAGI,IAAA,CAAA,iBAAiB,GAAW,GAAG,CAAC;QAEvC;;WAEG,CAGI,IAAA,CAAA,cAAc,GAAY,KAAK,CAAC;QA8BvC;;WAEG,CAGI,IAAA,CAAA,sBAAsB,GAAW,GAAG,CAAC;QAE5C;;;;WAIG,CAGI,IAAA,CAAA,sCAAsC,GAAW,WAAW,CAAC,+BAA+B,CAAC;QAqDpG;;;;;;;;WAQG,CAGI,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;QAE5B;;;;;;;;WAQG,CAGI,IAAA,CAAA,wBAAwB,iKAAG,SAAM,CAAC,KAAK,EAAE,CAAC;QAEjD;;;WAGG,CAGI,IAAA,CAAA,6CAA6C,GAAG,KAAK,CAAC;QA0D7D;;WAEG,CAGI,IAAA,CAAA,YAAY,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C;;WAEG,CAGI,IAAA,CAAA,WAAW,GAAG,IAAI,uKAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzC;;WAEG,CAGI,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QAStB;;WAEG,CAGI,IAAA,CAAA,iBAAiB,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE/C;;WAEG,CAGI,IAAA,CAAA,eAAe,GAAG,kKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAEnD;;WAEG,CAGI,IAAA,CAAA,aAAa,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C;;WAEG,CAGI,IAAA,CAAA,YAAY,GAAG,GAAG,CAAC;QAyC1B;;WAEG,CAGI,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG,CAGI,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAEzC;;WAEG,CAGI,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAE9B;;WAEG,CAGI,IAAA,CAAA,WAAW,GAAG,GAAG,CAAC;QAEzB;;;WAGG,CAGI,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG,CAGI,IAAA,CAAA,uCAAuC,GAAG,KAAK,CAAC;QAEvD;;WAEG,CAGI,IAAA,CAAA,oCAAoC,GAAG,IAAI,CAAC;QAEnD;;;WAGG,CAGI,IAAA,CAAA,oCAAoC,GAAG,KAAK,CAAC;QAEpD;;WAEG,CAGI,IAAA,CAAA,oCAAoC,GAAG,KAAK,CAAC;QAEpD;;WAEG,CAGI,IAAA,CAAA,yCAAyC,GAAG,KAAK,CAAC;QAEzD;;WAEG,CAGI,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAErC;;;WAGG,CAGI,IAAA,CAAA,sCAAsC,GAAG,KAAK,CAAC;QAwDtD;;;WAGG,CAGI,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG,CAGI,IAAA,CAAA,uBAAuB,GAAG,KAAK,CAAC;QAEvC;;WAEG,CAGI,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QAE3B;;WAEG,CAGI,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAEpC;;WAEG,CAGI,IAAA,CAAA,iBAAiB,GAAG,IAAI,CAAC;QAEhC;;WAEG,CAGI,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAE/B;;WAEG,CAGI,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAEzC;;WAEG,CAGI,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC;QAEjC;;WAEG,CAGI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG,CAGI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG,CAGI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEhC;;;WAGG,CAGI,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAE/B;;;WAGG,CAGI,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAErC;;;;;;;WAOG,CAEI,IAAA,CAAA,sBAAsB,GAA0B,IAAI,CAAC;QAE5D;;WAEG,CAGI,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAElC;;;;WAIG,CAGI,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QAE1C;;;WAGG,CAGI,IAAA,CAAA,mBAAmB,GAAG,IAAI,CAAC;QAElC;;;WAGG,CAGI,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG,CAGI,IAAA,CAAA,KAAK,GAAG,KAAK,CAAC;QAErB;;WAEG,CAGI,IAAA,CAAA,2BAA2B,GAAG,KAAK,CAAC;QAqIvC,IAAI,CAAC,uBAAuB,uKAAG,4BAAA,AAAyB,EAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG,CACa,YAAY,GAAA;QACxB,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;;;OAMG,CACa,KAAK,CAAC,IAAY,EAAE,wBAAiC,IAAI,EAAE,OAAO,GAAG,EAAE,EAAA;QACnF,MAAM,KAAK,8KAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE;YAAE,qBAAqB;QAAA,CAAE,CAAC,CAAC;QAEvH,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC;QAChB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAEnC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACa,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,UAAU,GAAG,qBAAqB,CAAC;QAEvD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED,UAAU;IACV;;;;;;OAMG,CACI,MAAM,CAAU,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe,EAAA;QACnE,MAAM,QAAQ,8KAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAE9G,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC3D,CAAC;qKAED,WAAQ,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAEzD,6HAA6H;QAC7H,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC/D,CAAC;QACD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YACd,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;;AAh1BD;;GAEG,CAC6B,YAAA,kBAAkB,8KAAG,kBAAe,CAAC,kBAAnB,CAAsC;AAExF;;GAEG,CAC6B,YAAA,qBAAqB,GAAG,6LAAe,CAAC,qBAAnB,CAAyC;AAE9F;;GAEG,CAC6B,YAAA,sBAAsB,8KAAG,kBAAe,CAAC,sBAAnB,CAA0C;AAEhG;;;GAGG,CAC6B,YAAA,6BAA6B,8KAAG,kBAAe,CAAC,6BAAnB,CAAiD;AAE9G;;;GAGG,CACoB,YAAA,+BAA+B,8KAAG,kBAAe,CAAC,+BAAnB,CAAmD;wJAQlG,aAAA,EAAA;kKAFN,YAAS,AAAT,EAAW;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;oDAChB;wJAQ9B,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;sDACd;wJAQhC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;yDACX;wJAQnC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;sDACd;wJAOhC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;iKACX,oBAAA,AAAgB,EAAC,kCAAkC,CAAC;mDACd;wJAOhC,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;QACpB,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;kDACT;wJAOrC,aAAA,EAAA;IAFN,mLAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;sDACL;wJAOzC,aAAA,EAAA;IAFN,mLAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;gEACK;CAOnD,oKAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;QACpB,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;mDACR;wJAOtC,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAE;kKACX,mBAAgB,AAAhB,EAAiB,kCAAkC,CAAC;2DACT;wJASrC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAgB,AAAhB,EAAiB,kCAAkC,CAAC;2EAC+C;wJAO7F,aAAA,EAAA;QAFN,+KAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,yCAAyC,CAAC;mDACf;wJAOtC,aAAA,EAAA;KAFN,kLAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;sDACL;AAOzC,qKAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;oDACP;wJAOvC,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;QACpB,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;wDACH;IAO3C,iKAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;oDACP;wJAQvC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;KACX,gLAAA,AAAgB,EAAC,kCAAkC,CAAC;6CACnB;wJAQ3B,aAAA,EAAA;KAFN,yKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8CAClB;wJAa5B,aAAA,EAAA;kKAFN,YAAS,AAAT,EAAW;IACX,iLAAgB,AAAhB,EAAiB,kCAAkC,CAAC;qDACzB;IAarB,iKAAA,EAAA;kKAFN,oBAAA,AAAiB,EAAE;kKACnB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;6DACJ;wJAQ1C,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;kFACQ;AAStD,qKAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;+DACI;wJAUlD,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;uDACJ;wJAQ1C,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;iKACpB,oBAAA,AAAgB,EAAC,kCAAkC,CAAC;wDACH;wJAO3C,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;iKACpB,oBAAA,AAAgB,EAAC,kCAAkC,CAAC;gDACX;wJAOnC,aAAA,EAAA;iKAFN,sBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,IAAI,CAAC;oDACb;wJAsBvC,aAAA,EAAA;kKAFN,oBAAA,AAAiB,EAAC,SAAS,CAAC;kKAC5B,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;iDACX;wJAOnC,aAAA,EAAA;iKAFN,qBAAA,AAAiB,EAAC,QAAQ,CAAC;kKAC3B,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;gDACZ;wJAOlC,aAAA,EAAA;KAFN,yKAAA,AAAS,EAAC,YAAY,CAAC;kKACvB,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;+CAC/B;CAOf,oKAAA,EAAA;kKAFN,YAAA,AAAS,EAAC,sBAAsB,CAAC;kKACjC,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;yDACP;wJAOvC,aAAA,EAAA;kKAFN,oBAAA,AAAiB,EAAC,cAAc,CAAC;kKACjC,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;sDACN;AAOxC,qKAAA,EAAA;kKAFN,oBAAA,AAAiB,EAAC,YAAY,CAAC;kKAC/B,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;oDACF;wJAO5C,aAAA,EAAA;kKAFN,oBAAA,AAAiB,EAAC,UAAU,CAAC;QAC7B,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;kDACV;wJAOpC,aAAA,EAAA;iKAFN,aAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;iDAC3B;AA8CnB,qKAAA,EAAA;kKAFN,YAAS,AAAT,EAAW;kKACX,mBAAgB,AAAhB,EAAiB,kCAAkC,CAAC;2DACf;wJAO/B,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;IACX,iLAAA,AAAgB,EAAC,yCAAyC,CAAC;8DACnB;wJAOlC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,yCAAyC,CAAC;mDAC9B;wJAOvB,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,yCAAyC,CAAC;gDACnC;wJAQlB,aAAA,EAAA;iKAFN,aAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;yDAClB;IAO5B,iKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;4EACE;wJAOhD,aAAA,EAAA;kKAFN,YAAS,AAAT,EAAW;IACX,iLAAA,AAAgB,EAAC,kCAAkC,CAAC;yEACF;wJAQ5C,aAAA,EAAA;QAFN,sKAAS,AAAT,EAAW;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;yEACD;AAO7C,qKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAgB,AAAhB,EAAiB,kCAAkC,CAAC;yEACD;wJAO7C,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8EACI;wJAOlD,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;KACX,gLAAgB,AAAhB,EAAiB,kCAAkC,CAAC;0DAChB;wJAQ9B,aAAA,EAAA;QAFN,sKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;2EACC;wJAQtD,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;0DAGX;wJAyBD,aAAA,EAAA;KADC,yKAAA,AAAS,EAAE;sDAGX;wJAyBM,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;yDAClB;wJAO5B,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;iKACX,oBAAA,AAAgB,EAAC,kCAAkC,CAAC;4DACd;wJAOhC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;gDAC1B;wJAOpB,aAAA,EAAA;QAFN,sKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;yDACjB;uJAO7B,cAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;sDACrB;wJAOzB,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;IACX,iLAAgB,AAAhB,EAAiB,gCAAgC,CAAC;oDACpB;AAOxB,qKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8DACZ;wJAOlC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;IACX,iLAAA,AAAgB,EAAC,gCAAgC,CAAC;0DAClB;wJAO1B,aAAA,EAAA;QAFN,sKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;qDACrB;wJAOzB,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;qDACrB;CAOzB,oKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;KACX,gLAAA,AAAgB,EAAC,kCAAkC,CAAC;qDACrB;wJAQzB,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAE;kKACX,mBAAgB,AAAhB,EAAiB,kCAAkC,CAAC;oDACtB;wJAQxB,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;0DAChB;IAW9B,iKAAA,EAAA;kKADN,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;2DACO;CAOrD,oKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;uDACnB;wJAS3B,aAAA,EAAA;KAFN,yKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;+DACX;wJAQnC,aAAA,EAAA;KAFN,yKAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;wDACnB;wJAQ3B,aAAA,EAAA;kKAFN,YAAS,AAAT,EAAW;KACX,gLAAA,AAAgB,EAAC,kCAAkC,CAAC;yDAClB;wJAO5B,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;IACX,iLAAA,AAAgB,EAAC,8BAA8B,CAAC;0CAC5B;wJAOd,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,8BAA8B,CAAC;gEACN;6JAuN/C,gBAAA,AAAa,EAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 5208, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrBaseSimpleMaterial.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrBaseSimpleMaterial.ts"], "sourcesContent": ["import { serialize, serializeAsColor3, expandToProperty, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Color3 } from \"../../Maths/math.color\";\r\nimport { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * The Physically based simple base material of BJS.\r\n *\r\n * This enables better naming and convention enforcements on top of the pbrMaterial.\r\n * It is used as the base class for both the specGloss and metalRough conventions.\r\n */\r\nexport abstract class PBRBaseSimpleMaterial extends PBRBaseMaterial {\r\n    /**\r\n     * Number of Simultaneous lights allowed on the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public maxSimultaneousLights = 4;\r\n\r\n    /**\r\n     * If sets to true, disables all the lights affecting the material.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsLightsDirty\")\r\n    public disableLighting = false;\r\n\r\n    /**\r\n     * Environment Texture used in the material (this is use for both reflection and environment lighting).\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_reflectionTexture\")\r\n    public environmentTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * If sets to true, x component of normal map value will invert (x = 1.0 - x).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapX = false;\r\n\r\n    /**\r\n     * If sets to true, y component of normal map value will invert (y = 1.0 - y).\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public invertNormalMapY = false;\r\n\r\n    /**\r\n     * Normal map used in the model.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_bumpTexture\")\r\n    public normalTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Emissivie color used to self-illuminate the model.\r\n     */\r\n    @serializeAsColor3(\"emissive\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveColor = new Color3(0, 0, 0);\r\n\r\n    /**\r\n     * Emissivie texture used to self-illuminate the model.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public emissiveTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Occlusion Channel Strength.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_ambientTextureStrength\")\r\n    public occlusionStrength: number = 1.0;\r\n\r\n    /**\r\n     * Occlusion Texture of the material (adding extra occlusion effects).\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_ambientTexture\")\r\n    public occlusionTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Defines the alpha limits in alpha test mode.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_alphaCutOff\")\r\n    public alphaCutOff: number;\r\n\r\n    /**\r\n     * Gets the current double sided mode.\r\n     */\r\n    @serialize()\r\n    public get doubleSided(): boolean {\r\n        return this._twoSidedLighting;\r\n    }\r\n    /**\r\n     * If sets to true and backfaceCulling is false, normals will be flipped on the backside.\r\n     */\r\n    public set doubleSided(value: boolean) {\r\n        if (this._twoSidedLighting === value) {\r\n            return;\r\n        }\r\n        this._twoSidedLighting = value;\r\n        this.backFaceCulling = !value;\r\n        this._markAllSubMeshesAsTexturesDirty();\r\n    }\r\n\r\n    /**\r\n     * Stores the pre-calculated light information of a mesh in a texture.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", null)\r\n    public lightmapTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * If true, the light map contains occlusion information instead of lighting info.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public useLightmapAsShadowmap = false;\r\n\r\n    /**\r\n     * Instantiates a new PBRMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n\r\n        this._useAlphaFromAlbedoTexture = true;\r\n        this._useAmbientInGrayScale = true;\r\n    }\r\n\r\n    public override getClassName(): string {\r\n        return \"PBRBaseSimpleMaterial\";\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE3G,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAChD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;;;;;AAU9C,MAAgB,qBAAsB,oLAAQ,kBAAe;IA8E/D;;OAEG,CAEH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IACD;;OAEG,CACH,IAAW,WAAW,CAAC,KAAc,EAAA;QACjC,IAAI,IAAI,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YACnC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,CAAC,KAAK,CAAC;QAC9B,IAAI,CAAC,gCAAgC,EAAE,CAAC;IAC5C,CAAC;IAgBD;;;;;OAKG,CACH,YAAY,IAAY,EAAE,KAAa,CAAA;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QArHvB;;WAEG,CAGI,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC;QAEjC;;WAEG,CAGI,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAS/B;;WAEG,CAGI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG,CAGI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAShC;;WAEG,CAGI,IAAA,CAAA,aAAa,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAS3C;;WAEG,CAGI,IAAA,CAAA,iBAAiB,GAAW,GAAG,CAAC;QA0CvC;;WAEG,CAGI,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAWlC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACvC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;IACvC,CAAC;IAEe,YAAY,GAAA;QACxB,OAAO,uBAAuB,CAAC;IACnC,CAAC;CACJ;CAzHU,oKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;QACX,6KAAA,AAAgB,EAAC,gCAAgC,CAAC;oEAClB;wJAO1B,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,gCAAgC,CAAC;8DACpB;wJAOxB,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;QACpB,6KAAA,AAAgB,EAAC,kCAAkC,EAAE,oBAAoB,CAAC;iEAC1B;IAO1C,iKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;+DACrB;wJAOzB,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;+DACrB;wJAOzB,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,cAAc,CAAC;4DACzB;wJAOrC,aAAA,EAAA;iKAFN,qBAAA,AAAiB,EAAC,UAAU,CAAC;kKAC7B,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;4DACV;IAOpC,iKAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;QACpB,6KAAA,AAAgB,EAAC,kCAAkC,CAAC;8DACP;wJAOvC,aAAA,EAAA;IAFN,0KAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,yBAAyB,CAAC;gEACzC;AAOhC,qKAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,iBAAiB,CAAC;+DACzB;CAOxC,oKAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,cAAc,CAAC;0DAC1C;AAM3B,qKAAA,EAAA;kKADC,YAAA,AAAS,EAAE;wDAGX;wJAkBM,aAAA,EAAA;KAFN,kLAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,IAAI,CAAC;8DACb;wJAOvC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;qEACf", "debugId": null}}, {"offset": {"line": 5329, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrMetallicRoughnessMaterial.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrMetallicRoughnessMaterial.ts"], "sourcesContent": ["import { serialize, serializeAsColor3, expandToProperty, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Color3 } from \"../../Maths/math.color\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { PBRBaseSimpleMaterial } from \"./pbrBaseSimpleMaterial\";\r\nimport { RegisterClass } from \"../../Misc/typeStore\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * The PBR material of BJS following the metal roughness convention.\r\n *\r\n * This fits to the PBR convention in the GLTF definition:\r\n * https://github.com/KhronosGroup/glTF/blob/main/extensions/2.0/Archived/KHR_materials_pbrSpecularGlossiness/README.md\r\n */\r\nexport class PBRMetallicRoughnessMaterial extends PBRBaseSimpleMaterial {\r\n    /**\r\n     * The base color has two different interpretations depending on the value of metalness.\r\n     * When the material is a metal, the base color is the specific measured reflectance value\r\n     * at normal incidence (F0). For a non-metal the base color represents the reflected diffuse color\r\n     * of the material.\r\n     */\r\n    @serializeAsColor3()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_albedoColor\")\r\n    public baseColor: Color3;\r\n\r\n    /**\r\n     * Base texture of the metallic workflow. It contains both the baseColor information in RGB as\r\n     * well as opacity information in the alpha channel.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_albedoTexture\")\r\n    public baseTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Specifies the metallic scalar value of the material.\r\n     * Can also be used to scale the metalness values of the metallic texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public metallic: number;\r\n\r\n    /**\r\n     * Specifies the roughness scalar value of the material.\r\n     * Can also be used to scale the roughness values of the metallic texture.\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\")\r\n    public roughness: number;\r\n\r\n    /**\r\n     * Texture containing both the metallic value in the B channel and the\r\n     * roughness value in the G channel to keep better precision.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_metallicTexture\")\r\n    public metallicRoughnessTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Instantiates a new PBRMetalRoughnessMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n        this._useRoughnessFromMetallicTextureAlpha = false;\r\n        this._useRoughnessFromMetallicTextureGreen = true;\r\n        this._useMetallnessFromMetallicTextureBlue = true;\r\n        this.metallic = 1.0;\r\n        this.roughness = 1.0;\r\n    }\r\n\r\n    /**\r\n     * @returns the current class name of the material.\r\n     */\r\n    public override getClassName(): string {\r\n        return \"PBRMetallicRoughnessMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current material.\r\n     * @param name - name to use for the new material.\r\n     * @returns cloned material instance\r\n     */\r\n    public override clone(name: string): PBRMetallicRoughnessMaterial {\r\n        const clone = SerializationHelper.Clone(() => new PBRMetallicRoughnessMaterial(name, this.getScene()), this);\r\n\r\n        clone.id = name;\r\n        clone.name = name;\r\n\r\n        this.clearCoat.copyTo(clone.clearCoat);\r\n        this.anisotropy.copyTo(clone.anisotropy);\r\n        this.brdf.copyTo(clone.brdf);\r\n        this.sheen.copyTo(clone.sheen);\r\n        this.subSurface.copyTo(clone.subSurface);\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Serialize the material to a parsable JSON object.\r\n     * @returns the JSON object\r\n     */\r\n    public override serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"BABYLON.PBRMetallicRoughnessMaterial\";\r\n\r\n        if (!this.clearCoat.doNotSerialize) {\r\n            serializationObject.clearCoat = this.clearCoat.serialize();\r\n        }\r\n        if (!this.anisotropy.doNotSerialize) {\r\n            serializationObject.anisotropy = this.anisotropy.serialize();\r\n        }\r\n        if (!this.brdf.doNotSerialize) {\r\n            serializationObject.brdf = this.brdf.serialize();\r\n        }\r\n        if (!this.sheen.doNotSerialize) {\r\n            serializationObject.sheen = this.sheen.serialize();\r\n        }\r\n        if (!this.subSurface.doNotSerialize) {\r\n            serializationObject.subSurface = this.subSurface.serialize();\r\n        }\r\n        if (!this.iridescence.doNotSerialize) {\r\n            serializationObject.iridescence = this.iridescence.serialize();\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a JSON object corresponding to the serialize function.\r\n     * @param source - JSON source object.\r\n     * @param scene - Defines the scene we are parsing for\r\n     * @param rootUrl - Defines the rootUrl of this parsed object\r\n     * @returns a new PBRMetalRoughnessMaterial\r\n     */\r\n    public static override Parse(source: any, scene: Scene, rootUrl: string): PBRMetallicRoughnessMaterial {\r\n        const material = SerializationHelper.Parse(() => new PBRMetallicRoughnessMaterial(source.name, scene), source, scene, rootUrl);\r\n        if (source.clearCoat) {\r\n            material.clearCoat.parse(source.clearCoat, scene, rootUrl);\r\n        }\r\n        if (source.anisotropy) {\r\n            material.anisotropy.parse(source.anisotropy, scene, rootUrl);\r\n        }\r\n        if (source.brdf) {\r\n            material.brdf.parse(source.brdf, scene, rootUrl);\r\n        }\r\n        if (source.sheen) {\r\n            material.sheen.parse(source.sheen, scene, rootUrl);\r\n        }\r\n        if (source.subSurface) {\r\n            material.subSurface.parse(source.subSurface, scene, rootUrl);\r\n        }\r\n        if (source.iridescence) {\r\n            material.iridescence.parse(source.iridescence, scene, rootUrl);\r\n        }\r\n        return material;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRMetallicRoughnessMaterial\", PBRMetallicRoughnessMaterial);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3G,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;AAI1E,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;;;;;;AAS/C,MAAO,4BAA6B,0LAAQ,wBAAqB;IA2CnE;;;;;OAKG,CACH,YAAY,IAAY,EAAE,KAAa,CAAA;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,qCAAqC,GAAG,KAAK,CAAC;QACnD,IAAI,CAAC,qCAAqC,GAAG,IAAI,CAAC;QAClD,IAAI,CAAC,qCAAqC,GAAG,IAAI,CAAC;QAClD,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;IACzB,CAAC;IAED;;OAEG,CACa,YAAY,GAAA;QACxB,OAAO,8BAA8B,CAAC;IAC1C,CAAC;IAED;;;;OAIG,CACa,KAAK,CAAC,IAAY,EAAA;QAC9B,MAAM,KAAK,8KAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAE7G,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC;QAChB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEzC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACa,SAAS,GAAA;QACrB,MAAM,mBAAmB,8KAAG,sBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,sCAAsC,CAAC;QAExE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YACjC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC/D,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YAClC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QACjE,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5B,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACrD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC7B,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACvD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YAClC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QACjE,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YACnC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;QACnE,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAU,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe,EAAA;QACnE,MAAM,QAAQ,8KAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,4BAA4B,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC/H,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC/D,CAAC;QACD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YACd,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;wJAvIU,aAAA,EAAA;kKAFN,oBAAA,AAAiB,EAAE;IACnB,iLAAA,AAAgB,EAAC,kCAAkC,EAAE,cAAc,CAAC;+DAC5C;wJAQlB,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;KACpB,gLAAA,AAAgB,EAAC,kCAAkC,EAAE,gBAAgB,CAAC;iEAC7B;wJAQnC,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,CAAC;8DAC7B;wJAQjB,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;KACX,gLAAA,AAAgB,EAAC,kCAAkC,CAAC;+DAC5B;wJAQlB,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,kBAAkB,CAAC;8EAClB;6JAyG3D,gBAAA,AAAa,EAAC,sCAAsC,EAAE,4BAA4B,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 5456, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrSpecularGlossinessMaterial.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrSpecularGlossinessMaterial.ts"], "sourcesContent": ["import { serialize, serializeAsColor3, expandToProperty, serializeAsTexture } from \"../../Misc/decorators\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Color3 } from \"../../Maths/math.color\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { PBRBaseSimpleMaterial } from \"./pbrBaseSimpleMaterial\";\r\nimport { RegisterClass } from \"../../Misc/typeStore\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { SerializationHelper } from \"../../Misc/decorators.serialization\";\r\n\r\n/**\r\n * The PBR material of BJS following the specular glossiness convention.\r\n *\r\n * This fits to the PBR convention in the GLTF definition:\r\n * https://github.com/KhronosGroup/glTF/tree/2.0/extensions/Khronos/KHR_materials_pbrSpecularGlossiness\r\n */\r\nexport class PBRSpecularGlossinessMaterial extends PBRBaseSimpleMaterial {\r\n    /**\r\n     * Specifies the diffuse color of the material.\r\n     */\r\n    @serializeAsColor3(\"diffuse\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_albedoColor\")\r\n    public diffuseColor: Color3;\r\n\r\n    /**\r\n     * Specifies the diffuse texture of the material. This can also contains the opacity value in its alpha\r\n     * channel.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_albedoTexture\")\r\n    public diffuseTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Specifies the specular color of the material. This indicates how reflective is the material (none to mirror).\r\n     */\r\n    @serializeAsColor3(\"specular\")\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_reflectivityColor\")\r\n    public specularColor: Color3;\r\n\r\n    /**\r\n     * Specifies the glossiness of the material. This indicates \"how sharp is the reflection\".\r\n     */\r\n    @serialize()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_microSurface\")\r\n    public glossiness: number;\r\n\r\n    /**\r\n     * Specifies both the specular color RGB and the glossiness A of the material per pixels.\r\n     */\r\n    @serializeAsTexture()\r\n    @expandToProperty(\"_markAllSubMeshesAsTexturesDirty\", \"_reflectivityTexture\")\r\n    public specularGlossinessTexture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Specifies if the reflectivity texture contains the glossiness information in its alpha channel.\r\n     */\r\n    public get useMicroSurfaceFromReflectivityMapAlpha() {\r\n        return this._useMicroSurfaceFromReflectivityMapAlpha;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new PBRSpecularGlossinessMaterial instance.\r\n     *\r\n     * @param name The material name\r\n     * @param scene The scene the material will be use in.\r\n     */\r\n    constructor(name: string, scene?: Scene) {\r\n        super(name, scene);\r\n        this._useMicroSurfaceFromReflectivityMapAlpha = true;\r\n    }\r\n\r\n    /**\r\n     * @returns the current class name of the material.\r\n     */\r\n    public override getClassName(): string {\r\n        return \"PBRSpecularGlossinessMaterial\";\r\n    }\r\n\r\n    /**\r\n     * Makes a duplicate of the current material.\r\n     * @param name - name to use for the new material.\r\n     * @returns cloned material instance\r\n     */\r\n    public override clone(name: string): PBRSpecularGlossinessMaterial {\r\n        const clone = SerializationHelper.Clone(() => new PBRSpecularGlossinessMaterial(name, this.getScene()), this);\r\n\r\n        clone.id = name;\r\n        clone.name = name;\r\n\r\n        this.clearCoat.copyTo(clone.clearCoat);\r\n        this.anisotropy.copyTo(clone.anisotropy);\r\n        this.brdf.copyTo(clone.brdf);\r\n        this.sheen.copyTo(clone.sheen);\r\n        this.subSurface.copyTo(clone.subSurface);\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Serialize the material to a parsable JSON object.\r\n     * @returns the JSON object\r\n     */\r\n    public override serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"BABYLON.PBRSpecularGlossinessMaterial\";\r\n\r\n        if (!this.clearCoat.doNotSerialize) {\r\n            serializationObject.clearCoat = this.clearCoat.serialize();\r\n        }\r\n        if (!this.anisotropy.doNotSerialize) {\r\n            serializationObject.anisotropy = this.anisotropy.serialize();\r\n        }\r\n        if (!this.brdf.doNotSerialize) {\r\n            serializationObject.brdf = this.brdf.serialize();\r\n        }\r\n        if (!this.sheen.doNotSerialize) {\r\n            serializationObject.sheen = this.sheen.serialize();\r\n        }\r\n        if (!this.subSurface.doNotSerialize) {\r\n            serializationObject.subSurface = this.subSurface.serialize();\r\n        }\r\n        if (!this.iridescence.doNotSerialize) {\r\n            serializationObject.iridescence = this.iridescence.serialize();\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a JSON object corresponding to the serialize function.\r\n     * @param source - JSON source object.\r\n     * @param scene - the scene to parse to.\r\n     * @param rootUrl - root url of the assets.\r\n     * @returns a new PBRSpecularGlossinessMaterial.\r\n     */\r\n    public static override Parse(source: any, scene: Scene, rootUrl: string): PBRSpecularGlossinessMaterial {\r\n        const material = SerializationHelper.Parse(() => new PBRSpecularGlossinessMaterial(source.name, scene), source, scene, rootUrl);\r\n        if (source.clearCoat) {\r\n            material.clearCoat.parse(source.clearCoat, scene, rootUrl);\r\n        }\r\n        if (source.anisotropy) {\r\n            material.anisotropy.parse(source.anisotropy, scene, rootUrl);\r\n        }\r\n        if (source.brdf) {\r\n            material.brdf.parse(source.brdf, scene, rootUrl);\r\n        }\r\n        if (source.sheen) {\r\n            material.sheen.parse(source.sheen, scene, rootUrl);\r\n        }\r\n        if (source.subSurface) {\r\n            material.subSurface.parse(source.subSurface, scene, rootUrl);\r\n        }\r\n        if (source.iridescence) {\r\n            material.iridescence.parse(source.iridescence, scene, rootUrl);\r\n        }\r\n        return material;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.PBRSpecularGlossinessMaterial\", PBRSpecularGlossinessMaterial);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAI3G,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,aAAa,EAAE,MAAM,sBAAsB,CAAC;AAErD,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAC;;;;;;AAQpE,MAAO,6BAA8B,0LAAQ,wBAAqB;IAqCpE;;OAEG,CACH,IAAW,uCAAuC,GAAA;QAC9C,OAAO,IAAI,CAAC,wCAAwC,CAAC;IACzD,CAAC;IAED;;;;;OAKG,CACH,YAAY,IAAY,EAAE,KAAa,CAAA;QACnC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,wCAAwC,GAAG,IAAI,CAAC;IACzD,CAAC;IAED;;OAEG,CACa,YAAY,GAAA;QACxB,OAAO,+BAA+B,CAAC;IAC3C,CAAC;IAED;;;;OAIG,CACa,KAAK,CAAC,IAAY,EAAA;QAC9B,MAAM,KAAK,GAAG,iMAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAE9G,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC;QAChB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAElB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEzC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACa,SAAS,GAAA;QACrB,MAAM,mBAAmB,8KAAG,sBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,uCAAuC,CAAC;QAEzE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YACjC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC/D,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YAClC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QACjE,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5B,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACrD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC7B,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACvD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC;YAClC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QACjE,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YACnC,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;QACnE,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAU,KAAK,CAAC,MAAW,EAAE,KAAY,EAAE,OAAe,EAAA;QACnE,MAAM,QAAQ,8KAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,6BAA6B,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAChI,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC/D,CAAC;QACD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YACd,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACpB,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;CACJ;wJAvIU,aAAA,EAAA;kKAFN,oBAAiB,AAAjB,EAAkB,SAAS,CAAC;kKAC5B,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,cAAc,CAAC;mEACzC;wJAQrB,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,gBAAgB,CAAC;qEAC1B;wJAOtC,aAAA,EAAA;kKAFN,oBAAA,AAAiB,EAAC,UAAU,CAAC;kKAC7B,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,oBAAoB,CAAC;oEAC9C;wJAOtB,aAAA,EAAA;kKAFN,YAAA,AAAS,EAAE;kKACX,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,eAAe,CAAC;iEAC5C;wJAOnB,aAAA,EAAA;kKAFN,qBAAA,AAAkB,EAAE;kKACpB,mBAAA,AAAgB,EAAC,kCAAkC,EAAE,sBAAsB,CAAC;gFACrB;6JA4G5D,gBAAA,AAAa,EAAC,uCAAuC,EAAE,6BAA6B,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 5584, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/index.ts"], "sourcesContent": ["export * from \"./pbrAnisotropicConfiguration\";\r\nexport * from \"./pbrBaseMaterial\";\r\nexport * from \"./pbrBaseSimpleMaterial\";\r\nexport * from \"./pbrBRDFConfiguration\";\r\nexport * from \"./pbrClearCoatConfiguration\";\r\nexport * from \"./pbrIridescenceConfiguration\";\r\nexport * from \"./pbrMaterial\";\r\nexport * from \"./pbrMetallicRoughnessMaterial\";\r\nexport * from \"./pbrSpecularGlossinessMaterial\";\r\nexport * from \"./pbrSheenConfiguration\";\r\nexport * from \"./pbrSubSurfaceConfiguration\";\r\n\r\n// async-loaded shaders\r\nexport * from \"../../ShadersWGSL/pbr.vertex\";\r\nexport * from \"../../ShadersWGSL/pbr.fragment\";\r\nexport * from \"../../Shaders/pbr.vertex\";\r\nexport * from \"../../Shaders/pbr.fragment\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,+BAA+B,CAAC;AAC9C,cAAc,mBAAmB,CAAC;AAClC,cAAc,yBAAyB,CAAC;AACxC,cAAc,wBAAwB,CAAC;AACvC,cAAc,6BAA6B,CAAC;AAC5C,cAAc,+BAA+B,CAAC;AAC9C,cAAc,eAAe,CAAC;AAC9B,cAAc,gCAAgC,CAAC;AAC/C,cAAc,iCAAiC,CAAC;AAChD,cAAc,yBAAyB,CAAC;AACxC,cAAc,8BAA8B,CAAC;AAE7C,uBAAuB;AACvB,cAAc,8BAA8B,CAAC;AAC7C,cAAc,gCAAgC,CAAC;AAC/C,cAAc,0BAA0B,CAAC;AACzC,cAAc,4BAA4B,CAAC", "debugId": null}}, {"offset": {"line": 5641, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/PBR/pbrMaterial.decalMap.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/PBR/pbrMaterial.decalMap.ts"], "sourcesContent": ["import type { Nullable } from \"core/types\";\r\nimport { DecalMapConfiguration } from \"../material.decalMapConfiguration\";\r\nimport { PBRBaseMaterial } from \"./pbrBaseMaterial\";\r\n\r\ndeclare module \"./pbrBaseMaterial\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface PBRBaseMaterial {\r\n        /** @internal */\r\n        _decalMap: Nullable<DecalMapConfiguration>;\r\n\r\n        /**\r\n         * Defines the decal map parameters for the material.\r\n         */\r\n        decalMap: Nullable<DecalMapConfiguration>;\r\n    }\r\n}\r\n\r\nObject.defineProperty(PBRBaseMaterial.prototype, \"decalMap\", {\r\n    get: function (this: PBRBaseMaterial) {\r\n        if (!this._decalMap) {\r\n            this._decalMap = new DecalMapConfiguration(this);\r\n        }\r\n        return this._decalMap;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n"], "names": [], "mappings": ";AACA,OAAO,EAAE,qBAAqB,EAAE,MAAM,mCAAmC,CAAC;AAC1E,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;;;AAepD,MAAM,CAAC,cAAc,4KAAC,kBAAe,CAAC,SAAS,EAAE,UAAU,EAAE;IACzD,GAAG,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,GAAG,0LAAI,wBAAqB,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC", "debugId": null}}]}