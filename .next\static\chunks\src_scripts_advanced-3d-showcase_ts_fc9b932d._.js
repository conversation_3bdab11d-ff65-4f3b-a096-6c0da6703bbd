(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/scripts/advanced-3d-showcase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Advanced3DShowcaseEngine
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$scene$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/scene.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Cameras$2f$arcRotateCamera$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Cameras/arcRotateCamera.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.vector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.color.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$hemisphericLight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Lights/hemisphericLight.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$directionalLight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Lights/directionalLight.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$spotLight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Lights/spotLight.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$pointLight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Lights/pointLight.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$Shadows$2f$shadowGenerator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Lights/Shadows/shadowGenerator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Meshes/meshBuilder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/PBR/pbrMaterial.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$cubeTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/cubeTexture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$dynamicTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/dynamicTexture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Animations/animation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$RenderPipeline$2f$Pipelines$2f$defaultRenderingPipeline$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/PostProcesses/RenderPipeline/Pipelines/defaultRenderingPipeline.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$XR$2f$webXRFeaturesManager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/XR/webXRFeaturesManager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Audio$2f$analyser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Audio/analyser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$particleSystem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Particles/particleSystem.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$gpuParticleSystem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Particles/gpuParticleSystem.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$loaders$2f$glTF$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/loaders/glTF/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$materials$2f$custom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/materials/custom/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/index.js [app-client] (ecmascript) <module evaluation>");
;
;
;
;
;
;
class Advanced3DShowcaseEngine {
    async initialize() {
        try {
            // Setup lighting
            this.setupAdvancedLighting();
            // Create advanced materials showcase
            this.createMaterialShowcase();
            // Setup advanced post-processing
            this.setupAdvancedPostProcessing();
            // Create particle systems
            this.createAdvancedParticles();
            // Setup WebXR if enabled
            if (this.options.enableWebXR) {
                await this.setupWebXR();
            }
            // Setup audio reactive features if enabled
            if (this.options.enableAudioReactive) {
                await this.setupAudioReactive();
            }
            // Start render loop
            this.startRenderLoop();
            // Setup performance monitoring
            if (this.options.showPerformanceStats) {
                this.setupPerformanceMonitoring();
            }
            console.log("Advanced 3D Showcase initialized successfully");
        } catch (error) {
            console.error("Error initializing Advanced 3D Showcase:", error);
        }
    }
    setupAdvancedLighting() {
        // Ambient lighting
        const hemisphericLight = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$hemisphericLight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HemisphericLight"]("hemisphericLight", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](0, 1, 0), this.scene);
        hemisphericLight.intensity = 0.3;
        hemisphericLight.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0.2, 0.4, 0.8);
        hemisphericLight.specular = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0.1, 0.2, 0.4);
        // Main directional light with shadows
        const directionalLight = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$directionalLight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DirectionalLight"]("directionalLight", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](-1, -1, -1), this.scene);
        directionalLight.intensity = 1.2;
        directionalLight.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](1, 0.9, 0.8);
        directionalLight.specular = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](1, 1, 1);
        // Setup shadow generator
        const shadowGenerator = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$Shadows$2f$shadowGenerator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShadowGenerator"](2048, directionalLight);
        shadowGenerator.useExponentialShadowMap = true;
        shadowGenerator.darkness = 0.3;
        // Accent spot lights
        const spotLight1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$spotLight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SpotLight"]("spotLight1", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](-10, 15, -10), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](1, -1, 1), Math.PI / 3, 2, this.scene);
        spotLight1.intensity = 0.8;
        spotLight1.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0, 1, 1);
        const spotLight2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$spotLight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SpotLight"]("spotLight2", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](10, 15, 10), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](-1, -1, -1), Math.PI / 3, 2, this.scene);
        spotLight2.intensity = 0.8;
        spotLight2.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](1, 0, 1);
        // Animated point lights
        const pointLight1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$pointLight$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PointLight"]("pointLight1", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](0, 5, 0), this.scene);
        pointLight1.intensity = 1.5;
        pointLight1.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](1, 0.5, 0);
        pointLight1.range = 20;
        // Animate point light
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Animation"].CreateAndStartAnimation("pointLightAnimation", pointLight1, "position", 30, 120, pointLight1.position, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](8, 5, 8), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Animation"].ANIMATIONLOOPMODE_YOYO);
    }
    createMaterialShowcase() {
        // Create a collection of objects showcasing different PBR materials
        const showcaseItems = [
            {
                type: "sphere",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](-8, 2, 0),
                material: "chrome"
            },
            {
                type: "box",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](-4, 2, 0),
                material: "gold"
            },
            {
                type: "cylinder",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](0, 2, 0),
                material: "glass"
            },
            {
                type: "torus",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](4, 2, 0),
                material: "carbon"
            },
            {
                type: "dodecahedron",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](8, 2, 0),
                material: "iridescent"
            },
            {
                type: "ground",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](0, 0, 0),
                material: "marble"
            }
        ];
        showcaseItems.forEach((item, index)=>{
            let mesh;
            switch(item.type){
                case "sphere":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshBuilder"].CreateSphere("showcase_sphere_".concat(index), {
                        diameter: 3,
                        segments: 32
                    }, this.scene);
                    break;
                case "box":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshBuilder"].CreateBox("showcase_box_".concat(index), {
                        size: 2.5
                    }, this.scene);
                    break;
                case "cylinder":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshBuilder"].CreateCylinder("showcase_cylinder_".concat(index), {
                        height: 3,
                        diameter: 2.5,
                        tessellation: 24
                    }, this.scene);
                    break;
                case "torus":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshBuilder"].CreateTorus("showcase_torus_".concat(index), {
                        diameter: 3,
                        thickness: 1,
                        tessellation: 32
                    }, this.scene);
                    break;
                case "dodecahedron":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshBuilder"].CreatePolyhedron("showcase_dodecahedron_".concat(index), {
                        type: 2,
                        size: 1.5
                    }, this.scene);
                    break;
                case "ground":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MeshBuilder"].CreateGround("showcase_ground_".concat(index), {
                        width: 30,
                        height: 30,
                        subdivisions: 32
                    }, this.scene);
                    break;
                default:
                    return;
            }
            mesh.position = item.position;
            mesh.material = this.createAdvancedMaterial(item.material, "".concat(item.material, "_").concat(index));
            // Add floating animation for non-ground objects
            if (item.type !== "ground") {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Animation"].CreateAndStartAnimation("float_".concat(index), mesh, "position.y", 30, 120 + index * 10, mesh.position.y, mesh.position.y + 1 + Math.sin(index) * 0.3, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Animation"].ANIMATIONLOOPMODE_YOYO);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Animation"].CreateAndStartAnimation("rotate_".concat(index), mesh, "rotation.y", 30, 180 + index * 20, 0, Math.PI * 2, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Animation"].ANIMATIONLOOPMODE_CYCLE);
            }
            this.showcaseObjects.push(mesh);
        });
    }
    createAdvancedMaterial(materialType, name) {
        const pbr = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrMaterial$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PBRMaterial"](name, this.scene);
        // Create environment texture for reflections
        const envTexture = this.createEnvironmentTexture();
        this.scene.environmentTexture = envTexture;
        switch(materialType){
            case "chrome":
                pbr.albedoColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0.9, 0.9, 0.9);
                pbr.metallic = 1.0;
                pbr.roughness = 0.05;
                pbr.environmentIntensity = 1.5;
                break;
            case "gold":
                pbr.albedoColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](1.0, 0.8, 0.2);
                pbr.metallic = 1.0;
                pbr.roughness = 0.1;
                pbr.environmentIntensity = 1.2;
                break;
            case "glass":
                pbr.albedoColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0.95, 0.98, 1.0);
                pbr.metallic = 0.0;
                pbr.roughness = 0.0;
                pbr.alpha = 0.15;
                pbr.indexOfRefraction = 1.52;
                pbr.linkRefractionWithTransparency = true;
                pbr.subSurface.isRefractionEnabled = true;
                pbr.subSurface.refractionIntensity = 1.0;
                break;
            case "carbon":
                pbr.albedoColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0.1, 0.1, 0.1);
                pbr.metallic = 0.8;
                pbr.roughness = 0.3;
                pbr.anisotropy.isEnabled = true;
                pbr.anisotropy.intensity = 1.0;
                pbr.anisotropy.direction = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector2"](1, 0);
                break;
            case "iridescent":
                pbr.albedoColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0.05, 0.05, 0.05);
                pbr.metallic = 1.0;
                pbr.roughness = 0.0;
                pbr.iridescence.isEnabled = true;
                pbr.iridescence.intensity = 1.0;
                pbr.iridescence.indexOfRefraction = 1.3;
                pbr.iridescence.minimumThickness = 100;
                pbr.iridescence.maximumThickness = 400;
                break;
            case "marble":
                pbr.albedoColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0.9, 0.9, 0.85);
                pbr.metallic = 0.0;
                pbr.roughness = 0.6;
                pbr.subSurface.isTranslucencyEnabled = true;
                pbr.subSurface.translucencyIntensity = 0.3;
                pbr.subSurface.tintColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0.95, 0.95, 0.9);
                // Add procedural marble texture
                const marbleTexture = this.createMarbleTexture();
                pbr.albedoTexture = marbleTexture;
                pbr.bumpTexture = marbleTexture;
                break;
            default:
                pbr.albedoColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color3"](0.5, 0.5, 0.5);
                pbr.metallic = 0.5;
                pbr.roughness = 0.5;
                break;
        }
        return pbr;
    }
    createEnvironmentTexture() {
        // Create a procedural environment texture
        const envTexture = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$cubeTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CubeTexture"].CreateFromImages([
            this.createSkyTexture("px"),
            this.createSkyTexture("nx"),
            this.createSkyTexture("py"),
            this.createSkyTexture("ny"),
            this.createSkyTexture("pz"),
            this.createSkyTexture("nz")
        ], this.scene);
        return envTexture;
    }
    createSkyTexture(face) {
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");
        // Create cyberpunk-style sky gradient
        const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 512);
        gradient.addColorStop(0, "#001a33");
        gradient.addColorStop(0.3, "#003366");
        gradient.addColorStop(0.6, "#001122");
        gradient.addColorStop(1, "#000011");
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 512);
        // Add stars and nebula effects
        for(let i = 0; i < 150; i++){
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const brightness = Math.random();
            const size = Math.random() * 2 + 1;
            ctx.fillStyle = "rgba(".concat(100 + brightness * 155, ", ").concat(150 + brightness * 105, ", 255, ").concat(brightness, ")");
            ctx.fillRect(x, y, size, size);
        }
        return canvas.toDataURL();
    }
    createMarbleTexture() {
        const texture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$dynamicTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DynamicTexture"]("marbleTexture", {
            width: 512,
            height: 512
        }, this.scene);
        const ctx = texture.getContext();
        // Create marble pattern
        const imageData = new ImageData(512, 512);
        const data = imageData.data;
        for(let y = 0; y < 512; y++){
            for(let x = 0; x < 512; x++){
                const index = (y * 512 + x) * 4;
                // Create marble-like noise pattern
                const noise1 = Math.sin(x * 0.01 + y * 0.01) * 0.5 + 0.5;
                const noise2 = Math.sin(x * 0.02 + y * 0.005) * 0.3 + 0.7;
                const noise3 = Math.sin(x * 0.005 + y * 0.02) * 0.2 + 0.8;
                const marble = noise1 * noise2 * noise3;
                const color = Math.floor(200 + marble * 55);
                data[index] = color; // R
                data[index + 1] = color; // G
                data[index + 2] = Math.floor(color * 0.95); // B
                data[index + 3] = 255; // A
            }
        }
        ctx.putImageData(imageData, 0, 0);
        texture.update();
        return texture;
    }
    setupAdvancedPostProcessing() {
        try {
            // Create advanced rendering pipeline
            this.renderingPipeline = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$RenderPipeline$2f$Pipelines$2f$defaultRenderingPipeline$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DefaultRenderingPipeline"]("advancedPipeline", true, this.scene, [
                this.camera
            ]);
            if (this.renderingPipeline) {
                // Configure bloom for glowing effects
                this.renderingPipeline.bloomEnabled = true;
                this.renderingPipeline.bloomThreshold = 0.7;
                this.renderingPipeline.bloomWeight = 0.4;
                this.renderingPipeline.bloomKernel = 64;
                this.renderingPipeline.bloomScale = 0.6;
                // Enable tone mapping for HDR
                if (this.renderingPipeline.imageProcessing) {
                    this.renderingPipeline.imageProcessing.toneMappingEnabled = true;
                    this.renderingPipeline.imageProcessing.toneMappingType = 1; // ACES tone mapping
                }
                // Enable FXAA anti-aliasing
                this.renderingPipeline.fxaaEnabled = true;
                // Configure chromatic aberration
                this.renderingPipeline.chromaticAberrationEnabled = true;
                this.renderingPipeline.chromaticAberration.aberrationAmount = 20;
                this.renderingPipeline.chromaticAberration.radialIntensity = 0.8;
                // Configure grain effect
                this.renderingPipeline.grainEnabled = true;
                this.renderingPipeline.grain.intensity = 8;
                this.renderingPipeline.grain.animated = true;
                // Configure vignette
                if (this.renderingPipeline.imageProcessing) {
                    this.renderingPipeline.imageProcessing.vignetteEnabled = true;
                    this.renderingPipeline.imageProcessing.vignetteStretch = 0.15;
                    this.renderingPipeline.imageProcessing.vignetteWeight = 1.2;
                    this.renderingPipeline.imageProcessing.vignetteColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color4"](0, 0, 0, 0);
                }
                // Enable depth of field
                this.renderingPipeline.depthOfFieldEnabled = true;
                this.renderingPipeline.depthOfFieldBlurLevel = 0;
                this.renderingPipeline.depthOfField.focusDistance = 2000;
                this.renderingPipeline.depthOfField.focalLength = 50;
                this.renderingPipeline.depthOfField.fStop = 1.4;
            // Enable screen space reflections (commented out due to API changes)
            // this.renderingPipeline.screenSpaceReflectionEnabled = true;
            // if (this.renderingPipeline.screenSpaceReflectionPostProcess) {
            //   this.renderingPipeline.screenSpaceReflectionPostProcess.strength = 0.6;
            //   this.renderingPipeline.screenSpaceReflectionPostProcess.reflectionSpecularFalloffExponent = 1.2;
            //   this.renderingPipeline.screenSpaceReflectionPostProcess.maxDistance = 1500;
            // }
            }
            console.log("Advanced post-processing pipeline setup complete");
        } catch (error) {
            console.error("Error setting up post-processing:", error);
        }
    }
    createAdvancedParticles() {
        try {
            // Create GPU-based particle system for better performance
            const particleSystem = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$gpuParticleSystem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GPUParticleSystem"]("advancedParticles", {
                capacity: 50000
            }, this.scene);
            // Configure particle emitter
            particleSystem.emitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"].Zero();
            particleSystem.minEmitBox = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](-15, 0, -15);
            particleSystem.maxEmitBox = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](15, 0, 15);
            // Particle properties
            particleSystem.particleTexture = this.createParticleTexture();
            particleSystem.emitRate = 1000;
            particleSystem.minLifeTime = 2;
            particleSystem.maxLifeTime = 8;
            particleSystem.minSize = 0.1;
            particleSystem.maxSize = 0.8;
            particleSystem.minInitialRotation = 0;
            particleSystem.maxInitialRotation = Math.PI * 2;
            particleSystem.minAngularSpeed = -Math.PI;
            particleSystem.maxAngularSpeed = Math.PI;
            // Particle movement
            particleSystem.direction1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](-1, 1, -1);
            particleSystem.direction2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](1, 1, 1);
            particleSystem.minEmitPower = 2;
            particleSystem.maxEmitPower = 6;
            particleSystem.updateSpeed = 0.02;
            // Gravity and forces
            particleSystem.gravity = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](0, -2, 0);
            // Color animation
            particleSystem.color1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color4"](0, 1, 1, 1);
            particleSystem.color2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color4"](1, 0, 1, 1);
            particleSystem.colorDead = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Color4"](0, 0, 0, 0);
            // Blending mode
            particleSystem.blendMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$particleSystem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ParticleSystem"].BLENDMODE_ONEONE;
            // Start the particle system
            particleSystem.start();
            this.particleSystems.push(particleSystem);
            console.log("Advanced particle system created");
        } catch (error) {
            console.error("Error creating particle system:", error);
        }
    }
    createParticleTexture() {
        const texture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$dynamicTexture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DynamicTexture"]("particleTexture", {
            width: 64,
            height: 64
        }, this.scene);
        const ctx = texture.getContext();
        // Create glowing particle texture
        const gradient = ctx.createRadialGradient(32, 32, 0, 32, 32, 32);
        gradient.addColorStop(0, "rgba(255, 255, 255, 1)");
        gradient.addColorStop(0.3, "rgba(0, 255, 255, 0.8)");
        gradient.addColorStop(0.6, "rgba(255, 0, 255, 0.4)");
        gradient.addColorStop(1, "rgba(0, 0, 0, 0)");
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 64, 64);
        texture.update();
        return texture;
    }
    async setupWebXR() {
        try {
            if ("xr" in navigator) {
                this.webXRExperience = await this.scene.createDefaultXRExperienceAsync({
                    floorMeshes: this.showcaseObjects.filter((obj)=>obj.name.includes("ground"))
                });
                if (this.webXRExperience) {
                    // Enable hand tracking
                    const handTracking = this.webXRExperience.baseExperience.featuresManager.enableFeature(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$XR$2f$webXRFeaturesManager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WebXRFeatureName"].HAND_TRACKING, "latest");
                    // Enable hit test for AR
                    const hitTest = this.webXRExperience.baseExperience.featuresManager.enableFeature(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$XR$2f$webXRFeaturesManager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WebXRFeatureName"].HIT_TEST, "latest");
                    console.log("WebXR experience initialized");
                }
            }
        } catch (error) {
            console.error("Error setting up WebXR:", error);
        }
    }
    async setupAudioReactive() {
        try {
            // Request microphone access
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: true
            });
            // Create audio context and analyser
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const source = this.audioContext.createMediaStreamSource(stream);
            this.audioAnalyser = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Audio$2f$analyser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Analyser"](this.scene);
            console.log("Audio reactive features initialized");
        } catch (error) {
            console.error("Error setting up audio reactive features:", error);
        }
    }
    startRenderLoop() {
        this.engine.runRenderLoop(()=>{
            if (this.scene) {
                // Update audio reactive effects
                if (this.audioAnalyser && this.options.enableAudioReactive) {
                    this.updateAudioReactiveEffects();
                }
                // Render the scene
                this.scene.render();
            }
        });
    }
    updateAudioReactiveEffects() {
        if (!this.audioAnalyser) return;
        try {
            const frequencyData = this.audioAnalyser.getByteFrequencyData();
            const averageFrequency = frequencyData.reduce((a, b)=>a + b) / frequencyData.length;
            const normalizedFrequency = averageFrequency / 255;
            // Update particle emission rate based on audio
            this.particleSystems.forEach((system)=>{
                if (system instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$gpuParticleSystem$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GPUParticleSystem"]) {
                    system.emitRate = 500 + normalizedFrequency * 2000;
                }
            });
            // Update post-processing effects based on audio
            if (this.renderingPipeline) {
                this.renderingPipeline.bloomWeight = 0.2 + normalizedFrequency * 0.6;
                this.renderingPipeline.chromaticAberration.aberrationAmount = 10 + normalizedFrequency * 40;
            }
            // Update object animations based on audio
            this.showcaseObjects.forEach((obj, index)=>{
                if (!obj.name.includes("ground")) {
                    const scaleMultiplier = 1 + normalizedFrequency * 0.3;
                    obj.scaling = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"](scaleMultiplier, scaleMultiplier, scaleMultiplier);
                }
            });
        } catch (error) {
            console.error("Error updating audio reactive effects:", error);
        }
    }
    setupPerformanceMonitoring() {
        setInterval(()=>{
            this.performanceStats.fps = Math.round(this.engine.getFps());
            this.performanceStats.drawCalls = this.scene.getActiveMeshes().length;
            this.performanceStats.triangles = this.scene.getTotalVertices();
        }, 1000);
    }
    getPerformanceStats() {
        return this.performanceStats;
    }
    async toggleWebXR() {
        if (this.webXRExperience) {
            try {
                if (this.webXRExperience.baseExperience.state === 0) {
                    await this.webXRExperience.baseExperience.enterXRAsync("immersive-ar", "local-floor");
                } else {
                    await this.webXRExperience.baseExperience.exitXRAsync();
                }
            } catch (error) {
                console.error("Error toggling WebXR:", error);
            }
        }
    }
    toggleAudioReactive() {
        this.options.enableAudioReactive = !this.options.enableAudioReactive;
        console.log("Audio reactive mode:", this.options.enableAudioReactive ? "enabled" : "disabled");
    }
    destroy() {
        // Dispose of all resources
        this.particleSystems.forEach((system)=>system.dispose());
        this.animationGroups.forEach((group)=>group.dispose());
        this.showcaseObjects.forEach((obj)=>obj.dispose());
        if (this.renderingPipeline) {
            this.renderingPipeline.dispose();
        }
        if (this.webXRExperience) {
            this.webXRExperience.dispose();
        }
        if (this.audioContext) {
            this.audioContext.close();
        }
        if (this.scene) {
            this.scene.dispose();
        }
        if (this.engine) {
            this.engine.dispose();
        }
        console.log("Advanced 3D Showcase destroyed");
    }
    constructor(canvas, options = {}){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "canvas", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "engine", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "scene", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "camera", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "options", void 0);
        // Advanced features
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "renderingPipeline", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "webXRExperience", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "audioAnalyser", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "audioContext", null);
        // 3D Objects and animations
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "showcaseObjects", []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "particleSystems", []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "animationGroups", []);
        // Performance tracking
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "performanceStats", {
            fps: 0,
            drawCalls: 0,
            triangles: 0
        });
        this.canvas = canvas;
        this.options = options;
        // Initialize Babylon.js engine
        this.engine = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Engine"](canvas, true, {
            adaptToDeviceRatio: true,
            antialias: true,
            powerPreference: "high-performance",
            xrCompatible: options.enableWebXR
        });
        // Create scene
        this.scene = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$scene$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Scene"](this.engine);
        this.scene.useRightHandedSystem = true;
        // Setup camera
        this.camera = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Cameras$2f$arcRotateCamera$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ArcRotateCamera"]("showcaseCamera", -Math.PI / 2, Math.PI / 2.5, 25, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Vector3"].Zero(), this.scene);
        this.camera.attachControl(canvas, true);
        this.camera.wheelPrecision = 50;
        this.camera.minZ = 0.1;
        this.camera.maxZ = 1000;
        this.initialize();
    }
}
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_scripts_advanced-3d-showcase_ts_fc9b932d._.js.map