{"version": 3, "file": "engine.dynamicBuffer.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.dynamicBuffer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AA0BtD,6DAA6D;AAC7D,UAAU,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAA4B,WAAuB,EAAE,OAAqB,EAAE,SAAiB,CAAC;IAC1I,qBAAqB;IACrB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC;IAC/D,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IAElC,IAAI,IAAqB,CAAC;IAC1B,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;QACvB,sEAAsE;QACtE,IAAI,GAAG,OAAO,YAAY,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/E,CAAC;SAAM,CAAC;QACJ,sEAAsE;QACtE,IAAI,GAAG,OAAO,YAAY,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/E,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAEhF,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACpC,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAA4B,YAAwB,EAAE,IAAe,EAAE,UAAmB,EAAE,UAAmB;IAC5J,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;IAEnC,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;QAC3B,UAAU,GAAG,CAAC,CAAC;IACnB,CAAC;IAED,MAAM,UAAU,GAAI,IAAoB,CAAC,UAAU,IAAK,IAAiB,CAAC,MAAM,CAAC;IAEjF,IAAI,UAAU,KAAK,SAAS,IAAI,CAAC,UAAU,IAAI,UAAU,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;QAC7E,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QACtF,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;QAClH,CAAC;aAAM,CAAC;YACJ,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;gBAC9B,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACJ,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACrC,CAAC,CAAC", "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\nimport type { IndicesArray, DataArray } from \"../../types\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Update a dynamic index buffer\r\n         * @param indexBuffer defines the target index buffer\r\n         * @param indices defines the data to update\r\n         * @param offset defines the offset in the target index buffer where update should start\r\n         */\r\n        updateDynamicIndexBuffer(indexBuffer: DataBuffer, indices: IndicesArray, offset?: number): void;\r\n\r\n        /**\r\n         * Updates a dynamic vertex buffer.\r\n         * @param vertexBuffer the vertex buffer to update\r\n         * @param data the data used to update the vertex buffer\r\n         * @param byteOffset the byte offset of the data\r\n         * @param byteLength the byte length of the data\r\n         */\r\n        updateDynamicVertexBuffer(vertexBuffer: DataBuffer, data: DataArray, byteOffset?: number, byteLength?: number): void;\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nThinEngine.prototype.updateDynamicIndexBuffer = function (this: ThinEngine, indexBuffer: DataBuffer, indices: IndicesArray, offset: number = 0): void {\r\n    // Force cache update\r\n    this._currentBoundBuffer[this._gl.ELEMENT_ARRAY_BUFFER] = null;\r\n    this.bindIndexBuffer(indexBuffer);\r\n\r\n    let view: ArrayBufferView;\r\n    if (indexBuffer.is32Bits) {\r\n        // anything else than Uint32Array needs to be converted to Uint32Array\r\n        view = indices instanceof Uint32Array ? indices : new Uint32Array(indices);\r\n    } else {\r\n        // anything else than Uint16Array needs to be converted to Uint16Array\r\n        view = indices instanceof Uint16Array ? indices : new Uint16Array(indices);\r\n    }\r\n\r\n    this._gl.bufferData(this._gl.ELEMENT_ARRAY_BUFFER, view, this._gl.DYNAMIC_DRAW);\r\n\r\n    this._resetIndexBufferBinding();\r\n};\r\n\r\nThinEngine.prototype.updateDynamicVertexBuffer = function (this: ThinEngine, vertexBuffer: DataBuffer, data: DataArray, byteOffset?: number, byteLength?: number): void {\r\n    this.bindArrayBuffer(vertexBuffer);\r\n\r\n    if (byteOffset === undefined) {\r\n        byteOffset = 0;\r\n    }\r\n\r\n    const dataLength = (data as ArrayBuffer).byteLength || (data as number[]).length;\r\n\r\n    if (byteLength === undefined || (byteLength >= dataLength && byteOffset === 0)) {\r\n        if (data instanceof Array) {\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, byteOffset, new Float32Array(data));\r\n        } else {\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, byteOffset, data);\r\n        }\r\n    } else {\r\n        if (data instanceof Array) {\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, byteOffset, new Float32Array(data).subarray(0, byteLength / 4));\r\n        } else {\r\n            if (data instanceof ArrayBuffer) {\r\n                data = new Uint8Array(data, 0, byteLength);\r\n            } else {\r\n                data = new Uint8Array(data.buffer, data.byteOffset, byteLength);\r\n            }\r\n\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, byteOffset, data);\r\n        }\r\n    }\r\n\r\n    this._resetVertexBufferBinding();\r\n};\r\n"]}