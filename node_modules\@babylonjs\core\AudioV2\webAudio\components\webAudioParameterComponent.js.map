{"version": 3, "file": "webAudioParameterComponent.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/webAudio/components/webAudioParameterComponent.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,yBAAyB,EAAE,MAAM,kBAAkB,CAAC;AAG7D;;;;;;;;;;GAUG;AACH,MAAM,WAAW,GAAG,KAAK,CAAC;AAE1B;;;;;GAKG;AACH,MAAM,eAAe,GAAG,QAAQ,CAAC;AAEjC,gBAAgB;AAChB,MAAM,OAAO,2BAA2B;IAYpC,gBAAgB;IAChB,YAAY,MAAuB,EAAE,KAAiB;QAZ9C,yBAAoB,GAAG;YAC3B,QAAQ,EAAE,CAAC;YACX,KAAK,+CAAgC;SACxC,CAAC;QACM,yBAAoB,GAAG,CAAC,CAAC,CAAC;QAC1B,wBAAmB,GAAG,KAAK,CAAC;QAC5B,iBAAY,GAAW,CAAC,CAAC;QAqGzB,uBAAkB,GAAG,GAAG,EAAE;YAC9B,IAAI,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBACzF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC,CAAC;QAlGE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC;IACpC,CAAC;IAED,gBAAgB;IAChB,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;IACxD,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,gBAAgB;IAChB,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,CAAC,MAAM,GAAG,IAAK,CAAC;QACpB,IAAI,CAAC,OAAO,GAAG,IAAK,CAAC;IACzB,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,cAAc,CAAC,KAAa,EAAE,UAAyD,IAAI;QAC9F,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,OAAO,EAAE,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,8CAA+B,CAAC;QAElG,IAAI,QAAQ,GAAG,OAAO,OAAO,EAAE,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;QAC3J,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;QAE3C,IAAI,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAE/C,IAAI,WAAW,GAAG,QAAQ,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;YACjF,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBACxC,OAAO;YACX,CAAC;QACL,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC,GAAG,eAAe,EAAE,CAAC;YACxF,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;YACnE,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAEvI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,CAAC,YAAY,GAAG,SAAS,GAAG,QAAQ,CAAC;IAC7C,CAAC;IAEO,UAAU,CAAC,KAAa,EAAE,QAAgB,EAAE,KAA8B;QAC9E,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC9C,IAAI,CAAC,oBAAoB,CAAC,KAAK,GAAG,KAAK,CAAC;QACxC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;IACL,CAAC;IAQO,kBAAkB;QACtB,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG,CAAC,CAAC;QAEvC,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC5D,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACrC,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { IAudioParameterRampOptions } from \"../../audioParameter\";\nimport { AudioParameterRampShape } from \"../../audioParameter\";\nimport { _GetAudioParamCurveValues } from \"../../audioUtils\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\n\n/**\n * Maximum time in seconds to wait for an active ramp to finish before starting a new ramp.\n *\n * New ramps will throw an error if the active ramp has more than this amount of time remaining.\n *\n * This is needed because short ramps are used to avoid pops and clicks when setting audio parameters, and we\n * don't want to throw an error if a short ramp is active.\n *\n * This constant is set to 11 milliseconds, which is short enough to avoid perceptual differences in most cases, but\n * long enough to allow for short ramps to be completed in a reasonable time frame.\n */\nconst MaxWaitTime = 0.011;\n\n/**\n * Minimum duration in seconds for a ramp to be considered valid.\n *\n * If the duration is less than this value, the value will be set immediately instead of being ramped smoothly since\n * there is no perceptual difference for such short durations, so a ramp is not needed.\n */\nconst MinRampDuration = 0.000001;\n\n/** @internal */\nexport class _WebAudioParameterComponent {\n    private _deferredRampOptions = {\n        duration: 0,\n        shape: AudioParameterRampShape.Linear,\n    };\n    private _deferredTargetValue = -1;\n    private _isObservingUpdates = false;\n    private _rampEndTime: number = 0;\n    private _engine: _WebAudioEngine;\n    private _param: AudioParam;\n    private _targetValue: number;\n\n    /** @internal */\n    constructor(engine: _WebAudioEngine, param: AudioParam) {\n        this._engine = engine;\n        this._param = param;\n        this._targetValue = param.value;\n    }\n\n    /** @internal */\n    public get isRamping(): boolean {\n        return this._engine.currentTime < this._rampEndTime;\n    }\n\n    /** @internal */\n    public get targetValue(): number {\n        return this._targetValue;\n    }\n\n    public set targetValue(value: number) {\n        this.setTargetValue(value);\n    }\n\n    /** @internal */\n    public get value(): number {\n        return this._param.value;\n    }\n\n    /** @internal */\n    public dispose(): void {\n        this._clearDeferredRamp();\n\n        this._param = null!;\n        this._engine = null!;\n    }\n\n    /**\n     * Sets the target value of the audio parameter with an optional ramping duration and shape.\n     *\n     * If a ramp is close to finishing, it will wait for the ramp to finish before setting the new value; otherwise it\n     * will throw an error because of a bug in Firefox that prevents active ramps from being cancelled with\n     * `cancelScheduledValues`. See https://bugzilla.mozilla.org/show_bug.cgi?id=1752775. Other browsers do not have\n     * this issue, but we throw an error in all browsers to ensure consistent behavior.\n     *\n     * There are other similar WebAudio APIs for ramping parameters, (e.g. `linearRampToValueAtTime` and\n     * `exponentialRampToValueAtTime`) but they don't work in Firefox and Meta Quest Chrome.\n     *\n     * It may be better in the long run to implement our own ramping logic with a WASM audio worklet instead of using\n     * `setValueCurveAtTime`. Another alternative is to use `setValueAtTime` wtih a custom shape, but that will\n     * probably be a performance hit to maintain quality at audio rates.\n     *\n     * @internal\n     */\n    public setTargetValue(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        if (this._targetValue === value) {\n            return;\n        }\n\n        const shape = typeof options?.shape === \"string\" ? options.shape : AudioParameterRampShape.Linear;\n\n        let duration = typeof options?.duration === \"number\" ? Math.max(options.duration, this._engine.parameterRampDuration) : this._engine.parameterRampDuration;\n        const startTime = this._engine.currentTime;\n\n        if (startTime < this._rampEndTime) {\n            const timeLeft = this._rampEndTime - startTime;\n\n            if (MaxWaitTime < timeLeft) {\n                throw new Error(\"Audio parameter not set. Wait for current ramp to finish.\");\n            } else {\n                this._deferRamp(value, duration, shape);\n                return;\n            }\n        }\n\n        if ((duration = Math.max(this._engine.parameterRampDuration, duration)) < MinRampDuration) {\n            this._param.setValueAtTime((this._targetValue = value), startTime);\n            return;\n        }\n\n        this._param.cancelScheduledValues(startTime);\n        this._param.setValueCurveAtTime(_GetAudioParamCurveValues(shape, this._targetValue, (this._targetValue = value)), startTime, duration);\n\n        this._clearDeferredRamp();\n\n        this._rampEndTime = startTime + duration;\n    }\n\n    private _deferRamp(value: number, duration: number, shape: AudioParameterRampShape): void {\n        this._deferredRampOptions.duration = duration;\n        this._deferredRampOptions.shape = shape;\n        this._deferredTargetValue = value;\n\n        if (!this._isObservingUpdates) {\n            this._engine._addUpdateObserver(this._applyDeferredRamp);\n            this._isObservingUpdates = true;\n        }\n    }\n\n    private _applyDeferredRamp = () => {\n        if (0 < this._deferredRampOptions.duration && this._rampEndTime < this._engine.currentTime) {\n            this.setTargetValue(this._deferredTargetValue, this._deferredRampOptions);\n        }\n    };\n\n    private _clearDeferredRamp(): void {\n        this._deferredRampOptions.duration = 0;\n\n        if (this._isObservingUpdates) {\n            this._engine._removeUpdateObserver(this._applyDeferredRamp);\n            this._isObservingUpdates = false;\n        }\n    }\n}\n"]}