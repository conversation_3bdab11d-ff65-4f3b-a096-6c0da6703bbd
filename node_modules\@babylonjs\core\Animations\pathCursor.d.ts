import { Vector3 } from "../Maths/math.vector.js";
import type { Path2 } from "../Maths/math.path.js";
/**
 * A cursor which tracks a point on a path
 */
export declare class PathCursor {
    private _path;
    /**
     * Stores path cursor callbacks for when an onchange event is triggered
     */
    private _onchange;
    /**
     * The value of the path cursor
     */
    value: number;
    /**
     * The animation array of the path cursor
     */
    animations: Animation[];
    /**
     * Initializes the path cursor
     * @param _path The path to track
     */
    constructor(_path: Path2);
    /**
     * Gets the cursor point on the path
     * @returns A point on the path cursor at the cursor location
     */
    getPoint(): Vector3;
    /**
     * Moves the cursor ahead by the step amount
     * @param step The amount to move the cursor forward
     * @returns This path cursor
     */
    moveAhead(step?: number): PathCursor;
    /**
     * Moves the cursor behind by the step amount
     * @param step The amount to move the cursor back
     * @returns This path cursor
     */
    moveBack(step?: number): PathCursor;
    /**
     * Moves the cursor by the step amount
     * If the step amount is greater than one, an exception is thrown
     * @param step The amount to move the cursor
     * @returns This path cursor
     */
    move(step: number): PathCursor;
    /**
     * Ensures that the value is limited between zero and one
     * @returns This path cursor
     */
    private _ensureLimits;
    /**
     * Runs onchange callbacks on change (used by the animation engine)
     * @returns This path cursor
     */
    private _raiseOnChange;
    /**
     * Executes a function on change
     * @param f A path cursor onchange callback
     * @returns This path cursor
     */
    onchange(f: (cursor: PathCursor) => void): PathCursor;
}
