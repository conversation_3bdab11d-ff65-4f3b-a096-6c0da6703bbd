{"version": 3, "file": "nativeDataStream.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Native/nativeDataStream.ts"], "names": [], "mappings": "AASA,gBAAgB;AAChB,MAAM,OAAO,gBAAgB;IAYzB;QACI,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;QAE1C,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,mBAAmB,GAAG,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,gBAAgB,CAAC,GAAG,EAAE;YACvD,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,KAAa;QAC5B,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;;OAGG;IACI,UAAU,CAAC,KAAa;QAC3B,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,KAAa;QAC7B,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,CAAC;IAC7C,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,MAAmB;QACvC,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAChD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,MAAkB;QACrC,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,MAAiC;QACtD,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3C,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,MAAkB;QACrC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,KAAc;QAC9B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAEO,iBAAiB,CAAC,QAAgB;QACtC,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC3C,IAAI,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC;IACL,CAAC;IAEO,MAAM;QACV,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACzE,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IACvB,CAAC;;AAzGD,yBAAyB;AACzB,gEAAgE;AACxC,oCAAmB,GAAG,KAAK,CAAC", "sourcesContent": ["import type { DeepImmutable, FloatArray } from \"../../types\";\r\nimport type { INative, INativeDataStream } from \"./nativeInterfaces\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare const _native: INative;\r\n\r\n/** @internal */\r\nexport type NativeData = Uint32Array;\r\n\r\n/** @internal */\r\nexport class NativeDataStream {\r\n    private readonly _uint32s: Uint32Array;\r\n    private readonly _int32s: Int32Array;\r\n    private readonly _float32s: Float32Array;\r\n    private readonly _length: number;\r\n    private _position: number;\r\n    private readonly _nativeDataStream: INativeDataStream;\r\n\r\n    // Must be multiple of 4!\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private static readonly DEFAULT_BUFFER_SIZE = 65536;\r\n\r\n    constructor() {\r\n        const buffer = new ArrayBuffer(NativeDataStream.DEFAULT_BUFFER_SIZE);\r\n        this._uint32s = new Uint32Array(buffer);\r\n        this._int32s = new Int32Array(buffer);\r\n        this._float32s = new Float32Array(buffer);\r\n\r\n        this._length = NativeDataStream.DEFAULT_BUFFER_SIZE / 4;\r\n        this._position = 0;\r\n\r\n        this._nativeDataStream = new _native.NativeDataStream(() => {\r\n            this._flush();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Writes a uint32 to the stream\r\n     * @param value the value to write\r\n     */\r\n    public writeUint32(value: number): void {\r\n        this._flushIfNecessary(1);\r\n        this._uint32s[this._position++] = value;\r\n    }\r\n\r\n    /**\r\n     * Writes an int32 to the stream\r\n     * @param value the value to write\r\n     */\r\n    public writeInt32(value: number): void {\r\n        this._flushIfNecessary(1);\r\n        this._int32s[this._position++] = value;\r\n    }\r\n\r\n    /**\r\n     * Writes a float32 to the stream\r\n     * @param value the value to write\r\n     */\r\n    public writeFloat32(value: number): void {\r\n        this._flushIfNecessary(1);\r\n        this._float32s[this._position++] = value;\r\n    }\r\n\r\n    /**\r\n     * Writes a uint32 array to the stream\r\n     * @param values the values to write\r\n     */\r\n    public writeUint32Array(values: Uint32Array): void {\r\n        this._flushIfNecessary(1 + values.length);\r\n        this._uint32s[this._position++] = values.length;\r\n        this._uint32s.set(values, this._position);\r\n        this._position += values.length;\r\n    }\r\n\r\n    /**\r\n     * Writes an int32 array to the stream\r\n     * @param values the values to write\r\n     */\r\n    public writeInt32Array(values: Int32Array): void {\r\n        this._flushIfNecessary(1 + values.length);\r\n        this._uint32s[this._position++] = values.length;\r\n        this._int32s.set(values, this._position);\r\n        this._position += values.length;\r\n    }\r\n\r\n    /**\r\n     * Writes a float32 array to the stream\r\n     * @param values the values to write\r\n     */\r\n    public writeFloat32Array(values: DeepImmutable<FloatArray>): void {\r\n        this._flushIfNecessary(1 + values.length);\r\n        this._uint32s[this._position++] = values.length;\r\n        this._float32s.set(values, this._position);\r\n        this._position += values.length;\r\n    }\r\n\r\n    /**\r\n     * Writes native data to the stream\r\n     * @param handle the handle to the native data\r\n     */\r\n    public writeNativeData(handle: NativeData) {\r\n        this._flushIfNecessary(handle.length);\r\n        this._uint32s.set(handle, this._position);\r\n        this._position += handle.length;\r\n    }\r\n\r\n    /**\r\n     * Writes a boolean to the stream\r\n     * @param value the value to write\r\n     */\r\n    public writeBoolean(value: boolean) {\r\n        this.writeUint32(value ? 1 : 0);\r\n    }\r\n\r\n    private _flushIfNecessary(required: number): void {\r\n        if (this._position + required > this._length) {\r\n            this._flush();\r\n        }\r\n    }\r\n\r\n    private _flush(): void {\r\n        this._nativeDataStream.writeBuffer(this._uint32s.buffer, this._position);\r\n        this._position = 0;\r\n    }\r\n}\r\n"]}