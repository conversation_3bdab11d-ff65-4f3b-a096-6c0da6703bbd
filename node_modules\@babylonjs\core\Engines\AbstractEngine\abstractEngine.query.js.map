{"version": 3, "file": "abstractEngine.query.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.query.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,qCAAiC;AAExD,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAKnD,gBAAgB;AAChB,gEAAgE;AAChE,MAAM,OAAO,qBAAqB;IAAlC;QACI,gBAAgB;QACT,kCAA6B,GAAG,CAAC,CAAC;QAEzC,gBAAgB;QACT,+BAA0B,GAAG,KAAK,CAAC;QAE1C,gBAAgB;QACT,eAAU,GAAG,KAAK,CAAC;QAE1B,gBAAgB;QACT,wBAAmB,GAAG,CAAC,CAAC,CAAC;QAEhC,gBAAgB;QACT,kBAAa,GAAG,YAAY,CAAC,mBAAmB,CAAC;QAExD,gBAAgB;QACT,gCAA2B,GAAG,YAAY,CAAC,qCAAqC,CAAC;QAExF,gBAAgB;QACT,+BAA0B,GAAG,KAAK,CAAC;IAC9C,CAAC;CAAA;AA6CD,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG;IACnC,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,KAAqB;IAClE,mDAAmD;IACnD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAAU,KAAqB;IAC7E,mDAAmD;IACnD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,KAAqB;IACrE,mDAAmD;IACnD,OAAO,CAAC,CAAC;AACb,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,aAAqB,EAAE,KAAqB;IACjG,mDAAmD;IACnD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,aAAqB;IACxE,mDAAmD;IACnD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AA6DF,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,4BAA4B,EAAE;IACxE,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC;IACjE,CAAC;IACD,GAAG,EAAE,UAA8B,KAAc;QAC7C,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,GAAG,KAAK,CAAC;IAClE,CAAC;IACD,UAAU,EAAE,KAAK;IACjB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,uBAAuB,EAAE;IACnE,GAAG,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC/B,IAAI,CAAC,sBAAsB,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAC9D,CAAC;QACD,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IACD,UAAU,EAAE,KAAK;IACjB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,YAAY,EAAE;IACxD,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC;IACjD,CAAC;IACD,GAAG,EAAE,UAA8B,KAAc;QAC7C,IAAI,CAAC,qBAAqB,CAAC,UAAU,GAAG,KAAK,CAAC;IAClD,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,6BAA6B,EAAE;IACzE,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,CAAC;IAClE,CAAC;IACD,GAAG,EAAE,UAA8B,KAAa;QAC5C,IAAI,CAAC,qBAAqB,CAAC,2BAA2B,GAAG,KAAK,CAAC;IACnE,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,eAAe,EAAE;IAC3D,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC;IACpD,CAAC;IACD,GAAG,EAAE,UAA8B,KAAa;QAC5C,IAAI,CAAC,qBAAqB,CAAC,aAAa,GAAG,KAAK,CAAC;IACrD,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,qBAAqB,EAAE;IACjE,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAC;IAC1D,CAAC;IACD,GAAG,EAAE,UAA8B,KAAa;QAC5C,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,GAAG,KAAK,CAAC;IAC3D,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,EAAE,4BAA4B,EAAE;IACxE,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,CAAC;IACjE,CAAC;IACD,GAAG,EAAE,UAA8B,KAAc;QAC7C,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,GAAG,KAAK,CAAC;IAClE,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,8EAA8E;AAC9E,YAAY,CAAC,SAAS,CAAC,oBAAoB,GAAG;IAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC;IAE/C,IAAI,WAAW,CAAC,aAAa,KAAK,YAAY,CAAC,mBAAmB,EAAE,CAAC;QACjE,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC;QAC/B,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAEhC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,qBAAqB,EAAE,CAAC;QAC1C,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC;QAC/B,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;QACjC,uCAAuC;QACvC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC;QAC/B,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;QACzG,MAAM,yBAAyB,GAAG,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtF,IAAI,yBAAyB,EAAE,CAAC;YAC5B,MAAM,oBAAoB,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAEzE,WAAW,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC/C,WAAW,CAAC,6BAA6B,GAAG,CAAC,CAAC;YAC9C,WAAW,CAAC,UAAU,GAAG,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QACrE,CAAC;aAAM,CAAC;YACJ,WAAW,CAAC,6BAA6B,EAAE,CAAC;YAE5C,IAAI,WAAW,CAAC,mBAAmB,KAAK,CAAC,CAAC,IAAI,WAAW,CAAC,6BAA6B,GAAG,WAAW,CAAC,mBAAmB,EAAE,CAAC;gBACxH,WAAW,CAAC,0BAA0B,GAAG,KAAK,CAAC;gBAC/C,WAAW,CAAC,6BAA6B,GAAG,CAAC,CAAC;gBAE9C,oHAAoH;gBACpH,mDAAmD;gBACnD,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,aAAa,KAAK,YAAY,CAAC,yBAAyB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC;YACnI,CAAC;iBAAM,CAAC;gBACJ,OAAO,WAAW,CAAC,aAAa,KAAK,YAAY,CAAC,yBAAyB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC;YACjH,CAAC;QACL,CAAC;IACL,CAAC;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAC9B,IAAI,KAAK,CAAC,sBAAsB,EAAE,CAAC;QAC/B,MAAM,4BAA4B,GAAG,KAAK,CAAC,sBAAsB,EAAE,CAAC;QAEpE,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QAChD,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,MAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,2BAA2B,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;YACpH,4BAA4B,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,CAAC,iBAAiB,CAAC,WAAW,CAAC,2BAA2B,CAAC,CAAC;YAClE,IAAI,CAAC,qBAAqB,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACjE,CAAC;IACL,CAAC;IAED,OAAO,WAAW,CAAC,UAAU,CAAC;AAClC,CAAC,CAAC", "sourcesContent": ["import { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { AbstractEngine } from \"../abstractEngine\";\r\n\r\n/** @internal */\r\nexport type OcclusionQuery = WebGLQuery | number;\r\n\r\n/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class _OcclusionDataStorage {\r\n    /** @internal */\r\n    public occlusionInternalRetryCounter = 0;\r\n\r\n    /** @internal */\r\n    public isOcclusionQueryInProgress = false;\r\n\r\n    /** @internal */\r\n    public isOccluded = false;\r\n\r\n    /** @internal */\r\n    public occlusionRetryCount = -1;\r\n\r\n    /** @internal */\r\n    public occlusionType = AbstractMesh.OCCLUSION_TYPE_NONE;\r\n\r\n    /** @internal */\r\n    public occlusionQueryAlgorithmType = AbstractMesh.OCCLUSION_ALGORITHM_TYPE_CONSERVATIVE;\r\n\r\n    /** @internal */\r\n    public forceRenderingWhenOccluded = false;\r\n}\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Create a new webGL query (you must be sure that queries are supported by checking getCaps() function)\r\n         * @returns the new query\r\n         */\r\n        createQuery(): Nullable<OcclusionQuery>;\r\n        /**\r\n         * Delete and release a webGL query\r\n         * @param query defines the query to delete\r\n         * @returns the current engine\r\n         */\r\n        deleteQuery(query: OcclusionQuery): AbstractEngine /**\r\n         * Check if a given query has resolved and got its value\r\n         * @param query defines the query to check\r\n         * @returns true if the query got its value\r\n         */;\r\n        isQueryResultAvailable(query: OcclusionQuery): boolean;\r\n        /**\r\n         * Gets the value of a given query\r\n         * @param query defines the query to check\r\n         * @returns the value of the query\r\n         */\r\n        getQueryResult(query: OcclusionQuery): number;\r\n        /**\r\n         * Initiates an occlusion query\r\n         * @param algorithmType defines the algorithm to use\r\n         * @param query defines the query to use\r\n         * @returns the current engine\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/occlusionQueries\r\n         */\r\n        beginOcclusionQuery(algorithmType: number, query: OcclusionQuery): boolean;\r\n        /**\r\n         * Ends an occlusion query\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/occlusionQueries\r\n         * @param algorithmType defines the algorithm to use\r\n         * @returns the current engine\r\n         */\r\n        endOcclusionQuery(algorithmType: number): AbstractEngine;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype.createQuery = function (): Nullable<OcclusionQuery> {\r\n    return null;\r\n};\r\n\r\nAbstractEngine.prototype.deleteQuery = function (query: OcclusionQuery): AbstractEngine {\r\n    // Do nothing. Must be implemented by child classes\r\n    return this;\r\n};\r\n\r\nAbstractEngine.prototype.isQueryResultAvailable = function (query: OcclusionQuery): boolean {\r\n    // Do nothing. Must be implemented by child classes\r\n    return false;\r\n};\r\n\r\nAbstractEngine.prototype.getQueryResult = function (query: OcclusionQuery): number {\r\n    // Do nothing. Must be implemented by child classes\r\n    return 0;\r\n};\r\n\r\nAbstractEngine.prototype.beginOcclusionQuery = function (algorithmType: number, query: OcclusionQuery): boolean {\r\n    // Do nothing. Must be implemented by child classes\r\n    return false;\r\n};\r\n\r\nAbstractEngine.prototype.endOcclusionQuery = function (algorithmType: number): AbstractEngine {\r\n    // Do nothing. Must be implemented by child classes\r\n    return this;\r\n};\r\n\r\ndeclare module \"../../Meshes/abstractMesh\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractMesh {\r\n        /**\r\n         * Backing filed\r\n         * @internal\r\n         */\r\n        // eslint-disable-next-line @typescript-eslint/naming-convention\r\n        __occlusionDataStorage: _OcclusionDataStorage;\r\n\r\n        /**\r\n         * Access property\r\n         * @internal\r\n         */\r\n        _occlusionDataStorage: _OcclusionDataStorage;\r\n\r\n        /**\r\n         * This number indicates the number of allowed retries before stop the occlusion query, this is useful if the occlusion query is taking long time before to the query result is retrieved, the query result indicates if the object is visible within the scene or not and based on that Babylon.Js engine decides to show or hide the object.\r\n         * The default value is -1 which means don't break the query and wait till the result\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/occlusionQueries\r\n         */\r\n        occlusionRetryCount: number;\r\n\r\n        /**\r\n         * This property is responsible for starting the occlusion query within the Mesh or not, this property is also used to determine what should happen when the occlusionRetryCount is reached. It has supports 3 values:\r\n         * * OCCLUSION_TYPE_NONE (Default Value): this option means no occlusion query within the Mesh.\r\n         * * OCCLUSION_TYPE_OPTIMISTIC: this option is means use occlusion query and if occlusionRetryCount is reached and the query is broken show the mesh.\r\n         * * OCCLUSION_TYPE_STRICT: this option is means use occlusion query and if occlusionRetryCount is reached and the query is broken restore the last state of the mesh occlusion if the mesh was visible then show the mesh if was hidden then hide don't show.\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/occlusionQueries\r\n         */\r\n        occlusionType: number;\r\n\r\n        /**\r\n         * This property determines the type of occlusion query algorithm to run in WebGl, you can use:\r\n         * * AbstractMesh.OCCLUSION_ALGORITHM_TYPE_ACCURATE which is mapped to GL_ANY_SAMPLES_PASSED.\r\n         * * AbstractMesh.OCCLUSION_ALGORITHM_TYPE_CONSERVATIVE (Default Value) which is mapped to GL_ANY_SAMPLES_PASSED_CONSERVATIVE which is a false positive algorithm that is faster than GL_ANY_SAMPLES_PASSED but less accurate.\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/occlusionQueries\r\n         */\r\n        occlusionQueryAlgorithmType: number;\r\n\r\n        /**\r\n         * Gets or sets whether the mesh is occluded or not, it is used also to set the initial state of the mesh to be occluded or not\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/occlusionQueries\r\n         */\r\n        isOccluded: boolean;\r\n\r\n        /**\r\n         * Flag to check the progress status of the query\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/occlusionQueries\r\n         */\r\n        isOcclusionQueryInProgress: boolean;\r\n\r\n        /**\r\n         * Flag to force rendering the mesh even if occluded\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/occlusionQueries\r\n         */\r\n        forceRenderingWhenOccluded: boolean;\r\n    }\r\n}\r\nObject.defineProperty(AbstractMesh.prototype, \"isOcclusionQueryInProgress\", {\r\n    get: function (this: AbstractMesh) {\r\n        return this._occlusionDataStorage.isOcclusionQueryInProgress;\r\n    },\r\n    set: function (this: AbstractMesh, value: boolean) {\r\n        this._occlusionDataStorage.isOcclusionQueryInProgress = value;\r\n    },\r\n    enumerable: false,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(AbstractMesh.prototype, \"_occlusionDataStorage\", {\r\n    get: function (this: AbstractMesh) {\r\n        if (!this.__occlusionDataStorage) {\r\n            this.__occlusionDataStorage = new _OcclusionDataStorage();\r\n        }\r\n        return this.__occlusionDataStorage;\r\n    },\r\n    enumerable: false,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(AbstractMesh.prototype, \"isOccluded\", {\r\n    get: function (this: AbstractMesh) {\r\n        return this._occlusionDataStorage.isOccluded;\r\n    },\r\n    set: function (this: AbstractMesh, value: boolean) {\r\n        this._occlusionDataStorage.isOccluded = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(AbstractMesh.prototype, \"occlusionQueryAlgorithmType\", {\r\n    get: function (this: AbstractMesh) {\r\n        return this._occlusionDataStorage.occlusionQueryAlgorithmType;\r\n    },\r\n    set: function (this: AbstractMesh, value: number) {\r\n        this._occlusionDataStorage.occlusionQueryAlgorithmType = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(AbstractMesh.prototype, \"occlusionType\", {\r\n    get: function (this: AbstractMesh) {\r\n        return this._occlusionDataStorage.occlusionType;\r\n    },\r\n    set: function (this: AbstractMesh, value: number) {\r\n        this._occlusionDataStorage.occlusionType = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(AbstractMesh.prototype, \"occlusionRetryCount\", {\r\n    get: function (this: AbstractMesh) {\r\n        return this._occlusionDataStorage.occlusionRetryCount;\r\n    },\r\n    set: function (this: AbstractMesh, value: number) {\r\n        this._occlusionDataStorage.occlusionRetryCount = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(AbstractMesh.prototype, \"forceRenderingWhenOccluded\", {\r\n    get: function (this: AbstractMesh) {\r\n        return this._occlusionDataStorage.forceRenderingWhenOccluded;\r\n    },\r\n    set: function (this: AbstractMesh, value: boolean) {\r\n        this._occlusionDataStorage.forceRenderingWhenOccluded = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\n// We also need to update AbstractMesh as there is a portion of the code there\r\nAbstractMesh.prototype._checkOcclusionQuery = function () {\r\n    const dataStorage = this._occlusionDataStorage;\r\n\r\n    if (dataStorage.occlusionType === AbstractMesh.OCCLUSION_TYPE_NONE) {\r\n        dataStorage.isOccluded = false;\r\n        return false;\r\n    }\r\n\r\n    const engine = this.getEngine();\r\n\r\n    if (!engine.getCaps().supportOcclusionQuery) {\r\n        dataStorage.isOccluded = false;\r\n        return false;\r\n    }\r\n\r\n    if (!engine.isQueryResultAvailable) {\r\n        // Occlusion query where not referenced\r\n        dataStorage.isOccluded = false;\r\n        return false;\r\n    }\r\n\r\n    if (this.isOcclusionQueryInProgress && this._occlusionQuery !== null && this._occlusionQuery !== undefined) {\r\n        const isOcclusionQueryAvailable = engine.isQueryResultAvailable(this._occlusionQuery);\r\n        if (isOcclusionQueryAvailable) {\r\n            const occlusionQueryResult = engine.getQueryResult(this._occlusionQuery);\r\n\r\n            dataStorage.isOcclusionQueryInProgress = false;\r\n            dataStorage.occlusionInternalRetryCounter = 0;\r\n            dataStorage.isOccluded = occlusionQueryResult > 0 ? false : true;\r\n        } else {\r\n            dataStorage.occlusionInternalRetryCounter++;\r\n\r\n            if (dataStorage.occlusionRetryCount !== -1 && dataStorage.occlusionInternalRetryCounter > dataStorage.occlusionRetryCount) {\r\n                dataStorage.isOcclusionQueryInProgress = false;\r\n                dataStorage.occlusionInternalRetryCounter = 0;\r\n\r\n                // if optimistic set isOccluded to false regardless of the status of isOccluded. (Render in the current render loop)\r\n                // if strict continue the last state of the object.\r\n                dataStorage.isOccluded = dataStorage.occlusionType === AbstractMesh.OCCLUSION_TYPE_OPTIMISTIC ? false : dataStorage.isOccluded;\r\n            } else {\r\n                return dataStorage.occlusionType === AbstractMesh.OCCLUSION_TYPE_OPTIMISTIC ? false : dataStorage.isOccluded;\r\n            }\r\n        }\r\n    }\r\n\r\n    const scene = this.getScene();\r\n    if (scene.getBoundingBoxRenderer) {\r\n        const occlusionBoundingBoxRenderer = scene.getBoundingBoxRenderer();\r\n\r\n        if (this._occlusionQuery === null) {\r\n            this._occlusionQuery = engine.createQuery();\r\n        }\r\n\r\n        if (this._occlusionQuery && engine.beginOcclusionQuery(dataStorage.occlusionQueryAlgorithmType, this._occlusionQuery)) {\r\n            occlusionBoundingBoxRenderer.renderOcclusionBoundingBox(this);\r\n            engine.endOcclusionQuery(dataStorage.occlusionQueryAlgorithmType);\r\n            this._occlusionDataStorage.isOcclusionQueryInProgress = true;\r\n        }\r\n    }\r\n\r\n    return dataStorage.isOccluded;\r\n};\r\n"]}