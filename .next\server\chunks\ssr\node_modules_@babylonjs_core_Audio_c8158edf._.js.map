{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Audio/Interfaces/IAudioEngine.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Audio/Interfaces/IAudioEngine.ts"], "sourcesContent": ["import type { Observable } from \"../../Misc/observable\";\r\nimport type { IDisposable } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Analyser } from \"../analyser\";\r\n\r\n/**\r\n * This represents an audio engine and it is responsible\r\n * to play, synchronize and analyse sounds throughout the application.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport interface IAudioEngine extends IDisposable {\r\n    /**\r\n     * Gets whether the current host supports Web Audio and thus could create AudioContexts.\r\n     */\r\n    readonly canUseWebAudio: boolean;\r\n\r\n    /**\r\n     * Gets the current AudioContext if available.\r\n     */\r\n    readonly audioContext: Nullable<AudioContext>;\r\n\r\n    /**\r\n     * The master gain node defines the global audio volume of your audio engine.\r\n     */\r\n    readonly masterGain: GainNode;\r\n\r\n    /**\r\n     * Gets whether or not mp3 are supported by your browser.\r\n     */\r\n    readonly isMP3supported: boolean;\r\n\r\n    /**\r\n     * Gets whether or not ogg are supported by your browser.\r\n     */\r\n    readonly isOGGsupported: boolean;\r\n\r\n    /**\r\n     * Defines if Babylon should emit a warning if WebAudio is not supported.\r\n     * @ignoreNaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    WarnedWebAudioUnsupported: boolean;\r\n\r\n    /**\r\n     * Defines if the audio engine relies on a custom unlocked button.\r\n     * In this case, the embedded button will not be displayed.\r\n     */\r\n    useCustomUnlockedButton: boolean;\r\n\r\n    /**\r\n     * Gets whether or not the audio engine is unlocked (require first a user gesture on some browser).\r\n     */\r\n    readonly unlocked: boolean;\r\n\r\n    /**\r\n     * Event raised when audio has been unlocked on the browser.\r\n     */\r\n    onAudioUnlockedObservable: Observable<IAudioEngine>;\r\n\r\n    /**\r\n     * Event raised when audio has been locked on the browser.\r\n     */\r\n    onAudioLockedObservable: Observable<IAudioEngine>;\r\n\r\n    /**\r\n     * Flags the audio engine in Locked state.\r\n     * This happens due to new browser policies preventing audio to autoplay.\r\n     */\r\n    lock(): void;\r\n\r\n    /**\r\n     * Unlocks the audio engine once a user action has been done on the dom.\r\n     * This is helpful to resume play once browser policies have been satisfied.\r\n     */\r\n    unlock(): void;\r\n\r\n    /**\r\n     * Gets the global volume sets on the master gain.\r\n     * @returns the global volume if set or -1 otherwise\r\n     */\r\n    getGlobalVolume(): number;\r\n\r\n    /**\r\n     * Sets the global volume of your experience (sets on the master gain).\r\n     * @param newVolume Defines the new global volume of the application\r\n     */\r\n    setGlobalVolume(newVolume: number): void;\r\n\r\n    /**\r\n     * Connect the audio engine to an audio analyser allowing some amazing\r\n     * synchronization between the sounds/music and your visualization (VuMeter for instance).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser\r\n     * @param analyser The analyser to connect to the engine\r\n     */\r\n    connectToAnalyser(analyser: Analyser): void;\r\n\r\n    /** @internal */\r\n    _resumeAudioContextOnStateChange(): void;\r\n}\r\n"], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Audio/Interfaces/ISoundOptions.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Audio/Interfaces/ISoundOptions.ts"], "sourcesContent": ["/**\r\n * Interface used to define options for Sound class\r\n */\r\nexport interface ISoundOptions {\r\n    /**\r\n     * Does the sound autoplay once loaded.\r\n     */\r\n    autoplay?: boolean;\r\n    /**\r\n     * Does the sound loop after it finishes playing once.\r\n     */\r\n    loop?: boolean;\r\n    /**\r\n     * Sound's volume\r\n     */\r\n    volume?: number;\r\n    /**\r\n     * Is it a spatial sound?\r\n     */\r\n    spatialSound?: boolean;\r\n    /**\r\n     * Maximum distance to hear that sound\r\n     */\r\n    maxDistance?: number;\r\n    /**\r\n     * Uses user defined attenuation function\r\n     */\r\n    useCustomAttenuation?: boolean;\r\n    /**\r\n     * Define the roll off factor of spatial sounds.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    rolloffFactor?: number;\r\n    /**\r\n     * Define the reference distance the sound should be heard perfectly.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    refDistance?: number;\r\n    /**\r\n     * Define the distance attenuation model the sound will follow.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    distanceModel?: string;\r\n    /**\r\n     * Defines the playback speed (1 by default)\r\n     */\r\n    playbackRate?: number;\r\n    /**\r\n     * Defines if the sound is from a streaming source\r\n     */\r\n    streaming?: boolean;\r\n    /**\r\n     * Defines an optional length (in seconds) inside the sound file\r\n     */\r\n    length?: number;\r\n    /**\r\n     * Defines an optional offset (in seconds) inside the sound file\r\n     */\r\n    offset?: number;\r\n    /**\r\n     * If true, URLs will not be required to state the audio file codec to use.\r\n     */\r\n    skipCodecCheck?: boolean;\r\n}\r\n"], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Audio/analyser.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Audio/analyser.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { IAudioEngine } from \"./Interfaces/IAudioEngine\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { AbstractEngine } from \"core/Engines/abstractEngine\";\r\n\r\n/**\r\n * Class used to work with sound analyzer using fast fourier transform (FFT)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport class Analyser {\r\n    /**\r\n     * Gets or sets the smoothing\r\n     */\r\n    public SMOOTHING = 0.75;\r\n    /**\r\n     * Gets or sets the FFT table size\r\n     */\r\n    public FFT_SIZE = 512;\r\n    /**\r\n     * Gets or sets the bar graph amplitude\r\n     */\r\n    public BARGRAPHAMPLITUDE = 256;\r\n    /**\r\n     * Gets or sets the position of the debug canvas\r\n     */\r\n    public DEBUGCANVASPOS = { x: 20, y: 20 };\r\n    /**\r\n     * Gets or sets the debug canvas size\r\n     */\r\n    public DEBUGCANVASSIZE = { width: 320, height: 200 };\r\n\r\n    private _byteFreqs: Uint8Array;\r\n    private _byteTime: Uint8Array;\r\n    private _floatFreqs: Float32Array;\r\n    private _webAudioAnalyser: AnalyserNode;\r\n    private _debugCanvas: Nullable<HTMLCanvasElement>;\r\n    private _debugCanvasContext: Nullable<CanvasRenderingContext2D>;\r\n    private _scene: Scene;\r\n    private _registerFunc: Nullable<() => void>;\r\n    private _audioEngine: IAudioEngine;\r\n\r\n    /**\r\n     * Creates a new analyser\r\n     * @param scene defines hosting scene\r\n     */\r\n    constructor(scene?: Nullable<Scene>) {\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this._scene = scene;\r\n        if (!AbstractEngine.audioEngine) {\r\n            Tools.Warn(\"No audio engine initialized, failed to create an audio analyser\");\r\n            return;\r\n        }\r\n        this._audioEngine = AbstractEngine.audioEngine;\r\n        if (this._audioEngine.canUseWebAudio && this._audioEngine.audioContext) {\r\n            this._webAudioAnalyser = this._audioEngine.audioContext.createAnalyser();\r\n            this._webAudioAnalyser.minDecibels = -140;\r\n            this._webAudioAnalyser.maxDecibels = 0;\r\n            this._byteFreqs = new Uint8Array(this._webAudioAnalyser.frequencyBinCount);\r\n            this._byteTime = new Uint8Array(this._webAudioAnalyser.frequencyBinCount);\r\n            this._floatFreqs = new Float32Array(this._webAudioAnalyser.frequencyBinCount);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the number of data values you will have to play with for the visualization\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/frequencyBinCount\r\n     * @returns a number\r\n     */\r\n    public getFrequencyBinCount(): number {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            return this._webAudioAnalyser.frequencyBinCount;\r\n        } else {\r\n            return 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the current frequency data as a byte array\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/getByteFrequencyData\r\n     * @returns a Uint8Array\r\n     */\r\n    public getByteFrequencyData(): Uint8Array {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            this._webAudioAnalyser.smoothingTimeConstant = this.SMOOTHING;\r\n            this._webAudioAnalyser.fftSize = this.FFT_SIZE;\r\n            this._webAudioAnalyser.getByteFrequencyData(this._byteFreqs);\r\n        }\r\n        return this._byteFreqs;\r\n    }\r\n\r\n    /**\r\n     * Gets the current waveform as a byte array\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/getByteTimeDomainData\r\n     * @returns a Uint8Array\r\n     */\r\n    public getByteTimeDomainData(): Uint8Array {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            this._webAudioAnalyser.smoothingTimeConstant = this.SMOOTHING;\r\n            this._webAudioAnalyser.fftSize = this.FFT_SIZE;\r\n            this._webAudioAnalyser.getByteTimeDomainData(this._byteTime);\r\n        }\r\n        return this._byteTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the current frequency data as a float array\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/getByteFrequencyData\r\n     * @returns a Float32Array\r\n     */\r\n    public getFloatFrequencyData(): Float32Array {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            this._webAudioAnalyser.smoothingTimeConstant = this.SMOOTHING;\r\n            this._webAudioAnalyser.fftSize = this.FFT_SIZE;\r\n            this._webAudioAnalyser.getFloatFrequencyData(this._floatFreqs);\r\n        }\r\n        return this._floatFreqs;\r\n    }\r\n\r\n    /**\r\n     * Renders the debug canvas\r\n     */\r\n    public drawDebugCanvas() {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            if (!this._debugCanvas) {\r\n                this._debugCanvas = document.createElement(\"canvas\");\r\n                this._debugCanvas.width = this.DEBUGCANVASSIZE.width;\r\n                this._debugCanvas.height = this.DEBUGCANVASSIZE.height;\r\n                this._debugCanvas.style.position = \"absolute\";\r\n                this._debugCanvas.style.top = this.DEBUGCANVASPOS.y + \"px\";\r\n                this._debugCanvas.style.left = this.DEBUGCANVASPOS.x + \"px\";\r\n                this._debugCanvasContext = this._debugCanvas.getContext(\"2d\");\r\n                document.body.appendChild(this._debugCanvas);\r\n                this._registerFunc = () => {\r\n                    this.drawDebugCanvas();\r\n                };\r\n                this._scene.registerBeforeRender(this._registerFunc);\r\n            }\r\n            if (this._registerFunc && this._debugCanvasContext) {\r\n                const workingArray = this.getByteFrequencyData();\r\n\r\n                this._debugCanvasContext.fillStyle = \"rgb(0, 0, 0)\";\r\n                this._debugCanvasContext.fillRect(0, 0, this.DEBUGCANVASSIZE.width, this.DEBUGCANVASSIZE.height);\r\n\r\n                // Draw the frequency domain chart.\r\n                for (let i = 0; i < this.getFrequencyBinCount(); i++) {\r\n                    const value = workingArray[i];\r\n                    const percent = value / this.BARGRAPHAMPLITUDE;\r\n                    const height = this.DEBUGCANVASSIZE.height * percent;\r\n                    const offset = this.DEBUGCANVASSIZE.height - height - 1;\r\n                    const barWidth = this.DEBUGCANVASSIZE.width / this.getFrequencyBinCount();\r\n                    const hue = (i / this.getFrequencyBinCount()) * 360;\r\n                    this._debugCanvasContext.fillStyle = \"hsl(\" + hue + \", 100%, 50%)\";\r\n                    this._debugCanvasContext.fillRect(i * barWidth, offset, barWidth, height);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stops rendering the debug canvas and removes it\r\n     */\r\n    public stopDebugCanvas() {\r\n        if (this._debugCanvas) {\r\n            if (this._registerFunc) {\r\n                this._scene.unregisterBeforeRender(this._registerFunc);\r\n                this._registerFunc = null;\r\n            }\r\n            document.body.removeChild(this._debugCanvas);\r\n            this._debugCanvas = null;\r\n            this._debugCanvasContext = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connects two audio nodes\r\n     * @param inputAudioNode defines first node to connect\r\n     * @param outputAudioNode defines second node to connect\r\n     */\r\n    public connectAudioNodes(inputAudioNode: AudioNode, outputAudioNode: AudioNode) {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            inputAudioNode.connect(this._webAudioAnalyser);\r\n            this._webAudioAnalyser.connect(outputAudioNode);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Releases all associated resources\r\n     */\r\n    public dispose() {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            this._webAudioAnalyser.disconnect();\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAIA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,qCAAoC;;;;AAMvD,MAAO,QAAQ;IAgCjB;;;OAGG,CACH,YAAY,KAAuB,CAAA;QAnCnC;;WAEG,CACI,IAAA,CAAA,SAAS,GAAG,IAAI,CAAC;QACxB;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAG,GAAG,CAAC;QACtB;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAG,GAAG,CAAC;QAC/B;;WAEG,CACI,IAAA,CAAA,cAAc,GAAG;YAAE,CAAC,EAAE,EAAE;YAAE,CAAC,EAAE,EAAE;QAAA,CAAE,CAAC;QACzC;;WAEG,CACI,IAAA,CAAA,eAAe,GAAG;YAAE,KAAK,EAAE,GAAG;YAAE,MAAM,EAAE,GAAG;QAAA,CAAE,CAAC;QAiBjD,KAAK,GAAG,KAAK,kKAAI,cAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,kKAAC,iBAAc,CAAC,WAAW,EAAE,CAAC;iKAC9B,QAAK,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;YAC9E,OAAO;QACX,CAAC;QACD,IAAI,CAAC,YAAY,oKAAG,iBAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACrE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACzE,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC;YAC1C,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,CAAC;YACvC,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAC3E,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAC1E,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAClF,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,oBAAoB,GAAA;QACvB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;QACpD,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,CAAC;QACb,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,oBAAoB,GAAA;QACvB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9D,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;OAIG,CACI,qBAAqB,GAAA;QACxB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9D,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;;OAIG,CACI,qBAAqB,GAAA;QACxB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9D,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG,CACI,eAAe,GAAA;QAClB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrB,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBACvD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAC9C,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC3D,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC5D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC9D,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC7C,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE;oBACtB,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC3B,CAAC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzD,CAAC;YACD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACjD,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAEjD,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC;gBACpD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAEjG,mCAAmC;gBACnC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC;oBACnD,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;oBAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,OAAO,CAAC;oBACrD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;oBACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC1E,MAAM,GAAG,GAAG,AAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC,EAAG,GAAG,CAAC;oBACpD,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,MAAM,GAAG,GAAG,GAAG,cAAc,CAAC;oBACnE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC9E,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,eAAe,GAAA;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACvD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC9B,CAAC;YACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,cAAyB,EAAE,eAA0B,EAAA;QAC1E,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC;QACxC,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Audio/audioEngine.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Audio/audioEngine.ts"], "sourcesContent": ["import type { Analyser } from \"./analyser\";\r\n\r\nimport type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport type { IAudioEngine } from \"./Interfaces/IAudioEngine\";\r\nimport { _WebAudioEngine } from \"../AudioV2/webAudio/webAudioEngine\";\r\n\r\n// Sets the default audio engine to Babylon.js\r\nAbstractEngine.AudioEngineFactory = (\r\n    hostElement: Nullable<HTMLElement>,\r\n    audioContext: Nullable<AudioContext>,\r\n    audioDestination: Nullable<AudioDestinationNode | MediaStreamAudioDestinationNode>\r\n) => {\r\n    return new AudioEngine(hostElement, audioContext, audioDestination);\r\n};\r\n\r\n/**\r\n * This represents the default audio engine used in babylon.\r\n * It is responsible to play, synchronize and analyse sounds throughout the  application.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport class AudioEngine implements IAudioEngine {\r\n    private _audioContext: Nullable<AudioContext> = null;\r\n    private _masterGain: GainNode;\r\n    private _tryToRun = false;\r\n    private _useCustomUnlockedButton: boolean = false;\r\n\r\n    /**\r\n     * Gets whether the current host supports Web Audio and thus could create AudioContexts.\r\n     */\r\n    public canUseWebAudio: boolean = true;\r\n\r\n    /**\r\n     * The master gain node defines the global audio volume of your audio engine.\r\n     */\r\n    public get masterGain(): GainNode {\r\n        return this._masterGain;\r\n    }\r\n\r\n    public set masterGain(value: GainNode) {\r\n        this._masterGain = this._v2.mainOut._inNode = value;\r\n    }\r\n\r\n    /**\r\n     * Defines if Babylon should emit a warning if WebAudio is not supported.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public WarnedWebAudioUnsupported: boolean = false;\r\n\r\n    /**\r\n     * Gets whether or not mp3 are supported by your browser.\r\n     */\r\n    public isMP3supported: boolean = false;\r\n\r\n    /**\r\n     * Gets whether or not ogg are supported by your browser.\r\n     */\r\n    public isOGGsupported: boolean = false;\r\n\r\n    /**\r\n     * Gets whether audio has been unlocked on the device.\r\n     * Some Browsers have strong restrictions about Audio and won't autoplay unless\r\n     * a user interaction has happened.\r\n     */\r\n    public unlocked: boolean = false;\r\n\r\n    /**\r\n     * Defines if the audio engine relies on a custom unlocked button.\r\n     * In this case, the embedded button will not be displayed.\r\n     */\r\n    public get useCustomUnlockedButton(): boolean {\r\n        return this._useCustomUnlockedButton;\r\n    }\r\n\r\n    public set useCustomUnlockedButton(value: boolean) {\r\n        this._useCustomUnlockedButton = value;\r\n        this._v2._unmuteUIEnabled = !value;\r\n    }\r\n\r\n    /**\r\n     * Event raised when audio has been unlocked on the browser.\r\n     */\r\n    public onAudioUnlockedObservable = new Observable<IAudioEngine>();\r\n\r\n    /**\r\n     * Event raised when audio has been locked on the browser.\r\n     */\r\n    public onAudioLockedObservable = new Observable<IAudioEngine>();\r\n\r\n    /** @internal */\r\n    public _v2: _WebAudioEngine;\r\n\r\n    /**\r\n     * Gets the current AudioContext if available.\r\n     */\r\n    public get audioContext(): Nullable<AudioContext> {\r\n        if (this._v2.state === \"running\") {\r\n            // Do not wait for the promise to unlock.\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._triggerRunningStateAsync();\r\n        }\r\n        return this._v2._audioContext;\r\n    }\r\n\r\n    private _connectedAnalyser: Nullable<Analyser>;\r\n\r\n    /**\r\n     * Instantiates a new audio engine.\r\n     *\r\n     * @param hostElement defines the host element where to display the mute icon if necessary\r\n     * @param audioContext defines the audio context to be used by the audio engine\r\n     * @param audioDestination defines the audio destination node to be used by audio engine\r\n     */\r\n    constructor(\r\n        hostElement: Nullable<HTMLElement> = null,\r\n        audioContext: Nullable<AudioContext> = null,\r\n        audioDestination: Nullable<AudioDestinationNode | MediaStreamAudioDestinationNode> = null\r\n    ) {\r\n        const v2 = new _WebAudioEngine({\r\n            audioContext: audioContext ? audioContext : undefined,\r\n            defaultUIParentElement: hostElement?.parentElement ? hostElement.parentElement : undefined,\r\n        });\r\n\r\n        // Historically the unmute button is disabled until a sound tries to play and can't, which results in a call\r\n        // to `AudioEngine.lock()`, which is where the unmute button is enabled if no custom UI is requested.\r\n        v2._unmuteUIEnabled = false;\r\n\r\n        this._masterGain = new GainNode(v2._audioContext);\r\n        v2._audioDestination = audioDestination;\r\n\r\n        v2.stateChangedObservable.add((state) => {\r\n            if (state === \"running\") {\r\n                this.unlocked = true;\r\n                this.onAudioUnlockedObservable.notifyObservers(this);\r\n            } else {\r\n                this.unlocked = false;\r\n                this.onAudioLockedObservable.notifyObservers(this);\r\n            }\r\n        });\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n        v2._initAsync({ resumeOnInteraction: false }).then(() => {\r\n            v2.mainOut._inNode = this._masterGain;\r\n            v2.stateChangedObservable.notifyObservers(v2.state);\r\n        });\r\n\r\n        this.isMP3supported = v2.isFormatValid(\"mp3\");\r\n        this.isOGGsupported = v2.isFormatValid(\"ogg\");\r\n\r\n        this._v2 = v2;\r\n    }\r\n\r\n    /**\r\n     * Flags the audio engine in Locked state.\r\n     * This happens due to new browser policies preventing audio to autoplay.\r\n     */\r\n    public lock() {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._v2._audioContext.suspend();\r\n\r\n        if (!this._useCustomUnlockedButton) {\r\n            this._v2._unmuteUIEnabled = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Unlocks the audio engine once a user action has been done on the dom.\r\n     * This is helpful to resume play once browser policies have been satisfied.\r\n     */\r\n    public unlock() {\r\n        if (this._audioContext?.state === \"running\") {\r\n            if (!this.unlocked) {\r\n                // Notify users that the audio stack is unlocked/unmuted\r\n                this.unlocked = true;\r\n                this.onAudioUnlockedObservable.notifyObservers(this);\r\n            }\r\n\r\n            return;\r\n        }\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._triggerRunningStateAsync();\r\n    }\r\n\r\n    /** @internal */\r\n    public _resumeAudioContextOnStateChange(): void {\r\n        this._audioContext?.addEventListener(\r\n            \"statechange\",\r\n            () => {\r\n                if (this.unlocked && this._audioContext?.state !== \"running\") {\r\n                    // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                    this._resumeAudioContextAsync();\r\n                }\r\n            },\r\n            {\r\n                once: true,\r\n                passive: true,\r\n                signal: AbortSignal.timeout(3000),\r\n            }\r\n        );\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    private _resumeAudioContextAsync(): Promise<void> {\r\n        if (this._v2._isUsingOfflineAudioContext) {\r\n            return Promise.resolve();\r\n        }\r\n\r\n        return this._v2._audioContext.resume();\r\n    }\r\n\r\n    /**\r\n     * Destroy and release the resources associated with the audio context.\r\n     */\r\n    public dispose(): void {\r\n        this._v2.dispose();\r\n\r\n        this.onAudioUnlockedObservable.clear();\r\n        this.onAudioLockedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Gets the global volume sets on the master gain.\r\n     * @returns the global volume if set or -1 otherwise\r\n     */\r\n    public getGlobalVolume(): number {\r\n        return this.masterGain.gain.value;\r\n    }\r\n\r\n    /**\r\n     * Sets the global volume of your experience (sets on the master gain).\r\n     * @param newVolume Defines the new global volume of the application\r\n     */\r\n    public setGlobalVolume(newVolume: number): void {\r\n        this.masterGain.gain.value = newVolume;\r\n    }\r\n\r\n    /**\r\n     * Connect the audio engine to an audio analyser allowing some amazing\r\n     * synchronization between the sounds/music and your visualization (VuMeter for instance).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser\r\n     * @param analyser The analyser to connect to the engine\r\n     */\r\n    public connectToAnalyser(analyser: Analyser): void {\r\n        if (this._connectedAnalyser) {\r\n            this._connectedAnalyser.stopDebugCanvas();\r\n        }\r\n\r\n        this._connectedAnalyser = analyser;\r\n        this.masterGain.disconnect();\r\n        this._connectedAnalyser.connectAudioNodes(this.masterGain, this._v2._audioContext.destination);\r\n    }\r\n\r\n    private async _triggerRunningStateAsync() {\r\n        if (this._tryToRun) {\r\n            return;\r\n        }\r\n        this._tryToRun = true;\r\n\r\n        await this._resumeAudioContextAsync();\r\n\r\n        this._tryToRun = false;\r\n        this.unlocked = true;\r\n\r\n        this.onAudioUnlockedObservable.notifyObservers(this);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAC;;;;AAErE,8CAA8C;iKAC9C,iBAAc,CAAC,kBAAkB,GAAG,CAChC,WAAkC,EAClC,YAAoC,EACpC,gBAAkF,EACpF,EAAE;IACA,OAAO,IAAI,WAAW,CAAC,WAAW,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACxE,CAAC,CAAC;AAOI,MAAO,WAAW;IAWpB;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAe,EAAA;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;IACxD,CAAC;IAyBD;;;OAGG,CACH,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAW,uBAAuB,CAAC,KAAc,EAAA;QAC7C,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QACtC,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG,CAAC,KAAK,CAAC;IACvC,CAAC;IAeD;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,yCAAyC;YACzC,mEAAmE;YACnE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACrC,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;IAClC,CAAC;IAID;;;;;;OAMG,CACH,YACI,cAAqC,IAAI,EACzC,eAAuC,IAAI,EAC3C,mBAAqF,IAAI,CAAA;QA9FrF,IAAA,CAAA,aAAa,GAA2B,IAAI,CAAC;QAE7C,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAClB,IAAA,CAAA,wBAAwB,GAAY,KAAK,CAAC;QAElD;;WAEG,CACI,IAAA,CAAA,cAAc,GAAY,IAAI,CAAC;QAatC;;WAEG,CACH,gEAAgE;QACzD,IAAA,CAAA,yBAAyB,GAAY,KAAK,CAAC;QAElD;;WAEG,CACI,IAAA,CAAA,cAAc,GAAY,KAAK,CAAC;QAEvC;;WAEG,CACI,IAAA,CAAA,cAAc,GAAY,KAAK,CAAC;QAEvC;;;;WAIG,CACI,IAAA,CAAA,QAAQ,GAAY,KAAK,CAAC;QAejC;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,8JAAI,aAAU,EAAgB,CAAC;QAElE;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAG,8JAAI,aAAU,EAAgB,CAAC;QA+B5D,MAAM,EAAE,GAAG,iLAAI,kBAAe,CAAC;YAC3B,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;YACrD,sBAAsB,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;SAC7F,CAAC,CAAC;QAEH,4GAA4G;QAC5G,qGAAqG;QACrG,EAAE,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE5B,IAAI,CAAC,WAAW,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;QAClD,EAAE,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAExC,EAAE,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACtB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,mFAAmF;QACnF,EAAE,CAAC,UAAU,CAAC;YAAE,mBAAmB,EAAE,KAAK;QAAA,CAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACpD,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;YACtC,EAAE,CAAC,sBAAsB,CAAC,eAAe,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE9C,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAClB,CAAC;IAED;;;OAGG,CACI,IAAI,GAAA;QACP,mEAAmE;QACnE,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAEjC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACrC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,MAAM,GAAA;QACT,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjB,wDAAwD;gBACxD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC;YAED,OAAO;QACX,CAAC;QAED,mEAAmE;QACnE,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAED,cAAA,EAAgB,CACT,gCAAgC,GAAA;QACnC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAChC,aAAa,EACb,GAAG,EAAE;YACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC3D,mEAAmE;gBACnE,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACpC,CAAC;QACL,CAAC,EACD;YACI,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;SACpC,CACJ,CAAC;IACN,CAAC;IAED,2FAA2F;IACnF,wBAAwB,GAAA;QAC5B,IAAI,IAAI,CAAC,GAAG,CAAC,2BAA2B,EAAE,CAAC;YACvC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAEnB,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC;IAED;;;OAGG,CACI,eAAe,GAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;IACtC,CAAC;IAED;;;OAGG,CACI,eAAe,CAAC,SAAiB,EAAA;QACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;IAC3C,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,QAAkB,EAAA;QACvC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QACnC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IACnG,CAAC;IAEO,KAAK,CAAC,yBAAyB,GAAA;QACnC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEtC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Audio/sound.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Audio/sound.ts"], "sourcesContent": ["import { Tools } from \"../Misc/tools\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport type { ISoundOptions } from \"./Interfaces/ISoundOptions\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { IAudioEngine } from \"./Interfaces/IAudioEngine\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport { _RetryWithInterval } from \"core/Misc/timingTools\";\r\n\r\n/**\r\n * Defines a sound that can be played in the application.\r\n * The sound can either be an ambient track or a simple sound played in reaction to a user action.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport class Sound {\r\n    /**\r\n     * The name of the sound in the scene.\r\n     */\r\n    public name: string;\r\n    /**\r\n     * Does the sound autoplay once loaded.\r\n     */\r\n    public autoplay: boolean = false;\r\n\r\n    private _loop = false;\r\n    /**\r\n     * Does the sound loop after it finishes playing once.\r\n     */\r\n    public get loop(): boolean {\r\n        return this._loop;\r\n    }\r\n\r\n    public set loop(value: boolean) {\r\n        if (value === this._loop) {\r\n            return;\r\n        }\r\n\r\n        this._loop = value;\r\n        this.updateOptions({ loop: value });\r\n    }\r\n\r\n    /**\r\n     * Does the sound use a custom attenuation curve to simulate the falloff\r\n     * happening when the source gets further away from the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-your-own-custom-attenuation-function\r\n     */\r\n    public useCustomAttenuation: boolean = false;\r\n    /**\r\n     * The sound track id this sound belongs to.\r\n     */\r\n    public soundTrackId: number;\r\n    /**\r\n     * Is this sound currently played.\r\n     */\r\n    public isPlaying: boolean = false;\r\n    /**\r\n     * Is this sound currently paused.\r\n     */\r\n    public isPaused: boolean = false;\r\n    /**\r\n     * Define the reference distance the sound should be heard perfectly.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public refDistance: number = 1;\r\n    /**\r\n     * Define the roll off factor of spatial sounds.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public rolloffFactor: number = 1;\r\n    /**\r\n     * Define the max distance the sound should be heard (intensity just became 0 at this point).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public maxDistance: number = 100;\r\n    /**\r\n     * Define the distance attenuation model the sound will follow.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public distanceModel: string = \"linear\";\r\n    /**\r\n     * @internal\r\n     * Back Compat\r\n     **/\r\n    public onended: () => any;\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the sound.\r\n     */\r\n    public metadata: any = null;\r\n\r\n    /**\r\n     * Observable event when the current playing sound finishes.\r\n     */\r\n    public onEndedObservable = new Observable<Sound>();\r\n\r\n    /**\r\n     * Gets the current time for the sound.\r\n     */\r\n    public get currentTime(): number {\r\n        if (this._htmlAudioElement) {\r\n            return this._htmlAudioElement.currentTime;\r\n        }\r\n\r\n        if (AbstractEngine.audioEngine?.audioContext && (this.isPlaying || this.isPaused)) {\r\n            // The `_currentTime` member is only updated when the sound is paused. Add the time since the last start\r\n            // to get the actual current time.\r\n            const timeSinceLastStart = this.isPaused ? 0 : AbstractEngine.audioEngine.audioContext.currentTime - this._startTime;\r\n            return this._currentTime + timeSinceLastStart;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Does this sound enables spatial sound.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public get spatialSound(): boolean {\r\n        return this._spatialSound;\r\n    }\r\n\r\n    /**\r\n     * Does this sound enables spatial sound.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public set spatialSound(newValue: boolean) {\r\n        if (newValue == this._spatialSound) {\r\n            return;\r\n        }\r\n\r\n        const wasPlaying = this.isPlaying;\r\n        this.pause();\r\n\r\n        if (newValue) {\r\n            this._spatialSound = newValue;\r\n            this._updateSpatialParameters();\r\n        } else {\r\n            this._disableSpatialSound();\r\n        }\r\n\r\n        if (wasPlaying) {\r\n            this.play();\r\n        }\r\n    }\r\n\r\n    private _spatialSound: boolean = false;\r\n    private _panningModel: string = \"equalpower\";\r\n    private _playbackRate: number = 1;\r\n    private _streaming: boolean = false;\r\n    private _startTime: number = 0;\r\n    private _currentTime: number = 0;\r\n    private _position: Vector3 = Vector3.Zero();\r\n    private _localDirection: Vector3 = new Vector3(1, 0, 0);\r\n    private _volume: number = 1;\r\n    private _isReadyToPlay: boolean = false;\r\n    private _isDirectional: boolean = false;\r\n    private _readyToPlayCallback: Nullable<() => any>;\r\n    private _audioBuffer: Nullable<AudioBuffer>;\r\n    private _soundSource: Nullable<AudioBufferSourceNode>;\r\n    private _streamingSource: Nullable<AudioNode>;\r\n    private _soundPanner: Nullable<PannerNode>;\r\n    private _soundGain: Nullable<GainNode>;\r\n    private _inputAudioNode: Nullable<AudioNode>;\r\n    private _outputAudioNode: Nullable<AudioNode>;\r\n    // Used if you'd like to create a directional sound.\r\n    // If not set, the sound will be omnidirectional\r\n    private _coneInnerAngle: number = 360;\r\n    private _coneOuterAngle: number = 360;\r\n    private _coneOuterGain: number = 0;\r\n    private _scene: Scene;\r\n    private _connectedTransformNode: Nullable<TransformNode>;\r\n    private _customAttenuationFunction: (currentVolume: number, currentDistance: number, maxDistance: number, refDistance: number, rolloffFactor: number) => number;\r\n    private _registerFunc: Nullable<(connectedMesh: TransformNode) => void>;\r\n    private _isOutputConnected = false;\r\n    private _htmlAudioElement: Nullable<HTMLAudioElement>;\r\n    private _urlType: \"Unknown\" | \"String\" | \"Array\" | \"ArrayBuffer\" | \"MediaStream\" | \"AudioBuffer\" | \"MediaElement\" = \"Unknown\";\r\n    private _length?: number;\r\n    private _offset?: number;\r\n    private _tryToPlayTimeout: Nullable<NodeJS.Timeout>;\r\n    private _audioUnlockedObserver?: Nullable<Observer<IAudioEngine>>;\r\n    private _url?: Nullable<string>;\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _SceneComponentInitialization: (scene: Scene) => void = (_) => {\r\n        throw _WarnImport(\"AudioSceneComponent\");\r\n    };\r\n\r\n    /**\r\n     * Create a sound and attach it to a scene\r\n     * @param name Name of your sound\r\n     * @param urlOrArrayBuffer Url to the sound to load async or ArrayBuffer, it also works with MediaStreams and AudioBuffers\r\n     * @param scene defines the scene the sound belongs to\r\n     * @param readyToPlayCallback Provide a callback function if you'd like to load your code once the sound is ready to be played\r\n     * @param options Objects to provide with the current available options: autoplay, loop, volume, spatialSound, maxDistance, rolloffFactor, refDistance, distanceModel, panningModel, streaming\r\n     */\r\n    constructor(name: string, urlOrArrayBuffer: any, scene?: Nullable<Scene>, readyToPlayCallback: Nullable<() => void> = null, options?: ISoundOptions) {\r\n        this.name = name;\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this._scene = scene;\r\n        Sound._SceneComponentInitialization(scene);\r\n\r\n        this._readyToPlayCallback = readyToPlayCallback;\r\n        // Default custom attenuation function is a linear attenuation\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        this._customAttenuationFunction = (currentVolume: number, currentDistance: number, maxDistance: number, refDistance: number, rolloffFactor: number) => {\r\n            if (currentDistance < maxDistance) {\r\n                return currentVolume * (1 - currentDistance / maxDistance);\r\n            } else {\r\n                return 0;\r\n            }\r\n        };\r\n        if (options) {\r\n            this.autoplay = options.autoplay || false;\r\n            this._loop = options.loop || false;\r\n            // if volume === 0, we need another way to check this option\r\n            if (options.volume !== undefined) {\r\n                this._volume = options.volume;\r\n            }\r\n            this._spatialSound = options.spatialSound ?? false;\r\n            this.maxDistance = options.maxDistance ?? 100;\r\n            this.useCustomAttenuation = options.useCustomAttenuation ?? false;\r\n            this.rolloffFactor = options.rolloffFactor || 1;\r\n            this.refDistance = options.refDistance || 1;\r\n            this.distanceModel = options.distanceModel || \"linear\";\r\n            this._playbackRate = options.playbackRate || 1;\r\n            this._streaming = options.streaming ?? false;\r\n            this._length = options.length;\r\n            this._offset = options.offset;\r\n        }\r\n\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && AbstractEngine.audioEngine.audioContext) {\r\n            this._soundGain = AbstractEngine.audioEngine.audioContext.createGain();\r\n            this._soundGain.gain.value = this._volume;\r\n            this._inputAudioNode = this._soundGain;\r\n            this._outputAudioNode = this._soundGain;\r\n            if (this._spatialSound) {\r\n                this._createSpatialParameters();\r\n            }\r\n            this._scene.mainSoundTrack.addSound(this);\r\n            let validParameter = true;\r\n\r\n            // if no parameter is passed, you need to call setAudioBuffer yourself to prepare the sound\r\n            if (urlOrArrayBuffer) {\r\n                try {\r\n                    if (typeof urlOrArrayBuffer === \"string\") {\r\n                        this._urlType = \"String\";\r\n                        this._url = urlOrArrayBuffer;\r\n                    } else if (urlOrArrayBuffer instanceof ArrayBuffer) {\r\n                        this._urlType = \"ArrayBuffer\";\r\n                    } else if (urlOrArrayBuffer instanceof HTMLMediaElement) {\r\n                        this._urlType = \"MediaElement\";\r\n                    } else if (urlOrArrayBuffer instanceof MediaStream) {\r\n                        this._urlType = \"MediaStream\";\r\n                    } else if (urlOrArrayBuffer instanceof AudioBuffer) {\r\n                        this._urlType = \"AudioBuffer\";\r\n                    } else if (Array.isArray(urlOrArrayBuffer)) {\r\n                        this._urlType = \"Array\";\r\n                    }\r\n\r\n                    let urls: string[] = [];\r\n                    let codecSupportedFound = false;\r\n\r\n                    switch (this._urlType) {\r\n                        case \"MediaElement\":\r\n                            this._streaming = true;\r\n                            this._isReadyToPlay = true;\r\n                            this._streamingSource = AbstractEngine.audioEngine.audioContext.createMediaElementSource(urlOrArrayBuffer);\r\n\r\n                            if (this.autoplay) {\r\n                                this.play(0, this._offset, this._length);\r\n                            }\r\n\r\n                            if (this._readyToPlayCallback) {\r\n                                this._readyToPlayCallback();\r\n                            }\r\n                            break;\r\n                        case \"MediaStream\":\r\n                            this._streaming = true;\r\n                            this._isReadyToPlay = true;\r\n                            this._streamingSource = AbstractEngine.audioEngine.audioContext.createMediaStreamSource(urlOrArrayBuffer);\r\n\r\n                            if (this.autoplay) {\r\n                                this.play(0, this._offset, this._length);\r\n                            }\r\n\r\n                            if (this._readyToPlayCallback) {\r\n                                this._readyToPlayCallback();\r\n                            }\r\n                            break;\r\n                        case \"ArrayBuffer\":\r\n                            if ((<ArrayBuffer>urlOrArrayBuffer).byteLength > 0) {\r\n                                codecSupportedFound = true;\r\n                                this._soundLoaded(urlOrArrayBuffer);\r\n                            }\r\n                            break;\r\n                        case \"AudioBuffer\":\r\n                            this._audioBufferLoaded(urlOrArrayBuffer);\r\n                            break;\r\n                        case \"String\":\r\n                            urls.push(urlOrArrayBuffer);\r\n                        // eslint-disable-next-line no-fallthrough\r\n                        case \"Array\":\r\n                            if (urls.length === 0) {\r\n                                urls = urlOrArrayBuffer;\r\n                            }\r\n                            // If we found a supported format, we load it immediately and stop the loop\r\n                            for (let i = 0; i < urls.length; i++) {\r\n                                const url = urls[i];\r\n                                codecSupportedFound =\r\n                                    (options && options.skipCodecCheck) ||\r\n                                    (url.indexOf(\".mp3\", url.length - 4) !== -1 && AbstractEngine.audioEngine.isMP3supported) ||\r\n                                    (url.indexOf(\".ogg\", url.length - 4) !== -1 && AbstractEngine.audioEngine.isOGGsupported) ||\r\n                                    url.indexOf(\".wav\", url.length - 4) !== -1 ||\r\n                                    url.indexOf(\".m4a\", url.length - 4) !== -1 ||\r\n                                    url.indexOf(\".mp4\", url.length - 4) !== -1 ||\r\n                                    url.indexOf(\"blob:\") !== -1;\r\n                                if (codecSupportedFound) {\r\n                                    // Loading sound\r\n                                    if (!this._streaming) {\r\n                                        this._scene._loadFile(\r\n                                            url,\r\n                                            (data) => {\r\n                                                this._soundLoaded(data as ArrayBuffer);\r\n                                            },\r\n                                            undefined,\r\n                                            true,\r\n                                            true,\r\n                                            (exception) => {\r\n                                                if (exception) {\r\n                                                    Logger.Error(\"XHR \" + exception.status + \" error on: \" + url + \".\");\r\n                                                }\r\n                                                Logger.Error(\"Sound creation aborted.\");\r\n                                                this._scene.mainSoundTrack.removeSound(this);\r\n                                            }\r\n                                        );\r\n                                    }\r\n                                    // Streaming sound using HTML5 Audio tag\r\n                                    else {\r\n                                        this._htmlAudioElement = new Audio(url);\r\n                                        this._htmlAudioElement.controls = false;\r\n                                        this._htmlAudioElement.loop = this.loop;\r\n                                        Tools.SetCorsBehavior(url, this._htmlAudioElement);\r\n                                        this._htmlAudioElement.preload = \"auto\";\r\n                                        this._htmlAudioElement.addEventListener(\r\n                                            \"canplaythrough\",\r\n                                            () => {\r\n                                                this._isReadyToPlay = true;\r\n                                                if (this.autoplay) {\r\n                                                    this.play(0, this._offset, this._length);\r\n                                                }\r\n                                                if (this._readyToPlayCallback) {\r\n                                                    this._readyToPlayCallback();\r\n                                                }\r\n                                            },\r\n                                            { once: true }\r\n                                        );\r\n                                        document.body.appendChild(this._htmlAudioElement);\r\n                                        this._htmlAudioElement.load();\r\n                                    }\r\n                                    break;\r\n                                }\r\n                            }\r\n                            break;\r\n                        default:\r\n                            validParameter = false;\r\n                            break;\r\n                    }\r\n\r\n                    if (!validParameter) {\r\n                        Logger.Error(\"Parameter must be a URL to the sound, an Array of URLs (.mp3 & .ogg) or an ArrayBuffer of the sound.\");\r\n                    } else {\r\n                        if (!codecSupportedFound) {\r\n                            this._isReadyToPlay = true;\r\n                            // Simulating a ready to play event to avoid breaking code path\r\n                            if (this._readyToPlayCallback) {\r\n                                setTimeout(() => {\r\n                                    if (this._readyToPlayCallback) {\r\n                                        this._readyToPlayCallback();\r\n                                    }\r\n                                }, 1000);\r\n                            }\r\n                        }\r\n                    }\r\n                } catch (ex) {\r\n                    Logger.Error(\"Unexpected error. Sound creation aborted.\");\r\n                    this._scene.mainSoundTrack.removeSound(this);\r\n                }\r\n            }\r\n        } else {\r\n            // Adding an empty sound to avoid breaking audio calls for non Web Audio browsers\r\n            this._scene.mainSoundTrack.addSound(this);\r\n            if (AbstractEngine.audioEngine && !AbstractEngine.audioEngine.WarnedWebAudioUnsupported) {\r\n                Logger.Error(\"Web Audio is not supported by your browser.\");\r\n                AbstractEngine.audioEngine.WarnedWebAudioUnsupported = true;\r\n            }\r\n            // Simulating a ready to play event to avoid breaking code for non web audio browsers\r\n            if (this._readyToPlayCallback) {\r\n                setTimeout(() => {\r\n                    if (this._readyToPlayCallback) {\r\n                        this._readyToPlayCallback();\r\n                    }\r\n                }, 1000);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release the sound and its associated resources\r\n     */\r\n    public dispose() {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\r\n            if (this.isPlaying) {\r\n                this.stop();\r\n            }\r\n            this._isReadyToPlay = false;\r\n            if (this.soundTrackId === -1) {\r\n                this._scene.mainSoundTrack.removeSound(this);\r\n            } else if (this._scene.soundTracks) {\r\n                this._scene.soundTracks[this.soundTrackId].removeSound(this);\r\n            }\r\n            if (this._soundGain) {\r\n                this._soundGain.disconnect();\r\n                this._soundGain = null;\r\n            }\r\n            if (this._soundPanner) {\r\n                this._soundPanner.disconnect();\r\n                this._soundPanner = null;\r\n            }\r\n            if (this._soundSource) {\r\n                this._soundSource.disconnect();\r\n                this._soundSource = null;\r\n            }\r\n            this._audioBuffer = null;\r\n\r\n            if (this._htmlAudioElement) {\r\n                this._htmlAudioElement.pause();\r\n                this._htmlAudioElement.src = \"\";\r\n                document.body.removeChild(this._htmlAudioElement);\r\n                this._htmlAudioElement = null;\r\n            }\r\n\r\n            if (this._streamingSource) {\r\n                this._streamingSource.disconnect();\r\n                this._streamingSource = null;\r\n            }\r\n\r\n            if (this._connectedTransformNode && this._registerFunc) {\r\n                this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\r\n                this._connectedTransformNode = null;\r\n            }\r\n\r\n            this._clearTimeoutsAndObservers();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets if the sounds is ready to be played or not.\r\n     * @returns true if ready, otherwise false\r\n     */\r\n    public isReady(): boolean {\r\n        return this._isReadyToPlay;\r\n    }\r\n\r\n    /**\r\n     * Get the current class name.\r\n     * @returns current class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"Sound\";\r\n    }\r\n\r\n    private _audioBufferLoaded(buffer: AudioBuffer) {\r\n        if (!AbstractEngine.audioEngine?.audioContext) {\r\n            return;\r\n        }\r\n        this._audioBuffer = buffer;\r\n        this._isReadyToPlay = true;\r\n        if (this.autoplay) {\r\n            this.play(0, this._offset, this._length);\r\n        }\r\n        if (this._readyToPlayCallback) {\r\n            this._readyToPlayCallback();\r\n        }\r\n    }\r\n\r\n    private _soundLoaded(audioData: ArrayBuffer) {\r\n        if (!AbstractEngine.audioEngine?.audioContext) {\r\n            return;\r\n        }\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        AbstractEngine.audioEngine.audioContext.decodeAudioData(\r\n            audioData,\r\n            (buffer) => {\r\n                this._audioBufferLoaded(buffer);\r\n            },\r\n            (err: any) => {\r\n                Logger.Error(\"Error while decoding audio data for: \" + this.name + \" / Error: \" + err);\r\n            }\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Sets the data of the sound from an audiobuffer\r\n     * @param audioBuffer The audioBuffer containing the data\r\n     */\r\n    public setAudioBuffer(audioBuffer: AudioBuffer): void {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\r\n            this._audioBuffer = audioBuffer;\r\n            this._isReadyToPlay = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the current sounds options such as maxdistance, loop...\r\n     * @param options A JSON object containing values named as the object properties\r\n     */\r\n    public updateOptions(options: ISoundOptions): void {\r\n        if (options) {\r\n            this.loop = options.loop ?? this.loop;\r\n            this.maxDistance = options.maxDistance ?? this.maxDistance;\r\n            this.useCustomAttenuation = options.useCustomAttenuation ?? this.useCustomAttenuation;\r\n            this.rolloffFactor = options.rolloffFactor ?? this.rolloffFactor;\r\n            this.refDistance = options.refDistance ?? this.refDistance;\r\n            this.distanceModel = options.distanceModel ?? this.distanceModel;\r\n            this._playbackRate = options.playbackRate ?? this._playbackRate;\r\n            this._length = options.length ?? undefined;\r\n            this.spatialSound = options.spatialSound ?? this._spatialSound;\r\n            this._setOffset(options.offset ?? undefined);\r\n            this.setVolume(options.volume ?? this._volume);\r\n            this._updateSpatialParameters();\r\n            if (this.isPlaying) {\r\n                if (this._streaming && this._htmlAudioElement) {\r\n                    this._htmlAudioElement.playbackRate = this._playbackRate;\r\n                    if (this._htmlAudioElement.loop !== this.loop) {\r\n                        this._htmlAudioElement.loop = this.loop;\r\n                    }\r\n                } else {\r\n                    if (this._soundSource) {\r\n                        this._soundSource.playbackRate.value = this._playbackRate;\r\n                        if (this._soundSource.loop !== this.loop) {\r\n                            this._soundSource.loop = this.loop;\r\n                        }\r\n                        if (this._offset !== undefined && this._soundSource.loopStart !== this._offset) {\r\n                            this._soundSource.loopStart = this._offset;\r\n                        }\r\n                        if (this._length !== undefined && this._length !== this._soundSource.loopEnd) {\r\n                            this._soundSource.loopEnd = (this._offset! | 0) + this._length;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _createSpatialParameters() {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && AbstractEngine.audioEngine.audioContext) {\r\n            if (this._scene.headphone) {\r\n                this._panningModel = \"HRTF\";\r\n            }\r\n            this._soundPanner = this._soundPanner ?? AbstractEngine.audioEngine.audioContext.createPanner();\r\n            if (this._soundPanner && this._outputAudioNode) {\r\n                this._updateSpatialParameters();\r\n                this._soundPanner.connect(this._outputAudioNode);\r\n                this._inputAudioNode = this._soundPanner;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _disableSpatialSound() {\r\n        if (!this._spatialSound) {\r\n            return;\r\n        }\r\n        this._inputAudioNode = this._soundGain;\r\n        this._soundPanner?.disconnect();\r\n        this._soundPanner = null;\r\n        this._spatialSound = false;\r\n    }\r\n\r\n    private _updateSpatialParameters() {\r\n        if (!this._spatialSound) {\r\n            return;\r\n        }\r\n        if (this._soundPanner) {\r\n            if (this.useCustomAttenuation) {\r\n                // Tricks to disable in a way embedded Web Audio attenuation\r\n                this._soundPanner.distanceModel = \"linear\";\r\n                this._soundPanner.maxDistance = Number.MAX_VALUE;\r\n                this._soundPanner.refDistance = 1;\r\n                this._soundPanner.rolloffFactor = 1;\r\n                this._soundPanner.panningModel = this._panningModel as any;\r\n            } else {\r\n                this._soundPanner.distanceModel = this.distanceModel as any;\r\n                this._soundPanner.maxDistance = this.maxDistance;\r\n                this._soundPanner.refDistance = this.refDistance;\r\n                this._soundPanner.rolloffFactor = this.rolloffFactor;\r\n                this._soundPanner.panningModel = this._panningModel as any;\r\n            }\r\n        } else {\r\n            this._createSpatialParameters();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to HRTF:\r\n     * Renders a stereo output of higher quality than equalpower — it uses a convolution with measured impulse responses from human subjects.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToHRTF() {\r\n        this._panningModel = \"HRTF\";\r\n        this._switchPanningModel();\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to Equal Power:\r\n     * Represents the equal-power panning algorithm, generally regarded as simple and efficient. equalpower is the default value.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToEqualPower() {\r\n        this._panningModel = \"equalpower\";\r\n        this._switchPanningModel();\r\n    }\r\n\r\n    private _switchPanningModel() {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\r\n            this._soundPanner.panningModel = this._panningModel as any;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connect this sound to a sound track audio node like gain...\r\n     * @param soundTrackAudioNode the sound track audio node to connect to\r\n     */\r\n    public connectToSoundTrackAudioNode(soundTrackAudioNode: AudioNode): void {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\r\n            if (this._isOutputConnected) {\r\n                this._outputAudioNode.disconnect();\r\n            }\r\n            this._outputAudioNode.connect(soundTrackAudioNode);\r\n            this._isOutputConnected = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Transform this sound into a directional source\r\n     * @param coneInnerAngle Size of the inner cone in degree\r\n     * @param coneOuterAngle Size of the outer cone in degree\r\n     * @param coneOuterGain Volume of the sound outside the outer cone (between 0.0 and 1.0)\r\n     */\r\n    public setDirectionalCone(coneInnerAngle: number, coneOuterAngle: number, coneOuterGain: number): void {\r\n        if (coneOuterAngle < coneInnerAngle) {\r\n            Logger.Error(\"setDirectionalCone(): outer angle of the cone must be superior or equal to the inner angle.\");\r\n            return;\r\n        }\r\n        this._coneInnerAngle = coneInnerAngle;\r\n        this._coneOuterAngle = coneOuterAngle;\r\n        this._coneOuterGain = coneOuterGain;\r\n        this._isDirectional = true;\r\n\r\n        if (this.isPlaying && this.loop) {\r\n            this.stop();\r\n            this.play(0, this._offset, this._length);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the inner angle for the directional cone.\r\n     */\r\n    public get directionalConeInnerAngle(): number {\r\n        return this._coneInnerAngle;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the inner angle for the directional cone.\r\n     */\r\n    public set directionalConeInnerAngle(value: number) {\r\n        if (value != this._coneInnerAngle) {\r\n            if (this._coneOuterAngle < value) {\r\n                Logger.Error(\"directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneInnerAngle = value;\r\n            if (AbstractEngine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\r\n                this._soundPanner.coneInnerAngle = this._coneInnerAngle;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the outer angle for the directional cone.\r\n     */\r\n    public get directionalConeOuterAngle(): number {\r\n        return this._coneOuterAngle;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the outer angle for the directional cone.\r\n     */\r\n    public set directionalConeOuterAngle(value: number) {\r\n        if (value != this._coneOuterAngle) {\r\n            if (value < this._coneInnerAngle) {\r\n                Logger.Error(\"directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneOuterAngle = value;\r\n            if (AbstractEngine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\r\n                this._soundPanner.coneOuterAngle = this._coneOuterAngle;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the position of the emitter if spatial sound is enabled\r\n     * @param newPosition Defines the new position\r\n     */\r\n    public setPosition(newPosition: Vector3): void {\r\n        if (newPosition.equals(this._position)) {\r\n            return;\r\n        }\r\n        this._position.copyFrom(newPosition);\r\n\r\n        if (\r\n            AbstractEngine.audioEngine?.canUseWebAudio &&\r\n            this._spatialSound &&\r\n            this._soundPanner &&\r\n            !isNaN(this._position.x) &&\r\n            !isNaN(this._position.y) &&\r\n            !isNaN(this._position.z)\r\n        ) {\r\n            this._soundPanner.positionX.value = this._position.x;\r\n            this._soundPanner.positionY.value = this._position.y;\r\n            this._soundPanner.positionZ.value = this._position.z;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the local direction of the emitter if spatial sound is enabled\r\n     * @param newLocalDirection Defines the new local direction\r\n     */\r\n    public setLocalDirectionToMesh(newLocalDirection: Vector3): void {\r\n        this._localDirection = newLocalDirection;\r\n\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._connectedTransformNode && this.isPlaying) {\r\n            this._updateDirection();\r\n        }\r\n    }\r\n\r\n    private _updateDirection() {\r\n        if (!this._connectedTransformNode || !this._soundPanner) {\r\n            return;\r\n        }\r\n\r\n        const mat = this._connectedTransformNode.getWorldMatrix();\r\n        const direction = Vector3.TransformNormal(this._localDirection, mat);\r\n        direction.normalize();\r\n        this._soundPanner.orientationX.value = direction.x;\r\n        this._soundPanner.orientationY.value = direction.y;\r\n        this._soundPanner.orientationZ.value = direction.z;\r\n    }\r\n\r\n    /** @internal */\r\n    public updateDistanceFromListener() {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._connectedTransformNode && this.useCustomAttenuation && this._soundGain && this._scene.activeCamera) {\r\n            const distance = this._scene.audioListenerPositionProvider\r\n                ? this._connectedTransformNode.position.subtract(this._scene.audioListenerPositionProvider()).length()\r\n                : this._connectedTransformNode.getDistanceToCamera(this._scene.activeCamera);\r\n            this._soundGain.gain.value = this._customAttenuationFunction(this._volume, distance, this.maxDistance, this.refDistance, this.rolloffFactor);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a new custom attenuation function for the sound.\r\n     * @param callback Defines the function used for the attenuation\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-your-own-custom-attenuation-function\r\n     */\r\n    public setAttenuationFunction(callback: (currentVolume: number, currentDistance: number, maxDistance: number, refDistance: number, rolloffFactor: number) => number): void {\r\n        this._customAttenuationFunction = callback;\r\n    }\r\n\r\n    /**\r\n     * Play the sound\r\n     * @param time (optional) Start the sound after X seconds. Start immediately (0) by default.\r\n     * @param offset (optional) Start the sound at a specific time in seconds\r\n     * @param length (optional) Sound duration (in seconds)\r\n     */\r\n    public play(time?: number, offset?: number, length?: number): void {\r\n        if (this._isReadyToPlay && this._scene.audioEnabled && AbstractEngine.audioEngine?.audioContext) {\r\n            try {\r\n                this._clearTimeoutsAndObservers();\r\n\r\n                let startTime = time ? AbstractEngine.audioEngine?.audioContext.currentTime + time : AbstractEngine.audioEngine?.audioContext.currentTime;\r\n                if (!this._soundSource || !this._streamingSource) {\r\n                    if (this._spatialSound && this._soundPanner) {\r\n                        if (!isNaN(this._position.x) && !isNaN(this._position.y) && !isNaN(this._position.z)) {\r\n                            this._soundPanner.positionX.value = this._position.x;\r\n                            this._soundPanner.positionY.value = this._position.y;\r\n                            this._soundPanner.positionZ.value = this._position.z;\r\n                        }\r\n                        if (this._isDirectional) {\r\n                            this._soundPanner.coneInnerAngle = this._coneInnerAngle;\r\n                            this._soundPanner.coneOuterAngle = this._coneOuterAngle;\r\n                            this._soundPanner.coneOuterGain = this._coneOuterGain;\r\n                            if (this._connectedTransformNode) {\r\n                                this._updateDirection();\r\n                            } else {\r\n                                this._soundPanner.setOrientation(this._localDirection.x, this._localDirection.y, this._localDirection.z);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                if (this._streaming) {\r\n                    if (!this._streamingSource && this._htmlAudioElement) {\r\n                        this._streamingSource = AbstractEngine.audioEngine.audioContext.createMediaElementSource(this._htmlAudioElement);\r\n                        this._htmlAudioElement.onended = () => {\r\n                            this._onended();\r\n                        };\r\n                        this._htmlAudioElement.playbackRate = this._playbackRate;\r\n                    }\r\n                    if (this._streamingSource) {\r\n                        this._streamingSource.disconnect();\r\n                        if (this._inputAudioNode) {\r\n                            this._streamingSource.connect(this._inputAudioNode);\r\n                        }\r\n                    }\r\n                    if (this._htmlAudioElement) {\r\n                        // required to manage properly the new suspended default state of Chrome\r\n                        // When the option 'streaming: true' is used, we need first to wait for\r\n                        // the audio engine to be unlocked by a user gesture before trying to play\r\n                        // an HTML Audio element\r\n                        const tryToPlay = () => {\r\n                            if (AbstractEngine.audioEngine?.unlocked) {\r\n                                if (!this._htmlAudioElement) {\r\n                                    return;\r\n                                }\r\n\r\n                                this._htmlAudioElement.currentTime = offset ?? 0;\r\n                                const playPromise = this._htmlAudioElement.play();\r\n\r\n                                // In browsers that don’t yet support this functionality,\r\n                                // playPromise won’t be defined.\r\n                                if (playPromise !== undefined) {\r\n                                    // eslint-disable-next-line github/no-then\r\n                                    playPromise.catch(() => {\r\n                                        // Automatic playback failed.\r\n                                        // Waiting for the audio engine to be unlocked by user click on unmute\r\n                                        AbstractEngine.audioEngine?.lock();\r\n                                        if (this.loop || this.autoplay) {\r\n                                            this._audioUnlockedObserver = AbstractEngine.audioEngine?.onAudioUnlockedObservable.addOnce(() => {\r\n                                                tryToPlay();\r\n                                            });\r\n                                        }\r\n                                    });\r\n                                }\r\n                            } else {\r\n                                if (this.loop || this.autoplay) {\r\n                                    this._audioUnlockedObserver = AbstractEngine.audioEngine?.onAudioUnlockedObservable.addOnce(() => {\r\n                                        tryToPlay();\r\n                                    });\r\n                                }\r\n                            }\r\n                        };\r\n                        tryToPlay();\r\n                    }\r\n                } else {\r\n                    const tryToPlay = () => {\r\n                        if (AbstractEngine.audioEngine?.audioContext) {\r\n                            length = length || this._length;\r\n\r\n                            if (offset !== undefined) {\r\n                                this._setOffset(offset);\r\n                            }\r\n\r\n                            if (this._soundSource) {\r\n                                const oldSource = this._soundSource;\r\n                                oldSource.onended = () => {\r\n                                    oldSource.disconnect();\r\n                                };\r\n                            }\r\n                            this._soundSource = AbstractEngine.audioEngine?.audioContext.createBufferSource();\r\n                            if (this._soundSource && this._inputAudioNode) {\r\n                                this._soundSource.buffer = this._audioBuffer;\r\n                                this._soundSource.connect(this._inputAudioNode);\r\n                                this._soundSource.loop = this.loop;\r\n                                if (offset !== undefined) {\r\n                                    this._soundSource.loopStart = offset;\r\n                                }\r\n                                if (length !== undefined) {\r\n                                    this._soundSource.loopEnd = (offset! | 0) + length;\r\n                                }\r\n                                this._soundSource.playbackRate.value = this._playbackRate;\r\n                                this._soundSource.onended = () => {\r\n                                    this._onended();\r\n                                };\r\n                                startTime = time ? AbstractEngine.audioEngine?.audioContext.currentTime + time : AbstractEngine.audioEngine.audioContext.currentTime;\r\n                                const actualOffset = ((this.isPaused ? this.currentTime : 0) + (this._offset ?? 0)) % this._soundSource.buffer!.duration;\r\n                                this._soundSource.start(startTime, actualOffset, this.loop ? undefined : length);\r\n                            }\r\n                        }\r\n                    };\r\n\r\n                    if (AbstractEngine.audioEngine?.audioContext.state === \"suspended\") {\r\n                        // Wait a bit for FF as context seems late to be ready.\r\n                        this._tryToPlayTimeout = setTimeout(() => {\r\n                            if (AbstractEngine.audioEngine?.audioContext!.state === \"suspended\") {\r\n                                // Automatic playback failed.\r\n                                // Waiting for the audio engine to be unlocked by user click on unmute\r\n                                AbstractEngine.audioEngine.lock();\r\n                                if (this.loop || this.autoplay) {\r\n                                    this._audioUnlockedObserver = AbstractEngine.audioEngine.onAudioUnlockedObservable.addOnce(() => {\r\n                                        tryToPlay();\r\n                                    });\r\n                                }\r\n                            } else {\r\n                                tryToPlay();\r\n                            }\r\n                        }, 500);\r\n                    } else {\r\n                        tryToPlay();\r\n                    }\r\n                }\r\n                this._startTime = startTime;\r\n                this.isPlaying = true;\r\n                this.isPaused = false;\r\n            } catch (ex) {\r\n                Logger.Error(\"Error while trying to play audio: \" + this.name + \", \" + ex.message);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _onended() {\r\n        this.isPlaying = false;\r\n        this._startTime = 0;\r\n        this._currentTime = 0;\r\n        if (this.onended) {\r\n            this.onended();\r\n        }\r\n        this.onEndedObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Stop the sound\r\n     * @param time (optional) Stop the sound after X seconds. Stop immediately (0) by default.\r\n     */\r\n    public stop(time?: number): void {\r\n        if (this.isPlaying) {\r\n            this._clearTimeoutsAndObservers();\r\n            if (this._streaming) {\r\n                if (this._htmlAudioElement) {\r\n                    this._htmlAudioElement.pause();\r\n                    // Test needed for Firefox or it will generate an Invalid State Error\r\n                    if (this._htmlAudioElement.currentTime > 0) {\r\n                        this._htmlAudioElement.currentTime = 0;\r\n                    }\r\n                } else {\r\n                    this._streamingSource?.disconnect();\r\n                }\r\n                this.isPlaying = false;\r\n            } else if (AbstractEngine.audioEngine?.audioContext && this._soundSource) {\r\n                const stopTime = time ? AbstractEngine.audioEngine.audioContext.currentTime + time : undefined;\r\n                this._soundSource.onended = () => {\r\n                    this.isPlaying = false;\r\n                    this.isPaused = false;\r\n                    this._startTime = 0;\r\n                    this._currentTime = 0;\r\n                    if (this._soundSource) {\r\n                        this._soundSource.onended = () => void 0;\r\n                    }\r\n                    this._onended();\r\n                };\r\n                this._soundSource.stop(stopTime);\r\n            } else {\r\n                this.isPlaying = false;\r\n            }\r\n        } else if (this.isPaused) {\r\n            this.isPaused = false;\r\n            this._startTime = 0;\r\n            this._currentTime = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Put the sound in pause\r\n     */\r\n    public pause(): void {\r\n        if (this.isPlaying) {\r\n            this._clearTimeoutsAndObservers();\r\n            if (this._streaming) {\r\n                if (this._htmlAudioElement) {\r\n                    this._htmlAudioElement.pause();\r\n                } else {\r\n                    this._streamingSource?.disconnect();\r\n                }\r\n                this.isPlaying = false;\r\n                this.isPaused = true;\r\n            } else if (AbstractEngine.audioEngine?.audioContext && this._soundSource) {\r\n                this._soundSource.onended = () => void 0;\r\n                this._soundSource.stop();\r\n                this.isPlaying = false;\r\n                this.isPaused = true;\r\n                this._currentTime += AbstractEngine.audioEngine.audioContext.currentTime - this._startTime;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a dedicated volume for this sounds\r\n     * @param newVolume Define the new volume of the sound\r\n     * @param time Define time for gradual change to new volume\r\n     */\r\n    public setVolume(newVolume: number, time?: number): void {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._soundGain) {\r\n            if (time && AbstractEngine.audioEngine.audioContext) {\r\n                this._soundGain.gain.cancelScheduledValues(AbstractEngine.audioEngine.audioContext.currentTime);\r\n                this._soundGain.gain.setValueAtTime(this._soundGain.gain.value, AbstractEngine.audioEngine.audioContext.currentTime);\r\n                this._soundGain.gain.linearRampToValueAtTime(newVolume, AbstractEngine.audioEngine.audioContext.currentTime + time);\r\n            } else {\r\n                this._soundGain.gain.value = newVolume;\r\n            }\r\n        }\r\n        this._volume = newVolume;\r\n    }\r\n\r\n    /**\r\n     * Set the sound play back rate\r\n     * @param newPlaybackRate Define the playback rate the sound should be played at\r\n     */\r\n    public setPlaybackRate(newPlaybackRate: number): void {\r\n        this._playbackRate = newPlaybackRate;\r\n        if (this.isPlaying) {\r\n            if (this._streaming && this._htmlAudioElement) {\r\n                this._htmlAudioElement.playbackRate = this._playbackRate;\r\n            } else if (this._soundSource) {\r\n                this._soundSource.playbackRate.value = this._playbackRate;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the sound play back rate.\r\n     * @returns the  play back rate of the sound\r\n     */\r\n    public getPlaybackRate(): number {\r\n        return this._playbackRate;\r\n    }\r\n\r\n    /**\r\n     * Gets the volume of the sound.\r\n     * @returns the volume of the sound\r\n     */\r\n    public getVolume(): number {\r\n        return this._volume;\r\n    }\r\n\r\n    /**\r\n     * Attach the sound to a dedicated mesh\r\n     * @param transformNode The transform node to connect the sound with\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#attaching-a-sound-to-a-mesh\r\n     */\r\n    public attachToMesh(transformNode: TransformNode): void {\r\n        if (this._connectedTransformNode && this._registerFunc) {\r\n            this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\r\n            this._registerFunc = null;\r\n        }\r\n        this._connectedTransformNode = transformNode;\r\n        if (!this._spatialSound) {\r\n            this._spatialSound = true;\r\n            this._createSpatialParameters();\r\n            if (this.isPlaying && this.loop) {\r\n                this.stop();\r\n                this.play(0, this._offset, this._length);\r\n            }\r\n        }\r\n        this._onRegisterAfterWorldMatrixUpdate(this._connectedTransformNode);\r\n        this._registerFunc = (transformNode: TransformNode) => this._onRegisterAfterWorldMatrixUpdate(transformNode);\r\n        this._connectedTransformNode.registerAfterWorldMatrixUpdate(this._registerFunc);\r\n    }\r\n\r\n    /**\r\n     * Detach the sound from the previously attached mesh\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#attaching-a-sound-to-a-mesh\r\n     */\r\n    public detachFromMesh() {\r\n        if (this._connectedTransformNode && this._registerFunc) {\r\n            this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\r\n            this._registerFunc = null;\r\n            this._connectedTransformNode = null;\r\n        }\r\n    }\r\n\r\n    private _onRegisterAfterWorldMatrixUpdate(node: TransformNode): void {\r\n        if (!(<any>node).getBoundingInfo) {\r\n            this.setPosition(node.absolutePosition);\r\n        } else {\r\n            const mesh = node as AbstractMesh;\r\n            const boundingInfo = mesh.getBoundingInfo();\r\n            this.setPosition(boundingInfo.boundingSphere.centerWorld);\r\n        }\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._isDirectional && this.isPlaying) {\r\n            this._updateDirection();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clone the current sound in the scene.\r\n     * @returns the new sound clone\r\n     */\r\n    public clone(): Nullable<Sound> {\r\n        if (!this._streaming) {\r\n            const setBufferAndRun = () => {\r\n                _RetryWithInterval(\r\n                    () => this._isReadyToPlay,\r\n                    () => {\r\n                        clonedSound._audioBuffer = this.getAudioBuffer();\r\n                        clonedSound._isReadyToPlay = true;\r\n                        if (clonedSound.autoplay) {\r\n                            clonedSound.play(0, this._offset, this._length);\r\n                        }\r\n                    },\r\n                    undefined,\r\n                    300\r\n                );\r\n            };\r\n\r\n            const currentOptions = {\r\n                autoplay: this.autoplay,\r\n                loop: this.loop,\r\n                volume: this._volume,\r\n                spatialSound: this._spatialSound,\r\n                maxDistance: this.maxDistance,\r\n                useCustomAttenuation: this.useCustomAttenuation,\r\n                rolloffFactor: this.rolloffFactor,\r\n                refDistance: this.refDistance,\r\n                distanceModel: this.distanceModel,\r\n            };\r\n\r\n            const clonedSound = new Sound(this.name + \"_cloned\", new ArrayBuffer(0), this._scene, null, currentOptions);\r\n            if (this.useCustomAttenuation) {\r\n                clonedSound.setAttenuationFunction(this._customAttenuationFunction);\r\n            }\r\n            clonedSound.setPosition(this._position);\r\n            clonedSound.setPlaybackRate(this._playbackRate);\r\n            setBufferAndRun();\r\n\r\n            return clonedSound;\r\n        }\r\n        // Can't clone a streaming sound\r\n        else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the current underlying audio buffer containing the data\r\n     * @returns the audio buffer\r\n     */\r\n    public getAudioBuffer(): Nullable<AudioBuffer> {\r\n        return this._audioBuffer;\r\n    }\r\n\r\n    /**\r\n     * Gets the WebAudio AudioBufferSourceNode, lets you keep track of and stop instances of this Sound.\r\n     * @returns the source node\r\n     */\r\n    public getSoundSource(): Nullable<AudioBufferSourceNode> {\r\n        return this._soundSource;\r\n    }\r\n\r\n    /**\r\n     * Gets the WebAudio GainNode, gives you precise control over the gain of instances of this Sound.\r\n     * @returns the gain node\r\n     */\r\n    public getSoundGain(): Nullable<GainNode> {\r\n        return this._soundGain;\r\n    }\r\n\r\n    /**\r\n     * Serializes the Sound in a JSON representation\r\n     * @returns the JSON representation of the sound\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {\r\n            name: this.name,\r\n            url: this._url,\r\n            autoplay: this.autoplay,\r\n            loop: this.loop,\r\n            volume: this._volume,\r\n            spatialSound: this._spatialSound,\r\n            maxDistance: this.maxDistance,\r\n            rolloffFactor: this.rolloffFactor,\r\n            refDistance: this.refDistance,\r\n            distanceModel: this.distanceModel,\r\n            playbackRate: this._playbackRate,\r\n            panningModel: this._panningModel,\r\n            soundTrackId: this.soundTrackId,\r\n            metadata: this.metadata,\r\n        };\r\n\r\n        if (this._spatialSound) {\r\n            if (this._connectedTransformNode) {\r\n                serializationObject.connectedMeshId = this._connectedTransformNode.id;\r\n            }\r\n\r\n            serializationObject.position = this._position.asArray();\r\n            serializationObject.refDistance = this.refDistance;\r\n            serializationObject.distanceModel = this.distanceModel;\r\n\r\n            serializationObject.isDirectional = this._isDirectional;\r\n            serializationObject.localDirectionToMesh = this._localDirection.asArray();\r\n            serializationObject.coneInnerAngle = this._coneInnerAngle;\r\n            serializationObject.coneOuterAngle = this._coneOuterAngle;\r\n            serializationObject.coneOuterGain = this._coneOuterGain;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse a JSON representation of a sound to instantiate in a given scene\r\n     * @param parsedSound Define the JSON representation of the sound (usually coming from the serialize method)\r\n     * @param scene Define the scene the new parsed sound should be created in\r\n     * @param rootUrl Define the rooturl of the load in case we need to fetch relative dependencies\r\n     * @param sourceSound Define a sound place holder if do not need to instantiate a new one\r\n     * @returns the newly parsed sound\r\n     */\r\n    public static Parse(parsedSound: any, scene: Scene, rootUrl: string, sourceSound?: Sound): Sound {\r\n        const soundName = parsedSound.name;\r\n        let soundUrl;\r\n\r\n        if (parsedSound.url) {\r\n            soundUrl = rootUrl + parsedSound.url;\r\n        } else {\r\n            soundUrl = rootUrl + soundName;\r\n        }\r\n\r\n        const options = {\r\n            autoplay: parsedSound.autoplay,\r\n            loop: parsedSound.loop,\r\n            volume: parsedSound.volume,\r\n            spatialSound: parsedSound.spatialSound,\r\n            maxDistance: parsedSound.maxDistance,\r\n            rolloffFactor: parsedSound.rolloffFactor,\r\n            refDistance: parsedSound.refDistance,\r\n            distanceModel: parsedSound.distanceModel,\r\n            playbackRate: parsedSound.playbackRate,\r\n        };\r\n\r\n        let newSound: Sound;\r\n\r\n        if (!sourceSound) {\r\n            newSound = new Sound(\r\n                soundName,\r\n                soundUrl,\r\n                scene,\r\n                () => {\r\n                    scene.removePendingData(newSound);\r\n                },\r\n                options\r\n            );\r\n            scene.addPendingData(newSound);\r\n        } else {\r\n            const setBufferAndRun = () => {\r\n                _RetryWithInterval(\r\n                    () => sourceSound._isReadyToPlay,\r\n                    () => {\r\n                        newSound._audioBuffer = sourceSound.getAudioBuffer();\r\n                        newSound._isReadyToPlay = true;\r\n                        if (newSound.autoplay) {\r\n                            newSound.play(0, newSound._offset, newSound._length);\r\n                        }\r\n                    },\r\n                    undefined,\r\n                    300\r\n                );\r\n            };\r\n\r\n            newSound = new Sound(soundName, new ArrayBuffer(0), scene, null, options);\r\n            setBufferAndRun();\r\n        }\r\n\r\n        if (parsedSound.position) {\r\n            const soundPosition = Vector3.FromArray(parsedSound.position);\r\n            newSound.setPosition(soundPosition);\r\n        }\r\n        if (parsedSound.isDirectional) {\r\n            newSound.setDirectionalCone(parsedSound.coneInnerAngle || 360, parsedSound.coneOuterAngle || 360, parsedSound.coneOuterGain || 0);\r\n            if (parsedSound.localDirectionToMesh) {\r\n                const localDirectionToMesh = Vector3.FromArray(parsedSound.localDirectionToMesh);\r\n                newSound.setLocalDirectionToMesh(localDirectionToMesh);\r\n            }\r\n        }\r\n        if (parsedSound.connectedMeshId) {\r\n            const connectedMesh = scene.getMeshById(parsedSound.connectedMeshId);\r\n            if (connectedMesh) {\r\n                newSound.attachToMesh(connectedMesh);\r\n            }\r\n        }\r\n\r\n        if (parsedSound.metadata) {\r\n            newSound.metadata = parsedSound.metadata;\r\n        }\r\n\r\n        return newSound;\r\n    }\r\n\r\n    private _setOffset(value?: number) {\r\n        if (this._offset === value) {\r\n            return;\r\n        }\r\n        if (this.isPaused) {\r\n            this.stop();\r\n            this.isPaused = false;\r\n        }\r\n        this._offset = value;\r\n    }\r\n\r\n    private _clearTimeoutsAndObservers() {\r\n        if (this._tryToPlayTimeout) {\r\n            clearTimeout(this._tryToPlayTimeout);\r\n            this._tryToPlayTimeout = null;\r\n        }\r\n        if (this._audioUnlockedObserver) {\r\n            AbstractEngine.audioEngine?.onAudioUnlockedObservable.remove(this._audioUnlockedObserver);\r\n            this._audioUnlockedObserver = null;\r\n        }\r\n    }\r\n}\r\n\r\n// Register Class Name\r\nRegisterClass(\"BABYLON.Sound\", Sound);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAK/C,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAGrD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,cAAc,EAAE,qCAAoC;AAC7D,OAAO,EAAE,kBAAkB,EAAE,+BAA8B;;;;;;;;;;AAOrD,MAAO,KAAK;IAWd;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAc,EAAA;QAC1B,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,aAAa,CAAC;YAAE,IAAI,EAAE,KAAK;QAAA,CAAE,CAAC,CAAC;IACxC,CAAC;IAuDD;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;QAC9C,CAAC;QAED,qKAAI,iBAAc,CAAC,WAAW,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChF,wGAAwG;YACxG,kCAAkC;YAClC,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kKAAC,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YACrH,OAAO,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC;QAClD,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACH,IAAW,YAAY,CAAC,QAAiB,EAAA;QACrC,IAAI,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;YAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;IACL,CAAC;IA6CD;;;;;;;OAOG,CACH,YAAY,IAAY,EAAE,gBAAqB,EAAE,KAAuB,EAAE,sBAA4C,IAAI,EAAE,OAAuB,CAAA;QAhLnJ;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAY,KAAK,CAAC;QAEzB,IAAA,CAAA,KAAK,GAAG,KAAK,CAAC;QAiBtB;;;;WAIG,CACI,IAAA,CAAA,oBAAoB,GAAY,KAAK,CAAC;QAK7C;;WAEG,CACI,IAAA,CAAA,SAAS,GAAY,KAAK,CAAC;QAClC;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAY,KAAK,CAAC;QACjC;;;WAGG,CACI,IAAA,CAAA,WAAW,GAAW,CAAC,CAAC;QAC/B;;;WAGG,CACI,IAAA,CAAA,aAAa,GAAW,CAAC,CAAC;QACjC;;;WAGG,CACI,IAAA,CAAA,WAAW,GAAW,GAAG,CAAC;QACjC;;;WAGG,CACI,IAAA,CAAA,aAAa,GAAW,QAAQ,CAAC;QAMxC;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAQ,IAAI,CAAC;QAE5B;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAG,8JAAI,aAAU,EAAS,CAAC;QAoD3C,IAAA,CAAA,aAAa,GAAY,KAAK,CAAC;QAC/B,IAAA,CAAA,aAAa,GAAW,YAAY,CAAC;QACrC,IAAA,CAAA,aAAa,GAAW,CAAC,CAAC;QAC1B,IAAA,CAAA,UAAU,GAAY,KAAK,CAAC;QAC5B,IAAA,CAAA,UAAU,GAAW,CAAC,CAAC;QACvB,IAAA,CAAA,YAAY,GAAW,CAAC,CAAC;QACzB,IAAA,CAAA,SAAS,kKAAY,UAAO,CAAC,IAAI,EAAE,CAAC;QACpC,IAAA,CAAA,eAAe,GAAY,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChD,IAAA,CAAA,OAAO,GAAW,CAAC,CAAC;QACpB,IAAA,CAAA,cAAc,GAAY,KAAK,CAAC;QAChC,IAAA,CAAA,cAAc,GAAY,KAAK,CAAC;QASxC,oDAAoD;QACpD,gDAAgD;QACxC,IAAA,CAAA,eAAe,GAAW,GAAG,CAAC;QAC9B,IAAA,CAAA,eAAe,GAAW,GAAG,CAAC;QAC9B,IAAA,CAAA,cAAc,GAAW,CAAC,CAAC;QAK3B,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAE3B,IAAA,CAAA,QAAQ,GAAoG,SAAS,CAAC;QAsB1H,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,KAAK,GAAG,KAAK,kKAAI,cAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,KAAK,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;QAE3C,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAChD,8DAA8D;QAC9D,6DAA6D;QAC7D,IAAI,CAAC,0BAA0B,GAAG,CAAC,aAAqB,EAAE,eAAuB,EAAE,WAAmB,EAAE,WAAmB,EAAE,aAAqB,EAAE,EAAE;YAClJ,IAAI,eAAe,GAAG,WAAW,EAAE,CAAC;gBAChC,OAAO,aAAa,GAAG,CAAC,CAAC,GAAG,eAAe,GAAG,WAAW,CAAC,CAAC;YAC/D,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,CAAC;YACb,CAAC;QACL,CAAC,CAAC;QACF,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;YAC1C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC;YACnC,4DAA4D;YAC5D,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;YAClC,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC;YACnD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,GAAG,CAAC;YAC9C,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,KAAK,CAAC;YAClE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;YAChD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;YACvD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC;YAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;YAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QAClC,CAAC;QAED,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,qKAAI,iBAAc,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACxF,IAAI,CAAC,UAAU,GAAG,kLAAc,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YACvE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;YAC1C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC;YACxC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,cAAc,GAAG,IAAI,CAAC;YAE1B,2FAA2F;YAC3F,IAAI,gBAAgB,EAAE,CAAC;gBACnB,IAAI,CAAC;oBACD,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE,CAAC;wBACvC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;wBACzB,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;oBACjC,CAAC,MAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE,CAAC;wBACjD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;oBAClC,CAAC,MAAM,IAAI,gBAAgB,YAAY,gBAAgB,EAAE,CAAC;wBACtD,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;oBACnC,CAAC,MAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE,CAAC;wBACjD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;oBAClC,CAAC,MAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE,CAAC;wBACjD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;oBAClC,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;wBACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;oBAC5B,CAAC;oBAED,IAAI,IAAI,GAAa,EAAE,CAAC;oBACxB,IAAI,mBAAmB,GAAG,KAAK,CAAC;oBAEhC,OAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACpB,KAAK,cAAc;4BACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;4BACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4BAC3B,IAAI,CAAC,gBAAgB,oKAAG,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;4BAE3G,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gCAChB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;4BAC7C,CAAC;4BAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gCAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BAChC,CAAC;4BACD,MAAM;wBACV,KAAK,aAAa;4BACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;4BACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4BAC3B,IAAI,CAAC,gBAAgB,oKAAG,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;4BAE1G,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gCAChB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;4BAC7C,CAAC;4BAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gCAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BAChC,CAAC;4BACD,MAAM;wBACV,KAAK,aAAa;4BACd,IAAkB,gBAAiB,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gCACjD,mBAAmB,GAAG,IAAI,CAAC;gCAC3B,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;4BACxC,CAAC;4BACD,MAAM;wBACV,KAAK,aAAa;4BACd,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;4BAC1C,MAAM;wBACV,KAAK,QAAQ;4BACT,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBAChC,0CAA0C;wBAC1C,KAAK,OAAO;4BACR,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gCACpB,IAAI,GAAG,gBAAgB,CAAC;4BAC5B,CAAC;4BACD,2EAA2E;4BAC3E,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gCACnC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gCACpB,mBAAmB,GACf,AAAC,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,GAClC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,qKAAI,iBAAc,CAAC,WAAW,CAAC,cAAc,CAAC,GACxF,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,qKAAI,iBAAc,CAAC,WAAW,CAAC,cAAc,CAAC,GACzF,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,IAC1C,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,IAC1C,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,IAC1C,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gCAChC,IAAI,mBAAmB,EAAE,CAAC;oCACtB,gBAAgB;oCAChB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;wCACnB,IAAI,CAAC,MAAM,CAAC,SAAS,CACjB,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;4CACL,IAAI,CAAC,YAAY,CAAC,IAAmB,CAAC,CAAC;wCAC3C,CAAC,EACD,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,CAAC,SAAS,EAAE,EAAE;4CACV,IAAI,SAAS,EAAE,CAAC;gDACZ,+JAAM,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;4CACxE,CAAC;kMACD,SAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;4CACxC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;wCACjD,CAAC,CACJ,CAAC;oCACN,CAAC,MAEI,CAAC;wCACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;wCACxC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,GAAG,KAAK,CAAC;wCACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;wCACxC,6JAAK,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;wCACnD,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,MAAM,CAAC;wCACxC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CACnC,gBAAgB,EAChB,GAAG,EAAE;4CACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4CAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gDAChB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;4CAC7C,CAAC;4CACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gDAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;4CAChC,CAAC;wCACL,CAAC,EACD;4CAAE,IAAI,EAAE,IAAI;wCAAA,CAAE,CACjB,CAAC;wCACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;wCAClD,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;oCAClC,CAAC;oCACD,MAAM;gCACV,CAAC;4BACL,CAAC;4BACD,MAAM;wBACV;4BACI,cAAc,GAAG,KAAK,CAAC;4BACvB,MAAM;oBACd,CAAC;oBAED,IAAI,CAAC,cAAc,EAAE,CAAC;wBAClB,+JAAM,CAAC,KAAK,CAAC,sGAAsG,CAAC,CAAC;oBACzH,CAAC,MAAM,CAAC;wBACJ,IAAI,CAAC,mBAAmB,EAAE,CAAC;4BACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4BAC3B,+DAA+D;4BAC/D,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gCAC5B,UAAU,CAAC,GAAG,EAAE;oCACZ,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wCAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;oCAChC,CAAC;gCACL,CAAC,EAAE,IAAI,CAAC,CAAC;4BACb,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;0KACV,SAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;oBAC1D,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACjD,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,iFAAiF;YACjF,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC1C,qKAAI,iBAAc,CAAC,WAAW,IAAI,kKAAC,iBAAc,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC;sKACtF,SAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;iLAC5D,iBAAc,CAAC,WAAW,CAAC,yBAAyB,GAAG,IAAI,CAAC;YAChE,CAAC;YACD,qFAAqF;YACrF,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,UAAU,CAAC,GAAG,EAAE;oBACZ,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAChC,CAAC;gBACL,CAAC,EAAE,IAAI,CAAC,CAAC;YACb,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,kLAAc,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;YAC7C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACjE,CAAC;YACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YAC3B,CAAC;YACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAC7B,CAAC;YACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAC7B,CAAC;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAEzB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,EAAE,CAAC;gBAChC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAClD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAClC,CAAC;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;gBACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACjC,CAAC;YAED,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrD,IAAI,CAAC,uBAAuB,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClF,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;YACxC,CAAC;YAED,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACtC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,kBAAkB,CAAC,MAAmB,EAAA;QAC1C,IAAI,kKAAC,iBAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;YAC5C,OAAO;QACX,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,SAAsB,EAAA;QACvC,IAAI,kKAAC,iBAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;YAC5C,OAAO;QACX,CAAC;QACD,mEAAmE;yKACnE,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,eAAe,CACnD,SAAS,EACT,CAAC,MAAM,EAAE,EAAE;YACP,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC,EACD,CAAC,GAAQ,EAAE,EAAE;kKACT,SAAM,CAAC,KAAK,CAAC,uCAAuC,GAAG,IAAI,CAAC,IAAI,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC;QAC3F,CAAC,CACJ,CAAC;IACN,CAAC;IAED;;;OAGG,CACI,cAAc,CAAC,WAAwB,EAAA;QAC1C,IAAI,kLAAc,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;YAC7C,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;YAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,aAAa,CAAC,OAAsB,EAAA;QACvC,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;YACtC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAC3D,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC;YACtF,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC;YACjE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAC3D,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC;YACjE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC;YAChE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC;YAC3C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC;YAC/D,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC;YAC7C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/C,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC5C,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;oBACzD,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;wBAC5C,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;oBAC5C,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBACpB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;wBAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;4BACvC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;wBACvC,CAAC;wBACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;4BAC7E,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC/C,CAAC;wBACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;4BAC3E,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;wBACnE,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAEO,wBAAwB,GAAA;QAC5B,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,qKAAI,iBAAc,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACxF,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACxB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,qKAAI,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YAChG,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC7C,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACjD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;YAC7C,CAAC;QACL,CAAC;IACL,CAAC;IAEO,oBAAoB,GAAA;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;QACvC,IAAI,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC/B,CAAC;IAEO,wBAAwB,GAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,4DAA4D;gBAC5D,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,QAAQ,CAAC;gBAC3C,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC;gBAClC,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,CAAC,CAAC;gBACpC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,aAAoB,CAAC;YAC/D,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAoB,CAAC;gBAC5D,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,aAAoB,CAAC;YAC/D,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,wBAAwB,GAAA;QAC3B,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG,CACI,8BAA8B,GAAA;QACjC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,mBAAmB,GAAA;QACvB,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACxF,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,aAAoB,CAAC;QAC/D,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,4BAA4B,CAAC,mBAA8B,EAAA;QAC9D,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtE,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;YACvC,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACnD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACnC,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,kBAAkB,CAAC,cAAsB,EAAE,cAAsB,EAAE,aAAqB,EAAA;QAC3F,IAAI,cAAc,GAAG,cAAc,EAAE,CAAC;iKAClC,UAAM,CAAC,KAAK,CAAC,6FAA6F,CAAC,CAAC;YAC5G,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,CAAC,KAAa,EAAA;QAC9C,IAAI,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,eAAe,GAAG,KAAK,EAAE,CAAC;sKAC/B,SAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;YACX,CAAC;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,kLAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACxF,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC5D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,CAAC,KAAa,EAAA;QAC9C,IAAI,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChC,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;sKAC/B,SAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;YACX,CAAC;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACxF,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC5D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,WAAW,CAAC,WAAoB,EAAA;QACnC,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAErC,qKACI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAC1C,IAAI,CAAC,aAAa,IAClB,IAAI,CAAC,YAAY,IACjB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IACxB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IACxB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAC1B,CAAC;YACC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,uBAAuB,CAAC,iBAA0B,EAAA;QACrD,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;QAEzC,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC/F,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;IACL,CAAC;IAEO,gBAAgB,GAAA;QACpB,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACtD,OAAO;QACX,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC;QAC1D,MAAM,SAAS,kKAAG,UAAO,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;QACrE,SAAS,CAAC,SAAS,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,cAAA,EAAgB,CACT,0BAA0B,GAAA;QAC7B,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACzJ,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,6BAA6B,GACpD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC,CAAC,MAAM,EAAE,GACpG,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACjF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACjJ,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,sBAAsB,CAAC,QAAqI,EAAA;QAC/J,IAAI,CAAC,0BAA0B,GAAG,QAAQ,CAAC;IAC/C,CAAC;IAED;;;;;OAKG,CACI,IAAI,CAAC,IAAa,EAAE,MAAe,EAAE,MAAe,EAAA;QACvD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,qKAAI,iBAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;YAC9F,IAAI,CAAC;gBACD,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAElC,IAAI,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,kLAAc,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,kKAAC,iBAAc,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,CAAC;gBAC1I,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC/C,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;4BACnF,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;4BACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;4BACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;wBACzD,CAAC;wBACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;4BACtB,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;4BACxD,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;4BACxD,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;4BACtD,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gCAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BAC5B,CAAC,MAAM,CAAC;gCACJ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;4BAC7G,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACnD,IAAI,CAAC,gBAAgB,oKAAG,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;wBACjH,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,GAAG,EAAE;4BAClC,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACpB,CAAC,CAAC;wBACF,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;oBAC7D,CAAC;oBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACxB,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;wBACnC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;4BACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBACxD,CAAC;oBACL,CAAC;oBACD,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACzB,wEAAwE;wBACxE,uEAAuE;wBACvE,0EAA0E;wBAC1E,wBAAwB;wBACxB,MAAM,SAAS,GAAG,GAAG,EAAE;4BACnB,qKAAI,iBAAc,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC;gCACvC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oCAC1B,OAAO;gCACX,CAAC;gCAED,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,CAAC;gCACjD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;gCAElD,yDAAyD;gCACzD,gCAAgC;gCAChC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;oCAC5B,0CAA0C;oCAC1C,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE;wCACnB,6BAA6B;wCAC7B,sEAAsE;yMACtE,iBAAc,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;wCACnC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;4CAC7B,IAAI,CAAC,sBAAsB,oKAAG,iBAAc,CAAC,WAAW,EAAE,yBAAyB,CAAC,OAAO,CAAC,GAAG,EAAE;gDAC7F,SAAS,EAAE,CAAC;4CAChB,CAAC,CAAC,CAAC;wCACP,CAAC;oCACL,CAAC,CAAC,CAAC;gCACP,CAAC;4BACL,CAAC,MAAM,CAAC;gCACJ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oCAC7B,IAAI,CAAC,sBAAsB,oKAAG,iBAAc,CAAC,WAAW,EAAE,yBAAyB,CAAC,OAAO,CAAC,GAAG,EAAE;wCAC7F,SAAS,EAAE,CAAC;oCAChB,CAAC,CAAC,CAAC;gCACP,CAAC;4BACL,CAAC;wBACL,CAAC,CAAC;wBACF,SAAS,EAAE,CAAC;oBAChB,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,MAAM,SAAS,GAAG,GAAG,EAAE;wBACnB,qKAAI,iBAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;4BAC3C,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;4BAEhC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gCACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;4BAC5B,CAAC;4BAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gCACpB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;gCACpC,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;oCACrB,SAAS,CAAC,UAAU,EAAE,CAAC;gCAC3B,CAAC,CAAC;4BACN,CAAC;4BACD,IAAI,CAAC,YAAY,oKAAG,iBAAc,CAAC,WAAW,EAAE,YAAY,CAAC,kBAAkB,EAAE,CAAC;4BAClF,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gCAC5C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;gCAC7C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gCAChD,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gCACnC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oCACvB,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,MAAM,CAAC;gCACzC,CAAC;gCACD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oCACvB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,MAAO,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;gCACvD,CAAC;gCACD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;gCAC1D,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE;oCAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;gCACpB,CAAC,CAAC;gCACF,SAAS,GAAG,IAAI,CAAC,CAAC,kKAAC,iBAAc,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,iKAAC,kBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC;gCACrI,MAAM,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,QAAQ,CAAC;gCACzH,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;4BACrF,CAAC;wBACL,CAAC;oBACL,CAAC,CAAC;oBAEF,qKAAI,iBAAc,CAAC,WAAW,EAAE,YAAY,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;wBACjE,uDAAuD;wBACvD,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,EAAE;4BACrC,IAAI,kLAAc,CAAC,WAAW,EAAE,YAAa,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;gCAClE,6BAA6B;gCAC7B,sEAAsE;iMACtE,iBAAc,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gCAClC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oCAC7B,IAAI,CAAC,sBAAsB,oKAAG,iBAAc,CAAC,WAAW,CAAC,yBAAyB,CAAC,OAAO,CAAC,GAAG,EAAE;wCAC5F,SAAS,EAAE,CAAC;oCAChB,CAAC,CAAC,CAAC;gCACP,CAAC;4BACL,CAAC,MAAM,CAAC;gCACJ,SAAS,EAAE,CAAC;4BAChB,CAAC;wBACL,CAAC,EAAE,GAAG,CAAC,CAAC;oBACZ,CAAC,MAAM,CAAC;wBACJ,SAAS,EAAE,CAAC;oBAChB,CAAC;gBACL,CAAC;gBACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YAC1B,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;sKACV,SAAM,CAAC,KAAK,CAAC,oCAAoC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC;YACvF,CAAC;QACL,CAAC;IACL,CAAC;IAEO,QAAQ,GAAA;QACZ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG,CACI,IAAI,CAAC,IAAa,EAAA;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;oBAC/B,qEAAqE;oBACrE,IAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;wBACzC,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,CAAC;oBAC3C,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,CAAC;gBACxC,CAAC;gBACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAC3B,CAAC,MAAM,qKAAI,iBAAc,CAAC,WAAW,EAAE,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,kKAAC,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC/F,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;oBACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;oBACpB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;oBACtB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBACpB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,CAAG,CAAD,IAAM,CAAC,CAAC;oBAC7C,CAAC;oBACD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACpB,CAAC,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAC3B,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;IAED;;OAEG,CACI,KAAK,GAAA;QACR,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBACnC,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,CAAC;gBACxC,CAAC;gBACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACzB,CAAC,MAAM,IAAI,kLAAc,CAAC,WAAW,EAAE,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,CAAG,CAAD,IAAM,CAAC,CAAC;gBACzC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,YAAY,oKAAI,kBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YAC/F,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,SAAS,CAAC,SAAiB,EAAE,IAAa,EAAA;QAC7C,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAChE,IAAI,IAAI,qKAAI,iBAAc,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;gBAClD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,iKAAC,kBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBAChG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,kLAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBACrH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,mKAAE,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC;YACxH,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YAC3C,CAAC;QACL,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACI,eAAe,CAAC,eAAuB,EAAA;QAC1C,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;QACrC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC5C,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YAC7D,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC3B,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;YAC9D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,eAAe,GAAA;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;OAIG,CACI,YAAY,CAAC,aAA4B,EAAA;QAC5C,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,CAAC,uBAAuB,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,uBAAuB,GAAG,aAAa,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC;QACD,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrE,IAAI,CAAC,aAAa,GAAG,CAAC,aAA4B,EAAE,CAAG,CAAD,GAAK,CAAC,iCAAiC,CAAC,aAAa,CAAC,CAAC;QAC7G,IAAI,CAAC,uBAAuB,CAAC,8BAA8B,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,CAAC,uBAAuB,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,CAAC;IACL,CAAC;IAEO,iCAAiC,CAAC,IAAmB,EAAA;QACzD,IAAI,CAAO,IAAK,CAAC,eAAe,EAAE,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC,MAAM,CAAC;YACJ,MAAM,IAAI,GAAG,IAAoB,CAAC;YAClC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC9D,CAAC;QACD,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACtF,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,KAAK,GAAA;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,MAAM,eAAe,GAAG,GAAG,EAAE;oBACzB,gLAAA,AAAkB,EACd,GAAG,CAAG,CAAD,GAAK,CAAC,cAAc,EACzB,GAAG,EAAE;oBACD,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;oBACjD,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;oBAClC,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;wBACvB,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;oBACpD,CAAC;gBACL,CAAC,EACD,SAAS,EACT,GAAG,CACN,CAAC;YACN,CAAC,CAAC;YAEF,MAAM,cAAc,GAAG;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,YAAY,EAAE,IAAI,CAAC,aAAa;gBAChC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;aACpC,CAAC;YAEF,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;YAC5G,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,WAAW,CAAC,sBAAsB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxE,CAAC;YACD,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,eAAe,EAAE,CAAC;YAElB,OAAO,WAAW,CAAC;QACvB,CAAC,MAEI,CAAC;YACF,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACI,cAAc,GAAA;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,GAAQ;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SAC1B,CAAC;QAEF,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAC1E,CAAC;YAED,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACxD,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACnD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;YAEvD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;YACxD,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC1E,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC1D,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC1D,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC5D,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,KAAK,CAAC,WAAgB,EAAE,KAAY,EAAE,OAAe,EAAE,WAAmB,EAAA;QACpF,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC;QACnC,IAAI,QAAQ,CAAC;QAEb,IAAI,WAAW,CAAC,GAAG,EAAE,CAAC;YAClB,QAAQ,GAAG,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC;QACzC,CAAC,MAAM,CAAC;YACJ,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;QACnC,CAAC;QAED,MAAM,OAAO,GAAG;YACZ,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,YAAY,EAAE,WAAW,CAAC,YAAY;SACzC,CAAC;QAEF,IAAI,QAAe,CAAC;QAEpB,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,QAAQ,GAAG,IAAI,KAAK,CAChB,SAAS,EACT,QAAQ,EACR,KAAK,EACL,GAAG,EAAE;gBACD,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC,EACD,OAAO,CACV,CAAC;YACF,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC,MAAM,CAAC;YACJ,MAAM,eAAe,GAAG,GAAG,EAAE;oBACzB,gLAAA,AAAkB,EACd,GAAG,CAAG,CAAD,UAAY,CAAC,cAAc,EAChC,GAAG,EAAE;oBACD,QAAQ,CAAC,YAAY,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;oBACrD,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC;oBAC/B,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;wBACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;oBACzD,CAAC;gBACL,CAAC,EACD,SAAS,EACT,GAAG,CACN,CAAC;YACN,CAAC,CAAC;YAEF,QAAQ,GAAG,IAAI,KAAK,CAAC,SAAS,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC1E,eAAe,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;YACvB,MAAM,aAAa,kKAAG,UAAO,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC9D,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC;YAC5B,QAAQ,CAAC,kBAAkB,CAAC,WAAW,CAAC,cAAc,IAAI,GAAG,EAAE,WAAW,CAAC,cAAc,IAAI,GAAG,EAAE,WAAW,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC;YAClI,IAAI,WAAW,CAAC,oBAAoB,EAAE,CAAC;gBACnC,MAAM,oBAAoB,kKAAG,UAAO,CAAC,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;gBACjF,QAAQ,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC;QACD,IAAI,WAAW,CAAC,eAAe,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YACrE,IAAI,aAAa,EAAE,CAAC;gBAChB,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;YACvB,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QAC7C,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,UAAU,CAAC,KAAc,EAAA;QAC7B,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAEO,0BAA0B,GAAA;QAC9B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAClC,CAAC;QACD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;6KAC9B,iBAAc,CAAC,WAAW,EAAE,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1F,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACvC,CAAC;IACL,CAAC;;AA5nCD;;GAEG,CACW,MAAA,6BAA6B,GAA2B,CAAC,CAAC,EAAE,EAAE;IACxE,kKAAM,cAAA,AAAW,EAAC,qBAAqB,CAAC,CAAC;AAC7C,CAAC,AAF0C,CAEzC;AA0nCN,sBAAsB;6JACtB,gBAAA,AAAa,EAAC,eAAe,EAAE,KAAK,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1505, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Audio/soundTrack.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Audio/soundTrack.ts"], "sourcesContent": ["import type { Sound } from \"./sound\";\r\nimport type { Analyser } from \"./analyser\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { AbstractEngine } from \"core/Engines/abstractEngine\";\r\n\r\n/**\r\n * Options allowed during the creation of a sound track.\r\n */\r\nexport interface ISoundTrackOptions {\r\n    /**\r\n     * The volume the sound track should take during creation\r\n     */\r\n    volume?: number;\r\n    /**\r\n     * Define if the sound track is the main sound track of the scene\r\n     */\r\n    mainTrack?: boolean;\r\n}\r\n\r\n/**\r\n * It could be useful to isolate your music & sounds on several tracks to better manage volume on a grouped instance of sounds.\r\n * It will be also used in a future release to apply effects on a specific track.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-sound-tracks\r\n */\r\nexport class SoundTrack {\r\n    /**\r\n     * The unique identifier of the sound track in the scene.\r\n     */\r\n    public id: number = -1;\r\n    /**\r\n     * The list of sounds included in the sound track.\r\n     */\r\n    public soundCollection: Array<Sound>;\r\n\r\n    private _outputAudioNode: Nullable<GainNode>;\r\n    private _scene: Scene;\r\n    private _connectedAnalyser: Analyser;\r\n    private _options: ISoundTrackOptions;\r\n    private _isInitialized = false;\r\n\r\n    /**\r\n     * Creates a new sound track.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-sound-tracks\r\n     * @param scene Define the scene the sound track belongs to\r\n     * @param options\r\n     */\r\n    constructor(scene?: Nullable<Scene>, options: ISoundTrackOptions = {}) {\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this._scene = scene;\r\n        this.soundCollection = [];\r\n        this._options = options;\r\n\r\n        if (!this._options.mainTrack && this._scene.soundTracks) {\r\n            this._scene.soundTracks.push(this);\r\n            this.id = this._scene.soundTracks.length - 1;\r\n        }\r\n    }\r\n\r\n    private _initializeSoundTrackAudioGraph() {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && AbstractEngine.audioEngine.audioContext) {\r\n            this._outputAudioNode = AbstractEngine.audioEngine.audioContext.createGain();\r\n            this._outputAudioNode.connect(AbstractEngine.audioEngine.masterGain);\r\n\r\n            if (this._options) {\r\n                if (this._options.volume) {\r\n                    this._outputAudioNode.gain.value = this._options.volume;\r\n                }\r\n            }\r\n\r\n            this._isInitialized = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release the sound track and its associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (AbstractEngine.audioEngine && AbstractEngine.audioEngine.canUseWebAudio) {\r\n            if (this._connectedAnalyser) {\r\n                this._connectedAnalyser.stopDebugCanvas();\r\n            }\r\n            while (this.soundCollection.length) {\r\n                this.soundCollection[0].dispose();\r\n            }\r\n            if (this._outputAudioNode) {\r\n                this._outputAudioNode.disconnect();\r\n            }\r\n            this._outputAudioNode = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds a sound to this sound track\r\n     * @param sound define the sound to add\r\n     * @ignoreNaming\r\n     */\r\n    public addSound(sound: Sound): void {\r\n        if (!this._isInitialized) {\r\n            this._initializeSoundTrackAudioGraph();\r\n        }\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\r\n            sound.connectToSoundTrackAudioNode(this._outputAudioNode);\r\n        }\r\n        if (sound.soundTrackId !== undefined) {\r\n            if (sound.soundTrackId === -1) {\r\n                this._scene.mainSoundTrack.removeSound(sound);\r\n            } else if (this._scene.soundTracks) {\r\n                this._scene.soundTracks[sound.soundTrackId].removeSound(sound);\r\n            }\r\n        }\r\n\r\n        this.soundCollection.push(sound);\r\n        sound.soundTrackId = this.id;\r\n    }\r\n\r\n    /**\r\n     * Removes a sound to this sound track\r\n     * @param sound define the sound to remove\r\n     * @ignoreNaming\r\n     */\r\n    public removeSound(sound: Sound): void {\r\n        const index = this.soundCollection.indexOf(sound);\r\n        if (index !== -1) {\r\n            this.soundCollection.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set a global volume for the full sound track.\r\n     * @param newVolume Define the new volume of the sound track\r\n     */\r\n    public setVolume(newVolume: number): void {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\r\n            this._outputAudioNode.gain.value = newVolume;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to HRTF:\r\n     * Renders a stereo output of higher quality than equalpower — it uses a convolution with measured impulse responses from human subjects.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToHRTF(): void {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\r\n            for (let i = 0; i < this.soundCollection.length; i++) {\r\n                this.soundCollection[i].switchPanningModelToHRTF();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to Equal Power:\r\n     * Represents the equal-power panning algorithm, generally regarded as simple and efficient. equalpower is the default value.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToEqualPower(): void {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\r\n            for (let i = 0; i < this.soundCollection.length; i++) {\r\n                this.soundCollection[i].switchPanningModelToEqualPower();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connect the sound track to an audio analyser allowing some amazing\r\n     * synchronization between the sounds/music and your visualization (VuMeter for instance).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser\r\n     * @param analyser The analyser to connect to the engine\r\n     */\r\n    public connectToAnalyser(analyser: Analyser): void {\r\n        if (this._connectedAnalyser) {\r\n            this._connectedAnalyser.stopDebugCanvas();\r\n        }\r\n        this._connectedAnalyser = analyser;\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\r\n            this._outputAudioNode.disconnect();\r\n            this._connectedAnalyser.connectAudioNodes(this._outputAudioNode, AbstractEngine.audioEngine.masterGain);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAIA,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,qCAAoC;;;AAqBvD,MAAO,UAAU;IAgBnB;;;;;OAKG,CACH,YAAY,KAAuB,EAAE,UAA8B,CAAA,CAAE,CAAA;QArBrE;;WAEG,CACI,IAAA,CAAA,EAAE,GAAW,CAAC,CAAC,CAAC;QAUf,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAS3B,KAAK,GAAG,KAAK,kKAAI,cAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAEO,+BAA+B,GAAA;QACnC,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,qKAAI,iBAAc,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACxF,IAAI,CAAC,gBAAgB,oKAAG,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YAC7E,IAAI,CAAC,gBAAgB,CAAC,OAAO,kKAAC,iBAAc,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAErE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC5D,CAAC;YACL,CAAC;YAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,qKAAI,iBAAc,CAAC,WAAW,qKAAI,iBAAc,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;YAC1E,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;YAC9C,CAAC;YACD,MAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAE,CAAC;gBACjC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YACtC,CAAC;YACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;YACvC,CAAC;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,QAAQ,CAAC,KAAY,EAAA;QACxB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,+BAA+B,EAAE,CAAC;QAC3C,CAAC;QACD,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtE,KAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,KAAK,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACnC,IAAI,KAAK,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAClD,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC;IACjC,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,KAAY,EAAA;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,SAAS,CAAC,SAAiB,EAAA;QAC9B,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QACjD,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,wBAAwB,GAAA;QAC3B,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;YAC7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACnD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC;YACvD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,8BAA8B,GAAA;QACjC,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;YAC7C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACnD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,8BAA8B,EAAE,CAAC;YAC7D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,QAAkB,EAAA;QACvC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QACnC,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtE,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;YACnC,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,mKAAE,iBAAc,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC5G,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Audio/audioSceneComponent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Audio/audioSceneComponent.ts"], "sourcesContent": ["import { Sound } from \"./sound\";\r\nimport { SoundTrack } from \"./soundTrack\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport type { ISceneSerializableComponent } from \"../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { Scene } from \"../scene\";\r\nimport type { AssetContainer } from \"../assetContainer\";\r\n\r\nimport \"./audioEngine\";\r\nimport { PrecisionDate } from \"../Misc/precisionDate\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport { AddParser } from \"core/Loading/Plugins/babylonFileParser.function\";\r\nimport type { IAssetContainer } from \"core/IAssetContainer\";\r\n\r\n// Adds the parser to the scene parsers.\r\nAddParser(SceneComponentConstants.NAME_AUDIO, (parsedData: any, scene: Scene, container: AssetContainer, rootUrl: string) => {\r\n    // TODO: add sound\r\n    let loadedSounds: Sound[] = [];\r\n    let loadedSound: Sound;\r\n    container.sounds = container.sounds || [];\r\n    if (parsedData.sounds !== undefined && parsedData.sounds !== null) {\r\n        for (let index = 0, cache = parsedData.sounds.length; index < cache; index++) {\r\n            const parsedSound = parsedData.sounds[index];\r\n            if (AbstractEngine.audioEngine?.canUseWebAudio) {\r\n                if (!parsedSound.url) {\r\n                    parsedSound.url = parsedSound.name;\r\n                }\r\n                if (!loadedSounds[parsedSound.url]) {\r\n                    loadedSound = Sound.Parse(parsedSound, scene, rootUrl);\r\n                    loadedSounds[parsedSound.url] = loadedSound;\r\n                    container.sounds.push(loadedSound);\r\n                } else {\r\n                    container.sounds.push(Sound.Parse(parsedSound, scene, rootUrl, loadedSounds[parsedSound.url]));\r\n                }\r\n            } else {\r\n                container.sounds.push(new Sound(parsedSound.name, null, scene));\r\n            }\r\n        }\r\n    }\r\n\r\n    loadedSounds = [];\r\n});\r\n\r\ndeclare module \"../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /**\r\n         * @internal\r\n         * Backing field\r\n         */\r\n        _mainSoundTrack: SoundTrack;\r\n        /**\r\n         * The main sound track played by the scene.\r\n         * It contains your primary collection of sounds.\r\n         */\r\n        mainSoundTrack: SoundTrack;\r\n        /**\r\n         * The list of sound tracks added to the scene\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        soundTracks: Nullable<Array<SoundTrack>>;\r\n\r\n        /**\r\n         * Gets a sound using a given name\r\n         * @param name defines the name to search for\r\n         * @returns the found sound or null if not found at all.\r\n         */\r\n        getSoundByName(name: string): Nullable<Sound>;\r\n\r\n        /**\r\n         * Gets or sets if audio support is enabled\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        audioEnabled: boolean;\r\n\r\n        /**\r\n         * Gets or sets if audio will be output to headphones\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        headphone: boolean;\r\n\r\n        /**\r\n         * Gets or sets custom audio listener position provider\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        audioListenerPositionProvider: Nullable<() => Vector3>;\r\n\r\n        /**\r\n         * Gets or sets custom audio listener rotation provider\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        audioListenerRotationProvider: Nullable<() => Vector3>;\r\n\r\n        /**\r\n         * Gets or sets a refresh rate when using 3D audio positioning\r\n         */\r\n        audioPositioningRefreshRate: number;\r\n    }\r\n}\r\n\r\nObject.defineProperty(Scene.prototype, \"mainSoundTrack\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (!this._mainSoundTrack) {\r\n            this._mainSoundTrack = new SoundTrack(this, { mainTrack: true });\r\n        }\r\n\r\n        return this._mainSoundTrack;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nScene.prototype.getSoundByName = function (name: string): Nullable<Sound> {\r\n    let index: number;\r\n    for (index = 0; index < this.mainSoundTrack.soundCollection.length; index++) {\r\n        if (this.mainSoundTrack.soundCollection[index].name === name) {\r\n            return this.mainSoundTrack.soundCollection[index];\r\n        }\r\n    }\r\n\r\n    if (this.soundTracks) {\r\n        for (let sdIndex = 0; sdIndex < this.soundTracks.length; sdIndex++) {\r\n            for (index = 0; index < this.soundTracks[sdIndex].soundCollection.length; index++) {\r\n                if (this.soundTracks[sdIndex].soundCollection[index].name === name) {\r\n                    return this.soundTracks[sdIndex].soundCollection[index];\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\nObject.defineProperty(Scene.prototype, \"audioEnabled\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.audioEnabled;\r\n    },\r\n    set: function (this: Scene, value: boolean) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (value) {\r\n            compo.enableAudio();\r\n        } else {\r\n            compo.disableAudio();\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Scene.prototype, \"headphone\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.headphone;\r\n    },\r\n    set: function (this: Scene, value: boolean) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (value) {\r\n            compo.switchAudioModeForHeadphones();\r\n        } else {\r\n            compo.switchAudioModeForNormalSpeakers();\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Scene.prototype, \"audioListenerPositionProvider\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.audioListenerPositionProvider;\r\n    },\r\n    set: function (this: Scene, value: () => Vector3) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (value && typeof value !== \"function\") {\r\n            throw new Error(\"The value passed to [Scene.audioListenerPositionProvider] must be a function that returns a Vector3\");\r\n        } else {\r\n            compo.audioListenerPositionProvider = value;\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Scene.prototype, \"audioListenerRotationProvider\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.audioListenerRotationProvider;\r\n    },\r\n    set: function (this: Scene, value: () => Vector3) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (value && typeof value !== \"function\") {\r\n            throw new Error(\"The value passed to [Scene.audioListenerRotationProvider] must be a function that returns a Vector3\");\r\n        } else {\r\n            compo.audioListenerRotationProvider = value;\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Scene.prototype, \"audioPositioningRefreshRate\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.audioPositioningRefreshRate;\r\n    },\r\n    set: function (this: Scene, value: number) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        compo.audioPositioningRefreshRate = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\n/**\r\n * Defines the sound scene component responsible to manage any sounds\r\n * in a given scene.\r\n */\r\nexport class AudioSceneComponent implements ISceneSerializableComponent {\r\n    private static _CameraDirection = new Vector3(0, 0, -1);\r\n\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_AUDIO;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    private _audioEnabled = true;\r\n    /**\r\n     * Gets whether audio is enabled or not.\r\n     * Please use related enable/disable method to switch state.\r\n     */\r\n    public get audioEnabled(): boolean {\r\n        return this._audioEnabled;\r\n    }\r\n\r\n    private _headphone = false;\r\n    /**\r\n     * Gets whether audio is outputting to headphone or not.\r\n     * Please use the according Switch methods to change output.\r\n     */\r\n    public get headphone(): boolean {\r\n        return this._headphone;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a refresh rate when using 3D audio positioning\r\n     */\r\n    public audioPositioningRefreshRate = 500;\r\n\r\n    /**\r\n     * Gets or Sets a custom listener position for all sounds in the scene\r\n     * By default, this is the position of the first active camera\r\n     */\r\n    public audioListenerPositionProvider: Nullable<() => Vector3> = null;\r\n\r\n    /**\r\n     * Gets or Sets a custom listener rotation for all sounds in the scene\r\n     * By default, this is the rotation of the first active camera\r\n     */\r\n    public audioListenerRotationProvider: Nullable<() => Vector3> = null;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene?: Nullable<Scene>) {\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this.scene = scene;\r\n\r\n        scene.soundTracks = [] as SoundTrack[];\r\n        scene.sounds = [] as Sound[];\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._afterRenderStage.registerStep(SceneComponentConstants.STEP_AFTERRENDER_AUDIO, this, this._afterRender);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing to do here. (Not rendering related)\r\n    }\r\n\r\n    /**\r\n     * Serializes the component data to the specified json object\r\n     * @param serializationObject The object to serialize to\r\n     */\r\n    public serialize(serializationObject: any): void {\r\n        serializationObject.sounds = [];\r\n\r\n        if (this.scene.soundTracks) {\r\n            for (let index = 0; index < this.scene.soundTracks.length; index++) {\r\n                const soundtrack = this.scene.soundTracks[index];\r\n\r\n                for (let soundId = 0; soundId < soundtrack.soundCollection.length; soundId++) {\r\n                    serializationObject.sounds.push(soundtrack.soundCollection[soundId].serialize());\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds all the elements from the container to the scene\r\n     * @param container the container holding the elements\r\n     */\r\n    public addFromContainer(container: IAssetContainer): void {\r\n        if (!container.sounds) {\r\n            return;\r\n        }\r\n        for (const sound of container.sounds) {\r\n            sound.play();\r\n            sound.autoplay = true;\r\n            this.scene.mainSoundTrack.addSound(sound);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes all the elements in the container from the scene\r\n     * @param container contains the elements to remove\r\n     * @param dispose if the removed element should be disposed (default: false)\r\n     */\r\n    public removeFromContainer(container: IAssetContainer, dispose = false): void {\r\n        if (!container.sounds) {\r\n            return;\r\n        }\r\n        for (const sound of container.sounds) {\r\n            sound.stop();\r\n            sound.autoplay = false;\r\n            this.scene.mainSoundTrack.removeSound(sound);\r\n            if (dispose) {\r\n                sound.dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        const scene = this.scene;\r\n        if (scene._mainSoundTrack) {\r\n            scene.mainSoundTrack.dispose();\r\n        }\r\n\r\n        if (scene.soundTracks) {\r\n            for (let scIndex = 0; scIndex < scene.soundTracks.length; scIndex++) {\r\n                scene.soundTracks[scIndex].dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disables audio in the associated scene.\r\n     */\r\n    public disableAudio() {\r\n        const scene = this.scene;\r\n        this._audioEnabled = false;\r\n\r\n        if (AbstractEngine.audioEngine && AbstractEngine.audioEngine.audioContext) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            AbstractEngine.audioEngine.audioContext.suspend();\r\n        }\r\n\r\n        let i: number;\r\n        for (i = 0; i < scene.mainSoundTrack.soundCollection.length; i++) {\r\n            scene.mainSoundTrack.soundCollection[i].pause();\r\n        }\r\n        if (scene.soundTracks) {\r\n            for (i = 0; i < scene.soundTracks.length; i++) {\r\n                for (let j = 0; j < scene.soundTracks[i].soundCollection.length; j++) {\r\n                    scene.soundTracks[i].soundCollection[j].pause();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enables audio in the associated scene.\r\n     */\r\n    public enableAudio() {\r\n        const scene = this.scene;\r\n        this._audioEnabled = true;\r\n\r\n        if (AbstractEngine.audioEngine && AbstractEngine.audioEngine.audioContext) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            AbstractEngine.audioEngine.audioContext.resume();\r\n        }\r\n\r\n        let i: number;\r\n        for (i = 0; i < scene.mainSoundTrack.soundCollection.length; i++) {\r\n            if (scene.mainSoundTrack.soundCollection[i].isPaused) {\r\n                scene.mainSoundTrack.soundCollection[i].play();\r\n            }\r\n        }\r\n        if (scene.soundTracks) {\r\n            for (i = 0; i < scene.soundTracks.length; i++) {\r\n                for (let j = 0; j < scene.soundTracks[i].soundCollection.length; j++) {\r\n                    if (scene.soundTracks[i].soundCollection[j].isPaused) {\r\n                        scene.soundTracks[i].soundCollection[j].play();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch audio to headphone output.\r\n     */\r\n    public switchAudioModeForHeadphones() {\r\n        const scene = this.scene;\r\n        this._headphone = true;\r\n\r\n        scene.mainSoundTrack.switchPanningModelToHRTF();\r\n        if (scene.soundTracks) {\r\n            for (let i = 0; i < scene.soundTracks.length; i++) {\r\n                scene.soundTracks[i].switchPanningModelToHRTF();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch audio to normal speakers.\r\n     */\r\n    public switchAudioModeForNormalSpeakers() {\r\n        const scene = this.scene;\r\n        this._headphone = false;\r\n\r\n        scene.mainSoundTrack.switchPanningModelToEqualPower();\r\n\r\n        if (scene.soundTracks) {\r\n            for (let i = 0; i < scene.soundTracks.length; i++) {\r\n                scene.soundTracks[i].switchPanningModelToEqualPower();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _cachedCameraDirection = new Vector3();\r\n    private _cachedCameraPosition = new Vector3();\r\n    private _lastCheck = 0;\r\n    private _invertMatrixTemp = new Matrix();\r\n    private _cameraDirectionTemp = new Vector3();\r\n\r\n    private _afterRender() {\r\n        const now = PrecisionDate.Now;\r\n        if (this._lastCheck && now - this._lastCheck < this.audioPositioningRefreshRate) {\r\n            return;\r\n        }\r\n\r\n        this._lastCheck = now;\r\n\r\n        const scene = this.scene;\r\n        if (!this._audioEnabled || !scene._mainSoundTrack || !scene.soundTracks || (scene._mainSoundTrack.soundCollection.length === 0 && scene.soundTracks.length === 1)) {\r\n            return;\r\n        }\r\n\r\n        const audioEngine = AbstractEngine.audioEngine;\r\n\r\n        if (!audioEngine) {\r\n            return;\r\n        }\r\n\r\n        if (audioEngine.audioContext) {\r\n            let listeningCamera = scene.activeCamera;\r\n            if (scene.activeCameras && scene.activeCameras.length > 0) {\r\n                listeningCamera = scene.activeCameras[0];\r\n            }\r\n\r\n            // A custom listener position provider was set\r\n            // Use the users provided position instead of camera's\r\n            if (this.audioListenerPositionProvider) {\r\n                const position: Vector3 = this.audioListenerPositionProvider();\r\n                // Set the listener position\r\n                audioEngine.audioContext.listener.setPosition(position.x || 0, position.y || 0, position.z || 0);\r\n                // Check if there is a listening camera\r\n            } else if (listeningCamera) {\r\n                // Set the listener position to the listening camera global position\r\n                if (!this._cachedCameraPosition.equals(listeningCamera.globalPosition)) {\r\n                    this._cachedCameraPosition.copyFrom(listeningCamera.globalPosition);\r\n                    audioEngine.audioContext.listener.setPosition(listeningCamera.globalPosition.x, listeningCamera.globalPosition.y, listeningCamera.globalPosition.z);\r\n                }\r\n            }\r\n            // Otherwise set the listener position to 0, 0 ,0\r\n            else {\r\n                // Set the listener position\r\n                audioEngine.audioContext.listener.setPosition(0, 0, 0);\r\n            }\r\n\r\n            // A custom listener rotation provider was set\r\n            // Use the users provided rotation instead of camera's\r\n            if (this.audioListenerRotationProvider) {\r\n                const rotation: Vector3 = this.audioListenerRotationProvider();\r\n                audioEngine.audioContext.listener.setOrientation(rotation.x || 0, rotation.y || 0, rotation.z || 0, 0, 1, 0);\r\n                // Check if there is a listening camera\r\n            } else if (listeningCamera) {\r\n                // for VR cameras\r\n                if (listeningCamera.rigCameras && listeningCamera.rigCameras.length > 0) {\r\n                    listeningCamera = listeningCamera.rigCameras[0];\r\n                }\r\n\r\n                listeningCamera.getViewMatrix().invertToRef(this._invertMatrixTemp);\r\n                Vector3.TransformNormalToRef(AudioSceneComponent._CameraDirection, this._invertMatrixTemp, this._cameraDirectionTemp);\r\n                this._cameraDirectionTemp.normalize();\r\n                // To avoid some errors on GearVR\r\n                if (!isNaN(this._cameraDirectionTemp.x) && !isNaN(this._cameraDirectionTemp.y) && !isNaN(this._cameraDirectionTemp.z)) {\r\n                    if (!this._cachedCameraDirection.equals(this._cameraDirectionTemp)) {\r\n                        this._cachedCameraDirection.copyFrom(this._cameraDirectionTemp);\r\n                        audioEngine.audioContext.listener.setOrientation(this._cameraDirectionTemp.x, this._cameraDirectionTemp.y, this._cameraDirectionTemp.z, 0, 1, 0);\r\n                    }\r\n                }\r\n            }\r\n            // Otherwise set the listener rotation to 0, 0 ,0\r\n            else {\r\n                // Set the listener position\r\n                audioEngine.audioContext.listener.setOrientation(0, 0, 0, 0, 1, 0);\r\n            }\r\n\r\n            let i: number;\r\n            for (i = 0; i < scene.mainSoundTrack.soundCollection.length; i++) {\r\n                const sound = scene.mainSoundTrack.soundCollection[i];\r\n                if (sound.useCustomAttenuation) {\r\n                    sound.updateDistanceFromListener();\r\n                }\r\n            }\r\n            if (scene.soundTracks) {\r\n                for (i = 0; i < scene.soundTracks.length; i++) {\r\n                    for (let j = 0; j < scene.soundTracks[i].soundCollection.length; j++) {\r\n                        const sound = scene.soundTracks[i].soundCollection[j];\r\n                        if (sound.useCustomAttenuation) {\r\n                            sound.updateDistanceFromListener();\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nSound._SceneComponentInitialization = (scene: Scene) => {\r\n    let compo = scene._getComponent(SceneComponentConstants.NAME_AUDIO);\r\n    if (!compo) {\r\n        compo = new AudioSceneComponent(scene);\r\n        scene._addComponent(compo);\r\n    }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAEvD,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAGjC,OAAO,eAAe,CAAC;AACvB,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,qCAAoC;AAC7D,OAAO,EAAE,SAAS,EAAE,yDAAwD;;;;;;;;;;;AAG5E,wCAAwC;8LACxC,aAAA,AAAS,wJAAC,0BAAuB,CAAC,UAAU,EAAE,CAAC,UAAe,EAAE,KAAY,EAAE,SAAyB,EAAE,OAAe,EAAE,EAAE;IACxH,kBAAkB;IAClB,IAAI,YAAY,GAAY,EAAE,CAAC;IAC/B,IAAI,WAAkB,CAAC;IACvB,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,IAAI,EAAE,CAAC;IAC1C,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;QAChE,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YAC3E,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC7C,qKAAI,iBAAc,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;gBAC7C,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;oBACnB,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC;gBACvC,CAAC;gBACD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;oBACjC,WAAW,yJAAG,QAAK,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;oBACvD,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;oBAC5C,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACvC,CAAC,MAAM,CAAC;oBACJ,SAAS,CAAC,MAAM,CAAC,IAAI,uJAAC,QAAK,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACnG,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,0JAAI,QAAK,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;YACpE,CAAC;QACL,CAAC;IACL,CAAC;IAED,YAAY,GAAG,EAAE,CAAC;AACtB,CAAC,CAAC,CAAC;AA2DH,MAAM,CAAC,cAAc,CAAC,qJAAK,CAAC,SAAS,EAAE,gBAAgB,EAAE;IACrD,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,GAAG,+JAAI,aAAU,CAAC,IAAI,EAAE;gBAAE,SAAS,EAAE,IAAI;YAAA,CAAE,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;6IAEH,QAAK,CAAC,SAAS,CAAC,cAAc,GAAG,SAAU,IAAY;IACnD,IAAI,KAAa,CAAC;IAClB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;QAC1E,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,CAAE,CAAC;YACjE,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAChF,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;oBACjE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC5D,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,CAAC,cAAc,8IAAC,QAAK,CAAC,SAAS,EAAE,cAAc,EAAE;IACnD,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,gLAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,KAAK,CAAC,YAAY,CAAC;IAC9B,CAAC;IACD,GAAG,EAAE,SAAuB,KAAc;QACtC,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,KAAK,CAAC,WAAW,EAAE,CAAC;QACxB,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,YAAY,EAAE,CAAC;QACzB,CAAC;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,8IAAC,QAAK,CAAC,SAAS,EAAE,WAAW,EAAE;IAChD,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,KAAK,CAAC,SAAS,CAAC;IAC3B,CAAC;IACD,GAAG,EAAE,SAAuB,KAAc;QACtC,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,gLAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,KAAK,CAAC,4BAA4B,EAAE,CAAC;QACzC,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,gCAAgC,EAAE,CAAC;QAC7C,CAAC;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,8IAAC,QAAK,CAAC,SAAS,EAAE,+BAA+B,EAAE;IACpE,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,KAAK,CAAC,6BAA6B,CAAC;IAC/C,CAAC;IACD,GAAG,EAAE,SAAuB,KAAoB;QAC5C,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,qGAAqG,CAAC,CAAC;QAC3H,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,6BAA6B,GAAG,KAAK,CAAC;QAChD,CAAC;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,qJAAK,CAAC,SAAS,EAAE,+BAA+B,EAAE;IACpE,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,KAAK,CAAC,6BAA6B,CAAC;IAC/C,CAAC;IACD,GAAG,EAAE,SAAuB,KAAoB;QAC5C,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,gLAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,qGAAqG,CAAC,CAAC;QAC3H,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,6BAA6B,GAAG,KAAK,CAAC;QAChD,CAAC;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,8IAAC,QAAK,CAAC,SAAS,EAAE,6BAA6B,EAAE;IAClE,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,KAAK,CAAC,2BAA2B,CAAC;IAC7C,CAAC;IACD,GAAG,EAAE,SAAuB,KAAa;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,KAAK,CAAC,2BAA2B,GAAG,KAAK,CAAC;IAC9C,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAMG,MAAO,mBAAmB;IAc5B;;;OAGG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAGD;;;OAGG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAmBD;;;OAGG,CACH,YAAY,KAAuB,CAAA;QAjDnC;;WAEG,CACa,IAAA,CAAA,IAAI,yJAAG,0BAAuB,CAAC,UAAU,CAAC;QAOlD,IAAA,CAAA,aAAa,GAAG,IAAI,CAAC;QASrB,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAS3B;;WAEG,CACI,IAAA,CAAA,2BAA2B,GAAG,GAAG,CAAC;QAEzC;;;WAGG,CACI,IAAA,CAAA,6BAA6B,GAA4B,IAAI,CAAC;QAErE;;;WAGG,CACI,IAAA,CAAA,6BAA6B,GAA4B,IAAI,CAAC;QAyL7D,IAAA,CAAA,sBAAsB,GAAG,mKAAI,UAAO,EAAE,CAAC;QACvC,IAAA,CAAA,qBAAqB,GAAG,IAAI,yKAAO,EAAE,CAAC;QACtC,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QACf,IAAA,CAAA,iBAAiB,GAAG,mKAAI,SAAM,EAAE,CAAC;QACjC,IAAA,CAAA,oBAAoB,GAAG,mKAAI,UAAO,EAAE,CAAC;QAtLzC,KAAK,GAAG,KAAK,kKAAI,cAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,KAAK,CAAC,WAAW,GAAG,EAAkB,CAAC;QACvC,KAAK,CAAC,MAAM,GAAG,EAAa,CAAC;IACjC,CAAC;IAED;;OAEG,CACI,QAAQ,GAAA;QACX,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,YAAY,uJAAC,0BAAuB,CAAC,sBAAsB,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACvH,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;IACV,8CAA8C;IAClD,CAAC;IAED;;;OAGG,CACI,SAAS,CAAC,mBAAwB,EAAA;QACrC,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACzB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACjE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBAEjD,IAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,CAAE,CAAC;oBAC3E,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;gBACrF,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,gBAAgB,CAAC,SAA0B,EAAA;QAC9C,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,CAAE,CAAC;YACnC,KAAK,CAAC,IAAI,EAAE,CAAC;YACb,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,SAA0B,EAAE,OAAO,GAAG,KAAK,EAAA;QAClE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,CAAE,CAAC;YACnC,KAAK,CAAC,IAAI,EAAE,CAAC;YACb,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,OAAO,EAAE,CAAC;gBACV,KAAK,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QACnC,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACpB,IAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,CAAE,CAAC;gBAClE,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;YACzC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,YAAY,GAAA;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,qKAAI,iBAAc,CAAC,WAAW,qKAAI,iBAAc,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACxE,mEAAmE;6KACnE,iBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QACtD,CAAC;QAED,IAAI,CAAS,CAAC;QACd,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC/D,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QACpD,CAAC;QACD,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACpB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC5C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;oBACnE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;gBACpD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,WAAW,GAAA;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,qKAAI,iBAAc,CAAC,WAAW,qKAAI,iBAAc,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACxE,mEAAmE;4KACnE,kBAAc,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QACrD,CAAC;QAED,IAAI,CAAS,CAAC;QACd,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC/D,IAAI,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACnD,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnD,CAAC;QACL,CAAC;QACD,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACpB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC5C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;oBACnE,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;wBACnD,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBACnD,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,4BAA4B,GAAA;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,KAAK,CAAC,cAAc,CAAC,wBAAwB,EAAE,CAAC;QAChD,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAChD,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC;YACpD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,gCAAgC,GAAA;QACnC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,KAAK,CAAC,cAAc,CAAC,8BAA8B,EAAE,CAAC;QAEtD,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAChD,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,8BAA8B,EAAE,CAAC;YAC1D,CAAC;QACL,CAAC;IACL,CAAC;IAQO,YAAY,GAAA;QAChB,MAAM,GAAG,gKAAG,gBAAa,CAAC,GAAG,CAAC;QAC9B,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;YAC9E,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QAEtB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,AAAC,KAAK,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAE,CAAC;YAChK,OAAO;QACX,CAAC;QAED,MAAM,WAAW,oKAAG,iBAAc,CAAC,WAAW,CAAC;QAE/C,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QAED,IAAI,WAAW,CAAC,YAAY,EAAE,CAAC;YAC3B,IAAI,eAAe,GAAG,KAAK,CAAC,YAAY,CAAC;YACzC,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxD,eAAe,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC;YAED,8CAA8C;YAC9C,sDAAsD;YACtD,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAY,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBAC/D,4BAA4B;gBAC5B,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YACjG,uCAAuC;YAC3C,CAAC,MAAM,IAAI,eAAe,EAAE,CAAC;gBACzB,oEAAoE;gBACpE,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC;oBACrE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;oBACpE,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBACxJ,CAAC;YACL,CAAC,MAEI,CAAC;gBACF,4BAA4B;gBAC5B,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3D,CAAC;YAED,8CAA8C;YAC9C,sDAAsD;YACtD,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAY,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBAC/D,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7G,uCAAuC;YAC3C,CAAC,MAAM,IAAI,eAAe,EAAE,CAAC;gBACzB,iBAAiB;gBACjB,IAAI,eAAe,CAAC,UAAU,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtE,eAAe,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC;gBAED,eAAe,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;+KACpE,UAAO,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACtH,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAC;gBACtC,iCAAiC;gBACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC;oBACpH,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC;wBACjE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;wBAChE,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACrJ,CAAC;gBACL,CAAC;YACL,CAAC,MAEI,CAAC;gBACF,4BAA4B;gBAC5B,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvE,CAAC;YAED,IAAI,CAAS,CAAC;YACd,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC/D,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACtD,IAAI,KAAK,CAAC,oBAAoB,EAAE,CAAC;oBAC7B,KAAK,CAAC,0BAA0B,EAAE,CAAC;gBACvC,CAAC;YACL,CAAC;YACD,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBACpB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;oBAC5C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;wBACnE,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACtD,IAAI,KAAK,CAAC,oBAAoB,EAAE,CAAC;4BAC7B,KAAK,CAAC,0BAA0B,EAAE,CAAC;wBACvC,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;;AAxUc,oBAAA,gBAAgB,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,AAAxB,CAAyB;AA2U5D,8JAAK,CAAC,6BAA6B,GAAG,CAAC,KAAY,EAAE,EAAE;IACnD,IAAI,KAAK,GAAG,KAAK,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAC,CAAC;IACpE,IAAI,CAAC,KAAK,EAAE,CAAC;QACT,KAAK,GAAG,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACvC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;AACL,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2122, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Audio/weightedsound.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Audio/weightedsound.ts"], "sourcesContent": ["import type { Sound } from \"../Audio/sound\";\r\nimport { Logger } from \"../Misc/logger\";\r\n\r\n/**\r\n * Wraps one or more Sound objects and selects one with random weight for playback.\r\n */\r\nexport class WeightedSound {\r\n    /** When true a Sound will be selected and played when the current playing Sound completes. */\r\n    public loop: boolean = false;\r\n    private _coneInnerAngle: number = 360;\r\n    private _coneOuterAngle: number = 360;\r\n    private _volume: number = 1;\r\n    /** A Sound is currently playing. */\r\n    public isPlaying: boolean = false;\r\n    /** A Sound is currently paused. */\r\n    public isPaused: boolean = false;\r\n\r\n    private _sounds: Sound[] = [];\r\n    private _weights: number[] = [];\r\n    private _currentIndex?: number;\r\n\r\n    /**\r\n     * Creates a new WeightedSound from the list of sounds given.\r\n     * @param loop When true a Sound will be selected and played when the current playing Sound completes.\r\n     * @param sounds Array of Sounds that will be selected from.\r\n     * @param weights Array of number values for selection weights; length must equal sounds, values will be normalized to 1\r\n     */\r\n    constructor(loop: boolean, sounds: Sound[], weights: number[]) {\r\n        if (sounds.length !== weights.length) {\r\n            throw new Error(\"Sounds length does not equal weights length\");\r\n        }\r\n\r\n        this.loop = loop;\r\n        this._weights = weights;\r\n        // Normalize the weights\r\n        let weightSum = 0;\r\n        for (const weight of weights) {\r\n            weightSum += weight;\r\n        }\r\n        const invWeightSum = weightSum > 0 ? 1 / weightSum : 0;\r\n        for (let i = 0; i < this._weights.length; i++) {\r\n            this._weights[i] *= invWeightSum;\r\n        }\r\n        this._sounds = sounds;\r\n        for (const sound of this._sounds) {\r\n            sound.onEndedObservable.add(() => {\r\n                this._onended();\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * The size of cone in degrees for a directional sound in which there will be no attenuation.\r\n     */\r\n    public get directionalConeInnerAngle(): number {\r\n        return this._coneInnerAngle;\r\n    }\r\n\r\n    /**\r\n     * The size of cone in degrees for a directional sound in which there will be no attenuation.\r\n     */\r\n    public set directionalConeInnerAngle(value: number) {\r\n        if (value !== this._coneInnerAngle) {\r\n            if (this._coneOuterAngle < value) {\r\n                Logger.Error(\"directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneInnerAngle = value;\r\n            for (const sound of this._sounds) {\r\n                sound.directionalConeInnerAngle = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Size of cone in degrees for a directional sound outside of which there will be no sound.\r\n     * Listener angles between innerAngle and outerAngle will falloff linearly.\r\n     */\r\n    public get directionalConeOuterAngle(): number {\r\n        return this._coneOuterAngle;\r\n    }\r\n\r\n    /**\r\n     * Size of cone in degrees for a directional sound outside of which there will be no sound.\r\n     * Listener angles between innerAngle and outerAngle will falloff linearly.\r\n     */\r\n    public set directionalConeOuterAngle(value: number) {\r\n        if (value !== this._coneOuterAngle) {\r\n            if (value < this._coneInnerAngle) {\r\n                Logger.Error(\"directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneOuterAngle = value;\r\n            for (const sound of this._sounds) {\r\n                sound.directionalConeOuterAngle = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Playback volume.\r\n     */\r\n    public get volume(): number {\r\n        return this._volume;\r\n    }\r\n\r\n    /**\r\n     * Playback volume.\r\n     */\r\n    public set volume(value: number) {\r\n        if (value !== this._volume) {\r\n            for (const sound of this._sounds) {\r\n                sound.setVolume(value);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _onended() {\r\n        if (this._currentIndex !== undefined) {\r\n            this._sounds[this._currentIndex].autoplay = false;\r\n        }\r\n        if (this.loop && this.isPlaying) {\r\n            this.play();\r\n        } else {\r\n            this.isPlaying = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Suspend playback\r\n     */\r\n    public pause() {\r\n        if (this.isPlaying) {\r\n            this.isPaused = true;\r\n            if (this._currentIndex !== undefined) {\r\n                this._sounds[this._currentIndex].pause();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stop playback\r\n     */\r\n    public stop() {\r\n        this.isPlaying = false;\r\n        if (this._currentIndex !== undefined) {\r\n            this._sounds[this._currentIndex].stop();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Start playback.\r\n     * @param startOffset Position the clip head at a specific time in seconds.\r\n     */\r\n    public play(startOffset?: number) {\r\n        if (!this.isPaused) {\r\n            this.stop();\r\n            const randomValue = Math.random();\r\n            let total = 0;\r\n            for (let i = 0; i < this._weights.length; i++) {\r\n                total += this._weights[i];\r\n                if (randomValue <= total) {\r\n                    this._currentIndex = i;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        const sound = this._sounds[this._currentIndex ?? 0];\r\n        if (sound.isReady()) {\r\n            sound.play(0, this.isPaused ? undefined : startOffset);\r\n        } else {\r\n            sound.autoplay = true;\r\n        }\r\n        this.isPlaying = true;\r\n        this.isPaused = false;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;;AAKlC,MAAO,aAAa;IAetB;;;;;OAKG,CACH,YAAY,IAAa,EAAE,MAAe,EAAE,OAAiB,CAAA;QApB7D,4FAAA,EAA8F,CACvF,IAAA,CAAA,IAAI,GAAY,KAAK,CAAC;QACrB,IAAA,CAAA,eAAe,GAAW,GAAG,CAAC;QAC9B,IAAA,CAAA,eAAe,GAAW,GAAG,CAAC;QAC9B,IAAA,CAAA,OAAO,GAAW,CAAC,CAAC;QAC5B,kCAAA,EAAoC,CAC7B,IAAA,CAAA,SAAS,GAAY,KAAK,CAAC;QAClC,iCAAA,EAAmC,CAC5B,IAAA,CAAA,QAAQ,GAAY,KAAK,CAAC;QAEzB,IAAA,CAAA,OAAO,GAAY,EAAE,CAAC;QACtB,IAAA,CAAA,QAAQ,GAAa,EAAE,CAAC;QAU5B,IAAI,MAAM,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,wBAAwB;QACxB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,KAAK,MAAM,MAAM,IAAI,OAAO,CAAE,CAAC;YAC3B,SAAS,IAAI,MAAM,CAAC;QACxB,CAAC;QACD,MAAM,YAAY,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC5C,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAC/B,KAAK,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpB,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG,CACH,IAAW,yBAAyB,CAAC,KAAa,EAAA;QAC9C,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,eAAe,GAAG,KAAK,EAAE,CAAC;sKAC/B,SAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;YACX,CAAC;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAC/B,KAAK,CAAC,yBAAyB,GAAG,KAAK,CAAC;YAC5C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACH,IAAW,yBAAyB,GAAA;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;OAGG,CACH,IAAW,yBAAyB,CAAC,KAAa,EAAA;QAC9C,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;YACjC,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;sKAC/B,SAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;YACX,CAAC;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAC/B,KAAK,CAAC,yBAAyB,GAAG,KAAK,CAAC;YAC5C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;YACzB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAC/B,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC3B,CAAC;QACL,CAAC;IACL,CAAC;IAEO,QAAQ,GAAA;QACZ,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtD,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAC3B,CAAC;IACL,CAAC;IAED;;OAEG,CACI,KAAK,GAAA;QACR,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;gBACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,EAAE,CAAC;YAC7C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,IAAI,GAAA;QACP,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;QAC5C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,IAAI,CAAC,WAAoB,EAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAClC,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC5C,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,WAAW,IAAI,KAAK,EAAE,CAAC;oBACvB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;oBACvB,MAAM;gBACV,CAAC;YACL,CAAC;QACL,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC;QACpD,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAClB,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC3D,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 2275, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Audio/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Audio/index.ts"], "sourcesContent": ["export * from \"./Interfaces/IAudioEngine\";\r\nexport * from \"./Interfaces/ISoundOptions\";\r\nexport * from \"./analyser\";\r\nexport * from \"./audioEngine\";\r\nexport * from \"./audioSceneComponent\";\r\nexport * from \"./sound\";\r\nexport * from \"./soundTrack\";\r\nexport * from \"./weightedsound\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,2BAA2B,CAAC;AAC1C,cAAc,4BAA4B,CAAC;AAC3C,cAAc,YAAY,CAAC;AAC3B,cAAc,eAAe,CAAC;AAC9B,cAAc,uBAAuB,CAAC;AACtC,cAAc,SAAS,CAAC;AACxB,cAAc,cAAc,CAAC;AAC7B,cAAc,iBAAiB,CAAC", "debugId": null}}]}