{"version": 3, "file": "staticSoundBuffer.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/abstractAudio/staticSoundBuffer.ts"], "names": [], "mappings": "AAEA,IAAI,mBAAmB,GAAG,CAAC,CAAC;AAyB5B;;;;;;;;;;GAUG;AACH,MAAM,OAAgB,iBAAiB;IAWnC,YAAsB,MAAqB;QAL3C;;WAEG;QACI,SAAI,GAAW,sBAAsB,mBAAmB,EAAE,EAAE,CAAC;QAGhE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;CA2BJ", "sourcesContent": ["import type { AudioEngineV2 } from \"./audioEngineV2\";\n\nlet StaticSoundBufferId = 1;\n\n/**\n * Options for creating a static sound buffer.\n */\nexport interface IStaticSoundBufferOptions {\n    /**\n     * Whether to skip codec checking before attempting to load each source URL when `source` is a string array. Defaults to `false`.\n     * - Has no effect if the sound's source is not a string array.\n     * @see {@link CreateSoundAsync} `source` parameter.\n     */\n    skipCodecCheck: boolean;\n}\n\n/**\n * Options for cloning a static sound buffer.\n * - @see {@link StaticSoundBuffer.clone}.\n */\nexport interface IStaticSoundBufferCloneOptions {\n    /**\n     * The name of the cloned sound buffer. Defaults to `StaticSoundBuffer #${id}`.\n     */\n    name: string;\n}\n\n/**\n * Abstract class representing a static sound buffer.\n *\n * A static sound buffer is a fully downloaded and decoded array of audio data that is ready to be played.\n *\n * Static sound buffers can be reused multiple times by different {@link StaticSound} instances.\n *\n * Static sound buffers are created by the {@link CreateSoundBufferAsync} function.\n *\n * @see {@link StaticSound.buffer}\n */\nexport abstract class StaticSoundBuffer {\n    /**\n     * The engine that the sound buffer belongs to.\n     */\n    public readonly engine: AudioEngineV2;\n\n    /**\n     * The name of the sound buffer.\n     */\n    public name: string = `StaticSoundBuffer #${StaticSoundBufferId++}`;\n\n    protected constructor(engine: AudioEngineV2) {\n        this.engine = engine;\n    }\n\n    /**\n     * The sample rate of the sound buffer.\n     */\n    public abstract readonly sampleRate: number;\n\n    /**\n     * The length of the sound buffer, in sample frames.\n     */\n    public abstract readonly length: number;\n\n    /**\n     * The duration of the sound buffer, in seconds.\n     */\n    public abstract readonly duration: number;\n\n    /**\n     * The number of channels in the sound buffer.\n     */\n    public abstract readonly channelCount: number;\n\n    /**\n     * Clones the sound buffer.\n     * @param options Options for cloning the sound buffer.\n     */\n    public abstract clone(options?: Partial<IStaticSoundBufferCloneOptions>): StaticSoundBuffer;\n}\n"]}