{"version": 3, "file": "internalDeviceSourceManager.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/DeviceInput/internalDeviceSourceManager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,4BAA4B,CAAC;AAGxD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AA+B3D,gBAAgB;AAChB,MAAM,OAAO,2BAA2B;IAYpC,YAAmB,MAAsB;QAJxB,wBAAmB,GAAG,IAAI,KAAK,EAAsB,CAAC;QAEhE,cAAS,GAAG,CAAC,CAAC;QA4CrB,mBAAmB;QACH,oBAAe,GAAG,CAAC,OAA2B,EAAQ,EAAE;YACpE,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC;gBACvE,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBACzC,KAAK,MAAM,aAAa,IAAI,MAAM,EAAE,CAAC;oBACjC,MAAM,UAAU,GAAG,CAAC,aAAa,CAAC;oBAClC,OAAO,CAAC,UAAU,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;gBAC1F,CAAC;YACL,CAAC;YACD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC,CAAC;QAEc,sBAAiB,GAAG,CAAC,OAA2B,EAAQ,EAAE;YACtE,MAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAEtD,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC;gBACX,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC,CAAC;QA3DE,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAgB,mBAAmB,CAAC,CAAC;QAE9D,MAAM,iBAAiB,GAAG,CAAC,UAAsB,EAAE,UAAkB,EAAE,EAAE;YACrE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,KAAK,EAAU,CAAC;YACpD,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;YACvD,CAAC;YACD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7C,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;gBACvF,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YACrC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,oBAAoB,GAAG,CAAC,UAAsB,EAAE,UAAkB,EAAE,EAAE;YACxE,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;YACjD,CAAC;YACD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7C,OAAO,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAClD,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,CAAC,UAAsB,EAAE,UAAkB,EAAE,SAAmB,EAAE,EAAE;YACvF,IAAI,SAAS,EAAE,CAAC;gBACZ,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC7C,OAAO,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,uBAAuB,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;QACnH,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,kBAAkB,GAAG,IAAI,oBAAoB,CAAC,MAAM,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,cAAc,CAAC,CAAC;QACxH,CAAC;IACL,CAAC;IAsBM,OAAO;QACV,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;IACtC,CAAC;CACJ", "sourcesContent": ["import type { IDisposable } from \"../scene\";\r\nimport { DeviceType } from \"./InputDevices/deviceEnums\";\r\nimport type { Observable } from \"../Misc/observable\";\r\nimport type { IDeviceInputSystem } from \"./inputInterfaces\";\r\nimport { NativeDeviceInputSystem } from \"./nativeDeviceInputSystem\";\r\nimport { WebDeviceInputSystem } from \"./webDeviceInputSystem\";\r\nimport { DeviceSource } from \"./InputDevices/deviceSource\";\r\nimport type { INative } from \"../Engines/Native/nativeInterfaces\";\r\nimport type { IUIEvent } from \"../Events/deviceInputEvents\";\r\nimport type { AbstractEngine } from \"../Engines/abstractEngine\";\r\n\r\ntype Distribute<T> = T extends DeviceType ? DeviceSource<T> : never;\r\n\r\nexport type DeviceSourceType = Distribute<DeviceType>;\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare const _native: INative;\r\n\r\ndeclare module \"../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    interface AbstractEngine {\r\n        /** @internal */\r\n        _deviceSourceManager?: InternalDeviceSourceManager;\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport interface IObservableManager {\r\n    onDeviceConnectedObservable: Observable<DeviceSourceType>;\r\n    onDeviceDisconnectedObservable: Observable<DeviceSourceType>;\r\n\r\n    // Functions\r\n    _onInputChanged(deviceType: DeviceType, deviceSlot: number, eventData: IUIEvent): void;\r\n    _addDevice(deviceSource: DeviceSource<DeviceType>): void;\r\n    _removeDevice(deviceType: DeviceType, deviceSlot: number): void;\r\n}\r\n\r\n/** @internal */\r\nexport class InternalDeviceSourceManager implements IDisposable {\r\n    // Public Members\r\n    public readonly _deviceInputSystem: IDeviceInputSystem;\r\n\r\n    // Private Members\r\n    // This is a master list of all device type/slot combos\r\n    private readonly _devices: Array<Array<number>>;\r\n\r\n    private readonly _registeredManagers = new Array<IObservableManager>();\r\n\r\n    public _refCount = 0;\r\n\r\n    public constructor(engine: AbstractEngine) {\r\n        const numberOfDeviceTypes = Object.keys(DeviceType).length / 2;\r\n        this._devices = new Array<Array<number>>(numberOfDeviceTypes);\r\n\r\n        const onDeviceConnected = (deviceType: DeviceType, deviceSlot: number) => {\r\n            if (!this._devices[deviceType]) {\r\n                this._devices[deviceType] = new Array<number>();\r\n            }\r\n\r\n            if (!this._devices[deviceType][deviceSlot]) {\r\n                this._devices[deviceType][deviceSlot] = deviceSlot;\r\n            }\r\n            for (const manager of this._registeredManagers) {\r\n                const deviceSource = new DeviceSource(this._deviceInputSystem, deviceType, deviceSlot);\r\n                manager._addDevice(deviceSource);\r\n            }\r\n        };\r\n\r\n        const onDeviceDisconnected = (deviceType: DeviceType, deviceSlot: number) => {\r\n            if (this._devices[deviceType]?.[deviceSlot]) {\r\n                delete this._devices[deviceType][deviceSlot];\r\n            }\r\n            for (const manager of this._registeredManagers) {\r\n                manager._removeDevice(deviceType, deviceSlot);\r\n            }\r\n        };\r\n\r\n        const onInputChanged = (deviceType: DeviceType, deviceSlot: number, eventData: IUIEvent) => {\r\n            if (eventData) {\r\n                for (const manager of this._registeredManagers) {\r\n                    manager._onInputChanged(deviceType, deviceSlot, eventData);\r\n                }\r\n            }\r\n        };\r\n\r\n        if (typeof _native !== \"undefined\") {\r\n            this._deviceInputSystem = new NativeDeviceInputSystem(onDeviceConnected, onDeviceDisconnected, onInputChanged);\r\n        } else {\r\n            this._deviceInputSystem = new WebDeviceInputSystem(engine, onDeviceConnected, onDeviceDisconnected, onInputChanged);\r\n        }\r\n    }\r\n\r\n    // Public Functions\r\n    public readonly registerManager = (manager: IObservableManager): void => {\r\n        for (let deviceType = 0; deviceType < this._devices.length; deviceType++) {\r\n            const device = this._devices[deviceType];\r\n            for (const deviceSlotKey in device) {\r\n                const deviceSlot = +deviceSlotKey;\r\n                manager._addDevice(new DeviceSource(this._deviceInputSystem, deviceType, deviceSlot));\r\n            }\r\n        }\r\n        this._registeredManagers.push(manager);\r\n    };\r\n\r\n    public readonly unregisterManager = (manager: IObservableManager): void => {\r\n        const idx = this._registeredManagers.indexOf(manager);\r\n\r\n        if (idx > -1) {\r\n            this._registeredManagers.splice(idx, 1);\r\n        }\r\n    };\r\n\r\n    public dispose(): void {\r\n        this._deviceInputSystem.dispose();\r\n    }\r\n}\r\n"]}