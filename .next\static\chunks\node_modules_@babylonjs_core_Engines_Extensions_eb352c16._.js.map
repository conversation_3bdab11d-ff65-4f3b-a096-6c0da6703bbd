{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.alpha.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.alpha.ts"], "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { Constants } from \"../constants\";\r\n\r\ndeclare module \"../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Sets the current alpha mode\r\n         * @param mode defines the mode to use (one of the Engine.ALPHA_XXX)\r\n         * @param noDepthWriteChange defines if depth writing state should remains unchanged (false by default)\r\n         * @param targetIndex defines the index of the target to set the alpha mode for (default is 0)\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/transparent_rendering\r\n         */\r\n        setAlphaMode(mode: number, noDepthWriteChange?: boolean, targetIndex?: number): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.setAlphaMode = function (mode: number, noDepthWriteChange: boolean = false, targetIndex: number = 0): void {\r\n    if (this._alphaMode[targetIndex] === mode) {\r\n        if (!noDepthWriteChange) {\r\n            // Make sure we still have the correct depth mask according to the alpha mode (a transparent material could have forced writting to the depth buffer, for instance)\r\n            const depthMask = mode === Constants.ALPHA_DISABLE;\r\n            if (this.depthCullingState.depthMask !== depthMask) {\r\n                this.depthCullingState.depthMask = depthMask;\r\n            }\r\n        }\r\n        return;\r\n    }\r\n\r\n    const alphaBlendDisabled = mode === Constants.ALPHA_DISABLE;\r\n\r\n    this._alphaState.setAlphaBlend(!alphaBlendDisabled, targetIndex);\r\n    this._alphaState.setAlphaMode(mode, targetIndex);\r\n\r\n    if (!noDepthWriteChange) {\r\n        this.depthCullingState.depthMask = alphaBlendDisabled;\r\n    }\r\n    this._alphaMode[targetIndex] = mode;\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;;gKAiBtD,aAAU,CAAC,SAAS,CAAC,YAAY,GAAG,SAAU,IAAY;6BAAE,iEAA8B,KAAK,gBAAE,iEAAsB,CAAC;IACpH,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC;QACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,mKAAmK;YACnK,MAAM,SAAS,GAAG,IAAI,KAAK,SAAS,CAAC,aAAa,CAAC;YACnD,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACjD,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC;YACjD,CAAC;QACL,CAAC;QACD,OAAO;IACX,CAAC;IAED,MAAM,kBAAkB,GAAG,IAAI,KAAK,SAAS,CAAC,aAAa,CAAC;IAE5D,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;IACjE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAEjD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtB,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG,kBAAkB,CAAC;IAC1D,CAAC;IACD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;AACxC,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.rawTexture.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.rawTexture.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Constants } from \"../constants\";\r\nimport { ThinEngine } from \"../thinEngine\";\r\nimport type { IWebRequest } from \"../../Misc/interfaces/iWebRequest\";\r\nimport { IsExponentOfTwo } from \"../../Misc/tools.functions\";\r\n\r\ndeclare module \"../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Update a raw texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store in the texture\r\n         * @param format defines the format of the data\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawTexture(texture: Nullable<InternalTexture>, data: Nullable<ArrayBufferView>, format: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store in the texture\r\n         * @param format defines the format of the data\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the compression used (null by default)\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n         */\r\n        updateRawTexture(\r\n            texture: Nullable<InternalTexture>,\r\n            data: Nullable<ArrayBufferView>,\r\n            format: number,\r\n            invertY: boolean,\r\n            compression: Nullable<string>,\r\n            type: number,\r\n            useSRGBBuffer: boolean\r\n        ): void;\r\n        /**\r\n         * Update a raw cube texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawCubeTexture(texture: InternalTexture, data: ArrayBufferView[], format: number, type: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw cube texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the compression used (null by default)\r\n         */\r\n        updateRawCubeTexture(texture: InternalTexture, data: ArrayBufferView[], format: number, type: number, invertY: boolean, compression: Nullable<string>): void;\r\n\r\n        /**\r\n         * Update a raw cube texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the compression used (null by default)\r\n         * @param level defines which level of the texture to update\r\n         */\r\n        updateRawCubeTexture(texture: InternalTexture, data: ArrayBufferView[], format: number, type: number, invertY: boolean, compression: Nullable<string>, level: number): void;\r\n\r\n        /**\r\n         * Creates a new raw cube texture from a specified url\r\n         * @param url defines the url where the data is located\r\n         * @param scene defines the current scene\r\n         * @param size defines the size of the textures\r\n         * @param format defines the format of the data\r\n         * @param type defines the type fo the data (like Engine.TEXTURETYPE_UNSIGNED_BYTE)\r\n         * @param noMipmap defines if the engine should avoid generating the mip levels\r\n         * @param callback defines a callback used to extract texture data from loaded data\r\n         * @param mipmapGenerator defines to provide an optional tool to generate mip levels\r\n         * @param onLoad defines a callback called when texture is loaded\r\n         * @param onError defines a callback called if there is an error\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createRawCubeTextureFromUrl(\r\n            url: string,\r\n            scene: Nullable<Scene>,\r\n            size: number,\r\n            format: number,\r\n            type: number,\r\n            noMipmap: boolean,\r\n            callback: (ArrayBuffer: ArrayBuffer) => Nullable<ArrayBufferView[]>,\r\n            mipmapGenerator: Nullable<(faces: ArrayBufferView[]) => ArrayBufferView[][]>,\r\n            onLoad: Nullable<() => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Creates a new raw cube texture from a specified url\r\n         * @param url defines the url where the data is located\r\n         * @param scene defines the current scene\r\n         * @param size defines the size of the textures\r\n         * @param format defines the format of the data\r\n         * @param type defines the type fo the data (like Engine.TEXTURETYPE_UNSIGNED_BYTE)\r\n         * @param noMipmap defines if the engine should avoid generating the mip levels\r\n         * @param callback defines a callback used to extract texture data from loaded data\r\n         * @param mipmapGenerator defines to provide an optional tool to generate mip levels\r\n         * @param onLoad defines a callback called when texture is loaded\r\n         * @param onError defines a callback called if there is an error\r\n         * @param samplingMode defines the required sampling mode (like Texture.NEAREST_SAMPLINGMODE)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createRawCubeTextureFromUrl(\r\n            url: string,\r\n            scene: Nullable<Scene>,\r\n            size: number,\r\n            format: number,\r\n            type: number,\r\n            noMipmap: boolean,\r\n            callback: (ArrayBuffer: ArrayBuffer) => Nullable<ArrayBufferView[]>,\r\n            mipmapGenerator: Nullable<(faces: ArrayBufferView[]) => ArrayBufferView[][]>,\r\n            onLoad: Nullable<() => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            samplingMode: number,\r\n            invertY: boolean\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Update a raw 3D texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawTexture3D(texture: InternalTexture, data: Nullable<ArrayBufferView>, format: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw 3D texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the used compression (can be null)\r\n         * @param textureType defines the texture Type (Engine.TEXTURETYPE_UNSIGNED_BYTE, Engine.TEXTURETYPE_FLOAT...)\r\n         */\r\n        updateRawTexture3D(texture: InternalTexture, data: Nullable<ArrayBufferView>, format: number, invertY: boolean, compression: Nullable<string>, textureType: number): void;\r\n\r\n        /**\r\n         * Update a raw 2D array texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawTexture2DArray(texture: InternalTexture, data: Nullable<ArrayBufferView>, format: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw 2D array texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the used compression (can be null)\r\n         * @param textureType defines the texture Type (Engine.TEXTURETYPE_UNSIGNED_BYTE, Engine.TEXTURETYPE_FLOAT...)\r\n         */\r\n        updateRawTexture2DArray(\r\n            texture: InternalTexture,\r\n            data: Nullable<ArrayBufferView>,\r\n            format: number,\r\n            invertY: boolean,\r\n            compression: Nullable<string>,\r\n            textureType: number\r\n        ): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.updateRawTexture = function (\r\n    texture: Nullable<InternalTexture>,\r\n    data: Nullable<ArrayBufferView>,\r\n    format: number,\r\n    invertY: boolean,\r\n    compression: Nullable<string> = null,\r\n    type: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n    useSRGBBuffer: boolean = false\r\n): void {\r\n    if (!texture) {\r\n        return;\r\n    }\r\n    // Babylon's internalSizedFomat but gl's texImage2D internalFormat\r\n    const internalSizedFomat = this._getRGBABufferInternalSizedFormat(type, format, useSRGBBuffer);\r\n\r\n    // Babylon's internalFormat but gl's texImage2D format\r\n    const internalFormat = this._getInternalFormat(format);\r\n    const textureType = this._getWebGLTextureType(type);\r\n    this._bindTextureDirectly(this._gl.TEXTURE_2D, texture, true);\r\n    this._unpackFlipY(invertY === undefined ? true : invertY ? true : false);\r\n\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = data;\r\n        texture.format = format;\r\n        texture.type = type;\r\n        texture.invertY = invertY;\r\n        texture._compression = compression;\r\n    }\r\n\r\n    if (texture.width % 4 !== 0) {\r\n        this._gl.pixelStorei(this._gl.UNPACK_ALIGNMENT, 1);\r\n    }\r\n\r\n    if (compression && data) {\r\n        this._gl.compressedTexImage2D(this._gl.TEXTURE_2D, 0, (<any>this.getCaps().s3tc)[compression], texture.width, texture.height, 0, <DataView>data);\r\n    } else {\r\n        this._gl.texImage2D(this._gl.TEXTURE_2D, 0, internalSizedFomat, texture.width, texture.height, 0, internalFormat, textureType, data);\r\n    }\r\n\r\n    if (texture.generateMipMaps) {\r\n        this._gl.generateMipmap(this._gl.TEXTURE_2D);\r\n    }\r\n    this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n    //  this.resetTextureCache();\r\n    texture.isReady = true;\r\n};\r\n\r\nThinEngine.prototype.createRawTexture = function (\r\n    data: Nullable<ArrayBufferView>,\r\n    width: number,\r\n    height: number,\r\n    format: number,\r\n    generateMipMaps: boolean,\r\n    invertY: boolean,\r\n    samplingMode: number,\r\n    compression: Nullable<string> = null,\r\n    type: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    creationFlags = 0,\r\n    useSRGBBuffer = false\r\n): InternalTexture {\r\n    const texture = new InternalTexture(this, InternalTextureSource.Raw);\r\n    texture.baseWidth = width;\r\n    texture.baseHeight = height;\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.format = format;\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n    texture.invertY = invertY;\r\n    texture._compression = compression;\r\n    texture.type = type;\r\n    texture._useSRGBBuffer = this._getUseSRGBBuffer(useSRGBBuffer, !generateMipMaps);\r\n\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = data;\r\n    }\r\n\r\n    this.updateRawTexture(texture, data, format, invertY, compression, type, texture._useSRGBBuffer);\r\n    this._bindTextureDirectly(this._gl.TEXTURE_2D, texture, true);\r\n\r\n    // Filters\r\n    const filters = this._getSamplingParameters(samplingMode, generateMipMaps);\r\n\r\n    this._gl.texParameteri(this._gl.TEXTURE_2D, this._gl.TEXTURE_MAG_FILTER, filters.mag);\r\n    this._gl.texParameteri(this._gl.TEXTURE_2D, this._gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n    if (generateMipMaps) {\r\n        this._gl.generateMipmap(this._gl.TEXTURE_2D);\r\n    }\r\n\r\n    this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    return texture;\r\n};\r\n\r\nThinEngine.prototype.createRawCubeTexture = function (\r\n    data: Nullable<ArrayBufferView[]>,\r\n    size: number,\r\n    format: number,\r\n    type: number,\r\n    generateMipMaps: boolean,\r\n    invertY: boolean,\r\n    samplingMode: number,\r\n    compression: Nullable<string> = null\r\n): InternalTexture {\r\n    const gl = this._gl;\r\n    const texture = new InternalTexture(this, InternalTextureSource.CubeRaw);\r\n    texture.isCube = true;\r\n    texture.format = format;\r\n    texture.type = type;\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferViewArray = data;\r\n    }\r\n\r\n    const textureType = this._getWebGLTextureType(type);\r\n    let internalFormat = this._getInternalFormat(format);\r\n\r\n    if (internalFormat === gl.RGB) {\r\n        internalFormat = gl.RGBA;\r\n    }\r\n\r\n    // Mipmap generation needs a sized internal format that is both color-renderable and texture-filterable\r\n    if (textureType === gl.FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n        generateMipMaps = false;\r\n        samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        Logger.Warn(\"Float texture filtering is not supported. Mipmap generation and sampling mode are forced to false and TEXTURE_NEAREST_SAMPLINGMODE, respectively.\");\r\n    } else if (textureType === this._gl.HALF_FLOAT_OES && !this._caps.textureHalfFloatLinearFiltering) {\r\n        generateMipMaps = false;\r\n        samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        Logger.Warn(\"Half float texture filtering is not supported. Mipmap generation and sampling mode are forced to false and TEXTURE_NEAREST_SAMPLINGMODE, respectively.\");\r\n    } else if (textureType === gl.FLOAT && !this._caps.textureFloatRender) {\r\n        generateMipMaps = false;\r\n        Logger.Warn(\"Render to float textures is not supported. Mipmap generation forced to false.\");\r\n    } else if (textureType === gl.HALF_FLOAT && !this._caps.colorBufferFloat) {\r\n        generateMipMaps = false;\r\n        Logger.Warn(\"Render to half float textures is not supported. Mipmap generation forced to false.\");\r\n    }\r\n\r\n    const width = size;\r\n    const height = width;\r\n\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.invertY = invertY;\r\n    texture._compression = compression;\r\n\r\n    // Double check on POT to generate Mips.\r\n    const isPot = !this.needPOTTextures || (IsExponentOfTwo(texture.width) && IsExponentOfTwo(texture.height));\r\n    if (!isPot) {\r\n        generateMipMaps = false;\r\n    }\r\n\r\n    // Upload data if needed. The texture won't be ready until then.\r\n    if (data) {\r\n        this.updateRawCubeTexture(texture, data, format, type, invertY, compression);\r\n    } else {\r\n        const internalSizedFomat = this._getRGBABufferInternalSizedFormat(type);\r\n        const level = 0;\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n\r\n        for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\r\n            if (compression) {\r\n                gl.compressedTexImage2D(\r\n                    gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex,\r\n                    level,\r\n                    (<any>this.getCaps().s3tc)[compression],\r\n                    texture.width,\r\n                    texture.height,\r\n                    0,\r\n                    undefined as any\r\n                );\r\n            } else {\r\n                gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex, level, internalSizedFomat, texture.width, texture.height, 0, internalFormat, textureType, null);\r\n            }\r\n        }\r\n\r\n        this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, null);\r\n    }\r\n\r\n    this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, texture, true);\r\n\r\n    // Filters\r\n    if (data && generateMipMaps) {\r\n        this._gl.generateMipmap(this._gl.TEXTURE_CUBE_MAP);\r\n    }\r\n\r\n    const filters = this._getSamplingParameters(samplingMode, generateMipMaps);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n    texture.isReady = true;\r\n\r\n    return texture;\r\n};\r\n\r\nThinEngine.prototype.updateRawCubeTexture = function (\r\n    texture: InternalTexture,\r\n    data: ArrayBufferView[],\r\n    format: number,\r\n    type: number,\r\n    invertY: boolean,\r\n    compression: Nullable<string> = null,\r\n    level: number = 0\r\n): void {\r\n    texture._bufferViewArray = data;\r\n    texture.format = format;\r\n    texture.type = type;\r\n    texture.invertY = invertY;\r\n    texture._compression = compression;\r\n\r\n    const gl = this._gl;\r\n    const textureType = this._getWebGLTextureType(type);\r\n    let internalFormat = this._getInternalFormat(format);\r\n    const internalSizedFomat = this._getRGBABufferInternalSizedFormat(type);\r\n\r\n    let needConversion = false;\r\n    if (internalFormat === gl.RGB) {\r\n        internalFormat = gl.RGBA;\r\n        needConversion = true;\r\n    }\r\n\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n    this._unpackFlipY(invertY === undefined ? true : invertY ? true : false);\r\n\r\n    if (texture.width % 4 !== 0) {\r\n        gl.pixelStorei(gl.UNPACK_ALIGNMENT, 1);\r\n    }\r\n\r\n    // Data are known to be in +X +Y +Z -X -Y -Z\r\n    for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\r\n        let faceData = data[faceIndex];\r\n\r\n        if (compression) {\r\n            gl.compressedTexImage2D(\r\n                gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex,\r\n                level,\r\n                (<any>this.getCaps().s3tc)[compression],\r\n                texture.width,\r\n                texture.height,\r\n                0,\r\n                <DataView>faceData\r\n            );\r\n        } else {\r\n            if (needConversion) {\r\n                faceData = ConvertRGBtoRGBATextureData(faceData, texture.width, texture.height, type);\r\n            }\r\n            gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex, level, internalSizedFomat, texture.width, texture.height, 0, internalFormat, textureType, faceData);\r\n        }\r\n    }\r\n\r\n    const isPot = !this.needPOTTextures || (IsExponentOfTwo(texture.width) && IsExponentOfTwo(texture.height));\r\n    if (isPot && texture.generateMipMaps && level === 0) {\r\n        this._gl.generateMipmap(this._gl.TEXTURE_CUBE_MAP);\r\n    }\r\n    this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, null);\r\n\r\n    // this.resetTextureCache();\r\n    texture.isReady = true;\r\n};\r\n\r\nThinEngine.prototype.createRawCubeTextureFromUrl = function (\r\n    url: string,\r\n    scene: Nullable<Scene>,\r\n    size: number,\r\n    format: number,\r\n    type: number,\r\n    noMipmap: boolean,\r\n    callback: (ArrayBuffer: ArrayBuffer) => Nullable<ArrayBufferView[]>,\r\n    mipmapGenerator: Nullable<(faces: ArrayBufferView[]) => ArrayBufferView[][]>,\r\n    onLoad: Nullable<() => void> = null,\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n    invertY: boolean = false\r\n): InternalTexture {\r\n    const gl = this._gl;\r\n    const texture = this.createRawCubeTexture(null, size, format, type, !noMipmap, invertY, samplingMode, null);\r\n    scene?.addPendingData(texture);\r\n    texture.url = url;\r\n    texture.isReady = false;\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    const onerror = (request?: IWebRequest, exception?: any) => {\r\n        scene?.removePendingData(texture);\r\n        if (onError && request) {\r\n            onError(request.status + \" \" + request.statusText, exception);\r\n        }\r\n    };\r\n\r\n    const internalCallback = (data: any) => {\r\n        // If the texture has been disposed\r\n        if (!texture._hardwareTexture) {\r\n            return;\r\n        }\r\n\r\n        const width = texture.width;\r\n        const faceDataArrays = callback(data);\r\n\r\n        if (!faceDataArrays) {\r\n            return;\r\n        }\r\n\r\n        if (mipmapGenerator) {\r\n            const textureType = this._getWebGLTextureType(type);\r\n            let internalFormat = this._getInternalFormat(format);\r\n            const internalSizedFomat = this._getRGBABufferInternalSizedFormat(type);\r\n\r\n            let needConversion = false;\r\n            if (internalFormat === gl.RGB) {\r\n                internalFormat = gl.RGBA;\r\n                needConversion = true;\r\n            }\r\n\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n            this._unpackFlipY(false);\r\n\r\n            const mipData = mipmapGenerator(faceDataArrays);\r\n            for (let level = 0; level < mipData.length; level++) {\r\n                const mipSize = width >> level;\r\n\r\n                for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\r\n                    let mipFaceData = mipData[level][faceIndex];\r\n                    if (needConversion) {\r\n                        mipFaceData = ConvertRGBtoRGBATextureData(mipFaceData, mipSize, mipSize, type);\r\n                    }\r\n                    gl.texImage2D(faceIndex, level, internalSizedFomat, mipSize, mipSize, 0, internalFormat, textureType, mipFaceData);\r\n                }\r\n            }\r\n\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n        } else {\r\n            this.updateRawCubeTexture(texture, faceDataArrays, format, type, invertY);\r\n        }\r\n\r\n        texture.isReady = true;\r\n        // this.resetTextureCache();\r\n        scene?.removePendingData(texture);\r\n\r\n        texture.onLoadedObservable.notifyObservers(texture);\r\n        texture.onLoadedObservable.clear();\r\n\r\n        if (onLoad) {\r\n            onLoad();\r\n        }\r\n    };\r\n\r\n    this._loadFile(\r\n        url,\r\n        (data) => {\r\n            internalCallback(data);\r\n        },\r\n        undefined,\r\n        scene?.offlineProvider,\r\n        true,\r\n        onerror\r\n    );\r\n\r\n    return texture;\r\n};\r\n\r\n/**\r\n * @internal\r\n */\r\nfunction ConvertRGBtoRGBATextureData(rgbData: any, width: number, height: number, textureType: number): ArrayBufferView {\r\n    // Create new RGBA data container.\r\n    let rgbaData: any;\r\n    let val1 = 1;\r\n    if (textureType === Constants.TEXTURETYPE_FLOAT) {\r\n        rgbaData = new Float32Array(width * height * 4);\r\n    } else if (textureType === Constants.TEXTURETYPE_HALF_FLOAT) {\r\n        rgbaData = new Uint16Array(width * height * 4);\r\n        val1 = 15360; // 15360 is the encoding of 1 in half float\r\n    } else if (textureType === Constants.TEXTURETYPE_UNSIGNED_INTEGER) {\r\n        rgbaData = new Uint32Array(width * height * 4);\r\n    } else {\r\n        rgbaData = new Uint8Array(width * height * 4);\r\n    }\r\n\r\n    // Convert each pixel.\r\n    for (let x = 0; x < width; x++) {\r\n        for (let y = 0; y < height; y++) {\r\n            const index = (y * width + x) * 3;\r\n            const newIndex = (y * width + x) * 4;\r\n\r\n            // Map Old Value to new value.\r\n            rgbaData[newIndex + 0] = rgbData[index + 0];\r\n            rgbaData[newIndex + 1] = rgbData[index + 1];\r\n            rgbaData[newIndex + 2] = rgbData[index + 2];\r\n\r\n            // Add fully opaque alpha channel.\r\n            rgbaData[newIndex + 3] = val1;\r\n        }\r\n    }\r\n\r\n    return rgbaData;\r\n}\r\n\r\n/**\r\n * Create a function for createRawTexture3D/createRawTexture2DArray\r\n * @param is3D true for TEXTURE_3D and false for TEXTURE_2D_ARRAY\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nfunction MakeCreateRawTextureFunction(is3D: boolean) {\r\n    return function (\r\n        this: ThinEngine,\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        depth: number,\r\n        format: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean,\r\n        samplingMode: number,\r\n        compression: Nullable<string> = null,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE\r\n    ): InternalTexture {\r\n        const target = is3D ? this._gl.TEXTURE_3D : this._gl.TEXTURE_2D_ARRAY;\r\n        const source = is3D ? InternalTextureSource.Raw3D : InternalTextureSource.Raw2DArray;\r\n        const texture = new InternalTexture(this, source);\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.baseDepth = depth;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.depth = depth;\r\n        texture.format = format;\r\n        texture.type = textureType;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        if (is3D) {\r\n            texture.is3D = true;\r\n        } else {\r\n            texture.is2DArray = true;\r\n        }\r\n\r\n        if (!this._doNotHandleContextLost) {\r\n            texture._bufferView = data;\r\n        }\r\n\r\n        if (is3D) {\r\n            this.updateRawTexture3D(texture, data, format, invertY, compression, textureType);\r\n        } else {\r\n            this.updateRawTexture2DArray(texture, data, format, invertY, compression, textureType);\r\n        }\r\n        this._bindTextureDirectly(target, texture, true);\r\n\r\n        // Filters\r\n        const filters = this._getSamplingParameters(samplingMode, generateMipMaps);\r\n\r\n        this._gl.texParameteri(target, this._gl.TEXTURE_MAG_FILTER, filters.mag);\r\n        this._gl.texParameteri(target, this._gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n        if (generateMipMaps) {\r\n            this._gl.generateMipmap(target);\r\n        }\r\n\r\n        this._bindTextureDirectly(target, null);\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        return texture;\r\n    };\r\n}\r\n\r\nThinEngine.prototype.createRawTexture2DArray = MakeCreateRawTextureFunction(false);\r\nThinEngine.prototype.createRawTexture3D = MakeCreateRawTextureFunction(true);\r\n\r\n/**\r\n * Create a function for updateRawTexture3D/updateRawTexture2DArray\r\n * @param is3D true for TEXTURE_3D and false for TEXTURE_2D_ARRAY\r\n * @internal\r\n */\r\nfunction MakeUpdateRawTextureFunction(is3D: boolean) {\r\n    return function (\r\n        this: ThinEngine,\r\n        texture: InternalTexture,\r\n        data: Nullable<ArrayBufferView>,\r\n        format: number,\r\n        invertY: boolean,\r\n        compression: Nullable<string> = null,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE\r\n    ): void {\r\n        const target = is3D ? this._gl.TEXTURE_3D : this._gl.TEXTURE_2D_ARRAY;\r\n        const internalType = this._getWebGLTextureType(textureType);\r\n        const internalFormat = this._getInternalFormat(format);\r\n        const internalSizedFomat = this._getRGBABufferInternalSizedFormat(textureType, format);\r\n\r\n        this._bindTextureDirectly(target, texture, true);\r\n        this._unpackFlipY(invertY === undefined ? true : invertY ? true : false);\r\n\r\n        if (!this._doNotHandleContextLost) {\r\n            texture._bufferView = data;\r\n            texture.format = format;\r\n            texture.invertY = invertY;\r\n            texture._compression = compression;\r\n        }\r\n\r\n        if (texture.width % 4 !== 0) {\r\n            this._gl.pixelStorei(this._gl.UNPACK_ALIGNMENT, 1);\r\n        }\r\n\r\n        if (compression && data) {\r\n            this._gl.compressedTexImage3D(target, 0, (<any>this.getCaps().s3tc)[compression], texture.width, texture.height, texture.depth, 0, data);\r\n        } else {\r\n            this._gl.texImage3D(target, 0, internalSizedFomat, texture.width, texture.height, texture.depth, 0, internalFormat, internalType, data);\r\n        }\r\n\r\n        if (texture.generateMipMaps) {\r\n            this._gl.generateMipmap(target);\r\n        }\r\n        this._bindTextureDirectly(target, null);\r\n        // this.resetTextureCache();\r\n        texture.isReady = true;\r\n    };\r\n}\r\n\r\nThinEngine.prototype.updateRawTexture2DArray = MakeUpdateRawTextureFunction(false);\r\nThinEngine.prototype.updateRawTexture3D = MakeUpdateRawTextureFunction(true);\r\n"], "names": [], "mappings": ";AACA,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;;;;;AA6K7D,6KAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,SACpC,OAAkC,EAClC,IAA+B,EAC/B,MAAc,EACd,OAAgB;sBAChB,iEAAgC,IAAI,SACpC,iEAAe,GAAA,gBAAA,MAAS,CAAC,0DAAA,gBAAyB,EAClD,gBAAyB,KAAK;IAE9B,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,OAAO;IACX,CAAC;IACD,kEAAkE;IAClE,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IAE/F,sDAAsD;IACtD,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACvD,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC9D,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEzE,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;QAC3B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACvC,CAAC;IAED,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAQ,IAAI,CAAC,OAAO,EAAE,CAAC,IAAK,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAY,IAAI,CAAC,CAAC;IACrJ,CAAC,MAAM,CAAC;QACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAE,kBAAkB,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IACzI,CAAC;IAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACrD,6BAA6B;IAC7B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,SACpC,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB;sBACpB,iEAAgC,IAAI,SACpC,iEAAe,GACf,MADwB,CAAC,yBAAyB,6BACW;oBAC7D,aAAa,oDAAG,CAAC,kBACjB,aAAa,uDAAG,KAAK;IAErB,MAAM,OAAO,GAAG,IAAI,qMAAe,CAAC,IAAI,EAAA,EAAA,6BAAA,GAA4B,CAAC;IACrE,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;IAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACpC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACnC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC,eAAe,CAAC,CAAC;IAEjF,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IACjG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAE9D,UAAU;IACV,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;IAE3E,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACtF,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAEtF,IAAI,eAAe,EAAE,CAAC;QAClB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAErD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,SACxC,IAAiC,EACjC,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,eAAwB,EACxB,OAAgB,EAChB,YAAoB;sBACpB,iEAAgC,IAAI;IAEpC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,OAAO,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAA,EAAA,iCAAA,GAAgC,CAAC;IACzE,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAErD,IAAI,cAAc,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;QAC5B,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED,uGAAuG;IACvG,IAAI,WAAW,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC;QACtE,eAAe,GAAG,KAAK,CAAC;QACxB,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;iKACtD,SAAM,CAAC,IAAI,CAAC,mJAAmJ,CAAC,CAAC;IACrK,CAAC,MAAM,IAAI,WAAW,KAAK,IAAI,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC;QAChG,eAAe,GAAG,KAAK,CAAC;QACxB,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QACtD,kKAAM,CAAC,IAAI,CAAC,wJAAwJ,CAAC,CAAC;IAC1K,CAAC,MAAM,IAAI,WAAW,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;QACpE,eAAe,GAAG,KAAK,CAAC;iKACxB,SAAM,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;IACjG,CAAC,MAAM,IAAI,WAAW,KAAK,EAAE,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;QACvE,eAAe,GAAG,KAAK,CAAC;iKACxB,SAAM,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;IACtG,CAAC;IAED,MAAM,KAAK,GAAG,IAAI,CAAC;IACnB,MAAM,MAAM,GAAG,KAAK,CAAC;IAErB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IAEnC,wCAAwC;IACxC,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,yKAAC,kBAAA,AAAe,EAAC,OAAO,CAAC,KAAK,CAAC,IAAI,2LAAA,AAAe,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3G,IAAI,CAAC,KAAK,EAAE,CAAC;QACT,eAAe,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED,gEAAgE;IAChE,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IACjF,CAAC,MAAM,CAAC;QACJ,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,CAAC,CAAC;QAEhB,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,CAAE,CAAC;YACjD,IAAI,WAAW,EAAE,CAAC;gBACd,EAAE,CAAC,oBAAoB,CACnB,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAC1C,KAAK,EACC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAK,CAAC,WAAW,CAAC,EACvC,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,EACd,CAAC,EACD,SAAgB,CACnB,CAAC;YACN,CAAC,MAAM,CAAC;gBACJ,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;YAC9J,CAAC;QACL,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC/D,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAEpE,UAAU;IACV,IAAI,IAAI,IAAI,eAAe,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;IAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAE1E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAErD,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACpC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IAEvB,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,SACxC,OAAwB,EACxB,IAAuB,EACvB,MAAc,EACd,IAAY,EACZ,OAAgB;sBAChB,iEAAgC,IAAI,UACpC,iEAAgB,CAAC;IAEjB,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAChC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IAEnC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACrD,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,CAAC;IAExE,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,cAAc,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;QAC5B,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;QACzB,cAAc,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC9D,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEzE,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,4CAA4C;IAC5C,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,CAAE,CAAC;QACjD,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAE/B,IAAI,WAAW,EAAE,CAAC;YACd,EAAE,CAAC,oBAAoB,CACnB,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAC1C,KAAK,EACC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAK,CAAC,WAAW,CAAC,EACvC,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,EACd,CAAC,EACS,QAAQ,CACrB,CAAC;QACN,CAAC,MAAM,CAAC;YACJ,IAAI,cAAc,EAAE,CAAC;gBACjB,QAAQ,GAAG,2BAA2B,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC1F,CAAC;YACD,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAClK,CAAC;IACL,CAAC;IAED,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,yKAAC,kBAAA,AAAe,EAAC,OAAO,CAAC,KAAK,CAAC,6KAAI,kBAAA,AAAe,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3G,IAAI,KAAK,IAAI,OAAO,CAAC,eAAe,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;QAClD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACvD,CAAC;IACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAE3D,4BAA4B;IAC5B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,2BAA2B,GAAG,SAC/C,GAAW,EACX,KAAsB,EACtB,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,QAAiB,EACjB,QAAmE,EACnE,eAA4E;QAC5E,0EAA+B,IAAI,YACnC,iEAAiE,IAAI,iBACrE,oEAAuB,GAAA,UAAA,MAAS,CAAC,6DAAA,2BAA8B,EAC/D,UAAmB,KAAK;IAExB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;kDAC5G,KAAK,CAAE,cAAc,CAAC,OAAO,CAAC,CAAC;IAC/B,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;IAClB,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;IACxB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,MAAM,OAAO,GAAG,CAAC,OAAqB,EAAE,SAAe,EAAE,EAAE;sDACvD,KAAK,CAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;YACrB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAClE,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,IAAS,EAAE,EAAE;QACnC,mCAAmC;QACnC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YAClB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,CAAC;YAExE,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,IAAI,cAAc,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;gBAC5B,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;gBACzB,cAAc,GAAG,IAAI,CAAC;YAC1B,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAC9D,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAEzB,MAAM,OAAO,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC;YAChD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAClD,MAAM,OAAO,GAAG,KAAK,IAAI,KAAK,CAAC;gBAE/B,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,CAAE,CAAC;oBACjD,IAAI,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC;oBAC5C,IAAI,cAAc,EAAE,CAAC;wBACjB,WAAW,GAAG,2BAA2B,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;oBACnF,CAAC;oBACD,EAAE,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;gBACvH,CAAC;YACL,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,4BAA4B;sDAC5B,KAAK,CAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAElC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEnC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;QACb,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,CAAC,SAAS,CACV,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;QACL,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC,EACD,SAAS,gDACT,KAAK,CAAE,eAAe,EACtB,IAAI,EACJ,OAAO,CACV,CAAC;IAEF,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF;;GAEG,CACH,SAAS,2BAA2B,CAAC,OAAY,EAAE,KAAa,EAAE,MAAc,EAAE,WAAmB;IACjG,kCAAkC;IAClC,IAAI,QAAa,CAAC;IAClB,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,WAAW,KAAK,GAAA,MAAS,CAAC,iBAAiB,EAAE,CAAC;QAC9C,QAAQ,GAAG,IAAI,YAAY,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IACpD,CAAC,MAAM,IAAI,WAAW,KAAK,GAAA,MAAS,CAAC,sBAAsB,EAAE,CAAC;QAC1D,QAAQ,GAAG,IAAI,WAAW,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/C,IAAI,GAAG,KAAK,CAAC,CAAC,2CAA2C;IAC7D,CAAC,MAAM,IAAI,WAAW,KAAK,GAAA,MAAS,CAAC,4BAA4B,EAAE,CAAC;QAChE,QAAQ,GAAG,IAAI,WAAW,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IACnD,CAAC,MAAM,CAAC;QACJ,QAAQ,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,sBAAsB;IACtB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE,CAAC;QAC7B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC9B,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAErC,8BAA8B;YAC9B,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAC5C,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAC5C,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAE5C,kCAAkC;YAClC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAClC,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;;;GAIG,CACH,gEAAgE;AAChE,SAAS,4BAA4B,CAAC,IAAa;IAC/C,OAAO,SAEH,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,KAAa,EACb,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB;0BACpB,iEAAgC,IAAI,gBACpC,iEAAsB,SAAS,CAAC,yBAAyB;QAEzD,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACtE,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAA,GAAA,+BAAA,GAA6B,CAAC,EAAA,GAAA,oCAAA,EAAiC,CAAC;QACrF,MAAM,OAAO,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClD,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;QAC3B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,IAAI,IAAI,EAAE,CAAC;YACP,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACxB,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACtF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QAC3F,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAEjD,UAAU;QACV,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;QAE3E,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzE,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAEzE,IAAI,eAAe,EAAE,CAAC;YAClB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,OAAO,OAAO,CAAC;IACnB,CAAC,CAAC;AACN,CAAC;gKAED,aAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,4BAA4B,CAAC,KAAK,CAAC,CAAC;gKACnF,aAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAC;AAE7E;;;;GAIG,CACH,SAAS,4BAA4B,CAAC,IAAa;IAC/C,OAAO,SAEH,OAAwB,EACxB,IAA+B,EAC/B,MAAc,EACd,OAAgB;0BAChB,iEAAgC,IAAI,gBACpC,iEAAsB,SAAS,CAAC,yBAAyB;QAEzD,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACtE,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC5D,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAEvF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAEzE,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;YAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;QACvC,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAQ,IAAI,CAAC,OAAO,EAAE,CAAC,IAAK,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAC7I,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,kBAAkB,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,cAAc,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAC5I,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxC,4BAA4B;QAC5B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IAC3B,CAAC,CAAC;AACN,CAAC;gKAED,aAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,4BAA4B,CAAC,KAAK,CAAC,CAAC;gKACnF,aAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.readTexture.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.readTexture.ts"], "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /** @internal */\r\n        _readTexturePixels(\r\n            texture: InternalTexture,\r\n            width: number,\r\n            height: number,\r\n            faceIndex?: number,\r\n            level?: number,\r\n            buffer?: Nullable<ArrayBufferView>,\r\n            flushRenderer?: boolean,\r\n            noDataConversion?: boolean,\r\n            x?: number,\r\n            y?: number\r\n        ): Promise<ArrayBufferView>;\r\n\r\n        /** @internal */\r\n        _readTexturePixelsSync(\r\n            texture: InternalTexture,\r\n            width: number,\r\n            height: number,\r\n            faceIndex?: number,\r\n            level?: number,\r\n            buffer?: Nullable<ArrayBufferView>,\r\n            flushRenderer?: boolean,\r\n            noDataConversion?: boolean,\r\n            x?: number,\r\n            y?: number\r\n        ): ArrayBufferView;\r\n    }\r\n}\r\n\r\n// back-compat\r\nimport { allocateAndCopyTypedBuffer } from \"../../Engines/abstractEngine.functions\";\r\nexport { allocateAndCopyTypedBuffer };\r\n\r\nThinEngine.prototype._readTexturePixelsSync = function (\r\n    texture: InternalTexture,\r\n    width: number,\r\n    height: number,\r\n    faceIndex = -1,\r\n    level = 0,\r\n    buffer: Nullable<ArrayBufferView> = null,\r\n    flushRenderer = true,\r\n    noDataConversion = false,\r\n    x = 0,\r\n    y = 0\r\n): ArrayBufferView {\r\n    const gl = this._gl;\r\n    if (!gl) {\r\n        throw new Error(\"Engine does not have gl rendering context.\");\r\n    }\r\n    if (!this._dummyFramebuffer) {\r\n        const dummy = gl.createFramebuffer();\r\n\r\n        if (!dummy) {\r\n            throw new Error(\"Unable to create dummy framebuffer\");\r\n        }\r\n\r\n        this._dummyFramebuffer = dummy;\r\n    }\r\n    gl.bindFramebuffer(gl.FRAMEBUFFER, this._dummyFramebuffer);\r\n\r\n    if (faceIndex > -1) {\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex, texture._hardwareTexture?.underlyingResource, level);\r\n    } else {\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture._hardwareTexture?.underlyingResource, level);\r\n    }\r\n\r\n    let readType = texture.type !== undefined ? this._getWebGLTextureType(texture.type) : gl.UNSIGNED_BYTE;\r\n\r\n    if (!noDataConversion) {\r\n        switch (readType) {\r\n            case gl.UNSIGNED_BYTE:\r\n                if (!buffer) {\r\n                    buffer = new Uint8Array(4 * width * height);\r\n                }\r\n                readType = gl.UNSIGNED_BYTE;\r\n                break;\r\n            default:\r\n                if (!buffer) {\r\n                    buffer = new Float32Array(4 * width * height);\r\n                }\r\n                readType = gl.FLOAT;\r\n                break;\r\n        }\r\n    } else if (!buffer) {\r\n        buffer = allocateAndCopyTypedBuffer(texture.type, 4 * width * height);\r\n    }\r\n\r\n    if (flushRenderer) {\r\n        this.flushFramebuffer();\r\n    }\r\n\r\n    gl.readPixels(x, y, width, height, gl.RGBA, readType, <DataView>buffer);\r\n    gl.bindFramebuffer(gl.FRAMEBUFFER, this._currentFramebuffer);\r\n\r\n    return buffer;\r\n};\r\n\r\n// eslint-disable-next-line @typescript-eslint/promise-function-async\r\nThinEngine.prototype._readTexturePixels = function (\r\n    texture: InternalTexture,\r\n    width: number,\r\n    height: number,\r\n    faceIndex = -1,\r\n    level = 0,\r\n    buffer: Nullable<ArrayBufferView> = null,\r\n    flushRenderer = true,\r\n    noDataConversion = false,\r\n    x = 0,\r\n    y = 0\r\n): Promise<ArrayBufferView> {\r\n    return Promise.resolve(this._readTexturePixelsSync(texture, width, height, faceIndex, level, buffer, flushRenderer, noDataConversion, x, y));\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAqCtD,cAAc;AACd,OAAO,EAAE,0BAA0B,EAAE,MAAM,wCAAwC,CAAC;;;;gKAGpF,aAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,SAC1C,OAAwB,EACxB,KAAa,EACb,MAAc;oBACd,SAAS,wDAAG,CAAC,CAAC,UACd,KAAK,4DAAG,CAAC,EACT,0EAAoC,IAAI,EACxC,aAAa,oEAAG,IAAI,qBACpB,gBAAgB,iDAAG,KAAK,MACxB,CAAC,gEAAG,CAAC,MACL,CAAC,gEAAG,CAAC;IAEL,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,IAAI,CAAC,EAAE,EAAE,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAClE,CAAC;IACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAErC,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IACD,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAE3D,IAAI,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;;QACjB,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,2BAA2B,GAAG,SAAS,+BAAE,OAAO,CAAC,gBAAgB,wFAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;IACnK,CAAC,MAAM,CAAC;YACyE,OAAO;QAApF,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,wCAAU,gBAAgB,0FAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;IACtI,CAAC;IAED,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC;IAEvG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACpB,OAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,EAAE,CAAC,aAAa;gBACjB,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,MAAM,GAAG,IAAI,UAAU,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;gBAChD,CAAC;gBACD,QAAQ,GAAG,EAAE,CAAC,aAAa,CAAC;gBAC5B,MAAM;YACV;gBACI,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,MAAM,GAAG,IAAI,YAAY,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;gBAClD,CAAC;gBACD,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC;gBACpB,MAAM;QACd,CAAC;IACL,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QACjB,MAAM,wLAAG,6BAAA,AAA0B,EAAC,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,aAAa,EAAE,CAAC;QAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAY,MAAM,CAAC,CAAC;IACxE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAE7D,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,qEAAqE;gKACrE,aAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,SACtC,OAAwB,EACxB,KAAa,EACb,MAAc;oBACd,SAAS,wDAAG,CAAC,CAAC,UACd,KAAK,4DAAG,CAAC,WACT,iEAAoC,IAAI,kBACxC,aAAa,oDAAG,IAAI,EACpB,gBAAgB,oEAAG,KAAK,MACxB,CAAC,gEAAG,CAAC,MACL,CAAC,gEAAG,CAAC;IAEL,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjJ,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.dynamicBuffer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.dynamicBuffer.ts"], "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\nimport type { IndicesArray, DataArray } from \"../../types\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Update a dynamic index buffer\r\n         * @param indexBuffer defines the target index buffer\r\n         * @param indices defines the data to update\r\n         * @param offset defines the offset in the target index buffer where update should start\r\n         */\r\n        updateDynamicIndexBuffer(indexBuffer: DataBuffer, indices: IndicesArray, offset?: number): void;\r\n\r\n        /**\r\n         * Updates a dynamic vertex buffer.\r\n         * @param vertexBuffer the vertex buffer to update\r\n         * @param data the data used to update the vertex buffer\r\n         * @param byteOffset the byte offset of the data\r\n         * @param byteLength the byte length of the data\r\n         */\r\n        updateDynamicVertexBuffer(vertexBuffer: DataBuffer, data: DataArray, byteOffset?: number, byteLength?: number): void;\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nThinEngine.prototype.updateDynamicIndexBuffer = function (this: ThinEngine, indexBuffer: DataBuffer, indices: IndicesArray, offset: number = 0): void {\r\n    // Force cache update\r\n    this._currentBoundBuffer[this._gl.ELEMENT_ARRAY_BUFFER] = null;\r\n    this.bindIndexBuffer(indexBuffer);\r\n\r\n    let view: ArrayBufferView;\r\n    if (indexBuffer.is32Bits) {\r\n        // anything else than Uint32Array needs to be converted to Uint32Array\r\n        view = indices instanceof Uint32Array ? indices : new Uint32Array(indices);\r\n    } else {\r\n        // anything else than Uint16Array needs to be converted to Uint16Array\r\n        view = indices instanceof Uint16Array ? indices : new Uint16Array(indices);\r\n    }\r\n\r\n    this._gl.bufferData(this._gl.ELEMENT_ARRAY_BUFFER, view, this._gl.DYNAMIC_DRAW);\r\n\r\n    this._resetIndexBufferBinding();\r\n};\r\n\r\nThinEngine.prototype.updateDynamicVertexBuffer = function (this: ThinEngine, vertexBuffer: DataBuffer, data: DataArray, byteOffset?: number, byteLength?: number): void {\r\n    this.bindArrayBuffer(vertexBuffer);\r\n\r\n    if (byteOffset === undefined) {\r\n        byteOffset = 0;\r\n    }\r\n\r\n    const dataLength = (data as ArrayBuffer).byteLength || (data as number[]).length;\r\n\r\n    if (byteLength === undefined || (byteLength >= dataLength && byteOffset === 0)) {\r\n        if (data instanceof Array) {\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, byteOffset, new Float32Array(data));\r\n        } else {\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, byteOffset, data);\r\n        }\r\n    } else {\r\n        if (data instanceof Array) {\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, byteOffset, new Float32Array(data).subarray(0, byteLength / 4));\r\n        } else {\r\n            if (data instanceof ArrayBuffer) {\r\n                data = new Uint8Array(data, 0, byteLength);\r\n            } else {\r\n                data = new Uint8Array(data.buffer, data.byteOffset, byteLength);\r\n            }\r\n\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, byteOffset, data);\r\n        }\r\n    }\r\n\r\n    this._resetVertexBufferBinding();\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;;AA0BtD,6DAA6D;gKAC7D,aAAU,CAAC,SAAS,CAAC,wBAAwB,GAAG,SAA4B,WAAuB,EAAE,OAAqB;iBAAE,iEAAiB,CAAC;IAC1I,qBAAqB;IACrB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC;IAC/D,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;IAElC,IAAI,IAAqB,CAAC;IAC1B,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;QACvB,sEAAsE;QACtE,IAAI,GAAG,OAAO,YAAY,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/E,CAAC,MAAM,CAAC;QACJ,sEAAsE;QACtE,IAAI,GAAG,OAAO,YAAY,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/E,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAEhF,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACpC,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,SAA4B,YAAwB,EAAE,IAAe,EAAE,UAAmB,EAAE,UAAmB;IAC5J,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;IAEnC,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;QAC3B,UAAU,GAAG,CAAC,CAAC;IACnB,CAAC;IAED,MAAM,UAAU,GAAI,IAAoB,CAAC,UAAU,IAAK,IAAiB,CAAC,MAAM,CAAC;IAEjF,IAAI,UAAU,KAAK,SAAS,IAAI,AAAC,UAAU,IAAI,UAAU,IAAI,UAAU,KAAK,CAAC,CAAC,CAAE,CAAC;QAC7E,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QACtF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACpE,CAAC;IACL,CAAC,MAAM,CAAC;QACJ,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;QAClH,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;gBAC9B,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;YAC/C,CAAC,MAAM,CAAC;gBACJ,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YACpE,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACrC,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.cubeTexture.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.cubeTexture.ts"], "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Constants } from \"../constants\";\r\nimport type { DepthTextureCreationOptions } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport { GetExponentOfTwo } from \"../../Misc/tools.functions\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * @internal\r\n         */\r\n        _setCubeMapTextureParams(texture: InternalTexture, loadMipmap: boolean, maxLevel?: number): void;\r\n        /**\r\n         * Creates a depth stencil cube texture.\r\n         * This is only available in WebGL 2.\r\n         * @param size The size of face edge in the cube texture.\r\n         * @param options The options defining the cube texture.\r\n         * @returns The cube texture\r\n         */\r\n        _createDepthStencilCubeTexture(size: number, options: DepthTextureCreationOptions): InternalTexture;\r\n\r\n        /**\r\n         * Creates a cube texture\r\n         * @param rootUrl defines the url where the files to load is located\r\n         * @param scene defines the current scene\r\n         * @param files defines the list of files to load (1 per face)\r\n         * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @param createPolynomials if a polynomial sphere should be created for the cube texture\r\n         * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\r\n         * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\r\n         * @param fallback defines texture to use while falling back when (compressed) texture file not found.\r\n         * @param loaderOptions options to be passed to the loader\r\n         * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n         * @param buffer defines the data buffer to load instead of loading the rootUrl\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean | undefined,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number,\r\n            fallback: Nullable<InternalTexture>,\r\n            loaderOptions: any,\r\n            useSRGBBuffer: boolean,\r\n            buffer: Nullable<ArrayBufferView>\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Creates a cube texture\r\n         * @param rootUrl defines the url where the files to load is located\r\n         * @param scene defines the current scene\r\n         * @param files defines the list of files to load (1 per face)\r\n         * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Creates a cube texture\r\n         * @param rootUrl defines the url where the files to load is located\r\n         * @param scene defines the current scene\r\n         * @param files defines the list of files to load (1 per face)\r\n         * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @param createPolynomials if a polynomial sphere should be created for the cube texture\r\n         * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\r\n         * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number\r\n        ): InternalTexture;\r\n\r\n        /** @internal */\r\n        createCubeTextureBase(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number,\r\n            fallback: Nullable<InternalTexture>,\r\n            beforeLoadCubeDataCallback: Nullable<(texture: InternalTexture, data: ArrayBufferView | ArrayBufferView[]) => void>,\r\n            imageHandler: Nullable<(texture: InternalTexture, imgs: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n            useSRGBBuffer: boolean,\r\n            buffer: ArrayBufferView\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Force the mipmap generation for the given render target texture\r\n         * @param texture defines the render target texture to use\r\n         * @param unbind defines whether or not to unbind the texture after generation. Defaults to true.\r\n         */\r\n        generateMipMapsForCubemap(texture: InternalTexture, unbind?: boolean): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype._createDepthStencilCubeTexture = function (size: number, options: DepthTextureCreationOptions): InternalTexture {\r\n    const internalTexture = new InternalTexture(this, InternalTextureSource.DepthStencil);\r\n    internalTexture.isCube = true;\r\n\r\n    if (this.webGLVersion === 1) {\r\n        Logger.Error(\"Depth cube texture is not supported by WebGL 1.\");\r\n        return internalTexture;\r\n    }\r\n\r\n    const internalOptions = {\r\n        bilinearFiltering: false,\r\n        comparisonFunction: 0,\r\n        generateStencil: false,\r\n        ...options,\r\n    };\r\n\r\n    const gl = this._gl;\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, internalTexture, true);\r\n\r\n    this._setupDepthStencilTexture(internalTexture, size, internalOptions.bilinearFiltering, internalOptions.comparisonFunction);\r\n\r\n    // Create the depth/stencil buffer\r\n    for (let face = 0; face < 6; face++) {\r\n        if (internalOptions.generateStencil) {\r\n            gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + face, 0, gl.DEPTH24_STENCIL8, size, size, 0, gl.DEPTH_STENCIL, gl.UNSIGNED_INT_24_8, null);\r\n        } else {\r\n            gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + face, 0, gl.DEPTH_COMPONENT24, size, size, 0, gl.DEPTH_COMPONENT, gl.UNSIGNED_INT, null);\r\n        }\r\n    }\r\n\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n\r\n    this._internalTexturesCache.push(internalTexture);\r\n\r\n    return internalTexture;\r\n};\r\n\r\nThinEngine.prototype._setCubeMapTextureParams = function (texture: InternalTexture, loadMipmap: boolean, maxLevel?: number): void {\r\n    const gl = this._gl;\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAG_FILTER, gl.LINEAR);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MIN_FILTER, loadMipmap ? gl.LINEAR_MIPMAP_LINEAR : gl.LINEAR);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n    texture.samplingMode = loadMipmap ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : Constants.TEXTURE_LINEAR_LINEAR;\r\n\r\n    if (loadMipmap && this.getCaps().textureMaxLevel && maxLevel !== undefined && maxLevel > 0) {\r\n        gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAX_LEVEL, maxLevel);\r\n        texture._maxLodLevel = maxLevel;\r\n    }\r\n\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n};\r\n\r\nThinEngine.prototype.createCubeTexture = function (\r\n    rootUrl: string,\r\n    scene: Nullable<Scene>,\r\n    files: Nullable<string[]>,\r\n    noMipmap?: boolean,\r\n    onLoad: Nullable<(data?: any) => void> = null,\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    format?: number,\r\n    forcedExtension: any = null,\r\n    createPolynomials: boolean = false,\r\n    lodScale: number = 0,\r\n    lodOffset: number = 0,\r\n    fallback: Nullable<InternalTexture> = null,\r\n    loaderOptions?: any,\r\n    useSRGBBuffer = false,\r\n    buffer: Nullable<ArrayBufferView> = null\r\n): InternalTexture {\r\n    const gl = this._gl;\r\n\r\n    return this.createCubeTextureBase(\r\n        rootUrl,\r\n        scene,\r\n        files,\r\n        !!noMipmap,\r\n        onLoad,\r\n        onError,\r\n        format,\r\n        forcedExtension,\r\n        createPolynomials,\r\n        lodScale,\r\n        lodOffset,\r\n        fallback,\r\n        (texture: InternalTexture) => this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true),\r\n        (texture: InternalTexture, imgs: HTMLImageElement[] | ImageBitmap[]) => {\r\n            const width = this.needPOTTextures ? GetExponentOfTwo(imgs[0].width, this._caps.maxCubemapTextureSize) : imgs[0].width;\r\n            const height = width;\r\n\r\n            const faces = [\r\n                gl.TEXTURE_CUBE_MAP_POSITIVE_X,\r\n                gl.TEXTURE_CUBE_MAP_POSITIVE_Y,\r\n                gl.TEXTURE_CUBE_MAP_POSITIVE_Z,\r\n                gl.TEXTURE_CUBE_MAP_NEGATIVE_X,\r\n                gl.TEXTURE_CUBE_MAP_NEGATIVE_Y,\r\n                gl.TEXTURE_CUBE_MAP_NEGATIVE_Z,\r\n            ];\r\n\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n            this._unpackFlipY(false);\r\n\r\n            const internalFormat = format ? this._getInternalFormat(format, texture._useSRGBBuffer) : texture._useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : gl.RGBA;\r\n            let texelFormat = format ? this._getInternalFormat(format) : gl.RGBA;\r\n\r\n            if (texture._useSRGBBuffer && this.webGLVersion === 1) {\r\n                texelFormat = internalFormat;\r\n            }\r\n\r\n            for (let index = 0; index < faces.length; index++) {\r\n                if (imgs[index].width !== width || imgs[index].height !== height) {\r\n                    this._prepareWorkingCanvas();\r\n\r\n                    if (!this._workingCanvas || !this._workingContext) {\r\n                        Logger.Warn(\"Cannot create canvas to resize texture.\");\r\n                        return;\r\n                    }\r\n                    this._workingCanvas.width = width;\r\n                    this._workingCanvas.height = height;\r\n\r\n                    this._workingContext.drawImage(imgs[index], 0, 0, imgs[index].width, imgs[index].height, 0, 0, width, height);\r\n                    gl.texImage2D(faces[index], 0, internalFormat, texelFormat, gl.UNSIGNED_BYTE, this._workingCanvas as TexImageSource);\r\n                } else {\r\n                    gl.texImage2D(faces[index], 0, internalFormat, texelFormat, gl.UNSIGNED_BYTE, imgs[index]);\r\n                }\r\n            }\r\n\r\n            if (!noMipmap) {\r\n                gl.generateMipmap(gl.TEXTURE_CUBE_MAP);\r\n            }\r\n\r\n            this._setCubeMapTextureParams(texture, !noMipmap);\r\n\r\n            texture.width = width;\r\n            texture.height = height;\r\n            texture.isReady = true;\r\n            if (format) {\r\n                texture.format = format;\r\n            }\r\n\r\n            texture.onLoadedObservable.notifyObservers(texture);\r\n            texture.onLoadedObservable.clear();\r\n\r\n            if (onLoad) {\r\n                onLoad();\r\n            }\r\n        },\r\n        !!useSRGBBuffer,\r\n        buffer\r\n    );\r\n};\r\n\r\nThinEngine.prototype.generateMipMapsForCubemap = function (texture: InternalTexture, unbind = true) {\r\n    if (texture.generateMipMaps) {\r\n        const gl = this._gl;\r\n        this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n        gl.generateMipmap(gl.TEXTURE_CUBE_MAP);\r\n        if (unbind) {\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAK3C,OAAO,EAAE,gBAAgB,EAAE,MAAM,4BAA4B,CAAC;;;;;AAwI9D,6KAAU,CAAC,SAAS,CAAC,8BAA8B,GAAG,SAAU,IAAY,EAAE,OAAoC;IAC9G,MAAM,eAAe,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAA,GAAA,sCAAA,GAAqC,CAAC;IACtF,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;IAE9B,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;iKAC1B,SAAM,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QAChE,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED,MAAM,eAAe,GAAG;QACpB,iBAAiB,EAAE,KAAK;QACxB,kBAAkB,EAAE,CAAC;QACrB,eAAe,EAAE,KAAK;QACtB,GAAG,OAAO;KACb,CAAC;IAEF,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAEtE,IAAI,CAAC,yBAAyB,CAAC,eAAe,EAAE,IAAI,EAAE,eAAe,CAAC,iBAAiB,EAAE,eAAe,CAAC,kBAAkB,CAAC,CAAC;IAE7H,kCAAkC;IAClC,IAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,CAAE,CAAC;QAClC,IAAI,eAAe,CAAC,eAAe,EAAE,CAAC;YAClC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAC9I,CAAC,MAAM,CAAC;YACJ,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,eAAe,EAAE,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC5I,CAAC;IACL,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAErD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAElD,OAAO,eAAe,CAAC;AAC3B,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,wBAAwB,GAAG,SAAU,OAAwB,EAAE,UAAmB,EAAE,QAAiB;IACtH,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;IACxE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;IAC/G,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC;IAE/G,IAAI,UAAU,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;QACzF,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QACtE,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;IACpC,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;AACzD,CAAC,CAAC;+JAEF,cAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG,SACrC,OAAe,EACf,KAAsB,EACtB,KAAyB,EACzB,QAAkB;iBAClB,iEAAyC,IAAI,YAC7C,iEAAiE,IAAI,EACrE,MAAe,iDACf,mFAAuB,IAAI,sBAC3B,iEAA6B,KAAK,aAClC,iEAAmB,CAAC,cACpB,oEAAoB,CAAC,aACrB,oEAAsC,IAAI,EAC1C,aAAmB,mEACnB,aAAa,uDAAG,KAAK,EACrB,6EAAoC,IAAI;IAExC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,OAAO,IAAI,CAAC,qBAAqB,CAC7B,OAAO,EACP,KAAK,EACL,KAAK,EACL,CAAC,CAAC,QAAQ,EACV,MAAM,EACN,OAAO,EACP,MAAM,EACN,eAAe,EACf,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,CAAC,OAAwB,EAAE,CAAG,CAAD,GAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,EAC3F,CAAC,OAAwB,EAAE,IAAwC,EAAE,EAAE;QACnE,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,0KAAC,mBAAA,AAAgB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACvH,MAAM,MAAM,GAAG,KAAK,CAAC;QAErB,MAAM,KAAK,GAAG;YACV,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;YAC9B,EAAE,CAAC,2BAA2B;SACjC,CAAC;QAEF,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;QACtK,IAAI,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;QAErE,IAAI,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;YACpD,WAAW,GAAG,cAAc,CAAC;QACjC,CAAC;QAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAChD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC/D,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAE7B,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;6KAChD,SAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;oBACvD,OAAO;gBACX,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC;gBAClC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;gBAEpC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC9G,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,cAAgC,CAAC,CAAC;YACzH,CAAC,MAAM,CAAC;gBACJ,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC/F,CAAC;QACL,CAAC;QAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;QAElD,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,IAAI,MAAM,EAAE,CAAC;YACT,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QAC5B,CAAC;QAED,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEnC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;QACb,CAAC;IACL,CAAC,EACD,CAAC,CAAC,aAAa,EACf,MAAM,CACT,CAAC;AACN,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,SAAU,OAAwB;iBAAE,MAAM,2DAAG,IAAI;IAC9F,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9D,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;QACvC,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;AACL,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.renderTarget.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.renderTarget.ts"], "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { RenderTargetCreationOptions, DepthTextureCreationOptions, TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport { ThinEngine } from \"../thinEngine\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport { WebGLRenderTargetWrapper } from \"../WebGL/webGLRenderTargetWrapper\";\r\nimport type { WebGLHardwareTexture } from \"../WebGL/webGLHardwareTexture\";\r\nimport { HasStencilAspect } from \"core/Materials/Textures/textureHelper.functions\";\r\n\r\nimport { Constants } from \"../constants\";\r\n\r\nimport \"../AbstractEngine/abstractEngine.texture\";\r\n\r\n/**\r\n * Type used to define a texture size (either with a number or with a rect width and height)\r\n * @deprecated please use TextureSize instead\r\n */\r\nexport type RenderTargetTextureSize = TextureSize;\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a new render target texture\r\n         * @param size defines the size of the texture\r\n         * @param options defines the options used to create the texture\r\n         * @returns a new render target wrapper ready to render texture\r\n         */\r\n        createRenderTargetTexture(size: TextureSize, options: boolean | RenderTargetCreationOptions): RenderTargetWrapper;\r\n\r\n        /**\r\n         * Updates the sample count of a render target texture\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#multisample-render-targets\r\n         * @param rtWrapper defines the render target wrapper to update\r\n         * @param samples defines the sample count to set\r\n         * @returns the effective sample count (could be 0 if multisample render targets are not supported)\r\n         */\r\n        updateRenderTargetTextureSampleCount(rtWrapper: Nullable<RenderTargetWrapper>, samples: number): number;\r\n\r\n        /** @internal */\r\n        _createDepthStencilTexture(size: TextureSize, options: DepthTextureCreationOptions, rtWrapper: RenderTargetWrapper): InternalTexture;\r\n\r\n        /** @internal */\r\n        _createHardwareRenderTargetWrapper(isMulti: boolean, isCube: boolean, size: TextureSize): RenderTargetWrapper;\r\n\r\n        /** @internal */\r\n        _setupDepthStencilTexture(internalTexture: InternalTexture, size: TextureSize, bilinearFiltering: boolean, comparisonFunction: number, samples?: number): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype._createHardwareRenderTargetWrapper = function (isMulti: boolean, isCube: boolean, size: TextureSize): RenderTargetWrapper {\r\n    const rtWrapper = new WebGLRenderTargetWrapper(isMulti, isCube, size, this, this._gl);\r\n    this._renderTargetWrapperCache.push(rtWrapper);\r\n    return rtWrapper;\r\n};\r\n\r\nThinEngine.prototype.createRenderTargetTexture = function (this: ThinEngine, size: TextureSize, options: boolean | RenderTargetCreationOptions): RenderTargetWrapper {\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(false, false, size) as WebGLRenderTargetWrapper;\r\n\r\n    let generateDepthBuffer = true;\r\n    let generateStencilBuffer = false;\r\n    let noColorAttachment = false;\r\n    let colorAttachment: InternalTexture | undefined = undefined;\r\n    let samples = 1;\r\n    let label: string | undefined = undefined;\r\n    if (options !== undefined && typeof options === \"object\") {\r\n        generateDepthBuffer = options.generateDepthBuffer ?? true;\r\n        generateStencilBuffer = !!options.generateStencilBuffer;\r\n        noColorAttachment = !!options.noColorAttachment;\r\n        colorAttachment = options.colorAttachment;\r\n        samples = options.samples ?? 1;\r\n        label = options.label;\r\n    }\r\n\r\n    const texture = colorAttachment || (noColorAttachment ? null : this._createInternalTexture(size, options, true, InternalTextureSource.RenderTarget));\r\n    const width = (<{ width: number; height: number; layers?: number }>size).width || <number>size;\r\n    const height = (<{ width: number; height: number; layers?: number }>size).height || <number>size;\r\n\r\n    const currentFrameBuffer = this._currentFramebuffer;\r\n    const gl = this._gl;\r\n\r\n    // Create the framebuffer\r\n    const framebuffer = gl.createFramebuffer();\r\n    this._bindUnboundFramebuffer(framebuffer);\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(generateStencilBuffer, generateDepthBuffer, width, height);\r\n\r\n    // No need to rebind on every frame\r\n    if (texture && !texture.is2DArray && !texture.is3D) {\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture._hardwareTexture!.underlyingResource, 0);\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(currentFrameBuffer);\r\n\r\n    rtWrapper.label = label ?? \"RenderTargetWrapper\";\r\n    rtWrapper._framebuffer = framebuffer;\r\n    rtWrapper._generateDepthBuffer = generateDepthBuffer;\r\n    rtWrapper._generateStencilBuffer = generateStencilBuffer;\r\n\r\n    rtWrapper.setTextures(texture);\r\n\r\n    if (!colorAttachment) {\r\n        this.updateRenderTargetTextureSampleCount(rtWrapper, samples);\r\n    } else {\r\n        rtWrapper._samples = colorAttachment.samples;\r\n        if (colorAttachment.samples > 1) {\r\n            const msaaRenderBuffer = (colorAttachment._hardwareTexture as WebGLHardwareTexture).getMSAARenderBuffer(0);\r\n\r\n            rtWrapper._MSAAFramebuffer = gl.createFramebuffer();\r\n\r\n            this._bindUnboundFramebuffer(rtWrapper._MSAAFramebuffer);\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.RENDERBUFFER, msaaRenderBuffer);\r\n            this._bindUnboundFramebuffer(null);\r\n        }\r\n    }\r\n\r\n    return rtWrapper;\r\n};\r\n\r\nThinEngine.prototype._createDepthStencilTexture = function (size: TextureSize, options: DepthTextureCreationOptions, rtWrapper: WebGLRenderTargetWrapper): InternalTexture {\r\n    const gl = this._gl;\r\n    const layers = (<{ width: number; height: number; depth?: number; layers?: number }>size).layers || 0;\r\n    const depth = (<{ width: number; height: number; depth?: number; layers?: number }>size).depth || 0;\r\n    let target: number = gl.TEXTURE_2D;\r\n    if (layers !== 0) {\r\n        target = gl.TEXTURE_2D_ARRAY;\r\n    } else if (depth !== 0) {\r\n        target = gl.TEXTURE_3D;\r\n    }\r\n    const internalTexture = new InternalTexture(this, InternalTextureSource.DepthStencil);\r\n    internalTexture.label = options.label;\r\n    if (!this._caps.depthTextureExtension) {\r\n        Logger.Error(\"Depth texture is not supported by your browser or hardware.\");\r\n        return internalTexture;\r\n    }\r\n\r\n    const internalOptions = {\r\n        bilinearFiltering: false,\r\n        comparisonFunction: 0,\r\n        generateStencil: false,\r\n        ...options,\r\n    };\r\n\r\n    this._bindTextureDirectly(target, internalTexture, true);\r\n\r\n    this._setupDepthStencilTexture(\r\n        internalTexture,\r\n        size,\r\n        internalOptions.comparisonFunction === 0 ? false : internalOptions.bilinearFiltering,\r\n        internalOptions.comparisonFunction,\r\n        internalOptions.samples\r\n    );\r\n\r\n    if (internalOptions.depthTextureFormat !== undefined) {\r\n        if (\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH16 &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH24 &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8 &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH32_FLOAT &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8\r\n        ) {\r\n            Logger.Error(`Depth texture ${internalOptions.depthTextureFormat} format is not supported.`);\r\n            return internalTexture;\r\n        }\r\n        internalTexture.format = internalOptions.depthTextureFormat;\r\n    } else {\r\n        internalTexture.format = internalOptions.generateStencil ? Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 : Constants.TEXTUREFORMAT_DEPTH24;\r\n    }\r\n\r\n    const hasStencil = HasStencilAspect(internalTexture.format);\r\n    const type = this._getWebGLTextureTypeFromDepthTextureFormat(internalTexture.format);\r\n    const format = hasStencil ? gl.DEPTH_STENCIL : gl.DEPTH_COMPONENT;\r\n    const internalFormat = this._getInternalFormatFromDepthTextureFormat(internalTexture.format, true, hasStencil);\r\n\r\n    if (internalTexture.is2DArray) {\r\n        gl.texImage3D(target, 0, internalFormat, internalTexture.width, internalTexture.height, layers, 0, format, type, null);\r\n    } else if (internalTexture.is3D) {\r\n        gl.texImage3D(target, 0, internalFormat, internalTexture.width, internalTexture.height, depth, 0, format, type, null);\r\n    } else {\r\n        gl.texImage2D(target, 0, internalFormat, internalTexture.width, internalTexture.height, 0, format, type, null);\r\n    }\r\n\r\n    this._bindTextureDirectly(target, null);\r\n\r\n    this._internalTexturesCache.push(internalTexture);\r\n\r\n    if (rtWrapper._depthStencilBuffer) {\r\n        gl.deleteRenderbuffer(rtWrapper._depthStencilBuffer);\r\n        rtWrapper._depthStencilBuffer = null;\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(rtWrapper._MSAAFramebuffer ?? rtWrapper._framebuffer);\r\n\r\n    rtWrapper._generateStencilBuffer = hasStencil;\r\n    rtWrapper._depthStencilTextureWithStencil = hasStencil;\r\n\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(\r\n        rtWrapper._generateStencilBuffer,\r\n        rtWrapper._generateDepthBuffer,\r\n        rtWrapper.width,\r\n        rtWrapper.height,\r\n        rtWrapper.samples,\r\n        internalTexture.format\r\n    );\r\n\r\n    this._bindUnboundFramebuffer(null);\r\n\r\n    return internalTexture;\r\n};\r\n\r\nThinEngine.prototype.updateRenderTargetTextureSampleCount = function (rtWrapper: Nullable<WebGLRenderTargetWrapper>, samples: number): number {\r\n    if (this.webGLVersion < 2 || !rtWrapper) {\r\n        return 1;\r\n    }\r\n\r\n    if (rtWrapper.samples === samples) {\r\n        return samples;\r\n    }\r\n\r\n    const gl = this._gl;\r\n\r\n    samples = Math.min(samples, this.getCaps().maxMSAASamples);\r\n\r\n    // Dispose previous render buffers\r\n    if (rtWrapper._depthStencilBuffer) {\r\n        gl.deleteRenderbuffer(rtWrapper._depthStencilBuffer);\r\n        rtWrapper._depthStencilBuffer = null;\r\n    }\r\n\r\n    if (rtWrapper._MSAAFramebuffer) {\r\n        gl.deleteFramebuffer(rtWrapper._MSAAFramebuffer);\r\n        rtWrapper._MSAAFramebuffer = null;\r\n    }\r\n\r\n    const hardwareTexture = rtWrapper.texture?._hardwareTexture as Nullable<WebGLHardwareTexture>;\r\n    hardwareTexture?.releaseMSAARenderBuffers();\r\n\r\n    if (rtWrapper.texture && samples > 1 && typeof gl.renderbufferStorageMultisample === \"function\") {\r\n        const framebuffer = gl.createFramebuffer();\r\n\r\n        if (!framebuffer) {\r\n            throw new Error(\"Unable to create multi sampled framebuffer\");\r\n        }\r\n\r\n        rtWrapper._MSAAFramebuffer = framebuffer;\r\n        this._bindUnboundFramebuffer(rtWrapper._MSAAFramebuffer);\r\n\r\n        const colorRenderbuffer = this._createRenderBuffer(\r\n            rtWrapper.texture.width,\r\n            rtWrapper.texture.height,\r\n            samples,\r\n            -1 /* not used */,\r\n            this._getRGBABufferInternalSizedFormat(rtWrapper.texture.type, rtWrapper.texture.format, rtWrapper.texture._useSRGBBuffer),\r\n            gl.COLOR_ATTACHMENT0,\r\n            false\r\n        );\r\n\r\n        if (!colorRenderbuffer) {\r\n            throw new Error(\"Unable to create multi sampled framebuffer\");\r\n        }\r\n\r\n        hardwareTexture?.addMSAARenderBuffer(colorRenderbuffer);\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(rtWrapper._MSAAFramebuffer ?? rtWrapper._framebuffer);\r\n\r\n    if (rtWrapper.texture) {\r\n        rtWrapper.texture.samples = samples;\r\n    }\r\n\r\n    rtWrapper._samples = samples;\r\n\r\n    const depthFormat = rtWrapper._depthStencilTexture ? rtWrapper._depthStencilTexture.format : undefined;\r\n\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(\r\n        rtWrapper._generateStencilBuffer,\r\n        rtWrapper._generateDepthBuffer,\r\n        rtWrapper.width,\r\n        rtWrapper.height,\r\n        samples,\r\n        depthFormat\r\n    );\r\n\r\n    this._bindUnboundFramebuffer(null);\r\n\r\n    return samples;\r\n};\r\n\r\nThinEngine.prototype._setupDepthStencilTexture = function (\r\n    internalTexture: InternalTexture,\r\n    size: TextureSize,\r\n    bilinearFiltering: boolean,\r\n    comparisonFunction: number,\r\n    samples = 1\r\n) {\r\n    const width = (<{ width: number; height: number; layers?: number }>size).width ?? <number>size;\r\n    const height = (<{ width: number; height: number; layers?: number }>size).height ?? <number>size;\r\n    const layers = (<{ width: number; height: number; depth?: number; layers?: number }>size).layers || 0;\r\n    const depth = (<{ width: number; height: number; depth?: number; layers?: number }>size).depth || 0;\r\n\r\n    internalTexture.baseWidth = width;\r\n    internalTexture.baseHeight = height;\r\n    internalTexture.width = width;\r\n    internalTexture.height = height;\r\n    internalTexture.is2DArray = layers > 0;\r\n    internalTexture.depth = layers || depth;\r\n    internalTexture.isReady = true;\r\n    internalTexture.samples = samples;\r\n    internalTexture.generateMipMaps = false;\r\n    internalTexture.samplingMode = bilinearFiltering ? Constants.TEXTURE_BILINEAR_SAMPLINGMODE : Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n    internalTexture.type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n    internalTexture._comparisonFunction = comparisonFunction;\r\n\r\n    const gl = this._gl;\r\n    const target = this._getTextureTarget(internalTexture);\r\n    const samplingParameters = this._getSamplingParameters(internalTexture.samplingMode, false);\r\n    gl.texParameteri(target, gl.TEXTURE_MAG_FILTER, samplingParameters.mag);\r\n    gl.texParameteri(target, gl.TEXTURE_MIN_FILTER, samplingParameters.min);\r\n    gl.texParameteri(target, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n    gl.texParameteri(target, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n    // TEXTURE_COMPARE_FUNC/MODE are only availble in WebGL2.\r\n    if (this.webGLVersion > 1) {\r\n        if (comparisonFunction === 0) {\r\n            gl.texParameteri(target, gl.TEXTURE_COMPARE_FUNC, Constants.LEQUAL);\r\n            gl.texParameteri(target, gl.TEXTURE_COMPARE_MODE, gl.NONE);\r\n        } else {\r\n            gl.texParameteri(target, gl.TEXTURE_COMPARE_FUNC, comparisonFunction);\r\n            gl.texParameteri(target, gl.TEXTURE_COMPARE_MODE, gl.COMPARE_REF_TO_TEXTURE);\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAE7E,OAAO,EAAE,gBAAgB,EAAE,4DAAwD;AAInF,OAAO,0CAA0C,CAAC;;;;;;;+JAuClD,cAAU,CAAC,SAAS,CAAC,kCAAkC,GAAG,SAAU,OAAgB,EAAE,MAAe,EAAE,IAAiB;IACpH,MAAM,SAAS,GAAG,2LAAI,2BAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACtF,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/C,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,SAA4B,IAAiB,EAAE,OAA8C;IAC1I,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAA6B,CAAC;IAE1G,IAAI,mBAAmB,GAAG,IAAI,CAAC;IAC/B,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAClC,IAAI,iBAAiB,GAAG,KAAK,CAAC;IAC9B,IAAI,eAAe,GAAgC,SAAS,CAAC;IAC7D,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,KAAK,GAAuB,SAAS,CAAC;IAC1C,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;;QACvD,mBAAmB,mCAAG,OAAO,CAAC,mBAAmB,uFAAI,IAAI,CAAC;QAC1D,qBAAqB,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;QACxD,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;QAChD,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;;QAC1C,OAAO,uBAAG,OAAO,CAAC,OAAO,+DAAI,CAAC,CAAC;QAC/B,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,MAAM,OAAO,GAAG,eAAe,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAA,EAAA,sCAAA,IAAqC,CAAC,CAAC;IACrJ,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;IAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;IAEjG,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;IACpD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,yBAAyB;IACzB,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAC3C,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAC1C,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAElI,mCAAmC;IACnC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACjD,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,gBAAiB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;IAClI,CAAC;IAED,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;IAEjD,SAAS,CAAC,KAAK,wCAAG,KAAK,GAAI,qBAAqB,CAAC;IACjD,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;IACrC,SAAS,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;IACrD,SAAS,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;IAEzD,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAE/B,IAAI,CAAC,eAAe,EAAE,CAAC;QACnB,IAAI,CAAC,oCAAoC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC,MAAM,CAAC;QACJ,SAAS,CAAC,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC;QAC7C,IAAI,eAAe,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,gBAAgB,GAAI,eAAe,CAAC,gBAAyC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAE3G,SAAS,CAAC,gBAAgB,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;YAEpD,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YACzD,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;YACpG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;IACL,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,0BAA0B,GAAG,SAAU,IAAiB,EAAE,OAAoC,EAAE,SAAmC;IACpJ,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,MAAM,GAAwE,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;IACtG,MAAM,KAAK,GAAwE,IAAK,CAAC,KAAK,IAAI,CAAC,CAAC;IACpG,IAAI,MAAM,GAAW,EAAE,CAAC,UAAU,CAAC;IACnC,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACf,MAAM,GAAG,EAAE,CAAC,gBAAgB,CAAC;IACjC,CAAC,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;QACrB,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,MAAM,eAAe,GAAG,IAAI,qMAAe,CAAC,IAAI,EAAA,GAAA,sCAAA,GAAqC,CAAC;IACtF,eAAe,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACtC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;iKACpC,SAAM,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;QAC5E,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED,MAAM,eAAe,GAAG;QACpB,iBAAiB,EAAE,KAAK;QACxB,kBAAkB,EAAE,CAAC;QACrB,eAAe,EAAE,KAAK;QACtB,GAAG,OAAO;KACb,CAAC;IAEF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAEzD,IAAI,CAAC,yBAAyB,CAC1B,eAAe,EACf,IAAI,EACJ,eAAe,CAAC,kBAAkB,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,iBAAiB,EACpF,eAAe,CAAC,kBAAkB,EAClC,eAAe,CAAC,OAAO,CAC1B,CAAC;IAEF,IAAI,eAAe,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;QACnD,IACI,eAAe,CAAC,kBAAkB,KAAK,MACvC,GADgD,CAAC,WAClC,CAAC,SADsD,SACpC,KAAK,MACvC,GADgD,CAAC,WAClC,CAAC,SADsD,SACpC,KAAK,MACvC,GADgD,CAAC,WAClC,CAAC,kBAAkB,KAAK,AAD6C,MAEpF,GADgD,CAAC,WAClC,CAAC,kBAD+D,AAC7C,KAAK,MACvC,GADgD,CAAC,WAClC,CAAC,eAD4D,GAC1C,KAAK,IAAA,KAAS,CAAC,mCAAmC,EACtF,CAAC;qKACC,SAAM,CAAC,KAAK,CAAC,iBAAmD,OAAlC,eAAe,CAAC,kBAAkB,EAAA,0BAA2B,CAAC,CAAC;YAC7F,OAAO,eAAe,CAAC;QAC3B,CAAC;QACD,eAAe,CAAC,MAAM,GAAG,eAAe,CAAC,kBAAkB,CAAC;IAChE,CAAC,MAAM,CAAC;QACJ,eAAe,CAAC,MAAM,GAAG,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,KAAA,IAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC;IAC1I,CAAC;IAED,MAAM,UAAU,qMAAG,mBAAA,AAAgB,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC5D,MAAM,IAAI,GAAG,IAAI,CAAC,0CAA0C,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IACrF,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC;IAClE,MAAM,cAAc,GAAG,IAAI,CAAC,wCAAwC,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IAE/G,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;QAC5B,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3H,CAAC,MAAM,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC;QAC9B,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1H,CAAC,MAAM,CAAC;QACJ,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACnH,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAExC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAElD,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;QAChC,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACrD,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACzC,CAAC;;IAED,IAAI,CAAC,uBAAuB,0CAAW,gBAAgB,uDAA1B,SAAS,qBAAqB,SAAS,CAAC,YAAY,CAAC,CAAC;IAEnF,SAAS,CAAC,sBAAsB,GAAG,UAAU,CAAC;IAC9C,SAAS,CAAC,+BAA+B,GAAG,UAAU,CAAC;IAEvD,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAClE,SAAS,CAAC,sBAAsB,EAChC,SAAS,CAAC,oBAAoB,EAC9B,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,MAAM,EAChB,SAAS,CAAC,OAAO,EACjB,eAAe,CAAC,MAAM,CACzB,CAAC;IAEF,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnC,OAAO,eAAe,CAAC;AAC3B,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,oCAAoC,GAAG,SAAU,SAA6C,EAAE,OAAe;QAwBxG,SAAS;IAvBjC,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACtC,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAI,SAAS,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;QAChC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC;IAE3D,kCAAkC;IAClC,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;QAChC,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACrD,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACzC,CAAC;IAED,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;QAC7B,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACjD,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED,MAAM,eAAe,mCAAa,OAAO,0EAAE,gBAAkD,CAAC;sEAC9F,eAAe,CAAE,wBAAwB,EAAE,CAAC;IAE5C,IAAI,SAAS,CAAC,OAAO,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,8BAA8B,KAAK,UAAU,EAAE,CAAC;QAC9F,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAE3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAClE,CAAC;QAED,SAAS,CAAC,gBAAgB,GAAG,WAAW,CAAC;QACzC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAEzD,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAC9C,SAAS,CAAC,OAAO,CAAC,KAAK,EACvB,SAAS,CAAC,OAAO,CAAC,MAAM,EACxB,OAAO,EACP,CAAC,CAAC,CAAC,YAAA,EAAc,GACjB,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,EAC1H,EAAE,CAAC,iBAAiB,EACpB,KAAK,CACR,CAAC;QAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAClE,CAAC;0EAED,eAAe,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;IAC5D,CAAC;;IAED,IAAI,CAAC,uBAAuB,CAAC,SAAS,gCAAC,gBAAgB,qFAAI,SAAS,CAAC,YAAY,CAAC,CAAC;IAEnF,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;QACpB,SAAS,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IACxC,CAAC;IAED,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;IAE7B,MAAM,WAAW,GAAG,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;IAEvG,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAClE,SAAS,CAAC,sBAAsB,EAChC,SAAS,CAAC,oBAAoB,EAC9B,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,MAAM,EAChB,OAAO,EACP,WAAW,CACd,CAAC;IAEF,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnC,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,6KAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,SAC7C,eAAgC,EAChC,IAAiB,EACjB,iBAA0B,EAC1B,kBAA0B;kBAC1B,OAAO,0DAAG,CAAC;QAEwD,IAAK;IAAxE,MAAM,KAAK,uBAA8D,KAAK,qDAAY,IAAI,CAAC;;IAC/F,MAAM,MAAM,IAAwD,IAAK,gBAAC,MAAM,uDAAY,IAAI,CAAC;IACjG,MAAM,MAAM,GAAwE,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;IACtG,MAAM,KAAK,GAAwE,IAAK,CAAC,KAAK,IAAI,CAAC,CAAC;IAEpG,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC;IAClC,eAAe,CAAC,UAAU,GAAG,MAAM,CAAC;IACpC,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;IAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;IAChC,eAAe,CAAC,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;IACvC,eAAe,CAAC,KAAK,GAAG,MAAM,IAAI,KAAK,CAAC;IACxC,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;IAC/B,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;IACxC,eAAe,CAAC,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC;IACpI,eAAe,CAAC,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;IAC3D,eAAe,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IAEzD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;IACvD,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAC5F,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACxE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACxE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC9D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAE9D,yDAAyD;IACzD,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;QACxB,IAAI,kBAAkB,KAAK,CAAC,EAAE,CAAC;YAC3B,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YACpE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC,MAAM,CAAC;YACJ,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;YACtE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,EAAE,CAAC,sBAAsB,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;AACL,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.renderTargetTexture.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.renderTargetTexture.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Sets a depth stencil texture from a render target to the according uniform.\r\n         * @param channel The texture channel\r\n         * @param uniform The uniform to set\r\n         * @param texture The render target texture containing the depth stencil texture to apply\r\n         * @param name The texture name\r\n         */\r\n        setDepthStencilTexture(channel: number, uniform: Nullable<WebGLUniformLocation>, texture: Nullable<RenderTargetTexture>, name?: string): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.setDepthStencilTexture = function (channel: number, uniform: Nullable<WebGLUniformLocation>, texture: Nullable<RenderTargetTexture>, name?: string): void {\r\n    if (channel === undefined) {\r\n        return;\r\n    }\r\n\r\n    if (uniform) {\r\n        this._boundUniforms[channel] = uniform;\r\n    }\r\n\r\n    if (!texture || !texture.depthStencilTexture) {\r\n        this._setTexture(channel, null, undefined, undefined, name);\r\n    } else {\r\n        this._setTexture(channel, texture, false, true, name);\r\n    }\r\n};\r\n"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;;gKAiBtD,aAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,SAAU,OAAe,EAAE,OAAuC,EAAE,OAAsC,EAAE,IAAa;IACnK,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QACxB,OAAO;IACX,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACV,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;IAC3C,CAAC;IAED,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;QAC3C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC,MAAM,CAAC;QACJ,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;AACL,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.renderTargetCube.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.renderTargetCube.ts"], "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { Constants } from \"../constants\";\r\nimport { ThinEngine } from \"../thinEngine\";\r\nimport type { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { WebGLRenderTargetWrapper } from \"../WebGL/webGLRenderTargetWrapper\";\r\nimport type { RenderTargetCreationOptions } from \"../../Materials/Textures/textureCreationOptions\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a new render target cube wrapper\r\n         * @param size defines the size of the texture\r\n         * @param options defines the options used to create the texture\r\n         * @returns a new render target cube wrapper\r\n         */\r\n        createRenderTargetCubeTexture(size: number, options?: RenderTargetCreationOptions): RenderTargetWrapper;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createRenderTargetCubeTexture = function (size: number, options?: RenderTargetCreationOptions): RenderTargetWrapper {\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(false, true, size) as WebGLRenderTargetWrapper;\r\n\r\n    const fullOptions = {\r\n        generateMipMaps: true,\r\n        generateDepthBuffer: true,\r\n        generateStencilBuffer: false,\r\n        type: Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n        samplingMode: Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        format: Constants.TEXTUREFORMAT_RGBA,\r\n        ...options,\r\n    };\r\n    fullOptions.generateStencilBuffer = fullOptions.generateDepthBuffer && fullOptions.generateStencilBuffer;\r\n\r\n    if (fullOptions.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n        // if floating point linear (gl.FLOAT) then force to NEAREST_SAMPLINGMODE\r\n        fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n    } else if (fullOptions.type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n        // if floating point linear (HALF_FLOAT) then force to NEAREST_SAMPLINGMODE\r\n        fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n    }\r\n    const gl = this._gl;\r\n\r\n    const texture = new InternalTexture(this, InternalTextureSource.RenderTarget);\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n\r\n    const filters = this._getSamplingParameters(fullOptions.samplingMode, fullOptions.generateMipMaps);\r\n\r\n    if (fullOptions.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n        fullOptions.type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n        Logger.Warn(\"Float textures are not supported. Cube render target forced to TEXTURETYPE_UNESIGNED_BYTE type\");\r\n    }\r\n\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MIN_FILTER, filters.min);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n    for (let face = 0; face < 6; face++) {\r\n        gl.texImage2D(\r\n            gl.TEXTURE_CUBE_MAP_POSITIVE_X + face,\r\n            0,\r\n            this._getRGBABufferInternalSizedFormat(fullOptions.type, fullOptions.format),\r\n            size,\r\n            size,\r\n            0,\r\n            this._getInternalFormat(fullOptions.format),\r\n            this._getWebGLTextureType(fullOptions.type),\r\n            null\r\n        );\r\n    }\r\n\r\n    // Create the framebuffer\r\n    const framebuffer = gl.createFramebuffer();\r\n    this._bindUnboundFramebuffer(framebuffer);\r\n\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(fullOptions.generateStencilBuffer, fullOptions.generateDepthBuffer, size, size);\r\n\r\n    // MipMaps\r\n    if (fullOptions.generateMipMaps) {\r\n        gl.generateMipmap(gl.TEXTURE_CUBE_MAP);\r\n    }\r\n\r\n    // Unbind\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n    this._bindUnboundFramebuffer(null);\r\n\r\n    rtWrapper._framebuffer = framebuffer;\r\n    rtWrapper._generateDepthBuffer = fullOptions.generateDepthBuffer;\r\n    rtWrapper._generateStencilBuffer = fullOptions.generateStencilBuffer;\r\n\r\n    texture.width = size;\r\n    texture.height = size;\r\n    texture.isReady = true;\r\n    texture.isCube = true;\r\n    texture.samples = 1;\r\n    texture.generateMipMaps = fullOptions.generateMipMaps;\r\n    texture.samplingMode = fullOptions.samplingMode;\r\n    texture.type = fullOptions.type;\r\n    texture.format = fullOptions.format;\r\n\r\n    this._internalTexturesCache.push(texture);\r\n    rtWrapper.setTextures(texture);\r\n\r\n    return rtWrapper;\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;;;;gKAkB3C,aAAU,CAAC,SAAS,CAAC,6BAA6B,GAAG,SAAU,IAAY,EAAE,OAAqC;IAC9G,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAA6B,CAAC;IAEzG,MAAM,WAAW,GAAG;QAChB,eAAe,EAAE,IAAI;QACrB,mBAAmB,EAAE,IAAI;QACzB,qBAAqB,EAAE,KAAK;QAC5B,IAAI,EAAE,SAAS,CAAC,yBAAyB;QACzC,YAAY,EAAE,SAAS,CAAC,8BAA8B;QACtD,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,GAAG,OAAO;KACb,CAAC;IACF,WAAW,CAAC,qBAAqB,GAAG,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,qBAAqB,CAAC;IAEzG,IAAI,WAAW,CAAC,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,UAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,EAAA,wBAA2B,EAAE,CAAC;QAC9F,yEAAyE;QACzE,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;IACtE,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,eAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAA,6BAA+B,EAAE,CAAC;QAC9G,2EAA2E;QAC3E,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;IACtE,CAAC;IACD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,MAAM,OAAO,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAA,EAAA,sCAAA,GAAqC,CAAC;IAC9E,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAE9D,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;IAEnG,IAAI,WAAW,CAAC,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,UAAiB,EAAA,EAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QAC/E,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;iKACvD,SAAM,CAAC,IAAI,CAAC,gGAAgG,CAAC,CAAC;IAClH,CAAC;IAED,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAE3E,IAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,CAAE,CAAC;QAClC,EAAE,CAAC,UAAU,CACT,EAAE,CAAC,2BAA2B,GAAG,IAAI,EACrC,CAAC,EACD,IAAI,CAAC,iCAAiC,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,EAC5E,IAAI,EACJ,IAAI,EACJ,CAAC,EACD,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,EAC3C,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,IAAI,CAAC,EAC3C,IAAI,CACP,CAAC;IACN,CAAC;IAED,yBAAyB;IACzB,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAC3C,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAE1C,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAAC,WAAW,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAEvJ,UAAU;IACV,IAAI,WAAW,CAAC,eAAe,EAAE,CAAC;QAC9B,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC3C,CAAC;IAED,SAAS;IACT,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACrD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnC,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;IACrC,SAAS,CAAC,oBAAoB,GAAG,WAAW,CAAC,mBAAmB,CAAC;IACjE,SAAS,CAAC,sBAAsB,GAAG,WAAW,CAAC,qBAAqB,CAAC;IAErE,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IACrB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IACvB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;IACpB,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;IACtD,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IAChD,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;IAChC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;IAEpC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1C,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAE/B,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.prefilteredCubeTexture.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.prefilteredCubeTexture.ts"], "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Constants } from \"../constants\";\r\nimport { SphericalPolynomial } from \"core/Maths/sphericalPolynomial\";\r\nimport { BaseTexture } from \"core/Materials/Textures/baseTexture\";\r\nimport type { DDSInfo } from \"core/Misc/dds\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Create a cube texture from prefiltered data (ie. the mipmaps contain ready to use data for PBR reflection)\r\n         * @param rootUrl defines the url where the file to load is located\r\n         * @param scene defines the current scene\r\n         * @param lodScale defines scale to apply to the mip map selection\r\n         * @param lodOffset defines offset to apply to the mip map selection\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @param createPolynomials defines wheter or not to create polynomails harmonics for the texture\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createPrefilteredCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            lodScale: number,\r\n            lodOffset: number,\r\n            onLoad?: Nullable<(internalTexture: Nullable<InternalTexture>) => void>,\r\n            onError?: Nullable<(message?: string, exception?: any) => void>,\r\n            format?: number,\r\n            forcedExtension?: any,\r\n            createPolynomials?: boolean\r\n        ): InternalTexture;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createPrefilteredCubeTexture = function (\r\n    rootUrl: string,\r\n    scene: Nullable<Scene>,\r\n    lodScale: number,\r\n    lodOffset: number,\r\n    onLoad: Nullable<(internalTexture: Nullable<InternalTexture>) => void> = null,\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    format?: number,\r\n    forcedExtension: any = null,\r\n    createPolynomials: boolean = true\r\n): InternalTexture {\r\n    const callbackAsync = async (loadData: any) => {\r\n        if (!loadData) {\r\n            if (onLoad) {\r\n                onLoad(null);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const texture = loadData.texture as InternalTexture;\r\n        if (!createPolynomials) {\r\n            texture._sphericalPolynomial = new SphericalPolynomial();\r\n        } else if (loadData.info.sphericalPolynomial) {\r\n            texture._sphericalPolynomial = loadData.info.sphericalPolynomial;\r\n        }\r\n        texture._source = InternalTextureSource.CubePrefiltered;\r\n\r\n        if (this.getCaps().textureLOD) {\r\n            // Do not add extra process if texture lod is supported.\r\n            if (onLoad) {\r\n                onLoad(texture);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const mipSlices = 3;\r\n\r\n        const gl = this._gl;\r\n        const width = loadData.width;\r\n        if (!width) {\r\n            return;\r\n        }\r\n\r\n        // eslint-disable-next-line @typescript-eslint/naming-convention\r\n        const { DDSTools } = await import(\"core/Misc/dds\");\r\n\r\n        const textures: BaseTexture[] = [];\r\n        for (let i = 0; i < mipSlices; i++) {\r\n            //compute LOD from even spacing in smoothness (matching shader calculation)\r\n            const smoothness = i / (mipSlices - 1);\r\n            const roughness = 1 - smoothness;\r\n\r\n            const minLODIndex = lodOffset; // roughness = 0\r\n            const maxLODIndex = Math.log2(width) * lodScale + lodOffset; // roughness = 1\r\n\r\n            const lodIndex = minLODIndex + (maxLODIndex - minLODIndex) * roughness;\r\n            const mipmapIndex = Math.round(Math.min(Math.max(lodIndex, 0), maxLODIndex));\r\n\r\n            const glTextureFromLod = new InternalTexture(this, InternalTextureSource.Temp);\r\n            glTextureFromLod.type = texture.type;\r\n            glTextureFromLod.format = texture.format;\r\n            glTextureFromLod.width = Math.pow(2, Math.max(Math.log2(width) - mipmapIndex, 0));\r\n            glTextureFromLod.height = glTextureFromLod.width;\r\n            glTextureFromLod.isCube = true;\r\n            glTextureFromLod._cachedWrapU = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n            glTextureFromLod._cachedWrapV = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, glTextureFromLod, true);\r\n\r\n            glTextureFromLod.samplingMode = Constants.TEXTURE_LINEAR_LINEAR;\r\n            gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAG_FILTER, gl.LINEAR);\r\n            gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MIN_FILTER, gl.LINEAR);\r\n            gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n            gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n            if (loadData.isDDS) {\r\n                const info: DDSInfo = loadData.info;\r\n                const data: any = loadData.data;\r\n                this._unpackFlipY(info.isCompressed);\r\n\r\n                DDSTools.UploadDDSLevels(this, glTextureFromLod, data, info, true, 6, mipmapIndex);\r\n            } else {\r\n                Logger.Warn(\"DDS is the only prefiltered cube map supported so far.\");\r\n            }\r\n\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n\r\n            // Wrap in a base texture for easy binding.\r\n            const lodTexture = new BaseTexture(scene);\r\n            lodTexture._isCube = true;\r\n            lodTexture._texture = glTextureFromLod;\r\n\r\n            glTextureFromLod.isReady = true;\r\n            textures.push(lodTexture);\r\n        }\r\n\r\n        texture._lodTextureHigh = textures[2];\r\n        texture._lodTextureMid = textures[1];\r\n        texture._lodTextureLow = textures[0];\r\n\r\n        if (onLoad) {\r\n            onLoad(texture);\r\n        }\r\n    };\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-misused-promises\r\n    return this.createCubeTexture(rootUrl, scene, null, false, callbackAsync, onError, format, forcedExtension, createPolynomials, lodScale, lodOffset);\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAI3C,OAAO,EAAE,mBAAmB,EAAE,2CAAuC;AACrE,OAAO,EAAE,WAAW,EAAE,gDAA4C;;;;;;gKAiClE,aAAU,CAAC,SAAS,CAAC,4BAA4B,GAAG,SAChD,OAAe,EACf,KAAsB,EACtB,QAAgB,EAChB,SAAiB;iBACjB,iEAAyE,IAAI,YAC7E,iEAAiE,IAAI,EACrE,MAAe,mEACf,iEAAuB,IAAI,sBAC3B,iEAA6B,IAAI;IAEjC,MAAM,aAAa,GAAG,KAAK,EAAE,QAAa,EAAE,EAAE;QAC1C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,CAAC,IAAI,CAAC,CAAC;YACjB,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,OAA0B,CAAC;QACpD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,OAAO,CAAC,oBAAoB,GAAG,2KAAI,sBAAmB,EAAE,CAAC;QAC7D,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3C,OAAO,CAAC,oBAAoB,GAAG,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC;QACrE,CAAC;QACD,OAAO,CAAC,OAAO,GAAA,EAAA,yCAAA,EAAwC,CAAC;QAExD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE,CAAC;YAC5B,wDAAwD;YACxD,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,CAAC,OAAO,CAAC,CAAC;YACpB,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,CAAC,CAAC;QAEpB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QAED,gEAAgE;QAChE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,MAAM,qBAAiB,CAAC;QAEnD,MAAM,QAAQ,GAAkB,EAAE,CAAC;QACnC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,CAAE,CAAC;YACjC,2EAA2E;YAC3E,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;YACvC,MAAM,SAAS,GAAG,CAAC,GAAG,UAAU,CAAC;YAEjC,MAAM,WAAW,GAAG,SAAS,CAAC,CAAC,gBAAgB;YAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS,CAAC,CAAC,gBAAgB;YAE7E,MAAM,QAAQ,GAAG,WAAW,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,SAAS,CAAC;YACvE,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC;YAE7E,MAAM,gBAAgB,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAA,EAAA,8BAAA,GAA6B,CAAC;YAC/E,gBAAgB,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACrC,gBAAgB,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACzC,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;YAClF,gBAAgB,CAAC,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC;YACjD,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC;YAC/B,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;YACpE,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;YACpE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAEvE,gBAAgB,CAAC,YAAY,GAAG,SAAS,CAAC,qBAAqB,CAAC;YAChE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;YACxE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;YACxE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;YAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;YAE3E,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACjB,MAAM,IAAI,GAAY,QAAQ,CAAC,IAAI,CAAC;gBACpC,MAAM,IAAI,GAAQ,QAAQ,CAAC,IAAI,CAAC;gBAChC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAErC,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;YACvF,CAAC,MAAM,CAAC;yKACJ,SAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;YAC1E,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAErD,2CAA2C;YAC3C,MAAM,UAAU,GAAG,mLAAI,cAAW,CAAC,KAAK,CAAC,CAAC;YAC1C,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;YAC1B,UAAU,CAAC,QAAQ,GAAG,gBAAgB,CAAC;YAEvC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,CAAC,eAAe,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrC,OAAO,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAErC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,CAAC,OAAO,CAAC,CAAC;QACpB,CAAC;IACL,CAAC,CAAC;IAEF,kEAAkE;IAClE,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AACxJ,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.uniformBuffer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.uniformBuffer.ts"], "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { FloatArray, Nullable } from \"../../types\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\nimport { WebGLDataBuffer } from \"../../Meshes/WebGL/webGLDataBuffer\";\r\nimport type { IPipelineContext } from \"../IPipelineContext\";\r\nimport type { WebGLPipelineContext } from \"../WebGL/webGLPipelineContext\";\r\n\r\ndeclare module \"../../Engines/thinEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface ThinEngine {\r\n        /**\r\n         * Create an uniform buffer\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n         * @param elements defines the content of the uniform buffer\r\n         * @param label defines a name for the buffer (for debugging purpose)\r\n         * @returns the webGL uniform buffer\r\n         */\r\n        createUniformBuffer(elements: FloatArray, label?: string): DataBuffer;\r\n\r\n        /**\r\n         * Create a dynamic uniform buffer\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n         * @param elements defines the content of the uniform buffer\r\n         * @param label defines a name for the buffer (for debugging purpose)\r\n         * @returns the webGL uniform buffer\r\n         */\r\n        createDynamicUniformBuffer(elements: FloatArray, label?: string): DataBuffer;\r\n\r\n        /**\r\n         * Update an existing uniform buffer\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n         * @param uniformBuffer defines the target uniform buffer\r\n         * @param elements defines the content to update\r\n         * @param offset defines the offset in the uniform buffer where update should start\r\n         * @param count defines the size of the data to update\r\n         */\r\n        updateUniformBuffer(uniformBuffer: DataBuffer, elements: FloatArray, offset?: number, count?: number): void;\r\n\r\n        /**\r\n         * Bind an uniform buffer to the current webGL context\r\n         * @param buffer defines the buffer to bind\r\n         */\r\n        bindUniformBuffer(buffer: Nullable<DataBuffer>): void;\r\n\r\n        /**\r\n         * Bind a buffer to the current webGL context at a given location\r\n         * @param buffer defines the buffer to bind\r\n         * @param location defines the index where to bind the buffer\r\n         * @param name Name of the uniform variable to bind\r\n         */\r\n        bindUniformBufferBase(buffer: DataBuffer, location: number, name: string): void;\r\n\r\n        /**\r\n         * Bind a specific block at a given index in a specific shader program\r\n         * @param pipelineContext defines the pipeline context to use\r\n         * @param blockName defines the block name\r\n         * @param index defines the index where to bind the block\r\n         */\r\n        bindUniformBlock(pipelineContext: IPipelineContext, blockName: string, index: number): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createUniformBuffer = function (elements: FloatArray, _label?: string): DataBuffer {\r\n    const ubo = this._gl.createBuffer();\r\n\r\n    if (!ubo) {\r\n        throw new Error(\"Unable to create uniform buffer\");\r\n    }\r\n    const result = new WebGLDataBuffer(ubo);\r\n\r\n    this.bindUniformBuffer(result);\r\n\r\n    if (elements instanceof Float32Array) {\r\n        this._gl.bufferData(this._gl.UNIFORM_BUFFER, <Float32Array>elements, this._gl.STATIC_DRAW);\r\n    } else {\r\n        this._gl.bufferData(this._gl.UNIFORM_BUFFER, new Float32Array(elements), this._gl.STATIC_DRAW);\r\n    }\r\n\r\n    this.bindUniformBuffer(null);\r\n\r\n    result.references = 1;\r\n    return result;\r\n};\r\n\r\nThinEngine.prototype.createDynamicUniformBuffer = function (elements: FloatArray, _label?: string): DataBuffer {\r\n    const ubo = this._gl.createBuffer();\r\n\r\n    if (!ubo) {\r\n        throw new Error(\"Unable to create dynamic uniform buffer\");\r\n    }\r\n\r\n    const result = new WebGLDataBuffer(ubo);\r\n    this.bindUniformBuffer(result);\r\n\r\n    if (elements instanceof Float32Array) {\r\n        this._gl.bufferData(this._gl.UNIFORM_BUFFER, <Float32Array>elements, this._gl.DYNAMIC_DRAW);\r\n    } else {\r\n        this._gl.bufferData(this._gl.UNIFORM_BUFFER, new Float32Array(elements), this._gl.DYNAMIC_DRAW);\r\n    }\r\n\r\n    this.bindUniformBuffer(null);\r\n\r\n    result.references = 1;\r\n    return result;\r\n};\r\n\r\nThinEngine.prototype.updateUniformBuffer = function (uniformBuffer: DataBuffer, elements: FloatArray, offset?: number, count?: number): void {\r\n    this.bindUniformBuffer(uniformBuffer);\r\n\r\n    if (offset === undefined) {\r\n        offset = 0;\r\n    }\r\n\r\n    if (count === undefined) {\r\n        if (elements instanceof Float32Array) {\r\n            this._gl.bufferSubData(this._gl.UNIFORM_BUFFER, offset, <Float32Array>elements);\r\n        } else {\r\n            this._gl.bufferSubData(this._gl.UNIFORM_BUFFER, offset, new Float32Array(elements));\r\n        }\r\n    } else {\r\n        if (elements instanceof Float32Array) {\r\n            this._gl.bufferSubData(this._gl.UNIFORM_BUFFER, 0, elements.subarray(offset, offset + count));\r\n        } else {\r\n            this._gl.bufferSubData(this._gl.UNIFORM_BUFFER, 0, new Float32Array(elements).subarray(offset, offset + count));\r\n        }\r\n    }\r\n\r\n    this.bindUniformBuffer(null);\r\n};\r\n\r\nThinEngine.prototype.bindUniformBuffer = function (buffer: Nullable<DataBuffer>): void {\r\n    this._gl.bindBuffer(this._gl.UNIFORM_BUFFER, buffer ? buffer.underlyingResource : null);\r\n};\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nThinEngine.prototype.bindUniformBufferBase = function (buffer: DataBuffer, location: number, name: string): void {\r\n    this._gl.bindBufferBase(this._gl.UNIFORM_BUFFER, location, buffer ? buffer.underlyingResource : null);\r\n};\r\n\r\nThinEngine.prototype.bindUniformBlock = function (pipelineContext: IPipelineContext, blockName: string, index: number): void {\r\n    const program = (pipelineContext as WebGLPipelineContext).program!;\r\n\r\n    const uniformLocation = this._gl.getUniformBlockIndex(program, blockName);\r\n\r\n    if (uniformLocation !== 0xffffffff) {\r\n        this._gl.uniformBlockBinding(program, uniformLocation, index);\r\n    }\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAGtD,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAC;;;gKA2DrE,aAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAU,QAAoB,EAAE,MAAe;IACtF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;IAEpC,IAAI,CAAC,GAAG,EAAE,CAAC;QACP,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvD,CAAC;IACD,MAAM,MAAM,GAAG,iLAAI,kBAAe,CAAC,GAAG,CAAC,CAAC;IAExC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAE/B,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;QACnC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAgB,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC/F,CAAC,MAAM,CAAC;QACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACnG,CAAC;IAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAE7B,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;IACtB,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,0BAA0B,GAAG,SAAU,QAAoB,EAAE,MAAe;IAC7F,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;IAEpC,IAAI,CAAC,GAAG,EAAE,CAAC;QACP,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM,MAAM,GAAG,iLAAI,kBAAe,CAAC,GAAG,CAAC,CAAC;IACxC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAE/B,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;QACnC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAgB,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAChG,CAAC,MAAM,CAAC;QACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACpG,CAAC;IAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAE7B,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;IACtB,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAU,aAAyB,EAAE,QAAoB,EAAE,MAAe,EAAE,KAAc;IACjI,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAEtC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACvB,MAAM,GAAG,CAAC,CAAC;IACf,CAAC;IAED,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QACtB,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;YACnC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,EAAgB,QAAQ,CAAC,CAAC;QACpF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxF,CAAC;IACL,CAAC,MAAM,CAAC;QACJ,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;YACnC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;QAClG,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;QACpH,CAAC;IACL,CAAC;IAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACjC,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG,SAAU,MAA4B;IAC3E,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5F,CAAC,CAAC;AAEF,6DAA6D;gKAC7D,aAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG,SAAU,MAAkB,EAAE,QAAgB,EAAE,IAAY;IACrG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC1G,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAU,eAAiC,EAAE,SAAiB,EAAE,KAAa;IACjH,MAAM,OAAO,GAAI,eAAwC,CAAC,OAAQ,CAAC;IAEnE,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAE1E,IAAI,eAAe,KAAK,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;AACL,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.multiview.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.multiview.ts"], "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { Engine } from \"../../Engines/engine\";\r\nimport { Scene } from \"../../scene\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\nimport { Matrix, TmpVectors } from \"../../Maths/math.vector\";\r\nimport { UniformBuffer } from \"../../Materials/uniformBuffer\";\r\nimport { MultiviewRenderTarget } from \"../../Materials/Textures/MultiviewRenderTarget\";\r\nimport { Frustum } from \"../../Maths/math.frustum\";\r\nimport type { WebGLRenderTargetWrapper } from \"../WebGL/webGLRenderTargetWrapper\";\r\nimport type { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { AbstractEngine } from \"../abstractEngine\";\r\n\r\ndeclare module \"../../Engines/engine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Engine {\r\n        /**\r\n         * Creates a new multiview render target\r\n         * @param width defines the width of the texture\r\n         * @param height defines the height of the texture\r\n         * @returns the created multiview render target wrapper\r\n         */\r\n        createMultiviewRenderTargetTexture(width: number, height: number, colorTexture?: WebGLTexture, depthStencilTexture?: WebGLTexture): RenderTargetWrapper;\r\n\r\n        /**\r\n         * Binds a multiview render target wrapper to be drawn to\r\n         * @param multiviewTexture render target wrapper to bind\r\n         */\r\n        bindMultiviewFramebuffer(multiviewTexture: RenderTargetWrapper): void;\r\n\r\n        /**\r\n         * Binds a Space Warp render target wrapper to be drawn to\r\n         * @param spaceWarpTexture render target wrapper to bind\r\n         */\r\n        bindSpaceWarpFramebuffer(spaceWarpTexture: RenderTargetWrapper): void;\r\n    }\r\n}\r\n\r\nEngine.prototype.createMultiviewRenderTargetTexture = function (width: number, height: number, colorTexture?: WebGLTexture, depthStencilTexture?: WebGLTexture) {\r\n    const gl = this._gl;\r\n\r\n    if (!this.getCaps().multiview) {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Multiview is not supported\";\r\n    }\r\n\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(false, false, { width, height }) as WebGLRenderTargetWrapper;\r\n\r\n    rtWrapper._framebuffer = gl.createFramebuffer();\r\n\r\n    const internalTexture = new InternalTexture(this, InternalTextureSource.Unknown, true);\r\n    internalTexture.width = width;\r\n    internalTexture.height = height;\r\n    internalTexture.isMultiview = true;\r\n\r\n    if (!colorTexture) {\r\n        colorTexture = gl.createTexture();\r\n        gl.bindTexture(gl.TEXTURE_2D_ARRAY, colorTexture);\r\n        (gl as any).texStorage3D(gl.TEXTURE_2D_ARRAY, 1, gl.RGBA8, width, height, 2);\r\n    }\r\n\r\n    rtWrapper._colorTextureArray = colorTexture;\r\n\r\n    if (!depthStencilTexture) {\r\n        depthStencilTexture = gl.createTexture();\r\n        gl.bindTexture(gl.TEXTURE_2D_ARRAY, depthStencilTexture);\r\n        (gl as any).texStorage3D(gl.TEXTURE_2D_ARRAY, 1, (gl as any).DEPTH24_STENCIL8, width, height, 2);\r\n    }\r\n\r\n    rtWrapper._depthStencilTextureArray = depthStencilTexture;\r\n\r\n    internalTexture.isReady = true;\r\n\r\n    rtWrapper.setTextures(internalTexture);\r\n    rtWrapper._depthStencilTexture = internalTexture;\r\n\r\n    return rtWrapper;\r\n};\r\n\r\nEngine.prototype.bindMultiviewFramebuffer = function (_multiviewTexture: RenderTargetWrapper) {\r\n    const multiviewTexture = _multiviewTexture as WebGLRenderTargetWrapper;\r\n\r\n    const gl: any = this._gl;\r\n    const ext = this.getCaps().oculusMultiview || this.getCaps().multiview;\r\n\r\n    this.bindFramebuffer(multiviewTexture, undefined, undefined, undefined, true);\r\n    gl.bindFramebuffer(gl.DRAW_FRAMEBUFFER, multiviewTexture._framebuffer);\r\n    if (multiviewTexture._colorTextureArray && multiviewTexture._depthStencilTextureArray) {\r\n        if (this.getCaps().oculusMultiview) {\r\n            ext.framebufferTextureMultisampleMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.COLOR_ATTACHMENT0, multiviewTexture._colorTextureArray, 0, multiviewTexture.samples, 0, 2);\r\n            ext.framebufferTextureMultisampleMultiviewOVR(\r\n                gl.DRAW_FRAMEBUFFER,\r\n                gl.DEPTH_STENCIL_ATTACHMENT,\r\n                multiviewTexture._depthStencilTextureArray,\r\n                0,\r\n                multiviewTexture.samples,\r\n                0,\r\n                2\r\n            );\r\n        } else {\r\n            ext.framebufferTextureMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.COLOR_ATTACHMENT0, multiviewTexture._colorTextureArray, 0, 0, 2);\r\n            ext.framebufferTextureMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.DEPTH_STENCIL_ATTACHMENT, multiviewTexture._depthStencilTextureArray, 0, 0, 2);\r\n        }\r\n    } else {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Invalid multiview frame buffer\";\r\n    }\r\n};\r\n\r\nEngine.prototype.bindSpaceWarpFramebuffer = function (_spaceWarpTexture: RenderTargetWrapper) {\r\n    const spaceWarpTexture = _spaceWarpTexture as WebGLRenderTargetWrapper;\r\n\r\n    const gl: any = this._gl;\r\n    const ext = this.getCaps().oculusMultiview || this.getCaps().multiview;\r\n\r\n    this.bindFramebuffer(spaceWarpTexture, undefined, undefined, undefined, true);\r\n    gl.bindFramebuffer(gl.DRAW_FRAMEBUFFER, spaceWarpTexture._framebuffer);\r\n    if (spaceWarpTexture._colorTextureArray && spaceWarpTexture._depthStencilTextureArray) {\r\n        ext.framebufferTextureMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.COLOR_ATTACHMENT0, spaceWarpTexture._colorTextureArray, 0, 0, 2);\r\n        ext.framebufferTextureMultiviewOVR(gl.DRAW_FRAMEBUFFER, gl.DEPTH_ATTACHMENT, spaceWarpTexture._depthStencilTextureArray, 0, 0, 2);\r\n    } else {\r\n        throw new Error(\"Invalid Space Warp framebuffer\");\r\n    }\r\n};\r\n\r\ndeclare module \"../../Cameras/camera\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Camera {\r\n        /**\r\n         * @internal\r\n         * For cameras that cannot use multiview images to display directly. (e.g. webVR camera will render to multiview texture, then copy to each eye texture and go from there)\r\n         */\r\n        _useMultiviewToSingleView: boolean;\r\n        /**\r\n         * @internal\r\n         * For cameras that cannot use multiview images to display directly. (e.g. webVR camera will render to multiview texture, then copy to each eye texture and go from there)\r\n         */\r\n        _multiviewTexture: Nullable<RenderTargetTexture>;\r\n\r\n        /**\r\n         * @internal\r\n         * For WebXR cameras that are rendering to multiview texture arrays.\r\n         */\r\n        _renderingMultiview: boolean;\r\n\r\n        /**\r\n         * @internal\r\n         * ensures the multiview texture of the camera exists and has the specified width/height\r\n         * @param width height to set on the multiview texture\r\n         * @param height width to set on the multiview texture\r\n         */\r\n        _resizeOrCreateMultiviewTexture(width: number, height: number): void;\r\n    }\r\n}\r\n\r\nCamera.prototype._useMultiviewToSingleView = false;\r\n\r\nCamera.prototype._multiviewTexture = null;\r\n\r\nCamera.prototype._resizeOrCreateMultiviewTexture = function (width: number, height: number) {\r\n    if (!this._multiviewTexture) {\r\n        this._multiviewTexture = new MultiviewRenderTarget(this.getScene(), { width: width, height: height });\r\n    } else if (this._multiviewTexture.getRenderWidth() != width || this._multiviewTexture.getRenderHeight() != height) {\r\n        this._multiviewTexture.dispose();\r\n        this._multiviewTexture = new MultiviewRenderTarget(this.getScene(), { width: width, height: height });\r\n    }\r\n};\r\n\r\ndeclare module \"../../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /** @internal */\r\n        _transformMatrixR: Matrix;\r\n        /** @internal */\r\n        _multiviewSceneUbo: Nullable<UniformBuffer>;\r\n        /** @internal */\r\n        _createMultiviewUbo(): void;\r\n        /** @internal */\r\n        _updateMultiviewUbo(viewR?: Matrix, projectionR?: Matrix): void;\r\n        /** @internal */\r\n        _renderMultiviewToSingleView(camera: Camera): void;\r\n    }\r\n}\r\n\r\nfunction CreateMultiviewUbo(engine: AbstractEngine, name?: string) {\r\n    const ubo = new UniformBuffer(engine, undefined, true, name);\r\n    ubo.addUniform(\"viewProjection\", 16);\r\n    ubo.addUniform(\"viewProjectionR\", 16);\r\n    ubo.addUniform(\"view\", 16);\r\n    ubo.addUniform(\"projection\", 16);\r\n    ubo.addUniform(\"vEyePosition\", 4);\r\n    return ubo;\r\n}\r\n\r\nconst CurrentCreateSceneUniformBuffer = Scene.prototype.createSceneUniformBuffer;\r\n\r\nScene.prototype._transformMatrixR = Matrix.Zero();\r\nScene.prototype._multiviewSceneUbo = null;\r\nScene.prototype._createMultiviewUbo = function () {\r\n    this._multiviewSceneUbo = CreateMultiviewUbo(this.getEngine(), \"scene_multiview\");\r\n};\r\nScene.prototype.createSceneUniformBuffer = function (name?: string): UniformBuffer {\r\n    if (this._multiviewSceneUbo) {\r\n        return CreateMultiviewUbo(this.getEngine(), name);\r\n    }\r\n    return CurrentCreateSceneUniformBuffer.bind(this)(name);\r\n};\r\nScene.prototype._updateMultiviewUbo = function (viewR?: Matrix, projectionR?: Matrix) {\r\n    if (viewR && projectionR) {\r\n        viewR.multiplyToRef(projectionR, this._transformMatrixR);\r\n    }\r\n\r\n    if (viewR && projectionR) {\r\n        viewR.multiplyToRef(projectionR, TmpVectors.Matrix[0]);\r\n        Frustum.GetRightPlaneToRef(TmpVectors.Matrix[0], this._frustumPlanes[3]); // Replace right plane by second camera right plane\r\n    }\r\n\r\n    if (this._multiviewSceneUbo) {\r\n        this._multiviewSceneUbo.updateMatrix(\"viewProjection\", this.getTransformMatrix());\r\n        this._multiviewSceneUbo.updateMatrix(\"viewProjectionR\", this._transformMatrixR);\r\n        this._multiviewSceneUbo.updateMatrix(\"view\", this._viewMatrix);\r\n        this._multiviewSceneUbo.updateMatrix(\"projection\", this._projectionMatrix);\r\n    }\r\n};\r\nScene.prototype._renderMultiviewToSingleView = function (camera: Camera) {\r\n    // Multiview is only able to be displayed directly for API's such as webXR\r\n    // This displays a multiview image by rendering to the multiview image and then\r\n    // copying the result into the sub cameras instead of rendering them and proceeding as normal from there\r\n\r\n    // Render to a multiview texture\r\n    camera._resizeOrCreateMultiviewTexture(\r\n        camera._rigPostProcess && camera._rigPostProcess && camera._rigPostProcess.width > 0 ? camera._rigPostProcess.width : this.getEngine().getRenderWidth(true),\r\n        camera._rigPostProcess && camera._rigPostProcess && camera._rigPostProcess.height > 0 ? camera._rigPostProcess.height : this.getEngine().getRenderHeight(true)\r\n    );\r\n    if (!this._multiviewSceneUbo) {\r\n        this._createMultiviewUbo();\r\n    }\r\n    camera.outputRenderTarget = camera._multiviewTexture;\r\n    this._renderForCamera(camera);\r\n    camera.outputRenderTarget = null;\r\n\r\n    // Consume the multiview texture through a shader for each eye\r\n    for (let index = 0; index < camera._rigCameras.length; index++) {\r\n        const engine = this.getEngine();\r\n        this._activeCamera = camera._rigCameras[index];\r\n        engine.setViewport(this._activeCamera.viewport);\r\n        if (this.postProcessManager) {\r\n            this.postProcessManager._prepareFrame();\r\n            this.postProcessManager._finalizeFrame(this._activeCamera.isIntermediate);\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AACpC,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAGlG,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAC7D,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAE,qBAAqB,EAAE,MAAM,gDAAgD,CAAC;AACvF,OAAO,EAAE,OAAO,EAAE,MAAM,0BAA0B,CAAC;;;;;;;;;4JA8BnD,SAAM,CAAC,SAAS,CAAC,kCAAkC,GAAG,SAAU,KAAa,EAAE,MAAc,EAAE,YAA2B,EAAE,mBAAkC;IAC1J,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC;QAC5B,4CAA4C;QAC5C,MAAM,4BAA4B,CAAC;IACvC,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,KAAK,EAAE;QAAE,KAAK;QAAE,MAAM;IAAA,CAAE,CAA6B,CAAC;IAEvH,SAAS,CAAC,YAAY,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAEhD,MAAM,eAAe,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAA,EAAA,iCAAA,KAAiC,IAAI,CAAC,CAAC;IACvF,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;IAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;IAChC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC;IAEnC,IAAI,CAAC,YAAY,EAAE,CAAC;QAChB,YAAY,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;QAClC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC;QACjD,EAAU,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACjF,CAAC;IAED,SAAS,CAAC,kBAAkB,GAAG,YAAY,CAAC;IAE5C,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACvB,mBAAmB,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;QACzC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;QACxD,EAAU,CAAC,YAAY,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,EAAG,EAAU,CAAC,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IACrG,CAAC;IAED,SAAS,CAAC,yBAAyB,GAAG,mBAAmB,CAAC;IAE1D,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;IAE/B,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;IACvC,SAAS,CAAC,oBAAoB,GAAG,eAAe,CAAC;IAEjD,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,wBAAwB,GAAG,SAAU,iBAAsC;IACxF,MAAM,gBAAgB,GAAG,iBAA6C,CAAC;IAEvE,MAAM,EAAE,GAAQ,IAAI,CAAC,GAAG,CAAC;IACzB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC;IAEvE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC9E,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,YAAY,CAAC,CAAC;IACvE,IAAI,gBAAgB,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;QACpF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC;YACjC,GAAG,CAAC,yCAAyC,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACjK,GAAG,CAAC,yCAAyC,CACzC,EAAE,CAAC,gBAAgB,EACnB,EAAE,CAAC,wBAAwB,EAC3B,gBAAgB,CAAC,yBAAyB,EAC1C,CAAC,EACD,gBAAgB,CAAC,OAAO,EACxB,CAAC,EACD,CAAC,CACJ,CAAC;QACN,CAAC,MAAM,CAAC;YACJ,GAAG,CAAC,8BAA8B,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5H,GAAG,CAAC,8BAA8B,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,wBAAwB,EAAE,gBAAgB,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9I,CAAC;IACL,CAAC,MAAM,CAAC;QACJ,4CAA4C;QAC5C,MAAM,gCAAgC,CAAC;IAC3C,CAAC;AACL,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,wBAAwB,GAAG,SAAU,iBAAsC;IACxF,MAAM,gBAAgB,GAAG,iBAA6C,CAAC;IAEvE,MAAM,EAAE,GAAQ,IAAI,CAAC,GAAG,CAAC;IACzB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC;IAEvE,IAAI,CAAC,eAAe,CAAC,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC9E,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,YAAY,CAAC,CAAC;IACvE,IAAI,gBAAgB,CAAC,kBAAkB,IAAI,gBAAgB,CAAC,yBAAyB,EAAE,CAAC;QACpF,GAAG,CAAC,8BAA8B,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5H,GAAG,CAAC,8BAA8B,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,yBAAyB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACtI,CAAC,MAAM,CAAC;QACJ,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtD,CAAC;AACL,CAAC,CAAC;4JAgCF,SAAM,CAAC,SAAS,CAAC,yBAAyB,GAAG,KAAK,CAAC;4JAEnD,SAAM,CAAC,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC;4JAE1C,SAAM,CAAC,SAAS,CAAC,+BAA+B,GAAG,SAAU,KAAa,EAAE,MAAc;IACtF,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,6LAAI,wBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YAAE,KAAK,EAAE,KAAK;YAAE,MAAM,EAAE,MAAM;QAAA,CAAE,CAAC,CAAC;IAC1G,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,IAAI,KAAK,IAAI,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,IAAI,MAAM,EAAE,CAAC;QAChH,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,6LAAI,wBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;YAAE,KAAK,EAAE,KAAK;YAAE,MAAM,EAAE,MAAM;QAAA,CAAE,CAAC,CAAC;IAC1G,CAAC;AACL,CAAC,CAAC;AAkBF,SAAS,kBAAkB,CAAC,MAAsB,EAAE,IAAa;IAC7D,MAAM,GAAG,GAAG,yKAAI,gBAAa,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC7D,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;IACrC,GAAG,CAAC,UAAU,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IACtC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAC3B,GAAG,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;IACjC,GAAG,CAAC,UAAU,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;IAClC,OAAO,GAAG,CAAC;AACf,CAAC;AAED,MAAM,+BAA+B,mJAAG,QAAK,CAAC,SAAS,CAAC,wBAAwB,CAAC;gJAEjF,QAAK,CAAC,SAAS,CAAC,iBAAiB,qKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;gJAClD,QAAK,CAAC,SAAS,CAAC,kBAAkB,GAAG,IAAI,CAAC;gJAC1C,QAAK,CAAC,SAAS,CAAC,mBAAmB,GAAG;IAClC,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,iBAAiB,CAAC,CAAC;AACtF,CAAC,CAAC;gJACF,QAAK,CAAC,SAAS,CAAC,wBAAwB,GAAG,SAAU,IAAa;IAC9D,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,OAAO,kBAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IACD,OAAO,+BAA+B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5D,CAAC,CAAC;gJACF,QAAK,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAU,KAAc,EAAE,WAAoB;IAChF,IAAI,KAAK,IAAI,WAAW,EAAE,CAAC;QACvB,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC7D,CAAC;IAED,IAAI,KAAK,IAAI,WAAW,EAAE,CAAC;QACvB,KAAK,CAAC,aAAa,CAAC,WAAW,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;2KACvD,UAAO,CAAC,kBAAkB,mKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mDAAmD;IACjI,CAAC;IAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAChF,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/D,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC/E,CAAC;AACL,CAAC,CAAC;gJACF,QAAK,CAAC,SAAS,CAAC,4BAA4B,GAAG,SAAU,MAAc;IACnE,0EAA0E;IAC1E,+EAA+E;IAC/E,wGAAwG;IAExG,gCAAgC;IAChC,MAAM,CAAC,+BAA+B,CAClC,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAC3J,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CACjK,CAAC;IACF,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IACD,MAAM,CAAC,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;IACrD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC9B,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC;IAEjC,8DAA8D;IAC9D,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC/C,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;YACxC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC;AACL,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.dynamicTexture.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.dynamicTexture.ts"], "sourcesContent": ["import { GetExponentOfTwo } from \"core/Misc/tools.functions\";\r\nimport { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport type { ImageSource, Nullable } from \"../../types\";\r\nimport type { ICanvas } from \"../ICanvas\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a dynamic texture\r\n         * @param width defines the width of the texture\r\n         * @param height defines the height of the texture\r\n         * @param generateMipMaps defines if the engine should generate the mip levels\r\n         * @param samplingMode defines the required sampling mode (Texture.NEAREST_SAMPLINGMODE by default)\r\n         * @returns the dynamic texture inside an InternalTexture\r\n         */\r\n        createDynamicTexture(width: number, height: number, generateMipMaps: boolean, samplingMode: number): InternalTexture;\r\n\r\n        /**\r\n         * Update the content of a dynamic texture\r\n         * @param texture defines the texture to update\r\n         * @param source defines the source containing the data\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param premulAlpha defines if alpha is stored as premultiplied\r\n         * @param format defines the format of the data\r\n         * @param forceBindTexture if the texture should be forced to be bound eg. after a graphics context loss (Default: false)\r\n         * @param allowGPUOptimization true to allow some specific GPU optimizations (subject to engine feature \"allowGPUOptimizationsForGUI\" being true)\r\n         */\r\n        updateDynamicTexture(\r\n            texture: Nullable<InternalTexture>,\r\n            source: ImageSource | ICanvas,\r\n            invertY?: boolean,\r\n            premulAlpha?: boolean,\r\n            format?: number,\r\n            forceBindTexture?: boolean,\r\n            allowGPUOptimization?: boolean\r\n        ): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createDynamicTexture = function (width: number, height: number, generateMipMaps: boolean, samplingMode: number): InternalTexture {\r\n    const texture = new InternalTexture(this, InternalTextureSource.Dynamic);\r\n    texture.baseWidth = width;\r\n    texture.baseHeight = height;\r\n\r\n    if (generateMipMaps) {\r\n        width = this.needPOTTextures ? GetExponentOfTwo(width, this._caps.maxTextureSize) : width;\r\n        height = this.needPOTTextures ? GetExponentOfTwo(height, this._caps.maxTextureSize) : height;\r\n    }\r\n\r\n    //  this.resetTextureCache();\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.isReady = false;\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n\r\n    this.updateTextureSamplingMode(samplingMode, texture);\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    return texture;\r\n};\r\n\r\nThinEngine.prototype.updateDynamicTexture = function (\r\n    texture: Nullable<InternalTexture>,\r\n    source: ImageSource,\r\n    invertY?: boolean,\r\n    premulAlpha: boolean = false,\r\n    format?: number,\r\n    forceBindTexture: boolean = false,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    allowGPUOptimization: boolean = false\r\n): void {\r\n    if (!texture) {\r\n        return;\r\n    }\r\n\r\n    const gl = this._gl;\r\n    const target = gl.TEXTURE_2D;\r\n\r\n    const wasPreviouslyBound = this._bindTextureDirectly(target, texture, true, forceBindTexture);\r\n\r\n    this._unpackFlipY(invertY === undefined ? texture.invertY : invertY);\r\n\r\n    if (premulAlpha) {\r\n        gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, 1);\r\n    }\r\n\r\n    const textureType = this._getWebGLTextureType(texture.type);\r\n    const glformat = this._getInternalFormat(format ? format : texture.format);\r\n    const internalFormat = this._getRGBABufferInternalSizedFormat(texture.type, glformat);\r\n\r\n    gl.texImage2D(target, 0, internalFormat, glformat, textureType, source as TexImageSource);\r\n\r\n    if (texture.generateMipMaps) {\r\n        gl.generateMipmap(target);\r\n    }\r\n\r\n    if (!wasPreviouslyBound) {\r\n        this._bindTextureDirectly(target, null);\r\n    }\r\n\r\n    if (premulAlpha) {\r\n        gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, 0);\r\n    }\r\n\r\n    if (format) {\r\n        texture.format = format;\r\n    }\r\n\r\n    texture._dynamicTextureSource = source;\r\n    texture._premulAlpha = premulAlpha;\r\n    texture.invertY = invertY || false;\r\n    texture.isReady = true;\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,gBAAgB,EAAE,sCAAkC;AAC7D,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;;;;AAuClG,6KAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,SAAU,KAAa,EAAE,MAAc,EAAE,eAAwB,EAAE,YAAoB;IAC/H,MAAM,OAAO,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAA,EAAA,iCAAA,GAAgC,CAAC;IACzE,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;IAE5B,IAAI,eAAe,EAAE,CAAC;QAClB,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,4LAAA,AAAgB,EAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC1F,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,KAAC,wLAAA,AAAgB,EAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACjG,CAAC;IAED,6BAA6B;IAC7B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;IACxB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IAEpC,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAEtD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,SACxC,OAAkC,EAClC,MAAmB,EACnB,OAAiB;sBACjB,iEAAuB,KAAK,EAC5B,MAAe,oEACf,iEAA4B,KAAK,EACjC,6DAA6D;2BAC7D,iEAAgC,KAAK;IAErC,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,OAAO;IACX,CAAC;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC;IAE7B,MAAM,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;IAE9F,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAErE,IAAI,WAAW,EAAE,CAAC;QACd,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAEtF,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAwB,CAAC,CAAC;IAE1F,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC1B,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QACd,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACT,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IAC5B,CAAC;IAED,OAAO,CAAC,qBAAqB,GAAG,MAAM,CAAC;IACvC,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACnC,OAAO,CAAC,OAAO,GAAG,OAAO,IAAI,KAAK,CAAC;IACnC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1344, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.debugging.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.debugging.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { AbstractEngine } from \"../../Engines/abstractEngine\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /** @internal */\r\n        _debugPushGroup(groupName: string, targetObject?: number): void;\r\n\r\n        /** @internal */\r\n        _debugPopGroup(targetObject?: number): void;\r\n\r\n        /** @internal */\r\n        _debugInsertMarker(text: string, targetObject?: number): void;\r\n\r\n        /** @internal */\r\n        _debugFlushPendingCommands(): void;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype._debugPushGroup = function (groupName: string, targetObject?: number): void {};\r\n\r\nAbstractEngine.prototype._debugPopGroup = function (targetObject?: number): void {};\r\n\r\nAbstractEngine.prototype._debugInsertMarker = function (text: string, targetObject?: number): void {};\r\n\r\nAbstractEngine.prototype._debugFlushPendingCommands = function (): void {};\r\n"], "names": [], "mappings": "AAAA,oDAAA,EAAsD;AACtD,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;;oKAmB9D,iBAAc,CAAC,SAAS,CAAC,eAAe,GAAG,SAAU,SAAiB,EAAE,YAAqB,GAAS,CAAC,CAAC;oKAExG,iBAAc,CAAC,SAAS,CAAC,cAAc,GAAG,SAAU,YAAqB,GAAS,CAAC,CAAC;oKAEpF,iBAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAU,IAAY,EAAE,YAAqB,GAAS,CAAC,CAAC;oKAEtG,iBAAc,CAAC,SAAS,CAAC,0BAA0B,GAAG,YAAmB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.query.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.query.ts"], "sourcesContent": ["import type { Nullable, int } from \"../../types\";\r\nimport { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { _TimeToken } from \"../../Instrumentation/timeToken\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { AbstractEngine } from \"../abstractEngine\";\r\nimport type { OcclusionQuery } from \"../AbstractEngine/abstractEngine.query\";\r\n\r\nimport \"../AbstractEngine/abstractEngine.timeQuery\";\r\nimport \"../AbstractEngine/abstractEngine.query\";\r\n\r\ndeclare module \"../../Engines/thinEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface ThinEngine {\r\n        /**\r\n         * @internal\r\n         */\r\n        _captureGPUFrameTime: boolean;\r\n\r\n        /**\r\n         * Starts a time query (used to measure time spent by the GPU on a specific frame)\r\n         * Please note that only one query can be issued at a time\r\n         * @returns a time token used to track the time span\r\n         */\r\n        startTimeQuery(): Nullable<_TimeToken>;\r\n\r\n        /**\r\n         * Ends a time query\r\n         * @param token defines the token used to measure the time span\r\n         * @returns the time spent (in ns)\r\n         */\r\n        endTimeQuery(token: _TimeToken): int;\r\n\r\n        /** @internal */\r\n        _currentNonTimestampToken: Nullable<_TimeToken>;\r\n        /** @internal */\r\n        _gpuFrameTimeToken: Nullable<_TimeToken>;\r\n        /** @internal */\r\n        _onBeginFrameObserver: Nullable<Observer<AbstractEngine>>;\r\n        /** @internal */\r\n        _onEndFrameObserver: Nullable<Observer<AbstractEngine>>;\r\n\r\n        /** @internal */\r\n        _createTimeQuery(): Nullable<WebGLQuery>;\r\n\r\n        /** @internal */\r\n        _deleteTimeQuery(query: WebGLQuery): void;\r\n\r\n        /** @internal */\r\n        _getGlAlgorithmType(algorithmType: number): number;\r\n\r\n        /** @internal */\r\n        _getTimeQueryResult(query: WebGLQuery): any;\r\n\r\n        /** @internal */\r\n        _getTimeQueryAvailability(query: WebGLQuery): any;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createQuery = function (): OcclusionQuery {\r\n    const query = this._gl.createQuery();\r\n    if (!query) {\r\n        throw new Error(\"Unable to create Occlusion Query\");\r\n    }\r\n    return query;\r\n};\r\n\r\nThinEngine.prototype.deleteQuery = function (query: OcclusionQuery): ThinEngine {\r\n    this._gl.deleteQuery(query);\r\n\r\n    return this;\r\n};\r\n\r\nThinEngine.prototype.isQueryResultAvailable = function (query: OcclusionQuery): boolean {\r\n    return this._gl.getQueryParameter(query, this._gl.QUERY_RESULT_AVAILABLE) as boolean;\r\n};\r\n\r\nThinEngine.prototype.getQueryResult = function (query: OcclusionQuery): number {\r\n    return this._gl.getQueryParameter(query, this._gl.QUERY_RESULT) as number;\r\n};\r\n\r\nThinEngine.prototype.beginOcclusionQuery = function (algorithmType: number, query: OcclusionQuery): boolean {\r\n    const glAlgorithm = this._getGlAlgorithmType(algorithmType);\r\n    this._gl.beginQuery(glAlgorithm, query);\r\n\r\n    return true;\r\n};\r\n\r\nThinEngine.prototype.endOcclusionQuery = function (algorithmType: number): ThinEngine {\r\n    const glAlgorithm = this._getGlAlgorithmType(algorithmType);\r\n    this._gl.endQuery(glAlgorithm);\r\n\r\n    return this;\r\n};\r\n\r\nThinEngine.prototype._createTimeQuery = function (): Nullable<WebGLQuery> {\r\n    const timerQuery = <EXT_disjoint_timer_query>this.getCaps().timerQuery;\r\n\r\n    if (timerQuery.createQueryEXT) {\r\n        return timerQuery.createQueryEXT();\r\n    }\r\n\r\n    return this.createQuery();\r\n};\r\n\r\nThinEngine.prototype._deleteTimeQuery = function (query: WebGLQuery): void {\r\n    const timerQuery = <EXT_disjoint_timer_query>this.getCaps().timerQuery;\r\n\r\n    if (timerQuery.deleteQueryEXT) {\r\n        timerQuery.deleteQueryEXT(query);\r\n        return;\r\n    }\r\n\r\n    this.deleteQuery(query);\r\n};\r\n\r\nThinEngine.prototype._getTimeQueryResult = function (query: WebGLQuery): any {\r\n    const timerQuery = <EXT_disjoint_timer_query>this.getCaps().timerQuery;\r\n\r\n    if (timerQuery.getQueryObjectEXT) {\r\n        return timerQuery.getQueryObjectEXT(query, timerQuery.QUERY_RESULT_EXT);\r\n    }\r\n    return this.getQueryResult(query);\r\n};\r\n\r\nThinEngine.prototype._getTimeQueryAvailability = function (query: WebGLQuery): any {\r\n    const timerQuery = <EXT_disjoint_timer_query>this.getCaps().timerQuery;\r\n\r\n    if (timerQuery.getQueryObjectEXT) {\r\n        return timerQuery.getQueryObjectEXT(query, timerQuery.QUERY_RESULT_AVAILABLE_EXT);\r\n    }\r\n    return this.isQueryResultAvailable(query);\r\n};\r\n\r\nThinEngine.prototype.startTimeQuery = function (): Nullable<_TimeToken> {\r\n    const caps = this.getCaps();\r\n    const timerQuery = caps.timerQuery;\r\n    if (!timerQuery) {\r\n        return null;\r\n    }\r\n\r\n    const token = new _TimeToken();\r\n    this._gl.getParameter(timerQuery.GPU_DISJOINT_EXT);\r\n    if (caps.canUseTimestampForTimerQuery) {\r\n        token._startTimeQuery = this._createTimeQuery();\r\n\r\n        if (token._startTimeQuery) {\r\n            timerQuery.queryCounterEXT(token._startTimeQuery, timerQuery.TIMESTAMP_EXT);\r\n        }\r\n    } else {\r\n        if (this._currentNonTimestampToken) {\r\n            return this._currentNonTimestampToken;\r\n        }\r\n\r\n        token._timeElapsedQuery = this._createTimeQuery();\r\n        if (token._timeElapsedQuery) {\r\n            if (timerQuery.beginQueryEXT) {\r\n                timerQuery.beginQueryEXT(timerQuery.TIME_ELAPSED_EXT, token._timeElapsedQuery);\r\n            } else {\r\n                this._gl.beginQuery(timerQuery.TIME_ELAPSED_EXT, token._timeElapsedQuery);\r\n            }\r\n        }\r\n\r\n        this._currentNonTimestampToken = token;\r\n    }\r\n    return token;\r\n};\r\n\r\nThinEngine.prototype.endTimeQuery = function (token: _TimeToken): int {\r\n    const caps = this.getCaps();\r\n    const timerQuery = caps.timerQuery;\r\n    if (!timerQuery || !token) {\r\n        return -1;\r\n    }\r\n\r\n    if (caps.canUseTimestampForTimerQuery) {\r\n        if (!token._startTimeQuery) {\r\n            return -1;\r\n        }\r\n        if (!token._endTimeQuery) {\r\n            token._endTimeQuery = this._createTimeQuery();\r\n            if (token._endTimeQuery) {\r\n                timerQuery.queryCounterEXT(token._endTimeQuery, timerQuery.TIMESTAMP_EXT);\r\n            }\r\n        }\r\n    } else if (!token._timeElapsedQueryEnded) {\r\n        if (!token._timeElapsedQuery) {\r\n            return -1;\r\n        }\r\n        if (timerQuery.endQueryEXT) {\r\n            timerQuery.endQueryEXT(timerQuery.TIME_ELAPSED_EXT);\r\n        } else {\r\n            this._gl.endQuery(timerQuery.TIME_ELAPSED_EXT);\r\n            this._currentNonTimestampToken = null;\r\n        }\r\n        token._timeElapsedQueryEnded = true;\r\n    }\r\n\r\n    const disjoint = this._gl.getParameter(timerQuery.GPU_DISJOINT_EXT);\r\n    let available: boolean = false;\r\n    if (token._endTimeQuery) {\r\n        available = this._getTimeQueryAvailability(token._endTimeQuery);\r\n    } else if (token._timeElapsedQuery) {\r\n        available = this._getTimeQueryAvailability(token._timeElapsedQuery);\r\n    }\r\n\r\n    if (available && !disjoint) {\r\n        let result = 0;\r\n        if (caps.canUseTimestampForTimerQuery) {\r\n            if (!token._startTimeQuery || !token._endTimeQuery) {\r\n                return -1;\r\n            }\r\n            const timeStart = this._getTimeQueryResult(token._startTimeQuery);\r\n            const timeEnd = this._getTimeQueryResult(token._endTimeQuery);\r\n\r\n            result = timeEnd - timeStart;\r\n            this._deleteTimeQuery(token._startTimeQuery);\r\n            this._deleteTimeQuery(token._endTimeQuery);\r\n            token._startTimeQuery = null;\r\n            token._endTimeQuery = null;\r\n        } else {\r\n            if (!token._timeElapsedQuery) {\r\n                return -1;\r\n            }\r\n\r\n            result = this._getTimeQueryResult(token._timeElapsedQuery);\r\n            this._deleteTimeQuery(token._timeElapsedQuery);\r\n            token._timeElapsedQuery = null;\r\n            token._timeElapsedQueryEnded = false;\r\n        }\r\n        return result;\r\n    }\r\n\r\n    return -1;\r\n};\r\n\r\nThinEngine.prototype.captureGPUFrameTime = function (value: boolean) {\r\n    if (value === this._captureGPUFrameTime) {\r\n        return;\r\n    }\r\n\r\n    this._captureGPUFrameTime = value;\r\n\r\n    if (value) {\r\n        const gpuFrameTime = this.getGPUFrameTimeCounter();\r\n\r\n        this._onBeginFrameObserver = this.onBeginFrameObservable.add(() => {\r\n            if (!this._gpuFrameTimeToken) {\r\n                this._gpuFrameTimeToken = this.startTimeQuery();\r\n            }\r\n        });\r\n\r\n        this._onEndFrameObserver = this.onEndFrameObservable.add(() => {\r\n            if (!this._gpuFrameTimeToken) {\r\n                return;\r\n            }\r\n            const time = this.endTimeQuery(this._gpuFrameTimeToken);\r\n\r\n            if (time > -1) {\r\n                this._gpuFrameTimeToken = null;\r\n                gpuFrameTime.fetchNewFrame();\r\n                gpuFrameTime.addCount(time, true);\r\n            }\r\n        });\r\n    } else {\r\n        this.onBeginFrameObservable.remove(this._onBeginFrameObserver);\r\n        this._onBeginFrameObserver = null;\r\n        this.onEndFrameObservable.remove(this._onEndFrameObserver);\r\n        this._onEndFrameObserver = null;\r\n    }\r\n};\r\n\r\nThinEngine.prototype._getGlAlgorithmType = function (algorithmType: number): number {\r\n    return algorithmType === AbstractMesh.OCCLUSION_ALGORITHM_TYPE_CONSERVATIVE ? this._gl.ANY_SAMPLES_PASSED_CONSERVATIVE : this._gl.ANY_SAMPLES_PASSED;\r\n};\r\n"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,iCAAiC,CAAC;AAK7D,OAAO,4CAA4C,CAAC;AACpD,OAAO,wCAAwC,CAAC;;;;;;gKAkDhD,aAAU,CAAC,SAAS,CAAC,WAAW,GAAG;IAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;IACrC,IAAI,CAAC,KAAK,EAAE,CAAC;QACT,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACxD,CAAC;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,WAAW,GAAG,SAAU,KAAqB;IAC9D,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAE5B,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,SAAU,KAAqB;IACzE,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAY,CAAC;AACzF,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,cAAc,GAAG,SAAU,KAAqB;IACjE,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAW,CAAC;AAC9E,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAU,aAAqB,EAAE,KAAqB;IAC7F,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IAC5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAExC,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG,SAAU,aAAqB;IACpE,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IAC5D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAE/B,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG;IACpC,MAAM,UAAU,GAA6B,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IAEvE,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;QAC5B,OAAO,UAAU,CAAC,cAAc,EAAE,CAAC;IACvC,CAAC;IAED,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;AAC9B,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,SAAU,KAAiB;IAC/D,MAAM,UAAU,GAA6B,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IAEvE,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;QAC5B,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QACjC,OAAO;IACX,CAAC;IAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAU,KAAiB;IAClE,MAAM,UAAU,GAA6B,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IAEvE,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAC/B,OAAO,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC5E,CAAC;IACD,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACtC,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,SAAU,KAAiB;IACxE,MAAM,UAAU,GAA6B,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IAEvE,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAC/B,OAAO,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,0BAA0B,CAAC,CAAC;IACtF,CAAC;IACD,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AAC9C,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,cAAc,GAAG;IAClC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,IAAI,CAAC,UAAU,EAAE,CAAC;QACd,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,KAAK,GAAG,2KAAI,aAAU,EAAE,CAAC;IAC/B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;IACnD,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAEhD,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;QAChF,CAAC;IACL,CAAC,MAAM,CAAC;QACJ,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,yBAAyB,CAAC;QAC1C,CAAC;QAED,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAClD,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC3B,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACnF,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;QAED,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;IAC3C,CAAC;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,YAAY,GAAG,SAAU,KAAiB;IAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE,CAAC;QACxB,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACzB,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YACvB,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9C,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACtB,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;IACL,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC3B,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;QACD,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;YACzB,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;QACxD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC/C,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QAC1C,CAAC;QACD,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;IACpE,IAAI,SAAS,GAAY,KAAK,CAAC;IAC/B,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;QACtB,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACpE,CAAC,MAAM,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;QACjC,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACxE,CAAC;IAED,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBACjD,OAAO,CAAC,CAAC,CAAC;YACd,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAClE,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAE9D,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC;YAC7B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC3C,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC;YAC7B,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;QAC/B,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,OAAO,CAAC,CAAC,CAAC;YACd,CAAC;YAED,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC3D,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC/C,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC/B,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;QACzC,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,OAAO,CAAC,CAAC,CAAC;AACd,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAU,KAAc;IAC/D,IAAI,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACtC,OAAO;IACX,CAAC;IAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IAElC,IAAI,KAAK,EAAE,CAAC;QACR,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEnD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC9D,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACpD,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC1D,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3B,OAAO;YACX,CAAC;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAExD,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;gBACZ,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,YAAY,CAAC,aAAa,EAAE,CAAC;gBAC7B,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACtC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC,MAAM,CAAC;QACJ,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC/D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC3D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;AACL,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAU,aAAqB;IACtE,OAAO,aAAa,sKAAK,eAAY,CAAC,qCAAqC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACzJ,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1549, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.transformFeedback.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.transformFeedback.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { Engine } from \"../../Engines/engine\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\n\r\n/** @internal */\r\n// eslint-disable-next-line no-var, @typescript-eslint/naming-convention\r\nexport var _forceTransformFeedbackToBundle = true;\r\n\r\ndeclare module \"../../Engines/engine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Engine {\r\n        /**\r\n         * Creates a webGL transform feedback object\r\n         * Please makes sure to check webGLVersion property to check if you are running webGL 2+\r\n         * @returns the webGL transform feedback object\r\n         */\r\n        createTransformFeedback(): WebGLTransformFeedback;\r\n\r\n        /**\r\n         * Delete a webGL transform feedback object\r\n         * @param value defines the webGL transform feedback object to delete\r\n         */\r\n        deleteTransformFeedback(value: WebGLTransformFeedback): void;\r\n\r\n        /**\r\n         * Bind a webGL transform feedback object to the webgl context\r\n         * @param value defines the webGL transform feedback object to bind\r\n         */\r\n        bindTransformFeedback(value: Nullable<WebGLTransformFeedback>): void;\r\n\r\n        /**\r\n         * Begins a transform feedback operation\r\n         * @param usePoints defines if points or triangles must be used\r\n         */\r\n        beginTransformFeedback(usePoints: boolean): void;\r\n\r\n        /**\r\n         * Ends a transform feedback operation\r\n         */\r\n        endTransformFeedback(): void;\r\n\r\n        /**\r\n         * Specify the varyings to use with transform feedback\r\n         * @param program defines the associated webGL program\r\n         * @param value defines the list of strings representing the varying names\r\n         */\r\n        setTranformFeedbackVaryings(program: WebGLProgram, value: string[]): void;\r\n\r\n        /**\r\n         * Bind a webGL buffer for a transform feedback operation\r\n         * @param value defines the webGL buffer to bind\r\n         */\r\n        bindTransformFeedbackBuffer(value: Nullable<DataBuffer>): void;\r\n\r\n        /**\r\n         * Read data back from the bound transform feedback buffer\r\n         * @param target defines the webGL buffer to write to\r\n         */\r\n        readTransformFeedbackBuffer(target: ArrayBufferView): void;\r\n    }\r\n}\r\n\r\nEngine.prototype.createTransformFeedback = function (): WebGLTransformFeedback {\r\n    const transformFeedback = this._gl.createTransformFeedback();\r\n    if (!transformFeedback) {\r\n        throw new Error(\"Unable to create Transform Feedback\");\r\n    }\r\n    return transformFeedback;\r\n};\r\n\r\nEngine.prototype.deleteTransformFeedback = function (value: WebGLTransformFeedback): void {\r\n    this._gl.deleteTransformFeedback(value);\r\n};\r\n\r\nEngine.prototype.bindTransformFeedback = function (value: Nullable<WebGLTransformFeedback>): void {\r\n    this._gl.bindTransformFeedback(this._gl.TRANSFORM_FEEDBACK, value);\r\n};\r\n\r\nEngine.prototype.beginTransformFeedback = function (usePoints: boolean = true): void {\r\n    this._gl.beginTransformFeedback(usePoints ? this._gl.POINTS : this._gl.TRIANGLES);\r\n};\r\n\r\nEngine.prototype.endTransformFeedback = function (): void {\r\n    this._gl.endTransformFeedback();\r\n};\r\n\r\nEngine.prototype.setTranformFeedbackVaryings = function (program: WebGLProgram, value: string[]): void {\r\n    this._gl.transformFeedbackVaryings(program, value, this._gl.INTERLEAVED_ATTRIBS);\r\n};\r\n\r\nEngine.prototype.bindTransformFeedbackBuffer = function (value: Nullable<DataBuffer>): void {\r\n    this._gl.bindBufferBase(this._gl.TRANSFORM_FEEDBACK_BUFFER, 0, value ? value.underlyingResource : null);\r\n};\r\n\r\nEngine.prototype.readTransformFeedbackBuffer = function (target: ArrayBufferView): void {\r\n    this._gl.getBufferSubData(this._gl.TRANSFORM_FEEDBACK_BUFFER, 0, target);\r\n};\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;;AAKvC,IAAI,+BAA+B,GAAG,IAAI,CAAC;4JAwDlD,SAAM,CAAC,SAAS,CAAC,uBAAuB,GAAG;IACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE,CAAC;IAC7D,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACrB,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IAC3D,CAAC;IACD,OAAO,iBAAiB,CAAC;AAC7B,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,uBAAuB,GAAG,SAAU,KAA6B;IAC9E,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;AAC5C,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,qBAAqB,GAAG,SAAU,KAAuC;IACtF,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AACvE,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,sBAAsB,GAAG;oBAAU,iEAAqB,IAAI;IACzE,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACtF,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,oBAAoB,GAAG;IACpC,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,CAAC;AACpC,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,2BAA2B,GAAG,SAAU,OAAqB,EAAE,KAAe;IAC3F,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACrF,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,2BAA2B,GAAG,SAAU,KAA2B;IAChF,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5G,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,2BAA2B,GAAG,SAAU,MAAuB;IAC5E,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAC7E,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.videoTexture.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.videoTexture.ts"], "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Constants } from \"../constants\";\r\nimport type { ExternalTexture } from \"../../Materials/Textures/externalTexture\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Update a video texture\r\n         * @param texture defines the texture to update\r\n         * @param video defines the video element to use\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateVideoTexture(texture: Nullable<InternalTexture>, video: HTMLVideoElement | Nullable<ExternalTexture>, invertY: boolean): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.updateVideoTexture = function (texture: Nullable<InternalTexture>, video: HTMLVideoElement, invertY: boolean): void {\r\n    if (!texture || texture._isDisabled) {\r\n        return;\r\n    }\r\n\r\n    const glformat = this._getInternalFormat(texture.format);\r\n    const internalFormat = this._getRGBABufferInternalSizedFormat(Constants.TEXTURETYPE_UNSIGNED_BYTE, texture.format);\r\n\r\n    const wasPreviouslyBound = this._bindTextureDirectly(this._gl.TEXTURE_2D, texture, true);\r\n    this._unpackFlipY(!invertY); // Video are upside down by default\r\n\r\n    try {\r\n        // Testing video texture support\r\n        if (this._videoTextureSupported === undefined) {\r\n            // clear old errors just in case.\r\n            this._gl.getError();\r\n\r\n            this._gl.texImage2D(this._gl.TEXTURE_2D, 0, internalFormat, glformat, this._gl.UNSIGNED_BYTE, video);\r\n\r\n            if (this._gl.getError() !== 0) {\r\n                this._videoTextureSupported = false;\r\n            } else {\r\n                this._videoTextureSupported = true;\r\n            }\r\n        }\r\n\r\n        // Copy video through the current working canvas if video texture is not supported\r\n        if (!this._videoTextureSupported) {\r\n            if (!texture._workingCanvas) {\r\n                texture._workingCanvas = this.createCanvas(texture.width, texture.height);\r\n                const context = texture._workingCanvas.getContext(\"2d\");\r\n\r\n                if (!context) {\r\n                    throw new Error(\"Unable to get 2d context\");\r\n                }\r\n\r\n                texture._workingContext = context;\r\n                texture._workingCanvas.width = texture.width;\r\n                texture._workingCanvas.height = texture.height;\r\n            }\r\n\r\n            texture._workingContext!.clearRect(0, 0, texture.width, texture.height);\r\n            texture._workingContext!.drawImage(video, 0, 0, video.videoWidth, video.videoHeight, 0, 0, texture.width, texture.height);\r\n\r\n            this._gl.texImage2D(this._gl.TEXTURE_2D, 0, internalFormat, glformat, this._gl.UNSIGNED_BYTE, texture._workingCanvas as TexImageSource);\r\n        } else {\r\n            this._gl.texImage2D(this._gl.TEXTURE_2D, 0, internalFormat, glformat, this._gl.UNSIGNED_BYTE, video);\r\n        }\r\n\r\n        if (texture.generateMipMaps) {\r\n            this._gl.generateMipmap(this._gl.TEXTURE_2D);\r\n        }\r\n\r\n        if (!wasPreviouslyBound) {\r\n            this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n        }\r\n        //    this.resetTextureCache();\r\n        texture.isReady = true;\r\n    } catch (ex) {\r\n        // Something unexpected\r\n        // Let's disable the texture\r\n        texture._isDisabled = true;\r\n    }\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;;gKAmBtD,aAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAU,OAAkC,EAAE,KAAuB,EAAE,OAAgB;IAC7H,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QAClC,OAAO;IACX,CAAC;IAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACzD,MAAM,cAAc,GAAG,IAAI,CAAC,iCAAiC,CAAC,GAAA,MAAS,CAAC,CAAA,MAAA,kBAAyB,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IAEnH,MAAM,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACzF,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,mCAAmC;IAEhE,IAAI,CAAC;QACD,gCAAgC;QAChC,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAC5C,iCAAiC;YACjC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YAEpB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAErG,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACxC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACvC,CAAC;QACL,CAAC;QAED,kFAAkF;QAClF,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC1B,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC1E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAExD,IAAI,CAAC,OAAO,EAAE,CAAC;oBACX,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAChD,CAAC;gBAED,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC;gBAClC,OAAO,CAAC,cAAc,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;YACnD,CAAC;YAED,OAAO,CAAC,eAAgB,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YACxE,OAAO,CAAC,eAAgB,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAE1H,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,cAAgC,CAAC,CAAC;QAC5I,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACzG,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC;QACD,+BAA+B;QAC/B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;QACV,uBAAuB;QACvB,4BAA4B;QAC5B,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAC/B,CAAC;AACL,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1647, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.multiRender.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.multiRender.ts"], "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport type { IMultiRenderTargetOptions } from \"../../Materials/Textures/multiRenderTarget\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Constants } from \"../constants\";\r\nimport { ThinEngine } from \"../thinEngine\";\r\nimport type { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { WebGLRenderTargetWrapper } from \"../WebGL/webGLRenderTargetWrapper\";\r\nimport type { WebGLHardwareTexture } from \"../WebGL/webGLHardwareTexture\";\r\nimport type { TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Unbind a list of render target textures from the webGL context\r\n         * This is used only when drawBuffer extension or webGL2 are active\r\n         * @param rtWrapper defines the render target wrapper to unbind\r\n         * @param disableGenerateMipMaps defines a boolean indicating that mipmaps must not be generated\r\n         * @param onBeforeUnbind defines a function which will be called before the effective unbind\r\n         */\r\n        unBindMultiColorAttachmentFramebuffer(rtWrapper: RenderTargetWrapper, disableGenerateMipMaps: boolean, onBeforeUnbind?: () => void): void;\r\n\r\n        /**\r\n         * Create a multi render target texture\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#multiple-render-target\r\n         * @param size defines the size of the texture\r\n         * @param options defines the creation options\r\n         * @param initializeBuffers if set to true, the engine will make an initializing call of drawBuffers\r\n         * @returns a new render target wrapper ready to render textures\r\n         */\r\n        createMultipleRenderTarget(size: TextureSize, options: IMultiRenderTargetOptions, initializeBuffers?: boolean): RenderTargetWrapper;\r\n\r\n        /**\r\n         * Update the sample count for a given multiple render target texture\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#multisample-render-targets\r\n         * @param rtWrapper defines the render target wrapper to update\r\n         * @param samples defines the sample count to set\r\n         * @param initializeBuffers if set to true, the engine will make an initializing call of drawBuffers\r\n         * @returns the effective sample count (could be 0 if multisample render targets are not supported)\r\n         */\r\n        updateMultipleRenderTargetTextureSampleCount(rtWrapper: Nullable<RenderTargetWrapper>, samples: number, initializeBuffers?: boolean): number;\r\n\r\n        /**\r\n         * Generates mipmaps for the texture of the (multi) render target\r\n         * @param texture The render target containing the textures to generate the mipmaps for\r\n         */\r\n        generateMipMapsMultiFramebuffer(texture: RenderTargetWrapper): void;\r\n\r\n        /**\r\n         * Resolves the MSAA textures of the (multi) render target into their non-MSAA version.\r\n         * Note that if \"texture\" is not a MSAA render target, no resolve is performed.\r\n         * @param texture The render target texture containing the MSAA textures to resolve\r\n         */\r\n        resolveMultiFramebuffer(texture: RenderTargetWrapper): void;\r\n\r\n        /**\r\n         * Select a subsets of attachments to draw to.\r\n         * @param attachments gl attachments\r\n         */\r\n        bindAttachments(attachments: number[]): void;\r\n\r\n        /**\r\n         * Creates a layout object to draw/clear on specific textures in a MRT\r\n         * @param textureStatus textureStatus[i] indicates if the i-th is active\r\n         * @returns A layout to be fed to the engine, calling `bindAttachments`.\r\n         */\r\n        buildTextureLayout(textureStatus: boolean[]): number[];\r\n\r\n        /**\r\n         * Restores the webgl state to only draw on the main color attachment\r\n         * when the frame buffer associated is the canvas frame buffer\r\n         */\r\n        restoreSingleAttachment(): void;\r\n\r\n        /**\r\n         * Restores the webgl state to only draw on the main color attachment\r\n         * when the frame buffer associated is not the canvas frame buffer\r\n         */\r\n        restoreSingleAttachmentForRenderTarget(): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.restoreSingleAttachment = function (): void {\r\n    const gl = this._gl;\r\n\r\n    this.bindAttachments([gl.BACK]);\r\n};\r\n\r\nThinEngine.prototype.restoreSingleAttachmentForRenderTarget = function (): void {\r\n    const gl = this._gl;\r\n\r\n    this.bindAttachments([gl.COLOR_ATTACHMENT0]);\r\n};\r\n\r\nThinEngine.prototype.buildTextureLayout = function (textureStatus: boolean[]): number[] {\r\n    const gl = this._gl;\r\n\r\n    const result = [];\r\n\r\n    for (let i = 0; i < textureStatus.length; i++) {\r\n        if (textureStatus[i]) {\r\n            result.push((<any>gl)[\"COLOR_ATTACHMENT\" + i]);\r\n        } else {\r\n            result.push(gl.NONE);\r\n        }\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nThinEngine.prototype.bindAttachments = function (attachments: number[]): void {\r\n    const gl = this._gl;\r\n\r\n    gl.drawBuffers(attachments);\r\n};\r\n\r\nThinEngine.prototype.unBindMultiColorAttachmentFramebuffer = function (\r\n    rtWrapper: WebGLRenderTargetWrapper,\r\n    disableGenerateMipMaps: boolean = false,\r\n    onBeforeUnbind?: () => void\r\n): void {\r\n    this._currentRenderTarget = null;\r\n\r\n    if (!rtWrapper.disableAutomaticMSAAResolve) {\r\n        this.resolveMultiFramebuffer(rtWrapper);\r\n    }\r\n\r\n    if (!disableGenerateMipMaps) {\r\n        this.generateMipMapsMultiFramebuffer(rtWrapper);\r\n    }\r\n\r\n    if (onBeforeUnbind) {\r\n        if (rtWrapper._MSAAFramebuffer) {\r\n            // Bind the correct framebuffer\r\n            this._bindUnboundFramebuffer(rtWrapper._framebuffer);\r\n        }\r\n        onBeforeUnbind();\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(null);\r\n};\r\n\r\nThinEngine.prototype.createMultipleRenderTarget = function (size: TextureSize, options: IMultiRenderTargetOptions, initializeBuffers: boolean = true): RenderTargetWrapper {\r\n    let generateMipMaps = false;\r\n    let generateDepthBuffer = true;\r\n    let generateStencilBuffer = false;\r\n    let generateDepthTexture = false;\r\n    let depthTextureFormat: number | undefined = undefined;\r\n    let textureCount = 1;\r\n    let samples = 1;\r\n\r\n    const defaultType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n    const defaultSamplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n    const defaultUseSRGBBuffer = false;\r\n    const defaultFormat = Constants.TEXTUREFORMAT_RGBA;\r\n    const defaultTarget = Constants.TEXTURE_2D;\r\n\r\n    let types: number[] = [];\r\n    let samplingModes: number[] = [];\r\n    let useSRGBBuffers: boolean[] = [];\r\n    let formats: number[] = [];\r\n    let targets: number[] = [];\r\n    let faceIndex: number[] = [];\r\n    let layerIndex: number[] = [];\r\n    let layers: number[] = [];\r\n    let labels: string[] = [];\r\n    let dontCreateTextures = false;\r\n\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(true, false, size) as WebGLRenderTargetWrapper;\r\n\r\n    if (options !== undefined) {\r\n        generateMipMaps = options.generateMipMaps === undefined ? false : options.generateMipMaps;\r\n        generateDepthBuffer = options.generateDepthBuffer === undefined ? true : options.generateDepthBuffer;\r\n        generateStencilBuffer = options.generateStencilBuffer === undefined ? false : options.generateStencilBuffer;\r\n        generateDepthTexture = options.generateDepthTexture === undefined ? false : options.generateDepthTexture;\r\n        textureCount = options.textureCount ?? 1;\r\n        samples = options.samples ?? samples;\r\n        types = options.types || types;\r\n        samplingModes = options.samplingModes || samplingModes;\r\n        useSRGBBuffers = options.useSRGBBuffers || useSRGBBuffers;\r\n        formats = options.formats || formats;\r\n        targets = options.targetTypes || targets;\r\n        faceIndex = options.faceIndex || faceIndex;\r\n        layerIndex = options.layerIndex || layerIndex;\r\n        layers = options.layerCounts || layers;\r\n        labels = options.labels || labels;\r\n        dontCreateTextures = options.dontCreateTextures ?? false;\r\n\r\n        if (\r\n            this.webGLVersion > 1 &&\r\n            (options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 ||\r\n                options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8 ||\r\n                options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24 ||\r\n                options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32_FLOAT ||\r\n                options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8)\r\n        ) {\r\n            depthTextureFormat = options.depthTextureFormat;\r\n        }\r\n    }\r\n\r\n    if (depthTextureFormat === undefined) {\r\n        depthTextureFormat = generateStencilBuffer ? Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 : Constants.TEXTUREFORMAT_DEPTH32_FLOAT;\r\n    }\r\n\r\n    const gl = this._gl;\r\n    // Create the framebuffer\r\n    const currentFramebuffer = this._currentFramebuffer;\r\n    const framebuffer = gl.createFramebuffer();\r\n    this._bindUnboundFramebuffer(framebuffer);\r\n\r\n    const width = (<{ width: number; height: number }>size).width ?? <number>size;\r\n    const height = (<{ width: number; height: number }>size).height ?? <number>size;\r\n\r\n    const textures: InternalTexture[] = [];\r\n    const attachments: number[] = [];\r\n\r\n    const useStencilTexture =\r\n        this.webGLVersion > 1 &&\r\n        (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 ||\r\n            depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8 ||\r\n            depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8);\r\n\r\n    rtWrapper.label = options?.label ?? \"MultiRenderTargetWrapper\";\r\n    rtWrapper._framebuffer = framebuffer;\r\n    rtWrapper._generateDepthBuffer = generateDepthTexture || generateDepthBuffer;\r\n    rtWrapper._generateStencilBuffer = generateDepthTexture ? useStencilTexture : generateStencilBuffer;\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(rtWrapper._generateStencilBuffer, rtWrapper._generateDepthBuffer, width, height, 1, depthTextureFormat);\r\n    rtWrapper._attachments = attachments;\r\n\r\n    for (let i = 0; i < textureCount; i++) {\r\n        let samplingMode = samplingModes[i] || defaultSamplingMode;\r\n        let type = types[i] || defaultType;\r\n        let useSRGBBuffer = useSRGBBuffers[i] || defaultUseSRGBBuffer;\r\n        const format = formats[i] || defaultFormat;\r\n\r\n        const target = targets[i] || defaultTarget;\r\n        const layerCount = layers[i] ?? 1;\r\n\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n            // if floating point linear (gl.FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        } else if (type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n            // if floating point linear (HALF_FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n\r\n        const filters = this._getSamplingParameters(samplingMode, generateMipMaps);\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n            type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n            Logger.Warn(\"Float textures are not supported. Render target forced to TEXTURETYPE_UNSIGNED_BYTE type\");\r\n        }\r\n\r\n        useSRGBBuffer = useSRGBBuffer && this._caps.supportSRGBBuffers && (this.webGLVersion > 1 || this.isWebGPU);\r\n\r\n        const isWebGL2 = this.webGLVersion > 1;\r\n        const attachment = (<any>gl)[isWebGL2 ? \"COLOR_ATTACHMENT\" + i : \"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n\r\n        attachments.push(attachment);\r\n\r\n        if (target === -1 || dontCreateTextures) {\r\n            continue;\r\n        }\r\n\r\n        const texture = new InternalTexture(this, InternalTextureSource.MultiRenderTarget);\r\n        textures[i] = texture;\r\n\r\n        gl.activeTexture((<any>gl)[\"TEXTURE\" + i]);\r\n        gl.bindTexture(target, texture._hardwareTexture!.underlyingResource);\r\n\r\n        gl.texParameteri(target, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n        gl.texParameteri(target, gl.TEXTURE_MIN_FILTER, filters.min);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n        const internalSizedFormat = this._getRGBABufferInternalSizedFormat(type, format, useSRGBBuffer);\r\n        const internalFormat = this._getInternalFormat(format);\r\n        const webGLTextureType = this._getWebGLTextureType(type);\r\n\r\n        if (isWebGL2 && (target === Constants.TEXTURE_2D_ARRAY || target === Constants.TEXTURE_3D)) {\r\n            if (target === Constants.TEXTURE_2D_ARRAY) {\r\n                texture.is2DArray = true;\r\n            } else {\r\n                texture.is3D = true;\r\n            }\r\n\r\n            texture.baseDepth = texture.depth = layerCount;\r\n\r\n            gl.texImage3D(target, 0, internalSizedFormat, width, height, layerCount, 0, internalFormat, webGLTextureType, null);\r\n        } else if (target === Constants.TEXTURE_CUBE_MAP) {\r\n            // We have to generate all faces to complete the framebuffer\r\n            for (let i = 0; i < 6; i++) {\r\n                gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + i, 0, internalSizedFormat, width, height, 0, internalFormat, webGLTextureType, null);\r\n            }\r\n            texture.isCube = true;\r\n        } else {\r\n            gl.texImage2D(gl.TEXTURE_2D, 0, internalSizedFormat, width, height, 0, internalFormat, webGLTextureType, null);\r\n        }\r\n\r\n        if (generateMipMaps) {\r\n            gl.generateMipmap(target);\r\n        }\r\n\r\n        // Unbind\r\n        this._bindTextureDirectly(target, null);\r\n\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.isReady = true;\r\n        texture.samples = 1;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        texture.type = type;\r\n        texture._useSRGBBuffer = useSRGBBuffer;\r\n        texture.format = format;\r\n        texture.label = labels[i] ?? rtWrapper.label + \"-Texture\" + i;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n    }\r\n\r\n    if (generateDepthTexture && this._caps.depthTextureExtension && !dontCreateTextures) {\r\n        // Depth texture\r\n        const depthTexture = new InternalTexture(this, InternalTextureSource.Depth);\r\n\r\n        let depthTextureType = Constants.TEXTURETYPE_UNSIGNED_SHORT;\r\n        let glDepthTextureInternalFormat: GLenum = gl.DEPTH_COMPONENT16;\r\n        let glDepthTextureFormat: GLenum = gl.DEPTH_COMPONENT;\r\n        let glDepthTextureType: GLenum = gl.UNSIGNED_SHORT;\r\n        let glDepthTextureAttachment: GLenum = gl.DEPTH_ATTACHMENT;\r\n        if (this.webGLVersion < 2) {\r\n            glDepthTextureInternalFormat = gl.DEPTH_COMPONENT;\r\n        } else {\r\n            if (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32_FLOAT) {\r\n                depthTextureType = Constants.TEXTURETYPE_FLOAT;\r\n                glDepthTextureType = gl.FLOAT;\r\n                glDepthTextureInternalFormat = gl.DEPTH_COMPONENT32F;\r\n            } else if (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8) {\r\n                depthTextureType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n                glDepthTextureType = gl.FLOAT_32_UNSIGNED_INT_24_8_REV;\r\n                glDepthTextureInternalFormat = gl.DEPTH32F_STENCIL8;\r\n                glDepthTextureFormat = gl.DEPTH_STENCIL;\r\n                glDepthTextureAttachment = gl.DEPTH_STENCIL_ATTACHMENT;\r\n            } else if (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24) {\r\n                depthTextureType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n                glDepthTextureType = gl.UNSIGNED_INT;\r\n                glDepthTextureInternalFormat = gl.DEPTH_COMPONENT24;\r\n                glDepthTextureAttachment = gl.DEPTH_ATTACHMENT;\r\n            } else if (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 || depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8) {\r\n                depthTextureType = Constants.TEXTURETYPE_UNSIGNED_INT_24_8;\r\n                glDepthTextureType = gl.UNSIGNED_INT_24_8;\r\n                glDepthTextureInternalFormat = gl.DEPTH24_STENCIL8;\r\n                glDepthTextureFormat = gl.DEPTH_STENCIL;\r\n                glDepthTextureAttachment = gl.DEPTH_STENCIL_ATTACHMENT;\r\n            }\r\n        }\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_2D, depthTexture, true);\r\n\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n        gl.texImage2D(gl.TEXTURE_2D, 0, glDepthTextureInternalFormat, width, height, 0, glDepthTextureFormat, glDepthTextureType, null);\r\n\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, glDepthTextureAttachment, gl.TEXTURE_2D, depthTexture._hardwareTexture!.underlyingResource, 0);\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_2D, null);\r\n\r\n        rtWrapper._depthStencilTexture = depthTexture;\r\n        rtWrapper._depthStencilTextureWithStencil = useStencilTexture;\r\n\r\n        depthTexture.baseWidth = width;\r\n        depthTexture.baseHeight = height;\r\n        depthTexture.width = width;\r\n        depthTexture.height = height;\r\n        depthTexture.isReady = true;\r\n        depthTexture.samples = 1;\r\n        depthTexture.generateMipMaps = generateMipMaps;\r\n        depthTexture.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        depthTexture.format = depthTextureFormat;\r\n        depthTexture.type = depthTextureType;\r\n        depthTexture.label = rtWrapper.label + \"-DepthStencil\";\r\n\r\n        textures[textureCount] = depthTexture;\r\n        this._internalTexturesCache.push(depthTexture);\r\n    }\r\n    rtWrapper.setTextures(textures);\r\n    if (initializeBuffers) {\r\n        gl.drawBuffers(attachments);\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(currentFramebuffer);\r\n\r\n    rtWrapper.setLayerAndFaceIndices(layerIndex, faceIndex);\r\n\r\n    this.resetTextureCache();\r\n\r\n    if (!dontCreateTextures) {\r\n        this.updateMultipleRenderTargetTextureSampleCount(rtWrapper, samples, initializeBuffers);\r\n    } else if (samples > 1) {\r\n        const framebuffer = gl.createFramebuffer();\r\n\r\n        if (!framebuffer) {\r\n            throw new Error(\"Unable to create multi sampled framebuffer\");\r\n        }\r\n\r\n        rtWrapper._samples = samples;\r\n        rtWrapper._MSAAFramebuffer = framebuffer;\r\n\r\n        if (textureCount > 0 && initializeBuffers) {\r\n            this._bindUnboundFramebuffer(framebuffer);\r\n            gl.drawBuffers(attachments);\r\n            this._bindUnboundFramebuffer(currentFramebuffer);\r\n        }\r\n    }\r\n\r\n    return rtWrapper;\r\n};\r\n\r\nThinEngine.prototype.updateMultipleRenderTargetTextureSampleCount = function (\r\n    rtWrapper: Nullable<WebGLRenderTargetWrapper>,\r\n    samples: number,\r\n    initializeBuffers: boolean = true\r\n): number {\r\n    if (this.webGLVersion < 2 || !rtWrapper) {\r\n        return 1;\r\n    }\r\n\r\n    if (rtWrapper.samples === samples) {\r\n        return samples;\r\n    }\r\n\r\n    const gl = this._gl;\r\n\r\n    samples = Math.min(samples, this.getCaps().maxMSAASamples);\r\n\r\n    // Dispose previous render buffers\r\n    if (rtWrapper._depthStencilBuffer) {\r\n        gl.deleteRenderbuffer(rtWrapper._depthStencilBuffer);\r\n        rtWrapper._depthStencilBuffer = null;\r\n    }\r\n\r\n    if (rtWrapper._MSAAFramebuffer) {\r\n        gl.deleteFramebuffer(rtWrapper._MSAAFramebuffer);\r\n        rtWrapper._MSAAFramebuffer = null;\r\n    }\r\n\r\n    const count = rtWrapper._attachments!.length; // We do it this way instead of rtWrapper.textures.length to avoid taking into account the depth/stencil texture, in case it has been created\r\n\r\n    for (let i = 0; i < count; i++) {\r\n        const texture = rtWrapper.textures![i];\r\n        const hardwareTexture = texture._hardwareTexture as Nullable<WebGLHardwareTexture>;\r\n\r\n        hardwareTexture?.releaseMSAARenderBuffers();\r\n    }\r\n\r\n    if (samples > 1 && typeof gl.renderbufferStorageMultisample === \"function\") {\r\n        const framebuffer = gl.createFramebuffer();\r\n\r\n        if (!framebuffer) {\r\n            throw new Error(\"Unable to create multi sampled framebuffer\");\r\n        }\r\n\r\n        rtWrapper._MSAAFramebuffer = framebuffer;\r\n        this._bindUnboundFramebuffer(framebuffer);\r\n\r\n        const attachments = [];\r\n\r\n        for (let i = 0; i < count; i++) {\r\n            const texture = rtWrapper.textures![i];\r\n            const hardwareTexture = texture._hardwareTexture as WebGLHardwareTexture;\r\n            const attachment = (<any>gl)[this.webGLVersion > 1 ? \"COLOR_ATTACHMENT\" + i : \"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n\r\n            const colorRenderbuffer = this._createRenderBuffer(\r\n                texture.width,\r\n                texture.height,\r\n                samples,\r\n                -1 /* not used */,\r\n                this._getRGBABufferInternalSizedFormat(texture.type, texture.format, texture._useSRGBBuffer),\r\n                attachment\r\n            );\r\n\r\n            if (!colorRenderbuffer) {\r\n                throw new Error(\"Unable to create multi sampled framebuffer\");\r\n            }\r\n\r\n            hardwareTexture.addMSAARenderBuffer(colorRenderbuffer);\r\n            texture.samples = samples;\r\n\r\n            attachments.push(attachment);\r\n        }\r\n        if (initializeBuffers) {\r\n            gl.drawBuffers(attachments);\r\n        }\r\n    } else {\r\n        this._bindUnboundFramebuffer(rtWrapper._framebuffer);\r\n    }\r\n\r\n    const depthFormat = rtWrapper._depthStencilTexture ? rtWrapper._depthStencilTexture.format : undefined;\r\n\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(\r\n        rtWrapper._generateStencilBuffer,\r\n        rtWrapper._generateDepthBuffer,\r\n        rtWrapper.width,\r\n        rtWrapper.height,\r\n        samples,\r\n        depthFormat\r\n    );\r\n\r\n    this._bindUnboundFramebuffer(null);\r\n\r\n    rtWrapper._samples = samples;\r\n\r\n    return samples;\r\n};\r\n\r\nThinEngine.prototype.generateMipMapsMultiFramebuffer = function (texture: RenderTargetWrapper): void {\r\n    const rtWrapper = texture as WebGLRenderTargetWrapper;\r\n    const gl = this._gl;\r\n\r\n    if (!rtWrapper.isMulti) {\r\n        return;\r\n    }\r\n\r\n    for (let i = 0; i < rtWrapper._attachments!.length; i++) {\r\n        const texture = rtWrapper.textures![i];\r\n        if (texture?.generateMipMaps && !texture?.isCube && !texture?.is3D) {\r\n            this._bindTextureDirectly(gl.TEXTURE_2D, texture, true);\r\n            gl.generateMipmap(gl.TEXTURE_2D);\r\n            this._bindTextureDirectly(gl.TEXTURE_2D, null);\r\n        }\r\n    }\r\n};\r\n\r\nThinEngine.prototype.resolveMultiFramebuffer = function (texture: RenderTargetWrapper): void {\r\n    const rtWrapper = texture as WebGLRenderTargetWrapper;\r\n    const gl = this._gl;\r\n\r\n    if (!rtWrapper._MSAAFramebuffer || !rtWrapper.isMulti) {\r\n        return;\r\n    }\r\n\r\n    let bufferBits = rtWrapper.resolveMSAAColors ? gl.COLOR_BUFFER_BIT : 0;\r\n    bufferBits |= rtWrapper._generateDepthBuffer && rtWrapper.resolveMSAADepth ? gl.DEPTH_BUFFER_BIT : 0;\r\n    bufferBits |= rtWrapper._generateStencilBuffer && rtWrapper.resolveMSAAStencil ? gl.STENCIL_BUFFER_BIT : 0;\r\n\r\n    const attachments = rtWrapper._attachments!;\r\n    const count = attachments.length;\r\n\r\n    gl.bindFramebuffer(gl.READ_FRAMEBUFFER, rtWrapper._MSAAFramebuffer);\r\n    gl.bindFramebuffer(gl.DRAW_FRAMEBUFFER, rtWrapper._framebuffer);\r\n\r\n    for (let i = 0; i < count; i++) {\r\n        const texture = rtWrapper.textures![i];\r\n\r\n        for (let j = 0; j < count; j++) {\r\n            attachments[j] = gl.NONE;\r\n        }\r\n\r\n        attachments[i] = (<any>gl)[this.webGLVersion > 1 ? \"COLOR_ATTACHMENT\" + i : \"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n        gl.readBuffer(attachments[i]);\r\n        gl.drawBuffers(attachments);\r\n        gl.blitFramebuffer(0, 0, texture.width, texture.height, 0, 0, texture.width, texture.height, bufferBits, gl.NEAREST);\r\n    }\r\n\r\n    for (let i = 0; i < count; i++) {\r\n        attachments[i] = (<any>gl)[this.webGLVersion > 1 ? \"COLOR_ATTACHMENT\" + i : \"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n    }\r\n\r\n    gl.drawBuffers(attachments);\r\n    gl.bindFramebuffer(this._gl.FRAMEBUFFER, rtWrapper._MSAAFramebuffer);\r\n};\r\n"], "names": [], "mappings": ";AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAElG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAG3C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;;;;AA8E3C,6KAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG;IAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,eAAe,CAAC;QAAC,EAAE,CAAC,IAAI;KAAC,CAAC,CAAC;AACpC,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,sCAAsC,GAAG;IAC1D,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,eAAe,CAAC;QAAC,EAAE,CAAC,iBAAiB;KAAC,CAAC,CAAC;AACjD,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAU,aAAwB;IACxE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,MAAM,MAAM,GAAG,EAAE,CAAC;IAElB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;QAC5C,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAO,EAAG,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,eAAe,GAAG,SAAU,WAAqB;IAClE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,qCAAqC,GAAG,SACzD,SAAmC;iCACnC,iEAAkC,KAAK,EACvC,cAA2B;IAE3B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAEjC,IAAI,CAAC,SAAS,CAAC,2BAA2B,EAAE,CAAC;QACzC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC1B,IAAI,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,cAAc,EAAE,CAAC;QACjB,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAC7B,+BAA+B;YAC/B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACzD,CAAC;QACD,cAAc,EAAE,CAAC;IACrB,CAAC;IAED,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;AACvC,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,0BAA0B,GAAG,SAAU,IAAiB,EAAE,OAAkC;4BAAE,iEAA6B,IAAI;IAChJ,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAI,mBAAmB,GAAG,IAAI,CAAC;IAC/B,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAClC,IAAI,oBAAoB,GAAG,KAAK,CAAC;IACjC,IAAI,kBAAkB,GAAuB,SAAS,CAAC;IACvD,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,MAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC;IACxD,MAAM,mBAAmB,GAAG,SAAS,CAAC,8BAA8B,CAAC;IACrE,MAAM,oBAAoB,GAAG,KAAK,CAAC;IACnC,MAAM,aAAa,GAAG,SAAS,CAAC,kBAAkB,CAAC;IACnD,MAAM,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC;IAE3C,IAAI,KAAK,GAAa,EAAE,CAAC;IACzB,IAAI,aAAa,GAAa,EAAE,CAAC;IACjC,IAAI,cAAc,GAAc,EAAE,CAAC;IACnC,IAAI,OAAO,GAAa,EAAE,CAAC;IAC3B,IAAI,OAAO,GAAa,EAAE,CAAC;IAC3B,IAAI,SAAS,GAAa,EAAE,CAAC;IAC7B,IAAI,UAAU,GAAa,EAAE,CAAC;IAC9B,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,IAAI,kBAAkB,GAAG,KAAK,CAAC;IAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAA6B,CAAC;IAEzG,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QACxB,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;QAC1F,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACrG,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;QAC5G,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC;;QACzG,YAAY,4BAAG,OAAO,CAAC,YAAY,yEAAI,CAAC,CAAC;;QACzC,OAAO,+BAAW,OAAO,4CAAf,OAAO,YAAY,OAAO,CAAC;QACrC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;QAC/B,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC;QACvD,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC;QAC1D,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC;QACrC,OAAO,GAAG,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC;QACzC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC;QAC3C,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC;QAC9C,MAAM,GAAG,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC;QACvC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC;;QAClC,kBAAkB,IAAG,OAAO,+BAAC,kBAAkB,qFAAI,KAAK,CAAC;QAEzD,IACI,IAAI,CAAC,YAAY,GAAG,CAAC,IACrB,CAAC,OAAO,CAAC,kBAAkB,KAAK,MAC5B,GADqC,CAAC,GAC/B,CAAC,kBAAkB,KAAK,GADqC,GAEpE,GADwC,CAAC,GAClC,CAAC,kBAAkB,KAAK,MAC/B,EAF4E,CACpC,CAAC,GAClC,CAAC,iBADsD,CACpC,KAAK,MAC/B,GADwC,CAAC,GAClC,CAAC,kBAAkB,KAD0C,AACrC,EAAA,GAAA,IAAS,CAAC,mCAAmC,CAAC,EACnF,CAAC;YACC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACpD,CAAC;IACL,CAAC;IAED,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;QACnC,kBAAkB,GAAG,qBAAqB,CAAC,CAAC,CAAC,KAAA,IAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC;IAClI,CAAC;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,yBAAyB;IACzB,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;IACpD,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAC3C,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;;IAE1C,MAAM,KAAK,kBAAuC,IAAK,CAAC,KAAK,qDAAY,IAAI,CAAC;;IAC9E,MAAM,MAAM,wBAA6C,MAAM,wCAAZ,IAAK,WAAmB,IAAI,CAAC;IAEhF,MAAM,QAAQ,GAAsB,EAAE,CAAC;IACvC,MAAM,WAAW,GAAa,EAAE,CAAC;IAEjC,MAAM,iBAAiB,GACnB,IAAI,CAAC,YAAY,GAAG,CAAC,IACrB,CAAC,kBAAkB,KAAK,MACpB,GAD6B,CAAC,cACZ,KAAK,MACvB,GADgC,CAAC,CAD2B,aAE1C,KAAK,EAAA,OAAS,CAAC,MADmC,6BACA,CAAC,CAAC;;IAE9E,SAAS,CAAC,KAAK,IAAG,OAAO,oEAAE,KAAK,2DAAI,0BAA0B,CAAC;IAC/D,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;IACrC,SAAS,CAAC,oBAAoB,GAAG,oBAAoB,IAAI,mBAAmB,CAAC;IAC7E,SAAS,CAAC,sBAAsB,GAAG,oBAAoB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,qBAAqB,CAAC;IACpG,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,sBAAsB,EAAE,SAAS,CAAC,oBAAoB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,kBAAkB,CAAC,CAAC;IAC/K,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;IAErC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,CAAE,CAAC;QACpC,IAAI,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC;QAC3D,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC;QACnC,IAAI,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC;QAC9D,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC;QAE3C,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC;;QAC3C,MAAM,UAAU,sBAAS,CAAC,CAAC,CAAC,qCAAT,YAAa,CAAC,CAAC;QAElC,IAAI,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,UAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,EAAA,wBAA2B,EAAE,CAAC;YAClF,yEAAyE;YACzE,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QAC1D,CAAC,MAAM,IAAI,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,eAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAA,6BAA+B,EAAE,CAAC;YAClG,2EAA2E;YAC3E,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QAC1D,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;QAC3E,IAAI,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,UAAiB,EAAA,EAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YACnE,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;qKAC3C,SAAM,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;QAC5G,CAAC;QAED,aAAa,GAAG,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE3G,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACvC,MAAM,UAAU,GAAS,EAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;QAEpG,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7B,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;YACtC,SAAS;QACb,CAAC;QAED,MAAM,OAAO,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAA,EAAA,2CAAA,GAA0C,CAAC;QACnF,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QAEtB,EAAE,CAAC,aAAa,CAAO,EAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;QAC3C,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAiB,CAAC,kBAAkB,CAAC,CAAC;QAErE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAC9D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAE9D,MAAM,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAChG,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,QAAQ,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,UAAA,KAAA,CAAgB,EAAA,EAAI,MAAM,KAAK,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;YACzF,IAAI,MAAM,KAAK,OAAA,EAAS,CAAC,gBAAgB,EAAE,CAAC;gBACxC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YAC7B,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACxB,CAAC;YAED,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC;YAE/C,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;QACxH,CAAC,MAAM,IAAI,MAAM,KAAK,OAAA,EAAS,CAAC,gBAAgB,EAAE,CAAC;YAC/C,4DAA4D;YAC5D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC;gBACzB,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,CAAC,EAAE,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;YACxI,CAAC;YACD,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QAC1B,CAAC,MAAM,CAAC;YACJ,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;QACnH,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YAClB,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;QAED,SAAS;QACT,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAExC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;QACpB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;QACvC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACR;QAAhB,OAAO,CAAC,KAAK,sBAAS,CAAC,CAAC,CAAC,iDAAI,SAAS,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;QAE9D,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,oBAAoB,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClF,gBAAgB;QAChB,MAAM,YAAY,GAAG,uLAAI,kBAAe,CAAC,IAAI,EAAA,GAAA,+BAAA,GAA8B,CAAC;QAE5E,IAAI,gBAAgB,GAAG,SAAS,CAAC,0BAA0B,CAAC;QAC5D,IAAI,4BAA4B,GAAW,EAAE,CAAC,iBAAiB,CAAC;QAChE,IAAI,oBAAoB,GAAW,EAAE,CAAC,eAAe,CAAC;QACtD,IAAI,kBAAkB,GAAW,EAAE,CAAC,cAAc,CAAC;QACnD,IAAI,wBAAwB,GAAW,EAAE,CAAC,gBAAgB,CAAC;QAC3D,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;YACxB,4BAA4B,GAAG,EAAE,CAAC,eAAe,CAAC;QACtD,CAAC,MAAM,CAAC;YACJ,IAAI,kBAAkB,KAAK,IAAA,KAAS,CAAC,2BAA2B,EAAE,CAAC;gBAC/D,gBAAgB,GAAG,SAAS,CAAC,iBAAiB,CAAC;gBAC/C,kBAAkB,GAAG,EAAE,CAAC,KAAK,CAAC;gBAC9B,4BAA4B,GAAG,EAAE,CAAC,kBAAkB,CAAC;YACzD,CAAC,MAAM,IAAI,kBAAkB,KAAK,IAAA,KAAS,CAAC,mCAAmC,EAAE,CAAC;gBAC9E,gBAAgB,GAAG,SAAS,CAAC,yBAAyB,CAAC;gBACvD,kBAAkB,GAAG,EAAE,CAAC,8BAA8B,CAAC;gBACvD,4BAA4B,GAAG,EAAE,CAAC,iBAAiB,CAAC;gBACpD,oBAAoB,GAAG,EAAE,CAAC,aAAa,CAAC;gBACxC,wBAAwB,GAAG,EAAE,CAAC,wBAAwB,CAAC;YAC3D,CAAC,MAAM,IAAI,kBAAkB,KAAK,IAAA,KAAS,CAAC,qBAAqB,EAAE,CAAC;gBAChE,gBAAgB,GAAG,SAAS,CAAC,yBAAyB,CAAC;gBACvD,kBAAkB,GAAG,EAAE,CAAC,YAAY,CAAC;gBACrC,4BAA4B,GAAG,EAAE,CAAC,iBAAiB,CAAC;gBACpD,wBAAwB,GAAG,EAAE,CAAC,gBAAgB,CAAC;YACnD,CAAC,MAAM,IAAI,kBAAkB,KAAK,MAAA,GAAS,CAAC,mBAAA,IAAA,OAA8B,IAAI,kBAAkB,KAAK,SAAS,CAAC,mCAAmC,EAAE,CAAC;gBACjJ,gBAAgB,GAAG,SAAS,CAAC,6BAA6B,CAAC;gBAC3D,kBAAkB,GAAG,EAAE,CAAC,iBAAiB,CAAC;gBAC1C,4BAA4B,GAAG,EAAE,CAAC,gBAAgB,CAAC;gBACnD,oBAAoB,GAAG,EAAE,CAAC,aAAa,CAAC;gBACxC,wBAAwB,GAAG,EAAE,CAAC,wBAAwB,CAAC;YAC3D,CAAC;QACL,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAE7D,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;QACnE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;QACnE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QACrE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QACrE,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,4BAA4B,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAEhI,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,wBAAwB,EAAE,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC,gBAAiB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAEvI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAE/C,SAAS,CAAC,oBAAoB,GAAG,YAAY,CAAC;QAC9C,SAAS,CAAC,+BAA+B,GAAG,iBAAiB,CAAC;QAE9D,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;QAC/B,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC;QACjC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;QAC3B,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;QAC7B,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QAC5B,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;QACzB,YAAY,CAAC,eAAe,GAAG,eAAe,CAAC;QAC/C,YAAY,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QACnE,YAAY,CAAC,MAAM,GAAG,kBAAkB,CAAC;QACzC,YAAY,CAAC,IAAI,GAAG,gBAAgB,CAAC;QACrC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG,eAAe,CAAC;QAEvD,QAAQ,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACnD,CAAC;IACD,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAChC,IAAI,iBAAiB,EAAE,CAAC;QACpB,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAChC,CAAC;IAED,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;IAEjD,SAAS,CAAC,sBAAsB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAExD,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAEzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtB,IAAI,CAAC,4CAA4C,CAAC,SAAS,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;IAC7F,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAE3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAClE,CAAC;QAED,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC7B,SAAS,CAAC,gBAAgB,GAAG,WAAW,CAAC;QAEzC,IAAI,YAAY,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACxC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;YAC1C,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAC5B,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,4CAA4C,GAAG,SAChE,SAA6C,EAC7C,OAAe;QACf,qFAA6B,IAAI;IAEjC,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACtC,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAI,SAAS,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;QAChC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC;IAE3D,kCAAkC;IAClC,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;QAChC,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACrD,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACzC,CAAC;IAED,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;QAC7B,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACjD,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED,MAAM,KAAK,GAAG,SAAS,CAAC,YAAa,CAAC,MAAM,CAAC,CAAC,6IAA6I;IAE3L,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,eAAe,GAAG,OAAO,CAAC,gBAAkD,CAAC;0EAEnF,eAAe,CAAE,wBAAwB,EAAE,CAAC;IAChD,CAAC;IAED,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,8BAA8B,KAAK,UAAU,EAAE,CAAC;QACzE,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAE3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAClE,CAAC;QAED,SAAS,CAAC,gBAAgB,GAAG,WAAW,CAAC;QACzC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QAE1C,MAAM,WAAW,GAAG,EAAE,CAAC;QAEvB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,eAAe,GAAG,OAAO,CAAC,gBAAwC,CAAC;YACzE,MAAM,UAAU,GAAS,EAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;YAEjH,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAC9C,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,EACd,OAAO,EACP,CAAC,CAAC,CAAC,YAAA,EAAc,GACjB,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,EAC5F,UAAU,CACb,CAAC;YAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAClE,CAAC;YAED,eAAe,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;YACvD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;YAE1B,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjC,CAAC;QACD,IAAI,iBAAiB,EAAE,CAAC;YACpB,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAChC,CAAC;IACL,CAAC,MAAM,CAAC;QACJ,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,WAAW,GAAG,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;IAEvG,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAClE,SAAS,CAAC,sBAAsB,EAChC,SAAS,CAAC,oBAAoB,EAC9B,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,MAAM,EAChB,OAAO,EACP,WAAW,CACd,CAAC;IAEF,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;IAE7B,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,+BAA+B,GAAG,SAAU,OAA4B;IACzF,MAAM,SAAS,GAAG,OAAmC,CAAC;IACtD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACrB,OAAO;IACX,CAAC;IAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,YAAa,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;QACtD,MAAM,OAAO,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,OAAO,oDAAE,eAAe,KAAI,oDAAC,OAAO,CAAE,MAAM,KAAI,oDAAC,OAAO,CAAE,IAAI,GAAE,CAAC;YACjE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YACxD,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;YACjC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;AACL,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,SAAU,OAA4B;IACjF,MAAM,SAAS,GAAG,OAAmC,CAAC;IACtD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACpD,OAAO;IACX,CAAC;IAED,IAAI,UAAU,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,UAAU,IAAI,SAAS,CAAC,oBAAoB,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;IACrG,UAAU,IAAI,SAAS,CAAC,sBAAsB,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3G,MAAM,WAAW,GAAG,SAAS,CAAC,YAAa,CAAC;IAC5C,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC;IAEjC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC;IACpE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;IAEhE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;QAEvC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7B,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;QAC7B,CAAC;QAED,WAAW,CAAC,CAAC,CAAC,GAAS,EAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;QAC/G,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5B,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;IACzH,CAAC;IAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAE,CAAC;QAC7B,WAAW,CAAC,CAAC,CAAC,GAAS,EAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;IACnH,CAAC;IAED,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAC5B,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC;AACzE,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2033, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.computeShader.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.computeShader.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { ComputeEffect, IComputeEffectCreationOptions, IComputeShaderPath } from \"../../Compute/computeEffect\";\r\nimport type { IComputeContext } from \"../../Compute/IComputeContext\";\r\nimport type { IComputePipelineContext } from \"../../Compute/IComputePipelineContext\";\r\nimport { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { AbstractEngine } from \"../abstractEngine\";\r\nimport type { WebGPUPerfCounter } from \"../WebGPU/webgpuPerfCounter\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\n\r\n/**\r\n * Type used to locate a resource in a compute shader.\r\n * TODO: remove this when browsers support reflection for wgsl shaders\r\n */\r\nexport type ComputeBindingLocation = { group: number; binding: number };\r\n\r\n/**\r\n * Type used to lookup a resource and retrieve its binding location\r\n * TODO: remove this when browsers support reflection for wgsl shaders\r\n */\r\nexport type ComputeBindingMapping = { [key: string]: ComputeBindingLocation };\r\n\r\n/**\r\n * Types of messages that can be generated during compilation\r\n */\r\nexport type ComputeCompilationMessageType = \"error\" | \"warning\" | \"info\";\r\n\r\n/**\r\n * Messages generated during compilation\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface ComputeCompilationMessages {\r\n    /**\r\n     * Number of errors generated during compilation\r\n     */\r\n    numErrors: number;\r\n    /**\r\n     * List of messages generated during compilation\r\n     */\r\n    messages: {\r\n        type: ComputeCompilationMessageType;\r\n        text: string;\r\n        line?: number;\r\n        column?: number;\r\n        length?: number;\r\n        offset?: number;\r\n    }[];\r\n}\r\n\r\n/** @internal */\r\nexport const enum ComputeBindingType {\r\n    Texture = 0,\r\n    StorageTexture = 1,\r\n    UniformBuffer = 2,\r\n    StorageBuffer = 3,\r\n    TextureWithoutSampler = 4,\r\n    Sampler = 5,\r\n    ExternalTexture = 6,\r\n    DataBuffer = 7,\r\n}\r\n\r\n/** @internal */\r\nexport type ComputeBindingList = { [key: string]: { type: ComputeBindingType; object: any; indexInGroupEntries?: number } };\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a new compute effect\r\n         * @param baseName Name of the effect\r\n         * @param options Options used to create the effect\r\n         * @returns The new compute effect\r\n         */\r\n        createComputeEffect(\r\n            baseName:\r\n                | string\r\n                | (IComputeShaderPath & {\r\n                      /**\r\n                       * @internal\r\n                       */\r\n                      computeToken?: string;\r\n                  }),\r\n            options: IComputeEffectCreationOptions\r\n        ): ComputeEffect;\r\n\r\n        /**\r\n         * Creates a new compute pipeline context\r\n         * @returns the new pipeline\r\n         */\r\n        createComputePipelineContext(): IComputePipelineContext;\r\n\r\n        /**\r\n         * Creates a new compute context\r\n         * @returns the new context\r\n         */\r\n        createComputeContext(): IComputeContext | undefined;\r\n\r\n        /**\r\n         * Dispatches a compute shader\r\n         * @param effect The compute effect\r\n         * @param context The compute context\r\n         * @param bindings The list of resources to bind to the shader\r\n         * @param x The number of workgroups to execute on the X dimension\r\n         * @param y The number of workgroups to execute on the Y dimension\r\n         * @param z The number of workgroups to execute on the Z dimension\r\n         * @param bindingsMapping list of bindings mapping (key is property name, value is binding location)\r\n         * @param gpuPerfCounter GPU time computed for the compute shader will be assigned to this object\r\n         */\r\n        computeDispatch(\r\n            effect: ComputeEffect,\r\n            context: IComputeContext,\r\n            bindings: ComputeBindingList,\r\n            x: number,\r\n            y?: number,\r\n            z?: number,\r\n            bindingsMapping?: ComputeBindingMapping,\r\n            gpuPerfCounter?: WebGPUPerfCounter\r\n        ): void;\r\n\r\n        /**\r\n         * Dispatches a compute shader\r\n         * @param effect The compute effect\r\n         * @param context The compute context\r\n         * @param bindings The list of resources to bind to the shader\r\n         * @param x The number of workgroups to execute on the X dimension\r\n         * @param y The number of workgroups to execute on the Y dimension\r\n         * @param z The number of workgroups to execute on the Z dimension\r\n         * @param bindingsMapping list of bindings mapping (key is property name, value is binding location)\r\n         * @param gpuPerfCounter GPU time computed for the compute shader will be assigned to this object\r\n         */\r\n        computeDispatchIndirect(\r\n            effect: ComputeEffect,\r\n            context: IComputeContext,\r\n            bindings: ComputeBindingList,\r\n            buffer: DataBuffer,\r\n            offset?: number,\r\n            bindingsMapping?: ComputeBindingMapping,\r\n            gpuPerfCounter?: WebGPUPerfCounter\r\n        ): void;\r\n\r\n        /**\r\n         * Gets a boolean indicating if all created compute effects are ready\r\n         * @returns true if all effects are ready\r\n         */\r\n        areAllComputeEffectsReady(): boolean;\r\n\r\n        /**\r\n         * Forces the engine to release all cached compute effects. This means that next effect compilation will have to be done completely even if a similar effect was already compiled\r\n         */\r\n        releaseComputeEffects(): void;\r\n\r\n        /** @internal */\r\n        _prepareComputePipelineContext(\r\n            pipelineContext: IComputePipelineContext,\r\n            computeSourceCode: string,\r\n            rawComputeSourceCode: string,\r\n            defines: Nullable<string>,\r\n            entryPoint: string\r\n        ): void;\r\n\r\n        /** @internal */\r\n        _rebuildComputeEffects(): void;\r\n\r\n        /** @internal */\r\n        _executeWhenComputeStateIsCompiled(pipelineContext: IComputePipelineContext, action: (messages: Nullable<ComputeCompilationMessages>) => void): void;\r\n\r\n        /** @internal */\r\n        _releaseComputeEffect(effect: ComputeEffect): void;\r\n\r\n        /** @internal */\r\n        _deleteComputePipelineContext(pipelineContext: IComputePipelineContext): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createComputeEffect = function (baseName: IComputeShaderPath & { computeToken?: string }, options: IComputeEffectCreationOptions): ComputeEffect {\r\n    throw new Error(\"createComputeEffect: This engine does not support compute shaders!\");\r\n};\r\n\r\nThinEngine.prototype.createComputePipelineContext = function (): IComputePipelineContext {\r\n    throw new Error(\"createComputePipelineContext: This engine does not support compute shaders!\");\r\n};\r\n\r\nThinEngine.prototype.createComputeContext = function (): IComputeContext | undefined {\r\n    return undefined;\r\n};\r\n\r\nThinEngine.prototype.computeDispatch = function (\r\n    effect: ComputeEffect,\r\n    context: IComputeContext,\r\n    bindings: ComputeBindingList,\r\n    x: number,\r\n    y?: number,\r\n    z?: number,\r\n    bindingsMapping?: ComputeBindingMapping\r\n): void {\r\n    throw new Error(\"computeDispatch: This engine does not support compute shaders!\");\r\n};\r\nThinEngine.prototype.computeDispatchIndirect = function (\r\n    effect: ComputeEffect,\r\n    context: IComputeContext,\r\n    bindings: ComputeBindingList,\r\n    buffer: DataBuffer,\r\n    offset?: number,\r\n    bindingsMapping?: ComputeBindingMapping\r\n): void {\r\n    throw new Error(\"computeDispatchIndirect: This engine does not support compute shaders!\");\r\n};\r\n\r\nThinEngine.prototype.areAllComputeEffectsReady = function (): boolean {\r\n    return true;\r\n};\r\n\r\nThinEngine.prototype.releaseComputeEffects = function (): void {};\r\n\r\nThinEngine.prototype._prepareComputePipelineContext = function (\r\n    pipelineContext: IComputePipelineContext,\r\n    computeSourceCode: string,\r\n    rawComputeSourceCode: string,\r\n    defines: Nullable<string>,\r\n    entryPoint: string\r\n): void {};\r\n\r\nThinEngine.prototype._rebuildComputeEffects = function (): void {};\r\n\r\nAbstractEngine.prototype._executeWhenComputeStateIsCompiled = function (\r\n    pipelineContext: IComputePipelineContext,\r\n    action: (messages: Nullable<ComputeCompilationMessages>) => void\r\n): void {\r\n    action(null);\r\n};\r\n\r\nThinEngine.prototype._releaseComputeEffect = function (effect: ComputeEffect): void {};\r\n\r\nThinEngine.prototype._deleteComputePipelineContext = function (pipelineContext: IComputePipelineContext): void {};\r\n"], "names": [], "mappings": ";;;AAIA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEtD,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;;;AA4CnD,IAAkB,kBASjB;AATD,CAAA,SAAkB,kBAAkB;IAChC,kBAAA,CAAA,kBAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAW,CAAA;IACX,kBAAA,CAAA,kBAAA,CAAA,iBAAA,GAAA,EAAA,GAAA,gBAAkB,CAAA;IAClB,kBAAA,CAAA,kBAAA,CAAA,gBAAA,GAAA,EAAA,GAAA,eAAiB,CAAA;IACjB,kBAAA,CAAA,kBAAA,CAAA,gBAAA,GAAA,EAAA,GAAA,eAAiB,CAAA;IACjB,kBAAA,CAAA,kBAAA,CAAA,wBAAA,GAAA,EAAA,GAAA,uBAAyB,CAAA;IACzB,kBAAA,CAAA,kBAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAW,CAAA;IACX,kBAAA,CAAA,kBAAA,CAAA,kBAAA,GAAA,EAAA,GAAA,iBAAmB,CAAA;IACnB,kBAAA,CAAA,kBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAc,CAAA;AAClB,CAAC,EATiB,kBAAkB,IAAA,CAAlB,kBAAkB,GAAA,CAAA,CAAA,GASnC;gKAmHD,aAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,SAAU,QAAwD,EAAE,OAAsC;IACjJ,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;AAC1F,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,4BAA4B,GAAG;IAChD,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;AACnG,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG;IACxC,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,eAAe,GAAG,SACnC,MAAqB,EACrB,OAAwB,EACxB,QAA4B,EAC5B,CAAS,EACT,CAAU,EACV,CAAU,EACV,eAAuC;IAEvC,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;AACtF,CAAC,CAAC;gKACF,aAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,SAC3C,MAAqB,EACrB,OAAwB,EACxB,QAA4B,EAC5B,MAAkB,EAClB,MAAe,EACf,eAAuC;IAEvC,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;AAC9F,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG;IAC7C,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG,YAAmB,CAAC,CAAC;gKAElE,aAAU,CAAC,SAAS,CAAC,8BAA8B,GAAG,SAClD,eAAwC,EACxC,iBAAyB,EACzB,oBAA4B,EAC5B,OAAyB,EACzB,UAAkB,GACb,CAAC,CAAC;gKAEX,aAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,YAAmB,CAAC,CAAC;oKAEnE,iBAAc,CAAC,SAAS,CAAC,kCAAkC,GAAG,SAC1D,eAAwC,EACxC,MAAgE;IAEhE,MAAM,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC,CAAC;gKAEF,aAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG,SAAU,MAAqB,GAAS,CAAC,CAAC;gKAEvF,aAAU,CAAC,SAAS,CAAC,6BAA6B,GAAG,SAAU,eAAwC,GAAS,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/engine.textureSelector.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/engine.textureSelector.ts"], "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { Engine } from \"../engine\";\r\n\r\ndeclare module \"../../Engines/engine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Engine {\r\n        /** @internal */\r\n        _excludedCompressedTextures: string[];\r\n\r\n        /** @internal */\r\n        _textureFormatInUse: string;\r\n\r\n        /**\r\n         * Gets the list of texture formats supported\r\n         */\r\n        readonly texturesSupported: Array<string>;\r\n\r\n        /**\r\n         * Gets the texture format in use\r\n         */\r\n        readonly textureFormatInUse: Nullable<string>;\r\n\r\n        /**\r\n         * Set the compressed texture extensions or file names to skip.\r\n         *\r\n         * @param skippedFiles defines the list of those texture files you want to skip\r\n         * Example: [\".dds\", \".env\", \"myfile.png\"]\r\n         */\r\n        setCompressedTextureExclusions(skippedFiles: Array<string>): void;\r\n\r\n        /**\r\n         * Set the compressed texture format to use, based on the formats you have, and the formats\r\n         * supported by the hardware / browser.\r\n         *\r\n         * Khronos Texture Container (.ktx) files are used to support this.  This format has the\r\n         * advantage of being specifically designed for OpenGL.  Header elements directly correspond\r\n         * to API arguments needed to compressed textures.  This puts the burden on the container\r\n         * generator to house the arcane code for determining these for current & future formats.\r\n         *\r\n         * for description see https://www.khronos.org/opengles/sdk/tools/KTX/\r\n         * for file layout see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/\r\n         *\r\n         * Note: The result of this call is not taken into account when a texture is base64.\r\n         *\r\n         * @param formatsAvailable defines the list of those format families you have created\r\n         * on your server.  Syntax: '-' + format family + '.ktx'.  (Case and order do not matter.)\r\n         *\r\n         * Current families are astc, dxt, pvrtc, etc2, & etc1.\r\n         * @returns The extension selected.\r\n         */\r\n        setTextureFormatToUse(formatsAvailable: Array<string>): Nullable<string>;\r\n    }\r\n}\r\n\r\nfunction TransformTextureUrl(this: Engine, url: string): string {\r\n    const excludeFn = (entry: string) => {\r\n        const strRegExPattern: string = \"\\\\b\" + entry + \"\\\\b\";\r\n        return url && (url === entry || url.match(new RegExp(strRegExPattern, \"g\")));\r\n    };\r\n\r\n    if (this._excludedCompressedTextures && this._excludedCompressedTextures.some(excludeFn)) {\r\n        return url;\r\n    }\r\n\r\n    const lastDot = url.lastIndexOf(\".\");\r\n    const lastQuestionMark = url.lastIndexOf(\"?\");\r\n    const querystring = lastQuestionMark > -1 ? url.substring(lastQuestionMark, url.length) : \"\";\r\n    return (lastDot > -1 ? url.substring(0, lastDot) : url) + this._textureFormatInUse + querystring;\r\n}\r\n\r\nObject.defineProperty(Engine.prototype, \"texturesSupported\", {\r\n    get: function (this: Engine) {\r\n        // Intelligently add supported compressed formats in order to check for.\r\n        // Check for ASTC support first as it is most powerful and to be very cross platform.\r\n        // Next PVRTC & DXT, which are probably superior to ETC1/2.\r\n        // Likely no hardware which supports both PVR & DXT, so order matters little.\r\n        // ETC2 is newer and handles ETC1 (no alpha capability), so check for first.\r\n        const texturesSupported: string[] = [];\r\n        if (this._caps.astc) {\r\n            texturesSupported.push(\"-astc.ktx\");\r\n        }\r\n        if (this._caps.s3tc) {\r\n            texturesSupported.push(\"-dxt.ktx\");\r\n        }\r\n        if (this._caps.pvrtc) {\r\n            texturesSupported.push(\"-pvrtc.ktx\");\r\n        }\r\n        if (this._caps.etc2) {\r\n            texturesSupported.push(\"-etc2.ktx\");\r\n        }\r\n        if (this._caps.etc1) {\r\n            texturesSupported.push(\"-etc1.ktx\");\r\n        }\r\n        return texturesSupported;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Engine.prototype, \"textureFormatInUse\", {\r\n    get: function (this: Engine) {\r\n        return this._textureFormatInUse || null;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nEngine.prototype.setCompressedTextureExclusions = function (skippedFiles: Array<string>): void {\r\n    this._excludedCompressedTextures = skippedFiles;\r\n};\r\n\r\nEngine.prototype.setTextureFormatToUse = function (formatsAvailable: Array<string>): Nullable<string> {\r\n    const texturesSupported = this.texturesSupported;\r\n    for (let i = 0, len1 = texturesSupported.length; i < len1; i++) {\r\n        for (let j = 0, len2 = formatsAvailable.length; j < len2; j++) {\r\n            if (texturesSupported[i] === formatsAvailable[j].toLowerCase()) {\r\n                this._transformTextureUrl = TransformTextureUrl.bind(this);\r\n                return (this._textureFormatInUse = texturesSupported[i]);\r\n            }\r\n        }\r\n    }\r\n    // actively set format to nothing, to allow this to be called more than once\r\n    // and possibly fail the 2nd time\r\n    this._textureFormatInUse = \"\";\r\n    this._transformTextureUrl = null;\r\n    return null;\r\n};\r\n"], "names": [], "mappings": ";AACA,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;;AAqDnC,SAAS,mBAAmB,CAAe,GAAW;IAClD,MAAM,SAAS,GAAG,CAAC,KAAa,EAAE,EAAE;QAChC,MAAM,eAAe,GAAW,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;QACtD,OAAO,GAAG,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QACvF,OAAO,GAAG,CAAC;IACf,CAAC;IAED,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACrC,MAAM,gBAAgB,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC9C,MAAM,WAAW,GAAG,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7F,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC;AACrG,CAAC;AAED,MAAM,CAAC,cAAc,6JAAC,SAAM,CAAC,SAAS,EAAE,mBAAmB,EAAE;IACzD,GAAG,EAAE;QACD,wEAAwE;QACxE,qFAAqF;QACrF,2DAA2D;QAC3D,6EAA6E;QAC7E,4EAA4E;QAC5E,MAAM,iBAAiB,GAAa,EAAE,CAAC;QACvC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACnB,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,6JAAC,SAAM,CAAC,SAAS,EAAE,oBAAoB,EAAE;IAC1D,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC;IAC5C,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;4JAEH,SAAM,CAAC,SAAS,CAAC,8BAA8B,GAAG,SAAU,YAA2B;IACnF,IAAI,CAAC,2BAA2B,GAAG,YAAY,CAAC;AACpD,CAAC,CAAC;4JAEF,SAAM,CAAC,SAAS,CAAC,qBAAqB,GAAG,SAAU,gBAA+B;IAC9E,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;IACjD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,CAAE,CAAC;QAC7D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,CAAE,CAAC;YAC5D,IAAI,iBAAiB,CAAC,CAAC,CAAC,KAAK,gBAAgB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC7D,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3D,OAAO,AAAC,IAAI,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;IACL,CAAC;IACD,4EAA4E;IAC5E,iCAAiC;IACjC,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;IAC9B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACjC,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2155, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/Extensions/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/Extensions/index.ts"], "sourcesContent": ["/* eslint-disable import/export */\r\nexport * from \"./engine.alpha\";\r\nexport * from \"./engine.debugging\";\r\nexport * from \"./engine.query\";\r\nexport * from \"./engine.transformFeedback\";\r\nexport * from \"./engine.multiview\";\r\nexport * from \"./engine.rawTexture\";\r\nexport * from \"./engine.dynamicTexture\";\r\nexport * from \"./engine.videoTexture\";\r\nexport * from \"./engine.multiRender\";\r\nexport * from \"./engine.cubeTexture\";\r\nexport * from \"./engine.prefilteredCubeTexture\";\r\nexport * from \"./engine.renderTarget\";\r\nexport * from \"./engine.renderTargetCube\";\r\nexport * from \"./engine.renderTargetTexture\";\r\nexport * from \"./engine.uniformBuffer\";\r\nexport * from \"./engine.dynamicBuffer\";\r\nexport * from \"./engine.readTexture\";\r\nexport * from \"./engine.computeShader\";\r\n\r\n// must import first since nothing references the exports\r\nimport \"./engine.textureSelector\";\r\n// eslint-disable-next-line no-duplicate-imports\r\nexport * from \"./engine.textureSelector\";\r\n"], "names": [], "mappings": "AAAA,gCAAA,EAAkC;AAClC,cAAc,gBAAgB,CAAC;AAC/B,cAAc,oBAAoB,CAAC;AACnC,cAAc,gBAAgB,CAAC;AAC/B,cAAc,4BAA4B,CAAC;AAC3C,cAAc,oBAAoB,CAAC;AACnC,cAAc,qBAAqB,CAAC;AACpC,cAAc,yBAAyB,CAAC;AACxC,cAAc,uBAAuB,CAAC;AACtC,cAAc,sBAAsB,CAAC;AACrC,cAAc,sBAAsB,CAAC;AACrC,cAAc,iCAAiC,CAAC;AAChD,cAAc,uBAAuB,CAAC;AACtC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,8BAA8B,CAAC;AAC7C,cAAc,wBAAwB,CAAC;AACvC,cAAc,wBAAwB,CAAC;AACvC,cAAc,sBAAsB,CAAC;AACrC,cAAc,wBAAwB,CAAC;AAEvC,yDAAyD;AACzD,OAAO,0BAA0B,CAAC", "debugId": null}}]}