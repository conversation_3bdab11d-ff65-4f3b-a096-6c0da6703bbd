{"version": 3, "file": "vertexAnimationBaker.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/BakedVertexAnimation/vertexAnimationBaker.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAC9D,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAExD,OAAO,EAAE,yBAAyB,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAEtF,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,6BAA4B;AAE/C,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC;;;GAGG;AACH,MAAM,OAAO,oBAAoB;IAK7B;;;;OAIG;IACH,YAAY,KAAY,EAAE,cAA+B;QACrD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,cAAc,YAAY,QAAQ,EAAE,CAAC;YACrC,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC;YAChC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QACtB,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,QAAQ,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,MAAwB,EAAE,SAAkB;QAClE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC;QAC1C,MAAM,cAAc,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACxC,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEpG,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,cAAc,GAAG,WAAW,CAAC,CAAC;QAE9H,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QAE9B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACzB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC7D,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACrB,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;gBAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjE,MAAM,IAAI,GAAG,QAAQ,GAAG,cAAc,CAAC;gBACvC,IAAI,SAAS,EAAE,CAAC;oBACZ,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;wBACxB,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;oBAC5C,CAAC,CAAC,CAAC;gBACP,CAAC;qBAAM,CAAC;oBACJ,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;wBACxB,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;oBAC/B,CAAC,CAAC,CAAC;gBACP,CAAC;gBACD,QAAQ,EAAE,CAAC;YACf,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACH,kEAAkE;IAClE,gEAAgE;IACzD,KAAK,CAAC,cAAc,CAAC,MAAwB;QAChD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC;QAE9C,+CAA+C;QAC/C,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,QAAgB,EAAE,OAAuB,EAAE,EAAE,CAAC,QAAQ,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7H,IAAI,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QACjD,CAAC;QAED,sBAAsB;QACtB,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,MAAM,WAAW,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;QACzD,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;QACjD,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QAE9B,oCAAoC;QACpC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YACzB,KAAK,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,EAAE,UAAU,IAAI,KAAK,CAAC,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC;gBACrE,4CAA4C;gBAC5C,MAAM,IAAI,CAAC,2BAA2B,CAAC,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC,CAAC;YACnF,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;;;OAOG;IACK,KAAK,CAAC,2BAA2B,CAAC,UAAwB,EAAE,UAAkB,EAAE,YAAoB;QACxG,OAAO,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE;gBAChF,oBAAoB;gBACpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1E,UAAU,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAEzE,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IACD;;;;;OAKG;IACI,0BAA0B,CAAC,UAAsC;QACpE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC;QAE9C,IAAI,UAAU,YAAY,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,sBAAsB,EAAE,CAAC;gBAC5D,MAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;YAC9F,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,UAAU,CAAC,iBAAiB,CACxC,UAAU,EACV,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,EACnB,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAC7C,IAAI,CAAC,MAAM,EACX,KAAK,EACL,KAAK,EACL,OAAO,CAAC,eAAe,EACvB,UAAU,YAAY,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,sBAAsB,CACtG,CAAC;QACF,OAAO,CAAC,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAC3C,OAAO,OAAO,CAAC;IACnB,CAAC;IACD;;;;OAIG;IACI,gCAAgC,CAAC,UAAwB;QAC5D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC7C,CAAC;QAED,4EAA4E;QAC5E,qBAAqB;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC;QAC9C,MAAM,KAAK,GAAG,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7D,MAAM,IAAI,GAAG;YACT,UAAU,EAAE,yBAAyB,CAAC,UAAU,CAAC;YACjD,KAAK;YACL,MAAM;SACT,CAAC;QACF,OAAO,IAAI,CAAC;IAChB,CAAC;IACD;;;;OAIG;IACI,6BAA6B,CAAC,IAAyB;QAC1D,OAAO,IAAI,YAAY,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACnE,CAAC;IACD;;;;;OAKG;IACI,8BAA8B,CAAC,UAAwB;QAC1D,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7E,CAAC;IACD;;;;OAIG;IACI,2BAA2B,CAAC,IAAY;QAC3C,OAAO,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC;CACJ", "sourcesContent": ["import type { AnimationRange } from \"../Animations/animationRange\";\r\nimport { RawTexture } from \"../Materials/Textures/rawTexture\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { EncodeArrayBufferToBase64, DecodeBase64ToBinary } from \"../Misc/stringTools\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { Skeleton } from \"core/Bones/skeleton\";\r\nimport type { Nullable } from \"core/types\";\r\nimport { ToHalfFloat } from \"../Misc/textureTools\";\r\nimport { Logger } from \"../Misc/logger\";\r\n\r\n/**\r\n * Class to bake vertex animation textures.\r\n * @since 5.0\r\n */\r\nexport class VertexAnimationBaker {\r\n    private _scene: Scene;\r\n    private _mesh: Nullable<Mesh>;\r\n    private _skeleton: Nullable<Skeleton>;\r\n\r\n    /**\r\n     * Create a new VertexAnimationBaker object which can help baking animations into a texture.\r\n     * @param scene Defines the scene the VAT belongs to\r\n     * @param meshOrSkeleton Defines the skeleton or the mesh from which to retrieve the skeleton from.\r\n     */\r\n    constructor(scene: Scene, meshOrSkeleton: Mesh | Skeleton) {\r\n        this._scene = scene;\r\n        if (meshOrSkeleton instanceof Skeleton) {\r\n            this._skeleton = meshOrSkeleton;\r\n            this._mesh = null;\r\n        } else {\r\n            this._mesh = meshOrSkeleton;\r\n            this._skeleton = meshOrSkeleton.skeleton;\r\n        }\r\n    }\r\n\r\n    /**\r\n     *\r\n     * @param ranges Defines the ranges in the animation that will be baked.\r\n     * @param halfFloat If true, the vertex data will be returned as half-float (Uint16Array), otherwise as full float (Float32Array).\r\n     * @returns The array of matrix transforms for each vertex (columns) and frame (rows), as a Float32Array or Uint16Array.\r\n     */\r\n    public bakeVertexDataSync(ranges: AnimationRange[], halfFloat: boolean): Float32Array | Uint16Array {\r\n        if (!this._skeleton) {\r\n            throw new Error(\"No skeleton provided.\");\r\n        }\r\n        const bones = this._skeleton.bones.length;\r\n        const floatsPerFrame = (bones + 1) * 16;\r\n        const totalFrames = ranges.reduce((sum, r) => sum + (Math.floor(r.to) - Math.floor(r.from) + 1), 0);\r\n\r\n        const vertexData = halfFloat ? new Uint16Array(floatsPerFrame * totalFrames) : new Float32Array(floatsPerFrame * totalFrames);\r\n\r\n        let frameIdx = 0;\r\n\r\n        this._skeleton.returnToRest();\r\n\r\n        for (const range of ranges) {\r\n            for (let f = Math.floor(range.from); f <= Math.floor(range.to); f++) {\r\n                this._scene.beginAnimation(this._skeleton, f, f, false, 1.0);\r\n                this._scene.render();\r\n                this._skeleton.computeAbsoluteMatrices(true);\r\n                const matrices = this._skeleton.getTransformMatrices(this._mesh);\r\n                const base = frameIdx * floatsPerFrame;\r\n                if (halfFloat) {\r\n                    matrices.forEach((val, i) => {\r\n                        vertexData[base + i] = ToHalfFloat(val);\r\n                    });\r\n                } else {\r\n                    matrices.forEach((val, i) => {\r\n                        vertexData[base + i] = val;\r\n                    });\r\n                }\r\n                frameIdx++;\r\n            }\r\n        }\r\n\r\n        return vertexData;\r\n    }\r\n\r\n    /**\r\n     * Bakes the animation into the texture. This should be called once, when the\r\n     * scene starts, so the VAT is generated and associated to the mesh.\r\n     * @param ranges Defines the ranges in the animation that will be baked.\r\n     * @returns The array of matrix transforms for each vertex (columns) and frame (rows), as a Float32Array.\r\n     */\r\n    // async function, without Async suffix, to avoid breaking the API\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public async bakeVertexData(ranges: AnimationRange[]): Promise<Float32Array> {\r\n        if (!this._skeleton) {\r\n            throw new Error(\"No skeleton provided.\");\r\n        }\r\n        const boneCount = this._skeleton.bones.length;\r\n\r\n        /** total number of frames in our animations */\r\n        const frameCount = ranges.reduce((previous: number, current: AnimationRange) => previous + current.to - current.from + 1, 0);\r\n\r\n        if (isNaN(frameCount)) {\r\n            throw new Error(\"Invalid animation ranges.\");\r\n        }\r\n\r\n        // reset our loop data\r\n        let textureIndex = 0;\r\n        const textureSize = (boneCount + 1) * 4 * 4 * frameCount;\r\n        const vertexData = new Float32Array(textureSize);\r\n        this._scene.stopAnimation(this._skeleton);\r\n        this._skeleton.returnToRest();\r\n\r\n        // render all frames from our slices\r\n        for (const range of ranges) {\r\n            for (let frameIndex = range.from; frameIndex <= range.to; frameIndex++) {\r\n                // eslint-disable-next-line no-await-in-loop\r\n                await this._executeAnimationFrameAsync(vertexData, frameIndex, textureIndex++);\r\n            }\r\n        }\r\n\r\n        return vertexData;\r\n    }\r\n\r\n    /**\r\n     * Runs an animation frame and stores its vertex data\r\n     *\r\n     * @param vertexData The array to save data to.\r\n     * @param frameIndex Current frame in the skeleton animation to render.\r\n     * @param textureIndex Current index of the texture data.\r\n     * @returns A promise that resolves when the animation frame is done.\r\n     */\r\n    private async _executeAnimationFrameAsync(vertexData: Float32Array, frameIndex: number, textureIndex: number): Promise<void> {\r\n        return await new Promise<void>((resolve, _reject) => {\r\n            this._scene.beginAnimation(this._skeleton, frameIndex, frameIndex, false, 1.0, () => {\r\n                // generate matrices\r\n                const skeletonMatrices = this._skeleton!.getTransformMatrices(this._mesh);\r\n                vertexData.set(skeletonMatrices, textureIndex * skeletonMatrices.length);\r\n\r\n                resolve();\r\n            });\r\n        });\r\n    }\r\n    /**\r\n     * Builds a vertex animation texture given the vertexData in an array.\r\n     * @param vertexData The vertex animation data. You can generate it with bakeVertexData(). You can pass in a Float32Array to return a full precision texture, or a Uint16Array to return a half-float texture.\r\n     * If you pass in a Uint16Array, make sure your device supports half-float textures\r\n     * @returns The vertex animation texture to be used with BakedVertexAnimationManager.\r\n     */\r\n    public textureFromBakedVertexData(vertexData: Float32Array | Uint16Array): RawTexture {\r\n        if (!this._skeleton) {\r\n            throw new Error(\"No skeleton provided.\");\r\n        }\r\n        const boneCount = this._skeleton.bones.length;\r\n\r\n        if (vertexData instanceof Uint16Array) {\r\n            if (!this._scene.getEngine().getCaps().textureHalfFloatRender) {\r\n                Logger.Warn(\"VertexAnimationBaker: Half-float textures are not supported on this device\");\r\n            }\r\n        }\r\n\r\n        const texture = RawTexture.CreateRGBATexture(\r\n            vertexData,\r\n            (boneCount + 1) * 4,\r\n            vertexData.length / ((boneCount + 1) * 4 * 4),\r\n            this._scene,\r\n            false,\r\n            false,\r\n            Texture.NEAREST_NEAREST,\r\n            vertexData instanceof Float32Array ? Constants.TEXTURETYPE_FLOAT : Constants.TEXTURETYPE_HALF_FLOAT\r\n        );\r\n        texture.name = \"VAT\" + this._skeleton.name;\r\n        return texture;\r\n    }\r\n    /**\r\n     * Serializes our vertexData to an object, with a nice string for the vertexData.\r\n     * @param vertexData The vertex array data.\r\n     * @returns This object serialized to a JS dict.\r\n     */\r\n    public serializeBakedVertexDataToObject(vertexData: Float32Array): Record<string, any> {\r\n        if (!this._skeleton) {\r\n            throw new Error(\"No skeleton provided.\");\r\n        }\r\n\r\n        // this converts the float array to a serialized base64 string, ~1.3x larger\r\n        // than the original.\r\n        const boneCount = this._skeleton.bones.length;\r\n        const width = (boneCount + 1) * 4;\r\n        const height = vertexData.length / ((boneCount + 1) * 4 * 4);\r\n        const data = {\r\n            vertexData: EncodeArrayBufferToBase64(vertexData),\r\n            width,\r\n            height,\r\n        };\r\n        return data;\r\n    }\r\n    /**\r\n     * Loads previously baked data.\r\n     * @param data The object as serialized by serializeBakedVertexDataToObject()\r\n     * @returns The array of matrix transforms for each vertex (columns) and frame (rows), as a Float32Array.\r\n     */\r\n    public loadBakedVertexDataFromObject(data: Record<string, any>): Float32Array {\r\n        return new Float32Array(DecodeBase64ToBinary(data.vertexData));\r\n    }\r\n    /**\r\n     * Serializes our vertexData to a JSON string, with a nice string for the vertexData.\r\n     * Should be called right after bakeVertexData().\r\n     * @param vertexData The vertex array data.\r\n     * @returns This object serialized to a safe string.\r\n     */\r\n    public serializeBakedVertexDataToJSON(vertexData: Float32Array): string {\r\n        return JSON.stringify(this.serializeBakedVertexDataToObject(vertexData));\r\n    }\r\n    /**\r\n     * Loads previously baked data in string format.\r\n     * @param json The json string as serialized by serializeBakedVertexDataToJSON().\r\n     * @returns The array of matrix transforms for each vertex (columns) and frame (rows), as a Float32Array.\r\n     */\r\n    public loadBakedVertexDataFromJSON(json: string): Float32Array {\r\n        return this.loadBakedVertexDataFromObject(JSON.parse(json));\r\n    }\r\n}\r\n"]}