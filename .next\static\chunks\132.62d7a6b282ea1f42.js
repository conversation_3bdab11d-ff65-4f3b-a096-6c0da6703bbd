"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[132],{29132:(e,t,i)=>{i.d(t,{default:()=>a});var n=i(23377);i(86951),i(69802),i(81089),i(2575);class a{async initialize(){try{this.setupAdvancedLighting(),this.createMaterialShowcase(),this.setupAdvancedPostProcessing(),this.createAdvancedParticles(),this.options.enableWebXR&&await this.setupWebXR(),this.options.enableAudioReactive&&await this.setupAudioReactive(),this.startRenderLoop(),this.options.showPerformanceStats&&this.setupPerformanceMonitoring(),console.log("Advanced 3D Showcase initialized successfully")}catch(e){console.error("Error initializing Advanced 3D Showcase:",e)}}setupAdvancedLighting(){let e=new n.g4z("hemisphericLight",new n.Pq0(0,1,0),this.scene);e.intensity=.3,e.diffuse=new n.v9j(.2,.4,.8),e.specular=new n.v9j(.1,.2,.4);let t=new n.ZyN("directionalLight",new n.Pq0(-1,-1,-1),this.scene);t.intensity=1.2,t.diffuse=new n.v9j(1,.9,.8),t.specular=new n.v9j(1,1,1);let i=new n.owA(2048,t);i.useExponentialShadowMap=!0,i.darkness=.3;let a=new n.nCl("spotLight1",new n.Pq0(-10,15,-10),new n.Pq0(1,-1,1),Math.PI/3,2,this.scene);a.intensity=.8,a.diffuse=new n.v9j(0,1,1);let s=new n.nCl("spotLight2",new n.Pq0(10,15,10),new n.Pq0(-1,-1,-1),Math.PI/3,2,this.scene);s.intensity=.8,s.diffuse=new n.v9j(1,0,1);let r=new n.HiM("pointLight1",new n.Pq0(0,5,0),this.scene);r.intensity=1.5,r.diffuse=new n.v9j(1,.5,0),r.range=20,n.X55.CreateAndStartAnimation("pointLightAnimation",r,"position",30,120,r.position,new n.Pq0(8,5,8),n.X55.ANIMATIONLOOPMODE_YOYO)}createMaterialShowcase(){[{type:"sphere",position:new n.Pq0(-8,2,0),material:"chrome"},{type:"box",position:new n.Pq0(-4,2,0),material:"gold"},{type:"cylinder",position:new n.Pq0(0,2,0),material:"glass"},{type:"torus",position:new n.Pq0(4,2,0),material:"carbon"},{type:"dodecahedron",position:new n.Pq0(8,2,0),material:"iridescent"},{type:"ground",position:new n.Pq0(0,0,0),material:"marble"}].forEach((e,t)=>{let i;switch(e.type){case"sphere":i=n.PeD.CreateSphere("showcase_sphere_".concat(t),{diameter:3,segments:32},this.scene);break;case"box":i=n.PeD.CreateBox("showcase_box_".concat(t),{size:2.5},this.scene);break;case"cylinder":i=n.PeD.CreateCylinder("showcase_cylinder_".concat(t),{height:3,diameter:2.5,tessellation:24},this.scene);break;case"torus":i=n.PeD.CreateTorus("showcase_torus_".concat(t),{diameter:3,thickness:1,tessellation:32},this.scene);break;case"dodecahedron":i=n.PeD.CreatePolyhedron("showcase_dodecahedron_".concat(t),{type:2,size:1.5},this.scene);break;case"ground":i=n.PeD.CreateGround("showcase_ground_".concat(t),{width:30,height:30,subdivisions:32},this.scene);break;default:return}i.position=e.position,i.material=this.createAdvancedMaterial(e.material,"".concat(e.material,"_").concat(t)),"ground"!==e.type&&(n.X55.CreateAndStartAnimation("float_".concat(t),i,"position.y",30,120+10*t,i.position.y,i.position.y+1+.3*Math.sin(t),n.X55.ANIMATIONLOOPMODE_YOYO),n.X55.CreateAndStartAnimation("rotate_".concat(t),i,"rotation.y",30,180+20*t,0,2*Math.PI,n.X55.ANIMATIONLOOPMODE_CYCLE)),this.showcaseObjects.push(i)})}createAdvancedMaterial(e,t){let i=new n.YOq(t,this.scene),a=this.createEnvironmentTexture();switch(this.scene.environmentTexture=a,e){case"chrome":i.albedoColor=new n.v9j(.9,.9,.9),i.metallic=1,i.roughness=.05,i.environmentIntensity=1.5;break;case"gold":i.albedoColor=new n.v9j(1,.8,.2),i.metallic=1,i.roughness=.1,i.environmentIntensity=1.2;break;case"glass":i.albedoColor=new n.v9j(.95,.98,1),i.metallic=0,i.roughness=0,i.alpha=.15,i.indexOfRefraction=1.52,i.linkRefractionWithTransparency=!0,i.subSurface.isRefractionEnabled=!0,i.subSurface.refractionIntensity=1;break;case"carbon":i.albedoColor=new n.v9j(.1,.1,.1),i.metallic=.8,i.roughness=.3,i.anisotropy.isEnabled=!0,i.anisotropy.intensity=1,i.anisotropy.direction=new n.I9Y(1,0);break;case"iridescent":i.albedoColor=new n.v9j(.05,.05,.05),i.metallic=1,i.roughness=0,i.iridescence.isEnabled=!0,i.iridescence.intensity=1,i.iridescence.indexOfRefraction=1.3,i.iridescence.minimumThickness=100,i.iridescence.maximumThickness=400;break;case"marble":i.albedoColor=new n.v9j(.9,.9,.85),i.metallic=0,i.roughness=.6,i.subSurface.isTranslucencyEnabled=!0,i.subSurface.translucencyIntensity=.3,i.subSurface.tintColor=new n.v9j(.95,.95,.9);let s=this.createMarbleTexture();i.albedoTexture=s,i.bumpTexture=s;break;default:i.albedoColor=new n.v9j(.5,.5,.5),i.metallic=.5,i.roughness=.5}return i}createEnvironmentTexture(){return n.b4q.CreateFromImages([this.createSkyTexture("px"),this.createSkyTexture("nx"),this.createSkyTexture("py"),this.createSkyTexture("ny"),this.createSkyTexture("pz"),this.createSkyTexture("nz")],this.scene)}createSkyTexture(e){let t=document.createElement("canvas");t.width=512,t.height=512;let i=t.getContext("2d"),n=i.createRadialGradient(256,256,0,256,256,512);n.addColorStop(0,"#001a33"),n.addColorStop(.3,"#003366"),n.addColorStop(.6,"#001122"),n.addColorStop(1,"#000011"),i.fillStyle=n,i.fillRect(0,0,512,512);for(let e=0;e<150;e++){let e=512*Math.random(),t=512*Math.random(),n=Math.random(),a=2*Math.random()+1;i.fillStyle="rgba(".concat(100+155*n,", ").concat(150+105*n,", 255, ").concat(n,")"),i.fillRect(e,t,a,a)}return t.toDataURL()}createMarbleTexture(){let e=new n.RCS("marbleTexture",{width:512,height:512},this.scene),t=e.getContext(),i=new ImageData(512,512),a=i.data;for(let e=0;e<512;e++)for(let t=0;t<512;t++){let i=(512*e+t)*4,n=Math.floor(200+55*((.5*Math.sin(.01*t+.01*e)+.5)*(.3*Math.sin(.02*t+.005*e)+.7)*(.2*Math.sin(.005*t+.02*e)+.8)));a[i]=n,a[i+1]=n,a[i+2]=Math.floor(.95*n),a[i+3]=255}return t.putImageData(i,0,0),e.update(),e}setupAdvancedPostProcessing(){try{this.renderingPipeline=new n.eEV("advancedPipeline",!0,this.scene,[this.camera]),this.renderingPipeline&&(this.renderingPipeline.bloomEnabled=!0,this.renderingPipeline.bloomThreshold=.7,this.renderingPipeline.bloomWeight=.4,this.renderingPipeline.bloomKernel=64,this.renderingPipeline.bloomScale=.6,this.renderingPipeline.imageProcessing&&(this.renderingPipeline.imageProcessing.toneMappingEnabled=!0,this.renderingPipeline.imageProcessing.toneMappingType=1),this.renderingPipeline.fxaaEnabled=!0,this.renderingPipeline.chromaticAberrationEnabled=!0,this.renderingPipeline.chromaticAberration.aberrationAmount=20,this.renderingPipeline.chromaticAberration.radialIntensity=.8,this.renderingPipeline.grainEnabled=!0,this.renderingPipeline.grain.intensity=8,this.renderingPipeline.grain.animated=!0,this.renderingPipeline.imageProcessing&&(this.renderingPipeline.imageProcessing.vignetteEnabled=!0,this.renderingPipeline.imageProcessing.vignetteStretch=.15,this.renderingPipeline.imageProcessing.vignetteWeight=1.2,this.renderingPipeline.imageProcessing.vignetteColor=new n.ov8(0,0,0,0)),this.renderingPipeline.depthOfFieldEnabled=!0,this.renderingPipeline.depthOfFieldBlurLevel=0,this.renderingPipeline.depthOfField.focusDistance=2e3,this.renderingPipeline.depthOfField.focalLength=50,this.renderingPipeline.depthOfField.fStop=1.4),console.log("Advanced post-processing pipeline setup complete")}catch(e){console.error("Error setting up post-processing:",e)}}createAdvancedParticles(){try{let e=new n.AqF("advancedParticles",{capacity:5e4},this.scene);e.emitter=n.Pq0.Zero(),e.minEmitBox=new n.Pq0(-15,0,-15),e.maxEmitBox=new n.Pq0(15,0,15),e.particleTexture=this.createParticleTexture(),e.emitRate=1e3,e.minLifeTime=2,e.maxLifeTime=8,e.minSize=.1,e.maxSize=.8,e.minInitialRotation=0,e.maxInitialRotation=2*Math.PI,e.minAngularSpeed=-Math.PI,e.maxAngularSpeed=Math.PI,e.direction1=new n.Pq0(-1,1,-1),e.direction2=new n.Pq0(1,1,1),e.minEmitPower=2,e.maxEmitPower=6,e.updateSpeed=.02,e.gravity=new n.Pq0(0,-2,0),e.color1=new n.ov8(0,1,1,1),e.color2=new n.ov8(1,0,1,1),e.colorDead=new n.ov8(0,0,0,0),e.blendMode=n.okU.BLENDMODE_ONEONE,e.start(),this.particleSystems.push(e),console.log("Advanced particle system created")}catch(e){console.error("Error creating particle system:",e)}}createParticleTexture(){let e=new n.RCS("particleTexture",{width:64,height:64},this.scene),t=e.getContext(),i=t.createRadialGradient(32,32,0,32,32,32);return i.addColorStop(0,"rgba(255, 255, 255, 1)"),i.addColorStop(.3,"rgba(0, 255, 255, 0.8)"),i.addColorStop(.6,"rgba(255, 0, 255, 0.4)"),i.addColorStop(1,"rgba(0, 0, 0, 0)"),t.fillStyle=i,t.fillRect(0,0,64,64),e.update(),e}async setupWebXR(){try{"xr"in navigator&&(this.webXRExperience=await this.scene.createDefaultXRExperienceAsync({floorMeshes:this.showcaseObjects.filter(e=>e.name.includes("ground"))}),this.webXRExperience&&(this.webXRExperience.baseExperience.featuresManager.enableFeature(n._qY.HAND_TRACKING,"latest"),this.webXRExperience.baseExperience.featuresManager.enableFeature(n._qY.HIT_TEST,"latest"),console.log("WebXR experience initialized")))}catch(e){console.error("Error setting up WebXR:",e)}}async setupAudioReactive(){try{let e=await navigator.mediaDevices.getUserMedia({audio:!0});this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.audioContext.createMediaStreamSource(e),this.audioAnalyser=new n.KSX(this.scene),console.log("Audio reactive features initialized")}catch(e){console.error("Error setting up audio reactive features:",e)}}startRenderLoop(){this.engine.runRenderLoop(()=>{this.scene&&(this.audioAnalyser&&this.options.enableAudioReactive&&this.updateAudioReactiveEffects(),this.scene.render())})}updateAudioReactiveEffects(){if(this.audioAnalyser)try{let e=this.audioAnalyser.getByteFrequencyData(),t=e.reduce((e,t)=>e+t)/e.length/255;this.particleSystems.forEach(e=>{e instanceof n.AqF&&(e.emitRate=500+2e3*t)}),this.renderingPipeline&&(this.renderingPipeline.bloomWeight=.2+.6*t,this.renderingPipeline.chromaticAberration.aberrationAmount=10+40*t),this.showcaseObjects.forEach((e,i)=>{if(!e.name.includes("ground")){let i=1+.3*t;e.scaling=new n.Pq0(i,i,i)}})}catch(e){console.error("Error updating audio reactive effects:",e)}}setupPerformanceMonitoring(){setInterval(()=>{this.performanceStats.fps=Math.round(this.engine.getFps()),this.performanceStats.drawCalls=this.scene.getActiveMeshes().length,this.performanceStats.triangles=this.scene.getTotalVertices()},1e3)}getPerformanceStats(){return this.performanceStats}async toggleWebXR(){if(this.webXRExperience)try{0===this.webXRExperience.baseExperience.state?await this.webXRExperience.baseExperience.enterXRAsync("immersive-ar","local-floor"):await this.webXRExperience.baseExperience.exitXRAsync()}catch(e){console.error("Error toggling WebXR:",e)}}toggleAudioReactive(){this.options.enableAudioReactive=!this.options.enableAudioReactive,console.log("Audio reactive mode:",this.options.enableAudioReactive?"enabled":"disabled")}destroy(){this.particleSystems.forEach(e=>e.dispose()),this.animationGroups.forEach(e=>e.dispose()),this.showcaseObjects.forEach(e=>e.dispose()),this.renderingPipeline&&this.renderingPipeline.dispose(),this.webXRExperience&&this.webXRExperience.dispose(),this.audioContext&&this.audioContext.close(),this.scene&&this.scene.dispose(),this.engine&&this.engine.dispose(),console.log("Advanced 3D Showcase destroyed")}constructor(e,t={}){this.renderingPipeline=null,this.webXRExperience=null,this.audioAnalyser=null,this.audioContext=null,this.showcaseObjects=[],this.particleSystems=[],this.animationGroups=[],this.performanceStats={fps:0,drawCalls:0,triangles:0},this.canvas=e,this.options=t,this.engine=new n.N$8(e,!0,{adaptToDeviceRatio:!0,antialias:!0,powerPreference:"high-performance",xrCompatible:t.enableWebXR}),this.scene=new n.Z58(this.engine),this.scene.useRightHandedSystem=!0,this.camera=new n.Lqh("showcaseCamera",-Math.PI/2,Math.PI/2.5,25,n.Pq0.Zero(),this.scene),this.camera.attachControl(e,!0),this.camera.wheelPrecision=50,this.camera.minZ=.1,this.camera.maxZ=1e3,this.initialize()}}}}]);