{"version": 3, "file": "engine.dynamicTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.dynamicTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,sCAAkC;AAC7D,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAuClG,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,KAAa,EAAE,MAAc,EAAE,eAAwB,EAAE,YAAoB;IAC/H,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,wCAAgC,CAAC;IACzE,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;IAE5B,IAAI,eAAe,EAAE,CAAC;QAClB,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC1F,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACjG,CAAC;IAED,6BAA6B;IAC7B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;IACxB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IAEpC,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAEtD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,UACxC,OAAkC,EAClC,MAAmB,EACnB,OAAiB,EACjB,cAAuB,KAAK,EAC5B,MAAe,EACf,mBAA4B,KAAK;AACjC,6DAA6D;AAC7D,uBAAgC,KAAK;IAErC,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,OAAO;IACX,CAAC;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC;IAE7B,MAAM,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;IAE9F,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAErE,IAAI,WAAW,EAAE,CAAC;QACd,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAEtF,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAwB,CAAC,CAAC;IAE1F,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC1B,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QACd,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACT,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IAC5B,CAAC;IAED,OAAO,CAAC,qBAAqB,GAAG,MAAM,CAAC;IACvC,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACnC,OAAO,CAAC,OAAO,GAAG,OAAO,IAAI,KAAK,CAAC;IACnC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC", "sourcesContent": ["import { GetExponentOfTwo } from \"core/Misc/tools.functions\";\r\nimport { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport type { ImageSource, Nullable } from \"../../types\";\r\nimport type { ICanvas } from \"../ICanvas\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a dynamic texture\r\n         * @param width defines the width of the texture\r\n         * @param height defines the height of the texture\r\n         * @param generateMipMaps defines if the engine should generate the mip levels\r\n         * @param samplingMode defines the required sampling mode (Texture.NEAREST_SAMPLINGMODE by default)\r\n         * @returns the dynamic texture inside an InternalTexture\r\n         */\r\n        createDynamicTexture(width: number, height: number, generateMipMaps: boolean, samplingMode: number): InternalTexture;\r\n\r\n        /**\r\n         * Update the content of a dynamic texture\r\n         * @param texture defines the texture to update\r\n         * @param source defines the source containing the data\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param premulAlpha defines if alpha is stored as premultiplied\r\n         * @param format defines the format of the data\r\n         * @param forceBindTexture if the texture should be forced to be bound eg. after a graphics context loss (Default: false)\r\n         * @param allowGPUOptimization true to allow some specific GPU optimizations (subject to engine feature \"allowGPUOptimizationsForGUI\" being true)\r\n         */\r\n        updateDynamicTexture(\r\n            texture: Nullable<InternalTexture>,\r\n            source: ImageSource | ICanvas,\r\n            invertY?: boolean,\r\n            premulAlpha?: boolean,\r\n            format?: number,\r\n            forceBindTexture?: boolean,\r\n            allowGPUOptimization?: boolean\r\n        ): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createDynamicTexture = function (width: number, height: number, generateMipMaps: boolean, samplingMode: number): InternalTexture {\r\n    const texture = new InternalTexture(this, InternalTextureSource.Dynamic);\r\n    texture.baseWidth = width;\r\n    texture.baseHeight = height;\r\n\r\n    if (generateMipMaps) {\r\n        width = this.needPOTTextures ? GetExponentOfTwo(width, this._caps.maxTextureSize) : width;\r\n        height = this.needPOTTextures ? GetExponentOfTwo(height, this._caps.maxTextureSize) : height;\r\n    }\r\n\r\n    //  this.resetTextureCache();\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.isReady = false;\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n\r\n    this.updateTextureSamplingMode(samplingMode, texture);\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    return texture;\r\n};\r\n\r\nThinEngine.prototype.updateDynamicTexture = function (\r\n    texture: Nullable<InternalTexture>,\r\n    source: ImageSource,\r\n    invertY?: boolean,\r\n    premulAlpha: boolean = false,\r\n    format?: number,\r\n    forceBindTexture: boolean = false,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    allowGPUOptimization: boolean = false\r\n): void {\r\n    if (!texture) {\r\n        return;\r\n    }\r\n\r\n    const gl = this._gl;\r\n    const target = gl.TEXTURE_2D;\r\n\r\n    const wasPreviouslyBound = this._bindTextureDirectly(target, texture, true, forceBindTexture);\r\n\r\n    this._unpackFlipY(invertY === undefined ? texture.invertY : invertY);\r\n\r\n    if (premulAlpha) {\r\n        gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, 1);\r\n    }\r\n\r\n    const textureType = this._getWebGLTextureType(texture.type);\r\n    const glformat = this._getInternalFormat(format ? format : texture.format);\r\n    const internalFormat = this._getRGBABufferInternalSizedFormat(texture.type, glformat);\r\n\r\n    gl.texImage2D(target, 0, internalFormat, glformat, textureType, source as TexImageSource);\r\n\r\n    if (texture.generateMipMaps) {\r\n        gl.generateMipmap(target);\r\n    }\r\n\r\n    if (!wasPreviouslyBound) {\r\n        this._bindTextureDirectly(target, null);\r\n    }\r\n\r\n    if (premulAlpha) {\r\n        gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, 0);\r\n    }\r\n\r\n    if (format) {\r\n        texture.format = format;\r\n    }\r\n\r\n    texture._dynamicTextureSource = source;\r\n    texture._premulAlpha = premulAlpha;\r\n    texture.invertY = invertY || false;\r\n    texture.isReady = true;\r\n};\r\n"]}