{"version": 3, "file": "engine.cubeTexture.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.cubeTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,6CAA6C,CAAC;AAErG,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAG5C,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;AAG7D,OAAO,EAAE,gBAAgB,EAAE,kCAAsC;AA2KjE,gBAAgB,CAAC,SAAS,CAAC,8BAA8B,GAAG,UAAU,IAAY,EAAE,OAAoC;IACpH,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,6CAAoC,CAAC,qCAA4B,CAAC,CAAC;IAE9I,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;IAC9B,eAAe,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAEtC,MAAM,eAAe,GAAG;QACpB,iBAAiB,EAAE,KAAK;QACxB,kBAAkB,EAAE,CAAC;QACrB,eAAe,EAAE,KAAK;QACtB,OAAO,EAAE,CAAC;QACV,kBAAkB,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,2BAA2B;QAC9H,GAAG,OAAO;KACb,CAAC;IAEF,eAAe,CAAC,MAAM,GAAG,eAAe,CAAC,kBAAkB,CAAC;IAE5D,IAAI,CAAC,yBAAyB,CAAC,eAAe,EAAE,IAAI,EAAE,eAAe,CAAC,iBAAiB,EAAE,eAAe,CAAC,kBAAkB,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC;IAEtJ,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,eAAe,CAAC,CAAC;IAExE,0HAA0H;IAC1H,MAAM,iBAAiB,GAAG,eAAe,CAAC,gBAAyC,CAAC;IAEpF,eAAe,CAAC,IAAI,GAAG,mBAAmB,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAE9F,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAElD,OAAO,eAAe,CAAC;AAC3B,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAC3C,OAAe,EACf,KAAsB,EACtB,KAAyB,EACzB,QAAkB,EAClB,SAAyC,IAAI,EAC7C,UAAiE,IAAI,EACrE,MAAe,EACf,kBAAuB,IAAI,EAC3B,oBAA6B,KAAK,EAClC,WAAmB,CAAC,EACpB,YAAoB,CAAC,EACrB,WAAsC,IAAI,EAC1C,aAAmB,EACnB,aAAa,GAAG,KAAK,EACrB,SAAoC,IAAI;IAExC,OAAO,IAAI,CAAC,qBAAqB,CAC7B,OAAO,EACP,KAAK,EACL,KAAK,EACL,CAAC,CAAC,QAAQ,EACV,MAAM,EACN,OAAO,EACP,MAAM,EACN,eAAe,EACf,iBAAiB,EACjB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,CAAC,OAAwB,EAAE,IAAwC,EAAE,EAAE;QACnE,MAAM,YAAY,GAAG,IAAqB,CAAC,CAAC,oDAAoD;QAChG,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACpC,MAAM,MAAM,GAAG,KAAK,CAAC;QAErB,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;QAClD,OAAO,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC,CAAC;QAE9B,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEzG,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,YAAY,EAAE,iBAAiB,CAAC,kBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzJ,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAEvB,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEnC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;QACb,CAAC;IACL,CAAC,EACD,CAAC,CAAC,aAAa,EACf,MAAM,CACT,CAAC;AACN,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,OAAwB,EAAE,UAAmB,EAAE,QAAiB;IAC5H,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC;IACvH,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;IAC3D,OAAO,CAAC,YAAY,GAAG,SAAS,CAAC,yBAAyB,CAAC;IAC3D,IAAI,QAAQ,EAAE,CAAC;QACX,OAAO,CAAC,YAAY,GAAG,QAAQ,CAAC;IACpC,CAAC;AACL,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,OAAwB;IACrF,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAG,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,CAAC;QAEhE,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { Constants } from \"../../constants\";\r\nimport type { DepthTextureCreationOptions } from \"../../../Materials/Textures/textureCreationOptions\";\r\nimport type { WebGPUHardwareTexture } from \"../webgpuHardwareTexture\";\r\nimport { WebGPUTextureHelper } from \"../webgpuTextureHelper\";\r\n\r\nimport type { Scene } from \"../../../scene\";\r\nimport { ThinWebGPUEngine } from \"core/Engines/thinWebGPUEngine\";\r\n\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * @internal\r\n         */\r\n        _setCubeMapTextureParams(texture: InternalTexture, loadMipmap: boolean, maxLevel?: number): void;\r\n\r\n        /**\r\n         * Creates a depth stencil cube texture.\r\n         * This is only available in WebGL 2.\r\n         * @param size The size of face edge in the cube texture.\r\n         * @param options The options defining the cube texture.\r\n         * @returns The cube texture\r\n         */\r\n        _createDepthStencilCubeTexture(size: number, options: DepthTextureCreationOptions): InternalTexture;\r\n\r\n        /**\r\n         * Creates a cube texture\r\n         * @param rootUrl defines the url where the files to load is located\r\n         * @param scene defines the current scene\r\n         * @param files defines the list of files to load (1 per face)\r\n         * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @param createPolynomials if a polynomial sphere should be created for the cube texture\r\n         * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\r\n         * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\r\n         * @param fallback defines texture to use while falling back when (compressed) texture file not found.\r\n         * @param loaderOptions options to be passed to the loader\r\n         * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n         * @param buffer defines the data buffer to load instead of loading the rootUrl\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean | undefined,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number,\r\n            fallback: Nullable<InternalTexture>,\r\n            loaderOptions: any,\r\n            useSRGBBuffer: boolean,\r\n            buffer: Nullable<ArrayBufferView>\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Creates a cube texture\r\n         * @param rootUrl defines the url where the files to load is located\r\n         * @param scene defines the current scene\r\n         * @param files defines the list of files to load (1 per face)\r\n         * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Creates a cube texture\r\n         * @param rootUrl defines the url where the files to load is located\r\n         * @param scene defines the current scene\r\n         * @param files defines the list of files to load (1 per face)\r\n         * @param noMipmap defines a boolean indicating that no mipmaps shall be generated (false by default)\r\n         * @param onLoad defines an optional callback raised when the texture is loaded\r\n         * @param onError defines an optional callback raised if there is an issue to load the texture\r\n         * @param format defines the format of the data\r\n         * @param forcedExtension defines the extension to use to pick the right loader\r\n         * @param createPolynomials if a polynomial sphere should be created for the cube texture\r\n         * @param lodScale defines the scale applied to environment texture. This manages the range of LOD level used for IBL according to the roughness\r\n         * @param lodOffset defines the offset applied to environment texture. This manages first LOD level used for IBL according to the roughness\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createCubeTexture(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number\r\n        ): InternalTexture;\r\n\r\n        /** @internal */\r\n        createCubeTextureBase(\r\n            rootUrl: string,\r\n            scene: Nullable<Scene>,\r\n            files: Nullable<string[]>,\r\n            noMipmap: boolean,\r\n            onLoad: Nullable<(data?: any) => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            format: number | undefined,\r\n            forcedExtension: any,\r\n            createPolynomials: boolean,\r\n            lodScale: number,\r\n            lodOffset: number,\r\n            fallback: Nullable<InternalTexture>,\r\n            beforeLoadCubeDataCallback: Nullable<(texture: InternalTexture, data: ArrayBufferView | ArrayBufferView[]) => void>,\r\n            imageHandler: Nullable<(texture: InternalTexture, imgs: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n            useSRGBBuffer: boolean,\r\n            buffer: Nullable<ArrayBufferView>\r\n        ): InternalTexture;\r\n\r\n        /** @internal */\r\n        _partialLoadFile(\r\n            url: string,\r\n            index: number,\r\n            loadedFiles: ArrayBuffer[],\r\n            onfinish: (files: ArrayBuffer[]) => void,\r\n            onErrorCallBack: Nullable<(message?: string, exception?: any) => void>\r\n        ): void;\r\n\r\n        /** @internal */\r\n        _cascadeLoadFiles(scene: Nullable<Scene>, onfinish: (images: ArrayBuffer[]) => void, files: string[], onError: Nullable<(message?: string, exception?: any) => void>): void;\r\n\r\n        /** @internal */\r\n        _cascadeLoadImgs(\r\n            scene: Nullable<Scene>,\r\n            texture: InternalTexture,\r\n            onfinish: Nullable<(texture: InternalTexture, images: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n            files: string[],\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            mimeType?: string\r\n        ): void;\r\n\r\n        /** @internal */\r\n        _partialLoadImg(\r\n            url: string,\r\n            index: number,\r\n            loadedImages: HTMLImageElement[] | ImageBitmap[],\r\n            scene: Nullable<Scene>,\r\n            texture: InternalTexture,\r\n            onfinish: Nullable<(texture: InternalTexture, images: HTMLImageElement[] | ImageBitmap[]) => void>,\r\n            onErrorCallBack: Nullable<(message?: string, exception?: any) => void>,\r\n            mimeType?: string\r\n        ): void;\r\n\r\n        /**\r\n         * Force the mipmap generation for the given render target texture\r\n         * @param texture defines the render target texture to use\r\n         * @param unbind defines whether or not to unbind the texture after generation. Defaults to true.\r\n         */\r\n        generateMipMapsForCubemap(texture: InternalTexture, unbind?: boolean): void;\r\n    }\r\n}\r\n\r\nThinWebGPUEngine.prototype._createDepthStencilCubeTexture = function (size: number, options: DepthTextureCreationOptions): InternalTexture {\r\n    const internalTexture = new InternalTexture(this, options.generateStencil ? InternalTextureSource.DepthStencil : InternalTextureSource.Depth);\r\n\r\n    internalTexture.isCube = true;\r\n    internalTexture.label = options.label;\r\n\r\n    const internalOptions = {\r\n        bilinearFiltering: false,\r\n        comparisonFunction: 0,\r\n        generateStencil: false,\r\n        samples: 1,\r\n        depthTextureFormat: options.generateStencil ? Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 : Constants.TEXTUREFORMAT_DEPTH32_FLOAT,\r\n        ...options,\r\n    };\r\n\r\n    internalTexture.format = internalOptions.depthTextureFormat;\r\n\r\n    this._setupDepthStencilTexture(internalTexture, size, internalOptions.bilinearFiltering, internalOptions.comparisonFunction, internalOptions.samples);\r\n\r\n    this._textureHelper.createGPUTextureForInternalTexture(internalTexture);\r\n\r\n    // Now that the hardware texture is created, we can retrieve the GPU format and set the right type to the internal texture\r\n    const gpuTextureWrapper = internalTexture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n    internalTexture.type = WebGPUTextureHelper.GetTextureTypeFromFormat(gpuTextureWrapper.format);\r\n\r\n    this._internalTexturesCache.push(internalTexture);\r\n\r\n    return internalTexture;\r\n};\r\n\r\nThinWebGPUEngine.prototype.createCubeTexture = function (\r\n    rootUrl: string,\r\n    scene: Nullable<Scene>,\r\n    files: Nullable<string[]>,\r\n    noMipmap?: boolean,\r\n    onLoad: Nullable<(data?: any) => void> = null,\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    format?: number,\r\n    forcedExtension: any = null,\r\n    createPolynomials: boolean = false,\r\n    lodScale: number = 0,\r\n    lodOffset: number = 0,\r\n    fallback: Nullable<InternalTexture> = null,\r\n    loaderOptions?: any,\r\n    useSRGBBuffer = false,\r\n    buffer: Nullable<ArrayBufferView> = null\r\n): InternalTexture {\r\n    return this.createCubeTextureBase(\r\n        rootUrl,\r\n        scene,\r\n        files,\r\n        !!noMipmap,\r\n        onLoad,\r\n        onError,\r\n        format,\r\n        forcedExtension,\r\n        createPolynomials,\r\n        lodScale,\r\n        lodOffset,\r\n        fallback,\r\n        null,\r\n        (texture: InternalTexture, imgs: HTMLImageElement[] | ImageBitmap[]) => {\r\n            const imageBitmaps = imgs as ImageBitmap[]; // we will always get an ImageBitmap array in WebGPU\r\n            const width = imageBitmaps[0].width;\r\n            const height = width;\r\n\r\n            this._setCubeMapTextureParams(texture, !noMipmap);\r\n            texture.format = format ?? -1;\r\n\r\n            const gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture, width, height);\r\n\r\n            this._textureHelper.updateCubeTextures(imageBitmaps, gpuTextureWrapper.underlyingResource!, width, height, gpuTextureWrapper.format, false, false, 0, 0);\r\n\r\n            if (!noMipmap) {\r\n                this._generateMipmaps(texture, this._uploadEncoder);\r\n            }\r\n\r\n            texture.isReady = true;\r\n\r\n            texture.onLoadedObservable.notifyObservers(texture);\r\n            texture.onLoadedObservable.clear();\r\n\r\n            if (onLoad) {\r\n                onLoad();\r\n            }\r\n        },\r\n        !!useSRGBBuffer,\r\n        buffer\r\n    );\r\n};\r\n\r\nThinWebGPUEngine.prototype._setCubeMapTextureParams = function (texture: InternalTexture, loadMipmap: boolean, maxLevel?: number) {\r\n    texture.samplingMode = loadMipmap ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : Constants.TEXTURE_BILINEAR_SAMPLINGMODE;\r\n    texture._cachedWrapU = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n    texture._cachedWrapV = Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n    if (maxLevel) {\r\n        texture._maxLodLevel = maxLevel;\r\n    }\r\n};\r\n\r\nThinWebGPUEngine.prototype.generateMipMapsForCubemap = function (texture: InternalTexture) {\r\n    if (texture.generateMipMaps) {\r\n        const gpuTexture = texture._hardwareTexture?.underlyingResource;\r\n\r\n        if (!gpuTexture) {\r\n            this._textureHelper.createGPUTextureForInternalTexture(texture);\r\n        }\r\n\r\n        this._generateMipmaps(texture);\r\n    }\r\n};\r\n"]}