{"version": 3, "file": "webDeviceInputSystem.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/DeviceInput/webDeviceInputSystem.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAE7D,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,OAAO,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAGtE,gEAAgE;AAChE,MAAM,YAAY,GAAG,GAAG,CAAC;AACzB,gEAAgE;AAChE,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAEhE,gBAAgB;AAChB,MAAM,OAAO,oBAAoB;IA+D7B;;;;;;OAMG;IACH,YACI,MAAsB,EACtB,iBAAuE,EACvE,oBAA0E,EAC1E,cAAyF;QAzE7F,kBAAkB;QACV,YAAO,GAAmD,EAAE,CAAC;QAE7D,oBAAe,GAAY,KAAK,CAAC;QACjC,mBAAc,GAAY,KAAK,CAAC;QAIvB,iBAAY,GAAY,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC1D,8DAA8D;QAC9D,0HAA0H;QACzG,gBAAW,GAAY,oBAAoB,EAAE,IAAI,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAMrH,6DAA6D;QACrD,uBAAkB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAC9C,6DAA6D;QACrD,qBAAgB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAC5C,6DAA6D;QACrD,uBAAkB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAE9C,6DAA6D;QACrD,sBAAiB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAC7C,6DAA6D;QACrD,sBAAiB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAC7C,6DAA6D;QACrD,oBAAe,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAC3C,6DAA6D;QACrD,wBAAmB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAC/C,6DAA6D;QACrD,wBAAmB,GAAG,CAAC,SAAiB,EAAE,EAAE,GAAE,CAAC,CAAC;QACxD,6DAA6D;QACrD,uBAAkB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAC9C,6DAA6D;QACrD,uBAAkB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAC9C,6DAA6D;QACrD,sBAAiB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAC7C,6DAA6D;QACrD,gCAA2B,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAE/C,oBAAe,GAAY,KAAK,CAAC;QAEjC,aAAQ,GAAG,CAAC,CAAC,CAAC;QACL,oBAAe,GAAG,oBAAoB,EAAE,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QACjH,qBAAgB,GAAG,oBAAoB,EAAE,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAI1H,oBAAe,GAAW,CAAC,CAAC;QAE5B,+BAA0B,GAAuC,IAAI,CAAC;QAE9E,6DAA6D;QACrD,2BAAsB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAClD,6DAA6D;QACrD,8BAAyB,GAAG,CAAC,GAAQ,EAAE,EAAE,GAAE,CAAC,CAAC;QAiBjD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QAEtC,iDAAiD;QACjD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7C,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACxB,CAAC;QAED,2EAA2E;QAC3E,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,GAAG,EAAE;gBACrC,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,CAAC,CAAC;QACN,CAAC;IACL,CAAC;IAED,mBAAmB;IACnB;;;;;;OAMG;IACI,SAAS,CAAC,UAAsB,EAAE,UAAkB,EAAE,UAAkB;QAC3E,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;QAEpD,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,4CAA4C;YAC5C,MAAM,yBAAyB,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QAC5D,CAAC;QAED,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE,CAAC;YAC3E,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;QACxC,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YAC7B,4CAA4C;YAC5C,MAAM,wBAAwB,UAAU,eAAe,UAAU,CAAC,UAAU,CAAC,YAAY,UAAU,EAAE,CAAC;QAC1G,CAAC;QAED,IAAI,UAAU,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC;YACnC,KAAK,CAAC,IAAI,CAAC,iIAAiI,CAAC,CAAC;QAClJ,CAAC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,UAAsB;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,SAAS,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,OAAO;QACV,YAAY;QACZ,IAAI,CAAC,kBAAkB,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;QACnC,IAAI,CAAC,qBAAqB,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;QACtC,IAAI,CAAC,eAAe,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;QAEzC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1B,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa;QACjB,MAAM,YAAY,GAAG,IAAI,EAAE,OAAO,CAAC,eAAe,EAAE,CAAC;QACrD,IAAI,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,kBAAkB,KAAK,YAAY,CAAC,EAAE,CAAC;YACtF,iGAAiG;YACjG,IAAI,CAAC,cAAc,EAAE,CAAC;YAEtB,wFAAwF;YACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBAChC,IAAI,MAAM,EAAE,CAAC;wBACT,KAAK,MAAM,aAAa,IAAI,MAAM,EAAE,CAAC;4BACjC,MAAM,UAAU,GAAG,CAAC,aAAa,CAAC;4BAClC,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;4BAClC,IAAI,MAAM,EAAE,CAAC;gCACT,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC;oCAChE,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gCAC3B,CAAC;4BACL,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,CAAC,kBAAkB,GAAG,YAAY,CAAC;YACvC,kHAAkH;YAClH,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;YAC5I,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAE5B,oHAAoH;YACpH,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACrC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,cAAc;YACd,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC7E,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAE5E,kBAAkB;YAClB,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAChF,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAE5E,iBAAiB;YACjB,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAChG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC5F,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,GAAG,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACpG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAClG,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC3F,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC5C,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACxG,CAAC;YAED,iBAAiB;YACjB,MAAM,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC5E,MAAM,CAAC,mBAAmB,CAAC,qBAAqB,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IACjC,CAAC;IAED;;;OAGG;IACK,yBAAyB;QAC7B,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;YACxB,MAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAEzC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;gBAC7B,IAAI,OAAO,EAAE,CAAC;oBACV,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAC9B,CAAC;YACL,CAAC;QACL,CAAC;QAED,kEAAkE;QAClE,IAAI,OAAO,UAAU,KAAK,UAAU,IAAI,UAAU,CAAC,gBAAgB,CAAC,CAAC,OAAO,EAAE,CAAC;YAC3E,+HAA+H;YAC/H,+GAA+G;YAC/G,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED,oBAAoB;IACpB;;;OAGG;IACK,WAAW,CAAC,OAAY;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1D,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC;QAEjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,KAAK,CAAa,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE3F,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACK,iBAAiB,CAAC,UAAsB,EAAE,UAAkB,EAAE,QAAgB,EAAE,QAAgB;QACpG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,kBAAkB,CAAC,CAAC;QACjE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,oEAAoE;QAC1H,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;QACtB,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACK,eAAe,CAAC,UAAsB,EAAE,UAAkB,EAAE,cAAsB;QACtF,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC3B,4CAA4C;YAC5C,MAAM,6BAA6B,UAAU,CAAC,UAAU,CAAC,qBAAqB,CAAC;QACnF,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,KAAK,CAAS,cAAc,CAAC,CAAC;YAEjD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEf,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,iBAAiB,CAAC,UAAsB,EAAE,UAAkB;QAChE,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;YAC5C,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB;QACrB,IAAI,CAAC,kBAAkB,GAAG,CAAC,GAAG,EAAE,EAAE;YAC9B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAEvB,MAAM,WAAW,GAAG,GAAe,CAAC;gBACpC,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC;gBAErC,IAAI,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,GAAG,KAAK,MAAM,EAAE,CAAC;oBACxD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;wBACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBACrC,CAAC;gBACL,CAAC;gBAED,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;YAC9D,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,GAAG,CAAC,GAAG,EAAE,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAEvB,MAAM,WAAW,GAAG,GAAe,CAAC;gBACpC,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC;gBAErC,IAAI,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,GAAG,KAAK,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtE,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;wBACnC,MAAM,WAAW,GAAa,kBAAkB,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBACtI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;wBACnB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;oBAC9D,CAAC;oBACD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBACpD,CAAC;gBAED,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;YAC9D,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,kBAAkB,GAAG,GAAG,EAAE;YAC3B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACpC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;wBACjB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;wBAEb,MAAM,WAAW,GAAa,kBAAkB,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAEhI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;oBAC9D,CAAC;gBACL,CAAC;gBACD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBACpD,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7E,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzE,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACK,qBAAqB;QACzB,kHAAkH;QAClH,IAAI,CAAC,eAAe,GAAG,CAAC,oBAAoB,EAAE,IAAI,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACjF,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,GAAG,IAAI,KAAK,CAAS,IAAI,CAAC,eAAe,CAAC,CAAC;QACnE,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,CAAC,GAAG,EAAE,EAAE;YAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC7C,IAAI,UAAU,GAAG,UAAU,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAEnG,gGAAgG;YAChG,2DAA2D;YAC3D,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;gBACvD,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE7C,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;oBACX,UAAU,GAAG,GAAG,CAAC;oBACjB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC;oBAC1C,+DAA+D;oBAC/D,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACJ,0GAA0G;oBAC1G,KAAK,CAAC,IAAI,CAAC,kEAAkE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;oBACrG,OAAO;gBACX,CAAC;YACL,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YAClC,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC;gBACxC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;YAC7E,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,WAAW,GAAG,GAAoB,CAAC;gBACzC,WAAW,CAAC,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC;gBAE3C,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;gBAC/C,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;gBAE7C,oFAAoF;gBACpF,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,IAAI,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC3E,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACxC,CAAC;gBAED,IAAI,GAAG,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC9B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAClC,CAAC;gBAED,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBAE1D,wDAAwD;gBACxD,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC1C,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;oBACxC,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kDAAkD;oBAC7G,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,iBAAiB,GAAG,CAAC,GAAG,EAAE,EAAE;YAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC7C,IAAI,UAAU,GAAG,UAAU,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC;YAErE,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,EAAE,CAAC;gBAClC,0DAA0D;gBAC1D,mHAAmH;gBACnH,IAAI,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACtD,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACb,iEAAiE;oBACjE,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC;gBAED,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;oBACX,UAAU,GAAG,GAAG,CAAC;oBACjB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC;gBAC9C,CAAC;qBAAM,CAAC;oBACJ,0GAA0G;oBAC1G,KAAK,CAAC,IAAI,CAAC,kEAAkE,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;oBACrG,OAAO;gBACX,CAAC;YACL,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YAClC,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC;gBACxC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;YAC7E,CAAC;iBAAM,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,EAAE,CAAC;gBACzC,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,kBAAkB,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAC5D,MAAM,gBAAgB,GAAG,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAExD,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,EAAE,CAAC;oBAClC,oCAAoC;oBACpC,IAAI,GAAG,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;wBAC9B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAClC,CAAC;oBAED,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;wBAC/B,IAAI,CAAC;4BACD,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAC7D,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACT,aAAa;wBACjB,CAAC;oBACL,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,mFAAmF;oBACnF,IAAI,GAAG,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;wBAChD,IAAI,CAAC;4BACD,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBAC7D,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACT,aAAa;wBACjB,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;gBAC/C,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;gBAC7C,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBAE5B,MAAM,WAAW,GAAG,GAAe,CAAC;gBAEpC,gIAAgI;gBAChI,qHAAqH;gBACrH,iDAAiD;gBACjD,WAAW,CAAC,UAAU,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;gBAExC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBAE1D,IAAI,kBAAkB,KAAK,GAAG,CAAC,OAAO,IAAI,gBAAgB,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;oBACzE,WAAW,CAAC,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC;oBAC3C,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,EAAE,EAAE;YAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,UAAU,GAAG,UAAU,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAErG,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,EAAE,CAAC;gBAClC,iFAAiF;gBACjF,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;oBACpB,OAAO;gBACX,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC1C,CAAC;YACL,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;YACvD,IAAI,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;YACxB,IAAI,sBAAsB,GAAG,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAElE,2GAA2G;YAC3G,+GAA+G;YAC/G,+GAA+G;YAC/G,iHAAiH;YACjH,kHAAkH;YAClH,6BAA6B;YAC7B,mHAAmH;YACnH,sHAAsH;YACtH,8BAA8B;YAC9B,qFAAqF;YACrF,iGAAiG;YACjG,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,EAAE,CAAC;gBACjF,8CAA8C;gBAC9C,MAAM,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE9B,sBAAsB,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,sBAAsB,EAAE,CAAC;gBACzB,MAAM,kBAAkB,GAAG,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAC5D,MAAM,gBAAgB,GAAG,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAExD,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;gBAC/C,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC;gBAC7C,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBAExB,MAAM,WAAW,GAAG,GAAe,CAAC;gBAEpC,IAAI,GAAG,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC9B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAClC,CAAC;gBAED,IAAI,kBAAkB,KAAK,GAAG,CAAC,OAAO,IAAI,gBAAgB,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;oBACzE,WAAW,CAAC,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC;oBAC3C,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBAC9D,CAAC;gBAED,gIAAgI;gBAChI,qHAAqH;gBACrH,iDAAiD;gBACjD,WAAW,CAAC,UAAU,GAAG,MAAM,GAAG,CAAC,CAAC;gBAEpC,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtH,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjE,CAAC;qBAAM,IAAI,GAAG,CAAC,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oBACrF,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACjE,CAAC;gBAED,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBAE1D,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,EAAE,CAAC;oBAClC,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;gBACvD,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,mBAAmB,GAAG,CAAC,SAAiB,EAAE,EAAE;YAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAE3D,oFAAoF;YACpF,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;gBACpB,OAAO;YACX,CAAC;YAED,IAAI,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;gBACzD,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAEvE,MAAM,WAAW,GAAa,kBAAkB,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,EAAE,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;YAEtK,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;YAEhE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;YACtC,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAC7D,CAAC,CAAC;QAEF,IAAI,CAAC,mBAAmB,GAAG,CAAC,GAAG,EAAE,EAAE;YAC/B,IAAI,GAAG,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;gBAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAElD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACnF,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjE,CAAC;gBAED,KAAK,IAAI,UAAU,GAAG,YAAY,CAAC,SAAS,EAAE,UAAU,IAAI,YAAY,CAAC,cAAc,EAAE,UAAU,EAAE,EAAE,CAAC;oBACpG,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC5B,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBAExB,MAAM,WAAW,GAAa,kBAAkB,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAEtI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;oBAC3D,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,kBAAkB,GAAG,CAAC,GAAG,EAAE,EAAE;YAC9B,IAAI,GAAG,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;gBAC5B,sHAAsH;gBACtH,2DAA2D;gBAC3D,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC,CAAC;QAEF,gEAAgE;QAChE,IAAI,CAAC,eAAe;YAChB,SAAS,IAAI,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;gBACtC,CAAC,CAAC,OAAO,CAAC,kCAAkC;gBAC5C,CAAC,CAAO,QAAS,CAAC,YAAY,KAAK,SAAS;oBAC1C,CAAC,CAAC,YAAY,CAAC,8CAA8C;oBAC7D,CAAC,CAAC,gBAAgB,CAAC,CAAC,yDAAyD;QAEvF,2CAA2C;QAC3C,8FAA8F;QAC9F,yFAAyF;QACzF,0GAA0G;QAC1G,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAC7B,MAAM,IAAI,GAAG,cAAa,CAAC,CAAC;QAE5B,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE;gBACjD,GAAG,EAAE;oBACD,gBAAgB,GAAG,IAAI,CAAC;gBAC5B,CAAC;aACJ,CAAC,CAAC;YAEH,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAChE,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,KAAK;QACT,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,GAAG,EAAE;YAC1B,uBAAuB;YACvB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAElD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACnF,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjE,CAAC;gBAED,KAAK,IAAI,UAAU,GAAG,YAAY,CAAC,SAAS,EAAE,UAAU,IAAI,YAAY,CAAC,cAAc,EAAE,UAAU,EAAE,EAAE,CAAC;oBACpG,IAAI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC5B,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;wBAExB,MAAM,WAAW,GAAa,kBAAkB,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;wBAEtI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;oBAC3D,CAAC;gBACL,CAAC;YACL,CAAC;YAED,wBAAwB;YACxB,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBAE/C,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC;oBAC9E,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;oBAEnD,IAAI,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;wBACzD,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;oBAC7D,CAAC;oBAED,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC1E,OAAO,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBAEhD,MAAM,WAAW,GAAa,kBAAkB,CAAC,iBAAiB,CAC9D,UAAU,CAAC,KAAK,EAChB,UAAU,EACV,YAAY,CAAC,SAAS,EACtB,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,kBAAkB,EACvB,SAAS,CACZ,CAAC;wBAEF,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;wBAEhE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;wBACtC,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;oBAC7D,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,kBAAkB,GAAG,CAAC,GAAG,EAAE,EAAE;YAC9B,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC;YACpC,MAAM,UAAU,GAAG,CAAC,CAAC;YAErB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YAClC,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC;gBACxC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,kBAAkB,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;gBACpD,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC;gBACtE,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;gBAEpD,MAAM,WAAW,GAAG,GAAe,CAAC;gBACpC,iFAAiF;gBACjF,+EAA+E;gBAC/E,sCAAsC;gBACtC,IAAI,GAAG,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;oBAC9B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAClC,CAAC;gBAED,IAAI,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC1C,WAAW,CAAC,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC;oBAClD,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBAC9D,CAAC;gBACD,IAAI,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC1C,WAAW,CAAC,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC;oBAClD,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBAC9D,CAAC;gBACD,IAAI,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC1C,WAAW,CAAC,UAAU,GAAG,YAAY,CAAC,WAAW,CAAC;oBAClD,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,sEAAsE;QACtE,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC5C,IAAI,CAAC,2BAA2B,GAAG,CAAC,GAAG,EAAE,EAAE;gBACvC,IAAI,GAAG,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;oBAClB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;gBAClC,CAAC;YACL,CAAC,CAAC;YACF,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACrG,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7F,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,GAAG,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7F,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACzF,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,GAAG,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACjG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC/F,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACzE,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAEvI,qGAAqG;QACrG,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE;YACzE,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClD,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBACtC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBACtC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,qBAAqB;QACzB,IAAI,CAAC,sBAAsB,GAAG,CAAC,GAAQ,EAAE,EAAE;YACvC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC;QAEF,IAAI,CAAC,yBAAyB,GAAG,CAAC,GAAQ,EAAE,EAAE;YAC1C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC9D,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;gBAErC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;gBAC/C,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YACtC,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACzE,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACnF,CAAC;IAED;;;;;OAKG;IACK,aAAa,CAAC,UAAsB,EAAE,UAAkB,EAAE,UAAkB;QAChF,WAAW;QACX,MAAM,EAAE,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,CAAC;QAE/C,IAAI,EAAE,IAAI,UAAU,KAAK,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;YAClD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,CAAC;YAEpD,IAAI,UAAU,IAAI,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBAClC,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;YACtD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,qBAAqB,CAAC,UAAkB;QAC5C,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACpC,sBAAsB;YACtB,OAAO,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC;QAC3F,CAAC;aAAM,IAAI,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7H,eAAe;YACf,OAAO,UAAU,CAAC,IAAI,CAAC;QAC3B,CAAC;aAAM,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3C,iBAAiB;YACjB,OAAO,UAAU,CAAC,MAAM,CAAC;QAC7B,CAAC;QAED,OAAO,UAAU,CAAC,OAAO,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACK,eAAe,CAAC,GAAQ;QAC5B,IAAI,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC;QAElC,IAAI,GAAG,CAAC,WAAW,KAAK,OAAO,IAAI,GAAG,CAAC,WAAW,KAAK,KAAK,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAC1E,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC;QAClC,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ", "sourcesContent": ["import type { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport type { IPointerEvent, IUIEvent } from \"../Events/deviceInputEvents\";\r\nimport { IsNavigatorAvailable } from \"../Misc/domManagement\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport type { Nullable } from \"../types\";\r\nimport { DeviceEventFactory } from \"./eventFactory\";\r\nimport { DeviceType, PointerInput } from \"./InputDevices/deviceEnums\";\r\nimport type { IDeviceInputSystem } from \"./inputInterfaces\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nconst MAX_KEYCODES = 255;\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nconst MAX_POINTER_INPUTS = Object.keys(PointerInput).length / 2;\r\n\r\n/** @internal */\r\nexport class WebDeviceInputSystem implements IDeviceInputSystem {\r\n    // Private Members\r\n    private _inputs: Array<{ [deviceSlot: number]: Array<number> }> = [];\r\n    private _gamepads: Array<DeviceType>;\r\n    private _keyboardActive: boolean = false;\r\n    private _pointerActive: boolean = false;\r\n    private _elementToAttachTo: HTMLElement;\r\n    private _metaKeys: Array<number>;\r\n    private readonly _engine: AbstractEngine;\r\n    private readonly _usingSafari: boolean = Tools.IsSafari();\r\n    // Found solution for determining if MacOS is being used here:\r\n    // https://stackoverflow.com/questions/10527983/best-way-to-detect-mac-os-x-or-windows-computers-with-javascript-or-jquery\r\n    private readonly _usingMacOs: boolean = IsNavigatorAvailable() && /(Mac|iPhone|iPod|iPad)/i.test(navigator.platform);\r\n\r\n    private _onDeviceConnected: (deviceType: DeviceType, deviceSlot: number) => void;\r\n    private _onDeviceDisconnected: (deviceType: DeviceType, deviceSlot: number) => void;\r\n    private _onInputChanged: (deviceType: DeviceType, deviceSlot: number, eventData: IUIEvent) => void;\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _keyboardDownEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _keyboardUpEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _keyboardBlurEvent = (evt: any) => {};\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _pointerMoveEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _pointerDownEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _pointerUpEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _pointerCancelEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _pointerCancelTouch = (pointerId: number) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _pointerLeaveEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _pointerWheelEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _pointerBlurEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _pointerMacOsChromeOutEvent = (evt: any) => {};\r\n    private _wheelEventName: string;\r\n    private _eventsAttached: boolean = false;\r\n\r\n    private _mouseId = -1;\r\n    private readonly _isUsingFirefox = IsNavigatorAvailable() && navigator.userAgent && navigator.userAgent.indexOf(\"Firefox\") !== -1;\r\n    private readonly _isUsingChromium = IsNavigatorAvailable() && navigator.userAgent && navigator.userAgent.indexOf(\"Chrome\") !== -1;\r\n\r\n    // Array to store active Pointer ID values; prevents issues with negative pointerIds\r\n    private _activeTouchIds: Array<number>;\r\n    private _maxTouchPoints: number = 0;\r\n\r\n    private _pointerInputClearObserver: Nullable<Observer<AbstractEngine>> = null;\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _gamepadConnectedEvent = (evt: any) => {};\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    private _gamepadDisconnectedEvent = (evt: any) => {};\r\n\r\n    private _eventPrefix: string;\r\n\r\n    /**\r\n     * Constructor for the WebDeviceInputSystem\r\n     * @param engine Engine to reference\r\n     * @param onDeviceConnected Callback to execute when device is connected\r\n     * @param onDeviceDisconnected Callback to execute when device is disconnected\r\n     * @param onInputChanged Callback to execute when input changes on device\r\n     */\r\n    constructor(\r\n        engine: AbstractEngine,\r\n        onDeviceConnected: (deviceType: DeviceType, deviceSlot: number) => void,\r\n        onDeviceDisconnected: (deviceType: DeviceType, deviceSlot: number) => void,\r\n        onInputChanged: (deviceType: DeviceType, deviceSlot: number, eventData: IUIEvent) => void\r\n    ) {\r\n        this._eventPrefix = Tools.GetPointerPrefix(engine);\r\n        this._engine = engine;\r\n\r\n        this._onDeviceConnected = onDeviceConnected;\r\n        this._onDeviceDisconnected = onDeviceDisconnected;\r\n        this._onInputChanged = onInputChanged;\r\n\r\n        // If we need a pointerId, set one for future use\r\n        this._mouseId = this._isUsingFirefox ? 0 : 1;\r\n\r\n        this._enableEvents();\r\n\r\n        if (this._usingMacOs) {\r\n            this._metaKeys = [];\r\n        }\r\n\r\n        // Set callback to enable event handler switching when inputElement changes\r\n        if (!this._engine._onEngineViewChanged) {\r\n            this._engine._onEngineViewChanged = () => {\r\n                this._enableEvents();\r\n            };\r\n        }\r\n    }\r\n\r\n    // Public functions\r\n    /**\r\n     * Checks for current device input value, given an id and input index. Throws exception if requested device not initialized.\r\n     * @param deviceType Enum specifying device type\r\n     * @param deviceSlot \"Slot\" or index that device is referenced in\r\n     * @param inputIndex Id of input to be checked\r\n     * @returns Current value of input\r\n     */\r\n    public pollInput(deviceType: DeviceType, deviceSlot: number, inputIndex: number): number {\r\n        const device = this._inputs[deviceType][deviceSlot];\r\n\r\n        if (!device) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw `Unable to find device ${DeviceType[deviceType]}`;\r\n        }\r\n\r\n        if (deviceType >= DeviceType.DualShock && deviceType <= DeviceType.DualSense) {\r\n            this._updateDevice(deviceType, deviceSlot, inputIndex);\r\n        }\r\n\r\n        const currentValue = device[inputIndex];\r\n        if (currentValue === undefined) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw `Unable to find input ${inputIndex} for device ${DeviceType[deviceType]} in slot ${deviceSlot}`;\r\n        }\r\n\r\n        if (inputIndex === PointerInput.Move) {\r\n            Tools.Warn(`Unable to provide information for PointerInput.Move.  Try using PointerInput.Horizontal or PointerInput.Vertical for move data.`);\r\n        }\r\n\r\n        return currentValue;\r\n    }\r\n\r\n    /**\r\n     * Check for a specific device in the DeviceInputSystem\r\n     * @param deviceType Type of device to check for\r\n     * @returns bool with status of device's existence\r\n     */\r\n    public isDeviceAvailable(deviceType: DeviceType): boolean {\r\n        return this._inputs[deviceType] !== undefined;\r\n    }\r\n\r\n    /**\r\n     * Dispose of all the eventlisteners\r\n     */\r\n    public dispose(): void {\r\n        // Callbacks\r\n        this._onDeviceConnected = () => {};\r\n        this._onDeviceDisconnected = () => {};\r\n        this._onInputChanged = () => {};\r\n        delete this._engine._onEngineViewChanged;\r\n\r\n        if (this._elementToAttachTo) {\r\n            this._disableEvents();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enable listening for user input events\r\n     */\r\n    private _enableEvents(): void {\r\n        const inputElement = this?._engine.getInputElement();\r\n        if (inputElement && (!this._eventsAttached || this._elementToAttachTo !== inputElement)) {\r\n            // Remove events before adding to avoid double events or simultaneous events on multiple canvases\r\n            this._disableEvents();\r\n\r\n            // If the inputs array has already been created, zero it out to before setting up events\r\n            if (this._inputs) {\r\n                for (const inputs of this._inputs) {\r\n                    if (inputs) {\r\n                        for (const deviceSlotKey in inputs) {\r\n                            const deviceSlot = +deviceSlotKey;\r\n                            const device = inputs[deviceSlot];\r\n                            if (device) {\r\n                                for (let inputIndex = 0; inputIndex < device.length; inputIndex++) {\r\n                                    device[inputIndex] = 0;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            this._elementToAttachTo = inputElement;\r\n            // Set tab index for the inputElement to the engine's canvasTabIndex, if and only if the element's tab index is -1\r\n            this._elementToAttachTo.tabIndex = this._elementToAttachTo.tabIndex !== -1 ? this._elementToAttachTo.tabIndex : this._engine.canvasTabIndex;\r\n            this._handleKeyActions();\r\n            this._handlePointerActions();\r\n            this._handleGamepadActions();\r\n            this._eventsAttached = true;\r\n\r\n            // Check for devices that are already connected but aren't registered. Currently, only checks for gamepads and mouse\r\n            this._checkForConnectedDevices();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disable listening for user input events\r\n     */\r\n    private _disableEvents(): void {\r\n        if (this._elementToAttachTo) {\r\n            // Blur Events\r\n            this._elementToAttachTo.removeEventListener(\"blur\", this._keyboardBlurEvent);\r\n            this._elementToAttachTo.removeEventListener(\"blur\", this._pointerBlurEvent);\r\n\r\n            // Keyboard Events\r\n            this._elementToAttachTo.removeEventListener(\"keydown\", this._keyboardDownEvent);\r\n            this._elementToAttachTo.removeEventListener(\"keyup\", this._keyboardUpEvent);\r\n\r\n            // Pointer Events\r\n            this._elementToAttachTo.removeEventListener(this._eventPrefix + \"move\", this._pointerMoveEvent);\r\n            this._elementToAttachTo.removeEventListener(this._eventPrefix + \"down\", this._pointerDownEvent);\r\n            this._elementToAttachTo.removeEventListener(this._eventPrefix + \"up\", this._pointerUpEvent);\r\n            this._elementToAttachTo.removeEventListener(this._eventPrefix + \"cancel\", this._pointerCancelEvent);\r\n            this._elementToAttachTo.removeEventListener(this._eventPrefix + \"leave\", this._pointerLeaveEvent);\r\n            this._elementToAttachTo.removeEventListener(this._wheelEventName, this._pointerWheelEvent);\r\n            if (this._usingMacOs && this._isUsingChromium) {\r\n                this._elementToAttachTo.removeEventListener(\"lostpointercapture\", this._pointerMacOsChromeOutEvent);\r\n            }\r\n\r\n            // Gamepad Events\r\n            window.removeEventListener(\"gamepadconnected\", this._gamepadConnectedEvent);\r\n            window.removeEventListener(\"gamepaddisconnected\", this._gamepadDisconnectedEvent);\r\n        }\r\n\r\n        if (this._pointerInputClearObserver) {\r\n            this._engine.onEndFrameObservable.remove(this._pointerInputClearObserver);\r\n        }\r\n\r\n        this._eventsAttached = false;\r\n    }\r\n\r\n    /**\r\n     * Checks for existing connections to devices and register them, if necessary\r\n     * Currently handles gamepads and mouse\r\n     */\r\n    private _checkForConnectedDevices(): void {\r\n        if (navigator.getGamepads) {\r\n            const gamepads = navigator.getGamepads();\r\n\r\n            for (const gamepad of gamepads) {\r\n                if (gamepad) {\r\n                    this._addGamePad(gamepad);\r\n                }\r\n            }\r\n        }\r\n\r\n        // If the device in use has mouse capabilities, pre-register mouse\r\n        if (typeof matchMedia === \"function\" && matchMedia(\"(pointer:fine)\").matches) {\r\n            // This will provide a dummy value for the cursor position and is expected to be overridden when the first mouse event happens.\r\n            // There isn't any good way to get the current position outside of a pointer event so that's why this was done.\r\n            this._addPointerDevice(DeviceType.Mouse, 0, 0, 0);\r\n        }\r\n    }\r\n\r\n    // Private functions\r\n    /**\r\n     * Add a gamepad to the DeviceInputSystem\r\n     * @param gamepad A single DOM Gamepad object\r\n     */\r\n    private _addGamePad(gamepad: any): void {\r\n        const deviceType = this._getGamepadDeviceType(gamepad.id);\r\n        const deviceSlot = gamepad.index;\r\n\r\n        this._gamepads = this._gamepads || new Array<DeviceType>(gamepad.index + 1);\r\n        this._registerDevice(deviceType, deviceSlot, gamepad.buttons.length + gamepad.axes.length);\r\n\r\n        this._gamepads[deviceSlot] = deviceType;\r\n    }\r\n\r\n    /**\r\n     * Add pointer device to DeviceInputSystem\r\n     * @param deviceType Type of Pointer to add\r\n     * @param deviceSlot Pointer ID (0 for mouse, pointerId for Touch)\r\n     * @param currentX Current X at point of adding\r\n     * @param currentY Current Y at point of adding\r\n     */\r\n    private _addPointerDevice(deviceType: DeviceType, deviceSlot: number, currentX: number, currentY: number): void {\r\n        if (!this._pointerActive) {\r\n            this._pointerActive = true;\r\n        }\r\n        this._registerDevice(deviceType, deviceSlot, MAX_POINTER_INPUTS);\r\n        const pointer = this._inputs[deviceType][deviceSlot]; /* initialize our pointer position immediately after registration */\r\n        pointer[0] = currentX;\r\n        pointer[1] = currentY;\r\n    }\r\n\r\n    /**\r\n     * Add device and inputs to device array\r\n     * @param deviceType Enum specifying device type\r\n     * @param deviceSlot \"Slot\" or index that device is referenced in\r\n     * @param numberOfInputs Number of input entries to create for given device\r\n     */\r\n    private _registerDevice(deviceType: DeviceType, deviceSlot: number, numberOfInputs: number): void {\r\n        if (deviceSlot === undefined) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw `Unable to register device ${DeviceType[deviceType]} to undefined slot.`;\r\n        }\r\n\r\n        if (!this._inputs[deviceType]) {\r\n            this._inputs[deviceType] = {};\r\n        }\r\n\r\n        if (!this._inputs[deviceType][deviceSlot]) {\r\n            const device = new Array<number>(numberOfInputs);\r\n\r\n            device.fill(0);\r\n\r\n            this._inputs[deviceType][deviceSlot] = device;\r\n            this._onDeviceConnected(deviceType, deviceSlot);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Given a specific device name, remove that device from the device map\r\n     * @param deviceType Enum specifying device type\r\n     * @param deviceSlot \"Slot\" or index that device is referenced in\r\n     */\r\n    private _unregisterDevice(deviceType: DeviceType, deviceSlot: number): void {\r\n        if (this._inputs[deviceType][deviceSlot]) {\r\n            delete this._inputs[deviceType][deviceSlot];\r\n            this._onDeviceDisconnected(deviceType, deviceSlot);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Handle all actions that come from keyboard interaction\r\n     */\r\n    private _handleKeyActions(): void {\r\n        this._keyboardDownEvent = (evt) => {\r\n            if (!this._keyboardActive) {\r\n                this._keyboardActive = true;\r\n                this._registerDevice(DeviceType.Keyboard, 0, MAX_KEYCODES);\r\n            }\r\n\r\n            const kbKey = this._inputs[DeviceType.Keyboard][0];\r\n            if (kbKey) {\r\n                kbKey[evt.keyCode] = 1;\r\n\r\n                const deviceEvent = evt as IUIEvent;\r\n                deviceEvent.inputIndex = evt.keyCode;\r\n\r\n                if (this._usingMacOs && evt.metaKey && evt.key !== \"Meta\") {\r\n                    if (!this._metaKeys.includes(evt.keyCode)) {\r\n                        this._metaKeys.push(evt.keyCode);\r\n                    }\r\n                }\r\n\r\n                this._onInputChanged(DeviceType.Keyboard, 0, deviceEvent);\r\n            }\r\n        };\r\n\r\n        this._keyboardUpEvent = (evt) => {\r\n            if (!this._keyboardActive) {\r\n                this._keyboardActive = true;\r\n                this._registerDevice(DeviceType.Keyboard, 0, MAX_KEYCODES);\r\n            }\r\n\r\n            const kbKey = this._inputs[DeviceType.Keyboard][0];\r\n            if (kbKey) {\r\n                kbKey[evt.keyCode] = 0;\r\n\r\n                const deviceEvent = evt as IUIEvent;\r\n                deviceEvent.inputIndex = evt.keyCode;\r\n\r\n                if (this._usingMacOs && evt.key === \"Meta\" && this._metaKeys.length > 0) {\r\n                    for (const keyCode of this._metaKeys) {\r\n                        const deviceEvent: IUIEvent = DeviceEventFactory.CreateDeviceEvent(DeviceType.Keyboard, 0, keyCode, 0, this, this._elementToAttachTo);\r\n                        kbKey[keyCode] = 0;\r\n                        this._onInputChanged(DeviceType.Keyboard, 0, deviceEvent);\r\n                    }\r\n                    this._metaKeys.splice(0, this._metaKeys.length);\r\n                }\r\n\r\n                this._onInputChanged(DeviceType.Keyboard, 0, deviceEvent);\r\n            }\r\n        };\r\n\r\n        this._keyboardBlurEvent = () => {\r\n            if (this._keyboardActive) {\r\n                const kbKey = this._inputs[DeviceType.Keyboard][0];\r\n\r\n                for (let i = 0; i < kbKey.length; i++) {\r\n                    if (kbKey[i] !== 0) {\r\n                        kbKey[i] = 0;\r\n\r\n                        const deviceEvent: IUIEvent = DeviceEventFactory.CreateDeviceEvent(DeviceType.Keyboard, 0, i, 0, this, this._elementToAttachTo);\r\n\r\n                        this._onInputChanged(DeviceType.Keyboard, 0, deviceEvent);\r\n                    }\r\n                }\r\n                if (this._usingMacOs) {\r\n                    this._metaKeys.splice(0, this._metaKeys.length);\r\n                }\r\n            }\r\n        };\r\n\r\n        this._elementToAttachTo.addEventListener(\"keydown\", this._keyboardDownEvent);\r\n        this._elementToAttachTo.addEventListener(\"keyup\", this._keyboardUpEvent);\r\n        this._elementToAttachTo.addEventListener(\"blur\", this._keyboardBlurEvent);\r\n    }\r\n\r\n    /**\r\n     * Handle all actions that come from pointer interaction\r\n     */\r\n    private _handlePointerActions(): void {\r\n        // If maxTouchPoints is defined, use that value.  Otherwise, allow for a minimum for supported gestures like pinch\r\n        this._maxTouchPoints = (IsNavigatorAvailable() && navigator.maxTouchPoints) || 2;\r\n        if (!this._activeTouchIds) {\r\n            this._activeTouchIds = new Array<number>(this._maxTouchPoints);\r\n        }\r\n\r\n        for (let i = 0; i < this._maxTouchPoints; i++) {\r\n            this._activeTouchIds[i] = -1;\r\n        }\r\n\r\n        this._pointerMoveEvent = (evt) => {\r\n            const deviceType = this._getPointerType(evt);\r\n            let deviceSlot = deviceType === DeviceType.Mouse ? 0 : this._activeTouchIds.indexOf(evt.pointerId);\r\n\r\n            // In the event that we're getting pointermove events from touch inputs that we aren't tracking,\r\n            // look for an available slot and retroactively connect it.\r\n            if (deviceType === DeviceType.Touch && deviceSlot === -1) {\r\n                const idx = this._activeTouchIds.indexOf(-1);\r\n\r\n                if (idx >= 0) {\r\n                    deviceSlot = idx;\r\n                    this._activeTouchIds[idx] = evt.pointerId;\r\n                    // Because this is a \"new\" input, inform the connected callback\r\n                    this._onDeviceConnected(deviceType, deviceSlot);\r\n                } else {\r\n                    // We can't find an open slot to store new pointer so just return (can only support max number of touches)\r\n                    Tools.Warn(`Max number of touches exceeded.  Ignoring touches in excess of ${this._maxTouchPoints}`);\r\n                    return;\r\n                }\r\n            }\r\n\r\n            if (!this._inputs[deviceType]) {\r\n                this._inputs[deviceType] = {};\r\n            }\r\n\r\n            if (!this._inputs[deviceType][deviceSlot]) {\r\n                this._addPointerDevice(deviceType, deviceSlot, evt.clientX, evt.clientY);\r\n            }\r\n\r\n            const pointer = this._inputs[deviceType][deviceSlot];\r\n            if (pointer) {\r\n                const deviceEvent = evt as IPointerEvent;\r\n                deviceEvent.inputIndex = PointerInput.Move;\r\n\r\n                pointer[PointerInput.Horizontal] = evt.clientX;\r\n                pointer[PointerInput.Vertical] = evt.clientY;\r\n\r\n                // For touches that aren't started with a down, we need to set the button state to 1\r\n                if (deviceType === DeviceType.Touch && pointer[PointerInput.LeftClick] === 0) {\r\n                    pointer[PointerInput.LeftClick] = 1;\r\n                }\r\n\r\n                if (evt.pointerId === undefined) {\r\n                    evt.pointerId = this._mouseId;\r\n                }\r\n\r\n                this._onInputChanged(deviceType, deviceSlot, deviceEvent);\r\n\r\n                // Lets Propagate the event for move with same position.\r\n                if (!this._usingSafari && evt.button !== -1) {\r\n                    deviceEvent.inputIndex = evt.button + 2;\r\n                    pointer[evt.button + 2] = pointer[evt.button + 2] ? 0 : 1; // Reverse state of button if evt.button has value\r\n                    this._onInputChanged(deviceType, deviceSlot, deviceEvent);\r\n                }\r\n            }\r\n        };\r\n\r\n        this._pointerDownEvent = (evt) => {\r\n            const deviceType = this._getPointerType(evt);\r\n            let deviceSlot = deviceType === DeviceType.Mouse ? 0 : evt.pointerId;\r\n\r\n            if (deviceType === DeviceType.Touch) {\r\n                // See if this pointerId is already using an existing slot\r\n                // (possible on some devices which raise the pointerMove event before the pointerDown event, e.g. when using a pen)\r\n                let idx = this._activeTouchIds.indexOf(evt.pointerId);\r\n                if (idx === -1) {\r\n                    // If the pointerId wasn't already using a slot, find an open one\r\n                    idx = this._activeTouchIds.indexOf(-1);\r\n                }\r\n\r\n                if (idx >= 0) {\r\n                    deviceSlot = idx;\r\n                    this._activeTouchIds[idx] = evt.pointerId;\r\n                } else {\r\n                    // We can't find an open slot to store new pointer so just return (can only support max number of touches)\r\n                    Tools.Warn(`Max number of touches exceeded.  Ignoring touches in excess of ${this._maxTouchPoints}`);\r\n                    return;\r\n                }\r\n            }\r\n\r\n            if (!this._inputs[deviceType]) {\r\n                this._inputs[deviceType] = {};\r\n            }\r\n\r\n            if (!this._inputs[deviceType][deviceSlot]) {\r\n                this._addPointerDevice(deviceType, deviceSlot, evt.clientX, evt.clientY);\r\n            } else if (deviceType === DeviceType.Touch) {\r\n                this._onDeviceConnected(deviceType, deviceSlot);\r\n            }\r\n\r\n            const pointer = this._inputs[deviceType][deviceSlot];\r\n            if (pointer) {\r\n                const previousHorizontal = pointer[PointerInput.Horizontal];\r\n                const previousVertical = pointer[PointerInput.Vertical];\r\n\r\n                if (deviceType === DeviceType.Mouse) {\r\n                    // Mouse; Set pointerId if undefined\r\n                    if (evt.pointerId === undefined) {\r\n                        evt.pointerId = this._mouseId;\r\n                    }\r\n\r\n                    if (!document.pointerLockElement) {\r\n                        try {\r\n                            this._elementToAttachTo.setPointerCapture(this._mouseId);\r\n                        } catch (e) {\r\n                            // DO NOTHING\r\n                        }\r\n                    }\r\n                } else {\r\n                    // Touch; Since touches are dynamically assigned, only set capture if we have an id\r\n                    if (evt.pointerId && !document.pointerLockElement) {\r\n                        try {\r\n                            this._elementToAttachTo.setPointerCapture(evt.pointerId);\r\n                        } catch (e) {\r\n                            // DO NOTHING\r\n                        }\r\n                    }\r\n                }\r\n\r\n                pointer[PointerInput.Horizontal] = evt.clientX;\r\n                pointer[PointerInput.Vertical] = evt.clientY;\r\n                pointer[evt.button + 2] = 1;\r\n\r\n                const deviceEvent = evt as IUIEvent;\r\n\r\n                // NOTE: The +2 used here to is because PointerInput has the same value progression for its mouse buttons as PointerEvent.button\r\n                // However, we have our X and Y values front-loaded to group together the touch inputs but not break this progression\r\n                // EG. ([X, Y, Left-click], Middle-click, etc...)\r\n                deviceEvent.inputIndex = evt.button + 2;\r\n\r\n                this._onInputChanged(deviceType, deviceSlot, deviceEvent);\r\n\r\n                if (previousHorizontal !== evt.clientX || previousVertical !== evt.clientY) {\r\n                    deviceEvent.inputIndex = PointerInput.Move;\r\n                    this._onInputChanged(deviceType, deviceSlot, deviceEvent);\r\n                }\r\n            }\r\n        };\r\n\r\n        this._pointerUpEvent = (evt) => {\r\n            const deviceType = this._getPointerType(evt);\r\n            const deviceSlot = deviceType === DeviceType.Mouse ? 0 : this._activeTouchIds.indexOf(evt.pointerId);\r\n\r\n            if (deviceType === DeviceType.Touch) {\r\n                // If we're getting a pointerup event for a touch that isn't active, just return.\r\n                if (deviceSlot === -1) {\r\n                    return;\r\n                } else {\r\n                    this._activeTouchIds[deviceSlot] = -1;\r\n                }\r\n            }\r\n\r\n            const pointer = this._inputs[deviceType]?.[deviceSlot];\r\n            let button = evt.button;\r\n            let shouldProcessPointerUp = pointer && pointer[button + 2] !== 0;\r\n\r\n            // Workaround for an issue in Firefox on MacOS only where the browser allows the user to change left button\r\n            // actions into right button actions by holding down control. If the user starts a drag with the control button\r\n            // down, then lifts control, then releases the mouse, we'll get mismatched up and down events (the down will be\r\n            // the right button, and the up will be the left button). In that specific case, where we get an up from a button\r\n            // which didn't have a corresponding down, and we are in Firefox on MacOS, we should process the up event as if it\r\n            // was from the other button.\r\n            // Ideally this would be fixed in Firefox so that if you start a drag with the control button down, then the button\r\n            // passed along to both pointer down and up would be the right button regardless of the order in which control and the\r\n            // mouse button were released.\r\n            // If Firefox makes a fix to ensure this is the case, this workaround can be removed.\r\n            // Relevant forum thread: https://forum.babylonjs.com/t/camera-pan-getting-stuck-in-firefox/57158\r\n            if (!shouldProcessPointerUp && this._isUsingFirefox && this._usingMacOs && pointer) {\r\n                // Try the other button (left or right button)\r\n                button = button === 2 ? 0 : 2;\r\n\r\n                shouldProcessPointerUp = pointer[button + 2] !== 0;\r\n            }\r\n\r\n            if (shouldProcessPointerUp) {\r\n                const previousHorizontal = pointer[PointerInput.Horizontal];\r\n                const previousVertical = pointer[PointerInput.Vertical];\r\n\r\n                pointer[PointerInput.Horizontal] = evt.clientX;\r\n                pointer[PointerInput.Vertical] = evt.clientY;\r\n                pointer[button + 2] = 0;\r\n\r\n                const deviceEvent = evt as IUIEvent;\r\n\r\n                if (evt.pointerId === undefined) {\r\n                    evt.pointerId = this._mouseId;\r\n                }\r\n\r\n                if (previousHorizontal !== evt.clientX || previousVertical !== evt.clientY) {\r\n                    deviceEvent.inputIndex = PointerInput.Move;\r\n                    this._onInputChanged(deviceType, deviceSlot, deviceEvent);\r\n                }\r\n\r\n                // NOTE: The +2 used here to is because PointerInput has the same value progression for its mouse buttons as PointerEvent.button\r\n                // However, we have our X and Y values front-loaded to group together the touch inputs but not break this progression\r\n                // EG. ([X, Y, Left-click], Middle-click, etc...)\r\n                deviceEvent.inputIndex = button + 2;\r\n\r\n                if (deviceType === DeviceType.Mouse && this._mouseId >= 0 && this._elementToAttachTo.hasPointerCapture?.(this._mouseId)) {\r\n                    this._elementToAttachTo.releasePointerCapture(this._mouseId);\r\n                } else if (evt.pointerId && this._elementToAttachTo.hasPointerCapture?.(evt.pointerId)) {\r\n                    this._elementToAttachTo.releasePointerCapture(evt.pointerId);\r\n                }\r\n\r\n                this._onInputChanged(deviceType, deviceSlot, deviceEvent);\r\n\r\n                if (deviceType === DeviceType.Touch) {\r\n                    this._onDeviceDisconnected(deviceType, deviceSlot);\r\n                }\r\n            }\r\n        };\r\n\r\n        this._pointerCancelTouch = (pointerId: number) => {\r\n            const deviceSlot = this._activeTouchIds.indexOf(pointerId);\r\n\r\n            // If we're getting a pointercancel event for a touch that isn't active, just return\r\n            if (deviceSlot === -1) {\r\n                return;\r\n            }\r\n\r\n            if (this._elementToAttachTo.hasPointerCapture?.(pointerId)) {\r\n                this._elementToAttachTo.releasePointerCapture(pointerId);\r\n            }\r\n\r\n            this._inputs[DeviceType.Touch][deviceSlot][PointerInput.LeftClick] = 0;\r\n\r\n            const deviceEvent: IUIEvent = DeviceEventFactory.CreateDeviceEvent(DeviceType.Touch, deviceSlot, PointerInput.LeftClick, 0, this, this._elementToAttachTo, pointerId);\r\n\r\n            this._onInputChanged(DeviceType.Touch, deviceSlot, deviceEvent);\r\n\r\n            this._activeTouchIds[deviceSlot] = -1;\r\n            this._onDeviceDisconnected(DeviceType.Touch, deviceSlot);\r\n        };\r\n\r\n        this._pointerCancelEvent = (evt) => {\r\n            if (evt.pointerType === \"mouse\") {\r\n                const pointer = this._inputs[DeviceType.Mouse][0];\r\n\r\n                if (this._mouseId >= 0 && this._elementToAttachTo.hasPointerCapture?.(this._mouseId)) {\r\n                    this._elementToAttachTo.releasePointerCapture(this._mouseId);\r\n                }\r\n\r\n                for (let inputIndex = PointerInput.LeftClick; inputIndex <= PointerInput.BrowserForward; inputIndex++) {\r\n                    if (pointer[inputIndex] === 1) {\r\n                        pointer[inputIndex] = 0;\r\n\r\n                        const deviceEvent: IUIEvent = DeviceEventFactory.CreateDeviceEvent(DeviceType.Mouse, 0, inputIndex, 0, this, this._elementToAttachTo);\r\n\r\n                        this._onInputChanged(DeviceType.Mouse, 0, deviceEvent);\r\n                    }\r\n                }\r\n            } else {\r\n                this._pointerCancelTouch(evt.pointerId);\r\n            }\r\n        };\r\n\r\n        this._pointerLeaveEvent = (evt) => {\r\n            if (evt.pointerType === \"pen\") {\r\n                // If a pen leaves the hover range detectible by the hardware this event is raised and we need to cancel the operation\r\n                // Note that pen operations are treated as touch operations\r\n                this._pointerCancelTouch(evt.pointerId);\r\n            }\r\n        };\r\n\r\n        // Set Wheel Event Name, code originally from scene.inputManager\r\n        this._wheelEventName =\r\n            \"onwheel\" in document.createElement(\"div\")\r\n                ? \"wheel\" // Modern browsers support \"wheel\"\r\n                : (<any>document).onmousewheel !== undefined\r\n                  ? \"mousewheel\" // Webkit and IE support at least \"mousewheel\"\r\n                  : \"DOMMouseScroll\"; // let's assume that remaining browsers are older Firefox\r\n\r\n        // Code originally in scene.inputManager.ts\r\n        // Chrome reports warning in console if wheel listener doesn't set an explicit passive option.\r\n        // IE11 only supports captureEvent:boolean, not options:object, and it defaults to false.\r\n        // Feature detection technique copied from: https://github.com/github/eventlistener-polyfill (MIT license)\r\n        let passiveSupported = false;\r\n        const noop = function () {};\r\n\r\n        try {\r\n            const options = Object.defineProperty({}, \"passive\", {\r\n                get: function () {\r\n                    passiveSupported = true;\r\n                },\r\n            });\r\n\r\n            this._elementToAttachTo.addEventListener(\"test\", noop, options);\r\n            this._elementToAttachTo.removeEventListener(\"test\", noop, options);\r\n        } catch (e) {\r\n            /* */\r\n        }\r\n\r\n        this._pointerBlurEvent = () => {\r\n            // Handle mouse buttons\r\n            if (this.isDeviceAvailable(DeviceType.Mouse)) {\r\n                const pointer = this._inputs[DeviceType.Mouse][0];\r\n\r\n                if (this._mouseId >= 0 && this._elementToAttachTo.hasPointerCapture?.(this._mouseId)) {\r\n                    this._elementToAttachTo.releasePointerCapture(this._mouseId);\r\n                }\r\n\r\n                for (let inputIndex = PointerInput.LeftClick; inputIndex <= PointerInput.BrowserForward; inputIndex++) {\r\n                    if (pointer[inputIndex] === 1) {\r\n                        pointer[inputIndex] = 0;\r\n\r\n                        const deviceEvent: IUIEvent = DeviceEventFactory.CreateDeviceEvent(DeviceType.Mouse, 0, inputIndex, 0, this, this._elementToAttachTo);\r\n\r\n                        this._onInputChanged(DeviceType.Mouse, 0, deviceEvent);\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Handle Active Touches\r\n            if (this.isDeviceAvailable(DeviceType.Touch)) {\r\n                const pointer = this._inputs[DeviceType.Touch];\r\n\r\n                for (let deviceSlot = 0; deviceSlot < this._activeTouchIds.length; deviceSlot++) {\r\n                    const pointerId = this._activeTouchIds[deviceSlot];\r\n\r\n                    if (this._elementToAttachTo.hasPointerCapture?.(pointerId)) {\r\n                        this._elementToAttachTo.releasePointerCapture(pointerId);\r\n                    }\r\n\r\n                    if (pointerId !== -1 && pointer[deviceSlot]?.[PointerInput.LeftClick] === 1) {\r\n                        pointer[deviceSlot][PointerInput.LeftClick] = 0;\r\n\r\n                        const deviceEvent: IUIEvent = DeviceEventFactory.CreateDeviceEvent(\r\n                            DeviceType.Touch,\r\n                            deviceSlot,\r\n                            PointerInput.LeftClick,\r\n                            0,\r\n                            this,\r\n                            this._elementToAttachTo,\r\n                            pointerId\r\n                        );\r\n\r\n                        this._onInputChanged(DeviceType.Touch, deviceSlot, deviceEvent);\r\n\r\n                        this._activeTouchIds[deviceSlot] = -1;\r\n                        this._onDeviceDisconnected(DeviceType.Touch, deviceSlot);\r\n                    }\r\n                }\r\n            }\r\n        };\r\n\r\n        this._pointerWheelEvent = (evt) => {\r\n            const deviceType = DeviceType.Mouse;\r\n            const deviceSlot = 0;\r\n\r\n            if (!this._inputs[deviceType]) {\r\n                this._inputs[deviceType] = [];\r\n            }\r\n\r\n            if (!this._inputs[deviceType][deviceSlot]) {\r\n                this._pointerActive = true;\r\n                this._registerDevice(deviceType, deviceSlot, MAX_POINTER_INPUTS);\r\n            }\r\n\r\n            const pointer = this._inputs[deviceType][deviceSlot];\r\n            if (pointer) {\r\n                pointer[PointerInput.MouseWheelX] = evt.deltaX || 0;\r\n                pointer[PointerInput.MouseWheelY] = evt.deltaY || evt.wheelDelta || 0;\r\n                pointer[PointerInput.MouseWheelZ] = evt.deltaZ || 0;\r\n\r\n                const deviceEvent = evt as IUIEvent;\r\n                // By default, there is no pointerId for mouse wheel events so we'll add one here\r\n                // This logic was originally in the InputManager but was added here to make the\r\n                // InputManager more platform-agnostic\r\n                if (evt.pointerId === undefined) {\r\n                    evt.pointerId = this._mouseId;\r\n                }\r\n\r\n                if (pointer[PointerInput.MouseWheelX] !== 0) {\r\n                    deviceEvent.inputIndex = PointerInput.MouseWheelX;\r\n                    this._onInputChanged(deviceType, deviceSlot, deviceEvent);\r\n                }\r\n                if (pointer[PointerInput.MouseWheelY] !== 0) {\r\n                    deviceEvent.inputIndex = PointerInput.MouseWheelY;\r\n                    this._onInputChanged(deviceType, deviceSlot, deviceEvent);\r\n                }\r\n                if (pointer[PointerInput.MouseWheelZ] !== 0) {\r\n                    deviceEvent.inputIndex = PointerInput.MouseWheelZ;\r\n                    this._onInputChanged(deviceType, deviceSlot, deviceEvent);\r\n                }\r\n            }\r\n        };\r\n\r\n        // Workaround for MacOS Chromium Browsers for lost pointer capture bug\r\n        if (this._usingMacOs && this._isUsingChromium) {\r\n            this._pointerMacOsChromeOutEvent = (evt) => {\r\n                if (evt.buttons > 1) {\r\n                    this._pointerCancelEvent(evt);\r\n                }\r\n            };\r\n            this._elementToAttachTo.addEventListener(\"lostpointercapture\", this._pointerMacOsChromeOutEvent);\r\n        }\r\n\r\n        this._elementToAttachTo.addEventListener(this._eventPrefix + \"move\", this._pointerMoveEvent);\r\n        this._elementToAttachTo.addEventListener(this._eventPrefix + \"down\", this._pointerDownEvent);\r\n        this._elementToAttachTo.addEventListener(this._eventPrefix + \"up\", this._pointerUpEvent);\r\n        this._elementToAttachTo.addEventListener(this._eventPrefix + \"cancel\", this._pointerCancelEvent);\r\n        this._elementToAttachTo.addEventListener(this._eventPrefix + \"leave\", this._pointerLeaveEvent);\r\n        this._elementToAttachTo.addEventListener(\"blur\", this._pointerBlurEvent);\r\n        this._elementToAttachTo.addEventListener(this._wheelEventName, this._pointerWheelEvent, passiveSupported ? { passive: false } : false);\r\n\r\n        // Since there's no up or down event for mouse wheel or delta x/y, clear mouse values at end of frame\r\n        this._pointerInputClearObserver = this._engine.onEndFrameObservable.add(() => {\r\n            if (this.isDeviceAvailable(DeviceType.Mouse)) {\r\n                const pointer = this._inputs[DeviceType.Mouse][0];\r\n                pointer[PointerInput.MouseWheelX] = 0;\r\n                pointer[PointerInput.MouseWheelY] = 0;\r\n                pointer[PointerInput.MouseWheelZ] = 0;\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Handle all actions that come from gamepad interaction\r\n     */\r\n    private _handleGamepadActions(): void {\r\n        this._gamepadConnectedEvent = (evt: any) => {\r\n            this._addGamePad(evt.gamepad);\r\n        };\r\n\r\n        this._gamepadDisconnectedEvent = (evt: any) => {\r\n            if (this._gamepads) {\r\n                const deviceType = this._getGamepadDeviceType(evt.gamepad.id);\r\n                const deviceSlot = evt.gamepad.index;\r\n\r\n                this._unregisterDevice(deviceType, deviceSlot);\r\n                delete this._gamepads[deviceSlot];\r\n            }\r\n        };\r\n\r\n        window.addEventListener(\"gamepadconnected\", this._gamepadConnectedEvent);\r\n        window.addEventListener(\"gamepaddisconnected\", this._gamepadDisconnectedEvent);\r\n    }\r\n\r\n    /**\r\n     * Update all non-event based devices with each frame\r\n     * @param deviceType Enum specifying device type\r\n     * @param deviceSlot \"Slot\" or index that device is referenced in\r\n     * @param inputIndex Id of input to be checked\r\n     */\r\n    private _updateDevice(deviceType: DeviceType, deviceSlot: number, inputIndex: number): void {\r\n        // Gamepads\r\n        const gp = navigator.getGamepads()[deviceSlot];\r\n\r\n        if (gp && deviceType === this._gamepads[deviceSlot]) {\r\n            const device = this._inputs[deviceType][deviceSlot];\r\n\r\n            if (inputIndex >= gp.buttons.length) {\r\n                device[inputIndex] = gp.axes[inputIndex - gp.buttons.length].valueOf();\r\n            } else {\r\n                device[inputIndex] = gp.buttons[inputIndex].value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets DeviceType from the device name\r\n     * @param deviceName Name of Device from DeviceInputSystem\r\n     * @returns DeviceType enum value\r\n     */\r\n    private _getGamepadDeviceType(deviceName: string): DeviceType {\r\n        if (deviceName.indexOf(\"054c\") !== -1) {\r\n            // DualShock 4 Gamepad\r\n            return deviceName.indexOf(\"0ce6\") !== -1 ? DeviceType.DualSense : DeviceType.DualShock;\r\n        } else if (deviceName.indexOf(\"Xbox One\") !== -1 || deviceName.search(\"Xbox 360\") !== -1 || deviceName.search(\"xinput\") !== -1) {\r\n            // Xbox Gamepad\r\n            return DeviceType.Xbox;\r\n        } else if (deviceName.indexOf(\"057e\") !== -1) {\r\n            // Switch Gamepad\r\n            return DeviceType.Switch;\r\n        }\r\n\r\n        return DeviceType.Generic;\r\n    }\r\n\r\n    /**\r\n     * Get DeviceType from a given pointer/mouse/touch event.\r\n     * @param evt PointerEvent to evaluate\r\n     * @returns DeviceType interpreted from event\r\n     */\r\n    private _getPointerType(evt: any): DeviceType {\r\n        let deviceType = DeviceType.Mouse;\r\n\r\n        if (evt.pointerType === \"touch\" || evt.pointerType === \"pen\" || evt.touches) {\r\n            deviceType = DeviceType.Touch;\r\n        }\r\n\r\n        return deviceType;\r\n    }\r\n}\r\n"]}