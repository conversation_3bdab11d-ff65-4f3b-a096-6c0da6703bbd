{"version": 3, "file": "webAudioBusAndSoundSubGraph.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/webAudio/subNodes/webAudioBusAndSoundSubGraph.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,uBAAuB,EAAE,MAAM,kDAAkD,CAAC;AAC3F,OAAO,EAAE,sBAAsB,EAAE,MAAM,iDAAiD,CAAC;AAEzF,OAAO,EAAE,sBAAsB,EAAE,MAAM,iDAAiD,CAAC;AAEzF,OAAO,EAAE,uBAAuB,EAAE,MAAM,wDAAwD,CAAC;AAEjG,OAAO,EAAE,sBAAsB,EAAE,MAAM,uDAAuD,CAAC;AAG/F,OAAO,EAAE,+BAA+B,EAAE,MAAM,0BAA0B,CAAC;AAE3E,OAAO,EAAE,8BAA8B,EAAE,MAAM,yBAAyB,CAAC;AAEzE,OAAO,EAAE,qBAAqB,EAAE,MAAM,wBAAwB,CAAC;AAK/D,gBAAgB;AAChB,MAAM,OAAgB,4BAA6B,SAAQ,qBAAqB;IAAhF;;QACY,cAAS,GAAuB,IAAI,CAAC;QAGnC,eAAU,GAAwB,IAAI,CAAC;IAkIrD,CAAC;IAhIG,gBAAgB;IACA,KAAK,CAAC,SAAS,CAAC,OAAqD;QACjF,MAAM,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAE/B,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,gBAAgB,GAAG,KAAK,CAAC;QAE7B,IAAI,CAAC,iBAAiB,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,CAAC,wBAAwB,sCAAsB,CAAC;QAC9D,CAAC;QACD,IAAI,CAAC,gBAAgB,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,CAAC,wBAAwB,oCAAqB,CAAC;QAC7D,CAAC;QAED,MAAM,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAEjD,IAAI,iBAAiB,EAAE,CAAC;YACpB,uBAAuB,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,gBAAgB,EAAE,CAAC;YACnB,sBAAsB,CAAC,IAAI,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED,gBAAgB;IAChB,IAAoB,OAAO;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,qEAAqE;IAClD,cAAc,CAAC,IAAY;QAC1C,IAAI,CAAC;YACD,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC,CAAA,CAAC;QAEd,QAAQ,IAAI,EAAE,CAAC;YACX;gBACI,OAAO,+BAA+B,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC/D;gBACI,OAAO,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9D;gBACI,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAEkB,kBAAkB;QACjC,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,MAAM,WAAW,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAClD,MAAM,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAEhD,IAAI,WAAW,IAAI,WAAW,CAAC,YAAY,EAAE,KAAK,yBAAyB,EAAE,CAAC;YAC1E,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,UAAU,IAAI,UAAU,CAAC,YAAY,EAAE,KAAK,wBAAwB,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,UAAU,IAAI,UAAU,CAAC,YAAY,EAAE,KAAK,wBAAwB,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,aAAa,EAAE,CAAC;YAE5B,IAAI,UAAU,EAAE,CAAC;gBACb,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,aAAa,EAAE,CAAC;YAE3B,IAAI,UAAU,EAAE,CAAC;gBACb,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACnC,CAAC;QACL,CAAC;QAED,IAAI,WAAW,IAAI,UAAU,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAChE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAE,WAAuC,CAAC,QAAQ,CAAC,CAAC;YAC1E,IAAI,CAAC,SAAS,CAAC,OAAO,CAAE,UAAqC,CAAC,QAAQ,CAAC,CAAC;QAC5E,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC;QAED,IAAI,SAAS,GAA+B,IAAI,CAAC;QAEjD,IAAI,MAAM,GAAwB,IAAI,CAAC;QAEvC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;QAC5B,CAAC;aAAM,CAAC;YACJ,IAAI,WAAW,EAAE,CAAC;gBACd,SAAS,GAAG,WAAsC,CAAC;YACvD,CAAC;iBAAM,IAAI,UAAU,EAAE,CAAC;gBACpB,SAAS,GAAG,UAAoC,CAAC;YACrD,CAAC;iBAAM,IAAI,UAAU,EAAE,CAAC;gBACpB,SAAS,GAAG,UAAoC,CAAC;YACrD,CAAC;YAED,MAAM,GAAG,SAAS,EAAE,IAAI,IAAI,IAAI,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YAC7B,qFAAqF;YACrF,gDAAgD;YAChD,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzC,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;gBACxC,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;oBACrD,IAAI,CAAC,KAA0B,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC3E,CAAC;YACL,CAAC;YAED,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;YAEzB,gFAAgF;YAChF,gDAAgD;YAChD,IAAI,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBAChC,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;gBACxC,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;oBACrD,IAAI,CAAC,KAA0B,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { AbstractAudioNode } from \"../../abstractAudio/abstractAudioNode\";\nimport type { _AbstractAudioSubNode } from \"../../abstractAudio/subNodes/abstractAudioSubNode\";\nimport { AudioSubNode } from \"../../abstractAudio/subNodes/audioSubNode\";\nimport { _GetSpatialAudioSubNode } from \"../../abstractAudio/subNodes/spatialAudioSubNode\";\nimport { _GetStereoAudioSubNode } from \"../../abstractAudio/subNodes/stereoAudioSubNode\";\nimport type { IVolumeAudioOptions } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport { _GetVolumeAudioSubNode } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport type { ISpatialAudioOptions } from \"../../abstractAudio/subProperties/abstractSpatialAudio\";\nimport { _HasSpatialAudioOptions } from \"../../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { IStereoAudioOptions } from \"../../abstractAudio/subProperties/abstractStereoAudio\";\nimport { _HasStereoAudioOptions } from \"../../abstractAudio/subProperties/abstractStereoAudio\";\nimport type { IWebAudioOutNode, IWebAudioSubNode } from \"../webAudioNode\";\nimport type { _SpatialWebAudioSubNode } from \"./spatialWebAudioSubNode\";\nimport { _CreateSpatialAudioSubNodeAsync } from \"./spatialWebAudioSubNode\";\nimport type { _StereoWebAudioSubNode } from \"./stereoWebAudioSubNode\";\nimport { _CreateStereoAudioSubNodeAsync } from \"./stereoWebAudioSubNode\";\nimport type { _VolumeWebAudioSubNode } from \"./volumeWebAudioSubNode\";\nimport { _WebAudioBaseSubGraph } from \"./webAudioBaseSubGraph\";\n\n/** @internal */\nexport interface IWebAudioBusAndSoundSubGraphOptions extends ISpatialAudioOptions, IStereoAudioOptions, IVolumeAudioOptions {}\n\n/** @internal */\nexport abstract class _WebAudioBusAndSoundSubGraph extends _WebAudioBaseSubGraph {\n    private _rootNode: Nullable<GainNode> = null;\n    protected abstract readonly _upstreamNodes: Nullable<Set<AbstractAudioNode>>;\n\n    protected _inputNode: Nullable<AudioNode> = null;\n\n    /** @internal */\n    public override async initAsync(options: Partial<IWebAudioBusAndSoundSubGraphOptions>): Promise<void> {\n        await super.initAsync(options);\n\n        let hasSpatialOptions = false;\n        let hasStereoOptions = false;\n\n        if ((hasSpatialOptions = _HasSpatialAudioOptions(options))) {\n            await this.createAndAddSubNodeAsync(AudioSubNode.SPATIAL);\n        }\n        if ((hasStereoOptions = _HasStereoAudioOptions(options))) {\n            await this.createAndAddSubNodeAsync(AudioSubNode.STEREO);\n        }\n\n        await this._createSubNodePromisesResolvedAsync();\n\n        if (hasSpatialOptions) {\n            _GetSpatialAudioSubNode(this)?.setOptions(options);\n        }\n        if (hasStereoOptions) {\n            _GetStereoAudioSubNode(this)?.setOptions(options);\n        }\n    }\n\n    /** @internal */\n    public override get _inNode(): Nullable<AudioNode> {\n        return this._inputNode;\n    }\n\n    // eslint-disable-next-line @typescript-eslint/promise-function-async\n    protected override _createSubNode(name: string): Promise<_AbstractAudioSubNode> {\n        try {\n            const node = super._createSubNode(name);\n            return node;\n        } catch (e) {}\n\n        switch (name) {\n            case AudioSubNode.SPATIAL:\n                return _CreateSpatialAudioSubNodeAsync(this._owner.engine);\n            case AudioSubNode.STEREO:\n                return _CreateStereoAudioSubNodeAsync(this._owner.engine);\n            default:\n                throw new Error(`Unknown subnode name: ${name}`);\n        }\n    }\n\n    protected override _onSubNodesChanged(): void {\n        super._onSubNodesChanged();\n\n        const spatialNode = _GetSpatialAudioSubNode(this);\n        const stereoNode = _GetStereoAudioSubNode(this);\n        const volumeNode = _GetVolumeAudioSubNode(this);\n\n        if (spatialNode && spatialNode.getClassName() !== \"_SpatialWebAudioSubNode\") {\n            throw new Error(\"Not a WebAudio subnode.\");\n        }\n        if (stereoNode && stereoNode.getClassName() !== \"_StereoWebAudioSubNode\") {\n            throw new Error(\"Not a WebAudio subnode.\");\n        }\n        if (volumeNode && volumeNode.getClassName() !== \"_VolumeWebAudioSubNode\") {\n            throw new Error(\"Not a WebAudio subnode.\");\n        }\n\n        if (spatialNode) {\n            spatialNode.disconnectAll();\n\n            if (volumeNode) {\n                spatialNode.connect(volumeNode);\n            }\n        }\n\n        if (stereoNode) {\n            stereoNode.disconnectAll();\n\n            if (volumeNode) {\n                stereoNode.connect(volumeNode);\n            }\n        }\n\n        if (spatialNode && stereoNode) {\n            this._rootNode = new GainNode(this._owner.engine._audioContext);\n            this._rootNode.connect((spatialNode as _SpatialWebAudioSubNode)._outNode);\n            this._rootNode.connect((stereoNode as _StereoWebAudioSubNode)._outNode);\n        } else {\n            this._rootNode?.disconnect();\n            this._rootNode = null;\n        }\n\n        let inSubNode: Nullable<IWebAudioSubNode> = null;\n\n        let inNode: Nullable<AudioNode> = null;\n\n        if (this._rootNode) {\n            inNode = this._rootNode;\n        } else {\n            if (spatialNode) {\n                inSubNode = spatialNode as _SpatialWebAudioSubNode;\n            } else if (stereoNode) {\n                inSubNode = stereoNode as _StereoWebAudioSubNode;\n            } else if (volumeNode) {\n                inSubNode = volumeNode as _VolumeWebAudioSubNode;\n            }\n\n            inNode = inSubNode?.node ?? null;\n        }\n\n        if (this._inputNode !== inNode) {\n            // Disconnect the wrapped upstream WebAudio nodes from the old wrapped WebAudio node.\n            // The wrapper nodes are unaware of this change.\n            if (this._inputNode && this._upstreamNodes) {\n                const it = this._upstreamNodes.values();\n                for (let next = it.next(); !next.done; next = it.next()) {\n                    (next.value as IWebAudioOutNode)._outNode?.disconnect(this._inputNode);\n                }\n            }\n\n            this._inputNode = inNode;\n\n            // Connect the wrapped upstream WebAudio nodes to the new wrapped WebAudio node.\n            // The wrapper nodes are unaware of this change.\n            if (inNode && this._upstreamNodes) {\n                const it = this._upstreamNodes.values();\n                for (let next = it.next(); !next.done; next = it.next()) {\n                    (next.value as IWebAudioOutNode)._outNode?.connect(inNode);\n                }\n            }\n        }\n    }\n}\n"]}