import type { Mesh } from "../../Meshes/mesh.js";
import type { TransformNode } from "../../Meshes/transformNode.js";
import type { Behavior } from "../../Behaviors/behavior.js";
/**
 * A behavior that when attached to a mesh will will place a specified node on the meshes face pointing towards the camera
 */
export declare class AttachToBoxBehavior implements Behavior<Mesh> {
    private _ui;
    /**
     *  ["AttachToBoxBehavior"] The name of the behavior
     */
    name: string;
    /**
     * [0.15] The distance away from the face of the mesh that the UI should be attached to (default: 0.15)
     */
    distanceAwayFromFace: number;
    /**
     * [0.15] The distance from the bottom of the face that the UI should be attached to (default: 0.15)
     */
    distanceAwayFromBottomOfFace: number;
    private _faceVectors;
    private _target;
    private _scene;
    private _onRenderObserver;
    private _tmpMatrix;
    private _tmpVector;
    /**
     * Creates the AttachToBoxBehavior, used to attach UI to the closest face of the box to a camera
     * @param _ui The transform node that should be attached to the mesh
     */
    constructor(_ui: TransformNode);
    /**
     *  Initializes the behavior
     */
    init(): void;
    private _closestFace;
    private _zeroVector;
    private _lookAtTmpMatrix;
    private _lookAtToRef;
    /**
     * Attaches the AttachToBoxBehavior to the passed in mesh
     * @param target The mesh that the specified node will be attached to
     */
    attach(target: Mesh): void;
    /**
     *  Detaches the behavior from the mesh
     */
    detach(): void;
}
