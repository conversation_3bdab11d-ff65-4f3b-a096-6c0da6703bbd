{"version": 3, "file": "webGLShaderProcessors.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGL/webGLShaderProcessors.ts"], "names": [], "mappings": "AAKA,gBAAgB;AAChB,MAAM,OAAO,oBAAoB;IAAjC;QACW,mBAAc,+BAAuB;IAkBhD,CAAC;IAhBU,aAAa,CAChB,IAAY,EACZ,OAAiB,EACjB,UAAmB,EACnB,iBAAsD,EACtD,UAAoE;QAEpE,oBAAoB;QACpB,IAAI,UAAU,CAAC,4BAA4B,EAAE,CAAC;YAC1C,iHAAiH;YACjH,MAAM,KAAK,GAAG,oDAAoD,CAAC;YACnE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["import { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { IShaderProcessor } from \"../Processors/iShaderProcessor\";\r\nimport type { _IShaderProcessingContext } from \"../Processors/shaderProcessingOptions\";\r\n\r\n/** @internal */\r\nexport class WebGLShaderProcessor implements IShaderProcessor {\r\n    public shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    public postProcessor(\r\n        code: string,\r\n        defines: string[],\r\n        isFragment: boolean,\r\n        processingContext: Nullable<_IShaderProcessingContext>,\r\n        parameters: { [key: string]: number | string | boolean | undefined }\r\n    ) {\r\n        // Remove extensions\r\n        if (parameters.drawBuffersExtensionDisabled) {\r\n            // even if enclosed in #if/#endif, IE11 does parse the #extension declaration, so we need to remove it altogether\r\n            const regex = /#extension.+GL_EXT_draw_buffers.+(enable|require)/g;\r\n            code = code.replace(regex, \"\");\r\n        }\r\n\r\n        return code;\r\n    }\r\n}\r\n"]}