{"version": 3, "file": "abstractAudioAnalyzer.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/abstractAudio/subProperties/abstractAudioAnalyzer.ts"], "names": [], "mappings": "AAGA,MAAM,CAAC,MAAM,sBAAsB,GAAG;IAClC,OAAO,EAAE,IAAgC;IACzC,WAAW,EAAE,CAAC,GAAa;IAC3B,WAAW,EAAE,CAAC,EAAY;IAC1B,SAAS,EAAE,GAAa;CAClB,CAAC;AA+BX;;;GAGG;AACH,MAAM,UAAU,wBAAwB,CAAC,OAAuC;IAC5E,OAAO,CACH,OAAO,CAAC,eAAe;QACvB,OAAO,CAAC,eAAe,KAAK,SAAS;QACrC,OAAO,CAAC,mBAAmB,KAAK,SAAS;QACzC,OAAO,CAAC,mBAAmB,KAAK,SAAS;QACzC,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAC1C,CAAC;AACN,CAAC;AAED;;GAEG;AACH,MAAM,OAAgB,qBAAqB;IAMvC;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;IAC5B,CAAC;CA6CJ", "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/naming-convention\nexport type AudioAnalyzerFFTSizeType = 32 | 64 | 128 | 256 | 512 | 1024 | 2048 | 4096 | 8192 | 16384 | 32768;\n\nexport const _AudioAnalyzerDefaults = {\n    fftSize: 2048 as AudioAnalyzerFFTSizeType,\n    minDecibels: -100 as number,\n    maxDecibels: -30 as number,\n    smoothing: 0.8 as number,\n} as const;\n\n/**\n * Options for the AudioAnalyzer\n */\nexport interface IAudioAnalyzerOptions {\n    /**\n     * Enable the audio analyzer. Defaults to false.\n     */\n    analyzerEnabled: boolean;\n    /**\n     * The size of the FFT (fast fourier transform) to use when converting time-domain data to frequency-domain data. Default is 2048.\n     */\n    analyzerFFTSize: AudioAnalyzerFFTSizeType;\n\n    /**\n     * The minimum decibel value for the range of the analyzer. Default is -100.\n     */\n    analyzerMinDecibels: number;\n\n    /**\n     * The maximum decibel value for the range of the analyzer. Default is -30.\n     */\n    analyzerMaxDecibels: number;\n\n    /**\n     * A number between 0 and 1 that determines how quickly the analyzer's value changes. Default is 0.8.\n     */\n    analyzerSmoothing: number;\n}\n\n/**\n * @param options The audio analyzer options to check.\n * @returns `true` if audio analyzer options are defined, otherwise `false`.\n */\nexport function _HasAudioAnalyzerOptions(options: Partial<IAudioAnalyzerOptions>): boolean {\n    return (\n        options.analyzerEnabled ||\n        options.analyzerFFTSize !== undefined ||\n        options.analyzerMinDecibels !== undefined ||\n        options.analyzerMaxDecibels !== undefined ||\n        options.analyzerSmoothing !== undefined\n    );\n}\n\n/**\n * An AudioAnalyzer converts time-domain audio data into the frequency-domain.\n */\nexport abstract class AbstractAudioAnalyzer {\n    /**\n     * The size of the FFT (fast fourier transform) to use when converting time-domain data to frequency-domain data. Default is 2048.\n     */\n    public abstract fftSize: AudioAnalyzerFFTSizeType;\n\n    /**\n     * The number of data values that will be returned when calling getByteFrequencyData() or getFloatFrequencyData(). This is always half the `fftSize`.\n     */\n    public get frequencyBinCount(): number {\n        return this.fftSize / 2;\n    }\n\n    /**\n     * Whether the analyzer is enabled or not.\n     * - The `getByteFrequencyData` and `getFloatFrequencyData` functions return `null` if the analyzer is not enabled.\n     * @see {@link enableAsync}\n     */\n    public abstract isEnabled: boolean;\n\n    /**\n     * The minimum decibel value for the range of the analyzer. Default is -100.\n     */\n    public abstract minDecibels: number;\n\n    /**\n     * The maximum decibel value for the range of the analyzer. Default is -30.\n     */\n    public abstract maxDecibels: number;\n\n    /**\n     * A number between 0 and 1 that determines how quickly the analyzer's value changes. Default is 0.8.\n     */\n    public abstract smoothing: number;\n\n    /**\n     * Releases associated resources.\n     */\n    public abstract dispose(): void;\n\n    /**\n     * Enables the analyzer\n     */\n    public abstract enableAsync(): Promise<void>;\n\n    /**\n     * Gets the current frequency data as a byte array\n     * @returns a Uint8Array if the analyzer is enabled, otherwise `null`\n     */\n    public abstract getByteFrequencyData(): Uint8Array;\n\n    /**\n     * Gets the current frequency data as a float array\n     * @returns a Float32Array if the analyzer is enabled, otherwise `null`\n     */\n    public abstract getFloatFrequencyData(): Float32Array;\n}\n"]}