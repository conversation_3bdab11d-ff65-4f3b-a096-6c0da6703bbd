{"version": 3, "file": "engine.renderTarget.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.renderTarget.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAG3C,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAC;AAE7E,OAAO,EAAE,gBAAgB,EAAE,4DAAwD;AAEnF,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,0CAA0C,CAAC;AAuClD,UAAU,CAAC,SAAS,CAAC,kCAAkC,GAAG,UAAU,OAAgB,EAAE,MAAe,EAAE,IAAiB;IACpH,MAAM,SAAS,GAAG,IAAI,wBAAwB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACtF,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/C,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAA4B,IAAiB,EAAE,OAA8C;IAC1I,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAA6B,CAAC;IAE1G,IAAI,mBAAmB,GAAG,IAAI,CAAC;IAC/B,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAClC,IAAI,iBAAiB,GAAG,KAAK,CAAC;IAC9B,IAAI,eAAe,GAAgC,SAAS,CAAC;IAC7D,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,KAAK,GAAuB,SAAS,CAAC;IAC1C,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QACvD,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC;QAC1D,qBAAqB,GAAG,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;QACxD,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;QAChD,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC1C,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;QAC/B,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC1B,CAAC;IAED,MAAM,OAAO,GAAG,eAAe,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,6CAAqC,CAAC,CAAC;IACrJ,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;IAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;IAEjG,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;IACpD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,yBAAyB;IACzB,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAC3C,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAC1C,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAElI,mCAAmC;IACnC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACjD,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,gBAAiB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;IAClI,CAAC;IAED,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;IAEjD,SAAS,CAAC,KAAK,GAAG,KAAK,IAAI,qBAAqB,CAAC;IACjD,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;IACrC,SAAS,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;IACrD,SAAS,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;IAEzD,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAE/B,IAAI,CAAC,eAAe,EAAE,CAAC;QACnB,IAAI,CAAC,oCAAoC,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAClE,CAAC;SAAM,CAAC;QACJ,SAAS,CAAC,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC;QAC7C,IAAI,eAAe,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,gBAAgB,GAAI,eAAe,CAAC,gBAAyC,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAE3G,SAAS,CAAC,gBAAgB,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;YAEpD,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YACzD,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;YACpG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;IACL,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,0BAA0B,GAAG,UAAU,IAAiB,EAAE,OAAoC,EAAE,SAAmC;IACpJ,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,MAAM,GAAwE,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;IACtG,MAAM,KAAK,GAAwE,IAAK,CAAC,KAAK,IAAI,CAAC,CAAC;IACpG,IAAI,MAAM,GAAW,EAAE,CAAC,UAAU,CAAC;IACnC,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;QACf,MAAM,GAAG,EAAE,CAAC,gBAAgB,CAAC;IACjC,CAAC;SAAM,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;QACrB,MAAM,GAAG,EAAE,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,IAAI,8CAAqC,CAAC;IACtF,eAAe,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IACtC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,qBAAqB,EAAE,CAAC;QACpC,MAAM,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;QAC5E,OAAO,eAAe,CAAC;IAC3B,CAAC;IAED,MAAM,eAAe,GAAG;QACpB,iBAAiB,EAAE,KAAK;QACxB,kBAAkB,EAAE,CAAC;QACrB,eAAe,EAAE,KAAK;QACtB,GAAG,OAAO;KACb,CAAC;IAEF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAEzD,IAAI,CAAC,yBAAyB,CAC1B,eAAe,EACf,IAAI,EACJ,eAAe,CAAC,kBAAkB,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,iBAAiB,EACpF,eAAe,CAAC,kBAAkB,EAClC,eAAe,CAAC,OAAO,CAC1B,CAAC;IAEF,IAAI,eAAe,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;QACnD,IACI,eAAe,CAAC,kBAAkB,KAAK,SAAS,CAAC,qBAAqB;YACtE,eAAe,CAAC,kBAAkB,KAAK,SAAS,CAAC,qBAAqB;YACtE,eAAe,CAAC,kBAAkB,KAAK,SAAS,CAAC,mCAAmC;YACpF,eAAe,CAAC,kBAAkB,KAAK,SAAS,CAAC,8BAA8B;YAC/E,eAAe,CAAC,kBAAkB,KAAK,SAAS,CAAC,2BAA2B;YAC5E,eAAe,CAAC,kBAAkB,KAAK,SAAS,CAAC,mCAAmC,EACtF,CAAC;YACC,MAAM,CAAC,KAAK,CAAC,iBAAiB,eAAe,CAAC,kBAAkB,2BAA2B,CAAC,CAAC;YAC7F,OAAO,eAAe,CAAC;QAC3B,CAAC;QACD,eAAe,CAAC,MAAM,GAAG,eAAe,CAAC,kBAAkB,CAAC;IAChE,CAAC;SAAM,CAAC;QACJ,eAAe,CAAC,MAAM,GAAG,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC;IAC1I,CAAC;IAED,MAAM,UAAU,GAAG,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC5D,MAAM,IAAI,GAAG,IAAI,CAAC,0CAA0C,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IACrF,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC;IAClE,MAAM,cAAc,GAAG,IAAI,CAAC,wCAAwC,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IAE/G,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;QAC5B,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3H,CAAC;SAAM,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC;QAC9B,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1H,CAAC;SAAM,CAAC;QACJ,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACnH,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAExC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAElD,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;QAChC,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACrD,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACzC,CAAC;IAED,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,gBAAgB,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;IAEnF,SAAS,CAAC,sBAAsB,GAAG,UAAU,CAAC;IAC9C,SAAS,CAAC,+BAA+B,GAAG,UAAU,CAAC;IAEvD,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAClE,SAAS,CAAC,sBAAsB,EAChC,SAAS,CAAC,oBAAoB,EAC9B,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,MAAM,EAChB,SAAS,CAAC,OAAO,EACjB,eAAe,CAAC,MAAM,CACzB,CAAC;IAEF,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnC,OAAO,eAAe,CAAC;AAC3B,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,oCAAoC,GAAG,UAAU,SAA6C,EAAE,OAAe;IAChI,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACtC,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAI,SAAS,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;QAChC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC;IAE3D,kCAAkC;IAClC,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;QAChC,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACrD,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACzC,CAAC;IAED,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;QAC7B,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACjD,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED,MAAM,eAAe,GAAG,SAAS,CAAC,OAAO,EAAE,gBAAkD,CAAC;IAC9F,eAAe,EAAE,wBAAwB,EAAE,CAAC;IAE5C,IAAI,SAAS,CAAC,OAAO,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,8BAA8B,KAAK,UAAU,EAAE,CAAC;QAC9F,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAE3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAClE,CAAC;QAED,SAAS,CAAC,gBAAgB,GAAG,WAAW,CAAC;QACzC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAEzD,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAC9C,SAAS,CAAC,OAAO,CAAC,KAAK,EACvB,SAAS,CAAC,OAAO,CAAC,MAAM,EACxB,OAAO,EACP,CAAC,CAAC,CAAC,cAAc,EACjB,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,EAC1H,EAAE,CAAC,iBAAiB,EACpB,KAAK,CACR,CAAC;QAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAClE,CAAC;QAED,eAAe,EAAE,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;IAC5D,CAAC;IAED,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,gBAAgB,IAAI,SAAS,CAAC,YAAY,CAAC,CAAC;IAEnF,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;QACpB,SAAS,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IACxC,CAAC;IAED,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;IAE7B,MAAM,WAAW,GAAG,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;IAEvG,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAClE,SAAS,CAAC,sBAAsB,EAChC,SAAS,CAAC,oBAAoB,EAC9B,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,MAAM,EAChB,OAAO,EACP,WAAW,CACd,CAAC;IAEF,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnC,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAC7C,eAAgC,EAChC,IAAiB,EACjB,iBAA0B,EAC1B,kBAA0B,EAC1B,OAAO,GAAG,CAAC;IAEX,MAAM,KAAK,GAAwD,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;IAC/F,MAAM,MAAM,GAAwD,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;IACjG,MAAM,MAAM,GAAwE,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;IACtG,MAAM,KAAK,GAAwE,IAAK,CAAC,KAAK,IAAI,CAAC,CAAC;IAEpG,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC;IAClC,eAAe,CAAC,UAAU,GAAG,MAAM,CAAC;IACpC,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;IAC9B,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;IAChC,eAAe,CAAC,SAAS,GAAG,MAAM,GAAG,CAAC,CAAC;IACvC,eAAe,CAAC,KAAK,GAAG,MAAM,IAAI,KAAK,CAAC;IACxC,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC;IAC/B,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;IACxC,eAAe,CAAC,YAAY,GAAG,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC,SAAS,CAAC,4BAA4B,CAAC;IACpI,eAAe,CAAC,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;IAC3D,eAAe,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;IAEzD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;IACvD,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAC5F,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACxE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACxE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC9D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAE9D,yDAAyD;IACzD,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;QACxB,IAAI,kBAAkB,KAAK,CAAC,EAAE,CAAC;YAC3B,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YACpE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACJ,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;YACtE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,EAAE,CAAC,sBAAsB,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { RenderTargetCreationOptions, DepthTextureCreationOptions, TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport { ThinEngine } from \"../thinEngine\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport { WebGLRenderTargetWrapper } from \"../WebGL/webGLRenderTargetWrapper\";\r\nimport type { WebGLHardwareTexture } from \"../WebGL/webGLHardwareTexture\";\r\nimport { HasStencilAspect } from \"core/Materials/Textures/textureHelper.functions\";\r\n\r\nimport { Constants } from \"../constants\";\r\n\r\nimport \"../AbstractEngine/abstractEngine.texture\";\r\n\r\n/**\r\n * Type used to define a texture size (either with a number or with a rect width and height)\r\n * @deprecated please use TextureSize instead\r\n */\r\nexport type RenderTargetTextureSize = TextureSize;\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a new render target texture\r\n         * @param size defines the size of the texture\r\n         * @param options defines the options used to create the texture\r\n         * @returns a new render target wrapper ready to render texture\r\n         */\r\n        createRenderTargetTexture(size: TextureSize, options: boolean | RenderTargetCreationOptions): RenderTargetWrapper;\r\n\r\n        /**\r\n         * Updates the sample count of a render target texture\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#multisample-render-targets\r\n         * @param rtWrapper defines the render target wrapper to update\r\n         * @param samples defines the sample count to set\r\n         * @returns the effective sample count (could be 0 if multisample render targets are not supported)\r\n         */\r\n        updateRenderTargetTextureSampleCount(rtWrapper: Nullable<RenderTargetWrapper>, samples: number): number;\r\n\r\n        /** @internal */\r\n        _createDepthStencilTexture(size: TextureSize, options: DepthTextureCreationOptions, rtWrapper: RenderTargetWrapper): InternalTexture;\r\n\r\n        /** @internal */\r\n        _createHardwareRenderTargetWrapper(isMulti: boolean, isCube: boolean, size: TextureSize): RenderTargetWrapper;\r\n\r\n        /** @internal */\r\n        _setupDepthStencilTexture(internalTexture: InternalTexture, size: TextureSize, bilinearFiltering: boolean, comparisonFunction: number, samples?: number): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype._createHardwareRenderTargetWrapper = function (isMulti: boolean, isCube: boolean, size: TextureSize): RenderTargetWrapper {\r\n    const rtWrapper = new WebGLRenderTargetWrapper(isMulti, isCube, size, this, this._gl);\r\n    this._renderTargetWrapperCache.push(rtWrapper);\r\n    return rtWrapper;\r\n};\r\n\r\nThinEngine.prototype.createRenderTargetTexture = function (this: ThinEngine, size: TextureSize, options: boolean | RenderTargetCreationOptions): RenderTargetWrapper {\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(false, false, size) as WebGLRenderTargetWrapper;\r\n\r\n    let generateDepthBuffer = true;\r\n    let generateStencilBuffer = false;\r\n    let noColorAttachment = false;\r\n    let colorAttachment: InternalTexture | undefined = undefined;\r\n    let samples = 1;\r\n    let label: string | undefined = undefined;\r\n    if (options !== undefined && typeof options === \"object\") {\r\n        generateDepthBuffer = options.generateDepthBuffer ?? true;\r\n        generateStencilBuffer = !!options.generateStencilBuffer;\r\n        noColorAttachment = !!options.noColorAttachment;\r\n        colorAttachment = options.colorAttachment;\r\n        samples = options.samples ?? 1;\r\n        label = options.label;\r\n    }\r\n\r\n    const texture = colorAttachment || (noColorAttachment ? null : this._createInternalTexture(size, options, true, InternalTextureSource.RenderTarget));\r\n    const width = (<{ width: number; height: number; layers?: number }>size).width || <number>size;\r\n    const height = (<{ width: number; height: number; layers?: number }>size).height || <number>size;\r\n\r\n    const currentFrameBuffer = this._currentFramebuffer;\r\n    const gl = this._gl;\r\n\r\n    // Create the framebuffer\r\n    const framebuffer = gl.createFramebuffer();\r\n    this._bindUnboundFramebuffer(framebuffer);\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(generateStencilBuffer, generateDepthBuffer, width, height);\r\n\r\n    // No need to rebind on every frame\r\n    if (texture && !texture.is2DArray && !texture.is3D) {\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture._hardwareTexture!.underlyingResource, 0);\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(currentFrameBuffer);\r\n\r\n    rtWrapper.label = label ?? \"RenderTargetWrapper\";\r\n    rtWrapper._framebuffer = framebuffer;\r\n    rtWrapper._generateDepthBuffer = generateDepthBuffer;\r\n    rtWrapper._generateStencilBuffer = generateStencilBuffer;\r\n\r\n    rtWrapper.setTextures(texture);\r\n\r\n    if (!colorAttachment) {\r\n        this.updateRenderTargetTextureSampleCount(rtWrapper, samples);\r\n    } else {\r\n        rtWrapper._samples = colorAttachment.samples;\r\n        if (colorAttachment.samples > 1) {\r\n            const msaaRenderBuffer = (colorAttachment._hardwareTexture as WebGLHardwareTexture).getMSAARenderBuffer(0);\r\n\r\n            rtWrapper._MSAAFramebuffer = gl.createFramebuffer();\r\n\r\n            this._bindUnboundFramebuffer(rtWrapper._MSAAFramebuffer);\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.RENDERBUFFER, msaaRenderBuffer);\r\n            this._bindUnboundFramebuffer(null);\r\n        }\r\n    }\r\n\r\n    return rtWrapper;\r\n};\r\n\r\nThinEngine.prototype._createDepthStencilTexture = function (size: TextureSize, options: DepthTextureCreationOptions, rtWrapper: WebGLRenderTargetWrapper): InternalTexture {\r\n    const gl = this._gl;\r\n    const layers = (<{ width: number; height: number; depth?: number; layers?: number }>size).layers || 0;\r\n    const depth = (<{ width: number; height: number; depth?: number; layers?: number }>size).depth || 0;\r\n    let target: number = gl.TEXTURE_2D;\r\n    if (layers !== 0) {\r\n        target = gl.TEXTURE_2D_ARRAY;\r\n    } else if (depth !== 0) {\r\n        target = gl.TEXTURE_3D;\r\n    }\r\n    const internalTexture = new InternalTexture(this, InternalTextureSource.DepthStencil);\r\n    internalTexture.label = options.label;\r\n    if (!this._caps.depthTextureExtension) {\r\n        Logger.Error(\"Depth texture is not supported by your browser or hardware.\");\r\n        return internalTexture;\r\n    }\r\n\r\n    const internalOptions = {\r\n        bilinearFiltering: false,\r\n        comparisonFunction: 0,\r\n        generateStencil: false,\r\n        ...options,\r\n    };\r\n\r\n    this._bindTextureDirectly(target, internalTexture, true);\r\n\r\n    this._setupDepthStencilTexture(\r\n        internalTexture,\r\n        size,\r\n        internalOptions.comparisonFunction === 0 ? false : internalOptions.bilinearFiltering,\r\n        internalOptions.comparisonFunction,\r\n        internalOptions.samples\r\n    );\r\n\r\n    if (internalOptions.depthTextureFormat !== undefined) {\r\n        if (\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH16 &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH24 &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8 &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH32_FLOAT &&\r\n            internalOptions.depthTextureFormat !== Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8\r\n        ) {\r\n            Logger.Error(`Depth texture ${internalOptions.depthTextureFormat} format is not supported.`);\r\n            return internalTexture;\r\n        }\r\n        internalTexture.format = internalOptions.depthTextureFormat;\r\n    } else {\r\n        internalTexture.format = internalOptions.generateStencil ? Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 : Constants.TEXTUREFORMAT_DEPTH24;\r\n    }\r\n\r\n    const hasStencil = HasStencilAspect(internalTexture.format);\r\n    const type = this._getWebGLTextureTypeFromDepthTextureFormat(internalTexture.format);\r\n    const format = hasStencil ? gl.DEPTH_STENCIL : gl.DEPTH_COMPONENT;\r\n    const internalFormat = this._getInternalFormatFromDepthTextureFormat(internalTexture.format, true, hasStencil);\r\n\r\n    if (internalTexture.is2DArray) {\r\n        gl.texImage3D(target, 0, internalFormat, internalTexture.width, internalTexture.height, layers, 0, format, type, null);\r\n    } else if (internalTexture.is3D) {\r\n        gl.texImage3D(target, 0, internalFormat, internalTexture.width, internalTexture.height, depth, 0, format, type, null);\r\n    } else {\r\n        gl.texImage2D(target, 0, internalFormat, internalTexture.width, internalTexture.height, 0, format, type, null);\r\n    }\r\n\r\n    this._bindTextureDirectly(target, null);\r\n\r\n    this._internalTexturesCache.push(internalTexture);\r\n\r\n    if (rtWrapper._depthStencilBuffer) {\r\n        gl.deleteRenderbuffer(rtWrapper._depthStencilBuffer);\r\n        rtWrapper._depthStencilBuffer = null;\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(rtWrapper._MSAAFramebuffer ?? rtWrapper._framebuffer);\r\n\r\n    rtWrapper._generateStencilBuffer = hasStencil;\r\n    rtWrapper._depthStencilTextureWithStencil = hasStencil;\r\n\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(\r\n        rtWrapper._generateStencilBuffer,\r\n        rtWrapper._generateDepthBuffer,\r\n        rtWrapper.width,\r\n        rtWrapper.height,\r\n        rtWrapper.samples,\r\n        internalTexture.format\r\n    );\r\n\r\n    this._bindUnboundFramebuffer(null);\r\n\r\n    return internalTexture;\r\n};\r\n\r\nThinEngine.prototype.updateRenderTargetTextureSampleCount = function (rtWrapper: Nullable<WebGLRenderTargetWrapper>, samples: number): number {\r\n    if (this.webGLVersion < 2 || !rtWrapper) {\r\n        return 1;\r\n    }\r\n\r\n    if (rtWrapper.samples === samples) {\r\n        return samples;\r\n    }\r\n\r\n    const gl = this._gl;\r\n\r\n    samples = Math.min(samples, this.getCaps().maxMSAASamples);\r\n\r\n    // Dispose previous render buffers\r\n    if (rtWrapper._depthStencilBuffer) {\r\n        gl.deleteRenderbuffer(rtWrapper._depthStencilBuffer);\r\n        rtWrapper._depthStencilBuffer = null;\r\n    }\r\n\r\n    if (rtWrapper._MSAAFramebuffer) {\r\n        gl.deleteFramebuffer(rtWrapper._MSAAFramebuffer);\r\n        rtWrapper._MSAAFramebuffer = null;\r\n    }\r\n\r\n    const hardwareTexture = rtWrapper.texture?._hardwareTexture as Nullable<WebGLHardwareTexture>;\r\n    hardwareTexture?.releaseMSAARenderBuffers();\r\n\r\n    if (rtWrapper.texture && samples > 1 && typeof gl.renderbufferStorageMultisample === \"function\") {\r\n        const framebuffer = gl.createFramebuffer();\r\n\r\n        if (!framebuffer) {\r\n            throw new Error(\"Unable to create multi sampled framebuffer\");\r\n        }\r\n\r\n        rtWrapper._MSAAFramebuffer = framebuffer;\r\n        this._bindUnboundFramebuffer(rtWrapper._MSAAFramebuffer);\r\n\r\n        const colorRenderbuffer = this._createRenderBuffer(\r\n            rtWrapper.texture.width,\r\n            rtWrapper.texture.height,\r\n            samples,\r\n            -1 /* not used */,\r\n            this._getRGBABufferInternalSizedFormat(rtWrapper.texture.type, rtWrapper.texture.format, rtWrapper.texture._useSRGBBuffer),\r\n            gl.COLOR_ATTACHMENT0,\r\n            false\r\n        );\r\n\r\n        if (!colorRenderbuffer) {\r\n            throw new Error(\"Unable to create multi sampled framebuffer\");\r\n        }\r\n\r\n        hardwareTexture?.addMSAARenderBuffer(colorRenderbuffer);\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(rtWrapper._MSAAFramebuffer ?? rtWrapper._framebuffer);\r\n\r\n    if (rtWrapper.texture) {\r\n        rtWrapper.texture.samples = samples;\r\n    }\r\n\r\n    rtWrapper._samples = samples;\r\n\r\n    const depthFormat = rtWrapper._depthStencilTexture ? rtWrapper._depthStencilTexture.format : undefined;\r\n\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(\r\n        rtWrapper._generateStencilBuffer,\r\n        rtWrapper._generateDepthBuffer,\r\n        rtWrapper.width,\r\n        rtWrapper.height,\r\n        samples,\r\n        depthFormat\r\n    );\r\n\r\n    this._bindUnboundFramebuffer(null);\r\n\r\n    return samples;\r\n};\r\n\r\nThinEngine.prototype._setupDepthStencilTexture = function (\r\n    internalTexture: InternalTexture,\r\n    size: TextureSize,\r\n    bilinearFiltering: boolean,\r\n    comparisonFunction: number,\r\n    samples = 1\r\n) {\r\n    const width = (<{ width: number; height: number; layers?: number }>size).width ?? <number>size;\r\n    const height = (<{ width: number; height: number; layers?: number }>size).height ?? <number>size;\r\n    const layers = (<{ width: number; height: number; depth?: number; layers?: number }>size).layers || 0;\r\n    const depth = (<{ width: number; height: number; depth?: number; layers?: number }>size).depth || 0;\r\n\r\n    internalTexture.baseWidth = width;\r\n    internalTexture.baseHeight = height;\r\n    internalTexture.width = width;\r\n    internalTexture.height = height;\r\n    internalTexture.is2DArray = layers > 0;\r\n    internalTexture.depth = layers || depth;\r\n    internalTexture.isReady = true;\r\n    internalTexture.samples = samples;\r\n    internalTexture.generateMipMaps = false;\r\n    internalTexture.samplingMode = bilinearFiltering ? Constants.TEXTURE_BILINEAR_SAMPLINGMODE : Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n    internalTexture.type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n    internalTexture._comparisonFunction = comparisonFunction;\r\n\r\n    const gl = this._gl;\r\n    const target = this._getTextureTarget(internalTexture);\r\n    const samplingParameters = this._getSamplingParameters(internalTexture.samplingMode, false);\r\n    gl.texParameteri(target, gl.TEXTURE_MAG_FILTER, samplingParameters.mag);\r\n    gl.texParameteri(target, gl.TEXTURE_MIN_FILTER, samplingParameters.min);\r\n    gl.texParameteri(target, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n    gl.texParameteri(target, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n    // TEXTURE_COMPARE_FUNC/MODE are only availble in WebGL2.\r\n    if (this.webGLVersion > 1) {\r\n        if (comparisonFunction === 0) {\r\n            gl.texParameteri(target, gl.TEXTURE_COMPARE_FUNC, Constants.LEQUAL);\r\n            gl.texParameteri(target, gl.TEXTURE_COMPARE_MODE, gl.NONE);\r\n        } else {\r\n            gl.texParameteri(target, gl.TEXTURE_COMPARE_FUNC, comparisonFunction);\r\n            gl.texParameteri(target, gl.TEXTURE_COMPARE_MODE, gl.COMPARE_REF_TO_TEXTURE);\r\n        }\r\n    }\r\n};\r\n"]}