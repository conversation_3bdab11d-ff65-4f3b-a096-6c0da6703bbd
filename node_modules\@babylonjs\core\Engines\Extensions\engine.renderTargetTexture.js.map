{"version": 3, "file": "engine.renderTargetTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.renderTargetTexture.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAiBtD,UAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAAU,OAAe,EAAE,OAAuC,EAAE,OAAsC,EAAE,IAAa;IACnK,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QACxB,OAAO;IACX,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACV,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;IAC3C,CAAC;IAED,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;QAC3C,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAChE,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { RenderTargetTexture } from \"../../Materials/Textures/renderTargetTexture\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Sets a depth stencil texture from a render target to the according uniform.\r\n         * @param channel The texture channel\r\n         * @param uniform The uniform to set\r\n         * @param texture The render target texture containing the depth stencil texture to apply\r\n         * @param name The texture name\r\n         */\r\n        setDepthStencilTexture(channel: number, uniform: Nullable<WebGLUniformLocation>, texture: Nullable<RenderTargetTexture>, name?: string): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.setDepthStencilTexture = function (channel: number, uniform: Nullable<WebGLUniformLocation>, texture: Nullable<RenderTargetTexture>, name?: string): void {\r\n    if (channel === undefined) {\r\n        return;\r\n    }\r\n\r\n    if (uniform) {\r\n        this._boundUniforms[channel] = uniform;\r\n    }\r\n\r\n    if (!texture || !texture.depthStencilTexture) {\r\n        this._setTexture(channel, null, undefined, undefined, name);\r\n    } else {\r\n        this._setTexture(channel, texture, false, true, name);\r\n    }\r\n};\r\n"]}