{"version": 3, "file": "abstractEngine.loadingScreen.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.loadingScreen.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAE/D,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAqCnD,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG;IACxC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;QACzB,OAAO;IACX,CAAC;IACD,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;IACzC,IAAI,aAAa,EAAE,CAAC;QAChB,aAAa,CAAC,gBAAgB,EAAE,CAAC;IACrC,CAAC;AACL,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,aAAa,GAAG;IACrC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;QACzB,OAAO;IACX,CAAC;IACD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;IAC1C,IAAI,aAAa,EAAE,CAAC;QAChB,aAAa,CAAC,aAAa,EAAE,CAAC;IAClC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,eAAe,EAAE;IAC7D,GAAG,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAChD,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5F,CAAC;QACD,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD,GAAG,EAAE,UAAgC,KAAqB;QACtD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,eAAe,EAAE;IAC7D,GAAG,EAAE,UAAgC,KAAa;QAC9C,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7C,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,0BAA0B,EAAE;IACxE,GAAG,EAAE,UAAgC,KAAa;QAC9C,IAAI,CAAC,aAAa,CAAC,wBAAwB,GAAG,KAAK,CAAC;IACxD,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC", "sourcesContent": ["import { IsWindowObjectExist } from \"../../Misc/domManagement\";\r\nimport type { ILoadingScreen } from \"../../Loading/loadingScreen\";\r\nimport { AbstractEngine } from \"../abstractEngine\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Display the loading screen\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/customLoadingScreen\r\n         */\r\n        displayLoadingUI(): void;\r\n\r\n        /**\r\n         * Hide the loading screen\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/customLoadingScreen\r\n         */\r\n        hideLoadingUI(): void;\r\n\r\n        /**\r\n         * Gets or sets the current loading screen object\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/customLoadingScreen\r\n         */\r\n        loadingScreen: ILoadingScreen;\r\n\r\n        /**\r\n         * Sets the current loading screen text\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/customLoadingScreen\r\n         */\r\n        loadingUIText: string;\r\n\r\n        /**\r\n         * Sets the current loading screen background color\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/customLoadingScreen\r\n         */\r\n        loadingUIBackgroundColor: string;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype.displayLoadingUI = function (): void {\r\n    if (!IsWindowObjectExist()) {\r\n        return;\r\n    }\r\n    const loadingScreen = this.loadingScreen;\r\n    if (loadingScreen) {\r\n        loadingScreen.displayLoadingUI();\r\n    }\r\n};\r\n\r\nAbstractEngine.prototype.hideLoadingUI = function (): void {\r\n    if (!IsWindowObjectExist()) {\r\n        return;\r\n    }\r\n    const loadingScreen = this._loadingScreen;\r\n    if (loadingScreen) {\r\n        loadingScreen.hideLoadingUI();\r\n    }\r\n};\r\n\r\nObject.defineProperty(AbstractEngine.prototype, \"loadingScreen\", {\r\n    get: function (this: AbstractEngine) {\r\n        if (!this._loadingScreen && this._renderingCanvas) {\r\n            this._loadingScreen = AbstractEngine.DefaultLoadingScreenFactory(this._renderingCanvas);\r\n        }\r\n        return this._loadingScreen;\r\n    },\r\n    set: function (this: AbstractEngine, value: ILoadingScreen) {\r\n        this._loadingScreen = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(AbstractEngine.prototype, \"loadingUIText\", {\r\n    set: function (this: AbstractEngine, value: string) {\r\n        this.loadingScreen.loadingUIText = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(AbstractEngine.prototype, \"loadingUIBackgroundColor\", {\r\n    set: function (this: AbstractEngine, value: string) {\r\n        this.loadingScreen.loadingUIBackgroundColor = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n"]}