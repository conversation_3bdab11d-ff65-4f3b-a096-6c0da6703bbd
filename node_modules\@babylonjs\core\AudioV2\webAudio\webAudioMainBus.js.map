{"version": 3, "file": "webAudioMainBus.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/webAudio/webAudioMainBus.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAC7D,OAAO,EAAE,qBAAqB,EAAE,MAAM,iCAAiC,CAAC;AAIxE,gBAAgB;AAChB,MAAM,OAAO,gBAAiB,SAAQ,YAAY;IAM9C,gBAAgB;IAChB,YAAmB,IAAY,EAAE,MAAuB;QACpD,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEpB,IAAI,CAAC,SAAS,GAAG,IAAI,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,UAAU,CAAC,OAAsC;QAC1D,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACtB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAEkB,QAAQ,CAAC,IAAqB;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,kBAAkB,CAAC;IAC9B,CAAC;;AAEc,0BAAS,GAAG,KAAM,SAAQ,qBAAqB;IAG1D,IAAc,gBAAgB;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAChD,CAAC;CACJ,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { AbstractAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type { IMainAudioBusOptions } from \"../abstractAudio/mainAudioBus\";\nimport { MainAudioBus } from \"../abstractAudio/mainAudioBus\";\nimport { _WebAudioBaseSubGraph } from \"./subNodes/webAudioBaseSubGraph\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioSuperNode } from \"./webAudioNode\";\n\n/** @internal */\nexport class _WebAudioMainBus extends MainAudioBus implements IWebAudioSuperNode {\n    protected _subGraph: _WebAudioBaseSubGraph;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(name: string, engine: _WebAudioEngine) {\n        super(name, engine);\n\n        this._subGraph = new _WebAudioMainBus._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(options: Partial<IMainAudioBusOptions>): Promise<void> {\n        await this._subGraph.initAsync(options);\n\n        if (this.engine.mainOut) {\n            if (!this._connect(this.engine.mainOut)) {\n                throw new Error(\"Connect failed\");\n            }\n        }\n\n        this.engine._addMainBus(this);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this.engine._removeMainBus(this);\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._subGraph._inNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioMainBus\";\n    }\n\n    private static _SubGraph = class extends _WebAudioBaseSubGraph {\n        protected override _owner: _WebAudioMainBus;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n    };\n}\n"]}