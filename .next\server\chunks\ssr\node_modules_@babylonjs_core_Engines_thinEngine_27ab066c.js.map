{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Engines/thinEngine.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Engines/thinEngine.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { IEffectCreationOptions, IShaderPath } from \"../Materials/effect\";\r\nimport type { _IShaderProcessingContext } from \"./Processors/shaderProcessingOptions\";\r\nimport type { Nullable, DataArray, IndicesArray, FloatArray, DeepImmutable } from \"../types\";\r\nimport type { IColor4Like } from \"../Maths/math.like\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport type { IPipelineContext } from \"./IPipelineContext\";\r\nimport type { WebGLPipelineContext } from \"./WebGL/webGLPipelineContext\";\r\nimport type { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { InstancingAttributeInfo } from \"./instancingAttributeInfo\";\r\nimport type { ThinTexture } from \"../Materials/Textures/thinTexture\";\r\nimport type { IEffectFallbacks } from \"../Materials/iEffectFallbacks\";\r\nimport type { IHardwareTextureWrapper } from \"../Materials/Textures/hardwareTextureWrapper\";\r\nimport type { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport type { IMaterialContext } from \"./IMaterialContext\";\r\nimport type { IDrawContext } from \"./IDrawContext\";\r\nimport type { ICanvas, ICanvasRenderingContext } from \"./ICanvas\";\r\nimport type { IStencilState } from \"../States/IStencilState\";\r\nimport type { InternalTextureCreationOptions, TextureSize } from \"../Materials/Textures/textureCreationOptions\";\r\nimport type { RenderTargetWrapper } from \"./renderTargetWrapper\";\r\nimport type { WebGLRenderTargetWrapper } from \"./WebGL/webGLRenderTargetWrapper\";\r\nimport type { VideoTexture } from \"../Materials/Textures/videoTexture\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport {\r\n    createPipelineContext,\r\n    createRawShaderProgram,\r\n    createShaderProgram,\r\n    _finalizePipelineContext,\r\n    _preparePipelineContext,\r\n    _setProgram,\r\n    _executeWhenRenderingStateIsCompiled,\r\n    getStateObject,\r\n    _createShaderProgram,\r\n    deleteStateObject,\r\n    _isRenderingStateCompiled,\r\n} from \"./thinEngine.functions\";\r\n\r\nimport type { AbstractEngineOptions, ISceneLike, PrepareTextureFunction, PrepareTextureProcessFunction } from \"./abstractEngine\";\r\nimport type { PerformanceMonitor } from \"../Misc/performanceMonitor\";\r\nimport { IsWrapper } from \"../Materials/drawWrapper.functions\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { IsWindowObjectExist } from \"../Misc/domManagement\";\r\nimport { WebGLShaderProcessor } from \"./WebGL/webGLShaderProcessors\";\r\nimport { WebGL2ShaderProcessor } from \"./WebGL/webGL2ShaderProcessors\";\r\nimport { WebGLDataBuffer } from \"../Meshes/WebGL/webGLDataBuffer\";\r\nimport { GetExponentOfTwo } from \"../Misc/tools.functions\";\r\nimport { AbstractEngine } from \"./abstractEngine\";\r\nimport { Constants } from \"./constants\";\r\nimport { WebGLHardwareTexture } from \"./WebGL/webGLHardwareTexture\";\r\nimport { ShaderLanguage } from \"../Materials/shaderLanguage\";\r\nimport { InternalTexture, InternalTextureSource } from \"../Materials/Textures/internalTexture\";\r\nimport { Effect } from \"../Materials/effect\";\r\nimport { _ConcatenateShader, _GetGlobalDefines } from \"./abstractEngine.functions\";\r\nimport { resetCachedPipeline } from \"core/Materials/effect.functions\";\r\nimport { HasStencilAspect, IsDepthTexture } from \"core/Materials/Textures/textureHelper.functions\";\r\n\r\n/**\r\n * Keeps track of all the buffer info used in engine.\r\n */\r\nclass BufferPointer {\r\n    public active: boolean;\r\n    public index: number;\r\n    public size: number;\r\n    public type: number;\r\n    public normalized: boolean;\r\n    public stride: number;\r\n    public offset: number;\r\n    public buffer: WebGLBuffer;\r\n}\r\n\r\n/** Interface defining initialization parameters for Engine class */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface EngineOptions extends AbstractEngineOptions, WebGLContextAttributes {\r\n    /**\r\n     * Defines if webgl2 should be turned off even if supported\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2\r\n     */\r\n    disableWebGL2Support?: boolean;\r\n\r\n    /**\r\n     * Defines that engine should compile shaders with high precision floats (if supported). True by default\r\n     */\r\n    useHighPrecisionFloats?: boolean;\r\n    /**\r\n     * Make the canvas XR Compatible for XR sessions\r\n     */\r\n    xrCompatible?: boolean;\r\n\r\n    /**\r\n     * Will prevent the system from falling back to software implementation if a hardware device cannot be created\r\n     */\r\n    failIfMajorPerformanceCaveat?: boolean;\r\n\r\n    /**\r\n     * If sRGB Buffer support is not set during construction, use this value to force a specific state\r\n     * This is added due to an issue when processing textures in chrome/edge/firefox\r\n     * This will not influence NativeEngine and WebGPUEngine which set the behavior to true during construction.\r\n     */\r\n    forceSRGBBufferSupportState?: boolean;\r\n\r\n    /**\r\n     * Defines if the gl context should be released.\r\n     * It's false by default for backward compatibility, but you should probably pass true (see https://registry.khronos.org/webgl/extensions/WEBGL_lose_context/)\r\n     */\r\n    loseContextOnDispose?: boolean;\r\n}\r\n\r\n/**\r\n * The base engine class (root of all engines)\r\n */\r\nexport class ThinEngine extends AbstractEngine {\r\n    private static _TempClearColorUint32 = new Uint32Array(4);\r\n    private static _TempClearColorInt32 = new Int32Array(4);\r\n\r\n    /** Use this array to turn off some WebGL2 features on known buggy browsers version */\r\n    public static ExceptionList = [\r\n        { key: \"Chrome/63.0\", capture: \"63\\\\.0\\\\.3239\\\\.(\\\\d+)\", captureConstraint: 108, targets: [\"uniformBuffer\"] },\r\n        { key: \"Firefox/58\", capture: null, captureConstraint: null, targets: [\"uniformBuffer\"] },\r\n        { key: \"Firefox/59\", capture: null, captureConstraint: null, targets: [\"uniformBuffer\"] },\r\n        { key: \"Chrome/72.+?Mobile\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Chrome/73.+?Mobile\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Chrome/74.+?Mobile\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Mac OS.+Chrome/71\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Mac OS.+Chrome/72\", capture: null, captureConstraint: null, targets: [\"vao\"] },\r\n        { key: \"Mac OS.+Chrome\", capture: null, captureConstraint: null, targets: [\"uniformBuffer\"] },\r\n        { key: \"Chrome/12\\\\d\\\\..+?Mobile\", capture: null, captureConstraint: null, targets: [\"uniformBuffer\"] },\r\n        // desktop osx safari 15.4\r\n        { key: \".*AppleWebKit.*(15.4).*Safari\", capture: null, captureConstraint: null, targets: [\"antialias\", \"maxMSAASamples\"] },\r\n        // mobile browsers using safari 15.4 on ios\r\n        { key: \".*(15.4).*AppleWebKit.*Safari\", capture: null, captureConstraint: null, targets: [\"antialias\", \"maxMSAASamples\"] },\r\n    ];\r\n\r\n    /** @internal */\r\n    protected override _name = \"WebGL\";\r\n\r\n    /**\r\n     * Gets or sets the name of the engine\r\n     */\r\n    public override get name(): string {\r\n        return this._name;\r\n    }\r\n\r\n    public override set name(value: string) {\r\n        this._name = value;\r\n    }\r\n\r\n    /**\r\n     * Returns the version of the engine\r\n     */\r\n    public get version(): number {\r\n        return this._webGLVersion;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the relative url used to load shaders if using the engine in non-minified mode\r\n     */\r\n    public static get ShadersRepository(): string {\r\n        return Effect.ShadersRepository;\r\n    }\r\n    public static set ShadersRepository(value: string) {\r\n        Effect.ShadersRepository = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean that indicates if textures must be forced to power of 2 size even if not required\r\n     */\r\n    public forcePOTTextures = false;\r\n\r\n    /** Gets or sets a boolean indicating if the engine should validate programs after compilation */\r\n    public validateShaderPrograms = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating that uniform buffers must be disabled even if they are supported\r\n     */\r\n    public disableUniformBuffers = false;\r\n\r\n    /**\r\n     * Gets a boolean indicating that the engine supports uniform buffers\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n     */\r\n    public get supportsUniformBuffers(): boolean {\r\n        return this.webGLVersion > 1 && !this.disableUniformBuffers;\r\n    }\r\n\r\n    // Private Members\r\n\r\n    /** @internal */\r\n    public _gl: WebGL2RenderingContext;\r\n    /** @internal */\r\n    public _webGLVersion = 1.0;\r\n\r\n    /** @internal */\r\n    public _glSRGBExtensionValues: {\r\n        SRGB: typeof WebGL2RenderingContext.SRGB;\r\n        SRGB8: typeof WebGL2RenderingContext.SRGB8 | EXT_sRGB[\"SRGB_ALPHA_EXT\"];\r\n        SRGB8_ALPHA8: typeof WebGL2RenderingContext.SRGB8_ALPHA8 | EXT_sRGB[\"SRGB_ALPHA_EXT\"];\r\n    };\r\n\r\n    /**\r\n     * Gets a boolean indicating that only power of 2 textures are supported\r\n     * Please note that you can still use non power of 2 textures but in this case the engine will forcefully convert them\r\n     */\r\n    public get needPOTTextures(): boolean {\r\n        return this._webGLVersion < 2 || this.forcePOTTextures;\r\n    }\r\n\r\n    private _glVersion: string;\r\n    private _glRenderer: string;\r\n    private _glVendor: string;\r\n\r\n    // Cache\r\n\r\n    /** @internal */\r\n    public _currentMaterialContext: IMaterialContext;\r\n    /** @internal */\r\n    protected _currentProgram: Nullable<WebGLProgram>;\r\n    private _vertexAttribArraysEnabled: boolean[] = [];\r\n    private _cachedVertexArrayObject: Nullable<WebGLVertexArrayObject>;\r\n\r\n    private _uintIndicesCurrentlySet = false;\r\n    protected _currentBoundBuffer = new Array<Nullable<DataBuffer>>();\r\n    /** @internal */\r\n    public _currentFramebuffer: Nullable<WebGLFramebuffer> = null;\r\n    /** @internal */\r\n    public _dummyFramebuffer: Nullable<WebGLFramebuffer> = null;\r\n    private _currentBufferPointers = new Array<BufferPointer>();\r\n    private _currentInstanceLocations = new Array<number>();\r\n    private _currentInstanceBuffers = new Array<DataBuffer>();\r\n    private _textureUnits: Int32Array;\r\n\r\n    /** @internal */\r\n    public _workingCanvas: Nullable<ICanvas>;\r\n    /** @internal */\r\n    public _workingContext: Nullable<ICanvasRenderingContext>;\r\n\r\n    private _vaoRecordInProgress = false;\r\n    private _mustWipeVertexAttributes = false;\r\n\r\n    private _nextFreeTextureSlots = new Array<number>();\r\n    private _maxSimultaneousTextures = 0;\r\n    private _maxMSAASamplesOverride: Nullable<number> = null;\r\n\r\n    protected get _supportsHardwareTextureRescaling() {\r\n        return false;\r\n    }\r\n\r\n    protected _framebufferDimensionsObject: Nullable<{ framebufferWidth: number; framebufferHeight: number }>;\r\n\r\n    /**\r\n     * sets the object from which width and height will be taken from when getting render width and height\r\n     * Will fallback to the gl object\r\n     * @param dimensions the framebuffer width and height that will be used.\r\n     */\r\n    public set framebufferDimensionsObject(dimensions: Nullable<{ framebufferWidth: number; framebufferHeight: number }>) {\r\n        this._framebufferDimensionsObject = dimensions;\r\n    }\r\n\r\n    /**\r\n     * Creates a new snapshot at the next frame using the current snapshotRenderingMode\r\n     */\r\n    public snapshotRenderingReset(): void {\r\n        this.snapshotRendering = false;\r\n    }\r\n\r\n    /**\r\n     * Creates a new engine\r\n     * @param canvasOrContext defines the canvas or WebGL context to use for rendering. If you provide a WebGL context, Babylon.js will not hook events on the canvas (like pointers, keyboards, etc...) so no event observables will be available. This is mostly used when Babylon.js is used as a plugin on a system which already used the WebGL context\r\n     * @param antialias defines whether anti-aliasing should be enabled (default value is \"undefined\", meaning that the browser may or may not enable it)\r\n     * @param options defines further options to be sent to the getContext() function\r\n     * @param adaptToDeviceRatio defines whether to adapt to the device's viewport characteristics (default: false)\r\n     */\r\n    constructor(\r\n        canvasOrContext: Nullable<HTMLCanvasElement | OffscreenCanvas | WebGLRenderingContext | WebGL2RenderingContext>,\r\n        antialias?: boolean,\r\n        options?: EngineOptions,\r\n        adaptToDeviceRatio?: boolean\r\n    ) {\r\n        options = options || {};\r\n        super(antialias ?? options.antialias, options, adaptToDeviceRatio);\r\n\r\n        if (!canvasOrContext) {\r\n            return;\r\n        }\r\n\r\n        let canvas: Nullable<HTMLCanvasElement> = null;\r\n        if ((canvasOrContext as any).getContext) {\r\n            canvas = <HTMLCanvasElement>canvasOrContext;\r\n\r\n            if (options.preserveDrawingBuffer === undefined) {\r\n                options.preserveDrawingBuffer = false;\r\n            }\r\n\r\n            if (options.xrCompatible === undefined) {\r\n                options.xrCompatible = false;\r\n            }\r\n\r\n            // Exceptions\r\n            if (navigator && navigator.userAgent) {\r\n                this._setupMobileChecks();\r\n\r\n                const ua = navigator.userAgent;\r\n                for (const exception of ThinEngine.ExceptionList) {\r\n                    const key = exception.key;\r\n                    const targets = exception.targets;\r\n                    const check = new RegExp(key);\r\n\r\n                    if (check.test(ua)) {\r\n                        if (exception.capture && exception.captureConstraint) {\r\n                            const capture = exception.capture;\r\n                            const constraint = exception.captureConstraint;\r\n\r\n                            const regex = new RegExp(capture);\r\n                            const matches = regex.exec(ua);\r\n\r\n                            if (matches && matches.length > 0) {\r\n                                const capturedValue = parseInt(matches[matches.length - 1]);\r\n                                if (capturedValue >= constraint) {\r\n                                    continue;\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        for (const target of targets) {\r\n                            switch (target) {\r\n                                case \"uniformBuffer\":\r\n                                    this.disableUniformBuffers = true;\r\n                                    break;\r\n                                case \"vao\":\r\n                                    this.disableVertexArrayObjects = true;\r\n                                    break;\r\n                                case \"antialias\":\r\n                                    options.antialias = false;\r\n                                    break;\r\n                                case \"maxMSAASamples\":\r\n                                    this._maxMSAASamplesOverride = 1;\r\n                                    break;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Context lost\r\n            if (!this._doNotHandleContextLost) {\r\n                this._onContextLost = (evt: Event) => {\r\n                    evt.preventDefault();\r\n                    this._contextWasLost = true;\r\n                    deleteStateObject(this._gl);\r\n                    Logger.Warn(\"WebGL context lost.\");\r\n\r\n                    this.onContextLostObservable.notifyObservers(this);\r\n                };\r\n\r\n                this._onContextRestored = () => {\r\n                    this._restoreEngineAfterContextLost(() => this._initGLContext());\r\n                };\r\n\r\n                canvas.addEventListener(\"webglcontextrestored\", this._onContextRestored, false);\r\n\r\n                options.powerPreference = options.powerPreference || \"high-performance\";\r\n            } else {\r\n                this._onContextLost = () => {\r\n                    deleteStateObject(this._gl);\r\n                };\r\n            }\r\n\r\n            canvas.addEventListener(\"webglcontextlost\", this._onContextLost, false);\r\n\r\n            if (this._badDesktopOS) {\r\n                options.xrCompatible = false;\r\n            }\r\n\r\n            // GL\r\n            if (!options.disableWebGL2Support) {\r\n                try {\r\n                    this._gl = <any>(canvas.getContext(\"webgl2\", options) || canvas.getContext(\"experimental-webgl2\", options));\r\n                    if (this._gl) {\r\n                        this._webGLVersion = 2.0;\r\n                        this._shaderPlatformName = \"WEBGL2\";\r\n\r\n                        // Prevent weird browsers to lie (yeah that happens!)\r\n                        if (!this._gl.deleteQuery) {\r\n                            this._webGLVersion = 1.0;\r\n                            this._shaderPlatformName = \"WEBGL1\";\r\n                        }\r\n                    }\r\n                } catch (e) {\r\n                    // Do nothing\r\n                }\r\n            }\r\n\r\n            if (!this._gl) {\r\n                if (!canvas) {\r\n                    throw new Error(\"The provided canvas is null or undefined.\");\r\n                }\r\n                try {\r\n                    this._gl = <WebGL2RenderingContext>(canvas.getContext(\"webgl\", options) || canvas.getContext(\"experimental-webgl\", options));\r\n                } catch (e) {\r\n                    throw new Error(\"WebGL not supported\");\r\n                }\r\n            }\r\n\r\n            if (!this._gl) {\r\n                throw new Error(\"WebGL not supported\");\r\n            }\r\n        } else {\r\n            this._gl = <WebGL2RenderingContext>canvasOrContext;\r\n            canvas = this._gl.canvas as HTMLCanvasElement;\r\n\r\n            if ((this._gl as any).renderbufferStorageMultisample) {\r\n                this._webGLVersion = 2.0;\r\n                this._shaderPlatformName = \"WEBGL2\";\r\n            } else {\r\n                this._shaderPlatformName = \"WEBGL1\";\r\n            }\r\n\r\n            const attributes = this._gl.getContextAttributes();\r\n            if (attributes) {\r\n                options.stencil = attributes.stencil;\r\n            }\r\n        }\r\n\r\n        this._sharedInit(canvas);\r\n\r\n        // Ensures a consistent color space unpacking of textures cross browser.\r\n        this._gl.pixelStorei(this._gl.UNPACK_COLORSPACE_CONVERSION_WEBGL, this._gl.NONE);\r\n\r\n        if (options.useHighPrecisionFloats !== undefined) {\r\n            this._highPrecisionShadersAllowed = options.useHighPrecisionFloats;\r\n        }\r\n\r\n        this.resize();\r\n\r\n        this._initGLContext();\r\n        this._initFeatures();\r\n\r\n        // Prepare buffer pointers\r\n        for (let i = 0; i < this._caps.maxVertexAttribs; i++) {\r\n            this._currentBufferPointers[i] = new BufferPointer();\r\n        }\r\n\r\n        // Shader processor\r\n        this._shaderProcessor = this.webGLVersion > 1 ? new WebGL2ShaderProcessor() : new WebGLShaderProcessor();\r\n\r\n        // Starting with iOS 14, we can trust the browser\r\n        // let matches = navigator.userAgent.match(/Version\\/(\\d+)/);\r\n\r\n        // if (matches && matches.length === 2) {\r\n        //     if (parseInt(matches[1]) >= 14) {\r\n        //         this._badOS = false;\r\n        //     }\r\n        // }\r\n\r\n        const versionToLog = `Babylon.js v${ThinEngine.Version}`;\r\n        Logger.Log(versionToLog + ` - ${this.description}`);\r\n\r\n        // Check setAttribute in case of workers\r\n        if (this._renderingCanvas && this._renderingCanvas.setAttribute) {\r\n            this._renderingCanvas.setAttribute(\"data-engine\", versionToLog);\r\n        }\r\n        const stateObject = getStateObject(this._gl);\r\n        // update state object with the current engine state\r\n        stateObject.validateShaderPrograms = this.validateShaderPrograms;\r\n        stateObject.parallelShaderCompile = this._caps.parallelShaderCompile;\r\n    }\r\n\r\n    protected override _clearEmptyResources(): void {\r\n        this._dummyFramebuffer = null;\r\n        super._clearEmptyResources();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getShaderProcessingContext(shaderLanguage: ShaderLanguage): Nullable<_IShaderProcessingContext> {\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if all created effects are ready\r\n     * @returns true if all effects are ready\r\n     */\r\n    public areAllEffectsReady(): boolean {\r\n        for (const key in this._compiledEffects) {\r\n            const effect = this._compiledEffects[key];\r\n\r\n            if (!effect.isReady()) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    protected _initGLContext(): void {\r\n        // Caps\r\n        this._caps = {\r\n            maxTexturesImageUnits: this._gl.getParameter(this._gl.MAX_TEXTURE_IMAGE_UNITS),\r\n            maxCombinedTexturesImageUnits: this._gl.getParameter(this._gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS),\r\n            maxVertexTextureImageUnits: this._gl.getParameter(this._gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS),\r\n            maxTextureSize: this._gl.getParameter(this._gl.MAX_TEXTURE_SIZE),\r\n            maxSamples: this._webGLVersion > 1 ? this._gl.getParameter(this._gl.MAX_SAMPLES) : 1,\r\n            maxCubemapTextureSize: this._gl.getParameter(this._gl.MAX_CUBE_MAP_TEXTURE_SIZE),\r\n            maxRenderTextureSize: this._gl.getParameter(this._gl.MAX_RENDERBUFFER_SIZE),\r\n            maxVertexAttribs: this._gl.getParameter(this._gl.MAX_VERTEX_ATTRIBS),\r\n            maxVaryingVectors: this._gl.getParameter(this._gl.MAX_VARYING_VECTORS),\r\n            maxFragmentUniformVectors: this._gl.getParameter(this._gl.MAX_FRAGMENT_UNIFORM_VECTORS),\r\n            maxVertexUniformVectors: this._gl.getParameter(this._gl.MAX_VERTEX_UNIFORM_VECTORS),\r\n            parallelShaderCompile: this._gl.getExtension(\"KHR_parallel_shader_compile\") || undefined,\r\n            standardDerivatives: this._webGLVersion > 1 || this._gl.getExtension(\"OES_standard_derivatives\") !== null,\r\n            maxAnisotropy: 1,\r\n            astc: this._gl.getExtension(\"WEBGL_compressed_texture_astc\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_astc\"),\r\n            bptc: this._gl.getExtension(\"EXT_texture_compression_bptc\") || this._gl.getExtension(\"WEBKIT_EXT_texture_compression_bptc\"),\r\n            s3tc: this._gl.getExtension(\"WEBGL_compressed_texture_s3tc\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_s3tc\"),\r\n            // eslint-disable-next-line @typescript-eslint/naming-convention\r\n            s3tc_srgb: this._gl.getExtension(\"WEBGL_compressed_texture_s3tc_srgb\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_s3tc_srgb\"),\r\n            pvrtc: this._gl.getExtension(\"WEBGL_compressed_texture_pvrtc\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_pvrtc\"),\r\n            etc1: this._gl.getExtension(\"WEBGL_compressed_texture_etc1\") || this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_etc1\"),\r\n            etc2:\r\n                this._gl.getExtension(\"WEBGL_compressed_texture_etc\") ||\r\n                this._gl.getExtension(\"WEBKIT_WEBGL_compressed_texture_etc\") ||\r\n                this._gl.getExtension(\"WEBGL_compressed_texture_es3_0\"), // also a requirement of OpenGL ES 3\r\n            textureAnisotropicFilterExtension:\r\n                this._gl.getExtension(\"EXT_texture_filter_anisotropic\") ||\r\n                this._gl.getExtension(\"WEBKIT_EXT_texture_filter_anisotropic\") ||\r\n                this._gl.getExtension(\"MOZ_EXT_texture_filter_anisotropic\"),\r\n            uintIndices: this._webGLVersion > 1 || this._gl.getExtension(\"OES_element_index_uint\") !== null,\r\n            fragmentDepthSupported: this._webGLVersion > 1 || this._gl.getExtension(\"EXT_frag_depth\") !== null,\r\n            highPrecisionShaderSupported: false,\r\n            timerQuery: this._gl.getExtension(\"EXT_disjoint_timer_query_webgl2\") || this._gl.getExtension(\"EXT_disjoint_timer_query\"),\r\n            supportOcclusionQuery: this._webGLVersion > 1,\r\n            canUseTimestampForTimerQuery: false,\r\n            drawBuffersExtension: false,\r\n            maxMSAASamples: 1,\r\n            colorBufferFloat: !!(this._webGLVersion > 1 && this._gl.getExtension(\"EXT_color_buffer_float\")),\r\n            supportFloatTexturesResolve: false,\r\n            rg11b10ufColorRenderable: false,\r\n            colorBufferHalfFloat: !!(this._webGLVersion > 1 && this._gl.getExtension(\"EXT_color_buffer_half_float\")),\r\n            textureFloat: this._webGLVersion > 1 || this._gl.getExtension(\"OES_texture_float\") ? true : false,\r\n            textureHalfFloat: this._webGLVersion > 1 || this._gl.getExtension(\"OES_texture_half_float\") ? true : false,\r\n            textureHalfFloatRender: false,\r\n            textureFloatLinearFiltering: false,\r\n            textureFloatRender: false,\r\n            textureHalfFloatLinearFiltering: false,\r\n            vertexArrayObject: false,\r\n            instancedArrays: false,\r\n            textureLOD: this._webGLVersion > 1 || this._gl.getExtension(\"EXT_shader_texture_lod\") ? true : false,\r\n            texelFetch: this._webGLVersion !== 1,\r\n            blendMinMax: false,\r\n            multiview: this._gl.getExtension(\"OVR_multiview2\"),\r\n            oculusMultiview: this._gl.getExtension(\"OCULUS_multiview\"),\r\n            depthTextureExtension: false,\r\n            canUseGLInstanceID: this._webGLVersion > 1,\r\n            canUseGLVertexID: this._webGLVersion > 1,\r\n            supportComputeShaders: false,\r\n            supportSRGBBuffers: false,\r\n            supportTransformFeedbacks: this._webGLVersion > 1,\r\n            textureMaxLevel: this._webGLVersion > 1,\r\n            texture2DArrayMaxLayerCount: this._webGLVersion > 1 ? this._gl.getParameter(this._gl.MAX_ARRAY_TEXTURE_LAYERS) : 128,\r\n            disableMorphTargetTexture: false,\r\n            textureNorm16: this._gl.getExtension(\"EXT_texture_norm16\") ? true : false,\r\n            blendParametersPerTarget: false,\r\n            dualSourceBlending: false,\r\n        };\r\n\r\n        this._caps.supportFloatTexturesResolve = this._caps.colorBufferFloat;\r\n        this._caps.rg11b10ufColorRenderable = this._caps.colorBufferFloat;\r\n\r\n        // Infos\r\n        this._glVersion = this._gl.getParameter(this._gl.VERSION);\r\n\r\n        const rendererInfo: any = this._gl.getExtension(\"WEBGL_debug_renderer_info\");\r\n        if (rendererInfo != null) {\r\n            this._glRenderer = this._gl.getParameter(rendererInfo.UNMASKED_RENDERER_WEBGL);\r\n            this._glVendor = this._gl.getParameter(rendererInfo.UNMASKED_VENDOR_WEBGL);\r\n        }\r\n\r\n        if (!this._glVendor) {\r\n            this._glVendor = this._gl.getParameter(this._gl.VENDOR) || \"Unknown vendor\";\r\n        }\r\n\r\n        if (!this._glRenderer) {\r\n            this._glRenderer = this._gl.getParameter(this._gl.RENDERER) || \"Unknown renderer\";\r\n        }\r\n\r\n        // Constants\r\n        if (this._gl.HALF_FLOAT_OES !== 0x8d61) {\r\n            this._gl.HALF_FLOAT_OES = 0x8d61; // Half floating-point type (16-bit).\r\n        }\r\n        if (this._gl.RGBA16F !== 0x881a) {\r\n            this._gl.RGBA16F = 0x881a; // RGBA 16-bit floating-point color-renderable internal sized format.\r\n        }\r\n        if (this._gl.RGBA32F !== 0x8814) {\r\n            this._gl.RGBA32F = 0x8814; // RGBA 32-bit floating-point color-renderable internal sized format.\r\n        }\r\n        if (this._gl.DEPTH24_STENCIL8 !== 35056) {\r\n            this._gl.DEPTH24_STENCIL8 = 35056;\r\n        }\r\n\r\n        // Extensions\r\n        if (this._caps.timerQuery) {\r\n            if (this._webGLVersion === 1) {\r\n                this._gl.getQuery = (<any>this._caps.timerQuery).getQueryEXT.bind(this._caps.timerQuery);\r\n            }\r\n            // WebGLQuery casted to number to avoid TS error\r\n            this._caps.canUseTimestampForTimerQuery = ((this._gl.getQuery(this._caps.timerQuery.TIMESTAMP_EXT, this._caps.timerQuery.QUERY_COUNTER_BITS_EXT) as number) ?? 0) > 0;\r\n        }\r\n\r\n        this._caps.maxAnisotropy = this._caps.textureAnisotropicFilterExtension\r\n            ? this._gl.getParameter(this._caps.textureAnisotropicFilterExtension.MAX_TEXTURE_MAX_ANISOTROPY_EXT)\r\n            : 0;\r\n        this._caps.textureFloatLinearFiltering = this._caps.textureFloat && this._gl.getExtension(\"OES_texture_float_linear\") ? true : false;\r\n        this._caps.textureFloatRender = this._caps.textureFloat && this._canRenderToFloatFramebuffer() ? true : false;\r\n        this._caps.textureHalfFloatLinearFiltering =\r\n            this._webGLVersion > 1 || (this._caps.textureHalfFloat && this._gl.getExtension(\"OES_texture_half_float_linear\")) ? true : false;\r\n\r\n        if (this._caps.textureNorm16) {\r\n            this._gl.R16_EXT = 0x822a;\r\n            this._gl.RG16_EXT = 0x822c;\r\n            this._gl.RGB16_EXT = 0x8054;\r\n            this._gl.RGBA16_EXT = 0x805b;\r\n            this._gl.R16_SNORM_EXT = 0x8f98;\r\n            this._gl.RG16_SNORM_EXT = 0x8f99;\r\n            this._gl.RGB16_SNORM_EXT = 0x8f9a;\r\n            this._gl.RGBA16_SNORM_EXT = 0x8f9b;\r\n        }\r\n\r\n        const oesDrawBuffersIndexed = this._gl.getExtension(\"OES_draw_buffers_indexed\");\r\n        this._caps.blendParametersPerTarget = oesDrawBuffersIndexed ? true : false;\r\n\r\n        if (oesDrawBuffersIndexed) {\r\n            this._gl.blendEquationSeparateIndexed = oesDrawBuffersIndexed.blendEquationSeparateiOES.bind(oesDrawBuffersIndexed);\r\n            this._gl.blendEquationIndexed = oesDrawBuffersIndexed.blendEquationiOES.bind(oesDrawBuffersIndexed);\r\n            this._gl.blendFuncSeparateIndexed = oesDrawBuffersIndexed.blendFuncSeparateiOES.bind(oesDrawBuffersIndexed);\r\n            this._gl.blendFuncIndexed = oesDrawBuffersIndexed.blendFunciOES.bind(oesDrawBuffersIndexed);\r\n            this._gl.colorMaskIndexed = oesDrawBuffersIndexed.colorMaskiOES.bind(oesDrawBuffersIndexed);\r\n            this._gl.disableIndexed = oesDrawBuffersIndexed.disableiOES.bind(oesDrawBuffersIndexed);\r\n            this._gl.enableIndexed = oesDrawBuffersIndexed.enableiOES.bind(oesDrawBuffersIndexed);\r\n        }\r\n\r\n        this._caps.dualSourceBlending = this._gl.getExtension(\"WEBGL_blend_func_extended\") ? true : false;\r\n\r\n        // Compressed formats\r\n        if (this._caps.astc) {\r\n            this._gl.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR = this._caps.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR;\r\n        }\r\n        if (this._caps.bptc) {\r\n            this._gl.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT = this._caps.bptc.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT;\r\n        }\r\n        if (this._caps.s3tc_srgb) {\r\n            this._gl.COMPRESSED_SRGB_S3TC_DXT1_EXT = this._caps.s3tc_srgb.COMPRESSED_SRGB_S3TC_DXT1_EXT;\r\n            this._gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT = this._caps.s3tc_srgb.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;\r\n            this._gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT = this._caps.s3tc_srgb.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT;\r\n        }\r\n        if (this._caps.etc2) {\r\n            this._gl.COMPRESSED_SRGB8_ETC2 = this._caps.etc2.COMPRESSED_SRGB8_ETC2;\r\n            this._gl.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC = this._caps.etc2.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC;\r\n        }\r\n\r\n        // Checks if some of the format renders first to allow the use of webgl inspector.\r\n        if (this._webGLVersion > 1) {\r\n            if (this._gl.HALF_FLOAT_OES !== 0x140b) {\r\n                this._gl.HALF_FLOAT_OES = 0x140b;\r\n            }\r\n        }\r\n        this._caps.textureHalfFloatRender = this._caps.textureHalfFloat && this._canRenderToHalfFloatFramebuffer();\r\n        // Draw buffers\r\n        if (this._webGLVersion > 1) {\r\n            this._caps.drawBuffersExtension = true;\r\n            this._caps.maxMSAASamples = this._maxMSAASamplesOverride !== null ? this._maxMSAASamplesOverride : this._gl.getParameter(this._gl.MAX_SAMPLES);\r\n            this._caps.maxDrawBuffers = this._gl.getParameter(this._gl.MAX_DRAW_BUFFERS);\r\n        } else {\r\n            const drawBuffersExtension = this._gl.getExtension(\"WEBGL_draw_buffers\");\r\n\r\n            if (drawBuffersExtension !== null) {\r\n                this._caps.drawBuffersExtension = true;\r\n                this._gl.drawBuffers = drawBuffersExtension.drawBuffersWEBGL.bind(drawBuffersExtension);\r\n                this._caps.maxDrawBuffers = this._gl.getParameter(drawBuffersExtension.MAX_DRAW_BUFFERS_WEBGL);\r\n                (this._gl.DRAW_FRAMEBUFFER as any) = this._gl.FRAMEBUFFER;\r\n\r\n                for (let i = 0; i < 16; i++) {\r\n                    (<any>this._gl)[\"COLOR_ATTACHMENT\" + i + \"_WEBGL\"] = (<any>drawBuffersExtension)[\"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n                }\r\n            }\r\n        }\r\n\r\n        // Depth Texture\r\n        if (this._webGLVersion > 1) {\r\n            this._caps.depthTextureExtension = true;\r\n        } else {\r\n            const depthTextureExtension = this._gl.getExtension(\"WEBGL_depth_texture\");\r\n\r\n            if (depthTextureExtension != null) {\r\n                this._caps.depthTextureExtension = true;\r\n                this._gl.UNSIGNED_INT_24_8 = depthTextureExtension.UNSIGNED_INT_24_8_WEBGL;\r\n            }\r\n        }\r\n\r\n        // Vertex array object\r\n        if (this.disableVertexArrayObjects) {\r\n            this._caps.vertexArrayObject = false;\r\n        } else if (this._webGLVersion > 1) {\r\n            this._caps.vertexArrayObject = true;\r\n        } else {\r\n            const vertexArrayObjectExtension = this._gl.getExtension(\"OES_vertex_array_object\");\r\n\r\n            if (vertexArrayObjectExtension != null) {\r\n                this._caps.vertexArrayObject = true;\r\n                this._gl.createVertexArray = vertexArrayObjectExtension.createVertexArrayOES.bind(vertexArrayObjectExtension);\r\n                this._gl.bindVertexArray = vertexArrayObjectExtension.bindVertexArrayOES.bind(vertexArrayObjectExtension);\r\n                this._gl.deleteVertexArray = vertexArrayObjectExtension.deleteVertexArrayOES.bind(vertexArrayObjectExtension);\r\n            }\r\n        }\r\n\r\n        // Instances count\r\n        if (this._webGLVersion > 1) {\r\n            this._caps.instancedArrays = true;\r\n        } else {\r\n            const instanceExtension = <ANGLE_instanced_arrays>this._gl.getExtension(\"ANGLE_instanced_arrays\");\r\n\r\n            if (instanceExtension != null) {\r\n                this._caps.instancedArrays = true;\r\n                this._gl.drawArraysInstanced = instanceExtension.drawArraysInstancedANGLE.bind(instanceExtension);\r\n                this._gl.drawElementsInstanced = instanceExtension.drawElementsInstancedANGLE.bind(instanceExtension);\r\n                this._gl.vertexAttribDivisor = instanceExtension.vertexAttribDivisorANGLE.bind(instanceExtension);\r\n            } else {\r\n                this._caps.instancedArrays = false;\r\n            }\r\n        }\r\n\r\n        if (this._gl.getShaderPrecisionFormat) {\r\n            const vertexhighp = this._gl.getShaderPrecisionFormat(this._gl.VERTEX_SHADER, this._gl.HIGH_FLOAT);\r\n            const fragmenthighp = this._gl.getShaderPrecisionFormat(this._gl.FRAGMENT_SHADER, this._gl.HIGH_FLOAT);\r\n\r\n            if (vertexhighp && fragmenthighp) {\r\n                this._caps.highPrecisionShaderSupported = vertexhighp.precision !== 0 && fragmenthighp.precision !== 0;\r\n            }\r\n        }\r\n\r\n        if (this._webGLVersion > 1) {\r\n            this._caps.blendMinMax = true;\r\n        } else {\r\n            const blendMinMaxExtension = this._gl.getExtension(\"EXT_blend_minmax\");\r\n            if (blendMinMaxExtension != null) {\r\n                this._caps.blendMinMax = true;\r\n                this._gl.MAX = blendMinMaxExtension.MAX_EXT as typeof WebGL2RenderingContext.MAX;\r\n                this._gl.MIN = blendMinMaxExtension.MIN_EXT as typeof WebGL2RenderingContext.MIN;\r\n            }\r\n        }\r\n\r\n        // sRGB buffers\r\n        // only run this if not already set to true (in the constructor, for example)\r\n        if (!this._caps.supportSRGBBuffers) {\r\n            if (this._webGLVersion > 1) {\r\n                this._caps.supportSRGBBuffers = true;\r\n                this._glSRGBExtensionValues = {\r\n                    SRGB: WebGL2RenderingContext.SRGB,\r\n                    SRGB8: WebGL2RenderingContext.SRGB8,\r\n                    SRGB8_ALPHA8: WebGL2RenderingContext.SRGB8_ALPHA8,\r\n                };\r\n            } else {\r\n                const sRGBExtension = this._gl.getExtension(\"EXT_sRGB\");\r\n\r\n                if (sRGBExtension != null) {\r\n                    this._caps.supportSRGBBuffers = true;\r\n                    this._glSRGBExtensionValues = {\r\n                        SRGB: sRGBExtension.SRGB_EXT as typeof WebGL2RenderingContext.SRGB,\r\n                        SRGB8: sRGBExtension.SRGB_ALPHA_EXT as typeof WebGL2RenderingContext.SRGB8 | EXT_sRGB[\"SRGB_ALPHA_EXT\"],\r\n                        SRGB8_ALPHA8: sRGBExtension.SRGB_ALPHA_EXT as typeof WebGL2RenderingContext.SRGB8_ALPHA8,\r\n                    };\r\n                }\r\n            }\r\n            // take into account the forced state that was provided in options\r\n            if (this._creationOptions) {\r\n                const forceSRGBBufferSupportState = (this._creationOptions as EngineOptions).forceSRGBBufferSupportState;\r\n                if (forceSRGBBufferSupportState !== undefined) {\r\n                    this._caps.supportSRGBBuffers = this._caps.supportSRGBBuffers && forceSRGBBufferSupportState;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Depth buffer\r\n        this._depthCullingState.depthTest = true;\r\n        this._depthCullingState.depthFunc = this._gl.LEQUAL;\r\n        this._depthCullingState.depthMask = true;\r\n\r\n        // Texture maps\r\n        this._maxSimultaneousTextures = this._caps.maxCombinedTexturesImageUnits;\r\n        for (let slot = 0; slot < this._maxSimultaneousTextures; slot++) {\r\n            this._nextFreeTextureSlots.push(slot);\r\n        }\r\n\r\n        if (this._glRenderer === \"Mali-G72\") {\r\n            // Overcome a bug when using a texture to store morph targets on Mali-G72\r\n            this._caps.disableMorphTargetTexture = true;\r\n        }\r\n    }\r\n\r\n    protected _initFeatures(): void {\r\n        this._features = {\r\n            forceBitmapOverHTMLImageElement: typeof HTMLImageElement === \"undefined\",\r\n            supportRenderAndCopyToLodForFloatTextures: this._webGLVersion !== 1,\r\n            supportDepthStencilTexture: this._webGLVersion !== 1,\r\n            supportShadowSamplers: this._webGLVersion !== 1,\r\n            uniformBufferHardCheckMatrix: false,\r\n            allowTexturePrefiltering: this._webGLVersion !== 1,\r\n            trackUbosInFrame: false,\r\n            checkUbosContentBeforeUpload: false,\r\n            supportCSM: this._webGLVersion !== 1,\r\n            basisNeedsPOT: this._webGLVersion === 1,\r\n            support3DTextures: this._webGLVersion !== 1,\r\n            needTypeSuffixInShaderConstants: this._webGLVersion !== 1,\r\n            supportMSAA: this._webGLVersion !== 1,\r\n            supportSSAO2: this._webGLVersion !== 1,\r\n            supportIBLShadows: this._webGLVersion !== 1,\r\n            supportExtendedTextureFormats: this._webGLVersion !== 1,\r\n            supportSwitchCaseInShader: this._webGLVersion !== 1,\r\n            supportSyncTextureRead: true,\r\n            needsInvertingBitmap: true,\r\n            useUBOBindingCache: true,\r\n            needShaderCodeInlining: false,\r\n            needToAlwaysBindUniformBuffers: false,\r\n            supportRenderPasses: false,\r\n            supportSpriteInstancing: true,\r\n            forceVertexBufferStrideAndOffsetMultiple4Bytes: false,\r\n            _checkNonFloatVertexBuffersDontRecreatePipelineContext: false,\r\n            _collectUbosUpdatedInFrame: false,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Gets version of the current webGL context\r\n     * Keep it for back compat - use version instead\r\n     */\r\n    public get webGLVersion(): number {\r\n        return this._webGLVersion;\r\n    }\r\n\r\n    /**\r\n     * Gets a string identifying the name of the class\r\n     * @returns \"Engine\" string\r\n     */\r\n    public override getClassName(): string {\r\n        return \"ThinEngine\";\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepareWorkingCanvas(): void {\r\n        if (this._workingCanvas) {\r\n            return;\r\n        }\r\n\r\n        this._workingCanvas = this.createCanvas(1, 1);\r\n        const context = this._workingCanvas.getContext(\"2d\");\r\n\r\n        if (context) {\r\n            this._workingContext = context;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets an object containing information about the current engine context\r\n     * @returns an object containing the vendor, the renderer and the version of the current engine context\r\n     */\r\n    public getInfo() {\r\n        return this.getGlInfo();\r\n    }\r\n\r\n    /**\r\n     * Gets an object containing information about the current webGL context\r\n     * @returns an object containing the vendor, the renderer and the version of the current webGL context\r\n     */\r\n    public getGlInfo() {\r\n        return {\r\n            vendor: this._glVendor,\r\n            renderer: this._glRenderer,\r\n            version: this._glVersion,\r\n        };\r\n    }\r\n\r\n    /**Gets driver info if available */\r\n    public extractDriverInfo() {\r\n        const glInfo = this.getGlInfo();\r\n        if (glInfo && glInfo.renderer) {\r\n            return glInfo.renderer;\r\n        }\r\n\r\n        return \"\";\r\n    }\r\n\r\n    /**\r\n     * Gets the current render width\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render width\r\n     */\r\n    public getRenderWidth(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.width;\r\n        }\r\n\r\n        return this._framebufferDimensionsObject ? this._framebufferDimensionsObject.framebufferWidth : this._gl.drawingBufferWidth;\r\n    }\r\n\r\n    /**\r\n     * Gets the current render height\r\n     * @param useScreen defines if screen size must be used (or the current render target if any)\r\n     * @returns a number defining the current render height\r\n     */\r\n    public getRenderHeight(useScreen = false): number {\r\n        if (!useScreen && this._currentRenderTarget) {\r\n            return this._currentRenderTarget.height;\r\n        }\r\n\r\n        return this._framebufferDimensionsObject ? this._framebufferDimensionsObject.framebufferHeight : this._gl.drawingBufferHeight;\r\n    }\r\n\r\n    /**\r\n     * Clear the current render buffer or the current render target (if any is set up)\r\n     * @param color defines the color to use\r\n     * @param backBuffer defines if the back buffer must be cleared\r\n     * @param depth defines if the depth buffer must be cleared\r\n     * @param stencil defines if the stencil buffer must be cleared\r\n     */\r\n    public clear(color: Nullable<IColor4Like>, backBuffer: boolean, depth: boolean, stencil: boolean = false): void {\r\n        const useStencilGlobalOnly = this.stencilStateComposer.useStencilGlobalOnly;\r\n        this.stencilStateComposer.useStencilGlobalOnly = true; // make sure the stencil mask is coming from the global stencil and not from a material (effect) which would currently be in effect\r\n\r\n        this.applyStates();\r\n\r\n        this.stencilStateComposer.useStencilGlobalOnly = useStencilGlobalOnly;\r\n\r\n        let mode = 0;\r\n        if (backBuffer && color) {\r\n            let setBackBufferColor = true;\r\n            if (this._currentRenderTarget) {\r\n                const textureFormat = this._currentRenderTarget.texture?.format;\r\n                if (\r\n                    textureFormat === Constants.TEXTUREFORMAT_RED_INTEGER ||\r\n                    textureFormat === Constants.TEXTUREFORMAT_RG_INTEGER ||\r\n                    textureFormat === Constants.TEXTUREFORMAT_RGB_INTEGER ||\r\n                    textureFormat === Constants.TEXTUREFORMAT_RGBA_INTEGER\r\n                ) {\r\n                    const textureType = this._currentRenderTarget.texture?.type;\r\n                    if (textureType === Constants.TEXTURETYPE_UNSIGNED_INTEGER || textureType === Constants.TEXTURETYPE_UNSIGNED_SHORT) {\r\n                        ThinEngine._TempClearColorUint32[0] = color.r * 255;\r\n                        ThinEngine._TempClearColorUint32[1] = color.g * 255;\r\n                        ThinEngine._TempClearColorUint32[2] = color.b * 255;\r\n                        ThinEngine._TempClearColorUint32[3] = color.a * 255;\r\n                        this._gl.clearBufferuiv(this._gl.COLOR, 0, ThinEngine._TempClearColorUint32);\r\n                        setBackBufferColor = false;\r\n                    } else {\r\n                        ThinEngine._TempClearColorInt32[0] = color.r * 255;\r\n                        ThinEngine._TempClearColorInt32[1] = color.g * 255;\r\n                        ThinEngine._TempClearColorInt32[2] = color.b * 255;\r\n                        ThinEngine._TempClearColorInt32[3] = color.a * 255;\r\n                        this._gl.clearBufferiv(this._gl.COLOR, 0, ThinEngine._TempClearColorInt32);\r\n                        setBackBufferColor = false;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (setBackBufferColor) {\r\n                this._gl.clearColor(color.r, color.g, color.b, color.a !== undefined ? color.a : 1.0);\r\n                mode |= this._gl.COLOR_BUFFER_BIT;\r\n            }\r\n        }\r\n\r\n        if (depth) {\r\n            if (this.useReverseDepthBuffer) {\r\n                this._depthCullingState.depthFunc = this._gl.GEQUAL;\r\n                this._gl.clearDepth(0.0);\r\n            } else {\r\n                this._gl.clearDepth(1.0);\r\n            }\r\n            mode |= this._gl.DEPTH_BUFFER_BIT;\r\n        }\r\n        if (stencil) {\r\n            this._gl.clearStencil(0);\r\n            mode |= this._gl.STENCIL_BUFFER_BIT;\r\n        }\r\n        this._gl.clear(mode);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _viewport(x: number, y: number, width: number, height: number): void {\r\n        if (x !== this._viewportCached.x || y !== this._viewportCached.y || width !== this._viewportCached.z || height !== this._viewportCached.w) {\r\n            this._viewportCached.x = x;\r\n            this._viewportCached.y = y;\r\n            this._viewportCached.z = width;\r\n            this._viewportCached.w = height;\r\n\r\n            this._gl.viewport(x, y, width, height);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * End the current frame\r\n     */\r\n    public override endFrame(): void {\r\n        super.endFrame();\r\n        // Force a flush in case we are using a bad OS.\r\n        if (this._badOS) {\r\n            this.flushFramebuffer();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the performance monitor attached to this engine\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimize_your_scene#engineinstrumentation\r\n     */\r\n    public get performanceMonitor(): PerformanceMonitor {\r\n        throw new Error(\"Not Supported by ThinEngine\");\r\n    }\r\n\r\n    /**\r\n     * Binds the frame buffer to the specified texture.\r\n     * @param rtWrapper The render target wrapper to render to\r\n     * @param faceIndex The face of the texture to render to in case of cube texture and if the render target wrapper is not a multi render target\r\n     * @param requiredWidth The width of the target to render to\r\n     * @param requiredHeight The height of the target to render to\r\n     * @param forceFullscreenViewport Forces the viewport to be the entire texture/screen if true\r\n     * @param lodLevel Defines the lod level to bind to the frame buffer\r\n     * @param layer Defines the 2d array index to bind to the frame buffer if the render target wrapper is not a multi render target\r\n     */\r\n    public bindFramebuffer(\r\n        rtWrapper: RenderTargetWrapper,\r\n        faceIndex: number = 0,\r\n        requiredWidth?: number,\r\n        requiredHeight?: number,\r\n        forceFullscreenViewport?: boolean,\r\n        lodLevel = 0,\r\n        layer = 0\r\n    ): void {\r\n        const webglRtWrapper = rtWrapper as WebGLRenderTargetWrapper;\r\n\r\n        if (this._currentRenderTarget) {\r\n            this._resolveAndGenerateMipMapsFramebuffer(this._currentRenderTarget);\r\n        }\r\n        this._currentRenderTarget = rtWrapper;\r\n        this._bindUnboundFramebuffer(webglRtWrapper._framebuffer);\r\n\r\n        const gl = this._gl;\r\n        if (!rtWrapper.isMulti) {\r\n            if (rtWrapper.is2DArray || rtWrapper.is3D) {\r\n                gl.framebufferTextureLayer(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, rtWrapper.texture!._hardwareTexture?.underlyingResource, lodLevel, layer);\r\n                webglRtWrapper._currentLOD = lodLevel;\r\n            } else if (rtWrapper.isCube) {\r\n                gl.framebufferTexture2D(\r\n                    gl.FRAMEBUFFER,\r\n                    gl.COLOR_ATTACHMENT0,\r\n                    gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex,\r\n                    rtWrapper.texture!._hardwareTexture?.underlyingResource,\r\n                    lodLevel\r\n                );\r\n            } else if (webglRtWrapper._currentLOD !== lodLevel) {\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, rtWrapper.texture!._hardwareTexture?.underlyingResource, lodLevel);\r\n                webglRtWrapper._currentLOD = lodLevel;\r\n            }\r\n        }\r\n\r\n        const depthStencilTexture = rtWrapper._depthStencilTexture;\r\n        if (depthStencilTexture) {\r\n            if (rtWrapper.is3D) {\r\n                if (\r\n                    rtWrapper.texture!.width !== depthStencilTexture.width ||\r\n                    rtWrapper.texture!.height !== depthStencilTexture.height ||\r\n                    rtWrapper.texture!.depth !== depthStencilTexture.depth\r\n                ) {\r\n                    Logger.Warn(\"Depth/Stencil attachment for 3D target must have same dimensions as color attachment\");\r\n                }\r\n            }\r\n            const attachment = rtWrapper._depthStencilTextureWithStencil ? gl.DEPTH_STENCIL_ATTACHMENT : gl.DEPTH_ATTACHMENT;\r\n            if (rtWrapper.is2DArray || rtWrapper.is3D) {\r\n                gl.framebufferTextureLayer(gl.FRAMEBUFFER, attachment, depthStencilTexture._hardwareTexture?.underlyingResource, lodLevel, layer);\r\n            } else if (rtWrapper.isCube) {\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex, depthStencilTexture._hardwareTexture?.underlyingResource, lodLevel);\r\n            } else {\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, gl.TEXTURE_2D, depthStencilTexture._hardwareTexture?.underlyingResource, lodLevel);\r\n            }\r\n        }\r\n\r\n        if (webglRtWrapper._MSAAFramebuffer) {\r\n            this._bindUnboundFramebuffer(webglRtWrapper._MSAAFramebuffer);\r\n        }\r\n\r\n        if (this._cachedViewport && !forceFullscreenViewport) {\r\n            this.setViewport(this._cachedViewport, requiredWidth, requiredHeight);\r\n        } else {\r\n            if (!requiredWidth) {\r\n                requiredWidth = rtWrapper.width;\r\n                if (lodLevel) {\r\n                    requiredWidth = requiredWidth / Math.pow(2, lodLevel);\r\n                }\r\n            }\r\n            if (!requiredHeight) {\r\n                requiredHeight = rtWrapper.height;\r\n                if (lodLevel) {\r\n                    requiredHeight = requiredHeight / Math.pow(2, lodLevel);\r\n                }\r\n            }\r\n\r\n            this._viewport(0, 0, requiredWidth, requiredHeight);\r\n        }\r\n\r\n        this.wipeCaches();\r\n    }\r\n\r\n    public override setStateCullFaceType(cullBackFaces?: boolean, force?: boolean): void {\r\n        const cullFace = (this.cullBackFaces ?? cullBackFaces ?? true) ? this._gl.BACK : this._gl.FRONT;\r\n        if (this._depthCullingState.cullFace !== cullFace || force) {\r\n            this._depthCullingState.cullFace = cullFace;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set various states to the webGL context\r\n     * @param culling defines culling state: true to enable culling, false to disable it\r\n     * @param zOffset defines the value to apply to zOffset (0 by default)\r\n     * @param force defines if states must be applied even if cache is up to date\r\n     * @param reverseSide defines if culling must be reversed (CCW if false, CW if true)\r\n     * @param cullBackFaces true to cull back faces, false to cull front faces (if culling is enabled)\r\n     * @param stencil stencil states to set\r\n     * @param zOffsetUnits defines the value to apply to zOffsetUnits (0 by default)\r\n     */\r\n    public setState(culling: boolean, zOffset: number = 0, force?: boolean, reverseSide = false, cullBackFaces?: boolean, stencil?: IStencilState, zOffsetUnits: number = 0): void {\r\n        // Culling\r\n        if (this._depthCullingState.cull !== culling || force) {\r\n            this._depthCullingState.cull = culling;\r\n        }\r\n\r\n        // Cull face\r\n        this.setStateCullFaceType(cullBackFaces, force);\r\n\r\n        // Z offset\r\n        this.setZOffset(zOffset);\r\n        this.setZOffsetUnits(zOffsetUnits);\r\n\r\n        // Front face\r\n        const frontFace = reverseSide ? this._gl.CW : this._gl.CCW;\r\n        if (this._depthCullingState.frontFace !== frontFace || force) {\r\n            this._depthCullingState.frontFace = frontFace;\r\n        }\r\n\r\n        this._stencilStateComposer.stencilMaterial = stencil;\r\n    }\r\n\r\n    private _resolveAndGenerateMipMapsFramebuffer(texture: RenderTargetWrapper, disableGenerateMipMaps = false): void {\r\n        const webglRtWrapper = texture as WebGLRenderTargetWrapper;\r\n\r\n        if (!webglRtWrapper.disableAutomaticMSAAResolve) {\r\n            if (texture.isMulti) {\r\n                this.resolveMultiFramebuffer(texture);\r\n            } else {\r\n                this.resolveFramebuffer(texture);\r\n            }\r\n        }\r\n\r\n        if (!disableGenerateMipMaps) {\r\n            if (texture.isMulti) {\r\n                this.generateMipMapsMultiFramebuffer(texture);\r\n            } else {\r\n                this.generateMipMapsFramebuffer(texture);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindUnboundFramebuffer(framebuffer: Nullable<WebGLFramebuffer>) {\r\n        if (this._currentFramebuffer !== framebuffer) {\r\n            this._gl.bindFramebuffer(this._gl.FRAMEBUFFER, framebuffer);\r\n            this._currentFramebuffer = framebuffer;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _currentFrameBufferIsDefaultFrameBuffer() {\r\n        return this._currentFramebuffer === null;\r\n    }\r\n\r\n    /**\r\n     * Generates the mipmaps for a texture\r\n     * @param texture texture to generate the mipmaps for\r\n     */\r\n    public generateMipmaps(texture: InternalTexture): void {\r\n        const target = this._getTextureTarget(texture);\r\n        this._bindTextureDirectly(target, texture, true);\r\n        this._gl.generateMipmap(target);\r\n        this._bindTextureDirectly(target, null);\r\n    }\r\n\r\n    /**\r\n     * Unbind the current render target texture from the webGL context\r\n     * @param texture defines the render target wrapper to unbind\r\n     * @param disableGenerateMipMaps defines a boolean indicating that mipmaps must not be generated\r\n     * @param onBeforeUnbind defines a function which will be called before the effective unbind\r\n     */\r\n    public unBindFramebuffer(texture: RenderTargetWrapper, disableGenerateMipMaps?: boolean, onBeforeUnbind?: () => void): void {\r\n        const webglRtWrapper = texture as WebGLRenderTargetWrapper;\r\n\r\n        this._currentRenderTarget = null;\r\n        this._resolveAndGenerateMipMapsFramebuffer(texture, disableGenerateMipMaps);\r\n\r\n        if (onBeforeUnbind) {\r\n            if (webglRtWrapper._MSAAFramebuffer) {\r\n                // Bind the correct framebuffer\r\n                this._bindUnboundFramebuffer(webglRtWrapper._framebuffer);\r\n            }\r\n            onBeforeUnbind();\r\n        }\r\n\r\n        this._bindUnboundFramebuffer(null);\r\n    }\r\n\r\n    /**\r\n     * Generates mipmaps for the texture of the (single) render target\r\n     * @param texture The render target containing the texture to generate the mipmaps for\r\n     */\r\n    public generateMipMapsFramebuffer(texture: RenderTargetWrapper): void {\r\n        if (!texture.isMulti && texture.texture?.generateMipMaps && !texture.isCube) {\r\n            this.generateMipmaps(texture.texture);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Resolves the MSAA texture of the (single) render target into its non-MSAA version.\r\n     * Note that if \"texture\" is not a MSAA render target, no resolve is performed.\r\n     * @param texture  The render target texture containing the MSAA textures to resolve\r\n     */\r\n    public resolveFramebuffer(texture: RenderTargetWrapper): void {\r\n        const rtWrapper = texture as WebGLRenderTargetWrapper;\r\n        const gl = this._gl;\r\n\r\n        if (!rtWrapper._MSAAFramebuffer || rtWrapper.isMulti) {\r\n            return;\r\n        }\r\n\r\n        let bufferBits = rtWrapper.resolveMSAAColors ? gl.COLOR_BUFFER_BIT : 0;\r\n        bufferBits |= rtWrapper._generateDepthBuffer && rtWrapper.resolveMSAADepth ? gl.DEPTH_BUFFER_BIT : 0;\r\n        bufferBits |= rtWrapper._generateStencilBuffer && rtWrapper.resolveMSAAStencil ? gl.STENCIL_BUFFER_BIT : 0;\r\n\r\n        gl.bindFramebuffer(gl.READ_FRAMEBUFFER, rtWrapper._MSAAFramebuffer);\r\n        gl.bindFramebuffer(gl.DRAW_FRAMEBUFFER, rtWrapper._framebuffer);\r\n        gl.blitFramebuffer(0, 0, texture.width, texture.height, 0, 0, texture.width, texture.height, bufferBits, gl.NEAREST);\r\n    }\r\n\r\n    /**\r\n     * Force a webGL flush (ie. a flush of all waiting webGL commands)\r\n     */\r\n    public flushFramebuffer(): void {\r\n        this._gl.flush();\r\n    }\r\n\r\n    /**\r\n     * Unbind the current render target and bind the default framebuffer\r\n     */\r\n    public restoreDefaultFramebuffer(): void {\r\n        if (this._currentRenderTarget) {\r\n            this.unBindFramebuffer(this._currentRenderTarget);\r\n        } else {\r\n            this._bindUnboundFramebuffer(null);\r\n        }\r\n        if (this._cachedViewport) {\r\n            this.setViewport(this._cachedViewport);\r\n        }\r\n\r\n        this.wipeCaches();\r\n    }\r\n\r\n    // VBOs\r\n\r\n    /** @internal */\r\n    protected _resetVertexBufferBinding(): void {\r\n        this.bindArrayBuffer(null);\r\n        this._cachedVertexBuffers = null;\r\n    }\r\n\r\n    /**\r\n     * Creates a vertex buffer\r\n     * @param data the data or size for the vertex buffer\r\n     * @param _updatable whether the buffer should be created as updatable\r\n     * @param _label defines the label of the buffer (for debug purpose)\r\n     * @returns the new WebGL static buffer\r\n     */\r\n    public createVertexBuffer(data: DataArray | number, _updatable?: boolean, _label?: string): DataBuffer {\r\n        return this._createVertexBuffer(data, this._gl.STATIC_DRAW);\r\n    }\r\n\r\n    private _createVertexBuffer(data: DataArray | number, usage: number): DataBuffer {\r\n        const vbo = this._gl.createBuffer();\r\n\r\n        if (!vbo) {\r\n            throw new Error(\"Unable to create vertex buffer\");\r\n        }\r\n\r\n        const dataBuffer = new WebGLDataBuffer(vbo);\r\n        this.bindArrayBuffer(dataBuffer);\r\n\r\n        if (typeof data !== \"number\") {\r\n            if (data instanceof Array) {\r\n                this._gl.bufferData(this._gl.ARRAY_BUFFER, new Float32Array(data), usage);\r\n                dataBuffer.capacity = data.length * 4;\r\n            } else {\r\n                this._gl.bufferData(this._gl.ARRAY_BUFFER, <ArrayBuffer>data, usage);\r\n                dataBuffer.capacity = data.byteLength;\r\n            }\r\n        } else {\r\n            this._gl.bufferData(this._gl.ARRAY_BUFFER, new Uint8Array(data), usage);\r\n            dataBuffer.capacity = data;\r\n        }\r\n\r\n        this._resetVertexBufferBinding();\r\n\r\n        dataBuffer.references = 1;\r\n        return dataBuffer;\r\n    }\r\n\r\n    /**\r\n     * Creates a dynamic vertex buffer\r\n     * @param data the data for the dynamic vertex buffer\r\n     * @param _label defines the label of the buffer (for debug purpose)\r\n     * @returns the new WebGL dynamic buffer\r\n     */\r\n    public createDynamicVertexBuffer(data: DataArray | number, _label?: string): DataBuffer {\r\n        return this._createVertexBuffer(data, this._gl.DYNAMIC_DRAW);\r\n    }\r\n\r\n    protected _resetIndexBufferBinding(): void {\r\n        this.bindIndexBuffer(null);\r\n        this._cachedIndexBuffer = null;\r\n    }\r\n\r\n    /**\r\n     * Creates a new index buffer\r\n     * @param indices defines the content of the index buffer\r\n     * @param updatable defines if the index buffer must be updatable\r\n     * @param _label defines the label of the buffer (for debug purpose)\r\n     * @returns a new webGL buffer\r\n     */\r\n    public createIndexBuffer(indices: IndicesArray, updatable?: boolean, _label?: string): DataBuffer {\r\n        const vbo = this._gl.createBuffer();\r\n        const dataBuffer = new WebGLDataBuffer(vbo);\r\n\r\n        if (!vbo) {\r\n            throw new Error(\"Unable to create index buffer\");\r\n        }\r\n\r\n        this.bindIndexBuffer(dataBuffer);\r\n\r\n        const data = this._normalizeIndexData(indices);\r\n        this._gl.bufferData(this._gl.ELEMENT_ARRAY_BUFFER, data, updatable ? this._gl.DYNAMIC_DRAW : this._gl.STATIC_DRAW);\r\n        this._resetIndexBufferBinding();\r\n        dataBuffer.references = 1;\r\n        dataBuffer.is32Bits = data.BYTES_PER_ELEMENT === 4;\r\n        return dataBuffer;\r\n    }\r\n\r\n    protected _normalizeIndexData(indices: IndicesArray): Uint16Array | Uint32Array {\r\n        const bytesPerElement = (indices as Exclude<IndicesArray, number[]>).BYTES_PER_ELEMENT;\r\n        if (bytesPerElement === 2) {\r\n            return indices as Uint16Array;\r\n        }\r\n\r\n        // Check 32 bit support\r\n        if (this._caps.uintIndices) {\r\n            if (indices instanceof Uint32Array) {\r\n                return indices;\r\n            } else {\r\n                // number[] or Int32Array, check if 32 bit is necessary\r\n                for (let index = 0; index < indices.length; index++) {\r\n                    if (indices[index] >= 65535) {\r\n                        return new Uint32Array(indices);\r\n                    }\r\n                }\r\n\r\n                return new Uint16Array(indices);\r\n            }\r\n        }\r\n\r\n        // No 32 bit support, force conversion to 16 bit (values greater 16 bit are lost)\r\n        return new Uint16Array(indices);\r\n    }\r\n\r\n    /**\r\n     * Bind a webGL buffer to the webGL context\r\n     * @param buffer defines the buffer to bind\r\n     */\r\n    public bindArrayBuffer(buffer: Nullable<DataBuffer>): void {\r\n        if (!this._vaoRecordInProgress) {\r\n            this._unbindVertexArrayObject();\r\n        }\r\n        this._bindBuffer(buffer, this._gl.ARRAY_BUFFER);\r\n    }\r\n\r\n    /**\r\n     * Bind a specific block at a given index in a specific shader program\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param blockName defines the block name\r\n     * @param index defines the index where to bind the block\r\n     */\r\n    public bindUniformBlock(pipelineContext: IPipelineContext, blockName: string, index: number): void {\r\n        const program = (pipelineContext as WebGLPipelineContext).program!;\r\n\r\n        const uniformLocation = this._gl.getUniformBlockIndex(program, blockName);\r\n\r\n        this._gl.uniformBlockBinding(program, uniformLocation, index);\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected bindIndexBuffer(buffer: Nullable<DataBuffer>): void {\r\n        if (!this._vaoRecordInProgress) {\r\n            this._unbindVertexArrayObject();\r\n        }\r\n        this._bindBuffer(buffer, this._gl.ELEMENT_ARRAY_BUFFER);\r\n    }\r\n\r\n    private _bindBuffer(buffer: Nullable<DataBuffer>, target: number): void {\r\n        if (this._vaoRecordInProgress || this._currentBoundBuffer[target] !== buffer) {\r\n            this._gl.bindBuffer(target, buffer ? buffer.underlyingResource : null);\r\n            this._currentBoundBuffer[target] = buffer;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * update the bound buffer with the given data\r\n     * @param data defines the data to update\r\n     */\r\n    public updateArrayBuffer(data: Float32Array): void {\r\n        this._gl.bufferSubData(this._gl.ARRAY_BUFFER, 0, data);\r\n    }\r\n\r\n    private _vertexAttribPointer(buffer: DataBuffer, indx: number, size: number, type: number, normalized: boolean, stride: number, offset: number): void {\r\n        const pointer = this._currentBufferPointers[indx];\r\n        if (!pointer) {\r\n            return;\r\n        }\r\n\r\n        let changed = false;\r\n        if (!pointer.active) {\r\n            changed = true;\r\n            pointer.active = true;\r\n            pointer.index = indx;\r\n            pointer.size = size;\r\n            pointer.type = type;\r\n            pointer.normalized = normalized;\r\n            pointer.stride = stride;\r\n            pointer.offset = offset;\r\n            pointer.buffer = buffer;\r\n        } else {\r\n            if (pointer.buffer !== buffer) {\r\n                pointer.buffer = buffer;\r\n                changed = true;\r\n            }\r\n            if (pointer.size !== size) {\r\n                pointer.size = size;\r\n                changed = true;\r\n            }\r\n            if (pointer.type !== type) {\r\n                pointer.type = type;\r\n                changed = true;\r\n            }\r\n            if (pointer.normalized !== normalized) {\r\n                pointer.normalized = normalized;\r\n                changed = true;\r\n            }\r\n            if (pointer.stride !== stride) {\r\n                pointer.stride = stride;\r\n                changed = true;\r\n            }\r\n            if (pointer.offset !== offset) {\r\n                pointer.offset = offset;\r\n                changed = true;\r\n            }\r\n        }\r\n\r\n        if (changed || this._vaoRecordInProgress) {\r\n            this.bindArrayBuffer(buffer);\r\n            if (type === this._gl.UNSIGNED_INT || type === this._gl.INT) {\r\n                this._gl.vertexAttribIPointer(indx, size, type, stride, offset);\r\n            } else {\r\n                this._gl.vertexAttribPointer(indx, size, type, normalized, stride, offset);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindIndexBufferWithCache(indexBuffer: Nullable<DataBuffer>): void {\r\n        if (indexBuffer == null) {\r\n            return;\r\n        }\r\n        if (this._cachedIndexBuffer !== indexBuffer) {\r\n            this._cachedIndexBuffer = indexBuffer;\r\n            this.bindIndexBuffer(indexBuffer);\r\n            this._uintIndicesCurrentlySet = indexBuffer.is32Bits;\r\n        }\r\n    }\r\n\r\n    private _bindVertexBuffersAttributes(\r\n        vertexBuffers: { [key: string]: Nullable<VertexBuffer> },\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): void {\r\n        const attributes = effect.getAttributesNames();\r\n\r\n        if (!this._vaoRecordInProgress) {\r\n            this._unbindVertexArrayObject();\r\n        }\r\n\r\n        this.unbindAllAttributes();\r\n\r\n        for (let index = 0; index < attributes.length; index++) {\r\n            const order = effect.getAttributeLocation(index);\r\n\r\n            if (order >= 0) {\r\n                const ai = attributes[index];\r\n                let vertexBuffer: Nullable<VertexBuffer> = null;\r\n\r\n                if (overrideVertexBuffers) {\r\n                    vertexBuffer = overrideVertexBuffers[ai];\r\n                }\r\n\r\n                if (!vertexBuffer) {\r\n                    vertexBuffer = vertexBuffers[ai];\r\n                }\r\n\r\n                if (!vertexBuffer) {\r\n                    continue;\r\n                }\r\n\r\n                this._gl.enableVertexAttribArray(order);\r\n                if (!this._vaoRecordInProgress) {\r\n                    this._vertexAttribArraysEnabled[order] = true;\r\n                }\r\n\r\n                const buffer = vertexBuffer.getBuffer();\r\n                if (buffer) {\r\n                    this._vertexAttribPointer(buffer, order, vertexBuffer.getSize(), vertexBuffer.type, vertexBuffer.normalized, vertexBuffer.byteStride, vertexBuffer.byteOffset);\r\n\r\n                    if (vertexBuffer.getIsInstanced()) {\r\n                        this._gl.vertexAttribDivisor(order, vertexBuffer.getInstanceDivisor());\r\n                        if (!this._vaoRecordInProgress) {\r\n                            this._currentInstanceLocations.push(order);\r\n                            this._currentInstanceBuffers.push(buffer);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Records a vertex array object\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#vertex-array-objects\r\n     * @param vertexBuffers defines the list of vertex buffers to store\r\n     * @param indexBuffer defines the index buffer to store\r\n     * @param effect defines the effect to store\r\n     * @param overrideVertexBuffers defines optional list of avertex buffers that overrides the entries in vertexBuffers\r\n     * @returns the new vertex array object\r\n     */\r\n    public recordVertexArrayObject(\r\n        vertexBuffers: { [key: string]: VertexBuffer },\r\n        indexBuffer: Nullable<DataBuffer>,\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): WebGLVertexArrayObject {\r\n        const vao = this._gl.createVertexArray();\r\n\r\n        if (!vao) {\r\n            throw new Error(\"Unable to create VAO\");\r\n        }\r\n\r\n        this._vaoRecordInProgress = true;\r\n\r\n        this._gl.bindVertexArray(vao);\r\n\r\n        this._mustWipeVertexAttributes = true;\r\n        this._bindVertexBuffersAttributes(vertexBuffers, effect, overrideVertexBuffers);\r\n\r\n        this.bindIndexBuffer(indexBuffer);\r\n\r\n        this._vaoRecordInProgress = false;\r\n        this._gl.bindVertexArray(null);\r\n\r\n        return vao;\r\n    }\r\n\r\n    /**\r\n     * Bind a specific vertex array object\r\n     * @see https://doc.babylonjs.com/setup/support/webGL2#vertex-array-objects\r\n     * @param vertexArrayObject defines the vertex array object to bind\r\n     * @param indexBuffer defines the index buffer to bind\r\n     */\r\n    public bindVertexArrayObject(vertexArrayObject: WebGLVertexArrayObject, indexBuffer: Nullable<DataBuffer>): void {\r\n        if (this._cachedVertexArrayObject !== vertexArrayObject) {\r\n            this._cachedVertexArrayObject = vertexArrayObject;\r\n\r\n            this._gl.bindVertexArray(vertexArrayObject);\r\n            this._cachedVertexBuffers = null;\r\n            this._cachedIndexBuffer = null;\r\n\r\n            this._uintIndicesCurrentlySet = indexBuffer != null && indexBuffer.is32Bits;\r\n            this._mustWipeVertexAttributes = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Bind webGl buffers directly to the webGL context\r\n     * @param vertexBuffer defines the vertex buffer to bind\r\n     * @param indexBuffer defines the index buffer to bind\r\n     * @param vertexDeclaration defines the vertex declaration to use with the vertex buffer\r\n     * @param vertexStrideSize defines the vertex stride of the vertex buffer\r\n     * @param effect defines the effect associated with the vertex buffer\r\n     */\r\n    public bindBuffersDirectly(vertexBuffer: DataBuffer, indexBuffer: DataBuffer, vertexDeclaration: number[], vertexStrideSize: number, effect: Effect): void {\r\n        if (this._cachedVertexBuffers !== vertexBuffer || this._cachedEffectForVertexBuffers !== effect) {\r\n            this._cachedVertexBuffers = vertexBuffer;\r\n            this._cachedEffectForVertexBuffers = effect;\r\n\r\n            const attributesCount = effect.getAttributesCount();\r\n\r\n            this._unbindVertexArrayObject();\r\n            this.unbindAllAttributes();\r\n\r\n            let offset = 0;\r\n            for (let index = 0; index < attributesCount; index++) {\r\n                if (index < vertexDeclaration.length) {\r\n                    const order = effect.getAttributeLocation(index);\r\n\r\n                    if (order >= 0) {\r\n                        this._gl.enableVertexAttribArray(order);\r\n                        this._vertexAttribArraysEnabled[order] = true;\r\n                        this._vertexAttribPointer(vertexBuffer, order, vertexDeclaration[index], this._gl.FLOAT, false, vertexStrideSize, offset);\r\n                    }\r\n\r\n                    offset += vertexDeclaration[index] * 4;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._bindIndexBufferWithCache(indexBuffer);\r\n    }\r\n\r\n    private _unbindVertexArrayObject(): void {\r\n        if (!this._cachedVertexArrayObject) {\r\n            return;\r\n        }\r\n\r\n        this._cachedVertexArrayObject = null;\r\n        this._gl.bindVertexArray(null);\r\n    }\r\n\r\n    /**\r\n     * Bind a list of vertex buffers to the webGL context\r\n     * @param vertexBuffers defines the list of vertex buffers to bind\r\n     * @param indexBuffer defines the index buffer to bind\r\n     * @param effect defines the effect associated with the vertex buffers\r\n     * @param overrideVertexBuffers defines optional list of avertex buffers that overrides the entries in vertexBuffers\r\n     */\r\n    public bindBuffers(\r\n        vertexBuffers: { [key: string]: Nullable<VertexBuffer> },\r\n        indexBuffer: Nullable<DataBuffer>,\r\n        effect: Effect,\r\n        overrideVertexBuffers?: { [kind: string]: Nullable<VertexBuffer> }\r\n    ): void {\r\n        if (this._cachedVertexBuffers !== vertexBuffers || this._cachedEffectForVertexBuffers !== effect) {\r\n            this._cachedVertexBuffers = vertexBuffers;\r\n            this._cachedEffectForVertexBuffers = effect;\r\n\r\n            this._bindVertexBuffersAttributes(vertexBuffers, effect, overrideVertexBuffers);\r\n        }\r\n\r\n        this._bindIndexBufferWithCache(indexBuffer);\r\n    }\r\n\r\n    /**\r\n     * Unbind all instance attributes\r\n     */\r\n    public unbindInstanceAttributes() {\r\n        let boundBuffer;\r\n        for (let i = 0, ul = this._currentInstanceLocations.length; i < ul; i++) {\r\n            const instancesBuffer = this._currentInstanceBuffers[i];\r\n            if (boundBuffer != instancesBuffer && instancesBuffer.references) {\r\n                boundBuffer = instancesBuffer;\r\n                this.bindArrayBuffer(instancesBuffer);\r\n            }\r\n            const offsetLocation = this._currentInstanceLocations[i];\r\n            this._gl.vertexAttribDivisor(offsetLocation, 0);\r\n        }\r\n        this._currentInstanceBuffers.length = 0;\r\n        this._currentInstanceLocations.length = 0;\r\n    }\r\n\r\n    /**\r\n     * Release and free the memory of a vertex array object\r\n     * @param vao defines the vertex array object to delete\r\n     */\r\n    public releaseVertexArrayObject(vao: WebGLVertexArrayObject) {\r\n        this._gl.deleteVertexArray(vao);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseBuffer(buffer: DataBuffer): boolean {\r\n        buffer.references--;\r\n\r\n        if (buffer.references === 0) {\r\n            this._deleteBuffer(buffer);\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    protected _deleteBuffer(buffer: DataBuffer): void {\r\n        this._gl.deleteBuffer(buffer.underlyingResource);\r\n    }\r\n\r\n    /**\r\n     * Update the content of a webGL buffer used with instantiation and bind it to the webGL context\r\n     * @param instancesBuffer defines the webGL buffer to update and bind\r\n     * @param data defines the data to store in the buffer\r\n     * @param offsetLocations defines the offsets or attributes information used to determine where data must be stored in the buffer\r\n     */\r\n    public updateAndBindInstancesBuffer(instancesBuffer: DataBuffer, data: Float32Array, offsetLocations: number[] | InstancingAttributeInfo[]): void {\r\n        this.bindArrayBuffer(instancesBuffer);\r\n        if (data) {\r\n            this._gl.bufferSubData(this._gl.ARRAY_BUFFER, 0, data);\r\n        }\r\n\r\n        if ((<any>offsetLocations[0]).index !== undefined) {\r\n            this.bindInstancesBuffer(instancesBuffer, offsetLocations as any, true);\r\n        } else {\r\n            for (let index = 0; index < 4; index++) {\r\n                const offsetLocation = <number>offsetLocations[index];\r\n\r\n                if (!this._vertexAttribArraysEnabled[offsetLocation]) {\r\n                    this._gl.enableVertexAttribArray(offsetLocation);\r\n                    this._vertexAttribArraysEnabled[offsetLocation] = true;\r\n                }\r\n\r\n                this._vertexAttribPointer(instancesBuffer, offsetLocation, 4, this._gl.FLOAT, false, 64, index * 16);\r\n                this._gl.vertexAttribDivisor(offsetLocation, 1);\r\n                this._currentInstanceLocations.push(offsetLocation);\r\n                this._currentInstanceBuffers.push(instancesBuffer);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Bind the content of a webGL buffer used with instantiation\r\n     * @param instancesBuffer defines the webGL buffer to bind\r\n     * @param attributesInfo defines the offsets or attributes information used to determine where data must be stored in the buffer\r\n     * @param computeStride defines Whether to compute the strides from the info or use the default 0\r\n     */\r\n    public bindInstancesBuffer(instancesBuffer: DataBuffer, attributesInfo: InstancingAttributeInfo[], computeStride = true): void {\r\n        this.bindArrayBuffer(instancesBuffer);\r\n\r\n        let stride = 0;\r\n        if (computeStride) {\r\n            for (let i = 0; i < attributesInfo.length; i++) {\r\n                const ai = attributesInfo[i];\r\n                stride += ai.attributeSize * 4;\r\n            }\r\n        }\r\n\r\n        for (let i = 0; i < attributesInfo.length; i++) {\r\n            const ai = attributesInfo[i];\r\n            if (ai.index === undefined) {\r\n                ai.index = this._currentEffect!.getAttributeLocationByName(ai.attributeName);\r\n            }\r\n\r\n            if (ai.index < 0) {\r\n                continue;\r\n            }\r\n\r\n            if (!this._vertexAttribArraysEnabled[ai.index]) {\r\n                this._gl.enableVertexAttribArray(ai.index);\r\n                this._vertexAttribArraysEnabled[ai.index] = true;\r\n            }\r\n\r\n            this._vertexAttribPointer(instancesBuffer, ai.index, ai.attributeSize, ai.attributeType || this._gl.FLOAT, ai.normalized || false, stride, ai.offset);\r\n            this._gl.vertexAttribDivisor(ai.index, ai.divisor === undefined ? 1 : ai.divisor);\r\n            this._currentInstanceLocations.push(ai.index);\r\n            this._currentInstanceBuffers.push(instancesBuffer);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disable the instance attribute corresponding to the name in parameter\r\n     * @param name defines the name of the attribute to disable\r\n     */\r\n    public disableInstanceAttributeByName(name: string) {\r\n        if (!this._currentEffect) {\r\n            return;\r\n        }\r\n\r\n        const attributeLocation = this._currentEffect.getAttributeLocationByName(name);\r\n        this.disableInstanceAttribute(attributeLocation);\r\n    }\r\n\r\n    /**\r\n     * Disable the instance attribute corresponding to the location in parameter\r\n     * @param attributeLocation defines the attribute location of the attribute to disable\r\n     */\r\n    public disableInstanceAttribute(attributeLocation: number) {\r\n        let shouldClean = false;\r\n        let index: number;\r\n        while ((index = this._currentInstanceLocations.indexOf(attributeLocation)) !== -1) {\r\n            this._currentInstanceLocations.splice(index, 1);\r\n            this._currentInstanceBuffers.splice(index, 1);\r\n\r\n            shouldClean = true;\r\n            index = this._currentInstanceLocations.indexOf(attributeLocation);\r\n        }\r\n\r\n        if (shouldClean) {\r\n            this._gl.vertexAttribDivisor(attributeLocation, 0);\r\n            this.disableAttributeByIndex(attributeLocation);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disable the attribute corresponding to the location in parameter\r\n     * @param attributeLocation defines the attribute location of the attribute to disable\r\n     */\r\n    public disableAttributeByIndex(attributeLocation: number) {\r\n        this._gl.disableVertexAttribArray(attributeLocation);\r\n        this._vertexAttribArraysEnabled[attributeLocation] = false;\r\n        this._currentBufferPointers[attributeLocation].active = false;\r\n    }\r\n\r\n    /**\r\n     * Send a draw order\r\n     * @param useTriangles defines if triangles must be used to draw (else wireframe will be used)\r\n     * @param indexStart defines the starting index\r\n     * @param indexCount defines the number of index to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public draw(useTriangles: boolean, indexStart: number, indexCount: number, instancesCount?: number): void {\r\n        this.drawElementsType(useTriangles ? Constants.MATERIAL_TriangleFillMode : Constants.MATERIAL_WireFrameFillMode, indexStart, indexCount, instancesCount);\r\n    }\r\n\r\n    /**\r\n     * Draw a list of points\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawPointClouds(verticesStart: number, verticesCount: number, instancesCount?: number): void {\r\n        this.drawArraysType(Constants.MATERIAL_PointFillMode, verticesStart, verticesCount, instancesCount);\r\n    }\r\n\r\n    /**\r\n     * Draw a list of unindexed primitives\r\n     * @param useTriangles defines if triangles must be used to draw (else wireframe will be used)\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawUnIndexed(useTriangles: boolean, verticesStart: number, verticesCount: number, instancesCount?: number): void {\r\n        this.drawArraysType(useTriangles ? Constants.MATERIAL_TriangleFillMode : Constants.MATERIAL_WireFrameFillMode, verticesStart, verticesCount, instancesCount);\r\n    }\r\n\r\n    /**\r\n     * Draw a list of indexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param indexStart defines the starting index\r\n     * @param indexCount defines the number of index to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawElementsType(fillMode: number, indexStart: number, indexCount: number, instancesCount?: number): void {\r\n        // Apply states\r\n        this.applyStates();\r\n\r\n        this._reportDrawCall();\r\n\r\n        // Render\r\n\r\n        const drawMode = this._drawMode(fillMode);\r\n        const indexFormat = this._uintIndicesCurrentlySet ? this._gl.UNSIGNED_INT : this._gl.UNSIGNED_SHORT;\r\n        const mult = this._uintIndicesCurrentlySet ? 4 : 2;\r\n        if (instancesCount) {\r\n            this._gl.drawElementsInstanced(drawMode, indexCount, indexFormat, indexStart * mult, instancesCount);\r\n        } else {\r\n            this._gl.drawElements(drawMode, indexCount, indexFormat, indexStart * mult);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Draw a list of unindexed primitives\r\n     * @param fillMode defines the primitive to use\r\n     * @param verticesStart defines the index of first vertex to draw\r\n     * @param verticesCount defines the count of vertices to draw\r\n     * @param instancesCount defines the number of instances to draw (if instantiation is enabled)\r\n     */\r\n    public drawArraysType(fillMode: number, verticesStart: number, verticesCount: number, instancesCount?: number): void {\r\n        // Apply states\r\n        this.applyStates();\r\n\r\n        this._reportDrawCall();\r\n\r\n        const drawMode = this._drawMode(fillMode);\r\n        if (instancesCount) {\r\n            this._gl.drawArraysInstanced(drawMode, verticesStart, verticesCount, instancesCount);\r\n        } else {\r\n            this._gl.drawArrays(drawMode, verticesStart, verticesCount);\r\n        }\r\n    }\r\n\r\n    private _drawMode(fillMode: number): number {\r\n        switch (fillMode) {\r\n            // Triangle views\r\n            case Constants.MATERIAL_TriangleFillMode:\r\n                return this._gl.TRIANGLES;\r\n            case Constants.MATERIAL_PointFillMode:\r\n                return this._gl.POINTS;\r\n            case Constants.MATERIAL_WireFrameFillMode:\r\n                return this._gl.LINES;\r\n            // Draw modes\r\n            case Constants.MATERIAL_PointListDrawMode:\r\n                return this._gl.POINTS;\r\n            case Constants.MATERIAL_LineListDrawMode:\r\n                return this._gl.LINES;\r\n            case Constants.MATERIAL_LineLoopDrawMode:\r\n                return this._gl.LINE_LOOP;\r\n            case Constants.MATERIAL_LineStripDrawMode:\r\n                return this._gl.LINE_STRIP;\r\n            case Constants.MATERIAL_TriangleStripDrawMode:\r\n                return this._gl.TRIANGLE_STRIP;\r\n            case Constants.MATERIAL_TriangleFanDrawMode:\r\n                return this._gl.TRIANGLE_FAN;\r\n            default:\r\n                return this._gl.TRIANGLES;\r\n        }\r\n    }\r\n\r\n    // Shaders\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseEffect(effect: Effect): void {\r\n        if (this._compiledEffects[effect._key]) {\r\n            delete this._compiledEffects[effect._key];\r\n        }\r\n        const pipelineContext = effect.getPipelineContext();\r\n        if (pipelineContext) {\r\n            this._deletePipelineContext(pipelineContext);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _deletePipelineContext(pipelineContext: IPipelineContext): void {\r\n        const webGLPipelineContext = pipelineContext as WebGLPipelineContext;\r\n        if (webGLPipelineContext && webGLPipelineContext.program) {\r\n            webGLPipelineContext.program.__SPECTOR_rebuildProgram = null;\r\n            resetCachedPipeline(webGLPipelineContext);\r\n            if (this._gl) {\r\n                if (this._currentProgram === webGLPipelineContext.program) {\r\n                    this._setProgram(null);\r\n                }\r\n                this._gl.deleteProgram(webGLPipelineContext.program);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override _getGlobalDefines(defines?: { [key: string]: string }): string | undefined {\r\n        return _GetGlobalDefines(defines, this.isNDCHalfZRange, this.useReverseDepthBuffer, this.useExactSrgbConversions);\r\n    }\r\n\r\n    /**\r\n     * Create a new effect (used to store vertex/fragment shaders)\r\n     * @param baseName defines the base name of the effect (The name of file without .fragment.fx or .vertex.fx)\r\n     * @param attributesNamesOrOptions defines either a list of attribute names or an IEffectCreationOptions object\r\n     * @param uniformsNamesOrEngine defines either a list of uniform names or the engine to use\r\n     * @param samplers defines an array of string used to represent textures\r\n     * @param defines defines the string containing the defines to use to compile the shaders\r\n     * @param fallbacks defines the list of potential fallbacks to use if shader compilation fails\r\n     * @param onCompiled defines a function to call when the effect creation is successful\r\n     * @param onError defines a function to call when the effect creation has failed\r\n     * @param indexParameters defines an object containing the index values to use to compile shaders (like the maximum number of simultaneous lights)\r\n     * @param shaderLanguage the language the shader is written in (default: GLSL)\r\n     * @param extraInitializationsAsync additional async code to run before preparing the effect\r\n     * @returns the new Effect\r\n     */\r\n    public createEffect(\r\n        baseName: string | (IShaderPath & { vertexToken?: string; fragmentToken?: string }),\r\n        attributesNamesOrOptions: string[] | IEffectCreationOptions,\r\n        uniformsNamesOrEngine: string[] | ThinEngine,\r\n        samplers?: string[],\r\n        defines?: string,\r\n        fallbacks?: IEffectFallbacks,\r\n        onCompiled?: Nullable<(effect: Effect) => void>,\r\n        onError?: Nullable<(effect: Effect, errors: string) => void>,\r\n        indexParameters?: any,\r\n        shaderLanguage = ShaderLanguage.GLSL,\r\n        extraInitializationsAsync?: () => Promise<void>\r\n    ): Effect {\r\n        const vertex = typeof baseName === \"string\" ? baseName : baseName.vertexToken || baseName.vertexSource || baseName.vertexElement || baseName.vertex;\r\n        const fragment = typeof baseName === \"string\" ? baseName : baseName.fragmentToken || baseName.fragmentSource || baseName.fragmentElement || baseName.fragment;\r\n        const globalDefines = this._getGlobalDefines()!;\r\n\r\n        const isOptions = (attributesNamesOrOptions as IEffectCreationOptions).attributes !== undefined;\r\n\r\n        let fullDefines = defines ?? (<IEffectCreationOptions>attributesNamesOrOptions).defines ?? \"\";\r\n\r\n        if (globalDefines) {\r\n            fullDefines += globalDefines;\r\n        }\r\n\r\n        const name = vertex + \"+\" + fragment + \"@\" + fullDefines;\r\n        if (this._compiledEffects[name]) {\r\n            const compiledEffect = this._compiledEffects[name];\r\n            if (onCompiled && compiledEffect.isReady()) {\r\n                onCompiled(compiledEffect);\r\n            }\r\n            compiledEffect._refCount++;\r\n            return compiledEffect;\r\n        }\r\n        if (this._gl) {\r\n            getStateObject(this._gl);\r\n        }\r\n        const effect = new Effect(\r\n            baseName,\r\n            attributesNamesOrOptions,\r\n            isOptions ? this : uniformsNamesOrEngine,\r\n            samplers,\r\n            this,\r\n            defines,\r\n            fallbacks,\r\n            onCompiled,\r\n            onError,\r\n            indexParameters,\r\n            name,\r\n            (<IEffectCreationOptions>attributesNamesOrOptions).shaderLanguage ?? shaderLanguage,\r\n            (<IEffectCreationOptions>attributesNamesOrOptions).extraInitializationsAsync ?? extraInitializationsAsync\r\n        );\r\n        this._compiledEffects[name] = effect;\r\n\r\n        return effect;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    protected static _ConcatenateShader = _ConcatenateShader;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getShaderSource(shader: WebGLShader): Nullable<string> {\r\n        return this._gl.getShaderSource(shader);\r\n    }\r\n\r\n    /**\r\n     * Directly creates a webGL program\r\n     * @param pipelineContext  defines the pipeline context to attach to\r\n     * @param vertexCode defines the vertex shader code to use\r\n     * @param fragmentCode defines the fragment shader code to use\r\n     * @param context defines the webGL context to use (if not set, the current one will be used)\r\n     * @param transformFeedbackVaryings defines the list of transform feedback varyings to use\r\n     * @returns the new webGL program\r\n     */\r\n    public createRawShaderProgram(\r\n        pipelineContext: IPipelineContext,\r\n        vertexCode: string,\r\n        fragmentCode: string,\r\n        context?: WebGLRenderingContext,\r\n        transformFeedbackVaryings: Nullable<string[]> = null\r\n    ): WebGLProgram {\r\n        const stateObject = getStateObject(this._gl);\r\n        stateObject._contextWasLost = this._contextWasLost;\r\n        stateObject.validateShaderPrograms = this.validateShaderPrograms;\r\n        return createRawShaderProgram(pipelineContext, vertexCode, fragmentCode, context || this._gl, transformFeedbackVaryings);\r\n    }\r\n\r\n    /**\r\n     * Creates a webGL program\r\n     * @param pipelineContext  defines the pipeline context to attach to\r\n     * @param vertexCode  defines the vertex shader code to use\r\n     * @param fragmentCode defines the fragment shader code to use\r\n     * @param defines defines the string containing the defines to use to compile the shaders\r\n     * @param context defines the webGL context to use (if not set, the current one will be used)\r\n     * @param transformFeedbackVaryings defines the list of transform feedback varyings to use\r\n     * @returns the new webGL program\r\n     */\r\n    public createShaderProgram(\r\n        pipelineContext: IPipelineContext,\r\n        vertexCode: string,\r\n        fragmentCode: string,\r\n        defines: Nullable<string>,\r\n        context?: WebGLRenderingContext,\r\n        transformFeedbackVaryings: Nullable<string[]> = null\r\n    ): WebGLProgram {\r\n        const stateObject = getStateObject(this._gl);\r\n        // assure the state object is correct\r\n        stateObject._contextWasLost = this._contextWasLost;\r\n        stateObject.validateShaderPrograms = this.validateShaderPrograms;\r\n        return createShaderProgram(pipelineContext, vertexCode, fragmentCode, defines, context || this._gl, transformFeedbackVaryings);\r\n    }\r\n\r\n    /**\r\n     * Inline functions in shader code that are marked to be inlined\r\n     * @param code code to inline\r\n     * @returns inlined code\r\n     */\r\n    public inlineShaderCode(code: string): string {\r\n        // no inlining needed in the WebGL engine\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Creates a new pipeline context\r\n     * @param shaderProcessingContext defines the shader processing context used during the processing if available\r\n     * @returns the new pipeline\r\n     */\r\n    public createPipelineContext(shaderProcessingContext: Nullable<_IShaderProcessingContext>): IPipelineContext {\r\n        if (this._gl) {\r\n            const stateObject = getStateObject(this._gl);\r\n            stateObject.parallelShaderCompile = this._caps.parallelShaderCompile;\r\n        }\r\n        const context = createPipelineContext(this._gl, shaderProcessingContext) as WebGLPipelineContext;\r\n        context.engine = this;\r\n        return context;\r\n    }\r\n\r\n    /**\r\n     * Creates a new material context\r\n     * @returns the new context\r\n     */\r\n    public createMaterialContext(): IMaterialContext | undefined {\r\n        return undefined;\r\n    }\r\n\r\n    /**\r\n     * Creates a new draw context\r\n     * @returns the new context\r\n     */\r\n    public createDrawContext(): IDrawContext | undefined {\r\n        return undefined;\r\n    }\r\n\r\n    protected _finalizePipelineContext(pipelineContext: WebGLPipelineContext) {\r\n        return _finalizePipelineContext(pipelineContext, this._gl, this.validateShaderPrograms);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // named async but not actually an async function\r\n    // eslint-disable-next-line no-restricted-syntax\r\n    public _preparePipelineContextAsync(\r\n        pipelineContext: IPipelineContext,\r\n        vertexSourceCode: string,\r\n        fragmentSourceCode: string,\r\n        createAsRaw: boolean,\r\n        rawVertexSourceCode: string,\r\n        rawFragmentSourceCode: string,\r\n        rebuildRebind: any,\r\n        defines: Nullable<string>,\r\n        transformFeedbackVaryings: Nullable<string[]>,\r\n        key: string,\r\n        onReady: () => void\r\n    ) {\r\n        const stateObject = getStateObject(this._gl);\r\n        stateObject._contextWasLost = this._contextWasLost;\r\n        stateObject.validateShaderPrograms = this.validateShaderPrograms;\r\n        stateObject._createShaderProgramInjection = this._createShaderProgram.bind(this);\r\n        stateObject.createRawShaderProgramInjection = this.createRawShaderProgram.bind(this);\r\n        stateObject.createShaderProgramInjection = this.createShaderProgram.bind(this);\r\n        stateObject.loadFileInjection = this._loadFile.bind(this);\r\n        return _preparePipelineContext(\r\n            pipelineContext as WebGLPipelineContext,\r\n            vertexSourceCode,\r\n            fragmentSourceCode,\r\n            createAsRaw,\r\n            rawVertexSourceCode,\r\n            rawFragmentSourceCode,\r\n            rebuildRebind,\r\n            defines,\r\n            transformFeedbackVaryings,\r\n            key,\r\n            onReady\r\n        );\r\n    }\r\n\r\n    protected _createShaderProgram(\r\n        pipelineContext: WebGLPipelineContext,\r\n        vertexShader: WebGLShader,\r\n        fragmentShader: WebGLShader,\r\n        context: WebGLRenderingContext,\r\n        transformFeedbackVaryings: Nullable<string[]> = null\r\n    ): WebGLProgram {\r\n        return _createShaderProgram(pipelineContext, vertexShader, fragmentShader, context, transformFeedbackVaryings);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _isRenderingStateCompiled(pipelineContext: IPipelineContext): boolean {\r\n        if (this._isDisposed) {\r\n            return false;\r\n        }\r\n        return _isRenderingStateCompiled(pipelineContext, this._gl, this.validateShaderPrograms);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _executeWhenRenderingStateIsCompiled(pipelineContext: IPipelineContext, action: () => void) {\r\n        _executeWhenRenderingStateIsCompiled(pipelineContext as WebGLPipelineContext, action);\r\n    }\r\n\r\n    /**\r\n     * Gets the list of webGL uniform locations associated with a specific program based on a list of uniform names\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param uniformsNames defines the list of uniform names\r\n     * @returns an array of webGL uniform locations\r\n     */\r\n    public getUniforms(pipelineContext: IPipelineContext, uniformsNames: string[]): Nullable<WebGLUniformLocation>[] {\r\n        const results = new Array<Nullable<WebGLUniformLocation>>();\r\n        const webGLPipelineContext = pipelineContext as WebGLPipelineContext;\r\n\r\n        for (let index = 0; index < uniformsNames.length; index++) {\r\n            results.push(this._gl.getUniformLocation(webGLPipelineContext.program!, uniformsNames[index]));\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of active attributes for a given webGL program\r\n     * @param pipelineContext defines the pipeline context to use\r\n     * @param attributesNames defines the list of attribute names to get\r\n     * @returns an array of indices indicating the offset of each attribute\r\n     */\r\n    public getAttributes(pipelineContext: IPipelineContext, attributesNames: string[]): number[] {\r\n        const results = [];\r\n        const webGLPipelineContext = pipelineContext as WebGLPipelineContext;\r\n\r\n        for (let index = 0; index < attributesNames.length; index++) {\r\n            try {\r\n                results.push(this._gl.getAttribLocation(webGLPipelineContext.program!, attributesNames[index]));\r\n            } catch (e) {\r\n                results.push(-1);\r\n            }\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Activates an effect, making it the current one (ie. the one used for rendering)\r\n     * @param effect defines the effect to activate\r\n     */\r\n    public enableEffect(effect: Nullable<Effect | DrawWrapper>): void {\r\n        effect = effect !== null && IsWrapper(effect) ? effect.effect : effect; // get only the effect, we don't need a Wrapper in the WebGL engine\r\n\r\n        if (!effect || effect === this._currentEffect) {\r\n            return;\r\n        }\r\n\r\n        this._stencilStateComposer.stencilMaterial = undefined;\r\n\r\n        // Use program\r\n        this.bindSamplers(effect);\r\n\r\n        this._currentEffect = effect;\r\n\r\n        if (effect.onBind) {\r\n            effect.onBind(effect);\r\n        }\r\n        if (effect._onBindObservable) {\r\n            effect._onBindObservable.notifyObservers(effect);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a number (int)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param value defines the int number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setInt(uniform: Nullable<WebGLUniformLocation>, value: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1i(uniform, value);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a int2\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setInt2(uniform: Nullable<WebGLUniformLocation>, x: number, y: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2i(uniform, x, y);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a int3\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setInt3(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3i(uniform, x, y, z);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a int4\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @param w defines the 4th component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setInt4(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number, w: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4i(uniform, x, y, z, w);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setIntArray(uniform: Nullable<WebGLUniformLocation>, array: Int32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1iv(uniform, array);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32 (stored as vec2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setIntArray2(uniform: Nullable<WebGLUniformLocation>, array: Int32Array): boolean {\r\n        if (!uniform || array.length % 2 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2iv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32 (stored as vec3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setIntArray3(uniform: Nullable<WebGLUniformLocation>, array: Int32Array): boolean {\r\n        if (!uniform || array.length % 3 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3iv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of int32 (stored as vec4)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setIntArray4(uniform: Nullable<WebGLUniformLocation>, array: Int32Array): boolean {\r\n        if (!uniform || array.length % 4 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4iv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a number (unsigned int)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param value defines the unsigned int number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUInt(uniform: Nullable<WebGLUniformLocation>, value: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1ui(uniform, value);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a unsigned int2\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setUInt2(uniform: Nullable<WebGLUniformLocation>, x: number, y: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2ui(uniform, x, y);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a unsigned int3\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setUInt3(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3ui(uniform, x, y, z);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a unsigned int4\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @param w defines the 4th component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setUInt4(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number, w: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4ui(uniform, x, y, z, w);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of unsigned int32\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of unsigned int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUIntArray(uniform: Nullable<WebGLUniformLocation>, array: Uint32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1uiv(uniform, array);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of unsigned int32 (stored as vec2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of unsigned int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUIntArray2(uniform: Nullable<WebGLUniformLocation>, array: Uint32Array): boolean {\r\n        if (!uniform || array.length % 2 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2uiv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of unsigned int32 (stored as vec3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of unsigned int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUIntArray3(uniform: Nullable<WebGLUniformLocation>, array: Uint32Array): boolean {\r\n        if (!uniform || array.length % 3 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3uiv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of unsigned int32 (stored as vec4)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of unsigned int32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setUIntArray4(uniform: Nullable<WebGLUniformLocation>, array: Uint32Array): boolean {\r\n        if (!uniform || array.length % 4 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4uiv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setArray(uniform: Nullable<WebGLUniformLocation>, array: FloatArray): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        if (array.length < 1) {\r\n            return false;\r\n        }\r\n        this._gl.uniform1fv(uniform, array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number (stored as vec2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setArray2(uniform: Nullable<WebGLUniformLocation>, array: FloatArray): boolean {\r\n        if (!uniform || array.length % 2 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2fv(uniform, <any>array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number (stored as vec3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setArray3(uniform: Nullable<WebGLUniformLocation>, array: FloatArray): boolean {\r\n        if (!uniform || array.length % 3 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3fv(uniform, <any>array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of number (stored as vec4)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param array defines the array of number to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setArray4(uniform: Nullable<WebGLUniformLocation>, array: FloatArray): boolean {\r\n        if (!uniform || array.length % 4 !== 0) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4fv(uniform, <any>array);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to an array of float32 (stored as matrices)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param matrices defines the array of float32 to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setMatrices(uniform: Nullable<WebGLUniformLocation>, matrices: DeepImmutable<FloatArray>): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniformMatrix4fv(uniform, false, matrices);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a matrix (3x3)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param matrix defines the Float32Array representing the 3x3 matrix to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setMatrix3x3(uniform: Nullable<WebGLUniformLocation>, matrix: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniformMatrix3fv(uniform, false, matrix);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a matrix (2x2)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param matrix defines the Float32Array representing the 2x2 matrix to store\r\n     * @returns true if the value was set\r\n     */\r\n    public setMatrix2x2(uniform: Nullable<WebGLUniformLocation>, matrix: Float32Array): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniformMatrix2fv(uniform, false, matrix);\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a number (float)\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param value defines the float number to store\r\n     * @returns true if the value was transferred\r\n     */\r\n    public setFloat(uniform: Nullable<WebGLUniformLocation>, value: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform1f(uniform, value);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a vec2\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setFloat2(uniform: Nullable<WebGLUniformLocation>, x: number, y: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform2f(uniform, x, y);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a vec3\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setFloat3(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform3f(uniform, x, y, z);\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Set the value of an uniform to a vec4\r\n     * @param uniform defines the webGL uniform location where to store the value\r\n     * @param x defines the 1st component of the value\r\n     * @param y defines the 2nd component of the value\r\n     * @param z defines the 3rd component of the value\r\n     * @param w defines the 4th component of the value\r\n     * @returns true if the value was set\r\n     */\r\n    public setFloat4(uniform: Nullable<WebGLUniformLocation>, x: number, y: number, z: number, w: number): boolean {\r\n        if (!uniform) {\r\n            return false;\r\n        }\r\n\r\n        this._gl.uniform4f(uniform, x, y, z, w);\r\n\r\n        return true;\r\n    }\r\n\r\n    // States\r\n\r\n    /**\r\n     * Apply all cached states (depth, culling, stencil and alpha)\r\n     */\r\n    public applyStates() {\r\n        this._depthCullingState.apply(this._gl);\r\n        this._stencilStateComposer.apply(this._gl);\r\n        this._alphaState.apply(this._gl, this._currentRenderTarget && this._currentRenderTarget.textures ? this._currentRenderTarget.textures!.length : 1);\r\n\r\n        if (this._colorWriteChanged) {\r\n            this._colorWriteChanged = false;\r\n            const enable = this._colorWrite;\r\n            this._gl.colorMask(enable, enable, enable, enable);\r\n        }\r\n    }\r\n\r\n    // Textures\r\n\r\n    /**\r\n     * Force the entire cache to be cleared\r\n     * You should not have to use this function unless your engine needs to share the webGL context with another engine\r\n     * @param bruteForce defines a boolean to force clearing ALL caches (including stencil, detoh and alpha states)\r\n     */\r\n    public wipeCaches(bruteForce?: boolean): void {\r\n        if (this.preventCacheWipeBetweenFrames && !bruteForce) {\r\n            return;\r\n        }\r\n        this._currentEffect = null;\r\n        this._viewportCached.x = 0;\r\n        this._viewportCached.y = 0;\r\n        this._viewportCached.z = 0;\r\n        this._viewportCached.w = 0;\r\n\r\n        // Done before in case we clean the attributes\r\n        this._unbindVertexArrayObject();\r\n\r\n        if (bruteForce) {\r\n            this._currentProgram = null;\r\n            this.resetTextureCache();\r\n\r\n            this._stencilStateComposer.reset();\r\n\r\n            this._depthCullingState.reset();\r\n            this._depthCullingState.depthFunc = this._gl.LEQUAL;\r\n\r\n            this._alphaState.reset();\r\n            this._resetAlphaMode();\r\n\r\n            this._colorWrite = true;\r\n            this._colorWriteChanged = true;\r\n\r\n            this._unpackFlipYCached = null;\r\n\r\n            this._gl.pixelStorei(this._gl.UNPACK_COLORSPACE_CONVERSION_WEBGL, this._gl.NONE);\r\n            this._gl.pixelStorei(this._gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, 0);\r\n\r\n            this._mustWipeVertexAttributes = true;\r\n            this.unbindAllAttributes();\r\n        }\r\n\r\n        this._resetVertexBufferBinding();\r\n        this._cachedIndexBuffer = null;\r\n        this._cachedEffectForVertexBuffers = null;\r\n        this.bindIndexBuffer(null);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getSamplingParameters(samplingMode: number, generateMipMaps: boolean): { min: number; mag: number; hasMipMaps: boolean } {\r\n        const gl = this._gl;\r\n        let magFilter: GLenum = gl.NEAREST;\r\n        let minFilter: GLenum = gl.NEAREST;\r\n        let hasMipMaps = false;\r\n\r\n        switch (samplingMode) {\r\n            case Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST:\r\n                magFilter = gl.LINEAR;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.LINEAR_MIPMAP_NEAREST;\r\n                } else {\r\n                    minFilter = gl.LINEAR;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR:\r\n                magFilter = gl.LINEAR;\r\n                hasMipMaps = true;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.LINEAR_MIPMAP_LINEAR;\r\n                } else {\r\n                    minFilter = gl.LINEAR;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_NEAREST_MIPLINEAR:\r\n                hasMipMaps = true;\r\n                magFilter = gl.NEAREST;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.NEAREST_MIPMAP_LINEAR;\r\n                } else {\r\n                    minFilter = gl.NEAREST;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_NEAREST_MIPNEAREST:\r\n                magFilter = gl.NEAREST;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.NEAREST_MIPMAP_NEAREST;\r\n                } else {\r\n                    minFilter = gl.NEAREST;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_LINEAR_MIPNEAREST:\r\n                magFilter = gl.NEAREST;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.LINEAR_MIPMAP_NEAREST;\r\n                } else {\r\n                    minFilter = gl.LINEAR;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_LINEAR_MIPLINEAR:\r\n                hasMipMaps = true;\r\n                magFilter = gl.NEAREST;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.LINEAR_MIPMAP_LINEAR;\r\n                } else {\r\n                    minFilter = gl.LINEAR;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_LINEAR:\r\n                magFilter = gl.NEAREST;\r\n                minFilter = gl.LINEAR;\r\n                break;\r\n            case Constants.TEXTURE_NEAREST_NEAREST:\r\n                magFilter = gl.NEAREST;\r\n                minFilter = gl.NEAREST;\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_NEAREST_MIPNEAREST:\r\n                magFilter = gl.LINEAR;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.NEAREST_MIPMAP_NEAREST;\r\n                } else {\r\n                    minFilter = gl.NEAREST;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_NEAREST_MIPLINEAR:\r\n                hasMipMaps = true;\r\n                magFilter = gl.LINEAR;\r\n                if (generateMipMaps) {\r\n                    minFilter = gl.NEAREST_MIPMAP_LINEAR;\r\n                } else {\r\n                    minFilter = gl.NEAREST;\r\n                }\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_LINEAR:\r\n                magFilter = gl.LINEAR;\r\n                minFilter = gl.LINEAR;\r\n                break;\r\n            case Constants.TEXTURE_LINEAR_NEAREST:\r\n                magFilter = gl.LINEAR;\r\n                minFilter = gl.NEAREST;\r\n                break;\r\n        }\r\n\r\n        return {\r\n            min: minFilter,\r\n            mag: magFilter,\r\n            hasMipMaps: hasMipMaps,\r\n        };\r\n    }\r\n\r\n    /** @internal */\r\n    protected _createTexture(): WebGLTexture {\r\n        const texture = this._gl.createTexture();\r\n\r\n        if (!texture) {\r\n            throw new Error(\"Unable to create texture\");\r\n        }\r\n\r\n        return texture;\r\n    }\r\n\r\n    /** @internal */\r\n    public _createHardwareTexture(): IHardwareTextureWrapper {\r\n        return new WebGLHardwareTexture(this._createTexture(), this._gl);\r\n    }\r\n\r\n    /**\r\n     * Creates an internal texture without binding it to a framebuffer\r\n     * @internal\r\n     * @param size defines the size of the texture\r\n     * @param options defines the options used to create the texture\r\n     * @param delayGPUTextureCreation true to delay the texture creation the first time it is really needed. false to create it right away\r\n     * @param source source type of the texture\r\n     * @returns a new internal texture\r\n     */\r\n    public _createInternalTexture(\r\n        size: TextureSize,\r\n        options: boolean | InternalTextureCreationOptions,\r\n        delayGPUTextureCreation = true,\r\n        source = InternalTextureSource.Unknown\r\n    ): InternalTexture {\r\n        let generateMipMaps = false;\r\n        let createMipMaps = false;\r\n        let type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n        let samplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n        let format = Constants.TEXTUREFORMAT_RGBA;\r\n        let useSRGBBuffer = false;\r\n        let samples = 1;\r\n        let label: string | undefined;\r\n        let createMSAATexture = false;\r\n        let comparisonFunction = 0;\r\n        if (options !== undefined && typeof options === \"object\") {\r\n            generateMipMaps = !!options.generateMipMaps;\r\n            createMipMaps = !!options.createMipMaps;\r\n            type = options.type === undefined ? Constants.TEXTURETYPE_UNSIGNED_BYTE : options.type;\r\n            samplingMode = options.samplingMode === undefined ? Constants.TEXTURE_TRILINEAR_SAMPLINGMODE : options.samplingMode;\r\n            format = options.format === undefined ? Constants.TEXTUREFORMAT_RGBA : options.format;\r\n            useSRGBBuffer = options.useSRGBBuffer === undefined ? false : options.useSRGBBuffer;\r\n            samples = options.samples ?? 1;\r\n            label = options.label;\r\n            createMSAATexture = !!options.createMSAATexture;\r\n            comparisonFunction = options.comparisonFunction || 0;\r\n        } else {\r\n            generateMipMaps = !!options;\r\n        }\r\n\r\n        useSRGBBuffer &&= this._caps.supportSRGBBuffers && (this.webGLVersion > 1 || this.isWebGPU);\r\n\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n            // if floating point linear (gl.FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        } else if (type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n            // if floating point linear (HALF_FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n            type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n            Logger.Warn(\"Float textures are not supported. Type forced to TEXTURETYPE_UNSIGNED_BYTE\");\r\n        }\r\n\r\n        const isDepthTexture = IsDepthTexture(format);\r\n        const hasStencil = HasStencilAspect(format);\r\n\r\n        const gl = this._gl;\r\n        const texture = new InternalTexture(this, source);\r\n        const width = (<{ width: number; height: number; depth?: number; layers?: number }>size).width || <number>size;\r\n        const height = (<{ width: number; height: number; depth?: number; layers?: number }>size).height || <number>size;\r\n        const depth = (<{ width: number; height: number; depth?: number; layers?: number }>size).depth || 0;\r\n        const layers = (<{ width: number; height: number; depth?: number; layers?: number }>size).layers || 0;\r\n        const filters = this._getSamplingParameters(samplingMode, (generateMipMaps || createMipMaps) && !isDepthTexture);\r\n        const target = layers !== 0 ? gl.TEXTURE_2D_ARRAY : depth !== 0 ? gl.TEXTURE_3D : gl.TEXTURE_2D;\r\n        const sizedFormat = isDepthTexture\r\n            ? this._getInternalFormatFromDepthTextureFormat(format, true, hasStencil)\r\n            : this._getRGBABufferInternalSizedFormat(type, format, useSRGBBuffer);\r\n        const internalFormat = isDepthTexture ? (hasStencil ? gl.DEPTH_STENCIL : gl.DEPTH_COMPONENT) : this._getInternalFormat(format);\r\n        const textureType = isDepthTexture ? this._getWebGLTextureTypeFromDepthTextureFormat(format) : this._getWebGLTextureType(type);\r\n\r\n        // Bind\r\n        this._bindTextureDirectly(target, texture);\r\n\r\n        if (layers !== 0) {\r\n            texture.is2DArray = true;\r\n            gl.texImage3D(target, 0, sizedFormat, width, height, layers, 0, internalFormat, textureType, null);\r\n        } else if (depth !== 0) {\r\n            texture.is3D = true;\r\n            gl.texImage3D(target, 0, sizedFormat, width, height, depth, 0, internalFormat, textureType, null);\r\n        } else {\r\n            gl.texImage2D(target, 0, sizedFormat, width, height, 0, internalFormat, textureType, null);\r\n        }\r\n\r\n        gl.texParameteri(target, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n        gl.texParameteri(target, gl.TEXTURE_MIN_FILTER, filters.min);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n        if (isDepthTexture && this.webGLVersion > 1) {\r\n            if (comparisonFunction === 0) {\r\n                gl.texParameteri(target, gl.TEXTURE_COMPARE_FUNC, Constants.LEQUAL);\r\n                gl.texParameteri(target, gl.TEXTURE_COMPARE_MODE, gl.NONE);\r\n            } else {\r\n                gl.texParameteri(target, gl.TEXTURE_COMPARE_FUNC, comparisonFunction);\r\n                gl.texParameteri(target, gl.TEXTURE_COMPARE_MODE, gl.COMPARE_REF_TO_TEXTURE);\r\n            }\r\n        }\r\n\r\n        // MipMaps\r\n        if (generateMipMaps || createMipMaps) {\r\n            this._gl.generateMipmap(target);\r\n        }\r\n\r\n        this._bindTextureDirectly(target, null);\r\n\r\n        texture._useSRGBBuffer = useSRGBBuffer;\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.depth = layers || depth;\r\n        texture.isReady = true;\r\n        texture.samples = samples;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        texture.type = type;\r\n        texture.format = format;\r\n        texture.label = label;\r\n        texture.comparisonFunction = comparisonFunction;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        if (createMSAATexture) {\r\n            let renderBuffer: Nullable<WebGLRenderbuffer> = null;\r\n\r\n            if (IsDepthTexture(texture.format)) {\r\n                renderBuffer = this._setupFramebufferDepthAttachments(\r\n                    HasStencilAspect(texture.format),\r\n                    texture.format !== Constants.TEXTUREFORMAT_STENCIL8,\r\n                    texture.width,\r\n                    texture.height,\r\n                    samples,\r\n                    texture.format,\r\n                    true\r\n                );\r\n            } else {\r\n                renderBuffer = this._createRenderBuffer(\r\n                    texture.width,\r\n                    texture.height,\r\n                    samples,\r\n                    -1 /* not used */,\r\n                    this._getRGBABufferInternalSizedFormat(texture.type, texture.format, texture._useSRGBBuffer),\r\n                    -1 /* attachment */\r\n                );\r\n            }\r\n\r\n            if (!renderBuffer) {\r\n                throw new Error(\"Unable to create render buffer\");\r\n            }\r\n\r\n            texture._autoMSAAManagement = true;\r\n\r\n            let hardwareTexture = texture._hardwareTexture as Nullable<WebGLHardwareTexture>;\r\n            if (!hardwareTexture) {\r\n                hardwareTexture = texture._hardwareTexture = this._createHardwareTexture() as WebGLHardwareTexture;\r\n            }\r\n\r\n            hardwareTexture.addMSAARenderBuffer(renderBuffer);\r\n        }\r\n\r\n        return texture;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getUseSRGBBuffer(useSRGBBuffer: boolean, noMipmap: boolean): boolean {\r\n        // Generating mipmaps for sRGB textures is not supported in WebGL1 so we must disable the support if mipmaps is enabled\r\n        return useSRGBBuffer && this._caps.supportSRGBBuffers && (this.webGLVersion > 1 || noMipmap);\r\n    }\r\n\r\n    /**\r\n     * Usually called from Texture.ts.\r\n     * Passed information to create a WebGLTexture\r\n     * @param url defines a value which contains one of the following:\r\n     * * A conventional http URL, e.g. 'http://...' or 'file://...'\r\n     * * A base64 string of in-line texture data, e.g. 'data:image/jpg;base64,/...'\r\n     * * An indicator that data being passed using the buffer parameter, e.g. 'data:mytexture.jpg'\r\n     * @param noMipmap defines a boolean indicating that no mipmaps shall be generated.  Ignored for compressed textures.  They must be in the file\r\n     * @param invertY when true, image is flipped when loaded.  You probably want true. Certain compressed textures may invert this if their default is inverted (eg. ktx)\r\n     * @param scene needed for loading to the correct scene\r\n     * @param samplingMode mode with should be used sample / access the texture (Default: Texture.TRILINEAR_SAMPLINGMODE)\r\n     * @param onLoad optional callback to be called upon successful completion\r\n     * @param onError optional callback to be called upon failure\r\n     * @param buffer a source of a file previously fetched as either a base64 string, an ArrayBuffer (compressed or image format), HTMLImageElement (image format), or a Blob\r\n     * @param fallback an internal argument in case the function must be called again, due to etc1 not having alpha capabilities\r\n     * @param format internal format.  Default: RGB when extension is '.jpg' else RGBA.  Ignored for compressed textures\r\n     * @param forcedExtension defines the extension to use to pick the right loader\r\n     * @param mimeType defines an optional mime type\r\n     * @param loaderOptions options to be passed to the loader\r\n     * @param creationFlags specific flags to use when creating the texture (Constants.TEXTURE_CREATIONFLAG_STORAGE for storage textures, for eg)\r\n     * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n     * @returns a InternalTexture for assignment back into BABYLON.Texture\r\n     */\r\n    public createTexture(\r\n        url: Nullable<string>,\r\n        noMipmap: boolean,\r\n        invertY: boolean,\r\n        scene: Nullable<ISceneLike>,\r\n        samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        onLoad: Nullable<(texture: InternalTexture) => void> = null,\r\n        onError: Nullable<(message: string, exception: any) => void> = null,\r\n        buffer: Nullable<string | ArrayBuffer | ArrayBufferView | HTMLImageElement | Blob | ImageBitmap> = null,\r\n        fallback: Nullable<InternalTexture> = null,\r\n        format: Nullable<number> = null,\r\n        forcedExtension: Nullable<string> = null,\r\n        mimeType?: string,\r\n        loaderOptions?: any,\r\n        creationFlags?: number,\r\n        useSRGBBuffer?: boolean\r\n    ): InternalTexture {\r\n        return this._createTextureBase(\r\n            url,\r\n            noMipmap,\r\n            invertY,\r\n            scene,\r\n            samplingMode,\r\n            onLoad,\r\n            onError,\r\n            (...args: Parameters<PrepareTextureFunction>) => this._prepareWebGLTexture(...args, format),\r\n            (potWidth, potHeight, img, extension, texture, continuationCallback) => {\r\n                const gl = this._gl;\r\n                const isPot = img.width === potWidth && img.height === potHeight;\r\n\r\n                texture._creationFlags = creationFlags ?? 0;\r\n\r\n                const tip = this._getTexImageParametersForCreateTexture(texture.format, texture._useSRGBBuffer);\r\n                if (isPot) {\r\n                    gl.texImage2D(gl.TEXTURE_2D, 0, tip.internalFormat, tip.format, tip.type, img as any);\r\n                    return false;\r\n                }\r\n\r\n                const maxTextureSize = this._caps.maxTextureSize;\r\n\r\n                if (img.width > maxTextureSize || img.height > maxTextureSize || !this._supportsHardwareTextureRescaling) {\r\n                    this._prepareWorkingCanvas();\r\n                    if (!this._workingCanvas || !this._workingContext) {\r\n                        return false;\r\n                    }\r\n\r\n                    this._workingCanvas.width = potWidth;\r\n                    this._workingCanvas.height = potHeight;\r\n\r\n                    this._workingContext.drawImage(img as any, 0, 0, img.width, img.height, 0, 0, potWidth, potHeight);\r\n                    gl.texImage2D(gl.TEXTURE_2D, 0, tip.internalFormat, tip.format, tip.type, this._workingCanvas as TexImageSource);\r\n\r\n                    texture.width = potWidth;\r\n                    texture.height = potHeight;\r\n\r\n                    return false;\r\n                } else {\r\n                    // Using shaders when possible to rescale because canvas.drawImage is lossy\r\n                    const source = new InternalTexture(this, InternalTextureSource.Temp);\r\n                    this._bindTextureDirectly(gl.TEXTURE_2D, source, true);\r\n                    gl.texImage2D(gl.TEXTURE_2D, 0, tip.internalFormat, tip.format, tip.type, img as any);\r\n\r\n                    this._rescaleTexture(source, texture, scene, tip.format, () => {\r\n                        this._releaseTexture(source);\r\n                        this._bindTextureDirectly(gl.TEXTURE_2D, texture, true);\r\n\r\n                        continuationCallback();\r\n                    });\r\n                }\r\n\r\n                return true;\r\n            },\r\n            buffer,\r\n            fallback,\r\n            format,\r\n            forcedExtension,\r\n            mimeType,\r\n            loaderOptions,\r\n            useSRGBBuffer\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Calls to the GL texImage2D and texImage3D functions require three arguments describing the pixel format of the texture.\r\n     * createTexture derives these from the babylonFormat and useSRGBBuffer arguments and also the file extension of the URL it's working with.\r\n     * This function encapsulates that derivation for easy unit testing.\r\n     * @param babylonFormat Babylon's format enum, as specified in ITextureCreationOptions.\r\n     * @param fileExtension The file extension including the dot, e.g. .jpg.\r\n     * @param useSRGBBuffer Use SRGB not linear.\r\n     * @returns The options to pass to texImage2D or texImage3D calls.\r\n     * @internal\r\n     */\r\n    public _getTexImageParametersForCreateTexture(babylonFormat: number, useSRGBBuffer: boolean): TexImageParameters {\r\n        let format: number, internalFormat: number;\r\n        if (this.webGLVersion === 1) {\r\n            // In WebGL 1, format and internalFormat must be the same and taken from a limited set of values, see https://docs.gl/es2/glTexImage2D.\r\n            // The SRGB extension (https://developer.mozilla.org/en-US/docs/Web/API/EXT_sRGB) adds some extra values, hence passing useSRGBBuffer\r\n            // to getInternalFormat.\r\n            format = this._getInternalFormat(babylonFormat, useSRGBBuffer);\r\n            internalFormat = format;\r\n        } else {\r\n            // In WebGL 2, format has a wider range of values and internal format can be one of the sized formats, see\r\n            // https://registry.khronos.org/OpenGL-Refpages/es3.0/html/glTexImage2D.xhtml.\r\n            // SRGB is included in the sized format and should not be passed in \"format\", hence always passing useSRGBBuffer as false.\r\n            format = this._getInternalFormat(babylonFormat, false);\r\n            internalFormat = this._getRGBABufferInternalSizedFormat(Constants.TEXTURETYPE_UNSIGNED_BYTE, babylonFormat, useSRGBBuffer);\r\n        }\r\n\r\n        return {\r\n            internalFormat,\r\n            format,\r\n            type: this._gl.UNSIGNED_BYTE,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _rescaleTexture(source: InternalTexture, destination: InternalTexture, scene: Nullable<any>, internalFormat: number, onComplete: () => void): void {}\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private _unpackFlipYCached: Nullable<boolean> = null;\r\n\r\n    /**\r\n     * In case you are sharing the context with other applications, it might\r\n     * be interested to not cache the unpack flip y state to ensure a consistent\r\n     * value would be set.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public enableUnpackFlipYCached = true;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _unpackFlipY(value: boolean): void {\r\n        if (this._unpackFlipYCached !== value) {\r\n            this._gl.pixelStorei(this._gl.UNPACK_FLIP_Y_WEBGL, value ? 1 : 0);\r\n\r\n            if (this.enableUnpackFlipYCached) {\r\n                this._unpackFlipYCached = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public _getUnpackAlignement(): number {\r\n        return this._gl.getParameter(this._gl.UNPACK_ALIGNMENT) as number;\r\n    }\r\n\r\n    /** @internal */\r\n    public _getTextureTarget(texture: InternalTexture): number {\r\n        if (texture.isCube) {\r\n            return this._gl.TEXTURE_CUBE_MAP;\r\n        } else if (texture.is3D) {\r\n            return this._gl.TEXTURE_3D;\r\n        } else if (texture.is2DArray || texture.isMultiview) {\r\n            return this._gl.TEXTURE_2D_ARRAY;\r\n        }\r\n        return this._gl.TEXTURE_2D;\r\n    }\r\n\r\n    /**\r\n     * Update the sampling mode of a given texture\r\n     * @param samplingMode defines the required sampling mode\r\n     * @param texture defines the texture to update\r\n     * @param generateMipMaps defines whether to generate mipmaps for the texture\r\n     */\r\n    public updateTextureSamplingMode(samplingMode: number, texture: InternalTexture, generateMipMaps: boolean = false): void {\r\n        const target = this._getTextureTarget(texture);\r\n        const filters = this._getSamplingParameters(samplingMode, texture.useMipMaps || generateMipMaps);\r\n\r\n        this._setTextureParameterInteger(target, this._gl.TEXTURE_MAG_FILTER, filters.mag, texture);\r\n        this._setTextureParameterInteger(target, this._gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n        if (generateMipMaps && filters.hasMipMaps) {\r\n            texture.generateMipMaps = true;\r\n            this._gl.generateMipmap(target);\r\n        }\r\n\r\n        this._bindTextureDirectly(target, null);\r\n\r\n        texture.samplingMode = samplingMode;\r\n    }\r\n\r\n    /**\r\n     * Update the dimensions of a texture\r\n     * @param texture texture to update\r\n     * @param width new width of the texture\r\n     * @param height new height of the texture\r\n     * @param depth new depth of the texture\r\n     */\r\n    public updateTextureDimensions(texture: InternalTexture, width: number, height: number, depth: number = 1): void {}\r\n\r\n    /**\r\n     * Update the sampling mode of a given texture\r\n     * @param texture defines the texture to update\r\n     * @param wrapU defines the texture wrap mode of the u coordinates\r\n     * @param wrapV defines the texture wrap mode of the v coordinates\r\n     * @param wrapR defines the texture wrap mode of the r coordinates\r\n     */\r\n    public updateTextureWrappingMode(texture: InternalTexture, wrapU: Nullable<number>, wrapV: Nullable<number> = null, wrapR: Nullable<number> = null): void {\r\n        const target = this._getTextureTarget(texture);\r\n\r\n        if (wrapU !== null) {\r\n            this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_S, this._getTextureWrapMode(wrapU), texture);\r\n            texture._cachedWrapU = wrapU;\r\n        }\r\n        if (wrapV !== null) {\r\n            this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_T, this._getTextureWrapMode(wrapV), texture);\r\n            texture._cachedWrapV = wrapV;\r\n        }\r\n        if ((texture.is2DArray || texture.is3D) && wrapR !== null) {\r\n            this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_R, this._getTextureWrapMode(wrapR), texture);\r\n            texture._cachedWrapR = wrapR;\r\n        }\r\n\r\n        this._bindTextureDirectly(target, null);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadCompressedDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        internalFormat: number,\r\n        width: number,\r\n        height: number,\r\n        data: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0\r\n    ) {\r\n        const gl = this._gl;\r\n\r\n        let target: GLenum = gl.TEXTURE_2D;\r\n        if (texture.isCube) {\r\n            target = gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex;\r\n        }\r\n\r\n        if (texture._useSRGBBuffer) {\r\n            switch (internalFormat) {\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGB8_ETC2:\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGB_ETC1_WEBGL:\r\n                    // Note, if using ETC1 and sRGB is requested, this will use ETC2 if available.\r\n                    if (this._caps.etc2) {\r\n                        internalFormat = gl.COMPRESSED_SRGB8_ETC2;\r\n                    } else {\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA8_ETC2_EAC:\r\n                    if (this._caps.etc2) {\r\n                        internalFormat = gl.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC;\r\n                    } else {\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_BPTC_UNORM:\r\n                    internalFormat = gl.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_ASTC_4x4:\r\n                    internalFormat = gl.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGB_S3TC_DXT1:\r\n                    if (this._caps.s3tc_srgb) {\r\n                        internalFormat = gl.COMPRESSED_SRGB_S3TC_DXT1_EXT;\r\n                    } else {\r\n                        // S3TC sRGB extension not supported\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT1:\r\n                    if (this._caps.s3tc_srgb) {\r\n                        internalFormat = gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT;\r\n                    } else {\r\n                        // S3TC sRGB extension not supported\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT5:\r\n                    if (this._caps.s3tc_srgb) {\r\n                        internalFormat = gl.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT;\r\n                    } else {\r\n                        // S3TC sRGB extension not supported\r\n                        texture._useSRGBBuffer = false;\r\n                    }\r\n                    break;\r\n                default:\r\n                    // We don't support a sRGB format corresponding to internalFormat, so revert to non sRGB format\r\n                    texture._useSRGBBuffer = false;\r\n                    break;\r\n            }\r\n        }\r\n\r\n        this._gl.compressedTexImage2D(target, lod, internalFormat, width, height, 0, <DataView>data);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadDataToTextureDirectly(\r\n        texture: InternalTexture,\r\n        imageData: ArrayBufferView,\r\n        faceIndex: number = 0,\r\n        lod: number = 0,\r\n        babylonInternalFormat?: number,\r\n        useTextureWidthAndHeight = false\r\n    ): void {\r\n        const gl = this._gl;\r\n\r\n        const textureType = this._getWebGLTextureType(texture.type);\r\n        const format = this._getInternalFormat(texture.format);\r\n        const internalFormat =\r\n            babylonInternalFormat === undefined\r\n                ? this._getRGBABufferInternalSizedFormat(texture.type, texture.format, texture._useSRGBBuffer)\r\n                : this._getInternalFormat(babylonInternalFormat, texture._useSRGBBuffer);\r\n\r\n        this._unpackFlipY(texture.invertY);\r\n\r\n        let target: GLenum = gl.TEXTURE_2D;\r\n        if (texture.isCube) {\r\n            target = gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex;\r\n        }\r\n\r\n        const lodMaxWidth = Math.round(Math.log(texture.width) * Math.LOG2E);\r\n        const lodMaxHeight = Math.round(Math.log(texture.height) * Math.LOG2E);\r\n        const width = useTextureWidthAndHeight ? texture.width : Math.pow(2, Math.max(lodMaxWidth - lod, 0));\r\n        const height = useTextureWidthAndHeight ? texture.height : Math.pow(2, Math.max(lodMaxHeight - lod, 0));\r\n\r\n        gl.texImage2D(target, lod, internalFormat, width, height, 0, format, textureType, imageData);\r\n    }\r\n\r\n    /**\r\n     * Update a portion of an internal texture\r\n     * @param texture defines the texture to update\r\n     * @param imageData defines the data to store into the texture\r\n     * @param xOffset defines the x coordinates of the update rectangle\r\n     * @param yOffset defines the y coordinates of the update rectangle\r\n     * @param width defines the width of the update rectangle\r\n     * @param height defines the height of the update rectangle\r\n     * @param faceIndex defines the face index if texture is a cube (0 by default)\r\n     * @param lod defines the lod level to update (0 by default)\r\n     * @param generateMipMaps defines whether to generate mipmaps or not\r\n     */\r\n    public updateTextureData(\r\n        texture: InternalTexture,\r\n        imageData: ArrayBufferView,\r\n        xOffset: number,\r\n        yOffset: number,\r\n        width: number,\r\n        height: number,\r\n        faceIndex: number = 0,\r\n        lod: number = 0,\r\n        generateMipMaps = false\r\n    ): void {\r\n        const gl = this._gl;\r\n\r\n        const textureType = this._getWebGLTextureType(texture.type);\r\n        const format = this._getInternalFormat(texture.format);\r\n\r\n        this._unpackFlipY(texture.invertY);\r\n\r\n        let targetForBinding: GLenum = gl.TEXTURE_2D;\r\n        let target: GLenum = gl.TEXTURE_2D;\r\n        if (texture.isCube) {\r\n            target = gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex;\r\n            targetForBinding = gl.TEXTURE_CUBE_MAP;\r\n        }\r\n\r\n        this._bindTextureDirectly(targetForBinding, texture, true);\r\n\r\n        gl.texSubImage2D(target, lod, xOffset, yOffset, width, height, format, textureType, imageData);\r\n\r\n        if (generateMipMaps) {\r\n            this._gl.generateMipmap(target);\r\n        }\r\n\r\n        this._bindTextureDirectly(targetForBinding, null);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _uploadArrayBufferViewToTexture(texture: InternalTexture, imageData: ArrayBufferView, faceIndex: number = 0, lod: number = 0): void {\r\n        const gl = this._gl;\r\n        const bindTarget = texture.isCube ? gl.TEXTURE_CUBE_MAP : gl.TEXTURE_2D;\r\n\r\n        this._bindTextureDirectly(bindTarget, texture, true);\r\n\r\n        this._uploadDataToTextureDirectly(texture, imageData, faceIndex, lod);\r\n\r\n        this._bindTextureDirectly(bindTarget, null, true);\r\n    }\r\n\r\n    protected _prepareWebGLTextureContinuation(texture: InternalTexture, scene: Nullable<ISceneLike>, noMipmap: boolean, isCompressed: boolean, samplingMode: number): void {\r\n        const gl = this._gl;\r\n        if (!gl) {\r\n            return;\r\n        }\r\n\r\n        const filters = this._getSamplingParameters(samplingMode, !noMipmap);\r\n\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n        if (!noMipmap && !isCompressed) {\r\n            gl.generateMipmap(gl.TEXTURE_2D);\r\n        }\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_2D, null);\r\n\r\n        // this.resetTextureCache();\r\n        if (scene) {\r\n            scene.removePendingData(texture);\r\n        }\r\n\r\n        texture.onLoadedObservable.notifyObservers(texture);\r\n        texture.onLoadedObservable.clear();\r\n    }\r\n\r\n    private _prepareWebGLTexture(\r\n        texture: InternalTexture,\r\n        extension: string,\r\n        scene: Nullable<ISceneLike>,\r\n        img: HTMLImageElement | ImageBitmap | { width: number; height: number },\r\n        invertY: boolean,\r\n        noMipmap: boolean,\r\n        isCompressed: boolean,\r\n        processFunction: PrepareTextureProcessFunction,\r\n        samplingMode: number,\r\n        format: Nullable<number>\r\n    ): void {\r\n        const maxTextureSize = this.getCaps().maxTextureSize;\r\n        const potWidth = Math.min(maxTextureSize, this.needPOTTextures ? GetExponentOfTwo(img.width, maxTextureSize) : img.width);\r\n        const potHeight = Math.min(maxTextureSize, this.needPOTTextures ? GetExponentOfTwo(img.height, maxTextureSize) : img.height);\r\n\r\n        const gl = this._gl;\r\n        if (!gl) {\r\n            return;\r\n        }\r\n\r\n        if (!texture._hardwareTexture) {\r\n            //  this.resetTextureCache();\r\n            if (scene) {\r\n                scene.removePendingData(texture);\r\n            }\r\n\r\n            return;\r\n        }\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_2D, texture, true);\r\n        this._unpackFlipY(invertY === undefined ? true : invertY ? true : false);\r\n\r\n        texture.baseWidth = img.width;\r\n        texture.baseHeight = img.height;\r\n        texture.width = potWidth;\r\n        texture.height = potHeight;\r\n        texture.isReady = true;\r\n        texture.type = texture.type !== -1 ? texture.type : Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n        texture.format =\r\n            texture.format !== -1 ? texture.format : (format ?? (extension === \".jpg\" && !texture._useSRGBBuffer ? Constants.TEXTUREFORMAT_RGB : Constants.TEXTUREFORMAT_RGBA));\r\n\r\n        if (\r\n            processFunction(potWidth, potHeight, img, extension, texture, () => {\r\n                this._prepareWebGLTextureContinuation(texture, scene, noMipmap, isCompressed, samplingMode);\r\n            })\r\n        ) {\r\n            // Returning as texture needs extra async steps\r\n            return;\r\n        }\r\n\r\n        this._prepareWebGLTextureContinuation(texture, scene, noMipmap, isCompressed, samplingMode);\r\n    }\r\n\r\n    public _getInternalFormatFromDepthTextureFormat(textureFormat: number, hasDepth: boolean, hasStencil: boolean): number {\r\n        const gl = this._gl;\r\n\r\n        if (!hasDepth) {\r\n            return gl.STENCIL_INDEX8;\r\n        }\r\n\r\n        const format: GLenum = hasStencil ? gl.DEPTH_STENCIL : gl.DEPTH_COMPONENT;\r\n        let internalFormat = format;\r\n        if (this.webGLVersion > 1) {\r\n            if (textureFormat === Constants.TEXTUREFORMAT_DEPTH16) {\r\n                internalFormat = gl.DEPTH_COMPONENT16;\r\n            } else if (textureFormat === Constants.TEXTUREFORMAT_DEPTH24) {\r\n                internalFormat = gl.DEPTH_COMPONENT24;\r\n            } else if (textureFormat === Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8 || textureFormat === Constants.TEXTUREFORMAT_DEPTH24_STENCIL8) {\r\n                internalFormat = hasStencil ? gl.DEPTH24_STENCIL8 : gl.DEPTH_COMPONENT24;\r\n            } else if (textureFormat === Constants.TEXTUREFORMAT_DEPTH32_FLOAT) {\r\n                internalFormat = gl.DEPTH_COMPONENT32F;\r\n            } else if (textureFormat === Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8) {\r\n                internalFormat = hasStencil ? gl.DEPTH32F_STENCIL8 : gl.DEPTH_COMPONENT32F;\r\n            }\r\n        } else {\r\n            internalFormat = gl.DEPTH_COMPONENT16;\r\n        }\r\n\r\n        return internalFormat;\r\n    }\r\n\r\n    public _getWebGLTextureTypeFromDepthTextureFormat(textureFormat: number): GLenum {\r\n        const gl = this._gl;\r\n\r\n        let type: GLenum = gl.UNSIGNED_INT;\r\n        if (textureFormat === Constants.TEXTUREFORMAT_DEPTH16) {\r\n            type = gl.UNSIGNED_SHORT;\r\n        } else if (textureFormat === Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8 || textureFormat === Constants.TEXTUREFORMAT_DEPTH24_STENCIL8) {\r\n            type = gl.UNSIGNED_INT_24_8;\r\n        } else if (textureFormat === Constants.TEXTUREFORMAT_DEPTH32_FLOAT) {\r\n            type = gl.FLOAT;\r\n        } else if (textureFormat === Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8) {\r\n            type = gl.FLOAT_32_UNSIGNED_INT_24_8_REV;\r\n        } else if (textureFormat === Constants.TEXTUREFORMAT_STENCIL8) {\r\n            type = gl.UNSIGNED_BYTE;\r\n        }\r\n\r\n        return type;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setupFramebufferDepthAttachments(\r\n        generateStencilBuffer: boolean,\r\n        generateDepthBuffer: boolean,\r\n        width: number,\r\n        height: number,\r\n        samples = 1,\r\n        depthTextureFormat?: number,\r\n        dontBindRenderBufferToFrameBuffer = false\r\n    ): Nullable<WebGLRenderbuffer> {\r\n        const gl = this._gl;\r\n\r\n        depthTextureFormat = depthTextureFormat ?? (generateStencilBuffer ? Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 : Constants.TEXTUREFORMAT_DEPTH32_FLOAT);\r\n\r\n        const internalFormat = this._getInternalFormatFromDepthTextureFormat(depthTextureFormat, generateDepthBuffer, generateStencilBuffer);\r\n\r\n        // Create the depth/stencil buffer\r\n        if (generateStencilBuffer && generateDepthBuffer) {\r\n            return this._createRenderBuffer(width, height, samples, gl.DEPTH_STENCIL, internalFormat, dontBindRenderBufferToFrameBuffer ? -1 : gl.DEPTH_STENCIL_ATTACHMENT);\r\n        }\r\n        if (generateDepthBuffer) {\r\n            return this._createRenderBuffer(width, height, samples, internalFormat, internalFormat, dontBindRenderBufferToFrameBuffer ? -1 : gl.DEPTH_ATTACHMENT);\r\n        }\r\n        if (generateStencilBuffer) {\r\n            return this._createRenderBuffer(width, height, samples, internalFormat, internalFormat, dontBindRenderBufferToFrameBuffer ? -1 : gl.STENCIL_ATTACHMENT);\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _createRenderBuffer(\r\n        width: number,\r\n        height: number,\r\n        samples: number,\r\n        internalFormat: number,\r\n        msInternalFormat: number,\r\n        attachment: number,\r\n        unbindBuffer = true\r\n    ): Nullable<WebGLRenderbuffer> {\r\n        const gl = this._gl;\r\n        const renderBuffer = gl.createRenderbuffer();\r\n        return this._updateRenderBuffer(renderBuffer, width, height, samples, internalFormat, msInternalFormat, attachment, unbindBuffer);\r\n    }\r\n\r\n    public _updateRenderBuffer(\r\n        renderBuffer: Nullable<WebGLRenderbuffer>,\r\n        width: number,\r\n        height: number,\r\n        samples: number,\r\n        internalFormat: number,\r\n        msInternalFormat: number,\r\n        attachment: number,\r\n        unbindBuffer = true\r\n    ): Nullable<WebGLRenderbuffer> {\r\n        const gl = this._gl;\r\n\r\n        gl.bindRenderbuffer(gl.RENDERBUFFER, renderBuffer);\r\n\r\n        if (samples > 1 && gl.renderbufferStorageMultisample) {\r\n            gl.renderbufferStorageMultisample(gl.RENDERBUFFER, samples, msInternalFormat, width, height);\r\n        } else {\r\n            gl.renderbufferStorage(gl.RENDERBUFFER, internalFormat, width, height);\r\n        }\r\n\r\n        if (attachment !== -1) {\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, attachment, gl.RENDERBUFFER, renderBuffer);\r\n        }\r\n\r\n        if (unbindBuffer) {\r\n            gl.bindRenderbuffer(gl.RENDERBUFFER, null);\r\n        }\r\n\r\n        return renderBuffer;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _releaseTexture(texture: InternalTexture): void {\r\n        this._deleteTexture(texture._hardwareTexture as Nullable<WebGLHardwareTexture>);\r\n\r\n        // Unbind channels\r\n        this.unbindAllTextures();\r\n\r\n        const index = this._internalTexturesCache.indexOf(texture);\r\n        if (index !== -1) {\r\n            this._internalTexturesCache.splice(index, 1);\r\n        }\r\n\r\n        // Integrated fixed lod samplers.\r\n        if (texture._lodTextureHigh) {\r\n            texture._lodTextureHigh.dispose();\r\n        }\r\n        if (texture._lodTextureMid) {\r\n            texture._lodTextureMid.dispose();\r\n        }\r\n        if (texture._lodTextureLow) {\r\n            texture._lodTextureLow.dispose();\r\n        }\r\n\r\n        // Integrated irradiance map.\r\n        if (texture._irradianceTexture) {\r\n            texture._irradianceTexture.dispose();\r\n        }\r\n    }\r\n\r\n    protected _deleteTexture(texture: Nullable<WebGLHardwareTexture>): void {\r\n        texture?.release();\r\n    }\r\n\r\n    protected _setProgram(program: Nullable<WebGLProgram>): void {\r\n        if (this._currentProgram !== program) {\r\n            _setProgram(program, this._gl);\r\n            this._currentProgram = program;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _boundUniforms: { [key: number]: WebGLUniformLocation } = {};\r\n\r\n    /**\r\n     * Binds an effect to the webGL context\r\n     * @param effect defines the effect to bind\r\n     */\r\n    public bindSamplers(effect: Effect): void {\r\n        const webGLPipelineContext = effect.getPipelineContext() as WebGLPipelineContext;\r\n        this._setProgram(webGLPipelineContext.program);\r\n        const samplers = effect.getSamplers();\r\n        for (let index = 0; index < samplers.length; index++) {\r\n            const uniform = effect.getUniform(samplers[index]);\r\n\r\n            if (uniform) {\r\n                this._boundUniforms[index] = uniform;\r\n            }\r\n        }\r\n        this._currentEffect = null;\r\n    }\r\n\r\n    private _activateCurrentTexture() {\r\n        if (this._currentTextureChannel !== this._activeChannel) {\r\n            this._gl.activeTexture(this._gl.TEXTURE0 + this._activeChannel);\r\n            this._currentTextureChannel = this._activeChannel;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTextureDirectly(target: number, texture: Nullable<InternalTexture>, forTextureDataUpdate = false, force = false): boolean {\r\n        let wasPreviouslyBound = false;\r\n        const isTextureForRendering = texture && texture._associatedChannel > -1;\r\n        if (forTextureDataUpdate && isTextureForRendering) {\r\n            this._activeChannel = texture._associatedChannel;\r\n        }\r\n\r\n        const currentTextureBound = this._boundTexturesCache[this._activeChannel];\r\n\r\n        if (currentTextureBound !== texture || force) {\r\n            this._activateCurrentTexture();\r\n\r\n            if (texture && texture.isMultiview) {\r\n                //this._gl.bindTexture(target, texture ? texture._colorTextureArray : null);\r\n                Logger.Error([\"_bindTextureDirectly called with a multiview texture!\", target, texture]);\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"_bindTextureDirectly called with a multiview texture!\";\r\n            } else {\r\n                this._gl.bindTexture(target, texture?._hardwareTexture?.underlyingResource ?? null);\r\n            }\r\n\r\n            this._boundTexturesCache[this._activeChannel] = texture;\r\n\r\n            if (texture) {\r\n                texture._associatedChannel = this._activeChannel;\r\n            }\r\n        } else if (forTextureDataUpdate) {\r\n            wasPreviouslyBound = true;\r\n            this._activateCurrentTexture();\r\n        }\r\n\r\n        if (isTextureForRendering && !forTextureDataUpdate) {\r\n            this._bindSamplerUniformToChannel(texture._associatedChannel, this._activeChannel);\r\n        }\r\n\r\n        return wasPreviouslyBound;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _bindTexture(channel: number, texture: Nullable<InternalTexture>, name: string): void {\r\n        if (channel === undefined) {\r\n            return;\r\n        }\r\n\r\n        if (texture) {\r\n            texture._associatedChannel = channel;\r\n        }\r\n\r\n        this._activeChannel = channel;\r\n        const target = texture ? this._getTextureTarget(texture) : this._gl.TEXTURE_2D;\r\n        this._bindTextureDirectly(target, texture);\r\n    }\r\n\r\n    /**\r\n     * Unbind all textures from the webGL context\r\n     */\r\n    public unbindAllTextures(): void {\r\n        for (let channel = 0; channel < this._maxSimultaneousTextures; channel++) {\r\n            this._activeChannel = channel;\r\n            this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n            this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, null);\r\n            if (this.webGLVersion > 1) {\r\n                this._bindTextureDirectly(this._gl.TEXTURE_3D, null);\r\n                this._bindTextureDirectly(this._gl.TEXTURE_2D_ARRAY, null);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a texture to the according uniform.\r\n     * @param channel The texture channel\r\n     * @param uniform The uniform to set\r\n     * @param texture The texture to apply\r\n     * @param name The name of the uniform in the effect\r\n     */\r\n    public setTexture(channel: number, uniform: Nullable<WebGLUniformLocation>, texture: Nullable<ThinTexture>, name: string): void {\r\n        if (channel === undefined) {\r\n            return;\r\n        }\r\n\r\n        if (uniform) {\r\n            this._boundUniforms[channel] = uniform;\r\n        }\r\n\r\n        this._setTexture(channel, texture);\r\n    }\r\n\r\n    private _bindSamplerUniformToChannel(sourceSlot: number, destination: number) {\r\n        const uniform = this._boundUniforms[sourceSlot];\r\n        if (!uniform || uniform._currentState === destination) {\r\n            return;\r\n        }\r\n        this._gl.uniform1i(uniform, destination);\r\n        uniform._currentState = destination;\r\n    }\r\n\r\n    private _getTextureWrapMode(mode: number): number {\r\n        switch (mode) {\r\n            case Constants.TEXTURE_WRAP_ADDRESSMODE:\r\n                return this._gl.REPEAT;\r\n            case Constants.TEXTURE_CLAMP_ADDRESSMODE:\r\n                return this._gl.CLAMP_TO_EDGE;\r\n            case Constants.TEXTURE_MIRROR_ADDRESSMODE:\r\n                return this._gl.MIRRORED_REPEAT;\r\n        }\r\n        return this._gl.REPEAT;\r\n    }\r\n\r\n    public override _setTexture(channel: number, texture: Nullable<ThinTexture>, isPartOfTextureArray = false, depthStencilTexture = false, name = \"\"): boolean {\r\n        // Not ready?\r\n        if (!texture) {\r\n            if (this._boundTexturesCache[channel] != null) {\r\n                this._activeChannel = channel;\r\n                this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n                this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, null);\r\n                if (this.webGLVersion > 1) {\r\n                    this._bindTextureDirectly(this._gl.TEXTURE_3D, null);\r\n                    this._bindTextureDirectly(this._gl.TEXTURE_2D_ARRAY, null);\r\n                }\r\n            }\r\n            return false;\r\n        }\r\n\r\n        // Video\r\n        if ((<VideoTexture>texture).video) {\r\n            this._activeChannel = channel;\r\n            const videoInternalTexture = (<VideoTexture>texture).getInternalTexture();\r\n            if (videoInternalTexture) {\r\n                videoInternalTexture._associatedChannel = channel;\r\n            }\r\n            (<VideoTexture>texture).update();\r\n        } else if (texture.delayLoadState === Constants.DELAYLOADSTATE_NOTLOADED) {\r\n            // Delay loading\r\n            texture.delayLoad();\r\n            return false;\r\n        }\r\n\r\n        let internalTexture: InternalTexture;\r\n        if (depthStencilTexture) {\r\n            internalTexture = (<RenderTargetTexture>texture).depthStencilTexture!;\r\n        } else if (texture.isReady()) {\r\n            internalTexture = <InternalTexture>texture.getInternalTexture();\r\n        } else if (texture.isCube) {\r\n            internalTexture = this.emptyCubeTexture;\r\n        } else if (texture.is3D) {\r\n            internalTexture = this.emptyTexture3D;\r\n        } else if (texture.is2DArray) {\r\n            internalTexture = this.emptyTexture2DArray;\r\n        } else {\r\n            internalTexture = this.emptyTexture;\r\n        }\r\n\r\n        if (!isPartOfTextureArray && internalTexture) {\r\n            internalTexture._associatedChannel = channel;\r\n        }\r\n\r\n        let needToBind = true;\r\n        if (this._boundTexturesCache[channel] === internalTexture) {\r\n            if (!isPartOfTextureArray) {\r\n                this._bindSamplerUniformToChannel(internalTexture._associatedChannel, channel);\r\n            }\r\n\r\n            needToBind = false;\r\n        }\r\n\r\n        this._activeChannel = channel;\r\n        const target = this._getTextureTarget(internalTexture);\r\n        if (needToBind) {\r\n            this._bindTextureDirectly(target, internalTexture, isPartOfTextureArray);\r\n        }\r\n\r\n        if (internalTexture && !internalTexture.isMultiview) {\r\n            // CUBIC_MODE and SKYBOX_MODE both require CLAMP_TO_EDGE.  All other modes use REPEAT.\r\n            if (internalTexture.isCube && internalTexture._cachedCoordinatesMode !== texture.coordinatesMode) {\r\n                internalTexture._cachedCoordinatesMode = texture.coordinatesMode;\r\n\r\n                const textureWrapMode =\r\n                    texture.coordinatesMode !== Constants.TEXTURE_CUBIC_MODE && texture.coordinatesMode !== Constants.TEXTURE_SKYBOX_MODE\r\n                        ? Constants.TEXTURE_WRAP_ADDRESSMODE\r\n                        : Constants.TEXTURE_CLAMP_ADDRESSMODE;\r\n                texture.wrapU = textureWrapMode;\r\n                texture.wrapV = textureWrapMode;\r\n            }\r\n\r\n            if (internalTexture._cachedWrapU !== texture.wrapU) {\r\n                internalTexture._cachedWrapU = texture.wrapU;\r\n                this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_S, this._getTextureWrapMode(texture.wrapU), internalTexture);\r\n            }\r\n\r\n            if (internalTexture._cachedWrapV !== texture.wrapV) {\r\n                internalTexture._cachedWrapV = texture.wrapV;\r\n                this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_T, this._getTextureWrapMode(texture.wrapV), internalTexture);\r\n            }\r\n\r\n            if (internalTexture.is3D && internalTexture._cachedWrapR !== texture.wrapR) {\r\n                internalTexture._cachedWrapR = texture.wrapR;\r\n                this._setTextureParameterInteger(target, this._gl.TEXTURE_WRAP_R, this._getTextureWrapMode(texture.wrapR), internalTexture);\r\n            }\r\n\r\n            this._setAnisotropicLevel(target, internalTexture, texture.anisotropicFilteringLevel);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Sets an array of texture to the webGL context\r\n     * @param channel defines the channel where the texture array must be set\r\n     * @param uniform defines the associated uniform location\r\n     * @param textures defines the array of textures to bind\r\n     * @param name name of the channel\r\n     */\r\n    public setTextureArray(channel: number, uniform: Nullable<WebGLUniformLocation>, textures: ThinTexture[], name: string): void {\r\n        if (channel === undefined || !uniform) {\r\n            return;\r\n        }\r\n\r\n        if (!this._textureUnits || this._textureUnits.length !== textures.length) {\r\n            this._textureUnits = new Int32Array(textures.length);\r\n        }\r\n        for (let i = 0; i < textures.length; i++) {\r\n            const texture = textures[i].getInternalTexture();\r\n\r\n            if (texture) {\r\n                this._textureUnits[i] = channel + i;\r\n                texture._associatedChannel = channel + i;\r\n            } else {\r\n                this._textureUnits[i] = -1;\r\n            }\r\n        }\r\n        this._gl.uniform1iv(uniform, this._textureUnits);\r\n\r\n        for (let index = 0; index < textures.length; index++) {\r\n            this._setTexture(this._textureUnits[index], textures[index], true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _setAnisotropicLevel(target: number, internalTexture: InternalTexture, anisotropicFilteringLevel: number) {\r\n        const anisotropicFilterExtension = this._caps.textureAnisotropicFilterExtension;\r\n        if (\r\n            internalTexture.samplingMode !== Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST &&\r\n            internalTexture.samplingMode !== Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR &&\r\n            internalTexture.samplingMode !== Constants.TEXTURE_LINEAR_LINEAR\r\n        ) {\r\n            anisotropicFilteringLevel = 1; // Forcing the anisotropic to 1 because else webgl will force filters to linear\r\n        }\r\n\r\n        if (anisotropicFilterExtension && internalTexture._cachedAnisotropicFilteringLevel !== anisotropicFilteringLevel) {\r\n            this._setTextureParameterFloat(\r\n                target,\r\n                anisotropicFilterExtension.TEXTURE_MAX_ANISOTROPY_EXT,\r\n                Math.min(anisotropicFilteringLevel, this._caps.maxAnisotropy),\r\n                internalTexture\r\n            );\r\n            internalTexture._cachedAnisotropicFilteringLevel = anisotropicFilteringLevel;\r\n        }\r\n    }\r\n\r\n    private _setTextureParameterFloat(target: number, parameter: number, value: number, texture: InternalTexture): void {\r\n        this._bindTextureDirectly(target, texture, true, true);\r\n        this._gl.texParameterf(target, parameter, value);\r\n    }\r\n\r\n    private _setTextureParameterInteger(target: number, parameter: number, value: number, texture?: InternalTexture) {\r\n        if (texture) {\r\n            this._bindTextureDirectly(target, texture, true, true);\r\n        }\r\n        this._gl.texParameteri(target, parameter, value);\r\n    }\r\n\r\n    /**\r\n     * Unbind all vertex attributes from the webGL context\r\n     */\r\n    public unbindAllAttributes() {\r\n        if (this._mustWipeVertexAttributes) {\r\n            this._mustWipeVertexAttributes = false;\r\n\r\n            for (let i = 0; i < this._caps.maxVertexAttribs; i++) {\r\n                this.disableAttributeByIndex(i);\r\n            }\r\n            return;\r\n        }\r\n\r\n        for (let i = 0, ul = this._vertexAttribArraysEnabled.length; i < ul; i++) {\r\n            if (i >= this._caps.maxVertexAttribs || !this._vertexAttribArraysEnabled[i]) {\r\n                continue;\r\n            }\r\n\r\n            this.disableAttributeByIndex(i);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Force the engine to release all cached effects. This means that next effect compilation will have to be done completely even if a similar effect was already compiled\r\n     */\r\n    public releaseEffects() {\r\n        this._compiledEffects = {};\r\n        this.onReleaseEffectsObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Dispose and release all associated resources\r\n     */\r\n    public override dispose(): void {\r\n        // Events\r\n        if (IsWindowObjectExist()) {\r\n            if (this._renderingCanvas) {\r\n                this._renderingCanvas.removeEventListener(\"webglcontextlost\", this._onContextLost);\r\n\r\n                if (this._onContextRestored) {\r\n                    this._renderingCanvas.removeEventListener(\"webglcontextrestored\", this._onContextRestored);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Should not be moved up of renderingCanvas will be null.\r\n        super.dispose();\r\n\r\n        if (this._dummyFramebuffer) {\r\n            this._gl.deleteFramebuffer(this._dummyFramebuffer);\r\n        }\r\n\r\n        // Unbind\r\n        this.unbindAllAttributes();\r\n        this._boundUniforms = {};\r\n\r\n        this._workingCanvas = null;\r\n        this._workingContext = null;\r\n        this._currentBufferPointers.length = 0;\r\n        this._currentProgram = null;\r\n\r\n        if ((this._creationOptions as EngineOptions).loseContextOnDispose) {\r\n            this._gl.getExtension(\"WEBGL_lose_context\")?.loseContext();\r\n        }\r\n        // clear the state object\r\n        deleteStateObject(this._gl);\r\n    }\r\n\r\n    /**\r\n     * Attach a new callback raised when context lost event is fired\r\n     * @param callback defines the callback to call\r\n     */\r\n    public attachContextLostEvent(callback: (event: WebGLContextEvent) => void): void {\r\n        if (this._renderingCanvas) {\r\n            this._renderingCanvas.addEventListener(\"webglcontextlost\", <any>callback, false);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attach a new callback raised when context restored event is fired\r\n     * @param callback defines the callback to call\r\n     */\r\n    public attachContextRestoredEvent(callback: (event: WebGLContextEvent) => void): void {\r\n        if (this._renderingCanvas) {\r\n            this._renderingCanvas.addEventListener(\"webglcontextrestored\", <any>callback, false);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the current error code of the webGL context\r\n     * @returns the error code\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebGLRenderingContext/getError\r\n     */\r\n    public getError(): number {\r\n        return this._gl.getError();\r\n    }\r\n\r\n    private _canRenderToFloatFramebuffer(): boolean {\r\n        if (this._webGLVersion > 1) {\r\n            return this._caps.colorBufferFloat;\r\n        }\r\n        return this._canRenderToFramebuffer(Constants.TEXTURETYPE_FLOAT);\r\n    }\r\n\r\n    private _canRenderToHalfFloatFramebuffer(): boolean {\r\n        if (this._webGLVersion > 1) {\r\n            return this._caps.colorBufferFloat;\r\n        }\r\n        return this._canRenderToFramebuffer(Constants.TEXTURETYPE_HALF_FLOAT);\r\n    }\r\n\r\n    // Thank you : http://stackoverflow.com/questions/28827511/webgl-ios-render-to-floating-point-texture\r\n    private _canRenderToFramebuffer(type: number): boolean {\r\n        const gl = this._gl;\r\n\r\n        //clear existing errors\r\n        // eslint-disable-next-line no-empty\r\n        while (gl.getError() !== gl.NO_ERROR) {}\r\n\r\n        let successful = true;\r\n\r\n        const texture = gl.createTexture();\r\n        gl.bindTexture(gl.TEXTURE_2D, texture);\r\n        gl.texImage2D(gl.TEXTURE_2D, 0, this._getRGBABufferInternalSizedFormat(type), 1, 1, 0, gl.RGBA, this._getWebGLTextureType(type), null);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\r\n\r\n        const fb = gl.createFramebuffer();\r\n        gl.bindFramebuffer(gl.FRAMEBUFFER, fb);\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0);\r\n        const status = gl.checkFramebufferStatus(gl.FRAMEBUFFER);\r\n\r\n        successful = successful && status === gl.FRAMEBUFFER_COMPLETE;\r\n        successful = successful && gl.getError() === gl.NO_ERROR;\r\n\r\n        //try render by clearing frame buffer's color buffer\r\n        if (successful) {\r\n            gl.clear(gl.COLOR_BUFFER_BIT);\r\n            successful = successful && gl.getError() === gl.NO_ERROR;\r\n        }\r\n\r\n        //try reading from frame to ensure render occurs (just creating the FBO is not sufficient to determine if rendering is supported)\r\n        if (successful) {\r\n            //in practice it's sufficient to just read from the backbuffer rather than handle potentially issues reading from the texture\r\n            gl.bindFramebuffer(gl.FRAMEBUFFER, null);\r\n            const readFormat = gl.RGBA;\r\n            const readType = gl.UNSIGNED_BYTE;\r\n            const buffer = new Uint8Array(4);\r\n            gl.readPixels(0, 0, 1, 1, readFormat, readType, buffer);\r\n            successful = successful && gl.getError() === gl.NO_ERROR;\r\n        }\r\n\r\n        //clean up\r\n        gl.deleteTexture(texture);\r\n        gl.deleteFramebuffer(fb);\r\n        gl.bindFramebuffer(gl.FRAMEBUFFER, null);\r\n\r\n        //clear accumulated errors\r\n        // eslint-disable-next-line no-empty\r\n        while (!successful && gl.getError() !== gl.NO_ERROR) {}\r\n\r\n        return successful;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getWebGLTextureType(type: number): number {\r\n        if (this._webGLVersion === 1) {\r\n            switch (type) {\r\n                case Constants.TEXTURETYPE_FLOAT:\r\n                    return this._gl.FLOAT;\r\n                case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                    return this._gl.HALF_FLOAT_OES;\r\n                case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                    return this._gl.UNSIGNED_BYTE;\r\n                case Constants.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4:\r\n                    return this._gl.UNSIGNED_SHORT_4_4_4_4;\r\n                case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1:\r\n                    return this._gl.UNSIGNED_SHORT_5_5_5_1;\r\n                case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_6_5:\r\n                    return this._gl.UNSIGNED_SHORT_5_6_5;\r\n            }\r\n            return this._gl.UNSIGNED_BYTE;\r\n        }\r\n\r\n        switch (type) {\r\n            case Constants.TEXTURETYPE_BYTE:\r\n                return this._gl.BYTE;\r\n            case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                return this._gl.UNSIGNED_BYTE;\r\n            case Constants.TEXTURETYPE_SHORT:\r\n                return this._gl.SHORT;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT:\r\n                return this._gl.UNSIGNED_SHORT;\r\n            case Constants.TEXTURETYPE_INT:\r\n                return this._gl.INT;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INTEGER: // Refers to UNSIGNED_INT\r\n                return this._gl.UNSIGNED_INT;\r\n            case Constants.TEXTURETYPE_FLOAT:\r\n                return this._gl.FLOAT;\r\n            case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                return this._gl.HALF_FLOAT;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4:\r\n                return this._gl.UNSIGNED_SHORT_4_4_4_4;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1:\r\n                return this._gl.UNSIGNED_SHORT_5_5_5_1;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_6_5:\r\n                return this._gl.UNSIGNED_SHORT_5_6_5;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV:\r\n                return this._gl.UNSIGNED_INT_2_10_10_10_REV;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_24_8:\r\n                return this._gl.UNSIGNED_INT_24_8;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV:\r\n                return this._gl.UNSIGNED_INT_10F_11F_11F_REV;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV:\r\n                return this._gl.UNSIGNED_INT_5_9_9_9_REV;\r\n            case Constants.TEXTURETYPE_FLOAT_32_UNSIGNED_INT_24_8_REV:\r\n                return this._gl.FLOAT_32_UNSIGNED_INT_24_8_REV;\r\n        }\r\n\r\n        return this._gl.UNSIGNED_BYTE;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getInternalFormat(format: number, useSRGBBuffer = false): number {\r\n        let internalFormat: GLenum = useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : this._gl.RGBA;\r\n\r\n        switch (format) {\r\n            case Constants.TEXTUREFORMAT_ALPHA:\r\n                internalFormat = this._gl.ALPHA;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_LUMINANCE:\r\n                internalFormat = this._gl.LUMINANCE;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_LUMINANCE_ALPHA:\r\n                internalFormat = this._gl.LUMINANCE_ALPHA;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_RED:\r\n            case Constants.TEXTUREFORMAT_R16_UNORM:\r\n            case Constants.TEXTUREFORMAT_R16_SNORM:\r\n                internalFormat = this._gl.RED;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_RG:\r\n            case Constants.TEXTUREFORMAT_RG16_UNORM:\r\n            case Constants.TEXTUREFORMAT_RG16_SNORM:\r\n                internalFormat = this._gl.RG;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_RGB:\r\n            case Constants.TEXTUREFORMAT_RGB16_UNORM:\r\n            case Constants.TEXTUREFORMAT_RGB16_SNORM:\r\n                internalFormat = useSRGBBuffer ? this._glSRGBExtensionValues.SRGB : this._gl.RGB;\r\n                break;\r\n            case Constants.TEXTUREFORMAT_RGBA:\r\n            case Constants.TEXTUREFORMAT_RGBA16_UNORM:\r\n            case Constants.TEXTUREFORMAT_RGBA16_SNORM:\r\n                internalFormat = useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : this._gl.RGBA;\r\n                break;\r\n        }\r\n\r\n        if (this._webGLVersion > 1) {\r\n            switch (format) {\r\n                case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                    internalFormat = this._gl.RED_INTEGER;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                    internalFormat = this._gl.RG_INTEGER;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                    internalFormat = this._gl.RGB_INTEGER;\r\n                    break;\r\n                case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                    internalFormat = this._gl.RGBA_INTEGER;\r\n                    break;\r\n            }\r\n        }\r\n\r\n        return internalFormat;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getRGBABufferInternalSizedFormat(type: number, format?: number, useSRGBBuffer = false): number {\r\n        if (this._webGLVersion === 1) {\r\n            if (format !== undefined) {\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_ALPHA:\r\n                        return this._gl.ALPHA;\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE:\r\n                        return this._gl.LUMINANCE;\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE_ALPHA:\r\n                        return this._gl.LUMINANCE_ALPHA;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return useSRGBBuffer ? this._glSRGBExtensionValues.SRGB : this._gl.RGB;\r\n                }\r\n            }\r\n            return this._gl.RGBA;\r\n        }\r\n\r\n        switch (type) {\r\n            case Constants.TEXTURETYPE_BYTE:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return this._gl.R8_SNORM;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return this._gl.RG8_SNORM;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return this._gl.RGB8_SNORM;\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R8I;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG8I;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB8I;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA8I;\r\n                    default:\r\n                        return this._gl.RGBA8_SNORM;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return this._gl.R8;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return this._gl.RG8;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8 : this._gl.RGB8; // By default. Other possibilities are RGB565, SRGB8.\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : this._gl.RGBA8; // By default. Other possibilities are RGB5_A1, RGBA4, SRGB8_ALPHA8.\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R8UI;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG8UI;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB8UI;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA8UI;\r\n                    case Constants.TEXTUREFORMAT_ALPHA:\r\n                        return this._gl.ALPHA;\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE:\r\n                        return this._gl.LUMINANCE;\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE_ALPHA:\r\n                        return this._gl.LUMINANCE_ALPHA;\r\n                    default:\r\n                        return this._gl.RGBA8;\r\n                }\r\n            case Constants.TEXTURETYPE_SHORT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R16I;\r\n                    case Constants.TEXTUREFORMAT_R16_SNORM:\r\n                        return this._gl.R16_SNORM_EXT;\r\n                    case Constants.TEXTUREFORMAT_RG16_SNORM:\r\n                        return this._gl.RG16_SNORM_EXT;\r\n                    case Constants.TEXTUREFORMAT_RGB16_SNORM:\r\n                        return this._gl.RGB16_SNORM_EXT;\r\n                    case Constants.TEXTUREFORMAT_RGBA16_SNORM:\r\n                        return this._gl.RGBA16_SNORM_EXT;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG16I;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB16I;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA16I;\r\n                    default:\r\n                        return this._gl.RGBA16I;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R16UI;\r\n                    case Constants.TEXTUREFORMAT_R16_UNORM:\r\n                        return this._gl.R16_EXT;\r\n                    case Constants.TEXTUREFORMAT_RG16_UNORM:\r\n                        return this._gl.RG16_EXT;\r\n                    case Constants.TEXTUREFORMAT_RGB16_UNORM:\r\n                        return this._gl.RGB16_EXT;\r\n                    case Constants.TEXTUREFORMAT_RGBA16_UNORM:\r\n                        return this._gl.RGBA16_EXT;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG16UI;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB16UI;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA16UI;\r\n                    default:\r\n                        return this._gl.RGBA16UI;\r\n                }\r\n            case Constants.TEXTURETYPE_INT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R32I;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG32I;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB32I;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA32I;\r\n                    default:\r\n                        return this._gl.RGBA32I;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_INTEGER: // Refers to UNSIGNED_INT\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return this._gl.R32UI;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return this._gl.RG32UI;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        return this._gl.RGB32UI;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGBA32UI;\r\n                    default:\r\n                        return this._gl.RGBA32UI;\r\n                }\r\n            case Constants.TEXTURETYPE_FLOAT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return this._gl.R32F; // By default. Other possibility is R16F.\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return this._gl.RG32F; // By default. Other possibility is RG16F.\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return this._gl.RGB32F; // By default. Other possibilities are RGB16F, R11F_G11F_B10F, RGB9_E5.\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return this._gl.RGBA32F; // By default. Other possibility is RGBA16F.\r\n                    default:\r\n                        return this._gl.RGBA32F;\r\n                }\r\n            case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return this._gl.R16F;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return this._gl.RG16F;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        return this._gl.RGB16F; // By default. Other possibilities are R11F_G11F_B10F, RGB9_E5.\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return this._gl.RGBA16F;\r\n                    default:\r\n                        return this._gl.RGBA16F;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_6_5:\r\n                return this._gl.RGB565;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV:\r\n                return this._gl.R11F_G11F_B10F;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV:\r\n                return this._gl.RGB9_E5;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4:\r\n                return this._gl.RGBA4;\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1:\r\n                return this._gl.RGB5_A1;\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return this._gl.RGB10_A2; // By default. Other possibility is RGB5_A1.\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return this._gl.RGB10_A2UI;\r\n                    default:\r\n                        return this._gl.RGB10_A2;\r\n                }\r\n        }\r\n\r\n        return useSRGBBuffer ? this._glSRGBExtensionValues.SRGB8_ALPHA8 : this._gl.RGBA8;\r\n    }\r\n\r\n    /**\r\n     * Reads pixels from the current frame buffer. Please note that this function can be slow\r\n     * @param x defines the x coordinate of the rectangle where pixels must be read\r\n     * @param y defines the y coordinate of the rectangle where pixels must be read\r\n     * @param width defines the width of the rectangle where pixels must be read\r\n     * @param height defines the height of the rectangle where pixels must be read\r\n     * @param hasAlpha defines whether the output should have alpha or not (defaults to true)\r\n     * @param flushRenderer true to flush the renderer from the pending commands before reading the pixels\r\n     * @param data defines the data to fill with the read pixels (if not provided, a new one will be created)\r\n     * @returns a ArrayBufferView promise (Uint8Array) containing RGBA colors\r\n     */\r\n    // Async function, not named Async and not marked as async to avoid breaking changes\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async\r\n    public readPixels(x: number, y: number, width: number, height: number, hasAlpha = true, flushRenderer = true, data: Nullable<Uint8Array> = null): Promise<ArrayBufferView> {\r\n        const numChannels = hasAlpha ? 4 : 3;\r\n        const format = hasAlpha ? this._gl.RGBA : this._gl.RGB;\r\n\r\n        const dataLength = width * height * numChannels;\r\n        if (!data) {\r\n            data = new Uint8Array(dataLength);\r\n        } else if (data.length < dataLength) {\r\n            Logger.Error(`Data buffer is too small to store the read pixels (${data.length} should be more than ${dataLength})`);\r\n            return Promise.resolve(data);\r\n        }\r\n\r\n        if (flushRenderer) {\r\n            this.flushFramebuffer();\r\n        }\r\n        this._gl.readPixels(x, y, width, height, format, this._gl.UNSIGNED_BYTE, data);\r\n        return Promise.resolve(data);\r\n    }\r\n\r\n    // Statics\r\n\r\n    private static _IsSupported: Nullable<boolean> = null;\r\n    private static _HasMajorPerformanceCaveat: Nullable<boolean> = null;\r\n\r\n    /**\r\n     * Gets a Promise<boolean> indicating if the engine can be instantiated (ie. if a webGL context can be found)\r\n     */\r\n    // eslint-disable-next-line no-restricted-syntax\r\n    public static get IsSupportedAsync(): Promise<boolean> {\r\n        return Promise.resolve(this.isSupported());\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the engine can be instantiated (ie. if a webGL context can be found)\r\n     */\r\n    public static get IsSupported(): boolean {\r\n        return this.isSupported(); // Backward compat\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the engine can be instantiated (ie. if a webGL context can be found)\r\n     * @returns true if the engine can be created\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static isSupported(): boolean {\r\n        if (this._HasMajorPerformanceCaveat !== null) {\r\n            return !this._HasMajorPerformanceCaveat; // We know it is performant so WebGL is supported\r\n        }\r\n\r\n        if (this._IsSupported === null) {\r\n            try {\r\n                const tempcanvas = AbstractEngine._CreateCanvas(1, 1);\r\n                const gl = tempcanvas.getContext(\"webgl\") || (tempcanvas as any).getContext(\"experimental-webgl\");\r\n\r\n                this._IsSupported = gl != null && !!window.WebGLRenderingContext;\r\n            } catch (e) {\r\n                this._IsSupported = false;\r\n            }\r\n        }\r\n\r\n        return this._IsSupported;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating if the engine can be instantiated on a performant device (ie. if a webGL context can be found and it does not use a slow implementation)\r\n     */\r\n    public static get HasMajorPerformanceCaveat(): boolean {\r\n        if (this._HasMajorPerformanceCaveat === null) {\r\n            try {\r\n                const tempcanvas = AbstractEngine._CreateCanvas(1, 1);\r\n                const gl =\r\n                    tempcanvas.getContext(\"webgl\", { failIfMajorPerformanceCaveat: true }) ||\r\n                    (tempcanvas as any).getContext(\"experimental-webgl\", { failIfMajorPerformanceCaveat: true });\r\n\r\n                this._HasMajorPerformanceCaveat = !gl;\r\n            } catch (e) {\r\n                this._HasMajorPerformanceCaveat = false;\r\n            }\r\n        }\r\n\r\n        return this._HasMajorPerformanceCaveat;\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ninterface TexImageParameters {\r\n    internalFormat: number;\r\n    format: number;\r\n    type: number;\r\n}\r\n"], "names": [], "mappings": ";;;AAuBA,OAAO,EACH,qBAAqB,EACrB,sBAAsB,EACtB,mBAAmB,EACnB,wBAAwB,EACxB,uBAAuB,EACvB,WAAW,EACX,oCAAoC,EACpC,cAAc,EACd,oBAAoB,EACpB,iBAAiB,EACjB,yBAAyB,GAC5B,MAAM,wBAAwB,CAAC;AAIhC,OAAO,EAAE,SAAS,EAAE,MAAM,oCAAoC,CAAC;AAC/D,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AACrE,OAAO,EAAE,qBAAqB,EAAE,MAAM,gCAAgC,CAAC;AACvE,OAAO,EAAE,eAAe,EAAE,MAAM,iCAAiC,CAAC;AAClE,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,OAAO,EAAE,oBAAoB,EAAE,MAAM,8BAA8B,CAAC;AAEpE,OAAO,EAAE,eAAe,EAAyB,MAAM,uCAAuC,CAAC;AAC/F,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AACnF,OAAO,EAAE,mBAAmB,EAAE,yCAAwC;AACtE,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,yDAAwD;;;;;;;;;;;;;;;;AAEnG;;GAEG,CACH,MAAM,aAAa;CASlB;AA0CK,MAAO,UAAW,yKAAQ,kBAAc;IAyB1C;;OAEG,CACH,IAAoB,IAAI,GAAA;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAoB,IAAI,CAAC,KAAa,EAAA;QAClC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,iBAAiB,GAAA;QAC/B,kKAAO,SAAM,CAAC,iBAAiB,CAAC;IACpC,CAAC;IACM,MAAM,KAAK,iBAAiB,CAAC,KAAa,EAAA;mKAC7C,SAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACrC,CAAC;IAeD;;;OAGG,CACH,IAAW,sBAAsB,GAAA;QAC7B,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;IAChE,CAAC;IAgBD;;;OAGG,CACH,IAAW,eAAe,GAAA;QACtB,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC;IAC3D,CAAC;IAsCD,IAAc,iCAAiC,GAAA;QAC3C,OAAO,KAAK,CAAC;IACjB,CAAC;IAID;;;;OAIG,CACH,IAAW,2BAA2B,CAAC,UAA6E,EAAA;QAChH,IAAI,CAAC,4BAA4B,GAAG,UAAU,CAAC;IACnD,CAAC;IAED;;OAEG,CACI,sBAAsB,GAAA;QACzB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;;;;;OAMG,CACH,YACI,eAA+G,EAC/G,SAAmB,EACnB,OAAuB,EACvB,kBAA4B,CAAA;QAE5B,OAAO,GAAG,OAAO,IAAI,CAAA,CAAE,CAAC;QACxB,KAAK,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAlJvE,cAAA,EAAgB,CACG,IAAA,CAAA,KAAK,GAAG,OAAO,CAAC;QA8BnC;;WAEG,CACI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEhC,+FAAA,EAAiG,CAC1F,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAcrC,cAAA,EAAgB,CACT,IAAA,CAAA,aAAa,GAAG,GAAG,CAAC;QA2BnB,IAAA,CAAA,0BAA0B,GAAc,EAAE,CAAC;QAG3C,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QAC/B,IAAA,CAAA,mBAAmB,GAAG,IAAI,KAAK,EAAwB,CAAC;QAClE,cAAA,EAAgB,CACT,IAAA,CAAA,mBAAmB,GAA+B,IAAI,CAAC;QAC9D,cAAA,EAAgB,CACT,IAAA,CAAA,iBAAiB,GAA+B,IAAI,CAAC;QACpD,IAAA,CAAA,sBAAsB,GAAG,IAAI,KAAK,EAAiB,CAAC;QACpD,IAAA,CAAA,yBAAyB,GAAG,IAAI,KAAK,EAAU,CAAC;QAChD,IAAA,CAAA,uBAAuB,GAAG,IAAI,KAAK,EAAc,CAAC;QAQlD,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAC7B,IAAA,CAAA,yBAAyB,GAAG,KAAK,CAAC;QAElC,IAAA,CAAA,qBAAqB,GAAG,IAAI,KAAK,EAAU,CAAC;QAC5C,IAAA,CAAA,wBAAwB,GAAG,CAAC,CAAC;QAC7B,IAAA,CAAA,uBAAuB,GAAqB,IAAI,CAAC;QA26FzD,gEAAgE;QACxD,IAAA,CAAA,kBAAkB,GAAsB,IAAI,CAAC;QAErD;;;;WAIG,CACH,gEAAgE;QACzD,IAAA,CAAA,uBAAuB,GAAG,IAAI,CAAC;QAigBtC;;WAEG,CACI,IAAA,CAAA,cAAc,GAA4C,CAAA,CAAE,CAAC;QAh5GhE,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,IAAI,MAAM,GAAgC,IAAI,CAAC;QAC/C,IAAK,eAAuB,CAAC,UAAU,EAAE,CAAC;YACtC,MAAM,GAAsB,eAAe,CAAC;YAE5C,IAAI,OAAO,CAAC,qBAAqB,KAAK,SAAS,EAAE,CAAC;gBAC9C,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;YAC1C,CAAC;YAED,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;gBACrC,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;YACjC,CAAC;YAED,aAAa;YACb,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;gBACnC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAE1B,MAAM,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC;gBAC/B,KAAK,MAAM,SAAS,IAAI,UAAU,CAAC,aAAa,CAAE,CAAC;oBAC/C,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC;oBAC1B,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;oBAClC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;oBAE9B,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;wBACjB,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,iBAAiB,EAAE,CAAC;4BACnD,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;4BAClC,MAAM,UAAU,GAAG,SAAS,CAAC,iBAAiB,CAAC;4BAE/C,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;4BAClC,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;4BAE/B,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCAChC,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gCAC5D,IAAI,aAAa,IAAI,UAAU,EAAE,CAAC;oCAC9B,SAAS;gCACb,CAAC;4BACL,CAAC;wBACL,CAAC;wBAED,KAAK,MAAM,MAAM,IAAI,OAAO,CAAE,CAAC;4BAC3B,OAAQ,MAAM,EAAE,CAAC;gCACb,KAAK,eAAe;oCAChB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;oCAClC,MAAM;gCACV,KAAK,KAAK;oCACN,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;oCACtC,MAAM;gCACV,KAAK,WAAW;oCACZ,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;oCAC1B,MAAM;gCACV,KAAK,gBAAgB;oCACjB,IAAI,CAAC,uBAAuB,GAAG,CAAC,CAAC;oCACjC,MAAM;4BACd,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,eAAe;YACf,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAChC,IAAI,CAAC,cAAc,GAAG,CAAC,GAAU,EAAE,EAAE;oBACjC,GAAG,CAAC,cAAc,EAAE,CAAC;oBACrB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;kMAC5B,oBAAA,AAAiB,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;0KAC5B,SAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;oBAEnC,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACvD,CAAC,CAAC;gBAEF,IAAI,CAAC,kBAAkB,GAAG,GAAG,EAAE;oBAC3B,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAG,CAAD,GAAK,CAAC,cAAc,EAAE,CAAC,CAAC;gBACrE,CAAC,CAAC;gBAEF,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,EAAE,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBAEhF,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,kBAAkB,CAAC;YAC5E,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,cAAc,GAAG,GAAG,EAAE;qBACvB,iMAAA,AAAiB,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAChC,CAAC,CAAC;YACN,CAAC;YAED,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YAExE,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;YACjC,CAAC;YAED,KAAK;YACL,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;gBAChC,IAAI,CAAC;oBACD,IAAI,CAAC,GAAG,GAAQ,AAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC,CAAC;oBAC5G,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;wBACX,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;wBACzB,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;wBAEpC,qDAAqD;wBACrD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;4BACxB,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;4BACzB,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;wBACxC,CAAC;oBACL,CAAC;gBACL,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;gBACT,aAAa;gBACjB,CAAC;YACL,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;gBACjE,CAAC;gBACD,IAAI,CAAC;oBACD,IAAI,CAAC,GAAG,GAA2B,AAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC,CAAC;gBACjI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;oBACT,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;gBAC3C,CAAC;YACL,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,GAA2B,eAAe,CAAC;YACnD,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAA2B,CAAC;YAE9C,IAAK,IAAI,CAAC,GAAW,CAAC,8BAA8B,EAAE,CAAC;gBACnD,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;gBACzB,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;YACxC,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC;YACxC,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,CAAC;YACnD,IAAI,UAAU,EAAE,CAAC;gBACb,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;YACzC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAEzB,wEAAwE;QACxE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEjF,IAAI,OAAO,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAC/C,IAAI,CAAC,4BAA4B,GAAG,OAAO,CAAC,sBAAsB,CAAC;QACvE,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;QAEd,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,EAAE,CAAC;QAErB,0BAA0B;QAC1B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAE,CAAC;YACnD,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,IAAI,aAAa,EAAE,CAAC;QACzD,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,sLAAI,wBAAqB,EAAE,CAAC,CAAC,CAAC,qLAAI,uBAAoB,EAAE,CAAC;QAEzG,iDAAiD;QACjD,6DAA6D;QAE7D,yCAAyC;QACzC,wCAAwC;QACxC,+BAA+B;QAC/B,QAAQ;QACR,IAAI;QAEJ,MAAM,YAAY,GAAG,CAAA,YAAA,EAAe,UAAU,CAAC,OAAO,EAAE,CAAC;8JACzD,SAAM,CAAC,GAAG,CAAC,YAAY,GAAG,CAAA,GAAA,EAAM,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAEpD,wCAAwC;QACxC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;YAC9D,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;QACpE,CAAC;QACD,MAAM,WAAW,iLAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,oDAAoD;QACpD,WAAW,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QACjE,WAAW,CAAC,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;IACzE,CAAC;IAEkB,oBAAoB,GAAA;QACnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,KAAK,CAAC,oBAAoB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG,CACI,2BAA2B,CAAC,cAA8B,EAAA;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,kBAAkB,GAAA;QACrB,IAAK,MAAM,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAE,CAAC;YACtC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpB,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,cAAc,GAAA;QACpB,OAAO;QACP,IAAI,CAAC,KAAK,GAAG;YACT,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC;YAC9E,6BAA6B,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,gCAAgC,CAAC;YAC/F,0BAA0B,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,8BAA8B,CAAC;YAC1F,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;YAChE,UAAU,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACpF,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC;YAChF,oBAAoB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC;YAC3E,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;YACpE,iBAAiB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC;YACtE,yBAAyB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC;YACvF,uBAAuB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC;YACnF,qBAAqB,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,6BAA6B,CAAC,IAAI,SAAS;YACxF,mBAAmB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,0BAA0B,CAAC,KAAK,IAAI;YACzG,aAAa,EAAE,CAAC;YAChB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,+BAA+B,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sCAAsC,CAAC;YAC7H,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,8BAA8B,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,qCAAqC,CAAC;YAC3H,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,+BAA+B,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sCAAsC,CAAC;YAC7H,gEAAgE;YAChE,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oCAAoC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,2CAA2C,CAAC;YAC5I,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gCAAgC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,uCAAuC,CAAC;YAChI,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,+BAA+B,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,sCAAsC,CAAC;YAC7H,IAAI,EACA,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,8BAA8B,CAAC,IACrD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,qCAAqC,CAAC,IAC5D,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gCAAgC,CAAC,EAAE,oCAAoC;YACjG,iCAAiC,EAC7B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gCAAgC,CAAC,IACvD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,uCAAuC,CAAC,IAC9D,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oCAAoC,CAAC;YAC/D,WAAW,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,KAAK,IAAI;YAC/F,sBAAsB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,IAAI;YAClG,4BAA4B,EAAE,KAAK;YACnC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,iCAAiC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,0BAA0B,CAAC;YACzH,qBAAqB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YAC7C,4BAA4B,EAAE,KAAK;YACnC,oBAAoB,EAAE,KAAK;YAC3B,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;YAC/F,2BAA2B,EAAE,KAAK;YAClC,wBAAwB,EAAE,KAAK;YAC/B,oBAAoB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,6BAA6B,CAAC,CAAC;YACxG,YAAY,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YACjG,gBAAgB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YAC1G,sBAAsB,EAAE,KAAK;YAC7B,2BAA2B,EAAE,KAAK;YAClC,kBAAkB,EAAE,KAAK;YACzB,+BAA+B,EAAE,KAAK;YACtC,iBAAiB,EAAE,KAAK;YACxB,eAAe,EAAE,KAAK;YACtB,UAAU,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YACpG,UAAU,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACpC,WAAW,EAAE,KAAK;YAClB,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,gBAAgB,CAAC;YAClD,eAAe,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,kBAAkB,CAAC;YAC1D,qBAAqB,EAAE,KAAK;YAC5B,kBAAkB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YAC1C,gBAAgB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YACxC,qBAAqB,EAAE,KAAK;YAC5B,kBAAkB,EAAE,KAAK;YACzB,yBAAyB,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YACjD,eAAe,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;YACvC,2BAA2B,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,GAAG;YACpH,yBAAyB,EAAE,KAAK;YAChC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;YACzE,wBAAwB,EAAE,KAAK;YAC/B,kBAAkB,EAAE,KAAK;SAC5B,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,2BAA2B,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;QACrE,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;QAElE,QAAQ;QACR,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE1D,MAAM,YAAY,GAAQ,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC;QAC7E,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC;YAC/E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,gBAAgB,CAAC;QAChF,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,kBAAkB,CAAC;QACtF,CAAC;QAED,YAAY;QACZ,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,EAAE,CAAC;YACrC,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,qCAAqC;QAC3E,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YAC9B,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,qEAAqE;QACpG,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YAC9B,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,qEAAqE;QACpG,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB,KAAK,KAAK,EAAE,CAAC;YACtC,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG,KAAK,CAAC;QACtC,CAAC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAS,IAAI,CAAC,KAAK,CAAC,UAAW,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC7F,CAAC;YACD,gDAAgD;YAChD,IAAI,CAAC,KAAK,CAAC,4BAA4B,GAAG,CAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,sBAAsB,CAAY,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1K,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,iCAAiC,GACjE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,8BAA8B,CAAC,GAClG,CAAC,CAAC;QACR,IAAI,CAAC,KAAK,CAAC,2BAA2B,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,0BAA0B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACrI,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAC9G,IAAI,CAAC,KAAK,CAAC,+BAA+B,GACtC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,AAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAErI,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YAC3B,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC;YAC3B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC;YAC7B,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,MAAM,CAAC;YAChC,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,MAAM,CAAC;YAClC,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG,MAAM,CAAC;QACvC,CAAC;QAED,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,0BAA0B,CAAC,CAAC;QAChF,IAAI,CAAC,KAAK,CAAC,wBAAwB,GAAG,qBAAqB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAE3E,IAAI,qBAAqB,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,CAAC,4BAA4B,GAAG,qBAAqB,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpH,IAAI,CAAC,GAAG,CAAC,oBAAoB,GAAG,qBAAqB,CAAC,iBAAiB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACpG,IAAI,CAAC,GAAG,CAAC,wBAAwB,GAAG,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC5G,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC5F,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,aAAa,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAC5F,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,qBAAqB,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACxF,IAAI,CAAC,GAAG,CAAC,aAAa,GAAG,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAElG,qBAAqB;QACrB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,GAAG,CAAC,oCAAoC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oCAAoC,CAAC;QACzG,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,GAAG,CAAC,oCAAoC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oCAAoC,CAAC;QACzG,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,6BAA6B,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,6BAA6B,CAAC;YAC5F,IAAI,CAAC,GAAG,CAAC,mCAAmC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,mCAAmC,CAAC;YACxG,IAAI,CAAC,GAAG,CAAC,mCAAmC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,mCAAmC,CAAC;QAC5G,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,IAAI,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC;YACvE,IAAI,CAAC,GAAG,CAAC,gCAAgC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gCAAgC,CAAC;QACjG,CAAC;QAED,kFAAkF;QAClF,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,EAAE,CAAC;gBACrC,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC;YACrC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAC3G,eAAe;QACf,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACvC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAC/I,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QACjF,CAAC,MAAM,CAAC;YACJ,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;YAEzE,IAAI,oBAAoB,KAAK,IAAI,EAAE,CAAC;gBAChC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACvC,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACxF,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,CAAC;gBAC9F,IAAI,CAAC,GAAG,CAAC,gBAAwB,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;gBAE1D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC;oBACpB,IAAI,CAAC,GAAI,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAS,oBAAqB,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;gBACxH,CAAC;YACL,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAC5C,CAAC,MAAM,CAAC;YACJ,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;YAE3E,IAAI,qBAAqB,IAAI,IAAI,EAAE,CAAC;gBAChC,IAAI,CAAC,KAAK,CAAC,qBAAqB,GAAG,IAAI,CAAC;gBACxC,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,qBAAqB,CAAC,uBAAuB,CAAC;YAC/E,CAAC;QACL,CAAC;QAED,sBAAsB;QACtB,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC;QACzC,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;QACxC,CAAC,MAAM,CAAC;YACJ,MAAM,0BAA0B,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,yBAAyB,CAAC,CAAC;YAEpF,IAAI,0BAA0B,IAAI,IAAI,EAAE,CAAC;gBACrC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBACpC,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,0BAA0B,CAAC,oBAAoB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBAC9G,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,0BAA0B,CAAC,kBAAkB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBAC1G,IAAI,CAAC,GAAG,CAAC,iBAAiB,GAAG,0BAA0B,CAAC,oBAAoB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAClH,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC;QACtC,CAAC,MAAM,CAAC;YACJ,MAAM,iBAAiB,GAA2B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;YAElG,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;gBAC5B,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC;gBAClC,IAAI,CAAC,GAAG,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAClG,IAAI,CAAC,GAAG,CAAC,qBAAqB,GAAG,iBAAiB,CAAC,0BAA0B,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACtG,IAAI,CAAC,GAAG,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACtG,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC;YACvC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,wBAAwB,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACnG,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEvG,IAAI,WAAW,IAAI,aAAa,EAAE,CAAC;gBAC/B,IAAI,CAAC,KAAK,CAAC,4BAA4B,GAAG,WAAW,CAAC,SAAS,KAAK,CAAC,IAAI,aAAa,CAAC,SAAS,KAAK,CAAC,CAAC;YAC3G,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;QAClC,CAAC,MAAM,CAAC;YACJ,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;YACvE,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;gBAC/B,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;gBAC9B,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,oBAAoB,CAAC,OAA4C,CAAC;gBACjF,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,oBAAoB,CAAC,OAA4C,CAAC;YACrF,CAAC;QACL,CAAC;QAED,eAAe;QACf,6EAA6E;QAC7E,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YACjC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBACrC,IAAI,CAAC,sBAAsB,GAAG;oBAC1B,IAAI,EAAE,sBAAsB,CAAC,IAAI;oBACjC,KAAK,EAAE,sBAAsB,CAAC,KAAK;oBACnC,YAAY,EAAE,sBAAsB,CAAC,YAAY;iBACpD,CAAC;YACN,CAAC,MAAM,CAAC;gBACJ,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAExD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;oBACxB,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBACrC,IAAI,CAAC,sBAAsB,GAAG;wBAC1B,IAAI,EAAE,aAAa,CAAC,QAA8C;wBAClE,KAAK,EAAE,aAAa,CAAC,cAAkF;wBACvG,YAAY,EAAE,aAAa,CAAC,cAA4D;qBAC3F,CAAC;gBACN,CAAC;YACL,CAAC;YACD,kEAAkE;YAClE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM,2BAA2B,GAAI,IAAI,CAAC,gBAAkC,CAAC,2BAA2B,CAAC;gBACzG,IAAI,2BAA2B,KAAK,SAAS,EAAE,CAAC;oBAC5C,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,2BAA2B,CAAC;gBACjG,CAAC;YACL,CAAC;QACL,CAAC;QAED,eAAe;QACf,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;QACzC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QACpD,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC;QAEzC,eAAe;QACf,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC;QACzE,IAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,wBAAwB,EAAE,IAAI,EAAE,CAAE,CAAC;YAC9D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;YAClC,yEAAyE;YACzE,IAAI,CAAC,KAAK,CAAC,yBAAyB,GAAG,IAAI,CAAC;QAChD,CAAC;IACL,CAAC;IAES,aAAa,GAAA;QACnB,IAAI,CAAC,SAAS,GAAG;YACb,+BAA+B,EAAE,OAAO,gBAAgB,KAAK,WAAW;YACxE,yCAAyC,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACnE,0BAA0B,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACpD,qBAAqB,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YAC/C,4BAA4B,EAAE,KAAK;YACnC,wBAAwB,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YAClD,gBAAgB,EAAE,KAAK;YACvB,4BAA4B,EAAE,KAAK;YACnC,UAAU,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACpC,aAAa,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACvC,iBAAiB,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YAC3C,+BAA+B,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACzD,WAAW,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACrC,YAAY,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACtC,iBAAiB,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YAC3C,6BAA6B,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACvD,yBAAyB,EAAE,IAAI,CAAC,aAAa,KAAK,CAAC;YACnD,sBAAsB,EAAE,IAAI;YAC5B,oBAAoB,EAAE,IAAI;YAC1B,kBAAkB,EAAE,IAAI;YACxB,sBAAsB,EAAE,KAAK;YAC7B,8BAA8B,EAAE,KAAK;YACrC,mBAAmB,EAAE,KAAK;YAC1B,uBAAuB,EAAE,IAAI;YAC7B,8CAA8C,EAAE,KAAK;YACrD,sDAAsD,EAAE,KAAK;YAC7D,0BAA0B,EAAE,KAAK;SACpC,CAAC;IACN,CAAC;IAED;;;OAGG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,YAAY,CAAC;IACxB,CAAC;IAED,cAAA,EAAgB,CACT,qBAAqB,GAAA;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAErD,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;QACnC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,SAAS;YACtB,QAAQ,EAAE,IAAI,CAAC,WAAW;YAC1B,OAAO,EAAE,IAAI,CAAC,UAAU;SAC3B,CAAC;IACN,CAAC;IAED,iCAAA,EAAmC,CAC5B,iBAAiB,GAAA;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC5B,OAAO,MAAM,CAAC,QAAQ,CAAC;QAC3B,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,SAAS,GAAG,KAAK,EAAA;QACnC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAChI,CAAC;IAED;;;;OAIG,CACI,eAAe,CAAC,SAAS,GAAG,KAAK,EAAA;QACpC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC;IAClI,CAAC;IAED;;;;;;OAMG,CACI,KAAK,CAAC,KAA4B,EAAE,UAAmB,EAAE,KAAc,EAAE,UAAmB,KAAK,EAAA;QACpG,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC;QAC5E,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC,mIAAmI;QAE1L,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QAEtE,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAI,UAAU,IAAI,KAAK,EAAE,CAAC;YACtB,IAAI,kBAAkB,GAAG,IAAI,CAAC;YAC9B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC;gBAChE,IACI,aAAa,KAAK,KAClB,IAD2B,CAAC,QACf,KAAK,KAClB,IAD2B,CAAC,EADyB,MAExC,KAAK,MAClB,GAD2B,CAAC,CADwB,QAEvC,KAAK,IAAA,KAAS,CAAC,CADyB,yBACC,EACxD,CAAC;oBACC,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC;oBAC5D,IAAI,WAAW,KAAK,KAAA,IAAS,CAAC,WAAA,GAAA,cAA4B,IAAI,WAAW,KAAK,SAAS,CAAC,0BAA0B,EAAE,CAAC;wBACjH,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACpD,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACpD,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACpD,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACpD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,qBAAqB,CAAC,CAAC;wBAC7E,kBAAkB,GAAG,KAAK,CAAC;oBAC/B,CAAC,MAAM,CAAC;wBACJ,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACnD,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACnD,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACnD,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;wBACnD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,oBAAoB,CAAC,CAAC;wBAC3E,kBAAkB,GAAG,KAAK,CAAC;oBAC/B,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,kBAAkB,EAAE,CAAC;gBACrB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACtF,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;YACtC,CAAC;QACL,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC7B,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC7B,CAAC;YACD,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACtC,CAAC;QACD,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;QACxC,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED;;OAEG,CACI,SAAS,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAA;QAChE,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC;YACxI,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,MAAM,CAAC;YAEhC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAED;;OAEG,CACa,QAAQ,GAAA;QACpB,KAAK,CAAC,QAAQ,EAAE,CAAC;QACjB,+CAA+C;QAC/C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;IACL,CAAC;IAED;;;OAGG,CACH,IAAW,kBAAkB,GAAA;QACzB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;OASG,CACI,eAAe,CAClB,SAA8B,EAC9B,YAAoB,CAAC,EACrB,aAAsB,EACtB,cAAuB,EACvB,uBAAiC,EACjC,QAAQ,GAAG,CAAC,EACZ,KAAK,GAAG,CAAC,EAAA;QAET,MAAM,cAAc,GAAG,SAAqC,CAAC;QAE7D,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,qCAAqC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC1E,CAAC;QACD,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAE1D,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACrB,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gBACxC,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,SAAS,CAAC,OAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC3I,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC;YAC1C,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;gBAC1B,EAAE,CAAC,oBAAoB,CACnB,EAAE,CAAC,WAAW,EACd,EAAE,CAAC,iBAAiB,EACpB,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAC1C,SAAS,CAAC,OAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EACvD,QAAQ,CACX,CAAC;YACN,CAAC,MAAM,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;gBACjD,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,OAAQ,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;gBAChJ,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,MAAM,mBAAmB,GAAG,SAAS,CAAC,oBAAoB,CAAC;QAC3D,IAAI,mBAAmB,EAAE,CAAC;YACtB,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gBACjB,IACI,SAAS,CAAC,OAAQ,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK,IACtD,SAAS,CAAC,OAAQ,CAAC,MAAM,KAAK,mBAAmB,CAAC,MAAM,IACxD,SAAS,CAAC,OAAQ,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK,EACxD,CAAC;0KACC,SAAM,CAAC,IAAI,CAAC,sFAAsF,CAAC,CAAC;gBACxG,CAAC;YACL,CAAC;YACD,MAAM,UAAU,GAAG,SAAS,CAAC,+BAA+B,CAAC,CAAC,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC;YACjH,IAAI,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gBACxC,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,mBAAmB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YACtI,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;gBAC1B,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAAE,mBAAmB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YACxK,CAAC,MAAM,CAAC;gBACJ,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,UAAU,EAAE,mBAAmB,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAC3I,CAAC;QACL,CAAC;QAED,IAAI,cAAc,CAAC,gBAAgB,EAAE,CAAC;YAClC,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAC1E,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC;gBAChC,IAAI,QAAQ,EAAE,CAAC;oBACX,aAAa,GAAG,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBAC1D,CAAC;YACL,CAAC;YACD,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC;gBAClC,IAAI,QAAQ,EAAE,CAAC;oBACX,cAAc,GAAG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;gBAC5D,CAAC;YACL,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAEe,oBAAoB,CAAC,aAAuB,EAAE,KAAe,EAAA;QACzE,MAAM,QAAQ,GAAG,AAAC,IAAI,CAAC,aAAa,IAAI,aAAa,IAAI,IAAI,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;QAChG,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,KAAK,QAAQ,IAAI,KAAK,EAAE,CAAC;YACzD,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG,CACI,QAAQ,CAAC,OAAgB,EAAE,UAAkB,CAAC,EAAE,KAAe,EAAE,WAAW,GAAG,KAAK,EAAE,aAAuB,EAAE,OAAuB,EAAE,eAAuB,CAAC,EAAA;QACnK,UAAU;QACV,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,EAAE,CAAC;YACpD,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,OAAO,CAAC;QAC3C,CAAC;QAED,YAAY;QACZ,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAEhD,WAAW;QACX,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAEnC,aAAa;QACb,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QAC3D,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,KAAK,SAAS,IAAI,KAAK,EAAE,CAAC;YAC3D,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,SAAS,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,OAAO,CAAC;IACzD,CAAC;IAEO,qCAAqC,CAAC,OAA4B,EAAE,sBAAsB,GAAG,KAAK,EAAA;QACtG,MAAM,cAAc,GAAG,OAAmC,CAAC;QAE3D,IAAI,CAAC,cAAc,CAAC,2BAA2B,EAAE,CAAC;YAC9C,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClB,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAC1C,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC1B,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBAClB,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,uBAAuB,CAAC,WAAuC,EAAA;QAClE,IAAI,IAAI,CAAC,mBAAmB,KAAK,WAAW,EAAE,CAAC;YAC3C,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC;QAC3C,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,uCAAuC,GAAA;QAC1C,OAAO,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC;IAC7C,CAAC;IAED;;;OAGG,CACI,eAAe,CAAC,OAAwB,EAAA;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAChC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;OAKG,CACI,iBAAiB,CAAC,OAA4B,EAAE,sBAAgC,EAAE,cAA2B,EAAA;QAChH,MAAM,cAAc,GAAG,OAAmC,CAAC;QAE3D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,qCAAqC,CAAC,OAAO,EAAE,sBAAsB,CAAC,CAAC;QAE5E,IAAI,cAAc,EAAE,CAAC;YACjB,IAAI,cAAc,CAAC,gBAAgB,EAAE,CAAC;gBAClC,+BAA+B;gBAC/B,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YAC9D,CAAC;YACD,cAAc,EAAE,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG,CACI,0BAA0B,CAAC,OAA4B,EAAA;QAC1D,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,eAAe,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC1E,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,OAA4B,EAAA;QAClD,MAAM,SAAS,GAAG,OAAmC,CAAC;QACtD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACnD,OAAO;QACX,CAAC;QAED,IAAI,UAAU,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,UAAU,IAAI,SAAS,CAAC,oBAAoB,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,UAAU,IAAI,SAAS,CAAC,sBAAsB,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3G,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACpE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;QAChE,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;IACzH,CAAC;IAED;;OAEG,CACI,gBAAgB,GAAA;QACnB,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAED;;OAEG,CACI,yBAAyB,GAAA;QAC5B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACtD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAED,OAAO;IAEP,cAAA,EAAgB,CACN,yBAAyB,GAAA;QAC/B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACrC,CAAC;IAED;;;;;;OAMG,CACI,kBAAkB,CAAC,IAAwB,EAAE,UAAoB,EAAE,MAAe,EAAA;QACrF,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAChE,CAAC;IAEO,mBAAmB,CAAC,IAAwB,EAAE,KAAa,EAAA;QAC/D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;QAEpC,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,UAAU,GAAG,8KAAI,kBAAe,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEjC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3B,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;gBACxB,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC1E,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;YAC1C,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAe,IAAI,EAAE,KAAK,CAAC,CAAC;gBACrE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;YAC1C,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;YACxE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;QAEjC,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC;QAC1B,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG,CACI,yBAAyB,CAAC,IAAwB,EAAE,MAAe,EAAA;QACtE,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACjE,CAAC;IAES,wBAAwB,GAAA;QAC9B,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IACnC,CAAC;IAED;;;;;;OAMG,CACI,iBAAiB,CAAC,OAAqB,EAAE,SAAmB,EAAE,MAAe,EAAA;QAChF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;QACpC,MAAM,UAAU,GAAG,8KAAI,kBAAe,CAAC,GAAG,CAAC,CAAC;QAE5C,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEjC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACnH,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC;QAC1B,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC;QACnD,OAAO,UAAU,CAAC;IACtB,CAAC;IAES,mBAAmB,CAAC,OAAqB,EAAA;QAC/C,MAAM,eAAe,GAAI,OAA2C,CAAC,iBAAiB,CAAC;QACvF,IAAI,eAAe,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,OAAsB,CAAC;QAClC,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACzB,IAAI,OAAO,YAAY,WAAW,EAAE,CAAC;gBACjC,OAAO,OAAO,CAAC;YACnB,CAAC,MAAM,CAAC;gBACJ,uDAAuD;gBACvD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBAClD,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC;wBAC1B,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;oBACpC,CAAC;gBACL,CAAC;gBAED,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,iFAAiF;QACjF,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAED;;;OAGG,CACI,eAAe,CAAC,MAA4B,EAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC7B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACpD,CAAC;IAED;;;;;OAKG,CACI,gBAAgB,CAAC,eAAiC,EAAE,SAAiB,EAAE,KAAa,EAAA;QACvF,MAAM,OAAO,GAAI,eAAwC,CAAC,OAAQ,CAAC;QAEnE,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAE1E,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IAED,gEAAgE;IACtD,eAAe,CAAC,MAA4B,EAAA;QAClD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC7B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAC5D,CAAC;IAEO,WAAW,CAAC,MAA4B,EAAE,MAAc,EAAA;QAC5D,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,MAAM,EAAE,CAAC;YAC3E,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QAC9C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,iBAAiB,CAAC,IAAkB,EAAA;QACvC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAC3D,CAAC;IAEO,oBAAoB,CAAC,MAAkB,EAAE,IAAY,EAAE,IAAY,EAAE,IAAY,EAAE,UAAmB,EAAE,MAAc,EAAE,MAAc,EAAA;QAC1I,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO;QACX,CAAC;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,GAAG,IAAI,CAAC;YACf,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;YACtB,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YACrB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACpB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACpB,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;YAChC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QAC5B,CAAC,MAAM,CAAC;YACJ,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC5B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;gBACxB,OAAO,GAAG,IAAI,CAAC;YACnB,CAAC;YACD,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACxB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;gBACpB,OAAO,GAAG,IAAI,CAAC;YACnB,CAAC;YACD,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACxB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;gBACpB,OAAO,GAAG,IAAI,CAAC;YACnB,CAAC;YACD,IAAI,OAAO,CAAC,UAAU,KAAK,UAAU,EAAE,CAAC;gBACpC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;gBAChC,OAAO,GAAG,IAAI,CAAC;YACnB,CAAC;YACD,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC5B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;gBACxB,OAAO,GAAG,IAAI,CAAC;YACnB,CAAC;YACD,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC5B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;gBACxB,OAAO,GAAG,IAAI,CAAC;YACnB,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACvC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC7B,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;gBAC1D,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YACpE,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAC/E,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,yBAAyB,CAAC,WAAiC,EAAA;QAC9D,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QACD,IAAI,IAAI,CAAC,kBAAkB,KAAK,WAAW,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;YACtC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAClC,IAAI,CAAC,wBAAwB,GAAG,WAAW,CAAC,QAAQ,CAAC;QACzD,CAAC;IACL,CAAC;IAEO,4BAA4B,CAChC,aAAwD,EACxD,MAAc,EACd,qBAAkE,EAAA;QAElE,MAAM,UAAU,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC7B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACrD,MAAM,KAAK,GAAG,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAEjD,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;gBACb,MAAM,EAAE,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC7B,IAAI,YAAY,GAA2B,IAAI,CAAC;gBAEhD,IAAI,qBAAqB,EAAE,CAAC;oBACxB,YAAY,GAAG,qBAAqB,CAAC,EAAE,CAAC,CAAC;gBAC7C,CAAC;gBAED,IAAI,CAAC,YAAY,EAAE,CAAC;oBAChB,YAAY,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;gBACrC,CAAC;gBAED,IAAI,CAAC,YAAY,EAAE,CAAC;oBAChB,SAAS;gBACb,CAAC;gBAED,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC7B,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;gBAClD,CAAC;gBAED,MAAM,MAAM,GAAG,YAAY,CAAC,SAAS,EAAE,CAAC;gBACxC,IAAI,MAAM,EAAE,CAAC;oBACT,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,IAAI,EAAE,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;oBAE/J,IAAI,YAAY,CAAC,cAAc,EAAE,EAAE,CAAC;wBAChC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,EAAE,YAAY,CAAC,kBAAkB,EAAE,CAAC,CAAC;wBACvE,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BAC7B,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BAC3C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC9C,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG,CACI,uBAAuB,CAC1B,aAA8C,EAC9C,WAAiC,EACjC,MAAc,EACd,qBAAkE,EAAA;QAElE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;QAEzC,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAE9B,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QACtC,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QAEhF,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAElC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE/B,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;;OAKG,CACI,qBAAqB,CAAC,iBAAyC,EAAE,WAAiC,EAAA;QACrG,IAAI,IAAI,CAAC,wBAAwB,KAAK,iBAAiB,EAAE,CAAC;YACtD,IAAI,CAAC,wBAAwB,GAAG,iBAAiB,CAAC;YAElD,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;YAC5C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAE/B,IAAI,CAAC,wBAAwB,GAAG,WAAW,IAAI,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC;YAC5E,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;;;;;OAOG,CACI,mBAAmB,CAAC,YAAwB,EAAE,WAAuB,EAAE,iBAA2B,EAAE,gBAAwB,EAAE,MAAc,EAAA;QAC/I,IAAI,IAAI,CAAC,oBAAoB,KAAK,YAAY,IAAI,IAAI,CAAC,6BAA6B,KAAK,MAAM,EAAE,CAAC;YAC9F,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC;YACzC,IAAI,CAAC,6BAA6B,GAAG,MAAM,CAAC;YAE5C,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAEpD,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,EAAE,KAAK,EAAE,CAAE,CAAC;gBACnD,IAAI,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC;oBACnC,MAAM,KAAK,GAAG,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;oBAEjD,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;wBACb,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;wBACxC,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;wBAC9C,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,KAAK,EAAE,iBAAiB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,CAAC,CAAC;oBAC9H,CAAC;oBAED,MAAM,IAAI,iBAAiB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC3C,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC;IAEO,wBAAwB,GAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;;;;OAMG,CACI,WAAW,CACd,aAAwD,EACxD,WAAiC,EACjC,MAAc,EACd,qBAAkE,EAAA;QAElE,IAAI,IAAI,CAAC,oBAAoB,KAAK,aAAa,IAAI,IAAI,CAAC,6BAA6B,KAAK,MAAM,EAAE,CAAC;YAC/F,IAAI,CAAC,oBAAoB,GAAG,aAAa,CAAC;YAC1C,IAAI,CAAC,6BAA6B,GAAG,MAAM,CAAC;YAE5C,IAAI,CAAC,4BAA4B,CAAC,aAAa,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG,CACI,wBAAwB,GAAA;QAC3B,IAAI,WAAW,CAAC;QAChB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC;YACtE,MAAM,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;YACxD,IAAI,WAAW,IAAI,eAAe,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;gBAC/D,WAAW,GAAG,eAAe,CAAC;gBAC9B,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;YAC1C,CAAC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG,CACI,wBAAwB,CAAC,GAA2B,EAAA;QACvD,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG,CACI,cAAc,CAAC,MAAkB,EAAA;QACpC,MAAM,CAAC,UAAU,EAAE,CAAC;QAEpB,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAES,aAAa,CAAC,MAAkB,EAAA;QACtC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG,CACI,4BAA4B,CAAC,eAA2B,EAAE,IAAkB,EAAE,eAAqD,EAAA;QACtI,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAC3D,CAAC;QAED,IAAU,eAAe,CAAC,CAAC,CAAE,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAChD,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,eAAsB,EAAE,IAAI,CAAC,CAAC;QAC5E,CAAC,MAAM,CAAC;YACJ,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;gBACrC,MAAM,cAAc,GAAW,eAAe,CAAC,KAAK,CAAC,CAAC;gBAEtD,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,EAAE,CAAC;oBACnD,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;oBACjD,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;gBAC3D,CAAC;gBAED,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,cAAc,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;gBACrG,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACpD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACvD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,mBAAmB,CAAC,eAA2B,EAAE,cAAyC,EAAE,aAAa,GAAG,IAAI,EAAA;QACnH,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;QAEtC,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,aAAa,EAAE,CAAC;YAChB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC7C,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM,IAAI,EAAE,CAAC,aAAa,GAAG,CAAC,CAAC;YACnC,CAAC;QACL,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7C,MAAM,EAAE,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,EAAE,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACzB,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,cAAe,CAAC,0BAA0B,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;YACjF,CAAC;YAED,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;gBACf,SAAS;YACb,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;gBAC3C,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;YACrD,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,IAAI,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;YACtJ,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;YAClF,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;YAC9C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,8BAA8B,CAAC,IAAY,EAAA;QAC9C,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG,CACI,wBAAwB,CAAC,iBAAyB,EAAA;QACrD,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,KAAa,CAAC;QAClB,MAAO,CAAC,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAE,CAAC;YAChF,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAE9C,WAAW,GAAG,IAAI,CAAC;YACnB,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;YACnD,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,uBAAuB,CAAC,iBAAyB,EAAA;QACpD,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QACrD,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,GAAG,KAAK,CAAC;QAC3D,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;;;;;OAMG,CACI,IAAI,CAAC,YAAqB,EAAE,UAAkB,EAAE,UAAkB,EAAE,cAAuB,EAAA;QAC9F,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAA,GAAA,EAAS,CAAC,SAAA,YAAA,IAAyB,CAAC,CAAC,CAAC,SAAS,CAAC,0BAA0B,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IAC7J,CAAC;IAED;;;;;OAKG,CACI,eAAe,CAAC,aAAqB,EAAE,aAAqB,EAAE,cAAuB,EAAA;QACxF,IAAI,CAAC,cAAc,CAAC,GAAA,MAAS,CAAC,QAAA,cAAsB,CAAA,CAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IACxG,CAAC;IAED;;;;;;OAMG,CACI,aAAa,CAAC,YAAqB,EAAE,aAAqB,EAAE,aAAqB,EAAE,cAAuB,EAAA;QAC7G,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC,IAAA,GAAA,EAAS,CAAC,YAAA,aAAyB,CAAC,CAAC,CAAC,SAAS,CAAC,0BAA0B,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;IACjK,CAAC;IAED;;;;;;OAMG,CACI,gBAAgB,CAAC,QAAgB,EAAE,UAAkB,EAAE,UAAkB,EAAE,cAAuB,EAAA;QACrG,eAAe;QACf,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,SAAS;QAET,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;QACpG,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,cAAc,EAAE,CAAC;YACjB,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI,EAAE,cAAc,CAAC,CAAC;QACzG,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACI,cAAc,CAAC,QAAgB,EAAE,aAAqB,EAAE,aAAqB,EAAE,cAAuB,EAAA;QACzG,eAAe;QACf,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,cAAc,EAAE,CAAC;YACjB,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QACzF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,QAAgB,EAAA;QAC9B,OAAQ,QAAQ,EAAE,CAAC;YACf,iBAAiB;YACjB,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;YAC9B,KAAK,SAAS,CAAC,sBAAsB;gBACjC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,aAAa;YACb,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;YAC9B,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;YAC/B,KAAK,SAAS,CAAC,8BAA8B;gBACzC,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACnC,KAAK,SAAS,CAAC,4BAA4B;gBACvC,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC;YACjC;gBACI,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;QAClC,CAAC;IACL,CAAC;IAED,UAAU;IAEV;;OAEG,CACI,cAAc,CAAC,MAAc,EAAA;QAChC,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC;QACD,MAAM,eAAe,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;QACpD,IAAI,eAAe,EAAE,CAAC;YAClB,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAED;;OAEG,CACI,sBAAsB,CAAC,eAAiC,EAAA;QAC3D,MAAM,oBAAoB,GAAG,eAAuC,CAAC;QACrE,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,OAAO,EAAE,CAAC;YACvD,oBAAoB,CAAC,OAAO,CAAC,wBAAwB,GAAG,IAAI,CAAC;wLAC7D,sBAAA,AAAmB,EAAC,oBAAoB,CAAC,CAAC;YAC1C,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,IAAI,IAAI,CAAC,eAAe,KAAK,oBAAoB,CAAC,OAAO,EAAE,CAAC;oBACxD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAC3B,CAAC;gBACD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACzD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACa,iBAAiB,CAAC,OAAmC,EAAA;QACjE,yLAAO,oBAAiB,AAAjB,EAAkB,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;IACtH,CAAC;IAED;;;;;;;;;;;;;;OAcG,CACI,YAAY,CACf,QAAmF,EACnF,wBAA2D,EAC3D,qBAA4C,EAC5C,QAAmB,EACnB,OAAgB,EAChB,SAA4B,EAC5B,UAA+C,EAC/C,OAA4D,EAC5D,eAAqB,EACrB,cAAc,GAAA,EAAA,uBAAA,EAAsB,CAAtB,EACd,yBAA+C,EAAA;QAE/C,MAAM,MAAM,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,MAAM,CAAC;QACpJ,MAAM,QAAQ,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,eAAe,IAAI,QAAQ,CAAC,QAAQ,CAAC;QAC9J,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAG,CAAC;QAEhD,MAAM,SAAS,GAAI,wBAAmD,CAAC,UAAU,KAAK,SAAS,CAAC;QAEhG,IAAI,WAAW,GAAG,OAAO,IAA6B,wBAAyB,CAAC,OAAO,IAAI,EAAE,CAAC;QAE9F,IAAI,aAAa,EAAE,CAAC;YAChB,WAAW,IAAI,aAAa,CAAC;QACjC,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,QAAQ,GAAG,GAAG,GAAG,WAAW,CAAC;QACzD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACnD,IAAI,UAAU,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;gBACzC,UAAU,CAAC,cAAc,CAAC,CAAC;YAC/B,CAAC;YACD,cAAc,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO,cAAc,CAAC;QAC1B,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;0LACX,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC;QACD,MAAM,MAAM,GAAG,8JAAI,UAAM,CACrB,QAAQ,EACR,wBAAwB,EACxB,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB,EACxC,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,eAAe,EACf,IAAI,EACqB,wBAAyB,CAAC,cAAc,IAAI,cAAc,EAC1D,wBAAyB,CAAC,yBAAyB,IAAI,yBAAyB,CAC5G,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAErC,OAAO,MAAM,CAAC;IAClB,CAAC;IAKD;;OAEG,CACI,gBAAgB,CAAC,MAAmB,EAAA;QACvC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED;;;;;;;;OAQG,CACI,sBAAsB,CACzB,eAAiC,EACjC,UAAkB,EAClB,YAAoB,EACpB,OAA+B,EAC/B,4BAAgD,IAAI,EAAA;QAEpD,MAAM,WAAW,iLAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QACnD,WAAW,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QACjE,OAAO,uMAAA,AAAsB,EAAC,eAAe,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAAC;IAC7H,CAAC;IAED;;;;;;;;;OASG,CACI,mBAAmB,CACtB,eAAiC,EACjC,UAAkB,EAClB,YAAoB,EACpB,OAAyB,EACzB,OAA+B,EAC/B,4BAAgD,IAAI,EAAA;QAEpD,MAAM,WAAW,iLAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,qCAAqC;QACrC,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QACnD,WAAW,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QACjE,qLAAO,sBAAA,AAAmB,EAAC,eAAe,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAAC;IACnI,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,IAAY,EAAA;QAChC,yCAAyC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,qBAAqB,CAAC,uBAA4D,EAAA;QACrF,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,MAAM,WAAW,iLAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC7C,WAAW,CAAC,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC;QACzE,CAAC;QACD,MAAM,OAAO,iLAAG,wBAAA,AAAqB,EAAC,IAAI,CAAC,GAAG,EAAE,uBAAuB,CAAyB,CAAC;QACjG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QACtB,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG,CACI,qBAAqB,GAAA;QACxB,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;OAGG,CACI,iBAAiB,GAAA;QACpB,OAAO,SAAS,CAAC;IACrB,CAAC;IAES,wBAAwB,CAAC,eAAqC,EAAA;QACpE,qLAAO,2BAAwB,AAAxB,EAAyB,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAC5F,CAAC;IAED;;OAEG,CACH,iDAAiD;IACjD,gDAAgD;IACzC,4BAA4B,CAC/B,eAAiC,EACjC,gBAAwB,EACxB,kBAA0B,EAC1B,WAAoB,EACpB,mBAA2B,EAC3B,qBAA6B,EAC7B,aAAkB,EAClB,OAAyB,EACzB,yBAA6C,EAC7C,GAAW,EACX,OAAmB,EAAA;QAEnB,MAAM,WAAW,iLAAG,iBAAA,AAAc,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QACnD,WAAW,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;QACjE,WAAW,CAAC,6BAA6B,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjF,WAAW,CAAC,+BAA+B,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrF,WAAW,CAAC,4BAA4B,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/E,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,OAAO,wMAAA,AAAuB,EAC1B,eAAuC,EACvC,gBAAgB,EAChB,kBAAkB,EAClB,WAAW,EACX,mBAAmB,EACnB,qBAAqB,EACrB,aAAa,EACb,OAAO,EACP,yBAAyB,EACzB,GAAG,EACH,OAAO,CACV,CAAC;IACN,CAAC;IAES,oBAAoB,CAC1B,eAAqC,EACrC,YAAyB,EACzB,cAA2B,EAC3B,OAA8B,EAC9B,4BAAgD,IAAI,EAAA;QAEpD,qLAAO,uBAAA,AAAoB,EAAC,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,OAAO,EAAE,yBAAyB,CAAC,CAAC;IACnH,CAAC;IAED;;OAEG,CACI,yBAAyB,CAAC,eAAiC,EAAA;QAC9D,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,0MAAA,AAAyB,EAAC,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;IAC7F,CAAC;IAED;;OAEG,CACI,oCAAoC,CAAC,eAAiC,EAAE,MAAkB,EAAA;QAC7F,qNAAA,AAAoC,EAAC,eAAuC,EAAE,MAAM,CAAC,CAAC;IAC1F,CAAC;IAED;;;;;OAKG,CACI,WAAW,CAAC,eAAiC,EAAE,aAAuB,EAAA;QACzE,MAAM,OAAO,GAAG,IAAI,KAAK,EAAkC,CAAC;QAC5D,MAAM,oBAAoB,GAAG,eAAuC,CAAC;QAErE,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,OAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnG,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;OAKG,CACI,aAAa,CAAC,eAAiC,EAAE,eAAyB,EAAA;QAC7E,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,oBAAoB,GAAG,eAAuC,CAAC;QAErE,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC1D,IAAI,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,OAAQ,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;gBACT,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG,CACI,YAAY,CAAC,MAAsC,EAAA;QACtD,MAAM,GAAG,MAAM,KAAK,IAAI,qLAAI,YAAA,AAAS,EAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,mEAAmE;QAE3I,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5C,OAAO;QACX,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,SAAS,CAAC;QAEvD,cAAc;QACd,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE1B,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;QAE7B,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;YAC3B,MAAM,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,OAAuC,EAAE,KAAa,EAAA;QAChE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,OAAO,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAA;QACxE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG,CACI,OAAO,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;QACnF,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG,CACI,OAAO,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;QAC9F,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,WAAW,CAAC,OAAuC,EAAE,KAAiB,EAAA;QACzE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,OAAuC,EAAE,KAAiB,EAAA;QAC1E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,OAAuC,EAAE,KAAiB,EAAA;QAC1E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,OAAuC,EAAE,KAAiB,EAAA;QAC1E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,OAAO,CAAC,OAAuC,EAAE,KAAa,EAAA;QACjE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,QAAQ,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAA;QACzE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG,CACI,QAAQ,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;QACpF,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG,CACI,QAAQ,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;QAC/F,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,OAAuC,EAAE,KAAkB,EAAA;QAC3E,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,aAAa,CAAC,OAAuC,EAAE,KAAkB,EAAA;QAC5E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,aAAa,CAAC,OAAuC,EAAE,KAAkB,EAAA;QAC5E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,aAAa,CAAC,OAAuC,EAAE,KAAkB,EAAA;QAC5E,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACrC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,QAAQ,CAAC,OAAuC,EAAE,KAAiB,EAAA;QACtE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,SAAS,CAAC,OAAuC,EAAE,KAAiB,EAAA;QACvE,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAO,KAAK,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,SAAS,CAAC,OAAuC,EAAE,KAAiB,EAAA;QACvE,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAO,KAAK,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,SAAS,CAAC,OAAuC,EAAE,KAAiB,EAAA;QACvE,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAO,KAAK,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,WAAW,CAAC,OAAuC,EAAE,QAAmC,EAAA;QAC3F,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,OAAuC,EAAE,MAAoB,EAAA;QAC7E,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,OAAuC,EAAE,MAAoB,EAAA;QAC7E,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,QAAQ,CAAC,OAAuC,EAAE,KAAa,EAAA;QAClE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEnC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,SAAS,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAA;QAC1E,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;OAOG,CACI,SAAS,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;QACrF,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAErC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;OAQG,CACI,SAAS,CAAC,OAAuC,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAA;QAChG,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,SAAS;IAET;;OAEG,CACI,WAAW,GAAA;QACd,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnJ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAChC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;YAChC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED,WAAW;IAEX;;;;OAIG,CACI,UAAU,CAAC,UAAoB,EAAA;QAClC,IAAI,IAAI,CAAC,6BAA6B,IAAI,CAAC,UAAU,EAAE,CAAC;YACpD,OAAO;QACX,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;QAE3B,8CAA8C;QAC9C,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEhC,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;YAEnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,kBAAkB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAEpD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,EAAE,CAAC;YAEvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAE/B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAE/B,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACjF,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;YAEjE,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;YACtC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAC1C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG,CACI,sBAAsB,CAAC,YAAoB,EAAE,eAAwB,EAAA;QACxE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,SAAS,GAAW,EAAE,CAAC,OAAO,CAAC;QACnC,IAAI,SAAS,GAAW,EAAE,CAAC,OAAO,CAAC;QACnC,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,OAAQ,YAAY,EAAE,CAAC;YACnB,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,IAAI,eAAe,EAAE,CAAC;oBAClB,SAAS,GAAG,EAAE,CAAC,qBAAqB,CAAC;gBACzC,CAAC,MAAM,CAAC;oBACJ,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBAC1B,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,+BAA+B;gBAC1C,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,UAAU,GAAG,IAAI,CAAC;gBAClB,IAAI,eAAe,EAAE,CAAC;oBAClB,SAAS,GAAG,EAAE,CAAC,oBAAoB,CAAC;gBACxC,CAAC,MAAM,CAAC;oBACJ,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBAC1B,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,UAAU,GAAG,IAAI,CAAC;gBAClB,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,IAAI,eAAe,EAAE,CAAC;oBAClB,SAAS,GAAG,EAAE,CAAC,qBAAqB,CAAC;gBACzC,CAAC,MAAM,CAAC;oBACJ,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBAC3B,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,IAAI,eAAe,EAAE,CAAC;oBAClB,SAAS,GAAG,EAAE,CAAC,sBAAsB,CAAC;gBAC1C,CAAC,MAAM,CAAC;oBACJ,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBAC3B,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,IAAI,eAAe,EAAE,CAAC;oBAClB,SAAS,GAAG,EAAE,CAAC,qBAAqB,CAAC;gBACzC,CAAC,MAAM,CAAC;oBACJ,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBAC1B,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,UAAU,GAAG,IAAI,CAAC;gBAClB,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,IAAI,eAAe,EAAE,CAAC;oBAClB,SAAS,GAAG,EAAE,CAAC,oBAAoB,CAAC;gBACxC,CAAC,MAAM,CAAC;oBACJ,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBAC1B,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,sBAAsB;gBACjC,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,MAAM;YACV,KAAK,SAAS,CAAC,uBAAuB;gBAClC,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,MAAM;YACV,KAAK,SAAS,CAAC,iCAAiC;gBAC5C,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,IAAI,eAAe,EAAE,CAAC;oBAClB,SAAS,GAAG,EAAE,CAAC,sBAAsB,CAAC;gBAC1C,CAAC,MAAM,CAAC;oBACJ,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBAC3B,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,UAAU,GAAG,IAAI,CAAC;gBAClB,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,IAAI,eAAe,EAAE,CAAC;oBAClB,SAAS,GAAG,EAAE,CAAC,qBAAqB,CAAC;gBACzC,CAAC,MAAM,CAAC;oBACJ,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBAC3B,CAAC;gBACD,MAAM;YACV,KAAK,SAAS,CAAC,qBAAqB;gBAChC,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,MAAM;YACV,KAAK,SAAS,CAAC,sBAAsB;gBACjC,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;gBACtB,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC;gBACvB,MAAM;QACd,CAAC;QAED,OAAO;YACH,GAAG,EAAE,SAAS;YACd,GAAG,EAAE,SAAS;YACd,UAAU,EAAE,UAAU;SACzB,CAAC;IACN,CAAC;IAED,cAAA,EAAgB,CACN,cAAc,GAAA;QACpB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;QAEzC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,cAAA,EAAgB,CACT,sBAAsB,GAAA;QACzB,OAAO,IAAI,uMAAoB,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACrE,CAAC;IAED;;;;;;;;OAQG,CACI,sBAAsB,CACzB,IAAiB,EACjB,OAAiD,EACjD,uBAAuB,GAAG,IAAI,EAC9B,MAAM,GAAA,EAAA,iCAAA,EAAgC,CAAhC,EAAgC;QAEtC,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;QAC/C,IAAI,YAAY,GAAG,SAAS,CAAC,8BAA8B,CAAC;QAC5D,IAAI,MAAM,GAAG,SAAS,CAAC,kBAAkB,CAAC;QAC1C,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,KAAyB,CAAC;QAC9B,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC3B,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YACvD,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;YAC5C,aAAa,GAAG,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YACxC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,EAAA,IAAA,mBAAyB,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;YACvF,YAAY,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,EAAA,YAAA,gBAA8B,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;YACpH,MAAM,GAAG,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,IAAA,KAAS,CAAC,EAAA,MAAA,UAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;YACtF,aAAa,GAAG,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YACpF,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;YAC/B,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;YACtB,iBAAiB,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAChD,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC;QACzD,CAAC,MAAM,CAAC;YACJ,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC;QAChC,CAAC;QAED,aAAa,IAAA,CAAb,aAAa,GAAK,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAC;QAE5F,IAAI,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,UAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,EAAA,wBAA2B,EAAE,CAAC;YAClF,yEAAyE;YACzE,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QAC1D,CAAC,MAAM,IAAI,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,eAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAA,6BAA+B,EAAE,CAAC;YAClG,2EAA2E;YAC3E,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QAC1D,CAAC;QACD,IAAI,IAAI,KAAK,KAAA,CAAA,GAAS,CAAC,CAAA,KAAA,CAAA,UAAiB,EAAA,EAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YACnE,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;YAC3C,+JAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAC;QAC9F,CAAC;QAED,MAAM,cAAc,kMAAG,iBAAA,AAAc,EAAC,MAAM,CAAC,CAAC;QAC9C,MAAM,UAAU,kMAAG,mBAAgB,AAAhB,EAAiB,MAAM,CAAC,CAAC;QAE5C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,OAAO,GAAG,IAAI,kMAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClD,MAAM,KAAK,GAAwE,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;QAC/G,MAAM,MAAM,GAAwE,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;QACjH,MAAM,KAAK,GAAwE,IAAK,CAAC,KAAK,IAAI,CAAC,CAAC;QACpG,MAAM,MAAM,GAAwE,IAAK,CAAC,MAAM,IAAI,CAAC,CAAC;QACtG,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,CAAC,eAAe,IAAI,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACjH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC;QAChG,MAAM,WAAW,GAAG,cAAc,GAC5B,IAAI,CAAC,wCAAwC,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,GACvE,IAAI,CAAC,iCAAiC,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAC1E,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,AAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC/H,MAAM,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,0CAA0C,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAE/H,OAAO;QACP,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAE3C,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YACf,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YACzB,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QACvG,CAAC,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACpB,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QACtG,CAAC,MAAM,CAAC;YACJ,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAC/F,CAAC;QAED,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAC9D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAE9D,IAAI,cAAc,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;YAC1C,IAAI,kBAAkB,KAAK,CAAC,EAAE,CAAC;gBAC3B,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;gBACpE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;YAC/D,CAAC,MAAM,CAAC;gBACJ,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;gBACtE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,oBAAoB,EAAE,EAAE,CAAC,sBAAsB,CAAC,CAAC;YACjF,CAAC;QACL,CAAC;QAED,UAAU;QACV,IAAI,eAAe,IAAI,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAExC,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;QACvC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,MAAM,IAAI,KAAK,CAAC;QAChC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAEhD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,IAAI,iBAAiB,EAAE,CAAC;YACpB,IAAI,YAAY,GAAgC,IAAI,CAAC;YAErD,mMAAI,iBAAA,AAAc,EAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjC,YAAY,GAAG,IAAI,CAAC,iCAAiC,gMACjD,mBAAA,AAAgB,EAAC,OAAO,CAAC,MAAM,CAAC,EAChC,OAAO,CAAC,MAAM,KAAK,IAAA,KAAS,CAAC,EAAA,KAAA,EAAA,QAAA,KAAsB,CAAA,CACnD,CAAA,MAAO,CAAC,EAAA,GAAK,EACb,GAAA,IAAO,CAAC,CAAA,EAAA,GAAM,EACd,OAAO,EACP,OAAO,CAAC,MAAM,EACd,IAAI,CACP,CAAC;YACN,CAAC,MAAM,CAAC;gBACJ,YAAY,GAAG,IAAI,CAAC,mBAAmB,CACnC,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,EACd,OAAO,EACP,CAAC,CAAC,CAAC,YAAA,EAAc,GACjB,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,EAC5F,CAAC,CAAC,CAAC,cAAA,EAAgB,CACtB,CAAC;YACN,CAAC;YAED,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACtD,CAAC;YAED,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC;YAEnC,IAAI,eAAe,GAAG,OAAO,CAAC,gBAAkD,CAAC;YACjF,IAAI,CAAC,eAAe,EAAE,CAAC;gBACnB,eAAe,GAAG,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,EAA0B,CAAC;YACvG,CAAC;YAED,eAAe,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG,CACI,iBAAiB,CAAC,aAAsB,EAAE,QAAiB,EAAA;QAC9D,uHAAuH;QACvH,OAAO,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,QAAQ,CAAC,CAAC;IACjG,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;OAsBG,CACI,aAAa,CAChB,GAAqB,EACrB,QAAiB,EACjB,OAAgB,EAChB,KAA2B,EAC3B,eAAuB,CAAA,EAAA,MAAS,CAAC,EAAA,IAAA,EAAA,UAAA,IAAA,EAAA,MAA8B,EAC/D,CAAA,IAAA,EAAA,EAAuD,IAAI,EAC3D,GAAA,IAAA,EAAA,CAA+D,IAAI,EACnE,EAAA,IAAA,EAAA,CAAmG,IAAI,EACvG,WAAsC,IAAI,EAC1C,QAAA,CAA2B,CAAA,GAAI,EAC/B,QAAA,EAAA,QAAoC,IAAI,CAAA,CACxC,CAAA,OAAiB,EACjB,IAAA,EAAA,OAAmB,EACnB,aAAsB,EACtB,aAAuB;QAEvB,OAAO,IAAI,CAAC,kBAAkB,CAC1B,GAAG,EACH,QAAQ,EACR,OAAO,EACP,KAAK,EACL,YAAY,EACZ,MAAM,EACN,OAAO,EACP,CAAC,GAAG,IAAwC,EAAE,CAAG,CAAD,GAAK,CAAC,oBAAoB,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,EAC3F,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,oBAAoB,EAAE,EAAE;YACnE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;YACpB,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,CAAC;YAEjE,OAAO,CAAC,cAAc,GAAG,aAAa,IAAI,CAAC,CAAC;YAE5C,MAAM,GAAG,GAAG,IAAI,CAAC,sCAAsC,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;YAChG,IAAI,KAAK,EAAE,CAAC;gBACR,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAU,CAAC,CAAC;gBACtF,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAEjD,IAAI,GAAG,CAAC,KAAK,GAAG,cAAc,IAAI,GAAG,CAAC,MAAM,GAAG,cAAc,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE,CAAC;gBACvG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;oBAChD,OAAO,KAAK,CAAC;gBACjB,CAAC;gBAED,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG,QAAQ,CAAC;gBACrC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC;gBAEvC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAU,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;gBACnG,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,cAAgC,CAAC,CAAC;gBAEjH,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC;gBACzB,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;gBAE3B,OAAO,KAAK,CAAC;YACjB,CAAC,MAAM,CAAC;gBACJ,2EAA2E;gBAC3E,MAAM,MAAM,GAAG,oLAAI,kBAAe,CAAC,IAAI,EAAA,EAAA,8BAAA,GAA6B,CAAC;gBACrE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;gBACvD,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,GAAU,CAAC,CAAC;gBAEtF,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE;oBAC1D,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;oBAC7B,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;oBAExD,oBAAoB,EAAE,CAAC;gBAC3B,CAAC,CAAC,CAAC;YACP,CAAC;YAED,OAAO,IAAI,CAAC;QAChB,CAAC,EACD,MAAM,EACN,QAAQ,EACR,MAAM,EACN,eAAe,EACf,QAAQ,EACR,aAAa,EACb,aAAa,CAChB,CAAC;IACN,CAAC;IAED;;;;;;;;;OASG,CACI,sCAAsC,CAAC,aAAqB,EAAE,aAAsB,EAAA;QACvF,IAAI,MAAc,EAAE,cAAsB,CAAC;QAC3C,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;YAC1B,uIAAuI;YACvI,qIAAqI;YACrI,wBAAwB;YACxB,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAC/D,cAAc,GAAG,MAAM,CAAC;QAC5B,CAAC,MAAM,CAAC;YACJ,0GAA0G;YAC1G,8EAA8E;YAC9E,0HAA0H;YAC1H,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACvD,cAAc,GAAG,IAAI,CAAC,iCAAiC,CAAC,GAAA,MAAS,CAAC,QAAA,iBAAyB,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QAC/H,CAAC;QAED,OAAO;YACH,cAAc;YACd,MAAM;YACN,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa;SAC/B,CAAC;IACN,CAAC;IAED;;OAEG,CACI,eAAe,CAAC,MAAuB,EAAE,WAA4B,EAAE,KAAoB,EAAE,cAAsB,EAAE,UAAsB,EAAA,CAAS,CAAC;IAa5J;;OAEG,CACI,YAAY,CAAC,KAAc,EAAA;QAC9B,IAAI,IAAI,CAAC,kBAAkB,KAAK,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAElE,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACpC,CAAC;QACL,CAAC;IACL,CAAC;IAED,cAAA,EAAgB,CACT,oBAAoB,GAAA;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAW,CAAC;IACtE,CAAC;IAED,cAAA,EAAgB,CACT,iBAAiB,CAAC,OAAwB,EAAA;QAC7C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACrC,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;QAC/B,CAAC,MAAM,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACrC,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;IAC/B,CAAC;IAED;;;;;OAKG,CACI,yBAAyB,CAAC,YAAoB,EAAE,OAAwB,EAAE,kBAA2B,KAAK,EAAA;QAC7G,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,OAAO,CAAC,UAAU,IAAI,eAAe,CAAC,CAAC;QAEjG,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAC5F,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAEnF,IAAI,eAAe,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACxC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;YAC/B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAExC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACxC,CAAC;IAED;;;;;;OAMG,CACI,uBAAuB,CAAC,OAAwB,EAAE,KAAa,EAAE,MAAc,EAAE,QAAgB,CAAC,EAAA,CAAS,CAAC;IAEnH;;;;;;OAMG,CACI,yBAAyB,CAAC,OAAwB,EAAE,KAAuB,EAAE,QAA0B,IAAI,EAAE,QAA0B,IAAI,EAAA;QAC9I,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;YAC5G,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;QACjC,CAAC;QACD,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;YAC5G,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACxD,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;YAC5G,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG,CACI,sCAAsC,CACzC,OAAwB,EACxB,cAAsB,EACtB,KAAa,EACb,MAAc,EACd,IAAqB,EACrB,YAAoB,CAAC,EACrB,MAAc,CAAC,EAAA;QAEf,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,IAAI,MAAM,GAAW,EAAE,CAAC,UAAU,CAAC;QACnC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,GAAG,EAAE,CAAC,2BAA2B,GAAG,SAAS,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,OAAQ,cAAc,EAAE,CAAC;gBACrB,KAAK,SAAS,CAAC,kCAAkC,CAAC;gBAClD,KAAK,SAAS,CAAC,uCAAuC;oBAClD,8EAA8E;oBAC9E,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;wBAClB,cAAc,GAAG,EAAE,CAAC,qBAAqB,CAAC;oBAC9C,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBACnC,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,uCAAuC;oBAClD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;wBAClB,cAAc,GAAG,EAAE,CAAC,gCAAgC,CAAC;oBACzD,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBACnC,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,wCAAwC;oBACnD,cAAc,GAAG,EAAE,CAAC,oCAAoC,CAAC;oBACzD,MAAM;gBACV,KAAK,SAAS,CAAC,sCAAsC;oBACjD,cAAc,GAAG,EAAE,CAAC,oCAAoC,CAAC;oBACzD,MAAM;gBACV,KAAK,SAAS,CAAC,sCAAsC;oBACjD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;wBACvB,cAAc,GAAG,EAAE,CAAC,6BAA6B,CAAC;oBACtD,CAAC,MAAM,CAAC;wBACJ,oCAAoC;wBACpC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBACnC,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,uCAAuC;oBAClD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;wBACvB,cAAc,GAAG,EAAE,CAAC,mCAAmC,CAAC;oBAC5D,CAAC,MAAM,CAAC;wBACJ,oCAAoC;wBACpC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBACnC,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,uCAAuC;oBAClD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;wBACvB,cAAc,GAAG,EAAE,CAAC,mCAAmC,CAAC;oBAC5D,CAAC,MAAM,CAAC;wBACJ,oCAAoC;wBACpC,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBACnC,CAAC;oBACD,MAAM;gBACV;oBACI,+FAA+F;oBAC/F,OAAO,CAAC,cAAc,GAAG,KAAK,CAAC;oBAC/B,MAAM;YACd,CAAC;QACL,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAM,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAY,IAAI,CAAC,CAAC;IACjG,CAAC;IAED;;OAEG,CACI,4BAA4B,CAC/B,OAAwB,EACxB,SAA0B,EAC1B,YAAoB,CAAC,EACrB,MAAc,CAAC,EACf,qBAA8B,EAC9B,wBAAwB,GAAG,KAAK,EAAA;QAEhC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,cAAc,GAChB,qBAAqB,KAAK,SAAS,GAC7B,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,GAC5F,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;QAEjF,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,MAAM,GAAW,EAAE,CAAC,UAAU,CAAC;QACnC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,GAAG,EAAE,CAAC,2BAA2B,GAAG,SAAS,CAAC;QACxD,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACvE,MAAM,KAAK,GAAG,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACrG,MAAM,MAAM,GAAG,wBAAwB,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAExG,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;IACjG,CAAC;IAED;;;;;;;;;;;OAWG,CACI,iBAAiB,CACpB,OAAwB,EACxB,SAA0B,EAC1B,OAAe,EACf,OAAe,EACf,KAAa,EACb,MAAc,EACd,YAAoB,CAAC,EACrB,MAAc,CAAC,EACf,eAAe,GAAG,KAAK,EAAA;QAEvB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAEvD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,gBAAgB,GAAW,EAAE,CAAC,UAAU,CAAC;QAC7C,IAAI,MAAM,GAAW,EAAE,CAAC,UAAU,CAAC;QACnC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,GAAG,EAAE,CAAC,2BAA2B,GAAG,SAAS,CAAC;YACpD,gBAAgB,GAAG,EAAE,CAAC,gBAAgB,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAE3D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QAE/F,IAAI,eAAe,EAAE,CAAC;YAClB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG,CACI,+BAA+B,CAAC,OAAwB,EAAE,SAA0B,EAAE,YAAoB,CAAC,EAAE,MAAc,CAAC,EAAA;QAC/H,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC;QAExE,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAErD,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;QAEtE,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;IAES,gCAAgC,CAAC,OAAwB,EAAE,KAA2B,EAAE,QAAiB,EAAE,YAAqB,EAAE,YAAoB,EAAA;QAC5J,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,CAAC,EAAE,EAAE,CAAC;YACN,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,CAAC,QAAQ,CAAC,CAAC;QAErE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACpE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAEpE,IAAI,CAAC,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;YAC7B,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAE/C,4BAA4B;QAC5B,IAAI,KAAK,EAAE,CAAC;YACR,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;IACvC,CAAC;IAEO,oBAAoB,CACxB,OAAwB,EACxB,SAAiB,EACjB,KAA2B,EAC3B,GAAuE,EACvE,OAAgB,EAChB,QAAiB,EACjB,YAAqB,EACrB,eAA8C,EAC9C,YAAoB,EACpB,MAAwB,EAAA;QAExB,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,uKAAC,mBAAgB,AAAhB,EAAiB,GAAG,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC1H,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,uKAAC,mBAAA,AAAgB,EAAC,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE7H,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,CAAC,EAAE,EAAE,CAAC;YACN,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC5B,6BAA6B;YAC7B,IAAI,KAAK,EAAE,CAAC;gBACR,KAAK,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;YAED,OAAO;QACX,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAEzE,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC;QAC9B,OAAO,CAAC,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC;QAChC,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC;QACzB,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;QAC3B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC;QACxF,OAAO,CAAC,MAAM,GACV,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,AAAC,MAAM,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAA,CAAA,IAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAExK,IACI,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE;YAC/D,IAAI,CAAC,gCAAgC,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QAChG,CAAC,CAAC,EACJ,CAAC;YACC,+CAA+C;YAC/C,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gCAAgC,CAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;IAChG,CAAC;IAEM,wCAAwC,CAAC,aAAqB,EAAE,QAAiB,EAAE,UAAmB,EAAA;QACzG,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC,cAAc,CAAC;QAC7B,CAAC;QAED,MAAM,MAAM,GAAW,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC;QAC1E,IAAI,cAAc,GAAG,MAAM,CAAC;QAC5B,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;YACxB,IAAI,aAAa,KAAK,IAAA,KAAS,CAAC,qBAAqB,EAAE,CAAC;gBACpD,cAAc,GAAG,EAAE,CAAC,iBAAiB,CAAC;YAC1C,CAAC,MAAM,IAAI,aAAa,KAAK,IAAA,KAAS,CAAC,qBAAqB,EAAE,CAAC;gBAC3D,cAAc,GAAG,EAAE,CAAC,iBAAiB,CAAC;YAC1C,CAAC,MAAM,IAAI,aAAa,KAAK,MAAA,GAAS,CAAC,cAAA,IAAA,iBAAmC,IAAI,aAAa,KAAK,SAAS,CAAC,8BAA8B,EAAE,CAAC;gBACvI,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAC;YAC7E,CAAC,MAAM,IAAI,aAAa,KAAK,IAAA,KAAS,CAAC,2BAA2B,EAAE,CAAC;gBACjE,cAAc,GAAG,EAAE,CAAC,kBAAkB,CAAC;YAC3C,CAAC,MAAM,IAAI,aAAa,KAAK,IAAA,KAAS,CAAC,mCAAmC,EAAE,CAAC;gBACzE,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC;YAC/E,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,cAAc,GAAG,EAAE,CAAC,iBAAiB,CAAC;QAC1C,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,0CAA0C,CAAC,aAAqB,EAAA;QACnE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,IAAI,IAAI,GAAW,EAAE,CAAC,YAAY,CAAC;QACnC,IAAI,aAAa,KAAK,IAAA,KAAS,CAAC,qBAAqB,EAAE,CAAC;YACpD,IAAI,GAAG,EAAE,CAAC,cAAc,CAAC;QAC7B,CAAC,MAAM,IAAI,aAAa,KAAK,MAAA,GAAS,CAAC,cAAA,IAAA,iBAAmC,IAAI,aAAa,KAAK,SAAS,CAAC,8BAA8B,EAAE,CAAC;YACvI,IAAI,GAAG,EAAE,CAAC,iBAAiB,CAAC;QAChC,CAAC,MAAM,IAAI,aAAa,KAAK,IAAA,KAAS,CAAC,2BAA2B,EAAE,CAAC;YACjE,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC;QACpB,CAAC,MAAM,IAAI,aAAa,KAAK,IAAA,KAAS,CAAC,mCAAmC,EAAE,CAAC;YACzE,IAAI,GAAG,EAAE,CAAC,8BAA8B,CAAC;QAC7C,CAAC,MAAM,IAAI,aAAa,KAAK,IAAA,KAAS,CAAC,sBAAsB,EAAE,CAAC;YAC5D,IAAI,GAAG,EAAE,CAAC,aAAa,CAAC;QAC5B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,iCAAiC,CACpC,qBAA8B,EAC9B,mBAA4B,EAC5B,KAAa,EACb,MAAc,EACd,OAAO,GAAG,CAAC,EACX,kBAA2B,EAC3B,iCAAiC,GAAG,KAAK,EAAA;QAEzC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,kBAAkB,GAAG,kBAAkB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,KAAA,EAAA,EAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAC;QAEtJ,MAAM,cAAc,GAAG,IAAI,CAAC,wCAAwC,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,qBAAqB,CAAC,CAAC;QAErI,kCAAkC;QAClC,IAAI,qBAAqB,IAAI,mBAAmB,EAAE,CAAC;YAC/C,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAAC;QACpK,CAAC;QACD,IAAI,mBAAmB,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;QAC1J,CAAC;QACD,IAAI,qBAAqB,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,iCAAiC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC;QAC5J,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,mBAAmB,CACtB,KAAa,EACb,MAAc,EACd,OAAe,EACf,cAAsB,EACtB,gBAAwB,EACxB,UAAkB,EAClB,YAAY,GAAG,IAAI,EAAA;QAEnB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,MAAM,YAAY,GAAG,EAAE,CAAC,kBAAkB,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;IACtI,CAAC;IAEM,mBAAmB,CACtB,YAAyC,EACzC,KAAa,EACb,MAAc,EACd,OAAe,EACf,cAAsB,EACtB,gBAAwB,EACxB,UAAkB,EAClB,YAAY,GAAG,IAAI,EAAA;QAEnB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QAEnD,IAAI,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC,8BAA8B,EAAE,CAAC;YACnD,EAAE,CAAC,8BAA8B,CAAC,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACjG,CAAC,MAAM,CAAC;YACJ,EAAE,CAAC,mBAAmB,CAAC,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;YACpB,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACf,EAAE,CAAC,gBAAgB,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAC/C,CAAC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG,CACI,eAAe,CAAC,OAAwB,EAAA;QAC3C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,gBAAkD,CAAC,CAAC;QAEhF,kBAAkB;QAClB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,iCAAiC;QACjC,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,OAAO,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QACrC,CAAC;QACD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QACrC,CAAC;QAED,6BAA6B;QAC7B,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QACzC,CAAC;IACL,CAAC;IAES,cAAc,CAAC,OAAuC,EAAA;QAC5D,OAAO,EAAE,OAAO,EAAE,CAAC;IACvB,CAAC;IAES,WAAW,CAAC,OAA+B,EAAA;QACjD,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO,EAAE,CAAC;0LACnC,cAAA,AAAW,EAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;YAC/B,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;QACnC,CAAC;IACL,CAAC;IAOD;;;OAGG,CACI,YAAY,CAAC,MAAc,EAAA;QAC9B,MAAM,oBAAoB,GAAG,MAAM,CAAC,kBAAkB,EAA0B,CAAC;QACjF,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACtC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACnD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAEnD,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC;YACzC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEO,uBAAuB,GAAA;QAC3B,IAAI,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,cAAc,EAAE,CAAC;YACtD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;YAChE,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC;QACtD,CAAC;IACL,CAAC;IAED;;OAEG,CACI,oBAAoB,CAAC,MAAc,EAAE,OAAkC,EAAE,oBAAoB,GAAG,KAAK,EAAE,KAAK,GAAG,KAAK,EAAA;QACvH,IAAI,kBAAkB,GAAG,KAAK,CAAC;QAC/B,MAAM,qBAAqB,GAAG,OAAO,IAAI,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC;QACzE,IAAI,oBAAoB,IAAI,qBAAqB,EAAE,CAAC;YAChD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,CAAC;QAED,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE1E,IAAI,mBAAmB,KAAK,OAAO,IAAI,KAAK,EAAE,CAAC;YAC3C,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAE/B,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBACjC,4EAA4E;sKAC5E,SAAM,CAAC,KAAK,CAAC;oBAAC,uDAAuD;oBAAE,MAAM;oBAAE,OAAO;iBAAC,CAAC,CAAC;gBACzF,4CAA4C;gBAC5C,MAAM,uDAAuD,CAAC;YAClE,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,kBAAkB,IAAI,IAAI,CAAC,CAAC;YACxF,CAAC;YAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,OAAO,CAAC;YAExD,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC;YACrD,CAAC;QACL,CAAC,MAAM,IAAI,oBAAoB,EAAE,CAAC;YAC9B,kBAAkB,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACnC,CAAC;QAED,IAAI,qBAAqB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACjD,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACvF,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG,CACI,YAAY,CAAC,OAAe,EAAE,OAAkC,EAAE,IAAY,EAAA;QACjF,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,kBAAkB,GAAG,OAAO,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;QAC/E,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG,CACI,iBAAiB,GAAA;QACpB,IAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,wBAAwB,EAAE,OAAO,EAAE,CAAE,CAAC;YACvE,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAC9B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAC3D,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAC/D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACI,UAAU,CAAC,OAAe,EAAE,OAAuC,EAAE,OAA8B,EAAE,IAAY,EAAA;QACpH,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAEO,4BAA4B,CAAC,UAAkB,EAAE,WAAmB,EAAA;QACxE,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,aAAa,KAAK,WAAW,EAAE,CAAC;YACpD,OAAO;QACX,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QACzC,OAAO,CAAC,aAAa,GAAG,WAAW,CAAC;IACxC,CAAC;IAEO,mBAAmB,CAAC,IAAY,EAAA;QACpC,OAAQ,IAAI,EAAE,CAAC;YACX,KAAK,SAAS,CAAC,wBAAwB;gBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;YAClC,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;QACxC,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;IAC3B,CAAC;IAEe,WAAW,CAAC,OAAe,EAAE,OAA8B,EAAE,oBAAoB,GAAG,KAAK,EAAE,mBAAmB,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE,EAAA;QAC7I,aAAa;QACb,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC5C,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;gBAC9B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;gBAC3D,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;oBACxB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;oBACrD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;YACD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,QAAQ;QACR,IAAmB,OAAQ,CAAC,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;YAC9B,MAAM,oBAAoB,GAAkB,OAAQ,CAAC,kBAAkB,EAAE,CAAC;YAC1E,IAAI,oBAAoB,EAAE,CAAC;gBACvB,oBAAoB,CAAC,kBAAkB,GAAG,OAAO,CAAC;YACtD,CAAC;YACc,OAAQ,CAAC,MAAM,EAAE,CAAC;QACrC,CAAC,MAAM,IAAI,OAAO,CAAC,cAAc,KAAK,GAAA,MAAS,CAAC,wBAAwB,EAAE,CAAC;YACvE,gBAAgB;YAChB,OAAO,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,eAAgC,CAAC;QACrC,IAAI,mBAAmB,EAAE,CAAC;YACtB,eAAe,GAAyB,OAAQ,CAAC,mBAAoB,CAAC;QAC1E,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3B,eAAe,GAAoB,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACpE,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACxB,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC5C,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACtB,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,CAAC,MAAM,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC/C,CAAC,MAAM,CAAC;YACJ,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,oBAAoB,IAAI,eAAe,EAAE,CAAC;YAC3C,eAAe,CAAC,kBAAkB,GAAG,OAAO,CAAC;QACjD,CAAC;QAED,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,KAAK,eAAe,EAAE,CAAC;YACxD,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBACxB,IAAI,CAAC,4BAA4B,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;YACnF,CAAC;YAED,UAAU,GAAG,KAAK,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;QACvD,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,eAAe,EAAE,oBAAoB,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,eAAe,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC;YAClD,sFAAsF;YACtF,IAAI,eAAe,CAAC,MAAM,IAAI,eAAe,CAAC,sBAAsB,KAAK,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC/F,eAAe,CAAC,sBAAsB,GAAG,OAAO,CAAC,eAAe,CAAC;gBAEjE,MAAM,eAAe,GACjB,OAAO,CAAC,eAAe,KAAK,KAAA,IAAS,CAAC,GAAA,eAAkB,IAAI,CAAA,IACtD,EAD6D,CAAC,CAE9D,KADS,CAAC,GACD,CAAC,IAFmE,KAAK,SAAS,CAAC,CAC1D,KACC,CAAC,YAF2E;gBAGzH,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC;gBAChC,OAAO,CAAC,KAAK,GAAG,eAAe,CAAC;YACpC,CAAC;YAED,IAAI,eAAe,CAAC,YAAY,KAAK,OAAO,CAAC,KAAK,EAAE,CAAC;gBACjD,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,eAAe,CAAC,CAAC;YAChI,CAAC;YAED,IAAI,eAAe,CAAC,YAAY,KAAK,OAAO,CAAC,KAAK,EAAE,CAAC;gBACjD,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,eAAe,CAAC,CAAC;YAChI,CAAC;YAED,IAAI,eAAe,CAAC,IAAI,IAAI,eAAe,CAAC,YAAY,KAAK,OAAO,CAAC,KAAK,EAAE,CAAC;gBACzE,eAAe,CAAC,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC7C,IAAI,CAAC,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,eAAe,CAAC,CAAC;YAChI,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,eAAe,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,eAAe,CAAC,OAAe,EAAE,OAAuC,EAAE,QAAuB,EAAE,IAAY,EAAA;QAClH,IAAI,OAAO,KAAK,SAAS,IAAI,CAAC,OAAO,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvE,IAAI,CAAC,aAAa,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACzD,CAAC;QACD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACvC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,kBAAkB,EAAE,CAAC;YAEjD,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC;gBACpC,OAAO,CAAC,kBAAkB,GAAG,OAAO,GAAG,CAAC,CAAC;YAC7C,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,CAAC;QACL,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAEjD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACnD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAED;;OAEG,CACI,oBAAoB,CAAC,MAAc,EAAE,eAAgC,EAAE,yBAAiC,EAAA;QAC3G,MAAM,0BAA0B,GAAG,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC;QAChF,IACI,eAAe,CAAC,YAAY,KAAK,MACjC,GAD0C,CAAC,WAC5B,CAAC,YAAY,KAAK,GAD0C,EAE3E,IAD0C,CAAC,UAC5B,CAAC,YAAY,KAAK,GADyC,AACzC,MAAS,CAAC,qBAAqB,EAClE,CAAC;YACC,yBAAyB,GAAG,CAAC,CAAC,CAAC,+EAA+E;QAClH,CAAC;QAED,IAAI,0BAA0B,IAAI,eAAe,CAAC,gCAAgC,KAAK,yBAAyB,EAAE,CAAC;YAC/G,IAAI,CAAC,yBAAyB,CAC1B,MAAM,EACN,0BAA0B,CAAC,0BAA0B,EACrD,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAC7D,eAAe,CAClB,CAAC;YACF,eAAe,CAAC,gCAAgC,GAAG,yBAAyB,CAAC;QACjF,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,MAAc,EAAE,SAAiB,EAAE,KAAa,EAAE,OAAwB,EAAA;QACxG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAEO,2BAA2B,CAAC,MAAc,EAAE,SAAiB,EAAE,KAAa,EAAE,OAAyB,EAAA;QAC3G,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC3D,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG,CACI,mBAAmB,GAAA;QACtB,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;YAEvC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAE,CAAC;gBACnD,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC;YACD,OAAO;QACX,CAAC;QAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC;YACvE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1E,SAAS;YACb,CAAC;YAED,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;OAEG,CACI,cAAc,GAAA;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAA,CAAE,CAAC;QAC3B,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG,CACa,OAAO,GAAA;QACnB,SAAS;QACT,QAAI,mLAAA,AAAmB,EAAE,GAAE,CAAC;YACxB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAEnF,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,sBAAsB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAC/F,CAAC;YACL,CAAC;QACL,CAAC;QAED,0DAA0D;QAC1D,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACvD,CAAC;QAED,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,CAAA,CAAE,CAAC;QAEzB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAK,IAAI,CAAC,gBAAkC,CAAC,oBAAoB,EAAE,CAAC;YAChE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,oBAAoB,CAAC,EAAE,WAAW,EAAE,CAAC;QAC/D,CAAC;QACD,yBAAyB;SACzB,iMAAA,AAAiB,EAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG,CACI,sBAAsB,CAAC,QAA4C,EAAA;QACtE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,kBAAkB,EAAO,QAAQ,EAAE,KAAK,CAAC,CAAC;QACrF,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,0BAA0B,CAAC,QAA4C,EAAA;QAC1E,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,sBAAsB,EAAO,QAAQ,EAAE,KAAK,CAAC,CAAC;QACzF,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,QAAQ,GAAA;QACX,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;IAC/B,CAAC;IAEO,4BAA4B,GAAA;QAChC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;QACvC,CAAC;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;IACrE,CAAC;IAEO,gCAAgC,GAAA;QACpC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC;QACvC,CAAC;QACD,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;IAC1E,CAAC;IAED,qGAAqG;IAC7F,uBAAuB,CAAC,IAAY,EAAA;QACxC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAEpB,uBAAuB;QACvB,oCAAoC;QACpC,MAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAE,CAAC,CAAC;QAExC,IAAI,UAAU,GAAG,IAAI,CAAC;QAEtB,MAAM,OAAO,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;QACnC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QACvC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QACvI,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;QACnE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;QAEnE,MAAM,EAAE,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAClC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QACvC,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QACzF,MAAM,MAAM,GAAG,EAAE,CAAC,sBAAsB,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC;QAEzD,UAAU,GAAG,UAAU,IAAI,MAAM,KAAK,EAAE,CAAC,oBAAoB,CAAC;QAC9D,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC;QAEzD,oDAAoD;QACpD,IAAI,UAAU,EAAE,CAAC;YACb,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;YAC9B,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC;QAC7D,CAAC;QAED,iIAAiI;QACjI,IAAI,UAAU,EAAE,CAAC;YACb,6HAA6H;YAC7H,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YACzC,MAAM,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC;YAC3B,MAAM,QAAQ,GAAG,EAAE,CAAC,aAAa,CAAC;YAClC,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YACjC,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACxD,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC;QAC7D,CAAC;QAED,UAAU;QACV,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC1B,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;QACzB,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAEzC,0BAA0B;QAC1B,oCAAoC;QACpC,MAAO,CAAC,UAAU,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAE,CAAC,CAAC;QAEvD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG,CACI,oBAAoB,CAAC,IAAY,EAAA;QACpC,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAQ,IAAI,EAAE,CAAC;gBACX,KAAK,SAAS,CAAC,iBAAiB;oBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC1B,KAAK,SAAS,CAAC,sBAAsB;oBACjC,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;gBACnC,KAAK,SAAS,CAAC,yBAAyB;oBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;gBAClC,KAAK,SAAS,CAAC,kCAAkC;oBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC;gBAC3C,KAAK,SAAS,CAAC,kCAAkC;oBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC;gBAC3C,KAAK,SAAS,CAAC,gCAAgC;oBAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC;YAC7C,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;QAClC,CAAC;QAED,OAAQ,IAAI,EAAE,CAAC;YACX,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;YACzB,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;YAClC,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACnC,KAAK,SAAS,CAAC,eAAe;gBAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACxB,KAAK,SAAS,CAAC,4BAA4B,EAAE,yBAAyB;gBAClE,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC;YACjC,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,KAAK,SAAS,CAAC,sBAAsB;gBACjC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;YAC/B,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC;YAC3C,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAC;YAC3C,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC;YACzC,KAAK,SAAS,CAAC,uCAAuC;gBAClD,OAAO,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC;YAChD,KAAK,SAAS,CAAC,6BAA6B;gBACxC,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC;YACtC,KAAK,SAAS,CAAC,wCAAwC;gBACnD,OAAO,IAAI,CAAC,GAAG,CAAC,4BAA4B,CAAC;YACjD,KAAK,SAAS,CAAC,oCAAoC;gBAC/C,OAAO,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC;YAC7C,KAAK,SAAS,CAAC,0CAA0C;gBACrD,OAAO,IAAI,CAAC,GAAG,CAAC,8BAA8B,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;IAClC,CAAC;IAED;;OAEG,CACI,kBAAkB,CAAC,MAAc,EAAE,aAAa,GAAG,KAAK,EAAA;QAC3D,IAAI,cAAc,GAAW,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QAEtG,OAAQ,MAAM,EAAE,CAAC;YACb,KAAK,SAAS,CAAC,mBAAmB;gBAC9B,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAChC,MAAM;YACV,KAAK,SAAS,CAAC,uBAAuB;gBAClC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;gBACpC,MAAM;YACV,KAAK,SAAS,CAAC,6BAA6B;gBACxC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;gBAC1C,MAAM;YACV,KAAK,SAAS,CAAC,iBAAiB,CAAC;YACjC,KAAK,SAAS,CAAC,uBAAuB,CAAC;YACvC,KAAK,SAAS,CAAC,uBAAuB;gBAClC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBAC9B,MAAM;YACV,KAAK,SAAS,CAAC,gBAAgB,CAAC;YAChC,KAAK,SAAS,CAAC,wBAAwB,CAAC;YACxC,KAAK,SAAS,CAAC,wBAAwB;gBACnC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM;YACV,KAAK,SAAS,CAAC,iBAAiB,CAAC;YACjC,KAAK,SAAS,CAAC,yBAAyB,CAAC;YACzC,KAAK,SAAS,CAAC,yBAAyB;gBACpC,cAAc,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBACjF,MAAM;YACV,KAAK,SAAS,CAAC,kBAAkB,CAAC;YAClC,KAAK,SAAS,CAAC,0BAA0B,CAAC;YAC1C,KAAK,SAAS,CAAC,0BAA0B;gBACrC,cAAc,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC1F,MAAM;QACd,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YACzB,OAAQ,MAAM,EAAE,CAAC;gBACb,KAAK,SAAS,CAAC,yBAAyB;oBACpC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;oBACtC,MAAM;gBACV,KAAK,SAAS,CAAC,wBAAwB;oBACnC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;oBACrC,MAAM;gBACV,KAAK,SAAS,CAAC,yBAAyB;oBACpC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;oBACtC,MAAM;gBACV,KAAK,SAAS,CAAC,0BAA0B;oBACrC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC;oBACvC,MAAM;YACd,CAAC;QACL,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;OAEG,CACI,iCAAiC,CAAC,IAAY,EAAE,MAAe,EAAE,aAAa,GAAG,KAAK,EAAA;QACzF,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACvB,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,mBAAmB;wBAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,uBAAuB;wBAClC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC9B,KAAK,SAAS,CAAC,6BAA6B;wBACxC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;oBACpC,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBAC/E,CAAC;YACL,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QACzB,CAAC;QAED,OAAQ,IAAI,EAAE,CAAC;YACX,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC7B,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC9B,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;oBAC/B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;oBACxB,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC;gBACpC,CAAC;YACL,KAAK,SAAS,CAAC,yBAAyB;gBACpC,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvB,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;oBACxB,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qDAAqD;oBACnI,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,oEAAoE;oBAC1J,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B,KAAK,SAAS,CAAC,mBAAmB;wBAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,uBAAuB;wBAClC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC9B,KAAK,SAAS,CAAC,6BAA6B;wBACxC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;oBACpC;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC9B,CAAC;YACL,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,uBAAuB;wBAClC,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;oBAClC,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;oBACnC,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC;oBACpC,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;oBACrC,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;gBAChC,CAAC;YACL,KAAK,SAAS,CAAC,0BAA0B;gBACrC,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,uBAAuB;wBAClC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC7B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;oBAC9B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;oBAC/B,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC7B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACjC,CAAC;YACL,KAAK,SAAS,CAAC,eAAe;gBAC1B,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;gBAChC,CAAC;YACL,KAAK,SAAS,CAAC,4BAA4B,EAAE,yBAAyB;gBAClE,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,wBAAwB;wBACnC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBAC3B,KAAK,SAAS,CAAC,yBAAyB;wBACpC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;oBAC7B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACjC,CAAC;YACL,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,yCAAyC;oBACnE,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,0CAA0C;oBACrE,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,uEAAuE;oBACnG,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,4CAA4C;oBACzE;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;gBAChC,CAAC;YACL,KAAK,SAAS,CAAC,sBAAsB;gBACjC,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;oBACzB,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAC1B,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,+DAA+D;oBAC3F,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAC5B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;gBAChC,CAAC;YACL,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,KAAK,SAAS,CAAC,wCAAwC;gBACnD,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACnC,KAAK,SAAS,CAAC,oCAAoC;gBAC/C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAC5B,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;YAC1B,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAC5B,KAAK,SAAS,CAAC,uCAAuC;gBAClD,OAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,4CAA4C;oBAC1E,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;oBAC/B;wBACI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;gBACjC,CAAC;QACT,CAAC;QAED,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;IACrF,CAAC;IAED;;;;;;;;;;OAUG,CACH,oFAAoF;IACpF,qEAAqE;IAC9D,UAAU,CAAC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAE,QAAQ,GAAG,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,OAA6B,IAAI,EAAA;QAC3I,MAAM,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QAEvD,MAAM,UAAU,GAAG,KAAK,GAAG,MAAM,GAAG,WAAW,CAAC;QAChD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,IAAI,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;QACtC,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;kKAClC,SAAM,CAAC,KAAK,CAAC,CAAA,mDAAA,EAAsD,IAAI,CAAC,MAAM,CAAA,qBAAA,EAAwB,UAAU,CAAA,CAAA,CAAG,CAAC,CAAC;YACrH,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAC/E,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAOD;;OAEG,CACH,gDAAgD;IACzC,MAAM,KAAK,gBAAgB,GAAA;QAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,WAAW,GAAA;QACzB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,kBAAkB;IACjD,CAAC;IAED;;;OAGG,CACH,gEAAgE;IACzD,MAAM,CAAC,WAAW,GAAA;QACrB,IAAI,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE,CAAC;YAC3C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,iDAAiD;QAC9F,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACD,MAAM,UAAU,oKAAG,iBAAc,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtD,MAAM,EAAE,GAAG,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,IAAK,UAAkB,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;gBAElG,IAAI,CAAC,YAAY,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC;YACrE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;gBACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC9B,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,yBAAyB,GAAA;QACvC,IAAI,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE,CAAC;YAC3C,IAAI,CAAC;gBACD,MAAM,UAAU,oKAAG,iBAAc,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtD,MAAM,EAAE,GACJ,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE;oBAAE,4BAA4B,EAAE,IAAI;gBAAA,CAAE,CAAC,IACrE,UAAkB,CAAC,UAAU,CAAC,oBAAoB,EAAE;oBAAE,4BAA4B,EAAE,IAAI;gBAAA,CAAE,CAAC,CAAC;gBAEjG,IAAI,CAAC,0BAA0B,GAAG,CAAC,EAAE,CAAC;YAC1C,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;gBACT,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;YAC5C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;;AA35Ic,WAAA,qBAAqB,GAAG,IAAI,WAAW,CAAC,CAAC,CAApB,AAAqB,CAAC;AAC3C,WAAA,oBAAoB,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,AAApB,CAAqB;AAExD,oFAAA,EAAsF,CACxE,WAAA,aAAa,GAAG;IAC1B;QAAE,GAAG,EAAE,aAAa;QAAE,OAAO,EAAE,wBAAwB;QAAE,iBAAiB,EAAE,GAAG;QAAE,OAAO,EAAE;YAAC,eAAe;SAAC;IAAA,CAAE;IAC7G;QAAE,GAAG,EAAE,YAAY;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,eAAe;SAAC;IAAA,CAAE;IACzF;QAAE,GAAG,EAAE,YAAY;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,eAAe;SAAC;IAAA,CAAE;IACzF;QAAE,GAAG,EAAE,oBAAoB;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,KAAK;SAAC;IAAA,CAAE;IACvF;QAAE,GAAG,EAAE,oBAAoB;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,KAAK;SAAC;IAAA,CAAE;IACvF;QAAE,GAAG,EAAE,oBAAoB;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,KAAK;SAAC;IAAA,CAAE;IACvF;QAAE,GAAG,EAAE,mBAAmB;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,KAAK;SAAC;IAAA,CAAE;IACtF;QAAE,GAAG,EAAE,mBAAmB;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,KAAK;SAAC;IAAA,CAAE;IACtF;QAAE,GAAG,EAAE,gBAAgB;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,eAAe;SAAC;IAAA,CAAE;IAC7F;QAAE,GAAG,EAAE,0BAA0B;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,eAAe;SAAC;IAAA,CAAE;IACvG,0BAA0B;IAC1B;QAAE,GAAG,EAAE,+BAA+B;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,WAAW;YAAE,gBAAgB;SAAC;IAAA,CAAE;IAC1H,2CAA2C;IAC3C;QAAE,GAAG,EAAE,+BAA+B;QAAE,OAAO,EAAE,IAAI;QAAE,iBAAiB,EAAE,IAAI;QAAE,OAAO,EAAE;YAAC,WAAW;YAAE,gBAAgB;SAAC;IAAA,CAAE;CAdnG,CAezB;AAm4DF,gEAAgE;AAC/C,WAAA,kBAAkB,iLAAG,qBAAH,CAAsB;AAs8EzD,UAAU;AAEK,WAAA,YAAY,GAAsB,IAAI,AAA1B,CAA2B;AACvC,WAAA,0BAA0B,GAAsB,IAAI,AAA1B,CAA2B", "debugId": null}}]}