{"version": 3, "file": "spatialAudio.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/abstractAudio/subProperties/spatialAudio.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,MAAM,iCAAiC,CAAC;AACpG,OAAO,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAErF,gBAAgB;AAChB,MAAM,OAAgB,aAAc,SAAQ,oBAAoB;IAc5D,gBAAgB;IAChB,YAAmB,QAAgC;QAC/C,KAAK,EAAE,CAAC;QAfJ,oBAAe,GAAW,qBAAqB,CAAC,cAAc,CAAC;QAC/D,oBAAe,GAAW,qBAAqB,CAAC,cAAc,CAAC;QAC/D,qBAAgB,GAAW,qBAAqB,CAAC,eAAe,CAAC;QACjE,mBAAc,GAAsB,qBAAqB,CAAC,aAAa,CAAC;QACxE,iBAAY,GAAW,qBAAqB,CAAC,WAAW,CAAC;QACzD,iBAAY,GAAW,qBAAqB,CAAC,WAAW,CAAC;QACzD,kBAAa,GAAqB,qBAAqB,CAAC,YAAY,CAAC;QAErE,mBAAc,GAAW,qBAAqB,CAAC,aAAa,CAAC;QASjE,MAAM,OAAO,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAClE,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACxD,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACxD,IAAI,CAAC,mBAAmB,GAAG,qBAAqB,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;YAE5E,mEAAmE;YACnE,QAAQ,CAAC,wBAAwB,sCAAsB,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAED,gBAAgB;IAChB,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,cAAc,CAAC,KAAa;QACnC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACtE,CAAC;IAED,gBAAgB;IAChB,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,cAAc,CAAC,KAAa;QACnC,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7B,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACtE,CAAC;IAED,gBAAgB;IAChB,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,IAAW,eAAe,CAAC,KAAa;QACpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IAED,gBAAgB;IAChB,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAwB;QAC7C,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED,gBAAgB;IAChB,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,sCAA4C,EAAE,UAAU,IAAI,KAAK,CAAC;IACtG,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACb,KAAK,GAAG,QAAQ,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;IACnE,CAAC;IAED,gBAAgB;IAChB,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAAuB;QAC3C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAc;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,gBAAgB;IAChB,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAc;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,gBAAgB;IAChB,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAW,kBAAkB,CAAC,KAAiB;QAC3C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,SAAyB,EAAE,iBAA0B,KAAK,EAAE,uEAA2F;QACjK,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,SAAS,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;IAC/F,CAAC;IAED;;OAEG;IACI,MAAM;QACT,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,MAAM;QACT,MAAM,OAAO,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAExD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO;QACX,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO,CAAC,MAAM,EAAE,CAAC;QACrB,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC9B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,UAA0C,IAAI;QAClE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAElD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO;YACX,CAAC;QACL,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9C,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1C,OAAO,CAAC,eAAe,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,UAA0C,IAAI;QAClE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAElD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO;YACX,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC1E,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC9D,OAAO,CAAC,eAAe,EAAE,CAAC;QAC9B,CAAC;aAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7D,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1C,OAAO,CAAC,eAAe,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["import type { Quaternion, Vector3 } from \"../../../Maths/math.vector\";\nimport type { Node } from \"../../../node\";\nimport type { Nullable } from \"../../../types\";\nimport { SpatialAudioAttachmentType } from \"../../spatialAudioAttachmentType\";\nimport type { _AbstractAudioSubGraph } from \"../subNodes/abstractAudioSubGraph\";\nimport { AudioSubNode } from \"../subNodes/audioSubNode\";\nimport type { _SpatialAudioSubNode } from \"../subNodes/spatialAudioSubNode\";\nimport { _GetSpatialAudioSubNode, _SetSpatialAudioProperty } from \"../subNodes/spatialAudioSubNode\";\nimport { _SpatialAudioDefaults, AbstractSpatialAudio } from \"./abstractSpatialAudio\";\n\n/** @internal */\nexport abstract class _SpatialAudio extends AbstractSpatialAudio {\n    private _coneInnerAngle: number = _SpatialAudioDefaults.coneInnerAngle;\n    private _coneOuterAngle: number = _SpatialAudioDefaults.coneOuterAngle;\n    private _coneOuterVolume: number = _SpatialAudioDefaults.coneOuterVolume;\n    private _distanceModel: DistanceModelType = _SpatialAudioDefaults.distanceModel;\n    private _maxDistance: number = _SpatialAudioDefaults.maxDistance;\n    private _minDistance: number = _SpatialAudioDefaults.minDistance;\n    private _panningModel: PanningModelType = _SpatialAudioDefaults.panningModel;\n    private _position: Vector3;\n    private _rolloffFactor: number = _SpatialAudioDefaults.rolloffFactor;\n    private _rotation: Vector3;\n    private _rotationQuaternion: Quaternion;\n    private _subGraph: _AbstractAudioSubGraph;\n\n    /** @internal */\n    public constructor(subGraph: _AbstractAudioSubGraph) {\n        super();\n\n        const subNode = _GetSpatialAudioSubNode(subGraph);\n        if (subNode) {\n            this._position = subNode.position.clone();\n            this._rotation = subNode.rotation.clone();\n            this._rotationQuaternion = subNode.rotationQuaternion.clone();\n        } else {\n            this._position = _SpatialAudioDefaults.position.clone();\n            this._rotation = _SpatialAudioDefaults.rotation.clone();\n            this._rotationQuaternion = _SpatialAudioDefaults.rotationQuaternion.clone();\n\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            subGraph.createAndAddSubNodeAsync(AudioSubNode.SPATIAL);\n        }\n\n        this._subGraph = subGraph;\n    }\n\n    /** @internal */\n    public get coneInnerAngle(): number {\n        return this._coneInnerAngle;\n    }\n\n    public set coneInnerAngle(value: number) {\n        this._coneInnerAngle = value;\n        _SetSpatialAudioProperty(this._subGraph, \"coneInnerAngle\", value);\n    }\n\n    /** @internal */\n    public get coneOuterAngle(): number {\n        return this._coneOuterAngle;\n    }\n\n    public set coneOuterAngle(value: number) {\n        this._coneOuterAngle = value;\n        _SetSpatialAudioProperty(this._subGraph, \"coneOuterAngle\", value);\n    }\n\n    /** @internal */\n    public get coneOuterVolume(): number {\n        return this._coneOuterVolume;\n    }\n\n    public set coneOuterVolume(value: number) {\n        this._coneOuterVolume = value;\n        _SetSpatialAudioProperty(this._subGraph, \"coneOuterVolume\", value);\n    }\n\n    /** @internal */\n    public get distanceModel(): DistanceModelType {\n        return this._distanceModel;\n    }\n\n    public set distanceModel(value: DistanceModelType) {\n        this._distanceModel = value;\n        _SetSpatialAudioProperty(this._subGraph, \"distanceModel\", value);\n    }\n\n    /** @internal */\n    public get isAttached(): boolean {\n        return this._subGraph.getSubNode<_SpatialAudioSubNode>(AudioSubNode.SPATIAL)?.isAttached ?? false;\n    }\n\n    /** @internal */\n    public get maxDistance(): number {\n        return this._maxDistance;\n    }\n\n    public set maxDistance(value: number) {\n        if (value <= 0) {\n            value = 0.000001;\n        }\n\n        this._maxDistance = value;\n        _SetSpatialAudioProperty(this._subGraph, \"maxDistance\", value);\n    }\n\n    /** @internal */\n    public get minDistance(): number {\n        return this._minDistance;\n    }\n\n    public set minDistance(value: number) {\n        this._minDistance = value;\n        _SetSpatialAudioProperty(this._subGraph, \"minDistance\", value);\n    }\n\n    /** @internal */\n    public get panningModel(): PanningModelType {\n        return this._panningModel;\n    }\n\n    public set panningModel(value: PanningModelType) {\n        this._panningModel = value;\n        _SetSpatialAudioProperty(this._subGraph, \"panningModel\", value);\n    }\n\n    /** @internal */\n    public get position(): Vector3 {\n        return this._position;\n    }\n\n    public set position(value: Vector3) {\n        this._position = value;\n        this._updatePosition();\n    }\n\n    /** @internal */\n    public get rolloffFactor(): number {\n        return this._rolloffFactor;\n    }\n\n    public set rolloffFactor(value: number) {\n        this._rolloffFactor = value;\n        _SetSpatialAudioProperty(this._subGraph, \"rolloffFactor\", value);\n    }\n\n    /** @internal */\n    public get rotation(): Vector3 {\n        return this._rotation;\n    }\n\n    public set rotation(value: Vector3) {\n        this._rotation = value;\n        this._updateRotation();\n    }\n\n    /** @internal */\n    public get rotationQuaternion(): Quaternion {\n        return this._rotationQuaternion;\n    }\n\n    public set rotationQuaternion(value: Quaternion) {\n        this._rotationQuaternion = value;\n        this._updateRotation();\n    }\n\n    /**\n     * Attaches to a scene node.\n     *\n     * Detaches automatically before attaching to the given scene node.\n     * If `sceneNode` is `null` it is the same as calling `detach()`.\n     *\n     * @param sceneNode The scene node to attach to, or `null` to detach.\n     * @param useBoundingBox Whether to use the bounding box of the node for positioning. Defaults to `false`.\n     * @param attachmentType Whether to attach to the node's position and/or rotation. Defaults to `PositionAndRotation`.\n     */\n    public attach(sceneNode: Nullable<Node>, useBoundingBox: boolean = false, attachmentType: SpatialAudioAttachmentType = SpatialAudioAttachmentType.PositionAndRotation): void {\n        _GetSpatialAudioSubNode(this._subGraph)?.attach(sceneNode, useBoundingBox, attachmentType);\n    }\n\n    /**\n     * Detaches from the scene node if attached.\n     */\n    public detach(): void {\n        _GetSpatialAudioSubNode(this._subGraph)?.detach();\n    }\n\n    /** @internal */\n    public update(): void {\n        const subNode = _GetSpatialAudioSubNode(this._subGraph);\n\n        if (!subNode) {\n            return;\n        }\n\n        if (subNode.isAttached) {\n            subNode.update();\n        } else {\n            this._updatePosition(subNode);\n            this._updateRotation(subNode);\n        }\n    }\n\n    private _updatePosition(subNode: Nullable<_SpatialAudioSubNode> = null): void {\n        if (!subNode) {\n            subNode = _GetSpatialAudioSubNode(this._subGraph);\n\n            if (!subNode) {\n                return;\n            }\n        }\n\n        const position = subNode.position;\n        if (!position.equalsWithEpsilon(this._position)) {\n            subNode.position.copyFrom(this._position);\n            subNode._updatePosition();\n        }\n    }\n\n    private _updateRotation(subNode: Nullable<_SpatialAudioSubNode> = null): void {\n        if (!subNode) {\n            subNode = _GetSpatialAudioSubNode(this._subGraph);\n\n            if (!subNode) {\n                return;\n            }\n        }\n\n        if (!subNode.rotationQuaternion.equalsWithEpsilon(this._rotationQuaternion)) {\n            subNode.rotationQuaternion.copyFrom(this._rotationQuaternion);\n            subNode._updateRotation();\n        } else if (!subNode.rotation.equalsWithEpsilon(this._rotation)) {\n            subNode.rotation.copyFrom(this._rotation);\n            subNode._updateRotation();\n        }\n    }\n}\n"]}