{"version": 3, "file": "webAudioSoundSource.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/webAudio/webAudioSoundSource.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,mBAAmB,EAAE,MAAM,sCAAsC,CAAC;AAC3E,OAAO,EAAE,uBAAuB,EAAE,MAAM,qDAAqD,CAAC;AAE9F,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAInE,gBAAgB;AAChB,MAAM,OAAO,oBAAqB,SAAQ,mBAAmB;IAezD,gBAAgB;IAChB,YAAmB,IAAY,EAAE,YAAuB,EAAE,MAAuB,EAAE,OAAqC;QACpH,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAhBhB,aAAQ,GAA+B,IAAI,CAAC;QACnC,uBAAkB,GAAY,IAAI,CAAC;QACnC,0BAAqB,GAAW,CAAC,CAAC;QAC3C,YAAO,GAA2B,IAAI,CAAC;QAe3C,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,UAAU,CAAC,OAAqC;QACzD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,CAAC;aAAM,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,IAAI,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,IAAoB,OAAO;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED,gBAAgB;IAChB,IAAoB,MAAM;QACtB,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAEzB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,sBAAsB,CAAC;IAClC,CAAC;IAEkB,QAAQ,CAAC,IAAqB;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9G,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;;AAEc,8BAAS,GAAG,KAAM,SAAQ,4BAA4B;IAGjE,IAAc,gBAAgB;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAChD,CAAC;IAED,IAAc,cAAc;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC;IAC9C,CAAC;IAEkB,kBAAkB;QACjC,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAE3B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC;CACJ,AApBuB,CAoBtB", "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { AbstractAudioNode } from \"../abstractAudio\";\nimport type { ISoundSourceOptions } from \"../abstractAudio/abstractSoundSource\";\nimport { AbstractSoundSource } from \"../abstractAudio/abstractSoundSource\";\nimport { _HasSpatialAudioOptions } from \"../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { _SpatialAudio } from \"../abstractAudio/subProperties/spatialAudio\";\nimport { _StereoAudio } from \"../abstractAudio/subProperties/stereoAudio\";\nimport { _WebAudioBusAndSoundSubGraph } from \"./subNodes/webAudioBusAndSoundSubGraph\";\nimport { _SpatialWebAudio } from \"./subProperties/spatialWebAudio\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode } from \"./webAudioNode\";\n\n/** @internal */\nexport class _WebAudioSoundSource extends AbstractSoundSource {\n    private _spatial: Nullable<_SpatialWebAudio> = null;\n    private readonly _spatialAutoUpdate: boolean = true;\n    private readonly _spatialMinUpdateTime: number = 0;\n    private _stereo: Nullable<_StereoAudio> = null;\n\n    protected _subGraph: _WebAudioBusAndSoundSubGraph;\n    protected _webAudioNode: AudioNode;\n\n    /** @internal */\n    public _audioContext: AudioContext | OfflineAudioContext;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(name: string, webAudioNode: AudioNode, engine: _WebAudioEngine, options: Partial<ISoundSourceOptions>) {\n        super(name, engine);\n\n        if (typeof options.spatialAutoUpdate === \"boolean\") {\n            this._spatialAutoUpdate = options.spatialAutoUpdate;\n        }\n\n        if (typeof options.spatialMinUpdateTime === \"number\") {\n            this._spatialMinUpdateTime = options.spatialMinUpdateTime;\n        }\n\n        this._audioContext = this.engine._audioContext;\n        this._webAudioNode = webAudioNode;\n\n        this._subGraph = new _WebAudioSoundSource._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(options: Partial<ISoundSourceOptions>): Promise<void> {\n        if (options.outBus) {\n            this.outBus = options.outBus;\n        } else if (options.outBusAutoDefault !== false) {\n            await this.engine.isReadyPromise;\n            this.outBus = this.engine.defaultMainBus;\n        }\n\n        await this._subGraph.initAsync(options);\n\n        if (_HasSpatialAudioOptions(options)) {\n            this._initSpatialProperty();\n        }\n\n        this.engine._addNode(this);\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._webAudioNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    /** @internal */\n    public override get spatial(): _SpatialAudio {\n        if (this._spatial) {\n            return this._spatial;\n        }\n        return this._initSpatialProperty();\n    }\n\n    /** @internal */\n    public override get stereo(): _StereoAudio {\n        return this._stereo ?? (this._stereo = new _StereoAudio(this._subGraph));\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._spatial?.dispose();\n        this._spatial = null;\n\n        this._stereo = null;\n\n        this._subGraph.dispose();\n\n        this.engine._removeNode(this);\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioSoundSource\";\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initSpatialProperty(): _SpatialAudio {\n        if (!this._spatial) {\n            this._spatial = new _SpatialWebAudio(this._subGraph, this._spatialAutoUpdate, this._spatialMinUpdateTime);\n        }\n\n        return this._spatial;\n    }\n\n    private static _SubGraph = class extends _WebAudioBusAndSoundSubGraph {\n        protected override _owner: _WebAudioSoundSource;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n\n        protected get _upstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._upstreamNodes ?? null;\n        }\n\n        protected override _onSubNodesChanged(): void {\n            super._onSubNodesChanged();\n\n            this._owner._inNode.disconnect();\n\n            if (this._owner._subGraph._inNode) {\n                this._owner._inNode.connect(this._owner._subGraph._inNode);\n            }\n        }\n    };\n}\n"]}