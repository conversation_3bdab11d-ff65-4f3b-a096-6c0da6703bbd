import type { ArcRotateCamera } from "../../Cameras/arcRotateCamera.js";
import type { ICameraInput } from "../../Cameras/cameraInputsManager.js";
/**
 * Manage the keyboard inputs to control the movement of an arc rotate camera.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs
 */
export declare class ArcRotateCameraKeyboardMoveInput implements ICameraInput<ArcRotateCamera> {
    /**
     * Defines the camera the input is attached to.
     */
    camera: ArcRotateCamera;
    /**
     * Defines the list of key codes associated with the up action (increase alpha)
     */
    keysUp: number[];
    /**
     * Defines the list of key codes associated with the down action (decrease alpha)
     */
    keysDown: number[];
    /**
     * Defines the list of key codes associated with the left action (increase beta)
     */
    keysLeft: number[];
    /**
     * Defines the list of key codes associated with the right action (decrease beta)
     */
    keysRight: number[];
    /**
     * Defines the list of key codes associated with the reset action.
     * Those keys reset the camera to its last stored state (with the method camera.storeState())
     */
    keysReset: number[];
    /**
     * Defines the panning sensibility of the inputs.
     * (How fast is the camera panning)
     */
    panningSensibility: number;
    /**
     * Defines the zooming sensibility of the inputs.
     * (How fast is the camera zooming)
     */
    zoomingSensibility: number;
    /**
     * Defines whether maintaining the alt key down switch the movement mode from
     * orientation to zoom.
     */
    useAltToZoom: boolean;
    /**
     * Rotation speed of the camera
     */
    angularSpeed: number;
    private _keys;
    private _ctrlPressed;
    private _altPressed;
    private _onCanvasBlurObserver;
    private _onKeyboardObserver;
    private _engine;
    private _scene;
    /**
     * Attach the input controls to a specific dom element to get the input from.
     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)
     */
    attachControl(noPreventDefault?: boolean): void;
    /**
     * Detach the current controls from the specified dom element.
     */
    detachControl(): void;
    /**
     * Update the current camera state depending on the inputs that have been used this frame.
     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.
     */
    checkInputs(): void;
    /**
     * Gets the class name of the current input.
     * @returns the class name
     */
    getClassName(): string;
    /**
     * Get the friendly name associated with the input class.
     * @returns the input friendly name
     */
    getSimpleName(): string;
}
