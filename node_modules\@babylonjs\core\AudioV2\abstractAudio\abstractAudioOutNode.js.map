{"version": 3, "file": "abstractAudioOutNode.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/abstractAudio/abstractAudioOutNode.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAI7D,OAAO,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,MAAM,+BAA+B,CAAC;AAEhG,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAK/D;;GAEG;AACH,MAAM,OAAgB,oBAAqB,SAAQ,sBAAsB;IAKrE,YAAsB,IAAY,EAAE,MAAqB,EAAE,QAAuB;QAC9E,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAL1B,cAAS,GAAoC,IAAI,CAAC;IAM1D,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG;IAEH,IAAW,MAAM;QACb,OAAO,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED,IAAW,MAAM,CAAC,KAAa;QAC3B,2EAA2E;QAC3E,MAAM,IAAI,GAAG,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAED;;OAEG;IACa,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,KAAa,EAAE,UAAyD,IAAI;QACzF,MAAM,IAAI,GAAG,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACnC,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { IAudioParameterRampOptions } from \"../audioParameter\";\nimport type { AudioNodeType } from \"./abstractAudioNode\";\nimport { AbstractNamedAudioNode } from \"./abstractAudioNode\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { _AbstractAudioSubGraph } from \"./subNodes/abstractAudioSubGraph\";\nimport type { IVolumeAudioOptions } from \"./subNodes/volumeAudioSubNode\";\nimport { _GetVolumeAudioProperty, _GetVolumeAudioSubNode } from \"./subNodes/volumeAudioSubNode\";\nimport type { AbstractAudioAnalyzer, IAudioAnalyzerOptions } from \"./subProperties/abstractAudioAnalyzer\";\nimport { _AudioAnalyzer } from \"./subProperties/audioAnalyzer\";\n\n/** @internal */\nexport interface IAbstractAudioOutNodeOptions extends IAudioAnalyzerOptions, IVolumeAudioOptions {}\n\n/**\n * Abstract class representing and audio output node with an analyzer and volume control.\n */\nexport abstract class AbstractAudioOutNode extends AbstractNamedAudioNode {\n    private _analyzer: Nullable<AbstractAudioAnalyzer> = null;\n\n    protected abstract _subGraph: _AbstractAudioSubGraph;\n\n    protected constructor(name: string, engine: AudioEngineV2, nodeType: AudioNodeType) {\n        super(name, engine, nodeType);\n    }\n\n    /**\n     * The analyzer features of the bus.\n     */\n    public get analyzer(): AbstractAudioAnalyzer {\n        return this._analyzer ?? (this._analyzer = new _AudioAnalyzer(this._subGraph));\n    }\n\n    /**\n     * The audio output volume.\n     */\n\n    public get volume(): number {\n        return _GetVolumeAudioProperty(this._subGraph, \"volume\");\n    }\n\n    public set volume(value: number) {\n        // The volume subnode is created on initialization and should always exist.\n        const node = _GetVolumeAudioSubNode(this._subGraph);\n        if (!node) {\n            throw new Error(\"No volume subnode\");\n        }\n\n        node.volume = value;\n    }\n\n    /**\n     * Releases associated resources.\n     */\n    public override dispose(): void {\n        super.dispose();\n\n        this._analyzer?.dispose();\n        this._analyzer = null;\n\n        this._subGraph.dispose();\n    }\n\n    /**\n     * Sets the audio output volume with optional ramping.\n     * If the duration is 0 then the volume is set immediately, otherwise it is ramped to the new value over the given duration using the given shape.\n     * If a ramp is already in progress then the volume is not set and an error is thrown.\n     * @param value The value to set the volume to.\n     * @param options The options to use for ramping the volume change.\n     */\n    public setVolume(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        const node = _GetVolumeAudioSubNode(this._subGraph);\n        if (!node) {\n            throw new Error(\"No volume subnode\");\n        }\n\n        node.setVolume(value, options);\n    }\n}\n"]}