{"version": 3, "file": "engine.alpha.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.alpha.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,gCAAoC;AAC7D,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C,OAAO,2CAA2C,CAAC;AACnD,OAAO,EAAE,gBAAgB,EAAE,kCAAsC;AAgBjE,gBAAgB,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,IAAY,EAAE,qBAA8B,KAAK,EAAE,cAAsB,CAAC;IAC1H,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAE7D,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,aAAa,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC;QACnJ,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,mKAAmK;YACnK,MAAM,SAAS,GAAG,IAAI,KAAK,SAAS,CAAC,aAAa,CAAC;YACnD,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACjD,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBAC9B,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAC9D,CAAC;QACL,CAAC;QACD,OAAO;IACX,CAAC;IAED,MAAM,kBAAkB,GAAG,IAAI,KAAK,SAAS,CAAC,aAAa,CAAC;IAE5D,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;IACjE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;IAEjD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtB,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QACvC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;IACvE,CAAC;IACD,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;IACpC,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACjH,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;AACzI,CAAC,CAAC;AAEF,gBAAgB,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,QAAgB,EAAE,cAAsB,CAAC;IAC7F,cAAc,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;IAE5E,IAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;AACzI,CAAC,CAAC", "sourcesContent": ["import { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport { Constants } from \"../../constants\";\r\n\r\nimport \"../../AbstractEngine/abstractEngine.alpha\";\r\nimport { ThinWebGPUEngine } from \"core/Engines/thinWebGPUEngine\";\r\n\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Sets the current alpha mode\r\n         * @param mode defines the mode to use (one of the Engine.ALPHA_XXX)\r\n         * @param noDepthWriteChange defines if depth writing state should remains unchanged (false by default)\r\n         * @param targetIndex defines the index of the target to set the alpha mode for (default is 0)\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/transparent_rendering\r\n         */\r\n        setAlphaMode(mode: number, noDepthWriteChange?: boolean, targetIndex?: number): void;\r\n    }\r\n}\r\n\r\nThinWebGPUEngine.prototype.setAlphaMode = function (mode: number, noDepthWriteChange: boolean = false, targetIndex: number = 0): void {\r\n    const alphaBlend = this._alphaState._alphaBlend[targetIndex];\r\n\r\n    if (this._alphaMode[targetIndex] === mode && ((mode === Constants.ALPHA_DISABLE && !alphaBlend) || (mode !== Constants.ALPHA_DISABLE && alphaBlend))) {\r\n        if (!noDepthWriteChange) {\r\n            // Make sure we still have the correct depth mask according to the alpha mode (a transparent material could have forced writting to the depth buffer, for instance)\r\n            const depthMask = mode === Constants.ALPHA_DISABLE;\r\n            if (this.depthCullingState.depthMask !== depthMask) {\r\n                this.setDepthWrite(depthMask);\r\n                this._cacheRenderPipeline.setDepthWriteEnabled(depthMask);\r\n            }\r\n        }\r\n        return;\r\n    }\r\n\r\n    const alphaBlendDisabled = mode === Constants.ALPHA_DISABLE;\r\n\r\n    this._alphaState.setAlphaBlend(!alphaBlendDisabled, targetIndex);\r\n    this._alphaState.setAlphaMode(mode, targetIndex);\r\n\r\n    if (!noDepthWriteChange) {\r\n        this.setDepthWrite(alphaBlendDisabled);\r\n        this._cacheRenderPipeline.setDepthWriteEnabled(alphaBlendDisabled);\r\n    }\r\n    this._alphaMode[targetIndex] = mode;\r\n    this._cacheRenderPipeline.setAlphaBlendEnabled(this._alphaState._alphaBlend, this._alphaState._numTargetEnabled);\r\n    this._cacheRenderPipeline.setAlphaBlendFactors(this._alphaState._blendFunctionParameters, this._alphaState._blendEquationParameters);\r\n};\r\n\r\nThinWebGPUEngine.prototype.setAlphaEquation = function (equation: number, targetIndex: number = 0): void {\r\n    AbstractEngine.prototype.setAlphaEquation.call(this, equation, targetIndex);\r\n\r\n    this._cacheRenderPipeline.setAlphaBlendFactors(this._alphaState._blendFunctionParameters, this._alphaState._blendEquationParameters);\r\n};\r\n"]}