{"version": 3, "file": "webgpuBufferManager.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuBufferManager.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,gBAAgB,EAAE,MAAM,sCAAsC,CAAC;AACxE,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AAExD,OAAO,EAAE,0BAA0B,EAAE,MAAM,6BAA6B,CAAC;AACzE,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,gEAAgE;AAChE,OAAO,KAAK,eAAe,MAAM,mBAAmB,CAAC;AAErD,gBAAgB;AAChB,MAAM,OAAO,mBAAmB;IAKpB,MAAM,CAAC,YAAY,CAAC,MAA8B;QACtD,OAAQ,MAAqB,CAAC,kBAAkB,KAAK,SAAS,CAAC;IACnE,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,KAA0B,EAAE,MAAM,GAAG,EAAE;QACjE,IAAI,MAAM,GAAG,MAAM,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;YAC1B,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACnB,IAAI,MAAM,EAAE,CAAC;oBACT,MAAM,IAAI,GAAG,CAAC;gBAClB,CAAC;gBACD,MAAM,IAAI,eAAe,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,YAAY,MAAoB,EAAE,MAAiB;QArB3C,4BAAuB,GAAqB,EAAE,CAAC;QAsBnD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAEM,eAAe,CAAC,UAAoC,EAAE,KAA0B,EAAE,gBAAgB,GAAG,KAAK,EAAE,KAAc;QAC7H,MAAM,aAAa,GAAI,UAA8B,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,CAAE,UAA8B,CAAC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,UAAqB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,iEAAiE;QAC7O,MAAM,wBAAwB,GAAG;YAC7B,KAAK,EAAE,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,GAAG,GAAG,mBAAmB,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,IAAI,QAAQ,CAAC,GAAG,OAAO,GAAG,aAAa;YACnJ,gBAAgB;YAChB,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,KAAK;SACf,CAAC;QAEF,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC;IAC/D,CAAC;IAEM,YAAY,CAAC,UAAoC,EAAE,KAA0B,EAAE,KAAc;QAChG,MAAM,MAAM,GAAI,UAA8B,CAAC,UAAU,KAAK,SAAS,CAAC;QACxE,MAAM,UAAU,GAAG,IAAI,gBAAgB,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAG,qBAAqB,GAAG,UAAU,CAAC,QAAQ,CAAC;QAC5D,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAChH,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC;QAC1B,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAE,UAA8B,CAAC,UAAU,CAAC,CAAC,CAAE,UAAqB,CAAC;QACnG,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QAE5C,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,EAAE,UAA6B,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,mEAAmE;IACnE,iHAAiH;IAC1G,UAAU,CAAC,MAAiB,EAAE,aAAqB,EAAE,GAAoB,EAAE,aAAqB,EAAE,UAAkB;QACvH,aAAa,IAAI,GAAG,CAAC,UAAU,CAAC;QAEhC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;IACjG,CAAC;IAED,oIAAoI;IACpI,mKAAmK;IAC5J,UAAU,CAAC,UAA4B,EAAE,aAAqB,EAAE,GAAoB,EAAE,aAAa,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC;QAC1H,MAAM,MAAM,GAAG,UAAU,CAAC,kBAA+B,CAAC;QAE1D,UAAU,GAAG,UAAU,IAAI,GAAG,CAAC,UAAU,GAAG,aAAa,CAAC;QAE1D,iDAAiD;QACjD,MAAM,QAAQ,GAAG,aAAa,GAAG,CAAC,CAAC;QAEnC,aAAa,IAAI,QAAQ,CAAC;QAC1B,aAAa,IAAI,QAAQ,CAAC;QAE1B,kDAAkD;QAClD,MAAM,kBAAkB,GAAG,UAAU,CAAC;QAEtC,UAAU,GAAG,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAE9C,mHAAmH;QACnH,MAAM,iBAAiB,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;QAEjE,IAAI,iBAAiB,GAAG,UAAU,EAAE,CAAC;YACjC,+DAA+D;YAC/D,uDAAuD;YACvD,yEAAyE;YACzE,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;YAC7C,SAAS,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,GAAG,aAAa,EAAE,kBAAkB,CAAC,CAAC,CAAC;YAC9F,GAAG,GAAG,SAAS,CAAC;YAChB,aAAa,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;IAC3E,CAAC;IAEO,mCAAmC,CAAC,UAAkB,EAAE,WAAwB,EAAE,SAAwB;QAC9G,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS,GAAG,IAAI,YAAY,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACJ,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;QACxD,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC;QAC7C,OAAO,UAAU,EAAE,EAAE,CAAC;YAClB,SAAS,CAAC,UAAU,CAAC,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,2GAA2G;IACpG,kBAAkB,CACrB,SAAoB,EACpB,IAAY,EACZ,KAAa,EACb,MAAc,EACd,WAAmB,EACnB,kBAA0B,EAC1B,IAAI,GAAG,SAAS,CAAC,yBAAyB,EAC1C,MAAM,GAAG,CAAC,EACV,SAAoC,IAAI,EACxC,aAAa,GAAG,IAAI,EACpB,gBAAgB,GAAG,KAAK;QAExB,MAAM,WAAW,GAAG,IAAI,KAAK,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjH,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;QACvC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,0CAA0C;YAC1C,SAAS,CAAC,QAAQ,uCAA+B,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAC/D,GAAG,EAAE;gBACD,MAAM,eAAe,GAAG,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC/D,IAAI,IAAI,GAA0D,MAAM,CAAC;gBACzE,IAAI,gBAAgB,EAAE,CAAC;oBACnB,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;wBAChB,IAAI,GAAG,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;oBACzE,CAAC;yBAAM,CAAC;wBACJ,IAAI,GAAG,0BAA0B,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;oBACrF,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;wBAChB,QAAQ,WAAW,EAAE,CAAC;4BAClB,KAAK,CAAC,EAAE,cAAc;gCAClB,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;gCAC3B,IAAmB,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;gCAC1D,MAAM;4BACV,KAAK,CAAC,EAAE,aAAa;gCACjB,4EAA4E;gCAC5E,IAAI,GAAG,IAAI,CAAC,mCAAmC,CAAC,IAAI,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC;gCAC3E,MAAM;4BACV,KAAK,CAAC,EAAE,QAAQ;gCACZ,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;gCACjC,IAAqB,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC;gCAC9D,MAAM;wBACd,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,QAAQ,WAAW,EAAE,CAAC;4BAClB,KAAK,CAAC,EAAE,cAAc;gCAClB,IAAI,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gCAClC,IAAmB,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;gCAC9F,MAAM;4BACV,KAAK,CAAC,EAAE,aAAa;gCACjB,4EAA4E;gCAC5E,IAAI,GAAG,IAAI,CAAC,mCAAmC,CAAC,IAAI,GAAG,CAAC,EAAE,eAAe,EAAE,MAAsB,CAAC,CAAC;gCACnG,MAAM;4BACV,KAAK,CAAC,EAAE,QAAQ;gCACZ,IAAI,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gCACpC,IAAqB,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;gCACtF,MAAM;wBACd,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,IAAI,WAAW,KAAK,kBAAkB,EAAE,CAAC;oBACrC,oFAAoF;oBACpF,IAAI,WAAW,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACzC,gDAAgD;wBAChD,WAAW,IAAI,CAAC,CAAC;wBACjB,kBAAkB,IAAI,CAAC,CAAC;oBAC5B,CAAC;oBACD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC1C,IAAI,MAAM,GAAG,WAAW,EACpB,OAAO,GAAG,CAAC,CAAC;oBAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;wBAC9B,OAAO,GAAG,CAAC,GAAG,kBAAkB,CAAC;wBACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC;4BACnC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;wBACvC,CAAC;oBACL,CAAC;oBACD,IAAI,WAAW,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACzC,IAAI,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;oBACzD,CAAC;yBAAM,CAAC;wBACJ,IAAI,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;oBACnD,CAAC;gBACL,CAAC;gBACD,SAAS,CAAC,KAAK,EAAE,CAAC;gBAClB,IAAI,aAAa,EAAE,CAAC;oBAChB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBAClC,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,CAAC,MAAM,EAAE,EAAE;gBACP,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;oBAChE,kHAAkH;oBAClH,OAAO,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC;gBAC9B,CAAC;qBAAM,CAAC;oBACJ,2EAA2E;oBAC3E,MAAM,CAAC,MAAM,CAAC,CAAC;gBACnB,CAAC;YACL,CAAC,CACJ,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,aAAa,CAAC,MAA8B;QAC/C,IAAI,mBAAmB,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,CAAC,UAAU,EAAE,CAAC;QAEpB,IAAI,MAAM,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,kBAA+B,CAAC,CAAC;YAC1E,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,sBAAsB;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC3D,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC5C,CAAC;CACJ", "sourcesContent": ["/* eslint-disable baby<PERSON>js/available */\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\nimport { WebGPUDataBuffer } from \"../../Meshes/WebGPU/webgpuDataBuffer\";\r\nimport { FromHalfFloat } from \"../../Misc/textureTools\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { allocateAndCopyTypedBuffer } from \"../abstractEngine.functions\";\r\nimport { Constants } from \"../constants\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\n\r\n/** @internal */\r\nexport class WebGPUBufferManager {\r\n    private _engine: WebGPUEngine;\r\n    private _device: GPUDevice;\r\n    private _deferredReleaseBuffers: Array<GPUBuffer> = [];\r\n\r\n    private static _IsGPUBuffer(buffer: DataBuffer | GPUBuffer): buffer is GPUBuffer {\r\n        return (buffer as DataBuffer).underlyingResource === undefined;\r\n    }\r\n\r\n    private static _FlagsToString(flags: GPUBufferUsageFlags, suffix = \"\") {\r\n        let result = suffix;\r\n\r\n        for (let i = 0; i <= 9; ++i) {\r\n            if (flags & (1 << i)) {\r\n                if (result) {\r\n                    result += \"_\";\r\n                }\r\n                result += WebGPUConstants.BufferUsage[1 << i];\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    constructor(engine: WebGPUEngine, device: GPUDevice) {\r\n        this._engine = engine;\r\n        this._device = device;\r\n    }\r\n\r\n    public createRawBuffer(viewOrSize: ArrayBufferView | number, flags: GPUBufferUsageFlags, mappedAtCreation = false, label?: string): GPUBuffer {\r\n        const alignedLength = (viewOrSize as ArrayBufferView).byteLength !== undefined ? ((viewOrSize as ArrayBufferView).byteLength + 3) & ~3 : ((viewOrSize as number) + 3) & ~3; // 4 bytes alignments (because of the upload which requires this)\r\n        const verticesBufferDescriptor = {\r\n            label: \"BabylonWebGPUDevice\" + this._engine.uniqueId + \"_\" + WebGPUBufferManager._FlagsToString(flags, label ?? \"Buffer\") + \"_size\" + alignedLength,\r\n            mappedAtCreation,\r\n            size: alignedLength,\r\n            usage: flags,\r\n        };\r\n\r\n        return this._device.createBuffer(verticesBufferDescriptor);\r\n    }\r\n\r\n    public createBuffer(viewOrSize: ArrayBufferView | number, flags: GPUBufferUsageFlags, label?: string): WebGPUDataBuffer {\r\n        const isView = (viewOrSize as ArrayBufferView).byteLength !== undefined;\r\n        const dataBuffer = new WebGPUDataBuffer();\r\n        const labelId = \"DataBufferUniqueId=\" + dataBuffer.uniqueId;\r\n        dataBuffer.buffer = this.createRawBuffer(viewOrSize, flags, undefined, label ? labelId + \"-\" + label : labelId);\r\n        dataBuffer.references = 1;\r\n        dataBuffer.capacity = isView ? (viewOrSize as ArrayBufferView).byteLength : (viewOrSize as number);\r\n        dataBuffer.engineId = this._engine.uniqueId;\r\n\r\n        if (isView) {\r\n            this.setSubData(dataBuffer, 0, viewOrSize as ArrayBufferView);\r\n        }\r\n\r\n        return dataBuffer;\r\n    }\r\n\r\n    // This calls GPUBuffer.writeBuffer() with no alignment corrections\r\n    // dstByteOffset and byteLength must both be aligned to 4 bytes and bytes moved must be within src and dst arrays\r\n    public setRawData(buffer: GPUBuffer, dstByteOffset: number, src: ArrayBufferView, srcByteOffset: number, byteLength: number): void {\r\n        srcByteOffset += src.byteOffset;\r\n\r\n        this._device.queue.writeBuffer(buffer, dstByteOffset, src.buffer, srcByteOffset, byteLength);\r\n    }\r\n\r\n    // This calls GPUBuffer.writeBuffer() with alignment corrections (dstByteOffset and byteLength will be aligned to 4 byte boundaries)\r\n    // If alignment is needed, src must be a full copy of dataBuffer, or at least should be large enough to cope with the additional bytes copied because of alignment!\r\n    public setSubData(dataBuffer: WebGPUDataBuffer, dstByteOffset: number, src: ArrayBufferView, srcByteOffset = 0, byteLength = 0): void {\r\n        const buffer = dataBuffer.underlyingResource as GPUBuffer;\r\n\r\n        byteLength = byteLength || src.byteLength - srcByteOffset;\r\n\r\n        // Make sure the dst offset is aligned to 4 bytes\r\n        const startPre = dstByteOffset & 3;\r\n\r\n        srcByteOffset -= startPre;\r\n        dstByteOffset -= startPre;\r\n\r\n        // Make sure the byte length is aligned to 4 bytes\r\n        const originalByteLength = byteLength;\r\n\r\n        byteLength = (byteLength + startPre + 3) & ~3;\r\n\r\n        // Check if the backing buffer of src is large enough to cope with the additional bytes copied because of alignment\r\n        const backingBufferSize = src.buffer.byteLength - src.byteOffset;\r\n\r\n        if (backingBufferSize < byteLength) {\r\n            // Not enough place in the backing buffer for the aligned copy.\r\n            // Creates a new buffer and copy the source data to it.\r\n            // The buffer will have byteLength - originalByteLength zeros at the end.\r\n            const tmpBuffer = new Uint8Array(byteLength);\r\n            tmpBuffer.set(new Uint8Array(src.buffer, src.byteOffset + srcByteOffset, originalByteLength));\r\n            src = tmpBuffer;\r\n            srcByteOffset = 0;\r\n        }\r\n\r\n        this.setRawData(buffer, dstByteOffset, src, srcByteOffset, byteLength);\r\n    }\r\n\r\n    private _getHalfFloatAsFloatRGBAArrayBuffer(dataLength: number, arrayBuffer: ArrayBuffer, destArray?: Float32Array): Float32Array {\r\n        if (!destArray) {\r\n            destArray = new Float32Array(dataLength);\r\n        } else {\r\n            dataLength = Math.min(dataLength, destArray.length);\r\n        }\r\n        const srcData = new Uint16Array(arrayBuffer);\r\n        while (dataLength--) {\r\n            destArray[dataLength] = FromHalfFloat(srcData[dataLength]);\r\n        }\r\n\r\n        return destArray;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/promise-function-async\r\n    public readDataFromBuffer(\r\n        gpuBuffer: GPUBuffer,\r\n        size: number,\r\n        width: number,\r\n        height: number,\r\n        bytesPerRow: number,\r\n        bytesPerRowAligned: number,\r\n        type = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n        offset = 0,\r\n        buffer: Nullable<ArrayBufferView> = null,\r\n        destroyBuffer = true,\r\n        noDataConversion = false\r\n    ): Promise<ArrayBufferView> {\r\n        const floatFormat = type === Constants.TEXTURETYPE_FLOAT ? 2 : type === Constants.TEXTURETYPE_HALF_FLOAT ? 1 : 0;\r\n        const engineId = this._engine.uniqueId;\r\n        return new Promise((resolve, reject) => {\r\n            // eslint-disable-next-line github/no-then\r\n            gpuBuffer.mapAsync(WebGPUConstants.MapMode.Read, offset, size).then(\r\n                () => {\r\n                    const copyArrayBuffer = gpuBuffer.getMappedRange(offset, size);\r\n                    let data: Nullable<ArrayBufferView> | Uint8Array | Float32Array = buffer;\r\n                    if (noDataConversion) {\r\n                        if (data === null) {\r\n                            data = allocateAndCopyTypedBuffer(type, size, true, copyArrayBuffer);\r\n                        } else {\r\n                            data = allocateAndCopyTypedBuffer(type, data.buffer, undefined, copyArrayBuffer);\r\n                        }\r\n                    } else {\r\n                        if (data === null) {\r\n                            switch (floatFormat) {\r\n                                case 0: // byte format\r\n                                    data = new Uint8Array(size);\r\n                                    (data as Uint8Array).set(new Uint8Array(copyArrayBuffer));\r\n                                    break;\r\n                                case 1: // half float\r\n                                    // TODO WEBGPU use computer shaders (or render pass) to make the conversion?\r\n                                    data = this._getHalfFloatAsFloatRGBAArrayBuffer(size / 2, copyArrayBuffer);\r\n                                    break;\r\n                                case 2: // float\r\n                                    data = new Float32Array(size / 4);\r\n                                    (data as Float32Array).set(new Float32Array(copyArrayBuffer));\r\n                                    break;\r\n                            }\r\n                        } else {\r\n                            switch (floatFormat) {\r\n                                case 0: // byte format\r\n                                    data = new Uint8Array(data.buffer);\r\n                                    (data as Uint8Array).set(new Uint8Array(copyArrayBuffer, 0, Math.min(data.byteLength, size)));\r\n                                    break;\r\n                                case 1: // half float\r\n                                    // TODO WEBGPU use computer shaders (or render pass) to make the conversion?\r\n                                    data = this._getHalfFloatAsFloatRGBAArrayBuffer(size / 2, copyArrayBuffer, buffer as Float32Array);\r\n                                    break;\r\n                                case 2: // float\r\n                                    data = new Float32Array(data.buffer);\r\n                                    (data as Float32Array).set(new Float32Array(copyArrayBuffer, 0, data.byteLength / 4));\r\n                                    break;\r\n                            }\r\n                        }\r\n                    }\r\n                    if (bytesPerRow !== bytesPerRowAligned) {\r\n                        // TODO WEBGPU use computer shaders (or render pass) to build the final buffer data?\r\n                        if (floatFormat === 1 && !noDataConversion) {\r\n                            // half float have been converted to float above\r\n                            bytesPerRow *= 2;\r\n                            bytesPerRowAligned *= 2;\r\n                        }\r\n                        const data2 = new Uint8Array(data.buffer);\r\n                        let offset = bytesPerRow,\r\n                            offset2 = 0;\r\n                        for (let y = 1; y < height; ++y) {\r\n                            offset2 = y * bytesPerRowAligned;\r\n                            for (let x = 0; x < bytesPerRow; ++x) {\r\n                                data2[offset++] = data2[offset2++];\r\n                            }\r\n                        }\r\n                        if (floatFormat !== 0 && !noDataConversion) {\r\n                            data = new Float32Array(data2.buffer, 0, offset / 4);\r\n                        } else {\r\n                            data = new Uint8Array(data2.buffer, 0, offset);\r\n                        }\r\n                    }\r\n                    gpuBuffer.unmap();\r\n                    if (destroyBuffer) {\r\n                        this.releaseBuffer(gpuBuffer);\r\n                    }\r\n                    resolve(data);\r\n                },\r\n                (reason) => {\r\n                    if (this._engine.isDisposed || this._engine.uniqueId !== engineId) {\r\n                        // The engine was disposed while waiting for the promise, or a context loss/restoration has occurred: don't reject\r\n                        resolve(new Uint8Array());\r\n                    } else {\r\n                        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                        reject(reason);\r\n                    }\r\n                }\r\n            );\r\n        });\r\n    }\r\n\r\n    public releaseBuffer(buffer: DataBuffer | GPUBuffer): boolean {\r\n        if (WebGPUBufferManager._IsGPUBuffer(buffer)) {\r\n            this._deferredReleaseBuffers.push(buffer);\r\n            return true;\r\n        }\r\n\r\n        buffer.references--;\r\n\r\n        if (buffer.references === 0) {\r\n            this._deferredReleaseBuffers.push(buffer.underlyingResource as GPUBuffer);\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public destroyDeferredBuffers(): void {\r\n        for (let i = 0; i < this._deferredReleaseBuffers.length; ++i) {\r\n            this._deferredReleaseBuffers[i].destroy();\r\n        }\r\n\r\n        this._deferredReleaseBuffers.length = 0;\r\n    }\r\n}\r\n"]}