{"version": 3, "file": "webgpuShaderProcessorsWGSL.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuShaderProcessorsWGSL.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAEhF,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,cAAc,EAAE,2BAA2B,EAAE,MAAM,mCAAmC,CAAC;AAGhG,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,kEAAkE,CAAC;AAC1E,OAAO,uDAAuD,CAAC;AAC/D,OAAO,uDAAuD,CAAC;AAC/D,OAAO,kDAAkD,CAAC;AAC1D,OAAO,kDAAkD,CAAC;AAC1D,OAAO,kDAAkD,CAAC;AAC1D,OAAO,qDAAqD,CAAC;AAC7D,OAAO,sDAAsD,CAAC;AAC9D,OAAO,gDAAgD,CAAC;AAExD,MAAM,sBAAsB,GAAG,2BAA2B,CAAC;AAE3D,MAAM,eAAe,GAAG,UAAU,CAAC;AACnC,MAAM,gBAAgB,GAAG,WAAW,CAAC;AAErC,MAAM,8CAA8C,GAAyD;IACzG,UAAU,qDAA0C;IACpD,UAAU,qDAA0C;IACpD,gBAAgB,gEAA+C;IAC/D,UAAU,qDAA0C;IACpD,YAAY,wDAA2C;IACvD,kBAAkB,mEAAgD;IAClE,uBAAuB,qDAA0C;IACjE,gBAAgB,qDAA0C;IAC1D,sBAAsB,gEAA+C;IACrE,kBAAkB,wDAA2C;IAC7D,wBAAwB,mEAAgD;IACxE,6BAA6B,qDAA0C;IACvE,kBAAkB,qDAA0C;IAC5D,kBAAkB,qDAA0C;IAC5D,wBAAwB,gEAA+C;IACvE,kBAAkB,qDAA0C;IAC5D,gBAAgB,EAAE,IAAI;CACzB,CAAC;AAEF,gBAAgB;AAChB,MAAM,OAAO,yBAA0B,SAAQ,qBAAqB;IAApE;;QASoB,mBAAc,+BAAuB;QAC9C,kBAAa,GAAG,gCAAgC,CAAC;QACjD,kBAAa,GAAG,sFAAsF,CAAC;QACvG,gBAAW,GAAG,IAAI,CAAC;QACnB,aAAQ,GAAG,KAAK,CAAC;IAglB5B,CAAC;IA9kBa,aAAa,CAAC,IAAY,EAAE,WAAmB,EAAE,aAAwC;QAC/F,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,MAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACpD,IAAI,UAAU,GAAG,QAAQ,CAAC;YAC1B,OAAO,UAAU,GAAG,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC;gBACxG,UAAU,EAAE,CAAC;YACjB,CAAC;YACD,MAAM,cAAc,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;YACvE,MAAM,GAAG,CAAC,cAAc,CAAC;YACzB,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;gBAChB,MAAM,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;YACnD,CAAC;YACD,OAAO,UAAU,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;gBAC1G,UAAU,EAAE,CAAC;YACjB,CAAC;YACD,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;QACtF,CAAC;QAED,OAAO,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC;IAEM,iBAAiB,CAAC,iBAAsD;QAC3E,IAAI,CAAC,wBAAwB,GAAG,iBAAkD,CAAC;QAEnF,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,6BAA6B,GAAG,EAAE,CAAC;QACxC,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;IACpC,CAAC;IAEM,oBAAoB,CAAC,IAAY;QACpC,gGAAgG;QAChG,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ;YAC/B,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,UAAU,qBAAqB,CAAC,gBAAgB,wEAAwE,gBAAgB,MAAM,qBAAqB,CAAC,gBAAgB,KAAK,CAAC;QAChM,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3D,OAAO,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC;IAEM,YAAY,CAAC,OAAe;QAC/B,MAAM,KAAK,GAAG,sEAAsE,CAAC;QAErF,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAEM,gBAAgB,CAAC,OAAe,EAAE,UAAmB,EAAE,aAAwC;QAClG,MAAM,YAAY,GAAG,yHAAyH,CAAC;QAC/I,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,iBAAiB,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC;YACpD,MAAM,qBAAqB,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC;YACnD,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,aAAa,GAAG,iBAAiB,KAAK,MAAM,CAAC,CAAC,CAAC,gBAAgB,iBAAiB,GAAG,CAAC,CAAC,CAAC,gBAAgB,iBAAiB,KAAK,qBAAqB,GAAG,CAAC;YAC3J,IAAI,QAAgB,CAAC;YACrB,IAAI,UAAU,EAAE,CAAC;gBACb,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACjE,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACzB,MAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,2EAA2E,CAAC,CAAC;gBAChJ,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtI,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;gBACjE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,QAAQ,KAAK,aAAa,IAAI,IAAI,MAAM,WAAW,GAAG,CAAC,CAAC;gBAC/F,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YAED,OAAO,GAAG,EAAE,CAAC;QACjB,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,kBAAkB,CAAC,SAAiB,EAAE,aAAwC;QACjF,MAAM,WAAW,GAAG,uCAAuC,CAAC;QAC5D,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAElJ,IAAI,CAAC,wBAAwB,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;YACnE,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;YAEjE,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,oCAAoC,CAAC,IAAI,CAAC,CAAC;YAC/F,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gBAC9B,oGAAoG;gBACpG,MAAM,OAAO,GACT,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,aAAa,GAAG,OAAO,CAAC;gBAC1J,MAAM,OAAO,GAAG,QAAQ,IAAI,GAAG,CAAC;gBAEhC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,QAAQ,KAAK,OAAO,MAAM,OAAO,GAAG,CAAC,CAAC;gBAClF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,MAAM,aAAa,GAAG,CAAC,CAAC;gBACzD,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,gBAAgB,IAAI,MAAM,aAAa,kBAAkB,OAAO,IAAI,CAAC,CAAC;gBAC9G,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,QAAQ,KAAK,IAAI,MAAM,aAAa,GAAG,CAAC,CAAC;gBACrF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,IAAI,MAAM,aAAa,GAAG,CAAC,CAAC;gBACzD,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,gBAAgB,IAAI,oBAAoB,IAAI,GAAG,CAAC,CAAC;YAC7F,CAAC;YACD,SAAS,GAAG,EAAE,CAAC;QACnB,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEM,gBAAgB,CAAC,OAAe,EAAE,UAAmB,EAAE,aAAwC;QAClG,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;YAEhE,OAAO,GAAG,EAAE,CAAC;QACjB,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,gBAAgB,CAAC,OAAe,EAAE,UAAmB,EAAE,aAAwC;QAClG,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB;YAC9C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,4DAA4D;YACnF,MAAM,gBAAgB,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpC,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;YAClE,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC5D,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,iCAAiC;YACjE,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,CAAC,CAAE,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAuB,CAAC,CAAC,CAAC,IAAI,CAAC;YAE3I,IAAI,SAAS,GAAG,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF,IAAI,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACxE,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,WAAW,GAAG;oBACV,cAAc,EAAE,SAAS,GAAG,CAAC;oBAC7B,gBAAgB;oBAChB,QAAQ,EAAE,EAAE;oBACZ,UAAU,uDAAyC;iBACtD,CAAC;gBACF,SAAS,GAAG,SAAS,IAAI,CAAC,CAAC;gBAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;oBACjC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,CAAC,CAAC;gBACrF,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,CAAC;YAED,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;YAEpE,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACxD,MAAM,gBAAgB,GAAG,8CAA8C,CAAC,WAAW,CAAC,CAAC;YACrF,MAAM,UAAU,GAAG,cAAc;gBAC7B,CAAC;gBACD,CAAC,CAAC,aAAa,KAAK,KAAK;oBACvB,CAAC;oBACD,CAAC,CAAC,aAAa,KAAK,KAAK;wBACvB,CAAC;wBACD,CAAC,sDAAwC,CAAC;YAElD,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC;YAEpC,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;gBACjC,4CAA4C;gBAC5C,MAAM,0EAA0E,WAAW,IAAI,CAAC;YACpG,CAAC;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;gBACjC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAE7D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACV,OAAO,GAAG,UAAU,UAAU,cAAc,YAAY,KAAK,OAAO,EAAE,CAAC;gBAC3E,CAAC;gBAED,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,CAAC,UAAU,CAAC,CAAC;YAClH,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,sBAAsB,CAAC,aAAwC;QACnE,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC;YAC9B,MAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACvD,IAAI,IAAI,SAAS,GAAG,MAAM,KAAK,KAAK,CAAC;YACzC,CAAC;iBAAM,IAAI,GAAG,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;gBAC7B,IAAI,IAAI,SAAS,GAAG,YAAY,CAAC;YACrC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,aAAa,CAChB,IAAY,EACZ,QAAkB,EAClB,WAAoB,EACpB,kBAAuD,EACvD,WAEC,EACD,aAAwC,EACxC,qBAAgD;QAEhD,qMAAqM;QACrM,0GAA0G;QAC1G,EAAE;QACF,8OAA8O;QAC9O,EAAE;QACF,uKAAuK;QACvK,4BAA4B;QAC5B,EAAE;QACF,kCAAkC;QAClC,qDAAqD;QACrD,GAAG;QACH,iFAAiF;QACjF,EAAE;QACF,sJAAsJ;QACtJ,+FAA+F;QAC/F,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,KAAK,MAAM,GAAG,IAAI,qBAAqB,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;YAEzC,kJAAkJ;YAClJ,kOAAkO;YAClO,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;gBACnB,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,CAAC;QACL,CAAC;QAED,+KAA+K;QAC/K,sEAAsE;QACtE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1F,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC5B,uDAAuD;YACvD,uIAAuI;YACvI,eAAe;YACf,yBAAyB;YACzB,2BAA2B;YAC3B,EAAE;YACF,oDAAoD;YACpD,2BAA2B;YAC3B,6BAA6B;YAC7B,EAAE;YACF,uIAAuI;YACvI,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;YAE3C,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAChC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;gBACZ,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;YACrB,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YAE3D,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;QAEzD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,eAAe,CAAC,UAAkB,EAAE,YAAoB;QAC3D,MAAM,iBAAiB,GAAa,EAAE,CAAC;QAEvC,MAAM,aAAa,GACf,YAAY,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAClE,CAAC,CAAC;;;;SAIT;YACO,CAAC,CAAC,EAAE,CAAC;QAEb,8FAA8F;QAC9F,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACrD,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAE1D,yIAAyI;QACzI,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC1D,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAE/D,4BAA4B;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE7C,UAAU,GAAG,WAAW,GAAG,UAAU,CAAC;QACtC,YAAY,GAAG,WAAW,GAAG,YAAY,CAAC;QAE1C,cAAc;QACd,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAC3D,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;QAE3D,IAAI,YAAY,GAAG,uHAAuH,CAAC;QAC3I,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC;QACD,YAAY,IAAI,iCAAiC,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC;QACnH,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,YAAY,IAAI,qEAAqE,CAAC;YACtF,YAAY,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChD,YAAY,IAAI,oDAAoD,CAAC;QACzE,CAAC;QAED,IAAI,aAAa,GAAG,uEAAuE,CAAC;QAC5F,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC;QACD,aAAa,IAAI,sDAAsD,CAAC;QAExE,UAAU,GAAG,YAAY,GAAG,aAAa,GAAG,UAAU,CAAC;QAEvD,IAAI,sBAAsB,GAAG,mBAAmB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC;QACnG,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,sBAAsB,IAAI,oHAAoH,CAAC;YAC/I,sBAAsB,IAAI,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxE,sBAAsB,IAAI,IAAI,CAAC;QACnC,CAAC;QACD,MAAM,oBAAoB,GAAG,IAAI,CAAC,QAAQ;YACtC,CAAC,CAAC,yBAAyB;YAC3B,CAAC,CAAC,sGAAsG,CAAC;QAC7G,IAAI,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAEvE,UAAU;YACN,CAAC,iBAAiB,CAAC,CAAC,CAAC,2CAA2C,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtE,+CAA+C;gBAC/C,2BAA2B,CAAC,UAAU,EAAE,SAAS,EAAE,sBAAsB,EAAE,oBAAoB,CAAC,CAAC;QAErG,gBAAgB;QAChB,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QAC/D,YAAY,GAAG,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAC,CAAC,2CAA2C;QAC3H,CAAC;QAED,IAAI,cAAc,GAAG,qHAAqH,CAAC;QAC3I,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,cAAc,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC;QACD,cAAc,IAAI,uDAAuD,CAAC;QAE1E,IAAI,eAAe,GAAG,4BAA4B,CAAC;QAEnD,mCAAmC;QACnC,MAAM,SAAS,GAAG,4BAA4B,CAAC;QAC/C,IAAI,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACjE,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,IAAI,KAAK,EAAE,CAAC;YACR,eAAe,IAAI,cAAc,aAAa,4BAA4B,CAAC;YAC3E,aAAa,EAAE,CAAC;YAChB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACrC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,GAAG,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;gBAC/D,IAAI,KAAK,EAAE,CAAC;oBACR,eAAe,IAAI,cAAc,aAAa,aAAa,aAAa,iBAAiB,CAAC;oBAC1F,aAAa,EAAE,CAAC;gBACpB,CAAC;YACL,CAAC;YACD,IAAI,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC/C,eAAe,IAAI,eAAe,aAAa,wBAAwB,CAAC;gBACxE,aAAa,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;QAED,mCAAmC;QACnC,MAAM,KAAK,GAAG,iBAAiB,CAAC;QAChC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAElC,IAAI,KAAK,EAAE,CAAC;YACR,eAAe,IAAI,cAAc,aAAa,EAAE,wBAAwB,CAAC;YACzE,eAAe,IAAI,cAAc,aAAa,EAAE,6BAA6B,CAAC;YAC9E,eAAe,IAAI,cAAc,aAAa,EAAE,4BAA4B,CAAC;QACjF,CAAC;QAED,IAAI,aAAa,KAAK,CAAC,EAAE,CAAC;YACtB,MAAM,qBAAqB,GAAG,YAAY,CAAC,OAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC;YAElF,IAAI,qBAAqB,EAAE,CAAC;gBACxB,iBAAiB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBAE/C,eAAe,IAAI,mDAAmD,CAAC;gBACvE,eAAe,IAAI,oDAAoD,CAAC;YAC5E,CAAC;iBAAM,CAAC;gBACJ,eAAe,IAAI,qCAAqC,CAAC;YAC7D,CAAC;YACD,aAAa,EAAE,CAAC;QACpB,CAAC;QAED,YAAY;QACZ,IAAI,YAAY,GAAG,KAAK,CAAC;QACzB,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,OAAO,CAAC,YAAY,EAAE,CAAC;YACnB,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC;YACxD,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;gBACV,MAAM;YACV,CAAC;YACD,MAAM,SAAS,GAAG,GAAG,CAAC;YACtB,YAAY,GAAG,IAAI,CAAC;YACpB,OAAO,GAAG,GAAG,CAAC,IAAI,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;gBAClD,IAAI,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBAC3E,YAAY,GAAG,KAAK,CAAC;oBACrB,MAAM;gBACV,CAAC;gBACD,GAAG,EAAE,CAAC;YACV,CAAC;YACD,GAAG,GAAG,SAAS,GAAG,sBAAsB,CAAC,MAAM,CAAC;QACpD,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACf,eAAe,IAAI,0CAA0C,CAAC;QAClE,CAAC;QAED,eAAe,IAAI,uDAAuD,CAAC;QAE3E,YAAY,GAAG,cAAc,GAAG,eAAe,GAAG,YAAY,CAAC;QAE/D,MAAM,oBAAoB,GAAG,+BAA+B,GAAG,aAAa,CAAC;QAC7E,MAAM,kBAAkB,GAAG,2BAA2B,CAAC;QACvD,iBAAiB,GAAG,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAErE,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,YAAY,GAAG,SAAS,GAAG,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,GAAG,YAAY,CAAC;QAC3F,CAAC;QAED,YAAY;YACR,CAAC,iBAAiB,CAAC,CAAC,CAAC,2CAA2C,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtE,+CAA+C;gBAC/C,2BAA2B,CAAC,YAAY,EAAE,SAAS,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;QAEnG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,IAAI,CAAC,wBAAwB,CAAC,oCAAoC,GAAG,EAAE,CAAC;QAExE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC;IACxC,CAAC;IAES,wBAAwB,CAAC,IAAY,EAAE,wBAAiD;QAC9F,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,GAAG,GAAG,UAAU,IAAI,MAAM,CAAC;QAC/B,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,CAAC;YAC3E,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;YAClE,MAAM,IAAI,GAAG,qBAAqB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAEtD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;oBACZ,MAAM,gBAAgB,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,cAAc,CAAC;oBACpF,aAAa,IAAI,UAAU,gBAAgB;;8BAEjC,IAAI;sBACZ,CAAC;oBACH,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBAEtD,GAAG,IAAI,eAAe,eAAe,CAAC,IAAI,YAAY,gBAAgB,KAAK,eAAe,CAAC,MAAM,MAAM,CAAC;gBAC5G,CAAC;qBAAM,CAAC;oBACJ,GAAG,IAAI,IAAI,eAAe,CAAC,IAAI,YAAY,eAAe,CAAC,IAAI,KAAK,eAAe,CAAC,MAAM,MAAM,CAAC;gBACrG,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,GAAG,IAAI,KAAK,eAAe,CAAC,IAAI,MAAM,eAAe,CAAC,IAAI,KAAK,CAAC;YACpE,CAAC;QACL,CAAC;QACD,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,GAAG,GAAG,aAAa,KAAK,GAAG,EAAE,CAAC;QACjC,GAAG,IAAI,UAAU,wBAAwB,CAAC,OAAO,CAAC,UAAU,cAAc,wBAAwB,CAAC,OAAO,CAAC,YAAY,kBAAkB,eAAe,MAAM,IAAI,KAAK,CAAC;QAExK,OAAO,GAAG,CAAC;IACf,CAAC;IAEO,gBAAgB,CAAC,IAAY,EAAE,QAAiB;QACpD,MAAM,aAAa,GAAG,6DAA6D,CAAC;QAEpF,iDAAiD;QACjD,OAAO,IAAI,EAAE,CAAC;YACV,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACjB,MAAM;YACV,CAAC;YAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,uBAAuB;YAC9C,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAgC;YAC9D,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC1E,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,iBAAiB,CAAC,KAAK,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACpI,MAAM,kBAAkB,GAAG,WAAW,KAAK,oBAAoB,CAAC,CAAC,kEAA+C,CAAC,+DAA6C,CAAC;YAE/J,IAAI,WAAW,EAAE,CAAC;gBACd,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBACjF,IAAI,WAAW,EAAE,CAAC;oBACd,WAAW,CAAC,eAAe,GAAG,IAAI,CAAC;gBACvC,CAAC;YACL,CAAC;YAED,IAAI,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACxE,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,WAAW,GAAG;oBACV,OAAO,EAAE,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,EAAE;oBAC9D,IAAI,EAAE,kBAAkB;iBAC3B,CAAC;gBACF,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;YACxE,CAAC;YAED,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;YAEhE,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,UAAU,GAAG,UAAU,WAAW,CAAC,OAAO,CAAC,UAAU,cAAc,WAAW,CAAC,OAAO,CAAC,YAAY,IAAI,CAAC;YAC9G,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1C,IAAI,GAAG,KAAK,GAAG,UAAU,GAAG,KAAK,CAAC;YAElC,aAAa,CAAC,SAAS,IAAI,UAAU,CAAC,MAAM,CAAC;QACjD,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,qBAAqB,CAAC,IAAY,EAAE,QAAiB;QACzD,MAAM,uBAAuB,GAAG,mFAAmF,CAAC;QAEpH,iDAAiD;QACjD,OAAO,IAAI,EAAE,CAAC;YACV,MAAM,KAAK,GAAG,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACjB,MAAM;YACV,CAAC;YAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACpB,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAE5B,IAAI,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACtE,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,MAAM,QAAQ,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,6BAA6B,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAEjG,IAAI,OAAO,CAAC;gBACZ,IAAI,QAAQ,EAAE,CAAC;oBACX,IAAI,GAAG,UAAU,CAAC;oBAClB,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;oBAC3B,IAAI,OAAO,CAAC,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;wBAC5B,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC;wBACxE,IAAI,CAAC,OAAO,EAAE,CAAC;4BACX,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,CAAC;wBACpE,CAAC;oBACL,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,CAAC;gBACpE,CAAC;gBAED,UAAU,GAAG,EAAE,OAAO,EAAE,CAAC;gBACzB,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;YACtE,CAAC;YAED,IAAI,CAAC,4BAA4B,CAC7B,IAAI,EACJ,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,EACpD,UAAU,KAAK,YAAY;gBACvB,CAAC;gBACD,CAAC,CAAC,IAAI,KAAK,SAAS;oBAClB,CAAC;oBACD,CAAC,0DAA0C,EACjD,QAAQ,CACX,CAAC;YAEF,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC;YACjD,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC;YAErD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YAC7C,MAAM,UAAU,GAAG,UAAU,UAAU,cAAc,YAAY,IAAI,CAAC;YACtE,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAE1C,IAAI,GAAG,KAAK,GAAG,UAAU,GAAG,KAAK,CAAC;YAElC,uBAAuB,CAAC,SAAS,IAAI,UAAU,CAAC,MAAM,CAAC;QAC3D,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,4BAA4B,CAAC,IAAY;QAC7C,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACxD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,GAAG,gBAAgB,iBAAiB,EAAE,GAAG,CAAC,EAAE,GAAG,gBAAgB,SAAS,CAAC,CAAC;QAC7G,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n/* eslint-disable babylonjs/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport type { Nullable } from \"../../types\";\r\nimport type { _IShaderProcessingContext } from \"../Processors/shaderProcessingOptions\";\r\nimport type { WebGPUBufferDescription } from \"./webgpuShaderProcessingContext\";\r\nimport { WebGPUShaderProcessingContext } from \"./webgpuShaderProcessingContext\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { WebGPUShaderProcessor } from \"./webgpuShaderProcessor\";\r\nimport { RemoveComments, InjectStartingAndEndingCode } from \"../../Misc/codeStringParsingTools\";\r\nimport { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\n\r\nimport { Constants } from \"../constants\";\r\n\r\nimport \"../../ShadersWGSL/ShadersInclude/bakedVertexAnimationDeclaration\";\r\nimport \"../../ShadersWGSL/ShadersInclude/bakedVertexAnimation\";\r\nimport \"../../ShadersWGSL/ShadersInclude/instancesDeclaration\";\r\nimport \"../../ShadersWGSL/ShadersInclude/instancesVertex\";\r\nimport \"../../ShadersWGSL/ShadersInclude/helperFunctions\";\r\nimport \"../../ShadersWGSL/ShadersInclude/fresnelFunction\";\r\nimport \"../../ShadersWGSL/ShadersInclude/meshUboDeclaration\";\r\nimport \"../../ShadersWGSL/ShadersInclude/sceneUboDeclaration\";\r\nimport \"../../ShadersWGSL/ShadersInclude/decalFragment\";\r\n\r\nconst builtInName_frag_depth = \"fragmentOutputs.fragDepth\";\r\n\r\nconst leftOverVarName = \"uniforms\";\r\nconst internalsVarName = \"internals\";\r\n\r\nconst gpuTextureViewDimensionByWebGPUTextureFunction: { [key: string]: Nullable<GPUTextureViewDimension> } = {\r\n    texture_1d: WebGPUConstants.TextureViewDimension.E1d,\r\n    texture_2d: WebGPUConstants.TextureViewDimension.E2d,\r\n    texture_2d_array: WebGPUConstants.TextureViewDimension.E2dArray,\r\n    texture_3d: WebGPUConstants.TextureViewDimension.E3d,\r\n    texture_cube: WebGPUConstants.TextureViewDimension.Cube,\r\n    texture_cube_array: WebGPUConstants.TextureViewDimension.CubeArray,\r\n    texture_multisampled_2d: WebGPUConstants.TextureViewDimension.E2d,\r\n    texture_depth_2d: WebGPUConstants.TextureViewDimension.E2d,\r\n    texture_depth_2d_array: WebGPUConstants.TextureViewDimension.E2dArray,\r\n    texture_depth_cube: WebGPUConstants.TextureViewDimension.Cube,\r\n    texture_depth_cube_array: WebGPUConstants.TextureViewDimension.CubeArray,\r\n    texture_depth_multisampled_2d: WebGPUConstants.TextureViewDimension.E2d,\r\n    texture_storage_1d: WebGPUConstants.TextureViewDimension.E1d,\r\n    texture_storage_2d: WebGPUConstants.TextureViewDimension.E2d,\r\n    texture_storage_2d_array: WebGPUConstants.TextureViewDimension.E2dArray,\r\n    texture_storage_3d: WebGPUConstants.TextureViewDimension.E3d,\r\n    texture_external: null,\r\n};\r\n\r\n/** @internal */\r\nexport class WebGPUShaderProcessorWGSL extends WebGPUShaderProcessor {\r\n    protected _attributesInputWGSL: string[];\r\n    protected _attributesWGSL: string[];\r\n    protected _attributesConversionCodeWGSL: string[];\r\n    protected _hasNonFloatAttribute: boolean;\r\n    protected _varyingsWGSL: string[];\r\n    protected _varyingNamesWGSL: string[];\r\n    protected _stridedUniformArrays: string[];\r\n\r\n    public override shaderLanguage = ShaderLanguage.WGSL;\r\n    public uniformRegexp = /uniform\\s+(\\w+)\\s*:\\s*(.+)\\s*;/;\r\n    public textureRegexp = /var\\s+(\\w+)\\s*:\\s*((array<\\s*)?(texture_\\w+)\\s*(<\\s*(.+)\\s*>)?\\s*(,\\s*\\w+\\s*>\\s*)?);/;\r\n    public noPrecision = true;\r\n    public pureMode = false;\r\n\r\n    protected _getArraySize(name: string, uniformType: string, preProcessors: { [key: string]: string }): [string, string, number] {\r\n        let length = 0;\r\n\r\n        const endArray = uniformType.lastIndexOf(\">\");\r\n        if (uniformType.indexOf(\"array\") >= 0 && endArray > 0) {\r\n            let startArray = endArray;\r\n            while (startArray > 0 && uniformType.charAt(startArray) !== \" \" && uniformType.charAt(startArray) !== \",\") {\r\n                startArray--;\r\n            }\r\n            const lengthInString = uniformType.substring(startArray + 1, endArray);\r\n            length = +lengthInString;\r\n            if (isNaN(length)) {\r\n                length = +preProcessors[lengthInString.trim()];\r\n            }\r\n            while (startArray > 0 && (uniformType.charAt(startArray) === \" \" || uniformType.charAt(startArray) === \",\")) {\r\n                startArray--;\r\n            }\r\n            uniformType = uniformType.substring(uniformType.indexOf(\"<\") + 1, startArray + 1);\r\n        }\r\n\r\n        return [name, uniformType, length];\r\n    }\r\n\r\n    public initializeShaders(processingContext: Nullable<_IShaderProcessingContext>): void {\r\n        this._webgpuProcessingContext = processingContext as WebGPUShaderProcessingContext;\r\n\r\n        this._attributesInputWGSL = [];\r\n        this._attributesWGSL = [];\r\n        this._attributesConversionCodeWGSL = [];\r\n        this._hasNonFloatAttribute = false;\r\n        this._varyingsWGSL = [];\r\n        this._varyingNamesWGSL = [];\r\n        this._stridedUniformArrays = [];\r\n    }\r\n\r\n    public preProcessShaderCode(code: string): string {\r\n        // Same check as in webgpuShaderProcessorsGLSL to avoid same ubDelcaration to be injected twice.\r\n        const ubDeclaration = this.pureMode\r\n            ? \"\"\r\n            : `struct ${WebGPUShaderProcessor.InternalsUBOName} {\\n  yFactor_: f32,\\n  textureOutputHeight_: f32,\\n};\\nvar<uniform> ${internalsVarName} : ${WebGPUShaderProcessor.InternalsUBOName};\\n`;\r\n        const alreadyInjected = code.indexOf(ubDeclaration) !== -1;\r\n        return alreadyInjected ? code : ubDeclaration + RemoveComments(code);\r\n    }\r\n\r\n    public varyingCheck(varying: string): boolean {\r\n        const regex = /(flat|linear|perspective)?\\s*(center|centroid|sample)?\\s*\\bvarying\\b/;\r\n\r\n        return regex.test(varying);\r\n    }\r\n\r\n    public varyingProcessor(varying: string, isFragment: boolean, preProcessors: { [key: string]: string }) {\r\n        const varyingRegex = /\\s*(flat|linear|perspective)?\\s*(center|centroid|sample)?\\s*varying\\s+(?:(?:highp)?|(?:lowp)?)\\s*(\\S+)\\s*:\\s*(.+)\\s*;/gm;\r\n        const match = varyingRegex.exec(varying);\r\n        if (match !== null) {\r\n            const interpolationType = match[1] ?? \"perspective\";\r\n            const interpolationSampling = match[2] ?? \"center\";\r\n            const varyingType = match[4];\r\n            const name = match[3];\r\n            const interpolation = interpolationType === \"flat\" ? `@interpolate(${interpolationType})` : `@interpolate(${interpolationType}, ${interpolationSampling})`;\r\n            let location: number;\r\n            if (isFragment) {\r\n                location = this._webgpuProcessingContext.availableVaryings[name];\r\n                if (location === undefined) {\r\n                    Logger.Warn(`Invalid fragment shader: The varying named \"${name}\" is not declared in the vertex shader! This declaration will be ignored.`);\r\n                }\r\n            } else {\r\n                location = this._webgpuProcessingContext.getVaryingNextLocation(varyingType, this._getArraySize(name, varyingType, preProcessors)[2]);\r\n                this._webgpuProcessingContext.availableVaryings[name] = location;\r\n                this._varyingsWGSL.push(`  @location(${location}) ${interpolation} ${name} : ${varyingType},`);\r\n                this._varyingNamesWGSL.push(name);\r\n            }\r\n\r\n            varying = \"\";\r\n        }\r\n        return varying;\r\n    }\r\n\r\n    public attributeProcessor(attribute: string, preProcessors: { [key: string]: string }) {\r\n        const attribRegex = /\\s*attribute\\s+(\\S+)\\s*:\\s*(.+)\\s*;/gm;\r\n        const match = attribRegex.exec(attribute);\r\n        if (match !== null) {\r\n            const attributeType = match[2];\r\n            const name = match[1];\r\n            const location = this._webgpuProcessingContext.getAttributeNextLocation(attributeType, this._getArraySize(name, attributeType, preProcessors)[2]);\r\n\r\n            this._webgpuProcessingContext.availableAttributes[name] = location;\r\n            this._webgpuProcessingContext.orderedAttributes[location] = name;\r\n\r\n            const numComponents = this._webgpuProcessingContext.vertexBufferKindToNumberOfComponents[name];\r\n            if (numComponents !== undefined) {\r\n                // Special case for an int/ivecX vertex buffer that is used as a float/vecX attribute in the shader.\r\n                const newType =\r\n                    numComponents < 0 ? (numComponents === -1 ? \"i32\" : \"vec\" + -numComponents + \"<i32>\") : numComponents === 1 ? \"u32\" : \"vec\" + numComponents + \"<u32>\";\r\n                const newName = `_int_${name}_`;\r\n\r\n                this._attributesInputWGSL.push(`@location(${location}) ${newName} : ${newType},`);\r\n                this._attributesWGSL.push(`${name} : ${attributeType},`);\r\n                this._attributesConversionCodeWGSL.push(`vertexInputs.${name} = ${attributeType}(vertexInputs_.${newName});`);\r\n                this._hasNonFloatAttribute = true;\r\n            } else {\r\n                this._attributesInputWGSL.push(`@location(${location}) ${name} : ${attributeType},`);\r\n                this._attributesWGSL.push(`${name} : ${attributeType},`);\r\n                this._attributesConversionCodeWGSL.push(`vertexInputs.${name} = vertexInputs_.${name};`);\r\n            }\r\n            attribute = \"\";\r\n        }\r\n        return attribute;\r\n    }\r\n\r\n    public uniformProcessor(uniform: string, isFragment: boolean, preProcessors: { [key: string]: string }): string {\r\n        const match = this.uniformRegexp.exec(uniform);\r\n        if (match !== null) {\r\n            const uniformType = match[2];\r\n            const name = match[1];\r\n\r\n            this._addUniformToLeftOverUBO(name, uniformType, preProcessors);\r\n\r\n            uniform = \"\";\r\n        }\r\n        return uniform;\r\n    }\r\n\r\n    public textureProcessor(texture: string, isFragment: boolean, preProcessors: { [key: string]: string }): string {\r\n        const match = this.textureRegexp.exec(texture);\r\n        if (match !== null) {\r\n            const name = match[1]; // name of the variable\r\n            const type = match[2]; // texture_2d<f32> or array<texture_2d_array<f32>, 5> for eg\r\n            const isArrayOfTexture = !!match[3];\r\n            const textureFunc = match[4]; // texture_2d, texture_depth_2d, etc\r\n            const isStorageTexture = textureFunc.indexOf(\"storage\") > 0;\r\n            const componentType = match[6]; // f32 or i32 or u32 or undefined\r\n            const storageTextureFormat = isStorageTexture ? (componentType.substring(0, componentType.indexOf(\",\")).trim() as GPUTextureFormat) : null;\r\n\r\n            let arraySize = isArrayOfTexture ? this._getArraySize(name, type, preProcessors)[2] : 0;\r\n            let textureInfo = this._webgpuProcessingContext.availableTextures[name];\r\n            if (!textureInfo) {\r\n                textureInfo = {\r\n                    isTextureArray: arraySize > 0,\r\n                    isStorageTexture,\r\n                    textures: [],\r\n                    sampleType: WebGPUConstants.TextureSampleType.Float,\r\n                };\r\n                arraySize = arraySize || 1;\r\n                for (let i = 0; i < arraySize; ++i) {\r\n                    textureInfo.textures.push(this._webgpuProcessingContext.getNextFreeUBOBinding());\r\n                }\r\n            } else {\r\n                arraySize = textureInfo.textures.length;\r\n            }\r\n\r\n            this._webgpuProcessingContext.availableTextures[name] = textureInfo;\r\n\r\n            const isDepthTexture = textureFunc.indexOf(\"depth\") > 0;\r\n            const textureDimension = gpuTextureViewDimensionByWebGPUTextureFunction[textureFunc];\r\n            const sampleType = isDepthTexture\r\n                ? WebGPUConstants.TextureSampleType.Depth\r\n                : componentType === \"u32\"\r\n                  ? WebGPUConstants.TextureSampleType.Uint\r\n                  : componentType === \"i32\"\r\n                    ? WebGPUConstants.TextureSampleType.Sint\r\n                    : WebGPUConstants.TextureSampleType.Float;\r\n\r\n            textureInfo.sampleType = sampleType;\r\n\r\n            if (textureDimension === undefined) {\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw `Can't get the texture dimension corresponding to the texture function \"${textureFunc}\"!`;\r\n            }\r\n\r\n            for (let i = 0; i < arraySize; ++i) {\r\n                const { groupIndex, bindingIndex } = textureInfo.textures[i];\r\n\r\n                if (i === 0) {\r\n                    texture = `@group(${groupIndex}) @binding(${bindingIndex}) ${texture}`;\r\n                }\r\n\r\n                this._addTextureBindingDescription(name, textureInfo, i, textureDimension, storageTextureFormat, !isFragment);\r\n            }\r\n        }\r\n\r\n        return texture;\r\n    }\r\n\r\n    private _convertDefinesToConst(preProcessors: { [key: string]: string }) {\r\n        let code = \"\";\r\n        for (const key in preProcessors) {\r\n            const value = preProcessors[key];\r\n            if (key.startsWith(\"__\")) {\r\n                continue;\r\n            }\r\n            if (!isNaN(parseInt(value)) || !isNaN(parseFloat(value))) {\r\n                code += `const ${key} = ${value};\\n`;\r\n            } else if (key && value === \"\") {\r\n                code += `const ${key} = true;\\n`;\r\n            }\r\n        }\r\n        return code;\r\n    }\r\n\r\n    public postProcessor(\r\n        code: string,\r\n        _defines: string[],\r\n        _isFragment: boolean,\r\n        _processingContext: Nullable<_IShaderProcessingContext>,\r\n        _parameters: {\r\n            [key: string]: number | string | boolean | undefined;\r\n        },\r\n        preProcessors: { [key: string]: string },\r\n        preProcessorsFromCode: { [key: string]: string }\r\n    ) {\r\n        // Collect the preprocessor names (coming from a \"#define NAME VALUE\" declaration) directly defined in the shader code (preProcessorsFromCode) and not defined at the material level (preProcessors).\r\n        // This is because we will have to perform a replace on the code to replace the defines with their values.\r\n        //\r\n        // We don't have to do it for preprocessor names defined at the material level because replacing them by \"const NAME = VALUE;\" will take care of it (see _convertDefinesToConst()) and is faster than doing a search/replace for each of them.\r\n        //\r\n        // The reason why doing \"const NAME = VALUE;\" doesn't work for preprocessor names defined in the code is that VALUE can be any string and not only numbers or booleans.\r\n        // So, if we have this code:\r\n        //\r\n        //      #define vDiffuseUV vMainUV\r\n        //      textureSample(..., fragmentInputs.vDiffuseUV)\r\n        ///\r\n        // only a search/replace will work, 'const vDiffuseUV = \"vMainUV\";' will not work\r\n        //\r\n        // Note that the search/replace text processing will also apply to the \"#define NAME VALUE\" definition itself, so it will become \"#define VALUE VALUE\"\r\n        // It's not a problem, though, because all #define XXX will be commented out in the final code.\r\n        const defineList: string[] = [];\r\n\r\n        for (const key in preProcessorsFromCode) {\r\n            const value = preProcessorsFromCode[key];\r\n\r\n            // Excludes the defines that are booleans (note that there aren't \"false\" booleans: we simply don't add them in the preProcessorsFromCode object).\r\n            // That's because we need (at least some of) them to stay untouched, like #define DISABLE_UNIFORMTY_ANALYSIS or #define CUSTOM_VERTEX_BEGIN (else, they would be replaced with \"#define true\" after the search/replace processing)\r\n            if (value !== \"true\") {\r\n                defineList.push(key);\r\n            }\r\n        }\r\n\r\n        // We must sort the define names by length to avoid replacing a define with a longer name (ex: #define A 1 and #define AB 2, if we replace A with 1, we will have #define 1B 2)\r\n        // So, we start by longest names and we finish with the shortest ones.\r\n        defineList.sort((a, b) => (a.length - b.length > 0 ? -1 : a.length === b.length ? 0 : 1));\r\n\r\n        for (const name of defineList) {\r\n            // Let's retrieve the value of the define from the code\r\n            // Note that we can't use the value from preProcessorsFromCode[name] because this value could have been changed from a previous replace\r\n            // For example:\r\n            //      #define IOR 1.333\r\n            //      #define ETA 1.0/IOR\r\n            //\r\n            // After IOR replacement is performed, we will have:\r\n            //      #define 1.333 1.333\r\n            //      #define ETA 1.0/1.333\r\n            //\r\n            // but preProcessorsFromCode[\"ETA\"] is still \"1.0/IOR\" and not \"1.0/1.333\", so we must retrieve the value for ETA from the current code\r\n            const i0 = code.indexOf(\"#define \" + name);\r\n\r\n            let i1 = code.indexOf(\"\\n\", i0);\r\n            if (i1 === -1) {\r\n                i1 = code.length;\r\n            }\r\n\r\n            const value = code.substring(i0 + 8 + name.length + 1, i1);\r\n\r\n            code = code.replace(new RegExp(name, \"g\"), value);\r\n        }\r\n\r\n        code = this._convertDefinesToConst(preProcessors) + code;\r\n\r\n        return code;\r\n    }\r\n\r\n    public finalizeShaders(vertexCode: string, fragmentCode: string): { vertexCode: string; fragmentCode: string } {\r\n        const enabledExtensions: string[] = [];\r\n\r\n        const fragCoordCode =\r\n            fragmentCode.indexOf(\"fragmentInputs.position\") >= 0 && !this.pureMode\r\n                ? `\r\n            if (internals.yFactor_ == 1.) {\r\n                fragmentInputs.position.y = internals.textureOutputHeight_ - fragmentInputs.position.y;\r\n            }\r\n        `\r\n                : \"\";\r\n\r\n        // Add the group/binding info to the sampler declaration (var xxx: sampler|sampler_comparison)\r\n        vertexCode = this._processSamplers(vertexCode, true);\r\n        fragmentCode = this._processSamplers(fragmentCode, false);\r\n\r\n        // Add the group/binding info to the uniform/storage buffer declarations (var<uniform> XXX:YYY or var<storage(,read_write|read)> XXX:YYY)\r\n        vertexCode = this._processCustomBuffers(vertexCode, true);\r\n        fragmentCode = this._processCustomBuffers(fragmentCode, false);\r\n\r\n        // Builds the leftover UBOs.\r\n        const leftOverUBO = this._buildLeftOverUBO();\r\n\r\n        vertexCode = leftOverUBO + vertexCode;\r\n        fragmentCode = leftOverUBO + fragmentCode;\r\n\r\n        // Vertex code\r\n        vertexCode = vertexCode.replace(/#define /g, \"//#define \");\r\n        vertexCode = this._processStridedUniformArrays(vertexCode);\r\n\r\n        let vertexInputs = \"struct VertexInputs {\\n  @builtin(vertex_index) vertexIndex : u32,\\n  @builtin(instance_index) instanceIndex : u32,\\n\";\r\n        if (this._attributesInputWGSL.length > 0) {\r\n            vertexInputs += this._attributesInputWGSL.join(\"\\n\");\r\n        }\r\n        vertexInputs += \"\\n};\\nvar<private> vertexInputs\" + (this._hasNonFloatAttribute ? \"_\" : \"\") + \" : VertexInputs;\\n\";\r\n        if (this._hasNonFloatAttribute) {\r\n            vertexInputs += \"struct VertexInputs_ {\\n  vertexIndex : u32, instanceIndex : u32,\\n\";\r\n            vertexInputs += this._attributesWGSL.join(\"\\n\");\r\n            vertexInputs += \"\\n};\\nvar<private> vertexInputs : VertexInputs_;\\n\";\r\n        }\r\n\r\n        let vertexOutputs = \"struct FragmentInputs {\\n  @builtin(position) position : vec4<f32>,\\n\";\r\n        if (this._varyingsWGSL.length > 0) {\r\n            vertexOutputs += this._varyingsWGSL.join(\"\\n\");\r\n        }\r\n        vertexOutputs += \"\\n};\\nvar<private> vertexOutputs : FragmentInputs;\\n\";\r\n\r\n        vertexCode = vertexInputs + vertexOutputs + vertexCode;\r\n\r\n        let vertexMainStartingCode = `\\n  vertexInputs${this._hasNonFloatAttribute ? \"_\" : \"\"} = input;\\n`;\r\n        if (this._hasNonFloatAttribute) {\r\n            vertexMainStartingCode += \"vertexInputs.vertexIndex = vertexInputs_.vertexIndex;\\nvertexInputs.instanceIndex = vertexInputs_.instanceIndex;\\n\";\r\n            vertexMainStartingCode += this._attributesConversionCodeWGSL.join(\"\\n\");\r\n            vertexMainStartingCode += \"\\n\";\r\n        }\r\n        const vertexMainEndingCode = this.pureMode\r\n            ? `  return vertexOutputs;`\r\n            : `  vertexOutputs.position.y = vertexOutputs.position.y * internals.yFactor_;\\n  return vertexOutputs;`;\r\n        let needDiagnosticOff = vertexCode.indexOf(Constants.DISABLEUA) !== -1;\r\n\r\n        vertexCode =\r\n            (needDiagnosticOff ? \"diagnostic(off, derivative_uniformity);\\n\" : \"\") +\r\n            \"diagnostic(off, chromium.unreachable_code);\\n\" +\r\n            InjectStartingAndEndingCode(vertexCode, \"fn main\", vertexMainStartingCode, vertexMainEndingCode);\r\n\r\n        // fragment code\r\n        fragmentCode = fragmentCode.replace(/#define /g, \"//#define \");\r\n        fragmentCode = this._processStridedUniformArrays(fragmentCode);\r\n        if (!this.pureMode) {\r\n            fragmentCode = fragmentCode.replace(/dpdy/g, \"(-internals.yFactor_)*dpdy\"); // will also handle dpdyCoarse and dpdyFine\r\n        }\r\n\r\n        let fragmentInputs = \"struct FragmentInputs {\\n  @builtin(position) position : vec4<f32>,\\n  @builtin(front_facing) frontFacing : bool,\\n\";\r\n        if (this._varyingsWGSL.length > 0) {\r\n            fragmentInputs += this._varyingsWGSL.join(\"\\n\");\r\n        }\r\n        fragmentInputs += \"\\n};\\nvar<private> fragmentInputs : FragmentInputs;\\n\";\r\n\r\n        let fragmentOutputs = \"struct FragmentOutputs {\\n\";\r\n\r\n        // Adding fragData output locations\r\n        const regexRoot = \"fragmentOutputs\\\\.fragData\";\r\n        let match = fragmentCode.match(new RegExp(regexRoot + \"0\", \"g\"));\r\n        let indexLocation = 0;\r\n\r\n        if (match) {\r\n            fragmentOutputs += ` @location(${indexLocation}) fragData0 : vec4<f32>,\\n`;\r\n            indexLocation++;\r\n            for (let index = 1; index < 8; index++) {\r\n                match = fragmentCode.match(new RegExp(regexRoot + index, \"g\"));\r\n                if (match) {\r\n                    fragmentOutputs += ` @location(${indexLocation}) fragData${indexLocation} : vec4<f32>,\\n`;\r\n                    indexLocation++;\r\n                }\r\n            }\r\n            if (fragmentCode.indexOf(\"MRT_AND_COLOR\") !== -1) {\r\n                fragmentOutputs += `  @location(${indexLocation}) color : vec4<f32>,\\n`;\r\n                indexLocation++;\r\n            }\r\n        }\r\n\r\n        // Adding fragData output locations\r\n        const regex = /oitDepthSampler/;\r\n        match = fragmentCode.match(regex);\r\n\r\n        if (match) {\r\n            fragmentOutputs += ` @location(${indexLocation++}) depth : vec2<f32>,\\n`;\r\n            fragmentOutputs += ` @location(${indexLocation++}) frontColor : vec4<f32>,\\n`;\r\n            fragmentOutputs += ` @location(${indexLocation++}) backColor : vec4<f32>,\\n`;\r\n        }\r\n\r\n        if (indexLocation === 0) {\r\n            const useDualSourceBlending = fragmentCode.indexOf(\"DUAL_SOURCE_BLENDING\") !== -1;\r\n\r\n            if (useDualSourceBlending) {\r\n                enabledExtensions.push(\"dual_source_blending\");\r\n\r\n                fragmentOutputs += \"  @location(0) @blend_src(0) color : vec4<f32>,\\n\";\r\n                fragmentOutputs += \"  @location(0) @blend_src(1) color2 : vec4<f32>,\\n\";\r\n            } else {\r\n                fragmentOutputs += \"  @location(0) color : vec4<f32>,\\n\";\r\n            }\r\n            indexLocation++;\r\n        }\r\n\r\n        // FragDepth\r\n        let hasFragDepth = false;\r\n        let idx = 0;\r\n        while (!hasFragDepth) {\r\n            idx = fragmentCode.indexOf(builtInName_frag_depth, idx);\r\n            if (idx < 0) {\r\n                break;\r\n            }\r\n            const saveIndex = idx;\r\n            hasFragDepth = true;\r\n            while (idx > 1 && fragmentCode.charAt(idx) !== \"\\n\") {\r\n                if (fragmentCode.charAt(idx) === \"/\" && fragmentCode.charAt(idx - 1) === \"/\") {\r\n                    hasFragDepth = false;\r\n                    break;\r\n                }\r\n                idx--;\r\n            }\r\n            idx = saveIndex + builtInName_frag_depth.length;\r\n        }\r\n\r\n        if (hasFragDepth) {\r\n            fragmentOutputs += \"  @builtin(frag_depth) fragDepth: f32,\\n\";\r\n        }\r\n\r\n        fragmentOutputs += \"};\\nvar<private> fragmentOutputs : FragmentOutputs;\\n\";\r\n\r\n        fragmentCode = fragmentInputs + fragmentOutputs + fragmentCode;\r\n\r\n        const fragmentStartingCode = \"  fragmentInputs = input;\\n  \" + fragCoordCode;\r\n        const fragmentEndingCode = \"  return fragmentOutputs;\";\r\n        needDiagnosticOff = fragmentCode.indexOf(Constants.DISABLEUA) !== -1;\r\n\r\n        if (enabledExtensions.length > 0) {\r\n            fragmentCode = \"enable \" + enabledExtensions.join(\";\\nenable \") + \";\\n\" + fragmentCode;\r\n        }\r\n\r\n        fragmentCode =\r\n            (needDiagnosticOff ? \"diagnostic(off, derivative_uniformity);\\n\" : \"\") +\r\n            \"diagnostic(off, chromium.unreachable_code);\\n\" +\r\n            InjectStartingAndEndingCode(fragmentCode, \"fn main\", fragmentStartingCode, fragmentEndingCode);\r\n\r\n        this._collectBindingNames();\r\n        this._preCreateBindGroupEntries();\r\n\r\n        this._webgpuProcessingContext.vertexBufferKindToNumberOfComponents = {};\r\n\r\n        return { vertexCode, fragmentCode };\r\n    }\r\n\r\n    protected _generateLeftOverUBOCode(name: string, uniformBufferDescription: WebGPUBufferDescription): string {\r\n        let stridedArrays = \"\";\r\n        let ubo = `struct ${name} {\\n`;\r\n        for (const leftOverUniform of this._webgpuProcessingContext.leftOverUniforms) {\r\n            const type = leftOverUniform.type.replace(/^(.*?)(<.*>)?$/, \"$1\");\r\n            const size = WebGPUShaderProcessor.UniformSizes[type];\r\n\r\n            if (leftOverUniform.length > 0) {\r\n                if (size <= 2) {\r\n                    const stridedArrayType = `${name}_${this._stridedUniformArrays.length}_strided_arr`;\r\n                    stridedArrays += `struct ${stridedArrayType} {\r\n                        @size(16)\r\n                        el: ${type},\r\n                    }`;\r\n                    this._stridedUniformArrays.push(leftOverUniform.name);\r\n\r\n                    ubo += ` @align(16) ${leftOverUniform.name} : array<${stridedArrayType}, ${leftOverUniform.length}>,\\n`;\r\n                } else {\r\n                    ubo += ` ${leftOverUniform.name} : array<${leftOverUniform.type}, ${leftOverUniform.length}>,\\n`;\r\n                }\r\n            } else {\r\n                ubo += `  ${leftOverUniform.name} : ${leftOverUniform.type},\\n`;\r\n            }\r\n        }\r\n        ubo += \"};\\n\";\r\n        ubo = `${stridedArrays}\\n${ubo}`;\r\n        ubo += `@group(${uniformBufferDescription.binding.groupIndex}) @binding(${uniformBufferDescription.binding.bindingIndex}) var<uniform> ${leftOverVarName} : ${name};\\n`;\r\n\r\n        return ubo;\r\n    }\r\n\r\n    private _processSamplers(code: string, isVertex: boolean): string {\r\n        const samplerRegexp = /var\\s+(\\w+Sampler)\\s*:\\s*(sampler|sampler_comparison)\\s*;/gm;\r\n\r\n        // eslint-disable-next-line no-constant-condition\r\n        while (true) {\r\n            const match = samplerRegexp.exec(code);\r\n            if (match === null) {\r\n                break;\r\n            }\r\n\r\n            const name = match[1]; // name of the variable\r\n            const samplerType = match[2]; // sampler or sampler_comparison\r\n            const suffixLessLength = name.length - Constants.AUTOSAMPLERSUFFIX.length;\r\n            const textureName = name.lastIndexOf(Constants.AUTOSAMPLERSUFFIX) === suffixLessLength ? name.substring(0, suffixLessLength) : null;\r\n            const samplerBindingType = samplerType === \"sampler_comparison\" ? WebGPUConstants.SamplerBindingType.Comparison : WebGPUConstants.SamplerBindingType.Filtering;\r\n\r\n            if (textureName) {\r\n                const textureInfo = this._webgpuProcessingContext.availableTextures[textureName];\r\n                if (textureInfo) {\r\n                    textureInfo.autoBindSampler = true;\r\n                }\r\n            }\r\n\r\n            let samplerInfo = this._webgpuProcessingContext.availableSamplers[name];\r\n            if (!samplerInfo) {\r\n                samplerInfo = {\r\n                    binding: this._webgpuProcessingContext.getNextFreeUBOBinding(),\r\n                    type: samplerBindingType,\r\n                };\r\n                this._webgpuProcessingContext.availableSamplers[name] = samplerInfo;\r\n            }\r\n\r\n            this._addSamplerBindingDescription(name, samplerInfo, isVertex);\r\n\r\n            const part1 = code.substring(0, match.index);\r\n            const insertPart = `@group(${samplerInfo.binding.groupIndex}) @binding(${samplerInfo.binding.bindingIndex}) `;\r\n            const part2 = code.substring(match.index);\r\n\r\n            code = part1 + insertPart + part2;\r\n\r\n            samplerRegexp.lastIndex += insertPart.length;\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    private _processCustomBuffers(code: string, isVertex: boolean): string {\r\n        const instantiateBufferRegexp = /var<\\s*(uniform|storage)\\s*(,\\s*(read|read_write)\\s*)?>\\s+(\\S+)\\s*:\\s*(\\S+)\\s*;/gm;\r\n\r\n        // eslint-disable-next-line no-constant-condition\r\n        while (true) {\r\n            const match = instantiateBufferRegexp.exec(code);\r\n            if (match === null) {\r\n                break;\r\n            }\r\n\r\n            const type = match[1];\r\n            const decoration = match[3];\r\n            let name = match[4];\r\n            const structName = match[5];\r\n\r\n            let bufferInfo = this._webgpuProcessingContext.availableBuffers[name];\r\n            if (!bufferInfo) {\r\n                const knownUBO = type === \"uniform\" ? WebGPUShaderProcessingContext.KnownUBOs[structName] : null;\r\n\r\n                let binding;\r\n                if (knownUBO) {\r\n                    name = structName;\r\n                    binding = knownUBO.binding;\r\n                    if (binding.groupIndex === -1) {\r\n                        binding = this._webgpuProcessingContext.availableBuffers[name]?.binding;\r\n                        if (!binding) {\r\n                            binding = this._webgpuProcessingContext.getNextFreeUBOBinding();\r\n                        }\r\n                    }\r\n                } else {\r\n                    binding = this._webgpuProcessingContext.getNextFreeUBOBinding();\r\n                }\r\n\r\n                bufferInfo = { binding };\r\n                this._webgpuProcessingContext.availableBuffers[name] = bufferInfo;\r\n            }\r\n\r\n            this._addBufferBindingDescription(\r\n                name,\r\n                this._webgpuProcessingContext.availableBuffers[name],\r\n                decoration === \"read_write\"\r\n                    ? WebGPUConstants.BufferBindingType.Storage\r\n                    : type === \"storage\"\r\n                      ? WebGPUConstants.BufferBindingType.ReadOnlyStorage\r\n                      : WebGPUConstants.BufferBindingType.Uniform,\r\n                isVertex\r\n            );\r\n\r\n            const groupIndex = bufferInfo.binding.groupIndex;\r\n            const bindingIndex = bufferInfo.binding.bindingIndex;\r\n\r\n            const part1 = code.substring(0, match.index);\r\n            const insertPart = `@group(${groupIndex}) @binding(${bindingIndex}) `;\r\n            const part2 = code.substring(match.index);\r\n\r\n            code = part1 + insertPart + part2;\r\n\r\n            instantiateBufferRegexp.lastIndex += insertPart.length;\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    private _processStridedUniformArrays(code: string): string {\r\n        for (const uniformArrayName of this._stridedUniformArrays) {\r\n            code = code.replace(new RegExp(`${uniformArrayName}\\\\s*\\\\[(.*?)\\\\]`, \"g\"), `${uniformArrayName}[$1].el`);\r\n        }\r\n        return code;\r\n    }\r\n}\r\n"]}