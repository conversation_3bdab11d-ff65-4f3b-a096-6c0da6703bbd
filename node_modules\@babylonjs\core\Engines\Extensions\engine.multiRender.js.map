{"version": 3, "file": "engine.multiRender.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.multiRender.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAElG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AA8E3C,UAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG;IAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AACpC,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,sCAAsC,GAAG;IAC1D,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,aAAwB;IACxE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,MAAM,MAAM,GAAG,EAAE,CAAC;IAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAO,EAAG,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,WAAqB;IAClE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,qCAAqC,GAAG,UACzD,SAAmC,EACnC,yBAAkC,KAAK,EACvC,cAA2B;IAE3B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAEjC,IAAI,CAAC,SAAS,CAAC,2BAA2B,EAAE,CAAC;QACzC,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC1B,IAAI,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,cAAc,EAAE,CAAC;QACjB,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAC7B,+BAA+B;YAC/B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QACzD,CAAC;QACD,cAAc,EAAE,CAAC;IACrB,CAAC;IAED,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;AACvC,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,0BAA0B,GAAG,UAAU,IAAiB,EAAE,OAAkC,EAAE,oBAA6B,IAAI;IAChJ,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAI,mBAAmB,GAAG,IAAI,CAAC;IAC/B,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAClC,IAAI,oBAAoB,GAAG,KAAK,CAAC;IACjC,IAAI,kBAAkB,GAAuB,SAAS,CAAC;IACvD,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,OAAO,GAAG,CAAC,CAAC;IAEhB,MAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC;IACxD,MAAM,mBAAmB,GAAG,SAAS,CAAC,8BAA8B,CAAC;IACrE,MAAM,oBAAoB,GAAG,KAAK,CAAC;IACnC,MAAM,aAAa,GAAG,SAAS,CAAC,kBAAkB,CAAC;IACnD,MAAM,aAAa,GAAG,SAAS,CAAC,UAAU,CAAC;IAE3C,IAAI,KAAK,GAAa,EAAE,CAAC;IACzB,IAAI,aAAa,GAAa,EAAE,CAAC;IACjC,IAAI,cAAc,GAAc,EAAE,CAAC;IACnC,IAAI,OAAO,GAAa,EAAE,CAAC;IAC3B,IAAI,OAAO,GAAa,EAAE,CAAC;IAC3B,IAAI,SAAS,GAAa,EAAE,CAAC;IAC7B,IAAI,UAAU,GAAa,EAAE,CAAC;IAC9B,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,IAAI,kBAAkB,GAAG,KAAK,CAAC;IAE/B,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAA6B,CAAC;IAEzG,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QACxB,eAAe,GAAG,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;QAC1F,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACrG,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;QAC5G,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC;QACzG,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;QACzC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC;QACrC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;QAC/B,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,aAAa,CAAC;QACvD,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC;QAC1D,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC;QACrC,OAAO,GAAG,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC;QACzC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC;QAC3C,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC;QAC9C,MAAM,GAAG,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC;QACvC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC;QAClC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,KAAK,CAAC;QAEzD,IACI,IAAI,CAAC,YAAY,GAAG,CAAC;YACrB,CAAC,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,8BAA8B;gBACpE,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,mCAAmC;gBAC5E,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,qBAAqB;gBAC9D,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,2BAA2B;gBACpE,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC,mCAAmC,CAAC,EACnF,CAAC;YACC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACpD,CAAC;IACL,CAAC;IAED,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;QACnC,kBAAkB,GAAG,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC;IAClI,CAAC;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,yBAAyB;IACzB,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;IACpD,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAC3C,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAE1C,MAAM,KAAK,GAAuC,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;IAC9E,MAAM,MAAM,GAAuC,IAAK,CAAC,MAAM,IAAY,IAAI,CAAC;IAEhF,MAAM,QAAQ,GAAsB,EAAE,CAAC;IACvC,MAAM,WAAW,GAAa,EAAE,CAAC;IAEjC,MAAM,iBAAiB,GACnB,IAAI,CAAC,YAAY,GAAG,CAAC;QACrB,CAAC,kBAAkB,KAAK,SAAS,CAAC,8BAA8B;YAC5D,kBAAkB,KAAK,SAAS,CAAC,mCAAmC;YACpE,kBAAkB,KAAK,SAAS,CAAC,mCAAmC,CAAC,CAAC;IAE9E,SAAS,CAAC,KAAK,GAAG,OAAO,EAAE,KAAK,IAAI,0BAA0B,CAAC;IAC/D,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;IACrC,SAAS,CAAC,oBAAoB,GAAG,oBAAoB,IAAI,mBAAmB,CAAC;IAC7E,SAAS,CAAC,sBAAsB,GAAG,oBAAoB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,qBAAqB,CAAC;IACpG,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAAC,SAAS,CAAC,sBAAsB,EAAE,SAAS,CAAC,oBAAoB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,kBAAkB,CAAC,CAAC;IAC/K,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;IAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;QACpC,IAAI,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC;QAC3D,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC;QACnC,IAAI,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,IAAI,oBAAoB,CAAC;QAC9D,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC;QAE3C,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,aAAa,CAAC;QAC3C,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC;YAClF,yEAAyE;YACzE,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QAC1D,CAAC;aAAM,IAAI,IAAI,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC;YAClG,2EAA2E;YAC3E,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QAC1D,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;QAC3E,IAAI,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;YACnE,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;YAC3C,MAAM,CAAC,IAAI,CAAC,0FAA0F,CAAC,CAAC;QAC5G,CAAC;QAED,aAAa,GAAG,aAAa,IAAI,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE3G,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACvC,MAAM,UAAU,GAAS,EAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;QAEpG,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE7B,IAAI,MAAM,KAAK,CAAC,CAAC,IAAI,kBAAkB,EAAE,CAAC;YACtC,SAAS;QACb,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,kDAA0C,CAAC;QACnF,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;QAEtB,EAAE,CAAC,aAAa,CAAO,EAAG,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;QAC3C,EAAE,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAiB,CAAC,kBAAkB,CAAC,CAAC;QAErE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAC9D,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QAE9D,MAAM,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAChG,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,QAAQ,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,gBAAgB,IAAI,MAAM,KAAK,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;YACzF,IAAI,MAAM,KAAK,SAAS,CAAC,gBAAgB,EAAE,CAAC;gBACxC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;YACxB,CAAC;YAED,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC;YAE/C,EAAE,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,EAAE,cAAc,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;QACxH,CAAC;aAAM,IAAI,MAAM,KAAK,SAAS,CAAC,gBAAgB,EAAE,CAAC;YAC/C,4DAA4D;YAC5D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACzB,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,CAAC,EAAE,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;YACxI,CAAC;YACD,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QAC1B,CAAC;aAAM,CAAC;YACJ,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;QACnH,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YAClB,EAAE,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;QAED,SAAS;QACT,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAExC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;QACpB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,cAAc,GAAG,aAAa,CAAC;QACvC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;QAE9D,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,oBAAoB,IAAI,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClF,gBAAgB;QAChB,MAAM,YAAY,GAAG,IAAI,eAAe,CAAC,IAAI,uCAA8B,CAAC;QAE5E,IAAI,gBAAgB,GAAG,SAAS,CAAC,0BAA0B,CAAC;QAC5D,IAAI,4BAA4B,GAAW,EAAE,CAAC,iBAAiB,CAAC;QAChE,IAAI,oBAAoB,GAAW,EAAE,CAAC,eAAe,CAAC;QACtD,IAAI,kBAAkB,GAAW,EAAE,CAAC,cAAc,CAAC;QACnD,IAAI,wBAAwB,GAAW,EAAE,CAAC,gBAAgB,CAAC;QAC3D,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;YACxB,4BAA4B,GAAG,EAAE,CAAC,eAAe,CAAC;QACtD,CAAC;aAAM,CAAC;YACJ,IAAI,kBAAkB,KAAK,SAAS,CAAC,2BAA2B,EAAE,CAAC;gBAC/D,gBAAgB,GAAG,SAAS,CAAC,iBAAiB,CAAC;gBAC/C,kBAAkB,GAAG,EAAE,CAAC,KAAK,CAAC;gBAC9B,4BAA4B,GAAG,EAAE,CAAC,kBAAkB,CAAC;YACzD,CAAC;iBAAM,IAAI,kBAAkB,KAAK,SAAS,CAAC,mCAAmC,EAAE,CAAC;gBAC9E,gBAAgB,GAAG,SAAS,CAAC,yBAAyB,CAAC;gBACvD,kBAAkB,GAAG,EAAE,CAAC,8BAA8B,CAAC;gBACvD,4BAA4B,GAAG,EAAE,CAAC,iBAAiB,CAAC;gBACpD,oBAAoB,GAAG,EAAE,CAAC,aAAa,CAAC;gBACxC,wBAAwB,GAAG,EAAE,CAAC,wBAAwB,CAAC;YAC3D,CAAC;iBAAM,IAAI,kBAAkB,KAAK,SAAS,CAAC,qBAAqB,EAAE,CAAC;gBAChE,gBAAgB,GAAG,SAAS,CAAC,yBAAyB,CAAC;gBACvD,kBAAkB,GAAG,EAAE,CAAC,YAAY,CAAC;gBACrC,4BAA4B,GAAG,EAAE,CAAC,iBAAiB,CAAC;gBACpD,wBAAwB,GAAG,EAAE,CAAC,gBAAgB,CAAC;YACnD,CAAC;iBAAM,IAAI,kBAAkB,KAAK,SAAS,CAAC,8BAA8B,IAAI,kBAAkB,KAAK,SAAS,CAAC,mCAAmC,EAAE,CAAC;gBACjJ,gBAAgB,GAAG,SAAS,CAAC,6BAA6B,CAAC;gBAC3D,kBAAkB,GAAG,EAAE,CAAC,iBAAiB,CAAC;gBAC1C,4BAA4B,GAAG,EAAE,CAAC,gBAAgB,CAAC;gBACnD,oBAAoB,GAAG,EAAE,CAAC,aAAa,CAAC;gBACxC,wBAAwB,GAAG,EAAE,CAAC,wBAAwB,CAAC;YAC3D,CAAC;QACL,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAE7D,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;QACnE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;QACnE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QACrE,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;QACrE,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,4BAA4B,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC;QAEhI,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,wBAAwB,EAAE,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC,gBAAiB,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;QAEvI,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAE/C,SAAS,CAAC,oBAAoB,GAAG,YAAY,CAAC;QAC9C,SAAS,CAAC,+BAA+B,GAAG,iBAAiB,CAAC;QAE9D,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;QAC/B,YAAY,CAAC,UAAU,GAAG,MAAM,CAAC;QACjC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;QAC3B,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;QAC7B,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QAC5B,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC;QACzB,YAAY,CAAC,eAAe,GAAG,eAAe,CAAC;QAC/C,YAAY,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QACnE,YAAY,CAAC,MAAM,GAAG,kBAAkB,CAAC;QACzC,YAAY,CAAC,IAAI,GAAG,gBAAgB,CAAC;QACrC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG,eAAe,CAAC;QAEvD,QAAQ,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC;QACtC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACnD,CAAC;IACD,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IAChC,IAAI,iBAAiB,EAAE,CAAC;QACpB,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAChC,CAAC;IAED,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;IAEjD,SAAS,CAAC,sBAAsB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAExD,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAEzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACtB,IAAI,CAAC,4CAA4C,CAAC,SAAS,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;IAC7F,CAAC;SAAM,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAE3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAClE,CAAC;QAED,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC7B,SAAS,CAAC,gBAAgB,GAAG,WAAW,CAAC;QAEzC,IAAI,YAAY,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACxC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;YAC1C,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAC5B,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,4CAA4C,GAAG,UAChE,SAA6C,EAC7C,OAAe,EACf,oBAA6B,IAAI;IAEjC,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACtC,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAI,SAAS,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;QAChC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC;IAE3D,kCAAkC;IAClC,IAAI,SAAS,CAAC,mBAAmB,EAAE,CAAC;QAChC,EAAE,CAAC,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;QACrD,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACzC,CAAC;IAED,IAAI,SAAS,CAAC,gBAAgB,EAAE,CAAC;QAC7B,EAAE,CAAC,iBAAiB,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QACjD,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED,MAAM,KAAK,GAAG,SAAS,CAAC,YAAa,CAAC,MAAM,CAAC,CAAC,6IAA6I;IAE3L,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,eAAe,GAAG,OAAO,CAAC,gBAAkD,CAAC;QAEnF,eAAe,EAAE,wBAAwB,EAAE,CAAC;IAChD,CAAC;IAED,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,EAAE,CAAC,8BAA8B,KAAK,UAAU,EAAE,CAAC;QACzE,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAE3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;QAClE,CAAC;QAED,SAAS,CAAC,gBAAgB,GAAG,WAAW,CAAC;QACzC,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QAE1C,MAAM,WAAW,GAAG,EAAE,CAAC;QAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,eAAe,GAAG,OAAO,CAAC,gBAAwC,CAAC;YACzE,MAAM,UAAU,GAAS,EAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;YAEjH,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAC9C,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,EACd,OAAO,EACP,CAAC,CAAC,CAAC,cAAc,EACjB,IAAI,CAAC,iCAAiC,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,EAC5F,UAAU,CACb,CAAC;YAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAClE,CAAC;YAED,eAAe,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;YACvD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;YAE1B,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjC,CAAC;QACD,IAAI,iBAAiB,EAAE,CAAC;YACpB,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAChC,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,WAAW,GAAG,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;IAEvG,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAClE,SAAS,CAAC,sBAAsB,EAChC,SAAS,CAAC,oBAAoB,EAC9B,SAAS,CAAC,KAAK,EACf,SAAS,CAAC,MAAM,EAChB,OAAO,EACP,WAAW,CACd,CAAC;IAEF,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC;IAE7B,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,+BAA+B,GAAG,UAAU,OAA4B;IACzF,MAAM,SAAS,GAAG,OAAmC,CAAC;IACtD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACrB,OAAO;IACX,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,YAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtD,MAAM,OAAO,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,OAAO,EAAE,eAAe,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;YACjE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YACxD,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;YACjC,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;AACL,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,OAA4B;IACjF,MAAM,SAAS,GAAG,OAAmC,CAAC;IACtD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACpD,OAAO;IACX,CAAC;IAED,IAAI,UAAU,GAAG,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,UAAU,IAAI,SAAS,CAAC,oBAAoB,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;IACrG,UAAU,IAAI,SAAS,CAAC,sBAAsB,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3G,MAAM,WAAW,GAAG,SAAS,CAAC,YAAa,CAAC;IAC5C,MAAM,KAAK,GAAG,WAAW,CAAC,MAAM,CAAC;IAEjC,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC;IACpE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,gBAAgB,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC;IAEhE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,SAAS,CAAC,QAAS,CAAC,CAAC,CAAC,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;QAC7B,CAAC;QAED,WAAW,CAAC,CAAC,CAAC,GAAS,EAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;QAC/G,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QAC5B,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;IACzH,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7B,WAAW,CAAC,CAAC,CAAC,GAAS,EAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;IACnH,CAAC;IAED,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAC5B,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC;AACzE,CAAC,CAAC", "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport type { IMultiRenderTargetOptions } from \"../../Materials/Textures/multiRenderTarget\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Constants } from \"../constants\";\r\nimport { ThinEngine } from \"../thinEngine\";\r\nimport type { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { WebGLRenderTargetWrapper } from \"../WebGL/webGLRenderTargetWrapper\";\r\nimport type { WebGLHardwareTexture } from \"../WebGL/webGLHardwareTexture\";\r\nimport type { TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Unbind a list of render target textures from the webGL context\r\n         * This is used only when drawBuffer extension or webGL2 are active\r\n         * @param rtWrapper defines the render target wrapper to unbind\r\n         * @param disableGenerateMipMaps defines a boolean indicating that mipmaps must not be generated\r\n         * @param onBeforeUnbind defines a function which will be called before the effective unbind\r\n         */\r\n        unBindMultiColorAttachmentFramebuffer(rtWrapper: RenderTargetWrapper, disableGenerateMipMaps: boolean, onBeforeUnbind?: () => void): void;\r\n\r\n        /**\r\n         * Create a multi render target texture\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#multiple-render-target\r\n         * @param size defines the size of the texture\r\n         * @param options defines the creation options\r\n         * @param initializeBuffers if set to true, the engine will make an initializing call of drawBuffers\r\n         * @returns a new render target wrapper ready to render textures\r\n         */\r\n        createMultipleRenderTarget(size: TextureSize, options: IMultiRenderTargetOptions, initializeBuffers?: boolean): RenderTargetWrapper;\r\n\r\n        /**\r\n         * Update the sample count for a given multiple render target texture\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#multisample-render-targets\r\n         * @param rtWrapper defines the render target wrapper to update\r\n         * @param samples defines the sample count to set\r\n         * @param initializeBuffers if set to true, the engine will make an initializing call of drawBuffers\r\n         * @returns the effective sample count (could be 0 if multisample render targets are not supported)\r\n         */\r\n        updateMultipleRenderTargetTextureSampleCount(rtWrapper: Nullable<RenderTargetWrapper>, samples: number, initializeBuffers?: boolean): number;\r\n\r\n        /**\r\n         * Generates mipmaps for the texture of the (multi) render target\r\n         * @param texture The render target containing the textures to generate the mipmaps for\r\n         */\r\n        generateMipMapsMultiFramebuffer(texture: RenderTargetWrapper): void;\r\n\r\n        /**\r\n         * Resolves the MSAA textures of the (multi) render target into their non-MSAA version.\r\n         * Note that if \"texture\" is not a MSAA render target, no resolve is performed.\r\n         * @param texture The render target texture containing the MSAA textures to resolve\r\n         */\r\n        resolveMultiFramebuffer(texture: RenderTargetWrapper): void;\r\n\r\n        /**\r\n         * Select a subsets of attachments to draw to.\r\n         * @param attachments gl attachments\r\n         */\r\n        bindAttachments(attachments: number[]): void;\r\n\r\n        /**\r\n         * Creates a layout object to draw/clear on specific textures in a MRT\r\n         * @param textureStatus textureStatus[i] indicates if the i-th is active\r\n         * @returns A layout to be fed to the engine, calling `bindAttachments`.\r\n         */\r\n        buildTextureLayout(textureStatus: boolean[]): number[];\r\n\r\n        /**\r\n         * Restores the webgl state to only draw on the main color attachment\r\n         * when the frame buffer associated is the canvas frame buffer\r\n         */\r\n        restoreSingleAttachment(): void;\r\n\r\n        /**\r\n         * Restores the webgl state to only draw on the main color attachment\r\n         * when the frame buffer associated is not the canvas frame buffer\r\n         */\r\n        restoreSingleAttachmentForRenderTarget(): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.restoreSingleAttachment = function (): void {\r\n    const gl = this._gl;\r\n\r\n    this.bindAttachments([gl.BACK]);\r\n};\r\n\r\nThinEngine.prototype.restoreSingleAttachmentForRenderTarget = function (): void {\r\n    const gl = this._gl;\r\n\r\n    this.bindAttachments([gl.COLOR_ATTACHMENT0]);\r\n};\r\n\r\nThinEngine.prototype.buildTextureLayout = function (textureStatus: boolean[]): number[] {\r\n    const gl = this._gl;\r\n\r\n    const result = [];\r\n\r\n    for (let i = 0; i < textureStatus.length; i++) {\r\n        if (textureStatus[i]) {\r\n            result.push((<any>gl)[\"COLOR_ATTACHMENT\" + i]);\r\n        } else {\r\n            result.push(gl.NONE);\r\n        }\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nThinEngine.prototype.bindAttachments = function (attachments: number[]): void {\r\n    const gl = this._gl;\r\n\r\n    gl.drawBuffers(attachments);\r\n};\r\n\r\nThinEngine.prototype.unBindMultiColorAttachmentFramebuffer = function (\r\n    rtWrapper: WebGLRenderTargetWrapper,\r\n    disableGenerateMipMaps: boolean = false,\r\n    onBeforeUnbind?: () => void\r\n): void {\r\n    this._currentRenderTarget = null;\r\n\r\n    if (!rtWrapper.disableAutomaticMSAAResolve) {\r\n        this.resolveMultiFramebuffer(rtWrapper);\r\n    }\r\n\r\n    if (!disableGenerateMipMaps) {\r\n        this.generateMipMapsMultiFramebuffer(rtWrapper);\r\n    }\r\n\r\n    if (onBeforeUnbind) {\r\n        if (rtWrapper._MSAAFramebuffer) {\r\n            // Bind the correct framebuffer\r\n            this._bindUnboundFramebuffer(rtWrapper._framebuffer);\r\n        }\r\n        onBeforeUnbind();\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(null);\r\n};\r\n\r\nThinEngine.prototype.createMultipleRenderTarget = function (size: TextureSize, options: IMultiRenderTargetOptions, initializeBuffers: boolean = true): RenderTargetWrapper {\r\n    let generateMipMaps = false;\r\n    let generateDepthBuffer = true;\r\n    let generateStencilBuffer = false;\r\n    let generateDepthTexture = false;\r\n    let depthTextureFormat: number | undefined = undefined;\r\n    let textureCount = 1;\r\n    let samples = 1;\r\n\r\n    const defaultType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n    const defaultSamplingMode = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE;\r\n    const defaultUseSRGBBuffer = false;\r\n    const defaultFormat = Constants.TEXTUREFORMAT_RGBA;\r\n    const defaultTarget = Constants.TEXTURE_2D;\r\n\r\n    let types: number[] = [];\r\n    let samplingModes: number[] = [];\r\n    let useSRGBBuffers: boolean[] = [];\r\n    let formats: number[] = [];\r\n    let targets: number[] = [];\r\n    let faceIndex: number[] = [];\r\n    let layerIndex: number[] = [];\r\n    let layers: number[] = [];\r\n    let labels: string[] = [];\r\n    let dontCreateTextures = false;\r\n\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(true, false, size) as WebGLRenderTargetWrapper;\r\n\r\n    if (options !== undefined) {\r\n        generateMipMaps = options.generateMipMaps === undefined ? false : options.generateMipMaps;\r\n        generateDepthBuffer = options.generateDepthBuffer === undefined ? true : options.generateDepthBuffer;\r\n        generateStencilBuffer = options.generateStencilBuffer === undefined ? false : options.generateStencilBuffer;\r\n        generateDepthTexture = options.generateDepthTexture === undefined ? false : options.generateDepthTexture;\r\n        textureCount = options.textureCount ?? 1;\r\n        samples = options.samples ?? samples;\r\n        types = options.types || types;\r\n        samplingModes = options.samplingModes || samplingModes;\r\n        useSRGBBuffers = options.useSRGBBuffers || useSRGBBuffers;\r\n        formats = options.formats || formats;\r\n        targets = options.targetTypes || targets;\r\n        faceIndex = options.faceIndex || faceIndex;\r\n        layerIndex = options.layerIndex || layerIndex;\r\n        layers = options.layerCounts || layers;\r\n        labels = options.labels || labels;\r\n        dontCreateTextures = options.dontCreateTextures ?? false;\r\n\r\n        if (\r\n            this.webGLVersion > 1 &&\r\n            (options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 ||\r\n                options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8 ||\r\n                options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24 ||\r\n                options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32_FLOAT ||\r\n                options.depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8)\r\n        ) {\r\n            depthTextureFormat = options.depthTextureFormat;\r\n        }\r\n    }\r\n\r\n    if (depthTextureFormat === undefined) {\r\n        depthTextureFormat = generateStencilBuffer ? Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 : Constants.TEXTUREFORMAT_DEPTH32_FLOAT;\r\n    }\r\n\r\n    const gl = this._gl;\r\n    // Create the framebuffer\r\n    const currentFramebuffer = this._currentFramebuffer;\r\n    const framebuffer = gl.createFramebuffer();\r\n    this._bindUnboundFramebuffer(framebuffer);\r\n\r\n    const width = (<{ width: number; height: number }>size).width ?? <number>size;\r\n    const height = (<{ width: number; height: number }>size).height ?? <number>size;\r\n\r\n    const textures: InternalTexture[] = [];\r\n    const attachments: number[] = [];\r\n\r\n    const useStencilTexture =\r\n        this.webGLVersion > 1 &&\r\n        (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 ||\r\n            depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8 ||\r\n            depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8);\r\n\r\n    rtWrapper.label = options?.label ?? \"MultiRenderTargetWrapper\";\r\n    rtWrapper._framebuffer = framebuffer;\r\n    rtWrapper._generateDepthBuffer = generateDepthTexture || generateDepthBuffer;\r\n    rtWrapper._generateStencilBuffer = generateDepthTexture ? useStencilTexture : generateStencilBuffer;\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(rtWrapper._generateStencilBuffer, rtWrapper._generateDepthBuffer, width, height, 1, depthTextureFormat);\r\n    rtWrapper._attachments = attachments;\r\n\r\n    for (let i = 0; i < textureCount; i++) {\r\n        let samplingMode = samplingModes[i] || defaultSamplingMode;\r\n        let type = types[i] || defaultType;\r\n        let useSRGBBuffer = useSRGBBuffers[i] || defaultUseSRGBBuffer;\r\n        const format = formats[i] || defaultFormat;\r\n\r\n        const target = targets[i] || defaultTarget;\r\n        const layerCount = layers[i] ?? 1;\r\n\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n            // if floating point linear (gl.FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        } else if (type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n            // if floating point linear (HALF_FLOAT) then force to NEAREST_SAMPLINGMODE\r\n            samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        }\r\n\r\n        const filters = this._getSamplingParameters(samplingMode, generateMipMaps);\r\n        if (type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n            type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n            Logger.Warn(\"Float textures are not supported. Render target forced to TEXTURETYPE_UNSIGNED_BYTE type\");\r\n        }\r\n\r\n        useSRGBBuffer = useSRGBBuffer && this._caps.supportSRGBBuffers && (this.webGLVersion > 1 || this.isWebGPU);\r\n\r\n        const isWebGL2 = this.webGLVersion > 1;\r\n        const attachment = (<any>gl)[isWebGL2 ? \"COLOR_ATTACHMENT\" + i : \"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n\r\n        attachments.push(attachment);\r\n\r\n        if (target === -1 || dontCreateTextures) {\r\n            continue;\r\n        }\r\n\r\n        const texture = new InternalTexture(this, InternalTextureSource.MultiRenderTarget);\r\n        textures[i] = texture;\r\n\r\n        gl.activeTexture((<any>gl)[\"TEXTURE\" + i]);\r\n        gl.bindTexture(target, texture._hardwareTexture!.underlyingResource);\r\n\r\n        gl.texParameteri(target, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n        gl.texParameteri(target, gl.TEXTURE_MIN_FILTER, filters.min);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n        gl.texParameteri(target, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n        const internalSizedFormat = this._getRGBABufferInternalSizedFormat(type, format, useSRGBBuffer);\r\n        const internalFormat = this._getInternalFormat(format);\r\n        const webGLTextureType = this._getWebGLTextureType(type);\r\n\r\n        if (isWebGL2 && (target === Constants.TEXTURE_2D_ARRAY || target === Constants.TEXTURE_3D)) {\r\n            if (target === Constants.TEXTURE_2D_ARRAY) {\r\n                texture.is2DArray = true;\r\n            } else {\r\n                texture.is3D = true;\r\n            }\r\n\r\n            texture.baseDepth = texture.depth = layerCount;\r\n\r\n            gl.texImage3D(target, 0, internalSizedFormat, width, height, layerCount, 0, internalFormat, webGLTextureType, null);\r\n        } else if (target === Constants.TEXTURE_CUBE_MAP) {\r\n            // We have to generate all faces to complete the framebuffer\r\n            for (let i = 0; i < 6; i++) {\r\n                gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + i, 0, internalSizedFormat, width, height, 0, internalFormat, webGLTextureType, null);\r\n            }\r\n            texture.isCube = true;\r\n        } else {\r\n            gl.texImage2D(gl.TEXTURE_2D, 0, internalSizedFormat, width, height, 0, internalFormat, webGLTextureType, null);\r\n        }\r\n\r\n        if (generateMipMaps) {\r\n            gl.generateMipmap(target);\r\n        }\r\n\r\n        // Unbind\r\n        this._bindTextureDirectly(target, null);\r\n\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.isReady = true;\r\n        texture.samples = 1;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        texture.type = type;\r\n        texture._useSRGBBuffer = useSRGBBuffer;\r\n        texture.format = format;\r\n        texture.label = labels[i] ?? rtWrapper.label + \"-Texture\" + i;\r\n\r\n        this._internalTexturesCache.push(texture);\r\n    }\r\n\r\n    if (generateDepthTexture && this._caps.depthTextureExtension && !dontCreateTextures) {\r\n        // Depth texture\r\n        const depthTexture = new InternalTexture(this, InternalTextureSource.Depth);\r\n\r\n        let depthTextureType = Constants.TEXTURETYPE_UNSIGNED_SHORT;\r\n        let glDepthTextureInternalFormat: GLenum = gl.DEPTH_COMPONENT16;\r\n        let glDepthTextureFormat: GLenum = gl.DEPTH_COMPONENT;\r\n        let glDepthTextureType: GLenum = gl.UNSIGNED_SHORT;\r\n        let glDepthTextureAttachment: GLenum = gl.DEPTH_ATTACHMENT;\r\n        if (this.webGLVersion < 2) {\r\n            glDepthTextureInternalFormat = gl.DEPTH_COMPONENT;\r\n        } else {\r\n            if (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32_FLOAT) {\r\n                depthTextureType = Constants.TEXTURETYPE_FLOAT;\r\n                glDepthTextureType = gl.FLOAT;\r\n                glDepthTextureInternalFormat = gl.DEPTH_COMPONENT32F;\r\n            } else if (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8) {\r\n                depthTextureType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n                glDepthTextureType = gl.FLOAT_32_UNSIGNED_INT_24_8_REV;\r\n                glDepthTextureInternalFormat = gl.DEPTH32F_STENCIL8;\r\n                glDepthTextureFormat = gl.DEPTH_STENCIL;\r\n                glDepthTextureAttachment = gl.DEPTH_STENCIL_ATTACHMENT;\r\n            } else if (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24) {\r\n                depthTextureType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n                glDepthTextureType = gl.UNSIGNED_INT;\r\n                glDepthTextureInternalFormat = gl.DEPTH_COMPONENT24;\r\n                glDepthTextureAttachment = gl.DEPTH_ATTACHMENT;\r\n            } else if (depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24_STENCIL8 || depthTextureFormat === Constants.TEXTUREFORMAT_DEPTH24UNORM_STENCIL8) {\r\n                depthTextureType = Constants.TEXTURETYPE_UNSIGNED_INT_24_8;\r\n                glDepthTextureType = gl.UNSIGNED_INT_24_8;\r\n                glDepthTextureInternalFormat = gl.DEPTH24_STENCIL8;\r\n                glDepthTextureFormat = gl.DEPTH_STENCIL;\r\n                glDepthTextureAttachment = gl.DEPTH_STENCIL_ATTACHMENT;\r\n            }\r\n        }\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_2D, depthTexture, true);\r\n\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n        gl.texImage2D(gl.TEXTURE_2D, 0, glDepthTextureInternalFormat, width, height, 0, glDepthTextureFormat, glDepthTextureType, null);\r\n\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, glDepthTextureAttachment, gl.TEXTURE_2D, depthTexture._hardwareTexture!.underlyingResource, 0);\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_2D, null);\r\n\r\n        rtWrapper._depthStencilTexture = depthTexture;\r\n        rtWrapper._depthStencilTextureWithStencil = useStencilTexture;\r\n\r\n        depthTexture.baseWidth = width;\r\n        depthTexture.baseHeight = height;\r\n        depthTexture.width = width;\r\n        depthTexture.height = height;\r\n        depthTexture.isReady = true;\r\n        depthTexture.samples = 1;\r\n        depthTexture.generateMipMaps = generateMipMaps;\r\n        depthTexture.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        depthTexture.format = depthTextureFormat;\r\n        depthTexture.type = depthTextureType;\r\n        depthTexture.label = rtWrapper.label + \"-DepthStencil\";\r\n\r\n        textures[textureCount] = depthTexture;\r\n        this._internalTexturesCache.push(depthTexture);\r\n    }\r\n    rtWrapper.setTextures(textures);\r\n    if (initializeBuffers) {\r\n        gl.drawBuffers(attachments);\r\n    }\r\n\r\n    this._bindUnboundFramebuffer(currentFramebuffer);\r\n\r\n    rtWrapper.setLayerAndFaceIndices(layerIndex, faceIndex);\r\n\r\n    this.resetTextureCache();\r\n\r\n    if (!dontCreateTextures) {\r\n        this.updateMultipleRenderTargetTextureSampleCount(rtWrapper, samples, initializeBuffers);\r\n    } else if (samples > 1) {\r\n        const framebuffer = gl.createFramebuffer();\r\n\r\n        if (!framebuffer) {\r\n            throw new Error(\"Unable to create multi sampled framebuffer\");\r\n        }\r\n\r\n        rtWrapper._samples = samples;\r\n        rtWrapper._MSAAFramebuffer = framebuffer;\r\n\r\n        if (textureCount > 0 && initializeBuffers) {\r\n            this._bindUnboundFramebuffer(framebuffer);\r\n            gl.drawBuffers(attachments);\r\n            this._bindUnboundFramebuffer(currentFramebuffer);\r\n        }\r\n    }\r\n\r\n    return rtWrapper;\r\n};\r\n\r\nThinEngine.prototype.updateMultipleRenderTargetTextureSampleCount = function (\r\n    rtWrapper: Nullable<WebGLRenderTargetWrapper>,\r\n    samples: number,\r\n    initializeBuffers: boolean = true\r\n): number {\r\n    if (this.webGLVersion < 2 || !rtWrapper) {\r\n        return 1;\r\n    }\r\n\r\n    if (rtWrapper.samples === samples) {\r\n        return samples;\r\n    }\r\n\r\n    const gl = this._gl;\r\n\r\n    samples = Math.min(samples, this.getCaps().maxMSAASamples);\r\n\r\n    // Dispose previous render buffers\r\n    if (rtWrapper._depthStencilBuffer) {\r\n        gl.deleteRenderbuffer(rtWrapper._depthStencilBuffer);\r\n        rtWrapper._depthStencilBuffer = null;\r\n    }\r\n\r\n    if (rtWrapper._MSAAFramebuffer) {\r\n        gl.deleteFramebuffer(rtWrapper._MSAAFramebuffer);\r\n        rtWrapper._MSAAFramebuffer = null;\r\n    }\r\n\r\n    const count = rtWrapper._attachments!.length; // We do it this way instead of rtWrapper.textures.length to avoid taking into account the depth/stencil texture, in case it has been created\r\n\r\n    for (let i = 0; i < count; i++) {\r\n        const texture = rtWrapper.textures![i];\r\n        const hardwareTexture = texture._hardwareTexture as Nullable<WebGLHardwareTexture>;\r\n\r\n        hardwareTexture?.releaseMSAARenderBuffers();\r\n    }\r\n\r\n    if (samples > 1 && typeof gl.renderbufferStorageMultisample === \"function\") {\r\n        const framebuffer = gl.createFramebuffer();\r\n\r\n        if (!framebuffer) {\r\n            throw new Error(\"Unable to create multi sampled framebuffer\");\r\n        }\r\n\r\n        rtWrapper._MSAAFramebuffer = framebuffer;\r\n        this._bindUnboundFramebuffer(framebuffer);\r\n\r\n        const attachments = [];\r\n\r\n        for (let i = 0; i < count; i++) {\r\n            const texture = rtWrapper.textures![i];\r\n            const hardwareTexture = texture._hardwareTexture as WebGLHardwareTexture;\r\n            const attachment = (<any>gl)[this.webGLVersion > 1 ? \"COLOR_ATTACHMENT\" + i : \"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n\r\n            const colorRenderbuffer = this._createRenderBuffer(\r\n                texture.width,\r\n                texture.height,\r\n                samples,\r\n                -1 /* not used */,\r\n                this._getRGBABufferInternalSizedFormat(texture.type, texture.format, texture._useSRGBBuffer),\r\n                attachment\r\n            );\r\n\r\n            if (!colorRenderbuffer) {\r\n                throw new Error(\"Unable to create multi sampled framebuffer\");\r\n            }\r\n\r\n            hardwareTexture.addMSAARenderBuffer(colorRenderbuffer);\r\n            texture.samples = samples;\r\n\r\n            attachments.push(attachment);\r\n        }\r\n        if (initializeBuffers) {\r\n            gl.drawBuffers(attachments);\r\n        }\r\n    } else {\r\n        this._bindUnboundFramebuffer(rtWrapper._framebuffer);\r\n    }\r\n\r\n    const depthFormat = rtWrapper._depthStencilTexture ? rtWrapper._depthStencilTexture.format : undefined;\r\n\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(\r\n        rtWrapper._generateStencilBuffer,\r\n        rtWrapper._generateDepthBuffer,\r\n        rtWrapper.width,\r\n        rtWrapper.height,\r\n        samples,\r\n        depthFormat\r\n    );\r\n\r\n    this._bindUnboundFramebuffer(null);\r\n\r\n    rtWrapper._samples = samples;\r\n\r\n    return samples;\r\n};\r\n\r\nThinEngine.prototype.generateMipMapsMultiFramebuffer = function (texture: RenderTargetWrapper): void {\r\n    const rtWrapper = texture as WebGLRenderTargetWrapper;\r\n    const gl = this._gl;\r\n\r\n    if (!rtWrapper.isMulti) {\r\n        return;\r\n    }\r\n\r\n    for (let i = 0; i < rtWrapper._attachments!.length; i++) {\r\n        const texture = rtWrapper.textures![i];\r\n        if (texture?.generateMipMaps && !texture?.isCube && !texture?.is3D) {\r\n            this._bindTextureDirectly(gl.TEXTURE_2D, texture, true);\r\n            gl.generateMipmap(gl.TEXTURE_2D);\r\n            this._bindTextureDirectly(gl.TEXTURE_2D, null);\r\n        }\r\n    }\r\n};\r\n\r\nThinEngine.prototype.resolveMultiFramebuffer = function (texture: RenderTargetWrapper): void {\r\n    const rtWrapper = texture as WebGLRenderTargetWrapper;\r\n    const gl = this._gl;\r\n\r\n    if (!rtWrapper._MSAAFramebuffer || !rtWrapper.isMulti) {\r\n        return;\r\n    }\r\n\r\n    let bufferBits = rtWrapper.resolveMSAAColors ? gl.COLOR_BUFFER_BIT : 0;\r\n    bufferBits |= rtWrapper._generateDepthBuffer && rtWrapper.resolveMSAADepth ? gl.DEPTH_BUFFER_BIT : 0;\r\n    bufferBits |= rtWrapper._generateStencilBuffer && rtWrapper.resolveMSAAStencil ? gl.STENCIL_BUFFER_BIT : 0;\r\n\r\n    const attachments = rtWrapper._attachments!;\r\n    const count = attachments.length;\r\n\r\n    gl.bindFramebuffer(gl.READ_FRAMEBUFFER, rtWrapper._MSAAFramebuffer);\r\n    gl.bindFramebuffer(gl.DRAW_FRAMEBUFFER, rtWrapper._framebuffer);\r\n\r\n    for (let i = 0; i < count; i++) {\r\n        const texture = rtWrapper.textures![i];\r\n\r\n        for (let j = 0; j < count; j++) {\r\n            attachments[j] = gl.NONE;\r\n        }\r\n\r\n        attachments[i] = (<any>gl)[this.webGLVersion > 1 ? \"COLOR_ATTACHMENT\" + i : \"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n        gl.readBuffer(attachments[i]);\r\n        gl.drawBuffers(attachments);\r\n        gl.blitFramebuffer(0, 0, texture.width, texture.height, 0, 0, texture.width, texture.height, bufferBits, gl.NEAREST);\r\n    }\r\n\r\n    for (let i = 0; i < count; i++) {\r\n        attachments[i] = (<any>gl)[this.webGLVersion > 1 ? \"COLOR_ATTACHMENT\" + i : \"COLOR_ATTACHMENT\" + i + \"_WEBGL\"];\r\n    }\r\n\r\n    gl.drawBuffers(attachments);\r\n    gl.bindFramebuffer(this._gl.FRAMEBUFFER, rtWrapper._MSAAFramebuffer);\r\n};\r\n"]}