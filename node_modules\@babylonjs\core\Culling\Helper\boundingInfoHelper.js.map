{"version": 3, "file": "boundingInfoHelper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Culling/Helper/boundingInfoHelper.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,MAAM,EAAE,6BAAyB;AAE1C;;;;;;;GAOG;AACH,MAAM,OAAO,kBAAkB;IAI3B;;;OAGG;IACH,YAAmB,MAAsB;QACrC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAEO,KAAK,CAAC,wBAAwB;QAClC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,qBAAqB,EAAE,CAAC;gBAC/C,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;gBAC7D,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1E,CAAC;iBAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,yBAAyB,EAAE,CAAC;gBAC1D,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;gBACjE,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM,CAAC,+BAA+B,CAAC,IAAI,CAAC,OAAqB,CAAC,CAAC;YAC5F,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;YAC3F,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,YAAY,CAAC,MAAqC;QAC3D,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACtC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,oBAAoB,CAAC,MAAqC;QACnE,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACtC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAC9D,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;IACrC,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,sBAAsB;QAC/B,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACtC,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,4BAA4B,EAAE,CAAC;IAC/D,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["import type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport type { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport type { IBoundingInfoHelperPlatform } from \"./IBoundingInfoHelperPlatform\";\r\nimport type { ThinEngine } from \"core/Engines\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Utility class to help with bounding info management\r\n * Warning: using the BoundingInfoHelper class may be slower than executing calculations on the CPU!\r\n * This will happen if there are a lot of meshes / few vertices (like with the BrainStem model)\r\n * The BoundingInfoHelper will perform better if there are few meshes / a lot of vertices\r\n *  https://playground.babylonjs.com/#QPOERJ#9 : WebGL\r\n *  https://playground.babylonjs.com/#QPOERJ#10 : WebGPU\r\n */\r\nexport class BoundingInfoHelper {\r\n    private _platform: IBoundingInfoHelperPlatform;\r\n    private _engine: AbstractEngine;\r\n\r\n    /**\r\n     * Creates a new BoundingInfoHelper\r\n     * @param engine defines the engine to use\r\n     */\r\n    public constructor(engine: AbstractEngine) {\r\n        this._engine = engine;\r\n    }\r\n\r\n    private async _initializePlatformAsync() {\r\n        if (!this._platform) {\r\n            if (this._engine.getCaps().supportComputeShaders) {\r\n                const module = await import(\"./computeShaderBoundingHelper\");\r\n                this._platform = new module.ComputeShaderBoundingHelper(this._engine);\r\n            } else if (this._engine.getCaps().supportTransformFeedbacks) {\r\n                const module = await import(\"./transformFeedbackBoundingHelper\");\r\n                this._platform = new module.TransformFeedbackBoundingHelper(this._engine as ThinEngine);\r\n            } else {\r\n                throw new Error(\"Your engine does not support Compute Shaders or Transform Feedbacks\");\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Compute the bounding info of a mesh / array of meshes using shaders\r\n     * @param target defines the mesh(es) to update\r\n     * @returns a promise that resolves when the bounding info is/are computed\r\n     */\r\n    public async computeAsync(target: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        await this._initializePlatformAsync();\r\n        return await this._platform.processAsync(target);\r\n    }\r\n\r\n    /**\r\n     * Register a mesh / array of meshes to be processed per batch\r\n     * This method must be called before calling batchProcess (which can be called several times) and batchFetchResultsAsync\r\n     * @param target defines the mesh(es) to be processed per batch\r\n     * @returns a promise that resolves when the initialization is done\r\n     */\r\n    public async batchInitializeAsync(target: AbstractMesh | AbstractMesh[]): Promise<void> {\r\n        await this._initializePlatformAsync();\r\n        return await this._platform.registerMeshListAsync(target);\r\n    }\r\n\r\n    /**\r\n     * Processes meshes registered with batchRegisterAsync\r\n     * If called multiple times, the second, third, etc calls will perform a union of the bounding boxes calculated in the previous calls\r\n     */\r\n    public batchProcess(): void {\r\n        if (this._platform === null) {\r\n            Logger.Warn(\"Helper is not initialized. Skipping batch.\");\r\n            return;\r\n        }\r\n        this._platform.processMeshList();\r\n    }\r\n\r\n    /**\r\n     * Update the bounding info of the meshes registered with batchRegisterAsync, after batchProcess has been called once or several times\r\n     * @returns a promise that resolves when the bounding info is/are computed\r\n     */\r\n    public async batchFetchResultsAsync(): Promise<void> {\r\n        await this._initializePlatformAsync();\r\n        return await this._platform.fetchResultsForMeshListAsync();\r\n    }\r\n\r\n    /**\r\n     * Dispose and release associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this._platform) {\r\n            this._platform.dispose();\r\n        }\r\n    }\r\n}\r\n"]}