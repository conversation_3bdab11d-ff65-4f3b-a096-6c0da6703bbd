{"version": 3, "file": "boundingInfo.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Culling/boundingInfo.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAKlD,MAAM,QAAQ,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACpC,MAAM,QAAQ,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AACpC,MAAM,iBAAiB,GAAG,CAAC,IAA4B,EAAE,GAA+B,EAAE,MAAoC,EAAE,EAAE;IAC9H,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAE7C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7E,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAC7E,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IAE7E,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACvB,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACnB,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACvB,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,IAA4B,EAAE,IAAgC,EAAE,IAAgC,EAAW,EAAE;IAC9H,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACxC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACxC,OAAO,CAAC,CAAC,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzE,CAAC,CAAC;AAsBF;;GAEG;AACH,MAAM,OAAO,YAAY;IAcrB;;;;;OAKG;IACH,YAAY,OAA+B,EAAE,OAA+B,EAAE,WAAmC;QAVzG,cAAS,GAAG,KAAK,CAAC;QAWtB,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAClE,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IAC5E,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,GAA2B,EAAE,GAA2B,EAAE,WAAmC;QAC5G,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,QAAQ,CAAC,KAAc;QAC9B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,UAAU;IACV;;;OAGG;IACI,MAAM,CAAC,KAA4B;QACtC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAED;;;;;OAKG;IACI,QAAQ,CAAC,MAA8B,EAAE,MAA8B;QAC1E,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACrF,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAEhF,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC;QAErF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,KAAc;QAC7B,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC;QAEtE,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,uBAAuB,CAAC,aAA2B;QACtD,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAEpD,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEhC,OAAO,CAAC,yBAAyB,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACnF,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEpB,OAAO,CAAC,yBAAyB,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACnF,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEpB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,MAAc;QACvB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;;OAUG;IACI,WAAW,CAAC,aAA0C,EAAE,WAAmB,SAAS,CAAC,+BAA+B;QACvH,MAAM,aAAa,GACf,QAAQ,KAAK,SAAS,CAAC,2CAA2C,IAAI,QAAQ,KAAK,SAAS,CAAC,6DAA6D,CAAC;QAC/J,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,eAAe,GACjB,QAAQ,KAAK,SAAS,CAAC,0CAA0C,IAAI,QAAQ,KAAK,SAAS,CAAC,6DAA6D,CAAC;QAC9J,IAAI,eAAe,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,IAAI,GAAG,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3G,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,aAA0C;QACnE,OAAO,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;IACjE,CAAC;IACD;;OAEG;IACI,eAAe,CAAC,QAAkB;QACrC,OAAO,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;IACpK,CAAC;IAED;;;;;OAKG;IACI,eAAe,CAAC,KAA6B;QAChD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YACnC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,UAAU,CAAC,YAAyC,EAAE,OAAgB;QACzE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC;YAC/E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC;YACtE,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;QAC9B,MAAM,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;YAClF,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;;AAjRuB,wBAAW,GAAG,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,AAA9B,CAA+B", "sourcesContent": ["import type { DeepImmutable } from \"../types\";\r\nimport { <PERSON><PERSON><PERSON><PERSON>y } from \"../Misc/arrayTools\";\r\nimport type { Matrix } from \"../Maths/math.vector\";\r\nimport { TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { BoundingBox } from \"./boundingBox\";\r\nimport { BoundingSphere } from \"./boundingSphere\";\r\nimport type { Plane } from \"../Maths/math.plane\";\r\n\r\nimport type { Collider } from \"../Collisions/collider\";\r\n\r\nconst _Result0 = { min: 0, max: 0 };\r\nconst _Result1 = { min: 0, max: 0 };\r\nconst ComputeBoxExtents = (axis: DeepImmutable<Vector3>, box: DeepImmutable<BoundingBox>, result: { min: number; max: number }) => {\r\n    const p = Vector3.Dot(box.centerWorld, axis);\r\n\r\n    const r0 = Math.abs(Vector3.Dot(box.directions[0], axis)) * box.extendSize.x;\r\n    const r1 = Math.abs(Vector3.Dot(box.directions[1], axis)) * box.extendSize.y;\r\n    const r2 = Math.abs(Vector3.Dot(box.directions[2], axis)) * box.extendSize.z;\r\n\r\n    const r = r0 + r1 + r2;\r\n    result.min = p - r;\r\n    result.max = p + r;\r\n};\r\n\r\nconst AxisOverlap = (axis: DeepImmutable<Vector3>, box0: DeepImmutable<BoundingBox>, box1: DeepImmutable<BoundingBox>): boolean => {\r\n    ComputeBoxExtents(axis, box0, _Result0);\r\n    ComputeBoxExtents(axis, box1, _Result1);\r\n    return !(_Result0.min > _Result1.max || _Result1.min > _Result0.max);\r\n};\r\n\r\n/**\r\n * Interface for cullable objects\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/using/materials_introduction#back-face-culling\r\n */\r\nexport interface ICullable {\r\n    /**\r\n     * Checks if the object or part of the object is in the frustum\r\n     * @param frustumPlanes Camera near/planes\r\n     * @returns true if the object is in frustum otherwise false\r\n     */\r\n    isInFrustum(frustumPlanes: Plane[]): boolean;\r\n    /**\r\n     * Checks if a cullable object (mesh...) is in the camera frustum\r\n     * Unlike isInFrustum this checks the full bounding box\r\n     * @param frustumPlanes Camera near/planes\r\n     * @returns true if the object is in frustum otherwise false\r\n     */\r\n    isCompletelyInFrustum(frustumPlanes: Plane[]): boolean;\r\n}\r\n\r\n/**\r\n * Info for a bounding data of a mesh\r\n */\r\nexport class BoundingInfo implements ICullable {\r\n    /**\r\n     * Bounding box for the mesh\r\n     */\r\n    public readonly boundingBox: BoundingBox;\r\n    /**\r\n     * Bounding sphere for the mesh\r\n     */\r\n    public readonly boundingSphere: BoundingSphere;\r\n\r\n    private _isLocked = false;\r\n\r\n    private static readonly _TmpVector3 = BuildArray(2, Vector3.Zero);\r\n\r\n    /**\r\n     * Constructs bounding info\r\n     * @param minimum min vector of the bounding box/sphere\r\n     * @param maximum max vector of the bounding box/sphere\r\n     * @param worldMatrix defines the new world matrix\r\n     */\r\n    constructor(minimum: DeepImmutable<Vector3>, maximum: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>) {\r\n        this.boundingBox = new BoundingBox(minimum, maximum, worldMatrix);\r\n        this.boundingSphere = new BoundingSphere(minimum, maximum, worldMatrix);\r\n    }\r\n\r\n    /**\r\n     * Recreates the entire bounding info from scratch as if we call the constructor in place\r\n     * @param min defines the new minimum vector (in local space)\r\n     * @param max defines the new maximum vector (in local space)\r\n     * @param worldMatrix defines the new world matrix\r\n     */\r\n    public reConstruct(min: DeepImmutable<Vector3>, max: DeepImmutable<Vector3>, worldMatrix?: DeepImmutable<Matrix>) {\r\n        this.boundingBox.reConstruct(min, max, worldMatrix);\r\n        this.boundingSphere.reConstruct(min, max, worldMatrix);\r\n    }\r\n\r\n    /**\r\n     * min vector of the bounding box/sphere\r\n     */\r\n    public get minimum(): Vector3 {\r\n        return this.boundingBox.minimum;\r\n    }\r\n\r\n    /**\r\n     * max vector of the bounding box/sphere\r\n     */\r\n    public get maximum(): Vector3 {\r\n        return this.boundingBox.maximum;\r\n    }\r\n\r\n    /**\r\n     * If the info is locked and won't be updated to avoid perf overhead\r\n     */\r\n    public get isLocked(): boolean {\r\n        return this._isLocked;\r\n    }\r\n\r\n    public set isLocked(value: boolean) {\r\n        this._isLocked = value;\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Updates the bounding sphere and box\r\n     * @param world world matrix to be used to update\r\n     */\r\n    public update(world: DeepImmutable<Matrix>) {\r\n        if (this._isLocked) {\r\n            return;\r\n        }\r\n        this.boundingBox._update(world);\r\n        this.boundingSphere._update(world);\r\n    }\r\n\r\n    /**\r\n     * Recreate the bounding info to be centered around a specific point given a specific extend.\r\n     * @param center New center of the bounding info\r\n     * @param extend New extend of the bounding info\r\n     * @returns the current bounding info\r\n     */\r\n    public centerOn(center: DeepImmutable<Vector3>, extend: DeepImmutable<Vector3>): BoundingInfo {\r\n        const minimum = BoundingInfo._TmpVector3[0].copyFrom(center).subtractInPlace(extend);\r\n        const maximum = BoundingInfo._TmpVector3[1].copyFrom(center).addInPlace(extend);\r\n\r\n        this.boundingBox.reConstruct(minimum, maximum, this.boundingBox.getWorldMatrix());\r\n        this.boundingSphere.reConstruct(minimum, maximum, this.boundingBox.getWorldMatrix());\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Grows the bounding info to include the given point.\r\n     * @param point The point that will be included in the current bounding info (in local space)\r\n     * @returns the current bounding info\r\n     */\r\n    public encapsulate(point: Vector3): BoundingInfo {\r\n        const minimum = Vector3.Minimize(this.minimum, point);\r\n        const maximum = Vector3.Maximize(this.maximum, point);\r\n        this.reConstruct(minimum, maximum, this.boundingBox.getWorldMatrix());\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Grows the bounding info to encapsulate the given bounding info.\r\n     * @param toEncapsulate The bounding info that will be encapsulated in the current bounding info\r\n     * @returns the current bounding info\r\n     */\r\n    public encapsulateBoundingInfo(toEncapsulate: BoundingInfo): BoundingInfo {\r\n        const invw = TmpVectors.Matrix[0];\r\n        this.boundingBox.getWorldMatrix().invertToRef(invw);\r\n\r\n        const v = TmpVectors.Vector3[0];\r\n\r\n        Vector3.TransformCoordinatesToRef(toEncapsulate.boundingBox.minimumWorld, invw, v);\r\n        this.encapsulate(v);\r\n\r\n        Vector3.TransformCoordinatesToRef(toEncapsulate.boundingBox.maximumWorld, invw, v);\r\n        this.encapsulate(v);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Scale the current bounding info by applying a scale factor\r\n     * @param factor defines the scale factor to apply\r\n     * @returns the current bounding info\r\n     */\r\n    public scale(factor: number): BoundingInfo {\r\n        this.boundingBox.scale(factor);\r\n        this.boundingSphere.scale(factor);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Returns `true` if the bounding info is within the frustum defined by the passed array of planes.\r\n     * @param frustumPlanes defines the frustum to test\r\n     * @param strategy defines the strategy to use for the culling (default is BABYLON.AbstractMesh.CULLINGSTRATEGY_STANDARD)\r\n     * The different strategies available are:\r\n     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_STANDARD most accurate but slower @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_STANDARD\r\n     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY faster but less accurate @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY\r\n     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_OPTIMISTIC_INCLUSION can be faster if always visible @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_OPTIMISTIC_INCLUSION\r\n     * * BABYLON.AbstractMesh.CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY can be faster if always visible @see https://doc.babylonjs.com/typedoc/classes/BABYLON.AbstractMesh#CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY\r\n     * @returns true if the bounding info is in the frustum planes\r\n     */\r\n    public isInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>, strategy: number = Constants.MESHES_CULLINGSTRATEGY_STANDARD): boolean {\r\n        const inclusionTest =\r\n            strategy === Constants.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION || strategy === Constants.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY;\r\n        if (inclusionTest) {\r\n            if (this.boundingSphere.isCenterInFrustum(frustumPlanes)) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        if (!this.boundingSphere.isInFrustum(frustumPlanes)) {\r\n            return false;\r\n        }\r\n\r\n        const bSphereOnlyTest =\r\n            strategy === Constants.MESHES_CULLINGSTRATEGY_BOUNDINGSPHERE_ONLY || strategy === Constants.MESHES_CULLINGSTRATEGY_OPTIMISTIC_INCLUSION_THEN_BSPHERE_ONLY;\r\n        if (bSphereOnlyTest) {\r\n            return true;\r\n        }\r\n\r\n        return this.boundingBox.isInFrustum(frustumPlanes);\r\n    }\r\n\r\n    /**\r\n     * Gets the world distance between the min and max points of the bounding box\r\n     */\r\n    public get diagonalLength(): number {\r\n        const boundingBox = this.boundingBox;\r\n        const diag = boundingBox.maximumWorld.subtractToRef(boundingBox.minimumWorld, BoundingInfo._TmpVector3[0]);\r\n        return diag.length();\r\n    }\r\n\r\n    /**\r\n     * Checks if a cullable object (mesh...) is in the camera frustum\r\n     * Unlike isInFrustum this checks the full bounding box\r\n     * @param frustumPlanes Camera near/planes\r\n     * @returns true if the object is in frustum otherwise false\r\n     */\r\n    public isCompletelyInFrustum(frustumPlanes: Array<DeepImmutable<Plane>>): boolean {\r\n        return this.boundingBox.isCompletelyInFrustum(frustumPlanes);\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    public _checkCollision(collider: Collider): boolean {\r\n        return collider._canDoCollision(this.boundingSphere.centerWorld, this.boundingSphere.radiusWorld, this.boundingBox.minimumWorld, this.boundingBox.maximumWorld);\r\n    }\r\n\r\n    /**\r\n     * Checks if a point is inside the bounding box and bounding sphere or the mesh\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/mesh_intersect\r\n     * @param point the point to check intersection with\r\n     * @returns if the point intersects\r\n     */\r\n    public intersectsPoint(point: DeepImmutable<Vector3>): boolean {\r\n        if (!this.boundingSphere.centerWorld) {\r\n            return false;\r\n        }\r\n\r\n        if (!this.boundingSphere.intersectsPoint(point)) {\r\n            return false;\r\n        }\r\n\r\n        if (!this.boundingBox.intersectsPoint(point)) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Checks if another bounding info intersects the bounding box and bounding sphere or the mesh\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/mesh/interactions/mesh_intersect\r\n     * @param boundingInfo the bounding info to check intersection with\r\n     * @param precise if the intersection should be done using OBB\r\n     * @returns if the bounding info intersects\r\n     */\r\n    public intersects(boundingInfo: DeepImmutable<BoundingInfo>, precise: boolean): boolean {\r\n        if (!BoundingSphere.Intersects(this.boundingSphere, boundingInfo.boundingSphere)) {\r\n            return false;\r\n        }\r\n\r\n        if (!BoundingBox.Intersects(this.boundingBox, boundingInfo.boundingBox)) {\r\n            return false;\r\n        }\r\n\r\n        if (!precise) {\r\n            return true;\r\n        }\r\n\r\n        const box0 = this.boundingBox;\r\n        const box1 = boundingInfo.boundingBox;\r\n\r\n        if (!AxisOverlap(box0.directions[0], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box0.directions[1], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box0.directions[2], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box1.directions[0], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box1.directions[1], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(box1.directions[2], box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[0], box1.directions[0]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[0], box1.directions[1]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[0], box1.directions[2]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[1], box1.directions[0]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[1], box1.directions[1]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[1], box1.directions[2]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[2], box1.directions[0]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[2], box1.directions[1]), box0, box1)) {\r\n            return false;\r\n        }\r\n        if (!AxisOverlap(Vector3.Cross(box0.directions[2], box1.directions[2]), box0, box1)) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n}\r\n"]}