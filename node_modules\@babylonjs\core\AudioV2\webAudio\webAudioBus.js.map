{"version": 3, "file": "webAudioBus.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/webAudio/webAudioBus.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AACrD,OAAO,EAAE,uBAAuB,EAAE,MAAM,qDAAqD,CAAC;AAE9F,OAAO,EAAE,YAAY,EAAE,MAAM,4CAA4C,CAAC;AAC1E,OAAO,EAAE,4BAA4B,EAAE,MAAM,wCAAwC,CAAC;AACtF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAInE,gBAAgB;AAChB,MAAM,OAAO,YAAa,SAAQ,QAAQ;IAWtC,gBAAgB;IAChB,YAAmB,IAAY,EAAE,MAAuB,EAAE,OAAkC;QACxF,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAZhB,aAAQ,GAA4B,IAAI,CAAC;QAChC,uBAAkB,GAAY,IAAI,CAAC;QACnC,0BAAqB,GAAW,CAAC,CAAC;QAC3C,YAAO,GAA2B,IAAI,CAAC;QAW3C,IAAI,OAAO,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACxD,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,UAAU,CAAC,OAAkC;QACtD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAExC,IAAI,uBAAuB,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAClC,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,IAAoB,OAAO;QACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;IACvC,CAAC;IAED,gBAAgB;IAChB,IAAoB,MAAM;QACtB,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEkB,QAAQ,CAAC,IAAqB;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,IAAI,CAAC,QAAQ,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9G,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;;AAEc,sBAAS,GAAG,KAAM,SAAQ,4BAA4B;IAGjE,IAAc,gBAAgB;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAChD,CAAC;IAED,IAAc,cAAc;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC;IAC9C,CAAC;CACJ,AAVuB,CAUtB", "sourcesContent": ["import type { Nullable } from \"core/types\";\nimport type { AbstractAudioNode } from \"../abstractAudio/abstractAudioNode\";\nimport type { IAudioBusOptions } from \"../abstractAudio/audioBus\";\nimport { AudioBus } from \"../abstractAudio/audioBus\";\nimport { _HasSpatialAudioOptions } from \"../abstractAudio/subProperties/abstractSpatialAudio\";\nimport type { _SpatialAudio } from \"../abstractAudio/subProperties/spatialAudio\";\nimport { _StereoAudio } from \"../abstractAudio/subProperties/stereoAudio\";\nimport { _WebAudioBusAndSoundSubGraph } from \"./subNodes/webAudioBusAndSoundSubGraph\";\nimport { _SpatialWebAudio } from \"./subProperties/spatialWebAudio\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioSuperNode } from \"./webAudioNode\";\n\n/** @internal */\nexport class _WebAudioBus extends AudioBus implements IWebAudioSuperNode {\n    private _spatial: Nullable<_SpatialAudio> = null;\n    private readonly _spatialAutoUpdate: boolean = true;\n    private readonly _spatialMinUpdateTime: number = 0;\n    private _stereo: Nullable<_StereoAudio> = null;\n\n    protected _subGraph: _WebAudioBusAndSoundSubGraph;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(name: string, engine: _WebAudioEngine, options: Partial<IAudioBusOptions>) {\n        super(name, engine);\n\n        if (typeof options.spatialAutoUpdate === \"boolean\") {\n            this._spatialAutoUpdate = options.spatialAutoUpdate;\n        }\n\n        if (typeof options.spatialMinUpdateTime === \"number\") {\n            this._spatialMinUpdateTime = options.spatialMinUpdateTime;\n        }\n\n        this._subGraph = new _WebAudioBus._SubGraph(this);\n    }\n\n    /** @internal */\n    public async _initAsync(options: Partial<IAudioBusOptions>): Promise<void> {\n        if (options.outBus) {\n            this.outBus = options.outBus;\n        } else {\n            await this.engine.isReadyPromise;\n            this.outBus = this.engine.defaultMainBus;\n        }\n\n        await this._subGraph.initAsync(options);\n\n        if (_HasSpatialAudioOptions(options)) {\n            this._initSpatialProperty();\n        }\n\n        this.engine._addNode(this);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._spatial = null;\n        this._stereo = null;\n\n        this.engine._removeNode(this);\n    }\n\n    /** @internal */\n    public get _inNode() {\n        return this._subGraph._inNode;\n    }\n\n    /** @internal */\n    public get _outNode() {\n        return this._subGraph._outNode;\n    }\n\n    /** @internal */\n    public override get spatial(): _SpatialAudio {\n        if (this._spatial) {\n            return this._spatial;\n        }\n        return this._initSpatialProperty();\n    }\n\n    /** @internal */\n    public override get stereo(): _StereoAudio {\n        return this._stereo ?? (this._stereo = new _StereoAudio(this._subGraph));\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioBus\";\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this._outNode?.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    private _initSpatialProperty(): _SpatialAudio {\n        if (!this._spatial) {\n            this._spatial = new _SpatialWebAudio(this._subGraph, this._spatialAutoUpdate, this._spatialMinUpdateTime);\n        }\n\n        return this._spatial;\n    }\n\n    private static _SubGraph = class extends _WebAudioBusAndSoundSubGraph {\n        protected override _owner: _WebAudioBus;\n\n        protected get _downstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._downstreamNodes ?? null;\n        }\n\n        protected get _upstreamNodes(): Nullable<Set<AbstractAudioNode>> {\n            return this._owner._upstreamNodes ?? null;\n        }\n    };\n}\n"]}