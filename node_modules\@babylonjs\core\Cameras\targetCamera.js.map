{"version": 3, "file": "targetCamera.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Cameras/targetCamera.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AAE7F,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACxF,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IACpD,OAAO,GAAG,EAAE,CAAC,IAAI,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC;AAEH,kDAAkD;AAClD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAChC,MAAM,aAAa,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;AAE5C;;;;GAIG;AACH,MAAM,OAAO,YAAa,SAAQ,MAAM;IAkFpC;;;;;;;;OAQG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,KAAa,EAAE,4BAA4B,GAAG,IAAI;QAC3F,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,4BAA4B,CAAC,CAAC;QAvF/D;;WAEG;QACI,oBAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C;;WAEG;QACI,mBAAc,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1C;;WAEG;QAEI,+BAA0B,GAAG,KAAK,CAAC;QAa1C;;WAEG;QAEI,UAAK,GAAG,GAAG,CAAC;QAEnB;;;WAGG;QACI,yBAAoB,GAAG,KAAK,CAAC;QAEpC;;;WAGG;QACI,mBAAc,GAAG,KAAK,CAAC;QAE9B;;WAEG;QACI,yBAAoB,GAAG,GAAG,CAAC;QAElC;;;WAGG;QAEI,iBAAY,GAAQ,IAAI,CAAC;QAEb,mBAAc,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACzC,0BAAqB,GAAG,CAAC,CAAC;QACjB,gBAAW,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAE/C,gBAAgB;QACA,2BAAsB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QACvD,gBAAgB;QACA,0BAAqB,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QAGnC,+BAA0B,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE5C,4BAAuB,GAAG,IAAI,OAAO,EAAE,CAAC;QACxC,sCAAiC,GAAG,IAAI,UAAU,EAAE,CAAC;QACrD,4BAAuB,GAAG,IAAI,OAAO,EAAE,CAAC;QACjD,qBAAgB,GAAG,KAAK,CAAC;QACzB,eAAU,GAAY,KAAK,CAAC;QAoV9B,qBAAgB,GAAG,CAAC,CAAC;QACrB,+BAA0B,GAAG,CAAC,CAAC;QApUnC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC;QAE7E,qGAAqG;QACrG,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1F,CAAC;IAED;;;;OAIG;IACI,gBAAgB,CAAC,QAAgB;QACpC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3C,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACtE,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QACnD,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACjD,CAAC;IAED,gBAAgB;IACT,wBAAwB;QAC3B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;YACrC,MAAM,YAAY,GAAG,IAAI,CAAC,YAA4B,CAAC;YACvD,MAAM,CAAC,GAAG,YAAY,CAAC,kBAAkB,EAAE,CAAC;YAC5C,wGAAwG;YACxG,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,IAAI,IAAI,CAAC,YAAY,CAAC;IACnE,CAAC;IAMD;;;OAGG;IACa,UAAU;QACtB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QACrE,CAAC;QAED,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACa,mBAAmB;QAC/B,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAC/B,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAE7C,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACA,UAAU;QACtB,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC7F,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACzF,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;IAC5H,CAAC;IAED;;OAEG;IACa,YAAY,CAAC,iBAA2B;QACpD,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrB,KAAK,CAAC,YAAY,EAAE,CAAC;QACzB,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAC7D,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC;QACpC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,oBAAoB,CAAC,KAAK,EAAE,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;IAED,eAAe;IACf,gBAAgB;IACA,yBAAyB;QACrC,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAE7D,OAAO,CACH,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;YAC1G,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAC1I,CAAC;IACN,CAAC;IAED,UAAU;IACV,gBAAgB;IACT,wBAAwB;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC;IACrF,CAAC;IAED,SAAS;IAET;;;OAGG;IACI,SAAS,CAAC,MAAe;QAC5B,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;QAE1B,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;QAErE,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAE1E,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,CAAC;YACvC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAC/E,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAC/E,CAAC;QACD,SAAS,CAAC,MAAM,EAAE,CAAC;QAEnB,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,IAAI,aAAa,CAAC;QACpE,UAAU,CAAC,uBAAuB,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;QAElE,kBAAkB,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAErD,oDAAoD;QACpD,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC5B,CAAC;IACD,IAAW,MAAM,CAAC,KAAc;QAC5B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChI,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAChG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACjC,CAAC;YACD,OAAO;QACX,CAAC;QACD,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;IACL,CAAC;IAED,gBAAgB;IACA,YAAY;QACxB,MAAM,mBAAmB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC;QACnF,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;QAEpE,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,iCAAiC,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7E,CAAC;QAED,OAAO;QACP,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC;QAED,SAAS;QACT,IAAI,YAAY,EAAE,CAAC;YACf,oDAAoD;YACpD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,mBAAmB,CAAC;YAC9E,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,mBAAmB,CAAC;YAE9E,oBAAoB;YACpB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC7B,MAAM,KAAK,GAAG,QAAQ,CAAC;gBAEvB,IAAI,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;oBACzC,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,KAAK,CAAC;gBAC3C,CAAC;gBACD,IAAI,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;oBAC1C,IAAI,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;gBAC5C,CAAC;YACL,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACjC,CAAC;YAED,oDAAoD;YACpD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,aAAa,EAAE,CAAC;gBACzD,IAAI,GAAG,EAAE,CAAC;oBACN,UAAU,CAAC,yBAAyB,CAChC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAC9B,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAC9B,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAC9B,IAAI,CAAC,iCAAiC,CACzC,CAAC;oBACF,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;wBACnB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;oBAC7E,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;oBACjC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,UAAU;QACV,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;gBAC1D,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC;YAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;gBAC1D,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC;YAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;gBAC1D,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/B,CAAC;YAED,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,YAAY,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;gBACzD,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;YAC9B,CAAC;YAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE,CAAC;gBACzD,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC;YAC9B,CAAC;YACD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;QAED,KAAK,CAAC,YAAY,EAAE,CAAC;IACzB,CAAC;IAES,2BAA2B;QACjC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACzE,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACpH,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,uCAAuC;QAC3C,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5F,OAAO,IAAI,CAAC;IAChB,CAAC;IAID,gBAAgB;IACA,cAAc;QAC1B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,wBAAwB,EAAG,CAAC,CAAC;QACrD,CAAC;QAED,UAAU;QACV,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAEnC,6CAA6C;QAC7C,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC;YAC1F,IAAI,CAAC,uCAAuC,EAAE,CAAC;YAC/C,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAChE,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,uCAAuC,EAAE,CAAC;YAC/C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAErH,oCAAoC;QACpC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7E,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACJ,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;gBAC9D,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjE,CAAC;QACL,CAAC;QACD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3E,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAES,kBAAkB,CAAC,QAAiB,EAAE,MAAe,EAAE,EAAW;QACxE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC,oBAAoB,EAAE,CAAC;YACvC,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YACvD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,iBAAiB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;YACpE,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YAE1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACjC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,6DAA6D;IAC7C,eAAe,CAAC,IAAY,EAAE,WAAmB;QAC7D,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa,EAAE,CAAC;YAC9C,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACjF,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;YAC7B,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC;YAC3B,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC5C,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;gBAC/C,CAAC;gBACD,SAAS,CAAC,gBAAgB,GAAG,EAAE,CAAC;gBAChC,SAAS,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,CAAC;YACpD,CAAC;YAED,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YAC3B,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACrC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YAEzC,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACa,iBAAiB;QAC7B,MAAM,OAAO,GAAiB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,QAAQ,GAAiB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEnD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,KAAK,MAAM,CAAC,8BAA8B,CAAC;YAC3C,KAAK,MAAM,CAAC,yCAAyC,CAAC;YACtD,KAAK,MAAM,CAAC,0CAA0C,CAAC;YACvD,KAAK,MAAM,CAAC,+BAA+B,CAAC;YAC5C,KAAK,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAAC;gBAC3C,4HAA4H;gBAC5H,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,0CAA0C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnG,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,0CAA0C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC5F,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,GAAG,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC9F,MAAM;YACV,CAAC;YACD,KAAK,MAAM,CAAC,WAAW;gBACnB,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC7B,OAAO,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAC7D,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAClE,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACzC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC9C,CAAC;gBACD,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACzC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAE1C,MAAM;QACd,CAAC;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC9B,CAAC;IAEO,2BAA2B,CAAC,SAAiB,EAAE,SAAuB;QAC1E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAC;QAEpE,YAAY,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACpF,MAAM,cAAc,GAAG,YAAY,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhF,MAAM,CAAC,gBAAgB,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC;QACtH,YAAY,CAAC,sBAAsB,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAC3I,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAEnH,YAAY,CAAC,sBAAsB,CAAC,aAAa,CAAC,YAAY,CAAC,sBAAsB,EAAE,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAE5H,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,sBAAsB,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC1G,SAAS,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED;;;OAGG;IACa,YAAY;QACxB,OAAO,cAAc,CAAC;IAC1B,CAAC;;AAjjBc,mCAAsB,GAAG,IAAI,MAAM,EAAE,AAAf,CAAgB;AACtC,mCAAsB,GAAG,IAAI,MAAM,EAAE,AAAf,CAAgB;AACtC,8BAAiB,GAAG,IAAI,OAAO,EAAE,AAAhB,CAAiB;AAe1C;IADN,SAAS,EAAE;gEAC8B;AAMnC;IADN,kBAAkB,EAAE;8CACI;AAWlB;IADN,SAAS,EAAE;2CACO;AAwBZ;IADN,wBAAwB,CAAC,gBAAgB,CAAC;kDACX", "sourcesContent": ["import { serialize, serializeAsVector3, serializeAsMeshReference } from \"../Misc/decorators\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Camera } from \"./camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Quaternion, Matrix, Vector3, Vector2, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\nimport { Axis } from \"../Maths/math.axis\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Node } from \"../node\";\r\n\r\nNode.AddNodeConstructor(\"TargetCamera\", (name, scene) => {\r\n    return () => new TargetCamera(name, Vector3.Zero(), scene);\r\n});\r\n\r\n// Temporary cache variables to avoid allocations.\r\nconst TmpMatrix = Matrix.Zero();\r\nconst TmpQuaternion = Quaternion.Identity();\r\n\r\n/**\r\n * A target camera takes a mesh or position as a target and continues to look at it while it moves.\r\n * This is the base of the follow, arc rotate cameras and Free camera\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n */\r\nexport class TargetCamera extends Camera {\r\n    private static _RigCamTransformMatrix = new Matrix();\r\n    private static _TargetTransformMatrix = new Matrix();\r\n    private static _TargetFocalPoint = new Vector3();\r\n\r\n    /**\r\n     * Define the current direction the camera is moving to\r\n     */\r\n    public cameraDirection = new Vector3(0, 0, 0);\r\n    /**\r\n     * Define the current rotation the camera is rotating to\r\n     */\r\n    public cameraRotation = new Vector2(0, 0);\r\n\r\n    /**\r\n     * When set, the up vector of the camera will be updated by the rotation of the camera\r\n     */\r\n    @serialize()\r\n    public updateUpVectorFromRotation = false;\r\n\r\n    /**\r\n     * Define the current rotation of the camera\r\n     */\r\n    @serializeAsVector3()\r\n    public rotation: Vector3;\r\n\r\n    /**\r\n     * Define the current rotation of the camera as a quaternion to prevent Gimbal lock\r\n     */\r\n    public rotationQuaternion: Quaternion;\r\n\r\n    /**\r\n     * Define the current speed of the camera\r\n     */\r\n    @serialize()\r\n    public speed = 2.0;\r\n\r\n    /**\r\n     * Add constraint to the camera to prevent it to move freely in all directions and\r\n     * around all axis.\r\n     */\r\n    public noRotationConstraint = false;\r\n\r\n    /**\r\n     * Reverses mouselook direction to 'natural' panning as opposed to traditional direct\r\n     * panning\r\n     */\r\n    public invertRotation = false;\r\n\r\n    /**\r\n     * Speed multiplier for inverse camera panning\r\n     */\r\n    public inverseRotationSpeed = 0.2;\r\n\r\n    /**\r\n     * Define the current target of the camera as an object or a position.\r\n     * Please note that locking a target will disable panning.\r\n     */\r\n    @serializeAsMeshReference(\"lockedTargetId\")\r\n    public lockedTarget: any = null;\r\n\r\n    protected readonly _currentTarget = Vector3.Zero();\r\n    protected _initialFocalDistance = 1;\r\n    protected readonly _viewMatrix = Matrix.Zero();\r\n\r\n    /** @internal */\r\n    public readonly _cameraTransformMatrix = Matrix.Zero();\r\n    /** @internal */\r\n    public readonly _cameraRotationMatrix = Matrix.Zero();\r\n\r\n    protected readonly _referencePoint: Vector3;\r\n    protected readonly _transformedReferencePoint = Vector3.Zero();\r\n\r\n    protected readonly _deferredPositionUpdate = new Vector3();\r\n    protected readonly _deferredRotationQuaternionUpdate = new Quaternion();\r\n    protected readonly _deferredRotationUpdate = new Vector3();\r\n    protected _deferredUpdated = false;\r\n    protected _deferOnly: boolean = false;\r\n\r\n    /** @internal */\r\n    public _reset: () => void;\r\n\r\n    /**\r\n     * Instantiates a target camera that takes a mesh or position as a target and continues to look at it while it moves.\r\n     * This is the base of the follow, arc rotate cameras and Free camera\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n     * @param name Defines the name of the camera in the scene\r\n     * @param position Defines the start position of the camera in the scene\r\n     * @param scene Defines the scene the camera belongs to\r\n     * @param setActiveOnSceneIfNoneActive Defines whether the camera should be marked as active if not other active cameras have been defined\r\n     */\r\n    constructor(name: string, position: Vector3, scene?: Scene, setActiveOnSceneIfNoneActive = true) {\r\n        super(name, position, scene, setActiveOnSceneIfNoneActive);\r\n\r\n        this._referencePoint = Vector3.Forward(this.getScene().useRightHandedSystem);\r\n\r\n        // Set the y component of the rotation to Math.PI in right-handed system for backwards compatibility.\r\n        this.rotation = new Vector3(0, this.getScene().useRightHandedSystem ? Math.PI : 0, 0);\r\n    }\r\n\r\n    /**\r\n     * Gets the position in front of the camera at a given distance.\r\n     * @param distance The distance from the camera we want the position to be\r\n     * @returns the position\r\n     */\r\n    public getFrontPosition(distance: number): Vector3 {\r\n        this.getWorldMatrix();\r\n        const worldForward = TmpVectors.Vector3[0];\r\n        const localForward = TmpVectors.Vector3[1];\r\n        localForward.set(0, 0, this._scene.useRightHandedSystem ? -1.0 : 1.0);\r\n        this.getDirectionToRef(localForward, worldForward);\r\n        worldForward.scaleInPlace(distance);\r\n        return this.globalPosition.add(worldForward);\r\n    }\r\n\r\n    /** @internal */\r\n    public _getLockedTargetPosition(): Nullable<Vector3> {\r\n        if (!this.lockedTarget) {\r\n            return null;\r\n        }\r\n\r\n        if (this.lockedTarget.absolutePosition) {\r\n            const lockedTarget = this.lockedTarget as AbstractMesh;\r\n            const m = lockedTarget.computeWorldMatrix();\r\n            // in some cases the absolute position resets externally, but doesn't update since the matrix is cached.\r\n            m.getTranslationToRef(lockedTarget.absolutePosition);\r\n        }\r\n\r\n        return this.lockedTarget.absolutePosition || this.lockedTarget;\r\n    }\r\n\r\n    private _storedPosition: Vector3;\r\n    private _storedRotation: Vector3;\r\n    private _storedRotationQuaternion: Quaternion;\r\n\r\n    /**\r\n     * Store current camera state of the camera (fov, position, rotation, etc..)\r\n     * @returns the camera\r\n     */\r\n    public override storeState(): Camera {\r\n        this._storedPosition = this.position.clone();\r\n        this._storedRotation = this.rotation.clone();\r\n        if (this.rotationQuaternion) {\r\n            this._storedRotationQuaternion = this.rotationQuaternion.clone();\r\n        }\r\n\r\n        return super.storeState();\r\n    }\r\n\r\n    /**\r\n     * Restored camera state. You must call storeState() first\r\n     * @returns whether it was successful or not\r\n     * @internal\r\n     */\r\n    public override _restoreStateValues(): boolean {\r\n        if (!super._restoreStateValues()) {\r\n            return false;\r\n        }\r\n\r\n        this.position = this._storedPosition.clone();\r\n        this.rotation = this._storedRotation.clone();\r\n\r\n        if (this.rotationQuaternion) {\r\n            this.rotationQuaternion = this._storedRotationQuaternion.clone();\r\n        }\r\n\r\n        this.cameraDirection.copyFromFloats(0, 0, 0);\r\n        this.cameraRotation.copyFromFloats(0, 0);\r\n\r\n        return true;\r\n    }\r\n\r\n    /** @internal */\r\n    public override _initCache() {\r\n        super._initCache();\r\n        this._cache.lockedTarget = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        this._cache.rotation = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        this._cache.rotationQuaternion = new Quaternion(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override _updateCache(ignoreParentClass?: boolean): void {\r\n        if (!ignoreParentClass) {\r\n            super._updateCache();\r\n        }\r\n\r\n        const lockedTargetPosition = this._getLockedTargetPosition();\r\n        if (!lockedTargetPosition) {\r\n            this._cache.lockedTarget = null;\r\n        } else {\r\n            if (!this._cache.lockedTarget) {\r\n                this._cache.lockedTarget = lockedTargetPosition.clone();\r\n            } else {\r\n                this._cache.lockedTarget.copyFrom(lockedTargetPosition);\r\n            }\r\n        }\r\n\r\n        this._cache.rotation.copyFrom(this.rotation);\r\n        if (this.rotationQuaternion) {\r\n            this._cache.rotationQuaternion.copyFrom(this.rotationQuaternion);\r\n        }\r\n    }\r\n\r\n    // Synchronized\r\n    /** @internal */\r\n    public override _isSynchronizedViewMatrix(): boolean {\r\n        if (!super._isSynchronizedViewMatrix()) {\r\n            return false;\r\n        }\r\n\r\n        const lockedTargetPosition = this._getLockedTargetPosition();\r\n\r\n        return (\r\n            (this._cache.lockedTarget ? this._cache.lockedTarget.equals(lockedTargetPosition) : !lockedTargetPosition) &&\r\n            (this.rotationQuaternion ? this.rotationQuaternion.equals(this._cache.rotationQuaternion) : this._cache.rotation.equals(this.rotation))\r\n        );\r\n    }\r\n\r\n    // Methods\r\n    /** @internal */\r\n    public _computeLocalCameraSpeed(): number {\r\n        const engine = this.getEngine();\r\n        return this.speed * Math.sqrt(engine.getDeltaTime() / (engine.getFps() * 100.0));\r\n    }\r\n\r\n    // Target\r\n\r\n    /**\r\n     * Defines the target the camera should look at.\r\n     * @param target Defines the new target as a Vector\r\n     */\r\n    public setTarget(target: Vector3): void {\r\n        this.upVector.normalize();\r\n\r\n        this._initialFocalDistance = target.subtract(this.position).length();\r\n\r\n        if (this.position.z === target.z) {\r\n            this.position.z += Epsilon;\r\n        }\r\n\r\n        this._referencePoint.normalize().scaleInPlace(this._initialFocalDistance);\r\n\r\n        if (this.getScene().useRightHandedSystem) {\r\n            Matrix.LookAtRHToRef(this.position, target, Vector3.UpReadOnly, TmpMatrix);\r\n        } else {\r\n            Matrix.LookAtLHToRef(this.position, target, Vector3.UpReadOnly, TmpMatrix);\r\n        }\r\n        TmpMatrix.invert();\r\n\r\n        const rotationQuaternion = this.rotationQuaternion || TmpQuaternion;\r\n        Quaternion.FromRotationMatrixToRef(TmpMatrix, rotationQuaternion);\r\n\r\n        rotationQuaternion.toEulerAnglesToRef(this.rotation);\r\n\r\n        // Explicitly set z to 0 to match previous behavior.\r\n        this.rotation.z = 0;\r\n    }\r\n\r\n    /**\r\n     * Defines the target point of the camera.\r\n     * The camera looks towards it form the radius distance.\r\n     */\r\n    public get target(): Vector3 {\r\n        return this.getTarget();\r\n    }\r\n    public set target(value: Vector3) {\r\n        this.setTarget(value);\r\n    }\r\n\r\n    /**\r\n     * Return the current target position of the camera. This value is expressed in local space.\r\n     * @returns the target position\r\n     */\r\n    public getTarget(): Vector3 {\r\n        return this._currentTarget;\r\n    }\r\n\r\n    /** @internal */\r\n    public _decideIfNeedsToMove(): boolean {\r\n        return Math.abs(this.cameraDirection.x) > 0 || Math.abs(this.cameraDirection.y) > 0 || Math.abs(this.cameraDirection.z) > 0;\r\n    }\r\n\r\n    /** @internal */\r\n    public _updatePosition(): void {\r\n        if (this.parent) {\r\n            this.parent.getWorldMatrix().invertToRef(TmpVectors.Matrix[0]);\r\n            Vector3.TransformNormalToRef(this.cameraDirection, TmpVectors.Matrix[0], TmpVectors.Vector3[0]);\r\n            this._deferredPositionUpdate.addInPlace(TmpVectors.Vector3[0]);\r\n            if (!this._deferOnly) {\r\n                this.position.copyFrom(this._deferredPositionUpdate);\r\n            } else {\r\n                this._deferredUpdated = true;\r\n            }\r\n            return;\r\n        }\r\n        this._deferredPositionUpdate.addInPlace(this.cameraDirection);\r\n        if (!this._deferOnly) {\r\n            this.position.copyFrom(this._deferredPositionUpdate);\r\n        } else {\r\n            this._deferredUpdated = true;\r\n        }\r\n    }\r\n\r\n    /** @internal */\r\n    public override _checkInputs(): void {\r\n        const directionMultiplier = this.invertRotation ? -this.inverseRotationSpeed : 1.0;\r\n        const needToMove = this._decideIfNeedsToMove();\r\n        const needToRotate = this.cameraRotation.x || this.cameraRotation.y;\r\n\r\n        this._deferredUpdated = false;\r\n        this._deferredRotationUpdate.copyFrom(this.rotation);\r\n        this._deferredPositionUpdate.copyFrom(this.position);\r\n        if (this.rotationQuaternion) {\r\n            this._deferredRotationQuaternionUpdate.copyFrom(this.rotationQuaternion);\r\n        }\r\n\r\n        // Move\r\n        if (needToMove) {\r\n            this._updatePosition();\r\n        }\r\n\r\n        // Rotate\r\n        if (needToRotate) {\r\n            //rotate, if quaternion is set and rotation was used\r\n            if (this.rotationQuaternion) {\r\n                this.rotationQuaternion.toEulerAnglesToRef(this._deferredRotationUpdate);\r\n            }\r\n\r\n            this._deferredRotationUpdate.x += this.cameraRotation.x * directionMultiplier;\r\n            this._deferredRotationUpdate.y += this.cameraRotation.y * directionMultiplier;\r\n\r\n            // Apply constraints\r\n            if (!this.noRotationConstraint) {\r\n                const limit = 1.570796;\r\n\r\n                if (this._deferredRotationUpdate.x > limit) {\r\n                    this._deferredRotationUpdate.x = limit;\r\n                }\r\n                if (this._deferredRotationUpdate.x < -limit) {\r\n                    this._deferredRotationUpdate.x = -limit;\r\n                }\r\n            }\r\n\r\n            if (!this._deferOnly) {\r\n                this.rotation.copyFrom(this._deferredRotationUpdate);\r\n            } else {\r\n                this._deferredUpdated = true;\r\n            }\r\n\r\n            //rotate, if quaternion is set and rotation was used\r\n            if (this.rotationQuaternion) {\r\n                const len = this._deferredRotationUpdate.lengthSquared();\r\n                if (len) {\r\n                    Quaternion.RotationYawPitchRollToRef(\r\n                        this._deferredRotationUpdate.y,\r\n                        this._deferredRotationUpdate.x,\r\n                        this._deferredRotationUpdate.z,\r\n                        this._deferredRotationQuaternionUpdate\r\n                    );\r\n                    if (!this._deferOnly) {\r\n                        this.rotationQuaternion.copyFrom(this._deferredRotationQuaternionUpdate);\r\n                    } else {\r\n                        this._deferredUpdated = true;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Inertia\r\n        if (needToMove) {\r\n            if (Math.abs(this.cameraDirection.x) < this.speed * Epsilon) {\r\n                this.cameraDirection.x = 0;\r\n            }\r\n\r\n            if (Math.abs(this.cameraDirection.y) < this.speed * Epsilon) {\r\n                this.cameraDirection.y = 0;\r\n            }\r\n\r\n            if (Math.abs(this.cameraDirection.z) < this.speed * Epsilon) {\r\n                this.cameraDirection.z = 0;\r\n            }\r\n\r\n            this.cameraDirection.scaleInPlace(this.inertia);\r\n        }\r\n        if (needToRotate) {\r\n            if (Math.abs(this.cameraRotation.x) < this.speed * Epsilon) {\r\n                this.cameraRotation.x = 0;\r\n            }\r\n\r\n            if (Math.abs(this.cameraRotation.y) < this.speed * Epsilon) {\r\n                this.cameraRotation.y = 0;\r\n            }\r\n            this.cameraRotation.scaleInPlace(this.inertia);\r\n        }\r\n\r\n        super._checkInputs();\r\n    }\r\n\r\n    protected _updateCameraRotationMatrix() {\r\n        if (this.rotationQuaternion) {\r\n            this.rotationQuaternion.toRotationMatrix(this._cameraRotationMatrix);\r\n        } else {\r\n            Matrix.RotationYawPitchRollToRef(this.rotation.y, this.rotation.x, this.rotation.z, this._cameraRotationMatrix);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Update the up vector to apply the rotation of the camera (So if you changed the camera rotation.z this will let you update the up vector as well)\r\n     * @returns the current camera\r\n     */\r\n    private _rotateUpVectorWithCameraRotationMatrix(): TargetCamera {\r\n        Vector3.TransformNormalToRef(Vector3.UpReadOnly, this._cameraRotationMatrix, this.upVector);\r\n        return this;\r\n    }\r\n\r\n    private _cachedRotationZ = 0;\r\n    private _cachedQuaternionRotationZ = 0;\r\n    /** @internal */\r\n    public override _getViewMatrix(): Matrix {\r\n        if (this.lockedTarget) {\r\n            this.setTarget(this._getLockedTargetPosition()!);\r\n        }\r\n\r\n        // Compute\r\n        this._updateCameraRotationMatrix();\r\n\r\n        // Apply the changed rotation to the upVector\r\n        if (this.rotationQuaternion && this._cachedQuaternionRotationZ != this.rotationQuaternion.z) {\r\n            this._rotateUpVectorWithCameraRotationMatrix();\r\n            this._cachedQuaternionRotationZ = this.rotationQuaternion.z;\r\n        } else if (this._cachedRotationZ !== this.rotation.z) {\r\n            this._rotateUpVectorWithCameraRotationMatrix();\r\n            this._cachedRotationZ = this.rotation.z;\r\n        }\r\n\r\n        Vector3.TransformCoordinatesToRef(this._referencePoint, this._cameraRotationMatrix, this._transformedReferencePoint);\r\n\r\n        // Computing target and final matrix\r\n        this.position.addToRef(this._transformedReferencePoint, this._currentTarget);\r\n        if (this.updateUpVectorFromRotation) {\r\n            if (this.rotationQuaternion) {\r\n                Axis.Y.rotateByQuaternionToRef(this.rotationQuaternion, this.upVector);\r\n            } else {\r\n                Quaternion.FromEulerVectorToRef(this.rotation, TmpQuaternion);\r\n                Axis.Y.rotateByQuaternionToRef(TmpQuaternion, this.upVector);\r\n            }\r\n        }\r\n        this._computeViewMatrix(this.position, this._currentTarget, this.upVector);\r\n        return this._viewMatrix;\r\n    }\r\n\r\n    protected _computeViewMatrix(position: Vector3, target: Vector3, up: Vector3): void {\r\n        if (this.getScene().useRightHandedSystem) {\r\n            Matrix.LookAtRHToRef(position, target, up, this._viewMatrix);\r\n        } else {\r\n            Matrix.LookAtLHToRef(position, target, up, this._viewMatrix);\r\n        }\r\n\r\n        if (this.parent) {\r\n            const parentWorldMatrix = this.parent.getWorldMatrix();\r\n            this._viewMatrix.invert();\r\n            this._viewMatrix.multiplyToRef(parentWorldMatrix, this._viewMatrix);\r\n            this._viewMatrix.getTranslationToRef(this._globalPosition);\r\n            this._viewMatrix.invert();\r\n\r\n            this._markSyncedWithParent();\r\n        } else {\r\n            this._globalPosition.copyFrom(position);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public override createRigCamera(name: string, cameraIndex: number): Nullable<Camera> {\r\n        if (this.cameraRigMode !== Camera.RIG_MODE_NONE) {\r\n            const rigCamera = new TargetCamera(name, this.position.clone(), this.getScene());\r\n            rigCamera.isRigCamera = true;\r\n            rigCamera.rigParent = this;\r\n            if (this.cameraRigMode === Camera.RIG_MODE_VR) {\r\n                if (!this.rotationQuaternion) {\r\n                    this.rotationQuaternion = new Quaternion();\r\n                }\r\n                rigCamera._cameraRigParams = {};\r\n                rigCamera.rotationQuaternion = new Quaternion();\r\n            }\r\n\r\n            rigCamera.mode = this.mode;\r\n            rigCamera.orthoLeft = this.orthoLeft;\r\n            rigCamera.orthoRight = this.orthoRight;\r\n            rigCamera.orthoTop = this.orthoTop;\r\n            rigCamera.orthoBottom = this.orthoBottom;\r\n\r\n            return rigCamera;\r\n        }\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override _updateRigCameras() {\r\n        const camLeft = <TargetCamera>this._rigCameras[0];\r\n        const camRight = <TargetCamera>this._rigCameras[1];\r\n\r\n        this.computeWorldMatrix();\r\n\r\n        switch (this.cameraRigMode) {\r\n            case Camera.RIG_MODE_STEREOSCOPIC_ANAGLYPH:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_OVERUNDER:\r\n            case Camera.RIG_MODE_STEREOSCOPIC_INTERLACED: {\r\n                //provisionnaly using _cameraRigParams.stereoHalfAngle instead of calculations based on _cameraRigParams.interaxialDistance:\r\n                const leftSign = this.cameraRigMode === Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED ? 1 : -1;\r\n                const rightSign = this.cameraRigMode === Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_CROSSEYED ? -1 : 1;\r\n                this._getRigCamPositionAndTarget(this._cameraRigParams.stereoHalfAngle * leftSign, camLeft);\r\n                this._getRigCamPositionAndTarget(this._cameraRigParams.stereoHalfAngle * rightSign, camRight);\r\n                break;\r\n            }\r\n            case Camera.RIG_MODE_VR:\r\n                if (camLeft.rotationQuaternion) {\r\n                    camLeft.rotationQuaternion.copyFrom(this.rotationQuaternion);\r\n                    camRight.rotationQuaternion.copyFrom(this.rotationQuaternion);\r\n                } else {\r\n                    camLeft.rotation.copyFrom(this.rotation);\r\n                    camRight.rotation.copyFrom(this.rotation);\r\n                }\r\n                camLeft.position.copyFrom(this.position);\r\n                camRight.position.copyFrom(this.position);\r\n\r\n                break;\r\n        }\r\n        super._updateRigCameras();\r\n    }\r\n\r\n    private _getRigCamPositionAndTarget(halfSpace: number, rigCamera: TargetCamera) {\r\n        const target = this.getTarget();\r\n        target.subtractToRef(this.position, TargetCamera._TargetFocalPoint);\r\n\r\n        TargetCamera._TargetFocalPoint.normalize().scaleInPlace(this._initialFocalDistance);\r\n        const newFocalTarget = TargetCamera._TargetFocalPoint.addInPlace(this.position);\r\n\r\n        Matrix.TranslationToRef(-newFocalTarget.x, -newFocalTarget.y, -newFocalTarget.z, TargetCamera._TargetTransformMatrix);\r\n        TargetCamera._TargetTransformMatrix.multiplyToRef(Matrix.RotationAxis(rigCamera.upVector, halfSpace), TargetCamera._RigCamTransformMatrix);\r\n        Matrix.TranslationToRef(newFocalTarget.x, newFocalTarget.y, newFocalTarget.z, TargetCamera._TargetTransformMatrix);\r\n\r\n        TargetCamera._RigCamTransformMatrix.multiplyToRef(TargetCamera._TargetTransformMatrix, TargetCamera._RigCamTransformMatrix);\r\n\r\n        Vector3.TransformCoordinatesToRef(this.position, TargetCamera._RigCamTransformMatrix, rigCamera.position);\r\n        rigCamera.setTarget(newFocalTarget);\r\n    }\r\n\r\n    /**\r\n     * Gets the current object class name.\r\n     * @returns the class name\r\n     */\r\n    public override getClassName(): string {\r\n        return \"TargetCamera\";\r\n    }\r\n}\r\n"]}