{"version": 3, "file": "gpuPicker.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Collisions/gpuPicker.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,gCAA+B;AAGnD,OAAO,EAAE,mBAAmB,EAAE,qDAAoD;AAGlF,OAAO,EAAE,cAAc,EAAE,uCAAsC;AAC/D,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,+BAA8B;AAGvD,OAAO,EAAE,YAAY,EAAE,4BAA2B;AAGlD,OAAO,EAAE,MAAM,EAAE,0BAAyB;AAiC1C;;;GAGG;AACH,MAAM,OAAO,SAAS;IAAtB;QACY,oBAAe,GAAkC,IAAI,CAAC;QACtD,WAAM,GAAkB,EAAE,CAAC;QAC3B,eAAU,GAA8C,EAAE,CAAC;QAC3D,cAAS,GAAkB,EAAE,CAAC;QAK9B,qBAAgB,GAAsC,IAAI,GAAG,EAAE,CAAC;QAEhE,wBAAmB,GAAW,CAAC,CAAC;QACvB,mBAAc,GAAG,gBAAgB,CAAC;QAC3C,mBAAc,GAAG,KAAK,CAAC;QACvB,0BAAqB,GAAG,KAAK,CAAC;QAC9B,+BAA0B,GAA8B,IAAI,CAAC;QAC7D,yCAAoC,GAA+B,IAAI,CAAC;QAEhF,4CAA4C;QAClC,oBAAe,+BAAuB;QAexC,uBAAkB,GAAG,KAAK,CAAC;IA4lBvC,CAAC;IAnmBG;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAID;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAEO,MAAM,CAAC,QAAQ,CAAC,EAAU;QAC9B,SAAS,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/C,SAAS,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9C,SAAS,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;IAEO,yBAAyB,CAAC,MAAc;QAC5C,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACnC,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACvC,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACvC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IACpC,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,MAAoB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS;QACzF,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACtB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC1B,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAC1B,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IACxB,CAAC;IAEO,mBAAmB,CAAC,KAAY,EAAE,KAAa,EAAE,MAAc;QACnE,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAClF,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACvD,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACvC,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAC1C,eAAe,EACf,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAChC,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS,CAAC,yBAAyB,EACnC,KAAK,EACL,SAAS,CAAC,uBAAuB,CACpC,CAAC;IACN,CAAC;IAED,4DAA4D;IACpD,KAAK,CAAC,yBAAyB,CAAC,KAAY;QAChD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QAEnC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,eAAe,8BAAsB,CAAC;QAC/C,CAAC;QAED,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAoC;YAC7C,UAAU,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,cAAc,EAAE,uCAAuC,CAAC;YACrG,QAAQ,EAAE,CAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,CAAC;YAC/C,iBAAiB,EAAE,KAAK;YACxB,OAAO,EAAE,OAAO;YAChB,YAAY,EAAE,IAAI;YAClB,cAAc,EAAE,IAAI,CAAC,eAAe;YACpC,yBAAyB,EAAE,KAAK,IAAI,EAAE;gBAClC,IAAI,IAAI,CAAC,cAAc,gCAAwB,EAAE,CAAC;oBAC9C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,iCAAiC,CAAC,EAAE,MAAM,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;gBAC5G,CAAC;qBAAM,CAAC;oBACJ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,6BAA6B,CAAC,EAAE,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC;gBACpG,CAAC;YACL,CAAC;SACJ,CAAC;QAEF,IAAI,CAAC,sBAAsB,GAAG,IAAI,cAAc,CAAC,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAEpG,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC7G,CAAC;IAEO,qBAAqB,CAAC,IAA8B;QACxD,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;QAElD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC,oIAAoI,CAAC,CAAC;YACtJ,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;QAEpC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE,CAAC;YACpH,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,kBAAkB,CAAC,aAAqB,EAAE,EAAU,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,UAA2C;QACtI,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;QAE5D,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvB,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClB,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACxH,EAAE,EAAE,CAAC;QACT,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,8BAA8B,CAAC,aAAqB,EAAE,EAAU,EAAE,UAA2C;QACjH,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;QAEtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvB,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAClB,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAElH,EAAE,EAAE,CAAC;QACT,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;OAKG;IACI,cAAc,CAAC,IAAsF;QACxG,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,UAAU;YACV,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBACzC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACnB,IAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC3D,CAAC;gBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACvB,IAAa,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;gBACpE,CAAC;gBACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAClE,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;gBAClD,IAAI,QAAQ,KAAK,IAAI,CAAC,sBAAsB,EAAE,CAAC;oBAC3C,QAAQ,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACzE,CAAC;YACL,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;YAChC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YAC1B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC;YACzC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAA2B,CAAC;QAEnD,iBAAiB;QACjB,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACtE,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAE5C,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;gBACrF,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;QAED,IAAI,CAAC,0BAA0B,EAAE,MAAM,EAAE,CAAC;QAC1C,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtE,IAAI,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC9F,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;gBACpG,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC;YAC1C,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;YACpD,mEAAmE;YACnE,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACjB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACpD,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,sBAAuB,CAAC,CAAC;YAClE,CAAC;QACL,CAAC;QAED,IAAI,CAAC,eAAgB,CAAC,UAAU,GAAG,EAAE,CAAC;QAEtC,wDAAwD;QACxD,IAAI,EAAE,GAAG,CAAC,CAAC;QACX,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;YAElD,IAAI,QAAQ,KAAK,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC3C,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAC1F,CAAC;YACD,IAAI,CAAC,eAAgB,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9D,IAAI,CAAC,eAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE5C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,SAAS,CAAC,0CAA0C;YACxD,CAAC;YAED,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEvB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAM,SAAS,GAAG,IAAI,CAAC,8BAA8B,CAAE,IAAa,CAAC,iBAAiB,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE;oBAClG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;gBACvD,CAAC,CAAC,CAAC;gBACH,EAAE,IAAK,IAAa,CAAC,iBAAiB,CAAC;gBACtC,IAAa,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;YAC5E,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;gBACxB,EAAE,EAAE,CAAC;gBAEL,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,IAAK,CAAmB,CAAC,UAAU,KAAK,IAAI,CAAC,CAAC;oBACxH,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE;wBACrJ,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;wBACrC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC7D,CAAC,CAAC,CAAC;oBAEH,EAAE,IAAI,gBAAgB,CAAC,MAAM,CAAC;oBAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBAEhC,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;oBAC9F,IAAa,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBACnD,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC5H,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,SAAS,CAAC,CAAS,EAAE,CAAS,EAAE,eAAe,GAAG,KAAK;QAChE,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7D,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEvE,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC;QACvF,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,EAAE,CAAC;YACnF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,WAAW;QACX,MAAM,SAAS,GAAG,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAEpF,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC,CAAC;IAClF,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,cAAc,CAAC,EAAkB,EAAE,eAAe,GAAG,KAAK;QACnE,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClB,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;YACnE,OAAO;gBACH,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,IAAI,IAAI,CAAC;gBAC1B,mBAAmB,EAAE,EAAE,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS;aAClF,CAAC;QACN,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAE/B,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAEzC,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC;QACrB,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC;QAErB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEvE,2CAA2C;QAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACjC,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACnB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC;YAEtB,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC;YAEvF,WAAW,CAAC,CAAC,CAAC,GAAG;gBACb,GAAG,IAAI;gBACP,CAAC,EAAE,SAAS;gBACZ,CAAC,EAAE,SAAS;aACf,CAAC;YAEF,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACjC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACjC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACjC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACrC,CAAC;QAED,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;QACnC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;QACnC,MAAM,WAAW,GAAG,QAAQ,GAAG,IAAI,GAAG,CAAC,CAAC;QAExC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEvF,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC;IAC1G,CAAC;IAEO,cAAc;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAa,CAAC,SAAS,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QACzC,MAAM,QAAQ,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;QAC1C,MAAM,gBAAgB,GAAG,CAAC,GAAG,MAAM,CAAC,qBAAqB,CAAC;QAE1D,OAAO;YACH,QAAQ;YACR,QAAQ;YACR,gBAAgB;SACnB,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,CAAS,EAAE,CAAS,EAAE,gBAAwB;QACrE,OAAO,EAAE,CAAC,EAAE,CAAC,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;IAC9E,CAAC;IAEO,qBAAqB,CAAC,MAAsB,EAAE,QAAgB,EAAE,QAAgB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC;QACxH,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAE7B,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClF,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,kBAAkB,EAAE,CAAC;YACpE,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,kBAAkB,CAAC,CAAC;QAC1D,CAAC;QAED,iCAAiC;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,eAAgB,CAAC,OAAO,EAAE,CAAC;QAC7C,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACtD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAa,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACjE,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,eAAgB,CAAC,UAAU,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE1D,IAAI,CAAC,eAAgB,CAAC,cAAc,GAAG,GAAG,EAAE;YACxC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC;QAEF,IAAI,CAAC,oCAAoC,EAAE,MAAM,EAAE,CAAC;QACpD,IAAI,CAAC,oCAAoC,GAAG,IAAI,CAAC,eAAgB,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/F,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,YAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,eAAgB,CAAC,CAAC;QACnE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED,iBAAiB;IACT,KAAK,CAAC,oBAAoB,CAAC,CAAS,EAAE,CAAS,EAAE,eAAwB;QAC7E,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;gBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;gBACjD,OAAO;YACX,CAAC;YAED,kEAAkE;YAClE,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,KAAK,IAAI,EAAE;gBAC5C,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;oBAC5B,IAAI,CAAC,eAAgB,CAAC,aAAa,GAAG,IAAW,CAAC;oBAClD,IAAI,UAAU,GAA2B,IAAI,CAAC;oBAC9C,IAAI,iBAAiB,GAAuB,SAAS,CAAC;oBAEtD,8BAA8B;oBAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,eAAgB,CAAC,CAAC;oBACpF,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;wBACb,IAAI,CAAC,YAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;wBACxD,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;oBACvC,CAAC;oBAED,wBAAwB;oBACxB,IAAI,MAAM,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;wBAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;wBAElD,QAAQ;wBACR,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;4BAC3B,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC;4BACnE,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;wBACxD,CAAC;6BAAM,CAAC;4BACJ,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;wBAC5D,CAAC;oBACL,CAAC;oBAED,IAAI,eAAe,EAAE,CAAC;wBAClB,IAAI,CAAC,OAAO,EAAE,CAAC;oBACnB,CAAC;oBAED,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;oBAChC,IAAI,UAAU,EAAE,CAAC;wBACb,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,CAAC,CAAC;oBACxE,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC;gBACL,CAAC;YACL,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED,uBAAuB;IACf,KAAK,CAAC,yBAAyB,CACnC,EAAkB,EAClB,IAAY,EACZ,IAAY,EACZ,QAAgB,EAChB,CAAS,EACT,CAAS,EACT,eAAwB;QAExB,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;gBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;gBACjD,OAAO;YACX,CAAC;YAED,kEAAkE;YAClE,IAAI,CAAC,eAAe,CAAC,aAAa,GAAG,KAAK,IAAI,EAAE;gBAC5C,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE,CAAC;oBAC5B,IAAI,CAAC,eAAgB,CAAC,aAAa,GAAG,IAAW,CAAC;oBAClD,MAAM,YAAY,GAA6B,EAAE,CAAC;oBAClD,MAAM,mBAAmB,GAAa,EAAE,CAAC;oBAEzC,IAAI,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;wBACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;4BACjC,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;4BAC3G,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;4BAC9B,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC,CAAC;wBACrD,CAAC;oBACL,CAAC;oBAED,IAAI,eAAe,EAAE,CAAC;wBAClB,IAAI,CAAC,OAAO,EAAE,CAAC;oBACnB,CAAC;oBAED,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;oBAChC,OAAO,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,CAAC,CAAC;gBAChF,CAAC;YACL,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,cAAc,CAAC,CAAS,EAAE,CAAS,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC;QACrD,IAAK,IAAI,CAAC,OAAiC,CAAC,aAAa,EAAE,CAAC;YACvD,IAAI,CAAC,OAAiC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;IACO,eAAe;QACnB,IAAK,IAAI,CAAC,OAAiC,CAAC,cAAc,EAAE,CAAC;YACxD,IAAI,CAAC,OAAiC,CAAC,cAAc,EAAE,CAAC;QAC7D,CAAC;IACL,CAAC;IAED;;OAEG;IACK,kBAAkB;QACtB,MAAM,cAAc,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QACpD,IAAI,cAAc,EAAE,CAAC;YACjB,8BAA8B;YAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,eAAgB,CAAC,CAAC;YACpF,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,YAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACxD,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACvC,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,OAAO,KAAK,CAAC,CAAC,+BAA+B;IACjD,CAAC;IAEO,0BAA0B,CAAC,CAAS,EAAE,CAAS,EAAE,IAAY,EAAE,IAAY,EAAE,CAAS;QAC1F,IAAI,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAErC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC/B,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAE/B,MAAM,OAAO,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC;QAElE,IAAI,UAAU,GAA2B,IAAI,CAAC;QAC9C,IAAI,iBAAqC,CAAC;QAE1C,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YACd,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3B,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC;gBACnE,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACJ,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC;QAED,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,iBAAiB;QACrB,IAAI,CAAC,eAAgB,CAAC,UAAU,GAAG,EAAE,CAAC;QACtC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACtC,IAAI,CAAC,eAAgB,CAAC,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YACrF,IAAI,CAAC,eAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,CAAS,EAAE,CAAS,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC;QACpE,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;QAC7C,MAAM,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhH,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,4BAA4B;IACrB,OAAO;QACV,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,cAAc;QACd,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,sBAAsB,EAAE,OAAO,EAAE,CAAC;QACvC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACnC,IAAI,CAAC,0BAA0B,EAAE,MAAM,EAAE,CAAC;QAC1C,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;IAC3C,CAAC;;AAxmBc,oBAAU,GAAgB;IACrC,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,CAAC;CACP,AAJwB,CAIvB", "sourcesContent": ["import type { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport { Constants } from \"core/Engines/constants\";\r\nimport type { Engine } from \"core/Engines/engine\";\r\nimport type { WebGPUEngine } from \"core/Engines/webgpuEngine\";\r\nimport { RenderTargetTexture } from \"core/Materials/Textures/renderTargetTexture\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\nimport type { IShaderMaterialOptions } from \"core/Materials/shaderMaterial\";\r\nimport { ShaderMaterial } from \"core/Materials/shaderMaterial\";\r\nimport { Color3, Color4 } from \"core/Maths/math.color\";\r\nimport type { IColor3Like, IVector2Like } from \"core/Maths/math.like\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport { VertexBuffer } from \"core/Meshes/buffer\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport type { InstancedMesh } from \"core/Meshes/instancedMesh\";\r\nimport { Logger } from \"core/Misc/logger\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { Nullable } from \"core/types\";\r\nimport type { Observer } from \"core/Misc/observable\";\r\n\r\n/**\r\n * Class used to store the result of a GPU picking operation\r\n */\r\nexport interface IGPUPickingInfo {\r\n    /**\r\n     * Picked mesh\r\n     */\r\n    mesh: AbstractMesh;\r\n    /**\r\n     * Picked thin instance index\r\n     */\r\n    thinInstanceIndex?: number;\r\n}\r\n\r\n/**\r\n * Stores the result of a multi GPU piciking operation\r\n */\r\nexport interface IGPUMultiPickingInfo {\r\n    /**\r\n     * Picked mesh\r\n     */\r\n    meshes: Nullable<AbstractMesh>[];\r\n    /**\r\n     * Picked thin instance index\r\n     */\r\n    thinInstanceIndexes?: number[];\r\n}\r\n\r\n/**\r\n * Class used to perform a picking operation using GPU\r\n * GPUPIcker can pick meshes, instances and thin instances\r\n */\r\nexport class GPUPicker {\r\n    private _pickingTexture: Nullable<RenderTargetTexture> = null;\r\n    private _idMap: Array<number> = [];\r\n    private _thinIdMap: Array<{ meshId: number; thinId: number }> = [];\r\n    private _idColors: Array<Color3> = [];\r\n    private _cachedScene: Nullable<Scene>;\r\n    private _engine: Nullable<AbstractEngine>;\r\n    private _defaultRenderMaterial: Nullable<ShaderMaterial>;\r\n    private _pickableMeshes: Array<AbstractMesh>;\r\n    private _meshMaterialMap: Map<AbstractMesh, ShaderMaterial> = new Map();\r\n    private _readbuffer: Uint8Array;\r\n    private _meshRenderingCount: number = 0;\r\n    private readonly _attributeName = \"instanceMeshID\";\r\n    private _warningIssued = false;\r\n    private _renderPickingTexture = false;\r\n    private _sceneBeforeRenderObserver: Nullable<Observer<Scene>> = null;\r\n    private _pickingTextureAfterRenderObservable: Nullable<Observer<number>> = null;\r\n\r\n    /** Shader language used by the generator */\r\n    protected _shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    private static _TempColor: IColor3Like = {\r\n        r: 0,\r\n        g: 0,\r\n        b: 0,\r\n    };\r\n\r\n    /**\r\n     * Gets the shader language used in this generator.\r\n     */\r\n    public get shaderLanguage(): ShaderLanguage {\r\n        return this._shaderLanguage;\r\n    }\r\n\r\n    private _pickingInProgress = false;\r\n\r\n    /**\r\n     * Gets a boolean indicating if the picking is in progress\r\n     */\r\n    public get pickingInProgress() {\r\n        return this._pickingInProgress;\r\n    }\r\n\r\n    /**\r\n     * Gets the default render material used by the picker.\r\n     */\r\n    public get defaultRenderMaterial(): Nullable<ShaderMaterial> {\r\n        return this._defaultRenderMaterial;\r\n    }\r\n\r\n    private static _IdToRgb(id: number) {\r\n        GPUPicker._TempColor.r = (id & 0xff0000) >> 16;\r\n        GPUPicker._TempColor.g = (id & 0x00ff00) >> 8;\r\n        GPUPicker._TempColor.b = (id & 0x0000ff) >> 0;\r\n    }\r\n\r\n    private _getColorIdFromReadBuffer(offset: number) {\r\n        const r = this._readbuffer[offset];\r\n        const g = this._readbuffer[offset + 1];\r\n        const b = this._readbuffer[offset + 2];\r\n        return (r << 16) + (g << 8) + b;\r\n    }\r\n\r\n    private static _SetColorData(buffer: Float32Array, i: number, r: number, g: number, b: number) {\r\n        buffer[i] = r / 255.0;\r\n        buffer[i + 1] = g / 255.0;\r\n        buffer[i + 2] = b / 255.0;\r\n        buffer[i + 3] = 1.0;\r\n    }\r\n\r\n    private _createRenderTarget(scene: Scene, width: number, height: number) {\r\n        if (this._cachedScene && this._pickingTexture) {\r\n            const index = this._cachedScene.customRenderTargets.indexOf(this._pickingTexture);\r\n            if (index > -1) {\r\n                this._cachedScene.customRenderTargets.splice(index, 1);\r\n                this._renderPickingTexture = false;\r\n            }\r\n        }\r\n        if (this._pickingTexture) {\r\n            this._pickingTexture.dispose();\r\n        }\r\n        this._pickingTexture = new RenderTargetTexture(\r\n            \"pickingTexure\",\r\n            { width: width, height: height },\r\n            scene,\r\n            false,\r\n            undefined,\r\n            Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n            false,\r\n            Constants.TEXTURE_NEAREST_NEAREST\r\n        );\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/require-await\r\n    private async _createColorMaterialAsync(scene: Scene) {\r\n        if (this._defaultRenderMaterial) {\r\n            this._defaultRenderMaterial.dispose();\r\n        }\r\n\r\n        this._defaultRenderMaterial = null;\r\n\r\n        const engine = scene.getEngine();\r\n\r\n        if (engine.isWebGPU) {\r\n            this._shaderLanguage = ShaderLanguage.WGSL;\r\n        }\r\n\r\n        const defines: string[] = [];\r\n        const options: Partial<IShaderMaterialOptions> = {\r\n            attributes: [VertexBuffer.PositionKind, this._attributeName, \"bakedVertexAnimationSettingsInstanced\"],\r\n            uniforms: [\"world\", \"viewProjection\", \"meshID\"],\r\n            needAlphaBlending: false,\r\n            defines: defines,\r\n            useClipPlane: null,\r\n            shaderLanguage: this._shaderLanguage,\r\n            extraInitializationsAsync: async () => {\r\n                if (this.shaderLanguage === ShaderLanguage.WGSL) {\r\n                    await Promise.all([import(\"../ShadersWGSL/picking.fragment\"), import(\"../ShadersWGSL/picking.vertex\")]);\r\n                } else {\r\n                    await Promise.all([import(\"../Shaders/picking.fragment\"), import(\"../Shaders/picking.vertex\")]);\r\n                }\r\n            },\r\n        };\r\n\r\n        this._defaultRenderMaterial = new ShaderMaterial(\"pickingShader\", scene, \"picking\", options, false);\r\n\r\n        this._defaultRenderMaterial.onBindObservable.add(this._materialBindCallback, undefined, undefined, this);\r\n    }\r\n\r\n    private _materialBindCallback(mesh: AbstractMesh | undefined) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        const material = this._meshMaterialMap.get(mesh)!;\r\n\r\n        if (!material) {\r\n            if (!this._warningIssued) {\r\n                this._warningIssued = true;\r\n                Logger.Warn(\"GPUPicker issue: Mesh not found in the material map. This may happen when the root mesh of an instance is not in the picking list.\");\r\n            }\r\n            return;\r\n        }\r\n\r\n        const effect = material.getEffect();\r\n\r\n        if (!mesh.hasInstances && !mesh.isAnInstance && !mesh.hasThinInstances && this._idColors[mesh.uniqueId] !== undefined) {\r\n            effect.setColor4(\"meshID\", this._idColors[mesh.uniqueId], 1);\r\n        }\r\n\r\n        this._meshRenderingCount++;\r\n    }\r\n\r\n    private _generateColorData(instanceCount: number, id: number, r: number, g: number, b: number, onInstance: (i: number, id: number) => void) {\r\n        const colorData = new Float32Array(4 * (instanceCount + 1));\r\n\r\n        GPUPicker._SetColorData(colorData, 0, r, g, b);\r\n\r\n        for (let i = 0; i < instanceCount; i++) {\r\n            GPUPicker._IdToRgb(id);\r\n\r\n            onInstance(i, id);\r\n            GPUPicker._SetColorData(colorData, (i + 1) * 4, GPUPicker._TempColor.r, GPUPicker._TempColor.g, GPUPicker._TempColor.b);\r\n            id++;\r\n        }\r\n\r\n        return colorData;\r\n    }\r\n\r\n    private _generateThinInstanceColorData(instanceCount: number, id: number, onInstance: (i: number, id: number) => void) {\r\n        const colorData = new Float32Array(4 * instanceCount);\r\n\r\n        for (let i = 0; i < instanceCount; i++) {\r\n            GPUPicker._IdToRgb(id);\r\n\r\n            onInstance(i, id);\r\n            GPUPicker._SetColorData(colorData, i * 4, GPUPicker._TempColor.r, GPUPicker._TempColor.g, GPUPicker._TempColor.b);\r\n\r\n            id++;\r\n        }\r\n\r\n        return colorData;\r\n    }\r\n\r\n    /**\r\n     * Set the list of meshes to pick from\r\n     * Set that value to null to clear the list (and avoid leaks)\r\n     * The module will read and delete from the array provided by reference. Disposing the module or setting the value to null will clear the array.\r\n     * @param list defines the list of meshes to pick from\r\n     */\r\n    public setPickingList(list: Nullable<Array<AbstractMesh | { mesh: AbstractMesh; material: ShaderMaterial }>>) {\r\n        if (this._pickableMeshes) {\r\n            // Cleanup\r\n            for (let index = 0; index < this._pickableMeshes.length; index++) {\r\n                const mesh = this._pickableMeshes[index];\r\n                if (mesh.hasInstances) {\r\n                    (mesh as Mesh).removeVerticesData(this._attributeName);\r\n                }\r\n                if (mesh.hasThinInstances) {\r\n                    (mesh as Mesh).thinInstanceSetBuffer(this._attributeName, null);\r\n                }\r\n                if (this._pickingTexture) {\r\n                    this._pickingTexture.setMaterialForRendering(mesh, undefined);\r\n                }\r\n\r\n                const material = this._meshMaterialMap.get(mesh)!;\r\n                if (material !== this._defaultRenderMaterial) {\r\n                    material.onBindObservable.removeCallback(this._materialBindCallback);\r\n                }\r\n            }\r\n            this._pickableMeshes.length = 0;\r\n            this._meshMaterialMap.clear();\r\n            this._idMap.length = 0;\r\n            this._thinIdMap.length = 0;\r\n            this._idColors.length = 0;\r\n            if (this._pickingTexture) {\r\n                this._pickingTexture.renderList = [];\r\n            }\r\n        }\r\n        if (!list || list.length === 0) {\r\n            return;\r\n        }\r\n\r\n        this._pickableMeshes = list as Array<AbstractMesh>;\r\n\r\n        // Prepare target\r\n        const scene = (\"mesh\" in list[0] ? list[0].mesh : list[0]).getScene();\r\n        const engine = scene.getEngine();\r\n        const rttSizeW = engine.getRenderWidth();\r\n        const rttSizeH = engine.getRenderHeight();\r\n        if (!this._pickingTexture) {\r\n            this._createRenderTarget(scene, rttSizeW, rttSizeH);\r\n        } else {\r\n            const size = this._pickingTexture.getSize();\r\n\r\n            if (size.width !== rttSizeW || size.height !== rttSizeH || this._cachedScene !== scene) {\r\n                this._createRenderTarget(scene, rttSizeW, rttSizeH);\r\n            }\r\n        }\r\n\r\n        this._sceneBeforeRenderObserver?.remove();\r\n        this._sceneBeforeRenderObserver = scene.onBeforeRenderObservable.add(() => {\r\n            if (scene.frameGraph && this._renderPickingTexture && this._cachedScene && this._pickingTexture) {\r\n                this._cachedScene._renderRenderTarget(this._pickingTexture, this._cachedScene.cameras?.[0] ?? null);\r\n                this._cachedScene.activeCamera = null;\r\n            }\r\n        });\r\n\r\n        if (!this._cachedScene || this._cachedScene !== scene) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._createColorMaterialAsync(scene);\r\n        }\r\n\r\n        this._cachedScene = scene;\r\n        this._engine = scene.getEngine();\r\n\r\n        for (let i = 0; i < list.length; i++) {\r\n            const item = list[i];\r\n            if (\"mesh\" in item) {\r\n                this._meshMaterialMap.set(item.mesh, item.material);\r\n                list[i] = item.mesh;\r\n            } else {\r\n                this._meshMaterialMap.set(item, this._defaultRenderMaterial!);\r\n            }\r\n        }\r\n\r\n        this._pickingTexture!.renderList = [];\r\n\r\n        // We will affect colors and create vertex color buffers\r\n        let id = 1;\r\n        for (let index = 0; index < this._pickableMeshes.length; index++) {\r\n            const mesh = this._pickableMeshes[index];\r\n            const material = this._meshMaterialMap.get(mesh)!;\r\n\r\n            if (material !== this._defaultRenderMaterial) {\r\n                material.onBindObservable.add(this._materialBindCallback, undefined, undefined, this);\r\n            }\r\n            this._pickingTexture!.setMaterialForRendering(mesh, material);\r\n            this._pickingTexture!.renderList.push(mesh);\r\n\r\n            if (mesh.isAnInstance) {\r\n                continue; // This will be handled by the source mesh\r\n            }\r\n\r\n            GPUPicker._IdToRgb(id);\r\n\r\n            if (mesh.hasThinInstances) {\r\n                const colorData = this._generateThinInstanceColorData((mesh as Mesh).thinInstanceCount, id, (i, id) => {\r\n                    this._thinIdMap[id] = { meshId: index, thinId: i };\r\n                });\r\n                id += (mesh as Mesh).thinInstanceCount;\r\n                (mesh as Mesh).thinInstanceSetBuffer(this._attributeName, colorData, 4);\r\n            } else {\r\n                this._idMap[id] = index;\r\n                id++;\r\n\r\n                if (mesh.hasInstances) {\r\n                    const instancesForPick = this._pickableMeshes.filter((m) => m.isAnInstance && (m as InstancedMesh).sourceMesh === mesh);\r\n                    const colorData = this._generateColorData(instancesForPick.length, id, GPUPicker._TempColor.r, GPUPicker._TempColor.g, GPUPicker._TempColor.b, (i, id) => {\r\n                        const instance = instancesForPick[i];\r\n                        this._idMap[id] = this._pickableMeshes.indexOf(instance);\r\n                    });\r\n\r\n                    id += instancesForPick.length;\r\n                    const engine = mesh.getEngine();\r\n\r\n                    const buffer = new VertexBuffer(engine, colorData, this._attributeName, false, false, 4, true);\r\n                    (mesh as Mesh).setVerticesBuffer(buffer, true);\r\n                } else {\r\n                    this._idColors[mesh.uniqueId] = Color3.FromInts(GPUPicker._TempColor.r, GPUPicker._TempColor.g, GPUPicker._TempColor.b);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Execute a picking operation\r\n     * @param x defines the X coordinates where to run the pick\r\n     * @param y defines the Y coordinates where to run the pick\r\n     * @param disposeWhenDone defines a boolean indicating we do not want to keep resources alive (false by default)\r\n     * @returns A promise with the picking results\r\n     */\r\n    public async pickAsync(x: number, y: number, disposeWhenDone = false): Promise<Nullable<IGPUPickingInfo>> {\r\n        if (this._pickingInProgress) {\r\n            return null;\r\n        }\r\n\r\n        if (!this._pickableMeshes || this._pickableMeshes.length === 0) {\r\n            return null;\r\n        }\r\n\r\n        const { rttSizeW, rttSizeH, devicePixelRatio } = this._getRenderInfo();\r\n\r\n        const { x: adjustedX, y: adjustedY } = this._prepareForPicking(x, y, devicePixelRatio);\r\n        if (adjustedX < 0 || adjustedY < 0 || adjustedX >= rttSizeW || adjustedY >= rttSizeH) {\r\n            return null;\r\n        }\r\n\r\n        this._pickingInProgress = true;\r\n\r\n        // Invert Y\r\n        const invertedY = rttSizeH - adjustedY - 1;\r\n        this._preparePickingBuffer(this._engine!, rttSizeW, rttSizeH, adjustedX, invertedY);\r\n\r\n        return await this._executePickingAsync(adjustedX, invertedY, disposeWhenDone);\r\n    }\r\n\r\n    /**\r\n     * Execute a picking operation on multiple coordinates\r\n     * @param xy defines the X,Y coordinates where to run the pick\r\n     * @param disposeWhenDone defines a boolean indicating we do not want to keep resources alive (false by default)\r\n     * @returns A promise with the picking results. Always returns an array with the same length as the number of coordinates. The mesh or null at the index where no mesh was picked.\r\n     */\r\n    public async multiPickAsync(xy: IVector2Like[], disposeWhenDone = false): Promise<Nullable<IGPUMultiPickingInfo>> {\r\n        if (this._pickingInProgress) {\r\n            return null;\r\n        }\r\n\r\n        if (!this._pickableMeshes || this._pickableMeshes.length === 0 || xy.length === 0) {\r\n            return null;\r\n        }\r\n\r\n        if (xy.length === 1) {\r\n            const pi = await this.pickAsync(xy[0].x, xy[0].y, disposeWhenDone);\r\n            return {\r\n                meshes: [pi?.mesh ?? null],\r\n                thinInstanceIndexes: pi?.thinInstanceIndex ? [pi.thinInstanceIndex] : undefined,\r\n            };\r\n        }\r\n\r\n        this._pickingInProgress = true;\r\n\r\n        const processedXY = new Array(xy.length);\r\n\r\n        let minX = Infinity;\r\n        let maxX = -Infinity;\r\n        let minY = Infinity;\r\n        let maxY = -Infinity;\r\n\r\n        const { rttSizeW, rttSizeH, devicePixelRatio } = this._getRenderInfo();\r\n\r\n        // Process screen coordinates adjust to dpr\r\n        for (let i = 0; i < xy.length; i++) {\r\n            const item = xy[i];\r\n            const { x, y } = item;\r\n\r\n            const { x: adjustedX, y: adjustedY } = this._prepareForPicking(x, y, devicePixelRatio);\r\n\r\n            processedXY[i] = {\r\n                ...item,\r\n                x: adjustedX,\r\n                y: adjustedY,\r\n            };\r\n\r\n            minX = Math.min(minX, adjustedX);\r\n            maxX = Math.max(maxX, adjustedX);\r\n            minY = Math.min(minY, adjustedY);\r\n            maxY = Math.max(maxY, adjustedY);\r\n        }\r\n\r\n        const w = Math.max(maxX - minX, 1);\r\n        const h = Math.max(maxY - minY, 1);\r\n        const partialCutH = rttSizeH - maxY - 1;\r\n\r\n        this._preparePickingBuffer(this._engine!, rttSizeW, rttSizeH, minX, partialCutH, w, h);\r\n\r\n        return await this._executeMultiPickingAsync(processedXY, minX, maxY, rttSizeH, w, h, disposeWhenDone);\r\n    }\r\n\r\n    private _getRenderInfo() {\r\n        const engine = this._cachedScene!.getEngine();\r\n        const rttSizeW = engine.getRenderWidth();\r\n        const rttSizeH = engine.getRenderHeight();\r\n        const devicePixelRatio = 1 / engine._hardwareScalingLevel;\r\n\r\n        return {\r\n            rttSizeW,\r\n            rttSizeH,\r\n            devicePixelRatio,\r\n        };\r\n    }\r\n\r\n    private _prepareForPicking(x: number, y: number, devicePixelRatio: number) {\r\n        return { x: (devicePixelRatio * x) >> 0, y: (devicePixelRatio * y) >> 0 };\r\n    }\r\n\r\n    private _preparePickingBuffer(engine: AbstractEngine, rttSizeW: number, rttSizeH: number, x: number, y: number, w = 1, h = 1) {\r\n        this._meshRenderingCount = 0;\r\n\r\n        const requiredBufferSize = engine.isWebGPU ? (4 * w * h + 255) & ~255 : 4 * w * h;\r\n        if (!this._readbuffer || this._readbuffer.length < requiredBufferSize) {\r\n            this._readbuffer = new Uint8Array(requiredBufferSize);\r\n        }\r\n\r\n        // Do we need to rebuild the RTT?\r\n        const size = this._pickingTexture!.getSize();\r\n        if (size.width !== rttSizeW || size.height !== rttSizeH) {\r\n            this._createRenderTarget(this._cachedScene!, rttSizeW, rttSizeH);\r\n            this._updateRenderList();\r\n        }\r\n\r\n        this._pickingTexture!.clearColor = new Color4(0, 0, 0, 0);\r\n\r\n        this._pickingTexture!.onBeforeRender = () => {\r\n            this._enableScissor(x, y, w, h);\r\n        };\r\n\r\n        this._pickingTextureAfterRenderObservable?.remove();\r\n        this._pickingTextureAfterRenderObservable = this._pickingTexture!.onAfterRenderObservable.add(() => {\r\n            this._disableScissor();\r\n        });\r\n\r\n        this._cachedScene!.customRenderTargets.push(this._pickingTexture!);\r\n        this._renderPickingTexture = true;\r\n    }\r\n\r\n    // pick one pixel\r\n    private async _executePickingAsync(x: number, y: number, disposeWhenDone: boolean): Promise<Nullable<IGPUPickingInfo>> {\r\n        return await new Promise((resolve, reject) => {\r\n            if (!this._pickingTexture) {\r\n                this._pickingInProgress = false;\r\n                reject(new Error(\"Picking texture not created\"));\r\n                return;\r\n            }\r\n\r\n            // eslint-disable-next-line @typescript-eslint/no-misused-promises\r\n            this._pickingTexture.onAfterRender = async () => {\r\n                if (this._checkRenderStatus()) {\r\n                    this._pickingTexture!.onAfterRender = null as any;\r\n                    let pickedMesh: Nullable<AbstractMesh> = null;\r\n                    let thinInstanceIndex: number | undefined = undefined;\r\n\r\n                    // Remove from the active RTTs\r\n                    const index = this._cachedScene!.customRenderTargets.indexOf(this._pickingTexture!);\r\n                    if (index > -1) {\r\n                        this._cachedScene!.customRenderTargets.splice(index, 1);\r\n                        this._renderPickingTexture = false;\r\n                    }\r\n\r\n                    // Do the actual picking\r\n                    if (await this._readTexturePixelsAsync(x, y)) {\r\n                        const colorId = this._getColorIdFromReadBuffer(0);\r\n\r\n                        // Thin?\r\n                        if (this._thinIdMap[colorId]) {\r\n                            pickedMesh = this._pickableMeshes[this._thinIdMap[colorId].meshId];\r\n                            thinInstanceIndex = this._thinIdMap[colorId].thinId;\r\n                        } else {\r\n                            pickedMesh = this._pickableMeshes[this._idMap[colorId]];\r\n                        }\r\n                    }\r\n\r\n                    if (disposeWhenDone) {\r\n                        this.dispose();\r\n                    }\r\n\r\n                    this._pickingInProgress = false;\r\n                    if (pickedMesh) {\r\n                        resolve({ mesh: pickedMesh, thinInstanceIndex: thinInstanceIndex });\r\n                    } else {\r\n                        resolve(null);\r\n                    }\r\n                }\r\n            };\r\n        });\r\n    }\r\n\r\n    // pick multiple pixels\r\n    private async _executeMultiPickingAsync(\r\n        xy: IVector2Like[],\r\n        minX: number,\r\n        maxY: number,\r\n        rttSizeH: number,\r\n        w: number,\r\n        h: number,\r\n        disposeWhenDone: boolean\r\n    ): Promise<Nullable<IGPUMultiPickingInfo>> {\r\n        return await new Promise((resolve, reject) => {\r\n            if (!this._pickingTexture) {\r\n                this._pickingInProgress = false;\r\n                reject(new Error(\"Picking texture not created\"));\r\n                return;\r\n            }\r\n\r\n            // eslint-disable-next-line @typescript-eslint/no-misused-promises\r\n            this._pickingTexture.onAfterRender = async () => {\r\n                if (this._checkRenderStatus()) {\r\n                    this._pickingTexture!.onAfterRender = null as any;\r\n                    const pickedMeshes: Nullable<AbstractMesh>[] = [];\r\n                    const thinInstanceIndexes: number[] = [];\r\n\r\n                    if (await this._readTexturePixelsAsync(minX, rttSizeH - maxY - 1, w, h)) {\r\n                        for (let i = 0; i < xy.length; i++) {\r\n                            const { pickedMesh, thinInstanceIndex } = this._getMeshFromMultiplePoints(xy[i].x, xy[i].y, minX, maxY, w);\r\n                            pickedMeshes.push(pickedMesh);\r\n                            thinInstanceIndexes.push(thinInstanceIndex ?? 0);\r\n                        }\r\n                    }\r\n\r\n                    if (disposeWhenDone) {\r\n                        this.dispose();\r\n                    }\r\n\r\n                    this._pickingInProgress = false;\r\n                    resolve({ meshes: pickedMeshes, thinInstanceIndexes: thinInstanceIndexes });\r\n                }\r\n            };\r\n        });\r\n    }\r\n\r\n    private _enableScissor(x: number, y: number, w = 1, h = 1) {\r\n        if ((this._engine as WebGPUEngine | Engine).enableScissor) {\r\n            (this._engine as WebGPUEngine | Engine).enableScissor(x, y, w, h);\r\n        }\r\n    }\r\n    private _disableScissor() {\r\n        if ((this._engine as WebGPUEngine | Engine).disableScissor) {\r\n            (this._engine as WebGPUEngine | Engine).disableScissor();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * @returns true if rendering if the picking texture has finished, otherwise false\r\n     */\r\n    private _checkRenderStatus(): boolean {\r\n        const wasSuccessfull = this._meshRenderingCount > 0;\r\n        if (wasSuccessfull) {\r\n            // Remove from the active RTTs\r\n            const index = this._cachedScene!.customRenderTargets.indexOf(this._pickingTexture!);\r\n            if (index > -1) {\r\n                this._cachedScene!.customRenderTargets.splice(index, 1);\r\n                this._renderPickingTexture = false;\r\n            }\r\n            return true;\r\n        }\r\n\r\n        this._meshRenderingCount = 0;\r\n        return false; // Wait for shaders to be ready\r\n    }\r\n\r\n    private _getMeshFromMultiplePoints(x: number, y: number, minX: number, maxY: number, w: number): { pickedMesh: Nullable<AbstractMesh>; thinInstanceIndex: number | undefined } {\r\n        let offsetX = (x - minX - 1) * 4;\r\n        let offsetY = (maxY - y - 1) * w * 4;\r\n\r\n        offsetX = Math.max(offsetX, 0);\r\n        offsetY = Math.max(offsetY, 0);\r\n\r\n        const colorId = this._getColorIdFromReadBuffer(offsetX + offsetY);\r\n\r\n        let pickedMesh: Nullable<AbstractMesh> = null;\r\n        let thinInstanceIndex: number | undefined;\r\n\r\n        if (colorId > 0) {\r\n            if (this._thinIdMap[colorId]) {\r\n                pickedMesh = this._pickableMeshes[this._thinIdMap[colorId].meshId];\r\n                thinInstanceIndex = this._thinIdMap[colorId].thinId;\r\n            } else {\r\n                pickedMesh = this._pickableMeshes[this._idMap[colorId]];\r\n            }\r\n        }\r\n\r\n        return { pickedMesh, thinInstanceIndex };\r\n    }\r\n\r\n    /**\r\n     * Updates the render list with the current pickable meshes.\r\n     */\r\n    private _updateRenderList() {\r\n        this._pickingTexture!.renderList = [];\r\n        for (const mesh of this._pickableMeshes) {\r\n            this._pickingTexture!.setMaterialForRendering(mesh, this._meshMaterialMap.get(mesh));\r\n            this._pickingTexture!.renderList.push(mesh);\r\n        }\r\n    }\r\n\r\n    private async _readTexturePixelsAsync(x: number, y: number, w = 1, h = 1) {\r\n        if (!this._cachedScene || !this._pickingTexture?._texture) {\r\n            return false;\r\n        }\r\n        const engine = this._cachedScene.getEngine();\r\n        await engine._readTexturePixels(this._pickingTexture._texture, w, h, -1, 0, this._readbuffer, true, true, x, y);\r\n\r\n        return true;\r\n    }\r\n\r\n    /** Release the resources */\r\n    public dispose() {\r\n        this.setPickingList(null);\r\n        this._cachedScene = null;\r\n\r\n        // Cleaning up\r\n        this._pickingTexture?.dispose();\r\n        this._pickingTexture = null;\r\n        this._defaultRenderMaterial?.dispose();\r\n        this._defaultRenderMaterial = null;\r\n        this._sceneBeforeRenderObserver?.remove();\r\n        this._sceneBeforeRenderObserver = null;\r\n    }\r\n}\r\n"]}