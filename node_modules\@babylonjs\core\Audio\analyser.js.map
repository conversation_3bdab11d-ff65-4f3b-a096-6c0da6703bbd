{"version": 3, "file": "analyser.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Audio/analyser.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,cAAc,EAAE,qCAAoC;AAE7D;;;GAGG;AACH,MAAM,OAAO,QAAQ;IAgCjB;;;OAGG;IACH,YAAY,KAAuB;QAnCnC;;WAEG;QACI,cAAS,GAAG,IAAI,CAAC;QACxB;;WAEG;QACI,aAAQ,GAAG,GAAG,CAAC;QACtB;;WAEG;QACI,sBAAiB,GAAG,GAAG,CAAC;QAC/B;;WAEG;QACI,mBAAc,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;QACzC;;WAEG;QACI,oBAAe,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;QAiBjD,KAAK,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;YAC9E,OAAO;QACX,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,WAAW,CAAC;QAC/C,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YACrE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACzE,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC;YAC1C,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,CAAC;YACvC,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAC3E,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;YAC1E,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAClF,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,oBAAoB;QACvB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;QACpD,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,CAAC;QACb,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,oBAAoB;QACvB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9D,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACI,qBAAqB;QACxB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9D,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACI,qBAAqB;QACxB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9D,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACnE,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrB,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBACvD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAC9C,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC3D,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC5D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC9D,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC7C,IAAI,CAAC,aAAa,GAAG,GAAG,EAAE;oBACtB,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC3B,CAAC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzD,CAAC;YACD,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACjD,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAEjD,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,cAAc,CAAC;gBACpD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;gBAEjG,mCAAmC;gBACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;oBACnD,MAAM,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;oBAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,OAAO,CAAC;oBACrD,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;oBACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC1E,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC,GAAG,GAAG,CAAC;oBACpD,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,MAAM,GAAG,GAAG,GAAG,cAAc,CAAC;oBACnE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,GAAG,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAC9E,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBACvD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC9B,CAAC;YACD,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,cAAyB,EAAE,eAA0B;QAC1E,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC/C,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACpD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC;QACxC,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { IAudioEngine } from \"./Interfaces/IAudioEngine\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { AbstractEngine } from \"core/Engines/abstractEngine\";\r\n\r\n/**\r\n * Class used to work with sound analyzer using fast fourier transform (FFT)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport class Analyser {\r\n    /**\r\n     * Gets or sets the smoothing\r\n     */\r\n    public SMOOTHING = 0.75;\r\n    /**\r\n     * Gets or sets the FFT table size\r\n     */\r\n    public FFT_SIZE = 512;\r\n    /**\r\n     * Gets or sets the bar graph amplitude\r\n     */\r\n    public BARGRAPHAMPLITUDE = 256;\r\n    /**\r\n     * Gets or sets the position of the debug canvas\r\n     */\r\n    public DEBUGCANVASPOS = { x: 20, y: 20 };\r\n    /**\r\n     * Gets or sets the debug canvas size\r\n     */\r\n    public DEBUGCANVASSIZE = { width: 320, height: 200 };\r\n\r\n    private _byteFreqs: Uint8Array;\r\n    private _byteTime: Uint8Array;\r\n    private _floatFreqs: Float32Array;\r\n    private _webAudioAnalyser: AnalyserNode;\r\n    private _debugCanvas: Nullable<HTMLCanvasElement>;\r\n    private _debugCanvasContext: Nullable<CanvasRenderingContext2D>;\r\n    private _scene: Scene;\r\n    private _registerFunc: Nullable<() => void>;\r\n    private _audioEngine: IAudioEngine;\r\n\r\n    /**\r\n     * Creates a new analyser\r\n     * @param scene defines hosting scene\r\n     */\r\n    constructor(scene?: Nullable<Scene>) {\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this._scene = scene;\r\n        if (!AbstractEngine.audioEngine) {\r\n            Tools.Warn(\"No audio engine initialized, failed to create an audio analyser\");\r\n            return;\r\n        }\r\n        this._audioEngine = AbstractEngine.audioEngine;\r\n        if (this._audioEngine.canUseWebAudio && this._audioEngine.audioContext) {\r\n            this._webAudioAnalyser = this._audioEngine.audioContext.createAnalyser();\r\n            this._webAudioAnalyser.minDecibels = -140;\r\n            this._webAudioAnalyser.maxDecibels = 0;\r\n            this._byteFreqs = new Uint8Array(this._webAudioAnalyser.frequencyBinCount);\r\n            this._byteTime = new Uint8Array(this._webAudioAnalyser.frequencyBinCount);\r\n            this._floatFreqs = new Float32Array(this._webAudioAnalyser.frequencyBinCount);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get the number of data values you will have to play with for the visualization\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/frequencyBinCount\r\n     * @returns a number\r\n     */\r\n    public getFrequencyBinCount(): number {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            return this._webAudioAnalyser.frequencyBinCount;\r\n        } else {\r\n            return 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the current frequency data as a byte array\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/getByteFrequencyData\r\n     * @returns a Uint8Array\r\n     */\r\n    public getByteFrequencyData(): Uint8Array {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            this._webAudioAnalyser.smoothingTimeConstant = this.SMOOTHING;\r\n            this._webAudioAnalyser.fftSize = this.FFT_SIZE;\r\n            this._webAudioAnalyser.getByteFrequencyData(this._byteFreqs);\r\n        }\r\n        return this._byteFreqs;\r\n    }\r\n\r\n    /**\r\n     * Gets the current waveform as a byte array\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/getByteTimeDomainData\r\n     * @returns a Uint8Array\r\n     */\r\n    public getByteTimeDomainData(): Uint8Array {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            this._webAudioAnalyser.smoothingTimeConstant = this.SMOOTHING;\r\n            this._webAudioAnalyser.fftSize = this.FFT_SIZE;\r\n            this._webAudioAnalyser.getByteTimeDomainData(this._byteTime);\r\n        }\r\n        return this._byteTime;\r\n    }\r\n\r\n    /**\r\n     * Gets the current frequency data as a float array\r\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/getByteFrequencyData\r\n     * @returns a Float32Array\r\n     */\r\n    public getFloatFrequencyData(): Float32Array {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            this._webAudioAnalyser.smoothingTimeConstant = this.SMOOTHING;\r\n            this._webAudioAnalyser.fftSize = this.FFT_SIZE;\r\n            this._webAudioAnalyser.getFloatFrequencyData(this._floatFreqs);\r\n        }\r\n        return this._floatFreqs;\r\n    }\r\n\r\n    /**\r\n     * Renders the debug canvas\r\n     */\r\n    public drawDebugCanvas() {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            if (!this._debugCanvas) {\r\n                this._debugCanvas = document.createElement(\"canvas\");\r\n                this._debugCanvas.width = this.DEBUGCANVASSIZE.width;\r\n                this._debugCanvas.height = this.DEBUGCANVASSIZE.height;\r\n                this._debugCanvas.style.position = \"absolute\";\r\n                this._debugCanvas.style.top = this.DEBUGCANVASPOS.y + \"px\";\r\n                this._debugCanvas.style.left = this.DEBUGCANVASPOS.x + \"px\";\r\n                this._debugCanvasContext = this._debugCanvas.getContext(\"2d\");\r\n                document.body.appendChild(this._debugCanvas);\r\n                this._registerFunc = () => {\r\n                    this.drawDebugCanvas();\r\n                };\r\n                this._scene.registerBeforeRender(this._registerFunc);\r\n            }\r\n            if (this._registerFunc && this._debugCanvasContext) {\r\n                const workingArray = this.getByteFrequencyData();\r\n\r\n                this._debugCanvasContext.fillStyle = \"rgb(0, 0, 0)\";\r\n                this._debugCanvasContext.fillRect(0, 0, this.DEBUGCANVASSIZE.width, this.DEBUGCANVASSIZE.height);\r\n\r\n                // Draw the frequency domain chart.\r\n                for (let i = 0; i < this.getFrequencyBinCount(); i++) {\r\n                    const value = workingArray[i];\r\n                    const percent = value / this.BARGRAPHAMPLITUDE;\r\n                    const height = this.DEBUGCANVASSIZE.height * percent;\r\n                    const offset = this.DEBUGCANVASSIZE.height - height - 1;\r\n                    const barWidth = this.DEBUGCANVASSIZE.width / this.getFrequencyBinCount();\r\n                    const hue = (i / this.getFrequencyBinCount()) * 360;\r\n                    this._debugCanvasContext.fillStyle = \"hsl(\" + hue + \", 100%, 50%)\";\r\n                    this._debugCanvasContext.fillRect(i * barWidth, offset, barWidth, height);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stops rendering the debug canvas and removes it\r\n     */\r\n    public stopDebugCanvas() {\r\n        if (this._debugCanvas) {\r\n            if (this._registerFunc) {\r\n                this._scene.unregisterBeforeRender(this._registerFunc);\r\n                this._registerFunc = null;\r\n            }\r\n            document.body.removeChild(this._debugCanvas);\r\n            this._debugCanvas = null;\r\n            this._debugCanvasContext = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connects two audio nodes\r\n     * @param inputAudioNode defines first node to connect\r\n     * @param outputAudioNode defines second node to connect\r\n     */\r\n    public connectAudioNodes(inputAudioNode: AudioNode, outputAudioNode: AudioNode) {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            inputAudioNode.connect(this._webAudioAnalyser);\r\n            this._webAudioAnalyser.connect(outputAudioNode);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Releases all associated resources\r\n     */\r\n    public dispose() {\r\n        if (this._audioEngine.canUseWebAudio) {\r\n            this._webAudioAnalyser.disconnect();\r\n        }\r\n    }\r\n}\r\n"]}