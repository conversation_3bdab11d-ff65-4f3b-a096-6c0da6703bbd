{"version": 3, "file": "abstractAudioSubGraph.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/abstractAudio/subNodes/abstractAudioSubGraph.ts"], "names": [], "mappings": "AAKA;;;;;;;;;;;;;;GAcG;AACH,MAAM,OAAgB,sBAAsB;IAA5C;QACY,2BAAsB,GAAsD,EAAE,CAAC;QAC/E,gBAAW,GAAG,KAAK,CAAC;QACpB,cAAS,GAA6C,EAAE,CAAC;QA8HzD,uBAAkB,GAAG,CAAC,IAAuB,EAAE,EAAE;YACrD,MAAM,OAAO,GAAG,IAA8B,CAAC;YAE/C,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEpC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC,CAAC;IACN,CAAC;IAnIG;;;;;;;OAOG;IACI,aAAa,CAAkC,IAAkB,EAAE,QAA2B;QACjG,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,IAAI,EAAE,CAAC;YACP,QAAQ,CAAC,IAAS,CAAC,CAAC;YACpB,OAAO;QACX,CAAC;QAED,mFAAmF;QACnF,IAAI,CAAC,mCAAmC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YACjD,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,IAAI,EAAE,CAAC;gBACP,QAAQ,CAAC,IAAS,CAAC,CAAC;gBACpB,OAAO;YACX,CAAC;YAED,mFAAmF;YACnF,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBAC9C,QAAQ,CAAC,IAAS,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;OAOG;IACH,2FAA2F;IACpF,wBAAwB,CAAC,IAAkB;;QAC9C,0CAA0C;QAC1C,MAAA,IAAI,CAAC,sBAAsB,EAAC,IAAI,SAAJ,IAAI,IAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1E,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,EAAC;QAEH,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACI,OAAO;QACV,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,OAAO,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;IACrC,CAAC;IAED;;;;;;;SAOK;IACE,UAAU,CAAkC,IAAY;QAC3D,OAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAO,IAAI,IAAI,CAAC;IAC/C,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,kBAAkB,CAAC,OAA8B;QAC1D,MAAM,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAEjD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAEzC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAUS,KAAK,CAAC,mCAAmC;QAC/C,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC;IACzE,CAAC;IAEO,WAAW,CAAC,IAA2B;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAEjC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAE1D,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;CASJ", "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport type { AbstractAudioNode, AbstractNamedAudioNode } from \"../abstractAudioNode\";\nimport type { _AbstractAudioSubNode } from \"./abstractAudioSubNode\";\nimport type { AudioSubNode } from \"./audioSubNode\";\n\n/**\n * Adds common sub graph functionality to an audio node.\n *\n * Audio nodes such as static sounds, streaming sounds, and buses can use audio sub graphs to process audio internally\n * before sending it to connected downstream audio nodes. This is useful for applying effects, spatial audio, and other\n * audio processing tasks common to multiple audio node classes.\n *\n * A key feature of audio sub graphs is their audio sub nodes are created asynchronously on demand so the minimum set\n * of sub nodes are used at all times to save memory and CPU resources. The tradeoff is a small delay when first\n * setting a property backed by a sub node. This delay is avoided by using the appropriate options to initialize the\n * sub node on creation, e.g. `spatialEnabled` and `stereoEnabled`, or by setting any creation option backed by the\n * sub node, e.g. `spatialPosition` and `stereoPan`.\n *\n * @internal\n */\nexport abstract class _AbstractAudioSubGraph {\n    private _createSubNodePromises: { [key: string]: Promise<_AbstractAudioSubNode> } = {};\n    private _isDisposed = false;\n    private _subNodes: { [key: string]: _AbstractAudioSubNode } = {};\n\n    /**\n     * Executes the given callback with the named sub node, creating the sub node if needed.\n     *\n     * @param name The name of the sub node\n     * @param callback The function to call with the named sub node\n     *\n     * @internal\n     */\n    public callOnSubNode<T extends _AbstractAudioSubNode>(name: AudioSubNode, callback: (node: T) => void): void {\n        const node = this.getSubNode(name);\n        if (node) {\n            callback(node as T);\n            return;\n        }\n\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\n        this._createSubNodePromisesResolvedAsync().then(() => {\n            const node = this.getSubNode(name);\n            if (node) {\n                callback(node as T);\n                return;\n            }\n\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\n            this.createAndAddSubNodeAsync(name).then((node) => {\n                callback(node as T);\n            });\n        });\n    }\n\n    /**\n     * Creates the named subnode and adds it to the sub graph.\n     *\n     * @param name The name of the sub node.\n     * @returns A promise that resolves to the created sub node.\n     *\n     * @internal\n     */\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\n    public createAndAddSubNodeAsync(name: AudioSubNode): Promise<_AbstractAudioSubNode> {\n        // eslint-disable-next-line github/no-then\n        this._createSubNodePromises[name] ||= this._createSubNode(name).then((node) => {\n            this._addSubNode(node);\n            return node;\n        });\n\n        return this._createSubNodePromises[name];\n    }\n\n    /**\n     * Releases associated resources.\n     *\n     * @internal\n     */\n    public dispose() {\n        this._isDisposed = true;\n\n        const subNodes = Object.values(this._subNodes);\n        for (const subNode of subNodes) {\n            subNode.dispose();\n        }\n\n        this._subNodes = {};\n        this._createSubNodePromises = {};\n    }\n\n    /**\n     * Gets a previously created sub node.\n     *\n     * @param name - The name of the sub node\n     * @returns The named sub node, or `null` if it has not been created, yet\n     *\n     * @internal\n     * */\n    public getSubNode<T extends _AbstractAudioSubNode>(name: string): Nullable<T> {\n        return (this._subNodes[name] as T) ?? null;\n    }\n\n    /**\n     * Removes a sub node from the sub graph.\n     *\n     * @param subNode - The sub node to remove\n     * @returns A promise that resolves when the sub node is removed\n     *\n     * @internal\n     */\n    public async removeSubNodeAsync(subNode: _AbstractAudioSubNode): Promise<void> {\n        await this._createSubNodePromisesResolvedAsync();\n\n        const name = subNode.name;\n        if (this._subNodes[name]) {\n            delete this._subNodes[name];\n        }\n\n        delete this._createSubNodePromises[name];\n\n        this._onSubNodesChanged();\n    }\n\n    protected abstract _createSubNode(name: string): Promise<_AbstractAudioSubNode>;\n\n    /**\n     * Called when sub-nodes are added or removed.\n     * - Override this to connect and reconnect sub-nodes as needed.\n     */\n    protected abstract _onSubNodesChanged(): void;\n\n    protected async _createSubNodePromisesResolvedAsync(): Promise<_AbstractAudioSubNode[]> {\n        return await Promise.all(Object.values(this._createSubNodePromises));\n    }\n\n    private _addSubNode(node: _AbstractAudioSubNode): void {\n        if (this._isDisposed) {\n            node.dispose();\n            return;\n        }\n\n        this._subNodes[node.name] = node;\n\n        node.onDisposeObservable.addOnce(this._onSubNodeDisposed);\n\n        this._onSubNodesChanged();\n    }\n\n    private _onSubNodeDisposed = (node: AbstractAudioNode) => {\n        const subNode = node as AbstractNamedAudioNode;\n\n        delete this._subNodes[subNode.name];\n\n        this._onSubNodesChanged();\n    };\n}\n"]}