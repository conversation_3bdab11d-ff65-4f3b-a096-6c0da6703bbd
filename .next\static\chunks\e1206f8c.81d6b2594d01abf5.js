"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[216],{8848:(e,t,s)=>{s.d(t,{Z:()=>k});var r,i=s(94500),a=s(89803),n=s(89048),o=s(97907),h=s(99300),l=s(28488),d=s(1743),c=s(18783),u=s(11347),g=s(76121),m=s(77498),f=s(36470),_=s(14262),v=s(12663),b=s(87764),p=s(18671),M=s(35047),C=s(32467),y=s(40598),R=s(98595),P=s(7306),O=s(26740),A=s(45874),S=s(96775),w=s(44257),T=s(53812),B=s(93625),I=s(20482);!function(e){e[e.BackwardCompatible=0]="BackwardCompatible",e[e.Intermediate=1]="Intermediate",e[e.Aggressive=2]="Aggressive"}(r||(r={}));class k{static DefaultMaterialFactory(e){throw(0,M.n)("StandardMaterial")}static CollisionCoordinatorFactory(){throw(0,M.n)("DefaultCollisionCoordinator")}get clearColor(){return this._clearColor}set clearColor(e){e!==this._clearColor&&(this._clearColor=e,this.onClearColorChangedObservable.notifyObservers(this._clearColor))}get imageProcessingConfiguration(){return this._imageProcessingConfiguration}get performancePriority(){return this._performancePriority}set performancePriority(e){if(e!==this._performancePriority){switch(this._performancePriority=e,e){case 0:this.skipFrustumClipping=!1,this._renderingManager.maintainStateBetweenFrames=!1,this.skipPointerMovePicking=!1,this.autoClear=!0;break;case 1:this.skipFrustumClipping=!1,this._renderingManager.maintainStateBetweenFrames=!1,this.skipPointerMovePicking=!0,this.autoClear=!1;break;case 2:this.skipFrustumClipping=!0,this._renderingManager.maintainStateBetweenFrames=!0,this.skipPointerMovePicking=!0,this.autoClear=!1}this.onScenePerformancePriorityChangedObservable.notifyObservers(e)}}set forceWireframe(e){this._forceWireframe!==e&&(this._forceWireframe=e,this.markAllMaterialsAsDirty(16))}get forceWireframe(){return this._forceWireframe}set skipFrustumClipping(e){this._skipFrustumClipping!==e&&(this._skipFrustumClipping=e)}get skipFrustumClipping(){return this._skipFrustumClipping}set forcePointsCloud(e){this._forcePointsCloud!==e&&(this._forcePointsCloud=e,this.markAllMaterialsAsDirty(16))}get forcePointsCloud(){return this._forcePointsCloud}get environmentTexture(){return this._environmentTexture}set environmentTexture(e){this._environmentTexture!==e&&(this._environmentTexture=e,this.onEnvironmentTextureChangedObservable.notifyObservers(e),this.markAllMaterialsAsDirty(1))}getNodes(){let e=[];for(let t of(e=(e=(e=(e=e.concat(this.meshes)).concat(this.lights)).concat(this.cameras)).concat(this.transformNodes),this.skeletons))e=e.concat(t.bones);return e}get animationPropertiesOverride(){return this._animationPropertiesOverride}set animationPropertiesOverride(e){this._animationPropertiesOverride=e}set onDispose(e){this._onDisposeObserver&&this.onDisposeObservable.remove(this._onDisposeObserver),this._onDisposeObserver=this.onDisposeObservable.add(e)}set beforeRender(e){this._onBeforeRenderObserver&&this.onBeforeRenderObservable.remove(this._onBeforeRenderObserver),e&&(this._onBeforeRenderObserver=this.onBeforeRenderObservable.add(e))}set afterRender(e){this._onAfterRenderObserver&&this.onAfterRenderObservable.remove(this._onAfterRenderObserver),e&&(this._onAfterRenderObserver=this.onAfterRenderObservable.add(e))}set beforeCameraRender(e){this._onBeforeCameraRenderObserver&&this.onBeforeCameraRenderObservable.remove(this._onBeforeCameraRenderObserver),this._onBeforeCameraRenderObserver=this.onBeforeCameraRenderObservable.add(e)}set afterCameraRender(e){this._onAfterCameraRenderObserver&&this.onAfterCameraRenderObservable.remove(this._onAfterCameraRenderObserver),this._onAfterCameraRenderObserver=this.onAfterCameraRenderObservable.add(e)}get pointerDownPredicate(){return this._pointerPickingConfiguration.pointerDownPredicate}set pointerDownPredicate(e){this._pointerPickingConfiguration.pointerDownPredicate=e}get pointerUpPredicate(){return this._pointerPickingConfiguration.pointerUpPredicate}set pointerUpPredicate(e){this._pointerPickingConfiguration.pointerUpPredicate=e}get pointerMovePredicate(){return this._pointerPickingConfiguration.pointerMovePredicate}set pointerMovePredicate(e){this._pointerPickingConfiguration.pointerMovePredicate=e}get pointerDownFastCheck(){return this._pointerPickingConfiguration.pointerDownFastCheck}set pointerDownFastCheck(e){this._pointerPickingConfiguration.pointerDownFastCheck=e}get pointerUpFastCheck(){return this._pointerPickingConfiguration.pointerUpFastCheck}set pointerUpFastCheck(e){this._pointerPickingConfiguration.pointerUpFastCheck=e}get pointerMoveFastCheck(){return this._pointerPickingConfiguration.pointerMoveFastCheck}set pointerMoveFastCheck(e){this._pointerPickingConfiguration.pointerMoveFastCheck=e}get skipPointerMovePicking(){return this._pointerPickingConfiguration.skipPointerMovePicking}set skipPointerMovePicking(e){this._pointerPickingConfiguration.skipPointerMovePicking=e}get skipPointerDownPicking(){return this._pointerPickingConfiguration.skipPointerDownPicking}set skipPointerDownPicking(e){this._pointerPickingConfiguration.skipPointerDownPicking=e}get skipPointerUpPicking(){return this._pointerPickingConfiguration.skipPointerUpPicking}set skipPointerUpPicking(e){this._pointerPickingConfiguration.skipPointerUpPicking=e}get unTranslatedPointer(){return this._inputManager.unTranslatedPointer}static get DragMovementThreshold(){return C.e.DragMovementThreshold}static set DragMovementThreshold(e){C.e.DragMovementThreshold=e}static get LongPressDelay(){return C.e.LongPressDelay}static set LongPressDelay(e){C.e.LongPressDelay=e}static get DoubleClickDelay(){return C.e.DoubleClickDelay}static set DoubleClickDelay(e){C.e.DoubleClickDelay=e}static get ExclusiveDoubleClickMode(){return C.e.ExclusiveDoubleClickMode}static set ExclusiveDoubleClickMode(e){C.e.ExclusiveDoubleClickMode=e}bindEyePosition(e,t="vEyePosition",s=!1){let r=this._forcedViewPosition?this._forcedViewPosition:this._mirroredCameraPosition?this._mirroredCameraPosition:this.activeCamera?.globalPosition??d.Pq.ZeroReadOnly,i=this.useRightHandedSystem===(null!=this._mirroredCameraPosition);return d.AA.Vector4[0].set(r.x,r.y,r.z,i?-1:1),e&&(s?e.setFloat3(t,d.AA.Vector4[0].x,d.AA.Vector4[0].y,d.AA.Vector4[0].z):e.setVector4(t,d.AA.Vector4[0])),d.AA.Vector4[0]}finalizeSceneUbo(){let e=this.getSceneUniformBuffer(),t=this.bindEyePosition(null);return e.updateFloat4("vEyePosition",t.x,t.y,t.z,t.w),e.update(),e}set useRightHandedSystem(e){this._useRightHandedSystem!==e&&(this._useRightHandedSystem=e,this.markAllMaterialsAsDirty(16))}get useRightHandedSystem(){return this._useRightHandedSystem}setStepId(e){this._currentStepId=e}getStepId(){return this._currentStepId}getInternalStep(){return this._currentInternalStep}set fogEnabled(e){this._fogEnabled!==e&&(this._fogEnabled=e,this.markAllMaterialsAsDirty(16))}get fogEnabled(){return this._fogEnabled}set fogMode(e){this._fogMode!==e&&(this._fogMode=e,this.markAllMaterialsAsDirty(16))}get fogMode(){return this._fogMode}get prePass(){return!!this.prePassRenderer&&this.prePassRenderer.defaultRT.enabled}set shadowsEnabled(e){this._shadowsEnabled!==e&&(this._shadowsEnabled=e,this.markAllMaterialsAsDirty(2))}get shadowsEnabled(){return this._shadowsEnabled}set lightsEnabled(e){this._lightsEnabled!==e&&(this._lightsEnabled=e,this.markAllMaterialsAsDirty(2))}get lightsEnabled(){return this._lightsEnabled}get activeCameras(){return this._activeCameras}set activeCameras(e){this._unObserveActiveCameras&&(this._unObserveActiveCameras(),this._unObserveActiveCameras=null),e&&(this._unObserveActiveCameras=(0,w.lL)(e,()=>{this.onActiveCamerasChanged.notifyObservers(this)})),this._activeCameras=e}get activeCamera(){return this._activeCamera}set activeCamera(e){e!==this._activeCamera&&(this._activeCamera=e,this.onActiveCameraChanged.notifyObservers(this))}get _hasDefaultMaterial(){return k.DefaultMaterialFactory!==k._OriginalDefaultMaterialFactory}get defaultMaterial(){return this._defaultMaterial||(this._defaultMaterial=k.DefaultMaterialFactory(this)),this._defaultMaterial}set defaultMaterial(e){this._defaultMaterial=e}set texturesEnabled(e){this._texturesEnabled!==e&&(this._texturesEnabled=e,this.markAllMaterialsAsDirty(1))}get texturesEnabled(){return this._texturesEnabled}get frameGraph(){return this._frameGraph}set frameGraph(e){if(this._frameGraph){this._frameGraph=e,e||(this.customRenderFunction=this._currentCustomRenderFunction);return}this._frameGraph=e,e&&(this._currentCustomRenderFunction=this.customRenderFunction,this.customRenderFunction=this._renderWithFrameGraph,this.activeCamera=null)}set skeletonsEnabled(e){this._skeletonsEnabled!==e&&(this._skeletonsEnabled=e,this.markAllMaterialsAsDirty(8))}get skeletonsEnabled(){return this._skeletonsEnabled}get collisionCoordinator(){return this._collisionCoordinator||(this._collisionCoordinator=k.CollisionCoordinatorFactory(),this._collisionCoordinator.init(this)),this._collisionCoordinator}get renderingManager(){return this._renderingManager}get frustumPlanes(){return this._frustumPlanes}_registerTransientComponents(){if(this._transientComponents.length>0){for(let e of this._transientComponents)e.register();this._transientComponents.length=0}}_addComponent(e){this._components.push(e),this._transientComponents.push(e),e.addFromContainer&&e.serialize&&this._serializableComponents.push(e)}_getComponent(e){for(let t of this._components)if(t.name===e)return t;return null}get uniqueId(){return this._uniqueId}constructor(e,t){this._inputManager=new C.e(this),this.cameraToUseForPointers=null,this._isScene=!0,this._blockEntityCollection=!1,this.autoClear=!0,this.autoClearDepthAndStencil=!0,this._clearColor=new R.ov(.2,.2,.3,1),this.onClearColorChangedObservable=new n.cP,this.ambientColor=new R.v9(0,0,0),this.environmentIntensity=1,this.iblIntensity=1,this._performancePriority=0,this.onScenePerformancePriorityChangedObservable=new n.cP,this._forceWireframe=!1,this._skipFrustumClipping=!1,this._forcePointsCloud=!1,this.rootNodes=[],this.cameras=[],this.lights=[],this.meshes=[],this.skeletons=[],this.particleSystems=[],this.animations=[],this.animationGroups=[],this.multiMaterials=[],this.materials=[],this.morphTargetManagers=[],this.geometries=[],this.transformNodes=[],this.actionManagers=[],this.textures=[],this._environmentTexture=null,this.postProcesses=[],this.effectLayers=[],this.sounds=null,this.layers=[],this.lensFlareSystems=[],this.proceduralTextures=[],this.animationsEnabled=!0,this._animationPropertiesOverride=null,this.useConstantAnimationDeltaTime=!1,this.constantlyUpdateMeshUnderPointer=!1,this.hoverCursor="pointer",this.defaultCursor="",this.doNotHandleCursors=!1,this.preventDefaultOnPointerDown=!0,this.preventDefaultOnPointerUp=!0,this.metadata=null,this.reservedDataStore=null,this.disableOfflineSupportExceptionRules=[],this.onDisposeObservable=new n.cP,this._onDisposeObserver=null,this.onBeforeRenderObservable=new n.cP,this._onBeforeRenderObserver=null,this.onAfterRenderObservable=new n.cP,this.onAfterRenderCameraObservable=new n.cP,this._onAfterRenderObserver=null,this.onBeforeAnimationsObservable=new n.cP,this.onAfterAnimationsObservable=new n.cP,this.onBeforeDrawPhaseObservable=new n.cP,this.onAfterDrawPhaseObservable=new n.cP,this.onReadyObservable=new n.cP,this.onBeforeCameraRenderObservable=new n.cP,this._onBeforeCameraRenderObserver=null,this.onAfterCameraRenderObservable=new n.cP,this._onAfterCameraRenderObserver=null,this.onBeforeActiveMeshesEvaluationObservable=new n.cP,this.onAfterActiveMeshesEvaluationObservable=new n.cP,this.onBeforeParticlesRenderingObservable=new n.cP,this.onAfterParticlesRenderingObservable=new n.cP,this.onDataLoadedObservable=new n.cP,this.onNewCameraAddedObservable=new n.cP,this.onCameraRemovedObservable=new n.cP,this.onNewLightAddedObservable=new n.cP,this.onLightRemovedObservable=new n.cP,this.onNewGeometryAddedObservable=new n.cP,this.onGeometryRemovedObservable=new n.cP,this.onNewTransformNodeAddedObservable=new n.cP,this.onTransformNodeRemovedObservable=new n.cP,this.onNewMeshAddedObservable=new n.cP,this.onMeshRemovedObservable=new n.cP,this.onNewSkeletonAddedObservable=new n.cP,this.onSkeletonRemovedObservable=new n.cP,this.onNewParticleSystemAddedObservable=new n.cP,this.onParticleSystemRemovedObservable=new n.cP,this.onNewAnimationGroupAddedObservable=new n.cP,this.onAnimationGroupRemovedObservable=new n.cP,this.onNewMaterialAddedObservable=new n.cP,this.onNewMultiMaterialAddedObservable=new n.cP,this.onMaterialRemovedObservable=new n.cP,this.onMultiMaterialRemovedObservable=new n.cP,this.onNewTextureAddedObservable=new n.cP,this.onTextureRemovedObservable=new n.cP,this.onNewFrameGraphAddedObservable=new n.cP,this.onFrameGraphRemovedObservable=new n.cP,this.onNewPostProcessAddedObservable=new n.cP,this.onPostProcessRemovedObservable=new n.cP,this.onNewEffectLayerAddedObservable=new n.cP,this.onEffectLayerRemovedObservable=new n.cP,this.onBeforeRenderTargetsRenderObservable=new n.cP,this.onAfterRenderTargetsRenderObservable=new n.cP,this.onBeforeStepObservable=new n.cP,this.onAfterStepObservable=new n.cP,this.onActiveCameraChanged=new n.cP,this.onActiveCamerasChanged=new n.cP,this.onBeforeRenderingGroupObservable=new n.cP,this.onAfterRenderingGroupObservable=new n.cP,this.onMeshImportedObservable=new n.cP,this.onAnimationFileImportedObservable=new n.cP,this.onEnvironmentTextureChangedObservable=new n.cP,this.onMeshUnderPointerUpdatedObservable=new n.cP,this._registeredForLateAnimationBindings=new o.b(256),this._pointerPickingConfiguration=new T.d,this.onPrePointerObservable=new n.cP,this.onPointerObservable=new n.cP,this.onPreKeyboardObservable=new n.cP,this.onKeyboardObservable=new n.cP,this._useRightHandedSystem=!1,this._timeAccumulator=0,this._currentStepId=0,this._currentInternalStep=0,this._fogEnabled=!0,this._fogMode=k.FOGMODE_NONE,this.fogColor=new R.v9(.2,.2,.3),this.fogDensity=.1,this.fogStart=0,this.fogEnd=1e3,this.needsPreviousWorldMatrices=!1,this._shadowsEnabled=!0,this._lightsEnabled=!0,this._unObserveActiveCameras=null,this._texturesEnabled=!0,this._frameGraph=null,this.frameGraphs=[],this.physicsEnabled=!0,this.particlesEnabled=!0,this.spritesEnabled=!0,this._skeletonsEnabled=!0,this.lensFlaresEnabled=!0,this.collisionsEnabled=!0,this.gravity=new d.Pq(0,-9.807,0),this.postProcessesEnabled=!0,this.renderTargetsEnabled=!0,this.dumpNextRenderTargets=!1,this.customRenderTargets=[],this.importedMeshesFiles=[],this.probesEnabled=!0,this._meshesForIntersections=new o.b(256),this.proceduralTexturesEnabled=!0,this._totalVertices=new y.A,this._activeIndices=new y.A,this._activeParticles=new y.A,this._activeBones=new y.A,this._animationTime=0,this.animationTimeScale=1,this._renderId=0,this._frameId=0,this._executeWhenReadyTimeoutId=null,this._intermediateRendering=!1,this._defaultFrameBufferCleared=!1,this._viewUpdateFlag=-1,this._projectionUpdateFlag=-1,this._toBeDisposed=Array(256),this._activeRequests=[],this._pendingData=[],this._isDisposed=!1,this.dispatchAllSubMeshesOfActiveMeshes=!1,this._activeMeshes=new o.L(256),this._processedMaterials=new o.L(256),this._renderTargets=new o.b(256),this._materialsRenderTargets=new o.b(256),this._activeParticleSystems=new o.L(256),this._activeSkeletons=new o.b(32),this._softwareSkinnedMeshes=new o.b(32),this._activeAnimatables=[],this._transformMatrix=d.uq.Zero(),this.requireLightSorting=!1,this._components=[],this._serializableComponents=[],this._transientComponents=[],this._beforeCameraUpdateStage=v.B.Create(),this._beforeClearStage=v.B.Create(),this._beforeRenderTargetClearStage=v.B.Create(),this._gatherRenderTargetsStage=v.B.Create(),this._gatherActiveCameraRenderTargetsStage=v.B.Create(),this._isReadyForMeshStage=v.B.Create(),this._beforeEvaluateActiveMeshStage=v.B.Create(),this._evaluateSubMeshStage=v.B.Create(),this._preActiveMeshStage=v.B.Create(),this._cameraDrawRenderTargetStage=v.B.Create(),this._beforeCameraDrawStage=v.B.Create(),this._beforeRenderTargetDrawStage=v.B.Create(),this._beforeRenderingGroupDrawStage=v.B.Create(),this._beforeRenderingMeshStage=v.B.Create(),this._afterRenderingMeshStage=v.B.Create(),this._afterRenderingGroupDrawStage=v.B.Create(),this._afterCameraDrawStage=v.B.Create(),this._afterCameraPostProcessStage=v.B.Create(),this._afterRenderTargetDrawStage=v.B.Create(),this._afterRenderTargetPostProcessStage=v.B.Create(),this._afterRenderStage=v.B.Create(),this._pointerMoveStage=v.B.Create(),this._pointerDownStage=v.B.Create(),this._pointerUpStage=v.B.Create(),this._geometriesByUniqueId=null,this._uniqueId=0,this._defaultMeshCandidates={data:[],length:0},this._defaultSubMeshCandidates={data:[],length:0},this._preventFreeActiveMeshesAndRenderingGroups=!1,this._activeMeshesFrozen=!1,this._activeMeshesFrozenButKeepClipping=!1,this._skipEvaluateActiveMeshesCompletely=!1,this._useCurrentFrameBuffer=!1,this._allowPostProcessClearColor=!0,this.getDeterministicFrameTime=()=>this._engine.getTimeStep(),this._registeredActions=0,this._blockMaterialDirtyMechanism=!1,this._perfCollector=null,this.activeCameras=[],this._uniqueId=this.getUniqueId();let s={useGeometryUniqueIdsMap:!0,useMaterialMeshMap:!0,useClonedMeshMap:!0,virtual:!1,...t};e=this._engine=e||p.q.LastCreatedEngine,s.virtual?e._virtualScenes.push(this):(p.q._LastCreatedScene=this,e.scenes.push(this)),this._uid=null,this._renderingManager=new _.m(this),f.X&&(this.postProcessManager=new f.X(this)),(0,b.BA)()&&this.attachControl(),this._createUbo(),c.p&&(this._imageProcessingConfiguration=new c.p),this.setDefaultCandidateProviders(),s.useGeometryUniqueIdsMap&&(this._geometriesByUniqueId={}),this.useMaterialMeshMap=s.useMaterialMeshMap,this.useClonedMeshMap=s.useClonedMeshMap,t&&t.virtual||e.onNewSceneAddedObservable.notifyObservers(this)}getClassName(){return"Scene"}_getDefaultMeshCandidates(){return this._defaultMeshCandidates.data=this.meshes,this._defaultMeshCandidates.length=this.meshes.length,this._defaultMeshCandidates}_getDefaultSubMeshCandidates(e){return this._defaultSubMeshCandidates.data=e.subMeshes,this._defaultSubMeshCandidates.length=e.subMeshes.length,this._defaultSubMeshCandidates}setDefaultCandidateProviders(){this.getActiveMeshCandidates=()=>this._getDefaultMeshCandidates(),this.getActiveSubMeshCandidates=e=>this._getDefaultSubMeshCandidates(e),this.getIntersectingSubMeshCandidates=(e,t)=>this._getDefaultSubMeshCandidates(e),this.getCollidingSubMeshCandidates=(e,t)=>this._getDefaultSubMeshCandidates(e)}get meshUnderPointer(){return this._inputManager.meshUnderPointer}get pointerX(){return this._inputManager.pointerX}set pointerX(e){this._inputManager.pointerX=e}get pointerY(){return this._inputManager.pointerY}set pointerY(e){this._inputManager.pointerY=e}getCachedMaterial(){return this._cachedMaterial}getCachedEffect(){return this._cachedEffect}getCachedVisibility(){return this._cachedVisibility}isCachedMaterialInvalid(e,t,s=1){return this._cachedEffect!==t||this._cachedMaterial!==e||this._cachedVisibility!==s}getEngine(){return this._engine}getTotalVertices(){return this._totalVertices.current}get totalVerticesPerfCounter(){return this._totalVertices}getActiveIndices(){return this._activeIndices.current}get totalActiveIndicesPerfCounter(){return this._activeIndices}getActiveParticles(){return this._activeParticles.current}get activeParticlesPerfCounter(){return this._activeParticles}getActiveBones(){return this._activeBones.current}get activeBonesPerfCounter(){return this._activeBones}getActiveMeshes(){return this._activeMeshes}getAnimationRatio(){return void 0!==this._animationRatio?this._animationRatio:1}getRenderId(){return this._renderId}getFrameId(){return this._frameId}incrementRenderId(){this._renderId++}_createUbo(){this.setSceneUniformBuffer(this.createSceneUniformBuffer())}simulatePointerMove(e,t){return this._inputManager.simulatePointerMove(e,t),this}simulatePointerDown(e,t){return this._inputManager.simulatePointerDown(e,t),this}simulatePointerUp(e,t,s){return this._inputManager.simulatePointerUp(e,t,s),this}isPointerCaptured(e=0){return this._inputManager.isPointerCaptured(e)}attachControl(e=!0,t=!0,s=!0){this._inputManager.attachControl(e,t,s)}detachControl(){this._inputManager.detachControl()}isReady(e=!0){let t;if(this._isDisposed)return!1;let s=this.getEngine(),r=s.currentRenderPassId;s.currentRenderPassId=this.activeCamera?.renderPassId??r;let i=!0;for(this._pendingData.length>0&&(i=!1),this.prePassRenderer?.update(),this.useOrderIndependentTransparency&&this.depthPeelingRenderer&&i&&(i=this.depthPeelingRenderer.isReady()),e&&(this._processedMaterials.reset(),this._materialsRenderTargets.reset()),t=0;t<this.meshes.length;t++){let r=this.meshes[t];if(!r.subMeshes||0===r.subMeshes.length)continue;if(!r.isReady(!0)){i=!1;continue}let a=r.hasThinInstances||"InstancedMesh"===r.getClassName()||"InstancedLinesMesh"===r.getClassName()||s.getCaps().instancedArrays&&r.instances.length>0;for(let e of this._isReadyForMeshStage)e.action(r,a)||(i=!1);if(!e)continue;let n=r.material||this.defaultMaterial;if(n)if(n._storeEffectOnSubMeshes)for(let e of r.subMeshes){let t=e.getMaterial();t&&t.hasRenderTargetTextures&&null!=t.getRenderTargetTextures&&-1===this._processedMaterials.indexOf(t)&&(this._processedMaterials.push(t),this._materialsRenderTargets.concatWithNoDuplicate(t.getRenderTargetTextures()))}else n.hasRenderTargetTextures&&null!=n.getRenderTargetTextures&&-1===this._processedMaterials.indexOf(n)&&(this._processedMaterials.push(n),this._materialsRenderTargets.concatWithNoDuplicate(n.getRenderTargetTextures()))}if(e)for(t=0;t<this._materialsRenderTargets.length;++t)this._materialsRenderTargets.data[t].isReadyForRendering()||(i=!1);for(t=0;t<this.geometries.length;t++)2===this.geometries[t].delayLoadState&&(i=!1);if(this.activeCameras&&this.activeCameras.length>0)for(let e of this.activeCameras)e.isReady(!0)||(i=!1);else this.activeCamera&&!this.activeCamera.isReady(!0)&&(i=!1);for(let e of this.particleSystems)e.isReady()||(i=!1);if(this.layers)for(let e of this.layers)e.isReady()||(i=!1);if(this.effectLayers)for(let e of this.effectLayers)e.isLayerReady()||(i=!1);return s.areAllEffectsReady()||(i=!1),s.currentRenderPassId=r,i}resetCachedMaterial(){this._cachedMaterial=null,this._cachedEffect=null,this._cachedVisibility=null}registerBeforeRender(e){this.onBeforeRenderObservable.add(e)}unregisterBeforeRender(e){this.onBeforeRenderObservable.removeCallback(e)}registerAfterRender(e){this.onAfterRenderObservable.add(e)}unregisterAfterRender(e){this.onAfterRenderObservable.removeCallback(e)}_executeOnceBeforeRender(e){let t=()=>{e(),setTimeout(()=>{this.unregisterBeforeRender(t)})};this.registerBeforeRender(t)}executeOnceBeforeRender(e,t){void 0!==t?setTimeout(()=>{this._executeOnceBeforeRender(e)},t):this._executeOnceBeforeRender(e)}addPendingData(e){this._pendingData.push(e)}removePendingData(e){let t=this.isLoading,s=this._pendingData.indexOf(e);-1!==s&&this._pendingData.splice(s,1),t&&!this.isLoading&&this.onDataLoadedObservable.notifyObservers(this)}getWaitingItemsCount(){return this._pendingData.length}get isLoading(){return this._pendingData.length>0}executeWhenReady(e,t=!1){this.onReadyObservable.addOnce(e),null===this._executeWhenReadyTimeoutId&&this._checkIsReady(t)}async whenReadyAsync(e=!1){return await new Promise(t=>{this.executeWhenReady(()=>{t()},e)})}_checkIsReady(e=!1){if(this._registerTransientComponents(),this.isReady(e)){this.onReadyObservable.notifyObservers(this),this.onReadyObservable.clear(),this._executeWhenReadyTimeoutId=null;return}if(this._isDisposed){this.onReadyObservable.clear(),this._executeWhenReadyTimeoutId=null;return}this._executeWhenReadyTimeoutId=setTimeout(()=>{this.incrementRenderId(),this._checkIsReady(e)},100)}get animatables(){return this._activeAnimatables}resetLastAnimationTimeFrame(){this._animationTimeLast=a.j.Now}getViewMatrix(){return this._viewMatrix}getProjectionMatrix(){return this._projectionMatrix}getTransformMatrix(){return this._transformMatrix}setTransformMatrix(e,t,s,r){s||r||!this._multiviewSceneUbo||(this._multiviewSceneUbo.dispose(),this._multiviewSceneUbo=null),(this._viewUpdateFlag!==e.updateFlag||this._projectionUpdateFlag!==t.updateFlag)&&(this._viewUpdateFlag=e.updateFlag,this._projectionUpdateFlag=t.updateFlag,this._viewMatrix=e,this._projectionMatrix=t,this._viewMatrix.multiplyToRef(this._projectionMatrix,this._transformMatrix),this._frustumPlanes?P.P.GetPlanesToRef(this._transformMatrix,this._frustumPlanes):this._frustumPlanes=P.P.GetPlanes(this._transformMatrix),this._multiviewSceneUbo&&this._multiviewSceneUbo.useUbo?this._updateMultiviewUbo(s,r):this._sceneUbo.useUbo&&(this._sceneUbo.updateMatrix("viewProjection",this._transformMatrix),this._sceneUbo.updateMatrix("view",this._viewMatrix),this._sceneUbo.updateMatrix("projection",this._projectionMatrix)))}getSceneUniformBuffer(){return this._multiviewSceneUbo?this._multiviewSceneUbo:this._sceneUbo}createSceneUniformBuffer(e){let t=new u.D(this._engine,void 0,!1,e??"scene");return t.addUniform("viewProjection",16),t.addUniform("view",16),t.addUniform("projection",16),t.addUniform("vEyePosition",4),t}setSceneUniformBuffer(e){this._sceneUbo=e,this._viewUpdateFlag=-1,this._projectionUpdateFlag=-1}getUniqueId(){return O.K.UniqueId}addMesh(e,t=!1){if(!this._blockEntityCollection&&(this.meshes.push(e),e._resyncLightSources(),e.parent||e._addToSceneRootNodes(),i.S0.SetImmediate(()=>{this.onNewMeshAddedObservable.notifyObservers(e)}),t))for(let t of e.getChildMeshes())this.addMesh(t)}removeMesh(e,t=!1){let s=this.meshes.indexOf(e);if(-1!==s&&(this.meshes.splice(s,1),e.parent||e._removeFromSceneRootNodes()),this._inputManager._invalidateMesh(e),this.onMeshRemovedObservable.notifyObservers(e),t)for(let t of e.getChildMeshes())this.removeMesh(t);return s}addTransformNode(e){this._blockEntityCollection||(e.getScene()!==this||-1===e._indexInSceneTransformNodesArray)&&(e._indexInSceneTransformNodesArray=this.transformNodes.length,this.transformNodes.push(e),e.parent||e._addToSceneRootNodes(),this.onNewTransformNodeAddedObservable.notifyObservers(e))}removeTransformNode(e){let t=e._indexInSceneTransformNodesArray;if(-1!==t){if(t!==this.transformNodes.length-1){let e=this.transformNodes[this.transformNodes.length-1];this.transformNodes[t]=e,e._indexInSceneTransformNodesArray=t}e._indexInSceneTransformNodesArray=-1,this.transformNodes.pop(),e.parent||e._removeFromSceneRootNodes()}return this.onTransformNodeRemovedObservable.notifyObservers(e),t}removeSkeleton(e){let t=this.skeletons.indexOf(e);return -1!==t&&(this.skeletons.splice(t,1),this.onSkeletonRemovedObservable.notifyObservers(e),this._executeActiveContainerCleanup(this._activeSkeletons)),t}removeMorphTargetManager(e){let t=this.morphTargetManagers.indexOf(e);return -1!==t&&this.morphTargetManagers.splice(t,1),t}removeLight(e){let t=this.lights.indexOf(e);if(-1!==t){for(let t of this.meshes)t._removeLightSource(e,!1);this.lights.splice(t,1),this.sortLightsByPriority(),e.parent||e._removeFromSceneRootNodes()}return this.onLightRemovedObservable.notifyObservers(e),t}removeCamera(e){let t=this.cameras.indexOf(e);if(-1!==t&&(this.cameras.splice(t,1),e.parent||e._removeFromSceneRootNodes()),this.activeCameras){let t=this.activeCameras.indexOf(e);-1!==t&&this.activeCameras.splice(t,1)}return this.activeCamera===e&&(this.cameras.length>0?this.activeCamera=this.cameras[0]:this.activeCamera=null),this.onCameraRemovedObservable.notifyObservers(e),t}removeParticleSystem(e){let t=this.particleSystems.indexOf(e);return -1!==t&&(this.particleSystems.splice(t,1),this._executeActiveContainerCleanup(this._activeParticleSystems)),this.onParticleSystemRemovedObservable.notifyObservers(e),t}removeAnimation(e){let t=this.animations.indexOf(e);return -1!==t&&this.animations.splice(t,1),t}stopAnimation(e,t,s){}removeAnimationGroup(e){let t=this.animationGroups.indexOf(e);return -1!==t&&this.animationGroups.splice(t,1),this.onAnimationGroupRemovedObservable.notifyObservers(e),t}removeMultiMaterial(e){let t=this.multiMaterials.indexOf(e);return -1!==t&&this.multiMaterials.splice(t,1),this.onMultiMaterialRemovedObservable.notifyObservers(e),t}removeMaterial(e){let t=e._indexInSceneMaterialArray;if(-1!==t&&t<this.materials.length){if(t!==this.materials.length-1){let e=this.materials[this.materials.length-1];this.materials[t]=e,e._indexInSceneMaterialArray=t}e._indexInSceneMaterialArray=-1,this.materials.pop()}return this.onMaterialRemovedObservable.notifyObservers(e),t}removeActionManager(e){let t=this.actionManagers.indexOf(e);return -1!==t&&this.actionManagers.splice(t,1),t}removeTexture(e){let t=this.textures.indexOf(e);return -1!==t&&this.textures.splice(t,1),this.onTextureRemovedObservable.notifyObservers(e),t}removeFrameGraph(e){let t=this.frameGraphs.indexOf(e);return -1!==t&&this.frameGraphs.splice(t,1),this.onFrameGraphRemovedObservable.notifyObservers(e),t}removePostProcess(e){let t=this.postProcesses.indexOf(e);return -1!==t&&this.postProcesses.splice(t,1),this.onPostProcessRemovedObservable.notifyObservers(e),t}removeEffectLayer(e){let t=this.effectLayers.indexOf(e);return -1!==t&&this.effectLayers.splice(t,1),this.onEffectLayerRemovedObservable.notifyObservers(e),t}addLight(e){if(!this._blockEntityCollection){for(let t of(this.lights.push(e),this.sortLightsByPriority(),e.parent||e._addToSceneRootNodes(),this.meshes))-1===t.lightSources.indexOf(e)&&(t.lightSources.push(e),t._resyncLightSources());i.S0.SetImmediate(()=>{this.onNewLightAddedObservable.notifyObservers(e)})}}sortLightsByPriority(){this.requireLightSorting&&this.lights.sort(S.c.CompareLightsPriority)}addCamera(e){!this._blockEntityCollection&&(this.cameras.push(e),i.S0.SetImmediate(()=>{this.onNewCameraAddedObservable.notifyObservers(e)}),e.parent||e._addToSceneRootNodes())}addSkeleton(e){this._blockEntityCollection||(this.skeletons.push(e),i.S0.SetImmediate(()=>{this.onNewSkeletonAddedObservable.notifyObservers(e)}))}addParticleSystem(e){this._blockEntityCollection||(this.particleSystems.push(e),i.S0.SetImmediate(()=>{this.onNewParticleSystemAddedObservable.notifyObservers(e)}))}addAnimation(e){this._blockEntityCollection||this.animations.push(e)}addAnimationGroup(e){this._blockEntityCollection||(this.animationGroups.push(e),i.S0.SetImmediate(()=>{this.onNewAnimationGroupAddedObservable.notifyObservers(e)}))}addMultiMaterial(e){this._blockEntityCollection||(this.multiMaterials.push(e),i.S0.SetImmediate(()=>{this.onNewMultiMaterialAddedObservable.notifyObservers(e)}))}addMaterial(e){this._blockEntityCollection||(e.getScene()!==this||-1===e._indexInSceneMaterialArray)&&(e._indexInSceneMaterialArray=this.materials.length,this.materials.push(e),i.S0.SetImmediate(()=>{this.onNewMaterialAddedObservable.notifyObservers(e)}))}addMorphTargetManager(e){this._blockEntityCollection||this.morphTargetManagers.push(e)}addGeometry(e){this._blockEntityCollection||(this._geometriesByUniqueId&&(this._geometriesByUniqueId[e.uniqueId]=this.geometries.length),this.geometries.push(e))}addActionManager(e){this.actionManagers.push(e)}addTexture(e){this._blockEntityCollection||(this.textures.push(e),this.onNewTextureAddedObservable.notifyObservers(e))}addFrameGraph(e){this.frameGraphs.push(e),i.S0.SetImmediate(()=>{this.onNewFrameGraphAddedObservable.notifyObservers(e)})}addPostProcess(e){this._blockEntityCollection||(this.postProcesses.push(e),i.S0.SetImmediate(()=>{this.onNewPostProcessAddedObservable.notifyObservers(e)}))}addEffectLayer(e){this._blockEntityCollection||(this.effectLayers.push(e),i.S0.SetImmediate(()=>{this.onNewEffectLayerAddedObservable.notifyObservers(e)}))}switchActiveCamera(e,t=!0){this._engine.getInputElement()&&(this.activeCamera&&this.activeCamera.detachControl(),this.activeCamera=e,t&&e.attachControl())}setActiveCameraById(e){let t=this.getCameraById(e);return t?(this.activeCamera=t,t):null}setActiveCameraByName(e){let t=this.getCameraByName(e);return t?(this.activeCamera=t,t):null}getAnimationGroupByName(e){for(let t=0;t<this.animationGroups.length;t++)if(this.animationGroups[t].name===e)return this.animationGroups[t];return null}_getMaterial(e,t){for(let e=0;e<this.materials.length;e++){let s=this.materials[e];if(t(s))return s}if(e)for(let e=0;e<this.multiMaterials.length;e++){let s=this.multiMaterials[e];if(t(s))return s}return null}getMaterialByUniqueID(e,t=!1){return this.getMaterialByUniqueId(e,t)}getMaterialByUniqueId(e,t=!1){return this._getMaterial(t,t=>t.uniqueId===e)}getMaterialById(e,t=!1){return this._getMaterial(t,t=>t.id===e)}getMaterialByName(e,t=!1){return this._getMaterial(t,t=>t.name===e)}getLastMaterialById(e,t=!1){for(let t=this.materials.length-1;t>=0;t--)if(this.materials[t].id===e)return this.materials[t];if(t){for(let t=this.multiMaterials.length-1;t>=0;t--)if(this.multiMaterials[t].id===e)return this.multiMaterials[t]}return null}getTextureByUniqueId(e){for(let t=0;t<this.textures.length;t++)if(this.textures[t].uniqueId===e)return this.textures[t];return null}getTextureByName(e){for(let t=0;t<this.textures.length;t++)if(this.textures[t].name===e)return this.textures[t];return null}getCameraById(e){for(let t=0;t<this.cameras.length;t++)if(this.cameras[t].id===e)return this.cameras[t];return null}getCameraByUniqueId(e){for(let t=0;t<this.cameras.length;t++)if(this.cameras[t].uniqueId===e)return this.cameras[t];return null}getCameraByName(e){for(let t=0;t<this.cameras.length;t++)if(this.cameras[t].name===e)return this.cameras[t];return null}getBoneById(e){for(let t=0;t<this.skeletons.length;t++){let s=this.skeletons[t];for(let t=0;t<s.bones.length;t++)if(s.bones[t].id===e)return s.bones[t]}return null}getBoneByName(e){for(let t=0;t<this.skeletons.length;t++){let s=this.skeletons[t];for(let t=0;t<s.bones.length;t++)if(s.bones[t].name===e)return s.bones[t]}return null}getLightByName(e){for(let t=0;t<this.lights.length;t++)if(this.lights[t].name===e)return this.lights[t];return null}getLightById(e){for(let t=0;t<this.lights.length;t++)if(this.lights[t].id===e)return this.lights[t];return null}getLightByUniqueId(e){for(let t=0;t<this.lights.length;t++)if(this.lights[t].uniqueId===e)return this.lights[t];return null}getParticleSystemById(e){for(let t=0;t<this.particleSystems.length;t++)if(this.particleSystems[t].id===e)return this.particleSystems[t];return null}getGeometryById(e){for(let t=0;t<this.geometries.length;t++)if(this.geometries[t].id===e)return this.geometries[t];return null}_getGeometryByUniqueId(e){if(this._geometriesByUniqueId){let t=this._geometriesByUniqueId[e];if(void 0!==t)return this.geometries[t]}else for(let t=0;t<this.geometries.length;t++)if(this.geometries[t].uniqueId===e)return this.geometries[t];return null}getFrameGraphByName(e){for(let t=0;t<this.frameGraphs.length;t++)if(this.frameGraphs[t].name===e)return this.frameGraphs[t];return null}pushGeometry(e,t){return!(!t&&this._getGeometryByUniqueId(e.uniqueId))&&(this.addGeometry(e),i.S0.SetImmediate(()=>{this.onNewGeometryAddedObservable.notifyObservers(e)}),!0)}removeGeometry(e){let t;if(this._geometriesByUniqueId){if(void 0===(t=this._geometriesByUniqueId[e.uniqueId]))return!1}else if((t=this.geometries.indexOf(e))<0)return!1;if(t!==this.geometries.length-1){let e=this.geometries[this.geometries.length-1];e&&(this.geometries[t]=e,this._geometriesByUniqueId&&(this._geometriesByUniqueId[e.uniqueId]=t))}return this._geometriesByUniqueId&&(this._geometriesByUniqueId[e.uniqueId]=void 0),this.geometries.pop(),this.onGeometryRemovedObservable.notifyObservers(e),!0}getGeometries(){return this.geometries}getMeshById(e){for(let t=0;t<this.meshes.length;t++)if(this.meshes[t].id===e)return this.meshes[t];return null}getMeshesById(e){return this.meshes.filter(function(t){return t.id===e})}getTransformNodeById(e){for(let t=0;t<this.transformNodes.length;t++)if(this.transformNodes[t].id===e)return this.transformNodes[t];return null}getTransformNodeByUniqueId(e){for(let t=0;t<this.transformNodes.length;t++)if(this.transformNodes[t].uniqueId===e)return this.transformNodes[t];return null}getTransformNodesById(e){return this.transformNodes.filter(function(t){return t.id===e})}getMeshByUniqueId(e){for(let t=0;t<this.meshes.length;t++)if(this.meshes[t].uniqueId===e)return this.meshes[t];return null}getLastMeshById(e){for(let t=this.meshes.length-1;t>=0;t--)if(this.meshes[t].id===e)return this.meshes[t];return null}getLastTransformNodeById(e){for(let t=this.transformNodes.length-1;t>=0;t--)if(this.transformNodes[t].id===e)return this.transformNodes[t];return null}getLastEntryById(e){let t;for(t=this.meshes.length-1;t>=0;t--)if(this.meshes[t].id===e)return this.meshes[t];for(t=this.transformNodes.length-1;t>=0;t--)if(this.transformNodes[t].id===e)return this.transformNodes[t];for(t=this.cameras.length-1;t>=0;t--)if(this.cameras[t].id===e)return this.cameras[t];for(t=this.lights.length-1;t>=0;t--)if(this.lights[t].id===e)return this.lights[t];return null}getNodeById(e){let t=this.getMeshById(e);if(t)return t;let s=this.getTransformNodeById(e);if(s)return s;let r=this.getLightById(e);if(r)return r;let i=this.getCameraById(e);if(i)return i;let a=this.getBoneById(e);return a||null}getNodeByName(e){let t=this.getMeshByName(e);if(t)return t;let s=this.getTransformNodeByName(e);if(s)return s;let r=this.getLightByName(e);if(r)return r;let i=this.getCameraByName(e);if(i)return i;let a=this.getBoneByName(e);return a||null}getMeshByName(e){for(let t=0;t<this.meshes.length;t++)if(this.meshes[t].name===e)return this.meshes[t];return null}getTransformNodeByName(e){for(let t=0;t<this.transformNodes.length;t++)if(this.transformNodes[t].name===e)return this.transformNodes[t];return null}getLastSkeletonById(e){for(let t=this.skeletons.length-1;t>=0;t--)if(this.skeletons[t].id===e)return this.skeletons[t];return null}getSkeletonByUniqueId(e){for(let t=0;t<this.skeletons.length;t++)if(this.skeletons[t].uniqueId===e)return this.skeletons[t];return null}getSkeletonById(e){for(let t=0;t<this.skeletons.length;t++)if(this.skeletons[t].id===e)return this.skeletons[t];return null}getSkeletonByName(e){for(let t=0;t<this.skeletons.length;t++)if(this.skeletons[t].name===e)return this.skeletons[t];return null}getMorphTargetManagerById(e){for(let t=0;t<this.morphTargetManagers.length;t++)if(this.morphTargetManagers[t].uniqueId===e)return this.morphTargetManagers[t];return null}getMorphTargetById(e){for(let t=0;t<this.morphTargetManagers.length;++t){let s=this.morphTargetManagers[t];for(let t=0;t<s.numTargets;++t){let r=s.getTarget(t);if(r.id===e)return r}}return null}getMorphTargetByName(e){for(let t=0;t<this.morphTargetManagers.length;++t){let s=this.morphTargetManagers[t];for(let t=0;t<s.numTargets;++t){let r=s.getTarget(t);if(r.name===e)return r}}return null}getPostProcessByName(e){for(let t=0;t<this.postProcesses.length;++t){let s=this.postProcesses[t];if(s.name===e)return s}return null}isActiveMesh(e){return -1!==this._activeMeshes.indexOf(e)}get uid(){return this._uid||(this._uid=i.S0.RandomId()),this._uid}addExternalData(e,t){return this._externalData||(this._externalData=new h.w),this._externalData.add(e,t)}getExternalData(e){return this._externalData?this._externalData.get(e):null}getOrAddExternalDataWithFactory(e,t){return this._externalData||(this._externalData=new h.w),this._externalData.getOrAddWithFactory(e,t)}removeExternalData(e){return this._externalData.remove(e)}_evaluateSubMesh(e,t,s,r){if(r||e.isInFrustum(this._frustumPlanes)){for(let s of this._evaluateSubMeshStage)s.action(t,e);let s=e.getMaterial();null!=s&&(s.hasRenderTargetTextures&&null!=s.getRenderTargetTextures&&-1===this._processedMaterials.indexOf(s)&&(this._processedMaterials.push(s),this._materialsRenderTargets.concatWithNoDuplicate(s.getRenderTargetTextures())),this._renderingManager.dispatch(e,t,s))}}freeProcessedMaterials(){this._processedMaterials.dispose()}get blockfreeActiveMeshesAndRenderingGroups(){return this._preventFreeActiveMeshesAndRenderingGroups}set blockfreeActiveMeshesAndRenderingGroups(e){this._preventFreeActiveMeshesAndRenderingGroups!==e&&(e&&(this.freeActiveMeshes(),this.freeRenderingGroups()),this._preventFreeActiveMeshesAndRenderingGroups=e)}freeActiveMeshes(){if(!this.blockfreeActiveMeshesAndRenderingGroups&&(this._activeMeshes.dispose(),this.activeCamera&&this.activeCamera._activeMeshes&&this.activeCamera._activeMeshes.dispose(),this.activeCameras))for(let e=0;e<this.activeCameras.length;e++){let t=this.activeCameras[e];t&&t._activeMeshes&&t._activeMeshes.dispose()}}freeRenderingGroups(){if(!this.blockfreeActiveMeshesAndRenderingGroups&&(this._renderingManager&&this._renderingManager.freeRenderingGroups(),this.textures))for(let e=0;e<this.textures.length;e++){let t=this.textures[e];t&&t.renderList&&t.freeRenderingGroups()}}_isInIntermediateRendering(){return this._intermediateRendering}freezeActiveMeshes(e=!1,t,s,r=!0,i=!1){return this.executeWhenReady(()=>{if(!this.activeCamera){s&&s("No active camera found");return}if(this._frustumPlanes||this.updateTransformMatrix(),this._evaluateActiveMeshes(),this._activeMeshesFrozen=!0,this._activeMeshesFrozenButKeepClipping=i,this._skipEvaluateActiveMeshesCompletely=e,r)for(let e=0;e<this._activeMeshes.length;e++)this._activeMeshes.data[e]._freeze();t&&t()}),this}unfreezeActiveMeshes(){for(let e=0;e<this.meshes.length;e++){let t=this.meshes[e];t._internalAbstractMeshDataInfo&&(t._internalAbstractMeshDataInfo._isActive=!1)}for(let e=0;e<this._activeMeshes.length;e++)this._activeMeshes.data[e]._unFreeze();return this._activeMeshesFrozen=!1,this}_executeActiveContainerCleanup(e){(!this._engine.snapshotRendering||1!==this._engine.snapshotRenderingMode)&&this._activeMeshesFrozen&&this._activeMeshes.length||this.onBeforeRenderObservable.addOnce(()=>e.dispose())}_evaluateActiveMeshes(){if(this._engine.snapshotRendering&&1===this._engine.snapshotRenderingMode){this._activeMeshes.length>0&&(this.activeCamera?._activeMeshes.reset(),this._activeMeshes.reset(),this._renderingManager.reset(),this._processedMaterials.reset(),this._activeParticleSystems.reset(),this._activeSkeletons.reset(),this._softwareSkinnedMeshes.reset());return}if(this._activeMeshesFrozen&&this._activeMeshes.length){if(!this._skipEvaluateActiveMeshesCompletely){let e=this._activeMeshes.length;for(let t=0;t<e;t++)this._activeMeshes.data[t].computeWorldMatrix()}if(this._activeParticleSystems){let e=this._activeParticleSystems.length;for(let t=0;t<e;t++)this._activeParticleSystems.data[t].animate()}this._renderingManager.resetSprites();return}if(!this.activeCamera)return;for(let e of(this.onBeforeActiveMeshesEvaluationObservable.notifyObservers(this),this.activeCamera._activeMeshes.reset(),this._activeMeshes.reset(),this._renderingManager.reset(),this._processedMaterials.reset(),this._activeParticleSystems.reset(),this._activeSkeletons.reset(),this._softwareSkinnedMeshes.reset(),this._materialsRenderTargets.reset(),this._beforeEvaluateActiveMeshStage))e.action();let e=this.getActiveMeshCandidates(),t=e.length;for(let s=0;s<t;s++){let t=e.data[s],r=t._internalAbstractMeshDataInfo._currentLOD.get(this.activeCamera);if(r?r[1]=-1:(r=[t,-1],t._internalAbstractMeshDataInfo._currentLOD.set(this.activeCamera,r)),t.isBlocked||(this._totalVertices.addCount(t.getTotalVertices(),!1),!t.isReady()||!t.isEnabled()||t.scaling.hasAZeroComponent))continue;t.computeWorldMatrix(),t.actionManager&&t.actionManager.hasSpecificTriggers2(12,13)&&this._meshesForIntersections.pushNoDuplicate(t);let i=this.customLODSelector?this.customLODSelector(t,this.activeCamera):t.getLOD(this.activeCamera);if(r[0]=i,r[1]=this._frameId,null!=i&&(i!==t&&0!==i.billboardMode&&i.computeWorldMatrix(),t._preActivate(),t.isVisible&&t.visibility>0&&(t.layerMask&this.activeCamera.layerMask)!=0&&(this._skipFrustumClipping||t.alwaysSelectAsActiveMesh||t.isInFrustum(this._frustumPlanes)))){for(let e of(this._activeMeshes.push(t),this.activeCamera._activeMeshes.push(t),i!==t&&i._activate(this._renderId,!1),this._preActiveMeshStage))e.action(t);t._activate(this._renderId,!1)&&(t.isAnInstance?t._internalAbstractMeshDataInfo._actAsRegularMesh&&(i=t):i._internalAbstractMeshDataInfo._onlyForInstances=!1,i._internalAbstractMeshDataInfo._isActive=!0,this._activeMesh(t,i)),t._postActivate()}}if(this.onAfterActiveMeshesEvaluationObservable.notifyObservers(this),this.particlesEnabled){this.onBeforeParticlesRenderingObservable.notifyObservers(this);for(let e=0;e<this.particleSystems.length;e++){let t=this.particleSystems[e];if(!t.isStarted()||!t.emitter)continue;let s=t.emitter;(!s.position||s.isEnabled())&&(this._activeParticleSystems.push(t),t.animate(),this._renderingManager.dispatchParticles(t))}this.onAfterParticlesRenderingObservable.notifyObservers(this)}}_prepareSkeleton(e){this._skeletonsEnabled&&e.skeleton&&(this._activeSkeletons.pushNoDuplicate(e.skeleton)&&(e.skeleton.prepare(),this._activeBones.addCount(e.skeleton.bones.length,!1)),!e.computeBonesUsingShaders&&this._softwareSkinnedMeshes.pushNoDuplicate(e)&&this.frameGraph&&e.applySkeleton(e.skeleton))}_activeMesh(e,t){this._prepareSkeleton(t);let s=e.hasInstances||e.isAnInstance||this.dispatchAllSubMeshesOfActiveMeshes||this._skipFrustumClipping||t.alwaysSelectAsActiveMesh;if(t&&t.subMeshes&&t.subMeshes.length>0){let r=this.getActiveSubMeshCandidates(t),i=r.length;s=s||1===i;for(let a=0;a<i;a++){let i=r.data[a];this._evaluateSubMesh(i,t,e,s)}}}updateTransformMatrix(e){let t=this.activeCamera;if(t)if(t._renderingMultiview){let s=t._rigCameras[0],r=t._rigCameras[1];this.setTransformMatrix(s.getViewMatrix(),s.getProjectionMatrix(e),r.getViewMatrix(),r.getProjectionMatrix(e))}else this.setTransformMatrix(t.getViewMatrix(),t.getProjectionMatrix(e))}_bindFrameBuffer(e,t=!0){!this._useCurrentFrameBuffer&&(e&&e._multiviewTexture?e._multiviewTexture._bindFrameBuffer():e&&e.outputRenderTarget?e.outputRenderTarget._bindFrameBuffer():this._engine._currentFrameBufferIsDefaultFrameBuffer()||this._engine.restoreDefaultFramebuffer()),t&&this._clearFrameBuffer(e)}_clearFrameBuffer(e){if(e&&e._multiviewTexture);else if(e&&e.outputRenderTarget&&!e._renderingMultiview){let t=e.outputRenderTarget;t.onClearObservable.hasObservers()?t.onClearObservable.notifyObservers(this._engine):t.skipInitialClear||e.isRightCamera||(this.autoClear&&this._engine.clear(t.clearColor||this._clearColor,!t._cleared,!0,!0),t._cleared=!0)}else this._defaultFrameBufferCleared?this._engine.clear(null,!1,!0,!0):(this._defaultFrameBufferCleared=!0,this._clear())}_renderForCamera(e,t,s=!0){if(e&&e._skipRendering)return;let r=this._engine;if(this._activeCamera=e,!this.activeCamera)throw Error("Active camera not set");if(r.setViewport(this.activeCamera.viewport),this.resetCachedMaterial(),this._renderId++,!this.prePass&&s){let t=!0;e._renderingMultiview&&e.outputRenderTarget&&(t=e.outputRenderTarget.skipInitialClear,this.autoClear&&(this._defaultFrameBufferCleared=!1,e.outputRenderTarget.skipInitialClear=!1)),this._bindFrameBuffer(this._activeCamera),e._renderingMultiview&&e.outputRenderTarget&&(e.outputRenderTarget.skipInitialClear=t)}this.updateTransformMatrix(),this.onBeforeCameraRenderObservable.notifyObservers(this.activeCamera),this._evaluateActiveMeshes();for(let e=0;e<this._softwareSkinnedMeshes.length;e++){let t=this._softwareSkinnedMeshes.data[e];t.applySkeleton(t.skeleton)}for(let s of(this.onBeforeRenderTargetsRenderObservable.notifyObservers(this),this._renderTargets.concatWithNoDuplicate(this._materialsRenderTargets),e.customRenderTargets&&e.customRenderTargets.length>0&&this._renderTargets.concatWithNoDuplicate(e.customRenderTargets),t&&t.customRenderTargets&&t.customRenderTargets.length>0&&this._renderTargets.concatWithNoDuplicate(t.customRenderTargets),this.environmentTexture&&this.environmentTexture.isRenderTarget&&this._renderTargets.pushNoDuplicate(this.environmentTexture),this._gatherActiveCameraRenderTargetsStage))s.action(this._renderTargets);let a=!1;if(this.renderTargetsEnabled){if(this._intermediateRendering=!0,this._renderTargets.length>0){i.S0.StartPerformanceCounter("Render targets",this._renderTargets.length>0);for(let e=0;e<this._renderTargets.length;e++){let t=this._renderTargets.data[e];if(t._shouldRender()){this._renderId++;let e=t.activeCamera&&t.activeCamera!==this.activeCamera;t.render(e,this.dumpNextRenderTargets),a=!0}}i.S0.EndPerformanceCounter("Render targets",this._renderTargets.length>0),this._renderId++}for(let e of this._cameraDrawRenderTargetStage)a=e.action(this.activeCamera)||a;this._intermediateRendering=!1}for(let t of(this._engine.currentRenderPassId=e.outputRenderTarget?.renderPassId??e.renderPassId??0,a&&!this.prePass&&(this._bindFrameBuffer(this._activeCamera,!1),this.updateTransformMatrix()),this.onAfterRenderTargetsRenderObservable.notifyObservers(this),!this.postProcessManager||e._multiviewTexture||this.prePass||this.postProcessManager._prepareFrame(),this._beforeCameraDrawStage))t.action(this.activeCamera);for(let e of(this.onBeforeDrawPhaseObservable.notifyObservers(this),r.snapshotRendering&&1===r.snapshotRenderingMode&&this.finalizeSceneUbo(),this._renderingManager.render(null,null,!0,!0),this.onAfterDrawPhaseObservable.notifyObservers(this),this._afterCameraDrawStage))e.action(this.activeCamera);if(this.postProcessManager&&!e._multiviewTexture){let t=e.outputRenderTarget?e.outputRenderTarget.renderTarget:void 0;this.postProcessManager._finalizeFrame(e.isIntermediate,t)}for(let e of this._afterCameraPostProcessStage)e.action(this.activeCamera);this._renderTargets.reset(),this.onAfterCameraRenderObservable.notifyObservers(this.activeCamera)}_processSubCameras(e,t=!0){if(0===e.cameraRigMode||e._renderingMultiview){e._renderingMultiview&&!this._multiviewSceneUbo&&this._createMultiviewUbo(),this._renderForCamera(e,void 0,t),this.onAfterRenderCameraObservable.notifyObservers(e);return}if(e._useMultiviewToSingleView)this._renderMultiviewToSingleView(e);else{this.onBeforeCameraRenderObservable.notifyObservers(e);for(let t=0;t<e._rigCameras.length;t++)this._renderForCamera(e._rigCameras[t],e)}this._activeCamera=e,this.updateTransformMatrix(),this.onAfterRenderCameraObservable.notifyObservers(e)}_checkIntersections(){for(let e=0;e<this._meshesForIntersections.length;e++){let t=this._meshesForIntersections.data[e];if(t.actionManager)for(let e=0;t.actionManager&&e<t.actionManager.actions.length;e++){let s=t.actionManager.actions[e];if(12===s.trigger||13===s.trigger){let e=s.getTriggerParameter(),r=e.mesh?e.mesh:e,i=r.intersectsMesh(t,e.usePreciseIntersection),a=t._intersectionsInProgress.indexOf(r);i&&-1===a?12===s.trigger?(s._executeCurrent(m.X.CreateNew(t,void 0,r)),t._intersectionsInProgress.push(r)):13===s.trigger&&t._intersectionsInProgress.push(r):!i&&a>-1&&(13===s.trigger&&s._executeCurrent(m.X.CreateNew(t,void 0,r)),t.actionManager.hasSpecificTrigger(13,e=>r===(e.mesh?e.mesh:e))&&13!==s.trigger||t._intersectionsInProgress.splice(a,1))}}}}_advancePhysicsEngineStep(e){}_animate(e){}animate(){if(this._engine.isDeterministicLockStep()){let e=Math.max(k.MinDeltaTime,Math.min(this._engine.getDeltaTime(),k.MaxDeltaTime))+this._timeAccumulator,t=this._engine.getTimeStep(),s=1e3/t/1e3,r=0,i=this._engine.getLockstepMaxSteps(),a=Math.floor(e/t);for(a=Math.min(a,i);e>0&&r<a;)this.onBeforeStepObservable.notifyObservers(this),this._animationRatio=t*s,this._animate(t),this.onAfterAnimationsObservable.notifyObservers(this),this.physicsEnabled&&this._advancePhysicsEngineStep(t),this.onAfterStepObservable.notifyObservers(this),this._currentStepId++,r++,e-=t;this._timeAccumulator=e<0?0:e}else{let e=this.useConstantAnimationDeltaTime?16:Math.max(k.MinDeltaTime,Math.min(this._engine.getDeltaTime(),k.MaxDeltaTime));this._animationRatio=.06*e,this._animate(),this.onAfterAnimationsObservable.notifyObservers(this),this.physicsEnabled&&this._advancePhysicsEngineStep(e)}}_clear(){(this.autoClearDepthAndStencil||this.autoClear)&&this._engine.clear(this._clearColor,this.autoClear||this.forceWireframe||this.forcePointsCloud,this.autoClearDepthAndStencil,this.autoClearDepthAndStencil)}_checkCameraRenderTarget(e){if(e?.outputRenderTarget&&!e?.isRigCamera&&(e.outputRenderTarget._cleared=!1),e?.rigCameras?.length)for(let t=0;t<e.rigCameras.length;++t){let s=e.rigCameras[t].outputRenderTarget;s&&(s._cleared=!1)}}resetDrawCache(e){if(this.meshes)for(let t of this.meshes)t.resetDrawCache(e)}_renderWithFrameGraph(e=!0,t=!1){if(this.activeCamera=null,this._activeParticleSystems.reset(),this._activeSkeletons.reset(),e){for(let e of this.cameras)if(e.update(),0!==e.cameraRigMode)for(let t=0;t<e._rigCameras.length;t++)e._rigCameras[t].update()}for(let e of(this.onBeforeRenderObservable.notifyObservers(this),this._beforeClearStage))e.action();let s=this.getActiveMeshCandidates(),r=s.length;for(let e=0;e<r;e++){let t=s.data[e];!t.isBlocked&&(this._totalVertices.addCount(t.getTotalVertices(),!1),t.isReady()&&t.isEnabled()&&!t.scaling.hasAZeroComponent&&(t.computeWorldMatrix(),t.actionManager&&t.actionManager.hasSpecificTriggers2(12,13)&&this._meshesForIntersections.pushNoDuplicate(t)))}if(this.particlesEnabled)for(let e=0;e<this.particleSystems.length;e++){let t=this.particleSystems[e];if(!t.isStarted()||!t.emitter)continue;let s=t.emitter;(!s.position||s.isEnabled())&&(this._activeParticleSystems.push(t),t.animate())}this.frameGraph?.execute()}_renderRenderTarget(e,t,s=!1,r=!1){if(this._intermediateRendering=!0,e._shouldRender()){if(this._renderId++,this.activeCamera=t,!this.activeCamera)throw Error("Active camera not set");this._engine.setViewport(this.activeCamera.viewport),this.updateTransformMatrix(),e.render(s,r)}this._intermediateRendering=!1}render(e=!0,t=!1){if(!this.isDisposed){if(this.onReadyObservable.hasObservers()&&null===this._executeWhenReadyTimeoutId&&this._checkIsReady(),this._frameId++,this._defaultFrameBufferCleared=!1,this._checkCameraRenderTarget(this.activeCamera),this.activeCameras?.length)for(let e of this.activeCameras)this._checkCameraRenderTarget(e);for(let e of(this._registerTransientComponents(),this._activeParticles.fetchNewFrame(),this._totalVertices.fetchNewFrame(),this._activeIndices.fetchNewFrame(),this._activeBones.fetchNewFrame(),this._meshesForIntersections.reset(),this.resetCachedMaterial(),this.onBeforeAnimationsObservable.notifyObservers(this),this.actionManager&&this.actionManager.processTrigger(11),t||this.animate(),this._beforeCameraUpdateStage))e.action();if(e){if(this.activeCameras&&this.activeCameras.length>0)for(let e=0;e<this.activeCameras.length;e++){let t=this.activeCameras[e];if(t.update(),0!==t.cameraRigMode)for(let e=0;e<t._rigCameras.length;e++)t._rigCameras[e].update()}else if(this.activeCamera&&(this.activeCamera.update(),0!==this.activeCamera.cameraRigMode))for(let e=0;e<this.activeCamera._rigCameras.length;e++)this.activeCamera._rigCameras[e].update()}if(this.customRenderFunction)this._renderId++,this._engine.currentRenderPassId=0,this.customRenderFunction(e,t);else{this.onBeforeRenderObservable.notifyObservers(this),this.onBeforeRenderTargetsRenderObservable.notifyObservers(this);let e=this.activeCameras?.length?this.activeCameras[0]:this.activeCamera;if(this.renderTargetsEnabled){i.S0.StartPerformanceCounter("Custom render targets",this.customRenderTargets.length>0);for(let t=0;t<this.customRenderTargets.length;t++){let s=this.customRenderTargets[t],r=s.activeCamera||this.activeCamera;this._renderRenderTarget(s,r,e!==r,this.dumpNextRenderTargets)}i.S0.EndPerformanceCounter("Custom render targets",this.customRenderTargets.length>0),this._renderId++}for(let t of(this._engine.currentRenderPassId=e?.renderPassId??0,this.activeCamera=e,this._activeCamera&&22!==this._activeCamera.cameraRigMode&&!this.prePass&&this._bindFrameBuffer(this._activeCamera,!1),this.onAfterRenderTargetsRenderObservable.notifyObservers(this),this._beforeClearStage))t.action();for(let e of(this._clearFrameBuffer(this.activeCamera),this._gatherRenderTargetsStage))e.action(this._renderTargets);if(this.activeCameras&&this.activeCameras.length>0)for(let e=0;e<this.activeCameras.length;e++)this._processSubCameras(this.activeCameras[e],e>0);else{if(!this.activeCamera)throw Error("No camera defined");this._processSubCameras(this.activeCamera,!!this.activeCamera.outputRenderTarget)}}for(let e of(this._checkIntersections(),this._afterRenderStage))e.action();if(this.afterRender&&this.afterRender(),this.onAfterRenderObservable.notifyObservers(this),this._toBeDisposed.length){for(let e=0;e<this._toBeDisposed.length;e++){let t=this._toBeDisposed[e];t&&t.dispose()}this._toBeDisposed.length=0}this.dumpNextRenderTargets&&(this.dumpNextRenderTargets=!1),this._activeBones.addCount(0,!0),this._activeIndices.addCount(0,!0),this._activeParticles.addCount(0,!0),this._engine.restoreDefaultFramebuffer()}}freezeMaterials(){for(let e=0;e<this.materials.length;e++)this.materials[e].freeze()}unfreezeMaterials(){for(let e=0;e<this.materials.length;e++)this.materials[e].unfreeze()}dispose(){if(this.isDisposed)return;if(this.beforeRender=null,this.afterRender=null,this.metadata=null,this.skeletons.length=0,this.morphTargetManagers.length=0,this._transientComponents.length=0,this._isReadyForMeshStage.clear(),this._beforeEvaluateActiveMeshStage.clear(),this._evaluateSubMeshStage.clear(),this._preActiveMeshStage.clear(),this._cameraDrawRenderTargetStage.clear(),this._beforeCameraDrawStage.clear(),this._beforeRenderTargetDrawStage.clear(),this._beforeRenderingGroupDrawStage.clear(),this._beforeRenderingMeshStage.clear(),this._afterRenderingMeshStage.clear(),this._afterRenderingGroupDrawStage.clear(),this._afterCameraDrawStage.clear(),this._afterRenderTargetDrawStage.clear(),this._afterRenderStage.clear(),this._beforeCameraUpdateStage.clear(),this._beforeClearStage.clear(),this._gatherRenderTargetsStage.clear(),this._gatherActiveCameraRenderTargetsStage.clear(),this._pointerMoveStage.clear(),this._pointerDownStage.clear(),this._pointerUpStage.clear(),this.importedMeshesFiles=[],this._activeAnimatables&&this.stopAllAnimations){for(let e of this._activeAnimatables)e.onAnimationEndObservable.clear(),e.onAnimationEnd=null;this.stopAllAnimations()}for(let e of(this.resetCachedMaterial(),this.activeCamera&&(this.activeCamera._activeMeshes.dispose(),this.activeCamera=null),this.activeCameras=null,this._activeMeshes.dispose(),this._renderingManager.dispose(),this._processedMaterials.dispose(),this._activeParticleSystems.dispose(),this._activeSkeletons.dispose(),this._softwareSkinnedMeshes.dispose(),this._renderTargets.dispose(),this._materialsRenderTargets.dispose(),this._registeredForLateAnimationBindings.dispose(),this._meshesForIntersections.dispose(),this._toBeDisposed.length=0,this._activeRequests.slice()))e.abort();this._activeRequests.length=0;try{this.onDisposeObservable.notifyObservers(this)}catch(e){B.V.Error("An error occurred while calling onDisposeObservable!",e)}if(this.detachControl(),this._engine.getInputElement())for(let e=0;e<this.cameras.length;e++)this.cameras[e].detachControl();this._disposeList(this.animationGroups),this._disposeList(this.lights),this._defaultMaterial&&this._defaultMaterial.dispose(),this._disposeList(this.multiMaterials),this._disposeList(this.materials),this._disposeList(this.meshes,e=>e.dispose(!0)),this._disposeList(this.transformNodes,e=>e.dispose(!0));let e=this.cameras;this._disposeList(e),this._disposeList(this.particleSystems),this._disposeList(this.postProcesses),this._disposeList(this.textures),this._disposeList(this.morphTargetManagers),this._sceneUbo.dispose(),this._multiviewSceneUbo&&this._multiviewSceneUbo.dispose(),this.postProcessManager.dispose(),this._disposeList(this._components);let t=this._engine.scenes.indexOf(this);if(t>-1&&this._engine.scenes.splice(t,1),p.q._LastCreatedScene===this){p.q._LastCreatedScene=null;let e=p.q.Instances.length-1;for(;e>=0;){let t=p.q.Instances[e];if(t.scenes.length>0){p.q._LastCreatedScene=t.scenes[this._engine.scenes.length-1];break}e--}}(t=this._engine._virtualScenes.indexOf(this))>-1&&this._engine._virtualScenes.splice(t,1),this._engine.wipeCaches(!0),this.onDisposeObservable.clear(),this.onBeforeRenderObservable.clear(),this.onAfterRenderObservable.clear(),this.onBeforeRenderTargetsRenderObservable.clear(),this.onAfterRenderTargetsRenderObservable.clear(),this.onAfterStepObservable.clear(),this.onBeforeStepObservable.clear(),this.onBeforeActiveMeshesEvaluationObservable.clear(),this.onAfterActiveMeshesEvaluationObservable.clear(),this.onBeforeParticlesRenderingObservable.clear(),this.onAfterParticlesRenderingObservable.clear(),this.onBeforeDrawPhaseObservable.clear(),this.onAfterDrawPhaseObservable.clear(),this.onBeforeAnimationsObservable.clear(),this.onAfterAnimationsObservable.clear(),this.onDataLoadedObservable.clear(),this.onBeforeRenderingGroupObservable.clear(),this.onAfterRenderingGroupObservable.clear(),this.onMeshImportedObservable.clear(),this.onBeforeCameraRenderObservable.clear(),this.onAfterCameraRenderObservable.clear(),this.onAfterRenderCameraObservable.clear(),this.onReadyObservable.clear(),this.onNewCameraAddedObservable.clear(),this.onCameraRemovedObservable.clear(),this.onNewLightAddedObservable.clear(),this.onLightRemovedObservable.clear(),this.onNewGeometryAddedObservable.clear(),this.onGeometryRemovedObservable.clear(),this.onNewTransformNodeAddedObservable.clear(),this.onTransformNodeRemovedObservable.clear(),this.onNewMeshAddedObservable.clear(),this.onMeshRemovedObservable.clear(),this.onNewSkeletonAddedObservable.clear(),this.onSkeletonRemovedObservable.clear(),this.onNewMaterialAddedObservable.clear(),this.onNewMultiMaterialAddedObservable.clear(),this.onMaterialRemovedObservable.clear(),this.onMultiMaterialRemovedObservable.clear(),this.onNewTextureAddedObservable.clear(),this.onTextureRemovedObservable.clear(),this.onPrePointerObservable.clear(),this.onPointerObservable.clear(),this.onPreKeyboardObservable.clear(),this.onKeyboardObservable.clear(),this.onActiveCameraChanged.clear(),this.onScenePerformancePriorityChangedObservable.clear(),this.onClearColorChangedObservable.clear(),this.onEnvironmentTextureChangedObservable.clear(),this.onMeshUnderPointerUpdatedObservable.clear(),this._isDisposed=!0}_disposeList(e,t){let s=e.slice(0);for(let e of(t=t??(e=>e.dispose()),s))t(e);e.length=0}get isDisposed(){return this._isDisposed}clearCachedVertexData(){for(let e=0;e<this.meshes.length;e++){let t=this.meshes[e].geometry;t&&t.clearCachedData()}}cleanCachedTextureBuffer(){for(let e of this.textures)e._buffer&&(e._buffer=null)}getWorldExtends(e){let t=new d.Pq(Number.MAX_VALUE,Number.MAX_VALUE,Number.MAX_VALUE),s=new d.Pq(-Number.MAX_VALUE,-Number.MAX_VALUE,-Number.MAX_VALUE);for(let r of(e=e||(()=>!0),this.meshes.filter(e))){if(r.computeWorldMatrix(!0),!r.subMeshes||0===r.subMeshes.length||r.infiniteDistance)continue;let e=r.getBoundingInfo(),i=e.boundingBox.minimumWorld,a=e.boundingBox.maximumWorld;d.Pq.CheckExtends(i,t,s),d.Pq.CheckExtends(a,t,s)}return{min:t,max:s}}createPickingRay(e,t,s,r,i=!1){throw(0,M.n)("Ray")}createPickingRayToRef(e,t,s,r,i,a=!1,n=!1){throw(0,M.n)("Ray")}createPickingRayInCameraSpace(e,t,s){throw(0,M.n)("Ray")}createPickingRayInCameraSpaceToRef(e,t,s,r){throw(0,M.n)("Ray")}pick(e,t,s,r,i,a){let n=(0,M.n)("Ray",!0);return n&&B.V.Warn(n),new g.G}pickWithBoundingInfo(e,t,s,r,i){let a=(0,M.n)("Ray",!0);return a&&B.V.Warn(a),new g.G}pickWithRay(e,t,s,r){throw(0,M.n)("Ray")}multiPick(e,t,s,r,i){throw(0,M.n)("Ray")}multiPickWithRay(e,t,s){throw(0,M.n)("Ray")}setPointerOverMesh(e,t,s){this._inputManager.setPointerOverMesh(e,t,s)}getPointerOverMesh(){return this._inputManager.getPointerOverMesh()}_rebuildGeometries(){for(let e of this.geometries)e._rebuild();for(let e of this.meshes)e._rebuild();for(let e of(this.postProcessManager&&this.postProcessManager._rebuild(),this._components))e.rebuild();for(let e of this.particleSystems)e.rebuild();if(this.spriteManagers)for(let e of this.spriteManagers)e.rebuild()}_rebuildTextures(){for(let e of this.textures)e._rebuild(!0);this.markAllMaterialsAsDirty(1)}_getByTags(e,t,s){if(void 0===t)return e;let r=[];for(let i in e){let a=e[i];l.Y&&l.Y.MatchesQuery(a,t)&&(!s||s(a))&&r.push(a)}return r}getMeshesByTags(e,t){return this._getByTags(this.meshes,e,t)}getCamerasByTags(e,t){return this._getByTags(this.cameras,e,t)}getLightsByTags(e,t){return this._getByTags(this.lights,e,t)}getMaterialByTags(e,t){return this._getByTags(this.materials,e,t).concat(this._getByTags(this.multiMaterials,e,t))}getTransformNodesByTags(e,t){return this._getByTags(this.transformNodes,e,t)}setRenderingOrder(e,t=null,s=null,r=null){this._renderingManager.setRenderingOrder(e,t,s,r)}setRenderingAutoClearDepthStencil(e,t,s=!0,r=!0){this._renderingManager.setRenderingAutoClearDepthStencil(e,t,s,r)}getAutoClearDepthStencilSetup(e){return this._renderingManager.getAutoClearDepthStencilSetup(e)}_forceBlockMaterialDirtyMechanism(e){this._blockMaterialDirtyMechanism=e}get blockMaterialDirtyMechanism(){return this._blockMaterialDirtyMechanism}set blockMaterialDirtyMechanism(e){this._blockMaterialDirtyMechanism!==e&&(this._blockMaterialDirtyMechanism=e,e||this.markAllMaterialsAsDirty(127))}markAllMaterialsAsDirty(e,t){if(!this._blockMaterialDirtyMechanism)for(let s of this.materials)(!t||t(s))&&s.markAsDirty(e)}_loadFile(e,t,s,r,i,a,n){let o=(0,A.zU)(e,t,s,r?this.offlineProvider:void 0,i,a,n);return this._activeRequests.push(o),o.onCompleteObservable.add(e=>{this._activeRequests.splice(this._activeRequests.indexOf(e),1)}),o}async _loadFileAsync(e,t,s,r,i){return await new Promise((a,n)=>{this._loadFile(e,e=>{a(e)},t,s,r,(e,t)=>{n(t)},i)})}_requestFile(e,t,s,r,i,a,n){let o=(0,A.sh)(e,t,s,r?this.offlineProvider:void 0,i,a,n);return this._activeRequests.push(o),o.onCompleteObservable.add(e=>{this._activeRequests.splice(this._activeRequests.indexOf(e),1)}),o}async _requestFileAsync(e,t,s,r,i){return await new Promise((a,n)=>{this._requestFile(e,e=>{a(e)},t,s,r,e=>{n(e)},i)})}_readFile(e,t,s,r,i){let a=(0,A.NJ)(e,t,s,r,i);return this._activeRequests.push(a),a.onCompleteObservable.add(e=>{this._activeRequests.splice(this._activeRequests.indexOf(e),1)}),a}async _readFileAsync(e,t,s){return await new Promise((r,i)=>{this._readFile(e,e=>{r(e)},t,s,e=>{i(e)})})}getPerfCollector(){throw(0,M.n)("performanceViewerSceneExtension")}setActiveCameraByID(e){return this.setActiveCameraById(e)}getMaterialByID(e){return this.getMaterialById(e)}getLastMaterialByID(e){return this.getLastMaterialById(e)}getTextureByUniqueID(e){return this.getTextureByUniqueId(e)}getCameraByID(e){return this.getCameraById(e)}getCameraByUniqueID(e){return this.getCameraByUniqueId(e)}getBoneByID(e){return this.getBoneById(e)}getLightByID(e){return this.getLightById(e)}getLightByUniqueID(e){return this.getLightByUniqueId(e)}getParticleSystemByID(e){return this.getParticleSystemById(e)}getGeometryByID(e){return this.getGeometryById(e)}getMeshByID(e){return this.getMeshById(e)}getMeshByUniqueID(e){return this.getMeshByUniqueId(e)}getLastMeshByID(e){return this.getLastMeshById(e)}getMeshesByID(e){return this.getMeshesById(e)}getTransformNodeByID(e){return this.getTransformNodeById(e)}getTransformNodeByUniqueID(e){return this.getTransformNodeByUniqueId(e)}getTransformNodesByID(e){return this.getTransformNodesById(e)}getNodeByID(e){return this.getNodeById(e)}getLastEntryByID(e){return this.getLastEntryById(e)}getLastSkeletonByID(e){return this.getLastSkeletonById(e)}}k.FOGMODE_NONE=0,k.FOGMODE_EXP=1,k.FOGMODE_EXP2=2,k.FOGMODE_LINEAR=3,k.MinDeltaTime=1,k.MaxDeltaTime=1e3,k._OriginalDefaultMaterialFactory=k.DefaultMaterialFactory,(0,I.Y5)("BABYLON.Scene",k)}}]);