{"version": 3, "file": "webAudioMainOut.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/webAudio/webAudioMainOut.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAE9D,OAAO,EAAE,2BAA2B,EAAE,MAAM,yCAAyC,CAAC;AAItF,gBAAgB;AAChB,MAAM,OAAO,gBAAiB,SAAQ,aAAa;IAO/C,gBAAgB;IAChB,YAAmB,MAAuB;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEd,IAAI,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC5B,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;IACvC,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAW,OAAO,CAAC,KAAe;QAC9B,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED,gBAAgB;IAChB,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;IACpC,CAAC;IAED,gBAAgB;IAChB,IAAW,MAAM,CAAC,KAAa;QAC3B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;IACrC,CAAC;IAED,IAAY,gBAAgB;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;IACzC,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED,gBAAgB;IACT,SAAS,CAAC,KAAa,EAAE,UAAyD,IAAI;QACzF,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAEO,YAAY,CAAC,QAAkB;QACnC,IAAI,IAAI,CAAC,SAAS,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;QAC7B,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAExC,IAAI,CAAC,OAAO,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE3E,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport { _MainAudioOut } from \"../abstractAudio/mainAudioOut\";\nimport type { IAudioParameterRampOptions } from \"../audioParameter\";\nimport { _WebAudioParameterComponent } from \"./components/webAudioParameterComponent\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\nimport type { IWebAudioInNode } from \"./webAudioNode\";\n\n/** @internal */\nexport class _WebAudioMainOut extends _MainAudioOut implements IWebAudioInNode {\n    private _gainNode: GainNode;\n    private _volume: _WebAudioParameterComponent;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n\n        this._setGainNode(new GainNode(engine._audioContext));\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._volume.dispose();\n        this._gainNode.disconnect();\n        this._destinationNode.disconnect();\n    }\n\n    /** @internal */\n    public get _inNode(): GainNode {\n        return this._gainNode;\n    }\n\n    public set _inNode(value: GainNode) {\n        if (this._gainNode === value) {\n            return;\n        }\n\n        this._setGainNode(value);\n    }\n\n    /** @internal */\n    public get volume(): number {\n        return this._volume.targetValue;\n    }\n\n    /** @internal */\n    public set volume(value: number) {\n        this._volume.targetValue = value;\n    }\n\n    private get _destinationNode(): AudioNode {\n        return this.engine._audioDestination;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_WebAudioMainOut\";\n    }\n\n    /** @internal */\n    public setVolume(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        this._volume.setTargetValue(value, options);\n    }\n\n    private _setGainNode(gainNode: GainNode): void {\n        if (this._gainNode === gainNode) {\n            return;\n        }\n\n        this._gainNode?.disconnect();\n        gainNode.connect(this._destinationNode);\n\n        this._volume = new _WebAudioParameterComponent(this.engine, gainNode.gain);\n\n        this._gainNode = gainNode;\n    }\n}\n"]}