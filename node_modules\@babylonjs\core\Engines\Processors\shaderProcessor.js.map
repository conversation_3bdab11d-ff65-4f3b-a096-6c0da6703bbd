{"version": 3, "file": "shaderProcessor.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderProcessor.ts"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,6BAA6B,EAAE,MAAM,uDAAuD,CAAC;AACtG,OAAO,EAAE,sBAAsB,EAAE,MAAM,gDAAgD,CAAC;AACxF,OAAO,EAAE,uBAAuB,EAAE,MAAM,iDAAiD,CAAC;AAC1F,OAAO,EAAE,sBAAsB,EAAE,MAAM,sCAAsC,CAAC;AAC9E,OAAO,EAAE,8BAA8B,EAAE,MAAM,wDAAwD,CAAC;AAExG,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAOlD,OAAO,EAAE,iBAAiB,EAAE,MAAM,6BAA6B,CAAC;AAGhE,MAAM,OAAO,GAAG,uBAAuB,CAAC;AACxC,MAAM,aAAa,GAAG,uBAAuB,CAAC;AAC9C,MAAM,kBAAkB,GAAG,0CAA0C,CAAC;AACtE,MAAM,eAAe,GAAG,UAAU,CAAC;AACnC,MAAM,WAAW,GAAG,mBAAmB,CAAC;AACxC,MAAM,MAAM,GAAG,QAAQ,CAAC;AACxB,MAAM,eAAe,GAAuB,EAAE,CAAC;AAE/C,MAAM,eAAe,GAAG,mDAAmD,CAAC;AAE5E,gBAAgB;AAChB,MAAM,UAAU,UAAU,CAAC,OAA4B;IACnD,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC;QAC3D,OAAO,CAAC,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACnE,CAAC;AACL,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,OAAO,CAAC,UAAkB,EAAE,OAA4B,EAAE,QAAqE,EAAE,MAAuB;IACpK,IAAI,OAAO,CAAC,SAAS,EAAE,oBAAoB,EAAE,CAAC;QAC1C,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;IACxF,CAAC;IACD,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,gBAAgB,EAAE,EAAE;QACtD,IAAI,OAAO,CAAC,wBAAwB,EAAE,CAAC;YACnC,gBAAgB,GAAG,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACvI,CAAC;QACD,MAAM,YAAY,GAAG,uBAAuB,CAAC,gBAAgB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAChF,QAAQ,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;AACP,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,UAAU,CAAC,UAAkB,EAAE,OAA4B,EAAE,QAAqE,EAAE,MAAsB;IACtK,IAAI,OAAO,CAAC,SAAS,EAAE,oBAAoB,EAAE,CAAC;QAC1C,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;IACxF,CAAC;IACD,eAAe,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,gBAAgB,EAAE,EAAE;QACtD,IAAI,OAAO,CAAC,wBAAwB,EAAE,CAAC;YACnC,gBAAgB,GAAG,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACvI,CAAC;QACD,MAAM,YAAY,GAAG,kBAAkB,CAAC,gBAAgB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3E,QAAQ,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;IAC7C,CAAC,CAAC,CAAC;AACP,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,QAAQ,CAAC,UAAkB,EAAE,YAAoB,EAAE,OAA4B;IAC3F,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;QAC3D,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC;IACxC,CAAC;IAED,OAAO,OAAO,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAClG,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAc,EAAE,OAA4B;IAClE,IAAI,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,CAAC;QACjC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,4BAA4B,GAAG,OAAO,CAAC,4BAA4B,CAAC;IAE1E,IAAI,MAAM,CAAC,OAAO,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QACjD,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAChC,MAAM,GAAG,4BAA4B,GAAG,MAAM,CAAC;QACnD,CAAC;aAAM,CAAC;YACJ,MAAM,GAAG,0BAA0B,GAAG,MAAM,CAAC;QACjD,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAChC,0BAA0B;YAC1B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,yBAAyB,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,gBAAgB,CAAC,UAAkB;IACxC,MAAM,KAAK,GAAG,iBAAiB,CAAC;IAEhC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAErC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;QACxB,OAAO,IAAI,6BAA6B,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IACrF,CAAC;IAED,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACrD,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,IAAI,aAAa,GAAG,CAAC,CAAC;IAEtB,KAAK,QAAQ,IAAI,SAAS,EAAE,CAAC;QACzB,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC;YACrB,MAAM;QACV,CAAC;IACL,CAAC;IAED,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;QACvB,OAAO,IAAI,6BAA6B,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;IAED,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;IAC7D,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;IAE3E,OAAO,IAAI,8BAA8B,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACvE,CAAC;AAED,SAAS,kBAAkB,CAAC,UAAkB;IAC1C,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IAExD,MAAM,OAAO,GAAG,sBAAsB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IAElE,MAAM,KAAK,GAAwC,EAAE,CAAC;IAEtD,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACtB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YAC3B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC3B,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5B,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAEjC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC;YAElB,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,uBAAuB,EAAE,CAAC,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;YAE1F,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;gBACzB,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;gBACzB,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAClD,CAAC;YAED,QAAQ,CAAC,WAAW,GAAG,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1E,QAAQ,CAAC,YAAY,GAAG,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAE3E,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;IACL,CAAC;IAED,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAErC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC7B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IAC1D,CAAC;IAED,gEAAgE;IAEhE,OAAO,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC1E,CAAC;AAED,SAAS,eAAe,CAAC,IAAY,EAAE,KAAa;IAChD,MAAM,IAAI,GAAG,IAAI,kBAAkB,EAAE,CAAC;IACtC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACzC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAEvC,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAEzG,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,IAAI,6BAA6B,CAAC,UAAU,CAAC,CAAC;IACxE,CAAC;SAAM,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,6BAA6B,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAC9E,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,cAAc,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;IACzD,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,MAAwB,EAAE,QAAiC,EAAE,MAAsB,EAAE,qBAAgD;IAC7J,IAAI,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC;IAC9B,OAAO,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,qBAAqB,CAAC,EAAE,CAAC;QACvD,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAElD,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;YACrB,MAAM,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;YACtC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjC,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;YACpD,OAAO;QACX,CAAC;aAAM,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAE1C,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjC,MAAM,GAAG,QAAQ,CAAC;QACtB,CAAC;IACL,CAAC;AACL,CAAC;AAED,SAAS,UAAU,CAAC,MAAwB,EAAE,QAAwB,EAAE,qBAAgD;IACpH,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC;QACpB,MAAM,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC;QAEhC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,MAAM,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBAC5B,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE3B,QAAQ,OAAO,EAAE,CAAC;oBACd,KAAK,QAAQ,CAAC,CAAC,CAAC;wBACZ,MAAM,WAAW,GAAG,IAAI,uBAAuB,EAAE,CAAC;wBAClD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAEpC,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;wBACxC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAClC,kBAAkB,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;wBACvE,MAAM;oBACV,CAAC;oBACD,KAAK,OAAO,CAAC;oBACb,KAAK,OAAO;wBACR,OAAO,IAAI,CAAC;oBAChB,KAAK,QAAQ;wBACT,OAAO,KAAK,CAAC;oBACjB,KAAK,SAAS,CAAC,CAAC,CAAC;wBACb,MAAM,WAAW,GAAG,IAAI,uBAAuB,EAAE,CAAC;wBAClD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAEpC,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;wBACxC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAClC,kBAAkB,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;wBACvE,MAAM;oBACV,CAAC;oBACD,KAAK,KAAK,CAAC,CAAC,CAAC;wBACT,MAAM,WAAW,GAAG,IAAI,uBAAuB,EAAE,CAAC;wBAClD,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;wBACxC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAEpC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAClC,kBAAkB,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAC;wBACvE,MAAM;oBACV,CAAC;gBACL,CAAC;gBACD,SAAS;YACb,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,cAAc,EAAE,CAAC;QACrC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEhC,4BAA4B;QAC5B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/C,OAAO,CAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEvC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC;IACL,CAAC;IACD,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,SAAS,qBAAqB,CAC1B,UAAkB,EAClB,aAAwC,EACxC,OAA4B,EAC5B,qBAAgD;IAEhD,MAAM,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;IACtC,MAAM,MAAM,GAAG,IAAI,gBAAgB,EAAE,CAAC;IAEtC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IACtB,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAEtC,8FAA8F;IAC9F,UAAU,CAAC,MAAM,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;IAEpD,YAAY;IACZ,OAAO,QAAQ,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;AAC3E,CAAC;AAED,SAAS,oBAAoB,CAAC,OAA4B,EAAE,MAAuB;IAC/E,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAChC,MAAM,aAAa,GAA8B,EAAE,CAAC;IAEpD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC3B,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;QACvE,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC/D,CAAC;IAED,IAAI,OAAO,CAAC,SAAS,EAAE,cAAc,gCAAwB,EAAE,CAAC;QAC5D,aAAa,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;IACpC,CAAC;IACD,aAAa,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;IAC/C,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;IAE7C,iBAAiB,CAAC,aAAa,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,uBAAuB,CAAC,CAAC;IAE1H,OAAO,aAAa,CAAC;AACzB,CAAC;AAED,SAAS,uBAAuB,CAAC,UAAkB,EAAE,OAA4B,EAAE,MAAuB;IACtG,IAAI,kBAAkB,GAAG,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAE/D,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACrB,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED,oBAAoB;IACpB,IAAI,OAAO,CAAC,SAAS,CAAC,cAAc,gCAAwB,IAAI,kBAAkB,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC9G,kBAAkB,GAAG,kBAAkB,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;QACvE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YAChC,OAAO,kBAAkB,CAAC;QAC9B,CAAC;IACL,CAAC;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAEhC,MAAM,aAAa,GAAG,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAE5D,yBAAyB;IACzB,IAAI,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QACjC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACnJ,CAAC;IAED,MAAM,qBAAqB,GAA8B,EAAE,CAAC;IAE5D,kBAAkB,GAAG,qBAAqB,CAAC,kBAAkB,EAAE,aAAa,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;IAE9G,kBAAkB;IAClB,IAAI,OAAO,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;QAClC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,aAAa,CAChD,kBAAkB,EAClB,OAAO,EACP,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,iBAAiB,EACzB,MAAM;YACF,CAAC,CAAC;gBACI,4BAA4B,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;aACrF;YACH,CAAC,CAAC,EAAE,EACR,aAAa,EACb,qBAAqB,CACxB,CAAC;IACN,CAAC;IAED,8CAA8C;IAC9C,IAAI,MAAM,EAAE,SAAS,CAAC,sBAAsB,EAAE,CAAC;QAC3C,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IACrE,CAAC;IAED,OAAO,kBAAkB,CAAC;AAC9B,CAAC;AAED,SAAS,kBAAkB,CAAC,UAAkB,EAAE,OAA4B,EAAE,MAAsB;IAChG,IAAI,kBAAkB,GAAG,UAAU,CAAC;IAEpC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAEhC,MAAM,aAAa,GAAG,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAE5D,yBAAyB;IACzB,IAAI,OAAO,CAAC,SAAS,EAAE,YAAY,EAAE,CAAC;QAClC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,kBAAkB,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;IACnJ,CAAC;IAED,MAAM,qBAAqB,GAA8B,EAAE,CAAC;IAE5D,kBAAkB,GAAG,qBAAqB,CAAC,kBAAkB,EAAE,aAAa,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;IAE9G,kBAAkB;IAClB,IAAI,OAAO,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QACnC,kBAAkB,GAAG,OAAO,CAAC,SAAS,CAAC,aAAa,CAChD,kBAAkB,EAClB,OAAO,EACP,OAAO,CAAC,UAAU,EAClB,OAAO,CAAC,iBAAiB,EACzB,MAAM;YACF,CAAC,CAAC;gBACI,4BAA4B,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;aACrF;YACH,CAAC,CAAC,EAAE,EACR,aAAa,EACb,qBAAqB,CACxB,CAAC;IACN,CAAC;IAED,8CAA8C;IAC9C,IAAI,MAAM,CAAC,SAAS,CAAC,sBAAsB,EAAE,CAAC;QAC1C,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IACrE,CAAC;IAED,OAAO,kBAAkB,CAAC;AAC9B,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,eAAe,CAAC,UAAkB,EAAE,OAA4B,EAAE,QAA6B;IAC3G,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IAC3B,IAAI,KAA8B,CAAC;IACnC,8CAA8C;IAC9C,OAAO,CAAC,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QAC5D,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED,IAAI,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;IACrC,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,CAAC;IAEzB,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;QAClC,IAAI,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAE3B,sBAAsB;QACtB,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACzC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;YACvD,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;gBACjC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAClF,CAAC;YACD,WAAW,GAAG,WAAW,GAAG,aAAa,CAAC;QAC9C,CAAC;QAED,IAAI,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5C,eAAe;YACf,IAAI,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAC/D,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACX,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAEnC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;oBACpD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC9C,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;oBAE/B,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC1D,CAAC;YACL,CAAC;YAED,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACX,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE7B,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;oBACnC,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC5C,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,IAAI,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,IAAI,oBAAoB,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBACnD,cAAc,GAAG,EAAE,CAAC;oBAEpB,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAClB,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvD,CAAC;oBAED,KAAK,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;wBACvC,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;4BAClC,kBAAkB;4BAClB,oBAAoB,GAAG,oBAAoB,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,GAAW,EAAE,EAAU,EAAE,EAAE;gCACzF,OAAO,EAAE,GAAG,KAAK,CAAC;4BACtB,CAAC,CAAC,CAAC;wBACP,CAAC;wBACD,cAAc,IAAI,oBAAoB,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC;oBAChF,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;wBAClC,kBAAkB;wBAClB,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,GAAW,EAAE,EAAU,EAAE,EAAE;4BAC7E,OAAO,EAAE,GAAG,KAAK,CAAC;wBACtB,CAAC,CAAC,CAAC;oBACP,CAAC;oBACD,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBACjE,CAAC;YACL,CAAC;YAED,UAAU;YACV,iFAAiF;YACjF,MAAM,QAAQ,GAAG,EAAE,CAAC;YACpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACvB,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC5C,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAClC,CAAC;gBACD,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;YACD,KAAK,GAAG,QAAQ,CAAC;YAEjB,cAAc,GAAG,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC7H,CAAC;aAAM,CAAC;YACJ,MAAM,gBAAgB,GAAG,OAAO,CAAC,iBAAiB,GAAG,iBAAiB,GAAG,WAAW,GAAG,KAAK,CAAC;YAE7F,kBAAkB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,CAAC,WAAW,EAAE,EAAE;gBAC1D,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,WAAqB,CAAC;gBAClE,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YACH,OAAO;QACX,CAAC;IACL,CAAC;IACD,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;IAE3B,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAE7B,IAAI,cAAc,EAAE,CAAC;QACjB,eAAe,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC/D,CAAC;SAAM,CAAC;QACJ,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC1B,CAAC;AACL,CAAC;AAED,gBAAgB;AAChB,MAAM,CAAC,MAAM,kBAAkB,GAAG;IAC9B;;;;;;;;;;OAUG;IACH,QAAQ,EAAE,CACN,GAAW,EACX,SAAqE,EACrE,UAAwC,EACxC,eAAkC,EAClC,cAAwB,EACxB,OAAmE,EACvD,EAAE;QACd,MAAM,WAAW,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;CACJ,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { ShaderCodeNode } from \"./shaderCodeNode\";\r\nimport { ShaderCodeCursor } from \"./shaderCodeCursor\";\r\nimport { ShaderCodeConditionNode } from \"./shaderCodeConditionNode\";\r\nimport { ShaderCodeTestNode } from \"./shaderCodeTestNode\";\r\nimport { ShaderDefineIsDefinedOperator } from \"./Expressions/Operators/shaderDefineIsDefinedOperator\";\r\nimport { ShaderDefineOrOperator } from \"./Expressions/Operators/shaderDefineOrOperator\";\r\nimport { ShaderDefineAndOperator } from \"./Expressions/Operators/shaderDefineAndOperator\";\r\nimport { ShaderDefineExpression } from \"./Expressions/shaderDefineExpression\";\r\nimport { ShaderDefineArithmeticOperator } from \"./Expressions/Operators/shaderDefineArithmeticOperator\";\r\nimport type { _IProcessingOptions } from \"./shaderProcessingOptions\";\r\nimport { _WarnImport } from \"../../Misc/devTools\";\r\nimport { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\n\r\nimport type { WebRequest } from \"../../Misc/webRequest\";\r\nimport type { LoadFileError } from \"../../Misc/fileTools\";\r\nimport type { IOfflineProvider } from \"../../Offline/IOfflineProvider\";\r\nimport type { IFileRequest } from \"../../Misc/fileRequest\";\r\nimport { _GetGlobalDefines } from \"../abstractEngine.functions\";\r\nimport type { AbstractEngine } from \"../abstractEngine\";\r\n\r\nconst RegexSe = /defined\\s*?\\((.+?)\\)/g;\r\nconst RegexSeRevert = /defined\\s*?\\[(.+?)\\]/g;\r\nconst RegexShaderInclude = /#include\\s?<(.+)>(\\((.*)\\))*(\\[(.*)\\])*/g;\r\nconst RegexShaderDecl = /__decl__/;\r\nconst RegexLightX = /light\\{X\\}.(\\w*)/g;\r\nconst RegexX = /\\{X\\}/g;\r\nconst ReusableMatches: RegExpMatchArray[] = [];\r\n\r\nconst MoveCursorRegex = /(#ifdef)|(#else)|(#elif)|(#endif)|(#ifndef)|(#if)/;\r\n\r\n/** @internal */\r\nexport function Initialize(options: _IProcessingOptions): void {\r\n    if (options.processor && options.processor.initializeShaders) {\r\n        options.processor.initializeShaders(options.processingContext);\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport function Process(sourceCode: string, options: _IProcessingOptions, callback: (migratedCode: string, codeBeforeMigration: string) => void, engine?: AbstractEngine) {\r\n    if (options.processor?.preProcessShaderCode) {\r\n        sourceCode = options.processor.preProcessShaderCode(sourceCode, options.isFragment);\r\n    }\r\n    ProcessIncludes(sourceCode, options, (codeWithIncludes) => {\r\n        if (options.processCodeAfterIncludes) {\r\n            codeWithIncludes = options.processCodeAfterIncludes(options.isFragment ? \"fragment\" : \"vertex\", codeWithIncludes, options.defines);\r\n        }\r\n        const migratedCode = ProcessShaderConversion(codeWithIncludes, options, engine);\r\n        callback(migratedCode, codeWithIncludes);\r\n    });\r\n}\r\n\r\n/** @internal */\r\nexport function PreProcess(sourceCode: string, options: _IProcessingOptions, callback: (migratedCode: string, codeBeforeMigration: string) => void, engine: AbstractEngine) {\r\n    if (options.processor?.preProcessShaderCode) {\r\n        sourceCode = options.processor.preProcessShaderCode(sourceCode, options.isFragment);\r\n    }\r\n    ProcessIncludes(sourceCode, options, (codeWithIncludes) => {\r\n        if (options.processCodeAfterIncludes) {\r\n            codeWithIncludes = options.processCodeAfterIncludes(options.isFragment ? \"fragment\" : \"vertex\", codeWithIncludes, options.defines);\r\n        }\r\n        const migratedCode = ApplyPreProcessing(codeWithIncludes, options, engine);\r\n        callback(migratedCode, codeWithIncludes);\r\n    });\r\n}\r\n\r\n/** @internal */\r\nexport function Finalize(vertexCode: string, fragmentCode: string, options: _IProcessingOptions): { vertexCode: string; fragmentCode: string } {\r\n    if (!options.processor || !options.processor.finalizeShaders) {\r\n        return { vertexCode, fragmentCode };\r\n    }\r\n\r\n    return options.processor.finalizeShaders(vertexCode, fragmentCode, options.processingContext);\r\n}\r\n\r\nfunction ProcessPrecision(source: string, options: _IProcessingOptions): string {\r\n    if (options.processor?.noPrecision) {\r\n        return source;\r\n    }\r\n\r\n    const shouldUseHighPrecisionShader = options.shouldUseHighPrecisionShader;\r\n\r\n    if (source.indexOf(\"precision highp float\") === -1) {\r\n        if (!shouldUseHighPrecisionShader) {\r\n            source = \"precision mediump float;\\n\" + source;\r\n        } else {\r\n            source = \"precision highp float;\\n\" + source;\r\n        }\r\n    } else {\r\n        if (!shouldUseHighPrecisionShader) {\r\n            // Moving highp to mediump\r\n            source = source.replace(\"precision highp float\", \"precision mediump float\");\r\n        }\r\n    }\r\n\r\n    return source;\r\n}\r\n\r\nfunction ExtractOperation(expression: string) {\r\n    const regex = /defined\\((.+)\\)/;\r\n\r\n    const match = regex.exec(expression);\r\n\r\n    if (match && match.length) {\r\n        return new ShaderDefineIsDefinedOperator(match[1].trim(), expression[0] === \"!\");\r\n    }\r\n\r\n    const operators = [\"==\", \"!=\", \">=\", \"<=\", \"<\", \">\"];\r\n    let operator = \"\";\r\n    let indexOperator = 0;\r\n\r\n    for (operator of operators) {\r\n        indexOperator = expression.indexOf(operator);\r\n\r\n        if (indexOperator > -1) {\r\n            break;\r\n        }\r\n    }\r\n\r\n    if (indexOperator === -1) {\r\n        return new ShaderDefineIsDefinedOperator(expression);\r\n    }\r\n\r\n    const define = expression.substring(0, indexOperator).trim();\r\n    const value = expression.substring(indexOperator + operator.length).trim();\r\n\r\n    return new ShaderDefineArithmeticOperator(define, operator, value);\r\n}\r\n\r\nfunction BuildSubExpression(expression: string): ShaderDefineExpression {\r\n    expression = expression.replace(RegexSe, \"defined[$1]\");\r\n\r\n    const postfix = ShaderDefineExpression.infixToPostfix(expression);\r\n\r\n    const stack: (string | ShaderDefineExpression)[] = [];\r\n\r\n    for (const c of postfix) {\r\n        if (c !== \"||\" && c !== \"&&\") {\r\n            stack.push(c);\r\n        } else if (stack.length >= 2) {\r\n            let v1 = stack[stack.length - 1],\r\n                v2 = stack[stack.length - 2];\r\n\r\n            stack.length -= 2;\r\n\r\n            const operator = c == \"&&\" ? new ShaderDefineAndOperator() : new ShaderDefineOrOperator();\r\n\r\n            if (typeof v1 === \"string\") {\r\n                v1 = v1.replace(RegexSeRevert, \"defined($1)\");\r\n            }\r\n\r\n            if (typeof v2 === \"string\") {\r\n                v2 = v2.replace(RegexSeRevert, \"defined($1)\");\r\n            }\r\n\r\n            operator.leftOperand = typeof v2 === \"string\" ? ExtractOperation(v2) : v2;\r\n            operator.rightOperand = typeof v1 === \"string\" ? ExtractOperation(v1) : v1;\r\n\r\n            stack.push(operator);\r\n        }\r\n    }\r\n\r\n    let result = stack[stack.length - 1];\r\n\r\n    if (typeof result === \"string\") {\r\n        result = result.replace(RegexSeRevert, \"defined($1)\");\r\n    }\r\n\r\n    // note: stack.length !== 1 if there was an error in the parsing\r\n\r\n    return typeof result === \"string\" ? ExtractOperation(result) : result;\r\n}\r\n\r\nfunction BuildExpression(line: string, start: number): ShaderCodeTestNode {\r\n    const node = new ShaderCodeTestNode();\r\n    const command = line.substring(0, start);\r\n    let expression = line.substring(start);\r\n\r\n    expression = expression.substring(0, (expression.indexOf(\"//\") + 1 || expression.length + 1) - 1).trim();\r\n\r\n    if (command === \"#ifdef\") {\r\n        node.testExpression = new ShaderDefineIsDefinedOperator(expression);\r\n    } else if (command === \"#ifndef\") {\r\n        node.testExpression = new ShaderDefineIsDefinedOperator(expression, true);\r\n    } else {\r\n        node.testExpression = BuildSubExpression(expression);\r\n    }\r\n\r\n    return node;\r\n}\r\n\r\nfunction MoveCursorWithinIf(cursor: ShaderCodeCursor, rootNode: ShaderCodeConditionNode, ifNode: ShaderCodeNode, preProcessorsFromCode: { [key: string]: string }) {\r\n    let line = cursor.currentLine;\r\n    while (MoveCursor(cursor, ifNode, preProcessorsFromCode)) {\r\n        line = cursor.currentLine;\r\n        const first5 = line.substring(0, 5).toLowerCase();\r\n\r\n        if (first5 === \"#else\") {\r\n            const elseNode = new ShaderCodeNode();\r\n            rootNode.children.push(elseNode);\r\n            MoveCursor(cursor, elseNode, preProcessorsFromCode);\r\n            return;\r\n        } else if (first5 === \"#elif\") {\r\n            const elifNode = BuildExpression(line, 5);\r\n\r\n            rootNode.children.push(elifNode);\r\n            ifNode = elifNode;\r\n        }\r\n    }\r\n}\r\n\r\nfunction MoveCursor(cursor: ShaderCodeCursor, rootNode: ShaderCodeNode, preProcessorsFromCode: { [key: string]: string }): boolean {\r\n    while (cursor.canRead) {\r\n        cursor.lineIndex++;\r\n        const line = cursor.currentLine;\r\n\r\n        if (line.indexOf(\"#\") >= 0) {\r\n            const matches = MoveCursorRegex.exec(line);\r\n\r\n            if (matches && matches.length) {\r\n                const keyword = matches[0];\r\n\r\n                switch (keyword) {\r\n                    case \"#ifdef\": {\r\n                        const newRootNode = new ShaderCodeConditionNode();\r\n                        rootNode.children.push(newRootNode);\r\n\r\n                        const ifNode = BuildExpression(line, 6);\r\n                        newRootNode.children.push(ifNode);\r\n                        MoveCursorWithinIf(cursor, newRootNode, ifNode, preProcessorsFromCode);\r\n                        break;\r\n                    }\r\n                    case \"#else\":\r\n                    case \"#elif\":\r\n                        return true;\r\n                    case \"#endif\":\r\n                        return false;\r\n                    case \"#ifndef\": {\r\n                        const newRootNode = new ShaderCodeConditionNode();\r\n                        rootNode.children.push(newRootNode);\r\n\r\n                        const ifNode = BuildExpression(line, 7);\r\n                        newRootNode.children.push(ifNode);\r\n                        MoveCursorWithinIf(cursor, newRootNode, ifNode, preProcessorsFromCode);\r\n                        break;\r\n                    }\r\n                    case \"#if\": {\r\n                        const newRootNode = new ShaderCodeConditionNode();\r\n                        const ifNode = BuildExpression(line, 3);\r\n                        rootNode.children.push(newRootNode);\r\n\r\n                        newRootNode.children.push(ifNode);\r\n                        MoveCursorWithinIf(cursor, newRootNode, ifNode, preProcessorsFromCode);\r\n                        break;\r\n                    }\r\n                }\r\n                continue;\r\n            }\r\n        }\r\n\r\n        const newNode = new ShaderCodeNode();\r\n        newNode.line = line;\r\n        rootNode.children.push(newNode);\r\n\r\n        // Detect additional defines\r\n        if (line[0] === \"#\" && line[1] === \"d\") {\r\n            const split = line.replace(\";\", \"\").split(\" \");\r\n            newNode.additionalDefineKey = split[1];\r\n\r\n            if (split.length === 3) {\r\n                newNode.additionalDefineValue = split[2];\r\n            }\r\n        }\r\n    }\r\n    return false;\r\n}\r\n\r\nfunction EvaluatePreProcessors(\r\n    sourceCode: string,\r\n    preprocessors: { [key: string]: string },\r\n    options: _IProcessingOptions,\r\n    preProcessorsFromCode: { [key: string]: string }\r\n): string {\r\n    const rootNode = new ShaderCodeNode();\r\n    const cursor = new ShaderCodeCursor();\r\n\r\n    cursor.lineIndex = -1;\r\n    cursor.lines = sourceCode.split(\"\\n\");\r\n\r\n    // Decompose (We keep it in 2 steps so it is easier to maintain and perf hit is insignificant)\r\n    MoveCursor(cursor, rootNode, preProcessorsFromCode);\r\n\r\n    // Recompose\r\n    return rootNode.process(preprocessors, options, preProcessorsFromCode);\r\n}\r\n\r\nfunction PreparePreProcessors(options: _IProcessingOptions, engine?: AbstractEngine): { [key: string]: string } {\r\n    const defines = options.defines;\r\n    const preprocessors: { [key: string]: string } = {};\r\n\r\n    for (const define of defines) {\r\n        const keyValue = define.replace(\"#define\", \"\").replace(\";\", \"\").trim();\r\n        const split = keyValue.split(\" \");\r\n        preprocessors[split[0]] = split.length > 1 ? split[1] : \"\";\r\n    }\r\n\r\n    if (options.processor?.shaderLanguage === ShaderLanguage.GLSL) {\r\n        preprocessors[\"GL_ES\"] = \"true\";\r\n    }\r\n    preprocessors[\"__VERSION__\"] = options.version;\r\n    preprocessors[options.platformName] = \"true\";\r\n\r\n    _GetGlobalDefines(preprocessors, engine?.isNDCHalfZRange, engine?.useReverseDepthBuffer, engine?.useExactSrgbConversions);\r\n\r\n    return preprocessors;\r\n}\r\n\r\nfunction ProcessShaderConversion(sourceCode: string, options: _IProcessingOptions, engine?: AbstractEngine): string {\r\n    let preparedSourceCode = ProcessPrecision(sourceCode, options);\r\n\r\n    if (!options.processor) {\r\n        return preparedSourceCode;\r\n    }\r\n\r\n    // Already converted\r\n    if (options.processor.shaderLanguage === ShaderLanguage.GLSL && preparedSourceCode.indexOf(\"#version 3\") !== -1) {\r\n        preparedSourceCode = preparedSourceCode.replace(\"#version 300 es\", \"\");\r\n        if (!options.processor.parseGLES3) {\r\n            return preparedSourceCode;\r\n        }\r\n    }\r\n\r\n    const defines = options.defines;\r\n\r\n    const preprocessors = PreparePreProcessors(options, engine);\r\n\r\n    // General pre processing\r\n    if (options.processor.preProcessor) {\r\n        preparedSourceCode = options.processor.preProcessor(preparedSourceCode, defines, preprocessors, options.isFragment, options.processingContext);\r\n    }\r\n\r\n    const preProcessorsFromCode: { [key: string]: string } = {};\r\n\r\n    preparedSourceCode = EvaluatePreProcessors(preparedSourceCode, preprocessors, options, preProcessorsFromCode);\r\n\r\n    // Post processing\r\n    if (options.processor.postProcessor) {\r\n        preparedSourceCode = options.processor.postProcessor(\r\n            preparedSourceCode,\r\n            defines,\r\n            options.isFragment,\r\n            options.processingContext,\r\n            engine\r\n                ? {\r\n                      drawBuffersExtensionDisabled: engine.getCaps().drawBuffersExtension ? false : true,\r\n                  }\r\n                : {},\r\n            preprocessors,\r\n            preProcessorsFromCode\r\n        );\r\n    }\r\n\r\n    // Inline functions tagged with #define inline\r\n    if (engine?._features.needShaderCodeInlining) {\r\n        preparedSourceCode = engine.inlineShaderCode(preparedSourceCode);\r\n    }\r\n\r\n    return preparedSourceCode;\r\n}\r\n\r\nfunction ApplyPreProcessing(sourceCode: string, options: _IProcessingOptions, engine: AbstractEngine): string {\r\n    let preparedSourceCode = sourceCode;\r\n\r\n    const defines = options.defines;\r\n\r\n    const preprocessors = PreparePreProcessors(options, engine);\r\n\r\n    // General pre processing\r\n    if (options.processor?.preProcessor) {\r\n        preparedSourceCode = options.processor.preProcessor(preparedSourceCode, defines, preprocessors, options.isFragment, options.processingContext);\r\n    }\r\n\r\n    const preProcessorsFromCode: { [key: string]: string } = {};\r\n\r\n    preparedSourceCode = EvaluatePreProcessors(preparedSourceCode, preprocessors, options, preProcessorsFromCode);\r\n\r\n    // Post processing\r\n    if (options.processor?.postProcessor) {\r\n        preparedSourceCode = options.processor.postProcessor(\r\n            preparedSourceCode,\r\n            defines,\r\n            options.isFragment,\r\n            options.processingContext,\r\n            engine\r\n                ? {\r\n                      drawBuffersExtensionDisabled: engine.getCaps().drawBuffersExtension ? false : true,\r\n                  }\r\n                : {},\r\n            preprocessors,\r\n            preProcessorsFromCode\r\n        );\r\n    }\r\n\r\n    // Inline functions tagged with #define inline\r\n    if (engine._features.needShaderCodeInlining) {\r\n        preparedSourceCode = engine.inlineShaderCode(preparedSourceCode);\r\n    }\r\n\r\n    return preparedSourceCode;\r\n}\r\n\r\n/** @internal */\r\nexport function ProcessIncludes(sourceCode: string, options: _IProcessingOptions, callback: (data: any) => void): void {\r\n    ReusableMatches.length = 0;\r\n    let match: RegExpMatchArray | null;\r\n    // stay back-compat to the old matchAll syntax\r\n    while ((match = RegexShaderInclude.exec(sourceCode)) !== null) {\r\n        ReusableMatches.push(match);\r\n    }\r\n\r\n    let returnValue = String(sourceCode);\r\n    let parts = [sourceCode];\r\n\r\n    let keepProcessing = false;\r\n\r\n    for (const match of ReusableMatches) {\r\n        let includeFile = match[1];\r\n\r\n        // Uniform declaration\r\n        if (includeFile.indexOf(\"__decl__\") !== -1) {\r\n            includeFile = includeFile.replace(RegexShaderDecl, \"\");\r\n            if (options.supportsUniformBuffers) {\r\n                includeFile = includeFile.replace(\"Vertex\", \"Ubo\").replace(\"Fragment\", \"Ubo\");\r\n            }\r\n            includeFile = includeFile + \"Declaration\";\r\n        }\r\n\r\n        if (options.includesShadersStore[includeFile]) {\r\n            // Substitution\r\n            let includeContent = options.includesShadersStore[includeFile];\r\n            if (match[2]) {\r\n                const splits = match[3].split(\",\");\r\n\r\n                for (let index = 0; index < splits.length; index += 2) {\r\n                    const source = new RegExp(splits[index], \"g\");\r\n                    const dest = splits[index + 1];\r\n\r\n                    includeContent = includeContent.replace(source, dest);\r\n                }\r\n            }\r\n\r\n            if (match[4]) {\r\n                const indexString = match[5];\r\n\r\n                if (indexString.indexOf(\"..\") !== -1) {\r\n                    const indexSplits = indexString.split(\"..\");\r\n                    const minIndex = parseInt(indexSplits[0]);\r\n                    let maxIndex = parseInt(indexSplits[1]);\r\n                    let sourceIncludeContent = includeContent.slice(0);\r\n                    includeContent = \"\";\r\n\r\n                    if (isNaN(maxIndex)) {\r\n                        maxIndex = options.indexParameters[indexSplits[1]];\r\n                    }\r\n\r\n                    for (let i = minIndex; i < maxIndex; i++) {\r\n                        if (!options.supportsUniformBuffers) {\r\n                            // Ubo replacement\r\n                            sourceIncludeContent = sourceIncludeContent.replace(RegexLightX, (str: string, p1: string) => {\r\n                                return p1 + \"{X}\";\r\n                            });\r\n                        }\r\n                        includeContent += sourceIncludeContent.replace(RegexX, i.toString()) + \"\\n\";\r\n                    }\r\n                } else {\r\n                    if (!options.supportsUniformBuffers) {\r\n                        // Ubo replacement\r\n                        includeContent = includeContent.replace(RegexLightX, (str: string, p1: string) => {\r\n                            return p1 + \"{X}\";\r\n                        });\r\n                    }\r\n                    includeContent = includeContent.replace(RegexX, indexString);\r\n                }\r\n            }\r\n\r\n            // Replace\r\n            // Split all parts on match[0] and intersperse the parts with the include content\r\n            const newParts = [];\r\n            for (const part of parts) {\r\n                const splitPart = part.split(match[0]);\r\n                for (let i = 0; i < splitPart.length - 1; i++) {\r\n                    newParts.push(splitPart[i]);\r\n                    newParts.push(includeContent);\r\n                }\r\n                newParts.push(splitPart[splitPart.length - 1]);\r\n            }\r\n            parts = newParts;\r\n\r\n            keepProcessing = keepProcessing || includeContent.indexOf(\"#include<\") >= 0 || includeContent.indexOf(\"#include <\") >= 0;\r\n        } else {\r\n            const includeShaderUrl = options.shadersRepository + \"ShadersInclude/\" + includeFile + \".fx\";\r\n\r\n            _FunctionContainer.loadFile(includeShaderUrl, (fileContent) => {\r\n                options.includesShadersStore[includeFile] = fileContent as string;\r\n                ProcessIncludes(parts.join(\"\"), options, callback);\r\n            });\r\n            return;\r\n        }\r\n    }\r\n    ReusableMatches.length = 0;\r\n\r\n    returnValue = parts.join(\"\");\r\n\r\n    if (keepProcessing) {\r\n        ProcessIncludes(returnValue.toString(), options, callback);\r\n    } else {\r\n        callback(returnValue);\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport const _FunctionContainer = {\r\n    /**\r\n     * Loads a file from a url\r\n     * @param url url to load\r\n     * @param onSuccess callback called when the file successfully loads\r\n     * @param onProgress callback called while file is loading (if the server supports this mode)\r\n     * @param offlineProvider defines the offline provider for caching\r\n     * @param useArrayBuffer defines a boolean indicating that date must be returned as ArrayBuffer\r\n     * @param onError callback called when the file fails to load\r\n     * @returns a file request object\r\n     * @internal\r\n     */\r\n    loadFile: (\r\n        url: string,\r\n        onSuccess: (data: string | ArrayBuffer, responseURL?: string) => void,\r\n        onProgress?: (ev: ProgressEvent) => void,\r\n        offlineProvider?: IOfflineProvider,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest, exception?: LoadFileError) => void\r\n    ): IFileRequest => {\r\n        throw _WarnImport(\"FileTools\");\r\n    },\r\n};\r\n"]}