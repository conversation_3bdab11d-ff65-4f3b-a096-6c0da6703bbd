{"version": 3, "file": "animationGroup.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Animations/animationGroup.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAKxC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAGrD,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAEpC,OAAO,cAAc,CAAC;AAEtB,OAAO,EAAE,iBAAiB,EAAE,qCAAoC;AAEhE;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAgB1B;;;OAGG;IACI,YAAY;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,YAA4B,MAAsB;QAAtB,WAAM,GAAN,MAAM,CAAgB;QAjBlD;;WAEG;QACa,aAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;IAcD,CAAC;IAEtD;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QACpC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC3D,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QAE9C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;CACJ;AAgBD;;GAEG;AACH,MAAM,OAAO,cAAc;IAgEvB;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAmC;QAC/C,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,WAAW,GAAG,KAAK;QACnC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC7D,OAAO;QACX,CAAC;QAED,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;QAE/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtF,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;oBACpB,UAAU,CAAC,OAAO,EAAE,CAAC;gBACzB,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;oBACrB,UAAU,CAAC,KAAK,EAAE,CAAC;gBACvB,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,wBAAwB;QAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnC,OAAO;QACX,CAAC;QAED,iFAAiF;QACjF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnD,UAAU,CAAC,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/B,EAAE,CAAC,CAAC;YACR,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACnE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAE1D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC1C,EAAE,KAAK,CAAC;YACZ,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa;QACzB,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;QACtC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB,CAAC;IAED,IAAW,EAAE,CAAC,KAAa;QACvB,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;QAEjB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAc;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAE5B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QACnD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAc;QAChC,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAAa;QAC3B,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAa;QAC9B,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAChD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;YACrD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;QACxC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,cAAc,CAAC,KAAwB;QAC9C,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAE7B,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,KAAK,CAAC;YACjE,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAuB;QAC5C,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAE5B,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC;YAChE,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACI,SAAS,CAAC,IAAa,EAAE,EAAW;QACvC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC;QAC1B,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC;QAEpB,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;QAEnF,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,oBAAoB,CAAC,eAAsC,EAAE,aAAa,GAAG,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,MAAe;QAC/H,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,GAAG,MAAM,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAE7C,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;QAClC,IAAI,QAAQ,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QAEjC,IAAI,SAAS,EAAE,CAAC;YACZ,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;gBAC3C,IAAI,cAAc,CAAC,IAAI,GAAG,UAAU,EAAE,CAAC;oBACnC,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC;gBACrC,CAAC;gBAED,IAAI,cAAc,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC;oBAC/B,QAAQ,GAAG,cAAc,CAAC,EAAE,CAAC;gBACjC,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAExH,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;YAC3C,IAAI,SAAS,EAAE,CAAC;gBACZ,cAAc,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACnD,CAAC;YAED,KAAK,MAAM,iBAAiB,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;gBAChE,oBAAoB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,SAAS,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACrG,CAAC;YAED,IAAI,aAAa,EAAE,CAAC;gBAChB,cAAc,CAAC,OAAO,EAAE,CAAC;YAC7B,CAAC;QACL,CAAC;QAED,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;;;;;;;OAQG;IACH;IACI,sCAAsC;IAC/B,IAAY,EACnB,QAAyB,IAAI,EAC7B,MAAM,GAAG,CAAC,CAAC,EACX,SAAS,GAAG,CAAC;QAHN,SAAI,GAAJ,IAAI,CAAQ;QApbf,wBAAmB,GAAG,IAAI,KAAK,EAAqB,CAAC;QACrD,iBAAY,GAAG,IAAI,KAAK,EAAc,CAAC;QACvC,UAAK,GAAG,MAAM,CAAC,SAAS,CAAC;QACzB,QAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QAGxB,gBAAW,GAAG,CAAC,CAAC;QAChB,mBAAc,GAAG,KAAK,CAAC;QACvB,gBAAW,GAAG,KAAK,CAAC;QACpB,YAAO,GAAG,CAAC,CAAC,CAAC;QACb,eAAU,GAAG,CAAC,CAAC;QACf,oBAAe,GAAsB,IAAI,CAAC;QAC1C,mBAAc,GAAqB,IAAI,CAAC;QACxC,0BAAqB,GAAG,CAAC,CAAC;QAC1B,iBAAY,GAAG,IAAI,CAAC;QAE5B,gBAAgB;QACT,qBAAgB,GAA8B,IAAI,CAAC;QAO1D;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAqB,CAAC;QAEtE;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAqB,CAAC;QAEvE;;WAEG;QACI,mCAA8B,GAAG,IAAI,UAAU,EAAkB,CAAC;QAEzE;;WAEG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAAkB,CAAC;QAExE;;WAEG;QACI,oCAA+B,GAAG,IAAI,UAAU,EAAkB,CAAC;QAE1E;;WAEG;QACI,mCAA8B,GAAG,IAAI,UAAU,EAAkB,CAAC;QAEzE;;WAEG;QACI,aAAQ,GAAQ,IAAI,CAAC;QAEpB,UAAK,GAAiC,IAAI,CAAC;QAwe3C,wBAAmB,GAAc,EAAE,CAAC;QA1GxC,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAiB,CAAC;QACrD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE1C,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,SAAoB,EAAE,MAAW;QACzD,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACtD,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC;QACxC,iBAAiB,CAAC,MAAM,GAAG,MAAM,CAAC;QAElC,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACzC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3C,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE,CAAC;YAChC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YAC/B,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,SAAoB;QAC/C,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;YACxE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC1D,IAAI,iBAAiB,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,aAA+B,IAAI,EAAE,WAA6B,IAAI;QACnF,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACrB,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,CAAC;QACD,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACnB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;QACxB,CAAC;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACnE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,iBAAiB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAErC,IAAI,QAAQ,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC;gBAC9B,MAAM,MAAM,GAAkB;oBAC1B,KAAK,EAAE,UAAU;oBACjB,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,SAAS,EAAE,QAAQ,CAAC,SAAS;oBAC7B,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,aAAa,EAAE,QAAQ,CAAC,aAAa;iBACxC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YAC9B,CAAC;YAED,IAAI,MAAM,CAAC,KAAK,GAAG,QAAQ,EAAE,CAAC;gBAC1B,MAAM,MAAM,GAAkB;oBAC1B,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;iBACtC,CAAC;gBACF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;QAEpB,OAAO,IAAI,CAAC;IAChB,CAAC;IAKO,YAAY,CAAC,UAAsB,EAAE,iBAAoC,EAAE,KAAa;QAC5F,UAAU,CAAC,eAAe,GAAG,GAAG,EAAE;YAC9B,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;YAElE,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO;YACX,CAAC;YAED,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;YAEvC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC1D,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;gBAC7B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;YACxC,CAAC;QACL,CAAC,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,IAAI,GAAG,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,IAAa,EAAE,EAAW,EAAE,UAAoB;QACvF,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEpC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACnE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAC/C,iBAAiB,CAAC,MAAM,EACxB,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAC7B,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EACtC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAChC,IAAI,EACJ,UAAU,EACV,SAAS,EACT,SAAS,EACT,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAC3D,CAAC;YACF,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YACjC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,UAAU,CAAC,cAAc,GAAG,GAAG,EAAE;gBAC7B,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBACjE,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;YAC/C,CAAC,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;QAEpC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,IAAI,CAAC,IAAc;QACtB,yCAAyC;QACzC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACnE,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC9B,CAAC;YACD,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,IAAI,CAAC,kBAAkB,GAAG,KAAK;QAClC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QACvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC/C,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;QACrE,CAAC;QAED,wDAAwD;QACxD,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACzE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACzD,IAAI,UAAU,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,GAAG,UAAU,CAAC;YAC5D,CAAC;iBAAM,IAAI,kBAAkB,EAAE,CAAC;gBAC5B,qHAAqH;gBACrH,kIAAkI;gBAClI,4FAA4F;gBAC5F,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,QAAQ,CAAC;QAEjD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACI,0BAA0B,CAAC,MAAc;QAC5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;QAC/B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,IAA0B;QACnD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,SAAS,CAAC,KAAa,EAAE,SAAS,GAAG,KAAK;QAC7C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;QAE7B,oBAAoB;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;IAChD,CAAC;IAEO,yBAAyB,CAAC,UAAsB,EAAE,kBAAkB,GAAG,KAAK;QAChF,8CAA8C;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAClD,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC;QAED,uDAAuD;QACvD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC5F,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACtB,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7D,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,OAAe,EAAE,eAAyC,EAAE,eAAe,GAAG,KAAK;QAC5F,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QACvB,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACvC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACvC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;QAC/C,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAClC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAE1B,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACrD,QAAQ,CAAC,oBAAoB,CACzB,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,EAC/E,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CACrF,CAAC;QACN,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACjC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEvD,mBAAmB,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC5C,KAAK,IAAI,sBAAsB,GAAG,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,sBAAsB,EAAE,EAAE,CAAC;YACrH,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;YAC1E,mBAAmB,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,GAAG,iBAAiB,CAAC,SAAS,EAAE,CAAC;QACnG,CAAC;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAED,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACjD,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED,UAAU;IACV;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,oBAAyB,EAAE,KAAY,EAAE,OAA+B;QACxF,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE,oBAAoB,CAAC,MAAM,EAAE,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACzI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtE,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACrE,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC/D,MAAM,EAAE,GAAG,iBAAiB,CAAC,QAAQ,CAAC;YACtC,IAAI,iBAAiB,CAAC,SAAS,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;gBACvD,yBAAyB;gBACzB,MAAM,WAAW,GAAG,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;gBACjD,IAAI,WAAW,EAAE,CAAC;oBACd,cAAc,CAAC,oBAAoB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAChE,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBAErE,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;oBACrB,cAAc,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,oBAAoB,CAAC,IAAI,KAAK,IAAI,IAAI,oBAAoB,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;YACzE,cAAc,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,oBAAoB,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YAChD,cAAc,CAAC,WAAW,GAAG,oBAAoB,CAAC,UAAU,CAAC;QACjE,CAAC;QACD,IAAI,oBAAoB,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACnD,cAAc,CAAC,cAAc,GAAG,oBAAoB,CAAC,aAAa,CAAC;QACvE,CAAC;QAED,IAAI,oBAAoB,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YAChD,cAAc,CAAC,WAAW,GAAG,oBAAoB,CAAC,UAAU,CAAC;QACjE,CAAC;QAED,IAAI,oBAAoB,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC5C,cAAc,CAAC,OAAO,GAAG,oBAAoB,CAAC,MAAM,CAAC;QACzD,CAAC;QAED,IAAI,oBAAoB,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC/C,cAAc,CAAC,UAAU,GAAG,oBAAoB,CAAC,SAAS,CAAC;QAC/D,CAAC;QAED,IAAI,oBAAoB,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACpD,cAAc,CAAC,eAAe,GAAG,oBAAoB,CAAC,cAAc,CAAC;QACzE,CAAC;QAED,IAAI,oBAAoB,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACnD,cAAc,CAAC,cAAc,GAAG,oBAAoB,CAAC,aAAa,CAAC;QACvE,CAAC;QAED,IAAI,oBAAoB,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC9C,cAAc,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC;QAC5D,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAqBD,gBAAgB;IACT,MAAM,CAAC,qBAAqB,CAC/B,oBAAoC,EACpC,uBAAqE,EACrE,KAAc,EACd,aAAa,GAAG,KAAK,EACrB,UAAmB;QAEnB,IAAI,OAA2C,CAAC;QAEhD,IAAI,OAAO,uBAAuB,KAAK,QAAQ,EAAE,CAAC;YAC9C,OAAO,GAAG,uBAAuB,CAAC;QACtC,CAAC;aAAM,CAAC;YACJ,OAAO,GAAG;gBACN,cAAc,EAAE,uBAAuB;gBACvC,KAAK,EAAE,KAAK;gBACZ,2BAA2B,EAAE,aAAa;gBAC1C,mBAAmB,EAAE,UAAU;aAClC,CAAC;QACN,CAAC;QAED,IAAI,cAAc,GAAG,oBAAoB,CAAC;QAC1C,IAAI,OAAO,CAAC,2BAA2B,EAAE,CAAC;YACtC,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,wBAAwB,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;QACzG,CAAC;QAED,MAAM,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAC7D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC7D,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACpD,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACxG,CAAC;QAED,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC;QAEjC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,4GAA4G;YAC5G,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC;YAC5B,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;YAE3B,MAAM,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;YAC7D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC7D,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBACpD,MAAM,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC;gBAC9C,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;gBAEjC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;oBACvB,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBACzB,CAAC;gBAED,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;oBACnC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;gBACrC,CAAC;YACL,CAAC;YAED,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC;YAC5B,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC;QAC5B,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,QAAQ,CAAC,oBAAoC,EAAE,OAAe,EAAE,KAAa,EAAE,IAAa,EAAE,mBAA6B;QACrI,MAAM,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAErF,OAAO,cAAc,CAAC,eAAe,CAAC,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC;IAC/F,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,eAAe,CAAC,cAA8B,EAAE,OAAe,EAAE,KAAa,EAAE,mBAA6B;QACvH,OAAO,cAAc,CAAC,WAAW,CAAC,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;IAClG,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,UAAU,CAAC,oBAAoC,EAAE,SAAiB,EAAE,OAAe,EAAE,IAAa,EAAE,mBAA6B;QAC3I,MAAM,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAErF,OAAO,cAAc,CAAC,iBAAiB,CAAC,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;IACrG,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,iBAAiB,CAAC,cAA8B,EAAE,SAAiB,EAAE,OAAe,EAAE,mBAA6B;QAC7H,OAAO,cAAc,CAAC,WAAW,CAAC,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;IACrG,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,WAAW,CAAC,cAA8B,EAAE,KAAa,EAAE,GAAW,EAAE,mBAA6B,EAAE,QAAQ,GAAG,KAAK;QACjI,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC;QAC5B,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QAE3B,MAAM,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAC7D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC7D,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAE1G,IAAI,QAAQ,EAAE,CAAC;gBACX,wEAAwE;gBACxE,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBACnC,SAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC;YAED,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YACjC,MAAM,OAAO,GAAoB,EAAE,CAAC;YAEpC,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,GAAG,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,CAAC;oBAChG,MAAM,MAAM,GAAkB;wBAC1B,KAAK,EAAE,GAAG,CAAC,KAAK;wBAChB,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK;wBACtD,SAAS,EAAE,GAAG,CAAC,SAAS;wBACxB,UAAU,EAAE,GAAG,CAAC,UAAU;wBAC1B,aAAa,EAAE,GAAG,CAAC,aAAa;wBAChC,aAAa,EAAE,GAAG,CAAC,aAAa;qBACnC,CAAC;oBACF,IAAI,UAAU,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;wBAClC,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC;oBAC9B,CAAC;oBACD,MAAM,CAAC,KAAK,IAAI,UAAU,CAAC;oBAC3B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzB,CAAC;YACL,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACpC,KAAK,EAAE,CAAC;gBACR,SAAS;YACb,CAAC;YAED,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;gBAC1B,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5B,CAAC;YAED,IAAI,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;gBACzC,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;YAC3C,CAAC;YAED,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACjC,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,wCAAwC;QACrF,CAAC;QAED,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC;QAC5B,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC;QAExB,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,WAAqB;QACjC,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,WAAW,EAAE,CAAC;YACd,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;YAC/B,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;YAC3B,GAAG,IAAI,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;YACzC,GAAG,IAAI,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC;YAC3C,GAAG,IAAI,+BAA+B,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YACzE,GAAG,IAAI,wBAAwB,GAAG,IAAI,CAAC,YAAY,CAAC;QACxD,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;CACJ", "sourcesContent": ["import type { Animatable } from \"./animatable.core\";\r\nimport { Animation } from \"./animation\";\r\nimport type { IMakeAnimationAdditiveOptions } from \"./animation\";\r\nimport type { IAnimationKey } from \"./animationKey\";\r\n\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { Node } from \"../node\";\r\n\r\nimport { Tags } from \"../Misc/tags\";\r\nimport type { AnimationGroupMask } from \"./animationGroupMask\";\r\nimport \"./animatable\";\r\nimport type { IAssetContainer } from \"core/IAssetContainer\";\r\nimport { UniqueIdGenerator } from \"core/Misc/uniqueIdGenerator\";\r\n\r\n/**\r\n * This class defines the direct association between an animation and a target\r\n */\r\nexport class TargetedAnimation {\r\n    /**\r\n     * Animation to perform\r\n     */\r\n    public animation: Animation;\r\n\r\n    /**\r\n     * Target to animate\r\n     */\r\n    public target: any;\r\n\r\n    /**\r\n     * Gets or sets the unique id of the targeted animation\r\n     */\r\n    public readonly uniqueId = UniqueIdGenerator.UniqueId;\r\n\r\n    /**\r\n     * Returns the string \"TargetedAnimation\"\r\n     * @returns \"TargetedAnimation\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"TargetedAnimation\";\r\n    }\r\n\r\n    /**\r\n     * Creates a new targeted animation\r\n     * @param parent The animation group to which the animation belongs\r\n     */\r\n    constructor(public readonly parent: AnimationGroup) {}\r\n\r\n    /**\r\n     * Serialize the object\r\n     * @returns the JSON object representing the current entity\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        serializationObject.animation = this.animation.serialize();\r\n        serializationObject.targetId = this.target.id;\r\n\r\n        return serializationObject;\r\n    }\r\n}\r\n\r\n/**\r\n * Options to be used when creating an additive group animation\r\n */\r\nexport interface IMakeAnimationGroupAdditiveOptions extends IMakeAnimationAdditiveOptions {\r\n    /**\r\n     * Defines if the animation group should be cloned or not (default is false)\r\n     */\r\n    cloneOriginalAnimationGroup?: boolean;\r\n    /**\r\n     * The name of the cloned animation group if cloneOriginalAnimationGroup is true\r\n     */\r\n    clonedAnimationGroupName?: string;\r\n}\r\n\r\n/**\r\n * Use this class to create coordinated animations on multiple targets\r\n */\r\nexport class AnimationGroup implements IDisposable {\r\n    private _scene: Scene;\r\n\r\n    private _targetedAnimations = new Array<TargetedAnimation>();\r\n    private _animatables = new Array<Animatable>();\r\n    private _from = Number.MAX_VALUE;\r\n    private _to = -Number.MAX_VALUE;\r\n    private _isStarted: boolean;\r\n    private _isPaused: boolean;\r\n    private _speedRatio = 1;\r\n    private _loopAnimation = false;\r\n    private _isAdditive = false;\r\n    private _weight = -1;\r\n    private _playOrder = 0;\r\n    private _enableBlending: Nullable<boolean> = null;\r\n    private _blendingSpeed: Nullable<number> = null;\r\n    private _numActiveAnimatables = 0;\r\n    private _shouldStart = true;\r\n\r\n    /** @internal */\r\n    public _parentContainer: Nullable<IAssetContainer> = null;\r\n\r\n    /**\r\n     * Gets or sets the unique id of the node\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * This observable will notify when one animation have ended\r\n     */\r\n    public onAnimationEndObservable = new Observable<TargetedAnimation>();\r\n\r\n    /**\r\n     * Observer raised when one animation loops\r\n     */\r\n    public onAnimationLoopObservable = new Observable<TargetedAnimation>();\r\n\r\n    /**\r\n     * Observer raised when all animations have looped\r\n     */\r\n    public onAnimationGroupLoopObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * This observable will notify when all animations have ended.\r\n     */\r\n    public onAnimationGroupEndObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * This observable will notify when all animations have paused.\r\n     */\r\n    public onAnimationGroupPauseObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * This observable will notify when all animations are playing.\r\n     */\r\n    public onAnimationGroupPlayObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the node\r\n     */\r\n    public metadata: any = null;\r\n\r\n    private _mask: Nullable<AnimationGroupMask> = null;\r\n\r\n    /**\r\n     * Gets or sets the mask associated with this animation group. This mask is used to filter which objects should be animated.\r\n     */\r\n    public get mask() {\r\n        return this._mask;\r\n    }\r\n\r\n    public set mask(value: Nullable<AnimationGroupMask>) {\r\n        if (this._mask === value) {\r\n            return;\r\n        }\r\n\r\n        this._mask = value;\r\n\r\n        this.syncWithMask(true);\r\n    }\r\n\r\n    /**\r\n     * Makes sure that the animations are either played or stopped according to the animation group mask.\r\n     * Note however that the call won't have any effect if the animation group has not been started yet.\r\n     * @param forceUpdate If true, forces to loop over the animatables even if no mask is defined (used internally, you shouldn't need to use it). Default: false.\r\n     */\r\n    public syncWithMask(forceUpdate = false) {\r\n        if (!this.mask && !forceUpdate) {\r\n            this._numActiveAnimatables = this._targetedAnimations.length;\r\n            return;\r\n        }\r\n\r\n        this._numActiveAnimatables = 0;\r\n\r\n        for (let i = 0; i < this._animatables.length; ++i) {\r\n            const animatable = this._animatables[i];\r\n\r\n            if (!this.mask || this.mask.disabled || this.mask.retainsTarget(animatable.target.name)) {\r\n                this._numActiveAnimatables++;\r\n                if (animatable.paused) {\r\n                    animatable.restart();\r\n                }\r\n            } else {\r\n                if (!animatable.paused) {\r\n                    animatable.pause();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes all animations for the targets not retained by the animation group mask.\r\n     * Use this function if you know you won't need those animations anymore and if you want to free memory.\r\n     */\r\n    public removeUnmaskedAnimations() {\r\n        if (!this.mask || this.mask.disabled) {\r\n            return;\r\n        }\r\n\r\n        // Removes all animatables (in case the animation group has already been started)\r\n        for (let i = 0; i < this._animatables.length; ++i) {\r\n            const animatable = this._animatables[i];\r\n\r\n            if (!this.mask.retainsTarget(animatable.target.name)) {\r\n                animatable.stop();\r\n                this._animatables.splice(i, 1);\r\n                --i;\r\n            }\r\n        }\r\n\r\n        // Removes the targeted animations\r\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n\r\n            if (!this.mask.retainsTarget(targetedAnimation.target.name)) {\r\n                this._targetedAnimations.splice(index, 1);\r\n                --index;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the first frame\r\n     */\r\n    public get from(): number {\r\n        return this._from;\r\n    }\r\n\r\n    public set from(value: number) {\r\n        if (this._from === value) {\r\n            return;\r\n        }\r\n\r\n        this._from = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.fromFrame = this._from;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the last frame\r\n     */\r\n    public get to(): number {\r\n        return this._to;\r\n    }\r\n\r\n    public set to(value: number) {\r\n        if (this._to === value) {\r\n            return;\r\n        }\r\n\r\n        this._to = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.toFrame = this._to;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Define if the animations are started\r\n     */\r\n    public get isStarted(): boolean {\r\n        return this._isStarted;\r\n    }\r\n\r\n    /**\r\n     * Gets a value indicating that the current group is playing\r\n     */\r\n    public get isPlaying(): boolean {\r\n        return this._isStarted && !this._isPaused;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the speed ratio to use for all animations\r\n     */\r\n    public get speedRatio(): number {\r\n        return this._speedRatio;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the speed ratio to use for all animations\r\n     */\r\n    public set speedRatio(value: number) {\r\n        if (this._speedRatio === value) {\r\n            return;\r\n        }\r\n\r\n        this._speedRatio = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.speedRatio = this._speedRatio;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets if all animations should loop or not\r\n     */\r\n    public get loopAnimation(): boolean {\r\n        return this._loopAnimation;\r\n    }\r\n\r\n    public set loopAnimation(value: boolean) {\r\n        if (this._loopAnimation === value) {\r\n            return;\r\n        }\r\n\r\n        this._loopAnimation = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.loopAnimation = this._loopAnimation;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets if all animations should be evaluated additively\r\n     */\r\n    public get isAdditive(): boolean {\r\n        return this._isAdditive;\r\n    }\r\n\r\n    public set isAdditive(value: boolean) {\r\n        if (this._isAdditive === value) {\r\n            return;\r\n        }\r\n\r\n        this._isAdditive = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.isAdditive = this._isAdditive;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the weight to apply to all animations of the group\r\n     */\r\n    public get weight(): number {\r\n        return this._weight;\r\n    }\r\n\r\n    public set weight(value: number) {\r\n        if (this._weight === value) {\r\n            return;\r\n        }\r\n\r\n        this._weight = value;\r\n        this.setWeightForAllAnimatables(this._weight);\r\n    }\r\n\r\n    /**\r\n     * Gets the targeted animations for this animation group\r\n     */\r\n    public get targetedAnimations(): Array<TargetedAnimation> {\r\n        return this._targetedAnimations;\r\n    }\r\n\r\n    /**\r\n     * returning the list of animatables controlled by this animation group.\r\n     */\r\n    public get animatables(): Array<Animatable> {\r\n        return this._animatables;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of target animations\r\n     */\r\n    public get children() {\r\n        return this._targetedAnimations;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the order of play of the animation group (default: 0)\r\n     */\r\n    public get playOrder() {\r\n        return this._playOrder;\r\n    }\r\n\r\n    public set playOrder(value: number) {\r\n        if (this._playOrder === value) {\r\n            return;\r\n        }\r\n\r\n        this._playOrder = value;\r\n\r\n        if (this._animatables.length > 0) {\r\n            for (let i = 0; i < this._animatables.length; i++) {\r\n                this._animatables[i].playOrder = this._playOrder;\r\n            }\r\n\r\n            this._scene.sortActiveAnimatables();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Allows the animations of the animation group to blend with current running animations\r\n     * Note that a null value means that each animation will use their own existing blending configuration (Animation.enableBlending)\r\n     */\r\n    public get enableBlending() {\r\n        return this._enableBlending;\r\n    }\r\n\r\n    public set enableBlending(value: Nullable<boolean>) {\r\n        if (this._enableBlending === value) {\r\n            return;\r\n        }\r\n\r\n        this._enableBlending = value;\r\n\r\n        if (value !== null) {\r\n            for (let i = 0; i < this._targetedAnimations.length; ++i) {\r\n                this._targetedAnimations[i].animation.enableBlending = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the animation blending speed\r\n     * Note that a null value means that each animation will use their own existing blending configuration (Animation.blendingSpeed)\r\n     */\r\n    public get blendingSpeed() {\r\n        return this._blendingSpeed;\r\n    }\r\n\r\n    public set blendingSpeed(value: Nullable<number>) {\r\n        if (this._blendingSpeed === value) {\r\n            return;\r\n        }\r\n\r\n        this._blendingSpeed = value;\r\n\r\n        if (value !== null) {\r\n            for (let i = 0; i < this._targetedAnimations.length; ++i) {\r\n                this._targetedAnimations[i].animation.blendingSpeed = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the length (in seconds) of the animation group\r\n     * This function assumes that all animations are played at the same framePerSecond speed!\r\n     * Note: you can only call this method after you've added at least one targeted animation!\r\n     * @param from Starting frame range (default is AnimationGroup.from)\r\n     * @param to Ending frame range (default is AnimationGroup.to)\r\n     * @returns The length in seconds\r\n     */\r\n    public getLength(from?: number, to?: number): number {\r\n        from = from ?? this._from;\r\n        to = to ?? this._to;\r\n\r\n        const fps = this.targetedAnimations[0].animation.framePerSecond * this._speedRatio;\r\n\r\n        return (to - from) / fps;\r\n    }\r\n\r\n    /**\r\n     * Merge the array of animation groups into a new animation group\r\n     * @param animationGroups List of animation groups to merge\r\n     * @param disposeSource If true, animation groups will be disposed after being merged (default: true)\r\n     * @param normalize If true, animation groups will be normalized before being merged, so that all animations have the same \"from\" and \"to\" frame (default: false)\r\n     * @param weight Weight for the new animation group. If not provided, it will inherit the weight from the first animation group of the array\r\n     * @returns The new animation group or null if no animation groups were passed\r\n     */\r\n    public static MergeAnimationGroups(animationGroups: Array<AnimationGroup>, disposeSource = true, normalize = false, weight?: number): Nullable<AnimationGroup> {\r\n        if (animationGroups.length === 0) {\r\n            return null;\r\n        }\r\n\r\n        weight = weight ?? animationGroups[0].weight;\r\n\r\n        let beginFrame = Number.MAX_VALUE;\r\n        let endFrame = -Number.MAX_VALUE;\r\n\r\n        if (normalize) {\r\n            for (const animationGroup of animationGroups) {\r\n                if (animationGroup.from < beginFrame) {\r\n                    beginFrame = animationGroup.from;\r\n                }\r\n\r\n                if (animationGroup.to > endFrame) {\r\n                    endFrame = animationGroup.to;\r\n                }\r\n            }\r\n        }\r\n\r\n        const mergedAnimationGroup = new AnimationGroup(animationGroups[0].name + \"_merged\", animationGroups[0]._scene, weight);\r\n\r\n        for (const animationGroup of animationGroups) {\r\n            if (normalize) {\r\n                animationGroup.normalize(beginFrame, endFrame);\r\n            }\r\n\r\n            for (const targetedAnimation of animationGroup.targetedAnimations) {\r\n                mergedAnimationGroup.addTargetedAnimation(targetedAnimation.animation, targetedAnimation.target);\r\n            }\r\n\r\n            if (disposeSource) {\r\n                animationGroup.dispose();\r\n            }\r\n        }\r\n\r\n        return mergedAnimationGroup;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new Animation Group.\r\n     * This helps managing several animations at once.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/groupAnimations\r\n     * @param name Defines the name of the group\r\n     * @param scene Defines the scene the group belongs to\r\n     * @param weight Defines the weight to use for animations in the group (-1.0 by default, meaning \"no weight\")\r\n     * @param playOrder Defines the order of play of the animation group (default is 0)\r\n     */\r\n    public constructor(\r\n        /** The name of the animation group */\r\n        public name: string,\r\n        scene: Nullable<Scene> = null,\r\n        weight = -1,\r\n        playOrder = 0\r\n    ) {\r\n        this._scene = scene || EngineStore.LastCreatedScene!;\r\n        this._weight = weight;\r\n        this._playOrder = playOrder;\r\n        this.uniqueId = this._scene.getUniqueId();\r\n\r\n        this._scene.addAnimationGroup(this);\r\n    }\r\n\r\n    /**\r\n     * Add an animation (with its target) in the group\r\n     * @param animation defines the animation we want to add\r\n     * @param target defines the target of the animation\r\n     * @returns the TargetedAnimation object\r\n     */\r\n    public addTargetedAnimation(animation: Animation, target: any): TargetedAnimation {\r\n        const targetedAnimation = new TargetedAnimation(this);\r\n        targetedAnimation.animation = animation;\r\n        targetedAnimation.target = target;\r\n\r\n        const keys = animation.getKeys();\r\n        if (this._from > keys[0].frame) {\r\n            this._from = keys[0].frame;\r\n        }\r\n\r\n        if (this._to < keys[keys.length - 1].frame) {\r\n            this._to = keys[keys.length - 1].frame;\r\n        }\r\n\r\n        if (this._enableBlending !== null) {\r\n            animation.enableBlending = this._enableBlending;\r\n        }\r\n\r\n        if (this._blendingSpeed !== null) {\r\n            animation.blendingSpeed = this._blendingSpeed;\r\n        }\r\n\r\n        this._targetedAnimations.push(targetedAnimation);\r\n        this._shouldStart = true;\r\n\r\n        return targetedAnimation;\r\n    }\r\n\r\n    /**\r\n     * Remove an animation from the group\r\n     * @param animation defines the animation we want to remove\r\n     */\r\n    public removeTargetedAnimation(animation: Animation) {\r\n        for (let index = this._targetedAnimations.length - 1; index > -1; index--) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n            if (targetedAnimation.animation === animation) {\r\n                this._targetedAnimations.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This function will normalize every animation in the group to make sure they all go from beginFrame to endFrame\r\n     * It can add constant keys at begin or end\r\n     * @param beginFrame defines the new begin frame for all animations or the smallest begin frame of all animations if null (defaults to null)\r\n     * @param endFrame defines the new end frame for all animations or the largest end frame of all animations if null (defaults to null)\r\n     * @returns the animation group\r\n     */\r\n    public normalize(beginFrame: Nullable<number> = null, endFrame: Nullable<number> = null): AnimationGroup {\r\n        if (beginFrame == null) {\r\n            beginFrame = this._from;\r\n        }\r\n        if (endFrame == null) {\r\n            endFrame = this._to;\r\n        }\r\n\r\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n            const keys = targetedAnimation.animation.getKeys();\r\n            const startKey = keys[0];\r\n            const endKey = keys[keys.length - 1];\r\n\r\n            if (startKey.frame > beginFrame) {\r\n                const newKey: IAnimationKey = {\r\n                    frame: beginFrame,\r\n                    value: startKey.value,\r\n                    inTangent: startKey.inTangent,\r\n                    outTangent: startKey.outTangent,\r\n                    interpolation: startKey.interpolation,\r\n                };\r\n                keys.splice(0, 0, newKey);\r\n            }\r\n\r\n            if (endKey.frame < endFrame) {\r\n                const newKey: IAnimationKey = {\r\n                    frame: endFrame,\r\n                    value: endKey.value,\r\n                    inTangent: endKey.inTangent,\r\n                    outTangent: endKey.outTangent,\r\n                    interpolation: endKey.interpolation,\r\n                };\r\n                keys.push(newKey);\r\n            }\r\n        }\r\n\r\n        this._from = beginFrame;\r\n        this._to = endFrame;\r\n\r\n        return this;\r\n    }\r\n\r\n    private _animationLoopCount: number;\r\n    private _animationLoopFlags: boolean[] = [];\r\n\r\n    private _processLoop(animatable: Animatable, targetedAnimation: TargetedAnimation, index: number) {\r\n        animatable.onAnimationLoop = () => {\r\n            this.onAnimationLoopObservable.notifyObservers(targetedAnimation);\r\n\r\n            if (this._animationLoopFlags[index]) {\r\n                return;\r\n            }\r\n\r\n            this._animationLoopFlags[index] = true;\r\n\r\n            this._animationLoopCount++;\r\n            if (this._animationLoopCount === this._numActiveAnimatables) {\r\n                this.onAnimationGroupLoopObservable.notifyObservers(this);\r\n                this._animationLoopCount = 0;\r\n                this._animationLoopFlags.length = 0;\r\n            }\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Start all animations on given targets\r\n     * @param loop defines if animations must loop\r\n     * @param speedRatio defines the ratio to apply to animation speed (1 by default)\r\n     * @param from defines the from key (optional)\r\n     * @param to defines the to key (optional)\r\n     * @param isAdditive defines the additive state for the resulting animatables (optional)\r\n     * @returns the current animation group\r\n     */\r\n    public start(loop = false, speedRatio = 1, from?: number, to?: number, isAdditive?: boolean): AnimationGroup {\r\n        if (this._isStarted || this._targetedAnimations.length === 0) {\r\n            return this;\r\n        }\r\n\r\n        this._loopAnimation = loop;\r\n\r\n        this._shouldStart = false;\r\n        this._animationLoopCount = 0;\r\n        this._animationLoopFlags.length = 0;\r\n\r\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n            const animatable = this._scene.beginDirectAnimation(\r\n                targetedAnimation.target,\r\n                [targetedAnimation.animation],\r\n                from !== undefined ? from : this._from,\r\n                to !== undefined ? to : this._to,\r\n                loop,\r\n                speedRatio,\r\n                undefined,\r\n                undefined,\r\n                isAdditive !== undefined ? isAdditive : this._isAdditive\r\n            );\r\n            animatable.weight = this._weight;\r\n            animatable.playOrder = this._playOrder;\r\n            animatable.onAnimationEnd = () => {\r\n                this.onAnimationEndObservable.notifyObservers(targetedAnimation);\r\n                this._checkAnimationGroupEnded(animatable);\r\n            };\r\n\r\n            this._processLoop(animatable, targetedAnimation, index);\r\n            this._animatables.push(animatable);\r\n        }\r\n\r\n        this.syncWithMask();\r\n\r\n        this._scene.sortActiveAnimatables();\r\n\r\n        this._speedRatio = speedRatio;\r\n\r\n        this._isStarted = true;\r\n        this._isPaused = false;\r\n\r\n        this.onAnimationGroupPlayObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Pause all animations\r\n     * @returns the animation group\r\n     */\r\n    public pause(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        this._isPaused = true;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.pause();\r\n        }\r\n\r\n        this.onAnimationGroupPauseObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Play all animations to initial state\r\n     * This function will start() the animations if they were not started or will restart() them if they were paused\r\n     * @param loop defines if animations must loop\r\n     * @returns the animation group\r\n     */\r\n    public play(loop?: boolean): AnimationGroup {\r\n        // only if there are animatable available\r\n        if (this.isStarted && this._animatables.length && !this._shouldStart) {\r\n            if (loop !== undefined) {\r\n                this.loopAnimation = loop;\r\n            }\r\n            this.restart();\r\n        } else {\r\n            this.stop();\r\n            this.start(loop, this._speedRatio);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Reset all animations to initial state\r\n     * @returns the animation group\r\n     */\r\n    public reset(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            this.play();\r\n            this.goToFrame(0);\r\n            this.stop(true);\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.reset();\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Restart animations from after pausing it\r\n     * @returns the animation group\r\n     */\r\n    public restart(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.restart();\r\n        }\r\n\r\n        this.syncWithMask();\r\n\r\n        this._isPaused = false;\r\n\r\n        this.onAnimationGroupPlayObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Stop all animations\r\n     * @param skipOnAnimationEnd defines if the system should not raise onAnimationEnd. Default is false\r\n     * @returns the animation group\r\n     */\r\n    public stop(skipOnAnimationEnd = false): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        const list = this._animatables.slice();\r\n        for (let index = 0; index < list.length; index++) {\r\n            list[index].stop(undefined, undefined, true, skipOnAnimationEnd);\r\n        }\r\n\r\n        // We will take care of removing all stopped animatables\r\n        let curIndex = 0;\r\n        for (let index = 0; index < this._scene._activeAnimatables.length; index++) {\r\n            const animatable = this._scene._activeAnimatables[index];\r\n            if (animatable._runtimeAnimations.length > 0) {\r\n                this._scene._activeAnimatables[curIndex++] = animatable;\r\n            } else if (skipOnAnimationEnd) {\r\n                // We normally rely on the onAnimationEnd callback (assigned in the start function) to be notified when an animatable\r\n                // ends and should be removed from the active animatables array. However, if the animatable is stopped with the skipOnAnimationEnd\r\n                // flag set to true, then we need to explicitly remove it from the active animatables array.\r\n                this._checkAnimationGroupEnded(animatable, skipOnAnimationEnd);\r\n            }\r\n        }\r\n        this._scene._activeAnimatables.length = curIndex;\r\n\r\n        this._isStarted = false;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set animation weight for all animatables\r\n     *\r\n     * @since 6.12.4\r\n     *  You can pass the weight to the AnimationGroup constructor, or use the weight property to set it after the group has been created,\r\n     *  making it easier to define the overall animation weight than calling setWeightForAllAnimatables() after the animation group has been started\r\n     * @param weight defines the weight to use\r\n     * @returns the animationGroup\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights\r\n     */\r\n    public setWeightForAllAnimatables(weight: number): AnimationGroup {\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.weight = weight;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Synchronize and normalize all animatables with a source animatable\r\n     * @param root defines the root animatable to synchronize with (null to stop synchronizing)\r\n     * @returns the animationGroup\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights\r\n     */\r\n    public syncAllAnimationsWith(root: Nullable<Animatable>): AnimationGroup {\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.syncWith(root);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Goes to a specific frame in this animation group. Note that the animation group must be in playing or paused status\r\n     * @param frame the frame number to go to\r\n     * @param useWeight defines whether the animation weight should be applied to the image to be jumped to (false by default)\r\n     * @returns the animationGroup\r\n     */\r\n    public goToFrame(frame: number, useWeight = false): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.goToFrame(frame, useWeight);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Helper to get the current frame. This will return 0 if the AnimationGroup is not running, and it might return wrong results if multiple animations are running in different frames.\r\n     * @returns current animation frame.\r\n     */\r\n    public getCurrentFrame(): number {\r\n        return this.animatables[0]?.masterFrame || 0;\r\n    }\r\n\r\n    /**\r\n     * Dispose all associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this.isStarted) {\r\n            this.stop();\r\n        }\r\n        this._targetedAnimations.length = 0;\r\n        this._animatables.length = 0;\r\n\r\n        // Remove from scene\r\n        const index = this._scene.animationGroups.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._scene.animationGroups.splice(index, 1);\r\n        }\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.animationGroups.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.animationGroups.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        this.onAnimationEndObservable.clear();\r\n        this.onAnimationGroupEndObservable.clear();\r\n        this.onAnimationGroupPauseObservable.clear();\r\n        this.onAnimationGroupPlayObservable.clear();\r\n        this.onAnimationLoopObservable.clear();\r\n        this.onAnimationGroupLoopObservable.clear();\r\n    }\r\n\r\n    private _checkAnimationGroupEnded(animatable: Animatable, skipOnAnimationEnd = false) {\r\n        // animatable should be taken out of the array\r\n        const idx = this._animatables.indexOf(animatable);\r\n        if (idx > -1) {\r\n            this._animatables.splice(idx, 1);\r\n        }\r\n\r\n        // all animatables were removed? animation group ended!\r\n        if (this._animatables.length === this._targetedAnimations.length - this._numActiveAnimatables) {\r\n            this._isStarted = false;\r\n            if (!skipOnAnimationEnd) {\r\n                this.onAnimationGroupEndObservable.notifyObservers(this);\r\n            }\r\n            this._animatables.length = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clone the current animation group and returns a copy\r\n     * @param newName defines the name of the new group\r\n     * @param targetConverter defines an optional function used to convert current animation targets to new ones\r\n     * @param cloneAnimations defines if the animations should be cloned or referenced\r\n     * @returns the new animation group\r\n     */\r\n    public clone(newName: string, targetConverter?: (oldTarget: any) => any, cloneAnimations = false): AnimationGroup {\r\n        const newGroup = new AnimationGroup(newName || this.name, this._scene, this._weight, this._playOrder);\r\n\r\n        newGroup._from = this.from;\r\n        newGroup._to = this.to;\r\n        newGroup._speedRatio = this.speedRatio;\r\n        newGroup._loopAnimation = this.loopAnimation;\r\n        newGroup._isAdditive = this.isAdditive;\r\n        newGroup._enableBlending = this.enableBlending;\r\n        newGroup._blendingSpeed = this.blendingSpeed;\r\n        newGroup.metadata = this.metadata;\r\n        newGroup.mask = this.mask;\r\n\r\n        for (const targetAnimation of this._targetedAnimations) {\r\n            newGroup.addTargetedAnimation(\r\n                cloneAnimations ? targetAnimation.animation.clone() : targetAnimation.animation,\r\n                targetConverter ? targetConverter(targetAnimation.target) : targetAnimation.target\r\n            );\r\n        }\r\n\r\n        return newGroup;\r\n    }\r\n\r\n    /**\r\n     * Serializes the animationGroup to an object\r\n     * @returns Serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.from = this.from;\r\n        serializationObject.to = this.to;\r\n        serializationObject.speedRatio = this.speedRatio;\r\n        serializationObject.loopAnimation = this.loopAnimation;\r\n        serializationObject.isAdditive = this.isAdditive;\r\n        serializationObject.weight = this.weight;\r\n        serializationObject.playOrder = this.playOrder;\r\n        serializationObject.enableBlending = this.enableBlending;\r\n        serializationObject.blendingSpeed = this.blendingSpeed;\r\n\r\n        serializationObject.targetedAnimations = [];\r\n        for (let targetedAnimationIndex = 0; targetedAnimationIndex < this.targetedAnimations.length; targetedAnimationIndex++) {\r\n            const targetedAnimation = this.targetedAnimations[targetedAnimationIndex];\r\n            serializationObject.targetedAnimations[targetedAnimationIndex] = targetedAnimation.serialize();\r\n        }\r\n\r\n        if (Tags && Tags.HasTags(this)) {\r\n            serializationObject.tags = Tags.GetTags(this);\r\n        }\r\n\r\n        // Metadata\r\n        if (this.metadata) {\r\n            serializationObject.metadata = this.metadata;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Returns a new AnimationGroup object parsed from the source provided.\r\n     * @param parsedAnimationGroup defines the source\r\n     * @param scene defines the scene that will receive the animationGroup\r\n     * @param nodeMap a map of node.id to node in this scene, to accelerate node lookup\r\n     * @returns a new AnimationGroup\r\n     */\r\n    public static Parse(parsedAnimationGroup: any, scene: Scene, nodeMap?: Map<Node[\"id\"], Node>): AnimationGroup {\r\n        const animationGroup = new AnimationGroup(parsedAnimationGroup.name, scene, parsedAnimationGroup.weight, parsedAnimationGroup.playOrder);\r\n        for (let i = 0; i < parsedAnimationGroup.targetedAnimations.length; i++) {\r\n            const targetedAnimation = parsedAnimationGroup.targetedAnimations[i];\r\n            const animation = Animation.Parse(targetedAnimation.animation);\r\n            const id = targetedAnimation.targetId;\r\n            if (targetedAnimation.animation.property === \"influence\") {\r\n                // morph target animation\r\n                const morphTarget = scene.getMorphTargetById(id);\r\n                if (morphTarget) {\r\n                    animationGroup.addTargetedAnimation(animation, morphTarget);\r\n                }\r\n            } else {\r\n                const targetNode = nodeMap ? nodeMap.get(id) : scene.getNodeById(id);\r\n\r\n                if (targetNode != null) {\r\n                    animationGroup.addTargetedAnimation(animation, targetNode);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (Tags) {\r\n            Tags.AddTagsTo(animationGroup, parsedAnimationGroup.tags);\r\n        }\r\n\r\n        if (parsedAnimationGroup.from !== null && parsedAnimationGroup.to !== null) {\r\n            animationGroup.normalize(parsedAnimationGroup.from, parsedAnimationGroup.to);\r\n        }\r\n\r\n        if (parsedAnimationGroup.speedRatio !== undefined) {\r\n            animationGroup._speedRatio = parsedAnimationGroup.speedRatio;\r\n        }\r\n        if (parsedAnimationGroup.loopAnimation !== undefined) {\r\n            animationGroup._loopAnimation = parsedAnimationGroup.loopAnimation;\r\n        }\r\n\r\n        if (parsedAnimationGroup.isAdditive !== undefined) {\r\n            animationGroup._isAdditive = parsedAnimationGroup.isAdditive;\r\n        }\r\n\r\n        if (parsedAnimationGroup.weight !== undefined) {\r\n            animationGroup._weight = parsedAnimationGroup.weight;\r\n        }\r\n\r\n        if (parsedAnimationGroup.playOrder !== undefined) {\r\n            animationGroup._playOrder = parsedAnimationGroup.playOrder;\r\n        }\r\n\r\n        if (parsedAnimationGroup.enableBlending !== undefined) {\r\n            animationGroup._enableBlending = parsedAnimationGroup.enableBlending;\r\n        }\r\n\r\n        if (parsedAnimationGroup.blendingSpeed !== undefined) {\r\n            animationGroup._blendingSpeed = parsedAnimationGroup.blendingSpeed;\r\n        }\r\n\r\n        if (parsedAnimationGroup.metadata !== undefined) {\r\n            animationGroup.metadata = parsedAnimationGroup.metadata;\r\n        }\r\n\r\n        return animationGroup;\r\n    }\r\n\r\n    /**\r\n     * Convert the keyframes for all animations belonging to the group to be relative to a given reference frame.\r\n     * @param sourceAnimationGroup defines the AnimationGroup containing animations to convert\r\n     * @param referenceFrame defines the frame that keyframes in the range will be relative to (default: 0)\r\n     * @param range defines the name of the AnimationRange belonging to the animations in the group to convert\r\n     * @param cloneOriginal defines whether or not to clone the group and convert the clone or convert the original group (default is false)\r\n     * @param clonedName defines the name of the resulting cloned AnimationGroup if cloneOriginal is true\r\n     * @returns a new AnimationGroup if cloneOriginal is true or the original AnimationGroup if cloneOriginal is false\r\n     */\r\n    public static MakeAnimationAdditive(sourceAnimationGroup: AnimationGroup, referenceFrame: number, range?: string, cloneOriginal?: boolean, clonedName?: string): AnimationGroup;\r\n\r\n    /**\r\n     * Convert the keyframes for all animations belonging to the group to be relative to a given reference frame.\r\n     * @param sourceAnimationGroup defines the AnimationGroup containing animations to convert\r\n     * @param options defines the options to use when converting keyframes\r\n     * @returns a new AnimationGroup if options.cloneOriginalAnimationGroup is true or the original AnimationGroup if options.cloneOriginalAnimationGroup is false\r\n     */\r\n    public static MakeAnimationAdditive(sourceAnimationGroup: AnimationGroup, options?: IMakeAnimationGroupAdditiveOptions): AnimationGroup;\r\n\r\n    /** @internal */\r\n    public static MakeAnimationAdditive(\r\n        sourceAnimationGroup: AnimationGroup,\r\n        referenceFrameOrOptions?: number | IMakeAnimationGroupAdditiveOptions,\r\n        range?: string,\r\n        cloneOriginal = false,\r\n        clonedName?: string\r\n    ): AnimationGroup {\r\n        let options: IMakeAnimationGroupAdditiveOptions;\r\n\r\n        if (typeof referenceFrameOrOptions === \"object\") {\r\n            options = referenceFrameOrOptions;\r\n        } else {\r\n            options = {\r\n                referenceFrame: referenceFrameOrOptions,\r\n                range: range,\r\n                cloneOriginalAnimationGroup: cloneOriginal,\r\n                clonedAnimationName: clonedName,\r\n            };\r\n        }\r\n\r\n        let animationGroup = sourceAnimationGroup;\r\n        if (options.cloneOriginalAnimationGroup) {\r\n            animationGroup = sourceAnimationGroup.clone(options.clonedAnimationGroupName || animationGroup.name);\r\n        }\r\n\r\n        const targetedAnimations = animationGroup.targetedAnimations;\r\n        for (let index = 0; index < targetedAnimations.length; index++) {\r\n            const targetedAnimation = targetedAnimations[index];\r\n            targetedAnimation.animation = Animation.MakeAnimationAdditive(targetedAnimation.animation, options);\r\n        }\r\n\r\n        animationGroup.isAdditive = true;\r\n\r\n        if (options.clipKeys) {\r\n            // We need to recalculate the from/to frames for the animation group because some keys may have been removed\r\n            let from = Number.MAX_VALUE;\r\n            let to = -Number.MAX_VALUE;\r\n\r\n            const targetedAnimations = animationGroup.targetedAnimations;\r\n            for (let index = 0; index < targetedAnimations.length; index++) {\r\n                const targetedAnimation = targetedAnimations[index];\r\n                const animation = targetedAnimation.animation;\r\n                const keys = animation.getKeys();\r\n\r\n                if (from > keys[0].frame) {\r\n                    from = keys[0].frame;\r\n                }\r\n\r\n                if (to < keys[keys.length - 1].frame) {\r\n                    to = keys[keys.length - 1].frame;\r\n                }\r\n            }\r\n\r\n            animationGroup._from = from;\r\n            animationGroup._to = to;\r\n        }\r\n\r\n        return animationGroup;\r\n    }\r\n\r\n    /**\r\n     * Creates a new animation, keeping only the keys that are inside a given key range\r\n     * @param sourceAnimationGroup defines the animation group on which to operate\r\n     * @param fromKey defines the lower bound of the range\r\n     * @param toKey defines the upper bound of the range\r\n     * @param name defines the name of the new animation group. If not provided, use the same name as animationGroup\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the keys. Default is false, so animations will be cloned\r\n     * @returns a new animation group stripped from all the keys outside the given range\r\n     */\r\n    public static ClipKeys(sourceAnimationGroup: AnimationGroup, fromKey: number, toKey: number, name?: string, dontCloneAnimations?: boolean): AnimationGroup {\r\n        const animationGroup = sourceAnimationGroup.clone(name || sourceAnimationGroup.name);\r\n\r\n        return AnimationGroup.ClipKeysInPlace(animationGroup, fromKey, toKey, dontCloneAnimations);\r\n    }\r\n\r\n    /**\r\n     * Updates an existing animation, keeping only the keys that are inside a given key range\r\n     * @param animationGroup defines the animation group on which to operate\r\n     * @param fromKey defines the lower bound of the range\r\n     * @param toKey defines the upper bound of the range\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the keys. Default is false, so animations will be cloned\r\n     * @returns the animationGroup stripped from all the keys outside the given range\r\n     */\r\n    public static ClipKeysInPlace(animationGroup: AnimationGroup, fromKey: number, toKey: number, dontCloneAnimations?: boolean): AnimationGroup {\r\n        return AnimationGroup.ClipInPlace(animationGroup, fromKey, toKey, dontCloneAnimations, false);\r\n    }\r\n\r\n    /**\r\n     * Creates a new animation, keeping only the frames that are inside a given frame range\r\n     * @param sourceAnimationGroup defines the animation group on which to operate\r\n     * @param fromFrame defines the lower bound of the range\r\n     * @param toFrame defines the upper bound of the range\r\n     * @param name defines the name of the new animation group. If not provided, use the same name as animationGroup\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the frames. Default is false, so animations will be cloned\r\n     * @returns a new animation group stripped from all the frames outside the given range\r\n     */\r\n    public static ClipFrames(sourceAnimationGroup: AnimationGroup, fromFrame: number, toFrame: number, name?: string, dontCloneAnimations?: boolean): AnimationGroup {\r\n        const animationGroup = sourceAnimationGroup.clone(name || sourceAnimationGroup.name);\r\n\r\n        return AnimationGroup.ClipFramesInPlace(animationGroup, fromFrame, toFrame, dontCloneAnimations);\r\n    }\r\n\r\n    /**\r\n     * Updates an existing animation, keeping only the frames that are inside a given frame range\r\n     * @param animationGroup defines the animation group on which to operate\r\n     * @param fromFrame defines the lower bound of the range\r\n     * @param toFrame defines the upper bound of the range\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the frames. Default is false, so animations will be cloned\r\n     * @returns the animationGroup stripped from all the frames outside the given range\r\n     */\r\n    public static ClipFramesInPlace(animationGroup: AnimationGroup, fromFrame: number, toFrame: number, dontCloneAnimations?: boolean): AnimationGroup {\r\n        return AnimationGroup.ClipInPlace(animationGroup, fromFrame, toFrame, dontCloneAnimations, true);\r\n    }\r\n\r\n    /**\r\n     * Updates an existing animation, keeping only the keys that are inside a given key or frame range\r\n     * @param animationGroup defines the animation group on which to operate\r\n     * @param start defines the lower bound of the range\r\n     * @param end defines the upper bound of the range\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the keys. Default is false, so animations will be cloned\r\n     * @param useFrame defines if the range is defined by frame numbers or key indices (default is false which means use key indices)\r\n     * @returns the animationGroup stripped from all the keys outside the given range\r\n     */\r\n    public static ClipInPlace(animationGroup: AnimationGroup, start: number, end: number, dontCloneAnimations?: boolean, useFrame = false): AnimationGroup {\r\n        let from = Number.MAX_VALUE;\r\n        let to = -Number.MAX_VALUE;\r\n\r\n        const targetedAnimations = animationGroup.targetedAnimations;\r\n        for (let index = 0; index < targetedAnimations.length; index++) {\r\n            const targetedAnimation = targetedAnimations[index];\r\n            const animation = dontCloneAnimations ? targetedAnimation.animation : targetedAnimation.animation.clone();\r\n\r\n            if (useFrame) {\r\n                // Make sure we have keys corresponding to the bounds of the frame range\r\n                animation.createKeyForFrame(start);\r\n                animation.createKeyForFrame(end);\r\n            }\r\n\r\n            const keys = animation.getKeys();\r\n            const newKeys: IAnimationKey[] = [];\r\n\r\n            let startFrame = Number.MAX_VALUE;\r\n            for (let k = 0; k < keys.length; k++) {\r\n                const key = keys[k];\r\n                if ((!useFrame && k >= start && k <= end) || (useFrame && key.frame >= start && key.frame <= end)) {\r\n                    const newKey: IAnimationKey = {\r\n                        frame: key.frame,\r\n                        value: key.value.clone ? key.value.clone() : key.value,\r\n                        inTangent: key.inTangent,\r\n                        outTangent: key.outTangent,\r\n                        interpolation: key.interpolation,\r\n                        lockedTangent: key.lockedTangent,\r\n                    };\r\n                    if (startFrame === Number.MAX_VALUE) {\r\n                        startFrame = newKey.frame;\r\n                    }\r\n                    newKey.frame -= startFrame;\r\n                    newKeys.push(newKey);\r\n                }\r\n            }\r\n\r\n            if (newKeys.length === 0) {\r\n                targetedAnimations.splice(index, 1);\r\n                index--;\r\n                continue;\r\n            }\r\n\r\n            if (from > newKeys[0].frame) {\r\n                from = newKeys[0].frame;\r\n            }\r\n\r\n            if (to < newKeys[newKeys.length - 1].frame) {\r\n                to = newKeys[newKeys.length - 1].frame;\r\n            }\r\n\r\n            animation.setKeys(newKeys, true);\r\n            targetedAnimation.animation = animation; // in case the animation has been cloned\r\n        }\r\n\r\n        animationGroup._from = from;\r\n        animationGroup._to = to;\r\n\r\n        return animationGroup;\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"AnimationGroup\"\r\n     * @returns \"AnimationGroup\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"AnimationGroup\";\r\n    }\r\n\r\n    /**\r\n     * Creates a detailed string about the object\r\n     * @param fullDetails defines if the output string will support multiple levels of logging within scene loading\r\n     * @returns a string representing the object\r\n     */\r\n    public toString(fullDetails?: boolean): string {\r\n        let ret = \"Name: \" + this.name;\r\n        ret += \", type: \" + this.getClassName();\r\n        if (fullDetails) {\r\n            ret += \", from: \" + this._from;\r\n            ret += \", to: \" + this._to;\r\n            ret += \", isStarted: \" + this._isStarted;\r\n            ret += \", speedRatio: \" + this._speedRatio;\r\n            ret += \", targetedAnimations length: \" + this._targetedAnimations.length;\r\n            ret += \", animatables length: \" + this._animatables;\r\n        }\r\n        return ret;\r\n    }\r\n}\r\n"]}