{"version": 3, "file": "webgpuCacheRenderPipelineTree.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuCacheRenderPipelineTree.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAExE,gBAAgB;AAChB,MAAM,SAAS;IAIX;QACI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACrB,CAAC;IAEM,KAAK;QACR,IAAI,SAAS,GAAG,CAAC,EACb,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChC,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC3D,SAAS,IAAI,eAAe,CAAC;YAC7B,aAAa,IAAI,kBAAkB,CAAC;YACpC,SAAS,EAAE,CAAC;QAChB,CAAC;QACD,OAAO,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IACtC,CAAC;CACJ;AAED,gBAAgB;AAChB,MAAM,OAAO,6BAA8B,SAAQ,yBAAyB;IAKjE,MAAM,CAAC,aAAa;QACvB,MAAM,MAAM,GAAG,6BAA6B,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAE5D,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9D,CAAC;IAEM,MAAM,CAAC,aAAa,CAAC,IAAe,EAAE,SAA+B,EAAE,OAAsB,EAAE,UAAkB;QACpH,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;YACzB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,OAAO,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;YACtC,6BAA6B,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC;QAC3F,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,YAAY;QACtB,MAAM,SAAS,GAAyB,EAAE,CAAC;QAC3C,6BAA6B,CAAC,aAAa,CAAC,6BAA6B,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QACpG,OAAO,SAAS,CAAC;IACrB,CAAC;IAEM,MAAM,CAAC,UAAU;QACpB,6BAA6B,CAAC,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;IAC3D,CAAC;IAEe,KAAK;QACjB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,6BAA6B,CAAC,MAAM,CAAC;QAC1D,KAAK,CAAC,KAAK,EAAE,CAAC;IAClB,CAAC;IAES,kBAAkB,CAAC,KAA4D;QACrF,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACxD,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC;YACpE,IAAI,EAAE,GAA0B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,IAAI,CAAC,EAAE,EAAE,CAAC;gBACN,EAAE,GAAG,IAAI,SAAS,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YACtC,CAAC;YACD,IAAI,GAAG,EAAE,CAAC;YACV,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAClC,CAAC;QAED,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;QACnB,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IACnC,CAAC;IAES,kBAAkB,CAAC,KAAkE;QAC3F,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAS,CAAC;IAC3C,CAAC;;AAzDc,oCAAM,GAAc,IAAI,SAAS,EAAE,CAAC", "sourcesContent": ["/* eslint-disable babylonjs/available */\r\nimport type { Nullable } from \"../../types\";\r\nimport { WebGPUCacheRenderPipeline } from \"./webgpuCacheRenderPipeline\";\r\n\r\n/** @internal */\r\nclass NodeState {\r\n    public values: { [id: number]: NodeState };\r\n    public pipeline: GPURenderPipeline;\r\n\r\n    constructor() {\r\n        this.values = {};\r\n    }\r\n\r\n    public count(): [number, number] {\r\n        let countNode = 0,\r\n            countPipeline = this.pipeline ? 1 : 0;\r\n        for (const value in this.values) {\r\n            const node = this.values[value];\r\n            const [childCountNodes, childCoundPipeline] = node.count();\r\n            countNode += childCountNodes;\r\n            countPipeline += childCoundPipeline;\r\n            countNode++;\r\n        }\r\n        return [countNode, countPipeline];\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport class WebGPUCacheRenderPipelineTree extends WebGPUCacheRenderPipeline {\r\n    private static _Cache: NodeState = new NodeState();\r\n\r\n    private _nodeStack: NodeState[];\r\n\r\n    public static GetNodeCounts(): { nodeCount: number; pipelineCount: number } {\r\n        const counts = WebGPUCacheRenderPipelineTree._Cache.count();\r\n\r\n        return { nodeCount: counts[0], pipelineCount: counts[1] };\r\n    }\r\n\r\n    public static _GetPipelines(node: NodeState, pipelines: Array<Array<number>>, curPath: Array<number>, curPathLen: number): void {\r\n        if (node.pipeline) {\r\n            const path = curPath.slice();\r\n            path.length = curPathLen;\r\n            pipelines.push(path);\r\n        }\r\n        for (const value in node.values) {\r\n            const nnode = node.values[value];\r\n            curPath[curPathLen] = parseInt(value);\r\n            WebGPUCacheRenderPipelineTree._GetPipelines(nnode, pipelines, curPath, curPathLen + 1);\r\n        }\r\n    }\r\n\r\n    public static GetPipelines(): Array<Array<number>> {\r\n        const pipelines: Array<Array<number>> = [];\r\n        WebGPUCacheRenderPipelineTree._GetPipelines(WebGPUCacheRenderPipelineTree._Cache, pipelines, [], 0);\r\n        return pipelines;\r\n    }\r\n\r\n    public static ResetCache() {\r\n        WebGPUCacheRenderPipelineTree._Cache = new NodeState();\r\n    }\r\n\r\n    public override reset(): void {\r\n        this._nodeStack = [];\r\n        this._nodeStack[0] = WebGPUCacheRenderPipelineTree._Cache;\r\n        super.reset();\r\n    }\r\n\r\n    protected _getRenderPipeline(param: { token: any; pipeline: Nullable<GPURenderPipeline> }): void {\r\n        let node = this._nodeStack[this._stateDirtyLowestIndex];\r\n        for (let i = this._stateDirtyLowestIndex; i < this._statesLength; ++i) {\r\n            let nn: NodeState | undefined = node.values[this._states[i]];\r\n            if (!nn) {\r\n                nn = new NodeState();\r\n                node.values[this._states[i]] = nn;\r\n            }\r\n            node = nn;\r\n            this._nodeStack[i + 1] = node;\r\n        }\r\n\r\n        param.token = node;\r\n        param.pipeline = node.pipeline;\r\n    }\r\n\r\n    protected _setRenderPipeline(param: { token: NodeState; pipeline: Nullable<GPURenderPipeline> }): void {\r\n        param.token.pipeline = param.pipeline!;\r\n    }\r\n}\r\n"]}