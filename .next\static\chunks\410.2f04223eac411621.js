"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[410],{22410:(e,t,i)=>{i.d(t,{default:()=>a});var n=i(23377),s=i(2575);class o{setupPipeline(){this.pipeline=new n.m3G(this.engine,"cyberpunkPipeline"),this.createBloomEffect(),this.createFilmEffect(),this.createGlitchEffect(),this.createColorCorrectionEffect(),this.createChromaticAberrationEffect(),this.createGrainEffect(),this.createMotionBlurEffect(),this.createDepthOfFieldEffect(),this.scene.postProcessRenderPipelineManager.addPipeline(this.pipeline),this.scene.postProcessRenderPipelineManager.attachCamerasToRenderPipeline("cyberpunkPipeline",this.camera)}createBloomEffect(){let e=new s.BloomPostProcess("bloom",1,this.camera,n.gPd.BILINEAR_SAMPLINGMODE,this.engine,!1,n.YMJ.TEXTURETYPE_UNSIGNED_INT);e.threshold=.8,e.weight=.3,e.kernel=64,this.effects.bloom=e;let t=new n.lck(this.engine,"bloomEffect",()=>[e]);this.pipeline.addEffect(t)}createFilmEffect(){n.Mjc.ShadersStore.filmFragmentShader="\n      precision highp float;\n      \n      varying vec2 vUV;\n      uniform sampler2D textureSampler;\n      uniform float time;\n      uniform float intensity;\n      uniform float scanlines;\n      \n      float random(vec2 co) {\n        return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);\n      }\n      \n      void main() {\n        vec4 color = texture2D(textureSampler, vUV);\n        \n        // Film grain\n        float noise = random(vUV + time) * intensity;\n        color.rgb += noise;\n        \n        // Scanlines\n        float scanline = sin(vUV.y * 800.0) * scanlines;\n        color.rgb += scanline;\n        \n        // Vignette\n        vec2 center = vUV - 0.5;\n        float vignette = 1.0 - dot(center, center) * 0.8;\n        color.rgb *= vignette;\n        \n        gl_FragColor = color;\n      }\n    ";let e=new n.wbN("film","film",["time","intensity","scanlines"],null,1,this.camera,n.gPd.BILINEAR_SAMPLINGMODE,this.engine,!1);e.onApply=e=>{e.setFloat("time",.001*performance.now()),e.setFloat("intensity",.1),e.setFloat("scanlines",.05)},this.effects.film=e;let t=new n.lck(this.engine,"filmEffect",()=>[e]);this.pipeline.addEffect(t)}createGlitchEffect(){n.Mjc.ShadersStore.glitchFragmentShader="\n      precision highp float;\n      \n      varying vec2 vUV;\n      uniform sampler2D textureSampler;\n      uniform float time;\n      uniform float distortion;\n      uniform float speed;\n      \n      float random(float x) {\n        return fract(sin(x * 12.9898) * 43758.5453);\n      }\n      \n      void main() {\n        vec2 uv = vUV;\n        \n        // Digital distortion\n        float noise = random(time * speed + uv.y * 80.0) * distortion;\n        noise += random(time * speed * 2.0 + uv.y * 200.0) * distortion;\n        \n        // Horizontal displacement\n        uv.x += noise * 0.05;\n        \n        // Vertical roll\n        uv.y += random(time * 0.1) * 0.01;\n        \n        // Color separation\n        vec4 color;\n        color.r = texture2D(textureSampler, uv + vec2(0.01, 0.0)).r;\n        color.g = texture2D(textureSampler, uv).g;\n        color.b = texture2D(textureSampler, uv - vec2(0.01, 0.0)).b;\n        color.a = texture2D(textureSampler, uv).a;\n        \n        // Random glitch lines\n        float glitch = step(0.9, random(time * 10.0 + uv.y * 100.0));\n        color.rgb = mix(color.rgb, vec3(1.0, 0.0, 1.0), glitch * 0.1);\n        \n        gl_FragColor = color;\n      }\n    ";let e=new n.wbN("glitch","glitch",["time","distortion","speed"],null,1,this.camera,n.gPd.BILINEAR_SAMPLINGMODE,this.engine,!1);e.onApply=e=>{e.setFloat("time",.001*performance.now()),e.setFloat("distortion",.02),e.setFloat("speed",1)},this.effects.glitch=e;let t=new n.lck(this.engine,"glitchEffect",()=>[e]);this.pipeline.addEffect(t)}createColorCorrectionEffect(){n.Mjc.ShadersStore.colorCorrectionFragmentShader="\n      precision highp float;\n      \n      varying vec2 vUV;\n      uniform sampler2D textureSampler;\n      uniform float contrast;\n      uniform float brightness;\n      uniform float saturation;\n      uniform vec3 colorTint;\n      \n      vec3 adjustContrast(vec3 color, float contrast) {\n        return (color - 0.5) * contrast + 0.5;\n      }\n      \n      vec3 adjustSaturation(vec3 color, float saturation) {\n        float gray = dot(color, vec3(0.299, 0.587, 0.114));\n        return mix(vec3(gray), color, saturation);\n      }\n      \n      void main() {\n        vec4 color = texture2D(textureSampler, vUV);\n        \n        // Apply brightness\n        color.rgb += brightness;\n        \n        // Apply contrast\n        color.rgb = adjustContrast(color.rgb, contrast);\n        \n        // Apply saturation\n        color.rgb = adjustSaturation(color.rgb, saturation);\n        \n        // Apply color tint\n        color.rgb *= colorTint;\n        \n        gl_FragColor = color;\n      }\n    ";let e=new n.wbN("colorCorrection","colorCorrection",["contrast","brightness","saturation","colorTint"],null,1,this.camera,n.gPd.BILINEAR_SAMPLINGMODE,this.engine,!1);e.onApply=e=>{e.setFloat("contrast",1.2),e.setFloat("brightness",.1),e.setFloat("saturation",1.3),e.setColor3("colorTint",new n.v9j(1,.95,1.1))},this.effects.colorCorrection=e;let t=new n.lck(this.engine,"colorCorrectionEffect",()=>[e]);this.pipeline.addEffect(t)}createChromaticAberrationEffect(){let e=new s.ChromaticAberrationPostProcess("chromaticAberration",1,this.camera);e.aberrationAmount=30,e.radialIntensity=1,e.direction=new n.I9Y(.707,.707),this.effects.chromaticAberration=e;let t=new n.lck(this.engine,"chromaticAberrationEffect",()=>[e]);this.pipeline.addEffect(t)}createGrainEffect(){let e=new s.GrainPostProcess("grain",1,this.camera);e.intensity=10,e.animated=!0,this.effects.grain=e;let t=new n.lck(this.engine,"grainEffect",()=>[e]);this.pipeline.addEffect(t)}createMotionBlurEffect(){let e=new s.MotionBlurPostProcess("motionBlur",this.scene,1,this.camera);e.motionStrength=.5,e.motionBlurSamples=32,this.effects.motionBlur=e;let t=new n.lck(this.engine,"motionBlurEffect",()=>[e]);this.pipeline.addEffect(t)}createDepthOfFieldEffect(){let e=new s.DepthOfFieldPostProcess("depthOfField",this.scene,1,this.camera);e.focusDistance=2e3,e.focalLength=50,e.fStop=1.4,this.effects.depthOfField=e;let t=new n.lck(this.engine,"depthOfFieldEffect",()=>[e]);this.pipeline.addEffect(t)}setBloomIntensity(e){this.effects.bloom&&(this.effects.bloom.weight=e)}setGlitchIntensity(e){this.effects.glitch&&(this.effects.glitch.onApply=t=>{t.setFloat("time",.001*performance.now()),t.setFloat("distortion",e),t.setFloat("speed",1)})}setFilmIntensity(e){this.effects.film&&(this.effects.film.onApply=t=>{t.setFloat("time",.001*performance.now()),t.setFloat("intensity",e),t.setFloat("scanlines",.5*e)})}setMotionBlurIntensity(e){this.effects.motionBlur&&(this.effects.motionBlur.motionStrength=e)}setDepthOfFieldFocus(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1.4;this.effects.depthOfField&&(this.effects.depthOfField.focusDistance=e,this.effects.depthOfField.focalLength=t,this.effects.depthOfField.fStop=i)}enableEffect(e,t){this.effects[e]&&this.effects[e].setEnabled(t)}dispose(){this.pipeline&&(this.scene.postProcessRenderPipelineManager.detachCamerasFromRenderPipeline("cyberpunkPipeline",this.camera),this.pipeline.dispose()),Object.values(this.effects).forEach(e=>{e&&e.dispose&&e.dispose()})}constructor(e,t){this.scene=e,this.camera=t,this.engine=e.getEngine(),this.pipeline=null,this.effects={},this.setupPipeline()}}class r{async init(){try{this.engine=new n.N$8(this.canvas,!0,{preserveDrawingBuffer:!0,stencil:!0,antialias:!0,alpha:!0,premultipliedAlpha:!1}),this.scene=new n.Z58(this.engine),this.scene.clearColor=new n.ov8(0,0,0,0),this.camera=new n.SCQ("camera",new n.Pq0(0,0,-10),this.scene),this.camera.setTarget(n.Pq0.Zero()),this.setupAdvancedLighting(),await this.createParticleSystem(),this.postProcessing=new o(this.scene,this.camera),this.addEventListeners(),this.startRenderLoop(),console.log("Babylon.js particle system initialized successfully")}catch(e){console.error("Error initializing Babylon.js particle system:",e)}}setupAdvancedLighting(){let e=new n.ZyN("directionalLight",new n.Pq0(-1,-1,-1),this.scene);e.intensity=1.2,e.diffuse=new n.v9j(.8,.9,1);let t=new n.ZyN("rimLight",new n.Pq0(1,.5,1),this.scene);t.intensity=.6,t.diffuse=new n.v9j(0,1,1);let i=new n.nCl("spotLight",new n.Pq0(0,30,0),new n.Pq0(0,-1,0),Math.PI/3,2,this.scene);i.intensity=.8,i.diffuse=new n.v9j(1,0,1);let s=new n.HiM("pointLight1",new n.Pq0(-20,10,-20),this.scene);s.intensity=.4,s.diffuse=new n.v9j(0,1,0);let o=new n.HiM("pointLight2",new n.Pq0(20,10,20),this.scene);o.intensity=.4,o.diffuse=new n.v9j(1,.5,0),this.lights={directional:e,rim:t,spot:i,point1:s,point2:o}}async createParticleSystem(){let e=this.createParticleTexture();this.particleSystem=new n.okU("particles",2e3,this.scene),this.particleSystem.particleTexture=e,this.particleSystem.emitter=n.Pq0.Zero(),this.particleSystem.minEmitBox=new n.Pq0(-50,-50,-50),this.particleSystem.maxEmitBox=new n.Pq0(50,50,50),this.particleSystem.color1=this.colorSchemes.cyberpunk.primary,this.particleSystem.color2=this.colorSchemes.cyberpunk.secondary,this.particleSystem.colorDead=new n.ov8(0,0,0,0),this.particleSystem.minSize=.1,this.particleSystem.maxSize=2,this.particleSystem.minLifeTime=2,this.particleSystem.maxLifeTime=8,this.particleSystem.emitRate=100,this.particleSystem.blendMode=n.okU.BLENDMODE_ONEONE,this.particleSystem.direction1=new n.Pq0(-1,-1,-1),this.particleSystem.direction2=new n.Pq0(1,1,1),this.particleSystem.minAngularSpeed=0,this.particleSystem.maxAngularSpeed=Math.PI,this.particleSystem.minInitialRotation=0,this.particleSystem.maxInitialRotation=Math.PI,this.particleSystem.gravity=new n.Pq0(0,-9.81,0),this.particleSystem.start(),await this.createCustomShader()}createParticleTexture(){let e=new n.RCS("particleTexture",64,this.scene),t=e.getContext();t.fillStyle="rgba(0, 0, 0, 0)",t.fillRect(0,0,64,64);let i=t.createRadialGradient(32,32,0,32,32,16);return i.addColorStop(0,"rgba(0, 255, 255, 1)"),i.addColorStop(.5,"rgba(0, 255, 255, 0.5)"),i.addColorStop(1,"rgba(0, 255, 255, 0)"),t.fillStyle=i,t.beginPath(),t.arc(32,32,16,0,2*Math.PI),t.fill(),e.update(),e}async createCustomShader(){n.Mjc.ShadersStore.customParticleVertexShader="\n      precision highp float;\n      \n      attribute vec3 position;\n      attribute vec2 uv;\n      attribute vec4 color;\n      attribute float age;\n      attribute float life;\n      attribute vec3 velocity;\n      \n      uniform mat4 view;\n      uniform mat4 projection;\n      uniform float time;\n      uniform vec2 mouse;\n      uniform vec2 resolution;\n      \n      varying vec2 vUV;\n      varying vec4 vColor;\n      varying float vAge;\n      \n      void main() {\n        vUV = uv;\n        vColor = color;\n        vAge = age / life;\n        \n        vec3 pos = position;\n        \n        // Wave animation\n        float wave1 = sin(time * 0.002 + position.x * 0.01) * 15.0;\n        float wave2 = cos(time * 0.003 + position.y * 0.008) * 10.0;\n        float wave3 = sin(time * 0.001 + position.z * 0.005) * 8.0;\n        \n        pos.y += wave1 + wave2;\n        pos.x += wave2 + wave3;\n        pos.z += wave1 + wave3;\n        \n        // Mouse interaction\n        vec2 mouseInfluence = (mouse - 0.5) * 2.0;\n        float mouseDistance = length(mouseInfluence);\n        float influence = 1.0 / (1.0 + mouseDistance * 0.1);\n        \n        pos.xy += mouseInfluence * influence * 50.0;\n        \n        gl_Position = projection * view * vec4(pos, 1.0);\n        gl_PointSize = 2.0 * (1.0 - vAge) + 1.0;\n      }\n    ",n.Mjc.ShadersStore.customParticleFragmentShader="\n      precision highp float;\n      \n      uniform sampler2D textureSampler;\n      uniform float time;\n      \n      varying vec2 vUV;\n      varying vec4 vColor;\n      varying float vAge;\n      \n      void main() {\n        vec4 textureColor = texture2D(textureSampler, vUV);\n        \n        // Fade out over lifetime\n        float alpha = (1.0 - vAge) * textureColor.a;\n        \n        // Pulsing effect\n        alpha *= 0.8 + 0.2 * sin(time * 0.005);\n        \n        gl_FragColor = vec4(vColor.rgb * textureColor.rgb, alpha);\n      }\n    "}addEventListeners(){window.addEventListener("resize",()=>this.onWindowResize(),!1),document.addEventListener("mousemove",e=>this.onMouseMove(e),!1),document.addEventListener("touchmove",e=>this.onTouchMove(e),!1)}onWindowResize(){this.engine&&this.engine.resize()}onMouseMove(e){this.mouseTarget.x=e.clientX/window.innerWidth*2-1,this.mouseTarget.y=-(2*(e.clientY/window.innerHeight))+1}onTouchMove(e){if(e.touches.length>0){let t=e.touches[0];this.mouseTarget.x=t.clientX/window.innerWidth*2-1,this.mouseTarget.y=-(2*(t.clientY/window.innerHeight))+1}}startRenderLoop(){this.engine.runRenderLoop(()=>{this.update(),this.scene.render()})}update(){if(this.time+=this.engine.getDeltaTime(),this.mouse.x+=(this.mouseTarget.x-this.mouse.x)*.05,this.mouse.y+=(this.mouseTarget.y-this.mouse.y)*.05,this.particleSystem){let e=.5*Math.sin(.001*this.time)+.5;this.particleSystem.color1=n.ov8.Lerp(this.colorSchemes.cyberpunk.primary,this.colorSchemes.cyberpunk.secondary,e)}if(this.lights){let e=5e-4*this.time;this.lights.spot.intensity=.8+.3*Math.sin(3*e),this.lights.point1.position.x=25*Math.cos(e),this.lights.point1.position.z=25*Math.sin(e),this.lights.point2.position.x=25*Math.cos(e+Math.PI),this.lights.point2.position.z=25*Math.sin(e+Math.PI),this.lights.point1.intensity=.4+.2*Math.sin(2*e),this.lights.point2.intensity=.4+.2*Math.cos(2.5*e)}}setColorScheme(e){this.colorSchemes[e]&&this.particleSystem&&(this.particleSystem.color1=this.colorSchemes[e].primary,this.particleSystem.color2=this.colorSchemes[e].secondary)}destroy(){this.postProcessing&&this.postProcessing.dispose(),this.particleSystem&&this.particleSystem.dispose(),this.scene&&this.scene.dispose(),this.engine&&this.engine.dispose()}constructor(e){this.container=e,this.canvas=e.querySelector("#babylon-canvas"),this.engine=null,this.scene=null,this.camera=null,this.particleSystem=null,this.postProcessing=null,this.mouse={x:0,y:0},this.mouseTarget={x:0,y:0},this.time=0,this.colorSchemes={cyberpunk:{primary:new n.ov8(0,1,1,1),secondary:new n.ov8(1,0,1,1),accent:new n.ov8(0,1,0,1)},matrix:{primary:new n.ov8(0,1,0,1),secondary:new n.ov8(0,.8,0,1),accent:new n.ov8(0,.6,0,1)},neon:{primary:new n.ov8(1,.4,0,1),secondary:new n.ov8(0,.5,1,1),accent:new n.ov8(.5,0,1,1)}},this.init()}}let a=r}}]);