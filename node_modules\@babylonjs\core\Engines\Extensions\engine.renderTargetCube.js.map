{"version": 3, "file": "engine.renderTargetCube.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.renderTargetCube.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAkB3C,UAAU,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAAU,IAAY,EAAE,OAAqC;IAC9G,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAA6B,CAAC;IAEzG,MAAM,WAAW,GAAG;QAChB,eAAe,EAAE,IAAI;QACrB,mBAAmB,EAAE,IAAI;QACzB,qBAAqB,EAAE,KAAK;QAC5B,IAAI,EAAE,SAAS,CAAC,yBAAyB;QACzC,YAAY,EAAE,SAAS,CAAC,8BAA8B;QACtD,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,GAAG,OAAO;KACb,CAAC;IACF,WAAW,CAAC,qBAAqB,GAAG,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,qBAAqB,CAAC;IAEzG,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC;QAC9F,yEAAyE;QACzE,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;IACtE,CAAC;SAAM,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,sBAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC;QAC9G,2EAA2E;QAC3E,WAAW,CAAC,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;IACtE,CAAC;IACD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IAEpB,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,6CAAqC,CAAC;IAC9E,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAE9D,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;IAEnG,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QAC/E,WAAW,CAAC,IAAI,GAAG,SAAS,CAAC,yBAAyB,CAAC;QACvD,MAAM,CAAC,IAAI,CAAC,gGAAgG,CAAC,CAAC;IAClH,CAAC;IAED,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAE3E,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;QAClC,EAAE,CAAC,UAAU,CACT,EAAE,CAAC,2BAA2B,GAAG,IAAI,EACrC,CAAC,EACD,IAAI,CAAC,iCAAiC,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,EAC5E,IAAI,EACJ,IAAI,EACJ,CAAC,EACD,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,EAC3C,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,IAAI,CAAC,EAC3C,IAAI,CACP,CAAC;IACN,CAAC;IAED,yBAAyB;IACzB,MAAM,WAAW,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAC3C,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;IAE1C,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAC,iCAAiC,CAAC,WAAW,CAAC,qBAAqB,EAAE,WAAW,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAEvJ,UAAU;IACV,IAAI,WAAW,CAAC,eAAe,EAAE,CAAC;QAC9B,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC3C,CAAC;IAED,SAAS;IACT,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IACrD,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAEnC,SAAS,CAAC,YAAY,GAAG,WAAW,CAAC;IACrC,SAAS,CAAC,oBAAoB,GAAG,WAAW,CAAC,mBAAmB,CAAC;IACjE,SAAS,CAAC,sBAAsB,GAAG,WAAW,CAAC,qBAAqB,CAAC;IAErE,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IACrB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IACvB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;IACpB,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;IACtD,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IAChD,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;IAChC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;IAEpC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1C,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAE/B,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC", "sourcesContent": ["import { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { Constants } from \"../constants\";\r\nimport { ThinEngine } from \"../thinEngine\";\r\nimport type { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { WebGLRenderTargetWrapper } from \"../WebGL/webGLRenderTargetWrapper\";\r\nimport type { RenderTargetCreationOptions } from \"../../Materials/Textures/textureCreationOptions\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a new render target cube wrapper\r\n         * @param size defines the size of the texture\r\n         * @param options defines the options used to create the texture\r\n         * @returns a new render target cube wrapper\r\n         */\r\n        createRenderTargetCubeTexture(size: number, options?: RenderTargetCreationOptions): RenderTargetWrapper;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createRenderTargetCubeTexture = function (size: number, options?: RenderTargetCreationOptions): RenderTargetWrapper {\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(false, true, size) as WebGLRenderTargetWrapper;\r\n\r\n    const fullOptions = {\r\n        generateMipMaps: true,\r\n        generateDepthBuffer: true,\r\n        generateStencilBuffer: false,\r\n        type: Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n        samplingMode: Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        format: Constants.TEXTUREFORMAT_RGBA,\r\n        ...options,\r\n    };\r\n    fullOptions.generateStencilBuffer = fullOptions.generateDepthBuffer && fullOptions.generateStencilBuffer;\r\n\r\n    if (fullOptions.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n        // if floating point linear (gl.FLOAT) then force to NEAREST_SAMPLINGMODE\r\n        fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n    } else if (fullOptions.type === Constants.TEXTURETYPE_HALF_FLOAT && !this._caps.textureHalfFloatLinearFiltering) {\r\n        // if floating point linear (HALF_FLOAT) then force to NEAREST_SAMPLINGMODE\r\n        fullOptions.samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n    }\r\n    const gl = this._gl;\r\n\r\n    const texture = new InternalTexture(this, InternalTextureSource.RenderTarget);\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n\r\n    const filters = this._getSamplingParameters(fullOptions.samplingMode, fullOptions.generateMipMaps);\r\n\r\n    if (fullOptions.type === Constants.TEXTURETYPE_FLOAT && !this._caps.textureFloat) {\r\n        fullOptions.type = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n        Logger.Warn(\"Float textures are not supported. Cube render target forced to TEXTURETYPE_UNESIGNED_BYTE type\");\r\n    }\r\n\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MIN_FILTER, filters.min);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n\r\n    for (let face = 0; face < 6; face++) {\r\n        gl.texImage2D(\r\n            gl.TEXTURE_CUBE_MAP_POSITIVE_X + face,\r\n            0,\r\n            this._getRGBABufferInternalSizedFormat(fullOptions.type, fullOptions.format),\r\n            size,\r\n            size,\r\n            0,\r\n            this._getInternalFormat(fullOptions.format),\r\n            this._getWebGLTextureType(fullOptions.type),\r\n            null\r\n        );\r\n    }\r\n\r\n    // Create the framebuffer\r\n    const framebuffer = gl.createFramebuffer();\r\n    this._bindUnboundFramebuffer(framebuffer);\r\n\r\n    rtWrapper._depthStencilBuffer = this._setupFramebufferDepthAttachments(fullOptions.generateStencilBuffer, fullOptions.generateDepthBuffer, size, size);\r\n\r\n    // MipMaps\r\n    if (fullOptions.generateMipMaps) {\r\n        gl.generateMipmap(gl.TEXTURE_CUBE_MAP);\r\n    }\r\n\r\n    // Unbind\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n    this._bindUnboundFramebuffer(null);\r\n\r\n    rtWrapper._framebuffer = framebuffer;\r\n    rtWrapper._generateDepthBuffer = fullOptions.generateDepthBuffer;\r\n    rtWrapper._generateStencilBuffer = fullOptions.generateStencilBuffer;\r\n\r\n    texture.width = size;\r\n    texture.height = size;\r\n    texture.isReady = true;\r\n    texture.isCube = true;\r\n    texture.samples = 1;\r\n    texture.generateMipMaps = fullOptions.generateMipMaps;\r\n    texture.samplingMode = fullOptions.samplingMode;\r\n    texture.type = fullOptions.type;\r\n    texture.format = fullOptions.format;\r\n\r\n    this._internalTexturesCache.push(texture);\r\n    rtWrapper.setTextures(texture);\r\n\r\n    return rtWrapper;\r\n};\r\n"]}