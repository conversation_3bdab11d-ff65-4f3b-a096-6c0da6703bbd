{"version": 3, "file": "animation.optimizations.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Animations/animation.optimizations.ts"], "names": [], "mappings": "AA2BA;;;;;;GAMG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAY,EAAE,UAA0C,EAAE;IACzF,MAAM,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,IAAI,IAAI,CAAC;IACtE,MAAM,cAAc,GAAG,OAAO,CAAC,sBAAsB,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAE3G,gEAAgE;IAChE,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;IAEtC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC3B,OAAO;IACX,CAAC;IAED,MAAM,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IAEtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC1C,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAElC,iDAAiD;QACjD,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,kBAAkB,CAAC,CAAC;IAC7E,CAAC;IAED,IAAI,sBAAsB,IAAI,cAAc,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzE,2DAA2D;QAC3D,MAAM,oBAAoB,GAAG,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAClE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChE,MAAM,gBAAgB,GAAG,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC9D,gBAAgB,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClE,CAAC;IACL,CAAC;IAED,IAAI,cAAc,IAAI,cAAc,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjE,yEAAyE;QACzE,MAAM,aAAa,GAAG,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChE,MAAM,gBAAgB,GAAG,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,SAAS,GAAG,gBAAgB,CAAC,UAAU,CAAC;YAC9C,SAAS,CAAC,cAAc,GAAG,aAAa,CAAC;QAC7C,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB,GAAG,CAAC,cAAc,CAAC,CAAC;AAChD,CAAC", "sourcesContent": ["import type { Scene } from \"core/scene\";\r\n\r\n/**\r\n * Interface used to define the optimization options for animations\r\n */\r\nexport type AnimationOptimization =\r\n    | {\r\n          /**\r\n           * Do not merge runtime animations\r\n           * @defaultValue true\r\n           */\r\n          mergeRuntimeAnimations: false;\r\n      }\r\n    | {\r\n          /**\r\n           * All runtime animations will be merged into the first animatable\r\n           * @defaultValue true\r\n           */\r\n          mergeRuntimeAnimations: true;\r\n          /**\r\n           * If true, all keyframes evaluation will be merged from the first runtime animation\r\n           * You need to turn on `mergeRuntimeAnimations` for this to work\r\n           * @defaultValue false\r\n           */\r\n          mergeKeyFrames: boolean;\r\n      };\r\n\r\n/**\r\n * This is a destructive optimization that merges all animatables into the first one.\r\n * That animatable will also host all the runtime animations.\r\n * We expect that all the animatables are on the same timeframe (same start, end, loop, etc..)\r\n * @param scene defines the scene to optimize\r\n * @param options defines the optimization options\r\n */\r\nexport function OptimizeAnimations(scene: Scene, options: Partial<AnimationOptimization> = {}) {\r\n    const mergeRuntimeAnimations = options.mergeRuntimeAnimations ?? true;\r\n    const mergeKeyFrames = options.mergeRuntimeAnimations === true ? (options.mergeKeyFrames ?? false) : false;\r\n\r\n    // We will go through all the current animatables and merge them\r\n    const animatables = scene.animatables;\r\n\r\n    if (animatables.length === 0) {\r\n        return;\r\n    }\r\n\r\n    const mainAnimatable = animatables[0];\r\n\r\n    for (let i = 1; i < animatables.length; i++) {\r\n        const animatable = animatables[i];\r\n\r\n        // Merge the current animatable with the main one\r\n        mainAnimatable._runtimeAnimations.push(...animatable._runtimeAnimations);\r\n    }\r\n\r\n    if (mergeRuntimeAnimations && mainAnimatable._runtimeAnimations.length > 1) {\r\n        // Make sure only one runtime animation is driving the beat\r\n        const mainRuntimeAnimation = mainAnimatable._runtimeAnimations[0];\r\n        for (let i = 1; i < mainAnimatable._runtimeAnimations.length; i++) {\r\n            const runtimeAnimation = mainAnimatable._runtimeAnimations[i];\r\n            runtimeAnimation._coreRuntimeAnimation = mainRuntimeAnimation;\r\n        }\r\n    }\r\n\r\n    if (mergeKeyFrames && mainAnimatable._runtimeAnimations.length > 1) {\r\n        // Merge the keyframes from all the runtime animations into the first one\r\n        const mainAnimation = mainAnimatable._runtimeAnimations[0]._animation;\r\n        for (let i = 1; i < mainAnimatable._runtimeAnimations.length; i++) {\r\n            const runtimeAnimation = mainAnimatable._runtimeAnimations[i];\r\n            const animation = runtimeAnimation._animation;\r\n            animation._coreAnimation = mainAnimation;\r\n        }\r\n    }\r\n\r\n    scene._activeAnimatables = [mainAnimatable];\r\n}\r\n"]}