{"version": 3, "file": "shaderCodeConditionNode.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderCodeConditionNode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAGlD,gBAAgB;AAChB,MAAM,OAAO,uBAAwB,SAAQ,cAAc;IAC9C,OAAO,CAAC,aAAwC,EAAE,OAA4B,EAAE,qBAAgD;QACrI,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACxD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAElC,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,EAAE,qBAAqB,CAAC,CAAC;YACvE,CAAC;QACL,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;CACJ", "sourcesContent": ["import { ShaderCodeNode } from \"./shaderCodeNode\";\r\nimport type { _IProcessingOptions } from \"./shaderProcessingOptions\";\r\n\r\n/** @internal */\r\nexport class ShaderCodeConditionNode extends ShaderCodeNode {\r\n    override process(preprocessors: { [key: string]: string }, options: _IProcessingOptions, preProcessorsFromCode: { [key: string]: string }) {\r\n        for (let index = 0; index < this.children.length; index++) {\r\n            const node = this.children[index];\r\n\r\n            if (node.isValid(preprocessors)) {\r\n                return node.process(preprocessors, options, preProcessorsFromCode);\r\n            }\r\n        }\r\n\r\n        return \"\";\r\n    }\r\n}\r\n"]}