import type { ActionManager } from "./actionManager.js";
/**
 * A Condition applied to an Action
 */
export declare class Condition {
    /**
     * Internal only - manager for action
     * @internal
     */
    _actionManager: ActionManager;
    /**
     * @internal
     */
    _evaluationId: number;
    /**
     * @internal
     */
    _currentResult: boolean;
    /**
     * Creates a new Condition
     * @param actionManager the manager of the action the condition is applied to
     */
    constructor(actionManager: ActionManager);
    /**
     * Check if the current condition is valid
     * @returns a boolean
     */
    isValid(): boolean;
    /**
     * @internal
     */
    _getProperty(propertyPath: string): string;
    /**
     * @internal
     */
    _getEffectiveTarget(target: any, propertyPath: string): any;
    /**
     * Serialize placeholder for child classes
     * @returns the serialized object
     */
    serialize(): any;
    /**
     * @internal
     */
    protected _serialize(serializedCondition: any): any;
}
/**
 * Defines specific conditional operators as extensions of Condition
 */
export declare class ValueCondition extends Condition {
    /** path to specify the property of the target the conditional operator uses  */
    propertyPath: string;
    /** the value compared by the conditional operator against the current value of the property */
    value: any;
    /** [number] the conditional operator, default ValueCondition.IsEqual */
    operator: number;
    private static _IsEqual;
    private static _IsDifferent;
    private static _IsGreater;
    private static _IsLesser;
    /**
     * returns the number for IsEqual
     */
    static get IsEqual(): number;
    /**
     * Returns the number for IsDifferent
     */
    static get IsDifferent(): number;
    /**
     * Returns the number for IsGreater
     */
    static get IsGreater(): number;
    /**
     * Returns the number for IsLesser
     */
    static get IsLesser(): number;
    /**
     * Internal only The action manager for the condition
     * @internal
     */
    _actionManager: ActionManager;
    private _target;
    private _effectiveTarget;
    private _property;
    /**
     * Creates a new ValueCondition
     * @param actionManager manager for the action the condition applies to
     * @param target for the action
     * @param propertyPath path to specify the property of the target the conditional operator uses
     * @param value the value compared by the conditional operator against the current value of the property
     * @param operator the conditional operator, default ValueCondition.IsEqual
     */
    constructor(actionManager: ActionManager, target: any, 
    /** path to specify the property of the target the conditional operator uses  */
    propertyPath: string, 
    /** the value compared by the conditional operator against the current value of the property */
    value: any, 
    /** [number] the conditional operator, default ValueCondition.IsEqual */
    operator?: number);
    /**
     * Compares the given value with the property value for the specified conditional operator
     * @returns the result of the comparison
     */
    isValid(): boolean;
    /**
     * Serialize the ValueCondition into a JSON compatible object
     * @returns serialization object
     */
    serialize(): any;
    /**
     * Gets the name of the conditional operator for the ValueCondition
     * @param operator the conditional operator
     * @returns the name
     */
    static GetOperatorName(operator: number): string;
}
/**
 * Defines a predicate condition as an extension of Condition
 */
export declare class PredicateCondition extends Condition {
    /** defines the predicate function used to validate the condition */
    predicate: () => boolean;
    /**
     * Internal only - manager for action
     * @internal
     */
    _actionManager: ActionManager;
    /**
     * Creates a new PredicateCondition
     * @param actionManager manager for the action the condition applies to
     * @param predicate defines the predicate function used to validate the condition
     */
    constructor(actionManager: ActionManager, 
    /** defines the predicate function used to validate the condition */
    predicate: () => boolean);
    /**
     * @returns the validity of the predicate condition
     */
    isValid(): boolean;
}
/**
 * Defines a state condition as an extension of Condition
 */
export declare class StateCondition extends Condition {
    /** Value to compare with target state  */
    value: string;
    /**
     * Internal only - manager for action
     * @internal
     */
    _actionManager: ActionManager;
    private _target;
    /**
     * Creates a new StateCondition
     * @param actionManager manager for the action the condition applies to
     * @param target of the condition
     * @param value to compare with target state
     */
    constructor(actionManager: ActionManager, target: any, 
    /** Value to compare with target state  */
    value: string);
    /**
     * Gets a boolean indicating if the current condition is met
     * @returns the validity of the state
     */
    isValid(): boolean;
    /**
     * Serialize the StateCondition into a JSON compatible object
     * @returns serialization object
     */
    serialize(): any;
}
