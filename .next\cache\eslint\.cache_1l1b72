[{"C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\app\\blog\\page.tsx": "1", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\app\\blog\\[slug]\\page.tsx": "2", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\Advanced3DShowcase.tsx": "5", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\BabylonBackground.tsx": "6", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\Footer.tsx": "7", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\Header.tsx": "8", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\mdx-components.tsx": "9", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ProjectCard.tsx": "10", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\sections\\Advanced3DSection.tsx": "11", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\SkillCard.tsx": "12", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\badge.tsx": "13", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\button.tsx": "14", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\card.tsx": "15", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\dialog.tsx": "16", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\form.tsx": "17", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\input.tsx": "18", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\label.tsx": "19", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\navigation-menu.tsx": "20", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\sheet.tsx": "21", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\slider.tsx": "22", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\sonner.tsx": "23", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\switch.tsx": "24", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\tabs.tsx": "25", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\textarea.tsx": "26", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\lib\\content.ts": "27", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\lib\\utils.ts": "28", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\advanced-3d-showcase.ts": "29", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\babylon-particles.js": "30", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\babylon-particles.ts": "31", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\babylon-post-processing.js": "32", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\gsap-animations.js": "33", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\content-manager.js": "34", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\error-tracker.js": "35", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\performance.js": "36", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\testing.js": "37", "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\theme-system.js": "38"}, {"size": 3209, "mtime": 1753719426934, "results": "39", "hashOfConfig": "40"}, {"size": 3208, "mtime": 1753722249029, "results": "41", "hashOfConfig": "40"}, {"size": 2198, "mtime": 1753719426934, "results": "42", "hashOfConfig": "40"}, {"size": 9177, "mtime": 1753722071396, "results": "43", "hashOfConfig": "40"}, {"size": 6615, "mtime": 1753721797328, "results": "44", "hashOfConfig": "40"}, {"size": 2797, "mtime": 1753719426902, "results": "45", "hashOfConfig": "40"}, {"size": 6873, "mtime": 1753719426902, "results": "46", "hashOfConfig": "40"}, {"size": 8365, "mtime": 1753722059435, "results": "47", "hashOfConfig": "40"}, {"size": 6458, "mtime": 1753719426934, "results": "48", "hashOfConfig": "40"}, {"size": 6388, "mtime": 1753719426902, "results": "49", "hashOfConfig": "40"}, {"size": 14596, "mtime": 1753722002634, "results": "50", "hashOfConfig": "40"}, {"size": 4466, "mtime": 1753719426902, "results": "51", "hashOfConfig": "40"}, {"size": 1128, "mtime": 1753719426934, "results": "52", "hashOfConfig": "40"}, {"size": 2123, "mtime": 1753719426934, "results": "53", "hashOfConfig": "40"}, {"size": 1989, "mtime": 1753719426934, "results": "54", "hashOfConfig": "40"}, {"size": 3982, "mtime": 1753719426924, "results": "55", "hashOfConfig": "40"}, {"size": 3759, "mtime": 1753719426918, "results": "56", "hashOfConfig": "40"}, {"size": 967, "mtime": 1753719426918, "results": "57", "hashOfConfig": "40"}, {"size": 611, "mtime": 1753719426918, "results": "58", "hashOfConfig": "40"}, {"size": 6664, "mtime": 1753719426924, "results": "59", "hashOfConfig": "40"}, {"size": 4090, "mtime": 1753719426924, "results": "60", "hashOfConfig": "40"}, {"size": 2001, "mtime": 1753722138648, "results": "61", "hashOfConfig": "40"}, {"size": 564, "mtime": 1753719426924, "results": "62", "hashOfConfig": "40"}, {"size": 1177, "mtime": 1753722138640, "results": "63", "hashOfConfig": "40"}, {"size": 1969, "mtime": 1753722138627, "results": "64", "hashOfConfig": "40"}, {"size": 759, "mtime": 1753719426918, "results": "65", "hashOfConfig": "40"}, {"size": 7667, "mtime": 1753719426808, "results": "66", "hashOfConfig": "40"}, {"size": 166, "mtime": 1753719426808, "results": "67", "hashOfConfig": "40"}, {"size": 23104, "mtime": 1753723172299, "results": "68", "hashOfConfig": "40"}, {"size": 11797, "mtime": 1753713896512, "results": "69", "hashOfConfig": "70"}, {"size": 35184, "mtime": 1753723304520, "results": "71", "hashOfConfig": "40"}, {"size": 11378, "mtime": 1753713950215, "results": "72", "hashOfConfig": "70"}, {"size": 11632, "mtime": 1753719426562, "results": "73", "hashOfConfig": "70"}, {"size": 9687, "mtime": 1753719426630, "results": "74", "hashOfConfig": "70"}, {"size": 12609, "mtime": 1753719426657, "results": "75", "hashOfConfig": "70"}, {"size": 9099, "mtime": 1753719426697, "results": "76", "hashOfConfig": "70"}, {"size": 13329, "mtime": 1753719426697, "results": "77", "hashOfConfig": "70"}, {"size": 10990, "mtime": 1753719426697, "results": "78", "hashOfConfig": "70"}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1pxkd4c", {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "t19ayf", {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 40, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\app\\blog\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\Advanced3DShowcase.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\BabylonBackground.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\mdx-components.tsx", ["193", "194"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ProjectCard.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\sections\\Advanced3DSection.tsx", ["195", "196", "197", "198", "199"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\SkillCard.tsx", ["200"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\lib\\content.ts", ["201", "202", "203"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\advanced-3d-showcase.ts", ["204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\babylon-particles.js", ["220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\babylon-particles.ts", ["232", "233", "234", "235", "236", "237", "238", "239", "240", "241", "242", "243", "244", "245", "246", "247", "248", "249", "250", "251", "252", "253", "254", "255", "256", "257", "258", "259", "260", "261", "262", "263", "264", "265", "266", "267", "268", "269", "270", "271"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\babylon-post-processing.js", ["272", "273"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\scripts\\gsap-animations.js", ["274"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\content-manager.js", ["275", "276", "277", "278", "279"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\error-tracker.js", ["280"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\performance.js", [], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\testing.js", ["281", "282", "283"], [], "C:\\Users\\<USER>\\dyad-apps\\Website-Portofolio\\src\\utils\\theme-system.js", ["284"], [], {"ruleId": "285", "severity": 1, "message": "286", "line": 147, "column": 5, "nodeType": "287", "endLine": 151, "endColumn": 7}, {"ruleId": "288", "severity": 1, "message": "289", "line": 214, "column": 35, "nodeType": null, "messageId": "290", "endLine": 214, "endColumn": 40}, {"ruleId": "288", "severity": 1, "message": "291", "line": 3, "column": 28, "nodeType": null, "messageId": "290", "endLine": 3, "endColumn": 37}, {"ruleId": "288", "severity": 1, "message": "292", "line": 5, "column": 10, "nodeType": null, "messageId": "290", "endLine": 5, "endColumn": 16}, {"ruleId": "288", "severity": 1, "message": "293", "line": 14, "column": 3, "nodeType": null, "messageId": "290", "endLine": 14, "endColumn": 6}, {"ruleId": "288", "severity": 1, "message": "294", "line": 17, "column": 3, "nodeType": null, "messageId": "290", "endLine": 17, "endColumn": 10}, {"ruleId": "288", "severity": 1, "message": "295", "line": 227, "column": 55, "nodeType": null, "messageId": "290", "endLine": 227, "endColumn": 60}, {"ruleId": "296", "severity": 1, "message": "297", "line": 76, "column": 36, "nodeType": "298", "endLine": 76, "endColumn": 43}, {"ruleId": "288", "severity": 1, "message": "299", "line": 117, "column": 12, "nodeType": null, "messageId": "290", "endLine": 117, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "299", "line": 199, "column": 12, "nodeType": null, "messageId": "290", "endLine": 199, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "299", "line": 218, "column": 12, "nodeType": null, "messageId": "290", "endLine": 218, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "300", "line": 25, "column": 3, "nodeType": null, "messageId": "290", "endLine": 25, "endColumn": 8}, {"ruleId": "288", "severity": 1, "message": "301", "line": 27, "column": 3, "nodeType": null, "messageId": "290", "endLine": 27, "endColumn": 16}, {"ruleId": "288", "severity": 1, "message": "302", "line": 30, "column": 3, "nodeType": null, "messageId": "290", "endLine": 30, "endColumn": 15}, {"ruleId": "288", "severity": 1, "message": "303", "line": 31, "column": 3, "nodeType": null, "messageId": "290", "endLine": 31, "endColumn": 8}, {"ruleId": "288", "severity": 1, "message": "304", "line": 32, "column": 3, "nodeType": null, "messageId": "290", "endLine": 32, "endColumn": 14}, {"ruleId": "288", "severity": 1, "message": "305", "line": 33, "column": 3, "nodeType": null, "messageId": "290", "endLine": 33, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "306", "line": 34, "column": 3, "nodeType": null, "messageId": "290", "endLine": 34, "endColumn": 9}, {"ruleId": "288", "severity": 1, "message": "307", "line": 35, "column": 3, "nodeType": null, "messageId": "290", "endLine": 35, "endColumn": 13}, {"ruleId": "288", "severity": 1, "message": "308", "line": 36, "column": 3, "nodeType": null, "messageId": "290", "endLine": 36, "endColumn": 19}, {"ruleId": "288", "severity": 1, "message": "309", "line": 37, "column": 3, "nodeType": null, "messageId": "290", "endLine": 37, "endColumn": 20}, {"ruleId": "288", "severity": 1, "message": "310", "line": 38, "column": 3, "nodeType": null, "messageId": "290", "endLine": 38, "endColumn": 25}, {"ruleId": "288", "severity": 1, "message": "311", "line": 414, "column": 20, "nodeType": null, "messageId": "290", "endLine": 414, "endColumn": 24}, {"ruleId": "288", "severity": 1, "message": "312", "line": 642, "column": 17, "nodeType": null, "messageId": "290", "endLine": 642, "endColumn": 29}, {"ruleId": "288", "severity": 1, "message": "313", "line": 649, "column": 17, "nodeType": null, "messageId": "290", "endLine": 649, "endColumn": 24}, {"ruleId": "288", "severity": 1, "message": "314", "line": 671, "column": 13, "nodeType": null, "messageId": "290", "endLine": 671, "endColumn": 19}, {"ruleId": "288", "severity": 1, "message": "295", "line": 719, "column": 42, "nodeType": null, "messageId": "290", "endLine": 719, "endColumn": 47}, {"ruleId": "288", "severity": 1, "message": "315", "line": 9, "column": 3, "nodeType": null, "messageId": "290", "endLine": 9, "endColumn": 10}, {"ruleId": "288", "severity": 1, "message": "316", "line": 10, "column": 3, "nodeType": null, "messageId": "290", "endLine": 10, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "317", "line": 12, "column": 3, "nodeType": null, "messageId": "290", "endLine": 12, "endColumn": 15}, {"ruleId": "288", "severity": 1, "message": "318", "line": 13, "column": 3, "nodeType": null, "messageId": "290", "endLine": 13, "endColumn": 7}, {"ruleId": "288", "severity": 1, "message": "319", "line": 14, "column": 3, "nodeType": null, "messageId": "290", "endLine": 14, "endColumn": 14}, {"ruleId": "288", "severity": 1, "message": "308", "line": 15, "column": 3, "nodeType": null, "messageId": "290", "endLine": 15, "endColumn": 19}, {"ruleId": "288", "severity": 1, "message": "320", "line": 16, "column": 3, "nodeType": null, "messageId": "290", "endLine": 16, "endColumn": 14}, {"ruleId": "288", "severity": 1, "message": "321", "line": 18, "column": 3, "nodeType": null, "messageId": "290", "endLine": 18, "endColumn": 12}, {"ruleId": "288", "severity": 1, "message": "322", "line": 22, "column": 3, "nodeType": null, "messageId": "290", "endLine": 22, "endColumn": 18}, {"ruleId": "288", "severity": 1, "message": "323", "line": 23, "column": 3, "nodeType": null, "messageId": "290", "endLine": 23, "endColumn": 14}, {"ruleId": "288", "severity": 1, "message": "324", "line": 24, "column": 3, "nodeType": null, "messageId": "290", "endLine": 24, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "325", "line": 25, "column": 3, "nodeType": null, "messageId": "290", "endLine": 25, "endColumn": 20}, {"ruleId": "288", "severity": 1, "message": "326", "line": 5, "column": 3, "nodeType": null, "messageId": "290", "endLine": 5, "endColumn": 18}, {"ruleId": "288", "severity": 1, "message": "315", "line": 10, "column": 3, "nodeType": null, "messageId": "290", "endLine": 10, "endColumn": 10}, {"ruleId": "288", "severity": 1, "message": "316", "line": 11, "column": 3, "nodeType": null, "messageId": "290", "endLine": 11, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "317", "line": 13, "column": 3, "nodeType": null, "messageId": "290", "endLine": 13, "endColumn": 15}, {"ruleId": "288", "severity": 1, "message": "327", "line": 18, "column": 3, "nodeType": null, "messageId": "290", "endLine": 18, "endColumn": 31}, {"ruleId": "288", "severity": 1, "message": "321", "line": 20, "column": 3, "nodeType": null, "messageId": "290", "endLine": 20, "endColumn": 12}, {"ruleId": "288", "severity": 1, "message": "322", "line": 24, "column": 3, "nodeType": null, "messageId": "290", "endLine": 24, "endColumn": 18}, {"ruleId": "288", "severity": 1, "message": "324", "line": 26, "column": 3, "nodeType": null, "messageId": "290", "endLine": 26, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "325", "line": 27, "column": 3, "nodeType": null, "messageId": "290", "endLine": 27, "endColumn": 20}, {"ruleId": "288", "severity": 1, "message": "328", "line": 29, "column": 3, "nodeType": null, "messageId": "290", "endLine": 29, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "301", "line": 30, "column": 3, "nodeType": null, "messageId": "290", "endLine": 30, "endColumn": 16}, {"ruleId": "288", "severity": 1, "message": "302", "line": 31, "column": 3, "nodeType": null, "messageId": "290", "endLine": 31, "endColumn": 15}, {"ruleId": "288", "severity": 1, "message": "329", "line": 32, "column": 3, "nodeType": null, "messageId": "290", "endLine": 32, "endColumn": 13}, {"ruleId": "288", "severity": 1, "message": "330", "line": 33, "column": 3, "nodeType": null, "messageId": "290", "endLine": 33, "endColumn": 20}, {"ruleId": "288", "severity": 1, "message": "331", "line": 34, "column": 3, "nodeType": null, "messageId": "290", "endLine": 34, "endColumn": 22}, {"ruleId": "288", "severity": 1, "message": "332", "line": 35, "column": 3, "nodeType": null, "messageId": "290", "endLine": 35, "endColumn": 16}, {"ruleId": "288", "severity": 1, "message": "333", "line": 36, "column": 3, "nodeType": null, "messageId": "290", "endLine": 36, "endColumn": 11}, {"ruleId": "288", "severity": 1, "message": "334", "line": 37, "column": 3, "nodeType": null, "messageId": "290", "endLine": 37, "endColumn": 15}, {"ruleId": "288", "severity": 1, "message": "335", "line": 38, "column": 3, "nodeType": null, "messageId": "290", "endLine": 38, "endColumn": 22}, {"ruleId": "288", "severity": 1, "message": "336", "line": 39, "column": 3, "nodeType": null, "messageId": "290", "endLine": 39, "endColumn": 20}, {"ruleId": "288", "severity": 1, "message": "337", "line": 40, "column": 3, "nodeType": null, "messageId": "290", "endLine": 40, "endColumn": 20}, {"ruleId": "288", "severity": 1, "message": "338", "line": 41, "column": 3, "nodeType": null, "messageId": "290", "endLine": 41, "endColumn": 30}, {"ruleId": "288", "severity": 1, "message": "339", "line": 44, "column": 3, "nodeType": null, "messageId": "290", "endLine": 44, "endColumn": 19}, {"ruleId": "288", "severity": 1, "message": "309", "line": 54, "column": 3, "nodeType": null, "messageId": "290", "endLine": 54, "endColumn": 20}, {"ruleId": "288", "severity": 1, "message": "310", "line": 55, "column": 3, "nodeType": null, "messageId": "290", "endLine": 55, "endColumn": 25}, {"ruleId": "288", "severity": 1, "message": "340", "line": 57, "column": 3, "nodeType": null, "messageId": "290", "endLine": 57, "endColumn": 18}, {"ruleId": "288", "severity": 1, "message": "341", "line": 58, "column": 3, "nodeType": null, "messageId": "290", "endLine": 58, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "300", "line": 60, "column": 3, "nodeType": null, "messageId": "290", "endLine": 60, "endColumn": 8}, {"ruleId": "288", "severity": 1, "message": "342", "line": 61, "column": 3, "nodeType": null, "messageId": "290", "endLine": 61, "endColumn": 11}, {"ruleId": "288", "severity": 1, "message": "343", "line": 65, "column": 3, "nodeType": null, "messageId": "290", "endLine": 65, "endColumn": 25}, {"ruleId": "288", "severity": 1, "message": "344", "line": 66, "column": 3, "nodeType": null, "messageId": "290", "endLine": 66, "endColumn": 19}, {"ruleId": "288", "severity": 1, "message": "345", "line": 68, "column": 3, "nodeType": null, "messageId": "290", "endLine": 68, "endColumn": 27}, {"ruleId": "288", "severity": 1, "message": "346", "line": 69, "column": 3, "nodeType": null, "messageId": "290", "endLine": 69, "endColumn": 17}, {"ruleId": "288", "severity": 1, "message": "306", "line": 71, "column": 3, "nodeType": null, "messageId": "290", "endLine": 71, "endColumn": 9}, {"ruleId": "288", "severity": 1, "message": "307", "line": 72, "column": 3, "nodeType": null, "messageId": "290", "endLine": 72, "endColumn": 13}, {"ruleId": "288", "severity": 1, "message": "303", "line": 74, "column": 3, "nodeType": null, "messageId": "290", "endLine": 74, "endColumn": 8}, {"ruleId": "288", "severity": 1, "message": "304", "line": 75, "column": 3, "nodeType": null, "messageId": "290", "endLine": 75, "endColumn": 14}, {"ruleId": "288", "severity": 1, "message": "311", "line": 927, "column": 20, "nodeType": null, "messageId": "290", "endLine": 927, "endColumn": 24}, {"ruleId": "288", "severity": 1, "message": "347", "line": 1037, "column": 11, "nodeType": null, "messageId": "290", "endLine": 1037, "endColumn": 26}, {"ruleId": "288", "severity": 1, "message": "348", "line": 1148, "column": 11, "nodeType": null, "messageId": "290", "endLine": 1148, "endColumn": 18}, {"ruleId": "288", "severity": 1, "message": "349", "line": 13, "column": 3, "nodeType": null, "messageId": "290", "endLine": 13, "endColumn": 18}, {"ruleId": "288", "severity": 1, "message": "350", "line": 18, "column": 3, "nodeType": null, "messageId": "290", "endLine": 18, "endColumn": 39}, {"ruleId": "288", "severity": 1, "message": "295", "line": 417, "column": 40, "nodeType": null, "messageId": "290", "endLine": 417, "endColumn": 45}, {"ruleId": "288", "severity": 1, "message": "351", "line": 95, "column": 7, "nodeType": null, "messageId": "290", "endLine": 95, "endColumn": 12}, {"ruleId": "288", "severity": 1, "message": "352", "line": 96, "column": 7, "nodeType": null, "messageId": "290", "endLine": 96, "endColumn": 13}, {"ruleId": "288", "severity": 1, "message": "353", "line": 97, "column": 7, "nodeType": null, "messageId": "290", "endLine": 97, "endColumn": 13}, {"ruleId": "288", "severity": 1, "message": "354", "line": 98, "column": 7, "nodeType": null, "messageId": "290", "endLine": 98, "endColumn": 16}, {"ruleId": "288", "severity": 1, "message": "355", "line": 99, "column": 7, "nodeType": null, "messageId": "290", "endLine": 99, "endColumn": 13}, {"ruleId": "288", "severity": 1, "message": "356", "line": 282, "column": 20, "nodeType": null, "messageId": "290", "endLine": 282, "endColumn": 29}, {"ruleId": "288", "severity": 1, "message": "357", "line": 184, "column": 18, "nodeType": null, "messageId": "290", "endLine": 184, "endColumn": 19}, {"ruleId": "288", "severity": 1, "message": "358", "line": 371, "column": 21, "nodeType": null, "messageId": "290", "endLine": 371, "endColumn": 27}, {"ruleId": "288", "severity": 1, "message": "359", "line": 371, "column": 29, "nodeType": null, "messageId": "290", "endLine": 371, "endColumn": 35}, {"ruleId": "288", "severity": 1, "message": "360", "line": 286, "column": 11, "nodeType": null, "messageId": "290", "endLine": 286, "endColumn": 16}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'props' is defined but never used.", "unusedVar", "'useEffect' is defined but never used.", "'Button' is defined but never used.", "'Cpu' is defined but never used.", "'Palette' is defined but never used.", "'index' is defined but never used.", "react-hooks/exhaustive-deps", "The ref value 'cardRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'cardRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "'error' is defined but never used.", "'Sound' is defined but never used.", "'TransformNode' is defined but never used.", "'NodeMaterial' is defined but never used.", "'Tools' is defined but never used.", "'SceneLoader' is defined but never used.", "'AssetContainer' is defined but never used.", "'Matrix' is defined but never used.", "'Quaternion' is defined but never used.", "'StandardMaterial' is defined but never used.", "'ProceduralTexture' is defined but never used.", "'NoiseProceduralTexture' is defined but never used.", "'face' is defined but never used.", "'handTracking' is assigned a value but never used.", "'hitTest' is assigned a value but never used.", "'source' is assigned a value but never used.", "'Texture' is defined but never used.", "'ShaderMaterial' is defined but never used.", "'VertexBuffer' is defined but never used.", "'Mesh' is defined but never used.", "'MeshBuilder' is defined but never used.", "'PBRMaterial' is defined but never used.", "'Constants' is defined but never used.", "'ShadowGenerator' is defined but never used.", "'CubeTexture' is defined but never used.", "'HDRCubeTexture' is defined but never used.", "'EnvironmentHelper' is defined but never used.", "'ArcRotateCamera' is defined but never used.", "'PBRMetallicRoughnessMaterial' is defined but never used.", "'AnimationGroup' is defined but never used.", "'InputBlock' is defined but never used.", "'VertexOutputBlock' is defined but never used.", "'FragmentOutputBlock' is defined but never used.", "'MultiplyBlock' is defined but never used.", "'AddBlock' is defined but never used.", "'TextureBlock' is defined but never used.", "'VectorSplitterBlock' is defined but never used.", "'VectorMergerBlock' is defined but never used.", "'TrigonometryBlock' is defined but never used.", "'TrigonometryBlockOperations' is defined but never used.", "'HemisphericLight' is defined but never used.", "'PhysicsImpostor' is defined but never used.", "'CannonJSPlugin' is defined but never used.", "'Analyser' is defined but never used.", "'WebXRDefaultExperience' is defined but never used.", "'WebXRFeatureName' is defined but never used.", "'NodeMaterialSystemValues' is defined but never used.", "'TransformBlock' is defined but never used.", "'fxaaPostProcess' is assigned a value but never used.", "'scaleUp' is assigned a value but never used.", "'FxaaPostProcess' is defined but never used.", "'VolumetricLightScatteringPostProcess' is defined but never used.", "'limit' is assigned a value but never used.", "'offset' is assigned a value but never used.", "'sortBy' is assigned a value but never used.", "'sortOrder' is assigned a value but never used.", "'filter' is assigned a value but never used.", "'errorData' is defined but never used.", "'e' is defined but never used.", "'color1' is defined but never used.", "'color2' is defined but never used.", "'theme' is assigned a value but never used."]