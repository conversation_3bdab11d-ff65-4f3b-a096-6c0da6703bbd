"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[16],{1743:(t,e,r)=>{r.d(e,{AA:()=>f,I9:()=>y,IU:()=>m,PT:()=>c,Pq:()=>u,uq:()=>x});var i=r(30645),s=r(44257),o=r(20482),a=r(41579),n=r(18671),_=r(38187),h=r(26362);let l=t=>parseInt(t.toString().replace(/\W/g,""));class y{constructor(t=0,e=0){this.x=t,this.y=e}toString(){return`{X: ${this.x} Y: ${this.y}}`}getClassName(){return"Vector2"}getHashCode(){let t=l(this.x),e=l(this.y),r=t;return 397*r^e}toArray(t,e=0){return t[e]=this.x,t[e+1]=this.y,this}fromArray(t,e=0){return y.FromArrayToRef(t,e,this),this}asArray(){return[this.x,this.y]}copyFrom(t){return this.x=t.x,this.y=t.y,this}copyFromFloats(t,e){return this.x=t,this.y=e,this}set(t,e){return this.copyFromFloats(t,e)}setAll(t){return this.copyFromFloats(t,t)}add(t){return new y(this.x+t.x,this.y+t.y)}addToRef(t,e){return e.x=this.x+t.x,e.y=this.y+t.y,e}addInPlace(t){return this.x+=t.x,this.y+=t.y,this}addInPlaceFromFloats(t,e){return this.x+=t,this.y+=e,this}addVector3(t){return new y(this.x+t.x,this.y+t.y)}subtract(t){return new y(this.x-t.x,this.y-t.y)}subtractToRef(t,e){return e.x=this.x-t.x,e.y=this.y-t.y,e}subtractInPlace(t){return this.x-=t.x,this.y-=t.y,this}multiplyInPlace(t){return this.x*=t.x,this.y*=t.y,this}multiply(t){return new y(this.x*t.x,this.y*t.y)}multiplyToRef(t,e){return e.x=this.x*t.x,e.y=this.y*t.y,e}multiplyByFloats(t,e){return new y(this.x*t,this.y*e)}divide(t){return new y(this.x/t.x,this.y/t.y)}divideToRef(t,e){return e.x=this.x/t.x,e.y=this.y/t.y,e}divideInPlace(t){return this.x=this.x/t.x,this.y=this.y/t.y,this}minimizeInPlace(t){return this.minimizeInPlaceFromFloats(t.x,t.y)}maximizeInPlace(t){return this.maximizeInPlaceFromFloats(t.x,t.y)}minimizeInPlaceFromFloats(t,e){return this.x=Math.min(t,this.x),this.y=Math.min(e,this.y),this}maximizeInPlaceFromFloats(t,e){return this.x=Math.max(t,this.x),this.y=Math.max(e,this.y),this}subtractFromFloats(t,e){return new y(this.x-t,this.y-e)}subtractFromFloatsToRef(t,e,r){return r.x=this.x-t,r.y=this.y-e,r}negate(){return new y(-this.x,-this.y)}negateInPlace(){return this.x*=-1,this.y*=-1,this}negateToRef(t){return t.x=-this.x,t.y=-this.y,t}scaleInPlace(t){return this.x*=t,this.y*=t,this}scale(t){return new y(this.x*t,this.y*t)}scaleToRef(t,e){return e.x=this.x*t,e.y=this.y*t,e}scaleAndAddToRef(t,e){return e.x+=this.x*t,e.y+=this.y*t,e}equals(t){return t&&this.x===t.x&&this.y===t.y}equalsWithEpsilon(t,e=i.bH){return t&&(0,_.WithinEpsilon)(this.x,t.x,e)&&(0,_.WithinEpsilon)(this.y,t.y,e)}equalsToFloats(t,e){return this.x===t&&this.y===e}floor(){return new y(Math.floor(this.x),Math.floor(this.y))}floorToRef(t){return t.x=Math.floor(this.x),t.y=Math.floor(this.y),t}fract(){return new y(this.x-Math.floor(this.x),this.y-Math.floor(this.y))}fractToRef(t){return t.x=this.x-Math.floor(this.x),t.y=this.y-Math.floor(this.y),t}rotate(t){return this.rotateToRef(t,new y)}rotateToRef(t,e){let r=Math.cos(t),i=Math.sin(t);return e.x=r*this.x-i*this.y,e.y=i*this.x+r*this.y,e}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}lengthSquared(){return this.x*this.x+this.y*this.y}normalize(){return this.normalizeFromLength(this.length())}normalizeFromLength(t){return 0===t||1===t?this:this.scaleInPlace(1/t)}normalizeToNew(){let t=new y;return this.normalizeToRef(t),t}normalizeToRef(t){let e=this.length();return 0===e&&(t.x=this.x,t.y=this.y),this.scaleToRef(1/e,t)}clone(){return new y(this.x,this.y)}dot(t){return this.x*t.x+this.y*t.y}static Zero(){return new y(0,0)}static One(){return new y(1,1)}static Random(t=0,e=1){return new y((0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e))}static RandomToRef(t=0,e=1,r){return r.copyFromFloats((0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e))}static get ZeroReadOnly(){return y._ZeroReadOnly}static FromArray(t,e=0){return new y(t[e],t[e+1])}static FromArrayToRef(t,e,r){return r.x=t[e],r.y=t[e+1],r}static FromFloatsToRef(t,e,r){return r.copyFromFloats(t,e),r}static CatmullRom(t,e,r,i,s){let o=s*s,a=s*o;return new y(.5*(2*e.x+(-t.x+r.x)*s+(2*t.x-5*e.x+4*r.x-i.x)*o+(-t.x+3*e.x-3*r.x+i.x)*a),.5*(2*e.y+(-t.y+r.y)*s+(2*t.y-5*e.y+4*r.y-i.y)*o+(-t.y+3*e.y-3*r.y+i.y)*a))}static ClampToRef(t,e,r,i){return i.x=(0,_.Clamp)(t.x,e.x,r.x),i.y=(0,_.Clamp)(t.y,e.y,r.y),i}static Clamp(t,e,r){return new y((0,_.Clamp)(t.x,e.x,r.x),(0,_.Clamp)(t.y,e.y,r.y))}static Hermite(t,e,r,i,s){let o=s*s,a=s*o,n=2*a-3*o+1,_=-2*a+3*o,h=a-2*o+s,l=a-o;return new y(t.x*n+r.x*_+e.x*h+i.x*l,t.y*n+r.y*_+e.y*h+i.y*l)}static Hermite1stDerivative(t,e,r,i,s){return this.Hermite1stDerivativeToRef(t,e,r,i,s,new y)}static Hermite1stDerivativeToRef(t,e,r,i,s,o){let a=s*s;return o.x=(a-s)*6*t.x+(3*a-4*s+1)*e.x+(-a+s)*6*r.x+(3*a-2*s)*i.x,o.y=(a-s)*6*t.y+(3*a-4*s+1)*e.y+(-a+s)*6*r.y+(3*a-2*s)*i.y,o}static Lerp(t,e,r){return y.LerpToRef(t,e,r,new y)}static LerpToRef(t,e,r,i){return i.x=t.x+(e.x-t.x)*r,i.y=t.y+(e.y-t.y)*r,i}static Dot(t,e){return t.x*e.x+t.y*e.y}static Normalize(t){return y.NormalizeToRef(t,new y)}static NormalizeToRef(t,e){return t.normalizeToRef(e),e}static Minimize(t,e){return new y(t.x<e.x?t.x:e.x,t.y<e.y?t.y:e.y)}static Maximize(t,e){return new y(t.x>e.x?t.x:e.x,t.y>e.y?t.y:e.y)}static Transform(t,e){return y.TransformToRef(t,e,new y)}static TransformToRef(t,e,r){let i=e.m,s=t.x*i[0]+t.y*i[4]+i[12],o=t.x*i[1]+t.y*i[5]+i[13];return r.x=s,r.y=o,r}static PointInTriangle(t,e,r,i){let s=.5*(-r.y*i.x+e.y*(-r.x+i.x)+e.x*(r.y-i.y)+r.x*i.y),o=s<0?-1:1,a=(e.y*i.x-e.x*i.y+(i.y-e.y)*t.x+(e.x-i.x)*t.y)*o,n=(e.x*r.y-e.y*r.x+(e.y-r.y)*t.x+(r.x-e.x)*t.y)*o;return a>0&&n>0&&a+n<2*s*o}static Distance(t,e){return Math.sqrt(y.DistanceSquared(t,e))}static DistanceSquared(t,e){let r=t.x-e.x,i=t.y-e.y;return r*r+i*i}static Center(t,e){return y.CenterToRef(t,e,new y)}static CenterToRef(t,e,r){return r.copyFromFloats((t.x+e.x)/2,(t.y+e.y)/2)}static DistanceOfPointFromSegment(t,e,r){let i=y.DistanceSquared(e,r);if(0===i)return y.Distance(t,e);let s=r.subtract(e),o=Math.max(0,Math.min(1,y.Dot(t.subtract(e),s)/i)),a=e.add(s.multiplyByFloats(o,o));return y.Distance(t,a)}}y._V8PerformanceHack=new y(.5,.5),y._ZeroReadOnly=y.Zero(),Object.defineProperties(y.prototype,{dimension:{value:[2]},rank:{value:1}});class u{get x(){return this._x}set x(t){this._x=t,this._isDirty=!0}get y(){return this._y}set y(t){this._y=t,this._isDirty=!0}get z(){return this._z}set z(t){this._z=t,this._isDirty=!0}constructor(t=0,e=0,r=0){this._isDirty=!0,this._x=t,this._y=e,this._z=r}toString(){return`{X: ${this._x} Y: ${this._y} Z: ${this._z}}`}getClassName(){return"Vector3"}getHashCode(){let t=l(this._x),e=l(this._y),r=l(this._z),i=t;return 397*(i=397*i^e)^r}asArray(){return[this._x,this._y,this._z]}toArray(t,e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,this}fromArray(t,e=0){return u.FromArrayToRef(t,e,this),this}toQuaternion(){return c.RotationYawPitchRoll(this._y,this._x,this._z)}addInPlace(t){return this._x+=t._x,this._y+=t._y,this._z+=t._z,this._isDirty=!0,this}addInPlaceFromFloats(t,e,r){return this._x+=t,this._y+=e,this._z+=r,this._isDirty=!0,this}add(t){return new u(this._x+t._x,this._y+t._y,this._z+t._z)}addToRef(t,e){return e._x=this._x+t._x,e._y=this._y+t._y,e._z=this._z+t._z,e._isDirty=!0,e}subtractInPlace(t){return this._x-=t._x,this._y-=t._y,this._z-=t._z,this._isDirty=!0,this}subtract(t){return new u(this._x-t._x,this._y-t._y,this._z-t._z)}subtractToRef(t,e){return this.subtractFromFloatsToRef(t._x,t._y,t._z,e)}subtractFromFloats(t,e,r){return new u(this._x-t,this._y-e,this._z-r)}subtractFromFloatsToRef(t,e,r,i){return i._x=this._x-t,i._y=this._y-e,i._z=this._z-r,i._isDirty=!0,i}negate(){return new u(-this._x,-this._y,-this._z)}negateInPlace(){return this._x*=-1,this._y*=-1,this._z*=-1,this._isDirty=!0,this}negateToRef(t){return t._x=-1*this._x,t._y=-1*this._y,t._z=-1*this._z,t._isDirty=!0,t}scaleInPlace(t){return this._x*=t,this._y*=t,this._z*=t,this._isDirty=!0,this}scale(t){return new u(this._x*t,this._y*t,this._z*t)}scaleToRef(t,e){return e._x=this._x*t,e._y=this._y*t,e._z=this._z*t,e._isDirty=!0,e}getNormalToRef(t){let e=this.length(),r=Math.acos(this._y/e),i=Math.atan2(this._z,this._x);r>Math.PI/2?r-=Math.PI/2:r+=Math.PI/2;let s=e*Math.sin(r)*Math.cos(i),o=e*Math.cos(r),a=e*Math.sin(r)*Math.sin(i);return t.set(s,o,a),t}applyRotationQuaternionToRef(t,e){let r=this._x,i=this._y,s=this._z,o=t._x,a=t._y,n=t._z,_=t._w,h=2*(a*s-n*i),l=2*(n*r-o*s),y=2*(o*i-a*r);return e._x=r+_*h+a*y-n*l,e._y=i+_*l+n*h-o*y,e._z=s+_*y+o*l-a*h,e._isDirty=!0,e}applyRotationQuaternionInPlace(t){return this.applyRotationQuaternionToRef(t,this)}applyRotationQuaternion(t){return this.applyRotationQuaternionToRef(t,new u)}scaleAndAddToRef(t,e){return e._x+=this._x*t,e._y+=this._y*t,e._z+=this._z*t,e._isDirty=!0,e}projectOnPlane(t,e){return this.projectOnPlaneToRef(t,e,new u)}projectOnPlaneToRef(t,e,r){let i=t.normal,s=t.d,o=R.Vector3[0];this.subtractToRef(e,o),o.normalize();let a=u.Dot(o,i);if(1e-10>Math.abs(a))r.setAll(1/0);else{let t=-(u.Dot(e,i)+s)/a,n=o.scaleInPlace(t);e.addToRef(n,r)}return r}equals(t){return t&&this._x===t._x&&this._y===t._y&&this._z===t._z}equalsWithEpsilon(t,e=i.bH){return t&&(0,_.WithinEpsilon)(this._x,t._x,e)&&(0,_.WithinEpsilon)(this._y,t._y,e)&&(0,_.WithinEpsilon)(this._z,t._z,e)}equalsToFloats(t,e,r){return this._x===t&&this._y===e&&this._z===r}multiplyInPlace(t){return this._x*=t._x,this._y*=t._y,this._z*=t._z,this._isDirty=!0,this}multiply(t){return this.multiplyByFloats(t._x,t._y,t._z)}multiplyToRef(t,e){return e._x=this._x*t._x,e._y=this._y*t._y,e._z=this._z*t._z,e._isDirty=!0,e}multiplyByFloats(t,e,r){return new u(this._x*t,this._y*e,this._z*r)}divide(t){return new u(this._x/t._x,this._y/t._y,this._z/t._z)}divideToRef(t,e){return e._x=this._x/t._x,e._y=this._y/t._y,e._z=this._z/t._z,e._isDirty=!0,e}divideInPlace(t){return this._x=this._x/t._x,this._y=this._y/t._y,this._z=this._z/t._z,this._isDirty=!0,this}minimizeInPlace(t){return this.minimizeInPlaceFromFloats(t._x,t._y,t._z)}maximizeInPlace(t){return this.maximizeInPlaceFromFloats(t._x,t._y,t._z)}minimizeInPlaceFromFloats(t,e,r){return t<this._x&&(this.x=t),e<this._y&&(this.y=e),r<this._z&&(this.z=r),this}maximizeInPlaceFromFloats(t,e,r){return t>this._x&&(this.x=t),e>this._y&&(this.y=e),r>this._z&&(this.z=r),this}isNonUniformWithinEpsilon(t){let e=Math.abs(this._x),r=Math.abs(this._y);if(!(0,_.WithinEpsilon)(e,r,t))return!0;let i=Math.abs(this._z);return!((0,_.WithinEpsilon)(e,i,t)&&(0,_.WithinEpsilon)(r,i,t))}get isNonUniform(){let t=Math.abs(this._x);return t!==Math.abs(this._y)||t!==Math.abs(this._z)}floorToRef(t){return t._x=Math.floor(this._x),t._y=Math.floor(this._y),t._z=Math.floor(this._z),t._isDirty=!0,t}floor(){return new u(Math.floor(this._x),Math.floor(this._y),Math.floor(this._z))}fractToRef(t){return t._x=this._x-Math.floor(this._x),t._y=this._y-Math.floor(this._y),t._z=this._z-Math.floor(this._z),t._isDirty=!0,t}fract(){return new u(this._x-Math.floor(this._x),this._y-Math.floor(this._y),this._z-Math.floor(this._z))}length(){return Math.sqrt(this.lengthSquared())}lengthSquared(){return this._x*this._x+this._y*this._y+this._z*this._z}get hasAZeroComponent(){return this._x*this._y*this._z==0}normalize(){return this.normalizeFromLength(this.length())}reorderInPlace(t){if("xyz"===(t=t.toLowerCase()))return this;let e=R.Vector3[0].copyFrom(this);return this.x=e[t[0]],this.y=e[t[1]],this.z=e[t[2]],this}rotateByQuaternionToRef(t,e){return t.toRotationMatrix(R.Matrix[0]),u.TransformCoordinatesToRef(this,R.Matrix[0],e),e}rotateByQuaternionAroundPointToRef(t,e,r){return this.subtractToRef(e,R.Vector3[0]),R.Vector3[0].rotateByQuaternionToRef(t,R.Vector3[0]),e.addToRef(R.Vector3[0],r),r}cross(t){return u.CrossToRef(this,t,new u)}normalizeFromLength(t){return 0===t||1===t?this:this.scaleInPlace(1/t)}normalizeToNew(){return this.normalizeToRef(new u)}normalizeToRef(t){let e=this.length();return 0===e||1===e?(t._x=this._x,t._y=this._y,t._z=this._z,t._isDirty=!0,t):this.scaleToRef(1/e,t)}clone(){return new u(this._x,this._y,this._z)}copyFrom(t){return this.copyFromFloats(t._x,t._y,t._z)}copyFromFloats(t,e,r){return this._x=t,this._y=e,this._z=r,this._isDirty=!0,this}set(t,e,r){return this.copyFromFloats(t,e,r)}setAll(t){return this._x=this._y=this._z=t,this._isDirty=!0,this}static GetClipFactor(t,e,r,i){let s=u.Dot(t,r);return(s-i)/(s-u.Dot(e,r))}static GetAngleBetweenVectors(t,e,r){let i=t.normalizeToRef(R.Vector3[1]),s=e.normalizeToRef(R.Vector3[2]),o=u.Dot(i,s),a=Math.acos(o=(0,_.Clamp)(o,-1,1)),n=R.Vector3[3];return(u.CrossToRef(i,s,n),u.Dot(n,r)>0)?isNaN(a)?0:a:isNaN(a)?-Math.PI:-Math.acos(o)}static GetAngleBetweenVectorsOnPlane(t,e,r){R.Vector3[0].copyFrom(t);let i=R.Vector3[0];R.Vector3[1].copyFrom(e);let s=R.Vector3[1];R.Vector3[2].copyFrom(r);let o=R.Vector3[2],a=R.Vector3[3],n=R.Vector3[4];i.normalize(),s.normalize(),o.normalize(),u.CrossToRef(o,i,a),u.CrossToRef(a,o,n);let h=Math.atan2(u.Dot(s,a),u.Dot(s,n));return(0,_.NormalizeRadians)(h)}static PitchYawRollToMoveBetweenPointsToRef(t,e,r){let i=f.Vector3[0];return e.subtractToRef(t,i),r._y=Math.atan2(i.x,i.z)||0,r._x=Math.atan2(Math.sqrt(i.x**2+i.z**2),i.y)||0,r._z=0,r._isDirty=!0,r}static PitchYawRollToMoveBetweenPoints(t,e){let r=u.Zero();return u.PitchYawRollToMoveBetweenPointsToRef(t,e,r)}static SlerpToRef(t,e,r,s){let o,a;r=(0,_.Clamp)(r,0,1);let n=R.Vector3[0],h=R.Vector3[1];n.copyFrom(t);let l=n.length();n.normalizeFromLength(l),h.copyFrom(e);let y=h.length();h.normalizeFromLength(y);let m=u.Dot(n,h);if(m<1-i.bH){let t=Math.acos(m),e=1/Math.sin(t);o=Math.sin((1-r)*t)*e,a=Math.sin(r*t)*e}else o=1-r,a=r;return n.scaleInPlace(o),h.scaleInPlace(a),s.copyFrom(n).addInPlace(h),s.scaleInPlace((0,_.Lerp)(l,y,r)),s}static SmoothToRef(t,e,r,i,s){return u.SlerpToRef(t,e,0===i?1:r/i,s),s}static FromArray(t,e=0){return new u(t[e],t[e+1],t[e+2])}static FromFloatArray(t,e){return u.FromArray(t,e)}static FromArrayToRef(t,e,r){return r._x=t[e],r._y=t[e+1],r._z=t[e+2],r._isDirty=!0,r}static FromFloatArrayToRef(t,e,r){return u.FromArrayToRef(t,e,r)}static FromFloatsToRef(t,e,r,i){return i.copyFromFloats(t,e,r),i}static Zero(){return new u(0,0,0)}static One(){return new u(1,1,1)}static Up(){return new u(0,1,0)}static get UpReadOnly(){return u._UpReadOnly}static get DownReadOnly(){return u._DownReadOnly}static get RightReadOnly(){return u._RightReadOnly}static get LeftReadOnly(){return u._LeftReadOnly}static get LeftHandedForwardReadOnly(){return u._LeftHandedForwardReadOnly}static get RightHandedForwardReadOnly(){return u._RightHandedForwardReadOnly}static get LeftHandedBackwardReadOnly(){return u._LeftHandedBackwardReadOnly}static get RightHandedBackwardReadOnly(){return u._RightHandedBackwardReadOnly}static get ZeroReadOnly(){return u._ZeroReadOnly}static get OneReadOnly(){return u._OneReadOnly}static Down(){return new u(0,-1,0)}static Forward(t=!1){return new u(0,0,t?-1:1)}static Backward(t=!1){return new u(0,0,t?1:-1)}static Right(){return new u(1,0,0)}static Left(){return new u(-1,0,0)}static Random(t=0,e=1){return new u((0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e))}static RandomToRef(t=0,e=1,r){return r.copyFromFloats((0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e))}static TransformCoordinates(t,e){let r=u.Zero();return u.TransformCoordinatesToRef(t,e,r),r}static TransformCoordinatesToRef(t,e,r){return u.TransformCoordinatesFromFloatsToRef(t._x,t._y,t._z,e,r),r}static TransformCoordinatesFromFloatsToRef(t,e,r,i,s){let o=i.m,a=t*o[0]+e*o[4]+r*o[8]+o[12],n=t*o[1]+e*o[5]+r*o[9]+o[13],_=t*o[2]+e*o[6]+r*o[10]+o[14],h=1/(t*o[3]+e*o[7]+r*o[11]+o[15]);return s._x=a*h,s._y=n*h,s._z=_*h,s._isDirty=!0,s}static TransformNormal(t,e){let r=u.Zero();return u.TransformNormalToRef(t,e,r),r}static TransformNormalToRef(t,e,r){return this.TransformNormalFromFloatsToRef(t._x,t._y,t._z,e,r),r}static TransformNormalFromFloatsToRef(t,e,r,i,s){let o=i.m;return s._x=t*o[0]+e*o[4]+r*o[8],s._y=t*o[1]+e*o[5]+r*o[9],s._z=t*o[2]+e*o[6]+r*o[10],s._isDirty=!0,s}static CatmullRom(t,e,r,i,s){let o=s*s,a=s*o,n=.5*(2*e._x+(-t._x+r._x)*s+(2*t._x-5*e._x+4*r._x-i._x)*o+(-t._x+3*e._x-3*r._x+i._x)*a);return new u(n,.5*(2*e._y+(-t._y+r._y)*s+(2*t._y-5*e._y+4*r._y-i._y)*o+(-t._y+3*e._y-3*r._y+i._y)*a),.5*(2*e._z+(-t._z+r._z)*s+(2*t._z-5*e._z+4*r._z-i._z)*o+(-t._z+3*e._z-3*r._z+i._z)*a))}static Clamp(t,e,r){let i=new u;return u.ClampToRef(t,e,r,i),i}static ClampToRef(t,e,r,i){let s=t._x;s=(s=s>r._x?r._x:s)<e._x?e._x:s;let o=t._y;o=(o=o>r._y?r._y:o)<e._y?e._y:o;let a=t._z;return a=(a=a>r._z?r._z:a)<e._z?e._z:a,i.copyFromFloats(s,o,a),i}static CheckExtends(t,e,r){e.minimizeInPlace(t),r.maximizeInPlace(t)}static Hermite(t,e,r,i,s){let o=s*s,a=s*o,n=2*a-3*o+1,_=-2*a+3*o,h=a-2*o+s,l=a-o,y=t._x*n+r._x*_+e._x*h+i._x*l;return new u(y,t._y*n+r._y*_+e._y*h+i._y*l,t._z*n+r._z*_+e._z*h+i._z*l)}static Hermite1stDerivative(t,e,r,i,s){let o=new u;return this.Hermite1stDerivativeToRef(t,e,r,i,s,o),o}static Hermite1stDerivativeToRef(t,e,r,i,s,o){let a=s*s;return o._x=(a-s)*6*t._x+(3*a-4*s+1)*e._x+(-a+s)*6*r._x+(3*a-2*s)*i._x,o._y=(a-s)*6*t._y+(3*a-4*s+1)*e._y+(-a+s)*6*r._y+(3*a-2*s)*i._y,o._z=(a-s)*6*t._z+(3*a-4*s+1)*e._z+(-a+s)*6*r._z+(3*a-2*s)*i._z,o._isDirty=!0,o}static Lerp(t,e,r){let i=new u(0,0,0);return u.LerpToRef(t,e,r,i),i}static LerpToRef(t,e,r,i){return i._x=t._x+(e._x-t._x)*r,i._y=t._y+(e._y-t._y)*r,i._z=t._z+(e._z-t._z)*r,i._isDirty=!0,i}static Dot(t,e){return t._x*e._x+t._y*e._y+t._z*e._z}dot(t){return this._x*t._x+this._y*t._y+this._z*t._z}static Cross(t,e){let r=new u;return u.CrossToRef(t,e,r),r}static CrossToRef(t,e,r){let i=t._y*e._z-t._z*e._y,s=t._z*e._x-t._x*e._z,o=t._x*e._y-t._y*e._x;return r.copyFromFloats(i,s,o),r}static Normalize(t){let e=u.Zero();return u.NormalizeToRef(t,e),e}static NormalizeToRef(t,e){return t.normalizeToRef(e),e}static Project(t,e,r,i){let s=new u;return u.ProjectToRef(t,e,r,i,s),s}static ProjectToRef(t,e,r,i,s){let o=i.width,a=i.height,_=i.x,h=i.y,l=R.Matrix[1],y=n.q.LastCreatedEngine?.isNDCHalfZRange;x.FromValuesToRef(o/2,0,0,0,0,-a/2,0,0,0,0,y?1:.5,0,_+o/2,a/2+h,.5*!y,1,l);let m=R.Matrix[0];return e.multiplyToRef(r,m),m.multiplyToRef(l,m),u.TransformCoordinatesToRef(t,m,s),s}static Reflect(t,e){return this.ReflectToRef(t,e,new u)}static ReflectToRef(t,e,r){let i=f.Vector3[0];return i.copyFrom(e).scaleInPlace(2*u.Dot(t,e)),r.copyFrom(t).subtractInPlace(i)}static UnprojectFromTransform(t,e,r,i,s){return this.Unproject(t,e,r,i,s,x.IdentityReadOnly)}static Unproject(t,e,r,i,s,o){let a=new u;return u.UnprojectToRef(t,e,r,i,s,o,a),a}static UnprojectToRef(t,e,r,i,s,o,a){return u.UnprojectFloatsToRef(t._x,t._y,t._z,e,r,i,s,o,a),a}static UnprojectFloatsToRef(t,e,r,i,s,o,a,_,h){let l=R.Matrix[0];o.multiplyToRef(a,l),l.multiplyToRef(_,l),l.invert();let y=R.Vector3[0];return y.x=t/i*2-1,y.y=-(e/s*2-1),n.q.LastCreatedEngine?.isNDCHalfZRange?y.z=r:y.z=2*r-1,u.TransformCoordinatesToRef(y,l,h),h}static Minimize(t,e){let r=new u;return r.copyFrom(t),r.minimizeInPlace(e),r}static Maximize(t,e){let r=new u;return r.copyFrom(t),r.maximizeInPlace(e),r}static Distance(t,e){return Math.sqrt(u.DistanceSquared(t,e))}static DistanceSquared(t,e){let r=t._x-e._x,i=t._y-e._y,s=t._z-e._z;return r*r+i*i+s*s}static ProjectOnTriangleToRef(t,e,r,s,o){let a,n,h=R.Vector3[0],l=R.Vector3[1],y=R.Vector3[2],m=R.Vector3[3],c=R.Vector3[4];r.subtractToRef(e,h),s.subtractToRef(e,l),s.subtractToRef(r,y);let x=h.length(),f=l.length(),z=y.length();if(x<i.bH||f<i.bH||z<i.bH)return o.copyFrom(e),u.Distance(t,e);t.subtractToRef(e,c),u.CrossToRef(h,l,m);let d=m.length();if(d<i.bH)return o.copyFrom(e),u.Distance(t,e);m.normalizeFromLength(d);let T=c.length();if(T<i.bH)return o.copyFrom(e),0;c.normalizeFromLength(T);let w=u.Dot(m,c),p=R.Vector3[5],F=R.Vector3[6];p.copyFrom(m).scaleInPlace(-T*w),F.copyFrom(t).addInPlace(p);let M=R.Vector3[4],I=R.Vector3[5],g=R.Vector3[7],P=R.Vector3[8];M.copyFrom(h).scaleInPlace(1/x),P.copyFrom(l).scaleInPlace(1/f),M.addInPlace(P).scaleInPlace(-1),I.copyFrom(h).scaleInPlace(-1/x),P.copyFrom(y).scaleInPlace(1/z),I.addInPlace(P).scaleInPlace(-1),g.copyFrom(y).scaleInPlace(-1/z),P.copyFrom(l).scaleInPlace(-1/f),g.addInPlace(P).scaleInPlace(-1);let A=R.Vector3[9];A.copyFrom(F).subtractInPlace(e),u.CrossToRef(M,A,P);let D=u.Dot(P,m);A.copyFrom(F).subtractInPlace(r),u.CrossToRef(I,A,P);let C=u.Dot(P,m);A.copyFrom(F).subtractInPlace(s),u.CrossToRef(g,A,P);let V=u.Dot(P,m),k=R.Vector3[10];D>0&&C<0?(k.copyFrom(h),a=e,n=r):C>0&&V<0?(k.copyFrom(y),a=r,n=s):(k.copyFrom(l).scaleInPlace(-1),a=s,n=e);let H=R.Vector3[9],L=R.Vector3[4];if(a.subtractToRef(F,P),n.subtractToRef(F,H),u.CrossToRef(P,H,L),!(0>u.Dot(L,m)))return o.copyFrom(F),Math.abs(T*w);let O=R.Vector3[5];u.CrossToRef(k,L,O),O.normalize();let b=R.Vector3[9];b.copyFrom(a).subtractInPlace(F);let v=b.length();if(v<i.bH)return o.copyFrom(a),u.Distance(t,a);b.normalizeFromLength(v);let q=u.Dot(O,b),U=R.Vector3[7];U.copyFrom(F).addInPlace(O.scaleInPlace(v*q)),P.copyFrom(U).subtractInPlace(a),T=k.length(),k.normalizeFromLength(T);let S=u.Dot(P,k)/Math.max(T,i.bH);return S=(0,_.Clamp)(S,0,1),U.copyFrom(a).addInPlace(k.scaleInPlace(S*T)),o.copyFrom(U),u.Distance(t,U)}static Center(t,e){return u.CenterToRef(t,e,u.Zero())}static CenterToRef(t,e,r){return r.copyFromFloats((t._x+e._x)/2,(t._y+e._y)/2,(t._z+e._z)/2)}static RotationFromAxis(t,e,r){let i=new u;return u.RotationFromAxisToRef(t,e,r,i),i}static RotationFromAxisToRef(t,e,r,i){let s=R.Quaternion[0];return c.RotationQuaternionFromAxisToRef(t,e,r,s),s.toEulerAnglesToRef(i),i}}u._V8PerformanceHack=new u(.5,.5,.5),u._UpReadOnly=u.Up(),u._DownReadOnly=u.Down(),u._LeftHandedForwardReadOnly=u.Forward(!1),u._RightHandedForwardReadOnly=u.Forward(!0),u._LeftHandedBackwardReadOnly=u.Backward(!1),u._RightHandedBackwardReadOnly=u.Backward(!0),u._RightReadOnly=u.Right(),u._LeftReadOnly=u.Left(),u._ZeroReadOnly=u.Zero(),u._OneReadOnly=u.One(),Object.defineProperties(u.prototype,{dimension:{value:[3]},rank:{value:1}});class m{get x(){return this._x}set x(t){this._x=t,this._isDirty=!0}get y(){return this._y}set y(t){this._y=t,this._isDirty=!0}get z(){return this._z}set z(t){this._z=t,this._isDirty=!0}get w(){return this._w}set w(t){this._w=t,this._isDirty=!0}constructor(t=0,e=0,r=0,i=0){this._isDirty=!0,this._x=t,this._y=e,this._z=r,this._w=i}toString(){return`{X: ${this._x} Y: ${this._y} Z: ${this._z} W: ${this._w}}`}getClassName(){return"Vector4"}getHashCode(){let t=l(this._x),e=l(this._y),r=l(this._z),i=l(this._w),s=t;return 397*(s=397*(s=397*s^e)^r)^i}asArray(){return[this._x,this._y,this._z,this._w]}toArray(t,e){return void 0===e&&(e=0),t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._w,this}fromArray(t,e=0){return m.FromArrayToRef(t,e,this),this}addInPlace(t){return this.x+=t._x,this.y+=t._y,this.z+=t._z,this.w+=t._w,this}addInPlaceFromFloats(t,e,r,i){return this.x+=t,this.y+=e,this.z+=r,this.w+=i,this}add(t){return new m(this._x+t.x,this._y+t.y,this._z+t.z,this._w+t.w)}addToRef(t,e){return e.x=this._x+t.x,e.y=this._y+t.y,e.z=this._z+t.z,e.w=this._w+t.w,e}subtractInPlace(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this.w-=t.w,this}subtract(t){return new m(this._x-t.x,this._y-t.y,this._z-t.z,this._w-t.w)}subtractToRef(t,e){return e.x=this._x-t.x,e.y=this._y-t.y,e.z=this._z-t.z,e.w=this._w-t.w,e}subtractFromFloats(t,e,r,i){return new m(this._x-t,this._y-e,this._z-r,this._w-i)}subtractFromFloatsToRef(t,e,r,i,s){return s.x=this._x-t,s.y=this._y-e,s.z=this._z-r,s.w=this._w-i,s}negate(){return new m(-this._x,-this._y,-this._z,-this._w)}negateInPlace(){return this.x*=-1,this.y*=-1,this.z*=-1,this.w*=-1,this}negateToRef(t){return t.x=-this._x,t.y=-this._y,t.z=-this._z,t.w=-this._w,t}scaleInPlace(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this}scale(t){return new m(this._x*t,this._y*t,this._z*t,this._w*t)}scaleToRef(t,e){return e.x=this._x*t,e.y=this._y*t,e.z=this._z*t,e.w=this._w*t,e}scaleAndAddToRef(t,e){return e.x+=this._x*t,e.y+=this._y*t,e.z+=this._z*t,e.w+=this._w*t,e}equals(t){return t&&this._x===t.x&&this._y===t.y&&this._z===t.z&&this._w===t.w}equalsWithEpsilon(t,e=i.bH){return t&&(0,_.WithinEpsilon)(this._x,t.x,e)&&(0,_.WithinEpsilon)(this._y,t.y,e)&&(0,_.WithinEpsilon)(this._z,t.z,e)&&(0,_.WithinEpsilon)(this._w,t.w,e)}equalsToFloats(t,e,r,i){return this._x===t&&this._y===e&&this._z===r&&this._w===i}multiplyInPlace(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this.w*=t.w,this}multiply(t){return new m(this._x*t.x,this._y*t.y,this._z*t.z,this._w*t.w)}multiplyToRef(t,e){return e.x=this._x*t.x,e.y=this._y*t.y,e.z=this._z*t.z,e.w=this._w*t.w,e}multiplyByFloats(t,e,r,i){return new m(this._x*t,this._y*e,this._z*r,this._w*i)}divide(t){return new m(this._x/t.x,this._y/t.y,this._z/t.z,this._w/t.w)}divideToRef(t,e){return e.x=this._x/t.x,e.y=this._y/t.y,e.z=this._z/t.z,e.w=this._w/t.w,e}divideInPlace(t){return this.divideToRef(t,this)}minimizeInPlace(t){return t.x<this._x&&(this.x=t.x),t.y<this._y&&(this.y=t.y),t.z<this._z&&(this.z=t.z),t.w<this._w&&(this.w=t.w),this}maximizeInPlace(t){return t.x>this._x&&(this.x=t.x),t.y>this._y&&(this.y=t.y),t.z>this._z&&(this.z=t.z),t.w>this._w&&(this.w=t.w),this}minimizeInPlaceFromFloats(t,e,r,i){return this.x=Math.min(t,this._x),this.y=Math.min(e,this._y),this.z=Math.min(r,this._z),this.w=Math.min(i,this._w),this}maximizeInPlaceFromFloats(t,e,r,i){return this.x=Math.max(t,this._x),this.y=Math.max(e,this._y),this.z=Math.max(r,this._z),this.w=Math.max(i,this._w),this}floorToRef(t){return t.x=Math.floor(this._x),t.y=Math.floor(this._y),t.z=Math.floor(this._z),t.w=Math.floor(this._w),t}floor(){return new m(Math.floor(this._x),Math.floor(this._y),Math.floor(this._z),Math.floor(this._w))}fractToRef(t){return t.x=this._x-Math.floor(this._x),t.y=this._y-Math.floor(this._y),t.z=this._z-Math.floor(this._z),t.w=this._w-Math.floor(this._w),t}fract(){return new m(this._x-Math.floor(this._x),this._y-Math.floor(this._y),this._z-Math.floor(this._z),this._w-Math.floor(this._w))}length(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)}lengthSquared(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}normalize(){return this.normalizeFromLength(this.length())}normalizeFromLength(t){return 0===t||1===t?this:this.scaleInPlace(1/t)}normalizeToNew(){return this.normalizeToRef(new m)}normalizeToRef(t){let e=this.length();return 0===e||1===e?(t.x=this._x,t.y=this._y,t.z=this._z,t.w=this._w,t):this.scaleToRef(1/e,t)}toVector3(){return new u(this._x,this._y,this._z)}clone(){return new m(this._x,this._y,this._z,this._w)}copyFrom(t){return this.x=t.x,this.y=t.y,this.z=t.z,this.w=t.w,this}copyFromFloats(t,e,r,i){return this.x=t,this.y=e,this.z=r,this.w=i,this}set(t,e,r,i){return this.copyFromFloats(t,e,r,i)}setAll(t){return this.x=this.y=this.z=this.w=t,this}dot(t){return this._x*t.x+this._y*t.y+this._z*t.z+this._w*t.w}static FromArray(t,e){return e||(e=0),new m(t[e],t[e+1],t[e+2],t[e+3])}static FromArrayToRef(t,e,r){return r.x=t[e],r.y=t[e+1],r.z=t[e+2],r.w=t[e+3],r}static FromFloatArrayToRef(t,e,r){return m.FromArrayToRef(t,e,r),r}static FromFloatsToRef(t,e,r,i,s){return s.x=t,s.y=e,s.z=r,s.w=i,s}static Zero(){return new m(0,0,0,0)}static One(){return new m(1,1,1,1)}static Random(t=0,e=1){return new m((0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e))}static RandomToRef(t=0,e=1,r){return r.x=(0,_.RandomRange)(t,e),r.y=(0,_.RandomRange)(t,e),r.z=(0,_.RandomRange)(t,e),r.w=(0,_.RandomRange)(t,e),r}static Clamp(t,e,r){return m.ClampToRef(t,e,r,new m)}static ClampToRef(t,e,r,i){return i.x=(0,_.Clamp)(t.x,e.x,r.x),i.y=(0,_.Clamp)(t.y,e.y,r.y),i.z=(0,_.Clamp)(t.z,e.z,r.z),i.w=(0,_.Clamp)(t.w,e.w,r.w),i}static CheckExtends(t,e,r){e.minimizeInPlace(t),r.maximizeInPlace(t)}static get ZeroReadOnly(){return m._ZeroReadOnly}static Normalize(t){return m.NormalizeToRef(t,new m)}static NormalizeToRef(t,e){return t.normalizeToRef(e),e}static Minimize(t,e){let r=new m;return r.copyFrom(t),r.minimizeInPlace(e),r}static Maximize(t,e){let r=new m;return r.copyFrom(t),r.maximizeInPlace(e),r}static Distance(t,e){return Math.sqrt(m.DistanceSquared(t,e))}static DistanceSquared(t,e){let r=t.x-e.x,i=t.y-e.y,s=t.z-e.z,o=t.w-e.w;return r*r+i*i+s*s+o*o}static Center(t,e){return m.CenterToRef(t,e,new m)}static CenterToRef(t,e,r){return r.x=(t.x+e.x)/2,r.y=(t.y+e.y)/2,r.z=(t.z+e.z)/2,r.w=(t.w+e.w)/2,r}static TransformCoordinates(t,e){return m.TransformCoordinatesToRef(t,e,new m)}static TransformCoordinatesToRef(t,e,r){return m.TransformCoordinatesFromFloatsToRef(t._x,t._y,t._z,e,r),r}static TransformCoordinatesFromFloatsToRef(t,e,r,i,s){let o=i.m,a=t*o[0]+e*o[4]+r*o[8]+o[12],n=t*o[1]+e*o[5]+r*o[9]+o[13],_=t*o[2]+e*o[6]+r*o[10]+o[14],h=t*o[3]+e*o[7]+r*o[11]+o[15];return s.x=a,s.y=n,s.z=_,s.w=h,s}static TransformNormal(t,e){return m.TransformNormalToRef(t,e,new m)}static TransformNormalToRef(t,e,r){let i=e.m,s=t.x*i[0]+t.y*i[4]+t.z*i[8],o=t.x*i[1]+t.y*i[5]+t.z*i[9],a=t.x*i[2]+t.y*i[6]+t.z*i[10];return r.x=s,r.y=o,r.z=a,r.w=t.w,r}static TransformNormalFromFloatsToRef(t,e,r,i,s,o){let a=s.m;return o.x=t*a[0]+e*a[4]+r*a[8],o.y=t*a[1]+e*a[5]+r*a[9],o.z=t*a[2]+e*a[6]+r*a[10],o.w=i,o}static FromVector3(t,e=0){return new m(t._x,t._y,t._z,e)}static Dot(t,e){return t.x*e.x+t.y*e.y+t.z*e.z+t.w*e.w}}m._V8PerformanceHack=new m(.5,.5,.5,.5),m._ZeroReadOnly=m.Zero(),Object.defineProperties(m.prototype,{dimension:{value:[4]},rank:{value:1}});class c{get x(){return this._x}set x(t){this._x=t,this._isDirty=!0}get y(){return this._y}set y(t){this._y=t,this._isDirty=!0}get z(){return this._z}set z(t){this._z=t,this._isDirty=!0}get w(){return this._w}set w(t){this._w=t,this._isDirty=!0}constructor(t=0,e=0,r=0,i=1){this._isDirty=!0,this._x=t,this._y=e,this._z=r,this._w=i}toString(){return`{X: ${this._x} Y: ${this._y} Z: ${this._z} W: ${this._w}}`}getClassName(){return"Quaternion"}getHashCode(){let t=l(this._x),e=l(this._y),r=l(this._z),i=l(this._w),s=t;return 397*(s=397*(s=397*s^e)^r)^i}asArray(){return[this._x,this._y,this._z,this._w]}toArray(t,e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._w,this}fromArray(t,e=0){return c.FromArrayToRef(t,e,this)}equals(t){return t&&this._x===t._x&&this._y===t._y&&this._z===t._z&&this._w===t._w}equalsWithEpsilon(t,e=i.bH){return t&&(0,_.WithinEpsilon)(this._x,t._x,e)&&(0,_.WithinEpsilon)(this._y,t._y,e)&&(0,_.WithinEpsilon)(this._z,t._z,e)&&(0,_.WithinEpsilon)(this._w,t._w,e)}isApprox(t,e=i.bH){return t&&((0,_.WithinEpsilon)(this._x,t._x,e)&&(0,_.WithinEpsilon)(this._y,t._y,e)&&(0,_.WithinEpsilon)(this._z,t._z,e)&&(0,_.WithinEpsilon)(this._w,t._w,e)||(0,_.WithinEpsilon)(this._x,-t._x,e)&&(0,_.WithinEpsilon)(this._y,-t._y,e)&&(0,_.WithinEpsilon)(this._z,-t._z,e)&&(0,_.WithinEpsilon)(this._w,-t._w,e))}clone(){return new c(this._x,this._y,this._z,this._w)}copyFrom(t){return this._x=t._x,this._y=t._y,this._z=t._z,this._w=t._w,this._isDirty=!0,this}copyFromFloats(t,e,r,i){return this._x=t,this._y=e,this._z=r,this._w=i,this._isDirty=!0,this}set(t,e,r,i){return this.copyFromFloats(t,e,r,i)}setAll(t){return this.copyFromFloats(t,t,t,t)}add(t){return new c(this._x+t._x,this._y+t._y,this._z+t._z,this._w+t._w)}addInPlace(t){return this._x+=t._x,this._y+=t._y,this._z+=t._z,this._w+=t._w,this._isDirty=!0,this}addToRef(t,e){return e._x=this._x+t._x,e._y=this._y+t._y,e._z=this._z+t._z,e._w=this._w+t._w,e._isDirty=!0,e}addInPlaceFromFloats(t,e,r,i){return this._x+=t,this._y+=e,this._z+=r,this._w+=i,this._isDirty=!0,this}subtractToRef(t,e){return e._x=this._x-t._x,e._y=this._y-t._y,e._z=this._z-t._z,e._w=this._w-t._w,e._isDirty=!0,e}subtractFromFloats(t,e,r,i){return this.subtractFromFloatsToRef(t,e,r,i,new c)}subtractFromFloatsToRef(t,e,r,i,s){return s._x=this._x-t,s._y=this._y-e,s._z=this._z-r,s._w=this._w-i,s._isDirty=!0,s}subtract(t){return new c(this._x-t._x,this._y-t._y,this._z-t._z,this._w-t._w)}subtractInPlace(t){return this._x-=t._x,this._y-=t._y,this._z-=t._z,this._w-=t._w,this._isDirty=!0,this}scale(t){return new c(this._x*t,this._y*t,this._z*t,this._w*t)}scaleToRef(t,e){return e._x=this._x*t,e._y=this._y*t,e._z=this._z*t,e._w=this._w*t,e._isDirty=!0,e}scaleInPlace(t){return this._x*=t,this._y*=t,this._z*=t,this._w*=t,this._isDirty=!0,this}scaleAndAddToRef(t,e){return e._x+=this._x*t,e._y+=this._y*t,e._z+=this._z*t,e._w+=this._w*t,e._isDirty=!0,e}multiply(t){let e=new c(0,0,0,1);return this.multiplyToRef(t,e),e}multiplyToRef(t,e){let r=this._x*t._w+this._y*t._z-this._z*t._y+this._w*t._x,i=-this._x*t._z+this._y*t._w+this._z*t._x+this._w*t._y,s=this._x*t._y-this._y*t._x+this._z*t._w+this._w*t._z,o=-this._x*t._x-this._y*t._y-this._z*t._z+this._w*t._w;return e.copyFromFloats(r,i,s,o),e}multiplyInPlace(t){return this.multiplyToRef(t,this)}multiplyByFloats(t,e,r,i){return this._x*=t,this._y*=e,this._z*=r,this._w*=i,this._isDirty=!0,this}divide(t){throw ReferenceError("Can not divide a quaternion")}divideToRef(t,e){throw ReferenceError("Can not divide a quaternion")}divideInPlace(t){throw ReferenceError("Can not divide a quaternion")}minimizeInPlace(){throw ReferenceError("Can not minimize a quaternion")}minimizeInPlaceFromFloats(){throw ReferenceError("Can not minimize a quaternion")}maximizeInPlace(){throw ReferenceError("Can not maximize a quaternion")}maximizeInPlaceFromFloats(){throw ReferenceError("Can not maximize a quaternion")}negate(){return this.negateToRef(new c)}negateInPlace(){return this._x=-this._x,this._y=-this._y,this._z=-this._z,this._w=-this._w,this._isDirty=!0,this}negateToRef(t){return t._x=-this._x,t._y=-this._y,t._z=-this._z,t._w=-this._w,t._isDirty=!0,t}equalsToFloats(t,e,r,i){return this._x===t&&this._y===e&&this._z===r&&this._w===i}floorToRef(t){throw ReferenceError("Can not floor a quaternion")}floor(){throw ReferenceError("Can not floor a quaternion")}fractToRef(t){throw ReferenceError("Can not fract a quaternion")}fract(){throw ReferenceError("Can not fract a quaternion")}conjugateToRef(t){return t.copyFromFloats(-this._x,-this._y,-this._z,this._w),t}conjugateInPlace(){return this._x*=-1,this._y*=-1,this._z*=-1,this._isDirty=!0,this}conjugate(){return new c(-this._x,-this._y,-this._z,this._w)}invert(){let t=this.conjugate(),e=this.lengthSquared();return 0==e||1==e||t.scaleInPlace(1/e),t}invertInPlace(){this.conjugateInPlace();let t=this.lengthSquared();return 0==t||1==t||this.scaleInPlace(1/t),this}lengthSquared(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}length(){return Math.sqrt(this.lengthSquared())}normalize(){return this.normalizeFromLength(this.length())}normalizeFromLength(t){return 0===t||1===t?this:this.scaleInPlace(1/t)}normalizeToNew(){let t=new c(0,0,0,1);return this.normalizeToRef(t),t}normalizeToRef(t){let e=this.length();return 0===e||1===e?t.copyFromFloats(this._x,this._y,this._z,this._w):this.scaleToRef(1/e,t)}toEulerAngles(){let t=u.Zero();return this.toEulerAnglesToRef(t),t}toEulerAnglesToRef(t){let e=this._z,r=this._x,i=this._y,s=this._w,o=i*e-r*s;if(o<-.4999999)t._y=2*Math.atan2(i,s),t._x=Math.PI/2,t._z=0,t._isDirty=!0;else if(o>.4999999)t._y=2*Math.atan2(i,s),t._x=-Math.PI/2,t._z=0,t._isDirty=!0;else{let a=s*s,n=e*e,_=r*r,h=i*i;t._z=Math.atan2(2*(r*i+e*s),-n-_+h+a),t._x=Math.asin(-2*o),t._y=Math.atan2(2*(e*r+i*s),n-_-h+a),t._isDirty=!0}return t}toAlphaBetaGammaToRef(t){let e=this._z,r=this._x,i=this._y,s=this._w,o=2*Math.atan2(Math.sqrt(r*r+i*i),Math.sqrt(e*e+s*s)),a=2*Math.atan2(e,s),n=2*Math.atan2(i,r);return t.set((a-n)/2,o,(a+n)/2),t}toRotationMatrix(t){return x.FromQuaternionToRef(this,t),t}fromRotationMatrix(t){return c.FromRotationMatrixToRef(t,this),this}dot(t){return this._x*t._x+this._y*t._y+this._z*t._z+this._w*t._w}toAxisAngle(){let t=u.Zero(),e=this.toAxisAngleToRef(t);return{axis:t,angle:e}}toAxisAngleToRef(t){let e=0,r=Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z),i=this._w;return r>0?(e=2*Math.atan2(r,i),t.set(this._x/r,this._y/r,this._z/r)):(e=0,t.set(1,0,0)),e}static FromRotationMatrix(t){let e=new c;return c.FromRotationMatrixToRef(t,e),e}static FromRotationMatrixToRef(t,e){let r,i=t.m,s=i[0],o=i[4],a=i[8],n=i[1],_=i[5],h=i[9],l=i[2],y=i[6],u=i[10],m=s+_+u;return m>0?(e._w=.25/(r=.5/Math.sqrt(m+1)),e._x=(y-h)*r,e._y=(a-l)*r,e._z=(n-o)*r):s>_&&s>u?(e._w=(y-h)/(r=2*Math.sqrt(1+s-_-u)),e._x=.25*r,e._y=(o+n)/r,e._z=(a+l)/r):_>u?(e._w=(a-l)/(r=2*Math.sqrt(1+_-s-u)),e._x=(o+n)/r,e._y=.25*r,e._z=(h+y)/r):(e._w=(n-o)/(r=2*Math.sqrt(1+u-s-_)),e._x=(a+l)/r,e._y=(h+y)/r,e._z=.25*r),e._isDirty=!0,e}static Dot(t,e){return t._x*e._x+t._y*e._y+t._z*e._z+t._w*e._w}static AreClose(t,e,r=.1){let i=c.Dot(t,e);return 1-i*i<=r}static SmoothToRef(t,e,r,i,s){let o=0===i?1:r/i;return o=(0,_.Clamp)(o,0,1),c.SlerpToRef(t,e,o,s),s}static Zero(){return new c(0,0,0,0)}static Inverse(t){return new c(-t._x,-t._y,-t._z,t._w)}static InverseToRef(t,e){return e.set(-t._x,-t._y,-t._z,t._w),e}static Identity(){return new c(0,0,0,1)}static IsIdentity(t){return t&&0===t._x&&0===t._y&&0===t._z&&1===t._w}static RotationAxis(t,e){return c.RotationAxisToRef(t,e,new c)}static RotationAxisToRef(t,e,r){r._w=Math.cos(e/2);let i=Math.sin(e/2)/t.length();return r._x=t._x*i,r._y=t._y*i,r._z=t._z*i,r._isDirty=!0,r}static FromArray(t,e){return e||(e=0),new c(t[e],t[e+1],t[e+2],t[e+3])}static FromArrayToRef(t,e,r){return r._x=t[e],r._y=t[e+1],r._z=t[e+2],r._w=t[e+3],r._isDirty=!0,r}static FromFloatsToRef(t,e,r,i,s){return s.copyFromFloats(t,e,r,i),s}static FromEulerAngles(t,e,r){let i=new c;return c.RotationYawPitchRollToRef(e,t,r,i),i}static FromEulerAnglesToRef(t,e,r,i){return c.RotationYawPitchRollToRef(e,t,r,i),i}static FromEulerVector(t){let e=new c;return c.RotationYawPitchRollToRef(t._y,t._x,t._z,e),e}static FromEulerVectorToRef(t,e){return c.RotationYawPitchRollToRef(t._y,t._x,t._z,e),e}static FromUnitVectorsToRef(t,e,r,s=i.bH){let o=u.Dot(t,e)+1;return o<s?Math.abs(t.x)>Math.abs(t.z)?r.set(-t.y,t.x,0,0):r.set(0,-t.z,t.y,0):(u.CrossToRef(t,e,f.Vector3[0]),r.set(f.Vector3[0].x,f.Vector3[0].y,f.Vector3[0].z,o)),r.normalize()}static RotationYawPitchRoll(t,e,r){let i=new c;return c.RotationYawPitchRollToRef(t,e,r,i),i}static RotationYawPitchRollToRef(t,e,r,i){let s=.5*r,o=.5*e,a=.5*t,n=Math.sin(s),_=Math.cos(s),h=Math.sin(o),l=Math.cos(o),y=Math.sin(a),u=Math.cos(a);return i._x=u*h*_+y*l*n,i._y=y*l*_-u*h*n,i._z=u*l*n-y*h*_,i._w=u*l*_+y*h*n,i._isDirty=!0,i}static RotationAlphaBetaGamma(t,e,r){let i=new c;return c.RotationAlphaBetaGammaToRef(t,e,r,i),i}static RotationAlphaBetaGammaToRef(t,e,r,i){let s=(r+t)*.5,o=(r-t)*.5,a=.5*e;return i._x=Math.cos(o)*Math.sin(a),i._y=Math.sin(o)*Math.sin(a),i._z=Math.sin(s)*Math.cos(a),i._w=Math.cos(s)*Math.cos(a),i._isDirty=!0,i}static RotationQuaternionFromAxis(t,e,r){let i=new c(0,0,0,0);return c.RotationQuaternionFromAxisToRef(t,e,r,i),i}static RotationQuaternionFromAxisToRef(t,e,r,i){let s=R.Matrix[0];return t=t.normalizeToRef(R.Vector3[0]),e=e.normalizeToRef(R.Vector3[1]),r=r.normalizeToRef(R.Vector3[2]),x.FromXYZAxesToRef(t,e,r,s),c.FromRotationMatrixToRef(s,i),i}static FromLookDirectionLH(t,e){let r=new c;return c.FromLookDirectionLHToRef(t,e,r),r}static FromLookDirectionLHToRef(t,e,r){let i=R.Matrix[0];return x.LookDirectionLHToRef(t,e,i),c.FromRotationMatrixToRef(i,r),r}static FromLookDirectionRH(t,e){let r=new c;return c.FromLookDirectionRHToRef(t,e,r),r}static FromLookDirectionRHToRef(t,e,r){let i=R.Matrix[0];return x.LookDirectionRHToRef(t,e,i),c.FromRotationMatrixToRef(i,r)}static Slerp(t,e,r){let i=c.Identity();return c.SlerpToRef(t,e,r,i),i}static SlerpToRef(t,e,r,i){let s,o,a=t._x*e._x+t._y*e._y+t._z*e._z+t._w*e._w,n=!1;if(a<0&&(n=!0,a=-a),a>.999999)o=1-r,s=n?-r:r;else{let t=Math.acos(a),e=1/Math.sin(t);o=Math.sin((1-r)*t)*e,s=n?-Math.sin(r*t)*e:Math.sin(r*t)*e}return i._x=o*t._x+s*e._x,i._y=o*t._y+s*e._y,i._z=o*t._z+s*e._z,i._w=o*t._w+s*e._w,i._isDirty=!0,i}static Hermite(t,e,r,i,s){let o=s*s,a=s*o,n=2*a-3*o+1,_=-2*a+3*o,h=a-2*o+s,l=a-o,y=t._x*n+r._x*_+e._x*h+i._x*l,u=t._y*n+r._y*_+e._y*h+i._y*l;return new c(y,u,t._z*n+r._z*_+e._z*h+i._z*l,t._w*n+r._w*_+e._w*h+i._w*l)}static Hermite1stDerivative(t,e,r,i,s){let o=new c;return this.Hermite1stDerivativeToRef(t,e,r,i,s,o),o}static Hermite1stDerivativeToRef(t,e,r,i,s,o){let a=s*s;return o._x=(a-s)*6*t._x+(3*a-4*s+1)*e._x+(-a+s)*6*r._x+(3*a-2*s)*i._x,o._y=(a-s)*6*t._y+(3*a-4*s+1)*e._y+(-a+s)*6*r._y+(3*a-2*s)*i._y,o._z=(a-s)*6*t._z+(3*a-4*s+1)*e._z+(-a+s)*6*r._z+(3*a-2*s)*i._z,o._w=(a-s)*6*t._w+(3*a-4*s+1)*e._w+(-a+s)*6*r._w+(3*a-2*s)*i._w,o._isDirty=!0,o}static Normalize(t){let e=c.Zero();return c.NormalizeToRef(t,e),e}static NormalizeToRef(t,e){return t.normalizeToRef(e),e}static Clamp(t,e,r){let i=new c;return c.ClampToRef(t,e,r,i),i}static ClampToRef(t,e,r,i){return i.copyFromFloats((0,_.Clamp)(t.x,e.x,r.x),(0,_.Clamp)(t.y,e.y,r.y),(0,_.Clamp)(t.z,e.z,r.z),(0,_.Clamp)(t.w,e.w,r.w))}static Random(t=0,e=1){return new c((0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e))}static RandomToRef(t=0,e=1,r){return r.copyFromFloats((0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e),(0,_.RandomRange)(t,e))}static Minimize(){throw ReferenceError("Quaternion.Minimize does not make sense")}static Maximize(){throw ReferenceError("Quaternion.Maximize does not make sense")}static Distance(t,e){return Math.sqrt(c.DistanceSquared(t,e))}static DistanceSquared(t,e){let r=t.x-e.x,i=t.y-e.y,s=t.z-e.z,o=t.w-e.w;return r*r+i*i+s*s+o*o}static Center(t,e){return c.CenterToRef(t,e,c.Zero())}static CenterToRef(t,e,r){return r.copyFromFloats((t.x+e.x)/2,(t.y+e.y)/2,(t.z+e.z)/2,(t.w+e.w)/2)}}c._V8PerformanceHack=new c(.5,.5,.5,.5),Object.defineProperties(c.prototype,{dimension:{value:[4]},rank:{value:1}});class x{static get Use64Bits(){return a.I.MatrixUse64Bits}get m(){return this._m}markAsUpdated(){this.updateFlag=h.jk._UpdateFlagSeed++,this._isIdentity=!1,this._isIdentity3x2=!1,this._isIdentityDirty=!0,this._isIdentity3x2Dirty=!0}_updateIdentityStatus(t,e=!1,r=!1,i=!0){this._isIdentity=t,this._isIdentity3x2=t||r,this._isIdentityDirty=!this._isIdentity&&e,this._isIdentity3x2Dirty=!this._isIdentity3x2&&i}constructor(){this._isIdentity=!1,this._isIdentityDirty=!0,this._isIdentity3x2=!0,this._isIdentity3x2Dirty=!0,this.updateFlag=-1,a.I.MatrixTrackPrecisionChange&&a.I.MatrixTrackedMatrices.push(this),this._m=new a.I.MatrixCurrentType(16),this.markAsUpdated()}isIdentity(){if(this._isIdentityDirty){this._isIdentityDirty=!1;let t=this._m;this._isIdentity=1===t[0]&&0===t[1]&&0===t[2]&&0===t[3]&&0===t[4]&&1===t[5]&&0===t[6]&&0===t[7]&&0===t[8]&&0===t[9]&&1===t[10]&&0===t[11]&&0===t[12]&&0===t[13]&&0===t[14]&&1===t[15]}return this._isIdentity}isIdentityAs3x2(){return this._isIdentity3x2Dirty&&(this._isIdentity3x2Dirty=!1,1!==this._m[0]||1!==this._m[5]||1!==this._m[15]||0!==this._m[1]||0!==this._m[2]||0!==this._m[3]||0!==this._m[4]||0!==this._m[6]||0!==this._m[7]||0!==this._m[8]||0!==this._m[9]||0!==this._m[10]||0!==this._m[11]||0!==this._m[12]||0!==this._m[13]||0!==this._m[14]?this._isIdentity3x2=!1:this._isIdentity3x2=!0),this._isIdentity3x2}determinant(){if(!0===this._isIdentity)return 1;let t=this._m,e=t[0],r=t[1],i=t[2],s=t[3],o=t[4],a=t[5],n=t[6],_=t[7],h=t[8],l=t[9],y=t[10],u=t[11],m=t[12],c=t[13],x=t[14],R=t[15],f=y*R-x*u,z=l*R-c*u,d=l*x-c*y,T=h*R-m*u,w=h*x-y*m,p=h*c-m*l;return e*(a*f-n*z+_*d)+-(r*(o*f-n*T+_*w))+i*(o*z-a*T+_*p)+-(s*(o*d-a*w+n*p))}toString(){return`{${this.m[0]}, ${this.m[1]}, ${this.m[2]}, ${this.m[3]}
${this.m[4]}, ${this.m[5]}, ${this.m[6]}, ${this.m[7]}
${this.m[8]}, ${this.m[9]}, ${this.m[10]}, ${this.m[11]}
${this.m[12]}, ${this.m[13]}, ${this.m[14]}, ${this.m[15]}}`}toArray(t=null,e=0){if(!t)return this._m;let r=this._m;for(let i=0;i<16;i++)t[e+i]=r[i];return this}asArray(){return this._m}fromArray(t,e=0){return x.FromArrayToRef(t,e,this)}copyFromFloats(...t){return x.FromArrayToRef(t,0,this)}set(...t){let e=this._m;for(let r=0;r<16;r++)e[r]=t[r];return this.markAsUpdated(),this}setAll(t){let e=this._m;for(let r=0;r<16;r++)e[r]=t;return this.markAsUpdated(),this}invert(){return this.invertToRef(this),this}reset(){return x.FromValuesToRef(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,this),this._updateIdentityStatus(!1),this}add(t){let e=new x;return this.addToRef(t,e),e}addToRef(t,e){let r=this._m,i=e._m,s=t.m;for(let t=0;t<16;t++)i[t]=r[t]+s[t];return e.markAsUpdated(),e}addToSelf(t){let e=this._m,r=t.m;return e[0]+=r[0],e[1]+=r[1],e[2]+=r[2],e[3]+=r[3],e[4]+=r[4],e[5]+=r[5],e[6]+=r[6],e[7]+=r[7],e[8]+=r[8],e[9]+=r[9],e[10]+=r[10],e[11]+=r[11],e[12]+=r[12],e[13]+=r[13],e[14]+=r[14],e[15]+=r[15],this.markAsUpdated(),this}addInPlace(t){let e=this._m,r=t.m;for(let t=0;t<16;t++)e[t]+=r[t];return this.markAsUpdated(),this}addInPlaceFromFloats(...t){let e=this._m;for(let r=0;r<16;r++)e[r]+=t[r];return this.markAsUpdated(),this}subtract(t){let e=this._m,r=t.m;for(let t=0;t<16;t++)e[t]-=r[t];return this.markAsUpdated(),this}subtractToRef(t,e){let r=this._m,i=t.m,s=e._m;for(let t=0;t<16;t++)s[t]=r[t]-i[t];return e.markAsUpdated(),e}subtractInPlace(t){let e=this._m,r=t.m;for(let t=0;t<16;t++)e[t]-=r[t];return this.markAsUpdated(),this}subtractFromFloats(...t){return this.subtractFromFloatsToRef(...t,new x)}subtractFromFloatsToRef(...t){let e=t.pop(),r=this._m,i=e._m;for(let e=0;e<16;e++)i[e]=r[e]-t[e];return e.markAsUpdated(),e}invertToRef(t){return!0===this._isIdentity?x.IdentityToRef(t):(0,h.yj)(this,t.asArray())?t.markAsUpdated():t.copyFrom(this),t}addAtIndex(t,e){return this._m[t]+=e,this.markAsUpdated(),this}multiplyAtIndex(t,e){return this._m[t]*=e,this.markAsUpdated(),this}setTranslationFromFloats(t,e,r){return this._m[12]=t,this._m[13]=e,this._m[14]=r,this.markAsUpdated(),this}addTranslationFromFloats(t,e,r){return this._m[12]+=t,this._m[13]+=e,this._m[14]+=r,this.markAsUpdated(),this}setTranslation(t){return this.setTranslationFromFloats(t._x,t._y,t._z)}getTranslation(){return new u(this._m[12],this._m[13],this._m[14])}getTranslationToRef(t){return t.x=this._m[12],t.y=this._m[13],t.z=this._m[14],t}removeRotationAndScaling(){let t=this.m;return x.FromValuesToRef(1,0,0,0,0,1,0,0,0,0,1,0,t[12],t[13],t[14],t[15],this),this._updateIdentityStatus(0===t[12]&&0===t[13]&&0===t[14]&&1===t[15]),this}copyFrom(t){return t.copyToArray(this._m),this.updateFlag=t.updateFlag,this._updateIdentityStatus(t._isIdentity,t._isIdentityDirty,t._isIdentity3x2,t._isIdentity3x2Dirty),this}copyToArray(t,e=0){return(0,h.rl)(this,t,e),this}multiply(t){let e=new x;return this.multiplyToRef(t,e),e}multiplyInPlace(t){let e=this._m,r=t.m;for(let t=0;t<16;t++)e[t]*=r[t];return this.markAsUpdated(),this}multiplyByFloats(...t){let e=this._m;for(let r=0;r<16;r++)e[r]*=t[r];return this.markAsUpdated(),this}multiplyByFloatsToRef(...t){let e=t.pop(),r=this._m,i=e._m;for(let e=0;e<16;e++)i[e]=r[e]*t[e];return e.markAsUpdated(),e}multiplyToRef(t,e){return this._isIdentity?e.copyFrom(t):t._isIdentity?e.copyFrom(this):(this.multiplyToArray(t,e._m,0),e.markAsUpdated()),e}multiplyToArray(t,e,r){return(0,h.xO)(this,t,e,r),this}divide(t){return this.divideToRef(t,new x)}divideToRef(t,e){let r=this._m,i=t.m,s=e._m;for(let t=0;t<16;t++)s[t]=r[t]/i[t];return e.markAsUpdated(),e}divideInPlace(t){let e=this._m,r=t.m;for(let t=0;t<16;t++)e[t]/=r[t];return this.markAsUpdated(),this}minimizeInPlace(t){let e=this._m,r=t.m;for(let t=0;t<16;t++)e[t]=Math.min(e[t],r[t]);return this.markAsUpdated(),this}minimizeInPlaceFromFloats(...t){let e=this._m;for(let r=0;r<16;r++)e[r]=Math.min(e[r],t[r]);return this.markAsUpdated(),this}maximizeInPlace(t){let e=this._m,r=t.m;for(let t=0;t<16;t++)e[t]=Math.min(e[t],r[t]);return this.markAsUpdated(),this}maximizeInPlaceFromFloats(...t){let e=this._m;for(let r=0;r<16;r++)e[r]=Math.min(e[r],t[r]);return this.markAsUpdated(),this}negate(){return this.negateToRef(new x)}negateInPlace(){let t=this._m;for(let e=0;e<16;e++)t[e]=-t[e];return this.markAsUpdated(),this}negateToRef(t){let e=this._m,r=t._m;for(let t=0;t<16;t++)r[t]=-e[t];return t.markAsUpdated(),t}equals(t){if(!t)return!1;if((this._isIdentity||t._isIdentity)&&!this._isIdentityDirty&&!t._isIdentityDirty)return this._isIdentity&&t._isIdentity;let e=this.m,r=t.m;return e[0]===r[0]&&e[1]===r[1]&&e[2]===r[2]&&e[3]===r[3]&&e[4]===r[4]&&e[5]===r[5]&&e[6]===r[6]&&e[7]===r[7]&&e[8]===r[8]&&e[9]===r[9]&&e[10]===r[10]&&e[11]===r[11]&&e[12]===r[12]&&e[13]===r[13]&&e[14]===r[14]&&e[15]===r[15]}equalsWithEpsilon(t,e=0){let r=this._m,i=t.m;for(let t=0;t<16;t++)if(!(0,_.WithinEpsilon)(r[t],i[t],e))return!1;return!0}equalsToFloats(...t){let e=this._m;for(let r=0;r<16;r++)if(e[r]!=t[r])return!1;return!0}floor(){return this.floorToRef(new x)}floorToRef(t){let e=this._m,r=t._m;for(let t=0;t<16;t++)r[t]=Math.floor(e[t]);return t.markAsUpdated(),t}fract(){return this.fractToRef(new x)}fractToRef(t){let e=this._m,r=t._m;for(let t=0;t<16;t++)r[t]=e[t]-Math.floor(e[t]);return t.markAsUpdated(),t}clone(){let t=new x;return t.copyFrom(this),t}getClassName(){return"Matrix"}getHashCode(){let t=l(this._m[0]);for(let e=1;e<16;e++)t=397*t^l(this._m[e]);return t}decomposeToTransformNode(t){return t.rotationQuaternion=t.rotationQuaternion||new c,this.decompose(t.scaling,t.rotationQuaternion,t.position)}decompose(t,e,r,i,s=!0){if(this._isIdentity)return r&&r.setAll(0),t&&t.setAll(1),e&&e.copyFromFloats(0,0,0,1),!0;let o=this._m;if(r&&r.copyFromFloats(o[12],o[13],o[14]),(t=t||R.Vector3[0]).x=Math.sqrt(o[0]*o[0]+o[1]*o[1]+o[2]*o[2]),t.y=Math.sqrt(o[4]*o[4]+o[5]*o[5]+o[6]*o[6]),t.z=Math.sqrt(o[8]*o[8]+o[9]*o[9]+o[10]*o[10]),i){let e=(s?i.absoluteScaling.x:i.scaling.x)<0?-1:1,r=(s?i.absoluteScaling.y:i.scaling.y)<0?-1:1,o=(s?i.absoluteScaling.z:i.scaling.z)<0?-1:1;t.x*=e,t.y*=r,t.z*=o}else 0>=this.determinant()&&(t.y*=-1);if(0===t._x||0===t._y||0===t._z)return e&&e.copyFromFloats(0,0,0,1),!1;if(e){let r=1/t._x,i=1/t._y,s=1/t._z;x.FromValuesToRef(o[0]*r,o[1]*r,o[2]*r,0,o[4]*i,o[5]*i,o[6]*i,0,o[8]*s,o[9]*s,o[10]*s,0,0,0,0,1,R.Matrix[0]),c.FromRotationMatrixToRef(R.Matrix[0],e)}return!0}getRow(t){if(t<0||t>3)return null;let e=4*t;return new m(this._m[e+0],this._m[e+1],this._m[e+2],this._m[e+3])}getRowToRef(t,e){if(t>=0&&t<=3){let r=4*t;e.x=this._m[r+0],e.y=this._m[r+1],e.z=this._m[r+2],e.w=this._m[r+3]}return e}setRow(t,e){return this.setRowFromFloats(t,e.x,e.y,e.z,e.w)}transpose(){let t=new x;return x.TransposeToRef(this,t),t}transposeToRef(t){return x.TransposeToRef(this,t),t}setRowFromFloats(t,e,r,i,s){if(t<0||t>3)return this;let o=4*t;return this._m[o+0]=e,this._m[o+1]=r,this._m[o+2]=i,this._m[o+3]=s,this.markAsUpdated(),this}scale(t){let e=new x;return this.scaleToRef(t,e),e}scaleToRef(t,e){for(let r=0;r<16;r++)e._m[r]=this._m[r]*t;return e.markAsUpdated(),e}scaleAndAddToRef(t,e){for(let r=0;r<16;r++)e._m[r]+=this._m[r]*t;return e.markAsUpdated(),e}scaleInPlace(t){let e=this._m;for(let r=0;r<16;r++)e[r]*=t;return this.markAsUpdated(),this}toNormalMatrix(t){let e=R.Matrix[0];this.invertToRef(e),e.transposeToRef(t);let r=t._m;return x.FromValuesToRef(r[0],r[1],r[2],0,r[4],r[5],r[6],0,r[8],r[9],r[10],0,0,0,0,1,t),t}getRotationMatrix(){let t=new x;return this.getRotationMatrixToRef(t),t}getRotationMatrixToRef(t){let e=R.Vector3[0];if(!this.decompose(e))return x.IdentityToRef(t),t;let r=this._m,i=1/e._x,s=1/e._y,o=1/e._z;return x.FromValuesToRef(r[0]*i,r[1]*i,r[2]*i,0,r[4]*s,r[5]*s,r[6]*s,0,r[8]*o,r[9]*o,r[10]*o,0,0,0,0,1,t),t}toggleModelMatrixHandInPlace(){let t=this._m;return t[2]*=-1,t[6]*=-1,t[8]*=-1,t[9]*=-1,t[14]*=-1,this.markAsUpdated(),this}toggleProjectionMatrixHandInPlace(){let t=this._m;return t[8]*=-1,t[9]*=-1,t[10]*=-1,t[11]*=-1,this.markAsUpdated(),this}static FromArray(t,e=0){let r=new x;return x.FromArrayToRef(t,e,r),r}static FromArrayToRef(t,e,r){for(let i=0;i<16;i++)r._m[i]=t[i+e];return r.markAsUpdated(),r}static FromFloat32ArrayToRefScaled(t,e,r,i){return i._m[0]=t[0+e]*r,i._m[1]=t[1+e]*r,i._m[2]=t[2+e]*r,i._m[3]=t[3+e]*r,i._m[4]=t[4+e]*r,i._m[5]=t[5+e]*r,i._m[6]=t[6+e]*r,i._m[7]=t[7+e]*r,i._m[8]=t[8+e]*r,i._m[9]=t[9+e]*r,i._m[10]=t[10+e]*r,i._m[11]=t[11+e]*r,i._m[12]=t[12+e]*r,i._m[13]=t[13+e]*r,i._m[14]=t[14+e]*r,i._m[15]=t[15+e]*r,i.markAsUpdated(),i}static get IdentityReadOnly(){return x._IdentityReadOnly}static FromValuesToRef(t,e,r,i,s,o,a,n,_,h,l,y,u,m,c,x,R){let f=R._m;f[0]=t,f[1]=e,f[2]=r,f[3]=i,f[4]=s,f[5]=o,f[6]=a,f[7]=n,f[8]=_,f[9]=h,f[10]=l,f[11]=y,f[12]=u,f[13]=m,f[14]=c,f[15]=x,R.markAsUpdated()}static FromValues(t,e,r,i,s,o,a,n,_,h,l,y,u,m,c,R){let f=new x,z=f._m;return z[0]=t,z[1]=e,z[2]=r,z[3]=i,z[4]=s,z[5]=o,z[6]=a,z[7]=n,z[8]=_,z[9]=h,z[10]=l,z[11]=y,z[12]=u,z[13]=m,z[14]=c,z[15]=R,f.markAsUpdated(),f}static Compose(t,e,r){let i=new x;return x.ComposeToRef(t,e,r,i),i}static ComposeToRef(t,e,r,i){let s=i._m,o=e._x,a=e._y,n=e._z,_=e._w,h=o+o,l=a+a,y=n+n,u=o*h,m=o*l,c=o*y,x=a*l,R=a*y,f=n*y,z=_*h,d=_*l,T=_*y,w=t._x,p=t._y,F=t._z;return s[0]=(1-(x+f))*w,s[1]=(m+T)*w,s[2]=(c-d)*w,s[3]=0,s[4]=(m-T)*p,s[5]=(1-(u+f))*p,s[6]=(R+z)*p,s[7]=0,s[8]=(c+d)*F,s[9]=(R-z)*F,s[10]=(1-(u+x))*F,s[11]=0,s[12]=r._x,s[13]=r._y,s[14]=r._z,s[15]=1,i.markAsUpdated(),i}static Identity(){let t=x.FromValues(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1);return t._updateIdentityStatus(!0),t}static IdentityToRef(t){return x.FromValuesToRef(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1,t),t._updateIdentityStatus(!0),t}static Zero(){let t=x.FromValues(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);return t._updateIdentityStatus(!1),t}static RotationX(t){let e=new x;return x.RotationXToRef(t,e),e}static Invert(t){let e=new x;return t.invertToRef(e),e}static RotationXToRef(t,e){let r=Math.sin(t),i=Math.cos(t);return x.FromValuesToRef(1,0,0,0,0,i,r,0,0,-r,i,0,0,0,0,1,e),e._updateIdentityStatus(1===i&&0===r),e}static RotationY(t){let e=new x;return x.RotationYToRef(t,e),e}static RotationYToRef(t,e){let r=Math.sin(t),i=Math.cos(t);return x.FromValuesToRef(i,0,-r,0,0,1,0,0,r,0,i,0,0,0,0,1,e),e._updateIdentityStatus(1===i&&0===r),e}static RotationZ(t){let e=new x;return x.RotationZToRef(t,e),e}static RotationZToRef(t,e){let r=Math.sin(t),i=Math.cos(t);return x.FromValuesToRef(i,r,0,0,-r,i,0,0,0,0,1,0,0,0,0,1,e),e._updateIdentityStatus(1===i&&0===r),e}static RotationAxis(t,e){let r=new x;return x.RotationAxisToRef(t,e,r),r}static RotationAxisToRef(t,e,r){let i=Math.sin(-e),s=Math.cos(-e),o=1-s;t=t.normalizeToRef(R.Vector3[0]);let a=r._m;return a[0]=t._x*t._x*o+s,a[1]=t._x*t._y*o-t._z*i,a[2]=t._x*t._z*o+t._y*i,a[3]=0,a[4]=t._y*t._x*o+t._z*i,a[5]=t._y*t._y*o+s,a[6]=t._y*t._z*o-t._x*i,a[7]=0,a[8]=t._z*t._x*o-t._y*i,a[9]=t._z*t._y*o+t._x*i,a[10]=t._z*t._z*o+s,a[11]=0,a[12]=0,a[13]=0,a[14]=0,a[15]=1,r.markAsUpdated(),r}static RotationAlignToRef(t,e,r,s=!1){let o=u.Dot(e,t),a=r._m;if(o<-1+i.bH)a[0]=-1,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=s?1:-1,a[6]=0,a[7]=0,a[8]=0,a[9]=0,a[10]=s?-1:1,a[11]=0;else{let r=u.Cross(e,t),i=1/(1+o);a[0]=r._x*r._x*i+o,a[1]=r._y*r._x*i-r._z,a[2]=r._z*r._x*i+r._y,a[3]=0,a[4]=r._x*r._y*i+r._z,a[5]=r._y*r._y*i+o,a[6]=r._z*r._y*i-r._x,a[7]=0,a[8]=r._x*r._z*i-r._y,a[9]=r._y*r._z*i+r._x,a[10]=r._z*r._z*i+o,a[11]=0}return a[12]=0,a[13]=0,a[14]=0,a[15]=1,r.markAsUpdated(),r}static RotationYawPitchRoll(t,e,r){let i=new x;return x.RotationYawPitchRollToRef(t,e,r,i),i}static RotationYawPitchRollToRef(t,e,r,i){return c.RotationYawPitchRollToRef(t,e,r,R.Quaternion[0]),R.Quaternion[0].toRotationMatrix(i),i}static Scaling(t,e,r){let i=new x;return x.ScalingToRef(t,e,r,i),i}static ScalingToRef(t,e,r,i){return x.FromValuesToRef(t,0,0,0,0,e,0,0,0,0,r,0,0,0,0,1,i),i._updateIdentityStatus(1===t&&1===e&&1===r),i}static Translation(t,e,r){let i=new x;return x.TranslationToRef(t,e,r,i),i}static TranslationToRef(t,e,r,i){return x.FromValuesToRef(1,0,0,0,0,1,0,0,0,0,1,0,t,e,r,1,i),i._updateIdentityStatus(0===t&&0===e&&0===r),i}static Lerp(t,e,r){let i=new x;return x.LerpToRef(t,e,r,i),i}static LerpToRef(t,e,r,i){let s=i._m,o=t.m,a=e.m;for(let t=0;t<16;t++)s[t]=o[t]*(1-r)+a[t]*r;return i.markAsUpdated(),i}static DecomposeLerp(t,e,r){let i=new x;return x.DecomposeLerpToRef(t,e,r,i),i}static DecomposeLerpToRef(t,e,r,i){let s=R.Vector3[0],o=R.Quaternion[0],a=R.Vector3[1];t.decompose(s,o,a);let n=R.Vector3[2],_=R.Quaternion[1],h=R.Vector3[3];e.decompose(n,_,h);let l=R.Vector3[4];u.LerpToRef(s,n,r,l);let y=R.Quaternion[2];c.SlerpToRef(o,_,r,y);let m=R.Vector3[5];return u.LerpToRef(a,h,r,m),x.ComposeToRef(l,y,m,i),i}static LookAtLH(t,e,r){let i=new x;return x.LookAtLHToRef(t,e,r,i),i}static LookAtLHToRef(t,e,r,i){let s=R.Vector3[0],o=R.Vector3[1],a=R.Vector3[2];e.subtractToRef(t,a),a.normalize(),u.CrossToRef(r,a,s);let n=s.lengthSquared();0===n?s.x=1:s.normalizeFromLength(Math.sqrt(n)),u.CrossToRef(a,s,o),o.normalize();let _=-u.Dot(s,t),h=-u.Dot(o,t),l=-u.Dot(a,t);return x.FromValuesToRef(s._x,o._x,a._x,0,s._y,o._y,a._y,0,s._z,o._z,a._z,0,_,h,l,1,i),i}static LookAtRH(t,e,r){let i=new x;return x.LookAtRHToRef(t,e,r,i),i}static LookAtRHToRef(t,e,r,i){let s=R.Vector3[0],o=R.Vector3[1],a=R.Vector3[2];t.subtractToRef(e,a),a.normalize(),u.CrossToRef(r,a,s);let n=s.lengthSquared();0===n?s.x=1:s.normalizeFromLength(Math.sqrt(n)),u.CrossToRef(a,s,o),o.normalize();let _=-u.Dot(s,t),h=-u.Dot(o,t),l=-u.Dot(a,t);return x.FromValuesToRef(s._x,o._x,a._x,0,s._y,o._y,a._y,0,s._z,o._z,a._z,0,_,h,l,1,i),i}static LookDirectionLH(t,e){let r=new x;return x.LookDirectionLHToRef(t,e,r),r}static LookDirectionLHToRef(t,e,r){let i=R.Vector3[0];i.copyFrom(t),i.scaleInPlace(-1);let s=R.Vector3[1];return u.CrossToRef(e,i,s),x.FromValuesToRef(s._x,s._y,s._z,0,e._x,e._y,e._z,0,i._x,i._y,i._z,0,0,0,0,1,r),r}static LookDirectionRH(t,e){let r=new x;return x.LookDirectionRHToRef(t,e,r),r}static LookDirectionRHToRef(t,e,r){let i=R.Vector3[2];return u.CrossToRef(e,t,i),x.FromValuesToRef(i._x,i._y,i._z,0,e._x,e._y,e._z,0,t._x,t._y,t._z,0,0,0,0,1,r),r}static OrthoLH(t,e,r,i,s){let o=new x;return x.OrthoLHToRef(t,e,r,i,o,s),o}static OrthoLHToRef(t,e,r,i,s,o){let a=2/t,n=2/e,_=2/(i-r),h=-(i+r)/(i-r);return x.FromValuesToRef(a,0,0,0,0,n,0,0,0,0,_,0,0,0,h,1,s),o&&s.multiplyToRef(z,s),s._updateIdentityStatus(1===a&&1===n&&1===_&&0===h),s}static OrthoOffCenterLH(t,e,r,i,s,o,a){let n=new x;return x.OrthoOffCenterLHToRef(t,e,r,i,s,o,n,a),n}static OrthoOffCenterLHToRef(t,e,r,i,s,o,a,n){return x.FromValuesToRef(2/(e-t),0,0,0,0,2/(i-r),0,0,0,0,2/(o-s),0,(t+e)/(t-e),(i+r)/(r-i),-(o+s)/(o-s),1,a),n&&a.multiplyToRef(z,a),a.markAsUpdated(),a}static ObliqueOffCenterLHToRef(t,e,r,i,s,o,a,n,_,h,l){let y=-a*Math.cos(n),u=-a*Math.sin(n);return x.TranslationToRef(0,0,-_,R.Matrix[1]),x.FromValuesToRef(1,0,0,0,0,1,0,0,y,u,1,0,0,0,0,1,R.Matrix[0]),R.Matrix[1].multiplyToRef(R.Matrix[0],R.Matrix[0]),x.TranslationToRef(0,0,_,R.Matrix[1]),R.Matrix[0].multiplyToRef(R.Matrix[1],R.Matrix[0]),x.OrthoOffCenterLHToRef(t,e,r,i,s,o,h,l),R.Matrix[0].multiplyToRef(h,h),h}static OrthoOffCenterRH(t,e,r,i,s,o,a){let n=new x;return x.OrthoOffCenterRHToRef(t,e,r,i,s,o,n,a),n}static OrthoOffCenterRHToRef(t,e,r,i,s,o,a,n){return x.OrthoOffCenterLHToRef(t,e,r,i,s,o,a,n),a._m[10]*=-1,a}static ObliqueOffCenterRHToRef(t,e,r,i,s,o,a,n,_,h,l){let y=a*Math.cos(n),u=a*Math.sin(n);return x.TranslationToRef(0,0,_,R.Matrix[1]),x.FromValuesToRef(1,0,0,0,0,1,0,0,y,u,1,0,0,0,0,1,R.Matrix[0]),R.Matrix[1].multiplyToRef(R.Matrix[0],R.Matrix[0]),x.TranslationToRef(0,0,-_,R.Matrix[1]),R.Matrix[0].multiplyToRef(R.Matrix[1],R.Matrix[0]),x.OrthoOffCenterRHToRef(t,e,r,i,s,o,h,l),R.Matrix[0].multiplyToRef(h,h),h}static PerspectiveLH(t,e,r,i,s,o=0){let a=new x,n=Math.tan(o);return x.FromValuesToRef(2*r/t,0,0,0,0,2*r/e,0,n,0,0,(i+r)/(i-r),1,0,0,-2*i*r/(i-r),0,a),s&&a.multiplyToRef(z,a),a._updateIdentityStatus(!1),a}static PerspectiveFovLH(t,e,r,i,s,o=0,a=!1){let n=new x;return x.PerspectiveFovLHToRef(t,e,r,i,n,!0,s,o,a),n}static PerspectiveFovLHToRef(t,e,r,i,s,o=!0,a,n=0,_=!1){let h=1/Math.tan(.5*t),l=o?h/e:h,y=o?h:h*e,u=_&&0===r?-1:0!==i?(i+r)/(i-r):1,m=_&&0===r?2*i:0!==i?-2*i*r/(i-r):-2*r,c=Math.tan(n);return x.FromValuesToRef(l,0,0,0,0,y,0,c,0,0,u,1,0,0,m,0,s),a&&s.multiplyToRef(z,s),s._updateIdentityStatus(!1),s}static PerspectiveFovReverseLHToRef(t,e,r,i,s,o=!0,a,n=0){let _=1/Math.tan(.5*t),h=o?_/e:_,l=o?_:_*e,y=Math.tan(n);return x.FromValuesToRef(h,0,0,0,0,l,0,y,0,0,-r,1,0,0,1,0,s),a&&s.multiplyToRef(z,s),s._updateIdentityStatus(!1),s}static PerspectiveFovRH(t,e,r,i,s,o=0,a=!1){let n=new x;return x.PerspectiveFovRHToRef(t,e,r,i,n,!0,s,o,a),n}static PerspectiveFovRHToRef(t,e,r,i,s,o=!0,a,n=0,_=!1){let h=1/Math.tan(.5*t),l=o?h/e:h,y=o?h:h*e,u=_&&0===r?1:0!==i?-(i+r)/(i-r):-1,m=_&&0===r?2*i:0!==i?-2*i*r/(i-r):-2*r,c=Math.tan(n);return x.FromValuesToRef(l,0,0,0,0,y,0,c,0,0,u,-1,0,0,m,0,s),a&&s.multiplyToRef(z,s),s._updateIdentityStatus(!1),s}static PerspectiveFovReverseRHToRef(t,e,r,i,s,o=!0,a,n=0){let _=1/Math.tan(.5*t),h=o?_/e:_,l=o?_:_*e,y=Math.tan(n);return x.FromValuesToRef(h,0,0,0,0,l,0,y,0,0,-r,-1,0,0,-1,0,s),a&&s.multiplyToRef(z,s),s._updateIdentityStatus(!1),s}static GetFinalMatrix(t,e,r,i,s,o){let a=t.width,n=t.height,_=t.x,h=t.y,l=x.FromValues(a/2,0,0,0,0,-n/2,0,0,0,0,o-s,0,_+a/2,n/2+h,s,1),y=new x;return e.multiplyToRef(r,y),y.multiplyToRef(i,y),y.multiplyToRef(l,y)}static GetAsMatrix2x2(t){let e=t.m,r=[e[0],e[1],e[4],e[5]];return a.I.MatrixUse64Bits?r:new Float32Array(r)}static GetAsMatrix3x3(t){let e=t.m,r=[e[0],e[1],e[2],e[4],e[5],e[6],e[8],e[9],e[10]];return a.I.MatrixUse64Bits?r:new Float32Array(r)}static Transpose(t){let e=new x;return x.TransposeToRef(t,e),e}static TransposeToRef(t,e){let r=t.m,i=r[0],s=r[4],o=r[8],a=r[12],n=r[1],_=r[5],h=r[9],l=r[13],y=r[2],u=r[6],m=r[10],c=r[14],x=r[3],R=r[7],f=r[11],z=r[15],d=e._m;return d[0]=i,d[1]=s,d[2]=o,d[3]=a,d[4]=n,d[5]=_,d[6]=h,d[7]=l,d[8]=y,d[9]=u,d[10]=m,d[11]=c,d[12]=x,d[13]=R,d[14]=f,d[15]=z,e.markAsUpdated(),e._updateIdentityStatus(t._isIdentity,t._isIdentityDirty),e}static Reflection(t){let e=new x;return x.ReflectionToRef(t,e),e}static ReflectionToRef(t,e){t.normalize();let r=t.normal.x,i=t.normal.y,s=t.normal.z,o=-2*r,a=-2*i,n=-2*s;return x.FromValuesToRef(o*r+1,a*r,n*r,0,o*i,a*i+1,n*i,0,o*s,a*s,n*s+1,0,o*t.d,a*t.d,n*t.d,1,e),e}static FromXYZAxesToRef(t,e,r,i){return x.FromValuesToRef(t._x,t._y,t._z,0,e._x,e._y,e._z,0,r._x,r._y,r._z,0,0,0,0,1,i),i}static FromQuaternionToRef(t,e){let r=t._x*t._x,i=t._y*t._y,s=t._z*t._z,o=t._x*t._y,a=t._z*t._w,n=t._z*t._x,_=t._y*t._w,h=t._y*t._z,l=t._x*t._w;return e._m[0]=1-2*(i+s),e._m[1]=2*(o+a),e._m[2]=2*(n-_),e._m[3]=0,e._m[4]=2*(o-a),e._m[5]=1-2*(s+r),e._m[6]=2*(h+l),e._m[7]=0,e._m[8]=2*(n+_),e._m[9]=2*(h-l),e._m[10]=1-2*(i+r),e._m[11]=0,e._m[12]=0,e._m[13]=0,e._m[14]=0,e._m[15]=1,e.markAsUpdated(),e}}x._IdentityReadOnly=x.Identity(),Object.defineProperties(x.prototype,{dimension:{value:[4,4]},rank:{value:2}});class R{}R.Vector3=(0,s.ln)(11,u.Zero),R.Matrix=(0,s.ln)(2,x.Identity),R.Quaternion=(0,s.ln)(3,c.Zero);class f{}f.Vector2=(0,s.ln)(3,y.Zero),f.Vector3=(0,s.ln)(13,u.Zero),f.Vector4=(0,s.ln)(3,m.Zero),f.Quaternion=(0,s.ln)(3,c.Zero),f.Matrix=(0,s.ln)(8,x.Identity),(0,o.Y5)("BABYLON.Vector2",y),(0,o.Y5)("BABYLON.Vector3",u),(0,o.Y5)("BABYLON.Vector4",m),(0,o.Y5)("BABYLON.Matrix",x);let z=x.FromValues(1,0,0,0,0,1,0,0,0,0,.5,0,0,0,.5,1)}}]);