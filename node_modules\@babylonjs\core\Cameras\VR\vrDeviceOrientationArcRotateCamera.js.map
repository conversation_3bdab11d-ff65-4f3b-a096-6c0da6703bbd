{"version": 3, "file": "vrDeviceOrientationArcRotateCamera.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/VR/vrDeviceOrientationArcRotateCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAEtD,OAAO,mDAAmD,CAAC;AAE3D,IAAI,CAAC,kBAAkB,CAAC,oCAAoC,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;IAC1E,OAAO,GAAG,EAAE,CAAC,IAAI,kCAAkC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;AAChG,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,kCAAmC,SAAQ,eAAe;IACnE;;;;;;;;;;OAUG;IACH,YACI,IAAY,EACZ,KAAa,EACb,IAAY,EACZ,MAAc,EACd,MAAe,EACf,KAAa,EACb,oBAAoB,GAAG,IAAI,EAC3B,kBAAmC,eAAe,CAAC,UAAU,EAAE;QAE/D,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAgBjC,gBAAW,GAAG,CAAC,SAAc,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAdhF,eAAe,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QAC5D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,eAAe,EAAE,eAAe,EAAE,CAAC,CAAC;QAEhF,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;IACzC,CAAC;IAED;;;OAGG;IACa,YAAY;QACxB,OAAO,oCAAoC,CAAC;IAChD,CAAC;CAGJ", "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport { VRCameraMetrics } from \"./vrCameraMetrics\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Node } from \"../../node\";\r\nimport { _SetVrRigMode } from \"../RigModes/vrRigMode\";\r\n\r\nimport \"../Inputs/arcRotateCameraVRDeviceOrientationInput\";\r\n\r\nNode.AddNodeConstructor(\"VRDeviceOrientationArcRotateCamera\", (name, scene) => {\r\n    return () => new VRDeviceOrientationArcRotateCamera(name, 0, 0, 1.0, Vector3.Zero(), scene);\r\n});\r\n\r\n/**\r\n * Camera used to simulate VR rendering (based on ArcRotateCamera)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#vr-device-orientation-cameras\r\n */\r\nexport class VRDeviceOrientationArcRotateCamera extends ArcRotateCamera {\r\n    /**\r\n     * Creates a new VRDeviceOrientationArcRotateCamera\r\n     * @param name defines camera name\r\n     * @param alpha defines the camera rotation along the longitudinal axis\r\n     * @param beta defines the camera rotation along the latitudinal axis\r\n     * @param radius defines the camera distance from its target\r\n     * @param target defines the camera target\r\n     * @param scene defines the scene the camera belongs to\r\n     * @param compensateDistortion defines if the camera needs to compensate the lens distortion\r\n     * @param vrCameraMetrics defines the vr metrics associated to the camera\r\n     */\r\n    constructor(\r\n        name: string,\r\n        alpha: number,\r\n        beta: number,\r\n        radius: number,\r\n        target: Vector3,\r\n        scene?: Scene,\r\n        compensateDistortion = true,\r\n        vrCameraMetrics: VRCameraMetrics = VRCameraMetrics.GetDefault()\r\n    ) {\r\n        super(name, alpha, beta, radius, target, scene);\r\n\r\n        vrCameraMetrics.compensateDistortion = compensateDistortion;\r\n        this.setCameraRigMode(Camera.RIG_MODE_VR, { vrCameraMetrics: vrCameraMetrics });\r\n\r\n        this.inputs.addVRDeviceOrientation();\r\n    }\r\n\r\n    /**\r\n     * Gets camera class name\r\n     * @returns VRDeviceOrientationArcRotateCamera\r\n     */\r\n    public override getClassName(): string {\r\n        return \"VRDeviceOrientationArcRotateCamera\";\r\n    }\r\n\r\n    protected override _setRigMode = (rigParams: any) => _SetVrRigMode(this, rigParams);\r\n}\r\n"]}