{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/behavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/behavior.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\n\r\n/**\r\n * Interface used to define a behavior\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface Behavior<T> {\r\n    /** gets or sets behavior's name */\r\n    name: string;\r\n\r\n    /**\r\n     * Function called when the behavior needs to be initialized (before attaching it to a target)\r\n     */\r\n    init(): void;\r\n    /**\r\n     * Called when the behavior is attached to a target\r\n     * @param target defines the target where the behavior is attached to\r\n     */\r\n    attach(target: T): void;\r\n    /**\r\n     * Called when the behavior is detached from its target\r\n     */\r\n    detach(): void;\r\n}\r\n\r\n/**\r\n * Interface implemented by classes supporting behaviors\r\n */\r\nexport interface IBehaviorAware<T> {\r\n    /**\r\n     * Attach a behavior\r\n     * @param behavior defines the behavior to attach\r\n     * @returns the current host\r\n     */\r\n    addBehavior(behavior: Behavior<T>): T;\r\n    /**\r\n     * Remove a behavior from the current object\r\n     * @param behavior defines the behavior to detach\r\n     * @returns the current host\r\n     */\r\n    removeBehavior(behavior: Behavior<T>): T;\r\n    /**\r\n     * Gets a behavior using its name to search\r\n     * @param name defines the name to search\r\n     * @returns the behavior or null if not found\r\n     */\r\n    getBehaviorByName(name: string): Nullable<Behavior<T>>;\r\n}\r\n"], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Cameras/autoRotationBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Cameras/autoRotationBehavior.ts"], "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { PointerInfoPre } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { PrecisionDate } from \"../../Misc/precisionDate\";\r\nimport { Epsilon } from \"../../Maths/math.constants\";\r\n\r\n/**\r\n * The autoRotation behavior (AutoRotationBehavior) is designed to create a smooth rotation of an ArcRotateCamera when there is no user interaction.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#autorotation-behavior\r\n */\r\nexport class AutoRotationBehavior implements Behavior<ArcRotateCamera> {\r\n    /**\r\n     * Gets the name of the behavior.\r\n     */\r\n    public get name(): string {\r\n        return \"AutoRotation\";\r\n    }\r\n\r\n    private _zoomStopsAnimation = false;\r\n    private _idleRotationSpeed = 0.05;\r\n    private _idleRotationWaitTime = 2000;\r\n    private _idleRotationSpinupTime = 2000;\r\n\r\n    /**\r\n     * Target alpha\r\n     */\r\n    public targetAlpha: Nullable<number> = null;\r\n\r\n    /**\r\n     * Sets the flag that indicates if user zooming should stop animation.\r\n     */\r\n    public set zoomStopsAnimation(flag: boolean) {\r\n        this._zoomStopsAnimation = flag;\r\n    }\r\n\r\n    /**\r\n     * Gets the flag that indicates if user zooming should stop animation.\r\n     */\r\n    public get zoomStopsAnimation(): boolean {\r\n        return this._zoomStopsAnimation;\r\n    }\r\n\r\n    /**\r\n     * Sets the default speed at which the camera rotates around the model.\r\n     */\r\n    public set idleRotationSpeed(speed: number) {\r\n        this._idleRotationSpeed = speed;\r\n    }\r\n\r\n    /**\r\n     * Gets the default speed at which the camera rotates around the model.\r\n     */\r\n    public get idleRotationSpeed() {\r\n        return this._idleRotationSpeed;\r\n    }\r\n\r\n    /**\r\n     * Sets the time (in milliseconds) to wait after user interaction before the camera starts rotating.\r\n     */\r\n    public set idleRotationWaitTime(time: number) {\r\n        this._idleRotationWaitTime = time;\r\n    }\r\n\r\n    /**\r\n     * Gets the time (milliseconds) to wait after user interaction before the camera starts rotating.\r\n     */\r\n    public get idleRotationWaitTime() {\r\n        return this._idleRotationWaitTime;\r\n    }\r\n\r\n    /**\r\n     * Sets the time (milliseconds) to take to spin up to the full idle rotation speed.\r\n     */\r\n    public set idleRotationSpinupTime(time: number) {\r\n        this._idleRotationSpinupTime = time;\r\n    }\r\n\r\n    /**\r\n     * Gets the time (milliseconds) to take to spin up to the full idle rotation speed.\r\n     */\r\n    public get idleRotationSpinupTime() {\r\n        return this._idleRotationSpinupTime;\r\n    }\r\n\r\n    /**\r\n     * Gets a value indicating if the camera is currently rotating because of this behavior\r\n     */\r\n    public get rotationInProgress(): boolean {\r\n        return Math.abs(this._cameraRotationSpeed) > 0;\r\n    }\r\n\r\n    // Default behavior functions\r\n    private _onPrePointerObservableObserver: Nullable<Observer<PointerInfoPre>>;\r\n    private _onAfterCheckInputsObserver: Nullable<Observer<Camera>>;\r\n    private _attachedCamera: Nullable<ArcRotateCamera>;\r\n    private _isPointerDown = false;\r\n    private _lastFrameTime: Nullable<number> = null;\r\n    private _lastInteractionTime = -Infinity;\r\n    private _cameraRotationSpeed: number = 0;\r\n\r\n    /**\r\n     * Initializes the behavior.\r\n     */\r\n    public init(): void {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Attaches the behavior to its arc rotate camera.\r\n     * @param camera Defines the camera to attach the behavior to\r\n     */\r\n    public attach(camera: ArcRotateCamera): void {\r\n        this._attachedCamera = camera;\r\n        const scene = this._attachedCamera.getScene();\r\n\r\n        this._onPrePointerObservableObserver = scene.onPrePointerObservable.add((pointerInfoPre) => {\r\n            if (pointerInfoPre.type === PointerEventTypes.POINTERDOWN) {\r\n                this._isPointerDown = true;\r\n                return;\r\n            }\r\n\r\n            if (pointerInfoPre.type === PointerEventTypes.POINTERUP) {\r\n                this._isPointerDown = false;\r\n            }\r\n        });\r\n\r\n        this._onAfterCheckInputsObserver = camera.onAfterCheckInputsObservable.add(() => {\r\n            if (this._reachTargetAlpha()) {\r\n                return;\r\n            }\r\n            const now = PrecisionDate.Now;\r\n            let dt = 0;\r\n            if (this._lastFrameTime != null) {\r\n                dt = now - this._lastFrameTime;\r\n            }\r\n            this._lastFrameTime = now;\r\n\r\n            // Stop the animation if there is user interaction and the animation should stop for this interaction\r\n            this._applyUserInteraction();\r\n\r\n            const timeToRotation = now - this._lastInteractionTime - this._idleRotationWaitTime;\r\n            const scale = Math.max(Math.min(timeToRotation / this._idleRotationSpinupTime, 1), 0);\r\n            this._cameraRotationSpeed = this._idleRotationSpeed * scale;\r\n\r\n            // Step camera rotation by rotation speed\r\n            if (this._attachedCamera) {\r\n                this._attachedCamera.alpha -= this._cameraRotationSpeed * (dt / 1000);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from its current arc rotate camera.\r\n     */\r\n    public detach(): void {\r\n        if (!this._attachedCamera) {\r\n            return;\r\n        }\r\n        const scene = this._attachedCamera.getScene();\r\n\r\n        if (this._onPrePointerObservableObserver) {\r\n            scene.onPrePointerObservable.remove(this._onPrePointerObservableObserver);\r\n        }\r\n\r\n        this._attachedCamera.onAfterCheckInputsObservable.remove(this._onAfterCheckInputsObserver);\r\n        this._attachedCamera = null;\r\n        this._lastFrameTime = null;\r\n    }\r\n\r\n    /**\r\n     * Force-reset the last interaction time\r\n     * @param customTime an optional time that will be used instead of the current last interaction time. For example `Date.now()`\r\n     */\r\n    public resetLastInteractionTime(customTime?: number): void {\r\n        this._lastInteractionTime = customTime ?? PrecisionDate.Now;\r\n    }\r\n\r\n    /**\r\n     * Returns true if camera alpha reaches the target alpha\r\n     * @returns true if camera alpha reaches the target alpha\r\n     */\r\n    private _reachTargetAlpha(): boolean {\r\n        if (this._attachedCamera && this.targetAlpha) {\r\n            return Math.abs(this._attachedCamera.alpha - this.targetAlpha) < Epsilon;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Returns true if user is scrolling.\r\n     * @returns true if user is scrolling.\r\n     */\r\n    private _userIsZooming(): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n        return this._attachedCamera.inertialRadiusOffset !== 0;\r\n    }\r\n\r\n    private _lastFrameRadius = 0;\r\n    private _shouldAnimationStopForInteraction(): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n\r\n        let zoomHasHitLimit = false;\r\n        if (this._lastFrameRadius === this._attachedCamera.radius && this._attachedCamera.inertialRadiusOffset !== 0) {\r\n            zoomHasHitLimit = true;\r\n        }\r\n\r\n        // Update the record of previous radius - works as an approx. indicator of hitting radius limits\r\n        this._lastFrameRadius = this._attachedCamera.radius;\r\n        return this._zoomStopsAnimation ? zoomHasHitLimit : this._userIsZooming();\r\n    }\r\n\r\n    /**\r\n     *  Applies any current user interaction to the camera. Takes into account maximum alpha rotation.\r\n     */\r\n    private _applyUserInteraction(): void {\r\n        if (this._userIsMoving() && !this._shouldAnimationStopForInteraction()) {\r\n            this._lastInteractionTime = PrecisionDate.Now;\r\n        }\r\n    }\r\n\r\n    // Tools\r\n    private _userIsMoving(): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            this._attachedCamera.inertialAlphaOffset !== 0 ||\r\n            this._attachedCamera.inertialBetaOffset !== 0 ||\r\n            this._attachedCamera.inertialRadiusOffset !== 0 ||\r\n            this._attachedCamera.inertialPanningX !== 0 ||\r\n            this._attachedCamera.inertialPanningY !== 0 ||\r\n            this._isPointerDown\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAMA,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACzD,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;;;;AAM/C,MAAO,oBAAoB;IAAjC,aAAA;QAQY,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,kBAAkB,GAAG,IAAI,CAAC;QAC1B,IAAA,CAAA,qBAAqB,GAAG,IAAI,CAAC;QAC7B,IAAA,CAAA,uBAAuB,GAAG,IAAI,CAAC;QAEvC;;WAEG,CACI,IAAA,CAAA,WAAW,GAAqB,IAAI,CAAC;QAqEpC,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,cAAc,GAAqB,IAAI,CAAC;QACxC,IAAA,CAAA,oBAAoB,GAAG,CAAC,QAAQ,CAAC;QACjC,IAAA,CAAA,oBAAoB,GAAW,CAAC,CAAC;QAqGjC,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;IAwCjC,CAAC;IApOG;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,cAAc,CAAC;IAC1B,CAAC;IAYD;;OAEG,CACH,IAAW,kBAAkB,CAAC,IAAa,EAAA;QACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,iBAAiB,CAAC,KAAa,EAAA;QACtC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,CAAC,IAAY,EAAA;QACxC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED;;OAEG,CACH,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,CAAC,IAAY,EAAA;QAC1C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED;;OAEG,CACH,IAAW,sBAAsB,GAAA;QAC7B,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACnD,CAAC;IAWD;;OAEG,CACI,IAAI,GAAA;IACP,aAAa;IACjB,CAAC;IAED;;;OAGG,CACI,MAAM,CAAC,MAAuB,EAAA;QACjC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9C,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE;YACvF,IAAI,cAAc,CAAC,IAAI,oKAAK,oBAAiB,CAAC,WAAW,EAAE,CAAC;gBACxD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,OAAO;YACX,CAAC;YAED,IAAI,cAAc,CAAC,IAAI,oKAAK,oBAAiB,CAAC,SAAS,EAAE,CAAC;gBACtD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAChC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,2BAA2B,GAAG,MAAM,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5E,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC;gBAC3B,OAAO;YACX,CAAC;YACD,MAAM,GAAG,gKAAG,gBAAa,CAAC,GAAG,CAAC;YAC9B,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;gBAC9B,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC;YACnC,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;YAE1B,qGAAqG;YACrG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAE7B,MAAM,cAAc,GAAG,GAAG,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACpF,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtF,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YAE5D,yCAAyC;YACzC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;YAC1E,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,+BAA+B,EAAE,CAAC;YACvC,KAAK,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC3F,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED;;;OAGG,CACI,wBAAwB,CAAC,UAAmB,EAAA;QAC/C,IAAI,CAAC,oBAAoB,GAAG,UAAU,iKAAI,gBAAa,CAAC,GAAG,CAAC;IAChE,CAAC;IAED;;;OAGG,CACK,iBAAiB,GAAA;QACrB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,qKAAG,UAAO,CAAC;QAC7E,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACK,cAAc,GAAA;QAClB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC,oBAAoB,KAAK,CAAC,CAAC;IAC3D,CAAC;IAGO,kCAAkC,GAAA;QACtC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,eAAe,GAAG,KAAK,CAAC;QAC5B,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,oBAAoB,KAAK,CAAC,EAAE,CAAC;YAC3G,eAAe,GAAG,IAAI,CAAC;QAC3B,CAAC;QAED,gGAAgG;QAChG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QACpD,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;IAC9E,CAAC;IAED;;OAEG,CACK,qBAAqB,GAAA;QACzB,IAAI,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,CAAC;YACrE,IAAI,CAAC,oBAAoB,gKAAG,gBAAa,CAAC,GAAG,CAAC;QAClD,CAAC;IACL,CAAC;IAED,QAAQ;IACA,aAAa,GAAA;QACjB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,AACH,IAAI,CAAC,eAAe,CAAC,mBAAmB,KAAK,CAAC,IAC9C,IAAI,CAAC,eAAe,CAAC,kBAAkB,KAAK,CAAC,IAC7C,IAAI,CAAC,eAAe,CAAC,oBAAoB,KAAK,CAAC,IAC/C,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC,IAC3C,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC,IAC3C,IAAI,CAAC,cAAc,CACtB,CAAC;IACN,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Cameras/bouncingBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Cameras/bouncingBehavior.ts"], "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport { BackEase, EasingFunction } from \"../../Animations/easing\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { Animatable } from \"../../Animations/animatable.core\";\r\nimport { Animation } from \"../../Animations/animation\";\r\n\r\n/**\r\n * Add a bouncing effect to an ArcRotateCamera when reaching a specified minimum and maximum radius\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#bouncing-behavior\r\n */\r\nexport class BouncingBehavior implements Behavior<ArcRotateCamera> {\r\n    /**\r\n     * Gets the name of the behavior.\r\n     */\r\n    public get name(): string {\r\n        return \"Bouncing\";\r\n    }\r\n\r\n    /**\r\n     * The easing function used by animations\r\n     */\r\n    public static EasingFunction = new BackEase(0.3);\r\n\r\n    /**\r\n     * The easing mode used by animations\r\n     */\r\n    public static EasingMode = EasingFunction.EASINGMODE_EASEOUT;\r\n\r\n    /**\r\n     * The duration of the animation, in milliseconds\r\n     */\r\n    public transitionDuration = 450;\r\n\r\n    /**\r\n     * Length of the distance animated by the transition when lower radius is reached\r\n     */\r\n    public lowerRadiusTransitionRange = 2;\r\n\r\n    /**\r\n     * Length of the distance animated by the transition when upper radius is reached\r\n     */\r\n    public upperRadiusTransitionRange = -2;\r\n\r\n    private _autoTransitionRange = false;\r\n\r\n    /**\r\n     * Gets a value indicating if the lowerRadiusTransitionRange and upperRadiusTransitionRange are defined automatically\r\n     */\r\n    public get autoTransitionRange(): boolean {\r\n        return this._autoTransitionRange;\r\n    }\r\n\r\n    /**\r\n     * Sets a value indicating if the lowerRadiusTransitionRange and upperRadiusTransitionRange are defined automatically\r\n     * Transition ranges will be set to 5% of the bounding box diagonal in world space\r\n     */\r\n    public set autoTransitionRange(value: boolean) {\r\n        if (this._autoTransitionRange === value) {\r\n            return;\r\n        }\r\n\r\n        this._autoTransitionRange = value;\r\n\r\n        const camera = this._attachedCamera;\r\n        if (!camera) {\r\n            return;\r\n        }\r\n\r\n        if (value) {\r\n            this._onMeshTargetChangedObserver = camera.onMeshTargetChangedObservable.add((transformNode) => {\r\n                if (!transformNode) {\r\n                    return;\r\n                }\r\n\r\n                transformNode.computeWorldMatrix(true);\r\n                if ((transformNode as AbstractMesh).getBoundingInfo) {\r\n                    const diagonal = (transformNode as AbstractMesh).getBoundingInfo().diagonalLength;\r\n\r\n                    this.lowerRadiusTransitionRange = diagonal * 0.05;\r\n                    this.upperRadiusTransitionRange = diagonal * 0.05;\r\n                }\r\n            });\r\n        } else if (this._onMeshTargetChangedObserver) {\r\n            camera.onMeshTargetChangedObservable.remove(this._onMeshTargetChangedObserver);\r\n        }\r\n    }\r\n\r\n    // Connection\r\n    private _attachedCamera: Nullable<ArcRotateCamera>;\r\n    private _onAfterCheckInputsObserver: Nullable<Observer<Camera>>;\r\n    private _onMeshTargetChangedObserver: Nullable<Observer<Nullable<TransformNode>>>;\r\n\r\n    /**\r\n     * Initializes the behavior.\r\n     */\r\n    public init(): void {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Attaches the behavior to its arc rotate camera.\r\n     * @param camera Defines the camera to attach the behavior to\r\n     */\r\n    public attach(camera: ArcRotateCamera): void {\r\n        this._attachedCamera = camera;\r\n        this._onAfterCheckInputsObserver = camera.onAfterCheckInputsObservable.add(() => {\r\n            if (!this._attachedCamera) {\r\n                return;\r\n            }\r\n\r\n            // Add the bounce animation to the lower radius limit\r\n            if (this._isRadiusAtLimit(this._attachedCamera.lowerRadiusLimit)) {\r\n                this._applyBoundRadiusAnimation(this.lowerRadiusTransitionRange);\r\n            }\r\n\r\n            // Add the bounce animation to the upper radius limit\r\n            if (this._isRadiusAtLimit(this._attachedCamera.upperRadiusLimit)) {\r\n                this._applyBoundRadiusAnimation(this.upperRadiusTransitionRange);\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from its current arc rotate camera.\r\n     */\r\n    public detach(): void {\r\n        if (!this._attachedCamera) {\r\n            return;\r\n        }\r\n        if (this._onAfterCheckInputsObserver) {\r\n            this._attachedCamera.onAfterCheckInputsObservable.remove(this._onAfterCheckInputsObserver);\r\n        }\r\n        if (this._onMeshTargetChangedObserver) {\r\n            this._attachedCamera.onMeshTargetChangedObservable.remove(this._onMeshTargetChangedObserver);\r\n        }\r\n        this._attachedCamera = null;\r\n    }\r\n\r\n    // Animations\r\n    private _radiusIsAnimating: boolean = false;\r\n    private _radiusBounceTransition: Nullable<Animation> = null;\r\n    private _animatables = new Array<Animatable>();\r\n    private _cachedWheelPrecision: number;\r\n\r\n    /**\r\n     * Checks if the camera radius is at the specified limit. Takes into account animation locks.\r\n     * @param radiusLimit The limit to check against.\r\n     * @returns Bool to indicate if at limit.\r\n     */\r\n    private _isRadiusAtLimit(radiusLimit: Nullable<number>): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n\r\n        if (this._attachedCamera.radius === radiusLimit && !this._radiusIsAnimating) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Applies an animation to the radius of the camera, extending by the radiusDelta.\r\n     * @param radiusDelta The delta by which to animate to. Can be negative.\r\n     */\r\n    private _applyBoundRadiusAnimation(radiusDelta: number): void {\r\n        if (!this._attachedCamera) {\r\n            return;\r\n        }\r\n\r\n        if (!this._radiusBounceTransition) {\r\n            BouncingBehavior.EasingFunction.setEasingMode(BouncingBehavior.EasingMode);\r\n            this._radiusBounceTransition = Animation.CreateAnimation(\"radius\", Animation.ANIMATIONTYPE_FLOAT, 60, BouncingBehavior.EasingFunction);\r\n        }\r\n        // Prevent zoom until bounce has completed\r\n        this._cachedWheelPrecision = this._attachedCamera.wheelPrecision;\r\n        this._attachedCamera.wheelPrecision = Infinity;\r\n        this._attachedCamera.inertialRadiusOffset = 0;\r\n\r\n        // Animate to the radius limit\r\n        this.stopAllAnimations();\r\n        this._radiusIsAnimating = true;\r\n        const animatable = Animation.TransitionTo(\r\n            \"radius\",\r\n            this._attachedCamera.radius + radiusDelta,\r\n            this._attachedCamera,\r\n            this._attachedCamera.getScene(),\r\n            60,\r\n            this._radiusBounceTransition,\r\n            this.transitionDuration,\r\n            () => this._clearAnimationLocks()\r\n        );\r\n\r\n        if (animatable) {\r\n            this._animatables.push(animatable);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes all animation locks. Allows new animations to be added to any of the camera properties.\r\n     */\r\n    protected _clearAnimationLocks(): void {\r\n        this._radiusIsAnimating = false;\r\n\r\n        if (this._attachedCamera) {\r\n            this._attachedCamera.wheelPrecision = this._cachedWheelPrecision;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stops and removes all animations that have been applied to the camera\r\n     */\r\n    public stopAllAnimations(): void {\r\n        if (this._attachedCamera) {\r\n            this._attachedCamera.animations = [];\r\n        }\r\n        while (this._animatables.length) {\r\n            this._animatables[0].onAnimationEnd = null;\r\n            this._animatables[0].stop();\r\n            this._animatables.shift();\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAMnE,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;;;AAMjD,MAAO,gBAAgB;IAA7B,aAAA;QAkBI;;WAEG,CACI,IAAA,CAAA,kBAAkB,GAAG,GAAG,CAAC;QAEhC;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,CAAC,CAAC;QAEtC;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,CAAC,CAAC,CAAC;QAE/B,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QA+FrC,aAAa;QACL,IAAA,CAAA,kBAAkB,GAAY,KAAK,CAAC;QACpC,IAAA,CAAA,uBAAuB,GAAwB,IAAI,CAAC;QACpD,IAAA,CAAA,YAAY,GAAG,IAAI,KAAK,EAAc,CAAC;IAgFnD,CAAC;IAlNG;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,UAAU,CAAC;IACtB,CAAC;IA6BD;;OAEG,CACH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;;OAGG,CACH,IAAW,mBAAmB,CAAC,KAAc,EAAA;QACzC,IAAI,IAAI,CAAC,oBAAoB,KAAK,KAAK,EAAE,CAAC;YACtC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;QACpC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE;gBAC3F,IAAI,CAAC,aAAa,EAAE,CAAC;oBACjB,OAAO;gBACX,CAAC;gBAED,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBACvC,IAAK,aAA8B,CAAC,eAAe,EAAE,CAAC;oBAClD,MAAM,QAAQ,GAAI,aAA8B,CAAC,eAAe,EAAE,CAAC,cAAc,CAAC;oBAElF,IAAI,CAAC,0BAA0B,GAAG,QAAQ,GAAG,IAAI,CAAC;oBAClD,IAAI,CAAC,0BAA0B,GAAG,QAAQ,GAAG,IAAI,CAAC;gBACtD,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YAC3C,MAAM,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACnF,CAAC;IACL,CAAC;IAOD;;OAEG,CACI,IAAI,GAAA;IACP,aAAa;IACjB,CAAC;IAED;;;OAGG,CACI,MAAM,CAAC,MAAuB,EAAA;QACjC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,IAAI,CAAC,2BAA2B,GAAG,MAAM,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5E,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxB,OAAO;YACX,CAAC;YAED,qDAAqD;YACrD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC/D,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACrE,CAAC;YAED,qDAAqD;YACrD,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC/D,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACrE,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QACD,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC/F,CAAC;QACD,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAI,CAAC,eAAe,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACjG,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAQD;;;;OAIG,CACK,gBAAgB,CAAC,WAA6B,EAAA;QAClD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1E,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG,CACK,0BAA0B,CAAC,WAAmB,EAAA;QAClD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,gBAAgB,CAAC,cAAc,CAAC,aAAa,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAC3E,IAAI,CAAC,uBAAuB,kKAAG,YAAS,CAAC,eAAe,CAAC,QAAQ,iKAAE,YAAS,CAAC,mBAAmB,EAAE,EAAE,EAAE,gBAAgB,CAAC,cAAc,CAAC,CAAC;QAC3I,CAAC;QACD,0CAA0C;QAC1C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QACjE,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,QAAQ,CAAC;QAC/C,IAAI,CAAC,eAAe,CAAC,oBAAoB,GAAG,CAAC,CAAC;QAE9C,8BAA8B;QAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,MAAM,UAAU,kKAAG,YAAS,CAAC,YAAY,CACrC,QAAQ,EACR,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,WAAW,EACzC,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAC/B,EAAE,EACF,IAAI,CAAC,uBAAuB,EAC5B,IAAI,CAAC,kBAAkB,EACvB,GAAG,CAAG,CAAD,GAAK,CAAC,oBAAoB,EAAE,CACpC,CAAC;QAEF,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;IACL,CAAC;IAED;;OAEG,CACO,oBAAoB,GAAA;QAC1B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAEhC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACrE,CAAC;IACL,CAAC;IAED;;OAEG,CACI,iBAAiB,GAAA;QACpB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC;QACzC,CAAC;QACD,MAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAE,CAAC;YAC9B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;;AA1MD;;GAEG,CACW,iBAAA,cAAc,GAAG,gKAAI,WAAQ,CAAC,GAAG,CAAC,AAApB,CAAqB;AAEjD;;GAEG,CACW,iBAAA,UAAU,+JAAG,iBAAc,CAAC,kBAAlB,CAAqC", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Cameras/framingBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Cameras/framingBehavior.ts"], "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport { ExponentialEase, EasingFunction } from \"../../Animations/easing\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { PointerInfoPre } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { PrecisionDate } from \"../../Misc/precisionDate\";\r\n\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Animatable } from \"../../Animations/animatable.core\";\r\nimport { Animation } from \"../../Animations/animation\";\r\n\r\n/**\r\n * The framing behavior (FramingBehavior) is designed to automatically position an ArcRotateCamera when its target is set to a mesh. It is also useful if you want to prevent the camera to go under a virtual horizontal plane.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/behaviors/cameraBehaviors#framing-behavior\r\n */\r\nexport class FramingBehavior implements Behavior<ArcRotateCamera> {\r\n    /**\r\n     * Gets the name of the behavior.\r\n     */\r\n    public get name(): string {\r\n        return \"Framing\";\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the animation to zoom on target mesh has ended\r\n     */\r\n    public onTargetFramingAnimationEndObservable = new Observable<void>();\r\n\r\n    private _mode = FramingBehavior.FitFrustumSidesMode;\r\n    private _radiusScale = 1.0;\r\n    private _positionScale = 0.5;\r\n    private _defaultElevation = 0.3;\r\n    private _elevationReturnTime = 1500;\r\n    private _elevationReturnWaitTime = 1000;\r\n    private _zoomStopsAnimation = false;\r\n    private _framingTime = 1500;\r\n\r\n    /**\r\n     * The easing function used by animations\r\n     */\r\n    public static EasingFunction = new ExponentialEase();\r\n\r\n    /**\r\n     * The easing mode used by animations\r\n     */\r\n    public static EasingMode = EasingFunction.EASINGMODE_EASEINOUT;\r\n\r\n    /**\r\n     * Sets the current mode used by the behavior\r\n     */\r\n    public set mode(mode: number) {\r\n        this._mode = mode;\r\n    }\r\n\r\n    /**\r\n     * Gets current mode used by the behavior.\r\n     */\r\n    public get mode(): number {\r\n        return this._mode;\r\n    }\r\n\r\n    /**\r\n     * Sets the scale applied to the radius (1 by default)\r\n     */\r\n    public set radiusScale(radius: number) {\r\n        this._radiusScale = radius;\r\n    }\r\n\r\n    /**\r\n     * Gets the scale applied to the radius\r\n     */\r\n    public get radiusScale(): number {\r\n        return this._radiusScale;\r\n    }\r\n\r\n    /**\r\n     * Sets the scale to apply on Y axis to position camera focus. 0.5 by default which means the center of the bounding box.\r\n     */\r\n    public set positionScale(scale: number) {\r\n        this._positionScale = scale;\r\n    }\r\n\r\n    /**\r\n     * Gets the scale to apply on Y axis to position camera focus. 0.5 by default which means the center of the bounding box.\r\n     */\r\n    public get positionScale(): number {\r\n        return this._positionScale;\r\n    }\r\n\r\n    /**\r\n     * Sets the angle above/below the horizontal plane to return to when the return to default elevation idle\r\n     * behaviour is triggered, in radians.\r\n     */\r\n    public set defaultElevation(elevation: number) {\r\n        this._defaultElevation = elevation;\r\n    }\r\n\r\n    /**\r\n     * Gets the angle above/below the horizontal plane to return to when the return to default elevation idle\r\n     * behaviour is triggered, in radians.\r\n     */\r\n    public get defaultElevation() {\r\n        return this._defaultElevation;\r\n    }\r\n\r\n    /**\r\n     * Sets the time (in milliseconds) taken to return to the default beta position.\r\n     * Negative value indicates camera should not return to default.\r\n     */\r\n    public set elevationReturnTime(speed: number) {\r\n        this._elevationReturnTime = speed;\r\n    }\r\n\r\n    /**\r\n     * Gets the time (in milliseconds) taken to return to the default beta position.\r\n     * Negative value indicates camera should not return to default.\r\n     */\r\n    public get elevationReturnTime(): number {\r\n        return this._elevationReturnTime;\r\n    }\r\n\r\n    /**\r\n     * Sets the delay (in milliseconds) taken before the camera returns to the default beta position.\r\n     */\r\n    public set elevationReturnWaitTime(time: number) {\r\n        this._elevationReturnWaitTime = time;\r\n    }\r\n\r\n    /**\r\n     * Gets the delay (in milliseconds) taken before the camera returns to the default beta position.\r\n     */\r\n    public get elevationReturnWaitTime(): number {\r\n        return this._elevationReturnWaitTime;\r\n    }\r\n\r\n    /**\r\n     * Sets the flag that indicates if user zooming should stop animation.\r\n     */\r\n    public set zoomStopsAnimation(flag: boolean) {\r\n        this._zoomStopsAnimation = flag;\r\n    }\r\n\r\n    /**\r\n     * Gets the flag that indicates if user zooming should stop animation.\r\n     */\r\n    public get zoomStopsAnimation(): boolean {\r\n        return this._zoomStopsAnimation;\r\n    }\r\n\r\n    /**\r\n     * Sets the transition time when framing the mesh, in milliseconds\r\n     */\r\n    public set framingTime(time: number) {\r\n        this._framingTime = time;\r\n    }\r\n\r\n    /**\r\n     * Gets the transition time when framing the mesh, in milliseconds\r\n     */\r\n    public get framingTime() {\r\n        return this._framingTime;\r\n    }\r\n\r\n    /**\r\n     * Define if the behavior should automatically change the configured\r\n     * camera limits and sensibilities.\r\n     */\r\n    public autoCorrectCameraLimitsAndSensibility = true;\r\n\r\n    // Default behavior functions\r\n    private _onPrePointerObservableObserver: Nullable<Observer<PointerInfoPre>>;\r\n    private _onAfterCheckInputsObserver: Nullable<Observer<Camera>>;\r\n    private _onMeshTargetChangedObserver: Nullable<Observer<Nullable<TransformNode>>>;\r\n    private _attachedCamera: Nullable<ArcRotateCamera>;\r\n    private _isPointerDown = false;\r\n    private _lastInteractionTime = -Infinity;\r\n\r\n    /**\r\n     * Initializes the behavior.\r\n     */\r\n    public init(): void {\r\n        // Do nothing\r\n    }\r\n\r\n    /**\r\n     * Attaches the behavior to its arc rotate camera.\r\n     * @param camera Defines the camera to attach the behavior to\r\n     */\r\n    public attach(camera: ArcRotateCamera): void {\r\n        this._attachedCamera = camera;\r\n        const scene = this._attachedCamera.getScene();\r\n\r\n        FramingBehavior.EasingFunction.setEasingMode(FramingBehavior.EasingMode);\r\n\r\n        this._onPrePointerObservableObserver = scene.onPrePointerObservable.add((pointerInfoPre) => {\r\n            if (pointerInfoPre.type === PointerEventTypes.POINTERDOWN) {\r\n                this._isPointerDown = true;\r\n                return;\r\n            }\r\n\r\n            if (pointerInfoPre.type === PointerEventTypes.POINTERUP) {\r\n                this._isPointerDown = false;\r\n            }\r\n        });\r\n\r\n        this._onMeshTargetChangedObserver = camera.onMeshTargetChangedObservable.add((transformNode) => {\r\n            if (transformNode && (transformNode as AbstractMesh).getBoundingInfo) {\r\n                this.zoomOnMesh(transformNode as AbstractMesh, undefined, () => {\r\n                    this.onTargetFramingAnimationEndObservable.notifyObservers();\r\n                });\r\n            }\r\n        });\r\n\r\n        this._onAfterCheckInputsObserver = camera.onAfterCheckInputsObservable.add(() => {\r\n            // Stop the animation if there is user interaction and the animation should stop for this interaction\r\n            this._applyUserInteraction();\r\n\r\n            // Maintain the camera above the ground. If the user pulls the camera beneath the ground plane, lift it\r\n            // back to the default position after a given timeout\r\n            this._maintainCameraAboveGround();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from its current arc rotate camera.\r\n     */\r\n    public detach(): void {\r\n        if (!this._attachedCamera) {\r\n            return;\r\n        }\r\n\r\n        const scene = this._attachedCamera.getScene();\r\n\r\n        if (this._onPrePointerObservableObserver) {\r\n            scene.onPrePointerObservable.remove(this._onPrePointerObservableObserver);\r\n        }\r\n\r\n        if (this._onAfterCheckInputsObserver) {\r\n            this._attachedCamera.onAfterCheckInputsObservable.remove(this._onAfterCheckInputsObserver);\r\n        }\r\n\r\n        if (this._onMeshTargetChangedObserver) {\r\n            this._attachedCamera.onMeshTargetChangedObservable.remove(this._onMeshTargetChangedObserver);\r\n        }\r\n\r\n        this._attachedCamera = null;\r\n    }\r\n\r\n    // Framing control\r\n    private _animatables = new Array<Animatable>();\r\n    private _betaIsAnimating = false;\r\n    private _betaTransition: Animation;\r\n    private _radiusTransition: Animation;\r\n    private _vectorTransition: Animation;\r\n\r\n    /**\r\n     * Targets the given mesh and updates zoom level accordingly.\r\n     * @param mesh  The mesh to target.\r\n     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh\r\n     * @param onAnimationEnd Callback triggered at the end of the framing animation\r\n     */\r\n    public zoomOnMesh(mesh: AbstractMesh, focusOnOriginXZ: boolean = false, onAnimationEnd: Nullable<() => void> = null): void {\r\n        mesh.computeWorldMatrix(true);\r\n\r\n        const boundingBox = mesh.getBoundingInfo().boundingBox;\r\n        this.zoomOnBoundingInfo(boundingBox.minimumWorld, boundingBox.maximumWorld, focusOnOriginXZ, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Targets the given mesh with its children and updates zoom level accordingly.\r\n     * @param mesh The mesh to target.\r\n     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh\r\n     * @param onAnimationEnd Callback triggered at the end of the framing animation\r\n     */\r\n    public zoomOnMeshHierarchy(mesh: AbstractMesh, focusOnOriginXZ: boolean = false, onAnimationEnd: Nullable<() => void> = null): void {\r\n        mesh.computeWorldMatrix(true);\r\n\r\n        const boundingBox = mesh.getHierarchyBoundingVectors(true);\r\n        this.zoomOnBoundingInfo(boundingBox.min, boundingBox.max, focusOnOriginXZ, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Targets the given meshes with their children and updates zoom level accordingly.\r\n     * @param meshes  The mesh to target.\r\n     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh\r\n     * @param onAnimationEnd Callback triggered at the end of the framing animation\r\n     */\r\n    public zoomOnMeshesHierarchy(meshes: AbstractMesh[], focusOnOriginXZ: boolean = false, onAnimationEnd: Nullable<() => void> = null): void {\r\n        const min = new Vector3(Number.MAX_VALUE, Number.MAX_VALUE, Number.MAX_VALUE);\r\n        const max = new Vector3(-Number.MAX_VALUE, -Number.MAX_VALUE, -Number.MAX_VALUE);\r\n\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const boundingInfo = meshes[i].getHierarchyBoundingVectors(true);\r\n            Vector3.CheckExtends(boundingInfo.min, min, max);\r\n            Vector3.CheckExtends(boundingInfo.max, min, max);\r\n        }\r\n\r\n        this.zoomOnBoundingInfo(min, max, focusOnOriginXZ, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Targets the bounding box info defined by its extends and updates zoom level accordingly.\r\n     * @param minimumWorld Determines the smaller position of the bounding box extend\r\n     * @param maximumWorld Determines the bigger position of the bounding box extend\r\n     * @param focusOnOriginXZ Determines if the camera should focus on 0 in the X and Z axis instead of the mesh\r\n     * @param onAnimationEnd Callback triggered at the end of the framing animation\r\n     * @returns true if the zoom was done\r\n     */\r\n    public zoomOnBoundingInfo(minimumWorld: Vector3, maximumWorld: Vector3, focusOnOriginXZ: boolean = false, onAnimationEnd: Nullable<() => void> = null): boolean {\r\n        let zoomTarget: Vector3;\r\n\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n\r\n        // Find target by interpolating from bottom of bounding box in world-space to top via framingPositionY\r\n        const bottom = minimumWorld.y;\r\n        const top = maximumWorld.y;\r\n        const zoomTargetY = bottom + (top - bottom) * this._positionScale;\r\n        const radiusWorld = maximumWorld.subtract(minimumWorld).scale(0.5);\r\n\r\n        if (!isFinite(zoomTargetY)) {\r\n            return false; // Abort mission as there is no target\r\n        }\r\n\r\n        if (focusOnOriginXZ) {\r\n            zoomTarget = new Vector3(0, zoomTargetY, 0);\r\n        } else {\r\n            const centerWorld = minimumWorld.add(radiusWorld);\r\n            zoomTarget = new Vector3(centerWorld.x, zoomTargetY, centerWorld.z);\r\n        }\r\n\r\n        if (!this._vectorTransition) {\r\n            this._vectorTransition = Animation.CreateAnimation(\"target\", Animation.ANIMATIONTYPE_VECTOR3, 60, FramingBehavior.EasingFunction);\r\n        }\r\n\r\n        this._betaIsAnimating = true;\r\n        let animatable = Animation.TransitionTo(\"target\", zoomTarget, this._attachedCamera, this._attachedCamera.getScene(), 60, this._vectorTransition, this._framingTime);\r\n        if (animatable) {\r\n            this._animatables.push(animatable);\r\n        }\r\n\r\n        // sets the radius and lower radius bounds\r\n        // Small delta ensures camera is not always at lower zoom limit.\r\n        let radius = 0;\r\n        if (this._mode === FramingBehavior.FitFrustumSidesMode) {\r\n            const position = this._calculateLowerRadiusFromModelBoundingSphere(minimumWorld, maximumWorld);\r\n            if (this.autoCorrectCameraLimitsAndSensibility) {\r\n                this._attachedCamera.lowerRadiusLimit = radiusWorld.length() + this._attachedCamera.minZ;\r\n            }\r\n            radius = position;\r\n        } else if (this._mode === FramingBehavior.IgnoreBoundsSizeMode) {\r\n            radius = this._calculateLowerRadiusFromModelBoundingSphere(minimumWorld, maximumWorld);\r\n            if (this.autoCorrectCameraLimitsAndSensibility && this._attachedCamera.lowerRadiusLimit === null) {\r\n                this._attachedCamera.lowerRadiusLimit = this._attachedCamera.minZ;\r\n            }\r\n        }\r\n\r\n        // Set sensibilities\r\n        if (this.autoCorrectCameraLimitsAndSensibility) {\r\n            const extend = maximumWorld.subtract(minimumWorld).length();\r\n            this._attachedCamera.panningSensibility = 5000 / extend;\r\n            this._attachedCamera.wheelPrecision = 100 / radius;\r\n        }\r\n\r\n        // transition to new radius\r\n        if (!this._radiusTransition) {\r\n            this._radiusTransition = Animation.CreateAnimation(\"radius\", Animation.ANIMATIONTYPE_FLOAT, 60, FramingBehavior.EasingFunction);\r\n        }\r\n\r\n        animatable = Animation.TransitionTo(\"radius\", radius, this._attachedCamera, this._attachedCamera.getScene(), 60, this._radiusTransition, this._framingTime, () => {\r\n            this.stopAllAnimations();\r\n            if (onAnimationEnd) {\r\n                onAnimationEnd();\r\n            }\r\n\r\n            if (this._attachedCamera && this._attachedCamera.useInputToRestoreState) {\r\n                this._attachedCamera.storeState();\r\n            }\r\n        });\r\n\r\n        if (animatable) {\r\n            this._animatables.push(animatable);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Calculates the lowest radius for the camera based on the bounding box of the mesh.\r\n     * @param minimumWorld\r\n     * @param maximumWorld\r\n     * @returns The minimum distance from the primary mesh's center point at which the camera must be kept in order\r\n     *\t\t to fully enclose the mesh in the viewing frustum.\r\n     */\r\n    protected _calculateLowerRadiusFromModelBoundingSphere(minimumWorld: Vector3, maximumWorld: Vector3): number {\r\n        const camera = this._attachedCamera;\r\n\r\n        if (!camera) {\r\n            return 0;\r\n        }\r\n\r\n        let distance = camera._calculateLowerRadiusFromModelBoundingSphere(minimumWorld, maximumWorld, this._radiusScale);\r\n        if (camera.lowerRadiusLimit && this._mode === FramingBehavior.IgnoreBoundsSizeMode) {\r\n            // Don't exceed the requested limit\r\n            distance = distance < camera.lowerRadiusLimit ? camera.lowerRadiusLimit : distance;\r\n        }\r\n\r\n        // Don't exceed the upper radius limit\r\n        if (camera.upperRadiusLimit) {\r\n            distance = distance > camera.upperRadiusLimit ? camera.upperRadiusLimit : distance;\r\n        }\r\n\r\n        return distance;\r\n    }\r\n\r\n    /**\r\n     * Keeps the camera above the ground plane. If the user pulls the camera below the ground plane, the camera\r\n     * is automatically returned to its default position (expected to be above ground plane).\r\n     */\r\n    private _maintainCameraAboveGround(): void {\r\n        if (this._elevationReturnTime < 0) {\r\n            return;\r\n        }\r\n\r\n        const timeSinceInteraction = PrecisionDate.Now - this._lastInteractionTime;\r\n        const defaultBeta = Math.PI * 0.5 - this._defaultElevation;\r\n        const limitBeta = Math.PI * 0.5;\r\n\r\n        // Bring the camera back up if below the ground plane\r\n        if (this._attachedCamera && !this._betaIsAnimating && this._attachedCamera.beta > limitBeta && timeSinceInteraction >= this._elevationReturnWaitTime) {\r\n            this._betaIsAnimating = true;\r\n\r\n            //Transition to new position\r\n            this.stopAllAnimations();\r\n\r\n            if (!this._betaTransition) {\r\n                this._betaTransition = Animation.CreateAnimation(\"beta\", Animation.ANIMATIONTYPE_FLOAT, 60, FramingBehavior.EasingFunction);\r\n            }\r\n\r\n            const animatable = Animation.TransitionTo(\r\n                \"beta\",\r\n                defaultBeta,\r\n                this._attachedCamera,\r\n                this._attachedCamera.getScene(),\r\n                60,\r\n                this._betaTransition,\r\n                this._elevationReturnTime,\r\n                () => {\r\n                    this._clearAnimationLocks();\r\n                    this.stopAllAnimations();\r\n                }\r\n            );\r\n\r\n            if (animatable) {\r\n                this._animatables.push(animatable);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes all animation locks. Allows new animations to be added to any of the arcCamera properties.\r\n     */\r\n    private _clearAnimationLocks(): void {\r\n        this._betaIsAnimating = false;\r\n    }\r\n\r\n    /**\r\n     *  Applies any current user interaction to the camera. Takes into account maximum alpha rotation.\r\n     */\r\n    private _applyUserInteraction(): void {\r\n        if (this.isUserIsMoving) {\r\n            this._lastInteractionTime = PrecisionDate.Now;\r\n            this.stopAllAnimations();\r\n            this._clearAnimationLocks();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Stops and removes all animations that have been applied to the camera\r\n     */\r\n    public stopAllAnimations(): void {\r\n        if (this._attachedCamera) {\r\n            this._attachedCamera.animations = [];\r\n        }\r\n\r\n        while (this._animatables.length) {\r\n            if (this._animatables[0]) {\r\n                this._animatables[0].onAnimationEnd = null;\r\n                this._animatables[0].stop();\r\n            }\r\n            this._animatables.shift();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets a value indicating if the user is moving the camera\r\n     */\r\n    public get isUserIsMoving(): boolean {\r\n        if (!this._attachedCamera) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            this._attachedCamera.inertialAlphaOffset !== 0 ||\r\n            this._attachedCamera.inertialBetaOffset !== 0 ||\r\n            this._attachedCamera.inertialRadiusOffset !== 0 ||\r\n            this._attachedCamera.inertialPanningX !== 0 ||\r\n            this._attachedCamera.inertialPanningY !== 0 ||\r\n            this._isPointerDown\r\n        );\r\n    }\r\n\r\n    // Statics\r\n\r\n    /**\r\n     * The camera can move all the way towards the mesh.\r\n     */\r\n    public static IgnoreBoundsSizeMode = 0;\r\n\r\n    /**\r\n     * The camera is not allowed to zoom closer to the mesh than the point at which the adjusted bounding sphere touches the frustum sides\r\n     */\r\n    public static FitFrustumSidesMode = 1;\r\n}\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AAE1E,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAGnD,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAIzD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD,OAAO,EAAE,SAAS,EAAE,MAAM,4BAA4B,CAAC;;;;;;;AAMjD,MAAO,eAAe;IAA5B,aAAA;QAQI;;WAEG,CACI,IAAA,CAAA,qCAAqC,GAAG,8JAAI,aAAU,EAAQ,CAAC;QAE9D,IAAA,CAAA,KAAK,GAAG,eAAe,CAAC,mBAAmB,CAAC;QAC5C,IAAA,CAAA,YAAY,GAAG,GAAG,CAAC;QACnB,IAAA,CAAA,cAAc,GAAG,GAAG,CAAC;QACrB,IAAA,CAAA,iBAAiB,GAAG,GAAG,CAAC;QACxB,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAC5B,IAAA,CAAA,wBAAwB,GAAG,IAAI,CAAC;QAChC,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAA,CAAA,YAAY,GAAG,IAAI,CAAC;QAgI5B;;;WAGG,CACI,IAAA,CAAA,qCAAqC,GAAG,IAAI,CAAC;QAO5C,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,oBAAoB,GAAG,CAAC,QAAQ,CAAC;QAyEzC,kBAAkB;QACV,IAAA,CAAA,YAAY,GAAG,IAAI,KAAK,EAAc,CAAC;QACvC,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;IAkRrC,CAAC;IA5fG;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,SAAS,CAAC;IACrB,CAAC;IA0BD;;OAEG,CACH,IAAW,IAAI,CAAC,IAAY,EAAA;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACtB,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,CAAC,MAAc,EAAA;QACjC,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,CAAC,KAAa,EAAA;QAClC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;IAChC,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG,CACH,IAAW,gBAAgB,CAAC,SAAiB,EAAA;QACzC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;IACvC,CAAC;IAED;;;OAGG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG,CACH,IAAW,mBAAmB,CAAC,KAAa,EAAA;QACxC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IACtC,CAAC;IAED;;;OAGG,CACH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG,CACH,IAAW,uBAAuB,CAAC,IAAY,EAAA;QAC3C,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;IACzC,CAAC;IAED;;OAEG,CACH,IAAW,uBAAuB,GAAA;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,CAAC,IAAa,EAAA;QACvC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,CAAC,IAAY,EAAA;QAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAgBD;;OAEG,CACI,IAAI,GAAA;IACP,aAAa;IACjB,CAAC;IAED;;;OAGG,CACI,MAAM,CAAC,MAAuB,EAAA;QACjC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9C,eAAe,CAAC,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAEzE,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE;YACvF,IAAI,cAAc,CAAC,IAAI,oKAAK,oBAAiB,CAAC,WAAW,EAAE,CAAC;gBACxD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,OAAO;YACX,CAAC;YAED,IAAI,cAAc,CAAC,IAAI,oKAAK,oBAAiB,CAAC,SAAS,EAAE,CAAC;gBACtD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAChC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE;YAC3F,IAAI,aAAa,IAAK,aAA8B,CAAC,eAAe,EAAE,CAAC;gBACnE,IAAI,CAAC,UAAU,CAAC,aAA6B,EAAE,SAAS,EAAE,GAAG,EAAE;oBAC3D,IAAI,CAAC,qCAAqC,CAAC,eAAe,EAAE,CAAC;gBACjE,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,2BAA2B,GAAG,MAAM,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5E,qGAAqG;YACrG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAE7B,uGAAuG;YACvG,qDAAqD;YACrD,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,+BAA+B,EAAE,CAAC;YACvC,KAAK,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,IAAI,CAAC,eAAe,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC/F,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAI,CAAC,eAAe,CAAC,6BAA6B,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QACjG,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IASD;;;;;OAKG,CACI,UAAU,CAAC,IAAkB,EAAE,kBAA2B,KAAK,EAAE,iBAAuC,IAAI,EAAA;QAC/G,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE9B,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,WAAW,CAAC;QACvD,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,YAAY,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACjH,CAAC;IAED;;;;;OAKG,CACI,mBAAmB,CAAC,IAAkB,EAAE,kBAA2B,KAAK,EAAE,iBAAuC,IAAI,EAAA;QACxH,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAE9B,MAAM,WAAW,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,GAAG,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IAC/F,CAAC;IAED;;;;;OAKG,CACI,qBAAqB,CAAC,MAAsB,EAAE,kBAA2B,KAAK,EAAE,iBAAuC,IAAI,EAAA;QAC9H,MAAM,GAAG,GAAG,mKAAI,UAAO,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9E,MAAM,GAAG,GAAG,mKAAI,UAAO,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEjF,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACrC,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;2KACjE,UAAO,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;2KACjD,UAAO,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC;IACvE,CAAC;IAED;;;;;;;OAOG,CACI,kBAAkB,CAAC,YAAqB,EAAE,YAAqB,EAAE,kBAA2B,KAAK,EAAE,iBAAuC,IAAI,EAAA;QACjJ,IAAI,UAAmB,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,sGAAsG;QACtG,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC;QAC9B,MAAM,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC;QAC3B,MAAM,WAAW,GAAG,MAAM,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;QAClE,MAAM,WAAW,GAAG,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEnE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACzB,OAAO,KAAK,CAAC,CAAC,sCAAsC;QACxD,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YAClB,UAAU,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;QAChD,CAAC,MAAM,CAAC;YACJ,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAClD,UAAU,GAAG,mKAAI,UAAO,CAAC,WAAW,CAAC,CAAC,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,kKAAG,YAAS,CAAC,eAAe,CAAC,QAAQ,iKAAE,YAAS,CAAC,qBAAqB,EAAE,EAAE,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;QACtI,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,UAAU,kKAAG,YAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACpK,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;QAED,0CAA0C;QAC1C,gEAAgE;QAChE,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,mBAAmB,EAAE,CAAC;YACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,4CAA4C,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;YAC/F,IAAI,IAAI,CAAC,qCAAqC,EAAE,CAAC;gBAC7C,IAAI,CAAC,eAAe,CAAC,gBAAgB,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC7F,CAAC;YACD,MAAM,GAAG,QAAQ,CAAC;QACtB,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,oBAAoB,EAAE,CAAC;YAC7D,MAAM,GAAG,IAAI,CAAC,4CAA4C,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;YACvF,IAAI,IAAI,CAAC,qCAAqC,IAAI,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;gBAC/F,IAAI,CAAC,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACtE,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,IAAI,IAAI,CAAC,qCAAqC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC;YAC5D,IAAI,CAAC,eAAe,CAAC,kBAAkB,GAAG,IAAI,GAAG,MAAM,CAAC;YACxD,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC;QACvD,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,kKAAG,YAAS,CAAC,eAAe,CAAC,QAAQ,iKAAE,YAAS,CAAC,mBAAmB,EAAE,EAAE,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;QACpI,CAAC;QAED,UAAU,kKAAG,YAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7J,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,cAAc,EAAE,CAAC;gBACjB,cAAc,EAAE,CAAC;YACrB,CAAC;YAED,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE,CAAC;gBACtE,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;YACtC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACO,4CAA4C,CAAC,YAAqB,EAAE,YAAqB,EAAA;QAC/F,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;QAEpC,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO,CAAC,CAAC;QACb,CAAC;QAED,IAAI,QAAQ,GAAG,MAAM,CAAC,4CAA4C,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAClH,IAAI,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,oBAAoB,EAAE,CAAC;YACjF,mCAAmC;YACnC,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC;QACvF,CAAC;QAED,sCAAsC;QACtC,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC1B,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC;QACvF,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG,CACK,0BAA0B,GAAA;QAC9B,IAAI,IAAI,CAAC,oBAAoB,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,MAAM,oBAAoB,gKAAG,gBAAa,CAAC,GAAG,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC3E,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;QAEhC,qDAAqD;QACrD,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,SAAS,IAAI,oBAAoB,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACnJ,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAE7B,4BAA4B;YAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEzB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACxB,IAAI,CAAC,eAAe,kKAAG,YAAS,CAAC,eAAe,CAAC,MAAM,iKAAE,YAAS,CAAC,mBAAmB,EAAE,EAAE,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;YAChI,CAAC;YAED,MAAM,UAAU,kKAAG,YAAS,CAAC,YAAY,CACrC,MAAM,EACN,WAAW,EACX,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAC/B,EAAE,EACF,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,oBAAoB,EACzB,GAAG,EAAE;gBACD,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,CACJ,CAAC;YAEF,IAAI,UAAU,EAAE,CAAC;gBACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACK,oBAAoB,GAAA;QACxB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED;;OAEG,CACK,qBAAqB,GAAA;QACzB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,oBAAoB,gKAAG,gBAAa,CAAC,GAAG,CAAC;YAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;IACL,CAAC;IAED;;OAEG,CACI,iBAAiB,GAAA;QACpB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,EAAE,CAAC;QACzC,CAAC;QAED,MAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3C,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,AACH,IAAI,CAAC,eAAe,CAAC,mBAAmB,KAAK,CAAC,IAC9C,IAAI,CAAC,eAAe,CAAC,kBAAkB,KAAK,CAAC,IAC7C,IAAI,CAAC,eAAe,CAAC,oBAAoB,KAAK,CAAC,IAC/C,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC,IAC3C,IAAI,CAAC,eAAe,CAAC,gBAAgB,KAAK,CAAC,IAC3C,IAAI,CAAC,cAAc,CACtB,CAAC;IACN,CAAC;;AA1dD;;GAEG,CACW,gBAAA,cAAc,GAAG,gKAAI,kBAAe,EAAE,AAAxB,CAAyB;AAErD;;GAEG,CACW,gBAAA,UAAU,+JAAG,iBAAc,CAAC,oBAAlB,CAAuC;AAod/D,UAAU;AAEV;;GAEG,CACW,gBAAA,oBAAoB,GAAG,CAAC,AAAJ,CAAK;AAEvC;;GAEG,CACW,gBAAA,mBAAmB,GAAG,CAAC,AAAJ,CAAK", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Cameras/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Cameras/index.ts"], "sourcesContent": ["export * from \"./autoRotationBehavior\";\r\nexport * from \"./bouncingBehavior\";\r\nexport * from \"./framingBehavior\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,wBAAwB,CAAC;AACvC,cAAc,oBAAoB,CAAC;AACnC,cAAc,mBAAmB,CAAC", "debugId": null}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/attachToBoxBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/attachToBoxBehavior.ts"], "sourcesContent": ["import { Vector3, Matrix, Quaternion } from \"../../Maths/math.vector\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Behavior } from \"../../Behaviors/behavior\";\r\n\r\n/**\r\n * @internal\r\n */\r\nclass FaceDirectionInfo {\r\n    constructor(\r\n        public direction: Vector3,\r\n        public rotatedDirection = new Vector3(),\r\n        public diff = 0,\r\n        public ignore = false\r\n    ) {}\r\n}\r\n\r\n/**\r\n * A behavior that when attached to a mesh will will place a specified node on the meshes face pointing towards the camera\r\n */\r\nexport class AttachToBoxBehavior implements Behavior<Mesh> {\r\n    /**\r\n     *  [\"AttachToBoxBehavior\"] The name of the behavior\r\n     */\r\n    public name = \"AttachToBoxBehavior\";\r\n    /**\r\n     * [0.15] The distance away from the face of the mesh that the UI should be attached to (default: 0.15)\r\n     */\r\n    public distanceAwayFromFace = 0.15;\r\n    /**\r\n     * [0.15] The distance from the bottom of the face that the UI should be attached to (default: 0.15)\r\n     */\r\n    public distanceAwayFromBottomOfFace = 0.15;\r\n    private _faceVectors = [\r\n        new FaceDirectionInfo(Vector3.Up()),\r\n        new FaceDirectionInfo(Vector3.Down()),\r\n        new FaceDirectionInfo(Vector3.Left()),\r\n        new FaceDirectionInfo(Vector3.Right()),\r\n        new FaceDirectionInfo(Vector3.Forward()),\r\n        new FaceDirectionInfo(Vector3.Forward().scaleInPlace(-1)),\r\n    ];\r\n    private _target: Mesh;\r\n    private _scene: Scene;\r\n    private _onRenderObserver: Nullable<Observer<Scene>>;\r\n    private _tmpMatrix = new Matrix();\r\n    private _tmpVector = new Vector3();\r\n\r\n    /**\r\n     * Creates the AttachToBoxBehavior, used to attach UI to the closest face of the box to a camera\r\n     * @param _ui The transform node that should be attached to the mesh\r\n     */\r\n    constructor(private _ui: TransformNode) {\r\n        /* Does nothing */\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {\r\n        /* Does nothing */\r\n    }\r\n\r\n    private _closestFace(targetDirection: Vector3) {\r\n        // Go over each face and calculate the angle between the face's normal and targetDirection\r\n        for (const v of this._faceVectors) {\r\n            if (!this._target.rotationQuaternion) {\r\n                this._target.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._target.rotation.y, this._target.rotation.x, this._target.rotation.z);\r\n            }\r\n            this._target.rotationQuaternion.toRotationMatrix(this._tmpMatrix);\r\n            Vector3.TransformCoordinatesToRef(v.direction, this._tmpMatrix, v.rotatedDirection);\r\n            v.diff = Vector3.GetAngleBetweenVectors(v.rotatedDirection, targetDirection, Vector3.Cross(v.rotatedDirection, targetDirection));\r\n        }\r\n        // Return the face information of the one with the normal closest to target direction\r\n        return this._faceVectors.reduce((min, p) => {\r\n            if (min.ignore) {\r\n                return p;\r\n            } else if (p.ignore) {\r\n                return min;\r\n            } else {\r\n                return min.diff < p.diff ? min : p;\r\n            }\r\n        }, this._faceVectors[0]);\r\n    }\r\n\r\n    private _zeroVector = Vector3.Zero();\r\n    private _lookAtTmpMatrix = new Matrix();\r\n    private _lookAtToRef(pos: Vector3, up = new Vector3(0, 1, 0), ref: Quaternion) {\r\n        Matrix.LookAtLHToRef(this._zeroVector, pos, up, this._lookAtTmpMatrix);\r\n        this._lookAtTmpMatrix.invert();\r\n        Quaternion.FromRotationMatrixToRef(this._lookAtTmpMatrix, ref);\r\n    }\r\n\r\n    /**\r\n     * Attaches the AttachToBoxBehavior to the passed in mesh\r\n     * @param target The mesh that the specified node will be attached to\r\n     */\r\n    attach(target: Mesh) {\r\n        this._target = target;\r\n        this._scene = this._target.getScene();\r\n\r\n        // Every frame, update the app bars position\r\n        this._onRenderObserver = this._scene.onBeforeRenderObservable.add(() => {\r\n            if (!this._scene.activeCamera) {\r\n                return;\r\n            }\r\n\r\n            // Find the face closest to the cameras position\r\n            let cameraPos = this._scene.activeCamera.position;\r\n            if ((<any>this._scene.activeCamera).devicePosition) {\r\n                cameraPos = (<any>this._scene.activeCamera).devicePosition;\r\n            }\r\n            const facing = this._closestFace(cameraPos.subtract(target.position));\r\n            if (this._scene.activeCamera.leftCamera) {\r\n                this._scene.activeCamera.leftCamera.computeWorldMatrix().getRotationMatrixToRef(this._tmpMatrix);\r\n            } else {\r\n                this._scene.activeCamera.computeWorldMatrix().getRotationMatrixToRef(this._tmpMatrix);\r\n            }\r\n\r\n            // Get camera up direction\r\n            Vector3.TransformCoordinatesToRef(Vector3.Up(), this._tmpMatrix, this._tmpVector);\r\n            // Ignore faces to not select a parallel face for the up vector of the UI\r\n            for (const v of this._faceVectors) {\r\n                if (facing.direction.x && v.direction.x) {\r\n                    v.ignore = true;\r\n                }\r\n                if (facing.direction.y && v.direction.y) {\r\n                    v.ignore = true;\r\n                }\r\n                if (facing.direction.z && v.direction.z) {\r\n                    v.ignore = true;\r\n                }\r\n            }\r\n            const facingUp = this._closestFace(this._tmpVector);\r\n            // Unignore faces\r\n            for (const v of this._faceVectors) {\r\n                v.ignore = false;\r\n            }\r\n\r\n            // Position the app bar on that face\r\n            this._ui.position.copyFrom(target.position);\r\n            if (facing.direction.x) {\r\n                facing.rotatedDirection.scaleToRef(target.scaling.x / 2 + this.distanceAwayFromFace, this._tmpVector);\r\n                this._ui.position.addInPlace(this._tmpVector);\r\n            }\r\n            if (facing.direction.y) {\r\n                facing.rotatedDirection.scaleToRef(target.scaling.y / 2 + this.distanceAwayFromFace, this._tmpVector);\r\n                this._ui.position.addInPlace(this._tmpVector);\r\n            }\r\n            if (facing.direction.z) {\r\n                facing.rotatedDirection.scaleToRef(target.scaling.z / 2 + this.distanceAwayFromFace, this._tmpVector);\r\n                this._ui.position.addInPlace(this._tmpVector);\r\n            }\r\n\r\n            // Rotate to be oriented properly to the camera\r\n            if (!this._ui.rotationQuaternion) {\r\n                this._ui.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._ui.rotation.y, this._ui.rotation.x, this._ui.rotation.z);\r\n            }\r\n            facing.rotatedDirection.scaleToRef(-1, this._tmpVector);\r\n            this._lookAtToRef(this._tmpVector, facingUp.rotatedDirection, this._ui.rotationQuaternion);\r\n\r\n            // Place ui the correct distance from the bottom of the mesh\r\n            if (facingUp.direction.x) {\r\n                this._ui.up.scaleToRef(this.distanceAwayFromBottomOfFace - target.scaling.x / 2, this._tmpVector);\r\n            }\r\n            if (facingUp.direction.y) {\r\n                this._ui.up.scaleToRef(this.distanceAwayFromBottomOfFace - target.scaling.y / 2, this._tmpVector);\r\n            }\r\n            if (facingUp.direction.z) {\r\n                this._ui.up.scaleToRef(this.distanceAwayFromBottomOfFace - target.scaling.z / 2, this._tmpVector);\r\n            }\r\n            this._ui.position.addInPlace(this._tmpVector);\r\n        });\r\n    }\r\n\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    detach() {\r\n        this._scene.onBeforeRenderObservable.remove(this._onRenderObserver);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;;AAQtE;;GAEG,CACH,MAAM,iBAAiB;IACnB,YACW,SAAkB,EAClB,mBAAmB,mKAAI,UAAO,EAAE,EAChC,OAAO,CAAC,EACR,SAAS,KAAK,CAAA;QAHd,IAAA,CAAA,SAAS,GAAT,SAAS,CAAS;QAClB,IAAA,CAAA,gBAAgB,GAAhB,gBAAgB,CAAgB;QAChC,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAI;QACR,IAAA,CAAA,MAAM,GAAN,MAAM,CAAQ;IACtB,CAAC;CACP;AAKK,MAAO,mBAAmB;IA2B5B;;;OAGG,CACH,YAAoB,GAAkB,CAAA;QAAlB,IAAA,CAAA,GAAG,GAAH,GAAG,CAAe;QA9BtC;;WAEG,CACI,IAAA,CAAA,IAAI,GAAG,qBAAqB,CAAC;QACpC;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QACnC;;WAEG,CACI,IAAA,CAAA,4BAA4B,GAAG,IAAI,CAAC;QACnC,IAAA,CAAA,YAAY,GAAG;YACnB,IAAI,iBAAiB,gKAAC,UAAO,CAAC,EAAE,EAAE,CAAC;YACnC,IAAI,iBAAiB,gKAAC,UAAO,CAAC,IAAI,EAAE,CAAC;YACrC,IAAI,iBAAiB,gKAAC,UAAO,CAAC,IAAI,EAAE,CAAC;YACrC,IAAI,iBAAiB,gKAAC,UAAO,CAAC,KAAK,EAAE,CAAC;YACtC,IAAI,iBAAiB,gKAAC,UAAO,CAAC,OAAO,EAAE,CAAC;YACxC,IAAI,iBAAiB,gKAAC,UAAO,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5D,CAAC;QAIM,IAAA,CAAA,UAAU,GAAG,mKAAI,SAAM,EAAE,CAAC;QAC1B,IAAA,CAAA,UAAU,GAAG,mKAAI,UAAO,EAAE,CAAC;QAuC3B,IAAA,CAAA,WAAW,kKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAA,CAAA,gBAAgB,GAAG,mKAAI,SAAM,EAAE,CAAC;IAjCpC,gBAAA,EAAkB,CACtB,CAAC;IAED;;OAEG,CACI,IAAI,GAAA;IACP,gBAAA,EAAkB,CACtB,CAAC;IAEO,YAAY,CAAC,eAAwB,EAAA;QACzC,0FAA0F;QAC1F,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBACnC,IAAI,CAAC,OAAO,CAAC,kBAAkB,kKAAG,aAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACjJ,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;2KAClE,UAAO,CAAC,yBAAyB,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,gBAAgB,CAAC,CAAC;YACpF,CAAC,CAAC,IAAI,kKAAG,UAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,gBAAgB,EAAE,eAAe,iKAAE,UAAO,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC,CAAC;QACrI,CAAC;QACD,qFAAqF;QACrF,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBACb,OAAO,CAAC,CAAC;YACb,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;gBAClB,OAAO,GAAG,CAAC;YACf,CAAC,MAAM,CAAC;gBACJ,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC;QACL,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IAIO,YAAY,CAAC,GAAY,EAAE,EAAE,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAe,EAAA;uKACzE,SAAM,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;uKAC/B,aAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;IACnE,CAAC;IAED;;;OAGG,CACH,MAAM,CAAC,MAAY,EAAA;QACf,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAEtC,4CAA4C;QAC5C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACnE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC5B,OAAO;YACX,CAAC;YAED,gDAAgD;YAChD,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;YAClD,IAAU,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC,cAAc,EAAE,CAAC;gBACjD,SAAS,GAAS,IAAI,CAAC,MAAM,CAAC,YAAa,CAAC,cAAc,CAAC;YAC/D,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;YACtE,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBACtC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACrG,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1F,CAAC;YAED,0BAA0B;2KAC1B,UAAO,CAAC,yBAAyB,gKAAC,UAAO,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAClF,yEAAyE;YACzE,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAE,CAAC;gBAChC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;oBACtC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,CAAC;gBACD,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;oBACtC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,CAAC;gBACD,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;oBACtC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,CAAC;YACL,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACpD,iBAAiB;YACjB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,YAAY,CAAE,CAAC;gBAChC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;YACrB,CAAC;YAED,oCAAoC;YACpC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,CAAC;YACD,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,CAAC;YACD,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBACrB,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACtG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClD,CAAC;YAED,+CAA+C;YAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,IAAI,CAAC,GAAG,CAAC,kBAAkB,kKAAG,aAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACjI,CAAC;YACD,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAE3F,4DAA4D;YAC5D,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBACvB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACtG,CAAC;YACD,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBACvB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACtG,CAAC;YACD,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC;gBACvB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,4BAA4B,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACtG,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACH,MAAM,GAAA;QACF,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IACxE,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 932, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/fadeInOutBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/fadeInOutBehavior.ts"], "sourcesContent": ["import type { Behavior } from \"../behavior\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"core/Misc/observable\";\r\nimport type { Scene } from \"core/scene\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will allow the mesh to fade in and out\r\n */\r\nexport class FadeInOutBehavior implements Behavior<Mesh> {\r\n    /**\r\n     * Time in milliseconds to delay before fading in (Default: 0)\r\n     */\r\n    public fadeInDelay = 0;\r\n\r\n    /**\r\n     * Time in milliseconds to delay before fading out (Default: 0)\r\n     */\r\n    public fadeOutDelay = 0;\r\n\r\n    /**\r\n     * Time in milliseconds for the mesh to fade in (Default: 300)\r\n     */\r\n    public fadeInTime = 300;\r\n\r\n    /**\r\n     * Time in milliseconds for the mesh to fade out (Default: 300)\r\n     */\r\n    public fadeOutTime = 300;\r\n\r\n    /**\r\n     * Time in milliseconds to delay before fading in (Default: 0)\r\n     * Will set both fade in and out delay to the same value\r\n     */\r\n    public get delay(): number {\r\n        return this.fadeInDelay;\r\n    }\r\n\r\n    public set delay(value: number) {\r\n        this.fadeInDelay = value;\r\n        this.fadeOutDelay = value;\r\n    }\r\n\r\n    private _millisecondsPerFrame = 1000 / 60;\r\n    private _hovered = false;\r\n    private _hoverValue = 0;\r\n    private _ownerNode: Nullable<Mesh> = null;\r\n    private _onBeforeRenderObserver: Nullable<Observer<Scene>> | undefined;\r\n    private _delay: number = 0;\r\n    private _time: number = 300;\r\n\r\n    /**\r\n     * Instantiates the FadeInOutBehavior\r\n     */\r\n    constructor() {}\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"FadeInOut\";\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    /**\r\n     * Attaches the fade behavior on the passed in mesh\r\n     * @param ownerNode The mesh that will be faded in/out once attached\r\n     */\r\n    public attach(ownerNode: Mesh): void {\r\n        this._ownerNode = ownerNode;\r\n        this._setAllVisibility(this._ownerNode, 0);\r\n    }\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        this._ownerNode = null;\r\n    }\r\n\r\n    /**\r\n     * Triggers the mesh to begin fading in (or out)\r\n     * @param fadeIn if the object should fade in or out (true to fade in)\r\n     */\r\n    public fadeIn(fadeIn: boolean = true) {\r\n        this._delay = fadeIn ? this.fadeInDelay : this.fadeOutDelay;\r\n        this._time = fadeIn ? this.fadeInTime : this.fadeOutTime;\r\n\r\n        // Cancel any pending updates\r\n        this._detachObserver();\r\n\r\n        // If fading in and already visible or fading out and already not visible do nothing\r\n        if (this._ownerNode && ((fadeIn && this._ownerNode.visibility >= 1) || (!fadeIn && this._ownerNode.visibility <= 0))) {\r\n            return;\r\n        }\r\n\r\n        this._hovered = fadeIn;\r\n        if (!this._hovered) {\r\n            // Make the delay the negative of fadeout delay so the hoverValue is kept above 1 until\r\n            // fadeOutDelay has elapsed\r\n            this._delay *= -1;\r\n        }\r\n\r\n        // Reset the hoverValue.  This is necessary because we may have been fading out, e.g. but not yet reached\r\n        // the delay, so the hover value is greater than 1\r\n        if (this._ownerNode!.visibility >= 1) {\r\n            this._hoverValue = this._time;\r\n        } else if (this._ownerNode!.visibility <= 0) {\r\n            this._hoverValue = 0;\r\n        }\r\n        this._update();\r\n    }\r\n\r\n    /**\r\n     * Triggers the mesh to begin fading out\r\n     */\r\n    public fadeOut() {\r\n        this.fadeIn(false);\r\n    }\r\n\r\n    private _update = () => {\r\n        if (this._ownerNode) {\r\n            this._hoverValue += this._hovered ? this._millisecondsPerFrame : -this._millisecondsPerFrame;\r\n\r\n            this._setAllVisibility(this._ownerNode, (this._hoverValue - this._delay) / this._time);\r\n\r\n            if (this._ownerNode.visibility > 1) {\r\n                this._setAllVisibility(this._ownerNode, 1);\r\n                if (this._hoverValue > this._time) {\r\n                    this._hoverValue = this._time;\r\n                    this._detachObserver();\r\n                    return;\r\n                }\r\n            } else if (this._ownerNode.visibility < 0) {\r\n                this._setAllVisibility(this._ownerNode, 0);\r\n                if (this._hoverValue < 0) {\r\n                    this._hoverValue = 0;\r\n                    this._detachObserver();\r\n                    return;\r\n                }\r\n            }\r\n\r\n            this._attachObserver();\r\n        }\r\n    };\r\n\r\n    private _setAllVisibility(mesh: AbstractMesh, value: number) {\r\n        mesh.visibility = value;\r\n\r\n        const children = mesh.getChildMeshes();\r\n\r\n        for (const c of children) {\r\n            this._setAllVisibility(c, value);\r\n        }\r\n    }\r\n\r\n    private _attachObserver() {\r\n        if (!this._onBeforeRenderObserver) {\r\n            this._onBeforeRenderObserver = this._ownerNode?.getScene().onBeforeRenderObservable.add(this._update);\r\n        }\r\n    }\r\n\r\n    private _detachObserver() {\r\n        if (this._onBeforeRenderObserver) {\r\n            this._ownerNode?.getScene().onBeforeRenderObservable.remove(this._onBeforeRenderObserver);\r\n            this._onBeforeRenderObserver = null;\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAOA;;GAEG;;;AACG,MAAO,iBAAiB;IAqB1B;;;OAGG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,KAAK,CAAC,KAAa,EAAA;QAC1B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IAC9B,CAAC;IAUD;;OAEG,CACH,aAAA;QA5CA;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAEvB;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QAExB;;WAEG,CACI,IAAA,CAAA,UAAU,GAAG,GAAG,CAAC;QAExB;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG,GAAG,CAAC;QAejB,IAAA,CAAA,qBAAqB,GAAG,IAAI,GAAG,EAAE,CAAC;QAClC,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACjB,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAChB,IAAA,CAAA,UAAU,GAAmB,IAAI,CAAC;QAElC,IAAA,CAAA,MAAM,GAAW,CAAC,CAAC;QACnB,IAAA,CAAA,KAAK,GAAW,GAAG,CAAC;QA0EpB,IAAA,CAAA,OAAO,GAAG,GAAG,EAAE;YACnB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;gBAE7F,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEvF,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;oBACjC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;oBAC3C,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;wBAChC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;wBAC9B,IAAI,CAAC,eAAe,EAAE,CAAC;wBACvB,OAAO;oBACX,CAAC;gBACL,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;oBACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;oBAC3C,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;wBACvB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;wBACrB,IAAI,CAAC,eAAe,EAAE,CAAC;wBACvB,OAAO;oBACX,CAAC;gBACL,CAAC;gBAED,IAAI,CAAC,eAAe,EAAE,CAAC;YAC3B,CAAC;QACL,CAAC,CAAC;IA7Fa,CAAC;IAEhB;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG,CACI,IAAI,GAAA,CAAI,CAAC;IAEhB;;;OAGG,CACI,MAAM,CAAC,SAAe,EAAA;QACzB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACI,MAAM,CAAC,SAAkB,IAAI,EAAA;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC;QAC5D,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC;QAEzD,6BAA6B;QAC7B,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,oFAAoF;QACpF,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,AAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,CAAC,GAAK,CAAD,AAAE,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC,AAAC,CAAC,EAAE,CAAC;YACnH,OAAO;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjB,uFAAuF;YACvF,2BAA2B;YAC3B,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;QACtB,CAAC;QAED,yGAAyG;QACzG,kDAAkD;QAClD,IAAI,IAAI,CAAC,UAAW,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;QAClC,CAAC,MAAM,IAAI,IAAI,CAAC,UAAW,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IA4BO,iBAAiB,CAAC,IAAkB,EAAE,KAAa,EAAA;QACvD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEvC,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;YACvB,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAEO,eAAe,GAAA;QACnB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1G,CAAC;IACL,CAAC;IAEO,eAAe,GAAA;QACnB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC1F,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/pointerDragBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/pointerDragBehavior.ts"], "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { TmpVectors, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { Ray } from \"../../Culling/ray\";\r\nimport { PivotTools } from \"../../Misc/pivotTools\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport { CreatePlane } from \"../../Meshes/Builders/planeBuilder\";\r\n\r\nimport type { IPointerEvent } from \"../../Events/deviceInputEvents\";\r\nimport { Epsilon } from \"../../Maths/math.constants\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will allow the mesh to be dragged around the screen based on pointer events\r\n */\r\nexport class PointerDragBehavior implements Behavior<AbstractMesh> {\r\n    private static _AnyMouseId = -2;\r\n    /**\r\n     * Abstract mesh the behavior is set on\r\n     */\r\n    public attachedNode: AbstractMesh;\r\n    protected _dragPlane: Mesh;\r\n    private _scene: Scene;\r\n    private _pointerObserver: Nullable<Observer<PointerInfo>>;\r\n    private _beforeRenderObserver: Nullable<Observer<Scene>>;\r\n    private static _PlaneScene: Scene;\r\n    private _useAlternatePickedPointAboveMaxDragAngleDragSpeed = -1.1;\r\n    private _activeDragButton: number = -1;\r\n    private _activePointerInfo: Nullable<PointerInfo>;\r\n    /**\r\n     * The maximum tolerated angle between the drag plane and dragging pointer rays to trigger pointer events. Set to 0 to allow any angle (default: 0)\r\n     */\r\n    public maxDragAngle = 0;\r\n    /**\r\n     * Butttons that can be used to initiate a drag\r\n     */\r\n    public dragButtons = [0, 1, 2];\r\n    /**\r\n     * @internal\r\n     */\r\n    public _useAlternatePickedPointAboveMaxDragAngle = false;\r\n    /**\r\n     * Get or set the currentDraggingPointerId\r\n     * @deprecated Please use currentDraggingPointerId instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get currentDraggingPointerID(): number {\r\n        return this.currentDraggingPointerId;\r\n    }\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public set currentDraggingPointerID(currentDraggingPointerId: number) {\r\n        this.currentDraggingPointerId = currentDraggingPointerId;\r\n    }\r\n    /**\r\n     * The id of the pointer that is currently interacting with the behavior (-1 when no pointer is active)\r\n     */\r\n    public currentDraggingPointerId = -1;\r\n    /**\r\n     * The last position where the pointer hit the drag plane in world space\r\n     */\r\n    public lastDragPosition: Vector3;\r\n    /**\r\n     * If the behavior is currently in a dragging state\r\n     */\r\n    public dragging = false;\r\n    /**\r\n     * The distance towards the target drag position to move each frame. This can be useful to avoid jitter. Set this to 1 for no delay. (Default: 0.2)\r\n     */\r\n    public dragDeltaRatio = 0.2;\r\n    /**\r\n     * If the drag plane orientation should be updated during the dragging (Default: true)\r\n     */\r\n    public updateDragPlane = true;\r\n    // Debug mode will display drag planes to help visualize behavior\r\n    private _debugMode = false;\r\n    private _moving = false;\r\n    /**\r\n     *  Fires each time the attached mesh is dragged with the pointer\r\n     *  * delta between last drag position and current drag position in world space\r\n     *  * dragDistance along the drag axis\r\n     *  * dragPlaneNormal normal of the current drag plane used during the drag\r\n     *  * dragPlanePoint in world space where the drag intersects the drag plane\r\n     *\r\n     *  (if validatedDrag is used, the position of the attached mesh might not equal dragPlanePoint)\r\n     */\r\n    public onDragObservable = new Observable<{\r\n        delta: Vector3;\r\n        dragPlanePoint: Vector3;\r\n        dragPlaneNormal: Vector3;\r\n        dragDistance: number;\r\n        pointerId: number;\r\n        pointerInfo: Nullable<PointerInfo>;\r\n    }>();\r\n    /**\r\n     *  Fires each time a drag begins (eg. mouse down on mesh)\r\n     *  * dragPlanePoint in world space where the drag intersects the drag plane\r\n     *\r\n     *  (if validatedDrag is used, the position of the attached mesh might not equal dragPlanePoint)\r\n     */\r\n    public onDragStartObservable = new Observable<{ dragPlanePoint: Vector3; pointerId: number; pointerInfo: Nullable<PointerInfo> }>();\r\n    /**\r\n     *  Fires each time a drag ends (eg. mouse release after drag)\r\n     *  * dragPlanePoint in world space where the drag intersects the drag plane\r\n     *\r\n     *  (if validatedDrag is used, the position of the attached mesh might not equal dragPlanePoint)\r\n     */\r\n    public onDragEndObservable = new Observable<{ dragPlanePoint: Vector3; pointerId: number; pointerInfo: Nullable<PointerInfo> }>();\r\n    /**\r\n     *  Fires each time behavior enabled state changes\r\n     */\r\n    public onEnabledObservable = new Observable<boolean>();\r\n\r\n    /**\r\n     *  If the attached mesh should be moved when dragged\r\n     */\r\n    public moveAttached = true;\r\n\r\n    /**\r\n     *  If the drag behavior will react to drag events (Default: true)\r\n     */\r\n    public set enabled(value: boolean) {\r\n        if (value != this._enabled) {\r\n            this.onEnabledObservable.notifyObservers(value);\r\n        }\r\n        this._enabled = value;\r\n    }\r\n\r\n    public get enabled() {\r\n        return this._enabled;\r\n    }\r\n    private _enabled = true;\r\n\r\n    /**\r\n     * If pointer events should start and release the drag (Default: true)\r\n     */\r\n    public startAndReleaseDragOnPointerEvents = true;\r\n    /**\r\n     * If camera controls should be detached during the drag\r\n     */\r\n    public detachCameraControls = true;\r\n\r\n    /**\r\n     * If set, the drag plane/axis will be rotated based on the attached mesh's world rotation (Default: true)\r\n     */\r\n    public useObjectOrientationForDragging = true;\r\n\r\n    /**\r\n     * Normally a drag is canceled when the user presses another button on the same pointer. If this is set to true,\r\n     * the drag will continue even if another button is pressed on the same pointer.\r\n     */\r\n    public allowOtherButtonsDuringDrag = false;\r\n\r\n    private _options: { dragAxis?: Vector3; dragPlaneNormal?: Vector3 };\r\n\r\n    /**\r\n     * Gets the options used by the behavior\r\n     */\r\n    public get options(): { dragAxis?: Vector3; dragPlaneNormal?: Vector3 } {\r\n        return this._options;\r\n    }\r\n\r\n    /**\r\n     * Sets the options used by the behavior\r\n     */\r\n    public set options(options: { dragAxis?: Vector3; dragPlaneNormal?: Vector3 }) {\r\n        this._options = options;\r\n    }\r\n\r\n    /**\r\n     * Creates a pointer drag behavior that can be attached to a mesh\r\n     * @param options The drag axis or normal of the plane that will be dragged across. If no options are specified the drag plane will always face the ray's origin (eg. camera)\r\n     * @param options.dragAxis\r\n     * @param options.dragPlaneNormal\r\n     */\r\n    constructor(options?: { dragAxis?: Vector3; dragPlaneNormal?: Vector3 }) {\r\n        this._options = options ? options : {};\r\n\r\n        let optionCount = 0;\r\n        if (this._options.dragAxis) {\r\n            optionCount++;\r\n        }\r\n        if (this._options.dragPlaneNormal) {\r\n            optionCount++;\r\n        }\r\n        if (optionCount > 1) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Multiple drag modes specified in dragBehavior options. Only one expected\";\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Predicate to determine if it is valid to move the object to a new position when it is moved.\r\n     * In the case of rotation gizmo, target contains the angle.\r\n     * @param target destination position or desired angle delta\r\n     * @returns boolean for whether or not it is valid to move\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public validateDrag = (target: Vector3) => {\r\n        return true;\r\n    };\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"PointerDrag\";\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    private _tmpVector = new Vector3(0, 0, 0);\r\n    private _alternatePickedPoint = new Vector3(0, 0, 0);\r\n    private _worldDragAxis = new Vector3(0, 0, 0);\r\n    private _targetPosition = new Vector3(0, 0, 0);\r\n    private _attachedToElement: boolean = false;\r\n    /**\r\n     * Attaches the drag behavior the passed in mesh\r\n     * @param ownerNode The mesh that will be dragged around once attached\r\n     * @param predicate Predicate to use for pick filtering\r\n     */\r\n    public attach(ownerNode: AbstractMesh, predicate?: (m: AbstractMesh) => boolean): void {\r\n        this._scene = ownerNode.getScene();\r\n        ownerNode.isNearGrabbable = true;\r\n        this.attachedNode = ownerNode;\r\n\r\n        // Initialize drag plane to not interfere with existing scene\r\n        if (!PointerDragBehavior._PlaneScene) {\r\n            if (this._debugMode) {\r\n                PointerDragBehavior._PlaneScene = this._scene;\r\n            } else {\r\n                PointerDragBehavior._PlaneScene = new Scene(this._scene.getEngine(), { virtual: true });\r\n                PointerDragBehavior._PlaneScene.detachControl();\r\n                this._scene.onDisposeObservable.addOnce(() => {\r\n                    PointerDragBehavior._PlaneScene.dispose();\r\n                    (<any>PointerDragBehavior._PlaneScene) = null;\r\n                });\r\n            }\r\n        }\r\n        this._dragPlane = CreatePlane(\r\n            \"pointerDragPlane\",\r\n            { size: this._debugMode ? 1 : 10000, updatable: false, sideOrientation: Mesh.DOUBLESIDE },\r\n            PointerDragBehavior._PlaneScene\r\n        );\r\n\r\n        // State of the drag\r\n        this.lastDragPosition = new Vector3(0, 0, 0);\r\n\r\n        const pickPredicate = predicate\r\n            ? predicate\r\n            : (m: AbstractMesh) => {\r\n                  return this.attachedNode == m || m.isDescendantOf(this.attachedNode);\r\n              };\r\n\r\n        this._pointerObserver = this._scene.onPointerObservable.add((pointerInfo) => {\r\n            if (!this.enabled) {\r\n                // If behavior is disabled before releaseDrag is ever called, call it now.\r\n                if (this._attachedToElement) {\r\n                    this.releaseDrag();\r\n                }\r\n\r\n                return;\r\n            }\r\n\r\n            // If we are dragging and the user presses another button on the same pointer, end the drag. Otherwise,\r\n            // tracking when the drag should end becomes very complex.\r\n            // gizmo.ts has similar behavior.\r\n            if (\r\n                this.dragging &&\r\n                this.currentDraggingPointerId == (<IPointerEvent>pointerInfo.event).pointerId &&\r\n                pointerInfo.event.button !== -1 &&\r\n                pointerInfo.event.button !== this._activeDragButton &&\r\n                !this.allowOtherButtonsDuringDrag\r\n            ) {\r\n                this.releaseDrag();\r\n                return;\r\n            }\r\n\r\n            if (pointerInfo.type == PointerEventTypes.POINTERDOWN) {\r\n                if (\r\n                    this.startAndReleaseDragOnPointerEvents &&\r\n                    !this.dragging &&\r\n                    pointerInfo.pickInfo &&\r\n                    pointerInfo.pickInfo.hit &&\r\n                    pointerInfo.pickInfo.pickedMesh &&\r\n                    pointerInfo.pickInfo.pickedPoint &&\r\n                    pointerInfo.pickInfo.ray &&\r\n                    pickPredicate(pointerInfo.pickInfo.pickedMesh)\r\n                ) {\r\n                    if (this._activeDragButton === -1 && this.dragButtons.indexOf(pointerInfo.event.button) !== -1) {\r\n                        this._activeDragButton = pointerInfo.event.button;\r\n                        this._activePointerInfo = pointerInfo;\r\n                        this._startDrag((<IPointerEvent>pointerInfo.event).pointerId, pointerInfo.pickInfo.ray, pointerInfo.pickInfo.pickedPoint);\r\n                    }\r\n                }\r\n            } else if (pointerInfo.type == PointerEventTypes.POINTERUP) {\r\n                if (\r\n                    this.startAndReleaseDragOnPointerEvents &&\r\n                    this.currentDraggingPointerId == (<IPointerEvent>pointerInfo.event).pointerId &&\r\n                    (this._activeDragButton === pointerInfo.event.button || this._activeDragButton === -1)\r\n                ) {\r\n                    this.releaseDrag();\r\n                }\r\n            } else if (pointerInfo.type == PointerEventTypes.POINTERMOVE) {\r\n                const pointerId = (<IPointerEvent>pointerInfo.event).pointerId;\r\n\r\n                // If drag was started with anyMouseID specified, set pointerID to the next mouse that moved\r\n                if (this.currentDraggingPointerId === PointerDragBehavior._AnyMouseId && pointerId !== PointerDragBehavior._AnyMouseId) {\r\n                    const evt = <IPointerEvent>pointerInfo.event;\r\n                    const isMouseEvent = evt.pointerType === \"mouse\" || (!this._scene.getEngine().hostInformation.isMobile && evt instanceof MouseEvent);\r\n                    if (isMouseEvent) {\r\n                        if (this._lastPointerRay[this.currentDraggingPointerId]) {\r\n                            this._lastPointerRay[pointerId] = this._lastPointerRay[this.currentDraggingPointerId];\r\n                            delete this._lastPointerRay[this.currentDraggingPointerId];\r\n                        }\r\n                        this.currentDraggingPointerId = pointerId;\r\n                    }\r\n                }\r\n\r\n                // Keep track of last pointer ray, this is used simulating the start of a drag in startDrag()\r\n                if (!this._lastPointerRay[pointerId]) {\r\n                    this._lastPointerRay[pointerId] = new Ray(new Vector3(), new Vector3());\r\n                }\r\n                if (pointerInfo.pickInfo && pointerInfo.pickInfo.ray) {\r\n                    this._lastPointerRay[pointerId].origin.copyFrom(pointerInfo.pickInfo.ray.origin);\r\n                    this._lastPointerRay[pointerId].direction.copyFrom(pointerInfo.pickInfo.ray.direction);\r\n\r\n                    if (this.currentDraggingPointerId == pointerId && this.dragging) {\r\n                        this._moveDrag(pointerInfo.pickInfo.ray);\r\n                    }\r\n                }\r\n            }\r\n        });\r\n\r\n        this._beforeRenderObserver = this._scene.onBeforeRenderObservable.add(() => {\r\n            if (this._moving && this.moveAttached) {\r\n                let needMatrixUpdate = false;\r\n                PivotTools._RemoveAndStorePivotPoint(this.attachedNode);\r\n                // Slowly move mesh to avoid jitter\r\n                this._targetPosition.subtractToRef(this.attachedNode.absolutePosition, this._tmpVector);\r\n                this._tmpVector.scaleInPlace(this.dragDeltaRatio);\r\n                this.attachedNode.getAbsolutePosition().addToRef(this._tmpVector, this._tmpVector);\r\n                if (this.validateDrag(this._tmpVector)) {\r\n                    this.attachedNode.setAbsolutePosition(this._tmpVector);\r\n                    needMatrixUpdate = true;\r\n                }\r\n                PivotTools._RestorePivotPoint(this.attachedNode);\r\n                if (needMatrixUpdate) {\r\n                    this.attachedNode.computeWorldMatrix();\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Force release the drag action by code.\r\n     */\r\n    public releaseDrag() {\r\n        if (this.dragging) {\r\n            this.dragging = false;\r\n            this.onDragEndObservable.notifyObservers({ dragPlanePoint: this.lastDragPosition, pointerId: this.currentDraggingPointerId, pointerInfo: this._activePointerInfo });\r\n        }\r\n\r\n        this.currentDraggingPointerId = -1;\r\n        this._activeDragButton = -1;\r\n        this._activePointerInfo = null;\r\n        this._moving = false;\r\n\r\n        // Reattach camera controls\r\n        if (this.detachCameraControls && this._attachedToElement && this._scene.activeCamera && !this._scene.activeCamera.leftCamera) {\r\n            if (this._scene.activeCamera.getClassName() === \"ArcRotateCamera\") {\r\n                const arcRotateCamera = this._scene.activeCamera as ArcRotateCamera;\r\n                arcRotateCamera.attachControl(\r\n                    arcRotateCamera.inputs ? arcRotateCamera.inputs.noPreventDefault : true,\r\n                    arcRotateCamera._useCtrlForPanning,\r\n                    arcRotateCamera._panningMouseButton\r\n                );\r\n            } else {\r\n                this._scene.activeCamera.attachControl(this._scene.activeCamera.inputs ? this._scene.activeCamera.inputs.noPreventDefault : true);\r\n            }\r\n            this._attachedToElement = false;\r\n        }\r\n    }\r\n\r\n    private _startDragRay = new Ray(new Vector3(), new Vector3());\r\n    private _lastPointerRay: { [key: number]: Ray } = {};\r\n    /**\r\n     * Simulates the start of a pointer drag event on the behavior\r\n     * @param pointerId pointerID of the pointer that should be simulated (Default: Any mouse pointer ID)\r\n     * @param fromRay initial ray of the pointer to be simulated (Default: Ray from camera to attached mesh)\r\n     * @param startPickedPoint picked point of the pointer to be simulated (Default: attached mesh position)\r\n     */\r\n    public startDrag(pointerId: number = PointerDragBehavior._AnyMouseId, fromRay?: Ray, startPickedPoint?: Vector3) {\r\n        this._startDrag(pointerId, fromRay, startPickedPoint);\r\n\r\n        let lastRay = this._lastPointerRay[pointerId];\r\n        if (pointerId === PointerDragBehavior._AnyMouseId) {\r\n            lastRay = this._lastPointerRay[<any>Object.keys(this._lastPointerRay)[0]];\r\n        }\r\n\r\n        if (lastRay) {\r\n            // if there was a last pointer ray drag the object there\r\n            this._moveDrag(lastRay);\r\n        }\r\n    }\r\n\r\n    protected _startDrag(pointerId: number, fromRay?: Ray, startPickedPoint?: Vector3) {\r\n        if (!this._scene.activeCamera || this.dragging || !this.attachedNode) {\r\n            return;\r\n        }\r\n\r\n        PivotTools._RemoveAndStorePivotPoint(this.attachedNode);\r\n        // Create start ray from the camera to the object\r\n        if (fromRay) {\r\n            this._startDragRay.direction.copyFrom(fromRay.direction);\r\n            this._startDragRay.origin.copyFrom(fromRay.origin);\r\n        } else {\r\n            this._startDragRay.origin.copyFrom(this._scene.activeCamera.position);\r\n            this.attachedNode.getWorldMatrix().getTranslationToRef(this._tmpVector);\r\n            this._tmpVector.subtractToRef(this._scene.activeCamera.position, this._startDragRay.direction);\r\n        }\r\n\r\n        this._updateDragPlanePosition(this._startDragRay, startPickedPoint ? startPickedPoint : this._tmpVector);\r\n\r\n        const pickedPoint = this._pickWithRayOnDragPlane(this._startDragRay);\r\n        if (pickedPoint) {\r\n            this.dragging = true;\r\n            this.currentDraggingPointerId = pointerId;\r\n            this.lastDragPosition.copyFrom(pickedPoint);\r\n            this.onDragStartObservable.notifyObservers({ dragPlanePoint: pickedPoint, pointerId: this.currentDraggingPointerId, pointerInfo: this._activePointerInfo });\r\n            this._targetPosition.copyFrom(this.attachedNode.getAbsolutePosition());\r\n\r\n            // Detatch camera controls\r\n            if (this.detachCameraControls && this._scene.activeCamera && this._scene.activeCamera.inputs && !this._scene.activeCamera.leftCamera) {\r\n                if (this._scene.activeCamera.inputs.attachedToElement) {\r\n                    this._scene.activeCamera.detachControl();\r\n                    this._attachedToElement = true;\r\n                } else {\r\n                    this._attachedToElement = false;\r\n                }\r\n            }\r\n        } else {\r\n            this.releaseDrag();\r\n        }\r\n        PivotTools._RestorePivotPoint(this.attachedNode);\r\n    }\r\n\r\n    private _dragDelta = new Vector3();\r\n    protected _moveDrag(ray: Ray) {\r\n        this._moving = true;\r\n        const pickedPoint = this._pickWithRayOnDragPlane(ray);\r\n\r\n        if (pickedPoint) {\r\n            PivotTools._RemoveAndStorePivotPoint(this.attachedNode);\r\n\r\n            if (this.updateDragPlane) {\r\n                this._updateDragPlanePosition(ray, pickedPoint);\r\n            }\r\n            let dragLength = 0;\r\n            // depending on the drag mode option drag accordingly\r\n            if (this._options.dragAxis) {\r\n                // Convert local drag axis to world if useObjectOrientationForDragging\r\n                this.useObjectOrientationForDragging\r\n                    ? Vector3.TransformCoordinatesToRef(this._options.dragAxis, this.attachedNode.getWorldMatrix().getRotationMatrix(), this._worldDragAxis)\r\n                    : this._worldDragAxis.copyFrom(this._options.dragAxis);\r\n                // Project delta drag from the drag plane onto the drag axis\r\n                pickedPoint.subtractToRef(this.lastDragPosition, this._tmpVector);\r\n\r\n                this._worldDragAxis.normalize();\r\n                dragLength = Vector3.Dot(this._tmpVector, this._worldDragAxis);\r\n                this._worldDragAxis.scaleToRef(dragLength, this._dragDelta);\r\n            } else {\r\n                dragLength = this._dragDelta.length();\r\n                pickedPoint.subtractToRef(this.lastDragPosition, this._dragDelta);\r\n            }\r\n            this._targetPosition.addInPlace(this._dragDelta);\r\n            this.onDragObservable.notifyObservers({\r\n                dragDistance: dragLength,\r\n                delta: this._dragDelta,\r\n                dragPlanePoint: pickedPoint,\r\n                dragPlaneNormal: this._dragPlane.forward,\r\n                pointerId: this.currentDraggingPointerId,\r\n                pointerInfo: this._activePointerInfo,\r\n            });\r\n            this.lastDragPosition.copyFrom(pickedPoint);\r\n\r\n            PivotTools._RestorePivotPoint(this.attachedNode);\r\n        }\r\n    }\r\n\r\n    private _pickWithRayOnDragPlane(ray: Nullable<Ray>) {\r\n        if (!ray) {\r\n            return null;\r\n        }\r\n\r\n        // Calculate angle between plane normal and ray\r\n        let angle = Math.acos(Vector3.Dot(this._dragPlane.forward, ray.direction));\r\n        // Correct if ray is casted from oposite side\r\n        if (angle > Math.PI / 2) {\r\n            angle = Math.PI - angle;\r\n        }\r\n\r\n        // If the angle is too perpendicular to the plane pick another point on the plane where it is looking\r\n        if (this.maxDragAngle > 0 && angle > this.maxDragAngle) {\r\n            if (this._useAlternatePickedPointAboveMaxDragAngle) {\r\n                // Invert ray direction along the towards object axis\r\n                this._tmpVector.copyFrom(ray.direction);\r\n                this.attachedNode.absolutePosition.subtractToRef(ray.origin, this._alternatePickedPoint);\r\n                this._alternatePickedPoint.normalize();\r\n                this._alternatePickedPoint.scaleInPlace(this._useAlternatePickedPointAboveMaxDragAngleDragSpeed * Vector3.Dot(this._alternatePickedPoint, this._tmpVector));\r\n                this._tmpVector.addInPlace(this._alternatePickedPoint);\r\n\r\n                // Project resulting vector onto the drag plane and add it to the attached nodes absolute position to get a picked point\r\n                const dot = Vector3.Dot(this._dragPlane.forward, this._tmpVector);\r\n                this._dragPlane.forward.scaleToRef(-dot, this._alternatePickedPoint);\r\n                this._alternatePickedPoint.addInPlace(this._tmpVector);\r\n                this._alternatePickedPoint.addInPlace(this.attachedNode.absolutePosition);\r\n                return this._alternatePickedPoint;\r\n            } else {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        // use an infinite plane instead of ray picking a mesh that must be updated every frame\r\n        const planeNormal = this._dragPlane.forward;\r\n        const planePosition = this._dragPlane.position;\r\n        const dotProduct = ray.direction.dot(planeNormal);\r\n        if (Math.abs(dotProduct) < Epsilon) {\r\n            // Ray and plane are parallel, no intersection\r\n            return null;\r\n        }\r\n\r\n        planePosition.subtractToRef(ray.origin, TmpVectors.Vector3[0]);\r\n        const t = TmpVectors.Vector3[0].dot(planeNormal) / dotProduct;\r\n        // Ensure the intersection point is in front of the ray (t must be positive)\r\n        if (t < 0) {\r\n            // Intersection point is behind the ray\r\n            return null;\r\n        }\r\n\r\n        // Calculate the intersection point using the parameter t\r\n        ray.direction.scaleToRef(t, TmpVectors.Vector3[0]);\r\n        const intersectionPoint = ray.origin.add(TmpVectors.Vector3[0]);\r\n        return intersectionPoint;\r\n    }\r\n\r\n    // Variables to avoid instantiation in the below method\r\n    private _pointA = new Vector3(0, 0, 0);\r\n    private _pointC = new Vector3(0, 0, 0);\r\n    private _localAxis = new Vector3(0, 0, 0);\r\n    private _lookAt = new Vector3(0, 0, 0);\r\n    // Position the drag plane based on the attached mesh position, for single axis rotate the plane along the axis to face the camera\r\n    private _updateDragPlanePosition(ray: Ray, dragPlanePosition: Vector3) {\r\n        this._pointA.copyFrom(dragPlanePosition);\r\n        if (this._options.dragAxis) {\r\n            this.useObjectOrientationForDragging\r\n                ? Vector3.TransformCoordinatesToRef(this._options.dragAxis, this.attachedNode.getWorldMatrix().getRotationMatrix(), this._localAxis)\r\n                : this._localAxis.copyFrom(this._options.dragAxis);\r\n\r\n            // Calculate plane normal that is the cross product of local axis and (eye-dragPlanePosition)\r\n            ray.origin.subtractToRef(this._pointA, this._pointC);\r\n            this._pointC.normalize();\r\n            if (Math.abs(Vector3.Dot(this._localAxis, this._pointC)) > 0.999) {\r\n                // the drag axis is colinear with the (eye to position) ray. The cross product will give jittered values.\r\n                // A new axis vector need to be computed\r\n                if (Math.abs(Vector3.Dot(Vector3.UpReadOnly, this._pointC)) > 0.999) {\r\n                    this._lookAt.copyFrom(Vector3.Right());\r\n                } else {\r\n                    this._lookAt.copyFrom(Vector3.UpReadOnly);\r\n                }\r\n            } else {\r\n                Vector3.CrossToRef(this._localAxis, this._pointC, this._lookAt);\r\n                // Get perpendicular line from previous result and drag axis to adjust lineB to be perpendicular to camera\r\n                Vector3.CrossToRef(this._localAxis, this._lookAt, this._lookAt);\r\n                this._lookAt.normalize();\r\n            }\r\n\r\n            this._dragPlane.position.copyFrom(this._pointA);\r\n            this._pointA.addToRef(this._lookAt, this._lookAt);\r\n            this._dragPlane.lookAt(this._lookAt);\r\n        } else if (this._options.dragPlaneNormal) {\r\n            this.useObjectOrientationForDragging\r\n                ? Vector3.TransformCoordinatesToRef(this._options.dragPlaneNormal, this.attachedNode.getWorldMatrix().getRotationMatrix(), this._localAxis)\r\n                : this._localAxis.copyFrom(this._options.dragPlaneNormal);\r\n            this._dragPlane.position.copyFrom(this._pointA);\r\n            this._pointA.addToRef(this._localAxis, this._lookAt);\r\n            this._dragPlane.lookAt(this._lookAt);\r\n        } else {\r\n            if (this._scene.activeCamera) {\r\n                this._scene.activeCamera.getForwardRay().direction.normalizeToRef(this._localAxis);\r\n            }\r\n            this._dragPlane.position.copyFrom(this._pointA);\r\n            this._dragPlane.lookAt(this._pointA.add(this._localAxis));\r\n        }\r\n        // Update the position of the drag plane so it doesn't get out of sync with the node (eg. when moving back and forth quickly)\r\n        this._dragPlane.position.copyFrom(this.attachedNode.getAbsolutePosition());\r\n\r\n        this._dragPlane.computeWorldMatrix(true);\r\n    }\r\n\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        this._lastPointerRay = {};\r\n        if (this.attachedNode) {\r\n            this.attachedNode.isNearGrabbable = false;\r\n        }\r\n        if (this._pointerObserver) {\r\n            this._scene.onPointerObservable.remove(this._pointerObserver);\r\n        }\r\n        if (this._beforeRenderObserver) {\r\n            this._scene.onBeforeRenderObservable.remove(this._beforeRenderObserver);\r\n        }\r\n        if (this._dragPlane) {\r\n            this._dragPlane.dispose();\r\n        }\r\n        this.releaseDrag();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAGpC,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAE9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAC;AAGjE,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;;;;;;;;;;AAK/C,MAAO,mBAAmB;IA0B5B;;;OAGG,CACH,gEAAgE;IAChE,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IACD,gEAAgE;IAChE,IAAW,wBAAwB,CAAC,wBAAgC,EAAA;QAChE,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;IAC7D,CAAC;IAiED;;OAEG,CACH,IAAW,OAAO,CAAC,KAAc,EAAA;QAC7B,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzB,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAyBD;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,CAAC,OAA0D,EAAA;QACzE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAED;;;;;OAKG,CACH,YAAY,OAA2D,CAAA;QApJ/D,IAAA,CAAA,kDAAkD,GAAG,CAAC,GAAG,CAAC;QAC1D,IAAA,CAAA,iBAAiB,GAAW,CAAC,CAAC,CAAC;QAEvC;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACxB;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG;YAAC,CAAC;YAAE,CAAC;YAAE,CAAC;SAAC,CAAC;QAC/B;;WAEG,CACI,IAAA,CAAA,yCAAyC,GAAG,KAAK,CAAC;QAazD;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,CAAC,CAAC,CAAC;QAKrC;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QACxB;;WAEG,CACI,IAAA,CAAA,cAAc,GAAG,GAAG,CAAC;QAC5B;;WAEG,CACI,IAAA,CAAA,eAAe,GAAG,IAAI,CAAC;QAC9B,iEAAiE;QACzD,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QACnB,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QACxB;;;;;;;;WAQG,CACI,IAAA,CAAA,gBAAgB,GAAG,8JAAI,aAAU,EAOpC,CAAC;QACL;;;;;WAKG,CACI,IAAA,CAAA,qBAAqB,GAAG,8JAAI,aAAU,EAAsF,CAAC;QACpI;;;;;WAKG,CACI,IAAA,CAAA,mBAAmB,GAAG,8JAAI,aAAU,EAAsF,CAAC;QAClI;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,IAAI,uKAAU,EAAW,CAAC;QAEvD;;WAEG,CACI,IAAA,CAAA,YAAY,GAAG,IAAI,CAAC;QAenB,IAAA,CAAA,QAAQ,GAAG,IAAI,CAAC;QAExB;;WAEG,CACI,IAAA,CAAA,kCAAkC,GAAG,IAAI,CAAC;QACjD;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG,CACI,IAAA,CAAA,+BAA+B,GAAG,IAAI,CAAC;QAE9C;;;WAGG,CACI,IAAA,CAAA,2BAA2B,GAAG,KAAK,CAAC;QAwC3C;;;;;WAKG,CACH,6DAA6D;QACtD,IAAA,CAAA,YAAY,GAAG,CAAC,MAAe,EAAE,EAAE;YACtC,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC;QAcM,IAAA,CAAA,UAAU,GAAG,IAAI,yKAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,IAAA,CAAA,qBAAqB,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,IAAA,CAAA,cAAc,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,IAAA,CAAA,eAAe,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,IAAA,CAAA,kBAAkB,GAAY,KAAK,CAAC;QAyKpC,IAAA,CAAA,aAAa,GAAG,kKAAI,MAAG,CAAC,mKAAI,UAAO,EAAE,EAAE,mKAAI,UAAO,EAAE,CAAC,CAAC;QACtD,IAAA,CAAA,eAAe,GAA2B,CAAA,CAAE,CAAC;QA8D7C,IAAA,CAAA,UAAU,GAAG,mKAAI,UAAO,EAAE,CAAC;QAmGnC,uDAAuD;QAC/C,IAAA,CAAA,OAAO,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,IAAA,CAAA,OAAO,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,IAAA,CAAA,UAAU,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,IAAA,CAAA,OAAO,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAzXnC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC;QAEvC,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACzB,WAAW,EAAE,CAAC;QAClB,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;YAChC,WAAW,EAAE,CAAC;QAClB,CAAC;QACD,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YAClB,4CAA4C;YAC5C,MAAM,0EAA0E,CAAC;QACrF,CAAC;IACL,CAAC;IAaD;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG,CACI,IAAI,GAAA,CAAI,CAAC;IAOhB;;;;OAIG,CACI,MAAM,CAAC,SAAuB,EAAE,SAAwC,EAAA;QAC3E,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACnC,SAAS,CAAC,eAAe,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAE9B,6DAA6D;QAC7D,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;YAClD,CAAC,MAAM,CAAC;gBACJ,mBAAmB,CAAC,WAAW,GAAG,iJAAI,QAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;oBAAE,OAAO,EAAE,IAAI;gBAAA,CAAE,CAAC,CAAC;gBACxF,mBAAmB,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;gBAChD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;oBACzC,mBAAmB,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oBACpC,mBAAmB,CAAC,WAAY,GAAG,IAAI,CAAC;gBAClD,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,4LAAA,AAAW,EACzB,kBAAkB,EAClB;YAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;YAAE,SAAS,EAAE,KAAK;YAAE,eAAe,wJAAE,OAAI,CAAC,UAAU;QAAA,CAAE,EACzF,mBAAmB,CAAC,WAAW,CAClC,CAAC;QAEF,oBAAoB;QACpB,IAAI,CAAC,gBAAgB,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7C,MAAM,aAAa,GAAG,SAAS,GACzB,SAAS,GACT,CAAC,CAAe,EAAE,EAAE;YAChB,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzE,CAAC,CAAC;QAER,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACxE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,0EAA0E;gBAC1E,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,IAAI,CAAC,WAAW,EAAE,CAAC;gBACvB,CAAC;gBAED,OAAO;YACX,CAAC;YAED,uGAAuG;YACvG,0DAA0D;YAC1D,iCAAiC;YACjC,IACI,IAAI,CAAC,QAAQ,IACb,IAAI,CAAC,wBAAwB,IAAoB,WAAW,CAAC,KAAM,CAAC,SAAS,IAC7E,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,IAC/B,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,iBAAiB,IACnD,CAAC,IAAI,CAAC,2BAA2B,EACnC,CAAC;gBACC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,OAAO;YACX,CAAC;YAED,IAAI,WAAW,CAAC,IAAI,mKAAI,oBAAiB,CAAC,WAAW,EAAE,CAAC;gBACpD,IACI,IAAI,CAAC,kCAAkC,IACvC,CAAC,IAAI,CAAC,QAAQ,IACd,WAAW,CAAC,QAAQ,IACpB,WAAW,CAAC,QAAQ,CAAC,GAAG,IACxB,WAAW,CAAC,QAAQ,CAAC,UAAU,IAC/B,WAAW,CAAC,QAAQ,CAAC,WAAW,IAChC,WAAW,CAAC,QAAQ,CAAC,GAAG,IACxB,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAChD,CAAC;oBACC,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBAC7F,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC;wBAClD,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;wBACtC,IAAI,CAAC,UAAU,CAAiB,WAAW,CAAC,KAAM,CAAC,SAAS,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBAC9H,CAAC;gBACL,CAAC;YACL,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,mKAAI,oBAAiB,CAAC,SAAS,EAAE,CAAC;gBACzD,IACI,IAAI,CAAC,kCAAkC,IACvC,IAAI,CAAC,wBAAwB,IAAoB,WAAW,CAAC,KAAM,CAAC,SAAS,IAC7E,CAAC,IAAI,CAAC,iBAAiB,KAAK,WAAW,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,CAAC,EACxF,CAAC;oBACC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACvB,CAAC;YACL,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,mKAAI,oBAAiB,CAAC,WAAW,EAAE,CAAC;gBAC3D,MAAM,SAAS,GAAmB,WAAW,CAAC,KAAM,CAAC,SAAS,CAAC;gBAE/D,4FAA4F;gBAC5F,IAAI,IAAI,CAAC,wBAAwB,KAAK,mBAAmB,CAAC,WAAW,IAAI,SAAS,KAAK,mBAAmB,CAAC,WAAW,EAAE,CAAC;oBACrH,MAAM,GAAG,GAAkB,WAAW,CAAC,KAAK,CAAC;oBAC7C,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,KAAK,OAAO,IAAI,AAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,QAAQ,IAAI,GAAG,YAAY,UAAU,CAAC,CAAC;oBACrI,IAAI,YAAY,EAAE,CAAC;wBACf,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC;4BACtD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;4BACtF,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;wBAC/D,CAAC;wBACD,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;oBAC9C,CAAC;gBACL,CAAC;gBAED,6FAA6F;gBAC7F,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;oBACnC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,kKAAI,MAAG,CAAC,mKAAI,UAAO,EAAE,EAAE,mKAAI,UAAO,EAAE,CAAC,CAAC;gBAC5E,CAAC;gBACD,IAAI,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;oBACnD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACjF,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAEvF,IAAI,IAAI,CAAC,wBAAwB,IAAI,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAC9D,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBAC7C,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACvE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpC,IAAI,gBAAgB,GAAG,KAAK,CAAC;gBAC7B,uKAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACxD,mCAAmC;gBACnC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACxF,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAClD,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACnF,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBACrC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACvD,gBAAgB,GAAG,IAAI,CAAC;gBAC5B,CAAC;0KACD,aAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjD,IAAI,gBAAgB,EAAE,CAAC;oBACnB,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;gBAC3C,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,WAAW,GAAA;QACd,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC;gBAAE,cAAc,EAAE,IAAI,CAAC,gBAAgB;gBAAE,SAAS,EAAE,IAAI,CAAC,wBAAwB;gBAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB;YAAA,CAAE,CAAC,CAAC;QACxK,CAAC;QAED,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,2BAA2B;QAC3B,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YAC3H,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,iBAAiB,EAAE,CAAC;gBAChE,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,YAA+B,CAAC;gBACpE,eAAe,CAAC,aAAa,CACzB,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,EACvE,eAAe,CAAC,kBAAkB,EAClC,eAAe,CAAC,mBAAmB,CACtC,CAAC;YACN,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACtI,CAAC;YACD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACpC,CAAC;IACL,CAAC;IAID;;;;;OAKG,CACI,SAAS,CAAC,YAAoB,mBAAmB,CAAC,WAAW,EAAE,OAAa,EAAE,gBAA0B,EAAA;QAC3G,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAEtD,IAAI,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,SAAS,KAAK,mBAAmB,CAAC,WAAW,EAAE,CAAC;YAChD,OAAO,GAAG,IAAI,CAAC,eAAe,CAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,wDAAwD;YACxD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;IACL,CAAC;IAES,UAAU,CAAC,SAAiB,EAAE,OAAa,EAAE,gBAA0B,EAAA;QAC7E,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACnE,OAAO;QACX,CAAC;kKAED,aAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxD,iDAAiD;QACjD,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACtE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzG,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrE,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;YAC1C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC5C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC;gBAAE,cAAc,EAAE,WAAW;gBAAE,SAAS,EAAE,IAAI,CAAC,wBAAwB;gBAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB;YAAA,CAAE,CAAC,CAAC;YAC5J,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAEvE,0BAA0B;YAC1B,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBACnI,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;oBACpD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;oBACzC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBACnC,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;gBACpC,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;kKACD,aAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACrD,CAAC;IAGS,SAAS,CAAC,GAAQ,EAAA;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;QAEtD,IAAI,WAAW,EAAE,CAAC;sKACd,aAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAExD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,qDAAqD;YACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACzB,sEAAsE;gBACtE,IAAI,CAAC,+BAA+B,GAC9B,yKAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,GACtI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC3D,4DAA4D;gBAC5D,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBAElE,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;gBAChC,UAAU,GAAG,yKAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC/D,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAChE,CAAC,MAAM,CAAC;gBACJ,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBACtC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACtE,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjD,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBAClC,YAAY,EAAE,UAAU;gBACxB,KAAK,EAAE,IAAI,CAAC,UAAU;gBACtB,cAAc,EAAE,WAAW;gBAC3B,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO;gBACxC,SAAS,EAAE,IAAI,CAAC,wBAAwB;gBACxC,WAAW,EAAE,IAAI,CAAC,kBAAkB;aACvC,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;sKAE5C,aAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,GAAkB,EAAA;QAC9C,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,+CAA+C;QAC/C,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,gKAAC,UAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;QAC3E,6CAA6C;QAC7C,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;YACtB,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;QAC5B,CAAC;QAED,qGAAqG;QACrG,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACrD,IAAI,IAAI,CAAC,yCAAyC,EAAE,CAAC;gBACjD,qDAAqD;gBACrD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACxC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACzF,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,CAAC;gBACvC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,IAAI,CAAC,kDAAkD,kKAAG,UAAO,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC5J,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAEvD,wHAAwH;gBACxH,MAAM,GAAG,kKAAG,UAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACrE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACvD,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;gBAC1E,OAAO,IAAI,CAAC,qBAAqB,CAAC;YACtC,CAAC,MAAM,CAAC;gBACJ,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,uFAAuF;QACvF,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC/C,MAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,qKAAG,UAAO,EAAE,CAAC;YACjC,8CAA8C;YAC9C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAM,CAAC,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC;QAC9D,4EAA4E;QAC5E,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACR,uCAAuC;YACvC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,yDAAyD;QACzD,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,iKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,iBAAiB,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,gKAAC,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAOD,kIAAkI;IAC1H,wBAAwB,CAAC,GAAQ,EAAE,iBAA0B,EAAA;QACjE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACzB,IAAI,CAAC,+BAA+B,kKAC9B,UAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,GAClI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEvD,6FAA6F;YAC7F,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,GAAG,gKAAC,UAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;gBAC/D,yGAAyG;gBACzG,wCAAwC;gBACxC,IAAI,IAAI,CAAC,GAAG,CAAC,yKAAO,CAAC,GAAG,gKAAC,UAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;oBAClE,IAAI,CAAC,OAAO,CAAC,QAAQ,gKAAC,UAAO,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC3C,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,OAAO,CAAC,QAAQ,gKAAC,UAAO,CAAC,UAAU,CAAC,CAAC;gBAC9C,CAAC;YACL,CAAC,MAAM,CAAC;+KACJ,UAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,0GAA0G;+KAC1G,UAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC7B,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAClD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;YACvC,IAAI,CAAC,+BAA+B,kKAC9B,UAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,GACzI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YAC9D,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvF,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9D,CAAC;QACD,6HAA6H;QAC7H,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAE3E,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,eAAe,GAAG,CAAA,CAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,KAAK,CAAC;QAC9C,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC5E,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;;AA5lBc,oBAAA,WAAW,GAAG,CAAC,CAAC,AAAL,CAAM", "debugId": null}}, {"offset": {"line": 1579, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/multiPointerScaleBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/multiPointerScaleBehavior.ts"], "sourcesContent": ["import type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Behavior } from \"../behavior\";\r\nimport { PointerDragBehavior } from \"./pointerDragBehavior\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Scene } from \"../../scene\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will allow the mesh to be scaled\r\n */\r\nexport class MultiPointerScaleBehavior implements Behavior<Mesh> {\r\n    private _dragBehaviorA: PointerDragBehavior;\r\n    private _dragBehaviorB: PointerDragBehavior;\r\n    private _startDistance = 0;\r\n    private _initialScale = new Vector3(0, 0, 0);\r\n    private _targetScale = new Vector3(0, 0, 0);\r\n    private _ownerNode: Mesh;\r\n    private _sceneRenderObserver: Nullable<Observer<Scene>> = null;\r\n\r\n    /**\r\n     * Instantiate a new behavior that when attached to a mesh will allow the mesh to be scaled\r\n     */\r\n    constructor() {\r\n        this._dragBehaviorA = new PointerDragBehavior({});\r\n        this._dragBehaviorA.moveAttached = false;\r\n        this._dragBehaviorB = new PointerDragBehavior({});\r\n        this._dragBehaviorB.moveAttached = false;\r\n    }\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"MultiPointerScale\";\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    private _getCurrentDistance() {\r\n        return this._dragBehaviorA.lastDragPosition.subtract(this._dragBehaviorB.lastDragPosition).length();\r\n    }\r\n\r\n    /**\r\n     * Attaches the scale behavior the passed in mesh\r\n     * @param ownerNode The mesh that will be scaled around once attached\r\n     */\r\n    public attach(ownerNode: Mesh): void {\r\n        this._ownerNode = ownerNode;\r\n\r\n        // Create 2 drag behaviors such that each will only be triggered by a separate pointer\r\n        this._dragBehaviorA.onDragStartObservable.add(() => {\r\n            if (this._dragBehaviorA.dragging && this._dragBehaviorB.dragging) {\r\n                if (this._dragBehaviorA.currentDraggingPointerId == this._dragBehaviorB.currentDraggingPointerId) {\r\n                    this._dragBehaviorA.releaseDrag();\r\n                } else {\r\n                    this._initialScale.copyFrom(ownerNode.scaling);\r\n                    this._startDistance = this._getCurrentDistance();\r\n                }\r\n            }\r\n        });\r\n        this._dragBehaviorB.onDragStartObservable.add(() => {\r\n            if (this._dragBehaviorA.dragging && this._dragBehaviorB.dragging) {\r\n                if (this._dragBehaviorA.currentDraggingPointerId == this._dragBehaviorB.currentDraggingPointerId) {\r\n                    this._dragBehaviorB.releaseDrag();\r\n                } else {\r\n                    this._initialScale.copyFrom(ownerNode.scaling);\r\n                    this._startDistance = this._getCurrentDistance();\r\n                }\r\n            }\r\n        });\r\n\r\n        // Once both drag behaviors are active scale based on the distance between the two pointers\r\n        const dragBehaviors = [this._dragBehaviorA, this._dragBehaviorB];\r\n        for (const behavior of dragBehaviors) {\r\n            behavior.onDragObservable.add(() => {\r\n                if (this._dragBehaviorA.dragging && this._dragBehaviorB.dragging) {\r\n                    const ratio = this._getCurrentDistance() / this._startDistance;\r\n                    this._initialScale.scaleToRef(ratio, this._targetScale);\r\n                }\r\n            });\r\n        }\r\n\r\n        ownerNode.addBehavior(this._dragBehaviorA);\r\n        ownerNode.addBehavior(this._dragBehaviorB);\r\n\r\n        // On every frame move towards target scaling to avoid jitter caused by vr controllers\r\n        this._sceneRenderObserver = ownerNode.getScene().onBeforeRenderObservable.add(() => {\r\n            if (this._dragBehaviorA.dragging && this._dragBehaviorB.dragging) {\r\n                const change = this._targetScale.subtract(ownerNode.scaling).scaleInPlace(0.1);\r\n                if (change.length() > 0.01) {\r\n                    ownerNode.scaling.addInPlace(change);\r\n                }\r\n            }\r\n        });\r\n    }\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        this._ownerNode.getScene().onBeforeRenderObservable.remove(this._sceneRenderObserver);\r\n        const dragBehaviors = [this._dragBehaviorA, this._dragBehaviorB];\r\n        for (const behavior of dragBehaviors) {\r\n            behavior.onDragStartObservable.clear();\r\n            behavior.onDragObservable.clear();\r\n            this._ownerNode.removeBehavior(behavior);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;;;AAQ5C,MAAO,yBAAyB;IASlC;;OAEG,CACH,aAAA;QATQ,IAAA,CAAA,cAAc,GAAG,CAAC,CAAC;QACnB,IAAA,CAAA,aAAa,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,IAAA,CAAA,YAAY,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEpC,IAAA,CAAA,oBAAoB,GAA8B,IAAI,CAAC;QAM3D,IAAI,CAAC,cAAc,GAAG,sLAAI,sBAAmB,CAAC,CAAA,CAAE,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,KAAK,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,sLAAI,sBAAmB,CAAC,CAAA,CAAE,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,KAAK,CAAC;IAC7C,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;OAEG,CACI,IAAI,GAAA,CAAI,CAAC;IAER,mBAAmB,GAAA;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,MAAM,EAAE,CAAC;IACxG,CAAC;IAED;;;OAGG,CACI,MAAM,CAAC,SAAe,EAAA;QACzB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAE5B,sFAAsF;QACtF,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/C,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;gBAC/D,IAAI,IAAI,CAAC,cAAc,CAAC,wBAAwB,IAAI,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,CAAC;oBAC/F,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;gBACtC,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC/C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACrD,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/C,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;gBAC/D,IAAI,IAAI,CAAC,cAAc,CAAC,wBAAwB,IAAI,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,CAAC;oBAC/F,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;gBACtC,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;oBAC/C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACrD,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,2FAA2F;QAC3F,MAAM,aAAa,GAAG;YAAC,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,cAAc;SAAC,CAAC;QACjE,KAAK,MAAM,QAAQ,IAAI,aAAa,CAAE,CAAC;YACnC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC/B,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;oBAC/D,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;oBAC/D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC5D,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;QAED,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3C,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAE3C,sFAAsF;QACtF,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/E,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;gBAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC/E,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC;oBACzB,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACzC,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IACD;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACtF,MAAM,aAAa,GAAG;YAAC,IAAI,CAAC,cAAc;YAAE,IAAI,CAAC,cAAc;SAAC,CAAC;QACjE,KAAK,MAAM,QAAQ,IAAI,aAAa,CAAE,CAAC;YACnC,QAAQ,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;YACvC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/baseSixDofDragBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/baseSixDofDragBehavior.ts"], "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { Vector3, Quaternion, TmpVectors } from \"../../Maths/math.vector\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { PickingInfo } from \"../../Collisions/pickingInfo\";\r\nimport { Camera } from \"../../Cameras/camera\";\r\nimport type { Ray } from \"../../Culling/ray\";\r\nimport type { IPointerEvent } from \"../../Events/deviceInputEvents\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\n\r\n/**\r\n * Data store to track virtual pointers movement\r\n */\r\ntype VirtualMeshInfo = {\r\n    dragging: boolean;\r\n    moving: boolean;\r\n    dragMesh: TransformNode;\r\n    originMesh: TransformNode;\r\n    pivotMesh: TransformNode;\r\n    startingPivotPosition: Vector3;\r\n    startingPivotOrientation: Quaternion;\r\n    startingPosition: Vector3;\r\n    startingOrientation: Quaternion;\r\n    lastOriginPosition: Vector3;\r\n    lastDragPosition: Vector3;\r\n};\r\n\r\n/**\r\n * Base behavior for six degrees of freedom interactions in XR experiences.\r\n * Creates virtual meshes that are dragged around\r\n * And observables for position/rotation changes\r\n */\r\nexport class BaseSixDofDragBehavior implements Behavior<Mesh> {\r\n    protected static _VirtualScene: Scene;\r\n    private _pointerObserver: Nullable<Observer<PointerInfo>>;\r\n    private _attachedToElement: boolean = false;\r\n    protected _virtualMeshesInfo: {\r\n        [id: number]: VirtualMeshInfo;\r\n    } = {};\r\n\r\n    private _tmpVector: Vector3 = new Vector3();\r\n    private _tmpQuaternion: Quaternion = new Quaternion();\r\n\r\n    protected _dragType = {\r\n        NONE: 0,\r\n        DRAG: 1,\r\n        DRAG_WITH_CONTROLLER: 2,\r\n        NEAR_DRAG: 3,\r\n    };\r\n\r\n    protected _scene: Scene;\r\n    protected _moving = false;\r\n    protected _ownerNode: TransformNode;\r\n    protected _dragging = this._dragType.NONE;\r\n\r\n    /**\r\n     * The list of child meshes that can receive drag events\r\n     * If `null`, all child meshes will receive drag event\r\n     */\r\n    public draggableMeshes: Nullable<AbstractMesh[]> = null;\r\n\r\n    /**\r\n     * How much faster the object should move when the controller is moving towards it. This is useful to bring objects that are far away from the user to them faster. Set this to 0 to avoid any speed increase. (Default: 3)\r\n     */\r\n    public zDragFactor = 3;\r\n    /**\r\n     * The id of the pointer that is currently interacting with the behavior (-1 when no pointer is active)\r\n     */\r\n    public get currentDraggingPointerId() {\r\n        if (this.currentDraggingPointerIds[0] !== undefined) {\r\n            return this.currentDraggingPointerIds[0];\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    public set currentDraggingPointerId(value: number) {\r\n        this.currentDraggingPointerIds[0] = value;\r\n    }\r\n\r\n    /**\r\n     * In case of multipointer interaction, all pointer ids currently active are stored here\r\n     */\r\n    public currentDraggingPointerIds: number[] = [];\r\n\r\n    /**\r\n     * Get or set the currentDraggingPointerId\r\n     * @deprecated Please use currentDraggingPointerId instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get currentDraggingPointerID(): number {\r\n        return this.currentDraggingPointerId;\r\n    }\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public set currentDraggingPointerID(currentDraggingPointerID: number) {\r\n        this.currentDraggingPointerId = currentDraggingPointerID;\r\n    }\r\n    /**\r\n    /**\r\n     * If camera controls should be detached during the drag\r\n     */\r\n    public detachCameraControls = true;\r\n\r\n    /**\r\n     * Fires each time a drag starts\r\n     */\r\n    public onDragStartObservable = new Observable<{ position: Vector3 }>();\r\n    /**\r\n     * Fires each time a drag happens\r\n     */\r\n    public onDragObservable = new Observable<{ delta: Vector3; position: Vector3; pickInfo: PickingInfo }>();\r\n    /**\r\n     *  Fires each time a drag ends (eg. mouse release after drag)\r\n     */\r\n    public onDragEndObservable = new Observable<unknown>();\r\n\r\n    /**\r\n     * Should the behavior allow simultaneous pointers to interact with the owner node.\r\n     */\r\n    public allowMultiPointer: boolean = true;\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"BaseSixDofDrag\";\r\n    }\r\n\r\n    /**\r\n     *  Returns true if the attached mesh is currently moving with this behavior\r\n     */\r\n    public get isMoving(): boolean {\r\n        return this._moving;\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    /**\r\n     * In the case of multiple active cameras, the cameraToUseForPointers should be used if set instead of active camera\r\n     */\r\n    private get _pointerCamera() {\r\n        if (this._scene.cameraToUseForPointers) {\r\n            return this._scene.cameraToUseForPointers;\r\n        } else {\r\n            return this._scene.activeCamera;\r\n        }\r\n    }\r\n\r\n    private _createVirtualMeshInfo() {\r\n        // Setup virtual meshes to be used for dragging without dirtying the existing scene\r\n\r\n        const dragMesh = new TransformNode(\"\", BaseSixDofDragBehavior._VirtualScene);\r\n        dragMesh.rotationQuaternion = new Quaternion();\r\n        const originMesh = new TransformNode(\"\", BaseSixDofDragBehavior._VirtualScene);\r\n        originMesh.rotationQuaternion = new Quaternion();\r\n        const pivotMesh = new TransformNode(\"\", BaseSixDofDragBehavior._VirtualScene);\r\n        pivotMesh.rotationQuaternion = new Quaternion();\r\n\r\n        return {\r\n            dragging: false,\r\n            moving: false,\r\n            dragMesh,\r\n            originMesh,\r\n            pivotMesh,\r\n            startingPivotPosition: new Vector3(),\r\n            startingPivotOrientation: new Quaternion(),\r\n            startingPosition: new Vector3(),\r\n            startingOrientation: new Quaternion(),\r\n            lastOriginPosition: new Vector3(),\r\n            lastDragPosition: new Vector3(),\r\n        };\r\n    }\r\n\r\n    protected _resetVirtualMeshesPosition() {\r\n        for (let i = 0; i < this.currentDraggingPointerIds.length; i++) {\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].pivotMesh.position.copyFrom(this._ownerNode.getAbsolutePivotPoint());\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].pivotMesh.rotationQuaternion!.copyFrom(this._ownerNode.rotationQuaternion!);\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].startingPivotPosition.copyFrom(\r\n                this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].pivotMesh.position\r\n            );\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].startingPivotOrientation.copyFrom(\r\n                this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].pivotMesh.rotationQuaternion!\r\n            );\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].startingPosition.copyFrom(this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].dragMesh.position);\r\n            this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].startingOrientation.copyFrom(\r\n                this._virtualMeshesInfo[this.currentDraggingPointerIds[i]].dragMesh.rotationQuaternion!\r\n            );\r\n        }\r\n    }\r\n\r\n    private _pointerUpdate2D(ray: Ray, pointerId: number, zDragFactor: number) {\r\n        if (this._pointerCamera && this._pointerCamera.cameraRigMode == Camera.RIG_MODE_NONE && !this._pointerCamera._isLeftCamera && !this._pointerCamera._isRightCamera) {\r\n            ray.origin.copyFrom(this._pointerCamera.globalPosition);\r\n            zDragFactor = 0;\r\n        }\r\n\r\n        const virtualMeshesInfo = this._virtualMeshesInfo[pointerId];\r\n\r\n        // Calculate controller drag distance in controller space\r\n        const originDragDifference = TmpVectors.Vector3[11];\r\n        ray.origin.subtractToRef(virtualMeshesInfo.lastOriginPosition, originDragDifference);\r\n        virtualMeshesInfo.lastOriginPosition.copyFrom(ray.origin);\r\n        const localOriginDragDifference = -Vector3.Dot(originDragDifference, ray.direction);\r\n\r\n        virtualMeshesInfo.originMesh.addChild(virtualMeshesInfo.dragMesh);\r\n        virtualMeshesInfo.originMesh.addChild(virtualMeshesInfo.pivotMesh);\r\n\r\n        this._applyZOffset(virtualMeshesInfo.dragMesh, localOriginDragDifference, zDragFactor);\r\n        this._applyZOffset(virtualMeshesInfo.pivotMesh, localOriginDragDifference, zDragFactor);\r\n\r\n        // Update the controller position\r\n        virtualMeshesInfo.originMesh.position.copyFrom(ray.origin);\r\n        const lookAt = TmpVectors.Vector3[10];\r\n        ray.origin.addToRef(ray.direction, lookAt);\r\n        virtualMeshesInfo.originMesh.lookAt(lookAt);\r\n\r\n        virtualMeshesInfo.originMesh.removeChild(virtualMeshesInfo.dragMesh);\r\n        virtualMeshesInfo.originMesh.removeChild(virtualMeshesInfo.pivotMesh);\r\n    }\r\n\r\n    private _pointerUpdateXR(controllerAimTransform: TransformNode, controllerGripTransform: Nullable<TransformNode>, pointerId: number, zDragFactor: number) {\r\n        const virtualMeshesInfo = this._virtualMeshesInfo[pointerId];\r\n        virtualMeshesInfo.originMesh.position.copyFrom(controllerAimTransform.position);\r\n        if (this._dragging === this._dragType.NEAR_DRAG && controllerGripTransform) {\r\n            virtualMeshesInfo.originMesh.rotationQuaternion!.copyFrom(controllerGripTransform.rotationQuaternion!);\r\n        } else {\r\n            virtualMeshesInfo.originMesh.rotationQuaternion!.copyFrom(controllerAimTransform.rotationQuaternion!);\r\n        }\r\n\r\n        virtualMeshesInfo.pivotMesh.computeWorldMatrix(true);\r\n        virtualMeshesInfo.dragMesh.computeWorldMatrix(true);\r\n\r\n        // Z scaling logic\r\n        if (zDragFactor !== 0) {\r\n            // Camera.getForwardRay modifies TmpVectors.Vector[0-3], so cache it in advance\r\n            const cameraForwardVec = TmpVectors.Vector3[10];\r\n            const originDragDirection = TmpVectors.Vector3[11];\r\n            cameraForwardVec.copyFrom(this._pointerCamera!.getForwardRay().direction);\r\n            virtualMeshesInfo.originMesh.position.subtractToRef(virtualMeshesInfo.lastOriginPosition, originDragDirection);\r\n            virtualMeshesInfo.lastOriginPosition.copyFrom(virtualMeshesInfo.originMesh.position);\r\n            const controllerDragDistance = originDragDirection.length();\r\n            originDragDirection.normalize();\r\n\r\n            const cameraToDrag = TmpVectors.Vector3[12];\r\n            const controllerToDrag = TmpVectors.Vector3[9];\r\n            virtualMeshesInfo.dragMesh.absolutePosition.subtractToRef(this._pointerCamera!.globalPosition, cameraToDrag);\r\n            virtualMeshesInfo.dragMesh.absolutePosition.subtractToRef(virtualMeshesInfo.originMesh.position, controllerToDrag);\r\n            const controllerToDragDistance = controllerToDrag.length();\r\n            cameraToDrag.normalize();\r\n            controllerToDrag.normalize();\r\n\r\n            const controllerDragScaling = Math.abs(Vector3.Dot(originDragDirection, controllerToDrag)) * Vector3.Dot(originDragDirection, cameraForwardVec);\r\n            let zOffsetScaling = controllerDragScaling * zDragFactor * controllerDragDistance * controllerToDragDistance;\r\n\r\n            // Prevent pulling the mesh through the controller\r\n            const minDistanceFromControllerToDragMesh = 0.01;\r\n            if (zOffsetScaling < 0 && minDistanceFromControllerToDragMesh - controllerToDragDistance > zOffsetScaling) {\r\n                zOffsetScaling = Math.min(minDistanceFromControllerToDragMesh - controllerToDragDistance, 0);\r\n            }\r\n            controllerToDrag.scaleInPlace(zOffsetScaling);\r\n\r\n            controllerToDrag.addToRef(virtualMeshesInfo.pivotMesh.absolutePosition, this._tmpVector);\r\n            virtualMeshesInfo.pivotMesh.setAbsolutePosition(this._tmpVector);\r\n            controllerToDrag.addToRef(virtualMeshesInfo.dragMesh.absolutePosition, this._tmpVector);\r\n            virtualMeshesInfo.dragMesh.setAbsolutePosition(this._tmpVector);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Attaches the scale behavior the passed in mesh\r\n     * @param ownerNode The mesh that will be scaled around once attached\r\n     */\r\n    public attach(ownerNode: TransformNode): void {\r\n        this._ownerNode = ownerNode;\r\n        this._scene = this._ownerNode.getScene();\r\n        if (!BaseSixDofDragBehavior._VirtualScene) {\r\n            BaseSixDofDragBehavior._VirtualScene = new Scene(this._scene.getEngine(), { virtual: true });\r\n            BaseSixDofDragBehavior._VirtualScene.detachControl();\r\n        }\r\n\r\n        const pickPredicate = (m: AbstractMesh) => {\r\n            return this._ownerNode === m || (m.isDescendantOf(this._ownerNode) && (!this.draggableMeshes || this.draggableMeshes.indexOf(m) !== -1));\r\n        };\r\n\r\n        this._pointerObserver = this._scene.onPointerObservable.add((pointerInfo) => {\r\n            const pointerId = (<IPointerEvent>pointerInfo.event).pointerId;\r\n            if (!this._virtualMeshesInfo[pointerId]) {\r\n                this._virtualMeshesInfo[pointerId] = this._createVirtualMeshInfo();\r\n            }\r\n            const virtualMeshesInfo = this._virtualMeshesInfo[pointerId];\r\n            const isXRPointer = (<IPointerEvent>pointerInfo.event).pointerType === \"xr-near\" || (<IPointerEvent>pointerInfo.event).pointerType === \"xr\";\r\n            const isNearXRPointer = (<IPointerEvent>pointerInfo.event).pointerType === \"xr-near\";\r\n            if (pointerInfo.type == PointerEventTypes.POINTERDOWN) {\r\n                if (\r\n                    !virtualMeshesInfo.dragging &&\r\n                    pointerInfo.pickInfo &&\r\n                    pointerInfo.pickInfo.hit &&\r\n                    pointerInfo.pickInfo.pickedMesh &&\r\n                    pointerInfo.pickInfo.pickedPoint &&\r\n                    pointerInfo.pickInfo.ray &&\r\n                    (!isNearXRPointer || pointerInfo.pickInfo.aimTransform) &&\r\n                    pickPredicate(pointerInfo.pickInfo.pickedMesh)\r\n                ) {\r\n                    if ((!this.allowMultiPointer || isXRPointer) && this.currentDraggingPointerIds.length > 0) {\r\n                        return;\r\n                    }\r\n\r\n                    if (\r\n                        this._pointerCamera &&\r\n                        this._pointerCamera.cameraRigMode === Camera.RIG_MODE_NONE &&\r\n                        !this._pointerCamera._isLeftCamera &&\r\n                        !this._pointerCamera._isRightCamera\r\n                    ) {\r\n                        pointerInfo.pickInfo.ray.origin.copyFrom(this._pointerCamera.globalPosition);\r\n                    }\r\n\r\n                    this._ownerNode.computeWorldMatrix(true);\r\n                    const virtualMeshesInfo = this._virtualMeshesInfo[pointerId];\r\n\r\n                    if (isXRPointer) {\r\n                        this._dragging = pointerInfo.pickInfo.originMesh ? this._dragType.NEAR_DRAG : this._dragType.DRAG_WITH_CONTROLLER;\r\n                        virtualMeshesInfo.originMesh.position.copyFrom(pointerInfo.pickInfo.aimTransform!.position);\r\n                        if (this._dragging === this._dragType.NEAR_DRAG && pointerInfo.pickInfo.gripTransform) {\r\n                            virtualMeshesInfo.originMesh.rotationQuaternion!.copyFrom(pointerInfo.pickInfo.gripTransform.rotationQuaternion!);\r\n                        } else {\r\n                            virtualMeshesInfo.originMesh.rotationQuaternion!.copyFrom(pointerInfo.pickInfo.aimTransform!.rotationQuaternion!);\r\n                        }\r\n                    } else {\r\n                        this._dragging = this._dragType.DRAG;\r\n                        virtualMeshesInfo.originMesh.position.copyFrom(pointerInfo.pickInfo.ray.origin);\r\n                    }\r\n\r\n                    virtualMeshesInfo.lastOriginPosition.copyFrom(virtualMeshesInfo.originMesh.position);\r\n\r\n                    virtualMeshesInfo.dragMesh.position.copyFrom(pointerInfo.pickInfo.pickedPoint);\r\n                    virtualMeshesInfo.lastDragPosition.copyFrom(pointerInfo.pickInfo.pickedPoint);\r\n\r\n                    virtualMeshesInfo.pivotMesh.position.copyFrom(this._ownerNode.getAbsolutePivotPoint());\r\n                    virtualMeshesInfo.pivotMesh.rotationQuaternion!.copyFrom(this._ownerNode.absoluteRotationQuaternion);\r\n\r\n                    virtualMeshesInfo.startingPosition.copyFrom(virtualMeshesInfo.dragMesh.position);\r\n                    virtualMeshesInfo.startingPivotPosition.copyFrom(virtualMeshesInfo.pivotMesh.position);\r\n                    virtualMeshesInfo.startingOrientation.copyFrom(virtualMeshesInfo.dragMesh.rotationQuaternion!);\r\n                    virtualMeshesInfo.startingPivotOrientation.copyFrom(virtualMeshesInfo.pivotMesh.rotationQuaternion!);\r\n\r\n                    if (isNearXRPointer) {\r\n                        virtualMeshesInfo.originMesh.addChild(virtualMeshesInfo.dragMesh);\r\n                        virtualMeshesInfo.originMesh.addChild(virtualMeshesInfo.pivotMesh);\r\n                    } else {\r\n                        virtualMeshesInfo.originMesh.lookAt(virtualMeshesInfo.dragMesh.position);\r\n                    }\r\n\r\n                    // Update state\r\n                    virtualMeshesInfo.dragging = true;\r\n\r\n                    if (this.currentDraggingPointerIds.indexOf(pointerId) === -1) {\r\n                        this.currentDraggingPointerIds.push(pointerId);\r\n                    }\r\n\r\n                    // Detach camera controls\r\n                    if (this.detachCameraControls && this._pointerCamera && !this._pointerCamera.leftCamera) {\r\n                        if (this._pointerCamera.inputs && this._pointerCamera.inputs.attachedToElement) {\r\n                            this._pointerCamera.detachControl();\r\n                            this._attachedToElement = true;\r\n                        } else if (!this.allowMultiPointer || this.currentDraggingPointerIds.length === 0) {\r\n                            this._attachedToElement = false;\r\n                        }\r\n                    }\r\n\r\n                    this._targetDragStart(virtualMeshesInfo.pivotMesh.position, virtualMeshesInfo.pivotMesh.rotationQuaternion!, pointerId);\r\n                    this.onDragStartObservable.notifyObservers({ position: virtualMeshesInfo.pivotMesh.position });\r\n                }\r\n            } else if (pointerInfo.type == PointerEventTypes.POINTERUP || pointerInfo.type == PointerEventTypes.POINTERDOUBLETAP) {\r\n                const registeredPointerIndex = this.currentDraggingPointerIds.indexOf(pointerId);\r\n\r\n                // Update state\r\n                virtualMeshesInfo.dragging = false;\r\n\r\n                if (registeredPointerIndex !== -1) {\r\n                    this.currentDraggingPointerIds.splice(registeredPointerIndex, 1);\r\n                    if (this.currentDraggingPointerIds.length === 0) {\r\n                        this._moving = false;\r\n                        this._dragging = this._dragType.NONE;\r\n\r\n                        // Reattach camera controls\r\n                        if (this.detachCameraControls && this._attachedToElement && this._pointerCamera && !this._pointerCamera.leftCamera) {\r\n                            this._reattachCameraControls();\r\n                            this._attachedToElement = false;\r\n                        }\r\n                    }\r\n\r\n                    virtualMeshesInfo.originMesh.removeChild(virtualMeshesInfo.dragMesh);\r\n                    virtualMeshesInfo.originMesh.removeChild(virtualMeshesInfo.pivotMesh);\r\n                    this._targetDragEnd(pointerId);\r\n                    this.onDragEndObservable.notifyObservers({});\r\n                }\r\n            } else if (pointerInfo.type == PointerEventTypes.POINTERMOVE) {\r\n                const registeredPointerIndex = this.currentDraggingPointerIds.indexOf(pointerId);\r\n\r\n                if (registeredPointerIndex !== -1 && virtualMeshesInfo.dragging && pointerInfo.pickInfo && (pointerInfo.pickInfo.ray || pointerInfo.pickInfo.aimTransform)) {\r\n                    let zDragFactor = this.zDragFactor;\r\n\r\n                    // 2 pointer interaction should not have a z axis drag factor\r\n                    // as well as near interaction\r\n                    if (this.currentDraggingPointerIds.length > 1 || pointerInfo.pickInfo.originMesh) {\r\n                        zDragFactor = 0;\r\n                    }\r\n\r\n                    this._ownerNode.computeWorldMatrix(true);\r\n                    if (!isNearXRPointer) {\r\n                        this._pointerUpdate2D(pointerInfo.pickInfo.ray!, pointerId, zDragFactor);\r\n                    } else {\r\n                        this._pointerUpdateXR(pointerInfo.pickInfo.aimTransform!, pointerInfo.pickInfo.gripTransform, pointerId, zDragFactor);\r\n                    }\r\n\r\n                    // Get change in rotation\r\n                    this._tmpQuaternion.copyFrom(virtualMeshesInfo.startingPivotOrientation);\r\n                    this._tmpQuaternion.x = -this._tmpQuaternion.x;\r\n                    this._tmpQuaternion.y = -this._tmpQuaternion.y;\r\n                    this._tmpQuaternion.z = -this._tmpQuaternion.z;\r\n                    virtualMeshesInfo.pivotMesh.absoluteRotationQuaternion.multiplyToRef(this._tmpQuaternion, this._tmpQuaternion);\r\n                    virtualMeshesInfo.pivotMesh.absolutePosition.subtractToRef(virtualMeshesInfo.startingPivotPosition, this._tmpVector);\r\n\r\n                    this.onDragObservable.notifyObservers({ delta: this._tmpVector, position: virtualMeshesInfo.pivotMesh.position, pickInfo: pointerInfo.pickInfo });\r\n\r\n                    // Notify herited methods and observables\r\n                    this._targetDrag(this._tmpVector, this._tmpQuaternion, pointerId);\r\n                    virtualMeshesInfo.lastDragPosition.copyFrom(virtualMeshesInfo.dragMesh.absolutePosition);\r\n\r\n                    this._moving = true;\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    private _applyZOffset(node: TransformNode, localOriginDragDifference: number, zDragFactor: number) {\r\n        // Determine how much the controller moved to/away towards the dragged object and use this to move the object further when its further away\r\n        node.position.z -= node.position.z < 1 ? localOriginDragDifference * zDragFactor : localOriginDragDifference * zDragFactor * node.position.z;\r\n        if (node.position.z < 0) {\r\n            node.position.z = 0;\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _targetDragStart(worldPosition: Vector3, worldRotation: Quaternion, pointerId: number) {\r\n        // Herited classes can override that\r\n    }\r\n\r\n    protected _targetDrag(worldDeltaPosition: Vector3, worldDeltaRotation: Quaternion, pointerId: number) {\r\n        // Herited classes can override that\r\n    }\r\n\r\n    protected _targetDragEnd(pointerId: number) {\r\n        // Herited classes can override that\r\n    }\r\n\r\n    protected _reattachCameraControls() {\r\n        if (this._pointerCamera) {\r\n            // If the camera is an ArcRotateCamera, preserve the settings from the camera\r\n            // when reattaching control\r\n            if (this._pointerCamera.getClassName() === \"ArcRotateCamera\") {\r\n                const arcRotateCamera = this._pointerCamera as ArcRotateCamera;\r\n                arcRotateCamera.attachControl(\r\n                    arcRotateCamera.inputs ? arcRotateCamera.inputs.noPreventDefault : true,\r\n                    arcRotateCamera._useCtrlForPanning,\r\n                    arcRotateCamera._panningMouseButton\r\n                );\r\n            } else {\r\n                // preserve the settings from the camera when reattaching control\r\n                this._pointerCamera.attachControl(this._pointerCamera.inputs ? this._pointerCamera.inputs.noPreventDefault : true);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        if (this._scene) {\r\n            if (this.detachCameraControls && this._attachedToElement && this._pointerCamera && !this._pointerCamera.leftCamera) {\r\n                this._reattachCameraControls();\r\n                this._attachedToElement = false;\r\n            }\r\n            this._scene.onPointerObservable.remove(this._pointerObserver);\r\n        }\r\n\r\n        for (const pointerId in this._virtualMeshesInfo) {\r\n            this._virtualMeshesInfo[pointerId].originMesh.dispose();\r\n            this._virtualMeshesInfo[pointerId].dragMesh.dispose();\r\n        }\r\n\r\n        this.onDragEndObservable.clear();\r\n        this.onDragObservable.clear();\r\n        this.onDragStartObservable.clear();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAGpC,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAE1E,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;;;;;;;AA2BxC,MAAO,sBAAsB;IAAnC,aAAA;QAGY,IAAA,CAAA,kBAAkB,GAAY,KAAK,CAAC;QAClC,IAAA,CAAA,kBAAkB,GAExB,CAAA,CAAE,CAAC;QAEC,IAAA,CAAA,UAAU,GAAY,mKAAI,UAAO,EAAE,CAAC;QACpC,IAAA,CAAA,cAAc,GAAe,mKAAI,aAAU,EAAE,CAAC;QAE5C,IAAA,CAAA,SAAS,GAAG;YAClB,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,oBAAoB,EAAE,CAAC;YACvB,SAAS,EAAE,CAAC;SACf,CAAC;QAGQ,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAEhB,IAAA,CAAA,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAE1C;;;WAGG,CACI,IAAA,CAAA,eAAe,GAA6B,IAAI,CAAC;QAExD;;WAEG,CACI,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAevB;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAa,EAAE,CAAC;QAchD;;;WAGG,CACI,IAAA,CAAA,oBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAG,8JAAI,aAAU,EAAyB,CAAC;QACvE;;WAEG,CACI,IAAA,CAAA,gBAAgB,GAAG,8JAAI,aAAU,EAAgE,CAAC;QACzG;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,8JAAI,aAAU,EAAW,CAAC;QAEvD;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAY,IAAI,CAAC;IA2X7C,CAAC;IAhbG;;OAEG,CACH,IAAW,wBAAwB,GAAA;QAC/B,IAAI,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED,IAAW,wBAAwB,CAAC,KAAa,EAAA;QAC7C,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;IAC9C,CAAC;IAOD;;;OAGG,CACH,gEAAgE;IAChE,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IACD,gEAAgE;IAChE,IAAW,wBAAwB,CAAC,wBAAgC,EAAA;QAChE,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;IAC7D,CAAC;IAyBD;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG,CACI,IAAI,GAAA,CAAI,CAAC;IAEhB;;OAEG,CACH,IAAY,cAAc,GAAA;QACtB,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAC9C,CAAC,MAAM,CAAC;YACJ,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QACpC,CAAC;IACL,CAAC;IAEO,sBAAsB,GAAA;QAC1B,mFAAmF;QAEnF,MAAM,QAAQ,GAAG,mKAAI,gBAAa,CAAC,EAAE,EAAE,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC7E,QAAQ,CAAC,kBAAkB,GAAG,mKAAI,aAAU,EAAE,CAAC;QAC/C,MAAM,UAAU,GAAG,mKAAI,gBAAa,CAAC,EAAE,EAAE,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC/E,UAAU,CAAC,kBAAkB,GAAG,mKAAI,aAAU,EAAE,CAAC;QACjD,MAAM,SAAS,GAAG,mKAAI,gBAAa,CAAC,EAAE,EAAE,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC9E,SAAS,CAAC,kBAAkB,GAAG,mKAAI,aAAU,EAAE,CAAC;QAEhD,OAAO;YACH,QAAQ,EAAE,KAAK;YACf,MAAM,EAAE,KAAK;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,qBAAqB,EAAE,mKAAI,UAAO,EAAE;YACpC,wBAAwB,EAAE,mKAAI,aAAU,EAAE;YAC1C,gBAAgB,EAAE,mKAAI,UAAO,EAAE;YAC/B,mBAAmB,EAAE,mKAAI,aAAU,EAAE;YACrC,kBAAkB,EAAE,mKAAI,UAAO,EAAE;YACjC,gBAAgB,EAAE,mKAAI,UAAO,EAAE;SAClC,CAAC;IACN,CAAC;IAES,2BAA2B,GAAA;QACjC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7D,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC;YAChI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAmB,CAAC,CAAC;YACvI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CACrF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAChF,CAAC;YACF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CACxF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,kBAAmB,CAC3F,CAAC;YACF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnK,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,QAAQ,CACnF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAmB,CAC1F,CAAC;QACN,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,GAAQ,EAAE,SAAiB,EAAE,WAAmB,EAAA;QACrE,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,6JAAI,SAAM,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YAChK,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;YACxD,WAAW,GAAG,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE7D,yDAAyD;QACzD,MAAM,oBAAoB,kKAAG,aAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACpD,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;QACrF,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC1D,MAAM,yBAAyB,GAAG,gKAAC,UAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;QAEpF,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAClE,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAEnE,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,yBAAyB,EAAE,WAAW,CAAC,CAAC;QACvF,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS,EAAE,yBAAyB,EAAE,WAAW,CAAC,CAAC;QAExF,iCAAiC;QACjC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,MAAM,kKAAG,aAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACtC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC3C,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE5C,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACrE,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC1E,CAAC;IAEO,gBAAgB,CAAC,sBAAqC,EAAE,uBAAgD,EAAE,SAAiB,EAAE,WAAmB,EAAA;QACpJ,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC7D,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAChF,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,uBAAuB,EAAE,CAAC;YACzE,iBAAiB,CAAC,UAAU,CAAC,kBAAmB,CAAC,QAAQ,CAAC,uBAAuB,CAAC,kBAAmB,CAAC,CAAC;QAC3G,CAAC,MAAM,CAAC;YACJ,iBAAiB,CAAC,UAAU,CAAC,kBAAmB,CAAC,QAAQ,CAAC,sBAAsB,CAAC,kBAAmB,CAAC,CAAC;QAC1G,CAAC;QAED,iBAAiB,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACrD,iBAAiB,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAEpD,kBAAkB;QAClB,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACpB,+EAA+E;YAC/E,MAAM,gBAAgB,kKAAG,aAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAChD,MAAM,mBAAmB,kKAAG,aAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnD,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAe,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,CAAC;YAC1E,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;YAC/G,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACrF,MAAM,sBAAsB,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAC5D,mBAAmB,CAAC,SAAS,EAAE,CAAC;YAEhC,MAAM,YAAY,kKAAG,aAAU,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC5C,MAAM,gBAAgB,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC/C,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,cAAe,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAC7G,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YACnH,MAAM,wBAAwB,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC;YAC3D,YAAY,CAAC,SAAS,EAAE,CAAC;YACzB,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAE7B,MAAM,qBAAqB,GAAG,IAAI,CAAC,GAAG,gKAAC,UAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC,kKAAG,UAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;YAChJ,IAAI,cAAc,GAAG,qBAAqB,GAAG,WAAW,GAAG,sBAAsB,GAAG,wBAAwB,CAAC;YAE7G,kDAAkD;YAClD,MAAM,mCAAmC,GAAG,IAAI,CAAC;YACjD,IAAI,cAAc,GAAG,CAAC,IAAI,mCAAmC,GAAG,wBAAwB,GAAG,cAAc,EAAE,CAAC;gBACxG,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,mCAAmC,GAAG,wBAAwB,EAAE,CAAC,CAAC,CAAC;YACjG,CAAC;YACD,gBAAgB,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YAE9C,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACzF,iBAAiB,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjE,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACxF,iBAAiB,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,MAAM,CAAC,SAAwB,EAAA;QAClC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,CAAC;YACxC,sBAAsB,CAAC,aAAa,GAAG,iJAAI,QAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;gBAAE,OAAO,EAAE,IAAI;YAAA,CAAE,CAAC,CAAC;YAC7F,sBAAsB,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACzD,CAAC;QAED,MAAM,aAAa,GAAG,CAAC,CAAe,EAAE,EAAE;YACtC,OAAO,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,AAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7I,CAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACxE,MAAM,SAAS,GAAmB,WAAW,CAAC,KAAM,CAAC,SAAS,CAAC;YAC/D,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACvE,CAAC;YACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;YAC7D,MAAM,WAAW,GAAmB,WAAW,CAAC,KAAM,CAAC,WAAW,KAAK,SAAS,IAAoB,WAAW,CAAC,KAAM,CAAC,WAAW,KAAK,IAAI,CAAC;YAC5I,MAAM,eAAe,GAAmB,WAAW,CAAC,KAAM,CAAC,WAAW,KAAK,SAAS,CAAC;YACrF,IAAI,WAAW,CAAC,IAAI,mKAAI,oBAAiB,CAAC,WAAW,EAAE,CAAC;gBACpD,IACI,CAAC,iBAAiB,CAAC,QAAQ,IAC3B,WAAW,CAAC,QAAQ,IACpB,WAAW,CAAC,QAAQ,CAAC,GAAG,IACxB,WAAW,CAAC,QAAQ,CAAC,UAAU,IAC/B,WAAW,CAAC,QAAQ,CAAC,WAAW,IAChC,WAAW,CAAC,QAAQ,CAAC,GAAG,IACxB,CAAC,CAAC,eAAe,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,IACvD,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAChD,CAAC;oBACC,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB,IAAI,WAAW,CAAC,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACxF,OAAO;oBACX,CAAC;oBAED,IACI,IAAI,CAAC,cAAc,IACnB,IAAI,CAAC,cAAc,CAAC,aAAa,8JAAK,SAAM,CAAC,aAAa,IAC1D,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,IAClC,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,EACrC,CAAC;wBACC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;oBACjF,CAAC;oBAED,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBACzC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;oBAE7D,IAAI,WAAW,EAAE,CAAC;wBACd,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC;wBAClH,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAa,CAAC,QAAQ,CAAC,CAAC;wBAC5F,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,WAAW,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;4BACpF,iBAAiB,CAAC,UAAU,CAAC,kBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAmB,CAAC,CAAC;wBACtH,CAAC,MAAM,CAAC;4BACJ,iBAAiB,CAAC,UAAU,CAAC,kBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAa,CAAC,kBAAmB,CAAC,CAAC;wBACtH,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;wBACrC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpF,CAAC;oBAED,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;oBAErF,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBAC/E,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBAE9E,iBAAiB,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC,CAAC;oBACvF,iBAAiB,CAAC,SAAS,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;oBAErG,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBACjF,iBAAiB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;oBACvF,iBAAiB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,kBAAmB,CAAC,CAAC;oBAC/F,iBAAiB,CAAC,wBAAwB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,kBAAmB,CAAC,CAAC;oBAErG,IAAI,eAAe,EAAE,CAAC;wBAClB,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAClE,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;oBACvE,CAAC,MAAM,CAAC;wBACJ,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAC7E,CAAC;oBAED,eAAe;oBACf,iBAAiB,CAAC,QAAQ,GAAG,IAAI,CAAC;oBAElC,IAAI,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBAC3D,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACnD,CAAC;oBAED,yBAAyB;oBACzB,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;wBACtF,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;4BAC7E,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;4BACpC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;wBACnC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;4BAChF,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;wBACpC,CAAC;oBACL,CAAC;oBAED,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,SAAS,CAAC,QAAQ,EAAE,iBAAiB,CAAC,SAAS,CAAC,kBAAmB,EAAE,SAAS,CAAC,CAAC;oBACxH,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC;wBAAE,QAAQ,EAAE,iBAAiB,CAAC,SAAS,CAAC,QAAQ;oBAAA,CAAE,CAAC,CAAC;gBACnG,CAAC;YACL,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,mKAAI,oBAAiB,CAAC,SAAS,IAAI,WAAW,CAAC,IAAI,mKAAI,oBAAiB,CAAC,gBAAgB,EAAE,CAAC;gBACnH,MAAM,sBAAsB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAEjF,eAAe;gBACf,iBAAiB,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAEnC,IAAI,sBAAsB,KAAK,CAAC,CAAC,EAAE,CAAC;oBAChC,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC;oBACjE,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC9C,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;wBACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;wBAErC,2BAA2B;wBAC3B,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;4BACjH,IAAI,CAAC,uBAAuB,EAAE,CAAC;4BAC/B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;wBACpC,CAAC;oBACL,CAAC;oBAED,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBACrE,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;oBACtE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;oBAC/B,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAA,CAAE,CAAC,CAAC;gBACjD,CAAC;YACL,CAAC,MAAM,IAAI,WAAW,CAAC,IAAI,mKAAI,oBAAiB,CAAC,WAAW,EAAE,CAAC;gBAC3D,MAAM,sBAAsB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBAEjF,IAAI,sBAAsB,KAAK,CAAC,CAAC,IAAI,iBAAiB,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBACzJ,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;oBAEnC,6DAA6D;oBAC7D,8BAA8B;oBAC9B,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;wBAC/E,WAAW,GAAG,CAAC,CAAC;oBACpB,CAAC;oBAED,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBACzC,IAAI,CAAC,eAAe,EAAE,CAAC;wBACnB,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAI,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;oBAC7E,CAAC,MAAM,CAAC;wBACJ,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAa,EAAE,WAAW,CAAC,QAAQ,CAAC,aAAa,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;oBAC1H,CAAC;oBAED,yBAAyB;oBACzB,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,CAAC;oBACzE,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;oBAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;oBAC/C,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;oBAC/C,iBAAiB,CAAC,SAAS,CAAC,0BAA0B,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;oBAC/G,iBAAiB,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBAErH,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;wBAAE,KAAK,EAAE,IAAI,CAAC,UAAU;wBAAE,QAAQ,EAAE,iBAAiB,CAAC,SAAS,CAAC,QAAQ;wBAAE,QAAQ,EAAE,WAAW,CAAC,QAAQ;oBAAA,CAAE,CAAC,CAAC;oBAElJ,yCAAyC;oBACzC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;oBAClE,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;oBAEzF,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACxB,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,IAAmB,EAAE,yBAAiC,EAAE,WAAmB,EAAA;QAC7F,2IAA2I;QAC3I,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,yBAAyB,GAAG,WAAW,CAAC,CAAC,CAAC,yBAAyB,GAAG,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7I,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;IACL,CAAC;IAED,6DAA6D;IACnD,gBAAgB,CAAC,aAAsB,EAAE,aAAyB,EAAE,SAAiB,EAAA;IAC3F,oCAAoC;IACxC,CAAC;IAES,WAAW,CAAC,kBAA2B,EAAE,kBAA8B,EAAE,SAAiB,EAAA;IAChG,oCAAoC;IACxC,CAAC;IAES,cAAc,CAAC,SAAiB,EAAA;IACtC,oCAAoC;IACxC,CAAC;IAES,uBAAuB,GAAA;QAC7B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,6EAA6E;YAC7E,2BAA2B;YAC3B,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,KAAK,iBAAiB,EAAE,CAAC;gBAC3D,MAAM,eAAe,GAAG,IAAI,CAAC,cAAiC,CAAC;gBAC/D,eAAe,CAAC,aAAa,CACzB,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,EACvE,eAAe,CAAC,kBAAkB,EAClC,eAAe,CAAC,mBAAmB,CACtC,CAAC;YACN,CAAC,MAAM,CAAC;gBACJ,iEAAiE;gBACjE,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvH,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;gBACjH,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC;QAED,IAAK,MAAM,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAE,CAAC;YAC9C,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxD,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;IACvC,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 2063, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/sixDofDragBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/sixDofDragBehavior.ts"], "sourcesContent": ["import type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Vector3, Quaternion, Matrix, TmpVectors } from \"../../Maths/math.vector\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { BaseSixDofDragBehavior } from \"./baseSixDofDragBehavior\";\r\nimport { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Space } from \"../../Maths/math.axis\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will allow the mesh to be dragged around based on directions and origin of the pointer's ray\r\n */\r\nexport class SixDofDragBehavior extends BaseSixDofDragBehavior {\r\n    private _sceneRenderObserver: Nullable<Observer<Scene>> = null;\r\n    private _virtualTransformNode: TransformNode;\r\n\r\n    protected _targetPosition = new Vector3(0, 0, 0);\r\n    protected _targetOrientation = new Quaternion();\r\n    protected _targetScaling = new Vector3(1, 1, 1);\r\n    protected _startingPosition = new Vector3(0, 0, 0);\r\n    protected _startingOrientation = new Quaternion();\r\n    protected _startingScaling = new Vector3(1, 1, 1);\r\n\r\n    /**\r\n     * Fires when position is updated\r\n     */\r\n    public onPositionChangedObservable = new Observable<{ position: Vector3 }>();\r\n\r\n    /**\r\n     * The distance towards the target drag position to move each frame. This can be useful to avoid jitter. Set this to 1 for no delay. (Default: 0.2)\r\n     */\r\n    public dragDeltaRatio = 0.2;\r\n\r\n    /**\r\n     * If the object should rotate to face the drag origin\r\n     */\r\n    public rotateDraggedObject = true;\r\n\r\n    /**\r\n     * If `rotateDraggedObject` is set to `true`, this parameter determines if we are only rotating around the y axis (yaw)\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public rotateAroundYOnly = false;\r\n\r\n    /**\r\n     * Should the behavior rotate 1:1 with the motion controller, when one is used.\r\n     */\r\n    public rotateWithMotionController = true;\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public override get name(): string {\r\n        return \"SixDofDrag\";\r\n    }\r\n\r\n    /**\r\n     * Use this flag to update the target but not move the owner node towards the target\r\n     */\r\n    public disableMovement: boolean = false;\r\n\r\n    /**\r\n     * Should the object rotate towards the camera when we start dragging it\r\n     */\r\n    public faceCameraOnDragStart = false;\r\n\r\n    /**\r\n     * Attaches the six DoF drag behavior\r\n     * In XR mode the mesh and its children will have their isNearGrabbable property set to true\r\n     * @param ownerNode The mesh that will be dragged around once attached\r\n     */\r\n    public override attach(ownerNode: Mesh): void {\r\n        super.attach(ownerNode);\r\n\r\n        ownerNode.isNearGrabbable = true;\r\n        // if it has children, make sure they are grabbable too\r\n        const children = ownerNode.getChildMeshes();\r\n        for (const m of children) {\r\n            m.isNearGrabbable = true;\r\n        }\r\n\r\n        // Node that will save the owner's transform\r\n        this._virtualTransformNode = new TransformNode(\"virtual_sixDof\", BaseSixDofDragBehavior._VirtualScene);\r\n        this._virtualTransformNode.rotationQuaternion = Quaternion.Identity();\r\n\r\n        // On every frame move towards target scaling to avoid jitter caused by vr controllers\r\n        this._sceneRenderObserver = ownerNode.getScene().onBeforeRenderObservable.add(() => {\r\n            if (this.currentDraggingPointerIds.length === 1 && this._moving && !this.disableMovement) {\r\n                // 1 pointer only drags mesh\r\n                const deltaToAdd = TmpVectors.Vector3[0];\r\n                deltaToAdd.copyFrom(this._targetPosition).subtractInPlace(ownerNode.absolutePosition).scaleInPlace(this.dragDeltaRatio);\r\n                const deltaToAddTransformed = TmpVectors.Vector3[1];\r\n                deltaToAddTransformed.copyFrom(deltaToAdd);\r\n                // If the node has a parent, transform the delta to local space, so it can be added to the\r\n                // position in local space\r\n                if (ownerNode.parent) {\r\n                    const parentRotationMatrixInverse = TmpVectors.Matrix[0];\r\n                    (ownerNode.parent as TransformNode).absoluteRotationQuaternion.toRotationMatrix(parentRotationMatrixInverse);\r\n                    parentRotationMatrixInverse.invert();\r\n                    Vector3.TransformNormalToRef(deltaToAdd, parentRotationMatrixInverse, deltaToAddTransformed);\r\n                }\r\n                ownerNode.position.addInPlace(deltaToAddTransformed);\r\n\r\n                this.onPositionChangedObservable.notifyObservers({ position: ownerNode.absolutePosition });\r\n\r\n                // Only rotate the mesh if it's parent has uniform scaling\r\n                if (!ownerNode.parent || ((ownerNode.parent as TransformNode).scaling && !(ownerNode.parent as TransformNode).scaling.isNonUniformWithinEpsilon(0.001))) {\r\n                    const rotationToApply = TmpVectors.Quaternion[0];\r\n                    rotationToApply.copyFrom(this._targetOrientation);\r\n                    if (ownerNode.parent) {\r\n                        const parentRotationInverse = TmpVectors.Quaternion[0];\r\n                        parentRotationInverse.copyFrom((ownerNode.parent as TransformNode).absoluteRotationQuaternion);\r\n                        parentRotationInverse.invertInPlace();\r\n                        parentRotationInverse.multiplyToRef(this._targetOrientation, rotationToApply);\r\n                    }\r\n                    Quaternion.SlerpToRef(ownerNode.rotationQuaternion!, rotationToApply, this.dragDeltaRatio, ownerNode.rotationQuaternion!);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    private _getPositionOffsetAround(transformationLocalOrigin: Vector3, scaling: number, rotation: Quaternion): Vector3 {\r\n        const translationMatrix = TmpVectors.Matrix[0]; // T\r\n        const translationMatrixInv = TmpVectors.Matrix[1]; // T'\r\n        const rotationMatrix = TmpVectors.Matrix[2]; // R\r\n        const scaleMatrix = TmpVectors.Matrix[3]; // S\r\n        const finalMatrix = TmpVectors.Matrix[4]; // T' x R x S x T\r\n\r\n        Matrix.TranslationToRef(transformationLocalOrigin.x, transformationLocalOrigin.y, transformationLocalOrigin.z, translationMatrix); // T\r\n        Matrix.TranslationToRef(-transformationLocalOrigin.x, -transformationLocalOrigin.y, -transformationLocalOrigin.z, translationMatrixInv); // T'\r\n        Matrix.FromQuaternionToRef(rotation, rotationMatrix); // R\r\n        Matrix.ScalingToRef(scaling, scaling, scaling, scaleMatrix);\r\n        translationMatrixInv.multiplyToRef(rotationMatrix, finalMatrix); // T' x R\r\n        finalMatrix.multiplyToRef(scaleMatrix, finalMatrix); // T' x R x S\r\n        finalMatrix.multiplyToRef(translationMatrix, finalMatrix); // T' x R x S x T\r\n\r\n        return finalMatrix.getTranslation();\r\n    }\r\n\r\n    private _onePointerPositionUpdated(worldDeltaPosition: Vector3, worldDeltaRotation: Quaternion) {\r\n        const pointerDelta = TmpVectors.Vector3[0];\r\n        pointerDelta.setAll(0);\r\n\r\n        if (this._dragging === this._dragType.DRAG) {\r\n            if (this.rotateDraggedObject) {\r\n                if (this.rotateAroundYOnly) {\r\n                    // Convert change in rotation to only y axis rotation\r\n                    Quaternion.RotationYawPitchRollToRef(worldDeltaRotation.toEulerAngles().y, 0, 0, TmpVectors.Quaternion[0]);\r\n                } else {\r\n                    TmpVectors.Quaternion[0].copyFrom(worldDeltaRotation);\r\n                }\r\n                TmpVectors.Quaternion[0].multiplyToRef(this._startingOrientation, this._targetOrientation);\r\n            }\r\n        } else if (this._dragging === this._dragType.NEAR_DRAG || (this._dragging === this._dragType.DRAG_WITH_CONTROLLER && this.rotateWithMotionController)) {\r\n            worldDeltaRotation.multiplyToRef(this._startingOrientation, this._targetOrientation);\r\n        }\r\n\r\n        this._targetPosition.copyFrom(this._startingPosition).addInPlace(worldDeltaPosition);\r\n    }\r\n\r\n    private _twoPointersPositionUpdated() {\r\n        const startingPosition0 = this._virtualMeshesInfo[this.currentDraggingPointerIds[0]].startingPosition;\r\n        const startingPosition1 = this._virtualMeshesInfo[this.currentDraggingPointerIds[1]].startingPosition;\r\n        const startingCenter = TmpVectors.Vector3[0];\r\n        startingPosition0.addToRef(startingPosition1, startingCenter);\r\n        startingCenter.scaleInPlace(0.5);\r\n        const startingVector = TmpVectors.Vector3[1];\r\n        startingPosition1.subtractToRef(startingPosition0, startingVector);\r\n\r\n        const currentPosition0 = this._virtualMeshesInfo[this.currentDraggingPointerIds[0]].dragMesh.absolutePosition;\r\n        const currentPosition1 = this._virtualMeshesInfo[this.currentDraggingPointerIds[1]].dragMesh.absolutePosition;\r\n        const currentCenter = TmpVectors.Vector3[2];\r\n        currentPosition0.addToRef(currentPosition1, currentCenter);\r\n        currentCenter.scaleInPlace(0.5);\r\n        const currentVector = TmpVectors.Vector3[3];\r\n        currentPosition1.subtractToRef(currentPosition0, currentVector);\r\n\r\n        const scaling = currentVector.length() / startingVector.length();\r\n        const translation = currentCenter.subtract(startingCenter);\r\n        const rotationQuaternion = Quaternion.FromEulerAngles(\r\n            0,\r\n            Vector3.GetAngleBetweenVectorsOnPlane(startingVector.normalize(), currentVector.normalize(), Vector3.UpReadOnly),\r\n            0\r\n        );\r\n\r\n        const oldParent = this._ownerNode.parent;\r\n        this._ownerNode.setParent(null);\r\n\r\n        const positionOffset = this._getPositionOffsetAround(startingCenter.subtract(this._virtualTransformNode.getAbsolutePivotPoint()), scaling, rotationQuaternion);\r\n        this._virtualTransformNode.rotationQuaternion!.multiplyToRef(rotationQuaternion, this._ownerNode.rotationQuaternion!);\r\n        this._virtualTransformNode.scaling.scaleToRef(scaling, this._ownerNode.scaling);\r\n        this._virtualTransformNode.position.addToRef(translation.addInPlace(positionOffset), this._ownerNode.position);\r\n        this.onPositionChangedObservable.notifyObservers({ position: this._ownerNode.position });\r\n\r\n        this._ownerNode.setParent(oldParent);\r\n    }\r\n\r\n    protected override _targetDragStart() {\r\n        const pointerCount = this.currentDraggingPointerIds.length;\r\n\r\n        if (!this._ownerNode.rotationQuaternion) {\r\n            this._ownerNode.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._ownerNode.rotation.y, this._ownerNode.rotation.x, this._ownerNode.rotation.z);\r\n        }\r\n        const worldPivot = this._ownerNode.getAbsolutePivotPoint();\r\n\r\n        if (pointerCount === 1) {\r\n            this._targetPosition.copyFrom(this._ownerNode.absolutePosition);\r\n            this._targetOrientation.copyFrom(this._ownerNode.rotationQuaternion);\r\n            this._targetScaling.copyFrom(this._ownerNode.absoluteScaling);\r\n\r\n            if (this.faceCameraOnDragStart && this._scene.activeCamera) {\r\n                const toCamera = TmpVectors.Vector3[0];\r\n                this._scene.activeCamera.position.subtractToRef(worldPivot, toCamera);\r\n                toCamera.normalize();\r\n                const quat = TmpVectors.Quaternion[0];\r\n                if (this._scene.useRightHandedSystem) {\r\n                    Quaternion.FromLookDirectionRHToRef(toCamera, new Vector3(0, 1, 0), quat);\r\n                } else {\r\n                    Quaternion.FromLookDirectionLHToRef(toCamera, new Vector3(0, 1, 0), quat);\r\n                }\r\n                quat.normalize();\r\n                Quaternion.RotationYawPitchRollToRef(quat.toEulerAngles().y, 0, 0, TmpVectors.Quaternion[0]);\r\n                this._targetOrientation.copyFrom(TmpVectors.Quaternion[0]);\r\n            }\r\n            this._startingPosition.copyFrom(this._targetPosition);\r\n            this._startingOrientation.copyFrom(this._targetOrientation);\r\n            this._startingScaling.copyFrom(this._targetScaling);\r\n        } else if (pointerCount === 2) {\r\n            this._virtualTransformNode.setPivotPoint(new Vector3(0, 0, 0), Space.LOCAL);\r\n            this._virtualTransformNode.position.copyFrom(this._ownerNode.absolutePosition);\r\n            this._virtualTransformNode.scaling.copyFrom(this._ownerNode.absoluteScaling);\r\n            this._virtualTransformNode.rotationQuaternion!.copyFrom(this._ownerNode.absoluteRotationQuaternion);\r\n            this._virtualTransformNode.setPivotPoint(worldPivot, Space.WORLD);\r\n            this._resetVirtualMeshesPosition();\r\n        }\r\n    }\r\n\r\n    protected override _targetDrag(worldDeltaPosition: Vector3, worldDeltaRotation: Quaternion) {\r\n        if (this.currentDraggingPointerIds.length === 1) {\r\n            this._onePointerPositionUpdated(worldDeltaPosition, worldDeltaRotation);\r\n        } else if (this.currentDraggingPointerIds.length === 2) {\r\n            this._twoPointersPositionUpdated();\r\n        }\r\n    }\r\n\r\n    protected override _targetDragEnd() {\r\n        if (this.currentDraggingPointerIds.length === 1) {\r\n            // We still have 1 active pointer, we must simulate a dragstart with a reseted position/orientation\r\n            this._resetVirtualMeshesPosition();\r\n            const previousFaceCameraFlag = this.faceCameraOnDragStart;\r\n            this.faceCameraOnDragStart = false;\r\n            this._targetDragStart();\r\n            this.faceCameraOnDragStart = previousFaceCameraFlag;\r\n        }\r\n    }\r\n\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public override detach(): void {\r\n        super.detach();\r\n\r\n        if (this._ownerNode) {\r\n            this._ownerNode.getScene().onBeforeRenderObservable.remove(this._sceneRenderObserver);\r\n        }\r\n\r\n        if (this._virtualTransformNode) {\r\n            this._virtualTransformNode.dispose();\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAElF,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;;;;;AAMrD,MAAO,kBAAmB,8LAAQ,yBAAsB;IAA9D,aAAA;;QACY,IAAA,CAAA,oBAAoB,GAA8B,IAAI,CAAC;QAGrD,IAAA,CAAA,eAAe,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,IAAA,CAAA,kBAAkB,GAAG,mKAAI,aAAU,EAAE,CAAC;QACtC,IAAA,CAAA,cAAc,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,IAAA,CAAA,iBAAiB,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,IAAA,CAAA,oBAAoB,GAAG,mKAAI,aAAU,EAAE,CAAC;QACxC,IAAA,CAAA,gBAAgB,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAElD;;WAEG,CACI,IAAA,CAAA,2BAA2B,GAAG,8JAAI,aAAU,EAAyB,CAAC;QAE7E;;WAEG,CACI,IAAA,CAAA,cAAc,GAAG,GAAG,CAAC;QAE5B;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,IAAI,CAAC;QAElC;;WAEG,CACH,gEAAgE;QACzD,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAEjC;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,IAAI,CAAC;QASzC;;WAEG,CACI,IAAA,CAAA,eAAe,GAAY,KAAK,CAAC;QAExC;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;IA8MzC,CAAC;IA7NG;;OAEG,CACH,IAAoB,IAAI,GAAA;QACpB,OAAO,YAAY,CAAC;IACxB,CAAC;IAYD;;;;OAIG,CACa,MAAM,CAAC,SAAe,EAAA;QAClC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAExB,SAAS,CAAC,eAAe,GAAG,IAAI,CAAC;QACjC,uDAAuD;QACvD,MAAM,QAAQ,GAAG,SAAS,CAAC,cAAc,EAAE,CAAC;QAC5C,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;YACvB,CAAC,CAAC,eAAe,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,4CAA4C;QAC5C,IAAI,CAAC,qBAAqB,GAAG,mKAAI,gBAAa,CAAC,gBAAgB,uLAAE,yBAAsB,CAAC,aAAa,CAAC,CAAC;QACvG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,kKAAG,aAAU,CAAC,QAAQ,EAAE,CAAC;QAEtE,sFAAsF;QACtF,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/E,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvF,4BAA4B;gBAC5B,MAAM,UAAU,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACzC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACxH,MAAM,qBAAqB,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpD,qBAAqB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAC3C,0FAA0F;gBAC1F,0BAA0B;gBAC1B,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,2BAA2B,kKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxD,SAAS,CAAC,MAAwB,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,CAAC;oBAC7G,2BAA2B,CAAC,MAAM,EAAE,CAAC;mLACrC,UAAO,CAAC,oBAAoB,CAAC,UAAU,EAAE,2BAA2B,EAAE,qBAAqB,CAAC,CAAC;gBACjG,CAAC;gBACD,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;gBAErD,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC;oBAAE,QAAQ,EAAE,SAAS,CAAC,gBAAgB;gBAAA,CAAE,CAAC,CAAC;gBAE3F,0DAA0D;gBAC1D,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,AAAE,SAAS,CAAC,MAAwB,CAAC,OAAO,IAAI,CAAE,SAAS,CAAC,MAAwB,CAAC,OAAO,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAAE,CAAC;oBACtJ,MAAM,eAAe,kKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACjD,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAClD,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;wBACnB,MAAM,qBAAqB,kKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;wBACvD,qBAAqB,CAAC,QAAQ,CAAE,SAAS,CAAC,MAAwB,CAAC,0BAA0B,CAAC,CAAC;wBAC/F,qBAAqB,CAAC,aAAa,EAAE,CAAC;wBACtC,qBAAqB,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;oBAClF,CAAC;mLACD,aAAU,CAAC,UAAU,CAAC,SAAS,CAAC,kBAAmB,EAAE,eAAe,EAAE,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,kBAAmB,CAAC,CAAC;gBAC9H,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,wBAAwB,CAAC,yBAAkC,EAAE,OAAe,EAAE,QAAoB,EAAA;QACtG,MAAM,iBAAiB,kKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QACpD,MAAM,oBAAoB,kKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;QACxD,MAAM,cAAc,kKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QACjD,MAAM,WAAW,kKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QAC9C,MAAM,WAAW,kKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;uKAE3D,SAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC,EAAE,yBAAyB,CAAC,CAAC,EAAE,yBAAyB,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI;uKACvI,SAAM,CAAC,gBAAgB,CAAC,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,yBAAyB,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC,CAAC,KAAK;uKAC9I,SAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI;uKAC1D,SAAM,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAC5D,oBAAoB,CAAC,aAAa,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS;QAC1E,WAAW,CAAC,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC,aAAa;QAClE,WAAW,CAAC,aAAa,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC,CAAC,iBAAiB;QAE5E,OAAO,WAAW,CAAC,cAAc,EAAE,CAAC;IACxC,CAAC;IAEO,0BAA0B,CAAC,kBAA2B,EAAE,kBAA8B,EAAA;QAC1F,MAAM,YAAY,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC3C,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAEvB,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,qDAAqD;mLACrD,aAAU,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,iKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/G,CAAC,MAAM,CAAC;mLACJ,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;gBAC1D,CAAC;+KACD,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC/F,CAAC;QACL,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,AAAC,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,oBAAoB,IAAI,IAAI,CAAC,0BAA0B,CAAC,CAAE,CAAC;YACpJ,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzF,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;IACzF,CAAC;IAEO,2BAA2B,GAAA;QAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACtG,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACtG,MAAM,cAAc,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,iBAAiB,CAAC,QAAQ,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAC9D,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,cAAc,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,iBAAiB,CAAC,aAAa,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAEnE,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAC9G,MAAM,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QAC9G,MAAM,aAAa,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5C,gBAAgB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAC3D,aAAa,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAChC,MAAM,aAAa,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5C,gBAAgB,CAAC,aAAa,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAEhE,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;QACjE,MAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QAC3D,MAAM,kBAAkB,kKAAG,aAAU,CAAC,eAAe,CACjD,CAAC,iKACD,UAAO,CAAC,6BAA6B,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,aAAa,CAAC,SAAS,EAAE,iKAAE,UAAO,CAAC,UAAU,CAAC,EAChH,CAAC,CACJ,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEhC,MAAM,cAAc,GAAG,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,CAAC,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;QAC/J,IAAI,CAAC,qBAAqB,CAAC,kBAAmB,CAAC,aAAa,CAAC,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,kBAAmB,CAAC,CAAC;QACtH,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAChF,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC/G,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC;YAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ;QAAA,CAAE,CAAC,CAAC;QAEzF,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;IAEkB,gBAAgB,GAAA;QAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;QAE3D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,kBAAkB,kKAAG,aAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7J,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC;QAE3D,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAChE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;YACrE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAE9D,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBACzD,MAAM,QAAQ,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBACtE,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACrB,MAAM,IAAI,kKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACtC,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;mLACnC,aAAU,CAAC,wBAAwB,CAAC,QAAQ,EAAE,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC9E,CAAC,MAAM,CAAC;mLACJ,aAAU,CAAC,wBAAwB,CAAC,QAAQ,EAAE,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC9E,CAAC;gBACD,IAAI,CAAC,SAAS,EAAE,CAAC;+KACjB,aAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,iKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7F,IAAI,CAAC,kBAAkB,CAAC,QAAQ,gKAAC,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC;YACD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACtD,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAC5D,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxD,CAAC,MAAM,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAA,EAAA,eAAA,GAAc,CAAC;YAC5E,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC/E,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YAC7E,IAAI,CAAC,qBAAqB,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC;YACpG,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,UAAU,EAAA,EAAA,eAAA,GAAc,CAAC;YAClE,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC;IACL,CAAC;IAEkB,WAAW,CAAC,kBAA2B,EAAE,kBAA8B,EAAA;QACtF,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;QAC5E,CAAC,MAAM,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC;IACL,CAAC;IAEkB,cAAc,GAAA;QAC7B,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9C,mGAAmG;YACnG,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,MAAM,sBAAsB,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC1D,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,qBAAqB,GAAG,sBAAsB,CAAC;QACxD,CAAC;IACL,CAAC;IAED;;OAEG,CACa,MAAM,GAAA;QAClB,KAAK,CAAC,MAAM,EAAE,CAAC;QAEf,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC1F,CAAC;QAED,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;QACzC,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 2293, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/surfaceMagnetismBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/surfaceMagnetismBehavior.ts"], "sourcesContent": ["import type { PickingInfo } from \"../../Collisions/pickingInfo\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { Quaternion, TmpVectors, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../../Meshes/mesh\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Behavior } from \"../behavior\";\r\n\r\n/**\r\n * A behavior that allows a transform node to stick to a surface position/orientation\r\n * @since 5.0.0\r\n */\r\nexport class SurfaceMagnetismBehavior implements Behavior<Mesh> {\r\n    private _scene: Scene;\r\n    private _attachedMesh: Nullable<Mesh>;\r\n    private _attachPointLocalOffset: Vector3 = new Vector3();\r\n    private _pointerObserver: Nullable<Observer<PointerInfo>>;\r\n    private _workingPosition: Vector3 = new Vector3();\r\n    private _workingQuaternion: Quaternion = new Quaternion();\r\n    private _lastTick: number = -1;\r\n    private _onBeforeRender: Nullable<Observer<Scene>>;\r\n    private _hit = false;\r\n\r\n    /**\r\n     * Distance offset from the hit point to place the target at, along the hit normal.\r\n     */\r\n    public hitNormalOffset: number = 0.05;\r\n\r\n    /**\r\n     * Name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"SurfaceMagnetism\";\r\n    }\r\n\r\n    /**\r\n     * Spatial mapping meshes to collide with\r\n     */\r\n    public meshes: AbstractMesh[] = [];\r\n\r\n    /**\r\n     * Function called when the behavior needs to be initialized (after attaching it to a target)\r\n     */\r\n    public init(): void {}\r\n\r\n    /**\r\n     * Set to false if the node should strictly follow the camera without any interpolation time\r\n     */\r\n    public interpolatePose = true;\r\n\r\n    /**\r\n     * Rate of interpolation of position and rotation of the attached node.\r\n     * Higher values will give a slower interpolation.\r\n     */\r\n    public lerpTime = 250;\r\n\r\n    /**\r\n     * If true, pitch and roll are omitted.\r\n     */\r\n    public keepOrientationVertical = true;\r\n\r\n    /**\r\n     * Is this behavior reacting to pointer events\r\n     */\r\n    public enabled = true;\r\n\r\n    /**\r\n     * Maximum distance for the node to stick to the surface\r\n     */\r\n    public maxStickingDistance = 0.8;\r\n\r\n    /**\r\n     * Attaches the behavior to a transform node\r\n     * @param target defines the target where the behavior is attached to\r\n     * @param scene the scene\r\n     */\r\n    public attach(target: Mesh, scene?: Scene): void {\r\n        this._attachedMesh = target;\r\n        this._scene = scene || target.getScene();\r\n        if (!this._attachedMesh.rotationQuaternion) {\r\n            this._attachedMesh.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._attachedMesh.rotation.y, this._attachedMesh.rotation.x, this._attachedMesh.rotation.z);\r\n        }\r\n        this.updateAttachPoint();\r\n\r\n        this._workingPosition.copyFrom(this._attachedMesh.position);\r\n        this._workingQuaternion.copyFrom(this._attachedMesh.rotationQuaternion);\r\n        this._addObservables();\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior\r\n     */\r\n    public detach(): void {\r\n        this._attachedMesh = null;\r\n        this._removeObservables();\r\n    }\r\n\r\n    private _getTargetPose(pickingInfo: PickingInfo): Nullable<{ position: Vector3; quaternion: Quaternion }> {\r\n        if (!this._attachedMesh) {\r\n            return null;\r\n        }\r\n\r\n        if (pickingInfo && pickingInfo.hit) {\r\n            const pickedNormal = pickingInfo.getNormal(true, true);\r\n            const pickedPoint = pickingInfo.pickedPoint;\r\n\r\n            if (!pickedNormal || !pickedPoint) {\r\n                return null;\r\n            }\r\n            pickedNormal.normalize();\r\n\r\n            const worldTarget = TmpVectors.Vector3[0];\r\n            worldTarget.copyFrom(pickedNormal);\r\n            worldTarget.scaleInPlace(this.hitNormalOffset);\r\n            worldTarget.addInPlace(pickedPoint);\r\n\r\n            if (this._attachedMesh.parent) {\r\n                TmpVectors.Matrix[0].copyFrom(this._attachedMesh.parent.getWorldMatrix()).invert();\r\n                Vector3.TransformNormalToRef(worldTarget, TmpVectors.Matrix[0], worldTarget);\r\n            }\r\n\r\n            return {\r\n                position: worldTarget,\r\n                quaternion: Quaternion.RotationYawPitchRoll(\r\n                    -Math.atan2(pickedNormal.x, -pickedNormal.z),\r\n                    this.keepOrientationVertical ? 0 : Math.atan2(pickedNormal.y, Math.sqrt(pickedNormal.z * pickedNormal.z + pickedNormal.x * pickedNormal.x)),\r\n                    0\r\n                ),\r\n            };\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Updates the attach point with the current geometry extents of the attached mesh\r\n     */\r\n    public updateAttachPoint() {\r\n        this._getAttachPointOffsetToRef(this._attachPointLocalOffset);\r\n    }\r\n\r\n    /**\r\n     * Finds the intersection point of the given ray onto the meshes and updates the target.\r\n     * Transformation will be interpolated according to `interpolatePose` and `lerpTime` properties.\r\n     * If no mesh of `meshes` are hit, this does nothing.\r\n     * @param pickInfo The input pickingInfo that will be used to intersect the meshes\r\n     * @returns a boolean indicating if we found a hit to stick to\r\n     */\r\n    public findAndUpdateTarget(pickInfo: PickingInfo): boolean {\r\n        this._hit = false;\r\n        if (!pickInfo.ray) {\r\n            return false;\r\n        }\r\n\r\n        const subPicking = pickInfo.ray.intersectsMeshes(this.meshes)[0];\r\n\r\n        if (this._attachedMesh && subPicking && subPicking.hit && subPicking.pickedMesh) {\r\n            const pose = this._getTargetPose(subPicking);\r\n            if (pose && Vector3.Distance(this._attachedMesh.position, pose.position) < this.maxStickingDistance) {\r\n                this._workingPosition.copyFrom(pose.position);\r\n                this._workingQuaternion.copyFrom(pose.quaternion);\r\n                this._hit = true;\r\n            }\r\n        }\r\n\r\n        return this._hit;\r\n    }\r\n\r\n    private _getAttachPointOffsetToRef(ref: Vector3) {\r\n        if (!this._attachedMesh) {\r\n            ref.setAll(0);\r\n            return;\r\n        }\r\n\r\n        const storedQuat = TmpVectors.Quaternion[0];\r\n        storedQuat.copyFrom(this._attachedMesh.rotationQuaternion!);\r\n        this._attachedMesh.rotationQuaternion!.copyFromFloats(0, 0, 0, 1);\r\n        this._attachedMesh.computeWorldMatrix();\r\n        const boundingMinMax = this._attachedMesh.getHierarchyBoundingVectors();\r\n        const center = TmpVectors.Vector3[0];\r\n        boundingMinMax.max.addToRef(boundingMinMax.min, center);\r\n        center.scaleInPlace(0.5);\r\n        center.z = boundingMinMax.max.z;\r\n        // We max the z coordinate because we want the attach point to be on the back of the mesh\r\n        const invWorld = TmpVectors.Matrix[0];\r\n        this._attachedMesh.getWorldMatrix().invertToRef(invWorld);\r\n        Vector3.TransformCoordinatesToRef(center, invWorld, ref);\r\n        this._attachedMesh.rotationQuaternion!.copyFrom(storedQuat);\r\n    }\r\n\r\n    private _updateTransformToGoal(elapsed: number) {\r\n        if (!this._attachedMesh || !this._hit) {\r\n            return;\r\n        }\r\n\r\n        const oldParent = this._attachedMesh.parent;\r\n        this._attachedMesh.setParent(null);\r\n\r\n        const worldOffset = TmpVectors.Vector3[0];\r\n        Vector3.TransformNormalToRef(this._attachPointLocalOffset, this._attachedMesh.getWorldMatrix(), worldOffset);\r\n\r\n        if (!this.interpolatePose) {\r\n            this._attachedMesh.position.copyFrom(this._workingPosition).subtractInPlace(worldOffset);\r\n            this._attachedMesh.rotationQuaternion!.copyFrom(this._workingQuaternion);\r\n            return;\r\n        }\r\n\r\n        // position\r\n        const interpolatedPosition = new Vector3();\r\n        Vector3.SmoothToRef(this._attachedMesh.position, this._workingPosition, elapsed, this.lerpTime, interpolatedPosition);\r\n        this._attachedMesh.position.copyFrom(interpolatedPosition);\r\n\r\n        // rotation\r\n        const currentRotation = new Quaternion();\r\n        currentRotation.copyFrom(this._attachedMesh.rotationQuaternion!);\r\n        Quaternion.SmoothToRef(currentRotation, this._workingQuaternion, elapsed, this.lerpTime, this._attachedMesh.rotationQuaternion!);\r\n\r\n        this._attachedMesh.setParent(oldParent);\r\n    }\r\n\r\n    private _addObservables() {\r\n        this._pointerObserver = this._scene.onPointerObservable.add((pointerInfo) => {\r\n            if (this.enabled && pointerInfo.type == PointerEventTypes.POINTERMOVE && pointerInfo.pickInfo) {\r\n                this.findAndUpdateTarget(pointerInfo.pickInfo);\r\n            }\r\n        });\r\n\r\n        this._lastTick = Date.now();\r\n        this._onBeforeRender = this._scene.onBeforeRenderObservable.add(() => {\r\n            const tick = Date.now();\r\n            this._updateTransformToGoal(tick - this._lastTick);\r\n            this._lastTick = tick;\r\n        });\r\n    }\r\n\r\n    private _removeObservables() {\r\n        this._scene.onPointerObservable.remove(this._pointerObserver);\r\n        this._scene.onBeforeRenderObservable.remove(this._onBeforeRender);\r\n        this._pointerObserver = null;\r\n        this._onBeforeRender = null;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;;;AAYpE,MAAO,wBAAwB;IAArC,aAAA;QAGY,IAAA,CAAA,uBAAuB,GAAY,mKAAI,UAAO,EAAE,CAAC;QAEjD,IAAA,CAAA,gBAAgB,GAAY,mKAAI,UAAO,EAAE,CAAC;QAC1C,IAAA,CAAA,kBAAkB,GAAe,mKAAI,aAAU,EAAE,CAAC;QAClD,IAAA,CAAA,SAAS,GAAW,CAAC,CAAC,CAAC;QAEvB,IAAA,CAAA,IAAI,GAAG,KAAK,CAAC;QAErB;;WAEG,CACI,IAAA,CAAA,eAAe,GAAW,IAAI,CAAC;QAStC;;WAEG,CACI,IAAA,CAAA,MAAM,GAAmB,EAAE,CAAC;QAOnC;;WAEG,CACI,IAAA,CAAA,eAAe,GAAG,IAAI,CAAC;QAE9B;;;WAGG,CACI,IAAA,CAAA,QAAQ,GAAG,GAAG,CAAC;QAEtB;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAG,IAAI,CAAC;QAEtC;;WAEG,CACI,IAAA,CAAA,OAAO,GAAG,IAAI,CAAC;QAEtB;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,GAAG,CAAC;IA4KrC,CAAC;IArNG;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAOD;;OAEG,CACI,IAAI,GAAA,CAAU,CAAC;IA4BtB;;;;OAIG,CACI,MAAM,CAAC,MAAY,EAAE,KAAa,EAAA;QACrC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;YACzC,IAAI,CAAC,aAAa,CAAC,kBAAkB,kKAAG,aAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACzK,CAAC;QACD,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;QACxE,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,cAAc,CAAC,WAAwB,EAAA;QAC3C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,WAAW,IAAI,WAAW,CAAC,GAAG,EAAE,CAAC;YACjC,MAAM,YAAY,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACvD,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;YAE5C,IAAI,CAAC,YAAY,IAAI,CAAC,WAAW,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,YAAY,CAAC,SAAS,EAAE,CAAC;YAEzB,MAAM,WAAW,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1C,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YACnC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC/C,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAEpC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;+KAC5B,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;+KACnF,UAAO,CAAC,oBAAoB,CAAC,WAAW,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;YACjF,CAAC;YAED,OAAO;gBACH,QAAQ,EAAE,WAAW;gBACrB,UAAU,iKAAE,aAAU,CAAC,oBAAoB,CACvC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,EAC5C,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,EAC3I,CAAC,CACJ;aACJ,CAAC;QACN,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,iBAAiB,GAAA;QACpB,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;OAMG,CACI,mBAAmB,CAAC,QAAqB,EAAA;QAC5C,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,aAAa,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YAC9E,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAC7C,IAAI,IAAI,mKAAI,UAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAClG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC9C,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACrB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAEO,0BAA0B,CAAC,GAAY,EAAA;QAC3C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACd,OAAO;QACX,CAAC;QAED,MAAM,UAAU,kKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5C,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,CAAC;QAC5D,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;QACxC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,2BAA2B,EAAE,CAAC;QACxE,MAAM,MAAM,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACrC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACxD,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACzB,MAAM,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;QAChC,yFAAyF;QACzF,MAAM,QAAQ,kKAAG,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;uKAC1D,UAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IAChE,CAAC;IAEO,sBAAsB,CAAC,OAAe,EAAA;QAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5C,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEnC,MAAM,WAAW,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;uKAC1C,UAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC,CAAC;QAE7G,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACzF,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzE,OAAO;QACX,CAAC;QAED,WAAW;QACX,MAAM,oBAAoB,GAAG,mKAAI,UAAO,EAAE,CAAC;uKAC3C,UAAO,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAC;QACtH,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;QAE3D,WAAW;QACX,MAAM,eAAe,GAAG,mKAAI,aAAU,EAAE,CAAC;QACzC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,CAAC;uKACjE,aAAU,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,kBAAmB,CAAC,CAAC;QAEjI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC5C,CAAC;IAEO,eAAe,GAAA;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACxE,IAAI,IAAI,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,mKAAI,oBAAiB,CAAC,WAAW,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC5F,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACnD,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjE,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB,GAAA;QACtB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 2479, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/followBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/followBehavior.ts"], "sourcesContent": ["import type { Behavior } from \"../behavior\";\r\nimport type { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Camera } from \"../../Cameras/camera\";\r\nimport { Matrix, Quaternion, Vector3 } from \"../../Maths/math.vector\";\r\nimport { Clamp } from \"../../Maths/math.scalar.functions\";\r\nimport type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Epsilon } from \"../../Maths/math.constants\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will follow a camera\r\n * @since 5.0.0\r\n */\r\nexport class FollowBehavior implements Behavior<TransformNode> {\r\n    private _scene: Scene;\r\n\r\n    // Memory cache to avoid GC usage\r\n    private _tmpQuaternion: Quaternion = new Quaternion();\r\n    private _tmpVectors: Vector3[] = [new Vector3(), new Vector3(), new Vector3(), new Vector3(), new Vector3(), new Vector3(), new Vector3()];\r\n    private _tmpMatrix: Matrix = new Matrix();\r\n    private _tmpInvertView: Matrix = new Matrix();\r\n    private _tmpForward: Vector3 = new Vector3();\r\n    private _tmpNodeForward: Vector3 = new Vector3();\r\n    private _tmpPosition: Vector3 = new Vector3();\r\n\r\n    private _followedCamera: Nullable<Camera>;\r\n    private _onBeforeRender: Nullable<Observer<Scene>>;\r\n\r\n    private _workingPosition: Vector3 = new Vector3();\r\n    private _workingQuaternion: Quaternion = new Quaternion();\r\n    private _lastTick: number = -1;\r\n    private _recenterNextUpdate = true;\r\n\r\n    /**\r\n     * Attached node of this behavior\r\n     */\r\n    public attachedNode: Nullable<TransformNode>;\r\n\r\n    /**\r\n     * Set to false if the node should strictly follow the camera without any interpolation time\r\n     */\r\n    public interpolatePose = true;\r\n\r\n    /**\r\n     * Rate of interpolation of position and rotation of the attached node.\r\n     * Higher values will give a slower interpolation.\r\n     */\r\n    public lerpTime = 500;\r\n\r\n    /**\r\n     * If the behavior should ignore the pitch and roll of the camera.\r\n     */\r\n    public ignoreCameraPitchAndRoll = false;\r\n\r\n    /**\r\n     * Pitch offset from camera (relative to Max Distance)\r\n     * Is only effective if `ignoreCameraPitchAndRoll` is set to `true`.\r\n     */\r\n    public pitchOffset = 15;\r\n\r\n    /**\r\n     * The vertical angle from the camera forward axis to the owner will not exceed this value\r\n     */\r\n    public maxViewVerticalDegrees = 30;\r\n\r\n    /**\r\n     * The horizontal angle from the camera forward axis to the owner will not exceed this value\r\n     */\r\n    public maxViewHorizontalDegrees = 30;\r\n    /**\r\n     * The attached node will not reorient until the angle between its forward vector and the vector to the camera is greater than this value\r\n     */\r\n    public orientToCameraDeadzoneDegrees = 60;\r\n    /**\r\n     * Option to ignore distance clamping\r\n     */\r\n    public ignoreDistanceClamp = false;\r\n    /**\r\n     * Option to ignore angle clamping\r\n     */\r\n    public ignoreAngleClamp = false;\r\n    /**\r\n     * Max vertical distance between the attachedNode and camera\r\n     */\r\n    public verticalMaxDistance = 0;\r\n    /**\r\n     *  Default distance from eye to attached node, i.e. the sphere radius\r\n     */\r\n    public defaultDistance = 0.8;\r\n    /**\r\n     *  Max distance from eye to attached node, i.e. the sphere radius\r\n     */\r\n    public maximumDistance = 2;\r\n    /**\r\n     *  Min distance from eye to attached node, i.e. the sphere radius\r\n     */\r\n    public minimumDistance = 0.3;\r\n\r\n    /**\r\n     * Ignore vertical movement and lock the Y position of the object.\r\n     */\r\n    public useFixedVerticalOffset = false;\r\n\r\n    /**\r\n     * Fixed vertical position offset distance.\r\n     */\r\n    public fixedVerticalOffset = 0;\r\n\r\n    /**\r\n     * Enables/disables the behavior\r\n     * @internal\r\n     */\r\n    public _enabled = true;\r\n\r\n    /**\r\n     * The camera that should be followed by this behavior\r\n     */\r\n    public get followedCamera(): Nullable<Camera> {\r\n        return this._followedCamera || this._scene.activeCamera;\r\n    }\r\n\r\n    public set followedCamera(camera: Nullable<Camera>) {\r\n        this._followedCamera = camera;\r\n    }\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"Follow\";\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    /**\r\n     * Attaches the follow behavior\r\n     * @param ownerNode The mesh that will be following once attached\r\n     * @param followedCamera The camera that should be followed by the node\r\n     */\r\n    public attach(ownerNode: TransformNode, followedCamera?: Camera): void {\r\n        this._scene = ownerNode.getScene();\r\n        this.attachedNode = ownerNode;\r\n\r\n        if (followedCamera) {\r\n            this.followedCamera = followedCamera;\r\n        }\r\n\r\n        this._addObservables();\r\n    }\r\n\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        this.attachedNode = null;\r\n        this._removeObservables();\r\n    }\r\n\r\n    /**\r\n     * Recenters the attached node in front of the camera on the next update\r\n     */\r\n    public recenter() {\r\n        this._recenterNextUpdate = true;\r\n    }\r\n\r\n    private _angleBetweenVectorAndPlane(vector: Vector3, normal: Vector3) {\r\n        // Work on copies\r\n        this._tmpVectors[0].copyFrom(vector);\r\n        vector = this._tmpVectors[0];\r\n        this._tmpVectors[1].copyFrom(normal);\r\n        normal = this._tmpVectors[1];\r\n\r\n        vector.normalize();\r\n        normal.normalize();\r\n\r\n        return Math.PI / 2 - Math.acos(Vector3.Dot(vector, normal));\r\n    }\r\n\r\n    private _length2D(vector: Vector3) {\r\n        return Math.sqrt(vector.x * vector.x + vector.z * vector.z);\r\n    }\r\n\r\n    private _distanceClamp(currentToTarget: Vector3, moveToDefault: boolean = false) {\r\n        let minDistance = this.minimumDistance;\r\n        let maxDistance = this.maximumDistance;\r\n        const defaultDistance = this.defaultDistance;\r\n\r\n        const direction = this._tmpVectors[0];\r\n        direction.copyFrom(currentToTarget);\r\n        let currentDistance = direction.length();\r\n        direction.normalizeFromLength(currentDistance);\r\n\r\n        if (this.ignoreCameraPitchAndRoll) {\r\n            // If we don't account for pitch offset, the casted object will float up/down as the reference\r\n            // gets closer to it because we will still be casting in the direction of the pitched offset.\r\n            // To fix this, only modify the XZ position of the object.\r\n            minDistance = this._length2D(direction) * minDistance;\r\n            maxDistance = this._length2D(direction) * maxDistance;\r\n\r\n            const currentDistance2D = this._length2D(currentToTarget);\r\n            direction.scaleInPlace(currentDistance / currentDistance2D);\r\n            currentDistance = currentDistance2D;\r\n        }\r\n\r\n        let clampedDistance = currentDistance;\r\n\r\n        if (moveToDefault) {\r\n            clampedDistance = defaultDistance;\r\n        } else {\r\n            clampedDistance = Clamp(currentDistance, minDistance, maxDistance);\r\n        }\r\n\r\n        currentToTarget.copyFrom(direction).scaleInPlace(clampedDistance);\r\n\r\n        return currentDistance !== clampedDistance;\r\n    }\r\n\r\n    private _applyVerticalClamp(currentToTarget: Vector3) {\r\n        if (this.verticalMaxDistance !== 0) {\r\n            currentToTarget.y = Clamp(currentToTarget.y, -this.verticalMaxDistance, this.verticalMaxDistance);\r\n        }\r\n    }\r\n\r\n    private _toOrientationQuatToRef(vector: Vector3, quaternion: Quaternion) {\r\n        Quaternion.RotationYawPitchRollToRef(Math.atan2(vector.x, vector.z), Math.atan2(vector.y, Math.sqrt(vector.z * vector.z + vector.x * vector.x)), 0, quaternion);\r\n    }\r\n\r\n    private _applyPitchOffset(invertView: Matrix) {\r\n        const forward = this._tmpVectors[0];\r\n        const right = this._tmpVectors[1];\r\n        forward.copyFromFloats(0, 0, this._scene.useRightHandedSystem ? -1 : 1);\r\n        right.copyFromFloats(1, 0, 0);\r\n        Vector3.TransformNormalToRef(forward, invertView, forward);\r\n        forward.y = 0;\r\n        forward.normalize();\r\n        Vector3.TransformNormalToRef(right, invertView, right);\r\n\r\n        Quaternion.RotationAxisToRef(right, (this.pitchOffset * Math.PI) / 180, this._tmpQuaternion);\r\n        forward.rotateByQuaternionToRef(this._tmpQuaternion, forward);\r\n        this._toOrientationQuatToRef(forward, this._tmpQuaternion);\r\n        this._tmpQuaternion.toRotationMatrix(this._tmpMatrix);\r\n\r\n        // Since we already extracted position from the invert view matrix, we can\r\n        // disregard the position part of the matrix in the copy\r\n        invertView.copyFrom(this._tmpMatrix);\r\n    }\r\n\r\n    private _angularClamp(invertView: Matrix, currentToTarget: Vector3): boolean {\r\n        const forward = this._tmpVectors[5];\r\n        forward.copyFromFloats(0, 0, this._scene.useRightHandedSystem ? -1 : 1);\r\n        const right = this._tmpVectors[6];\r\n        right.copyFromFloats(1, 0, 0);\r\n\r\n        // forward and right are related to camera frame of reference\r\n        Vector3.TransformNormalToRef(forward, invertView, forward);\r\n        Vector3.TransformNormalToRef(right, invertView, right);\r\n\r\n        // Up is global Z\r\n        const up = Vector3.UpReadOnly;\r\n\r\n        const dist = currentToTarget.length();\r\n\r\n        if (dist < Epsilon) {\r\n            return false;\r\n        }\r\n\r\n        let angularClamped = false;\r\n        const rotationQuat = this._tmpQuaternion;\r\n\r\n        // X-axis leashing\r\n        if (this.ignoreCameraPitchAndRoll) {\r\n            const angle = Vector3.GetAngleBetweenVectorsOnPlane(currentToTarget, forward, right);\r\n            Quaternion.RotationAxisToRef(right, angle, rotationQuat);\r\n            currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n        } else {\r\n            const angle = -Vector3.GetAngleBetweenVectorsOnPlane(currentToTarget, forward, right);\r\n            const minMaxAngle = ((this.maxViewVerticalDegrees * Math.PI) / 180) * 0.5;\r\n            if (angle < -minMaxAngle) {\r\n                Quaternion.RotationAxisToRef(right, -angle - minMaxAngle, rotationQuat);\r\n                currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n                angularClamped = true;\r\n            } else if (angle > minMaxAngle) {\r\n                Quaternion.RotationAxisToRef(right, -angle + minMaxAngle, rotationQuat);\r\n                currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n                angularClamped = true;\r\n            }\r\n        }\r\n\r\n        // Y-axis leashing\r\n        const angle = this._angleBetweenVectorAndPlane(currentToTarget, right) * (this._scene.useRightHandedSystem ? -1 : 1);\r\n        const minMaxAngle = ((this.maxViewHorizontalDegrees * Math.PI) / 180) * 0.5;\r\n        if (angle < -minMaxAngle) {\r\n            Quaternion.RotationAxisToRef(up, -angle - minMaxAngle, rotationQuat);\r\n            currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n            angularClamped = true;\r\n        } else if (angle > minMaxAngle) {\r\n            Quaternion.RotationAxisToRef(up, -angle + minMaxAngle, rotationQuat);\r\n            currentToTarget.rotateByQuaternionToRef(rotationQuat, currentToTarget);\r\n            angularClamped = true;\r\n        }\r\n\r\n        return angularClamped;\r\n    }\r\n\r\n    private _orientationClamp(currentToTarget: Vector3, rotationQuaternion: Quaternion) {\r\n        // Construct a rotation quat from up vector and target vector\r\n        const toFollowed = this._tmpVectors[0];\r\n        toFollowed.copyFrom(currentToTarget).scaleInPlace(-1).normalize();\r\n\r\n        const up = this._tmpVectors[1];\r\n        const right = this._tmpVectors[2];\r\n        // We use global up vector to orient the following node (global +Y)\r\n        up.copyFromFloats(0, 1, 0);\r\n\r\n        // Gram-Schmidt to create an orthonormal frame\r\n        Vector3.CrossToRef(toFollowed, up, right);\r\n        const length = right.length();\r\n\r\n        if (length < Epsilon) {\r\n            return;\r\n        }\r\n\r\n        right.normalizeFromLength(length);\r\n\r\n        Vector3.CrossToRef(right, toFollowed, up);\r\n        if (this.attachedNode?.getScene().useRightHandedSystem) {\r\n            Quaternion.FromLookDirectionRHToRef(toFollowed, up, rotationQuaternion);\r\n        } else {\r\n            Quaternion.FromLookDirectionLHToRef(toFollowed, up, rotationQuaternion);\r\n        }\r\n    }\r\n\r\n    private _passedOrientationDeadzone(currentToTarget: Vector3, forward: Vector3) {\r\n        const leashToFollow = this._tmpVectors[5];\r\n        leashToFollow.copyFrom(currentToTarget);\r\n        leashToFollow.normalize();\r\n\r\n        const angle = Math.abs(Vector3.GetAngleBetweenVectorsOnPlane(forward, leashToFollow, Vector3.UpReadOnly));\r\n        return (angle * 180) / Math.PI > this.orientToCameraDeadzoneDegrees;\r\n    }\r\n\r\n    private _updateLeashing(camera: Camera) {\r\n        if (this.attachedNode && this._enabled) {\r\n            const oldParent = this.attachedNode.parent;\r\n            this.attachedNode.setParent(null);\r\n\r\n            const worldMatrix = this.attachedNode.getWorldMatrix();\r\n            const currentToTarget = this._workingPosition;\r\n            const rotationQuaternion = this._workingQuaternion;\r\n            const pivot = this.attachedNode.getPivotPoint();\r\n            const invertView = this._tmpInvertView;\r\n            invertView.copyFrom(camera.getViewMatrix());\r\n            invertView.invert();\r\n\r\n            Vector3.TransformCoordinatesToRef(pivot, worldMatrix, currentToTarget);\r\n            const position = this._tmpPosition;\r\n            position.copyFromFloats(0, 0, 0);\r\n            Vector3.TransformCoordinatesToRef(position, worldMatrix, position);\r\n            position.scaleInPlace(-1).subtractInPlace(pivot);\r\n            currentToTarget.subtractInPlace(camera.globalPosition);\r\n\r\n            if (this.ignoreCameraPitchAndRoll) {\r\n                this._applyPitchOffset(invertView);\r\n            }\r\n\r\n            let angularClamped = false;\r\n            const forward = this._tmpForward;\r\n            forward.copyFromFloats(0, 0, this._scene.useRightHandedSystem ? -1 : 1);\r\n            Vector3.TransformNormalToRef(forward, invertView, forward);\r\n\r\n            const nodeForward = this._tmpNodeForward;\r\n            nodeForward.copyFromFloats(0, 0, this._scene.useRightHandedSystem ? -1 : 1);\r\n            Vector3.TransformNormalToRef(nodeForward, worldMatrix, nodeForward);\r\n\r\n            if (this._recenterNextUpdate) {\r\n                currentToTarget.copyFrom(forward).scaleInPlace(this.defaultDistance);\r\n            } else {\r\n                if (this.ignoreAngleClamp) {\r\n                    const currentDistance = currentToTarget.length();\r\n                    currentToTarget.copyFrom(forward).scaleInPlace(currentDistance);\r\n                } else {\r\n                    angularClamped = this._angularClamp(invertView, currentToTarget);\r\n                }\r\n            }\r\n\r\n            let distanceClamped = false;\r\n            if (!this.ignoreDistanceClamp) {\r\n                distanceClamped = this._distanceClamp(currentToTarget, angularClamped);\r\n                this._applyVerticalClamp(currentToTarget);\r\n            }\r\n\r\n            if (this.useFixedVerticalOffset) {\r\n                currentToTarget.y = position.y - camera.globalPosition.y + this.fixedVerticalOffset;\r\n            }\r\n\r\n            if (angularClamped || distanceClamped || this._passedOrientationDeadzone(currentToTarget, nodeForward) || this._recenterNextUpdate) {\r\n                this._orientationClamp(currentToTarget, rotationQuaternion);\r\n            }\r\n\r\n            this._workingPosition.subtractInPlace(pivot);\r\n            this._recenterNextUpdate = false;\r\n\r\n            this.attachedNode.setParent(oldParent);\r\n        }\r\n    }\r\n\r\n    private _updateTransformToGoal(elapsed: number) {\r\n        if (!this.attachedNode || !this.followedCamera || !this._enabled) {\r\n            return;\r\n        }\r\n\r\n        if (!this.attachedNode.rotationQuaternion) {\r\n            this.attachedNode.rotationQuaternion = Quaternion.Identity();\r\n        }\r\n\r\n        const oldParent = this.attachedNode.parent;\r\n        this.attachedNode.setParent(null);\r\n\r\n        if (!this.interpolatePose) {\r\n            this.attachedNode.position.copyFrom(this.followedCamera.globalPosition).addInPlace(this._workingPosition);\r\n            this.attachedNode.rotationQuaternion.copyFrom(this._workingQuaternion);\r\n            return;\r\n        }\r\n\r\n        // position\r\n        const currentDirection = new Vector3();\r\n        currentDirection.copyFrom(this.attachedNode.position).subtractInPlace(this.followedCamera.globalPosition);\r\n        Vector3.SmoothToRef(currentDirection, this._workingPosition, elapsed, this.lerpTime, currentDirection);\r\n        currentDirection.addInPlace(this.followedCamera.globalPosition);\r\n        this.attachedNode.position.copyFrom(currentDirection);\r\n\r\n        // rotation\r\n        const currentRotation = new Quaternion();\r\n        currentRotation.copyFrom(this.attachedNode.rotationQuaternion);\r\n        Quaternion.SmoothToRef(currentRotation, this._workingQuaternion, elapsed, this.lerpTime, this.attachedNode.rotationQuaternion);\r\n\r\n        this.attachedNode.setParent(oldParent);\r\n    }\r\n\r\n    private _addObservables() {\r\n        this._lastTick = Date.now();\r\n        this._onBeforeRender = this._scene.onBeforeRenderObservable.add(() => {\r\n            if (!this.followedCamera) {\r\n                return;\r\n            }\r\n\r\n            const tick = Date.now();\r\n            this._updateLeashing(this.followedCamera);\r\n            this._updateTransformToGoal(tick - this._lastTick);\r\n            this._lastTick = tick;\r\n        });\r\n    }\r\n\r\n    private _removeObservables() {\r\n        if (this._onBeforeRender) {\r\n            this._scene.onBeforeRenderObservable.remove(this._onBeforeRender);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAKA,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EAAE,KAAK,EAAE,MAAM,mCAAmC,CAAC;AAE1D,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;;;;AAM/C,MAAO,cAAc;IAA3B,aAAA;QAGI,iCAAiC;QACzB,IAAA,CAAA,cAAc,GAAe,kKAAI,cAAU,EAAE,CAAC;QAC9C,IAAA,CAAA,WAAW,GAAc;YAAC,mKAAI,UAAO,EAAE;YAAE,mKAAI,UAAO,EAAE;YAAE,mKAAI,UAAO,EAAE;YAAE,kKAAI,WAAO,EAAE;YAAE,mKAAI,UAAO,EAAE;YAAE,mKAAI,UAAO,EAAE;YAAE,mKAAI,UAAO,EAAE;SAAC,CAAC;QACnI,IAAA,CAAA,UAAU,GAAW,mKAAI,SAAM,EAAE,CAAC;QAClC,IAAA,CAAA,cAAc,GAAW,mKAAI,SAAM,EAAE,CAAC;QACtC,IAAA,CAAA,WAAW,GAAY,mKAAI,UAAO,EAAE,CAAC;QACrC,IAAA,CAAA,eAAe,GAAY,kKAAI,WAAO,EAAE,CAAC;QACzC,IAAA,CAAA,YAAY,GAAY,mKAAI,UAAO,EAAE,CAAC;QAKtC,IAAA,CAAA,gBAAgB,GAAY,IAAI,yKAAO,EAAE,CAAC;QAC1C,IAAA,CAAA,kBAAkB,GAAe,mKAAI,aAAU,EAAE,CAAC;QAClD,IAAA,CAAA,SAAS,GAAW,CAAC,CAAC,CAAC;QACvB,IAAA,CAAA,mBAAmB,GAAG,IAAI,CAAC;QAOnC;;WAEG,CACI,IAAA,CAAA,eAAe,GAAG,IAAI,CAAC;QAE9B;;;WAGG,CACI,IAAA,CAAA,QAAQ,GAAG,GAAG,CAAC;QAEtB;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QAExC;;;WAGG,CACI,IAAA,CAAA,WAAW,GAAG,EAAE,CAAC;QAExB;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,EAAE,CAAC;QAEnC;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,EAAE,CAAC;QACrC;;WAEG,CACI,IAAA,CAAA,6BAA6B,GAAG,EAAE,CAAC;QAC1C;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QACnC;;WAEG,CACI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAChC;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC;QAC/B;;WAEG,CACI,IAAA,CAAA,eAAe,GAAG,GAAG,CAAC;QAC7B;;WAEG,CACI,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QAC3B;;WAEG,CACI,IAAA,CAAA,eAAe,GAAG,GAAG,CAAC;QAE7B;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,KAAK,CAAC;QAEtC;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,CAAC,CAAC;QAE/B;;;WAGG,CACI,IAAA,CAAA,QAAQ,GAAG,IAAI,CAAC;IA6V3B,CAAC;IA3VG;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;IAC5D,CAAC;IAED,IAAW,cAAc,CAAC,MAAwB,EAAA;QAC9C,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;IAClC,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG,CACI,IAAI,GAAA,CAAI,CAAC;IAEhB;;;;OAIG,CACI,MAAM,CAAC,SAAwB,EAAE,cAAuB,EAAA;QAC3D,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAE9B,IAAI,cAAc,EAAE,CAAC;YACjB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG,CACI,QAAQ,GAAA;QACX,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;IAEO,2BAA2B,CAAC,MAAe,EAAE,MAAe,EAAA;QAChE,iBAAiB;QACjB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAE7B,MAAM,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,CAAC,SAAS,EAAE,CAAC;QAEnB,OAAO,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,gKAAC,UAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAChE,CAAC;IAEO,SAAS,CAAC,MAAe,EAAA;QAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IAEO,cAAc,CAAC,eAAwB,EAAE,gBAAyB,KAAK,EAAA;QAC3E,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QACvC,IAAI,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QACvC,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE7C,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACtC,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACpC,IAAI,eAAe,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;QACzC,SAAS,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QAE/C,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,8FAA8F;YAC9F,6FAA6F;YAC7F,0DAA0D;YAC1D,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;YACtD,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;YAEtD,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC1D,SAAS,CAAC,YAAY,CAAC,eAAe,GAAG,iBAAiB,CAAC,CAAC;YAC5D,eAAe,GAAG,iBAAiB,CAAC;QACxC,CAAC;QAED,IAAI,eAAe,GAAG,eAAe,CAAC;QAEtC,IAAI,aAAa,EAAE,CAAC;YAChB,eAAe,GAAG,eAAe,CAAC;QACtC,CAAC,MAAM,CAAC;YACJ,eAAe,mLAAG,QAAK,AAAL,EAAM,eAAe,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACvE,CAAC;QAED,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAElE,OAAO,eAAe,KAAK,eAAe,CAAC;IAC/C,CAAC;IAEO,mBAAmB,CAAC,eAAwB,EAAA;QAChD,IAAI,IAAI,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;YACjC,eAAe,CAAC,CAAC,OAAG,oLAAA,AAAK,EAAC,eAAe,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACtG,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,MAAe,EAAE,UAAsB,EAAA;QACnE,4KAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;IACpK,CAAC;IAEO,iBAAiB,CAAC,UAAkB,EAAA;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAClC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9B,yKAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3D,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACd,OAAO,CAAC,SAAS,EAAE,CAAC;uKACpB,UAAO,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;uKAEvD,aAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,AAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,EAAG,GAAG,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC7F,OAAO,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC3D,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtD,0EAA0E;QAC1E,wDAAwD;QACxD,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;IAEO,aAAa,CAAC,UAAkB,EAAE,eAAwB,EAAA;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACpC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAClC,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE9B,6DAA6D;uKAC7D,UAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;uKAC3D,UAAO,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QAEvD,iBAAiB;QACjB,MAAM,EAAE,kKAAG,UAAO,CAAC,UAAU,CAAC;QAE9B,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;QAEtC,IAAI,IAAI,qKAAG,UAAO,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC;QAEzC,kBAAkB;QAClB,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,MAAM,KAAK,kKAAG,UAAO,CAAC,6BAA6B,CAAC,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;2KACrF,aAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;YACzD,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;QAC3E,CAAC,MAAM,CAAC;YACJ,MAAM,KAAK,GAAG,gKAAC,UAAO,CAAC,6BAA6B,CAAC,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;YACtF,MAAM,WAAW,GAAK,AAAF,CAAC,GAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAG,GAAG,CAAC,EAAG,GAAG,CAAC;YAC1E,IAAI,KAAK,GAAG,CAAC,WAAW,EAAE,CAAC;+KACvB,aAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,KAAK,GAAG,WAAW,EAAE,YAAY,CAAC,CAAC;gBACxE,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;gBACvE,cAAc,GAAG,IAAI,CAAC;YAC1B,CAAC,MAAM,IAAI,KAAK,GAAG,WAAW,EAAE,CAAC;+KAC7B,aAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC,KAAK,GAAG,WAAW,EAAE,YAAY,CAAC,CAAC;gBACxE,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;gBACvE,cAAc,GAAG,IAAI,CAAC;YAC1B,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,MAAM,KAAK,GAAG,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrH,MAAM,WAAW,GAAG,AAAE,CAAD,GAAK,CAAC,wBAAwB,GAAG,IAAI,CAAC,EAAE,CAAC,EAAG,GAAG,CAAC,EAAG,GAAG,CAAC;QAC5E,IAAI,KAAK,GAAG,CAAC,WAAW,EAAE,CAAC;YACvB,4KAAU,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,WAAW,EAAE,YAAY,CAAC,CAAC;YACrE,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;YACvE,cAAc,GAAG,IAAI,CAAC;QAC1B,CAAC,MAAM,IAAI,KAAK,GAAG,WAAW,EAAE,CAAC;YAC7B,4KAAU,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,WAAW,EAAE,YAAY,CAAC,CAAC;YACrE,eAAe,CAAC,uBAAuB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;YACvE,cAAc,GAAG,IAAI,CAAC;QAC1B,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,iBAAiB,CAAC,eAAwB,EAAE,kBAA8B,EAAA;QAC9E,6DAA6D;QAC7D,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACvC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAClC,mEAAmE;QACnE,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3B,8CAA8C;uKAC9C,UAAO,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAE9B,IAAI,MAAM,qKAAG,UAAO,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;uKAElC,UAAO,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;QAC1C,IAAI,IAAI,CAAC,YAAY,EAAE,QAAQ,EAAE,CAAC,oBAAoB,EAAE,CAAC;2KACrD,aAAU,CAAC,wBAAwB,CAAC,UAAU,EAAE,EAAE,EAAE,kBAAkB,CAAC,CAAC;QAC5E,CAAC,MAAM,CAAC;0KACJ,cAAU,CAAC,wBAAwB,CAAC,UAAU,EAAE,EAAE,EAAE,kBAAkB,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;IAEO,0BAA0B,CAAC,eAAwB,EAAE,OAAgB,EAAA;QACzE,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC1C,aAAa,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACxC,aAAa,CAAC,SAAS,EAAE,CAAC;QAE1B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,gKAAC,UAAO,CAAC,6BAA6B,CAAC,OAAO,EAAE,aAAa,iKAAE,UAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAC1G,OAAO,AAAC,KAAK,GAAG,GAAG,CAAC,EAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,6BAA6B,CAAC;IACxE,CAAC;IAEO,eAAe,CAAC,MAAc,EAAA;QAClC,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAElC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACvD,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;YAC9C,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;YACnD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC;YACvC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC;YAC5C,UAAU,CAAC,MAAM,EAAE,CAAC;YAEpB,yKAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;YACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC;YACnC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;2KACjC,UAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;YACnE,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACjD,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAEvD,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACvC,CAAC;YAED,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;YACjC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;2KACxE,UAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAE3D,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;YACzC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0KAC5E,WAAO,CAAC,oBAAoB,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;YAEpE,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACzE,CAAC,MAAM,CAAC;gBACJ,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,MAAM,eAAe,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;oBACjD,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;gBACpE,CAAC,MAAM,CAAC;oBACJ,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;gBACrE,CAAC;YACL,CAAC;YAED,IAAI,eAAe,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC5B,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;gBACvE,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;YAC9C,CAAC;YAED,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9B,eAAe,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACxF,CAAC;YAED,IAAI,cAAc,IAAI,eAAe,IAAI,IAAI,CAAC,0BAA0B,CAAC,eAAe,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACjI,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;YAEjC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,OAAe,EAAA;QAC1C,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/D,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;YACxC,IAAI,CAAC,YAAY,CAAC,kBAAkB,kKAAG,aAAU,CAAC,QAAQ,EAAE,CAAC;QACjE,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;QAC3C,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC1G,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvE,OAAO;QACX,CAAC;QAED,WAAW;QACX,MAAM,gBAAgB,GAAG,IAAI,yKAAO,EAAE,CAAC;QACvC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;uKAC1G,UAAO,CAAC,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QACvG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QAChE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QAEtD,WAAW;QACX,MAAM,eAAe,GAAG,IAAI,4KAAU,EAAE,CAAC;QACzC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;uKAC/D,aAAU,CAAC,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAE/H,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IAC3C,CAAC;IAEO,eAAe,GAAA;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACjE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvB,OAAO;YACX,CAAC;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC1C,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;YACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,kBAAkB,GAAA;QACtB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtE,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 2844, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/handConstraintBehavior.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/handConstraintBehavior.ts"], "sourcesContent": ["import type { TransformNode } from \"../../Meshes/transformNode\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { WebXRFeatureName } from \"../../XR/webXRFeaturesManager\";\r\nimport type { WebXRFeaturesManager } from \"../../XR/webXRFeaturesManager\";\r\nimport type { WebXREyeTracking } from \"../../XR/features/WebXREyeTracking\";\r\nimport type { WebXRHandTracking } from \"../../XR/features/WebXRHandTracking\";\r\nimport { WebXRHandJoint } from \"../../XR/features/WebXRHandTracking\";\r\nimport type { WebXRExperienceHelper } from \"../../XR/webXRExperienceHelper\";\r\nimport type { Behavior } from \"../behavior\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Quaternion, TmpVectors, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { Ray } from \"../../Culling/ray\";\r\nimport { Tools } from \"core/Misc/tools\";\r\n\r\n/**\r\n * Zones around the hand\r\n */\r\nexport const enum HandConstraintZone {\r\n    /**\r\n     * Above finger tips\r\n     */\r\n    ABOVE_FINGER_TIPS,\r\n    /**\r\n     * Next to the thumb\r\n     */\r\n    RADIAL_SIDE,\r\n    /**\r\n     * Next to the pinky finger\r\n     */\r\n    ULNAR_SIDE,\r\n    /**\r\n     * Below the wrist\r\n     */\r\n    BELOW_WRIST,\r\n}\r\n\r\n/**\r\n * Orientations for the hand zones and for the attached node\r\n */\r\nexport const enum HandConstraintOrientation {\r\n    /**\r\n     * Orientation is towards the camera\r\n     */\r\n    LOOK_AT_CAMERA,\r\n    /**\r\n     * Orientation is determined by the rotation of the palm\r\n     */\r\n    HAND_ROTATION,\r\n}\r\n\r\n/**\r\n * Orientations for the hand zones and for the attached node\r\n */\r\nexport const enum HandConstraintVisibility {\r\n    /**\r\n     * Constraint is always visible\r\n     */\r\n    ALWAYS_VISIBLE,\r\n    /**\r\n     * Constraint is only visible when the palm is up\r\n     */\r\n    PALM_UP,\r\n    /**\r\n     * Constraint is only visible when the user is looking at the constraint.\r\n     * Uses XR Eye Tracking if enabled/available, otherwise uses camera direction\r\n     */\r\n    GAZE_FOCUS,\r\n    /**\r\n     * Constraint is only visible when the palm is up and the user is looking at it\r\n     */\r\n    PALM_AND_GAZE,\r\n}\r\n\r\ntype HandPoseInfo = {\r\n    position: Vector3;\r\n    quaternion: Quaternion;\r\n    id: string;\r\n};\r\n\r\n/**\r\n * Hand constraint behavior that makes the attached `TransformNode` follow hands in XR experiences.\r\n * @since 5.0.0\r\n */\r\nexport class HandConstraintBehavior implements Behavior<TransformNode> {\r\n    private _scene: Scene;\r\n    private _node: TransformNode;\r\n    private _eyeTracking: Nullable<WebXREyeTracking>;\r\n    private _handTracking: Nullable<WebXRHandTracking>;\r\n    private _sceneRenderObserver: Nullable<Observer<Scene>> = null;\r\n    private _zoneAxis: { [id: number]: Vector3 } = {};\r\n\r\n    /**\r\n     * Sets the HandConstraintVisibility level for the hand constraint\r\n     */\r\n    public handConstraintVisibility: HandConstraintVisibility = HandConstraintVisibility.PALM_AND_GAZE;\r\n\r\n    /**\r\n     * A number from 0.0 to 1.0, marking how restricted the direction the palm faces is for the attached node to be enabled.\r\n     * A 1 means the palm must be directly facing the user before the node is enabled, a 0 means it is always enabled.\r\n     * Used with HandConstraintVisibility.PALM_UP\r\n     */\r\n    public palmUpStrictness: number = 0.95;\r\n\r\n    /**\r\n     * The radius in meters around the center of the hand that the user must gaze inside for the attached node to be enabled and appear.\r\n     * Used with HandConstraintVisibility.GAZE_FOCUS\r\n     */\r\n    public gazeProximityRadius: number = 0.15;\r\n\r\n    /**\r\n     * Offset distance from the hand in meters\r\n     */\r\n    public targetOffset: number = 0.1;\r\n\r\n    /**\r\n     * Where to place the node regarding the center of the hand.\r\n     */\r\n    public targetZone: HandConstraintZone = HandConstraintZone.ULNAR_SIDE;\r\n\r\n    /**\r\n     * Orientation mode of the 4 zones around the hand\r\n     */\r\n    public zoneOrientationMode: HandConstraintOrientation = HandConstraintOrientation.HAND_ROTATION;\r\n    /**\r\n     * Orientation mode of the node attached to this behavior\r\n     */\r\n    public nodeOrientationMode: HandConstraintOrientation = HandConstraintOrientation.HAND_ROTATION;\r\n\r\n    /**\r\n     * Set the hand this behavior should follow. If set to \"none\", it will follow any visible hand (prioritising the left one).\r\n     */\r\n    public handedness: XRHandedness = \"none\";\r\n\r\n    /**\r\n     * Rate of interpolation of position and rotation of the attached node.\r\n     * Higher values will give a slower interpolation.\r\n     */\r\n    public lerpTime = 100;\r\n\r\n    /**\r\n     * Builds a hand constraint behavior\r\n     */\r\n    constructor() {\r\n        // For a right hand\r\n        this._zoneAxis[HandConstraintZone.ABOVE_FINGER_TIPS] = new Vector3(0, 1, 0);\r\n        this._zoneAxis[HandConstraintZone.RADIAL_SIDE] = new Vector3(-1, 0, 0);\r\n        this._zoneAxis[HandConstraintZone.ULNAR_SIDE] = new Vector3(1, 0, 0);\r\n        this._zoneAxis[HandConstraintZone.BELOW_WRIST] = new Vector3(0, -1, 0);\r\n    }\r\n\r\n    /** gets or sets behavior's name */\r\n    public get name() {\r\n        return \"HandConstraint\";\r\n    }\r\n\r\n    /** Enable the behavior */\r\n    public enable() {\r\n        this._node.setEnabled(true);\r\n    }\r\n\r\n    /** Disable the behavior */\r\n    public disable() {\r\n        this._node.setEnabled(false);\r\n    }\r\n\r\n    private _getHandPose(): Nullable<HandPoseInfo> {\r\n        if (!this._handTracking) {\r\n            return null;\r\n        }\r\n\r\n        // Retrieve any available hand, starting by the left\r\n        let hand;\r\n        if (this.handedness === \"none\") {\r\n            hand = this._handTracking.getHandByHandedness(\"left\") || this._handTracking.getHandByHandedness(\"right\");\r\n        } else {\r\n            hand = this._handTracking.getHandByHandedness(this.handedness);\r\n        }\r\n\r\n        if (hand) {\r\n            const pinkyMetacarpal = hand.getJointMesh(WebXRHandJoint.PINKY_FINGER_METACARPAL);\r\n            const middleMetacarpal = hand.getJointMesh(WebXRHandJoint.MIDDLE_FINGER_METACARPAL);\r\n            const wrist = hand.getJointMesh(WebXRHandJoint.WRIST);\r\n\r\n            if (wrist && middleMetacarpal && pinkyMetacarpal) {\r\n                const handPose: HandPoseInfo = { position: middleMetacarpal.absolutePosition, quaternion: new Quaternion(), id: hand.xrController.uniqueId };\r\n\r\n                // palm forward\r\n                const up = TmpVectors.Vector3[0];\r\n                const forward = TmpVectors.Vector3[1];\r\n                const left = TmpVectors.Vector3[2];\r\n                up.copyFrom(middleMetacarpal.absolutePosition).subtractInPlace(wrist.absolutePosition).normalize();\r\n                forward.copyFrom(pinkyMetacarpal.absolutePosition).subtractInPlace(middleMetacarpal.absolutePosition).normalize();\r\n\r\n                // Create vectors for a rotation quaternion, where forward points out from the palm\r\n                Vector3.CrossToRef(up, forward, forward);\r\n                Vector3.CrossToRef(forward, up, left);\r\n\r\n                Quaternion.FromLookDirectionLHToRef(forward, up, handPose.quaternion);\r\n\r\n                return handPose;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Initializes the hand constraint behavior\r\n     */\r\n    public init() {}\r\n\r\n    /**\r\n     * Attaches the hand constraint to a `TransformNode`\r\n     * @param node defines the node to attach the behavior to\r\n     */\r\n    public attach(node: TransformNode): void {\r\n        this._node = node;\r\n        this._scene = node.getScene();\r\n\r\n        if (!this._node.rotationQuaternion) {\r\n            this._node.rotationQuaternion = Quaternion.RotationYawPitchRoll(this._node.rotation.y, this._node.rotation.x, this._node.rotation.z);\r\n        }\r\n\r\n        let lastTick = Date.now();\r\n        this._sceneRenderObserver = this._scene.onBeforeRenderObservable.add(() => {\r\n            const pose = this._getHandPose();\r\n\r\n            this._node.reservedDataStore = this._node.reservedDataStore || {};\r\n            this._node.reservedDataStore.nearInteraction = this._node.reservedDataStore.nearInteraction || {};\r\n            this._node.reservedDataStore.nearInteraction.excludedControllerId = null;\r\n\r\n            if (pose) {\r\n                const zoneOffset = TmpVectors.Vector3[0];\r\n                const camera = this._scene.activeCamera;\r\n\r\n                zoneOffset.copyFrom(this._zoneAxis[this.targetZone]);\r\n\r\n                const cameraLookAtQuaternion = TmpVectors.Quaternion[0];\r\n                if (camera && (this.zoneOrientationMode === HandConstraintOrientation.LOOK_AT_CAMERA || this.nodeOrientationMode === HandConstraintOrientation.LOOK_AT_CAMERA)) {\r\n                    const toCamera = TmpVectors.Vector3[1];\r\n                    toCamera.copyFrom(camera.position).subtractInPlace(pose.position).normalize();\r\n                    if (this._scene.useRightHandedSystem) {\r\n                        Quaternion.FromLookDirectionRHToRef(toCamera, Vector3.UpReadOnly, cameraLookAtQuaternion);\r\n                    } else {\r\n                        Quaternion.FromLookDirectionLHToRef(toCamera, Vector3.UpReadOnly, cameraLookAtQuaternion);\r\n                    }\r\n                }\r\n\r\n                if (this.zoneOrientationMode === HandConstraintOrientation.HAND_ROTATION) {\r\n                    pose.quaternion.toRotationMatrix(TmpVectors.Matrix[0]);\r\n                } else {\r\n                    cameraLookAtQuaternion.toRotationMatrix(TmpVectors.Matrix[0]);\r\n                }\r\n\r\n                Vector3.TransformNormalToRef(zoneOffset, TmpVectors.Matrix[0], zoneOffset);\r\n                zoneOffset.scaleInPlace(this.targetOffset);\r\n\r\n                const targetPosition = TmpVectors.Vector3[2];\r\n                const targetRotation = TmpVectors.Quaternion[1];\r\n                targetPosition.copyFrom(pose.position).addInPlace(zoneOffset);\r\n\r\n                if (this.nodeOrientationMode === HandConstraintOrientation.HAND_ROTATION) {\r\n                    targetRotation.copyFrom(pose.quaternion);\r\n                } else {\r\n                    targetRotation.copyFrom(cameraLookAtQuaternion);\r\n                }\r\n\r\n                const elapsed = Date.now() - lastTick;\r\n\r\n                Vector3.SmoothToRef(this._node.position, targetPosition, elapsed, this.lerpTime, this._node.position);\r\n                Quaternion.SmoothToRef(this._node.rotationQuaternion!, targetRotation, elapsed, this.lerpTime, this._node.rotationQuaternion!);\r\n\r\n                this._node.reservedDataStore.nearInteraction.excludedControllerId = pose.id;\r\n            }\r\n\r\n            this._setVisibility(pose);\r\n\r\n            lastTick = Date.now();\r\n        });\r\n    }\r\n\r\n    private _setVisibility(pose: Nullable<HandPoseInfo>) {\r\n        let palmVisible = true;\r\n        let gazeVisible = true;\r\n        const camera = this._scene.activeCamera;\r\n\r\n        if (camera) {\r\n            const cameraForward = camera.getForwardRay();\r\n\r\n            if (this.handConstraintVisibility === HandConstraintVisibility.GAZE_FOCUS || this.handConstraintVisibility === HandConstraintVisibility.PALM_AND_GAZE) {\r\n                gazeVisible = false;\r\n                let gaze: Ray | undefined;\r\n                if (this._eyeTracking) {\r\n                    gaze = this._eyeTracking.getEyeGaze()!;\r\n                }\r\n\r\n                gaze = gaze || cameraForward;\r\n\r\n                const gazeToBehavior = TmpVectors.Vector3[0];\r\n                if (pose) {\r\n                    pose.position.subtractToRef(gaze.origin, gazeToBehavior);\r\n                } else {\r\n                    this._node.getAbsolutePosition().subtractToRef(gaze.origin, gazeToBehavior);\r\n                }\r\n\r\n                const projectedDistance = Vector3.Dot(gazeToBehavior, gaze.direction);\r\n                const projectedSquared = projectedDistance * projectedDistance;\r\n\r\n                if (projectedDistance > 0) {\r\n                    const radiusSquared = gazeToBehavior.lengthSquared() - projectedSquared;\r\n                    if (radiusSquared < this.gazeProximityRadius * this.gazeProximityRadius) {\r\n                        gazeVisible = true;\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (this.handConstraintVisibility === HandConstraintVisibility.PALM_UP || this.handConstraintVisibility === HandConstraintVisibility.PALM_AND_GAZE) {\r\n                palmVisible = false;\r\n\r\n                if (pose) {\r\n                    const palmDirection = TmpVectors.Vector3[0];\r\n                    Vector3.LeftHandedForwardReadOnly.rotateByQuaternionToRef(pose.quaternion, palmDirection);\r\n\r\n                    if (Vector3.Dot(palmDirection, cameraForward.direction) > this.palmUpStrictness * 2 - 1) {\r\n                        palmVisible = true;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        this._node.setEnabled(palmVisible && gazeVisible);\r\n    }\r\n\r\n    /**\r\n     * Detaches the behavior from the `TransformNode`\r\n     */\r\n    public detach(): void {\r\n        this._scene.onBeforeRenderObservable.remove(this._sceneRenderObserver);\r\n    }\r\n\r\n    /**\r\n     * Links the behavior to the XR experience in which to retrieve hand transform information.\r\n     * @param xr xr experience\r\n     */\r\n    public linkToXRExperience(xr: WebXRExperienceHelper | WebXRFeaturesManager) {\r\n        const featuresManager: WebXRFeaturesManager = (xr as WebXRExperienceHelper).featuresManager ? (xr as WebXRExperienceHelper).featuresManager : (xr as WebXRFeaturesManager);\r\n        if (!featuresManager) {\r\n            Tools.Error(\"XR features manager must be available or provided directly for the Hand Menu to work\");\r\n        } else {\r\n            try {\r\n                this._eyeTracking = featuresManager.getEnabledFeature(WebXRFeatureName.EYE_TRACKING) as WebXREyeTracking;\r\n            } catch {}\r\n\r\n            try {\r\n                this._handTracking = featuresManager.getEnabledFeature(WebXRFeatureName.HAND_TRACKING) as WebXRHandTracking;\r\n            } catch {\r\n                Tools.Error(\"Hand tracking must be enabled for the Hand Menu to work\");\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AASjE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAE1E,OAAO,EAAE,KAAK,EAAE,4BAAwB;;;;AAKxC,IAAkB,kBAiBjB;AAjBD,CAAA,SAAkB,kBAAkB;IAChC;;OAEG,CACH,kBAAA,CAAA,kBAAA,CAAA,oBAAA,GAAA,EAAA,GAAA,mBAAiB,CAAA;IACjB;;OAEG,CACH,kBAAA,CAAA,kBAAA,CAAA,cAAA,GAAA,EAAA,GAAA,aAAW,CAAA;IACX;;OAEG,CACH,kBAAA,CAAA,kBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;IACV;;OAEG,CACH,kBAAA,CAAA,kBAAA,CAAA,cAAA,GAAA,EAAA,GAAA,aAAW,CAAA;AACf,CAAC,EAjBiB,kBAAkB,IAAA,CAAlB,kBAAkB,GAAA,CAAA,CAAA,GAiBnC;AAKD,IAAkB,yBASjB;AATD,CAAA,SAAkB,yBAAyB;IACvC;;OAEG,CACH,yBAAA,CAAA,yBAAA,CAAA,iBAAA,GAAA,EAAA,GAAA,gBAAc,CAAA;IACd;;OAEG,CACH,yBAAA,CAAA,yBAAA,CAAA,gBAAA,GAAA,EAAA,GAAA,eAAa,CAAA;AACjB,CAAC,EATiB,yBAAyB,IAAA,CAAzB,yBAAyB,GAAA,CAAA,CAAA,GAS1C;AAKD,IAAkB,wBAkBjB;AAlBD,CAAA,SAAkB,wBAAwB;IACtC;;OAEG,CACH,wBAAA,CAAA,wBAAA,CAAA,iBAAA,GAAA,EAAA,GAAA,gBAAc,CAAA;IACd;;OAEG,CACH,wBAAA,CAAA,wBAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAO,CAAA;IACP;;;OAGG,CACH,wBAAA,CAAA,wBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;IACV;;OAEG,CACH,wBAAA,CAAA,wBAAA,CAAA,gBAAA,GAAA,EAAA,GAAA,eAAa,CAAA;AACjB,CAAC,EAlBiB,wBAAwB,IAAA,CAAxB,wBAAwB,GAAA,CAAA,CAAA,GAkBzC;AAYK,MAAO,sBAAsB;IAwD/B;;OAEG,CACH,aAAA;QAtDQ,IAAA,CAAA,oBAAoB,GAA8B,IAAI,CAAC;QACvD,IAAA,CAAA,SAAS,GAA8B,CAAA,CAAE,CAAC;QAElD;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAA,EAAA,0CAAA,GAAoE;QAEnG;;;;WAIG,CACI,IAAA,CAAA,gBAAgB,GAAW,IAAI,CAAC;QAEvC;;;WAGG,CACI,IAAA,CAAA,mBAAmB,GAAW,IAAI,CAAC;QAE1C;;WAEG,CACI,IAAA,CAAA,YAAY,GAAW,GAAG,CAAC;QAElC;;WAEG,CACI,IAAA,CAAA,UAAU,GAAA,EAAA,iCAAA,GAAqD;QAEtE;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAA,EAAA,2CAAA,GAAsE;QAChG;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAA,EAAA,2CAAA,GAAsE;QAEhG;;WAEG,CACI,IAAA,CAAA,UAAU,GAAiB,MAAM,CAAC;QAEzC;;;WAGG,CACI,IAAA,CAAA,QAAQ,GAAG,GAAG,CAAC;QAMlB,mBAAmB;QACnB,IAAI,CAAC,SAAS,CAAA,EAAA,wCAAA,IAAsC,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,CAAA,EAAA,kCAAA,IAAgC,GAAG,mKAAI,UAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvE,IAAI,CAAC,SAAS,CAAA,EAAA,iCAAA,IAA+B,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrE,IAAI,CAAC,SAAS,CAAA,EAAA,kCAAA,IAAgC,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3E,CAAC;IAED,iCAAA,EAAmC,CACnC,IAAW,IAAI,GAAA;QACX,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED,wBAAA,EAA0B,CACnB,MAAM,GAAA;QACT,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,yBAAA,EAA2B,CACpB,OAAO,GAAA;QACV,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAEO,YAAY,GAAA;QAChB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,oDAAoD;QACpD,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YAC7B,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAC7G,CAAC,MAAM,CAAC;YACJ,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACP,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAA,0BAAA,0CAAA,GAAwC,CAAC;YAClF,MAAM,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAA,2BAAA,2CAAA,GAAyC,CAAC;YACpF,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAA,QAAA,wBAAA,GAAsB,CAAC;YAEtD,IAAI,KAAK,IAAI,gBAAgB,IAAI,eAAe,EAAE,CAAC;gBAC/C,MAAM,QAAQ,GAAiB;oBAAE,QAAQ,EAAE,gBAAgB,CAAC,gBAAgB;oBAAE,UAAU,EAAE,mKAAI,aAAU,EAAE;oBAAE,EAAE,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ;gBAAA,CAAE,CAAC;gBAE7I,eAAe;gBACf,MAAM,EAAE,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACjC,MAAM,OAAO,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,IAAI,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACnC,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,SAAS,EAAE,CAAC;gBACnG,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,SAAS,EAAE,CAAC;gBAElH,mFAAmF;+KACnF,UAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;+KACzC,UAAO,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;+KAEtC,aAAU,CAAC,wBAAwB,CAAC,OAAO,EAAE,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;gBAEtE,OAAO,QAAQ,CAAC;YACpB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,IAAI,GAAA,CAAI,CAAC;IAEhB;;;OAGG,CACI,MAAM,CAAC,IAAmB,EAAA;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,kBAAkB,kKAAG,aAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACzI,CAAC;QAED,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACtE,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAEjC,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAA,CAAE,CAAC;YAClE,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,IAAI,CAAA,CAAE,CAAC;YAClG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,oBAAoB,GAAG,IAAI,CAAC;YAEzE,IAAI,IAAI,EAAE,CAAC;gBACP,MAAM,UAAU,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACzC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;gBAExC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBAErD,MAAM,sBAAsB,kKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBACxD,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,KAAA,EAAA,4CAAA,EAA6C,KAAI,IAAI,CAAC,mBAAmB,KAAA,EAAA,4CAAA,GAA6C,CAAC,EAAE,CAAC;oBAC7J,MAAM,QAAQ,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACvC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,EAAE,CAAC;oBAC9E,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;uLACnC,aAAU,CAAC,wBAAwB,CAAC,QAAQ,iKAAE,UAAO,CAAC,UAAU,EAAE,sBAAsB,CAAC,CAAC;oBAC9F,CAAC,MAAM,CAAC;uLACJ,aAAU,CAAC,wBAAwB,CAAC,QAAQ,iKAAE,UAAO,CAAC,UAAU,EAAE,sBAAsB,CAAC,CAAC;oBAC9F,CAAC;gBACL,CAAC;gBAED,IAAI,IAAI,CAAC,mBAAmB,KAAA,EAAA,2CAAA,EAA4C,GAAE,CAAC;oBACvE,IAAI,CAAC,UAAU,CAAC,gBAAgB,gKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,CAAC,MAAM,CAAC;oBACJ,sBAAsB,CAAC,gBAAgB,gKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClE,CAAC;+KAED,UAAO,CAAC,oBAAoB,CAAC,UAAU,iKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;gBAC3E,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAE3C,MAAM,cAAc,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC7C,MAAM,cAAc,kKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;gBAChD,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gBAE9D,IAAI,IAAI,CAAC,mBAAmB,KAAA,EAAA,2CAAA,EAA4C,GAAE,CAAC;oBACvE,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC7C,CAAC,MAAM,CAAC;oBACJ,cAAc,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;gBACpD,CAAC;gBAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC;+KAEtC,UAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;+KACtG,aAAU,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAmB,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAmB,CAAC,CAAC;gBAE/H,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,eAAe,CAAC,oBAAoB,GAAG,IAAI,CAAC,EAAE,CAAC;YAChF,CAAC;YAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAE1B,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,cAAc,CAAC,IAA4B,EAAA;QAC/C,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAExC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;YAE7C,IAAI,IAAI,CAAC,wBAAwB,KAAA,EAAA,uCAAA,EAAwC,KAAI,IAAI,CAAC,wBAAwB,KAAA,EAAA,0CAAA,EAA2C,GAAE,CAAC;gBACpJ,WAAW,GAAG,KAAK,CAAC;gBACpB,IAAI,IAAqB,CAAC;gBAC1B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAG,CAAC;gBAC3C,CAAC;gBAED,IAAI,GAAG,IAAI,IAAI,aAAa,CAAC;gBAE7B,MAAM,cAAc,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC7C,IAAI,IAAI,EAAE,CAAC;oBACP,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;gBAC7D,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;gBAChF,CAAC;gBAED,MAAM,iBAAiB,kKAAG,UAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtE,MAAM,gBAAgB,GAAG,iBAAiB,GAAG,iBAAiB,CAAC;gBAE/D,IAAI,iBAAiB,GAAG,CAAC,EAAE,CAAC;oBACxB,MAAM,aAAa,GAAG,cAAc,CAAC,aAAa,EAAE,GAAG,gBAAgB,CAAC;oBACxE,IAAI,aAAa,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBACtE,WAAW,GAAG,IAAI,CAAC;oBACvB,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,wBAAwB,KAAA,EAAA,oCAAA,EAAqC,KAAI,IAAI,CAAC,wBAAwB,KAAA,EAAA,0CAAA,EAA2C,GAAE,CAAC;gBACjJ,WAAW,GAAG,KAAK,CAAC;gBAEpB,IAAI,IAAI,EAAE,CAAC;oBACP,MAAM,aAAa,kKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;mLAC5C,UAAO,CAAC,yBAAyB,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;oBAE1F,mKAAI,UAAO,CAAC,GAAG,CAAC,aAAa,EAAE,aAAa,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;wBACtF,WAAW,GAAG,IAAI,CAAC;oBACvB,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,IAAI,WAAW,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG,CACI,kBAAkB,CAAC,EAAgD,EAAA;QACtE,MAAM,eAAe,GAA0B,EAA4B,CAAC,eAAe,CAAC,CAAC,CAAE,EAA4B,CAAC,eAAe,CAAC,CAAC,CAAE,EAA2B,CAAC;QAC3K,IAAI,CAAC,eAAe,EAAE,CAAC;iKACnB,QAAK,CAAC,KAAK,CAAC,sFAAsF,CAAC,CAAC;QACxG,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC;gBACD,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC,iBAAiB,mKAAC,mBAAgB,CAAC,YAAY,CAAqB,CAAC;YAC7G,CAAC,CAAC,OAAM,CAAC,CAAC;YAEV,IAAI,CAAC;gBACD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,iBAAiB,mKAAC,mBAAgB,CAAC,aAAa,CAAsB,CAAC;YAChH,CAAC,CAAC,OAAM,CAAC;qKACL,QAAK,CAAC,KAAK,CAAC,yDAAyD,CAAC,CAAC;YAC3E,CAAC;QACL,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 3109, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/Meshes/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/Meshes/index.ts"], "sourcesContent": ["export * from \"./attachToBoxBehavior\";\r\nexport * from \"./fadeInOutBehavior\";\r\nexport * from \"./multiPointerScaleBehavior\";\r\nexport * from \"./pointerDragBehavior\";\r\nexport * from \"./sixDofDragBehavior\";\r\nexport * from \"./surfaceMagnetismBehavior\";\r\nexport * from \"./baseSixDofDragBehavior\";\r\nexport * from \"./followBehavior\";\r\nexport * from \"./handConstraintBehavior\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,uBAAuB,CAAC;AACtC,cAAc,qBAAqB,CAAC;AACpC,cAAc,6BAA6B,CAAC;AAC5C,cAAc,uBAAuB,CAAC;AACtC,cAAc,sBAAsB,CAAC;AACrC,cAAc,4BAA4B,CAAC;AAC3C,cAAc,0BAA0B,CAAC;AACzC,cAAc,kBAAkB,CAAC;AACjC,cAAc,0BAA0B,CAAC", "debugId": null}}, {"offset": {"line": 3147, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Behaviors/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Behaviors/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-restricted-imports */\r\nexport * from \"./behavior\";\r\nexport * from \"./Cameras/index\";\r\nexport * from \"./Meshes/index\";\r\n"], "names": [], "mappings": "AAAA,2DAAA,EAA6D;AAC7D,cAAc,YAAY,CAAC;AAC3B,cAAc,iBAAiB,CAAC;AAChC,cAAc,gBAAgB,CAAC", "debugId": null}}]}