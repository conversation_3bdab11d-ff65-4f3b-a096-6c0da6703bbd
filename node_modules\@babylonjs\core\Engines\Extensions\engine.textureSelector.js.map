{"version": 3, "file": "engine.textureSelector.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.textureSelector.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAqDnC,SAAS,mBAAmB,CAAe,GAAW;IAClD,MAAM,SAAS,GAAG,CAAC,KAAa,EAAE,EAAE;QAChC,MAAM,eAAe,GAAW,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;QACtD,OAAO,GAAG,IAAI,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QACvF,OAAO,GAAG,CAAC;IACf,CAAC;IAED,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACrC,MAAM,gBAAgB,GAAG,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC9C,MAAM,WAAW,GAAG,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7F,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,GAAG,WAAW,CAAC;AACrG,CAAC;AAED,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,mBAAmB,EAAE;IACzD,GAAG,EAAE;QACD,wEAAwE;QACxE,qFAAqF;QACrF,2DAA2D;QAC3D,6EAA6E;QAC7E,4EAA4E;QAC5E,MAAM,iBAAiB,GAAa,EAAE,CAAC;QACvC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YACnB,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAClB,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,oBAAoB,EAAE;IAC1D,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC;IAC5C,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,SAAS,CAAC,8BAA8B,GAAG,UAAU,YAA2B;IACnF,IAAI,CAAC,2BAA2B,GAAG,YAAY,CAAC;AACpD,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,gBAA+B;IAC9E,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;IACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5D,IAAI,iBAAiB,CAAC,CAAC,CAAC,KAAK,gBAAgB,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC7D,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3D,OAAO,CAAC,IAAI,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,CAAC;QACL,CAAC;IACL,CAAC;IACD,4EAA4E;IAC5E,iCAAiC;IACjC,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;IAC9B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACjC,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { Engine } from \"../engine\";\r\n\r\ndeclare module \"../../Engines/engine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Engine {\r\n        /** @internal */\r\n        _excludedCompressedTextures: string[];\r\n\r\n        /** @internal */\r\n        _textureFormatInUse: string;\r\n\r\n        /**\r\n         * Gets the list of texture formats supported\r\n         */\r\n        readonly texturesSupported: Array<string>;\r\n\r\n        /**\r\n         * Gets the texture format in use\r\n         */\r\n        readonly textureFormatInUse: Nullable<string>;\r\n\r\n        /**\r\n         * Set the compressed texture extensions or file names to skip.\r\n         *\r\n         * @param skippedFiles defines the list of those texture files you want to skip\r\n         * Example: [\".dds\", \".env\", \"myfile.png\"]\r\n         */\r\n        setCompressedTextureExclusions(skippedFiles: Array<string>): void;\r\n\r\n        /**\r\n         * Set the compressed texture format to use, based on the formats you have, and the formats\r\n         * supported by the hardware / browser.\r\n         *\r\n         * Khronos Texture Container (.ktx) files are used to support this.  This format has the\r\n         * advantage of being specifically designed for OpenGL.  Header elements directly correspond\r\n         * to API arguments needed to compressed textures.  This puts the burden on the container\r\n         * generator to house the arcane code for determining these for current & future formats.\r\n         *\r\n         * for description see https://www.khronos.org/opengles/sdk/tools/KTX/\r\n         * for file layout see https://www.khronos.org/opengles/sdk/tools/KTX/file_format_spec/\r\n         *\r\n         * Note: The result of this call is not taken into account when a texture is base64.\r\n         *\r\n         * @param formatsAvailable defines the list of those format families you have created\r\n         * on your server.  Syntax: '-' + format family + '.ktx'.  (Case and order do not matter.)\r\n         *\r\n         * Current families are astc, dxt, pvrtc, etc2, & etc1.\r\n         * @returns The extension selected.\r\n         */\r\n        setTextureFormatToUse(formatsAvailable: Array<string>): Nullable<string>;\r\n    }\r\n}\r\n\r\nfunction TransformTextureUrl(this: Engine, url: string): string {\r\n    const excludeFn = (entry: string) => {\r\n        const strRegExPattern: string = \"\\\\b\" + entry + \"\\\\b\";\r\n        return url && (url === entry || url.match(new RegExp(strRegExPattern, \"g\")));\r\n    };\r\n\r\n    if (this._excludedCompressedTextures && this._excludedCompressedTextures.some(excludeFn)) {\r\n        return url;\r\n    }\r\n\r\n    const lastDot = url.lastIndexOf(\".\");\r\n    const lastQuestionMark = url.lastIndexOf(\"?\");\r\n    const querystring = lastQuestionMark > -1 ? url.substring(lastQuestionMark, url.length) : \"\";\r\n    return (lastDot > -1 ? url.substring(0, lastDot) : url) + this._textureFormatInUse + querystring;\r\n}\r\n\r\nObject.defineProperty(Engine.prototype, \"texturesSupported\", {\r\n    get: function (this: Engine) {\r\n        // Intelligently add supported compressed formats in order to check for.\r\n        // Check for ASTC support first as it is most powerful and to be very cross platform.\r\n        // Next PVRTC & DXT, which are probably superior to ETC1/2.\r\n        // Likely no hardware which supports both PVR & DXT, so order matters little.\r\n        // ETC2 is newer and handles ETC1 (no alpha capability), so check for first.\r\n        const texturesSupported: string[] = [];\r\n        if (this._caps.astc) {\r\n            texturesSupported.push(\"-astc.ktx\");\r\n        }\r\n        if (this._caps.s3tc) {\r\n            texturesSupported.push(\"-dxt.ktx\");\r\n        }\r\n        if (this._caps.pvrtc) {\r\n            texturesSupported.push(\"-pvrtc.ktx\");\r\n        }\r\n        if (this._caps.etc2) {\r\n            texturesSupported.push(\"-etc2.ktx\");\r\n        }\r\n        if (this._caps.etc1) {\r\n            texturesSupported.push(\"-etc1.ktx\");\r\n        }\r\n        return texturesSupported;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Engine.prototype, \"textureFormatInUse\", {\r\n    get: function (this: Engine) {\r\n        return this._textureFormatInUse || null;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nEngine.prototype.setCompressedTextureExclusions = function (skippedFiles: Array<string>): void {\r\n    this._excludedCompressedTextures = skippedFiles;\r\n};\r\n\r\nEngine.prototype.setTextureFormatToUse = function (formatsAvailable: Array<string>): Nullable<string> {\r\n    const texturesSupported = this.texturesSupported;\r\n    for (let i = 0, len1 = texturesSupported.length; i < len1; i++) {\r\n        for (let j = 0, len2 = formatsAvailable.length; j < len2; j++) {\r\n            if (texturesSupported[i] === formatsAvailable[j].toLowerCase()) {\r\n                this._transformTextureUrl = TransformTextureUrl.bind(this);\r\n                return (this._textureFormatInUse = texturesSupported[i]);\r\n            }\r\n        }\r\n    }\r\n    // actively set format to nothing, to allow this to be called more than once\r\n    // and possibly fail the 2nd time\r\n    this._textureFormatInUse = \"\";\r\n    this._transformTextureUrl = null;\r\n    return null;\r\n};\r\n"]}