import type { ISmartArrayLike } from "../../Misc/smartArray.js";
import { Scene } from "../../scene.js";
import type { SubMesh } from "../../Meshes/subMesh.js";
import { AbstractMesh } from "../../Meshes/abstractMesh.js";
import { Ray } from "../../Culling/ray.js";
import { Octree } from "./octree.js";
import type { Collider } from "../../Collisions/collider.js";
declare module "../../scene.js" {
    interface Scene {
        /**
         * @internal
         * Backing Filed
         */
        _selectionOctree: Octree<AbstractMesh>;
        /**
         * Gets the octree used to boost mesh selection (picking)
         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees
         */
        selectionOctree: Octree<AbstractMesh>;
        /**
         * Creates or updates the octree used to boost selection (picking)
         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees
         * @param maxCapacity defines the maximum capacity per leaf
         * @param maxDepth defines the maximum depth of the octree
         * @returns an octree of AbstractMesh
         */
        createOrUpdateSelectionOctree(maxCapacity?: number, maxDepth?: number): Octree<AbstractMesh>;
    }
}
declare module "../../Meshes/abstractMesh.js" {
    interface AbstractMesh {
        /**
         * @internal
         * Backing Field
         */
        _submeshesOctree: Octree<SubMesh>;
        /**
         * This function will create an octree to help to select the right submeshes for rendering, picking and collision computations.
         * Please note that you must have a decent number of submeshes to get performance improvements when using an octree
         * @param maxCapacity defines the maximum size of each block (64 by default)
         * @param maxDepth defines the maximum depth to use (no more than 2 levels by default)
         * @returns the new octree
         * @see https://www.babylonjs-playground.com/#NA4OQ#12
         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/optimizeOctrees
         */
        createOrUpdateSubmeshesOctree(maxCapacity?: number, maxDepth?: number): Octree<SubMesh>;
    }
}
/**
 * Defines the octree scene component responsible to manage any octrees
 * in a given scene.
 */
export declare class OctreeSceneComponent {
    /**
     * The component name help to identify the component in the list of scene components.
     */
    readonly name = "Octree";
    /**
     * The scene the component belongs to.
     */
    scene: Scene;
    /**
     * Indicates if the meshes have been checked to make sure they are isEnabled()
     */
    readonly checksIsEnabled = true;
    /**
     * Creates a new instance of the component for the given scene
     * @param scene Defines the scene to register the component in
     */
    constructor(scene?: Scene);
    /**
     * Registers the component in a given scene
     */
    register(): void;
    /**
     * Return the list of active meshes
     * @returns the list of active meshes
     */
    getActiveMeshCandidates(): ISmartArrayLike<AbstractMesh>;
    /**
     * Return the list of active sub meshes
     * @param mesh The mesh to get the candidates sub meshes from
     * @returns the list of active sub meshes
     */
    getActiveSubMeshCandidates(mesh: AbstractMesh): ISmartArrayLike<SubMesh>;
    private _tempRay;
    /**
     * Return the list of sub meshes intersecting with a given local ray
     * @param mesh defines the mesh to find the submesh for
     * @param localRay defines the ray in local space
     * @returns the list of intersecting sub meshes
     */
    getIntersectingSubMeshCandidates(mesh: AbstractMesh, localRay: Ray): ISmartArrayLike<SubMesh>;
    /**
     * Return the list of sub meshes colliding with a collider
     * @param mesh defines the mesh to find the submesh for
     * @param collider defines the collider to evaluate the collision against
     * @returns the list of colliding sub meshes
     */
    getCollidingSubMeshCandidates(mesh: AbstractMesh, collider: Collider): ISmartArrayLike<SubMesh>;
    /**
     * Rebuilds the elements related to this component in case of
     * context lost for instance.
     */
    rebuild(): void;
    /**
     * Disposes the component and the associated resources.
     */
    dispose(): void;
}
