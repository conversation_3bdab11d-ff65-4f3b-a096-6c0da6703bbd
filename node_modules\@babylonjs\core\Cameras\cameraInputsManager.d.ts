import type { Nullable } from "../types.js";
import { Camera } from "./camera.js";
/**
 * @ignore
 * This is a list of all the different input types that are available in the application.
 * Fo instance: ArcRotateCameraGamepadInput...
 */
export declare var CameraInputTypes: {};
/**
 * This is the contract to implement in order to create a new input class.
 * Inputs are dealing with listening to user actions and moving the camera accordingly.
 */
export interface ICameraInput<Tcamera extends Camera> {
    /**
     * Defines the camera the input is attached to.
     */
    camera: Nullable<Tcamera>;
    /**
     * Gets the class name of the current input.
     * @returns the class name
     */
    getClassName(): string;
    /**
     * Get the friendly name associated with the input class.
     * @returns the input friendly name
     */
    getSimpleName(): string;
    /**
     * Attach the input controls to a specific dom element to get the input from.
     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)
     */
    attachControl(noPreventDefault?: boolean): void;
    /**
     * Detach the current controls from the specified dom element.
     */
    detachControl(): void;
    /**
     * Update the current camera state depending on the inputs that have been used this frame.
     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.
     */
    checkInputs?: () => void;
}
/**
 * Represents a map of input types to input instance or input index to input instance.
 */
export interface CameraInputsMap<Tcamera extends Camera> {
    /**
     * Accessor to the input by input type.
     */
    [name: string]: ICameraInput<Tcamera>;
    /**
     * Accessor to the input by input index.
     */
    [idx: number]: ICameraInput<Tcamera>;
}
/**
 * This represents the input manager used within a camera.
 * It helps dealing with all the different kind of input attached to a camera.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs
 */
export declare class CameraInputsManager<Tcamera extends Camera> {
    /**
     * Defines the list of inputs attached to the camera.
     */
    attached: CameraInputsMap<Tcamera>;
    /**
     * Defines the dom element the camera is collecting inputs from.
     * This is null if the controls have not been attached.
     */
    attachedToElement: boolean;
    /**
     * Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)
     */
    noPreventDefault: boolean;
    /**
     * Defined the camera the input manager belongs to.
     */
    camera: Tcamera;
    /**
     * Update the current camera state depending on the inputs that have been used this frame.
     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.
     */
    checkInputs: () => void;
    /**
     * Instantiate a new Camera Input Manager.
     * @param camera Defines the camera the input manager belongs to
     */
    constructor(camera: Tcamera);
    /**
     * Add an input method to a camera
     * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs
     * @param input Camera input method
     */
    add(input: ICameraInput<Tcamera>): void;
    /**
     * Remove a specific input method from a camera
     * example: camera.inputs.remove(camera.inputs.attached.mouse);
     * @param inputToRemove camera input method
     */
    remove(inputToRemove: ICameraInput<Tcamera>): void;
    /**
     * Remove a specific input type from a camera
     * example: camera.inputs.remove("ArcRotateCameraGamepadInput");
     * @param inputType the type of the input to remove
     */
    removeByType(inputType: string): void;
    private _addCheckInputs;
    /**
     * Attach the input controls to the currently attached dom element to listen the events from.
     * @param input Defines the input to attach
     */
    attachInput(input: ICameraInput<Tcamera>): void;
    /**
     * Attach the current manager inputs controls to a specific dom element to listen the events from.
     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)
     */
    attachElement(noPreventDefault?: boolean): void;
    /**
     * Detach the current manager inputs controls from a specific dom element.
     * @param disconnect Defines whether the input should be removed from the current list of attached inputs
     */
    detachElement(disconnect?: boolean): void;
    /**
     * Rebuild the dynamic inputCheck function from the current list of
     * defined inputs in the manager.
     */
    rebuildInputCheck(): void;
    /**
     * Remove all attached input methods from a camera
     */
    clear(): void;
    /**
     * Serialize the current input manager attached to a camera.
     * This ensures than once parsed,
     * the input associated to the camera will be identical to the current ones
     * @param serializedCamera Defines the camera serialization JSON the input serialization should write to
     */
    serialize(serializedCamera: any): void;
    /**
     * Parses an input manager serialized JSON to restore the previous list of inputs
     * and states associated to a camera.
     * @param parsedCamera Defines the JSON to parse
     */
    parse(parsedCamera: any): void;
}
