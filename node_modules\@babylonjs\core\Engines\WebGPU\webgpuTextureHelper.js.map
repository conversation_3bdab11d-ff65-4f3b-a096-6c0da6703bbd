{"version": 3, "file": "webgpuTextureHelper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuTextureHelper.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,KAAK,EAAE,MAAM,mCAAmC,CAAC;AAC1D,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAIzC,gBAAgB;AAChB,MAAM,OAAO,mBAAmB;IACrB,MAAM,CAAC,sBAAsB,CAAC,KAAa,EAAE,MAAc;QAC9D,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9C,CAAC;IAEM,MAAM,CAAC,wBAAwB,CAAC,MAAwB;QAC3D,QAAQ,MAAM,EAAE,CAAC;YACb,kCAAkC;YAClC,2DAA2C;YAC3C,yDAA0C;YAC1C,6DAA4C;YAC5C,2DAA2C;YAC3C,iEAA8C;YAC9C,0EAAkD;YAClD,+DAA6C;YAC7C,iEAA8C;YAC9C,0EAAkD;YAClD,mEAA+C,CAAC,4CAA4C;YAC5F,qEAAgD,CAAC,4CAA4C;YAC7F,qEAAgD,CAAC,4CAA4C;YAC7F,uEAAiD,CAAC,4CAA4C;YAC9F,uEAAgD;YAChD,gFAAoD;YACpD,yEAAiD;YACjD,mEAA8C;YAC9C,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,iEAA6C;YAC7C,uEAAgD;YAChD,gFAAoD;YACpD,wEAAiD;YACjD,iFAAqD;YACrD,4EAAmD;YACnD,qFAAuD;YACvD,0EAAkD;YAClD,mFAAsD;YACtD,oEAA+C;YAC/C,sEAAgD;YAChD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,yEAAiD;YACjD,kFAAqD;YACrD,yEAAiD;YACjD,kFAAqD;YACrD,yEAAiD;YACjD,kFAAqD;YACrD,2EAAkD;YAClD,oFAAsD;YACtD,2EAAkD;YAClD,oFAAsD;YACtD,2EAAkD;YAClD,oFAAsD;YACtD;gBACI,OAAO,SAAS,CAAC,yBAAyB,CAAC;YAE/C,gCAAgC;YAChC,2DAA2C;YAC3C,yDAA0C;YAC1C,6DAA4C;YAC5C,2DAA2C;YAC3C,iEAA8C;YAC9C,+DAA6C;YAC7C,uEAAgD;YAChD,mEAA8C;YAC9C,iEAA6C;YAC7C,oEAA+C;YAC/C;gBACI,OAAO,SAAS,CAAC,gBAAgB,CAAC;YAEtC,mCAAmC;YACnC,2DAA2C;YAC3C,6DAA4C;YAC5C,+DAA6C;YAC7C,mEAA+C;YAC/C,6DAA4C;YAC5C,iEAA8C;YAC9C;gBACI,OAAO,SAAS,CAAC,0BAA0B,CAAC;YAEhD,iCAAiC;YACjC,2DAA2C;YAC3C,6DAA4C;YAC5C,+DAA6C;YAC7C,mEAA+C;YAC/C,6DAA4C;YAC5C;gBACI,OAAO,SAAS,CAAC,iBAAiB,CAAC;YAEvC,6DAA4C;YAC5C,+DAA6C;YAC7C;gBACI,OAAO,SAAS,CAAC,sBAAsB,CAAC;YAE5C,mCAAmC;YACnC,2DAA2C;YAC3C,6DAA4C;YAC5C;gBACI,OAAO,SAAS,CAAC,4BAA4B,CAAC;YAElD,iCAAiC;YACjC,2DAA2C;YAC3C,6DAA4C;YAC5C;gBACI,OAAO,SAAS,CAAC,4BAA4B,CAAC;YAElD,6DAA4C;YAC5C,+DAA6C;YAC7C,mEAA+C;YAC/C,qEAAgD;YAChD,sFAAwD;YACxD,mEAA+C;YAC/C;gBACI,OAAO,SAAS,CAAC,iBAAiB,CAAC;QAC3C,CAAC;QAED,OAAO,SAAS,CAAC,yBAAyB,CAAC;IAC/C,CAAC;IAEM,MAAM,CAAC,6BAA6B,CAAC,MAAwB;QAChE,QAAQ,MAAM,EAAE,CAAC;YACb,iBAAiB;YACjB,2DAA2C;YAC3C,2DAA2C;YAC3C,yDAA0C;YAC1C;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,kBAAkB;YAClB,2DAA2C;YAC3C,2DAA2C;YAC3C,6DAA4C;YAC5C,+DAA6C;YAC7C,mEAA+C;YAC/C,6DAA4C;YAC5C,+DAA6C;YAC7C,mEAA+C;YAC/C,6DAA4C;YAC5C,6DAA4C;YAC5C,6DAA4C;YAC5C,2DAA2C;YAC3C;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,kBAAkB;YAClB,2DAA2C;YAC3C,2DAA2C;YAC3C,6DAA4C;YAC5C,6DAA4C;YAC5C,6DAA4C;YAC5C,+DAA6C;YAC7C,iEAA8C;YAC9C,0EAAkD;YAClD,iEAA8C;YAC9C,+DAA6C;YAC7C,+DAA6C;YAC7C,iEAA8C;YAC9C,0EAAkD;YAClD,qEAAgD;YAChD,mEAA+C;YAC/C,qEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,kBAAkB;YAClB,6DAA4C;YAC5C,6DAA4C;YAC5C,+DAA6C;YAC7C,iEAA8C;YAC9C,iEAA8C;YAC9C;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,mBAAmB;YACnB,iEAA8C;YAC9C,iEAA8C;YAC9C;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAE/C,4BAA4B;YAC5B;gBACI,4CAA4C;gBAC5C,MAAM,oCAAoC,CAAC;YAC/C;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAC9C;gBACI,4CAA4C;gBAC5C,MAAM,uCAAuC,CAAC;YAClD;gBACI,4CAA4C;gBAC5C,MAAM,+CAA+C,CAAC;YAC1D;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAC9C;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,mEAAmE;YACnE,mEAAmE;YACnE,uEAAgD;YAChD,gFAAoD;YACpD,yEAAiD;YACjD,uEAAgD;YAChD,mEAA8C;YAC9C,mEAA8C;YAC9C,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAE/C,iEAA6C;YAC7C,iEAA6C;YAC7C,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,uEAAuE;YACvE,mEAAmE;YACnE,wEAAiD;YACjD,iFAAqD;YACrD,4EAAmD;YACnD,qFAAuD;YACvD,oEAA+C;YAC/C;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAE9C,0EAAkD;YAClD,mFAAsD;YACtD,sEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAE/C,uEAAuE;YACvE,mEAAmE;YACnE,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,uEAAgD;YAChD;gBACI,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC/C,yEAAiD;YACjD;gBACI,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAChD,yEAAiD;YACjD;gBACI,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAChD,yEAAiD;YACjD;gBACI,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAChD,2EAAkD;YAClD;gBACI,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YACjD,2EAAkD;YAClD;gBACI,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YACjD,2EAAkD;YAClD;gBACI,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;QACrD,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;IAC9C,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,OAA6C;QACzE,OAAO,CAAC,CAAE,OAAmC,CAAC,OAAO,CAAC;IAC1D,CAAC;IAEM,MAAM,CAAC,iBAAiB,CAAC,OAAqC;QACjE,OAAO,CAAC,CAAE,OAA2B,CAAC,OAAO,CAAC;IAClD,CAAC;IAEM,MAAM,CAAC,aAAa,CAAC,WAA4D;QACpF,OAAQ,WAA2B,CAAC,KAAK,KAAK,SAAS,CAAC;IAC5D,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,WAA8D;QAC3F,OAAO,KAAK,CAAC,OAAO,CAAC,WAA4B,CAAC,IAAK,WAA6B,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,SAAS,CAAC;IAChH,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,MAAwB;QACrD,QAAQ,MAAM,EAAE,CAAC;YACb,gFAAoD;YACpD,uEAAgD;YAChD,uEAAgD;YAChD,yEAAiD;YACjD,mEAA8C;YAC9C,mEAA8C;YAC9C,iEAA6C;YAC7C,iEAA6C;YAC7C,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,wEAAiD;YACjD,iFAAqD;YACrD,4EAAmD;YACnD,qFAAuD;YACvD,0EAAkD;YAClD,mFAAsD;YACtD,oEAA+C;YAC/C,oEAA+C;YAC/C,sEAAgD;YAChD,sEAAgD;YAChD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,yEAAiD;YACjD,kFAAqD;YACrD,yEAAiD;YACjD,kFAAqD;YACrD,yEAAiD;YACjD,kFAAqD;YACrD,2EAAkD;YAClD,oFAAsD;YACtD,2EAAkD;YAClD,oFAAsD;YACtD,2EAAkD;YAClD;gBACI,OAAO,IAAI,CAAC;QACpB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,sBAAsB,CAAC,IAAY,EAAE,MAAc,EAAE,aAAa,GAAG,KAAK;QACpF,QAAQ,MAAM,EAAE,CAAC;YACb,KAAK,SAAS,CAAC,qBAAqB;gBAChC,uEAAkD;YACtD,KAAK,SAAS,CAAC,qBAAqB;gBAChC,qEAAiD;YACrD,KAAK,SAAS,CAAC,8BAA8B;gBACzC,sFAAyD;YAC7D,KAAK,SAAS,CAAC,2BAA2B;gBACtC,uEAAkD;YACtD,KAAK,SAAS,CAAC,mCAAmC;gBAC9C,wFAA0D;YAC9D,KAAK,SAAS,CAAC,sBAAsB;gBACjC,+DAA8C;YAElD,KAAK,SAAS,CAAC,wCAAwC;gBACnD,OAAO,aAAa,CAAC,CAAC,4EAAgD,CAAC,kEAA2C,CAAC;YACvH,KAAK,SAAS,CAAC,gDAAgD;gBAC3D,2EAAmD;YACvD,KAAK,SAAS,CAAC,8CAA8C;gBACzD,yEAAkD;YACtD,KAAK,SAAS,CAAC,uCAAuC;gBAClD,OAAO,aAAa,CAAC,CAAC,4EAAgD,CAAC,kEAA2C,CAAC;YACvH,KAAK,SAAS,CAAC,uCAAuC;gBAClD,OAAO,aAAa,CAAC,CAAC,4EAAgD,CAAC,kEAA2C,CAAC;YACvH,KAAK,SAAS,CAAC,uCAAuC,CAAC;YACvD,KAAK,SAAS,CAAC,sCAAsC;gBACjD,OAAO,aAAa,CAAC,CAAC,4EAAgD,CAAC,kEAA2C,CAAC;YACvH,KAAK,SAAS,CAAC,sCAAsC;gBACjD,OAAO,aAAa,CAAC,CAAC,4EAAgD,CAAC,kEAA2C,CAAC;YACvH,KAAK,SAAS,CAAC,uCAAuC,CAAC;YACvD,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,OAAO,aAAa,CAAC,CAAC,6EAAiD,CAAC,mEAA4C,CAAC;YACzH,KAAK,SAAS,CAAC,uCAAuC;gBAClD,OAAO,aAAa,CAAC,CAAC,+EAAkD,CAAC,qEAA6C,CAAC;QAC/H,CAAC;QAED,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,SAAS,CAAC,gBAAgB;gBAC3B,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,6DAA6C;oBACjD,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,+DAA8C;oBAClD,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,4CAA4C;wBAC5C,MAAM,oCAAoC,CAAC;oBAC/C,KAAK,SAAS,CAAC,yBAAyB;wBACpC,2DAA4C;oBAChD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,6DAA6C;oBACjD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,4CAA4C,CAAC;oBACvD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,iEAA+C;oBACnD;wBACI,mEAAgD;gBACxD,CAAC;YACL,KAAK,SAAS,CAAC,yBAAyB;gBACpC,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,6DAA6C;oBACjD,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,+DAA8C;oBAClD,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,4CAA4C;wBAC5C,MAAM,kDAAkD,CAAC;oBAC7D,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,aAAa,CAAC,CAAC,sEAA8C,CAAC,4DAAyC,CAAC;oBACnH,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,OAAO,aAAa,CAAC,CAAC,sEAA8C,CAAC,4DAAyC,CAAC;oBACnH,KAAK,SAAS,CAAC,yBAAyB;wBACpC,2DAA4C;oBAChD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,6DAA6C;oBACjD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,4CAA4C,CAAC;oBACvD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,iEAA+C;oBACnD,KAAK,SAAS,CAAC,mBAAmB;wBAC9B,4CAA4C;wBAC5C,MAAM,oDAAoD,CAAC;oBAC/D,KAAK,SAAS,CAAC,uBAAuB;wBAClC,4CAA4C;wBAC5C,MAAM,wDAAwD,CAAC;oBACnE,KAAK,SAAS,CAAC,6BAA6B;wBACxC,4CAA4C;wBAC5C,MAAM,8DAA8D,CAAC;oBACzE;wBACI,mEAAgD;gBACxD,CAAC;YACL,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,yBAAyB;wBACpC,6DAA6C;oBACjD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,+DAA8C;oBAClD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,0DAA0D,CAAC;oBACrE,KAAK,SAAS,CAAC,0BAA0B;wBACrC,mEAAgD;oBACpD;wBACI,mEAAgD;gBACxD,CAAC;YACL,KAAK,SAAS,CAAC,0BAA0B;gBACrC,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,yBAAyB;wBACpC,6DAA6C;oBACjD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,+DAA8C;oBAClD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,0DAA0D,CAAC;oBACrE,KAAK,SAAS,CAAC,0BAA0B;wBACrC,mEAAgD;oBACpD;wBACI,mEAAgD;gBACxD,CAAC;YACL,KAAK,SAAS,CAAC,eAAe;gBAC1B,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,yBAAyB;wBACpC,6DAA6C;oBACjD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,+DAA8C;oBAClD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,0DAA0D,CAAC;oBACrE,KAAK,SAAS,CAAC,0BAA0B;wBACrC,mEAAgD;oBACpD;wBACI,mEAAgD;gBACxD,CAAC;YACL,KAAK,SAAS,CAAC,4BAA4B,EAAE,yBAAyB;gBAClE,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,yBAAyB;wBACpC,6DAA6C;oBACjD,KAAK,SAAS,CAAC,wBAAwB;wBACnC,+DAA8C;oBAClD,KAAK,SAAS,CAAC,yBAAyB;wBACpC,4CAA4C;wBAC5C,MAAM,0DAA0D,CAAC;oBACrE,KAAK,SAAS,CAAC,0BAA0B;wBACrC,mEAAgD;oBACpD;wBACI,mEAAgD;gBACxD,CAAC;YACL,KAAK,SAAS,CAAC,iBAAiB;gBAC5B,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,+DAA8C,CAAC,6CAA6C;oBAChG,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,iEAA+C,CAAC,8CAA8C;oBAClG,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,4CAA4C;wBAC5C,MAAM,kDAAkD,CAAC;oBAC7D,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,qEAAiD,CAAC,gDAAgD;oBACtG;wBACI,qEAAiD;gBACzD,CAAC;YACL,KAAK,SAAS,CAAC,sBAAsB;gBACjC,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,+DAA8C;oBAClD,KAAK,SAAS,CAAC,gBAAgB;wBAC3B,iEAA+C;oBACnD,KAAK,SAAS,CAAC,iBAAiB;wBAC5B,4CAA4C;wBAC5C,MAAM,kDAAkD,CAAC;oBAC7D,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,qEAAiD;oBACrD;wBACI,qEAAiD;gBACzD,CAAC;YACL,KAAK,SAAS,CAAC,gCAAgC;gBAC3C,4CAA4C;gBAC5C,MAAM,iEAAiE,CAAC;YAC5E,KAAK,SAAS,CAAC,wCAAwC;gBACnD,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,yEAAmD;oBACvD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,4CAA4C;wBAC5C,MAAM,iHAAiH,CAAC;oBAC5H;wBACI,yEAAmD;gBAC3D,CAAC;YACL,KAAK,SAAS,CAAC,oCAAoC;gBAC/C,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,uEAAkD;oBACtD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,4CAA4C;wBAC5C,MAAM,6GAA6G,CAAC;oBACxH;wBACI,uEAAkD;gBAC1D,CAAC;YACL,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,4CAA4C;gBAC5C,MAAM,mEAAmE,CAAC;YAC9E,KAAK,SAAS,CAAC,kCAAkC;gBAC7C,4CAA4C;gBAC5C,MAAM,mEAAmE,CAAC;YAC9E,KAAK,SAAS,CAAC,uCAAuC;gBAClD,QAAQ,MAAM,EAAE,CAAC;oBACb,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,uEAAkD;oBACtD,KAAK,SAAS,CAAC,0BAA0B;wBACrC,qEAAiD;oBACrD;wBACI,uEAAkD;gBAC1D,CAAC;QACT,CAAC;QAED,OAAO,aAAa,CAAC,CAAC,sEAA8C,CAAC,4DAAyC,CAAC;IACnH,CAAC;IAEM,MAAM,CAAC,qCAAqC,CAAC,MAAwB;QACxE,QAAQ,MAAM,EAAE,CAAC;YACb,2DAA2C;YAC3C,2DAA2C;YAC3C,yDAA0C;YAC1C,yDAA0C;YAC1C,iEAA6C;YAC7C,iEAA6C;YAC7C,2DAA2C;YAC3C,2DAA2C;YAC3C,qEAAgD;YAChD,6DAA4C;YAC5C,6DAA4C;YAC5C,6DAA4C;YAC5C,2DAA2C;YAC3C,2DAA2C;YAC3C,6DAA4C;YAC5C,qEAAgD;YAChD,6DAA4C;YAC5C,mEAA+C;YAC/C,oEAA+C;YAC/C;gBACI,OAAO,CAAC,CAAC;YAEb,6DAA4C;YAC5C,6DAA4C;YAC5C,2DAA2C;YAC3C,2DAA2C;YAC3C,sFAAwD;YACxD,mEAA8C;YAC9C,mEAA8C;YAC9C,6DAA4C;YAC5C,6DAA4C;YAC5C,+DAA6C;YAC7C,+DAA6C;YAC7C,+DAA6C;YAC7C,6DAA4C;YAC5C,6DAA4C;YAC5C,+DAA6C;YAC7C,oFAAuD;YACvD,sEAAgD;YAChD;gBACI,OAAO,CAAC,CAAC;YAEb,qEAAgD;YAChD,uEAAiD;YACjD,yEAAiD;YACjD,uEAAgD;YAChD,wEAAiD;YACjD;gBACI,OAAO,CAAC,CAAC;YAEb,iEAA8C;YAC9C,0EAAkD;YAClD,iEAA8C;YAC9C,+DAA6C;YAC7C,+DAA6C;YAC7C,iEAA8C;YAC9C,0EAAkD;YAClD,mEAA+C;YAC/C,mEAA+C;YAC/C,mEAA+C;YAC/C,qEAAgD;YAChD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,iEAA8C;YAC9C,iEAA8C;YAC9C,mEAA+C;YAC/C,iEAA8C;YAC9C,iEAA8C;YAC9C,mEAA+C;YAC/C,4EAAmD;YACnD,qFAAuD;YACvD,0EAAkD;YAClD,mFAAsD;YACtD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,uEAAgD;YAChD,gFAAoD;YACpD,yEAAiD;YACjD,kFAAqD;YACrD,yEAAiD;YACjD,kFAAqD;YACrD,yEAAiD;YACjD,kFAAqD;YACrD,2EAAkD;YAClD,oFAAsD;YACtD,2EAAkD;YAClD,oFAAsD;YACtD,2EAAkD;YAClD;gBACI,OAAO,CAAC,CAAC;QACjB,CAAC;QAED,4CAA4C;QAC5C,MAAM,kBAAkB,MAAM,GAAG,CAAC;IACtC,CAAC;IAEM,MAAM,CAAC,gBAAgB,CAAC,MAAwB;QACnD,QAAQ,MAAM,EAAE,CAAC;YACb,6DAA4C;YAC5C,sFAAwD;YACxD;gBACI,OAAO,IAAI,CAAC;QACpB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,yBAAyB,CAAC,MAAwB;QAC5D,QAAQ,MAAM,EAAE,CAAC;YACb,sFAAwD;YACxD;gBACI,OAAO,IAAI,CAAC;QACpB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,MAAM,CAAC,kBAAkB,CAAC,MAAwB;QACrD,QAAQ,MAAM,EAAE,CAAC;YACb;gBACI,uEAAkD;YACtD;gBACI,qEAAiD;YACrD;gBACI,qEAAiD;YACrD;gBACI,uEAAkD;YACtD;gBACI,uEAAkD;QAC1D,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,MAAM,CAAC,SAAS,CAAC,WAAmB;QACvC,8BAA8B;QAC9B,OAAO,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;CACJ", "sourcesContent": ["/* eslint-disable babylonjs/available */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport { ILog2 } from \"../../Maths/math.scalar.functions\";\r\nimport { Constants } from \"../constants\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { IHardwareTextureWrapper } from \"../../Materials/Textures/hardwareTextureWrapper\";\r\n\r\n/** @internal */\r\nexport class WebGPUTextureHelper {\r\n    public static ComputeNumMipmapLevels(width: number, height: number) {\r\n        return ILog2(Math.max(width, height)) + 1;\r\n    }\r\n\r\n    public static GetTextureTypeFromFormat(format: GPUTextureFormat): number {\r\n        switch (format) {\r\n            // One Component = 8 bits unsigned\r\n            case WebGPUConstants.TextureFormat.R8Unorm:\r\n            case WebGPUConstants.TextureFormat.R8Uint:\r\n            case WebGPUConstants.TextureFormat.RG8Unorm:\r\n            case WebGPUConstants.TextureFormat.RG8Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGBA8Uint:\r\n            case WebGPUConstants.TextureFormat.BGRA8Unorm:\r\n            case WebGPUConstants.TextureFormat.BGRA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGB10A2UINT: // composite format - let's say it's byte...\r\n            case WebGPUConstants.TextureFormat.RGB10A2Unorm: // composite format - let's say it's byte...\r\n            case WebGPUConstants.TextureFormat.RGB9E5UFloat: // composite format - let's say it's byte...\r\n            case WebGPUConstants.TextureFormat.RG11B10UFloat: // composite format - let's say it's byte...\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBUFloat:\r\n            case WebGPUConstants.TextureFormat.BC5RGUnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC4RUnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.EACR11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.Stencil8:\r\n                return Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n\r\n            // One Component = 8 bits signed\r\n            case WebGPUConstants.TextureFormat.R8Snorm:\r\n            case WebGPUConstants.TextureFormat.R8Sint:\r\n            case WebGPUConstants.TextureFormat.RG8Snorm:\r\n            case WebGPUConstants.TextureFormat.RG8Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA8Snorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8Sint:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBFloat:\r\n            case WebGPUConstants.TextureFormat.BC5RGSnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RSnorm:\r\n            case WebGPUConstants.TextureFormat.EACR11Snorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Snorm:\r\n                return Constants.TEXTURETYPE_BYTE;\r\n\r\n            // One component = 16 bits unsigned\r\n            case WebGPUConstants.TextureFormat.R16Uint:\r\n            case WebGPUConstants.TextureFormat.R16Unorm:\r\n            case WebGPUConstants.TextureFormat.RG16Unorm:\r\n            case WebGPUConstants.TextureFormat.RGBA16Unorm:\r\n            case WebGPUConstants.TextureFormat.RG16Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Uint:\r\n            case WebGPUConstants.TextureFormat.Depth16Unorm:\r\n                return Constants.TEXTURETYPE_UNSIGNED_SHORT;\r\n\r\n            // One component = 16 bits signed\r\n            case WebGPUConstants.TextureFormat.R16Sint:\r\n            case WebGPUConstants.TextureFormat.R16Snorm:\r\n            case WebGPUConstants.TextureFormat.RG16Snorm:\r\n            case WebGPUConstants.TextureFormat.RGBA16Snorm:\r\n            case WebGPUConstants.TextureFormat.RG16Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Sint:\r\n                return Constants.TEXTURETYPE_SHORT;\r\n\r\n            case WebGPUConstants.TextureFormat.R16Float:\r\n            case WebGPUConstants.TextureFormat.RG16Float:\r\n            case WebGPUConstants.TextureFormat.RGBA16Float:\r\n                return Constants.TEXTURETYPE_HALF_FLOAT;\r\n\r\n            // One component = 32 bits unsigned\r\n            case WebGPUConstants.TextureFormat.R32Uint:\r\n            case WebGPUConstants.TextureFormat.RG32Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Uint:\r\n                return Constants.TEXTURETYPE_UNSIGNED_INTEGER;\r\n\r\n            // One component = 32 bits signed\r\n            case WebGPUConstants.TextureFormat.R32Sint:\r\n            case WebGPUConstants.TextureFormat.RG32Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Sint:\r\n                return Constants.TEXTURETYPE_UNSIGNED_INTEGER;\r\n\r\n            case WebGPUConstants.TextureFormat.R32Float:\r\n            case WebGPUConstants.TextureFormat.RG32Float:\r\n            case WebGPUConstants.TextureFormat.RGBA32Float:\r\n            case WebGPUConstants.TextureFormat.Depth32Float:\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n            case WebGPUConstants.TextureFormat.Depth24Plus:\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                return Constants.TEXTURETYPE_FLOAT;\r\n        }\r\n\r\n        return Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n    }\r\n\r\n    public static GetBlockInformationFromFormat(format: GPUTextureFormat): { width: number; height: number; length: number } {\r\n        switch (format) {\r\n            // 8 bits formats\r\n            case WebGPUConstants.TextureFormat.R8Unorm:\r\n            case WebGPUConstants.TextureFormat.R8Snorm:\r\n            case WebGPUConstants.TextureFormat.R8Uint:\r\n            case WebGPUConstants.TextureFormat.R8Sint:\r\n                return { width: 1, height: 1, length: 1 };\r\n\r\n            // 16 bits formats\r\n            case WebGPUConstants.TextureFormat.R16Uint:\r\n            case WebGPUConstants.TextureFormat.R16Sint:\r\n            case WebGPUConstants.TextureFormat.R16Unorm:\r\n            case WebGPUConstants.TextureFormat.RG16Unorm:\r\n            case WebGPUConstants.TextureFormat.RGBA16Unorm:\r\n            case WebGPUConstants.TextureFormat.R16Snorm:\r\n            case WebGPUConstants.TextureFormat.RG16Snorm:\r\n            case WebGPUConstants.TextureFormat.RGBA16Snorm:\r\n            case WebGPUConstants.TextureFormat.R16Float:\r\n            case WebGPUConstants.TextureFormat.RG8Unorm:\r\n            case WebGPUConstants.TextureFormat.RG8Snorm:\r\n            case WebGPUConstants.TextureFormat.RG8Uint:\r\n            case WebGPUConstants.TextureFormat.RG8Sint:\r\n                return { width: 1, height: 1, length: 2 };\r\n\r\n            // 32 bits formats\r\n            case WebGPUConstants.TextureFormat.R32Uint:\r\n            case WebGPUConstants.TextureFormat.R32Sint:\r\n            case WebGPUConstants.TextureFormat.R32Float:\r\n            case WebGPUConstants.TextureFormat.RG16Uint:\r\n            case WebGPUConstants.TextureFormat.RG16Sint:\r\n            case WebGPUConstants.TextureFormat.RG16Float:\r\n            case WebGPUConstants.TextureFormat.RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGBA8Snorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA8Sint:\r\n            case WebGPUConstants.TextureFormat.BGRA8Unorm:\r\n            case WebGPUConstants.TextureFormat.BGRA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGB9E5UFloat:\r\n            case WebGPUConstants.TextureFormat.RGB10A2UINT:\r\n            case WebGPUConstants.TextureFormat.RGB10A2Unorm:\r\n            case WebGPUConstants.TextureFormat.RG11B10UFloat:\r\n                return { width: 1, height: 1, length: 4 };\r\n\r\n            // 64 bits formats\r\n            case WebGPUConstants.TextureFormat.RG32Uint:\r\n            case WebGPUConstants.TextureFormat.RG32Sint:\r\n            case WebGPUConstants.TextureFormat.RG32Float:\r\n            case WebGPUConstants.TextureFormat.RGBA16Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Float:\r\n                return { width: 1, height: 1, length: 8 };\r\n\r\n            // 128 bits formats\r\n            case WebGPUConstants.TextureFormat.RGBA32Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Float:\r\n                return { width: 1, height: 1, length: 16 };\r\n\r\n            // Depth and stencil formats\r\n            case WebGPUConstants.TextureFormat.Stencil8:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"No fixed size for Stencil8 format!\";\r\n            case WebGPUConstants.TextureFormat.Depth16Unorm:\r\n                return { width: 1, height: 1, length: 2 };\r\n            case WebGPUConstants.TextureFormat.Depth24Plus:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"No fixed size for Depth24Plus format!\";\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"No fixed size for Depth24PlusStencil8 format!\";\r\n            case WebGPUConstants.TextureFormat.Depth32Float:\r\n                return { width: 1, height: 1, length: 4 };\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n                return { width: 1, height: 1, length: 5 };\r\n\r\n            // BC compressed formats usable if \"texture-compression-bc\" is both\r\n            // supported by the device/user agent and enabled in requestDevice.\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBUFloat:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBFloat:\r\n            case WebGPUConstants.TextureFormat.BC5RGUnorm:\r\n            case WebGPUConstants.TextureFormat.BC5RGSnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB:\r\n                return { width: 4, height: 4, length: 16 };\r\n\r\n            case WebGPUConstants.TextureFormat.BC4RUnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RSnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB:\r\n                return { width: 4, height: 4, length: 8 };\r\n\r\n            // ETC2 compressed formats usable if \"texture-compression-etc2\" is both\r\n            // supported by the device/user agent and enabled in requestDevice.\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.EACR11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACR11Snorm:\r\n                return { width: 4, height: 4, length: 8 };\r\n\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.EACRG11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Snorm:\r\n                return { width: 4, height: 4, length: 16 };\r\n\r\n            // ASTC compressed formats usable if \"texture-compression-astc\" is both\r\n            // supported by the device/user agent and enabled in requestDevice.\r\n            case WebGPUConstants.TextureFormat.ASTC4x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB:\r\n                return { width: 4, height: 4, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC5x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4UnormSRGB:\r\n                return { width: 5, height: 4, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC5x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5UnormSRGB:\r\n                return { width: 5, height: 5, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC6x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5UnormSRGB:\r\n                return { width: 6, height: 5, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC6x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6UnormSRGB:\r\n                return { width: 6, height: 6, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC8x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5UnormSRGB:\r\n                return { width: 8, height: 5, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC8x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6UnormSRGB:\r\n                return { width: 8, height: 6, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC8x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8UnormSRGB:\r\n                return { width: 8, height: 8, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC10x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5UnormSRGB:\r\n                return { width: 10, height: 5, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC10x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6UnormSRGB:\r\n                return { width: 10, height: 6, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC10x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8UnormSRGB:\r\n                return { width: 10, height: 8, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC10x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10UnormSRGB:\r\n                return { width: 10, height: 10, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC12x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10UnormSRGB:\r\n                return { width: 12, height: 10, length: 16 };\r\n            case WebGPUConstants.TextureFormat.ASTC12x12Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12UnormSRGB:\r\n                return { width: 12, height: 12, length: 16 };\r\n        }\r\n\r\n        return { width: 1, height: 1, length: 4 };\r\n    }\r\n\r\n    public static IsHardwareTexture(texture: IHardwareTextureWrapper | GPUTexture): texture is IHardwareTextureWrapper {\r\n        return !!(texture as IHardwareTextureWrapper).release;\r\n    }\r\n\r\n    public static IsInternalTexture(texture: InternalTexture | GPUTexture): texture is InternalTexture {\r\n        return !!(texture as InternalTexture).dispose;\r\n    }\r\n\r\n    public static IsImageBitmap(imageBitmap: ImageBitmap | { width: number; height: number }): imageBitmap is ImageBitmap {\r\n        return (imageBitmap as ImageBitmap).close !== undefined;\r\n    }\r\n\r\n    public static IsImageBitmapArray(imageBitmap: ImageBitmap[] | { width: number; height: number }): imageBitmap is ImageBitmap[] {\r\n        return Array.isArray(imageBitmap as ImageBitmap[]) && (imageBitmap as ImageBitmap[])[0].close !== undefined;\r\n    }\r\n\r\n    public static IsCompressedFormat(format: GPUTextureFormat): boolean {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBFloat:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBUFloat:\r\n            case WebGPUConstants.TextureFormat.BC5RGSnorm:\r\n            case WebGPUConstants.TextureFormat.BC5RGUnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RSnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RUnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.EACR11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACR11Snorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Snorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12UnormSRGB:\r\n                return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public static GetWebGPUTextureFormat(type: number, format: number, useSRGBBuffer = false): GPUTextureFormat {\r\n        switch (format) {\r\n            case Constants.TEXTUREFORMAT_DEPTH16:\r\n                return WebGPUConstants.TextureFormat.Depth16Unorm;\r\n            case Constants.TEXTUREFORMAT_DEPTH24:\r\n                return WebGPUConstants.TextureFormat.Depth24Plus;\r\n            case Constants.TEXTUREFORMAT_DEPTH24_STENCIL8:\r\n                return WebGPUConstants.TextureFormat.Depth24PlusStencil8;\r\n            case Constants.TEXTUREFORMAT_DEPTH32_FLOAT:\r\n                return WebGPUConstants.TextureFormat.Depth32Float;\r\n            case Constants.TEXTUREFORMAT_DEPTH32FLOAT_STENCIL8:\r\n                return WebGPUConstants.TextureFormat.Depth32FloatStencil8;\r\n            case Constants.TEXTUREFORMAT_STENCIL8:\r\n                return WebGPUConstants.TextureFormat.Stencil8;\r\n\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_BPTC_UNORM:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB : WebGPUConstants.TextureFormat.BC7RGBAUnorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT:\r\n                return WebGPUConstants.TextureFormat.BC6HRGBUFloat;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB_BPTC_SIGNED_FLOAT:\r\n                return WebGPUConstants.TextureFormat.BC6HRGBFloat;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT5:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB : WebGPUConstants.TextureFormat.BC3RGBAUnorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT3:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB : WebGPUConstants.TextureFormat.BC2RGBAUnorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_S3TC_DXT1:\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB_S3TC_DXT1:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB : WebGPUConstants.TextureFormat.BC1RGBAUnorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA_ASTC_4x4:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB : WebGPUConstants.TextureFormat.ASTC4x4Unorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB_ETC1_WEBGL:\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGB8_ETC2:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB : WebGPUConstants.TextureFormat.ETC2RGB8Unorm;\r\n            case Constants.TEXTUREFORMAT_COMPRESSED_RGBA8_ETC2_EAC:\r\n                return useSRGBBuffer ? WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB : WebGPUConstants.TextureFormat.ETC2RGBA8Unorm;\r\n        }\r\n\r\n        switch (type) {\r\n            case Constants.TEXTURETYPE_BYTE:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return WebGPUConstants.TextureFormat.R8Snorm;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return WebGPUConstants.TextureFormat.RG8Snorm;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"RGB format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R8Sint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG8Sint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA8Sint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA8Snorm;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_BYTE:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return WebGPUConstants.TextureFormat.R8Unorm;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return WebGPUConstants.TextureFormat.RG8Unorm;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return useSRGBBuffer ? WebGPUConstants.TextureFormat.RGBA8UnormSRGB : WebGPUConstants.TextureFormat.RGBA8Unorm;\r\n                    case Constants.TEXTUREFORMAT_BGRA:\r\n                        return useSRGBBuffer ? WebGPUConstants.TextureFormat.BGRA8UnormSRGB : WebGPUConstants.TextureFormat.BGRA8Unorm;\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R8Uint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG8Uint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA8Uint;\r\n                    case Constants.TEXTUREFORMAT_ALPHA:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_ALPHA format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_LUMINANCE format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_LUMINANCE_ALPHA:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_LUMINANCE_ALPHA format not supported in WebGPU\";\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA8Unorm;\r\n                }\r\n            case Constants.TEXTURETYPE_SHORT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R16Sint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG16Sint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Sint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Sint;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R16Uint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG16Uint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Uint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Uint;\r\n                }\r\n            case Constants.TEXTURETYPE_INT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R32Sint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG32Sint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Sint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Sint;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_INTEGER: // Refers to UNSIGNED_INT\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.R32Uint;\r\n                    case Constants.TEXTUREFORMAT_RG_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RG32Uint;\r\n                    case Constants.TEXTUREFORMAT_RGB_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB_INTEGER format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Uint;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Uint;\r\n                }\r\n            case Constants.TEXTURETYPE_FLOAT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return WebGPUConstants.TextureFormat.R32Float; // By default. Other possibility is R16Float.\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return WebGPUConstants.TextureFormat.RG32Float; // By default. Other possibility is RG16Float.\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Float; // By default. Other possibility is RGBA16Float.\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA32Float;\r\n                }\r\n            case Constants.TEXTURETYPE_HALF_FLOAT:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RED:\r\n                        return WebGPUConstants.TextureFormat.R16Float;\r\n                    case Constants.TEXTUREFORMAT_RG:\r\n                        return WebGPUConstants.TextureFormat.RG16Float;\r\n                    case Constants.TEXTUREFORMAT_RGB:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGB format not supported in WebGPU\";\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Float;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGBA16Float;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_6_5:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"TEXTURETYPE_UNSIGNED_SHORT_5_6_5 format not supported in WebGPU\";\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RG11B10UFloat;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGBA_INTEGER format not supported in WebGPU when type is TEXTURETYPE_UNSIGNED_INT_10F_11F_11F_REV\";\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RG11B10UFloat;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RGB9E5UFloat;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        // eslint-disable-next-line no-throw-literal\r\n                        throw \"TEXTUREFORMAT_RGBA_INTEGER format not supported in WebGPU when type is TEXTURETYPE_UNSIGNED_INT_5_9_9_9_REV\";\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGB9E5UFloat;\r\n                }\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"TEXTURETYPE_UNSIGNED_SHORT_4_4_4_4 format not supported in WebGPU\";\r\n            case Constants.TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1:\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"TEXTURETYPE_UNSIGNED_SHORT_5_5_5_1 format not supported in WebGPU\";\r\n            case Constants.TEXTURETYPE_UNSIGNED_INT_2_10_10_10_REV:\r\n                switch (format) {\r\n                    case Constants.TEXTUREFORMAT_RGBA:\r\n                        return WebGPUConstants.TextureFormat.RGB10A2Unorm;\r\n                    case Constants.TEXTUREFORMAT_RGBA_INTEGER:\r\n                        return WebGPUConstants.TextureFormat.RGB10A2UINT;\r\n                    default:\r\n                        return WebGPUConstants.TextureFormat.RGB10A2Unorm;\r\n                }\r\n        }\r\n\r\n        return useSRGBBuffer ? WebGPUConstants.TextureFormat.RGBA8UnormSRGB : WebGPUConstants.TextureFormat.RGBA8Unorm;\r\n    }\r\n\r\n    public static GetNumChannelsFromWebGPUTextureFormat(format: GPUTextureFormat): number {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.R8Unorm:\r\n            case WebGPUConstants.TextureFormat.R8Snorm:\r\n            case WebGPUConstants.TextureFormat.R8Uint:\r\n            case WebGPUConstants.TextureFormat.R8Sint:\r\n            case WebGPUConstants.TextureFormat.BC4RUnorm:\r\n            case WebGPUConstants.TextureFormat.BC4RSnorm:\r\n            case WebGPUConstants.TextureFormat.R16Uint:\r\n            case WebGPUConstants.TextureFormat.R16Sint:\r\n            case WebGPUConstants.TextureFormat.Depth16Unorm:\r\n            case WebGPUConstants.TextureFormat.R16Float:\r\n            case WebGPUConstants.TextureFormat.R16Unorm:\r\n            case WebGPUConstants.TextureFormat.R16Snorm:\r\n            case WebGPUConstants.TextureFormat.R32Uint:\r\n            case WebGPUConstants.TextureFormat.R32Sint:\r\n            case WebGPUConstants.TextureFormat.R32Float:\r\n            case WebGPUConstants.TextureFormat.Depth32Float:\r\n            case WebGPUConstants.TextureFormat.Stencil8:\r\n            case WebGPUConstants.TextureFormat.Depth24Plus:\r\n            case WebGPUConstants.TextureFormat.EACR11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACR11Snorm:\r\n                return 1;\r\n\r\n            case WebGPUConstants.TextureFormat.RG8Unorm:\r\n            case WebGPUConstants.TextureFormat.RG8Snorm:\r\n            case WebGPUConstants.TextureFormat.RG8Uint:\r\n            case WebGPUConstants.TextureFormat.RG8Sint:\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n            case WebGPUConstants.TextureFormat.BC5RGUnorm:\r\n            case WebGPUConstants.TextureFormat.BC5RGSnorm:\r\n            case WebGPUConstants.TextureFormat.RG16Uint:\r\n            case WebGPUConstants.TextureFormat.RG16Sint:\r\n            case WebGPUConstants.TextureFormat.RG16Float:\r\n            case WebGPUConstants.TextureFormat.RG16Unorm:\r\n            case WebGPUConstants.TextureFormat.RG16Snorm:\r\n            case WebGPUConstants.TextureFormat.RG32Uint:\r\n            case WebGPUConstants.TextureFormat.RG32Sint:\r\n            case WebGPUConstants.TextureFormat.RG32Float:\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n            case WebGPUConstants.TextureFormat.EACRG11Unorm:\r\n            case WebGPUConstants.TextureFormat.EACRG11Snorm:\r\n                return 2;\r\n\r\n            case WebGPUConstants.TextureFormat.RGB9E5UFloat:\r\n            case WebGPUConstants.TextureFormat.RG11B10UFloat:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBUFloat:\r\n            case WebGPUConstants.TextureFormat.BC6HRGBFloat:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8UnormSRGB:\r\n                return 3;\r\n\r\n            case WebGPUConstants.TextureFormat.RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGBA8Snorm:\r\n            case WebGPUConstants.TextureFormat.RGBA8Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA8Sint:\r\n            case WebGPUConstants.TextureFormat.BGRA8Unorm:\r\n            case WebGPUConstants.TextureFormat.BGRA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGBA16Unorm:\r\n            case WebGPUConstants.TextureFormat.RGBA16Snorm:\r\n            case WebGPUConstants.TextureFormat.RGB10A2UINT:\r\n            case WebGPUConstants.TextureFormat.RGB10A2Unorm:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC7RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC3RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC2RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnorm:\r\n            case WebGPUConstants.TextureFormat.BC1RGBAUnormSRGB:\r\n            case WebGPUConstants.TextureFormat.RGBA16Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA16Float:\r\n            case WebGPUConstants.TextureFormat.RGBA32Uint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Sint:\r\n            case WebGPUConstants.TextureFormat.RGBA32Float:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGB8A1UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8Unorm:\r\n            case WebGPUConstants.TextureFormat.ETC2RGBA8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC4x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x4UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC5x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC6x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC8x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x5UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x6UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x8UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC10x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x10UnormSRGB:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12Unorm:\r\n            case WebGPUConstants.TextureFormat.ASTC12x12UnormSRGB:\r\n                return 4;\r\n        }\r\n\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw `Unknown format ${format}!`;\r\n    }\r\n\r\n    public static HasStencilAspect(format: GPUTextureFormat): boolean {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.Stencil8:\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public static HasDepthAndStencilAspects(format: GPUTextureFormat): boolean {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                return true;\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    public static GetDepthFormatOnly(format: GPUTextureFormat): GPUTextureFormat {\r\n        switch (format) {\r\n            case WebGPUConstants.TextureFormat.Depth16Unorm:\r\n                return WebGPUConstants.TextureFormat.Depth16Unorm;\r\n            case WebGPUConstants.TextureFormat.Depth24Plus:\r\n                return WebGPUConstants.TextureFormat.Depth24Plus;\r\n            case WebGPUConstants.TextureFormat.Depth24PlusStencil8:\r\n                return WebGPUConstants.TextureFormat.Depth24Plus;\r\n            case WebGPUConstants.TextureFormat.Depth32Float:\r\n                return WebGPUConstants.TextureFormat.Depth32Float;\r\n            case WebGPUConstants.TextureFormat.Depth32FloatStencil8:\r\n                return WebGPUConstants.TextureFormat.Depth32Float;\r\n        }\r\n\r\n        return format;\r\n    }\r\n\r\n    public static GetSample(sampleCount: number) {\r\n        // WebGPU only supports 1 or 4\r\n        return sampleCount > 1 ? 4 : 1;\r\n    }\r\n}\r\n"]}