{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/scripts/advanced-3d-showcase.ts"], "sourcesContent": ["import {\n  Engine,\n  Scene,\n  ArcRotateCamera,\n  Vector3,\n  Vector2,\n  Color3,\n  Color4,\n  Hemis<PERSON>Light,\n  DirectionalLight,\n  <PERSON>Light,\n  PointLight,\n  ShadowGenerator,\n  MeshBuilder,\n  Mesh,\n  PBRMaterial,\n  CubeTexture,\n  Texture,\n  DynamicTexture,\n  Animation,\n  AnimationGroup,\n  DefaultRenderingPipeline,\n  WebXRDefaultExperience,\n  WebXRFeatureName,\n  Sound,\n  Analyser,\n  TransformNode,\n  ParticleSystem,\n  GPUParticleSystem,\n  NodeMaterial,\n  Tools,\n  SceneLoader,\n  AssetContainer,\n  Matrix,\n  Quaternion,\n  StandardMaterial,\n  ProceduralTexture,\n  NoiseProceduralTexture,\n} from \"@babylonjs/core\";\n\nimport \"@babylonjs/core/Materials/Node/Blocks\";\nimport \"@babylonjs/loaders/glTF\";\nimport \"@babylonjs/materials/custom\";\nimport \"@babylonjs/post-processes\";\n\ninterface ShowcaseOptions {\n  enableWebXR?: boolean;\n  enableAudioReactive?: boolean;\n  showPerformanceStats?: boolean;\n}\n\nexport default class Advanced3DShowcaseEngine {\n  canvas: HTMLCanvasElement;\n  engine: Engine;\n  scene: Scene;\n  camera: ArcRotateCamera;\n  options: ShowcaseOptions;\n\n  // Advanced features\n  renderingPipeline: DefaultRenderingPipeline | null = null;\n  webXRExperience: WebXRDefaultExperience | null = null;\n  audioAnalyser: Analyser | null = null;\n  audioContext: AudioContext | null = null;\n\n  // 3D Objects and animations\n  showcaseObjects: Mesh[] = [];\n  particleSystems: (ParticleSystem | GPUParticleSystem)[] = [];\n  animationGroups: AnimationGroup[] = [];\n\n  // Performance tracking\n  performanceStats = {\n    fps: 0,\n    drawCalls: 0,\n    triangles: 0,\n  };\n\n  constructor(canvas: HTMLCanvasElement, options: ShowcaseOptions = {}) {\n    this.canvas = canvas;\n    this.options = options;\n\n    // Initialize Babylon.js engine\n    this.engine = new Engine(canvas, true, {\n      adaptToDeviceRatio: true,\n      antialias: true,\n      powerPreference: \"high-performance\",\n      xrCompatible: options.enableWebXR,\n    });\n\n    // Create scene\n    this.scene = new Scene(this.engine);\n    this.scene.useRightHandedSystem = true;\n\n    // Setup camera\n    this.camera = new ArcRotateCamera(\n      \"showcaseCamera\",\n      -Math.PI / 2,\n      Math.PI / 2.5,\n      25,\n      Vector3.Zero(),\n      this.scene\n    );\n    this.camera.attachControl(canvas, true);\n    this.camera.wheelPrecision = 50;\n    this.camera.minZ = 0.1;\n    this.camera.maxZ = 1000;\n\n    this.initialize();\n  }\n\n  async initialize() {\n    try {\n      // Setup lighting\n      this.setupAdvancedLighting();\n\n      // Create advanced materials showcase\n      this.createMaterialShowcase();\n\n      // Setup advanced post-processing\n      this.setupAdvancedPostProcessing();\n\n      // Create particle systems\n      this.createAdvancedParticles();\n\n      // Setup WebXR if enabled\n      if (this.options.enableWebXR) {\n        await this.setupWebXR();\n      }\n\n      // Setup audio reactive features if enabled\n      if (this.options.enableAudioReactive) {\n        await this.setupAudioReactive();\n      }\n\n      // Start render loop\n      this.startRenderLoop();\n\n      // Setup performance monitoring\n      if (this.options.showPerformanceStats) {\n        this.setupPerformanceMonitoring();\n      }\n\n      console.log(\"Advanced 3D Showcase initialized successfully\");\n    } catch (error) {\n      console.error(\"Error initializing Advanced 3D Showcase:\", error);\n    }\n  }\n\n  setupAdvancedLighting() {\n    // Ambient lighting\n    const hemisphericLight = new HemisphericLight(\n      \"hemisphericLight\",\n      new Vector3(0, 1, 0),\n      this.scene\n    );\n    hemisphericLight.intensity = 0.3;\n    hemisphericLight.diffuse = new Color3(0.2, 0.4, 0.8);\n    hemisphericLight.specular = new Color3(0.1, 0.2, 0.4);\n\n    // Main directional light with shadows\n    const directionalLight = new DirectionalLight(\n      \"directionalLight\",\n      new Vector3(-1, -1, -1),\n      this.scene\n    );\n    directionalLight.intensity = 1.2;\n    directionalLight.diffuse = new Color3(1, 0.9, 0.8);\n    directionalLight.specular = new Color3(1, 1, 1);\n\n    // Setup shadow generator\n    const shadowGenerator = new ShadowGenerator(2048, directionalLight);\n    shadowGenerator.useExponentialShadowMap = true;\n    shadowGenerator.darkness = 0.3;\n\n    // Accent spot lights\n    const spotLight1 = new SpotLight(\n      \"spotLight1\",\n      new Vector3(-10, 15, -10),\n      new Vector3(1, -1, 1),\n      Math.PI / 3,\n      2,\n      this.scene\n    );\n    spotLight1.intensity = 0.8;\n    spotLight1.diffuse = new Color3(0, 1, 1);\n\n    const spotLight2 = new SpotLight(\n      \"spotLight2\",\n      new Vector3(10, 15, 10),\n      new Vector3(-1, -1, -1),\n      Math.PI / 3,\n      2,\n      this.scene\n    );\n    spotLight2.intensity = 0.8;\n    spotLight2.diffuse = new Color3(1, 0, 1);\n\n    // Animated point lights\n    const pointLight1 = new PointLight(\n      \"pointLight1\",\n      new Vector3(0, 5, 0),\n      this.scene\n    );\n    pointLight1.intensity = 1.5;\n    pointLight1.diffuse = new Color3(1, 0.5, 0);\n    pointLight1.range = 20;\n\n    // Animate point light\n    Animation.CreateAndStartAnimation(\n      \"pointLightAnimation\",\n      pointLight1,\n      \"position\",\n      30,\n      120,\n      pointLight1.position,\n      new Vector3(8, 5, 8),\n      Animation.ANIMATIONLOOPMODE_YOYO\n    );\n  }\n\n  createMaterialShowcase() {\n    // Create a collection of objects showcasing different PBR materials\n    const showcaseItems = [\n      { type: \"sphere\", position: new Vector3(-8, 2, 0), material: \"chrome\" },\n      { type: \"box\", position: new Vector3(-4, 2, 0), material: \"gold\" },\n      { type: \"cylinder\", position: new Vector3(0, 2, 0), material: \"glass\" },\n      { type: \"torus\", position: new Vector3(4, 2, 0), material: \"carbon\" },\n      {\n        type: \"dodecahedron\",\n        position: new Vector3(8, 2, 0),\n        material: \"iridescent\",\n      },\n      { type: \"ground\", position: new Vector3(0, 0, 0), material: \"marble\" },\n    ];\n\n    showcaseItems.forEach((item, index) => {\n      let mesh: Mesh;\n\n      switch (item.type) {\n        case \"sphere\":\n          mesh = MeshBuilder.CreateSphere(\n            `showcase_sphere_${index}`,\n            { diameter: 3, segments: 32 },\n            this.scene\n          );\n          break;\n        case \"box\":\n          mesh = MeshBuilder.CreateBox(\n            `showcase_box_${index}`,\n            { size: 2.5 },\n            this.scene\n          );\n          break;\n        case \"cylinder\":\n          mesh = MeshBuilder.CreateCylinder(\n            `showcase_cylinder_${index}`,\n            { height: 3, diameter: 2.5, tessellation: 24 },\n            this.scene\n          );\n          break;\n        case \"torus\":\n          mesh = MeshBuilder.CreateTorus(\n            `showcase_torus_${index}`,\n            { diameter: 3, thickness: 1, tessellation: 32 },\n            this.scene\n          );\n          break;\n        case \"dodecahedron\":\n          mesh = MeshBuilder.CreatePolyhedron(\n            `showcase_dodecahedron_${index}`,\n            { type: 2, size: 1.5 },\n            this.scene\n          );\n          break;\n        case \"ground\":\n          mesh = MeshBuilder.CreateGround(\n            `showcase_ground_${index}`,\n            { width: 30, height: 30, subdivisions: 32 },\n            this.scene\n          );\n          break;\n        default:\n          return;\n      }\n\n      mesh.position = item.position;\n      mesh.material = this.createAdvancedMaterial(\n        item.material,\n        `${item.material}_${index}`\n      );\n\n      // Add floating animation for non-ground objects\n      if (item.type !== \"ground\") {\n        Animation.CreateAndStartAnimation(\n          `float_${index}`,\n          mesh,\n          \"position.y\",\n          30,\n          120 + index * 10,\n          mesh.position.y,\n          mesh.position.y + 1 + Math.sin(index) * 0.3,\n          Animation.ANIMATIONLOOPMODE_YOYO\n        );\n\n        Animation.CreateAndStartAnimation(\n          `rotate_${index}`,\n          mesh,\n          \"rotation.y\",\n          30,\n          180 + index * 20,\n          0,\n          Math.PI * 2,\n          Animation.ANIMATIONLOOPMODE_CYCLE\n        );\n      }\n\n      this.showcaseObjects.push(mesh);\n    });\n  }\n\n  createAdvancedMaterial(materialType: string, name: string): PBRMaterial {\n    const pbr = new PBRMaterial(name, this.scene);\n\n    // Create environment texture for reflections\n    const envTexture = this.createEnvironmentTexture();\n    this.scene.environmentTexture = envTexture;\n\n    switch (materialType) {\n      case \"chrome\":\n        pbr.albedoColor = new Color3(0.9, 0.9, 0.9);\n        pbr.metallic = 1.0;\n        pbr.roughness = 0.05;\n        pbr.environmentIntensity = 1.5;\n        break;\n\n      case \"gold\":\n        pbr.albedoColor = new Color3(1.0, 0.8, 0.2);\n        pbr.metallic = 1.0;\n        pbr.roughness = 0.1;\n        pbr.environmentIntensity = 1.2;\n        break;\n\n      case \"glass\":\n        pbr.albedoColor = new Color3(0.95, 0.98, 1.0);\n        pbr.metallic = 0.0;\n        pbr.roughness = 0.0;\n        pbr.alpha = 0.15;\n        pbr.indexOfRefraction = 1.52;\n        pbr.linkRefractionWithTransparency = true;\n        pbr.subSurface.isRefractionEnabled = true;\n        pbr.subSurface.refractionIntensity = 1.0;\n        break;\n\n      case \"carbon\":\n        pbr.albedoColor = new Color3(0.1, 0.1, 0.1);\n        pbr.metallic = 0.8;\n        pbr.roughness = 0.3;\n        pbr.anisotropy.isEnabled = true;\n        pbr.anisotropy.intensity = 1.0;\n        pbr.anisotropy.direction = new Vector2(1, 0);\n        break;\n\n      case \"iridescent\":\n        pbr.albedoColor = new Color3(0.05, 0.05, 0.05);\n        pbr.metallic = 1.0;\n        pbr.roughness = 0.0;\n        pbr.iridescence.isEnabled = true;\n        pbr.iridescence.intensity = 1.0;\n        pbr.iridescence.indexOfRefraction = 1.3;\n        pbr.iridescence.minimumThickness = 100;\n        pbr.iridescence.maximumThickness = 400;\n        break;\n\n      case \"marble\":\n        pbr.albedoColor = new Color3(0.9, 0.9, 0.85);\n        pbr.metallic = 0.0;\n        pbr.roughness = 0.6;\n        pbr.subSurface.isTranslucencyEnabled = true;\n        pbr.subSurface.translucencyIntensity = 0.3;\n        pbr.subSurface.tintColor = new Color3(0.95, 0.95, 0.9);\n\n        // Add procedural marble texture\n        const marbleTexture = this.createMarbleTexture();\n        pbr.albedoTexture = marbleTexture;\n        pbr.bumpTexture = marbleTexture;\n        break;\n\n      default:\n        pbr.albedoColor = new Color3(0.5, 0.5, 0.5);\n        pbr.metallic = 0.5;\n        pbr.roughness = 0.5;\n        break;\n    }\n\n    return pbr;\n  }\n\n  createEnvironmentTexture(): CubeTexture {\n    // Create a procedural environment texture\n    const envTexture = CubeTexture.CreateFromImages(\n      [\n        this.createSkyTexture(\"px\"),\n        this.createSkyTexture(\"nx\"),\n        this.createSkyTexture(\"py\"),\n        this.createSkyTexture(\"ny\"),\n        this.createSkyTexture(\"pz\"),\n        this.createSkyTexture(\"nz\"),\n      ],\n      this.scene\n    );\n\n    return envTexture;\n  }\n\n  createSkyTexture(face: string): string {\n    const canvas = document.createElement(\"canvas\");\n    canvas.width = 512;\n    canvas.height = 512;\n    const ctx = canvas.getContext(\"2d\")!;\n\n    // Create cyberpunk-style sky gradient\n    const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 512);\n    gradient.addColorStop(0, \"#001a33\");\n    gradient.addColorStop(0.3, \"#003366\");\n    gradient.addColorStop(0.6, \"#001122\");\n    gradient.addColorStop(1, \"#000011\");\n\n    ctx.fillStyle = gradient;\n    ctx.fillRect(0, 0, 512, 512);\n\n    // Add stars and nebula effects\n    for (let i = 0; i < 150; i++) {\n      const x = Math.random() * 512;\n      const y = Math.random() * 512;\n      const brightness = Math.random();\n      const size = Math.random() * 2 + 1;\n\n      ctx.fillStyle = `rgba(${100 + brightness * 155}, ${\n        150 + brightness * 105\n      }, 255, ${brightness})`;\n      ctx.fillRect(x, y, size, size);\n    }\n\n    return canvas.toDataURL();\n  }\n\n  createMarbleTexture(): DynamicTexture {\n    const texture = new DynamicTexture(\n      \"marbleTexture\",\n      { width: 512, height: 512 },\n      this.scene\n    );\n    const ctx = texture.getContext();\n\n    // Create marble pattern\n    const imageData = new ImageData(512, 512);\n    const data = imageData.data;\n\n    for (let y = 0; y < 512; y++) {\n      for (let x = 0; x < 512; x++) {\n        const index = (y * 512 + x) * 4;\n\n        // Create marble-like noise pattern\n        const noise1 = Math.sin(x * 0.01 + y * 0.01) * 0.5 + 0.5;\n        const noise2 = Math.sin(x * 0.02 + y * 0.005) * 0.3 + 0.7;\n        const noise3 = Math.sin(x * 0.005 + y * 0.02) * 0.2 + 0.8;\n\n        const marble = noise1 * noise2 * noise3;\n        const color = Math.floor(200 + marble * 55);\n\n        data[index] = color; // R\n        data[index + 1] = color; // G\n        data[index + 2] = Math.floor(color * 0.95); // B\n        data[index + 3] = 255; // A\n      }\n    }\n\n    ctx.putImageData(imageData, 0, 0);\n    texture.update();\n\n    return texture;\n  }\n\n  setupAdvancedPostProcessing() {\n    try {\n      // Create advanced rendering pipeline\n      this.renderingPipeline = new DefaultRenderingPipeline(\n        \"advancedPipeline\",\n        true, // HDR enabled\n        this.scene,\n        [this.camera]\n      );\n\n      if (this.renderingPipeline) {\n        // Configure bloom for glowing effects\n        this.renderingPipeline.bloomEnabled = true;\n        this.renderingPipeline.bloomThreshold = 0.7;\n        this.renderingPipeline.bloomWeight = 0.4;\n        this.renderingPipeline.bloomKernel = 64;\n        this.renderingPipeline.bloomScale = 0.6;\n\n        // Enable tone mapping for HDR\n        if (this.renderingPipeline.imageProcessing) {\n          this.renderingPipeline.imageProcessing.toneMappingEnabled = true;\n          this.renderingPipeline.imageProcessing.toneMappingType = 1; // ACES tone mapping\n        }\n\n        // Enable FXAA anti-aliasing\n        this.renderingPipeline.fxaaEnabled = true;\n\n        // Configure chromatic aberration\n        this.renderingPipeline.chromaticAberrationEnabled = true;\n        this.renderingPipeline.chromaticAberration.aberrationAmount = 20;\n        this.renderingPipeline.chromaticAberration.radialIntensity = 0.8;\n\n        // Configure grain effect\n        this.renderingPipeline.grainEnabled = true;\n        this.renderingPipeline.grain.intensity = 8;\n        this.renderingPipeline.grain.animated = true;\n\n        // Configure vignette\n        if (this.renderingPipeline.imageProcessing) {\n          this.renderingPipeline.imageProcessing.vignetteEnabled = true;\n          this.renderingPipeline.imageProcessing.vignetteStretch = 0.15;\n          this.renderingPipeline.imageProcessing.vignetteWeight = 1.2;\n          this.renderingPipeline.imageProcessing.vignetteColor = new Color4(\n            0,\n            0,\n            0,\n            0\n          );\n        }\n\n        // Enable depth of field\n        this.renderingPipeline.depthOfFieldEnabled = true;\n        this.renderingPipeline.depthOfFieldBlurLevel = 0;\n        this.renderingPipeline.depthOfField.focusDistance = 2000;\n        this.renderingPipeline.depthOfField.focalLength = 50;\n        this.renderingPipeline.depthOfField.fStop = 1.4;\n\n        // Enable screen space reflections (commented out due to API changes)\n        // this.renderingPipeline.screenSpaceReflectionEnabled = true;\n        // if (this.renderingPipeline.screenSpaceReflectionPostProcess) {\n        //   this.renderingPipeline.screenSpaceReflectionPostProcess.strength = 0.6;\n        //   this.renderingPipeline.screenSpaceReflectionPostProcess.reflectionSpecularFalloffExponent = 1.2;\n        //   this.renderingPipeline.screenSpaceReflectionPostProcess.maxDistance = 1500;\n        // }\n      }\n\n      console.log(\"Advanced post-processing pipeline setup complete\");\n    } catch (error) {\n      console.error(\"Error setting up post-processing:\", error);\n    }\n  }\n\n  createAdvancedParticles() {\n    try {\n      // Create GPU-based particle system for better performance\n      const particleSystem = new GPUParticleSystem(\n        \"advancedParticles\",\n        { capacity: 50000 },\n        this.scene\n      );\n\n      // Configure particle emitter\n      particleSystem.emitter = Vector3.Zero();\n      particleSystem.minEmitBox = new Vector3(-15, 0, -15);\n      particleSystem.maxEmitBox = new Vector3(15, 0, 15);\n\n      // Particle properties\n      particleSystem.particleTexture = this.createParticleTexture();\n      particleSystem.emitRate = 1000;\n      particleSystem.minLifeTime = 2;\n      particleSystem.maxLifeTime = 8;\n      particleSystem.minSize = 0.1;\n      particleSystem.maxSize = 0.8;\n      particleSystem.minInitialRotation = 0;\n      particleSystem.maxInitialRotation = Math.PI * 2;\n      particleSystem.minAngularSpeed = -Math.PI;\n      particleSystem.maxAngularSpeed = Math.PI;\n\n      // Particle movement\n      particleSystem.direction1 = new Vector3(-1, 1, -1);\n      particleSystem.direction2 = new Vector3(1, 1, 1);\n      particleSystem.minEmitPower = 2;\n      particleSystem.maxEmitPower = 6;\n      particleSystem.updateSpeed = 0.02;\n\n      // Gravity and forces\n      particleSystem.gravity = new Vector3(0, -2, 0);\n\n      // Color animation\n      particleSystem.color1 = new Color4(0, 1, 1, 1);\n      particleSystem.color2 = new Color4(1, 0, 1, 1);\n      particleSystem.colorDead = new Color4(0, 0, 0, 0);\n\n      // Blending mode\n      particleSystem.blendMode = ParticleSystem.BLENDMODE_ONEONE;\n\n      // Start the particle system\n      particleSystem.start();\n      this.particleSystems.push(particleSystem);\n\n      console.log(\"Advanced particle system created\");\n    } catch (error) {\n      console.error(\"Error creating particle system:\", error);\n    }\n  }\n\n  createParticleTexture(): Texture {\n    const texture = new DynamicTexture(\n      \"particleTexture\",\n      { width: 64, height: 64 },\n      this.scene\n    );\n    const ctx = texture.getContext();\n\n    // Create glowing particle texture\n    const gradient = ctx.createRadialGradient(32, 32, 0, 32, 32, 32);\n    gradient.addColorStop(0, \"rgba(255, 255, 255, 1)\");\n    gradient.addColorStop(0.3, \"rgba(0, 255, 255, 0.8)\");\n    gradient.addColorStop(0.6, \"rgba(255, 0, 255, 0.4)\");\n    gradient.addColorStop(1, \"rgba(0, 0, 0, 0)\");\n\n    ctx.fillStyle = gradient;\n    ctx.fillRect(0, 0, 64, 64);\n    texture.update();\n\n    return texture;\n  }\n\n  async setupWebXR() {\n    try {\n      if (\"xr\" in navigator) {\n        this.webXRExperience = await this.scene.createDefaultXRExperienceAsync({\n          floorMeshes: this.showcaseObjects.filter((obj) =>\n            obj.name.includes(\"ground\")\n          ),\n        });\n\n        if (this.webXRExperience) {\n          // Enable hand tracking\n          const handTracking =\n            this.webXRExperience.baseExperience.featuresManager.enableFeature(\n              WebXRFeatureName.HAND_TRACKING,\n              \"latest\"\n            );\n\n          // Enable hit test for AR\n          const hitTest =\n            this.webXRExperience.baseExperience.featuresManager.enableFeature(\n              WebXRFeatureName.HIT_TEST,\n              \"latest\"\n            );\n\n          console.log(\"WebXR experience initialized\");\n        }\n      }\n    } catch (error) {\n      console.error(\"Error setting up WebXR:\", error);\n    }\n  }\n\n  async setupAudioReactive() {\n    try {\n      // Request microphone access\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n\n      // Create audio context and analyser\n      this.audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const source = this.audioContext.createMediaStreamSource(stream);\n\n      this.audioAnalyser = new Analyser(this.scene);\n\n      console.log(\"Audio reactive features initialized\");\n    } catch (error) {\n      console.error(\"Error setting up audio reactive features:\", error);\n    }\n  }\n\n  startRenderLoop() {\n    this.engine.runRenderLoop(() => {\n      if (this.scene) {\n        // Update audio reactive effects\n        if (this.audioAnalyser && this.options.enableAudioReactive) {\n          this.updateAudioReactiveEffects();\n        }\n\n        // Render the scene\n        this.scene.render();\n      }\n    });\n  }\n\n  updateAudioReactiveEffects() {\n    if (!this.audioAnalyser) return;\n\n    try {\n      const frequencyData = this.audioAnalyser.getByteFrequencyData();\n      const averageFrequency =\n        frequencyData.reduce((a, b) => a + b) / frequencyData.length;\n      const normalizedFrequency = averageFrequency / 255;\n\n      // Update particle emission rate based on audio\n      this.particleSystems.forEach((system) => {\n        if (system instanceof GPUParticleSystem) {\n          system.emitRate = 500 + normalizedFrequency * 2000;\n        }\n      });\n\n      // Update post-processing effects based on audio\n      if (this.renderingPipeline) {\n        this.renderingPipeline.bloomWeight = 0.2 + normalizedFrequency * 0.6;\n        this.renderingPipeline.chromaticAberration.aberrationAmount =\n          10 + normalizedFrequency * 40;\n      }\n\n      // Update object animations based on audio\n      this.showcaseObjects.forEach((obj, index) => {\n        if (!obj.name.includes(\"ground\")) {\n          const scaleMultiplier = 1 + normalizedFrequency * 0.3;\n          obj.scaling = new Vector3(\n            scaleMultiplier,\n            scaleMultiplier,\n            scaleMultiplier\n          );\n        }\n      });\n    } catch (error) {\n      console.error(\"Error updating audio reactive effects:\", error);\n    }\n  }\n\n  setupPerformanceMonitoring() {\n    setInterval(() => {\n      this.performanceStats.fps = Math.round(this.engine.getFps());\n      this.performanceStats.drawCalls = this.scene.getActiveMeshes().length;\n      this.performanceStats.triangles = this.scene.getTotalVertices();\n    }, 1000);\n  }\n\n  getPerformanceStats() {\n    return this.performanceStats;\n  }\n\n  async toggleWebXR() {\n    if (this.webXRExperience) {\n      try {\n        if (this.webXRExperience.baseExperience.state === 0) {\n          await this.webXRExperience.baseExperience.enterXRAsync(\n            \"immersive-ar\",\n            \"local-floor\"\n          );\n        } else {\n          await this.webXRExperience.baseExperience.exitXRAsync();\n        }\n      } catch (error) {\n        console.error(\"Error toggling WebXR:\", error);\n      }\n    }\n  }\n\n  toggleAudioReactive() {\n    this.options.enableAudioReactive = !this.options.enableAudioReactive;\n    console.log(\n      \"Audio reactive mode:\",\n      this.options.enableAudioReactive ? \"enabled\" : \"disabled\"\n    );\n  }\n\n  destroy() {\n    // Dispose of all resources\n    this.particleSystems.forEach((system) => system.dispose());\n    this.animationGroups.forEach((group) => group.dispose());\n    this.showcaseObjects.forEach((obj) => obj.dispose());\n\n    if (this.renderingPipeline) {\n      this.renderingPipeline.dispose();\n    }\n\n    if (this.webXRExperience) {\n      this.webXRExperience.dispose();\n    }\n\n    if (this.audioContext) {\n      this.audioContext.close();\n    }\n\n    if (this.scene) {\n      this.scene.dispose();\n    }\n\n    if (this.engine) {\n      this.engine.dispose();\n    }\n\n    console.log(\"Advanced 3D Showcase destroyed\");\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwCA;AACA;AACA;AACA;;;;;;AAQe,MAAM;IACnB,OAA0B;IAC1B,OAAe;IACf,MAAa;IACb,OAAwB;IACxB,QAAyB;IAEzB,oBAAoB;IACpB,oBAAqD,KAAK;IAC1D,kBAAiD,KAAK;IACtD,gBAAiC,KAAK;IACtC,eAAoC,KAAK;IAEzC,4BAA4B;IAC5B,kBAA0B,EAAE,CAAC;IAC7B,kBAA0D,EAAE,CAAC;IAC7D,kBAAoC,EAAE,CAAC;IAEvC,uBAAuB;IACvB,mBAAmB;QACjB,KAAK;QACL,WAAW;QACX,WAAW;IACb,EAAE;IAEF,YAAY,MAAyB,EAAE,UAA2B,CAAC,CAAC,CAAE;QACpE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;QAEf,+BAA+B;QAC/B,IAAI,CAAC,MAAM,GAAG,IAAI,wJAAA,CAAA,SAAM,CAAC,QAAQ,MAAM;YACrC,oBAAoB;YACpB,WAAW;YACX,iBAAiB;YACjB,cAAc,QAAQ,WAAW;QACnC;QAEA,eAAe;QACf,IAAI,CAAC,KAAK,GAAG,IAAI,4IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,MAAM;QAClC,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG;QAElC,eAAe;QACf,IAAI,CAAC,MAAM,GAAG,IAAI,iKAAA,CAAA,kBAAe,CAC/B,kBACA,CAAC,KAAK,EAAE,GAAG,GACX,KAAK,EAAE,GAAG,KACV,IACA,8JAAA,CAAA,UAAO,CAAC,IAAI,IACZ,IAAI,CAAC,KAAK;QAEZ,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ;QAClC,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG;QAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;QACnB,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;QAEnB,IAAI,CAAC,UAAU;IACjB;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,iBAAiB;YACjB,IAAI,CAAC,qBAAqB;YAE1B,qCAAqC;YACrC,IAAI,CAAC,sBAAsB;YAE3B,iCAAiC;YACjC,IAAI,CAAC,2BAA2B;YAEhC,0BAA0B;YAC1B,IAAI,CAAC,uBAAuB;YAE5B,yBAAyB;YACzB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;gBAC5B,MAAM,IAAI,CAAC,UAAU;YACvB;YAEA,2CAA2C;YAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBACpC,MAAM,IAAI,CAAC,kBAAkB;YAC/B;YAEA,oBAAoB;YACpB,IAAI,CAAC,eAAe;YAEpB,+BAA+B;YAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;gBACrC,IAAI,CAAC,0BAA0B;YACjC;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;QAC5D;IACF;IAEA,wBAAwB;QACtB,mBAAmB;QACnB,MAAM,mBAAmB,IAAI,iKAAA,CAAA,mBAAgB,CAC3C,oBACA,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,GAAG,IAClB,IAAI,CAAC,KAAK;QAEZ,iBAAiB,SAAS,GAAG;QAC7B,iBAAiB,OAAO,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,KAAK,KAAK;QAChD,iBAAiB,QAAQ,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,KAAK,KAAK;QAEjD,sCAAsC;QACtC,MAAM,mBAAmB,IAAI,iKAAA,CAAA,mBAAgB,CAC3C,oBACA,IAAI,8JAAA,CAAA,UAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IACrB,IAAI,CAAC,KAAK;QAEZ,iBAAiB,SAAS,GAAG;QAC7B,iBAAiB,OAAO,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,GAAG,KAAK;QAC9C,iBAAiB,QAAQ,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,GAAG,GAAG;QAE7C,yBAAyB;QACzB,MAAM,kBAAkB,IAAI,2KAAA,CAAA,kBAAe,CAAC,MAAM;QAClD,gBAAgB,uBAAuB,GAAG;QAC1C,gBAAgB,QAAQ,GAAG;QAE3B,qBAAqB;QACrB,MAAM,aAAa,IAAI,0JAAA,CAAA,YAAS,CAC9B,cACA,IAAI,8JAAA,CAAA,UAAO,CAAC,CAAC,IAAI,IAAI,CAAC,KACtB,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,CAAC,GAAG,IACnB,KAAK,EAAE,GAAG,GACV,GACA,IAAI,CAAC,KAAK;QAEZ,WAAW,SAAS,GAAG;QACvB,WAAW,OAAO,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,GAAG,GAAG;QAEtC,MAAM,aAAa,IAAI,0JAAA,CAAA,YAAS,CAC9B,cACA,IAAI,8JAAA,CAAA,UAAO,CAAC,IAAI,IAAI,KACpB,IAAI,8JAAA,CAAA,UAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IACrB,KAAK,EAAE,GAAG,GACV,GACA,IAAI,CAAC,KAAK;QAEZ,WAAW,SAAS,GAAG;QACvB,WAAW,OAAO,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,GAAG,GAAG;QAEtC,wBAAwB;QACxB,MAAM,cAAc,IAAI,2JAAA,CAAA,aAAU,CAChC,eACA,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,GAAG,IAClB,IAAI,CAAC,KAAK;QAEZ,YAAY,SAAS,GAAG;QACxB,YAAY,OAAO,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,GAAG,KAAK;QACzC,YAAY,KAAK,GAAG;QAEpB,sBAAsB;QACtB,8JAAA,CAAA,YAAS,CAAC,uBAAuB,CAC/B,uBACA,aACA,YACA,IACA,KACA,YAAY,QAAQ,EACpB,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,GAAG,IAClB,8JAAA,CAAA,YAAS,CAAC,sBAAsB;IAEpC;IAEA,yBAAyB;QACvB,oEAAoE;QACpE,MAAM,gBAAgB;YACpB;gBAAE,MAAM;gBAAU,UAAU,IAAI,8JAAA,CAAA,UAAO,CAAC,CAAC,GAAG,GAAG;gBAAI,UAAU;YAAS;YACtE;gBAAE,MAAM;gBAAO,UAAU,IAAI,8JAAA,CAAA,UAAO,CAAC,CAAC,GAAG,GAAG;gBAAI,UAAU;YAAO;YACjE;gBAAE,MAAM;gBAAY,UAAU,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,GAAG;gBAAI,UAAU;YAAQ;YACtE;gBAAE,MAAM;gBAAS,UAAU,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,GAAG;gBAAI,UAAU;YAAS;YACpE;gBACE,MAAM;gBACN,UAAU,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,GAAG;gBAC5B,UAAU;YACZ;YACA;gBAAE,MAAM;gBAAU,UAAU,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,GAAG;gBAAI,UAAU;YAAS;SACtE;QAED,cAAc,OAAO,CAAC,CAAC,MAAM;YAC3B,IAAI;YAEJ,OAAQ,KAAK,IAAI;gBACf,KAAK;oBACH,OAAO,4JAAA,CAAA,cAAW,CAAC,YAAY,CAC7B,CAAC,gBAAgB,EAAE,OAAO,EAC1B;wBAAE,UAAU;wBAAG,UAAU;oBAAG,GAC5B,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,4JAAA,CAAA,cAAW,CAAC,SAAS,CAC1B,CAAC,aAAa,EAAE,OAAO,EACvB;wBAAE,MAAM;oBAAI,GACZ,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,4JAAA,CAAA,cAAW,CAAC,cAAc,CAC/B,CAAC,kBAAkB,EAAE,OAAO,EAC5B;wBAAE,QAAQ;wBAAG,UAAU;wBAAK,cAAc;oBAAG,GAC7C,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,4JAAA,CAAA,cAAW,CAAC,WAAW,CAC5B,CAAC,eAAe,EAAE,OAAO,EACzB;wBAAE,UAAU;wBAAG,WAAW;wBAAG,cAAc;oBAAG,GAC9C,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,4JAAA,CAAA,cAAW,CAAC,gBAAgB,CACjC,CAAC,sBAAsB,EAAE,OAAO,EAChC;wBAAE,MAAM;wBAAG,MAAM;oBAAI,GACrB,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,4JAAA,CAAA,cAAW,CAAC,YAAY,CAC7B,CAAC,gBAAgB,EAAE,OAAO,EAC1B;wBAAE,OAAO;wBAAI,QAAQ;wBAAI,cAAc;oBAAG,GAC1C,IAAI,CAAC,KAAK;oBAEZ;gBACF;oBACE;YACJ;YAEA,KAAK,QAAQ,GAAG,KAAK,QAAQ;YAC7B,KAAK,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CACzC,KAAK,QAAQ,EACb,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,OAAO;YAG7B,gDAAgD;YAChD,IAAI,KAAK,IAAI,KAAK,UAAU;gBAC1B,8JAAA,CAAA,YAAS,CAAC,uBAAuB,CAC/B,CAAC,MAAM,EAAE,OAAO,EAChB,MACA,cACA,IACA,MAAM,QAAQ,IACd,KAAK,QAAQ,CAAC,CAAC,EACf,KAAK,QAAQ,CAAC,CAAC,GAAG,IAAI,KAAK,GAAG,CAAC,SAAS,KACxC,8JAAA,CAAA,YAAS,CAAC,sBAAsB;gBAGlC,8JAAA,CAAA,YAAS,CAAC,uBAAuB,CAC/B,CAAC,OAAO,EAAE,OAAO,EACjB,MACA,cACA,IACA,MAAM,QAAQ,IACd,GACA,KAAK,EAAE,GAAG,GACV,8JAAA,CAAA,YAAS,CAAC,uBAAuB;YAErC;YAEA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAC5B;IACF;IAEA,uBAAuB,YAAoB,EAAE,IAAY,EAAe;QACtE,MAAM,MAAM,IAAI,sKAAA,CAAA,cAAW,CAAC,MAAM,IAAI,CAAC,KAAK;QAE5C,6CAA6C;QAC7C,MAAM,aAAa,IAAI,CAAC,wBAAwB;QAChD,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG;QAEhC,OAAQ;YACN,KAAK;gBACH,IAAI,WAAW,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACvC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,oBAAoB,GAAG;gBAC3B;YAEF,KAAK;gBACH,IAAI,WAAW,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACvC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,oBAAoB,GAAG;gBAC3B;YAEF,KAAK;gBACH,IAAI,WAAW,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,MAAM,MAAM;gBACzC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,KAAK,GAAG;gBACZ,IAAI,iBAAiB,GAAG;gBACxB,IAAI,8BAA8B,GAAG;gBACrC,IAAI,UAAU,CAAC,mBAAmB,GAAG;gBACrC,IAAI,UAAU,CAAC,mBAAmB,GAAG;gBACrC;YAEF,KAAK;gBACH,IAAI,WAAW,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACvC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,UAAU,CAAC,SAAS,GAAG;gBAC3B,IAAI,UAAU,CAAC,SAAS,GAAG;gBAC3B,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG;gBAC1C;YAEF,KAAK;gBACH,IAAI,WAAW,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,MAAM,MAAM;gBACzC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,WAAW,CAAC,SAAS,GAAG;gBAC5B,IAAI,WAAW,CAAC,SAAS,GAAG;gBAC5B,IAAI,WAAW,CAAC,iBAAiB,GAAG;gBACpC,IAAI,WAAW,CAAC,gBAAgB,GAAG;gBACnC,IAAI,WAAW,CAAC,gBAAgB,GAAG;gBACnC;YAEF,KAAK;gBACH,IAAI,WAAW,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACvC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,UAAU,CAAC,qBAAqB,GAAG;gBACvC,IAAI,UAAU,CAAC,qBAAqB,GAAG;gBACvC,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,MAAM,MAAM;gBAElD,gCAAgC;gBAChC,MAAM,gBAAgB,IAAI,CAAC,mBAAmB;gBAC9C,IAAI,aAAa,GAAG;gBACpB,IAAI,WAAW,GAAG;gBAClB;YAEF;gBACE,IAAI,WAAW,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACvC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB;QACJ;QAEA,OAAO;IACT;IAEA,2BAAwC;QACtC,0CAA0C;QAC1C,MAAM,aAAa,2KAAA,CAAA,cAAW,CAAC,gBAAgB,CAC7C;YACE,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;SACvB,EACD,IAAI,CAAC,KAAK;QAGZ,OAAO;IACT;IAEA,iBAAiB,IAAY,EAAU;QACrC,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,sCAAsC;QACtC,MAAM,WAAW,IAAI,oBAAoB,CAAC,KAAK,KAAK,GAAG,KAAK,KAAK;QACjE,SAAS,YAAY,CAAC,GAAG;QACzB,SAAS,YAAY,CAAC,KAAK;QAC3B,SAAS,YAAY,CAAC,KAAK;QAC3B,SAAS,YAAY,CAAC,GAAG;QAEzB,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,KAAK;QAExB,+BAA+B;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,MAAM,IAAI,KAAK,MAAM,KAAK;YAC1B,MAAM,IAAI,KAAK,MAAM,KAAK;YAC1B,MAAM,aAAa,KAAK,MAAM;YAC9B,MAAM,OAAO,KAAK,MAAM,KAAK,IAAI;YAEjC,IAAI,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,aAAa,IAAI,EAAE,EAC/C,MAAM,aAAa,IACpB,OAAO,EAAE,WAAW,CAAC,CAAC;YACvB,IAAI,QAAQ,CAAC,GAAG,GAAG,MAAM;QAC3B;QAEA,OAAO,OAAO,SAAS;IACzB;IAEA,sBAAsC;QACpC,MAAM,UAAU,IAAI,8KAAA,CAAA,iBAAc,CAChC,iBACA;YAAE,OAAO;YAAK,QAAQ;QAAI,GAC1B,IAAI,CAAC,KAAK;QAEZ,MAAM,MAAM,QAAQ,UAAU;QAE9B,wBAAwB;QACxB,MAAM,YAAY,IAAI,UAAU,KAAK;QACrC,MAAM,OAAO,UAAU,IAAI;QAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gBAC5B,MAAM,QAAQ,CAAC,IAAI,MAAM,CAAC,IAAI;gBAE9B,mCAAmC;gBACnC,MAAM,SAAS,KAAK,GAAG,CAAC,IAAI,OAAO,IAAI,QAAQ,MAAM;gBACrD,MAAM,SAAS,KAAK,GAAG,CAAC,IAAI,OAAO,IAAI,SAAS,MAAM;gBACtD,MAAM,SAAS,KAAK,GAAG,CAAC,IAAI,QAAQ,IAAI,QAAQ,MAAM;gBAEtD,MAAM,SAAS,SAAS,SAAS;gBACjC,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,SAAS;gBAExC,IAAI,CAAC,MAAM,GAAG,OAAO,IAAI;gBACzB,IAAI,CAAC,QAAQ,EAAE,GAAG,OAAO,IAAI;gBAC7B,IAAI,CAAC,QAAQ,EAAE,GAAG,KAAK,KAAK,CAAC,QAAQ,OAAO,IAAI;gBAChD,IAAI,CAAC,QAAQ,EAAE,GAAG,KAAK,IAAI;YAC7B;QACF;QAEA,IAAI,YAAY,CAAC,WAAW,GAAG;QAC/B,QAAQ,MAAM;QAEd,OAAO;IACT;IAEA,8BAA8B;QAC5B,IAAI;YACF,qCAAqC;YACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,+MAAA,CAAA,2BAAwB,CACnD,oBACA,MACA,IAAI,CAAC,KAAK,EACV;gBAAC,IAAI,CAAC,MAAM;aAAC;YAGf,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,sCAAsC;gBACtC,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG;gBACtC,IAAI,CAAC,iBAAiB,CAAC,cAAc,GAAG;gBACxC,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG;gBACrC,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG;gBACrC,IAAI,CAAC,iBAAiB,CAAC,UAAU,GAAG;gBAEpC,8BAA8B;gBAC9B,IAAI,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE;oBAC1C,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,kBAAkB,GAAG;oBAC5D,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,eAAe,GAAG,GAAG,oBAAoB;gBAClF;gBAEA,4BAA4B;gBAC5B,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG;gBAErC,iCAAiC;gBACjC,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,GAAG;gBACpD,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,gBAAgB,GAAG;gBAC9D,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,eAAe,GAAG;gBAE7D,yBAAyB;gBACzB,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG;gBACtC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,GAAG;gBACzC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAExC,qBAAqB;gBACrB,IAAI,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE;oBAC1C,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,eAAe,GAAG;oBACzD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,eAAe,GAAG;oBACzD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,cAAc,GAAG;oBACxD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAa,GAAG,IAAI,6JAAA,CAAA,SAAM,CAC/D,GACA,GACA,GACA;gBAEJ;gBAEA,wBAAwB;gBACxB,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,GAAG;gBAC7C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,GAAG;gBAC/C,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,aAAa,GAAG;gBACpD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,WAAW,GAAG;gBAClD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,GAAG;YAE5C,qEAAqE;YACrE,8DAA8D;YAC9D,iEAAiE;YACjE,4EAA4E;YAC5E,qGAAqG;YACrG,gFAAgF;YAChF,IAAI;YACN;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,0BAA0B;QACxB,IAAI;YACF,0DAA0D;YAC1D,MAAM,iBAAiB,IAAI,qKAAA,CAAA,oBAAiB,CAC1C,qBACA;gBAAE,UAAU;YAAM,GAClB,IAAI,CAAC,KAAK;YAGZ,6BAA6B;YAC7B,eAAe,OAAO,GAAG,8JAAA,CAAA,UAAO,CAAC,IAAI;YACrC,eAAe,UAAU,GAAG,IAAI,8JAAA,CAAA,UAAO,CAAC,CAAC,IAAI,GAAG,CAAC;YACjD,eAAe,UAAU,GAAG,IAAI,8JAAA,CAAA,UAAO,CAAC,IAAI,GAAG;YAE/C,sBAAsB;YACtB,eAAe,eAAe,GAAG,IAAI,CAAC,qBAAqB;YAC3D,eAAe,QAAQ,GAAG;YAC1B,eAAe,WAAW,GAAG;YAC7B,eAAe,WAAW,GAAG;YAC7B,eAAe,OAAO,GAAG;YACzB,eAAe,OAAO,GAAG;YACzB,eAAe,kBAAkB,GAAG;YACpC,eAAe,kBAAkB,GAAG,KAAK,EAAE,GAAG;YAC9C,eAAe,eAAe,GAAG,CAAC,KAAK,EAAE;YACzC,eAAe,eAAe,GAAG,KAAK,EAAE;YAExC,oBAAoB;YACpB,eAAe,UAAU,GAAG,IAAI,8JAAA,CAAA,UAAO,CAAC,CAAC,GAAG,GAAG,CAAC;YAChD,eAAe,UAAU,GAAG,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,GAAG;YAC9C,eAAe,YAAY,GAAG;YAC9B,eAAe,YAAY,GAAG;YAC9B,eAAe,WAAW,GAAG;YAE7B,qBAAqB;YACrB,eAAe,OAAO,GAAG,IAAI,8JAAA,CAAA,UAAO,CAAC,GAAG,CAAC,GAAG;YAE5C,kBAAkB;YAClB,eAAe,MAAM,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;YAC5C,eAAe,MAAM,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;YAC5C,eAAe,SAAS,GAAG,IAAI,6JAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;YAE/C,gBAAgB;YAChB,eAAe,SAAS,GAAG,kKAAA,CAAA,iBAAc,CAAC,gBAAgB;YAE1D,4BAA4B;YAC5B,eAAe,KAAK;YACpB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAE1B,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,wBAAiC;QAC/B,MAAM,UAAU,IAAI,8KAAA,CAAA,iBAAc,CAChC,mBACA;YAAE,OAAO;YAAI,QAAQ;QAAG,GACxB,IAAI,CAAC,KAAK;QAEZ,MAAM,MAAM,QAAQ,UAAU;QAE9B,kCAAkC;QAClC,MAAM,WAAW,IAAI,oBAAoB,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI;QAC7D,SAAS,YAAY,CAAC,GAAG;QACzB,SAAS,YAAY,CAAC,KAAK;QAC3B,SAAS,YAAY,CAAC,KAAK;QAC3B,SAAS,YAAY,CAAC,GAAG;QAEzB,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,IAAI;QACvB,QAAQ,MAAM;QAEd,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,IAAI,QAAQ,WAAW;gBACrB,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC;oBACrE,aAAa,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,MACxC,IAAI,IAAI,CAAC,QAAQ,CAAC;gBAEtB;gBAEA,IAAI,IAAI,CAAC,eAAe,EAAE;oBACxB,uBAAuB;oBACvB,MAAM,eACJ,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAC/D,iKAAA,CAAA,mBAAgB,CAAC,aAAa,EAC9B;oBAGJ,yBAAyB;oBACzB,MAAM,UACJ,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,CAC/D,iKAAA,CAAA,mBAAgB,CAAC,QAAQ,EACzB;oBAGJ,QAAQ,GAAG,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,4BAA4B;YAC5B,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YAEvE,oCAAoC;YACpC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,YAAY,IAC1C,AAAC,OAAe,kBAAkB;YACpC,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC;YAEzD,IAAI,CAAC,aAAa,GAAG,IAAI,wJAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,KAAK;YAE5C,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;QAC7D;IACF;IAEA,kBAAkB;QAChB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;YACxB,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,gCAAgC;gBAChC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;oBAC1D,IAAI,CAAC,0BAA0B;gBACjC;gBAEA,mBAAmB;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM;YACnB;QACF;IACF;IAEA,6BAA6B;QAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;QAEzB,IAAI;YACF,MAAM,gBAAgB,IAAI,CAAC,aAAa,CAAC,oBAAoB;YAC7D,MAAM,mBACJ,cAAc,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,cAAc,MAAM;YAC9D,MAAM,sBAAsB,mBAAmB;YAE/C,+CAA+C;YAC/C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBAC5B,IAAI,kBAAkB,qKAAA,CAAA,oBAAiB,EAAE;oBACvC,OAAO,QAAQ,GAAG,MAAM,sBAAsB;gBAChD;YACF;YAEA,gDAAgD;YAChD,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,MAAM,sBAAsB;gBACjE,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,gBAAgB,GACzD,KAAK,sBAAsB;YAC/B;YAEA,0CAA0C;YAC1C,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,KAAK;gBACjC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW;oBAChC,MAAM,kBAAkB,IAAI,sBAAsB;oBAClD,IAAI,OAAO,GAAG,IAAI,8JAAA,CAAA,UAAO,CACvB,iBACA,iBACA;gBAEJ;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,6BAA6B;QAC3B,YAAY;YACV,IAAI,CAAC,gBAAgB,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;YACzD,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,MAAM;YACrE,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB;QAC/D,GAAG;IACL;IAEA,sBAAsB;QACpB,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IAEA,MAAM,cAAc;QAClB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI;gBACF,IAAI,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,KAAK,GAAG;oBACnD,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,YAAY,CACpD,gBACA;gBAEJ,OAAO;oBACL,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,WAAW;gBACvD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC;QACF;IACF;IAEA,sBAAsB;QACpB,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB;QACpE,QAAQ,GAAG,CACT,wBACA,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,YAAY;IAEnD;IAEA,UAAU;QACR,2BAA2B;QAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAW,OAAO,OAAO;QACvD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,QAAU,MAAM,OAAO;QACrD,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,MAAQ,IAAI,OAAO;QAEjD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,CAAC,OAAO;QAChC;QAEA,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,eAAe,CAAC,OAAO;QAC9B;QAEA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC,KAAK;QACzB;QAEA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,OAAO;QACpB;QAEA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,OAAO;QACrB;QAEA,QAAQ,GAAG,CAAC;IACd;AACF", "debugId": null}}]}