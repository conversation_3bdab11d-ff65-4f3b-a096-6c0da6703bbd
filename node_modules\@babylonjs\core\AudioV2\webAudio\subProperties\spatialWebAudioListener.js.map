{"version": 3, "file": "spatialWebAudioListener.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/webAudio/subProperties/spatialWebAudioListener.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AACzE,OAAO,EAAE,qBAAqB,EAAE,MAAM,wDAAwD,CAAC;AAC/F,OAAO,EAAE,gCAAgC,EAAE,MAAM,+CAA+C,CAAC;AACjG,OAAO,EAAE,2BAA2B,EAAE,MAAM,0CAA0C,CAAC;AAGvF,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAChC,MAAM,aAAa,GAAG,IAAI,UAAU,EAAE,CAAC;AACvC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;AAClC,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;AAElC,gBAAgB;AAChB,MAAM,UAAU,2BAA2B,CAAC,MAAuB,EAAE,UAAmB,EAAE,aAAqB;IAC3G,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IACI,QAAQ,CAAC,QAAQ;QACjB,QAAQ,CAAC,QAAQ;QACjB,QAAQ,CAAC,QAAQ;QACjB,QAAQ,CAAC,SAAS;QAClB,QAAQ,CAAC,SAAS;QAClB,QAAQ,CAAC,SAAS;QAClB,QAAQ,CAAC,GAAG;QACZ,QAAQ,CAAC,GAAG;QACZ,QAAQ,CAAC,GAAG,EACd,CAAC;QACC,OAAO,IAAI,wBAAwB,CAAC,MAAM,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IAC3E,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,gCAAgC,CAAC,MAAM,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IACnF,CAAC;AACL,CAAC;AAED,MAAe,gCAAiC,SAAQ,qBAAqB;IAkBzE,gBAAgB;IAChB,YAAmB,MAAuB,EAAE,UAAmB,EAAE,aAAqB;QAClF,KAAK,EAAE,CAAC;QAjBF,kBAAa,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACxC,kBAAa,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACxC,4BAAuB,GAAe,IAAI,UAAU,EAAE,CAAC;QAMjE,gBAAgB;QACA,aAAQ,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACnD,gBAAgB;QACA,aAAQ,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACnD,gBAAgB;QACA,uBAAkB,GAAe,IAAI,UAAU,EAAE,CAAC;QAM9D,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,iBAAiB,GAAG,IAAI,gCAAgC,CAAC,IAAI,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;IACnG,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,IAAK,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;IAChD,CAAC;IAED,gBAAgB;IAChB,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,CAAC,iBAAiB,CAAC,aAAa,GAAG,KAAK,CAAC;IACjD,CAAC;IAED,gBAAgB;IACT,MAAM;QACT,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,kBAAkB,EAAE,MAAM,EAAE,CAAC;QACtC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC;IACL,CAAC;IAEM,eAAe;QAClB,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEzC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAEM,eAAe;QAClB,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC3E,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnE,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9D,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;YAClG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACJ,OAAO;QACX,CAAC;QAED,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAErD,wCAAwC;QACxC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,0BAA0B,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QACxF,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAElE,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACzD,CAAC;CAIJ;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,wBAAyB,SAAQ,gCAAgC;IAWnE,YAAmB,MAAuB,EAAE,UAAmB,EAAE,aAAqB;QAClF,KAAK,CAAC,MAAM,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;QAEzC,MAAM,QAAQ,GAAG,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,UAAU,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC9E,IAAI,CAAC,UAAU,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC9E,IAAI,CAAC,UAAU,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC9E,IAAI,CAAC,IAAI,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;IACtE,CAAC;IAEkB,oBAAoB,CAAC,QAAiB;QACrD,6GAA6G;QAC7G,wFAAwF;QACxF,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3G,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC;IAC7C,CAAC;IAEkB,uBAAuB,CAAC,OAAgB,EAAE,EAAW;QACpE,6GAA6G;QAC7G,wFAAwF;QACxF,IACI,IAAI,CAAC,UAAU;YACf,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EACzJ,CAAC;YACC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC;QAEvC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;CACJ;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,gCAAiC,SAAQ,gCAAgC;IACxD,oBAAoB,CAAC,QAAiB;QACrD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC;IAEkB,uBAAuB,CAAC,OAAgB,EAAE,EAAW;QACpE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACrF,CAAC;CACJ", "sourcesContent": ["import { Matrix, Quaternion, Vector3 } from \"../../../Maths/math.vector\";\nimport { _SpatialAudioListener } from \"../../abstractAudio/subProperties/spatialAudioListener\";\nimport { _SpatialWebAudioUpdaterComponent } from \"../components/spatialWebAudioUpdaterComponent\";\nimport { _WebAudioParameterComponent } from \"../components/webAudioParameterComponent\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\n\nconst TmpMatrix = Matrix.Zero();\nconst TmpQuaternion = new Quaternion();\nconst TmpVector1 = Vector3.Zero();\nconst TmpVector2 = Vector3.Zero();\n\n/** @internal */\nexport function _CreateSpatialAudioListener(engine: _WebAudioEngine, autoUpdate: boolean, minUpdateTime: number): _SpatialAudioListener {\n    const listener = engine._audioContext.listener;\n    if (\n        listener.forwardX &&\n        listener.forwardY &&\n        listener.forwardZ &&\n        listener.positionX &&\n        listener.positionY &&\n        listener.positionZ &&\n        listener.upX &&\n        listener.upY &&\n        listener.upZ\n    ) {\n        return new _SpatialWebAudioListener(engine, autoUpdate, minUpdateTime);\n    } else {\n        return new _SpatialWebAudioListenerFallback(engine, autoUpdate, minUpdateTime);\n    }\n}\n\nabstract class _AbstractSpatialWebAudioListener extends _SpatialAudioListener {\n    protected readonly _listener: AudioListener;\n\n    protected _lastPosition: Vector3 = Vector3.Zero();\n    protected _lastRotation: Vector3 = Vector3.Zero();\n    protected _lastRotationQuaternion: Quaternion = new Quaternion();\n    protected _updaterComponent: _SpatialWebAudioUpdaterComponent;\n\n    /** @internal */\n    public readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public readonly position: Vector3 = Vector3.Zero();\n    /** @internal */\n    public readonly rotation: Vector3 = Vector3.Zero();\n    /** @internal */\n    public readonly rotationQuaternion: Quaternion = new Quaternion();\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine, autoUpdate: boolean, minUpdateTime: number) {\n        super();\n\n        this._listener = engine._audioContext.listener;\n        this.engine = engine;\n\n        this._updaterComponent = new _SpatialWebAudioUpdaterComponent(this, autoUpdate, minUpdateTime);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._updaterComponent.dispose();\n        this._updaterComponent = null!;\n    }\n\n    /** @internal */\n    public get minUpdateTime(): number {\n        return this._updaterComponent.minUpdateTime;\n    }\n\n    /** @internal */\n    public set minUpdateTime(value: number) {\n        this._updaterComponent.minUpdateTime = value;\n    }\n\n    /** @internal */\n    public update(): void {\n        if (this.isAttached) {\n            this._attacherComponent?.update();\n        } else {\n            this._updatePosition();\n            this._updateRotation();\n        }\n    }\n\n    public _updatePosition(): void {\n        if (this._lastPosition.equalsWithEpsilon(this.position)) {\n            return;\n        }\n\n        this._setWebAudioPosition(this.position);\n\n        this._lastPosition.copyFrom(this.position);\n    }\n\n    public _updateRotation(): void {\n        if (!this._lastRotationQuaternion.equalsWithEpsilon(this.rotationQuaternion)) {\n            TmpQuaternion.copyFrom(this.rotationQuaternion);\n            this._lastRotationQuaternion.copyFrom(this.rotationQuaternion);\n        } else if (!this._lastRotation.equalsWithEpsilon(this.rotation)) {\n            Quaternion.FromEulerAnglesToRef(this.rotation.x, this.rotation.y, this.rotation.z, TmpQuaternion);\n            this._lastRotation.copyFrom(this.rotation);\n        } else {\n            return;\n        }\n\n        Matrix.FromQuaternionToRef(TmpQuaternion, TmpMatrix);\n\n        // NB: The WebAudio API is right-handed.\n        Vector3.TransformNormalToRef(Vector3.RightHandedForwardReadOnly, TmpMatrix, TmpVector1);\n        Vector3.TransformNormalToRef(Vector3.Up(), TmpMatrix, TmpVector2);\n\n        this._setWebAudioOrientation(TmpVector1, TmpVector2);\n    }\n\n    protected abstract _setWebAudioPosition(position: Vector3): void;\n    protected abstract _setWebAudioOrientation(forward: Vector3, up: Vector3): void;\n}\n\n/**\n * Full-featured spatial audio listener for the Web Audio API.\n *\n * Used in browsers that support the `forwardX/Y/Z`, `positionX/Y/Z`, and `upX/Y/Z` properties on the AudioContext listener.\n *\n * NB: Firefox falls back to using this implementation.\n *\n * @see _SpatialWebAudioListenerFallback for the implementation used if only `setPosition` and `setOrientation` are available.\n *\n * NB: This sub property is not backed by a sub node and all properties are set directly on the audio context listener.\n *\n * @internal\n */\nclass _SpatialWebAudioListener extends _AbstractSpatialWebAudioListener {\n    private _forwardX: _WebAudioParameterComponent;\n    private _forwardY: _WebAudioParameterComponent;\n    private _forwardZ: _WebAudioParameterComponent;\n    private _positionX: _WebAudioParameterComponent;\n    private _positionY: _WebAudioParameterComponent;\n    private _positionZ: _WebAudioParameterComponent;\n    private _upX: _WebAudioParameterComponent;\n    private _upY: _WebAudioParameterComponent;\n    private _upZ: _WebAudioParameterComponent;\n\n    public constructor(engine: _WebAudioEngine, autoUpdate: boolean, minUpdateTime: number) {\n        super(engine, autoUpdate, minUpdateTime);\n\n        const listener = engine._audioContext.listener;\n        this._forwardX = new _WebAudioParameterComponent(engine, listener.forwardX);\n        this._forwardY = new _WebAudioParameterComponent(engine, listener.forwardY);\n        this._forwardZ = new _WebAudioParameterComponent(engine, listener.forwardZ);\n        this._positionX = new _WebAudioParameterComponent(engine, listener.positionX);\n        this._positionY = new _WebAudioParameterComponent(engine, listener.positionY);\n        this._positionZ = new _WebAudioParameterComponent(engine, listener.positionZ);\n        this._upX = new _WebAudioParameterComponent(engine, listener.upX);\n        this._upY = new _WebAudioParameterComponent(engine, listener.upY);\n        this._upZ = new _WebAudioParameterComponent(engine, listener.upZ);\n    }\n\n    protected override _setWebAudioPosition(position: Vector3): void {\n        // If attached and there is a ramp in progress, we assume another update is coming soon that we can wait for.\n        // We don't do this for unattached nodes because there may not be another update coming.\n        if (this.isAttached && (this._positionX.isRamping || this._positionY.isRamping || this._positionZ.isRamping)) {\n            return;\n        }\n\n        this._positionX.targetValue = position.x;\n        this._positionY.targetValue = position.y;\n        this._positionZ.targetValue = position.z;\n    }\n\n    protected override _setWebAudioOrientation(forward: Vector3, up: Vector3): void {\n        // If attached and there is a ramp in progress, we assume another update is coming soon that we can wait for.\n        // We don't do this for unattached nodes because there may not be another update coming.\n        if (\n            this.isAttached &&\n            (this._forwardX.isRamping || this._forwardY.isRamping || this._forwardZ.isRamping || this._upX.isRamping || this._upY.isRamping || this._upZ.isRamping)\n        ) {\n            return;\n        }\n\n        this._forwardX.targetValue = forward.x;\n        this._forwardY.targetValue = forward.y;\n        this._forwardZ.targetValue = forward.z;\n\n        this._upX.targetValue = up.x;\n        this._upY.targetValue = up.y;\n        this._upZ.targetValue = up.z;\n    }\n}\n\n/**\n * Fallback spatial audio listener for the Web Audio API.\n *\n * Used in browsers that do not support the `forwardX/Y/Z`, `positionX/Y/Z`, and `upX/Y/Z` properties on the\n * AudioContext listener.\n *\n * @see _SpatialWebAudioListener for the implementation used if the `forwardX/Y/Z`, `positionX/Y/Z`, and `upX/Y/Z`\n * properties are available.\n *\n * NB: This sub property is not backed by a sub node and all properties are set directly on the audio context listener.\n *\n * @internal\n */\nclass _SpatialWebAudioListenerFallback extends _AbstractSpatialWebAudioListener {\n    protected override _setWebAudioPosition(position: Vector3): void {\n        this._listener.setPosition(position.x, position.y, position.z);\n    }\n\n    protected override _setWebAudioOrientation(forward: Vector3, up: Vector3): void {\n        this._listener.setOrientation(forward.x, forward.y, forward.z, up.x, up.y, up.z);\n    }\n}\n"]}