{"version": 3, "file": "physicsViewer.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Debug/physicsViewer.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AACtC,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAChE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/E,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAErD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAGjE,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAC;AAChE,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,cAAc,EAAE,MAAM,oCAAoC,CAAC;AAEpE,OAAO,EAAE,aAAa,EAAE,MAAM,mCAAmC,CAAC;AAClE,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AAEpD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAElD;;GAEG;AACH,MAAM,OAAO,aAAa;IA8CtB;;;;;OAKG;IACH,YAAY,KAAa,EAAE,IAAa,EAAE,eAAqC,oBAAoB,CAAC,mBAAmB;QAnDvH,gBAAgB;QACN,eAAU,GAAqC,EAAE,CAAC;QAC5D,gBAAgB;QACN,YAAO,GAAkC,EAAE,CAAC;QACtD,gBAAgB;QACN,YAAO,GAAiC,EAAE,CAAC;QACrD,gBAAgB;QACN,mBAAc,GAAiC,EAAE,CAAC;QAC5D,gBAAgB;QACN,iBAAY,GAAuC,EAAE,CAAC;QAChE,gBAAgB;QACN,gBAAW,GAAkC,EAAE,CAAC;QAC1D,gBAAgB;QACN,mBAAc,GAAkC,EAAE,CAAC;QAC7D,gBAAgB;QACN,sBAAiB,GAAyC,EAAE,CAAC;QAGvE,gBAAgB;QACN,eAAU,GAAG,CAAC,CAAC;QACzB,gBAAgB;QACN,eAAU,GAAG,CAAC,CAAC;QACzB,gBAAgB;QACN,sBAAiB,GAAG,CAAC,CAAC;QAChC,gBAAgB;QACN,oBAAe,GAAG,CAAC,CAAC;QAOtB,qBAAgB,GAAG,KAAK,CAAC;QAQzB,qBAAgB,GAAG,IAAI,KAAK,EAAQ,CAAC;QAErC,wBAAmB,GAAG,GAAG,CAAC;QAC1B,2BAAsB,GAAG,GAAG,CAAC;QASjC,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAEpD,IAAI,YAAY,EAAE,CAAC;YACf,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAC,gBAAgB,EAAE,CAAC;QAChE,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QACtC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAClE,IAAI,CAAC,aAAa,CAAC,qBAAqB,GAAG,KAAK,CAAC;YACjD,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,wBAAwB,GAAG,IAAI,CAAC;YACrE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,mBAAmB,GAAG,GAAG,GAAG,IAAI,CAAC;YACtC,IAAI,CAAC,sBAAsB,GAAG,GAAG,GAAG,IAAI,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACO,kBAAkB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAEzC,IAAI,MAAM,EAAE,gBAAgB,EAAE,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,oBAA8C,CAAC;QACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEpC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACZ,SAAS;YACb,CAAC;YAED,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACJ,IAAI,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC,YAAY,EAAE,CAAC;oBACjD,SAAS;gBACb,CAAC;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE7B,IAAI,IAAI,IAAI,MAAM,EAAE,CAAC;oBACjB,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAChD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACO,oBAAoB;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,oBAA8C,CAAC;QACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,GAAI,CAAC;YACpC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjD,SAAS;YACb,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,IAAI,IAAI,SAAS,EAAE,CAAC;gBACpB,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC1C,CAAC;YACD,CAAC,EAAE,CAAC;QACR,CAAC;IACL,CAAC;IAES,oBAAoB;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAI,CAAC;YAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpD,SAAS;YACb,CAAC;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACf,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACzC,CAAC;YACD,CAAC,EAAE,CAAC;QACR,CAAC;IACL,CAAC;IAES,mBAAmB,CAAC,IAAiB,EAAE,WAAyB;QACtE,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC3C,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC7C,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,aAAa,GAAG,WAAmB,CAAC;YAC1C,MAAM,qBAAqB,GAAG,aAAa,CAAC,wBAAwB,CAAC,UAAW,CAAC;YACjF,MAAM,uBAAuB,GAAI,IAAI,CAAC,aAAsB,CAAC,wBAAwB,CAAC,UAAW,CAAC;YAClG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACxD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;gBAC9D,MAAM,CAAC,cAAc,CAAC,uBAAuB,EAAE,CAAC,GAAG,EAAE,EAAE,kBAAkB,CAAC,CAAC;gBAC3E,gBAAgB,CAAC,aAAa,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;gBACnE,cAAc,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YAC9D,CAAC;YACD,aAAa,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC;aAAM,CAAC;YACJ,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvC,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAC9D,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;YAC5E,kBAAkB,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC/D,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;gBAC5B,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBAC3E,kBAAkB,CAAC,aAAa,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;YAC1E,CAAC;YACD,gBAAgB,CAAC,aAAa,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;YACrE,gBAAgB,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QAC3D,CAAC;IACL,CAAC;IAES,uBAAuB;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACxC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YACvC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACrB,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG;IACO,uBAAuB,CAAC,OAAgB;QAC9C,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC;YACpC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC;YACpC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,CAAC;YACpC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAES,sBAAsB,CAAC,UAA6B,EAAE,aAA2B;QACvF,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,gCAAgC;QAChC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC;QAEvF,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,CAAC;YACrE,OAAO;QACX,CAAC;QAED,MAAM,WAAW,GAAG,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACvD,KAAK,MAAM,oBAAoB,IAAI,WAAW,EAAE,CAAC;YAC7C,2BAA2B;YAC3B,MAAM,qBAAqB,GAAG,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAkB,CAAC;YAC5F,MAAM,oBAAoB,GAAG,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAkB,CAAC;YAE3F,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,qBAAqB,CAAC,QAAQ,CAAC;YACvE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,oBAAoB,CAAC,QAAQ,CAAC;YAEpE,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;YAC3G,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YAExG,eAAe,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,CAAC;YAChE,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;YAE5D,cAAc,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;YAC9D,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE3D,6CAA6C;YAC7C,MAAM,mBAAmB,GAAG,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAkB,CAAC;YAC3F,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE9C,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAkB,CAAC;YACzF,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAE7C,iEAAiE;YACjE,UAAU,CAAC,uBAAuB,CAC9B,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAC5H,mBAAmB,CAAC,kBAAmB,CAC1C,CAAC;YACF,UAAU,CAAC,uBAAuB,CAC9B,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAC5H,kBAAkB,CAAC,kBAAmB,CACzC,CAAC;QACN,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,YAAY,CAAC,QAAyB,EAAE,UAAiB;QAC5D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAE3D,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;YAE1C,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,eAAe,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACvD,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;;OAQG;IACI,QAAQ,CAAC,IAAiB;QAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;YAE9C,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,eAAe,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACvD,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3D,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,IAAiB;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;YACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,SAAS,CAAC;YAExD,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,sBAAsB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAChE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,UAA6B;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,UAAU,CAAC;YACrD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,SAAS,CAAC;YAEzD,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,yBAAyB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBACtE,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACrE,CAAC;YAED,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3C,CAAC;IAED;;;;;;;;OAQG;IACI,YAAY,CAAC,QAAmC;QACnD,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACnD,OAAO;QACX,CAAC;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACjC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE7B,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,SAAS;gBACb,CAAC;gBAED,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEf,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAY,CAAC,CAAC;gBAC1D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;oBACb,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC3C,CAAC;gBAED,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;oBACtB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAChD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;oBACrC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBAC5C,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBACvB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC9B,CAAC;gBACD,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACV,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACI,QAAQ,CAAC,IAA2B;QACvC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;gBAEjC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,SAAS;gBACb,CAAC;gBAED,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEf,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;oBACtB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACxD,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;oBACzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBACzC,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBAC3B,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC3B,CAAC;gBACD,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACV,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC7D,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,IAA2B;QAC1C,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAC/C,OAAO,KAAK,CAAC;QACjB,CAAC;QACD,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAClC,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;gBAEpC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACR,SAAS;gBACb,CAAC;gBAED,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEf,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACjC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEjC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAEzB,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACV,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACpE,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,UAAuC;QACzD,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACrD,OAAO;QACX,CAAC;QACD,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE,CAAC;gBACtC,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBAEzC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,SAAS;gBACb,CAAC;gBAED,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;oBACxB,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBACnC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnB,CAAC;gBAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEpC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAEvB,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE,CAAC;oBAC3B,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC/D,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBACzE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;oBAC/C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;gBACxD,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;oBAC5B,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBACrC,CAAC;gBAED,OAAO,GAAG,IAAI,CAAC;gBACf,MAAM;YACV,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,KAAY;QAClC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACtD,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC;YACrC,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;YACnD,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG,IAAI,CAAC;QAC/C,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAEO,wBAAwB,CAAC,KAAY;QACzC,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC9B,IAAI,CAAC,qBAAqB,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,qBAAqB,CAAC,eAAe,GAAG,IAAI,CAAC;YAClD,IAAI,CAAC,qBAAqB,CAAC,KAAK,GAAG,GAAG,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAEO,4BAA4B,CAAC,UAAkB,EAAE,KAAY;QACjE,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACjD,QAAQ,CAAC,aAAa,GAAG,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAC3G,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;QAChC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,gBAAgB,CAAC,KAAY;QACjC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,wBAAwB,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAC7E,IAAI,CAAC,aAAa,CAAC,kBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC9D,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC5D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC;IAC3E,CAAC;IAEO,mBAAmB,CAAC,KAAY;QACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC,2BAA2B,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YAC1F,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;YACjE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;IACjF,CAAC;IAEO,oBAAoB,CAAC,KAAY;QACrC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,4BAA4B,EAAE,EAAE,MAAM,EAAE,CAAC,EAA2B,EAAE,KAAK,CAAC,CAAC;YACpH,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;YAClE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAChE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,gCAAgC,CAAC,CAAC;IACnF,CAAC;IAEO,qBAAqB,CAAC,KAAY;QACtC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC,6BAA6B,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;YACjI,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;YACnE,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,iCAAiC,CAAC,CAAC;IACrF,CAAC;IAEO,iBAAiB,CAAC,IAAU,EAAE,KAAY;QAC9C,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7D,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9B,aAAa,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACxC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAEvD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE1C,OAAO,aAAa,CAAC;IACzB,CAAC;IAEO,aAAa,CAAC,QAAyB,EAAE,UAAiB;QAC9D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,qEAAqE;QACrE,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,IAAK,UAAU,CAAC,MAAe,CAAC,eAAe,EAAE,CAAC;YACjF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,GAA2B,IAAI,CAAC;QACxC,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAC/D,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,gGAAgG,CAAC,CAAC;YAC9G,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpB,KAAK,eAAe,CAAC,WAAW;gBAC5B,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;gBAChD,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACvC,MAAM;YACV,KAAK,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC;gBAClC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;gBACnD,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;gBACpC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;gBAC5B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;gBAC5B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;gBAC5B,MAAM;YACV,CAAC;YACD,KAAK,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC;gBACnC,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;gBACpD,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;gBAC7C,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACvG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACvG,MAAM;YACV,CAAC;YACD,KAAK,eAAe,CAAC,YAAY;gBAC7B,IAAI,UAAU,EAAE,CAAC;oBACb,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;gBACjE,CAAC;gBACD,MAAM;YACV,KAAK,eAAe,CAAC,UAAU;gBAC3B,IAAI,UAAU,EAAE,CAAC;oBACb,4BAA4B;oBAC5B,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE;wBACzD,OAAO,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrC,CAAC,CAAC,CAAC;oBACH,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE,CAAC;wBAC1B,IAAI,CAAC,CAAC,eAAe,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,MAAM,EAAE,CAAC;4BACnD,MAAM,YAAY,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC;4BACzC,MAAM,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC;4BAC7C,MAAM,GAAG,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC;4BAC7C,QAAQ,CAAC,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;gCAC7B,KAAK,eAAe,CAAC,WAAW;oCAC5B,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;oCAChD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oCAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oCAC9B,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;oCAChC,MAAM;gCACV,KAAK,eAAe,CAAC,cAAc;oCAC/B,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;oCACnD,MAAM;gCACV,KAAK,eAAe,CAAC,gBAAgB;oCACjC,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;oCACrD,MAAM;gCACV;oCACI,IAAI,GAAG,IAAI,CAAC;oCACZ,MAAM;4BACd,CAAC;4BACD,IAAI,IAAI,EAAE,CAAC;gCACP,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gCAC/B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gCAC/B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gCAC/B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;4BACpB,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAC;gBAC/E,CAAC;gBACD,IAAI,GAAG,IAAI,CAAC;gBACZ,MAAM;YACV,KAAK,eAAe,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACpC,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;gBACrD,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;gBAC7C,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnG,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACnG,MAAM;YACV,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG;IACK,iBAAiB,CAAC,IAAiB;QACvC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAS,CAAC;QAC3C,UAAU,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;QAC1C,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QACtC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,MAAM,cAAc,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YAC/E,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,+BAA+B,CAAC,SAAgC,EAAE,MAAc;QACpF,MAAM,WAAW,GAAG,SAAS,CAAC,kBAAkB,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC1E,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACzD,MAAM,MAAM,GAAG,SAAS,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QAExD,MAAM,QAAQ,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACxE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;QAEnF,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;QAErF,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;QAErF,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAEhC,MAAM,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3F,MAAM,QAAQ,GAAG,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,MAAM,WAAW,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhG,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACxC,MAAM,CAAC,aAAa,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAE1C,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,oBAAoB,CAAC,IAAiB;QAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,qGAAqG;QACrG,MAAM,cAAc,GAAG,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;QACvF,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACpC,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC;YACnC,MAAM,cAAc,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YAC/E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBACxD,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBACvD,SAAS,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;YAClD,CAAC;YACD,cAAc,CAAC,qBAAqB,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACJ,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvC,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACvD,SAAS,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC;QACD,cAAc,CAAC,oBAAoB,EAAE,CAAC;QACtC,cAAc,CAAC,UAAU,GAAG,GAAG,CAAC;QAChC,cAAc,CAAC,UAAU,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnD,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,CAAC;QAE3E,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,0BAA0B,CAAC,IAAiB,EAAE,MAAc,EAAE,aAAsB;QACxF,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;QACjC,IAAI,aAAa,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;YACtC,OAAO,MAAM,CAAC,cAAc,CAAE,KAAc,CAAC,wBAAwB,CAAC,UAAW,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;QAC9G,CAAC;aAAM,CAAC;YACJ,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEO,4BAA4B,CAAC,QAAgB,EAAE,QAAgB,EAAE,UAAkB,EAAE,MAAqB,EAAE,KAAY;QAC5H,MAAM,QAAQ,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,GAAG,WAAW,CAAC,cAAc,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QACnJ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACrE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC;QAC7C,QAAQ,UAAU,EAAE,CAAC;YACjB,KAAK,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;gBAChC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;gBAC5C,iBAAiB;gBACjB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,GAAG,QAAQ,CAAC;gBAC3C,WAAW;gBACX,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,MAAM;YACV,KAAK,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC;gBAChC,WAAW;gBACX,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;gBACrC,MAAM;QACd,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,WAAW,CAAC,MAAqB,EAAE,KAAY;QACnD,MAAM,IAAI,GAAG,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,MAAM,mBAAmB,GAAG,IAAI,gBAAgB,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACzE,mBAAmB,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,oBAAoB;QACnD,IAAI,CAAC,QAAQ,GAAG,mBAAmB,CAAC;QAEpC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,uBAAuB,CAAC,UAA6B;QACzD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAE/D,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,gCAAgC;QAChC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC;QAEvF,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,CAAC;YACrE,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,6DAA6D;QAC7D,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,0BAA0B,EAAE,iBAAiB,CAAC,CAAC;QAE9E,6EAA6E;QAC7E,MAAM,qBAAqB,GAAG,UAAU,CAAC,wBAAwB,EAAE,CAAC;QAEpE,MAAM,wBAAwB,GAAG,EAAE,CAAC;QACpC,wBAAwB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7C,KAAK,MAAM,YAAY,IAAI,qBAAqB,EAAE,CAAC;YAC/C,oDAAoD;YACpD,MAAM,YAAY,GAAG,IAAI,aAAa,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAC1E,YAAY,CAAC,MAAM,GAAG,aAAa,CAAC;YAEpC,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,YAAY,CAAC;YAChF,2BAA2B;YAE3B,MAAM,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;YAC3G,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,CAAC,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YAExG,MAAM,qBAAqB,GAAG,IAAI,aAAa,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;YACxF,gDAAgD;YAChD,qBAAqB,CAAC,MAAM,GAAG,YAAY,CAAC;YAC5C,uEAAuE;YACvE,qBAAqB,CAAC,QAAQ,GAAG,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC;YACjE,eAAe,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,CAAC;YAEhE,MAAM,oBAAoB,GAAG,IAAI,aAAa,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YACtF,+CAA+C;YAC/C,oBAAoB,CAAC,MAAM,GAAG,YAAY,CAAC;YAC3C,sEAAsE;YACtE,oBAAoB,CAAC,QAAQ,GAAG,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC;YAC9D,cAAc,CAAC,wBAAwB,CAAC,oBAAoB,CAAC,CAAC;YAE9D,iEAAiE;YACjE,MAAM,kBAAkB,GAAG,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClJ,MAAM,iBAAiB,GAAG,UAAU,CAAC,kBAAkB,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEjJ,MAAM,wBAAwB,GAAG,MAAM,CAAC;YACxC,MAAM,uBAAuB,GAAG,MAAM,CAAC;YAEvC,6CAA6C;YAC7C,MAAM,mBAAmB,GAAG,IAAI,aAAa,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;YACtF,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;YAChE,mBAAmB,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;YAC5D,mBAAmB,CAAC,MAAM,GAAG,qBAAqB,CAAC;YAEnD,MAAM,kBAAkB,GAAG,IAAI,aAAa,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;YACpF,kBAAkB,CAAC,MAAM,GAAG,oBAAoB,CAAC;YACjD,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;YAC9D,kBAAkB,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;YAE1D,iCAAiC;YACjC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC/E,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;YAC9C,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;YAC9C,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;YAE9C,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC9E,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,CAAC;YAC5C,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,CAAC;YAC5C,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,kBAAkB,CAAC;YAE5C,gBAAgB;YAChB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAA+C,CAAC;YAEpE,MAAM,qBAAqB,GAAG,2HAAmG,CAAC;YAClI,MAAM,oBAAoB,GAAG,wHAAgG,CAAC;YAC9H,MAAM,cAAc,GAAG,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,CAAC;YAErE,iCAAiC;YACjC,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACzB,KAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,EAAE,aAAa,EAAE,EAAE,CAAC;gBAC7D,KAAK,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;oBAClC,MAAM,mBAAmB,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC;oBAChE,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;oBACrE,IAAI,QAAQ,iDAAyC,EAAE,CAAC;wBACpD,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;oBAC/B,CAAC;gBACL,CAAC;YACL,CAAC;YAED,+BAA+B;YAC/B,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;gBAEtE,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,OAAO,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBAEtC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,yCAAiC,kDAA0C,CAAC;gBACtH,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,yCAAiC,kDAA0C,CAAC;gBACtH,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,yCAAiC,kDAA0C,CAAC;gBAEtH,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,yCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,yCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,yCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,yCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,yCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7F,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,yCAAkC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE7F,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;gBAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;gBAC9B,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;gBAE9B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;gBACzC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;gBACzC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;gBACzC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;YAED,UAAU;YACV,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;gBACpB,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;oBACjD,MAAM,IAAI,GAAG,qBAAqB,CAAC,SAAS,CAAC,CAAC;oBAC9C,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;oBACtD,IAAI,QAAQ,GAAG,CAAC,CAAC;oBACjB,IAAI,QAAQ,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC3B,IAAI,QAAQ,kDAA0C,EAAE,CAAC;wBACrD,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAE,CAAC;wBACrD,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAE,CAAC;oBACzD,CAAC;oBACD,IAAI,QAAQ,iDAAyC,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjF,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;wBAC1H,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBAClD,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,YAAY;QACZ,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;YACxD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC;QACD,SAAS;QACT,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;YACxD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACD,UAAU;QACV,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;YAC/D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QACpC,CAAC;QACD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAEjC,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,CAAC;IACL,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport { Mesh } from \"../Meshes/mesh\";\r\nimport { CreateBox } from \"../Meshes/Builders/boxBuilder\";\r\nimport { CreateSphere } from \"../Meshes/Builders/sphereBuilder\";\r\nimport { Matrix, Quaternion, TmpVectors, Vector3 } from \"../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { StandardMaterial } from \"../Materials/standardMaterial\";\r\nimport type { IPhysicsEnginePlugin as IPhysicsEnginePluginV1 } from \"../Physics/v1/IPhysicsEnginePlugin\";\r\nimport { PhysicsConstraintAxis, PhysicsConstraintAxisLimitMode, type IPhysicsEnginePluginV2, type PhysicsMassProperties } from \"../Physics/v2/IPhysicsEnginePlugin\";\r\nimport { PhysicsImpostor } from \"../Physics/v1/physicsImpostor\";\r\nimport { UtilityLayerRenderer } from \"../Rendering/utilityLayerRenderer\";\r\nimport { CreateCylinder } from \"../Meshes/Builders/cylinderBuilder\";\r\nimport type { ICreateCapsuleOptions } from \"../Meshes/Builders/capsuleBuilder\";\r\nimport { CreateCapsule } from \"../Meshes/Builders/capsuleBuilder\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport type { PhysicsBody } from \"../Physics/v2/physicsBody\";\r\nimport { VertexData } from \"../Meshes/mesh.vertexData\";\r\nimport { MeshBuilder } from \"../Meshes/meshBuilder\";\r\nimport type { PhysicsConstraint } from \"../Physics/v2/physicsConstraint\";\r\nimport { AxesViewer } from \"./axesViewer\";\r\nimport { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Epsilon } from \"../Maths/math.constants\";\r\n\r\n/**\r\n * Used to show the physics impostor around the specific mesh\r\n */\r\nexport class PhysicsViewer {\r\n    /** @internal */\r\n    protected _impostors: Array<Nullable<PhysicsImpostor>> = [];\r\n    /** @internal */\r\n    protected _meshes: Array<Nullable<AbstractMesh>> = [];\r\n    /** @internal */\r\n    protected _bodies: Array<Nullable<PhysicsBody>> = [];\r\n    /** @internal */\r\n    protected _inertiaBodies: Array<Nullable<PhysicsBody>> = [];\r\n    /** @internal */\r\n    protected _constraints: Array<Nullable<PhysicsConstraint>> = [];\r\n    /** @internal */\r\n    protected _bodyMeshes: Array<Nullable<AbstractMesh>> = [];\r\n    /** @internal */\r\n    protected _inertiaMeshes: Array<Nullable<AbstractMesh>> = [];\r\n    /** @internal */\r\n    protected _constraintMeshes: Array<Nullable<Array<AbstractMesh>>> = [];\r\n    /** @internal */\r\n    protected _scene: Nullable<Scene>;\r\n    /** @internal */\r\n    protected _numMeshes = 0;\r\n    /** @internal */\r\n    protected _numBodies = 0;\r\n    /** @internal */\r\n    protected _numInertiaBodies = 0;\r\n    /** @internal */\r\n    protected _numConstraints = 0;\r\n    /** @internal */\r\n    protected _physicsEnginePlugin: IPhysicsEnginePluginV1 | IPhysicsEnginePluginV2 | null;\r\n    private _renderFunction: () => void;\r\n    private _inertiaRenderFunction: () => void;\r\n    private _constraintRenderFunction: () => void;\r\n    private _utilityLayer: Nullable<UtilityLayerRenderer>;\r\n    private _ownUtilityLayer = false;\r\n\r\n    private _debugBoxMesh: Mesh;\r\n    private _debugSphereMesh: Mesh;\r\n    private _debugCapsuleMesh: Mesh;\r\n    private _debugCylinderMesh: Mesh;\r\n    private _debugMaterial: StandardMaterial;\r\n    private _debugInertiaMaterial: StandardMaterial;\r\n    private _debugMeshMeshes = new Array<Mesh>();\r\n\r\n    private _constraintAxesSize = 0.4;\r\n    private _constraintAngularSize = 0.4;\r\n\r\n    /**\r\n     * Creates a new PhysicsViewer\r\n     * @param scene defines the hosting scene\r\n     * @param size Physics V2 size scalar\r\n     * @param utilityLayer The utility layer the viewer will be added to\r\n     */\r\n    constructor(scene?: Scene, size?: number, utilityLayer: UtilityLayerRenderer = UtilityLayerRenderer.DefaultUtilityLayer) {\r\n        this._scene = scene || EngineStore.LastCreatedScene;\r\n        if (!this._scene) {\r\n            return;\r\n        }\r\n        const physicEngine = this._scene.getPhysicsEngine();\r\n\r\n        if (physicEngine) {\r\n            this._physicsEnginePlugin = physicEngine.getPhysicsPlugin();\r\n        }\r\n\r\n        if (utilityLayer) {\r\n            this._utilityLayer = utilityLayer;\r\n        } else {\r\n            this._utilityLayer = new UtilityLayerRenderer(this._scene, false);\r\n            this._utilityLayer.pickUtilitySceneFirst = false;\r\n            this._utilityLayer.utilityLayerScene.autoClearDepthAndStencil = true;\r\n            this._ownUtilityLayer = true;\r\n        }\r\n        if (size) {\r\n            this._constraintAxesSize = 0.4 * size;\r\n            this._constraintAngularSize = 0.4 * size;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the debug meshes of the physics engine.\r\n     *\r\n     * This code is useful for synchronizing the debug meshes of the physics engine with the physics impostor and mesh.\r\n     * It checks if the impostor is disposed and if the plugin version is 1, then it syncs the mesh with the impostor.\r\n     * This ensures that the debug meshes are up to date with the physics engine.\r\n     */\r\n    protected _updateDebugMeshes(): void {\r\n        const plugin = this._physicsEnginePlugin;\r\n\r\n        if (plugin?.getPluginVersion() === 1) {\r\n            this._updateDebugMeshesV1();\r\n        } else {\r\n            this._updateDebugMeshesV2();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the debug meshes of the physics engine.\r\n     *\r\n     * This method is useful for synchronizing the debug meshes with the physics impostors.\r\n     * It iterates through the impostors and meshes, and if the plugin version is 1, it syncs the mesh with the impostor.\r\n     * This ensures that the debug meshes accurately reflect the physics impostors, which is important for debugging the physics engine.\r\n     */\r\n    protected _updateDebugMeshesV1(): void {\r\n        const plugin = this._physicsEnginePlugin as IPhysicsEnginePluginV1;\r\n        for (let i = 0; i < this._numMeshes; i++) {\r\n            const impostor = this._impostors[i];\r\n\r\n            if (!impostor) {\r\n                continue;\r\n            }\r\n\r\n            if (impostor.isDisposed) {\r\n                this.hideImpostor(this._impostors[i--]);\r\n            } else {\r\n                if (impostor.type === PhysicsImpostor.MeshImpostor) {\r\n                    continue;\r\n                }\r\n                const mesh = this._meshes[i];\r\n\r\n                if (mesh && plugin) {\r\n                    plugin.syncMeshWithImpostor(mesh, impostor);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the debug meshes of the physics engine for V2 plugin.\r\n     *\r\n     * This method is useful for synchronizing the debug meshes of the physics engine with the current state of the bodies.\r\n     * It iterates through the bodies array and updates the debug meshes with the current transform of each body.\r\n     * This ensures that the debug meshes accurately reflect the current state of the physics engine.\r\n     */\r\n    protected _updateDebugMeshesV2(): void {\r\n        const plugin = this._physicsEnginePlugin as IPhysicsEnginePluginV2;\r\n        for (let i = 0; i < this._numBodies; ) {\r\n            const body = this._bodies[i];\r\n            if (body && body.isDisposed && this.hideBody(body)) {\r\n                continue;\r\n            }\r\n            const transform = this._bodyMeshes[i];\r\n            if (body && transform) {\r\n                plugin.syncTransform(body, transform);\r\n            }\r\n            i++;\r\n        }\r\n    }\r\n\r\n    protected _updateInertiaMeshes(): void {\r\n        for (let i = 0; i < this._numInertiaBodies; ) {\r\n            const body = this._inertiaBodies[i];\r\n            if (body && body.isDisposed && this.hideInertia(body)) {\r\n                continue;\r\n            }\r\n            const mesh = this._inertiaMeshes[i];\r\n            if (body && mesh) {\r\n                this._updateDebugInertia(body, mesh);\r\n            }\r\n            i++;\r\n        }\r\n    }\r\n\r\n    protected _updateDebugInertia(body: PhysicsBody, inertiaMesh: AbstractMesh): void {\r\n        const inertiaMatrixRef = Matrix.Identity();\r\n        const transformMatrixRef = Matrix.Identity();\r\n        const finalMatrixRef = Matrix.Identity();\r\n        if (body._pluginDataInstances.length) {\r\n            const inertiaAsMesh = inertiaMesh as Mesh;\r\n            const inertiaMeshMatrixData = inertiaAsMesh._thinInstanceDataStorage.matrixData!;\r\n            const bodyTransformMatrixData = (body.transformNode as Mesh)._thinInstanceDataStorage.matrixData!;\r\n            for (let i = 0; i < body._pluginDataInstances.length; i++) {\r\n                const props = body.getMassProperties(i);\r\n                this._getMeshDebugInertiaMatrixToRef(props, inertiaMatrixRef);\r\n                Matrix.FromArrayToRef(bodyTransformMatrixData, i * 16, transformMatrixRef);\r\n                inertiaMatrixRef.multiplyToRef(transformMatrixRef, finalMatrixRef);\r\n                finalMatrixRef.copyToArray(inertiaMeshMatrixData, i * 16);\r\n            }\r\n            inertiaAsMesh.thinInstanceBufferUpdated(\"matrix\");\r\n        } else {\r\n            const props = body.getMassProperties();\r\n            this._getMeshDebugInertiaMatrixToRef(props, inertiaMatrixRef);\r\n            body.transformNode.rotationQuaternion?.toRotationMatrix(transformMatrixRef);\r\n            transformMatrixRef.setTranslation(body.transformNode.position);\r\n            if (body.transformNode.parent) {\r\n                const parentTransform = body.transformNode.parent.computeWorldMatrix(true);\r\n                transformMatrixRef.multiplyToRef(parentTransform, transformMatrixRef);\r\n            }\r\n            inertiaMatrixRef.multiplyToRef(transformMatrixRef, inertiaMatrixRef);\r\n            inertiaMatrixRef.decomposeToTransformNode(inertiaMesh);\r\n        }\r\n    }\r\n\r\n    protected _updateDebugConstraints() {\r\n        for (let i = 0; i < this._numConstraints; i++) {\r\n            const constraint = this._constraints[i];\r\n            const mesh = this._constraintMeshes[i];\r\n            if (constraint && mesh) {\r\n                this._updateDebugConstraint(constraint, mesh[0]);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Given a scaling vector, make all of its components\r\n     * 1, preserving the sign\r\n     * @param scaling\r\n     */\r\n    protected _makeScalingUnitInPlace(scaling: Vector3) {\r\n        if (Math.abs(scaling.x - 1) > Epsilon) {\r\n            scaling.x = 1 * Math.sign(scaling.x);\r\n        }\r\n        if (Math.abs(scaling.y - 1) > Epsilon) {\r\n            scaling.y = 1 * Math.sign(scaling.y);\r\n        }\r\n        if (Math.abs(scaling.z - 1) > Epsilon) {\r\n            scaling.z = 1 * Math.sign(scaling.z);\r\n        }\r\n    }\r\n\r\n    protected _updateDebugConstraint(constraint: PhysicsConstraint, parentingMesh: AbstractMesh) {\r\n        if (!constraint._initOptions) {\r\n            return;\r\n        }\r\n\r\n        // Get constraint pivot and axes\r\n        const { pivotA, pivotB, axisA, axisB, perpAxisA, perpAxisB } = constraint._initOptions;\r\n\r\n        if (!pivotA || !pivotB || !axisA || !axisB || !perpAxisA || !perpAxisB) {\r\n            return;\r\n        }\r\n\r\n        const descendants = parentingMesh.getDescendants(true);\r\n        for (const parentConstraintMesh of descendants) {\r\n            // Get the parent transform\r\n            const parentCoordSystemNode = parentConstraintMesh.getDescendants(true)[0] as TransformNode;\r\n            const childCoordSystemNode = parentConstraintMesh.getDescendants(true)[1] as TransformNode;\r\n\r\n            const { parentBody, parentBodyIndex } = parentCoordSystemNode.metadata;\r\n            const { childBody, childBodyIndex } = childCoordSystemNode.metadata;\r\n\r\n            const parentTransform = this._getTransformFromBodyToRef(parentBody, TmpVectors.Matrix[0], parentBodyIndex);\r\n            const childTransform = this._getTransformFromBodyToRef(childBody, TmpVectors.Matrix[1], childBodyIndex);\r\n\r\n            parentTransform.decomposeToTransformNode(parentCoordSystemNode);\r\n            this._makeScalingUnitInPlace(parentCoordSystemNode.scaling);\r\n\r\n            childTransform.decomposeToTransformNode(childCoordSystemNode);\r\n            this._makeScalingUnitInPlace(childCoordSystemNode.scaling);\r\n\r\n            // Create a transform node and set its matrix\r\n            const parentTransformNode = parentCoordSystemNode.getDescendants(true)[0] as TransformNode;\r\n            parentTransformNode.position.copyFrom(pivotA);\r\n\r\n            const childTransformNode = childCoordSystemNode.getDescendants(true)[0] as TransformNode;\r\n            childTransformNode.position.copyFrom(pivotB);\r\n\r\n            // Get the transform to align the XYZ axes to the constraint axes\r\n            Quaternion.FromRotationMatrixToRef(\r\n                Matrix.FromXYZAxesToRef(axisA, perpAxisA, Vector3.CrossToRef(axisA, perpAxisA, TmpVectors.Vector3[0]), TmpVectors.Matrix[0]),\r\n                parentTransformNode.rotationQuaternion!\r\n            );\r\n            Quaternion.FromRotationMatrixToRef(\r\n                Matrix.FromXYZAxesToRef(axisB, perpAxisB, Vector3.CrossToRef(axisB, perpAxisB, TmpVectors.Vector3[1]), TmpVectors.Matrix[1]),\r\n                childTransformNode.rotationQuaternion!\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Renders a specified physic impostor\r\n     * @param impostor defines the impostor to render\r\n     * @param targetMesh defines the mesh represented by the impostor\r\n     * @returns the new debug mesh used to render the impostor\r\n     */\r\n    public showImpostor(impostor: PhysicsImpostor, targetMesh?: Mesh): Nullable<AbstractMesh> {\r\n        if (!this._scene) {\r\n            return null;\r\n        }\r\n\r\n        for (let i = 0; i < this._numMeshes; i++) {\r\n            if (this._impostors[i] == impostor) {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        const debugMesh = this._getDebugMesh(impostor, targetMesh);\r\n\r\n        if (debugMesh) {\r\n            this._impostors[this._numMeshes] = impostor;\r\n            this._meshes[this._numMeshes] = debugMesh;\r\n\r\n            if (this._numMeshes === 0) {\r\n                this._renderFunction = () => this._updateDebugMeshes();\r\n                this._scene.registerBeforeRender(this._renderFunction);\r\n            }\r\n\r\n            this._numMeshes++;\r\n        }\r\n\r\n        return debugMesh;\r\n    }\r\n\r\n    /**\r\n     * Shows a debug mesh for a given physics body.\r\n     * @param body The physics body to show.\r\n     * @returns The debug mesh, or null if the body is already shown.\r\n     *\r\n     * This function is useful for visualizing the physics body in the scene.\r\n     * It creates a debug mesh for the given body and adds it to the scene.\r\n     * It also registers a before render function to update the debug mesh position and rotation.\r\n     */\r\n    public showBody(body: PhysicsBody): Nullable<AbstractMesh> {\r\n        if (!this._scene) {\r\n            return null;\r\n        }\r\n\r\n        for (let i = 0; i < this._numBodies; i++) {\r\n            if (this._bodies[i] == body) {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        const debugMesh = this._getDebugBodyMesh(body);\r\n\r\n        if (debugMesh) {\r\n            this._bodies[this._numBodies] = body;\r\n            this._bodyMeshes[this._numBodies] = debugMesh;\r\n\r\n            if (this._numBodies === 0) {\r\n                this._renderFunction = () => this._updateDebugMeshes();\r\n                this._scene.registerBeforeRender(this._renderFunction);\r\n            }\r\n\r\n            this._numBodies++;\r\n        }\r\n\r\n        return debugMesh;\r\n    }\r\n\r\n    /**\r\n     * Shows a debug box corresponding to the inertia of a given body\r\n     * @param body the physics body used to get the inertia\r\n     * @returns the debug mesh used to show the inertia, or null if the body is already shown\r\n     */\r\n    public showInertia(body: PhysicsBody): Nullable<AbstractMesh> {\r\n        if (!this._scene) {\r\n            return null;\r\n        }\r\n\r\n        for (let i = 0; i < this._numInertiaBodies; i++) {\r\n            if (this._inertiaBodies[i] == body) {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        const debugMesh = this._getDebugInertiaMesh(body);\r\n        if (debugMesh) {\r\n            this._inertiaBodies[this._numInertiaBodies] = body;\r\n            this._inertiaMeshes[this._numInertiaBodies] = debugMesh;\r\n\r\n            if (this._numInertiaBodies === 0) {\r\n                this._inertiaRenderFunction = () => this._updateInertiaMeshes();\r\n                this._scene.registerBeforeRender(this._inertiaRenderFunction);\r\n            }\r\n\r\n            this._numInertiaBodies++;\r\n        }\r\n\r\n        return debugMesh;\r\n    }\r\n\r\n    /**\r\n     * Shows a debug mesh for a given physics constraint.\r\n     * @param constraint the physics constraint to show\r\n     * @returns the debug mesh, or null if the constraint is already shown\r\n     */\r\n    public showConstraint(constraint: PhysicsConstraint): Nullable<AbstractMesh> {\r\n        if (!this._scene) {\r\n            return null;\r\n        }\r\n\r\n        for (let i = 0; i < this._numConstraints; i++) {\r\n            if (this._constraints[i] == constraint) {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        const debugMesh = this._getDebugConstraintMesh(constraint);\r\n        if (debugMesh) {\r\n            this._constraints[this._numConstraints] = constraint;\r\n            this._constraintMeshes[this._numConstraints] = debugMesh;\r\n\r\n            if (this._numConstraints === 0) {\r\n                this._constraintRenderFunction = () => this._updateDebugConstraints();\r\n                this._scene.registerBeforeRender(this._constraintRenderFunction);\r\n            }\r\n\r\n            this._numConstraints++;\r\n        }\r\n\r\n        return debugMesh ? debugMesh[0] : null;\r\n    }\r\n\r\n    /**\r\n     * Hides an impostor from the scene.\r\n     * @param impostor - The impostor to hide.\r\n     *\r\n     * This method is useful for hiding an impostor from the scene. It removes the\r\n     * impostor from the utility layer scene, disposes the mesh, and removes the\r\n     * impostor from the list of impostors. If the impostor is the last one in the\r\n     * list, it also unregisters the render function.\r\n     */\r\n    public hideImpostor(impostor: Nullable<PhysicsImpostor>) {\r\n        if (!impostor || !this._scene || !this._utilityLayer) {\r\n            return;\r\n        }\r\n\r\n        let removed = false;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        for (let i = 0; i < this._numMeshes; i++) {\r\n            if (this._impostors[i] == impostor) {\r\n                const mesh = this._meshes[i];\r\n\r\n                if (!mesh) {\r\n                    continue;\r\n                }\r\n\r\n                utilityLayerScene.removeMesh(mesh);\r\n                mesh.dispose();\r\n\r\n                const index = this._debugMeshMeshes.indexOf(mesh as Mesh);\r\n                if (index > -1) {\r\n                    this._debugMeshMeshes.splice(index, 1);\r\n                }\r\n\r\n                this._numMeshes--;\r\n                if (this._numMeshes > 0) {\r\n                    this._meshes[i] = this._meshes[this._numMeshes];\r\n                    this._impostors[i] = this._impostors[this._numMeshes];\r\n                    this._meshes[this._numMeshes] = null;\r\n                    this._impostors[this._numMeshes] = null;\r\n                } else {\r\n                    this._meshes[0] = null;\r\n                    this._impostors[0] = null;\r\n                }\r\n                removed = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (removed && this._numMeshes === 0) {\r\n            this._scene.unregisterBeforeRender(this._renderFunction);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Hides a body from the physics engine.\r\n     * @param body - The body to hide.\r\n     * @returns true if body actually removed\r\n     *\r\n     * This function is useful for hiding a body from the physics engine.\r\n     * It removes the body from the utility layer scene and disposes the mesh associated with it.\r\n     * It also unregisters the render function if the number of bodies is 0.\r\n     * This is useful for hiding a body from the physics engine without deleting it.\r\n     */\r\n    public hideBody(body: Nullable<PhysicsBody>): boolean {\r\n        if (!body || !this._scene || !this._utilityLayer) {\r\n            return false;\r\n        }\r\n\r\n        let removed = false;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        for (let i = 0; i < this._numBodies; i++) {\r\n            if (this._bodies[i] === body) {\r\n                const mesh = this._bodyMeshes[i];\r\n\r\n                if (!mesh) {\r\n                    continue;\r\n                }\r\n\r\n                utilityLayerScene.removeMesh(mesh);\r\n                mesh.dispose();\r\n\r\n                this._numBodies--;\r\n                if (this._numBodies > 0) {\r\n                    this._bodyMeshes[i] = this._bodyMeshes[this._numBodies];\r\n                    this._bodies[i] = this._bodies[this._numBodies];\r\n                    this._bodyMeshes[this._numBodies] = null;\r\n                    this._bodies[this._numBodies] = null;\r\n                } else {\r\n                    this._bodyMeshes[0] = null;\r\n                    this._bodies[0] = null;\r\n                }\r\n                removed = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (removed && this._numBodies === 0) {\r\n            this._scene.unregisterBeforeRender(this._renderFunction);\r\n        }\r\n        return removed;\r\n    }\r\n\r\n    /**\r\n     * Hides a body's inertia from the viewer utility layer\r\n     * @param body the body to hide\r\n     * @returns true if inertia actually removed\r\n     */\r\n    public hideInertia(body: Nullable<PhysicsBody>): boolean {\r\n        if (!body || !this._scene || !this._utilityLayer) {\r\n            return false;\r\n        }\r\n        let removed = false;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        for (let i = 0; i < this._numInertiaBodies; i++) {\r\n            if (this._inertiaBodies[i] === body) {\r\n                const mesh = this._inertiaMeshes[i];\r\n\r\n                if (!mesh) {\r\n                    continue;\r\n                }\r\n\r\n                utilityLayerScene.removeMesh(mesh);\r\n                mesh.dispose();\r\n\r\n                this._inertiaBodies.splice(i, 1);\r\n                this._inertiaMeshes.splice(i, 1);\r\n\r\n                this._numInertiaBodies--;\r\n\r\n                removed = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (removed && this._numInertiaBodies === 0) {\r\n            this._scene.unregisterBeforeRender(this._inertiaRenderFunction);\r\n        }\r\n        return removed;\r\n    }\r\n\r\n    /**\r\n     * Hide a physics constraint from the viewer utility layer\r\n     * @param constraint the constraint to hide\r\n     */\r\n    public hideConstraint(constraint: Nullable<PhysicsConstraint>) {\r\n        if (!constraint || !this._scene || !this._utilityLayer) {\r\n            return;\r\n        }\r\n        let removed = false;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        for (let i = 0; i < this._numConstraints; i++) {\r\n            if (this._constraints[i] === constraint) {\r\n                const meshes = this._constraintMeshes[i];\r\n\r\n                if (!meshes) {\r\n                    continue;\r\n                }\r\n\r\n                for (const mesh of meshes) {\r\n                    utilityLayerScene.removeMesh(mesh);\r\n                    mesh.dispose();\r\n                }\r\n\r\n                this._constraints.splice(i, 1);\r\n                this._constraintMeshes.splice(i, 1);\r\n\r\n                this._numConstraints--;\r\n\r\n                if (this._numConstraints > 0) {\r\n                    this._constraints[i] = this._constraints[this._numConstraints];\r\n                    this._constraintMeshes[i] = this._constraintMeshes[this._numConstraints];\r\n                    this._constraints[this._numConstraints] = null;\r\n                    this._constraintMeshes[this._numConstraints] = null;\r\n                } else {\r\n                    this._constraints[0] = null;\r\n                    this._constraintMeshes[0] = null;\r\n                }\r\n\r\n                removed = true;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (removed && this._numConstraints === 0) {\r\n            this._scene.unregisterBeforeRender(this._constraintRenderFunction);\r\n        }\r\n    }\r\n\r\n    private _getDebugMaterial(scene: Scene): Material {\r\n        if (!this._debugMaterial) {\r\n            this._debugMaterial = new StandardMaterial(\"\", scene);\r\n            this._debugMaterial.wireframe = true;\r\n            this._debugMaterial.emissiveColor = Color3.White();\r\n            this._debugMaterial.disableLighting = true;\r\n        }\r\n\r\n        return this._debugMaterial;\r\n    }\r\n\r\n    private _getDebugInertiaMaterial(scene: Scene): Material {\r\n        if (!this._debugInertiaMaterial) {\r\n            this._debugInertiaMaterial = new StandardMaterial(\"\", scene);\r\n            this._debugInertiaMaterial.disableLighting = true;\r\n            this._debugInertiaMaterial.alpha = 0.0;\r\n        }\r\n\r\n        return this._debugInertiaMaterial;\r\n    }\r\n\r\n    private _getDebugAxisColoredMaterial(axisNumber: number, scene: Scene): Material {\r\n        const material = new StandardMaterial(\"\", scene);\r\n        material.emissiveColor = axisNumber == 0 ? Color3.Red() : axisNumber == 1 ? Color3.Green() : Color3.Blue();\r\n        material.disableLighting = true;\r\n        return material;\r\n    }\r\n\r\n    private _getDebugBoxMesh(scene: Scene): AbstractMesh {\r\n        if (!this._debugBoxMesh) {\r\n            this._debugBoxMesh = CreateBox(\"physicsBodyBoxViewMesh\", { size: 1 }, scene);\r\n            this._debugBoxMesh.rotationQuaternion = Quaternion.Identity();\r\n            this._debugBoxMesh.material = this._getDebugMaterial(scene);\r\n            this._debugBoxMesh.setEnabled(false);\r\n        }\r\n\r\n        return this._debugBoxMesh.createInstance(\"physicsBodyBoxViewInstance\");\r\n    }\r\n\r\n    private _getDebugSphereMesh(scene: Scene): AbstractMesh {\r\n        if (!this._debugSphereMesh) {\r\n            this._debugSphereMesh = CreateSphere(\"physicsBodySphereViewMesh\", { diameter: 1 }, scene);\r\n            this._debugSphereMesh.rotationQuaternion = Quaternion.Identity();\r\n            this._debugSphereMesh.material = this._getDebugMaterial(scene);\r\n            this._debugSphereMesh.setEnabled(false);\r\n        }\r\n\r\n        return this._debugSphereMesh.createInstance(\"physicsBodySphereViewInstance\");\r\n    }\r\n\r\n    private _getDebugCapsuleMesh(scene: Scene): AbstractMesh {\r\n        if (!this._debugCapsuleMesh) {\r\n            this._debugCapsuleMesh = CreateCapsule(\"physicsBodyCapsuleViewMesh\", { height: 1 } as ICreateCapsuleOptions, scene);\r\n            this._debugCapsuleMesh.rotationQuaternion = Quaternion.Identity();\r\n            this._debugCapsuleMesh.material = this._getDebugMaterial(scene);\r\n            this._debugCapsuleMesh.setEnabled(false);\r\n        }\r\n\r\n        return this._debugCapsuleMesh.createInstance(\"physicsBodyCapsuleViewInstance\");\r\n    }\r\n\r\n    private _getDebugCylinderMesh(scene: Scene): AbstractMesh {\r\n        if (!this._debugCylinderMesh) {\r\n            this._debugCylinderMesh = CreateCylinder(\"physicsBodyCylinderViewMesh\", { diameterTop: 1, diameterBottom: 1, height: 1 }, scene);\r\n            this._debugCylinderMesh.rotationQuaternion = Quaternion.Identity();\r\n            this._debugCylinderMesh.material = this._getDebugMaterial(scene);\r\n            this._debugCylinderMesh.setEnabled(false);\r\n        }\r\n\r\n        return this._debugCylinderMesh.createInstance(\"physicsBodyCylinderViewInstance\");\r\n    }\r\n\r\n    private _getDebugMeshMesh(mesh: Mesh, scene: Scene): AbstractMesh {\r\n        const wireframeOver = new Mesh(mesh.name, scene, null, mesh);\r\n        wireframeOver.setParent(mesh);\r\n        wireframeOver.position = Vector3.Zero();\r\n        wireframeOver.material = this._getDebugMaterial(scene);\r\n\r\n        this._debugMeshMeshes.push(wireframeOver);\r\n\r\n        return wireframeOver;\r\n    }\r\n\r\n    private _getDebugMesh(impostor: PhysicsImpostor, targetMesh?: Mesh): Nullable<AbstractMesh> {\r\n        if (!this._utilityLayer) {\r\n            return null;\r\n        }\r\n\r\n        // Only create child impostor debug meshes when evaluating the parent\r\n        if (targetMesh && targetMesh.parent && (targetMesh.parent as Mesh).physicsImpostor) {\r\n            return null;\r\n        }\r\n\r\n        let mesh: Nullable<AbstractMesh> = null;\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n        if (!impostor.physicsBody) {\r\n            Logger.Warn(\"Unable to get physicsBody of impostor. It might be initialized later by its parent's impostor.\");\r\n            return null;\r\n        }\r\n        switch (impostor.type) {\r\n            case PhysicsImpostor.BoxImpostor:\r\n                mesh = this._getDebugBoxMesh(utilityLayerScene);\r\n                impostor.getBoxSizeToRef(mesh.scaling);\r\n                break;\r\n            case PhysicsImpostor.SphereImpostor: {\r\n                mesh = this._getDebugSphereMesh(utilityLayerScene);\r\n                const radius = impostor.getRadius();\r\n                mesh.scaling.x = radius * 2;\r\n                mesh.scaling.y = radius * 2;\r\n                mesh.scaling.z = radius * 2;\r\n                break;\r\n            }\r\n            case PhysicsImpostor.CapsuleImpostor: {\r\n                mesh = this._getDebugCapsuleMesh(utilityLayerScene);\r\n                const bi = impostor.object.getBoundingInfo();\r\n                mesh.scaling.x = (bi.boundingBox.maximum.x - bi.boundingBox.minimum.x) * 2 * impostor.object.scaling.x;\r\n                mesh.scaling.y = (bi.boundingBox.maximum.y - bi.boundingBox.minimum.y) * impostor.object.scaling.y;\r\n                mesh.scaling.z = (bi.boundingBox.maximum.z - bi.boundingBox.minimum.z) * 2 * impostor.object.scaling.z;\r\n                break;\r\n            }\r\n            case PhysicsImpostor.MeshImpostor:\r\n                if (targetMesh) {\r\n                    mesh = this._getDebugMeshMesh(targetMesh, utilityLayerScene);\r\n                }\r\n                break;\r\n            case PhysicsImpostor.NoImpostor:\r\n                if (targetMesh) {\r\n                    // Handle compound impostors\r\n                    const childMeshes = targetMesh.getChildMeshes().filter((c) => {\r\n                        return c.physicsImpostor ? 1 : 0;\r\n                    });\r\n                    for (const m of childMeshes) {\r\n                        if (m.physicsImpostor && m.getClassName() === \"Mesh\") {\r\n                            const boundingInfo = m.getBoundingInfo();\r\n                            const min = boundingInfo.boundingBox.minimum;\r\n                            const max = boundingInfo.boundingBox.maximum;\r\n                            switch (m.physicsImpostor.type) {\r\n                                case PhysicsImpostor.BoxImpostor:\r\n                                    mesh = this._getDebugBoxMesh(utilityLayerScene);\r\n                                    mesh.position.copyFrom(min);\r\n                                    mesh.position.addInPlace(max);\r\n                                    mesh.position.scaleInPlace(0.5);\r\n                                    break;\r\n                                case PhysicsImpostor.SphereImpostor:\r\n                                    mesh = this._getDebugSphereMesh(utilityLayerScene);\r\n                                    break;\r\n                                case PhysicsImpostor.CylinderImpostor:\r\n                                    mesh = this._getDebugCylinderMesh(utilityLayerScene);\r\n                                    break;\r\n                                default:\r\n                                    mesh = null;\r\n                                    break;\r\n                            }\r\n                            if (mesh) {\r\n                                mesh.scaling.x = max.x - min.x;\r\n                                mesh.scaling.y = max.y - min.y;\r\n                                mesh.scaling.z = max.z - min.z;\r\n                                mesh.parent = m;\r\n                            }\r\n                        }\r\n                    }\r\n                } else {\r\n                    Logger.Warn(\"No target mesh parameter provided for NoImpostor. Skipping.\");\r\n                }\r\n                mesh = null;\r\n                break;\r\n            case PhysicsImpostor.CylinderImpostor: {\r\n                mesh = this._getDebugCylinderMesh(utilityLayerScene);\r\n                const bi = impostor.object.getBoundingInfo();\r\n                mesh.scaling.x = (bi.boundingBox.maximum.x - bi.boundingBox.minimum.x) * impostor.object.scaling.x;\r\n                mesh.scaling.y = (bi.boundingBox.maximum.y - bi.boundingBox.minimum.y) * impostor.object.scaling.y;\r\n                mesh.scaling.z = (bi.boundingBox.maximum.z - bi.boundingBox.minimum.z) * impostor.object.scaling.z;\r\n                break;\r\n            }\r\n        }\r\n        return mesh;\r\n    }\r\n\r\n    /**\r\n     * Creates a debug mesh for a given physics body\r\n     * @param body The physics body to create the debug mesh for\r\n     * @returns The created debug mesh or null if the utility layer is not available\r\n     *\r\n     * This code is useful for creating a debug mesh for a given physics body.\r\n     * It creates a Mesh object with a VertexData object containing the positions and indices\r\n     * of the geometry of the body. The mesh is then assigned a debug material from the utility layer scene.\r\n     * This allows for visualizing the physics body in the scene.\r\n     */\r\n    private _getDebugBodyMesh(body: PhysicsBody): Nullable<AbstractMesh> {\r\n        if (!this._utilityLayer) {\r\n            return null;\r\n        }\r\n\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        const mesh = new Mesh(\"custom\", utilityLayerScene);\r\n        const vertexData = new VertexData();\r\n        const geometry = body.getGeometry() as any;\r\n        vertexData.positions = geometry.positions;\r\n        vertexData.indices = geometry.indices;\r\n        vertexData.applyToMesh(mesh);\r\n        if (body._pluginDataInstances) {\r\n            const instanceBuffer = new Float32Array(body._pluginDataInstances.length * 16);\r\n            mesh.thinInstanceSetBuffer(\"matrix\", instanceBuffer, 16, false);\r\n        }\r\n        mesh.material = this._getDebugMaterial(utilityLayerScene);\r\n        return mesh;\r\n    }\r\n\r\n    private _getMeshDebugInertiaMatrixToRef(massProps: PhysicsMassProperties, matrix: Matrix) {\r\n        const orientation = massProps.inertiaOrientation ?? Quaternion.Identity();\r\n        const inertiaLocal = massProps.inertia ?? Vector3.Zero();\r\n        const center = massProps.centerOfMass ?? Vector3.Zero();\r\n\r\n        const betaSqrd = (inertiaLocal.x - inertiaLocal.y + inertiaLocal.z) * 6;\r\n        const beta = Math.sqrt(Math.max(betaSqrd, 0)); // Safety check for zeroed elements!\r\n\r\n        const gammaSqrd = inertiaLocal.x * 12 - betaSqrd;\r\n        const gamma = Math.sqrt(Math.max(gammaSqrd, 0)); // Safety check for zeroed elements!\r\n\r\n        const alphaSqrd = inertiaLocal.z * 12 - betaSqrd;\r\n        const alpha = Math.sqrt(Math.max(alphaSqrd, 0)); // Safety check for zeroed elements!\r\n\r\n        const extents = TmpVectors.Vector3[0];\r\n        extents.set(alpha, beta, gamma);\r\n\r\n        const scaling = Matrix.ScalingToRef(extents.x, extents.y, extents.z, TmpVectors.Matrix[0]);\r\n        const rotation = orientation.toRotationMatrix(TmpVectors.Matrix[1]);\r\n        const translation = Matrix.TranslationToRef(center.x, center.y, center.z, TmpVectors.Matrix[2]);\r\n\r\n        scaling.multiplyToRef(rotation, matrix);\r\n        matrix.multiplyToRef(translation, matrix);\r\n\r\n        return matrix;\r\n    }\r\n\r\n    private _getDebugInertiaMesh(body: PhysicsBody): Nullable<AbstractMesh> {\r\n        if (!this._utilityLayer) {\r\n            return null;\r\n        }\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        // The base inertia mesh is going to be a 1x1 cube that's scaled and rotated according to the inertia\r\n        const inertiaBoxMesh = MeshBuilder.CreateBox(\"custom\", { size: 1 }, utilityLayerScene);\r\n        const matrixRef = Matrix.Identity();\r\n        if (body._pluginDataInstances.length) {\r\n            const instanceBuffer = new Float32Array(body._pluginDataInstances.length * 16);\r\n            for (let i = 0; i < body._pluginDataInstances.length; ++i) {\r\n                const props = body.getMassProperties(i);\r\n                this._getMeshDebugInertiaMatrixToRef(props, matrixRef);\r\n                matrixRef.copyToArray(instanceBuffer, i * 16);\r\n            }\r\n            inertiaBoxMesh.thinInstanceSetBuffer(\"matrix\", instanceBuffer, 16, false);\r\n        } else {\r\n            const props = body.getMassProperties();\r\n            this._getMeshDebugInertiaMatrixToRef(props, matrixRef);\r\n            matrixRef.decomposeToTransformNode(inertiaBoxMesh);\r\n        }\r\n        inertiaBoxMesh.enableEdgesRendering();\r\n        inertiaBoxMesh.edgesWidth = 2.0;\r\n        inertiaBoxMesh.edgesColor = new Color4(1, 0, 1, 1);\r\n        inertiaBoxMesh.material = this._getDebugInertiaMaterial(utilityLayerScene);\r\n\r\n        return inertiaBoxMesh;\r\n    }\r\n\r\n    private _getTransformFromBodyToRef(body: PhysicsBody, matrix: Matrix, instanceIndex?: number) {\r\n        const tnode = body.transformNode;\r\n        if (instanceIndex && instanceIndex >= 0) {\r\n            return Matrix.FromArrayToRef((tnode as Mesh)._thinInstanceDataStorage.matrixData!, instanceIndex, matrix);\r\n        } else {\r\n            return matrix.copyFrom(tnode.getWorldMatrix());\r\n        }\r\n    }\r\n\r\n    private _createAngularConstraintMesh(minLimit: number, maxLimit: number, axisNumber: number, parent: TransformNode, scene: Scene): AbstractMesh {\r\n        const arcAngle = (maxLimit - minLimit) / (Math.PI * 2);\r\n        const mesh = MeshBuilder.CreateCylinder(\"ConstraintCylinder\", { height: 0.0001, diameter: 3 * this._constraintAngularSize, arc: arcAngle }, scene);\r\n        mesh.material = this._getDebugAxisColoredMaterial(axisNumber, scene);\r\n        mesh.parent = parent;\r\n        const parentScaling = parent.absoluteScaling;\r\n        switch (axisNumber) {\r\n            case 0:\r\n                mesh.rotation.z = Math.PI * 0.5;\r\n                mesh.rotation.x = -minLimit + Math.PI * 0.5;\r\n                // scaling on y,z\r\n                mesh.scaling.x = 1 / parentScaling.x;\r\n                mesh.scaling.y = 1 / parentScaling.z;\r\n                mesh.scaling.z = 1 / parentScaling.y;\r\n                break;\r\n            case 1:\r\n                mesh.rotation.y = Math.PI * 1.5 + minLimit;\r\n                // flip x,z\r\n                mesh.scaling.x = 1 / parentScaling.z;\r\n                mesh.scaling.y = 1 / parentScaling.y;\r\n                mesh.scaling.z = 1 / parentScaling.x;\r\n                break;\r\n            case 2:\r\n                mesh.rotation.x = Math.PI * 0.5;\r\n                // flip z,y\r\n                mesh.scaling.x = 1 / parentScaling.x;\r\n                mesh.scaling.y = 1 / parentScaling.z;\r\n                mesh.scaling.z = 1 / parentScaling.y;\r\n                break;\r\n        }\r\n        return mesh;\r\n    }\r\n\r\n    private _createCage(parent: TransformNode, scene: Scene): AbstractMesh {\r\n        const cage = MeshBuilder.CreateBox(\"cage\", { size: 1 }, scene);\r\n        cage.setPivotPoint(new Vector3(-0.5, -0.5, -0.5));\r\n        const transparentMaterial = new StandardMaterial(\"cage_material\", scene);\r\n        transparentMaterial.alpha = 0; // Fully transparent\r\n        cage.material = transparentMaterial;\r\n\r\n        cage.enableEdgesRendering();\r\n        cage.edgesWidth = 4.0;\r\n        cage.edgesColor = new Color4(1, 1, 1, 1);\r\n        cage.parent = parent;\r\n        return cage;\r\n    }\r\n\r\n    private _getDebugConstraintMesh(constraint: PhysicsConstraint): Nullable<Array<AbstractMesh>> {\r\n        if (!this._utilityLayer) {\r\n            return null;\r\n        }\r\n        const utilityLayerScene = this._utilityLayer.utilityLayerScene;\r\n\r\n        if (!constraint._initOptions) {\r\n            return null;\r\n        }\r\n\r\n        // Get constraint pivot and axes\r\n        const { pivotA, pivotB, axisA, axisB, perpAxisA, perpAxisB } = constraint._initOptions;\r\n\r\n        if (!pivotA || !pivotB || !axisA || !axisB || !perpAxisA || !perpAxisB) {\r\n            return null;\r\n        }\r\n\r\n        // Create a mesh to parent all the constraint debug meshes to\r\n        const parentingMesh = new Mesh(\"parentingDebugConstraint\", utilityLayerScene);\r\n\r\n        // First, get a reference to all physic bodies that are using this constraint\r\n        const bodiesUsingConstraint = constraint.getBodiesUsingConstraint();\r\n\r\n        const parentedConstraintMeshes = [];\r\n        parentedConstraintMeshes.push(parentingMesh);\r\n\r\n        for (const bodyPairInfo of bodiesUsingConstraint) {\r\n            // Create a mesh to keep the pair of constraint axes\r\n            const parentOfPair = new TransformNode(\"parentOfPair\", utilityLayerScene);\r\n            parentOfPair.parent = parentingMesh;\r\n\r\n            const { parentBody, parentBodyIndex, childBody, childBodyIndex } = bodyPairInfo;\r\n            // Get the parent transform\r\n\r\n            const parentTransform = this._getTransformFromBodyToRef(parentBody, TmpVectors.Matrix[0], parentBodyIndex);\r\n            const childTransform = this._getTransformFromBodyToRef(childBody, TmpVectors.Matrix[1], childBodyIndex);\r\n\r\n            const parentCoordSystemNode = new TransformNode(\"parentCoordSystem\", utilityLayerScene);\r\n            // parentCoordSystemNode.parent = parentingMesh;\r\n            parentCoordSystemNode.parent = parentOfPair;\r\n            // Save parent and index here to be able to get the transform on update\r\n            parentCoordSystemNode.metadata = { parentBody, parentBodyIndex };\r\n            parentTransform.decomposeToTransformNode(parentCoordSystemNode);\r\n\r\n            const childCoordSystemNode = new TransformNode(\"childCoordSystem\", utilityLayerScene);\r\n            // childCoordSystemNode.parent = parentingMesh;\r\n            childCoordSystemNode.parent = parentOfPair;\r\n            // Save child and index here to be able to get the transform on update\r\n            childCoordSystemNode.metadata = { childBody, childBodyIndex };\r\n            childTransform.decomposeToTransformNode(childCoordSystemNode);\r\n\r\n            // Get the transform to align the XYZ axes to the constraint axes\r\n            const rotTransformParent = Quaternion.FromRotationMatrix(Matrix.FromXYZAxesToRef(axisA, perpAxisA, axisA.cross(perpAxisA), TmpVectors.Matrix[0]));\r\n            const rotTransformChild = Quaternion.FromRotationMatrix(Matrix.FromXYZAxesToRef(axisB, perpAxisB, axisB.cross(perpAxisB), TmpVectors.Matrix[0]));\r\n\r\n            const translateTransformParent = pivotA;\r\n            const translateTransformChild = pivotB;\r\n\r\n            // Create a transform node and set its matrix\r\n            const parentTransformNode = new TransformNode(\"constraint_parent\", utilityLayerScene);\r\n            parentTransformNode.position.copyFrom(translateTransformParent);\r\n            parentTransformNode.rotationQuaternion = rotTransformParent;\r\n            parentTransformNode.parent = parentCoordSystemNode;\r\n\r\n            const childTransformNode = new TransformNode(\"constraint_child\", utilityLayerScene);\r\n            childTransformNode.parent = childCoordSystemNode;\r\n            childTransformNode.position.copyFrom(translateTransformChild);\r\n            childTransformNode.rotationQuaternion = rotTransformChild;\r\n\r\n            // Create axes for the constraint\r\n            const parentAxes = new AxesViewer(utilityLayerScene, this._constraintAxesSize);\r\n            parentAxes.xAxis.parent = parentTransformNode;\r\n            parentAxes.yAxis.parent = parentTransformNode;\r\n            parentAxes.zAxis.parent = parentTransformNode;\r\n\r\n            const childAxes = new AxesViewer(utilityLayerScene, this._constraintAxesSize);\r\n            childAxes.xAxis.parent = childTransformNode;\r\n            childAxes.yAxis.parent = childTransformNode;\r\n            childAxes.zAxis.parent = childTransformNode;\r\n\r\n            // constrain vis\r\n            const engine = this._physicsEnginePlugin! as IPhysicsEnginePluginV2;\r\n\r\n            const constraintAxisAngular = [PhysicsConstraintAxis.ANGULAR_X, PhysicsConstraintAxis.ANGULAR_Y, PhysicsConstraintAxis.ANGULAR_Z];\r\n            const constraintAxisLinear = [PhysicsConstraintAxis.LINEAR_X, PhysicsConstraintAxis.LINEAR_Y, PhysicsConstraintAxis.LINEAR_Z];\r\n            const constraintAxis = [constraintAxisAngular, constraintAxisLinear];\r\n\r\n            // count axis. Angular and Linear\r\n            const lockCount = [0, 0];\r\n            for (let angularLinear = 0; angularLinear < 2; angularLinear++) {\r\n                for (let axis = 0; axis < 3; axis++) {\r\n                    const constraintAxisValue = constraintAxis[angularLinear][axis];\r\n                    const axisMode = engine.getAxisMode(constraint, constraintAxisValue);\r\n                    if (axisMode == PhysicsConstraintAxisLimitMode.LOCKED) {\r\n                        lockCount[angularLinear]++;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Any free/limited Linear axis\r\n            if (lockCount[1] != 3) {\r\n                const cage = this._createCage(parentTransformNode, utilityLayerScene);\r\n\r\n                const min = TmpVectors.Vector3[0];\r\n                const max = TmpVectors.Vector3[1];\r\n                const limited = [false, false, false];\r\n\r\n                limited[0] = engine.getAxisMode(constraint, PhysicsConstraintAxis.LINEAR_X) == PhysicsConstraintAxisLimitMode.LIMITED;\r\n                limited[1] = engine.getAxisMode(constraint, PhysicsConstraintAxis.LINEAR_Y) == PhysicsConstraintAxisLimitMode.LIMITED;\r\n                limited[2] = engine.getAxisMode(constraint, PhysicsConstraintAxis.LINEAR_Z) == PhysicsConstraintAxisLimitMode.LIMITED;\r\n\r\n                min.x = limited[0] ? engine.getAxisMinLimit(constraint, PhysicsConstraintAxis.LINEAR_X)! : 0;\r\n                max.x = limited[0] ? engine.getAxisMaxLimit(constraint, PhysicsConstraintAxis.LINEAR_X)! : 0;\r\n                min.y = limited[1] ? engine.getAxisMinLimit(constraint, PhysicsConstraintAxis.LINEAR_Y)! : 0;\r\n                max.y = limited[1] ? engine.getAxisMaxLimit(constraint, PhysicsConstraintAxis.LINEAR_Y)! : 0;\r\n                min.z = limited[2] ? engine.getAxisMinLimit(constraint, PhysicsConstraintAxis.LINEAR_Z)! : 0;\r\n                max.z = limited[2] ? engine.getAxisMaxLimit(constraint, PhysicsConstraintAxis.LINEAR_Z)! : 0;\r\n\r\n                cage.position.x = min.x + 0.5;\r\n                cage.position.y = min.y + 0.5;\r\n                cage.position.z = min.z + 0.5;\r\n\r\n                cage.scaling.x = max.x - min.x + Epsilon;\r\n                cage.scaling.y = max.y - min.y + Epsilon;\r\n                cage.scaling.z = max.z - min.z + Epsilon;\r\n                parentedConstraintMeshes.push(cage);\r\n            }\r\n\r\n            // Angular\r\n            if (lockCount[0] != 3) {\r\n                for (let axisIndex = 0; axisIndex < 3; axisIndex++) {\r\n                    const axis = constraintAxisAngular[axisIndex];\r\n                    const axisMode = engine.getAxisMode(constraint, axis);\r\n                    let minLimit = 0;\r\n                    let maxLimit = Math.PI * 2;\r\n                    if (axisMode == PhysicsConstraintAxisLimitMode.LIMITED) {\r\n                        minLimit = engine.getAxisMinLimit(constraint, axis)!;\r\n                        maxLimit = engine.getAxisMaxLimit(constraint, axis)!;\r\n                    }\r\n                    if (axisMode != PhysicsConstraintAxisLimitMode.LOCKED && constraint.options.pivotB) {\r\n                        const mesh = this._createAngularConstraintMesh(minLimit, maxLimit, axisIndex, childBody.transformNode, utilityLayerScene);\r\n                        mesh.position.copyFrom(constraint.options.pivotB);\r\n                        parentedConstraintMeshes.push(mesh);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return parentedConstraintMeshes;\r\n    }\r\n\r\n    /**\r\n     * Clean up physics debug display\r\n     */\r\n    public dispose() {\r\n        // impostors\r\n        for (let index = this._numMeshes - 1; index >= 0; index--) {\r\n            this.hideImpostor(this._impostors[0]);\r\n        }\r\n        // bodies\r\n        for (let index = this._numBodies - 1; index >= 0; index--) {\r\n            this.hideBody(this._bodies[0]);\r\n        }\r\n        // inertia\r\n        for (let index = this._numInertiaBodies - 1; index >= 0; index--) {\r\n            this.hideInertia(this._inertiaBodies[0]);\r\n        }\r\n\r\n        if (this._debugBoxMesh) {\r\n            this._debugBoxMesh.dispose();\r\n        }\r\n        if (this._debugSphereMesh) {\r\n            this._debugSphereMesh.dispose();\r\n        }\r\n        if (this._debugCylinderMesh) {\r\n            this._debugCylinderMesh.dispose();\r\n        }\r\n        if (this._debugMaterial) {\r\n            this._debugMaterial.dispose();\r\n        }\r\n\r\n        this._impostors.length = 0;\r\n        this._scene = null;\r\n        this._physicsEnginePlugin = null;\r\n\r\n        if (this._ownUtilityLayer && this._utilityLayer) {\r\n            this._utilityLayer.dispose();\r\n            this._utilityLayer = null;\r\n        }\r\n    }\r\n}\r\n"]}