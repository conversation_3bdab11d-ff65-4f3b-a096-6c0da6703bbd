{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { Menu, X } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface NavItem {\n  href: string;\n  label: string;\n  ariaLabel: string;\n}\n\nconst navItems: NavItem[] = [\n  { href: \"#home\", label: \"HOME\", ariaLabel: \"Navigate to home section\" },\n  { href: \"#about\", label: \"ABOUT\", ariaLabel: \"Navigate to about section\" },\n  { href: \"#skills\", label: \"SKILLS\", ariaLabel: \"Navigate to skills section\" },\n  {\n    href: \"#projects\",\n    label: \"PROJECTS\",\n    ariaLabel: \"Navigate to projects section\",\n  },\n  {\n    href: \"#advanced-3d\",\n    label: \"3D TECH\",\n    ariaLabel: \"Navigate to advanced 3D technology section\",\n  },\n  { href: \"/blog\", label: \"BLOG\", ariaLabel: \"Navigate to blog section\" },\n  {\n    href: \"#playground\",\n    label: \"PLAYGROUND\",\n    ariaLabel: \"Navigate to code playground section\",\n  },\n  {\n    href: \"#contact\",\n    label: \"CONTACT\",\n    ariaLabel: \"Navigate to contact section\",\n  },\n];\n\ninterface HeaderProps {\n  className?: string;\n}\n\nexport default function Header({ className }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 100);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  const handleSmoothScroll = (\n    e: React.MouseEvent<HTMLAnchorElement>,\n    href: string\n  ) => {\n    if (href.startsWith(\"#\")) {\n      e.preventDefault();\n      const target = document.querySelector(href);\n      if (target) {\n        target.scrollIntoView({\n          behavior: \"smooth\",\n          block: \"start\",\n        });\n        setIsMobileMenuOpen(false);\n      }\n    }\n  };\n\n  return (\n    <header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 backdrop-blur-md border-b border-cyan-500/30 transition-all duration-300\",\n        isScrolled ? \"bg-black/90\" : \"bg-black/80\",\n        className\n      )}\n    >\n      <nav\n        className=\"container mx-auto px-6 py-4\"\n        role=\"navigation\"\n        aria-label=\"Main navigation\"\n      >\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <div className=\"logo\">\n            <Link\n              href=\"/\"\n              className=\"text-2xl font-bold text-cyan-400 neon-glow glitch focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm font-mono\"\n              aria-label=\"Trinanda - Go to homepage\"\n            >\n              &lt;TRINANDA/&gt;\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <ul className=\"hidden md:flex space-x-8\" role=\"menubar\">\n            {navItems.map((item) => (\n              <li key={item.href} role=\"none\">\n                {item.href.startsWith(\"#\") ? (\n                  <a\n                    href={item.href}\n                    className=\"nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm\"\n                    role=\"menuitem\"\n                    aria-label={item.ariaLabel}\n                    onClick={(e) => handleSmoothScroll(e, item.href)}\n                  >\n                    {item.label}\n                  </a>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className=\"nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm\"\n                    role=\"menuitem\"\n                    aria-label={item.ariaLabel}\n                  >\n                    {item.label}\n                  </Link>\n                )}\n              </li>\n            ))}\n          </ul>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden text-cyan-400 hover:text-pink-400 transition-colors focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm p-1\"\n            aria-label=\"Toggle mobile menu\"\n            aria-expanded={isMobileMenuOpen}\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            type=\"button\"\n          >\n            {isMobileMenuOpen ? (\n              <X className=\"w-6 h-6\" aria-hidden=\"true\" />\n            ) : (\n              <Menu className=\"w-6 h-6\" aria-hidden=\"true\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div\n            className=\"md:hidden mt-4 pb-4\"\n            role=\"menu\"\n            aria-labelledby=\"mobile-menu-btn\"\n          >\n            <ul className=\"space-y-4\">\n              {navItems.map((item) => (\n                <li key={item.href} role=\"none\">\n                  {item.href.startsWith(\"#\") ? (\n                    <a\n                      href={item.href}\n                      className=\"mobile-nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm\"\n                      role=\"menuitem\"\n                      aria-label={item.ariaLabel}\n                      onClick={(e) => handleSmoothScroll(e, item.href)}\n                    >\n                      {item.label}\n                    </a>\n                  ) : (\n                    <Link\n                      href={item.href}\n                      className=\"mobile-nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm\"\n                      role=\"menuitem\"\n                      aria-label={item.ariaLabel}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                    >\n                      {item.label}\n                    </Link>\n                  )}\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n      </nav>\n\n      <style jsx>{`\n        .nav-link {\n          @apply text-gray-300 hover:text-cyan-400 transition-all duration-300 relative;\n          font-family: \"Orbitron\", monospace;\n          font-weight: 500;\n          letter-spacing: 1px;\n        }\n\n        .nav-link:hover {\n          text-shadow: 0 0 10px currentColor;\n        }\n\n        .nav-link::after {\n          content: \"\";\n          position: absolute;\n          bottom: -4px;\n          left: 0;\n          width: 0;\n          height: 2px;\n          background: linear-gradient(\n            90deg,\n            var(--neon-cyan),\n            var(--neon-pink)\n          );\n          transition: width 0.3s ease;\n        }\n\n        .nav-link:hover::after {\n          width: 100%;\n        }\n\n        .mobile-nav-link {\n          @apply block text-gray-300 hover:text-cyan-400 transition-colors py-2 border-b border-gray-700/50;\n          font-family: \"Orbitron\", monospace;\n          font-weight: 500;\n          letter-spacing: 1px;\n        }\n\n        .mobile-nav-link:hover {\n          text-shadow: 0 0 10px currentColor;\n        }\n\n        .neon-glow {\n          text-shadow: 0 0 5px currentColor, 0 0 10px currentColor,\n            0 0 15px currentColor;\n        }\n\n        .glitch {\n          position: relative;\n        }\n\n        .glitch:hover::before,\n        .glitch:hover::after {\n          content: attr(data-text);\n          position: absolute;\n          top: 0;\n          left: 0;\n          width: 100%;\n          height: 100%;\n        }\n\n        .glitch:hover::before {\n          animation: glitch-1 0.3s infinite;\n          color: #ff00ff;\n          z-index: -1;\n        }\n\n        .glitch:hover::after {\n          animation: glitch-2 0.3s infinite;\n          color: #00ffff;\n          z-index: -2;\n        }\n\n        @keyframes glitch-1 {\n          0%,\n          14%,\n          15%,\n          49%,\n          50%,\n          99%,\n          100% {\n            transform: translate(0);\n          }\n          15%,\n          49% {\n            transform: translate(-2px, 0);\n          }\n        }\n\n        @keyframes glitch-2 {\n          0%,\n          20%,\n          21%,\n          62%,\n          63%,\n          99%,\n          100% {\n            transform: translate(0);\n          }\n          21%,\n          62% {\n            transform: translate(2px, 0);\n          }\n        }\n      `}</style>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AALA;;;;;;;AAaA,MAAM,WAAsB;IAC1B;QAAE,MAAM;QAAS,OAAO;QAAQ,WAAW;IAA2B;IACtE;QAAE,MAAM;QAAU,OAAO;QAAS,WAAW;IAA4B;IACzE;QAAE,MAAM;QAAW,OAAO;QAAU,WAAW;IAA6B;IAC5E;QACE,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QAAE,MAAM;QAAS,OAAO;QAAQ,WAAW;IAA2B;IACtE;QACE,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,MAAM;QACN,OAAO;QACP,WAAW;IACb;CACD;AAMc,SAAS,OAAO,EAAE,SAAS,EAAe;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,qBAAqB,CACzB,GACA;QAEA,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,EAAE,cAAc;YAChB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,IAAI,QAAQ;gBACV,OAAO,cAAc,CAAC;oBACpB,UAAU;oBACV,OAAO;gBACT;gBACA,oBAAoB;YACtB;QACF;IACF;IAEA,qBACE,8OAAC;mDACY,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4GACA,aAAa,gBAAgB,eAC7B;;0BAGF,8OAAC;gBAEC,MAAK;gBACL,cAAW;0DAFD;;kCAIV,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,cAAW;8CACZ;;;;;;;;;;;0CAMH,8OAAC;gCAAwC,MAAK;0EAAhC;0CACX,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;wCAAmB,MAAK;;kDACtB,KAAK,IAAI,CAAC,UAAU,CAAC,qBACpB,8OAAC;4CACC,MAAM,KAAK,IAAI;4CAEf,MAAK;4CACL,cAAY,KAAK,SAAS;4CAC1B,SAAS,CAAC,IAAM,mBAAmB,GAAG,KAAK,IAAI;sFAHrC;sDAKT,KAAK,KAAK;;;;;iEAGb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,MAAK;4CACL,cAAY,KAAK,SAAS;sDAEzB,KAAK,KAAK;;;;;;uCAlBR,KAAK,IAAI;;;;;;;;;;0CA0BtB,8OAAC;gCAEC,cAAW;gCACX,iBAAe;gCACf,SAAS,IAAM,oBAAoB,CAAC;gCACpC,MAAK;0EAJK;0CAMT,iCACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;oCAAU,eAAY;;;;;yDAEnC,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;oCAAU,eAAY;;;;;;;;;;;;;;;;;oBAM3C,kCACC,8OAAC;wBAEC,MAAK;wBACL,mBAAgB;kEAFN;kCAIV,cAAA,8OAAC;sEAAa;sCACX,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;oCAAmB,MAAK;;8CACtB,KAAK,IAAI,CAAC,UAAU,CAAC,qBACpB,8OAAC;wCACC,MAAM,KAAK,IAAI;wCAEf,MAAK;wCACL,cAAY,KAAK,SAAS;wCAC1B,SAAS,CAAC,IAAM,mBAAmB,GAAG,KAAK,IAAI;kFAHrC;kDAKT,KAAK,KAAK;;;;;6DAGb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,MAAK;wCACL,cAAY,KAAK,SAAS;wCAC1B,SAAS,IAAM,oBAAoB;kDAElC,KAAK,KAAK;;;;;;mCAnBR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwIlC", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/Footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Mail, Linkedin, Instagram, Github } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface FooterProps {\n  className?: string;\n}\n\nconst quickLinks = [\n  { href: \"#about\", label: \"About Me\" },\n  { href: \"#skills\", label: \"Skills\" },\n  { href: \"#projects\", label: \"Projects\" },\n  { href: \"#contact\", label: \"Contact\" },\n];\n\nconst expertise = [\n  { label: \"Accounting & Finance\", color: \"bg-cyan-400\" },\n  { label: \"Graphic Design\", color: \"bg-pink-400\" },\n  { label: \"Data Analysis\", color: \"bg-green-400\" },\n  { label: \"Web Development\", color: \"bg-purple-400\" },\n];\n\nconst socialLinks = [\n  {\n    href: \"mailto:<EMAIL>\",\n    icon: Mail,\n    label: \"Email\",\n  },\n  {\n    href: \"https://www.linkedin.com/in/muhammad-trinanda/\",\n    icon: Linkedin,\n    label: \"LinkedIn\",\n  },\n  {\n    href: \"https://www.instagram.com/trinanda321\",\n    icon: Instagram,\n    label: \"Instagram\",\n  },\n  {\n    href: \"https://github.com/trinanda\",\n    icon: Github,\n    label: \"GitHub\",\n  },\n];\n\nexport default function Footer({ className }: FooterProps) {\n  const handleSmoothScroll = (\n    e: React.MouseEvent<HTMLAnchorElement>,\n    href: string\n  ) => {\n    if (href.startsWith(\"#\")) {\n      e.preventDefault();\n      const target = document.querySelector(href);\n      if (target) {\n        target.scrollIntoView({\n          behavior: \"smooth\",\n          block: \"start\",\n        });\n      }\n    }\n  };\n\n  return (\n    <footer\n      className={cn(\n        \"bg-black/90 border-t border-cyan-500/30 mt-0 mb-0\",\n        className\n      )}\n      style={{\n        marginTop: \"0 !important\",\n        marginBottom: \"0 !important\",\n        paddingBottom: \"0 !important\",\n      }}\n    >\n      <div className=\"container mx-auto px-6 py-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Brand Section */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-2xl font-bold text-cyan-400 neon-glow font-mono\">\n              &lt;TRINANDA/&gt;\n            </h3>\n            <p className=\"text-gray-400 leading-relaxed\">\n              Final-Semester Student of Sharia Accounting, UINSU | Accountant,\n              Graphic Designer, and Business Data Analyst\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <a\n                    key={social.label}\n                    href={social.href}\n                    target={\n                      social.href.startsWith(\"mailto:\") ? undefined : \"_blank\"\n                    }\n                    rel={\n                      social.href.startsWith(\"mailto:\")\n                        ? undefined\n                        : \"noopener noreferrer\"\n                    }\n                    className=\"social-link\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"w-5 h-5\" />\n                  </a>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <h4 className=\"text-lg font-semibold text-pink-400 neon-glow\">\n              Quick Links\n            </h4>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.href}>\n                  <a\n                    href={link.href}\n                    className=\"footer-link\"\n                    onClick={(e) => handleSmoothScroll(e, link.href)}\n                  >\n                    {link.label}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Skills */}\n          <div className=\"space-y-4\">\n            <h4 className=\"text-lg font-semibold text-green-400 neon-glow\">\n              Expertise\n            </h4>\n            <ul className=\"space-y-2 text-gray-400\">\n              {expertise.map((skill, index) => (\n                <li key={index} className=\"flex items-center space-x-2\">\n                  <span\n                    className={cn(\n                      \"w-2 h-2 rounded-full pulse-neon\",\n                      skill.color\n                    )}\n                  />\n                  <span>{skill.label}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-gray-700/50 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            &copy; 2025 Muhammad Trinanda. All rights reserved. |{\" \"}\n            <span className=\"text-cyan-400 neon-glow\">Powered by Next.js</span>\n          </p>\n          <p className=\"text-sm text-gray-500 mt-2\">\n            &quot;The future belongs to those who believe in the beauty of their\n            dreams.&quot;\n          </p>\n        </div>\n      </div>\n\n      <style jsx>{`\n        footer {\n          margin-bottom: 0 !important;\n          padding-bottom: 0 !important;\n          margin-top: 0 !important;\n          position: relative;\n          border-top: 1px solid rgba(6, 182, 212, 0.3) !important;\n        }\n\n        footer::after {\n          content: \"\";\n          display: block;\n          height: 0;\n          margin: 0;\n          padding: 0;\n          clear: both;\n        }\n\n        footer::before {\n          content: \"\";\n          display: block;\n          height: 0;\n          margin: 0;\n          padding: 0;\n        }\n\n        #playground + footer,\n        section:last-of-type + footer {\n          margin-top: 0 !important;\n          padding-top: 2rem !important;\n        }\n\n        .social-link {\n          @apply text-gray-400 hover:text-cyan-400 transition-all duration-300 p-2 rounded-lg;\n          background: rgba(0, 255, 255, 0.1);\n          border: 1px solid rgba(0, 255, 255, 0.2);\n        }\n\n        .social-link:hover {\n          background: rgba(0, 255, 255, 0.2);\n          box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);\n          transform: translateY(-2px);\n        }\n\n        .footer-link {\n          @apply text-gray-400 hover:text-cyan-400 transition-colors duration-300;\n          font-family: \"Orbitron\", monospace;\n        }\n\n        .footer-link:hover {\n          text-shadow: 0 0 10px currentColor;\n        }\n\n        .neon-glow {\n          text-shadow:\n            0 0 5px currentColor,\n            0 0 10px currentColor,\n            0 0 15px currentColor;\n        }\n\n        .pulse-neon {\n          animation: pulse-neon 2s infinite;\n        }\n\n        @keyframes pulse-neon {\n          0%,\n          100% {\n            opacity: 1;\n            box-shadow: 0 0 5px currentColor;\n          }\n          50% {\n            opacity: 0.5;\n            box-shadow:\n              0 0 10px currentColor,\n              0 0 15px currentColor;\n          }\n        }\n      `}</style>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AACA;AAHA;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAU,OAAO;IAAW;IACpC;QAAE,MAAM;QAAW,OAAO;IAAS;IACnC;QAAE,MAAM;QAAa,OAAO;IAAW;IACvC;QAAE,MAAM;QAAY,OAAO;IAAU;CACtC;AAED,MAAM,YAAY;IAChB;QAAE,OAAO;QAAwB,OAAO;IAAc;IACtD;QAAE,OAAO;QAAkB,OAAO;IAAc;IAChD;QAAE,OAAO;QAAiB,OAAO;IAAe;IAChD;QAAE,OAAO;QAAmB,OAAO;IAAgB;CACpD;AAED,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,4MAAA,CAAA,YAAS;QACf,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;CACD;AAEc,SAAS,OAAO,EAAE,SAAS,EAAe;IACvD,MAAM,qBAAqB,CACzB,GACA;QAEA,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,EAAE,cAAc;YAChB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,IAAI,QAAQ;gBACV,OAAO,cAAc,CAAC;oBACpB,UAAU;oBACV,OAAO;gBACT;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAKC,OAAO;YACL,WAAW;YACX,cAAc;YACd,eAAe;QACjB;mDARW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;;0BAQF,8OAAC;0DAAc;;kCACb,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;kDAAuD;;;;;;kDAGrE,8OAAC;kFAAY;kDAAgC;;;;;;kDAI7C,8OAAC;kFAAc;kDACZ,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,OAAO,OAAO,IAAI;4CACxB,qBACE,8OAAC;gDAEC,MAAM,OAAO,IAAI;gDACjB,QACE,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,YAAY;gDAElD,KACE,OAAO,IAAI,CAAC,UAAU,CAAC,aACnB,YACA;gDAGN,cAAY,OAAO,KAAK;0FADd;0DAGV,cAAA,8OAAC;8FAAe;;;;;;+CAbX,OAAO,KAAK;;;;;wCAgBvB;;;;;;;;;;;;0CAKJ,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;kDAAgD;;;;;;kDAG9D,8OAAC;kFAAa;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;;0DACC,cAAA,8OAAC;oDACC,MAAM,KAAK,IAAI;oDAEf,SAAS,CAAC,IAAM,mBAAmB,GAAG,KAAK,IAAI;8FADrC;8DAGT,KAAK,KAAK;;;;;;+CANN,KAAK,IAAI;;;;;;;;;;;;;;;;0CAcxB,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;kDAAiD;;;;;;kDAG/D,8OAAC;kFAAa;kDACX,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC;0FAAyB;;kEACxB,8OAAC;mGACY,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mCACA,MAAM,KAAK;;;;;;kEAGf,8OAAC;;kEAAM,MAAM,KAAK;;;;;;;+CAPX;;;;;;;;;;;;;;;;;;;;;;kCAejB,8OAAC;kEAAc;;0CACb,8OAAC;0EAAY;;oCAAgB;oCAC2B;kDACtD,8OAAC;kFAAe;kDAA0B;;;;;;;;;;;;0CAE5C,8OAAC;0EAAY;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFpD", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/BabylonBackground.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface BabylonParticleSystemInstance {\n  particleSystem?: {\n    emitRate: number;\n  };\n  engine?: {\n    resize: () => void;\n  };\n  setColorScheme: (scheme: string) => void;\n  destroy: () => void;\n}\n\nexport interface BabylonBackgroundProps {\n  particleCount?: number;\n  enableInteraction?: boolean;\n  colorScheme?: \"cyberpunk\" | \"matrix\" | \"neon\";\n  className?: string;\n}\n\nexport default function BabylonBackground({\n  particleCount = 1000,\n  enableInteraction = true,\n  colorScheme = \"cyberpunk\",\n  className,\n}: BabylonBackgroundProps) {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const particleSystemRef = useRef<BabylonParticleSystemInstance | null>(null);\n\n  useEffect(() => {\n    let mounted = true;\n\n    const initializeBabylon = async () => {\n      try {\n        // Dynamic import to avoid SSR issues\n        const { default: BabylonParticleSystem } = await import(\n          \"@/scripts/babylon-particles\"\n        );\n\n        if (!mounted || !containerRef.current || !canvasRef.current) return;\n\n        // Initialize particle system\n        const particles = new BabylonParticleSystem(\n          canvasRef.current\n        ) as BabylonParticleSystemInstance;\n        particleSystemRef.current = particles;\n\n        // Configure particle system\n        if (particles.particleSystem) {\n          particles.particleSystem.emitRate = Math.max(50, particleCount / 20);\n        }\n\n        // Set color scheme\n        particles.setColorScheme(colorScheme);\n\n        // Enable interaction if requested (simplified for now)\n        if (enableInteraction) {\n          console.log(\"Interaction enabled for particle system\");\n        }\n      } catch (error) {\n        console.error(\"Error loading Babylon.js particle system:\", error);\n      }\n    };\n\n    initializeBabylon();\n\n    return () => {\n      mounted = false;\n      if (particleSystemRef.current) {\n        particleSystemRef.current.destroy();\n        particleSystemRef.current = null;\n      }\n    };\n  }, [particleCount, enableInteraction, colorScheme]);\n\n  // Handle window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (particleSystemRef.current && particleSystemRef.current.engine) {\n        particleSystemRef.current.engine.resize();\n      }\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  return (\n    <div\n      ref={containerRef}\n      className={cn(\"fixed inset-0 -z-10 pointer-events-none\", className)}\n    >\n      <canvas\n        ref={canvasRef}\n        className=\"w-full h-full block\"\n        style={{ touchAction: \"none\" }}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAuBe,SAAS,kBAAkB,EACxC,gBAAgB,IAAI,EACpB,oBAAoB,IAAI,EACxB,cAAc,WAAW,EACzB,SAAS,EACc;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwC;IAEvE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;QAEd,MAAM,oBAAoB;YACxB,IAAI;gBACF,qCAAqC;gBACrC,MAAM,EAAE,SAAS,qBAAqB,EAAE,GAAG;gBAI3C,IAAI,CAAC,WAAW,CAAC,aAAa,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;gBAE7D,6BAA6B;gBAC7B,MAAM,YAAY,IAAI,sBACpB,UAAU,OAAO;gBAEnB,kBAAkB,OAAO,GAAG;gBAE5B,4BAA4B;gBAC5B,IAAI,UAAU,cAAc,EAAE;oBAC5B,UAAU,cAAc,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,IAAI,gBAAgB;gBACnE;gBAEA,mBAAmB;gBACnB,UAAU,cAAc,CAAC;gBAEzB,uDAAuD;gBACvD,IAAI,mBAAmB;oBACrB,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;QACF;QAEA;QAEA,OAAO;YACL,UAAU;YACV,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,kBAAkB,OAAO,CAAC,OAAO;gBACjC,kBAAkB,OAAO,GAAG;YAC9B;QACF;IACF,GAAG;QAAC;QAAe;QAAmB;KAAY;IAElD,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,OAAO,CAAC,MAAM,EAAE;gBACjE,kBAAkB,OAAO,CAAC,MAAM,CAAC,MAAM;YACzC;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;kBAEzD,cAAA,8OAAC;YACC,KAAK;YACL,WAAU;YACV,OAAO;gBAAE,aAAa;YAAO;;;;;;;;;;;AAIrC", "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 752, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitive from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Switch({\n  className,\n  ...props\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\n  return (\n    <SwitchPrimitive.Root\n      data-slot=\"switch\"\n      className={cn(\n        \"peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <SwitchPrimitive.Thumb\n        data-slot=\"switch-thumb\"\n        className={cn(\n          \"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\"\n        )}\n      />\n    </SwitchPrimitive.Root>\n  )\n}\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,kKAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Slider({\n  className,\n  defaultValue,\n  value,\n  min = 0,\n  max = 100,\n  ...props\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\n  const _values = React.useMemo(\n    () =>\n      Array.isArray(value)\n        ? value\n        : Array.isArray(defaultValue)\n          ? defaultValue\n          : [min, max],\n    [value, defaultValue, min, max]\n  )\n\n  return (\n    <SliderPrimitive.Root\n      data-slot=\"slider\"\n      defaultValue={defaultValue}\n      value={value}\n      min={min}\n      max={max}\n      className={cn(\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    >\n      <SliderPrimitive.Track\n        data-slot=\"slider-track\"\n        className={cn(\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\n        )}\n      >\n        <SliderPrimitive.Range\n          data-slot=\"slider-range\"\n          className={cn(\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\n          )}\n        />\n      </SliderPrimitive.Track>\n      {Array.from({ length: _values.length }, (_, index) => (\n        <SliderPrimitive.Thumb\n          data-slot=\"slider-thumb\"\n          key={index}\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\n        />\n      ))}\n    </SliderPrimitive.Root>\n  )\n}\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;IAClD,MAAM,UAAU,qMAAA,CAAA,UAAa,CAC3B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;YAAC;YAAK;SAAI,EAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/Advanced3DShowcase.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef, useState } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface Advanced3DShowcaseProps {\n  className?: string;\n  enableWebXR?: boolean;\n  enableAudioReactive?: boolean;\n  showPerformanceStats?: boolean;\n}\n\nexport default function Advanced3DShowcase({\n  className,\n  enableWebXR = false,\n  enableAudioReactive = false,\n  showPerformanceStats = false,\n}: Advanced3DShowcaseProps) {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const showcaseRef = useRef<any>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [webXRSupported, setWebXRSupported] = useState(false);\n  const [performanceStats, setPerformanceStats] = useState({\n    fps: 0,\n    drawCalls: 0,\n    triangles: 0,\n  });\n\n  useEffect(() => {\n    let mounted = true;\n\n    const initializeShowcase = async () => {\n      try {\n        setIsLoading(true);\n\n        // Dynamic import to avoid SSR issues\n        const { default: Advanced3DShowcaseEngine } = await import(\n          \"@/scripts/advanced-3d-showcase\"\n        );\n\n        if (!mounted || !containerRef.current || !canvasRef.current) return;\n\n        // Initialize the advanced 3D showcase\n        const showcase = new Advanced3DShowcaseEngine(canvasRef.current, {\n          enableWebXR,\n          enableAudioReactive,\n          showPerformanceStats,\n        });\n\n        showcaseRef.current = showcase;\n\n        // Check WebXR support\n        if (enableWebXR && 'xr' in navigator) {\n          try {\n            const isSupported = await (navigator as any).xr.isSessionSupported('immersive-ar');\n            setWebXRSupported(isSupported);\n          } catch (error) {\n            console.log(\"WebXR not supported:\", error);\n            setWebXRSupported(false);\n          }\n        }\n\n        // Setup performance monitoring\n        if (showPerformanceStats) {\n          const updateStats = () => {\n            if (showcase.getPerformanceStats) {\n              const stats = showcase.getPerformanceStats();\n              setPerformanceStats(stats);\n            }\n          };\n\n          const statsInterval = setInterval(updateStats, 1000);\n          return () => clearInterval(statsInterval);\n        }\n\n        setIsLoading(false);\n        console.log(\"Advanced 3D Showcase initialized successfully\");\n      } catch (error) {\n        console.error(\"Error loading Advanced 3D Showcase:\", error);\n        setIsLoading(false);\n      }\n    };\n\n    initializeShowcase();\n\n    return () => {\n      mounted = false;\n      if (showcaseRef.current) {\n        showcaseRef.current.destroy();\n        showcaseRef.current = null;\n      }\n    };\n  }, [enableWebXR, enableAudioReactive, showPerformanceStats]);\n\n  // Handle window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (showcaseRef.current && showcaseRef.current.engine) {\n        showcaseRef.current.engine.resize();\n      }\n    };\n\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  const handleWebXRToggle = async () => {\n    if (showcaseRef.current && showcaseRef.current.toggleWebXR) {\n      try {\n        await showcaseRef.current.toggleWebXR();\n      } catch (error) {\n        console.error(\"Error toggling WebXR:\", error);\n      }\n    }\n  };\n\n  const handleAudioToggle = () => {\n    if (showcaseRef.current && showcaseRef.current.toggleAudioReactive) {\n      showcaseRef.current.toggleAudioReactive();\n    }\n  };\n\n  return (\n    <div\n      ref={containerRef}\n      className={cn(\"relative w-full h-full overflow-hidden\", className)}\n    >\n      {/* Loading overlay */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-black/80 flex items-center justify-center z-20\">\n          <div className=\"text-center space-y-4\">\n            <div className=\"w-12 h-12 border-4 border-cyan-400 border-t-transparent rounded-full animate-spin mx-auto\"></div>\n            <p className=\"text-cyan-400 font-mono\">Loading Advanced 3D Experience...</p>\n          </div>\n        </div>\n      )}\n\n      {/* Performance stats overlay */}\n      {showPerformanceStats && !isLoading && (\n        <div className=\"absolute top-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg p-3 text-xs font-mono text-cyan-400 z-10\">\n          <div>FPS: {performanceStats.fps}</div>\n          <div>Draw Calls: {performanceStats.drawCalls}</div>\n          <div>Triangles: {performanceStats.triangles.toLocaleString()}</div>\n        </div>\n      )}\n\n      {/* Control panel */}\n      {!isLoading && (\n        <div className=\"absolute top-4 right-4 space-y-2 z-10\">\n          {enableWebXR && webXRSupported && (\n            <button\n              onClick={handleWebXRToggle}\n              className=\"block w-full px-4 py-2 bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/50 rounded-lg text-cyan-400 text-sm font-mono transition-colors\"\n            >\n              Toggle AR/VR\n            </button>\n          )}\n          \n          {enableAudioReactive && (\n            <button\n              onClick={handleAudioToggle}\n              className=\"block w-full px-4 py-2 bg-pink-500/20 hover:bg-pink-500/30 border border-pink-500/50 rounded-lg text-pink-400 text-sm font-mono transition-colors\"\n            >\n              Audio Reactive\n            </button>\n          )}\n        </div>\n      )}\n\n      {/* Main canvas */}\n      <canvas\n        ref={canvasRef}\n        className=\"w-full h-full block\"\n        style={{ touchAction: \"none\" }}\n      />\n\n      {/* Feature indicators */}\n      <div className=\"absolute bottom-4 left-4 flex space-x-2 z-10\">\n        {enableWebXR && (\n          <div className={cn(\n            \"px-2 py-1 rounded text-xs font-mono\",\n            webXRSupported \n              ? \"bg-green-500/20 text-green-400 border border-green-500/50\" \n              : \"bg-red-500/20 text-red-400 border border-red-500/50\"\n          )}>\n            WebXR {webXRSupported ? \"Ready\" : \"Not Supported\"}\n          </div>\n        )}\n        \n        {enableAudioReactive && (\n          <div className=\"px-2 py-1 rounded text-xs font-mono bg-purple-500/20 text-purple-400 border border-purple-500/50\">\n            Audio Reactive\n          </div>\n        )}\n        \n        <div className=\"px-2 py-1 rounded text-xs font-mono bg-blue-500/20 text-blue-400 border border-blue-500/50\">\n          PBR Materials\n        </div>\n        \n        <div className=\"px-2 py-1 rounded text-xs font-mono bg-yellow-500/20 text-yellow-400 border border-yellow-500/50\">\n          Advanced Post-FX\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYe,SAAS,mBAAmB,EACzC,SAAS,EACT,cAAc,KAAK,EACnB,sBAAsB,KAAK,EAC3B,uBAAuB,KAAK,EACJ;IACxB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAChC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,KAAK;QACL,WAAW;QACX,WAAW;IACb;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;QAEd,MAAM,qBAAqB;YACzB,IAAI;gBACF,aAAa;gBAEb,qCAAqC;gBACrC,MAAM,EAAE,SAAS,wBAAwB,EAAE,GAAG;gBAI9C,IAAI,CAAC,WAAW,CAAC,aAAa,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;gBAE7D,sCAAsC;gBACtC,MAAM,WAAW,IAAI,yBAAyB,UAAU,OAAO,EAAE;oBAC/D;oBACA;oBACA;gBACF;gBAEA,YAAY,OAAO,GAAG;gBAEtB,sBAAsB;gBACtB,IAAI,eAAe,QAAQ,WAAW;oBACpC,IAAI;wBACF,MAAM,cAAc,MAAM,AAAC,UAAkB,EAAE,CAAC,kBAAkB,CAAC;wBACnE,kBAAkB;oBACpB,EAAE,OAAO,OAAO;wBACd,QAAQ,GAAG,CAAC,wBAAwB;wBACpC,kBAAkB;oBACpB;gBACF;gBAEA,+BAA+B;gBAC/B,IAAI,sBAAsB;oBACxB,MAAM,cAAc;wBAClB,IAAI,SAAS,mBAAmB,EAAE;4BAChC,MAAM,QAAQ,SAAS,mBAAmB;4BAC1C,oBAAoB;wBACtB;oBACF;oBAEA,MAAM,gBAAgB,YAAY,aAAa;oBAC/C,OAAO,IAAM,cAAc;gBAC7B;gBAEA,aAAa;gBACb,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,aAAa;YACf;QACF;QAEA;QAEA,OAAO;YACL,UAAU;YACV,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,OAAO;gBAC3B,YAAY,OAAO,GAAG;YACxB;QACF;IACF,GAAG;QAAC;QAAa;QAAqB;KAAqB;IAE3D,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,MAAM,EAAE;gBACrD,YAAY,OAAO,CAAC,MAAM,CAAC,MAAM;YACnC;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,WAAW,EAAE;YAC1D,IAAI;gBACF,MAAM,YAAY,OAAO,CAAC,WAAW;YACvC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,mBAAmB,EAAE;YAClE,YAAY,OAAO,CAAC,mBAAmB;QACzC;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;YAGvD,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAA0B;;;;;;;;;;;;;;;;;YAM5C,wBAAwB,CAAC,2BACxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BAAI;4BAAM,iBAAiB,GAAG;;;;;;;kCAC/B,8OAAC;;4BAAI;4BAAa,iBAAiB,SAAS;;;;;;;kCAC5C,8OAAC;;4BAAI;4BAAY,iBAAiB,SAAS,CAAC,cAAc;;;;;;;;;;;;;YAK7D,CAAC,2BACA,8OAAC;gBAAI,WAAU;;oBACZ,eAAe,gCACd,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;oBAKF,qCACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAQP,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,aAAa;gBAAO;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,uCACA,iBACI,8DACA;;4BACH;4BACM,iBAAiB,UAAU;;;;;;;oBAIrC,qCACC,8OAAC;wBAAI,WAAU;kCAAmG;;;;;;kCAKpH,8OAAC;wBAAI,WAAU;kCAA6F;;;;;;kCAI5G,8OAAC;wBAAI,WAAU;kCAAmG;;;;;;;;;;;;;;;;;;AAM1H", "debugId": null}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/sections/Advanced3DSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef, useEffect } from \"react\";\nimport { motion, useInView } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Label } from \"@/components/ui/label\";\nimport { Slider } from \"@/components/ui/slider\";\nimport Advanced3DShowcase from \"@/components/Advanced3DShowcase\";\nimport { \n  Cpu, \n  Zap, \n  Eye, \n  Palette, \n  Volume2, \n  VolumeX, \n  Glasses,\n  BarChart3,\n  Sparkles,\n  Layers,\n  Lightbulb,\n  Atom\n} from \"lucide-react\";\n\nexport default function Advanced3DSection() {\n  const [activeTab, setActiveTab] = useState(\"showcase\");\n  const [webXREnabled, setWebXREnabled] = useState(false);\n  const [audioReactiveEnabled, setAudioReactiveEnabled] = useState(false);\n  const [performanceStatsEnabled, setPerformanceStatsEnabled] = useState(true);\n  const [postProcessingIntensity, setPostProcessingIntensity] = useState([0.5]);\n  \n  const sectionRef = useRef<HTMLDivElement>(null);\n  const isInView = useInView(sectionRef, { once: true, margin: \"-100px\" });\n\n  const features = [\n    {\n      icon: <Atom className=\"w-6 h-6\" />,\n      title: \"PBR Materials\",\n      description: \"Physically Based Rendering with metallic, glass, iridescent, and clearcoat materials\",\n      color: \"from-blue-500 to-cyan-500\"\n    },\n    {\n      icon: <Sparkles className=\"w-6 h-6\" />,\n      title: \"Advanced Post-Processing\",\n      description: \"HDR rendering, bloom, chromatic aberration, depth of field, and screen-space reflections\",\n      color: \"from-purple-500 to-pink-500\"\n    },\n    {\n      icon: <Glasses className=\"w-6 h-6\" />,\n      title: \"WebXR Support\",\n      description: \"Virtual and Augmented Reality experiences with hand tracking and hit testing\",\n      color: \"from-green-500 to-emerald-500\"\n    },\n    {\n      icon: <Volume2 className=\"w-6 h-6\" />,\n      title: \"Audio Reactive\",\n      description: \"Real-time audio analysis driving particle systems and visual effects\",\n      color: \"from-orange-500 to-red-500\"\n    },\n    {\n      icon: <Zap className=\"w-6 h-6\" />,\n      title: \"GPU Particles\",\n      description: \"High-performance GPU-based particle systems with 50,000+ particles\",\n      color: \"from-yellow-500 to-orange-500\"\n    },\n    {\n      icon: <BarChart3 className=\"w-6 h-6\" />,\n      title: \"Performance Monitoring\",\n      description: \"Real-time FPS, draw calls, and triangle count monitoring\",\n      color: \"from-indigo-500 to-purple-500\"\n    }\n  ];\n\n  const materialTypes = [\n    { name: \"Chrome\", description: \"Highly reflective metallic surface\", color: \"#C0C0C0\" },\n    { name: \"Gold\", description: \"Precious metal with warm reflections\", color: \"#FFD700\" },\n    { name: \"Glass\", description: \"Transparent refractive material\", color: \"#87CEEB\" },\n    { name: \"Carbon Fiber\", description: \"Anisotropic brushed material\", color: \"#2F2F2F\" },\n    { name: \"Iridescent\", description: \"Color-shifting interference coating\", color: \"#FF69B4\" },\n    { name: \"Marble\", description: \"Subsurface scattering stone\", color: \"#F5F5DC\" }\n  ];\n\n  const postProcessingEffects = [\n    { name: \"Bloom\", description: \"Glowing highlights and light bleeding\" },\n    { name: \"Tone Mapping\", description: \"HDR to LDR conversion with ACES\" },\n    { name: \"FXAA\", description: \"Fast approximate anti-aliasing\" },\n    { name: \"Chromatic Aberration\", description: \"Color fringing for cinematic effect\" },\n    { name: \"Film Grain\", description: \"Animated noise for vintage look\" },\n    { name: \"Vignette\", description: \"Darkened edges for focus\" },\n    { name: \"Depth of Field\", description: \"Selective focus blur\" },\n    { name: \"Screen Space Reflections\", description: \"Real-time reflections\" }\n  ];\n\n  return (\n    <section \n      ref={sectionRef}\n      className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-20\"\n    >\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-5xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-6\">\n            Advanced 3D Technology\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            Cutting-edge Babylon.js implementation featuring PBR materials, advanced post-processing, \n            WebXR capabilities, and audio-reactive visualizations for professional 3D experiences.\n          </p>\n        </motion.div>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-4 mb-8 bg-black/20 backdrop-blur-sm\">\n            <TabsTrigger value=\"showcase\" className=\"data-[state=active]:bg-cyan-500/20\">\n              3D Showcase\n            </TabsTrigger>\n            <TabsTrigger value=\"features\" className=\"data-[state=active]:bg-purple-500/20\">\n              Features\n            </TabsTrigger>\n            <TabsTrigger value=\"materials\" className=\"data-[state=active]:bg-pink-500/20\">\n              Materials\n            </TabsTrigger>\n            <TabsTrigger value=\"controls\" className=\"data-[state=active]:bg-green-500/20\">\n              Controls\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"showcase\" className=\"space-y-8\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95 }}\n              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.95 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              <Card className=\"bg-black/40 backdrop-blur-sm border-cyan-500/20 overflow-hidden\">\n                <CardHeader>\n                  <CardTitle className=\"text-2xl text-cyan-400 flex items-center gap-2\">\n                    <Lightbulb className=\"w-6 h-6\" />\n                    Interactive 3D Experience\n                  </CardTitle>\n                  <CardDescription className=\"text-gray-300\">\n                    Experience advanced Babylon.js features with real-time rendering, PBR materials, and interactive controls.\n                  </CardDescription>\n                </CardHeader>\n                <CardContent className=\"p-0\">\n                  <div className=\"h-[600px] relative\">\n                    <Advanced3DShowcase\n                      enableWebXR={webXREnabled}\n                      enableAudioReactive={audioReactiveEnabled}\n                      showPerformanceStats={performanceStatsEnabled}\n                      className=\"w-full h-full\"\n                    />\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          </TabsContent>\n\n          <TabsContent value=\"features\" className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {features.map((feature, index) => (\n                <motion.div\n                  key={feature.title}\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                >\n                  <Card className=\"bg-black/40 backdrop-blur-sm border-gray-700/50 hover:border-cyan-500/50 transition-all duration-300 h-full\">\n                    <CardHeader>\n                      <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center text-white mb-4`}>\n                        {feature.icon}\n                      </div>\n                      <CardTitle className=\"text-xl text-white\">{feature.title}</CardTitle>\n                      <CardDescription className=\"text-gray-300\">\n                        {feature.description}\n                      </CardDescription>\n                    </CardHeader>\n                  </Card>\n                </motion.div>\n              ))}\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"materials\" className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {materialTypes.map((material, index) => (\n                <motion.div\n                  key={material.name}\n                  initial={{ opacity: 0, x: -30 }}\n                  animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                >\n                  <Card className=\"bg-black/40 backdrop-blur-sm border-gray-700/50 hover:border-purple-500/50 transition-all duration-300\">\n                    <CardHeader className=\"pb-3\">\n                      <div className=\"flex items-center gap-3\">\n                        <div \n                          className=\"w-8 h-8 rounded-full border-2 border-white/20\"\n                          style={{ backgroundColor: material.color }}\n                        />\n                        <div>\n                          <CardTitle className=\"text-lg text-white\">{material.name}</CardTitle>\n                          <CardDescription className=\"text-sm text-gray-400\">\n                            {material.description}\n                          </CardDescription>\n                        </div>\n                      </div>\n                    </CardHeader>\n                  </Card>\n                </motion.div>\n              ))}\n            </div>\n\n            <Card className=\"bg-black/40 backdrop-blur-sm border-purple-500/20\">\n              <CardHeader>\n                <CardTitle className=\"text-xl text-purple-400 flex items-center gap-2\">\n                  <Layers className=\"w-5 h-5\" />\n                  Post-Processing Effects\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {postProcessingEffects.map((effect, index) => (\n                    <div key={effect.name} className=\"flex items-center gap-3 p-3 rounded-lg bg-white/5\">\n                      <Badge variant=\"outline\" className=\"border-purple-500/50 text-purple-400\">\n                        {effect.name}\n                      </Badge>\n                      <span className=\"text-sm text-gray-300\">{effect.description}</span>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"controls\" className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <Card className=\"bg-black/40 backdrop-blur-sm border-green-500/20\">\n                <CardHeader>\n                  <CardTitle className=\"text-xl text-green-400\">Experience Settings</CardTitle>\n                  <CardDescription>Configure advanced 3D features</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <Label htmlFor=\"webxr-toggle\" className=\"text-white flex items-center gap-2\">\n                      <Glasses className=\"w-4 h-4\" />\n                      WebXR (AR/VR)\n                    </Label>\n                    <Switch\n                      id=\"webxr-toggle\"\n                      checked={webXREnabled}\n                      onCheckedChange={setWebXREnabled}\n                    />\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <Label htmlFor=\"audio-toggle\" className=\"text-white flex items-center gap-2\">\n                      {audioReactiveEnabled ? <Volume2 className=\"w-4 h-4\" /> : <VolumeX className=\"w-4 h-4\" />}\n                      Audio Reactive\n                    </Label>\n                    <Switch\n                      id=\"audio-toggle\"\n                      checked={audioReactiveEnabled}\n                      onCheckedChange={setAudioReactiveEnabled}\n                    />\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <Label htmlFor=\"stats-toggle\" className=\"text-white flex items-center gap-2\">\n                      <BarChart3 className=\"w-4 h-4\" />\n                      Performance Stats\n                    </Label>\n                    <Switch\n                      id=\"stats-toggle\"\n                      checked={performanceStatsEnabled}\n                      onCheckedChange={setPerformanceStatsEnabled}\n                    />\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card className=\"bg-black/40 backdrop-blur-sm border-blue-500/20\">\n                <CardHeader>\n                  <CardTitle className=\"text-xl text-blue-400\">Visual Settings</CardTitle>\n                  <CardDescription>Adjust post-processing intensity</CardDescription>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  <div className=\"space-y-3\">\n                    <Label className=\"text-white flex items-center gap-2\">\n                      <Eye className=\"w-4 h-4\" />\n                      Post-Processing Intensity\n                    </Label>\n                    <Slider\n                      value={postProcessingIntensity}\n                      onValueChange={setPostProcessingIntensity}\n                      max={1}\n                      min={0}\n                      step={0.1}\n                      className=\"w-full\"\n                    />\n                    <div className=\"text-sm text-gray-400 text-center\">\n                      {Math.round(postProcessingIntensity[0] * 100)}%\n                    </div>\n                  </div>\n\n                  <div className=\"pt-4 border-t border-gray-700\">\n                    <p className=\"text-sm text-gray-400 leading-relaxed\">\n                      Adjust the intensity of bloom, chromatic aberration, and other post-processing effects \n                      to customize the visual experience.\n                    </p>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </TabsContent>\n        </Tabs>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;;AA2Be,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAI;IAE5E,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAM,QAAQ;IAAS;IAEtE,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,kNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAU,aAAa;YAAsC,OAAO;QAAU;QACtF;YAAE,MAAM;YAAQ,aAAa;YAAwC,OAAO;QAAU;QACtF;YAAE,MAAM;YAAS,aAAa;YAAmC,OAAO;QAAU;QAClF;YAAE,MAAM;YAAgB,aAAa;YAAgC,OAAO;QAAU;QACtF;YAAE,MAAM;YAAc,aAAa;YAAuC,OAAO;QAAU;QAC3F;YAAE,MAAM;YAAU,aAAa;YAA+B,OAAO;QAAU;KAChF;IAED,MAAM,wBAAwB;QAC5B;YAAE,MAAM;YAAS,aAAa;QAAwC;QACtE;YAAE,MAAM;YAAgB,aAAa;QAAkC;QACvE;YAAE,MAAM;YAAQ,aAAa;QAAiC;QAC9D;YAAE,MAAM;YAAwB,aAAa;QAAsC;QACnF;YAAE,MAAM;YAAc,aAAa;QAAkC;QACrE;YAAE,MAAM;YAAY,aAAa;QAA2B;QAC5D;YAAE,MAAM;YAAkB,aAAa;QAAuB;QAC9D;YAAE,MAAM;YAA4B,aAAa;QAAwB;KAC1E;IAED,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,WAAW;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC/D,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAkH;;;;;;sCAGhI,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CAAqC;;;;;;8CAG7E,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CAAuC;;;;;;8CAG/E,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;8CAAqC;;;;;;8CAG9E,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CAAsC;;;;;;;;;;;;sCAKhF,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAK;gCACnC,SAAS,WAAW;oCAAE,SAAS;oCAAG,OAAO;gCAAE,IAAI;oCAAE,SAAS;oCAAG,OAAO;gCAAK;gCACzE,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,4MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGnC,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DAAgB;;;;;;;;;;;;sDAI7C,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wIAAA,CAAA,UAAkB;oDACjB,aAAa;oDACb,qBAAqB;oDACrB,sBAAsB;oDACtB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQtB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS,WAAW;4CAAE,SAAS;4CAAG,GAAG;wCAAE,IAAI;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC/D,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;kDAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC;wDAAI,WAAW,CAAC,sCAAsC,EAAE,QAAQ,KAAK,CAAC,iDAAiD,CAAC;kEACtH,QAAQ,IAAI;;;;;;kEAEf,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsB,QAAQ,KAAK;;;;;;kEACxD,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,QAAQ,WAAW;;;;;;;;;;;;;;;;;uCAZrB,QAAQ,KAAK;;;;;;;;;;;;;;;sCAqB1B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;;8CACvC,8OAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS,WAAW;gDAAE,SAAS;gDAAG,GAAG;4CAAE,IAAI;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAChE,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;sDAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;oDAAC,WAAU;8DACpB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,SAAS,KAAK;gEAAC;;;;;;0EAE3C,8OAAC;;kFACC,8OAAC,gIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAsB,SAAS,IAAI;;;;;;kFACxD,8OAAC,gIAAA,CAAA,kBAAe;wEAAC,WAAU;kFACxB,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAf1B,SAAS,IAAI;;;;;;;;;;8CAyBxB,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIlC,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,sBAAsB,GAAG,CAAC,CAAC,QAAQ,sBAClC,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAChC,OAAO,IAAI;;;;;;0EAEd,8OAAC;gEAAK,WAAU;0EAAyB,OAAO,WAAW;;;;;;;uDAJnD,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAY/B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAyB;;;;;;kEAC9C,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAe,WAAU;;kFACtC,8OAAC,wMAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAGjC,8OAAC,kIAAA,CAAA,SAAM;gEACL,IAAG;gEACH,SAAS;gEACT,iBAAiB;;;;;;;;;;;;kEAIrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAe,WAAU;;oEACrC,qCAAuB,8OAAC,4MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;6FAAe,8OAAC,4MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAa;;;;;;;0EAG5F,8OAAC,kIAAA,CAAA,SAAM;gEACL,IAAG;gEACH,SAAS;gEACT,iBAAiB;;;;;;;;;;;;kEAIrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAe,WAAU;;kFACtC,8OAAC,kNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAGnC,8OAAC,kIAAA,CAAA,SAAM;gEACL,IAAG;gEACH,SAAS;gEACT,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;kDAMzB,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;;kEACT,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAwB;;;;;;kEAC7C,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAEnB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;;kFACf,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAY;;;;;;;0EAG7B,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;gEACP,eAAe;gEACf,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;0EAEZ,8OAAC;gEAAI,WAAU;;oEACZ,KAAK,KAAK,CAAC,uBAAuB,CAAC,EAAE,GAAG;oEAAK;;;;;;;;;;;;;kEAIlD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;sEAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAazE", "debugId": null}}, {"offset": {"line": 2170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/ProjectCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { ExternalLink, Github, Eye } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { Card, CardContent } from '@/components/ui/card'\n\ninterface ProjectData {\n  id: string\n  title: string\n  description: string\n  image?: string\n  category: 'dashboard' | 'design' | 'analysis' | 'web' | 'accounting' | 'visualization'\n  tags: string[]\n  links: {\n    demo?: string\n    github?: string\n    live?: string\n  }\n}\n\ninterface ProjectCardProps {\n  project: ProjectData\n  index?: number\n  className?: string\n}\n\n// Category colors for projects\nconst categoryColors = {\n  dashboard: 'from-blue-500 to-cyan-500',\n  design: 'from-pink-500 to-rose-500',\n  analysis: 'from-green-500 to-emerald-500',\n  web: 'from-purple-500 to-violet-500',\n  accounting: 'from-yellow-500 to-orange-500',\n  visualization: 'from-indigo-500 to-blue-500'\n} as const\n\nconst categoryEmojis = {\n  dashboard: '📊',\n  design: '🎨',\n  analysis: '📈',\n  web: '💻',\n  accounting: '💰',\n  visualization: '📉'\n} as const\n\nexport default function ProjectCard({ \n  project, \n  index = 0, \n  className \n}: ProjectCardProps) {\n  const [imageError, setImageError] = useState(false)\n  const gradientClass = categoryColors[project.category] || categoryColors.dashboard\n  const emoji = categoryEmojis[project.category] || categoryEmojis.dashboard\n\n  return (\n    <Card \n      className={cn(\n        'project-card group bg-gray-900/50 backdrop-blur-sm border border-cyan-500/30 rounded-xl overflow-hidden hover:border-cyan-400/60 transition-all duration-300 hover:shadow-xl hover:shadow-cyan-400/20 hover:-translate-y-2',\n        className\n      )}\n      style={{ animationDelay: `${index * 0.2}s` }}\n    >\n      {/* Project Image */}\n      <div className=\"relative overflow-hidden h-48 bg-gradient-to-br from-gray-800 to-gray-900\">\n        {project.image && !imageError ? (\n          <Image\n            src={project.image}\n            alt={`${project.title} project screenshot`}\n            fill\n            className=\"object-cover group-hover:scale-110 transition-transform duration-500\"\n            onError={() => setImageError(true)}\n          />\n        ) : (\n          <div className={cn('w-full h-full bg-gradient-to-br flex items-center justify-center', gradientClass)}>\n            <div className=\"text-6xl opacity-20\">\n              {emoji}\n            </div>\n          </div>\n        )}\n        \n        {/* Category Badge */}\n        <div className=\"absolute top-4 left-4\">\n          <span className={cn('px-3 py-1 text-xs font-semibold text-white bg-gradient-to-r rounded-full shadow-lg', gradientClass)}>\n            {project.category.toUpperCase()}\n          </span>\n        </div>\n      </div>\n      \n      {/* Project Content */}\n      <CardContent className=\"p-6 space-y-4\">\n        <header>\n          <h3 \n            id={`project-${project.id}`}\n            className=\"text-xl font-bold text-white group-hover:text-cyan-400 transition-colors\"\n          >\n            {project.title}\n          </h3>\n        </header>\n        \n        <p className=\"text-gray-300 text-sm leading-relaxed line-clamp-3\">\n          {project.description}\n        </p>\n        \n        {/* Tags */}\n        <div className=\"flex flex-wrap gap-2\" role=\"list\" aria-label=\"Project technologies\">\n          {project.tags.map((tag) => (\n            <span \n              key={tag}\n              className=\"px-2 py-1 text-xs bg-cyan-400/10 text-cyan-400 rounded border border-cyan-400/20 hover:bg-cyan-400/20 transition-colors\"\n              role=\"listitem\"\n            >\n              {tag}\n            </span>\n          ))}\n        </div>\n        \n        {/* Action Links */}\n        <div className=\"flex gap-3 pt-2\" role=\"group\" aria-label=\"Project links\">\n          {project.links.demo && (\n            <Link \n              href={project.links.demo}\n              className=\"flex-1 bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium text-center hover:from-cyan-400 hover:to-blue-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-gray-900 flex items-center justify-center gap-2\"\n              aria-label={`View ${project.title} demo`}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <Eye className=\"w-4 h-4\" />\n              View Demo\n            </Link>\n          )}\n          \n          {project.links.github && (\n            <Link \n              href={project.links.github}\n              className=\"px-4 py-2 border border-gray-600 text-gray-300 rounded-lg text-sm font-medium hover:border-gray-500 hover:text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-gray-900 flex items-center gap-2\"\n              aria-label={`View ${project.title} source code on GitHub`}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <Github className=\"w-4 h-4\" />\n              GitHub\n            </Link>\n          )}\n          \n          {project.links.live && (\n            <Link \n              href={project.links.live}\n              className=\"px-4 py-2 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-lg text-sm font-medium hover:from-pink-400 hover:to-rose-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-pink-400 focus:ring-offset-2 focus:ring-offset-gray-900 flex items-center gap-2\"\n              aria-label={`Visit live ${project.title} website`}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n            >\n              <ExternalLink className=\"w-4 h-4\" />\n              Live Site\n            </Link>\n          )}\n        </div>\n      </CardContent>\n\n      <style jsx>{`\n        .project-card {\n          opacity: 0;\n          transform: translateY(30px);\n          animation: fadeInUp 0.8s ease-out forwards;\n        }\n        \n        @keyframes fadeInUp {\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n        \n        .line-clamp-3 {\n          display: -webkit-box;\n          -webkit-line-clamp: 3;\n          -webkit-box-orient: vertical;\n          overflow: hidden;\n        }\n      `}</style>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;;AA6BA,+BAA+B;AAC/B,MAAM,iBAAiB;IACrB,WAAW;IACX,QAAQ;IACR,UAAU;IACV,KAAK;IACL,YAAY;IACZ,eAAe;AACjB;AAEA,MAAM,iBAAiB;IACrB,WAAW;IACX,QAAQ;IACR,UAAU;IACV,KAAK;IACL,YAAY;IACZ,eAAe;AACjB;AAEe,SAAS,YAAY,EAClC,OAAO,EACP,QAAQ,CAAC,EACT,SAAS,EACQ;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,gBAAgB,cAAc,CAAC,QAAQ,QAAQ,CAAC,IAAI,eAAe,SAAS;IAClF,MAAM,QAAQ,cAAc,CAAC,QAAQ,QAAQ,CAAC,IAAI,eAAe,SAAS;IAE1E,qBACE,8OAAC,gIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8NACA;QAEF,OAAO;YAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;QAAC;;0BAG3C,8OAAC;0DAAc;;oBACZ,QAAQ,KAAK,IAAI,CAAC,2BACjB,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,KAAK;wBAClB,KAAK,GAAG,QAAQ,KAAK,CAAC,mBAAmB,CAAC;wBAC1C,IAAI;wBACJ,WAAU;wBACV,SAAS,IAAM,cAAc;;;;;6CAG/B,8OAAC;mEAAe,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;kCACrF,cAAA,8OAAC;sEAAc;sCACZ;;;;;;;;;;;kCAMP,8OAAC;kEAAc;kCACb,cAAA,8OAAC;uEAAgB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sFAAsF;sCACvG,QAAQ,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;0BAMnC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;;kCACC,cAAA,8OAAC;4BACC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;sEACjB;sCAET,QAAQ,KAAK;;;;;;;;;;;kCAIlB,8OAAC;kEAAY;kCACV,QAAQ,WAAW;;;;;;kCAItB,8OAAC;wBAAqC,MAAK;wBAAO,cAAW;kEAA9C;kCACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,oBACjB,8OAAC;gCAGC,MAAK;0EADK;0CAGT;+BAJI;;;;;;;;;;kCAUX,8OAAC;wBAAgC,MAAK;wBAAQ,cAAW;kEAA1C;;4BACZ,QAAQ,KAAK,CAAC,IAAI,kBACjB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,QAAQ,KAAK,CAAC,IAAI;gCACxB,WAAU;gCACV,cAAY,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,KAAK,CAAC;gCACxC,QAAO;gCACP,KAAI;;kDAEJ,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAK9B,QAAQ,KAAK,CAAC,MAAM,kBACnB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,QAAQ,KAAK,CAAC,MAAM;gCAC1B,WAAU;gCACV,cAAY,CAAC,KAAK,EAAE,QAAQ,KAAK,CAAC,sBAAsB,CAAC;gCACzD,QAAO;gCACP,KAAI;;kDAEJ,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAKjC,QAAQ,KAAK,CAAC,IAAI,kBACjB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,QAAQ,KAAK,CAAC,IAAI;gCACxB,WAAU;gCACV,cAAY,CAAC,WAAW,EAAE,QAAQ,KAAK,CAAC,QAAQ,CAAC;gCACjD,QAAO;gCACP,KAAI;;kDAEJ,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BlD", "debugId": null}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/components/SkillCard.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useState } from 'react'\nimport { cn } from '@/lib/utils'\nimport { Card, CardContent } from '@/components/ui/card'\n\ninterface SkillData {\n  name: string\n  level: number\n  category: 'accounting' | 'design' | 'data' | 'web' | 'tools' | 'soft'\n}\n\ninterface SkillCardProps {\n  skill: SkillData\n  index?: number\n  className?: string\n}\n\n// Category colors mapping\nconst categoryColors = {\n  accounting: 'from-green-400 to-emerald-600',\n  design: 'from-pink-400 to-rose-600', \n  data: 'from-blue-400 to-indigo-600',\n  web: 'from-cyan-400 to-teal-600',\n  tools: 'from-yellow-400 to-orange-600',\n  soft: 'from-purple-400 to-violet-600'\n} as const\n\nconst categoryIcons = {\n  accounting: '💰',\n  design: '🎨',\n  data: '📊',\n  web: '💻',\n  tools: '🛠️',\n  soft: '🤝'\n} as const\n\nexport default function SkillCard({ \n  skill, \n  index = 0, \n  className \n}: SkillCardProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [progressWidth, setProgressWidth] = useState(0)\n  const cardRef = useRef<HTMLDivElement>(null)\n  \n  const gradientClass = categoryColors[skill.category] || categoryColors.data\n  const icon = categoryIcons[skill.category] || categoryIcons.data\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            setIsVisible(true)\n            // Animate progress bar after a short delay\n            setTimeout(() => {\n              setProgressWidth(skill.level)\n            }, 200)\n            observer.unobserve(entry.target)\n          }\n        })\n      },\n      {\n        threshold: 0.3,\n        rootMargin: '0px 0px -50px 0px'\n      }\n    )\n\n    if (cardRef.current) {\n      observer.observe(cardRef.current)\n    }\n\n    return () => {\n      if (cardRef.current) {\n        observer.unobserve(cardRef.current)\n      }\n    }\n  }, [skill.level])\n\n  return (\n    <Card \n      ref={cardRef}\n      className={cn(\n        'skill-card bg-gray-900/50 backdrop-blur-sm border border-cyan-500/30 rounded-lg hover:border-cyan-400/60 transition-all duration-300 hover:shadow-lg hover:shadow-cyan-400/20',\n        isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-5',\n        className\n      )}\n      style={{ animationDelay: `${index * 0.1}s` }}\n      role=\"article\"\n      aria-labelledby={`skill-${skill.name.replace(/\\s+/g, '-').toLowerCase()}`}\n    >\n      <CardContent className=\"p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center gap-3\">\n            <span \n              className=\"text-2xl\" \n              role=\"img\" \n              aria-label={`${skill.category} category`}\n            >\n              {icon}\n            </span>\n            <h3 \n              id={`skill-${skill.name.replace(/\\s+/g, '-').toLowerCase()}`}\n              className=\"text-lg font-semibold text-white\"\n            >\n              {skill.name}\n            </h3>\n          </div>\n          <span \n            className=\"text-sm font-mono text-cyan-400 bg-cyan-400/10 px-2 py-1 rounded\"\n            aria-label={`Skill level: ${skill.level} percent`}\n          >\n            {skill.level}%\n          </span>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <div className=\"flex justify-between text-sm text-gray-400\">\n            <span>Progress</span>\n            <span>{skill.level}%</span>\n          </div>\n          <div \n            className=\"w-full bg-gray-700 rounded-full h-2 overflow-hidden\"\n            role=\"progressbar\"\n            aria-valuenow={skill.level}\n            aria-valuemin={0}\n            aria-valuemax={100}\n            aria-label={`${skill.name} skill progress: ${skill.level}%`}\n          >\n            <div \n              className={cn(\n                'h-full bg-gradient-to-r rounded-full transition-all duration-1000 ease-out',\n                gradientClass\n              )}\n              style={{ width: `${progressWidth}%` }}\n            />\n          </div>\n        </div>\n      </CardContent>\n\n      <style jsx>{`\n        .skill-card {\n          opacity: 0;\n          transform: translateY(20px);\n        }\n        \n        .animate-fade-in-up {\n          animation: fadeInUp 0.6s ease-out forwards;\n        }\n        \n        @keyframes fadeInUp {\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n      `}</style>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;;AAkBA,0BAA0B;AAC1B,MAAM,iBAAiB;IACrB,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,KAAK;IACL,OAAO;IACP,MAAM;AACR;AAEA,MAAM,gBAAgB;IACpB,YAAY;IACZ,QAAQ;IACR,MAAM;IACN,KAAK;IACL,OAAO;IACP,MAAM;AACR;AAEe,SAAS,UAAU,EAChC,KAAK,EACL,QAAQ,CAAC,EACT,SAAS,EACM;IACf,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,MAAM,gBAAgB,cAAc,CAAC,MAAM,QAAQ,CAAC,IAAI,eAAe,IAAI;IAC3E,MAAM,OAAO,aAAa,CAAC,MAAM,QAAQ,CAAC,IAAI,cAAc,IAAI;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,QAAQ,OAAO,CAAC,CAAC;gBACf,IAAI,MAAM,cAAc,EAAE;oBACxB,aAAa;oBACb,2CAA2C;oBAC3C,WAAW;wBACT,iBAAiB,MAAM,KAAK;oBAC9B,GAAG;oBACH,SAAS,SAAS,CAAC,MAAM,MAAM;gBACjC;YACF;QACF,GACA;YACE,WAAW;YACX,YAAY;QACd;QAGF,IAAI,QAAQ,OAAO,EAAE;YACnB,SAAS,OAAO,CAAC,QAAQ,OAAO;QAClC;QAEA,OAAO;YACL,IAAI,QAAQ,OAAO,EAAE;gBACnB,SAAS,SAAS,CAAC,QAAQ,OAAO;YACpC;QACF;IACF,GAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QACH,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iLACA,YAAY,uBAAuB,2BACnC;QAEF,OAAO;YAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;QAAC;QAC3C,MAAK;QACL,mBAAiB,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW,IAAI;;0BAEzE,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;wCAEC,MAAK;wCACL,cAAY,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC;kFAF9B;kDAIT;;;;;;kDAEH,8OAAC;wCACC,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW,IAAI;kFAClD;kDAET,MAAM,IAAI;;;;;;;;;;;;0CAGf,8OAAC;gCAEC,cAAY,CAAC,aAAa,EAAE,MAAM,KAAK,CAAC,QAAQ,CAAC;0EADvC;;oCAGT,MAAM,KAAK;oCAAC;;;;;;;;;;;;;kCAIjB,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;;kDAAK;;;;;;kDACN,8OAAC;;;4CAAM,MAAM,KAAK;4CAAC;;;;;;;;;;;;;0CAErB,8OAAC;gCAEC,MAAK;gCACL,iBAAe,MAAM,KAAK;gCAC1B,iBAAe;gCACf,iBAAe;gCACf,cAAY,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,MAAM,KAAK,CAAC,CAAC,CAAC;0EALjD;0CAOV,cAAA,8OAAC;oCAKC,OAAO;wCAAE,OAAO,GAAG,cAAc,CAAC,CAAC;oCAAC;+EAJzB,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8EACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BhB", "debugId": null}}]}