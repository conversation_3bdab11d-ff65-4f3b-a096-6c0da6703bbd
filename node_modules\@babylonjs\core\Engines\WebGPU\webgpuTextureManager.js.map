{"version": 3, "file": "webgpuTextureManager.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuTextureManager.ts"], "names": [], "mappings": "AAAA,yDAAyD;AACzD,wCAAwC;AACxC,wCAAwC;AACxC,0CAA0C;AAC1C,EAAE;AACF,+BAA+B;AAC/B,EAAE;AACF,+EAA+E;AAC/E,gFAAgF;AAChF,+EAA+E;AAC/E,4EAA4E;AAC5E,wEAAwE;AACxE,2DAA2D;AAE3D,6EAA6E;AAC7E,sDAAsD;AAEtD,6EAA6E;AAC7E,2EAA2E;AAC3E,8EAA8E;AAC9E,yEAAyE;AACzE,gFAAgF;AAChF,gFAAgF;AAChF,YAAY;AACZ,OAAO,KAAK,eAAe,MAAM,mBAAmB,CAAC;AAErD,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAMzC,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAGhE,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAG5D,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAG9E,iEAAiE;AAEjE,MAAM,kBAAkB,GAAG;;;;;;;;;;;KAWtB,CAAC;AAEN,MAAM,oBAAoB,GAAG;;;;;;;;;;KAUxB,CAAC;AAEN,MAAM,mCAAmC,GAAG;;;;;;;;;;;;;;;;;KAiBvC,CAAC;AAEN,MAAM,qCAAqC,GAAG;;;;;;;;;;;;;;;;;;;KAmBzC,CAAC;AAEN,MAAM,2CAA2C,GAAG,mCAAmC,CAAC;AAExF,MAAM,6CAA6C,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BjD,CAAC;AAEN,MAAM,iBAAiB,GAAG;;;;;;;KAOrB,CAAC;AAEN,MAAM,mBAAmB,GAAG;;;;;;;;KAQvB,CAAC;AAEN,MAAM,8BAA8B,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8BlC,CAAC;AAEN,MAAM,gCAAgC,GAAG;;;;;;;;;;KAUpC,CAAC;AAEN,MAAM,uCAAuC,GAAG;;;;;;;;;;KAU3C,CAAC;AAEN,IAAK,YAKJ;AALD,WAAK,YAAY;IACb,mDAAU,CAAA;IACV,qFAA2B,CAAA;IAC3B,iDAAS,CAAA;IACT,qGAAmC,CAAA;AACvC,CAAC,EALI,YAAY,KAAZ,YAAY,QAKhB;AAED,IAAK,iBAGJ;AAHD,WAAK,iBAAiB;IAClB,uEAAe,CAAA;IACf,+DAAW,CAAA;AACf,CAAC,EAHI,iBAAiB,KAAjB,iBAAiB,QAGrB;AAOD,MAAM,sBAAsB,GAAG;IAC3B,EAAE,MAAM,EAAE,kBAAkB,EAAE,QAAQ,EAAE,oBAAoB,EAAE;IAC9D,EAAE,MAAM,EAAE,mCAAmC,EAAE,QAAQ,EAAE,qCAAqC,EAAE;IAChG,EAAE,MAAM,EAAE,iBAAiB,EAAE,QAAQ,EAAE,mBAAmB,EAAE;IAC5D,EAAE,MAAM,EAAE,2CAA2C,EAAE,QAAQ,EAAE,6CAA6C,EAAE;CACnH,CAAC;AAEF;;;GAGG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAA+B;IACtE,EAAE,EAAE,CAAC;IACL,OAAO,EAAE,CAAC;IACV,MAAM,EAAE,CAAC;IACT,MAAM,EAAE,CAAC;IAET,OAAO,EAAE,CAAC;IACV,OAAO,EAAE,CAAC;IACV,QAAQ,EAAE,CAAC;IACX,QAAQ,EAAE,CAAC;IACX,OAAO,EAAE,CAAC;IACV,OAAO,EAAE,CAAC;IAEV,OAAO,EAAE,EAAE;IACX,OAAO,EAAE,EAAE;IACX,QAAQ,EAAE,EAAE;IACZ,QAAQ,EAAE,EAAE;IACZ,QAAQ,EAAE,EAAE;IACZ,SAAS,EAAE,EAAE;IACb,UAAU,EAAE,EAAE;IACd,iBAAiB,EAAE,EAAE;IACrB,SAAS,EAAE,EAAE;IACb,SAAS,EAAE,EAAE;IACb,UAAU,EAAE,EAAE;IACd,iBAAiB,EAAE,EAAE;IAErB,WAAW,EAAE,EAAE;IACf,YAAY,EAAE,EAAE;IAChB,4GAA4G;IAE5G,QAAQ,EAAE,EAAE;IACZ,QAAQ,EAAE,EAAE;IACZ,SAAS,EAAE,EAAE;IACb,UAAU,EAAE,EAAE;IACd,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IAEf,UAAU,EAAE,EAAE;IACd,UAAU,EAAE,EAAE;IACd,WAAW,EAAE,EAAE;IAEf,QAAQ,EAAE,EAAE;IACZ,YAAY,EAAE,EAAE;IAChB,WAAW,EAAE,EAAE;IACf,sBAAsB,EAAE,EAAE;IAC1B,YAAY,EAAE,EAAE;IAEhB,uBAAuB,EAAE,EAAE;IAE3B,QAAQ,EAAE,EAAE;IACZ,SAAS,EAAE,EAAE;IACb,WAAW,EAAE,EAAE;IACf,QAAQ,EAAE,EAAE;IACZ,SAAS,EAAE,EAAE;IACb,WAAW,EAAE,EAAE;CAClB,CAAC;AAEF,gBAAgB;AAChB,MAAM,OAAO,oBAAoB;IAc7B,gFAAgF;IAChF,mDAAmD;IACnD,gFAAgF;IAEhF,YAAY,MAAoB,EAAE,MAAiB,EAAE,aAAkC,EAAE,iBAAmC;QAXpH,eAAU,GAAyE,EAAE,CAAC;QACtF,qBAAgB,GAAwB,EAAE,CAAC;QAC3C,oBAAe,GAAyE,EAAE,CAAC;QAC3F,0BAAqB,GAAwB,EAAE,CAAC;QAChD,6BAAwB,GAAmF,EAAE,CAAC;QAQlH,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,IAAI,iBAAiB,CAAC,OAAO,sFAAqD,KAAK,CAAC,CAAC,EAAE,CAAC;YACxF,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YACzD,8BAA8B,mEAA6C,GAAG,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5I,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,EAAE,SAAS,kDAAmC,EAAE,CAAC,CAAC;QAC7F,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,EAAE,SAAS,kDAAmC,EAAE,CAAC,CAAC;QAC5F,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CACnD,CAAC,GAAG,CAAC,EACL,eAAe,CAAC,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EACzE,kBAAkB,CACrB,CAAC,kBAAkB,CAAC;QAErB,IAAI,CAAC,YAAY,6DAA0C,CAAC;QAC5D,IAAI,CAAC,iBAAiB,6DAA0C,CAAC;IACrE,CAAC;IAEO,YAAY,CAAC,MAAwB,EAAE,OAAqB,YAAY,CAAC,MAAM,EAAE,MAA4B;QACjH,MAAM,KAAK,GACP,IAAI,KAAK,YAAY,CAAC,MAAM;YACxB,CAAC,CAAC,CAAC,IAAI,CAAC;YACR,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,uBAAuB;gBAC7C,CAAC,CAAC,CAAC,CAAC,MAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,MAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC9E,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK;oBAC3B,CAAC,CAAC,CAAC,IAAI,CAAC;oBACR,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,+BAA+B;wBACrD,CAAC,CAAC,CAAC,CAAC,MAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,MAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;wBAC9E,CAAC,CAAC,CAAC,CAAC;QAElB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;QACpD,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,IAAI,KAAK,YAAY,CAAC,uBAAuB,IAAI,IAAI,KAAK,YAAY,CAAC,+BAA+B,EAAE,CAAC;gBACzG,IAAI,MAAO,CAAC,OAAO,EAAE,CAAC;oBAClB,OAAO,IAAI,mBAAmB,CAAC;gBACnC,CAAC;gBACD,IAAI,MAAO,CAAC,gBAAgB,EAAE,CAAC;oBAC3B,OAAO,IAAI,4BAA4B,CAAC;gBAC5C,CAAC;YACL,CAAC;YAED,IAAI,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,IAAI,UAAU,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;gBACrD,IAAI,YAAY,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC;gBAEzD,MAAM,gBAAgB,GAAwB;oBAC1C,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;oBAC5B,eAAe,EAAE,IAAI;oBACrB,UAAU,EAAE,KAAK;oBACjB,4BAA4B,EAAE,IAAI;oBAClC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,6BAAqB;oBAChE,sBAAsB,EAAE,IAAI;oBAC5B,iBAAiB,EAAE,EAAE;oBACrB,oBAAoB,EAAE,EAAE;oBACxB,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE;oBAChD,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;oBAC7C,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,2BAA2B,8BAAsB,IAAI,CAAC;oBACtF,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe;oBAC7C,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB;iBAC5D,CAAC;gBAEF,UAAU,CAAC,gBAAgB,CAAC,CAAC;gBAE7B,4CAA4C;gBAC3C,gBAAgB,CAAC,SAAuC,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAE1E,OAAO,CACH,UAAU,EACV,gBAAgB,EAChB,CAAC,kBAAkB,EAAE,EAAE;oBACnB,UAAU,GAAG,kBAAkB,CAAC;gBACpC,CAAC,EACD,IAAI,CAAC,OAAO,CACf,CAAC;gBAEF,gBAAgB,CAAC,UAAU,GAAG,IAAI,CAAC;gBAEnC,OAAO,CACH,YAAY,EACZ,gBAAgB,EAChB,CAAC,oBAAoB,EAAE,EAAE;oBACrB,YAAY,GAAG,oBAAoB,CAAC;gBACxC,CAAC,EACD,IAAI,CAAC,OAAO,CACf,CAAC;gBAEF,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;gBAEnE,UAAU;gBACT,gBAAgB,CAAC,SAAuC,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAE3E,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBACjD,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,yBAAyB,KAAK,EAAE;oBAClF,IAAI,EAAE,KAAK,CAAC,UAAU;iBACzB,CAAC,CAAC;gBACH,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBACnD,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,2BAA2B,KAAK,EAAE;oBACpF,IAAI,EAAE,KAAK,CAAC,YAAY;iBAC3B,CAAC,CAAC;gBACH,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;YAC5E,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,qBAAqB,MAAM,IAAI,KAAK,EAAE;gBACxF,MAAM,kDAAqC;gBAC3C,MAAM,EAAE;oBACJ,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;oBAClB,UAAU,EAAE,MAAM;iBACrB;gBACD,QAAQ,EAAE;oBACN,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;oBAClB,UAAU,EAAE,MAAM;oBAClB,OAAO,EAAE;wBACL;4BACI,MAAM;yBACT;qBACJ;iBACJ;gBACD,SAAS,EAAE;oBACP,QAAQ,wEAAiD;oBACzD,gBAAgB,mDAAoC;iBACvD;aACJ,CAAC,CAAC;YAEH,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;QACjG,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,iBAAiB,CAAC,MAAwB,EAAE,OAA0B,iBAAiB,CAAC,WAAW;QACvG,MAAM,KAAK,GAAG,IAAI,KAAK,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9D,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QACtC,CAAC;QAED,IAAI,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,IAAI,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAChD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBACjD,IAAI,EAAE,8BAA8B;oBACpC,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,kCAAkC;iBACvF,CAAC,CAAC;gBACH,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;oBACnD,IAAI,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,gCAAgC,CAAC,CAAC,CAAC,uCAAuC;oBAC9F,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,sCAAsC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,EAAE;iBACpI,CAAC,CAAC;gBACH,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;YACjF,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;gBAC/C,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,0BAA0B,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,EAAE;gBAC/H,MAAM,kDAAqC;gBAC3C,MAAM,EAAE;oBACJ,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;oBAClB,UAAU,EAAE,MAAM;iBACrB;gBACD,QAAQ,EAAE;oBACN,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;oBAClB,UAAU,EAAE,MAAM;oBAClB,OAAO,EAAE;wBACL;4BACI,MAAM;yBACT;qBACJ;iBACJ;gBACD,SAAS,EAAE;oBACP,QAAQ,wEAAiD;oBACzD,gBAAgB,mDAAoC;iBACvD;aACJ,CAAC,CAAC;YAEH,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;QACtG,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEM,iBAAiB,CAAC,OAA0B;QAC/C,IAAI,CAAC,0BAA0B,GAAG,OAAO,CAAC;IAC9C,CAAC;IAEM,kBAAkB,CAAC,KAAsB,EAAE,OAAwB,EAAE,MAAwB,EAAE,OAAO,GAAG,KAAK,EAAE,cAAkC;QACrJ,MAAM,oBAAoB,GAAG,cAAc,KAAK,SAAS,CAAC;QAC1D,MAAM,CAAC,QAAQ,EAAE,eAAe,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAExI,IAAI,oBAAoB,EAAE,CAAC;YACvB,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,cAAe,CAAC,cAAc,EAAE,CAAC,mCAAmC,OAAO,EAAE,CAAC,CAAC;QAE/E,MAAM,qBAAqB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAEhF,MAAM,oBAAoB,GAA4B;YAClD,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,uBAAuB,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;YACnK,gBAAgB,EAAE;gBACd;oBACI,IAAI,EAAE,qBAAqB,CAAC,kBAAmB,CAAC,UAAU,CAAC;wBACvD,MAAM;wBACN,SAAS,qDAA0C;wBACnD,aAAa,EAAE,CAAC;wBAChB,cAAc,EAAE,CAAC;wBACjB,YAAY,EAAE,CAAC;wBACf,eAAe,EAAE,CAAC;wBAClB,MAAM,+CAAmC;qBAC5C,CAAC;oBACF,MAAM,0CAA6B;oBACnC,OAAO,6CAA+B;iBACzC;aACJ;SACJ,CAAC;QACF,MAAM,WAAW,GAAG,cAAe,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;QAE1E,MAAM,UAAU,GAA2B;YACvC,MAAM,EAAE,eAAe;YACvB,OAAO,EAAE;gBACL;oBACI,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE,IAAI,CAAC,aAAa;iBAC/B;gBACD;oBACI,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;wBACzC,MAAM,EAAE,KAAK,CAAC,kBAAkB;qBACnC,CAAC;iBACL;aACJ;SACJ,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAE3D,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAClC,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACvC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,WAAW,CAAC,GAAG,EAAE,CAAC;QAElB,cAAe,CAAC,aAAa,EAAE,EAAE,CAAC;QAElC,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAe,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACtD,cAAc,GAAG,IAAW,CAAC;QACjC,CAAC;IACL,CAAC;IAEM,uBAAuB,CAC1B,eAAmD,EACnD,KAAa,EACb,MAAc,EACd,MAAwB,EACxB,OAAO,GAAG,KAAK,EACf,gBAAgB,GAAG,KAAK,EACxB,SAAS,GAAG,CAAC,EACb,QAAQ,GAAG,CAAC,EACZ,MAAM,GAAG,CAAC,EACV,KAAK,GAAG,CAAC,EACT,KAAK,GAAG,CAAC,EACT,SAAS,GAAG,CAAC,EACb,UAAU,GAAG,CAAC,EACd,cAAkC;IAClC,6DAA6D;IAC7D,oBAA8B;QAE9B,MAAM,OAAO,GAAG,SAAS,KAAK,CAAC,CAAC;QAChC,MAAM,oBAAoB,GAAG,cAAc,KAAK,SAAS,CAAC;QAC1D,MAAM,CAAC,QAAQ,EAAE,eAAe,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,+BAA+B,CAAC,CAAC,CAAC,YAAY,CAAC,uBAAuB,EAAE;YACzJ,OAAO;YACP,gBAAgB;SACnB,CAAC,CAAC;QAEH,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAEnC,IAAI,oBAAoB,EAAE,CAAC;YACvB,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,cAAe,CAAC,cAAc,EAAE,CAAC,sCAAsC,OAAO,qBAAqB,gBAAgB,EAAE,CAAC,CAAC;QAEvH,IAAI,UAAgC,CAAC;QACrC,IAAI,mBAAmB,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE,CAAC;YACzD,UAAU,GAAG,eAAe,CAAC,kBAAkB,CAAC;YAChD,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,gBAAgB,IAAI,MAAM,KAAK,CAAC,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;gBACrE,2IAA2I;gBAC3I,eAAe,GAAG,SAAgB,CAAC;YACvC,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,UAAU,GAAG,eAAe,CAAC;YAC7B,eAAe,GAAG,SAAgB,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,YAAY,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/H,CAAC;QAED,MAAM,qBAAqB,GAAG,eAAkD,CAAC;QAEjF,MAAM,aAAa,GACf,qBAAqB,EAAE,uBAAuB;YAC9C,IAAI,CAAC,aAAa,CACd,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,EAC5B,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,CAAC,EACD,cAAc,EACd,qGAAoF,sDAA8C,EAClI,SAAS,EACT,+BAA+B,CAClC,CAAC;QAEN,MAAM,oBAAoB,GAAG,qBAAqB,EAAE,2BAA2B,IAAI;YAC/E,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,4BAA4B,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,IACvH,gBAAgB,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,sBAC5C,EAAE;YACF,gBAAgB,EAAE;gBACd;oBACI,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC;wBAC3B,MAAM;wBACN,SAAS,qDAA0C;wBACnD,YAAY,EAAE,CAAC;wBACf,aAAa,EAAE,CAAC;wBAChB,eAAe,EAAE,CAAC;wBAClB,cAAc,EAAE,CAAC;qBACpB,CAAC;oBACF,MAAM,0CAA6B;oBACnC,OAAO,6CAA+B;iBACzC;aACJ;SACJ,CAAC;QACF,MAAM,WAAW,GAAG,cAAe,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;QAE1E,IAAI,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,qBAAqB,EAAE,6BAA6B,CAAC,CAAC,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;QAC9H,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,MAAM,UAAU,GAA2B;gBACvC,MAAM,EAAE,eAAe;gBACvB,OAAO,EAAE;oBACL;wBACI,OAAO,EAAE,CAAC;wBACV,QAAQ,EAAE,UAAU,CAAC,UAAU,CAAC;4BAC5B,MAAM;4BACN,SAAS,qDAA0C;4BACnD,YAAY,EAAE,QAAQ;4BACtB,aAAa,EAAE,CAAC;4BAChB,eAAe,EAAE,MAAM;4BACvB,cAAc,EAAE,SAAS;yBAC5B,CAAC;qBACL;iBACJ;aACJ,CAAC;YACF,IAAI,OAAO,EAAE,CAAC;gBACV,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;oBACpB,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE;wBACN,MAAM,EAAE,IAAI,CAAC,eAAe;qBAC/B;iBACJ,CAAC,CAAC;YACP,CAAC;YACD,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QACzD,CAAC;QAED,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAClC,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACvC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,WAAW,CAAC,GAAG,EAAE,CAAC;QAElB,cAAe,CAAC,oBAAoB,CAChC;YACI,OAAO,EAAE,aAAa;SACzB,EACD;YACI,OAAO,EAAE,UAAU;YACnB,QAAQ;YACR,MAAM,EAAE;gBACJ,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,SAAS;aACf;SACJ,EACD;YACI,KAAK,EAAE,SAAS,IAAI,KAAK;YACzB,MAAM,EAAE,UAAU,IAAI,MAAM;YAC5B,kBAAkB,EAAE,CAAC;SACxB,CACJ,CAAC;QAEF,IAAI,qBAAqB,EAAE,CAAC;YACxB,qBAAqB,CAAC,uBAAuB,GAAG,aAAa,CAAC;YAC9D,qBAAqB,CAAC,2BAA2B,GAAG,oBAAoB,CAAC;YACzE,IAAI,OAAO,EAAE,CAAC;gBACV,qBAAqB,CAAC,6BAA6B,GAAG,SAAS,CAAC;YACpE,CAAC;iBAAM,CAAC;gBACJ,qBAAqB,CAAC,qBAAqB,GAAG,SAAS,CAAC;YAC5D,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,cAAe,CAAC,aAAa,EAAE,EAAE,CAAC;QAElC,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAe,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACtD,cAAc,GAAG,IAAW,CAAC;QACjC,CAAC;IACL,CAAC;IAED,gFAAgF;IAChF,yCAAyC;IACzC,gFAAgF;IAEzE,aAAa,CAChB,WAA4E,EAC5E,UAAU,GAAG,KAAK,EAClB,eAAe,GAAG,KAAK,EACvB,OAAO,GAAG,KAAK,EACf,gBAAgB,GAAG,KAAK,EACxB,IAAI,GAAG,KAAK,EACZ,oEAAmE,EACnE,WAAW,GAAG,CAAC,EACf,cAAkC,EAClC,KAAK,GAAG,CAAC,CAAC,EACV,gBAAgB,GAAG,CAAC,EACpB,KAAc;QAEd,WAAW,GAAG,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEzD,MAAM,UAAU,GAAI,WAAmB,CAAC,MAAM,IAAI,CAAC,CAAC;QACpD,MAAM,WAAW,GAAG;YAChB,KAAK,EAAE,WAAW,CAAC,KAAK;YACxB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,kBAAkB,EAAE,UAAU;SACjC,CAAC;QAEF,MAAM,oBAAoB,GAAG,8BAA8B,CAAC,MAAM,CAAC,CAAC,CAAC,wDAA+C,CAAC,CAAC,CAAC,CAAC;QACxH,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC1E,MAAM,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzH,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,2FAA2E,sDAA8C,CAAC;QAE9J,gBAAgB,IAAI,UAAU,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,+CAAuC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAExH,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,EAAE,CAAC;YAC/B,4KAA4K;YAC5K,gBAAgB,IAAI,oBAAoB,+CAAuC,CAAC;QACpF,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;YAC1C,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,WAAW,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,MAAM,IACjJ,WAAW,CAAC,kBAChB,IAAI,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,IAAI,MAAM,WAAW,WAAW,EAAE;YACrE,IAAI,EAAE,WAAW;YACjB,SAAS,EAAE,IAAI,CAAC,CAAC,iDAAsC,CAAC,gDAAqC;YAC7F,MAAM;YACN,KAAK,EAAE,MAAM,GAAG,gBAAgB;YAChC,WAAW;YACX,aAAa;SAChB,CAAC,CAAC;QAEH,IAAI,mBAAmB,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE9I,IAAI,UAAU,IAAI,eAAe,EAAE,CAAC;gBAChC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;YACrF,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,iBAAiB,CACpB,YAA+D,EAC/D,UAAU,GAAG,KAAK,EAClB,eAAe,GAAG,KAAK,EACvB,OAAO,GAAG,KAAK,EACf,gBAAgB,GAAG,KAAK,EACxB,oEAAmE,EACnE,WAAW,GAAG,CAAC,EACf,cAAkC,EAClC,KAAK,GAAG,CAAC,CAAC,EACV,gBAAgB,GAAG,CAAC,EACpB,KAAc;QAEd,WAAW,GAAG,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAEzD,MAAM,KAAK,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC;QAChH,MAAM,MAAM,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;QAEnH,MAAM,oBAAoB,GAAG,8BAA8B,CAAC,MAAM,CAAC,CAAC,CAAC,wDAA+C,CAAC,CAAC,CAAC,CAAC;QACxH,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC1E,MAAM,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjG,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,2FAA2E,sDAA8C,CAAC;QAE9J,gBAAgB,IAAI,UAAU,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,+CAAuC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;QAExH,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACtB,4KAA4K;YAC5K,gBAAgB,IAAI,oBAAoB,+CAAuC,CAAC;QACpF,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;YAC1C,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,IAAI,MAAM,MACxG,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAC3B,IAAI,MAAM,WAAW,WAAW,EAAE;YAClC,IAAI,EAAE;gBACF,KAAK;gBACL,MAAM;gBACN,kBAAkB,EAAE,CAAC;aACxB;YACD,SAAS,iDAAsC;YAC/C,MAAM;YACN,KAAK,EAAE,MAAM,GAAG,gBAAgB;YAChC,WAAW;YACX,aAAa;SAChB,CAAC,CAAC;QAEH,IAAI,mBAAmB,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE1G,IAAI,UAAU,IAAI,eAAe,EAAE,CAAC;gBAChC,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;YAChF,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,mBAAmB,CAAC,UAA8C,EAAE,MAAwB,EAAE,aAAqB,EAAE,cAAkC;QAC1J,MAAM,oBAAoB,GAAG,cAAc,KAAK,SAAS,CAAC;QAE1D,IAAI,oBAAoB,EAAE,CAAC;YACvB,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,cAAe,CAAC,cAAc,EAAE,CAAC,yBAAyB,aAAa,SAAS,CAAC,CAAC;QAElF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;QACtF,CAAC;QAED,cAAe,CAAC,aAAa,EAAE,EAAE,CAAC;QAElC,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAe,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACtD,cAAc,GAAG,IAAW,CAAC;QACjC,CAAC;IACL,CAAC;IAEM,eAAe,CAClB,eAAmD,EACnD,MAAwB,EACxB,aAAqB,EACrB,SAAS,GAAG,CAAC,EACb,IAAI,GAAG,KAAK,EACZ,cAAkC;QAElC,MAAM,oBAAoB,GAAG,cAAc,KAAK,SAAS,CAAC;QAC1D,MAAM,CAAC,QAAQ,EAAE,eAAe,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE9D,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAEnC,IAAI,oBAAoB,EAAE,CAAC;YACvB,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,cAAe,CAAC,cAAc,EAAE,CAAC,4BAA4B,SAAS,MAAM,aAAa,SAAS,CAAC,CAAC;QAEpG,IAAI,UAAgC,CAAC;QACrC,IAAI,mBAAmB,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE,CAAC;YACzD,UAAU,GAAG,eAAe,CAAC,kBAAkB,CAAC;YAChD,eAAe,CAAC,yBAAyB,GAAG,eAAe,CAAC,yBAAyB,IAAI,EAAE,CAAC;YAC5F,eAAe,CAAC,mBAAmB,GAAG,eAAe,CAAC,mBAAmB,IAAI,EAAE,CAAC;QACpF,CAAC;aAAM,CAAC;YACJ,UAAU,GAAG,eAAe,CAAC;YAC7B,eAAe,GAAG,SAAgB,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,MAAM,qBAAqB,GAAG,eAAkD,CAAC;QACjF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC;YACrC,MAAM,oBAAoB,GAAG,qBAAqB,EAAE,yBAAyB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI;gBACjG,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,oBAAoB,MAAM,aAAa,SAAS,SAAS,CAAC,EAAE;gBAC9G,gBAAgB,EAAE;oBACd;wBACI,IAAI,EAAE,UAAU,CAAC,UAAU,CAAC;4BACxB,MAAM;4BACN,SAAS,EAAE,IAAI,CAAC,CAAC,qDAA0C,CAAC,oDAAyC;4BACrG,YAAY,EAAE,CAAC;4BACf,aAAa,EAAE,CAAC;4BAChB,eAAe,EAAE,CAAC;4BAClB,cAAc,EAAE,SAAS;yBAC5B,CAAC;wBACF,MAAM,0CAA6B;wBACnC,OAAO,6CAA+B;qBACzC;iBACJ;aACJ,CAAC;YACF,IAAI,qBAAqB,EAAE,CAAC;gBACxB,qBAAqB,CAAC,yBAAyB,CAAC,SAAS,CAAC,GAAG,qBAAqB,CAAC,yBAAyB,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;gBAC9H,qBAAqB,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,oBAAoB,CAAC;YAC7F,CAAC;YACD,MAAM,WAAW,GAAG,cAAe,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;YAE1E,MAAM,SAAS,GACX,qBAAqB,EAAE,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC9D,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;oBACzB,MAAM,EAAE,eAAe;oBACvB,OAAO,EAAE;wBACL;4BACI,OAAO,EAAE,CAAC;4BACV,QAAQ,EAAE,UAAU,CAAC,UAAU,CAAC;gCAC5B,MAAM;gCACN,SAAS,EAAE,IAAI,CAAC,CAAC,qDAA0C,CAAC,oDAAyC;gCACrG,YAAY,EAAE,CAAC,GAAG,CAAC;gCACnB,aAAa,EAAE,CAAC;gCAChB,eAAe,EAAE,CAAC;gCAClB,cAAc,EAAE,SAAS;6BAC5B,CAAC;yBACL;wBACD;4BACI,OAAO,EAAE,CAAC;4BACV,QAAQ,EAAE,IAAI,CAAC,cAAc;yBAChC;qBACJ;iBACJ,CAAC,CAAC;YACP,IAAI,qBAAqB,EAAE,CAAC;gBACxB,qBAAqB,CAAC,mBAAmB,CAAC,SAAS,CAAC,GAAG,qBAAqB,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;gBAClH,qBAAqB,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC;YAC5E,CAAC;YAED,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAClC,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YACvC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B,WAAW,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC;QAED,cAAe,CAAC,aAAa,EAAE,EAAE,CAAC;QAElC,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAe,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACtD,cAAc,GAAG,IAAW,CAAC;QACjC,CAAC;IACL,CAAC;IAEM,kCAAkC,CACrC,OAAwB,EACxB,KAAc,EACd,MAAe,EACf,KAAc,EACd,aAAsB,EACtB,qBAA+B;QAE/B,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC5B,OAAO,CAAC,gBAAgB,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvE,CAAC;QAED,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACtB,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC1B,CAAC;QACD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACvB,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC5B,CAAC;QACD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACtB,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC1B,CAAC;QAED,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;QAC5E,MAAM,gBAAgB,GAAG,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC;QAE/F,iBAAiB,CAAC,MAAM,GAAG,mBAAmB,CAAC,sBAAsB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;QAE5H,iBAAiB,CAAC,aAAa;YAC3B,OAAO,CAAC,OAAO,+CAAuC,IAAI,OAAO,CAAC,MAAM,oDAA4C;gBAChH,CAAC,CAAC,kGAAkF,yDAAgD;gBACpI,CAAC,CAAC,OAAO,CAAC,OAAO,gDAAuC;oBACtD,CAAC,CAAC,4GAA2F;oBAC7F,CAAC,CAAC,CAAC,CAAC,CAAC;QAEf,iBAAiB,CAAC,uBAAuB,GAAG,gBAAgB,CAAC,CAAC,qDAA6C,CAAC,CAAC,CAAC,CAAC;QAE/G,MAAM,UAAU,GAAG,OAAO,CAAC,eAAe,CAAC;QAC3C,MAAM,UAAU,GAAG,KAAK,IAAI,CAAC,CAAC;QAC9B,IAAI,WAAW,CAAC;QAChB,IAAI,OAAO,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAChC,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;QACvC,CAAC;aAAM,CAAC;YACJ,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CACrC,EAAE,KAAK,EAAE,MAAM,EAAE,EACjB,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,OAAO,EACf,KAAK,EACL,iBAAiB,CAAC,MAAM,EACxB,CAAC,EACD,IAAI,CAAC,0BAA0B,EAC/B,iBAAiB,CAAC,aAAa,EAC/B,iBAAiB,CAAC,uBAAuB,EACzC,OAAO,CAAC,KAAK,CAChB,CAAC;YAEF,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAElC,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YACtD,MAAM,MAAM,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAChF,MAAM,MAAM,GAAG,mBAAmB,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,4DAAyC,CAAC,8CAAkC,CAAC;YACrK,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,mEAAgD,CAAC,uDAA0C,CAAC;YAEjI,iBAAiB,CAAC,UAAU,CACxB;gBACI,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,mBAAmB,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,IAAI,MAAM,IACvI,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAC3B,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,OAAO,CAAC,KAAK,IAAI,QAAQ,EAAE;gBAChE,MAAM;gBACN,SAAS;gBACT,aAAa,EAAE,WAAW;gBAC1B,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC;gBACf,eAAe,EAAE,CAAC;gBAClB,MAAM;aACT,EACD,gBAAgB,CACnB,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CACjC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,EACrC,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,eAAe,EACvB,OAAO,CAAC,OAAO,EACf,KAAK,EACL,OAAO,CAAC,IAAI,EACZ,iBAAiB,CAAC,MAAM,EACxB,CAAC,EACD,IAAI,CAAC,0BAA0B,EAC/B,iBAAiB,CAAC,aAAa,EAC/B,iBAAiB,CAAC,uBAAuB,EACzC,OAAO,CAAC,KAAK,CAChB,CAAC;YAEF,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAElC,MAAM,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YACtD,MAAM,MAAM,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAChF,MAAM,MAAM,GAAG,mBAAmB,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,4DAAyC,CAAC,8CAAkC,CAAC;YACrK,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS;gBAC/B,CAAC;gBACD,CAAC,CAAC,OAAO,CAAC,IAAI;oBACZ,CAAC;oBACD,CAAC,oDAAyC,CAAC;YAEjD,iBAAiB,CAAC,UAAU,CACxB;gBACI,KAAK,EAAE,sBAAsB,IAAI,CAAC,OAAO,CAAC,QAAQ,eAAe,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GACvF,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC,EACrD,IAAI,KAAK,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,OAAO,CAAC,KAAK,IAAI,QAAQ,EAAE;gBAC7J,MAAM;gBACN,SAAS;gBACT,aAAa,EAAE,WAAW;gBAC1B,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC;gBACf,eAAe;gBACf,MAAM;aACT,EACD,gBAAgB,CACnB,CAAC;QACN,CAAC;QAED,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1C,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC7C,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAE1C,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEM,iBAAiB,CAAC,OAAwB,EAAE,OAAe,EAAE,eAAe,GAAG,IAAI,EAAE,KAAK,GAAG,CAAC;QACjG,MAAM,iBAAiB,GAAG,OAAO,CAAC,gBAAmD,CAAC;QAEtF,IAAI,eAAe,EAAE,CAAC;YAClB,iBAAiB,EAAE,kBAAkB,EAAE,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5C,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CACrC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,EAC5B,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,iBAAiB,CAAC,MAAM,EACxB,OAAO,EACP,IAAI,CAAC,0BAA0B,0DAE/B,CAAC,EACD,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CACnD,CAAC;QACF,iBAAiB,CAAC,cAAc,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;IAED,gFAAgF;IAChF,0CAA0C;IAC1C,gFAAgF;IAEzE,kBAAkB,CACrB,YAA0C,EAC1C,UAAsB,EACtB,KAAa,EACb,MAAc,EACd,MAAwB,EACxB,OAAO,GAAG,KAAK,EACf,gBAAgB,GAAG,KAAK,EACxB,OAAO,GAAG,CAAC,EACX,OAAO,GAAG,CAAC;QAEX,MAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAE3C,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAC7H,CAAC;IACL,CAAC;IAED,4FAA4F;IACrF,aAAa,CAChB,WAA0I,EAC1I,OAAqC,EACrC,KAAa,EACb,MAAc,EACd,MAAc,EACd,MAAwB,EACxB,YAAoB,CAAC,EACrB,WAAmB,CAAC,EACpB,OAAO,GAAG,KAAK,EACf,gBAAgB,GAAG,KAAK,EACxB,OAAO,GAAG,CAAC,EACX,OAAO,GAAG,CAAC,EACX,oBAA8B;QAE9B,MAAM,UAAU,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAE,OAAO,CAAC,gBAA0C,CAAC,kBAAmB,CAAC,CAAC,CAAC,OAAO,CAAC;QACtJ,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;QACnF,MAAM,eAAe,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAE,OAAO,CAAC,gBAA0C,CAAC,CAAC,CAAC,OAAO,CAAC;QAEvI,MAAM,eAAe,GAA8B;YAC/C,OAAO,EAAE,UAAU;YACnB,MAAM,EAAE;gBACJ,CAAC,EAAE,OAAO;gBACV,CAAC,EAAE,OAAO;gBACV,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;aAC5B;YACD,QAAQ,EAAE,QAAQ;YAClB,kBAAkB,EAAE,gBAAgB;SACvC,CAAC;QAEF,MAAM,aAAa,GAAG;YAClB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,KAAK;YACzE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,MAAM;YAC7E,kBAAkB,EAAE,MAAM,IAAI,CAAC;SAClC,CAAC;QAEF,IAAK,WAA0B,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACvD,WAAW,GAAG,WAAyB,CAAC;YAExC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC;YACxF,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG,KAAK,WAAW,CAAC;YAEnE,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;gBAE7D,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAC9C,WAAW,CAAC,UAAU,EACtB,eAAe,CAAC,WAAW,CAAC,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EAC1E,IAAI,EACJ,4BAA4B,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAC5E,CAAC;gBAEF,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;gBAE5C,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAE7C,MAAM,CAAC,KAAK,EAAE,CAAC;gBAEf,cAAc,CAAC,mBAAmB,CAC9B;oBACI,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,CAAC;oBACT,WAAW;oBACX,YAAY,EAAE,MAAM;iBACvB,EACD,eAAe,EACf,aAAa,CAChB,CAAC;gBAEF,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBAErD,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAC3B,eAAe,EACf,WAAW,EACX;oBACI,MAAM,EAAE,CAAC;oBACT,WAAW;oBACX,YAAY,EAAE,MAAM;iBACvB,EACD,aAAa,CAChB,CAAC;YACN,CAAC;YAED,IAAI,OAAO,IAAI,gBAAgB,EAAE,CAAC;gBAC9B,IAAI,mBAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;oBACjD,MAAM,WAAW,GAAG,OAAO,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,KAAK,OAAO,CAAC,KAAK,IAAI,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC;oBAC3G,IAAI,CAAC,uBAAuB,CACxB,eAAe,EACf,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,EACd,MAAM,EACN,OAAO,EACP,gBAAgB,EAChB,SAAS,EACT,QAAQ,EACR,MAAM,IAAI,CAAC,EACX,OAAO,EACP,OAAO,EACP,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EACvB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EACxB,SAAS,EACT,oBAAoB,CACvB,CAAC;gBACN,CAAC;qBAAM,CAAC;oBACJ,sCAAsC;oBACtC,4CAA4C;oBAC5C,MAAM,gHAAgH,CAAC;gBAC3H,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,WAAW,GAAG,WAA+H,CAAC;YAC9I,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;QAC3H,CAAC;IACL,CAAC;IAED,qEAAqE;IAC9D,UAAU,CACb,OAAmB,EACnB,CAAS,EACT,CAAS,EACT,KAAa,EACb,MAAc,EACd,MAAwB,EACxB,YAAoB,CAAC,EACrB,WAAmB,CAAC,EACpB,SAAoC,IAAI,EACxC,gBAAgB,GAAG,KAAK;QAExB,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC;QAEnF,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC;QAExF,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QAE9D,MAAM,IAAI,GAAG,kBAAkB,GAAG,MAAM,CAAC;QAEzC,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CACjD,IAAI,EACJ,eAAe,CAAC,WAAW,CAAC,OAAO,GAAG,eAAe,CAAC,WAAW,CAAC,OAAO,EACzE,SAAS,EACT,yBAAyB,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CACzE,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QAE7D,cAAc,CAAC,mBAAmB,CAC9B;YACI,OAAO;YACP,QAAQ;YACR,MAAM,EAAE;gBACJ,CAAC;gBACD,CAAC;gBACD,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;aAC5B;SACJ,EACD;YACI,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,CAAC;YACT,WAAW,EAAE,kBAAkB;SAClC,EACD;YACI,KAAK;YACL,MAAM;YACN,kBAAkB,EAAE,CAAC;SACxB,CACJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAErD,OAAO,IAAI,CAAC,cAAc,CAAC,kBAAkB,CACzC,SAAS,EACT,IAAI,EACJ,KAAK,EACL,MAAM,EACN,WAAW,EACX,kBAAkB,EAClB,mBAAmB,CAAC,wBAAwB,CAAC,MAAM,CAAC,EACpD,CAAC,EACD,MAAM,EACN,IAAI,EACJ,gBAAgB,CACnB,CAAC;IACN,CAAC;IAED,gFAAgF;IAChF,uCAAuC;IACvC,gFAAgF;IAEzE,cAAc,CAAC,OAAqC;QACvD,IAAI,mBAAmB,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;YACjD,MAAM,eAAe,GAAG,OAAO,CAAC,gBAAgB,CAAC;YACjD,MAAM,iBAAiB,GAAG,OAAO,CAAC,kBAAkB,CAAC;YAErD,6IAA6I;YAC7I,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC,CAAC;QAC7E,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAEM,uBAAuB;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC5D,MAAM,CAAC,eAAe,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;YAE9E,IAAI,eAAe,EAAE,CAAC;gBAClB,IAAI,mBAAmB,CAAC,iBAAiB,CAAC,eAAe,CAAC,EAAE,CAAC;oBACzD,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC9B,CAAC;qBAAM,CAAC;oBACJ,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC9B,CAAC;YACL,CAAC;YACD,iBAAiB,EAAE,OAAO,EAAE,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC7C,CAAC;CACJ", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\n/* eslint-disable babylonjs/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\n// License for the mipmap generation code:\r\n//\r\n// Copyright 2020 <PERSON>\r\n//\r\n// Permission is hereby granted, free of charge, to any person obtaining a copy\r\n// of this software and associated documentation files (the \"Software\"), to deal\r\n// in the Software without restriction, including without limitation the rights\r\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\n// copies of the Software, and to permit persons to whom the Software is\r\n// furnished to do so, subject to the following conditions:\r\n\r\n// The above copyright notice and this permission notice shall be included in\r\n// all copies or substantial portions of the Software.\r\n\r\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\r\n// SOFTWARE.\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport type { WebGPUBufferManager } from \"./webgpuBufferManager\";\r\nimport { Constants } from \"../constants\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport { InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport type { IHardwareTextureWrapper } from \"../../Materials/Textures/hardwareTextureWrapper\";\r\nimport type { BaseTexture } from \"../../Materials/Textures/baseTexture\";\r\nimport { WebGPUHardwareTexture } from \"./webgpuHardwareTexture\";\r\nimport type { ExternalTexture } from \"../../Materials/Textures/externalTexture\";\r\nimport type { WebGPUEngine } from \"../webgpuEngine\";\r\nimport { WebGPUTextureHelper } from \"./webgpuTextureHelper\";\r\nimport type { _IProcessingOptions } from \"../Processors/shaderProcessingOptions\";\r\nimport { ShaderLanguage } from \"core/Materials\";\r\nimport { Finalize, Initialize, Process } from \"../Processors/shaderProcessor\";\r\nimport type { WebGPUShaderProcessorWGSL } from \"./webgpuShaderProcessorsWGSL\";\r\n\r\n// TODO WEBGPU improve mipmap generation by using compute shaders\r\n\r\nconst mipmapVertexSource = `\r\n    const pos = array<vec2<f32>, 4>( vec2f(-1.0f, 1.0f),  vec2f(1.0f, 1.0f),  vec2f(-1.0f, -1.0f),  vec2f(1.0f, -1.0f));\r\n    const tex = array<vec2<f32>, 4>( vec2f(0.0f, 0.0f),  vec2f(1.0f, 0.0f),  vec2f(0.0f, 1.0f),  vec2f(1.0f, 1.0f));\r\n\r\n    varying vTex: vec2f;\r\n\r\n    @vertex\r\n    fn main(input : VertexInputs) -> FragmentInputs {\r\n        vertexOutputs.vTex = tex[input.vertexIndex];\r\n        vertexOutputs.position = vec4f(pos[input.vertexIndex], 0.0, 1.0);\r\n    }\r\n    `;\r\n\r\nconst mipmapFragmentSource = `\r\n    var imgSampler: sampler;\r\n    var img: texture_2d<f32>;\r\n\r\n    varying vTex: vec2f;\r\n\r\n    @fragment\r\n    fn main(input: FragmentInputs) -> FragmentOutputs {\r\n        fragmentOutputs.color = textureSample(img, imgSampler, input.vTex);\r\n    }\r\n    `;\r\n\r\nconst invertYPreMultiplyAlphaVertexSource = `\r\n    const pos = array<vec2<f32>, 4>( vec2f(-1.0f, 1.0f),  vec2f(1.0f, 1.0f),  vec2f(-1.0f, -1.0f),  vec2f(1.0f, -1.0f));\r\n    const tex = array<vec2<f32>, 4>( vec2f(0.0f, 0.0f),  vec2f(1.0f, 0.0f),  vec2f(0.0f, 1.0f),  vec2f(1.0f, 1.0f));\r\n\r\n    var img: texture_2d<f32>;\r\n\r\n    #ifdef INVERTY\r\n        varying vTextureSize: vec2f;\r\n    #endif\r\n\r\n    @vertex\r\n    fn main(input : VertexInputs) -> FragmentInputs {\r\n        #ifdef INVERTY\r\n            vertexOutputs.vTextureSize = vec2f(textureDimensions(img, 0));\r\n        #endif\r\n        vertexOutputs.position =  vec4f(pos[input.vertexIndex], 0.0, 1.0);\r\n    }\r\n    `;\r\n\r\nconst invertYPreMultiplyAlphaFragmentSource = `\r\n    var img: texture_2d<f32>;\r\n\r\n    #ifdef INVERTY\r\n        varying vTextureSize: vec2f;\r\n    #endif\r\n\r\n    @fragment\r\n    fn main(input: FragmentInputs) -> FragmentOutputs {\r\n    #ifdef INVERTY\r\n        var color: vec4f = textureLoad(img, vec2i(i32(input.position.x), i32(input.vTextureSize.y - input.position.y)), 0);\r\n    #else\r\n        var color: vec4f = textureLoad(img, vec2i(input.position.xy), 0);\r\n    #endif\r\n    #ifdef PREMULTIPLYALPHA\r\n        color = vec4f(color.rgb * color.a, color.a);\r\n    #endif\r\n        fragmentOutputs.color = color;\r\n    }\r\n    `;\r\n\r\nconst invertYPreMultiplyAlphaWithOfstVertexSource = invertYPreMultiplyAlphaVertexSource;\r\n\r\nconst invertYPreMultiplyAlphaWithOfstFragmentSource = `\r\n    var img: texture_2d<f32>;\r\n    uniform ofstX: f32;\r\n    uniform ofstY: f32;\r\n    uniform width: f32;\r\n    uniform height: f32;\r\n\r\n    #ifdef INVERTY\r\n        varying vTextureSize: vec2f;\r\n    #endif\r\n\r\n    @fragment\r\n    fn main(input: FragmentInputs) -> FragmentOutputs {\r\n        if (input.position.x < uniforms.ofstX || input.position.x >= uniforms.ofstX + uniforms.width) {\r\n            discard;\r\n        }\r\n        if (input.position.y < uniforms.ofstY || input.position.y >= uniforms.ofstY + uniforms.height) {\r\n            discard;\r\n        }\r\n    #ifdef INVERTY\r\n        var color: vec4f = textureLoad(img, vec2i(i32(input.position.x), i32(uniforms.ofstY + uniforms.height - (input.position.y - uniforms.ofstY))), 0);\r\n    #else\r\n        var color: vec4f = textureLoad(img, vec2i(input.position.xy), 0);\r\n    #endif\r\n    #ifdef PREMULTIPLYALPHA\r\n        color = vec4f(color.rgb * color.a, color.a);\r\n    #endif\r\n        fragmentOutputs.color = color;\r\n    }\r\n    `;\r\n\r\nconst clearVertexSource = `\r\n    const pos = array<vec2<f32>, 4>( vec2f(-1.0f, 1.0f),  vec2f(1.0f, 1.0f),  vec2f(-1.0f, -1.0f),  vec2f(1.0f, -1.0f));\r\n\r\n    @vertex\r\n    fn main(input : VertexInputs) -> FragmentInputs {\r\n        vertexOutputs.position =  vec4f(pos[input.vertexIndex], 0.0, 1.0);\r\n    }\r\n    `;\r\n\r\nconst clearFragmentSource = `\r\n    uniform color: vec4f;\r\n\r\n\r\n    @fragment\r\n    fn main(input: FragmentInputs) -> FragmentOutputs {\r\n        fragmentOutputs.color = uniforms.color;\r\n    }\r\n    `;\r\n\r\nconst copyVideoToTextureVertexSource = `\r\n    struct VertexOutput {\r\n        @builtin(position) Position : vec4<f32>,\r\n        @location(0) fragUV : vec2<f32>\r\n    }\r\n\r\n    @vertex\r\n    fn main(\r\n        @builtin(vertex_index) VertexIndex : u32\r\n    ) -> VertexOutput {\r\n        var pos = array<vec2<f32>, 4>(\r\n            vec2(-1.0,  1.0),\r\n            vec2( 1.0,  1.0),\r\n            vec2(-1.0, -1.0),\r\n            vec2( 1.0, -1.0)\r\n        );\r\n        var tex = array<vec2<f32>, 4>(\r\n            vec2(0.0, 0.0),\r\n            vec2(1.0, 0.0),\r\n            vec2(0.0, 1.0),\r\n            vec2(1.0, 1.0)\r\n        );\r\n\r\n        var output: VertexOutput;\r\n\r\n        output.Position = vec4<f32>(pos[VertexIndex], 0.0, 1.0);\r\n        output.fragUV = tex[VertexIndex];\r\n\r\n        return output;\r\n    }\r\n    `;\r\n\r\nconst copyVideoToTextureFragmentSource = `\r\n    @group(0) @binding(0) var videoSampler: sampler;\r\n    @group(0) @binding(1) var videoTexture: texture_external;\r\n\r\n    @fragment\r\n    fn main(\r\n        @location(0) fragUV: vec2<f32>\r\n    ) -> @location(0) vec4<f32> {\r\n        return textureSampleBaseClampToEdge(videoTexture, videoSampler, fragUV);\r\n    }\r\n    `;\r\n\r\nconst copyVideoToTextureInvertYFragmentSource = `\r\n    @group(0) @binding(0) var videoSampler: sampler;\r\n    @group(0) @binding(1) var videoTexture: texture_external;\r\n\r\n    @fragment\r\n    fn main(\r\n        @location(0) fragUV: vec2<f32>\r\n    ) -> @location(0) vec4<f32> {\r\n        return textureSampleBaseClampToEdge(videoTexture, videoSampler, vec2<f32>(fragUV.x, 1.0 - fragUV.y));\r\n    }\r\n    `;\r\n\r\nenum PipelineType {\r\n    MipMap = 0,\r\n    InvertYPremultiplyAlpha = 1,\r\n    Clear = 2,\r\n    InvertYPremultiplyAlphaWithOfst = 3,\r\n}\r\n\r\nenum VideoPipelineType {\r\n    DontInvertY = 0,\r\n    InvertY = 1,\r\n}\r\n\r\ninterface IPipelineParameters {\r\n    invertY?: boolean;\r\n    premultiplyAlpha?: boolean;\r\n}\r\n\r\nconst shadersForPipelineType = [\r\n    { vertex: mipmapVertexSource, fragment: mipmapFragmentSource },\r\n    { vertex: invertYPreMultiplyAlphaVertexSource, fragment: invertYPreMultiplyAlphaFragmentSource },\r\n    { vertex: clearVertexSource, fragment: clearFragmentSource },\r\n    { vertex: invertYPreMultiplyAlphaWithOfstVertexSource, fragment: invertYPreMultiplyAlphaWithOfstFragmentSource },\r\n];\r\n\r\n/**\r\n * Map a (renderable) texture format (GPUTextureFormat) to an index for fast lookup (in caches for eg)\r\n * The number of entries should not go over 64! Else, the code in WebGPUCacheRenderPipeline.setMRT should be updated\r\n */\r\nexport const renderableTextureFormatToIndex: { [name: string]: number } = {\r\n    \"\": 0,\r\n    r8unorm: 1,\r\n    r8uint: 2,\r\n    r8sint: 3,\r\n\r\n    r16uint: 4,\r\n    r16sint: 5,\r\n    r16float: 6,\r\n    rg8unorm: 7,\r\n    rg8uint: 8,\r\n    rg8sint: 9,\r\n\r\n    r32uint: 10,\r\n    r32sint: 11,\r\n    r32float: 12,\r\n    rg16uint: 13,\r\n    rg16sint: 14,\r\n    rg16float: 15,\r\n    rgba8unorm: 16,\r\n    \"rgba8unorm-srgb\": 17,\r\n    rgba8uint: 18,\r\n    rgba8sint: 19,\r\n    bgra8unorm: 20,\r\n    \"bgra8unorm-srgb\": 21,\r\n\r\n    rgb10a2uint: 22,\r\n    rgb10a2unorm: 23,\r\n    /* rg11b10ufloat: this entry is dynamically added if the \"RG11B10UFloatRenderable\" extension is supported */\r\n\r\n    rg32uint: 24,\r\n    rg32sint: 25,\r\n    rg32float: 26,\r\n    rgba16uint: 27,\r\n    rgba16sint: 28,\r\n    rgba16float: 29,\r\n\r\n    rgba32uint: 30,\r\n    rgba32sint: 31,\r\n    rgba32float: 32,\r\n\r\n    stencil8: 33,\r\n    depth16unorm: 34,\r\n    depth24plus: 35,\r\n    \"depth24plus-stencil8\": 36,\r\n    depth32float: 37,\r\n\r\n    \"depth32float-stencil8\": 38,\r\n\r\n    r16unorm: 39,\r\n    rg16unorm: 40,\r\n    rgba16unorm: 41,\r\n    r16snorm: 42,\r\n    rg16snorm: 43,\r\n    rgba16snorm: 44,\r\n};\r\n\r\n/** @internal */\r\nexport class WebGPUTextureManager {\r\n    private _engine: WebGPUEngine;\r\n    private _device: GPUDevice;\r\n    private _bufferManager: WebGPUBufferManager;\r\n    private _mipmapSampler: GPUSampler;\r\n    private _videoSampler: GPUSampler;\r\n    private _ubCopyWithOfst: GPUBuffer;\r\n    private _pipelines: { [format: string]: Array<[GPURenderPipeline, GPUBindGroupLayout]> } = {};\r\n    private _compiledShaders: GPUShaderModule[][] = [];\r\n    private _videoPipelines: { [format: string]: Array<[GPURenderPipeline, GPUBindGroupLayout]> } = {};\r\n    private _videoCompiledShaders: GPUShaderModule[][] = [];\r\n    private _deferredReleaseTextures: Array<[Nullable<IHardwareTextureWrapper | GPUTexture>, Nullable<BaseTexture>]> = [];\r\n    private _commandEncoderForCreation: GPUCommandEncoder;\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                         Initialization / Helpers\r\n    //------------------------------------------------------------------------------\r\n\r\n    constructor(engine: WebGPUEngine, device: GPUDevice, bufferManager: WebGPUBufferManager, enabledExtensions: GPUFeatureName[]) {\r\n        this._engine = engine;\r\n        this._device = device;\r\n        this._bufferManager = bufferManager;\r\n\r\n        if (enabledExtensions.indexOf(WebGPUConstants.FeatureName.RG11B10UFloatRenderable) !== -1) {\r\n            const keys = Object.keys(renderableTextureFormatToIndex);\r\n            renderableTextureFormatToIndex[WebGPUConstants.TextureFormat.RG11B10UFloat] = renderableTextureFormatToIndex[keys[keys.length - 1]] + 1;\r\n        }\r\n\r\n        this._mipmapSampler = device.createSampler({ minFilter: WebGPUConstants.FilterMode.Linear });\r\n        this._videoSampler = device.createSampler({ minFilter: WebGPUConstants.FilterMode.Linear });\r\n        this._ubCopyWithOfst = this._bufferManager.createBuffer(\r\n            4 * 4,\r\n            WebGPUConstants.BufferUsage.Uniform | WebGPUConstants.BufferUsage.CopyDst,\r\n            \"UBCopyWithOffset\"\r\n        ).underlyingResource;\r\n\r\n        this._getPipeline(WebGPUConstants.TextureFormat.RGBA8Unorm);\r\n        this._getVideoPipeline(WebGPUConstants.TextureFormat.RGBA8Unorm);\r\n    }\r\n\r\n    private _getPipeline(format: GPUTextureFormat, type: PipelineType = PipelineType.MipMap, params?: IPipelineParameters): [GPURenderPipeline, GPUBindGroupLayout] {\r\n        const index =\r\n            type === PipelineType.MipMap\r\n                ? 1 << 0\r\n                : type === PipelineType.InvertYPremultiplyAlpha\r\n                  ? ((params!.invertY ? 1 : 0) << 1) + ((params!.premultiplyAlpha ? 1 : 0) << 2)\r\n                  : type === PipelineType.Clear\r\n                    ? 1 << 3\r\n                    : type === PipelineType.InvertYPremultiplyAlphaWithOfst\r\n                      ? ((params!.invertY ? 1 : 0) << 4) + ((params!.premultiplyAlpha ? 1 : 0) << 5)\r\n                      : 0;\r\n\r\n        if (!this._pipelines[format]) {\r\n            this._pipelines[format] = [];\r\n        }\r\n\r\n        let pipelineAndBGL = this._pipelines[format][index];\r\n        if (!pipelineAndBGL) {\r\n            let defines = \"\";\r\n            if (type === PipelineType.InvertYPremultiplyAlpha || type === PipelineType.InvertYPremultiplyAlphaWithOfst) {\r\n                if (params!.invertY) {\r\n                    defines += \"#define INVERTY\\n\";\r\n                }\r\n                if (params!.premultiplyAlpha) {\r\n                    defines += \"#define PREMULTIPLYALPHA\\n\";\r\n                }\r\n            }\r\n\r\n            let modules = this._compiledShaders[index];\r\n            if (!modules) {\r\n                let vertexCode = shadersForPipelineType[type].vertex;\r\n                let fragmentCode = shadersForPipelineType[type].fragment;\r\n\r\n                const processorOptions: _IProcessingOptions = {\r\n                    defines: defines.split(\"\\n\"),\r\n                    indexParameters: null,\r\n                    isFragment: false,\r\n                    shouldUseHighPrecisionShader: true,\r\n                    processor: this._engine._getShaderProcessor(ShaderLanguage.WGSL),\r\n                    supportsUniformBuffers: true,\r\n                    shadersRepository: \"\",\r\n                    includesShadersStore: {},\r\n                    version: (this._engine.version * 100).toString(),\r\n                    platformName: this._engine.shaderPlatformName,\r\n                    processingContext: this._engine._getShaderProcessingContext(ShaderLanguage.WGSL, true),\r\n                    isNDCHalfZRange: this._engine.isNDCHalfZRange,\r\n                    useReverseDepthBuffer: this._engine.useReverseDepthBuffer,\r\n                };\r\n\r\n                Initialize(processorOptions);\r\n\r\n                // Disable special additions not needed here\r\n                (processorOptions.processor as WebGPUShaderProcessorWGSL).pureMode = true;\r\n\r\n                Process(\r\n                    vertexCode,\r\n                    processorOptions,\r\n                    (migratedVertexCode) => {\r\n                        vertexCode = migratedVertexCode;\r\n                    },\r\n                    this._engine\r\n                );\r\n\r\n                processorOptions.isFragment = true;\r\n\r\n                Process(\r\n                    fragmentCode,\r\n                    processorOptions,\r\n                    (migratedFragmentCode) => {\r\n                        fragmentCode = migratedFragmentCode;\r\n                    },\r\n                    this._engine\r\n                );\r\n\r\n                const final = Finalize(vertexCode, fragmentCode, processorOptions);\r\n\r\n                // Restore\r\n                (processorOptions.processor as WebGPUShaderProcessorWGSL).pureMode = false;\r\n\r\n                const vertexModule = this._device.createShaderModule({\r\n                    label: `BabylonWebGPUDevice${this._engine.uniqueId}_InternalVertexShader_${index}`,\r\n                    code: final.vertexCode,\r\n                });\r\n                const fragmentModule = this._device.createShaderModule({\r\n                    label: `BabylonWebGPUDevice${this._engine.uniqueId}_InternalFragmentShader_${index}`,\r\n                    code: final.fragmentCode,\r\n                });\r\n                modules = this._compiledShaders[index] = [vertexModule, fragmentModule];\r\n            }\r\n\r\n            const pipeline = this._device.createRenderPipeline({\r\n                label: `BabylonWebGPUDevice${this._engine.uniqueId}_InternalPipeline_${format}_${index}`,\r\n                layout: WebGPUConstants.AutoLayoutMode.Auto,\r\n                vertex: {\r\n                    module: modules[0],\r\n                    entryPoint: \"main\",\r\n                },\r\n                fragment: {\r\n                    module: modules[1],\r\n                    entryPoint: \"main\",\r\n                    targets: [\r\n                        {\r\n                            format,\r\n                        },\r\n                    ],\r\n                },\r\n                primitive: {\r\n                    topology: WebGPUConstants.PrimitiveTopology.TriangleStrip,\r\n                    stripIndexFormat: WebGPUConstants.IndexFormat.Uint16,\r\n                },\r\n            });\r\n\r\n            pipelineAndBGL = this._pipelines[format][index] = [pipeline, pipeline.getBindGroupLayout(0)];\r\n        }\r\n\r\n        return pipelineAndBGL;\r\n    }\r\n\r\n    private _getVideoPipeline(format: GPUTextureFormat, type: VideoPipelineType = VideoPipelineType.DontInvertY): [GPURenderPipeline, GPUBindGroupLayout] {\r\n        const index = type === VideoPipelineType.InvertY ? 1 << 0 : 0;\r\n\r\n        if (!this._videoPipelines[format]) {\r\n            this._videoPipelines[format] = [];\r\n        }\r\n\r\n        let pipelineAndBGL = this._videoPipelines[format][index];\r\n        if (!pipelineAndBGL) {\r\n            let modules = this._videoCompiledShaders[index];\r\n            if (!modules) {\r\n                const vertexModule = this._device.createShaderModule({\r\n                    code: copyVideoToTextureVertexSource,\r\n                    label: `BabylonWebGPUDevice${this._engine.uniqueId}_CopyVideoToTexture_VertexShader`,\r\n                });\r\n                const fragmentModule = this._device.createShaderModule({\r\n                    code: index === 0 ? copyVideoToTextureFragmentSource : copyVideoToTextureInvertYFragmentSource,\r\n                    label: `BabylonWebGPUDevice${this._engine.uniqueId}_CopyVideoToTexture_FragmentShader_${index === 0 ? \"DontInvertY\" : \"InvertY\"}`,\r\n                });\r\n                modules = this._videoCompiledShaders[index] = [vertexModule, fragmentModule];\r\n            }\r\n\r\n            const pipeline = this._device.createRenderPipeline({\r\n                label: `BabylonWebGPUDevice${this._engine.uniqueId}_InternalVideoPipeline_${format}_${index === 0 ? \"DontInvertY\" : \"InvertY\"}`,\r\n                layout: WebGPUConstants.AutoLayoutMode.Auto,\r\n                vertex: {\r\n                    module: modules[0],\r\n                    entryPoint: \"main\",\r\n                },\r\n                fragment: {\r\n                    module: modules[1],\r\n                    entryPoint: \"main\",\r\n                    targets: [\r\n                        {\r\n                            format,\r\n                        },\r\n                    ],\r\n                },\r\n                primitive: {\r\n                    topology: WebGPUConstants.PrimitiveTopology.TriangleStrip,\r\n                    stripIndexFormat: WebGPUConstants.IndexFormat.Uint16,\r\n                },\r\n            });\r\n\r\n            pipelineAndBGL = this._videoPipelines[format][index] = [pipeline, pipeline.getBindGroupLayout(0)];\r\n        }\r\n\r\n        return pipelineAndBGL;\r\n    }\r\n\r\n    public setCommandEncoder(encoder: GPUCommandEncoder): void {\r\n        this._commandEncoderForCreation = encoder;\r\n    }\r\n\r\n    public copyVideoToTexture(video: ExternalTexture, texture: InternalTexture, format: GPUTextureFormat, invertY = false, commandEncoder?: GPUCommandEncoder): void {\r\n        const useOwnCommandEncoder = commandEncoder === undefined;\r\n        const [pipeline, bindGroupLayout] = this._getVideoPipeline(format, invertY ? VideoPipelineType.InvertY : VideoPipelineType.DontInvertY);\r\n\r\n        if (useOwnCommandEncoder) {\r\n            commandEncoder = this._device.createCommandEncoder({});\r\n        }\r\n\r\n        commandEncoder!.pushDebugGroup?.(`copy video to texture - invertY=${invertY}`);\r\n\r\n        const webgpuHardwareTexture = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n        const renderPassDescriptor: GPURenderPassDescriptor = {\r\n            label: `BabylonWebGPUDevice${this._engine.uniqueId}_copyVideoToTexture_${format}_${invertY ? \"InvertY\" : \"DontInvertY\"}${texture.label ? \"_\" + texture.label : \"\"}`,\r\n            colorAttachments: [\r\n                {\r\n                    view: webgpuHardwareTexture.underlyingResource!.createView({\r\n                        format,\r\n                        dimension: WebGPUConstants.TextureViewDimension.E2d,\r\n                        mipLevelCount: 1,\r\n                        baseArrayLayer: 0,\r\n                        baseMipLevel: 0,\r\n                        arrayLayerCount: 1,\r\n                        aspect: WebGPUConstants.TextureAspect.All,\r\n                    }),\r\n                    loadOp: WebGPUConstants.LoadOp.Load,\r\n                    storeOp: WebGPUConstants.StoreOp.Store,\r\n                },\r\n            ],\r\n        };\r\n        const passEncoder = commandEncoder!.beginRenderPass(renderPassDescriptor);\r\n\r\n        const descriptor: GPUBindGroupDescriptor = {\r\n            layout: bindGroupLayout,\r\n            entries: [\r\n                {\r\n                    binding: 0,\r\n                    resource: this._videoSampler,\r\n                },\r\n                {\r\n                    binding: 1,\r\n                    resource: this._device.importExternalTexture({\r\n                        source: video.underlyingResource,\r\n                    }),\r\n                },\r\n            ],\r\n        };\r\n\r\n        const bindGroup = this._device.createBindGroup(descriptor);\r\n\r\n        passEncoder.setPipeline(pipeline);\r\n        passEncoder.setBindGroup(0, bindGroup);\r\n        passEncoder.draw(4, 1, 0, 0);\r\n        passEncoder.end();\r\n\r\n        commandEncoder!.popDebugGroup?.();\r\n\r\n        if (useOwnCommandEncoder) {\r\n            this._device.queue.submit([commandEncoder!.finish()]);\r\n            commandEncoder = null as any;\r\n        }\r\n    }\r\n\r\n    public invertYPreMultiplyAlpha(\r\n        gpuOrHdwTexture: GPUTexture | WebGPUHardwareTexture,\r\n        width: number,\r\n        height: number,\r\n        format: GPUTextureFormat,\r\n        invertY = false,\r\n        premultiplyAlpha = false,\r\n        faceIndex = 0,\r\n        mipLevel = 0,\r\n        layers = 1,\r\n        ofstX = 0,\r\n        ofstY = 0,\r\n        rectWidth = 0,\r\n        rectHeight = 0,\r\n        commandEncoder?: GPUCommandEncoder,\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        allowGPUOptimization?: boolean\r\n    ): void {\r\n        const useRect = rectWidth !== 0;\r\n        const useOwnCommandEncoder = commandEncoder === undefined;\r\n        const [pipeline, bindGroupLayout] = this._getPipeline(format, useRect ? PipelineType.InvertYPremultiplyAlphaWithOfst : PipelineType.InvertYPremultiplyAlpha, {\r\n            invertY,\r\n            premultiplyAlpha,\r\n        });\r\n\r\n        faceIndex = Math.max(faceIndex, 0);\r\n\r\n        if (useOwnCommandEncoder) {\r\n            commandEncoder = this._device.createCommandEncoder({});\r\n        }\r\n\r\n        commandEncoder!.pushDebugGroup?.(`internal process texture - invertY=${invertY} premultiplyAlpha=${premultiplyAlpha}`);\r\n\r\n        let gpuTexture: Nullable<GPUTexture>;\r\n        if (WebGPUTextureHelper.IsHardwareTexture(gpuOrHdwTexture)) {\r\n            gpuTexture = gpuOrHdwTexture.underlyingResource;\r\n            if (!(invertY && !premultiplyAlpha && layers === 1 && faceIndex === 0)) {\r\n                // we optimize only for the most likely case (invertY=true, premultiplyAlpha=false, layers=1, faceIndex=0) to avoid dealing with big caches\r\n                gpuOrHdwTexture = undefined as any;\r\n            }\r\n        } else {\r\n            gpuTexture = gpuOrHdwTexture;\r\n            gpuOrHdwTexture = undefined as any;\r\n        }\r\n        if (!gpuTexture) {\r\n            return;\r\n        }\r\n\r\n        if (useRect) {\r\n            this._bufferManager.setRawData(this._ubCopyWithOfst, 0, new Float32Array([ofstX, ofstY, rectWidth, rectHeight]), 0, 4 * 4);\r\n        }\r\n\r\n        const webgpuHardwareTexture = gpuOrHdwTexture as Nullable<WebGPUHardwareTexture>;\r\n\r\n        const outputTexture =\r\n            webgpuHardwareTexture?._copyInvertYTempTexture ??\r\n            this.createTexture(\r\n                { width, height, layers: 1 },\r\n                false,\r\n                false,\r\n                false,\r\n                false,\r\n                false,\r\n                format,\r\n                1,\r\n                commandEncoder,\r\n                WebGPUConstants.TextureUsage.CopySrc | WebGPUConstants.TextureUsage.RenderAttachment | WebGPUConstants.TextureUsage.TextureBinding,\r\n                undefined,\r\n                \"TempTextureForCopyWithInvertY\"\r\n            );\r\n\r\n        const renderPassDescriptor = webgpuHardwareTexture?._copyInvertYRenderPassDescr ?? {\r\n            label: `BabylonWebGPUDevice${this._engine.uniqueId}_invertYPreMultiplyAlpha_${format}_${invertY ? \"InvertY\" : \"DontInvertY\"}_${\r\n                premultiplyAlpha ? \"PremultiplyAlpha\" : \"DontPremultiplyAlpha\"\r\n            }`,\r\n            colorAttachments: [\r\n                {\r\n                    view: outputTexture.createView({\r\n                        format,\r\n                        dimension: WebGPUConstants.TextureViewDimension.E2d,\r\n                        baseMipLevel: 0,\r\n                        mipLevelCount: 1,\r\n                        arrayLayerCount: 1,\r\n                        baseArrayLayer: 0,\r\n                    }),\r\n                    loadOp: WebGPUConstants.LoadOp.Load,\r\n                    storeOp: WebGPUConstants.StoreOp.Store,\r\n                },\r\n            ],\r\n        };\r\n        const passEncoder = commandEncoder!.beginRenderPass(renderPassDescriptor);\r\n\r\n        let bindGroup = useRect ? webgpuHardwareTexture?._copyInvertYBindGroupWithOfst : webgpuHardwareTexture?._copyInvertYBindGroup;\r\n        if (!bindGroup) {\r\n            const descriptor: GPUBindGroupDescriptor = {\r\n                layout: bindGroupLayout,\r\n                entries: [\r\n                    {\r\n                        binding: 0,\r\n                        resource: gpuTexture.createView({\r\n                            format,\r\n                            dimension: WebGPUConstants.TextureViewDimension.E2d,\r\n                            baseMipLevel: mipLevel,\r\n                            mipLevelCount: 1,\r\n                            arrayLayerCount: layers,\r\n                            baseArrayLayer: faceIndex,\r\n                        }),\r\n                    },\r\n                ],\r\n            };\r\n            if (useRect) {\r\n                descriptor.entries.push({\r\n                    binding: 1,\r\n                    resource: {\r\n                        buffer: this._ubCopyWithOfst,\r\n                    },\r\n                });\r\n            }\r\n            bindGroup = this._device.createBindGroup(descriptor);\r\n        }\r\n\r\n        passEncoder.setPipeline(pipeline);\r\n        passEncoder.setBindGroup(0, bindGroup);\r\n        passEncoder.draw(4, 1, 0, 0);\r\n        passEncoder.end();\r\n\r\n        commandEncoder!.copyTextureToTexture(\r\n            {\r\n                texture: outputTexture,\r\n            },\r\n            {\r\n                texture: gpuTexture,\r\n                mipLevel,\r\n                origin: {\r\n                    x: 0,\r\n                    y: 0,\r\n                    z: faceIndex,\r\n                },\r\n            },\r\n            {\r\n                width: rectWidth || width,\r\n                height: rectHeight || height,\r\n                depthOrArrayLayers: 1,\r\n            }\r\n        );\r\n\r\n        if (webgpuHardwareTexture) {\r\n            webgpuHardwareTexture._copyInvertYTempTexture = outputTexture;\r\n            webgpuHardwareTexture._copyInvertYRenderPassDescr = renderPassDescriptor;\r\n            if (useRect) {\r\n                webgpuHardwareTexture._copyInvertYBindGroupWithOfst = bindGroup;\r\n            } else {\r\n                webgpuHardwareTexture._copyInvertYBindGroup = bindGroup;\r\n            }\r\n        } else {\r\n            this._deferredReleaseTextures.push([outputTexture, null]);\r\n        }\r\n\r\n        commandEncoder!.popDebugGroup?.();\r\n\r\n        if (useOwnCommandEncoder) {\r\n            this._device.queue.submit([commandEncoder!.finish()]);\r\n            commandEncoder = null as any;\r\n        }\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                               Creation\r\n    //------------------------------------------------------------------------------\r\n\r\n    public createTexture(\r\n        imageBitmap: ImageBitmap | { width: number; height: number; layers: number },\r\n        hasMipmaps = false,\r\n        generateMipmaps = false,\r\n        invertY = false,\r\n        premultiplyAlpha = false,\r\n        is3D = false,\r\n        format: GPUTextureFormat = WebGPUConstants.TextureFormat.RGBA8Unorm,\r\n        sampleCount = 1,\r\n        commandEncoder?: GPUCommandEncoder,\r\n        usage = -1,\r\n        additionalUsages = 0,\r\n        label?: string\r\n    ): GPUTexture {\r\n        sampleCount = WebGPUTextureHelper.GetSample(sampleCount);\r\n\r\n        const layerCount = (imageBitmap as any).layers || 1;\r\n        const textureSize = {\r\n            width: imageBitmap.width,\r\n            height: imageBitmap.height,\r\n            depthOrArrayLayers: layerCount,\r\n        };\r\n\r\n        const renderAttachmentFlag = renderableTextureFormatToIndex[format] ? WebGPUConstants.TextureUsage.RenderAttachment : 0;\r\n        const isCompressedFormat = WebGPUTextureHelper.IsCompressedFormat(format);\r\n        const mipLevelCount = hasMipmaps ? WebGPUTextureHelper.ComputeNumMipmapLevels(imageBitmap.width, imageBitmap.height) : 1;\r\n        const usages = usage >= 0 ? usage : WebGPUConstants.TextureUsage.CopySrc | WebGPUConstants.TextureUsage.CopyDst | WebGPUConstants.TextureUsage.TextureBinding;\r\n\r\n        additionalUsages |= hasMipmaps && !isCompressedFormat ? WebGPUConstants.TextureUsage.CopySrc | renderAttachmentFlag : 0;\r\n\r\n        if (!isCompressedFormat && !is3D) {\r\n            // we don't know in advance if the texture will be updated with copyExternalImageToTexture (which requires to have those flags), so we need to force the flags all the times\r\n            additionalUsages |= renderAttachmentFlag | WebGPUConstants.TextureUsage.CopyDst;\r\n        }\r\n\r\n        const gpuTexture = this._device.createTexture({\r\n            label: `BabylonWebGPUDevice${this._engine.uniqueId}_Texture${is3D ? \"3D\" : \"2D\"}_${label ? label + \"_\" : \"\"}${textureSize.width}x${textureSize.height}x${\r\n                textureSize.depthOrArrayLayers\r\n            }_${hasMipmaps ? \"wmips\" : \"womips\"}_${format}_samples${sampleCount}`,\r\n            size: textureSize,\r\n            dimension: is3D ? WebGPUConstants.TextureDimension.E3d : WebGPUConstants.TextureDimension.E2d,\r\n            format,\r\n            usage: usages | additionalUsages,\r\n            sampleCount,\r\n            mipLevelCount,\r\n        });\r\n\r\n        if (WebGPUTextureHelper.IsImageBitmap(imageBitmap)) {\r\n            this.updateTexture(imageBitmap, gpuTexture, imageBitmap.width, imageBitmap.height, layerCount, format, 0, 0, invertY, premultiplyAlpha, 0, 0);\r\n\r\n            if (hasMipmaps && generateMipmaps) {\r\n                this.generateMipmaps(gpuTexture, format, mipLevelCount, 0, is3D, commandEncoder);\r\n            }\r\n        }\r\n\r\n        return gpuTexture;\r\n    }\r\n\r\n    public createCubeTexture(\r\n        imageBitmaps: ImageBitmap[] | { width: number; height: number },\r\n        hasMipmaps = false,\r\n        generateMipmaps = false,\r\n        invertY = false,\r\n        premultiplyAlpha = false,\r\n        format: GPUTextureFormat = WebGPUConstants.TextureFormat.RGBA8Unorm,\r\n        sampleCount = 1,\r\n        commandEncoder?: GPUCommandEncoder,\r\n        usage = -1,\r\n        additionalUsages = 0,\r\n        label?: string\r\n    ): GPUTexture {\r\n        sampleCount = WebGPUTextureHelper.GetSample(sampleCount);\r\n\r\n        const width = WebGPUTextureHelper.IsImageBitmapArray(imageBitmaps) ? imageBitmaps[0].width : imageBitmaps.width;\r\n        const height = WebGPUTextureHelper.IsImageBitmapArray(imageBitmaps) ? imageBitmaps[0].height : imageBitmaps.height;\r\n\r\n        const renderAttachmentFlag = renderableTextureFormatToIndex[format] ? WebGPUConstants.TextureUsage.RenderAttachment : 0;\r\n        const isCompressedFormat = WebGPUTextureHelper.IsCompressedFormat(format);\r\n        const mipLevelCount = hasMipmaps ? WebGPUTextureHelper.ComputeNumMipmapLevels(width, height) : 1;\r\n        const usages = usage >= 0 ? usage : WebGPUConstants.TextureUsage.CopySrc | WebGPUConstants.TextureUsage.CopyDst | WebGPUConstants.TextureUsage.TextureBinding;\r\n\r\n        additionalUsages |= hasMipmaps && !isCompressedFormat ? WebGPUConstants.TextureUsage.CopySrc | renderAttachmentFlag : 0;\r\n\r\n        if (!isCompressedFormat) {\r\n            // we don't know in advance if the texture will be updated with copyExternalImageToTexture (which requires to have those flags), so we need to force the flags all the times\r\n            additionalUsages |= renderAttachmentFlag | WebGPUConstants.TextureUsage.CopyDst;\r\n        }\r\n\r\n        const gpuTexture = this._device.createTexture({\r\n            label: `BabylonWebGPUDevice${this._engine.uniqueId}_TextureCube_${label ? label + \"_\" : \"\"}${width}x${height}x6_${\r\n                hasMipmaps ? \"wmips\" : \"womips\"\r\n            }_${format}_samples${sampleCount}`,\r\n            size: {\r\n                width,\r\n                height,\r\n                depthOrArrayLayers: 6,\r\n            },\r\n            dimension: WebGPUConstants.TextureDimension.E2d,\r\n            format,\r\n            usage: usages | additionalUsages,\r\n            sampleCount,\r\n            mipLevelCount,\r\n        });\r\n\r\n        if (WebGPUTextureHelper.IsImageBitmapArray(imageBitmaps)) {\r\n            this.updateCubeTextures(imageBitmaps, gpuTexture, width, height, format, invertY, premultiplyAlpha, 0, 0);\r\n\r\n            if (hasMipmaps && generateMipmaps) {\r\n                this.generateCubeMipmaps(gpuTexture, format, mipLevelCount, commandEncoder);\r\n            }\r\n        }\r\n\r\n        return gpuTexture;\r\n    }\r\n\r\n    public generateCubeMipmaps(gpuTexture: GPUTexture | WebGPUHardwareTexture, format: GPUTextureFormat, mipLevelCount: number, commandEncoder?: GPUCommandEncoder): void {\r\n        const useOwnCommandEncoder = commandEncoder === undefined;\r\n\r\n        if (useOwnCommandEncoder) {\r\n            commandEncoder = this._device.createCommandEncoder({});\r\n        }\r\n\r\n        commandEncoder!.pushDebugGroup?.(`create cube mipmaps - ${mipLevelCount} levels`);\r\n\r\n        for (let f = 0; f < 6; ++f) {\r\n            this.generateMipmaps(gpuTexture, format, mipLevelCount, f, false, commandEncoder);\r\n        }\r\n\r\n        commandEncoder!.popDebugGroup?.();\r\n\r\n        if (useOwnCommandEncoder) {\r\n            this._device.queue.submit([commandEncoder!.finish()]);\r\n            commandEncoder = null as any;\r\n        }\r\n    }\r\n\r\n    public generateMipmaps(\r\n        gpuOrHdwTexture: GPUTexture | WebGPUHardwareTexture,\r\n        format: GPUTextureFormat,\r\n        mipLevelCount: number,\r\n        faceIndex = 0,\r\n        is3D = false,\r\n        commandEncoder?: GPUCommandEncoder\r\n    ): void {\r\n        const useOwnCommandEncoder = commandEncoder === undefined;\r\n        const [pipeline, bindGroupLayout] = this._getPipeline(format);\r\n\r\n        faceIndex = Math.max(faceIndex, 0);\r\n\r\n        if (useOwnCommandEncoder) {\r\n            commandEncoder = this._device.createCommandEncoder({});\r\n        }\r\n\r\n        commandEncoder!.pushDebugGroup?.(`create mipmaps for face #${faceIndex} - ${mipLevelCount} levels`);\r\n\r\n        let gpuTexture: Nullable<GPUTexture>;\r\n        if (WebGPUTextureHelper.IsHardwareTexture(gpuOrHdwTexture)) {\r\n            gpuTexture = gpuOrHdwTexture.underlyingResource;\r\n            gpuOrHdwTexture._mipmapGenRenderPassDescr = gpuOrHdwTexture._mipmapGenRenderPassDescr || [];\r\n            gpuOrHdwTexture._mipmapGenBindGroup = gpuOrHdwTexture._mipmapGenBindGroup || [];\r\n        } else {\r\n            gpuTexture = gpuOrHdwTexture;\r\n            gpuOrHdwTexture = undefined as any;\r\n        }\r\n        if (!gpuTexture) {\r\n            return;\r\n        }\r\n\r\n        const webgpuHardwareTexture = gpuOrHdwTexture as Nullable<WebGPUHardwareTexture>;\r\n        for (let i = 1; i < mipLevelCount; ++i) {\r\n            const renderPassDescriptor = webgpuHardwareTexture?._mipmapGenRenderPassDescr[faceIndex]?.[i - 1] ?? {\r\n                label: `BabylonWebGPUDevice${this._engine.uniqueId}_generateMipmaps_${format}_faceIndex${faceIndex}_level${i}`,\r\n                colorAttachments: [\r\n                    {\r\n                        view: gpuTexture.createView({\r\n                            format,\r\n                            dimension: is3D ? WebGPUConstants.TextureViewDimension.E3d : WebGPUConstants.TextureViewDimension.E2d,\r\n                            baseMipLevel: i,\r\n                            mipLevelCount: 1,\r\n                            arrayLayerCount: 1,\r\n                            baseArrayLayer: faceIndex,\r\n                        }),\r\n                        loadOp: WebGPUConstants.LoadOp.Load,\r\n                        storeOp: WebGPUConstants.StoreOp.Store,\r\n                    },\r\n                ],\r\n            };\r\n            if (webgpuHardwareTexture) {\r\n                webgpuHardwareTexture._mipmapGenRenderPassDescr[faceIndex] = webgpuHardwareTexture._mipmapGenRenderPassDescr[faceIndex] || [];\r\n                webgpuHardwareTexture._mipmapGenRenderPassDescr[faceIndex][i - 1] = renderPassDescriptor;\r\n            }\r\n            const passEncoder = commandEncoder!.beginRenderPass(renderPassDescriptor);\r\n\r\n            const bindGroup =\r\n                webgpuHardwareTexture?._mipmapGenBindGroup[faceIndex]?.[i - 1] ??\r\n                this._device.createBindGroup({\r\n                    layout: bindGroupLayout,\r\n                    entries: [\r\n                        {\r\n                            binding: 0,\r\n                            resource: gpuTexture.createView({\r\n                                format,\r\n                                dimension: is3D ? WebGPUConstants.TextureViewDimension.E3d : WebGPUConstants.TextureViewDimension.E2d,\r\n                                baseMipLevel: i - 1,\r\n                                mipLevelCount: 1,\r\n                                arrayLayerCount: 1,\r\n                                baseArrayLayer: faceIndex,\r\n                            }),\r\n                        },\r\n                        {\r\n                            binding: 1,\r\n                            resource: this._mipmapSampler,\r\n                        },\r\n                    ],\r\n                });\r\n            if (webgpuHardwareTexture) {\r\n                webgpuHardwareTexture._mipmapGenBindGroup[faceIndex] = webgpuHardwareTexture._mipmapGenBindGroup[faceIndex] || [];\r\n                webgpuHardwareTexture._mipmapGenBindGroup[faceIndex][i - 1] = bindGroup;\r\n            }\r\n\r\n            passEncoder.setPipeline(pipeline);\r\n            passEncoder.setBindGroup(0, bindGroup);\r\n            passEncoder.draw(4, 1, 0, 0);\r\n            passEncoder.end();\r\n        }\r\n\r\n        commandEncoder!.popDebugGroup?.();\r\n\r\n        if (useOwnCommandEncoder) {\r\n            this._device.queue.submit([commandEncoder!.finish()]);\r\n            commandEncoder = null as any;\r\n        }\r\n    }\r\n\r\n    public createGPUTextureForInternalTexture(\r\n        texture: InternalTexture,\r\n        width?: number,\r\n        height?: number,\r\n        depth?: number,\r\n        creationFlags?: number,\r\n        dontCreateMSAATexture?: boolean\r\n    ): WebGPUHardwareTexture {\r\n        if (!texture._hardwareTexture) {\r\n            texture._hardwareTexture = new WebGPUHardwareTexture(this._engine);\r\n        }\r\n\r\n        if (width === undefined) {\r\n            width = texture.width;\r\n        }\r\n        if (height === undefined) {\r\n            height = texture.height;\r\n        }\r\n        if (depth === undefined) {\r\n            depth = texture.depth;\r\n        }\r\n\r\n        const gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n        const isStorageTexture = ((creationFlags ?? 0) & Constants.TEXTURE_CREATIONFLAG_STORAGE) !== 0;\r\n\r\n        gpuTextureWrapper.format = WebGPUTextureHelper.GetWebGPUTextureFormat(texture.type, texture.format, texture._useSRGBBuffer);\r\n\r\n        gpuTextureWrapper.textureUsages =\r\n            texture._source === InternalTextureSource.RenderTarget || texture.source === InternalTextureSource.MultiRenderTarget\r\n                ? WebGPUConstants.TextureUsage.TextureBinding | WebGPUConstants.TextureUsage.CopySrc | WebGPUConstants.TextureUsage.RenderAttachment\r\n                : texture._source === InternalTextureSource.DepthStencil\r\n                  ? WebGPUConstants.TextureUsage.TextureBinding | WebGPUConstants.TextureUsage.RenderAttachment\r\n                  : -1;\r\n\r\n        gpuTextureWrapper.textureAdditionalUsages = isStorageTexture ? WebGPUConstants.TextureUsage.StorageBinding : 0;\r\n\r\n        const hasMipMaps = texture.generateMipMaps;\r\n        const layerCount = depth || 1;\r\n        let mipmapCount;\r\n        if (texture._maxLodLevel !== null) {\r\n            mipmapCount = texture._maxLodLevel;\r\n        } else {\r\n            mipmapCount = hasMipMaps ? WebGPUTextureHelper.ComputeNumMipmapLevels(width, height) : 1;\r\n        }\r\n\r\n        if (texture.isCube) {\r\n            const gpuTexture = this.createCubeTexture(\r\n                { width, height },\r\n                texture.generateMipMaps,\r\n                texture.generateMipMaps,\r\n                texture.invertY,\r\n                false,\r\n                gpuTextureWrapper.format,\r\n                1,\r\n                this._commandEncoderForCreation,\r\n                gpuTextureWrapper.textureUsages,\r\n                gpuTextureWrapper.textureAdditionalUsages,\r\n                texture.label\r\n            );\r\n\r\n            gpuTextureWrapper.set(gpuTexture);\r\n\r\n            const arrayLayerCount = texture.is3D ? 1 : layerCount;\r\n            const format = WebGPUTextureHelper.GetDepthFormatOnly(gpuTextureWrapper.format);\r\n            const aspect = WebGPUTextureHelper.HasDepthAndStencilAspects(gpuTextureWrapper.format) ? WebGPUConstants.TextureAspect.DepthOnly : WebGPUConstants.TextureAspect.All;\r\n            const dimension = texture.is2DArray ? WebGPUConstants.TextureViewDimension.CubeArray : WebGPUConstants.TextureViewDimension.Cube;\r\n\r\n            gpuTextureWrapper.createView(\r\n                {\r\n                    label: `BabylonWebGPUDevice${this._engine.uniqueId}_TextureViewCube${texture.is2DArray ? \"_Array\" + arrayLayerCount : \"\"}_${width}x${height}_${\r\n                        hasMipMaps ? \"wmips\" : \"womips\"\r\n                    }_${format}_${dimension}_${aspect}_${texture.label ?? \"noname\"}`,\r\n                    format,\r\n                    dimension,\r\n                    mipLevelCount: mipmapCount,\r\n                    baseArrayLayer: 0,\r\n                    baseMipLevel: 0,\r\n                    arrayLayerCount: 6,\r\n                    aspect,\r\n                },\r\n                isStorageTexture\r\n            );\r\n        } else {\r\n            const gpuTexture = this.createTexture(\r\n                { width, height, layers: layerCount },\r\n                texture.generateMipMaps,\r\n                texture.generateMipMaps,\r\n                texture.invertY,\r\n                false,\r\n                texture.is3D,\r\n                gpuTextureWrapper.format,\r\n                1,\r\n                this._commandEncoderForCreation,\r\n                gpuTextureWrapper.textureUsages,\r\n                gpuTextureWrapper.textureAdditionalUsages,\r\n                texture.label\r\n            );\r\n\r\n            gpuTextureWrapper.set(gpuTexture);\r\n\r\n            const arrayLayerCount = texture.is3D ? 1 : layerCount;\r\n            const format = WebGPUTextureHelper.GetDepthFormatOnly(gpuTextureWrapper.format);\r\n            const aspect = WebGPUTextureHelper.HasDepthAndStencilAspects(gpuTextureWrapper.format) ? WebGPUConstants.TextureAspect.DepthOnly : WebGPUConstants.TextureAspect.All;\r\n            const dimension = texture.is2DArray\r\n                ? WebGPUConstants.TextureViewDimension.E2dArray\r\n                : texture.is3D\r\n                  ? WebGPUConstants.TextureDimension.E3d\r\n                  : WebGPUConstants.TextureViewDimension.E2d;\r\n\r\n            gpuTextureWrapper.createView(\r\n                {\r\n                    label: `BabylonWebGPUDevice${this._engine.uniqueId}_TextureView${texture.is3D ? \"3D\" : \"2D\"}${\r\n                        texture.is2DArray ? \"_Array\" + arrayLayerCount : \"\"\r\n                    }_${width}x${height}${texture.is3D ? \"x\" + layerCount : \"\"}_${hasMipMaps ? \"wmips\" : \"womips\"}_${format}_${dimension}_${aspect}_${texture.label ?? \"noname\"}`,\r\n                    format,\r\n                    dimension,\r\n                    mipLevelCount: mipmapCount,\r\n                    baseArrayLayer: 0,\r\n                    baseMipLevel: 0,\r\n                    arrayLayerCount,\r\n                    aspect,\r\n                },\r\n                isStorageTexture\r\n            );\r\n        }\r\n\r\n        texture.width = texture.baseWidth = width;\r\n        texture.height = texture.baseHeight = height;\r\n        texture.depth = texture.baseDepth = depth;\r\n\r\n        if (!dontCreateMSAATexture) {\r\n            this.createMSAATexture(texture, texture.samples);\r\n        }\r\n\r\n        return gpuTextureWrapper;\r\n    }\r\n\r\n    public createMSAATexture(texture: InternalTexture, samples: number, releaseExisting = true, index = 0): void {\r\n        const gpuTextureWrapper = texture._hardwareTexture as Nullable<WebGPUHardwareTexture>;\r\n\r\n        if (releaseExisting) {\r\n            gpuTextureWrapper?.releaseMSAATexture();\r\n        }\r\n\r\n        if (!gpuTextureWrapper || (samples ?? 1) <= 1) {\r\n            return;\r\n        }\r\n\r\n        const width = texture.width;\r\n        const height = texture.height;\r\n\r\n        const gpuMSAATexture = this.createTexture(\r\n            { width, height, layers: 1 },\r\n            false,\r\n            false,\r\n            false,\r\n            false,\r\n            false,\r\n            gpuTextureWrapper.format,\r\n            samples,\r\n            this._commandEncoderForCreation,\r\n            WebGPUConstants.TextureUsage.RenderAttachment,\r\n            0,\r\n            texture.label ? \"MSAA_\" + texture.label : \"MSAA\"\r\n        );\r\n        gpuTextureWrapper.setMSAATexture(gpuMSAATexture, index);\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                                  Update\r\n    //------------------------------------------------------------------------------\r\n\r\n    public updateCubeTextures(\r\n        imageBitmaps: ImageBitmap[] | Uint8Array[],\r\n        gpuTexture: GPUTexture,\r\n        width: number,\r\n        height: number,\r\n        format: GPUTextureFormat,\r\n        invertY = false,\r\n        premultiplyAlpha = false,\r\n        offsetX = 0,\r\n        offsetY = 0\r\n    ): void {\r\n        const faces = [0, 3, 1, 4, 2, 5];\r\n\r\n        for (let f = 0; f < faces.length; ++f) {\r\n            const imageBitmap = imageBitmaps[faces[f]];\r\n\r\n            this.updateTexture(imageBitmap, gpuTexture, width, height, 1, format, f, 0, invertY, premultiplyAlpha, offsetX, offsetY);\r\n        }\r\n    }\r\n\r\n    // TODO WEBGPU handle data source not being in the same format than the destination texture?\r\n    public updateTexture(\r\n        imageBitmap: ImageBitmap | Uint8Array | ImageData | HTMLImageElement | HTMLVideoElement | VideoFrame | HTMLCanvasElement | OffscreenCanvas,\r\n        texture: GPUTexture | InternalTexture,\r\n        width: number,\r\n        height: number,\r\n        layers: number,\r\n        format: GPUTextureFormat,\r\n        faceIndex: number = 0,\r\n        mipLevel: number = 0,\r\n        invertY = false,\r\n        premultiplyAlpha = false,\r\n        offsetX = 0,\r\n        offsetY = 0,\r\n        allowGPUOptimization?: boolean\r\n    ): void {\r\n        const gpuTexture = WebGPUTextureHelper.IsInternalTexture(texture) ? (texture._hardwareTexture as WebGPUHardwareTexture).underlyingResource! : texture;\r\n        const blockInformation = WebGPUTextureHelper.GetBlockInformationFromFormat(format);\r\n        const gpuOrHdwTexture = WebGPUTextureHelper.IsInternalTexture(texture) ? (texture._hardwareTexture as WebGPUHardwareTexture) : texture;\r\n\r\n        const textureCopyView: GPUImageCopyTextureTagged = {\r\n            texture: gpuTexture,\r\n            origin: {\r\n                x: offsetX,\r\n                y: offsetY,\r\n                z: Math.max(faceIndex, 0),\r\n            },\r\n            mipLevel: mipLevel,\r\n            premultipliedAlpha: premultiplyAlpha,\r\n        };\r\n\r\n        const textureExtent = {\r\n            width: Math.ceil(width / blockInformation.width) * blockInformation.width,\r\n            height: Math.ceil(height / blockInformation.height) * blockInformation.height,\r\n            depthOrArrayLayers: layers || 1,\r\n        };\r\n\r\n        if ((imageBitmap as Uint8Array).byteLength !== undefined) {\r\n            imageBitmap = imageBitmap as Uint8Array;\r\n\r\n            const bytesPerRow = Math.ceil(width / blockInformation.width) * blockInformation.length;\r\n            const aligned = Math.ceil(bytesPerRow / 256) * 256 === bytesPerRow;\r\n\r\n            if (aligned) {\r\n                const commandEncoder = this._device.createCommandEncoder({});\r\n\r\n                const buffer = this._bufferManager.createRawBuffer(\r\n                    imageBitmap.byteLength,\r\n                    WebGPUConstants.BufferUsage.MapWrite | WebGPUConstants.BufferUsage.CopySrc,\r\n                    true,\r\n                    \"TempBufferForUpdateTexture\" + (gpuTexture ? \"_\" + gpuTexture.label : \"\")\r\n                );\r\n\r\n                const arrayBuffer = buffer.getMappedRange();\r\n\r\n                new Uint8Array(arrayBuffer).set(imageBitmap);\r\n\r\n                buffer.unmap();\r\n\r\n                commandEncoder.copyBufferToTexture(\r\n                    {\r\n                        buffer: buffer,\r\n                        offset: 0,\r\n                        bytesPerRow,\r\n                        rowsPerImage: height,\r\n                    },\r\n                    textureCopyView,\r\n                    textureExtent\r\n                );\r\n\r\n                this._device.queue.submit([commandEncoder.finish()]);\r\n\r\n                this._bufferManager.releaseBuffer(buffer);\r\n            } else {\r\n                this._device.queue.writeTexture(\r\n                    textureCopyView,\r\n                    imageBitmap,\r\n                    {\r\n                        offset: 0,\r\n                        bytesPerRow,\r\n                        rowsPerImage: height,\r\n                    },\r\n                    textureExtent\r\n                );\r\n            }\r\n\r\n            if (invertY || premultiplyAlpha) {\r\n                if (WebGPUTextureHelper.IsInternalTexture(texture)) {\r\n                    const dontUseRect = offsetX === 0 && offsetY === 0 && width === texture.width && height === texture.height;\r\n                    this.invertYPreMultiplyAlpha(\r\n                        gpuOrHdwTexture,\r\n                        texture.width,\r\n                        texture.height,\r\n                        format,\r\n                        invertY,\r\n                        premultiplyAlpha,\r\n                        faceIndex,\r\n                        mipLevel,\r\n                        layers || 1,\r\n                        offsetX,\r\n                        offsetY,\r\n                        dontUseRect ? 0 : width,\r\n                        dontUseRect ? 0 : height,\r\n                        undefined,\r\n                        allowGPUOptimization\r\n                    );\r\n                } else {\r\n                    // we should never take this code path\r\n                    // eslint-disable-next-line no-throw-literal\r\n                    throw \"updateTexture: Can't process the texture data because a GPUTexture was provided instead of an InternalTexture!\";\r\n                }\r\n            }\r\n        } else {\r\n            imageBitmap = imageBitmap as ImageBitmap | ImageData | HTMLImageElement | HTMLVideoElement | VideoFrame | HTMLCanvasElement | OffscreenCanvas;\r\n            this._device.queue.copyExternalImageToTexture({ source: imageBitmap, flipY: invertY }, textureCopyView, textureExtent);\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async\r\n    public readPixels(\r\n        texture: GPUTexture,\r\n        x: number,\r\n        y: number,\r\n        width: number,\r\n        height: number,\r\n        format: GPUTextureFormat,\r\n        faceIndex: number = 0,\r\n        mipLevel: number = 0,\r\n        buffer: Nullable<ArrayBufferView> = null,\r\n        noDataConversion = false\r\n    ): Promise<ArrayBufferView> {\r\n        const blockInformation = WebGPUTextureHelper.GetBlockInformationFromFormat(format);\r\n\r\n        const bytesPerRow = Math.ceil(width / blockInformation.width) * blockInformation.length;\r\n\r\n        const bytesPerRowAligned = Math.ceil(bytesPerRow / 256) * 256;\r\n\r\n        const size = bytesPerRowAligned * height;\r\n\r\n        const gpuBuffer = this._bufferManager.createRawBuffer(\r\n            size,\r\n            WebGPUConstants.BufferUsage.MapRead | WebGPUConstants.BufferUsage.CopyDst,\r\n            undefined,\r\n            \"TempBufferForReadPixels\" + (texture.label ? \"_\" + texture.label : \"\")\r\n        );\r\n\r\n        const commandEncoder = this._device.createCommandEncoder({});\r\n\r\n        commandEncoder.copyTextureToBuffer(\r\n            {\r\n                texture,\r\n                mipLevel,\r\n                origin: {\r\n                    x,\r\n                    y,\r\n                    z: Math.max(faceIndex, 0),\r\n                },\r\n            },\r\n            {\r\n                buffer: gpuBuffer,\r\n                offset: 0,\r\n                bytesPerRow: bytesPerRowAligned,\r\n            },\r\n            {\r\n                width,\r\n                height,\r\n                depthOrArrayLayers: 1,\r\n            }\r\n        );\r\n\r\n        this._device.queue.submit([commandEncoder.finish()]);\r\n\r\n        return this._bufferManager.readDataFromBuffer(\r\n            gpuBuffer,\r\n            size,\r\n            width,\r\n            height,\r\n            bytesPerRow,\r\n            bytesPerRowAligned,\r\n            WebGPUTextureHelper.GetTextureTypeFromFormat(format),\r\n            0,\r\n            buffer,\r\n            true,\r\n            noDataConversion\r\n        );\r\n    }\r\n\r\n    //------------------------------------------------------------------------------\r\n    //                              Dispose\r\n    //------------------------------------------------------------------------------\r\n\r\n    public releaseTexture(texture: InternalTexture | GPUTexture): void {\r\n        if (WebGPUTextureHelper.IsInternalTexture(texture)) {\r\n            const hardwareTexture = texture._hardwareTexture;\r\n            const irradianceTexture = texture._irradianceTexture;\r\n\r\n            // We can't destroy the objects just now because they could be used in the current frame - we delay the destroying after the end of the frame\r\n            this._deferredReleaseTextures.push([hardwareTexture, irradianceTexture]);\r\n        } else {\r\n            this._deferredReleaseTextures.push([texture, null]);\r\n        }\r\n    }\r\n\r\n    public destroyDeferredTextures(): void {\r\n        for (let i = 0; i < this._deferredReleaseTextures.length; ++i) {\r\n            const [hardwareTexture, irradianceTexture] = this._deferredReleaseTextures[i];\r\n\r\n            if (hardwareTexture) {\r\n                if (WebGPUTextureHelper.IsHardwareTexture(hardwareTexture)) {\r\n                    hardwareTexture.release();\r\n                } else {\r\n                    hardwareTexture.destroy();\r\n                }\r\n            }\r\n            irradianceTexture?.dispose();\r\n        }\r\n\r\n        this._deferredReleaseTextures.length = 0;\r\n    }\r\n}\r\n"]}