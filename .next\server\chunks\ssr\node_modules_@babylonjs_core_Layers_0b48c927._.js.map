{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/thinEffectLayer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/thinEffectLayer.ts"], "sourcesContent": ["import type { SmartArray } from \"../Misc/smartArray\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport type { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { EffectWrapperCreationOptions } from \"core/Materials/effectRenderer\";\r\nimport { EffectWrapper } from \"core/Materials/effectRenderer\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport { EffectFallbacks } from \"../Materials/effectFallbacks\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport { AddClipPlaneUniforms, BindClipPlane, PrepareStringDefinesForClipPlanes } from \"../Materials/clipPlaneMaterialHelper\";\r\nimport { BindMorphTargetParameters, PrepareDefinesAndAttributesForMorphTargets, PushAttributesForInstances } from \"../Materials/materialHelper.functions\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\nimport { ObjectRenderer } from \"core/Rendering/objectRenderer\";\r\nimport type { Vector2 } from \"../Maths/math.vector\";\r\nimport { Engine } from \"core/Engines/engine\";\r\n\r\n/**\r\n * Special Glow Blur post process only blurring the alpha channel\r\n * It enforces keeping the most luminous color in the color channel.\r\n * @internal\r\n */\r\nexport class ThinGlowBlurPostProcess extends EffectWrapper {\r\n    /**\r\n     * The fragment shader url\r\n     */\r\n    public static readonly FragmentUrl = \"glowBlurPostProcess\";\r\n\r\n    /**\r\n     * The list of uniforms used by the effect\r\n     */\r\n    public static readonly Uniforms = [\"screenSize\", \"direction\", \"blurWidth\"];\r\n\r\n    constructor(\r\n        name: string,\r\n        engine: Nullable<AbstractEngine> = null,\r\n        public direction: Vector2,\r\n        public kernel: number,\r\n        options?: EffectWrapperCreationOptions\r\n    ) {\r\n        super({\r\n            ...options,\r\n            name,\r\n            engine: engine || Engine.LastCreatedEngine!,\r\n            useShaderStore: true,\r\n            useAsPostProcess: true,\r\n            fragmentShader: ThinGlowBlurPostProcess.FragmentUrl,\r\n            uniforms: ThinGlowBlurPostProcess.Uniforms,\r\n        });\r\n    }\r\n\r\n    protected override _gatherImports(useWebGPU: boolean, list: Promise<any>[]) {\r\n        if (useWebGPU) {\r\n            this._webGPUReady = true;\r\n            list.push(import(\"../ShadersWGSL/glowBlurPostProcess.fragment\"));\r\n        } else {\r\n            list.push(import(\"../Shaders/glowBlurPostProcess.fragment\"));\r\n        }\r\n\r\n        super._gatherImports(useWebGPU, list);\r\n    }\r\n\r\n    public textureWidth: number = 0;\r\n\r\n    public textureHeight: number = 0;\r\n\r\n    public override bind() {\r\n        super.bind();\r\n        this._drawWrapper.effect!.setFloat2(\"screenSize\", this.textureWidth, this.textureHeight);\r\n        this._drawWrapper.effect!.setVector2(\"direction\", this.direction);\r\n        this._drawWrapper.effect!.setFloat(\"blurWidth\", this.kernel);\r\n    }\r\n}\r\n\r\n/**\r\n * Effect layer options. This helps customizing the behaviour\r\n * of the effect layer.\r\n */\r\nexport interface IThinEffectLayerOptions {\r\n    /**\r\n     * Multiplication factor apply to the canvas size to compute the render target size\r\n     * used to generated the glowing objects (the smaller the faster). Default: 0.5\r\n     */\r\n    mainTextureRatio?: number;\r\n\r\n    /**\r\n     * Enforces a fixed size texture to ensure resize independent blur. Default: undefined\r\n     */\r\n    mainTextureFixedSize?: number;\r\n\r\n    /**\r\n     * The type of the main texture. Default: TEXTURETYPE_UNSIGNED_BYTE\r\n     */\r\n    mainTextureType?: number;\r\n\r\n    /**\r\n     * Alpha blending mode used to apply the blur. Default depends of the implementation. Default: ALPHA_COMBINE\r\n     */\r\n    alphaBlendingMode?: number;\r\n\r\n    /**\r\n     * The camera attached to the layer. Default: null\r\n     */\r\n    camera?: Nullable<Camera>;\r\n\r\n    /**\r\n     * The rendering group to draw the layer in. Default: -1\r\n     */\r\n    renderingGroupId?: number;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class ThinEffectLayer {\r\n    private _vertexBuffers: { [key: string]: Nullable<VertexBuffer> } = {};\r\n    private _indexBuffer: Nullable<DataBuffer>;\r\n    private _mergeDrawWrapper: DrawWrapper[];\r\n    private _dontCheckIfReady = false;\r\n\r\n    protected _scene: Scene;\r\n    protected _engine: AbstractEngine;\r\n    /** @internal */\r\n    public _options: Required<IThinEffectLayerOptions>;\r\n    protected _objectRenderer: ObjectRenderer;\r\n    /** @internal */\r\n    public _shouldRender = true;\r\n    /** @internal */\r\n    public _emissiveTextureAndColor: { texture: Nullable<BaseTexture>; color: Color4 } = { texture: null, color: new Color4() };\r\n    /** @internal */\r\n    public _effectIntensity: { [meshUniqueId: number]: number } = {};\r\n    /** @internal */\r\n    public _postProcesses: EffectWrapper[] = [];\r\n\r\n    /**\r\n     * Force all the effect layers to compile to glsl even on WebGPU engines.\r\n     * False by default. This is mostly meant for backward compatibility.\r\n     */\r\n    public static ForceGLSL = false;\r\n\r\n    /**\r\n     * The name of the layer\r\n     */\r\n    public name: string;\r\n\r\n    /**\r\n     * The clear color of the texture used to generate the glow map.\r\n     */\r\n    public neutralColor: Color4 = new Color4();\r\n\r\n    /**\r\n     * Specifies whether the effect layer is enabled or not.\r\n     */\r\n    public isEnabled: boolean = true;\r\n\r\n    /**\r\n     * Gets/sets the camera attached to the layer.\r\n     */\r\n    public get camera(): Nullable<Camera> {\r\n        return this._options.camera;\r\n    }\r\n\r\n    public set camera(camera: Nullable<Camera>) {\r\n        this._options.camera = camera;\r\n    }\r\n\r\n    /**\r\n     * Gets the rendering group id the layer should render in.\r\n     */\r\n    public get renderingGroupId(): number {\r\n        return this._options.renderingGroupId;\r\n    }\r\n    public set renderingGroupId(renderingGroupId: number) {\r\n        this._options.renderingGroupId = renderingGroupId;\r\n    }\r\n\r\n    /**\r\n     * Specifies if the bounding boxes should be rendered normally or if they should undergo the effect of the layer\r\n     */\r\n    public disableBoundingBoxesFromEffectLayer = false;\r\n\r\n    /**\r\n     * An event triggered when the effect layer has been disposed.\r\n     */\r\n    public onDisposeObservable = new Observable<ThinEffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when the effect layer is about rendering the main texture with the glowy parts.\r\n     */\r\n    public onBeforeRenderLayerObservable = new Observable<ThinEffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when the generated texture is being merged in the scene.\r\n     */\r\n    public onBeforeComposeObservable = new Observable<ThinEffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when the mesh is rendered into the effect render target.\r\n     */\r\n    public onBeforeRenderMeshToEffect = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * An event triggered after the mesh has been rendered into the effect render target.\r\n     */\r\n    public onAfterRenderMeshToEffect = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * An event triggered when the generated texture has been merged in the scene.\r\n     */\r\n    public onAfterComposeObservable = new Observable<ThinEffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when the layer is being blurred.\r\n     */\r\n    public onBeforeBlurObservable = new Observable<ThinEffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when the layer has been blurred.\r\n     */\r\n    public onAfterBlurObservable = new Observable<ThinEffectLayer>();\r\n\r\n    /**\r\n     * Gets the object renderer used to render objects in the layer\r\n     */\r\n    public get objectRenderer() {\r\n        return this._objectRenderer;\r\n    }\r\n\r\n    protected _shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    /**\r\n     * Gets the shader language used in this material.\r\n     */\r\n    public get shaderLanguage(): ShaderLanguage {\r\n        return this._shaderLanguage;\r\n    }\r\n\r\n    private _materialForRendering: { [id: string]: [AbstractMesh, Material] } = {};\r\n\r\n    /**\r\n     * Sets a specific material to be used to render a mesh/a list of meshes in the layer\r\n     * @param mesh mesh or array of meshes\r\n     * @param material material to use by the layer when rendering the mesh(es). If undefined is passed, the specific material created by the layer will be used.\r\n     */\r\n    public setMaterialForRendering(mesh: AbstractMesh | AbstractMesh[], material?: Material): void {\r\n        this._objectRenderer.setMaterialForRendering(mesh, material);\r\n        if (Array.isArray(mesh)) {\r\n            for (let i = 0; i < mesh.length; ++i) {\r\n                const currentMesh = mesh[i];\r\n                if (!material) {\r\n                    delete this._materialForRendering[currentMesh.uniqueId];\r\n                } else {\r\n                    this._materialForRendering[currentMesh.uniqueId] = [currentMesh, material];\r\n                }\r\n            }\r\n        } else {\r\n            if (!material) {\r\n                delete this._materialForRendering[mesh.uniqueId];\r\n            } else {\r\n                this._materialForRendering[mesh.uniqueId] = [mesh, material];\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the intensity of the effect for a specific mesh.\r\n     * @param mesh The mesh to get the effect intensity for\r\n     * @returns The intensity of the effect for the mesh\r\n     */\r\n    public getEffectIntensity(mesh: AbstractMesh) {\r\n        return this._effectIntensity[mesh.uniqueId] ?? 1;\r\n    }\r\n\r\n    /**\r\n     * Sets the intensity of the effect for a specific mesh.\r\n     * @param mesh The mesh to set the effect intensity for\r\n     * @param intensity The intensity of the effect for the mesh\r\n     */\r\n    public setEffectIntensity(mesh: AbstractMesh, intensity: number): void {\r\n        this._effectIntensity[mesh.uniqueId] = intensity;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new effect Layer\r\n     * @param name The name of the layer\r\n     * @param scene The scene to use the layer in\r\n     * @param forceGLSL Use the GLSL code generation for the shader (even on WebGPU). Default is false\r\n     * @param dontCheckIfReady Specifies if the layer should disable checking whether all the post processes are ready (default: false). To save performance, this should be set to true and you should call `isReady` manually before rendering to the layer.\r\n     * @param _additionalImportShadersAsync Additional shaders to import when the layer is created\r\n     */\r\n    constructor(\r\n        name: string,\r\n        scene?: Scene,\r\n        forceGLSL = false,\r\n        dontCheckIfReady = false,\r\n        private _additionalImportShadersAsync?: () => Promise<void>\r\n    ) {\r\n        this.name = name;\r\n        this._scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        this._dontCheckIfReady = dontCheckIfReady;\r\n\r\n        const engine = this._scene.getEngine();\r\n\r\n        if (engine.isWebGPU && !forceGLSL && !ThinEffectLayer.ForceGLSL) {\r\n            this._shaderLanguage = ShaderLanguage.WGSL;\r\n        }\r\n\r\n        this._engine = this._scene.getEngine();\r\n\r\n        this._mergeDrawWrapper = [];\r\n\r\n        // Generate Buffers\r\n        this._generateIndexBuffer();\r\n        this._generateVertexBuffer();\r\n    }\r\n\r\n    /** @internal */\r\n    public _shadersLoaded = false;\r\n\r\n    /**\r\n     * Get the effect name of the layer.\r\n     * @returns The effect name\r\n     */\r\n    public getEffectName(): string {\r\n        return \"\";\r\n    }\r\n\r\n    /**\r\n     * Checks for the readiness of the element composing the layer.\r\n     * @param _subMesh the mesh to check for\r\n     * @param _useInstances specify whether or not to use instances to render the mesh\r\n     * @returns true if ready otherwise, false\r\n     */\r\n    public isReady(_subMesh: SubMesh, _useInstances: boolean): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Returns whether or not the layer needs stencil enabled during the mesh rendering.\r\n     * @returns true if the effect requires stencil during the main canvas render pass.\r\n     */\r\n    public needStencil(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _createMergeEffect(): Effect {\r\n        throw new Error(\"Effect Layer: no merge effect defined\");\r\n    }\r\n\r\n    /** @internal */\r\n    public _createTextureAndPostProcesses(): void {}\r\n\r\n    /** @internal */\r\n    public bindTexturesForCompose: (effect: Effect) => void;\r\n\r\n    /** @internal */\r\n    public _internalCompose(_effect: Effect, _renderIndex: number): void {}\r\n\r\n    /** @internal */\r\n    public _setEmissiveTextureAndColor(_mesh: Mesh, _subMesh: SubMesh, _material: Material): void {}\r\n\r\n    /** @internal */\r\n    public _numInternalDraws(): number {\r\n        return 1;\r\n    }\r\n\r\n    /** @internal */\r\n    public _init(options: IThinEffectLayerOptions): void {\r\n        // Adapt options\r\n        this._options = {\r\n            mainTextureRatio: 0.5,\r\n            mainTextureFixedSize: 0,\r\n            mainTextureType: Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n            alphaBlendingMode: Constants.ALPHA_COMBINE,\r\n            camera: null,\r\n            renderingGroupId: -1,\r\n            ...options,\r\n        };\r\n\r\n        this._createObjectRenderer();\r\n    }\r\n\r\n    private _generateIndexBuffer(): void {\r\n        // Indices\r\n        const indices = [];\r\n        indices.push(0);\r\n        indices.push(1);\r\n        indices.push(2);\r\n\r\n        indices.push(0);\r\n        indices.push(2);\r\n        indices.push(3);\r\n\r\n        this._indexBuffer = this._engine.createIndexBuffer(indices);\r\n    }\r\n\r\n    private _generateVertexBuffer(): void {\r\n        // VBO\r\n        const vertices = [];\r\n        vertices.push(1, 1);\r\n        vertices.push(-1, 1);\r\n        vertices.push(-1, -1);\r\n        vertices.push(1, -1);\r\n\r\n        const vertexBuffer = new VertexBuffer(this._engine, vertices, VertexBuffer.PositionKind, false, false, 2);\r\n        this._vertexBuffers[VertexBuffer.PositionKind] = vertexBuffer;\r\n    }\r\n\r\n    protected _createObjectRenderer(): void {\r\n        this._objectRenderer = new ObjectRenderer(`ObjectRenderer for thin effect layer ${this.name}`, this._scene, {\r\n            doNotChangeAspectRatio: true,\r\n        });\r\n        this._objectRenderer.activeCamera = this._options.camera;\r\n        this._objectRenderer.renderParticles = false;\r\n        this._objectRenderer.renderList = null;\r\n\r\n        // Prevent package size in es6 (getBoundingBoxRenderer might not be present)\r\n        const hasBoundingBoxRenderer = !!this._scene.getBoundingBoxRenderer;\r\n\r\n        let boundingBoxRendererEnabled = false;\r\n        if (hasBoundingBoxRenderer) {\r\n            this._objectRenderer.onBeforeRenderObservable.add(() => {\r\n                boundingBoxRendererEnabled = this._scene.getBoundingBoxRenderer().enabled;\r\n                this._scene.getBoundingBoxRenderer().enabled = !this.disableBoundingBoxesFromEffectLayer && boundingBoxRendererEnabled;\r\n            });\r\n\r\n            this._objectRenderer.onAfterRenderObservable.add(() => {\r\n                this._scene.getBoundingBoxRenderer().enabled = boundingBoxRendererEnabled;\r\n            });\r\n        }\r\n\r\n        this._objectRenderer.customIsReadyFunction = (mesh: AbstractMesh, refreshRate: number, preWarm?: boolean) => {\r\n            if ((preWarm || refreshRate === 0) && mesh.subMeshes) {\r\n                for (let i = 0; i < mesh.subMeshes.length; ++i) {\r\n                    const subMesh = mesh.subMeshes[i];\r\n                    const material = subMesh.getMaterial();\r\n                    const renderingMesh = subMesh.getRenderingMesh();\r\n\r\n                    if (!material) {\r\n                        continue;\r\n                    }\r\n\r\n                    const batch = renderingMesh._getInstancesRenderList(subMesh._id, !!subMesh.getReplacementMesh());\r\n                    const hardwareInstancedRendering = batch.hardwareInstancedRendering[subMesh._id] || renderingMesh.hasThinInstances;\r\n\r\n                    this._setEmissiveTextureAndColor(renderingMesh, subMesh, material);\r\n\r\n                    if (!this._isSubMeshReady(subMesh, hardwareInstancedRendering, this._emissiveTextureAndColor.texture)) {\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n\r\n            return true;\r\n        };\r\n\r\n        // Custom render function\r\n        this._objectRenderer.customRenderFunction = (\r\n            opaqueSubMeshes: SmartArray<SubMesh>,\r\n            alphaTestSubMeshes: SmartArray<SubMesh>,\r\n            transparentSubMeshes: SmartArray<SubMesh>,\r\n            depthOnlySubMeshes: SmartArray<SubMesh>\r\n        ): void => {\r\n            this.onBeforeRenderLayerObservable.notifyObservers(this);\r\n\r\n            let index: number;\r\n\r\n            const engine = this._scene.getEngine();\r\n\r\n            if (depthOnlySubMeshes.length) {\r\n                engine.setColorWrite(false);\r\n                for (index = 0; index < depthOnlySubMeshes.length; index++) {\r\n                    this._renderSubMesh(depthOnlySubMeshes.data[index]);\r\n                }\r\n                engine.setColorWrite(true);\r\n            }\r\n\r\n            for (index = 0; index < opaqueSubMeshes.length; index++) {\r\n                this._renderSubMesh(opaqueSubMeshes.data[index]);\r\n            }\r\n\r\n            for (index = 0; index < alphaTestSubMeshes.length; index++) {\r\n                this._renderSubMesh(alphaTestSubMeshes.data[index]);\r\n            }\r\n\r\n            const previousAlphaMode = engine.getAlphaMode();\r\n\r\n            for (index = 0; index < transparentSubMeshes.length; index++) {\r\n                const subMesh = transparentSubMeshes.data[index];\r\n                const material = subMesh.getMaterial();\r\n                if (material && material.needDepthPrePass) {\r\n                    const engine = material.getScene().getEngine();\r\n                    engine.setColorWrite(false);\r\n                    this._renderSubMesh(subMesh);\r\n                    engine.setColorWrite(true);\r\n                }\r\n                this._renderSubMesh(subMesh, true);\r\n            }\r\n\r\n            engine.setAlphaMode(previousAlphaMode);\r\n        };\r\n    }\r\n\r\n    /** @internal */\r\n    public _addCustomEffectDefines(_defines: string[]): void {}\r\n\r\n    /** @internal */\r\n    public _internalIsSubMeshReady(subMesh: SubMesh, useInstances: boolean, emissiveTexture: Nullable<BaseTexture>): boolean {\r\n        const engine = this._scene.getEngine();\r\n        const mesh = subMesh.getMesh();\r\n\r\n        const renderingMaterial = mesh._internalAbstractMeshDataInfo._materialForRenderPass?.[engine.currentRenderPassId];\r\n\r\n        if (renderingMaterial) {\r\n            return renderingMaterial.isReadyForSubMesh(mesh, subMesh, useInstances);\r\n        }\r\n\r\n        const material = subMesh.getMaterial();\r\n\r\n        if (!material) {\r\n            return false;\r\n        }\r\n\r\n        if (this._useMeshMaterial(subMesh.getRenderingMesh())) {\r\n            return material.isReadyForSubMesh(subMesh.getMesh(), subMesh, useInstances);\r\n        }\r\n\r\n        const defines: string[] = [];\r\n\r\n        const attribs = [VertexBuffer.PositionKind];\r\n\r\n        let uv1 = false;\r\n        let uv2 = false;\r\n        const color = false;\r\n\r\n        // Diffuse\r\n        if (material) {\r\n            const needAlphaTest = material.needAlphaTestingForMesh(mesh);\r\n\r\n            const diffuseTexture = material.getAlphaTestTexture();\r\n            const needAlphaBlendFromDiffuse =\r\n                diffuseTexture && diffuseTexture.hasAlpha && ((material as any).useAlphaFromDiffuseTexture || (material as any)._useAlphaFromAlbedoTexture);\r\n\r\n            if (diffuseTexture && (needAlphaTest || needAlphaBlendFromDiffuse)) {\r\n                defines.push(\"#define DIFFUSE\");\r\n                if (mesh.isVerticesDataPresent(VertexBuffer.UV2Kind) && diffuseTexture.coordinatesIndex === 1) {\r\n                    defines.push(\"#define DIFFUSEUV2\");\r\n                    uv2 = true;\r\n                } else if (mesh.isVerticesDataPresent(VertexBuffer.UVKind)) {\r\n                    defines.push(\"#define DIFFUSEUV1\");\r\n                    uv1 = true;\r\n                }\r\n\r\n                if (needAlphaTest) {\r\n                    defines.push(\"#define ALPHATEST\");\r\n                    defines.push(\"#define ALPHATESTVALUE 0.4\");\r\n                }\r\n                if (!diffuseTexture.gammaSpace) {\r\n                    defines.push(\"#define DIFFUSE_ISLINEAR\");\r\n                }\r\n            }\r\n\r\n            const opacityTexture = (material as any).opacityTexture;\r\n            if (opacityTexture) {\r\n                defines.push(\"#define OPACITY\");\r\n                if (mesh.isVerticesDataPresent(VertexBuffer.UV2Kind) && opacityTexture.coordinatesIndex === 1) {\r\n                    defines.push(\"#define OPACITYUV2\");\r\n                    uv2 = true;\r\n                } else if (mesh.isVerticesDataPresent(VertexBuffer.UVKind)) {\r\n                    defines.push(\"#define OPACITYUV1\");\r\n                    uv1 = true;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Emissive\r\n        if (emissiveTexture) {\r\n            defines.push(\"#define EMISSIVE\");\r\n            if (mesh.isVerticesDataPresent(VertexBuffer.UV2Kind) && emissiveTexture.coordinatesIndex === 1) {\r\n                defines.push(\"#define EMISSIVEUV2\");\r\n                uv2 = true;\r\n            } else if (mesh.isVerticesDataPresent(VertexBuffer.UVKind)) {\r\n                defines.push(\"#define EMISSIVEUV1\");\r\n                uv1 = true;\r\n            }\r\n            if (!emissiveTexture.gammaSpace) {\r\n                defines.push(\"#define EMISSIVE_ISLINEAR\");\r\n            }\r\n        }\r\n\r\n        // Vertex\r\n        if (mesh.useVertexColors && mesh.isVerticesDataPresent(VertexBuffer.ColorKind) && mesh.hasVertexAlpha && material.transparencyMode !== Material.MATERIAL_OPAQUE) {\r\n            attribs.push(VertexBuffer.ColorKind);\r\n            defines.push(\"#define VERTEXALPHA\");\r\n        }\r\n\r\n        if (uv1) {\r\n            attribs.push(VertexBuffer.UVKind);\r\n            defines.push(\"#define UV1\");\r\n        }\r\n        if (uv2) {\r\n            attribs.push(VertexBuffer.UV2Kind);\r\n            defines.push(\"#define UV2\");\r\n        }\r\n\r\n        // Bones\r\n        const fallbacks = new EffectFallbacks();\r\n        if (mesh.useBones && mesh.computeBonesUsingShaders) {\r\n            attribs.push(VertexBuffer.MatricesIndicesKind);\r\n            attribs.push(VertexBuffer.MatricesWeightsKind);\r\n            if (mesh.numBoneInfluencers > 4) {\r\n                attribs.push(VertexBuffer.MatricesIndicesExtraKind);\r\n                attribs.push(VertexBuffer.MatricesWeightsExtraKind);\r\n            }\r\n\r\n            defines.push(\"#define NUM_BONE_INFLUENCERS \" + mesh.numBoneInfluencers);\r\n\r\n            const skeleton = mesh.skeleton;\r\n            if (skeleton && skeleton.isUsingTextureForMatrices) {\r\n                defines.push(\"#define BONETEXTURE\");\r\n            } else {\r\n                defines.push(\"#define BonesPerMesh \" + (skeleton ? skeleton.bones.length + 1 : 0));\r\n            }\r\n\r\n            if (mesh.numBoneInfluencers > 0) {\r\n                fallbacks.addCPUSkinningFallback(0, mesh);\r\n            }\r\n        } else {\r\n            defines.push(\"#define NUM_BONE_INFLUENCERS 0\");\r\n        }\r\n\r\n        // Morph targets\r\n        const numMorphInfluencers = mesh.morphTargetManager\r\n            ? PrepareDefinesAndAttributesForMorphTargets(\r\n                  mesh.morphTargetManager,\r\n                  defines,\r\n                  attribs,\r\n                  mesh,\r\n                  true, // usePositionMorph\r\n                  false, // useNormalMorph\r\n                  false, // useTangentMorph\r\n                  uv1, // useUVMorph\r\n                  uv2, // useUV2Morph\r\n                  color // useColorMorph\r\n              )\r\n            : 0;\r\n\r\n        // Instances\r\n        if (useInstances) {\r\n            defines.push(\"#define INSTANCES\");\r\n            PushAttributesForInstances(attribs);\r\n            if (subMesh.getRenderingMesh().hasThinInstances) {\r\n                defines.push(\"#define THIN_INSTANCES\");\r\n            }\r\n        }\r\n\r\n        // ClipPlanes\r\n        PrepareStringDefinesForClipPlanes(material, this._scene, defines);\r\n\r\n        this._addCustomEffectDefines(defines);\r\n\r\n        // Get correct effect\r\n        const drawWrapper = subMesh._getDrawWrapper(undefined, true)!;\r\n        const cachedDefines = drawWrapper.defines as string;\r\n        const join = defines.join(\"\\n\");\r\n        if (cachedDefines !== join) {\r\n            const uniforms = [\r\n                \"world\",\r\n                \"mBones\",\r\n                \"viewProjection\",\r\n                \"glowColor\",\r\n                \"morphTargetInfluences\",\r\n                \"morphTargetCount\",\r\n                \"boneTextureWidth\",\r\n                \"diffuseMatrix\",\r\n                \"emissiveMatrix\",\r\n                \"opacityMatrix\",\r\n                \"opacityIntensity\",\r\n                \"morphTargetTextureInfo\",\r\n                \"morphTargetTextureIndices\",\r\n                \"glowIntensity\",\r\n            ];\r\n\r\n            AddClipPlaneUniforms(uniforms);\r\n\r\n            drawWrapper.setEffect(\r\n                this._engine.createEffect(\r\n                    \"glowMapGeneration\",\r\n                    attribs,\r\n                    uniforms,\r\n                    [\"diffuseSampler\", \"emissiveSampler\", \"opacitySampler\", \"boneSampler\", \"morphTargets\"],\r\n                    join,\r\n                    fallbacks,\r\n                    undefined,\r\n                    undefined,\r\n                    { maxSimultaneousMorphTargets: numMorphInfluencers },\r\n                    this._shaderLanguage,\r\n                    this._shadersLoaded\r\n                        ? undefined\r\n                        : async () => {\r\n                              await this._importShadersAsync();\r\n                              this._shadersLoaded = true;\r\n                          }\r\n                ),\r\n                join\r\n            );\r\n        }\r\n\r\n        const effectIsReady = drawWrapper.effect!.isReady();\r\n\r\n        return effectIsReady && (this._dontCheckIfReady || (!this._dontCheckIfReady && this.isLayerReady()));\r\n    }\r\n\r\n    /** @internal */\r\n    public _isSubMeshReady(subMesh: SubMesh, useInstances: boolean, emissiveTexture: Nullable<BaseTexture>): boolean {\r\n        return this._internalIsSubMeshReady(subMesh, useInstances, emissiveTexture);\r\n    }\r\n\r\n    protected async _importShadersAsync(): Promise<void> {\r\n        if (this._shaderLanguage === ShaderLanguage.WGSL) {\r\n            await Promise.all([import(\"../ShadersWGSL/glowMapGeneration.vertex\"), import(\"../ShadersWGSL/glowMapGeneration.fragment\")]);\r\n        } else {\r\n            await Promise.all([import(\"../Shaders/glowMapGeneration.vertex\"), import(\"../Shaders/glowMapGeneration.fragment\")]);\r\n        }\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._additionalImportShadersAsync?.();\r\n    }\r\n\r\n    /** @internal */\r\n    public _internalIsLayerReady(): boolean {\r\n        let isReady = true;\r\n\r\n        for (let i = 0; i < this._postProcesses.length; i++) {\r\n            isReady = this._postProcesses[i].isReady() && isReady;\r\n        }\r\n\r\n        const numDraws = this._numInternalDraws();\r\n\r\n        for (let i = 0; i < numDraws; ++i) {\r\n            let currentEffect = this._mergeDrawWrapper[i];\r\n            if (!currentEffect) {\r\n                currentEffect = this._mergeDrawWrapper[i] = new DrawWrapper(this._engine);\r\n                currentEffect.setEffect(this._createMergeEffect());\r\n            }\r\n            isReady = currentEffect.effect!.isReady() && isReady;\r\n        }\r\n\r\n        return isReady;\r\n    }\r\n\r\n    /**\r\n     * Checks if the layer is ready to be used.\r\n     * @returns true if the layer is ready to be used\r\n     */\r\n    public isLayerReady(): boolean {\r\n        return this._internalIsLayerReady();\r\n    }\r\n\r\n    /**\r\n     * Renders the glowing part of the scene by blending the blurred glowing meshes on top of the rendered scene.\r\n     * @returns true if the rendering was successful\r\n     */\r\n    public compose(): boolean {\r\n        if (!this._dontCheckIfReady && !this.isLayerReady()) {\r\n            return false;\r\n        }\r\n\r\n        const engine = this._scene.getEngine();\r\n        const numDraws = this._numInternalDraws();\r\n\r\n        this.onBeforeComposeObservable.notifyObservers(this);\r\n\r\n        const previousAlphaMode = engine.getAlphaMode();\r\n\r\n        for (let i = 0; i < numDraws; ++i) {\r\n            const currentEffect = this._mergeDrawWrapper[i];\r\n\r\n            // Render\r\n            engine.enableEffect(currentEffect);\r\n            engine.setState(false);\r\n\r\n            // VBOs\r\n            engine.bindBuffers(this._vertexBuffers, this._indexBuffer, currentEffect.effect!);\r\n\r\n            // Go Blend.\r\n            engine.setAlphaMode(this._options.alphaBlendingMode);\r\n\r\n            // Blends the map on the main canvas.\r\n            this._internalCompose(currentEffect.effect!, i);\r\n        }\r\n\r\n        // Restore Alpha\r\n        engine.setAlphaMode(previousAlphaMode);\r\n\r\n        this.onAfterComposeObservable.notifyObservers(this);\r\n\r\n        return true;\r\n    }\r\n\r\n    /** @internal */\r\n    public _internalHasMesh(mesh: AbstractMesh): boolean {\r\n        if (this.renderingGroupId === -1 || mesh.renderingGroupId === this.renderingGroupId) {\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Determine if a given mesh will be used in the current effect.\r\n     * @param mesh mesh to test\r\n     * @returns true if the mesh will be used\r\n     */\r\n    public hasMesh(mesh: AbstractMesh): boolean {\r\n        return this._internalHasMesh(mesh);\r\n    }\r\n\r\n    /** @internal */\r\n    public _internalShouldRender(): boolean {\r\n        return this.isEnabled && this._shouldRender;\r\n    }\r\n\r\n    /**\r\n     * Returns true if the layer contains information to display, otherwise false.\r\n     * @returns true if the glow layer should be rendered\r\n     */\r\n    public shouldRender(): boolean {\r\n        return this._internalShouldRender();\r\n    }\r\n\r\n    /** @internal */\r\n    public _shouldRenderMesh(_mesh: AbstractMesh): boolean {\r\n        return true;\r\n    }\r\n\r\n    /** @internal */\r\n    public _internalCanRenderMesh(mesh: AbstractMesh, material: Material): boolean {\r\n        return !material.needAlphaBlendingForMesh(mesh);\r\n    }\r\n\r\n    /** @internal */\r\n    public _canRenderMesh(mesh: AbstractMesh, material: Material): boolean {\r\n        return this._internalCanRenderMesh(mesh, material);\r\n    }\r\n\r\n    protected _renderSubMesh(subMesh: SubMesh, enableAlphaMode: boolean = false): void {\r\n        if (!this._internalShouldRender()) {\r\n            return;\r\n        }\r\n\r\n        const material = subMesh.getMaterial();\r\n        const ownerMesh = subMesh.getMesh();\r\n        const replacementMesh = subMesh.getReplacementMesh();\r\n        const renderingMesh = subMesh.getRenderingMesh();\r\n        const effectiveMesh = subMesh.getEffectiveMesh();\r\n        const scene = this._scene;\r\n        const engine = scene.getEngine();\r\n\r\n        effectiveMesh._internalAbstractMeshDataInfo._isActiveIntermediate = false;\r\n\r\n        if (!material) {\r\n            return;\r\n        }\r\n\r\n        // Do not block in blend mode.\r\n        if (!this._canRenderMesh(renderingMesh, material)) {\r\n            return;\r\n        }\r\n\r\n        // Culling\r\n        let sideOrientation = material._getEffectiveOrientation(renderingMesh);\r\n        const mainDeterminant = effectiveMesh._getWorldMatrixDeterminant();\r\n        if (mainDeterminant < 0) {\r\n            sideOrientation = sideOrientation === Material.ClockWiseSideOrientation ? Material.CounterClockWiseSideOrientation : Material.ClockWiseSideOrientation;\r\n        }\r\n\r\n        const reverse = sideOrientation === Material.ClockWiseSideOrientation;\r\n        engine.setState(material.backFaceCulling, material.zOffset, undefined, reverse, material.cullBackFaces, undefined, material.zOffsetUnits);\r\n\r\n        // Managing instances\r\n        const batch = renderingMesh._getInstancesRenderList(subMesh._id, !!replacementMesh);\r\n        if (batch.mustReturn) {\r\n            return;\r\n        }\r\n\r\n        // Early Exit per mesh\r\n        if (!this._shouldRenderMesh(renderingMesh)) {\r\n            return;\r\n        }\r\n\r\n        const hardwareInstancedRendering = batch.hardwareInstancedRendering[subMesh._id] || renderingMesh.hasThinInstances;\r\n\r\n        this._setEmissiveTextureAndColor(renderingMesh, subMesh, material);\r\n\r\n        this.onBeforeRenderMeshToEffect.notifyObservers(ownerMesh);\r\n\r\n        if (this._useMeshMaterial(renderingMesh)) {\r\n            subMesh.getMaterial()!._glowModeEnabled = true;\r\n            renderingMesh.render(subMesh, enableAlphaMode, replacementMesh || undefined);\r\n            subMesh.getMaterial()!._glowModeEnabled = false;\r\n        } else if (this._isSubMeshReady(subMesh, hardwareInstancedRendering, this._emissiveTextureAndColor.texture)) {\r\n            const renderingMaterial = effectiveMesh._internalAbstractMeshDataInfo._materialForRenderPass?.[engine.currentRenderPassId];\r\n\r\n            let drawWrapper = subMesh._getDrawWrapper();\r\n            if (!drawWrapper && renderingMaterial) {\r\n                drawWrapper = renderingMaterial._getDrawWrapper();\r\n            }\r\n\r\n            if (!drawWrapper) {\r\n                return;\r\n            }\r\n\r\n            const effect = drawWrapper.effect!;\r\n\r\n            engine.enableEffect(drawWrapper);\r\n            if (!hardwareInstancedRendering) {\r\n                renderingMesh._bind(subMesh, effect, material.fillMode);\r\n            }\r\n\r\n            if (!renderingMaterial) {\r\n                effect.setMatrix(\"viewProjection\", scene.getTransformMatrix());\r\n                effect.setMatrix(\"world\", effectiveMesh.getWorldMatrix());\r\n                effect.setFloat4(\r\n                    \"glowColor\",\r\n                    this._emissiveTextureAndColor.color.r,\r\n                    this._emissiveTextureAndColor.color.g,\r\n                    this._emissiveTextureAndColor.color.b,\r\n                    this._emissiveTextureAndColor.color.a\r\n                );\r\n            } else {\r\n                renderingMaterial.bindForSubMesh(effectiveMesh.getWorldMatrix(), effectiveMesh as Mesh, subMesh);\r\n            }\r\n\r\n            if (!renderingMaterial) {\r\n                const needAlphaTest = material.needAlphaTestingForMesh(effectiveMesh);\r\n\r\n                const diffuseTexture = material.getAlphaTestTexture();\r\n                const needAlphaBlendFromDiffuse =\r\n                    diffuseTexture && diffuseTexture.hasAlpha && ((material as any).useAlphaFromDiffuseTexture || (material as any)._useAlphaFromAlbedoTexture);\r\n\r\n                if (diffuseTexture && (needAlphaTest || needAlphaBlendFromDiffuse)) {\r\n                    effect.setTexture(\"diffuseSampler\", diffuseTexture);\r\n                    const textureMatrix = diffuseTexture.getTextureMatrix();\r\n\r\n                    if (textureMatrix) {\r\n                        effect.setMatrix(\"diffuseMatrix\", textureMatrix);\r\n                    }\r\n                }\r\n\r\n                const opacityTexture = (material as any).opacityTexture;\r\n                if (opacityTexture) {\r\n                    effect.setTexture(\"opacitySampler\", opacityTexture);\r\n                    effect.setFloat(\"opacityIntensity\", opacityTexture.level);\r\n                    const textureMatrix = opacityTexture.getTextureMatrix();\r\n                    if (textureMatrix) {\r\n                        effect.setMatrix(\"opacityMatrix\", textureMatrix);\r\n                    }\r\n                }\r\n\r\n                // Glow emissive only\r\n                if (this._emissiveTextureAndColor.texture) {\r\n                    effect.setTexture(\"emissiveSampler\", this._emissiveTextureAndColor.texture);\r\n                    effect.setMatrix(\"emissiveMatrix\", this._emissiveTextureAndColor.texture.getTextureMatrix());\r\n                }\r\n\r\n                // Bones\r\n                if (renderingMesh.useBones && renderingMesh.computeBonesUsingShaders && renderingMesh.skeleton) {\r\n                    const skeleton = renderingMesh.skeleton;\r\n\r\n                    if (skeleton.isUsingTextureForMatrices) {\r\n                        const boneTexture = skeleton.getTransformMatrixTexture(renderingMesh);\r\n                        if (!boneTexture) {\r\n                            return;\r\n                        }\r\n\r\n                        effect.setTexture(\"boneSampler\", boneTexture);\r\n                        effect.setFloat(\"boneTextureWidth\", 4.0 * (skeleton.bones.length + 1));\r\n                    } else {\r\n                        effect.setMatrices(\"mBones\", skeleton.getTransformMatrices(renderingMesh));\r\n                    }\r\n                }\r\n\r\n                // Morph targets\r\n                BindMorphTargetParameters(renderingMesh, effect);\r\n                if (renderingMesh.morphTargetManager && renderingMesh.morphTargetManager.isUsingTextureForTargets) {\r\n                    renderingMesh.morphTargetManager._bind(effect);\r\n                }\r\n\r\n                // Alpha mode\r\n                if (enableAlphaMode) {\r\n                    engine.setAlphaMode(material.alphaMode);\r\n                }\r\n\r\n                // Intensity of effect\r\n                effect.setFloat(\"glowIntensity\", this.getEffectIntensity(renderingMesh));\r\n\r\n                // Clip planes\r\n                BindClipPlane(effect, material, scene);\r\n            }\r\n\r\n            // Draw\r\n            renderingMesh._processRendering(effectiveMesh, subMesh, effect, material.fillMode, batch, hardwareInstancedRendering, (isInstance, world) =>\r\n                effect.setMatrix(\"world\", world)\r\n            );\r\n        } else {\r\n            // Need to reset refresh rate of the main map\r\n            this._objectRenderer.resetRefreshCounter();\r\n        }\r\n\r\n        this.onAfterRenderMeshToEffect.notifyObservers(ownerMesh);\r\n    }\r\n\r\n    /** @internal */\r\n    public _useMeshMaterial(_mesh: AbstractMesh): boolean {\r\n        return false;\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        const vb = this._vertexBuffers[VertexBuffer.PositionKind];\r\n\r\n        if (vb) {\r\n            vb._rebuild();\r\n        }\r\n\r\n        this._generateIndexBuffer();\r\n    }\r\n\r\n    /**\r\n     * Dispose the effect layer and free resources.\r\n     */\r\n    public dispose(): void {\r\n        const vertexBuffer = this._vertexBuffers[VertexBuffer.PositionKind];\r\n        if (vertexBuffer) {\r\n            vertexBuffer.dispose();\r\n            this._vertexBuffers[VertexBuffer.PositionKind] = null;\r\n        }\r\n\r\n        if (this._indexBuffer) {\r\n            this._scene.getEngine()._releaseBuffer(this._indexBuffer);\r\n            this._indexBuffer = null;\r\n        }\r\n\r\n        for (const drawWrapper of this._mergeDrawWrapper) {\r\n            drawWrapper.dispose();\r\n        }\r\n        this._mergeDrawWrapper = [];\r\n\r\n        this._objectRenderer.dispose();\r\n\r\n        // Callback\r\n        this.onDisposeObservable.notifyObservers(this);\r\n\r\n        this.onDisposeObservable.clear();\r\n        this.onBeforeRenderLayerObservable.clear();\r\n        this.onBeforeComposeObservable.clear();\r\n        this.onBeforeRenderMeshToEffect.clear();\r\n        this.onAfterRenderMeshToEffect.clear();\r\n        this.onAfterComposeObservable.clear();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAIhD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAKjD,OAAO,EAAE,aAAa,EAAE,uCAAsC;AAG9D,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAIjD,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAE,oBAAoB,EAAE,aAAa,EAAE,iCAAiC,EAAE,MAAM,sCAAsC,CAAC;AAC9H,OAAO,EAAE,yBAAyB,EAAE,0CAA0C,EAAE,0BAA0B,EAAE,MAAM,uCAAuC,CAAC;AAE1J,OAAO,EAAE,cAAc,EAAE,uCAAsC;AAE/D,OAAO,EAAE,MAAM,EAAE,6BAA4B;;;;;;;;;;;;;AAOvC,MAAO,uBAAwB,4KAAQ,gBAAa;IAWtD,YACI,IAAY,EACZ,SAAmC,IAAI,EAChC,SAAkB,EAClB,MAAc,EACrB,OAAsC,CAAA;QAEtC,KAAK,CAAC;YACF,GAAG,OAAO;YACV,IAAI;YACJ,MAAM,EAAE,MAAM,6JAAI,SAAM,CAAC,iBAAkB;YAC3C,cAAc,EAAE,IAAI;YACpB,gBAAgB,EAAE,IAAI;YACtB,cAAc,EAAE,uBAAuB,CAAC,WAAW;YACnD,QAAQ,EAAE,uBAAuB,CAAC,QAAQ;SAC7C,CAAC,CAAC;QAZI,IAAA,CAAA,SAAS,GAAT,SAAS,CAAS;QAClB,IAAA,CAAA,MAAM,GAAN,MAAM,CAAQ;QAyBlB,IAAA,CAAA,YAAY,GAAW,CAAC,CAAC;QAEzB,IAAA,CAAA,aAAa,GAAW,CAAC,CAAC;IAfjC,CAAC;IAEkB,cAAc,CAAC,SAAkB,EAAE,IAAoB,EAAA;QACtE,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,6CAA6C,CAAC,CAAC,CAAC;QACrE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,yCAAyC,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,KAAK,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAMe,IAAI,GAAA;QAChB,KAAK,CAAC,IAAI,EAAE,CAAC;QACb,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACzF,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAClE,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IACjE,CAAC;;AAhDD;;GAEG,CACoB,wBAAA,WAAW,GAAG,qBAAqB,AAAxB,CAAyB;AAE3D;;GAEG,CACoB,wBAAA,QAAQ,GAAG;IAAC,YAAY;IAAE,WAAW;IAAE,WAAW;CAA1C,CAA4C;AAmFzE,MAAO,eAAe;IAyCxB;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAChC,CAAC;IAED,IAAW,MAAM,CAAC,MAAwB,EAAA;QACtC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;IAClC,CAAC;IAED;;OAEG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IAC1C,CAAC;IACD,IAAW,gBAAgB,CAAC,gBAAwB,EAAA;QAChD,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IACtD,CAAC;IA+CD;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAID;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAID;;;;OAIG,CACI,uBAAuB,CAAC,IAAmC,EAAE,QAAmB,EAAA;QACnF,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC7D,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;gBACnC,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC5B,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACZ,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBAC5D,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG;wBAAC,WAAW;wBAAE,QAAQ;qBAAC,CAAC;gBAC/E,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrD,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;oBAAC,IAAI;oBAAE,QAAQ;iBAAC,CAAC;YACjE,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,IAAkB,EAAA;QACxC,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,IAAkB,EAAE,SAAiB,EAAA;QAC3D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;IACrD,CAAC;IAED;;;;;;;OAOG,CACH,YACI,IAAY,EACZ,KAAa,EACb,SAAS,GAAG,KAAK,EACjB,gBAAgB,GAAG,KAAK,EAChB,6BAAmD,CAAA;QAAnD,IAAA,CAAA,6BAA6B,GAA7B,6BAA6B,CAAsB;QApLvD,IAAA,CAAA,cAAc,GAA8C,CAAA,CAAE,CAAC;QAG/D,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAOlC,cAAA,EAAgB,CACT,IAAA,CAAA,aAAa,GAAG,IAAI,CAAC;QAC5B,cAAA,EAAgB,CACT,IAAA,CAAA,wBAAwB,GAAsD;YAAE,OAAO,EAAE,IAAI;YAAE,KAAK,EAAE,kKAAI,SAAM,EAAE;QAAA,CAAE,CAAC;QAC5H,cAAA,EAAgB,CACT,IAAA,CAAA,gBAAgB,GAAuC,CAAA,CAAE,CAAC;QACjE,cAAA,EAAgB,CACT,IAAA,CAAA,cAAc,GAAoB,EAAE,CAAC;QAa5C;;WAEG,CACI,IAAA,CAAA,YAAY,GAAW,kKAAI,SAAM,EAAE,CAAC;QAE3C;;WAEG,CACI,IAAA,CAAA,SAAS,GAAY,IAAI,CAAC;QAuBjC;;WAEG,CACI,IAAA,CAAA,mCAAmC,GAAG,KAAK,CAAC;QAEnD;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,8JAAI,aAAU,EAAmB,CAAC;QAE/D;;WAEG,CACI,IAAA,CAAA,6BAA6B,GAAG,8JAAI,aAAU,EAAmB,CAAC;QAEzE;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,8JAAI,aAAU,EAAmB,CAAC;QAErE;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,8JAAI,aAAU,EAAgB,CAAC;QAEnE;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,8JAAI,aAAU,EAAgB,CAAC;QAElE;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,8JAAI,aAAU,EAAmB,CAAC;QAEpE;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,8JAAI,aAAU,EAAmB,CAAC;QAElE;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAG,8JAAI,aAAU,EAAmB,CAAC;QASvD,IAAA,CAAA,eAAe,GAAA,EAAA,uBAAA,GAAuB;QASxC,IAAA,CAAA,qBAAqB,GAA+C,CAAA,CAAE,CAAC;QA+E/E,cAAA,EAAgB,CACT,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QApB1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,KAAK,IAAW,4KAAW,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAE1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;YAC9D,IAAI,CAAC,eAAe,GAAA,EAAA,uBAAA,EAAsB,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAE5B,mBAAmB;QACnB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IAKD;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;;;;OAKG,CACI,OAAO,CAAC,QAAiB,EAAE,aAAsB,EAAA;QACpD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,WAAW,GAAA;QACd,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,cAAA,EAAgB,CACT,kBAAkB,GAAA;QACrB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC7D,CAAC;IAED,cAAA,EAAgB,CACT,8BAA8B,GAAA,CAAU,CAAC;IAKhD,cAAA,EAAgB,CACT,gBAAgB,CAAC,OAAe,EAAE,YAAoB,EAAA,CAAS,CAAC;IAEvE,cAAA,EAAgB,CACT,2BAA2B,CAAC,KAAW,EAAE,QAAiB,EAAE,SAAmB,EAAA,CAAS,CAAC;IAEhG,cAAA,EAAgB,CACT,iBAAiB,GAAA;QACpB,OAAO,CAAC,CAAC;IACb,CAAC;IAED,cAAA,EAAgB,CACT,KAAK,CAAC,OAAgC,EAAA;QACzC,gBAAgB;QAChB,IAAI,CAAC,QAAQ,GAAG;YACZ,gBAAgB,EAAE,GAAG;YACrB,oBAAoB,EAAE,CAAC;YACvB,eAAe,EAAE,SAAS,CAAC,yBAAyB;YACpD,iBAAiB,EAAE,SAAS,CAAC,aAAa;YAC1C,MAAM,EAAE,IAAI;YACZ,gBAAgB,EAAE,CAAC,CAAC;YACpB,GAAG,OAAO;SACb,CAAC;QAEF,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjC,CAAC;IAEO,oBAAoB,GAAA;QACxB,UAAU;QACV,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAChE,CAAC;IAEO,qBAAqB,GAAA;QACzB,MAAM;QACN,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAErB,MAAM,YAAY,GAAG,IAAI,wKAAY,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,2JAAE,eAAY,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1G,IAAI,CAAC,cAAc,0JAAC,eAAY,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC;IAClE,CAAC;IAES,qBAAqB,GAAA;QAC3B,IAAI,CAAC,eAAe,GAAG,uKAAI,iBAAc,CAAC,CAAA,qCAAA,EAAwC,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE;YACxG,sBAAsB,EAAE,IAAI;SAC/B,CAAC,CAAC;QACH,IAAI,CAAC,eAAe,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACzD,IAAI,CAAC,eAAe,CAAC,eAAe,GAAG,KAAK,CAAC;QAC7C,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvC,4EAA4E;QAC5E,MAAM,sBAAsB,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAEpE,IAAI,0BAA0B,GAAG,KAAK,CAAC;QACvC,IAAI,sBAAsB,EAAE,CAAC;YACzB,IAAI,CAAC,eAAe,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;gBACnD,0BAA0B,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,OAAO,CAAC;gBAC1E,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,mCAAmC,IAAI,0BAA0B,CAAC;YAC3H,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAClD,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,OAAO,GAAG,0BAA0B,CAAC;YAC9E,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,qBAAqB,GAAG,CAAC,IAAkB,EAAE,WAAmB,EAAE,OAAiB,EAAE,EAAE;YACxG,IAAI,CAAC,OAAO,IAAI,WAAW,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;oBAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAClC,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;oBACvC,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;oBAEjD,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACZ,SAAS;oBACb,CAAC;oBAED,MAAM,KAAK,GAAG,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;oBACjG,MAAM,0BAA0B,GAAG,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,gBAAgB,CAAC;oBAEnH,IAAI,CAAC,2BAA2B,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAEnE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,0BAA0B,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE,CAAC;wBACpG,OAAO,KAAK,CAAC;oBACjB,CAAC;gBACL,CAAC;YACL,CAAC;YAED,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC;QAEF,yBAAyB;QACzB,IAAI,CAAC,eAAe,CAAC,oBAAoB,GAAG,CACxC,eAAoC,EACpC,kBAAuC,EACvC,oBAAyC,EACzC,kBAAuC,EACnC,EAAE;YACN,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEzD,IAAI,KAAa,CAAC;YAElB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAEvC,IAAI,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBAC5B,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC5B,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBACzD,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBACxD,CAAC;gBACD,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;YAED,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACtD,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACrD,CAAC;YAED,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACzD,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,iBAAiB,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;YAEhD,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,oBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC3D,MAAM,OAAO,GAAG,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjD,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACvC,IAAI,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;oBACxC,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC;oBAC/C,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;oBAC7B,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACvC,CAAC;YAED,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QAC3C,CAAC,CAAC;IACN,CAAC;IAED,cAAA,EAAgB,CACT,uBAAuB,CAAC,QAAkB,EAAA,CAAS,CAAC;IAE3D,cAAA,EAAgB,CACT,uBAAuB,CAAC,OAAgB,EAAE,YAAqB,EAAE,eAAsC,EAAA;QAC1G,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QAE/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAElH,IAAI,iBAAiB,EAAE,CAAC;YACpB,OAAO,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAEvC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC;YACpD,OAAO,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAChF,CAAC;QAED,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,MAAM,OAAO,GAAG;qKAAC,eAAY,CAAC,YAAY;SAAC,CAAC;QAE5C,IAAI,GAAG,GAAG,KAAK,CAAC;QAChB,IAAI,GAAG,GAAG,KAAK,CAAC;QAChB,MAAM,KAAK,GAAG,KAAK,CAAC;QAEpB,UAAU;QACV,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,aAAa,GAAG,QAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YAE7D,MAAM,cAAc,GAAG,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YACtD,MAAM,yBAAyB,GAC3B,cAAc,IAAI,cAAc,CAAC,QAAQ,IAAI,CAAE,QAAgB,CAAC,0BAA0B,IAAK,QAAgB,CAAC,0BAA0B,CAAC,CAAC;YAEhJ,IAAI,cAAc,IAAI,CAAC,aAAa,IAAI,yBAAyB,CAAC,EAAE,CAAC;gBACjE,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAChC,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,OAAO,CAAC,IAAI,cAAc,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;oBAC5F,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACnC,GAAG,GAAG,IAAI,CAAC;gBACf,CAAC,MAAM,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,MAAM,CAAC,EAAE,CAAC;oBACzD,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACnC,GAAG,GAAG,IAAI,CAAC;gBACf,CAAC;gBAED,IAAI,aAAa,EAAE,CAAC;oBAChB,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;oBAClC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAC/C,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;oBAC7B,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;gBAC7C,CAAC;YACL,CAAC;YAED,MAAM,cAAc,GAAI,QAAgB,CAAC,cAAc,CAAC;YACxD,IAAI,cAAc,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAChC,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,OAAO,CAAC,IAAI,cAAc,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;oBAC5F,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACnC,GAAG,GAAG,IAAI,CAAC;gBACf,CAAC,MAAM,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,MAAM,CAAC,EAAE,CAAC;oBACzD,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBACnC,GAAG,GAAG,IAAI,CAAC;gBACf,CAAC;YACL,CAAC;QACL,CAAC;QAED,WAAW;QACX,IAAI,eAAe,EAAE,CAAC;YAClB,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACjC,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,OAAO,CAAC,IAAI,eAAe,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;gBAC7F,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACpC,GAAG,GAAG,IAAI,CAAC;YACf,CAAC,MAAM,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzD,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACpC,GAAG,GAAG,IAAI,CAAC;YACf,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,SAAS;QACT,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,qBAAqB,0JAAC,eAAY,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,cAAc,IAAI,QAAQ,CAAC,gBAAgB,kKAAK,WAAQ,CAAC,eAAe,EAAE,CAAC;YAC9J,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,SAAS,CAAC,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,GAAG,EAAE,CAAC;YACN,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,MAAM,CAAC,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChC,CAAC;QACD,IAAI,GAAG,EAAE,CAAC;YACN,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,OAAO,CAAC,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChC,CAAC;QAED,QAAQ;QACR,MAAM,SAAS,GAAG,wKAAI,kBAAe,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjD,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;YAC/C,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,mBAAmB,CAAC,CAAC;YAC/C,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;gBAC9B,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,wBAAwB,CAAC,CAAC;gBACpD,OAAO,CAAC,IAAI,0JAAC,eAAY,CAAC,wBAAwB,CAAC,CAAC;YACxD,CAAC;YAED,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAExE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,IAAI,QAAQ,IAAI,QAAQ,CAAC,yBAAyB,EAAE,CAAC;gBACjD,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACxC,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,IAAI,CAAC,uBAAuB,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,CAAC;YAED,IAAI,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC;gBAC9B,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACnD,CAAC;QAED,gBAAgB;QAChB,MAAM,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,uLAC7C,6CAAA,AAA0C,EACtC,IAAI,CAAC,kBAAkB,EACvB,OAAO,EACP,OAAO,EACP,IAAI,EACJ,IAAI,EAAE,AACN,KAAK,EAAE,AACP,KAAK,EAAE,AACP,GAAG,EAAE,AACL,AAJyB,GAItB,EAAE,AACL,AAJwB,KAInB,CAAC,EAHmB,AACP,MACC,QACG;YAE1B,CAAC,CAAC;QAER,YAAY;QACZ,IAAI,YAAY,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,iNAAA,AAA0B,EAAC,OAAO,CAAC,CAAC;YACpC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC,gBAAgB,EAAE,CAAC;gBAC9C,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC3C,CAAC;QACL,CAAC;QAED,aAAa;wLACb,oCAAA,AAAiC,EAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAElE,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAEtC,qBAAqB;QACrB,MAAM,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAE,CAAC;QAC9D,MAAM,aAAa,GAAG,WAAW,CAAC,OAAiB,CAAC;QACpD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG;gBACb,OAAO;gBACP,QAAQ;gBACR,gBAAgB;gBAChB,WAAW;gBACX,uBAAuB;gBACvB,kBAAkB;gBAClB,kBAAkB;gBAClB,eAAe;gBACf,gBAAgB;gBAChB,eAAe;gBACf,kBAAkB;gBAClB,wBAAwB;gBACxB,2BAA2B;gBAC3B,eAAe;aAClB,CAAC;4LAEF,uBAAA,AAAoB,EAAC,QAAQ,CAAC,CAAC;YAE/B,WAAW,CAAC,SAAS,CACjB,IAAI,CAAC,OAAO,CAAC,YAAY,CACrB,mBAAmB,EACnB,OAAO,EACP,QAAQ,EACR;gBAAC,gBAAgB;gBAAE,iBAAiB;gBAAE,gBAAgB;gBAAE,aAAa;gBAAE,cAAc;aAAC,EACtF,IAAI,EACJ,SAAS,EACT,SAAS,EACT,SAAS,EACT;gBAAE,2BAA2B,EAAE,mBAAmB;YAAA,CAAE,EACpD,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,cAAc,GACb,SAAS,GACT,KAAK,IAAI,EAAE;gBACP,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC/B,CAAC,CACV,EACD,IAAI,CACP,CAAC;QACN,CAAC;QAED,MAAM,aAAa,GAAG,WAAW,CAAC,MAAO,CAAC,OAAO,EAAE,CAAC;QAEpD,OAAO,aAAa,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,AAAC,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,EAAE,AAAC,CAAC,CAAC;IACzG,CAAC;IAED,cAAA,EAAgB,CACT,eAAe,CAAC,OAAgB,EAAE,YAAqB,EAAE,eAAsC,EAAA;QAClG,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;IAChF,CAAC;IAES,KAAK,CAAC,mBAAmB,GAAA;QAC/B,IAAI,IAAI,CAAC,eAAe,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,yCAAyC,CAAC,EAAE,MAAM,CAAC,2CAA2C,CAAC,CAAC,CAAC,CAAC;;;aAAA;QAChI,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,qCAAqC,CAAC,EAAE,MAAM,CAAC,uCAAuC,CAAC,CAAC,CAAC,CAAC;;;aAAA;QACxH,CAAC;QACD,mEAAmE;QACnE,IAAI,CAAC,6BAA6B,EAAE,EAAE,CAAC;IAC3C,CAAC;IAED,cAAA,EAAgB,CACT,qBAAqB,GAAA;QACxB,IAAI,OAAO,GAAG,IAAI,CAAC;QAEnB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAClD,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC;QAC1D,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE1C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,CAAE,CAAC;YAChC,IAAI,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,oKAAI,cAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC1E,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;YACvD,CAAC;YACD,OAAO,GAAG,aAAa,CAAC,MAAO,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC;QACzD,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACxC,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE1C,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAErD,MAAM,iBAAiB,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QAEhD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,CAAE,CAAC;YAChC,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAEhD,SAAS;YACT,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACnC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAEvB,OAAO;YACP,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,MAAO,CAAC,CAAC;YAElF,YAAY;YACZ,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;YAErD,qCAAqC;YACrC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAO,EAAE,CAAC,CAAC,CAAC;QACpD,CAAC;QAED,gBAAgB;QAChB,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QAEvC,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEpD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACT,gBAAgB,CAAC,IAAkB,EAAA;QACtC,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAClF,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,OAAO,CAAC,IAAkB,EAAA;QAC7B,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,cAAA,EAAgB,CACT,qBAAqB,GAAA;QACxB,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC;IAChD,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACxC,CAAC;IAED,cAAA,EAAgB,CACT,iBAAiB,CAAC,KAAmB,EAAA;QACxC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CACT,sBAAsB,CAAC,IAAkB,EAAE,QAAkB,EAAA;QAChE,OAAO,CAAC,QAAQ,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAED,cAAA,EAAgB,CACT,cAAc,CAAC,IAAkB,EAAE,QAAkB,EAAA;QACxD,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAES,cAAc,CAAC,OAAgB,EAAE,kBAA2B,KAAK,EAAA;QACvE,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACpC,MAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACrD,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjD,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAEjC,aAAa,CAAC,6BAA6B,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAE1E,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO;QACX,CAAC;QAED,8BAA8B;QAC9B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,QAAQ,CAAC,EAAE,CAAC;YAChD,OAAO;QACX,CAAC;QAED,UAAU;QACV,IAAI,eAAe,GAAG,QAAQ,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC;QACvE,MAAM,eAAe,GAAG,aAAa,CAAC,0BAA0B,EAAE,CAAC;QACnE,IAAI,eAAe,GAAG,CAAC,EAAE,CAAC;YACtB,eAAe,GAAG,eAAe,kKAAK,WAAQ,CAAC,wBAAwB,CAAC,CAAC,8JAAC,WAAQ,CAAC,+BAA+B,CAAC,CAAC,8JAAC,WAAQ,CAAC,wBAAwB,CAAC;QAC3J,CAAC;QAED,MAAM,OAAO,GAAG,eAAe,kKAAK,WAAQ,CAAC,wBAAwB,CAAC;QACtE,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;QAE1I,qBAAqB;QACrB,MAAM,KAAK,GAAG,aAAa,CAAC,uBAAuB,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,eAAe,CAAC,CAAC;QACpF,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,EAAE,CAAC;YACzC,OAAO;QACX,CAAC;QAED,MAAM,0BAA0B,GAAG,KAAK,CAAC,0BAA0B,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,gBAAgB,CAAC;QAEnH,IAAI,CAAC,2BAA2B,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAEnE,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAE3D,IAAI,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,WAAW,EAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC/C,aAAa,CAAC,MAAM,CAAC,OAAO,EAAE,eAAe,EAAE,eAAe,IAAI,SAAS,CAAC,CAAC;YAC7E,OAAO,CAAC,WAAW,EAAG,CAAC,gBAAgB,GAAG,KAAK,CAAC;QACpD,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,0BAA0B,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1G,MAAM,iBAAiB,GAAG,aAAa,CAAC,6BAA6B,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;YAE3H,IAAI,WAAW,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE,CAAC;gBACpC,WAAW,GAAG,iBAAiB,CAAC,eAAe,EAAE,CAAC;YACtD,CAAC;YAED,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,OAAO;YACX,CAAC;YAED,MAAM,MAAM,GAAG,WAAW,CAAC,MAAO,CAAC;YAEnC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACjC,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAC9B,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACrB,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,kBAAkB,EAAE,CAAC,CAAC;gBAC/D,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC;gBAC1D,MAAM,CAAC,SAAS,CACZ,WAAW,EACX,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,EACrC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,EACrC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,EACrC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CACxC,CAAC;YACN,CAAC,MAAM,CAAC;gBACJ,iBAAiB,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,EAAE,EAAE,aAAqB,EAAE,OAAO,CAAC,CAAC;YACrG,CAAC;YAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACrB,MAAM,aAAa,GAAG,QAAQ,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC;gBAEtE,MAAM,cAAc,GAAG,QAAQ,CAAC,mBAAmB,EAAE,CAAC;gBACtD,MAAM,yBAAyB,GAC3B,cAAc,IAAI,cAAc,CAAC,QAAQ,IAAI,CAAE,QAAgB,CAAC,0BAA0B,IAAK,QAAgB,CAAC,0BAA0B,CAAC,CAAC;gBAEhJ,IAAI,cAAc,IAAI,CAAC,aAAa,IAAI,yBAAyB,CAAC,EAAE,CAAC;oBACjE,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;oBACpD,MAAM,aAAa,GAAG,cAAc,CAAC,gBAAgB,EAAE,CAAC;oBAExD,IAAI,aAAa,EAAE,CAAC;wBAChB,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;oBACrD,CAAC;gBACL,CAAC;gBAED,MAAM,cAAc,GAAI,QAAgB,CAAC,cAAc,CAAC;gBACxD,IAAI,cAAc,EAAE,CAAC;oBACjB,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;oBACpD,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,cAAc,CAAC,KAAK,CAAC,CAAC;oBAC1D,MAAM,aAAa,GAAG,cAAc,CAAC,gBAAgB,EAAE,CAAC;oBACxD,IAAI,aAAa,EAAE,CAAC;wBAChB,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;oBACrD,CAAC;gBACL,CAAC;gBAED,qBAAqB;gBACrB,IAAI,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,CAAC;oBACxC,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;oBAC5E,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;gBACjG,CAAC;gBAED,QAAQ;gBACR,IAAI,aAAa,CAAC,QAAQ,IAAI,aAAa,CAAC,wBAAwB,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC7F,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;oBAExC,IAAI,QAAQ,CAAC,yBAAyB,EAAE,CAAC;wBACrC,MAAM,WAAW,GAAG,QAAQ,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC;wBACtE,IAAI,CAAC,WAAW,EAAE,CAAC;4BACf,OAAO;wBACX,CAAC;wBAED,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;wBAC9C,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC3E,CAAC,MAAM,CAAC;wBACJ,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC;oBAC/E,CAAC;gBACL,CAAC;gBAED,gBAAgB;oMAChB,4BAAA,AAAyB,EAAC,aAAa,EAAE,MAAM,CAAC,CAAC;gBACjD,IAAI,aAAa,CAAC,kBAAkB,IAAI,aAAa,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;oBAChG,aAAa,CAAC,kBAAkB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACnD,CAAC;gBAED,aAAa;gBACb,IAAI,eAAe,EAAE,CAAC;oBAClB,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAC5C,CAAC;gBAED,sBAAsB;gBACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC;gBAEzE,cAAc;gMACd,gBAAA,AAAa,EAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO;YACP,aAAa,CAAC,iBAAiB,CAAC,aAAa,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,CACtI,CADwI,KAClI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CACnC,CAAC;QACN,CAAC,MAAM,CAAC;YACJ,6CAA6C;YAC7C,IAAI,CAAC,eAAe,CAAC,mBAAmB,EAAE,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED,cAAA,EAAgB,CACT,gBAAgB,CAAC,KAAmB,EAAA;QACvC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,cAAA,EAAgB,CACT,QAAQ,GAAA;QACX,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,0JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;QAE1D,IAAI,EAAE,EAAE,CAAC;YACL,EAAE,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,0JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;QACpE,IAAI,YAAY,EAAE,CAAC;YACf,YAAY,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,0JAAC,eAAY,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,iBAAiB,CAAE,CAAC;YAC/C,WAAW,CAAC,OAAO,EAAE,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAE5B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAE/B,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;IAC1C,CAAC;;AA75BD;;;GAGG,CACW,gBAAA,SAAS,GAAG,KAAK,AAAR,CAAS", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/thinGlowLayer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/thinGlowLayer.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { Scene } from \"../scene\";\r\nimport { Vector2 } from \"../Maths/math.vector\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { Texture } from \"../Materials/Textures/texture\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { ThinEffectLayer } from \"./thinEffectLayer\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport type { PBRMaterial } from \"../Materials/PBR/pbrMaterial\";\r\n\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\nimport type { IThinEffectLayerOptions } from \"./thinEffectLayer\";\r\nimport { ThinBlurPostProcess } from \"core/PostProcesses/thinBlurPostProcess\";\r\n\r\n/**\r\n * Glow layer options. This helps customizing the behaviour\r\n * of the glow layer.\r\n */\r\nexport interface IThinGlowLayerOptions extends IThinEffectLayerOptions {\r\n    /**\r\n     * How big is the kernel of the blur texture. Default: 32\r\n     */\r\n    blurKernelSize?: number;\r\n\r\n    /**\r\n     * Forces the merge step to be done in ldr (clamp values > 1). Default: false\r\n     */\r\n    ldrMerge?: boolean;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class ThinGlowLayer extends ThinEffectLayer {\r\n    /**\r\n     * Effect Name of the layer.\r\n     */\r\n    public static readonly EffectName = \"GlowLayer\";\r\n\r\n    /**\r\n     * The default blur kernel size used for the glow.\r\n     */\r\n    public static DefaultBlurKernelSize = 32;\r\n\r\n    /**\r\n     * Gets the ldrMerge option.\r\n     */\r\n    public get ldrMerge(): boolean {\r\n        return this._options.ldrMerge;\r\n    }\r\n\r\n    /**\r\n     * Sets the kernel size of the blur.\r\n     */\r\n    public set blurKernelSize(value: number) {\r\n        if (value === this._options.blurKernelSize) {\r\n            return;\r\n        }\r\n\r\n        this._options.blurKernelSize = value;\r\n\r\n        const effectiveKernel = this._getEffectiveBlurKernelSize();\r\n        this._horizontalBlurPostprocess1.kernel = effectiveKernel;\r\n        this._verticalBlurPostprocess1.kernel = effectiveKernel;\r\n        this._horizontalBlurPostprocess2.kernel = effectiveKernel;\r\n        this._verticalBlurPostprocess2.kernel = effectiveKernel;\r\n    }\r\n\r\n    /**\r\n     * Gets the kernel size of the blur.\r\n     */\r\n    public get blurKernelSize(): number {\r\n        return this._options.blurKernelSize;\r\n    }\r\n\r\n    /**\r\n     * Sets the glow intensity.\r\n     */\r\n    public set intensity(value: number) {\r\n        this._intensity = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the glow intensity.\r\n     */\r\n    public get intensity(): number {\r\n        return this._intensity;\r\n    }\r\n\r\n    /** @internal */\r\n    public override _options: Required<IThinGlowLayerOptions>;\r\n\r\n    private _intensity: number = 1.0;\r\n    private _horizontalBlurPostprocess1: ThinBlurPostProcess;\r\n    private _verticalBlurPostprocess1: ThinBlurPostProcess;\r\n    private _horizontalBlurPostprocess2: ThinBlurPostProcess;\r\n    private _verticalBlurPostprocess2: ThinBlurPostProcess;\r\n\r\n    /** @internal */\r\n    public _includedOnlyMeshes: number[] = [];\r\n    /** @internal */\r\n    public _excludedMeshes: number[] = [];\r\n    private _meshesUsingTheirOwnMaterials: number[] = [];\r\n\r\n    /**\r\n     * Callback used to let the user override the color selection on a per mesh basis\r\n     */\r\n    public customEmissiveColorSelector: (mesh: Mesh, subMesh: SubMesh, material: Material, result: Color4) => void;\r\n    /**\r\n     * Callback used to let the user override the texture selection on a per mesh basis\r\n     */\r\n    public customEmissiveTextureSelector: (mesh: Mesh, subMesh: SubMesh, material: Material) => Texture;\r\n\r\n    /** @internal */\r\n    public _renderPassId = 0;\r\n\r\n    /**\r\n     * Instantiates a new glow Layer and references it to the scene.\r\n     * @param name The name of the layer\r\n     * @param scene The scene to use the layer in\r\n     * @param options Sets of none mandatory options to use with the layer (see IGlowLayerOptions for more information)\r\n     * @param dontCheckIfReady Specifies if the layer should disable checking whether all the post processes are ready (default: false). To save performance, this should be set to true and you should call `isReady` manually before rendering to the layer.\r\n     */\r\n    constructor(name: string, scene?: Scene, options?: IThinGlowLayerOptions, dontCheckIfReady = false) {\r\n        super(name, scene, false, dontCheckIfReady);\r\n        this.neutralColor = new Color4(0, 0, 0, 1);\r\n\r\n        // Adapt options\r\n        this._options = {\r\n            mainTextureRatio: 0.5,\r\n            mainTextureFixedSize: 0,\r\n            mainTextureType: Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n            blurKernelSize: 32,\r\n            camera: null,\r\n            renderingGroupId: -1,\r\n            ldrMerge: false,\r\n            alphaBlendingMode: Constants.ALPHA_ADD,\r\n            ...options,\r\n        };\r\n\r\n        // Initialize the layer\r\n        this._init(this._options);\r\n\r\n        if (dontCheckIfReady) {\r\n            // When dontCheckIfReady is true, we are in the new ThinXXX layer mode, so we must call _createTextureAndPostProcesses ourselves (it is called by EffectLayer otherwise)\r\n            this._createTextureAndPostProcesses();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the thin glow layer\r\n     * @returns the string with the class name of the glow layer\r\n     */\r\n    public getClassName(): string {\r\n        return \"GlowLayer\";\r\n    }\r\n\r\n    protected override async _importShadersAsync() {\r\n        if (this._shaderLanguage === ShaderLanguage.WGSL) {\r\n            await Promise.all([\r\n                import(\"../ShadersWGSL/glowMapMerge.fragment\"),\r\n                import(\"../ShadersWGSL/glowMapMerge.vertex\"),\r\n                import(\"../ShadersWGSL/glowBlurPostProcess.fragment\"),\r\n            ]);\r\n        } else {\r\n            await Promise.all([import(\"../Shaders/glowMapMerge.fragment\"), import(\"../Shaders/glowMapMerge.vertex\"), import(\"../Shaders/glowBlurPostProcess.fragment\")]);\r\n        }\r\n\r\n        await super._importShadersAsync();\r\n    }\r\n\r\n    public override getEffectName(): string {\r\n        return ThinGlowLayer.EffectName;\r\n    }\r\n\r\n    public override _createMergeEffect(): Effect {\r\n        let defines = \"#define EMISSIVE \\n\";\r\n        if (this._options.ldrMerge) {\r\n            defines += \"#define LDR \\n\";\r\n        }\r\n\r\n        // Effect\r\n        return this._engine.createEffect(\r\n            \"glowMapMerge\",\r\n            [VertexBuffer.PositionKind],\r\n            [\"offset\"],\r\n            [\"textureSampler\", \"textureSampler2\"],\r\n            defines,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            this.shaderLanguage,\r\n            this._shadersLoaded\r\n                ? undefined\r\n                : async () => {\r\n                      await this._importShadersAsync();\r\n                      this._shadersLoaded = true;\r\n                  }\r\n        );\r\n    }\r\n\r\n    public override _createTextureAndPostProcesses(): void {\r\n        const effectiveKernel = this._getEffectiveBlurKernelSize();\r\n        this._horizontalBlurPostprocess1 = new ThinBlurPostProcess(\"GlowLayerHBP1\", this._scene.getEngine(), new Vector2(1.0, 0), effectiveKernel);\r\n        this._verticalBlurPostprocess1 = new ThinBlurPostProcess(\"GlowLayerVBP1\", this._scene.getEngine(), new Vector2(0, 1.0), effectiveKernel);\r\n\r\n        this._horizontalBlurPostprocess2 = new ThinBlurPostProcess(\"GlowLayerHBP2\", this._scene.getEngine(), new Vector2(1.0, 0), effectiveKernel);\r\n        this._verticalBlurPostprocess2 = new ThinBlurPostProcess(\"GlowLayerVBP2\", this._scene.getEngine(), new Vector2(0, 1.0), effectiveKernel);\r\n\r\n        this._postProcesses = [this._horizontalBlurPostprocess1, this._verticalBlurPostprocess1, this._horizontalBlurPostprocess2, this._verticalBlurPostprocess2];\r\n    }\r\n\r\n    private _getEffectiveBlurKernelSize() {\r\n        return this._options.blurKernelSize / 2;\r\n    }\r\n\r\n    public override isReady(subMesh: SubMesh, useInstances: boolean): boolean {\r\n        const material = subMesh.getMaterial();\r\n        const mesh = subMesh.getRenderingMesh();\r\n\r\n        if (!material || !mesh) {\r\n            return false;\r\n        }\r\n\r\n        const emissiveTexture = (<any>material).emissiveTexture;\r\n        return super._isSubMeshReady(subMesh, useInstances, emissiveTexture);\r\n    }\r\n\r\n    public override _canRenderMesh(_mesh: AbstractMesh, _material: Material): boolean {\r\n        return true;\r\n    }\r\n\r\n    public override _internalCompose(effect: Effect): void {\r\n        // Texture\r\n        this.bindTexturesForCompose(effect);\r\n        effect.setFloat(\"offset\", this._intensity);\r\n\r\n        // Cache\r\n        const engine = this._engine;\r\n        const previousStencilBuffer = engine.getStencilBuffer();\r\n\r\n        // Draw order\r\n        engine.setStencilBuffer(false);\r\n\r\n        engine.drawElementsType(Material.TriangleFillMode, 0, 6);\r\n\r\n        // Draw order\r\n        engine.setStencilBuffer(previousStencilBuffer);\r\n    }\r\n\r\n    public override _setEmissiveTextureAndColor(mesh: Mesh, subMesh: SubMesh, material: Material): void {\r\n        let textureLevel = 1.0;\r\n\r\n        if (this.customEmissiveTextureSelector) {\r\n            this._emissiveTextureAndColor.texture = this.customEmissiveTextureSelector(mesh, subMesh, material);\r\n        } else {\r\n            if (material) {\r\n                this._emissiveTextureAndColor.texture = (<any>material).emissiveTexture;\r\n                if (this._emissiveTextureAndColor.texture) {\r\n                    textureLevel = this._emissiveTextureAndColor.texture.level;\r\n                }\r\n            } else {\r\n                this._emissiveTextureAndColor.texture = null;\r\n            }\r\n        }\r\n\r\n        if (this.customEmissiveColorSelector) {\r\n            this.customEmissiveColorSelector(mesh, subMesh, material, this._emissiveTextureAndColor.color);\r\n        } else {\r\n            if ((<any>material).emissiveColor) {\r\n                const emissiveIntensity = (<PBRMaterial>material).emissiveIntensity ?? 1;\r\n                textureLevel *= emissiveIntensity;\r\n                this._emissiveTextureAndColor.color.set(\r\n                    (<any>material).emissiveColor.r * textureLevel,\r\n                    (<any>material).emissiveColor.g * textureLevel,\r\n                    (<any>material).emissiveColor.b * textureLevel,\r\n                    material.alpha\r\n                );\r\n            } else {\r\n                this._emissiveTextureAndColor.color.set(this.neutralColor.r, this.neutralColor.g, this.neutralColor.b, this.neutralColor.a);\r\n            }\r\n        }\r\n    }\r\n\r\n    public override _shouldRenderMesh(mesh: Mesh): boolean {\r\n        return this.hasMesh(mesh);\r\n    }\r\n\r\n    public override _addCustomEffectDefines(defines: string[]): void {\r\n        defines.push(\"#define GLOW\");\r\n    }\r\n\r\n    /**\r\n     * Add a mesh in the exclusion list to prevent it to impact or being impacted by the glow layer.\r\n     * @param mesh The mesh to exclude from the glow layer\r\n     */\r\n    public addExcludedMesh(mesh: Mesh): void {\r\n        if (this._excludedMeshes.indexOf(mesh.uniqueId) === -1) {\r\n            this._excludedMeshes.push(mesh.uniqueId);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the exclusion list to let it impact or being impacted by the glow layer.\r\n     * @param mesh The mesh to remove\r\n     */\r\n    public removeExcludedMesh(mesh: Mesh): void {\r\n        const index = this._excludedMeshes.indexOf(mesh.uniqueId);\r\n        if (index !== -1) {\r\n            this._excludedMeshes.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Add a mesh in the inclusion list to impact or being impacted by the glow layer.\r\n     * @param mesh The mesh to include in the glow layer\r\n     */\r\n    public addIncludedOnlyMesh(mesh: Mesh): void {\r\n        if (this._includedOnlyMeshes.indexOf(mesh.uniqueId) === -1) {\r\n            this._includedOnlyMeshes.push(mesh.uniqueId);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the Inclusion list to prevent it to impact or being impacted by the glow layer.\r\n     * @param mesh The mesh to remove\r\n     */\r\n    public removeIncludedOnlyMesh(mesh: Mesh): void {\r\n        const index = this._includedOnlyMeshes.indexOf(mesh.uniqueId);\r\n        if (index !== -1) {\r\n            this._includedOnlyMeshes.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    public override hasMesh(mesh: AbstractMesh): boolean {\r\n        if (!super.hasMesh(mesh)) {\r\n            return false;\r\n        }\r\n\r\n        // Included Mesh\r\n        if (this._includedOnlyMeshes.length) {\r\n            return this._includedOnlyMeshes.indexOf(mesh.uniqueId) !== -1;\r\n        }\r\n\r\n        // Excluded Mesh\r\n        if (this._excludedMeshes.length) {\r\n            return this._excludedMeshes.indexOf(mesh.uniqueId) === -1;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override _useMeshMaterial(mesh: AbstractMesh): boolean {\r\n        // Specific case of material supporting glow directly\r\n        if (mesh.material?._supportGlowLayer) {\r\n            return true;\r\n        }\r\n\r\n        if (this._meshesUsingTheirOwnMaterials.length == 0) {\r\n            return false;\r\n        }\r\n\r\n        return this._meshesUsingTheirOwnMaterials.indexOf(mesh.uniqueId) > -1;\r\n    }\r\n\r\n    /**\r\n     * Add a mesh to be rendered through its own material and not with emissive only.\r\n     * @param mesh The mesh for which we need to use its material\r\n     */\r\n    public referenceMeshToUseItsOwnMaterial(mesh: AbstractMesh): void {\r\n        mesh.resetDrawCache(this._renderPassId);\r\n\r\n        this._meshesUsingTheirOwnMaterials.push(mesh.uniqueId);\r\n\r\n        mesh.onDisposeObservable.add(() => {\r\n            this._disposeMesh(mesh as Mesh);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from being rendered through its own material and not with emissive only.\r\n     * @param mesh The mesh for which we need to not use its material\r\n     * @param renderPassId The render pass id used when rendering the mesh\r\n     */\r\n    public unReferenceMeshFromUsingItsOwnMaterial(mesh: AbstractMesh, renderPassId: number): void {\r\n        let index = this._meshesUsingTheirOwnMaterials.indexOf(mesh.uniqueId);\r\n        while (index >= 0) {\r\n            this._meshesUsingTheirOwnMaterials.splice(index, 1);\r\n            index = this._meshesUsingTheirOwnMaterials.indexOf(mesh.uniqueId);\r\n        }\r\n        mesh.resetDrawCache(renderPassId);\r\n    }\r\n\r\n    /** @internal */\r\n    public _disposeMesh(mesh: Mesh): void {\r\n        this.removeIncludedOnlyMesh(mesh);\r\n        this.removeExcludedMesh(mesh);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAMjD,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAK7C,OAAO,EAAE,mBAAmB,EAAE,gDAA+C;;;;;;;AAqBvE,MAAO,aAAc,0KAAQ,kBAAe;IAW9C;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YACzC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC;QAErC,MAAM,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAC3D,IAAI,CAAC,2BAA2B,CAAC,MAAM,GAAG,eAAe,CAAC;QAC1D,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,eAAe,CAAC;QACxD,IAAI,CAAC,2BAA2B,CAAC,MAAM,GAAG,eAAe,CAAC;QAC1D,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,eAAe,CAAC;IAC5D,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;IACxC,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,CAAC,KAAa,EAAA;QAC9B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IA6BD;;;;;;OAMG,CACH,YAAY,IAAY,EAAE,KAAa,EAAE,OAA+B,EAAE,gBAAgB,GAAG,KAAK,CAAA;QAC9F,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAhCxC,IAAA,CAAA,UAAU,GAAW,GAAG,CAAC;QAMjC,cAAA,EAAgB,CACT,IAAA,CAAA,mBAAmB,GAAa,EAAE,CAAC;QAC1C,cAAA,EAAgB,CACT,IAAA,CAAA,eAAe,GAAa,EAAE,CAAC;QAC9B,IAAA,CAAA,6BAA6B,GAAa,EAAE,CAAC;QAWrD,cAAA,EAAgB,CACT,IAAA,CAAA,aAAa,GAAG,CAAC,CAAC;QAWrB,IAAI,CAAC,YAAY,GAAG,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE3C,gBAAgB;QAChB,IAAI,CAAC,QAAQ,GAAG;YACZ,gBAAgB,EAAE,GAAG;YACrB,oBAAoB,EAAE,CAAC;YACvB,eAAe,EAAE,SAAS,CAAC,yBAAyB;YACpD,cAAc,EAAE,EAAE;YAClB,MAAM,EAAE,IAAI;YACZ,gBAAgB,EAAE,CAAC,CAAC;YACpB,QAAQ,EAAE,KAAK;YACf,iBAAiB,EAAE,SAAS,CAAC,SAAS;YACtC,GAAG,OAAO;SACb,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE1B,IAAI,gBAAgB,EAAE,CAAC;YACnB,wKAAwK;YACxK,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,WAAW,CAAC;IACvB,CAAC;IAEkB,KAAK,CAAC,mBAAmB,GAAA;QACxC,IAAI,IAAI,CAAC,eAAe,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,MAAM,OAAO,CAAC,GAAG,CAAC;;;;aAIjB,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,kCAAkC,CAAC,EAAE,MAAM,CAAC,gCAAgC,CAAC,EAAE,MAAM,CAAC,yCAAyC,CAAC,CAAC,CAAC,CAAC;;;;aAAA;QACjK,CAAC;QAED,MAAM,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACtC,CAAC;IAEe,aAAa,GAAA;QACzB,OAAO,aAAa,CAAC,UAAU,CAAC;IACpC,CAAC;IAEe,kBAAkB,GAAA;QAC9B,IAAI,OAAO,GAAG,qBAAqB,CAAC;QACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACzB,OAAO,IAAI,gBAAgB,CAAC;QAChC,CAAC;QAED,SAAS;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAC5B,cAAc,EACd;qKAAC,eAAY,CAAC,YAAY;SAAC,EAC3B;YAAC,QAAQ;SAAC,EACV;YAAC,gBAAgB;YAAE,iBAAiB;SAAC,EACrC,OAAO,EACP,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,GACb,SAAS,GACT,KAAK,IAAI,EAAE;YACP,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC,CACV,CAAC;IACN,CAAC;IAEe,8BAA8B,GAAA;QAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAC3D,IAAI,CAAC,2BAA2B,GAAG,gLAAI,sBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,mKAAI,UAAO,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;QAC3I,IAAI,CAAC,yBAAyB,GAAG,gLAAI,sBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,mKAAI,UAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC;QAEzI,IAAI,CAAC,2BAA2B,GAAG,gLAAI,sBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,mKAAI,UAAO,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;QAC3I,IAAI,CAAC,yBAAyB,GAAG,gLAAI,sBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,mKAAI,UAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,eAAe,CAAC,CAAC;QAEzI,IAAI,CAAC,cAAc,GAAG;YAAC,IAAI,CAAC,2BAA2B;YAAE,IAAI,CAAC,yBAAyB;YAAE,IAAI,CAAC,2BAA2B;YAAE,IAAI,CAAC,yBAAyB;SAAC,CAAC;IAC/J,CAAC;IAEO,2BAA2B,GAAA;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,CAAC,CAAC;IAC5C,CAAC;IAEe,OAAO,CAAC,OAAgB,EAAE,YAAqB,EAAA;QAC3D,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAExC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,eAAe,GAAS,QAAS,CAAC,eAAe,CAAC;QACxD,OAAO,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;IACzE,CAAC;IAEe,cAAc,CAAC,KAAmB,EAAE,SAAmB,EAAA;QACnE,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,gBAAgB,CAAC,MAAc,EAAA;QAC3C,UAAU;QACV,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QACpC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3C,QAAQ;QACR,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,MAAM,qBAAqB,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAExD,aAAa;QACb,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAE/B,MAAM,CAAC,gBAAgB,8JAAC,WAAQ,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzD,aAAa;QACb,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;IACnD,CAAC;IAEe,2BAA2B,CAAC,IAAU,EAAE,OAAgB,EAAE,QAAkB,EAAA;QACxF,IAAI,YAAY,GAAG,GAAG,CAAC;QAEvB,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,IAAI,CAAC,wBAAwB,CAAC,OAAO,GAAG,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxG,CAAC,MAAM,CAAC;YACJ,IAAI,QAAQ,EAAE,CAAC;gBACX,IAAI,CAAC,wBAAwB,CAAC,OAAO,GAAS,QAAS,CAAC,eAAe,CAAC;gBACxE,IAAI,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,CAAC;oBACxC,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC/D,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,wBAAwB,CAAC,OAAO,GAAG,IAAI,CAAC;YACjD,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;YACnC,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QACnG,CAAC,MAAM,CAAC;YACJ,IAAU,QAAS,CAAC,aAAa,EAAE,CAAC;gBAChC,MAAM,iBAAiB,GAAiB,QAAS,CAAC,iBAAiB,IAAI,CAAC,CAAC;gBACzE,YAAY,IAAI,iBAAiB,CAAC;gBAClC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,GAAG,CAC7B,QAAS,CAAC,aAAa,CAAC,CAAC,GAAG,YAAY,EACxC,QAAS,CAAC,aAAa,CAAC,CAAC,GAAG,YAAY,EACxC,QAAS,CAAC,aAAa,CAAC,CAAC,GAAG,YAAY,EAC9C,QAAQ,CAAC,KAAK,CACjB,CAAC;YACN,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAChI,CAAC;QACL,CAAC;IACL,CAAC;IAEe,iBAAiB,CAAC,IAAU,EAAA;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAEe,uBAAuB,CAAC,OAAiB,EAAA;QACrD,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG,CACI,eAAe,CAAC,IAAU,EAAA;QAC7B,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,kBAAkB,CAAC,IAAU,EAAA;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,mBAAmB,CAAC,IAAU,EAAA;QACjC,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,sBAAsB,CAAC,IAAU,EAAA;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC9D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAEe,OAAO,CAAC,IAAkB,EAAA;QACtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,gBAAgB,CAAC,IAAkB,EAAA;QAC/C,qDAAqD;QACrD,IAAI,IAAI,CAAC,QAAQ,EAAE,iBAAiB,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,6BAA6B,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;;OAGG,CACI,gCAAgC,CAAC,IAAkB,EAAA;QACtD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAExC,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,IAAY,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG,CACI,sCAAsC,CAAC,IAAkB,EAAE,YAAoB,EAAA;QAClF,IAAI,KAAK,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtE,MAAO,KAAK,IAAI,CAAC,CAAE,CAAC;YAChB,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACpD,KAAK,GAAG,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtE,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IACtC,CAAC;IAED,cAAA,EAAgB,CACT,YAAY,CAAC,IAAU,EAAA;QAC1B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;;AA5WD;;GAEG,CACoB,cAAA,UAAU,GAAG,WAAW,AAAd,CAAe;AAEhD;;GAEG,CACW,cAAA,qBAAqB,GAAG,EAAE,AAAL,CAAM", "debugId": null}}, {"offset": {"line": 1065, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/thinHighlightLayer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/thinHighlightLayer.ts"], "sourcesContent": ["import type { <PERSON>, Nullable, Scene, SubMesh, Abs<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Effect, IThinEffectLayerOptions, Color3, EffectWrapper } from \"core/index\";\r\nimport { Vector2 } from \"../Maths/math.vector\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { ThinPassPostProcess } from \"../PostProcesses/thinPassPostProcess\";\r\nimport { ThinEffectLayer, ThinGlowBlurPostProcess } from \"./thinEffectLayer\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\nimport { ThinBlurPostProcess } from \"core/PostProcesses/thinBlurPostProcess\";\r\n\r\ninterface IBlurPostProcess extends EffectWrapper {\r\n    kernel: number;\r\n}\r\n\r\n/**\r\n * Highlight layer options. This helps customizing the behaviour\r\n * of the highlight layer.\r\n */\r\nexport interface IThinHighlightLayerOptions extends IThinEffectLayerOptions {\r\n    /**\r\n     * Multiplication factor apply to the main texture size in the first step of the blur to reduce the size\r\n     * of the picture to blur (the smaller the faster). Default: 0.5\r\n     */\r\n    blurTextureSizeRatio?: number;\r\n\r\n    /**\r\n     * How big in texel of the blur texture is the vertical blur. Default: 1\r\n     */\r\n    blurVerticalSize?: number;\r\n\r\n    /**\r\n     * How big in texel of the blur texture is the horizontal blur. Default: 1\r\n     */\r\n    blurHorizontalSize?: number;\r\n\r\n    /**\r\n     * Should we display highlight as a solid stroke? Default: false\r\n     */\r\n    isStroke?: boolean;\r\n\r\n    /**\r\n     * Use the GLSL code generation for the shader (even on WebGPU). Default is false\r\n     */\r\n    forceGLSL?: boolean;\r\n}\r\n\r\n/**\r\n * Storage interface grouping all the information required for glowing a mesh.\r\n */\r\ninterface IHighlightLayerMesh {\r\n    /**\r\n     * The glowy mesh\r\n     */\r\n    mesh: Mesh;\r\n    /**\r\n     * The color of the glow\r\n     */\r\n    color: Color3;\r\n    /**\r\n     * The mesh render callback use to insert stencil information\r\n     */\r\n    observerHighlight: Nullable<Observer<Mesh>>;\r\n    /**\r\n     * The mesh render callback use to come to the default behavior\r\n     */\r\n    observerDefault: Nullable<Observer<Mesh>>;\r\n    /**\r\n     * If it exists, the emissive color of the material will be used to generate the glow.\r\n     * Else it falls back to the current color.\r\n     */\r\n    glowEmissiveOnly: boolean;\r\n}\r\n\r\n/**\r\n * Storage interface grouping all the information required for an excluded mesh.\r\n */\r\ninterface IHighlightLayerExcludedMesh {\r\n    /**\r\n     * The glowy mesh\r\n     */\r\n    mesh: Mesh;\r\n    /**\r\n     * The mesh render callback use to prevent stencil use\r\n     */\r\n    beforeBind: Nullable<Observer<Mesh>>;\r\n    /**\r\n     * The mesh render callback use to restore previous stencil use\r\n     */\r\n    afterRender: Nullable<Observer<Mesh>>;\r\n    /**\r\n     * Current stencil state of the engine\r\n     */\r\n    stencilState: boolean;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\nexport class ThinHighlightLayer extends ThinEffectLayer {\r\n    /**\r\n     * Effect Name of the highlight layer.\r\n     */\r\n    public static readonly EffectName = \"HighlightLayer\";\r\n\r\n    /**\r\n     * The neutral color used during the preparation of the glow effect.\r\n     * This is black by default as the blend operation is a blend operation.\r\n     */\r\n    public static NeutralColor: Color4 = new Color4(0, 0, 0, 0);\r\n\r\n    /**\r\n     * Stencil value used for glowing meshes.\r\n     */\r\n    public static GlowingMeshStencilReference = 0x02;\r\n\r\n    /**\r\n     * Stencil value used for the other meshes in the scene.\r\n     */\r\n    public static NormalMeshStencilReference = 0x01;\r\n\r\n    /**\r\n     * Specifies whether or not the inner glow is ACTIVE in the layer.\r\n     */\r\n    public innerGlow: boolean = true;\r\n\r\n    /**\r\n     * Specifies whether or not the outer glow is ACTIVE in the layer.\r\n     */\r\n    public outerGlow: boolean = true;\r\n\r\n    /**\r\n     * Specifies the horizontal size of the blur.\r\n     */\r\n    public set blurHorizontalSize(value: number) {\r\n        this._horizontalBlurPostprocess.kernel = value;\r\n        this._options.blurHorizontalSize = value;\r\n    }\r\n\r\n    /**\r\n     * Specifies the vertical size of the blur.\r\n     */\r\n    public set blurVerticalSize(value: number) {\r\n        this._verticalBlurPostprocess.kernel = value;\r\n        this._options.blurVerticalSize = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the horizontal size of the blur.\r\n     */\r\n    public get blurHorizontalSize(): number {\r\n        return this._horizontalBlurPostprocess.kernel;\r\n    }\r\n\r\n    /**\r\n     * Gets the vertical size of the blur.\r\n     */\r\n    public get blurVerticalSize(): number {\r\n        return this._verticalBlurPostprocess.kernel;\r\n    }\r\n\r\n    private _instanceGlowingMeshStencilReference = ThinHighlightLayer.GlowingMeshStencilReference++;\r\n\r\n    /** @internal */\r\n    public override _options: Required<IThinHighlightLayerOptions>;\r\n\r\n    private _downSamplePostprocess: ThinPassPostProcess;\r\n    private _horizontalBlurPostprocess: IBlurPostProcess;\r\n    private _verticalBlurPostprocess: IBlurPostProcess;\r\n\r\n    /** @internal */\r\n    public _meshes: Nullable<{ [id: string]: Nullable<IHighlightLayerMesh> }> = {};\r\n    /** @internal */\r\n    public _excludedMeshes: Nullable<{ [id: string]: Nullable<IHighlightLayerExcludedMesh> }> = {};\r\n\r\n    /** @internal */\r\n    public _mainObjectRendererRenderPassId = -1;\r\n\r\n    /**\r\n     * Instantiates a new highlight Layer and references it to the scene..\r\n     * @param name The name of the layer\r\n     * @param scene The scene to use the layer in\r\n     * @param options Sets of none mandatory options to use with the layer (see IHighlightLayerOptions for more information)\r\n     * @param dontCheckIfReady Specifies if the layer should disable checking whether all the post processes are ready (default: false). To save performance, this should be set to true and you should call `isReady` manually before rendering to the layer.\r\n     */\r\n    constructor(name: string, scene?: Scene, options?: Partial<IThinHighlightLayerOptions>, dontCheckIfReady = false) {\r\n        super(name, scene, options !== undefined ? !!options.forceGLSL : false);\r\n\r\n        this.neutralColor = ThinHighlightLayer.NeutralColor;\r\n\r\n        // Adapt options\r\n        this._options = {\r\n            mainTextureRatio: 0.5,\r\n            blurTextureSizeRatio: 0.5,\r\n            mainTextureFixedSize: 0,\r\n            blurHorizontalSize: 1.0,\r\n            blurVerticalSize: 1.0,\r\n            alphaBlendingMode: Constants.ALPHA_COMBINE,\r\n            camera: null,\r\n            renderingGroupId: -1,\r\n            forceGLSL: false,\r\n            mainTextureType: Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n            isStroke: false,\r\n            ...options,\r\n        };\r\n\r\n        // Initialize the layer\r\n        this._init(this._options);\r\n\r\n        // Do not render as long as no meshes have been added\r\n        this._shouldRender = false;\r\n\r\n        if (dontCheckIfReady) {\r\n            // When dontCheckIfReady is true, we are in the new ThinXXX layer mode, so we must call _createTextureAndPostProcesses ourselves (it is called by EffectLayer otherwise)\r\n            this._createTextureAndPostProcesses();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the effect layer\r\n     * @returns the string with the class name of the effect layer\r\n     */\r\n    public getClassName(): string {\r\n        return \"HighlightLayer\";\r\n    }\r\n\r\n    protected override async _importShadersAsync() {\r\n        if (this._shaderLanguage === ShaderLanguage.WGSL) {\r\n            await Promise.all([\r\n                import(\"../ShadersWGSL/glowMapMerge.fragment\"),\r\n                import(\"../ShadersWGSL/glowMapMerge.vertex\"),\r\n                import(\"../ShadersWGSL/glowBlurPostProcess.fragment\"),\r\n            ]);\r\n        } else {\r\n            await Promise.all([import(\"../Shaders/glowMapMerge.fragment\"), import(\"../Shaders/glowMapMerge.vertex\"), import(\"../Shaders/glowBlurPostProcess.fragment\")]);\r\n        }\r\n\r\n        await super._importShadersAsync();\r\n    }\r\n\r\n    public override getEffectName(): string {\r\n        return ThinHighlightLayer.EffectName;\r\n    }\r\n\r\n    public override _numInternalDraws(): number {\r\n        return 2; // we need two rendering, one for the inner glow and the other for the outer glow\r\n    }\r\n\r\n    public override _createMergeEffect(): Effect {\r\n        return this._engine.createEffect(\r\n            \"glowMapMerge\",\r\n            [VertexBuffer.PositionKind],\r\n            [\"offset\"],\r\n            [\"textureSampler\"],\r\n            this._options.isStroke ? \"#define STROKE \\n\" : undefined,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            this._shaderLanguage,\r\n            this._shadersLoaded\r\n                ? undefined\r\n                : async () => {\r\n                      await this._importShadersAsync();\r\n                      this._shadersLoaded = true;\r\n                  }\r\n        );\r\n    }\r\n\r\n    public override _createTextureAndPostProcesses(): void {\r\n        if (this._options.alphaBlendingMode === Constants.ALPHA_COMBINE) {\r\n            this._downSamplePostprocess = new ThinPassPostProcess(\"HighlightLayerPPP\", this._scene.getEngine());\r\n            this._horizontalBlurPostprocess = new ThinGlowBlurPostProcess(\"HighlightLayerHBP\", this._scene.getEngine(), new Vector2(1.0, 0), this._options.blurHorizontalSize);\r\n            this._verticalBlurPostprocess = new ThinGlowBlurPostProcess(\"HighlightLayerVBP\", this._scene.getEngine(), new Vector2(0, 1.0), this._options.blurVerticalSize);\r\n            this._postProcesses = [this._downSamplePostprocess, this._horizontalBlurPostprocess, this._verticalBlurPostprocess];\r\n        } else {\r\n            this._horizontalBlurPostprocess = new ThinBlurPostProcess(\"HighlightLayerHBP\", this._scene.getEngine(), new Vector2(1.0, 0), this._options.blurHorizontalSize / 2);\r\n            this._verticalBlurPostprocess = new ThinBlurPostProcess(\"HighlightLayerVBP\", this._scene.getEngine(), new Vector2(0, 1.0), this._options.blurVerticalSize / 2);\r\n            this._postProcesses = [this._horizontalBlurPostprocess, this._verticalBlurPostprocess];\r\n        }\r\n    }\r\n\r\n    public override needStencil(): boolean {\r\n        return true;\r\n    }\r\n\r\n    public override isReady(subMesh: SubMesh, useInstances: boolean): boolean {\r\n        const material = subMesh.getMaterial();\r\n        const mesh = subMesh.getRenderingMesh();\r\n\r\n        if (!material || !mesh || !this._meshes) {\r\n            return false;\r\n        }\r\n\r\n        let emissiveTexture: Nullable<any> = null;\r\n        const highlightLayerMesh = this._meshes[mesh.uniqueId];\r\n\r\n        if (highlightLayerMesh && highlightLayerMesh.glowEmissiveOnly && material) {\r\n            emissiveTexture = (<any>material).emissiveTexture;\r\n        }\r\n        return super._isSubMeshReady(subMesh, useInstances, emissiveTexture);\r\n    }\r\n\r\n    public override _canRenderMesh(_mesh: AbstractMesh, _material: Material): boolean {\r\n        // all meshes can be rendered in the highlight layer, even transparent ones\r\n        return true;\r\n    }\r\n\r\n    public override _internalCompose(effect: Effect, renderIndex: number): void {\r\n        // Texture\r\n        this.bindTexturesForCompose(effect);\r\n\r\n        // Cache\r\n        const engine = this._engine;\r\n        engine.cacheStencilState();\r\n\r\n        // Stencil operations\r\n        engine.setStencilOperationPass(Constants.REPLACE);\r\n        engine.setStencilOperationFail(Constants.KEEP);\r\n        engine.setStencilOperationDepthFail(Constants.KEEP);\r\n\r\n        // Draw order\r\n        engine.setStencilMask(0x00);\r\n        engine.setStencilBuffer(true);\r\n        engine.setStencilFunctionReference(this._instanceGlowingMeshStencilReference);\r\n\r\n        // 2 passes inner outer\r\n        if (this.outerGlow && renderIndex === 0) {\r\n            // the outer glow is rendered the first time _internalRender is called, so when renderIndex == 0 (and only if outerGlow is enabled)\r\n            effect.setFloat(\"offset\", 0);\r\n            engine.setStencilFunction(Constants.NOTEQUAL);\r\n            engine.drawElementsType(Material.TriangleFillMode, 0, 6);\r\n        }\r\n        if (this.innerGlow && renderIndex === 1) {\r\n            // the inner glow is rendered the second time _internalRender is called, so when renderIndex == 1 (and only if innerGlow is enabled)\r\n            effect.setFloat(\"offset\", 1);\r\n            engine.setStencilFunction(Constants.EQUAL);\r\n            engine.drawElementsType(Material.TriangleFillMode, 0, 6);\r\n        }\r\n\r\n        // Restore Cache\r\n        engine.restoreStencilState();\r\n    }\r\n\r\n    public override _setEmissiveTextureAndColor(mesh: Mesh, _subMesh: SubMesh, material: Material): void {\r\n        const highlightLayerMesh = this._meshes![mesh.uniqueId];\r\n        if (highlightLayerMesh) {\r\n            this._emissiveTextureAndColor.color.set(highlightLayerMesh.color.r, highlightLayerMesh.color.g, highlightLayerMesh.color.b, 1.0);\r\n        } else {\r\n            this._emissiveTextureAndColor.color.set(this.neutralColor.r, this.neutralColor.g, this.neutralColor.b, this.neutralColor.a);\r\n        }\r\n\r\n        if (highlightLayerMesh && highlightLayerMesh.glowEmissiveOnly && material) {\r\n            this._emissiveTextureAndColor.texture = (<any>material).emissiveTexture;\r\n            this._emissiveTextureAndColor.color.set(1.0, 1.0, 1.0, 1.0);\r\n        } else {\r\n            this._emissiveTextureAndColor.texture = null;\r\n        }\r\n    }\r\n\r\n    public override shouldRender(): boolean {\r\n        return this._meshes && super.shouldRender() ? true : false;\r\n    }\r\n\r\n    public override _shouldRenderMesh(mesh: Mesh): boolean {\r\n        if (this._excludedMeshes && this._excludedMeshes[mesh.uniqueId]) {\r\n            return false;\r\n        }\r\n\r\n        return super.hasMesh(mesh);\r\n    }\r\n\r\n    public override _addCustomEffectDefines(defines: string[]): void {\r\n        defines.push(\"#define HIGHLIGHT\");\r\n    }\r\n\r\n    /**\r\n     * Add a mesh in the exclusion list to prevent it to impact or being impacted by the highlight layer.\r\n     * @param mesh The mesh to exclude from the highlight layer\r\n     */\r\n    public addExcludedMesh(mesh: Mesh) {\r\n        if (!this._excludedMeshes) {\r\n            return;\r\n        }\r\n\r\n        const meshExcluded = this._excludedMeshes[mesh.uniqueId];\r\n        if (!meshExcluded) {\r\n            const obj: IHighlightLayerExcludedMesh = {\r\n                mesh: mesh,\r\n                beforeBind: null,\r\n                afterRender: null,\r\n                stencilState: false,\r\n            };\r\n\r\n            obj.beforeBind = mesh.onBeforeBindObservable.add((mesh: Mesh) => {\r\n                if (this._mainObjectRendererRenderPassId !== -1 && this._mainObjectRendererRenderPassId !== this._engine.currentRenderPassId) {\r\n                    return;\r\n                }\r\n                obj.stencilState = mesh.getEngine().getStencilBuffer();\r\n                mesh.getEngine().setStencilBuffer(false);\r\n            });\r\n\r\n            obj.afterRender = mesh.onAfterRenderObservable.add((mesh: Mesh) => {\r\n                if (this._mainObjectRendererRenderPassId !== -1 && this._mainObjectRendererRenderPassId !== this._engine.currentRenderPassId) {\r\n                    return;\r\n                }\r\n                mesh.getEngine().setStencilBuffer(obj.stencilState);\r\n            });\r\n\r\n            this._excludedMeshes[mesh.uniqueId] = obj;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the exclusion list to let it impact or being impacted by the highlight layer.\r\n     * @param mesh The mesh to highlight\r\n     */\r\n    public removeExcludedMesh(mesh: Mesh) {\r\n        if (!this._excludedMeshes) {\r\n            return;\r\n        }\r\n\r\n        const meshExcluded = this._excludedMeshes[mesh.uniqueId];\r\n        if (meshExcluded) {\r\n            if (meshExcluded.beforeBind) {\r\n                mesh.onBeforeBindObservable.remove(meshExcluded.beforeBind);\r\n            }\r\n\r\n            if (meshExcluded.afterRender) {\r\n                mesh.onAfterRenderObservable.remove(meshExcluded.afterRender);\r\n            }\r\n        }\r\n\r\n        this._excludedMeshes[mesh.uniqueId] = null;\r\n    }\r\n\r\n    public override hasMesh(mesh: AbstractMesh): boolean {\r\n        if (!this._meshes || !super.hasMesh(mesh)) {\r\n            return false;\r\n        }\r\n\r\n        return !!this._meshes[mesh.uniqueId];\r\n    }\r\n\r\n    /**\r\n     * Add a mesh in the highlight layer in order to make it glow with the chosen color.\r\n     * @param mesh The mesh to highlight\r\n     * @param color The color of the highlight\r\n     * @param glowEmissiveOnly Extract the glow from the emissive texture\r\n     */\r\n    public addMesh(mesh: Mesh, color: Color3, glowEmissiveOnly = false) {\r\n        if (!this._meshes) {\r\n            return;\r\n        }\r\n\r\n        const meshHighlight = this._meshes[mesh.uniqueId];\r\n        if (meshHighlight) {\r\n            meshHighlight.color = color;\r\n        } else {\r\n            this._meshes[mesh.uniqueId] = {\r\n                mesh: mesh,\r\n                color: color,\r\n                // Lambda required for capture due to Observable this context\r\n                observerHighlight: mesh.onBeforeBindObservable.add((mesh: Mesh) => {\r\n                    if (this._mainObjectRendererRenderPassId !== -1 && this._mainObjectRendererRenderPassId !== this._engine.currentRenderPassId) {\r\n                        return;\r\n                    }\r\n                    if (this.isEnabled) {\r\n                        if (this._excludedMeshes && this._excludedMeshes[mesh.uniqueId]) {\r\n                            this._defaultStencilReference(mesh);\r\n                        } else {\r\n                            mesh.getScene().getEngine().setStencilFunctionReference(this._instanceGlowingMeshStencilReference);\r\n                        }\r\n                    }\r\n                }),\r\n                observerDefault: mesh.onAfterRenderObservable.add((mesh: Mesh) => {\r\n                    if (this._mainObjectRendererRenderPassId !== -1 && this._mainObjectRendererRenderPassId !== this._engine.currentRenderPassId) {\r\n                        return;\r\n                    }\r\n                    if (this.isEnabled) {\r\n                        this._defaultStencilReference(mesh);\r\n                    }\r\n                }),\r\n                glowEmissiveOnly: glowEmissiveOnly,\r\n            };\r\n\r\n            mesh.onDisposeObservable.add(() => {\r\n                this._disposeMesh(mesh);\r\n            });\r\n        }\r\n\r\n        this._shouldRender = true;\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the highlight layer in order to make it stop glowing.\r\n     * @param mesh The mesh to highlight\r\n     */\r\n    public removeMesh(mesh: Mesh) {\r\n        if (!this._meshes) {\r\n            return;\r\n        }\r\n\r\n        const meshHighlight = this._meshes[mesh.uniqueId];\r\n        if (meshHighlight) {\r\n            if (meshHighlight.observerHighlight) {\r\n                mesh.onBeforeBindObservable.remove(meshHighlight.observerHighlight);\r\n            }\r\n\r\n            if (meshHighlight.observerDefault) {\r\n                mesh.onAfterRenderObservable.remove(meshHighlight.observerDefault);\r\n            }\r\n            delete this._meshes[mesh.uniqueId];\r\n        }\r\n\r\n        this._shouldRender = false;\r\n        for (const meshHighlightToCheck in this._meshes) {\r\n            if (this._meshes[meshHighlightToCheck]) {\r\n                this._shouldRender = true;\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Remove all the meshes currently referenced in the highlight layer\r\n     */\r\n    public removeAllMeshes(): void {\r\n        if (!this._meshes) {\r\n            return;\r\n        }\r\n\r\n        for (const uniqueId in this._meshes) {\r\n            if (Object.prototype.hasOwnProperty.call(this._meshes, uniqueId)) {\r\n                const mesh = this._meshes[uniqueId];\r\n                if (mesh) {\r\n                    this.removeMesh(mesh.mesh);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _defaultStencilReference(mesh: Mesh) {\r\n        mesh.getScene().getEngine().setStencilFunctionReference(ThinHighlightLayer.NormalMeshStencilReference);\r\n    }\r\n\r\n    public _disposeMesh(mesh: Mesh): void {\r\n        this.removeMesh(mesh);\r\n        this.removeExcludedMesh(mesh);\r\n    }\r\n\r\n    public override dispose(): void {\r\n        if (this._meshes) {\r\n            // Clean mesh references\r\n            for (const id in this._meshes) {\r\n                const meshHighlight = this._meshes[id];\r\n                if (meshHighlight && meshHighlight.mesh) {\r\n                    if (meshHighlight.observerHighlight) {\r\n                        meshHighlight.mesh.onBeforeBindObservable.remove(meshHighlight.observerHighlight);\r\n                    }\r\n\r\n                    if (meshHighlight.observerDefault) {\r\n                        meshHighlight.mesh.onAfterRenderObservable.remove(meshHighlight.observerDefault);\r\n                    }\r\n                }\r\n            }\r\n            this._meshes = null;\r\n        }\r\n\r\n        if (this._excludedMeshes) {\r\n            for (const id in this._excludedMeshes) {\r\n                const meshHighlight = this._excludedMeshes[id];\r\n                if (meshHighlight) {\r\n                    if (meshHighlight.beforeBind) {\r\n                        meshHighlight.mesh.onBeforeBindObservable.remove(meshHighlight.beforeBind);\r\n                    }\r\n\r\n                    if (meshHighlight.afterRender) {\r\n                        meshHighlight.mesh.onAfterRenderObservable.remove(meshHighlight.afterRender);\r\n                    }\r\n                }\r\n            }\r\n            this._excludedMeshes = null;\r\n        }\r\n\r\n        super.dispose();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,mBAAmB,EAAE,MAAM,sCAAsC,CAAC;AAC3E,OAAO,EAAE,eAAe,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAE7E,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,mBAAmB,EAAE,gDAA+C;;;;;;;;AA0FvE,MAAO,kBAAmB,0KAAQ,kBAAe;IAgCnD;;OAEG,CACH,IAAW,kBAAkB,CAAC,KAAa,EAAA;QACvC,IAAI,CAAC,0BAA0B,CAAC,MAAM,GAAG,KAAK,CAAC;QAC/C,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAC7C,CAAC;IAED;;OAEG,CACH,IAAW,gBAAgB,CAAC,KAAa,EAAA;QACrC,IAAI,CAAC,wBAAwB,CAAC,MAAM,GAAG,KAAK,CAAC;QAC7C,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC;IAClD,CAAC;IAED;;OAEG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC;IAChD,CAAC;IAmBD;;;;;;OAMG,CACH,YAAY,IAAY,EAAE,KAAa,EAAE,OAA6C,EAAE,gBAAgB,GAAG,KAAK,CAAA;QAC5G,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAjE5E;;WAEG,CACI,IAAA,CAAA,SAAS,GAAY,IAAI,CAAC;QAEjC;;WAEG,CACI,IAAA,CAAA,SAAS,GAAY,IAAI,CAAC;QAgCzB,IAAA,CAAA,oCAAoC,GAAG,kBAAkB,CAAC,2BAA2B,EAAE,CAAC;QAShG,cAAA,EAAgB,CACT,IAAA,CAAA,OAAO,GAA8D,CAAA,CAAE,CAAC;QAC/E,cAAA,EAAgB,CACT,IAAA,CAAA,eAAe,GAAsE,CAAA,CAAE,CAAC;QAE/F,cAAA,EAAgB,CACT,IAAA,CAAA,+BAA+B,GAAG,CAAC,CAAC,CAAC;QAYxC,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC;QAEpD,gBAAgB;QAChB,IAAI,CAAC,QAAQ,GAAG;YACZ,gBAAgB,EAAE,GAAG;YACrB,oBAAoB,EAAE,GAAG;YACzB,oBAAoB,EAAE,CAAC;YACvB,kBAAkB,EAAE,GAAG;YACvB,gBAAgB,EAAE,GAAG;YACrB,iBAAiB,EAAE,SAAS,CAAC,aAAa;YAC1C,MAAM,EAAE,IAAI;YACZ,gBAAgB,EAAE,CAAC,CAAC;YACpB,SAAS,EAAE,KAAK;YAChB,eAAe,EAAE,SAAS,CAAC,yBAAyB;YACpD,QAAQ,EAAE,KAAK;YACf,GAAG,OAAO;SACb,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE1B,qDAAqD;QACrD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,IAAI,gBAAgB,EAAE,CAAC;YACnB,wKAAwK;YACxK,IAAI,CAAC,8BAA8B,EAAE,CAAC;QAC1C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAEkB,KAAK,CAAC,mBAAmB,GAAA;QACxC,IAAI,IAAI,CAAC,eAAe,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,MAAM,OAAO,CAAC,GAAG,CAAC;;;;aAIjB,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,kCAAkC,CAAC,EAAE,MAAM,CAAC,gCAAgC,CAAC,EAAE,MAAM,CAAC,yCAAyC,CAAC,CAAC,CAAC,CAAC;;;;aAAA;QACjK,CAAC;QAED,MAAM,KAAK,CAAC,mBAAmB,EAAE,CAAC;IACtC,CAAC;IAEe,aAAa,GAAA;QACzB,OAAO,kBAAkB,CAAC,UAAU,CAAC;IACzC,CAAC;IAEe,iBAAiB,GAAA;QAC7B,OAAO,CAAC,CAAC,CAAC,iFAAiF;IAC/F,CAAC;IAEe,kBAAkB,GAAA;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAC5B,cAAc,EACd;qKAAC,eAAY,CAAC,YAAY;SAAC,EAC3B;YAAC,QAAQ;SAAC,EACV;YAAC,gBAAgB;SAAC,EAClB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS,EACxD,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,cAAc,GACb,SAAS,GACT,KAAK,IAAI,EAAE;YACP,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC,CACV,CAAC;IACN,CAAC;IAEe,8BAA8B,GAAA;QAC1C,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,KAAK,GAAA,MAAS,CAAC,aAAa,EAAE,CAAC;YAC9D,IAAI,CAAC,sBAAsB,GAAG,gLAAI,sBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YACpG,IAAI,CAAC,0BAA0B,GAAG,qKAAI,0BAAuB,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,mKAAI,UAAO,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;YACnK,IAAI,CAAC,wBAAwB,GAAG,qKAAI,0BAAuB,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,mKAAI,UAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YAC/J,IAAI,CAAC,cAAc,GAAG;gBAAC,IAAI,CAAC,sBAAsB;gBAAE,IAAI,CAAC,0BAA0B;gBAAE,IAAI,CAAC,wBAAwB;aAAC,CAAC;QACxH,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,0BAA0B,GAAG,gLAAI,sBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,mKAAI,UAAO,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC;YACnK,IAAI,CAAC,wBAAwB,GAAG,gLAAI,sBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,mKAAI,UAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;YAC/J,IAAI,CAAC,cAAc,GAAG;gBAAC,IAAI,CAAC,0BAA0B;gBAAE,IAAI,CAAC,wBAAwB;aAAC,CAAC;QAC3F,CAAC;IACL,CAAC;IAEe,WAAW,GAAA;QACvB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,OAAO,CAAC,OAAgB,EAAE,YAAqB,EAAA;QAC3D,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAExC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,eAAe,GAAkB,IAAI,CAAC;QAC1C,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,gBAAgB,IAAI,QAAQ,EAAE,CAAC;YACxE,eAAe,GAAS,QAAS,CAAC,eAAe,CAAC;QACtD,CAAC;QACD,OAAO,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;IACzE,CAAC;IAEe,cAAc,CAAC,KAAmB,EAAE,SAAmB,EAAA;QACnE,2EAA2E;QAC3E,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,gBAAgB,CAAC,MAAc,EAAE,WAAmB,EAAA;QAChE,UAAU;QACV,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC;QAEpC,QAAQ;QACR,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,MAAM,CAAC,iBAAiB,EAAE,CAAC;QAE3B,qBAAqB;QACrB,MAAM,CAAC,uBAAuB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,CAAC,uBAAuB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC/C,MAAM,CAAC,4BAA4B,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAEpD,aAAa;QACb,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAC5B,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC9B,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAE9E,uBAAuB;QACvB,IAAI,IAAI,CAAC,SAAS,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtC,mIAAmI;YACnI,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC9C,MAAM,CAAC,gBAAgB,8JAAC,WAAQ,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YACtC,oIAAoI;YACpI,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC7B,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,CAAC,gBAAgB,8JAAC,WAAQ,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,gBAAgB;QAChB,MAAM,CAAC,mBAAmB,EAAE,CAAC;IACjC,CAAC;IAEe,2BAA2B,CAAC,IAAU,EAAE,QAAiB,EAAE,QAAkB,EAAA;QACzF,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,kBAAkB,EAAE,CAAC;YACrB,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACrI,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAChI,CAAC;QAED,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,gBAAgB,IAAI,QAAQ,EAAE,CAAC;YACxE,IAAI,CAAC,wBAAwB,CAAC,OAAO,GAAS,QAAS,CAAC,eAAe,CAAC;YACxE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAChE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,wBAAwB,CAAC,OAAO,GAAG,IAAI,CAAC;QACjD,CAAC;IACL,CAAC;IAEe,YAAY,GAAA;QACxB,OAAO,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/D,CAAC;IAEe,iBAAiB,CAAC,IAAU,EAAA;QACxC,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEe,uBAAuB,CAAC,OAAiB,EAAA;QACrD,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACtC,CAAC;IAED;;;OAGG,CACI,eAAe,CAAC,IAAU,EAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,MAAM,GAAG,GAAgC;gBACrC,IAAI,EAAE,IAAI;gBACV,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,IAAI;gBACjB,YAAY,EAAE,KAAK;aACtB,CAAC;YAEF,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,IAAU,EAAE,EAAE;gBAC5D,IAAI,IAAI,CAAC,+BAA+B,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,+BAA+B,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;oBAC3H,OAAO;gBACX,CAAC;gBACD,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC;gBACvD,IAAI,CAAC,SAAS,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,IAAU,EAAE,EAAE;gBAC9D,IAAI,IAAI,CAAC,+BAA+B,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,+BAA+B,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;oBAC3H,OAAO;gBACX,CAAC;gBACD,IAAI,CAAC,SAAS,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;QAC9C,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,kBAAkB,CAAC,IAAU,EAAA;QAChC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,OAAO;QACX,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,YAAY,EAAE,CAAC;YACf,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC1B,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,YAAY,CAAC,WAAW,EAAE,CAAC;gBAC3B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAClE,CAAC;QACL,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;IAC/C,CAAC;IAEe,OAAO,CAAC,IAAkB,EAAA;QACtC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED;;;;;OAKG,CACI,OAAO,CAAC,IAAU,EAAE,KAAa,EAAE,gBAAgB,GAAG,KAAK,EAAA;QAC9D,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,aAAa,EAAE,CAAC;YAChB,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;QAChC,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;gBAC1B,IAAI,EAAE,IAAI;gBACV,KAAK,EAAE,KAAK;gBACZ,6DAA6D;gBAC7D,iBAAiB,EAAE,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,IAAU,EAAE,EAAE;oBAC9D,IAAI,IAAI,CAAC,+BAA+B,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,+BAA+B,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;wBAC3H,OAAO;oBACX,CAAC;oBACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;wBACjB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;4BAC9D,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;wBACxC,CAAC,MAAM,CAAC;4BACJ,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,2BAA2B,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;wBACvG,CAAC;oBACL,CAAC;gBACL,CAAC,CAAC;gBACF,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,IAAU,EAAE,EAAE;oBAC7D,IAAI,IAAI,CAAC,+BAA+B,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,+BAA+B,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;wBAC3H,OAAO;oBACX,CAAC;oBACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;wBACjB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;oBACxC,CAAC;gBACL,CAAC,CAAC;gBACF,gBAAgB,EAAE,gBAAgB;aACrC,CAAC;YAEF,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACI,UAAU,CAAC,IAAU,EAAA;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,IAAI,aAAa,EAAE,CAAC;YAChB,IAAI,aAAa,CAAC,iBAAiB,EAAE,CAAC;gBAClC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;YACxE,CAAC;YAED,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;gBAChC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;YACvE,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAK,MAAM,oBAAoB,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBACrC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;gBAC1B,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,eAAe,GAAA;QAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAClC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC/D,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACpC,IAAI,IAAI,EAAE,CAAC;oBACP,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAEO,wBAAwB,CAAC,IAAU,EAAA;QACvC,IAAI,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;IAC3G,CAAC;IAEM,YAAY,CAAC,IAAU,EAAA;QAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAEe,OAAO,GAAA;QACnB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,wBAAwB;YACxB,IAAK,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACvC,IAAI,aAAa,IAAI,aAAa,CAAC,IAAI,EAAE,CAAC;oBACtC,IAAI,aAAa,CAAC,iBAAiB,EAAE,CAAC;wBAClC,aAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;oBACtF,CAAC;oBAED,IAAI,aAAa,CAAC,eAAe,EAAE,CAAC;wBAChC,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;oBACrF,CAAC;gBACL,CAAC;YACL,CAAC;YACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAK,MAAM,EAAE,IAAI,IAAI,CAAC,eAAe,CAAE,CAAC;gBACpC,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;gBAC/C,IAAI,aAAa,EAAE,CAAC;oBAChB,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;wBAC3B,aAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;oBAC/E,CAAC;oBAED,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC;wBAC5B,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;oBACjF,CAAC;gBACL,CAAC;YACL,CAAC;YACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAChC,CAAC;QAED,KAAK,CAAC,OAAO,EAAE,CAAC;IACpB,CAAC;;AAteD;;GAEG,CACoB,mBAAA,UAAU,GAAG,gBAAgB,AAAnB,CAAoB;AAErD;;;GAGG,CACW,mBAAA,YAAY,GAAW,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,AAAjC,CAAkC;AAE5D;;GAEG,CACW,mBAAA,2BAA2B,GAAG,IAAI,AAAP,CAAQ;AAEjD;;GAEG,CACW,mBAAA,0BAA0B,GAAG,IAAI,AAAP,CAAQ", "debugId": null}}, {"offset": {"line": 1480, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/effectLayer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/effectLayer.ts"], "sourcesContent": ["import { serialize, serializeAsColor4, serializeAsCameraReference } from \"../Misc/decorators\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { ISize } from \"../Maths/math.size\";\r\nimport type { Color4 } from \"../Maths/math.color\";\r\nimport type { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { PostProcess } from \"../PostProcesses/postProcess\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport { GetExponentOfTwo } from \"../Misc/tools.functions\";\r\nimport type { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\nimport { ThinEffectLayer } from \"./thinEffectLayer\";\r\nimport { UniqueIdGenerator } from \"core/Misc/uniqueIdGenerator\";\r\n\r\n/**\r\n * Effect layer options. This helps customizing the behaviour\r\n * of the effect layer.\r\n */\r\nexport interface IEffectLayerOptions {\r\n    /**\r\n     * Multiplication factor apply to the canvas size to compute the render target size\r\n     * used to generated the objects (the smaller the faster). Default: 0.5\r\n     */\r\n    mainTextureRatio: number;\r\n\r\n    /**\r\n     * Enforces a fixed size texture to ensure effect stability across devices. Default: undefined\r\n     */\r\n    mainTextureFixedSize?: number;\r\n\r\n    /**\r\n     * Alpha blending mode used to apply the blur. Default depends of the implementation. Default: ALPHA_COMBINE\r\n     */\r\n    alphaBlendingMode: number;\r\n\r\n    /**\r\n     * The camera attached to the layer. Default: null\r\n     */\r\n    camera: Nullable<Camera>;\r\n\r\n    /**\r\n     * The rendering group to draw the layer in. Default: -1\r\n     */\r\n    renderingGroupId: number;\r\n\r\n    /**\r\n     * The type of the main texture. Default: TEXTURETYPE_UNSIGNED_BYTE\r\n     */\r\n    mainTextureType: number;\r\n\r\n    /**\r\n     * Whether or not to generate a stencil buffer. Default: false\r\n     */\r\n    generateStencilBuffer: boolean;\r\n}\r\n\r\n/**\r\n * The effect layer Helps adding post process effect blended with the main pass.\r\n *\r\n * This can be for instance use to generate glow or highlight effects on the scene.\r\n *\r\n * The effect layer class can not be used directly and is intented to inherited from to be\r\n * customized per effects.\r\n */\r\nexport abstract class EffectLayer {\r\n    private _effectLayerOptions: IEffectLayerOptions;\r\n    private _mainTextureCreatedSize: ISize = { width: 0, height: 0 };\r\n\r\n    protected _scene: Scene;\r\n    protected _engine: AbstractEngine;\r\n    protected _maxSize: number = 0;\r\n    protected _mainTextureDesiredSize: ISize = { width: 0, height: 0 };\r\n    protected _mainTexture: RenderTargetTexture;\r\n    protected get _shouldRender() {\r\n        return this._thinEffectLayer._shouldRender;\r\n    }\r\n    protected set _shouldRender(value) {\r\n        this._thinEffectLayer._shouldRender = value;\r\n    }\r\n    protected _postProcesses: PostProcess[] = [];\r\n    protected _textures: BaseTexture[] = [];\r\n    protected get _emissiveTextureAndColor(): { texture: Nullable<BaseTexture>; color: Color4 } {\r\n        return this._thinEffectLayer._emissiveTextureAndColor;\r\n    }\r\n    protected set _emissiveTextureAndColor(value) {\r\n        this._thinEffectLayer._emissiveTextureAndColor = value;\r\n    }\r\n    protected get _effectIntensity(): { [meshUniqueId: number]: number } {\r\n        return this._thinEffectLayer._effectIntensity;\r\n    }\r\n    protected set _effectIntensity(value) {\r\n        this._thinEffectLayer._effectIntensity = value;\r\n    }\r\n    protected readonly _thinEffectLayer: ThinEffectLayer;\r\n    private readonly _internalThinEffectLayer: boolean;\r\n\r\n    /**\r\n     * Force all the effect layers to compile to glsl even on WebGPU engines.\r\n     * False by default. This is mostly meant for backward compatibility.\r\n     */\r\n    public static get ForceGLSL() {\r\n        return ThinEffectLayer.ForceGLSL;\r\n    }\r\n\r\n    public static set ForceGLSL(value: boolean) {\r\n        ThinEffectLayer.ForceGLSL = value;\r\n    }\r\n\r\n    /**\r\n     * The unique id of the layer\r\n     */\r\n    public readonly uniqueId = UniqueIdGenerator.UniqueId;\r\n\r\n    /**\r\n     * The name of the layer\r\n     */\r\n    @serialize()\r\n    public get name() {\r\n        return this._thinEffectLayer.name;\r\n    }\r\n\r\n    public set name(value: string) {\r\n        this._thinEffectLayer.name = value;\r\n    }\r\n\r\n    /**\r\n     * The clear color of the texture used to generate the glow map.\r\n     */\r\n    @serializeAsColor4()\r\n    public get neutralColor(): Color4 {\r\n        return this._thinEffectLayer.neutralColor;\r\n    }\r\n\r\n    public set neutralColor(value: Color4) {\r\n        this._thinEffectLayer.neutralColor = value;\r\n    }\r\n\r\n    /**\r\n     * Specifies whether the highlight layer is enabled or not.\r\n     */\r\n    @serialize()\r\n    public get isEnabled(): boolean {\r\n        return this._thinEffectLayer.isEnabled;\r\n    }\r\n\r\n    public set isEnabled(value: boolean) {\r\n        this._thinEffectLayer.isEnabled = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the camera attached to the layer.\r\n     */\r\n    @serializeAsCameraReference()\r\n    public get camera(): Nullable<Camera> {\r\n        return this._thinEffectLayer.camera;\r\n    }\r\n\r\n    /**\r\n     * Gets the rendering group id the layer should render in.\r\n     */\r\n    @serialize()\r\n    public get renderingGroupId(): number {\r\n        return this._thinEffectLayer.renderingGroupId;\r\n    }\r\n    public set renderingGroupId(renderingGroupId: number) {\r\n        this._thinEffectLayer.renderingGroupId = renderingGroupId;\r\n    }\r\n\r\n    /**\r\n     * Specifies if the bounding boxes should be rendered normally or if they should undergo the effect of the layer\r\n     */\r\n    @serialize()\r\n    public get disableBoundingBoxesFromEffectLayer() {\r\n        return this._thinEffectLayer.disableBoundingBoxesFromEffectLayer;\r\n    }\r\n\r\n    public set disableBoundingBoxesFromEffectLayer(value: boolean) {\r\n        this._thinEffectLayer.disableBoundingBoxesFromEffectLayer = value;\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the effect layer has been disposed.\r\n     */\r\n    public onDisposeObservable = new Observable<EffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when the effect layer is about rendering the main texture with the glowy parts.\r\n     */\r\n    public onBeforeRenderMainTextureObservable = new Observable<EffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when the generated texture is being merged in the scene.\r\n     */\r\n    public onBeforeComposeObservable = new Observable<EffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when the mesh is rendered into the effect render target.\r\n     */\r\n    public onBeforeRenderMeshToEffect = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * An event triggered after the mesh has been rendered into the effect render target.\r\n     */\r\n    public onAfterRenderMeshToEffect = new Observable<AbstractMesh>();\r\n\r\n    /**\r\n     * An event triggered when the generated texture has been merged in the scene.\r\n     */\r\n    public onAfterComposeObservable = new Observable<EffectLayer>();\r\n\r\n    /**\r\n     * An event triggered when the effect layer changes its size.\r\n     */\r\n    public onSizeChangedObservable = new Observable<EffectLayer>();\r\n\r\n    /**\r\n     * Gets the main texture where the effect is rendered\r\n     */\r\n    public get mainTexture() {\r\n        return this._mainTexture;\r\n    }\r\n\r\n    protected get _shaderLanguage(): ShaderLanguage {\r\n        return this._thinEffectLayer.shaderLanguage;\r\n    }\r\n\r\n    /**\r\n     * Gets the shader language used in this material.\r\n     */\r\n    public get shaderLanguage(): ShaderLanguage {\r\n        return this._thinEffectLayer.shaderLanguage;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _SceneComponentInitialization: (scene: Scene) => void = (_) => {\r\n        throw _WarnImport(\"EffectLayerSceneComponent\");\r\n    };\r\n    /**\r\n     * Sets a specific material to be used to render a mesh/a list of meshes in the layer\r\n     * @param mesh mesh or array of meshes\r\n     * @param material material to use by the layer when rendering the mesh(es). If undefined is passed, the specific material created by the layer will be used.\r\n     */\r\n    public setMaterialForRendering(mesh: AbstractMesh | AbstractMesh[], material?: Material): void {\r\n        this._thinEffectLayer.setMaterialForRendering(mesh, material);\r\n    }\r\n\r\n    /**\r\n     * Gets the intensity of the effect for a specific mesh.\r\n     * @param mesh The mesh to get the effect intensity for\r\n     * @returns The intensity of the effect for the mesh\r\n     */\r\n    public getEffectIntensity(mesh: AbstractMesh) {\r\n        return this._thinEffectLayer.getEffectIntensity(mesh);\r\n    }\r\n\r\n    /**\r\n     * Sets the intensity of the effect for a specific mesh.\r\n     * @param mesh The mesh to set the effect intensity for\r\n     * @param intensity The intensity of the effect for the mesh\r\n     */\r\n    public setEffectIntensity(mesh: AbstractMesh, intensity: number): void {\r\n        this._thinEffectLayer.setEffectIntensity(mesh, intensity);\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new effect Layer and references it in the scene.\r\n     * @param name The name of the layer\r\n     * @param scene The scene to use the layer in\r\n     * @param forceGLSL Use the GLSL code generation for the shader (even on WebGPU). Default is false\r\n     * @param thinEffectLayer The thin instance of the effect layer (optional)\r\n     */\r\n    constructor(\r\n        /** The Friendly of the effect in the scene */\r\n        name: string,\r\n        scene?: Scene,\r\n        forceGLSL = false,\r\n        thinEffectLayer?: ThinEffectLayer\r\n    ) {\r\n        this._internalThinEffectLayer = !thinEffectLayer;\r\n        if (!thinEffectLayer) {\r\n            thinEffectLayer = new ThinEffectLayer(name, scene, forceGLSL, false, this._importShadersAsync.bind(this));\r\n            thinEffectLayer.getEffectName = this.getEffectName.bind(this);\r\n            thinEffectLayer.isReady = this.isReady.bind(this);\r\n            thinEffectLayer._createMergeEffect = this._createMergeEffect.bind(this);\r\n            thinEffectLayer._createTextureAndPostProcesses = this._createTextureAndPostProcesses.bind(this);\r\n            thinEffectLayer._internalCompose = this._internalRender.bind(this);\r\n            thinEffectLayer._setEmissiveTextureAndColor = this._setEmissiveTextureAndColor.bind(this);\r\n            thinEffectLayer._numInternalDraws = this._numInternalDraws.bind(this);\r\n            thinEffectLayer._addCustomEffectDefines = this._addCustomEffectDefines.bind(this);\r\n            thinEffectLayer.hasMesh = this.hasMesh.bind(this);\r\n            thinEffectLayer.shouldRender = this.shouldRender.bind(this);\r\n            thinEffectLayer._shouldRenderMesh = this._shouldRenderMesh.bind(this);\r\n            thinEffectLayer._canRenderMesh = this._canRenderMesh.bind(this);\r\n            thinEffectLayer._useMeshMaterial = this._useMeshMaterial.bind(this);\r\n        }\r\n\r\n        this._thinEffectLayer = thinEffectLayer;\r\n        this.name = name;\r\n\r\n        this._scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        EffectLayer._SceneComponentInitialization(this._scene);\r\n\r\n        this._engine = this._scene.getEngine();\r\n        this._maxSize = this._engine.getCaps().maxTextureSize;\r\n        this._scene.addEffectLayer(this);\r\n\r\n        this._thinEffectLayer.onDisposeObservable.add(() => {\r\n            this.onDisposeObservable.notifyObservers(this);\r\n        });\r\n\r\n        this._thinEffectLayer.onBeforeRenderLayerObservable.add(() => {\r\n            this.onBeforeRenderMainTextureObservable.notifyObservers(this);\r\n        });\r\n\r\n        this._thinEffectLayer.onBeforeComposeObservable.add(() => {\r\n            this.onBeforeComposeObservable.notifyObservers(this);\r\n        });\r\n\r\n        this._thinEffectLayer.onBeforeRenderMeshToEffect.add((mesh) => {\r\n            this.onBeforeRenderMeshToEffect.notifyObservers(mesh);\r\n        });\r\n\r\n        this._thinEffectLayer.onAfterRenderMeshToEffect.add((mesh) => {\r\n            this.onAfterRenderMeshToEffect.notifyObservers(mesh);\r\n        });\r\n\r\n        this._thinEffectLayer.onAfterComposeObservable.add(() => {\r\n            this.onAfterComposeObservable.notifyObservers(this);\r\n        });\r\n    }\r\n\r\n    protected get _shadersLoaded() {\r\n        return this._thinEffectLayer._shadersLoaded;\r\n    }\r\n\r\n    protected set _shadersLoaded(value: boolean) {\r\n        this._thinEffectLayer._shadersLoaded = value;\r\n    }\r\n\r\n    /**\r\n     * Get the effect name of the layer.\r\n     * @returns The effect name\r\n     */\r\n    public abstract getEffectName(): string;\r\n\r\n    /**\r\n     * Checks for the readiness of the element composing the layer.\r\n     * @param subMesh the mesh to check for\r\n     * @param useInstances specify whether or not to use instances to render the mesh\r\n     * @returns true if ready otherwise, false\r\n     */\r\n    public abstract isReady(subMesh: SubMesh, useInstances: boolean): boolean;\r\n\r\n    /**\r\n     * Returns whether or not the layer needs stencil enabled during the mesh rendering.\r\n     * @returns true if the effect requires stencil during the main canvas render pass.\r\n     */\r\n    public abstract needStencil(): boolean;\r\n\r\n    /**\r\n     * Create the merge effect. This is the shader use to blit the information back\r\n     * to the main canvas at the end of the scene rendering.\r\n     * @returns The effect containing the shader used to merge the effect on the  main canvas\r\n     */\r\n    protected abstract _createMergeEffect(): Effect;\r\n\r\n    /**\r\n     * Creates the render target textures and post processes used in the effect layer.\r\n     */\r\n    protected abstract _createTextureAndPostProcesses(): void;\r\n\r\n    /**\r\n     * Implementation specific of rendering the generating effect on the main canvas.\r\n     * @param effect The effect used to render through\r\n     * @param renderNum Index of the _internalRender call (0 for the first time _internalRender is called, 1 for the second time, etc. _internalRender is called the number of times returned by _numInternalDraws())\r\n     */\r\n    protected abstract _internalRender(effect: Effect, renderIndex: number): void;\r\n\r\n    /**\r\n     * Sets the required values for both the emissive texture and and the main color.\r\n     */\r\n    protected abstract _setEmissiveTextureAndColor(mesh: Mesh, subMesh: SubMesh, material: Material): void;\r\n\r\n    /**\r\n     * Free any resources and references associated to a mesh.\r\n     * Internal use\r\n     * @param mesh The mesh to free.\r\n     */\r\n    public abstract _disposeMesh(mesh: Mesh): void;\r\n\r\n    /**\r\n     * Serializes this layer (Glow or Highlight for example)\r\n     * @returns a serialized layer object\r\n     */\r\n    public abstract serialize?(): any;\r\n\r\n    /**\r\n     * Number of times _internalRender will be called. Some effect layers need to render the mesh several times, so they should override this method with the number of times the mesh should be rendered\r\n     * @returns Number of times a mesh must be rendered in the layer\r\n     */\r\n    protected _numInternalDraws(): number {\r\n        return this._internalThinEffectLayer ? 1 : this._thinEffectLayer._numInternalDraws();\r\n    }\r\n\r\n    /**\r\n     * Initializes the effect layer with the required options.\r\n     * @param options Sets of none mandatory options to use with the layer (see IEffectLayerOptions for more information)\r\n     */\r\n    protected _init(options: Partial<IEffectLayerOptions>): void {\r\n        // Adapt options\r\n        this._effectLayerOptions = {\r\n            mainTextureRatio: 0.5,\r\n            alphaBlendingMode: Constants.ALPHA_COMBINE,\r\n            camera: null,\r\n            renderingGroupId: -1,\r\n            mainTextureType: Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n            generateStencilBuffer: false,\r\n            ...options,\r\n        };\r\n\r\n        this._setMainTextureSize();\r\n        this._thinEffectLayer._init(options);\r\n        this._createMainTexture();\r\n        this._createTextureAndPostProcesses();\r\n    }\r\n\r\n    /**\r\n     * Sets the main texture desired size which is the closest power of two\r\n     * of the engine canvas size.\r\n     */\r\n    private _setMainTextureSize(): void {\r\n        if (this._effectLayerOptions.mainTextureFixedSize) {\r\n            this._mainTextureDesiredSize.width = this._effectLayerOptions.mainTextureFixedSize;\r\n            this._mainTextureDesiredSize.height = this._effectLayerOptions.mainTextureFixedSize;\r\n        } else {\r\n            this._mainTextureDesiredSize.width = this._engine.getRenderWidth() * this._effectLayerOptions.mainTextureRatio;\r\n            this._mainTextureDesiredSize.height = this._engine.getRenderHeight() * this._effectLayerOptions.mainTextureRatio;\r\n\r\n            this._mainTextureDesiredSize.width = this._engine.needPOTTextures\r\n                ? GetExponentOfTwo(this._mainTextureDesiredSize.width, this._maxSize)\r\n                : this._mainTextureDesiredSize.width;\r\n            this._mainTextureDesiredSize.height = this._engine.needPOTTextures\r\n                ? GetExponentOfTwo(this._mainTextureDesiredSize.height, this._maxSize)\r\n                : this._mainTextureDesiredSize.height;\r\n        }\r\n\r\n        this._mainTextureDesiredSize.width = Math.floor(this._mainTextureDesiredSize.width);\r\n        this._mainTextureDesiredSize.height = Math.floor(this._mainTextureDesiredSize.height);\r\n    }\r\n\r\n    /**\r\n     * Creates the main texture for the effect layer.\r\n     */\r\n    protected _createMainTexture(): void {\r\n        this._mainTexture = new RenderTargetTexture(\r\n            \"EffectLayerMainRTT\",\r\n            {\r\n                width: this._mainTextureDesiredSize.width,\r\n                height: this._mainTextureDesiredSize.height,\r\n            },\r\n            this._scene,\r\n            {\r\n                type: this._effectLayerOptions.mainTextureType,\r\n                samplingMode: Texture.TRILINEAR_SAMPLINGMODE,\r\n                generateStencilBuffer: this._effectLayerOptions.generateStencilBuffer,\r\n                existingObjectRenderer: this._thinEffectLayer.objectRenderer,\r\n            }\r\n        );\r\n        this._mainTexture.activeCamera = this._effectLayerOptions.camera;\r\n        this._mainTexture.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this._mainTexture.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        this._mainTexture.anisotropicFilteringLevel = 1;\r\n        this._mainTexture.updateSamplingMode(Texture.BILINEAR_SAMPLINGMODE);\r\n        this._mainTexture.renderParticles = false;\r\n        this._mainTexture.renderList = null;\r\n        this._mainTexture.ignoreCameraViewport = true;\r\n\r\n        this._mainTexture.onClearObservable.add((engine: AbstractEngine) => {\r\n            engine.clear(this.neutralColor, true, true, true);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds specific effects defines.\r\n     * @param defines The defines to add specifics to.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _addCustomEffectDefines(defines: string[]): void {\r\n        // Nothing to add by default.\r\n    }\r\n\r\n    /**\r\n     * Checks for the readiness of the element composing the layer.\r\n     * @param subMesh the mesh to check for\r\n     * @param useInstances specify whether or not to use instances to render the mesh\r\n     * @param emissiveTexture the associated emissive texture used to generate the glow\r\n     * @returns true if ready otherwise, false\r\n     */\r\n    protected _isReady(subMesh: SubMesh, useInstances: boolean, emissiveTexture: Nullable<BaseTexture>): boolean {\r\n        return this._internalThinEffectLayer\r\n            ? this._thinEffectLayer._internalIsSubMeshReady(subMesh, useInstances, emissiveTexture)\r\n            : this._thinEffectLayer._isSubMeshReady(subMesh, useInstances, emissiveTexture);\r\n    }\r\n\r\n    protected async _importShadersAsync(): Promise<void> {}\r\n\r\n    protected _arePostProcessAndMergeReady(): boolean {\r\n        return this._internalThinEffectLayer ? this._thinEffectLayer._internalIsLayerReady() : this._thinEffectLayer.isLayerReady();\r\n    }\r\n\r\n    /**\r\n     * Checks if the layer is ready to be used.\r\n     * @returns true if the layer is ready to be used\r\n     */\r\n    public isLayerReady(): boolean {\r\n        return this._arePostProcessAndMergeReady() && this._mainTexture.isReady();\r\n    }\r\n\r\n    /**\r\n     * Renders the glowing part of the scene by blending the blurred glowing meshes on top of the rendered scene.\r\n     */\r\n    public render(): void {\r\n        if (!this._thinEffectLayer.compose()) {\r\n            return;\r\n        }\r\n\r\n        // Handle size changes.\r\n        this._setMainTextureSize();\r\n        if (\r\n            (this._mainTextureCreatedSize.width !== this._mainTextureDesiredSize.width || this._mainTextureCreatedSize.height !== this._mainTextureDesiredSize.height) &&\r\n            this._mainTextureDesiredSize.width !== 0 &&\r\n            this._mainTextureDesiredSize.height !== 0\r\n        ) {\r\n            // Recreate RTT and post processes on size change.\r\n            this.onSizeChangedObservable.notifyObservers(this);\r\n            this._disposeTextureAndPostProcesses();\r\n            this._createMainTexture();\r\n            this._createTextureAndPostProcesses();\r\n            this._mainTextureCreatedSize.width = this._mainTextureDesiredSize.width;\r\n            this._mainTextureCreatedSize.height = this._mainTextureDesiredSize.height;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Determine if a given mesh will be used in the current effect.\r\n     * @param mesh mesh to test\r\n     * @returns true if the mesh will be used\r\n     */\r\n    public hasMesh(mesh: AbstractMesh): boolean {\r\n        return this._internalThinEffectLayer ? this._thinEffectLayer._internalHasMesh(mesh) : this._thinEffectLayer.hasMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Returns true if the layer contains information to display, otherwise false.\r\n     * @returns true if the glow layer should be rendered\r\n     */\r\n    public shouldRender(): boolean {\r\n        return this._internalThinEffectLayer ? this._thinEffectLayer._internalShouldRender() : this._thinEffectLayer.shouldRender();\r\n    }\r\n\r\n    /**\r\n     * Returns true if the mesh should render, otherwise false.\r\n     * @param mesh The mesh to render\r\n     * @returns true if it should render otherwise false\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _shouldRenderMesh(mesh: AbstractMesh): boolean {\r\n        return this._internalThinEffectLayer ? true : this._thinEffectLayer._shouldRenderMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Returns true if the mesh can be rendered, otherwise false.\r\n     * @param mesh The mesh to render\r\n     * @param material The material used on the mesh\r\n     * @returns true if it can be rendered otherwise false\r\n     */\r\n    protected _canRenderMesh(mesh: AbstractMesh, material: Material): boolean {\r\n        return this._internalThinEffectLayer ? this._thinEffectLayer._internalCanRenderMesh(mesh, material) : this._thinEffectLayer._canRenderMesh(mesh, material);\r\n    }\r\n\r\n    /**\r\n     * Returns true if the mesh should render, otherwise false.\r\n     * @returns true if it should render otherwise false\r\n     */\r\n    protected _shouldRenderEmissiveTextureForMesh(): boolean {\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Defines whether the current material of the mesh should be use to render the effect.\r\n     * @param mesh defines the current mesh to render\r\n     * @returns true if the mesh material should be use\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    protected _useMeshMaterial(mesh: AbstractMesh): boolean {\r\n        return this._internalThinEffectLayer ? false : this._thinEffectLayer._useMeshMaterial(mesh);\r\n    }\r\n\r\n    /**\r\n     * Rebuild the required buffers.\r\n     * @internal Internal use only.\r\n     */\r\n    public _rebuild(): void {\r\n        this._thinEffectLayer._rebuild();\r\n    }\r\n\r\n    /**\r\n     * Dispose only the render target textures and post process.\r\n     */\r\n    private _disposeTextureAndPostProcesses(): void {\r\n        this._mainTexture.dispose();\r\n\r\n        for (let i = 0; i < this._postProcesses.length; i++) {\r\n            if (this._postProcesses[i]) {\r\n                this._postProcesses[i].dispose();\r\n            }\r\n        }\r\n        this._postProcesses = [];\r\n\r\n        for (let i = 0; i < this._textures.length; i++) {\r\n            if (this._textures[i]) {\r\n                this._textures[i].dispose();\r\n            }\r\n        }\r\n        this._textures = [];\r\n    }\r\n\r\n    /**\r\n     * Dispose the highlight layer and free resources.\r\n     */\r\n    public dispose(): void {\r\n        this._thinEffectLayer.dispose();\r\n\r\n        // Clean textures and post processes\r\n        this._disposeTextureAndPostProcesses();\r\n\r\n        // Remove from scene\r\n        this._scene.removeEffectLayer(this);\r\n\r\n        // Callback\r\n        this.onDisposeObservable.clear();\r\n        this.onBeforeRenderMainTextureObservable.clear();\r\n        this.onBeforeComposeObservable.clear();\r\n        this.onBeforeRenderMeshToEffect.clear();\r\n        this.onAfterRenderMeshToEffect.clear();\r\n        this.onAfterComposeObservable.clear();\r\n        this.onSizeChangedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the effect layer\r\n     * @returns the string with the class name of the effect layer\r\n     */\r\n    public getClassName(): string {\r\n        return \"EffectLayer\";\r\n    }\r\n\r\n    /**\r\n     * Creates an effect layer from parsed effect layer data\r\n     * @param parsedEffectLayer defines effect layer data\r\n     * @param scene defines the current scene\r\n     * @param rootUrl defines the root URL containing the effect layer information\r\n     * @returns a parsed effect Layer\r\n     */\r\n    public static Parse(parsedEffectLayer: any, scene: Scene, rootUrl: string): EffectLayer {\r\n        const effectLayerType = Tools.Instantiate(parsedEffectLayer.customType);\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\r\n        return effectLayerType.Parse(parsedEffectLayer, scene, rootUrl);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,0BAA0B,EAAE,MAAM,oBAAoB,CAAC;AAC9F,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAOhD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAMrD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAKhF,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAE3D,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,iBAAiB,EAAE,qCAAoC;;;;;;;;;;;;AAoD1D,MAAgB,WAAW;IAS7B,IAAc,aAAa,GAAA;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;IAC/C,CAAC;IACD,IAAc,aAAa,CAAC,KAAK,EAAA;QAC7B,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC;IAChD,CAAC;IAGD,IAAc,wBAAwB,GAAA;QAClC,OAAO,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC;IAC1D,CAAC;IACD,IAAc,wBAAwB,CAAC,KAAK,EAAA;QACxC,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,GAAG,KAAK,CAAC;IAC3D,CAAC;IACD,IAAc,gBAAgB,GAAA;QAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;IAClD,CAAC;IACD,IAAc,gBAAgB,CAAC,KAAK,EAAA;QAChC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,GAAG,KAAK,CAAC;IACnD,CAAC;IAID;;;OAGG,CACI,MAAM,KAAK,SAAS,GAAA;QACvB,wKAAO,kBAAe,CAAC,SAAS,CAAC;IACrC,CAAC;IAEM,MAAM,KAAK,SAAS,CAAC,KAAc,EAAA;yKACtC,kBAAe,CAAC,SAAS,GAAG,KAAK,CAAC;IACtC,CAAC;IAOD;;OAEG,CAEH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACtC,CAAC;IAED,IAAW,IAAI,CAAC,KAAa,EAAA;QACzB,IAAI,CAAC,gBAAgB,CAAC,IAAI,GAAG,KAAK,CAAC;IACvC,CAAC;IAED;;OAEG,CAEH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;IAC9C,CAAC;IAED,IAAW,YAAY,CAAC,KAAa,EAAA;QACjC,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED;;OAEG,CAEH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IAC3C,CAAC;IAED,IAAW,SAAS,CAAC,KAAc,EAAA;QAC/B,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG,CAEH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;IACxC,CAAC;IAED;;OAEG,CAEH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;IAClD,CAAC;IACD,IAAW,gBAAgB,CAAC,gBAAwB,EAAA;QAChD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC9D,CAAC;IAED;;OAEG,CAEH,IAAW,mCAAmC,GAAA;QAC1C,OAAO,IAAI,CAAC,gBAAgB,CAAC,mCAAmC,CAAC;IACrE,CAAC;IAED,IAAW,mCAAmC,CAAC,KAAc,EAAA;QACzD,IAAI,CAAC,gBAAgB,CAAC,mCAAmC,GAAG,KAAK,CAAC;IACtE,CAAC;IAqCD;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAc,eAAe,GAAA;QACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;IAChD,CAAC;IAQD;;;;OAIG,CACI,uBAAuB,CAAC,IAAmC,EAAE,QAAmB,EAAA;QACnF,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAClE,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,IAAkB,EAAA;QACxC,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED;;;;OAIG,CACI,kBAAkB,CAAC,IAAkB,EAAE,SAAiB,EAAA;QAC3D,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;;OAMG,CACH,YACI,4CAAA,EAA8C,CAC9C,IAAY,EACZ,KAAa,EACb,SAAS,GAAG,KAAK,EACjB,eAAiC,CAAA;QApN7B,IAAA,CAAA,uBAAuB,GAAU;YAAE,KAAK,EAAE,CAAC;YAAE,MAAM,EAAE,CAAC;QAAA,CAAE,CAAC;QAIvD,IAAA,CAAA,QAAQ,GAAW,CAAC,CAAC;QACrB,IAAA,CAAA,uBAAuB,GAAU;YAAE,KAAK,EAAE,CAAC;YAAE,MAAM,EAAE,CAAC;QAAA,CAAE,CAAC;QAQzD,IAAA,CAAA,cAAc,GAAkB,EAAE,CAAC;QACnC,IAAA,CAAA,SAAS,GAAkB,EAAE,CAAC;QA4BxC;;WAEG,CACa,IAAA,CAAA,QAAQ,oKAAG,oBAAiB,CAAC,QAAQ,CAAC;QAqEtD;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,8JAAI,aAAU,EAAe,CAAC;QAE3D;;WAEG,CACI,IAAA,CAAA,mCAAmC,GAAG,8JAAI,aAAU,EAAe,CAAC;QAE3E;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,8JAAI,aAAU,EAAe,CAAC;QAEjE;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,8JAAI,aAAU,EAAgB,CAAC;QAEnE;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,IAAI,uKAAU,EAAgB,CAAC;QAElE;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,8JAAI,aAAU,EAAe,CAAC;QAEhE;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAG,8JAAI,aAAU,EAAe,CAAC;QAmE3D,IAAI,CAAC,wBAAwB,GAAG,CAAC,eAAe,CAAC;QACjD,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,eAAe,GAAG,qKAAI,kBAAe,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAC1G,eAAe,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9D,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,eAAe,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxE,eAAe,CAAC,8BAA8B,GAAG,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChG,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnE,eAAe,CAAC,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1F,eAAe,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,eAAe,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClF,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClD,eAAe,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,eAAe,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtE,eAAe,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChE,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,IAAI,CAAC,MAAM,GAAG,KAAK,kKAAW,cAAW,CAAC,gBAAgB,CAAC;QAC3D,WAAW,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEvD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,cAAc,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAEjC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/C,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,GAAG,CAAC,GAAG,EAAE;YACzD,IAAI,CAAC,mCAAmC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,GAAG,CAAC,GAAG,EAAE;YACrD,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1D,IAAI,CAAC,0BAA0B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACzD,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACpD,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACP,CAAC;IAED,IAAc,cAAc,GAAA;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;IAChD,CAAC;IAED,IAAc,cAAc,CAAC,KAAc,EAAA;QACvC,IAAI,CAAC,gBAAgB,CAAC,cAAc,GAAG,KAAK,CAAC;IACjD,CAAC;IA2DD;;;OAGG,CACO,iBAAiB,GAAA;QACvB,OAAO,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;IACzF,CAAC;IAED;;;OAGG,CACO,KAAK,CAAC,OAAqC,EAAA;QACjD,gBAAgB;QAChB,IAAI,CAAC,mBAAmB,GAAG;YACvB,gBAAgB,EAAE,GAAG;YACrB,iBAAiB,EAAE,SAAS,CAAC,aAAa;YAC1C,MAAM,EAAE,IAAI;YACZ,gBAAgB,EAAE,CAAC,CAAC;YACpB,eAAe,EAAE,SAAS,CAAC,yBAAyB;YACpD,qBAAqB,EAAE,KAAK;YAC5B,GAAG,OAAO;SACb,CAAC;QAEF,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,8BAA8B,EAAE,CAAC;IAC1C,CAAC;IAED;;;OAGG,CACK,mBAAmB,GAAA;QACvB,IAAI,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;YACnF,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;QACxF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,uBAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;YAC/G,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;YAEjH,IAAI,CAAC,uBAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,yKAC3D,mBAAA,AAAgB,EAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,GACnE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;YACzC,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,yKAC5D,mBAAA,AAAgB,EAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GACpE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QACpF,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG,CACO,kBAAkB,GAAA;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,0MAAmB,CACvC,oBAAoB,EACpB;YACI,KAAK,EAAE,IAAI,CAAC,uBAAuB,CAAC,KAAK;YACzC,MAAM,EAAE,IAAI,CAAC,uBAAuB,CAAC,MAAM;SAC9C,EACD,IAAI,CAAC,MAAM,EACX;YACI,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,eAAe;YAC9C,YAAY,0KAAE,UAAO,CAAC,sBAAsB;YAC5C,qBAAqB,EAAE,IAAI,CAAC,mBAAmB,CAAC,qBAAqB;YACrE,sBAAsB,EAAE,IAAI,CAAC,gBAAgB,CAAC,cAAc;SAC/D,CACJ,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;QACjE,IAAI,CAAC,YAAY,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;QACpD,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,kLAAO,CAAC,iBAAiB,CAAC;QACpD,IAAI,CAAC,YAAY,CAAC,yBAAyB,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,YAAY,CAAC,kBAAkB,yKAAC,UAAO,CAAC,qBAAqB,CAAC,CAAC;QACpE,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,KAAK,CAAC;QAC1C,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC,YAAY,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAE9C,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAsB,EAAE,EAAE;YAC/D,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACH,6DAA6D;IACnD,uBAAuB,CAAC,OAAiB,EAAA;IAC/C,6BAA6B;IACjC,CAAC;IAED;;;;;;OAMG,CACO,QAAQ,CAAC,OAAgB,EAAE,YAAqB,EAAE,eAAsC,EAAA;QAC9F,OAAO,IAAI,CAAC,wBAAwB,GAC9B,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,EAAE,YAAY,EAAE,eAAe,CAAC,GACrF,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;IACxF,CAAC;IAES,KAAK,CAAC,mBAAmB,GAAA,CAAmB,CAAC;IAE7C,4BAA4B,GAAA;QAClC,OAAO,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;IAChI,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,IAAI,CAAC,4BAA4B,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;IAC9E,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;YACnC,OAAO;QACX,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IACI,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,KAAK,IAAI,CAAC,uBAAuB,CAAC,KAAK,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,KAAK,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAC1J,IAAI,CAAC,uBAAuB,CAAC,KAAK,KAAK,CAAC,IACxC,IAAI,CAAC,uBAAuB,CAAC,MAAM,KAAK,CAAC,EAC3C,CAAC;YACC,kDAAkD;YAClD,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACnD,IAAI,CAAC,+BAA+B,EAAE,CAAC;YACvC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,8BAA8B,EAAE,CAAC;YACtC,IAAI,CAAC,uBAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;YACxE,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;QAC9E,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,OAAO,CAAC,IAAkB,EAAA;QAC7B,OAAO,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9H,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;IAChI,CAAC;IAED;;;;OAIG,CACH,6DAA6D;IACnD,iBAAiB,CAAC,IAAkB,EAAA;QAC1C,OAAO,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAChG,CAAC;IAED;;;;;OAKG,CACO,cAAc,CAAC,IAAkB,EAAE,QAAkB,EAAA;QAC3D,OAAO,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC/J,CAAC;IAED;;;OAGG,CACO,mCAAmC,GAAA;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACH,6DAA6D;IACnD,gBAAgB,CAAC,IAAkB,EAAA;QACzC,OAAO,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAChG,CAAC;IAED;;;OAGG,CACI,QAAQ,GAAA;QACX,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG,CACK,+BAA+B,GAAA;QACnC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAE5B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAClD,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YACrC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7C,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAChC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAEhC,oCAAoC;QACpC,IAAI,CAAC,+BAA+B,EAAE,CAAC;QAEvC,oBAAoB;QACpB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAEpC,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,mCAAmC,CAAC,KAAK,EAAE,CAAC;QACjD,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;QACxC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,KAAK,CAAC,iBAAsB,EAAE,KAAY,EAAE,OAAe,EAAA;QACrE,MAAM,eAAe,uJAAG,SAAK,CAAC,WAAW,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAExE,+DAA+D;QAC/D,OAAO,eAAe,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IACpE,CAAC;;AAtbD;;GAEG,CACW,YAAA,6BAA6B,GAA2B,CAAC,CAAC,EAAE,EAAE;IACxE,kKAAM,cAAA,AAAW,EAAC,2BAA2B,CAAC,CAAC;AACnD,CAAC,AAF0C,CAEzC;wJAzHF,aAAA,EAAA;KADC,yKAAA,AAAS,EAAE;uCAGX;wJAUD,aAAA,EAAA;IADC,kLAAA,AAAiB,EAAE;+CAGnB;wJAUD,aAAA,EAAA;KADC,yKAAA,AAAS,EAAE;4CAGX;wJAUD,aAAA,EAAA;IADC,2LAAA,AAA0B,EAAE;yCAG5B;wJAMD,aAAA,EAAA;IADC,0KAAA,AAAS,EAAE;mDAGX;wJASD,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;sEAGX", "debugId": null}}, {"offset": {"line": 1940, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/effectLayerSceneComponent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/effectLayerSceneComponent.ts"], "sourcesContent": ["import { Camera } from \"../Cameras/camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { ISceneSerializableComponent } from \"../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { EffectLayer } from \"./effectLayer\";\r\nimport type { AssetContainer } from \"../assetContainer\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { AddParser } from \"core/Loading/Plugins/babylonFileParser.function\";\r\nimport type { IAssetContainer } from \"core/IAssetContainer\";\r\n\r\n// Adds the parser to the scene parsers.\r\nAddParser(SceneComponentConstants.NAME_EFFECTLAYER, (parsedData: any, scene: Scene, container: AssetContainer, rootUrl: string) => {\r\n    if (parsedData.effectLayers) {\r\n        if (!container.effectLayers) {\r\n            container.effectLayers = [] as EffectLayer[];\r\n        }\r\n\r\n        for (let index = 0; index < parsedData.effectLayers.length; index++) {\r\n            const effectLayer = EffectLayer.Parse(parsedData.effectLayers[index], scene, rootUrl);\r\n            container.effectLayers.push(effectLayer);\r\n        }\r\n    }\r\n});\r\n\r\n/**\r\n * Defines the layer scene component responsible to manage any effect layers\r\n * in a given scene.\r\n */\r\nexport class EffectLayerSceneComponent implements ISceneSerializableComponent {\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_EFFECTLAYER;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    private _engine: AbstractEngine;\r\n    private _renderEffects = false;\r\n    private _needStencil = false;\r\n    private _previousStencilState = false;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene?: Scene) {\r\n        this.scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        if (!this.scene) {\r\n            return;\r\n        }\r\n        this._engine = this.scene.getEngine();\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._isReadyForMeshStage.registerStep(SceneComponentConstants.STEP_ISREADYFORMESH_EFFECTLAYER, this, this._isReadyForMesh);\r\n\r\n        this.scene._cameraDrawRenderTargetStage.registerStep(SceneComponentConstants.STEP_CAMERADRAWRENDERTARGET_EFFECTLAYER, this, this._renderMainTexture);\r\n\r\n        this.scene._beforeCameraDrawStage.registerStep(SceneComponentConstants.STEP_BEFORECAMERADRAW_EFFECTLAYER, this, this._setStencil);\r\n\r\n        this.scene._afterRenderingGroupDrawStage.registerStep(SceneComponentConstants.STEP_AFTERRENDERINGGROUPDRAW_EFFECTLAYER_DRAW, this, this._drawRenderingGroup);\r\n\r\n        this.scene._afterCameraDrawStage.registerStep(SceneComponentConstants.STEP_AFTERCAMERADRAW_EFFECTLAYER, this, this._setStencilBack);\r\n        this.scene._afterCameraDrawStage.registerStep(SceneComponentConstants.STEP_AFTERCAMERADRAW_EFFECTLAYER_DRAW, this, this._drawCamera);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        const layers = this.scene.effectLayers;\r\n        for (const effectLayer of layers) {\r\n            effectLayer._rebuild();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes the component data to the specified json object\r\n     * @param serializationObject The object to serialize to\r\n     */\r\n    public serialize(serializationObject: any): void {\r\n        // Effect layers\r\n        serializationObject.effectLayers = [];\r\n\r\n        const layers = this.scene.effectLayers;\r\n        for (const effectLayer of layers) {\r\n            if (effectLayer.serialize) {\r\n                serializationObject.effectLayers.push(effectLayer.serialize());\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds all the elements from the container to the scene\r\n     * @param container the container holding the elements\r\n     */\r\n    public addFromContainer(container: IAssetContainer): void {\r\n        if (!container.effectLayers) {\r\n            return;\r\n        }\r\n        for (const o of container.effectLayers) {\r\n            this.scene.addEffectLayer(o);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes all the elements in the container from the scene\r\n     * @param container contains the elements to remove\r\n     * @param dispose if the removed element should be disposed (default: false)\r\n     */\r\n    public removeFromContainer(container: IAssetContainer, dispose?: boolean): void {\r\n        if (!container.effectLayers) {\r\n            return;\r\n        }\r\n        for (const o of container.effectLayers) {\r\n            this.scene.removeEffectLayer(o);\r\n            if (dispose) {\r\n                o.dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        const layers = this.scene.effectLayers;\r\n        while (layers.length) {\r\n            layers[0].dispose();\r\n        }\r\n    }\r\n\r\n    private _isReadyForMesh(mesh: AbstractMesh, hardwareInstancedRendering: boolean): boolean {\r\n        const currentRenderPassId = this._engine.currentRenderPassId;\r\n        const layers = this.scene.effectLayers;\r\n        for (const layer of layers) {\r\n            if (!layer.hasMesh(mesh)) {\r\n                continue;\r\n            }\r\n\r\n            const renderTarget = <RenderTargetTexture>(<any>layer)._mainTexture;\r\n            this._engine.currentRenderPassId = renderTarget.renderPassId;\r\n\r\n            for (const subMesh of mesh.subMeshes) {\r\n                if (!layer.isReady(subMesh, hardwareInstancedRendering)) {\r\n                    this._engine.currentRenderPassId = currentRenderPassId;\r\n                    return false;\r\n                }\r\n            }\r\n        }\r\n        this._engine.currentRenderPassId = currentRenderPassId;\r\n        return true;\r\n    }\r\n\r\n    private _renderMainTexture(camera: Camera): boolean {\r\n        this._renderEffects = false;\r\n        this._needStencil = false;\r\n\r\n        let needRebind = false;\r\n\r\n        const layers = this.scene.effectLayers;\r\n        if (layers && layers.length > 0) {\r\n            this._previousStencilState = this._engine.getStencilBuffer();\r\n            for (const effectLayer of layers) {\r\n                if (\r\n                    effectLayer.shouldRender() &&\r\n                    (!effectLayer.camera ||\r\n                        (effectLayer.camera.cameraRigMode === Camera.RIG_MODE_NONE && camera === effectLayer.camera) ||\r\n                        (effectLayer.camera.cameraRigMode !== Camera.RIG_MODE_NONE && effectLayer.camera._rigCameras.indexOf(camera) > -1))\r\n                ) {\r\n                    this._renderEffects = true;\r\n                    this._needStencil = this._needStencil || effectLayer.needStencil();\r\n\r\n                    const renderTarget = <RenderTargetTexture>(<any>effectLayer)._mainTexture;\r\n                    if (renderTarget._shouldRender()) {\r\n                        this.scene.incrementRenderId();\r\n                        renderTarget.render(false, false);\r\n                        needRebind = true;\r\n                    }\r\n                }\r\n            }\r\n\r\n            this.scene.incrementRenderId();\r\n        }\r\n\r\n        return needRebind;\r\n    }\r\n\r\n    private _setStencil() {\r\n        // Activate effect Layer stencil\r\n        if (this._needStencil) {\r\n            this._engine.setStencilBuffer(true);\r\n        }\r\n    }\r\n\r\n    private _setStencilBack() {\r\n        // Restore effect Layer stencil\r\n        if (this._needStencil) {\r\n            this._engine.setStencilBuffer(this._previousStencilState);\r\n        }\r\n    }\r\n\r\n    private _draw(renderingGroupId: number): void {\r\n        if (this._renderEffects) {\r\n            this._engine.setDepthBuffer(false);\r\n\r\n            const layers = this.scene.effectLayers;\r\n            for (let i = 0; i < layers.length; i++) {\r\n                const effectLayer = layers[i];\r\n                if (effectLayer.renderingGroupId === renderingGroupId) {\r\n                    if (effectLayer.shouldRender()) {\r\n                        effectLayer.render();\r\n                    }\r\n                }\r\n            }\r\n            this._engine.setDepthBuffer(true);\r\n        }\r\n    }\r\n\r\n    private _drawCamera(): void {\r\n        if (this._renderEffects) {\r\n            this._draw(-1);\r\n        }\r\n    }\r\n    private _drawRenderingGroup(index: number): void {\r\n        if (!this.scene._isInIntermediateRendering() && this._renderEffects) {\r\n            this._draw(index);\r\n        }\r\n    }\r\n}\r\n\r\nEffectLayer._SceneComponentInitialization = (scene: Scene) => {\r\n    let component = scene._getComponent(SceneComponentConstants.NAME_EFFECTLAYER) as EffectLayerSceneComponent;\r\n    if (!component) {\r\n        component = new EffectLayerSceneComponent(scene);\r\n        scene._addComponent(component);\r\n    }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAM3C,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,yDAAwD;;;;;;AAG5E,wCAAwC;+LACxC,YAAA,AAAS,wJAAC,0BAAuB,CAAC,gBAAgB,EAAE,CAAC,UAAe,EAAE,KAAY,EAAE,SAAyB,EAAE,OAAe,EAAE,EAAE;IAC9H,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YAC1B,SAAS,CAAC,YAAY,GAAG,EAAmB,CAAC;QACjD,CAAC;QAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAClE,MAAM,WAAW,gKAAG,cAAW,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YACtF,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;AACL,CAAC,CAAC,CAAC;AAMG,MAAO,yBAAyB;IAgBlC;;;OAGG,CACH,YAAY,KAAa,CAAA;QAnBzB;;WAEG,CACa,IAAA,CAAA,IAAI,yJAAG,0BAAuB,CAAC,gBAAgB,CAAC;QAQxD,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,YAAY,GAAG,KAAK,CAAC;QACrB,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAOlC,IAAI,CAAC,KAAK,GAAG,KAAK,kKAAW,cAAW,CAAC,gBAAgB,CAAC;QAC1D,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG,CACI,QAAQ,GAAA;QACX,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,YAAY,uJAAC,0BAAuB,CAAC,+BAA+B,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAElI,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,YAAY,uJAAC,0BAAuB,CAAC,uCAAuC,EAAE,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAErJ,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,YAAY,uJAAC,0BAAuB,CAAC,iCAAiC,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAElI,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,YAAY,CAAC,gLAAuB,CAAC,6CAA6C,EAAE,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAE7J,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,YAAY,uJAAC,0BAAuB,CAAC,gCAAgC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACpI,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,YAAY,uJAAC,0BAAuB,CAAC,qCAAqC,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACzI,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QACvC,KAAK,MAAM,WAAW,IAAI,MAAM,CAAE,CAAC;YAC/B,WAAW,CAAC,QAAQ,EAAE,CAAC;QAC3B,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,SAAS,CAAC,mBAAwB,EAAA;QACrC,gBAAgB;QAChB,mBAAmB,CAAC,YAAY,GAAG,EAAE,CAAC;QAEtC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QACvC,KAAK,MAAM,WAAW,IAAI,MAAM,CAAE,CAAC;YAC/B,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;gBACxB,mBAAmB,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,gBAAgB,CAAC,SAA0B,EAAA;QAC9C,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QACD,KAAK,MAAM,CAAC,IAAI,SAAS,CAAC,YAAY,CAAE,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,SAA0B,EAAE,OAAiB,EAAA;QACpE,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QACD,KAAK,MAAM,CAAC,IAAI,SAAS,CAAC,YAAY,CAAE,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,OAAO,EAAE,CAAC;gBACV,CAAC,CAAC,OAAO,EAAE,CAAC;YAChB,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QACvC,MAAO,MAAM,CAAC,MAAM,CAAE,CAAC;YACnB,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACxB,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,IAAkB,EAAE,0BAAmC,EAAA;QAC3E,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QACvC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;YAED,MAAM,YAAY,GAA8B,KAAM,CAAC,YAAY,CAAC;YACpE,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,YAAY,CAAC,YAAY,CAAC;YAE7D,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;gBACnC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,0BAA0B,CAAC,EAAE,CAAC;oBACtD,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;oBACvD,OAAO,KAAK,CAAC;gBACjB,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QACvD,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,kBAAkB,CAAC,MAAc,EAAA;QACrC,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QACvC,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7D,KAAK,MAAM,WAAW,IAAI,MAAM,CAAE,CAAC;gBAC/B,IACI,WAAW,CAAC,YAAY,EAAE,IAC1B,CAAC,CAAC,WAAW,CAAC,MAAM,IACf,WAAW,CAAC,MAAM,CAAC,aAAa,8JAAK,SAAM,CAAC,aAAa,IAAI,MAAM,KAAK,WAAW,CAAC,MAAM,CAAC,GAC3F,WAAW,CAAC,MAAM,CAAC,aAAa,6JAAK,UAAM,CAAC,aAAa,IAAI,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,AAAC,CAAC,EACzH,CAAC;oBACC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;oBAC3B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;oBAEnE,MAAM,YAAY,GAA8B,WAAY,CAAC,YAAY,CAAC;oBAC1E,IAAI,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;wBAC/B,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;wBAC/B,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;wBAClC,UAAU,GAAG,IAAI,CAAC;oBACtB,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;QACnC,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,WAAW,GAAA;QACf,gCAAgC;QAChC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC;IACL,CAAC;IAEO,eAAe,GAAA;QACnB,+BAA+B;QAC/B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAwB,EAAA;QAClC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAEnC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;YACvC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACrC,MAAM,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC9B,IAAI,WAAW,CAAC,gBAAgB,KAAK,gBAAgB,EAAE,CAAC;oBACpD,IAAI,WAAW,CAAC,YAAY,EAAE,EAAE,CAAC;wBAC7B,WAAW,CAAC,MAAM,EAAE,CAAC;oBACzB,CAAC;gBACL,CAAC;YACL,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;IACL,CAAC;IAEO,WAAW,GAAA;QACf,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;IACL,CAAC;IACO,mBAAmB,CAAC,KAAa,EAAA;QACrC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,0BAA0B,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAClE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACL,CAAC;CACJ;6JAED,cAAW,CAAC,6BAA6B,GAAG,CAAC,KAAY,EAAE,EAAE;IACzD,IAAI,SAAS,GAAG,KAAK,CAAC,aAAa,CAAC,gLAAuB,CAAC,gBAAgB,CAA8B,CAAC;IAC3G,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,SAAS,GAAG,IAAI,yBAAyB,CAAC,KAAK,CAAC,CAAC;QACjD,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;AACL,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2139, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/glowLayer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/glowLayer.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { serialize } from \"../Misc/decorators\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Scene } from \"../scene\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport type { PostProcess } from \"../PostProcesses/postProcess\";\r\nimport { BlurPostProcess } from \"../PostProcesses/blurPostProcess\";\r\nimport type { IThinGlowLayerOptions } from \"./thinGlowLayer\";\r\nimport { EffectLayer } from \"./effectLayer\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport type { Color4 } from \"core/Maths/math.color\";\r\n\r\nimport \"../Layers/effectLayerSceneComponent\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { GetExponentOfTwo } from \"../Misc/tools.functions\";\r\nimport { ThinGlowLayer } from \"./thinGlowLayer\";\r\nimport type { ThinBlurPostProcess } from \"core/PostProcesses/thinBlurPostProcess\";\r\n\r\ndeclare module \"../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /**\r\n         * Return the first glow layer of the scene with a given name.\r\n         * @param name The name of the glow layer to look for.\r\n         * @returns The glow layer if found otherwise null.\r\n         */\r\n        getGlowLayerByName(name: string): Nullable<GlowLayer>;\r\n    }\r\n}\r\n\r\nScene.prototype.getGlowLayerByName = function (name: string): Nullable<GlowLayer> {\r\n    for (let index = 0; index < this.effectLayers?.length; index++) {\r\n        if (this.effectLayers[index].name === name && this.effectLayers[index].getEffectName() === GlowLayer.EffectName) {\r\n            return (<any>this.effectLayers[index]) as GlowLayer;\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\n/**\r\n * Glow layer options. This helps customizing the behaviour\r\n * of the glow layer.\r\n */\r\nexport interface IGlowLayerOptions extends IThinGlowLayerOptions {\r\n    /**\r\n     * Enable MSAA by choosing the number of samples. Default: 1\r\n     */\r\n    mainTextureSamples?: number;\r\n\r\n    /**\r\n     * Whether or not to generate a stencil buffer. Default: false\r\n     */\r\n    generateStencilBuffer?: boolean;\r\n}\r\n\r\n/**\r\n * The glow layer Helps adding a glow effect around the emissive parts of a mesh.\r\n *\r\n * Once instantiated in a scene, by default, all the emissive meshes will glow.\r\n *\r\n * Documentation: https://doc.babylonjs.com/features/featuresDeepDive/mesh/glowLayer\r\n */\r\nexport class GlowLayer extends EffectLayer {\r\n    /**\r\n     * Effect Name of the layer.\r\n     */\r\n    public static get EffectName() {\r\n        return ThinGlowLayer.EffectName;\r\n    }\r\n\r\n    /**\r\n     * The default blur kernel size used for the glow.\r\n     */\r\n    public static DefaultBlurKernelSize = 32;\r\n\r\n    /**\r\n     * The default texture size ratio used for the glow.\r\n     */\r\n    public static DefaultTextureRatio = 0.5;\r\n\r\n    /**\r\n     * Sets the kernel size of the blur.\r\n     */\r\n    public set blurKernelSize(value: number) {\r\n        this._thinEffectLayer.blurKernelSize = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the kernel size of the blur.\r\n     */\r\n    @serialize()\r\n    public get blurKernelSize(): number {\r\n        return this._thinEffectLayer.blurKernelSize;\r\n    }\r\n\r\n    /**\r\n     * Sets the glow intensity.\r\n     */\r\n    public set intensity(value: number) {\r\n        this._thinEffectLayer.intensity = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the glow intensity.\r\n     */\r\n    @serialize()\r\n    public get intensity(): number {\r\n        return this._thinEffectLayer.intensity;\r\n    }\r\n\r\n    @serialize(\"options\")\r\n    protected _options: IGlowLayerOptions;\r\n\r\n    protected override readonly _thinEffectLayer: ThinGlowLayer;\r\n    private _horizontalBlurPostprocess1: BlurPostProcess;\r\n    private _verticalBlurPostprocess1: BlurPostProcess;\r\n    private _horizontalBlurPostprocess2: BlurPostProcess;\r\n    private _verticalBlurPostprocess2: BlurPostProcess;\r\n    private _blurTexture1: RenderTargetTexture;\r\n    private _blurTexture2: RenderTargetTexture;\r\n    private _postProcesses1: PostProcess[];\r\n    private _postProcesses2: PostProcess[];\r\n    /**\r\n     * Callback used to let the user override the color selection on a per mesh basis\r\n     */\r\n    public get customEmissiveColorSelector(): (mesh: Mesh, subMesh: SubMesh, material: Material, result: Color4) => void {\r\n        return this._thinEffectLayer.customEmissiveColorSelector;\r\n    }\r\n\r\n    public set customEmissiveColorSelector(value: (mesh: Mesh, subMesh: SubMesh, material: Material, result: Color4) => void) {\r\n        this._thinEffectLayer.customEmissiveColorSelector = value;\r\n    }\r\n\r\n    /**\r\n     * Callback used to let the user override the texture selection on a per mesh basis\r\n     */\r\n    public get customEmissiveTextureSelector(): (mesh: Mesh, subMesh: SubMesh, material: Material) => Texture {\r\n        return this._thinEffectLayer.customEmissiveTextureSelector;\r\n    }\r\n\r\n    public set customEmissiveTextureSelector(value: (mesh: Mesh, subMesh: SubMesh, material: Material) => Texture) {\r\n        this._thinEffectLayer.customEmissiveTextureSelector = value;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new glow Layer and references it to the scene.\r\n     * @param name The name of the layer\r\n     * @param scene The scene to use the layer in\r\n     * @param options Sets of none mandatory options to use with the layer (see IGlowLayerOptions for more information)\r\n     */\r\n    constructor(name: string, scene?: Scene, options?: Partial<IGlowLayerOptions>) {\r\n        super(name, scene, false, new ThinGlowLayer(name, scene, options));\r\n\r\n        // Adapt options\r\n        this._options = {\r\n            mainTextureRatio: GlowLayer.DefaultTextureRatio,\r\n            blurKernelSize: 32,\r\n            mainTextureFixedSize: undefined,\r\n            camera: null,\r\n            mainTextureSamples: 1,\r\n            renderingGroupId: -1,\r\n            ldrMerge: false,\r\n            alphaBlendingMode: Constants.ALPHA_ADD,\r\n            mainTextureType: Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n            generateStencilBuffer: false,\r\n            ...options,\r\n        };\r\n\r\n        // Initialize the layer\r\n        this._init(this._options);\r\n    }\r\n\r\n    /**\r\n     * Get the effect name of the layer.\r\n     * @returns The effect name\r\n     */\r\n    public getEffectName(): string {\r\n        return GlowLayer.EffectName;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     * Create the merge effect. This is the shader use to blit the information back\r\n     * to the main canvas at the end of the scene rendering.\r\n     */\r\n    protected _createMergeEffect(): Effect {\r\n        return this._thinEffectLayer._createMergeEffect();\r\n    }\r\n\r\n    /**\r\n     * Creates the render target textures and post processes used in the glow layer.\r\n     */\r\n    protected _createTextureAndPostProcesses(): void {\r\n        this._thinEffectLayer._renderPassId = this._mainTexture.renderPassId;\r\n\r\n        let blurTextureWidth = this._mainTextureDesiredSize.width;\r\n        let blurTextureHeight = this._mainTextureDesiredSize.height;\r\n        blurTextureWidth = this._engine.needPOTTextures ? GetExponentOfTwo(blurTextureWidth, this._maxSize) : blurTextureWidth;\r\n        blurTextureHeight = this._engine.needPOTTextures ? GetExponentOfTwo(blurTextureHeight, this._maxSize) : blurTextureHeight;\r\n\r\n        let textureType = 0;\r\n        if (this._engine.getCaps().textureHalfFloatRender) {\r\n            textureType = Constants.TEXTURETYPE_HALF_FLOAT;\r\n        } else {\r\n            textureType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n        }\r\n\r\n        this._blurTexture1 = new RenderTargetTexture(\r\n            \"GlowLayerBlurRTT\",\r\n            {\r\n                width: blurTextureWidth,\r\n                height: blurTextureHeight,\r\n            },\r\n            this._scene,\r\n            false,\r\n            true,\r\n            textureType\r\n        );\r\n        this._blurTexture1.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this._blurTexture1.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        this._blurTexture1.updateSamplingMode(Texture.BILINEAR_SAMPLINGMODE);\r\n        this._blurTexture1.renderParticles = false;\r\n        this._blurTexture1.ignoreCameraViewport = true;\r\n\r\n        const blurTextureWidth2 = Math.floor(blurTextureWidth / 2);\r\n        const blurTextureHeight2 = Math.floor(blurTextureHeight / 2);\r\n\r\n        this._blurTexture2 = new RenderTargetTexture(\r\n            \"GlowLayerBlurRTT2\",\r\n            {\r\n                width: blurTextureWidth2,\r\n                height: blurTextureHeight2,\r\n            },\r\n            this._scene,\r\n            false,\r\n            true,\r\n            textureType\r\n        );\r\n        this._blurTexture2.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this._blurTexture2.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        this._blurTexture2.updateSamplingMode(Texture.BILINEAR_SAMPLINGMODE);\r\n        this._blurTexture2.renderParticles = false;\r\n        this._blurTexture2.ignoreCameraViewport = true;\r\n\r\n        this._textures = [this._blurTexture1, this._blurTexture2];\r\n\r\n        this._thinEffectLayer.bindTexturesForCompose = (effect: Effect) => {\r\n            effect.setTexture(\"textureSampler\", this._blurTexture1);\r\n            effect.setTexture(\"textureSampler2\", this._blurTexture2);\r\n            effect.setFloat(\"offset\", this.intensity);\r\n        };\r\n\r\n        this._thinEffectLayer._createTextureAndPostProcesses();\r\n\r\n        const thinBlurPostProcesses1 = this._thinEffectLayer._postProcesses[0] as ThinBlurPostProcess;\r\n        this._horizontalBlurPostprocess1 = new BlurPostProcess(\"GlowLayerHBP1\", thinBlurPostProcesses1.direction, thinBlurPostProcesses1.kernel, {\r\n            samplingMode: Texture.BILINEAR_SAMPLINGMODE,\r\n            engine: this._scene.getEngine(),\r\n            width: blurTextureWidth,\r\n            height: blurTextureHeight,\r\n            textureType,\r\n            effectWrapper: thinBlurPostProcesses1,\r\n        });\r\n        this._horizontalBlurPostprocess1.width = blurTextureWidth;\r\n        this._horizontalBlurPostprocess1.height = blurTextureHeight;\r\n        this._horizontalBlurPostprocess1.externalTextureSamplerBinding = true;\r\n        this._horizontalBlurPostprocess1.onApplyObservable.add((effect) => {\r\n            effect.setTexture(\"textureSampler\", this._mainTexture);\r\n        });\r\n\r\n        const thinBlurPostProcesses2 = this._thinEffectLayer._postProcesses[1] as ThinBlurPostProcess;\r\n        this._verticalBlurPostprocess1 = new BlurPostProcess(\"GlowLayerVBP1\", thinBlurPostProcesses2.direction, thinBlurPostProcesses2.kernel, {\r\n            samplingMode: Texture.BILINEAR_SAMPLINGMODE,\r\n            engine: this._scene.getEngine(),\r\n            width: blurTextureWidth,\r\n            height: blurTextureHeight,\r\n            textureType,\r\n            effectWrapper: thinBlurPostProcesses2,\r\n        });\r\n\r\n        const thinBlurPostProcesses3 = this._thinEffectLayer._postProcesses[2] as ThinBlurPostProcess;\r\n        this._horizontalBlurPostprocess2 = new BlurPostProcess(\"GlowLayerHBP2\", thinBlurPostProcesses3.direction, thinBlurPostProcesses3.kernel, {\r\n            samplingMode: Texture.BILINEAR_SAMPLINGMODE,\r\n            engine: this._scene.getEngine(),\r\n            width: blurTextureWidth2,\r\n            height: blurTextureHeight2,\r\n            textureType,\r\n            effectWrapper: thinBlurPostProcesses3,\r\n        });\r\n        this._horizontalBlurPostprocess2.width = blurTextureWidth2;\r\n        this._horizontalBlurPostprocess2.height = blurTextureHeight2;\r\n        this._horizontalBlurPostprocess2.externalTextureSamplerBinding = true;\r\n        this._horizontalBlurPostprocess2.onApplyObservable.add((effect) => {\r\n            effect.setTexture(\"textureSampler\", this._blurTexture1);\r\n        });\r\n\r\n        const thinBlurPostProcesses4 = this._thinEffectLayer._postProcesses[3] as ThinBlurPostProcess;\r\n        this._verticalBlurPostprocess2 = new BlurPostProcess(\"GlowLayerVBP2\", thinBlurPostProcesses4.direction, thinBlurPostProcesses4.kernel, {\r\n            samplingMode: Texture.BILINEAR_SAMPLINGMODE,\r\n            engine: this._scene.getEngine(),\r\n            width: blurTextureWidth2,\r\n            height: blurTextureHeight2,\r\n            textureType,\r\n            effectWrapper: thinBlurPostProcesses4,\r\n        });\r\n\r\n        this._postProcesses = [this._horizontalBlurPostprocess1, this._verticalBlurPostprocess1, this._horizontalBlurPostprocess2, this._verticalBlurPostprocess2];\r\n        this._postProcesses1 = [this._horizontalBlurPostprocess1, this._verticalBlurPostprocess1];\r\n        this._postProcesses2 = [this._horizontalBlurPostprocess2, this._verticalBlurPostprocess2];\r\n\r\n        this._mainTexture.samples = this._options.mainTextureSamples!;\r\n        this._mainTexture.onAfterUnbindObservable.add(() => {\r\n            const internalTexture = this._blurTexture1.renderTarget;\r\n            if (internalTexture) {\r\n                this._scene.postProcessManager.directRender(this._postProcesses1, internalTexture, true);\r\n\r\n                const internalTexture2 = this._blurTexture2.renderTarget;\r\n                if (internalTexture2) {\r\n                    this._scene.postProcessManager.directRender(this._postProcesses2, internalTexture2, true);\r\n                }\r\n                this._engine.unBindFramebuffer(internalTexture2 ?? internalTexture, true);\r\n            }\r\n        });\r\n\r\n        // Prevent autoClear.\r\n        this._postProcesses.map((pp) => {\r\n            pp.autoClear = false;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Checks for the readiness of the element composing the layer.\r\n     * @param subMesh the mesh to check for\r\n     * @param useInstances specify whether or not to use instances to render the mesh\r\n     * @returns true if ready otherwise, false\r\n     */\r\n    public isReady(subMesh: SubMesh, useInstances: boolean): boolean {\r\n        return this._thinEffectLayer.isReady(subMesh, useInstances);\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not the layer needs stencil enabled during the mesh rendering.\r\n     */\r\n    public needStencil(): boolean {\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Returns true if the mesh can be rendered, otherwise false.\r\n     * @param mesh The mesh to render\r\n     * @param material The material used on the mesh\r\n     * @returns true if it can be rendered otherwise false\r\n     */\r\n    protected override _canRenderMesh(mesh: AbstractMesh, material: Material): boolean {\r\n        return this._thinEffectLayer._canRenderMesh(mesh, material);\r\n    }\r\n\r\n    /**\r\n     * Implementation specific of rendering the generating effect on the main canvas.\r\n     * @param effect The effect used to render through\r\n     */\r\n    protected _internalRender(effect: Effect): void {\r\n        this._thinEffectLayer._internalCompose(effect);\r\n    }\r\n\r\n    /**\r\n     * Sets the required values for both the emissive texture and and the main color.\r\n     * @param mesh\r\n     * @param subMesh\r\n     * @param material\r\n     */\r\n    protected _setEmissiveTextureAndColor(mesh: Mesh, subMesh: SubMesh, material: Material): void {\r\n        this._thinEffectLayer._setEmissiveTextureAndColor(mesh, subMesh, material);\r\n    }\r\n\r\n    /**\r\n     * Returns true if the mesh should render, otherwise false.\r\n     * @param mesh The mesh to render\r\n     * @returns true if it should render otherwise false\r\n     */\r\n    protected override _shouldRenderMesh(mesh: Mesh): boolean {\r\n        return this._thinEffectLayer._shouldRenderMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Adds specific effects defines.\r\n     * @param defines The defines to add specifics to.\r\n     */\r\n    protected override _addCustomEffectDefines(defines: string[]): void {\r\n        this._thinEffectLayer._addCustomEffectDefines(defines);\r\n    }\r\n\r\n    /**\r\n     * Add a mesh in the exclusion list to prevent it to impact or being impacted by the glow layer.\r\n     * @param mesh The mesh to exclude from the glow layer\r\n     */\r\n    public addExcludedMesh(mesh: Mesh): void {\r\n        this._thinEffectLayer.addExcludedMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the exclusion list to let it impact or being impacted by the glow layer.\r\n     * @param mesh The mesh to remove\r\n     */\r\n    public removeExcludedMesh(mesh: Mesh): void {\r\n        this._thinEffectLayer.removeExcludedMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Add a mesh in the inclusion list to impact or being impacted by the glow layer.\r\n     * @param mesh The mesh to include in the glow layer\r\n     */\r\n    public addIncludedOnlyMesh(mesh: Mesh): void {\r\n        this._thinEffectLayer.addIncludedOnlyMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the Inclusion list to prevent it to impact or being impacted by the glow layer.\r\n     * @param mesh The mesh to remove\r\n     */\r\n    public removeIncludedOnlyMesh(mesh: Mesh): void {\r\n        this._thinEffectLayer.removeIncludedOnlyMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Determine if a given mesh will be used in the glow layer\r\n     * @param mesh The mesh to test\r\n     * @returns true if the mesh will be highlighted by the current glow layer\r\n     */\r\n    public override hasMesh(mesh: AbstractMesh): boolean {\r\n        return this._thinEffectLayer.hasMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Defines whether the current material of the mesh should be use to render the effect.\r\n     * @param mesh defines the current mesh to render\r\n     * @returns true if the material of the mesh should be use to render the effect\r\n     */\r\n    protected override _useMeshMaterial(mesh: AbstractMesh): boolean {\r\n        return this._thinEffectLayer._useMeshMaterial(mesh);\r\n    }\r\n\r\n    /**\r\n     * Add a mesh to be rendered through its own material and not with emissive only.\r\n     * @param mesh The mesh for which we need to use its material\r\n     */\r\n    public referenceMeshToUseItsOwnMaterial(mesh: AbstractMesh): void {\r\n        this._thinEffectLayer.referenceMeshToUseItsOwnMaterial(mesh);\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from being rendered through its own material and not with emissive only.\r\n     * @param mesh The mesh for which we need to not use its material\r\n     */\r\n    public unReferenceMeshFromUsingItsOwnMaterial(mesh: AbstractMesh): void {\r\n        this._thinEffectLayer.unReferenceMeshFromUsingItsOwnMaterial(mesh, this._mainTexture.renderPassId);\r\n    }\r\n\r\n    /**\r\n     * Free any resources and references associated to a mesh.\r\n     * Internal use\r\n     * @param mesh The mesh to free.\r\n     * @internal\r\n     */\r\n    public _disposeMesh(mesh: Mesh): void {\r\n        this._thinEffectLayer._disposeMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the effect layer\r\n     * @returns the string with the class name of the effect layer\r\n     */\r\n    public override getClassName(): string {\r\n        return \"GlowLayer\";\r\n    }\r\n\r\n    /**\r\n     * Serializes this glow layer\r\n     * @returns a serialized glow layer object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"BABYLON.GlowLayer\";\r\n\r\n        let index;\r\n\r\n        // Included meshes\r\n        serializationObject.includedMeshes = [];\r\n\r\n        const includedOnlyMeshes = this._thinEffectLayer._includedOnlyMeshes;\r\n        if (includedOnlyMeshes.length) {\r\n            for (index = 0; index < includedOnlyMeshes.length; index++) {\r\n                const mesh = this._scene.getMeshByUniqueId(includedOnlyMeshes[index]);\r\n                if (mesh) {\r\n                    serializationObject.includedMeshes.push(mesh.id);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Excluded meshes\r\n        serializationObject.excludedMeshes = [];\r\n\r\n        const excludedMeshes = this._thinEffectLayer._excludedMeshes;\r\n        if (excludedMeshes.length) {\r\n            for (index = 0; index < excludedMeshes.length; index++) {\r\n                const mesh = this._scene.getMeshByUniqueId(excludedMeshes[index]);\r\n                if (mesh) {\r\n                    serializationObject.excludedMeshes.push(mesh.id);\r\n                }\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Creates a Glow Layer from parsed glow layer data\r\n     * @param parsedGlowLayer defines glow layer data\r\n     * @param scene defines the current scene\r\n     * @param rootUrl defines the root URL containing the glow layer information\r\n     * @returns a parsed Glow Layer\r\n     */\r\n    public static override Parse(parsedGlowLayer: any, scene: Scene, rootUrl: string): GlowLayer {\r\n        const gl = SerializationHelper.Parse(() => new GlowLayer(parsedGlowLayer.name, scene, parsedGlowLayer.options), parsedGlowLayer, scene, rootUrl);\r\n        let index;\r\n\r\n        // Excluded meshes\r\n        for (index = 0; index < parsedGlowLayer.excludedMeshes.length; index++) {\r\n            const mesh = scene.getMeshById(parsedGlowLayer.excludedMeshes[index]);\r\n            if (mesh) {\r\n                gl.addExcludedMesh(<Mesh>mesh);\r\n            }\r\n        }\r\n\r\n        // Included meshes\r\n        for (index = 0; index < parsedGlowLayer.includedMeshes.length; index++) {\r\n            const mesh = scene.getMeshById(parsedGlowLayer.includedMeshes[index]);\r\n            if (mesh) {\r\n                gl.addIncludedOnlyMesh(<Mesh>mesh);\r\n            }\r\n        }\r\n\r\n        return gl;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.GlowLayer\", GlowLayer);\r\n"], "names": [], "mappings": ";;;;AAAA,oDAAA,EAAsD,CACtD,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAE/C,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAIjC,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAIhF,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AAEnE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAGlD,OAAO,qCAAqC,CAAC;AAC7C,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;;;;;;;;;;;;;6IAehD,QAAK,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAU,IAAY;IACvD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;QAC7D,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;YAC9G,OAAa,IAAI,CAAC,YAAY,CAAC,KAAK,CAAe,CAAC;QACxD,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAyBI,MAAO,SAAU,SAAQ,2KAAW;IACtC;;OAEG,CACI,MAAM,KAAK,UAAU,GAAA;QACxB,sKAAO,gBAAa,CAAC,UAAU,CAAC;IACpC,CAAC;IAYD;;OAEG,CACH,IAAW,cAAc,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,gBAAgB,CAAC,cAAc,GAAG,KAAK,CAAC;IACjD,CAAC;IAED;;OAEG,CAEH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;IAChD,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,CAAC,KAAa,EAAA;QAC9B,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG,CAEH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IAC3C,CAAC;IAcD;;OAEG,CACH,IAAW,2BAA2B,GAAA;QAClC,OAAO,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC;IAC7D,CAAC;IAED,IAAW,2BAA2B,CAAC,KAAiF,EAAA;QACpH,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,GAAG,KAAK,CAAC;IAC9D,CAAC;IAED;;OAEG,CACH,IAAW,6BAA6B,GAAA;QACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,6BAA6B,CAAC;IAC/D,CAAC;IAED,IAAW,6BAA6B,CAAC,KAAoE,EAAA;QACzG,IAAI,CAAC,gBAAgB,CAAC,6BAA6B,GAAG,KAAK,CAAC;IAChE,CAAC;IAED;;;;;OAKG,CACH,YAAY,IAAY,EAAE,KAAa,EAAE,OAAoC,CAAA;QACzE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,mKAAI,gBAAa,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;QAEnE,gBAAgB;QAChB,IAAI,CAAC,QAAQ,GAAG;YACZ,gBAAgB,EAAE,SAAS,CAAC,mBAAmB;YAC/C,cAAc,EAAE,EAAE;YAClB,oBAAoB,EAAE,SAAS;YAC/B,MAAM,EAAE,IAAI;YACZ,kBAAkB,EAAE,CAAC;YACrB,gBAAgB,EAAE,CAAC,CAAC;YACpB,QAAQ,EAAE,KAAK;YACf,iBAAiB,EAAE,SAAS,CAAC,SAAS;YACtC,eAAe,EAAE,SAAS,CAAC,yBAAyB;YACpD,qBAAqB,EAAE,KAAK;YAC5B,GAAG,OAAO;SACb,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,SAAS,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;;;OAIG,CACO,kBAAkB,GAAA;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG,CACO,8BAA8B,GAAA;QACpC,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;QAErE,IAAI,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;QAC1D,IAAI,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;QAC5D,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,uKAAC,mBAAA,AAAgB,EAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACvH,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,sKAAC,oBAAgB,AAAhB,EAAiB,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC;QAE1H,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,sBAAsB,EAAE,CAAC;YAChD,WAAW,GAAG,SAAS,CAAC,sBAAsB,CAAC;QACnD,CAAC,MAAM,CAAC;YACJ,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,wLAAI,sBAAmB,CACxC,kBAAkB,EAClB;YACI,KAAK,EAAE,gBAAgB;YACvB,MAAM,EAAE,iBAAiB;SAC5B,EACD,IAAI,CAAC,MAAM,EACX,KAAK,EACL,IAAI,EACJ,WAAW,CACd,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,kBAAkB,yKAAC,UAAO,CAAC,qBAAqB,CAAC,CAAC;QACrE,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAE/C,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;QAC3D,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAE7D,IAAI,CAAC,aAAa,GAAG,wLAAI,sBAAmB,CACxC,mBAAmB,EACnB;YACI,KAAK,EAAE,iBAAiB;YACxB,MAAM,EAAE,kBAAkB;SAC7B,EACD,IAAI,CAAC,MAAM,EACX,KAAK,EACL,IAAI,EACJ,WAAW,CACd,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,kLAAO,CAAC,iBAAiB,CAAC;QACrD,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,kLAAO,CAAC,qBAAqB,CAAC,CAAC;QACrE,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,KAAK,CAAC;QAC3C,IAAI,CAAC,aAAa,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAE/C,IAAI,CAAC,SAAS,GAAG;YAAC,IAAI,CAAC,aAAa;YAAE,IAAI,CAAC,aAAa;SAAC,CAAC;QAE1D,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,GAAG,CAAC,MAAc,EAAE,EAAE;YAC9D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACxD,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,8BAA8B,EAAE,CAAC;QAEvD,MAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAwB,CAAC;QAC9F,IAAI,CAAC,2BAA2B,GAAG,4KAAI,kBAAe,CAAC,eAAe,EAAE,sBAAsB,CAAC,SAAS,EAAE,sBAAsB,CAAC,MAAM,EAAE;YACrI,YAAY,0KAAE,UAAO,CAAC,qBAAqB;YAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YAC/B,KAAK,EAAE,gBAAgB;YACvB,MAAM,EAAE,iBAAiB;YACzB,WAAW;YACX,aAAa,EAAE,sBAAsB;SACxC,CAAC,CAAC;QACH,IAAI,CAAC,2BAA2B,CAAC,KAAK,GAAG,gBAAgB,CAAC;QAC1D,IAAI,CAAC,2BAA2B,CAAC,MAAM,GAAG,iBAAiB,CAAC;QAC5D,IAAI,CAAC,2BAA2B,CAAC,6BAA6B,GAAG,IAAI,CAAC;QACtE,IAAI,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC9D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QAEH,MAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAwB,CAAC;QAC9F,IAAI,CAAC,yBAAyB,GAAG,4KAAI,kBAAe,CAAC,eAAe,EAAE,sBAAsB,CAAC,SAAS,EAAE,sBAAsB,CAAC,MAAM,EAAE;YACnI,YAAY,yKAAE,WAAO,CAAC,qBAAqB;YAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YAC/B,KAAK,EAAE,gBAAgB;YACvB,MAAM,EAAE,iBAAiB;YACzB,WAAW;YACX,aAAa,EAAE,sBAAsB;SACxC,CAAC,CAAC;QAEH,MAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAwB,CAAC;QAC9F,IAAI,CAAC,2BAA2B,GAAG,4KAAI,kBAAe,CAAC,eAAe,EAAE,sBAAsB,CAAC,SAAS,EAAE,sBAAsB,CAAC,MAAM,EAAE;YACrI,YAAY,0KAAE,UAAO,CAAC,qBAAqB;YAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YAC/B,KAAK,EAAE,iBAAiB;YACxB,MAAM,EAAE,kBAAkB;YAC1B,WAAW;YACX,aAAa,EAAE,sBAAsB;SACxC,CAAC,CAAC;QACH,IAAI,CAAC,2BAA2B,CAAC,KAAK,GAAG,iBAAiB,CAAC;QAC3D,IAAI,CAAC,2BAA2B,CAAC,MAAM,GAAG,kBAAkB,CAAC;QAC7D,IAAI,CAAC,2BAA2B,CAAC,6BAA6B,GAAG,IAAI,CAAC;QACtE,IAAI,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC9D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,MAAM,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAwB,CAAC;QAC9F,IAAI,CAAC,yBAAyB,GAAG,4KAAI,kBAAe,CAAC,eAAe,EAAE,sBAAsB,CAAC,SAAS,EAAE,sBAAsB,CAAC,MAAM,EAAE;YACnI,YAAY,yKAAE,WAAO,CAAC,qBAAqB;YAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YAC/B,KAAK,EAAE,iBAAiB;YACxB,MAAM,EAAE,kBAAkB;YAC1B,WAAW;YACX,aAAa,EAAE,sBAAsB;SACxC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG;YAAC,IAAI,CAAC,2BAA2B;YAAE,IAAI,CAAC,yBAAyB;YAAE,IAAI,CAAC,2BAA2B;YAAE,IAAI,CAAC,yBAAyB;SAAC,CAAC;QAC3J,IAAI,CAAC,eAAe,GAAG;YAAC,IAAI,CAAC,2BAA2B;YAAE,IAAI,CAAC,yBAAyB;SAAC,CAAC;QAC1F,IAAI,CAAC,eAAe,GAAG;YAAC,IAAI,CAAC,2BAA2B;YAAE,IAAI,CAAC,yBAAyB;SAAC,CAAC;QAE1F,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAmB,CAAC;QAC9D,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/C,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;YACxD,IAAI,eAAe,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;gBAEzF,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;gBACzD,IAAI,gBAAgB,EAAE,CAAC;oBACnB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;gBAC9F,CAAC;gBACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,eAAe,EAAE,IAAI,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;YAC3B,EAAE,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG,CACI,OAAO,CAAC,OAAgB,EAAE,YAAqB,EAAA;QAClD,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG,CACI,WAAW,GAAA;QACd,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG,CACgB,cAAc,CAAC,IAAkB,EAAE,QAAkB,EAAA;QACpE,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAChE,CAAC;IAED;;;OAGG,CACO,eAAe,CAAC,MAAc,EAAA;QACpC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IACnD,CAAC;IAED;;;;;OAKG,CACO,2BAA2B,CAAC,IAAU,EAAE,OAAgB,EAAE,QAAkB,EAAA;QAClF,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC/E,CAAC;IAED;;;;OAIG,CACgB,iBAAiB,CAAC,IAAU,EAAA;QAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;;OAGG,CACgB,uBAAuB,CAAC,OAAiB,EAAA;QACxD,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED;;;OAGG,CACI,eAAe,CAAC,IAAU,EAAA;QAC7B,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED;;;OAGG,CACI,kBAAkB,CAAC,IAAU,EAAA;QAChC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG,CACI,mBAAmB,CAAC,IAAU,EAAA;QACjC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;;OAGG,CACI,sBAAsB,CAAC,IAAU,EAAA;QACpC,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;;;OAIG,CACa,OAAO,CAAC,IAAkB,EAAA;QACtC,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;;;OAIG,CACgB,gBAAgB,CAAC,IAAkB,EAAA;QAClD,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;;OAGG,CACI,gCAAgC,CAAC,IAAkB,EAAA;QACtD,IAAI,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;IAED;;;OAGG,CACI,sCAAsC,CAAC,IAAkB,EAAA;QAC5D,IAAI,CAAC,gBAAgB,CAAC,sCAAsC,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IACvG,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,IAAU,EAAA;QAC1B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,8KAAG,sBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,mBAAmB,CAAC;QAErD,IAAI,KAAK,CAAC;QAEV,kBAAkB;QAClB,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QAExC,MAAM,kBAAkB,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;QACrE,IAAI,kBAAkB,CAAC,MAAM,EAAE,CAAC;YAC5B,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACzD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtE,IAAI,IAAI,EAAE,CAAC;oBACP,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrD,CAAC;YACL,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QAExC,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;QAC7D,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YACxB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACrD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;gBAClE,IAAI,IAAI,EAAE,CAAC;oBACP,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrD,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAU,KAAK,CAAC,eAAoB,EAAE,KAAY,EAAE,OAAe,EAAA;QAC5E,MAAM,EAAE,8KAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,eAAe,CAAC,OAAO,CAAC,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACjJ,IAAI,KAAK,CAAC;QAEV,kBAAkB;QAClB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACrE,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,IAAI,EAAE,CAAC;gBACP,EAAE,CAAC,eAAe,CAAO,IAAI,CAAC,CAAC;YACnC,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACrE,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;YACtE,IAAI,IAAI,EAAE,CAAC;gBACP,EAAE,CAAC,mBAAmB,CAAO,IAAI,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;;AAzdD;;GAEG,CACW,UAAA,qBAAqB,GAAG,EAAE,CAAC;AAEzC;;GAEG,CACW,UAAA,mBAAmB,GAAG,GAAG,CAAC;AAaxC,qKAAA,EAAA;kKADC,YAAA,AAAS,EAAE;+CAGX;wJAaD,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;0CAGX;CAGS,oKAAA,EAAA;kKADT,YAAA,AAAS,EAAC,SAAS,CAAC;2CACiB;6JAmb1C,gBAAA,AAAa,EAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2565, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/highlightLayer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/highlightLayer.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { serialize } from \"../Misc/decorators\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { Scene } from \"../scene\";\r\nimport { Vector2 } from \"../Maths/math.vector\";\r\nimport type { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport type { SubMesh } from \"../Meshes/subMesh\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { Effect } from \"../Materials/effect\";\r\nimport type { Material } from \"../Materials/material\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { PostProcessOptions } from \"../PostProcesses/postProcess\";\r\nimport { PostProcess } from \"../PostProcesses/postProcess\";\r\nimport { PassPostProcess } from \"../PostProcesses/passPostProcess\";\r\nimport { BlurPostProcess } from \"../PostProcesses/blurPostProcess\";\r\nimport { EffectLayer } from \"./effectLayer\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport type { Color4 } from \"../Maths/math.color\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\n\r\nimport type { ThinPassPostProcess } from \"core/PostProcesses/thinPassPostProcess\";\r\nimport type { ThinBlurPostProcess } from \"core/PostProcesses/thinBlurPostProcess\";\r\nimport type { IThinHighlightLayerOptions } from \"./thinHighlightLayer\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\nimport { GetExponentOfTwo } from \"../Misc/tools.functions\";\r\nimport { ThinHighlightLayer } from \"./thinHighlightLayer\";\r\nimport { ThinGlowBlurPostProcess } from \"./thinEffectLayer\";\r\n\r\ndeclare module \"../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /**\r\n         * Return a the first highlight layer of the scene with a given name.\r\n         * @param name The name of the highlight layer to look for.\r\n         * @returns The highlight layer if found otherwise null.\r\n         */\r\n        getHighlightLayerByName(name: string): Nullable<HighlightLayer>;\r\n    }\r\n}\r\n\r\nScene.prototype.getHighlightLayerByName = function (name: string): Nullable<HighlightLayer> {\r\n    for (let index = 0; index < this.effectLayers?.length; index++) {\r\n        if (this.effectLayers[index].name === name && this.effectLayers[index].getEffectName() === HighlightLayer.EffectName) {\r\n            return (<any>this.effectLayers[index]) as HighlightLayer;\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\ninterface IBlurPostProcess extends PostProcess {\r\n    kernel: number;\r\n}\r\n\r\n/**\r\n * Special Glow Blur post process only blurring the alpha channel\r\n * It enforces keeping the most luminous color in the color channel.\r\n */\r\nclass GlowBlurPostProcess extends PostProcess {\r\n    protected override _effectWrapper: ThinGlowBlurPostProcess;\r\n\r\n    constructor(\r\n        name: string,\r\n        public direction: Vector2,\r\n        public kernel: number,\r\n        options: number | PostProcessOptions,\r\n        camera: Nullable<Camera> = null,\r\n        samplingMode: number = Texture.BILINEAR_SAMPLINGMODE,\r\n        engine?: AbstractEngine,\r\n        reusable?: boolean\r\n    ) {\r\n        const localOptions = {\r\n            uniforms: ThinGlowBlurPostProcess.Uniforms,\r\n            size: typeof options === \"number\" ? options : undefined,\r\n            camera,\r\n            samplingMode,\r\n            engine,\r\n            reusable,\r\n            ...(options as PostProcessOptions),\r\n        };\r\n\r\n        super(name, ThinGlowBlurPostProcess.FragmentUrl, {\r\n            effectWrapper: typeof options === \"number\" || !options.effectWrapper ? new ThinGlowBlurPostProcess(name, engine, direction, kernel, localOptions) : undefined,\r\n            ...localOptions,\r\n        });\r\n\r\n        this.onApplyObservable.add(() => {\r\n            this._effectWrapper.textureWidth = this.width;\r\n            this._effectWrapper.textureHeight = this.height;\r\n        });\r\n    }\r\n\r\n    protected override _gatherImports(useWebGPU: boolean, list: Promise<any>[]) {\r\n        if (useWebGPU) {\r\n            this._webGPUReady = true;\r\n            list.push(import(\"../ShadersWGSL/glowBlurPostProcess.fragment\"));\r\n        } else {\r\n            list.push(import(\"../Shaders/glowBlurPostProcess.fragment\"));\r\n        }\r\n\r\n        super._gatherImports(useWebGPU, list);\r\n    }\r\n}\r\n\r\n/**\r\n * Highlight layer options. This helps customizing the behaviour\r\n * of the highlight layer.\r\n */\r\nexport interface IHighlightLayerOptions extends IThinHighlightLayerOptions {}\r\n\r\n/**\r\n * The highlight layer Helps adding a glow effect around a mesh.\r\n *\r\n * Once instantiated in a scene, simply use the addMesh or removeMesh method to add or remove\r\n * glowy meshes to your scene.\r\n *\r\n * !!! THIS REQUIRES AN ACTIVE STENCIL BUFFER ON THE CANVAS !!!\r\n */\r\nexport class HighlightLayer extends EffectLayer {\r\n    /**\r\n     * Effect Name of the highlight layer.\r\n     */\r\n    public static readonly EffectName = \"HighlightLayer\";\r\n\r\n    /**\r\n     * The neutral color used during the preparation of the glow effect.\r\n     * This is black by default as the blend operation is a blend operation.\r\n     */\r\n    public static get NeutralColor() {\r\n        return ThinHighlightLayer.NeutralColor;\r\n    }\r\n\r\n    public static set NeutralColor(value: Color4) {\r\n        ThinHighlightLayer.NeutralColor = value;\r\n    }\r\n\r\n    /**\r\n     * Specifies whether or not the inner glow is ACTIVE in the layer.\r\n     */\r\n    @serialize()\r\n    public get innerGlow() {\r\n        return this._thinEffectLayer.innerGlow;\r\n    }\r\n\r\n    public set innerGlow(value: boolean) {\r\n        this._thinEffectLayer.innerGlow = value;\r\n    }\r\n\r\n    /**\r\n     * Specifies whether or not the outer glow is ACTIVE in the layer.\r\n     */\r\n    @serialize()\r\n    public get outerGlow() {\r\n        return this._thinEffectLayer.outerGlow;\r\n    }\r\n\r\n    public set outerGlow(value: boolean) {\r\n        this._thinEffectLayer.outerGlow = value;\r\n    }\r\n\r\n    /**\r\n     * Specifies the horizontal size of the blur.\r\n     */\r\n    public set blurHorizontalSize(value: number) {\r\n        this._thinEffectLayer.blurHorizontalSize = value;\r\n    }\r\n\r\n    /**\r\n     * Specifies the vertical size of the blur.\r\n     */\r\n    public set blurVerticalSize(value: number) {\r\n        this._thinEffectLayer.blurVerticalSize = value;\r\n    }\r\n\r\n    /**\r\n     * Gets the horizontal size of the blur.\r\n     */\r\n    @serialize()\r\n    public get blurHorizontalSize(): number {\r\n        return this._thinEffectLayer.blurHorizontalSize;\r\n    }\r\n\r\n    /**\r\n     * Gets the vertical size of the blur.\r\n     */\r\n    @serialize()\r\n    public get blurVerticalSize(): number {\r\n        return this._thinEffectLayer.blurVerticalSize;\r\n    }\r\n\r\n    /**\r\n     * An event triggered when the highlight layer is being blurred.\r\n     */\r\n    public onBeforeBlurObservable = new Observable<HighlightLayer>();\r\n\r\n    /**\r\n     * An event triggered when the highlight layer has been blurred.\r\n     */\r\n    public onAfterBlurObservable = new Observable<HighlightLayer>();\r\n\r\n    @serialize(\"options\")\r\n    private _options: Required<IHighlightLayerOptions>;\r\n\r\n    protected override readonly _thinEffectLayer: ThinHighlightLayer;\r\n    private _downSamplePostprocess: PassPostProcess;\r\n    private _horizontalBlurPostprocess: IBlurPostProcess;\r\n    private _verticalBlurPostprocess: IBlurPostProcess;\r\n    private _blurTexture: RenderTargetTexture;\r\n\r\n    /**\r\n     * Instantiates a new highlight Layer and references it to the scene..\r\n     * @param name The name of the layer\r\n     * @param scene The scene to use the layer in\r\n     * @param options Sets of none mandatory options to use with the layer (see IHighlightLayerOptions for more information)\r\n     */\r\n    constructor(name: string, scene?: Scene, options?: Partial<IHighlightLayerOptions>) {\r\n        super(name, scene, options !== undefined ? !!options.forceGLSL : false, new ThinHighlightLayer(name, scene, options));\r\n\r\n        // Warn on stencil\r\n        if (!this._engine.isStencilEnable) {\r\n            Logger.Warn(\"Rendering the Highlight Layer requires the stencil to be active on the canvas. var engine = new Engine(canvas, antialias, { stencil: true }\");\r\n        }\r\n\r\n        // Adapt options\r\n        this._options = {\r\n            mainTextureRatio: 0.5,\r\n            blurTextureSizeRatio: 0.5,\r\n            mainTextureFixedSize: 0,\r\n            blurHorizontalSize: 1.0,\r\n            blurVerticalSize: 1.0,\r\n            alphaBlendingMode: Constants.ALPHA_COMBINE,\r\n            camera: null,\r\n            renderingGroupId: -1,\r\n            mainTextureType: Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n            forceGLSL: false,\r\n            isStroke: false,\r\n            ...options,\r\n        };\r\n\r\n        // Initialize the layer\r\n        this._init(this._options);\r\n\r\n        // Do not render as long as no meshes have been added\r\n        this._shouldRender = false;\r\n    }\r\n\r\n    /**\r\n     * Get the effect name of the layer.\r\n     * @returns The effect name\r\n     */\r\n    public getEffectName(): string {\r\n        return HighlightLayer.EffectName;\r\n    }\r\n\r\n    protected override _numInternalDraws(): number {\r\n        return 2; // we need two rendering, one for the inner glow and the other for the outer glow\r\n    }\r\n\r\n    /**\r\n     * Create the merge effect. This is the shader use to blit the information back\r\n     * to the main canvas at the end of the scene rendering.\r\n     * @returns The effect created\r\n     */\r\n    protected _createMergeEffect(): Effect {\r\n        return this._thinEffectLayer._createMergeEffect();\r\n    }\r\n\r\n    /**\r\n     * Creates the render target textures and post processes used in the highlight layer.\r\n     */\r\n    protected _createTextureAndPostProcesses(): void {\r\n        let blurTextureWidth = this._mainTextureDesiredSize.width * this._options.blurTextureSizeRatio;\r\n        let blurTextureHeight = this._mainTextureDesiredSize.height * this._options.blurTextureSizeRatio;\r\n        blurTextureWidth = this._engine.needPOTTextures ? GetExponentOfTwo(blurTextureWidth, this._maxSize) : blurTextureWidth;\r\n        blurTextureHeight = this._engine.needPOTTextures ? GetExponentOfTwo(blurTextureHeight, this._maxSize) : blurTextureHeight;\r\n\r\n        let textureType = 0;\r\n        if (this._engine.getCaps().textureHalfFloatRender) {\r\n            textureType = Constants.TEXTURETYPE_HALF_FLOAT;\r\n        } else {\r\n            textureType = Constants.TEXTURETYPE_UNSIGNED_BYTE;\r\n        }\r\n\r\n        this._blurTexture = new RenderTargetTexture(\r\n            \"HighlightLayerBlurRTT\",\r\n            {\r\n                width: blurTextureWidth,\r\n                height: blurTextureHeight,\r\n            },\r\n            this._scene,\r\n            false,\r\n            true,\r\n            textureType\r\n        );\r\n        this._blurTexture.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        this._blurTexture.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        this._blurTexture.anisotropicFilteringLevel = 16;\r\n        this._blurTexture.updateSamplingMode(Texture.TRILINEAR_SAMPLINGMODE);\r\n        this._blurTexture.renderParticles = false;\r\n        this._blurTexture.ignoreCameraViewport = true;\r\n\r\n        this._textures = [this._blurTexture];\r\n\r\n        this._thinEffectLayer.bindTexturesForCompose = (effect: Effect) => {\r\n            effect.setTexture(\"textureSampler\", this._blurTexture);\r\n        };\r\n\r\n        this._thinEffectLayer._createTextureAndPostProcesses();\r\n\r\n        if (this._options.alphaBlendingMode === Constants.ALPHA_COMBINE) {\r\n            this._downSamplePostprocess = new PassPostProcess(\"HighlightLayerPPP\", {\r\n                size: this._options.blurTextureSizeRatio,\r\n                samplingMode: Texture.BILINEAR_SAMPLINGMODE,\r\n                engine: this._scene.getEngine(),\r\n                effectWrapper: this._thinEffectLayer._postProcesses[0] as ThinPassPostProcess,\r\n            });\r\n            this._downSamplePostprocess.externalTextureSamplerBinding = true;\r\n            this._downSamplePostprocess.onApplyObservable.add((effect) => {\r\n                effect.setTexture(\"textureSampler\", this._mainTexture);\r\n            });\r\n\r\n            this._horizontalBlurPostprocess = new GlowBlurPostProcess(\"HighlightLayerHBP\", new Vector2(1.0, 0), this._options.blurHorizontalSize, {\r\n                samplingMode: Texture.BILINEAR_SAMPLINGMODE,\r\n                engine: this._scene.getEngine(),\r\n                effectWrapper: this._thinEffectLayer._postProcesses[1] as ThinGlowBlurPostProcess,\r\n            });\r\n            this._horizontalBlurPostprocess.onApplyObservable.add((effect) => {\r\n                effect.setFloat2(\"screenSize\", blurTextureWidth, blurTextureHeight);\r\n            });\r\n\r\n            this._verticalBlurPostprocess = new GlowBlurPostProcess(\"HighlightLayerVBP\", new Vector2(0, 1.0), this._options.blurVerticalSize, {\r\n                samplingMode: Texture.BILINEAR_SAMPLINGMODE,\r\n                engine: this._scene.getEngine(),\r\n                effectWrapper: this._thinEffectLayer._postProcesses[2] as ThinGlowBlurPostProcess,\r\n            });\r\n            this._verticalBlurPostprocess.onApplyObservable.add((effect) => {\r\n                effect.setFloat2(\"screenSize\", blurTextureWidth, blurTextureHeight);\r\n            });\r\n\r\n            this._postProcesses = [this._downSamplePostprocess, this._horizontalBlurPostprocess, this._verticalBlurPostprocess];\r\n        } else {\r\n            this._horizontalBlurPostprocess = new BlurPostProcess(\"HighlightLayerHBP\", new Vector2(1.0, 0), this._options.blurHorizontalSize / 2, {\r\n                size: {\r\n                    width: blurTextureWidth,\r\n                    height: blurTextureHeight,\r\n                },\r\n                samplingMode: Texture.BILINEAR_SAMPLINGMODE,\r\n                engine: this._scene.getEngine(),\r\n                textureType,\r\n                effectWrapper: this._thinEffectLayer._postProcesses[0] as ThinBlurPostProcess,\r\n            });\r\n            this._horizontalBlurPostprocess.width = blurTextureWidth;\r\n            this._horizontalBlurPostprocess.height = blurTextureHeight;\r\n            this._horizontalBlurPostprocess.externalTextureSamplerBinding = true;\r\n            this._horizontalBlurPostprocess.onApplyObservable.add((effect) => {\r\n                effect.setTexture(\"textureSampler\", this._mainTexture);\r\n            });\r\n\r\n            this._verticalBlurPostprocess = new BlurPostProcess(\"HighlightLayerVBP\", new Vector2(0, 1.0), this._options.blurVerticalSize / 2, {\r\n                size: {\r\n                    width: blurTextureWidth,\r\n                    height: blurTextureHeight,\r\n                },\r\n                samplingMode: Texture.BILINEAR_SAMPLINGMODE,\r\n                engine: this._scene.getEngine(),\r\n                textureType,\r\n            });\r\n\r\n            this._postProcesses = [this._horizontalBlurPostprocess, this._verticalBlurPostprocess];\r\n        }\r\n\r\n        this._mainTexture.onAfterUnbindObservable.add(() => {\r\n            this.onBeforeBlurObservable.notifyObservers(this);\r\n\r\n            const internalTexture = this._blurTexture.renderTarget;\r\n            if (internalTexture) {\r\n                this._scene.postProcessManager.directRender(this._postProcesses, internalTexture, true);\r\n                this._engine.unBindFramebuffer(internalTexture, true);\r\n            }\r\n\r\n            this.onAfterBlurObservable.notifyObservers(this);\r\n        });\r\n\r\n        // Prevent autoClear.\r\n        this._postProcesses.map((pp) => {\r\n            pp.autoClear = false;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @returns whether or not the layer needs stencil enabled during the mesh rendering.\r\n     */\r\n    public needStencil(): boolean {\r\n        return this._thinEffectLayer.needStencil();\r\n    }\r\n\r\n    /**\r\n     * Checks for the readiness of the element composing the layer.\r\n     * @param subMesh the mesh to check for\r\n     * @param useInstances specify whether or not to use instances to render the mesh\r\n     * @returns true if ready otherwise, false\r\n     */\r\n    public isReady(subMesh: SubMesh, useInstances: boolean): boolean {\r\n        return this._thinEffectLayer.isReady(subMesh, useInstances);\r\n    }\r\n\r\n    /**\r\n     * Implementation specific of rendering the generating effect on the main canvas.\r\n     * @param effect The effect used to render through\r\n     * @param renderIndex\r\n     */\r\n    protected _internalRender(effect: Effect, renderIndex: number): void {\r\n        this._thinEffectLayer._internalCompose(effect, renderIndex);\r\n    }\r\n\r\n    /**\r\n     * @returns true if the layer contains information to display, otherwise false.\r\n     */\r\n    public override shouldRender(): boolean {\r\n        return this._thinEffectLayer.shouldRender();\r\n    }\r\n\r\n    /**\r\n     * Returns true if the mesh should render, otherwise false.\r\n     * @param mesh The mesh to render\r\n     * @returns true if it should render otherwise false\r\n     */\r\n    protected override _shouldRenderMesh(mesh: Mesh): boolean {\r\n        return this._thinEffectLayer._shouldRenderMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Returns true if the mesh can be rendered, otherwise false.\r\n     * @param mesh The mesh to render\r\n     * @param material The material used on the mesh\r\n     * @returns true if it can be rendered otherwise false\r\n     */\r\n    protected override _canRenderMesh(mesh: AbstractMesh, material: Material): boolean {\r\n        return this._thinEffectLayer._canRenderMesh(mesh, material);\r\n    }\r\n\r\n    /**\r\n     * Adds specific effects defines.\r\n     * @param defines The defines to add specifics to.\r\n     */\r\n    protected override _addCustomEffectDefines(defines: string[]): void {\r\n        this._thinEffectLayer._addCustomEffectDefines(defines);\r\n    }\r\n\r\n    /**\r\n     * Sets the required values for both the emissive texture and and the main color.\r\n     * @param mesh\r\n     * @param subMesh\r\n     * @param material\r\n     */\r\n    protected _setEmissiveTextureAndColor(mesh: Mesh, subMesh: SubMesh, material: Material): void {\r\n        this._thinEffectLayer._setEmissiveTextureAndColor(mesh, subMesh, material);\r\n    }\r\n\r\n    /**\r\n     * Add a mesh in the exclusion list to prevent it to impact or being impacted by the highlight layer.\r\n     * @param mesh The mesh to exclude from the highlight layer\r\n     */\r\n    public addExcludedMesh(mesh: Mesh) {\r\n        this._thinEffectLayer.addExcludedMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the exclusion list to let it impact or being impacted by the highlight layer.\r\n     * @param mesh The mesh to highlight\r\n     */\r\n    public removeExcludedMesh(mesh: Mesh) {\r\n        this._thinEffectLayer.removeExcludedMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Determine if a given mesh will be highlighted by the current HighlightLayer\r\n     * @param mesh mesh to test\r\n     * @returns true if the mesh will be highlighted by the current HighlightLayer\r\n     */\r\n    public override hasMesh(mesh: AbstractMesh): boolean {\r\n        return this._thinEffectLayer.hasMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Add a mesh in the highlight layer in order to make it glow with the chosen color.\r\n     * @param mesh The mesh to highlight\r\n     * @param color The color of the highlight\r\n     * @param glowEmissiveOnly Extract the glow from the emissive texture\r\n     */\r\n    public addMesh(mesh: Mesh, color: Color3, glowEmissiveOnly = false) {\r\n        this._thinEffectLayer.addMesh(mesh, color, glowEmissiveOnly);\r\n    }\r\n\r\n    /**\r\n     * Remove a mesh from the highlight layer in order to make it stop glowing.\r\n     * @param mesh The mesh to highlight\r\n     */\r\n    public removeMesh(mesh: Mesh) {\r\n        this._thinEffectLayer.removeMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Remove all the meshes currently referenced in the highlight layer\r\n     */\r\n    public removeAllMeshes(): void {\r\n        this._thinEffectLayer.removeAllMeshes();\r\n    }\r\n\r\n    /**\r\n     * Free any resources and references associated to a mesh.\r\n     * Internal use\r\n     * @param mesh The mesh to free.\r\n     * @internal\r\n     */\r\n    public _disposeMesh(mesh: Mesh): void {\r\n        this._thinEffectLayer._disposeMesh(mesh);\r\n    }\r\n\r\n    /**\r\n     * Gets the class name of the effect layer\r\n     * @returns the string with the class name of the effect layer\r\n     */\r\n    public override getClassName(): string {\r\n        return \"HighlightLayer\";\r\n    }\r\n\r\n    /**\r\n     * Serializes this Highlight layer\r\n     * @returns a serialized Highlight layer object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject = SerializationHelper.Serialize(this);\r\n        serializationObject.customType = \"BABYLON.HighlightLayer\";\r\n\r\n        // Highlighted meshes\r\n        serializationObject.meshes = [];\r\n\r\n        const meshes = this._thinEffectLayer._meshes;\r\n        if (meshes) {\r\n            for (const m in meshes) {\r\n                const mesh = meshes[m];\r\n\r\n                if (mesh) {\r\n                    serializationObject.meshes.push({\r\n                        glowEmissiveOnly: mesh.glowEmissiveOnly,\r\n                        color: mesh.color.asArray(),\r\n                        meshId: mesh.mesh.id,\r\n                    });\r\n                }\r\n            }\r\n        }\r\n\r\n        // Excluded meshes\r\n        serializationObject.excludedMeshes = [];\r\n\r\n        const excludedMeshes = this._thinEffectLayer._excludedMeshes;\r\n        if (excludedMeshes) {\r\n            for (const e in excludedMeshes) {\r\n                const excludedMesh = excludedMeshes[e];\r\n\r\n                if (excludedMesh) {\r\n                    serializationObject.excludedMeshes.push(excludedMesh.mesh.id);\r\n                }\r\n            }\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Creates a Highlight layer from parsed Highlight layer data\r\n     * @param parsedHightlightLayer defines the Highlight layer data\r\n     * @param scene defines the current scene\r\n     * @param rootUrl defines the root URL containing the Highlight layer information\r\n     * @returns a parsed Highlight layer\r\n     */\r\n    public static override Parse(parsedHightlightLayer: any, scene: Scene, rootUrl: string): HighlightLayer {\r\n        const hl = SerializationHelper.Parse(() => new HighlightLayer(parsedHightlightLayer.name, scene, parsedHightlightLayer.options), parsedHightlightLayer, scene, rootUrl);\r\n        let index;\r\n\r\n        // Excluded meshes\r\n        for (index = 0; index < parsedHightlightLayer.excludedMeshes.length; index++) {\r\n            const mesh = scene.getMeshById(parsedHightlightLayer.excludedMeshes[index]);\r\n            if (mesh) {\r\n                hl.addExcludedMesh(<Mesh>mesh);\r\n            }\r\n        }\r\n\r\n        // Included meshes\r\n        for (index = 0; index < parsedHightlightLayer.meshes.length; index++) {\r\n            const highlightedMesh = parsedHightlightLayer.meshes[index];\r\n            const mesh = scene.getMeshById(highlightedMesh.meshId);\r\n\r\n            if (mesh) {\r\n                hl.addMesh(<Mesh>mesh, Color3.FromArray(highlightedMesh.color), highlightedMesh.glowEmissiveOnly);\r\n            }\r\n        }\r\n\r\n        return hl;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.HighlightLayer\", HighlightLayer);\r\n"], "names": [], "mappings": ";;;;AAAA,oDAAA,EAAsD,CACtD,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAO/C,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,2CAA2C,CAAC;AAEhF,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AACnE,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AACnE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAK7C,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AACvE,OAAO,EAAE,gBAAgB,EAAE,MAAM,yBAAyB,CAAC;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;;;;;;;;;;;;;;;;;;;6IAc5D,QAAK,CAAC,SAAS,CAAC,uBAAuB,GAAG,SAAU,IAAY;IAC5D,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;QAC7D,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,aAAa,EAAE,KAAK,cAAc,CAAC,UAAU,EAAE,CAAC;YACnH,OAAa,IAAI,CAAC,YAAY,CAAC,KAAK,CAAoB,CAAC;QAC7D,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAMF;;;GAGG,CACH,MAAM,mBAAoB,6KAAQ,cAAW;IAGzC,YACI,IAAY,EACL,SAAkB,EAClB,MAAc,EACrB,OAAoC,EACpC,SAA2B,IAAI,EAC/B,uLAAuB,UAAO,CAAC,qBAAqB,EACpD,MAAuB,EACvB,QAAkB,CAAA;QAElB,MAAM,YAAY,GAAG;YACjB,QAAQ,mKAAE,0BAAuB,CAAC,QAAQ;YAC1C,IAAI,EAAE,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACvD,MAAM;YACN,YAAY;YACZ,MAAM;YACN,QAAQ;YACR,GAAI,OAA8B;SACrC,CAAC;QAEF,KAAK,CAAC,IAAI,mKAAE,0BAAuB,CAAC,WAAW,EAAE;YAC7C,aAAa,EAAE,OAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,qKAAI,0BAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7J,GAAG,YAAY;SAClB,CAAC,CAAC;QArBI,IAAA,CAAA,SAAS,GAAT,SAAS,CAAS;QAClB,IAAA,CAAA,MAAM,GAAN,MAAM,CAAQ;QAsBrB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5B,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;YAC9C,IAAI,CAAC,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;QACpD,CAAC,CAAC,CAAC;IACP,CAAC;IAEkB,cAAc,CAAC,SAAkB,EAAE,IAAoB,EAAA;QACtE,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,6CAA6C,CAAC,CAAC,CAAC;QACrE,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,yCAAyC,CAAC,CAAC,CAAC;QACjE,CAAC;QAED,KAAK,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;CACJ;AAgBK,MAAO,cAAe,sKAAQ,cAAW;IAM3C;;;OAGG,CACI,MAAM,KAAK,YAAY,GAAA;QAC1B,2KAAO,qBAAkB,CAAC,YAAY,CAAC;IAC3C,CAAC;IAEM,MAAM,KAAK,YAAY,CAAC,KAAa,EAAA;QACxC,yLAAkB,CAAC,YAAY,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG,CAEH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IAC3C,CAAC;IAED,IAAW,SAAS,CAAC,KAAc,EAAA;QAC/B,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG,CAEH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IAC3C,CAAC;IAED,IAAW,SAAS,CAAC,KAAc,EAAA;QAC/B,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,CAAC,KAAa,EAAA;QACvC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACrD,CAAC;IAED;;OAEG,CACH,IAAW,gBAAgB,CAAC,KAAa,EAAA;QACrC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,GAAG,KAAK,CAAC;IACnD,CAAC;IAED;;OAEG,CAEH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;IACpD,CAAC;IAED;;OAEG,CAEH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;IAClD,CAAC;IAqBD;;;;;OAKG,CACH,YAAY,IAAY,EAAE,KAAa,EAAE,OAAyC,CAAA;QAC9E,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,yLAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;QA1B1H;;WAEG,CACI,IAAA,CAAA,sBAAsB,GAAG,8JAAI,aAAU,EAAkB,CAAC;QAEjE;;WAEG,CACI,IAAA,CAAA,qBAAqB,GAAG,8JAAI,aAAU,EAAkB,CAAC;QAoB5D,kBAAkB;QAClB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;kKAChC,SAAM,CAAC,IAAI,CAAC,6IAA6I,CAAC,CAAC;QAC/J,CAAC;QAED,gBAAgB;QAChB,IAAI,CAAC,QAAQ,GAAG;YACZ,gBAAgB,EAAE,GAAG;YACrB,oBAAoB,EAAE,GAAG;YACzB,oBAAoB,EAAE,CAAC;YACvB,kBAAkB,EAAE,GAAG;YACvB,gBAAgB,EAAE,GAAG;YACrB,iBAAiB,EAAE,SAAS,CAAC,aAAa;YAC1C,MAAM,EAAE,IAAI;YACZ,gBAAgB,EAAE,CAAC,CAAC;YACpB,eAAe,EAAE,SAAS,CAAC,yBAAyB;YACpD,SAAS,EAAE,KAAK;YAChB,QAAQ,EAAE,KAAK;YACf,GAAG,OAAO;SACb,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE1B,qDAAqD;QACrD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,cAAc,CAAC,UAAU,CAAC;IACrC,CAAC;IAEkB,iBAAiB,GAAA;QAChC,OAAO,CAAC,CAAC,CAAC,iFAAiF;IAC/F,CAAC;IAED;;;;OAIG,CACO,kBAAkB,GAAA;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG,CACO,8BAA8B,GAAA;QACpC,IAAI,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC;QAC/F,IAAI,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC;QACjG,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,EAAC,wLAAA,AAAgB,EAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACvH,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,uKAAC,mBAAA,AAAgB,EAAC,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC;QAE1H,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,sBAAsB,EAAE,CAAC;YAChD,WAAW,GAAG,SAAS,CAAC,sBAAsB,CAAC;QACnD,CAAC,MAAM,CAAC;YACJ,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,wLAAI,sBAAmB,CACvC,uBAAuB,EACvB;YACI,KAAK,EAAE,gBAAgB;YACvB,MAAM,EAAE,iBAAiB;SAC5B,EACD,IAAI,CAAC,MAAM,EACX,KAAK,EACL,IAAI,EACJ,WAAW,CACd,CAAC;QACF,IAAI,CAAC,YAAY,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;QACpD,IAAI,CAAC,YAAY,CAAC,KAAK,2KAAG,UAAO,CAAC,iBAAiB,CAAC;QACpD,IAAI,CAAC,YAAY,CAAC,yBAAyB,GAAG,EAAE,CAAC;QACjD,IAAI,CAAC,YAAY,CAAC,kBAAkB,yKAAC,UAAO,CAAC,sBAAsB,CAAC,CAAC;QACrE,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,KAAK,CAAC;QAC1C,IAAI,CAAC,YAAY,CAAC,oBAAoB,GAAG,IAAI,CAAC;QAE9C,IAAI,CAAC,SAAS,GAAG;YAAC,IAAI,CAAC,YAAY;SAAC,CAAC;QAErC,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,GAAG,CAAC,MAAc,EAAE,EAAE;YAC9D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3D,CAAC,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,8BAA8B,EAAE,CAAC;QAEvD,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,KAAK,GAAA,MAAS,CAAC,aAAa,EAAE,CAAC;YAC9D,IAAI,CAAC,sBAAsB,GAAG,4KAAI,kBAAe,CAAC,mBAAmB,EAAE;gBACnE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,oBAAoB;gBACxC,YAAY,0KAAE,UAAO,CAAC,qBAAqB;gBAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBAC/B,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAwB;aAChF,CAAC,CAAC;YACH,IAAI,CAAC,sBAAsB,CAAC,6BAA6B,GAAG,IAAI,CAAC;YACjE,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACzD,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,0BAA0B,GAAG,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,mKAAI,UAAO,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE;gBAClI,YAAY,0KAAE,UAAO,CAAC,qBAAqB;gBAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBAC/B,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAA4B;aACpF,CAAC,CAAC;YACH,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7D,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,wBAAwB,GAAG,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,mKAAI,UAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE;gBAC9H,YAAY,0KAAE,UAAO,CAAC,qBAAqB;gBAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBAC/B,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAA4B;aACpF,CAAC,CAAC;YACH,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC3D,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,GAAG;gBAAC,IAAI,CAAC,sBAAsB;gBAAE,IAAI,CAAC,0BAA0B;gBAAE,IAAI,CAAC,wBAAwB;aAAC,CAAC;QACxH,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,0BAA0B,GAAG,4KAAI,kBAAe,CAAC,mBAAmB,EAAE,mKAAI,UAAO,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,CAAC,EAAE;gBAClI,IAAI,EAAE;oBACF,KAAK,EAAE,gBAAgB;oBACvB,MAAM,EAAE,iBAAiB;iBAC5B;gBACD,YAAY,0KAAE,UAAO,CAAC,qBAAqB;gBAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBAC/B,WAAW;gBACX,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAwB;aAChF,CAAC,CAAC;YACH,IAAI,CAAC,0BAA0B,CAAC,KAAK,GAAG,gBAAgB,CAAC;YACzD,IAAI,CAAC,0BAA0B,CAAC,MAAM,GAAG,iBAAiB,CAAC;YAC3D,IAAI,CAAC,0BAA0B,CAAC,6BAA6B,GAAG,IAAI,CAAC;YACrE,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7D,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,wBAAwB,GAAG,4KAAI,kBAAe,CAAC,mBAAmB,EAAE,kKAAI,WAAO,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG,CAAC,EAAE;gBAC9H,IAAI,EAAE;oBACF,KAAK,EAAE,gBAAgB;oBACvB,MAAM,EAAE,iBAAiB;iBAC5B;gBACD,YAAY,0KAAE,UAAO,CAAC,qBAAqB;gBAC3C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBAC/B,WAAW;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,GAAG;gBAAC,IAAI,CAAC,0BAA0B;gBAAE,IAAI,CAAC,wBAAwB;aAAC,CAAC;QAC3F,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC/C,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAElD,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;YACvD,IAAI,eAAe,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;gBACxF,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;YAC1D,CAAC;YAED,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;YAC3B,EAAE,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,WAAW,GAAA;QACd,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;IAC/C,CAAC;IAED;;;;;OAKG,CACI,OAAO,CAAC,OAAgB,EAAE,YAAqB,EAAA;QAClD,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IAChE,CAAC;IAED;;;;OAIG,CACO,eAAe,CAAC,MAAc,EAAE,WAAmB,EAAA;QACzD,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG,CACa,YAAY,GAAA;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,CAAC;IAChD,CAAC;IAED;;;;OAIG,CACgB,iBAAiB,CAAC,IAAU,EAAA;QAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;IAED;;;;;OAKG,CACgB,cAAc,CAAC,IAAkB,EAAE,QAAkB,EAAA;QACpE,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAChE,CAAC;IAED;;;OAGG,CACgB,uBAAuB,CAAC,OAAiB,EAAA;QACxD,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;OAKG,CACO,2BAA2B,CAAC,IAAU,EAAE,OAAgB,EAAE,QAAkB,EAAA;QAClF,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC/E,CAAC;IAED;;;OAGG,CACI,eAAe,CAAC,IAAU,EAAA;QAC7B,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED;;;OAGG,CACI,kBAAkB,CAAC,IAAU,EAAA;QAChC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC;IAED;;;;OAIG,CACa,OAAO,CAAC,IAAkB,EAAA;QACtC,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;OAKG,CACI,OAAO,CAAC,IAAU,EAAE,KAAa,EAAE,gBAAgB,GAAG,KAAK,EAAA;QAC9D,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;IACjE,CAAC;IAED;;;OAGG,CACI,UAAU,CAAC,IAAU,EAAA;QACxB,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG,CACI,eAAe,GAAA;QAClB,IAAI,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC;IAC5C,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,IAAU,EAAA;QAC1B,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,6KAAG,uBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE,mBAAmB,CAAC,UAAU,GAAG,wBAAwB,CAAC;QAE1D,qBAAqB;QACrB,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAEhC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;QAC7C,IAAI,MAAM,EAAE,CAAC;YACT,IAAK,MAAM,CAAC,IAAI,MAAM,CAAE,CAAC;gBACrB,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBAEvB,IAAI,IAAI,EAAE,CAAC;oBACP,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC;wBAC5B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;wBACvC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;wBAC3B,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;qBACvB,CAAC,CAAC;gBACP,CAAC;YACL,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,mBAAmB,CAAC,cAAc,GAAG,EAAE,CAAC;QAExC,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;QAC7D,IAAI,cAAc,EAAE,CAAC;YACjB,IAAK,MAAM,CAAC,IAAI,cAAc,CAAE,CAAC;gBAC7B,MAAM,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;gBAEvC,IAAI,YAAY,EAAE,CAAC;oBACf,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAClE,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAU,KAAK,CAAC,qBAA0B,EAAE,KAAY,EAAE,OAAe,EAAA;QAClF,MAAM,EAAE,8KAAG,sBAAmB,CAAC,KAAK,CAAC,GAAG,CAAG,CAAD,GAAK,cAAc,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,EAAE,qBAAqB,CAAC,OAAO,CAAC,EAAE,qBAAqB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACxK,IAAI,KAAK,CAAC;QAEV,kBAAkB;QAClB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,qBAAqB,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC3E,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,qBAAqB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;YAC5E,IAAI,IAAI,EAAE,CAAC;gBACP,EAAE,CAAC,eAAe,CAAO,IAAI,CAAC,CAAC;YACnC,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,qBAAqB,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACnE,MAAM,eAAe,GAAG,qBAAqB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5D,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAEvD,IAAI,IAAI,EAAE,CAAC;gBACP,EAAE,CAAC,OAAO,CAAO,IAAI,gKAAE,SAAM,CAAC,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,eAAe,CAAC,gBAAgB,CAAC,CAAC;YACtG,CAAC;QACL,CAAC;QAED,OAAO,EAAE,CAAC;IACd,CAAC;;AAjeD;;GAEG,CACoB,eAAA,UAAU,GAAG,gBAAgB,AAAnB,CAAoB;wJAkBrD,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;+CAGX;wJAUD,aAAA,EAAA;KADC,yKAAS,AAAT,EAAW;+CAGX;wJAwBD,aAAA,EAAA;KADC,yKAAA,AAAS,EAAE;wDAGX;wJAMD,aAAA,EAAA;kKADC,YAAA,AAAS,EAAE;sDAGX;CAaO,oKAAA,EAAA;kKADP,YAAA,AAAS,EAAC,SAAS,CAAC;gDAC8B;6JAkZvD,gBAAA,AAAa,EAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 3043, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/layerSceneComponent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/layerSceneComponent.ts"], "sourcesContent": ["import type { Camera } from \"../Cameras/camera\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport type { ISceneComponent } from \"../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport type { Layer } from \"./layer\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { IAssetContainer } from \"core/IAssetContainer\";\r\n\r\n/**\r\n * Defines the layer scene component responsible to manage any layers\r\n * in a given scene.\r\n */\r\nexport class LayerSceneComponent implements ISceneComponent {\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_LAYER;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    private _engine: AbstractEngine;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene?: Scene) {\r\n        this.scene = scene || <Scene>EngineStore.LastCreatedScene;\r\n        if (!this.scene) {\r\n            return;\r\n        }\r\n        this._engine = this.scene.getEngine();\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._beforeCameraDrawStage.registerStep(SceneComponentConstants.STEP_BEFORECAMERADRAW_LAYER, this, this._drawCameraBackground);\r\n        this.scene._afterCameraDrawStage.registerStep(SceneComponentConstants.STEP_AFTERCAMERADRAW_LAYER, this, this._drawCameraForegroundWithPostProcessing);\r\n        this.scene._afterCameraPostProcessStage.registerStep(SceneComponentConstants.STEP_AFTERCAMERAPOSTPROCESS_LAYER, this, this._drawCameraForegroundWithoutPostProcessing);\r\n\r\n        this.scene._beforeRenderTargetDrawStage.registerStep(SceneComponentConstants.STEP_BEFORERENDERTARGETDRAW_LAYER, this, this._drawRenderTargetBackground);\r\n        this.scene._afterRenderTargetDrawStage.registerStep(SceneComponentConstants.STEP_AFTERRENDERTARGETDRAW_LAYER, this, this._drawRenderTargetForegroundWithPostProcessing);\r\n        this.scene._afterRenderTargetPostProcessStage.registerStep(\r\n            SceneComponentConstants.STEP_AFTERRENDERTARGETPOSTPROCESS_LAYER,\r\n            this,\r\n            this._drawRenderTargetForegroundWithoutPostProcessing\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        const layers = this.scene.layers;\r\n\r\n        for (const layer of layers) {\r\n            layer._rebuild();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        const layers = this.scene.layers;\r\n\r\n        while (layers.length) {\r\n            layers[0].dispose();\r\n        }\r\n    }\r\n\r\n    private _draw(predicate: (layer: Layer) => boolean): void {\r\n        const layers = this.scene.layers;\r\n\r\n        if (layers.length) {\r\n            this._engine.setDepthBuffer(false);\r\n            for (const layer of layers) {\r\n                if (predicate(layer)) {\r\n                    layer.render();\r\n                }\r\n            }\r\n            this._engine.setDepthBuffer(true);\r\n        }\r\n    }\r\n\r\n    private _drawCameraPredicate(layer: Layer, isBackground: boolean, applyPostProcess: boolean, cameraLayerMask: number): boolean {\r\n        return (\r\n            !layer.renderOnlyInRenderTargetTextures &&\r\n            layer.isBackground === isBackground &&\r\n            layer.applyPostProcess === applyPostProcess &&\r\n            (layer.layerMask & cameraLayerMask) !== 0\r\n        );\r\n    }\r\n\r\n    private _drawCameraBackground(camera: Camera): void {\r\n        this._draw((layer: Layer) => {\r\n            return this._drawCameraPredicate(layer, true, true, camera.layerMask);\r\n        });\r\n    }\r\n\r\n    private _drawCameraForegroundWithPostProcessing(camera: Camera): void {\r\n        this._draw((layer: Layer) => {\r\n            return this._drawCameraPredicate(layer, false, true, camera.layerMask);\r\n        });\r\n    }\r\n\r\n    private _drawCameraForegroundWithoutPostProcessing(camera: Camera): void {\r\n        this._draw((layer: Layer) => {\r\n            return this._drawCameraPredicate(layer, false, false, camera.layerMask);\r\n        });\r\n    }\r\n\r\n    private _drawRenderTargetPredicate(layer: Layer, isBackground: boolean, applyPostProcess: boolean, cameraLayerMask: number, renderTargetTexture: RenderTargetTexture): boolean {\r\n        return (\r\n            layer.renderTargetTextures.length > 0 &&\r\n            layer.isBackground === isBackground &&\r\n            layer.applyPostProcess === applyPostProcess &&\r\n            layer.renderTargetTextures.indexOf(renderTargetTexture) > -1 &&\r\n            (layer.layerMask & cameraLayerMask) !== 0\r\n        );\r\n    }\r\n\r\n    private _drawRenderTargetBackground(renderTarget: RenderTargetTexture): void {\r\n        this._draw((layer: Layer) => {\r\n            return this._drawRenderTargetPredicate(layer, true, true, this.scene.activeCamera!.layerMask, renderTarget);\r\n        });\r\n    }\r\n\r\n    private _drawRenderTargetForegroundWithPostProcessing(renderTarget: RenderTargetTexture): void {\r\n        this._draw((layer: Layer) => {\r\n            return this._drawRenderTargetPredicate(layer, false, true, this.scene.activeCamera!.layerMask, renderTarget);\r\n        });\r\n    }\r\n\r\n    private _drawRenderTargetForegroundWithoutPostProcessing(renderTarget: RenderTargetTexture): void {\r\n        this._draw((layer: Layer) => {\r\n            return this._drawRenderTargetPredicate(layer, false, false, this.scene.activeCamera!.layerMask, renderTarget);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Adds all the elements from the container to the scene\r\n     * @param container the container holding the elements\r\n     */\r\n    public addFromContainer(container: IAssetContainer): void {\r\n        if (!container.layers) {\r\n            return;\r\n        }\r\n        for (const layer of container.layers) {\r\n            this.scene.layers.push(layer);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes all the elements in the container from the scene\r\n     * @param container contains the elements to remove\r\n     * @param dispose if the removed element should be disposed (default: false)\r\n     */\r\n    public removeFromContainer(container: IAssetContainer, dispose = false): void {\r\n        if (!container.layers) {\r\n            return;\r\n        }\r\n        for (const layer of container.layers) {\r\n            const index = this.scene.layers.indexOf(layer);\r\n            if (index !== -1) {\r\n                this.scene.layers.splice(index, 1);\r\n            }\r\n            if (dispose) {\r\n                layer.dispose();\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAIA,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAG5D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;;;AAO/C,MAAO,mBAAmB;IAa5B;;;OAGG,CACH,YAAY,KAAa,CAAA;QAhBzB;;WAEG,CACa,IAAA,CAAA,IAAI,yJAAG,0BAAuB,CAAC,UAAU,CAAC;QActD,IAAI,CAAC,KAAK,GAAG,KAAK,kKAAW,cAAW,CAAC,gBAAgB,CAAC;QAC1D,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG,CACI,QAAQ,GAAA;QACX,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,YAAY,uJAAC,0BAAuB,CAAC,2BAA2B,EAAE,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACtI,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,YAAY,uJAAC,0BAAuB,CAAC,0BAA0B,EAAE,IAAI,EAAE,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACtJ,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,YAAY,uJAAC,0BAAuB,CAAC,iCAAiC,EAAE,IAAI,EAAE,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAEvK,IAAI,CAAC,KAAK,CAAC,4BAA4B,CAAC,YAAY,uJAAC,0BAAuB,CAAC,iCAAiC,EAAE,IAAI,EAAE,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACxJ,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,YAAY,uJAAC,0BAAuB,CAAC,gCAAgC,EAAE,IAAI,EAAE,IAAI,CAAC,6CAA6C,CAAC,CAAC;QACxK,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,YAAY,uJACtD,0BAAuB,CAAC,uCAAuC,EAC/D,IAAI,EACJ,IAAI,CAAC,gDAAgD,CACxD,CAAC;IACN,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAEjC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;YACzB,KAAK,CAAC,QAAQ,EAAE,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAEjC,MAAO,MAAM,CAAC,MAAM,CAAE,CAAC;YACnB,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QACxB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,SAAoC,EAAA;QAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAEjC,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YACnC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAE,CAAC;gBACzB,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;oBACnB,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,CAAC;YACL,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,KAAY,EAAE,YAAqB,EAAE,gBAAyB,EAAE,eAAuB,EAAA;QAChH,OAAO,AACH,CAAC,KAAK,CAAC,gCAAgC,IACvC,KAAK,CAAC,YAAY,KAAK,YAAY,IACnC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,IAC3C,CAAC,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,CAC5C,CAAC;IACN,CAAC;IAEO,qBAAqB,CAAC,MAAc,EAAA;QACxC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;YACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,uCAAuC,CAAC,MAAc,EAAA;QAC1D,IAAI,CAAC,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;YACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,0CAA0C,CAAC,MAAc,EAAA;QAC7D,IAAI,CAAC,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;YACxB,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC5E,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,0BAA0B,CAAC,KAAY,EAAE,YAAqB,EAAE,gBAAyB,EAAE,eAAuB,EAAE,mBAAwC,EAAA;QAChK,OAAO,AACH,KAAK,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,IACrC,KAAK,CAAC,YAAY,KAAK,YAAY,IACnC,KAAK,CAAC,gBAAgB,KAAK,gBAAgB,IAC3C,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,IAC5D,CAAC,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,CAC5C,CAAC;IACN,CAAC;IAEO,2BAA2B,CAAC,YAAiC,EAAA;QACjE,IAAI,CAAC,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;YACxB,OAAO,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,YAAa,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAChH,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,6CAA6C,CAAC,YAAiC,EAAA;QACnF,IAAI,CAAC,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;YACxB,OAAO,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,YAAa,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QACjH,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,gDAAgD,CAAC,YAAiC,EAAA;QACtF,IAAI,CAAC,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;YACxB,OAAO,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAa,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAClH,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG,CACI,gBAAgB,CAAC,SAA0B,EAAA;QAC9C,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,CAAE,CAAC;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,mBAAmB,CAAC,SAA0B,EAAE,OAAO,GAAG,KAAK,EAAA;QAClE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,MAAM,CAAE,CAAC;YACnC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACvC,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACV,KAAK,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 3173, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/layer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/layer.ts"], "sourcesContent": ["import type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Vector2 } from \"../Maths/math.vector\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { VertexBuffer } from \"../Buffers/buffer\";\r\nimport { Material } from \"../Materials/material\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport type { BaseTexture } from \"../Materials/Textures/baseTexture\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { LayerSceneComponent } from \"./layerSceneComponent\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { RenderTargetTexture } from \"../Materials/Textures/renderTargetTexture\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\n\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * This represents a full screen 2d layer.\r\n * This can be useful to display a picture in the  background of your scene for instance.\r\n * @see https://www.babylonjs-playground.com/#08A2BS#1\r\n */\r\nexport class Layer {\r\n    /**\r\n     * Force all the layers to compile to glsl even on WebGPU engines.\r\n     * False by default. This is mostly meant for backward compatibility.\r\n     */\r\n    public static ForceGLSL = false;\r\n    /**\r\n     * Define the texture the layer should display.\r\n     */\r\n    public texture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Is the layer in background or foreground.\r\n     */\r\n    public isBackground: boolean;\r\n\r\n    private _applyPostProcess: boolean = true;\r\n    /**\r\n     * Determines if the layer is drawn before (true) or after (false) post-processing.\r\n     * If the layer is background, it is always before.\r\n     */\r\n    public set applyPostProcess(value: boolean) {\r\n        this._applyPostProcess = value;\r\n    }\r\n    public get applyPostProcess(): boolean {\r\n        return this.isBackground || this._applyPostProcess;\r\n    }\r\n\r\n    /**\r\n     * Define the color of the layer (instead of texture).\r\n     */\r\n    public color: Color4;\r\n\r\n    /**\r\n     * Define the scale of the layer in order to zoom in out of the texture.\r\n     */\r\n    public scale = new Vector2(1, 1);\r\n\r\n    /**\r\n     * Define an offset for the layer in order to shift the texture.\r\n     */\r\n    public offset = new Vector2(0, 0);\r\n\r\n    /**\r\n     * Define the alpha blending mode used in the layer in case the texture or color has an alpha.\r\n     */\r\n    public alphaBlendingMode = Constants.ALPHA_COMBINE;\r\n\r\n    /**\r\n     * Define if the layer should alpha test or alpha blend with the rest of the scene.\r\n     * Alpha test will not mix with the background color in case of transparency.\r\n     * It will either use the texture color or the background depending on the alpha value of the current pixel.\r\n     */\r\n    public alphaTest: boolean;\r\n\r\n    /**\r\n     * Define a mask to restrict the layer to only some of the scene cameras.\r\n     */\r\n    public layerMask: number = 0x0fffffff;\r\n\r\n    /**\r\n     * Define the list of render target the layer is visible into.\r\n     */\r\n    public renderTargetTextures: RenderTargetTexture[] = [];\r\n\r\n    /**\r\n     * Define if the layer is only used in renderTarget or if it also\r\n     * renders in the main frame buffer of the canvas.\r\n     */\r\n    public renderOnlyInRenderTargetTextures = false;\r\n\r\n    /**\r\n     * Define if the colors of the layer should be generated in linear space (default: false)\r\n     */\r\n    public convertToLinearSpace = false;\r\n\r\n    /**\r\n     * Define if the layer is enabled (ie. should be displayed). Default: true\r\n     */\r\n    public isEnabled = true;\r\n\r\n    private _scene: Scene;\r\n    private _vertexBuffers: { [key: string]: Nullable<VertexBuffer> } = {};\r\n    private _indexBuffer: Nullable<DataBuffer>;\r\n    private _drawWrapper: DrawWrapper;\r\n    private _previousDefines: string;\r\n\r\n    /**\r\n     * An event triggered when the layer is disposed.\r\n     */\r\n    public onDisposeObservable = new Observable<Layer>();\r\n\r\n    private _onDisposeObserver: Nullable<Observer<Layer>>;\r\n    /**\r\n     * Back compatibility with callback before the onDisposeObservable existed.\r\n     * The set callback will be triggered when the layer has been disposed.\r\n     */\r\n    public set onDispose(callback: () => void) {\r\n        if (this._onDisposeObserver) {\r\n            this.onDisposeObservable.remove(this._onDisposeObserver);\r\n        }\r\n        this._onDisposeObserver = this.onDisposeObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered before rendering the scene\r\n     */\r\n    public onBeforeRenderObservable = new Observable<Layer>();\r\n\r\n    private _onBeforeRenderObserver: Nullable<Observer<Layer>>;\r\n    /**\r\n     * Back compatibility with callback before the onBeforeRenderObservable existed.\r\n     * The set callback will be triggered just before rendering the layer.\r\n     */\r\n    public set onBeforeRender(callback: () => void) {\r\n        if (this._onBeforeRenderObserver) {\r\n            this.onBeforeRenderObservable.remove(this._onBeforeRenderObserver);\r\n        }\r\n        this._onBeforeRenderObserver = this.onBeforeRenderObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * An event triggered after rendering the scene\r\n     */\r\n    public onAfterRenderObservable = new Observable<Layer>();\r\n\r\n    private _onAfterRenderObserver: Nullable<Observer<Layer>>;\r\n    /**\r\n     * Back compatibility with callback before the onAfterRenderObservable existed.\r\n     * The set callback will be triggered just after rendering the layer.\r\n     */\r\n    public set onAfterRender(callback: () => void) {\r\n        if (this._onAfterRenderObserver) {\r\n            this.onAfterRenderObservable.remove(this._onAfterRenderObserver);\r\n        }\r\n        this._onAfterRenderObserver = this.onAfterRenderObservable.add(callback);\r\n    }\r\n\r\n    /** Shader language used by the material */\r\n    private _shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    /**\r\n     * Gets the shader language used in this material.\r\n     */\r\n    public get shaderLanguage(): ShaderLanguage {\r\n        return this._shaderLanguage;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new layer.\r\n     * This represents a full screen 2d layer.\r\n     * This can be useful to display a picture in the  background of your scene for instance.\r\n     * @see https://www.babylonjs-playground.com/#08A2BS#1\r\n     * @param name Define the name of the layer in the scene\r\n     * @param imgUrl Define the url of the texture to display in the layer\r\n     * @param scene Define the scene the layer belongs to\r\n     * @param isBackground Defines whether the layer is displayed in front or behind the scene\r\n     * @param color Defines a color for the layer\r\n     * @param forceGLSL Use the GLSL code generation for the shader (even on WebGPU). Default is false\r\n     */\r\n    constructor(\r\n        /**\r\n         * Define the name of the layer.\r\n         */\r\n        public name: string,\r\n        imgUrl: Nullable<string>,\r\n        scene: Nullable<Scene>,\r\n        isBackground?: boolean,\r\n        color?: Color4,\r\n        forceGLSL = false\r\n    ) {\r\n        this.texture = imgUrl ? new Texture(imgUrl, scene, true) : null;\r\n        this.isBackground = isBackground === undefined ? true : isBackground;\r\n        this.color = color === undefined ? new Color4(1, 1, 1, 1) : color;\r\n\r\n        this._scene = <Scene>(scene || EngineStore.LastCreatedScene);\r\n        const engine = this._scene.getEngine();\r\n        if (engine.isWebGPU && !forceGLSL && !Layer.ForceGLSL) {\r\n            this._shaderLanguage = ShaderLanguage.WGSL;\r\n        }\r\n\r\n        let layerComponent = this._scene._getComponent(SceneComponentConstants.NAME_LAYER) as LayerSceneComponent;\r\n        if (!layerComponent) {\r\n            layerComponent = new LayerSceneComponent(this._scene);\r\n            this._scene._addComponent(layerComponent);\r\n        }\r\n        this._scene.layers.push(this);\r\n\r\n        this._drawWrapper = new DrawWrapper(engine);\r\n\r\n        // VBO\r\n        const vertices = [];\r\n        vertices.push(1, 1);\r\n        vertices.push(-1, 1);\r\n        vertices.push(-1, -1);\r\n        vertices.push(1, -1);\r\n\r\n        const vertexBuffer = new VertexBuffer(engine, vertices, VertexBuffer.PositionKind, false, false, 2);\r\n        this._vertexBuffers[VertexBuffer.PositionKind] = vertexBuffer;\r\n\r\n        this._createIndexBuffer();\r\n    }\r\n\r\n    private _shadersLoaded = false;\r\n\r\n    private _createIndexBuffer(): void {\r\n        const engine = this._scene.getEngine();\r\n\r\n        // Indices\r\n        const indices = [];\r\n        indices.push(0);\r\n        indices.push(1);\r\n        indices.push(2);\r\n\r\n        indices.push(0);\r\n        indices.push(2);\r\n        indices.push(3);\r\n\r\n        this._indexBuffer = engine.createIndexBuffer(indices);\r\n    }\r\n\r\n    /** @internal */\r\n    public _rebuild(): void {\r\n        const vb = this._vertexBuffers[VertexBuffer.PositionKind];\r\n\r\n        if (vb) {\r\n            vb._rebuild();\r\n        }\r\n\r\n        this._createIndexBuffer();\r\n    }\r\n\r\n    /**\r\n     * Checks if the layer is ready to be rendered\r\n     * @returns true if the layer is ready. False otherwise.\r\n     */\r\n    public isReady() {\r\n        const engine = this._scene.getEngine();\r\n\r\n        let defines = \"\";\r\n\r\n        if (this.alphaTest) {\r\n            defines = \"#define ALPHATEST\";\r\n        }\r\n\r\n        if (this.texture) {\r\n            if (this.texture.gammaSpace) {\r\n                if (this.convertToLinearSpace) {\r\n                    defines += \"\\n#define CONVERT_TO_LINEAR\";\r\n                }\r\n            } else if (!this.convertToLinearSpace) {\r\n                defines += \"\\n#define CONVERT_TO_GAMMA\";\r\n            }\r\n        }\r\n\r\n        if (this._previousDefines !== defines) {\r\n            this._previousDefines = defines;\r\n            this._drawWrapper.effect = engine.createEffect(\r\n                \"layer\",\r\n                [VertexBuffer.PositionKind],\r\n                [\"textureMatrix\", \"color\", \"scale\", \"offset\"],\r\n                [\"textureSampler\"],\r\n                defines,\r\n                undefined,\r\n                undefined,\r\n                undefined,\r\n                undefined,\r\n                this._shaderLanguage,\r\n                this._shadersLoaded\r\n                    ? undefined\r\n                    : async () => {\r\n                          if (this._shaderLanguage === ShaderLanguage.WGSL) {\r\n                              await Promise.all([import(\"../ShadersWGSL/layer.vertex\"), import(\"../ShadersWGSL/layer.fragment\")]);\r\n                          } else {\r\n                              await Promise.all([import(\"../Shaders/layer.vertex\"), import(\"../Shaders/layer.fragment\")]);\r\n                          }\r\n                          this._shadersLoaded = true;\r\n                      }\r\n            );\r\n        }\r\n\r\n        const currentEffect = this._drawWrapper.effect;\r\n\r\n        return !!currentEffect?.isReady() && (!this.texture || this.texture.isReady());\r\n    }\r\n\r\n    /**\r\n     * Renders the layer in the scene.\r\n     */\r\n    public render(): void {\r\n        if (!this.isEnabled) {\r\n            return;\r\n        }\r\n\r\n        const engine = this._scene.getEngine();\r\n\r\n        // Check\r\n        if (!this.isReady()) {\r\n            return;\r\n        }\r\n\r\n        const currentEffect = this._drawWrapper.effect!;\r\n\r\n        this.onBeforeRenderObservable.notifyObservers(this);\r\n\r\n        // Render\r\n        engine.enableEffect(this._drawWrapper);\r\n        engine.setState(false);\r\n\r\n        // Texture\r\n        if (this.texture) {\r\n            currentEffect.setTexture(\"textureSampler\", this.texture);\r\n            currentEffect.setMatrix(\"textureMatrix\", this.texture.getTextureMatrix());\r\n        }\r\n\r\n        // Color\r\n        currentEffect.setFloat4(\"color\", this.color.r, this.color.g, this.color.b, this.color.a);\r\n\r\n        // Scale / offset\r\n        currentEffect.setVector2(\"offset\", this.offset);\r\n        currentEffect.setVector2(\"scale\", this.scale);\r\n\r\n        // VBOs\r\n        engine.bindBuffers(this._vertexBuffers, this._indexBuffer, currentEffect);\r\n\r\n        // Draw order\r\n        if (!this.alphaTest) {\r\n            engine.setAlphaMode(this.alphaBlendingMode);\r\n            engine.drawElementsType(Material.TriangleFillMode, 0, 6);\r\n            engine.setAlphaMode(Constants.ALPHA_DISABLE);\r\n        } else {\r\n            engine.drawElementsType(Material.TriangleFillMode, 0, 6);\r\n        }\r\n\r\n        this.onAfterRenderObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Disposes and releases the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        const vertexBuffer = this._vertexBuffers[VertexBuffer.PositionKind];\r\n        if (vertexBuffer) {\r\n            vertexBuffer.dispose();\r\n            this._vertexBuffers[VertexBuffer.PositionKind] = null;\r\n        }\r\n\r\n        if (this._indexBuffer) {\r\n            this._scene.getEngine()._releaseBuffer(this._indexBuffer);\r\n            this._indexBuffer = null;\r\n        }\r\n\r\n        if (this.texture) {\r\n            this.texture.dispose();\r\n            this.texture = null;\r\n        }\r\n\r\n        // Clean RTT list\r\n        this.renderTargetTextures = [];\r\n\r\n        // Remove from scene\r\n        const index = this._scene.layers.indexOf(this);\r\n        this._scene.layers.splice(index, 1);\r\n\r\n        // Callback\r\n        this.onDisposeObservable.notifyObservers(this);\r\n\r\n        this.onDisposeObservable.clear();\r\n        this.onAfterRenderObservable.clear();\r\n        this.onBeforeRenderObservable.clear();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAGhD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AACrD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AAExD,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAI5D,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;;;;;;;;;;;AASjD,MAAO,KAAK;IAiBd;;;OAGG,CACH,IAAW,gBAAgB,CAAC,KAAc,EAAA;QACtC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IACD,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,CAAC;IACvD,CAAC;IAmED;;;OAGG,CACH,IAAW,SAAS,CAAC,QAAoB,EAAA;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrE,CAAC;IAQD;;;OAGG,CACH,IAAW,cAAc,CAAC,QAAoB,EAAA;QAC1C,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACvE,CAAC;QACD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/E,CAAC;IAQD;;;OAGG,CACH,IAAW,aAAa,CAAC,QAAoB,EAAA;QACzC,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC7E,CAAC;IAKD;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;OAWG,CACH,YACI;;OAEG,CACI,IAAY,EACnB,MAAwB,EACxB,KAAsB,EACtB,YAAsB,EACtB,KAAc,EACd,SAAS,GAAG,KAAK,CAAA;QALV,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QApJf,IAAA,CAAA,iBAAiB,GAAY,IAAI,CAAC;QAiB1C;;WAEG,CACI,IAAA,CAAA,KAAK,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjC;;WAEG,CACI,IAAA,CAAA,MAAM,GAAG,mKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAElC;;WAEG,CACI,IAAA,CAAA,iBAAiB,GAAG,SAAS,CAAC,aAAa,CAAC;QASnD;;WAEG,CACI,IAAA,CAAA,SAAS,GAAW,UAAU,CAAC;QAEtC;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAA0B,EAAE,CAAC;QAExD;;;WAGG,CACI,IAAA,CAAA,gCAAgC,GAAG,KAAK,CAAC;QAEhD;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAEpC;;WAEG,CACI,IAAA,CAAA,SAAS,GAAG,IAAI,CAAC;QAGhB,IAAA,CAAA,cAAc,GAA8C,CAAA,CAAE,CAAC;QAKvE;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,8JAAI,aAAU,EAAS,CAAC;QAcrD;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,8JAAI,aAAU,EAAS,CAAC;QAc1D;;WAEG,CACI,IAAA,CAAA,uBAAuB,GAAG,8JAAI,aAAU,EAAS,CAAC;QAczD,yCAAA,EAA2C,CACnC,IAAA,CAAA,eAAe,GAAA,EAAA,uBAAA,GAAuB;QAgEtC,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAhC3B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,4KAAI,UAAO,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAChE,IAAI,CAAC,YAAY,GAAG,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC;QACrE,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,kKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAElE,IAAI,CAAC,MAAM,GAAU,AAAC,KAAK,kKAAI,cAAW,CAAC,gBAAgB,CAAC,CAAC;QAC7D,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACpD,IAAI,CAAC,eAAe,GAAA,EAAA,uBAAA,EAAsB,CAAC;QAC/C,CAAC;QAED,IAAI,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,uJAAC,0BAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1G,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,cAAc,GAAG,yKAAI,sBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,CAAC,YAAY,GAAG,oKAAI,cAAW,CAAC,MAAM,CAAC,CAAC;QAE5C,MAAM;QACN,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAErB,MAAM,YAAY,GAAG,6JAAI,eAAY,CAAC,MAAM,EAAE,QAAQ,2JAAE,eAAY,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QACpG,IAAI,CAAC,cAAc,0JAAC,eAAY,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC;QAE9D,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAIO,kBAAkB,GAAA;QACtB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,UAAU;QACV,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;IAED,cAAA,EAAgB,CACT,QAAQ,GAAA;QACX,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,0JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;QAE1D,IAAI,EAAE,EAAE,CAAC;YACL,EAAE,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO,GAAG,mBAAmB,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC1B,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAC5B,OAAO,IAAI,6BAA6B,CAAC;gBAC7C,CAAC;YACL,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBACpC,OAAO,IAAI,4BAA4B,CAAC;YAC5C,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,YAAY,CAC1C,OAAO,EACP;yKAAC,eAAY,CAAC,YAAY;aAAC,EAC3B;gBAAC,eAAe;gBAAE,OAAO;gBAAE,OAAO;gBAAE,QAAQ;aAAC,EAC7C;gBAAC,gBAAgB;aAAC,EAClB,OAAO,EACP,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,cAAc,GACb,SAAS,GACT,KAAK,IAAI,EAAE;gBACP,IAAI,IAAI,CAAC,eAAe,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;oBAC/C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,6BAA6B,CAAC,EAAE,MAAM,CAAC,+BAA+B,CAAC,CAAC,CAAC,CAAC;;;qBAAA;gBACxG,CAAC,MAAM,CAAC;oBACJ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,yBAAyB,CAAC,EAAE,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC;;;qBAAA;gBAChG,CAAC;gBACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC/B,CAAC,CACV,CAAC;QACN,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;QAE/C,OAAO,CAAC,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;IACnF,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QAEvC,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC;QAEhD,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEpD,SAAS;QACT,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACvC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEvB,UAAU;QACV,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACzD,aAAa,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;QAC9E,CAAC;QAED,QAAQ;QACR,aAAa,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEzF,iBAAiB;QACjB,aAAa,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAChD,aAAa,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAE9C,OAAO;QACP,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAE1E,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC5C,MAAM,CAAC,gBAAgB,8JAAC,WAAQ,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACzD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,gBAAgB,8JAAC,WAAQ,CAAC,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,0JAAC,eAAY,CAAC,YAAY,CAAC,CAAC;QACpE,IAAI,YAAY,EAAE,CAAC;YACf,YAAY,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,cAAc,0JAAC,eAAY,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,iBAAiB;QACjB,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAE/B,oBAAoB;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAEpC,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;QACrC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;IAC1C,CAAC;;AAjXD;;;GAGG,CACW,MAAA,SAAS,GAAG,KAAK,AAAR,CAAS", "debugId": null}}, {"offset": {"line": 3456, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Layers/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Layers/index.ts"], "sourcesContent": ["export * from \"./effectLayer\";\r\nexport * from \"./effectLayerSceneComponent\";\r\nexport * from \"./glowLayer\";\r\nexport * from \"./highlightLayer\";\r\nexport * from \"./layer\";\r\nexport * from \"./layerSceneComponent\";\r\nexport * from \"./thinEffectLayer\";\r\nexport * from \"./thinGlowLayer\";\r\nexport * from \"./thinHighlightLayer\";\r\n\r\n// EffectLayer\r\nexport * from \"../Shaders/glowMapGeneration.fragment\";\r\nexport * from \"../Shaders/glowMapGeneration.vertex\";\r\nexport * from \"../ShadersWGSL/glowMapGeneration.fragment\";\r\nexport * from \"../ShadersWGSL/glowMapGeneration.vertex\";\r\n\r\n// Highlights\r\nexport * from \"../Shaders/glowMapMerge.fragment\";\r\nexport * from \"../Shaders/glowMapMerge.vertex\";\r\nexport * from \"../Shaders/glowBlurPostProcess.fragment\";\r\nexport * from \"../ShadersWGSL/glowMapMerge.fragment\";\r\nexport * from \"../ShadersWGSL/glowMapMerge.vertex\";\r\nexport * from \"../ShadersWGSL/glowBlurPostProcess.fragment\";\r\n\r\n// Layers shaders\r\nexport * from \"../Shaders/layer.fragment\";\r\nexport * from \"../Shaders/layer.vertex\";\r\nexport * from \"../ShadersWGSL/layer.fragment\";\r\nexport * from \"../ShadersWGSL/layer.vertex\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,eAAe,CAAC;AAC9B,cAAc,6BAA6B,CAAC;AAC5C,cAAc,aAAa,CAAC;AAC5B,cAAc,kBAAkB,CAAC;AACjC,cAAc,SAAS,CAAC;AACxB,cAAc,uBAAuB,CAAC;AACtC,cAAc,mBAAmB,CAAC;AAClC,cAAc,iBAAiB,CAAC;AAChC,cAAc,sBAAsB,CAAC;AAErC,cAAc;AACd,cAAc,uCAAuC,CAAC;AACtD,cAAc,qCAAqC,CAAC;AACpD,cAAc,2CAA2C,CAAC;AAC1D,cAAc,yCAAyC,CAAC;AAExD,aAAa;AACb,cAAc,kCAAkC,CAAC;AACjD,cAAc,gCAAgC,CAAC;AAC/C,cAAc,yCAAyC,CAAC;AACxD,cAAc,sCAAsC,CAAC;AACrD,cAAc,oCAAoC,CAAC;AACnD,cAAc,6CAA6C,CAAC;AAE5D,iBAAiB;AACjB,cAAc,2BAA2B,CAAC;AAC1C,cAAc,yBAAyB,CAAC;AACxC,cAAc,+BAA+B,CAAC;AAC9C,cAAc,6BAA6B,CAAC", "debugId": null}}]}