{"version": 3, "file": "compatibilityOptions.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Compat/compatibilityOptions.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,gEAAgE;AAChE,MAAM,CAAC,IAAI,yBAAyB,GAAG,KAAK,CAAC;AAE7C;;;GAGG;AACH,gEAAgE;AAChE,MAAM,UAAU,yBAAyB,CAAC,KAAc;IACpD,yBAAyB,GAAG,KAAK,CAAC;AACtC,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG;IAChC,yDAAyD;IACzD,IAAI,yBAAyB;QACzB,OAAO,yBAAyB,CAAC;IACrC,CAAC;IACD,IAAI,yBAAyB,CAAC,KAAK;QAC/B,yBAAyB,GAAG,KAAK,CAAC;IACtC,CAAC;IACD,wDAAwD;CAC3D,CAAC", "sourcesContent": ["/**\r\n * Defines if the system should use OpenGL convention for UVs when creating geometry or loading .babylon files (false by default)\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport let useOpenGLOrientationForUV = false;\r\n\r\n/**\r\n * Sets whether to use OpenGL convention for UVs\r\n * @param value the new value\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport function setOpenGLOrientationForUV(value: boolean) {\r\n    useOpenGLOrientationForUV = value;\r\n}\r\n\r\n/**\r\n * Options used to control default behaviors regarding compatibility support\r\n * @deprecated please use named exports\r\n */\r\nexport const CompatibilityOptions = {\r\n    /* eslint-disable @typescript-eslint/naming-convention */\r\n    get UseOpenGLOrientationForUV() {\r\n        return useOpenGLOrientationForUV;\r\n    },\r\n    set UseOpenGLOrientationForUV(value) {\r\n        useOpenGLOrientationForUV = value;\r\n    },\r\n    /* eslint-enable @typescript-eslint/naming-convention */\r\n};\r\n"]}