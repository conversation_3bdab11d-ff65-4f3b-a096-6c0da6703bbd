import type { Sound } from "./sound.js";
import type { Analyser } from "./analyser.js";
import type { Nullable } from "../types.js";
import type { Scene } from "../scene.js";
/**
 * Options allowed during the creation of a sound track.
 */
export interface ISoundTrackOptions {
    /**
     * The volume the sound track should take during creation
     */
    volume?: number;
    /**
     * Define if the sound track is the main sound track of the scene
     */
    mainTrack?: boolean;
}
/**
 * It could be useful to isolate your music & sounds on several tracks to better manage volume on a grouped instance of sounds.
 * It will be also used in a future release to apply effects on a specific track.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-sound-tracks
 */
export declare class SoundTrack {
    /**
     * The unique identifier of the sound track in the scene.
     */
    id: number;
    /**
     * The list of sounds included in the sound track.
     */
    soundCollection: Array<Sound>;
    private _outputAudioNode;
    private _scene;
    private _connectedAnalyser;
    private _options;
    private _isInitialized;
    /**
     * Creates a new sound track.
     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-sound-tracks
     * @param scene Define the scene the sound track belongs to
     * @param options
     */
    constructor(scene?: Nullable<Scene>, options?: ISoundTrackOptions);
    private _initializeSoundTrackAudioGraph;
    /**
     * Release the sound track and its associated resources
     */
    dispose(): void;
    /**
     * Adds a sound to this sound track
     * @param sound define the sound to add
     * @ignoreNaming
     */
    addSound(sound: Sound): void;
    /**
     * Removes a sound to this sound track
     * @param sound define the sound to remove
     * @ignoreNaming
     */
    removeSound(sound: Sound): void;
    /**
     * Set a global volume for the full sound track.
     * @param newVolume Define the new volume of the sound track
     */
    setVolume(newVolume: number): void;
    /**
     * Switch the panning model to HRTF:
     * Renders a stereo output of higher quality than equalpower — it uses a convolution with measured impulse responses from human subjects.
     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound
     */
    switchPanningModelToHRTF(): void;
    /**
     * Switch the panning model to Equal Power:
     * Represents the equal-power panning algorithm, generally regarded as simple and efficient. equalpower is the default value.
     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound
     */
    switchPanningModelToEqualPower(): void;
    /**
     * Connect the sound track to an audio analyser allowing some amazing
     * synchronization between the sounds/music and your visualization (VuMeter for instance).
     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser
     * @param analyser The analyser to connect to the engine
     */
    connectToAnalyser(analyser: Analyser): void;
}
