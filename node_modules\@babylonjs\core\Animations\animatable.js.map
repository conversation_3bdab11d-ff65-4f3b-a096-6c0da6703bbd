{"version": 3, "file": "animatable.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Animations/animatable.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAErC,OAAO,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAG3D,OAAO,EAAE,KAAK,EAAE,oBAAmB;AAEnC,cAAc,mBAAmB,CAAC;AAgMlC,sBAAsB;AACtB,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Vector3 } from \"../Maths/math.vector\";\r\nimport { Bone } from \"../Bones/bone\";\r\nimport type { Node } from \"../node\";\r\nimport { AddAnimationExtensions } from \"./animatable.core\";\r\nimport type { Animatable } from \"./animatable.core\";\r\nimport type { Animation } from \"./animation\";\r\nimport { Scene } from \"core/scene\";\r\n\r\nexport * from \"./animatable.core\";\r\n\r\ndeclare module \"../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /**\r\n         * Sort active animatables based on their playOrder property\r\n         */\r\n        sortActiveAnimatables(): void;\r\n\r\n        /**\r\n         * Will start the animation sequence of a given target\r\n         * @param target defines the target\r\n         * @param from defines from which frame should animation start\r\n         * @param to defines until which frame should animation run.\r\n         * @param weight defines the weight to apply to the animation (1.0 by default)\r\n         * @param loop defines if the animation loops\r\n         * @param speedRatio defines the speed in which to run the animation (1.0 by default)\r\n         * @param onAnimationEnd defines the function to be executed when the animation ends\r\n         * @param animatable defines an animatable object. If not provided a new one will be created from the given params\r\n         * @param targetMask defines if the target should be animated if animations are present (this is called recursively on descendant animatables regardless of return value)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the animatable object created for this animation\r\n         */\r\n        beginWeightedAnimation(\r\n            target: any,\r\n            from: number,\r\n            to: number,\r\n            weight: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            animatable?: Animatable,\r\n            targetMask?: (target: any) => boolean,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable;\r\n\r\n        /**\r\n         * Will start the animation sequence of a given target\r\n         *\r\n         * Note that it is possible that the value(s) of speedRatio from and to will be changed if the animation is inverted\r\n         * @param target defines the target\r\n         * @param from defines from which frame should animation start\r\n         * @param to defines until which frame should animation run.\r\n         * @param loop defines if the animation loops\r\n         * @param speedRatio defines the speed in which to run the animation (1.0 by default)\r\n         * @param onAnimationEnd defines the function to be executed when the animation ends\r\n         * @param animatable defines an animatable object. If not provided a new one will be created from the given params\r\n         * @param stopCurrent defines if the current animations must be stopped first (true by default)\r\n         * @param targetMask defines if the target should be animate if animations are present (this is called recursively on descendant animatables regardless of return value)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the animatable object created for this animation\r\n         */\r\n        beginAnimation(\r\n            target: any,\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            animatable?: Animatable,\r\n            stopCurrent?: boolean,\r\n            targetMask?: (target: any) => boolean,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable;\r\n\r\n        /**\r\n         * Will start the animation sequence of a given target and its hierarchy\r\n         * @param target defines the target\r\n         * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used.\r\n         * @param from defines from which frame should animation start\r\n         * @param to defines until which frame should animation run.\r\n         * @param loop defines if the animation loops\r\n         * @param speedRatio defines the speed in which to run the animation (1.0 by default)\r\n         * @param onAnimationEnd defines the function to be executed when the animation ends\r\n         * @param animatable defines an animatable object. If not provided a new one will be created from the given params\r\n         * @param stopCurrent defines if the current animations must be stopped first (true by default)\r\n         * @param targetMask defines if the target should be animated if animations are present (this is called recursively on descendant animatables regardless of return value)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the list of created animatables\r\n         */\r\n        beginHierarchyAnimation(\r\n            target: any,\r\n            directDescendantsOnly: boolean,\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            animatable?: Animatable,\r\n            stopCurrent?: boolean,\r\n            targetMask?: (target: any) => boolean,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable[];\r\n\r\n        /**\r\n         * Begin a new animation on a given node\r\n         *\r\n         * Note that it is possible that the value(s) of speedRatio from and to will be changed if the animation is inverted\r\n         * @param target defines the target where the animation will take place\r\n         * @param animations defines the list of animations to start\r\n         * @param from defines the initial value\r\n         * @param to defines the final value\r\n         * @param loop defines if you want animation to loop (off by default)\r\n         * @param speedRatio defines the speed ratio to apply to all animations\r\n         * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the list of created animatables\r\n         */\r\n        beginDirectAnimation(\r\n            target: any,\r\n            animations: Animation[],\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable;\r\n\r\n        /**\r\n         * Begin a new animation on a given node and its hierarchy\r\n         * @param target defines the root node where the animation will take place\r\n         * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used.\r\n         * @param animations defines the list of animations to start\r\n         * @param from defines the initial value\r\n         * @param to defines the final value\r\n         * @param loop defines if you want animation to loop (off by default)\r\n         * @param speedRatio defines the speed ratio to apply to all animations\r\n         * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the list of animatables created for all nodes\r\n         */\r\n        beginDirectHierarchyAnimation(\r\n            target: Node,\r\n            directDescendantsOnly: boolean,\r\n            animations: Animation[],\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable[];\r\n\r\n        /**\r\n         * Gets the animatable associated with a specific target\r\n         * @param target defines the target of the animatable\r\n         * @returns the required animatable if found\r\n         */\r\n        getAnimatableByTarget(target: any): Nullable<Animatable>;\r\n\r\n        /**\r\n         * Gets all animatables associated with a given target\r\n         * @param target defines the target to look animatables for\r\n         * @returns an array of Animatables\r\n         */\r\n        getAllAnimatablesByTarget(target: any): Array<Animatable>;\r\n\r\n        /**\r\n         * Stops and removes all animations that have been applied to the scene\r\n         */\r\n        stopAllAnimations(): void;\r\n    }\r\n}\r\n\r\ndeclare module \"../Bones/bone\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Bone {\r\n        /**\r\n         * Copy an animation range from another bone\r\n         * @param source defines the source bone\r\n         * @param rangeName defines the range name to copy\r\n         * @param frameOffset defines the frame offset\r\n         * @param rescaleAsRequired defines if rescaling must be applied if required\r\n         * @param skelDimensionsRatio defines the scaling ratio\r\n         * @returns true if operation was successful\r\n         */\r\n        copyAnimationRange(source: Bone, rangeName: string, frameOffset: number, rescaleAsRequired: boolean, skelDimensionsRatio: Nullable<Vector3>): boolean;\r\n    }\r\n}\r\n\r\n// Connect everything!\r\nAddAnimationExtensions(Scene, Bone);\r\n"]}