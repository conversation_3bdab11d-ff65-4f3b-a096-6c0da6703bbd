{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/src/scripts/babylon-particles.ts"], "sourcesContent": ["import {\n  Engine,\n  Scene,\n  FreeCamera,\n  ArcRotateCamera,\n  Vector3,\n  Color3,\n  Color4,\n  ParticleSystem,\n  Texture,\n  ShaderMaterial,\n  Effect,\n  VertexBuffer,\n  Mesh,\n  MeshBuilder,\n  StandardMaterial,\n  PBRMaterial,\n  PBRMetallicRoughnessMaterial,\n  DynamicTexture,\n  Constants,\n  DirectionalLight,\n  SpotLight,\n  PointLight,\n  ShadowGenerator,\n  CubeTexture,\n  HDRCubeTexture,\n  EnvironmentHelper,\n  Animation,\n  AnimationGroup,\n  TransformNode,\n  NodeMaterial,\n  InputBlock,\n  VertexOutputBlock,\n  FragmentOutputBlock,\n  MultiplyBlock,\n  AddBlock,\n  TextureBlock,\n  VectorSplitterBlock,\n  VectorMergerBlock,\n  TrigonometryBlock,\n  TrigonometryBlockOperations,\n  FxaaPostProcess,\n  PostProcess,\n  HemisphericLight,\n  // Advanced PBR and Post-Processing imports\n  DefaultRenderingPipeline,\n  ScreenSpaceReflectionPostProcess,\n  // BloomPostProcess, // Not available in current version\n  // ChromaticAberrationPostProcess, // Not available in current version\n  // GrainPostProcess, // Not available in current version\n  // VignettePostProcess, // Not available in current version\n  // TonemappingPostProcess, // Not available in current version\n  // Advanced Materials\n  ProceduralTexture,\n  NoiseProceduralTexture,\n  // Physics and Advanced Features\n  PhysicsImpostor,\n  CannonJSPlugin,\n  // Audio\n  Sound,\n  Analyser,\n  // Advanced Lighting\n  // ImageBasedLighting, // Not available in current version\n  // WebXR\n  WebXRDefaultExperience,\n  WebXRFeatureName,\n  // Node Material System\n  NodeMaterialSystemValues,\n  TransformBlock,\n  // Advanced Math\n  Matrix,\n  Quaternion,\n  // Utilities\n  Tools,\n  SceneLoader,\n} from \"@babylonjs/core\";\n\n// Import additional Babylon.js modules for advanced features\nimport \"@babylonjs/core/Materials/Node/Blocks\";\nimport \"@babylonjs/loaders/glTF\";\nimport \"@babylonjs/materials/custom\";\nimport \"@babylonjs/post-processes\";\n// Advanced post-processing manager with professional effects\nclass BabylonPostProcessingManager {\n  scene: any;\n  camera: any;\n  engine: any;\n  renderingPipeline: DefaultRenderingPipeline | null = null;\n  ssrPostProcess: ScreenSpaceReflectionPostProcess | null = null;\n  // bloomPostProcess: BloomPostProcess | null = null; // Not available\n  // chromaticAberrationPostProcess: ChromaticAberrationPostProcess | null = null; // Not available\n  // grainPostProcess: GrainPostProcess | null = null; // Not available\n  // vignettePostProcess: VignettePostProcess | null = null; // Not available\n\n  constructor(scene: any, camera: any) {\n    this.scene = scene;\n    this.camera = camera;\n    this.engine = scene.getEngine();\n    this.initializeAdvancedPipeline();\n  }\n\n  initializeAdvancedPipeline() {\n    try {\n      // Create default rendering pipeline with advanced features\n      this.renderingPipeline = new DefaultRenderingPipeline(\n        \"defaultPipeline\",\n        true, // HDR enabled\n        this.scene,\n        [this.camera]\n      );\n\n      // Configure advanced post-processing effects\n      if (this.renderingPipeline) {\n        // Enable and configure bloom\n        this.renderingPipeline.bloomEnabled = true;\n        this.renderingPipeline.bloomThreshold = 0.8;\n        this.renderingPipeline.bloomWeight = 0.3;\n        this.renderingPipeline.bloomKernel = 64;\n        this.renderingPipeline.bloomScale = 0.5;\n\n        // Enable and configure tone mapping\n        this.renderingPipeline.toneMappingEnabled = true;\n        this.renderingPipeline.toneMappingType =\n          TonemappingPostProcess.TONEMAP_ACES;\n\n        // Enable FXAA anti-aliasing\n        this.renderingPipeline.fxaaEnabled = true;\n\n        // Enable and configure chromatic aberration\n        this.renderingPipeline.chromaticAberrationEnabled = true;\n        this.renderingPipeline.chromaticAberration.aberrationAmount = 30;\n        this.renderingPipeline.chromaticAberration.radialIntensity = 1.0;\n\n        // Enable and configure grain effect\n        this.renderingPipeline.grainEnabled = true;\n        this.renderingPipeline.grain.intensity = 10;\n        this.renderingPipeline.grain.animated = true;\n\n        // Enable and configure vignette\n        this.renderingPipeline.vignetteEnabled = true;\n        this.renderingPipeline.vignette.vignetteStretch = 0.2;\n        this.renderingPipeline.vignette.vignetteCentreX = 0.5;\n        this.renderingPipeline.vignette.vignetteCentreY = 0.5;\n        this.renderingPipeline.vignette.vignetteWeight = 1.5;\n        this.renderingPipeline.vignette.vignetteColor = new Color4(0, 0, 0, 0);\n\n        // Enable depth of field for cinematic effect\n        this.renderingPipeline.depthOfFieldEnabled = true;\n        this.renderingPipeline.depthOfFieldBlurLevel = 0;\n        this.renderingPipeline.depthOfField.focusDistance = 2000;\n        this.renderingPipeline.depthOfField.focalLength = 50;\n        this.renderingPipeline.depthOfField.fStop = 1.4;\n\n        // Enable screen space reflections for advanced materials\n        this.renderingPipeline.screenSpaceReflectionsEnabled = true;\n        if (this.renderingPipeline.screenSpaceReflectionPostProcess) {\n          this.renderingPipeline.screenSpaceReflectionPostProcess.strength = 0.5;\n          this.renderingPipeline.screenSpaceReflectionPostProcess.reflectionSpecularFalloffExponent = 1.0;\n          this.renderingPipeline.screenSpaceReflectionPostProcess.maxDistance = 1000;\n        }\n      }\n\n      console.log(\"Advanced post-processing pipeline initialized successfully\");\n    } catch (error) {\n      console.error(\"Error initializing advanced post-processing:\", error);\n    }\n  }\n\n  updateSettings(settings: any) {\n    if (this.renderingPipeline) {\n      // Dynamic adjustment of post-processing effects\n      if (settings.bloomIntensity !== undefined) {\n        this.renderingPipeline.bloomWeight = settings.bloomIntensity;\n      }\n      if (settings.chromaticAberration !== undefined) {\n        this.renderingPipeline.chromaticAberration.aberrationAmount =\n          settings.chromaticAberration;\n      }\n      if (settings.grainIntensity !== undefined) {\n        this.renderingPipeline.grain.intensity = settings.grainIntensity;\n      }\n      if (settings.vignetteStrength !== undefined) {\n        this.renderingPipeline.vignette.vignetteWeight =\n          settings.vignetteStrength;\n      }\n    }\n  }\n\n  dispose() {\n    if (this.renderingPipeline) {\n      this.renderingPipeline.dispose();\n      this.renderingPipeline = null;\n    }\n    if (this.ssrPostProcess) {\n      this.ssrPostProcess.dispose();\n      this.ssrPostProcess = null;\n    }\n    if (this.bloomPostProcess) {\n      this.bloomPostProcess.dispose();\n      this.bloomPostProcess = null;\n    }\n    if (this.chromaticAberrationPostProcess) {\n      this.chromaticAberrationPostProcess.dispose();\n      this.chromaticAberrationPostProcess = null;\n    }\n    if (this.grainPostProcess) {\n      this.grainPostProcess.dispose();\n      this.grainPostProcess = null;\n    }\n    if (this.vignettePostProcess) {\n      this.vignettePostProcess.dispose();\n      this.vignettePostProcess = null;\n    }\n  }\n}\n\nclass BabylonParticleSystem {\n  canvas: any;\n  engine: any;\n  scene: any;\n  camera: any;\n  particleSystem: any;\n  postProcessing: any;\n  mouse: any;\n  mouseTarget: any;\n  time: number;\n  colorSchemes: any;\n  lights: any;\n  interactiveObjects: any[];\n  animations: any[];\n  customShaders: any;\n  dynamicTextures: any[];\n  audioAnalyzer: any;\n\n  constructor(canvas: any) {\n    this.canvas = canvas;\n    this.engine = null;\n    this.scene = null;\n    this.camera = null;\n    this.particleSystem = null;\n    this.postProcessing = null;\n    this.mouse = { x: 0, y: 0 };\n    this.mouseTarget = { x: 0, y: 0 };\n    this.time = 0;\n    this.colorSchemes = {\n      cyberpunk: {\n        primary: new Color4(0, 1, 1, 1), // Cyan\n        secondary: new Color4(1, 0, 1, 1), // Magenta\n        accent: new Color4(0, 1, 0, 1), // Green\n      },\n      matrix: {\n        primary: new Color4(0, 1, 0, 1), // Green\n        secondary: new Color4(0, 0.8, 0, 1), // Dark Green\n        accent: new Color4(0, 0.6, 0, 1), // Darker Green\n      },\n      neon: {\n        primary: new Color4(1, 0.4, 0, 1), // Orange\n        secondary: new Color4(0, 0.5, 1, 1), // Blue\n        accent: new Color4(0.5, 0, 1, 1), // Purple\n      },\n    };\n\n    // Initialize new properties\n    this.interactiveObjects = [];\n    this.animations = [];\n    this.customShaders = {};\n    this.dynamicTextures = [];\n    this.audioAnalyzer = null;\n\n    this.init();\n  }\n\n  async init() {\n    try {\n      // Create Babylon.js engine\n      this.engine = new Engine(this.canvas, true, {\n        preserveDrawingBuffer: true,\n        stencil: true,\n        antialias: true,\n        alpha: true,\n        premultipliedAlpha: false,\n      });\n\n      // Create scene\n      this.scene = new Scene(this.engine);\n      this.scene.clearColor = new Color4(0, 0, 0, 0); // Transparent background\n\n      // Create camera\n      this.camera = new FreeCamera(\n        \"camera\",\n        new Vector3(0, 0, -10),\n        this.scene\n      );\n      this.camera.setTarget(Vector3.Zero());\n\n      // Setup advanced lighting\n      this.setupAdvancedLighting();\n\n      // Create particle system\n      await this.createParticleSystem();\n\n      // Create post-processing pipeline\n      this.postProcessing = new BabylonPostProcessingManager(\n        this.scene,\n        this.camera\n      );\n\n      // Create interactive 3D objects\n      this.createInteractiveObjects();\n\n      // Create dynamic textures\n      this.createDynamicTextures();\n\n      // Create advanced post-processing\n      this.createAdvancedPostProcessing();\n\n      // Add mouse interaction\n      this.addMouseInteraction();\n\n      // Add event listeners\n      this.addEventListeners();\n\n      // Start render loop\n      this.startRenderLoop();\n\n      console.log(\"Babylon.js particle system initialized successfully\");\n    } catch (error) {\n      console.error(\"Error initializing Babylon.js particle system:\", error);\n    }\n  }\n\n  setupAdvancedLighting() {\n    // Main directional light (key light)\n    const directionalLight = new DirectionalLight(\n      \"directionalLight\",\n      new Vector3(-1, -1, -1),\n      this.scene\n    );\n    directionalLight.intensity = 1.2;\n    directionalLight.diffuse = new Color3(0.8, 0.9, 1.0);\n\n    // Rim light for edge definition\n    const rimLight = new DirectionalLight(\n      \"rimLight\",\n      new Vector3(1, 0.5, 1),\n      this.scene\n    );\n    rimLight.intensity = 0.6;\n    rimLight.diffuse = new Color3(0, 1, 1);\n\n    // Accent spot light\n    const spotLight = new SpotLight(\n      \"spotLight\",\n      new Vector3(0, 30, 0),\n      new Vector3(0, -1, 0),\n      Math.PI / 3,\n      2,\n      this.scene\n    );\n    spotLight.intensity = 0.8;\n    spotLight.diffuse = new Color3(1, 0, 1);\n\n    // Ambient point lights for atmosphere\n    const pointLight1 = new PointLight(\n      \"pointLight1\",\n      new Vector3(-20, 10, -20),\n      this.scene\n    );\n    pointLight1.intensity = 0.4;\n    pointLight1.diffuse = new Color3(0, 1, 0);\n\n    const pointLight2 = new PointLight(\n      \"pointLight2\",\n      new Vector3(20, 10, 20),\n      this.scene\n    );\n    pointLight2.intensity = 0.4;\n    pointLight2.diffuse = new Color3(1, 0.5, 0);\n\n    // Store lights for animation\n    this.lights = {\n      directional: directionalLight,\n      rim: rimLight,\n      spot: spotLight,\n      point1: pointLight1,\n      point2: pointLight2,\n    };\n  }\n\n  async createParticleSystem() {\n    // Create custom particle texture\n    const particleTexture = this.createParticleTexture();\n\n    // Create particle system\n    this.particleSystem = new ParticleSystem(\"particles\", 2000, this.scene);\n    this.particleSystem.particleTexture = particleTexture;\n\n    // Set emitter\n    this.particleSystem.emitter = Vector3.Zero();\n\n    // Particle properties\n    this.particleSystem.minEmitBox = new Vector3(-50, -50, -50);\n    this.particleSystem.maxEmitBox = new Vector3(50, 50, 50);\n\n    // Colors\n    this.particleSystem.color1 = this.colorSchemes.cyberpunk.primary;\n    this.particleSystem.color2 = this.colorSchemes.cyberpunk.secondary;\n    this.particleSystem.colorDead = new Color4(0, 0, 0, 0);\n\n    // Size\n    this.particleSystem.minSize = 0.1;\n    this.particleSystem.maxSize = 2.0;\n\n    // Life time\n    this.particleSystem.minLifeTime = 2.0;\n    this.particleSystem.maxLifeTime = 8.0;\n\n    // Emission rate\n    this.particleSystem.emitRate = 100;\n\n    // Blend mode\n    this.particleSystem.blendMode = ParticleSystem.BLENDMODE_ONEONE;\n\n    // Direction\n    this.particleSystem.direction1 = new Vector3(-1, -1, -1);\n    this.particleSystem.direction2 = new Vector3(1, 1, 1);\n\n    // Angular speed\n    this.particleSystem.minAngularSpeed = 0;\n    this.particleSystem.maxAngularSpeed = Math.PI;\n\n    // Speed\n    this.particleSystem.minInitialRotation = 0;\n    this.particleSystem.maxInitialRotation = Math.PI;\n\n    // Gravity\n    this.particleSystem.gravity = new Vector3(0, -9.81, 0);\n\n    // Start the particle system\n    this.particleSystem.start();\n\n    // Create custom shader for advanced effects\n    await this.createCustomShader();\n  }\n\n  createParticleTexture() {\n    // Create a dynamic texture for particles\n    const texture = new DynamicTexture(\"particleTexture\", 64, this.scene);\n    const context = texture.getContext();\n\n    // Draw a glowing circle\n    const size = 64;\n    const center = size / 2;\n    const radius = size / 4;\n\n    context.fillStyle = \"rgba(0, 0, 0, 0)\";\n    context.fillRect(0, 0, size, size);\n\n    // Create gradient for glow effect\n    const gradient = context.createRadialGradient(\n      center,\n      center,\n      0,\n      center,\n      center,\n      radius\n    );\n    gradient.addColorStop(0, \"rgba(0, 255, 255, 1)\");\n    gradient.addColorStop(0.5, \"rgba(0, 255, 255, 0.5)\");\n    gradient.addColorStop(1, \"rgba(0, 255, 255, 0)\");\n\n    context.fillStyle = gradient;\n    context.beginPath();\n    context.arc(center, center, radius, 0, 2 * Math.PI);\n    context.fill();\n\n    texture.update();\n    return texture;\n  }\n\n  async createCustomShader() {\n    // Define custom vertex shader\n    const vertexShader = `\n      precision highp float;\n      \n      attribute vec3 position;\n      attribute vec2 uv;\n      attribute vec4 color;\n      attribute float age;\n      attribute float life;\n      attribute vec3 velocity;\n      \n      uniform mat4 view;\n      uniform mat4 projection;\n      uniform float time;\n      uniform vec2 mouse;\n      uniform vec2 resolution;\n      \n      varying vec2 vUV;\n      varying vec4 vColor;\n      varying float vAge;\n      \n      void main() {\n        vUV = uv;\n        vColor = color;\n        vAge = age / life;\n        \n        vec3 pos = position;\n        \n        // Wave animation\n        float wave1 = sin(time * 0.002 + position.x * 0.01) * 15.0;\n        float wave2 = cos(time * 0.003 + position.y * 0.008) * 10.0;\n        float wave3 = sin(time * 0.001 + position.z * 0.005) * 8.0;\n        \n        pos.y += wave1 + wave2;\n        pos.x += wave2 + wave3;\n        pos.z += wave1 + wave3;\n        \n        // Mouse interaction\n        vec2 mouseInfluence = (mouse - 0.5) * 2.0;\n        float mouseDistance = length(mouseInfluence);\n        float influence = 1.0 / (1.0 + mouseDistance * 0.1);\n        \n        pos.xy += mouseInfluence * influence * 50.0;\n        \n        gl_Position = projection * view * vec4(pos, 1.0);\n        gl_PointSize = 2.0 * (1.0 - vAge) + 1.0;\n      }\n    `;\n\n    // Define custom fragment shader\n    const fragmentShader = `\n      precision highp float;\n      \n      uniform sampler2D textureSampler;\n      uniform float time;\n      \n      varying vec2 vUV;\n      varying vec4 vColor;\n      varying float vAge;\n      \n      void main() {\n        vec4 textureColor = texture2D(textureSampler, vUV);\n        \n        // Fade out over lifetime\n        float alpha = (1.0 - vAge) * textureColor.a;\n        \n        // Pulsing effect\n        alpha *= 0.8 + 0.2 * sin(time * 0.005);\n        \n        gl_FragColor = vec4(vColor.rgb * textureColor.rgb, alpha);\n      }\n    `;\n\n    // Register the shader\n    Effect.ShadersStore[\"customParticleVertexShader\"] = vertexShader;\n    Effect.ShadersStore[\"customParticleFragmentShader\"] = fragmentShader;\n  }\n\n  addEventListeners() {\n    // Resize handler\n    window.addEventListener(\"resize\", () => this.onWindowResize(), false);\n\n    // Mouse move handler\n    document.addEventListener(\n      \"mousemove\",\n      (event) => this.onMouseMove(event),\n      false\n    );\n\n    // Touch move handler\n    document.addEventListener(\n      \"touchmove\",\n      (event) => this.onTouchMove(event),\n      false\n    );\n  }\n\n  onWindowResize() {\n    if (this.engine) {\n      this.engine.resize();\n    }\n  }\n\n  onMouseMove(event: any) {\n    this.mouseTarget.x = (event.clientX / window.innerWidth) * 2 - 1;\n    this.mouseTarget.y = -(event.clientY / window.innerHeight) * 2 + 1;\n  }\n\n  onTouchMove(event: any) {\n    if (event.touches.length > 0) {\n      const touch = event.touches[0];\n      this.mouseTarget.x = (touch.clientX / window.innerWidth) * 2 - 1;\n      this.mouseTarget.y = -(touch.clientY / window.innerHeight) * 2 + 1;\n    }\n  }\n\n  startRenderLoop() {\n    this.engine.runRenderLoop(() => {\n      this.update();\n      this.scene.render();\n    });\n  }\n\n  update() {\n    this.time += this.engine.getDeltaTime();\n\n    // Smooth mouse interpolation\n    this.mouse.x += (this.mouseTarget.x - this.mouse.x) * 0.05;\n    this.mouse.y += (this.mouseTarget.y - this.mouse.y) * 0.05;\n\n    // Update particle system properties\n    if (this.particleSystem) {\n      // Dynamic color changes\n      const colorPhase = Math.sin(this.time * 0.001) * 0.5 + 0.5;\n      this.particleSystem.color1 = Color4.Lerp(\n        this.colorSchemes.cyberpunk.primary,\n        this.colorSchemes.cyberpunk.secondary,\n        colorPhase\n      );\n    }\n\n    // Animate lights for dynamic atmosphere\n    if (this.lights) {\n      const lightTime = this.time * 0.0005;\n\n      // Animate spot light intensity\n      this.lights.spot.intensity = 0.8 + Math.sin(lightTime * 3) * 0.3;\n\n      // Rotate point lights\n      const radius = 25;\n      this.lights.point1.position.x = Math.cos(lightTime) * radius;\n      this.lights.point1.position.z = Math.sin(lightTime) * radius;\n\n      this.lights.point2.position.x = Math.cos(lightTime + Math.PI) * radius;\n      this.lights.point2.position.z = Math.sin(lightTime + Math.PI) * radius;\n\n      // Pulse point light intensities\n      this.lights.point1.intensity = 0.4 + Math.sin(lightTime * 2) * 0.2;\n      this.lights.point2.intensity = 0.4 + Math.cos(lightTime * 2.5) * 0.2;\n    }\n  }\n\n  setColorScheme(scheme: any) {\n    if (this.colorSchemes[scheme] && this.particleSystem) {\n      this.particleSystem.color1 = this.colorSchemes[scheme].primary;\n      this.particleSystem.color2 = this.colorSchemes[scheme].secondary;\n    }\n  }\n\n  createInteractiveObjects() {\n    if (!this.scene) return;\n\n    // Create advanced floating geometric shapes with PBR materials\n    const shapes = [\n      {\n        type: \"box\",\n        position: new Vector3(-15, 5, 0),\n        materialType: \"metallic\",\n      },\n      {\n        type: \"sphere\",\n        position: new Vector3(0, 8, -10),\n        materialType: \"glass\",\n      },\n      {\n        type: \"cylinder\",\n        position: new Vector3(15, 3, 5),\n        materialType: \"iridescent\",\n      },\n      {\n        type: \"torus\",\n        position: new Vector3(-8, -5, 8),\n        materialType: \"clearcoat\",\n      },\n      {\n        type: \"dodecahedron\",\n        position: new Vector3(12, -3, -8),\n        materialType: \"anisotropic\",\n      },\n      {\n        type: \"octahedron\",\n        position: new Vector3(-12, 6, 10),\n        materialType: \"subsurface\",\n      },\n    ];\n\n    shapes.forEach((shapeConfig, index) => {\n      let mesh: Mesh;\n\n      switch (shapeConfig.type) {\n        case \"box\":\n          mesh = MeshBuilder.CreateBox(\n            `interactiveBox${index}`,\n            { size: 2 },\n            this.scene\n          );\n          break;\n        case \"sphere\":\n          mesh = MeshBuilder.CreateSphere(\n            `interactiveSphere${index}`,\n            { diameter: 2.5, segments: 32 },\n            this.scene\n          );\n          break;\n        case \"cylinder\":\n          mesh = MeshBuilder.CreateCylinder(\n            `interactiveCylinder${index}`,\n            { height: 3, diameter: 2, tessellation: 16 },\n            this.scene\n          );\n          break;\n        case \"torus\":\n          mesh = MeshBuilder.CreateTorus(\n            `interactiveTorus${index}`,\n            { diameter: 3, thickness: 0.8, tessellation: 32 },\n            this.scene\n          );\n          break;\n        case \"dodecahedron\":\n          mesh = MeshBuilder.CreatePolyhedron(\n            `interactiveDodecahedron${index}`,\n            { type: 2, size: 1.5 },\n            this.scene\n          );\n          break;\n        case \"octahedron\":\n          mesh = MeshBuilder.CreatePolyhedron(\n            `interactiveOctahedron${index}`,\n            { type: 1, size: 1.8 },\n            this.scene\n          );\n          break;\n        default:\n          return;\n      }\n\n      mesh.position = shapeConfig.position;\n\n      // Create advanced PBR material based on type\n      const pbrMaterial = this.createAdvancedPBRMaterial(\n        `pbrMat${index}`,\n        shapeConfig.materialType\n      );\n      mesh.material = pbrMaterial;\n\n      // Create wireframe overlay for cyberpunk effect\n      const wireframeMaterial = new StandardMaterial(\n        `wireframeMat${index}`,\n        this.scene\n      );\n      wireframeMaterial.wireframe = true;\n      wireframeMaterial.emissiveColor = new Color3(1, 0, 1);\n      wireframeMaterial.alpha = 0.3;\n\n      // Create wireframe clone\n      const wireframeMesh = mesh.clone(`wireframe${index}`);\n      wireframeMesh.material = wireframeMaterial;\n      wireframeMesh.scaling = new Vector3(1.05, 1.05, 1.05);\n\n      // Add complex floating animation with multiple axes\n      const floatAnimationY = Animation.CreateAndStartAnimation(\n        `floatY${index}`,\n        mesh,\n        \"position.y\",\n        30,\n        120 + index * 10,\n        mesh.position.y,\n        mesh.position.y + 2 + Math.sin(index) * 0.5,\n        Animation.ANIMATIONLOOPMODE_YOYO\n      );\n\n      const floatAnimationX = Animation.CreateAndStartAnimation(\n        `floatX${index}`,\n        mesh,\n        \"position.x\",\n        30,\n        180 + index * 15,\n        mesh.position.x,\n        mesh.position.x + Math.cos(index) * 1.5,\n        Animation.ANIMATIONLOOPMODE_YOYO\n      );\n\n      // Add complex rotation animation\n      const rotateAnimation = Animation.CreateAndStartAnimation(\n        `rotate${index}`,\n        mesh,\n        \"rotation\",\n        30,\n        300 + index * 20,\n        Vector3.Zero(),\n        new Vector3(\n          Math.PI * 2 * (1 + index * 0.1),\n          Math.PI * 2 * (1 + index * 0.15),\n          Math.PI * 2 * (1 + index * 0.05)\n        ),\n        Animation.ANIMATIONLOOPMODE_CYCLE\n      );\n\n      // Add scaling pulse animation\n      const scaleAnimation = Animation.CreateAndStartAnimation(\n        `scale${index}`,\n        mesh,\n        \"scaling\",\n        30,\n        90 + index * 5,\n        new Vector3(1, 1, 1),\n        new Vector3(1.1, 1.1, 1.1),\n        Animation.ANIMATIONLOOPMODE_YOYO\n      );\n\n      // Store references\n      this.interactiveObjects.push({\n        mesh,\n        wireframeMesh,\n        material: pbrMaterial,\n        wireframeMaterial,\n        originalPosition: shapeConfig.position.clone(),\n        floatAnimationY,\n        floatAnimationX,\n        rotateAnimation,\n        scaleAnimation,\n        materialType: shapeConfig.materialType,\n      });\n    });\n  }\n\n  createAdvancedPBRMaterial(name: string, materialType: string): PBRMaterial {\n    const pbr = new PBRMaterial(name, this.scene);\n\n    // Create environment texture for reflections\n    const envTexture = this.createEnvironmentTexture();\n    pbr.environmentTexture = envTexture;\n\n    switch (materialType) {\n      case \"metallic\":\n        // Highly reflective metallic material\n        pbr.baseColor = new Color3(0.2, 0.6, 1.0);\n        pbr.metallic = 1.0;\n        pbr.roughness = 0.1;\n        pbr.enableSpecularAntiAliasing = true;\n        break;\n\n      case \"glass\":\n        // Refractive glass material\n        pbr.baseColor = new Color3(0.9, 0.95, 1.0);\n        pbr.metallic = 0.0;\n        pbr.roughness = 0.0;\n        pbr.alpha = 0.1;\n        pbr.indexOfRefraction = 1.52;\n        pbr.linkRefractionWithTransparency = true;\n        pbr.subSurface.isRefractionEnabled = true;\n        pbr.subSurface.refractionIntensity = 1.0;\n        break;\n\n      case \"iridescent\":\n        // Iridescent material with color shifting\n        pbr.baseColor = new Color3(0.1, 0.1, 0.1);\n        pbr.metallic = 1.0;\n        pbr.roughness = 0.0;\n        pbr.iridescence.isEnabled = true;\n        pbr.iridescence.intensity = 1.0;\n        pbr.iridescence.indexOfRefraction = 1.3;\n        pbr.iridescence.thickness = 400;\n        break;\n\n      case \"clearcoat\":\n        // Clear coat material for automotive-like finish\n        pbr.baseColor = new Color3(0.8, 0.1, 0.1);\n        pbr.metallic = 0.0;\n        pbr.roughness = 0.8;\n        pbr.clearCoat.isEnabled = true;\n        pbr.clearCoat.intensity = 1.0;\n        pbr.clearCoat.roughness = 0.1;\n        pbr.clearCoat.indexOfRefraction = 1.5;\n        break;\n\n      case \"anisotropic\":\n        // Anisotropic material for brushed metal effect\n        pbr.baseColor = new Color3(0.7, 0.7, 0.9);\n        pbr.metallic = 1.0;\n        pbr.roughness = 0.3;\n        pbr.anisotropy.isEnabled = true;\n        pbr.anisotropy.intensity = 1.0;\n        pbr.anisotropy.direction = new Vector3(1, 0, 0);\n        break;\n\n      case \"subsurface\":\n        // Subsurface scattering for organic materials\n        pbr.baseColor = new Color3(0.9, 0.7, 0.6);\n        pbr.metallic = 0.0;\n        pbr.roughness = 0.4;\n        pbr.subSurface.isTranslucencyEnabled = true;\n        pbr.subSurface.translucencyIntensity = 0.8;\n        pbr.subSurface.tintColor = new Color3(1.0, 0.8, 0.6);\n        break;\n\n      default:\n        // Default cyberpunk material\n        pbr.baseColor = new Color3(0, 1, 1);\n        pbr.metallic = 0.8;\n        pbr.roughness = 0.2;\n        pbr.emissiveColor = new Color3(0, 0.2, 0.2);\n        break;\n    }\n\n    return pbr;\n  }\n\n  createEnvironmentTexture(): CubeTexture {\n    // Create a simple procedural environment texture\n    const envTexture = CubeTexture.CreateFromImages(\n      [\n        this.createSkyTexture(\"px\"), // positive X\n        this.createSkyTexture(\"nx\"), // negative X\n        this.createSkyTexture(\"py\"), // positive Y\n        this.createSkyTexture(\"ny\"), // negative Y\n        this.createSkyTexture(\"pz\"), // positive Z\n        this.createSkyTexture(\"nz\"), // negative Z\n      ],\n      this.scene\n    );\n\n    return envTexture;\n  }\n\n  createSkyTexture(face: string): string {\n    // Create a simple gradient sky texture\n    const canvas = document.createElement(\"canvas\");\n    canvas.width = 512;\n    canvas.height = 512;\n    const ctx = canvas.getContext(\"2d\")!;\n\n    // Create gradient based on face\n    const gradient = ctx.createLinearGradient(0, 0, 0, 512);\n    gradient.addColorStop(0, \"#001122\");\n    gradient.addColorStop(0.5, \"#003366\");\n    gradient.addColorStop(1, \"#000011\");\n\n    ctx.fillStyle = gradient;\n    ctx.fillRect(0, 0, 512, 512);\n\n    // Add some stars\n    for (let i = 0; i < 100; i++) {\n      const x = Math.random() * 512;\n      const y = Math.random() * 512;\n      const brightness = Math.random();\n\n      ctx.fillStyle = `rgba(255, 255, 255, ${brightness})`;\n      ctx.fillRect(x, y, 1, 1);\n    }\n\n    return canvas.toDataURL();\n  }\n\n  createDynamicTextures() {\n    if (!this.scene) return;\n\n    // Create animated noise texture\n    const noiseTexture = new DynamicTexture(\n      \"noiseTexture\",\n      { width: 512, height: 512 },\n      this.scene\n    );\n    const noiseContext = noiseTexture.getContext();\n\n    // Generate noise pattern\n    const generateNoise = () => {\n      // Clear the canvas\n      noiseContext.fillStyle = \"rgba(0, 0, 0, 0)\";\n      noiseContext.fillRect(0, 0, 512, 512);\n\n      // Generate noise using canvas drawing\n      for (let i = 0; i < 1000; i++) {\n        const x = Math.random() * 512;\n        const y = Math.random() * 512;\n        const noise = Math.random();\n\n        noiseContext.fillStyle = `rgba(${noise * 25}, ${noise * 200}, ${\n          noise * 255\n        }, 0.8)`;\n        noiseContext.fillRect(x, y, 2, 2);\n      }\n\n      noiseTexture.update();\n    };\n\n    // Update noise every frame\n    this.scene.registerBeforeRender(() => {\n      if (Math.random() < 0.1) {\n        // Update 10% of frames for performance\n        generateNoise();\n      }\n    });\n\n    this.dynamicTextures.push(noiseTexture);\n\n    // Create holographic grid texture\n    const gridTexture = new DynamicTexture(\n      \"gridTexture\",\n      { width: 256, height: 256 },\n      this.scene\n    );\n    const gridContext = gridTexture.getContext();\n\n    const drawGrid = () => {\n      gridContext.fillStyle = \"rgba(0, 0, 0, 0)\";\n      gridContext.fillRect(0, 0, 256, 256);\n\n      gridContext.strokeStyle = \"rgba(0, 255, 255, 0.8)\";\n      gridContext.lineWidth = 2;\n\n      // Draw grid lines\n      for (let i = 0; i <= 256; i += 32) {\n        gridContext.beginPath();\n        gridContext.moveTo(i, 0);\n        gridContext.lineTo(i, 256);\n        gridContext.stroke();\n\n        gridContext.beginPath();\n        gridContext.moveTo(0, i);\n        gridContext.lineTo(256, i);\n        gridContext.stroke();\n      }\n\n      gridTexture.update();\n    };\n\n    drawGrid();\n    this.dynamicTextures.push(gridTexture);\n  }\n\n  createAdvancedPostProcessing() {\n    if (!this.scene || !this.camera) return;\n\n    // Create FXAA anti-aliasing\n    const fxaaPostProcess = new FxaaPostProcess(\"fxaa\", 1.0, this.camera);\n\n    // Create custom glitch effect\n    const glitchEffect = new PostProcess(\n      \"glitch\",\n      \"./shaders/glitch\", // We'll create this shader\n      [\"time\", \"intensity\"],\n      [\"textureSampler\"],\n      1.0,\n      this.camera\n    );\n\n    glitchEffect.onApply = (effect) => {\n      effect.setFloat(\"time\", this.time * 0.001);\n      effect.setFloat(\"intensity\", 0.1 + Math.sin(this.time * 0.005) * 0.05);\n    };\n\n    // Create chromatic aberration effect\n    const chromaticEffect = new PostProcess(\n      \"chromatic\",\n      \"./shaders/chromatic\",\n      [\"aberrationAmount\"],\n      [\"textureSampler\"],\n      1.0,\n      this.camera\n    );\n\n    chromaticEffect.onApply = (effect) => {\n      effect.setFloat(\n        \"aberrationAmount\",\n        0.002 + Math.sin(this.time * 0.003) * 0.001\n      );\n    };\n  }\n\n  addMouseInteraction() {\n    if (!this.scene || !this.canvas) return;\n\n    // Add mouse/touch interaction with 3D objects\n    this.scene.onPointerObservable.add((pointerInfo: any) => {\n      if (pointerInfo.pickInfo && pointerInfo.pickInfo.hit) {\n        const pickedMesh = pointerInfo.pickInfo.pickedMesh;\n\n        // Check if it's one of our interactive objects\n        const interactiveObj = this.interactiveObjects.find(\n          (obj) => obj.mesh === pickedMesh || obj.wireframeMesh === pickedMesh\n        );\n\n        if (interactiveObj) {\n          switch (pointerInfo.type) {\n            case 1: // POINTERDOWN\n              this.onObjectClick(interactiveObj);\n              break;\n            case 2: // POINTERUP\n              break;\n            case 4: // POINTERMOVE\n              this.onObjectHover(interactiveObj);\n              break;\n          }\n        }\n      }\n    });\n\n    // Add mouse move tracking for particle attraction\n    this.canvas.addEventListener(\"mousemove\", (event: any) => {\n      const rect = this.canvas.getBoundingClientRect();\n      this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;\n      this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;\n\n      // Smooth mouse target interpolation\n      this.mouseTarget.x += (this.mouse.x - this.mouseTarget.x) * 0.1;\n      this.mouseTarget.y += (this.mouse.y - this.mouseTarget.y) * 0.1;\n    });\n  }\n\n  onObjectClick(interactiveObj: any) {\n    // Create explosion effect\n    const explosionParticles = new ParticleSystem(\"explosion\", 100, this.scene);\n    explosionParticles.particleTexture = new DynamicTexture(\n      \"explosionTexture\",\n      64,\n      this.scene\n    );\n\n    // Set explosion properties\n    explosionParticles.emitter = interactiveObj.mesh;\n    explosionParticles.minEmitBox = new Vector3(-0.5, -0.5, -0.5);\n    explosionParticles.maxEmitBox = new Vector3(0.5, 0.5, 0.5);\n\n    explosionParticles.color1 = new Color4(1, 1, 0, 1);\n    explosionParticles.color2 = new Color4(1, 0.5, 0, 1);\n    explosionParticles.colorDead = new Color4(0, 0, 0, 0);\n\n    explosionParticles.minSize = 0.1;\n    explosionParticles.maxSize = 0.5;\n    explosionParticles.minLifeTime = 0.3;\n    explosionParticles.maxLifeTime = 1.0;\n\n    explosionParticles.emitRate = 200;\n    explosionParticles.minEmitPower = 5;\n    explosionParticles.maxEmitPower = 10;\n\n    explosionParticles.start();\n\n    // Stop explosion after short time\n    setTimeout(() => {\n      explosionParticles.stop();\n      setTimeout(() => explosionParticles.dispose(), 2000);\n    }, 200);\n\n    // Scale animation on click\n    const scaleUp = Animation.CreateAndStartAnimation(\n      \"scaleUp\",\n      interactiveObj.mesh,\n      \"scaling\",\n      30,\n      15,\n      interactiveObj.mesh.scaling,\n      interactiveObj.mesh.scaling.scale(1.3),\n      Animation.ANIMATIONLOOPMODE_CONSTANT\n    );\n\n    setTimeout(() => {\n      Animation.CreateAndStartAnimation(\n        \"scaleDown\",\n        interactiveObj.mesh,\n        \"scaling\",\n        30,\n        15,\n        interactiveObj.mesh.scaling,\n        new Vector3(1, 1, 1),\n        Animation.ANIMATIONLOOPMODE_CONSTANT\n      );\n    }, 500);\n  }\n\n  onObjectHover(interactiveObj: any) {\n    // Increase glow on hover\n    if (interactiveObj.material) {\n      interactiveObj.material.emissiveColor = new Color3(0.2, 1.2, 1.2);\n    }\n\n    // Reset glow after delay\n    setTimeout(() => {\n      if (interactiveObj.material) {\n        interactiveObj.material.emissiveColor = new Color3(0, 1, 1);\n      }\n    }, 200);\n  }\n\n  destroy() {\n    // Dispose interactive objects\n    this.interactiveObjects.forEach((obj) => {\n      if (obj.mesh) obj.mesh.dispose();\n      if (obj.wireframeMesh) obj.wireframeMesh.dispose();\n      if (obj.material) obj.material.dispose();\n      if (obj.wireframeMaterial) obj.wireframeMaterial.dispose();\n    });\n    this.interactiveObjects = [];\n\n    // Dispose dynamic textures\n    this.dynamicTextures.forEach((texture) => {\n      if (texture) texture.dispose();\n    });\n    this.dynamicTextures = [];\n\n    // Dispose animations\n    this.animations.forEach((animation) => {\n      if (animation) animation.dispose();\n    });\n    this.animations = [];\n\n    // Dispose post-processing\n    if (this.postProcessing) {\n      this.postProcessing.dispose();\n    }\n\n    // Dispose particle system\n    if (this.particleSystem) {\n      this.particleSystem.dispose();\n    }\n\n    // Dispose scene\n    if (this.scene) {\n      this.scene.dispose();\n    }\n\n    // Dispose engine\n    if (this.engine) {\n      this.engine.dispose();\n    }\n  }\n}\n\nexport default BabylonParticleSystem;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6EA,6DAA6D;AAC7D;AACA;AACA;AACA;;;;;;;AACA,6DAA6D;AAC7D,MAAM;IAkBJ,6BAA6B;QAC3B,IAAI;YACF,2DAA2D;YAC3D,IAAI,CAAC,iBAAiB,GAAG,IAAI,kNAAA,CAAA,2BAAwB,CACnD,mBACA,MACA,IAAI,CAAC,KAAK,EACV;gBAAC,IAAI,CAAC,MAAM;aAAC;YAGf,6CAA6C;YAC7C,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC1B,6BAA6B;gBAC7B,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG;gBACtC,IAAI,CAAC,iBAAiB,CAAC,cAAc,GAAG;gBACxC,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG;gBACrC,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG;gBACrC,IAAI,CAAC,iBAAiB,CAAC,UAAU,GAAG;gBAEpC,oCAAoC;gBACpC,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,GAAG;gBAC5C,IAAI,CAAC,iBAAiB,CAAC,eAAe,GACpC,uBAAuB,YAAY;gBAErC,4BAA4B;gBAC5B,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG;gBAErC,4CAA4C;gBAC5C,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,GAAG;gBACpD,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,gBAAgB,GAAG;gBAC9D,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,eAAe,GAAG;gBAE7D,oCAAoC;gBACpC,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG;gBACtC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,GAAG;gBACzC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,GAAG;gBAExC,gCAAgC;gBAChC,IAAI,CAAC,iBAAiB,CAAC,eAAe,GAAG;gBACzC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,eAAe,GAAG;gBAClD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,eAAe,GAAG;gBAClD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,eAAe,GAAG;gBAClD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,cAAc,GAAG;gBACjD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,aAAa,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;gBAEpE,6CAA6C;gBAC7C,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,GAAG;gBAC7C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,GAAG;gBAC/C,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,aAAa,GAAG;gBACpD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,WAAW,GAAG;gBAClD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,GAAG;gBAE5C,yDAAyD;gBACzD,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,GAAG;gBACvD,IAAI,IAAI,CAAC,iBAAiB,CAAC,gCAAgC,EAAE;oBAC3D,IAAI,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,QAAQ,GAAG;oBACnE,IAAI,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,iCAAiC,GAAG;oBAC5F,IAAI,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,WAAW,GAAG;gBACxE;YACF;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;QAChE;IACF;IAEA,eAAe,QAAa,EAAE;QAC5B,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,gDAAgD;YAChD,IAAI,SAAS,cAAc,KAAK,WAAW;gBACzC,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,SAAS,cAAc;YAC9D;YACA,IAAI,SAAS,mBAAmB,KAAK,WAAW;gBAC9C,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,gBAAgB,GACzD,SAAS,mBAAmB;YAChC;YACA,IAAI,SAAS,cAAc,KAAK,WAAW;gBACzC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,cAAc;YAClE;YACA,IAAI,SAAS,gBAAgB,KAAK,WAAW;gBAC3C,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,cAAc,GAC5C,SAAS,gBAAgB;YAC7B;QACF;IACF;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,CAAC,iBAAiB,CAAC,OAAO;YAC9B,IAAI,CAAC,iBAAiB,GAAG;QAC3B;QACA,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,OAAO;YAC3B,IAAI,CAAC,cAAc,GAAG;QACxB;QACA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAC7B,IAAI,CAAC,gBAAgB,GAAG;QAC1B;QACA,IAAI,IAAI,CAAC,8BAA8B,EAAE;YACvC,IAAI,CAAC,8BAA8B,CAAC,OAAO;YAC3C,IAAI,CAAC,8BAA8B,GAAG;QACxC;QACA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAC7B,IAAI,CAAC,gBAAgB,GAAG;QAC1B;QACA,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC,OAAO;YAChC,IAAI,CAAC,mBAAmB,GAAG;QAC7B;IACF;IA5HA,qEAAqE;IACrE,iGAAiG;IACjG,qEAAqE;IACrE,2EAA2E;IAE3E,YAAY,KAAU,EAAE,MAAW,CAAE;QAVrC,+KAAA,SAAA,KAAA;QACA,+KAAA,UAAA,KAAA;QACA,+KAAA,UAAA,KAAA;QACA,+KAAA,qBAAqD;QACrD,+KAAA,kBAA0D;QAOxD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG,MAAM,SAAS;QAC7B,IAAI,CAAC,0BAA0B;IACjC;AAmHF;AAEA,MAAM;IAwDJ,MAAM,OAAO;QACX,IAAI;YACF,2BAA2B;YAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,2JAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM;gBAC1C,uBAAuB;gBACvB,SAAS;gBACT,WAAW;gBACX,OAAO;gBACP,oBAAoB;YACtB;YAEA,eAAe;YACf,IAAI,CAAC,KAAK,GAAG,IAAI,+IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,MAAM;YAClC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG,IAAI,yBAAyB;YAEzE,gBAAgB;YAChB,IAAI,CAAC,MAAM,GAAG,IAAI,+JAAA,CAAA,aAAU,CAC1B,UACA,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,GAAG,CAAC,KACnB,IAAI,CAAC,KAAK;YAEZ,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,iKAAA,CAAA,UAAO,CAAC,IAAI;YAElC,0BAA0B;YAC1B,IAAI,CAAC,qBAAqB;YAE1B,yBAAyB;YACzB,MAAM,IAAI,CAAC,oBAAoB;YAE/B,kCAAkC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,6BACxB,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,MAAM;YAGb,gCAAgC;YAChC,IAAI,CAAC,wBAAwB;YAE7B,0BAA0B;YAC1B,IAAI,CAAC,qBAAqB;YAE1B,kCAAkC;YAClC,IAAI,CAAC,4BAA4B;YAEjC,wBAAwB;YACxB,IAAI,CAAC,mBAAmB;YAExB,sBAAsB;YACtB,IAAI,CAAC,iBAAiB;YAEtB,oBAAoB;YACpB,IAAI,CAAC,eAAe;YAEpB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;QAClE;IACF;IAEA,wBAAwB;QACtB,qCAAqC;QACrC,MAAM,mBAAmB,IAAI,oKAAA,CAAA,mBAAgB,CAC3C,oBACA,IAAI,iKAAA,CAAA,UAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IACrB,IAAI,CAAC,KAAK;QAEZ,iBAAiB,SAAS,GAAG;QAC7B,iBAAiB,OAAO,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,KAAK;QAEhD,gCAAgC;QAChC,MAAM,WAAW,IAAI,oKAAA,CAAA,mBAAgB,CACnC,YACA,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,KAAK,IACpB,IAAI,CAAC,KAAK;QAEZ,SAAS,SAAS,GAAG;QACrB,SAAS,OAAO,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG;QAEpC,oBAAoB;QACpB,MAAM,YAAY,IAAI,6JAAA,CAAA,YAAS,CAC7B,aACA,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,IAAI,IACnB,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,GAAG,IACnB,KAAK,EAAE,GAAG,GACV,GACA,IAAI,CAAC,KAAK;QAEZ,UAAU,SAAS,GAAG;QACtB,UAAU,OAAO,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG;QAErC,sCAAsC;QACtC,MAAM,cAAc,IAAI,8JAAA,CAAA,aAAU,CAChC,eACA,IAAI,iKAAA,CAAA,UAAO,CAAC,CAAC,IAAI,IAAI,CAAC,KACtB,IAAI,CAAC,KAAK;QAEZ,YAAY,SAAS,GAAG;QACxB,YAAY,OAAO,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG;QAEvC,MAAM,cAAc,IAAI,8JAAA,CAAA,aAAU,CAChC,eACA,IAAI,iKAAA,CAAA,UAAO,CAAC,IAAI,IAAI,KACpB,IAAI,CAAC,KAAK;QAEZ,YAAY,SAAS,GAAG;QACxB,YAAY,OAAO,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,KAAK;QAEzC,6BAA6B;QAC7B,IAAI,CAAC,MAAM,GAAG;YACZ,aAAa;YACb,KAAK;YACL,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,uBAAuB;QAC3B,iCAAiC;QACjC,MAAM,kBAAkB,IAAI,CAAC,qBAAqB;QAElD,yBAAyB;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,qKAAA,CAAA,iBAAc,CAAC,aAAa,MAAM,IAAI,CAAC,KAAK;QACtE,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG;QAEtC,cAAc;QACd,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,iKAAA,CAAA,UAAO,CAAC,IAAI;QAE1C,sBAAsB;QACtB,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QACxD,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAC,IAAI,IAAI;QAErD,SAAS;QACT,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO;QAChE,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS;QAClE,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;QAEpD,OAAO;QACP,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAC9B,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAE9B,YAAY;QACZ,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG;QAClC,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG;QAElC,gBAAgB;QAChB,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;QAE/B,aAAa;QACb,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,qKAAA,CAAA,iBAAc,CAAC,gBAAgB;QAE/D,YAAY;QACZ,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACtD,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,GAAG;QAEnD,gBAAgB;QAChB,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG;QACtC,IAAI,CAAC,cAAc,CAAC,eAAe,GAAG,KAAK,EAAE;QAE7C,QAAQ;QACR,IAAI,CAAC,cAAc,CAAC,kBAAkB,GAAG;QACzC,IAAI,CAAC,cAAc,CAAC,kBAAkB,GAAG,KAAK,EAAE;QAEhD,UAAU;QACV,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,MAAM;QAEpD,4BAA4B;QAC5B,IAAI,CAAC,cAAc,CAAC,KAAK;QAEzB,4CAA4C;QAC5C,MAAM,IAAI,CAAC,kBAAkB;IAC/B;IAEA,wBAAwB;QACtB,yCAAyC;QACzC,MAAM,UAAU,IAAI,iLAAA,CAAA,iBAAc,CAAC,mBAAmB,IAAI,IAAI,CAAC,KAAK;QACpE,MAAM,UAAU,QAAQ,UAAU;QAElC,wBAAwB;QACxB,MAAM,OAAO;QACb,MAAM,SAAS,OAAO;QACtB,MAAM,SAAS,OAAO;QAEtB,QAAQ,SAAS,GAAG;QACpB,QAAQ,QAAQ,CAAC,GAAG,GAAG,MAAM;QAE7B,kCAAkC;QAClC,MAAM,WAAW,QAAQ,oBAAoB,CAC3C,QACA,QACA,GACA,QACA,QACA;QAEF,SAAS,YAAY,CAAC,GAAG;QACzB,SAAS,YAAY,CAAC,KAAK;QAC3B,SAAS,YAAY,CAAC,GAAG;QAEzB,QAAQ,SAAS,GAAG;QACpB,QAAQ,SAAS;QACjB,QAAQ,GAAG,CAAC,QAAQ,QAAQ,QAAQ,GAAG,IAAI,KAAK,EAAE;QAClD,QAAQ,IAAI;QAEZ,QAAQ,MAAM;QACd,OAAO;IACT;IAEA,MAAM,qBAAqB;QACzB,8BAA8B;QAC9B,MAAM,eAAgB;QAgDtB,gCAAgC;QAChC,MAAM,iBAAkB;QAuBxB,sBAAsB;QACtB,6JAAA,CAAA,SAAM,CAAC,YAAY,CAAC,6BAA6B,GAAG;QACpD,6JAAA,CAAA,SAAM,CAAC,YAAY,CAAC,+BAA+B,GAAG;IACxD;IAEA,oBAAoB;QAClB,iBAAiB;QACjB,OAAO,gBAAgB,CAAC,UAAU,IAAM,IAAI,CAAC,cAAc,IAAI;QAE/D,qBAAqB;QACrB,SAAS,gBAAgB,CACvB,aACA,CAAC,QAAU,IAAI,CAAC,WAAW,CAAC,QAC5B;QAGF,qBAAqB;QACrB,SAAS,gBAAgB,CACvB,aACA,CAAC,QAAU,IAAI,CAAC,WAAW,CAAC,QAC5B;IAEJ;IAEA,iBAAiB;QACf,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,MAAM;QACpB;IACF;IAEA,YAAY,KAAU,EAAE;QACtB,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,AAAC,MAAM,OAAO,GAAG,OAAO,UAAU,GAAI,IAAI;QAC/D,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,OAAO,GAAG,OAAO,WAAW,IAAI,IAAI;IACnE;IAEA,YAAY,KAAU,EAAE;QACtB,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;YAC5B,MAAM,QAAQ,MAAM,OAAO,CAAC,EAAE;YAC9B,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,AAAC,MAAM,OAAO,GAAG,OAAO,UAAU,GAAI,IAAI;YAC/D,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,OAAO,GAAG,OAAO,WAAW,IAAI,IAAI;QACnE;IACF;IAEA,kBAAkB;QAChB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;YACxB,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,KAAK,CAAC,MAAM;QACnB;IACF;IAEA,SAAS;QACP,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;QAErC,6BAA6B;QAC7B,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI;QACtD,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI;QAEtD,oCAAoC;QACpC,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,wBAAwB;YACxB,MAAM,aAAa,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,MAAM;YACvD,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,gKAAA,CAAA,SAAM,CAAC,IAAI,CACtC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EACnC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,EACrC;QAEJ;QAEA,wCAAwC;QACxC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAM,YAAY,IAAI,CAAC,IAAI,GAAG;YAE9B,+BAA+B;YAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,KAAK,GAAG,CAAC,YAAY,KAAK;YAE7D,sBAAsB;YACtB,MAAM,SAAS;YACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,aAAa;YACtD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,aAAa;YAEtD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,YAAY,KAAK,EAAE,IAAI;YAChE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,YAAY,KAAK,EAAE,IAAI;YAEhE,gCAAgC;YAChC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,KAAK,GAAG,CAAC,YAAY,KAAK;YAC/D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,KAAK,GAAG,CAAC,YAAY,OAAO;QACnE;IACF;IAEA,eAAe,MAAW,EAAE;QAC1B,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;YACpD,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO;YAC9D,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS;QAClE;IACF;IAEA,2BAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;QAEjB,+DAA+D;QAC/D,MAAM,SAAS;YACb;gBACE,MAAM;gBACN,UAAU,IAAI,iKAAA,CAAA,UAAO,CAAC,CAAC,IAAI,GAAG;gBAC9B,cAAc;YAChB;YACA;gBACE,MAAM;gBACN,UAAU,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,GAAG,CAAC;gBAC7B,cAAc;YAChB;YACA;gBACE,MAAM;gBACN,UAAU,IAAI,iKAAA,CAAA,UAAO,CAAC,IAAI,GAAG;gBAC7B,cAAc;YAChB;YACA;gBACE,MAAM;gBACN,UAAU,IAAI,iKAAA,CAAA,UAAO,CAAC,CAAC,GAAG,CAAC,GAAG;gBAC9B,cAAc;YAChB;YACA;gBACE,MAAM;gBACN,UAAU,IAAI,iKAAA,CAAA,UAAO,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC/B,cAAc;YAChB;YACA;gBACE,MAAM;gBACN,UAAU,IAAI,iKAAA,CAAA,UAAO,CAAC,CAAC,IAAI,GAAG;gBAC9B,cAAc;YAChB;SACD;QAED,OAAO,OAAO,CAAC,CAAC,aAAa;YAC3B,IAAI;YAEJ,OAAQ,YAAY,IAAI;gBACtB,KAAK;oBACH,OAAO,+JAAA,CAAA,cAAW,CAAC,SAAS,CAC1B,AAAC,iBAAsB,OAAN,QACjB;wBAAE,MAAM;oBAAE,GACV,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,+JAAA,CAAA,cAAW,CAAC,YAAY,CAC7B,AAAC,oBAAyB,OAAN,QACpB;wBAAE,UAAU;wBAAK,UAAU;oBAAG,GAC9B,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,+JAAA,CAAA,cAAW,CAAC,cAAc,CAC/B,AAAC,sBAA2B,OAAN,QACtB;wBAAE,QAAQ;wBAAG,UAAU;wBAAG,cAAc;oBAAG,GAC3C,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,+JAAA,CAAA,cAAW,CAAC,WAAW,CAC5B,AAAC,mBAAwB,OAAN,QACnB;wBAAE,UAAU;wBAAG,WAAW;wBAAK,cAAc;oBAAG,GAChD,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,+JAAA,CAAA,cAAW,CAAC,gBAAgB,CACjC,AAAC,0BAA+B,OAAN,QAC1B;wBAAE,MAAM;wBAAG,MAAM;oBAAI,GACrB,IAAI,CAAC,KAAK;oBAEZ;gBACF,KAAK;oBACH,OAAO,+JAAA,CAAA,cAAW,CAAC,gBAAgB,CACjC,AAAC,wBAA6B,OAAN,QACxB;wBAAE,MAAM;wBAAG,MAAM;oBAAI,GACrB,IAAI,CAAC,KAAK;oBAEZ;gBACF;oBACE;YACJ;YAEA,KAAK,QAAQ,GAAG,YAAY,QAAQ;YAEpC,6CAA6C;YAC7C,MAAM,cAAc,IAAI,CAAC,yBAAyB,CAChD,AAAC,SAAc,OAAN,QACT,YAAY,YAAY;YAE1B,KAAK,QAAQ,GAAG;YAEhB,gDAAgD;YAChD,MAAM,oBAAoB,IAAI,uKAAA,CAAA,mBAAgB,CAC5C,AAAC,eAAoB,OAAN,QACf,IAAI,CAAC,KAAK;YAEZ,kBAAkB,SAAS,GAAG;YAC9B,kBAAkB,aAAa,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG;YACnD,kBAAkB,KAAK,GAAG;YAE1B,yBAAyB;YACzB,MAAM,gBAAgB,KAAK,KAAK,CAAC,AAAC,YAAiB,OAAN;YAC7C,cAAc,QAAQ,GAAG;YACzB,cAAc,OAAO,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAC,MAAM,MAAM;YAEhD,oDAAoD;YACpD,MAAM,kBAAkB,iKAAA,CAAA,YAAS,CAAC,uBAAuB,CACvD,AAAC,SAAc,OAAN,QACT,MACA,cACA,IACA,MAAM,QAAQ,IACd,KAAK,QAAQ,CAAC,CAAC,EACf,KAAK,QAAQ,CAAC,CAAC,GAAG,IAAI,KAAK,GAAG,CAAC,SAAS,KACxC,iKAAA,CAAA,YAAS,CAAC,sBAAsB;YAGlC,MAAM,kBAAkB,iKAAA,CAAA,YAAS,CAAC,uBAAuB,CACvD,AAAC,SAAc,OAAN,QACT,MACA,cACA,IACA,MAAM,QAAQ,IACd,KAAK,QAAQ,CAAC,CAAC,EACf,KAAK,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,KACpC,iKAAA,CAAA,YAAS,CAAC,sBAAsB;YAGlC,iCAAiC;YACjC,MAAM,kBAAkB,iKAAA,CAAA,YAAS,CAAC,uBAAuB,CACvD,AAAC,SAAc,OAAN,QACT,MACA,YACA,IACA,MAAM,QAAQ,IACd,iKAAA,CAAA,UAAO,CAAC,IAAI,IACZ,IAAI,iKAAA,CAAA,UAAO,CACT,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,QAAQ,GAAG,GAC9B,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,QAAQ,IAAI,GAC/B,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,QAAQ,IAAI,IAEjC,iKAAA,CAAA,YAAS,CAAC,uBAAuB;YAGnC,8BAA8B;YAC9B,MAAM,iBAAiB,iKAAA,CAAA,YAAS,CAAC,uBAAuB,CACtD,AAAC,QAAa,OAAN,QACR,MACA,WACA,IACA,KAAK,QAAQ,GACb,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,GAAG,IAClB,IAAI,iKAAA,CAAA,UAAO,CAAC,KAAK,KAAK,MACtB,iKAAA,CAAA,YAAS,CAAC,sBAAsB;YAGlC,mBAAmB;YACnB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC3B;gBACA;gBACA,UAAU;gBACV;gBACA,kBAAkB,YAAY,QAAQ,CAAC,KAAK;gBAC5C;gBACA;gBACA;gBACA;gBACA,cAAc,YAAY,YAAY;YACxC;QACF;IACF;IAEA,0BAA0B,IAAY,EAAE,YAAoB,EAAe;QACzE,MAAM,MAAM,IAAI,yKAAA,CAAA,cAAW,CAAC,MAAM,IAAI,CAAC,KAAK;QAE5C,6CAA6C;QAC7C,MAAM,aAAa,IAAI,CAAC,wBAAwB;QAChD,IAAI,kBAAkB,GAAG;QAEzB,OAAQ;YACN,KAAK;gBACH,sCAAsC;gBACtC,IAAI,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACrC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,0BAA0B,GAAG;gBACjC;YAEF,KAAK;gBACH,4BAA4B;gBAC5B,IAAI,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,MAAM;gBACtC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,KAAK,GAAG;gBACZ,IAAI,iBAAiB,GAAG;gBACxB,IAAI,8BAA8B,GAAG;gBACrC,IAAI,UAAU,CAAC,mBAAmB,GAAG;gBACrC,IAAI,UAAU,CAAC,mBAAmB,GAAG;gBACrC;YAEF,KAAK;gBACH,0CAA0C;gBAC1C,IAAI,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACrC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,WAAW,CAAC,SAAS,GAAG;gBAC5B,IAAI,WAAW,CAAC,SAAS,GAAG;gBAC5B,IAAI,WAAW,CAAC,iBAAiB,GAAG;gBACpC,IAAI,WAAW,CAAC,SAAS,GAAG;gBAC5B;YAEF,KAAK;gBACH,iDAAiD;gBACjD,IAAI,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACrC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,SAAS,CAAC,SAAS,GAAG;gBAC1B,IAAI,SAAS,CAAC,SAAS,GAAG;gBAC1B,IAAI,SAAS,CAAC,SAAS,GAAG;gBAC1B,IAAI,SAAS,CAAC,iBAAiB,GAAG;gBAClC;YAEF,KAAK;gBACH,gDAAgD;gBAChD,IAAI,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACrC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,UAAU,CAAC,SAAS,GAAG;gBAC3B,IAAI,UAAU,CAAC,SAAS,GAAG;gBAC3B,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,GAAG;gBAC7C;YAEF,KAAK;gBACH,8CAA8C;gBAC9C,IAAI,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBACrC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,UAAU,CAAC,qBAAqB,GAAG;gBACvC,IAAI,UAAU,CAAC,qBAAqB,GAAG;gBACvC,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,KAAK;gBAChD;YAEF;gBACE,6BAA6B;gBAC7B,IAAI,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG;gBACjC,IAAI,QAAQ,GAAG;gBACf,IAAI,SAAS,GAAG;gBAChB,IAAI,aAAa,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,KAAK;gBACvC;QACJ;QAEA,OAAO;IACT;IAEA,2BAAwC;QACtC,iDAAiD;QACjD,MAAM,aAAa,8KAAA,CAAA,cAAW,CAAC,gBAAgB,CAC7C;YACE,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,gBAAgB,CAAC;SACvB,EACD,IAAI,CAAC,KAAK;QAGZ,OAAO;IACT;IAEA,iBAAiB,IAAY,EAAU;QACrC,uCAAuC;QACvC,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,GAAG;QACf,OAAO,MAAM,GAAG;QAChB,MAAM,MAAM,OAAO,UAAU,CAAC;QAE9B,gCAAgC;QAChC,MAAM,WAAW,IAAI,oBAAoB,CAAC,GAAG,GAAG,GAAG;QACnD,SAAS,YAAY,CAAC,GAAG;QACzB,SAAS,YAAY,CAAC,KAAK;QAC3B,SAAS,YAAY,CAAC,GAAG;QAEzB,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,KAAK;QAExB,iBAAiB;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,MAAM,IAAI,KAAK,MAAM,KAAK;YAC1B,MAAM,IAAI,KAAK,MAAM,KAAK;YAC1B,MAAM,aAAa,KAAK,MAAM;YAE9B,IAAI,SAAS,GAAG,AAAC,uBAAiC,OAAX,YAAW;YAClD,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG;QACxB;QAEA,OAAO,OAAO,SAAS;IACzB;IAEA,wBAAwB;QACtB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;QAEjB,gCAAgC;QAChC,MAAM,eAAe,IAAI,iLAAA,CAAA,iBAAc,CACrC,gBACA;YAAE,OAAO;YAAK,QAAQ;QAAI,GAC1B,IAAI,CAAC,KAAK;QAEZ,MAAM,eAAe,aAAa,UAAU;QAE5C,yBAAyB;QACzB,MAAM,gBAAgB;YACpB,mBAAmB;YACnB,aAAa,SAAS,GAAG;YACzB,aAAa,QAAQ,CAAC,GAAG,GAAG,KAAK;YAEjC,sCAAsC;YACtC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,MAAM,IAAI,KAAK,MAAM,KAAK;gBAC1B,MAAM,IAAI,KAAK,MAAM,KAAK;gBAC1B,MAAM,QAAQ,KAAK,MAAM;gBAEzB,aAAa,SAAS,GAAG,AAAC,QAAsB,OAAf,QAAQ,IAAG,MAC1C,OAD8C,QAAQ,KAAI,MAE3D,OADC,QAAQ,KACT;gBACD,aAAa,QAAQ,CAAC,GAAG,GAAG,GAAG;YACjC;YAEA,aAAa,MAAM;QACrB;QAEA,2BAA2B;QAC3B,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC;YAC9B,IAAI,KAAK,MAAM,KAAK,KAAK;gBACvB,uCAAuC;gBACvC;YACF;QACF;QAEA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAE1B,kCAAkC;QAClC,MAAM,cAAc,IAAI,iLAAA,CAAA,iBAAc,CACpC,eACA;YAAE,OAAO;YAAK,QAAQ;QAAI,GAC1B,IAAI,CAAC,KAAK;QAEZ,MAAM,cAAc,YAAY,UAAU;QAE1C,MAAM,WAAW;YACf,YAAY,SAAS,GAAG;YACxB,YAAY,QAAQ,CAAC,GAAG,GAAG,KAAK;YAEhC,YAAY,WAAW,GAAG;YAC1B,YAAY,SAAS,GAAG;YAExB,kBAAkB;YAClB,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,GAAI;gBACjC,YAAY,SAAS;gBACrB,YAAY,MAAM,CAAC,GAAG;gBACtB,YAAY,MAAM,CAAC,GAAG;gBACtB,YAAY,MAAM;gBAElB,YAAY,SAAS;gBACrB,YAAY,MAAM,CAAC,GAAG;gBACtB,YAAY,MAAM,CAAC,KAAK;gBACxB,YAAY,MAAM;YACpB;YAEA,YAAY,MAAM;QACpB;QAEA;QACA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;IAC5B;IAEA,+BAA+B;QAC7B,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAEjC,4BAA4B;QAC5B,MAAM,kBAAkB,IAAI,0KAAA,CAAA,kBAAe,CAAC,QAAQ,KAAK,IAAI,CAAC,MAAM;QAEpE,8BAA8B;QAC9B,MAAM,eAAe,IAAI,sKAAA,CAAA,cAAW,CAClC,UACA,oBACA;YAAC;YAAQ;SAAY,EACrB;YAAC;SAAiB,EAClB,KACA,IAAI,CAAC,MAAM;QAGb,aAAa,OAAO,GAAG,CAAC;YACtB,OAAO,QAAQ,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG;YACpC,OAAO,QAAQ,CAAC,aAAa,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS;QACnE;QAEA,qCAAqC;QACrC,MAAM,kBAAkB,IAAI,sKAAA,CAAA,cAAW,CACrC,aACA,uBACA;YAAC;SAAmB,EACpB;YAAC;SAAiB,EAClB,KACA,IAAI,CAAC,MAAM;QAGb,gBAAgB,OAAO,GAAG,CAAC;YACzB,OAAO,QAAQ,CACb,oBACA,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS;QAE1C;IACF;IAEA,sBAAsB;QACpB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAEjC,8CAA8C;QAC9C,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YAClC,IAAI,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,GAAG,EAAE;gBACpD,MAAM,aAAa,YAAY,QAAQ,CAAC,UAAU;gBAElD,+CAA+C;gBAC/C,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CACjD,CAAC,MAAQ,IAAI,IAAI,KAAK,cAAc,IAAI,aAAa,KAAK;gBAG5D,IAAI,gBAAgB;oBAClB,OAAQ,YAAY,IAAI;wBACtB,KAAK;4BACH,IAAI,CAAC,aAAa,CAAC;4BACnB;wBACF,KAAK;4BACH;wBACF,KAAK;4BACH,IAAI,CAAC,aAAa,CAAC;4BACnB;oBACJ;gBACF;YACF;QACF;QAEA,kDAAkD;QAClD,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC;YACzC,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,qBAAqB;YAC9C,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,AAAC,CAAC,MAAM,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,GAAI,IAAI;YAChE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,GAAG,KAAK,GAAG,IAAI,KAAK,MAAM,IAAI,IAAI;YAEjE,oCAAoC;YACpC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI;YAC5D,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI;QAC9D;IACF;IAEA,cAAc,cAAmB,EAAE;QACjC,0BAA0B;QAC1B,MAAM,qBAAqB,IAAI,qKAAA,CAAA,iBAAc,CAAC,aAAa,KAAK,IAAI,CAAC,KAAK;QAC1E,mBAAmB,eAAe,GAAG,IAAI,iLAAA,CAAA,iBAAc,CACrD,oBACA,IACA,IAAI,CAAC,KAAK;QAGZ,2BAA2B;QAC3B,mBAAmB,OAAO,GAAG,eAAe,IAAI;QAChD,mBAAmB,UAAU,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;QACzD,mBAAmB,UAAU,GAAG,IAAI,iKAAA,CAAA,UAAO,CAAC,KAAK,KAAK;QAEtD,mBAAmB,MAAM,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;QAChD,mBAAmB,MAAM,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,KAAK,GAAG;QAClD,mBAAmB,SAAS,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;QAEnD,mBAAmB,OAAO,GAAG;QAC7B,mBAAmB,OAAO,GAAG;QAC7B,mBAAmB,WAAW,GAAG;QACjC,mBAAmB,WAAW,GAAG;QAEjC,mBAAmB,QAAQ,GAAG;QAC9B,mBAAmB,YAAY,GAAG;QAClC,mBAAmB,YAAY,GAAG;QAElC,mBAAmB,KAAK;QAExB,kCAAkC;QAClC,WAAW;YACT,mBAAmB,IAAI;YACvB,WAAW,IAAM,mBAAmB,OAAO,IAAI;QACjD,GAAG;QAEH,2BAA2B;QAC3B,MAAM,UAAU,iKAAA,CAAA,YAAS,CAAC,uBAAuB,CAC/C,WACA,eAAe,IAAI,EACnB,WACA,IACA,IACA,eAAe,IAAI,CAAC,OAAO,EAC3B,eAAe,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAClC,iKAAA,CAAA,YAAS,CAAC,0BAA0B;QAGtC,WAAW;YACT,iKAAA,CAAA,YAAS,CAAC,uBAAuB,CAC/B,aACA,eAAe,IAAI,EACnB,WACA,IACA,IACA,eAAe,IAAI,CAAC,OAAO,EAC3B,IAAI,iKAAA,CAAA,UAAO,CAAC,GAAG,GAAG,IAClB,iKAAA,CAAA,YAAS,CAAC,0BAA0B;QAExC,GAAG;IACL;IAEA,cAAc,cAAmB,EAAE;QACjC,yBAAyB;QACzB,IAAI,eAAe,QAAQ,EAAE;YAC3B,eAAe,QAAQ,CAAC,aAAa,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,KAAK;QAC/D;QAEA,yBAAyB;QACzB,WAAW;YACT,IAAI,eAAe,QAAQ,EAAE;gBAC3B,eAAe,QAAQ,CAAC,aAAa,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG;YAC3D;QACF,GAAG;IACL;IAEA,UAAU;QACR,8BAA8B;QAC9B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC/B,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO;YAC9B,IAAI,IAAI,aAAa,EAAE,IAAI,aAAa,CAAC,OAAO;YAChD,IAAI,IAAI,QAAQ,EAAE,IAAI,QAAQ,CAAC,OAAO;YACtC,IAAI,IAAI,iBAAiB,EAAE,IAAI,iBAAiB,CAAC,OAAO;QAC1D;QACA,IAAI,CAAC,kBAAkB,GAAG,EAAE;QAE5B,2BAA2B;QAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC5B,IAAI,SAAS,QAAQ,OAAO;QAC9B;QACA,IAAI,CAAC,eAAe,GAAG,EAAE;QAEzB,qBAAqB;QACrB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACvB,IAAI,WAAW,UAAU,OAAO;QAClC;QACA,IAAI,CAAC,UAAU,GAAG,EAAE;QAEpB,0BAA0B;QAC1B,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,OAAO;QAC7B;QAEA,0BAA0B;QAC1B,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,OAAO;QAC7B;QAEA,gBAAgB;QAChB,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,OAAO;QACpB;QAEA,iBAAiB;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,OAAO;QACrB;IACF;IAj+BA,YAAY,MAAW,CAAE;QAjBzB,+KAAA,UAAA,KAAA;QACA,+KAAA,UAAA,KAAA;QACA,+KAAA,SAAA,KAAA;QACA,+KAAA,UAAA,KAAA;QACA,+KAAA,kBAAA,KAAA;QACA,+KAAA,kBAAA,KAAA;QACA,+KAAA,SAAA,KAAA;QACA,+KAAA,eAAA,KAAA;QACA,+KAAA,QAAA,KAAA;QACA,+KAAA,gBAAA,KAAA;QACA,+KAAA,UAAA,KAAA;QACA,+KAAA,sBAAA,KAAA;QACA,+KAAA,cAAA,KAAA;QACA,+KAAA,iBAAA,KAAA;QACA,+KAAA,mBAAA,KAAA;QACA,+KAAA,iBAAA,KAAA;QAGE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,KAAK,GAAG;YAAE,GAAG;YAAG,GAAG;QAAE;QAC1B,IAAI,CAAC,WAAW,GAAG;YAAE,GAAG;YAAG,GAAG;QAAE;QAChC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,YAAY,GAAG;YAClB,WAAW;gBACT,SAAS,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;gBAC7B,WAAW,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;gBAC/B,QAAQ,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;YAC9B;YACA,QAAQ;gBACN,SAAS,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,GAAG,GAAG;gBAC7B,WAAW,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,KAAK,GAAG;gBACjC,QAAQ,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,KAAK,GAAG;YAChC;YACA,MAAM;gBACJ,SAAS,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,KAAK,GAAG;gBAC/B,WAAW,IAAI,gKAAA,CAAA,SAAM,CAAC,GAAG,KAAK,GAAG;gBACjC,QAAQ,IAAI,gKAAA,CAAA,SAAM,CAAC,KAAK,GAAG,GAAG;YAChC;QACF;QAEA,4BAA4B;QAC5B,IAAI,CAAC,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,CAAC,aAAa,GAAG,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,EAAE;QACzB,IAAI,CAAC,aAAa,GAAG;QAErB,IAAI,CAAC,IAAI;IACX;AA87BF;uCAEe", "debugId": null}}]}