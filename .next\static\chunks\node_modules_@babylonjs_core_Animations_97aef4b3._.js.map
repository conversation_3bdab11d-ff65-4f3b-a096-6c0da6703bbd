{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animationRange.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animationRange.ts"], "sourcesContent": ["/**\r\n * Represents the range of an animation\r\n */\r\nexport class AnimationRange {\r\n    /**\r\n     * Initializes the range of an animation\r\n     * @param name The name of the animation range\r\n     * @param from The starting frame of the animation\r\n     * @param to The ending frame of the animation\r\n     */\r\n    constructor(\r\n        /**The name of the animation range**/\r\n        public name: string,\r\n        /**The starting frame of the animation */\r\n        public from: number,\r\n        /**The ending frame of the animation*/\r\n        public to: number\r\n    ) {}\r\n\r\n    /**\r\n     * Makes a copy of the animation range\r\n     * @returns A copy of the animation range\r\n     */\r\n    public clone(): AnimationRange {\r\n        return new AnimationRange(this.name, this.from, this.to);\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;GAEG;;;AACG,MAAO,cAAc;IAgBvB;;;OAGG,CACI,KAAK,GAAA;QACR,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7D,CAAC;IArBD;;;;;OAKG,CACH,YACI,mCAAA,EAAqC,CAC9B,IAAY,EACnB,uCAAA,EAAyC,CAClC,IAAY,EACnB,oCAAA,EAAsC,CAC/B,EAAU,CAAA;QAJV,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QAEZ,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QAEZ,IAAA,CAAA,EAAE,GAAF,EAAE,CAAQ;IAClB,CAAC;CASP", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animation.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animation.ts"], "sourcesContent": ["import type { IEasingFunction, EasingFunction } from \"./easing\";\r\nimport { Vector3, Quaternion, Vector2, Matrix, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport { Hermite, Lerp } from \"../Maths/math.scalar.functions\";\r\nimport type { DeepImmutable, Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport type { IAnimationKey } from \"./animationKey\";\r\nimport { AnimationKeyInterpolation } from \"./animationKey\";\r\nimport { AnimationRange } from \"./animationRange\";\r\nimport type { AnimationEvent } from \"./animationEvent\";\r\nimport { Node } from \"../node\";\r\nimport type { IAnimatable } from \"./animatable.interface\";\r\nimport { Size } from \"../Maths/math.size\";\r\nimport { WebRequest } from \"../Misc/webRequest\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { Animatable } from \"./animatable\";\r\nimport type { RuntimeAnimation } from \"./runtimeAnimation\";\r\nimport { SerializationHelper } from \"../Misc/decorators.serialization\";\r\n\r\n// Static values to help the garbage collector\r\n\r\n// Quaternion\r\nexport const _StaticOffsetValueQuaternion: DeepImmutable<Quaternion> = Object.freeze(new Quaternion(0, 0, 0, 0));\r\n\r\n// Vector3\r\nexport const _StaticOffsetValueVector3: DeepImmutable<Vector3> = Object.freeze(Vector3.Zero());\r\n\r\n// Vector2\r\nexport const _StaticOffsetValueVector2: DeepImmutable<Vector2> = Object.freeze(Vector2.Zero());\r\n\r\n// Size\r\nexport const _StaticOffsetValueSize: DeepImmutable<Size> = Object.freeze(Size.Zero());\r\n\r\n// Color3\r\nexport const _StaticOffsetValueColor3: DeepImmutable<Color3> = Object.freeze(Color3.Black());\r\n\r\n// Color4\r\nexport const _StaticOffsetValueColor4: DeepImmutable<Color4> = Object.freeze(new Color4(0, 0, 0, 0));\r\n\r\n/**\r\n * Options to be used when creating an additive animation\r\n */\r\nexport interface IMakeAnimationAdditiveOptions {\r\n    /**\r\n     * The frame that the animation should be relative to (if not provided, 0 will be used)\r\n     */\r\n    referenceFrame?: number;\r\n    /**\r\n     * The name of the animation range to convert to additive. If not provided, fromFrame / toFrame will be used\r\n     * If fromFrame / toFrame are not provided either, the whole animation will be converted to additive\r\n     */\r\n    range?: string;\r\n    /**\r\n     * If true, the original animation will be cloned and converted to additive. If false, the original animation will be converted to additive (default is false)\r\n     */\r\n    cloneOriginalAnimation?: boolean;\r\n    /**\r\n     * The name of the cloned animation if cloneOriginalAnimation is true. If not provided, use the original animation name\r\n     */\r\n    clonedAnimationName?: string;\r\n    /**\r\n     * Together with toFrame, defines the range of the animation to convert to additive. Will only be used if range is not provided\r\n     * If range and fromFrame / toFrame are not provided, the whole animation will be converted to additive\r\n     */\r\n    fromFrame?: number;\r\n    /**\r\n     * Together with fromFrame, defines the range of the animation to convert to additive.\r\n     */\r\n    toFrame?: number;\r\n    /**\r\n     * If true, the key frames will be clipped to the range specified by range or fromFrame / toFrame (default is false)\r\n     */\r\n    clipKeys?: boolean;\r\n}\r\n\r\n/**\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface _IAnimationState {\r\n    key: number;\r\n    repeatCount: number;\r\n    workValue?: any;\r\n    loopMode?: number;\r\n    offsetValue?: any;\r\n    highLimitValue?: any;\r\n}\r\n\r\nconst EvaluateAnimationState: _IAnimationState = {\r\n    key: 0,\r\n    repeatCount: 0,\r\n    loopMode: 2 /*Animation.ANIMATIONLOOPMODE_CONSTANT*/,\r\n};\r\n\r\n/**\r\n * Class used to store any kind of animation\r\n */\r\nexport class Animation {\r\n    private static _UniqueIdGenerator = 0;\r\n\r\n    /**\r\n     * Use matrix interpolation instead of using direct key value when animating matrices\r\n     */\r\n    public static AllowMatricesInterpolation = false;\r\n\r\n    /**\r\n     * When matrix interpolation is enabled, this boolean forces the system to use Matrix.DecomposeLerp instead of Matrix.Lerp. Interpolation is more precise but slower\r\n     */\r\n    public static AllowMatrixDecomposeForInterpolation = true;\r\n\r\n    /**\r\n     * Gets or sets the unique id of the animation (the uniqueness is solely among other animations)\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /** Define the Url to load snippets */\r\n    public static SnippetUrl = Constants.SnippetUrl;\r\n\r\n    /** Snippet ID if the animation was created from the snippet server */\r\n    public snippetId: string;\r\n\r\n    /**\r\n     * Stores the key frames of the animation\r\n     */\r\n    private _keys: Array<IAnimationKey>;\r\n\r\n    /**\r\n     * Stores the easing function of the animation\r\n     */\r\n    private _easingFunction: Nullable<IEasingFunction> = null;\r\n\r\n    /**\r\n     * @internal Internal use only\r\n     */\r\n    public _runtimeAnimations = new Array<RuntimeAnimation>();\r\n\r\n    /**\r\n     * The set of event that will be linked to this animation\r\n     */\r\n    private _events = new Array<AnimationEvent>();\r\n\r\n    /**\r\n     * Stores an array of target property paths\r\n     */\r\n    public targetPropertyPath: string[];\r\n\r\n    /**\r\n     * Stores the blending speed of the animation\r\n     */\r\n    public blendingSpeed = 0.01;\r\n\r\n    /**\r\n     * Stores the animation ranges for the animation\r\n     */\r\n    private _ranges: { [name: string]: Nullable<AnimationRange> } = {};\r\n\r\n    /** @internal */\r\n    public _coreAnimation: Nullable<Animation> = null;\r\n\r\n    /**\r\n     * @internal Internal use\r\n     */\r\n    public static _PrepareAnimation(\r\n        name: string,\r\n        targetProperty: string,\r\n        framePerSecond: number,\r\n        totalFrame: number,\r\n        from: any,\r\n        to: any,\r\n        loopMode?: number,\r\n        easingFunction?: EasingFunction\r\n    ): Nullable<Animation> {\r\n        let dataType = undefined;\r\n\r\n        if (!isNaN(parseFloat(from)) && isFinite(from)) {\r\n            dataType = Animation.ANIMATIONTYPE_FLOAT;\r\n        } else if (from instanceof Quaternion) {\r\n            dataType = Animation.ANIMATIONTYPE_QUATERNION;\r\n        } else if (from instanceof Vector3) {\r\n            dataType = Animation.ANIMATIONTYPE_VECTOR3;\r\n        } else if (from instanceof Vector2) {\r\n            dataType = Animation.ANIMATIONTYPE_VECTOR2;\r\n        } else if (from instanceof Color3) {\r\n            dataType = Animation.ANIMATIONTYPE_COLOR3;\r\n        } else if (from instanceof Color4) {\r\n            dataType = Animation.ANIMATIONTYPE_COLOR4;\r\n        } else if (from instanceof Size) {\r\n            dataType = Animation.ANIMATIONTYPE_SIZE;\r\n        }\r\n\r\n        if (dataType == undefined) {\r\n            return null;\r\n        }\r\n\r\n        const animation = new Animation(name, targetProperty, framePerSecond, dataType, loopMode);\r\n\r\n        const keys: Array<IAnimationKey> = [\r\n            { frame: 0, value: from },\r\n            { frame: totalFrame, value: to },\r\n        ];\r\n        animation.setKeys(keys);\r\n\r\n        if (easingFunction !== undefined) {\r\n            animation.setEasingFunction(easingFunction);\r\n        }\r\n\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Sets up an animation\r\n     * @param property The property to animate\r\n     * @param animationType The animation type to apply\r\n     * @param framePerSecond The frames per second of the animation\r\n     * @param easingFunction The easing function used in the animation\r\n     * @returns The created animation\r\n     */\r\n    public static CreateAnimation(property: string, animationType: number, framePerSecond: number, easingFunction: EasingFunction): Animation {\r\n        const animation: Animation = new Animation(property + \"Animation\", property, framePerSecond, animationType, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n\r\n        animation.setEasingFunction(easingFunction);\r\n\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Create and start an animation on a node\r\n     * @param name defines the name of the global animation that will be run on all nodes\r\n     * @param target defines the target where the animation will take place\r\n     * @param targetProperty defines property to animate\r\n     * @param framePerSecond defines the number of frame per second yo use\r\n     * @param totalFrame defines the number of frames in total\r\n     * @param from defines the initial value\r\n     * @param to defines the final value\r\n     * @param loopMode defines which loop mode you want to use (off by default)\r\n     * @param easingFunction defines the easing function to use (linear by default)\r\n     * @param onAnimationEnd defines the callback to call when animation end\r\n     * @param scene defines the hosting scene\r\n     * @returns the animatable created for this animation\r\n     */\r\n    public static CreateAndStartAnimation(\r\n        name: string,\r\n        target: any,\r\n        targetProperty: string,\r\n        framePerSecond: number,\r\n        totalFrame: number,\r\n        from: any,\r\n        to: any,\r\n        loopMode?: number,\r\n        easingFunction?: EasingFunction,\r\n        onAnimationEnd?: () => void,\r\n        scene?: Scene\r\n    ): Nullable<Animatable> {\r\n        const animation = Animation._PrepareAnimation(name, targetProperty, framePerSecond, totalFrame, from, to, loopMode, easingFunction);\r\n\r\n        if (!animation) {\r\n            return null;\r\n        }\r\n\r\n        if (target.getScene) {\r\n            scene = target.getScene();\r\n        }\r\n\r\n        if (!scene) {\r\n            return null;\r\n        }\r\n\r\n        return scene.beginDirectAnimation(target, [animation], 0, totalFrame, animation.loopMode !== Animation.ANIMATIONLOOPMODE_CONSTANT, 1.0, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Create and start an animation on a node and its descendants\r\n     * @param name defines the name of the global animation that will be run on all nodes\r\n     * @param node defines the root node where the animation will take place\r\n     * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used\r\n     * @param targetProperty defines property to animate\r\n     * @param framePerSecond defines the number of frame per second to use\r\n     * @param totalFrame defines the number of frames in total\r\n     * @param from defines the initial value\r\n     * @param to defines the final value\r\n     * @param loopMode defines which loop mode you want to use (off by default)\r\n     * @param easingFunction defines the easing function to use (linear by default)\r\n     * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)\r\n     * @returns the list of animatables created for all nodes\r\n     * @example https://www.babylonjs-playground.com/#MH0VLI\r\n     */\r\n    public static CreateAndStartHierarchyAnimation(\r\n        name: string,\r\n        node: Node,\r\n        directDescendantsOnly: boolean,\r\n        targetProperty: string,\r\n        framePerSecond: number,\r\n        totalFrame: number,\r\n        from: any,\r\n        to: any,\r\n        loopMode?: number,\r\n        easingFunction?: EasingFunction,\r\n        onAnimationEnd?: () => void\r\n    ): Nullable<Animatable[]> {\r\n        const animation = Animation._PrepareAnimation(name, targetProperty, framePerSecond, totalFrame, from, to, loopMode, easingFunction);\r\n\r\n        if (!animation) {\r\n            return null;\r\n        }\r\n\r\n        const scene = node.getScene();\r\n        return scene.beginDirectHierarchyAnimation(node, directDescendantsOnly, [animation], 0, totalFrame, animation.loopMode === 1, 1.0, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Creates a new animation, merges it with the existing animations and starts it\r\n     * @param name Name of the animation\r\n     * @param node Node which contains the scene that begins the animations\r\n     * @param targetProperty Specifies which property to animate\r\n     * @param framePerSecond The frames per second of the animation\r\n     * @param totalFrame The total number of frames\r\n     * @param from The frame at the beginning of the animation\r\n     * @param to The frame at the end of the animation\r\n     * @param loopMode Specifies the loop mode of the animation\r\n     * @param easingFunction (Optional) The easing function of the animation, which allow custom mathematical formulas for animations\r\n     * @param onAnimationEnd Callback to run once the animation is complete\r\n     * @returns Nullable animation\r\n     */\r\n    public static CreateMergeAndStartAnimation(\r\n        name: string,\r\n        node: Node,\r\n        targetProperty: string,\r\n        framePerSecond: number,\r\n        totalFrame: number,\r\n        from: any,\r\n        to: any,\r\n        loopMode?: number,\r\n        easingFunction?: EasingFunction,\r\n        onAnimationEnd?: () => void\r\n    ): Nullable<Animatable> {\r\n        const animation = Animation._PrepareAnimation(name, targetProperty, framePerSecond, totalFrame, from, to, loopMode, easingFunction);\r\n\r\n        if (!animation) {\r\n            return null;\r\n        }\r\n\r\n        node.animations.push(animation);\r\n\r\n        return node.getScene().beginAnimation(node, 0, totalFrame, animation.loopMode === 1, 1.0, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Convert the keyframes of an animation to be relative to a given reference frame.\r\n     * @param sourceAnimation defines the Animation containing keyframes to convert\r\n     * @param referenceFrame defines the frame that keyframes in the range will be relative to (default: 0)\r\n     * @param range defines the name of the AnimationRange belonging to the Animation to convert\r\n     * @param cloneOriginal defines whether or not to clone the animation and convert the clone or convert the original animation (default is false)\r\n     * @param clonedName defines the name of the resulting cloned Animation if cloneOriginal is true\r\n     * @returns a new Animation if cloneOriginal is true or the original Animation if cloneOriginal is false\r\n     */\r\n    public static MakeAnimationAdditive(sourceAnimation: Animation, referenceFrame?: number, range?: string, cloneOriginal?: boolean, clonedName?: string): Animation;\r\n\r\n    /**\r\n     * Convert the keyframes of an animation to be relative to a given reference frame.\r\n     * @param sourceAnimation defines the Animation containing keyframes to convert\r\n     * @param options defines the options to use when converting ey keyframes\r\n     * @returns a new Animation if options.cloneOriginalAnimation is true or the original Animation if options.cloneOriginalAnimation is false\r\n     */\r\n    public static MakeAnimationAdditive(sourceAnimation: Animation, options?: IMakeAnimationAdditiveOptions): Animation;\r\n\r\n    /** @internal */\r\n    public static MakeAnimationAdditive(\r\n        sourceAnimation: Animation,\r\n        referenceFrameOrOptions?: number | IMakeAnimationAdditiveOptions,\r\n        range?: string,\r\n        cloneOriginal = false,\r\n        clonedName?: string\r\n    ): Animation {\r\n        let options: IMakeAnimationAdditiveOptions;\r\n\r\n        if (typeof referenceFrameOrOptions === \"object\") {\r\n            options = referenceFrameOrOptions;\r\n        } else {\r\n            options = {\r\n                referenceFrame: referenceFrameOrOptions ?? 0,\r\n                range: range,\r\n                cloneOriginalAnimation: cloneOriginal,\r\n                clonedAnimationName: clonedName,\r\n            };\r\n        }\r\n\r\n        let animation = sourceAnimation;\r\n\r\n        if (options.cloneOriginalAnimation) {\r\n            animation = sourceAnimation.clone();\r\n            animation.name = options.clonedAnimationName || animation.name;\r\n        }\r\n\r\n        if (!animation._keys.length) {\r\n            return animation;\r\n        }\r\n\r\n        const referenceFrame = options.referenceFrame && options.referenceFrame >= 0 ? options.referenceFrame : 0;\r\n        let startIndex = 0;\r\n        const firstKey = animation._keys[0];\r\n        let endIndex = animation._keys.length - 1;\r\n        const lastKey = animation._keys[endIndex];\r\n        const valueStore = {\r\n            referenceValue: firstKey.value,\r\n            referencePosition: TmpVectors.Vector3[0],\r\n            referenceQuaternion: TmpVectors.Quaternion[0],\r\n            referenceScaling: TmpVectors.Vector3[1],\r\n            keyPosition: TmpVectors.Vector3[2],\r\n            keyQuaternion: TmpVectors.Quaternion[1],\r\n            keyScaling: TmpVectors.Vector3[3],\r\n        };\r\n        let from = firstKey.frame;\r\n        let to = lastKey.frame;\r\n        if (options.range) {\r\n            const rangeValue = animation.getRange(options.range);\r\n\r\n            if (rangeValue) {\r\n                from = rangeValue.from;\r\n                to = rangeValue.to;\r\n            }\r\n        } else {\r\n            from = options.fromFrame ?? from;\r\n            to = options.toFrame ?? to;\r\n        }\r\n\r\n        if (from !== firstKey.frame) {\r\n            startIndex = animation.createKeyForFrame(from);\r\n        }\r\n\r\n        if (to !== lastKey.frame) {\r\n            endIndex = animation.createKeyForFrame(to);\r\n        }\r\n\r\n        // There's only one key, so use it\r\n        if (animation._keys.length === 1) {\r\n            const value = animation._getKeyValue(animation._keys[0]);\r\n            valueStore.referenceValue = value.clone ? value.clone() : value;\r\n        }\r\n\r\n        // Reference frame is before the first frame, so just use the first frame\r\n        else if (referenceFrame <= firstKey.frame) {\r\n            const value = animation._getKeyValue(firstKey.value);\r\n            valueStore.referenceValue = value.clone ? value.clone() : value;\r\n        }\r\n\r\n        // Reference frame is after the last frame, so just use the last frame\r\n        else if (referenceFrame >= lastKey.frame) {\r\n            const value = animation._getKeyValue(lastKey.value);\r\n            valueStore.referenceValue = value.clone ? value.clone() : value;\r\n        }\r\n\r\n        // Interpolate the reference value from the animation\r\n        else {\r\n            EvaluateAnimationState.key = 0;\r\n            const value = animation._interpolate(referenceFrame, EvaluateAnimationState);\r\n            valueStore.referenceValue = value.clone ? value.clone() : value;\r\n        }\r\n\r\n        // Conjugate the quaternion\r\n        if (animation.dataType === Animation.ANIMATIONTYPE_QUATERNION) {\r\n            valueStore.referenceValue.normalize().conjugateInPlace();\r\n        }\r\n\r\n        // Decompose matrix and conjugate the quaternion\r\n        else if (animation.dataType === Animation.ANIMATIONTYPE_MATRIX) {\r\n            valueStore.referenceValue.decompose(valueStore.referenceScaling, valueStore.referenceQuaternion, valueStore.referencePosition);\r\n            valueStore.referenceQuaternion.normalize().conjugateInPlace();\r\n        }\r\n\r\n        let startFrame = Number.MAX_VALUE;\r\n        const clippedKeys: Nullable<IAnimationKey[]> = options.clipKeys ? [] : null;\r\n\r\n        // Subtract the reference value from all of the key values\r\n        for (let index = startIndex; index <= endIndex; index++) {\r\n            let key = animation._keys[index];\r\n\r\n            if (clippedKeys || options.cloneOriginalAnimation) {\r\n                key = {\r\n                    frame: key.frame,\r\n                    value: key.value.clone ? key.value.clone() : key.value,\r\n                    inTangent: key.inTangent,\r\n                    outTangent: key.outTangent,\r\n                    interpolation: key.interpolation,\r\n                    lockedTangent: key.lockedTangent,\r\n                };\r\n                if (clippedKeys) {\r\n                    if (startFrame === Number.MAX_VALUE) {\r\n                        startFrame = key.frame;\r\n                    }\r\n                    key.frame -= startFrame;\r\n                    clippedKeys.push(key);\r\n                }\r\n            }\r\n\r\n            // If this key was duplicated to create a frame 0 key, skip it because its value has already been updated\r\n            if (index && animation.dataType !== Animation.ANIMATIONTYPE_FLOAT && key.value === firstKey.value) {\r\n                continue;\r\n            }\r\n\r\n            switch (animation.dataType) {\r\n                case Animation.ANIMATIONTYPE_MATRIX:\r\n                    key.value.decompose(valueStore.keyScaling, valueStore.keyQuaternion, valueStore.keyPosition);\r\n                    valueStore.keyPosition.subtractInPlace(valueStore.referencePosition);\r\n                    valueStore.keyScaling.divideInPlace(valueStore.referenceScaling);\r\n                    valueStore.referenceQuaternion.multiplyToRef(valueStore.keyQuaternion, valueStore.keyQuaternion);\r\n                    Matrix.ComposeToRef(valueStore.keyScaling, valueStore.keyQuaternion, valueStore.keyPosition, key.value);\r\n                    break;\r\n\r\n                case Animation.ANIMATIONTYPE_QUATERNION:\r\n                    valueStore.referenceValue.multiplyToRef(key.value, key.value);\r\n                    break;\r\n\r\n                case Animation.ANIMATIONTYPE_VECTOR2:\r\n                case Animation.ANIMATIONTYPE_VECTOR3:\r\n                case Animation.ANIMATIONTYPE_COLOR3:\r\n                case Animation.ANIMATIONTYPE_COLOR4:\r\n                    key.value.subtractToRef(valueStore.referenceValue, key.value);\r\n                    break;\r\n\r\n                case Animation.ANIMATIONTYPE_SIZE:\r\n                    key.value.width -= valueStore.referenceValue.width;\r\n                    key.value.height -= valueStore.referenceValue.height;\r\n                    break;\r\n\r\n                default:\r\n                    key.value -= valueStore.referenceValue;\r\n            }\r\n        }\r\n\r\n        if (clippedKeys) {\r\n            animation.setKeys(clippedKeys, true);\r\n        }\r\n\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Transition property of an host to the target Value\r\n     * @param property The property to transition\r\n     * @param targetValue The target Value of the property\r\n     * @param host The object where the property to animate belongs\r\n     * @param scene Scene used to run the animation\r\n     * @param frameRate Framerate (in frame/s) to use\r\n     * @param transition The transition type we want to use\r\n     * @param duration The duration of the animation, in milliseconds\r\n     * @param onAnimationEnd Callback trigger at the end of the animation\r\n     * @returns Nullable animation\r\n     */\r\n    public static TransitionTo(\r\n        property: string,\r\n        targetValue: any,\r\n        host: any,\r\n        scene: Scene,\r\n        frameRate: number,\r\n        transition: Animation,\r\n        duration: number,\r\n        onAnimationEnd: Nullable<() => void> = null\r\n    ): Nullable<Animatable> {\r\n        if (duration <= 0) {\r\n            host[property] = targetValue;\r\n            if (onAnimationEnd) {\r\n                onAnimationEnd();\r\n            }\r\n            return null;\r\n        }\r\n\r\n        const endFrame: number = frameRate * (duration / 1000);\r\n\r\n        transition.setKeys([\r\n            {\r\n                frame: 0,\r\n                value: host[property].clone ? host[property].clone() : host[property],\r\n            },\r\n            {\r\n                frame: endFrame,\r\n                value: targetValue,\r\n            },\r\n        ]);\r\n\r\n        if (!host.animations) {\r\n            host.animations = [];\r\n        }\r\n\r\n        host.animations.push(transition);\r\n\r\n        const animation: Animatable = scene.beginAnimation(host, 0, endFrame, false);\r\n        animation.onAnimationEnd = onAnimationEnd;\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Return the array of runtime animations currently using this animation\r\n     */\r\n    public get runtimeAnimations(): RuntimeAnimation[] {\r\n        return this._runtimeAnimations;\r\n    }\r\n\r\n    /**\r\n     * Specifies if any of the runtime animations are currently running\r\n     */\r\n    public get hasRunningRuntimeAnimations(): boolean {\r\n        for (const runtimeAnimation of this._runtimeAnimations) {\r\n            if (!runtimeAnimation.isStopped()) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Initializes the animation\r\n     * @param name Name of the animation\r\n     * @param targetProperty Property to animate\r\n     * @param framePerSecond The frames per second of the animation\r\n     * @param dataType The data type of the animation\r\n     * @param loopMode The loop mode of the animation\r\n     * @param enableBlending Specifies if blending should be enabled\r\n     */\r\n    constructor(\r\n        /**Name of the animation */\r\n        public name: string,\r\n        /**Property to animate */\r\n        public targetProperty: string,\r\n        /**The frames per second of the animation */\r\n        public framePerSecond: number,\r\n        /**The data type of the animation */\r\n        public dataType: number,\r\n        /**The loop mode of the animation */\r\n        public loopMode?: number,\r\n        /**Specifies if blending should be enabled */\r\n        public enableBlending?: boolean\r\n    ) {\r\n        this.targetPropertyPath = targetProperty.split(\".\");\r\n        this.dataType = dataType;\r\n        this.loopMode = loopMode === undefined ? Animation.ANIMATIONLOOPMODE_CYCLE : loopMode;\r\n        this.uniqueId = Animation._UniqueIdGenerator++;\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Converts the animation to a string\r\n     * @param fullDetails support for multiple levels of logging within scene loading\r\n     * @returns String form of the animation\r\n     */\r\n    public toString(fullDetails?: boolean): string {\r\n        let ret = \"Name: \" + this.name + \", property: \" + this.targetProperty;\r\n        ret += \", datatype: \" + [\"Float\", \"Vector3\", \"Quaternion\", \"Matrix\", \"Color3\", \"Vector2\"][this.dataType];\r\n        ret += \", nKeys: \" + (this._keys ? this._keys.length : \"none\");\r\n        ret += \", nRanges: \" + (this._ranges ? Object.keys(this._ranges).length : \"none\");\r\n        if (fullDetails) {\r\n            ret += \", Ranges: {\";\r\n            let first = true;\r\n            for (const name in this._ranges) {\r\n                if (first) {\r\n                    ret += \", \";\r\n                    first = false;\r\n                }\r\n                ret += name;\r\n            }\r\n            ret += \"}\";\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Add an event to this animation\r\n     * @param event Event to add\r\n     */\r\n    public addEvent(event: AnimationEvent): void {\r\n        this._events.push(event);\r\n        this._events.sort((a, b) => a.frame - b.frame);\r\n    }\r\n\r\n    /**\r\n     * Remove all events found at the given frame\r\n     * @param frame The frame to remove events from\r\n     */\r\n    public removeEvents(frame: number): void {\r\n        for (let index = 0; index < this._events.length; index++) {\r\n            if (this._events[index].frame === frame) {\r\n                this._events.splice(index, 1);\r\n                index--;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Retrieves all the events from the animation\r\n     * @returns Events from the animation\r\n     */\r\n    public getEvents(): AnimationEvent[] {\r\n        return this._events;\r\n    }\r\n\r\n    /**\r\n     * Creates an animation range\r\n     * @param name Name of the animation range\r\n     * @param from Starting frame of the animation range\r\n     * @param to Ending frame of the animation\r\n     */\r\n    public createRange(name: string, from: number, to: number): void {\r\n        // check name not already in use; could happen for bones after serialized\r\n        if (!this._ranges[name]) {\r\n            this._ranges[name] = new AnimationRange(name, from, to);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Deletes an animation range by name\r\n     * @param name Name of the animation range to delete\r\n     * @param deleteFrames Specifies if the key frames for the range should also be deleted (true) or not (false)\r\n     */\r\n    public deleteRange(name: string, deleteFrames = true): void {\r\n        const range = this._ranges[name];\r\n        if (!range) {\r\n            return;\r\n        }\r\n        if (deleteFrames) {\r\n            const from = range.from;\r\n            const to = range.to;\r\n\r\n            // this loop MUST go high to low for multiple splices to work\r\n            for (let key = this._keys.length - 1; key >= 0; key--) {\r\n                if (this._keys[key].frame >= from && this._keys[key].frame <= to) {\r\n                    this._keys.splice(key, 1);\r\n                }\r\n            }\r\n        }\r\n        this._ranges[name] = null; // said much faster than 'delete this._range[name]'\r\n    }\r\n\r\n    /**\r\n     * Gets the animation range by name, or null if not defined\r\n     * @param name Name of the animation range\r\n     * @returns Nullable animation range\r\n     */\r\n    public getRange(name: string): Nullable<AnimationRange> {\r\n        return this._ranges[name];\r\n    }\r\n\r\n    /**\r\n     * Gets the key frames from the animation\r\n     * @returns The key frames of the animation\r\n     */\r\n    public getKeys(): Array<IAnimationKey> {\r\n        return this._keys;\r\n    }\r\n\r\n    /**\r\n     * Gets the highest frame of the animation\r\n     * @returns Highest frame of the animation\r\n     */\r\n    public getHighestFrame(): number {\r\n        let ret = 0;\r\n\r\n        for (let key = 0, nKeys = this._keys.length; key < nKeys; key++) {\r\n            if (ret < this._keys[key].frame) {\r\n                ret = this._keys[key].frame;\r\n            }\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Gets the easing function of the animation\r\n     * @returns Easing function of the animation\r\n     */\r\n    public getEasingFunction(): Nullable<IEasingFunction> {\r\n        return this._easingFunction;\r\n    }\r\n\r\n    /**\r\n     * Sets the easing function of the animation\r\n     * @param easingFunction A custom mathematical formula for animation\r\n     */\r\n    public setEasingFunction(easingFunction: Nullable<IEasingFunction>): void {\r\n        this._easingFunction = easingFunction;\r\n    }\r\n\r\n    /**\r\n     * Interpolates a scalar linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated scalar value\r\n     */\r\n    public floatInterpolateFunction(startValue: number, endValue: number, gradient: number): number {\r\n        return Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a scalar cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated scalar value\r\n     */\r\n    public floatInterpolateFunctionWithTangents(startValue: number, outTangent: number, endValue: number, inTangent: number, gradient: number): number {\r\n        return Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a quaternion using a spherical linear interpolation\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated quaternion value\r\n     */\r\n    public quaternionInterpolateFunction(startValue: Quaternion, endValue: Quaternion, gradient: number): Quaternion {\r\n        return Quaternion.Slerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a quaternion cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated quaternion value\r\n     */\r\n    public quaternionInterpolateFunctionWithTangents(startValue: Quaternion, outTangent: Quaternion, endValue: Quaternion, inTangent: Quaternion, gradient: number): Quaternion {\r\n        return Quaternion.Hermite(startValue, outTangent, endValue, inTangent, gradient).normalize();\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Vector3 linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate (value between 0 and 1)\r\n     * @returns Interpolated scalar value\r\n     */\r\n    public vector3InterpolateFunction(startValue: Vector3, endValue: Vector3, gradient: number): Vector3 {\r\n        return Vector3.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Vector3 cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate (value between 0 and 1)\r\n     * @returns InterpolatedVector3 value\r\n     */\r\n    public vector3InterpolateFunctionWithTangents(startValue: Vector3, outTangent: Vector3, endValue: Vector3, inTangent: Vector3, gradient: number): Vector3 {\r\n        return Vector3.Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Vector2 linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate (value between 0 and 1)\r\n     * @returns Interpolated Vector2 value\r\n     */\r\n    public vector2InterpolateFunction(startValue: Vector2, endValue: Vector2, gradient: number): Vector2 {\r\n        return Vector2.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Vector2 cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate (value between 0 and 1)\r\n     * @returns Interpolated Vector2 value\r\n     */\r\n    public vector2InterpolateFunctionWithTangents(startValue: Vector2, outTangent: Vector2, endValue: Vector2, inTangent: Vector2, gradient: number): Vector2 {\r\n        return Vector2.Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a size linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated Size value\r\n     */\r\n    public sizeInterpolateFunction(startValue: Size, endValue: Size, gradient: number): Size {\r\n        return Size.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Color3 linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated Color3 value\r\n     */\r\n    public color3InterpolateFunction(startValue: Color3, endValue: Color3, gradient: number): Color3 {\r\n        return Color3.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Color3 cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns interpolated value\r\n     */\r\n    public color3InterpolateFunctionWithTangents(startValue: Color3, outTangent: Color3, endValue: Color3, inTangent: Color3, gradient: number): Color3 {\r\n        return Color3.Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Color4 linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated Color3 value\r\n     */\r\n    public color4InterpolateFunction(startValue: Color4, endValue: Color4, gradient: number): Color4 {\r\n        return Color4.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Color4 cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns interpolated value\r\n     */\r\n    public color4InterpolateFunctionWithTangents(startValue: Color4, outTangent: Color4, endValue: Color4, inTangent: Color4, gradient: number): Color4 {\r\n        return Color4.Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * @internal Internal use only\r\n     */\r\n    public _getKeyValue(value: any): any {\r\n        if (typeof value === \"function\") {\r\n            return value();\r\n        }\r\n\r\n        return value;\r\n    }\r\n\r\n    /**\r\n     * Evaluate the animation value at a given frame\r\n     * @param currentFrame defines the frame where we want to evaluate the animation\r\n     * @returns the animation value\r\n     */\r\n    public evaluate(currentFrame: number) {\r\n        EvaluateAnimationState.key = 0;\r\n        return this._interpolate(currentFrame, EvaluateAnimationState);\r\n    }\r\n\r\n    /** @internal */\r\n    public _key: number;\r\n\r\n    /**\r\n     * @internal Internal use only\r\n     */\r\n    public _interpolate(currentFrame: number, state: _IAnimationState, searchClosestKeyOnly = false): any {\r\n        if (state.loopMode === Animation.ANIMATIONLOOPMODE_CONSTANT && state.repeatCount > 0) {\r\n            return state.highLimitValue.clone ? state.highLimitValue.clone() : state.highLimitValue;\r\n        }\r\n\r\n        const keys = this._keys;\r\n        let key: number;\r\n\r\n        if (!this._coreAnimation) {\r\n            const keysLength = keys.length;\r\n\r\n            key = state.key;\r\n\r\n            while (key >= 0 && currentFrame < keys[key].frame) {\r\n                --key;\r\n            }\r\n\r\n            while (key + 1 <= keysLength - 1 && currentFrame >= keys[key + 1].frame) {\r\n                ++key;\r\n            }\r\n\r\n            state.key = key;\r\n\r\n            if (key < 0) {\r\n                return searchClosestKeyOnly ? undefined : this._getKeyValue(keys[0].value);\r\n            } else if (key + 1 > keysLength - 1) {\r\n                return searchClosestKeyOnly ? undefined : this._getKeyValue(keys[keysLength - 1].value);\r\n            }\r\n\r\n            this._key = key;\r\n        } else {\r\n            key = this._coreAnimation._key;\r\n        }\r\n\r\n        const startKey = keys[key];\r\n        const endKey = keys[key + 1];\r\n\r\n        if (searchClosestKeyOnly && (currentFrame === startKey.frame || currentFrame === endKey.frame)) {\r\n            return undefined;\r\n        }\r\n        const startValue = this._getKeyValue(startKey.value);\r\n        const endValue = this._getKeyValue(endKey.value);\r\n        if (startKey.interpolation === AnimationKeyInterpolation.STEP) {\r\n            if (endKey.frame > currentFrame) {\r\n                return startValue;\r\n            } else {\r\n                return endValue;\r\n            }\r\n        }\r\n\r\n        const useTangent = startKey.outTangent !== undefined && endKey.inTangent !== undefined;\r\n        const frameDelta = endKey.frame - startKey.frame;\r\n\r\n        // gradient : percent of currentFrame between the frame inf and the frame sup\r\n        let gradient = (currentFrame - startKey.frame) / frameDelta;\r\n\r\n        // check for easingFunction and correction of gradient\r\n        const easingFunction = startKey.easingFunction || this.getEasingFunction();\r\n        // can also be undefined, if not provided\r\n        if (easingFunction) {\r\n            gradient = easingFunction.ease(gradient);\r\n        }\r\n\r\n        switch (this.dataType) {\r\n            // Float\r\n            case Animation.ANIMATIONTYPE_FLOAT: {\r\n                const floatValue = useTangent\r\n                    ? this.floatInterpolateFunctionWithTangents(startValue, startKey.outTangent * frameDelta, endValue, endKey.inTangent * frameDelta, gradient)\r\n                    : this.floatInterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                    case Animation.ANIMATIONLOOPMODE_YOYO:\r\n                        return floatValue;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT:\r\n                        return (state.offsetValue ?? 0) * state.repeatCount + floatValue;\r\n                }\r\n                break;\r\n            }\r\n            // Quaternion\r\n            case Animation.ANIMATIONTYPE_QUATERNION: {\r\n                const quatValue = useTangent\r\n                    ? this.quaternionInterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.quaternionInterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                    case Animation.ANIMATIONLOOPMODE_YOYO:\r\n                        return quatValue;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT:\r\n                        return quatValue.addInPlace((state.offsetValue || _StaticOffsetValueQuaternion).scale(state.repeatCount));\r\n                }\r\n\r\n                return quatValue;\r\n            }\r\n            // Vector3\r\n            case Animation.ANIMATIONTYPE_VECTOR3: {\r\n                const vec3Value = useTangent\r\n                    ? this.vector3InterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.vector3InterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                    case Animation.ANIMATIONLOOPMODE_YOYO:\r\n                        return vec3Value;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT:\r\n                        return vec3Value.add((state.offsetValue || _StaticOffsetValueVector3).scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Vector2\r\n            case Animation.ANIMATIONTYPE_VECTOR2: {\r\n                const vec2Value = useTangent\r\n                    ? this.vector2InterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.vector2InterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                    case Animation.ANIMATIONLOOPMODE_YOYO:\r\n                        return vec2Value;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT:\r\n                        return vec2Value.add((state.offsetValue || _StaticOffsetValueVector2).scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Size\r\n            case Animation.ANIMATIONTYPE_SIZE: {\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                    case Animation.ANIMATIONLOOPMODE_YOYO:\r\n                        return this.sizeInterpolateFunction(startValue, endValue, gradient);\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT:\r\n                        return this.sizeInterpolateFunction(startValue, endValue, gradient).add((state.offsetValue || _StaticOffsetValueSize).scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Color3\r\n            case Animation.ANIMATIONTYPE_COLOR3: {\r\n                const color3Value = useTangent\r\n                    ? this.color3InterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.color3InterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                    case Animation.ANIMATIONLOOPMODE_YOYO:\r\n                        return color3Value;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT:\r\n                        return color3Value.add((state.offsetValue || _StaticOffsetValueColor3).scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Color4\r\n            case Animation.ANIMATIONTYPE_COLOR4: {\r\n                const color4Value = useTangent\r\n                    ? this.color4InterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.color4InterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                    case Animation.ANIMATIONLOOPMODE_YOYO:\r\n                        return color4Value;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT:\r\n                        return color4Value.add((state.offsetValue || _StaticOffsetValueColor4).scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Matrix\r\n            case Animation.ANIMATIONTYPE_MATRIX: {\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                    case Animation.ANIMATIONLOOPMODE_YOYO: {\r\n                        if (Animation.AllowMatricesInterpolation) {\r\n                            return this.matrixInterpolateFunction(startValue, endValue, gradient, state.workValue);\r\n                        }\r\n                        return startValue;\r\n                    }\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT: {\r\n                        return startValue;\r\n                    }\r\n                }\r\n                break;\r\n            }\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Defines the function to use to interpolate matrices\r\n     * @param startValue defines the start matrix\r\n     * @param endValue defines the end matrix\r\n     * @param gradient defines the gradient between both matrices\r\n     * @param result defines an optional target matrix where to store the interpolation\r\n     * @returns the interpolated matrix\r\n     */\r\n    public matrixInterpolateFunction(startValue: Matrix, endValue: Matrix, gradient: number, result?: Matrix): Matrix {\r\n        if (Animation.AllowMatrixDecomposeForInterpolation) {\r\n            if (result) {\r\n                Matrix.DecomposeLerpToRef(startValue, endValue, gradient, result);\r\n                return result;\r\n            }\r\n            return Matrix.DecomposeLerp(startValue, endValue, gradient);\r\n        }\r\n\r\n        if (result) {\r\n            Matrix.LerpToRef(startValue, endValue, gradient, result);\r\n            return result;\r\n        }\r\n        return Matrix.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Makes a copy of the animation\r\n     * @returns Cloned animation\r\n     */\r\n    public clone(): Animation {\r\n        const clone = new Animation(this.name, this.targetPropertyPath.join(\".\"), this.framePerSecond, this.dataType, this.loopMode);\r\n\r\n        clone.enableBlending = this.enableBlending;\r\n        clone.blendingSpeed = this.blendingSpeed;\r\n\r\n        if (this._keys) {\r\n            clone.setKeys(this._keys);\r\n        }\r\n\r\n        if (this._ranges) {\r\n            clone._ranges = {};\r\n            for (const name in this._ranges) {\r\n                const range = this._ranges[name];\r\n                if (!range) {\r\n                    continue;\r\n                }\r\n                clone._ranges[name] = range.clone();\r\n            }\r\n        }\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Sets the key frames of the animation\r\n     * @param values The animation key frames to set\r\n     * @param dontClone Whether to clone the keys or not (default is false, so the array of keys is cloned)\r\n     */\r\n    public setKeys(values: Array<IAnimationKey>, dontClone = false): void {\r\n        this._keys = !dontClone ? values.slice(0) : values;\r\n    }\r\n\r\n    /**\r\n     * Creates a key for the frame passed as a parameter and adds it to the animation IF a key doesn't already exist for that frame\r\n     * @param frame Frame number\r\n     * @returns The key index if the key was added or the index of the pre existing key if the frame passed as parameter already has a corresponding key\r\n     */\r\n    public createKeyForFrame(frame: number) {\r\n        // Find the key corresponding to frame\r\n        EvaluateAnimationState.key = 0;\r\n        const value = this._interpolate(frame, EvaluateAnimationState, true);\r\n\r\n        if (!value) {\r\n            // A key corresponding to this frame already exists\r\n            return this._keys[EvaluateAnimationState.key].frame === frame ? EvaluateAnimationState.key : EvaluateAnimationState.key + 1;\r\n        }\r\n\r\n        // The frame is between two keys, so create a new key\r\n        const newKey: IAnimationKey = {\r\n            frame,\r\n            value: value.clone ? value.clone() : value,\r\n        };\r\n\r\n        this._keys.splice(EvaluateAnimationState.key + 1, 0, newKey);\r\n\r\n        return EvaluateAnimationState.key + 1;\r\n    }\r\n\r\n    /**\r\n     * Serializes the animation to an object\r\n     * @returns Serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.property = this.targetProperty;\r\n        serializationObject.framePerSecond = this.framePerSecond;\r\n        serializationObject.dataType = this.dataType;\r\n        serializationObject.loopBehavior = this.loopMode;\r\n        serializationObject.enableBlending = this.enableBlending;\r\n        serializationObject.blendingSpeed = this.blendingSpeed;\r\n\r\n        const dataType = this.dataType;\r\n        serializationObject.keys = [];\r\n        const keys = this.getKeys();\r\n        for (let index = 0; index < keys.length; index++) {\r\n            const animationKey = keys[index];\r\n\r\n            const key: any = {};\r\n            key.frame = animationKey.frame;\r\n\r\n            switch (dataType) {\r\n                case Animation.ANIMATIONTYPE_FLOAT:\r\n                    key.values = [animationKey.value];\r\n                    if (animationKey.inTangent !== undefined) {\r\n                        key.values.push(animationKey.inTangent);\r\n                    }\r\n                    if (animationKey.outTangent !== undefined) {\r\n                        if (animationKey.inTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        key.values.push(animationKey.outTangent);\r\n                    }\r\n                    if (animationKey.interpolation !== undefined) {\r\n                        if (animationKey.inTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        if (animationKey.outTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        key.values.push(animationKey.interpolation);\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_QUATERNION:\r\n                case Animation.ANIMATIONTYPE_MATRIX:\r\n                case Animation.ANIMATIONTYPE_VECTOR3:\r\n                case Animation.ANIMATIONTYPE_COLOR3:\r\n                case Animation.ANIMATIONTYPE_COLOR4:\r\n                    key.values = animationKey.value.asArray();\r\n                    if (animationKey.inTangent != undefined) {\r\n                        key.values.push(animationKey.inTangent.asArray());\r\n                    }\r\n                    if (animationKey.outTangent != undefined) {\r\n                        if (animationKey.inTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        key.values.push(animationKey.outTangent.asArray());\r\n                    }\r\n                    if (animationKey.interpolation !== undefined) {\r\n                        if (animationKey.inTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        if (animationKey.outTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        key.values.push(animationKey.interpolation);\r\n                    }\r\n                    break;\r\n            }\r\n\r\n            serializationObject.keys.push(key);\r\n        }\r\n\r\n        serializationObject.ranges = [];\r\n        for (const name in this._ranges) {\r\n            const source = this._ranges[name];\r\n\r\n            if (!source) {\r\n                continue;\r\n            }\r\n            const range: any = {};\r\n            range.name = name;\r\n            range.from = source.from;\r\n            range.to = source.to;\r\n            serializationObject.ranges.push(range);\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Float animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_FLOAT = Constants.ANIMATIONTYPE_FLOAT;\r\n    /**\r\n     * Vector3 animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_VECTOR3 = Constants.ANIMATIONTYPE_VECTOR3;\r\n    /**\r\n     * Quaternion animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_QUATERNION = Constants.ANIMATIONTYPE_QUATERNION;\r\n    /**\r\n     * Matrix animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_MATRIX = Constants.ANIMATIONTYPE_MATRIX;\r\n    /**\r\n     * Color3 animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_COLOR3 = Constants.ANIMATIONTYPE_COLOR3;\r\n    /**\r\n     * Color3 animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_COLOR4 = Constants.ANIMATIONTYPE_COLOR4;\r\n    /**\r\n     * Vector2 animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_VECTOR2 = Constants.ANIMATIONTYPE_VECTOR2;\r\n    /**\r\n     * Size animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_SIZE = Constants.ANIMATIONTYPE_SIZE;\r\n    /**\r\n     * Relative Loop Mode\r\n     */\r\n    public static readonly ANIMATIONLOOPMODE_RELATIVE = 0;\r\n    /**\r\n     * Cycle Loop Mode\r\n     */\r\n    public static readonly ANIMATIONLOOPMODE_CYCLE = 1;\r\n    /**\r\n     * Constant Loop Mode\r\n     */\r\n    public static readonly ANIMATIONLOOPMODE_CONSTANT = 2;\r\n    /**\r\n     * Yoyo Loop Mode\r\n     */\r\n    public static readonly ANIMATIONLOOPMODE_YOYO = 4;\r\n    /**\r\n     * Relative Loop Mode (add to current value of animated object, unlike ANIMATIONLOOPMODE_RELATIVE)\r\n     */\r\n    public static readonly ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT = 5;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _UniversalLerp(left: any, right: any, amount: number): any {\r\n        const constructor = left.constructor;\r\n        if (constructor.Lerp) {\r\n            // Lerp supported\r\n            return constructor.Lerp(left, right, amount);\r\n        } else if (constructor.Slerp) {\r\n            // Slerp supported\r\n            return constructor.Slerp(left, right, amount);\r\n        } else if (left.toFixed) {\r\n            // Number\r\n            return left * (1.0 - amount) + amount * right;\r\n        } else {\r\n            // Blending not supported\r\n            return right;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Parses an animation object and creates an animation\r\n     * @param parsedAnimation Parsed animation object\r\n     * @returns Animation object\r\n     */\r\n    public static Parse(parsedAnimation: any): Animation {\r\n        const animation = new Animation(parsedAnimation.name, parsedAnimation.property, parsedAnimation.framePerSecond, parsedAnimation.dataType, parsedAnimation.loopBehavior);\r\n\r\n        const dataType = parsedAnimation.dataType;\r\n        const keys: Array<IAnimationKey> = [];\r\n        let data;\r\n        let index: number;\r\n\r\n        if (parsedAnimation.enableBlending) {\r\n            animation.enableBlending = parsedAnimation.enableBlending;\r\n        }\r\n\r\n        if (parsedAnimation.blendingSpeed) {\r\n            animation.blendingSpeed = parsedAnimation.blendingSpeed;\r\n        }\r\n\r\n        for (index = 0; index < parsedAnimation.keys.length; index++) {\r\n            const key = parsedAnimation.keys[index];\r\n            let inTangent: any = undefined;\r\n            let outTangent: any = undefined;\r\n            let interpolation: any = undefined;\r\n\r\n            switch (dataType) {\r\n                case Animation.ANIMATIONTYPE_FLOAT:\r\n                    data = key.values[0];\r\n                    if (key.values.length >= 2) {\r\n                        inTangent = key.values[1];\r\n                    }\r\n                    if (key.values.length >= 3) {\r\n                        outTangent = key.values[2];\r\n                    }\r\n                    if (key.values.length >= 4) {\r\n                        interpolation = key.values[3];\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_QUATERNION:\r\n                    data = Quaternion.FromArray(key.values);\r\n                    if (key.values.length >= 8) {\r\n                        const _inTangent = Quaternion.FromArray(key.values.slice(4, 8));\r\n                        if (!_inTangent.equals(Quaternion.Zero())) {\r\n                            inTangent = _inTangent;\r\n                        }\r\n                    }\r\n                    if (key.values.length >= 12) {\r\n                        const _outTangent = Quaternion.FromArray(key.values.slice(8, 12));\r\n                        if (!_outTangent.equals(Quaternion.Zero())) {\r\n                            outTangent = _outTangent;\r\n                        }\r\n                    }\r\n                    if (key.values.length >= 13) {\r\n                        interpolation = key.values[12];\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_MATRIX:\r\n                    data = Matrix.FromArray(key.values);\r\n                    if (key.values.length >= 17) {\r\n                        interpolation = key.values[16];\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_COLOR3:\r\n                    data = Color3.FromArray(key.values);\r\n                    if (key.values[3]) {\r\n                        inTangent = Color3.FromArray(key.values[3]);\r\n                    }\r\n                    if (key.values[4]) {\r\n                        outTangent = Color3.FromArray(key.values[4]);\r\n                    }\r\n                    if (key.values[5]) {\r\n                        interpolation = key.values[5];\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_COLOR4:\r\n                    data = Color4.FromArray(key.values);\r\n                    if (key.values[4]) {\r\n                        inTangent = Color4.FromArray(key.values[4]);\r\n                    }\r\n                    if (key.values[5]) {\r\n                        outTangent = Color4.FromArray(key.values[5]);\r\n                    }\r\n                    if (key.values[6]) {\r\n                        interpolation = Color4.FromArray(key.values[6]);\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_VECTOR3:\r\n                default:\r\n                    data = Vector3.FromArray(key.values);\r\n                    if (key.values[3]) {\r\n                        inTangent = Vector3.FromArray(key.values[3]);\r\n                    }\r\n                    if (key.values[4]) {\r\n                        outTangent = Vector3.FromArray(key.values[4]);\r\n                    }\r\n                    if (key.values[5]) {\r\n                        interpolation = key.values[5];\r\n                    }\r\n                    break;\r\n            }\r\n\r\n            const keyData: any = {};\r\n            keyData.frame = key.frame;\r\n            keyData.value = data;\r\n\r\n            if (inTangent != undefined) {\r\n                keyData.inTangent = inTangent;\r\n            }\r\n            if (outTangent != undefined) {\r\n                keyData.outTangent = outTangent;\r\n            }\r\n            if (interpolation != undefined) {\r\n                keyData.interpolation = interpolation;\r\n            }\r\n            keys.push(keyData);\r\n        }\r\n\r\n        animation.setKeys(keys);\r\n\r\n        if (parsedAnimation.ranges) {\r\n            for (index = 0; index < parsedAnimation.ranges.length; index++) {\r\n                data = parsedAnimation.ranges[index];\r\n                animation.createRange(data.name, data.from, data.to);\r\n            }\r\n        }\r\n\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Appends the serialized animations from the source animations\r\n     * @param source Source containing the animations\r\n     * @param destination Target to store the animations\r\n     */\r\n    public static AppendSerializedAnimations(source: IAnimatable, destination: any): void {\r\n        SerializationHelper.AppendSerializedAnimations(source, destination);\r\n    }\r\n\r\n    /**\r\n     * Creates a new animation or an array of animations from a snippet saved in a remote file\r\n     * @param name defines the name of the animation to create (can be null or empty to use the one from the json data)\r\n     * @param url defines the url to load from\r\n     * @returns a promise that will resolve to the new animation or an array of animations\r\n     */\r\n    public static async ParseFromFileAsync(name: Nullable<string>, url: string): Promise<Animation | Array<Animation>> {\r\n        return await new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        let serializationObject = JSON.parse(request.responseText);\r\n                        if (serializationObject.animations) {\r\n                            serializationObject = serializationObject.animations;\r\n                        }\r\n\r\n                        if (serializationObject.length) {\r\n                            const output: Animation[] = [];\r\n                            for (const serializedAnimation of serializationObject) {\r\n                                output.push(this.Parse(serializedAnimation));\r\n                            }\r\n\r\n                            resolve(output);\r\n                        } else {\r\n                            const output = this.Parse(serializationObject);\r\n\r\n                            if (name) {\r\n                                output.name = name;\r\n                            }\r\n\r\n                            resolve(output);\r\n                        }\r\n                    } else {\r\n                        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                        reject(\"Unable to load the animation\");\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", url);\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates an animation or an array of animations from a snippet saved by the Inspector\r\n     * @param snippetId defines the snippet to load\r\n     * @returns a promise that will resolve to the new animation or a new array of animations\r\n     */\r\n    public static async ParseFromSnippetAsync(snippetId: string): Promise<Animation | Array<Animation>> {\r\n        return await new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);\r\n\r\n                        if (snippet.animations) {\r\n                            const serializationObject = JSON.parse(snippet.animations);\r\n                            const outputs: Animation[] = [];\r\n                            for (const serializedAnimation of serializationObject.animations) {\r\n                                const output = this.Parse(serializedAnimation);\r\n                                output.snippetId = snippetId;\r\n                                outputs.push(output);\r\n                            }\r\n\r\n                            resolve(outputs);\r\n                        } else {\r\n                            const serializationObject = JSON.parse(snippet.animation);\r\n                            const output = this.Parse(serializationObject);\r\n\r\n                            output.snippetId = snippetId;\r\n\r\n                            resolve(output);\r\n                        }\r\n                    } else {\r\n                        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                        reject(\"Unable to load the snippet \" + snippetId);\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", this.SnippetUrl + \"/\" + snippetId.replace(/#/g, \"/\"));\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates an animation or an array of animations from a snippet saved by the Inspector\r\n     * @deprecated Please use ParseFromSnippetAsync instead\r\n     * @param snippetId defines the snippet to load\r\n     * @returns a promise that will resolve to the new animation or a new array of animations\r\n     */\r\n    public static CreateFromSnippetAsync = Animation.ParseFromSnippetAsync;\r\n}\r\n\r\nRegisterClass(\"BABYLON.Animation\", Animation);\r\nNode._AnimationRangeFactory = (name: string, from: number, to: number) => new AnimationRange(name, from, to);\r\n"], "names": [], "mappings": ";;;;;;;;;AACA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACxF,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,gCAAgC,CAAC;AAG/D,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAGlD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAIhD,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;;;;;;;;;;AAKhE,MAAM,4BAA4B,GAA8B,MAAM,CAAC,MAAM,CAAC,sKAAI,aAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAG1G,MAAM,yBAAyB,GAA2B,MAAM,CAAC,MAAM,mKAAC,UAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAGxF,MAAM,yBAAyB,GAA2B,MAAM,CAAC,MAAM,mKAAC,UAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAGxF,MAAM,sBAAsB,GAAwB,MAAM,CAAC,MAAM,iKAAC,OAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAG/E,MAAM,wBAAwB,GAA0B,MAAM,CAAC,MAAM,CAAC,0KAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAGtF,MAAM,wBAAwB,GAA0B,MAAM,CAAC,MAAM,CAAC,qKAAI,SAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAmDrG,MAAM,sBAAsB,GAAqB;IAC7C,GAAG,EAAE,CAAC;IACN,WAAW,EAAE,CAAC;IACd,QAAQ,EAAE,CAAC,CAAC,sCAAA,EAAwC;CACvD,CAAC;AAKI,MAAO,SAAS;IA8DlB;;OAEG,CACI,MAAM,CAAC,iBAAiB,CAC3B,IAAY,EACZ,cAAsB,EACtB,cAAsB,EACtB,UAAkB,EAClB,IAAS,EACT,EAAO,EACP,QAAiB,EACjB,cAA+B,EAAA;QAE/B,IAAI,QAAQ,GAAG,SAAS,CAAC;QAEzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7C,QAAQ,GAAG,SAAS,CAAC,mBAAmB,CAAC;QAC7C,CAAC,MAAM,IAAI,IAAI,8KAAY,aAAU,EAAE,CAAC;YACpC,QAAQ,GAAG,SAAS,CAAC,wBAAwB,CAAC;QAClD,CAAC,MAAM,IAAI,IAAI,8KAAY,UAAO,EAAE,CAAC;YACjC,QAAQ,GAAG,SAAS,CAAC,qBAAqB,CAAC;QAC/C,CAAC,MAAM,IAAI,IAAI,6KAAY,WAAO,EAAE,CAAC;YACjC,QAAQ,GAAG,SAAS,CAAC,qBAAqB,CAAC;QAC/C,CAAC,MAAM,IAAI,IAAI,6KAAY,SAAM,EAAE,CAAC;YAChC,QAAQ,GAAG,SAAS,CAAC,oBAAoB,CAAC;QAC9C,CAAC,MAAM,IAAI,IAAI,6KAAY,SAAM,EAAE,CAAC;YAChC,QAAQ,GAAG,SAAS,CAAC,oBAAoB,CAAC;QAC9C,CAAC,MAAM,IAAI,IAAI,4KAAY,OAAI,EAAE,CAAC;YAC9B,QAAQ,GAAG,SAAS,CAAC,kBAAkB,CAAC;QAC5C,CAAC;QAED,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE1F,MAAM,IAAI,GAAyB;YAC/B;gBAAE,KAAK,EAAE,CAAC;gBAAE,KAAK,EAAE,IAAI;YAAA,CAAE;YACzB;gBAAE,KAAK,EAAE,UAAU;gBAAE,KAAK,EAAE,EAAE;YAAA,CAAE;SACnC,CAAC;QACF,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExB,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YAC/B,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,eAAe,CAAC,QAAgB,EAAE,aAAqB,EAAE,cAAsB,EAAE,cAA8B,EAAA;QACzH,MAAM,SAAS,GAAc,IAAI,SAAS,CAAC,QAAQ,GAAG,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAElJ,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAE5C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;;OAcG,CACI,MAAM,CAAC,uBAAuB,CACjC,IAAY,EACZ,MAAW,EACX,cAAsB,EACtB,cAAsB,EACtB,UAAkB,EAClB,IAAS,EACT,EAAO,EACP,QAAiB,EACjB,cAA+B,EAC/B,cAA2B,EAC3B,KAAa,EAAA;QAEb,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEpI,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC,oBAAoB,CAAC,MAAM,EAAE;YAAC,SAAS;SAAC,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,0BAA0B,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IAC5J,CAAC;IAED;;;;;;;;;;;;;;;OAeG,CACI,MAAM,CAAC,gCAAgC,CAC1C,IAAY,EACZ,IAAU,EACV,qBAA8B,EAC9B,cAAsB,EACtB,cAAsB,EACtB,UAAkB,EAClB,IAAS,EACT,EAAO,EACP,QAAiB,EACjB,cAA+B,EAC/B,cAA2B,EAAA;QAE3B,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEpI,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC,6BAA6B,CAAC,IAAI,EAAE,qBAAqB,EAAE;YAAC,SAAS;SAAC,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,QAAQ,KAAK,CAAC,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IACvJ,CAAC;IAED;;;;;;;;;;;;;OAaG,CACI,MAAM,CAAC,4BAA4B,CACtC,IAAY,EACZ,IAAU,EACV,cAAsB,EACtB,cAAsB,EACtB,UAAkB,EAClB,IAAS,EACT,EAAO,EACP,QAAiB,EACjB,cAA+B,EAC/B,cAA2B,EAAA;QAE3B,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEpI,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEhC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,QAAQ,KAAK,CAAC,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IAC9G,CAAC;IAqBD,cAAA,EAAgB,CACT,MAAM,CAAC,qBAAqB,CAC/B,eAA0B,EAC1B,uBAAgE,EAChE,KAAc,EAEK;4BADnB,aAAa,oDAAG,KAAK,EACrB,UAAmB;QAEnB,IAAI,OAAsC,CAAC;QAE3C,IAAI,OAAO,uBAAuB,KAAK,QAAQ,EAAE,CAAC;YAC9C,OAAO,GAAG,uBAAuB,CAAC;QACtC,CAAC,MAAM,CAAC;YACJ,OAAO,GAAG;gBACN,cAAc,2EAAE,uBAAuB,GAAI,CAAC;gBAC5C,KAAK,EAAE,KAAK;gBACZ,sBAAsB,EAAE,aAAa;gBACrC,mBAAmB,EAAE,UAAU;aAClC,CAAC;QACN,CAAC;QAED,IAAI,SAAS,GAAG,eAAe,CAAC;QAEhC,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;YACjC,SAAS,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;YACpC,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,mBAAmB,IAAI,SAAS,CAAC,IAAI,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1G,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG;YACf,cAAc,EAAE,QAAQ,CAAC,KAAK;YAC9B,iBAAiB,EAAE,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YACxC,mBAAmB,oKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YAC7C,gBAAgB,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YACvC,WAAW,EAAE,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YAClC,aAAa,oKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YACvC,UAAU,oKAAE,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC;SACpC,CAAC;QACF,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC1B,IAAI,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;QACvB,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAErD,IAAI,UAAU,EAAE,CAAC;gBACb,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;gBACvB,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;YACvB,CAAC;QACL,CAAC,MAAM,CAAC;;YACJ,IAAI,iCAAW,SAAS,cAAjB,OAAO,8CAAc,IAAI,CAAC;;YACjC,EAAE,+BAAW,OAAO,4CAAf,OAAO,YAAY,EAAE,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,KAAK,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC1B,UAAU,GAAG,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,EAAE,KAAK,OAAO,CAAC,KAAK,EAAE,CAAC;YACvB,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,kCAAkC;QAClC,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QACpE,CAAC,MAGI,IAAI,cAAc,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrD,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QACpE,CAAC,MAGI,IAAI,cAAc,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACpD,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QACpE,CAAC,MAGI,CAAC;YACF,sBAAsB,CAAC,GAAG,GAAG,CAAC,CAAC;YAC/B,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,cAAc,EAAE,sBAAsB,CAAC,CAAC;YAC7E,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QACpE,CAAC;QAED,2BAA2B;QAC3B,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,wBAAwB,EAAE,CAAC;YAC5D,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC;QAC7D,CAAC,MAGI,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,oBAAoB,EAAE,CAAC;YAC7D,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,mBAAmB,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAC/H,UAAU,CAAC,mBAAmB,CAAC,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC;QAClE,CAAC;QAED,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;QAClC,MAAM,WAAW,GAA8B,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAE5E,0DAA0D;QAC1D,IAAK,IAAI,KAAK,GAAG,UAAU,EAAE,KAAK,IAAI,QAAQ,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAEjC,IAAI,WAAW,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;gBAChD,GAAG,GAAG;oBACF,KAAK,EAAE,GAAG,CAAC,KAAK;oBAChB,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK;oBACtD,SAAS,EAAE,GAAG,CAAC,SAAS;oBACxB,UAAU,EAAE,GAAG,CAAC,UAAU;oBAC1B,aAAa,EAAE,GAAG,CAAC,aAAa;oBAChC,aAAa,EAAE,GAAG,CAAC,aAAa;iBACnC,CAAC;gBACF,IAAI,WAAW,EAAE,CAAC;oBACd,IAAI,UAAU,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;wBAClC,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC;oBAC3B,CAAC;oBACD,GAAG,CAAC,KAAK,IAAI,UAAU,CAAC;oBACxB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC;YACL,CAAC;YAED,yGAAyG;YACzG,IAAI,KAAK,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,mBAAmB,IAAI,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAChG,SAAS;YACb,CAAC;YAED,OAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACzB,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,aAAa,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;oBAC7F,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;oBACrE,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;oBACjE,UAAU,CAAC,mBAAmB,CAAC,aAAa,CAAC,UAAU,CAAC,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;sLACjG,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,aAAa,EAAE,UAAU,CAAC,WAAW,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;oBACxG,MAAM;gBAEV,KAAK,SAAS,CAAC,wBAAwB;oBACnC,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;oBAC9D,MAAM;gBAEV,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBACrC,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBACrC,KAAK,SAAS,CAAC,oBAAoB,CAAC;gBACpC,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;oBAC9D,MAAM;gBAEV,KAAK,SAAS,CAAC,kBAAkB;oBAC7B,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC;oBACnD,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC;oBACrD,MAAM;gBAEV;oBACI,GAAG,CAAC,KAAK,IAAI,UAAU,CAAC,cAAc,CAAC;YAC/C,CAAC;QACL,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YACd,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;OAWG,CACI,MAAM,CAAC,YAAY,CACtB,QAAgB,EAChB,WAAgB,EAChB,IAAS,EACT,KAAY,EACZ,SAAiB,EACjB,UAAqB,EACrB,QAAgB,EAC2B;6BAA3C,iEAAuC,IAAI;QAE3C,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC;YAC7B,IAAI,cAAc,EAAE,CAAC;gBACjB,cAAc,EAAE,CAAC;YACrB,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,QAAQ,GAAW,SAAS,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;QAEvD,UAAU,CAAC,OAAO,CAAC;YACf;gBACI,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;aACxE;YACD;gBACI,KAAK,EAAE,QAAQ;gBACf,KAAK,EAAE,WAAW;aACrB;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEjC,MAAM,SAAS,GAAe,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7E,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;QAC1C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG,CACH,IAAW,2BAA2B,GAAA;QAClC,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,CAAE,CAAC;YACrD,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE,CAAC;gBAChC,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IA+BD,UAAU;IACV;;;;OAIG,CACI,QAAQ,CAAC,WAAqB,EAAA;QACjC,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACtE,GAAG,IAAI,cAAc,GAAG;YAAC,OAAO;YAAE,SAAS;YAAE,YAAY;YAAE,QAAQ;YAAE,QAAQ;YAAE,SAAS;SAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzG,GAAG,IAAI,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC/D,GAAG,IAAI,aAAa,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAClF,IAAI,WAAW,EAAE,CAAC;YACd,GAAG,IAAI,aAAa,CAAC;YACrB,IAAI,KAAK,GAAG,IAAI,CAAC;YACjB,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAC9B,IAAI,KAAK,EAAE,CAAC;oBACR,GAAG,IAAI,IAAI,CAAC;oBACZ,KAAK,GAAG,KAAK,CAAC;gBAClB,CAAC;gBACD,GAAG,IAAI,IAAI,CAAC;YAChB,CAAC;YACD,GAAG,IAAI,GAAG,CAAC;QACf,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;OAGG,CACI,QAAQ,CAAC,KAAqB,EAAA;QACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG,CACI,YAAY,CAAC,KAAa,EAAA;QAC7B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACvD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;gBACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC9B,KAAK,EAAE,CAAC;YACZ,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;;OAKG,CACI,WAAW,CAAC,IAAY,EAAE,IAAY,EAAE,EAAU,EAAA;QACrD,yEAAyE;QACzE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,2KAAI,iBAAc,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QAC5D,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,WAAW,CAAC,IAAY,EAAqB;YAAnB,YAAY,oEAAG,IAAI;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QACD,IAAI,YAAY,EAAE,CAAC;YACf,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;YAEpB,6DAA6D;YAC7D,IAAK,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAE,CAAC;gBACpD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC;oBAC/D,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC9B,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,mDAAmD;IAClF,CAAC;IAED;;;;OAIG,CACI,QAAQ,CAAC,IAAY,EAAA;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;OAGG,CACI,eAAe,GAAA;QAClB,IAAI,GAAG,GAAG,CAAC,CAAC;QAEZ,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,CAAE,CAAC;YAC9D,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;gBAC9B,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;YAChC,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;OAGG,CACI,iBAAiB,GAAA;QACpB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;OAGG,CACI,iBAAiB,CAAC,cAAyC,EAAA;QAC9D,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IAC1C,CAAC;IAED;;;;;;OAMG,CACI,wBAAwB,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB,EAAA;QAClF,0LAAO,OAAA,AAAI,EAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;;OAQG,CACI,oCAAoC,CAAC,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAE,SAAiB,EAAE,QAAgB,EAAA;QACrI,WAAO,yLAAA,AAAO,EAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC1E,CAAC;IAED;;;;;;OAMG,CACI,6BAA6B,CAAC,UAAsB,EAAE,QAAoB,EAAE,QAAgB,EAAA;QAC/F,yKAAO,aAAU,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;;;OAQG,CACI,yCAAyC,CAAC,UAAsB,EAAE,UAAsB,EAAE,QAAoB,EAAE,SAAqB,EAAE,QAAgB,EAAA;QAC1J,wKAAO,cAAU,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,SAAS,EAAE,CAAC;IACjG,CAAC;IAED;;;;;;OAMG,CACI,0BAA0B,CAAC,UAAmB,EAAE,QAAiB,EAAE,QAAgB,EAAA;QACtF,yKAAO,UAAO,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;;OAQG,CACI,sCAAsC,CAAC,UAAmB,EAAE,UAAmB,EAAE,QAAiB,EAAE,SAAkB,EAAE,QAAgB,EAAA;QAC3I,yKAAO,UAAO,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;;;;;OAMG,CACI,0BAA0B,CAAC,UAAmB,EAAE,QAAiB,EAAE,QAAgB,EAAA;QACtF,yKAAO,UAAO,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;;OAQG,CACI,sCAAsC,CAAC,UAAmB,EAAE,UAAmB,EAAE,QAAiB,EAAE,SAAkB,EAAE,QAAgB,EAAA;QAC3I,yKAAO,UAAO,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;;;;;OAMG,CACI,uBAAuB,CAAC,UAAgB,EAAE,QAAc,EAAE,QAAgB,EAAA;QAC7E,uKAAO,OAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG,CACI,yBAAyB,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB,EAAA;QACnF,uKAAO,UAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;OAQG,CACI,qCAAqC,CAAC,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAE,SAAiB,EAAE,QAAgB,EAAA;QACtI,uKAAO,UAAM,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;OAMG,CACI,yBAAyB,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB,EAAA;QACnF,wKAAO,SAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;OAQG,CACI,qCAAqC,CAAC,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAE,SAAiB,EAAE,QAAgB,EAAA;QACtI,wKAAO,SAAM,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG,CACI,YAAY,CAAC,KAAU,EAAA;QAC1B,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YAC9B,OAAO,KAAK,EAAE,CAAC;QACnB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,QAAQ,CAAC,YAAoB,EAAA;QAChC,sBAAsB,CAAC,GAAG,GAAG,CAAC,CAAC;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,sBAAsB,CAAC,CAAC;IACnE,CAAC;IAKD;;OAEG,CACI,YAAY,CAAC,YAAoB,EAAE,KAAuB,EAA8B;mCAA5B,oBAAoB,6CAAG,KAAK;QAC3F,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,0BAA0B,IAAI,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YACnF,OAAO,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC;QAC5F,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,IAAI,GAAW,CAAC;QAEhB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;YAE/B,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;YAEhB,MAAO,GAAG,IAAI,CAAC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAE,CAAC;gBAChD,EAAE,GAAG,CAAC;YACV,CAAC;YAED,MAAO,GAAG,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,IAAI,YAAY,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAE,CAAC;gBACtE,EAAE,GAAG,CAAC;YACV,CAAC;YAED,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;YAEhB,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;gBACV,OAAO,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC/E,CAAC,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,oBAAoB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC5F,CAAC;YAED,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QACpB,CAAC,MAAM,CAAC;YACJ,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;QACnC,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAE7B,IAAI,oBAAoB,IAAI,CAAC,YAAY,KAAK,QAAQ,CAAC,KAAK,IAAI,YAAY,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7F,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,QAAQ,CAAC,aAAa,KAAA,EAAA,kCAAA,EAAmC,GAAE,CAAC;YAC5D,IAAI,MAAM,CAAC,KAAK,GAAG,YAAY,EAAE,CAAC;gBAC9B,OAAO,UAAU,CAAC;YACtB,CAAC,MAAM,CAAC;gBACJ,OAAO,QAAQ,CAAC;YACpB,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC;QACvF,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAEjD,6EAA6E;QAC7E,IAAI,QAAQ,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC;QAE5D,sDAAsD;QACtD,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3E,yCAAyC;QACzC,IAAI,cAAc,EAAE,CAAC;YACjB,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC;QAED,OAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpB,QAAQ;YACR,KAAK,SAAS,CAAC,mBAAmB,CAAC;gBAAC,CAAC;oBACjC,MAAM,UAAU,GAAG,UAAU,GACvB,IAAI,CAAC,oCAAoC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,GAAG,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,GAAG,UAAU,EAAE,QAAQ,CAAC,GAC1I,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBACpE,OAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACrB,KAAK,SAAS,CAAC,uBAAuB,CAAC;wBACvC,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,sBAAsB;4BACjC,OAAO,UAAU,CAAC;wBACtB,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,uCAAuC;;4BAClD,OAAO,6BAAO,WAAW,8CAAjB,KAAK,gBAAgB,CAAC,CAAC,GAAG,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC;oBACzE,CAAC;oBACD,MAAM;gBACV,CAAC;YACD,aAAa;YACb,KAAK,SAAS,CAAC,wBAAwB,CAAC;gBAAC,CAAC;oBACtC,MAAM,SAAS,GAAG,UAAU,GACtB,IAAI,CAAC,yCAAyC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,GACzJ,IAAI,CAAC,6BAA6B,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBACzE,OAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACrB,KAAK,SAAS,CAAC,uBAAuB,CAAC;wBACvC,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,sBAAsB;4BACjC,OAAO,SAAS,CAAC;wBACrB,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,uCAAuC;4BAClD,OAAO,SAAS,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,4BAA4B,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;oBAClH,CAAC;oBAED,OAAO,SAAS,CAAC;gBACrB,CAAC;YACD,UAAU;YACV,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBAAC,CAAC;oBACnC,MAAM,SAAS,GAAG,UAAU,GACtB,IAAI,CAAC,sCAAsC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,GACtJ,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBACtE,OAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACrB,KAAK,SAAS,CAAC,uBAAuB,CAAC;wBACvC,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,sBAAsB;4BACjC,OAAO,SAAS,CAAC;wBACrB,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,uCAAuC;4BAClD,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,yBAAyB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;oBACxG,CAAC;oBACD,MAAM;gBACV,CAAC;YACD,UAAU;YACV,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBAAC,CAAC;oBACnC,MAAM,SAAS,GAAG,UAAU,GACtB,IAAI,CAAC,sCAAsC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,GACtJ,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBACtE,OAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACrB,KAAK,SAAS,CAAC,uBAAuB,CAAC;wBACvC,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,sBAAsB;4BACjC,OAAO,SAAS,CAAC;wBACrB,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,uCAAuC;4BAClD,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,yBAAyB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;oBACxG,CAAC;oBACD,MAAM;gBACV,CAAC;YACD,OAAO;YACP,KAAK,SAAS,CAAC,kBAAkB,CAAC;gBAAC,CAAC;oBAChC,OAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACrB,KAAK,SAAS,CAAC,uBAAuB,CAAC;wBACvC,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,sBAAsB;4BACjC,OAAO,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;wBACxE,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,uCAAuC;4BAClD,OAAO,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,sBAAsB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;oBACxJ,CAAC;oBACD,MAAM;gBACV,CAAC;YACD,SAAS;YACT,KAAK,SAAS,CAAC,oBAAoB,CAAC;gBAAC,CAAC;oBAClC,MAAM,WAAW,GAAG,UAAU,GACxB,IAAI,CAAC,qCAAqC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,GACrJ,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBACrE,OAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACrB,KAAK,SAAS,CAAC,uBAAuB,CAAC;wBACvC,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,sBAAsB;4BACjC,OAAO,WAAW,CAAC;wBACvB,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,uCAAuC;4BAClD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,wBAAwB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;oBACzG,CAAC;oBACD,MAAM;gBACV,CAAC;YACD,SAAS;YACT,KAAK,SAAS,CAAC,oBAAoB,CAAC;gBAAC,CAAC;oBAClC,MAAM,WAAW,GAAG,UAAU,GACxB,IAAI,CAAC,qCAAqC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC,GACrJ,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBACrE,OAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACrB,KAAK,SAAS,CAAC,uBAAuB,CAAC;wBACvC,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,sBAAsB;4BACjC,OAAO,WAAW,CAAC;wBACvB,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,uCAAuC;4BAClD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,WAAW,IAAI,wBAAwB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;oBACzG,CAAC;oBACD,MAAM;gBACV,CAAC;YACD,SAAS;YACT,KAAK,SAAS,CAAC,oBAAoB,CAAC;gBAAC,CAAC;oBAClC,OAAQ,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACrB,KAAK,SAAS,CAAC,uBAAuB,CAAC;wBACvC,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,sBAAsB,CAAC;4BAAC,CAAC;gCACpC,IAAI,SAAS,CAAC,0BAA0B,EAAE,CAAC;oCACvC,OAAO,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;gCAC3F,CAAC;gCACD,OAAO,UAAU,CAAC;4BACtB,CAAC;wBACD,KAAK,SAAS,CAAC,0BAA0B,CAAC;wBAC1C,KAAK,SAAS,CAAC,uCAAuC,CAAC;4BAAC,CAAC;gCACrD,OAAO,UAAU,CAAC;4BACtB,CAAC;oBACL,CAAC;oBACD,MAAM;gBACV,CAAC;QACL,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;;;OAOG,CACI,yBAAyB,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB,EAAE,MAAe,EAAA;QACpG,IAAI,SAAS,CAAC,oCAAoC,EAAE,CAAC;YACjD,IAAI,MAAM,EAAE,CAAC;kLACT,SAAM,CAAC,kBAAkB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAClE,OAAO,MAAM,CAAC;YAClB,CAAC;YACD,yKAAO,SAAM,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;8KACT,SAAM,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzD,OAAO,MAAM,CAAC;QAClB,CAAC;QACD,yKAAO,SAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG,CACI,KAAK,GAAA;QACR,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE7H,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEzC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,KAAK,CAAC,OAAO,GAAG,CAAA,CAAE,CAAC;YACnB,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACT,SAAS;gBACb,CAAC;gBACD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YACxC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG,CACI,OAAO,CAAC,MAA4B,EAAmB;wBAAjB,SAAS,wDAAG,KAAK;QAC1D,IAAI,CAAC,KAAK,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACvD,CAAC;IAED;;;;OAIG,CACI,iBAAiB,CAAC,KAAa,EAAA;QAClC,sCAAsC;QACtC,sBAAsB,CAAC,GAAG,GAAG,CAAC,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,sBAAsB,EAAE,IAAI,CAAC,CAAC;QAErE,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,mDAAmD;YACnD,OAAO,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC,CAAC;QAChI,CAAC;QAED,qDAAqD;QACrD,MAAM,MAAM,GAAkB;YAC1B,KAAK;YACL,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;SAC7C,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAE7D,OAAO,sBAAsB,CAAC,GAAG,GAAG,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,GAAQ,CAAA,CAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC;QACnD,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;QACjD,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEvD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,mBAAmB,CAAC,IAAI,GAAG,EAAE,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YAEjC,MAAM,GAAG,GAAQ,CAAA,CAAE,CAAC;YACpB,GAAG,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;YAE/B,OAAQ,QAAQ,EAAE,CAAC;gBACf,KAAK,SAAS,CAAC,mBAAmB;oBAC9B,GAAG,CAAC,MAAM,GAAG;wBAAC,YAAY,CAAC,KAAK;qBAAC,CAAC;oBAClC,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;wBACvC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;oBAC5C,CAAC;oBACD,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;wBACxC,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;4BACvC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC/B,CAAC;wBACD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;oBAC7C,CAAC;oBACD,IAAI,YAAY,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;wBAC3C,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;4BACvC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC/B,CAAC;wBACD,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;4BACxC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC/B,CAAC;wBACD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;oBAChD,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,wBAAwB,CAAC;gBACxC,KAAK,SAAS,CAAC,oBAAoB,CAAC;gBACpC,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBACrC,KAAK,SAAS,CAAC,oBAAoB,CAAC;gBACpC,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,GAAG,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;oBAC1C,IAAI,YAAY,CAAC,SAAS,IAAI,SAAS,EAAE,CAAC;wBACtC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;oBACtD,CAAC;oBACD,IAAI,YAAY,CAAC,UAAU,IAAI,SAAS,EAAE,CAAC;wBACvC,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;4BACvC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC/B,CAAC;wBACD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;oBACvD,CAAC;oBACD,IAAI,YAAY,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;wBAC3C,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;4BACvC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC/B,CAAC;wBACD,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;4BACxC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBAC/B,CAAC;wBACD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;oBAChD,CAAC;oBACD,MAAM;YACd,CAAC;YAED,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC;QAED,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAChC,IAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAElC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,SAAS;YACb,CAAC;YACD,MAAM,KAAK,GAAQ,CAAA,CAAE,CAAC;YACtB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;YAClB,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;YACzB,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;YACrB,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAwDD;;OAEG,CACI,MAAM,CAAC,cAAc,CAAC,IAAS,EAAE,KAAU,EAAE,MAAc,EAAA;QAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;YACnB,iBAAiB;YACjB,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACjD,CAAC,MAAM,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;YAC3B,kBAAkB;YAClB,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAClD,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACtB,SAAS;YACT,OAAO,IAAI,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;QAClD,CAAC,MAAM,CAAC;YACJ,yBAAyB;YACzB,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,MAAM,CAAC,KAAK,CAAC,eAAoB,EAAA;QACpC,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,cAAc,EAAE,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,YAAY,CAAC,CAAC;QAExK,MAAM,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;QAC1C,MAAM,IAAI,GAAyB,EAAE,CAAC;QACtC,IAAI,IAAI,CAAC;QACT,IAAI,KAAa,CAAC;QAElB,IAAI,eAAe,CAAC,cAAc,EAAE,CAAC;YACjC,SAAS,CAAC,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC;QAC9D,CAAC;QAED,IAAI,eAAe,CAAC,aAAa,EAAE,CAAC;YAChC,SAAS,CAAC,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC;QAC5D,CAAC;QAED,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC3D,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,SAAS,GAAQ,SAAS,CAAC;YAC/B,IAAI,UAAU,GAAQ,SAAS,CAAC;YAChC,IAAI,aAAa,GAAQ,SAAS,CAAC;YAEnC,OAAQ,QAAQ,EAAE,CAAC;gBACf,KAAK,SAAS,CAAC,mBAAmB;oBAC9B,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACrB,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;wBACzB,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;wBACzB,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC/B,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;wBACzB,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAClC,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,wBAAwB;oBACnC,IAAI,qKAAG,aAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACxC,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;wBACzB,MAAM,UAAU,qKAAG,aAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBAChE,IAAI,CAAC,UAAU,CAAC,MAAM,mKAAC,aAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;4BACxC,SAAS,GAAG,UAAU,CAAC;wBAC3B,CAAC;oBACL,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;wBAC1B,MAAM,WAAW,qKAAG,aAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;wBAClE,IAAI,CAAC,WAAW,CAAC,MAAM,mKAAC,aAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;4BACzC,UAAU,GAAG,WAAW,CAAC;wBAC7B,CAAC;oBACL,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;wBAC1B,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACnC,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,IAAI,qKAAG,SAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpC,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;wBAC1B,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACnC,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,IAAI,oKAAG,SAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,SAAS,oKAAG,SAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChD,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,UAAU,oKAAG,SAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAClC,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,IAAI,oKAAG,SAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,SAAS,oKAAG,SAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChD,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,UAAU,oKAAG,SAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,aAAa,oKAAG,SAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpD,CAAC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBACrC;oBACI,IAAI,qKAAG,UAAO,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACrC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,SAAS,qKAAG,UAAO,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,UAAU,qKAAG,UAAO,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClD,CAAC;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAClC,CAAC;oBACD,MAAM;YACd,CAAC;YAED,MAAM,OAAO,GAAQ,CAAA,CAAE,CAAC;YACxB,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;YAC1B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YAErB,IAAI,SAAS,IAAI,SAAS,EAAE,CAAC;gBACzB,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;YAClC,CAAC;YACD,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC;gBAC1B,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;YACpC,CAAC;YACD,IAAI,aAAa,IAAI,SAAS,EAAE,CAAC;gBAC7B,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;YAC1C,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAED,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExB,IAAI,eAAe,CAAC,MAAM,EAAE,CAAC;YACzB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC7D,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACrC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;YACzD,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG,CACI,MAAM,CAAC,0BAA0B,CAAC,MAAmB,EAAE,WAAgB,EAAA;sLAC1E,sBAAmB,CAAC,0BAA0B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACxE,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAsB,EAAE,GAAW,EAAA;QACtE,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,MAAM,OAAO,GAAG,IAAI,0KAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;oBAC1B,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;wBACxB,IAAI,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAC3D,IAAI,mBAAmB,CAAC,UAAU,EAAE,CAAC;4BACjC,mBAAmB,GAAG,mBAAmB,CAAC,UAAU,CAAC;wBACzD,CAAC;wBAED,IAAI,mBAAmB,CAAC,MAAM,EAAE,CAAC;4BAC7B,MAAM,MAAM,GAAgB,EAAE,CAAC;4BAC/B,KAAK,MAAM,mBAAmB,IAAI,mBAAmB,CAAE,CAAC;gCACpD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;4BACjD,CAAC;4BAED,OAAO,CAAC,MAAM,CAAC,CAAC;wBACpB,CAAC,MAAM,CAAC;4BACJ,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;4BAE/C,IAAI,IAAI,EAAE,CAAC;gCACP,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;4BACvB,CAAC;4BAED,OAAO,CAAC,MAAM,CAAC,CAAC;wBACpB,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,2EAA2E;wBAC3E,MAAM,CAAC,8BAA8B,CAAC,CAAC;oBAC3C,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACzB,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG,CACI,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,SAAiB,EAAA;QACvD,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,MAAM,OAAO,GAAG,iKAAI,aAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;oBAC1B,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;wBACxB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;wBAEzE,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;4BACrB,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;4BAC3D,MAAM,OAAO,GAAgB,EAAE,CAAC;4BAChC,KAAK,MAAM,mBAAmB,IAAI,mBAAmB,CAAC,UAAU,CAAE,CAAC;gCAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;gCAC/C,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;gCAC7B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BACzB,CAAC;4BAED,OAAO,CAAC,OAAO,CAAC,CAAC;wBACrB,CAAC,MAAM,CAAC;4BACJ,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;4BAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;4BAE/C,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;4BAE7B,OAAO,CAAC,MAAM,CAAC,CAAC;wBACpB,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,2EAA2E;wBAC3E,MAAM,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC;oBACtD,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IArgCD;;;;;;;;OAQG,CACH,YACI,yBAAA,EAA2B,CACpB,IAAY,EACnB,uBAAA,EAAyB,CAClB,cAAsB,EAC7B,0CAAA,EAA4C,CACrC,cAAsB,EAC7B,kCAAA,EAAoC,CAC7B,QAAgB,EACvB,kCAAA,EAAoC,CAC7B,QAAiB,EACxB,2CAAA,EAA6C,CACtC,cAAwB,CAAA;QAVxB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QAEZ,IAAA,CAAA,cAAc,GAAd,cAAc,CAAQ;QAEtB,IAAA,CAAA,cAAc,GAAd,cAAc,CAAQ;QAEtB,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAQ;QAEhB,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAS;QAEjB,IAAA,CAAA,cAAc,GAAd,cAAc,CAAU;QAzfnC;;WAEG,CACK,IAAA,CAAA,eAAe,GAA8B,IAAI,CAAC;QAE1D;;WAEG,CACI,IAAA,CAAA,kBAAkB,GAAG,IAAI,KAAK,EAAoB,CAAC;QAE1D;;WAEG,CACK,IAAA,CAAA,OAAO,GAAG,IAAI,KAAK,EAAkB,CAAC;QAO9C;;WAEG,CACI,IAAA,CAAA,aAAa,GAAG,IAAI,CAAC;QAE5B;;WAEG,CACK,IAAA,CAAA,OAAO,GAAiD,CAAA,CAAE,CAAC;QAEnE,cAAA,EAAgB,CACT,IAAA,CAAA,cAAc,GAAwB,IAAI,CAAC;QA4d9C,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,QAAQ,CAAC;QACtF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;IACnD,CAAC;;AA3hBc,UAAA,kBAAkB,GAAG,CAAC,AAAJ,CAAK;AAEtC;;GAEG,CACW,UAAA,0BAA0B,GAAG,KAAK,AAAR,CAAS;AAEjD;;GAEG,CACW,UAAA,oCAAoC,GAAG,IAAI,AAAP,CAAQ;AAO1D,oCAAA,EAAsC,CACxB,UAAA,UAAU,GAAG,QAAS,CAAC,UAAU,AAAvB,CAAwB;AAusChD,UAAU;AACV;;GAEG,CACoB,UAAA,mBAAmB,GAAG,SAAS,CAAC,mBAAb,AAAgC,CAAC;AAC3E;;GAEG,CACoB,UAAA,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,AAAlC,CAAmC;AAC/E;;GAEG,CACoB,UAAA,wBAAwB,GAAG,SAAS,CAAC,wBAAwB,AAArC,CAAsC;AACrF;;GAEG,CACoB,UAAA,oBAAoB,GAAG,SAAS,CAAC,oBAAoB,AAAjC,CAAkC;AAC7E;;GAEG,CACoB,UAAA,oBAAoB,GAAG,SAAS,CAAC,oBAAoB,AAAjC,CAAkC;AAC7E;;GAEG,CACoB,UAAA,oBAAoB,GAAG,SAAS,CAAC,oBAAoB,AAAjC,CAAkC;AAC7E;;GAEG,CACoB,UAAA,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,AAAlC,CAAmC;AAC/E;;GAEG,CACoB,UAAA,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,AAA/B,CAAgC;AACzE;;GAEG,CACoB,UAAA,0BAA0B,GAAG,CAAC,AAAJ,CAAK;AACtD;;GAEG,CACoB,UAAA,uBAAuB,GAAG,CAAC,AAAJ,CAAK;AACnD;;GAEG,CACoB,UAAA,0BAA0B,GAAG,CAAC,AAAJ,CAAK;AACtD;;GAEG,CACoB,UAAA,sBAAsB,GAAG,CAAH,AAAI,CAAC;AAClD;;GAEG,CACoB,UAAA,uCAAuC,GAAG,CAAC,AAAJ,CAAK;AA0PnE;;;;;GAKG,CACW,UAAA,sBAAsB,GAAG,SAAS,CAAC,qBAAb,CAAmC;gKAG3E,gBAAA,AAAa,EAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;+IAC9C,OAAI,CAAC,sBAAsB,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,EAAU,EAAE,CAAG,CAAD,0KAAK,iBAAc,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1307, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/runtimeAnimation.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/runtimeAnimation.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Matrix } from \"../Maths/math.vector\";\r\nimport type { _IAnimationState } from \"./animation\";\r\nimport {\r\n    Animation,\r\n    _StaticOffsetValueColor3,\r\n    _StaticOffsetValueColor4,\r\n    _StaticOffsetValueQuaternion,\r\n    _StaticOffsetValueSize,\r\n    _StaticOffsetValueVector2,\r\n    _StaticOffsetValueVector3,\r\n} from \"./animation\";\r\nimport type { AnimationEvent } from \"./animationEvent\";\r\nimport type { Animatable } from \"./animatable\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { IAnimationKey } from \"./animationKey\";\r\n\r\n/**\r\n * Defines a runtime animation\r\n */\r\nexport class RuntimeAnimation {\r\n    private _events = new Array<AnimationEvent>();\r\n\r\n    /**\r\n     * The current frame of the runtime animation\r\n     */\r\n    private _currentFrame: number = 0;\r\n\r\n    /**\r\n     * The animation used by the runtime animation\r\n     */\r\n    public _animation: Animation;\r\n\r\n    /**\r\n     * The target of the runtime animation\r\n     */\r\n    private _target: any;\r\n\r\n    /**\r\n     * The initiating animatable\r\n     */\r\n    private _host: Animatable;\r\n\r\n    /**\r\n     * The original value of the runtime animation\r\n     */\r\n    private _originalValue = new Array<any>();\r\n\r\n    /**\r\n     * The original blend value of the runtime animation\r\n     */\r\n    private _originalBlendValue: Nullable<any> = null;\r\n\r\n    /**\r\n     * The offsets cache of the runtime animation\r\n     */\r\n    private _offsetsCache: { [key: string]: any } = {};\r\n\r\n    /**\r\n     * The high limits cache of the runtime animation\r\n     */\r\n    private _highLimitsCache: { [key: string]: any } = {};\r\n\r\n    /**\r\n     * Specifies if the runtime animation has been stopped\r\n     */\r\n    private _stopped = false;\r\n\r\n    /**\r\n     * The blending factor of the runtime animation\r\n     */\r\n    private _blendingFactor = 0;\r\n\r\n    /**\r\n     * The BabylonJS scene\r\n     */\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * The current value of the runtime animation\r\n     */\r\n    private _currentValue: Nullable<any> = null;\r\n\r\n    /** @internal */\r\n    public _animationState: _IAnimationState;\r\n\r\n    /**\r\n     * The active target of the runtime animation\r\n     */\r\n    private _activeTargets: any[];\r\n    private _currentActiveTarget: Nullable<any> = null;\r\n    private _directTarget: Nullable<any> = null;\r\n\r\n    /**\r\n     * The target path of the runtime animation\r\n     */\r\n    private _targetPath: string = \"\";\r\n\r\n    /**\r\n     * The weight of the runtime animation\r\n     */\r\n    private _weight = 1.0;\r\n\r\n    /**\r\n     * The absolute frame offset of the runtime animation\r\n     */\r\n    private _absoluteFrameOffset = 0;\r\n\r\n    /**\r\n     * The previous elapsed time (since start of animation) of the runtime animation\r\n     */\r\n    private _previousElapsedTime: number = 0;\r\n\r\n    private _yoyoDirection: number = 1;\r\n\r\n    /**\r\n     * The previous absolute frame of the runtime animation (meaning, without taking into account the from/to values, only the elapsed time and the fps)\r\n     */\r\n    private _previousAbsoluteFrame: number = 0;\r\n\r\n    private _enableBlending: boolean;\r\n\r\n    private _keys: IAnimationKey[];\r\n    private _minFrame: number;\r\n    private _maxFrame: number;\r\n    private _minValue: any;\r\n    private _maxValue: any;\r\n    private _targetIsArray = false;\r\n\r\n    /** @internal */\r\n    public _coreRuntimeAnimation: RuntimeAnimation | null = null;\r\n\r\n    /**\r\n     * Gets the current frame of the runtime animation\r\n     */\r\n    public get currentFrame(): number {\r\n        return this._currentFrame;\r\n    }\r\n\r\n    /**\r\n     * Gets the weight of the runtime animation\r\n     */\r\n    public get weight(): number {\r\n        return this._weight;\r\n    }\r\n\r\n    /**\r\n     * Gets the current value of the runtime animation\r\n     */\r\n    public get currentValue(): any {\r\n        return this._currentValue;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the target path of the runtime animation\r\n     */\r\n    public get targetPath(): string {\r\n        return this._targetPath;\r\n    }\r\n\r\n    /**\r\n     * Gets the actual target of the runtime animation\r\n     */\r\n    public get target(): any {\r\n        return this._currentActiveTarget;\r\n    }\r\n\r\n    /**\r\n     * Gets the additive state of the runtime animation\r\n     */\r\n    public get isAdditive(): boolean {\r\n        return this._host && this._host.isAdditive;\r\n    }\r\n\r\n    /** @internal */\r\n    public _onLoop: () => void;\r\n\r\n    /**\r\n     * Create a new RuntimeAnimation object\r\n     * @param target defines the target of the animation\r\n     * @param animation defines the source animation object\r\n     * @param scene defines the hosting scene\r\n     * @param host defines the initiating Animatable\r\n     */\r\n    public constructor(target: any, animation: Animation, scene: Scene, host: Animatable) {\r\n        this._animation = animation;\r\n        this._target = target;\r\n        this._scene = scene;\r\n        this._host = host;\r\n        this._activeTargets = [];\r\n\r\n        animation._runtimeAnimations.push(this);\r\n\r\n        // State\r\n        this._animationState = {\r\n            key: 0,\r\n            repeatCount: 0,\r\n            loopMode: this._getCorrectLoopMode(),\r\n        };\r\n\r\n        if (this._animation.dataType === Animation.ANIMATIONTYPE_MATRIX) {\r\n            this._animationState.workValue = Matrix.Zero();\r\n        }\r\n\r\n        // Limits\r\n        this._keys = this._animation.getKeys();\r\n        this._minFrame = this._keys[0].frame;\r\n        this._maxFrame = this._keys[this._keys.length - 1].frame;\r\n        this._minValue = this._keys[0].value;\r\n        this._maxValue = this._keys[this._keys.length - 1].value;\r\n\r\n        // Add a start key at frame 0 if missing\r\n        if (this._minFrame !== 0) {\r\n            const newKey = { frame: 0, value: this._minValue };\r\n            this._keys.splice(0, 0, newKey);\r\n        }\r\n\r\n        // Check data\r\n        if (this._target instanceof Array) {\r\n            let index = 0;\r\n            for (const target of this._target) {\r\n                this._preparePath(target, index);\r\n                this._getOriginalValues(index);\r\n                index++;\r\n            }\r\n            this._targetIsArray = true;\r\n        } else {\r\n            this._preparePath(this._target);\r\n            this._getOriginalValues();\r\n            this._targetIsArray = false;\r\n            this._directTarget = this._activeTargets[0];\r\n        }\r\n\r\n        // Cloning events locally\r\n        const events = animation.getEvents();\r\n        if (events && events.length > 0) {\r\n            for (const e of events) {\r\n                this._events.push(e._clone());\r\n            }\r\n        }\r\n\r\n        this._enableBlending = target && target.animationPropertiesOverride ? target.animationPropertiesOverride.enableBlending : this._animation.enableBlending;\r\n    }\r\n\r\n    private _preparePath(target: any, targetIndex = 0) {\r\n        const targetPropertyPath = this._animation.targetPropertyPath;\r\n\r\n        if (targetPropertyPath.length > 1) {\r\n            let property = target;\r\n            for (let index = 0; index < targetPropertyPath.length - 1; index++) {\r\n                const name = targetPropertyPath[index];\r\n                property = property[name];\r\n                if (property === undefined) {\r\n                    throw new Error(`Invalid property (${name}) in property path (${targetPropertyPath.join(\".\")})`);\r\n                }\r\n            }\r\n\r\n            this._targetPath = targetPropertyPath[targetPropertyPath.length - 1];\r\n            this._activeTargets[targetIndex] = property;\r\n        } else {\r\n            this._targetPath = targetPropertyPath[0];\r\n            this._activeTargets[targetIndex] = target;\r\n        }\r\n\r\n        if (this._activeTargets[targetIndex][this._targetPath] === undefined) {\r\n            throw new Error(`Invalid property (${this._targetPath}) in property path (${targetPropertyPath.join(\".\")})`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the animation from the runtime animation\r\n     */\r\n    public get animation(): Animation {\r\n        return this._animation;\r\n    }\r\n\r\n    /**\r\n     * Resets the runtime animation to the beginning\r\n     * @param restoreOriginal defines whether to restore the target property to the original value\r\n     */\r\n    public reset(restoreOriginal = false): void {\r\n        if (restoreOriginal) {\r\n            if (this._target instanceof Array) {\r\n                let index = 0;\r\n                for (const target of this._target) {\r\n                    if (this._originalValue[index] !== undefined) {\r\n                        this._setValue(target, this._activeTargets[index], this._originalValue[index], -1, index);\r\n                    }\r\n                    index++;\r\n                }\r\n            } else {\r\n                if (this._originalValue[0] !== undefined) {\r\n                    this._setValue(this._target, this._directTarget, this._originalValue[0], -1, 0);\r\n                }\r\n            }\r\n        }\r\n\r\n        this._offsetsCache = {};\r\n        this._highLimitsCache = {};\r\n        this._currentFrame = 0;\r\n        this._blendingFactor = 0;\r\n\r\n        // Events\r\n        for (let index = 0; index < this._events.length; index++) {\r\n            this._events[index].isDone = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Specifies if the runtime animation is stopped\r\n     * @returns Boolean specifying if the runtime animation is stopped\r\n     */\r\n    public isStopped(): boolean {\r\n        return this._stopped;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the runtime animation\r\n     */\r\n    public dispose(): void {\r\n        const index = this._animation.runtimeAnimations.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._animation.runtimeAnimations.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Apply the interpolated value to the target\r\n     * @param currentValue defines the value computed by the animation\r\n     * @param weight defines the weight to apply to this value (Defaults to 1.0)\r\n     */\r\n    public setValue(currentValue: any, weight: number) {\r\n        if (this._targetIsArray) {\r\n            for (let index = 0; index < this._target.length; index++) {\r\n                const target = this._target[index];\r\n                this._setValue(target, this._activeTargets[index], currentValue, weight, index);\r\n            }\r\n            return;\r\n        }\r\n        this._setValue(this._target, this._directTarget, currentValue, weight, 0);\r\n    }\r\n\r\n    private _getOriginalValues(targetIndex = 0) {\r\n        let originalValue: any;\r\n        const target = this._activeTargets[targetIndex];\r\n\r\n        if (target.getLocalMatrix && this._targetPath === \"_matrix\") {\r\n            // For bones\r\n            originalValue = target.getLocalMatrix();\r\n        } else {\r\n            originalValue = target[this._targetPath];\r\n        }\r\n\r\n        if (originalValue && originalValue.clone) {\r\n            this._originalValue[targetIndex] = originalValue.clone();\r\n        } else {\r\n            this._originalValue[targetIndex] = originalValue;\r\n        }\r\n    }\r\n\r\n    private _registerTargetForLateAnimationBinding(runtimeAnimation: RuntimeAnimation, originalValue: any): void {\r\n        const target = runtimeAnimation.target;\r\n        this._scene._registeredForLateAnimationBindings.pushNoDuplicate(target);\r\n\r\n        if (!target._lateAnimationHolders) {\r\n            target._lateAnimationHolders = {};\r\n        }\r\n\r\n        if (!target._lateAnimationHolders[runtimeAnimation.targetPath]) {\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath] = {\r\n                totalWeight: 0,\r\n                totalAdditiveWeight: 0,\r\n                animations: [],\r\n                additiveAnimations: [],\r\n                originalValue: originalValue,\r\n            };\r\n        }\r\n\r\n        if (runtimeAnimation.isAdditive) {\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath].additiveAnimations.push(runtimeAnimation);\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath].totalAdditiveWeight += runtimeAnimation.weight;\r\n        } else {\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath].animations.push(runtimeAnimation);\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath].totalWeight += runtimeAnimation.weight;\r\n        }\r\n    }\r\n\r\n    private _setValue(target: any, destination: any, currentValue: any, weight: number, targetIndex: number): void {\r\n        // Set value\r\n        this._currentActiveTarget = destination;\r\n\r\n        this._weight = weight;\r\n\r\n        if (this._enableBlending && this._blendingFactor <= 1.0) {\r\n            if (!this._originalBlendValue) {\r\n                const originalValue = destination[this._targetPath];\r\n\r\n                if (originalValue.clone) {\r\n                    this._originalBlendValue = originalValue.clone();\r\n                } else {\r\n                    this._originalBlendValue = originalValue;\r\n                }\r\n            }\r\n\r\n            if (this._originalBlendValue.m) {\r\n                // Matrix\r\n                if (Animation.AllowMatrixDecomposeForInterpolation) {\r\n                    if (this._currentValue) {\r\n                        Matrix.DecomposeLerpToRef(this._originalBlendValue, currentValue, this._blendingFactor, this._currentValue);\r\n                    } else {\r\n                        this._currentValue = Matrix.DecomposeLerp(this._originalBlendValue, currentValue, this._blendingFactor);\r\n                    }\r\n                } else {\r\n                    if (this._currentValue) {\r\n                        Matrix.LerpToRef(this._originalBlendValue, currentValue, this._blendingFactor, this._currentValue);\r\n                    } else {\r\n                        this._currentValue = Matrix.Lerp(this._originalBlendValue, currentValue, this._blendingFactor);\r\n                    }\r\n                }\r\n            } else {\r\n                this._currentValue = Animation._UniversalLerp(this._originalBlendValue, currentValue, this._blendingFactor);\r\n            }\r\n\r\n            const blendingSpeed = target && target.animationPropertiesOverride ? target.animationPropertiesOverride.blendingSpeed : this._animation.blendingSpeed;\r\n            this._blendingFactor += blendingSpeed;\r\n        } else {\r\n            if (!this._currentValue) {\r\n                if (currentValue?.clone) {\r\n                    this._currentValue = currentValue.clone();\r\n                } else {\r\n                    this._currentValue = currentValue;\r\n                }\r\n            } else if (this._currentValue.copyFrom) {\r\n                this._currentValue.copyFrom(currentValue);\r\n            } else {\r\n                this._currentValue = currentValue;\r\n            }\r\n        }\r\n\r\n        if (weight !== -1.0) {\r\n            this._registerTargetForLateAnimationBinding(this, this._originalValue[targetIndex]);\r\n        } else {\r\n            if (this._animationState.loopMode === Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT) {\r\n                if (this._currentValue.addToRef) {\r\n                    this._currentValue.addToRef(this._originalValue[targetIndex], destination[this._targetPath]);\r\n                } else {\r\n                    destination[this._targetPath] = this._originalValue[targetIndex] + this._currentValue;\r\n                }\r\n            } else {\r\n                destination[this._targetPath] = this._currentValue;\r\n            }\r\n        }\r\n\r\n        if (target.markAsDirty) {\r\n            target.markAsDirty(this._animation.targetProperty);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the loop pmode of the runtime animation\r\n     * @returns Loop Mode\r\n     */\r\n    private _getCorrectLoopMode(): number | undefined {\r\n        if (this._target && this._target.animationPropertiesOverride) {\r\n            return this._target.animationPropertiesOverride.loopMode as number;\r\n        }\r\n\r\n        return this._animation.loopMode;\r\n    }\r\n\r\n    /**\r\n     * Move the current animation to a given frame\r\n     * @param frame defines the frame to move to\r\n     * @param weight defines the weight to apply to the animation (-1.0 by default)\r\n     */\r\n    public goToFrame(frame: number, weight = -1): void {\r\n        const keys = this._animation.getKeys();\r\n\r\n        if (frame < keys[0].frame) {\r\n            frame = keys[0].frame;\r\n        } else if (frame > keys[keys.length - 1].frame) {\r\n            frame = keys[keys.length - 1].frame;\r\n        }\r\n\r\n        // Need to reset animation events\r\n        const events = this._events;\r\n        if (events.length) {\r\n            for (let index = 0; index < events.length; index++) {\r\n                if (!events[index].onlyOnce) {\r\n                    // reset events in the future\r\n                    events[index].isDone = events[index].frame < frame;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._currentFrame = frame;\r\n        const currentValue = this._animation._interpolate(frame, this._animationState);\r\n\r\n        this.setValue(currentValue, weight);\r\n    }\r\n\r\n    /**\r\n     * @internal Internal use only\r\n     */\r\n    public _prepareForSpeedRatioChange(newSpeedRatio: number): void {\r\n        const newAbsoluteFrame = (this._previousElapsedTime * (this._animation.framePerSecond * newSpeedRatio)) / 1000.0;\r\n\r\n        this._absoluteFrameOffset = this._previousAbsoluteFrame - newAbsoluteFrame;\r\n    }\r\n\r\n    /**\r\n     * Execute the current animation\r\n     * @param elapsedTimeSinceAnimationStart defines the elapsed time (in milliseconds) since the animation was started\r\n     * @param from defines the lower frame of the animation range\r\n     * @param to defines the upper frame of the animation range\r\n     * @param loop defines if the current animation must loop\r\n     * @param speedRatio defines the current speed ratio\r\n     * @param weight defines the weight of the animation (default is -1 so no weight)\r\n     * @returns a boolean indicating if the animation is running\r\n     */\r\n    public animate(elapsedTimeSinceAnimationStart: number, from: number, to: number, loop: boolean, speedRatio: number, weight = -1.0): boolean {\r\n        const animation = this._animation;\r\n        const targetPropertyPath = animation.targetPropertyPath;\r\n        if (!targetPropertyPath || targetPropertyPath.length < 1) {\r\n            this._stopped = true;\r\n            return false;\r\n        }\r\n\r\n        let returnValue = true;\r\n        let currentFrame: number;\r\n        const events = this._events;\r\n        let frameRange = 0;\r\n\r\n        if (!this._coreRuntimeAnimation) {\r\n            // Check limits\r\n            if (from < this._minFrame || from > this._maxFrame) {\r\n                from = this._minFrame;\r\n            }\r\n            if (to < this._minFrame || to > this._maxFrame) {\r\n                to = this._maxFrame;\r\n            }\r\n\r\n            frameRange = to - from;\r\n            let offsetValue: any;\r\n\r\n            // Compute the frame according to the elapsed time and the fps of the animation (\"from\" and \"to\" are not factored in!)\r\n            let absoluteFrame = (elapsedTimeSinceAnimationStart * (animation.framePerSecond * speedRatio)) / 1000.0 + this._absoluteFrameOffset;\r\n            let highLimitValue = 0;\r\n\r\n            // Apply the yoyo function if required\r\n            let yoyoLoop = false;\r\n            const yoyoMode = loop && this._animationState.loopMode === Animation.ANIMATIONLOOPMODE_YOYO;\r\n            if (yoyoMode) {\r\n                const position = (absoluteFrame - from) / frameRange;\r\n\r\n                // Apply the yoyo curve\r\n                const sin = Math.sin(position * Math.PI);\r\n                const yoyoPosition = Math.abs(sin);\r\n\r\n                // Map the yoyo position back to the range\r\n                absoluteFrame = yoyoPosition * frameRange + from;\r\n\r\n                const direction = sin >= 0 ? 1 : -1;\r\n                if (this._yoyoDirection !== direction) {\r\n                    yoyoLoop = true;\r\n                }\r\n\r\n                this._yoyoDirection = direction;\r\n            }\r\n\r\n            this._previousElapsedTime = elapsedTimeSinceAnimationStart;\r\n            this._previousAbsoluteFrame = absoluteFrame;\r\n\r\n            if (!loop && to >= from && ((absoluteFrame >= frameRange && speedRatio > 0) || (absoluteFrame <= 0 && speedRatio < 0))) {\r\n                // If we are out of range and not looping get back to caller\r\n                returnValue = false;\r\n                highLimitValue = animation._getKeyValue(this._maxValue);\r\n            } else if (!loop && from >= to && ((absoluteFrame <= frameRange && speedRatio < 0) || (absoluteFrame >= 0 && speedRatio > 0))) {\r\n                returnValue = false;\r\n                highLimitValue = animation._getKeyValue(this._minValue);\r\n            } else if (this._animationState.loopMode !== Animation.ANIMATIONLOOPMODE_CYCLE) {\r\n                const keyOffset = to.toString() + from.toString();\r\n                if (!this._offsetsCache[keyOffset]) {\r\n                    this._animationState.repeatCount = 0;\r\n                    this._animationState.loopMode = Animation.ANIMATIONLOOPMODE_CYCLE; // force a specific codepath in animation._interpolate()!\r\n                    const fromValue = animation._interpolate(from, this._animationState);\r\n                    const toValue = animation._interpolate(to, this._animationState);\r\n\r\n                    this._animationState.loopMode = this._getCorrectLoopMode();\r\n                    switch (animation.dataType) {\r\n                        // Float\r\n                        case Animation.ANIMATIONTYPE_FLOAT:\r\n                            this._offsetsCache[keyOffset] = toValue - fromValue;\r\n                            break;\r\n                        // Quaternion\r\n                        case Animation.ANIMATIONTYPE_QUATERNION:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        // Vector3\r\n                        case Animation.ANIMATIONTYPE_VECTOR3:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        // Vector2\r\n                        case Animation.ANIMATIONTYPE_VECTOR2:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        // Size\r\n                        case Animation.ANIMATIONTYPE_SIZE:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        // Color3\r\n                        case Animation.ANIMATIONTYPE_COLOR3:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        default:\r\n                            break;\r\n                    }\r\n\r\n                    this._highLimitsCache[keyOffset] = toValue;\r\n                }\r\n\r\n                highLimitValue = this._highLimitsCache[keyOffset];\r\n                offsetValue = this._offsetsCache[keyOffset];\r\n            }\r\n\r\n            if (offsetValue === undefined) {\r\n                switch (animation.dataType) {\r\n                    // Float\r\n                    case Animation.ANIMATIONTYPE_FLOAT:\r\n                        offsetValue = 0;\r\n                        break;\r\n                    // Quaternion\r\n                    case Animation.ANIMATIONTYPE_QUATERNION:\r\n                        offsetValue = _StaticOffsetValueQuaternion;\r\n                        break;\r\n                    // Vector3\r\n                    case Animation.ANIMATIONTYPE_VECTOR3:\r\n                        offsetValue = _StaticOffsetValueVector3;\r\n                        break;\r\n                    // Vector2\r\n                    case Animation.ANIMATIONTYPE_VECTOR2:\r\n                        offsetValue = _StaticOffsetValueVector2;\r\n                        break;\r\n                    // Size\r\n                    case Animation.ANIMATIONTYPE_SIZE:\r\n                        offsetValue = _StaticOffsetValueSize;\r\n                        break;\r\n                    // Color3\r\n                    case Animation.ANIMATIONTYPE_COLOR3:\r\n                        offsetValue = _StaticOffsetValueColor3;\r\n                        break;\r\n                    case Animation.ANIMATIONTYPE_COLOR4:\r\n                        offsetValue = _StaticOffsetValueColor4;\r\n                        break;\r\n                }\r\n            }\r\n\r\n            // Compute value\r\n\r\n            if (this._host && this._host.syncRoot) {\r\n                // If we must sync with an animatable, calculate the current frame based on the frame of the root animatable\r\n                const syncRoot = this._host.syncRoot;\r\n                const hostNormalizedFrame = (syncRoot.masterFrame - syncRoot.fromFrame) / (syncRoot.toFrame - syncRoot.fromFrame);\r\n                currentFrame = from + frameRange * hostNormalizedFrame;\r\n            } else {\r\n                if ((absoluteFrame > 0 && from > to) || (absoluteFrame < 0 && from < to)) {\r\n                    currentFrame = returnValue && frameRange !== 0 ? to + (absoluteFrame % frameRange) : from;\r\n                } else {\r\n                    currentFrame = returnValue && frameRange !== 0 ? from + (absoluteFrame % frameRange) : to;\r\n                }\r\n            }\r\n\r\n            // Reset event/state if looping\r\n            if ((!yoyoMode && ((speedRatio > 0 && this.currentFrame > currentFrame) || (speedRatio < 0 && this.currentFrame < currentFrame))) || (yoyoMode && yoyoLoop)) {\r\n                this._onLoop();\r\n\r\n                // Need to reset animation events\r\n                for (let index = 0; index < events.length; index++) {\r\n                    if (!events[index].onlyOnce) {\r\n                        // reset event, the animation is looping\r\n                        events[index].isDone = false;\r\n                    }\r\n                }\r\n\r\n                this._animationState.key = speedRatio > 0 ? 0 : animation.getKeys().length - 1;\r\n            }\r\n            this._currentFrame = currentFrame;\r\n            this._animationState.repeatCount = frameRange === 0 ? 0 : (absoluteFrame / frameRange) >> 0;\r\n            this._animationState.highLimitValue = highLimitValue;\r\n            this._animationState.offsetValue = offsetValue;\r\n        } else {\r\n            frameRange = to - from;\r\n            currentFrame = this._coreRuntimeAnimation.currentFrame;\r\n            this._currentFrame = currentFrame;\r\n            this._animationState.repeatCount = this._coreRuntimeAnimation._animationState.repeatCount;\r\n            this._animationState.highLimitValue = this._coreRuntimeAnimation._animationState.highLimitValue;\r\n            this._animationState.offsetValue = this._coreRuntimeAnimation._animationState.offsetValue;\r\n        }\r\n\r\n        const currentValue = animation._interpolate(currentFrame, this._animationState);\r\n\r\n        // Set value\r\n        this.setValue(currentValue, weight);\r\n\r\n        // Check events\r\n        if (events.length) {\r\n            for (let index = 0; index < events.length; index++) {\r\n                // Make sure current frame has passed event frame and that event frame is within the current range\r\n                // Also, handle both forward and reverse animations\r\n                if (\r\n                    (frameRange >= 0 && currentFrame >= events[index].frame && events[index].frame >= from) ||\r\n                    (frameRange < 0 && currentFrame <= events[index].frame && events[index].frame <= from)\r\n                ) {\r\n                    const event = events[index];\r\n                    if (!event.isDone) {\r\n                        // If event should be done only once, remove it.\r\n                        if (event.onlyOnce) {\r\n                            events.splice(index, 1);\r\n                            index--;\r\n                        }\r\n                        event.isDone = true;\r\n                        event.action(currentFrame);\r\n                    } // Don't do anything if the event has already been done.\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!returnValue) {\r\n            this._stopped = true;\r\n        }\r\n\r\n        return returnValue;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAE9C,OAAO,EACH,SAAS,EACT,wBAAwB,EACxB,wBAAwB,EACxB,4BAA4B,EAC5B,sBAAsB,EACtB,yBAAyB,EACzB,yBAAyB,GAC5B,MAAM,aAAa,CAAC;;;AASf,MAAO,gBAAgB;IAgHzB;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/C,CAAC;IAwEO,YAAY,CAAC,MAAW,EAAiB;YAAf,WAAW,oEAAG,CAAC;QAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;QAE9D,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,IAAI,QAAQ,GAAG,MAAM,CAAC;YACtB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;gBACjE,MAAM,IAAI,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBACvC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC1B,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACzB,MAAM,IAAI,KAAK,CAAC,4BAAqB,IAAI,EAAA,wBAAmD,OAA5B,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAA,EAAG,CAAC,CAAC;gBACrG,CAAC;YACL,CAAC;YAED,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACrE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC;QAChD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,4BAAqB,IAAI,CAAC,WAAW,EAAA,wBAAmD,EAAG,CAAC,CAAC,GAAjC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAA;QAC5G,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACI,KAAK,GAAwB;YAAvB,eAAe,oEAAG,KAAK;QAChC,IAAI,eAAe,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,OAAO,YAAY,KAAK,EAAE,CAAC;gBAChC,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;oBAChC,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC;wBAC3C,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC9F,CAAC;oBACD,KAAK,EAAE,CAAC;gBACZ,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;oBACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpF,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,CAAA,CAAE,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,CAAA,CAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,SAAS;QACT,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QACvC,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,QAAQ,CAAC,YAAiB,EAAE,MAAc,EAAA;QAC7C,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACvD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACpF,CAAC;YACD,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEO,kBAAkB,GAAgB;0BAAf,WAAW,sDAAG,CAAC;QACtC,IAAI,aAAkB,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAEhD,IAAI,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAC1D,YAAY;YACZ,aAAa,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAC5C,CAAC,MAAM,CAAC;YACJ,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;QAC7D,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;QACrD,CAAC;IACL,CAAC;IAEO,sCAAsC,CAAC,gBAAkC,EAAE,aAAkB,EAAA;QACjG,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,mCAAmC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAExE,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAChC,MAAM,CAAC,qBAAqB,GAAG,CAAA,CAAE,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7D,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG;gBACxD,WAAW,EAAE,CAAC;gBACd,mBAAmB,EAAE,CAAC;gBACtB,UAAU,EAAE,EAAE;gBACd,kBAAkB,EAAE,EAAE;gBACtB,aAAa,EAAE,aAAa;aAC/B,CAAC;QACN,CAAC;QAED,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;YAC9B,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACpG,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,mBAAmB,IAAI,gBAAgB,CAAC,MAAM,CAAC;QAC7G,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5F,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,WAAW,IAAI,gBAAgB,CAAC,MAAM,CAAC;QACrG,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,MAAW,EAAE,WAAgB,EAAE,YAAiB,EAAE,MAAc,EAAE,WAAmB,EAAA;QACnG,YAAY;QACZ,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;QAExC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,IAAI,GAAG,EAAE,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC5B,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAEpD,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;oBACtB,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;gBACrD,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC;gBAC7C,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC;gBAC7B,SAAS;gBACT,IAAI,8KAAS,CAAC,oCAAoC,EAAE,CAAC;oBACjD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;0LACrB,SAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBAChH,CAAC,MAAM,CAAC;wBACJ,IAAI,CAAC,aAAa,qKAAG,SAAM,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC5G,CAAC;gBACL,CAAC,MAAM,CAAC;oBACJ,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;wBACrB,2KAAM,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBACvG,CAAC,MAAM,CAAC;wBACJ,IAAI,CAAC,aAAa,oKAAG,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBACnG,CAAC;gBACL,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,qKAAG,YAAS,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAChH,CAAC;YAED,MAAM,aAAa,GAAG,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;YACtJ,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC;QAC1C,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACtB,gEAAI,YAAY,CAAE,KAAK,EAAE,CAAC;oBACtB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC;gBAC9C,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;gBACtC,CAAC;YACL,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;gBACrC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC9C,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YACtC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC;YAClB,IAAI,CAAC,sCAAsC,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;QACxF,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,uKAAK,YAAS,CAAC,uCAAuC,EAAE,CAAC;gBACtF,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC9B,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;gBACjG,CAAC,MAAM,CAAC;oBACJ,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;gBAC1F,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;YACvD,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;;OAGG,CACK,mBAAmB,GAAA;QACvB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,QAAkB,CAAC;QACvE,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED;;;;OAIG,CACI,SAAS,CAAC,KAAa,EAAa;qBAAX,MAAM,2DAAG,CAAC,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACxB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC1B,CAAC,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YAC7C,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QACxC,CAAC;QAED,iCAAiC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC1B,6BAA6B;oBAC7B,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;gBACvD,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAE/E,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG,CACI,2BAA2B,CAAC,aAAqB,EAAA;QACpD,MAAM,gBAAgB,GAAG,AAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,aAAa,CAAC,CAAC,EAAG,MAAM,CAAC;QAEjH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,GAAG,gBAAgB,CAAC;IAC/E,CAAC;IAED;;;;;;;;;OASG,CACI,OAAO,CAAC,8BAAsC,EAAE,IAAY,EAAE,EAAU,EAAE,IAAa,EAAE,UAAkB,EAAe;qBAAb,MAAM,2DAAG,CAAC,GAAG;QAC7H,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,CAAC;QACxD,IAAI,CAAC,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,IAAI,YAAoB,CAAC;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC9B,eAAe;YACf,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjD,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;YAC1B,CAAC;YACD,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC7C,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;YACxB,CAAC;YAED,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC;YACvB,IAAI,WAAgB,CAAC;YAErB,sHAAsH;YACtH,IAAI,aAAa,GAAG,AAAC,8BAA8B,GAAG,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,EAAG,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACpI,IAAI,cAAc,GAAG,CAAC,CAAC;YAEvB,sCAAsC;YACtC,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,MAAM,QAAQ,GAAG,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,uKAAK,YAAS,CAAC,sBAAsB,CAAC;YAC5F,IAAI,QAAQ,EAAE,CAAC;gBACX,MAAM,QAAQ,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,UAAU,CAAC;gBAErD,uBAAuB;gBACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAEnC,0CAA0C;gBAC1C,aAAa,GAAG,YAAY,GAAG,UAAU,GAAG,IAAI,CAAC;gBAEjD,MAAM,SAAS,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;oBACpC,QAAQ,GAAG,IAAI,CAAC;gBACpB,CAAC;gBAED,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YACpC,CAAC;YAED,IAAI,CAAC,oBAAoB,GAAG,8BAA8B,CAAC;YAC3D,IAAI,CAAC,sBAAsB,GAAG,aAAa,CAAC;YAE5C,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,AAAC,aAAa,IAAI,UAAU,IAAI,UAAU,GAAG,CAAC,CAAC,GAAK,CAAD,YAAc,IAAI,CAAC,IAAI,UAAU,GAAG,CAAC,AAAC,CAAC,EAAE,CAAC;gBACrH,4DAA4D;gBAC5D,WAAW,GAAG,KAAK,CAAC;gBACpB,cAAc,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5D,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,AAAC,aAAa,IAAI,UAAU,IAAI,UAAU,GAAG,CAAC,CAAC,GAAK,CAAD,YAAc,IAAI,CAAC,IAAI,UAAU,GAAG,CAAC,AAAC,CAAC,EAAE,CAAC;gBAC5H,WAAW,GAAG,KAAK,CAAC;gBACpB,cAAc,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5D,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,uKAAK,YAAS,CAAC,uBAAuB,EAAE,CAAC;gBAC7E,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;oBACjC,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,CAAC,CAAC;oBACrC,IAAI,CAAC,eAAe,CAAC,QAAQ,qKAAG,YAAS,CAAC,uBAAuB,CAAC,CAAC,yDAAyD;oBAC5H,MAAM,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBACrE,MAAM,OAAO,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBAEjE,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3D,OAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;wBACzB,QAAQ;wBACR,uKAAK,YAAS,CAAC,mBAAmB;4BAC9B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;4BACpD,MAAM;wBACV,aAAa;wBACb,uKAAK,YAAS,CAAC,wBAAwB;4BACnC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV,UAAU;wBACV,uKAAK,YAAS,CAAC,qBAAqB;4BAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV,UAAU;wBACV,sKAAK,aAAS,CAAC,qBAAqB;4BAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV,OAAO;wBACP,uKAAK,YAAS,CAAC,kBAAkB;4BAC7B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV,SAAS;wBACT,uKAAK,YAAS,CAAC,oBAAoB;4BAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV;4BACI,MAAM;oBACd,CAAC;oBAED,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;gBAC/C,CAAC;gBAED,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;gBAClD,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC5B,OAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;oBACzB,QAAQ;oBACR,uKAAK,YAAS,CAAC,mBAAmB;wBAC9B,WAAW,GAAG,CAAC,CAAC;wBAChB,MAAM;oBACV,aAAa;oBACb,uKAAK,YAAS,CAAC,wBAAwB;wBACnC,WAAW,oKAAG,gCAA4B,CAAC;wBAC3C,MAAM;oBACV,UAAU;oBACV,uKAAK,YAAS,CAAC,qBAAqB;wBAChC,WAAW,qKAAG,4BAAyB,CAAC;wBACxC,MAAM;oBACV,UAAU;oBACV,sKAAK,aAAS,CAAC,qBAAqB;wBAChC,WAAW,qKAAG,4BAAyB,CAAC;wBACxC,MAAM;oBACV,OAAO;oBACP,uKAAK,YAAS,CAAC,kBAAkB;wBAC7B,WAAW,qKAAG,yBAAsB,CAAC;wBACrC,MAAM;oBACV,SAAS;oBACT,uKAAK,YAAS,CAAC,oBAAoB;wBAC/B,WAAW,qKAAG,2BAAwB,CAAC;wBACvC,MAAM;oBACV,sKAAK,aAAS,CAAC,oBAAoB;wBAC/B,WAAW,qKAAG,2BAAwB,CAAC;wBACvC,MAAM;gBACd,CAAC;YACL,CAAC;YAED,gBAAgB;YAEhB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACpC,4GAA4G;gBAC5G,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACrC,MAAM,mBAAmB,GAAG,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAClH,YAAY,GAAG,IAAI,GAAG,UAAU,GAAG,mBAAmB,CAAC;YAC3D,CAAC,MAAM,CAAC;gBACJ,IAAI,AAAC,aAAa,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,GAAK,CAAD,YAAc,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAE,CAAC;oBACvE,YAAY,GAAG,WAAW,IAAI,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,AAAC,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC;gBAC9F,CAAC,MAAM,CAAC;oBACJ,YAAY,GAAG,WAAW,IAAI,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,AAAC,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,AAAC,EAAE,CAAC;gBAC9F,CAAC;YACL,CAAC;YAED,+BAA+B;YAC/B,IAAK,AAAD,CAAE,QAAQ,IAAI,CAAC,AAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,GAAK,CAAD,SAAW,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,AAAC,CAAC,CAAC,GAAK,CAAD,OAAS,IAAI,QAAQ,CAAC,CAAE,CAAC;gBAC1J,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEf,iCAAiC;gBACjC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;wBAC1B,wCAAwC;wBACxC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;oBACjC,CAAC;gBACL,CAAC;gBAED,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;YACnF,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,aAAa,GAAG,UAAU,CAAC,GAAI,CAAC,CAAC;YAC5F,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,cAAc,CAAC;YACrD,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,WAAW,CAAC;QACnD,CAAC,MAAM,CAAC;YACJ,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC;YACvB,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC;YACvD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,WAAW,CAAC;YAC1F,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,cAAc,CAAC;YAChG,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,WAAW,CAAC;QAC9F,CAAC;QAED,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAEhF,YAAY;QACZ,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAEpC,eAAe;QACf,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACjD,kGAAkG;gBAClG,mDAAmD;gBACnD,IACI,AAAC,UAAU,IAAI,CAAC,IAAI,YAAY,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,GACtF,UAAU,GAAG,CAAC,IAAI,YAAY,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,CACxF,CAAC;oBACC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAChB,gDAAgD;wBAChD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;4BACjB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;4BACxB,KAAK,EAAE,CAAC;wBACZ,CAAC;wBACD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;wBACpB,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;oBAC/B,CAAC,CAAC,wDAAwD;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACzB,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IA5iBD;;;;;;OAMG,CACH,YAAmB,MAAW,EAAE,SAAoB,EAAE,KAAY,EAAE,IAAgB,CAAA;QAnK5E,IAAA,CAAA,OAAO,GAAG,IAAI,KAAK,EAAkB,CAAC;QAE9C;;WAEG,CACK,IAAA,CAAA,aAAa,GAAW,CAAC,CAAC;QAiBlC;;WAEG,CACK,IAAA,CAAA,cAAc,GAAG,IAAI,KAAK,EAAO,CAAC;QAE1C;;WAEG,CACK,IAAA,CAAA,mBAAmB,GAAkB,IAAI,CAAC;QAElD;;WAEG,CACK,IAAA,CAAA,aAAa,GAA2B,CAAA,CAAE,CAAC;QAEnD;;WAEG,CACK,IAAA,CAAA,gBAAgB,GAA2B,CAAA,CAAE,CAAC;QAEtD;;WAEG,CACK,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QAEzB;;WAEG,CACK,IAAA,CAAA,eAAe,GAAG,CAAC,CAAC;QAO5B;;WAEG,CACK,IAAA,CAAA,aAAa,GAAkB,IAAI,CAAC;QASpC,IAAA,CAAA,oBAAoB,GAAkB,IAAI,CAAC;QAC3C,IAAA,CAAA,aAAa,GAAkB,IAAI,CAAC;QAE5C;;WAEG,CACK,IAAA,CAAA,WAAW,GAAW,EAAE,CAAC;QAEjC;;WAEG,CACK,IAAA,CAAA,OAAO,GAAG,GAAG,CAAC;QAEtB;;WAEG,CACK,IAAA,CAAA,oBAAoB,GAAG,CAAC,CAAC;QAEjC;;WAEG,CACK,IAAA,CAAA,oBAAoB,GAAW,CAAC,CAAC;QAEjC,IAAA,CAAA,cAAc,GAAW,CAAC,CAAC;QAEnC;;WAEG,CACK,IAAA,CAAA,sBAAsB,GAAW,CAAC,CAAC;QASnC,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAE/B,cAAA,EAAgB,CACT,IAAA,CAAA,qBAAqB,GAA4B,IAAI,CAAC;QAuDzD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExC,QAAQ;QACR,IAAI,CAAC,eAAe,GAAG;YACnB,GAAG,EAAE,CAAC;YACN,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,IAAI,CAAC,mBAAmB,EAAE;SACvC,CAAC;QAEF,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,uKAAK,YAAS,CAAC,oBAAoB,EAAE,CAAC;YAC9D,IAAI,CAAC,eAAe,CAAC,SAAS,qKAAG,SAAM,CAAC,IAAI,EAAE,CAAC;QACnD,CAAC;QAED,SAAS;QACT,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QAEzD,wCAAwC;QACxC,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG;gBAAE,KAAK,EAAE,CAAC;gBAAE,KAAK,EAAE,IAAI,CAAC,SAAS;YAAA,CAAE,CAAC;YACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,OAAO,YAAY,KAAK,EAAE,CAAC;YAChC,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;gBAChC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBAC/B,KAAK,EAAE,CAAC;YACZ,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;QAED,yBAAyB;QACzB,MAAM,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;QACrC,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,KAAK,MAAM,CAAC,IAAI,MAAM,CAAE,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;YAClC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAC7J,CAAC;CA4eJ", "debugId": null}}, {"offset": {"line": 1877, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animatable.core.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animatable.core.ts"], "sourcesContent": ["import { Observable } from \"core/Misc/observable\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { Nullable } from \"core/types\";\r\nimport { RuntimeAnimation } from \"./runtimeAnimation\";\r\nimport { Animation } from \"./animation\";\r\nimport { PrecisionDate } from \"core/Misc/precisionDate\";\r\nimport { Matrix, Quaternion, TmpVectors, Vector3 } from \"core/Maths/math.vector\";\r\nimport type { Bone } from \"core/Bones/bone\";\r\nimport type { Node } from \"../node\";\r\n\r\n/**\r\n * Class used to store an actual running animation\r\n */\r\nexport class Animatable {\r\n    /**\r\n     * If true, the animatable will be processed even if it is considered actively paused (weight of 0 and previous weight of 0).\r\n     * This can be used to force the full processing of paused animatables in the animation engine.\r\n     * Default is false.\r\n     */\r\n    public static ProcessPausedAnimatables = false;\r\n\r\n    private _localDelayOffset: Nullable<number> = null;\r\n    private _pausedDelay: Nullable<number> = null;\r\n    private _manualJumpDelay: Nullable<number> = null;\r\n    /** @hidden */\r\n    public _runtimeAnimations = new Array<RuntimeAnimation>();\r\n    private _paused = false;\r\n    private _scene: Scene;\r\n    private _speedRatio = 1;\r\n    private _weight = -1.0;\r\n    private _previousWeight = -1.0;\r\n    private _syncRoot: Nullable<Animatable> = null;\r\n    private _frameToSyncFromJump: Nullable<number> = null;\r\n    private _goToFrame: Nullable<number> = null;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the animatable must be disposed and removed at the end of the animation.\r\n     * This will only apply for non looping animation (default is true)\r\n     */\r\n    public disposeOnEnd = true;\r\n\r\n    /**\r\n     * Gets a boolean indicating if the animation has started\r\n     */\r\n    public animationStarted = false;\r\n\r\n    /**\r\n     * Observer raised when the animation ends\r\n     */\r\n    public onAnimationEndObservable = new Observable<Animatable>();\r\n\r\n    /**\r\n     * Observer raised when the animation loops\r\n     */\r\n    public onAnimationLoopObservable = new Observable<Animatable>();\r\n\r\n    /**\r\n     * Gets the root Animatable used to synchronize and normalize animations\r\n     */\r\n    public get syncRoot(): Nullable<Animatable> {\r\n        return this._syncRoot;\r\n    }\r\n\r\n    /**\r\n     * Gets the current frame of the first RuntimeAnimation\r\n     * Used to synchronize Animatables\r\n     */\r\n    public get masterFrame(): number {\r\n        if (this._runtimeAnimations.length === 0) {\r\n            return 0;\r\n        }\r\n\r\n        return this._runtimeAnimations[0].currentFrame;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the animatable weight (-1.0 by default meaning not weighted)\r\n     */\r\n    public get weight(): number {\r\n        return this._weight;\r\n    }\r\n\r\n    public set weight(value: number) {\r\n        if (value === -1) {\r\n            // -1 is ok and means no weight\r\n            this._weight = -1;\r\n            return;\r\n        }\r\n\r\n        // Else weight must be in [0, 1] range\r\n        this._weight = Math.min(Math.max(value, 0), 1.0);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the speed ratio to apply to the animatable (1.0 by default)\r\n     */\r\n    public get speedRatio(): number {\r\n        return this._speedRatio;\r\n    }\r\n\r\n    public set speedRatio(value: number) {\r\n        for (let index = 0; index < this._runtimeAnimations.length; index++) {\r\n            const animation = this._runtimeAnimations[index];\r\n\r\n            animation._prepareForSpeedRatioChange(value);\r\n        }\r\n        this._speedRatio = value;\r\n\r\n        // Resync _manualJumpDelay in case goToFrame was called before speedRatio was set.\r\n        if (this._goToFrame !== null) {\r\n            this.goToFrame(this._goToFrame);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the elapsed time since the animatable started in milliseconds\r\n     */\r\n    public get elapsedTime(): number {\r\n        return this._localDelayOffset === null ? 0 : this._scene._animationTime - this._localDelayOffset;\r\n    }\r\n\r\n    /**\r\n     * Creates a new Animatable\r\n     * @param scene defines the hosting scene\r\n     * @param target defines the target object\r\n     * @param fromFrame defines the starting frame number (default is 0)\r\n     * @param toFrame defines the ending frame number (default is 100)\r\n     * @param loopAnimation defines if the animation must loop (default is false)\r\n     * @param speedRatio defines the factor to apply to animation speed (default is 1)\r\n     * @param onAnimationEnd defines a callback to call when animation ends if it is not looping\r\n     * @param animations defines a group of animation to add to the new Animatable\r\n     * @param onAnimationLoop defines a callback to call when animation loops\r\n     * @param isAdditive defines whether the animation should be evaluated additively\r\n     * @param playOrder defines the order in which this animatable should be processed in the list of active animatables (default: 0)\r\n     */\r\n    constructor(\r\n        scene: Scene,\r\n        /** defines the target object */\r\n        public target: any,\r\n        /** [0] defines the starting frame number (default is 0) */\r\n        public fromFrame: number = 0,\r\n        /** [100] defines the ending frame number (default is 100) */\r\n        public toFrame: number = 100,\r\n        /** [false] defines if the animation must loop (default is false)  */\r\n        public loopAnimation: boolean = false,\r\n        speedRatio: number = 1.0,\r\n        /** defines a callback to call when animation ends if it is not looping */\r\n        public onAnimationEnd?: Nullable<() => void>,\r\n        animations?: Animation[],\r\n        /** defines a callback to call when animation loops */\r\n        public onAnimationLoop?: Nullable<() => void>,\r\n        /** [false] defines whether the animation should be evaluated additively */\r\n        public isAdditive: boolean = false,\r\n        /** [0] defines the order in which this animatable should be processed in the list of active animatables (default: 0) */\r\n        public playOrder = 0\r\n    ) {\r\n        this._scene = scene;\r\n        if (animations) {\r\n            this.appendAnimations(target, animations);\r\n        }\r\n\r\n        this._speedRatio = speedRatio;\r\n        scene._activeAnimatables.push(this);\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Synchronize and normalize current Animatable with a source Animatable\r\n     * This is useful when using animation weights and when animations are not of the same length\r\n     * @param root defines the root Animatable to synchronize with (null to stop synchronizing)\r\n     * @returns the current Animatable\r\n     */\r\n    public syncWith(root: Nullable<Animatable>): Animatable {\r\n        this._syncRoot = root;\r\n\r\n        if (root) {\r\n            // Make sure this animatable will animate after the root\r\n            const index = this._scene._activeAnimatables.indexOf(this);\r\n            if (index > -1) {\r\n                this._scene._activeAnimatables.splice(index, 1);\r\n                this._scene._activeAnimatables.push(this);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of runtime animations\r\n     * @returns an array of RuntimeAnimation\r\n     */\r\n    public getAnimations(): RuntimeAnimation[] {\r\n        return this._runtimeAnimations;\r\n    }\r\n\r\n    /**\r\n     * Adds more animations to the current animatable\r\n     * @param target defines the target of the animations\r\n     * @param animations defines the new animations to add\r\n     */\r\n    public appendAnimations(target: any, animations: Animation[]): void {\r\n        for (let index = 0; index < animations.length; index++) {\r\n            const animation = animations[index];\r\n\r\n            const newRuntimeAnimation = new RuntimeAnimation(target, animation, this._scene, this);\r\n            newRuntimeAnimation._onLoop = () => {\r\n                this.onAnimationLoopObservable.notifyObservers(this);\r\n                if (this.onAnimationLoop) {\r\n                    this.onAnimationLoop();\r\n                }\r\n            };\r\n\r\n            this._runtimeAnimations.push(newRuntimeAnimation);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the source animation for a specific property\r\n     * @param property defines the property to look for\r\n     * @returns null or the source animation for the given property\r\n     */\r\n    public getAnimationByTargetProperty(property: string): Nullable<Animation> {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            if (runtimeAnimations[index].animation.targetProperty === property) {\r\n                return runtimeAnimations[index].animation;\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Gets the runtime animation for a specific property\r\n     * @param property defines the property to look for\r\n     * @returns null or the runtime animation for the given property\r\n     */\r\n    public getRuntimeAnimationByTargetProperty(property: string): Nullable<RuntimeAnimation> {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            if (runtimeAnimations[index].animation.targetProperty === property) {\r\n                return runtimeAnimations[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Resets the animatable to its original state\r\n     */\r\n    public reset(): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].reset(true);\r\n        }\r\n\r\n        this._localDelayOffset = null;\r\n        this._pausedDelay = null;\r\n    }\r\n\r\n    /**\r\n     * Allows the animatable to blend with current running animations\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\r\n     * @param blendingSpeed defines the blending speed to use\r\n     */\r\n    public enableBlending(blendingSpeed: number): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].animation.enableBlending = true;\r\n            runtimeAnimations[index].animation.blendingSpeed = blendingSpeed;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disable animation blending\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-blending\r\n     */\r\n    public disableBlending(): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].animation.enableBlending = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Jump directly to a given frame\r\n     * @param frame defines the frame to jump to\r\n     * @param useWeight defines whether the animation weight should be applied to the image to be jumped to (false by default)\r\n     */\r\n    public goToFrame(frame: number, useWeight = false): void {\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n\r\n        if (runtimeAnimations[0]) {\r\n            const fps = runtimeAnimations[0].animation.framePerSecond;\r\n            this._frameToSyncFromJump = this._frameToSyncFromJump ?? runtimeAnimations[0].currentFrame;\r\n            const delay = this.speedRatio === 0 ? 0 : (((frame - this._frameToSyncFromJump) / fps) * 1000) / this.speedRatio;\r\n            this._manualJumpDelay = -delay;\r\n        }\r\n\r\n        for (let index = 0; index < runtimeAnimations.length; index++) {\r\n            runtimeAnimations[index].goToFrame(frame, useWeight ? this._weight : -1);\r\n        }\r\n\r\n        this._goToFrame = frame;\r\n    }\r\n\r\n    /**\r\n     * Returns true if the animations for this animatable are paused\r\n     */\r\n    public get paused() {\r\n        return this._paused;\r\n    }\r\n\r\n    /**\r\n     * Pause the animation\r\n     */\r\n    public pause(): void {\r\n        if (this._paused) {\r\n            return;\r\n        }\r\n        this._paused = true;\r\n    }\r\n\r\n    /**\r\n     * Restart the animation\r\n     */\r\n    public restart(): void {\r\n        this._paused = false;\r\n    }\r\n\r\n    private _raiseOnAnimationEnd() {\r\n        if (this.onAnimationEnd) {\r\n            this.onAnimationEnd();\r\n        }\r\n\r\n        this.onAnimationEndObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Stop and delete the current animation\r\n     * @param animationName defines a string used to only stop some of the runtime animations instead of all\r\n     * @param targetMask a function that determines if the animation should be stopped based on its target (all animations will be stopped if both this and animationName are empty)\r\n     * @param useGlobalSplice if true, the animatables will be removed by the caller of this function (false by default)\r\n     * @param skipOnAnimationEnd defines if the system should not raise onAnimationEnd. Default is false\r\n     */\r\n    public stop(animationName?: string, targetMask?: (target: any) => boolean, useGlobalSplice = false, skipOnAnimationEnd = false): void {\r\n        if (animationName || targetMask) {\r\n            const idx = this._scene._activeAnimatables.indexOf(this);\r\n\r\n            if (idx > -1) {\r\n                const runtimeAnimations = this._runtimeAnimations;\r\n\r\n                for (let index = runtimeAnimations.length - 1; index >= 0; index--) {\r\n                    const runtimeAnimation = runtimeAnimations[index];\r\n                    if (animationName && runtimeAnimation.animation.name != animationName) {\r\n                        continue;\r\n                    }\r\n                    if (targetMask && !targetMask(runtimeAnimation.target)) {\r\n                        continue;\r\n                    }\r\n\r\n                    runtimeAnimation.dispose();\r\n                    runtimeAnimations.splice(index, 1);\r\n                }\r\n\r\n                if (runtimeAnimations.length == 0) {\r\n                    if (!useGlobalSplice) {\r\n                        this._scene._activeAnimatables.splice(idx, 1);\r\n                    }\r\n                    if (!skipOnAnimationEnd) {\r\n                        this._raiseOnAnimationEnd();\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const index = this._scene._activeAnimatables.indexOf(this);\r\n\r\n            if (index > -1) {\r\n                if (!useGlobalSplice) {\r\n                    this._scene._activeAnimatables.splice(index, 1);\r\n                }\r\n                const runtimeAnimations = this._runtimeAnimations;\r\n\r\n                for (let index = 0; index < runtimeAnimations.length; index++) {\r\n                    runtimeAnimations[index].dispose();\r\n                }\r\n\r\n                this._runtimeAnimations.length = 0;\r\n\r\n                if (!skipOnAnimationEnd) {\r\n                    this._raiseOnAnimationEnd();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Wait asynchronously for the animation to end\r\n     * @returns a promise which will be fulfilled when the animation ends\r\n     */\r\n    public async waitAsync(): Promise<Animatable> {\r\n        return await new Promise((resolve) => {\r\n            this.onAnimationEndObservable.add(\r\n                () => {\r\n                    resolve(this);\r\n                },\r\n                undefined,\r\n                undefined,\r\n                this,\r\n                true\r\n            );\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _animate(delay: number): boolean {\r\n        if (this._paused) {\r\n            this.animationStarted = false;\r\n            if (this._pausedDelay === null) {\r\n                this._pausedDelay = delay;\r\n            }\r\n            return true;\r\n        }\r\n\r\n        if (this._localDelayOffset === null) {\r\n            this._localDelayOffset = delay;\r\n            this._pausedDelay = null;\r\n        } else if (this._pausedDelay !== null) {\r\n            this._localDelayOffset += delay - this._pausedDelay;\r\n            this._pausedDelay = null;\r\n        }\r\n\r\n        if (this._manualJumpDelay !== null) {\r\n            this._localDelayOffset += this.speedRatio < 0 ? -this._manualJumpDelay : this._manualJumpDelay;\r\n            this._manualJumpDelay = null;\r\n            this._frameToSyncFromJump = null;\r\n        }\r\n\r\n        this._goToFrame = null;\r\n\r\n        if (!Animatable.ProcessPausedAnimatables && this._weight === 0 && this._previousWeight === 0) {\r\n            // We consider that an animatable with a weight === 0 is \"actively\" paused\r\n            return true;\r\n        }\r\n\r\n        this._previousWeight = this._weight;\r\n\r\n        // Animating\r\n        let running = false;\r\n        const runtimeAnimations = this._runtimeAnimations;\r\n        let index: number;\r\n\r\n        for (index = 0; index < runtimeAnimations.length; index++) {\r\n            const animation = runtimeAnimations[index];\r\n            const isRunning = animation.animate(delay - this._localDelayOffset, this.fromFrame, this.toFrame, this.loopAnimation, this._speedRatio, this._weight);\r\n            running = running || isRunning;\r\n        }\r\n\r\n        this.animationStarted = running;\r\n\r\n        if (!running) {\r\n            if (this.disposeOnEnd) {\r\n                // Remove from active animatables\r\n                index = this._scene._activeAnimatables.indexOf(this);\r\n                this._scene._activeAnimatables.splice(index, 1);\r\n\r\n                // Dispose all runtime animations\r\n                for (index = 0; index < runtimeAnimations.length; index++) {\r\n                    runtimeAnimations[index].dispose();\r\n                }\r\n            }\r\n\r\n            this._raiseOnAnimationEnd();\r\n\r\n            if (this.disposeOnEnd) {\r\n                this.onAnimationEnd = null;\r\n                this.onAnimationLoop = null;\r\n                this.onAnimationLoopObservable.clear();\r\n                this.onAnimationEndObservable.clear();\r\n            }\r\n        }\r\n\r\n        return running;\r\n    }\r\n}\r\n\r\n/** @internal */\r\nfunction ProcessLateAnimationBindingsForMatrices(holder: {\r\n    totalWeight: number;\r\n    totalAdditiveWeight: number;\r\n    animations: RuntimeAnimation[];\r\n    additiveAnimations: RuntimeAnimation[];\r\n    originalValue: Matrix;\r\n}): any {\r\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight === 0) {\r\n        return holder.originalValue;\r\n    }\r\n\r\n    let normalizer = 1.0;\r\n    const finalPosition = TmpVectors.Vector3[0];\r\n    const finalScaling = TmpVectors.Vector3[1];\r\n    const finalQuaternion = TmpVectors.Quaternion[0];\r\n    let startIndex = 0;\r\n    const originalAnimation = holder.animations[0];\r\n    const originalValue = holder.originalValue;\r\n\r\n    let scale = 1;\r\n    let skipOverride = false;\r\n    if (holder.totalWeight < 1.0) {\r\n        // We need to mix the original value in\r\n        scale = 1.0 - holder.totalWeight;\r\n        originalValue.decompose(finalScaling, finalQuaternion, finalPosition);\r\n    } else {\r\n        startIndex = 1;\r\n        // We need to normalize the weights\r\n        normalizer = holder.totalWeight;\r\n        scale = originalAnimation.weight / normalizer;\r\n        if (scale == 1) {\r\n            if (holder.totalAdditiveWeight) {\r\n                skipOverride = true;\r\n            } else {\r\n                return originalAnimation.currentValue;\r\n            }\r\n        }\r\n\r\n        originalAnimation.currentValue.decompose(finalScaling, finalQuaternion, finalPosition);\r\n    }\r\n\r\n    // Add up the override animations\r\n    if (!skipOverride) {\r\n        finalScaling.scaleInPlace(scale);\r\n        finalPosition.scaleInPlace(scale);\r\n        finalQuaternion.scaleInPlace(scale);\r\n\r\n        for (let animIndex = startIndex; animIndex < holder.animations.length; animIndex++) {\r\n            const runtimeAnimation = holder.animations[animIndex];\r\n            if (runtimeAnimation.weight === 0) {\r\n                continue;\r\n            }\r\n\r\n            scale = runtimeAnimation.weight / normalizer;\r\n            const currentPosition = TmpVectors.Vector3[2];\r\n            const currentScaling = TmpVectors.Vector3[3];\r\n            const currentQuaternion = TmpVectors.Quaternion[1];\r\n\r\n            runtimeAnimation.currentValue.decompose(currentScaling, currentQuaternion, currentPosition);\r\n\r\n            currentScaling.scaleAndAddToRef(scale, finalScaling);\r\n            currentQuaternion.scaleAndAddToRef(Quaternion.Dot(finalQuaternion, currentQuaternion) > 0 ? scale : -scale, finalQuaternion);\r\n            currentPosition.scaleAndAddToRef(scale, finalPosition);\r\n        }\r\n\r\n        finalQuaternion.normalize();\r\n    }\r\n\r\n    // Add up the additive animations\r\n    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\r\n        const runtimeAnimation = holder.additiveAnimations[animIndex];\r\n        if (runtimeAnimation.weight === 0) {\r\n            continue;\r\n        }\r\n\r\n        const currentPosition = TmpVectors.Vector3[2];\r\n        const currentScaling = TmpVectors.Vector3[3];\r\n        const currentQuaternion = TmpVectors.Quaternion[1];\r\n\r\n        runtimeAnimation.currentValue.decompose(currentScaling, currentQuaternion, currentPosition);\r\n        currentScaling.multiplyToRef(finalScaling, currentScaling);\r\n        Vector3.LerpToRef(finalScaling, currentScaling, runtimeAnimation.weight, finalScaling);\r\n        finalQuaternion.multiplyToRef(currentQuaternion, currentQuaternion);\r\n        Quaternion.SlerpToRef(finalQuaternion, currentQuaternion, runtimeAnimation.weight, finalQuaternion);\r\n        currentPosition.scaleAndAddToRef(runtimeAnimation.weight, finalPosition);\r\n    }\r\n\r\n    const workValue = originalAnimation ? originalAnimation._animationState.workValue : TmpVectors.Matrix[0].clone();\r\n    Matrix.ComposeToRef(finalScaling, finalQuaternion, finalPosition, workValue);\r\n    return workValue;\r\n}\r\n\r\n/** @internal */\r\nfunction ProcessLateAnimationBindingsForQuaternions(\r\n    holder: {\r\n        totalWeight: number;\r\n        totalAdditiveWeight: number;\r\n        animations: RuntimeAnimation[];\r\n        additiveAnimations: RuntimeAnimation[];\r\n        originalValue: Quaternion;\r\n    },\r\n    refQuaternion: Quaternion\r\n): Quaternion {\r\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight === 0) {\r\n        return refQuaternion;\r\n    }\r\n\r\n    const originalAnimation = holder.animations[0];\r\n    const originalValue = holder.originalValue;\r\n    let cumulativeQuaternion = refQuaternion;\r\n\r\n    if (holder.totalWeight === 0 && holder.totalAdditiveWeight > 0) {\r\n        cumulativeQuaternion.copyFrom(originalValue);\r\n    } else if (holder.animations.length === 1) {\r\n        Quaternion.SlerpToRef(originalValue, originalAnimation.currentValue, Math.min(1.0, holder.totalWeight), cumulativeQuaternion);\r\n\r\n        if (holder.totalAdditiveWeight === 0) {\r\n            return cumulativeQuaternion;\r\n        }\r\n    } else if (holder.animations.length > 1) {\r\n        // Add up the override animations\r\n        let normalizer = 1.0;\r\n        let quaternions: Array<Quaternion>;\r\n        let weights: Array<number>;\r\n\r\n        if (holder.totalWeight < 1.0) {\r\n            const scale = 1.0 - holder.totalWeight;\r\n\r\n            quaternions = [];\r\n            weights = [];\r\n\r\n            quaternions.push(originalValue);\r\n            weights.push(scale);\r\n        } else {\r\n            if (holder.animations.length === 2) {\r\n                // Slerp as soon as we can\r\n                Quaternion.SlerpToRef(holder.animations[0].currentValue, holder.animations[1].currentValue, holder.animations[1].weight / holder.totalWeight, refQuaternion);\r\n\r\n                if (holder.totalAdditiveWeight === 0) {\r\n                    return refQuaternion;\r\n                }\r\n            }\r\n\r\n            quaternions = [];\r\n            weights = [];\r\n            normalizer = holder.totalWeight;\r\n        }\r\n\r\n        for (let animIndex = 0; animIndex < holder.animations.length; animIndex++) {\r\n            const runtimeAnimation = holder.animations[animIndex];\r\n            quaternions.push(runtimeAnimation.currentValue);\r\n            weights.push(runtimeAnimation.weight / normalizer);\r\n        }\r\n\r\n        // https://gamedev.stackexchange.com/questions/62354/method-for-interpolation-between-3-quaternions\r\n\r\n        let cumulativeAmount = 0;\r\n        for (let index = 0; index < quaternions.length; ) {\r\n            if (!index) {\r\n                Quaternion.SlerpToRef(quaternions[index], quaternions[index + 1], weights[index + 1] / (weights[index] + weights[index + 1]), refQuaternion);\r\n                cumulativeQuaternion = refQuaternion;\r\n                cumulativeAmount = weights[index] + weights[index + 1];\r\n                index += 2;\r\n                continue;\r\n            }\r\n            cumulativeAmount += weights[index];\r\n            Quaternion.SlerpToRef(cumulativeQuaternion, quaternions[index], weights[index] / cumulativeAmount, cumulativeQuaternion);\r\n            index++;\r\n        }\r\n    }\r\n\r\n    // Add up the additive animations\r\n    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\r\n        const runtimeAnimation = holder.additiveAnimations[animIndex];\r\n        if (runtimeAnimation.weight === 0) {\r\n            continue;\r\n        }\r\n\r\n        cumulativeQuaternion.multiplyToRef(runtimeAnimation.currentValue, TmpVectors.Quaternion[0]);\r\n        Quaternion.SlerpToRef(cumulativeQuaternion, TmpVectors.Quaternion[0], runtimeAnimation.weight, cumulativeQuaternion);\r\n    }\r\n\r\n    return cumulativeQuaternion;\r\n}\r\n\r\n/** @internal */\r\nfunction ProcessLateAnimationBindings(scene: Scene): void {\r\n    if (!scene._registeredForLateAnimationBindings.length) {\r\n        return;\r\n    }\r\n    for (let index = 0; index < scene._registeredForLateAnimationBindings.length; index++) {\r\n        const target = scene._registeredForLateAnimationBindings.data[index];\r\n\r\n        for (const path in target._lateAnimationHolders) {\r\n            const holder = target._lateAnimationHolders[path];\r\n            const originalAnimation: RuntimeAnimation = holder.animations[0];\r\n            const originalValue = holder.originalValue;\r\n            if (originalValue === undefined || originalValue === null) {\r\n                continue;\r\n            }\r\n            const matrixDecomposeMode = Animation.AllowMatrixDecomposeForInterpolation && originalValue.m; // ie. data is matrix\r\n\r\n            let finalValue: any = target[path];\r\n            if (matrixDecomposeMode) {\r\n                finalValue = ProcessLateAnimationBindingsForMatrices(holder);\r\n            } else {\r\n                const quaternionMode = originalValue.w !== undefined;\r\n                if (quaternionMode) {\r\n                    finalValue = ProcessLateAnimationBindingsForQuaternions(holder, finalValue || Quaternion.Identity());\r\n                } else {\r\n                    let startIndex = 0;\r\n                    let normalizer = 1.0;\r\n\r\n                    const originalAnimationIsLoopRelativeFromCurrent =\r\n                        originalAnimation && originalAnimation._animationState.loopMode === Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT;\r\n\r\n                    if (holder.totalWeight < 1.0) {\r\n                        // We need to mix the original value in\r\n                        if (originalAnimationIsLoopRelativeFromCurrent) {\r\n                            finalValue = originalValue.clone ? originalValue.clone() : originalValue;\r\n                        } else if (originalAnimation && originalValue.scale) {\r\n                            finalValue = originalValue.scale(1.0 - holder.totalWeight);\r\n                        } else if (originalAnimation) {\r\n                            finalValue = originalValue * (1.0 - holder.totalWeight);\r\n                        } else if (originalValue.clone) {\r\n                            finalValue = originalValue.clone();\r\n                        } else {\r\n                            finalValue = originalValue;\r\n                        }\r\n                    } else if (originalAnimation) {\r\n                        // We need to normalize the weights\r\n                        normalizer = holder.totalWeight;\r\n                        const scale = originalAnimation.weight / normalizer;\r\n                        if (scale !== 1) {\r\n                            if (originalAnimation.currentValue.scale) {\r\n                                finalValue = originalAnimation.currentValue.scale(scale);\r\n                            } else {\r\n                                finalValue = originalAnimation.currentValue * scale;\r\n                            }\r\n                        } else {\r\n                            finalValue = originalAnimation.currentValue;\r\n                        }\r\n\r\n                        if (originalAnimationIsLoopRelativeFromCurrent) {\r\n                            if (finalValue.addToRef) {\r\n                                finalValue.addToRef(originalValue, finalValue);\r\n                            } else {\r\n                                finalValue += originalValue;\r\n                            }\r\n                        }\r\n\r\n                        startIndex = 1;\r\n                    }\r\n\r\n                    // Add up the override animations\r\n                    for (let animIndex = startIndex; animIndex < holder.animations.length; animIndex++) {\r\n                        const runtimeAnimation = holder.animations[animIndex];\r\n                        const scale = runtimeAnimation.weight / normalizer;\r\n\r\n                        if (!scale) {\r\n                            continue;\r\n                        } else if (runtimeAnimation.currentValue.scaleAndAddToRef) {\r\n                            runtimeAnimation.currentValue.scaleAndAddToRef(scale, finalValue);\r\n                        } else {\r\n                            finalValue += runtimeAnimation.currentValue * scale;\r\n                        }\r\n                    }\r\n\r\n                    // Add up the additive animations\r\n                    for (let animIndex = 0; animIndex < holder.additiveAnimations.length; animIndex++) {\r\n                        const runtimeAnimation = holder.additiveAnimations[animIndex];\r\n                        const scale: number = runtimeAnimation.weight;\r\n\r\n                        if (!scale) {\r\n                            continue;\r\n                        } else if (runtimeAnimation.currentValue.scaleAndAddToRef) {\r\n                            runtimeAnimation.currentValue.scaleAndAddToRef(scale, finalValue);\r\n                        } else {\r\n                            finalValue += runtimeAnimation.currentValue * scale;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            target[path] = finalValue;\r\n        }\r\n\r\n        target._lateAnimationHolders = {};\r\n    }\r\n    scene._registeredForLateAnimationBindings.reset();\r\n}\r\n\r\n/** @internal */\r\nexport function RegisterTargetForLateAnimationBinding(scene: Scene, runtimeAnimation: RuntimeAnimation, originalValue: any): void {\r\n    const target = runtimeAnimation.target;\r\n    scene._registeredForLateAnimationBindings.pushNoDuplicate(target);\r\n\r\n    if (!target._lateAnimationHolders) {\r\n        target._lateAnimationHolders = {};\r\n    }\r\n\r\n    if (!target._lateAnimationHolders[runtimeAnimation.targetPath]) {\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath] = {\r\n            totalWeight: 0,\r\n            totalAdditiveWeight: 0,\r\n            animations: [],\r\n            additiveAnimations: [],\r\n            originalValue: originalValue,\r\n        };\r\n    }\r\n\r\n    if (runtimeAnimation.isAdditive) {\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].additiveAnimations.push(runtimeAnimation);\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].totalAdditiveWeight += runtimeAnimation.weight;\r\n    } else {\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].animations.push(runtimeAnimation);\r\n        target._lateAnimationHolders[runtimeAnimation.targetPath].totalWeight += runtimeAnimation.weight;\r\n    }\r\n}\r\n\r\n/**\r\n * Initialize all the inter dependecies between the animations and Scene and Bone\r\n * @param sceneClass defines the scene prototype to use\r\n * @param boneClass defines the bone prototype to use\r\n */\r\nexport function AddAnimationExtensions(sceneClass: typeof Scene, boneClass: typeof Bone): void {\r\n    if (boneClass) {\r\n        boneClass.prototype.copyAnimationRange = function (\r\n            source: Bone,\r\n            rangeName: string,\r\n            frameOffset: number,\r\n            rescaleAsRequired = false,\r\n            skelDimensionsRatio: Nullable<Vector3> = null\r\n        ): boolean {\r\n            // all animation may be coming from a library skeleton, so may need to create animation\r\n            if (this.animations.length === 0) {\r\n                this.animations.push(new Animation(this.name, \"_matrix\", source.animations[0].framePerSecond, Animation.ANIMATIONTYPE_MATRIX, 0));\r\n                this.animations[0].setKeys([]);\r\n            }\r\n\r\n            // get animation info / verify there is such a range from the source bone\r\n            const sourceRange = source.animations[0].getRange(rangeName);\r\n            if (!sourceRange) {\r\n                return false;\r\n            }\r\n            const from = sourceRange.from;\r\n            const to = sourceRange.to;\r\n            const sourceKeys = source.animations[0].getKeys();\r\n\r\n            // rescaling prep\r\n            const sourceBoneLength = source.length;\r\n            const sourceParent = source.getParent();\r\n            const parent = this.getParent();\r\n            const parentScalingReqd = rescaleAsRequired && sourceParent && sourceBoneLength && this.length && sourceBoneLength !== this.length;\r\n            const parentRatio = parentScalingReqd && parent && sourceParent ? parent.length / sourceParent.length : 1;\r\n\r\n            const dimensionsScalingReqd =\r\n                rescaleAsRequired && !parent && skelDimensionsRatio && (skelDimensionsRatio.x !== 1 || skelDimensionsRatio.y !== 1 || skelDimensionsRatio.z !== 1);\r\n\r\n            const destKeys = this.animations[0].getKeys();\r\n\r\n            // loop vars declaration\r\n            let orig: { frame: number; value: Matrix };\r\n            let origTranslation: Vector3;\r\n            let mat: Matrix;\r\n\r\n            for (let key = 0, nKeys = sourceKeys.length; key < nKeys; key++) {\r\n                orig = sourceKeys[key];\r\n                if (orig.frame >= from && orig.frame <= to) {\r\n                    if (rescaleAsRequired) {\r\n                        mat = orig.value.clone();\r\n\r\n                        // scale based on parent ratio, when bone has parent\r\n                        if (parentScalingReqd) {\r\n                            origTranslation = mat.getTranslation();\r\n                            mat.setTranslation(origTranslation.scaleInPlace(parentRatio));\r\n\r\n                            // scale based on skeleton dimension ratio when root bone, and value is passed\r\n                        } else if (dimensionsScalingReqd && skelDimensionsRatio) {\r\n                            origTranslation = mat.getTranslation();\r\n                            mat.setTranslation(origTranslation.multiplyInPlace(skelDimensionsRatio));\r\n\r\n                            // use original when root bone, and no data for skelDimensionsRatio\r\n                        } else {\r\n                            mat = orig.value;\r\n                        }\r\n                    } else {\r\n                        mat = orig.value;\r\n                    }\r\n                    destKeys.push({ frame: orig.frame + frameOffset, value: mat });\r\n                }\r\n            }\r\n            this.animations[0].createRange(rangeName, from + frameOffset, to + frameOffset);\r\n            return true;\r\n        };\r\n    }\r\n\r\n    if (!sceneClass) {\r\n        return;\r\n    }\r\n\r\n    sceneClass.prototype._animate = function (customDeltaTime?: number): void {\r\n        if (!this.animationsEnabled) {\r\n            return;\r\n        }\r\n\r\n        // Getting time\r\n        const now = PrecisionDate.Now;\r\n        if (!this._animationTimeLast) {\r\n            if (this._pendingData.length > 0) {\r\n                return;\r\n            }\r\n            this._animationTimeLast = now;\r\n        }\r\n\r\n        this.deltaTime = customDeltaTime !== undefined ? customDeltaTime : this.useConstantAnimationDeltaTime ? 16.0 : (now - this._animationTimeLast) * this.animationTimeScale;\r\n        this._animationTimeLast = now;\r\n\r\n        const animatables = this._activeAnimatables;\r\n        if (animatables.length === 0) {\r\n            return;\r\n        }\r\n\r\n        this._animationTime += this.deltaTime;\r\n        const animationTime = this._animationTime;\r\n\r\n        for (let index = 0; index < animatables.length; index++) {\r\n            const animatable = animatables[index];\r\n\r\n            if (!animatable._animate(animationTime) && animatable.disposeOnEnd) {\r\n                index--; // Array was updated\r\n            }\r\n        }\r\n\r\n        // Late animation bindings\r\n        ProcessLateAnimationBindings(this);\r\n    };\r\n\r\n    sceneClass.prototype.sortActiveAnimatables = function (): void {\r\n        this._activeAnimatables.sort((a, b) => {\r\n            return a.playOrder - b.playOrder;\r\n        });\r\n    };\r\n\r\n    sceneClass.prototype.beginWeightedAnimation = function (\r\n        target: any,\r\n        from: number,\r\n        to: number,\r\n        weight = 1.0,\r\n        loop?: boolean,\r\n        speedRatio: number = 1.0,\r\n        onAnimationEnd?: () => void,\r\n        animatable?: Animatable,\r\n        targetMask?: (target: any) => boolean,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable {\r\n        const returnedAnimatable = this.beginAnimation(target, from, to, loop, speedRatio, onAnimationEnd, animatable, false, targetMask, onAnimationLoop, isAdditive);\r\n        returnedAnimatable.weight = weight;\r\n\r\n        return returnedAnimatable;\r\n    };\r\n\r\n    sceneClass.prototype.beginAnimation = function (\r\n        target: any,\r\n        from: number,\r\n        to: number,\r\n        loop?: boolean,\r\n        speedRatio: number = 1.0,\r\n        onAnimationEnd?: () => void,\r\n        animatable?: Animatable,\r\n        stopCurrent = true,\r\n        targetMask?: (target: any) => boolean,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable {\r\n        // get speed speedRatio, to and from, based on the sign and value(s)\r\n        if (speedRatio < 0) {\r\n            const tmp = from;\r\n            from = to;\r\n            to = tmp;\r\n            speedRatio = -speedRatio;\r\n        }\r\n        // if from > to switch speed ratio\r\n        if (from > to) {\r\n            speedRatio = -speedRatio;\r\n        }\r\n        if (stopCurrent) {\r\n            this.stopAnimation(target, undefined, targetMask);\r\n        }\r\n\r\n        if (!animatable) {\r\n            animatable = new Animatable(this, target, from, to, loop, speedRatio, onAnimationEnd, undefined, onAnimationLoop, isAdditive);\r\n        }\r\n\r\n        const shouldRunTargetAnimations = targetMask ? targetMask(target) : true;\r\n        // Local animations\r\n        if (target.animations && shouldRunTargetAnimations) {\r\n            animatable.appendAnimations(target, target.animations);\r\n        }\r\n\r\n        // Children animations\r\n        if (target.getAnimatables) {\r\n            const animatables = target.getAnimatables();\r\n            for (let index = 0; index < animatables.length; index++) {\r\n                this.beginAnimation(animatables[index], from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, onAnimationLoop);\r\n            }\r\n        }\r\n\r\n        animatable.reset();\r\n\r\n        return animatable;\r\n    };\r\n\r\n    sceneClass.prototype.beginHierarchyAnimation = function (\r\n        target: any,\r\n        directDescendantsOnly: boolean,\r\n        from: number,\r\n        to: number,\r\n        loop?: boolean,\r\n        speedRatio: number = 1.0,\r\n        onAnimationEnd?: () => void,\r\n        animatable?: Animatable,\r\n        stopCurrent = true,\r\n        targetMask?: (target: any) => boolean,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable[] {\r\n        const children = target.getDescendants(directDescendantsOnly);\r\n\r\n        const result = [];\r\n        result.push(this.beginAnimation(target, from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, undefined, isAdditive));\r\n        for (const child of children) {\r\n            result.push(this.beginAnimation(child, from, to, loop, speedRatio, onAnimationEnd, animatable, stopCurrent, targetMask, undefined, isAdditive));\r\n        }\r\n\r\n        return result;\r\n    };\r\n\r\n    sceneClass.prototype.beginDirectAnimation = function (\r\n        target: any,\r\n        animations: Animation[],\r\n        from: number,\r\n        to: number,\r\n        loop?: boolean,\r\n        speedRatio: number = 1.0,\r\n        onAnimationEnd?: () => void,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable {\r\n        // get speed speedRatio, to and from, based on the sign and value(s)\r\n        if (speedRatio < 0) {\r\n            const tmp = from;\r\n            from = to;\r\n            to = tmp;\r\n            speedRatio = -speedRatio;\r\n        }\r\n        // if from > to switch speed ratio\r\n        if (from > to) {\r\n            speedRatio = -speedRatio;\r\n        }\r\n        const animatable = new Animatable(this, target, from, to, loop, speedRatio, onAnimationEnd, animations, onAnimationLoop, isAdditive);\r\n\r\n        return animatable;\r\n    };\r\n\r\n    sceneClass.prototype.beginDirectHierarchyAnimation = function (\r\n        target: Node,\r\n        directDescendantsOnly: boolean,\r\n        animations: Animation[],\r\n        from: number,\r\n        to: number,\r\n        loop?: boolean,\r\n        speedRatio?: number,\r\n        onAnimationEnd?: () => void,\r\n        onAnimationLoop?: () => void,\r\n        isAdditive = false\r\n    ): Animatable[] {\r\n        const children = target.getDescendants(directDescendantsOnly);\r\n\r\n        const result = [];\r\n        result.push(this.beginDirectAnimation(target, animations, from, to, loop, speedRatio, onAnimationEnd, onAnimationLoop, isAdditive));\r\n        for (const child of children) {\r\n            result.push(this.beginDirectAnimation(child, animations, from, to, loop, speedRatio, onAnimationEnd, onAnimationLoop, isAdditive));\r\n        }\r\n\r\n        return result;\r\n    };\r\n\r\n    sceneClass.prototype.getAnimatableByTarget = function (target: any): Nullable<Animatable> {\r\n        for (let index = 0; index < this._activeAnimatables.length; index++) {\r\n            if (this._activeAnimatables[index].target === target) {\r\n                return this._activeAnimatables[index];\r\n            }\r\n        }\r\n\r\n        return null;\r\n    };\r\n\r\n    sceneClass.prototype.getAllAnimatablesByTarget = function (target: any): Array<Animatable> {\r\n        const result = [];\r\n        for (let index = 0; index < this._activeAnimatables.length; index++) {\r\n            if (this._activeAnimatables[index].target === target) {\r\n                result.push(this._activeAnimatables[index]);\r\n            }\r\n        }\r\n\r\n        return result;\r\n    };\r\n\r\n    sceneClass.prototype.stopAnimation = function (target: any, animationName?: string, targetMask?: (target: any) => boolean): void {\r\n        const animatables = this.getAllAnimatablesByTarget(target);\r\n\r\n        for (const animatable of animatables) {\r\n            animatable.stop(animationName, targetMask);\r\n        }\r\n    };\r\n\r\n    sceneClass.prototype.stopAllAnimations = function (): void {\r\n        if (this._activeAnimatables) {\r\n            for (let i = 0; i < this._activeAnimatables.length; i++) {\r\n                this._activeAnimatables[i].stop(undefined, undefined, true);\r\n            }\r\n            this._activeAnimatables.length = 0;\r\n        }\r\n\r\n        for (const group of this.animationGroups) {\r\n            group.stop();\r\n        }\r\n    };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA,OAAO,EAAE,UAAU,EAAE,8BAA6B;AAGlD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,aAAa,EAAE,iCAAgC;AACxD,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,gCAA+B;;;;;;AAO3E,MAAO,UAAU;IA2CnB;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG,CACH,IAAW,WAAW,GAAA;QAClB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IACnD,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,+BAA+B;YAC/B,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YAClB,OAAO;QACX,CAAC;QAED,sCAAsC;QACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAClE,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAEjD,SAAS,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,kFAAkF;QAClF,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC;IACrG,CAAC;IA8CD,UAAU;IACV;;;;;OAKG,CACI,QAAQ,CAAC,IAA0B,EAAA;QACtC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,IAAI,EAAE,CAAC;YACP,wDAAwD;YACxD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAChD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,MAAW,EAAE,UAAuB,EAAA;QACxD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACrD,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;YAEpC,MAAM,mBAAmB,GAAG,6KAAI,mBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACvF,mBAAmB,CAAC,OAAO,GAAG,GAAG,EAAE;gBAC/B,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC3B,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACtD,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,4BAA4B,CAAC,QAAgB,EAAA;QAChD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;gBACjE,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,mCAAmC,CAAC,QAAgB,EAAA;QACvD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;gBACjE,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG,CACI,KAAK,GAAA;QACR,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;IAC7B,CAAC;IAED;;;;OAIG,CACI,cAAc,CAAC,aAAqB,EAAA;QACvC,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC;YACzD,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,aAAa,GAAG,aAAa,CAAC;QACrE,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,eAAe,GAAA;QAClB,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,KAAK,CAAC;QAC9D,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,SAAS,CAAC,KAAa,EAAmB;wBAAjB,SAAS,wDAAG,KAAK;QAC7C,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElD,IAAI,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;YACvB,MAAM,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC;;YAC1D,IAAI,CAAC,oBAAoB,qCAAO,CAAC,oBAAoB,sDAAzB,6BAA6B,iBAAiB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC3F,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAE,CAAD,AAAE,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,GAAG,CAAC,EAAG,IAAI,CAAC,EAAG,IAAI,CAAC,UAAU,CAAC;YACjH,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,CAAC;QACnC,CAAC;QAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG,CACI,KAAK,GAAA;QACR,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO;QACX,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAEO,oBAAoB,GAAA;QACxB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;OAMG,CACI,IAAI,CAAC,aAAsB,EAAE,UAAqC,EAAqD;YAAnD,eAAe,oEAAG,KAAK,uBAAE,kBAAkB,+CAAG,KAAK;QAC1H,IAAI,aAAa,IAAI,UAAU,EAAE,CAAC;YAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEzD,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC;gBACX,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAElD,IAAK,IAAI,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;oBACjE,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;oBAClD,IAAI,aAAa,IAAI,gBAAgB,CAAC,SAAS,CAAC,IAAI,IAAI,aAAa,EAAE,CAAC;wBACpE,SAAS;oBACb,CAAC;oBACD,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;wBACrD,SAAS;oBACb,CAAC;oBAED,gBAAgB,CAAC,OAAO,EAAE,CAAC;oBAC3B,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACvC,CAAC;gBAED,IAAI,iBAAiB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBAChC,IAAI,CAAC,eAAe,EAAE,CAAC;wBACnB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;oBAClD,CAAC;oBACD,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAChC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAE3D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,eAAe,EAAE,CAAC;oBACnB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACpD,CAAC;gBACD,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;gBAElD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBAC5D,iBAAiB,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;gBACvC,CAAC;gBAED,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEnC,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAChC,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,KAAK,CAAC,SAAS,GAAA;QAClB,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAC7B,GAAG,EAAE;gBACD,OAAO,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,EACD,SAAS,EACT,SAAS,EACT,IAAI,EACJ,IAAI,CACP,CAAC;QACN,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,QAAQ,CAAC,KAAa,EAAA;QACzB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;gBAC7B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC9B,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC,MAAM,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,iBAAiB,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC;YACpD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAC/F,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI,CAAC,UAAU,CAAC,wBAAwB,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,eAAe,KAAK,CAAC,EAAE,CAAC;YAC3F,0EAA0E;YAC1E,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC;QAEpC,YAAY;QACZ,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAClD,IAAI,KAAa,CAAC;QAElB,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACxD,MAAM,SAAS,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACtJ,OAAO,GAAG,OAAO,IAAI,SAAS,CAAC;QACnC,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;QAEhC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,iCAAiC;gBACjC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAEhD,iCAAiC;gBACjC,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;oBACxD,iBAAiB,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC;gBACvC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;gBACvC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAlXD;;;;;;;;;;;;;OAaG,CACH,YACI,KAAY,EACZ,8BAAA,EAAgC,CACzB,MAAW,EAClB,yDAAA,EAA2D,CACpD,YAAoB,CAAC,EAC5B,2DAAA,EAA6D,CACtD,UAAkB,GAAG,EAC5B,mEAAA,EAAqE,CAC9D,gBAAyB,KAAK,EACrC,aAAqB,GAAG,EACxB,wEAAA,EAA0E,CACnE,cAAqC,EAC5C,UAAwB,EACxB,oDAAA,EAAsD,CAC/C,eAAsC,EAC7C,yEAAA,EAA2E,CACpE,aAAsB,KAAK,EAClC,sHAAA,EAAwH,CACjH,YAAY,CAAC,CAAA;QAhBb,IAAA,CAAA,MAAM,GAAN,MAAM,CAAK;QAEX,IAAA,CAAA,SAAS,GAAT,SAAS,CAAY;QAErB,IAAA,CAAA,OAAO,GAAP,OAAO,CAAc;QAErB,IAAA,CAAA,aAAa,GAAb,aAAa,CAAiB;QAG9B,IAAA,CAAA,cAAc,GAAd,cAAc,CAAuB;QAGrC,IAAA,CAAA,eAAe,GAAf,eAAe,CAAuB;QAEtC,IAAA,CAAA,UAAU,GAAV,UAAU,CAAiB;QAE3B,IAAA,CAAA,SAAS,GAAT,SAAS,CAAI;QArIhB,IAAA,CAAA,iBAAiB,GAAqB,IAAI,CAAC;QAC3C,IAAA,CAAA,YAAY,GAAqB,IAAI,CAAC;QACtC,IAAA,CAAA,gBAAgB,GAAqB,IAAI,CAAC;QAClD,YAAA,EAAc,CACP,IAAA,CAAA,kBAAkB,GAAG,IAAI,KAAK,EAAoB,CAAC;QAClD,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QAEhB,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAChB,IAAA,CAAA,OAAO,GAAG,CAAC,GAAG,CAAC;QACf,IAAA,CAAA,eAAe,GAAG,CAAC,GAAG,CAAC;QACvB,IAAA,CAAA,SAAS,GAAyB,IAAI,CAAC;QACvC,IAAA,CAAA,oBAAoB,GAAqB,IAAI,CAAC;QAC9C,IAAA,CAAA,UAAU,GAAqB,IAAI,CAAC;QAE5C;;;WAGG,CACI,IAAA,CAAA,YAAY,GAAG,IAAI,CAAC;QAE3B;;WAEG,CACI,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;QAEhC;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,iKAAI,aAAU,EAAc,CAAC;QAE/D;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,iKAAI,aAAU,EAAc,CAAC;QAsG5D,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;;AArJD;;;;GAIG,CACW,WAAA,wBAAwB,GAAG,KAAK,AAAR,CAAS;AA2dnD,cAAA,EAAgB,CAChB,SAAS,uCAAuC,CAAC,MAMhD;IACG,IAAI,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;QAC/D,OAAO,MAAM,CAAC,aAAa,CAAC;IAChC,CAAC;IAED,IAAI,UAAU,GAAG,GAAG,CAAC;IACrB,MAAM,aAAa,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5C,MAAM,YAAY,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAM,eAAe,GAAG,+KAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACjD,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,MAAM,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;IAE3C,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;QAC3B,uCAAuC;QACvC,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;QACjC,aAAa,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAC1E,CAAC,MAAM,CAAC;QACJ,UAAU,GAAG,CAAC,CAAC;QACf,mCAAmC;QACnC,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;QAChC,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,UAAU,CAAC;QAC9C,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YACb,IAAI,MAAM,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,YAAY,GAAG,IAAI,CAAC;YACxB,CAAC,MAAM,CAAC;gBACJ,OAAO,iBAAiB,CAAC,YAAY,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAC3F,CAAC;IAED,iCAAiC;IACjC,IAAI,CAAC,YAAY,EAAE,CAAC;QAChB,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACjC,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAClC,eAAe,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAEpC,IAAK,IAAI,SAAS,GAAG,UAAU,EAAE,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;YACjF,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACtD,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,SAAS;YACb,CAAC;YAED,KAAK,GAAG,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC;YAC7C,MAAM,eAAe,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC9C,MAAM,cAAc,GAAG,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,iBAAiB,qKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAEnD,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;YAE5F,cAAc,CAAC,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YACrD,iBAAiB,CAAC,gBAAgB,mKAAC,aAAU,CAAC,GAAG,CAAC,eAAe,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;YAC7H,eAAe,CAAC,gBAAgB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;QAC3D,CAAC;QAED,eAAe,CAAC,SAAS,EAAE,CAAC;IAChC,CAAC;IAED,iCAAiC;IACjC,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;QAChF,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC9D,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,SAAS;QACb,CAAC;QAED,MAAM,eAAe,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC9C,MAAM,cAAc,GAAG,+KAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,iBAAiB,qKAAG,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAEnD,gBAAgB,CAAC,YAAY,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,EAAE,eAAe,CAAC,CAAC;QAC5F,cAAc,CAAC,aAAa,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;0KAC3D,UAAO,CAAC,SAAS,CAAC,YAAY,EAAE,cAAc,EAAE,gBAAgB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACvF,eAAe,CAAC,aAAa,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;0KACpE,aAAU,CAAC,UAAU,CAAC,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QACpG,eAAe,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAC7E,CAAC;IAED,MAAM,SAAS,GAAG,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,mKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;sKACjH,SAAM,CAAC,YAAY,CAAC,YAAY,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;IAC7E,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,cAAA,EAAgB,CAChB,SAAS,0CAA0C,CAC/C,MAMC,EACD,aAAyB;IAEzB,IAAI,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;QAC/D,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,MAAM,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;IAC3C,IAAI,oBAAoB,GAAG,aAAa,CAAC;IAEzC,IAAI,MAAM,CAAC,WAAW,KAAK,CAAC,IAAI,MAAM,CAAC,mBAAmB,GAAG,CAAC,EAAE,CAAC;QAC7D,oBAAoB,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;0KACxC,aAAU,CAAC,UAAU,CAAC,aAAa,EAAE,iBAAiB,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,oBAAoB,CAAC,CAAC;QAE9H,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,oBAAoB,CAAC;QAChC,CAAC;IACL,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtC,iCAAiC;QACjC,IAAI,UAAU,GAAG,GAAG,CAAC;QACrB,IAAI,WAA8B,CAAC;QACnC,IAAI,OAAsB,CAAC;QAE3B,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;YAEvC,WAAW,GAAG,EAAE,CAAC;YACjB,OAAO,GAAG,EAAE,CAAC;YAEb,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,CAAC,MAAM,CAAC;YACJ,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,0BAA0B;kLAC1B,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;gBAE7J,IAAI,MAAM,CAAC,mBAAmB,KAAK,CAAC,EAAE,CAAC;oBACnC,OAAO,aAAa,CAAC;gBACzB,CAAC;YACL,CAAC;YAED,WAAW,GAAG,EAAE,CAAC;YACjB,OAAO,GAAG,EAAE,CAAC;YACb,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;QACpC,CAAC;QAED,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;YACxE,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACtD,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAChD,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC;QACvD,CAAC;QAED,mGAAmG;QAEnG,IAAI,gBAAgB,GAAG,CAAC,CAAC;QACzB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAI,CAAC;YAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;kLACT,aAAU,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;gBAC7I,oBAAoB,GAAG,aAAa,CAAC;gBACrC,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACvD,KAAK,IAAI,CAAC,CAAC;gBACX,SAAS;YACb,CAAC;YACD,gBAAgB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;6KACnC,cAAU,CAAC,UAAU,CAAC,oBAAoB,EAAE,WAAW,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;YACzH,KAAK,EAAE,CAAC;QACZ,CAAC;IACL,CAAC;IAED,iCAAiC;IACjC,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;QAChF,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAC9D,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,SAAS;QACb,CAAC;QAED,oBAAoB,CAAC,aAAa,CAAC,gBAAgB,CAAC,YAAY,oKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,+KAAU,CAAC,UAAU,CAAC,oBAAoB,oKAAE,aAAU,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;IACzH,CAAC;IAED,OAAO,oBAAoB,CAAC;AAChC,CAAC;AAED,cAAA,EAAgB,CAChB,SAAS,4BAA4B,CAAC,KAAY;IAC9C,IAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,MAAM,EAAE,CAAC;QACpD,OAAO;IACX,CAAC;IACD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,mCAAmC,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;QACpF,MAAM,MAAM,GAAG,KAAK,CAAC,mCAAmC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAErE,IAAK,MAAM,IAAI,IAAI,MAAM,CAAC,qBAAqB,CAAE,CAAC;YAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,iBAAiB,GAAqB,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACjE,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC;YAC3C,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;gBACxD,SAAS;YACb,CAAC;YACD,MAAM,mBAAmB,qKAAG,YAAS,CAAC,oCAAoC,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,qBAAqB;YAEpH,IAAI,UAAU,GAAQ,MAAM,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,mBAAmB,EAAE,CAAC;gBACtB,UAAU,GAAG,uCAAuC,CAAC,MAAM,CAAC,CAAC;YACjE,CAAC,MAAM,CAAC;gBACJ,MAAM,cAAc,GAAG,aAAa,CAAC,CAAC,KAAK,SAAS,CAAC;gBACrD,IAAI,cAAc,EAAE,CAAC;oBACjB,UAAU,GAAG,0CAA0C,CAAC,MAAM,EAAE,UAAU,sKAAI,aAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACzG,CAAC,MAAM,CAAC;oBACJ,IAAI,UAAU,GAAG,CAAC,CAAC;oBACnB,IAAI,UAAU,GAAG,GAAG,CAAC;oBAErB,MAAM,0CAA0C,GAC5C,iBAAiB,IAAI,iBAAiB,CAAC,eAAe,CAAC,QAAQ,uKAAK,YAAS,CAAC,uCAAuC,CAAC;oBAE1H,IAAI,MAAM,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC;wBAC3B,uCAAuC;wBACvC,IAAI,0CAA0C,EAAE,CAAC;4BAC7C,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC;wBAC7E,CAAC,MAAM,IAAI,iBAAiB,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;4BAClD,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;wBAC/D,CAAC,MAAM,IAAI,iBAAiB,EAAE,CAAC;4BAC3B,UAAU,GAAG,aAAa,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;wBAC5D,CAAC,MAAM,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;4BAC7B,UAAU,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;wBACvC,CAAC,MAAM,CAAC;4BACJ,UAAU,GAAG,aAAa,CAAC;wBAC/B,CAAC;oBACL,CAAC,MAAM,IAAI,iBAAiB,EAAE,CAAC;wBAC3B,mCAAmC;wBACnC,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC;wBAChC,MAAM,KAAK,GAAG,iBAAiB,CAAC,MAAM,GAAG,UAAU,CAAC;wBACpD,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;4BACd,IAAI,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;gCACvC,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;4BAC7D,CAAC,MAAM,CAAC;gCACJ,UAAU,GAAG,iBAAiB,CAAC,YAAY,GAAG,KAAK,CAAC;4BACxD,CAAC;wBACL,CAAC,MAAM,CAAC;4BACJ,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC;wBAChD,CAAC;wBAED,IAAI,0CAA0C,EAAE,CAAC;4BAC7C,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;gCACtB,UAAU,CAAC,QAAQ,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;4BACnD,CAAC,MAAM,CAAC;gCACJ,UAAU,IAAI,aAAa,CAAC;4BAChC,CAAC;wBACL,CAAC;wBAED,UAAU,GAAG,CAAC,CAAC;oBACnB,CAAC;oBAED,iCAAiC;oBACjC,IAAK,IAAI,SAAS,GAAG,UAAU,EAAE,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;wBACjF,MAAM,gBAAgB,GAAG,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;wBACtD,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,GAAG,UAAU,CAAC;wBAEnD,IAAI,CAAC,KAAK,EAAE,CAAC;4BACT,SAAS;wBACb,CAAC,MAAM,IAAI,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;4BACxD,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;wBACtE,CAAC,MAAM,CAAC;4BACJ,UAAU,IAAI,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;wBACxD,CAAC;oBACL,CAAC;oBAED,iCAAiC;oBACjC,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;wBAChF,MAAM,gBAAgB,GAAG,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;wBAC9D,MAAM,KAAK,GAAW,gBAAgB,CAAC,MAAM,CAAC;wBAE9C,IAAI,CAAC,KAAK,EAAE,CAAC;4BACT,SAAS;wBACb,CAAC,MAAM,IAAI,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;4BACxD,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;wBACtE,CAAC,MAAM,CAAC;4BACJ,UAAU,IAAI,gBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;wBACxD,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;QAC9B,CAAC;QAED,MAAM,CAAC,qBAAqB,GAAG,CAAA,CAAE,CAAC;IACtC,CAAC;IACD,KAAK,CAAC,mCAAmC,CAAC,KAAK,EAAE,CAAC;AACtD,CAAC;AAGK,SAAU,qCAAqC,CAAC,KAAY,EAAE,gBAAkC,EAAE,aAAkB;IACtH,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;IACvC,KAAK,CAAC,mCAAmC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAElE,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;QAChC,MAAM,CAAC,qBAAqB,GAAG,CAAA,CAAE,CAAC;IACtC,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;QAC7D,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG;YACxD,WAAW,EAAE,CAAC;YACd,mBAAmB,EAAE,CAAC;YACtB,UAAU,EAAE,EAAE;YACd,kBAAkB,EAAE,EAAE;YACtB,aAAa,EAAE,aAAa;SAC/B,CAAC;IACN,CAAC;IAED,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;QAC9B,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpG,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,mBAAmB,IAAI,gBAAgB,CAAC,MAAM,CAAC;IAC7G,CAAC,MAAM,CAAC;QACJ,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5F,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,WAAW,IAAI,gBAAgB,CAAC,MAAM,CAAC;IACrG,CAAC;AACL,CAAC;AAOK,SAAU,sBAAsB,CAAC,UAAwB,EAAE,SAAsB;IACnF,IAAI,SAAS,EAAE,CAAC;QACZ,SAAS,CAAC,SAAS,CAAC,kBAAkB,GAAG,SACrC,MAAY,EACZ,SAAiB,EACjB,WAAmB;gBACnB,iBAAiB,oEAAG,KAAK,wBACzB,iEAAyC,IAAI;YAE7C,uFAAuF;YACvF,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,sKAAI,YAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,oKAAE,YAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnC,CAAC;YAED,yEAAyE;YACzE,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC7D,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;YACD,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;YAC9B,MAAM,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC;YAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAElD,iBAAiB;YACjB,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC;YACvC,MAAM,YAAY,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,MAAM,iBAAiB,GAAG,iBAAiB,IAAI,YAAY,IAAI,gBAAgB,IAAI,IAAI,CAAC,MAAM,IAAI,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC;YACnI,MAAM,WAAW,GAAG,iBAAiB,IAAI,MAAM,IAAI,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1G,MAAM,qBAAqB,GACvB,iBAAiB,IAAI,CAAC,MAAM,IAAI,mBAAmB,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YAEvJ,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;YAE9C,wBAAwB;YACxB,IAAI,IAAsC,CAAC;YAC3C,IAAI,eAAwB,CAAC;YAC7B,IAAI,GAAW,CAAC;YAEhB,IAAK,IAAI,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,CAAE,CAAC;gBAC9D,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;gBACvB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,CAAC;oBACzC,IAAI,iBAAiB,EAAE,CAAC;wBACpB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;wBAEzB,oDAAoD;wBACpD,IAAI,iBAAiB,EAAE,CAAC;4BACpB,eAAe,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;4BACvC,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;wBAE9D,8EAA8E;wBAClF,CAAC,MAAM,IAAI,qBAAqB,IAAI,mBAAmB,EAAE,CAAC;4BACtD,eAAe,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;4BACvC,GAAG,CAAC,cAAc,CAAC,eAAe,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC,CAAC;wBAEzE,mEAAmE;wBACvE,CAAC,MAAM,CAAC;4BACJ,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;wBACrB,CAAC;oBACL,CAAC,MAAM,CAAC;wBACJ,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;oBACrB,CAAC;oBACD,QAAQ,CAAC,IAAI,CAAC;wBAAE,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,WAAW;wBAAE,KAAK,EAAE,GAAG;oBAAA,CAAE,CAAC,CAAC;gBACnE,CAAC;YACL,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,GAAG,WAAW,EAAE,EAAE,GAAG,WAAW,CAAC,CAAC;YAChF,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC;IACN,CAAC;IAED,IAAI,CAAC,UAAU,EAAE,CAAC;QACd,OAAO;IACX,CAAC;IAED,UAAU,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAU,eAAwB;QAC9D,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QAED,eAAe;QACf,MAAM,GAAG,mKAAG,gBAAa,CAAC,GAAG,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,OAAO;YACX,CAAC;YACD,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,eAAe,KAAK,SAAS,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACzK,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;QAE9B,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAC5C,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,CAAC;QACtC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAE1C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACtD,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBACjE,KAAK,EAAE,CAAC,CAAC,oBAAoB;YACjC,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,4BAA4B,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG;QACzC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAClC,OAAO,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,SAC1C,MAAW,EACX,IAAY,EACZ,EAAU;qBACV,MAAM,2DAAG,GAAG,EACZ,IAAc,8DACd,iEAAqB,GAAG,EACxB,cAA2B,iDAC3B,UAAuB,iDACvB,UAAqC,iDACrC,eAA4B,8DAC5B,UAAU,0DAAG,KAAK;QAElB,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;QAC/J,kBAAkB,CAAC,MAAM,GAAG,MAAM,CAAC;QAEnC,OAAO,kBAAkB,CAAC;IAC9B,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,cAAc,GAAG,SAClC,MAAW,EACX,IAAY,EACZ,EAAU,EACV,IAAc;yBACd,iEAAqB,GAAG,EACxB,cAA2B,iDAC3B,UAAuB,+DACvB,WAAW,sDAAG,IAAI,EAClB,UAAqC,iDACrC,eAA4B,8DAC5B,UAAU,0DAAG,KAAK;QAElB,oEAAoE;QACpE,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACjB,MAAM,GAAG,GAAG,IAAI,CAAC;YACjB,IAAI,GAAG,EAAE,CAAC;YACV,EAAE,GAAG,GAAG,CAAC;YACT,UAAU,GAAG,CAAC,UAAU,CAAC;QAC7B,CAAC;QACD,kCAAkC;QAClC,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;YACZ,UAAU,GAAG,CAAC,UAAU,CAAC;QAC7B,CAAC;QACD,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;QAClI,CAAC;QAED,MAAM,yBAAyB,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzE,mBAAmB;QACnB,IAAI,MAAM,CAAC,UAAU,IAAI,yBAAyB,EAAE,CAAC;YACjD,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QAC3D,CAAC;QAED,sBAAsB;QACtB,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,WAAW,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;YAC5C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACtD,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC;YAC9I,CAAC;QACL,CAAC;QAED,UAAU,CAAC,KAAK,EAAE,CAAC;QAEnB,OAAO,UAAU,CAAC;IACtB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,SAC3C,MAAW,EACX,qBAA8B,EAC9B,IAAY,EACZ,EAAU,EACV,IAAc;yBACd,iEAAqB,GAAG,EACxB,cAA2B,iDAC3B,UAAuB,+DACvB,WAAW,sDAAG,IAAI,EAClB,UAAqC,iDACrC,eAA4B,mDAC5B,UAAU,uEAAG,KAAK;QAElB,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAE9D,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;QACjJ,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;QACpJ,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,SACxC,MAAW,EACX,UAAuB,EACvB,IAAY,EACZ,EAAU,EACV,IAAc;yBACd,iEAAqB,GAAG,EACxB,cAA2B,iDAC3B,eAA4B,8DAC5B,UAAU,uDAAG,KAAK;QAElB,oEAAoE;QACpE,IAAI,UAAU,GAAG,CAAC,EAAE,CAAC;YACjB,MAAM,GAAG,GAAG,IAAI,CAAC;YACjB,IAAI,GAAG,EAAE,CAAC;YACV,EAAE,GAAG,GAAG,CAAC;YACT,UAAU,GAAG,CAAC,UAAU,CAAC;QAC7B,CAAC;QACD,kCAAkC;QAClC,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;YACZ,UAAU,GAAG,CAAC,UAAU,CAAC;QAC7B,CAAC;QACD,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;QAErI,OAAO,UAAU,CAAC;IACtB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,6BAA6B,GAAG,SACjD,MAAY,EACZ,qBAA8B,EAC9B,UAAuB,EACvB,IAAY,EACZ,EAAU,EACV,IAAc,EACd,UAAmB,EACnB,cAA2B,EAC3B,eAA4B;yBAC5B,UAAU,uDAAG,KAAK;QAElB,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;QAE9D,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;QACpI,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC;QACvI,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG,SAAU,MAAW;QAC9D,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAClE,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBACnD,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,SAAU,MAAW;QAClE,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAClE,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBACnD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,aAAa,GAAG,SAAU,MAAW,EAAE,aAAsB,EAAE,UAAqC;QACrH,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAE3D,KAAK,MAAM,UAAU,IAAI,WAAW,CAAE,CAAC;YACnC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC,CAAC;IAEF,UAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACtD,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAChE,CAAC;YACD,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,eAAe,CAAE,CAAC;YACvC,KAAK,CAAC,IAAI,EAAE,CAAC;QACjB,CAAC;IACL,CAAC,CAAC;AACN,CAAC", "debugId": null}}, {"offset": {"line": 2724, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animatable.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animatable.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport type { Vector3 } from \"../Maths/math.vector\";\r\nimport { Bone } from \"../Bones/bone\";\r\nimport type { Node } from \"../node\";\r\nimport { AddAnimationExtensions } from \"./animatable.core\";\r\nimport type { Animatable } from \"./animatable.core\";\r\nimport type { Animation } from \"./animation\";\r\nimport { Scene } from \"core/scene\";\r\n\r\nexport * from \"./animatable.core\";\r\n\r\ndeclare module \"../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /**\r\n         * Sort active animatables based on their playOrder property\r\n         */\r\n        sortActiveAnimatables(): void;\r\n\r\n        /**\r\n         * Will start the animation sequence of a given target\r\n         * @param target defines the target\r\n         * @param from defines from which frame should animation start\r\n         * @param to defines until which frame should animation run.\r\n         * @param weight defines the weight to apply to the animation (1.0 by default)\r\n         * @param loop defines if the animation loops\r\n         * @param speedRatio defines the speed in which to run the animation (1.0 by default)\r\n         * @param onAnimationEnd defines the function to be executed when the animation ends\r\n         * @param animatable defines an animatable object. If not provided a new one will be created from the given params\r\n         * @param targetMask defines if the target should be animated if animations are present (this is called recursively on descendant animatables regardless of return value)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the animatable object created for this animation\r\n         */\r\n        beginWeightedAnimation(\r\n            target: any,\r\n            from: number,\r\n            to: number,\r\n            weight: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            animatable?: Animatable,\r\n            targetMask?: (target: any) => boolean,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable;\r\n\r\n        /**\r\n         * Will start the animation sequence of a given target\r\n         *\r\n         * Note that it is possible that the value(s) of speedRatio from and to will be changed if the animation is inverted\r\n         * @param target defines the target\r\n         * @param from defines from which frame should animation start\r\n         * @param to defines until which frame should animation run.\r\n         * @param loop defines if the animation loops\r\n         * @param speedRatio defines the speed in which to run the animation (1.0 by default)\r\n         * @param onAnimationEnd defines the function to be executed when the animation ends\r\n         * @param animatable defines an animatable object. If not provided a new one will be created from the given params\r\n         * @param stopCurrent defines if the current animations must be stopped first (true by default)\r\n         * @param targetMask defines if the target should be animate if animations are present (this is called recursively on descendant animatables regardless of return value)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the animatable object created for this animation\r\n         */\r\n        beginAnimation(\r\n            target: any,\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            animatable?: Animatable,\r\n            stopCurrent?: boolean,\r\n            targetMask?: (target: any) => boolean,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable;\r\n\r\n        /**\r\n         * Will start the animation sequence of a given target and its hierarchy\r\n         * @param target defines the target\r\n         * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used.\r\n         * @param from defines from which frame should animation start\r\n         * @param to defines until which frame should animation run.\r\n         * @param loop defines if the animation loops\r\n         * @param speedRatio defines the speed in which to run the animation (1.0 by default)\r\n         * @param onAnimationEnd defines the function to be executed when the animation ends\r\n         * @param animatable defines an animatable object. If not provided a new one will be created from the given params\r\n         * @param stopCurrent defines if the current animations must be stopped first (true by default)\r\n         * @param targetMask defines if the target should be animated if animations are present (this is called recursively on descendant animatables regardless of return value)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the list of created animatables\r\n         */\r\n        beginHierarchyAnimation(\r\n            target: any,\r\n            directDescendantsOnly: boolean,\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            animatable?: Animatable,\r\n            stopCurrent?: boolean,\r\n            targetMask?: (target: any) => boolean,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable[];\r\n\r\n        /**\r\n         * Begin a new animation on a given node\r\n         *\r\n         * Note that it is possible that the value(s) of speedRatio from and to will be changed if the animation is inverted\r\n         * @param target defines the target where the animation will take place\r\n         * @param animations defines the list of animations to start\r\n         * @param from defines the initial value\r\n         * @param to defines the final value\r\n         * @param loop defines if you want animation to loop (off by default)\r\n         * @param speedRatio defines the speed ratio to apply to all animations\r\n         * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the list of created animatables\r\n         */\r\n        beginDirectAnimation(\r\n            target: any,\r\n            animations: Animation[],\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable;\r\n\r\n        /**\r\n         * Begin a new animation on a given node and its hierarchy\r\n         * @param target defines the root node where the animation will take place\r\n         * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used.\r\n         * @param animations defines the list of animations to start\r\n         * @param from defines the initial value\r\n         * @param to defines the final value\r\n         * @param loop defines if you want animation to loop (off by default)\r\n         * @param speedRatio defines the speed ratio to apply to all animations\r\n         * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)\r\n         * @param onAnimationLoop defines the callback to call when an animation loops\r\n         * @param isAdditive defines whether the animation should be evaluated additively (false by default)\r\n         * @returns the list of animatables created for all nodes\r\n         */\r\n        beginDirectHierarchyAnimation(\r\n            target: Node,\r\n            directDescendantsOnly: boolean,\r\n            animations: Animation[],\r\n            from: number,\r\n            to: number,\r\n            loop?: boolean,\r\n            speedRatio?: number,\r\n            onAnimationEnd?: () => void,\r\n            onAnimationLoop?: () => void,\r\n            isAdditive?: boolean\r\n        ): Animatable[];\r\n\r\n        /**\r\n         * Gets the animatable associated with a specific target\r\n         * @param target defines the target of the animatable\r\n         * @returns the required animatable if found\r\n         */\r\n        getAnimatableByTarget(target: any): Nullable<Animatable>;\r\n\r\n        /**\r\n         * Gets all animatables associated with a given target\r\n         * @param target defines the target to look animatables for\r\n         * @returns an array of Animatables\r\n         */\r\n        getAllAnimatablesByTarget(target: any): Array<Animatable>;\r\n\r\n        /**\r\n         * Stops and removes all animations that have been applied to the scene\r\n         */\r\n        stopAllAnimations(): void;\r\n    }\r\n}\r\n\r\ndeclare module \"../Bones/bone\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Bone {\r\n        /**\r\n         * Copy an animation range from another bone\r\n         * @param source defines the source bone\r\n         * @param rangeName defines the range name to copy\r\n         * @param frameOffset defines the frame offset\r\n         * @param rescaleAsRequired defines if rescaling must be applied if required\r\n         * @param skelDimensionsRatio defines the scaling ratio\r\n         * @returns true if operation was successful\r\n         */\r\n        copyAnimationRange(source: Bone, rangeName: string, frameOffset: number, rescaleAsRequired: boolean, skelDimensionsRatio: Nullable<Vector3>): boolean;\r\n    }\r\n}\r\n\r\n// Connect everything!\r\nAddAnimationExtensions(Scene, Bone);\r\n"], "names": [], "mappings": ";AAEA,OAAO,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAErC,OAAO,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAG3D,OAAO,EAAE,KAAK,EAAE,oBAAmB;;;;;AAkMnC,sBAAsB;+KACtB,yBAAA,AAAsB,kJAAC,QAAK,0JAAE,OAAI,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2747, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animationPropertiesOverride.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animationPropertiesOverride.ts"], "sourcesContent": ["import { Animation } from \"../Animations/animation\";\r\n\r\n/**\r\n * Class used to override all child animations of a given target\r\n */\r\nexport class AnimationPropertiesOverride {\r\n    /**\r\n     * Gets or sets a value indicating if animation blending must be used\r\n     */\r\n    public enableBlending = false;\r\n\r\n    /**\r\n     * Gets or sets the blending speed to use when enableBlending is true\r\n     */\r\n    public blendingSpeed = 0.01;\r\n\r\n    /**\r\n     * Gets or sets the default loop mode to use\r\n     */\r\n    public loopMode = Animation.ANIMATIONLOOPMODE_CYCLE;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;;AAK9C,MAAO,2BAA2B;IAAxC,aAAA;QACI;;WAEG,CACI,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAE9B;;WAEG,CACI,IAAA,CAAA,aAAa,GAAG,IAAI,CAAC;QAE5B;;WAEG,CACI,IAAA,CAAA,QAAQ,qKAAG,YAAS,CAAC,uBAAuB,CAAC;IACxD,CAAC;CAAA", "debugId": null}}, {"offset": {"line": 2769, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/easing.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/easing.ts"], "sourcesContent": ["import { BezierCurve } from \"../Maths/math.path\";\r\n\r\n/**\r\n * This represents the main contract an easing function should follow.\r\n * Easing functions are used throughout the animation system.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport interface IEasingFunction {\r\n    /**\r\n     * Given an input gradient between 0 and 1, this returns the corresponding value\r\n     * of the easing function.\r\n     * The link below provides some of the most common examples of easing functions.\r\n     * @see https://easings.net/\r\n     * @param gradient Defines the value between 0 and 1 we want the easing value for\r\n     * @returns the corresponding value on the curve defined by the easing function\r\n     */\r\n    ease(gradient: number): number;\r\n}\r\n\r\n/**\r\n * Base class used for every default easing function.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Interpolation follows the mathematical formula associated with the easing function.\r\n     */\r\n    public static readonly EASINGMODE_EASEIN = 0;\r\n\r\n    /**\r\n     * Interpolation follows 100% interpolation minus the output of the formula associated with the easing function.\r\n     */\r\n    public static readonly EASINGMODE_EASEOUT = 1;\r\n\r\n    /**\r\n     * Interpolation uses EaseIn for the first half of the animation and EaseOut for the second half.\r\n     */\r\n    public static readonly EASINGMODE_EASEINOUT = 2;\r\n\r\n    private _easingMode = EasingFunction.EASINGMODE_EASEIN;\r\n\r\n    /**\r\n     * Sets the easing mode of the current function.\r\n     * @param easingMode Defines the willing mode (EASINGMODE_EASEIN, EASINGMODE_EASEOUT or EASINGMODE_EASEINOUT)\r\n     */\r\n    public setEasingMode(easingMode: number) {\r\n        const n = Math.min(Math.max(easingMode, 0), 2);\r\n        this._easingMode = n;\r\n    }\r\n    /**\r\n     * Gets the current easing mode.\r\n     * @returns the easing mode\r\n     */\r\n    public getEasingMode(): number {\r\n        return this._easingMode;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public easeInCore(gradient: number): number {\r\n        throw new Error(\"You must implement this method\");\r\n    }\r\n\r\n    /**\r\n     * Given an input gradient between 0 and 1, this returns the corresponding value\r\n     * of the easing function.\r\n     * @param gradient Defines the value between 0 and 1 we want the easing value for\r\n     * @returns the corresponding value on the curve defined by the easing function\r\n     */\r\n    public ease(gradient: number): number {\r\n        switch (this._easingMode) {\r\n            case EasingFunction.EASINGMODE_EASEIN:\r\n                return this.easeInCore(gradient);\r\n            case EasingFunction.EASINGMODE_EASEOUT:\r\n                return 1 - this.easeInCore(1 - gradient);\r\n        }\r\n\r\n        if (gradient >= 0.5) {\r\n            return (1 - this.easeInCore((1 - gradient) * 2)) * 0.5 + 0.5;\r\n        }\r\n\r\n        return this.easeInCore(gradient * 2) * 0.5;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a circle shape (see link below).\r\n * @see https://easings.net/#easeInCirc\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class CircleEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        gradient = Math.max(0, Math.min(1, gradient));\r\n        return 1.0 - Math.sqrt(1.0 - gradient * gradient);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a ease back shape (see link below).\r\n * @see https://easings.net/#easeInBack\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class BackEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates a back ease easing\r\n     * @see https://easings.net/#easeInBack\r\n     * @param amplitude Defines the amplitude of the function\r\n     */\r\n    constructor(\r\n        /** [1] Defines the amplitude of the function */\r\n        public amplitude: number = 1\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        const num = Math.max(0, this.amplitude);\r\n        return Math.pow(gradient, 3.0) - gradient * num * Math.sin(3.1415926535897931 * gradient);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a bouncing shape (see link below).\r\n * @see https://easings.net/#easeInBounce\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class BounceEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates a bounce easing\r\n     * @see https://easings.net/#easeInBounce\r\n     * @param bounces Defines the number of bounces\r\n     * @param bounciness Defines the amplitude of the bounce\r\n     */\r\n    constructor(\r\n        /** [3] Defines the number of bounces */\r\n        public bounces: number = 3,\r\n        /** [2] Defines the amplitude of the bounce */\r\n        public bounciness: number = 2\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        const y = Math.max(0.0, this.bounces);\r\n        let bounciness = this.bounciness;\r\n        if (bounciness <= 1.0) {\r\n            bounciness = 1.001;\r\n        }\r\n        const num9 = Math.pow(bounciness, y);\r\n        const num5 = 1.0 - bounciness;\r\n        const num4 = (1.0 - num9) / num5 + num9 * 0.5;\r\n        const num15 = gradient * num4;\r\n        const num65 = Math.log(-num15 * (1.0 - bounciness) + 1.0) / Math.log(bounciness);\r\n        const num3 = Math.floor(num65);\r\n        const num13 = num3 + 1.0;\r\n        const num8 = (1.0 - Math.pow(bounciness, num3)) / (num5 * num4);\r\n        const num12 = (1.0 - Math.pow(bounciness, num13)) / (num5 * num4);\r\n        const num7 = (num8 + num12) * 0.5;\r\n        const num6 = gradient - num7;\r\n        const num2 = num7 - num8;\r\n        return (-Math.pow(1.0 / bounciness, y - num3) / (num2 * num2)) * (num6 - num2) * (num6 + num2);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power of 3 shape (see link below).\r\n * @see https://easings.net/#easeInCubic\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class CubicEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        return gradient * gradient * gradient;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with an elastic shape (see link below).\r\n * @see https://easings.net/#easeInElastic\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class ElasticEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates an elastic easing function\r\n     * @see https://easings.net/#easeInElastic\r\n     * @param oscillations Defines the number of oscillations\r\n     * @param springiness Defines the amplitude of the oscillations\r\n     */\r\n    constructor(\r\n        /** [3] Defines the number of oscillations*/\r\n        public oscillations: number = 3,\r\n        /** [3] Defines the amplitude of the oscillations*/\r\n        public springiness: number = 3\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        let num2;\r\n        const num3 = Math.max(0.0, this.oscillations);\r\n        const num = Math.max(0.0, this.springiness);\r\n\r\n        if (num == 0) {\r\n            num2 = gradient;\r\n        } else {\r\n            num2 = (Math.exp(num * gradient) - 1.0) / (Math.exp(num) - 1.0);\r\n        }\r\n        return num2 * Math.sin((6.2831853071795862 * num3 + 1.5707963267948966) * gradient);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with an exponential shape (see link below).\r\n * @see https://easings.net/#easeInExpo\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class ExponentialEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates an exponential easing function\r\n     * @see https://easings.net/#easeInExpo\r\n     * @param exponent Defines the exponent of the function\r\n     */\r\n    constructor(\r\n        /** [3] Defines the exponent of the function */\r\n        public exponent: number = 2\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        if (this.exponent <= 0) {\r\n            return gradient;\r\n        }\r\n\r\n        return (Math.exp(this.exponent * gradient) - 1.0) / (Math.exp(this.exponent) - 1.0);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power shape (see link below).\r\n * @see https://easings.net/#easeInQuad\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class PowerEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates an power base easing function\r\n     * @see https://easings.net/#easeInQuad\r\n     * @param power Defines the power of the function\r\n     */\r\n    constructor(\r\n        /** [2] Defines the power of the function */\r\n        public power: number = 2\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        const y = Math.max(0.0, this.power);\r\n        return Math.pow(gradient, y);\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power of 2 shape (see link below).\r\n * @see https://easings.net/#easeInQuad\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class QuadraticEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        return gradient * gradient;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power of 4 shape (see link below).\r\n * @see https://easings.net/#easeInQuart\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class QuarticEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        return gradient * gradient * gradient * gradient;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a power of 5 shape (see link below).\r\n * @see https://easings.net/#easeInQuint\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class QuinticEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        return gradient * gradient * gradient * gradient * gradient;\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a sin shape (see link below).\r\n * @see https://easings.net/#easeInSine\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class SineEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        return 1.0 - Math.sin(1.5707963267948966 * (1.0 - gradient));\r\n    }\r\n}\r\n\r\n/**\r\n * Easing function with a bezier shape (see link below).\r\n * @see http://cubic-bezier.com/#.17,.67,.83,.67\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions\r\n */\r\nexport class BezierCurveEase extends EasingFunction implements IEasingFunction {\r\n    /**\r\n     * Instantiates a bezier function\r\n     * @see http://cubic-bezier.com/#.17,.67,.83,.67\r\n     * @param x1 Defines the x component of the start tangent in the bezier curve\r\n     * @param y1 Defines the y component of the start tangent in the bezier curve\r\n     * @param x2 Defines the x component of the end tangent in the bezier curve\r\n     * @param y2 Defines the y component of the end tangent in the bezier curve\r\n     */\r\n    constructor(\r\n        /** [0] Defines the x component of the start tangent in the bezier curve */\r\n        public x1: number = 0,\r\n        /** [0] Defines the y component of the start tangent in the bezier curve */\r\n        public y1: number = 0,\r\n        /** [1] Defines the x component of the end tangent in the bezier curve */\r\n        public x2: number = 1,\r\n        /** [1] Defines the y component of the end tangent in the bezier curve */\r\n        public y2: number = 1\r\n    ) {\r\n        super();\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public override easeInCore(gradient: number): number {\r\n        return BezierCurve.Interpolate(gradient, this.x1, this.y1, this.x2, this.y2);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;;AAuB3C,MAAO,cAAc;IAkBvB;;;OAGG,CACI,aAAa,CAAC,UAAkB,EAAA;QACnC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACzB,CAAC;IACD;;;OAGG,CACI,aAAa,GAAA;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,6DAA6D;IACtD,UAAU,CAAC,QAAgB,EAAA;QAC9B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtD,CAAC;IAED;;;;;OAKG,CACI,IAAI,CAAC,QAAgB,EAAA;QACxB,OAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;YACvB,KAAK,cAAc,CAAC,iBAAiB;gBACjC,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACrC,KAAK,cAAc,CAAC,kBAAkB;gBAClC,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC;YAClB,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;QACjE,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;IAC/C,CAAC;IA7DL,aAAA;QAgBY,IAAA,CAAA,WAAW,GAAG,cAAc,CAAC,iBAAiB,CAAC;IA8C3D,CAAC;;AA7DG;;GAEG,CACoB,eAAA,iBAAiB,GAAG,CAAC,AAAJ,CAAK;AAE7C;;GAEG,CACoB,eAAA,kBAAkB,GAAG,CAAC,AAAJ,CAAK;AAE9C;;GAEG,CACoB,eAAA,oBAAoB,GAAG,CAAC,AAAJ,CAAK;AAuD9C,MAAO,UAAW,SAAQ,cAAc;IAC1C;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC9C,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAAC;IACtD,CAAC;CACJ;AAOK,MAAO,QAAS,SAAQ,cAAc;IAaxC;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,GAAG,QAAQ,CAAC,CAAC;IAC9F,CAAC;IAlBD;;;;OAIG,CACH,YACI,8CAAA,EAAgD,CACzC,YAAoB,CAAC,CAAA;QAE5B,KAAK,EAAE,CAAC;QAFD,IAAA,CAAA,SAAS,GAAT,SAAS,CAAY;IAGhC,CAAC;CASJ;AAOK,MAAO,UAAW,SAAQ,cAAc;IAgB1C;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;YACpB,UAAU,GAAG,KAAK,CAAC;QACvB,CAAC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACrC,MAAM,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC;QAC9B,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC;QAC9C,MAAM,KAAK,GAAG,QAAQ,GAAG,IAAI,CAAC;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACjF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC;QACzB,MAAM,IAAI,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAChE,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAClE,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;QAClC,MAAM,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC;QAC7B,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;QACzB,OAAO,AAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,UAAU,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,EAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;IACnG,CAAC;IArCD;;;;;OAKG,CACH,YACI,sCAAA,EAAwC,CACjC,UAAkB,CAAC,EAC1B,4CAAA,EAA8C,CACvC,aAAqB,CAAC,CAAA;QAE7B,KAAK,EAAE,CAAC;QAJD,IAAA,CAAA,OAAO,GAAP,OAAO,CAAY;QAEnB,IAAA,CAAA,UAAU,GAAV,UAAU,CAAY;IAGjC,CAAC;CAyBJ;AAOK,MAAO,SAAU,SAAQ,cAAc;IACzC;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,OAAO,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAC1C,CAAC;CACJ;AAOK,MAAO,WAAY,SAAQ,cAAc;IAgB3C;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,IAAI,IAAI,CAAC;QACT,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAE5C,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;YACX,IAAI,GAAG,QAAQ,CAAC;QACpB,CAAC,MAAM,CAAC;YACJ,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QACpE,CAAC;QACD,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,kBAAkB,GAAG,IAAI,GAAG,kBAAkB,CAAC,GAAG,QAAQ,CAAC,CAAC;IACxF,CAAC;IA7BD;;;;;OAKG,CACH,YACI,0CAAA,EAA4C,CACrC,eAAuB,CAAC,EAC/B,iDAAA,EAAmD,CAC5C,cAAsB,CAAC,CAAA;QAE9B,KAAK,EAAE,CAAC;QAJD,IAAA,CAAA,YAAY,GAAZ,YAAY,CAAY;QAExB,IAAA,CAAA,WAAW,GAAX,WAAW,CAAY;IAGlC,CAAC;CAiBJ;AAOK,MAAO,eAAgB,SAAQ,cAAc;IAa/C;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;YACrB,OAAO,QAAQ,CAAC;QACpB,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC;IACxF,CAAC;IArBD;;;;OAIG,CACH,YACI,6CAAA,EAA+C,CACxC,WAAmB,CAAC,CAAA;QAE3B,KAAK,EAAE,CAAC;QAFD,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAY;IAG/B,CAAC;CAYJ;AAOK,MAAO,SAAU,SAAQ,cAAc;IAazC;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IACjC,CAAC;IAlBD;;;;OAIG,CACH,YACI,0CAAA,EAA4C,CACrC,QAAgB,CAAC,CAAA;QAExB,KAAK,EAAE,CAAC;QAFD,IAAA,CAAA,KAAK,GAAL,KAAK,CAAY;IAG5B,CAAC;CASJ;AAOK,MAAO,aAAc,SAAQ,cAAc;IAC7C;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,OAAO,QAAQ,GAAG,QAAQ,CAAC;IAC/B,CAAC;CACJ;AAOK,MAAO,WAAY,SAAQ,cAAc;IAC3C;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,OAAO,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACrD,CAAC;CACJ;AAOK,MAAO,WAAY,SAAQ,cAAc;IAC3C;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,OAAO,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC;IAChE,CAAC;CACJ;AAOK,MAAO,QAAS,SAAQ,cAAc;IACxC;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,OAAO,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC;CACJ;AAOK,MAAO,eAAgB,SAAQ,cAAc;IAsB/C;;OAEG,CACa,UAAU,CAAC,QAAgB,EAAA;QACvC,OAAO,8KAAW,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IACjF,CAAC;IA1BD;;;;;;;OAOG,CACH,YACI,yEAAA,EAA2E,CACpE,KAAa,CAAC,EACrB,yEAAA,EAA2E,CACpE,KAAa,CAAC,EACrB,uEAAA,EAAyE,CAClE,KAAa,CAAC,EACrB,uEAAA,EAAyE,CAClE,KAAa,CAAC,CAAA;QAErB,KAAK,EAAE,CAAC;QARD,IAAA,CAAA,EAAE,GAAF,EAAE,CAAY;QAEd,IAAA,CAAA,EAAE,GAAF,EAAE,CAAY;QAEd,IAAA,CAAA,EAAE,GAAF,EAAE,CAAY;QAEd,IAAA,CAAA,EAAE,GAAF,EAAE,CAAY;IAGzB,CAAC;CAQJ", "debugId": null}}, {"offset": {"line": 3013, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animationEvent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animationEvent.ts"], "sourcesContent": ["/**\r\n * Composed of a frame, and an action function\r\n */\r\nexport class AnimationEvent {\r\n    /**\r\n     * Specifies if the animation event is done\r\n     */\r\n    public isDone: boolean = false;\r\n\r\n    /**\r\n     * Initializes the animation event\r\n     * @param frame The frame for which the event is triggered\r\n     * @param action The event to perform when triggered\r\n     * @param onlyOnce Specifies if the event should be triggered only once\r\n     */\r\n    constructor(\r\n        /** The frame for which the event is triggered **/\r\n        public frame: number,\r\n        /** The event to perform when triggered **/\r\n        public action: (currentFrame: number) => void,\r\n        /** Specifies if the event should be triggered only once**/\r\n        public onlyOnce?: boolean\r\n    ) {}\r\n\r\n    /** @internal */\r\n    public _clone(): AnimationEvent {\r\n        return new AnimationEvent(this.frame, this.action, this.onlyOnce);\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;GAEG;;;AACG,MAAO,cAAc;IAqBvB,cAAA,EAAgB,CACT,MAAM,GAAA;QACT,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtE,CAAC;IAlBD;;;;;OAKG,CACH,YACI,gDAAA,EAAkD,CAC3C,KAAa,EACpB,yCAAA,EAA2C,CACpC,MAAsC,EAC7C,yDAAA,EAA2D,CACpD,QAAkB,CAAA;QAJlB,IAAA,CAAA,KAAK,GAAL,KAAK,CAAQ;QAEb,IAAA,CAAA,MAAM,GAAN,MAAM,CAAgC;QAEtC,IAAA,CAAA,QAAQ,GAAR,QAAQ,CAAU;QAjB7B;;WAEG,CACI,IAAA,CAAA,MAAM,GAAY,KAAK,CAAC;IAe5B,CAAC;CAMP", "debugId": null}}, {"offset": {"line": 3040, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animationGroup.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animationGroup.ts"], "sourcesContent": ["import type { Animatable } from \"./animatable.core\";\r\nimport { Animation } from \"./animation\";\r\nimport type { IMakeAnimationAdditiveOptions } from \"./animation\";\r\nimport type { IAnimationKey } from \"./animationKey\";\r\n\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { Node } from \"../node\";\r\n\r\nimport { Tags } from \"../Misc/tags\";\r\nimport type { AnimationGroupMask } from \"./animationGroupMask\";\r\nimport \"./animatable\";\r\nimport type { IAssetContainer } from \"core/IAssetContainer\";\r\nimport { UniqueIdGenerator } from \"core/Misc/uniqueIdGenerator\";\r\n\r\n/**\r\n * This class defines the direct association between an animation and a target\r\n */\r\nexport class TargetedAnimation {\r\n    /**\r\n     * Animation to perform\r\n     */\r\n    public animation: Animation;\r\n\r\n    /**\r\n     * Target to animate\r\n     */\r\n    public target: any;\r\n\r\n    /**\r\n     * Gets or sets the unique id of the targeted animation\r\n     */\r\n    public readonly uniqueId = UniqueIdGenerator.UniqueId;\r\n\r\n    /**\r\n     * Returns the string \"TargetedAnimation\"\r\n     * @returns \"TargetedAnimation\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"TargetedAnimation\";\r\n    }\r\n\r\n    /**\r\n     * Creates a new targeted animation\r\n     * @param parent The animation group to which the animation belongs\r\n     */\r\n    constructor(public readonly parent: AnimationGroup) {}\r\n\r\n    /**\r\n     * Serialize the object\r\n     * @returns the JSON object representing the current entity\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        serializationObject.animation = this.animation.serialize();\r\n        serializationObject.targetId = this.target.id;\r\n\r\n        return serializationObject;\r\n    }\r\n}\r\n\r\n/**\r\n * Options to be used when creating an additive group animation\r\n */\r\nexport interface IMakeAnimationGroupAdditiveOptions extends IMakeAnimationAdditiveOptions {\r\n    /**\r\n     * Defines if the animation group should be cloned or not (default is false)\r\n     */\r\n    cloneOriginalAnimationGroup?: boolean;\r\n    /**\r\n     * The name of the cloned animation group if cloneOriginalAnimationGroup is true\r\n     */\r\n    clonedAnimationGroupName?: string;\r\n}\r\n\r\n/**\r\n * Use this class to create coordinated animations on multiple targets\r\n */\r\nexport class AnimationGroup implements IDisposable {\r\n    private _scene: Scene;\r\n\r\n    private _targetedAnimations = new Array<TargetedAnimation>();\r\n    private _animatables = new Array<Animatable>();\r\n    private _from = Number.MAX_VALUE;\r\n    private _to = -Number.MAX_VALUE;\r\n    private _isStarted: boolean;\r\n    private _isPaused: boolean;\r\n    private _speedRatio = 1;\r\n    private _loopAnimation = false;\r\n    private _isAdditive = false;\r\n    private _weight = -1;\r\n    private _playOrder = 0;\r\n    private _enableBlending: Nullable<boolean> = null;\r\n    private _blendingSpeed: Nullable<number> = null;\r\n    private _numActiveAnimatables = 0;\r\n    private _shouldStart = true;\r\n\r\n    /** @internal */\r\n    public _parentContainer: Nullable<IAssetContainer> = null;\r\n\r\n    /**\r\n     * Gets or sets the unique id of the node\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * This observable will notify when one animation have ended\r\n     */\r\n    public onAnimationEndObservable = new Observable<TargetedAnimation>();\r\n\r\n    /**\r\n     * Observer raised when one animation loops\r\n     */\r\n    public onAnimationLoopObservable = new Observable<TargetedAnimation>();\r\n\r\n    /**\r\n     * Observer raised when all animations have looped\r\n     */\r\n    public onAnimationGroupLoopObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * This observable will notify when all animations have ended.\r\n     */\r\n    public onAnimationGroupEndObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * This observable will notify when all animations have paused.\r\n     */\r\n    public onAnimationGroupPauseObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * This observable will notify when all animations are playing.\r\n     */\r\n    public onAnimationGroupPlayObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the node\r\n     */\r\n    public metadata: any = null;\r\n\r\n    private _mask: Nullable<AnimationGroupMask> = null;\r\n\r\n    /**\r\n     * Gets or sets the mask associated with this animation group. This mask is used to filter which objects should be animated.\r\n     */\r\n    public get mask() {\r\n        return this._mask;\r\n    }\r\n\r\n    public set mask(value: Nullable<AnimationGroupMask>) {\r\n        if (this._mask === value) {\r\n            return;\r\n        }\r\n\r\n        this._mask = value;\r\n\r\n        this.syncWithMask(true);\r\n    }\r\n\r\n    /**\r\n     * Makes sure that the animations are either played or stopped according to the animation group mask.\r\n     * Note however that the call won't have any effect if the animation group has not been started yet.\r\n     * @param forceUpdate If true, forces to loop over the animatables even if no mask is defined (used internally, you shouldn't need to use it). Default: false.\r\n     */\r\n    public syncWithMask(forceUpdate = false) {\r\n        if (!this.mask && !forceUpdate) {\r\n            this._numActiveAnimatables = this._targetedAnimations.length;\r\n            return;\r\n        }\r\n\r\n        this._numActiveAnimatables = 0;\r\n\r\n        for (let i = 0; i < this._animatables.length; ++i) {\r\n            const animatable = this._animatables[i];\r\n\r\n            if (!this.mask || this.mask.disabled || this.mask.retainsTarget(animatable.target.name)) {\r\n                this._numActiveAnimatables++;\r\n                if (animatable.paused) {\r\n                    animatable.restart();\r\n                }\r\n            } else {\r\n                if (!animatable.paused) {\r\n                    animatable.pause();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes all animations for the targets not retained by the animation group mask.\r\n     * Use this function if you know you won't need those animations anymore and if you want to free memory.\r\n     */\r\n    public removeUnmaskedAnimations() {\r\n        if (!this.mask || this.mask.disabled) {\r\n            return;\r\n        }\r\n\r\n        // Removes all animatables (in case the animation group has already been started)\r\n        for (let i = 0; i < this._animatables.length; ++i) {\r\n            const animatable = this._animatables[i];\r\n\r\n            if (!this.mask.retainsTarget(animatable.target.name)) {\r\n                animatable.stop();\r\n                this._animatables.splice(i, 1);\r\n                --i;\r\n            }\r\n        }\r\n\r\n        // Removes the targeted animations\r\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n\r\n            if (!this.mask.retainsTarget(targetedAnimation.target.name)) {\r\n                this._targetedAnimations.splice(index, 1);\r\n                --index;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the first frame\r\n     */\r\n    public get from(): number {\r\n        return this._from;\r\n    }\r\n\r\n    public set from(value: number) {\r\n        if (this._from === value) {\r\n            return;\r\n        }\r\n\r\n        this._from = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.fromFrame = this._from;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the last frame\r\n     */\r\n    public get to(): number {\r\n        return this._to;\r\n    }\r\n\r\n    public set to(value: number) {\r\n        if (this._to === value) {\r\n            return;\r\n        }\r\n\r\n        this._to = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.toFrame = this._to;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Define if the animations are started\r\n     */\r\n    public get isStarted(): boolean {\r\n        return this._isStarted;\r\n    }\r\n\r\n    /**\r\n     * Gets a value indicating that the current group is playing\r\n     */\r\n    public get isPlaying(): boolean {\r\n        return this._isStarted && !this._isPaused;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the speed ratio to use for all animations\r\n     */\r\n    public get speedRatio(): number {\r\n        return this._speedRatio;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the speed ratio to use for all animations\r\n     */\r\n    public set speedRatio(value: number) {\r\n        if (this._speedRatio === value) {\r\n            return;\r\n        }\r\n\r\n        this._speedRatio = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.speedRatio = this._speedRatio;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets if all animations should loop or not\r\n     */\r\n    public get loopAnimation(): boolean {\r\n        return this._loopAnimation;\r\n    }\r\n\r\n    public set loopAnimation(value: boolean) {\r\n        if (this._loopAnimation === value) {\r\n            return;\r\n        }\r\n\r\n        this._loopAnimation = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.loopAnimation = this._loopAnimation;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets if all animations should be evaluated additively\r\n     */\r\n    public get isAdditive(): boolean {\r\n        return this._isAdditive;\r\n    }\r\n\r\n    public set isAdditive(value: boolean) {\r\n        if (this._isAdditive === value) {\r\n            return;\r\n        }\r\n\r\n        this._isAdditive = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.isAdditive = this._isAdditive;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the weight to apply to all animations of the group\r\n     */\r\n    public get weight(): number {\r\n        return this._weight;\r\n    }\r\n\r\n    public set weight(value: number) {\r\n        if (this._weight === value) {\r\n            return;\r\n        }\r\n\r\n        this._weight = value;\r\n        this.setWeightForAllAnimatables(this._weight);\r\n    }\r\n\r\n    /**\r\n     * Gets the targeted animations for this animation group\r\n     */\r\n    public get targetedAnimations(): Array<TargetedAnimation> {\r\n        return this._targetedAnimations;\r\n    }\r\n\r\n    /**\r\n     * returning the list of animatables controlled by this animation group.\r\n     */\r\n    public get animatables(): Array<Animatable> {\r\n        return this._animatables;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of target animations\r\n     */\r\n    public get children() {\r\n        return this._targetedAnimations;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the order of play of the animation group (default: 0)\r\n     */\r\n    public get playOrder() {\r\n        return this._playOrder;\r\n    }\r\n\r\n    public set playOrder(value: number) {\r\n        if (this._playOrder === value) {\r\n            return;\r\n        }\r\n\r\n        this._playOrder = value;\r\n\r\n        if (this._animatables.length > 0) {\r\n            for (let i = 0; i < this._animatables.length; i++) {\r\n                this._animatables[i].playOrder = this._playOrder;\r\n            }\r\n\r\n            this._scene.sortActiveAnimatables();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Allows the animations of the animation group to blend with current running animations\r\n     * Note that a null value means that each animation will use their own existing blending configuration (Animation.enableBlending)\r\n     */\r\n    public get enableBlending() {\r\n        return this._enableBlending;\r\n    }\r\n\r\n    public set enableBlending(value: Nullable<boolean>) {\r\n        if (this._enableBlending === value) {\r\n            return;\r\n        }\r\n\r\n        this._enableBlending = value;\r\n\r\n        if (value !== null) {\r\n            for (let i = 0; i < this._targetedAnimations.length; ++i) {\r\n                this._targetedAnimations[i].animation.enableBlending = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the animation blending speed\r\n     * Note that a null value means that each animation will use their own existing blending configuration (Animation.blendingSpeed)\r\n     */\r\n    public get blendingSpeed() {\r\n        return this._blendingSpeed;\r\n    }\r\n\r\n    public set blendingSpeed(value: Nullable<number>) {\r\n        if (this._blendingSpeed === value) {\r\n            return;\r\n        }\r\n\r\n        this._blendingSpeed = value;\r\n\r\n        if (value !== null) {\r\n            for (let i = 0; i < this._targetedAnimations.length; ++i) {\r\n                this._targetedAnimations[i].animation.blendingSpeed = value;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the length (in seconds) of the animation group\r\n     * This function assumes that all animations are played at the same framePerSecond speed!\r\n     * Note: you can only call this method after you've added at least one targeted animation!\r\n     * @param from Starting frame range (default is AnimationGroup.from)\r\n     * @param to Ending frame range (default is AnimationGroup.to)\r\n     * @returns The length in seconds\r\n     */\r\n    public getLength(from?: number, to?: number): number {\r\n        from = from ?? this._from;\r\n        to = to ?? this._to;\r\n\r\n        const fps = this.targetedAnimations[0].animation.framePerSecond * this._speedRatio;\r\n\r\n        return (to - from) / fps;\r\n    }\r\n\r\n    /**\r\n     * Merge the array of animation groups into a new animation group\r\n     * @param animationGroups List of animation groups to merge\r\n     * @param disposeSource If true, animation groups will be disposed after being merged (default: true)\r\n     * @param normalize If true, animation groups will be normalized before being merged, so that all animations have the same \"from\" and \"to\" frame (default: false)\r\n     * @param weight Weight for the new animation group. If not provided, it will inherit the weight from the first animation group of the array\r\n     * @returns The new animation group or null if no animation groups were passed\r\n     */\r\n    public static MergeAnimationGroups(animationGroups: Array<AnimationGroup>, disposeSource = true, normalize = false, weight?: number): Nullable<AnimationGroup> {\r\n        if (animationGroups.length === 0) {\r\n            return null;\r\n        }\r\n\r\n        weight = weight ?? animationGroups[0].weight;\r\n\r\n        let beginFrame = Number.MAX_VALUE;\r\n        let endFrame = -Number.MAX_VALUE;\r\n\r\n        if (normalize) {\r\n            for (const animationGroup of animationGroups) {\r\n                if (animationGroup.from < beginFrame) {\r\n                    beginFrame = animationGroup.from;\r\n                }\r\n\r\n                if (animationGroup.to > endFrame) {\r\n                    endFrame = animationGroup.to;\r\n                }\r\n            }\r\n        }\r\n\r\n        const mergedAnimationGroup = new AnimationGroup(animationGroups[0].name + \"_merged\", animationGroups[0]._scene, weight);\r\n\r\n        for (const animationGroup of animationGroups) {\r\n            if (normalize) {\r\n                animationGroup.normalize(beginFrame, endFrame);\r\n            }\r\n\r\n            for (const targetedAnimation of animationGroup.targetedAnimations) {\r\n                mergedAnimationGroup.addTargetedAnimation(targetedAnimation.animation, targetedAnimation.target);\r\n            }\r\n\r\n            if (disposeSource) {\r\n                animationGroup.dispose();\r\n            }\r\n        }\r\n\r\n        return mergedAnimationGroup;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new Animation Group.\r\n     * This helps managing several animations at once.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/groupAnimations\r\n     * @param name Defines the name of the group\r\n     * @param scene Defines the scene the group belongs to\r\n     * @param weight Defines the weight to use for animations in the group (-1.0 by default, meaning \"no weight\")\r\n     * @param playOrder Defines the order of play of the animation group (default is 0)\r\n     */\r\n    public constructor(\r\n        /** The name of the animation group */\r\n        public name: string,\r\n        scene: Nullable<Scene> = null,\r\n        weight = -1,\r\n        playOrder = 0\r\n    ) {\r\n        this._scene = scene || EngineStore.LastCreatedScene!;\r\n        this._weight = weight;\r\n        this._playOrder = playOrder;\r\n        this.uniqueId = this._scene.getUniqueId();\r\n\r\n        this._scene.addAnimationGroup(this);\r\n    }\r\n\r\n    /**\r\n     * Add an animation (with its target) in the group\r\n     * @param animation defines the animation we want to add\r\n     * @param target defines the target of the animation\r\n     * @returns the TargetedAnimation object\r\n     */\r\n    public addTargetedAnimation(animation: Animation, target: any): TargetedAnimation {\r\n        const targetedAnimation = new TargetedAnimation(this);\r\n        targetedAnimation.animation = animation;\r\n        targetedAnimation.target = target;\r\n\r\n        const keys = animation.getKeys();\r\n        if (this._from > keys[0].frame) {\r\n            this._from = keys[0].frame;\r\n        }\r\n\r\n        if (this._to < keys[keys.length - 1].frame) {\r\n            this._to = keys[keys.length - 1].frame;\r\n        }\r\n\r\n        if (this._enableBlending !== null) {\r\n            animation.enableBlending = this._enableBlending;\r\n        }\r\n\r\n        if (this._blendingSpeed !== null) {\r\n            animation.blendingSpeed = this._blendingSpeed;\r\n        }\r\n\r\n        this._targetedAnimations.push(targetedAnimation);\r\n        this._shouldStart = true;\r\n\r\n        return targetedAnimation;\r\n    }\r\n\r\n    /**\r\n     * Remove an animation from the group\r\n     * @param animation defines the animation we want to remove\r\n     */\r\n    public removeTargetedAnimation(animation: Animation) {\r\n        for (let index = this._targetedAnimations.length - 1; index > -1; index--) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n            if (targetedAnimation.animation === animation) {\r\n                this._targetedAnimations.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * This function will normalize every animation in the group to make sure they all go from beginFrame to endFrame\r\n     * It can add constant keys at begin or end\r\n     * @param beginFrame defines the new begin frame for all animations or the smallest begin frame of all animations if null (defaults to null)\r\n     * @param endFrame defines the new end frame for all animations or the largest end frame of all animations if null (defaults to null)\r\n     * @returns the animation group\r\n     */\r\n    public normalize(beginFrame: Nullable<number> = null, endFrame: Nullable<number> = null): AnimationGroup {\r\n        if (beginFrame == null) {\r\n            beginFrame = this._from;\r\n        }\r\n        if (endFrame == null) {\r\n            endFrame = this._to;\r\n        }\r\n\r\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n            const keys = targetedAnimation.animation.getKeys();\r\n            const startKey = keys[0];\r\n            const endKey = keys[keys.length - 1];\r\n\r\n            if (startKey.frame > beginFrame) {\r\n                const newKey: IAnimationKey = {\r\n                    frame: beginFrame,\r\n                    value: startKey.value,\r\n                    inTangent: startKey.inTangent,\r\n                    outTangent: startKey.outTangent,\r\n                    interpolation: startKey.interpolation,\r\n                };\r\n                keys.splice(0, 0, newKey);\r\n            }\r\n\r\n            if (endKey.frame < endFrame) {\r\n                const newKey: IAnimationKey = {\r\n                    frame: endFrame,\r\n                    value: endKey.value,\r\n                    inTangent: endKey.inTangent,\r\n                    outTangent: endKey.outTangent,\r\n                    interpolation: endKey.interpolation,\r\n                };\r\n                keys.push(newKey);\r\n            }\r\n        }\r\n\r\n        this._from = beginFrame;\r\n        this._to = endFrame;\r\n\r\n        return this;\r\n    }\r\n\r\n    private _animationLoopCount: number;\r\n    private _animationLoopFlags: boolean[] = [];\r\n\r\n    private _processLoop(animatable: Animatable, targetedAnimation: TargetedAnimation, index: number) {\r\n        animatable.onAnimationLoop = () => {\r\n            this.onAnimationLoopObservable.notifyObservers(targetedAnimation);\r\n\r\n            if (this._animationLoopFlags[index]) {\r\n                return;\r\n            }\r\n\r\n            this._animationLoopFlags[index] = true;\r\n\r\n            this._animationLoopCount++;\r\n            if (this._animationLoopCount === this._numActiveAnimatables) {\r\n                this.onAnimationGroupLoopObservable.notifyObservers(this);\r\n                this._animationLoopCount = 0;\r\n                this._animationLoopFlags.length = 0;\r\n            }\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Start all animations on given targets\r\n     * @param loop defines if animations must loop\r\n     * @param speedRatio defines the ratio to apply to animation speed (1 by default)\r\n     * @param from defines the from key (optional)\r\n     * @param to defines the to key (optional)\r\n     * @param isAdditive defines the additive state for the resulting animatables (optional)\r\n     * @returns the current animation group\r\n     */\r\n    public start(loop = false, speedRatio = 1, from?: number, to?: number, isAdditive?: boolean): AnimationGroup {\r\n        if (this._isStarted || this._targetedAnimations.length === 0) {\r\n            return this;\r\n        }\r\n\r\n        this._loopAnimation = loop;\r\n\r\n        this._shouldStart = false;\r\n        this._animationLoopCount = 0;\r\n        this._animationLoopFlags.length = 0;\r\n\r\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n            const animatable = this._scene.beginDirectAnimation(\r\n                targetedAnimation.target,\r\n                [targetedAnimation.animation],\r\n                from !== undefined ? from : this._from,\r\n                to !== undefined ? to : this._to,\r\n                loop,\r\n                speedRatio,\r\n                undefined,\r\n                undefined,\r\n                isAdditive !== undefined ? isAdditive : this._isAdditive\r\n            );\r\n            animatable.weight = this._weight;\r\n            animatable.playOrder = this._playOrder;\r\n            animatable.onAnimationEnd = () => {\r\n                this.onAnimationEndObservable.notifyObservers(targetedAnimation);\r\n                this._checkAnimationGroupEnded(animatable);\r\n            };\r\n\r\n            this._processLoop(animatable, targetedAnimation, index);\r\n            this._animatables.push(animatable);\r\n        }\r\n\r\n        this.syncWithMask();\r\n\r\n        this._scene.sortActiveAnimatables();\r\n\r\n        this._speedRatio = speedRatio;\r\n\r\n        this._isStarted = true;\r\n        this._isPaused = false;\r\n\r\n        this.onAnimationGroupPlayObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Pause all animations\r\n     * @returns the animation group\r\n     */\r\n    public pause(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        this._isPaused = true;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.pause();\r\n        }\r\n\r\n        this.onAnimationGroupPauseObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Play all animations to initial state\r\n     * This function will start() the animations if they were not started or will restart() them if they were paused\r\n     * @param loop defines if animations must loop\r\n     * @returns the animation group\r\n     */\r\n    public play(loop?: boolean): AnimationGroup {\r\n        // only if there are animatable available\r\n        if (this.isStarted && this._animatables.length && !this._shouldStart) {\r\n            if (loop !== undefined) {\r\n                this.loopAnimation = loop;\r\n            }\r\n            this.restart();\r\n        } else {\r\n            this.stop();\r\n            this.start(loop, this._speedRatio);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Reset all animations to initial state\r\n     * @returns the animation group\r\n     */\r\n    public reset(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            this.play();\r\n            this.goToFrame(0);\r\n            this.stop(true);\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.reset();\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Restart animations from after pausing it\r\n     * @returns the animation group\r\n     */\r\n    public restart(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.restart();\r\n        }\r\n\r\n        this.syncWithMask();\r\n\r\n        this._isPaused = false;\r\n\r\n        this.onAnimationGroupPlayObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Stop all animations\r\n     * @param skipOnAnimationEnd defines if the system should not raise onAnimationEnd. Default is false\r\n     * @returns the animation group\r\n     */\r\n    public stop(skipOnAnimationEnd = false): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        const list = this._animatables.slice();\r\n        for (let index = 0; index < list.length; index++) {\r\n            list[index].stop(undefined, undefined, true, skipOnAnimationEnd);\r\n        }\r\n\r\n        // We will take care of removing all stopped animatables\r\n        let curIndex = 0;\r\n        for (let index = 0; index < this._scene._activeAnimatables.length; index++) {\r\n            const animatable = this._scene._activeAnimatables[index];\r\n            if (animatable._runtimeAnimations.length > 0) {\r\n                this._scene._activeAnimatables[curIndex++] = animatable;\r\n            } else if (skipOnAnimationEnd) {\r\n                // We normally rely on the onAnimationEnd callback (assigned in the start function) to be notified when an animatable\r\n                // ends and should be removed from the active animatables array. However, if the animatable is stopped with the skipOnAnimationEnd\r\n                // flag set to true, then we need to explicitly remove it from the active animatables array.\r\n                this._checkAnimationGroupEnded(animatable, skipOnAnimationEnd);\r\n            }\r\n        }\r\n        this._scene._activeAnimatables.length = curIndex;\r\n\r\n        this._isStarted = false;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set animation weight for all animatables\r\n     *\r\n     * @since 6.12.4\r\n     *  You can pass the weight to the AnimationGroup constructor, or use the weight property to set it after the group has been created,\r\n     *  making it easier to define the overall animation weight than calling setWeightForAllAnimatables() after the animation group has been started\r\n     * @param weight defines the weight to use\r\n     * @returns the animationGroup\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights\r\n     */\r\n    public setWeightForAllAnimatables(weight: number): AnimationGroup {\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.weight = weight;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Synchronize and normalize all animatables with a source animatable\r\n     * @param root defines the root animatable to synchronize with (null to stop synchronizing)\r\n     * @returns the animationGroup\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights\r\n     */\r\n    public syncAllAnimationsWith(root: Nullable<Animatable>): AnimationGroup {\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.syncWith(root);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Goes to a specific frame in this animation group. Note that the animation group must be in playing or paused status\r\n     * @param frame the frame number to go to\r\n     * @param useWeight defines whether the animation weight should be applied to the image to be jumped to (false by default)\r\n     * @returns the animationGroup\r\n     */\r\n    public goToFrame(frame: number, useWeight = false): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.goToFrame(frame, useWeight);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Helper to get the current frame. This will return 0 if the AnimationGroup is not running, and it might return wrong results if multiple animations are running in different frames.\r\n     * @returns current animation frame.\r\n     */\r\n    public getCurrentFrame(): number {\r\n        return this.animatables[0]?.masterFrame || 0;\r\n    }\r\n\r\n    /**\r\n     * Dispose all associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this.isStarted) {\r\n            this.stop();\r\n        }\r\n        this._targetedAnimations.length = 0;\r\n        this._animatables.length = 0;\r\n\r\n        // Remove from scene\r\n        const index = this._scene.animationGroups.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._scene.animationGroups.splice(index, 1);\r\n        }\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.animationGroups.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.animationGroups.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        this.onAnimationEndObservable.clear();\r\n        this.onAnimationGroupEndObservable.clear();\r\n        this.onAnimationGroupPauseObservable.clear();\r\n        this.onAnimationGroupPlayObservable.clear();\r\n        this.onAnimationLoopObservable.clear();\r\n        this.onAnimationGroupLoopObservable.clear();\r\n    }\r\n\r\n    private _checkAnimationGroupEnded(animatable: Animatable, skipOnAnimationEnd = false) {\r\n        // animatable should be taken out of the array\r\n        const idx = this._animatables.indexOf(animatable);\r\n        if (idx > -1) {\r\n            this._animatables.splice(idx, 1);\r\n        }\r\n\r\n        // all animatables were removed? animation group ended!\r\n        if (this._animatables.length === this._targetedAnimations.length - this._numActiveAnimatables) {\r\n            this._isStarted = false;\r\n            if (!skipOnAnimationEnd) {\r\n                this.onAnimationGroupEndObservable.notifyObservers(this);\r\n            }\r\n            this._animatables.length = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clone the current animation group and returns a copy\r\n     * @param newName defines the name of the new group\r\n     * @param targetConverter defines an optional function used to convert current animation targets to new ones\r\n     * @param cloneAnimations defines if the animations should be cloned or referenced\r\n     * @returns the new animation group\r\n     */\r\n    public clone(newName: string, targetConverter?: (oldTarget: any) => any, cloneAnimations = false): AnimationGroup {\r\n        const newGroup = new AnimationGroup(newName || this.name, this._scene, this._weight, this._playOrder);\r\n\r\n        newGroup._from = this.from;\r\n        newGroup._to = this.to;\r\n        newGroup._speedRatio = this.speedRatio;\r\n        newGroup._loopAnimation = this.loopAnimation;\r\n        newGroup._isAdditive = this.isAdditive;\r\n        newGroup._enableBlending = this.enableBlending;\r\n        newGroup._blendingSpeed = this.blendingSpeed;\r\n        newGroup.metadata = this.metadata;\r\n        newGroup.mask = this.mask;\r\n\r\n        for (const targetAnimation of this._targetedAnimations) {\r\n            newGroup.addTargetedAnimation(\r\n                cloneAnimations ? targetAnimation.animation.clone() : targetAnimation.animation,\r\n                targetConverter ? targetConverter(targetAnimation.target) : targetAnimation.target\r\n            );\r\n        }\r\n\r\n        return newGroup;\r\n    }\r\n\r\n    /**\r\n     * Serializes the animationGroup to an object\r\n     * @returns Serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.from = this.from;\r\n        serializationObject.to = this.to;\r\n        serializationObject.speedRatio = this.speedRatio;\r\n        serializationObject.loopAnimation = this.loopAnimation;\r\n        serializationObject.isAdditive = this.isAdditive;\r\n        serializationObject.weight = this.weight;\r\n        serializationObject.playOrder = this.playOrder;\r\n        serializationObject.enableBlending = this.enableBlending;\r\n        serializationObject.blendingSpeed = this.blendingSpeed;\r\n\r\n        serializationObject.targetedAnimations = [];\r\n        for (let targetedAnimationIndex = 0; targetedAnimationIndex < this.targetedAnimations.length; targetedAnimationIndex++) {\r\n            const targetedAnimation = this.targetedAnimations[targetedAnimationIndex];\r\n            serializationObject.targetedAnimations[targetedAnimationIndex] = targetedAnimation.serialize();\r\n        }\r\n\r\n        if (Tags && Tags.HasTags(this)) {\r\n            serializationObject.tags = Tags.GetTags(this);\r\n        }\r\n\r\n        // Metadata\r\n        if (this.metadata) {\r\n            serializationObject.metadata = this.metadata;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Returns a new AnimationGroup object parsed from the source provided.\r\n     * @param parsedAnimationGroup defines the source\r\n     * @param scene defines the scene that will receive the animationGroup\r\n     * @param nodeMap a map of node.id to node in this scene, to accelerate node lookup\r\n     * @returns a new AnimationGroup\r\n     */\r\n    public static Parse(parsedAnimationGroup: any, scene: Scene, nodeMap?: Map<Node[\"id\"], Node>): AnimationGroup {\r\n        const animationGroup = new AnimationGroup(parsedAnimationGroup.name, scene, parsedAnimationGroup.weight, parsedAnimationGroup.playOrder);\r\n        for (let i = 0; i < parsedAnimationGroup.targetedAnimations.length; i++) {\r\n            const targetedAnimation = parsedAnimationGroup.targetedAnimations[i];\r\n            const animation = Animation.Parse(targetedAnimation.animation);\r\n            const id = targetedAnimation.targetId;\r\n            if (targetedAnimation.animation.property === \"influence\") {\r\n                // morph target animation\r\n                const morphTarget = scene.getMorphTargetById(id);\r\n                if (morphTarget) {\r\n                    animationGroup.addTargetedAnimation(animation, morphTarget);\r\n                }\r\n            } else {\r\n                const targetNode = nodeMap ? nodeMap.get(id) : scene.getNodeById(id);\r\n\r\n                if (targetNode != null) {\r\n                    animationGroup.addTargetedAnimation(animation, targetNode);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (Tags) {\r\n            Tags.AddTagsTo(animationGroup, parsedAnimationGroup.tags);\r\n        }\r\n\r\n        if (parsedAnimationGroup.from !== null && parsedAnimationGroup.to !== null) {\r\n            animationGroup.normalize(parsedAnimationGroup.from, parsedAnimationGroup.to);\r\n        }\r\n\r\n        if (parsedAnimationGroup.speedRatio !== undefined) {\r\n            animationGroup._speedRatio = parsedAnimationGroup.speedRatio;\r\n        }\r\n        if (parsedAnimationGroup.loopAnimation !== undefined) {\r\n            animationGroup._loopAnimation = parsedAnimationGroup.loopAnimation;\r\n        }\r\n\r\n        if (parsedAnimationGroup.isAdditive !== undefined) {\r\n            animationGroup._isAdditive = parsedAnimationGroup.isAdditive;\r\n        }\r\n\r\n        if (parsedAnimationGroup.weight !== undefined) {\r\n            animationGroup._weight = parsedAnimationGroup.weight;\r\n        }\r\n\r\n        if (parsedAnimationGroup.playOrder !== undefined) {\r\n            animationGroup._playOrder = parsedAnimationGroup.playOrder;\r\n        }\r\n\r\n        if (parsedAnimationGroup.enableBlending !== undefined) {\r\n            animationGroup._enableBlending = parsedAnimationGroup.enableBlending;\r\n        }\r\n\r\n        if (parsedAnimationGroup.blendingSpeed !== undefined) {\r\n            animationGroup._blendingSpeed = parsedAnimationGroup.blendingSpeed;\r\n        }\r\n\r\n        if (parsedAnimationGroup.metadata !== undefined) {\r\n            animationGroup.metadata = parsedAnimationGroup.metadata;\r\n        }\r\n\r\n        return animationGroup;\r\n    }\r\n\r\n    /**\r\n     * Convert the keyframes for all animations belonging to the group to be relative to a given reference frame.\r\n     * @param sourceAnimationGroup defines the AnimationGroup containing animations to convert\r\n     * @param referenceFrame defines the frame that keyframes in the range will be relative to (default: 0)\r\n     * @param range defines the name of the AnimationRange belonging to the animations in the group to convert\r\n     * @param cloneOriginal defines whether or not to clone the group and convert the clone or convert the original group (default is false)\r\n     * @param clonedName defines the name of the resulting cloned AnimationGroup if cloneOriginal is true\r\n     * @returns a new AnimationGroup if cloneOriginal is true or the original AnimationGroup if cloneOriginal is false\r\n     */\r\n    public static MakeAnimationAdditive(sourceAnimationGroup: AnimationGroup, referenceFrame: number, range?: string, cloneOriginal?: boolean, clonedName?: string): AnimationGroup;\r\n\r\n    /**\r\n     * Convert the keyframes for all animations belonging to the group to be relative to a given reference frame.\r\n     * @param sourceAnimationGroup defines the AnimationGroup containing animations to convert\r\n     * @param options defines the options to use when converting keyframes\r\n     * @returns a new AnimationGroup if options.cloneOriginalAnimationGroup is true or the original AnimationGroup if options.cloneOriginalAnimationGroup is false\r\n     */\r\n    public static MakeAnimationAdditive(sourceAnimationGroup: AnimationGroup, options?: IMakeAnimationGroupAdditiveOptions): AnimationGroup;\r\n\r\n    /** @internal */\r\n    public static MakeAnimationAdditive(\r\n        sourceAnimationGroup: AnimationGroup,\r\n        referenceFrameOrOptions?: number | IMakeAnimationGroupAdditiveOptions,\r\n        range?: string,\r\n        cloneOriginal = false,\r\n        clonedName?: string\r\n    ): AnimationGroup {\r\n        let options: IMakeAnimationGroupAdditiveOptions;\r\n\r\n        if (typeof referenceFrameOrOptions === \"object\") {\r\n            options = referenceFrameOrOptions;\r\n        } else {\r\n            options = {\r\n                referenceFrame: referenceFrameOrOptions,\r\n                range: range,\r\n                cloneOriginalAnimationGroup: cloneOriginal,\r\n                clonedAnimationName: clonedName,\r\n            };\r\n        }\r\n\r\n        let animationGroup = sourceAnimationGroup;\r\n        if (options.cloneOriginalAnimationGroup) {\r\n            animationGroup = sourceAnimationGroup.clone(options.clonedAnimationGroupName || animationGroup.name);\r\n        }\r\n\r\n        const targetedAnimations = animationGroup.targetedAnimations;\r\n        for (let index = 0; index < targetedAnimations.length; index++) {\r\n            const targetedAnimation = targetedAnimations[index];\r\n            targetedAnimation.animation = Animation.MakeAnimationAdditive(targetedAnimation.animation, options);\r\n        }\r\n\r\n        animationGroup.isAdditive = true;\r\n\r\n        if (options.clipKeys) {\r\n            // We need to recalculate the from/to frames for the animation group because some keys may have been removed\r\n            let from = Number.MAX_VALUE;\r\n            let to = -Number.MAX_VALUE;\r\n\r\n            const targetedAnimations = animationGroup.targetedAnimations;\r\n            for (let index = 0; index < targetedAnimations.length; index++) {\r\n                const targetedAnimation = targetedAnimations[index];\r\n                const animation = targetedAnimation.animation;\r\n                const keys = animation.getKeys();\r\n\r\n                if (from > keys[0].frame) {\r\n                    from = keys[0].frame;\r\n                }\r\n\r\n                if (to < keys[keys.length - 1].frame) {\r\n                    to = keys[keys.length - 1].frame;\r\n                }\r\n            }\r\n\r\n            animationGroup._from = from;\r\n            animationGroup._to = to;\r\n        }\r\n\r\n        return animationGroup;\r\n    }\r\n\r\n    /**\r\n     * Creates a new animation, keeping only the keys that are inside a given key range\r\n     * @param sourceAnimationGroup defines the animation group on which to operate\r\n     * @param fromKey defines the lower bound of the range\r\n     * @param toKey defines the upper bound of the range\r\n     * @param name defines the name of the new animation group. If not provided, use the same name as animationGroup\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the keys. Default is false, so animations will be cloned\r\n     * @returns a new animation group stripped from all the keys outside the given range\r\n     */\r\n    public static ClipKeys(sourceAnimationGroup: AnimationGroup, fromKey: number, toKey: number, name?: string, dontCloneAnimations?: boolean): AnimationGroup {\r\n        const animationGroup = sourceAnimationGroup.clone(name || sourceAnimationGroup.name);\r\n\r\n        return AnimationGroup.ClipKeysInPlace(animationGroup, fromKey, toKey, dontCloneAnimations);\r\n    }\r\n\r\n    /**\r\n     * Updates an existing animation, keeping only the keys that are inside a given key range\r\n     * @param animationGroup defines the animation group on which to operate\r\n     * @param fromKey defines the lower bound of the range\r\n     * @param toKey defines the upper bound of the range\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the keys. Default is false, so animations will be cloned\r\n     * @returns the animationGroup stripped from all the keys outside the given range\r\n     */\r\n    public static ClipKeysInPlace(animationGroup: AnimationGroup, fromKey: number, toKey: number, dontCloneAnimations?: boolean): AnimationGroup {\r\n        return AnimationGroup.ClipInPlace(animationGroup, fromKey, toKey, dontCloneAnimations, false);\r\n    }\r\n\r\n    /**\r\n     * Creates a new animation, keeping only the frames that are inside a given frame range\r\n     * @param sourceAnimationGroup defines the animation group on which to operate\r\n     * @param fromFrame defines the lower bound of the range\r\n     * @param toFrame defines the upper bound of the range\r\n     * @param name defines the name of the new animation group. If not provided, use the same name as animationGroup\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the frames. Default is false, so animations will be cloned\r\n     * @returns a new animation group stripped from all the frames outside the given range\r\n     */\r\n    public static ClipFrames(sourceAnimationGroup: AnimationGroup, fromFrame: number, toFrame: number, name?: string, dontCloneAnimations?: boolean): AnimationGroup {\r\n        const animationGroup = sourceAnimationGroup.clone(name || sourceAnimationGroup.name);\r\n\r\n        return AnimationGroup.ClipFramesInPlace(animationGroup, fromFrame, toFrame, dontCloneAnimations);\r\n    }\r\n\r\n    /**\r\n     * Updates an existing animation, keeping only the frames that are inside a given frame range\r\n     * @param animationGroup defines the animation group on which to operate\r\n     * @param fromFrame defines the lower bound of the range\r\n     * @param toFrame defines the upper bound of the range\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the frames. Default is false, so animations will be cloned\r\n     * @returns the animationGroup stripped from all the frames outside the given range\r\n     */\r\n    public static ClipFramesInPlace(animationGroup: AnimationGroup, fromFrame: number, toFrame: number, dontCloneAnimations?: boolean): AnimationGroup {\r\n        return AnimationGroup.ClipInPlace(animationGroup, fromFrame, toFrame, dontCloneAnimations, true);\r\n    }\r\n\r\n    /**\r\n     * Updates an existing animation, keeping only the keys that are inside a given key or frame range\r\n     * @param animationGroup defines the animation group on which to operate\r\n     * @param start defines the lower bound of the range\r\n     * @param end defines the upper bound of the range\r\n     * @param dontCloneAnimations defines whether or not the animations should be cloned before clipping the keys. Default is false, so animations will be cloned\r\n     * @param useFrame defines if the range is defined by frame numbers or key indices (default is false which means use key indices)\r\n     * @returns the animationGroup stripped from all the keys outside the given range\r\n     */\r\n    public static ClipInPlace(animationGroup: AnimationGroup, start: number, end: number, dontCloneAnimations?: boolean, useFrame = false): AnimationGroup {\r\n        let from = Number.MAX_VALUE;\r\n        let to = -Number.MAX_VALUE;\r\n\r\n        const targetedAnimations = animationGroup.targetedAnimations;\r\n        for (let index = 0; index < targetedAnimations.length; index++) {\r\n            const targetedAnimation = targetedAnimations[index];\r\n            const animation = dontCloneAnimations ? targetedAnimation.animation : targetedAnimation.animation.clone();\r\n\r\n            if (useFrame) {\r\n                // Make sure we have keys corresponding to the bounds of the frame range\r\n                animation.createKeyForFrame(start);\r\n                animation.createKeyForFrame(end);\r\n            }\r\n\r\n            const keys = animation.getKeys();\r\n            const newKeys: IAnimationKey[] = [];\r\n\r\n            let startFrame = Number.MAX_VALUE;\r\n            for (let k = 0; k < keys.length; k++) {\r\n                const key = keys[k];\r\n                if ((!useFrame && k >= start && k <= end) || (useFrame && key.frame >= start && key.frame <= end)) {\r\n                    const newKey: IAnimationKey = {\r\n                        frame: key.frame,\r\n                        value: key.value.clone ? key.value.clone() : key.value,\r\n                        inTangent: key.inTangent,\r\n                        outTangent: key.outTangent,\r\n                        interpolation: key.interpolation,\r\n                        lockedTangent: key.lockedTangent,\r\n                    };\r\n                    if (startFrame === Number.MAX_VALUE) {\r\n                        startFrame = newKey.frame;\r\n                    }\r\n                    newKey.frame -= startFrame;\r\n                    newKeys.push(newKey);\r\n                }\r\n            }\r\n\r\n            if (newKeys.length === 0) {\r\n                targetedAnimations.splice(index, 1);\r\n                index--;\r\n                continue;\r\n            }\r\n\r\n            if (from > newKeys[0].frame) {\r\n                from = newKeys[0].frame;\r\n            }\r\n\r\n            if (to < newKeys[newKeys.length - 1].frame) {\r\n                to = newKeys[newKeys.length - 1].frame;\r\n            }\r\n\r\n            animation.setKeys(newKeys, true);\r\n            targetedAnimation.animation = animation; // in case the animation has been cloned\r\n        }\r\n\r\n        animationGroup._from = from;\r\n        animationGroup._to = to;\r\n\r\n        return animationGroup;\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"AnimationGroup\"\r\n     * @returns \"AnimationGroup\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"AnimationGroup\";\r\n    }\r\n\r\n    /**\r\n     * Creates a detailed string about the object\r\n     * @param fullDetails defines if the output string will support multiple levels of logging within scene loading\r\n     * @returns a string representing the object\r\n     */\r\n    public toString(fullDetails?: boolean): string {\r\n        let ret = \"Name: \" + this.name;\r\n        ret += \", type: \" + this.getClassName();\r\n        if (fullDetails) {\r\n            ret += \", from: \" + this._from;\r\n            ret += \", to: \" + this._to;\r\n            ret += \", isStarted: \" + this._isStarted;\r\n            ret += \", speedRatio: \" + this._speedRatio;\r\n            ret += \", targetedAnimations length: \" + this._targetedAnimations.length;\r\n            ret += \", animatables length: \" + this._animatables;\r\n        }\r\n        return ret;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAKxC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAGrD,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAEpC,OAAO,cAAc,CAAC;AAEtB,OAAO,EAAE,iBAAiB,EAAE,qCAAoC;;;;;;;AAK1D,MAAO,iBAAiB;IAgB1B;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAQD;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,GAAQ,CAAA,CAAE,CAAC;QACpC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC3D,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QAE9C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAhBD;;;OAGG,CACH,YAA4B,MAAsB,CAAA;QAAtB,IAAA,CAAA,MAAM,GAAN,MAAM,CAAgB;QAjBlD;;WAEG,CACa,IAAA,CAAA,QAAQ,uKAAG,oBAAiB,CAAC,QAAQ,CAAC;IAcD,CAAC;CAazD;AAmBK,MAAO,cAAc;IAgEvB;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAmC,EAAA;QAC/C,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG,CACI,YAAY,GAAoB;0BAAnB,WAAW,sDAAG,KAAK;QACnC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YAC7B,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YAC7D,OAAO;QACX,CAAC;QAED,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;QAE/B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtF,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC7B,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;oBACpB,UAAU,CAAC,OAAO,EAAE,CAAC;gBACzB,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;oBACrB,UAAU,CAAC,KAAK,EAAE,CAAC;gBACvB,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACI,wBAAwB,GAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnC,OAAO;QACX,CAAC;QAED,iFAAiF;QACjF,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;YAChD,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnD,UAAU,CAAC,IAAI,EAAE,CAAC;gBAClB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/B,EAAE,CAAC,CAAC;YACR,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACnE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAE1D,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC1C,EAAE,KAAK,CAAC;YACZ,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa,EAAA;QACzB,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;QACtC,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB,CAAC;IAED,IAAW,EAAE,CAAC,KAAa,EAAA;QACvB,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC;QAEjB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;IAC9C,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAc,EAAA;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAE5B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QACnD,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAc,EAAA;QAChC,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,KAAa,EAAA;QAC3B,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG,CACH,IAAW,kBAAkB,GAAA;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAa,EAAA;QAC9B,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBAChD,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;YACrD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;QACxC,CAAC;IACL,CAAC;IAED;;;OAGG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED,IAAW,cAAc,CAAC,KAAwB,EAAA;QAC9C,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAE7B,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;gBACvD,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,KAAK,CAAC;YACjE,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAuB,EAAA;QAC5C,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAE5B,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAE,CAAC;gBACvD,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,aAAa,GAAG,KAAK,CAAC;YAChE,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;OAOG,CACI,SAAS,CAAC,IAAa,EAAE,EAAW,EAAA;QACvC,IAAI,sCAAG,IAAI,GAAI,IAAI,CAAC,KAAK,CAAC;QAC1B,EAAE,kCAAG,EAAE,GAAI,IAAI,CAAC,GAAG,CAAC;QAEpB,MAAM,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;QAEnF,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,oBAAoB,CAAC,eAAsC,EAA0D;4BAAxD,aAAa,oDAAG,IAAI,cAAE,SAAS,wDAAG,KAAK,EAAE,MAAe;QAC/H,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,0CAAG,MAAM,GAAI,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAE7C,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;QAClC,IAAI,QAAQ,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QAEjC,IAAI,SAAS,EAAE,CAAC;YACZ,KAAK,MAAM,cAAc,IAAI,eAAe,CAAE,CAAC;gBAC3C,IAAI,cAAc,CAAC,IAAI,GAAG,UAAU,EAAE,CAAC;oBACnC,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC;gBACrC,CAAC;gBAED,IAAI,cAAc,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC;oBAC/B,QAAQ,GAAG,cAAc,CAAC,EAAE,CAAC;gBACjC,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAExH,KAAK,MAAM,cAAc,IAAI,eAAe,CAAE,CAAC;YAC3C,IAAI,SAAS,EAAE,CAAC;gBACZ,cAAc,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACnD,CAAC;YAED,KAAK,MAAM,iBAAiB,IAAI,cAAc,CAAC,kBAAkB,CAAE,CAAC;gBAChE,oBAAoB,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,SAAS,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACrG,CAAC;YAED,IAAI,aAAa,EAAE,CAAC;gBAChB,cAAc,CAAC,OAAO,EAAE,CAAC;YAC7B,CAAC;QACL,CAAC;QAED,OAAO,oBAAoB,CAAC;IAChC,CAAC;IA0BD;;;;;OAKG,CACI,oBAAoB,CAAC,SAAoB,EAAE,MAAW,EAAA;QACzD,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACtD,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC;QACxC,iBAAiB,CAAC,MAAM,GAAG,MAAM,CAAC;QAElC,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACzC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QAC3C,CAAC;QAED,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE,CAAC;YAChC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;QACpD,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YAC/B,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACI,uBAAuB,CAAC,SAAoB,EAAA;QAC/C,IAAK,IAAI,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAE,CAAC;YACxE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC1D,IAAI,iBAAiB,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5C,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACI,SAAS,GAAuE;yBAAtE,iEAA+B,IAAI,aAAE,iEAA6B,IAAI;QACnF,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACrB,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,CAAC;QACD,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACnB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;QACxB,CAAC;QAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACnE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,iBAAiB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAErC,IAAI,QAAQ,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC;gBAC9B,MAAM,MAAM,GAAkB;oBAC1B,KAAK,EAAE,UAAU;oBACjB,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,SAAS,EAAE,QAAQ,CAAC,SAAS;oBAC7B,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,aAAa,EAAE,QAAQ,CAAC,aAAa;iBACxC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YAC9B,CAAC;YAED,IAAI,MAAM,CAAC,KAAK,GAAG,QAAQ,EAAE,CAAC;gBAC1B,MAAM,MAAM,GAAkB;oBAC1B,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;iBACtC,CAAC;gBACF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtB,CAAC;QACL,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;QAEpB,OAAO,IAAI,CAAC;IAChB,CAAC;IAKO,YAAY,CAAC,UAAsB,EAAE,iBAAoC,EAAE,KAAa,EAAA;QAC5F,UAAU,CAAC,eAAe,GAAG,GAAG,EAAE;YAC9B,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;YAElE,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClC,OAAO;YACX,CAAC;YAED,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;YAEvC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC1D,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;gBAC7B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;YACxC,CAAC;QACL,CAAC,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG,CACI,KAAK,GAA+E;mBAA9E,IAAI,6DAAG,KAAK,eAAE,UAAU,uDAAG,CAAC,EAAE,IAAa,iDAAE,EAAW,iDAAE,UAAoB;QACvF,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEpC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACnE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAC/C,iBAAiB,CAAC,MAAM,EACxB;gBAAC,iBAAiB,CAAC,SAAS;aAAC,EAC7B,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EACtC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAChC,IAAI,EACJ,UAAU,EACV,SAAS,EACT,SAAS,EACT,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAC3D,CAAC;YACF,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YACjC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,UAAU,CAAC,cAAc,GAAG,GAAG,EAAE;gBAC7B,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBACjE,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;YAC/C,CAAC,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;QAEpC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,KAAK,GAAA;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,IAAI,CAAC,IAAc,EAAA;QACtB,yCAAyC;QACzC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACnE,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC9B,CAAC;YACD,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,KAAK,GAAA;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,IAAI,GAA2B;iCAA1B,kBAAkB,+CAAG,KAAK;QAClC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QACvC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC/C,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;QACrE,CAAC;QAED,wDAAwD;QACxD,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YACzE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACzD,IAAI,UAAU,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,GAAG,UAAU,CAAC;YAC5D,CAAC,MAAM,IAAI,kBAAkB,EAAE,CAAC;gBAC5B,qHAAqH;gBACrH,kIAAkI;gBAClI,4FAA4F;gBAC5F,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;YACnE,CAAC;QACL,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,QAAQ,CAAC;QAEjD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;;;;OASG,CACI,0BAA0B,CAAC,MAAc,EAAA;QAC5C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;QAC/B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,qBAAqB,CAAC,IAA0B,EAAA;QACnD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,SAAS,CAAC,KAAa,EAAmB;wBAAjB,SAAS,wDAAG,KAAK;QAC7C,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,eAAe,GAAA;YACX;QAAP,kCAAW,CAAC,WAAW,CAAC,CAAC,CAAC,0EAAE,WAAW,KAAI,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;QAE7B,oBAAoB;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;gBACb,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;IAChD,CAAC;IAEO,yBAAyB,CAAC,UAAsB,EAA4B;iCAA1B,kBAAkB,+CAAG,KAAK;QAChF,8CAA8C;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAClD,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC;QAED,uDAAuD;QACvD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC5F,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBACtB,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC7D,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;;;;;OAMG,CACI,KAAK,CAAC,OAAe,EAAE,eAAyC,EAAyB;8BAAvB,eAAe,kDAAG,KAAK;QAC5F,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3B,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QACvB,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACvC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACvC,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;QAC/C,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAClC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAE1B,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,mBAAmB,CAAE,CAAC;YACrD,QAAQ,CAAC,oBAAoB,CACzB,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,EAC/E,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CACrF,CAAC;QACN,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,GAAQ,CAAA,CAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACjC,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEvD,mBAAmB,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC5C,IAAK,IAAI,sBAAsB,GAAG,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,sBAAsB,EAAE,CAAE,CAAC;YACrH,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;YAC1E,mBAAmB,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,GAAG,iBAAiB,CAAC,SAAS,EAAE,CAAC;QACnG,CAAC;QAED,2JAAI,OAAI,2JAAI,OAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,mBAAmB,CAAC,IAAI,0JAAG,OAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QAED,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACjD,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED,UAAU;IACV;;;;;;OAMG,CACI,MAAM,CAAC,KAAK,CAAC,oBAAyB,EAAE,KAAY,EAAE,OAA+B,EAAA;QACxF,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE,oBAAoB,CAAC,MAAM,EAAE,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACzI,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACtE,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACrE,MAAM,SAAS,qKAAG,YAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC/D,MAAM,EAAE,GAAG,iBAAiB,CAAC,QAAQ,CAAC;YACtC,IAAI,iBAAiB,CAAC,SAAS,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;gBACvD,yBAAyB;gBACzB,MAAM,WAAW,GAAG,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;gBACjD,IAAI,WAAW,EAAE,CAAC;oBACd,cAAc,CAAC,oBAAoB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAChE,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBAErE,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;oBACrB,cAAc,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,8JAAI,EAAE,CAAC;mKACP,OAAI,CAAC,SAAS,CAAC,cAAc,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,oBAAoB,CAAC,IAAI,KAAK,IAAI,IAAI,oBAAoB,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;YACzE,cAAc,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,oBAAoB,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YAChD,cAAc,CAAC,WAAW,GAAG,oBAAoB,CAAC,UAAU,CAAC;QACjE,CAAC;QACD,IAAI,oBAAoB,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACnD,cAAc,CAAC,cAAc,GAAG,oBAAoB,CAAC,aAAa,CAAC;QACvE,CAAC;QAED,IAAI,oBAAoB,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YAChD,cAAc,CAAC,WAAW,GAAG,oBAAoB,CAAC,UAAU,CAAC;QACjE,CAAC;QAED,IAAI,oBAAoB,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC5C,cAAc,CAAC,OAAO,GAAG,oBAAoB,CAAC,MAAM,CAAC;QACzD,CAAC;QAED,IAAI,oBAAoB,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YAC/C,cAAc,CAAC,UAAU,GAAG,oBAAoB,CAAC,SAAS,CAAC;QAC/D,CAAC;QAED,IAAI,oBAAoB,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACpD,cAAc,CAAC,eAAe,GAAG,oBAAoB,CAAC,cAAc,CAAC;QACzE,CAAC;QAED,IAAI,oBAAoB,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YACnD,cAAc,CAAC,cAAc,GAAG,oBAAoB,CAAC,aAAa,CAAC;QACvE,CAAC;QAED,IAAI,oBAAoB,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC9C,cAAc,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC;QAC5D,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAqBD,cAAA,EAAgB,CACT,MAAM,CAAC,qBAAqB,CAC/B,oBAAoC,EACpC,uBAAqE,EACrE,KAAc,EAEK;4BADnB,aAAa,oDAAG,KAAK,EACrB,UAAmB;QAEnB,IAAI,OAA2C,CAAC;QAEhD,IAAI,OAAO,uBAAuB,KAAK,QAAQ,EAAE,CAAC;YAC9C,OAAO,GAAG,uBAAuB,CAAC;QACtC,CAAC,MAAM,CAAC;YACJ,OAAO,GAAG;gBACN,cAAc,EAAE,uBAAuB;gBACvC,KAAK,EAAE,KAAK;gBACZ,2BAA2B,EAAE,aAAa;gBAC1C,mBAAmB,EAAE,UAAU;aAClC,CAAC;QACN,CAAC;QAED,IAAI,cAAc,GAAG,oBAAoB,CAAC;QAC1C,IAAI,OAAO,CAAC,2BAA2B,EAAE,CAAC;YACtC,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC,OAAO,CAAC,wBAAwB,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;QACzG,CAAC;QAED,MAAM,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAC7D,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC7D,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACpD,iBAAiB,CAAC,SAAS,qKAAG,YAAS,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACxG,CAAC;QAED,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC;QAEjC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,4GAA4G;YAC5G,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC;YAC5B,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;YAE3B,MAAM,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;YAC7D,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC7D,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBACpD,MAAM,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC;gBAC9C,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;gBAEjC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;oBACvB,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBACzB,CAAC;gBAED,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;oBACnC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;gBACrC,CAAC;YACL,CAAC;YAED,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC;YAC5B,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC;QAC5B,CAAC;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;;;;;OAQG,CACI,MAAM,CAAC,QAAQ,CAAC,oBAAoC,EAAE,OAAe,EAAE,KAAa,EAAE,IAAa,EAAE,mBAA6B,EAAA;QACrI,MAAM,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAErF,OAAO,cAAc,CAAC,eAAe,CAAC,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,mBAAmB,CAAC,CAAC;IAC/F,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,eAAe,CAAC,cAA8B,EAAE,OAAe,EAAE,KAAa,EAAE,mBAA6B,EAAA;QACvH,OAAO,cAAc,CAAC,WAAW,CAAC,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;IAClG,CAAC;IAED;;;;;;;;OAQG,CACI,MAAM,CAAC,UAAU,CAAC,oBAAoC,EAAE,SAAiB,EAAE,OAAe,EAAE,IAAa,EAAE,mBAA6B,EAAA;QAC3I,MAAM,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAErF,OAAO,cAAc,CAAC,iBAAiB,CAAC,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,CAAC,CAAC;IACrG,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,iBAAiB,CAAC,cAA8B,EAAE,SAAiB,EAAE,OAAe,EAAE,mBAA6B,EAAA;QAC7H,OAAO,cAAc,CAAC,WAAW,CAAC,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;IACrG,CAAC;IAED;;;;;;;;OAQG,CACI,MAAM,CAAC,WAAW,CAAC,cAA8B,EAAE,KAAa,EAAE,GAAW,EAAE,mBAA6B,EAAkB;uBAAhB,QAAQ,yDAAG,KAAK;QACjI,IAAI,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC;QAC5B,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QAE3B,MAAM,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAC7D,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;YAC7D,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACpD,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAE1G,IAAI,QAAQ,EAAE,CAAC;gBACX,wEAAwE;gBACxE,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBACnC,SAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC;YAED,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YACjC,MAAM,OAAO,GAAoB,EAAE,CAAC;YAEpC,IAAI,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;YAClC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;gBACnC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAI,AAAC,CAAC,QAAQ,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,GAAK,CAAD,OAAS,IAAI,GAAG,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,CAAE,CAAC;oBAChG,MAAM,MAAM,GAAkB;wBAC1B,KAAK,EAAE,GAAG,CAAC,KAAK;wBAChB,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK;wBACtD,SAAS,EAAE,GAAG,CAAC,SAAS;wBACxB,UAAU,EAAE,GAAG,CAAC,UAAU;wBAC1B,aAAa,EAAE,GAAG,CAAC,aAAa;wBAChC,aAAa,EAAE,GAAG,CAAC,aAAa;qBACnC,CAAC;oBACF,IAAI,UAAU,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC;wBAClC,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC;oBAC9B,CAAC;oBACD,MAAM,CAAC,KAAK,IAAI,UAAU,CAAC;oBAC3B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzB,CAAC;YACL,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACpC,KAAK,EAAE,CAAC;gBACR,SAAS;YACb,CAAC;YAED,IAAI,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;gBAC1B,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAC5B,CAAC;YAED,IAAI,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;gBACzC,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;YAC3C,CAAC;YAED,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACjC,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC,wCAAwC;QACrF,CAAC;QAED,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC;QAC5B,cAAc,CAAC,GAAG,GAAG,EAAE,CAAC;QAExB,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;;OAIG,CACI,QAAQ,CAAC,WAAqB,EAAA;QACjC,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,WAAW,EAAE,CAAC;YACd,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;YAC/B,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;YAC3B,GAAG,IAAI,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;YACzC,GAAG,IAAI,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC;YAC3C,GAAG,IAAI,+BAA+B,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YACzE,GAAG,IAAI,wBAAwB,GAAG,IAAI,CAAC,YAAY,CAAC;QACxD,CAAC;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IA/xBD;;;;;;;;OAQG,CACH,YACI,oCAAA,EAAsC,CAC/B,IAAY,EACnB,QAAyB,IAAI,EAC7B,MAAM,GAAG,CAAC,CAAC,EACX,SAAS,GAAG,CAAC,CAAA;QAHN,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QApbf,IAAA,CAAA,mBAAmB,GAAG,IAAI,KAAK,EAAqB,CAAC;QACrD,IAAA,CAAA,YAAY,GAAG,IAAI,KAAK,EAAc,CAAC;QACvC,IAAA,CAAA,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC;QACzB,IAAA,CAAA,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QAGxB,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QAChB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QACpB,IAAA,CAAA,OAAO,GAAG,CAAC,CAAC,CAAC;QACb,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QACf,IAAA,CAAA,eAAe,GAAsB,IAAI,CAAC;QAC1C,IAAA,CAAA,cAAc,GAAqB,IAAI,CAAC;QACxC,IAAA,CAAA,qBAAqB,GAAG,CAAC,CAAC;QAC1B,IAAA,CAAA,YAAY,GAAG,IAAI,CAAC;QAE5B,cAAA,EAAgB,CACT,IAAA,CAAA,gBAAgB,GAA8B,IAAI,CAAC;QAO1D;;WAEG,CACI,IAAA,CAAA,wBAAwB,GAAG,iKAAI,aAAU,EAAqB,CAAC;QAEtE;;WAEG,CACI,IAAA,CAAA,yBAAyB,GAAG,iKAAI,aAAU,EAAqB,CAAC;QAEvE;;WAEG,CACI,IAAA,CAAA,8BAA8B,GAAG,iKAAI,aAAU,EAAkB,CAAC;QAEzE;;WAEG,CACI,IAAA,CAAA,6BAA6B,GAAG,iKAAI,aAAU,EAAkB,CAAC;QAExE;;WAEG,CACI,IAAA,CAAA,+BAA+B,GAAG,iKAAI,aAAU,EAAkB,CAAC;QAE1E;;WAEG,CACI,IAAA,CAAA,8BAA8B,GAAG,IAAI,0KAAU,EAAkB,CAAC;QAEzE;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAQ,IAAI,CAAC;QAEpB,IAAA,CAAA,KAAK,GAAiC,IAAI,CAAC;QAwe3C,IAAA,CAAA,mBAAmB,GAAc,EAAE,CAAC;QA1GxC,IAAI,CAAC,MAAM,GAAG,KAAK,qKAAI,cAAW,CAAC,gBAAiB,CAAC;QACrD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE1C,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;CA0wBJ", "debugId": null}}, {"offset": {"line": 4025, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animationKey.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animationKey.ts"], "sourcesContent": ["import type { IEasingFunction } from \"./easing\";\r\n\r\n/**\r\n * Defines an interface which represents an animation key frame\r\n */\r\nexport interface IAnimationKey {\r\n    /**\r\n     * Frame of the key frame\r\n     */\r\n    frame: number;\r\n    /**\r\n     * Value at the specifies key frame\r\n     */\r\n    value: any;\r\n    /**\r\n     * The input tangent for the cubic hermite spline\r\n     */\r\n    inTangent?: any;\r\n    /**\r\n     * The output tangent for the cubic hermite spline\r\n     */\r\n    outTangent?: any;\r\n    /**\r\n     * The animation interpolation type\r\n     */\r\n    interpolation?: AnimationKeyInterpolation;\r\n    /**\r\n     * Property defined by UI tools to link (or not ) the tangents\r\n     */\r\n    lockedTangent?: boolean;\r\n    /**\r\n     * The easing function associated with the key frame (optional). If not defined, the easing function defined at the animation level (if any) will be used instead\r\n     */\r\n    easingFunction?: IEasingFunction;\r\n}\r\n\r\n/**\r\n * Enum for the animation key frame interpolation type\r\n */\r\nexport const enum AnimationKeyInterpolation {\r\n    /**\r\n     * Use tangents to interpolate between start and end values.\r\n     */\r\n    NONE = 0,\r\n    /**\r\n     * Do not interpolate between keys and use the start key value only. Tangents are ignored\r\n     */\r\n    STEP = 1,\r\n}\r\n"], "names": [], "mappings": "AAoCA;;GAEG;;;AACH,IAAkB,yBASjB;AATD,CAAA,SAAkB,yBAAyB;IACvC;;OAEG,CACH,yBAAA,CAAA,yBAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;IACR;;OAEG,CACH,yBAAA,CAAA,yBAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;AACZ,CAAC,EATiB,yBAAyB,IAAA,CAAzB,yBAAyB,GAAA,CAAA,CAAA,GAS1C", "debugId": null}}, {"offset": {"line": 4043, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animatable.interface.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animatable.interface.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\n\r\nimport type { Animation } from \"./animation\";\r\n\r\n/**\r\n * Interface containing an array of animations\r\n */\r\nexport interface IAnimatable {\r\n    /**\r\n     * Array of animations\r\n     */\r\n    animations: Nullable<Array<Animation>>;\r\n}\r\n"], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4050, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/pathCursor.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/pathCursor.ts"], "sourcesContent": ["import { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Path2 } from \"../Maths/math.path\";\r\n\r\n/**\r\n * A cursor which tracks a point on a path\r\n */\r\nexport class PathCursor {\r\n    /**\r\n     * Stores path cursor callbacks for when an onchange event is triggered\r\n     */\r\n    private _onchange = new Array<(cursor: PathCursor) => void>();\r\n\r\n    /**\r\n     * The value of the path cursor\r\n     */\r\n    value: number = 0;\r\n\r\n    /**\r\n     * The animation array of the path cursor\r\n     */\r\n    animations = [] as Animation[];\r\n\r\n    /**\r\n     * Initializes the path cursor\r\n     * @param _path The path to track\r\n     */\r\n    constructor(private _path: Path2) {}\r\n\r\n    /**\r\n     * Gets the cursor point on the path\r\n     * @returns A point on the path cursor at the cursor location\r\n     */\r\n    public getPoint(): Vector3 {\r\n        const point = this._path.getPointAtLengthPosition(this.value);\r\n        return new Vector3(point.x, 0, point.y);\r\n    }\r\n\r\n    /**\r\n     * Moves the cursor ahead by the step amount\r\n     * @param step The amount to move the cursor forward\r\n     * @returns This path cursor\r\n     */\r\n    public moveAhead(step: number = 0.002): PathCursor {\r\n        this.move(step);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Moves the cursor behind by the step amount\r\n     * @param step The amount to move the cursor back\r\n     * @returns This path cursor\r\n     */\r\n    public moveBack(step: number = 0.002): PathCursor {\r\n        this.move(-step);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Moves the cursor by the step amount\r\n     * If the step amount is greater than one, an exception is thrown\r\n     * @param step The amount to move the cursor\r\n     * @returns This path cursor\r\n     */\r\n    public move(step: number): PathCursor {\r\n        if (Math.abs(step) > 1) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"step size should be less than 1.\";\r\n        }\r\n\r\n        this.value += step;\r\n        this._ensureLimits();\r\n        this._raiseOnChange();\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Ensures that the value is limited between zero and one\r\n     * @returns This path cursor\r\n     */\r\n    private _ensureLimits(): PathCursor {\r\n        while (this.value > 1) {\r\n            this.value -= 1;\r\n        }\r\n        while (this.value < 0) {\r\n            this.value += 1;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Runs onchange callbacks on change (used by the animation engine)\r\n     * @returns This path cursor\r\n     */\r\n    private _raiseOnChange(): PathCursor {\r\n        for (const f of this._onchange) {\r\n            f(this);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Executes a function on change\r\n     * @param f A path cursor onchange callback\r\n     * @returns This path cursor\r\n     */\r\n    public onchange(f: (cursor: PathCursor) => void): PathCursor {\r\n        this._onchange.push(f);\r\n\r\n        return this;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;;AAMzC,MAAO,UAAU;IAsBnB;;;OAGG,CACI,QAAQ,GAAA;QACX,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,OAAO,sKAAI,UAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG,CACI,SAAS,GAAqB;mBAApB,iEAAe,KAAK;QACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,QAAQ,GAAqB;YAApB,wEAAe,KAAK;QAChC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QAEjB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,IAAI,CAAC,IAAY,EAAA;QACpB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACrB,4CAA4C;YAC5C,MAAM,kCAAkC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;QACnB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACK,aAAa,GAAA;QACjB,MAAO,IAAI,CAAC,KAAK,GAAG,CAAC,CAAE,CAAC;YACpB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,MAAO,IAAI,CAAC,KAAK,GAAG,CAAC,CAAE,CAAC;YACpB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;QACpB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACK,cAAc,GAAA;QAClB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;YAC7B,CAAC,CAAC,IAAI,CAAC,CAAC;QACZ,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG,CACI,QAAQ,CAAC,CAA+B,EAAA;QAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEvB,OAAO,IAAI,CAAC;IAChB,CAAC;IA5FD;;;OAGG,CACH,YAAoB,KAAY,CAAA;QAAZ,IAAA,CAAA,KAAK,GAAL,KAAK,CAAO;QAnBhC;;WAEG,CACK,IAAA,CAAA,SAAS,GAAG,IAAI,KAAK,EAAgC,CAAC;QAE9D;;WAEG,CACH,IAAA,CAAA,KAAK,GAAW,CAAC,CAAC;QAElB;;WAEG,CACH,IAAA,CAAA,UAAU,GAAG,EAAiB,CAAC;IAMI,CAAC;CAyFvC", "debugId": null}}, {"offset": {"line": 4145, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animationGroupMask.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animationGroupMask.ts"], "sourcesContent": ["/**\r\n * Enum used to define the mode for an animation group mask\r\n */\r\nexport const enum AnimationGroupMaskMode {\r\n    /**\r\n     * The mask defines the animatable target names that should be included\r\n     */\r\n    Include = 0,\r\n    /**\r\n     * The mask defines the animatable target names in a \"exclude\" mode: all animatable targets will be animated except the ones defined in the mask\r\n     */\r\n    Exclude = 1,\r\n}\r\n\r\n/**\r\n * Defines a mask used to filter animation targets.\r\n * If you apply a mask to an animation group (see the AnimationGroup.mask property), only the animations whose target names match the mask will play.\r\n * Note that a target is defined by its name (string). This means that the same mask can be used for several animation groups, provided that their targets are named in the same way.\r\n */\r\nexport class AnimationGroupMask {\r\n    /**\r\n     * The set of target names included in the mask. If mode is AnimationGroupMaskMode.Exclude, the targets in this set will be excluded from the mask instead.\r\n     */\r\n    private _targetNames: Set<string>;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the mask is disabled (default is false)\r\n     */\r\n    public disabled = false;\r\n\r\n    /**\r\n     * Creates a new mask\r\n     * @param names The list of target names to add to the mask (optional)\r\n     * @param mode Defines the mode for the mask (default: AnimationGroupMaskMode.Include)\r\n     */\r\n    constructor(\r\n        names?: string[],\r\n        /**\r\n         * [0] Defines the mode for the mask\r\n         */\r\n        public mode: AnimationGroupMaskMode = AnimationGroupMaskMode.Include\r\n    ) {\r\n        this._targetNames = new Set<string>();\r\n        if (names) {\r\n            this.addTargetName(names);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds one or several target names to the mask\r\n     * @param name The name(s) to add to the mask\r\n     */\r\n    public addTargetName(name: string | string[]): void {\r\n        if (Array.isArray(name)) {\r\n            for (const n of name) {\r\n                this._targetNames.add(n);\r\n            }\r\n            return;\r\n        }\r\n\r\n        this._targetNames.add(name);\r\n    }\r\n\r\n    /**\r\n     * Removes one or several target names from the mask\r\n     * @param name The name(s) to remove from the mask\r\n     */\r\n    public removeTargetName(name: string | string[]): void {\r\n        if (Array.isArray(name)) {\r\n            for (const n of name) {\r\n                this._targetNames.delete(n);\r\n            }\r\n            return;\r\n        }\r\n\r\n        this._targetNames.delete(name);\r\n    }\r\n\r\n    /**\r\n     * Checks if the mask includes a target name.\r\n     * This method is intended to know if a given target name is included in the mask, not if the name is actually retained by the mask (see retainsTarget() instead).\r\n     * @param name The name to check with the mask\r\n     * @returns True if the mask includes the name, false otherwise\r\n     */\r\n    public hasTarget(name: string): boolean {\r\n        return this._targetNames.has(name);\r\n    }\r\n\r\n    /**\r\n     * Checks if the mask retains a target name.\r\n     * Note that in the \"Exclude\" mode, this will return false if the mask includes the name, and true otherwise!\r\n     * This method is intended to know if a given target name is retained by the mask, not if the name is in the list of target names.\r\n     * @param name The name to check with the mask\r\n     * @returns True if the mask retains the name, false otherwise\r\n     */\r\n    public retainsTarget(name: string): boolean {\r\n        return this._targetNames.has(name) === (this.mode === AnimationGroupMaskMode.Include);\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;GAEG;;;;AACH,IAAkB,sBASjB;AATD,CAAA,SAAkB,sBAAsB;IACpC;;OAEG,CACH,sBAAA,CAAA,sBAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAW,CAAA;IACX;;OAEG,CACH,sBAAA,CAAA,sBAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAW,CAAA;AACf,CAAC,EATiB,sBAAsB,IAAA,CAAtB,sBAAsB,GAAA,CAAA,CAAA,GASvC;AAOK,MAAO,kBAAkB;IA6B3B;;;OAGG,CACI,aAAa,CAAC,IAAuB,EAAA;QACxC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAE,CAAC;gBACnB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC;YACD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG,CACI,gBAAgB,CAAC,IAAuB,EAAA;QAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,KAAK,MAAM,CAAC,IAAI,IAAI,CAAE,CAAC;gBACnB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;YACD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED;;;;;OAKG,CACI,SAAS,CAAC,IAAY,EAAA;QACzB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;OAMG,CACI,aAAa,CAAC,IAAY,EAAA;QAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAA,EAAA,kCAAA,GAAmC,CAAC,CAAC;IAC1F,CAAC;IAnED;;;;OAIG,CACH,YACI,KAAgB,EAChB;;OAEG,CACI,OAAA,EAAA,kCAAA,EAA6D,CAA7D,CAA6D;QAA7D,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAyD;QAfxE;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAG,KAAK,CAAC;QAcpB,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QACtC,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;IACL,CAAC;CAoDJ", "debugId": null}}, {"offset": {"line": 4223, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/animation.optimizations.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/animation.optimizations.ts"], "sourcesContent": ["import type { Scene } from \"core/scene\";\r\n\r\n/**\r\n * Interface used to define the optimization options for animations\r\n */\r\nexport type AnimationOptimization =\r\n    | {\r\n          /**\r\n           * Do not merge runtime animations\r\n           * @defaultValue true\r\n           */\r\n          mergeRuntimeAnimations: false;\r\n      }\r\n    | {\r\n          /**\r\n           * All runtime animations will be merged into the first animatable\r\n           * @defaultValue true\r\n           */\r\n          mergeRuntimeAnimations: true;\r\n          /**\r\n           * If true, all keyframes evaluation will be merged from the first runtime animation\r\n           * You need to turn on `mergeRuntimeAnimations` for this to work\r\n           * @defaultValue false\r\n           */\r\n          mergeKeyFrames: boolean;\r\n      };\r\n\r\n/**\r\n * This is a destructive optimization that merges all animatables into the first one.\r\n * That animatable will also host all the runtime animations.\r\n * We expect that all the animatables are on the same timeframe (same start, end, loop, etc..)\r\n * @param scene defines the scene to optimize\r\n * @param options defines the optimization options\r\n */\r\nexport function OptimizeAnimations(scene: Scene, options: Partial<AnimationOptimization> = {}) {\r\n    const mergeRuntimeAnimations = options.mergeRuntimeAnimations ?? true;\r\n    const mergeKeyFrames = options.mergeRuntimeAnimations === true ? (options.mergeKeyFrames ?? false) : false;\r\n\r\n    // We will go through all the current animatables and merge them\r\n    const animatables = scene.animatables;\r\n\r\n    if (animatables.length === 0) {\r\n        return;\r\n    }\r\n\r\n    const mainAnimatable = animatables[0];\r\n\r\n    for (let i = 1; i < animatables.length; i++) {\r\n        const animatable = animatables[i];\r\n\r\n        // Merge the current animatable with the main one\r\n        mainAnimatable._runtimeAnimations.push(...animatable._runtimeAnimations);\r\n    }\r\n\r\n    if (mergeRuntimeAnimations && mainAnimatable._runtimeAnimations.length > 1) {\r\n        // Make sure only one runtime animation is driving the beat\r\n        const mainRuntimeAnimation = mainAnimatable._runtimeAnimations[0];\r\n        for (let i = 1; i < mainAnimatable._runtimeAnimations.length; i++) {\r\n            const runtimeAnimation = mainAnimatable._runtimeAnimations[i];\r\n            runtimeAnimation._coreRuntimeAnimation = mainRuntimeAnimation;\r\n        }\r\n    }\r\n\r\n    if (mergeKeyFrames && mainAnimatable._runtimeAnimations.length > 1) {\r\n        // Merge the keyframes from all the runtime animations into the first one\r\n        const mainAnimation = mainAnimatable._runtimeAnimations[0]._animation;\r\n        for (let i = 1; i < mainAnimatable._runtimeAnimations.length; i++) {\r\n            const runtimeAnimation = mainAnimatable._runtimeAnimations[i];\r\n            const animation = runtimeAnimation._animation;\r\n            animation._coreAnimation = mainAnimation;\r\n        }\r\n    }\r\n\r\n    scene._activeAnimatables = [mainAnimatable];\r\n}\r\n"], "names": [], "mappings": "AA2BA;;;;;;GAMG;;;AACG,SAAU,kBAAkB,CAAC,KAAY;kBAAE,iEAA0C,CAAA,CAAE;;IACzF,MAAM,sBAAsB,8CAAW,sBAAsB,2DAA9B,OAAO,2BAA2B,IAAI,CAAC;QACJ,OAAO;IAAzE,MAAM,cAAc,GAAG,OAAO,CAAC,sBAAsB,KAAK,IAAI,CAAC,CAAC,CAAC,mCAAS,cAAc,6EAAI,KAAK,CAAC,CAAC,CAAC,AAAC,KAAK,CAAC;IAE3G,gEAAgE;IAChE,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;IAEtC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC3B,OAAO;IACX,CAAC;IAED,MAAM,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IAEtC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;QAC1C,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAElC,iDAAiD;QACjD,cAAc,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,kBAAkB,CAAC,CAAC;IAC7E,CAAC;IAED,IAAI,sBAAsB,IAAI,cAAc,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzE,2DAA2D;QAC3D,MAAM,oBAAoB,GAAG,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAClE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAChE,MAAM,gBAAgB,GAAG,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC9D,gBAAgB,CAAC,qBAAqB,GAAG,oBAAoB,CAAC;QAClE,CAAC;IACL,CAAC;IAED,IAAI,cAAc,IAAI,cAAc,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjE,yEAAyE;QACzE,MAAM,aAAa,GAAG,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QACtE,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAChE,MAAM,gBAAgB,GAAG,cAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,SAAS,GAAG,gBAAgB,CAAC,UAAU,CAAC;YAC9C,SAAS,CAAC,cAAc,GAAG,aAAa,CAAC;QAC7C,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB,GAAG;QAAC,cAAc;KAAC,CAAC;AAChD,CAAC", "debugId": null}}, {"offset": {"line": 4274, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Animations/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Animations/index.ts"], "sourcesContent": ["export * from \"./animatable\";\r\nexport * from \"./animation\";\r\nexport * from \"./animationPropertiesOverride\";\r\nexport * from \"./easing\";\r\nexport * from \"./runtimeAnimation\";\r\nexport * from \"./animationEvent\";\r\nexport * from \"./animationGroup\";\r\nexport * from \"./animationKey\";\r\nexport * from \"./animationRange\";\r\nexport * from \"./animatable.interface\";\r\nexport * from \"./pathCursor\";\r\nexport * from \"./animationGroupMask\";\r\nexport * from \"./animation.optimizations\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,cAAc,CAAC;AAC7B,cAAc,aAAa,CAAC;AAC5B,cAAc,+BAA+B,CAAC;AAC9C,cAAc,UAAU,CAAC;AACzB,cAAc,oBAAoB,CAAC;AACnC,cAAc,kBAAkB,CAAC;AACjC,cAAc,kBAAkB,CAAC;AACjC,cAAc,gBAAgB,CAAC;AAC/B,cAAc,kBAAkB,CAAC;AACjC,cAAc,wBAAwB,CAAC;AACvC,cAAc,cAAc,CAAC;AAC7B,cAAc,sBAAsB,CAAC;AACrC,cAAc,2BAA2B,CAAC", "debugId": null}}]}