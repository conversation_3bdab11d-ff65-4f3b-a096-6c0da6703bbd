"use strict";exports.id=288,exports.ids=[288],exports.modules={15158:(a,b,c)=>{c.d(b,{oR:()=>q,tQ:()=>o,Wt:()=>n,ZU:()=>p});var d=c(29363),e=c(75953),f=c(44173),g=c(55540);let h=[{regex:RegExp("^/nodes/\\d+/extensions/")}];class i{constructor(a,b){this._gltf=a,this._infoTree=b}convert(a){let b,c=this._gltf,d=this._infoTree;if(!a.startsWith("/"))throw Error("Path must start with a /");let e=a.split("/");if(e.shift(),e[e.length-1].includes(".length")){let a=e[e.length-1].split(".");e.pop(),e.push(...a)}let f=!1;for(let g of e){let e="length"===g;if(e&&!d.__array__)throw Error(`Path ${a} is invalid`);if(d.__ignoreObjectTree__&&(f=!0),d.__array__&&!e)d=d.__array__;else if(!(d=d[g]))throw Error(`Path ${a} is invalid`);if(!f)if(void 0===c){if(!h.find(b=>b.regex.test(a)))throw Error(`Path ${a} is invalid`)}else e||(c=c?.[g]);(d.__target__||e)&&(b=c)}return{object:b,info:d}}}function j(a,b,c,d){let e=k(a,b);return d?e[c][d]:e[c]}function k(a,b,c){return a._data?.[c?.fillMode??e.Y.MATERIAL_TriangleFillMode]?.babylonMaterial}function l(a,b){return{offset:{componentsCount:2,type:"Vector2",get:(c,e,f)=>{let g=j(c,f,a,b);return new d.I9(g?.uOffset,g?.vOffset)},getTarget:k,set:(c,d,e,f)=>{let g=j(d,f,a,b);g.uOffset=c.x,g.vOffset=c.y},getPropertyName:[()=>`${a}${b?"."+b:""}.uOffset`,()=>`${a}${b?"."+b:""}.vOffset`]},rotation:{type:"number",get:(c,d,e)=>j(c,e,a,b)?.wAng,getTarget:k,set:(c,d,e,f)=>j(d,f,a,b).wAng=c,getPropertyName:[()=>`${a}${b?"."+b:""}.wAng`]},scale:{componentsCount:2,type:"Vector2",get:(c,e,f)=>{let g=j(c,f,a,b);return new d.I9(g?.uScale,g?.vScale)},getTarget:k,set:(c,d,e,f)=>{let g=j(d,f,a,b);g.uScale=c.x,g.vScale=c.y},getPropertyName:[()=>`${a}${b?"."+b:""}.uScale`,()=>`${a}${b?"."+b:""}.vScale`]}}}let m={cameras:{__array__:{__target__:!0,orthographic:{xmag:{componentsCount:2,type:"Vector2",get:a=>new d.I9(a._babylonCamera?.orthoLeft??0,a._babylonCamera?.orthoRight??0),set:(a,b)=>{b._babylonCamera&&(b._babylonCamera.orthoLeft=a.x,b._babylonCamera.orthoRight=a.y)},getTarget:a=>a,getPropertyName:[()=>"orthoLeft",()=>"orthoRight"]},ymag:{componentsCount:2,type:"Vector2",get:a=>new d.I9(a._babylonCamera?.orthoBottom??0,a._babylonCamera?.orthoTop??0),set:(a,b)=>{b._babylonCamera&&(b._babylonCamera.orthoBottom=a.x,b._babylonCamera.orthoTop=a.y)},getTarget:a=>a,getPropertyName:[()=>"orthoBottom",()=>"orthoTop"]},zfar:{type:"number",get:a=>a._babylonCamera?.maxZ,set:(a,b)=>{b._babylonCamera&&(b._babylonCamera.maxZ=a)},getTarget:a=>a,getPropertyName:[()=>"maxZ"]},znear:{type:"number",get:a=>a._babylonCamera?.minZ,set:(a,b)=>{b._babylonCamera&&(b._babylonCamera.minZ=a)},getTarget:a=>a,getPropertyName:[()=>"minZ"]}},perspective:{aspectRatio:{type:"number",get:a=>a._babylonCamera?.getEngine().getAspectRatio(a._babylonCamera),getTarget:a=>a,getPropertyName:[()=>"aspectRatio"],isReadOnly:!0},yfov:{type:"number",get:a=>a._babylonCamera?.fov,set:(a,b)=>{b._babylonCamera&&(b._babylonCamera.fov=a)},getTarget:a=>a,getPropertyName:[()=>"fov"]},zfar:{type:"number",get:a=>a._babylonCamera?.maxZ,set:(a,b)=>{b._babylonCamera&&(b._babylonCamera.maxZ=a)},getTarget:a=>a,getPropertyName:[()=>"maxZ"]},znear:{type:"number",get:a=>a._babylonCamera?.minZ,set:(a,b)=>{b._babylonCamera&&(b._babylonCamera.minZ=a)},getTarget:a=>a,getPropertyName:[()=>"minZ"]}}}},nodes:{length:{type:"number",get:a=>a.length,getTarget:a=>a.map(a=>a._babylonTransformNode),getPropertyName:[()=>"length"]},__array__:{__target__:!0,translation:{type:"Vector3",get:a=>a._babylonTransformNode?.position,set:(a,b)=>b._babylonTransformNode?.position.copyFrom(a),getTarget:a=>a._babylonTransformNode,getPropertyName:[()=>"position"]},rotation:{type:"Quaternion",get:a=>a._babylonTransformNode?.rotationQuaternion,set:(a,b)=>b._babylonTransformNode?.rotationQuaternion?.copyFrom(a),getTarget:a=>a._babylonTransformNode,getPropertyName:[()=>"rotationQuaternion"]},scale:{type:"Vector3",get:a=>a._babylonTransformNode?.scaling,set:(a,b)=>b._babylonTransformNode?.scaling.copyFrom(a),getTarget:a=>a._babylonTransformNode,getPropertyName:[()=>"scaling"]},weights:{length:{type:"number",get:a=>a._numMorphTargets,getTarget:a=>a._babylonTransformNode,getPropertyName:[()=>"influence"]},__array__:{__target__:!0,type:"number",get:(a,b)=>void 0!==b?a._primitiveBabylonMeshes?.[0].morphTargetManager?.getTarget(b).influence:void 0,getTarget:a=>a._babylonTransformNode,getPropertyName:[()=>"influence"]},type:"number[]",get:(a,b)=>[0],getTarget:a=>a._babylonTransformNode,getPropertyName:[()=>"influence"]},matrix:{type:"Matrix",get:a=>d.uq.Compose(a._babylonTransformNode?.scaling,a._babylonTransformNode?.rotationQuaternion,a._babylonTransformNode?.position),getTarget:a=>a._babylonTransformNode,isReadOnly:!0},globalMatrix:{type:"Matrix",get:a=>{let b=d.uq.Identity(),c=a.parent;for(;c&&c.parent;)c=c.parent;let e=a._babylonTransformNode?.position._isDirty||a._babylonTransformNode?.rotationQuaternion?._isDirty||a._babylonTransformNode?.scaling._isDirty;if(c){let d=c._babylonTransformNode?.computeWorldMatrix(!0).invert();d&&a._babylonTransformNode?.computeWorldMatrix(e)?.multiplyToRef(d,b)}else a._babylonTransformNode&&b.copyFrom(a._babylonTransformNode.computeWorldMatrix(e));return b},getTarget:a=>a._babylonTransformNode,isReadOnly:!0},extensions:{EXT_lights_ies:{multiplier:{type:"number",get:a=>a._babylonTransformNode?.getChildren(a=>a instanceof g.n,!0)[0]?.intensity,getTarget:a=>a._babylonTransformNode?.getChildren(a=>a instanceof g.n,!0)[0],set:(a,b)=>{if(b._babylonTransformNode){let c=b._babylonTransformNode.getChildren(a=>a instanceof g.n,!0)[0];c&&(c.intensity=a)}}},color:{type:"Color3",get:a=>a._babylonTransformNode?.getChildren(a=>a instanceof g.n,!0)[0]?.diffuse,getTarget:a=>a._babylonTransformNode?.getChildren(a=>a instanceof g.n,!0)[0],set:(a,b)=>{if(b._babylonTransformNode){let c=b._babylonTransformNode.getChildren(a=>a instanceof g.n,!0)[0];c&&(c.diffuse=a)}}}}}}},materials:{__array__:{__target__:!0,emissiveFactor:{type:"Color3",get:(a,b,c)=>k(a,b,c).emissiveColor,set:(a,b,c,d)=>k(b,c,d).emissiveColor.copyFrom(a),getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"emissiveColor"]},emissiveTexture:{extensions:{KHR_texture_transform:l("emissiveTexture")}},normalTexture:{scale:{type:"number",get:(a,b,c)=>j(a,c,"bumpTexture")?.level,set:(a,b,c,d)=>{let e=j(b,d,"bumpTexture");e&&(e.level=a)},getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"level"]},extensions:{KHR_texture_transform:l("bumpTexture")}},occlusionTexture:{strength:{type:"number",get:(a,b,c)=>k(a,b,c).ambientTextureStrength,set:(a,b,c,d)=>{let e=k(b,c,d);e&&(e.ambientTextureStrength=a)},getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"ambientTextureStrength"]},extensions:{KHR_texture_transform:l("ambientTexture")}},pbrMetallicRoughness:{baseColorFactor:{type:"Color4",get:(a,b,c)=>{let d=k(a,b,c);return f.ov.FromColor3(d.albedoColor,d.alpha)},set:(a,b,c,d)=>{let e=k(b,c,d);e.albedoColor.set(a.r,a.g,a.b),e.alpha=a.a},getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"albedoColor",()=>"alpha"]},baseColorTexture:{extensions:{KHR_texture_transform:l("albedoTexture")}},metallicFactor:{type:"number",get:(a,b,c)=>k(a,b,c).metallic,set:(a,b,c,d)=>{let e=k(b,c,d);e&&(e.metallic=a)},getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"metallic"]},roughnessFactor:{type:"number",get:(a,b,c)=>k(a,b,c).roughness,set:(a,b,c,d)=>{let e=k(b,c,d);e&&(e.roughness=a)},getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"roughness"]},metallicRoughnessTexture:{extensions:{KHR_texture_transform:l("metallicTexture")}}},extensions:{KHR_materials_anisotropy:{anisotropyStrength:{type:"number",get:(a,b,c)=>k(a,b,c).anisotropy.intensity,set:(a,b,c,d)=>{k(b,c,d).anisotropy.intensity=a},getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"anisotropy.intensity"]},anisotropyRotation:{type:"number",get:(a,b,c)=>k(a,b,c).anisotropy.angle,set:(a,b,c,d)=>{k(b,c,d).anisotropy.angle=a},getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"anisotropy.angle"]},anisotropyTexture:{extensions:{KHR_texture_transform:l("anisotropy","texture")}}},KHR_materials_clearcoat:{clearcoatFactor:{type:"number",get:(a,b,c)=>k(a,b,c).clearCoat.intensity,set:(a,b,c,d)=>{k(b,c,d).clearCoat.intensity=a},getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"clearCoat.intensity"]},clearcoatRoughnessFactor:{type:"number",get:(a,b,c)=>k(a,b,c).clearCoat.roughness,set:(a,b,c,d)=>{k(b,c,d).clearCoat.roughness=a},getTarget:(a,b,c)=>k(a,b,c),getPropertyName:[()=>"clearCoat.roughness"]},clearcoatTexture:{extensions:{KHR_texture_transform:l("clearCoat","texture")}},clearcoatNormalTexture:{scale:{type:"number",get:(a,b,c)=>k(a,b,c).clearCoat.bumpTexture?.level,getTarget:k,set:(a,b,c,d)=>k(b,c,d).clearCoat.bumpTexture.level=a},extensions:{KHR_texture_transform:l("clearCoat","bumpTexture")}},clearcoatRoughnessTexture:{extensions:{KHR_texture_transform:l("clearCoat","textureRoughness")}}},KHR_materials_dispersion:{dispersion:{type:"number",get:(a,b,c)=>k(a,b,c).subSurface.dispersion,getTarget:k,set:(a,b,c,d)=>k(b,c,d).subSurface.dispersion=a}},KHR_materials_emissive_strength:{emissiveStrength:{type:"number",get:(a,b,c)=>k(a,b,c).emissiveIntensity,getTarget:k,set:(a,b,c,d)=>k(b,c,d).emissiveIntensity=a}},KHR_materials_ior:{ior:{type:"number",get:(a,b,c)=>k(a,b,c).indexOfRefraction,getTarget:k,set:(a,b,c,d)=>k(b,c,d).indexOfRefraction=a}},KHR_materials_iridescence:{iridescenceFactor:{type:"number",get:(a,b,c)=>k(a,b,c).iridescence.intensity,getTarget:k,set:(a,b,c,d)=>k(b,c,d).iridescence.intensity=a},iridescenceIor:{type:"number",get:(a,b,c)=>k(a,b,c).iridescence.indexOfRefraction,getTarget:k,set:(a,b,c,d)=>k(b,c,d).iridescence.indexOfRefraction=a},iridescenceTexture:{extensions:{KHR_texture_transform:l("iridescence","texture")}},iridescenceThicknessMaximum:{type:"number",get:(a,b,c)=>k(a,b,c).iridescence.maximumThickness,getTarget:k,set:(a,b,c,d)=>k(b,c,d).iridescence.maximumThickness=a},iridescenceThicknessMinimum:{type:"number",get:(a,b,c)=>k(a,b,c).iridescence.minimumThickness,getTarget:k,set:(a,b,c,d)=>k(b,c,d).iridescence.minimumThickness=a},iridescenceThicknessTexture:{extensions:{KHR_texture_transform:l("iridescence","thicknessTexture")}}},KHR_materials_sheen:{sheenColorFactor:{type:"Color3",get:(a,b,c)=>k(a,b,c).sheen.color,getTarget:k,set:(a,b,c,d)=>k(b,c,d).sheen.color.copyFrom(a)},sheenColorTexture:{extensions:{KHR_texture_transform:l("sheen","texture")}},sheenRoughnessFactor:{type:"number",get:(a,b,c)=>k(a,b,c).sheen.intensity,getTarget:k,set:(a,b,c,d)=>k(b,c,d).sheen.intensity=a},sheenRoughnessTexture:{extensions:{KHR_texture_transform:l("sheen","thicknessTexture")}}},KHR_materials_specular:{specularFactor:{type:"number",get:(a,b,c)=>k(a,b,c).metallicF0Factor,getTarget:k,set:(a,b,c,d)=>k(b,c,d).metallicF0Factor=a,getPropertyName:[()=>"metallicF0Factor"]},specularColorFactor:{type:"Color3",get:(a,b,c)=>k(a,b,c).metallicReflectanceColor,getTarget:k,set:(a,b,c,d)=>k(b,c,d).metallicReflectanceColor.copyFrom(a),getPropertyName:[()=>"metallicReflectanceColor"]},specularTexture:{extensions:{KHR_texture_transform:l("metallicReflectanceTexture")}},specularColorTexture:{extensions:{KHR_texture_transform:l("reflectanceTexture")}}},KHR_materials_transmission:{transmissionFactor:{type:"number",get:(a,b,c)=>k(a,b,c).subSurface.refractionIntensity,getTarget:k,set:(a,b,c,d)=>k(b,c,d).subSurface.refractionIntensity=a,getPropertyName:[()=>"subSurface.refractionIntensity"]},transmissionTexture:{extensions:{KHR_texture_transform:l("subSurface","refractionIntensityTexture")}}},KHR_materials_diffuse_transmission:{diffuseTransmissionFactor:{type:"number",get:(a,b,c)=>k(a,b,c).subSurface.translucencyIntensity,getTarget:k,set:(a,b,c,d)=>k(b,c,d).subSurface.translucencyIntensity=a},diffuseTransmissionTexture:{extensions:{KHR_texture_transform:l("subSurface","translucencyIntensityTexture")}},diffuseTransmissionColorFactor:{type:"Color3",get:(a,b,c)=>k(a,b,c).subSurface.translucencyColor,getTarget:k,set:(a,b,c,d)=>a&&k(b,c,d).subSurface.translucencyColor?.copyFrom(a)},diffuseTransmissionColorTexture:{extensions:{KHR_texture_transform:l("subSurface","translucencyColorTexture")}}},KHR_materials_volume:{attenuationColor:{type:"Color3",get:(a,b,c)=>k(a,b,c).subSurface.tintColor,getTarget:k,set:(a,b,c,d)=>k(b,c,d).subSurface.tintColor.copyFrom(a)},attenuationDistance:{type:"number",get:(a,b,c)=>k(a,b,c).subSurface.tintColorAtDistance,getTarget:k,set:(a,b,c,d)=>k(b,c,d).subSurface.tintColorAtDistance=a},thicknessFactor:{type:"number",get:(a,b,c)=>k(a,b,c).subSurface.maximumThickness,getTarget:k,set:(a,b,c,d)=>k(b,c,d).subSurface.maximumThickness=a},thicknessTexture:{extensions:{KHR_texture_transform:l("subSurface","thicknessTexture")}}}}}},extensions:{KHR_lights_punctual:{lights:{length:{type:"number",get:a=>a.length,getTarget:a=>a.map(a=>a._babylonLight),getPropertyName:[a=>"length"]},__array__:{__target__:!0,color:{type:"Color3",get:a=>a._babylonLight?.diffuse,set:(a,b)=>b._babylonLight?.diffuse.copyFrom(a),getTarget:a=>a._babylonLight,getPropertyName:[a=>"diffuse"]},intensity:{type:"number",get:a=>a._babylonLight?.intensity,set:(a,b)=>b._babylonLight?b._babylonLight.intensity=a:void 0,getTarget:a=>a._babylonLight,getPropertyName:[a=>"intensity"]},range:{type:"number",get:a=>a._babylonLight?.range,set:(a,b)=>b._babylonLight?b._babylonLight.range=a:void 0,getTarget:a=>a._babylonLight,getPropertyName:[a=>"range"]},spot:{innerConeAngle:{type:"number",get:a=>a._babylonLight?.innerAngle,set:(a,b)=>b._babylonLight?b._babylonLight.innerAngle=a:void 0,getTarget:a=>a._babylonLight,getPropertyName:[a=>"innerConeAngle"]},outerConeAngle:{type:"number",get:a=>a._babylonLight?.angle,set:(a,b)=>b._babylonLight?b._babylonLight.angle=a:void 0,getTarget:a=>a._babylonLight,getPropertyName:[a=>"outerConeAngle"]}}}}},EXT_lights_ies:{lights:{length:{type:"number",get:a=>a.length,getTarget:a=>a.map(a=>a._babylonLight),getPropertyName:[a=>"length"]}}},EXT_lights_image_based:{lights:{length:{type:"number",get:a=>a.length,getTarget:a=>a.map(a=>a._babylonTexture),getPropertyName:[a=>"length"]},__array__:{__target__:!0,intensity:{type:"number",get:a=>a._babylonTexture?.level,set:(a,b)=>{b._babylonTexture&&(b._babylonTexture.level=a)},getTarget:a=>a._babylonTexture},rotation:{type:"Quaternion",get:a=>a._babylonTexture&&d.PT.FromRotationMatrix(a._babylonTexture?.getReflectionTextureMatrix()),set:(a,b)=>{b._babylonTexture&&(b._babylonTexture.getScene()?.useRightHandedSystem||(a=d.PT.Inverse(a)),d.uq.FromQuaternionToRef(a,b._babylonTexture.getReflectionTextureMatrix()))},getTarget:a=>a._babylonTexture}}}}},animations:{length:{type:"number",get:a=>a.length,getTarget:a=>a.map(a=>a._babylonAnimationGroup),getPropertyName:[()=>"length"]},__array__:{}},meshes:{length:{type:"number",get:a=>a.length,getTarget:a=>a.map(a=>a.primitives[0]._instanceData?.babylonSourceMesh),getPropertyName:[()=>"length"]},__array__:{}}};function n(a){return new i(a,m)}function o(a){let b=a.split("/").map(a=>a.replace(/{}/g,"__array__")),c=m;for(let a of b)a&&(c=c[a]);if(c&&c.type&&c.get)return c}function p(a,b){let c=a.split("/").map(a=>a.replace(/{}/g,"__array__")),d=m;for(let a of c)a&&(d=d[a]);d&&d.type&&d.get&&(d.interpolation=b)}function q(a,b){let c=a.split("/").map(a=>a.replace(/{}/g,"__array__")),d=m;for(let a of c)if(a){if(!d[a]){if("?"===a){d.__ignoreObjectTree__=!0;continue}d[a]={},"__array__"===a&&(d[a].__target__=!0)}d=d[a]}Object.assign(d,b)}},22992:(a,b,c)=>{c.r(b),c.d(b,{AnimationPropertyInfo:()=>j,TransformNodeAnimationPropertyInfo:()=>k,WeightAnimationPropertyInfo:()=>l,getQuaternion:()=>h,getVector3:()=>g,getWeights:()=>i});var d=c(6232),e=c(29363),f=c(15158);function g(a,b,c,d){return e.Pq.FromArray(b,c).scaleInPlace(d)}function h(a,b,c,d){return e.PT.FromArray(b,c).scaleInPlace(d)}function i(a,b,c,d){let e=Array(a._numMorphTargets);for(let a=0;a<e.length;a++)e[a]=b[c++]*d;return e}class j{constructor(a,b,c,d){this.type=a,this.name=b,this.getValue=c,this.getStride=d}_buildAnimation(a,b,c){let e=new d.X5(a,this.name,b,this.type);return e.setKeys(c),e}}class k extends j{buildAnimations(a,b,c,d){let e=[];return e.push({babylonAnimatable:a._babylonTransformNode,babylonAnimation:this._buildAnimation(b,c,d)}),e}}class l extends j{buildAnimations(a,b,c,e){let f=[];if(a._numMorphTargets)for(let g=0;g<a._numMorphTargets;g++){let h=new d.X5(`${b}_${g}`,this.name,c,this.type);if(h.setKeys(e.map(a=>({frame:a.frame,inTangent:a.inTangent?a.inTangent[g]:void 0,value:a.value[g],outTangent:a.outTangent?a.outTangent[g]:void 0,interpolation:a.interpolation}))),a._primitiveBabylonMeshes){for(let b of a._primitiveBabylonMeshes)if(b.morphTargetManager){let a=b.morphTargetManager.getTarget(g),c=h.clone();a.animations.push(c),f.push({babylonAnimatable:a,babylonAnimation:c})}}}return f}}(0,f.ZU)("/nodes/{}/translation",[new k(d.X5.ANIMATIONTYPE_VECTOR3,"position",g,()=>3)]),(0,f.ZU)("/nodes/{}/rotation",[new k(d.X5.ANIMATIONTYPE_QUATERNION,"rotationQuaternion",h,()=>4)]),(0,f.ZU)("/nodes/{}/scale",[new k(d.X5.ANIMATIONTYPE_VECTOR3,"scaling",g,()=>3)]),(0,f.ZU)("/nodes/{}/weights",[new l(d.X5.ANIMATIONTYPE_FLOAT,"influence",i,a=>a._numMorphTargets)])},78135:(a,b,c)=>{c.r(b),c.d(b,{FlowGraphGLTFDataProvider:()=>f});var d=c(38926),e=c(77100);class f extends d.e{constructor(a){super();let b=a.glTF,c=b.animations?.map(a=>a._babylonAnimationGroup)||[];this.animationGroups=this.registerDataOutput("animationGroups",e.Vv,c);let d=b.nodes?.map(a=>a._babylonTransformNode)||[];this.nodes=this.registerDataOutput("nodes",e.Vv,d)}getClassName(){return"FlowGraphGLTFDataProvider"}}},79288:(a,b,c)=>{c.d(b,{default:()=>cX});var d,e,f,g,h,i,j,k,l,m,n,o,p=c(1850);c(60676);var q=c(8700),r=c(62686),s=c(12293),t=c(80933),u=c(81405),v=c(22462);function w(a,b,c,d){let e={externalResourceFunction:d};return c&&(e.uri="file:"===b?c:b+c),ArrayBuffer.isView(a)?GLTFValidator.validateBytes(a,e):GLTFValidator.validateString(a,e)}function x(){let a=[];onmessage=b=>{let c=b.data;switch(c.id){case"init":importScripts(c.url);break;case"validate":w(c.data,c.rootUrl,c.fileName,b=>new Promise((c,d)=>{let e=a.length;a.push({resolve:c,reject:d}),postMessage({id:"getExternalResource",index:e,uri:b})})).then(a=>{postMessage({id:"validate.resolve",value:a})},a=>{postMessage({id:"validate.reject",reason:a})});break;case"getExternalResource.resolve":a[c.index].resolve(c.value);break;case"getExternalResource.reject":a[c.index].reject(c.reason)}}}class y{static ValidateAsync(a,b,c,d){return"function"==typeof Worker?new Promise((e,f)=>{let g=`${w}(${x})()`,h=new Worker(URL.createObjectURL(new Blob([g],{type:"application/javascript"}))),i=a=>{h.removeEventListener("error",i),h.removeEventListener("message",j),f(a)},j=a=>{let b=a.data;switch(b.id){case"getExternalResource":d(b.uri).then(a=>{h.postMessage({id:"getExternalResource.resolve",index:b.index,value:a},[a.buffer])},a=>{h.postMessage({id:"getExternalResource.reject",index:b.index,reason:a})});break;case"validate.resolve":h.removeEventListener("error",i),h.removeEventListener("message",j),e(b.value),h.terminate();break;case"validate.reject":h.removeEventListener("error",i),h.removeEventListener("message",j),f(b.reason),h.terminate()}};if(h.addEventListener("error",i),h.addEventListener("message",j),h.postMessage({id:"init",url:r.S0.GetBabylonScriptURL(this.Configuration.url)}),ArrayBuffer.isView(a)){let d=a.slice();h.postMessage({id:"validate",data:d,rootUrl:b,fileName:c},[d.buffer])}else h.postMessage({id:"validate",data:a,rootUrl:b,fileName:c})}):(this._LoadScriptPromise||(this._LoadScriptPromise=r.S0.LoadBabylonScriptAsync(this.Configuration.url)),this._LoadScriptPromise.then(()=>w(a,b,c,d)))}}y.Configuration={url:`${r.S0._DefaultCdnUrl}/gltf_validator.js`};let z="Z2xURg",A={name:"gltf",extensions:{".gltf":{isBinary:!1,mimeType:"model/gltf+json"},".glb":{isBinary:!0,mimeType:"model/gltf-binary"}},canDirectLoad:a=>-1!==a.indexOf("asset")&&-1!==a.indexOf("version")||a.startsWith("data:base64,"+z)||a.startsWith("data:;base64,"+z)||a.startsWith("data:application/octet-stream;base64,"+z)||a.startsWith("data:model/gltf-binary;base64,"+z)};var B=c(23224),C=c(97835);function D(a,b,c){try{return Promise.resolve(new Uint8Array(a,b,c))}catch(a){return Promise.reject(a)}}!function(a){a[a.AUTO=0]="AUTO",a[a.FORCE_RIGHT_HANDED=1]="FORCE_RIGHT_HANDED"}(d||(d={})),function(a){a[a.NONE=0]="NONE",a[a.FIRST=1]="FIRST",a[a.ALL=2]="ALL"}(e||(e={})),function(a){a[a.LOADING=0]="LOADING",a[a.READY=1]="READY",a[a.COMPLETE=2]="COMPLETE"}(f||(f={}));class E{constructor(){this.coordinateSystemMode=d.AUTO,this.animationStartMode=e.FIRST,this.loadNodeAnimations=!0,this.loadSkins=!0,this.loadMorphTargets=!0,this.compileMaterials=!1,this.useClipPlane=!1,this.compileShadowGenerators=!1,this.transparencyAsCoverage=!1,this.useRangeRequests=!1,this.createInstances=!0,this.alwaysComputeBoundingBox=!1,this.loadAllMaterials=!1,this.loadOnlyMaterials=!1,this.skipMaterials=!1,this.useSRGBBuffers=!0,this.targetFps=60,this.alwaysComputeSkeletonRootNode=!1,this.useGltfTextureNames=!1,this.preprocessUrlAsync=a=>Promise.resolve(a),this.extensionOptions={}}copyFrom(a){a&&(this.onParsed=a.onParsed,this.coordinateSystemMode=a.coordinateSystemMode??this.coordinateSystemMode,this.animationStartMode=a.animationStartMode??this.animationStartMode,this.loadNodeAnimations=a.loadNodeAnimations??this.loadNodeAnimations,this.loadSkins=a.loadSkins??this.loadSkins,this.loadMorphTargets=a.loadMorphTargets??this.loadMorphTargets,this.compileMaterials=a.compileMaterials??this.compileMaterials,this.useClipPlane=a.useClipPlane??this.useClipPlane,this.compileShadowGenerators=a.compileShadowGenerators??this.compileShadowGenerators,this.transparencyAsCoverage=a.transparencyAsCoverage??this.transparencyAsCoverage,this.useRangeRequests=a.useRangeRequests??this.useRangeRequests,this.createInstances=a.createInstances??this.createInstances,this.alwaysComputeBoundingBox=a.alwaysComputeBoundingBox??this.alwaysComputeBoundingBox,this.loadAllMaterials=a.loadAllMaterials??this.loadAllMaterials,this.loadOnlyMaterials=a.loadOnlyMaterials??this.loadOnlyMaterials,this.skipMaterials=a.skipMaterials??this.skipMaterials,this.useSRGBBuffers=a.useSRGBBuffers??this.useSRGBBuffers,this.targetFps=a.targetFps??this.targetFps,this.alwaysComputeSkeletonRootNode=a.alwaysComputeSkeletonRootNode??this.alwaysComputeSkeletonRootNode,this.useGltfTextureNames=a.useGltfTextureNames??this.useGltfTextureNames,this.preprocessUrlAsync=a.preprocessUrlAsync??this.preprocessUrlAsync,this.customRootNode=a.customRootNode,this.onMeshLoaded=a.onMeshLoaded,this.onSkinLoaded=a.onSkinLoaded,this.onTextureLoaded=a.onTextureLoaded,this.onMaterialLoaded=a.onMaterialLoaded,this.onCameraLoaded=a.onCameraLoaded,this.extensionOptions=a.extensionOptions??this.extensionOptions)}}class F extends E{constructor(a){super(),this.onParsedObservable=new q.cP,this.onMeshLoadedObservable=new q.cP,this.onSkinLoadedObservable=new q.cP,this.onTextureLoadedObservable=new q.cP,this.onMaterialLoadedObservable=new q.cP,this.onCameraLoadedObservable=new q.cP,this.onCompleteObservable=new q.cP,this.onErrorObservable=new q.cP,this.onDisposeObservable=new q.cP,this.onExtensionLoadedObservable=new q.cP,this.validate=!1,this.onValidatedObservable=new q.cP,this._loader=null,this._state=null,this._requests=[],this.name=A.name,this.extensions=A.extensions,this.onLoaderStateChangedObservable=new q.cP,this._logIndentLevel=0,this._loggingEnabled=!1,this._log=this._logDisabled,this._capturePerformanceCounters=!1,this._startPerformanceCounter=this._startPerformanceCounterDisabled,this._endPerformanceCounter=this._endPerformanceCounterDisabled,this.copyFrom(a)}set onParsed(a){this._onParsedObserver&&this.onParsedObservable.remove(this._onParsedObserver),a&&(this._onParsedObserver=this.onParsedObservable.add(a))}set onMeshLoaded(a){this._onMeshLoadedObserver&&this.onMeshLoadedObservable.remove(this._onMeshLoadedObserver),a&&(this._onMeshLoadedObserver=this.onMeshLoadedObservable.add(a))}set onSkinLoaded(a){this._onSkinLoadedObserver&&this.onSkinLoadedObservable.remove(this._onSkinLoadedObserver),a&&(this._onSkinLoadedObserver=this.onSkinLoadedObservable.add(b=>a(b.node,b.skinnedNode)))}set onTextureLoaded(a){this._onTextureLoadedObserver&&this.onTextureLoadedObservable.remove(this._onTextureLoadedObserver),a&&(this._onTextureLoadedObserver=this.onTextureLoadedObservable.add(a))}set onMaterialLoaded(a){this._onMaterialLoadedObserver&&this.onMaterialLoadedObservable.remove(this._onMaterialLoadedObserver),a&&(this._onMaterialLoadedObserver=this.onMaterialLoadedObservable.add(a))}set onCameraLoaded(a){this._onCameraLoadedObserver&&this.onCameraLoadedObservable.remove(this._onCameraLoadedObserver),a&&(this._onCameraLoadedObserver=this.onCameraLoadedObservable.add(a))}set onComplete(a){this._onCompleteObserver&&this.onCompleteObservable.remove(this._onCompleteObserver),this._onCompleteObserver=this.onCompleteObservable.add(a)}set onError(a){this._onErrorObserver&&this.onErrorObservable.remove(this._onErrorObserver),this._onErrorObserver=this.onErrorObservable.add(a)}set onDispose(a){this._onDisposeObserver&&this.onDisposeObservable.remove(this._onDisposeObserver),this._onDisposeObserver=this.onDisposeObservable.add(a)}set onExtensionLoaded(a){this._onExtensionLoadedObserver&&this.onExtensionLoadedObservable.remove(this._onExtensionLoadedObserver),this._onExtensionLoadedObserver=this.onExtensionLoadedObservable.add(a)}get loggingEnabled(){return this._loggingEnabled}set loggingEnabled(a){this._loggingEnabled!==a&&(this._loggingEnabled=a,this._loggingEnabled?this._log=this._logEnabled:this._log=this._logDisabled)}get capturePerformanceCounters(){return this._capturePerformanceCounters}set capturePerformanceCounters(a){this._capturePerformanceCounters!==a&&(this._capturePerformanceCounters=a,this._capturePerformanceCounters?(this._startPerformanceCounter=this._startPerformanceCounterEnabled,this._endPerformanceCounter=this._endPerformanceCounterEnabled):(this._startPerformanceCounter=this._startPerformanceCounterDisabled,this._endPerformanceCounter=this._endPerformanceCounterDisabled))}set onValidated(a){this._onValidatedObserver&&this.onValidatedObservable.remove(this._onValidatedObserver),this._onValidatedObserver=this.onValidatedObservable.add(a)}dispose(){for(let a of(this._loader&&(this._loader.dispose(),this._loader=null),this._requests))a.abort();this._requests.length=0,delete this._progressCallback,this.preprocessUrlAsync=a=>Promise.resolve(a),this.onMeshLoadedObservable.clear(),this.onSkinLoadedObservable.clear(),this.onTextureLoadedObservable.clear(),this.onMaterialLoadedObservable.clear(),this.onCameraLoadedObservable.clear(),this.onCompleteObservable.clear(),this.onExtensionLoadedObservable.clear(),this.onDisposeObservable.notifyObservers(void 0),this.onDisposeObservable.clear()}loadFile(a,b,c,d,e,f,g,h){if(ArrayBuffer.isView(b))return this._loadBinary(a,b,c,d,g,h),null;this._progressCallback=e;let i=b.name||r.S0.GetFilename(b);if(!f)return this._loadFile(a,b,b=>{try{this._validate(a,b,c,i),d({json:this._parseJson(b)})}catch{g&&g()}},!1,g);if(this.useRangeRequests){this.validate&&u.V.Warn("glTF validation is not supported when range requests are enabled");let c={abort:()=>{},onCompleteObservable:new q.cP};return this._unpackBinaryAsync(new v.s({readAsync:(c,d)=>new Promise((e,f)=>{this._loadFile(a,b,a=>{e(new Uint8Array(a))},!0,a=>{f(a)},a=>{a.setRequestHeader("Range",`bytes=${c}-${c+d-1}`)})}),byteLength:0})).then(a=>{c.onCompleteObservable.notifyObservers(c),d(a)},g?a=>g(void 0,a):void 0),c}return this._loadFile(a,b,b=>{this._validate(a,new Uint8Array(b,0,b.byteLength),c,i),this._unpackBinaryAsync(new v.s({readAsync:(a,c)=>D(b,a,c),byteLength:b.byteLength})).then(a=>{d(a)},g?a=>g(void 0,a):void 0)},!0,g)}_loadBinary(a,b,c,d,e,f){this._validate(a,new Uint8Array(b.buffer,b.byteOffset,b.byteLength),c,f),this._unpackBinaryAsync(new v.s({readAsync:(a,c)=>(function(a,b,c){try{if(b<0||b>=a.byteLength)throw RangeError("Offset is out of range.");if(b+c>a.byteLength)throw RangeError("Length is out of range.");return Promise.resolve(new Uint8Array(a.buffer,a.byteOffset+b,c))}catch(a){return Promise.reject(a)}})(b,a,c),byteLength:b.byteLength})).then(a=>{d(a)},e?a=>e(void 0,a):void 0)}importMeshAsync(a,b,c,d,e,f){return Promise.resolve().then(()=>(this.onParsedObservable.notifyObservers(c),this.onParsedObservable.clear(),this._log(`Loading ${f||""}`),this._loader=this._getLoader(c),this._loader.importMeshAsync(a,b,null,c,d,e,f)))}loadAsync(a,b,c,d,e){return Promise.resolve().then(()=>(this.onParsedObservable.notifyObservers(b),this.onParsedObservable.clear(),this._log(`Loading ${e||""}`),this._loader=this._getLoader(b),this._loader.loadAsync(a,b,c,d,e)))}loadAssetContainerAsync(a,b,c,d,e){return Promise.resolve().then(()=>{this.onParsedObservable.notifyObservers(b),this.onParsedObservable.clear(),this._log(`Loading ${e||""}`),this._loader=this._getLoader(b);let f=new t.WZ(a),g=[];this.onMaterialLoadedObservable.add(a=>{g.push(a)});let h=[];this.onTextureLoadedObservable.add(a=>{h.push(a)});let i=[];this.onCameraLoadedObservable.add(a=>{i.push(a)});let j=[];return this.onMeshLoadedObservable.add(a=>{a.morphTargetManager&&j.push(a.morphTargetManager)}),this._loader.importMeshAsync(null,a,f,b,c,d,e).then(a=>(Array.prototype.push.apply(f.geometries,a.geometries),Array.prototype.push.apply(f.meshes,a.meshes),Array.prototype.push.apply(f.particleSystems,a.particleSystems),Array.prototype.push.apply(f.skeletons,a.skeletons),Array.prototype.push.apply(f.animationGroups,a.animationGroups),Array.prototype.push.apply(f.materials,g),Array.prototype.push.apply(f.textures,h),Array.prototype.push.apply(f.lights,a.lights),Array.prototype.push.apply(f.transformNodes,a.transformNodes),Array.prototype.push.apply(f.cameras,i),Array.prototype.push.apply(f.morphTargetManagers,j),f))})}canDirectLoad(a){return A.canDirectLoad(a)}directLoad(a,b){if(b.startsWith("base64,"+z)||b.startsWith(";base64,"+z)||b.startsWith("application/octet-stream;base64,"+z)||b.startsWith("model/gltf-binary;base64,"+z)){let c=(0,B.rz)(b);return this._validate(a,new Uint8Array(c,0,c.byteLength)),this._unpackBinaryAsync(new v.s({readAsync:(a,b)=>D(c,a,b),byteLength:c.byteLength}))}return this._validate(a,b),Promise.resolve({json:this._parseJson(b)})}createPlugin(a){return new F(a[A.name])}get loaderState(){return this._state}whenCompleteAsync(){return new Promise((a,b)=>{this.onCompleteObservable.addOnce(()=>{a()}),this.onErrorObservable.addOnce(a=>{b(a)})})}_setState(a){this._state!==a&&(this._state=a,this.onLoaderStateChangedObservable.notifyObservers(this._state),this._log(f[this._state]))}_loadFile(a,b,c,d,e,f){let g=a._loadFile(b,c,a=>{this._onProgress(a,g)},!0,d,e,f);return g.onCompleteObservable.add(()=>{g._lengthComputable=!0,g._total=g._loaded}),this._requests.push(g),g}_onProgress(a,b){if(!this._progressCallback)return;b._lengthComputable=a.lengthComputable,b._loaded=a.loaded,b._total=a.total;let c=!0,d=0,e=0;for(let a of this._requests){if(void 0===a._lengthComputable||void 0===a._loaded||void 0===a._total)return;c=c&&a._lengthComputable,d+=a._loaded,e+=a._total}this._progressCallback({lengthComputable:c,loaded:d,total:c?e:0})}_validate(a,b,c="",d=""){this.validate&&(this._startPerformanceCounter("Validate JSON"),y.ValidateAsync(b,c,d,b=>this.preprocessUrlAsync(c+b).then(b=>a._loadFileAsync(b,void 0,!0,!0).then(a=>new Uint8Array(a,0,a.byteLength)))).then(a=>{this._endPerformanceCounter("Validate JSON"),this.onValidatedObservable.notifyObservers(a),this.onValidatedObservable.clear()},a=>{this._endPerformanceCounter("Validate JSON"),r.S0.Warn(`Failed to validate: ${a.message}`),this.onValidatedObservable.clear()}))}_getLoader(a){let b=a.json.asset||{};this._log(`Asset version: ${b.version}`),b.minVersion&&this._log(`Asset minimum version: ${b.minVersion}`),b.generator&&this._log(`Asset generator: ${b.generator}`);let c=F._parseVersion(b.version);if(!c)throw Error("Invalid version: "+b.version);if(void 0!==b.minVersion){let a=F._parseVersion(b.minVersion);if(!a)throw Error("Invalid minimum version: "+b.minVersion);if(F._compareVersion(a,{major:2,minor:0})>0)throw Error("Incompatible minimum version: "+b.minVersion)}let d={1:F._CreateGLTF1Loader,2:F._CreateGLTF2Loader}[c.major];if(!d)throw Error("Unsupported version: "+b.version);return d(this)}_parseJson(a){this._startPerformanceCounter("Parse JSON"),this._log(`JSON length: ${a.length}`);let b=JSON.parse(a);return this._endPerformanceCounter("Parse JSON"),b}_unpackBinaryAsync(a){return this._startPerformanceCounter("Unpack Binary"),a.loadAsync(20).then(()=>{let b,c=a.readUint32();if(0x46546c67!==c)throw new C.bu("Unexpected magic: "+c,C.tG.GLTFLoaderUnexpectedMagicError);let d=a.readUint32();this.loggingEnabled&&this._log(`Binary version: ${d}`);let e=a.readUint32();switch(!this.useRangeRequests&&e!==a.buffer.byteLength&&u.V.Warn(`Length in header does not match actual data length: ${e} != ${a.buffer.byteLength}`),d){case 1:b=this._unpackBinaryV1Async(a,e);break;case 2:b=this._unpackBinaryV2Async(a,e);break;default:throw Error("Unsupported version: "+d)}return this._endPerformanceCounter("Unpack Binary"),b})}_unpackBinaryV1Async(a,b){let c=a.readUint32(),d=a.readUint32();if(0!==d)throw Error(`Unexpected content format: ${d}`);let e=b-a.byteOffset,f={json:this._parseJson(a.readString(c)),bin:null};if(0!==e){let b=a.byteOffset;f.bin={readAsync:(c,d)=>a.buffer.readAsync(b+c,d),byteLength:e}}return Promise.resolve(f)}_unpackBinaryV2Async(a,b){let c=a.readUint32();if(0x4e4f534a!==a.readUint32())throw Error("First chunk format is not JSON");return a.byteOffset+c===b?a.loadAsync(c).then(()=>({json:this._parseJson(a.readString(c)),bin:null})):a.loadAsync(c+8).then(()=>{let d={json:this._parseJson(a.readString(c)),bin:null},e=()=>{let c=a.readUint32();switch(a.readUint32()){case 0x4e4f534a:throw Error("Unexpected JSON chunk");case 5130562:{let b=a.byteOffset;d.bin={readAsync:(c,d)=>a.buffer.readAsync(b+c,d),byteLength:c},a.skipBytes(c);break}default:a.skipBytes(c)}return a.byteOffset!==b?a.loadAsync(8).then(e):Promise.resolve(d)};return e()})}static _parseVersion(a){if("1.0"===a||"1.0.1"===a)return{major:1,minor:0};let b=(a+"").match(/^(\d+)\.(\d+)/);return b?{major:parseInt(b[1]),minor:parseInt(b[2])}:null}static _compareVersion(a,b){return a.major>b.major?1:a.major<b.major?-1:a.minor>b.minor?1:a.minor<b.minor?-1:0}_logOpen(a){this._log(a),this._logIndentLevel++}_logClose(){--this._logIndentLevel}_logEnabled(a){let b=F._logSpaces.substring(0,2*this._logIndentLevel);u.V.Log(`${b}${a}`)}_logDisabled(a){}_startPerformanceCounterEnabled(a){r.S0.StartPerformanceCounter(a)}_startPerformanceCounterDisabled(a){}_endPerformanceCounterEnabled(a){r.S0.EndPerformanceCounter(a)}_endPerformanceCounterDisabled(a){}}F.IncrementalLoading=!0,F.HomogeneousCoordinates=!1,F._logSpaces="                                ",(0,s.qS)(new F),function(a){a[a.BYTE=5120]="BYTE",a[a.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",a[a.SHORT=5122]="SHORT",a[a.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",a[a.FLOAT=5126]="FLOAT"}(g||(g={})),function(a){a[a.FRAGMENT=35632]="FRAGMENT",a[a.VERTEX=35633]="VERTEX"}(h||(h={})),function(a){a[a.BYTE=5120]="BYTE",a[a.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",a[a.SHORT=5122]="SHORT",a[a.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",a[a.INT=5124]="INT",a[a.UNSIGNED_INT=5125]="UNSIGNED_INT",a[a.FLOAT=5126]="FLOAT",a[a.FLOAT_VEC2=35664]="FLOAT_VEC2",a[a.FLOAT_VEC3=35665]="FLOAT_VEC3",a[a.FLOAT_VEC4=35666]="FLOAT_VEC4",a[a.INT_VEC2=35667]="INT_VEC2",a[a.INT_VEC3=35668]="INT_VEC3",a[a.INT_VEC4=35669]="INT_VEC4",a[a.BOOL=35670]="BOOL",a[a.BOOL_VEC2=35671]="BOOL_VEC2",a[a.BOOL_VEC3=35672]="BOOL_VEC3",a[a.BOOL_VEC4=35673]="BOOL_VEC4",a[a.FLOAT_MAT2=35674]="FLOAT_MAT2",a[a.FLOAT_MAT3=35675]="FLOAT_MAT3",a[a.FLOAT_MAT4=35676]="FLOAT_MAT4",a[a.SAMPLER_2D=35678]="SAMPLER_2D"}(i||(i={})),function(a){a[a.CLAMP_TO_EDGE=33071]="CLAMP_TO_EDGE",a[a.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT",a[a.REPEAT=10497]="REPEAT"}(j||(j={})),function(a){a[a.NEAREST=9728]="NEAREST",a[a.LINEAR=9728]="LINEAR",a[a.NEAREST_MIPMAP_NEAREST=9984]="NEAREST_MIPMAP_NEAREST",a[a.LINEAR_MIPMAP_NEAREST=9985]="LINEAR_MIPMAP_NEAREST",a[a.NEAREST_MIPMAP_LINEAR=9986]="NEAREST_MIPMAP_LINEAR",a[a.LINEAR_MIPMAP_LINEAR=9987]="LINEAR_MIPMAP_LINEAR"}(k||(k={})),function(a){a[a.ALPHA=6406]="ALPHA",a[a.RGB=6407]="RGB",a[a.RGBA=6408]="RGBA",a[a.LUMINANCE=6409]="LUMINANCE",a[a.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA"}(l||(l={})),function(a){a[a.FRONT=1028]="FRONT",a[a.BACK=1029]="BACK",a[a.FRONT_AND_BACK=1032]="FRONT_AND_BACK"}(m||(m={})),function(a){a[a.ZERO=0]="ZERO",a[a.ONE=1]="ONE",a[a.SRC_COLOR=768]="SRC_COLOR",a[a.ONE_MINUS_SRC_COLOR=769]="ONE_MINUS_SRC_COLOR",a[a.DST_COLOR=774]="DST_COLOR",a[a.ONE_MINUS_DST_COLOR=775]="ONE_MINUS_DST_COLOR",a[a.SRC_ALPHA=770]="SRC_ALPHA",a[a.ONE_MINUS_SRC_ALPHA=771]="ONE_MINUS_SRC_ALPHA",a[a.DST_ALPHA=772]="DST_ALPHA",a[a.ONE_MINUS_DST_ALPHA=773]="ONE_MINUS_DST_ALPHA",a[a.CONSTANT_COLOR=32769]="CONSTANT_COLOR",a[a.ONE_MINUS_CONSTANT_COLOR=32770]="ONE_MINUS_CONSTANT_COLOR",a[a.CONSTANT_ALPHA=32771]="CONSTANT_ALPHA",a[a.ONE_MINUS_CONSTANT_ALPHA=32772]="ONE_MINUS_CONSTANT_ALPHA",a[a.SRC_ALPHA_SATURATE=776]="SRC_ALPHA_SATURATE"}(n||(n={}));var G=c(29363),H=c(44173),I=c(20568),J=c(65158),K=c(6232),L=c(31844),M=c(83209),N=c(18304),O=c(85330),P=c(94633),Q=c(34601),R=c(67297),S=c(94773),T=c(17843),U=c(60108),V=c(87090),W=c(6547),X=c(61269),Y=c(41587),Z=c(68109),$=c(64422),_=c(17576),aa=c(55540);class ab{static SetMatrix(a,b,c,d,e){let f=null;if("MODEL"===c.semantic?f=b.getWorldMatrix():"PROJECTION"===c.semantic?f=a.getProjectionMatrix():"VIEW"===c.semantic?f=a.getViewMatrix():"MODELVIEWINVERSETRANSPOSE"===c.semantic?f=G.uq.Transpose(b.getWorldMatrix().multiply(a.getViewMatrix()).invert()):"MODELVIEW"===c.semantic?f=b.getWorldMatrix().multiply(a.getViewMatrix()):"MODELVIEWPROJECTION"===c.semantic?f=b.getWorldMatrix().multiply(a.getTransformMatrix()):"MODELINVERSE"===c.semantic?f=b.getWorldMatrix().invert():"VIEWINVERSE"===c.semantic?f=a.getViewMatrix().invert():"PROJECTIONINVERSE"===c.semantic?f=a.getProjectionMatrix().invert():"MODELVIEWINVERSE"===c.semantic?f=b.getWorldMatrix().multiply(a.getViewMatrix()).invert():"MODELVIEWPROJECTIONINVERSE"===c.semantic?f=b.getWorldMatrix().multiply(a.getTransformMatrix()).invert():"MODELINVERSETRANSPOSE"===c.semantic&&(f=G.uq.Transpose(b.getWorldMatrix().invert())),f)switch(c.type){case i.FLOAT_MAT2:e.setMatrix2x2(d,G.uq.GetAsMatrix2x2(f));break;case i.FLOAT_MAT3:e.setMatrix3x3(d,G.uq.GetAsMatrix3x3(f));break;case i.FLOAT_MAT4:e.setMatrix(d,f)}}static SetUniform(a,b,c,d){switch(d){case i.FLOAT:return a.setFloat(b,c),!0;case i.FLOAT_VEC2:return a.setVector2(b,G.I9.FromArray(c)),!0;case i.FLOAT_VEC3:return a.setVector3(b,G.Pq.FromArray(c)),!0;case i.FLOAT_VEC4:return a.setVector4(b,G.IU.FromArray(c)),!0;default:return!1}}static GetWrapMode(a){switch(a){case j.CLAMP_TO_EDGE:return S.g.CLAMP_ADDRESSMODE;case j.MIRRORED_REPEAT:return S.g.MIRROR_ADDRESSMODE;case j.REPEAT:default:return S.g.WRAP_ADDRESSMODE}}static GetByteStrideFromType(a){switch(a.type){case"VEC2":return 2;case"VEC3":return 3;case"VEC4":case"MAT2":return 4;case"MAT3":return 9;case"MAT4":return 16;default:return 1}}static GetTextureFilterMode(a){switch(a){case k.LINEAR:case k.LINEAR_MIPMAP_NEAREST:case k.LINEAR_MIPMAP_LINEAR:return S.g.TRILINEAR_SAMPLINGMODE;case k.NEAREST:case k.NEAREST_MIPMAP_NEAREST:return S.g.NEAREST_SAMPLINGMODE;default:return S.g.BILINEAR_SAMPLINGMODE}}static GetBufferFromBufferView(a,b,c,d,e){c=b.byteOffset+c;let f=a.loadedBufferViews[b.buffer];if(c+d>f.byteLength)throw Error("Buffer access is out of range");let h=f.buffer;switch(c+=f.byteOffset,e){case g.BYTE:return new Int8Array(h,c,d);case g.UNSIGNED_BYTE:return new Uint8Array(h,c,d);case g.SHORT:return new Int16Array(h,c,d);case g.UNSIGNED_SHORT:return new Uint16Array(h,c,d);default:return new Float32Array(h,c,d)}}static GetBufferFromAccessor(a,b){let c=a.bufferViews[b.bufferView],d=b.count*ab.GetByteStrideFromType(b);return ab.GetBufferFromBufferView(a,c,b.byteOffset,d,b.componentType)}static DecodeBufferToText(a){let b="",c=a.byteLength;for(let d=0;d<c;++d)b+=String.fromCharCode(a[d]);return b}static GetDefaultMaterial(a){return ab._DefaultMaterial||(N.M.ShadersStore.GLTFDefaultMaterialVertexShader="precision highp float;\n\nuniform mat4 worldView;\nuniform mat4 projection;\n\nattribute vec3 position;\n\nvoid main(void)\n{\n    gl_Position = projection * worldView * vec4(position, 1.0);\n}",N.M.ShadersStore.GLTFDefaultMaterialPixelShader="precision highp float;\n\nuniform vec4 u_emission;\n\nvoid main(void)\n{\n    gl_FragColor = u_emission;\n}",ab._DefaultMaterial=new R.B("GLTFDefaultMaterial",a,{vertex:"GLTFDefaultMaterial",fragment:"GLTFDefaultMaterial"},{attributes:["position"],uniforms:["worldView","projection","u_emission"],samplers:[],needAlphaBlending:!1}),ab._DefaultMaterial.setColor4("u_emission",new H.ov(.5,.5,.5,1))),ab._DefaultMaterial}}ab._DefaultMaterial=null;var ac=c(75953);!function(a){a[a.IDENTIFIER=1]="IDENTIFIER",a[a.UNKNOWN=2]="UNKNOWN",a[a.END_OF_INPUT=3]="END_OF_INPUT"}(o||(o={}));class ad{constructor(a){this._pos=0,this.currentToken=o.UNKNOWN,this.currentIdentifier="",this.currentString="",this.isLetterOrDigitPattern=/^[a-zA-Z0-9]+$/,this._toParse=a,this._maxPos=a.length}getNextToken(){if(this.isEnd())return o.END_OF_INPUT;if(this.currentString=this.read(),this.currentToken=o.UNKNOWN,"_"===this.currentString||this.isLetterOrDigitPattern.test(this.currentString))for(this.currentToken=o.IDENTIFIER,this.currentIdentifier=this.currentString;!this.isEnd()&&(this.isLetterOrDigitPattern.test(this.currentString=this.peek())||"_"===this.currentString);)this.currentIdentifier+=this.currentString,this.forward();return this.currentToken}peek(){return this._toParse[this._pos]}read(){return this._toParse[this._pos++]}forward(){this._pos++}isEnd(){return this._pos>=this._maxPos}}let ae=["MODEL","VIEW","PROJECTION","MODELVIEW","MODELVIEWPROJECTION","JOINTMATRIX"],af=["world","view","projection","worldView","worldViewProjection","mBones"],ag=["translation","rotation","scale"],ah=["position","rotationQuaternion","scaling"],ai=(a,b,c)=>{for(let d in a){let e=a[d];c[b][d]=e}},aj=a=>{if(a)for(let b=0;b<a.length/2;b++)a[2*b+1]=1-a[2*b+1]},ak=a=>{if("NORMAL"===a.semantic)return"normal";if("POSITION"===a.semantic)return"position";if("JOINT"===a.semantic)return"matricesIndices";if("WEIGHT"===a.semantic)return"matricesWeights";if("COLOR"===a.semantic)return"color";else if(a.semantic&&-1!==a.semantic.indexOf("TEXCOORD_")){let b=Number(a.semantic.split("_")[1]);return"uv"+(0===b?"":b+1)}return null},al=a=>{let b=null;if(a.translation||a.rotation||a.scale){let c=G.Pq.FromArray(a.scale||[1,1,1]),d=G.PT.FromArray(a.rotation||[0,0,0,1]),e=G.Pq.FromArray(a.translation||[0,0,0]);b=G.uq.Compose(c,d,e)}else b=G.uq.FromArray(a.matrix);return b},am=(a,b,c,d)=>{for(let a=0;a<d.bones.length;a++)if(d.bones[a].name===c)return d.bones[a];let e=a.nodes;for(let f in e){let g=e[f];if(!g.jointName)continue;let h=g.children;for(let e=0;e<h.length;e++){let i=a.nodes[h[e]];if(i.jointName&&i.jointName===c){let c=al(g),e=new L.$(g.name||"",d,am(a,b,g.jointName,d),c);return e.id=f,e}}}return null},an=(a,b)=>{for(let c=0;c<a.length;c++){let d=a[c];for(let a=0;a<d.node.children.length;a++)if(d.node.children[a]===b)return d.bone}return null},ao=(a,b)=>{let c=a.nodes,d=c[b];if(d)return{node:d,id:b};for(let a in c)if((d=c[a]).jointName===b)return{node:d,id:a};return null},ap=(a,b)=>{for(let c=0;c<a.jointNames.length;c++)if(a.jointNames[c]===b)return!0;return!1},aq=(a,b,c,d,e)=>{let f;if(e||(a.scene._blockEntityCollection=!!a.assetContainer,(e=new Y.e(b.name||"",a.scene))._parentContainer=a.assetContainer,a.scene._blockEntityCollection=!1,e.id=d),!b.babylonNode)return e;let g=[],h=null,i=[],j=[],k=[],l=[];for(let b=0;b<c.length;b++){let d=c[b],e=a.meshes[d];if(e)for(let b=0;b<e.primitives.length;b++){let c=new T.P,d=e.primitives[b];d.mode;let f=d.attributes,m=null,n=null;for(let b in f)if(m=a.accessors[f[b]],n=ab.GetBufferFromAccessor(a,m),"NORMAL"===b)c.normals=new Float32Array(n.length),c.normals.set(n);else if("POSITION"===b){if(F.HomogeneousCoordinates){c.positions=new Float32Array(n.length-n.length/4);for(let a=0;a<n.length;a+=4)c.positions[a]=n[a],c.positions[a+1]=n[a+1],c.positions[a+2]=n[a+2]}else c.positions=new Float32Array(n.length),c.positions.set(n);j.push(c.positions.length)}else if(-1!==b.indexOf("TEXCOORD_")){let a=Number(b.split("_")[1]),d=U.R.UVKind+(0===a?"":a+1),e=new Float32Array(n.length);e.set(n),aj(e),c.set(e,d)}else"JOINT"===b?(c.matricesIndices=new Float32Array(n.length),c.matricesIndices.set(n)):"WEIGHT"===b?(c.matricesWeights=new Float32Array(n.length),c.matricesWeights.set(n)):"COLOR"===b&&(c.colors=new Float32Array(n.length),c.colors.set(n));if(m=a.accessors[d.indices])c.indices=new Int32Array((n=ab.GetBufferFromAccessor(a,m)).length),c.indices.set(n),l.push(c.indices.length);else{let a=[];for(let b=0;b<c.positions.length/3;b++)a.push(b);c.indices=new Int32Array(a),l.push(c.indices.length)}h?h.merge(c):h=c;let o=a.scene.getMaterialById(d.material);g.push(null===o?ab.GetDefaultMaterial(a.scene):o),i.push(0===i.length?0:i[i.length-1]+j[j.length-2]),k.push(0===k.length?0:k[k.length-1]+l[l.length-2])}}a.scene._blockEntityCollection=!!a.assetContainer,g.length>1?(f=new P.F("multimat"+d,a.scene)).subMaterials=g:f=new Q.F("multimat"+d,a.scene),1===g.length&&(f=g[0]),f._parentContainer=a.assetContainer,e.material||(e.material=f),new V.V(d,a.scene,h,!1,e),e.computeWorldMatrix(!0),a.scene._blockEntityCollection=!1,e.subMeshes=[];let m=0;for(let b=0;b<c.length;b++){let d=c[b],f=a.meshes[d];if(f)for(let a=0;a<f.primitives.length;a++)f.primitives[a].mode,W.K.AddToMesh(m,i[m],j[m],k[m],l[m],e,e,!0),m++}return e},ar=(a,b,c,d)=>{a.position&&(a.position=b),(a.rotationQuaternion||a.rotation)&&(a.rotationQuaternion=c),a.scaling&&(a.scaling=d)},as=(a,b,c,d=!1)=>{let e=a.nodes[b],f=null;if(d=!a.importOnlyMeshes||!!d||!a.importMeshesNames||-1!==a.importMeshesNames.indexOf(e.name||"")||0===a.importMeshesNames.length,!e.jointName&&d&&null!==(f=((a,b,c)=>{let d=null;if(a.importOnlyMeshes&&(b.skin||b.meshes)&&a.importMeshesNames&&a.importMeshesNames.length>0&&-1===a.importMeshesNames.indexOf(b.name||""))return null;if(b.skin){if(b.meshes){let e=a.skins[b.skin],f=aq(a,b,b.meshes,c,b.babylonNode);f.skeleton=a.scene.getLastSkeletonById(b.skin),null===f.skeleton&&(f.skeleton=((a,b,c,d)=>{if(d||(d=new M.E(b.name||"","",a.scene)),!b.babylonSkeleton)return d;let e=[],f=[];((a,b,c,d)=>{for(let e in a.nodes){let f=a.nodes[e];if(!f.jointName||ap(c,f.jointName))continue;let g=al(f),h=new L.$(f.name||"",b,null,g);h.id=e,d.push({bone:h,node:f,id:e})}for(let a=0;a<d.length;a++){let b=d[a],c=b.node.children;for(let a=0;a<c.length;a++){let e=null;for(let b=0;b<d.length;b++)if(d[b].id===c[a]){e=d[b];break}e&&(e.bone._parent=b.bone,b.bone.children.push(e.bone))}}})(a,d,b,e),d.bones=[];for(let c=0;c<b.jointNames.length;c++){let g=ao(a,b.jointNames[c]);if(!g)continue;let h=g.node;if(!h){r.S0.Warn("Joint named "+b.jointNames[c]+" does not exist");continue}let i=g.id,j=a.scene.getBoneById(i);if(j){d.bones.push(j);continue}let k=!1,l=null;for(let e=0;e<c;e++){let c=ao(a,b.jointNames[e]);if(!c)continue;let f=c.node;if(!f){r.S0.Warn("Joint named "+b.jointNames[e]+" does not exist when looking for parent");continue}let g=f.children;if(g){k=!1;for(let c=0;c<g.length;c++)if(g[c]===i){l=am(a,b,b.jointNames[e],d),k=!0;break}if(k)break}}let m=al(h);!l&&e.length>0&&(l=an(e,i))&&-1===f.indexOf(l)&&f.push(l),new L.$(h.jointName||"",d,l,m).id=i}let g=d.bones;d.bones=[];for(let c=0;c<b.jointNames.length;c++){let e=ao(a,b.jointNames[c]);if(e){for(let a=0;a<g.length;a++)if(g[a].id===e.id){d.bones.push(g[a]);break}}}d.prepare();for(let a=0;a<f.length;a++)d.bones.push(f[a]);return d})(a,e,0,e.babylonSkeleton),e.babylonSkeleton||(e.babylonSkeleton=f.skeleton)),d=f}}else if(b.meshes)d=aq(a,b,b.mesh?[b.mesh]:b.meshes,c,b.babylonNode);else if(!b.light||b.babylonNode||a.importOnlyMeshes){if(b.camera&&!b.babylonNode&&!a.importOnlyMeshes){let c=a.cameras[b.camera];if(c){if(a.scene._blockEntityCollection=!!a.assetContainer,"orthographic"===c.type){let c=new J.S(b.camera,G.Pq.Zero(),a.scene,!1);c.name=b.name||"",c.mode=I.i.ORTHOGRAPHIC_CAMERA,c.attachControl(),d=c,c._parentContainer=a.assetContainer}else if("perspective"===c.type){let e=c[c.type],f=new J.S(b.camera,G.Pq.Zero(),a.scene,!1);f.name=b.name||"",f.attachControl(),e.aspectRatio||(e.aspectRatio=a.scene.getEngine().getRenderWidth()/a.scene.getEngine().getRenderHeight()),e.znear&&e.zfar&&(f.maxZ=e.zfar,f.minZ=e.znear),d=f,f._parentContainer=a.assetContainer}a.scene._blockEntityCollection=!1}}}else{let c=a.lights[b.light];if(c){if("ambient"===c.type){let e=c[c.type],f=new Z.g(b.light,G.Pq.Zero(),a.scene);f.name=b.name||"",e.color&&(f.diffuse=H.v9.FromArray(e.color)),d=f}else if("directional"===c.type){let e=c[c.type],f=new $.Z(b.light,G.Pq.Zero(),a.scene);f.name=b.name||"",e.color&&(f.diffuse=H.v9.FromArray(e.color)),d=f}else if("point"===c.type){let e=c[c.type],f=new _.H(b.light,G.Pq.Zero(),a.scene);f.name=b.name||"",e.color&&(f.diffuse=H.v9.FromArray(e.color)),d=f}else if("spot"===c.type){let e=c[c.type],f=new aa.n(b.light,G.Pq.Zero(),G.Pq.Zero(),0,0,a.scene);f.name=b.name||"",e.color&&(f.diffuse=H.v9.FromArray(e.color)),e.fallOfAngle&&(f.angle=e.fallOfAngle),e.fallOffExponent&&(f.exponent=e.fallOffExponent),d=f}}}if(!b.jointName){if(b.babylonNode)return b.babylonNode;else if(null===d){a.scene._blockEntityCollection=!!a.assetContainer;let c=new Y.e(b.name||"",a.scene);c._parentContainer=a.assetContainer,a.scene._blockEntityCollection=!1,b.babylonNode=c,d=c}}if(null!==d){if(b.matrix&&d instanceof Y.e)((a,b)=>{if(b.matrix){let c=new G.Pq(0,0,0),d=new G.PT,e=new G.Pq(0,0,0);G.uq.FromArray(b.matrix).decompose(e,d,c),ar(a,c,d,e)}else b.translation&&b.rotation&&b.scale&&ar(a,G.Pq.FromArray(b.translation),G.PT.FromArray(b.rotation),G.Pq.FromArray(b.scale));a.computeWorldMatrix(!0)})(d,b);else{let a=b.translation||[0,0,0],c=b.rotation||[0,0,0,1],e=b.scale||[1,1,1];ar(d,G.Pq.FromArray(a),G.PT.FromArray(c),G.Pq.FromArray(e))}d.updateCache(!0),b.babylonNode=d}return d})(a,e,b))&&(f.id=b,f.parent=c),e.children)for(let b=0;b<e.children.length;b++)as(a,e.children[b],f,d)},at=a=>{let b=a.currentScene;if(b)for(let c=0;c<b.nodes.length;c++)as(a,b.nodes[c],null);else for(let c in a.scenes){b=a.scenes[c];for(let c=0;c<b.nodes.length;c++)as(a,b.nodes[c],null)}(a=>{for(let b in a.animations){let c=a.animations[b];if(!c.channels||!c.samplers)continue;let d=null;for(let e=0;e<c.channels.length;e++){let f=c.channels[e],g=c.samplers[f.sampler];if(!g)continue;let h=null,i=null;c.parameters?(h=c.parameters[g.input],i=c.parameters[g.output]):(h=g.input,i=g.output);let j=ab.GetBufferFromAccessor(a,a.accessors[h]),k=ab.GetBufferFromAccessor(a,a.accessors[i]),l=f.target.id,m=a.scene.getNodeById(l);if(null===m&&(m=a.scene.getNodeByName(l)),null===m){r.S0.Warn("Creating animation named "+b+". But cannot find node named "+l+" to attach to");continue}let n=m instanceof L.$,o=f.target.path,p=ag.indexOf(o);-1!==p&&(o=ah[p]);let q=K.X5.ANIMATIONTYPE_MATRIX;n||("rotationQuaternion"===o?(q=K.X5.ANIMATIONTYPE_QUATERNION,m.rotationQuaternion=new G.PT):q=K.X5.ANIMATIONTYPE_VECTOR3);let s=null,t=[],u=0,v=!1;n&&d&&d.getKeys().length===j.length&&(s=d,v=!0),v||(a.scene._blockEntityCollection=!!a.assetContainer,s=new K.X5(b,n?"_matrix":o,1,q,K.X5.ANIMATIONLOOPMODE_CYCLE),a.scene._blockEntityCollection=!1);for(let a=0;a<j.length;a++){let b=null;if("rotationQuaternion"===o?(b=G.PT.FromArray([k[u],k[u+1],k[u+2],k[u+3]]),u+=4):(b=G.Pq.FromArray([k[u],k[u+1],k[u+2]]),u+=3),n){let c=m,e=G.Pq.Zero(),f=new G.PT,g=G.Pq.Zero(),h=c.getBaseMatrix();v&&d&&(h=d.getKeys()[a].value),h.decompose(g,f,e),"position"===o?e=b:"rotationQuaternion"===o?f=b:g=b,b=G.uq.Compose(g,f,e)}v?d&&(d.getKeys()[a].value=b):t.push({frame:j[a],value:b})}!v&&s&&(s.setKeys(t),m.animations.push(s)),d=s,a.scene.stopAnimation(m),a.scene.beginAnimation(m,0,j[j.length-1],!0,1)}}})(a);for(let b=0;b<a.scene.skeletons.length;b++){let c=a.scene.skeletons[b];a.scene.beginAnimation(c,0,Number.MAX_VALUE,!0,1)}},au=(a,b,c)=>{for(let d in b.uniforms){let e=b.uniforms[d],f=b.parameters[e];if(a.currentIdentifier===d&&f.semantic&&!f.source&&!f.node){let a=ae.indexOf(f.semantic);if(-1!==a)return delete c[d],af[a]}}return a.currentIdentifier},av=a=>{for(let b in a.materials)ay.LoadMaterialAsync(a,b,()=>{},()=>{})};class aw{static CreateRuntime(a,b,c){let d={extensions:{},accessors:{},buffers:{},bufferViews:{},meshes:{},lights:{},cameras:{},nodes:{},images:{},textures:{},shaders:{},programs:{},samplers:{},techniques:{},materials:{},animations:{},skins:{},extensionsUsed:[],scenes:{},buffersCount:0,shaderscount:0,scene:b,rootUrl:c,loadedBufferCount:0,loadedBufferViews:{},loadedShaderCount:0,importOnlyMeshes:!1,dummyNodes:[],assetContainer:null};return a.extensions&&ai(a.extensions,"extensions",d),a.extensionsUsed&&ai(a.extensionsUsed,"extensionsUsed",d),a.buffers&&((a,b)=>{for(let c in a){let d=a[c];b.buffers[c]=d,b.buffersCount++}})(a.buffers,d),a.bufferViews&&ai(a.bufferViews,"bufferViews",d),a.accessors&&ai(a.accessors,"accessors",d),a.meshes&&ai(a.meshes,"meshes",d),a.lights&&ai(a.lights,"lights",d),a.cameras&&ai(a.cameras,"cameras",d),a.nodes&&ai(a.nodes,"nodes",d),a.images&&ai(a.images,"images",d),a.textures&&ai(a.textures,"textures",d),a.shaders&&((a,b)=>{for(let c in a){let d=a[c];b.shaders[c]=d,b.shaderscount++}})(a.shaders,d),a.programs&&ai(a.programs,"programs",d),a.samplers&&ai(a.samplers,"samplers",d),a.techniques&&ai(a.techniques,"techniques",d),a.materials&&ai(a.materials,"materials",d),a.animations&&ai(a.animations,"animations",d),a.skins&&ai(a.skins,"skins",d),a.scenes&&(d.scenes=a.scenes),a.scene&&a.scenes&&(d.currentScene=a.scenes[a.scene]),d}static LoadBufferAsync(a,b,c,d,e){let f=a.buffers[b];r.S0.IsBase64(f.uri)?setTimeout(()=>c(new Uint8Array(r.S0.DecodeBase64(f.uri)))):r.S0.LoadFile(a.rootUrl+f.uri,a=>c(new Uint8Array(a)),e,void 0,!0,a=>{a&&d(a.status+" "+a.statusText)})}static LoadTextureBufferAsync(a,b,c,d){let e=a.textures[b];if(!e||!e.source)return void d("");if(e.babylonTexture)return void c(null);let f=a.images[e.source];r.S0.IsBase64(f.uri)?setTimeout(()=>c(new Uint8Array(r.S0.DecodeBase64(f.uri)))):r.S0.LoadFile(a.rootUrl+f.uri,a=>c(new Uint8Array(a)),void 0,void 0,!0,a=>{a&&d(a.status+" "+a.statusText)})}static CreateTextureAsync(a,b,c,d){let e=a.textures[b];if(e.babylonTexture)return void d(e.babylonTexture);let f=a.samplers[e.sampler],g=f.minFilter===k.NEAREST_MIPMAP_NEAREST||f.minFilter===k.NEAREST_MIPMAP_LINEAR||f.minFilter===k.LINEAR_MIPMAP_NEAREST||f.minFilter===k.LINEAR_MIPMAP_LINEAR,h=S.g.BILINEAR_SAMPLINGMODE,i=null==c?new Blob:new Blob([c]),j=URL.createObjectURL(i),l=()=>URL.revokeObjectURL(j),m=new S.g(j,a.scene,!g,!0,h,l,l);void 0!==f.wrapS&&(m.wrapU=ab.GetWrapMode(f.wrapS)),void 0!==f.wrapT&&(m.wrapV=ab.GetWrapMode(f.wrapT)),m.name=b,e.babylonTexture=m,d(m)}static LoadShaderStringAsync(a,b,c,d){let e=a.shaders[b];if(r.S0.IsBase64(e.uri)){let a=atob(e.uri.split(",")[1]);c&&c(a)}else r.S0.LoadFile(a.rootUrl+e.uri,c,void 0,void 0,!1,a=>{a&&d&&d(a.status+" "+a.statusText)})}static LoadMaterialAsync(a,b,c,d){let e=a.materials[b];if(!e.technique){d&&d("No technique found.");return}let f=a.techniques[e.technique];if(!f){a.scene._blockEntityCollection=!!a.assetContainer;let d=new Q.F(b,a.scene);d._parentContainer=a.assetContainer,a.scene._blockEntityCollection=!1,d.diffuseColor=new H.v9(.5,.5,.5),d.sideOrientation=O.i.CounterClockWiseSideOrientation,c(d);return}let g=a.programs[f.program],h=f.states,j=N.M.ShadersStore[g.vertexShader+"VertexShader"],k=N.M.ShadersStore[g.fragmentShader+"PixelShader"],l="",p="",q=new ad(j),r=new ad(k),s={},t=[],u=[],v=[];for(let a in f.uniforms){let b=f.uniforms[a],c=f.parameters[b];if(s[a]=c,!c.semantic||c.node||c.source)c.type===i.SAMPLER_2D?v.push(a):t.push(a);else{let b=ae.indexOf(c.semantic);-1!==b?(t.push(af[b]),delete s[a]):t.push(a)}}for(let a in f.attributes){let b=f.attributes[a],c=f.parameters[b];if(c.semantic){let a=ak(c);a&&u.push(a)}}for(;!q.isEnd()&&q.getNextToken();){if(q.currentToken!==o.IDENTIFIER){l+=q.currentString;continue}let a=!1;for(let b in f.attributes){let c=f.attributes[b],d=f.parameters[c];if(q.currentIdentifier===b&&d.semantic){l+=ak(d),a=!0;break}}a||(l+=au(q,f,s))}for(;!r.isEnd()&&r.getNextToken();){if(r.currentToken!==o.IDENTIFIER){p+=r.currentString;continue}p+=au(r,f,s)}let w={vertex:g.vertexShader+b,fragment:g.fragmentShader+b},x={attributes:u,uniforms:t,samplers:v,needAlphaBlending:h&&h.enable&&-1!==h.enable.indexOf(3042)};N.M.ShadersStore[g.vertexShader+b+"VertexShader"]=l,N.M.ShadersStore[g.fragmentShader+b+"PixelShader"]=p;let y=new R.B(b,a.scene,w,x);if(y.onError=(a,b)=>{y.dispose(!0),d("Cannot compile program named "+g.name+". Error: "+b+". Default material will be applied")},y.onCompiled=b=>{((a,b,c,d,e)=>{let f=d.values||c.parameters,g=c.uniforms;for(let c in e){let h=e[c],j=h.type,k=f[g[c]];if(void 0===k&&(k=h.value),!k)continue;let l=a=>c=>{h.value&&a&&(b.setTexture(a,c),delete e[a])};j===i.SAMPLER_2D?ay.LoadTextureAsync(a,d.values?k:h.value,l(c),()=>l(null)):h.value&&ab.SetUniform(b,c,d.values?k:h.value,j)&&delete e[c]}})(a,y,f,e,s),y.onBind=b=>{((a,b,c,d,e,f,g)=>{let h=f.values||e.parameters;for(let g in c){let j=c[g],k=j.type;if(k===i.FLOAT_MAT2||k===i.FLOAT_MAT3||k===i.FLOAT_MAT4)if(!j.semantic||j.source||j.node){if(j.semantic&&(j.source||j.node)){let a=b.scene.getNodeByName(j.source||j.node||"");if(null===a&&(a=b.scene.getNodeById(j.source||j.node||"")),null===a)continue;ab.SetMatrix(b.scene,a,j,g,d.getEffect())}}else ab.SetMatrix(b.scene,a,j,g,d.getEffect());else{let a=h[e.uniforms[g]];if(!a)continue;if(k===i.SAMPLER_2D){let c=b.textures[f.values?a:j.value].babylonTexture;if(null==c)continue;d.getEffect().setTexture(g,c)}else ab.SetUniform(d.getEffect(),g,a,k)}}g(d)})(b,a,s,y,f,e,c)}},y.sideOrientation=O.i.CounterClockWiseSideOrientation,h&&h.functions){let a=h.functions;a.cullFace&&a.cullFace[0]!==m.BACK&&(y.backFaceCulling=!1);let b=a.blendFuncSeparate;b&&(b[0]===n.SRC_ALPHA&&b[1]===n.ONE_MINUS_SRC_ALPHA&&b[2]===n.ONE&&b[3]===n.ONE?y.alphaMode=ac.Y.ALPHA_COMBINE:b[0]===n.ONE&&b[1]===n.ONE&&b[2]===n.ZERO&&b[3]===n.ONE?y.alphaMode=ac.Y.ALPHA_ONEONE:b[0]===n.SRC_ALPHA&&b[1]===n.ONE&&b[2]===n.ZERO&&b[3]===n.ONE?y.alphaMode=ac.Y.ALPHA_ADD:b[0]===n.ZERO&&b[1]===n.ONE_MINUS_SRC_COLOR&&b[2]===n.ONE&&b[3]===n.ONE?y.alphaMode=ac.Y.ALPHA_SUBTRACT:b[0]===n.DST_COLOR&&b[1]===n.ZERO&&b[2]===n.ONE&&b[3]===n.ONE?y.alphaMode=ac.Y.ALPHA_MULTIPLY:b[0]===n.SRC_ALPHA&&b[1]===n.ONE_MINUS_SRC_COLOR&&b[2]===n.ONE&&b[3]===n.ONE&&(y.alphaMode=ac.Y.ALPHA_MAXIMIZED))}}}class ax{static RegisterExtension(a){if(ax.Extensions[a.name])return void r.S0.Error('Tool with the same name "'+a.name+'" already exists');ax.Extensions[a.name]=a}dispose(){}_importMeshAsync(a,b,c,d,e,f,g,h){return b.useRightHandedSystem=!0,ay.LoadRuntimeAsync(b,c,d,b=>{b.assetContainer=e,b.importOnlyMeshes=!0,""===a?b.importMeshesNames=[]:"string"==typeof a?b.importMeshesNames=[a]:!a||a instanceof Array?(b.importMeshesNames=[],r.S0.Warn("Argument meshesNames must be of type string or string[]")):b.importMeshesNames=[a],this._createNodes(b);let c=[],d=[];for(let a in b.nodes){let d=b.nodes[a];d.babylonNode instanceof X.u&&c.push(d.babylonNode)}for(let a in b.skins){let c=b.skins[a];c.babylonSkeleton instanceof M.E&&d.push(c.babylonSkeleton)}this._loadBuffersAsync(b,()=>{this._loadShadersAsync(b,()=>{av(b),at(b),!F.IncrementalLoading&&f&&f(c,d)})}),F.IncrementalLoading&&f&&f(c,d)},h),!0}importMeshAsync(a,b,c,d,e,f){return new Promise((g,h)=>{this._importMeshAsync(a,b,d,e,c,(a,b)=>{g({meshes:a,particleSystems:[],skeletons:b,animationGroups:[],lights:[],transformNodes:[],geometries:[],spriteManagers:[]})},f,a=>{h(Error(a))})})}_loadAsync(a,b,c,d,e,f){a.useRightHandedSystem=!0,ay.LoadRuntimeAsync(a,b,c,a=>{ay.LoadRuntimeExtensionsAsync(a,()=>{this._createNodes(a),this._loadBuffersAsync(a,()=>{this._loadShadersAsync(a,()=>{av(a),at(a),F.IncrementalLoading||d()})}),F.IncrementalLoading&&d()},f)},f)}async loadAsync(a,b,c,d){return await new Promise((e,f)=>{this._loadAsync(a,b,c,()=>{e()},d,a=>{f(Error(a))})})}_loadShadersAsync(a,b){let c=!1,d=(c,d)=>{ay.LoadShaderStringAsync(a,c,e=>{e instanceof ArrayBuffer||(a.loadedShaderCount++,e&&(N.M.ShadersStore[c+(d.type===h.VERTEX?"VertexShader":"PixelShader")]=e),a.loadedShaderCount===a.shaderscount&&b())},()=>{r.S0.Error("Error when loading shader program named "+c+" located at "+d.uri)})};for(let b in a.shaders){c=!0;let e=a.shaders[b];e?d.bind(this,b,e)():r.S0.Error("No shader named: "+b)}c||b()}_loadBuffersAsync(a,b){let c=!1,d=(c,d)=>{ay.LoadBufferAsync(a,c,e=>{a.loadedBufferCount++,e&&(e.byteLength!=a.buffers[c].byteLength&&r.S0.Error("Buffer named "+c+" is length "+e.byteLength+". Expected: "+d.byteLength),a.loadedBufferViews[c]=e),a.loadedBufferCount===a.buffersCount&&b()},()=>{r.S0.Error("Error when loading buffer named "+c+" located at "+d.uri)})};for(let b in a.buffers){c=!0;let e=a.buffers[b];e?d.bind(this,b,e)():r.S0.Error("No buffer named: "+b)}c||b()}_createNodes(a){let b=a.currentScene;if(b)for(let c=0;c<b.nodes.length;c++)as(a,b.nodes[c],null);else for(let c in a.scenes){b=a.scenes[c];for(let c=0;c<b.nodes.length;c++)as(a,b.nodes[c],null)}}}ax.Extensions={};class ay{constructor(a){this._name=a}get name(){return this._name}loadRuntimeAsync(a,b,c,d,e){return!1}loadRuntimeExtensionsAsync(a,b,c){return!1}loadBufferAsync(a,b,c,d,e){return!1}loadTextureBufferAsync(a,b,c,d){return!1}createTextureAsync(a,b,c,d,e){return!1}loadShaderStringAsync(a,b,c,d){return!1}loadMaterialAsync(a,b,c,d){return!1}static LoadRuntimeAsync(a,b,c,d,e){ay._ApplyExtensions(f=>f.loadRuntimeAsync(a,b,c,d,e),()=>{setTimeout(()=>{d&&d(aw.CreateRuntime(b.json,a,c))})})}static LoadRuntimeExtensionsAsync(a,b,c){ay._ApplyExtensions(d=>d.loadRuntimeExtensionsAsync(a,b,c),()=>{setTimeout(()=>{b()})})}static LoadBufferAsync(a,b,c,d,e){ay._ApplyExtensions(f=>f.loadBufferAsync(a,b,c,d,e),()=>{aw.LoadBufferAsync(a,b,c,d,e)})}static LoadTextureAsync(a,b,c,d){ay._LoadTextureBufferAsync(a,b,e=>{e&&ay._CreateTextureAsync(a,b,e,c,d)},d)}static LoadShaderStringAsync(a,b,c,d){ay._ApplyExtensions(e=>e.loadShaderStringAsync(a,b,c,d),()=>{aw.LoadShaderStringAsync(a,b,c,d)})}static LoadMaterialAsync(a,b,c,d){ay._ApplyExtensions(e=>e.loadMaterialAsync(a,b,c,d),()=>{aw.LoadMaterialAsync(a,b,c,d)})}static _LoadTextureBufferAsync(a,b,c,d){ay._ApplyExtensions(e=>e.loadTextureBufferAsync(a,b,c,d),()=>{aw.LoadTextureBufferAsync(a,b,c,d)})}static _CreateTextureAsync(a,b,c,d,e){ay._ApplyExtensions(f=>f.createTextureAsync(a,b,c,d,e),()=>{aw.CreateTextureAsync(a,b,c,d)})}static _ApplyExtensions(a,b){for(let b in ax.Extensions)if(a(ax.Extensions[b]))return;b()}}F._CreateGLTF1Loader=()=>new ax;class az extends ay{constructor(){super("KHR_binary_glTF")}loadRuntimeAsync(a,b,c,d){let e=b.json.extensionsUsed;return!!e&&-1!==e.indexOf(this.name)&&!!b.bin&&(this._bin=b.bin,d(aw.CreateRuntime(b.json,a,c)),!0)}loadBufferAsync(a,b,c,d){return -1!==a.extensionsUsed.indexOf(this.name)&&"binary_glTF"===b&&(this._bin.readAsync(0,this._bin.byteLength).then(c,a=>d(a.message)),!0)}loadTextureBufferAsync(a,b,c){let d=a.textures[b],e=a.images[d.source];if(!e.extensions||!(this.name in e.extensions))return!1;let f=e.extensions[this.name],h=a.bufferViews[f.bufferView];return c(ab.GetBufferFromBufferView(a,h,0,h.byteLength,g.UNSIGNED_BYTE)),!0}loadShaderStringAsync(a,b,c){let d=a.shaders[b];if(!d.extensions||!(this.name in d.extensions))return!1;let e=d.extensions[this.name],f=a.bufferViews[e.bufferView],h=ab.GetBufferFromBufferView(a,f,0,f.byteLength,g.UNSIGNED_BYTE);return setTimeout(()=>{c(ab.DecodeBufferToText(h))}),!0}}ax.RegisterExtension(new az);class aA extends ay{constructor(){super("KHR_materials_common")}loadRuntimeExtensionsAsync(a){if(!a.extensions)return!1;let b=a.extensions[this.name];if(!b)return!1;let c=b.lights;if(c)for(let b in c){let d=c[b];switch(d.type){case"ambient":{let b=new Z.g(d.name,new G.Pq(0,1,0),a.scene),c=d.ambient;c&&(b.diffuse=H.v9.FromArray(c.color||[1,1,1]));break}case"point":{let b=new _.H(d.name,new G.Pq(10,10,10),a.scene),c=d.point;c&&(b.diffuse=H.v9.FromArray(c.color||[1,1,1]));break}case"directional":{let b=new $.Z(d.name,new G.Pq(0,-1,0),a.scene),c=d.directional;c&&(b.diffuse=H.v9.FromArray(c.color||[1,1,1]));break}case"spot":{let b=d.spot;b&&(new aa.n(d.name,new G.Pq(0,10,0),new G.Pq(0,-1,0),b.fallOffAngle||Math.PI,b.fallOffExponent||0,a.scene).diffuse=H.v9.FromArray(b.color||[1,1,1]));break}default:r.S0.Warn('GLTF Material Common extension: light type "'+d.type+"” not supported")}}return!1}loadMaterialAsync(a,b,c,d){let e=a.materials[b];if(!e||!e.extensions)return!1;let f=e.extensions[this.name];if(!f)return!1;let g=new Q.F(b,a.scene);return g.sideOrientation=O.i.CounterClockWiseSideOrientation,"CONSTANT"===f.technique&&(g.disableLighting=!0),g.backFaceCulling=void 0!==f.doubleSided&&!f.doubleSided,g.alpha=void 0===f.values.transparency?1:f.values.transparency,g.specularPower=void 0===f.values.shininess?0:f.values.shininess,"string"==typeof f.values.ambient?this._loadTexture(a,f.values.ambient,g,"ambientTexture",d):g.ambientColor=H.v9.FromArray(f.values.ambient||[0,0,0]),"string"==typeof f.values.diffuse?this._loadTexture(a,f.values.diffuse,g,"diffuseTexture",d):g.diffuseColor=H.v9.FromArray(f.values.diffuse||[0,0,0]),"string"==typeof f.values.emission?this._loadTexture(a,f.values.emission,g,"emissiveTexture",d):g.emissiveColor=H.v9.FromArray(f.values.emission||[0,0,0]),"string"==typeof f.values.specular?this._loadTexture(a,f.values.specular,g,"specularTexture",d):g.specularColor=H.v9.FromArray(f.values.specular||[0,0,0]),!0}_loadTexture(a,b,c,d,e){aw.LoadTextureBufferAsync(a,b,e=>{aw.CreateTextureAsync(a,b,e,a=>c[d]=a)},e)}}ax.RegisterExtension(new aA);var aB=c(14242),aC=c(11199),aD=c(39276),aE=c(75346),aF=c(20167),aG=c(54471);let aH=new Map;function aI(a,b,c){aJ(a)&&u.V.Warn(`Extension with the name '${a}' already exists`),aH.set(a,{isGLTFExtension:b,factory:c})}function aJ(a){return aH.delete(a)}var aK=c(15158),aL=c(7001),aM=c(23275);class aN{static Get(a,b,c){if(!b||void 0==c||!b[c])throw Error(`${a}: Failed to find index (${c})`);return b[c]}static TryGet(a,b){return a&&void 0!=b&&a[b]?a[b]:null}static Assign(a){if(a)for(let b=0;b<a.length;b++)a[b].index=b}}function aO(a){if(a.min&&a.max){let b=a.min,c=a.max,d=G.AA.Vector3[0].copyFromFloats(b[0],b[1],b[2]),e=G.AA.Vector3[1].copyFromFloats(c[0],c[1],c[2]);if(a.normalized&&5126!==a.componentType){let b=1;switch(a.componentType){case 5120:b=127;break;case 5121:b=255;break;case 5122:b=32767;break;case 5123:b=65535}let c=1/b;d.scaleInPlace(c),e.scaleInPlace(c)}return new aG.j(d,e)}return null}class aP{static RegisterExtension(a,b){aI(a,!1,b)}static UnregisterExtension(a){return aJ(a)}get gltf(){if(!this._gltf)throw Error("glTF JSON is not available");return this._gltf}get bin(){return this._bin}get parent(){return this._parent}get babylonScene(){if(!this._babylonScene)throw Error("Scene is not available");return this._babylonScene}get rootBabylonMesh(){return this._rootBabylonMesh}get rootUrl(){return this._rootUrl}constructor(a){this._completePromises=[],this._assetContainer=null,this._babylonLights=[],this._disableInstancedMesh=0,this._allMaterialsDirtyRequired=!1,this._skipStartAnimationStep=!1,this._extensions=[],this._disposed=!1,this._rootUrl=null,this._fileName=null,this._uniqueRootUrl=null,this._bin=null,this._rootBabylonMesh=null,this._defaultBabylonMaterialData={},this._postSceneLoadActions=[],this._parent=a}dispose(){this._disposed||(this._disposed=!0,this._completePromises.length=0,this._extensions.forEach(a=>a.dispose&&a.dispose()),this._extensions.length=0,this._gltf=null,this._bin=null,this._babylonScene=null,this._rootBabylonMesh=null,this._defaultBabylonMaterialData={},this._postSceneLoadActions.length=0,this._parent.dispose())}async importMeshAsync(a,b,c,d,e,f,g=""){return await Promise.resolve().then(async()=>{this._babylonScene=b,this._assetContainer=c,this._loadData(d);let f=null;if(a){let b={};if(this._gltf.nodes)for(let a of this._gltf.nodes)a.name&&(b[a.name]=a.index);f=(a instanceof Array?a:[a]).map(a=>{let c=b[a];if(void 0===c)throw Error(`Failed to find node '${a}'`);return c})}return await this._loadAsync(e,g,f,()=>({meshes:this._getMeshes(),particleSystems:[],skeletons:this._getSkeletons(),animationGroups:this._getAnimationGroups(),lights:this._babylonLights,transformNodes:this._getTransformNodes(),geometries:this._getGeometries(),spriteManagers:[]}))})}async loadAsync(a,b,c,d,e=""){return this._babylonScene=a,this._loadData(b),await this._loadAsync(c,e,null,()=>void 0)}async _loadAsync(a,b,c,d){return await Promise.resolve().then(async()=>{this._rootUrl=a,this._uniqueRootUrl=!a.startsWith("file:")&&b?a:`${a}${Date.now()}/`,this._fileName=b,this._allMaterialsDirtyRequired=!1,await this._loadExtensionsAsync();let e=`${f[f.LOADING]} => ${f[f.READY]}`,g=`${f[f.LOADING]} => ${f[f.COMPLETE]}`;this._parent._startPerformanceCounter(e),this._parent._startPerformanceCounter(g),this._parent._setState(f.LOADING),this._extensionsOnLoading();let h=[],i=this._babylonScene.blockMaterialDirtyMechanism;if(this._babylonScene.blockMaterialDirtyMechanism=!0,!this.parent.loadOnlyMaterials){if(c)h.push(this.loadSceneAsync("/nodes",{nodes:c,index:-1}));else if(void 0!=this._gltf.scene||this._gltf.scenes&&this._gltf.scenes[0]){let a=aN.Get("/scene",this._gltf.scenes,this._gltf.scene||0);h.push(this.loadSceneAsync(`/scenes/${a.index}`,a))}}if(!this.parent.skipMaterials&&this.parent.loadAllMaterials&&this._gltf.materials)for(let a=0;a<this._gltf.materials.length;++a){let b=this._gltf.materials[a],c="/materials/"+a,d=O.i.TriangleFillMode;h.push(this._loadMaterialAsync(c,b,null,d,()=>{}))}this._allMaterialsDirtyRequired?this._babylonScene.blockMaterialDirtyMechanism=i:this._babylonScene._forceBlockMaterialDirtyMechanism(i),this._parent.compileMaterials&&h.push(this._compileMaterialsAsync()),this._parent.compileShadowGenerators&&h.push(this._compileShadowGeneratorsAsync());let j=Promise.all(h).then(()=>{for(let a of(this._rootBabylonMesh&&this._rootBabylonMesh!==this._parent.customRootNode&&this._rootBabylonMesh.setEnabled(!0),this._babylonScene.materials))void 0!==a.maxSimultaneousLights&&(a.maxSimultaneousLights=Math.max(a.maxSimultaneousLights,this._babylonScene.lights.length));return this._extensionsOnReady(),this._parent._setState(f.READY),this._skipStartAnimationStep||this._startAnimations(),d()});return await j.then(a=>(this._parent._endPerformanceCounter(e),r.S0.SetImmediate(()=>{this._disposed||Promise.all(this._completePromises).then(()=>{this._parent._endPerformanceCounter(g),this._parent._setState(f.COMPLETE),this._parent.onCompleteObservable.notifyObservers(void 0),this._parent.onCompleteObservable.clear(),this.dispose()},a=>{this._parent.onErrorObservable.notifyObservers(a),this._parent.onErrorObservable.clear(),this.dispose()})}),a))}).catch(a=>{throw this._disposed||(this._parent.onErrorObservable.notifyObservers(a),this._parent.onErrorObservable.clear(),this.dispose()),a})}_loadData(a){if(this._gltf=a.json,this._setupData(),a.bin){let b=this._gltf.buffers;if(b&&b[0]&&!b[0].uri){let c=b[0];(c.byteLength<a.bin.byteLength-3||c.byteLength>a.bin.byteLength)&&u.V.Warn(`Binary buffer length (${c.byteLength}) from JSON does not match chunk length (${a.bin.byteLength})`),this._bin=a.bin}else u.V.Warn("Unexpected BIN chunk")}}_setupData(){if(aN.Assign(this._gltf.accessors),aN.Assign(this._gltf.animations),aN.Assign(this._gltf.buffers),aN.Assign(this._gltf.bufferViews),aN.Assign(this._gltf.cameras),aN.Assign(this._gltf.images),aN.Assign(this._gltf.materials),aN.Assign(this._gltf.meshes),aN.Assign(this._gltf.nodes),aN.Assign(this._gltf.samplers),aN.Assign(this._gltf.scenes),aN.Assign(this._gltf.skins),aN.Assign(this._gltf.textures),this._gltf.nodes){let a={};for(let b of this._gltf.nodes)if(b.children)for(let c of b.children)a[c]=b.index;let b=this._createRootNode();for(let c of this._gltf.nodes){let d=a[c.index];c.parent=void 0===d?b:this._gltf.nodes[d]}}}async _loadExtensionsAsync(){let a=[];if(aH.forEach((b,c)=>{this.parent.extensionOptions[c]?.enabled===!1?b.isGLTFExtension&&this.isExtensionUsed(c)&&u.V.Warn(`Extension ${c} is used but has been explicitly disabled.`):(!b.isGLTFExtension||this.isExtensionUsed(c))&&a.push((async()=>{let a=await b.factory(this);return a.name!==c&&u.V.Warn(`The name of the glTF loader extension instance does not match the registered name: ${a.name} !== ${c}`),this._parent.onExtensionLoadedObservable.notifyObservers(a),a})())}),this._extensions.push(...await Promise.all(a)),this._extensions.sort((a,b)=>(a.order||Number.MAX_VALUE)-(b.order||Number.MAX_VALUE)),this._parent.onExtensionLoadedObservable.clear(),this._gltf.extensionsRequired){for(let a of this._gltf.extensionsRequired)if(!this._extensions.some(b=>b.name===a&&b.enabled)){if(this.parent.extensionOptions[a]?.enabled===!1)throw Error(`Required extension ${a} is disabled`);throw Error(`Required extension ${a} is not available`)}}}_createRootNode(){if(void 0!==this._parent.customRootNode)return this._rootBabylonMesh=this._parent.customRootNode,{_babylonTransformNode:null===this._rootBabylonMesh?void 0:this._rootBabylonMesh,index:-1};this._babylonScene._blockEntityCollection=!!this._assetContainer;let a=new Y.e("__root__",this._babylonScene);this._rootBabylonMesh=a,this._rootBabylonMesh._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,this._rootBabylonMesh.setEnabled(!1);let b={_babylonTransformNode:this._rootBabylonMesh,index:-1};switch(this._parent.coordinateSystemMode){case d.AUTO:this._babylonScene.useRightHandedSystem||(b.rotation=[0,1,0,0],b.scale=[1,1,-1],aP._LoadTransform(b,this._rootBabylonMesh));break;case d.FORCE_RIGHT_HANDED:this._babylonScene.useRightHandedSystem=!0;break;default:throw Error(`Invalid coordinate system mode (${this._parent.coordinateSystemMode})`)}return this._parent.onMeshLoadedObservable.notifyObservers(a),b}loadSceneAsync(a,b){let c=this._extensionsLoadSceneAsync(a,b);if(c)return c;let d=[];if(this.logOpen(`${a} ${b.name||""}`),b.nodes)for(let c of b.nodes){let b=aN.Get(`${a}/nodes/${c}`,this._gltf.nodes,c);d.push(this.loadNodeAsync(`/nodes/${b.index}`,b,a=>{a.parent=this._rootBabylonMesh}))}for(let a of this._postSceneLoadActions)a();return d.push(this._loadAnimationsAsync()),this.logClose(),Promise.all(d).then(()=>{})}_forEachPrimitive(a,b){if(a._primitiveBabylonMeshes)for(let c of a._primitiveBabylonMeshes)b(c)}_getGeometries(){let a=[],b=this._gltf.nodes;if(b)for(let c of b)this._forEachPrimitive(c,b=>{let c=b.geometry;c&&-1===a.indexOf(c)&&a.push(c)});return a}_getMeshes(){let a=[];this._rootBabylonMesh instanceof X.u&&a.push(this._rootBabylonMesh);let b=this._gltf.nodes;if(b)for(let c of b)this._forEachPrimitive(c,b=>{a.push(b)});return a}_getTransformNodes(){let a=[],b=this._gltf.nodes;if(b)for(let c of b)c._babylonTransformNode&&"TransformNode"===c._babylonTransformNode.getClassName()&&a.push(c._babylonTransformNode),c._babylonTransformNodeForSkin&&a.push(c._babylonTransformNodeForSkin);return a}_getSkeletons(){let a=[],b=this._gltf.skins;if(b)for(let c of b)c._data&&a.push(c._data.babylonSkeleton);return a}_getAnimationGroups(){let a=[],b=this._gltf.animations;if(b)for(let c of b)c._babylonAnimationGroup&&a.push(c._babylonAnimationGroup);return a}_startAnimations(){switch(this._parent.animationStartMode){case e.NONE:break;case e.FIRST:{let a=this._getAnimationGroups();0!==a.length&&a[0].start(!0);break}case e.ALL:for(let a of this._getAnimationGroups())a.start(!0);break;default:return void u.V.Error(`Invalid animation start mode (${this._parent.animationStartMode})`)}}loadNodeAsync(a,b,c=()=>{}){let d=this._extensionsLoadNodeAsync(a,b,c);if(d)return d;if(b._babylonTransformNode)throw Error(`${a}: Invalid recursive node hierarchy`);let e=[];this.logOpen(`${a} ${b.name||""}`);let f=d=>{if(aP.AddPointerMetadata(d,a),aP._LoadTransform(b,d),void 0!=b.camera){let c=aN.Get(`${a}/camera`,this._gltf.cameras,b.camera);e.push(this.loadCameraAsync(`/cameras/${c.index}`,c,a=>{a.parent=d,this._babylonScene.useRightHandedSystem||(d.scaling.x=-1)}))}if(b.children)for(let c of b.children){let b=aN.Get(`${a}/children/${c}`,this._gltf.nodes,c);e.push(this.loadNodeAsync(`/nodes/${b.index}`,b,a=>{a.parent=d}))}c(d)},g=void 0!=b.mesh,h=this._parent.loadSkins&&void 0!=b.skin;if(!g||h){let a=b.name||`node${b.index}`;this._babylonScene._blockEntityCollection=!!this._assetContainer;let c=new aD.V(a,this._babylonScene);c._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,void 0==b.mesh?b._babylonTransformNode=c:b._babylonTransformNodeForSkin=c,f(c)}if(g)if(h){let c=aN.Get(`${a}/mesh`,this._gltf.meshes,b.mesh);e.push(this._loadMeshAsync(`/meshes/${c.index}`,b,c,c=>{let d=b._babylonTransformNodeForSkin;c.metadata=(0,aL.$)(d.metadata,c.metadata||{});let f=aN.Get(`${a}/skin`,this._gltf.skins,b.skin);e.push(this._loadSkinAsync(`/skins/${f.index}`,b,f,a=>{this._forEachPrimitive(b,b=>{b.skeleton=a}),this._postSceneLoadActions.push(()=>{if(void 0!=f.skeleton){let a=aN.Get(`/skins/${f.index}/skeleton`,this._gltf.nodes,f.skeleton).parent;b.index===a.index?c.parent=d.parent:c.parent=a._babylonTransformNode}else c.parent=this._rootBabylonMesh;this._parent.onSkinLoadedObservable.notifyObservers({node:d,skinnedNode:c})})}))}))}else{let c=aN.Get(`${a}/mesh`,this._gltf.meshes,b.mesh);e.push(this._loadMeshAsync(`/meshes/${c.index}`,b,c,f))}return this.logClose(),Promise.all(e).then(()=>(this._forEachPrimitive(b,a=>{!a.isAnInstance&&a.geometry&&a.geometry.useBoundingInfoFromGeometry?a._updateBoundingInfo():a.refreshBoundingInfo(!0,!0)}),b._babylonTransformNode))}_loadMeshAsync(a,b,c,d){let e=c.primitives;if(!e||!e.length)throw Error(`${a}: Primitives are missing`);void 0==e[0].index&&aN.Assign(e);let f=[];this.logOpen(`${a} ${c.name||""}`);let g=b.name||`node${b.index}`;if(1===e.length){let d=c.primitives[0];f.push(this._loadMeshPrimitiveAsync(`${a}/primitives/${d.index}`,g,b,c,d,a=>{b._babylonTransformNode=a,b._primitiveBabylonMeshes=[a]}))}else for(let d of(this._babylonScene._blockEntityCollection=!!this._assetContainer,b._babylonTransformNode=new aD.V(g,this._babylonScene),b._babylonTransformNode._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,b._primitiveBabylonMeshes=[],e))f.push(this._loadMeshPrimitiveAsync(`${a}/primitives/${d.index}`,`${g}_primitive${d.index}`,b,c,d,a=>{a.parent=b._babylonTransformNode,b._primitiveBabylonMeshes.push(a)}));return d(b._babylonTransformNode),this.logClose(),Promise.all(f).then(()=>b._babylonTransformNode)}_loadMeshPrimitiveAsync(a,b,c,d,e,f){let g,h,i=this._extensionsLoadMeshPrimitiveAsync(a,b,c,d,e,f);if(i)return i;this.logOpen(`${a}`);let j=0===this._disableInstancedMesh&&this._parent.createInstances&&void 0==c.skin&&!d.primitives[0].targets;if(j&&e._instanceData)this._babylonScene._blockEntityCollection=!!this._assetContainer,(g=e._instanceData.babylonSourceMesh.createInstance(b))._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,h=e._instanceData.promise;else{let f=[];this._babylonScene._blockEntityCollection=!!this._assetContainer;let i=new Y.e(b,this._babylonScene);i._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,i.sideOrientation=this._babylonScene.useRightHandedSystem?O.i.CounterClockWiseSideOrientation:O.i.ClockWiseSideOrientation,this._createMorphTargets(a,c,d,e,i),f.push(this._loadVertexDataAsync(a,e,i).then(async b=>await this._loadMorphTargetsAsync(a,e,i,b).then(()=>{this._disposed||(this._babylonScene._blockEntityCollection=!!this._assetContainer,b.applyToMesh(i),b._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1)})));let k=aP._GetDrawMode(a,e.mode);if(void 0==e.material){let a=this._defaultBabylonMaterialData[k];a||(a=this._createDefaultMaterial("__GLTFLoader._default",k),this._parent.onMaterialLoadedObservable.notifyObservers(a),this._defaultBabylonMaterialData[k]=a),i.material=a}else if(!this.parent.skipMaterials){let b=aN.Get(`${a}/material`,this._gltf.materials,e.material);f.push(this._loadMaterialAsync(`/materials/${b.index}`,b,i,k,a=>{i.material=a}))}h=Promise.all(f),j&&(e._instanceData={babylonSourceMesh:i,promise:h}),g=i}return aP.AddPointerMetadata(g,a),this._parent.onMeshLoadedObservable.notifyObservers(g),f(g),this.logClose(),h.then(()=>g)}_loadVertexDataAsync(a,b,c){let d=this._extensionsLoadVertexDataAsync(a,b,c);if(d)return d;let e=b.attributes;if(!e)throw Error(`${a}: Attributes are missing`);let f=[],g=new V.V(c.name,this._babylonScene);if(void 0==b.indices)c.isUnIndexed=!0;else{let c=aN.Get(`${a}/indices`,this._gltf.accessors,b.indices);f.push(this._loadIndicesAccessorAsync(`/accessors/${c.index}`,c).then(a=>{g.setIndices(a)}))}let h=(b,d,h)=>{if(void 0==e[b])return;c._delayInfo=c._delayInfo||[],-1===c._delayInfo.indexOf(d)&&c._delayInfo.push(d);let i=aN.Get(`${a}/attributes/${b}`,this._gltf.accessors,e[b]);f.push(this._loadVertexAccessorAsync(`/accessors/${i.index}`,i,d).then(a=>{if(a.getKind()===U.R.PositionKind&&!this.parent.alwaysComputeBoundingBox&&!c.skeleton){let a=aO(i);a&&(g._boundingInfo=a,g.useBoundingInfoFromGeometry=!0)}g.setVerticesBuffer(a,i.count)})),d==U.R.MatricesIndicesExtraKind&&(c.numBoneInfluencers=8),h&&h(i)};return h("POSITION",U.R.PositionKind),h("NORMAL",U.R.NormalKind),h("TANGENT",U.R.TangentKind),h("TEXCOORD_0",U.R.UVKind),h("TEXCOORD_1",U.R.UV2Kind),h("TEXCOORD_2",U.R.UV3Kind),h("TEXCOORD_3",U.R.UV4Kind),h("TEXCOORD_4",U.R.UV5Kind),h("TEXCOORD_5",U.R.UV6Kind),h("JOINTS_0",U.R.MatricesIndicesKind),h("WEIGHTS_0",U.R.MatricesWeightsKind),h("JOINTS_1",U.R.MatricesIndicesExtraKind),h("WEIGHTS_1",U.R.MatricesWeightsExtraKind),h("COLOR_0",U.R.ColorKind,a=>{"VEC4"===a.type&&(c.hasVertexAlpha=!0)}),Promise.all(f).then(()=>g)}_createMorphTargets(a,b,c,d,e){if(!d.targets||!this._parent.loadMorphTargets)return;if(void 0==b._numMorphTargets)b._numMorphTargets=d.targets.length;else if(d.targets.length!==b._numMorphTargets)throw Error(`${a}: Primitives do not have the same number of targets`);let f=c.extras?c.extras.targetNames:null;this._babylonScene._blockEntityCollection=!!this._assetContainer,e.morphTargetManager=new aF.j(this._babylonScene),e.morphTargetManager._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,e.morphTargetManager.areUpdatesFrozen=!0;for(let a=0;a<d.targets.length;a++){let d=b.weights?b.weights[a]:c.weights?c.weights[a]:0,g=f?f[a]:`morphTarget${a}`;e.morphTargetManager.addTarget(new aE.M(g,d,e.getScene()))}}_loadMorphTargetsAsync(a,b,c,d){if(!b.targets||!this._parent.loadMorphTargets)return Promise.resolve();let e=[],f=c.morphTargetManager;for(let c=0;c<f.numTargets;c++){let g=f.getTarget(c);e.push(this._loadMorphTargetVertexDataAsync(`${a}/targets/${c}`,d,b.targets[c],g))}return Promise.all(e).then(()=>{f.areUpdatesFrozen=!1})}async _loadMorphTargetVertexDataAsync(a,b,c,d){let e=[],f=(d,f,g)=>{if(void 0==c[d])return;let h=b.getVertexBuffer(f);if(!h)return;let i=aN.Get(`${a}/${d}`,this._gltf.accessors,c[d]);e.push(this._loadFloatAccessorAsync(`/accessors/${i.index}`,i).then(a=>{g(h,a)}))};return f("POSITION",U.R.PositionKind,(a,b)=>{let c=new Float32Array(b.length);a.forEach(b.length,(a,d)=>{c[d]=b[d]+a}),d.setPositions(c)}),f("NORMAL",U.R.NormalKind,(a,b)=>{let c=new Float32Array(b.length);a.forEach(c.length,(a,d)=>{c[d]=b[d]+a}),d.setNormals(c)}),f("TANGENT",U.R.TangentKind,(a,b)=>{let c=new Float32Array(b.length/3*4),e=0;a.forEach(b.length/3*4,(a,d)=>{(d+1)%4!=0&&(c[e]=b[e]+a,e++)}),d.setTangents(c)}),f("TEXCOORD_0",U.R.UVKind,(a,b)=>{let c=new Float32Array(b.length);a.forEach(b.length,(a,d)=>{c[d]=b[d]+a}),d.setUVs(c)}),f("TEXCOORD_1",U.R.UV2Kind,(a,b)=>{let c=new Float32Array(b.length);a.forEach(b.length,(a,d)=>{c[d]=b[d]+a}),d.setUV2s(c)}),f("COLOR_0",U.R.ColorKind,(b,c)=>{let e=null,f=b.getSize();if(3===f){e=new Float32Array(c.length/3*4),b.forEach(c.length,(a,b)=>{let d=Math.floor(b/3),f=b%3;e[4*d+f]=c[3*d+f]+a});for(let a=0;a<c.length/3;++a)e[4*a+3]=1}else if(4===f)e=new Float32Array(c.length),b.forEach(c.length,(a,b)=>{e[b]=c[b]+a});else throw Error(`${a}: Invalid number of components (${f}) for COLOR_0 attribute`);d.setColors(e)}),await Promise.all(e).then(()=>{})}static _LoadTransform(a,b){if(void 0!=a.skin)return;let c=G.Pq.Zero(),d=G.PT.Identity(),e=G.Pq.One();a.matrix?G.uq.FromArray(a.matrix).decompose(e,d,c):(a.translation&&(c=G.Pq.FromArray(a.translation)),a.rotation&&(d=G.PT.FromArray(a.rotation)),a.scale&&(e=G.Pq.FromArray(a.scale))),b.position=c,b.rotationQuaternion=d,b.scaling=e}_loadSkinAsync(a,b,c,d){if(!this._parent.loadSkins)return Promise.resolve();let e=this._extensionsLoadSkinAsync(a,b,c);if(e)return e;if(c._data)return d(c._data.babylonSkeleton),c._data.promise;let f=`skeleton${c.index}`;this._babylonScene._blockEntityCollection=!!this._assetContainer;let g=new M.E(c.name||f,f,this._babylonScene);g._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,this._loadBones(a,c,g);let h=this._loadSkinInverseBindMatricesDataAsync(a,c).then(a=>{this._updateBoneMatrices(g,a)});return c._data={babylonSkeleton:g,promise:h},d(g),h}_loadBones(a,b,c){if(void 0==b.skeleton||this._parent.alwaysComputeSkeletonRootNode){let c=this._findSkeletonRootNode(`${a}/joints`,b.joints);if(c)if(void 0===b.skeleton)b.skeleton=c.index;else{let d=aN.Get(`${a}/skeleton`,this._gltf.nodes,b.skeleton);d===c||((a,b)=>{for(;b.parent;b=b.parent)if(b.parent===a)return!0;return!1})(d,c)||(u.V.Warn(`${a}/skeleton: Overriding with nearest common ancestor as skeleton node is not a common root`),b.skeleton=c.index)}else u.V.Warn(`${a}: Failed to find common root`)}let d={};for(let e of b.joints){let f=aN.Get(`${a}/joints/${e}`,this._gltf.nodes,e);this._loadBone(f,b,c,d)}}_findSkeletonRootNode(a,b){if(0===b.length)return null;let c={};for(let d of b){let b=[],e=aN.Get(`${a}/${d}`,this._gltf.nodes,d);for(;-1!==e.index;)b.unshift(e),e=e.parent;c[d]=b}let d=null;for(let a=0;;++a){let e=c[b[0]];if(a>=e.length)return d;let f=e[a];for(let g=1;g<b.length;++g)if(a>=(e=c[b[g]]).length||f!==e[a])return d;d=f}}_loadBone(a,b,c,d){a._isJoint=!0;let e=d[a.index];if(e)return e;let f=null;a.index!==b.skeleton&&(a.parent&&-1!==a.parent.index?f=this._loadBone(a.parent,b,c,d):void 0!==b.skeleton&&u.V.Warn(`/skins/${b.index}/skeleton: Skeleton node is not a common root`));let g=b.joints.indexOf(a.index);return e=new L.$(a.name||`joint${a.index}`,c,f,this._getNodeMatrix(a),null,null,g),d[a.index]=e,this._postSceneLoadActions.push(()=>{e.linkTransformNode(a._babylonTransformNode)}),e}_loadSkinInverseBindMatricesDataAsync(a,b){if(void 0==b.inverseBindMatrices)return Promise.resolve(null);let c=aN.Get(`${a}/inverseBindMatrices`,this._gltf.accessors,b.inverseBindMatrices);return this._loadFloatAccessorAsync(`/accessors/${c.index}`,c)}_updateBoneMatrices(a,b){for(let c of a.bones){let a=G.uq.Identity(),d=c._index;b&&-1!==d&&(G.uq.FromArrayToRef(b,16*d,a),a.invertToRef(a));let e=c.getParent();e&&a.multiplyToRef(e.getAbsoluteInverseBindMatrix(),a),c.updateMatrix(a,!1,!1),c._updateAbsoluteBindMatrices(void 0,!1)}}_getNodeMatrix(a){return a.matrix?G.uq.FromArray(a.matrix):G.uq.Compose(a.scale?G.Pq.FromArray(a.scale):G.Pq.One(),a.rotation?G.PT.FromArray(a.rotation):G.PT.Identity(),a.translation?G.Pq.FromArray(a.translation):G.Pq.Zero())}loadCameraAsync(a,b,c=()=>{}){let d=this._extensionsLoadCameraAsync(a,b,c);if(d)return d;this.logOpen(`${a} ${b.name||""}`),this._babylonScene._blockEntityCollection=!!this._assetContainer;let e=new J.S(b.name||`camera${b.index}`,G.Pq.Zero(),this._babylonScene,!1);switch(e._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,b._babylonCamera=e,e.setTarget(new G.Pq(0,0,-1)),b.type){case"perspective":{let c=b.perspective;if(!c)throw Error(`${a}: Camera perspective properties are missing`);e.fov=c.yfov,e.minZ=c.znear,e.maxZ=c.zfar||0;break}case"orthographic":if(!b.orthographic)throw Error(`${a}: Camera orthographic properties are missing`);e.mode=I.i.ORTHOGRAPHIC_CAMERA,e.orthoLeft=-b.orthographic.xmag,e.orthoRight=b.orthographic.xmag,e.orthoBottom=-b.orthographic.ymag,e.orthoTop=b.orthographic.ymag,e.minZ=b.orthographic.znear,e.maxZ=b.orthographic.zfar;break;default:throw Error(`${a}: Invalid camera type (${b.type})`)}return aP.AddPointerMetadata(e,a),this._parent.onCameraLoadedObservable.notifyObservers(e),c(e),this.logClose(),Promise.all([]).then(()=>e)}_loadAnimationsAsync(){let a=this._gltf.animations;if(!a)return Promise.resolve();let b=[];for(let c=0;c<a.length;c++){let d=a[c];b.push(this.loadAnimationAsync(`/animations/${d.index}`,d).then(a=>{0===a.targetedAnimations.length&&a.dispose()}))}return Promise.all(b).then(()=>{})}loadAnimationAsync(a,b){let d=this._extensionsLoadAnimationAsync(a,b);return d||Promise.resolve().then(c.bind(c,9613)).then(({AnimationGroup:c})=>{this._babylonScene._blockEntityCollection=!!this._assetContainer;let d=new c(b.name||`animation${b.index}`,this._babylonScene);d._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,b._babylonAnimationGroup=d;let e=[];for(let c of(aN.Assign(b.channels),aN.Assign(b.samplers),b.channels))e.push(this._loadAnimationChannelAsync(`${a}/channels/${c.index}`,a,b,c,(a,b)=>{a.animations=a.animations||[],a.animations.push(b),d.addTargetedAnimation(b,a)}));return Promise.all(e).then(()=>(d.normalize(0),d))})}async _loadAnimationChannelAsync(a,b,d,e,f){let g,h=this._extensionsLoadAnimationChannelAsync(a,b,d,e,f);if(h)return await h;if(void 0==e.target.node)return await Promise.resolve();let i=aN.Get(`${a}/target/node`,this._gltf.nodes,e.target.node),j=e.target.path,k="weights"===j;if(k&&!i._numMorphTargets||!k&&!i._babylonTransformNode||!this._parent.loadNodeAnimations&&!k&&!i._isJoint)return await Promise.resolve();switch(await Promise.resolve().then(c.bind(c,22992)),j){case"translation":g=(0,aK.tQ)("/nodes/{}/translation")?.interpolation;break;case"rotation":g=(0,aK.tQ)("/nodes/{}/rotation")?.interpolation;break;case"scale":g=(0,aK.tQ)("/nodes/{}/scale")?.interpolation;break;case"weights":g=(0,aK.tQ)("/nodes/{}/weights")?.interpolation;break;default:throw Error(`${a}/target/path: Invalid value (${e.target.path})`)}if(!g)throw Error(`${a}/target/path: Could not find interpolation properties for target path (${e.target.path})`);let l={object:i,info:g};return await this._loadAnimationChannelFromTargetInfoAsync(a,b,d,e,l,f)}_loadAnimationChannelFromTargetInfoAsync(a,b,c,d,e,f){let g=this.parent.targetFps,h=1/g,i=aN.Get(`${a}/sampler`,c.samplers,d.sampler);return this._loadAnimationSamplerAsync(`${b}/samplers/${d.sampler}`,i).then(a=>{let b=0,i=e.object;for(let j of e.info){let e=j.getStride(i),k=a.input,l=a.output,m=Array(k.length),n=0;switch(a.interpolation){case"STEP":for(let a=0;a<k.length;a++){let b=j.getValue(i,l,n,1);n+=e,m[a]={frame:k[a]*g,value:b,interpolation:1}}break;case"CUBICSPLINE":for(let a=0;a<k.length;a++){let b=j.getValue(i,l,n,h);n+=e;let c=j.getValue(i,l,n,1);n+=e;let d=j.getValue(i,l,n,h);n+=e,m[a]={frame:k[a]*g,inTangent:b,value:c,outTangent:d}}break;case"LINEAR":for(let a=0;a<k.length;a++){let b=j.getValue(i,l,n,1);n+=e,m[a]={frame:k[a]*g,value:b}}}if(n>0){let a=`${c.name||`animation${c.index}`}_channel${d.index}_${b}`;for(let c of j.buildAnimations(i,a,g,m))b++,f(c.babylonAnimatable,c.babylonAnimation)}}})}_loadAnimationSamplerAsync(a,b){if(b._data)return b._data;let c=b.interpolation||"LINEAR";switch(c){case"STEP":case"LINEAR":case"CUBICSPLINE":break;default:throw Error(`${a}/interpolation: Invalid value (${b.interpolation})`)}let d=aN.Get(`${a}/input`,this._gltf.accessors,b.input),e=aN.Get(`${a}/output`,this._gltf.accessors,b.output);return b._data=Promise.all([this._loadFloatAccessorAsync(`/accessors/${d.index}`,d),this._loadFloatAccessorAsync(`/accessors/${e.index}`,e)]).then(([a,b])=>({input:a,interpolation:c,output:b})),b._data}loadBufferAsync(a,b,c,d){let e=this._extensionsLoadBufferAsync(a,b,c,d);if(e)return e;if(!b._data)if(b.uri)b._data=this.loadUriAsync(`${a}/uri`,b,b.uri);else{if(!this._bin)throw Error(`${a}: Uri is missing or the binary glTF is missing its binary chunk`);b._data=this._bin.readAsync(0,b.byteLength)}return b._data.then(b=>{try{return new Uint8Array(b.buffer,b.byteOffset+c,d)}catch(b){throw Error(`${a}: ${b.message}`)}})}loadBufferViewAsync(a,b){let c=this._extensionsLoadBufferViewAsync(a,b);if(c)return c;if(b._data)return b._data;let d=aN.Get(`${a}/buffer`,this._gltf.buffers,b.buffer);return b._data=this.loadBufferAsync(`/buffers/${d.index}`,d,b.byteOffset||0,b.byteLength),b._data}_loadAccessorAsync(a,b,c){if(b._data)return b._data;let d=aP._GetNumComponents(a,b.type),e=d*U.R.GetTypeByteLength(b.componentType),f=d*b.count;if(void 0==b.bufferView)b._data=Promise.resolve(new c(f));else{let g=aN.Get(`${a}/bufferView`,this._gltf.bufferViews,b.bufferView);b._data=this.loadBufferViewAsync(`/bufferViews/${g.index}`,g).then(h=>{if(5126===b.componentType&&!b.normalized&&(!g.byteStride||g.byteStride===e))return aP._GetTypedArray(a,b.componentType,h,b.byteOffset,f);{let a=new c(f);return U.R.ForEach(h,b.byteOffset||0,g.byteStride||e,d,b.componentType,a.length,b.normalized||!1,(b,c)=>{a[c]=b}),a}})}if(b.sparse){let f=b.sparse;b._data=b._data.then(g=>{let h=aN.Get(`${a}/sparse/indices/bufferView`,this._gltf.bufferViews,f.indices.bufferView),i=aN.Get(`${a}/sparse/values/bufferView`,this._gltf.bufferViews,f.values.bufferView);return Promise.all([this.loadBufferViewAsync(`/bufferViews/${h.index}`,h),this.loadBufferViewAsync(`/bufferViews/${i.index}`,i)]).then(([h,i])=>{let j,k=aP._GetTypedArray(`${a}/sparse/indices`,f.indices.componentType,h,f.indices.byteOffset,f.count),l=d*f.count;if(5126!==b.componentType||b.normalized){let g=aP._GetTypedArray(`${a}/sparse/values`,b.componentType,i,f.values.byteOffset,l);j=new c(l),U.R.ForEach(g,0,e,d,b.componentType,j.length,b.normalized||!1,(a,b)=>{j[b]=a})}else j=aP._GetTypedArray(`${a}/sparse/values`,b.componentType,i,f.values.byteOffset,l);let m=0;for(let a=0;a<k.length;a++){let b=k[a]*d;for(let a=0;a<d;a++)g[b++]=j[m++]}return g})})}return b._data}_loadFloatAccessorAsync(a,b){return this._loadAccessorAsync(a,b,Float32Array)}_loadIndicesAccessorAsync(a,b){if("SCALAR"!==b.type)throw Error(`${a}/type: Invalid value ${b.type}`);if(5121!==b.componentType&&5123!==b.componentType&&5125!==b.componentType)throw Error(`${a}/componentType: Invalid value ${b.componentType}`);if(b._data)return b._data;if(b.sparse){let c=aP._GetTypedArrayConstructor(`${a}/componentType`,b.componentType);b._data=this._loadAccessorAsync(a,b,c)}else{let c=aN.Get(`${a}/bufferView`,this._gltf.bufferViews,b.bufferView);b._data=this.loadBufferViewAsync(`/bufferViews/${c.index}`,c).then(c=>aP._GetTypedArray(a,b.componentType,c,b.byteOffset,b.count))}return b._data}_loadVertexBufferViewAsync(a){if(a._babylonBuffer)return a._babylonBuffer;let b=this._babylonScene.getEngine();return a._babylonBuffer=this.loadBufferViewAsync(`/bufferViews/${a.index}`,a).then(a=>new U.h(b,a,!1)),a._babylonBuffer}_loadVertexAccessorAsync(a,b,c){if(b._babylonVertexBuffer?.[c])return b._babylonVertexBuffer[c];b._babylonVertexBuffer||(b._babylonVertexBuffer={});let d=this._babylonScene.getEngine();if(b.sparse||void 0==b.bufferView)b._babylonVertexBuffer[c]=this._loadFloatAccessorAsync(a,b).then(a=>new U.R(d,a,c,!1));else{let e=aN.Get(`${a}/bufferView`,this._gltf.bufferViews,b.bufferView);b._babylonVertexBuffer[c]=this._loadVertexBufferViewAsync(e).then(f=>{let g=aP._GetNumComponents(a,b.type);return new U.R(d,f,c,!1,void 0,e.byteStride,void 0,b.byteOffset,g,b.componentType,b.normalized,!0,void 0,!0)})}return b._babylonVertexBuffer[c]}_loadMaterialMetallicRoughnessPropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];return b&&(b.baseColorFactor?(c.albedoColor=H.v9.FromArray(b.baseColorFactor),c.alpha=b.baseColorFactor[3]):c.albedoColor=H.v9.White(),c.metallic=void 0==b.metallicFactor?1:b.metallicFactor,c.roughness=void 0==b.roughnessFactor?1:b.roughnessFactor,b.baseColorTexture&&d.push(this.loadTextureInfoAsync(`${a}/baseColorTexture`,b.baseColorTexture,a=>{a.name=`${c.name} (Base Color)`,c.albedoTexture=a})),b.metallicRoughnessTexture&&(b.metallicRoughnessTexture.nonColorData=!0,d.push(this.loadTextureInfoAsync(`${a}/metallicRoughnessTexture`,b.metallicRoughnessTexture,a=>{a.name=`${c.name} (Metallic Roughness)`,c.metallicTexture=a})),c.useMetallnessFromMetallicTextureBlue=!0,c.useRoughnessFromMetallicTextureGreen=!0,c.useRoughnessFromMetallicTextureAlpha=!1)),Promise.all(d).then(()=>{})}_loadMaterialAsync(a,b,c,d,e=()=>{}){let f=this._extensionsLoadMaterialAsync(a,b,c,d,e);if(f)return f;b._data=b._data||{};let g=b._data[d];if(!g){this.logOpen(`${a} ${b.name||""}`);let c=this.createMaterial(a,b,d);g={babylonMaterial:c,babylonMeshes:[],promise:this.loadMaterialPropertiesAsync(a,b,c)},b._data[d]=g,aP.AddPointerMetadata(c,a),this._parent.onMaterialLoadedObservable.notifyObservers(c),this.logClose()}return c&&(g.babylonMeshes.push(c),c.onDisposeObservable.addOnce(()=>{let a=g.babylonMeshes.indexOf(c);-1!==a&&g.babylonMeshes.splice(a,1)})),e(g.babylonMaterial),g.promise.then(()=>g.babylonMaterial)}_createDefaultMaterial(a,b){this._babylonScene._blockEntityCollection=!!this._assetContainer;let c=new aC.Y(a,this._babylonScene);return c._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,c.fillMode=b,c.enableSpecularAntiAliasing=!0,c.useRadianceOverAlpha=!this._parent.transparencyAsCoverage,c.useSpecularOverAlpha=!this._parent.transparencyAsCoverage,c.transparencyMode=aC.Y.PBRMATERIAL_OPAQUE,c.metallic=1,c.roughness=1,c}createMaterial(a,b,c){let d=this._extensionsCreateMaterial(a,b,c);if(d)return d;let e=b.name||`material${b.index}`;return this._createDefaultMaterial(e,c)}loadMaterialPropertiesAsync(a,b,c){let d=this._extensionsLoadMaterialPropertiesAsync(a,b,c);if(d)return d;let e=[];return e.push(this.loadMaterialBasePropertiesAsync(a,b,c)),b.pbrMetallicRoughness&&e.push(this._loadMaterialMetallicRoughnessPropertiesAsync(`${a}/pbrMetallicRoughness`,b.pbrMetallicRoughness,c)),this.loadMaterialAlphaProperties(a,b,c),Promise.all(e).then(()=>{})}loadMaterialBasePropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];return c.emissiveColor=b.emissiveFactor?H.v9.FromArray(b.emissiveFactor):new H.v9(0,0,0),b.doubleSided&&(c.backFaceCulling=!1,c.twoSidedLighting=!0),b.normalTexture&&(b.normalTexture.nonColorData=!0,d.push(this.loadTextureInfoAsync(`${a}/normalTexture`,b.normalTexture,a=>{a.name=`${c.name} (Normal)`,c.bumpTexture=a})),c.invertNormalMapX=!this._babylonScene.useRightHandedSystem,c.invertNormalMapY=this._babylonScene.useRightHandedSystem,void 0!=b.normalTexture.scale&&c.bumpTexture&&(c.bumpTexture.level=b.normalTexture.scale),c.forceIrradianceInFragment=!0),b.occlusionTexture&&(b.occlusionTexture.nonColorData=!0,d.push(this.loadTextureInfoAsync(`${a}/occlusionTexture`,b.occlusionTexture,a=>{a.name=`${c.name} (Occlusion)`,c.ambientTexture=a})),c.useAmbientInGrayScale=!0,void 0!=b.occlusionTexture.strength&&(c.ambientTextureStrength=b.occlusionTexture.strength)),b.emissiveTexture&&d.push(this.loadTextureInfoAsync(`${a}/emissiveTexture`,b.emissiveTexture,a=>{a.name=`${c.name} (Emissive)`,c.emissiveTexture=a})),Promise.all(d).then(()=>{})}loadMaterialAlphaProperties(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);switch(b.alphaMode||"OPAQUE"){case"OPAQUE":c.transparencyMode=aC.Y.PBRMATERIAL_OPAQUE,c.alpha=1;break;case"MASK":c.transparencyMode=aC.Y.PBRMATERIAL_ALPHATEST,c.alphaCutOff=void 0==b.alphaCutoff?.5:b.alphaCutoff,c.albedoTexture&&(c.albedoTexture.hasAlpha=!0);break;case"BLEND":c.transparencyMode=aC.Y.PBRMATERIAL_ALPHABLEND,c.albedoTexture&&(c.albedoTexture.hasAlpha=!0,c.useAlphaFromAlbedoTexture=!0);break;default:throw Error(`${a}/alphaMode: Invalid value (${b.alphaMode})`)}}loadTextureInfoAsync(a,b,c=()=>{}){let d=this._extensionsLoadTextureInfoAsync(a,b,c);if(d)return d;if(this.logOpen(`${a}`),b.texCoord>=6)throw Error(`${a}/texCoord: Invalid value (${b.texCoord})`);let e=aN.Get(`${a}/index`,this._gltf.textures,b.index);e._textureInfo=b;let f=this._loadTextureAsync(`/textures/${b.index}`,e,d=>{d.coordinatesIndex=b.texCoord||0,aP.AddPointerMetadata(d,a),this._parent.onTextureLoadedObservable.notifyObservers(d),c(d)});return this.logClose(),f}_loadTextureAsync(a,b,c=()=>{}){let d=this._extensionsLoadTextureAsync(a,b,c);if(d)return d;this.logOpen(`${a} ${b.name||""}`);let e=void 0==b.sampler?aP.DefaultSampler:aN.Get(`${a}/sampler`,this._gltf.samplers,b.sampler),f=aN.Get(`${a}/source`,this._gltf.images,b.source),g=this._createTextureAsync(a,e,f,c,void 0,!b._textureInfo.nonColorData);return this.logClose(),g}_createTextureAsync(a,b,c,d=()=>{},e,f){let g=this._loadSampler(`/samplers/${b.index}`,b),h=[],i=new aB.c;this._babylonScene._blockEntityCollection=!!this._assetContainer;let j={noMipmap:g.noMipMaps,invertY:!1,samplingMode:g.samplingMode,onLoad:()=>{this._disposed||i.resolve()},onError:(b,c)=>{this._disposed||i.reject(Error(`${a}: ${c&&c.message?c.message:b||"Failed to load texture"}`))},mimeType:c.mimeType??(0,B.ny)(c.uri??""),loaderOptions:e,useSRGBBuffer:!!f&&this._parent.useSRGBBuffers},k=new S.g(null,this._babylonScene,j);return k._parentContainer=this._assetContainer,this._babylonScene._blockEntityCollection=!1,h.push(i.promise),h.push(this.loadImageAsync(`/images/${c.index}`,c).then(a=>{let b=c.uri||`${this._fileName}#image${c.index}`,d=`data:${this._uniqueRootUrl}${b}`;k.updateURL(d,a);let e=k.getInternalTexture();e&&(e.label=c.name)})),k.wrapU=g.wrapU,k.wrapV=g.wrapV,d(k),this._parent.useGltfTextureNames&&(k.name=c.name||c.uri||`image${c.index}`),Promise.all(h).then(()=>k)}_loadSampler(a,b){return b._data||(b._data={noMipMaps:9728===b.minFilter||9729===b.minFilter,samplingMode:aP._GetTextureSamplingMode(a,b),wrapU:aP._GetTextureWrapMode(`${a}/wrapS`,b.wrapS),wrapV:aP._GetTextureWrapMode(`${a}/wrapT`,b.wrapT)}),b._data}loadImageAsync(a,b){if(!b._data){if(this.logOpen(`${a} ${b.name||""}`),b.uri)b._data=this.loadUriAsync(`${a}/uri`,b,b.uri);else{let c=aN.Get(`${a}/bufferView`,this._gltf.bufferViews,b.bufferView);b._data=this.loadBufferViewAsync(`/bufferViews/${c.index}`,c)}this.logClose()}return b._data}loadUriAsync(a,b,c){let d=this._extensionsLoadUriAsync(a,b,c);if(d)return d;if(!aP._ValidateUri(c))throw Error(`${a}: '${c}' is invalid`);if((0,B.f2)(c)){let b=new Uint8Array((0,B.rz)(c));return this.log(`${a}: Decoded ${c.substring(0,64)}... (${b.length} bytes)`),Promise.resolve(b)}return this.log(`${a}: Loading ${c}`),this._parent.preprocessUrlAsync(this._rootUrl+c).then(b=>new Promise((d,e)=>{this._parent._loadFile(this._babylonScene,b,b=>{this._disposed||(this.log(`${a}: Loaded ${c} (${b.byteLength} bytes)`),d(new Uint8Array(b)))},!0,b=>{e(new B.hX(`${a}: Failed to load '${c}'${b?": "+b.status+" "+b.statusText:""}`,b))})}))}static AddPointerMetadata(a,b){a.metadata=a.metadata||{};let c=a._internalMetadata=a._internalMetadata||{},d=c.gltf=c.gltf||{};(d.pointers=d.pointers||[]).push(b)}static _GetTextureWrapMode(a,b){switch(b=void 0==b?10497:b){case 33071:return S.g.CLAMP_ADDRESSMODE;case 33648:return S.g.MIRROR_ADDRESSMODE;case 10497:return S.g.WRAP_ADDRESSMODE;default:return u.V.Warn(`${a}: Invalid value (${b})`),S.g.WRAP_ADDRESSMODE}}static _GetTextureSamplingMode(a,b){let c=void 0==b.magFilter?9729:b.magFilter,d=void 0==b.minFilter?9987:b.minFilter;if(9729===c)switch(d){case 9728:return S.g.LINEAR_NEAREST;case 9729:return S.g.LINEAR_LINEAR;case 9984:return S.g.LINEAR_NEAREST_MIPNEAREST;case 9985:return S.g.LINEAR_LINEAR_MIPNEAREST;case 9986:return S.g.LINEAR_NEAREST_MIPLINEAR;case 9987:return S.g.LINEAR_LINEAR_MIPLINEAR;default:return u.V.Warn(`${a}/minFilter: Invalid value (${d})`),S.g.LINEAR_LINEAR_MIPLINEAR}switch(9728!==c&&u.V.Warn(`${a}/magFilter: Invalid value (${c})`),d){case 9728:return S.g.NEAREST_NEAREST;case 9729:return S.g.NEAREST_LINEAR;case 9984:return S.g.NEAREST_NEAREST_MIPNEAREST;case 9985:return S.g.NEAREST_LINEAR_MIPNEAREST;case 9986:return S.g.NEAREST_NEAREST_MIPLINEAR;case 9987:return S.g.NEAREST_LINEAR_MIPLINEAR;default:return u.V.Warn(`${a}/minFilter: Invalid value (${d})`),S.g.NEAREST_NEAREST_MIPNEAREST}}static _GetTypedArrayConstructor(a,b){try{return(0,aM.w)(b)}catch(b){throw Error(`${a}: ${b.message}`)}}static _GetTypedArray(a,b,c,d,e){let f=c.buffer;d=c.byteOffset+(d||0);let g=aP._GetTypedArrayConstructor(`${a}/componentType`,b),h=U.R.GetTypeByteLength(b);return d%h!=0?(u.V.Warn(`${a}: Copying buffer as byte offset (${d}) is not a multiple of component type byte length (${h})`),new g(f.slice(d,d+e*h),0)):new g(f,d,e)}static _GetNumComponents(a,b){switch(b){case"SCALAR":return 1;case"VEC2":return 2;case"VEC3":return 3;case"VEC4":case"MAT2":return 4;case"MAT3":return 9;case"MAT4":return 16}throw Error(`${a}: Invalid type (${b})`)}static _ValidateUri(a){return r.S0.IsBase64(a)||-1===a.indexOf("..")}static _GetDrawMode(a,b){switch(void 0==b&&(b=4),b){case 0:return O.i.PointListDrawMode;case 1:return O.i.LineListDrawMode;case 2:return O.i.LineLoopDrawMode;case 3:return O.i.LineStripDrawMode;case 4:return O.i.TriangleFillMode;case 5:return O.i.TriangleStripDrawMode;case 6:return O.i.TriangleFanDrawMode}throw Error(`${a}: Invalid mesh primitive mode (${b})`)}_compileMaterialsAsync(){this._parent._startPerformanceCounter("Compile materials");let a=[];if(this._gltf.materials){for(let b of this._gltf.materials)if(b._data)for(let c in b._data){let d=b._data[c];for(let b of d.babylonMeshes){b.computeWorldMatrix(!0);let c=d.babylonMaterial;a.push(c.forceCompilationAsync(b)),a.push(c.forceCompilationAsync(b,{useInstances:!0})),this._parent.useClipPlane&&(a.push(c.forceCompilationAsync(b,{clipPlane:!0})),a.push(c.forceCompilationAsync(b,{clipPlane:!0,useInstances:!0})))}}}return Promise.all(a).then(()=>{this._parent._endPerformanceCounter("Compile materials")})}_compileShadowGeneratorsAsync(){this._parent._startPerformanceCounter("Compile shadow generators");let a=[];for(let b of this._babylonScene.lights){let c=b.getShadowGenerator();c&&a.push(c.forceCompilationAsync())}return Promise.all(a).then(()=>{this._parent._endPerformanceCounter("Compile shadow generators")})}_forEachExtensions(a){for(let b of this._extensions)b.enabled&&a(b)}_applyExtensions(a,b,c){for(let d of this._extensions)if(d.enabled){let e=`${d.name}.${b}`;a._activeLoaderExtensionFunctions=a._activeLoaderExtensionFunctions||{};let f=a._activeLoaderExtensionFunctions;if(!f[e]){f[e]=!0;try{let a=c(d);if(a)return a}finally{delete f[e]}}}return null}_extensionsOnLoading(){this._forEachExtensions(a=>a.onLoading&&a.onLoading())}_extensionsOnReady(){this._forEachExtensions(a=>a.onReady&&a.onReady())}_extensionsLoadSceneAsync(a,b){return this._applyExtensions(b,"loadScene",c=>c.loadSceneAsync&&c.loadSceneAsync(a,b))}_extensionsLoadNodeAsync(a,b,c){return this._applyExtensions(b,"loadNode",d=>d.loadNodeAsync&&d.loadNodeAsync(a,b,c))}_extensionsLoadCameraAsync(a,b,c){return this._applyExtensions(b,"loadCamera",d=>d.loadCameraAsync&&d.loadCameraAsync(a,b,c))}_extensionsLoadVertexDataAsync(a,b,c){return this._applyExtensions(b,"loadVertexData",d=>d._loadVertexDataAsync&&d._loadVertexDataAsync(a,b,c))}_extensionsLoadMeshPrimitiveAsync(a,b,c,d,e,f){return this._applyExtensions(e,"loadMeshPrimitive",g=>g._loadMeshPrimitiveAsync&&g._loadMeshPrimitiveAsync(a,b,c,d,e,f))}_extensionsLoadMaterialAsync(a,b,c,d,e){return this._applyExtensions(b,"loadMaterial",f=>f._loadMaterialAsync&&f._loadMaterialAsync(a,b,c,d,e))}_extensionsCreateMaterial(a,b,c){return this._applyExtensions(b,"createMaterial",d=>d.createMaterial&&d.createMaterial(a,b,c))}_extensionsLoadMaterialPropertiesAsync(a,b,c){return this._applyExtensions(b,"loadMaterialProperties",d=>d.loadMaterialPropertiesAsync&&d.loadMaterialPropertiesAsync(a,b,c))}_extensionsLoadTextureInfoAsync(a,b,c){return this._applyExtensions(b,"loadTextureInfo",d=>d.loadTextureInfoAsync&&d.loadTextureInfoAsync(a,b,c))}_extensionsLoadTextureAsync(a,b,c){return this._applyExtensions(b,"loadTexture",d=>d._loadTextureAsync&&d._loadTextureAsync(a,b,c))}_extensionsLoadAnimationAsync(a,b){return this._applyExtensions(b,"loadAnimation",c=>c.loadAnimationAsync&&c.loadAnimationAsync(a,b))}_extensionsLoadAnimationChannelAsync(a,b,c,d,e){return this._applyExtensions(c,"loadAnimationChannel",f=>f._loadAnimationChannelAsync&&f._loadAnimationChannelAsync(a,b,c,d,e))}_extensionsLoadSkinAsync(a,b,c){return this._applyExtensions(c,"loadSkin",d=>d._loadSkinAsync&&d._loadSkinAsync(a,b,c))}_extensionsLoadUriAsync(a,b,c){return this._applyExtensions(b,"loadUri",d=>d._loadUriAsync&&d._loadUriAsync(a,b,c))}_extensionsLoadBufferViewAsync(a,b){return this._applyExtensions(b,"loadBufferView",c=>c.loadBufferViewAsync&&c.loadBufferViewAsync(a,b))}_extensionsLoadBufferAsync(a,b,c,d){return this._applyExtensions(b,"loadBuffer",e=>e.loadBufferAsync&&e.loadBufferAsync(a,b,c,d))}static LoadExtensionAsync(a,b,c,d){if(!b.extensions)return null;let e=b.extensions[c];return e?d(`${a}/extensions/${c}`,e):null}static LoadExtraAsync(a,b,c,d){if(!b.extras)return null;let e=b.extras[c];return e?d(`${a}/extras/${c}`,e):null}isExtensionUsed(a){return!!this._gltf.extensionsUsed&&-1!==this._gltf.extensionsUsed.indexOf(a)}logOpen(a){this._parent._logOpen(a)}logClose(){this._parent._logClose()}log(a){this._parent._log(a)}startPerformanceCounter(a){this._parent._startPerformanceCounter(a)}endPerformanceCounter(a){this._parent._endPerformanceCounter(a)}}aP.DefaultSampler={index:-1},F._CreateGLTF2Loader=a=>new aP(a);var aQ=c(22992),aR=c(14673),aS=c(94942);let aT="EXT_lights_image_based";class aU{constructor(a){this.name=aT,this._loader=a,this.enabled=this._loader.isExtensionUsed(aT)}dispose(){this._loader=null,delete this._lights}onLoading(){let a=this._loader.gltf.extensions;if(a&&a[this.name]){let b=a[this.name];this._lights=b.lights}}loadSceneAsync(a,b){return aP.LoadExtensionAsync(a,b,this.name,async(c,d)=>{this._loader._allMaterialsDirtyRequired=!0;let e=[];e.push(this._loader.loadSceneAsync(a,b)),this._loader.logOpen(`${c}`);let f=aN.Get(`${c}/light`,this._lights,d.light);return e.push(this._loadLightAsync(`/extensions/${this.name}/lights/${d.light}`,f).then(a=>{this._loader.babylonScene.environmentTexture=a})),this._loader.logClose(),await Promise.all(e).then(()=>{})})}_loadLightAsync(a,b){if(!b._loaded){let c=[];this._loader.logOpen(`${a}`);let d=Array(b.specularImages.length);for(let e=0;e<b.specularImages.length;e++){let f=b.specularImages[e];d[e]=Array(f.length);for(let b=0;b<f.length;b++){let g=`${a}/specularImages/${e}/${b}`;this._loader.logOpen(`${g}`);let h=f[b],i=aN.Get(g,this._loader.gltf.images,h);c.push(this._loader.loadImageAsync(`/images/${h}`,i).then(a=>{d[e][b]=a})),this._loader.logClose()}}this._loader.logClose(),b._loaded=Promise.all(c).then(async()=>{let c=new aS.p(this._loader.babylonScene,null,b.specularImageSize);if(c.name=b.name||"environment",b._babylonTexture=c,void 0!=b.intensity&&(c.level=b.intensity),b.rotation){let a=G.PT.FromArray(b.rotation);this._loader.babylonScene.useRightHandedSystem||(a=G.PT.Inverse(a)),G.uq.FromQuaternionToRef(a,c.getReflectionTextureMatrix())}if(!b.irradianceCoefficients)throw Error(`${a}: Irradiance coefficients are missing`);let e=aR.O.FromArray(b.irradianceCoefficients);e.scaleInPlace(b.intensity),e.convertIrradianceToLambertianRadiance();let f=aR.Q.FromHarmonics(e),g=(d.length-1)/Math.log2(b.specularImageSize);return await c.updateRGBDAsync(d,f,g)})}return b._loaded.then(()=>b._babylonTexture)}}aJ(aT),aI(aT,!0,a=>new aU(a)),c(52299);let aV="EXT_mesh_gpu_instancing";class aW{constructor(a){this.name=aV,this._loader=a,this.enabled=this._loader.isExtensionUsed(aV)}dispose(){this._loader=null}loadNodeAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(a,d)=>{this._loader._disableInstancedMesh++;let e=this._loader.loadNodeAsync(`/nodes/${b.index}`,b,c);if(this._loader._disableInstancedMesh--,!b._primitiveBabylonMeshes)return await e;let f=[],g=0,h=b=>{if(void 0==d.attributes[b])return void f.push(Promise.resolve(null));let c=aN.Get(`${a}/attributes/${b}`,this._loader.gltf.accessors,d.attributes[b]);if(f.push(this._loader._loadFloatAccessorAsync(`/accessors/${c.bufferView}`,c)),0===g)g=c.count;else if(g!==c.count)throw Error(`${a}/attributes: Instance buffer accessors do not have the same count.`)};return h("TRANSLATION"),h("ROTATION"),h("SCALE"),await e.then(async a=>{let[c,d,e]=await Promise.all(f),h=new Float32Array(16*g);G.AA.Vector3[0].copyFromFloats(0,0,0),G.AA.Quaternion[0].copyFromFloats(0,0,0,1),G.AA.Vector3[1].copyFromFloats(1,1,1);for(let a=0;a<g;++a)c&&G.Pq.FromArrayToRef(c,3*a,G.AA.Vector3[0]),d&&G.PT.FromArrayToRef(d,4*a,G.AA.Quaternion[0]),e&&G.Pq.FromArrayToRef(e,3*a,G.AA.Vector3[1]),G.uq.ComposeToRef(G.AA.Vector3[1],G.AA.Quaternion[0],G.AA.Vector3[0],G.AA.Matrix[0]),G.AA.Matrix[0].copyToArray(h,16*a);for(let a of b._primitiveBabylonMeshes)a.thinInstanceSetBuffer("matrix",h,16,!0);return a})})}}aJ(aV),aI(aV,!0,a=>new aW(a));var aX=c(51645);let aY="EXT_meshopt_compression";class aZ{constructor(a){this.name=aY,this.enabled=a.isExtensionUsed(aY),this._loader=a}dispose(){this._loader=null}loadBufferViewAsync(a,b){return aP.LoadExtensionAsync(a,b,this.name,async(c,d)=>{if(b._meshOptData)return await b._meshOptData;let e=aN.Get(`${a}/buffer`,this._loader.gltf.buffers,d.buffer);return b._meshOptData=this._loader.loadBufferAsync(`/buffers/${e.index}`,e,d.byteOffset||0,d.byteLength).then(async a=>await aX.v.Default.decodeGltfBufferAsync(a,d.count,d.byteStride,d.mode,d.filter)),await b._meshOptData})}}aJ(aY),aI(aY,!0,a=>new aZ(a));let a$="EXT_texture_webp";class a_{constructor(a){this.name=a$,this._loader=a,this.enabled=a.isExtensionUsed(a$)}dispose(){this._loader=null}_loadTextureAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=void 0==b.sampler?aP.DefaultSampler:aN.Get(`${a}/sampler`,this._loader.gltf.samplers,b.sampler),g=aN.Get(`${d}/source`,this._loader.gltf.images,e.source);return await this._loader._createTextureAsync(a,f,g,a=>{c(a)},void 0,!b._textureInfo.nonColorData)})}}aJ(a$),aI(a$,!0,a=>new a_(a));let a0="EXT_texture_avif";class a1{constructor(a){this.name=a0,this._loader=a,this.enabled=a.isExtensionUsed(a0)}dispose(){this._loader=null}_loadTextureAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=void 0==b.sampler?aP.DefaultSampler:aN.Get(`${a}/sampler`,this._loader.gltf.samplers,b.sampler),g=aN.Get(`${d}/source`,this._loader.gltf.images,e.source);return await this._loader._createTextureAsync(a,f,g,a=>{c(a)},void 0,!b._textureInfo.nonColorData)})}}aJ(a0),aI(a0,!0,a=>new a1(a));var a2=c(87180);let a3="EXT_lights_ies";class a4{constructor(a){this.name=a3,this._loader=a,this.enabled=this._loader.isExtensionUsed(a3)}dispose(){this._loader=null,delete this._lights}onLoading(){let a=this._loader.gltf.extensions;if(a&&a[this.name]){let b=a[this.name];this._lights=b.lights,aN.Assign(this._lights)}}loadNodeAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f,g,h;this._loader._allMaterialsDirtyRequired=!0;let i=await this._loader.loadNodeAsync(a,b,a=>{let b=(g=aN.Get(d,this._lights,e.light)).name||a.name;this._loader.babylonScene._blockEntityCollection=!!this._loader._assetContainer,(f=new aa.n(b,G.Pq.Zero(),G.Pq.Backward(),0,1,this._loader.babylonScene)).angle=Math.PI/2,f.innerAngle=0,f._parentContainer=this._loader._assetContainer,this._loader.babylonScene._blockEntityCollection=!1,g._babylonLight=f,f.falloffType=a2.v.FALLOFF_GLTF,f.diffuse=e.color?H.v9.FromArray(e.color):H.v9.White(),f.intensity=e.multiplier||1,f.range=Number.MAX_VALUE,f.parent=a,this._loader._babylonLights.push(f),aP.AddPointerMetadata(f,d),c(a)});if(g.uri)h=await this._loader.loadUriAsync(a,g,g.uri);else{let b=aN.Get(`${a}/bufferView`,this._loader.gltf.bufferViews,g.bufferView);h=await this._loader.loadBufferViewAsync(`/bufferViews/${b.index}`,b)}return f.iesProfileTexture=new S.g(name+"_iesProfile",this._loader.babylonScene,!0,!1,void 0,null,null,h,!0,void 0,void 0,void 0,void 0,".ies"),i})}}aJ(a3),aI(a3,!0,a=>new a4(a));var a5=c(28068);let a6="KHR_draco_mesh_compression";class a7{constructor(a){this.name=a6,this.useNormalizedFlagFromAccessor=!0,this._loader=a,this.enabled=a5.Y.DefaultAvailable&&this._loader.isExtensionUsed(a6)}dispose(){delete this.dracoDecoder,this._loader=null}_loadVertexDataAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{if(void 0!=b.mode&&4!==b.mode&&5!==b.mode)throw Error(`${a}: Unsupported mode ${b.mode}`);let f={},g={},h=(a,d)=>{let h=e.attributes[a];if(void 0!=h&&(c._delayInfo=c._delayInfo||[],-1===c._delayInfo.indexOf(d)&&c._delayInfo.push(d),f[d]=h,this.useNormalizedFlagFromAccessor)){let c=aN.TryGet(this._loader.gltf.accessors,b.attributes[a]);c&&(g[d]=c.normalized||!1)}};h("POSITION",U.R.PositionKind),h("NORMAL",U.R.NormalKind),h("TANGENT",U.R.TangentKind),h("TEXCOORD_0",U.R.UVKind),h("TEXCOORD_1",U.R.UV2Kind),h("TEXCOORD_2",U.R.UV3Kind),h("TEXCOORD_3",U.R.UV4Kind),h("TEXCOORD_4",U.R.UV5Kind),h("TEXCOORD_5",U.R.UV6Kind),h("JOINTS_0",U.R.MatricesIndicesKind),h("WEIGHTS_0",U.R.MatricesWeightsKind),h("COLOR_0",U.R.ColorKind);let i=aN.Get(d,this._loader.gltf.bufferViews,e.bufferView);return i._dracoBabylonGeometry||(i._dracoBabylonGeometry=this._loader.loadBufferViewAsync(`/bufferViews/${i.index}`,i).then(async d=>{let e=this.dracoDecoder||a5.Y.Default,h=aN.TryGet(this._loader.gltf.accessors,b.attributes.POSITION),i=this._loader.parent.alwaysComputeBoundingBox||c.skeleton||!h?null:aO(h);return await e._decodeMeshToGeometryForGltfAsync(c.name,this._loader.babylonScene,d,f,g,i).catch(b=>{throw Error(`${a}: ${b.message}`)})})),await i._dracoBabylonGeometry})}}aJ(a6),aI(a6,!0,a=>new a7(a));let a8="KHR_lights_punctual";class a9{constructor(a){this.name=a8,this._loader=a,this.enabled=this._loader.isExtensionUsed(a8)}dispose(){this._loader=null,delete this._lights}onLoading(){let a=this._loader.gltf.extensions;if(a&&a[this.name]){let b=a[this.name];this._lights=b.lights,aN.Assign(this._lights)}}loadNodeAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>(this._loader._allMaterialsDirtyRequired=!0,await this._loader.loadNodeAsync(a,b,a=>{let b,f=aN.Get(d,this._lights,e.light),g=f.name||a.name;switch(this._loader.babylonScene._blockEntityCollection=!!this._loader._assetContainer,f.type){case"directional":{let a=new $.Z(g,G.Pq.Backward(),this._loader.babylonScene);a.position.setAll(0),b=a;break}case"point":b=new _.H(g,G.Pq.Zero(),this._loader.babylonScene);break;case"spot":{let a=new aa.n(g,G.Pq.Zero(),G.Pq.Backward(),0,1,this._loader.babylonScene);a.angle=2*(f.spot&&f.spot.outerConeAngle||Math.PI/4),a.innerAngle=2*(f.spot&&f.spot.innerConeAngle||0),b=a;break}default:throw this._loader.babylonScene._blockEntityCollection=!1,Error(`${d}: Invalid light type (${f.type})`)}b._parentContainer=this._loader._assetContainer,this._loader.babylonScene._blockEntityCollection=!1,f._babylonLight=b,b.falloffType=a2.v.FALLOFF_GLTF,b.diffuse=f.color?H.v9.FromArray(f.color):H.v9.White(),b.intensity=void 0==f.intensity?1:f.intensity,b.range=void 0==f.range?Number.MAX_VALUE:f.range,b.parent=a,this._loader._babylonLights.push(b),aP.AddPointerMetadata(b,d),c(a)})))}}aJ(a8),aI(a8,!0,a=>new a9(a));let ba="KHR_materials_pbrSpecularGlossiness";class bb{constructor(a){this.name=ba,this.order=200,this._loader=a,this.enabled=this._loader.isExtensionUsed(ba)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialBasePropertiesAsync(a,b,c)),f.push(this._loadSpecularGlossinessPropertiesAsync(d,e,c)),this._loader.loadMaterialAlphaProperties(a,b,c),await Promise.all(f).then(()=>{})})}_loadSpecularGlossinessPropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];return c.metallic=null,c.roughness=null,b.diffuseFactor?(c.albedoColor=H.v9.FromArray(b.diffuseFactor),c.alpha=b.diffuseFactor[3]):c.albedoColor=H.v9.White(),c.reflectivityColor=b.specularFactor?H.v9.FromArray(b.specularFactor):H.v9.White(),c.microSurface=void 0==b.glossinessFactor?1:b.glossinessFactor,b.diffuseTexture&&d.push(this._loader.loadTextureInfoAsync(`${a}/diffuseTexture`,b.diffuseTexture,a=>{a.name=`${c.name} (Diffuse)`,c.albedoTexture=a})),b.specularGlossinessTexture&&(d.push(this._loader.loadTextureInfoAsync(`${a}/specularGlossinessTexture`,b.specularGlossinessTexture,a=>{a.name=`${c.name} (Specular Glossiness)`,c.reflectivityTexture=a,c.reflectivityTexture.hasAlpha=!0})),c.useMicroSurfaceFromReflectivityMapAlpha=!0),Promise.all(d).then(()=>{})}}aJ(ba),aI(ba,!0,a=>new bb(a));let bc="KHR_materials_unlit";class bd{constructor(a){this.name=bc,this.order=210,this._loader=a,this.enabled=this._loader.isExtensionUsed(bc)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async()=>await this._loadUnlitPropertiesAsync(a,b,c))}_loadUnlitPropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];c.unlit=!0;let e=b.pbrMetallicRoughness;return e&&(e.baseColorFactor?(c.albedoColor=H.v9.FromArray(e.baseColorFactor),c.alpha=e.baseColorFactor[3]):c.albedoColor=H.v9.White(),e.baseColorTexture&&d.push(this._loader.loadTextureInfoAsync(`${a}/baseColorTexture`,e.baseColorTexture,a=>{a.name=`${c.name} (Base Color)`,c.albedoTexture=a}))),b.doubleSided&&(c.backFaceCulling=!1,c.twoSidedLighting=!0),this._loader.loadMaterialAlphaProperties(a,b,c),Promise.all(d).then(()=>{})}}aJ(bc),aI(bc,!0,a=>new bd(a));let be="KHR_materials_clearcoat";class bf{constructor(a){this.name=be,this.order=190,this._loader=a,this.enabled=this._loader.isExtensionUsed(be)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadClearCoatPropertiesAsync(d,e,c)),await Promise.all(f)})}_loadClearCoatPropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];return c.clearCoat.isEnabled=!0,c.clearCoat.useRoughnessFromMainTexture=!1,c.clearCoat.remapF0OnInterfaceChange=!1,void 0!=b.clearcoatFactor?c.clearCoat.intensity=b.clearcoatFactor:c.clearCoat.intensity=0,b.clearcoatTexture&&d.push(this._loader.loadTextureInfoAsync(`${a}/clearcoatTexture`,b.clearcoatTexture,a=>{a.name=`${c.name} (ClearCoat)`,c.clearCoat.texture=a})),void 0!=b.clearcoatRoughnessFactor?c.clearCoat.roughness=b.clearcoatRoughnessFactor:c.clearCoat.roughness=0,b.clearcoatRoughnessTexture&&(b.clearcoatRoughnessTexture.nonColorData=!0,d.push(this._loader.loadTextureInfoAsync(`${a}/clearcoatRoughnessTexture`,b.clearcoatRoughnessTexture,a=>{a.name=`${c.name} (ClearCoat Roughness)`,c.clearCoat.textureRoughness=a}))),b.clearcoatNormalTexture&&(b.clearcoatNormalTexture.nonColorData=!0,d.push(this._loader.loadTextureInfoAsync(`${a}/clearcoatNormalTexture`,b.clearcoatNormalTexture,a=>{a.name=`${c.name} (ClearCoat Normal)`,c.clearCoat.bumpTexture=a})),c.invertNormalMapX=!c.getScene().useRightHandedSystem,c.invertNormalMapY=c.getScene().useRightHandedSystem,void 0!=b.clearcoatNormalTexture.scale&&(c.clearCoat.bumpTexture.level=b.clearcoatNormalTexture.scale)),Promise.all(d).then(()=>{})}}aJ(be),aI(be,!0,a=>new bf(a));let bg="KHR_materials_iridescence";class bh{constructor(a){this.name=bg,this.order=195,this._loader=a,this.enabled=this._loader.isExtensionUsed(bg)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadIridescencePropertiesAsync(d,e,c)),await Promise.all(f).then(()=>{})})}_loadIridescencePropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];return c.iridescence.isEnabled=!0,c.iridescence.intensity=b.iridescenceFactor??0,c.iridescence.indexOfRefraction=b.iridescenceIor??b.iridescenceIOR??1.3,c.iridescence.minimumThickness=b.iridescenceThicknessMinimum??100,c.iridescence.maximumThickness=b.iridescenceThicknessMaximum??400,b.iridescenceTexture&&d.push(this._loader.loadTextureInfoAsync(`${a}/iridescenceTexture`,b.iridescenceTexture,a=>{a.name=`${c.name} (Iridescence)`,c.iridescence.texture=a})),b.iridescenceThicknessTexture&&d.push(this._loader.loadTextureInfoAsync(`${a}/iridescenceThicknessTexture`,b.iridescenceThicknessTexture,a=>{a.name=`${c.name} (Iridescence Thickness)`,c.iridescence.thicknessTexture=a})),Promise.all(d).then(()=>{})}}aJ(bg),aI(bg,!0,a=>new bh(a));let bi="KHR_materials_anisotropy";class bj{constructor(a){this.name=bi,this.order=195,this._loader=a,this.enabled=this._loader.isExtensionUsed(bi)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadIridescencePropertiesAsync(d,e,c)),await Promise.all(f)})}async _loadIridescencePropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];c.anisotropy.isEnabled=!0,c.anisotropy.intensity=b.anisotropyStrength??0,c.anisotropy.angle=b.anisotropyRotation??0,b.anisotropyTexture&&(b.anisotropyTexture.nonColorData=!0,d.push(this._loader.loadTextureInfoAsync(`${a}/anisotropyTexture`,b.anisotropyTexture,a=>{a.name=`${c.name} (Anisotropy Intensity)`,c.anisotropy.texture=a}))),await Promise.all(d)}}aJ(bi),aI(bi,!0,a=>new bj(a));let bk="KHR_materials_emissive_strength";class bl{constructor(a){this.name=bk,this.order=170,this._loader=a,this.enabled=this._loader.isExtensionUsed(bk)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>await this._loader.loadMaterialPropertiesAsync(a,b,c).then(()=>{this._loadEmissiveProperties(d,e,c)}))}_loadEmissiveProperties(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);void 0!==b.emissiveStrength&&(c.emissiveIntensity=b.emissiveStrength)}}aJ(bk),aI(bk,!0,a=>new bl(a));let bm="KHR_materials_sheen";class bn{constructor(a){this.name=bm,this.order=190,this._loader=a,this.enabled=this._loader.isExtensionUsed(bm)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadSheenPropertiesAsync(d,e,c)),await Promise.all(f).then(()=>{})})}_loadSheenPropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];return c.sheen.isEnabled=!0,c.sheen.intensity=1,void 0!=b.sheenColorFactor?c.sheen.color=H.v9.FromArray(b.sheenColorFactor):c.sheen.color=H.v9.Black(),b.sheenColorTexture&&d.push(this._loader.loadTextureInfoAsync(`${a}/sheenColorTexture`,b.sheenColorTexture,a=>{a.name=`${c.name} (Sheen Color)`,c.sheen.texture=a})),void 0!==b.sheenRoughnessFactor?c.sheen.roughness=b.sheenRoughnessFactor:c.sheen.roughness=0,b.sheenRoughnessTexture&&(b.sheenRoughnessTexture.nonColorData=!0,d.push(this._loader.loadTextureInfoAsync(`${a}/sheenRoughnessTexture`,b.sheenRoughnessTexture,a=>{a.name=`${c.name} (Sheen Roughness)`,c.sheen.textureRoughness=a}))),c.sheen.albedoScaling=!0,c.sheen.useRoughnessFromMainTexture=!1,Promise.all(d).then(()=>{})}}aJ(bm),aI(bm,!0,a=>new bn(a));let bo="KHR_materials_specular";class bp{constructor(a){this.name=bo,this.order=190,this._loader=a,this.enabled=this._loader.isExtensionUsed(bo)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadSpecularPropertiesAsync(d,e,c)),e.extensions&&e.extensions.EXT_materials_specular_edge_color&&c instanceof aC.Y&&e.extensions.EXT_materials_specular_edge_color.specularEdgeColorEnabled&&(c.brdf.dielectricSpecularModel=ac.Y.MATERIAL_DIELECTRIC_SPECULAR_MODEL_OPENPBR,c.brdf.conductorSpecularModel=ac.Y.MATERIAL_CONDUCTOR_SPECULAR_MODEL_OPENPBR),await Promise.all(f).then(()=>{})})}_loadSpecularPropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];return void 0!==b.specularFactor&&(c.metallicF0Factor=b.specularFactor),void 0!==b.specularColorFactor&&(c.metallicReflectanceColor=H.v9.FromArray(b.specularColorFactor)),b.specularTexture&&(b.specularTexture.nonColorData=!0,d.push(this._loader.loadTextureInfoAsync(`${a}/specularTexture`,b.specularTexture,a=>{a.name=`${c.name} (Specular)`,c.metallicReflectanceTexture=a,c.useOnlyMetallicFromMetallicReflectanceTexture=!0}))),b.specularColorTexture&&d.push(this._loader.loadTextureInfoAsync(`${a}/specularColorTexture`,b.specularColorTexture,a=>{a.name=`${c.name} (Specular Color)`,c.reflectanceTexture=a})),Promise.all(d).then(()=>{})}}aJ(bo),aI(bo,!0,a=>new bp(a));let bq="KHR_materials_ior";class br{constructor(a){this.name=bq,this.order=180,this._loader=a,this.enabled=this._loader.isExtensionUsed(bq)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadIorPropertiesAsync(d,e,c)),await Promise.all(f).then(()=>{})})}_loadIorPropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);return void 0!==b.ior?c.indexOfRefraction=b.ior:c.indexOfRefraction=br._DEFAULT_IOR,Promise.resolve()}}br._DEFAULT_IOR=1.5,aJ(bq),aI(bq,!0,a=>new br(a));let bs="KHR_materials_variants";class bt{constructor(a){this.name=bs,this._loader=a,this.enabled=this._loader.isExtensionUsed(bs)}dispose(){this._loader=null}static GetAvailableVariants(a){let b=this._GetExtensionMetadata(a);return b?Object.keys(b.variants):[]}getAvailableVariants(a){return bt.GetAvailableVariants(a)}static SelectVariant(a,b){let c=this._GetExtensionMetadata(a);if(!c)throw Error(`Cannot select variant on a glTF mesh that does not have the ${bs} extension`);let d=a=>{let b=c.variants[a];if(b)for(let a of b)a.mesh.material=a.material};if(b instanceof Array)for(let a of b)d(a);else d(b);c.lastSelected=b}selectVariant(a,b){bt.SelectVariant(a,b)}static Reset(a){let b=this._GetExtensionMetadata(a);if(!b)throw Error(`Cannot reset on a glTF mesh that does not have the ${bs} extension`);for(let a of b.original)a.mesh.material=a.material;b.lastSelected=null}reset(a){bt.Reset(a)}static GetLastSelectedVariant(a){let b=this._GetExtensionMetadata(a);if(!b)throw Error(`Cannot get the last selected variant on a glTF mesh that does not have the ${bs} extension`);return b.lastSelected}getLastSelectedVariant(a){return bt.GetLastSelectedVariant(a)}static _GetExtensionMetadata(a){return a?._internalMetadata?.gltf?.[bs]||null}onLoading(){let a=this._loader.gltf.extensions;if(a&&a[this.name]){let b=a[this.name];this._variants=b.variants}}onReady(){let a=this._loader.rootBabylonMesh;if(a){let b=this._loader.parent.extensionOptions[bs];b?.defaultVariant&&bt.SelectVariant(a,b.defaultVariant),b?.onLoaded?.({get variants(){return bt.GetAvailableVariants(a)},get selectedVariant(){let b=bt.GetLastSelectedVariant(a);if(!b)return bt.GetAvailableVariants(a)[0];if(Array.isArray(b))return b[0];return b},set selectedVariant(variantName){bt.SelectVariant(a,variantName)}})}}_loadMeshPrimitiveAsync(a,b,c,d,e,f){return aP.LoadExtensionAsync(a,e,this.name,async(g,h)=>{let i=[];return i.push(this._loader._loadMeshPrimitiveAsync(a,b,c,d,e,b=>{if(f(b),b instanceof Y.e){let c=aP._GetDrawMode(a,e.mode),d=this._loader.rootBabylonMesh,f=d?d._internalMetadata=d._internalMetadata||{}:{},j=f.gltf=f.gltf||{},k=j[bs]=j[bs]||{lastSelected:null,original:[],variants:{}};k.original.push({mesh:b,material:b.material});for(let a=0;a<h.mappings.length;++a){let e=h.mappings[a],f=aN.Get(`${g}/mappings/${a}/material`,this._loader.gltf.materials,e.material);i.push(this._loader._loadMaterialAsync(`#/materials/${e.material}`,f,b,c,a=>{for(let c=0;c<e.variants.length;++c){let f=e.variants[c],g=aN.Get(`/extensions/${bs}/variants/${f}`,this._variants,f);k.variants[g.name]=k.variants[g.name]||[],k.variants[g.name].push({mesh:b,material:a}),b.onClonedObservable.add(a=>{let c=null,e=a;do{if(!(e=e.parent))return;c=bt._GetExtensionMetadata(e)}while(null===c);if(d&&c===bt._GetExtensionMetadata(d)){for(let a in e._internalMetadata={},d._internalMetadata)e._internalMetadata[a]=d._internalMetadata[a];for(let a in e._internalMetadata.gltf=[],d._internalMetadata.gltf)e._internalMetadata.gltf[a]=d._internalMetadata.gltf[a];for(let a of(e._internalMetadata.gltf[bs]={lastSelected:null,original:[],variants:{}},c.original))e._internalMetadata.gltf[bs].original.push({mesh:a.mesh,material:a.material});for(let a in c.variants)if(Object.prototype.hasOwnProperty.call(c.variants,a))for(let b of(e._internalMetadata.gltf[bs].variants[a]=[],c.variants[a]))e._internalMetadata.gltf[bs].variants[a].push({mesh:b.mesh,material:b.material});c=e._internalMetadata.gltf[bs]}for(let d of c.original)d.mesh===b&&(d.mesh=a);for(let d of c.variants[g.name])d.mesh===b&&(d.mesh=a)})}}))}}})),await Promise.all(i).then(([a])=>a)})}}aJ(bs),aI(bs,!0,a=>new bt(a));var bu=c(75810);class bv{static _GetDefaultOptions(){return{renderSize:1024,samples:4,lodGenerationScale:1,lodGenerationOffset:-4,renderTargetTextureType:ac.Y.TEXTURETYPE_HALF_FLOAT,generateMipmaps:!0}}constructor(a,b){this._opaqueRenderTarget=null,this._opaqueMeshesCache=[],this._transparentMeshesCache=[],this._materialObservers={},this._options={...bv._GetDefaultOptions(),...a},this._scene=b,this._scene._transmissionHelper=this,this.onErrorObservable=new q.cP,this._scene.onDisposeObservable.addOnce(()=>{this.dispose()}),this._parseScene(),this._setupRenderTargets()}updateOptions(a){if(!Object.keys(a).filter(b=>this._options[b]!==a[b]).length)return;let b={...this._options,...a},c=this._options;this._options=b,b.renderSize===c.renderSize&&b.renderTargetTextureType===c.renderTargetTextureType&&b.generateMipmaps===c.generateMipmaps&&this._opaqueRenderTarget?(this._opaqueRenderTarget.samples=b.samples,this._opaqueRenderTarget.lodGenerationScale=b.lodGenerationScale,this._opaqueRenderTarget.lodGenerationOffset=b.lodGenerationOffset):this._setupRenderTargets()}getOpaqueTarget(){return this._opaqueRenderTarget}_shouldRenderAsTransmission(a){return!!a&&a instanceof aC.Y&&!!a.subSurface.isRefractionEnabled}_addMesh(a){this._materialObservers[a.uniqueId]=a.onMaterialChangedObservable.add(this._onMeshMaterialChanged.bind(this)),r.S0.SetImmediate(()=>{this._shouldRenderAsTransmission(a.material)?(a.material.refractionTexture=this._opaqueRenderTarget,-1===this._transparentMeshesCache.indexOf(a)&&this._transparentMeshesCache.push(a)):-1===this._opaqueMeshesCache.indexOf(a)&&this._opaqueMeshesCache.push(a)})}_removeMesh(a){a.onMaterialChangedObservable.remove(this._materialObservers[a.uniqueId]),delete this._materialObservers[a.uniqueId];let b=this._transparentMeshesCache.indexOf(a);-1!==b&&this._transparentMeshesCache.splice(b,1),-1!==(b=this._opaqueMeshesCache.indexOf(a))&&this._opaqueMeshesCache.splice(b,1)}_parseScene(){this._scene.meshes.forEach(this._addMesh.bind(this)),this._scene.onNewMeshAddedObservable.add(this._addMesh.bind(this)),this._scene.onMeshRemovedObservable.add(this._removeMesh.bind(this))}_onMeshMaterialChanged(a){let b=this._transparentMeshesCache.indexOf(a),c=this._opaqueMeshesCache.indexOf(a);this._shouldRenderAsTransmission(a.material)?(a.material instanceof aC.Y&&(a.material.subSurface.refractionTexture=this._opaqueRenderTarget),-1!==c?(this._opaqueMeshesCache.splice(c,1),this._transparentMeshesCache.push(a)):-1===b&&this._transparentMeshesCache.push(a)):-1!==b?(this._transparentMeshesCache.splice(b,1),this._opaqueMeshesCache.push(a)):-1===c&&this._opaqueMeshesCache.push(a)}_isRenderTargetValid(){return this._opaqueRenderTarget?.getInternalTexture()!==null}_setupRenderTargets(){let a;for(let b of(this._opaqueRenderTarget&&this._opaqueRenderTarget.dispose(),this._opaqueRenderTarget=new bu.$("opaqueSceneTexture",this._options.renderSize,this._scene,this._options.generateMipmaps,void 0,this._options.renderTargetTextureType),this._opaqueRenderTarget.ignoreCameraViewport=!0,this._opaqueRenderTarget.renderList=this._opaqueMeshesCache,this._opaqueRenderTarget.clearColor=this._options.clearColor?.clone()??this._scene.clearColor.clone(),this._opaqueRenderTarget.gammaSpace=!1,this._opaqueRenderTarget.lodGenerationScale=this._options.lodGenerationScale,this._opaqueRenderTarget.lodGenerationOffset=this._options.lodGenerationOffset,this._opaqueRenderTarget.samples=this._options.samples,this._opaqueRenderTarget.renderSprites=!0,this._opaqueRenderTarget.renderParticles=!0,this._opaqueRenderTarget.disableImageProcessing=!0,this._opaqueRenderTarget.onBeforeBindObservable.add(b=>{a=this._scene.environmentIntensity,this._scene.environmentIntensity=1,this._options.clearColor?b.clearColor.copyFrom(this._options.clearColor):this._scene.clearColor.toLinearSpaceToRef(b.clearColor,this._scene.getEngine().useExactSrgbConversions)}),this._opaqueRenderTarget.onAfterUnbindObservable.add(()=>{this._scene.environmentIntensity=a}),this._transparentMeshesCache))this._shouldRenderAsTransmission(b.material)&&(b.material.refractionTexture=this._opaqueRenderTarget)}dispose(){this._scene._transmissionHelper=void 0,this._opaqueRenderTarget&&(this._opaqueRenderTarget.dispose(),this._opaqueRenderTarget=null),this._transparentMeshesCache=[],this._opaqueMeshesCache=[]}}let bw="KHR_materials_transmission";class bx{constructor(a){this.name=bw,this.order=175,this._loader=a,this.enabled=this._loader.isExtensionUsed(bw),this.enabled&&(a.parent.transparencyAsCoverage=!0)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadTransparentPropertiesAsync(d,b,c,e)),await Promise.all(f).then(()=>{})})}_loadTransparentPropertiesAsync(a,b,c,d){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);if(c.subSurface.isRefractionEnabled=!0,c.subSurface.volumeIndexOfRefraction=1,c.subSurface.useAlbedoToTintRefraction=!0,void 0===d.transmissionFactor)return c.subSurface.refractionIntensity=0,c.subSurface.isRefractionEnabled=!1,Promise.resolve();{c.subSurface.refractionIntensity=d.transmissionFactor;let a=c.getScene();c.subSurface.refractionIntensity&&!a._transmissionHelper?new bv({},c.getScene()):c.subSurface.refractionIntensity&&!a._transmissionHelper?._isRenderTargetValid()&&a._transmissionHelper?._setupRenderTargets()}return(c.subSurface.minimumThickness=0,c.subSurface.maximumThickness=0,d.transmissionTexture)?(d.transmissionTexture.nonColorData=!0,this._loader.loadTextureInfoAsync(`${a}/transmissionTexture`,d.transmissionTexture,void 0).then(a=>{a.name=`${c.name} (Transmission)`,c.subSurface.refractionIntensityTexture=a,c.subSurface.useGltfStyleTextures=!0})):Promise.resolve()}}aJ(bw),aI(bw,!0,a=>new bx(a));let by="KHR_materials_diffuse_transmission";class bz{constructor(a){this.name=by,this.order=174,this._loader=a,this.enabled=this._loader.isExtensionUsed(by),this.enabled&&(a.parent.transparencyAsCoverage=!0)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadTranslucentPropertiesAsync(d,b,c,e)),await Promise.all(f).then(()=>{})})}_loadTranslucentPropertiesAsync(a,b,c,d){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);if(c.subSurface.isTranslucencyEnabled=!0,c.subSurface.volumeIndexOfRefraction=1,c.subSurface.minimumThickness=0,c.subSurface.maximumThickness=0,c.subSurface.useAlbedoToTintTranslucency=!1,void 0===d.diffuseTransmissionFactor)return c.subSurface.translucencyIntensity=0,c.subSurface.isTranslucencyEnabled=!1,Promise.resolve();c.subSurface.translucencyIntensity=d.diffuseTransmissionFactor;let e=[];return c.subSurface.useGltfStyleTextures=!0,d.diffuseTransmissionTexture&&(d.diffuseTransmissionTexture.nonColorData=!0,e.push(this._loader.loadTextureInfoAsync(`${a}/diffuseTransmissionTexture`,d.diffuseTransmissionTexture).then(a=>{a.name=`${c.name} (Diffuse Transmission)`,c.subSurface.translucencyIntensityTexture=a}))),void 0!==d.diffuseTransmissionColorFactor?c.subSurface.translucencyColor=H.v9.FromArray(d.diffuseTransmissionColorFactor):c.subSurface.translucencyColor=H.v9.White(),d.diffuseTransmissionColorTexture&&e.push(this._loader.loadTextureInfoAsync(`${a}/diffuseTransmissionColorTexture`,d.diffuseTransmissionColorTexture).then(a=>{a.name=`${c.name} (Diffuse Transmission Color)`,c.subSurface.translucencyColorTexture=a})),Promise.all(e).then(()=>{})}}aJ(by),aI(by,!0,a=>new bz(a));let bA="KHR_materials_volume";class bB{constructor(a){this.name=bA,this.order=173,this._loader=a,this.enabled=this._loader.isExtensionUsed(bA),this.enabled&&this._loader._disableInstancedMesh++}dispose(){this.enabled&&this._loader._disableInstancedMesh--,this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadVolumePropertiesAsync(d,b,c,e)),await Promise.all(f).then(()=>{})})}_loadVolumePropertiesAsync(a,b,c,d){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);if(!c.subSurface.isRefractionEnabled&&!c.subSurface.isTranslucencyEnabled||!d.thicknessFactor)return Promise.resolve();c.subSurface.volumeIndexOfRefraction=c.indexOfRefraction;let e=void 0!==d.attenuationDistance?d.attenuationDistance:Number.MAX_VALUE;return(c.subSurface.tintColorAtDistance=e,void 0!==d.attenuationColor&&3==d.attenuationColor.length&&c.subSurface.tintColor.copyFromFloats(d.attenuationColor[0],d.attenuationColor[1],d.attenuationColor[2]),c.subSurface.minimumThickness=0,c.subSurface.maximumThickness=d.thicknessFactor,c.subSurface.useThicknessAsDepth=!0,d.thicknessTexture)?(d.thicknessTexture.nonColorData=!0,this._loader.loadTextureInfoAsync(`${a}/thicknessTexture`,d.thicknessTexture).then(a=>{a.name=`${c.name} (Thickness)`,c.subSurface.thicknessTexture=a,c.subSurface.useGltfStyleTextures=!0})):Promise.resolve()}}aJ(bA),aI(bA,!0,a=>new bB(a));let bC="KHR_materials_dispersion";class bD{constructor(a){this.name=bC,this.order=174,this._loader=a,this.enabled=this._loader.isExtensionUsed(bC)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadDispersionPropertiesAsync(d,b,c,e)),await Promise.all(f).then(()=>{})})}_loadDispersionPropertiesAsync(a,b,c,d){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);return c.subSurface.isRefractionEnabled&&d.dispersion&&(c.subSurface.isDispersionEnabled=!0,c.subSurface.dispersion=d.dispersion),Promise.resolve()}}aJ(bC),aI(bC,!0,a=>new bD(a));let bE="EXT_materials_diffuse_roughness";class bF{constructor(a){this.name=bE,this.order=190,this._loader=a,this.enabled=this._loader.isExtensionUsed(bE)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=[];return f.push(this._loader.loadMaterialPropertiesAsync(a,b,c)),f.push(this._loadDiffuseRoughnessPropertiesAsync(d,e,c)),await Promise.all(f).then(()=>{})})}_loadDiffuseRoughnessPropertiesAsync(a,b,c){if(!(c instanceof aC.Y))throw Error(`${a}: Material type not supported`);let d=[];return c.brdf.baseDiffuseModel=ac.Y.MATERIAL_DIFFUSE_MODEL_E_OREN_NAYAR,void 0!=b.diffuseRoughnessFactor?c.baseDiffuseRoughness=b.diffuseRoughnessFactor:c.baseDiffuseRoughness=0,b.diffuseRoughnessTexture&&d.push(this._loader.loadTextureInfoAsync(`${a}/diffuseRoughnessTexture`,b.diffuseRoughnessTexture,a=>{a.name=`${c.name} (Diffuse Roughness)`,c.baseDiffuseRoughnessTexture=a})),Promise.all(d).then(()=>{})}}aJ(bE),aI(bE,!0,a=>new bF(a));let bG="KHR_mesh_quantization";class bH{constructor(a){this.name=bG,this.enabled=a.isExtensionUsed(bG)}dispose(){}}aJ(bG),aI(bG,!0,a=>new bH(a));let bI="KHR_texture_basisu";class bJ{constructor(a){this.name=bI,this._loader=a,this.enabled=a.isExtensionUsed(bI)}dispose(){this._loader=null}_loadTextureAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>{let f=void 0==b.sampler?aP.DefaultSampler:aN.Get(`${a}/sampler`,this._loader.gltf.samplers,b.sampler),g=aN.Get(`${d}/source`,this._loader.gltf.images,e.source);return await this._loader._createTextureAsync(a,f,g,a=>{c(a)},b._textureInfo.nonColorData?{useRGBAIfASTCBC7NotAvailableWhenUASTC:!0}:void 0,!b._textureInfo.nonColorData)})}}aJ(bI),aI(bI,!0,a=>new bJ(a));let bK="KHR_texture_transform";class bL{constructor(a){this.name=bK,this._loader=a,this.enabled=this._loader.isExtensionUsed(bK)}dispose(){this._loader=null}loadTextureInfoAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(d,e)=>await this._loader.loadTextureInfoAsync(a,b,a=>{if(!(a instanceof S.g))throw Error(`${d}: Texture type not supported`);e.offset&&(a.uOffset=e.offset[0],a.vOffset=e.offset[1]),a.uRotationCenter=0,a.vRotationCenter=0,e.rotation&&(a.wAng=-e.rotation),e.scale&&(a.uScale=e.scale[0],a.vScale=e.scale[1]),void 0!=e.texCoord&&(a.coordinatesIndex=e.texCoord),c(a)}))}}aJ(bK),aI(bK,!0,a=>new bL(a));let bM="KHR_xmp_json_ld";class bN{constructor(a){this.name=bM,this.order=100,this._loader=a,this.enabled=this._loader.isExtensionUsed(bM)}dispose(){this._loader=null}onLoading(){if(null===this._loader.rootBabylonMesh)return;let a=this._loader.gltf.extensions?.KHR_xmp_json_ld,b=this._loader.gltf.asset?.extensions?.KHR_xmp_json_ld;if(a&&b){let c=+b.packet;a.packets&&c<a.packets.length&&(this._loader.rootBabylonMesh.metadata=this._loader.rootBabylonMesh.metadata||{},this._loader.rootBabylonMesh.metadata.xmp=a.packets[c])}}}function bO(a,b,c,d){return H.v9.FromArray(b,c).scale(d)}function bP(a,b,c,d){return b[c]*d}function bQ(a,b,c,d){return-b[c]*d}function bR(a,b,c,d){return b[c+1]*d}function bS(a,b,c,d){return b[c]*d*2}function bT(a){return{scale:[new bV(K.X5.ANIMATIONTYPE_FLOAT,`${a}.uScale`,bP,()=>2),new bV(K.X5.ANIMATIONTYPE_FLOAT,`${a}.vScale`,bR,()=>2)],offset:[new bV(K.X5.ANIMATIONTYPE_FLOAT,`${a}.uOffset`,bP,()=>2),new bV(K.X5.ANIMATIONTYPE_FLOAT,`${a}.vOffset`,bR,()=>2)],rotation:[new bV(K.X5.ANIMATIONTYPE_FLOAT,`${a}.wAng`,bQ,()=>1)]}}aJ(bM),aI(bM,!0,a=>new bN(a));class bU extends aQ.AnimationPropertyInfo{buildAnimations(a,b,c,d){return[{babylonAnimatable:a._babylonCamera,babylonAnimation:this._buildAnimation(b,c,d)}]}}class bV extends aQ.AnimationPropertyInfo{buildAnimations(a,b,c,d){let e=[];for(let f in a._data)e.push({babylonAnimatable:a._data[f].babylonMaterial,babylonAnimation:this._buildAnimation(b,c,d)});return e}}class bW extends aQ.AnimationPropertyInfo{buildAnimations(a,b,c,d){return[{babylonAnimatable:a._babylonLight,babylonAnimation:this._buildAnimation(b,c,d)}]}}(0,aK.ZU)("/cameras/{}/orthographic/xmag",[new bU(K.X5.ANIMATIONTYPE_FLOAT,"orthoLeft",bQ,()=>1),new bU(K.X5.ANIMATIONTYPE_FLOAT,"orthoRight",bR,()=>1)]),(0,aK.ZU)("/cameras/{}/orthographic/ymag",[new bU(K.X5.ANIMATIONTYPE_FLOAT,"orthoBottom",bQ,()=>1),new bU(K.X5.ANIMATIONTYPE_FLOAT,"orthoTop",bR,()=>1)]),(0,aK.ZU)("/cameras/{}/orthographic/zfar",[new bU(K.X5.ANIMATIONTYPE_FLOAT,"maxZ",bP,()=>1)]),(0,aK.ZU)("/cameras/{}/orthographic/znear",[new bU(K.X5.ANIMATIONTYPE_FLOAT,"minZ",bP,()=>1)]),(0,aK.ZU)("/cameras/{}/perspective/yfov",[new bU(K.X5.ANIMATIONTYPE_FLOAT,"fov",bP,()=>1)]),(0,aK.ZU)("/cameras/{}/perspective/zfar",[new bU(K.X5.ANIMATIONTYPE_FLOAT,"maxZ",bP,()=>1)]),(0,aK.ZU)("/cameras/{}/perspective/znear",[new bU(K.X5.ANIMATIONTYPE_FLOAT,"minZ",bP,()=>1)]),(0,aK.ZU)("/materials/{}/pbrMetallicRoughness/baseColorFactor",[new bV(K.X5.ANIMATIONTYPE_COLOR3,"albedoColor",bO,()=>4),new bV(K.X5.ANIMATIONTYPE_FLOAT,"alpha",function(a,b,c,d){return b[c+3]*d},()=>4)]),(0,aK.ZU)("/materials/{}/pbrMetallicRoughness/metallicFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"metallic",bP,()=>1)]),(0,aK.ZU)("/materials/{}/pbrMetallicRoughness/metallicFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"roughness",bP,()=>1)]);let bX=bT("albedoTexture");(0,aK.ZU)("/materials/{}/pbrMetallicRoughness/baseColorTexture/extensions/KHR_texture_transform/scale",bX.scale),(0,aK.ZU)("/materials/{}/pbrMetallicRoughness/baseColorTexture/extensions/KHR_texture_transform/offset",bX.offset),(0,aK.ZU)("/materials/{}/pbrMetallicRoughness/baseColorTexture/extensions/KHR_texture_transform/rotation",bX.rotation);let bY=bT("metallicTexture");(0,aK.ZU)("//materials/{}/pbrMetallicRoughness/metallicRoughnessTexture/scale",bY.scale),(0,aK.ZU)("//materials/{}/pbrMetallicRoughness/metallicRoughnessTexture/offset",bY.offset),(0,aK.ZU)("//materials/{}/pbrMetallicRoughness/metallicRoughnessTexture/rotation",bY.rotation),(0,aK.ZU)("/materials/{}/emissiveFactor",[new bV(K.X5.ANIMATIONTYPE_COLOR3,"emissiveColor",bO,()=>3)]);let bZ=bT("bumpTexture");(0,aK.ZU)("/materials/{}/normalTexture/scale",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"bumpTexture.level",bP,()=>1)]),(0,aK.ZU)("/materials/{}/normalTexture/extensions/KHR_texture_transform/scale",bZ.scale),(0,aK.ZU)("/materials/{}/normalTexture/extensions/KHR_texture_transform/offset",bZ.offset),(0,aK.ZU)("/materials/{}/normalTexture/extensions/KHR_texture_transform/rotation",bZ.rotation),(0,aK.ZU)("/materials/{}/occlusionTexture/strength",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"ambientTextureStrength",bP,()=>1)]);let b$=bT("ambientTexture");(0,aK.ZU)("/materials/{}/occlusionTexture/extensions/KHR_texture_transform/scale",b$.scale),(0,aK.ZU)("/materials/{}/occlusionTexture/extensions/KHR_texture_transform/offset",b$.offset),(0,aK.ZU)("/materials/{}/occlusionTexture/extensions/KHR_texture_transform/rotation",b$.rotation);let b_=bT("emissiveTexture");(0,aK.ZU)("/materials/{}/emissiveTexture/extensions/KHR_texture_transform/scale",b_.scale),(0,aK.ZU)("/materials/{}/emissiveTexture/extensions/KHR_texture_transform/offset",b_.offset),(0,aK.ZU)("/materials/{}/emissiveTexture/extensions/KHR_texture_transform/rotation",b_.rotation),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyStrength",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"anisotropy.intensity",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyRotation",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"anisotropy.angle",bP,()=>1)]);let b0=bT("anisotropy.texture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyTexture/extensions/KHR_texture_transform/scale",b0.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyTexture/extensions/KHR_texture_transform/offset",b0.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_anisotropy/anisotropyTexture/extensions/KHR_texture_transform/rotation",b0.rotation),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"clearCoat.intensity",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"clearCoat.roughness",bP,()=>1)]);let b1=bT("clearCoat.texture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatTexture/extensions/KHR_texture_transform/scale",b1.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatTexture/extensions/KHR_texture_transform/offset",b1.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatTexture/extensions/KHR_texture_transform/rotation",b1.rotation);let b2=bT("clearCoat.bumpTexture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/scale",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"clearCoat.bumpTexture.level",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/extensions/KHR_texture_transform/scale",b2.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/extensions/KHR_texture_transform/offset",b2.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatNormalTexture/extensions/KHR_texture_transform/rotation",b2.rotation);let b3=bT("clearCoat.textureRoughness");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessTexture/extensions/KHR_texture_transform/scale",b3.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessTexture/extensions/KHR_texture_transform/offset",b3.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_clearcoat/clearcoatRoughnessTexture/extensions/KHR_texture_transform/rotation",b3.rotation),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_dispersion/dispersionFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"subSurface.dispersion",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_emissive_strength/emissiveStrength",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"emissiveIntensity",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_ior/ior",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"indexOfRefraction",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"iridescence.intensity",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceIor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"iridescence.indexOfRefraction",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessMinimum",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"iridescence.minimumThickness",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessMaximum",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"iridescence.maximumThickness",bP,()=>1)]);let b4=bT("iridescence.texture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceTexture/extensions/KHR_texture_transform/scale",b4.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceTexture/extensions/KHR_texture_transform/offset",b4.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceTexture/extensions/KHR_texture_transform/rotation",b4.rotation);let b5=bT("iridescence.thicknessTexture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessTexture/extensions/KHR_texture_transform/scale",b5.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessTexture/extensions/KHR_texture_transform/offset",b5.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_iridescence/iridescenceThicknessTexture/extensions/KHR_texture_transform/rotation",b5.rotation),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_sheen/sheenColorFactor",[new bV(K.X5.ANIMATIONTYPE_COLOR3,"sheen.color",bO,()=>3)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"sheen.roughness",bP,()=>1)]);let b6=bT("sheen.texture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_sheen/sheenColorTexture/extensions/KHR_texture_transform/scale",b6.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_sheen/sheenColorTexture/extensions/KHR_texture_transform/offset",b6.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_sheen/sheenColorTexture/extensions/KHR_texture_transform/rotation",b6.rotation);let b7=bT("sheen.textureRoughness");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessTexture/extensions/KHR_texture_transform/scale",b7.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessTexture/extensions/KHR_texture_transform/offset",b7.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_sheen/sheenRoughnessTexture/extensions/KHR_texture_transform/rotation",b7.rotation),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_specular/specularFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"metallicF0Factor",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_specular/specularColorFactor",[new bV(K.X5.ANIMATIONTYPE_COLOR3,"metallicReflectanceColor",bO,()=>3)]);let b8=bT("metallicReflectanceTexture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_specular/specularTexture/extensions/KHR_texture_transform/scale",b8.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_specular/specularTexture/extensions/KHR_texture_transform/offset",b8.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_specular/specularTexture/extensions/KHR_texture_transform/rotation",b8.rotation);let b9=bT("reflectanceTexture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_specular/specularColorTexture/extensions/KHR_texture_transform/scale",b9.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_specular/specularColorTexture/extensions/KHR_texture_transform/offset",b9.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_specular/specularColorTexture/extensions/KHR_texture_transform/rotation",b9.rotation),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_transmission/transmissionFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"subSurface.refractionIntensity",bP,()=>1)]);let ca=bT("subSurface.refractionIntensityTexture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_transmission/transmissionTexture/extensions/KHR_texture_transform/scale",ca.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_transmission/transmissionTexture/extensions/KHR_texture_transform/offset",ca.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_transmission/transmissionTexture/extensions/KHR_texture_transform/rotation",ca.rotation),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_volume/attenuationColor",[new bV(K.X5.ANIMATIONTYPE_COLOR3,"subSurface.tintColor",bO,()=>3)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_volume/attenuationDistance",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"subSurface.tintColorAtDistance",bP,()=>1)]),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_volume/thicknessFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"subSurface.maximumThickness",bP,()=>1)]);let cb=bT("subSurface.thicknessTexture");(0,aK.ZU)("/materials/{}/extensions/KHR_materials_volume/thicknessTexture/extensions/KHR_texture_transform/scale",cb.scale),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_volume/thicknessTexture/extensions/KHR_texture_transform/offset",cb.offset),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_volume/thicknessTexture/extensions/KHR_texture_transform/rotation",cb.rotation),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionFactor",[new bV(K.X5.ANIMATIONTYPE_FLOAT,"subSurface.translucencyIntensity",bP,()=>1)]);let cc=bT("subSurface.translucencyIntensityTexture");(0,aK.ZU)("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionTexture/extensions/KHR_texture_transform/scale",cc.scale),(0,aK.ZU)("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionTexture/extensions/KHR_texture_transform/offset",cc.offset),(0,aK.ZU)("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionTexture/extensions/KHR_texture_transform/rotation",cc.rotation),(0,aK.ZU)("/materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorFactor",[new bV(K.X5.ANIMATIONTYPE_COLOR3,"subSurface.translucencyColor",bO,()=>3)]);let cd=bT("subSurface.translucencyColorTexture");(0,aK.ZU)("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorTexture/extensions/KHR_texture_transform/scale",cd.scale),(0,aK.ZU)("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorTexture/extensions/KHR_texture_transform/offset",cd.offset),(0,aK.ZU)("materials/{}/extensions/KHR_materials_diffuse_transmission/diffuseTransmissionColorTexture/extensions/KHR_texture_transform/rotation",cd.rotation),(0,aK.ZU)("/extensions/KHR_lights_punctual/lights/{}/color",[new bW(K.X5.ANIMATIONTYPE_COLOR3,"diffuse",bO,()=>3)]),(0,aK.ZU)("/extensions/KHR_lights_punctual/lights/{}/intensity",[new bW(K.X5.ANIMATIONTYPE_FLOAT,"intensity",bP,()=>1)]),(0,aK.ZU)("/extensions/KHR_lights_punctual/lights/{}/range",[new bW(K.X5.ANIMATIONTYPE_FLOAT,"range",bP,()=>1)]),(0,aK.ZU)("/extensions/KHR_lights_punctual/lights/{}/spot/innerConeAngle",[new bW(K.X5.ANIMATIONTYPE_FLOAT,"innerAngle",bS,()=>1)]),(0,aK.ZU)("/extensions/KHR_lights_punctual/lights/{}/spot/outerConeAngle",[new bW(K.X5.ANIMATIONTYPE_FLOAT,"angle",bS,()=>1)]),(0,aK.ZU)("/nodes/{}/extensions/EXT_lights_ies/color",[new bW(K.X5.ANIMATIONTYPE_COLOR3,"diffuse",bO,()=>3)]),(0,aK.ZU)("/nodes/{}/extensions/EXT_lights_ies/multiplier",[new bW(K.X5.ANIMATIONTYPE_FLOAT,"intensity",bP,()=>1)]);let ce="KHR_animation_pointer";class cf{constructor(a){this.name=ce,this._loader=a,this._pathToObjectConverter=(0,aK.Wt)(this._loader.gltf)}get enabled(){return this._loader.isExtensionUsed(ce)}dispose(){this._loader=null,delete this._pathToObjectConverter}_loadAnimationChannelAsync(a,b,c,d,e){let f=d.target.extensions?.KHR_animation_pointer;if(!f||!this._pathToObjectConverter)return null;"pointer"!==d.target.path&&u.V.Warn(`${a}/target/path: Value (${d.target.path}) must be (pointer) when using the ${this.name} extension`),void 0!=d.target.node&&u.V.Warn(`${a}/target/node: Value (${d.target.node}) must not be present when using the ${this.name} extension`);let g=`${a}/extensions/${this.name}`,h=f.pointer;if(!h)throw Error(`${g}: Pointer is missing`);try{let f=this._pathToObjectConverter.convert(h);if(!f.info.interpolation)throw Error(`${g}/pointer: Interpolation is missing`);return this._loader._loadAnimationChannelFromTargetInfoAsync(a,b,c,d,{object:f.object,info:f.info.interpolation},e)}catch(a){return u.V.Warn(`${g}/pointer: Invalid pointer (${h}) skipped`),null}}}aJ(ce),aI(ce,!0,a=>new cf(a));var cg=c(39766),ch=c(72026),ci=c(62009);c(53892);let cj="MSFT_audio_emitter";class ck{constructor(a){this.name=cj,this._loader=a,this.enabled=this._loader.isExtensionUsed(cj)}dispose(){this._loader=null,this._clips=null,this._emitters=null}onLoading(){let a=this._loader.gltf.extensions;if(a&&a[this.name]){let b=a[this.name];this._clips=b.clips,this._emitters=b.emitters,aN.Assign(this._clips),aN.Assign(this._emitters)}}loadSceneAsync(a,b){return aP.LoadExtensionAsync(a,b,this.name,async(c,d)=>{let e=[];for(let f of(e.push(this._loader.loadSceneAsync(a,b)),d.emitters)){let a=aN.Get(`${c}/emitters`,this._emitters,f);if(void 0!=a.refDistance||void 0!=a.maxDistance||void 0!=a.rolloffFactor||void 0!=a.distanceModel||void 0!=a.innerAngle||void 0!=a.outerAngle)throw Error(`${c}: Direction or Distance properties are not allowed on emitters attached to a scene`);e.push(this._loadEmitterAsync(`${c}/emitters/${a.index}`,a))}await Promise.all(e)})}loadNodeAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(a,d)=>{let e=[],f=await this._loader.loadNodeAsync(a,b,b=>{for(let c of d.emitters){let d=aN.Get(`${a}/emitters`,this._emitters,c);e.push(this._loadEmitterAsync(`${a}/emitters/${d.index}`,d).then(()=>{for(let a of d._babylonSounds)a.attachToMesh(b),(void 0!=d.innerAngle||void 0!=d.outerAngle)&&(a.setLocalDirectionToMesh(G.Pq.Forward()),a.setDirectionalCone(2*r.S0.ToDegrees(void 0==d.innerAngle?Math.PI:d.innerAngle),2*r.S0.ToDegrees(void 0==d.outerAngle?Math.PI:d.outerAngle),0))}))}c(b)});return await Promise.all(e),f})}loadAnimationAsync(a,b){return aP.LoadExtensionAsync(a,b,this.name,async(c,d)=>{let e=await this._loader.loadAnimationAsync(a,b),f=[];for(let g of(aN.Assign(d.events),d.events))f.push(this._loadAnimationEventAsync(`${c}/events/${g.index}`,a,b,g,e));return await Promise.all(f),e})}_loadClipAsync(a,b){let c;if(b._objectURL)return b._objectURL;if(b.uri)c=this._loader.loadUriAsync(a,b,b.uri);else{let d=aN.Get(`${a}/bufferView`,this._loader.gltf.bufferViews,b.bufferView);c=this._loader.loadBufferViewAsync(`/bufferViews/${d.index}`,d)}return b._objectURL=c.then(a=>URL.createObjectURL(new Blob([a],{type:b.mimeType}))),b._objectURL}_loadEmitterAsync(a,b){if(b._babylonSounds=b._babylonSounds||[],!b._babylonData){let a=[],c=b.name||`emitter${b.index}`,d={loop:!1,autoplay:!1,volume:void 0==b.volume?1:b.volume};for(let e=0;e<b.clips.length;e++){let f=`/extensions/${this.name}/clips`,g=aN.Get(f,this._clips,b.clips[e].clip);a.push(this._loadClipAsync(`${f}/${b.clips[e].clip}`,g).then(a=>{let f=b._babylonSounds[e]=new ch.A(c,a,this._loader.babylonScene,null,d);f.refDistance=b.refDistance||1,f.maxDistance=b.maxDistance||256,f.rolloffFactor=b.rolloffFactor||1,f.distanceModel=b.distanceModel||"exponential"}))}let e=Promise.all(a).then(()=>{let a=b.clips.map(a=>a.weight||1),c=new ci.r(b.loop||!1,b._babylonSounds,a);b.innerAngle&&(c.directionalConeInnerAngle=2*r.S0.ToDegrees(b.innerAngle)),b.outerAngle&&(c.directionalConeOuterAngle=2*r.S0.ToDegrees(b.outerAngle)),b.volume&&(c.volume=b.volume),b._babylonData.sound=c});b._babylonData={loaded:e}}return b._babylonData.loaded}_getEventAction(a,b,c,d,e){switch(c){case"play":return a=>{b.play((e||0)+(a-d))};case"stop":return()=>{b.stop()};case"pause":return()=>{b.pause()};default:throw Error(`${a}: Unsupported action ${c}`)}}_loadAnimationEventAsync(a,b,c,d,e){if(0==e.targetedAnimations.length)return Promise.resolve();let f=e.targetedAnimations[0],g=d.emitter,h=aN.Get(`/extensions/${this.name}/emitters`,this._emitters,g);return this._loadEmitterAsync(a,h).then(()=>{let b=h._babylonData.sound;if(b){let c=new cg.p(d.time,this._getEventAction(a,b,d.action,d.time,d.startOffset));f.animation.addEvent(c),e.onAnimationGroupEndObservable.add(()=>{b.stop()}),e.onAnimationGroupPauseObservable.add(()=>{b.pause()})}})}}aJ(cj),aI(cj,!0,a=>new ck(a));let cl="MSFT_lod";class cm{constructor(a){this.name=cl,this.order=100,this.maxLODsToLoad=10,this.onNodeLODsLoadedObservable=new q.cP,this.onMaterialLODsLoadedObservable=new q.cP,this._bufferLODs=[],this._nodeIndexLOD=null,this._nodeSignalLODs=[],this._nodePromiseLODs=[],this._nodeBufferLODs=[],this._materialIndexLOD=null,this._materialSignalLODs=[],this._materialPromiseLODs=[],this._materialBufferLODs=[],this._loader=a,this.maxLODsToLoad=this._loader.parent.extensionOptions[cl]?.maxLODsToLoad??this.maxLODsToLoad,this.enabled=this._loader.isExtensionUsed(cl)}dispose(){this._loader=null,this._nodeIndexLOD=null,this._nodeSignalLODs.length=0,this._nodePromiseLODs.length=0,this._nodeBufferLODs.length=0,this._materialIndexLOD=null,this._materialSignalLODs.length=0,this._materialPromiseLODs.length=0,this._materialBufferLODs.length=0,this.onMaterialLODsLoadedObservable.clear(),this.onNodeLODsLoadedObservable.clear()}onReady(){for(let a=0;a<this._nodePromiseLODs.length;a++){let b=Promise.all(this._nodePromiseLODs[a]).then(()=>{0!==a&&(this._loader.endPerformanceCounter(`Node LOD ${a}`),this._loader.log(`Loaded node LOD ${a}`)),this.onNodeLODsLoadedObservable.notifyObservers(a),a!==this._nodePromiseLODs.length-1&&(this._loader.startPerformanceCounter(`Node LOD ${a+1}`),this._loadBufferLOD(this._nodeBufferLODs,a+1),this._nodeSignalLODs[a]&&this._nodeSignalLODs[a].resolve())});this._loader._completePromises.push(b)}for(let a=0;a<this._materialPromiseLODs.length;a++){let b=Promise.all(this._materialPromiseLODs[a]).then(()=>{0!==a&&(this._loader.endPerformanceCounter(`Material LOD ${a}`),this._loader.log(`Loaded material LOD ${a}`)),this.onMaterialLODsLoadedObservable.notifyObservers(a),a!==this._materialPromiseLODs.length-1&&(this._loader.startPerformanceCounter(`Material LOD ${a+1}`),this._loadBufferLOD(this._materialBufferLODs,a+1),this._materialSignalLODs[a]&&this._materialSignalLODs[a].resolve())});this._loader._completePromises.push(b)}}loadSceneAsync(a,b){let c=this._loader.loadSceneAsync(a,b);return this._loadBufferLOD(this._bufferLODs,0),c}loadNodeAsync(a,b,c){return aP.LoadExtensionAsync(a,b,this.name,async(a,d)=>{let e,f=this._getLODs(a,b,this._loader.gltf.nodes,d.ids);this._loader.logOpen(`${a}`);for(let a=0;a<f.length;a++){let b=f[a];0!==a&&(this._nodeIndexLOD=a,this._nodeSignalLODs[a]=this._nodeSignalLODs[a]||new aB.c);let d=a=>{c(a),a.setEnabled(!1)},g=this._loader.loadNodeAsync(`/nodes/${b.index}`,b,d).then(b=>{if(0!==a){let b=f[a-1];b._babylonTransformNode&&(this._disposeTransformNode(b._babylonTransformNode),delete b._babylonTransformNode)}return b.setEnabled(!0),b});this._nodePromiseLODs[a]=this._nodePromiseLODs[a]||[],0===a?e=g:(this._nodeIndexLOD=null,this._nodePromiseLODs[a].push(g))}return this._loader.logClose(),await e})}_loadMaterialAsync(a,b,c,d,e){return this._nodeIndexLOD?null:aP.LoadExtensionAsync(a,b,this.name,async(a,f)=>{let g,h=this._getLODs(a,b,this._loader.gltf.materials,f.ids);this._loader.logOpen(`${a}`);for(let a=0;a<h.length;a++){let b=h[a];0!==a&&(this._materialIndexLOD=a);let f=this._loader._loadMaterialAsync(`/materials/${b.index}`,b,c,d,b=>{0===a&&e(b)}).then(b=>{if(0!==a){e(b);let c=h[a-1]._data;c[d]&&(this._disposeMaterials([c[d].babylonMaterial]),delete c[d])}return b});this._materialPromiseLODs[a]=this._materialPromiseLODs[a]||[],0===a?g=f:(this._materialIndexLOD=null,this._materialPromiseLODs[a].push(f))}return this._loader.logClose(),await g})}_loadUriAsync(a,b,c){if(null!==this._nodeIndexLOD){this._loader.log("deferred");let d=this._nodeIndexLOD-1;return this._nodeSignalLODs[d]=this._nodeSignalLODs[d]||new aB.c,this._nodeSignalLODs[this._nodeIndexLOD-1].promise.then(async()=>await this._loader.loadUriAsync(a,b,c))}if(null!==this._materialIndexLOD){this._loader.log("deferred");let d=this._materialIndexLOD-1;return this._materialSignalLODs[d]=this._materialSignalLODs[d]||new aB.c,this._materialSignalLODs[d].promise.then(async()=>await this._loader.loadUriAsync(a,b,c))}return null}loadBufferAsync(a,b,c,d){if(this._loader.parent.useRangeRequests&&!b.uri){if(!this._loader.bin)throw Error(`${a}: Uri is missing or the binary glTF is missing its binary chunk`);let b=async(a,b)=>{let e=c+d-1,f=a[b];return f?(f.start=Math.min(f.start,c),f.end=Math.max(f.end,e)):(f={start:c,end:e,loaded:new aB.c},a[b]=f),await f.loaded.promise.then(a=>new Uint8Array(a.buffer,a.byteOffset+c-f.start,d))};return(this._loader.log("deferred"),null!==this._nodeIndexLOD)?b(this._nodeBufferLODs,this._nodeIndexLOD):null!==this._materialIndexLOD?b(this._materialBufferLODs,this._materialIndexLOD):b(this._bufferLODs,0)}return null}_loadBufferLOD(a,b){let c=a[b];c&&(this._loader.log(`Loading buffer range [${c.start}-${c.end}]`),this._loader.bin.readAsync(c.start,c.end-c.start+1).then(a=>{c.loaded.resolve(a)},a=>{c.loaded.reject(a)}))}_getLODs(a,b,c,d){if(this.maxLODsToLoad<=0)throw Error("maxLODsToLoad must be greater than zero");let e=[];for(let b=d.length-1;b>=0;b--)if(e.push(aN.Get(`${a}/ids/${d[b]}`,c,d[b])),e.length===this.maxLODsToLoad)return e;return e.push(b),e}_disposeTransformNode(a){let b=[],c=a.material;for(let d of(c&&b.push(c),a.getChildMeshes()))d.material&&b.push(d.material);a.dispose();let d=b.filter(a=>this._loader.babylonScene.meshes.every(b=>b.material!=a));this._disposeMaterials(d)}_disposeMaterials(a){let b={};for(let c of a){for(let a of c.getActiveTextures())b[a.uniqueId]=a;c.dispose()}for(let a in b)for(let c of this._loader.babylonScene.materials)c.hasTexture(b[a])&&delete b[a];for(let a in b)b[a].dispose()}}aJ(cl),aI(cl,!0,a=>new cm(a));let cn="MSFT_minecraftMesh";class co{constructor(a){this.name=cn,this._loader=a,this.enabled=this._loader.isExtensionUsed(cn)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtraAsync(a,b,this.name,async(d,e)=>{if(e){if(!(c instanceof aC.Y))throw Error(`${d}: Material type not supported`);let e=this._loader.loadMaterialPropertiesAsync(a,b,c);return c.needAlphaBlending()&&(c.forceDepthWrite=!0,c.separateCullingPass=!0),c.backFaceCulling=c.forceDepthWrite,c.twoSidedLighting=!0,await e}})}}aJ(cn),aI(cn,!0,a=>new co(a));let cp="MSFT_sRGBFactors";class cq{constructor(a){this.name=cp,this._loader=a,this.enabled=this._loader.isExtensionUsed(cp)}dispose(){this._loader=null}loadMaterialPropertiesAsync(a,b,c){return aP.LoadExtraAsync(a,b,this.name,async(d,e)=>{if(e){if(!(c instanceof aC.Y))throw Error(`${d}: Material type not supported`);let e=this._loader.loadMaterialPropertiesAsync(a,b,c),f=c.getScene().getEngine().useExactSrgbConversions;return c.albedoTexture||c.albedoColor.toLinearSpaceToRef(c.albedoColor,f),c.reflectivityTexture||c.reflectivityColor.toLinearSpaceToRef(c.reflectivityColor,f),await e}})}}aJ(cp),aI(cp,!0,a=>new cq(a));var cr=c(51689),cs=c(73748),ct=c(77100);function cu(a){let[b,c]=a.split(":");return cv({op:b,extension:c})}function cv(a,b=!0){let c=a.extension?cx[a.extension]?.[a.op]:cy[a.op];if(!c&&(u.V.Warn(`No mapping found for operation ${a.op} and extension ${a.extension||"KHR_interactivity"}`),b)){let b={},c={flows:{}};if(a.inputValueSockets)for(let c in b.values={},a.inputValueSockets)b.values[c]={name:c};return a.outputValueSockets&&(c.values={},Object.keys(a.outputValueSockets).forEach(a=>{c.values[a]={name:a}})),{blocks:[],inputs:b,outputs:c}}return c}function cw(a,b,c){cx[b]||(cx[b]={}),cx[b][a]=c}let cx={BABYLON:{"flow/log":{blocks:["FlowGraphConsoleLogBlock"],inputs:{values:{message:{name:"message"}}}}}},cy={"event/onStart":{blocks:["FlowGraphSceneReadyEventBlock"],outputs:{flows:{out:{name:"done"}}}},"event/onTick":{blocks:["FlowGraphSceneTickEventBlock"],inputs:{},outputs:{values:{timeSinceLastTick:{name:"deltaTime",gltfType:"number"}},flows:{out:{name:"done"}}}},"event/send":{blocks:["FlowGraphSendCustomEventBlock"],extraProcessor(a,b,c,d,e){if("event/send"!==b.op||!a.configuration||1!==Object.keys(a.configuration).length)throw Error("Receive event should have a single configuration object, the event itself");let f=a.configuration.event.value[0];if("number"!=typeof f)throw Error("Event id should be a number");let g=d.arrays.events[f],h=e[0];return h.config||(h.config={}),h.config.eventId=g.eventId,h.config.eventData=g.eventData,e}},"event/receive":{blocks:["FlowGraphReceiveCustomEventBlock"],outputs:{flows:{out:{name:"done"}}},validation(a,b){if(!a.configuration)return u.V.Error("Receive event should have a configuration object"),{valid:!1,error:"Receive event should have a configuration object"};let c=a.configuration.event;if(!c)return u.V.Error("Receive event should have a single configuration object, the event itself"),{valid:!1,error:"Receive event should have a single configuration object, the event itself"};let d=c.value[0];return"number"!=typeof d?(u.V.Error("Event id should be a number"),{valid:!1,error:"Event id should be a number"}):b.events?.[d]?{valid:!0}:(u.V.Error(`Event with id ${d} not found`),{valid:!1,error:`Event with id ${d} not found`})},extraProcessor(a,b,c,d,e){if("event/receive"!==b.op||!a.configuration||1!==Object.keys(a.configuration).length)throw Error("Receive event should have a single configuration object, the event itself");let f=a.configuration.event.value[0];if("number"!=typeof f)throw Error("Event id should be a number");let g=d.arrays.events[f],h=e[0];return h.config||(h.config={}),h.config.eventId=g.eventId,h.config.eventData=g.eventData,e}},"math/e":cz("FlowGraphEBlock"),"math/pi":cz("FlowGraphPIBlock"),"math/inf":cz("FlowGraphInfBlock"),"math/nan":cz("FlowGraphNaNBlock"),"math/abs":cz("FlowGraphAbsBlock"),"math/sign":cz("FlowGraphSignBlock"),"math/trunc":cz("FlowGraphTruncBlock"),"math/floor":cz("FlowGraphFloorBlock"),"math/ceil":cz("FlowGraphCeilBlock"),"math/round":{blocks:["FlowGraphRoundBlock"],configuration:{},inputs:{values:{a:{name:"a"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e){var f;return(f=e[0]).config||(f.config={}),e[0].config.roundHalfAwayFromZero=!0,e}},"math/fract":cz("FlowGraphFractBlock"),"math/neg":cz("FlowGraphNegationBlock"),"math/add":cz("FlowGraphAddBlock",["a","b"],!0),"math/sub":cz("FlowGraphSubtractBlock",["a","b"],!0),"math/mul":{blocks:["FlowGraphMultiplyBlock"],extraProcessor(a,b,c,d,e){var f;(f=e[0]).config||(f.config={}),e[0].config.useMatrixPerComponent=!0,e[0].config.preventIntegerFloatArithmetic=!0;let g=-1;return Object.keys(a.values||{}).find(b=>a.values?.[b].type!==void 0&&(g=a.values[b].type,!0)),-1!==g&&(e[0].config.type=d.arrays.types[g].flowGraphType),e},validation:a=>a.values?cA(a):{valid:!0}},"math/div":cz("FlowGraphDivideBlock",["a","b"],!0),"math/rem":cz("FlowGraphModuloBlock",["a","b"]),"math/min":cz("FlowGraphMinBlock",["a","b"]),"math/max":cz("FlowGraphMaxBlock",["a","b"]),"math/clamp":cz("FlowGraphClampBlock",["a","b","c"]),"math/saturate":cz("FlowGraphSaturateBlock"),"math/mix":cz("FlowGraphMathInterpolationBlock",["a","b","c"]),"math/eq":cz("FlowGraphEqualityBlock",["a","b"]),"math/lt":cz("FlowGraphLessThanBlock",["a","b"]),"math/le":cz("FlowGraphLessThanOrEqualBlock",["a","b"]),"math/gt":cz("FlowGraphGreaterThanBlock",["a","b"]),"math/ge":cz("FlowGraphGreaterThanOrEqualBlock",["a","b"]),"math/isnan":cz("FlowGraphIsNaNBlock"),"math/isinf":cz("FlowGraphIsInfBlock"),"math/select":{blocks:["FlowGraphConditionalBlock"],inputs:{values:{condition:{name:"condition"},a:{name:"onTrue"},b:{name:"onFalse"}}},outputs:{values:{value:{name:"output"}}}},"math/random":{blocks:["FlowGraphRandomBlock"],outputs:{values:{value:{name:"value"}}}},"math/sin":cz("FlowGraphSinBlock"),"math/cos":cz("FlowGraphCosBlock"),"math/tan":cz("FlowGraphTanBlock"),"math/asin":cz("FlowGraphASinBlock"),"math/acos":cz("FlowGraphACosBlock"),"math/atan":cz("FlowGraphATanBlock"),"math/atan2":cz("FlowGraphATan2Block",["a","b"]),"math/sinh":cz("FlowGraphSinhBlock"),"math/cosh":cz("FlowGraphCoshBlock"),"math/tanh":cz("FlowGraphTanhBlock"),"math/asinh":cz("FlowGraphASinhBlock"),"math/acosh":cz("FlowGraphACoshBlock"),"math/atanh":cz("FlowGraphATanhBlock"),"math/exp":cz("FlowGraphExponentialBlock"),"math/log":cz("FlowGraphLogBlock"),"math/log2":cz("FlowGraphLog2Block"),"math/log10":cz("FlowGraphLog10Block"),"math/sqrt":cz("FlowGraphSquareRootBlock"),"math/cbrt":cz("FlowGraphCubeRootBlock"),"math/pow":cz("FlowGraphPowerBlock",["a","b"]),"math/length":cz("FlowGraphLengthBlock"),"math/normalize":cz("FlowGraphNormalizeBlock"),"math/dot":cz("FlowGraphDotBlock",["a","b"]),"math/cross":cz("FlowGraphCrossBlock",["a","b"]),"math/rotate2D":cz("FlowGraphRotate2DBlock",["a","b"]),"math/rotate3D":cz("FlowGraphRotate3DBlock",["a","b"]),"math/transform":{blocks:["FlowGraphTransformVectorBlock"],inputs:{values:{a:{name:"a"},b:{name:"b"}}},outputs:{values:{value:{name:"value"}}}},"math/combine2":{blocks:["FlowGraphCombineVector2Block"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}}},"math/combine3":{blocks:["FlowGraphCombineVector3Block"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}}},"math/combine4":{blocks:["FlowGraphCombineVector4Block"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"},d:{name:"input_3",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}}},"math/extract2":{blocks:["FlowGraphExtractVector2Block"],inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"}}}},"math/extract3":{blocks:["FlowGraphExtractVector3Block"],inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"}}}},"math/extract4":{blocks:["FlowGraphExtractVector4Block"],inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"},3:{name:"output_3"}}}},"math/transpose":cz("FlowGraphTransposeBlock"),"math/determinant":cz("FlowGraphDeterminantBlock"),"math/inverse":cz("FlowGraphInvertMatrixBlock"),"math/matmul":cz("FlowGraphMatrixMultiplicationBlock",["a","b"]),"math/matCompose":{blocks:["FlowGraphMatrixCompose"],inputs:{values:{translation:{name:"position",gltfType:"float3"},rotation:{name:"rotationQuaternion",gltfType:"float4"},scale:{name:"scaling",gltfType:"float3"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e,f){let g=e[0].dataInputs.find(a=>"rotationQuaternion"===a.name);if(!g)throw Error("Rotation quaternion input not found");return f._connectionValues[g.uniqueId]&&(f._connectionValues[g.uniqueId].type="Quaternion"),e}},"math/matDecompose":{blocks:["FlowGraphMatrixDecompose"],inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{translation:{name:"position"},rotation:{name:"rotationQuaternion"},scale:{name:"scaling"}}}},"math/quatConjugate":cz("FlowGraphConjugateBlock",["a"]),"math/quatMul":{blocks:["FlowGraphMultiplyBlock"],inputs:{values:{a:{name:"a",gltfType:"vector4"},b:{name:"b",gltfType:"vector4"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e){var f;return(f=e[0]).config||(f.config={}),e[0].config.type="Quaternion",e}},"math/quatAngleBetween":cz("FlowGraphAngleBetweenBlock",["a","b"]),"math/quatFromAxisAngle":{blocks:["FlowGraphQuaternionFromAxisAngleBlock"],inputs:{values:{axis:{name:"a",gltfType:"float3"},angle:{name:"b",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}}},"math/quatToAxisAngle":cz("FlowGraphAxisAngleFromQuaternionBlock",["a"]),"math/quatFromDirections":cz("FlowGraphQuaternionFromDirectionsBlock",["a","b"]),"math/combine2x2":{blocks:["FlowGraphCombineMatrix2DBlock"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"},d:{name:"input_3",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e){var f;return(f=e[0]).config||(f.config={}),e[0].config.inputIsColumnMajor=!0,e}},"math/extract2x2":{blocks:["FlowGraphExtractMatrix2DBlock"],inputs:{values:{a:{name:"input",gltfType:"float2x2"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"},3:{name:"output_3"}}}},"math/combine3x3":{blocks:["FlowGraphCombineMatrix3DBlock"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"},d:{name:"input_3",gltfType:"number"},e:{name:"input_4",gltfType:"number"},f:{name:"input_5",gltfType:"number"},g:{name:"input_6",gltfType:"number"},h:{name:"input_7",gltfType:"number"},i:{name:"input_8",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e){var f;return(f=e[0]).config||(f.config={}),e[0].config.inputIsColumnMajor=!0,e}},"math/extract3x3":{blocks:["FlowGraphExtractMatrix3DBlock"],inputs:{values:{a:{name:"input",gltfType:"float3x3"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"},3:{name:"output_3"},4:{name:"output_4"},5:{name:"output_5"},6:{name:"output_6"},7:{name:"output_7"},8:{name:"output_8"}}}},"math/combine4x4":{blocks:["FlowGraphCombineMatrixBlock"],inputs:{values:{a:{name:"input_0",gltfType:"number"},b:{name:"input_1",gltfType:"number"},c:{name:"input_2",gltfType:"number"},d:{name:"input_3",gltfType:"number"},e:{name:"input_4",gltfType:"number"},f:{name:"input_5",gltfType:"number"},g:{name:"input_6",gltfType:"number"},h:{name:"input_7",gltfType:"number"},i:{name:"input_8",gltfType:"number"},j:{name:"input_9",gltfType:"number"},k:{name:"input_10",gltfType:"number"},l:{name:"input_11",gltfType:"number"},m:{name:"input_12",gltfType:"number"},n:{name:"input_13",gltfType:"number"},o:{name:"input_14",gltfType:"number"},p:{name:"input_15",gltfType:"number"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e){var f;return(f=e[0]).config||(f.config={}),e[0].config.inputIsColumnMajor=!0,e}},"math/extract4x4":{blocks:["FlowGraphExtractMatrixBlock"],configuration:{},inputs:{values:{a:{name:"input",gltfType:"number"}}},outputs:{values:{0:{name:"output_0"},1:{name:"output_1"},2:{name:"output_2"},3:{name:"output_3"},4:{name:"output_4"},5:{name:"output_5"},6:{name:"output_6"},7:{name:"output_7"},8:{name:"output_8"},9:{name:"output_9"},10:{name:"output_10"},11:{name:"output_11"},12:{name:"output_12"},13:{name:"output_13"},14:{name:"output_14"},15:{name:"output_15"}}}},"math/not":{blocks:["FlowGraphBitwiseNotBlock"],inputs:{values:{a:{name:"a"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e,f){var g;(g=e[0]).config||(g.config={});let h=e[0].dataInputs[0];return e[0].config.valueType=f._connectionValues[h.uniqueId]?.type??"FlowGraphInteger",e}},"math/and":{blocks:["FlowGraphBitwiseAndBlock"],inputs:{values:{a:{name:"a"},b:{name:"b"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e,f){var g;(g=e[0]).config||(g.config={});let h=e[0].dataInputs[0],i=e[0].dataInputs[1];return e[0].config.valueType=f._connectionValues[h.uniqueId]?.type??f._connectionValues[i.uniqueId]?.type??"FlowGraphInteger",e}},"math/or":{blocks:["FlowGraphBitwiseOrBlock"],inputs:{values:{a:{name:"a"},b:{name:"b"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e,f){var g;(g=e[0]).config||(g.config={});let h=e[0].dataInputs[0],i=e[0].dataInputs[1];return e[0].config.valueType=f._connectionValues[h.uniqueId]?.type??f._connectionValues[i.uniqueId]?.type??"FlowGraphInteger",e}},"math/xor":{blocks:["FlowGraphBitwiseXorBlock"],inputs:{values:{a:{name:"a"},b:{name:"b"}}},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,c,d,e,f){var g;(g=e[0]).config||(g.config={});let h=e[0].dataInputs[0],i=e[0].dataInputs[1];return e[0].config.valueType=f._connectionValues[h.uniqueId]?.type??f._connectionValues[i.uniqueId]?.type??"FlowGraphInteger",e}},"math/asr":cz("FlowGraphBitwiseRightShiftBlock",["a","b"]),"math/lsl":cz("FlowGraphBitwiseLeftShiftBlock",["a","b"]),"math/clz":cz("FlowGraphLeadingZerosBlock"),"math/ctz":cz("FlowGraphTrailingZerosBlock"),"math/popcnt":cz("FlowGraphOneBitsCounterBlock"),"math/rad":cz("FlowGraphDegToRadBlock"),"math/deg":cz("FlowGraphRadToDegBlock"),"type/boolToInt":cz("FlowGraphBooleanToInt"),"type/boolToFloat":cz("FlowGraphBooleanToFloat"),"type/intToBool":cz("FlowGraphIntToBoolean"),"type/intToFloat":cz("FlowGraphIntToFloat"),"type/floatToInt":cz("FlowGraphFloatToInt"),"type/floatToBool":cz("FlowGraphFloatToBoolean"),"flow/sequence":{blocks:["FlowGraphSequenceBlock"],extraProcessor(a,b,c,d,e){let f=e[0];return f.config||(f.config={}),f.config.outputSignalCount=Object.keys(a.flows||[]).length,f.signalOutputs.forEach((a,b)=>{a.name="out_"+b}),e}},"flow/branch":{blocks:["FlowGraphBranchBlock"],outputs:{flows:{true:{name:"onTrue"},false:{name:"onFalse"}}}},"flow/switch":{blocks:["FlowGraphSwitchBlock"],configuration:{cases:{name:"cases",inOptions:!0,defaultValue:[]}},inputs:{values:{selection:{name:"case"},default:{name:"default"}}},validation(a){if(a.configuration&&a.configuration.cases){let b=a.configuration.cases.value;if(!b.every(a=>"number"==typeof a&&/^-?\d+$/.test(a.toString())))return u.V.Warn("Switch cases should be integers. Using empty array instead."),a.configuration.cases.value=[],{valid:!0};let c=new Set(b);a.configuration.cases.value=Array.from(c)}return{valid:!0}},extraProcessor(a,b,c,d,e){if("flow/switch"!==b.op||!a.flows||0===Object.keys(a.flows).length)throw Error("Switch should have a single configuration object, the cases array");return e[0].signalOutputs.forEach(a=>{"default"!==a.name&&(a.name="out_"+a.name)}),e}},"flow/while":{blocks:["FlowGraphWhileLoopBlock"],outputs:{flows:{loopBody:{name:"executionFlow"}}}},"flow/for":{blocks:["FlowGraphForLoopBlock"],configuration:{initialIndex:{name:"initialIndex",gltfType:"number",inOptions:!0,defaultValue:0}},inputs:{values:{startIndex:{name:"startIndex",gltfType:"number"},endIndex:{name:"endIndex",gltfType:"number"}}},outputs:{values:{index:{name:"index"}},flows:{loopBody:{name:"executionFlow"}}},extraProcessor(a,b,c,d,e){let f=e[0];return f.config||(f.config={}),f.config.incrementIndexWhenLoopDone=!0,e}},"flow/doN":{blocks:["FlowGraphDoNBlock"],configuration:{},inputs:{values:{n:{name:"maxExecutions",gltfType:"number"}}},outputs:{values:{currentCount:{name:"executionCount"}}}},"flow/multiGate":{blocks:["FlowGraphMultiGateBlock"],configuration:{isRandom:{name:"isRandom",gltfType:"boolean",inOptions:!0,defaultValue:!1},isLoop:{name:"isLoop",gltfType:"boolean",inOptions:!0,defaultValue:!1}},extraProcessor(a,b,c,d,e){if("flow/multiGate"!==b.op||!a.flows||0===Object.keys(a.flows).length)throw Error("MultiGate should have a single configuration object, the number of output flows");let f=e[0];return f.config||(f.config={}),f.config.outputSignalCount=Object.keys(a.flows).length,f.signalOutputs.forEach((a,b)=>{a.name="out_"+b}),e}},"flow/waitAll":{blocks:["FlowGraphWaitAllBlock"],configuration:{inputFlows:{name:"inputSignalCount",gltfType:"number",inOptions:!0,defaultValue:0}},inputs:{flows:{reset:{name:"reset"},"[segment]":{name:"in_$1"}}},validation:a=>("number"!=typeof a.configuration?.inputFlows?.value[0]&&(a.configuration=a.configuration||{inputFlows:{value:[0]}},a.configuration.inputFlows.value=[0]),{valid:!0})},"flow/throttle":{blocks:["FlowGraphThrottleBlock"],outputs:{flows:{err:{name:"error"}}}},"flow/setDelay":{blocks:["FlowGraphSetDelayBlock"],outputs:{flows:{err:{name:"error"}}}},"flow/cancelDelay":{blocks:["FlowGraphCancelDelayBlock"]},"variable/get":{blocks:["FlowGraphGetVariableBlock"],validation:a=>a.configuration?.variable?.value?{valid:!0}:(u.V.Error("Variable get block should have a variable configuration"),{valid:!1,error:"Variable get block should have a variable configuration"}),configuration:{variable:{name:"variable",gltfType:"number",flowGraphType:"string",inOptions:!0,isVariable:!0,dataTransformer:(a,b)=>[b.getVariableName(a[0])]}}},"variable/set":{blocks:["FlowGraphSetVariableBlock"],configuration:{variable:{name:"variable",gltfType:"number",flowGraphType:"string",inOptions:!0,isVariable:!0,dataTransformer:(a,b)=>[b.getVariableName(a[0])]}}},"variable/setMultiple":{blocks:["FlowGraphSetVariableBlock"],configuration:{variables:{name:"variables",gltfType:"number",flowGraphType:"string",inOptions:!0,dataTransformer:(a,b)=>[a[0].map(a=>b.getVariableName(a))]}},extraProcessor:(a,b,c,d,e)=>(e[0].dataInputs.forEach(a=>{a.name=d.getVariableName(+a.name)}),e)},"variable/interpolate":{blocks:["FlowGraphInterpolationBlock","FlowGraphContextBlock","FlowGraphPlayAnimationBlock","FlowGraphBezierCurveEasing","FlowGraphGetVariableBlock"],configuration:{variable:{name:"propertyName",inOptions:!0,isVariable:!0,dataTransformer:(a,b)=>[b.getVariableName(a[0])]},useSlerp:{name:"animationType",inOptions:!0,defaultValue:!1,dataTransformer:a=>!0===a[0]?["Quaternion"]:[void 0]}},inputs:{values:{value:{name:"value_1"},duration:{name:"duration_1",gltfType:"number"},p1:{name:"controlPoint1",toBlock:"FlowGraphBezierCurveEasing"},p2:{name:"controlPoint2",toBlock:"FlowGraphBezierCurveEasing"}},flows:{in:{name:"in",toBlock:"FlowGraphPlayAnimationBlock"}}},outputs:{flows:{err:{name:"error",toBlock:"FlowGraphPlayAnimationBlock"},out:{name:"out",toBlock:"FlowGraphPlayAnimationBlock"},done:{name:"done",toBlock:"FlowGraphPlayAnimationBlock"}}},interBlockConnectors:[{input:"object",output:"userVariables",inputBlockIndex:2,outputBlockIndex:1,isVariable:!0},{input:"animation",output:"animation",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0},{input:"easingFunction",output:"easingFunction",inputBlockIndex:0,outputBlockIndex:3,isVariable:!0},{input:"value_0",output:"value",inputBlockIndex:0,outputBlockIndex:4,isVariable:!0}],extraProcessor(a,b,c,d,e){var f,g;let h=e[0],i=a.configuration?.variable.value[0];if("number"!=typeof i)throw u.V.Error("Variable index is not defined for variable interpolation block"),Error("Variable index is not defined for variable interpolation block");let j=d.arrays.staticVariables[i];void 0===h.config.animationType.value&&(d.arrays.staticVariables,h.config.animationType.value=(0,ct.U_)(j.type));let k=e[4];return k.config||(k.config={}),(f=k.config).variable||(f.variable={}),k.config.variable.value=d.getVariableName(i),(g=e[3]).config||(g.config={}),e}},"pointer/get":{blocks:["FlowGraphGetPropertyBlock","FlowGraphJsonPointerParserBlock"],configuration:{pointer:{name:"jsonPointer",toBlock:"FlowGraphJsonPointerParserBlock"}},inputs:{values:{"[segment]":{name:"$1",toBlock:"FlowGraphJsonPointerParserBlock"}}},interBlockConnectors:[{input:"object",output:"object",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"propertyName",output:"propertyName",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"customGetFunction",output:"getFunction",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0}],extraProcessor:(a,b,c,d,e)=>(e.forEach(a=>{"FlowGraphJsonPointerParserBlock"===a.className&&(a.config||(a.config={}),a.config.outputValue=!0)}),e)},"pointer/set":{blocks:["FlowGraphSetPropertyBlock","FlowGraphJsonPointerParserBlock"],configuration:{pointer:{name:"jsonPointer",toBlock:"FlowGraphJsonPointerParserBlock"}},inputs:{values:{value:{name:"value"},"[segment]":{name:"$1",toBlock:"FlowGraphJsonPointerParserBlock"}}},outputs:{flows:{err:{name:"error"}}},interBlockConnectors:[{input:"object",output:"object",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"propertyName",output:"propertyName",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"customSetFunction",output:"setFunction",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0}],extraProcessor:(a,b,c,d,e)=>(e.forEach(a=>{"FlowGraphJsonPointerParserBlock"===a.className&&(a.config||(a.config={}),a.config.outputValue=!0)}),e)},"pointer/interpolate":{blocks:["FlowGraphInterpolationBlock","FlowGraphJsonPointerParserBlock","FlowGraphPlayAnimationBlock","FlowGraphBezierCurveEasing"],configuration:{pointer:{name:"jsonPointer",toBlock:"FlowGraphJsonPointerParserBlock"}},inputs:{values:{value:{name:"value_1"},"[segment]":{name:"$1",toBlock:"FlowGraphJsonPointerParserBlock"},duration:{name:"duration_1",gltfType:"number"},p1:{name:"controlPoint1",toBlock:"FlowGraphBezierCurveEasing"},p2:{name:"controlPoint2",toBlock:"FlowGraphBezierCurveEasing"}},flows:{in:{name:"in",toBlock:"FlowGraphPlayAnimationBlock"}}},outputs:{flows:{err:{name:"error",toBlock:"FlowGraphPlayAnimationBlock"},out:{name:"out",toBlock:"FlowGraphPlayAnimationBlock"},done:{name:"done",toBlock:"FlowGraphPlayAnimationBlock"}}},interBlockConnectors:[{input:"object",output:"object",inputBlockIndex:2,outputBlockIndex:1,isVariable:!0},{input:"propertyName",output:"propertyName",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"customBuildAnimation",output:"generateAnimationsFunction",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"animation",output:"animation",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0},{input:"easingFunction",output:"easingFunction",inputBlockIndex:0,outputBlockIndex:3,isVariable:!0},{input:"value_0",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0}],extraProcessor:(a,b,c,d,e)=>(e.forEach(b=>{"FlowGraphJsonPointerParserBlock"===b.className?(b.config||(b.config={}),b.config.outputValue=!0):"FlowGraphInterpolationBlock"===b.className&&(b.config||(b.config={}),Object.keys(a.values||[]).forEach(c=>{let e=a.values?.[c];if("value"===c&&e){let a=e.type;void 0!==a&&(b.config.animationType=d.arrays.types[a].flowGraphType)}}))}),e)},"animation/start":{blocks:["FlowGraphPlayAnimationBlock","FlowGraphArrayIndexBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],inputs:{values:{animation:{name:"index",gltfType:"number",toBlock:"FlowGraphArrayIndexBlock"},speed:{name:"speed",gltfType:"number"},startTime:{name:"from",gltfType:"number",dataTransformer:(a,b)=>[a[0]*b._animationTargetFps]},endTime:{name:"to",gltfType:"number",dataTransformer:(a,b)=>[a[0]*b._animationTargetFps]}}},outputs:{flows:{err:{name:"error"}}},interBlockConnectors:[{input:"animationGroup",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"animationGroups",inputBlockIndex:1,outputBlockIndex:2,isVariable:!0}],extraProcessor(a,b,c,d,e,f,g){let h=e[e.length-1];return h.config||(h.config={}),h.config.glTF=g,e}},"animation/stop":{blocks:["FlowGraphStopAnimationBlock","FlowGraphArrayIndexBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],inputs:{values:{animation:{name:"index",gltfType:"number",toBlock:"FlowGraphArrayIndexBlock"}}},outputs:{flows:{err:{name:"error"}}},interBlockConnectors:[{input:"animationGroup",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"animationGroups",inputBlockIndex:1,outputBlockIndex:2,isVariable:!0}],extraProcessor(a,b,c,d,e,f,g){let h=e[e.length-1];return h.config||(h.config={}),h.config.glTF=g,e}},"animation/stopAt":{blocks:["FlowGraphStopAnimationBlock","FlowGraphArrayIndexBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{},inputs:{values:{animation:{name:"index",gltfType:"number",toBlock:"FlowGraphArrayIndexBlock"},stopTime:{name:"stopAtFrame",gltfType:"number",dataTransformer:(a,b)=>[a[0]*b._animationTargetFps]}}},outputs:{flows:{err:{name:"error"}}},interBlockConnectors:[{input:"animationGroup",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"animationGroups",inputBlockIndex:1,outputBlockIndex:2,isVariable:!0}],extraProcessor(a,b,c,d,e,f,g){let h=e[e.length-1];return h.config||(h.config={}),h.config.glTF=g,e}},"math/switch":{blocks:["FlowGraphDataSwitchBlock"],configuration:{cases:{name:"cases",inOptions:!0,defaultValue:[]}},inputs:{values:{selection:{name:"case"}}},validation(a){if(a.configuration&&a.configuration.cases){let b=a.configuration.cases.value;if(!b.every(a=>"number"==typeof a&&/^-?\d+$/.test(a.toString())))return u.V.Warn("Switch cases should be integers. Using empty array instead."),a.configuration.cases.value=[],{valid:!0};let c=new Set(b);a.configuration.cases.value=Array.from(c)}return{valid:!0}},extraProcessor(a,b,c,d,e){let f=e[0];return f.dataInputs.forEach(a=>{"default"!==a.name&&"case"!==a.name&&(a.name="in_"+a.name)}),f.config||(f.config={}),f.config.treatCasesAsIntegers=!0,e}},"debug/log":{blocks:["FlowGraphConsoleLogBlock"],configuration:{message:{name:"messageTemplate",inOptions:!0}}}};function cz(a,b=["a"],c){return{blocks:[a],inputs:{values:b.reduce((a,b)=>(a[b]={name:b},a),{})},outputs:{values:{value:{name:"value"}}},extraProcessor(a,b,d,e,f){var g;if(c){(g=f[0]).config||(g.config={}),f[0].config.preventIntegerFloatArithmetic=!0;let b=-1;Object.keys(a.values||{}).find(c=>a.values?.[c].type!==void 0&&(b=a.values[c].type,!0)),-1!==b&&(f[0].config.type=e.arrays.types[b].flowGraphType)}return f},validation:a=>c?cA(a):{valid:!0}}}function cA(a){if(a.values){let b=Object.keys(a.values).map(b=>a.values[b].type).filter(a=>void 0!==a);if(!b.every(a=>a===b[0]))return{valid:!1,error:"All inputs must be of the same type"}}return{valid:!0}}cy["math/compose"]=cy["math/matCompose"],cy["math/decompose"]=cy["math/matDecompose"];var cB=c(15428);let cC={float:{length:1,flowGraphType:"number",elementType:"number"},bool:{length:1,flowGraphType:"boolean",elementType:"boolean"},float2:{length:2,flowGraphType:"Vector2",elementType:"number"},float3:{length:3,flowGraphType:"Vector3",elementType:"number"},float4:{length:4,flowGraphType:"Vector4",elementType:"number"},float4x4:{length:16,flowGraphType:"Matrix",elementType:"number"},float2x2:{length:4,flowGraphType:"Matrix2D",elementType:"number"},float3x3:{length:9,flowGraphType:"Matrix3D",elementType:"number"},int:{length:1,flowGraphType:"FlowGraphInteger",elementType:"number"}};class cD{constructor(a,b,c=60){this._interactivityGraph=a,this._gltf=b,this._animationTargetFps=c,this._types=[],this._mappings=[],this._staticVariables=[],this._events=[],this._internalEventsCounter=0,this._nodes=[],this._parseTypes(),this._parseDeclarations(),this._parseVariables(),this._parseEvents(),this._parseNodes()}get arrays(){return{types:this._types,mappings:this._mappings,staticVariables:this._staticVariables,events:this._events,nodes:this._nodes}}_parseTypes(){if(this._interactivityGraph.types)for(let a of this._interactivityGraph.types)this._types.push(cC[a.signature])}_parseDeclarations(){if(this._interactivityGraph.declarations)for(let a of this._interactivityGraph.declarations){let b=cv(a);if(!b)throw u.V.Error(["No mapping found for declaration",a]),Error("Error parsing declarations");this._mappings.push({flowGraphMapping:b,fullOperationName:a.extension?a.op+":"+a.extension:a.op})}}_parseVariables(){if(this._interactivityGraph.variables)for(let a of this._interactivityGraph.variables){let b=this._parseVariable(a);this._staticVariables.push(b)}}_parseVariable(a,b){let c=this._types[a.type];if(!c)throw u.V.Error(["No type found for variable",a]),Error("Error parsing variables");if(a.value&&a.value.length!==c.length)throw u.V.Error(["Invalid value length for variable",a,c]),Error("Error parsing variables");let d=a.value||[];if(!d.length)switch(c.flowGraphType){case"boolean":d.push(!1);break;case"FlowGraphInteger":d.push(0);break;case"number":d.push(NaN);break;case"Vector2":d.push(NaN,NaN);break;case"Vector3":d.push(NaN,NaN,NaN);break;case"Vector4":case"Matrix2D":case"Quaternion":d.fill(NaN,0,4);break;case"Matrix":d.fill(NaN,0,16);break;case"Matrix3D":d.fill(NaN,0,9)}return"number"===c.elementType&&"string"==typeof d[0]&&(d[0]=parseFloat(d[0])),{type:c.flowGraphType,value:b?b(d,this):d}}_parseEvents(){if(this._interactivityGraph.events)for(let a of this._interactivityGraph.events){let b={eventId:a.id||"internalEvent_"+this._internalEventsCounter++};a.values&&(b.eventData=Object.keys(a.values).map(b=>{let c=a.values?.[b];if(!c)throw u.V.Error(["No value found for event key",b]),Error("Error parsing events");let d=this._types[c.type];if(!d)throw u.V.Error(["No type found for event value",c]),Error("Error parsing events");let e=void 0!==c.value?this._parseVariable(c):void 0;return{id:b,type:d.flowGraphType,eventData:!0,value:e}})),this._events.push(b)}}_parseNodes(){if(this._interactivityGraph.nodes)for(let a of this._interactivityGraph.nodes){if("number"!=typeof a.declaration)throw u.V.Error(["No declaration found for node",a]),Error("Error parsing nodes");let b=this._mappings[a.declaration];if(!b)throw u.V.Error(["No mapping found for node",a]),Error("Error parsing nodes");if(b.flowGraphMapping.validation){let c=b.flowGraphMapping.validation(a,this._interactivityGraph,this._gltf);if(!c.valid)throw Error(`Error validating interactivity node ${this._interactivityGraph.declarations?.[a.declaration].op} - ${c.error}`)}let c=[];for(let d of b.flowGraphMapping.blocks){let e=this._getEmptyBlock(d,b.fullOperationName);this._parseNodeConfiguration(a,e,b.flowGraphMapping,d),c.push(e)}this._nodes.push({blocks:c,fullOperationName:b.fullOperationName})}}_getEmptyBlock(a,b){return{uniqueId:(0,cB.z)(),className:a,dataInputs:[],dataOutputs:[],signalInputs:[],signalOutputs:[],config:{},type:b,metadata:{}}}_parseNodeConfiguration(a,b,c,d){let e=b.config;if(a.configuration)for(let b of Object.keys(a.configuration)){let f=a.configuration?.[b];if(!f)throw u.V.Error(["No value found for node configuration",b]),Error("Error parsing node configuration");let g=c.configuration?.[b];if(g&&g.toBlock?g.toBlock===d:0===c.blocks.indexOf(d)){let a=g?.name||b;f&&void 0!==f.value||void 0===g?.defaultValue?f.value.length>=0?e[a]={value:1===f.value.length?f.value[0]:f.value}:u.V.Warn(["Invalid value for node configuration",f]):e[a]={value:g.defaultValue},g&&g.dataTransformer&&(e[a].value=g.dataTransformer([e[a].value],this)[0])}}}_parseNodeConnections(a){for(let b=0;b<this._nodes.length;b++){let c=this._interactivityGraph.nodes?.[b];if(!c)throw u.V.Error(["No node found for interactivity node",this._nodes[b]]),Error("Error parsing node connections");let d=this._nodes[b],e=this._mappings[c.declaration];if(!e)throw u.V.Error(["No mapping found for node",c]),Error("Error parsing node connections");let f=c.flows||{};for(let a of Object.keys(f).sort()){let b=f[a],c=e.flowGraphMapping.outputs?.flows?.[a],g=c?.name||a,h=this._createNewSocketConnection(g,!0);(c&&c.toBlock&&d.blocks.find(a=>a.className===c.toBlock)||d.blocks[0]).signalOutputs.push(h);let i=b.node,j=this._nodes[i];if(!j)throw u.V.Error(["No node found for input node id",i]),Error("Error parsing node connections");let k=cu(j.fullOperationName);if(!k)throw u.V.Error(["No mapping found for input node",j]),Error("Error parsing node connections");let l=k.inputs?.flows?.[b.socket||"in"],m=!1;if(!l)for(let a in k.inputs?.flows)a.startsWith("[")&&a.endsWith("]")&&(m=!0,l=k.inputs?.flows?.[a]);let n=l?m?l.name.replace("$1",b.socket||""):l.name:b.socket||"in",o=l&&l.toBlock&&j.blocks.find(a=>a.className===l.toBlock)||j.blocks[0],p=o.signalInputs.find(a=>a.name===n);p||(p=this._createNewSocketConnection(n),o.signalInputs.push(p)),p.connectedPointIds.push(h.uniqueId),h.connectedPointIds.push(p.uniqueId)}let g=c.values||{};for(let b of Object.keys(g)){let c=g[b],f=e.flowGraphMapping.inputs?.values?.[b],h=!1;if(!f)for(let a in e.flowGraphMapping.inputs?.values)a.startsWith("[")&&a.endsWith("]")&&(h=!0,f=e.flowGraphMapping.inputs?.values?.[a]);let i=f?h?f.name.replace("$1",b):f.name:b,j=this._createNewSocketConnection(i);if((f&&f.toBlock&&d.blocks.find(a=>a.className===f.toBlock)||d.blocks[0]).dataInputs.push(j),void 0!==c.value){let b=this._parseVariable(c,f&&f.dataTransformer);a._connectionValues[j.uniqueId]=b}else if(void 0!==c.node){let a=c.node,b=c.socket||"value",d=this._nodes[a];if(!d)throw u.V.Error(["No node found for output socket reference",c]),Error("Error parsing node connections");let e=cu(d.fullOperationName);if(!e)throw u.V.Error(["No mapping found for output socket reference",c]),Error("Error parsing node connections");let f=e.outputs?.values?.[b],g=!1;if(!f)for(let a in e.outputs?.values)a.startsWith("[")&&a.endsWith("]")&&(g=!0,f=e.outputs?.values?.[a]);let h=f?g?f.name.replace("$1",b):f?.name:b,i=f&&f.toBlock&&d.blocks.find(a=>a.className===f.toBlock)||d.blocks[0],k=i.dataOutputs.find(a=>a.name===h);k||(k=this._createNewSocketConnection(h,!0),i.dataOutputs.push(k)),j.connectedPointIds.push(k.uniqueId),k.connectedPointIds.push(j.uniqueId)}else throw u.V.Error(["Invalid value for value connection",c]),Error("Error parsing node connections")}if(e.flowGraphMapping.interBlockConnectors)for(let a of e.flowGraphMapping.interBlockConnectors){let b=a.input,c=a.output,e=a.isVariable;this._connectFlowGraphNodes(b,c,d.blocks[a.inputBlockIndex],d.blocks[a.outputBlockIndex],e)}if(e.flowGraphMapping.extraProcessor){let b=this._interactivityGraph.declarations?.[c.declaration];if(!b)throw u.V.Error(["No declaration found for extra processor",c]),Error("Error parsing node connections");d.blocks=e.flowGraphMapping.extraProcessor(c,b,e.flowGraphMapping,this,d.blocks,a,this._gltf)}}}_createNewSocketConnection(a,b){return{uniqueId:(0,cB.z)(),name:a,_connectionType:+!!b,connectedPointIds:[]}}_connectFlowGraphNodes(a,b,c,d,e){let f=e?c.dataInputs:c.signalInputs,g=e?d.dataOutputs:d.signalOutputs,h=f.find(b=>b.name===a)||this._createNewSocketConnection(a),i=g.find(a=>a.name===b)||this._createNewSocketConnection(b,!0);f.find(b=>b.name===a)||f.push(h),g.find(a=>a.name===b)||g.push(i),h.connectedPointIds.push(i.uniqueId),i.connectedPointIds.push(h.uniqueId)}getVariableName(a){return"staticVariable_"+a}serializeToFlowGraph(){let a={uniqueId:(0,cB.z)(),_userVariables:{},_connectionValues:{}};this._parseNodeConnections(a);for(let b=0;b<this._staticVariables.length;b++){let c=this._staticVariables[b];a._userVariables[this.getVariableName(b)]=c}return{rightHanded:!0,allBlocks:this._nodes.reduce((a,b)=>a.concat(b.blocks),[]),executionContexts:[a]}}}var cE=c(9875);let cF="KHR_interactivity";class cG{constructor(a){this._loader=a,this.name=cF,this.enabled=this._loader.isExtensionUsed(cF),this._pathConverter=(0,aK.Wt)(this._loader.gltf),a._skipStartAnimationStep=!0;let b=a.babylonScene;b&&function(a){(0,aK.oR)("/extensions/KHR_interactivity/?/activeCamera/rotation",{get:()=>{if(!a.activeCamera)return new G.PT(NaN,NaN,NaN,NaN);let b=G.PT.FromRotationMatrix(a.activeCamera.getWorldMatrix()).normalize();return a.useRightHandedSystem||(b.w*=-1,b.x*=-1),b},type:"Quaternion",getTarget:()=>a.activeCamera}),(0,aK.oR)("/extensions/KHR_interactivity/?/activeCamera/position",{get:()=>{if(!a.activeCamera)return new G.Pq(NaN,NaN,NaN);let b=a.activeCamera.getWorldMatrix().getTranslation();return a.useRightHandedSystem||(b.x*=-1),b},type:"Vector3",getTarget:()=>a.activeCamera}),(0,aK.oR)("/animations/{}/extensions/KHR_interactivity/isPlaying",{get:a=>a._babylonAnimationGroup?.isPlaying??!1,type:"boolean",getTarget:a=>a._babylonAnimationGroup}),(0,aK.oR)("/animations/{}/extensions/KHR_interactivity/minTime",{get:a=>(a._babylonAnimationGroup?.from??0)/60,type:"number",getTarget:a=>a._babylonAnimationGroup}),(0,aK.oR)("/animations/{}/extensions/KHR_interactivity/maxTime",{get:a=>(a._babylonAnimationGroup?.to??0)/60,type:"number",getTarget:a=>a._babylonAnimationGroup}),(0,aK.oR)("/animations/{}/extensions/KHR_interactivity/playhead",{get:a=>(a._babylonAnimationGroup?.getCurrentFrame()??0)/60,type:"number",getTarget:a=>a._babylonAnimationGroup}),(0,aK.oR)("/animations/{}/extensions/KHR_interactivity/virtualPlayhead",{get:a=>(a._babylonAnimationGroup?.getCurrentFrame()??0)/60,type:"number",getTarget:a=>a._babylonAnimationGroup})}(b)}dispose(){this._loader=null,delete this._pathConverter}async onReady(){if(!this._loader.babylonScene||!this._pathConverter)return;let a=this._loader.babylonScene,b=this._loader.gltf.extensions?.KHR_interactivity;if(!b)return;let c=new cr.x({scene:a});c.dispatchEventsSynchronously=!1;let d=b.graphs.map(a=>new cD(a,this._loader.gltf,this._loader.parent.targetFps).serializeToFlowGraph());await Promise.all(d.map(async a=>await (0,cs.cB)(a,{coordinator:c,pathConverter:this._pathConverter}))),c.start()}}(0,cE.Q)(cF,"FlowGraphGLTFDataProvider",async()=>(await Promise.resolve().then(c.bind(c,78135))).FlowGraphGLTFDataProvider),aJ(cF),aI(cF,!0,a=>new cG(a));let cH="KHR_node_visibility";(0,aK.oR)("/nodes/{}/extensions/KHR_node_visibility/visible",{get:a=>{let b=a._babylonTransformNode;return!b||void 0===b.isVisible||b.isVisible},set:(a,b)=>{b._primitiveBabylonMeshes?.forEach(a=>{a.inheritVisibility=!0}),b._babylonTransformNode&&(b._babylonTransformNode.isVisible=a),b._primitiveBabylonMeshes?.forEach(b=>{b.isVisible=a})},getTarget:a=>a._babylonTransformNode,getPropertyName:[()=>"isVisible"],type:"boolean"});class cI{constructor(a){this.name=cH,this._loader=a,this.enabled=a.isExtensionUsed(cH)}async onReady(){this._loader.gltf.nodes?.forEach(a=>{a._primitiveBabylonMeshes?.forEach(a=>{a.inheritVisibility=!0}),a.extensions?.KHR_node_visibility&&a.extensions?.KHR_node_visibility.visible===!1&&(a._babylonTransformNode&&(a._babylonTransformNode.isVisible=!1),a._primitiveBabylonMeshes?.forEach(a=>{a.isVisible=!1}))})}dispose(){this._loader=null}}aJ(cH),aI(cH,!0,a=>new cI(a));let cJ="KHR_node_selectability";cw("event/onSelect",cJ,{blocks:["FlowGraphMeshPickEventBlock","FlowGraphGetVariableBlock","FlowGraphIndexOfBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{stopPropagation:{name:"stopPropagation"},nodeIndex:{name:"variable",toBlock:"FlowGraphGetVariableBlock",dataTransformer:a=>["pickedMesh_"+a[0]]}},outputs:{values:{selectedNodeIndex:{name:"index",toBlock:"FlowGraphIndexOfBlock"},controllerIndex:{name:"pointerId"},selectionPoint:{name:"pickedPoint"},selectionRayOrigin:{name:"pickOrigin"}},flows:{out:{name:"done"}}},interBlockConnectors:[{input:"asset",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"nodes",inputBlockIndex:2,outputBlockIndex:3,isVariable:!0},{input:"object",output:"pickedMesh",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0}],extraProcessor(a,b,c,d,e,f,g){let h=e[e.length-1];h.config=h.config||{},h.config.glTF=g;let i=a.configuration?.nodeIndex?.value[0];if(void 0===i||"number"!=typeof i)throw Error("nodeIndex not found in configuration");let j="pickedMesh_"+i;return e[1].config.variable=j,f._userVariables[j]={className:"Mesh",id:g?.nodes?.[i]._babylonTransformNode?.id,uniqueId:g?.nodes?.[i]._babylonTransformNode?.uniqueId},e}}),(0,aK.oR)("/nodes/{}/extensions/KHR_node_selectability/selectable",{get:a=>{let b=a._babylonTransformNode;return!b||void 0===b.isPickable||b.isPickable},set:(a,b)=>{b._primitiveBabylonMeshes?.forEach(b=>{b.isPickable=a})},getTarget:a=>a._babylonTransformNode,getPropertyName:[()=>"isPickable"],type:"boolean"});class cK{constructor(a){this.name=cJ,this._loader=a,this.enabled=a.isExtensionUsed(cJ)}async onReady(){this._loader.gltf.nodes?.forEach(a=>{a.extensions?.KHR_node_selectability&&a.extensions?.KHR_node_selectability.selectable===!1&&a._babylonTransformNode?.getChildMeshes().forEach(a=>{a.isPickable=!1})})}dispose(){this._loader=null}}aJ(cJ),aI(cJ,!0,a=>new cK(a));let cL="KHR_node_hoverability",cM="targetMeshPointerOver_";cw("event/onHoverIn",cL,{blocks:["FlowGraphPointerOverEventBlock","FlowGraphGetVariableBlock","FlowGraphIndexOfBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{stopPropagation:{name:"stopPropagation"},nodeIndex:{name:"variable",toBlock:"FlowGraphGetVariableBlock",dataTransformer:a=>[cM+a[0]]}},outputs:{values:{hoverNodeIndex:{name:"index",toBlock:"FlowGraphIndexOfBlock"},controllerIndex:{name:"pointerId"}},flows:{out:{name:"done"}}},interBlockConnectors:[{input:"targetMesh",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"nodes",inputBlockIndex:2,outputBlockIndex:3,isVariable:!0},{input:"object",output:"meshUnderPointer",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0}],extraProcessor(a,b,c,d,e,f,g){let h=e[e.length-1];h.config=h.config||{},h.config.glTF=g;let i=a.configuration?.nodeIndex?.value[0];if(void 0===i||"number"!=typeof i)throw Error("nodeIndex not found in configuration");let j=cM+i;return e[1].config.variable=j,f._userVariables[j]={className:"Mesh",id:g?.nodes?.[i]._babylonTransformNode?.id,uniqueId:g?.nodes?.[i]._babylonTransformNode?.uniqueId},e}});let cN="targetMeshPointerOut_";cw("event/onHoverOut",cL,{blocks:["FlowGraphPointerOutEventBlock","FlowGraphGetVariableBlock","FlowGraphIndexOfBlock","KHR_interactivity/FlowGraphGLTFDataProvider"],configuration:{stopPropagation:{name:"stopPropagation"},nodeIndex:{name:"variable",toBlock:"FlowGraphGetVariableBlock",dataTransformer:a=>[cN+a[0]]}},outputs:{values:{hoverNodeIndex:{name:"index",toBlock:"FlowGraphIndexOfBlock"},controllerIndex:{name:"pointerId"}},flows:{out:{name:"done"}}},interBlockConnectors:[{input:"targetMesh",output:"value",inputBlockIndex:0,outputBlockIndex:1,isVariable:!0},{input:"array",output:"nodes",inputBlockIndex:2,outputBlockIndex:3,isVariable:!0},{input:"object",output:"meshOutOfPointer",inputBlockIndex:2,outputBlockIndex:0,isVariable:!0}],extraProcessor(a,b,c,d,e,f,g){let h=e[e.length-1];h.config=h.config||{},h.config.glTF=g;let i=a.configuration?.nodeIndex?.value[0];if(void 0===i||"number"!=typeof i)throw Error("nodeIndex not found in configuration");let j=cN+i;return e[1].config.variable=j,f._userVariables[j]={className:"Mesh",id:g?.nodes?.[i]._babylonTransformNode?.id,uniqueId:g?.nodes?.[i]._babylonTransformNode?.uniqueId},e}}),(0,aK.oR)("/nodes/{}/extensions/KHR_node_hoverability/hoverable",{get:a=>{let b=a._babylonTransformNode;return!b||void 0===b.pointerOverDisableMeshTesting||b.pointerOverDisableMeshTesting},set:(a,b)=>{b._primitiveBabylonMeshes?.forEach(b=>{b.pointerOverDisableMeshTesting=!a})},getTarget:a=>a._babylonTransformNode,getPropertyName:[()=>"pointerOverDisableMeshTesting"],type:"boolean"});class cO{constructor(a){this.name=cL,this._loader=a,this.enabled=a.isExtensionUsed(cL)}async onReady(){this._loader.gltf.nodes?.forEach(a=>{a.extensions?.KHR_node_hoverability&&a.extensions?.KHR_node_hoverability.hoverable===!1&&a._babylonTransformNode?.getChildMeshes().forEach(a=>{a.pointerOverDisableMeshTesting=!0})})}dispose(){this._loader=null}}aJ(cL),aI(cL,!0,a=>new cO(a));let cP="ExtrasAsMetadata";class cQ{_assignExtras(a,b){if(b.extras&&Object.keys(b.extras).length>0){let c=a.metadata=a.metadata||{};(c.gltf=c.gltf||{}).extras=b.extras}}constructor(a){this.name=cP,this.enabled=!0,this._loader=a}dispose(){this._loader=null}loadNodeAsync(a,b,c){return this._loader.loadNodeAsync(a,b,a=>{this._assignExtras(a,b),c(a)})}loadCameraAsync(a,b,c){return this._loader.loadCameraAsync(a,b,a=>{this._assignExtras(a,b),c(a)})}createMaterial(a,b,c){let d=this._loader.createMaterial(a,b,c);return this._assignExtras(d,b),d}}aJ(cP),aI(cP,!1,a=>new cQ(a)),c(78135);var cR=c(40532);c(31317),c(16217);class cS{constructor(){}}class cT extends Q.F{AttachAfterBind(a,b){if(this._newUniformInstances)for(let a in this._newUniformInstances){let c=a.toString().split("-");"vec2"==c[0]?b.setVector2(c[1],this._newUniformInstances[a]):"vec3"==c[0]?this._newUniformInstances[a]instanceof H.v9?b.setColor3(c[1],this._newUniformInstances[a]):b.setVector3(c[1],this._newUniformInstances[a]):"vec4"==c[0]?(this._newUniformInstances[a]instanceof H.ov?b.setDirectColor4(c[1],this._newUniformInstances[a]):b.setVector4(c[1],this._newUniformInstances[a]),b.setVector4(c[1],this._newUniformInstances[a])):"mat4"==c[0]?b.setMatrix(c[1],this._newUniformInstances[a]):"float"==c[0]&&b.setFloat(c[1],this._newUniformInstances[a])}if(this._newSamplerInstances)for(let a in this._newSamplerInstances){let c=a.toString().split("-");"sampler2D"==c[0]&&this._newSamplerInstances[a].isReady&&this._newSamplerInstances[a].isReady()&&b.setTexture(c[1],this._newSamplerInstances[a])}}ReviewUniform(a,b){if("uniform"==a&&this._newUniforms)for(let a=0;a<this._newUniforms.length;a++)-1==this._customUniform[a].indexOf("sampler")&&b.push(this._newUniforms[a].replace(/\[\d*\]/g,""));if("sampler"==a&&this._newUniforms)for(let a=0;a<this._newUniforms.length;a++)-1!=this._customUniform[a].indexOf("sampler")&&b.push(this._newUniforms[a].replace(/\[\d*\]/g,""));return b}Builder(a,b,c,d,e,f){f&&this._customAttributes&&this._customAttributes.length>0&&f.push(...this._customAttributes),this.ReviewUniform("uniform",b),this.ReviewUniform("sampler",d);let g=this._createdShaderName;return N.M.ShadersStore[g+"VertexShader"]&&N.M.ShadersStore[g+"PixelShader"]||(N.M.ShadersStore[g+"VertexShader"]=this._injectCustomCode(this.VertexShader,"vertex"),N.M.ShadersStore[g+"PixelShader"]=this._injectCustomCode(this.FragmentShader,"fragment")),g}_injectCustomCode(a,b){let c=this._getCustomCode(b);for(let b in c){let d=c[b];if(d&&d.length>0){let c="#define "+b;a=a.replace(c,"\n"+d+"\n"+c)}}return a}_getCustomCode(a){return"vertex"===a?{CUSTOM_VERTEX_BEGIN:this.CustomParts.Vertex_Begin,CUSTOM_VERTEX_DEFINITIONS:(this._customUniform?.join("\n")||"")+(this.CustomParts.Vertex_Definitions||""),CUSTOM_VERTEX_MAIN_BEGIN:this.CustomParts.Vertex_MainBegin,CUSTOM_VERTEX_UPDATE_POSITION:this.CustomParts.Vertex_Before_PositionUpdated,CUSTOM_VERTEX_UPDATE_NORMAL:this.CustomParts.Vertex_Before_NormalUpdated,CUSTOM_VERTEX_MAIN_END:this.CustomParts.Vertex_MainEnd,CUSTOM_VERTEX_UPDATE_WORLDPOS:this.CustomParts.Vertex_After_WorldPosComputed}:{CUSTOM_FRAGMENT_BEGIN:this.CustomParts.Fragment_Begin,CUSTOM_FRAGMENT_DEFINITIONS:(this._customUniform?.join("\n")||"")+(this.CustomParts.Fragment_Definitions||""),CUSTOM_FRAGMENT_MAIN_BEGIN:this.CustomParts.Fragment_MainBegin,CUSTOM_FRAGMENT_UPDATE_DIFFUSE:this.CustomParts.Fragment_Custom_Diffuse,CUSTOM_FRAGMENT_UPDATE_ALPHA:this.CustomParts.Fragment_Custom_Alpha,CUSTOM_FRAGMENT_BEFORE_LIGHTS:this.CustomParts.Fragment_Before_Lights,CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR:this.CustomParts.Fragment_Before_FragColor,CUSTOM_FRAGMENT_MAIN_END:this.CustomParts.Fragment_MainEnd,CUSTOM_FRAGMENT_BEFORE_FOG:this.CustomParts.Fragment_Before_Fog}}constructor(a,b){super(a,b,!0),this.CustomParts=new cS,this.customShaderNameResolve=this.Builder,this.FragmentShader=N.M.ShadersStore.defaultPixelShader,this.VertexShader=N.M.ShadersStore.defaultVertexShader,cT.ShaderIndexer++,this._createdShaderName="custom_"+cT.ShaderIndexer}_afterBind(a,b=null,c){if(b){this.AttachAfterBind(a,b);try{super._afterBind(a,b,c)}catch(a){}}}AddUniform(a,b,c){return this._customUniform||(this._customUniform=[],this._newUniforms=[],this._newSamplerInstances={},this._newUniformInstances={}),c&&(-1!=b.indexOf("sampler")?this._newSamplerInstances[b+"-"+a]=c:this._newUniformInstances[b+"-"+a]=c),this._customUniform.push("uniform "+b+" "+a+";"),this._newUniforms.push(a),this}AddAttribute(a){return this._customAttributes||(this._customAttributes=[]),this._customAttributes.push(a),this}Fragment_Begin(a){return this.CustomParts.Fragment_Begin=a,this}Fragment_Definitions(a){return this.CustomParts.Fragment_Definitions=a,this}Fragment_MainBegin(a){return this.CustomParts.Fragment_MainBegin=a,this}Fragment_MainEnd(a){return this.CustomParts.Fragment_MainEnd=a,this}Fragment_Custom_Diffuse(a){return this.CustomParts.Fragment_Custom_Diffuse=a.replace("result","diffuseColor"),this}Fragment_Custom_Alpha(a){return this.CustomParts.Fragment_Custom_Alpha=a.replace("result","alpha"),this}Fragment_Before_Lights(a){return this.CustomParts.Fragment_Before_Lights=a,this}Fragment_Before_Fog(a){return this.CustomParts.Fragment_Before_Fog=a,this}Fragment_Before_FragColor(a){return this.CustomParts.Fragment_Before_FragColor=a.replace("result","color"),this}Vertex_Begin(a){return this.CustomParts.Vertex_Begin=a,this}Vertex_Definitions(a){return this.CustomParts.Vertex_Definitions=a,this}Vertex_MainBegin(a){return this.CustomParts.Vertex_MainBegin=a,this}Vertex_Before_PositionUpdated(a){return this.CustomParts.Vertex_Before_PositionUpdated=a.replace("result","positionUpdated"),this}Vertex_Before_NormalUpdated(a){return this.CustomParts.Vertex_Before_NormalUpdated=a.replace("result","normalUpdated"),this}Vertex_After_WorldPosComputed(a){return this.CustomParts.Vertex_After_WorldPosComputed=a,this}Vertex_MainEnd(a){return this.CustomParts.Vertex_MainEnd=a,this}}cT.ShaderIndexer=1,(0,cR.Y5)("BABYLON.CustomMaterial",cT);var cU=c(71709);c(85420),c(71488);class cV{constructor(){}}class cW extends aC.Y{AttachAfterBind(a,b){if(this._newUniformInstances)for(let a in this._newUniformInstances){let c=a.toString().split("-");"vec2"==c[0]?b.setVector2(c[1],this._newUniformInstances[a]):"vec3"==c[0]?this._newUniformInstances[a]instanceof H.v9?b.setColor3(c[1],this._newUniformInstances[a]):b.setVector3(c[1],this._newUniformInstances[a]):"vec4"==c[0]?(this._newUniformInstances[a]instanceof H.ov?b.setDirectColor4(c[1],this._newUniformInstances[a]):b.setVector4(c[1],this._newUniformInstances[a]),b.setVector4(c[1],this._newUniformInstances[a])):"mat4"==c[0]?b.setMatrix(c[1],this._newUniformInstances[a]):"float"==c[0]&&b.setFloat(c[1],this._newUniformInstances[a])}if(this._newSamplerInstances)for(let a in this._newSamplerInstances){let c=a.toString().split("-");"sampler2D"==c[0]&&this._newSamplerInstances[a].isReady&&this._newSamplerInstances[a].isReady()&&b.setTexture(c[1],this._newSamplerInstances[a])}}ReviewUniform(a,b){if("uniform"==a&&this._newUniforms)for(let a=0;a<this._newUniforms.length;a++)-1==this._customUniform[a].indexOf("sampler")&&b.push(this._newUniforms[a].replace(/\[\d*\]/g,""));if("sampler"==a&&this._newUniforms)for(let a=0;a<this._newUniforms.length;a++)-1!=this._customUniform[a].indexOf("sampler")&&b.push(this._newUniforms[a].replace(/\[\d*\]/g,""));return b}Builder(a,b,c,d,e,f,g){if(g){let a=g.processFinalCode;g.processFinalCode=(b,c)=>{if("vertex"===b)return a?a(b,c):c;let d=new cU.Y(c);return d.inlineToken="#define pbr_inline",d.processCode(),a?a(b,d.code):d.code}}f&&this._customAttributes&&this._customAttributes.length>0&&f.push(...this._customAttributes),this.ReviewUniform("uniform",b),this.ReviewUniform("sampler",d);let h=this._createdShaderName;return N.M.ShadersStore[h+"VertexShader"]&&N.M.ShadersStore[h+"PixelShader"]||(N.M.ShadersStore[h+"VertexShader"]=this._injectCustomCode(this.VertexShader,"vertex"),N.M.ShadersStore[h+"PixelShader"]=this._injectCustomCode(this.FragmentShader,"fragment")),h}_injectCustomCode(a,b){let c=this._getCustomCode(b);for(let b in c){let d=c[b];if(d&&d.length>0){let c="#define "+b;a=a.replace(c,"\n"+d+"\n"+c)}}return a}_getCustomCode(a){return"vertex"===a?{CUSTOM_VERTEX_BEGIN:this.CustomParts.Vertex_Begin,CUSTOM_VERTEX_DEFINITIONS:(this._customUniform?.join("\n")||"")+(this.CustomParts.Vertex_Definitions||""),CUSTOM_VERTEX_MAIN_BEGIN:this.CustomParts.Vertex_MainBegin,CUSTOM_VERTEX_UPDATE_POSITION:this.CustomParts.Vertex_Before_PositionUpdated,CUSTOM_VERTEX_UPDATE_NORMAL:this.CustomParts.Vertex_Before_NormalUpdated,CUSTOM_VERTEX_MAIN_END:this.CustomParts.Vertex_MainEnd,CUSTOM_VERTEX_UPDATE_WORLDPOS:this.CustomParts.Vertex_After_WorldPosComputed}:{CUSTOM_FRAGMENT_BEGIN:this.CustomParts.Fragment_Begin,CUSTOM_FRAGMENT_MAIN_BEGIN:this.CustomParts.Fragment_MainBegin,CUSTOM_FRAGMENT_DEFINITIONS:(this._customUniform?.join("\n")||"")+(this.CustomParts.Fragment_Definitions||""),CUSTOM_FRAGMENT_UPDATE_ALBEDO:this.CustomParts.Fragment_Custom_Albedo,CUSTOM_FRAGMENT_UPDATE_ALPHA:this.CustomParts.Fragment_Custom_Alpha,CUSTOM_FRAGMENT_BEFORE_LIGHTS:this.CustomParts.Fragment_Before_Lights,CUSTOM_FRAGMENT_UPDATE_METALLICROUGHNESS:this.CustomParts.Fragment_Custom_MetallicRoughness,CUSTOM_FRAGMENT_UPDATE_MICROSURFACE:this.CustomParts.Fragment_Custom_MicroSurface,CUSTOM_FRAGMENT_BEFORE_FINALCOLORCOMPOSITION:this.CustomParts.Fragment_Before_FinalColorComposition,CUSTOM_FRAGMENT_BEFORE_FRAGCOLOR:this.CustomParts.Fragment_Before_FragColor,CUSTOM_FRAGMENT_MAIN_END:this.CustomParts.Fragment_MainEnd,CUSTOM_FRAGMENT_BEFORE_FOG:this.CustomParts.Fragment_Before_Fog}}constructor(a,b){super(a,b,!0),this.CustomParts=new cV,this.customShaderNameResolve=this.Builder,this.FragmentShader=N.M.ShadersStore.pbrPixelShader,this.VertexShader=N.M.ShadersStore.pbrVertexShader,this.FragmentShader=this.FragmentShader.replace(/#include<pbrBlockAlbedoOpacity>/g,N.M.IncludesShadersStore.pbrBlockAlbedoOpacity),this.FragmentShader=this.FragmentShader.replace(/#include<pbrBlockReflectivity>/g,N.M.IncludesShadersStore.pbrBlockReflectivity),this.FragmentShader=this.FragmentShader.replace(/#include<pbrBlockFinalColorComposition>/g,N.M.IncludesShadersStore.pbrBlockFinalColorComposition),cW.ShaderIndexer++,this._createdShaderName="custompbr_"+cW.ShaderIndexer}_afterBind(a,b=null,c){if(b){this.AttachAfterBind(a,b);try{super._afterBind(a,b,c)}catch(a){}}}AddUniform(a,b,c){return this._customUniform||(this._customUniform=[],this._newUniforms=[],this._newSamplerInstances={},this._newUniformInstances={}),c&&(-1!=b.indexOf("sampler")?this._newSamplerInstances[b+"-"+a]=c:this._newUniformInstances[b+"-"+a]=c),this._customUniform.push("uniform "+b+" "+a+";"),this._newUniforms.push(a),this}AddAttribute(a){return this._customAttributes||(this._customAttributes=[]),this._customAttributes.push(a),this}Fragment_Begin(a){return this.CustomParts.Fragment_Begin=a,this}Fragment_Definitions(a){return this.CustomParts.Fragment_Definitions=a,this}Fragment_MainBegin(a){return this.CustomParts.Fragment_MainBegin=a,this}Fragment_Custom_Albedo(a){return this.CustomParts.Fragment_Custom_Albedo=a.replace("result","surfaceAlbedo"),this}Fragment_Custom_Alpha(a){return this.CustomParts.Fragment_Custom_Alpha=a.replace("result","alpha"),this}Fragment_Before_Lights(a){return this.CustomParts.Fragment_Before_Lights=a,this}Fragment_Custom_MetallicRoughness(a){return this.CustomParts.Fragment_Custom_MetallicRoughness=a,this}Fragment_Custom_MicroSurface(a){return this.CustomParts.Fragment_Custom_MicroSurface=a,this}Fragment_Before_Fog(a){return this.CustomParts.Fragment_Before_Fog=a,this}Fragment_Before_FinalColorComposition(a){return this.CustomParts.Fragment_Before_FinalColorComposition=a,this}Fragment_Before_FragColor(a){return this.CustomParts.Fragment_Before_FragColor=a.replace("result","color"),this}Fragment_MainEnd(a){return this.CustomParts.Fragment_MainEnd=a,this}Vertex_Begin(a){return this.CustomParts.Vertex_Begin=a,this}Vertex_Definitions(a){return this.CustomParts.Vertex_Definitions=a,this}Vertex_MainBegin(a){return this.CustomParts.Vertex_MainBegin=a,this}Vertex_Before_PositionUpdated(a){return this.CustomParts.Vertex_Before_PositionUpdated=a.replace("result","positionUpdated"),this}Vertex_Before_NormalUpdated(a){return this.CustomParts.Vertex_Before_NormalUpdated=a.replace("result","normalUpdated"),this}Vertex_After_WorldPosComputed(a){return this.CustomParts.Vertex_After_WorldPosComputed=a,this}Vertex_MainEnd(a){return this.CustomParts.Vertex_MainEnd=a,this}}cW.ShaderIndexer=1,(0,cR.Y5)("BABYLON.PBRCustomMaterial",cW),c(14850);class cX{constructor(a,b={}){this.renderingPipeline=null,this.webXRExperience=null,this.audioAnalyser=null,this.audioContext=null,this.showcaseObjects=[],this.particleSystems=[],this.animationGroups=[],this.performanceStats={fps:0,drawCalls:0,triangles:0},this.canvas=a,this.options=b,this.engine=new p.N$8(a,!0,{adaptToDeviceRatio:!0,antialias:!0,powerPreference:"high-performance",xrCompatible:b.enableWebXR}),this.scene=new p.Z58(this.engine),this.scene.useRightHandedSystem=!0,this.camera=new p.Lqh("showcaseCamera",-Math.PI/2,Math.PI/2.5,25,p.Pq0.Zero(),this.scene),this.camera.attachControl(a,!0),this.camera.wheelPrecision=50,this.camera.minZ=.1,this.camera.maxZ=1e3,this.initialize()}async initialize(){try{this.setupAdvancedLighting(),this.createMaterialShowcase(),this.setupAdvancedPostProcessing(),this.createAdvancedParticles(),this.options.enableWebXR&&await this.setupWebXR(),this.options.enableAudioReactive&&await this.setupAudioReactive(),this.startRenderLoop(),this.options.showPerformanceStats&&this.setupPerformanceMonitoring(),console.log("Advanced 3D Showcase initialized successfully")}catch(a){console.error("Error initializing Advanced 3D Showcase:",a)}}setupAdvancedLighting(){let a=new p.g4z("hemisphericLight",new p.Pq0(0,1,0),this.scene);a.intensity=.3,a.diffuse=new p.v9j(.2,.4,.8),a.specular=new p.v9j(.1,.2,.4);let b=new p.ZyN("directionalLight",new p.Pq0(-1,-1,-1),this.scene);b.intensity=1.2,b.diffuse=new p.v9j(1,.9,.8),b.specular=new p.v9j(1,1,1);let c=new p.owA(2048,b);c.useExponentialShadowMap=!0,c.darkness=.3;let d=new p.nCl("spotLight1",new p.Pq0(-10,15,-10),new p.Pq0(1,-1,1),Math.PI/3,2,this.scene);d.intensity=.8,d.diffuse=new p.v9j(0,1,1);let e=new p.nCl("spotLight2",new p.Pq0(10,15,10),new p.Pq0(-1,-1,-1),Math.PI/3,2,this.scene);e.intensity=.8,e.diffuse=new p.v9j(1,0,1);let f=new p.HiM("pointLight1",new p.Pq0(0,5,0),this.scene);f.intensity=1.5,f.diffuse=new p.v9j(1,.5,0),f.range=20,p.X55.CreateAndStartAnimation("pointLightAnimation",f,"position",30,120,f.position,new p.Pq0(8,5,8),p.X55.ANIMATIONLOOPMODE_YOYO)}createMaterialShowcase(){[{type:"sphere",position:new p.Pq0(-8,2,0),material:"chrome"},{type:"box",position:new p.Pq0(-4,2,0),material:"gold"},{type:"cylinder",position:new p.Pq0(0,2,0),material:"glass"},{type:"torus",position:new p.Pq0(4,2,0),material:"carbon"},{type:"dodecahedron",position:new p.Pq0(8,2,0),material:"iridescent"},{type:"ground",position:new p.Pq0(0,0,0),material:"marble"}].forEach((a,b)=>{let c;switch(a.type){case"sphere":c=p.PeD.CreateSphere(`showcase_sphere_${b}`,{diameter:3,segments:32},this.scene);break;case"box":c=p.PeD.CreateBox(`showcase_box_${b}`,{size:2.5},this.scene);break;case"cylinder":c=p.PeD.CreateCylinder(`showcase_cylinder_${b}`,{height:3,diameter:2.5,tessellation:24},this.scene);break;case"torus":c=p.PeD.CreateTorus(`showcase_torus_${b}`,{diameter:3,thickness:1,tessellation:32},this.scene);break;case"dodecahedron":c=p.PeD.CreatePolyhedron(`showcase_dodecahedron_${b}`,{type:2,size:1.5},this.scene);break;case"ground":c=p.PeD.CreateGround(`showcase_ground_${b}`,{width:30,height:30,subdivisions:32},this.scene);break;default:return}c.position=a.position,c.material=this.createAdvancedMaterial(a.material,`${a.material}_${b}`),"ground"!==a.type&&(p.X55.CreateAndStartAnimation(`float_${b}`,c,"position.y",30,120+10*b,c.position.y,c.position.y+1+.3*Math.sin(b),p.X55.ANIMATIONLOOPMODE_YOYO),p.X55.CreateAndStartAnimation(`rotate_${b}`,c,"rotation.y",30,180+20*b,0,2*Math.PI,p.X55.ANIMATIONLOOPMODE_CYCLE)),this.showcaseObjects.push(c)})}createAdvancedMaterial(a,b){let c=new p.YOq(b,this.scene),d=this.createEnvironmentTexture();switch(this.scene.environmentTexture=d,a){case"chrome":c.albedoColor=new p.v9j(.9,.9,.9),c.metallic=1,c.roughness=.05,c.environmentIntensity=1.5;break;case"gold":c.albedoColor=new p.v9j(1,.8,.2),c.metallic=1,c.roughness=.1,c.environmentIntensity=1.2;break;case"glass":c.albedoColor=new p.v9j(.95,.98,1),c.metallic=0,c.roughness=0,c.alpha=.15,c.indexOfRefraction=1.52,c.linkRefractionWithTransparency=!0,c.subSurface.isRefractionEnabled=!0,c.subSurface.refractionIntensity=1;break;case"carbon":c.albedoColor=new p.v9j(.1,.1,.1),c.metallic=.8,c.roughness=.3,c.anisotropy.isEnabled=!0,c.anisotropy.intensity=1,c.anisotropy.direction=new p.I9Y(1,0);break;case"iridescent":c.albedoColor=new p.v9j(.05,.05,.05),c.metallic=1,c.roughness=0,c.iridescence.isEnabled=!0,c.iridescence.intensity=1,c.iridescence.indexOfRefraction=1.3,c.iridescence.minimumThickness=100,c.iridescence.maximumThickness=400;break;case"marble":c.albedoColor=new p.v9j(.9,.9,.85),c.metallic=0,c.roughness=.6,c.subSurface.isTranslucencyEnabled=!0,c.subSurface.translucencyIntensity=.3,c.subSurface.tintColor=new p.v9j(.95,.95,.9);let e=this.createMarbleTexture();c.albedoTexture=e,c.bumpTexture=e;break;default:c.albedoColor=new p.v9j(.5,.5,.5),c.metallic=.5,c.roughness=.5}return c}createEnvironmentTexture(){return p.b4q.CreateFromImages([this.createSkyTexture("px"),this.createSkyTexture("nx"),this.createSkyTexture("py"),this.createSkyTexture("ny"),this.createSkyTexture("pz"),this.createSkyTexture("nz")],this.scene)}createSkyTexture(a){let b=document.createElement("canvas");b.width=512,b.height=512;let c=b.getContext("2d"),d=c.createRadialGradient(256,256,0,256,256,512);d.addColorStop(0,"#001a33"),d.addColorStop(.3,"#003366"),d.addColorStop(.6,"#001122"),d.addColorStop(1,"#000011"),c.fillStyle=d,c.fillRect(0,0,512,512);for(let a=0;a<150;a++){let a=512*Math.random(),b=512*Math.random(),d=Math.random(),e=2*Math.random()+1;c.fillStyle=`rgba(${100+155*d}, ${150+105*d}, 255, ${d})`,c.fillRect(a,b,e,e)}return b.toDataURL()}createMarbleTexture(){let a=new p.RCS("marbleTexture",{width:512,height:512},this.scene),b=a.getContext(),c=new ImageData(512,512),d=c.data;for(let a=0;a<512;a++)for(let b=0;b<512;b++){let c=(512*a+b)*4,e=Math.floor(200+55*((.5*Math.sin(.01*b+.01*a)+.5)*(.3*Math.sin(.02*b+.005*a)+.7)*(.2*Math.sin(.005*b+.02*a)+.8)));d[c]=e,d[c+1]=e,d[c+2]=Math.floor(.95*e),d[c+3]=255}return b.putImageData(c,0,0),a.update(),a}setupAdvancedPostProcessing(){try{this.renderingPipeline=new p.eEV("advancedPipeline",!0,this.scene,[this.camera]),this.renderingPipeline&&(this.renderingPipeline.bloomEnabled=!0,this.renderingPipeline.bloomThreshold=.7,this.renderingPipeline.bloomWeight=.4,this.renderingPipeline.bloomKernel=64,this.renderingPipeline.bloomScale=.6,this.renderingPipeline.imageProcessing&&(this.renderingPipeline.imageProcessing.toneMappingEnabled=!0,this.renderingPipeline.imageProcessing.toneMappingType=1),this.renderingPipeline.fxaaEnabled=!0,this.renderingPipeline.chromaticAberrationEnabled=!0,this.renderingPipeline.chromaticAberration.aberrationAmount=20,this.renderingPipeline.chromaticAberration.radialIntensity=.8,this.renderingPipeline.grainEnabled=!0,this.renderingPipeline.grain.intensity=8,this.renderingPipeline.grain.animated=!0,this.renderingPipeline.imageProcessing&&(this.renderingPipeline.imageProcessing.vignetteEnabled=!0,this.renderingPipeline.imageProcessing.vignetteStretch=.15,this.renderingPipeline.imageProcessing.vignetteWeight=1.2,this.renderingPipeline.imageProcessing.vignetteColor=new p.ov8(0,0,0,0)),this.renderingPipeline.depthOfFieldEnabled=!0,this.renderingPipeline.depthOfFieldBlurLevel=0,this.renderingPipeline.depthOfField.focusDistance=2e3,this.renderingPipeline.depthOfField.focalLength=50,this.renderingPipeline.depthOfField.fStop=1.4),console.log("Advanced post-processing pipeline setup complete")}catch(a){console.error("Error setting up post-processing:",a)}}createAdvancedParticles(){try{let a=new p.AqF("advancedParticles",{capacity:5e4},this.scene);a.emitter=p.Pq0.Zero(),a.minEmitBox=new p.Pq0(-15,0,-15),a.maxEmitBox=new p.Pq0(15,0,15),a.particleTexture=this.createParticleTexture(),a.emitRate=1e3,a.minLifeTime=2,a.maxLifeTime=8,a.minSize=.1,a.maxSize=.8,a.minInitialRotation=0,a.maxInitialRotation=2*Math.PI,a.minAngularSpeed=-Math.PI,a.maxAngularSpeed=Math.PI,a.direction1=new p.Pq0(-1,1,-1),a.direction2=new p.Pq0(1,1,1),a.minEmitPower=2,a.maxEmitPower=6,a.updateSpeed=.02,a.gravity=new p.Pq0(0,-2,0),a.color1=new p.ov8(0,1,1,1),a.color2=new p.ov8(1,0,1,1),a.colorDead=new p.ov8(0,0,0,0),a.blendMode=p.okU.BLENDMODE_ONEONE,a.start(),this.particleSystems.push(a),console.log("Advanced particle system created")}catch(a){console.error("Error creating particle system:",a)}}createParticleTexture(){let a=new p.RCS("particleTexture",{width:64,height:64},this.scene),b=a.getContext(),c=b.createRadialGradient(32,32,0,32,32,32);return c.addColorStop(0,"rgba(255, 255, 255, 1)"),c.addColorStop(.3,"rgba(0, 255, 255, 0.8)"),c.addColorStop(.6,"rgba(255, 0, 255, 0.4)"),c.addColorStop(1,"rgba(0, 0, 0, 0)"),b.fillStyle=c,b.fillRect(0,0,64,64),a.update(),a}async setupWebXR(){try{"xr"in navigator&&(this.webXRExperience=await this.scene.createDefaultXRExperienceAsync({floorMeshes:this.showcaseObjects.filter(a=>a.name.includes("ground"))}),this.webXRExperience&&(this.webXRExperience.baseExperience.featuresManager.enableFeature(p._qY.HAND_TRACKING,"latest"),this.webXRExperience.baseExperience.featuresManager.enableFeature(p._qY.HIT_TEST,"latest"),console.log("WebXR experience initialized")))}catch(a){console.error("Error setting up WebXR:",a)}}async setupAudioReactive(){try{let a=await navigator.mediaDevices.getUserMedia({audio:!0});this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.audioContext.createMediaStreamSource(a),this.audioAnalyser=new p.KSX(this.scene),console.log("Audio reactive features initialized")}catch(a){console.error("Error setting up audio reactive features:",a)}}startRenderLoop(){this.engine.runRenderLoop(()=>{this.scene&&(this.audioAnalyser&&this.options.enableAudioReactive&&this.updateAudioReactiveEffects(),this.scene.render())})}updateAudioReactiveEffects(){if(this.audioAnalyser)try{let a=this.audioAnalyser.getByteFrequencyData(),b=a.reduce((a,b)=>a+b)/a.length/255;this.particleSystems.forEach(a=>{a instanceof p.AqF&&(a.emitRate=500+2e3*b)}),this.renderingPipeline&&(this.renderingPipeline.bloomWeight=.2+.6*b,this.renderingPipeline.chromaticAberration.aberrationAmount=10+40*b),this.showcaseObjects.forEach((a,c)=>{if(!a.name.includes("ground")){let c=1+.3*b;a.scaling=new p.Pq0(c,c,c)}})}catch(a){console.error("Error updating audio reactive effects:",a)}}setupPerformanceMonitoring(){setInterval(()=>{this.performanceStats.fps=Math.round(this.engine.getFps()),this.performanceStats.drawCalls=this.scene.getActiveMeshes().length,this.performanceStats.triangles=this.scene.getTotalVertices()},1e3)}getPerformanceStats(){return this.performanceStats}async toggleWebXR(){if(this.webXRExperience)try{0===this.webXRExperience.baseExperience.state?await this.webXRExperience.baseExperience.enterXRAsync("immersive-ar","local-floor"):await this.webXRExperience.baseExperience.exitXRAsync()}catch(a){console.error("Error toggling WebXR:",a)}}toggleAudioReactive(){this.options.enableAudioReactive=!this.options.enableAudioReactive,console.log("Audio reactive mode:",this.options.enableAudioReactive?"enabled":"disabled")}destroy(){this.particleSystems.forEach(a=>a.dispose()),this.animationGroups.forEach(a=>a.dispose()),this.showcaseObjects.forEach(a=>a.dispose()),this.renderingPipeline&&this.renderingPipeline.dispose(),this.webXRExperience&&this.webXRExperience.dispose(),this.audioContext&&this.audioContext.close(),this.scene&&this.scene.dispose(),this.engine&&this.engine.dispose(),console.log("Advanced 3D Showcase destroyed")}}}};