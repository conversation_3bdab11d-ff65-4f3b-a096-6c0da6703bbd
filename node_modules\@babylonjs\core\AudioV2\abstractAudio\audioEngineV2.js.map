{"version": 3, "file": "audioEngineV2.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/abstractAudio/audioEngineV2.ts"], "names": [], "mappings": "AAWA,MAAM,SAAS,GAAoB,EAAE,CAAC;AAEtC;;;GAGG;AACH,MAAM,UAAU,sBAAsB;IAClC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,OAAO,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3C,CAAC;AAsBD;;;;GAIG;AACH,MAAM,OAAgB,aAAa;IAW/B,YAAsB,OAAuC;QAV7D,qFAAqF;QACpE,eAAU,GAAG,IAAI,GAAG,EAAgB,CAAC;QAEtD,2CAA2C;QAC1B,WAAM,GAAG,IAAI,GAAG,EAA0B,CAAC;QAEpD,oBAAe,GAA2B,IAAI,CAAC;QAE/C,2BAAsB,GAAW,IAAI,CAAC;QAG1C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAErB,IAAI,OAAO,OAAO,CAAC,qBAAqB,KAAK,QAAQ,EAAE,CAAC;YACpD,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAC;QAC/D,CAAC;IACL,CAAC;IAOD;;;;OAIG;IACH,IAAW,cAAc;QACrB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IA8BD;;;;;;OAMG;IACH,IAAW,qBAAqB;QAC5B,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC,CAAC;IAED,IAAW,qBAAqB,CAAC,KAAa;QAC1C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAoED;;OAEG;IACI,OAAO;QACV,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACpC,KAAK,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;YAC9D,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAEpB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IA8BD;;;;OAIG;IACH,2FAA2F;IACpF,WAAW;QACd,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC9B,CAAC;IAES,WAAW,CAAC,OAAqB;QACvC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAE7B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAES,cAAc,CAAC,OAAqB;QAC1C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAES,QAAQ,CAAC,IAA4B;QAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAES,WAAW,CAAC,IAA4B;QAC9C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;CACJ;AAED;;;;;GAKG;AACH,MAAM,UAAU,eAAe,CAAC,MAA+B;IAC3D,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,MAAM,GAAG,sBAAsB,EAAE,CAAC;IACtC,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACT,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;GAMG;AACH,2FAA2F;AAC3F,MAAM,UAAU,mBAAmB,CAAC,IAAY,EAAE,UAAqC,EAAE,EAAE,SAAkC,IAAI;IAC7H,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC;AAED;;;;;;GAMG;AACH,2FAA2F;AAC3F,MAAM,UAAU,uBAAuB,CAAC,IAAY,EAAE,UAAyC,EAAE,EAAE,SAAkC,IAAI;IACrI,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACpD,CAAC;AAED;;;;;;GAMG;AACH,2FAA2F;AAC3F,MAAM,UAAU,gCAAgC,CAAC,IAAY,EAAE,UAAwC,EAAE,EAAE,SAAkC,IAAI;IAC7I,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,gCAAgC,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;GAOG;AACH,2FAA2F;AAC3F,MAAM,UAAU,gBAAgB,CAC5B,IAAY,EACZ,MAAyE,EACzE,UAAwC,EAAE,EAC1C,SAAkC,IAAI;IAEtC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB,CACxC,MAAyE,EACzE,UAA8C,EAAE,EAChD,SAAkC,IAAI;IAEtC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,MAAM,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC;AAED;;;;;;;GAOG;AACH,2FAA2F;AAC3F,MAAM,UAAU,sBAAsB,CAClC,IAAY,EACZ,MAAiB,EACjB,UAAwC,EAAE,EAC1C,SAAkC,IAAI;IAEtC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAChE,CAAC;AAED;;;;;;;GAOG;AACH,2FAA2F;AAC3F,MAAM,UAAU,yBAAyB,CACrC,IAAY,EACZ,MAA4C,EAC5C,UAA2C,EAAE,EAC7C,SAAkC,IAAI;IAEtC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,yBAAyB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACnE,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport type { IAudioParameterRampOptions } from \"../audioParameter\";\nimport type { AbstractAudioNode, AbstractNamedAudioNode } from \"./abstractAudioNode\";\nimport type { AbstractSoundSource, ISoundSourceOptions } from \"./abstractSoundSource\";\nimport type { AudioBus, IAudioBusOptions } from \"./audioBus\";\nimport type { IMainAudioBusOptions, MainAudioBus } from \"./mainAudioBus\";\nimport type { IStaticSoundOptions, StaticSound } from \"./staticSound\";\nimport type { IStaticSoundBufferOptions, StaticSoundBuffer } from \"./staticSoundBuffer\";\nimport type { IStreamingSoundOptions, StreamingSound } from \"./streamingSound\";\nimport type { AbstractSpatialAudioListener, ISpatialAudioListenerOptions } from \"./subProperties/abstractSpatialAudioListener\";\n\nconst Instances: AudioEngineV2[] = [];\n\n/**\n * Gets the most recently created v2 audio engine.\n * @returns The most recently created v2 audio engine.\n */\nexport function LastCreatedAudioEngine(): Nullable<AudioEngineV2> {\n    if (Instances.length === 0) {\n        return null;\n    }\n\n    return Instances[Instances.length - 1];\n}\n\n/**\n * Options for creating a v2 audio engine.\n */\nexport interface IAudioEngineV2Options extends ISpatialAudioListenerOptions {\n    /**\n     * The smoothing duration to use when changing audio parameters, in seconds. Defaults to `0.01` (10 milliseconds).\n     */\n    parameterRampDuration: number;\n    /**\n     * The initial output volume of the audio engine. Defaults to `1`.\n     */\n    volume: number;\n}\n\n/**\n * The state of a v2 audio engine.\n * @see {@link AudioEngineV2.state}\n */\nexport type AudioEngineV2State = \"closed\" | \"interrupted\" | \"running\" | \"suspended\";\n\n/**\n * Abstract base class for v2 audio engines.\n *\n * A v2 audio engine based on the WebAudio API can be created with the {@link CreateAudioEngineAsync} function.\n */\nexport abstract class AudioEngineV2 {\n    /** Not owned, but all items should be in `_nodes` container, too, which is owned. */\n    private readonly _mainBuses = new Set<MainAudioBus>();\n\n    /** Owned top-level sound and bus nodes. */\n    private readonly _nodes = new Set<AbstractNamedAudioNode>();\n\n    private _defaultMainBus: Nullable<MainAudioBus> = null;\n\n    private _parameterRampDuration: number = 0.01;\n\n    protected constructor(options: Partial<IAudioEngineV2Options>) {\n        Instances.push(this);\n\n        if (typeof options.parameterRampDuration === \"number\") {\n            this.parameterRampDuration = options.parameterRampDuration;\n        }\n    }\n\n    /**\n     * The elapsed time since the audio engine was started, in seconds.\n     */\n    public abstract readonly currentTime: number;\n\n    /**\n     * The default main bus that will be used for audio buses and sounds if their `outBus` option is not set.\n     * @see {@link IAudioBusOptions.outBus}\n     * @see {@link IAbstractSoundOptions.outBus}\n     */\n    public get defaultMainBus(): Nullable<MainAudioBus> {\n        if (this._mainBuses.size === 0) {\n            return null;\n        }\n\n        if (!this._defaultMainBus) {\n            this._defaultMainBus = Array.from(this._mainBuses)[0];\n        }\n\n        return this._defaultMainBus;\n    }\n\n    /**\n     * The spatial audio listener properties for the audio engine.\n     * - Each audio engine has exactly one listener.\n     */\n    public abstract readonly listener: AbstractSpatialAudioListener;\n\n    /**\n     * The main output node.\n     * - This is the last node in the audio graph before the audio is sent to the speakers.\n     */\n    public abstract readonly mainOut: AbstractAudioNode;\n\n    /**\n     * The current state of the audio engine.\n     *\n     * Possible values are:\n     * - `\"closed\"`: The audio engine has been closed.\n     * - `\"interrupted\"`: The audio engine has been interrupted and is not running.\n     * - `\"running\"`: The audio engine is running normally.\n     * - `\"suspended\"`: The audio engine is suspended and is not running.\n     */\n    public abstract readonly state: AudioEngineV2State;\n\n    /**\n     * The output volume of the audio engine.\n     */\n    public abstract volume: number;\n\n    /**\n     * The smoothing duration to use when changing audio parameters, in seconds. Defaults to `0.01` (10 milliseconds).\n     *\n     * Due to limitations in some browsers, it is not recommended to set this value to longer than `0.01` seconds.\n     *\n     * Setting this value to longer than `0.01` seconds may result in errors being throw when setting audio parameters.\n     */\n    public get parameterRampDuration(): number {\n        return this._parameterRampDuration;\n    }\n\n    public set parameterRampDuration(value: number) {\n        this._parameterRampDuration = Math.max(0, value);\n    }\n\n    /**\n     * Creates a new audio bus.\n     * @param name - The name of the audio bus.\n     * @param options - The options to use when creating the audio bus.\n     * @returns A promise that resolves with the created audio bus.\n     */\n    public abstract createBusAsync(name: string, options?: Partial<IAudioBusOptions>): Promise<AudioBus>;\n\n    /**\n     * Creates a new main audio bus.\n     * @param name - The name of the main audio bus.\n     * @param options - The options to use when creating the main audio bus.\n     * @returns A promise that resolves with the created main audio bus.\n     */\n    public abstract createMainBusAsync(name: string, options?: Partial<IMainAudioBusOptions>): Promise<MainAudioBus>;\n\n    /**\n     * Creates a new microphone sound source.\n     * @param name - The name of the sound.\n     * @param options - The options for the sound source.\n     * @returns A promise that resolves to the created sound source.\n     */\n    public abstract createMicrophoneSoundSourceAsync(name: string, options?: Partial<ISoundSourceOptions>): Promise<AbstractSoundSource>;\n\n    /**\n     * Creates a new static sound.\n     * @param name - The name of the sound.\n     * @param source - The source of the sound.\n     * @param options - The options for the static sound.\n     * @returns A promise that resolves to the created static sound.\n     */\n    public abstract createSoundAsync(\n        name: string,\n        source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n        options?: Partial<IStaticSoundOptions>\n    ): Promise<StaticSound>;\n\n    /**\n     * Creates a new static sound buffer.\n     * @param source - The source of the sound buffer.\n     * @param options - The options for the static sound buffer.\n     * @returns A promise that resolves to the created static sound buffer.\n     */\n    public abstract createSoundBufferAsync(\n        source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n        options?: Partial<IStaticSoundBufferOptions>\n    ): Promise<StaticSoundBuffer>;\n\n    /**\n     * Creates a new sound source.\n     * @param name - The name of the sound.\n     * @param source - The source of the sound.\n     * @param options - The options for the sound source.\n     * @returns A promise that resolves to the created sound source.\n     */\n    public abstract createSoundSourceAsync(name: string, source: AudioNode, options?: Partial<ISoundSourceOptions>): Promise<AbstractSoundSource>;\n\n    /**\n     * Creates a new streaming sound.\n     * @param name - The name of the sound.\n     * @param source - The source of the sound.\n     * @param options - The options for the streaming sound.\n     * @returns A promise that resolves to the created streaming sound.\n     */\n    public abstract createStreamingSoundAsync(name: string, source: HTMLMediaElement | string | string[], options?: Partial<IStreamingSoundOptions>): Promise<StreamingSound>;\n\n    /**\n     * Releases associated resources.\n     */\n    public dispose(): void {\n        if (Instances.includes(this)) {\n            Instances.splice(Instances.indexOf(this), 1);\n        }\n\n        const nodeIt = this._nodes.values();\n        for (let next = nodeIt.next(); !next.done; next = nodeIt.next()) {\n            next.value.dispose();\n        }\n\n        this._mainBuses.clear();\n        this._nodes.clear();\n\n        this._defaultMainBus = null;\n    }\n\n    /**\n     * Checks if the specified format is valid.\n     * @param format The format to check as an audio file extension like \"mp3\" or \"wav\".\n     * @returns `true` if the format is valid; otherwise `false`.\n     */\n    public abstract isFormatValid(format: string): boolean;\n\n    /**\n     * Pauses the audio engine if it is running.\n     * @returns A promise that resolves when the audio engine is paused.\n     */\n    public abstract pauseAsync(): Promise<void>;\n\n    /**\n     * Resumes the audio engine if it is not running.\n     * @returns A promise that resolves when the audio engine is running.\n     */\n    public abstract resumeAsync(): Promise<void>;\n\n    /**\n     * Sets the audio output volume with optional ramping.\n     * If the duration is 0 then the volume is set immediately, otherwise it is ramped to the new value over the given duration using the given shape.\n     * If a ramp is already in progress then the volume is not set and an error is thrown.\n     * @param value The value to set the volume to.\n     * @param options The options to use for ramping the volume change.\n     */\n    public abstract setVolume(value: number, options?: Partial<IAudioParameterRampOptions>): void;\n\n    /**\n     * Unlocks the audio engine if it is locked.\n     * - Note that the returned promise may already be resolved if the audio engine is already unlocked.\n     * @returns A promise that is resolved when the audio engine is unlocked.\n     */\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\n    public unlockAsync(): Promise<void> {\n        return this.resumeAsync();\n    }\n\n    protected _addMainBus(mainBus: MainAudioBus): void {\n        this._mainBuses.add(mainBus);\n\n        this._addNode(mainBus);\n    }\n\n    protected _removeMainBus(mainBus: MainAudioBus): void {\n        this._mainBuses.delete(mainBus);\n        this._defaultMainBus = null;\n\n        this._removeNode(mainBus);\n    }\n\n    protected _addNode(node: AbstractNamedAudioNode): void {\n        this._nodes.add(node);\n    }\n\n    protected _removeNode(node: AbstractNamedAudioNode): void {\n        this._nodes.delete(node);\n    }\n}\n\n/**\n * @internal\n * @param engine - The given audio engine. If `null` then the last created audio engine is used.\n * @returns the given audio engine or the last created audio engine.\n * @throws An error if the resulting engine is `null`.\n */\nexport function _GetAudioEngine(engine: Nullable<AudioEngineV2>): AudioEngineV2 {\n    if (!engine) {\n        engine = LastCreatedAudioEngine();\n    }\n\n    if (engine) {\n        return engine;\n    }\n\n    throw new Error(\"No audio engine.\");\n}\n\n/**\n * Creates a new audio bus.\n * @param name - The name of the audio bus.\n * @param options - The options to use when creating the audio bus.\n * @param engine - The audio engine.\n * @returns A promise that resolves with the created audio bus.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateAudioBusAsync(name: string, options: Partial<IAudioBusOptions> = {}, engine: Nullable<AudioEngineV2> = null): Promise<AudioBus> {\n    engine = _GetAudioEngine(engine);\n    return engine.createBusAsync(name, options);\n}\n\n/**\n * Creates a new main audio bus.\n * @param name - The name of the main audio bus.\n * @param options - The options to use when creating the main audio bus.\n * @param engine - The audio engine.\n * @returns A promise that resolves with the created main audio bus.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateMainAudioBusAsync(name: string, options: Partial<IMainAudioBusOptions> = {}, engine: Nullable<AudioEngineV2> = null): Promise<MainAudioBus> {\n    engine = _GetAudioEngine(engine);\n    return engine.createMainBusAsync(name, options);\n}\n\n/**\n * Creates a new microphone sound source.\n * @param name - The name of the sound.\n * @param options - The options for the sound source.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created sound source.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateMicrophoneSoundSourceAsync(name: string, options: Partial<ISoundSourceOptions> = {}, engine: Nullable<AudioEngineV2> = null): Promise<AbstractSoundSource> {\n    engine = _GetAudioEngine(engine);\n    return engine.createMicrophoneSoundSourceAsync(name, options);\n}\n\n/**\n * Creates a new static sound.\n * @param name - The name of the sound.\n * @param source - The source of the sound.\n * @param options - The options for the static sound.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created static sound.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateSoundAsync(\n    name: string,\n    source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n    options: Partial<IStaticSoundOptions> = {},\n    engine: Nullable<AudioEngineV2> = null\n): Promise<StaticSound> {\n    engine = _GetAudioEngine(engine);\n    return engine.createSoundAsync(name, source, options);\n}\n\n/**\n * Creates a new static sound buffer.\n * @param source - The source of the sound buffer.\n * @param options - The options for the static sound buffer.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created static sound buffer.\n */\nexport async function CreateSoundBufferAsync(\n    source: ArrayBuffer | AudioBuffer | StaticSoundBuffer | string | string[],\n    options: Partial<IStaticSoundBufferOptions> = {},\n    engine: Nullable<AudioEngineV2> = null\n): Promise<StaticSoundBuffer> {\n    engine = _GetAudioEngine(engine);\n    return await engine.createSoundBufferAsync(source, options);\n}\n\n/**\n * Creates a new sound source.\n * @param name - The name of the sound.\n * @param source - The source of the sound.\n * @param options - The options for the sound source.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created sound source.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateSoundSourceAsync(\n    name: string,\n    source: AudioNode,\n    options: Partial<ISoundSourceOptions> = {},\n    engine: Nullable<AudioEngineV2> = null\n): Promise<AbstractSoundSource> {\n    engine = _GetAudioEngine(engine);\n    return engine.createSoundSourceAsync(name, source, options);\n}\n\n/**\n * Creates a new streaming sound.\n * @param name - The name of the sound.\n * @param source - The source of the sound.\n * @param options - The options for the streaming sound.\n * @param engine - The audio engine.\n * @returns A promise that resolves to the created streaming sound.\n */\n// eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\nexport function CreateStreamingSoundAsync(\n    name: string,\n    source: HTMLMediaElement | string | string[],\n    options: Partial<IStreamingSoundOptions> = {},\n    engine: Nullable<AudioEngineV2> = null\n): Promise<StreamingSound> {\n    engine = _GetAudioEngine(engine);\n    return engine.createStreamingSoundAsync(name, source, options);\n}\n"]}