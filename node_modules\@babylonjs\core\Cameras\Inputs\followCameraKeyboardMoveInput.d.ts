import type { ICameraInput } from "../../Cameras/cameraInputsManager.js";
import type { FollowCamera } from "../../Cameras/followCamera.js";
/**
 * Manage the keyboard inputs to control the movement of a follow camera.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/customizingCameraInputs
 */
export declare class FollowCameraKeyboardMoveInput implements ICameraInput<FollowCamera> {
    /**
     * Defines the camera the input is attached to.
     */
    camera: FollowCamera;
    /**
     * Defines the list of key codes associated with the up action (increase heightOffset)
     */
    keysHeightOffsetIncr: number[];
    /**
     * Defines the list of key codes associated with the down action (decrease heightOffset)
     */
    keysHeightOffsetDecr: number[];
    /**
     * Defines whether the Alt modifier key is required to move up/down (alter heightOffset)
     */
    keysHeightOffsetModifierAlt: boolean;
    /**
     * Defines whether the Ctrl modifier key is required to move up/down (alter heightOffset)
     */
    keysHeightOffsetModifierCtrl: boolean;
    /**
     * Defines whether the Shift modifier key is required to move up/down (alter heightOffset)
     */
    keysHeightOffsetModifierShift: boolean;
    /**
     * Defines the list of key codes associated with the left action (increase rotationOffset)
     */
    keysRotationOffsetIncr: number[];
    /**
     * Defines the list of key codes associated with the right action (decrease rotationOffset)
     */
    keysRotationOffsetDecr: number[];
    /**
     * Defines whether the Alt modifier key is required to move left/right (alter rotationOffset)
     */
    keysRotationOffsetModifierAlt: boolean;
    /**
     * Defines whether the Ctrl modifier key is required to move left/right (alter rotationOffset)
     */
    keysRotationOffsetModifierCtrl: boolean;
    /**
     * Defines whether the Shift modifier key is required to move left/right (alter rotationOffset)
     */
    keysRotationOffsetModifierShift: boolean;
    /**
     * Defines the list of key codes associated with the zoom-in action (decrease radius)
     */
    keysRadiusIncr: number[];
    /**
     * Defines the list of key codes associated with the zoom-out action (increase radius)
     */
    keysRadiusDecr: number[];
    /**
     * Defines whether the Alt modifier key is required to zoom in/out (alter radius value)
     */
    keysRadiusModifierAlt: boolean;
    /**
     * Defines whether the Ctrl modifier key is required to zoom in/out (alter radius value)
     */
    keysRadiusModifierCtrl: boolean;
    /**
     * Defines whether the Shift modifier key is required to zoom in/out (alter radius value)
     */
    keysRadiusModifierShift: boolean;
    /**
     * Defines the rate of change of heightOffset.
     */
    heightSensibility: number;
    /**
     * Defines the rate of change of rotationOffset.
     */
    rotationSensibility: number;
    /**
     * Defines the rate of change of radius.
     */
    radiusSensibility: number;
    private _keys;
    private _ctrlPressed;
    private _altPressed;
    private _shiftPressed;
    private _onCanvasBlurObserver;
    private _onKeyboardObserver;
    private _engine;
    private _scene;
    /**
     * Attach the input controls to a specific dom element to get the input from.
     * @param noPreventDefault Defines whether event caught by the controls should call preventdefault() (https://developer.mozilla.org/en-US/docs/Web/API/Event/preventDefault)
     */
    attachControl(noPreventDefault?: boolean): void;
    /**
     * Detach the current controls from the specified dom element.
     */
    detachControl(): void;
    /**
     * Update the current camera state depending on the inputs that have been used this frame.
     * This is a dynamically created lambda to avoid the performance penalty of looping for inputs in the render loop.
     */
    checkInputs(): void;
    /**
     * Gets the class name of the current input.
     * @returns the class name
     */
    getClassName(): string;
    /**
     * Get the friendly name associated with the input class.
     * @returns the input friendly name
     */
    getSimpleName(): string;
    /**
     * Check if the pressed modifier keys (Alt/Ctrl/Shift) match those configured to
     * allow modification of the heightOffset value.
     * @returns true if modifier keys match
     */
    private _modifierHeightOffset;
    /**
     * Check if the pressed modifier keys (Alt/Ctrl/Shift) match those configured to
     * allow modification of the rotationOffset value.
     * @returns true if modifier keys match
     */
    private _modifierRotationOffset;
    /**
     * Check if the pressed modifier keys (Alt/Ctrl/Shift) match those configured to
     * allow modification of the radius value.
     * @returns true if modifier keys match
     */
    private _modifierRadius;
}
