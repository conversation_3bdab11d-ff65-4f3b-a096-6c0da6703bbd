{"version": 3, "file": "stereoscopicFreeCamera.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/Stereoscopic/stereoscopicFreeCamera.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEtD,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAAE,uBAAuB,EAAE,MAAM,iCAAiC,CAAC;AAE1E,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;IACvE,OAAO,GAAG,EAAE,CAAC,IAAI,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,mBAAmB,EAAE,OAAO,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;AACxI,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,sBAAuB,SAAQ,UAAU;IAClD;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,QAAiB,EAAE,kBAA0B,EAAE,wBAAiC,EAAE,KAAa;QACrH,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAgBd,gBAAW,GAAG,GAAG,EAAE,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAfjE,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;QACzD,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC,CAAC,MAAM,CAAC,yCAAyC,CAAC,CAAC,CAAC,MAAM,CAAC,+BAA+B,EAAE;YACxI,kBAAkB,EAAE,kBAAkB;SACzC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACa,YAAY;QACxB,OAAO,wBAAwB,CAAC;IACpC,CAAC;CAGJ", "sourcesContent": ["import { Camera } from \"../../Cameras/camera\";\r\nimport { FreeCamera } from \"../../Cameras/freeCamera\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Node } from \"../../node\";\r\nimport { _SetStereoscopicRigMode } from \"../RigModes/stereoscopicRigMode\";\r\n\r\nNode.AddNodeConstructor(\"StereoscopicFreeCamera\", (name, scene, options) => {\r\n    return () => new StereoscopicFreeCamera(name, Vector3.Zero(), options.interaxial_distance, options.isStereoscopicSideBySide, scene);\r\n});\r\n\r\n/**\r\n * Camera used to simulate stereoscopic rendering (based on FreeCamera)\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras\r\n */\r\nexport class StereoscopicFreeCamera extends FreeCamera {\r\n    /**\r\n     * Creates a new StereoscopicFreeCamera\r\n     * @param name defines camera name\r\n     * @param position defines initial position\r\n     * @param interaxialDistance defines distance between each color axis\r\n     * @param isStereoscopicSideBySide defines is stereoscopic is done side by side or over under\r\n     * @param scene defines the hosting scene\r\n     */\r\n    constructor(name: string, position: Vector3, interaxialDistance: number, isStereoscopicSideBySide: boolean, scene?: Scene) {\r\n        super(name, position, scene);\r\n        this.interaxialDistance = interaxialDistance;\r\n        this.isStereoscopicSideBySide = isStereoscopicSideBySide;\r\n        this.setCameraRigMode(isStereoscopicSideBySide ? Camera.RIG_MODE_STEREOSCOPIC_SIDEBYSIDE_PARALLEL : Camera.RIG_MODE_STEREOSCOPIC_OVERUNDER, {\r\n            interaxialDistance: interaxialDistance,\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Gets camera class name\r\n     * @returns StereoscopicFreeCamera\r\n     */\r\n    public override getClassName(): string {\r\n        return \"StereoscopicFreeCamera\";\r\n    }\r\n\r\n    protected override _setRigMode = () => _SetStereoscopicRigMode(this);\r\n}\r\n"]}