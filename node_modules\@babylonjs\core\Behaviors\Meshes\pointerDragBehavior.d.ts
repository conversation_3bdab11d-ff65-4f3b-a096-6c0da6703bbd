import type { Behavior } from "../../Behaviors/behavior.js";
import { Mesh } from "../../Meshes/mesh.js";
import type { AbstractMesh } from "../../Meshes/abstractMesh.js";
import type { Nullable } from "../../types.js";
import { Observable } from "../../Misc/observable.js";
import { Vector3 } from "../../Maths/math.vector.js";
import type { PointerInfo } from "../../Events/pointerEvents.js";
import { Ray } from "../../Culling/ray.js";
/**
 * A behavior that when attached to a mesh will allow the mesh to be dragged around the screen based on pointer events
 */
export declare class PointerDragBehavior implements Behavior<AbstractMesh> {
    private static _AnyMouseId;
    /**
     * Abstract mesh the behavior is set on
     */
    attachedNode: AbstractMesh;
    protected _dragPlane: Mesh;
    private _scene;
    private _pointerObserver;
    private _beforeRenderObserver;
    private static _PlaneScene;
    private _useAlternatePickedPointAboveMaxDragAngleDragSpeed;
    private _activeDragButton;
    private _activePointerInfo;
    /**
     * The maximum tolerated angle between the drag plane and dragging pointer rays to trigger pointer events. Set to 0 to allow any angle (default: 0)
     */
    maxDragAngle: number;
    /**
     * Butttons that can be used to initiate a drag
     */
    dragButtons: number[];
    /**
     * @internal
     */
    _useAlternatePickedPointAboveMaxDragAngle: boolean;
    /**
     * Get or set the currentDraggingPointerId
     * @deprecated Please use currentDraggingPointerId instead
     */
    get currentDraggingPointerID(): number;
    set currentDraggingPointerID(currentDraggingPointerId: number);
    /**
     * The id of the pointer that is currently interacting with the behavior (-1 when no pointer is active)
     */
    currentDraggingPointerId: number;
    /**
     * The last position where the pointer hit the drag plane in world space
     */
    lastDragPosition: Vector3;
    /**
     * If the behavior is currently in a dragging state
     */
    dragging: boolean;
    /**
     * The distance towards the target drag position to move each frame. This can be useful to avoid jitter. Set this to 1 for no delay. (Default: 0.2)
     */
    dragDeltaRatio: number;
    /**
     * If the drag plane orientation should be updated during the dragging (Default: true)
     */
    updateDragPlane: boolean;
    private _debugMode;
    private _moving;
    /**
     *  Fires each time the attached mesh is dragged with the pointer
     *  * delta between last drag position and current drag position in world space
     *  * dragDistance along the drag axis
     *  * dragPlaneNormal normal of the current drag plane used during the drag
     *  * dragPlanePoint in world space where the drag intersects the drag plane
     *
     *  (if validatedDrag is used, the position of the attached mesh might not equal dragPlanePoint)
     */
    onDragObservable: Observable<{
        delta: Vector3;
        dragPlanePoint: Vector3;
        dragPlaneNormal: Vector3;
        dragDistance: number;
        pointerId: number;
        pointerInfo: Nullable<PointerInfo>;
    }>;
    /**
     *  Fires each time a drag begins (eg. mouse down on mesh)
     *  * dragPlanePoint in world space where the drag intersects the drag plane
     *
     *  (if validatedDrag is used, the position of the attached mesh might not equal dragPlanePoint)
     */
    onDragStartObservable: Observable<{
        dragPlanePoint: Vector3;
        pointerId: number;
        pointerInfo: Nullable<PointerInfo>;
    }>;
    /**
     *  Fires each time a drag ends (eg. mouse release after drag)
     *  * dragPlanePoint in world space where the drag intersects the drag plane
     *
     *  (if validatedDrag is used, the position of the attached mesh might not equal dragPlanePoint)
     */
    onDragEndObservable: Observable<{
        dragPlanePoint: Vector3;
        pointerId: number;
        pointerInfo: Nullable<PointerInfo>;
    }>;
    /**
     *  Fires each time behavior enabled state changes
     */
    onEnabledObservable: Observable<boolean>;
    /**
     *  If the attached mesh should be moved when dragged
     */
    moveAttached: boolean;
    /**
     *  If the drag behavior will react to drag events (Default: true)
     */
    set enabled(value: boolean);
    get enabled(): boolean;
    private _enabled;
    /**
     * If pointer events should start and release the drag (Default: true)
     */
    startAndReleaseDragOnPointerEvents: boolean;
    /**
     * If camera controls should be detached during the drag
     */
    detachCameraControls: boolean;
    /**
     * If set, the drag plane/axis will be rotated based on the attached mesh's world rotation (Default: true)
     */
    useObjectOrientationForDragging: boolean;
    /**
     * Normally a drag is canceled when the user presses another button on the same pointer. If this is set to true,
     * the drag will continue even if another button is pressed on the same pointer.
     */
    allowOtherButtonsDuringDrag: boolean;
    private _options;
    /**
     * Gets the options used by the behavior
     */
    get options(): {
        dragAxis?: Vector3;
        dragPlaneNormal?: Vector3;
    };
    /**
     * Sets the options used by the behavior
     */
    set options(options: {
        dragAxis?: Vector3;
        dragPlaneNormal?: Vector3;
    });
    /**
     * Creates a pointer drag behavior that can be attached to a mesh
     * @param options The drag axis or normal of the plane that will be dragged across. If no options are specified the drag plane will always face the ray's origin (eg. camera)
     * @param options.dragAxis
     * @param options.dragPlaneNormal
     */
    constructor(options?: {
        dragAxis?: Vector3;
        dragPlaneNormal?: Vector3;
    });
    /**
     * Predicate to determine if it is valid to move the object to a new position when it is moved.
     * In the case of rotation gizmo, target contains the angle.
     * @param target destination position or desired angle delta
     * @returns boolean for whether or not it is valid to move
     */
    validateDrag: (target: Vector3) => boolean;
    /**
     *  The name of the behavior
     */
    get name(): string;
    /**
     *  Initializes the behavior
     */
    init(): void;
    private _tmpVector;
    private _alternatePickedPoint;
    private _worldDragAxis;
    private _targetPosition;
    private _attachedToElement;
    /**
     * Attaches the drag behavior the passed in mesh
     * @param ownerNode The mesh that will be dragged around once attached
     * @param predicate Predicate to use for pick filtering
     */
    attach(ownerNode: AbstractMesh, predicate?: (m: AbstractMesh) => boolean): void;
    /**
     * Force release the drag action by code.
     */
    releaseDrag(): void;
    private _startDragRay;
    private _lastPointerRay;
    /**
     * Simulates the start of a pointer drag event on the behavior
     * @param pointerId pointerID of the pointer that should be simulated (Default: Any mouse pointer ID)
     * @param fromRay initial ray of the pointer to be simulated (Default: Ray from camera to attached mesh)
     * @param startPickedPoint picked point of the pointer to be simulated (Default: attached mesh position)
     */
    startDrag(pointerId?: number, fromRay?: Ray, startPickedPoint?: Vector3): void;
    protected _startDrag(pointerId: number, fromRay?: Ray, startPickedPoint?: Vector3): void;
    private _dragDelta;
    protected _moveDrag(ray: Ray): void;
    private _pickWithRayOnDragPlane;
    private _pointA;
    private _pointC;
    private _localAxis;
    private _lookAt;
    private _updateDragPlanePosition;
    /**
     *  Detaches the behavior from the mesh
     */
    detach(): void;
}
