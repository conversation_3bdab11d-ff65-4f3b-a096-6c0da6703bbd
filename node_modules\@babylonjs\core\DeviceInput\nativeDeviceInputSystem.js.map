{"version": 3, "file": "nativeDeviceInputSystem.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/DeviceInput/nativeDeviceInputSystem.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,4BAA4B,CAAC;AAMxD,gBAAgB;AAChB,MAAM,OAAO,uBAAuB;IAGhC,YACI,iBAAuE,EACvE,oBAA0E,EAC1E,cAAyF;QAEzF,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,iBAAiB;YACzC,CAAC,CAAC,IAAI,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE;gBACxH,MAAM,GAAG,GAAG,kBAAkB,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;gBAEzG,cAAc,CAAC,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;YAChD,CAAC,CAAC;YACJ,CAAC,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACzC,CAAC;IAED,mBAAmB;IACnB;;;;;;OAMG;IACI,SAAS,CAAC,UAAsB,EAAE,UAAkB,EAAE,UAAkB;QAC3E,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IAC3E,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,UAAsB;QAC3C,6BAA6B;QAC7B,OAAO,UAAU,KAAK,UAAU,CAAC,KAAK,IAAI,UAAU,KAAK,UAAU,CAAC,KAAK,CAAC;IAC9E,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG;IACK,uBAAuB;QAC3B,MAAM,WAAW,GAAG;YAChB,SAAS,EAAE,GAAG,EAAE;gBACZ,OAAO,CAAC,CAAC;YACb,CAAC;YACD,iBAAiB,EAAE,GAAG,EAAE;gBACpB,OAAO,KAAK,CAAC;YACjB,CAAC;YACD,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC;SACpB,CAAC;QAEF,OAAO,WAAW,CAAC;IACvB,CAAC;CACJ", "sourcesContent": ["import type { INative } from \"../Engines/Native/nativeInterfaces\";\r\nimport type { IUIEvent } from \"../Events/deviceInputEvents\";\r\nimport { DeviceEventFactory } from \"./eventFactory\";\r\nimport { DeviceType } from \"./InputDevices/deviceEnums\";\r\nimport type { IDeviceInputSystem } from \"./inputInterfaces\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\ndeclare const _native: INative;\r\n\r\n/** @internal */\r\nexport class NativeDeviceInputSystem implements IDeviceInputSystem {\r\n    private readonly _nativeInput: IDeviceInputSystem;\r\n\r\n    public constructor(\r\n        onDeviceConnected: (deviceType: DeviceType, deviceSlot: number) => void,\r\n        onDeviceDisconnected: (deviceType: DeviceType, deviceSlot: number) => void,\r\n        onInputChanged: (deviceType: DeviceType, deviceSlot: number, eventData: IUIEvent) => void\r\n    ) {\r\n        this._nativeInput = _native.DeviceInputSystem\r\n            ? new _native.DeviceInputSystem(onDeviceConnected, onDeviceDisconnected, (deviceType, deviceSlot, inputIndex, currentState) => {\r\n                  const evt = DeviceEventFactory.CreateDeviceEvent(deviceType, deviceSlot, inputIndex, currentState, this);\r\n\r\n                  onInputChanged(deviceType, deviceSlot, evt);\r\n              })\r\n            : this._createDummyNativeInput();\r\n    }\r\n\r\n    // Public functions\r\n    /**\r\n     * Checks for current device input value, given an id and input index. Throws exception if requested device not initialized.\r\n     * @param deviceType Enum specifying device type\r\n     * @param deviceSlot \"Slot\" or index that device is referenced in\r\n     * @param inputIndex Id of input to be checked\r\n     * @returns Current value of input\r\n     */\r\n    public pollInput(deviceType: DeviceType, deviceSlot: number, inputIndex: number): number {\r\n        return this._nativeInput.pollInput(deviceType, deviceSlot, inputIndex);\r\n    }\r\n\r\n    /**\r\n     * Check for a specific device in the DeviceInputSystem\r\n     * @param deviceType Type of device to check for\r\n     * @returns bool with status of device's existence\r\n     */\r\n    public isDeviceAvailable(deviceType: DeviceType): boolean {\r\n        //TODO: FIx native side first\r\n        return deviceType === DeviceType.Mouse || deviceType === DeviceType.Touch;\r\n    }\r\n\r\n    /**\r\n     * Dispose of all the observables\r\n     */\r\n    public dispose(): void {\r\n        this._nativeInput.dispose();\r\n    }\r\n\r\n    /**\r\n     * For versions of BabylonNative that don't have the NativeInput plugin initialized, create a dummy version\r\n     * @returns Object with dummy functions\r\n     */\r\n    private _createDummyNativeInput() {\r\n        const nativeInput = {\r\n            pollInput: () => {\r\n                return 0;\r\n            },\r\n            isDeviceAvailable: () => {\r\n                return false;\r\n            },\r\n            dispose: () => {},\r\n        };\r\n\r\n        return nativeInput;\r\n    }\r\n}\r\n"]}