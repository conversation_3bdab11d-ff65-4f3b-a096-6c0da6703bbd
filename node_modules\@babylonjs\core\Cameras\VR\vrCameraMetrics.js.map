{"version": 3, "file": "vrCameraMetrics.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/VR/vrCameraMetrics.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACjD;;;GAGG;AACH,MAAM,OAAO,eAAe;IAA5B;QAmDI;;WAEG;QACI,yBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG;QACI,qBAAgB,GAAG,KAAK,CAAC;IA0EpC,CAAC;IAxEG;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC;IAC5G,CAAC;IAED;;OAEG;IACH,gEAAgE;IAChE,IAAW,WAAW;QAClB,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;QACtE,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;QAE1C,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,gEAAgE;IAChE,IAAW,YAAY;QACnB,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;QACtE,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;QAE1C,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,MAAM,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,UAAU;QACpB,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;QAErC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;QAC1B,MAAM,CAAC,WAAW,GAAG,GAAG,CAAC;QACzB,MAAM,CAAC,WAAW,GAAG,WAAW,CAAC;QACjC,MAAM,CAAC,WAAW,GAAG,YAAY,CAAC;QAClC,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC;QACpC,MAAM,CAAC,mBAAmB,GAAG,YAAY,CAAC;QAC1C,MAAM,CAAC,sBAAsB,GAAG,WAAW,CAAC;QAC5C,MAAM,CAAC,sBAAsB,GAAG,WAAW,CAAC;QAC5C,MAAM,CAAC,WAAW,GAAG,CAAC,GAAG,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC;QAC1D,MAAM,CAAC,kBAAkB,GAAG,CAAC,WAAW,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;QAC3E,MAAM,CAAC,sBAAsB,GAAG,iBAAiB,CAAC;QAClD,MAAM,CAAC,gBAAgB,GAAG,WAAW,CAAC;QAEtC,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ", "sourcesContent": ["import { Matrix } from \"../../Maths/math.vector\";\r\n/**\r\n * This represents all the required metrics to create a VR camera.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#device-orientation-camera\r\n */\r\nexport class VRCameraMetrics {\r\n    /**\r\n     * Define the horizontal resolution off the screen.\r\n     */\r\n    public hResolution: number;\r\n    /**\r\n     * Define the vertical resolution off the screen.\r\n     */\r\n    public vResolution: number;\r\n    /**\r\n     * Define the horizontal screen size.\r\n     */\r\n    public hScreenSize: number;\r\n    /**\r\n     * Define the vertical screen size.\r\n     */\r\n    public vScreenSize: number;\r\n    /**\r\n     * Define the vertical screen center position.\r\n     */\r\n    public vScreenCenter: number;\r\n    /**\r\n     * Define the distance of the eyes to the screen.\r\n     */\r\n    public eyeToScreenDistance: number;\r\n    /**\r\n     * Define the distance between both lenses\r\n     */\r\n    public lensSeparationDistance: number;\r\n    /**\r\n     * Define the distance between both viewer's eyes.\r\n     */\r\n    public interpupillaryDistance: number;\r\n    /**\r\n     * Define the distortion factor of the VR postprocess.\r\n     * Please, touch with care.\r\n     */\r\n    public distortionK: number[];\r\n    /**\r\n     * Define the chromatic aberration correction factors for the VR post process.\r\n     */\r\n    public chromaAbCorrection: number[];\r\n    /**\r\n     * Define the scale factor of the post process.\r\n     * The smaller the better but the slower.\r\n     */\r\n    public postProcessScaleFactor: number;\r\n    /**\r\n     * Define an offset for the lens center.\r\n     */\r\n    public lensCenterOffset: number;\r\n    /**\r\n     * Define if the current vr camera should compensate the distortion of the lens or not.\r\n     */\r\n    public compensateDistortion = true;\r\n\r\n    /**\r\n     * Defines if multiview should be enabled when rendering (Default: false)\r\n     */\r\n    public multiviewEnabled = false;\r\n\r\n    /**\r\n     * Gets the rendering aspect ratio based on the provided resolutions.\r\n     */\r\n    public get aspectRatio(): number {\r\n        return this.hResolution / (2 * this.vResolution);\r\n    }\r\n\r\n    /**\r\n     * Gets the aspect ratio based on the FOV, scale factors, and real screen sizes.\r\n     */\r\n    public get aspectRatioFov(): number {\r\n        return 2 * Math.atan((this.postProcessScaleFactor * this.vScreenSize) / (2 * this.eyeToScreenDistance));\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get leftHMatrix(): Matrix {\r\n        const meters = this.hScreenSize / 4 - this.lensSeparationDistance / 2;\r\n        const h = (4 * meters) / this.hScreenSize;\r\n\r\n        return Matrix.Translation(h, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get rightHMatrix(): Matrix {\r\n        const meters = this.hScreenSize / 4 - this.lensSeparationDistance / 2;\r\n        const h = (4 * meters) / this.hScreenSize;\r\n\r\n        return Matrix.Translation(-h, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public get leftPreViewMatrix(): Matrix {\r\n        return Matrix.Translation(0.5 * this.interpupillaryDistance, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public get rightPreViewMatrix(): Matrix {\r\n        return Matrix.Translation(-0.5 * this.interpupillaryDistance, 0, 0);\r\n    }\r\n\r\n    /**\r\n     * Get the default VRMetrics based on the most generic setup.\r\n     * @returns the default vr metrics\r\n     */\r\n    public static GetDefault(): VRCameraMetrics {\r\n        const result = new VRCameraMetrics();\r\n\r\n        result.hResolution = 1280;\r\n        result.vResolution = 800;\r\n        result.hScreenSize = 0.149759993;\r\n        result.vScreenSize = 0.0935999975;\r\n        result.vScreenCenter = 0.0467999987;\r\n        result.eyeToScreenDistance = 0.0410000011;\r\n        result.lensSeparationDistance = 0.063500002;\r\n        result.interpupillaryDistance = 0.064000003;\r\n        result.distortionK = [1.0, 0.219999999, 0.239999995, 0.0];\r\n        result.chromaAbCorrection = [0.995999992, -0.00400000019, 1.01400006, 0.0];\r\n        result.postProcessScaleFactor = 1.714605507808412;\r\n        result.lensCenterOffset = 0.151976421;\r\n\r\n        return result;\r\n    }\r\n}\r\n"]}