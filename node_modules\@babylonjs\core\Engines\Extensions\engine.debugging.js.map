{"version": 3, "file": "engine.debugging.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.debugging.ts"], "names": [], "mappings": "AAAA,sDAAsD;AACtD,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAmB9D,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,SAAiB,EAAE,YAAqB,IAAS,CAAC,CAAC;AAExG,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,YAAqB,IAAS,CAAC,CAAC;AAEpF,cAAc,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,IAAY,EAAE,YAAqB,IAAS,CAAC,CAAC;AAEtG,cAAc,CAAC,SAAS,CAAC,0BAA0B,GAAG,cAAmB,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { AbstractEngine } from \"../../Engines/abstractEngine\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /** @internal */\r\n        _debugPushGroup(groupName: string, targetObject?: number): void;\r\n\r\n        /** @internal */\r\n        _debugPopGroup(targetObject?: number): void;\r\n\r\n        /** @internal */\r\n        _debugInsertMarker(text: string, targetObject?: number): void;\r\n\r\n        /** @internal */\r\n        _debugFlushPendingCommands(): void;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype._debugPushGroup = function (groupName: string, targetObject?: number): void {};\r\n\r\nAbstractEngine.prototype._debugPopGroup = function (targetObject?: number): void {};\r\n\r\nAbstractEngine.prototype._debugInsertMarker = function (text: string, targetObject?: number): void {};\r\n\r\nAbstractEngine.prototype._debugFlushPendingCommands = function (): void {};\r\n"]}