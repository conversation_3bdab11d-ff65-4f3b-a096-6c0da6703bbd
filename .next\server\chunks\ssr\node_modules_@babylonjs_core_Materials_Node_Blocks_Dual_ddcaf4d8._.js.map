{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/currentScreenBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/currentScreenBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport type { InputBlock } from \"../Input/inputBlock\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\nimport { Constants } from \"core/Engines/constants\";\r\n\r\n/**\r\n * Base block used as input for post process\r\n */\r\nexport class CurrentScreenBlock extends NodeMaterialBlock {\r\n    protected _samplerName = \"textureSampler\";\r\n    protected _linearDefineName: string;\r\n    protected _gammaDefineName: string;\r\n    protected _mainUVName: string;\r\n    protected _tempTextureRead: string;\r\n\r\n    /**\r\n     * The name of the sampler to read the screen texture from.\r\n     */\r\n    public get samplerName(): string {\r\n        return this._samplerName;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public texture: Nullable<BaseTexture>;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to gamma space\r\n     */\r\n    public convertToGammaSpace = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to linear space\r\n     */\r\n    public convertToLinearSpace = false;\r\n\r\n    /**\r\n     * Create a new CurrentScreenBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this._isUnique = false;\r\n\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this.registerOutput(\"rgba\", NodeMaterialBlockConnectionPointTypes.Color4, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"rgb\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"r\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"g\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"b\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"a\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Vector2 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n\r\n        this._inputs[0]._prioritizeVertex = false;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"CurrentScreenBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgba output component\r\n     */\r\n    public get rgba(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgb output component\r\n     */\r\n    public get rgb(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the r output component\r\n     */\r\n    public get r(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the g output component\r\n     */\r\n    public get g(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the b output component\r\n     */\r\n    public get b(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the a output component\r\n     */\r\n    public get a(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(this._samplerName);\r\n    }\r\n\r\n    public override get target() {\r\n        if (!this.uv.isConnected) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        if (this.uv.sourceBlock!.isInput) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        return NodeMaterialBlockTargets.Fragment;\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        defines.setValue(this._linearDefineName, this.convertToGammaSpace, true);\r\n        defines.setValue(this._gammaDefineName, this.convertToLinearSpace, true);\r\n    }\r\n\r\n    public override isReady() {\r\n        if (this.texture && !this.texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    protected _getMainUvName(state: NodeMaterialBuildState): string {\r\n        return \"vMain\" + this.uv.associatedVariableName;\r\n    }\r\n\r\n    protected _injectVertexCode(state: NodeMaterialBuildState) {\r\n        const uvInput = this.uv;\r\n\r\n        if (uvInput.connectedPoint!.ownerBlock.isInput) {\r\n            const uvInputOwnerBlock = uvInput.connectedPoint!.ownerBlock as InputBlock;\r\n\r\n            if (!uvInputOwnerBlock.isAttribute) {\r\n                state._emitUniformFromString(uvInput.associatedVariableName, NodeMaterialBlockConnectionPointTypes.Vector2);\r\n            }\r\n        }\r\n\r\n        state.compilationString += `${this._mainUVName} = ${uvInput.associatedVariableName}.xy;\\n`;\r\n\r\n        if (!this._outputs.some((o) => o.isConnectedInVertexShader)) {\r\n            return;\r\n        }\r\n\r\n        this._writeTextureRead(state, true);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                this._writeOutput(state, output, output.name, true);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected _writeTextureRead(state: NodeMaterialBuildState, vertexMode = false) {\r\n        const uvInput = this.uv;\r\n\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n            const textureReadFunc =\r\n                state.shaderLanguage === ShaderLanguage.GLSL\r\n                    ? `texture2D(${this._samplerName},`\r\n                    : `textureSampleLevel(${this._samplerName}, ${this._samplerName + Constants.AUTOSAMPLERSUFFIX},`;\r\n\r\n            const complement = state.shaderLanguage === ShaderLanguage.GLSL ? \"\" : \", 0\";\r\n\r\n            state.compilationString += `${state._declareLocalVar(this._tempTextureRead, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${textureReadFunc} ${uvInput.associatedVariableName}${complement});\\n`;\r\n            return;\r\n        }\r\n\r\n        const textureReadFunc =\r\n            state.shaderLanguage === ShaderLanguage.GLSL\r\n                ? `texture2D(${this._samplerName},`\r\n                : `textureSample(${this._samplerName}, ${this._samplerName + Constants.AUTOSAMPLERSUFFIX},`;\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `${state._declareLocalVar(this._tempTextureRead, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${textureReadFunc} ${uvInput.associatedVariableName});\\n`;\r\n            return;\r\n        }\r\n\r\n        state.compilationString += `${state._declareLocalVar(this._tempTextureRead, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${textureReadFunc} ${this._mainUVName});\\n`;\r\n    }\r\n\r\n    protected _writeOutput(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string, vertexMode = false) {\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n\r\n            state.compilationString += `${state._declareOutput(output)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n\r\n            return;\r\n        }\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `${state._declareOutput(output)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n            return;\r\n        }\r\n\r\n        state.compilationString += `${state._declareOutput(output)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n\r\n        state.compilationString += `#ifdef ${this._linearDefineName}\\n`;\r\n        state.compilationString += `${output.associatedVariableName} = toGammaSpace(${output.associatedVariableName});\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n\r\n        state.compilationString += `#ifdef ${this._gammaDefineName}\\n`;\r\n        state.compilationString += `${output.associatedVariableName} = toLinearSpace(${output.associatedVariableName});\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n    }\r\n\r\n    protected _emitUvAndSampler(state: NodeMaterialBuildState) {\r\n        state._emitVaryingFromString(this._mainUVName, NodeMaterialBlockConnectionPointTypes.Vector2);\r\n        state._emit2DSampler(this._samplerName);\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this._tempTextureRead = state._getFreeVariableName(\"tempTextureRead\");\r\n\r\n        if (state.sharedData.blockingBlocks.indexOf(this) < 0) {\r\n            state.sharedData.blockingBlocks.push(this);\r\n        }\r\n        if (state.sharedData.textureBlocks.indexOf(this) < 0) {\r\n            state.sharedData.textureBlocks.push(this);\r\n        }\r\n        if (state.sharedData.blocksWithDefines.indexOf(this) < 0) {\r\n            state.sharedData.blocksWithDefines.push(this);\r\n        }\r\n        this._mainUVName = this._getMainUvName(state);\r\n\r\n        this._emitUvAndSampler(state);\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            // Vertex\r\n            this._injectVertexCode(state);\r\n            return;\r\n        }\r\n\r\n        // Fragment\r\n        if (!this._outputs.some((o) => o.isConnectedInFragmentShader)) {\r\n            return;\r\n        }\r\n\r\n        this._linearDefineName = state._getFreeDefineName(\"ISLINEAR\");\r\n        this._gammaDefineName = state._getFreeDefineName(\"ISGAMMA\");\r\n\r\n        const comments = `//${this.name}`;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n\r\n        this._writeTextureRead(state);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                this._writeOutput(state, output, output.name);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.convertToGammaSpace = this.convertToGammaSpace;\r\n        serializationObject.convertToLinearSpace = this.convertToLinearSpace;\r\n        if (this.texture && !this.texture.isRenderTarget) {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.convertToGammaSpace = serializationObject.convertToGammaSpace;\r\n        this.convertToLinearSpace = !!serializationObject.convertToLinearSpace;\r\n\r\n        if (serializationObject.texture) {\r\n            rootUrl = serializationObject.texture.url.indexOf(\"data:\") === 0 ? \"\" : rootUrl;\r\n            this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl) as Texture;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.CurrentScreenBlock\", CurrentScreenBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAKhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;;;;;;AAS9C,MAAO,kBAAmB,uLAAQ,oBAAiB;IAOrD;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAiBD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QAjClD,IAAA,CAAA,YAAY,GAAG,gBAAgB,CAAC;QAkB1C;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,KAAK,CAAC;QAEnC;;WAEG,CACI,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAShC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,mPAAqC,CAAC,UAAU,EAAE,KAAK,gMAAE,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QAE9H,IAAI,CAAC,cAAc,CAAC,MAAM,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,KAAK,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QAC3G,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QAExG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,4MACtD,wCAAqC,CAAC,OAAO,8MAAG,wCAAqC,CAAC,OAAO,8MAAG,wCAAqC,CAAC,OAAO,CAChJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClD,CAAC;IAED,IAAoB,MAAM,GAAA;QACtB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC;YACvB,qMAAO,2BAAwB,CAAC,iBAAiB,CAAC;QACtD,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,WAAY,CAAC,OAAO,EAAE,CAAC;YAC/B,qMAAO,2BAAwB,CAAC,iBAAiB,CAAC;QACtD,CAAC;QAED,qMAAO,2BAAwB,CAAC,QAAQ,CAAC;IAC7C,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QACzE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;IAC7E,CAAC;IAEe,OAAO,GAAA;QACnB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,cAAc,CAAC,KAA6B,EAAA;QAClD,OAAO,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC;IACpD,CAAC;IAES,iBAAiB,CAAC,KAA6B,EAAA;QACrD,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,OAAO,CAAC,cAAe,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC7C,MAAM,iBAAiB,GAAG,OAAO,CAAC,cAAe,CAAC,UAAwB,CAAC;YAE3E,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;gBACjC,KAAK,CAAC,sBAAsB,CAAC,OAAO,CAAC,sBAAsB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;YAChH,CAAC;QACL,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,WAAW,CAAA,GAAA,EAAM,OAAO,CAAC,sBAAsB,CAAA,MAAA,CAAQ,CAAC;QAE3F,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,yBAAyB,CAAC,EAAE,CAAC;YAC1D,OAAO;QACX,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEpC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;IACL,CAAC;IAES,iBAAiB,CAAC,KAA6B,EAAE,UAAU,GAAG,KAAK,EAAA;QACzE,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,KAAK,CAAC,MAAM,KAAK,yNAAwB,CAAC,QAAQ,EAAE,CAAC;gBACrD,OAAO;YACX,CAAC;YACD,MAAM,eAAe,GACjB,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,IACtC,CAAA,UAAA,EAAa,IAAI,CAAC,YAAY,CAAA,CAAA,CAAG,GACjC,CAAA,mBAAA,EAAsB,IAAI,CAAC,YAAY,CAAA,EAAA,EAAK,IAAI,CAAC,YAAY,GAAG,CAAA,OAAA,CAAS,CAAC,CAAA,CAAA,eAAiB,GAAG,CAAC;YAEzG,MAAM,UAAU,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAE7E,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,eAAe,CAAA,CAAA,EAAI,OAAO,CAAC,sBAAsB,GAAG,UAAU,CAAA,IAAA,CAAM,CAAC;YACrM,OAAO;QACX,CAAC;QAED,MAAM,eAAe,GACjB,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,IACtC,CAAA,UAAA,EAAa,IAAI,CAAC,YAAY,CAAA,CAAA,CAAG,GACjC,CAAA,cAAA,EAAiB,IAAI,CAAC,YAAY,CAAA,EAAA,EAAK,IAAI,CAAC,YAAY,GAAG,CAAA,OAAA,CAAS,CAAC,CAAA,CAAA,eAAiB,GAAG,CAAC;QAEpG,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YAClE,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,eAAe,CAAA,CAAA,EAAI,OAAO,CAAC,sBAAsB,CAAA,IAAA,CAAM,CAAC;YACxL,OAAO;QACX,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,eAAe,CAAA,CAAA,EAAI,IAAI,CAAC,WAAW,CAAA,IAAA,CAAM,CAAC;IAC9K,CAAC;IAES,YAAY,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe,EAAE,UAAU,GAAG,KAAK,EAAA;QAC1H,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,KAAK,CAAC,MAAM,KAAK,yNAAwB,CAAC,QAAQ,EAAE,CAAC;gBACrD,OAAO;YACX,CAAC;YAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,gBAAgB,CAAA,CAAA,EAAI,OAAO,CAAA,GAAA,CAAK,CAAC;YAEtG,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,kMAAK,4BAAwB,CAAC,QAAQ,EAAE,CAAC;YAClE,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,gBAAgB,CAAA,CAAA,EAAI,OAAO,CAAA,GAAA,CAAK,CAAC;YACtG,OAAO;QACX,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,gBAAgB,CAAA,CAAA,EAAI,OAAO,CAAA,GAAA,CAAK,CAAC;QAEtG,KAAK,CAAC,iBAAiB,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,iBAAiB,CAAA,EAAA,CAAI,CAAC;QAChE,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,CAAC,sBAAsB,CAAA,gBAAA,EAAmB,MAAM,CAAC,sBAAsB,CAAA,IAAA,CAAM,CAAC;QAClH,KAAK,CAAC,iBAAiB,IAAI,CAAA,QAAA,CAAU,CAAC;QAEtC,KAAK,CAAC,iBAAiB,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,gBAAgB,CAAA,EAAA,CAAI,CAAC;QAC/D,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,CAAC,sBAAsB,CAAA,iBAAA,EAAoB,MAAM,CAAC,sBAAsB,CAAA,IAAA,CAAM,CAAC;QACnH,KAAK,CAAC,iBAAiB,IAAI,CAAA,QAAA,CAAU,CAAC;IAC1C,CAAC;IAES,iBAAiB,CAAC,KAA6B,EAAA;QACrD,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAC9F,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAEtE,IAAI,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACpD,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACnD,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC;QACD,IAAI,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACvD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAE9C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE9B,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,SAAS;YACT,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,2BAA2B,CAAC,EAAE,CAAC;YAC5D,OAAO;QACX,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAC9D,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE5D,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE9B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC/C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC3D,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;QACnE,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;QAEvE,IAAI,mBAAmB,CAAC,OAAO,EAAE,CAAC;YAC9B,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAChF,IAAI,CAAC,OAAO,2KAAG,UAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAY,CAAC;QACzF,CAAC;IACL,CAAC;CACJ;6JAED,gBAAA,AAAa,EAAC,4BAA4B,EAAE,kBAAkB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/smartFilterTextureBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/smartFilterTextureBlock.ts"], "sourcesContent": ["import type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\nimport { CurrentScreenBlock } from \"./currentScreenBlock\";\nimport { RegisterClass } from \"core/Misc/typeStore\";\nimport { InputBlock } from \"../Input/inputBlock\";\nimport type { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\nimport type { NodeMaterial } from \"../../nodeMaterial\";\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"core/Decorators/nodeDecorator\";\nimport type { Scene } from \"core/scene\";\nimport { SfeModeDefine } from \"../Fragment/smartFilterFragmentOutputBlock\";\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\n\n/**\n * Base block used for creating Smart Filter shader blocks for the SFE framework.\n * This block extends the functionality of CurrentScreenBlock, as both are used\n * to represent arbitrary 2D textures to compose, and work similarly.\n */\nexport class SmartFilterTextureBlock extends CurrentScreenBlock {\n    private _firstInit: boolean = true;\n\n    /**\n     * A boolean indicating whether this block should be the main input for the SFE pipeline.\n     * If true, it can be used in SFE for auto-disabling.\n     */\n    @editableInPropertyPage(\"Is Main Input\", PropertyTypeForEdition.Boolean, undefined, { notifiers: { rebuild: true } })\n    public isMainInput: boolean = false;\n\n    /**\n     * Create a new SmartFilterTextureBlock\n     * @param name defines the block name\n     */\n    public constructor(name: string) {\n        super(name);\n    }\n\n    /**\n     * Gets the current class name\n     * @returns the class name\n     */\n    public override getClassName() {\n        return \"SmartFilterTextureBlock\";\n    }\n\n    /**\n     * Initialize the block and prepare the context for build\n     * @param state defines the state that will be used for the build\n     */\n    public override initialize(state: NodeMaterialBuildState) {\n        if (this._firstInit) {\n            this._samplerName = state._getFreeVariableName(this.name);\n            this._firstInit = false;\n        }\n    }\n\n    protected override _getMainUvName(state: NodeMaterialBuildState): string {\n        // Get the ScreenUVBlock's name, which is required for SFE and should be vUV.\n        // NOTE: In the future, when we move to vertex shaders, update this to check for the nearest vec2 varying output.\n        const screenUv = state.sharedData.nodeMaterial.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"postprocess_uv\");\n        if (!screenUv || !screenUv.isAnAncestorOf(this)) {\n            state.sharedData.raiseBuildError(\"SmartFilterTextureBlock: 'postprocess_uv' attribute from ScreenUVBlock is required.\");\n            return \"\";\n        }\n        return screenUv.associatedVariableName;\n    }\n\n    protected override _emitUvAndSampler(state: NodeMaterialBuildState): void {\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\n            // Wrap the varying in a define, as it won't be needed in SFE.\n            state._emitVaryingFromString(this._mainUVName, NodeMaterialBlockConnectionPointTypes.Vector2, SfeModeDefine, true);\n\n            // Append `// main` to denote this as the main input texture to composite\n            const annotation = this.isMainInput ? \"// main\" : undefined;\n            state._emit2DSampler(this._samplerName, undefined, undefined, annotation);\n        }\n    }\n\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\n        if (!this.uv.isConnected) {\n            let uvInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"postprocess_uv\" && additionalFilteringInfo(b));\n\n            if (!uvInput) {\n                uvInput = new InputBlock(\"uv\");\n                uvInput.setAsAttribute(\"postprocess_uv\");\n            }\n            uvInput.output.connectTo(this.uv);\n        }\n    }\n\n    public override _postBuildBlock(): void {\n        this._firstInit = true;\n    }\n\n    public override serialize(): any {\n        const serializationObject = super.serialize();\n        serializationObject.isMainInput = this.isMainInput;\n        return serializationObject;\n    }\n\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\n        super._deserialize(serializationObject, scene, rootUrl);\n        this.isMainInput = serializationObject.isMainInput;\n    }\n}\n\nRegisterClass(\"BABYLON.SmartFilterTextureBlock\", SmartFilterTextureBlock);\n"], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,aAAa,EAAE,sCAA4B;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,sBAAsB,EAA0B,gDAAsC;AAE/F,OAAO,EAAE,aAAa,EAAE,MAAM,4CAA4C,CAAC;AAC3E,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;;;;;;;;;AAOpG,MAAO,uBAAwB,0MAAQ,qBAAkB;IAU3D;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAdR,IAAA,CAAA,UAAU,GAAY,IAAI,CAAC;QAEnC;;;WAGG,CAEI,IAAA,CAAA,WAAW,GAAY,KAAK,CAAC;IAQpC,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,yBAAyB,CAAC;IACrC,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAC5B,CAAC;IACL,CAAC;IAEkB,cAAc,CAAC,KAA6B,EAAA;QAC3D,6EAA6E;QAC7E,iHAAiH;QACjH,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAC;QAC7H,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,qFAAqF,CAAC,CAAC;YACxH,OAAO,EAAE,CAAC;QACd,CAAC;QACD,OAAO,QAAQ,CAAC,sBAAsB,CAAC;IAC3C,CAAC;IAEkB,iBAAiB,CAAC,KAA6B,EAAA;QAC9D,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,8DAA8D;YAC9D,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,4MAAE,yCAAqC,CAAC,OAAO,mNAAE,gBAAa,EAAE,IAAI,CAAC,CAAC;YAEnH,yEAAyE;YACzE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;YAC5D,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC;IAEe,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,CAAG,CAAD,GAAK,EAAA;QAC5H,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,OAAO,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,gBAAgB,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEnI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO,GAAG,8LAAI,aAAU,CAAC,IAAI,CAAC,CAAC;gBAC/B,OAAO,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAC7C,CAAC;YACD,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC;IACL,CAAC;IAEe,eAAe,GAAA;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAC3B,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAC9C,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACxD,IAAI,CAAC,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC;IACvD,CAAC;CACJ;wJA7EU,aAAA,EAAA;2KADN,yBAAA,AAAsB,EAAC,eAAe,EAAA,EAAA,kCAAA,KAAkC,SAAS,EAAE;QAAE,SAAS,EAAE;YAAE,OAAO,EAAE,IAAI;QAAA,CAAE;IAAA,CAAE,CAAC;4DACjF;6JA+ExC,gBAAA,AAAa,EAAC,iCAAiC,EAAE,uBAAuB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/fogBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/fogBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\n\r\nimport { GetFogState } from \"core/Materials/materialHelper.functions\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to add support for scene fog\r\n */\r\nexport class FogBlock extends NodeMaterialBlock {\r\n    private _fogDistanceName: string;\r\n    private _fogParameters: string;\r\n\r\n    /**\r\n     * Create a new FogBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment, false);\r\n\r\n        // Vertex\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"view\", NodeMaterialBlockConnectionPointTypes.Matrix, false, NodeMaterialBlockTargets.Vertex);\r\n\r\n        // Fragment\r\n        this.registerInput(\"input\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"fogColor\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\"output\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.input.addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Color4\r\n        );\r\n        this.fogColor.addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Color4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"FogBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the view input component\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the color input component\r\n     */\r\n    public get input(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the fog color input component\r\n     */\r\n    public get fogColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get output(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync(state.shaderLanguage);\r\n    }\r\n\r\n    private async _initShaderSourceAsync(shaderLanguage: ShaderLanguage) {\r\n        this._codeIsReady = false;\r\n\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            await import(\"../../../../ShadersWGSL/ShadersInclude/fogFragmentDeclaration\");\r\n        } else {\r\n            await import(\"../../../../Shaders/ShadersInclude/fogFragmentDeclaration\");\r\n        }\r\n\r\n        this._codeIsReady = true;\r\n        this.onCodeIsReadyObservable.notifyObservers(this);\r\n    }\r\n\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.view.isConnected) {\r\n            let viewInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.View && additionalFilteringInfo(b));\r\n\r\n            if (!viewInput) {\r\n                viewInput = new InputBlock(\"view\");\r\n                viewInput.setAsSystemValue(NodeMaterialSystemValues.View);\r\n            }\r\n            viewInput.output.connectTo(this.view);\r\n        }\r\n        if (!this.fogColor.isConnected) {\r\n            let fogColorInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.FogColor && additionalFilteringInfo(b));\r\n\r\n            if (!fogColorInput) {\r\n                fogColorInput = new InputBlock(\"fogColor\", undefined, NodeMaterialBlockConnectionPointTypes.Color3);\r\n                fogColorInput.setAsSystemValue(NodeMaterialSystemValues.FogColor);\r\n            }\r\n            fogColorInput.output.connectTo(this.fogColor);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines, nodeMaterial: NodeMaterial, mesh?: AbstractMesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        const scene = mesh.getScene();\r\n        defines.setValue(\"FOG\", nodeMaterial.fogEnabled && GetFogState(mesh, scene));\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        const scene = mesh.getScene();\r\n        effect.setFloat4(this._fogParameters, scene.fogMode, scene.fogStart, scene.fogEnd, scene.fogDensity);\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.sharedData.blocksWithDefines.push(this);\r\n            state.sharedData.bindableBlocks.push(this);\r\n\r\n            let replaceStrings = [];\r\n            let prefix1 = \"\";\r\n            let prefix2 = \"\";\r\n\r\n            if (state.shaderLanguage === ShaderLanguage.WGSL) {\r\n                replaceStrings = [\r\n                    { search: /fn CalcFogFactor\\(\\)/, replace: \"fn CalcFogFactor(vFogDistance: vec3f, vFogInfos: vec4f)\" },\r\n                    { search: /uniforms.vFogInfos/g, replace: \"vFogInfos\" },\r\n                    { search: /fragmentInputs.vFogDistance/g, replace: \"vFogDistance\" },\r\n                ];\r\n\r\n                prefix1 = \"fragmentInputs.\";\r\n                prefix2 = \"uniforms.\";\r\n            } else {\r\n                replaceStrings = [{ search: /float CalcFogFactor\\(\\)/, replace: \"float CalcFogFactor(vec3 vFogDistance, vec4 vFogInfos)\" }];\r\n            }\r\n\r\n            state._emitFunctionFromInclude(\"fogFragmentDeclaration\", `//${this.name}`, {\r\n                removeUniforms: true,\r\n                removeVaryings: true,\r\n                removeIfDef: false,\r\n                replaceStrings: replaceStrings,\r\n            });\r\n\r\n            const tempFogVariablename = state._getFreeVariableName(\"fog\");\r\n            const color = this.input;\r\n            const fogColor = this.fogColor;\r\n            this._fogParameters = state._getFreeVariableName(\"fogParameters\");\r\n            const output = this._outputs[0];\r\n\r\n            state._emitUniformFromString(this._fogParameters, NodeMaterialBlockConnectionPointTypes.Vector4);\r\n\r\n            state.compilationString += `#ifdef FOG\\n`;\r\n            state.compilationString += `${state._declareLocalVar(tempFogVariablename, NodeMaterialBlockConnectionPointTypes.Float)} = CalcFogFactor(${prefix1}${this._fogDistanceName}, ${prefix2}${this._fogParameters});\\n`;\r\n            state.compilationString +=\r\n                state._declareOutput(output) +\r\n                ` = ${tempFogVariablename} * ${color.associatedVariableName}.rgb + (1.0 - ${tempFogVariablename}) * ${fogColor.associatedVariableName}.rgb;\\n`;\r\n            state.compilationString += `#else\\n${state._declareOutput(output)} =  ${color.associatedVariableName}.rgb;\\n`;\r\n            state.compilationString += `#endif\\n`;\r\n        } else {\r\n            const worldPos = this.worldPosition;\r\n            const view = this.view;\r\n            this._fogDistanceName = state._getFreeVariableName(\"vFogDistance\");\r\n            state._emitVaryingFromString(this._fogDistanceName, NodeMaterialBlockConnectionPointTypes.Vector3);\r\n            const prefix = state.shaderLanguage === ShaderLanguage.WGSL ? \"vertexOutputs.\" : \"\";\r\n            state.compilationString += `${prefix}${this._fogDistanceName} = (${view.associatedVariableName} * ${worldPos.associatedVariableName}).xyz;\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.FogBlock\", FogBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAMhF,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,WAAW,EAAE,6CAAgD;;;;;;;;AAMhE,MAAO,QAAS,uLAAQ,oBAAiB;IAI3C;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAE/D,SAAS;QACT,IAAI,CAAC,aAAa,CAAC,eAAe,6MAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,gMAAE,2BAAwB,CAAC,MAAM,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,MAAM,6MAAE,wCAAqC,CAAC,MAAM,EAAE,KAAK,gMAAE,2BAAwB,CAAC,MAAM,CAAC,CAAC;QAEjH,WAAW;QACX,IAAI,CAAC,aAAa,CAAC,OAAO,6MAAE,wCAAqC,CAAC,UAAU,EAAE,KAAK,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QACxH,IAAI,CAAC,aAAa,CAAC,UAAU,6MAAE,wCAAqC,CAAC,UAAU,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAE3H,IAAI,CAAC,cAAc,CAAC,QAAQ,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAE/G,IAAI,CAAC,KAAK,CAAC,0CAA0C,4MACjD,wCAAqC,CAAC,MAAM,8MAAG,wCAAqC,CAAC,OAAO,8MAAG,wCAAqC,CAAC,MAAM,CAC9I,CAAC;QACF,IAAI,CAAC,QAAQ,CAAC,0CAA0C,CACpD,mPAAqC,CAAC,MAAM,8MAAG,wCAAqC,CAAC,OAAO,8MAAG,wCAAqC,CAAC,MAAM,CAC9I,CAAC;IACN,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,UAAU,CAAC,KAA6B,EAAA;QACpD,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,cAA8B,EAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACzC,MAAM,MAAM,CAAC,+DAA+D,CAAC,CAAC;QAClF,CAAC,MAAM,CAAC;YACJ,MAAM,MAAM,CAAC,2DAA2D,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAEe,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,CAAG,CAAD,GAAK,EAAA;QAC5H,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACzB,IAAI,SAAS,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,mMAAK,2BAAwB,CAAC,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAExI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,SAAS,GAAG,8LAAI,aAAU,CAAC,MAAM,CAAC,CAAC;gBACnC,SAAS,CAAC,gBAAgB,+LAAC,2BAAwB,CAAC,IAAI,CAAC,CAAC;YAC9D,CAAC;YACD,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC7B,IAAI,aAAa,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,kMAAK,4BAAwB,CAAC,QAAQ,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEhJ,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,aAAa,GAAG,8LAAI,aAAU,CAAC,UAAU,EAAE,SAAS,6MAAE,wCAAqC,CAAC,MAAM,CAAC,CAAC;gBACpG,aAAa,CAAC,gBAAgB,CAAC,yNAAwB,CAAC,QAAQ,CAAC,CAAC;YACtE,CAAC;YACD,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAE,YAA0B,EAAE,IAAmB,EAAA;QACxG,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,OAAO,CAAC,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,UAAU,IAAI,kMAAA,AAAW,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IACjF,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;IACzG,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE3C,IAAI,cAAc,GAAG,EAAE,CAAC;YACxB,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,OAAO,GAAG,EAAE,CAAC;YAEjB,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;gBAC/C,cAAc,GAAG;oBACb;wBAAE,MAAM,EAAE,sBAAsB;wBAAE,OAAO,EAAE,yDAAyD;oBAAA,CAAE;oBACtG;wBAAE,MAAM,EAAE,qBAAqB;wBAAE,OAAO,EAAE,WAAW;oBAAA,CAAE;oBACvD;wBAAE,MAAM,EAAE,8BAA8B;wBAAE,OAAO,EAAE,cAAc;oBAAA,CAAE;iBACtE,CAAC;gBAEF,OAAO,GAAG,iBAAiB,CAAC;gBAC5B,OAAO,GAAG,WAAW,CAAC;YAC1B,CAAC,MAAM,CAAC;gBACJ,cAAc,GAAG;oBAAC;wBAAE,MAAM,EAAE,yBAAyB;wBAAE,OAAO,EAAE,wDAAwD;oBAAA,CAAE;iBAAC,CAAC;YAChI,CAAC;YAED,KAAK,CAAC,wBAAwB,CAAC,wBAAwB,EAAE,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,EAAE;gBACvE,cAAc,EAAE,IAAI;gBACpB,cAAc,EAAE,IAAI;gBACpB,WAAW,EAAE,KAAK;gBAClB,cAAc,EAAE,cAAc;aACjC,CAAC,CAAC;YAEH,MAAM,mBAAmB,GAAG,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAC9D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;YAC/B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;YAClE,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEhC,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,cAAc,4MAAE,yCAAqC,CAAC,OAAO,CAAC,CAAC;YAEjG,KAAK,CAAC,iBAAiB,IAAI,CAAA,YAAA,CAAc,CAAC;YAC1C,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,mPAAqC,CAAC,KAAK,CAAC,CAAA,iBAAA,EAAoB,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAA,EAAA,EAAK,OAAO,GAAG,IAAI,CAAC,cAAc,CAAA,IAAA,CAAM,CAAC;YAClN,KAAK,CAAC,iBAAiB,IACnB,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,GAC5B,CAAA,GAAA,EAAM,mBAAmB,CAAA,GAAA,EAAM,KAAK,CAAC,sBAAsB,CAAA,cAAA,EAAiB,mBAAmB,CAAA,IAAA,EAAO,QAAQ,CAAC,sBAAsB,CAAA,OAAA,CAAS,CAAC;YACnJ,KAAK,CAAC,iBAAiB,IAAI,CAAA,OAAA,EAAU,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,IAAA,EAAO,KAAK,CAAC,sBAAsB,CAAA,OAAA,CAAS,CAAC;YAC9G,KAAK,CAAC,iBAAiB,IAAI,CAAA,QAAA,CAAU,CAAC;QAC1C,CAAC,MAAM,CAAC;YACJ,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;YACpC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;YACnE,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAC;YACnG,MAAM,MAAM,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;YACpF,KAAK,CAAC,iBAAiB,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAA,IAAA,EAAO,IAAI,CAAC,sBAAsB,CAAA,GAAA,EAAM,QAAQ,CAAC,sBAAsB,CAAA,QAAA,CAAU,CAAC;QAClJ,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;6JAED,gBAAA,AAAa,EAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/lightBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/lightBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { Light } from \"../../../../Lights/light\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\n\r\nimport { Logger } from \"../../../../Misc/logger\";\r\nimport { BindLight, BindLights, PrepareDefinesForLight, PrepareDefinesForLights, PrepareUniformsAndSamplersForLight } from \"../../../materialHelper.functions\";\r\nimport { ShaderLanguage } from \"../../../../Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to add light in the fragment shader\r\n */\r\nexport class LightBlock extends NodeMaterialBlock {\r\n    private _lightId: number = 0;\r\n\r\n    /**\r\n     * Gets or sets the light associated with this block\r\n     */\r\n    public light: Nullable<Light>;\r\n\r\n    /** Indicates that no code should be generated in the vertex shader. Can be useful in some specific circumstances (like when doing ray marching for eg) */\r\n    @editableInPropertyPage(\"Generate only fragment code\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: { rebuild: true, update: true, onValidation: LightBlock._OnGenerateOnlyFragmentCodeChanged },\r\n    })\r\n    public generateOnlyFragmentCode = false;\r\n\r\n    private static _OnGenerateOnlyFragmentCodeChanged(block: NodeMaterialBlock, _propertyName: string): boolean {\r\n        const that = block as LightBlock;\r\n\r\n        if (that.worldPosition.isConnected) {\r\n            that.generateOnlyFragmentCode = !that.generateOnlyFragmentCode;\r\n            Logger.Error(\"The worldPosition input must not be connected to be able to switch!\");\r\n            return false;\r\n        }\r\n\r\n        that._setTarget();\r\n\r\n        return true;\r\n    }\r\n\r\n    private _setTarget(): void {\r\n        this._setInitialTarget(this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.VertexAndFragment);\r\n        this.getInputByName(\"worldPosition\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n    }\r\n\r\n    /**\r\n     * Create a new LightBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"cameraPosition\", NodeMaterialBlockConnectionPointTypes.Vector3, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"glossiness\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"glossPower\", NodeMaterialBlockConnectionPointTypes.Float, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"diffuseColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"specularColor\", NodeMaterialBlockConnectionPointTypes.Color3, true, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"view\", NodeMaterialBlockConnectionPointTypes.Matrix, true);\r\n\r\n        this.registerOutput(\"diffuseOutput\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"specularOutput\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"shadow\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"LightBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the camera (or eye) position component\r\n     */\r\n    public get cameraPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the glossiness component\r\n     */\r\n    public get glossiness(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the glossiness power component\r\n     */\r\n    public get glossPower(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the diffuse color component\r\n     */\r\n    public get diffuseColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the specular color component\r\n     */\r\n    public get specularColor(): NodeMaterialConnectionPoint {\r\n        return this._inputs[6];\r\n    }\r\n\r\n    /**\r\n     * Gets the view matrix component\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this._inputs[7];\r\n    }\r\n\r\n    /**\r\n     * Gets the diffuse output component\r\n     */\r\n    public get diffuseOutput(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the specular output component\r\n     */\r\n    public get specularOutput(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the shadow output component\r\n     */\r\n    public get shadow(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync(state.shaderLanguage);\r\n    }\r\n\r\n    private async _initShaderSourceAsync(shaderLanguage: ShaderLanguage) {\r\n        this._codeIsReady = false;\r\n\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            await Promise.all([\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/lightFragment\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/lightUboDeclaration\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/lightVxUboDeclaration\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/helperFunctions\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/lightsFragmentFunctions\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/shadowsFragmentFunctions\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/shadowsVertex\"),\r\n            ]);\r\n        } else {\r\n            await Promise.all([\r\n                import(\"../../../../Shaders/ShadersInclude/lightFragmentDeclaration\"),\r\n                import(\"../../../../Shaders/ShadersInclude/lightFragment\"),\r\n                import(\"../../../../Shaders/ShadersInclude/lightUboDeclaration\"),\r\n                import(\"../../../../Shaders/ShadersInclude/lightVxUboDeclaration\"),\r\n                import(\"../../../../Shaders/ShadersInclude/lightVxFragmentDeclaration\"),\r\n                import(\"../../../../Shaders/ShadersInclude/helperFunctions\"),\r\n                import(\"../../../../Shaders/ShadersInclude/lightsFragmentFunctions\"),\r\n                import(\"../../../../Shaders/ShadersInclude/shadowsFragmentFunctions\"),\r\n                import(\"../../../../Shaders/ShadersInclude/shadowsVertex\"),\r\n            ]);\r\n        }\r\n\r\n        this._codeIsReady = true;\r\n        this.onCodeIsReadyObservable.notifyObservers(this);\r\n    }\r\n\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.cameraPosition.isConnected) {\r\n            let cameraPositionInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.CameraPosition && additionalFilteringInfo(b));\r\n\r\n            if (!cameraPositionInput) {\r\n                cameraPositionInput = new InputBlock(\"cameraPosition\");\r\n                cameraPositionInput.setAsSystemValue(NodeMaterialSystemValues.CameraPosition);\r\n            }\r\n            cameraPositionInput.output.connectTo(this.cameraPosition);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines, nodeMaterial: NodeMaterial, mesh?: AbstractMesh) {\r\n        if (!mesh || !defines._areLightsDirty) {\r\n            return;\r\n        }\r\n\r\n        const scene = mesh.getScene();\r\n\r\n        if (!this.light) {\r\n            PrepareDefinesForLights(scene, mesh, defines, true, nodeMaterial.maxSimultaneousLights);\r\n        } else {\r\n            const state = {\r\n                needNormals: false,\r\n                needRebuild: false,\r\n                lightmapMode: false,\r\n                shadowEnabled: false,\r\n                specularEnabled: false,\r\n            };\r\n\r\n            PrepareDefinesForLight(scene, mesh, this.light, this._lightId, defines, true, state);\r\n\r\n            if (state.needRebuild) {\r\n                defines.rebuild();\r\n            }\r\n        }\r\n    }\r\n\r\n    public override updateUniformsAndSamples(state: NodeMaterialBuildState, nodeMaterial: NodeMaterial, defines: NodeMaterialDefines, uniformBuffers: string[]) {\r\n        state.samplers.push(\"areaLightsLTC1Sampler\");\r\n        state.samplers.push(\"areaLightsLTC2Sampler\");\r\n        for (let lightIndex = 0; lightIndex < nodeMaterial.maxSimultaneousLights; lightIndex++) {\r\n            if (!defines[\"LIGHT\" + lightIndex]) {\r\n                break;\r\n            }\r\n            const onlyUpdateBuffersList = state.uniforms.indexOf(\"vLightData\" + lightIndex) >= 0;\r\n            PrepareUniformsAndSamplersForLight(\r\n                lightIndex,\r\n                state.uniforms,\r\n                state.samplers,\r\n                defines[\"PROJECTEDLIGHTTEXTURE\" + lightIndex],\r\n                uniformBuffers,\r\n                onlyUpdateBuffersList,\r\n                defines[\"IESLIGHTTEXTURE\" + lightIndex]\r\n            );\r\n        }\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        const scene = mesh.getScene();\r\n\r\n        if (!this.light) {\r\n            BindLights(scene, mesh, effect, true, nodeMaterial.maxSimultaneousLights);\r\n        } else {\r\n            BindLight(this.light, this._lightId, scene, effect, true);\r\n        }\r\n    }\r\n\r\n    private _injectVertexCode(state: NodeMaterialBuildState) {\r\n        const worldPos = this.worldPosition;\r\n        const comments = `//${this.name}`;\r\n\r\n        // Declaration\r\n        if (!this.light) {\r\n            // Emit for all lights\r\n            state._emitFunctionFromInclude(state.supportUniformBuffers ? \"lightVxUboDeclaration\" : \"lightVxFragmentDeclaration\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n            });\r\n            this._lightId = 0;\r\n\r\n            state.sharedData.dynamicUniformBlocks.push(this);\r\n        } else {\r\n            this._lightId = (state.counters[\"lightCounter\"] !== undefined ? state.counters[\"lightCounter\"] : -1) + 1;\r\n            state.counters[\"lightCounter\"] = this._lightId;\r\n\r\n            state._emitFunctionFromInclude(\r\n                state.supportUniformBuffers ? \"lightVxUboDeclaration\" : \"lightVxFragmentDeclaration\",\r\n                comments,\r\n                {\r\n                    replaceStrings: [{ search: /{X}/g, replace: this._lightId.toString() }],\r\n                },\r\n                this._lightId.toString()\r\n            );\r\n        }\r\n\r\n        // Inject code in vertex\r\n        const worldPosVaryingName = \"v_\" + worldPos.associatedVariableName;\r\n\r\n        if (state._emitVaryingFromString(worldPosVaryingName, NodeMaterialBlockConnectionPointTypes.Vector4)) {\r\n            state.compilationString += (state.shaderLanguage === ShaderLanguage.WGSL ? \"vertexOutputs.\" : \"\") + `${worldPosVaryingName} = ${worldPos.associatedVariableName};\\n`;\r\n        }\r\n\r\n        if (this.light) {\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowsVertex\", comments, {\r\n                replaceStrings: [\r\n                    { search: /{X}/g, replace: this._lightId.toString() },\r\n                    { search: /worldPos/g, replace: worldPos.associatedVariableName },\r\n                ],\r\n            });\r\n        } else {\r\n            state.compilationString += `${state._declareLocalVar(\"worldPos\", NodeMaterialBlockConnectionPointTypes.Vector4)} = ${worldPos.associatedVariableName};\\n`;\r\n            if (this.view.isConnected) {\r\n                state.compilationString += `${state._declareLocalVar(\"view\", NodeMaterialBlockConnectionPointTypes.Matrix)} = ${this.view.associatedVariableName};\\n`;\r\n            }\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowsVertex\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n            });\r\n        }\r\n    }\r\n\r\n    private _injectUBODeclaration(state: NodeMaterialBuildState) {\r\n        const comments = `//${this.name}`;\r\n\r\n        if (!this.light) {\r\n            // Emit for all lights\r\n            state._emitFunctionFromInclude(state.supportUniformBuffers ? \"lightUboDeclaration\" : \"lightFragmentDeclaration\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n                substitutionVars: this.generateOnlyFragmentCode ? \"varying,\" : undefined,\r\n            });\r\n        } else {\r\n            state._emitFunctionFromInclude(\r\n                state.supportUniformBuffers ? \"lightUboDeclaration\" : \"lightFragmentDeclaration\",\r\n                comments,\r\n                {\r\n                    replaceStrings: [{ search: /{X}/g, replace: this._lightId.toString() }],\r\n                },\r\n                this._lightId.toString()\r\n            );\r\n        }\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const isWGSL = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        const addF = isWGSL ? \"f\" : \"\";\r\n\r\n        const comments = `//${this.name}`;\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            // Vertex\r\n            this._injectVertexCode(state);\r\n            return;\r\n        }\r\n\r\n        if (this.generateOnlyFragmentCode) {\r\n            state.sharedData.dynamicUniformBlocks.push(this);\r\n        }\r\n        // Fragment\r\n        const accessor = isWGSL ? \"fragmentInputs.\" : \"\";\r\n        state.sharedData.forcedBindableBlocks.push(this);\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        const worldPos = this.worldPosition;\r\n\r\n        let worldPosVariableName = worldPos.associatedVariableName;\r\n        if (this.generateOnlyFragmentCode) {\r\n            worldPosVariableName = state._getFreeVariableName(\"globalWorldPos\");\r\n            state._emitFunction(\"light_globalworldpos\", `${state._declareLocalVar(worldPosVariableName, NodeMaterialBlockConnectionPointTypes.Vector3)};\\n`, comments);\r\n            state.compilationString += `${worldPosVariableName} = ${worldPos.associatedVariableName}.xyz;\\n`;\r\n\r\n            state.compilationString += state._emitCodeFromInclude(\"shadowsVertex\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n                substitutionVars: this.generateOnlyFragmentCode ? `worldPos,${worldPos.associatedVariableName}` : undefined,\r\n            });\r\n        } else {\r\n            worldPosVariableName = accessor + \"v_\" + worldPosVariableName + \".xyz\";\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n\r\n        let replaceString = { search: /vPositionW/g, replace: worldPosVariableName };\r\n\r\n        if (isWGSL) {\r\n            replaceString = { search: /fragmentInputs\\.vPositionW/g, replace: worldPosVariableName };\r\n        }\r\n\r\n        state._emitFunctionFromInclude(\"lightsFragmentFunctions\", comments, {\r\n            replaceStrings: [replaceString],\r\n        });\r\n\r\n        state._emitFunctionFromInclude(\"shadowsFragmentFunctions\", comments, {\r\n            replaceStrings: [replaceString],\r\n        });\r\n\r\n        this._injectUBODeclaration(state);\r\n\r\n        // Code\r\n        if (this._lightId === 0) {\r\n            if (state._registerTempVariable(\"viewDirectionW\")) {\r\n                state.compilationString += `${state._declareLocalVar(\"viewDirectionW\", NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(${this.cameraPosition.associatedVariableName} - ${worldPosVariableName});\\n`;\r\n            }\r\n            state.compilationString += isWGSL ? `var info: lightingInfo;\\n` : `lightingInfo info;\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"shadow\", NodeMaterialBlockConnectionPointTypes.Float)} = 1.;\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"aggShadow\", NodeMaterialBlockConnectionPointTypes.Float)} = 0.;\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"numLights\", NodeMaterialBlockConnectionPointTypes.Float)} = 0.;\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"glossiness\", NodeMaterialBlockConnectionPointTypes.Float)} = ${this.glossiness.isConnected ? this.glossiness.associatedVariableName : \"1.0\"} * ${\r\n                this.glossPower.isConnected ? this.glossPower.associatedVariableName : \"1024.0\"\r\n            };\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"diffuseBase\", NodeMaterialBlockConnectionPointTypes.Vector3)} = vec3${addF}(0., 0., 0.);\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"specularBase\", NodeMaterialBlockConnectionPointTypes.Vector3)}  = vec3${addF}(0., 0., 0.);\\n`;\r\n            state.compilationString += `${state._declareLocalVar(\"normalW\", NodeMaterialBlockConnectionPointTypes.Vector3)} = ${this.worldNormal.associatedVariableName}.xyz;\\n`;\r\n        }\r\n\r\n        if (this.light) {\r\n            let replaceString = [{ search: /vPositionW/g, replace: worldPosVariableName + \".xyz\" }];\r\n\r\n            if (isWGSL) {\r\n                replaceString = [\r\n                    { search: /fragmentInputs\\.vPositionW/g, replace: worldPosVariableName + \".xyz\" },\r\n                    { search: /uniforms\\.vReflectivityColor/g, replace: \"vReflectivityColor\" },\r\n                ];\r\n            }\r\n\r\n            state.compilationString += state._emitCodeFromInclude(\"lightFragment\", comments, {\r\n                replaceStrings: [{ search: /{X}/g, replace: this._lightId.toString() }, ...replaceString],\r\n            });\r\n        } else {\r\n            let substitutionVars = `vPositionW,${worldPosVariableName}.xyz`;\r\n\r\n            if (isWGSL) {\r\n                substitutionVars = `fragmentInputs.vPositionW,${worldPosVariableName}.xyz`;\r\n            }\r\n            state.compilationString += state._emitCodeFromInclude(\"lightFragment\", comments, {\r\n                repeatKey: \"maxSimultaneousLights\",\r\n                substitutionVars: substitutionVars,\r\n            });\r\n        }\r\n\r\n        if (this._lightId === 0) {\r\n            state.compilationString += `aggShadow = aggShadow / numLights;\\n`;\r\n        }\r\n\r\n        const diffuseOutput = this.diffuseOutput;\r\n        const specularOutput = this.specularOutput;\r\n\r\n        state.compilationString +=\r\n            state._declareOutput(diffuseOutput) + ` = diffuseBase${this.diffuseColor.isConnected ? \" * \" + this.diffuseColor.associatedVariableName : \"\"};\\n`;\r\n        if (specularOutput.hasEndpoints) {\r\n            state.compilationString +=\r\n                state._declareOutput(specularOutput) + ` = specularBase${this.specularColor.isConnected ? \" * \" + this.specularColor.associatedVariableName : \"\"};\\n`;\r\n        }\r\n\r\n        if (this.shadow.hasEndpoints) {\r\n            state.compilationString += state._declareOutput(this.shadow) + ` = aggShadow;\\n`;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.generateOnlyFragmentCode = this.generateOnlyFragmentCode;\r\n\r\n        if (this.light) {\r\n            serializationObject.lightId = this.light.id;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        if (serializationObject.lightId) {\r\n            this.light = scene.getLightById(serializationObject.lightId);\r\n        }\r\n\r\n        this.generateOnlyFragmentCode = serializationObject.generateOnlyFragmentCode;\r\n\r\n        this._setTarget();\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.LightBlock\", LightBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAO1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AAEtG,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,sBAAsB,EAAE,uBAAuB,EAAE,kCAAkC,EAAE,MAAM,mCAAmC,CAAC;;;;;;;;;;;AAMzJ,MAAO,UAAW,uLAAQ,oBAAiB;IAcrC,MAAM,CAAC,kCAAkC,CAAC,KAAwB,EAAE,aAAqB,EAAA;QAC7F,MAAM,IAAI,GAAG,KAAmB,CAAC;QAEjC,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC/D,+JAAM,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACpF,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,UAAU,GAAA;QACd,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,yNAAwB,CAAC,QAAQ,CAAC,CAAC,+LAAC,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QACvI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,yNAAwB,CAAC,QAAQ,CAAC,CAAC,+LAAC,2BAAwB,CAAC,MAAM,CAAC;IACvJ,CAAC;IAED;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QArCpD,IAAA,CAAA,QAAQ,GAAW,CAAC,CAAC;QAO7B,wJAAA,EAA0J,CAInJ,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;QA4BpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,mPAAqC,CAAC,OAAO,EAAE,KAAK,gMAAE,2BAAwB,CAAC,MAAM,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,YAAY,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,YAAY,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,aAAa,CAAC,cAAc,4MAAE,yCAAqC,CAAC,MAAM,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC1H,IAAI,CAAC,aAAa,CAAC,eAAe,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,MAAM,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAE/E,IAAI,CAAC,cAAc,CAAC,eAAe,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACtH,IAAI,CAAC,cAAc,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACvH,IAAI,CAAC,cAAc,CAAC,QAAQ,4MAAE,yCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;IAClH,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,UAAU,CAAC,KAA6B,EAAA;QACpD,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,cAA8B,EAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC;;;;;;;;aAQjB,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC;;;;;;;;;;aAUjB,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAEe,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,CAAG,CAAD,GAAK,EAAA;QAC5H,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YACnC,IAAI,mBAAmB,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,mMAAK,2BAAwB,CAAC,cAAc,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5J,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvB,mBAAmB,GAAG,8LAAI,aAAU,CAAC,gBAAgB,CAAC,CAAC;gBACvD,mBAAmB,CAAC,gBAAgB,CAAC,yNAAwB,CAAC,cAAc,CAAC,CAAC;YAClF,CAAC;YACD,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAE,YAA0B,EAAE,IAAmB,EAAA;QACxG,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gMACd,0BAAA,AAAuB,EAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,CAAC,qBAAqB,CAAC,CAAC;QAC5F,CAAC,MAAM,CAAC;YACJ,MAAM,KAAK,GAAG;gBACV,WAAW,EAAE,KAAK;gBAClB,WAAW,EAAE,KAAK;gBAClB,YAAY,EAAE,KAAK;gBACnB,aAAa,EAAE,KAAK;gBACpB,eAAe,EAAE,KAAK;aACzB,CAAC;gMAEF,yBAAsB,AAAtB,EAAuB,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAErF,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBACpB,OAAO,CAAC,OAAO,EAAE,CAAC;YACtB,CAAC;QACL,CAAC;IACL,CAAC;IAEe,wBAAwB,CAAC,KAA6B,EAAE,YAA0B,EAAE,OAA4B,EAAE,cAAwB,EAAA;QACtJ,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC7C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAC7C,IAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,YAAY,CAAC,qBAAqB,EAAE,UAAU,EAAE,CAAE,CAAC;YACrF,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,CAAC;gBACjC,MAAM;YACV,CAAC;YACD,MAAM,qBAAqB,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;+LACrF,sCAAkC,AAAlC,EACI,UAAU,EACV,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,QAAQ,EACd,OAAO,CAAC,uBAAuB,GAAG,UAAU,CAAC,EAC7C,cAAc,EACd,qBAAqB,EACrB,OAAO,CAAC,iBAAiB,GAAG,UAAU,CAAC,CAC1C,CAAC;QACN,CAAC;IACL,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gMACd,aAAA,AAAU,EAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,qBAAqB,CAAC,CAAC;QAC9E,CAAC,MAAM,CAAC;gMACJ,YAAA,AAAS,EAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAA;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;QACpC,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAElC,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,sBAAsB;YACtB,KAAK,CAAC,wBAAwB,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,4BAA4B,EAAE,QAAQ,EAAE;gBAC3H,SAAS,EAAE,uBAAuB;aACrC,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;YAElB,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACzG,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE/C,KAAK,CAAC,wBAAwB,CAC1B,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,4BAA4B,EACpF,QAAQ,EACR;gBACI,cAAc,EAAE;oBAAC;wBAAE,MAAM,EAAE,MAAM;wBAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAAA,CAAE;iBAAC;aAC1E,EACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAC3B,CAAC;QACN,CAAC;QAED,wBAAwB;QACxB,MAAM,mBAAmB,GAAG,IAAI,GAAG,QAAQ,CAAC,sBAAsB,CAAC;QAEnE,IAAI,KAAK,CAAC,sBAAsB,CAAC,mBAAmB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,EAAE,CAAC;YACnG,KAAK,CAAC,iBAAiB,IAAI,CAAC,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,mBAAmB,CAAA,GAAA,EAAM,QAAQ,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;QACzK,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,cAAc,EAAE;oBACZ;wBAAE,MAAM,EAAE,MAAM;wBAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAAA,CAAE;oBACrD;wBAAE,MAAM,EAAE,WAAW;wBAAE,OAAO,EAAE,QAAQ,CAAC,sBAAsB;oBAAA,CAAE;iBACpE;aACJ,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,UAAU,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,QAAQ,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;YAC1J,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACxB,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,mPAAqC,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;YAC1J,CAAC;YACD,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,SAAS,EAAE,uBAAuB;aACrC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,KAA6B,EAAA;QACvD,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,sBAAsB;YACtB,KAAK,CAAC,wBAAwB,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,0BAA0B,EAAE,QAAQ,EAAE;gBACvH,SAAS,EAAE,uBAAuB;gBAClC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;aAC3E,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,KAAK,CAAC,wBAAwB,CAC1B,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,0BAA0B,EAChF,QAAQ,EACR;gBACI,cAAc,EAAE;oBAAC;wBAAE,MAAM,EAAE,MAAM;wBAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAAA,CAAE;iBAAC;aAC1E,EACD,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAC3B,CAAC;QACN,CAAC;IACL,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,MAAM,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC5D,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAE/B,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAElC,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,SAAS;YACT,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,CAAC;QACD,WAAW;QACX,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;QACjD,KAAK,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjD,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;QAEpC,IAAI,oBAAoB,GAAG,QAAQ,CAAC,sBAAsB,CAAC;QAC3D,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,oBAAoB,GAAG,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YACpE,KAAK,CAAC,aAAa,CAAC,sBAAsB,EAAE,GAAG,KAAK,CAAC,gBAAgB,CAAC,oBAAoB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,CAAK,EAAE,QAAQ,CAAC,CAAC;YAC3J,KAAK,CAAC,iBAAiB,IAAI,GAAG,oBAAoB,CAAA,GAAA,EAAM,QAAQ,CAAC,sBAAsB,CAAA,OAAA,CAAS,CAAC;YAEjG,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,SAAS,EAAE,uBAAuB;gBAClC,gBAAgB,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAA,SAAA,EAAY,QAAQ,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,SAAS;aAC9G,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,oBAAoB,GAAG,QAAQ,GAAG,IAAI,GAAG,oBAAoB,GAAG,MAAM,CAAC;QAC3E,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,IAAI,aAAa,GAAG;YAAE,MAAM,EAAE,aAAa;YAAE,OAAO,EAAE,oBAAoB;QAAA,CAAE,CAAC;QAE7E,IAAI,MAAM,EAAE,CAAC;YACT,aAAa,GAAG;gBAAE,MAAM,EAAE,6BAA6B;gBAAE,OAAO,EAAE,oBAAoB;YAAA,CAAE,CAAC;QAC7F,CAAC;QAED,KAAK,CAAC,wBAAwB,CAAC,yBAAyB,EAAE,QAAQ,EAAE;YAChE,cAAc,EAAE;gBAAC,aAAa;aAAC;SAClC,CAAC,CAAC;QAEH,KAAK,CAAC,wBAAwB,CAAC,0BAA0B,EAAE,QAAQ,EAAE;YACjE,cAAc,EAAE;gBAAC,aAAa;aAAC;SAClC,CAAC,CAAC;QAEH,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAElC,OAAO;QACP,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YACtB,IAAI,KAAK,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAChD,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,aAAA,EAAgB,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAA,GAAA,EAAM,oBAAoB,CAAA,IAAA,CAAM,CAAC;YACpN,CAAC;YACD,KAAK,CAAC,iBAAiB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAA,yBAAA,CAA2B,CAAC,CAAC,CAAC,CAAA,oBAAA,CAAsB,CAAC;YACzF,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,QAAQ,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAA,QAAA,CAAU,CAAC;YACtH,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAA,QAAA,CAAU,CAAC;YACzH,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAA,QAAA,CAAU,CAAC;YACzH,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,YAAY,6MAAE,wCAAqC,CAAC,KAAK,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAAK,CAAA,GAAA,EAC7L,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,QAC3E,CAAA,GAAA,CAAK,CAAC;YACN,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,OAAA,EAAU,IAAI,CAAA,eAAA,CAAiB,CAAC;YAClJ,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,cAAc,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,QAAA,EAAW,IAAI,CAAA,eAAA,CAAiB,CAAC;YACpJ,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,SAAS,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAA,OAAA,CAAS,CAAC;QACzK,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,aAAa,GAAG;gBAAC;oBAAE,MAAM,EAAE,aAAa;oBAAE,OAAO,EAAE,oBAAoB,GAAG,MAAM;gBAAA,CAAE;aAAC,CAAC;YAExF,IAAI,MAAM,EAAE,CAAC;gBACT,aAAa,GAAG;oBACZ;wBAAE,MAAM,EAAE,6BAA6B;wBAAE,OAAO,EAAE,oBAAoB,GAAG,MAAM;oBAAA,CAAE;oBACjF;wBAAE,MAAM,EAAE,+BAA+B;wBAAE,OAAO,EAAE,oBAAoB;oBAAA,CAAE;iBAC7E,CAAC;YACN,CAAC;YAED,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,cAAc,EAAE;oBAAC;wBAAE,MAAM,EAAE,MAAM;wBAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBAAA,CAAE,EAAE;uBAAG,aAAa;iBAAC;aAC5F,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,IAAI,gBAAgB,GAAG,CAAA,WAAA,EAAc,oBAAoB,CAAA,IAAA,CAAM,CAAC;YAEhE,IAAI,MAAM,EAAE,CAAC;gBACT,gBAAgB,GAAG,CAAA,0BAAA,EAA6B,oBAAoB,CAAA,IAAA,CAAM,CAAC;YAC/E,CAAC;YACD,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE;gBAC7E,SAAS,EAAE,uBAAuB;gBAClC,gBAAgB,EAAE,gBAAgB;aACrC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;YACtB,KAAK,CAAC,iBAAiB,IAAI,CAAA,oCAAA,CAAsC,CAAC;QACtE,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAE3C,KAAK,CAAC,iBAAiB,IACnB,KAAK,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,CAAA,cAAA,EAAiB,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAA,GAAA,CAAK,CAAC;QACtJ,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;YAC9B,KAAK,CAAC,iBAAiB,IACnB,KAAK,CAAC,cAAc,CAAC,cAAc,CAAC,GAAG,CAAA,eAAA,EAAkB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAA,GAAA,CAAK,CAAC;QAC9J,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3B,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAA,eAAA,CAAiB,CAAC;QACrF,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAE7E,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAChD,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,mBAAmB,CAAC,OAAO,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,wBAAwB,GAAG,mBAAmB,CAAC,wBAAwB,CAAC;QAE7E,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;CACJ;wJAncU,aAAA,EAAA;2KAHN,yBAAA,AAAsB,EAAC,6BAA6B,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAC/F,SAAS,EAAE;YAAE,OAAO,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,YAAY,EAAE,UAAU,CAAC,kCAAkC;QAAA,CAAE;KAC1G,CAAC;4DACsC;6JAqc5C,gBAAA,AAAa,EAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/imageSourceBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/imageSourceBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport { Constants } from \"../../../../Engines/constants\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport { NodeMaterial } from \"../../nodeMaterial\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { EngineStore } from \"../../../../Engines/engineStore\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n/**\r\n * Block used to provide an image for a TextureBlock\r\n */\r\nexport class ImageSourceBlock extends NodeMaterialBlock {\r\n    private _samplerName: string;\r\n    protected _texture: Nullable<Texture>;\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public get texture(): Nullable<Texture> {\r\n        return this._texture;\r\n    }\r\n\r\n    public set texture(texture: Nullable<Texture>) {\r\n        if (this._texture === texture) {\r\n            return;\r\n        }\r\n\r\n        const scene = texture?.getScene() ?? EngineStore.LastCreatedScene;\r\n\r\n        if (!texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this._texture!);\r\n            });\r\n        }\r\n\r\n        this._texture = texture;\r\n\r\n        if (texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(texture);\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the sampler name associated with this image source\r\n     */\r\n    public get samplerName(): string {\r\n        return this._samplerName;\r\n    }\r\n\r\n    /**\r\n     * Creates a new ImageSourceBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this.registerOutput(\r\n            \"source\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"source\", this, NodeMaterialConnectionPointDirection.Output, ImageSourceBlock, \"ImageSourceBlock\")\r\n        );\r\n\r\n        this.registerOutput(\"dimensions\", NodeMaterialBlockConnectionPointTypes.Vector2);\r\n    }\r\n\r\n    public override bind(effect: Effect) {\r\n        if (!this.texture) {\r\n            return;\r\n        }\r\n\r\n        effect.setTexture(this._samplerName, this.texture);\r\n    }\r\n\r\n    public override isReady() {\r\n        if (this.texture && !this.texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ImageSourceBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the output component\r\n     */\r\n    public get source(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the dimension component\r\n     */\r\n    public get dimensions(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex) {\r\n            this._samplerName = state._getFreeVariableName(this.name + \"Texture\");\r\n\r\n            // Declarations\r\n            state.sharedData.blockingBlocks.push(this);\r\n            state.sharedData.textureBlocks.push(this);\r\n            state.sharedData.bindableBlocks.push(this);\r\n        }\r\n\r\n        if (this.dimensions.isConnected) {\r\n            let affect: string = \"\";\r\n            if (state.shaderLanguage === ShaderLanguage.WGSL) {\r\n                affect = `vec2f(textureDimensions(${this._samplerName}, 0).xy)`;\r\n            } else {\r\n                affect = `vec2(textureSize(${this._samplerName}, 0).xy)`;\r\n            }\r\n\r\n            state.compilationString += `${state._declareOutput(this.dimensions)} = ${affect};\\n`;\r\n        }\r\n\r\n        state._emit2DSampler(this._samplerName);\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        if (!this.texture) {\r\n            return codeString;\r\n        }\r\n\r\n        codeString += `${this._codeVariableName}.texture = new BABYLON.Texture(\"${this.texture.name}\", null, ${this.texture.noMipmap}, ${this.texture.invertY}, ${this.texture.samplingMode});\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wrapU = ${this.texture.wrapU};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wrapV = ${this.texture.wrapV};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uAng = ${this.texture.uAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vAng = ${this.texture.vAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wAng = ${this.texture.wAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uOffset = ${this.texture.uOffset};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vOffset = ${this.texture.vOffset};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uScale = ${this.texture.uScale};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vScale = ${this.texture.vScale};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.coordinatesMode = ${this.texture.coordinatesMode};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        if (this.texture && !this.texture.isRenderTarget && this.texture.getClassName() !== \"VideoTexture\") {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string, urlRewriter?: (url: string) => string) {\r\n        super._deserialize(serializationObject, scene, rootUrl, urlRewriter);\r\n\r\n        if (serializationObject.texture && !NodeMaterial.IgnoreTexturesAtLoadTime && serializationObject.texture.url !== undefined) {\r\n            if (serializationObject.texture.url.indexOf(\"data:\") === 0) {\r\n                rootUrl = \"\";\r\n            } else if (urlRewriter) {\r\n                serializationObject.texture.url = urlRewriter(serializationObject.texture.url);\r\n                serializationObject.texture.name = serializationObject.texture.url;\r\n            }\r\n            this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl) as Texture;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ImageSourceBlock\", ImageSourceBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAI1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AAGpD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAElD,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;;;;;;;;;AAKxD,MAAO,gBAAiB,uLAAQ,oBAAiB;IAGnD;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,OAA0B,EAAA;QACzC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,EAAE,QAAQ,EAAE,kKAAI,cAAW,CAAC,gBAAgB,CAAC;QAElE,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC;YACpB,KAAK,CAAC,uBAAuB,CAAC,GAAA,CAAA,KAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,QAAS,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;YACnB,KAAK,CAAC,uBAAuB,CAAC,GAAA,CAAA,KAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QAExD,IAAI,CAAC,cAAc,CACf,QAAQ,6MACR,wCAAqC,CAAC,MAAM,gMAC5C,2BAAwB,CAAC,iBAAiB,EAC1C,wMAAI,0CAAuC,CAAC,QAAQ,EAAE,IAAI,EAAA,EAAA,+CAAA,KAA+C,gBAAgB,EAAE,kBAAkB,CAAC,CACjJ,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,YAAY,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;IACrF,CAAC;IAEe,IAAI,CAAC,MAAc,EAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACvD,CAAC;IAEe,OAAO,GAAA;QACnB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,kBAAkB,CAAC;IAC9B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;YACnD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;YAEtE,eAAe;YACf,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;YAC9B,IAAI,MAAM,GAAW,EAAE,CAAC;YACxB,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;gBAC/C,MAAM,GAAG,CAAA,wBAAA,EAA2B,IAAI,CAAC,YAAY,CAAA,QAAA,CAAU,CAAC;YACpE,CAAC,MAAM,CAAC;gBACJ,MAAM,GAAG,CAAA,iBAAA,EAAoB,IAAI,CAAC,YAAY,CAAA,QAAA,CAAU,CAAC;YAC7D,CAAC;YAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,GAAA,EAAM,MAAM,CAAA,GAAA,CAAK,CAAC;QACzF,CAAC;QAED,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO,UAAU,CAAC;QACtB,CAAC;QAED,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gCAAA,EAAmC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,SAAA,EAAY,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA,EAAA,EAAK,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA,EAAA,EAAK,IAAI,CAAC,OAAO,CAAC,YAAY,CAAA,IAAA,CAAM,CAAC;QAC1L,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,iBAAA,EAAoB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA,GAAA,CAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,iBAAA,EAAoB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA,GAAA,CAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gBAAA,EAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,GAAA,CAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gBAAA,EAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,GAAA,CAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gBAAA,EAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,GAAA,CAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,mBAAA,EAAsB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA,GAAA,CAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,mBAAA,EAAsB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA,GAAA,CAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,kBAAA,EAAqB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA,GAAA,CAAK,CAAC;QACrF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,kBAAA,EAAqB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA,GAAA,CAAK,CAAC;QACrF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,2BAAA,EAA8B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAA,GAAA,CAAK,CAAC;QAEvG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,cAAc,EAAE,CAAC;YACjG,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC3D,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAE,WAAqC,EAAA;QACvH,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAErE,IAAI,mBAAmB,CAAC,OAAO,IAAI,0KAAC,eAAY,CAAC,wBAAwB,IAAI,mBAAmB,CAAC,OAAO,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;YACzH,IAAI,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzD,OAAO,GAAG,EAAE,CAAC;YACjB,CAAC,MAAM,IAAI,WAAW,EAAE,CAAC;gBACrB,mBAAmB,CAAC,OAAO,CAAC,GAAG,GAAG,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC/E,mBAAmB,CAAC,OAAO,CAAC,IAAI,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC;YACvE,CAAC;YACD,IAAI,CAAC,OAAO,2KAAG,UAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAY,CAAC;QACzF,CAAC;IACL,CAAC;CACJ;6JAED,gBAAA,AAAa,EAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1082, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/textureBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/textureBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { NodeMaterialConnectionPointDirection } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { NodeMaterial } from \"../../nodeMaterial\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { NodeMaterialModes } from \"../../Enums/nodeMaterialModes\";\r\nimport { Constants } from \"../../../../Engines/constants\";\r\nimport \"../../../../Shaders/ShadersInclude/helperFunctions\";\r\nimport { ImageSourceBlock } from \"./imageSourceBlock\";\r\nimport { NodeMaterialConnectionPointCustomObject } from \"../../nodeMaterialConnectionPointCustomObject\";\r\nimport { EngineStore } from \"../../../../Engines/engineStore\";\r\nimport type { PrePassTextureBlock } from \"../Input/prePassTextureBlock\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Block used to read a texture from a sampler\r\n */\r\nexport class TextureBlock extends NodeMaterialBlock {\r\n    private _defineName: string;\r\n    private _linearDefineName: string;\r\n    private _gammaDefineName: string;\r\n    private _tempTextureRead: string;\r\n    private _samplerName: string;\r\n    private _transformedUVName: string;\r\n    private _textureTransformName: string;\r\n    private _textureInfoName: string;\r\n    private _mainUVName: string;\r\n    private _mainUVDefineName: string;\r\n    private _fragmentOnly: boolean;\r\n    private _imageSource: Nullable<ImageSourceBlock | PrePassTextureBlock>;\r\n\r\n    protected _texture: Nullable<Texture>;\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public get texture(): Nullable<Texture> {\r\n        if (this.source.isConnected) {\r\n            return (this.source.connectedPoint?.ownerBlock as ImageSourceBlock).texture;\r\n        }\r\n        return this._texture;\r\n    }\r\n\r\n    public set texture(texture: Nullable<Texture>) {\r\n        if (this._texture === texture) {\r\n            return;\r\n        }\r\n\r\n        const scene = texture?.getScene() ?? EngineStore.LastCreatedScene;\r\n\r\n        if (!texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this._texture!);\r\n            });\r\n        }\r\n\r\n        this._texture = texture;\r\n\r\n        if (texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(texture);\r\n            });\r\n        }\r\n    }\r\n\r\n    private static _IsPrePassTextureBlock(block: Nullable<ImageSourceBlock | PrePassTextureBlock>): block is PrePassTextureBlock {\r\n        return block?.getClassName() === \"PrePassTextureBlock\";\r\n    }\r\n\r\n    private get _isSourcePrePass() {\r\n        return TextureBlock._IsPrePassTextureBlock(this._imageSource);\r\n    }\r\n\r\n    /**\r\n     * Gets the sampler name associated with this texture\r\n     */\r\n    public get samplerName(): string {\r\n        if (this._imageSource) {\r\n            if (!TextureBlock._IsPrePassTextureBlock(this._imageSource)) {\r\n                return this._imageSource.samplerName;\r\n            }\r\n            if (this.source.connectedPoint) {\r\n                return this._imageSource.getSamplerName(this.source.connectedPoint);\r\n            }\r\n        }\r\n        return this._samplerName;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that this block is linked to an ImageSourceBlock\r\n     */\r\n    public get hasImageSource(): boolean {\r\n        return this.source.isConnected;\r\n    }\r\n\r\n    private _convertToGammaSpace = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to gamma space\r\n     */\r\n    public set convertToGammaSpace(value: boolean) {\r\n        if (value === this._convertToGammaSpace) {\r\n            return;\r\n        }\r\n\r\n        this._convertToGammaSpace = value;\r\n        if (this.texture) {\r\n            const scene = this.texture.getScene() ?? EngineStore.LastCreatedScene;\r\n            scene?.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this.texture!);\r\n            });\r\n        }\r\n    }\r\n    public get convertToGammaSpace(): boolean {\r\n        return this._convertToGammaSpace;\r\n    }\r\n\r\n    private _convertToLinearSpace = false;\r\n    /**\r\n     * Gets or sets a boolean indicating if content needs to be converted to linear space\r\n     */\r\n    public set convertToLinearSpace(value: boolean) {\r\n        if (value === this._convertToLinearSpace) {\r\n            return;\r\n        }\r\n\r\n        this._convertToLinearSpace = value;\r\n        if (this.texture) {\r\n            const scene = this.texture.getScene() ?? EngineStore.LastCreatedScene;\r\n            scene?.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this.texture!);\r\n            });\r\n        }\r\n    }\r\n    public get convertToLinearSpace(): boolean {\r\n        return this._convertToLinearSpace;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if multiplication of texture with level should be disabled\r\n     */\r\n    public disableLevelMultiplication = false;\r\n\r\n    /**\r\n     * Create a new TextureBlock\r\n     * @param name defines the block name\r\n     * @param fragmentOnly\r\n     */\r\n    public constructor(name: string, fragmentOnly = false) {\r\n        super(name, fragmentOnly ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this._fragmentOnly = fragmentOnly;\r\n\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.VertexAndFragment);\r\n        this.registerInput(\r\n            \"source\",\r\n            NodeMaterialBlockConnectionPointTypes.Object,\r\n            true,\r\n            NodeMaterialBlockTargets.VertexAndFragment,\r\n            new NodeMaterialConnectionPointCustomObject(\"source\", this, NodeMaterialConnectionPointDirection.Input, ImageSourceBlock, \"ImageSourceBlock\")\r\n        );\r\n        this.registerInput(\"layer\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n        this.registerInput(\"lod\", NodeMaterialBlockConnectionPointTypes.Float, true);\r\n\r\n        this.registerOutput(\"rgba\", NodeMaterialBlockConnectionPointTypes.Color4, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"rgb\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"r\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"g\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"b\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n        this.registerOutput(\"a\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this.registerOutput(\"level\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Vector2 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n\r\n        this._inputs[0]._prioritizeVertex = !fragmentOnly;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"TextureBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the source input component\r\n     */\r\n    public get source(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the layer input component\r\n     */\r\n    public get layer(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the LOD input component\r\n     */\r\n    public get lod(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgba output component\r\n     */\r\n    public get rgba(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgb output component\r\n     */\r\n    public get rgb(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the r output component\r\n     */\r\n    public get r(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the g output component\r\n     */\r\n    public get g(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the b output component\r\n     */\r\n    public get b(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the a output component\r\n     */\r\n    public get a(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the level output component\r\n     */\r\n    public get level(): NodeMaterialConnectionPoint {\r\n        return this._outputs[6];\r\n    }\r\n\r\n    private _isTiedToFragment(input: NodeMaterialConnectionPoint) {\r\n        if (input.target === NodeMaterialBlockTargets.Fragment) {\r\n            return true;\r\n        }\r\n\r\n        if (input.target === NodeMaterialBlockTargets.Vertex) {\r\n            return false;\r\n        }\r\n\r\n        if (input.target === NodeMaterialBlockTargets.Neutral || input.target === NodeMaterialBlockTargets.VertexAndFragment) {\r\n            const parentBlock = input.ownerBlock;\r\n\r\n            if (parentBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n                return true;\r\n            }\r\n\r\n            for (const input of parentBlock.inputs) {\r\n                if (!input.isConnected) {\r\n                    continue;\r\n                }\r\n                if (this._isTiedToFragment(input.connectedPoint!)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    private _getEffectiveTarget() {\r\n        if (this._fragmentOnly) {\r\n            return NodeMaterialBlockTargets.Fragment;\r\n        }\r\n\r\n        // TextureBlock has a special optimizations for uvs that come from the vertex shaders as they can be packed into a single varyings.\r\n        // But we need to detect uvs coming from fragment then\r\n        if (!this.uv.isConnected) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        if (this.uv.sourceBlock!.isInput) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        if (this._isTiedToFragment(this.uv.connectedPoint!)) {\r\n            return NodeMaterialBlockTargets.Fragment;\r\n        }\r\n\r\n        return NodeMaterialBlockTargets.VertexAndFragment;\r\n    }\r\n\r\n    public override get target() {\r\n        return this._getEffectiveTarget();\r\n    }\r\n\r\n    public override set target(value: NodeMaterialBlockTargets) {}\r\n\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.uv.isConnected) {\r\n            if (material.mode === NodeMaterialModes.PostProcess) {\r\n                const uvInput = material.getBlockByPredicate((b) => b.name === \"uv\" && additionalFilteringInfo(b));\r\n\r\n                if (uvInput) {\r\n                    uvInput.connectTo(this);\r\n                }\r\n            } else if (material.mode !== NodeMaterialModes.ProceduralTexture) {\r\n                const attributeName = material.mode === NodeMaterialModes.Particle ? \"particle_uv\" : \"uv\";\r\n\r\n                let uvInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === attributeName && additionalFilteringInfo(b));\r\n\r\n                if (!uvInput) {\r\n                    uvInput = new InputBlock(\"uv\");\r\n                    uvInput.setAsAttribute(attributeName);\r\n                }\r\n                uvInput.output.connectTo(this.uv);\r\n            }\r\n        }\r\n    }\r\n\r\n    public override initializeDefines(defines: NodeMaterialDefines) {\r\n        if (!defines._areTexturesDirty) {\r\n            return;\r\n        }\r\n\r\n        if (this._mainUVDefineName !== undefined) {\r\n            defines.setValue(this._mainUVDefineName, false, true);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        if (!defines._areTexturesDirty) {\r\n            return;\r\n        }\r\n\r\n        if (!this.texture || !this.texture.getTextureMatrix) {\r\n            if (this._isMixed) {\r\n                defines.setValue(this._defineName, false, true);\r\n                defines.setValue(this._mainUVDefineName, true, true);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const toGamma = this.convertToGammaSpace && this.texture && !this.texture.gammaSpace;\r\n        const toLinear = this.convertToLinearSpace && this.texture && this.texture.gammaSpace;\r\n\r\n        // Not a bug... Name defines the texture space not the required conversion\r\n        defines.setValue(this._linearDefineName, toGamma, true);\r\n        defines.setValue(this._gammaDefineName, toLinear, true);\r\n\r\n        if (this._isMixed) {\r\n            if (!this.texture.getTextureMatrix().isIdentityAs3x2()) {\r\n                defines.setValue(this._defineName, true);\r\n                if (defines[this._mainUVDefineName] == undefined) {\r\n                    defines.setValue(this._mainUVDefineName, false, true);\r\n                }\r\n            } else {\r\n                defines.setValue(this._defineName, false, true);\r\n                defines.setValue(this._mainUVDefineName, true, true);\r\n            }\r\n        }\r\n    }\r\n\r\n    public override isReady() {\r\n        if (this._isSourcePrePass) {\r\n            return true;\r\n        }\r\n\r\n        if (this.texture && !this.texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override bind(effect: Effect) {\r\n        if (this._isSourcePrePass) {\r\n            effect.setFloat(this._textureInfoName, 1);\r\n        }\r\n\r\n        if (!this.texture) {\r\n            return;\r\n        }\r\n\r\n        if (this._isMixed) {\r\n            effect.setFloat(this._textureInfoName, this.texture.level);\r\n            effect.setMatrix(this._textureTransformName, this.texture.getTextureMatrix());\r\n        }\r\n\r\n        if (!this._imageSource) {\r\n            effect.setTexture(this._samplerName, this.texture);\r\n        }\r\n    }\r\n\r\n    private get _isMixed() {\r\n        return this.target !== NodeMaterialBlockTargets.Fragment;\r\n    }\r\n\r\n    private _injectVertexCode(state: NodeMaterialBuildState) {\r\n        const uvInput = this.uv;\r\n\r\n        // Inject code in vertex\r\n        this._defineName = state._getFreeDefineName(\"UVTRANSFORM\");\r\n        this._mainUVDefineName = \"VMAIN\" + uvInput.declarationVariableName.toUpperCase();\r\n\r\n        this._mainUVName = \"vMain\" + uvInput.declarationVariableName;\r\n        this._transformedUVName = state._getFreeVariableName(\"transformedUV\");\r\n        this._textureTransformName = state._getFreeVariableName(\"textureTransform\");\r\n        this._textureInfoName = state._getFreeVariableName(\"textureInfoName\");\r\n\r\n        this.level.associatedVariableName = this._textureInfoName;\r\n\r\n        state._emitVaryingFromString(this._transformedUVName, NodeMaterialBlockConnectionPointTypes.Vector2, this._defineName);\r\n        state._emitVaryingFromString(this._mainUVName, NodeMaterialBlockConnectionPointTypes.Vector2, this._mainUVDefineName);\r\n\r\n        state._emitUniformFromString(this._textureTransformName, NodeMaterialBlockConnectionPointTypes.Matrix, this._defineName);\r\n\r\n        const vec4 = state._getShaderType(NodeMaterialBlockConnectionPointTypes.Vector4);\r\n        const vec2 = state._getShaderType(NodeMaterialBlockConnectionPointTypes.Vector2);\r\n\r\n        state.compilationString += `#ifdef ${this._defineName}\\n`;\r\n        state.compilationString += `${state._getVaryingName(this._transformedUVName)} = ${vec2}(${this._textureTransformName} * ${vec4}(${uvInput.associatedVariableName}.xy, 1.0, 0.0));\\n`;\r\n        state.compilationString += `#elif defined(${this._mainUVDefineName})\\n`;\r\n\r\n        let automaticPrefix = \"\";\r\n        if (state.shaderLanguage === ShaderLanguage.WGSL) {\r\n            if (uvInput.isConnectedToInputBlock && uvInput.associatedVariableName.indexOf(\"vertexInputs.\") === -1) {\r\n                automaticPrefix = \"vertexInputs.\"; // Force the prefix\r\n            }\r\n        }\r\n\r\n        state.compilationString += `${state._getVaryingName(this._mainUVName)} = ${automaticPrefix}${uvInput.associatedVariableName}.xy;\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n\r\n        if (!this._outputs.some((o) => o.isConnectedInVertexShader)) {\r\n            return;\r\n        }\r\n\r\n        this._writeTextureRead(state, true);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints && output.name !== \"level\") {\r\n                this._writeOutput(state, output, output.name, true);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _getUVW(uvName: string): string {\r\n        let coords = uvName;\r\n\r\n        const is2DArrayTexture = this._texture?._texture?.is2DArray ?? false;\r\n        const is3D = this._texture?._texture?.is3D ?? false;\r\n\r\n        if (is2DArrayTexture) {\r\n            const layerValue = this.layer.isConnected ? this.layer.associatedVariableName : \"0\";\r\n            coords = `vec3(${uvName}, ${layerValue})`;\r\n        } else if (is3D) {\r\n            const layerValue = this.layer.isConnected ? this.layer.associatedVariableName : \"0\";\r\n            coords = `vec3(${uvName}, ${layerValue})`;\r\n        }\r\n\r\n        return coords;\r\n    }\r\n\r\n    private _samplerFunc(state: NodeMaterialBuildState) {\r\n        if (state.shaderLanguage === ShaderLanguage.WGSL) {\r\n            return state.target === NodeMaterialBlockTargets.Vertex ? \"textureSampleLevel\" : \"textureSample\";\r\n        }\r\n        return this.lod.isConnected ? \"texture2DLodEXT\" : \"texture2D\";\r\n    }\r\n\r\n    private get _samplerLodSuffix() {\r\n        return this.lod.isConnected ? `, ${this.lod.associatedVariableName}` : \"\";\r\n    }\r\n\r\n    private _generateTextureSample(uv: string, state: NodeMaterialBuildState) {\r\n        if (state.shaderLanguage === ShaderLanguage.WGSL) {\r\n            const isVertex = state.target === NodeMaterialBlockTargets.Vertex;\r\n            return `${this._samplerFunc(state)}(${this.samplerName},${this.samplerName + Constants.AUTOSAMPLERSUFFIX}, ${this._getUVW(uv)}${this._samplerLodSuffix}${isVertex ? \", 0\" : \"\"})`;\r\n        }\r\n        return `${this._samplerFunc(state)}(${this.samplerName}, ${this._getUVW(uv)}${this._samplerLodSuffix})`;\r\n    }\r\n\r\n    private _generateTextureLookup(state: NodeMaterialBuildState): void {\r\n        state.compilationString += `#ifdef ${this._defineName}\\n`;\r\n        state.compilationString += `${state._declareLocalVar(this._tempTextureRead, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${this._generateTextureSample(state._getVaryingName(this._transformedUVName), state)};\\n`;\r\n        state.compilationString += `#elif defined(${this._mainUVDefineName})\\n`;\r\n        state.compilationString += `${state._declareLocalVar(this._tempTextureRead, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${this._generateTextureSample(this._mainUVName ? state._getVaryingName(this._mainUVName) : this.uv.associatedVariableName, state)}${this._samplerLodSuffix};\\n`;\r\n        state.compilationString += `#endif\\n`;\r\n    }\r\n\r\n    private _writeTextureRead(state: NodeMaterialBuildState, vertexMode = false) {\r\n        const uvInput = this.uv;\r\n\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n\r\n            this._generateTextureLookup(state);\r\n            return;\r\n        }\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `${state._declareLocalVar(this._tempTextureRead, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${this._generateTextureSample(uvInput.associatedVariableName, state)}${this._samplerLodSuffix};\\n`;\r\n            return;\r\n        }\r\n\r\n        this._generateTextureLookup(state);\r\n    }\r\n\r\n    private _generateConversionCode(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string): void {\r\n        if (swizzle !== \"a\") {\r\n            // no conversion if the output is \"a\" (alpha)\r\n            if (!this.texture || !this.texture.gammaSpace) {\r\n                state.compilationString += `#ifdef ${this._linearDefineName}\r\n                    ${output.associatedVariableName} = toGammaSpace(${output.associatedVariableName});\r\n                    #endif\r\n                `;\r\n            }\r\n\r\n            state.compilationString += `#ifdef ${this._gammaDefineName}\r\n                ${output.associatedVariableName} = ${state._toLinearSpace(output)};\r\n                #endif\r\n            `;\r\n        }\r\n    }\r\n\r\n    private _writeOutput(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string, vertexMode = false) {\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n\r\n            state.compilationString += `${state._declareOutput(output)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n            this._generateConversionCode(state, output, swizzle);\r\n            return;\r\n        }\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `${state._declareOutput(output)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n            this._generateConversionCode(state, output, swizzle);\r\n            return;\r\n        }\r\n        let complement = \"\";\r\n\r\n        if (!this.disableLevelMultiplication) {\r\n            complement = ` * ${(state.shaderLanguage === ShaderLanguage.WGSL ? \"uniforms.\" : \"\") + this._textureInfoName}`;\r\n        }\r\n\r\n        state.compilationString += `${state._declareOutput(output)} = ${this._tempTextureRead}.${swizzle}${complement};\\n`;\r\n        this._generateConversionCode(state, output, swizzle);\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (this.source.isConnected) {\r\n            this._imageSource = this.source.connectedPoint!.ownerBlock as ImageSourceBlock;\r\n        } else {\r\n            this._imageSource = null;\r\n        }\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Vertex || this._fragmentOnly || state.target === NodeMaterialBlockTargets.Fragment) {\r\n            this._tempTextureRead = state._getFreeVariableName(\"tempTextureRead\");\r\n            this._linearDefineName = state._getFreeDefineName(\"ISLINEAR\");\r\n            this._gammaDefineName = state._getFreeDefineName(\"ISGAMMA\");\r\n        }\r\n\r\n        if ((!this._isMixed && state.target === NodeMaterialBlockTargets.Fragment) || (this._isMixed && state.target === NodeMaterialBlockTargets.Vertex)) {\r\n            if (!this._imageSource) {\r\n                const varName = state._getFreeVariableName(this.name);\r\n                this._samplerName = varName + \"Texture\";\r\n\r\n                if (this._texture?._texture?.is2DArray) {\r\n                    state._emit2DArraySampler(this._samplerName);\r\n                } else {\r\n                    state._emit2DSampler(this._samplerName);\r\n                }\r\n            }\r\n\r\n            // Declarations\r\n            state.sharedData.blockingBlocks.push(this);\r\n            state.sharedData.textureBlocks.push(this);\r\n            state.sharedData.blocksWithDefines.push(this);\r\n            state.sharedData.bindableBlocks.push(this);\r\n        }\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            // Vertex\r\n            this._injectVertexCode(state);\r\n            return;\r\n        }\r\n\r\n        // Fragment\r\n        if (!this._outputs.some((o) => o.isConnectedInFragmentShader)) {\r\n            return;\r\n        }\r\n\r\n        if (this._isMixed && !this._imageSource) {\r\n            // Reexport the sampler\r\n            if (this._texture?._texture?.is2DArray) {\r\n                state._emit2DArraySampler(this._samplerName);\r\n            } else {\r\n                state._emit2DSampler(this._samplerName);\r\n            }\r\n        }\r\n\r\n        const comments = `//${this.name}`;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n\r\n        if (this._isMixed) {\r\n            state._emitUniformFromString(this._textureInfoName, NodeMaterialBlockConnectionPointTypes.Float);\r\n        }\r\n\r\n        this._writeTextureRead(state);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints && output.name !== \"level\") {\r\n                this._writeOutput(state, output, output.name);\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        codeString += `${this._codeVariableName}.convertToGammaSpace = ${this.convertToGammaSpace};\\n`;\r\n        codeString += `${this._codeVariableName}.convertToLinearSpace = ${this.convertToLinearSpace};\\n`;\r\n        codeString += `${this._codeVariableName}.disableLevelMultiplication = ${this.disableLevelMultiplication};\\n`;\r\n\r\n        if (!this.texture) {\r\n            return codeString;\r\n        }\r\n\r\n        codeString += `${this._codeVariableName}.texture = new BABYLON.Texture(\"${this.texture.name}\", null, ${this.texture.noMipmap}, ${this.texture.invertY}, ${this.texture.samplingMode});\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wrapU = ${this.texture.wrapU};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wrapV = ${this.texture.wrapV};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uAng = ${this.texture.uAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vAng = ${this.texture.vAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.wAng = ${this.texture.wAng};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uOffset = ${this.texture.uOffset};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vOffset = ${this.texture.vOffset};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.uScale = ${this.texture.uScale};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.vScale = ${this.texture.vScale};\\n`;\r\n        codeString += `${this._codeVariableName}.texture.coordinatesMode = ${this.texture.coordinatesMode};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.convertToGammaSpace = this.convertToGammaSpace;\r\n        serializationObject.convertToLinearSpace = this.convertToLinearSpace;\r\n        serializationObject.fragmentOnly = this._fragmentOnly;\r\n        serializationObject.disableLevelMultiplication = this.disableLevelMultiplication;\r\n        if (!this.hasImageSource && this.texture && !this.texture.isRenderTarget && this.texture.getClassName() !== \"VideoTexture\") {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string, urlRewriter?: (url: string) => string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.convertToGammaSpace = serializationObject.convertToGammaSpace;\r\n        this.convertToLinearSpace = !!serializationObject.convertToLinearSpace;\r\n        this._fragmentOnly = !!serializationObject.fragmentOnly;\r\n        this.disableLevelMultiplication = !!serializationObject.disableLevelMultiplication;\r\n\r\n        if (serializationObject.texture && !NodeMaterial.IgnoreTexturesAtLoadTime && serializationObject.texture.url !== undefined) {\r\n            if (serializationObject.texture.url.indexOf(\"data:\") === 0) {\r\n                rootUrl = \"\";\r\n            } else if (urlRewriter) {\r\n                serializationObject.texture.url = urlRewriter(serializationObject.texture.url);\r\n                serializationObject.texture.name = serializationObject.texture.url;\r\n            }\r\n            this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl) as Texture;\r\n        }\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.TextureBlock\", TextureBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAIhF,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAGjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AAEpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAElE,OAAO,oDAAoD,CAAC;AAC5D,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,uCAAuC,EAAE,MAAM,+CAA+C,CAAC;AACxG,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;;;;;;;;;;;;;AAOxD,MAAO,YAAa,uLAAQ,oBAAiB;IAe/C;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,UAA+B,CAAA,CAAC,OAAO,CAAC;QAChF,CAAC;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,OAA0B,EAAA;QACzC,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,EAAE,QAAQ,EAAE,kKAAI,cAAW,CAAC,gBAAgB,CAAC;QAElE,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC;YACpB,KAAK,CAAC,uBAAuB,CAAC,GAAA,CAAA,KAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,QAAS,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;YACnB,KAAK,CAAC,uBAAuB,CAAC,GAAA,CAAA,KAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,sBAAsB,CAAC,KAAuD,EAAA;QACzF,OAAO,KAAK,EAAE,YAAY,EAAE,KAAK,qBAAqB,CAAC;IAC3D,CAAC;IAED,IAAY,gBAAgB,GAAA;QACxB,OAAO,YAAY,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC1D,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;YACzC,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YACxE,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;IACnC,CAAC;IAGD;;OAEG,CACH,IAAW,mBAAmB,CAAC,KAAc,EAAA;QACzC,IAAI,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACtC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,iKAAI,eAAW,CAAC,gBAAgB,CAAC;YACtE,KAAK,EAAE,uBAAuB,CAAC,GAAA,CAAA,KAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACxE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IACD,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAGD;;OAEG,CACH,IAAW,oBAAoB,CAAC,KAAc,EAAA;QAC1C,IAAI,KAAK,KAAK,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACvC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,kKAAI,cAAW,CAAC,gBAAgB,CAAC;YACtE,KAAK,EAAE,uBAAuB,CAAC,GAAA,CAAA,KAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACxE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IACD,IAAW,oBAAoB,GAAA;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC,CAAC;IAOD;;;;OAIG,CACH,YAAmB,IAAY,EAAE,YAAY,GAAG,KAAK,CAAA;QACjD,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,+LAAC,2BAAwB,CAAC,QAAQ,CAAC,CAAC,+LAAC,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QArDvG,IAAA,CAAA,oBAAoB,GAAG,KAAK,CAAC;QAqB7B,IAAA,CAAA,qBAAqB,GAAG,KAAK,CAAC;QAqBtC;;WAEG,CACI,IAAA,CAAA,0BAA0B,GAAG,KAAK,CAAC;QAUtC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,mPAAqC,CAAC,UAAU,EAAE,KAAK,gMAAE,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CACd,QAAQ,6MACR,wCAAqC,CAAC,MAAM,EAC5C,IAAI,gMACJ,2BAAwB,CAAC,iBAAiB,EAC1C,uMAAI,2CAAuC,CAAC,QAAQ,EAAE,IAAI,EAAA,EAAA,8CAAA,oMAA8C,mBAAgB,EAAE,kBAAkB,CAAC,CAChJ,CAAC;QACF,IAAI,CAAC,aAAa,CAAC,OAAO,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,KAAK,6MAAE,wCAAqC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAE7E,IAAI,CAAC,cAAc,CAAC,MAAM,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,KAAK,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QAC3G,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,EAAE,yNAAwB,CAAC,OAAO,CAAC,CAAC;QACxG,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QAExG,IAAI,CAAC,cAAc,CAAC,OAAO,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QAE5G,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,4MACtD,wCAAqC,CAAC,OAAO,6MAAG,yCAAqC,CAAC,OAAO,GAAG,mPAAqC,CAAC,OAAO,CAChJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,CAAC,YAAY,CAAC;IACtD,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,MAAM,GAAA;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEO,iBAAiB,CAAC,KAAkC,EAAA;QACxD,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,iBAAiB,EAAE,CAAC;YACnH,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC;YAErC,IAAI,WAAW,CAAC,MAAM,KAAK,yNAAwB,CAAC,QAAQ,EAAE,CAAC;gBAC3D,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,KAAK,MAAM,KAAK,IAAI,WAAW,CAAC,MAAM,CAAE,CAAC;gBACrC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;oBACrB,SAAS;gBACb,CAAC;gBACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,cAAe,CAAC,EAAE,CAAC;oBAChD,OAAO,IAAI,CAAC;gBAChB,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,mBAAmB,GAAA;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO,yNAAwB,CAAC,QAAQ,CAAC;QAC7C,CAAC;QAED,mIAAmI;QACnI,sDAAsD;QACtD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC;YACvB,qMAAO,2BAAwB,CAAC,iBAAiB,CAAC;QACtD,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,WAAY,CAAC,OAAO,EAAE,CAAC;YAC/B,qMAAO,2BAAwB,CAAC,iBAAiB,CAAC;QACtD,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,cAAe,CAAC,EAAE,CAAC;YAClD,oMAAO,4BAAwB,CAAC,QAAQ,CAAC;QAC7C,CAAC;QAED,qMAAO,2BAAwB,CAAC,iBAAiB,CAAC;IACtD,CAAC;IAED,IAAoB,MAAM,GAAA;QACtB,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACtC,CAAC;IAED,IAAoB,MAAM,CAAC,KAA+B,EAAA,CAAG,CAAC;IAE9C,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,CAAG,CAAD,GAAK,EAAA;QAC5H,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC;YACvB,IAAI,QAAQ,CAAC,IAAI,4LAAK,oBAAiB,CAAC,WAAW,EAAE,CAAC;gBAClD,MAAM,OAAO,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,KAAK,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEnG,IAAI,OAAO,EAAE,CAAC;oBACV,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC5B,CAAC;YACL,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,2MAAiB,CAAC,iBAAiB,EAAE,CAAC;gBAC/D,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,4LAAK,oBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC;gBAE1F,IAAI,OAAO,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEhI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACX,OAAO,GAAG,8LAAI,aAAU,CAAC,IAAI,CAAC,CAAC;oBAC/B,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;gBAC1C,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;IACL,CAAC;IAEe,iBAAiB,CAAC,OAA4B,EAAA;QAC1D,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAClD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAChB,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAChD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACzD,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QACrF,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;QAEtF,0EAA0E;QAC1E,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACxD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAExD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,eAAe,EAAE,EAAE,CAAC;gBACrD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBACzC,IAAI,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,SAAS,EAAE,CAAC;oBAC/C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAC1D,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAChD,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACzD,CAAC;QACL,CAAC;IACL,CAAC;IAEe,OAAO,GAAA;QACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,IAAI,CAAC,MAAc,EAAA;QAC/B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED,IAAY,QAAQ,GAAA;QAChB,OAAO,IAAI,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,CAAC;IAC7D,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAA;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,wBAAwB;QACxB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAC3D,IAAI,CAAC,iBAAiB,GAAG,OAAO,GAAG,OAAO,CAAC,uBAAuB,CAAC,WAAW,EAAE,CAAC;QAEjF,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC,uBAAuB,CAAC;QAC7D,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QACtE,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAC5E,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAEtE,IAAI,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE1D,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,4MAAE,yCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACvH,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,6MAAE,wCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEtH,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEzH,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,4MAAC,wCAAqC,CAAC,OAAO,CAAC,CAAC;QACjF,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,4MAAC,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAEjF,KAAK,CAAC,iBAAiB,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,WAAW,CAAA,EAAA,CAAI,CAAC;QAC1D,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA,GAAA,EAAM,IAAI,CAAA,CAAA,EAAI,IAAI,CAAC,qBAAqB,CAAA,GAAA,EAAM,IAAI,CAAA,CAAA,EAAI,OAAO,CAAC,sBAAsB,CAAA,kBAAA,CAAoB,CAAC;QACrL,KAAK,CAAC,iBAAiB,IAAI,CAAA,cAAA,EAAiB,IAAI,CAAC,iBAAiB,CAAA,GAAA,CAAK,CAAC;QAExE,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,IAAI,OAAO,CAAC,uBAAuB,IAAI,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACpG,eAAe,GAAG,eAAe,CAAC,CAAC,mBAAmB;YAC1D,CAAC;QACL,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,EAAM,eAAe,GAAG,OAAO,CAAC,sBAAsB,CAAA,MAAA,CAAQ,CAAC;QACpI,KAAK,CAAC,iBAAiB,IAAI,CAAA,QAAA,CAAU,CAAC;QAEtC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,yBAAyB,CAAC,EAAE,CAAC;YAC1D,OAAO;QACX,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEpC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;IACL,CAAC;IAEO,OAAO,CAAC,MAAc,EAAA;QAC1B,IAAI,MAAM,GAAG,MAAM,CAAC;QAEpB,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,IAAI,KAAK,CAAC;QACrE,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC;QAEpD,IAAI,gBAAgB,EAAE,CAAC;YACnB,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,CAAC;YACpF,MAAM,GAAG,CAAA,KAAA,EAAQ,MAAM,CAAA,EAAA,EAAK,UAAU,CAAA,CAAA,CAAG,CAAC;QAC9C,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YACd,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,CAAC;YACpF,MAAM,GAAG,CAAA,KAAA,EAAQ,MAAM,CAAA,EAAA,EAAK,UAAU,CAAA,CAAA,CAAG,CAAC;QAC9C,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,YAAY,CAAC,KAA6B,EAAA;QAC9C,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,OAAO,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,MAAM,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,eAAe,CAAC;QACrG,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,WAAW,CAAC;IAClE,CAAC;IAED,IAAY,iBAAiB,GAAA;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA,EAAA,EAAK,IAAI,CAAC,GAAG,CAAC,sBAAsB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC9E,CAAC;IAEO,sBAAsB,CAAC,EAAU,EAAE,KAA6B,EAAA;QACpE,IAAI,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,MAAM,CAAC;YAClE,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA,CAAA,EAAI,IAAI,CAAC,WAAW,CAAA,CAAA,EAAI,IAAI,CAAC,WAAW,GAAG,CAAA,OAAA,CAAS,CAAC,EAAA,EAAA,IAAA,CAAA,OAAA,CAAiB,KAAK,CAAA,GAAI,CAAC,CAAA,MAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAA,WAAA,IAAiB,GAAG,CAAA,GAAA,CAAA,CAAA,EAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;QACtL,CAAC;QACD,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA,CAAA,EAAI,IAAI,CAAC,WAAW,CAAA,EAAA,EAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAA,CAAA,CAAG,CAAC;IAC5G,CAAC;IAEO,sBAAsB,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,iBAAiB,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,WAAW,CAAA,EAAA,CAAI,CAAC;QAC1D,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,KAAK,CAAC,CAAA,GAAA,CAAK,CAAC;QACxN,KAAK,CAAC,iBAAiB,IAAI,CAAA,cAAA,EAAiB,IAAI,CAAC,iBAAiB,CAAA,GAAA,CAAK,CAAC;QACxE,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,sBAAsB,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAA,GAAA,CAAK,CAAC;QAC9R,KAAK,CAAC,iBAAiB,IAAI,CAAA,QAAA,CAAU,CAAC;IAC1C,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAE,UAAU,GAAG,KAAK,EAAA;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,KAAK,CAAC,MAAM,KAAK,yNAAwB,CAAC,QAAQ,EAAE,CAAC;gBACrD,OAAO;YACX,CAAC;YAED,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACnC,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YAClE,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,sBAAsB,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAA,GAAA,CAAK,CAAC;YACjO,OAAO;QACX,CAAC;QAED,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;IACvC,CAAC;IAEO,uBAAuB,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe,EAAA;QAC/G,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;YAClB,6CAA6C;YAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC5C,KAAK,CAAC,iBAAiB,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,iBAAiB,CAAA;sBACrD,MAAM,CAAC,sBAAsB,CAAA,gBAAA,EAAmB,MAAM,CAAC,sBAAsB,CAAA;;iBAElF,CAAC;YACN,CAAC;YAED,KAAK,CAAC,iBAAiB,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,gBAAgB,CAAA;kBACpD,MAAM,CAAC,sBAAsB,CAAA,GAAA,EAAM,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;;aAEpE,CAAC;QACN,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe,EAAE,UAAU,GAAG,KAAK,EAAA;QACxH,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,KAAK,CAAC,MAAM,KAAK,yNAAwB,CAAC,QAAQ,EAAE,CAAC;gBACrD,OAAO;YACX,CAAC;YAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,gBAAgB,CAAA,CAAA,EAAI,OAAO,CAAA,GAAA,CAAK,CAAC;YACtG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,KAAK,yNAAwB,CAAC,QAAQ,EAAE,CAAC;YAClE,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,gBAAgB,CAAA,CAAA,EAAI,OAAO,CAAA,GAAA,CAAK,CAAC;YACtG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;YACrD,OAAO;QACX,CAAC;QACD,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACnC,UAAU,GAAG,CAAA,GAAA,EAAM,CAAC,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACnH,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,gBAAgB,CAAA,CAAA,EAAI,OAAO,GAAG,UAAU,CAAA,GAAA,CAAK,CAAC;QACnH,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACzD,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,cAAe,CAAC,UAA8B,CAAC;QACnF,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YAC/H,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YACtE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAC9D,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,AAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,yNAAwB,CAAC,QAAQ,CAAC,GAAK,CAAD,GAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,MAAM,CAAC,CAAE,CAAC;YAChJ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrB,MAAM,OAAO,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtD,IAAI,CAAC,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;gBAExC,IAAI,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;oBACrC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjD,CAAC,MAAM,CAAC;oBACJ,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC5C,CAAC;YACL,CAAC;YAED,eAAe;YACf,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC3C,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,SAAS;YACT,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAC,AAAF,CAAG,2BAA2B,CAAC,EAAE,CAAC;YAC5D,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACtC,uBAAuB;YACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;gBACrC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjD,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5C,CAAC;QACL,CAAC;QAED,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAE5D,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,EAAE,mPAAqC,CAAC,KAAK,CAAC,CAAC;QACrG,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE9B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,uBAAA,EAA0B,IAAI,CAAC,mBAAmB,CAAA,GAAA,CAAK,CAAC;QAC/F,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,wBAAA,EAA2B,IAAI,CAAC,oBAAoB,CAAA,GAAA,CAAK,CAAC;QACjG,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,8BAAA,EAAiC,IAAI,CAAC,0BAA0B,CAAA,GAAA,CAAK,CAAC;QAE7G,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO,UAAU,CAAC;QACtB,CAAC;QAED,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gCAAA,EAAmC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,SAAA,EAAY,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA,EAAA,EAAK,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA,EAAA,EAAK,IAAI,CAAC,OAAO,CAAC,YAAY,CAAA,IAAA,CAAM,CAAC;QAC1L,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,iBAAA,EAAoB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA,GAAA,CAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,iBAAA,EAAoB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAA,GAAA,CAAK,CAAC;QACnF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gBAAA,EAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,GAAA,CAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gBAAA,EAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,GAAA,CAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gBAAA,EAAmB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,GAAA,CAAK,CAAC;QACjF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,mBAAA,EAAsB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA,GAAA,CAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,mBAAA,EAAsB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA,GAAA,CAAK,CAAC;QACvF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,kBAAA,EAAqB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA,GAAA,CAAK,CAAC;QACrF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,kBAAA,EAAqB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA,GAAA,CAAK,CAAC;QACrF,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,2BAAA,EAA8B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAA,GAAA,CAAK,CAAC;QAEvG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACnE,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QACrE,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QACtD,mBAAmB,CAAC,0BAA0B,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACjF,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,cAAc,EAAE,CAAC;YACzH,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC3D,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAE,WAAqC,EAAA;QACvH,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,mBAAmB,CAAC;QACnE,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;QACvE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC;QACxD,IAAI,CAAC,0BAA0B,GAAG,CAAC,CAAC,mBAAmB,CAAC,0BAA0B,CAAC;QAEnF,IAAI,mBAAmB,CAAC,OAAO,IAAI,0KAAC,eAAY,CAAC,wBAAwB,IAAI,mBAAmB,CAAC,OAAO,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;YACzH,IAAI,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzD,OAAO,GAAG,EAAE,CAAC;YACjB,CAAC,MAAM,IAAI,WAAW,EAAE,CAAC;gBACrB,mBAAmB,CAAC,OAAO,CAAC,GAAG,GAAG,WAAW,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC/E,mBAAmB,CAAC,OAAO,CAAC,IAAI,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC;YACvE,CAAC;YACD,IAAI,CAAC,OAAO,2KAAG,UAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAY,CAAC;QACzF,CAAC;IACL,CAAC;CACJ;6JAED,gBAAA,AAAa,EAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/reflectionTextureBaseBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/reflectionTextureBaseBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport type { NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport { NodeMaterial } from \"../../nodeMaterial\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { Nullable } from \"../../../../types\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { Constants } from \"../../../../Engines/constants\";\r\n\r\nimport { CubeTexture } from \"../../../Textures/cubeTexture\";\r\nimport { Texture } from \"../../../Textures/texture\";\r\nimport { EngineStore } from \"../../../../Engines/engineStore\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { SubMesh } from \"../../../..//Meshes/subMesh\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n\r\n/**\r\n * Base block used to read a reflection texture from a sampler\r\n */\r\nexport abstract class ReflectionTextureBaseBlock extends NodeMaterialBlock {\r\n    /** @internal */\r\n    public _define3DName: string;\r\n    /** @internal */\r\n    public _defineCubicName: string;\r\n    /** @internal */\r\n    public _defineExplicitName: string;\r\n    /** @internal */\r\n    public _defineProjectionName: string;\r\n    /** @internal */\r\n    public _defineLocalCubicName: string;\r\n    /** @internal */\r\n    public _defineSphericalName: string;\r\n    /** @internal */\r\n    public _definePlanarName: string;\r\n    /** @internal */\r\n    public _defineEquirectangularName: string;\r\n    /** @internal */\r\n    public _defineMirroredEquirectangularFixedName: string;\r\n    /** @internal */\r\n    public _defineEquirectangularFixedName: string;\r\n    /** @internal */\r\n    public _defineSkyboxName: string;\r\n    /** @internal */\r\n    public _defineOppositeZ: string;\r\n    /** @internal */\r\n    public _cubeSamplerName: string;\r\n    /** @internal */\r\n    public _2DSamplerName: string;\r\n    /** @internal */\r\n    public _reflectionPositionName: string;\r\n    /** @internal */\r\n    public _reflectionSizeName: string;\r\n\r\n    protected _positionUVWName: string;\r\n    protected _directionWname: string;\r\n    protected _reflectionVectorName: string;\r\n    /** @internal */\r\n    public _reflectionCoordsName: string;\r\n    /** @internal */\r\n    public _reflectionMatrixName: string;\r\n    protected _reflectionColorName: string;\r\n    protected _worldPositionNameInFragmentOnlyMode: string;\r\n\r\n    protected _texture: Nullable<BaseTexture>;\r\n    /**\r\n     * Gets or sets the texture associated with the node\r\n     */\r\n    public get texture(): Nullable<BaseTexture> {\r\n        return this._texture;\r\n    }\r\n\r\n    public set texture(texture: Nullable<BaseTexture>) {\r\n        if (this._texture === texture) {\r\n            return;\r\n        }\r\n\r\n        const scene = texture?.getScene() ?? EngineStore.LastCreatedScene;\r\n\r\n        if (!texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(this._texture!);\r\n            });\r\n        }\r\n\r\n        this._texture = texture;\r\n\r\n        if (texture && scene) {\r\n            scene.markAllMaterialsAsDirty(Constants.MATERIAL_TextureDirtyFlag, (mat) => {\r\n                return mat.hasTexture(texture);\r\n            });\r\n        }\r\n    }\r\n\r\n    /** Indicates that no code should be generated in the vertex shader. Can be useful in some specific circumstances (like when doing ray marching for eg) */\r\n    @editableInPropertyPage(\"Generate only fragment code\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: { rebuild: true, update: true, onValidation: ReflectionTextureBaseBlock._OnGenerateOnlyFragmentCodeChanged },\r\n    })\r\n    public generateOnlyFragmentCode = false;\r\n\r\n    protected static _OnGenerateOnlyFragmentCodeChanged(block: NodeMaterialBlock, _propertyName: string): boolean {\r\n        const that = block as ReflectionTextureBaseBlock;\r\n        return that._onGenerateOnlyFragmentCodeChanged();\r\n    }\r\n\r\n    protected _onGenerateOnlyFragmentCodeChanged(): boolean {\r\n        this._setTarget();\r\n        return true;\r\n    }\r\n\r\n    protected _setTarget(): void {\r\n        this._setInitialTarget(this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.VertexAndFragment);\r\n    }\r\n\r\n    /**\r\n     * Create a new ReflectionTextureBaseBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ReflectionTextureBaseBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public abstract get position(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public abstract get worldPosition(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public abstract get worldNormal(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the world input component\r\n     */\r\n    public abstract get world(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the camera (or eye) position component\r\n     */\r\n    public abstract get cameraPosition(): NodeMaterialConnectionPoint;\r\n\r\n    /**\r\n     * Gets the view input component\r\n     */\r\n    public abstract get view(): NodeMaterialConnectionPoint;\r\n\r\n    protected _getTexture(): Nullable<BaseTexture> {\r\n        return this.texture;\r\n    }\r\n\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync(state.shaderLanguage);\r\n    }\r\n\r\n    private async _initShaderSourceAsync(shaderLanguage: ShaderLanguage) {\r\n        this._codeIsReady = false;\r\n\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            await import(\"../../../../ShadersWGSL/ShadersInclude/reflectionFunction\");\r\n        } else {\r\n            await import(\"../../../../Shaders/ShadersInclude/reflectionFunction\");\r\n        }\r\n\r\n        this._codeIsReady = true;\r\n        this.onCodeIsReadyObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Auto configure the node based on the existing material\r\n     * @param material defines the material to configure\r\n     * @param additionalFilteringInfo defines additional info to be used when filtering inputs (we might want to skip some non relevant blocks)\r\n     */\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        if (!this.position.isConnected) {\r\n            let positionInput = material.getInputBlockByPredicate((b) => b.isAttribute && b.name === \"position\" && additionalFilteringInfo(b));\r\n\r\n            if (!positionInput) {\r\n                positionInput = new InputBlock(\"position\");\r\n                positionInput.setAsAttribute();\r\n            }\r\n            positionInput.output.connectTo(this.position);\r\n        }\r\n\r\n        if (!this.world.isConnected) {\r\n            let worldInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.World && additionalFilteringInfo(b));\r\n\r\n            if (!worldInput) {\r\n                worldInput = new InputBlock(\"world\");\r\n                worldInput.setAsSystemValue(NodeMaterialSystemValues.World);\r\n            }\r\n            worldInput.output.connectTo(this.world);\r\n        }\r\n\r\n        if (this.view && !this.view.isConnected) {\r\n            let viewInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.View && additionalFilteringInfo(b));\r\n\r\n            if (!viewInput) {\r\n                viewInput = new InputBlock(\"view\");\r\n                viewInput.setAsSystemValue(NodeMaterialSystemValues.View);\r\n            }\r\n            viewInput.output.connectTo(this.view);\r\n        }\r\n    }\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines) {\r\n        if (!defines._areTexturesDirty) {\r\n            return;\r\n        }\r\n\r\n        const texture = this._getTexture();\r\n\r\n        if (!texture || !texture.getTextureMatrix) {\r\n            return;\r\n        }\r\n\r\n        defines.setValue(this._define3DName, texture.isCube, true);\r\n        defines.setValue(this._defineLocalCubicName, (<any>texture).boundingBoxSize ? true : false, true);\r\n        defines.setValue(this._defineExplicitName, texture.coordinatesMode === Constants.TEXTURE_EXPLICIT_MODE, true);\r\n        defines.setValue(this._defineSkyboxName, texture.coordinatesMode === Constants.TEXTURE_SKYBOX_MODE, true);\r\n        defines.setValue(this._defineCubicName, texture.coordinatesMode === Constants.TEXTURE_CUBIC_MODE || texture.coordinatesMode === Constants.TEXTURE_INVCUBIC_MODE, true);\r\n        defines.setValue(\"INVERTCUBICMAP\", texture.coordinatesMode === Constants.TEXTURE_INVCUBIC_MODE, true);\r\n        defines.setValue(this._defineSphericalName, texture.coordinatesMode === Constants.TEXTURE_SPHERICAL_MODE, true);\r\n        defines.setValue(this._definePlanarName, texture.coordinatesMode === Constants.TEXTURE_PLANAR_MODE, true);\r\n        defines.setValue(this._defineProjectionName, texture.coordinatesMode === Constants.TEXTURE_PROJECTION_MODE, true);\r\n        defines.setValue(this._defineEquirectangularName, texture.coordinatesMode === Constants.TEXTURE_EQUIRECTANGULAR_MODE, true);\r\n        defines.setValue(this._defineEquirectangularFixedName, texture.coordinatesMode === Constants.TEXTURE_FIXED_EQUIRECTANGULAR_MODE, true);\r\n        defines.setValue(this._defineMirroredEquirectangularFixedName, texture.coordinatesMode === Constants.TEXTURE_FIXED_EQUIRECTANGULAR_MIRRORED_MODE, true);\r\n    }\r\n\r\n    public override isReady() {\r\n        const texture = this._getTexture();\r\n\r\n        if (texture && !texture.isReadyOrNotBlocking()) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh, _subMesh?: SubMesh) {\r\n        const texture = this._getTexture();\r\n\r\n        if (!mesh || !texture) {\r\n            return;\r\n        }\r\n\r\n        effect.setMatrix(this._reflectionMatrixName, texture.getReflectionTextureMatrix());\r\n\r\n        if (texture.isCube) {\r\n            effect.setTexture(this._cubeSamplerName, texture);\r\n        } else {\r\n            effect.setTexture(this._2DSamplerName, texture);\r\n        }\r\n\r\n        if ((<any>texture).boundingBoxSize) {\r\n            const cubeTexture = <CubeTexture>texture;\r\n            effect.setVector3(this._reflectionPositionName, cubeTexture.boundingBoxPosition);\r\n            effect.setVector3(this._reflectionSizeName, cubeTexture.boundingBoxSize);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the code to inject in the vertex shader\r\n     * @param state current state of the node material building\r\n     * @returns the shader code\r\n     */\r\n    public handleVertexSide(state: NodeMaterialBuildState): string {\r\n        if (this.generateOnlyFragmentCode && state.target === NodeMaterialBlockTargets.Vertex) {\r\n            return \"\";\r\n        }\r\n\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        this._define3DName = state._getFreeDefineName(\"REFLECTIONMAP_3D\");\r\n        this._defineCubicName = state._getFreeDefineName(\"REFLECTIONMAP_CUBIC\");\r\n        this._defineSphericalName = state._getFreeDefineName(\"REFLECTIONMAP_SPHERICAL\");\r\n        this._definePlanarName = state._getFreeDefineName(\"REFLECTIONMAP_PLANAR\");\r\n        this._defineProjectionName = state._getFreeDefineName(\"REFLECTIONMAP_PROJECTION\");\r\n        this._defineExplicitName = state._getFreeDefineName(\"REFLECTIONMAP_EXPLICIT\");\r\n        this._defineEquirectangularName = state._getFreeDefineName(\"REFLECTIONMAP_EQUIRECTANGULAR\");\r\n        this._defineLocalCubicName = state._getFreeDefineName(\"USE_LOCAL_REFLECTIONMAP_CUBIC\");\r\n        this._defineMirroredEquirectangularFixedName = state._getFreeDefineName(\"REFLECTIONMAP_MIRROREDEQUIRECTANGULAR_FIXED\");\r\n        this._defineEquirectangularFixedName = state._getFreeDefineName(\"REFLECTIONMAP_EQUIRECTANGULAR_FIXED\");\r\n        this._defineSkyboxName = state._getFreeDefineName(\"REFLECTIONMAP_SKYBOX\");\r\n        this._defineOppositeZ = state._getFreeDefineName(\"REFLECTIONMAP_OPPOSITEZ\");\r\n\r\n        this._reflectionMatrixName = state._getFreeVariableName(\"reflectionMatrix\");\r\n\r\n        state._emitUniformFromString(this._reflectionMatrixName, NodeMaterialBlockConnectionPointTypes.Matrix);\r\n\r\n        let code = \"\";\r\n\r\n        this._worldPositionNameInFragmentOnlyMode = state._getFreeVariableName(\"worldPosition\");\r\n\r\n        const worldPosVaryingName = this.generateOnlyFragmentCode ? this._worldPositionNameInFragmentOnlyMode : \"v_\" + this.worldPosition.associatedVariableName;\r\n        if (this.generateOnlyFragmentCode || state._emitVaryingFromString(worldPosVaryingName, NodeMaterialBlockConnectionPointTypes.Vector4)) {\r\n            if (this.generateOnlyFragmentCode) {\r\n                code += `${state._declareLocalVar(worldPosVaryingName, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${this.worldPosition.associatedVariableName};\\n`;\r\n            } else {\r\n                code += `${isWebGPU ? \"vertexOutputs.\" : \"\"}${worldPosVaryingName} = ${this.worldPosition.associatedVariableName};\\n`;\r\n            }\r\n        }\r\n\r\n        this._positionUVWName = state._getFreeVariableName(\"positionUVW\");\r\n        this._directionWname = state._getFreeVariableName(\"directionW\");\r\n\r\n        if (this.generateOnlyFragmentCode || state._emitVaryingFromString(this._positionUVWName, NodeMaterialBlockConnectionPointTypes.Vector3, this._defineSkyboxName)) {\r\n            code += `#ifdef ${this._defineSkyboxName}\\n`;\r\n            if (this.generateOnlyFragmentCode) {\r\n                code += `${state._declareLocalVar(this._positionUVWName, NodeMaterialBlockConnectionPointTypes.Vector3)} = ${this.position.associatedVariableName}.xyz;\\n`;\r\n            } else {\r\n                code += `${isWebGPU ? \"vertexOutputs.\" : \"\"}${this._positionUVWName} = ${this.position.associatedVariableName}.xyz;\\n`;\r\n            }\r\n            code += `#endif\\n`;\r\n        }\r\n\r\n        if (\r\n            this.generateOnlyFragmentCode ||\r\n            state._emitVaryingFromString(\r\n                this._directionWname,\r\n                NodeMaterialBlockConnectionPointTypes.Vector3,\r\n                `defined(${this._defineEquirectangularFixedName}) || defined(${this._defineMirroredEquirectangularFixedName})`\r\n            )\r\n        ) {\r\n            code += `#if defined(${this._defineEquirectangularFixedName}) || defined(${this._defineMirroredEquirectangularFixedName})\\n`;\r\n            if (this.generateOnlyFragmentCode) {\r\n                code += `${state._declareLocalVar(this._directionWname, NodeMaterialBlockConnectionPointTypes.Vector3)} = normalize(vec3${state.fSuffix}(${this.world.associatedVariableName} * vec4${state.fSuffix}(${\r\n                    this.position.associatedVariableName\r\n                }.xyz, 0.0)));\\n`;\r\n            } else {\r\n                code += `${isWebGPU ? \"vertexOutputs.\" : \"\"}${this._directionWname} = normalize(vec3${state.fSuffix}(${this.world.associatedVariableName} * vec4${state.fSuffix}(${\r\n                    this.position.associatedVariableName\r\n                }.xyz, 0.0)));\\n`;\r\n            }\r\n            code += `#endif\\n`;\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Handles the inits for the fragment code path\r\n     * @param state node material build state\r\n     */\r\n    public handleFragmentSideInits(state: NodeMaterialBuildState) {\r\n        state.sharedData.blockingBlocks.push(this);\r\n        state.sharedData.textureBlocks.push(this);\r\n\r\n        // Samplers\r\n        this._cubeSamplerName = state._getFreeVariableName(this.name + \"CubeSampler\");\r\n        state.samplers.push(this._cubeSamplerName);\r\n\r\n        this._2DSamplerName = state._getFreeVariableName(this.name + \"2DSampler\");\r\n        state.samplers.push(this._2DSamplerName);\r\n\r\n        state._samplerDeclaration += `#ifdef ${this._define3DName}\\n`;\r\n        state._emitCubeSampler(this._cubeSamplerName, \"\", true);\r\n        state._samplerDeclaration += `#else\\n`;\r\n        state._emit2DSampler(this._2DSamplerName, \"\", true);\r\n        state._samplerDeclaration += `#endif\\n`;\r\n\r\n        // Fragment\r\n        state.sharedData.blocksWithDefines.push(this);\r\n        state.sharedData.bindableBlocks.push(this);\r\n\r\n        const comments = `//${this.name}`;\r\n        state._emitFunctionFromInclude(\"helperFunctions\", comments);\r\n        state._emitFunctionFromInclude(\"reflectionFunction\", comments, {\r\n            replaceStrings: [\r\n                { search: /vec3 computeReflectionCoords/g, replace: \"void DUMMYFUNC\" },\r\n                { search: /fn computeReflectionCoords\\(worldPos: vec4f,worldNormal: vec3f\\)->vec3f/g, replace: \"fn DUMMYFUNC()\" },\r\n            ],\r\n        });\r\n\r\n        this._reflectionColorName = state._getFreeVariableName(\"reflectionColor\");\r\n        this._reflectionVectorName = state._getFreeVariableName(\"reflectionUVW\");\r\n        this._reflectionCoordsName = state._getFreeVariableName(\"reflectionCoords\");\r\n\r\n        this._reflectionPositionName = state._getFreeVariableName(\"vReflectionPosition\");\r\n        state._emitUniformFromString(this._reflectionPositionName, NodeMaterialBlockConnectionPointTypes.Vector3);\r\n\r\n        this._reflectionSizeName = state._getFreeVariableName(\"vReflectionPosition\");\r\n        state._emitUniformFromString(this._reflectionSizeName, NodeMaterialBlockConnectionPointTypes.Vector3);\r\n    }\r\n\r\n    /**\r\n     * Generates the reflection coords code for the fragment code path\r\n     * @param state defines the build state\r\n     * @param worldNormalVarName name of the world normal variable\r\n     * @param worldPos name of the world position variable. If not provided, will use the world position connected to this block\r\n     * @param onlyReflectionVector if true, generates code only for the reflection vector computation, not for the reflection coordinates\r\n     * @param doNotEmitInvertZ if true, does not emit the invertZ code\r\n     * @returns the shader code\r\n     */\r\n    public handleFragmentSideCodeReflectionCoords(\r\n        state: NodeMaterialBuildState,\r\n        worldNormalVarName: string,\r\n        worldPos?: string,\r\n        onlyReflectionVector = false,\r\n        doNotEmitInvertZ = false\r\n    ): string {\r\n        const isWebGPU = state.shaderLanguage === ShaderLanguage.WGSL;\r\n        const reflectionMatrix = (isWebGPU ? \"uniforms.\" : \"\") + this._reflectionMatrixName;\r\n        const direction = `normalize(${this._directionWname})`;\r\n        const positionUVW = `${this._positionUVWName}`;\r\n        const vEyePosition = `${this.cameraPosition.associatedVariableName}`;\r\n        const view = `${this.view.associatedVariableName}`;\r\n        const fragmentInputsPrefix = isWebGPU ? \"fragmentInputs.\" : \"\";\r\n\r\n        if (!worldPos) {\r\n            worldPos = this.generateOnlyFragmentCode ? this._worldPositionNameInFragmentOnlyMode : `${fragmentInputsPrefix}v_${this.worldPosition.associatedVariableName}`;\r\n        }\r\n\r\n        worldNormalVarName += \".xyz\";\r\n\r\n        let code = `\r\n            #ifdef ${this._defineMirroredEquirectangularFixedName}\r\n               ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = computeMirroredFixedEquirectangularCoords(${worldPos}, ${worldNormalVarName}, ${direction});\r\n            #endif\r\n\r\n            #ifdef ${this._defineEquirectangularFixedName}\r\n                ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = computeFixedEquirectangularCoords(${worldPos}, ${worldNormalVarName}, ${direction});\r\n            #endif\r\n\r\n            #ifdef ${this._defineEquirectangularName}\r\n                ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = computeEquirectangularCoords(${worldPos}, ${worldNormalVarName}, ${vEyePosition}.xyz, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._defineSphericalName}\r\n                ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = computeSphericalCoords(${worldPos}, ${worldNormalVarName}, ${view}, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._definePlanarName}\r\n                ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = computePlanarCoords(${worldPos}, ${worldNormalVarName}, ${vEyePosition}.xyz, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._defineCubicName}\r\n                #ifdef ${this._defineLocalCubicName}\r\n                    ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = computeCubicLocalCoords(${worldPos}, ${worldNormalVarName}, ${vEyePosition}.xyz, ${reflectionMatrix}, ${this._reflectionSizeName}, ${this._reflectionPositionName});\r\n                #else\r\n                ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = computeCubicCoords(${worldPos}, ${worldNormalVarName}, ${vEyePosition}.xyz, ${reflectionMatrix});\r\n                #endif\r\n            #endif\r\n\r\n            #ifdef ${this._defineProjectionName}\r\n                ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = computeProjectionCoords(${worldPos}, ${view}, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._defineSkyboxName}\r\n                ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = computeSkyBoxCoords(${positionUVW}, ${reflectionMatrix});\r\n            #endif\r\n\r\n            #ifdef ${this._defineExplicitName}\r\n                ${state._declareLocalVar(this._reflectionVectorName, NodeMaterialBlockConnectionPointTypes.Vector3)} = vec3(0, 0, 0);\r\n            #endif\\n`;\r\n\r\n        if (!doNotEmitInvertZ) {\r\n            code += `#ifdef ${this._defineOppositeZ}\r\n                ${this._reflectionVectorName}.z *= -1.0;\r\n            #endif\\n`;\r\n        }\r\n\r\n        if (!onlyReflectionVector) {\r\n            code += `\r\n                #ifdef ${this._define3DName}\r\n                    ${state._declareLocalVar(this._reflectionCoordsName, NodeMaterialBlockConnectionPointTypes.Vector3)} = ${this._reflectionVectorName};\r\n                #else\r\n                    ${state._declareLocalVar(this._reflectionCoordsName, NodeMaterialBlockConnectionPointTypes.Vector2)} = ${this._reflectionVectorName}.xy;\r\n                    #ifdef ${this._defineProjectionName}\r\n                        ${this._reflectionCoordsName} /= ${this._reflectionVectorName}.z;\r\n                    #endif\r\n                    ${this._reflectionCoordsName}.y = 1.0 - ${this._reflectionCoordsName}.y;\r\n                #endif\\n`;\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Generates the reflection color code for the fragment code path\r\n     * @param state defines the build state\r\n     * @param lodVarName name of the lod variable\r\n     * @param swizzleLookupTexture swizzle to use for the final color variable\r\n     * @returns the shader code\r\n     */\r\n    public handleFragmentSideCodeReflectionColor(state: NodeMaterialBuildState, lodVarName?: string, swizzleLookupTexture = \".rgb\"): string {\r\n        let colorType = NodeMaterialBlockConnectionPointTypes.Vector4;\r\n\r\n        if (swizzleLookupTexture.length === 3) {\r\n            colorType = NodeMaterialBlockConnectionPointTypes.Vector3;\r\n        }\r\n\r\n        let code = `${state._declareLocalVar(this._reflectionColorName, colorType)};\r\n            #ifdef ${this._define3DName}\\n`;\r\n\r\n        if (lodVarName) {\r\n            code += `${this._reflectionColorName} = ${state._generateTextureSampleCubeLOD(this._reflectionVectorName, this._cubeSamplerName, lodVarName)}${swizzleLookupTexture};\\n`;\r\n        } else {\r\n            code += `${this._reflectionColorName} = ${state._generateTextureSampleCube(this._reflectionVectorName, this._cubeSamplerName)}${swizzleLookupTexture};\\n`;\r\n        }\r\n\r\n        code += `\r\n            #else\\n`;\r\n\r\n        if (lodVarName) {\r\n            code += `${this._reflectionColorName} =${state._generateTextureSampleLOD(this._reflectionCoordsName, this._2DSamplerName, lodVarName)}${swizzleLookupTexture};\\n`;\r\n        } else {\r\n            code += `${this._reflectionColorName} = ${state._generateTextureSample(this._reflectionCoordsName, this._2DSamplerName)}${swizzleLookupTexture};\\n`;\r\n        }\r\n\r\n        code += `#endif\\n`;\r\n\r\n        return code;\r\n    }\r\n\r\n    /**\r\n     * Generates the code corresponding to the connected output points\r\n     * @param state node material build state\r\n     * @param varName name of the variable to output\r\n     * @returns the shader code\r\n     */\r\n    public writeOutputs(state: NodeMaterialBuildState, varName: string): string {\r\n        let code = \"\";\r\n\r\n        if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n            for (const output of this._outputs) {\r\n                if (output.hasEndpoints) {\r\n                    code += `${state._declareOutput(output)} = ${varName}.${output.name};\\n`;\r\n                }\r\n            }\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n        return this;\r\n    }\r\n\r\n    protected override _dumpPropertiesCode() {\r\n        let codeString = super._dumpPropertiesCode();\r\n\r\n        if (!this.texture) {\r\n            return codeString;\r\n        }\r\n\r\n        if (this.texture.isCube) {\r\n            const forcedExtension = (this.texture as CubeTexture).forcedExtension;\r\n            codeString += `${this._codeVariableName}.texture = new BABYLON.CubeTexture(\"${this.texture.name}\", undefined, undefined, ${\r\n                this.texture.noMipmap\r\n            }, null, undefined, undefined, undefined, ${this.texture._prefiltered}, ${forcedExtension ? '\"' + forcedExtension + '\"' : \"null\"});\\n`;\r\n        } else {\r\n            codeString += `${this._codeVariableName}.texture = new BABYLON.Texture(\"${this.texture.name}\", null);\\n`;\r\n        }\r\n        codeString += `${this._codeVariableName}.texture.coordinatesMode = ${this.texture.coordinatesMode};\\n`;\r\n\r\n        return codeString;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        if (this.texture && !this.texture.isRenderTarget) {\r\n            serializationObject.texture = this.texture.serialize();\r\n        }\r\n\r\n        serializationObject.generateOnlyFragmentCode = this.generateOnlyFragmentCode;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        if (serializationObject.texture && !NodeMaterial.IgnoreTexturesAtLoadTime) {\r\n            rootUrl = serializationObject.texture.url.indexOf(\"data:\") === 0 ? \"\" : rootUrl;\r\n            if (serializationObject.texture.isCube) {\r\n                this.texture = CubeTexture.Parse(serializationObject.texture, scene, rootUrl);\r\n            } else {\r\n                this.texture = Texture.Parse(serializationObject.texture, scene, rootUrl);\r\n            }\r\n        }\r\n\r\n        this.generateOnlyFragmentCode = serializationObject.generateOnlyFragmentCode;\r\n\r\n        this._setTarget();\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ReflectionTextureBaseBlock\", ReflectionTextureBaseBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAIhF,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAIlD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAGhF,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAC;AAC5D,OAAO,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AACpD,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;AAEtG,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;;;;;;;;;;;;;AAMpG,MAAgB,0BAA2B,uLAAQ,oBAAiB;IA6CtE;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,OAA8B,EAAA;QAC7C,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,EAAE,QAAQ,EAAE,kKAAI,cAAW,CAAC,gBAAgB,CAAC;QAElE,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC;YACpB,KAAK,CAAC,uBAAuB,CAAC,GAAA,CAAA,KAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,QAAS,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;YACnB,KAAK,CAAC,uBAAuB,CAAC,GAAA,CAAA,KAAS,CAAC,yBAAyB,EAAE,CAAC,GAAG,EAAE,EAAE;gBACvE,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAQS,MAAM,CAAC,kCAAkC,CAAC,KAAwB,EAAE,aAAqB,EAAA;QAC/F,MAAM,IAAI,GAAG,KAAmC,CAAC;QACjD,OAAO,IAAI,CAAC,kCAAkC,EAAE,CAAC;IACrD,CAAC;IAES,kCAAkC,GAAA;QACxC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,UAAU,GAAA;QAChB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,+LAAC,2BAAwB,CAAC,QAAQ,CAAC,CAAC,+LAAC,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;IAC3I,CAAC;IAED;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,EAAE,yNAAwB,CAAC,iBAAiB,CAAC,CAAC;QAzB5D,wJAAA,EAA0J,CAInJ,IAAA,CAAA,wBAAwB,GAAG,KAAK,CAAC;IAsBxC,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,4BAA4B,CAAC;IACxC,CAAC;IAgCS,WAAW,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAEe,UAAU,CAAC,KAA6B,EAAA;QACpD,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,cAA8B,EAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACzC,MAAM,MAAM,CAAC,2DAA2D,CAAC,CAAC;QAC9E,CAAC,MAAM,CAAC;YACJ,MAAM,MAAM,CAAC,uDAAuD,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;;;OAIG,CACa,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,CAAG,CAAD,GAAK,EAAA;QAC5H,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC7B,IAAI,aAAa,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAC,AAAF,CAAG,WAAW,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAEnI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,aAAa,GAAG,6LAAI,cAAU,CAAC,UAAU,CAAC,CAAC;gBAC3C,aAAa,CAAC,cAAc,EAAE,CAAC;YACnC,CAAC;YACD,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YAC1B,IAAI,UAAU,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,mMAAK,2BAAwB,CAAC,KAAK,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1I,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,UAAU,GAAG,IAAI,uMAAU,CAAC,OAAO,CAAC,CAAC;gBACrC,UAAU,CAAC,gBAAgB,+LAAC,2BAAwB,CAAC,KAAK,CAAC,CAAC;YAChE,CAAC;YACD,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,IAAI,SAAS,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,mMAAK,2BAAwB,CAAC,IAAI,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAExI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,SAAS,GAAG,8LAAI,aAAU,CAAC,MAAM,CAAC,CAAC;gBACnC,SAAS,CAAC,gBAAgB,CAAC,yNAAwB,CAAC,IAAI,CAAC,CAAC;YAC9D,CAAC;YACD,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IAEe,cAAc,CAAC,OAA4B,EAAA;QACvD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACxC,OAAO;QACX,CAAC;QAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC3D,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAQ,OAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAClG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,eAAe,KAAK,GAAA,MAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAC9G,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,eAAe,KAAK,GAAA,MAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAC1G,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,eAAe,KAAK,KAAA,IAAS,CAAC,GAAA,eAAkB,IAAI,CAAA,GAAA,GAAO,CAAC,eAAe,KAAK,SAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACvK,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,eAAe,KAAK,GAAA,MAAS,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QACtG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,eAAe,KAAK,GAAA,MAAS,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;QAChH,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,eAAe,KAAK,GAAA,MAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;QAC1G,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,eAAe,KAAK,GAAA,MAAS,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAClH,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,0BAA0B,EAAE,OAAO,CAAC,eAAe,KAAK,GAAA,MAAS,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC;QAC5H,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,+BAA+B,EAAE,OAAO,CAAC,eAAe,KAAK,GAAA,MAAS,CAAC,kCAAkC,EAAE,IAAI,CAAC,CAAC;QACvI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,uCAAuC,EAAE,OAAO,CAAC,eAAe,KAAK,GAAA,MAAS,CAAC,2CAA2C,EAAE,IAAI,CAAC,CAAC;IAC5J,CAAC;IAEe,OAAO,GAAA;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAE,QAAkB,EAAA;QAC5F,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAEnC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,CAAC,0BAA0B,EAAE,CAAC,CAAC;QAEnF,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACtD,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC;QAED,IAAU,OAAQ,CAAC,eAAe,EAAE,CAAC;YACjC,MAAM,WAAW,GAAgB,OAAO,CAAC;YACzC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE,WAAW,CAAC,mBAAmB,CAAC,CAAC;YACjF,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;QAC7E,CAAC;IACL,CAAC;IAED;;;;OAIG,CACI,gBAAgB,CAAC,KAA6B,EAAA;QACjD,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,MAAM,EAAE,CAAC;YACpF,OAAO,EAAE,CAAC;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;QAClE,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;QACxE,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QAChF,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;QAC1E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;QAClF,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;QAC9E,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC,kBAAkB,CAAC,+BAA+B,CAAC,CAAC;QAC5F,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,kBAAkB,CAAC,+BAA+B,CAAC,CAAC;QACvF,IAAI,CAAC,uCAAuC,GAAG,KAAK,CAAC,kBAAkB,CAAC,6CAA6C,CAAC,CAAC;QACvH,IAAI,CAAC,+BAA+B,GAAG,KAAK,CAAC,kBAAkB,CAAC,qCAAqC,CAAC,CAAC;QACvG,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;QAC1E,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,CAAC;QAE5E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,MAAM,CAAC,CAAC;QAEvG,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,IAAI,CAAC,oCAAoC,GAAG,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAExF,MAAM,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC;QACzJ,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,CAAC,sBAAsB,CAAC,mBAAmB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,EAAE,CAAC;YACpI,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,mBAAmB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;YAC9J,CAAC,MAAM,CAAC;gBACJ,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,mBAAmB,CAAA,GAAA,EAAM,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAA,GAAA,CAAK,CAAC;YAC1H,CAAC;QACL,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAClE,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,wBAAwB,IAAI,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC9J,IAAI,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,iBAAiB,CAAA,EAAA,CAAI,CAAC;YAC7C,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAA,OAAA,CAAS,CAAC;YAC/J,CAAC,MAAM,CAAC;gBACJ,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAA,GAAA,EAAM,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAA,OAAA,CAAS,CAAC;YAC3H,CAAC;YACD,IAAI,IAAI,CAAA,QAAA,CAAU,CAAC;QACvB,CAAC;QAED,IACI,IAAI,CAAC,wBAAwB,IAC7B,KAAK,CAAC,sBAAsB,CACxB,IAAI,CAAC,eAAe,6MACpB,wCAAqC,CAAC,OAAO,EAC7C,CAAA,QAAA,EAAW,IAAI,CAAC,+BAA+B,CAAA,aAAA,EAAgB,IAAI,CAAC,uCAAuC,CAAA,CAAA,CAAG,CACjH,EACH,CAAC;YACC,IAAI,IAAI,CAAA,YAAA,EAAe,IAAI,CAAC,+BAA+B,CAAA,aAAA,EAAgB,IAAI,CAAC,uCAAuC,CAAA,GAAA,CAAK,CAAC;YAC7H,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,iBAAA,EAAoB,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,CAAA,EAC/L,IAAI,CAAC,QAAQ,CAAC,sBAClB,CAAA,eAAA,CAAiB,CAAC;YACtB,CAAC,MAAM,CAAC;gBACJ,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe,CAAA,iBAAA,EAAoB,KAAK,CAAC,OAAO,CAAA,CAAA,EAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAA,OAAA,EAAU,KAAK,CAAC,OAAO,CAAA,CAAA,EAC3J,IAAI,CAAC,QAAQ,CAAC,sBAClB,CAAA,eAAA,CAAiB,CAAC;YACtB,CAAC;YACD,IAAI,IAAI,CAAA,QAAA,CAAU,CAAC;QACvB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG,CACI,uBAAuB,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE1C,WAAW;QACX,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC;QAC9E,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAE3C,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,CAAC;QAC1E,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEzC,KAAK,CAAC,mBAAmB,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,aAAa,CAAA,EAAA,CAAI,CAAC;QAC9D,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QACxD,KAAK,CAAC,mBAAmB,IAAI,CAAA,OAAA,CAAS,CAAC;QACvC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QACpD,KAAK,CAAC,mBAAmB,IAAI,CAAA,QAAA,CAAU,CAAC;QAExC,WAAW;QACX,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE3C,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,KAAK,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;QAC5D,KAAK,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,EAAE;YAC3D,cAAc,EAAE;gBACZ;oBAAE,MAAM,EAAE,+BAA+B;oBAAE,OAAO,EAAE,gBAAgB;gBAAA,CAAE;gBACtE;oBAAE,MAAM,EAAE,0EAA0E;oBAAE,OAAO,EAAE,gBAAgB;gBAAA,CAAE;aACpH;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAC1E,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QACzE,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,CAAC;QAE5E,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QACjF,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,uBAAuB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAE1G,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QAC7E,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,mBAAmB,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAC;IAC1G,CAAC;IAED;;;;;;;;OAQG,CACI,sCAAsC,CACzC,KAA6B,EAC7B,kBAA0B,EAC1B,QAAiB,EACjB,oBAAoB,GAAG,KAAK,EAC5B,gBAAgB,GAAG,KAAK,EAAA;QAExB,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC;QAC9D,MAAM,gBAAgB,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpF,MAAM,SAAS,GAAG,CAAA,UAAA,EAAa,IAAI,CAAC,eAAe,CAAA,CAAA,CAAG,CAAC;QACvD,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC/C,MAAM,YAAY,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,sBAAsB,EAAE,CAAC;QACrE,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACnD,MAAM,oBAAoB,GAAG,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;QAE/D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC,CAAC,GAAG,oBAAoB,CAAA,EAAA,EAAK,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;QACnK,CAAC;QAED,kBAAkB,IAAI,MAAM,CAAC;QAE7B,IAAI,IAAI,GAAG,CAAA;qBACE,IAAI,CAAC,uCAAuC,CAAA;iBAChD,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAA,6CAAA,EAAgD,QAAQ,CAAA,EAAA,EAAK,kBAAkB,CAAA,EAAA,EAAK,SAAS,CAAA;;;qBAG1L,IAAI,CAAC,+BAA+B,CAAA;kBACvC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,qCAAA,EAAwC,QAAQ,CAAA,EAAA,EAAK,kBAAkB,CAAA,EAAA,EAAK,SAAS,CAAA;;;qBAGnL,IAAI,CAAC,0BAA0B,CAAA;kBAClC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,gCAAA,EAAmC,QAAQ,CAAA,EAAA,EAAK,kBAAkB,CAAA,EAAA,EAAK,YAAY,CAAA,MAAA,EAAS,gBAAgB,CAAA;;;qBAG1M,IAAI,CAAC,oBAAoB,CAAA;kBAC5B,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,0BAAA,EAA6B,QAAQ,CAAA,EAAA,EAAK,kBAAkB,CAAA,EAAA,EAAK,IAAI,CAAA,EAAA,EAAK,gBAAgB,CAAA;;;qBAGxL,IAAI,CAAC,iBAAiB,CAAA;kBACzB,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAA,uBAAA,EAA0B,QAAQ,CAAA,EAAA,EAAK,kBAAkB,CAAA,EAAA,EAAK,YAAY,CAAA,MAAA,EAAS,gBAAgB,CAAA;;;qBAGjM,IAAI,CAAC,gBAAgB,CAAA;yBACjB,IAAI,CAAC,qBAAqB,CAAA;sBAC7B,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAA,2BAAA,EAA8B,QAAQ,CAAA,EAAA,EAAK,kBAAkB,CAAA,EAAA,EAAK,YAAY,CAAA,MAAA,EAAS,gBAAgB,CAAA,EAAA,EAAK,IAAI,CAAC,mBAAmB,CAAA,EAAA,EAAK,IAAI,CAAC,uBAAuB,CAAA;;kBAE1Q,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,sBAAA,EAAyB,QAAQ,CAAA,EAAA,EAAK,kBAAkB,CAAA,EAAA,EAAK,YAAY,CAAA,MAAA,EAAS,gBAAgB,CAAA;;;;qBAIhM,IAAI,CAAC,qBAAqB,CAAA;kBAC7B,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,2BAAA,EAA8B,QAAQ,CAAA,EAAA,EAAK,IAAI,CAAA,EAAA,EAAK,gBAAgB,CAAA;;;qBAGlK,IAAI,CAAC,iBAAiB,CAAA;kBACzB,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAA,uBAAA,EAA0B,WAAW,CAAA,EAAA,EAAK,gBAAgB,CAAA;;;qBAGxJ,IAAI,CAAC,mBAAmB,CAAA;kBAC3B,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA;qBAC9F,CAAC;QAEd,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,IAAI,IAAI,CAAA,OAAA,EAAU,IAAI,CAAC,gBAAgB,CAAA;kBACjC,IAAI,CAAC,qBAAqB,CAAA;qBACvB,CAAC;QACd,CAAC;QAED,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACxB,IAAI,IAAI,CAAA;yBACK,IAAI,CAAC,aAAa,CAAA;sBACrB,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,qBAAqB,CAAA;;sBAEjI,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,qBAAqB,CAAA;6BAC1H,IAAI,CAAC,qBAAqB,CAAA;0BAC7B,IAAI,CAAC,qBAAqB,CAAA,IAAA,EAAO,IAAI,CAAC,qBAAqB,CAAA;;sBAE/D,IAAI,CAAC,qBAAqB,CAAA,WAAA,EAAc,IAAI,CAAC,qBAAqB,CAAA;yBAC/D,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,qCAAqC,CAAC,KAA6B,EAAE,UAAmB,EAAE,oBAAoB,GAAG,MAAM,EAAA;QAC1H,IAAI,SAAS,6MAAG,yCAAqC,CAAC,OAAO,CAAC;QAE9D,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,SAAS,8MAAG,wCAAqC,CAAC,OAAO,CAAC;QAC9D,CAAC;QAED,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAA;qBAC7D,IAAI,CAAC,aAAa,CAAA,EAAA,CAAI,CAAC;QAEpC,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAA,GAAA,EAAM,KAAK,CAAC,6BAA6B,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,gBAAgB,EAAE,UAAU,CAAC,GAAG,oBAAoB,CAAA,GAAA,CAAK,CAAC;QAC7K,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAA,GAAA,EAAM,KAAK,CAAC,0BAA0B,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,oBAAoB,CAAA,GAAA,CAAK,CAAC;QAC9J,CAAC;QAED,IAAI,IAAI,CAAA;oBACI,CAAC;QAEb,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAA,EAAA,EAAK,KAAK,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,GAAG,oBAAoB,CAAA,GAAA,CAAK,CAAC;QACtK,CAAC,MAAM,CAAC;YACJ,IAAI,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAA,GAAA,EAAM,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,oBAAoB,CAAA,GAAA,CAAK,CAAC;QACxJ,CAAC;QAED,IAAI,IAAI,CAAA,QAAA,CAAU,CAAC;QAEnB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG,CACI,YAAY,CAAC,KAA6B,EAAE,OAAe,EAAA;QAC9D,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;gBACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;oBACtB,IAAI,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,OAAO,CAAA,CAAA,EAAI,MAAM,CAAC,IAAI,CAAA,GAAA,CAAK,CAAC;gBAC7E,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,mBAAmB,GAAA;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE7C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO,UAAU,CAAC;QACtB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtB,MAAM,eAAe,GAAI,IAAI,CAAC,OAAuB,CAAC,eAAe,CAAC;YACtE,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,oCAAA,EAAuC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,yBAAA,EAC3F,IAAI,CAAC,OAAO,CAAC,QACjB,CAAA,yCAAA,EAA4C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAA,EAAA,EAAK,eAAe,CAAC,CAAC,CAAC,GAAG,GAAG,eAAe,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAA,IAAA,CAAM,CAAC;QAC3I,CAAC,MAAM,CAAC;YACJ,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,gCAAA,EAAmC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA,WAAA,CAAa,CAAC;QAC7G,CAAC;QACD,UAAU,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAA,2BAAA,EAA8B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAA,GAAA,CAAK,CAAC;QAEvG,OAAO,UAAU,CAAC;IACtB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC/C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAC3D,CAAC;QAED,mBAAmB,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAE7E,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,mBAAmB,CAAC,OAAO,IAAI,0KAAC,eAAY,CAAC,wBAAwB,EAAE,CAAC;YACxE,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YAChF,IAAI,mBAAmB,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBACrC,IAAI,CAAC,OAAO,8KAAG,eAAW,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAClF,CAAC,MAAM,CAAC;gBACJ,IAAI,CAAC,OAAO,2KAAG,UAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;QAED,IAAI,CAAC,wBAAwB,GAAG,mBAAmB,CAAC,wBAAwB,CAAC;QAE7E,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;CACJ;wJAxfU,aAAA,EAAA;2KAHN,yBAAA,AAAsB,EAAC,6BAA6B,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QAC/F,SAAS,EAAE;YAAE,OAAO,EAAE,IAAI;YAAE,MAAM,EAAE,IAAI;YAAE,YAAY,EAAE,0BAA0B,CAAC,kCAAkC;QAAA,CAAE;KAC1H,CAAC;4EACsC;6JA0f5C,gBAAA,AAAa,EAAC,oCAAoC,EAAE,0BAA0B,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2079, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/reflectionTextureBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/reflectionTextureBlock.ts"], "sourcesContent": ["import { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { NodeMaterial } from \"../../nodeMaterial\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport { InputBlock } from \"../Input/inputBlock\";\r\nimport { NodeMaterialSystemValues } from \"../../Enums/nodeMaterialSystemValues\";\r\nimport { ReflectionTextureBaseBlock } from \"./reflectionTextureBaseBlock\";\r\nimport type { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { Logger } from \"core/Misc/logger\";\r\n\r\n/**\r\n * Block used to read a reflection texture from a sampler\r\n */\r\nexport class ReflectionTextureBlock extends ReflectionTextureBaseBlock {\r\n    protected override _onGenerateOnlyFragmentCodeChanged(): boolean {\r\n        if (this.position.isConnected) {\r\n            this.generateOnlyFragmentCode = !this.generateOnlyFragmentCode;\r\n            Logger.Error(\"The position input must not be connected to be able to switch!\");\r\n            return false;\r\n        }\r\n\r\n        if (this.worldPosition.isConnected) {\r\n            this.generateOnlyFragmentCode = !this.generateOnlyFragmentCode;\r\n            Logger.Error(\"The worldPosition input must not be connected to be able to switch!\");\r\n            return false;\r\n        }\r\n\r\n        this._setTarget();\r\n\r\n        return true;\r\n    }\r\n\r\n    protected override _setTarget(): void {\r\n        super._setTarget();\r\n        this.getInputByName(\"position\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n        this.getInputByName(\"worldPosition\")!.target = this.generateOnlyFragmentCode ? NodeMaterialBlockTargets.Fragment : NodeMaterialBlockTargets.Vertex;\r\n    }\r\n\r\n    /**\r\n     * Create a new ReflectionTextureBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name);\r\n\r\n        this.registerInput(\"position\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Vertex);\r\n        this.registerInput(\"worldNormal\", NodeMaterialBlockConnectionPointTypes.Vector4, false, NodeMaterialBlockTargets.Fragment); // Flagging as fragment as the normal can be changed by fragment code\r\n        this.registerInput(\"world\", NodeMaterialBlockConnectionPointTypes.Matrix, false, NodeMaterialBlockTargets.Vertex);\r\n\r\n        this.registerInput(\"cameraPosition\", NodeMaterialBlockConnectionPointTypes.Vector3, false, NodeMaterialBlockTargets.Fragment);\r\n        this.registerInput(\"view\", NodeMaterialBlockConnectionPointTypes.Matrix, false, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this.registerOutput(\"rgb\", NodeMaterialBlockConnectionPointTypes.Color3, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"rgba\", NodeMaterialBlockConnectionPointTypes.Color4, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"r\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"g\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"b\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n        this.registerOutput(\"a\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Fragment);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Color3 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ReflectionTextureBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get position(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the world position input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the world normal input component\r\n     */\r\n    public get worldNormal(): NodeMaterialConnectionPoint {\r\n        return this._inputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the world input component\r\n     */\r\n    public get world(): NodeMaterialConnectionPoint {\r\n        return this._inputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the camera (or eye) position component\r\n     */\r\n    public get cameraPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the view input component\r\n     */\r\n    public get view(): NodeMaterialConnectionPoint {\r\n        return this._inputs[5];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgb output component\r\n     */\r\n    public get rgb(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the rgba output component\r\n     */\r\n    public get rgba(): NodeMaterialConnectionPoint {\r\n        return this._outputs[1];\r\n    }\r\n\r\n    /**\r\n     * Gets the r output component\r\n     */\r\n    public get r(): NodeMaterialConnectionPoint {\r\n        return this._outputs[2];\r\n    }\r\n\r\n    /**\r\n     * Gets the g output component\r\n     */\r\n    public get g(): NodeMaterialConnectionPoint {\r\n        return this._outputs[3];\r\n    }\r\n\r\n    /**\r\n     * Gets the b output component\r\n     */\r\n    public get b(): NodeMaterialConnectionPoint {\r\n        return this._outputs[4];\r\n    }\r\n\r\n    /**\r\n     * Gets the a output component\r\n     */\r\n    public get a(): NodeMaterialConnectionPoint {\r\n        return this._outputs[5];\r\n    }\r\n\r\n    public override autoConfigure(material: NodeMaterial, additionalFilteringInfo: (node: NodeMaterialBlock) => boolean = () => true) {\r\n        super.autoConfigure(material);\r\n\r\n        if (!this.cameraPosition.isConnected) {\r\n            let cameraPositionInput = material.getInputBlockByPredicate((b) => b.systemValue === NodeMaterialSystemValues.CameraPosition && additionalFilteringInfo(b));\r\n\r\n            if (!cameraPositionInput) {\r\n                cameraPositionInput = new InputBlock(\"cameraPosition\");\r\n                cameraPositionInput.setAsSystemValue(NodeMaterialSystemValues.CameraPosition);\r\n            }\r\n            cameraPositionInput.output.connectTo(this.cameraPosition);\r\n        }\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        if (!this.texture) {\r\n            state.compilationString += this.writeOutputs(state, `vec4${state.fSuffix}(0.)`);\r\n            return this;\r\n        }\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += this.handleVertexSide(state);\r\n            return this;\r\n        }\r\n\r\n        if (this.generateOnlyFragmentCode) {\r\n            state.compilationString += this.handleVertexSide(state);\r\n        }\r\n\r\n        this.handleFragmentSideInits(state);\r\n\r\n        const normalWUnit = state._getFreeVariableName(\"normalWUnit\");\r\n\r\n        state.compilationString += `${state._declareLocalVar(normalWUnit, NodeMaterialBlockConnectionPointTypes.Vector4)} = normalize(${this.worldNormal.associatedVariableName});\\n`;\r\n\r\n        state.compilationString += this.handleFragmentSideCodeReflectionCoords(state, normalWUnit);\r\n\r\n        state.compilationString += this.handleFragmentSideCodeReflectionColor(state, undefined, \"\");\r\n\r\n        state.compilationString += this.writeOutputs(state, this._reflectionColorName);\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ReflectionTextureBlock\", ReflectionTextureBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAGhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAChF,OAAO,EAAE,0BAA0B,EAAE,MAAM,8BAA8B,CAAC;AAE1E,OAAO,EAAE,MAAM,EAAE,mCAAyB;;;;;;;;AAKpC,MAAO,sBAAuB,kNAAQ,6BAA0B;IAC/C,kCAAkC,GAAA;QACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC5B,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC;kKAC/D,SAAM,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;YAC/E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,wBAAwB,GAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC;kKAC/D,SAAM,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC;YACpF,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,UAAU,GAAA;QACzB,KAAK,CAAC,UAAU,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,CAAC,UAAU,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,+LAAC,2BAAwB,CAAC,QAAQ,CAAC,CAAC,+LAAC,2BAAwB,CAAC,MAAM,CAAC;QAC9I,IAAI,CAAC,cAAc,CAAC,eAAe,CAAE,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,CAAC,CAAC,+LAAC,2BAAwB,CAAC,QAAQ,CAAC,CAAC,+LAAC,2BAAwB,CAAC,MAAM,CAAC;IACvJ,CAAC;IAED;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,CAAC,CAAC;QAEZ,IAAI,CAAC,aAAa,CAAC,UAAU,6MAAE,wCAAqC,CAAC,UAAU,EAAE,KAAK,gMAAE,2BAAwB,CAAC,MAAM,CAAC,CAAC;QACzH,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,mPAAqC,CAAC,OAAO,EAAE,KAAK,gMAAE,2BAAwB,CAAC,MAAM,CAAC,CAAC;QAC3H,IAAI,CAAC,aAAa,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC,qEAAqE;QACjM,IAAI,CAAC,aAAa,CAAC,OAAO,6MAAE,wCAAqC,CAAC,MAAM,EAAE,KAAK,gMAAE,2BAAwB,CAAC,MAAM,CAAC,CAAC;QAElH,IAAI,CAAC,aAAa,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,EAAE,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC9H,IAAI,CAAC,aAAa,CAAC,MAAM,6MAAE,wCAAqC,CAAC,MAAM,EAAE,KAAK,EAAE,yNAAwB,CAAC,QAAQ,CAAC,CAAC;QAEnH,IAAI,CAAC,cAAc,CAAC,KAAK,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC5G,IAAI,CAAC,cAAc,CAAC,MAAM,6MAAE,wCAAqC,CAAC,MAAM,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAC7G,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QACzG,IAAI,CAAC,cAAc,CAAC,GAAG,4MAAE,yCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,QAAQ,CAAC,CAAC;QAEzG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,4MACtD,wCAAqC,CAAC,MAAM,8MAAG,wCAAqC,CAAC,OAAO,8MAAG,wCAAqC,CAAC,OAAO,CAC/I,CAAC;IACN,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,wBAAwB,CAAC;IACpC,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,GAAG,GAAA;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG,CACH,IAAW,CAAC,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEe,aAAa,CAAC,QAAsB,EAAE,0BAAgE,GAAG,CAAG,CAAD,GAAK,EAAA;QAC5H,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;YACnC,IAAI,mBAAmB,GAAG,QAAQ,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,WAAW,mMAAK,2BAAwB,CAAC,cAAc,IAAI,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE5J,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvB,mBAAmB,GAAG,8LAAI,aAAU,CAAC,gBAAgB,CAAC,CAAC;gBACvD,mBAAmB,CAAC,gBAAgB,+LAAC,2BAAwB,CAAC,cAAc,CAAC,CAAC;YAClF,CAAC;YACD,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA,IAAA,EAAO,KAAK,CAAC,OAAO,CAAA,IAAA,CAAM,CAAC,CAAC;YAChF,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;QAEpC,MAAM,WAAW,GAAG,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAE9D,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,aAAA,EAAgB,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAA,IAAA,CAAM,CAAC;QAE9K,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,sCAAsC,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAE3F,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,qCAAqC,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;QAE5F,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAE/E,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;6JAED,gBAAA,AAAa,EAAC,gCAAgC,EAAE,sBAAsB,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2239, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/sceneDepthBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/sceneDepthBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport type { BaseTexture } from \"../../../Textures/baseTexture\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Scene } from \"../../../../scene\";\r\nimport type { InputBlock } from \"../Input/inputBlock\";\r\nimport { editableInPropertyPage, PropertyTypeForEdition } from \"../../../../Decorators/nodeDecorator\";\r\nimport type { Effect } from \"../../../effect\";\r\n\r\nimport type { NodeMaterial } from \"../../nodeMaterial\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\nimport { Constants } from \"core/Engines/constants\";\r\n\r\n/**\r\n * Block used to retrieve the depth (zbuffer) of the scene\r\n * @since 5.0.0\r\n */\r\nexport class SceneDepthBlock extends NodeMaterialBlock {\r\n    private _samplerName: string;\r\n    private _mainUVName: string;\r\n    private _tempTextureRead: string;\r\n\r\n    /**\r\n     * Defines if the depth renderer should be setup in non linear mode\r\n     */\r\n    @editableInPropertyPage(\"Use non linear depth\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        embedded: true,\r\n        notifiers: {\r\n            activatePreviewCommand: true,\r\n            callback: (scene, block) => {\r\n                const sceneDepthBlock = block as SceneDepthBlock;\r\n                let retVal = false;\r\n                if (sceneDepthBlock.useNonLinearDepth) {\r\n                    sceneDepthBlock.storeCameraSpaceZ = false;\r\n                    retVal = true;\r\n                }\r\n                if (scene) {\r\n                    scene.disableDepthRenderer();\r\n                }\r\n                return retVal;\r\n            },\r\n        },\r\n    })\r\n    public useNonLinearDepth = false;\r\n\r\n    /**\r\n     * Defines if the depth renderer should be setup in camera space Z mode (if set, useNonLinearDepth has no effect)\r\n     */\r\n    @editableInPropertyPage(\"Store Camera space Z\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: {\r\n            activatePreviewCommand: true,\r\n            callback: (scene, block) => {\r\n                const sceneDepthBlock = block as SceneDepthBlock;\r\n                let retVal = false;\r\n                if (sceneDepthBlock.storeCameraSpaceZ) {\r\n                    sceneDepthBlock.useNonLinearDepth = false;\r\n                    retVal = true;\r\n                }\r\n                if (scene) {\r\n                    scene.disableDepthRenderer();\r\n                }\r\n                return retVal;\r\n            },\r\n        },\r\n    })\r\n    public storeCameraSpaceZ = false;\r\n\r\n    /**\r\n     * Defines if the depth renderer should be setup in full 32 bits float mode\r\n     */\r\n    @editableInPropertyPage(\"Force 32 bits float\", PropertyTypeForEdition.Boolean, \"ADVANCED\", {\r\n        notifiers: { activatePreviewCommand: true, callback: (scene) => scene?.disableDepthRenderer() },\r\n    })\r\n    public force32itsFloat = false;\r\n\r\n    /**\r\n     * Create a new SceneDepthBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this._isUnique = true;\r\n\r\n        this.registerInput(\"uv\", NodeMaterialBlockConnectionPointTypes.AutoDetect, false, NodeMaterialBlockTargets.VertexAndFragment);\r\n\r\n        this.registerOutput(\"depth\", NodeMaterialBlockConnectionPointTypes.Float, NodeMaterialBlockTargets.Neutral);\r\n\r\n        this._inputs[0].addExcludedConnectionPointFromAllowedTypes(\r\n            NodeMaterialBlockConnectionPointTypes.Vector2 | NodeMaterialBlockConnectionPointTypes.Vector3 | NodeMaterialBlockConnectionPointTypes.Vector4\r\n        );\r\n\r\n        this._inputs[0]._prioritizeVertex = false;\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"SceneDepthBlock\";\r\n    }\r\n\r\n    /**\r\n     * Gets the uv input component\r\n     */\r\n    public get uv(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    /**\r\n     * Gets the depth output component\r\n     */\r\n    public get depth(): NodeMaterialConnectionPoint {\r\n        return this._outputs[0];\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"textureSampler\");\r\n    }\r\n\r\n    public override get target() {\r\n        if (!this.uv.isConnected) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        if (this.uv.sourceBlock!.isInput) {\r\n            return NodeMaterialBlockTargets.VertexAndFragment;\r\n        }\r\n\r\n        return NodeMaterialBlockTargets.Fragment;\r\n    }\r\n\r\n    private _getTexture(scene: Scene): BaseTexture {\r\n        const depthRenderer = scene.enableDepthRenderer(undefined, this.useNonLinearDepth, this.force32itsFloat, undefined, this.storeCameraSpaceZ);\r\n\r\n        return depthRenderer.getDepthMap();\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial) {\r\n        const texture = this._getTexture(nodeMaterial.getScene());\r\n\r\n        effect.setTexture(this._samplerName, texture);\r\n    }\r\n\r\n    private _injectVertexCode(state: NodeMaterialBuildState) {\r\n        const uvInput = this.uv;\r\n\r\n        if (uvInput.connectedPoint!.ownerBlock.isInput) {\r\n            const uvInputOwnerBlock = uvInput.connectedPoint!.ownerBlock as InputBlock;\r\n\r\n            if (!uvInputOwnerBlock.isAttribute) {\r\n                state._emitUniformFromString(\r\n                    uvInput.associatedVariableName,\r\n                    uvInput.type === NodeMaterialBlockConnectionPointTypes.Vector3\r\n                        ? NodeMaterialBlockConnectionPointTypes.Vector3\r\n                        : uvInput.type === NodeMaterialBlockConnectionPointTypes.Vector4\r\n                          ? NodeMaterialBlockConnectionPointTypes.Vector4\r\n                          : NodeMaterialBlockConnectionPointTypes.Vector2\r\n                );\r\n            }\r\n        }\r\n\r\n        this._mainUVName = \"vMain\" + uvInput.associatedVariableName;\r\n\r\n        state._emitVaryingFromString(this._mainUVName, NodeMaterialBlockConnectionPointTypes.Vector2);\r\n\r\n        state.compilationString += `${this._mainUVName} = ${uvInput.associatedVariableName}.xy;\\n`;\r\n\r\n        if (!this._outputs.some((o) => o.isConnectedInVertexShader)) {\r\n            return;\r\n        }\r\n\r\n        this._writeTextureRead(state, true);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                this._writeOutput(state, output, \"r\", true);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _writeTextureRead(state: NodeMaterialBuildState, vertexMode = false) {\r\n        const uvInput = this.uv;\r\n\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n            const textureReadFunc =\r\n                state.shaderLanguage === ShaderLanguage.GLSL\r\n                    ? `texture2D(${this._samplerName},`\r\n                    : `textureSampleLevel(${this._samplerName}, ${this._samplerName + Constants.AUTOSAMPLERSUFFIX},`;\r\n\r\n            const complement = state.shaderLanguage === ShaderLanguage.GLSL ? \"\" : \", 0\";\r\n\r\n            state.compilationString += `${state._declareLocalVar(this._tempTextureRead, NodeMaterialBlockConnectionPointTypes.Vector4)}=  ${textureReadFunc} ${uvInput.associatedVariableName}.xy${complement});\\n`;\r\n            return;\r\n        }\r\n\r\n        const textureReadFunc =\r\n            state.shaderLanguage === ShaderLanguage.GLSL\r\n                ? `texture2D(${this._samplerName},`\r\n                : `textureSample(${this._samplerName}, ${this._samplerName + Constants.AUTOSAMPLERSUFFIX},`;\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `${state._declareLocalVar(this._tempTextureRead, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${textureReadFunc} ${uvInput.associatedVariableName}.xy);\\n`;\r\n            return;\r\n        }\r\n\r\n        state.compilationString += `${state._declareLocalVar(this._tempTextureRead, NodeMaterialBlockConnectionPointTypes.Vector4)} = ${textureReadFunc} ${this._mainUVName});\\n`;\r\n    }\r\n\r\n    private _writeOutput(state: NodeMaterialBuildState, output: NodeMaterialConnectionPoint, swizzle: string, vertexMode = false) {\r\n        if (vertexMode) {\r\n            if (state.target === NodeMaterialBlockTargets.Fragment) {\r\n                return;\r\n            }\r\n\r\n            state.compilationString += `${state._declareOutput(output)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n            return;\r\n        }\r\n\r\n        if (this.uv.ownerBlock.target === NodeMaterialBlockTargets.Fragment) {\r\n            state.compilationString += `${state._declareOutput(output)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n            return;\r\n        }\r\n\r\n        state.compilationString += `${state._declareOutput(output)} = ${this._tempTextureRead}.${swizzle};\\n`;\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        this._samplerName = state._getFreeVariableName(this.name + \"Sampler\");\r\n        this._tempTextureRead = state._getFreeVariableName(\"tempTextureRead\");\r\n\r\n        if (state.sharedData.bindableBlocks.indexOf(this) < 0) {\r\n            state.sharedData.bindableBlocks.push(this);\r\n        }\r\n\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            // Vertex\r\n            state._emit2DSampler(this._samplerName);\r\n            this._injectVertexCode(state);\r\n            return;\r\n        }\r\n\r\n        // Fragment\r\n        if (!this._outputs.some((o) => o.isConnectedInFragmentShader)) {\r\n            return;\r\n        }\r\n\r\n        state._emit2DSampler(this._samplerName);\r\n\r\n        this._writeTextureRead(state);\r\n\r\n        for (const output of this._outputs) {\r\n            if (output.hasEndpoints) {\r\n                this._writeOutput(state, output, \"r\");\r\n            }\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public override serialize(): any {\r\n        const serializationObject = super.serialize();\r\n\r\n        serializationObject.useNonLinearDepth = this.useNonLinearDepth;\r\n        serializationObject.storeCameraSpaceZ = this.storeCameraSpaceZ;\r\n        serializationObject.force32itsFloat = this.force32itsFloat;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    public override _deserialize(serializationObject: any, scene: Scene, rootUrl: string) {\r\n        super._deserialize(serializationObject, scene, rootUrl);\r\n\r\n        this.useNonLinearDepth = serializationObject.useNonLinearDepth;\r\n        this.storeCameraSpaceZ = !!serializationObject.storeCameraSpaceZ;\r\n        this.force32itsFloat = serializationObject.force32itsFloat;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SceneDepthBlock\", SceneDepthBlock);\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAGhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAG3D,OAAO,EAAE,sBAAsB,EAA0B,MAAM,sCAAsC,CAAC;;;;;;;AAWhG,MAAO,eAAgB,uLAAQ,oBAAiB;IA0DlD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QA1D5D;;WAEG,CAmBI,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAEjC;;WAEG,CAkBI,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QAEjC;;WAEG,CAII,IAAA,CAAA,eAAe,GAAG,KAAK,CAAC;QAS3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,IAAI,6MAAE,wCAAqC,CAAC,UAAU,EAAE,KAAK,gMAAE,2BAAwB,CAAC,iBAAiB,CAAC,CAAC;QAE9H,IAAI,CAAC,cAAc,CAAC,OAAO,6MAAE,wCAAqC,CAAC,KAAK,gMAAE,2BAAwB,CAAC,OAAO,CAAC,CAAC;QAE5G,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,0CAA0C,2MACtD,yCAAqC,CAAC,OAAO,8MAAG,wCAAqC,CAAC,OAAO,8MAAG,wCAAqC,CAAC,OAAO,CAChJ,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC9C,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,EAAE,GAAA;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;IACjD,CAAC;IAED,IAAoB,MAAM,GAAA;QACtB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC;YACvB,qMAAO,2BAAwB,CAAC,iBAAiB,CAAC;QACtD,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,WAAY,CAAC,OAAO,EAAE,CAAC;YAC/B,qMAAO,2BAAwB,CAAC,iBAAiB,CAAC;QACtD,CAAC;QAED,oMAAO,4BAAwB,CAAC,QAAQ,CAAC;IAC7C,CAAC;IAEO,WAAW,CAAC,KAAY,EAAA;QAC5B,MAAM,aAAa,GAAG,KAAK,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAE5I,OAAO,aAAa,CAAC,WAAW,EAAE,CAAC;IACvC,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAA;QAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE1D,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAA;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,OAAO,CAAC,cAAe,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC7C,MAAM,iBAAiB,GAAG,OAAO,CAAC,cAAe,CAAC,UAAwB,CAAC;YAE3E,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;gBACjC,KAAK,CAAC,sBAAsB,CACxB,OAAO,CAAC,sBAAsB,EAC9B,OAAO,CAAC,IAAI,gNAAK,wCAAqC,CAAC,OAAO,8MACxD,wCAAqC,CAAC,OAAO,GAC7C,OAAO,CAAC,IAAI,gNAAK,wCAAqC,CAAC,OAAO,8MAC5D,wCAAqC,CAAC,OAAO,GAC7C,mPAAqC,CAAC,OAAO,CACxD,CAAC;YACN,CAAC;QACL,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC,sBAAsB,CAAC;QAE5D,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;QAE9F,KAAK,CAAC,iBAAiB,IAAI,GAAG,IAAI,CAAC,WAAW,CAAA,GAAA,EAAM,OAAO,CAAC,sBAAsB,CAAA,MAAA,CAAQ,CAAC;QAE3F,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,yBAAyB,CAAC,EAAE,CAAC;YAC1D,OAAO;QACX,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAEpC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,KAA6B,EAAE,UAAU,GAAG,KAAK,EAAA;QACvE,MAAM,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;QAExB,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;gBACrD,OAAO;YACX,CAAC;YACD,MAAM,eAAe,GACjB,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,IACtC,CAAA,UAAA,EAAa,IAAI,CAAC,YAAY,CAAA,CAAA,CAAG,GACjC,CAAA,mBAAA,EAAsB,IAAI,CAAC,YAAY,CAAA,EAAA,EAAK,IAAI,CAAC,YAAY,GAAG,CAAA,OAAA,CAAS,CAAC,CAAA,CAAA,eAAiB,GAAG,CAAC;YAEzG,MAAM,UAAU,GAAG,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,CAAC,CAAC,EAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAE7E,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,eAAe,CAAA,CAAA,EAAI,OAAO,CAAC,sBAAsB,CAAA,GAAA,EAAM,UAAU,CAAA,IAAA,CAAM,CAAC;YACxM,OAAO;QACX,CAAC;QAED,MAAM,eAAe,GACjB,KAAK,CAAC,cAAc,KAAA,EAAA,uBAAA,EAAwB,IACtC,CAAA,UAAA,EAAa,IAAI,CAAC,YAAY,CAAA,CAAA,CAAG,GACjC,CAAA,cAAA,EAAiB,IAAI,CAAC,YAAY,CAAA,EAAA,EAAK,IAAI,CAAC,YAAY,GAAG,CAAA,OAAA,CAAS,CAAC,CAAA,CAAA,eAAiB,GAAG,CAAC;QAEpG,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YAClE,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,eAAe,CAAA,CAAA,EAAI,OAAO,CAAC,sBAAsB,CAAA,OAAA,CAAS,CAAC;YAC3L,OAAO;QACX,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAA,GAAA,EAAM,eAAe,CAAA,CAAA,EAAI,IAAI,CAAC,WAAW,CAAA,IAAA,CAAM,CAAC;IAC9K,CAAC;IAEO,YAAY,CAAC,KAA6B,EAAE,MAAmC,EAAE,OAAe,EAAE,UAAU,GAAG,KAAK,EAAA;QACxH,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;gBACrD,OAAO;YACX,CAAC;YAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,gBAAgB,CAAA,CAAA,EAAI,OAAO,CAAA,GAAA,CAAK,CAAC;YACtG,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YAClE,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,gBAAgB,CAAA,CAAA,EAAI,OAAO,CAAA,GAAA,CAAK,CAAC;YACtG,OAAO;QACX,CAAC;QAED,KAAK,CAAC,iBAAiB,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,EAAM,IAAI,CAAC,gBAAgB,CAAA,CAAA,EAAI,OAAO,CAAA,GAAA,CAAK,CAAC;IAC1G,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;QACtE,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;QAEtE,IAAI,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACpD,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,mMAAK,2BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,SAAS;YACT,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACxC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,2BAA2B,CAAC,EAAE,CAAC;YAC5D,OAAO;QACX,CAAC;QAED,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAExC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAE9B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAE,CAAC;YACjC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEe,SAAS,GAAA;QACrB,MAAM,mBAAmB,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QAE9C,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAE3D,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAEe,YAAY,CAAC,mBAAwB,EAAE,KAAY,EAAE,OAAe,EAAA;QAChF,KAAK,CAAC,YAAY,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAExD,IAAI,CAAC,iBAAiB,GAAG,mBAAmB,CAAC,iBAAiB,CAAC;QAC/D,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;QACjE,IAAI,CAAC,eAAe,GAAG,mBAAmB,CAAC,eAAe,CAAC;IAC/D,CAAC;CACJ;wJApPU,aAAA,EAAA;2KAlBN,yBAAA,AAAsB,EAAC,sBAAsB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QACxF,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE;YACP,sBAAsB,EAAE,IAAI;YAC5B,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,eAAe,GAAG,KAAwB,CAAC;gBACjD,IAAI,MAAM,GAAG,KAAK,CAAC;gBACnB,IAAI,eAAe,CAAC,iBAAiB,EAAE,CAAC;oBACpC,eAAe,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAC1C,MAAM,GAAG,IAAI,CAAC;gBAClB,CAAC;gBACD,IAAI,KAAK,EAAE,CAAC;oBACR,KAAK,CAAC,oBAAoB,EAAE,CAAC;gBACjC,CAAC;gBACD,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ;KACJ,CAAC;0DAC+B;wJAsB1B,aAAA,EAAA;2KAjBN,yBAAA,AAAsB,EAAC,sBAAsB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QACxF,SAAS,EAAE;YACP,sBAAsB,EAAE,IAAI;YAC5B,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,eAAe,GAAG,KAAwB,CAAC;gBACjD,IAAI,MAAM,GAAG,KAAK,CAAC;gBACnB,IAAI,eAAe,CAAC,iBAAiB,EAAE,CAAC;oBACpC,eAAe,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAC1C,MAAM,GAAG,IAAI,CAAC;gBAClB,CAAC;gBACD,IAAI,KAAK,EAAE,CAAC;oBACR,KAAK,CAAC,oBAAoB,EAAE,CAAC;gBACjC,CAAC;gBACD,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ;KACJ,CAAC;0DAC+B;CAQ1B,oKAAA,EAAA;2KAHN,yBAAA,AAAsB,EAAC,qBAAqB,EAAA,EAAA,kCAAA,KAAkC,UAAU,EAAE;QACvF,SAAS,EAAE;YAAE,sBAAsB,EAAE,IAAI;YAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAG,CAAD,IAAM,EAAE,oBAAoB,EAAE;QAAA,CAAE;KAClG,CAAC;wDAC6B;6JAwNnC,gBAAA,AAAa,EAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2459, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/clipPlanesBlock.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/clipPlanesBlock.ts"], "sourcesContent": ["import { NodeMaterialBlock } from \"../../nodeMaterialBlock\";\r\nimport { NodeMaterialBlockConnectionPointTypes } from \"../../Enums/nodeMaterialBlockConnectionPointTypes\";\r\nimport type { NodeMaterialBuildState } from \"../../nodeMaterialBuildState\";\r\nimport { NodeMaterialBlockTargets } from \"../../Enums/nodeMaterialBlockTargets\";\r\nimport type { NodeMaterialConnectionPoint } from \"../../nodeMaterialBlockConnectionPoint\";\r\nimport { RegisterClass } from \"../../../../Misc/typeStore\";\r\nimport type { Effect } from \"../../../effect\";\r\nimport type { NodeMaterial, NodeMaterialDefines } from \"../../nodeMaterial\";\r\nimport type { Mesh } from \"../../../../Meshes/mesh\";\r\nimport type { AbstractMesh } from \"../../../../Meshes/abstractMesh\";\r\nimport { BindClipPlane } from \"../../../../Materials/clipPlaneMaterialHelper\";\r\nimport { ShaderLanguage } from \"core/Materials/shaderLanguage\";\r\n/**\r\n * Block used to implement clip planes\r\n */\r\nexport class ClipPlanesBlock extends NodeMaterialBlock {\r\n    /**\r\n     * Create a new ClipPlanesBlock\r\n     * @param name defines the block name\r\n     */\r\n    public constructor(name: string) {\r\n        super(name, NodeMaterialBlockTargets.VertexAndFragment, true);\r\n\r\n        this.registerInput(\"worldPosition\", NodeMaterialBlockConnectionPointTypes.Vector4, false);\r\n    }\r\n\r\n    /**\r\n     * Gets the current class name\r\n     * @returns the class name\r\n     */\r\n    public override getClassName() {\r\n        return \"ClipPlanesBlock\";\r\n    }\r\n\r\n    /**\r\n     * Initialize the block and prepare the context for build\r\n     * @param state defines the state that will be used for the build\r\n     */\r\n    public override initialize(state: NodeMaterialBuildState) {\r\n        state._excludeVariableName(\"vClipPlane\");\r\n        state._excludeVariableName(\"fClipDistance\");\r\n        state._excludeVariableName(\"vClipPlane2\");\r\n        state._excludeVariableName(\"fClipDistance2\");\r\n        state._excludeVariableName(\"vClipPlane3\");\r\n        state._excludeVariableName(\"fClipDistance3\");\r\n        state._excludeVariableName(\"vClipPlane4\");\r\n        state._excludeVariableName(\"fClipDistance4\");\r\n        state._excludeVariableName(\"vClipPlane5\");\r\n        state._excludeVariableName(\"fClipDistance5\");\r\n        state._excludeVariableName(\"vClipPlane6\");\r\n        state._excludeVariableName(\"fClipDistance6\");\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync(state.shaderLanguage);\r\n    }\r\n\r\n    private async _initShaderSourceAsync(shaderLanguage: ShaderLanguage) {\r\n        this._codeIsReady = false;\r\n\r\n        if (shaderLanguage === ShaderLanguage.WGSL) {\r\n            await Promise.all([\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/clipPlaneFragment\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/clipPlaneFragmentDeclaration\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/clipPlaneVertex\"),\r\n                import(\"../../../../ShadersWGSL/ShadersInclude/clipPlaneVertexDeclaration\"),\r\n            ]);\r\n        } else {\r\n            await Promise.all([\r\n                import(\"../../../../Shaders/ShadersInclude/clipPlaneFragment\"),\r\n                import(\"../../../../Shaders/ShadersInclude/clipPlaneFragmentDeclaration\"),\r\n                import(\"../../../../Shaders/ShadersInclude/clipPlaneVertex\"),\r\n                import(\"../../../../Shaders/ShadersInclude/clipPlaneVertexDeclaration\"),\r\n            ]);\r\n        }\r\n\r\n        this._codeIsReady = true;\r\n        this.onCodeIsReadyObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Gets the worldPosition input component\r\n     */\r\n    public get worldPosition(): NodeMaterialConnectionPoint {\r\n        return this._inputs[0];\r\n    }\r\n\r\n    public override get target() {\r\n        return NodeMaterialBlockTargets.VertexAndFragment;\r\n    }\r\n\r\n    public override set target(value: NodeMaterialBlockTargets) {}\r\n\r\n    public override prepareDefines(defines: NodeMaterialDefines, nodeMaterial: NodeMaterial, mesh?: AbstractMesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        const scene = mesh.getScene();\r\n\r\n        const useClipPlane1 = (nodeMaterial.clipPlane ?? scene.clipPlane) ? true : false;\r\n        const useClipPlane2 = (nodeMaterial.clipPlane2 ?? scene.clipPlane2) ? true : false;\r\n        const useClipPlane3 = (nodeMaterial.clipPlane3 ?? scene.clipPlane3) ? true : false;\r\n        const useClipPlane4 = (nodeMaterial.clipPlane4 ?? scene.clipPlane4) ? true : false;\r\n        const useClipPlane5 = (nodeMaterial.clipPlane5 ?? scene.clipPlane5) ? true : false;\r\n        const useClipPlane6 = (nodeMaterial.clipPlane6 ?? scene.clipPlane6) ? true : false;\r\n\r\n        defines.setValue(\"CLIPPLANE\", useClipPlane1, true);\r\n        defines.setValue(\"CLIPPLANE2\", useClipPlane2, true);\r\n        defines.setValue(\"CLIPPLANE3\", useClipPlane3, true);\r\n        defines.setValue(\"CLIPPLANE4\", useClipPlane4, true);\r\n        defines.setValue(\"CLIPPLANE5\", useClipPlane5, true);\r\n        defines.setValue(\"CLIPPLANE6\", useClipPlane6, true);\r\n    }\r\n\r\n    public override bind(effect: Effect, nodeMaterial: NodeMaterial, mesh?: Mesh) {\r\n        if (!mesh) {\r\n            return;\r\n        }\r\n\r\n        const scene = mesh.getScene();\r\n\r\n        BindClipPlane(effect, nodeMaterial, scene);\r\n    }\r\n\r\n    protected override _buildBlock(state: NodeMaterialBuildState) {\r\n        super._buildBlock(state);\r\n\r\n        const comments = `//${this.name}`;\r\n        if (state.target !== NodeMaterialBlockTargets.Fragment) {\r\n            // Vertex\r\n            const worldPos = this.worldPosition;\r\n\r\n            state._emitFunctionFromInclude(\"clipPlaneVertexDeclaration\", comments, {\r\n                replaceStrings: [{ search: /uniform vec4 vClipPlane\\d*;/g, replace: \"\" }],\r\n            });\r\n            state.compilationString += state._emitCodeFromInclude(\"clipPlaneVertex\", comments, {\r\n                replaceStrings: [{ search: /worldPos/g, replace: worldPos.associatedVariableName }],\r\n            });\r\n\r\n            state._emitUniformFromString(\"vClipPlane\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n            state._emitUniformFromString(\"vClipPlane2\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n            state._emitUniformFromString(\"vClipPlane3\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n            state._emitUniformFromString(\"vClipPlane4\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n            state._emitUniformFromString(\"vClipPlane5\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n            state._emitUniformFromString(\"vClipPlane6\", NodeMaterialBlockConnectionPointTypes.Vector4);\r\n\r\n            return;\r\n        }\r\n\r\n        // Fragment\r\n        state.sharedData.bindableBlocks.push(this);\r\n        state.sharedData.blocksWithDefines.push(this);\r\n\r\n        state._emitFunctionFromInclude(\"clipPlaneFragmentDeclaration\", comments);\r\n        state.compilationString += state._emitCodeFromInclude(\"clipPlaneFragment\", comments);\r\n\r\n        return this;\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.ClipPlanesBlock\", ClipPlanesBlock);\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qCAAqC,EAAE,MAAM,mDAAmD,CAAC;AAE1G,OAAO,EAAE,wBAAwB,EAAE,MAAM,sCAAsC,CAAC;AAEhF,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAK3D,OAAO,EAAE,aAAa,EAAE,MAAM,+CAA+C,CAAC;;;;;;AAKxE,MAAO,eAAgB,uLAAQ,oBAAiB;IAClD;;;OAGG,CACH,YAAmB,IAAY,CAAA;QAC3B,KAAK,CAAC,IAAI,gMAAE,2BAAwB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QAE9D,IAAI,CAAC,aAAa,CAAC,eAAe,EAAE,mPAAqC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC9F,CAAC;IAED;;;OAGG,CACa,YAAY,GAAA;QACxB,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;;OAGG,CACa,UAAU,CAAC,KAA6B,EAAA;QACpD,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;QACzC,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC5C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,KAAK,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1C,KAAK,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QAE7C,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IACtD,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,cAA8B,EAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAE1B,IAAI,cAAc,KAAA,EAAA,uBAAA,EAAwB,GAAE,CAAC;YACzC,MAAM,OAAO,CAAC,GAAG,CAAC;;;;;aAKjB,CAAC,CAAC;QACP,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC;;;;;aAKjB,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,IAAoB,MAAM,GAAA;QACtB,qMAAO,2BAAwB,CAAC,iBAAiB,CAAC;IACtD,CAAC;IAED,IAAoB,MAAM,CAAC,KAA+B,EAAA,CAAG,CAAC;IAE9C,cAAc,CAAC,OAA4B,EAAE,YAA0B,EAAE,IAAmB,EAAA;QACxG,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAE9B,MAAM,aAAa,GAAG,AAAC,YAAY,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACjF,MAAM,aAAa,GAAG,AAAC,YAAY,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACnF,MAAM,aAAa,GAAG,AAAC,YAAY,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACnF,MAAM,aAAa,GAAG,AAAC,YAAY,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACnF,MAAM,aAAa,GAAG,AAAC,YAAY,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QACnF,MAAM,aAAa,GAAG,AAAC,YAAY,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,AAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAEnF,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;QACnD,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;IACxD,CAAC;IAEe,IAAI,CAAC,MAAc,EAAE,YAA0B,EAAE,IAAW,EAAA;QACxE,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAE9B,4LAAA,AAAa,EAAC,MAAM,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;IAEkB,WAAW,CAAC,KAA6B,EAAA;QACxD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEzB,MAAM,QAAQ,GAAG,CAAA,EAAA,EAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,IAAI,KAAK,CAAC,MAAM,kMAAK,4BAAwB,CAAC,QAAQ,EAAE,CAAC;YACrD,SAAS;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;YAEpC,KAAK,CAAC,wBAAwB,CAAC,4BAA4B,EAAE,QAAQ,EAAE;gBACnE,cAAc,EAAE;oBAAC;wBAAE,MAAM,EAAE,8BAA8B;wBAAE,OAAO,EAAE,EAAE;oBAAA,CAAE;iBAAC;aAC5E,CAAC,CAAC;YACH,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,iBAAiB,EAAE,QAAQ,EAAE;gBAC/E,cAAc,EAAE;oBAAC;wBAAE,MAAM,EAAE,WAAW;wBAAE,OAAO,EAAE,QAAQ,CAAC,sBAAsB;oBAAA,CAAE;iBAAC;aACtF,CAAC,CAAC;YAEH,KAAK,CAAC,sBAAsB,CAAC,YAAY,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;YAC1F,KAAK,CAAC,sBAAsB,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;YAC3F,KAAK,CAAC,sBAAsB,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;YAC3F,KAAK,CAAC,sBAAsB,CAAC,aAAa,EAAE,mPAAqC,CAAC,OAAO,CAAC,CAAC;YAC3F,KAAK,CAAC,sBAAsB,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;YAC3F,KAAK,CAAC,sBAAsB,CAAC,aAAa,6MAAE,wCAAqC,CAAC,OAAO,CAAC,CAAC;YAE3F,OAAO;QACX,CAAC;QAED,WAAW;QACX,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9C,KAAK,CAAC,wBAAwB,CAAC,8BAA8B,EAAE,QAAQ,CAAC,CAAC;QACzE,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;QAErF,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;6JAED,gBAAA,AAAa,EAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2602, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/Dual/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Materials/Node/Blocks/Dual/index.ts"], "sourcesContent": ["export * from \"./fogBlock\";\r\nexport * from \"./lightBlock\";\r\nexport * from \"./textureBlock\";\r\nexport * from \"./reflectionTextureBaseBlock\";\r\nexport * from \"./reflectionTextureBlock\";\r\nexport * from \"./currentScreenBlock\";\r\nexport * from \"./sceneDepthBlock\";\r\nexport * from \"./imageSourceBlock\";\r\nexport * from \"./clipPlanesBlock\";\r\nexport * from \"./smartFilterTextureBlock\";\r\n\r\n// async-loaded shaders\r\n\r\n// clipPlaneBlock\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/clipPlaneFragment\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/clipPlaneFragmentDeclaration\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/clipPlaneVertex\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/clipPlaneVertexDeclaration\";\r\nexport * from \"../../../../Shaders/ShadersInclude/clipPlaneFragment\";\r\nexport * from \"../../../../Shaders/ShadersInclude/clipPlaneFragmentDeclaration\";\r\nexport * from \"../../../../Shaders/ShadersInclude/clipPlaneVertex\";\r\nexport * from \"../../../../Shaders/ShadersInclude/clipPlaneVertexDeclaration\";\r\n\r\n// fogBlock\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/fogFragmentDeclaration\";\r\nexport * from \"../../../../Shaders/ShadersInclude/fogFragmentDeclaration\";\r\n\r\n// lightBlock\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/lightFragment\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/lightUboDeclaration\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/lightVxUboDeclaration\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/helperFunctions\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/lightsFragmentFunctions\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/shadowsFragmentFunctions\";\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/shadowsVertex\";\r\nexport * from \"../../../../Shaders/ShadersInclude/lightFragmentDeclaration\";\r\nexport * from \"../../../../Shaders/ShadersInclude/lightFragment\";\r\nexport * from \"../../../../Shaders/ShadersInclude/lightUboDeclaration\";\r\nexport * from \"../../../../Shaders/ShadersInclude/lightVxUboDeclaration\";\r\nexport * from \"../../../../Shaders/ShadersInclude/lightVxFragmentDeclaration\";\r\nexport * from \"../../../../Shaders/ShadersInclude/helperFunctions\";\r\nexport * from \"../../../../Shaders/ShadersInclude/lightsFragmentFunctions\";\r\nexport * from \"../../../../Shaders/ShadersInclude/shadowsFragmentFunctions\";\r\nexport * from \"../../../../Shaders/ShadersInclude/shadowsVertex\";\r\n\r\n// reflectionTextureBlock\r\nexport * from \"../../../../ShadersWGSL/ShadersInclude/reflectionFunction\";\r\nexport * from \"../../../../Shaders/ShadersInclude/reflectionFunction\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,YAAY,CAAC;AAC3B,cAAc,cAAc,CAAC;AAC7B,cAAc,gBAAgB,CAAC;AAC/B,cAAc,8BAA8B,CAAC;AAC7C,cAAc,0BAA0B,CAAC;AACzC,cAAc,sBAAsB,CAAC;AACrC,cAAc,mBAAmB,CAAC;AAClC,cAAc,oBAAoB,CAAC;AACnC,cAAc,mBAAmB,CAAC;AAClC,cAAc,2BAA2B,CAAC;AAE1C,uBAAuB;AAEvB,iBAAiB;AACjB,cAAc,0DAA0D,CAAC;AACzE,cAAc,qEAAqE,CAAC;AACpF,cAAc,wDAAwD,CAAC;AACvE,cAAc,mEAAmE,CAAC;AAClF,cAAc,sDAAsD,CAAC;AACrE,cAAc,iEAAiE,CAAC;AAChF,cAAc,oDAAoD,CAAC;AACnE,cAAc,+DAA+D,CAAC;AAE9E,WAAW;AACX,cAAc,+DAA+D,CAAC;AAC9E,cAAc,2DAA2D,CAAC;AAE1E,aAAa;AACb,cAAc,sDAAsD,CAAC;AACrE,cAAc,4DAA4D,CAAC;AAC3E,cAAc,8DAA8D,CAAC;AAC7E,cAAc,wDAAwD,CAAC;AACvE,cAAc,gEAAgE,CAAC;AAC/E,cAAc,iEAAiE,CAAC;AAChF,cAAc,sDAAsD,CAAC;AACrE,cAAc,6DAA6D,CAAC;AAC5E,cAAc,kDAAkD,CAAC;AACjE,cAAc,wDAAwD,CAAC;AACvE,cAAc,0DAA0D,CAAC;AACzE,cAAc,+DAA+D,CAAC;AAC9E,cAAc,oDAAoD,CAAC;AACnE,cAAc,4DAA4D,CAAC;AAC3E,cAAc,6DAA6D,CAAC;AAC5E,cAAc,kDAAkD,CAAC;AAEjE,yBAAyB;AACzB,cAAc,2DAA2D,CAAC;AAC1E,cAAc,uDAAuD,CAAC", "debugId": null}}]}