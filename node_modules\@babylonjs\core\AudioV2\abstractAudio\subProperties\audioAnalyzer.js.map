{"version": 3, "file": "audioAnalyzer.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/abstractAudio/subProperties/audioAnalyzer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAG9C,OAAO,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,MAAM,yDAAyD,CAAC;AAExH,OAAO,EAAE,wBAAwB,EAAE,yBAAyB,EAAE,MAAM,kCAAkC,CAAC;AAGvG,IAAI,sBAAsB,GAAyB,IAAI,CAAC;AACxD,IAAI,uBAAuB,GAA2B,IAAI,CAAC;AAE3D,gBAAgB;AAChB,MAAM,UAAU,0BAA0B;IACtC,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC1B,sBAAsB,GAAG,IAAI,UAAU,EAAE,CAAC;IAC9C,CAAC;IACD,OAAO,sBAAsB,CAAC;AAClC,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,2BAA2B;IACvC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC3B,uBAAuB,GAAG,IAAI,YAAY,EAAE,CAAC;IACjD,CAAC;IACD,OAAO,uBAAuB,CAAC;AACnC,CAAC;AAED,gBAAgB;AAChB,MAAM,OAAO,cAAe,SAAQ,qBAAqB;IAOrD,gBAAgB;IAChB,YAAmB,QAAgC;QAC/C,KAAK,EAAE,CAAC;QARJ,aAAQ,GAA6B,sBAAsB,CAAC,OAAO,CAAC;QACpE,iBAAY,GAAW,sBAAsB,CAAC,WAAW,CAAC;QAC1D,iBAAY,GAAW,sBAAsB,CAAC,WAAW,CAAC;QAC1D,eAAU,GAAW,sBAAsB,CAAC,SAAS,CAAC;QAM1D,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC9B,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAA+B;QAC9C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;IAED,gBAAgB;IAChB,IAAW,SAAS;QAChB,OAAO,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC;IAC7D,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;IAED,gBAAgB;IAChB,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,IAAW,SAAS,CAAC,KAAa;QAC9B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,yBAAyB,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,MAAM,OAAO,GAAG,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,OAAO,EAAE,CAAC;YACV,mEAAmE;YACnE,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC3C,OAAO,CAAC,OAAO,EAAE,CAAC;QACtB,CAAC;IACL,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,WAAW;QACpB,MAAM,OAAO,GAAG,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,MAAM,IAAI,CAAC,SAAS,CAAC,wBAAwB,wCAAuB,CAAC;QACzE,CAAC;IACL,CAAC;IAED,gBAAgB;IACT,oBAAoB;QACvB,MAAM,OAAO,GAAG,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,mEAAmE;YACnE,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,0BAA0B,EAAE,CAAC;QACxC,CAAC;QACD,OAAO,OAAO,CAAC,oBAAoB,EAAE,CAAC;IAC1C,CAAC;IAED,gBAAgB;IACT,qBAAqB;QACxB,MAAM,OAAO,GAAG,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,mEAAmE;YACnE,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,2BAA2B,EAAE,CAAC;QACzC,CAAC;QACD,OAAO,OAAO,CAAC,qBAAqB,EAAE,CAAC;IAC3C,CAAC;CACJ", "sourcesContent": ["import { Logger } from \"../../../Misc/logger\";\nimport type { Nullable } from \"../../../types\";\nimport type { AudioAnalyzerFFTSizeType } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport { _AudioAnalyzerDefaults, AbstractAudioAnalyzer } from \"../../abstractAudio/subProperties/abstractAudioAnalyzer\";\nimport type { _AbstractAudioSubGraph } from \"../subNodes/abstractAudioSubGraph\";\nimport { _GetAudioAnalyzerSubNode, _SetAudioAnalyzerProperty } from \"../subNodes/audioAnalyzerSubNode\";\nimport { AudioSubNode } from \"../subNodes/audioSubNode\";\n\nlet EmptyByteFrequencyData: Nullable<Uint8Array> = null;\nlet EmptyFloatFrequencyData: Nullable<Float32Array> = null;\n\n/** @internal */\nexport function _GetEmptyByteFrequencyData(): Uint8Array {\n    if (!EmptyByteFrequencyData) {\n        EmptyByteFrequencyData = new Uint8Array();\n    }\n    return EmptyByteFrequencyData;\n}\n\n/** @internal */\nexport function _GetEmptyFloatFrequencyData(): Float32Array {\n    if (!EmptyFloatFrequencyData) {\n        EmptyFloatFrequencyData = new Float32Array();\n    }\n    return EmptyFloatFrequencyData;\n}\n\n/** @internal */\nexport class _AudioAnalyzer extends AbstractAudioAnalyzer {\n    private _fftSize: AudioAnalyzerFFTSizeType = _AudioAnalyzerDefaults.fftSize;\n    private _maxDecibels: number = _AudioAnalyzerDefaults.maxDecibels;\n    private _minDecibels: number = _AudioAnalyzerDefaults.minDecibels;\n    private _smoothing: number = _AudioAnalyzerDefaults.smoothing;\n    private _subGraph: _AbstractAudioSubGraph;\n\n    /** @internal */\n    public constructor(subGraph: _AbstractAudioSubGraph) {\n        super();\n        this._subGraph = subGraph;\n    }\n\n    /** @internal */\n    public get fftSize(): AudioAnalyzerFFTSizeType {\n        return this._fftSize;\n    }\n\n    public set fftSize(value: AudioAnalyzerFFTSizeType) {\n        this._fftSize = value;\n        _SetAudioAnalyzerProperty(this._subGraph, \"fftSize\", value);\n    }\n\n    /** @internal */\n    public get isEnabled(): boolean {\n        return _GetAudioAnalyzerSubNode(this._subGraph) !== null;\n    }\n\n    /** @internal */\n    public get minDecibels(): number {\n        return this._minDecibels;\n    }\n\n    public set minDecibels(value: number) {\n        this._minDecibels = value;\n        _SetAudioAnalyzerProperty(this._subGraph, \"minDecibels\", value);\n    }\n\n    /** @internal */\n    public get maxDecibels(): number {\n        return this._maxDecibels;\n    }\n\n    public set maxDecibels(value: number) {\n        this._maxDecibels = value;\n        _SetAudioAnalyzerProperty(this._subGraph, \"maxDecibels\", value);\n    }\n\n    /** @internal */\n    public get smoothing(): number {\n        return this._smoothing;\n    }\n\n    public set smoothing(value: number) {\n        this._smoothing = value;\n        _SetAudioAnalyzerProperty(this._subGraph, \"smoothing\", value);\n    }\n\n    /** @internal */\n    public dispose(): void {\n        const subNode = _GetAudioAnalyzerSubNode(this._subGraph);\n        if (subNode) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this._subGraph.removeSubNodeAsync(subNode);\n            subNode.dispose();\n        }\n    }\n\n    /** @internal */\n    public async enableAsync(): Promise<void> {\n        const subNode = _GetAudioAnalyzerSubNode(this._subGraph);\n        if (!subNode) {\n            await this._subGraph.createAndAddSubNodeAsync(AudioSubNode.ANALYZER);\n        }\n    }\n\n    /** @internal */\n    public getByteFrequencyData(): Uint8Array {\n        const subNode = _GetAudioAnalyzerSubNode(this._subGraph);\n        if (!subNode) {\n            Logger.Warn(\"AudioAnalyzer not enabled\");\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.enableAsync();\n            return _GetEmptyByteFrequencyData();\n        }\n        return subNode.getByteFrequencyData();\n    }\n\n    /** @internal */\n    public getFloatFrequencyData(): Float32Array {\n        const subNode = _GetAudioAnalyzerSubNode(this._subGraph);\n        if (!subNode) {\n            Logger.Warn(\"AudioAnalyzer not enabled\");\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.enableAsync();\n            return _GetEmptyFloatFrequencyData();\n        }\n        return subNode.getFloatFrequencyData();\n    }\n}\n"]}