{"version": 3, "file": "webgpuShaderProcessorsGLSL.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuShaderProcessorsGLSL.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAEhF,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAEhE,OAAO,EAAE,2BAA2B,EAAE,MAAM,mCAAmC,CAAC;AAChF,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,gBAAgB;AAChB,MAAM,OAAO,yBAA0B,SAAQ,qBAAqB;IAApE;;QACc,qBAAgB,GAAkB,EAAE,CAAC;QACrC,4BAAuB,GAAkB,EAAE,CAAC;QAE5C,mBAAc,GAAY,KAAK,CAAC;QAChC,qBAAgB,GAAY,KAAK,CAAC;QAE5B,mBAAc,+BAAuB;QAC9C,eAAU,GAAG,IAAI,CAAC;IAmY7B,CAAC;IA9Xa,aAAa,CAAC,IAAY,EAAE,IAAY,EAAE,aAAwC;QACxF,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACnC,IAAI,UAAU,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;YAChE,MAAM,GAAG,CAAC,cAAc,CAAC;YACzB,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;gBAChB,MAAM,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;YACnD,CAAC;YACD,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QACzC,CAAC;QACD,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC;IAEM,iBAAiB,CAAC,iBAAsD;QAC3E,IAAI,CAAC,wBAAwB,GAAG,iBAAkD,CAAC;QAEnF,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;QAC1C,IAAI,CAAC,0BAA0B,GAAG,SAAS,CAAC;IAChD,CAAC;IAEM,oBAAoB,CAAC,IAAY,EAAE,UAAmB;QACzD,MAAM,aAAa,GAAG,6BAA6B,qBAAqB,CAAC,gBAAgB,wDAAwD,CAAC;QAClJ,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;QAEhE,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1D,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;YAC3C,CAAC;YACD,OAAO,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,GAAG,kBAAkB,GAAG,IAAI,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;QACxD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;YACjC,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QAC1C,CAAC;QACD,OAAO,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,GAAG,IAAI,CAAC;IACzD,CAAC;IAEM,YAAY,CAAC,OAAe,EAAE,UAAmB;QACpD,MAAM,QAAQ,GAAG,qBAAqB,CAAC;QACvC,MAAM,OAAO,GAAG,oBAAoB,CAAC;QACrC,MAAM,YAAY,GAAG,yBAAyB,CAAC;QAE/C,MAAM,KAAK,GAAG,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC;QAE3H,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAEM,gBAAgB,CAAC,OAAe,EAAE,UAAmB,EAAE,aAAwC;QAClG,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,MAAM,QAAQ,GAAG,mEAAmE,CAAC;QACrF,MAAM,OAAO,GAAG,kEAAkE,CAAC;QACnF,MAAM,YAAY,GAAG,uEAAuE,CAAC;QAE7F,MAAM,KAAK,GAAG,UAAU,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC;QAC3H,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,sBAAsB,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,QAAgB,CAAC;YACrB,IAAI,UAAU,EAAE,CAAC;gBACb,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACjE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACrC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACzB,MAAM,CAAC,IAAI,CAAC,+CAA+C,IAAI,2EAA2E,CAAC,CAAC;gBAChJ,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtI,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;gBACjE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,qBAAqB,QAAQ,KAAK,sBAAsB,OAAO,WAAW,IAAI,IAAI,GAAG,CAAC;YAC5H,CAAC;YAED,OAAO,GAAG,OAAO,CAAC,OAAO,CACrB,KAAK,CAAC,CAAC,CAAC,EACR,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,qBAAqB,QAAQ,KAAK,sBAAsB,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,WAAW,IAAI,IAAI,GAAG,CAChJ,CAAC;QACN,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,kBAAkB,CAAC,SAAiB,EAAE,aAAwC;QACjF,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,MAAM,OAAO,GAAG,6BAA6B,CAAC;QAC9C,MAAM,WAAW,GAAG,oCAAoC,CAAC;QAEzD,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC;QAC1D,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAElJ,IAAI,CAAC,wBAAwB,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;YACnE,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;YAEjE,MAAM,aAAa,GAAG,IAAI,CAAC,wBAAwB,CAAC,oCAAoC,CAAC,IAAI,CAAC,CAAC;YAC/F,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;gBAC9B,oGAAoG;gBACpG,MAAM,OAAO,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,aAAa,CAAC;gBACrJ,MAAM,OAAO,GAAG,QAAQ,IAAI,GAAG,CAAC;gBAEhC,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,qBAAqB,QAAQ,QAAQ,OAAO,IAAI,OAAO,KAAK,aAAa,IAAI,IAAI,MAAM,aAAa,IAAI,OAAO,IAAI,CAAC,CAAC;YACjK,CAAC;iBAAM,CAAC;gBACJ,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,qBAAqB,QAAQ,QAAQ,aAAa,IAAI,IAAI,GAAG,CAAC,CAAC;YAC3G,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEM,gBAAgB,CAAC,OAAe,EAAE,UAAmB,EAAE,aAAwC;QAClG,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QAEpC,MAAM,YAAY,GAAG,6DAA6D,CAAC;QAEnF,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,IAAI,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEpB,IAAI,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/E,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC,kDAAkD;gBAErE,CAAC,IAAI,EAAE,WAAW,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;gBAEtF,IAAI,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACxE,IAAI,CAAC,WAAW,EAAE,CAAC;oBACf,WAAW,GAAG;wBACV,eAAe,EAAE,IAAI;wBACrB,cAAc,EAAE,SAAS,GAAG,CAAC;wBAC7B,gBAAgB,EAAE,KAAK;wBACvB,QAAQ,EAAE,EAAE;wBACZ,UAAU,uDAAyC;qBACtD,CAAC;oBACF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;wBACxC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,CAAC,CAAC;oBACrF,CAAC;gBACL,CAAC;gBAED,MAAM,WAAW,GAAG,qBAAqB,CAAC,8BAA8B,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC;gBACnG,MAAM,mBAAmB,GAAG,CAAC,CAAC,qBAAqB,CAAC,uCAAuC,CAAC,WAAW,CAAC,CAAC;gBACzG,MAAM,kBAAkB,GAAG,mBAAmB,CAAC,CAAC,kEAA+C,CAAC,+DAA6C,CAAC;gBAC9I,MAAM,WAAW,GAAG,IAAI,GAAG,SAAS,CAAC,iBAAiB,CAAC;gBAEvD,IAAI,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAC/E,IAAI,CAAC,WAAW,EAAE,CAAC;oBACf,WAAW,GAAG;wBACV,OAAO,EAAE,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,EAAE;wBAC9D,IAAI,EAAE,kBAAkB;qBAC3B,CAAC;gBACN,CAAC;gBAED,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAErG,IAAI,aAAa,EAAE,CAAC;oBAChB,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC;gBAED,MAAM,UAAU,GAAG,mBAAmB;oBAClC,CAAC;oBACD,CAAC,CAAC,aAAa,KAAK,GAAG;wBACrB,CAAC;wBACD,CAAC,CAAC,aAAa,KAAK,GAAG;4BACrB,CAAC;4BACD,CAAC,sDAAwC,CAAC;gBAElD,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC;gBAEpC,MAAM,cAAc,GAAG,SAAS,GAAG,CAAC,CAAC;gBACrC,MAAM,iBAAiB,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC;gBACzD,MAAM,mBAAmB,GAAG,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC;gBAC7D,MAAM,eAAe,GAAG,qBAAqB,CAAC,kCAAkC,CAAC,WAAW,CAAC,CAAC;gBAC9F,MAAM,WAAW,GAAG,qBAAqB,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAC;gBACtF,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,2CAA2C,CAAC,WAAW,CAAC,CAAC;gBAExG,gCAAgC;gBAChC,IAAI,CAAC,cAAc,EAAE,CAAC;oBAClB,SAAS,GAAG,CAAC,CAAC;oBACd,OAAO,GAAG,gBAAgB,iBAAiB,eAAe,mBAAmB,aAAa,WAAW,IAAI,WAAW;uCACjG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,eAAe,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,aAAa,aAAa,GAAG,WAAW,IAAI,IAAI;kCAC1I,IAAI,IAAI,aAAa,GAAG,eAAe,IAAI,IAAI,YAAY,WAAW,GAAG,CAAC;gBAC5F,CAAC;qBAAM,CAAC;oBACJ,MAAM,OAAO,GAAG,EAAE,CAAC;oBACnB,OAAO,CAAC,IAAI,CAAC,gBAAgB,iBAAiB,eAAe,mBAAmB,aAAa,aAAa,GAAG,WAAW,IAAI,WAAW,GAAG,CAAC,CAAC;oBAC5I,OAAO,GAAG,IAAI,CAAC;oBACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;wBACjC,MAAM,eAAe,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;wBAC3D,MAAM,mBAAmB,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;wBAEjE,OAAO,CAAC,IAAI,CAAC,gBAAgB,eAAe,eAAe,mBAAmB,aAAa,WAAW,IAAI,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;wBAE9H,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,IAAI,aAAa,GAAG,eAAe,IAAI,IAAI,UAAU,CAAC,KAAK,WAAW,GAAG,CAAC;oBAChI,CAAC;oBACD,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;oBACvC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5C,CAAC;gBAED,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;gBACpE,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,WAAW,CAAC,GAAG,WAAW,CAAC;gBAE3E,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE,WAAW,EAAE,CAAC,UAAU,CAAC,CAAC;gBAE1E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;oBACjC,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,UAAU,CAAC,CAAC;gBAClG,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;gBAChE,OAAO,GAAG,EAAE,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEM,sBAAsB,CAAC,aAAqB,EAAE,UAAmB;QACpE,MAAM,QAAQ,GAAG,mBAAmB,CAAC;QAErC,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3C,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,IAAI,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC7E,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACrB,MAAM,QAAQ,GAAG,6BAA6B,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAE/D,IAAI,OAAO,CAAC;gBACZ,IAAI,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;oBACjD,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACJ,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,qBAAqB,EAAE,CAAC;gBACpE,CAAC;gBAED,iBAAiB,GAAG,EAAE,OAAO,EAAE,CAAC;gBAChC,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC;YAC7E,CAAC;YAED,IAAI,CAAC,4BAA4B,CAAC,IAAI,EAAE,iBAAiB,6DAA6C,CAAC,UAAU,CAAC,CAAC;YAEnH,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,gBAAgB,iBAAiB,CAAC,OAAO,CAAC,UAAU,eAAe,iBAAiB,CAAC,OAAO,CAAC,YAAY,WAAW,CAAC,CAAC;QAC3K,CAAC;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;IAEM,aAAa,CAChB,IAAY,EACZ,OAAiB,EACjB,UAAmB,EACnB,kBAAuD,EACvD,WAAsE;QAEtE,MAAM,uBAAuB,GAAG,IAAI,CAAC,MAAM,CAAC,0CAA0C,CAAC,KAAK,CAAC,CAAC,CAAC;QAE/F,oBAAoB;QACpB,MAAM,KAAK,GAAG,gJAAgJ,CAAC;QAC/J,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAE/B,uBAAuB;QACvB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;QACnD,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACvD,MAAM,aAAa,GAAG;;;;;aAKrB,CAAC;YAEF,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;YAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,kCAAkC,CAAC,KAAK,CAAC,CAAC,CAAC;YAEzE,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;YAC5D,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,yBAAyB,EAAE,aAAa,CAAC,CAAC;YAC9D,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;YACrD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;YACxD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;YACpD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAClD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;YACrD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACzB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,uBAAuB,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,8CAA8C,CAAC,GAAG,YAAY,CAAC,CAAC;YAC3J,CAAC;iBAAM,CAAC;gBACJ,MAAM,KAAK,GAAG,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;oBACjB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,uBAAuB,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAClG,CAAC;YACL,CAAC;YACD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC,CAAC,2CAA2C;YAC7F,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;YAElD,IAAI,YAAY,EAAE,CAAC;gBACf,IAAI,GAAG,2BAA2B,CAAC,IAAI,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;YACzE,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;YAC1D,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;YACtD,MAAM,qBAAqB,GAAG,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1E,IAAI,qBAAqB,EAAE,CAAC;gBACxB,OAAO,sEAAsE,GAAG,IAAI,CAAC;YACzF,CAAC;QACL,CAAC;QAED,gDAAgD;QAChD,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC/C,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;YAC3C,IAAI,IAAI,8BAA8B,CAAC;YACvC,2CAA2C;YAC3C,IAAI,IAAI,GAAG,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,4BAA4B,CAAC,IAAY,EAAE,IAAY;QAC3D,iDAAiD;QACjD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,iBAAiB,EAAE,IAAI,CAAC,CAAC;QACzD,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7B,OAAO,KAAK,KAAK,IAAI,EAAE,CAAC;YACpB,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC;YACpB,IAAI,IAAI,CAAC,cAAc,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvC,MAAM,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;YAChD,CAAC;YACD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,CAAC;YAC7C,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAES,wBAAwB,CAAC,IAAY,EAAE,wBAAiD;QAC9F,IAAI,GAAG,GAAG,gBAAgB,wBAAwB,CAAC,OAAO,CAAC,UAAU,eAAe,wBAAwB,CAAC,OAAO,CAAC,YAAY,aAAa,IAAI,UAAU,CAAC;QAC7J,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,wBAAwB,CAAC,gBAAgB,EAAE,CAAC;YAC3E,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,GAAG,IAAI,OAAO,eAAe,CAAC,IAAI,IAAI,eAAe,CAAC,IAAI,IAAI,eAAe,CAAC,MAAM,MAAM,CAAC;YAC/F,CAAC;iBAAM,CAAC;gBACJ,GAAG,IAAI,OAAO,eAAe,CAAC,IAAI,IAAI,eAAe,CAAC,IAAI,KAAK,CAAC;YACpE,CAAC;QACL,CAAC;QACD,GAAG,IAAI,QAAQ,CAAC;QAEhB,OAAO,GAAG,CAAC;IACf,CAAC;IAEM,eAAe,CAAC,UAAkB,EAAE,YAAoB;QAC3D,gEAAgE;QAChE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;YAC7C,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YACjE,YAAY,GAAG,IAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QACzE,CAAC;QAED,oDAAoD;QACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG,YAAY,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,4BAA4B;QAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE7C,UAAU,GAAG,WAAW,GAAG,UAAU,CAAC;QACtC,YAAY,GAAG,WAAW,GAAG,YAAY,CAAC;QAE1C,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAElC,IAAI,CAAC,cAAc,GAAG,IAAW,CAAC;QAClC,IAAI,CAAC,wBAAwB,CAAC,oCAAoC,GAAG,EAAE,CAAC;QAExE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC;IACxC,CAAC;CACJ", "sourcesContent": ["/* eslint-disable baby<PERSON>js/available */\r\n/* eslint-disable jsdoc/require-jsdoc */\r\nimport type { Nullable } from \"../../types\";\r\nimport type { _IShaderProcessingContext } from \"../Processors/shaderProcessingOptions\";\r\nimport type { WebGPUBufferDescription } from \"./webgpuShaderProcessingContext\";\r\nimport { WebGPUShaderProcessingContext } from \"./webgpuShaderProcessingContext\";\r\nimport * as WebGPUConstants from \"./webgpuConstants\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { WebGPUShaderProcessor } from \"./webgpuShaderProcessor\";\r\nimport { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\nimport { InjectStartingAndEndingCode } from \"../../Misc/codeStringParsingTools\";\r\nimport { Constants } from \"../constants\";\r\n\r\n/** @internal */\r\nexport class WebGPUShaderProcessorGLSL extends WebGPUShaderProcessor {\r\n    protected _missingVaryings: Array<string> = [];\r\n    protected _textureArrayProcessing: Array<string> = [];\r\n    protected _preProcessors: { [key: string]: string };\r\n    protected _vertexIsGLES3: boolean = false;\r\n    protected _fragmentIsGLES3: boolean = false;\r\n\r\n    public override shaderLanguage = ShaderLanguage.GLSL;\r\n    public parseGLES3 = true;\r\n    public attributeKeywordName: string | undefined;\r\n    public varyingVertexKeywordName: string | undefined;\r\n    public varyingFragmentKeywordName: string | undefined;\r\n\r\n    protected _getArraySize(name: string, type: string, preProcessors: { [key: string]: string }): [string, string, number] {\r\n        let length = 0;\r\n        const startArray = name.indexOf(\"[\");\r\n        const endArray = name.indexOf(\"]\");\r\n        if (startArray > 0 && endArray > 0) {\r\n            const lengthInString = name.substring(startArray + 1, endArray);\r\n            length = +lengthInString;\r\n            if (isNaN(length)) {\r\n                length = +preProcessors[lengthInString.trim()];\r\n            }\r\n            name = name.substring(0, startArray);\r\n        }\r\n        return [name, type, length];\r\n    }\r\n\r\n    public initializeShaders(processingContext: Nullable<_IShaderProcessingContext>): void {\r\n        this._webgpuProcessingContext = processingContext as WebGPUShaderProcessingContext;\r\n\r\n        this._missingVaryings.length = 0;\r\n        this._textureArrayProcessing.length = 0;\r\n        this.attributeKeywordName = undefined;\r\n        this.varyingVertexKeywordName = undefined;\r\n        this.varyingFragmentKeywordName = undefined;\r\n    }\r\n\r\n    public preProcessShaderCode(code: string, isFragment: boolean): string {\r\n        const ubDeclaration = `// Internals UBO\\nuniform ${WebGPUShaderProcessor.InternalsUBOName} {\\nfloat yFactor_;\\nfloat textureOutputHeight_;\\n};\\n`;\r\n        const alreadyInjected = code.indexOf(\"// Internals UBO\") !== -1;\r\n\r\n        if (isFragment) {\r\n            this._fragmentIsGLES3 = code.indexOf(\"#version 3\") !== -1;\r\n            if (this._fragmentIsGLES3) {\r\n                this.varyingFragmentKeywordName = \"in\";\r\n            }\r\n            return alreadyInjected ? code : ubDeclaration + \"##INJECTCODE##\\n\" + code;\r\n        }\r\n\r\n        this._vertexIsGLES3 = code.indexOf(\"#version 3\") !== -1;\r\n        if (this._vertexIsGLES3) {\r\n            this.attributeKeywordName = \"in\";\r\n            this.varyingVertexKeywordName = \"out\";\r\n        }\r\n        return alreadyInjected ? code : ubDeclaration + code;\r\n    }\r\n\r\n    public varyingCheck(varying: string, isFragment: boolean) {\r\n        const outRegex = /(flat\\s)?\\s*\\bout\\b/;\r\n        const inRegex = /(flat\\s)?\\s*\\bin\\b/;\r\n        const varyingRegex = /(flat\\s)?\\s*\\bvarying\\b/;\r\n\r\n        const regex = isFragment && this._fragmentIsGLES3 ? inRegex : !isFragment && this._vertexIsGLES3 ? outRegex : varyingRegex;\r\n\r\n        return regex.test(varying);\r\n    }\r\n\r\n    public varyingProcessor(varying: string, isFragment: boolean, preProcessors: { [key: string]: string }) {\r\n        this._preProcessors = preProcessors;\r\n\r\n        const outRegex = /\\s*(flat)?\\s*out\\s+(?:(?:highp)?|(?:lowp)?)\\s*(\\S+)\\s+(\\S+)\\s*;/gm;\r\n        const inRegex = /\\s*(flat)?\\s*in\\s+(?:(?:highp)?|(?:lowp)?)\\s*(\\S+)\\s+(\\S+)\\s*;/gm;\r\n        const varyingRegex = /\\s*(flat)?\\s*varying\\s+(?:(?:highp)?|(?:lowp)?)\\s*(\\S+)\\s+(\\S+)\\s*;/gm;\r\n\r\n        const regex = isFragment && this._fragmentIsGLES3 ? inRegex : !isFragment && this._vertexIsGLES3 ? outRegex : varyingRegex;\r\n        const match = regex.exec(varying);\r\n        if (match !== null) {\r\n            const interpolationQualifier = match[1] ?? \"\";\r\n            const varyingType = match[2];\r\n            const name = match[3];\r\n            let location: number;\r\n            if (isFragment) {\r\n                location = this._webgpuProcessingContext.availableVaryings[name];\r\n                this._missingVaryings[location] = \"\";\r\n                if (location === undefined) {\r\n                    Logger.Warn(`Invalid fragment shader: The varying named \"${name}\" is not declared in the vertex shader! This declaration will be ignored.`);\r\n                }\r\n            } else {\r\n                location = this._webgpuProcessingContext.getVaryingNextLocation(varyingType, this._getArraySize(name, varyingType, preProcessors)[2]);\r\n                this._webgpuProcessingContext.availableVaryings[name] = location;\r\n                this._missingVaryings[location] = `layout(location = ${location}) ${interpolationQualifier} in ${varyingType} ${name};`;\r\n            }\r\n\r\n            varying = varying.replace(\r\n                match[0],\r\n                location === undefined ? \"\" : `layout(location = ${location}) ${interpolationQualifier} ${isFragment ? \"in\" : \"out\"} ${varyingType} ${name};`\r\n            );\r\n        }\r\n        return varying;\r\n    }\r\n\r\n    public attributeProcessor(attribute: string, preProcessors: { [key: string]: string }) {\r\n        this._preProcessors = preProcessors;\r\n\r\n        const inRegex = /\\s*in\\s+(\\S+)\\s+(\\S+)\\s*;/gm;\r\n        const attribRegex = /\\s*attribute\\s+(\\S+)\\s+(\\S+)\\s*;/gm;\r\n\r\n        const regex = this._vertexIsGLES3 ? inRegex : attribRegex;\r\n        const match = regex.exec(attribute);\r\n        if (match !== null) {\r\n            const attributeType = match[1];\r\n            const name = match[2];\r\n            const location = this._webgpuProcessingContext.getAttributeNextLocation(attributeType, this._getArraySize(name, attributeType, preProcessors)[2]);\r\n\r\n            this._webgpuProcessingContext.availableAttributes[name] = location;\r\n            this._webgpuProcessingContext.orderedAttributes[location] = name;\r\n\r\n            const numComponents = this._webgpuProcessingContext.vertexBufferKindToNumberOfComponents[name];\r\n            if (numComponents !== undefined) {\r\n                // Special case for an int/ivecX vertex buffer that is used as a float/vecX attribute in the shader.\r\n                const newType = numComponents < 0 ? (numComponents === -1 ? \"int\" : \"ivec\" + -numComponents) : numComponents === 1 ? \"uint\" : \"uvec\" + numComponents;\r\n                const newName = `_int_${name}_`;\r\n\r\n                attribute = attribute.replace(match[0], `layout(location = ${location}) in ${newType} ${newName}; ${attributeType} ${name} = ${attributeType}(${newName});`);\r\n            } else {\r\n                attribute = attribute.replace(match[0], `layout(location = ${location}) in ${attributeType} ${name};`);\r\n            }\r\n        }\r\n        return attribute;\r\n    }\r\n\r\n    public uniformProcessor(uniform: string, isFragment: boolean, preProcessors: { [key: string]: string }): string {\r\n        this._preProcessors = preProcessors;\r\n\r\n        const uniformRegex = /\\s*uniform\\s+(?:(?:highp)?|(?:lowp)?)\\s*(\\S+)\\s+(\\S+)\\s*;/gm;\r\n\r\n        const match = uniformRegex.exec(uniform);\r\n        if (match !== null) {\r\n            let uniformType = match[1];\r\n            let name = match[2];\r\n\r\n            if (uniformType.indexOf(\"sampler\") === 0 || uniformType.indexOf(\"sampler\") === 1) {\r\n                let arraySize = 0; // 0 means the texture is not declared as an array\r\n\r\n                [name, uniformType, arraySize] = this._getArraySize(name, uniformType, preProcessors);\r\n\r\n                let textureInfo = this._webgpuProcessingContext.availableTextures[name];\r\n                if (!textureInfo) {\r\n                    textureInfo = {\r\n                        autoBindSampler: true,\r\n                        isTextureArray: arraySize > 0,\r\n                        isStorageTexture: false,\r\n                        textures: [],\r\n                        sampleType: WebGPUConstants.TextureSampleType.Float,\r\n                    };\r\n                    for (let i = 0; i < (arraySize || 1); ++i) {\r\n                        textureInfo.textures.push(this._webgpuProcessingContext.getNextFreeUBOBinding());\r\n                    }\r\n                }\r\n\r\n                const samplerType = WebGPUShaderProcessor._SamplerTypeByWebGLSamplerType[uniformType] ?? \"sampler\";\r\n                const isComparisonSampler = !!WebGPUShaderProcessor._IsComparisonSamplerByWebGPUSamplerType[samplerType];\r\n                const samplerBindingType = isComparisonSampler ? WebGPUConstants.SamplerBindingType.Comparison : WebGPUConstants.SamplerBindingType.Filtering;\r\n                const samplerName = name + Constants.AUTOSAMPLERSUFFIX;\r\n\r\n                let samplerInfo = this._webgpuProcessingContext.availableSamplers[samplerName];\r\n                if (!samplerInfo) {\r\n                    samplerInfo = {\r\n                        binding: this._webgpuProcessingContext.getNextFreeUBOBinding(),\r\n                        type: samplerBindingType,\r\n                    };\r\n                }\r\n\r\n                const componentType = uniformType.charAt(0) === \"u\" ? \"u\" : uniformType.charAt(0) === \"i\" ? \"i\" : \"\";\r\n\r\n                if (componentType) {\r\n                    uniformType = uniformType.substring(1);\r\n                }\r\n\r\n                const sampleType = isComparisonSampler\r\n                    ? WebGPUConstants.TextureSampleType.Depth\r\n                    : componentType === \"u\"\r\n                      ? WebGPUConstants.TextureSampleType.Uint\r\n                      : componentType === \"i\"\r\n                        ? WebGPUConstants.TextureSampleType.Sint\r\n                        : WebGPUConstants.TextureSampleType.Float;\r\n\r\n                textureInfo.sampleType = sampleType;\r\n\r\n                const isTextureArray = arraySize > 0;\r\n                const samplerGroupIndex = samplerInfo.binding.groupIndex;\r\n                const samplerBindingIndex = samplerInfo.binding.bindingIndex;\r\n                const samplerFunction = WebGPUShaderProcessor._SamplerFunctionByWebGLSamplerType[uniformType];\r\n                const textureType = WebGPUShaderProcessor._TextureTypeByWebGLSamplerType[uniformType];\r\n                const textureDimension = WebGPUShaderProcessor._GpuTextureViewDimensionByWebGPUTextureType[textureType];\r\n\r\n                // Manage textures and samplers.\r\n                if (!isTextureArray) {\r\n                    arraySize = 1;\r\n                    uniform = `layout(set = ${samplerGroupIndex}, binding = ${samplerBindingIndex}) uniform ${samplerType} ${samplerName};\r\n                        layout(set = ${textureInfo.textures[0].groupIndex}, binding = ${textureInfo.textures[0].bindingIndex}) uniform ${componentType}${textureType} ${name}Texture;\r\n                        #define ${name} ${componentType}${samplerFunction}(${name}Texture, ${samplerName})`;\r\n                } else {\r\n                    const layouts = [];\r\n                    layouts.push(`layout(set = ${samplerGroupIndex}, binding = ${samplerBindingIndex}) uniform ${componentType}${samplerType} ${samplerName};`);\r\n                    uniform = `\\n`;\r\n                    for (let i = 0; i < arraySize; ++i) {\r\n                        const textureSetIndex = textureInfo.textures[i].groupIndex;\r\n                        const textureBindingIndex = textureInfo.textures[i].bindingIndex;\r\n\r\n                        layouts.push(`layout(set = ${textureSetIndex}, binding = ${textureBindingIndex}) uniform ${textureType} ${name}Texture${i};`);\r\n\r\n                        uniform += `${i > 0 ? \"\\n\" : \"\"}#define ${name}${i} ${componentType}${samplerFunction}(${name}Texture${i}, ${samplerName})`;\r\n                    }\r\n                    uniform = layouts.join(\"\\n\") + uniform;\r\n                    this._textureArrayProcessing.push(name);\r\n                }\r\n\r\n                this._webgpuProcessingContext.availableTextures[name] = textureInfo;\r\n                this._webgpuProcessingContext.availableSamplers[samplerName] = samplerInfo;\r\n\r\n                this._addSamplerBindingDescription(samplerName, samplerInfo, !isFragment);\r\n\r\n                for (let i = 0; i < arraySize; ++i) {\r\n                    this._addTextureBindingDescription(name, textureInfo, i, textureDimension, null, !isFragment);\r\n                }\r\n            } else {\r\n                this._addUniformToLeftOverUBO(name, uniformType, preProcessors);\r\n                uniform = \"\";\r\n            }\r\n        }\r\n        return uniform;\r\n    }\r\n\r\n    public uniformBufferProcessor(uniformBuffer: string, isFragment: boolean): string {\r\n        const uboRegex = /uniform\\s+(\\w+)/gm;\r\n\r\n        const match = uboRegex.exec(uniformBuffer);\r\n        if (match !== null) {\r\n            const name = match[1];\r\n\r\n            let uniformBufferInfo = this._webgpuProcessingContext.availableBuffers[name];\r\n            if (!uniformBufferInfo) {\r\n                const knownUBO = WebGPUShaderProcessingContext.KnownUBOs[name];\r\n\r\n                let binding;\r\n                if (knownUBO && knownUBO.binding.groupIndex !== -1) {\r\n                    binding = knownUBO.binding;\r\n                } else {\r\n                    binding = this._webgpuProcessingContext.getNextFreeUBOBinding();\r\n                }\r\n\r\n                uniformBufferInfo = { binding };\r\n                this._webgpuProcessingContext.availableBuffers[name] = uniformBufferInfo;\r\n            }\r\n\r\n            this._addBufferBindingDescription(name, uniformBufferInfo, WebGPUConstants.BufferBindingType.Uniform, !isFragment);\r\n\r\n            uniformBuffer = uniformBuffer.replace(\"uniform\", `layout(set = ${uniformBufferInfo.binding.groupIndex}, binding = ${uniformBufferInfo.binding.bindingIndex}) uniform`);\r\n        }\r\n        return uniformBuffer;\r\n    }\r\n\r\n    public postProcessor(\r\n        code: string,\r\n        defines: string[],\r\n        isFragment: boolean,\r\n        _processingContext: Nullable<_IShaderProcessingContext>,\r\n        _parameters?: { [key: string]: number | string | boolean | undefined }\r\n    ): string {\r\n        const hasDrawBuffersExtension = code.search(/#extension.+GL_EXT_draw_buffers.+require/) !== -1;\r\n\r\n        // Remove extensions\r\n        const regex = /#extension.+(GL_OVR_multiview2|GL_OES_standard_derivatives|GL_EXT_shader_texture_lod|GL_EXT_frag_depth|GL_EXT_draw_buffers).+(enable|require)/g;\r\n        code = code.replace(regex, \"\");\r\n\r\n        // Replace instructions\r\n        code = code.replace(/texture2D\\s*\\(/g, \"texture(\");\r\n        if (isFragment) {\r\n            const hasFragCoord = code.indexOf(\"gl_FragCoord\") >= 0;\r\n            const fragCoordCode = `\r\n                glFragCoord_ = gl_FragCoord;\r\n                if (yFactor_ == 1.) {\r\n                    glFragCoord_.y = textureOutputHeight_ - glFragCoord_.y;\r\n                }\r\n            `;\r\n\r\n            const injectCode = hasFragCoord ? \"vec4 glFragCoord_;\\n\" : \"\";\r\n            const hasOutput = code.search(/layout *\\(location *= *0\\) *out/g) !== -1;\r\n\r\n            code = code.replace(/texture2DLodEXT\\s*\\(/g, \"textureLod(\");\r\n            code = code.replace(/textureCubeLodEXT\\s*\\(/g, \"textureLod(\");\r\n            code = code.replace(/textureCube\\s*\\(/g, \"texture(\");\r\n            code = code.replace(/gl_FragDepthEXT/g, \"gl_FragDepth\");\r\n            code = code.replace(/gl_FragColor/g, \"glFragColor\");\r\n            code = code.replace(/gl_FragData/g, \"glFragData\");\r\n            code = code.replace(/gl_FragCoord/g, \"glFragCoord_\");\r\n            if (!this._fragmentIsGLES3) {\r\n                code = code.replace(/void\\s+?main\\s*\\(/g, (hasDrawBuffersExtension || hasOutput ? \"\" : \"layout(location = 0) out vec4 glFragColor;\\n\") + \"void main(\");\r\n            } else {\r\n                const match = /^\\s*out\\s+\\S+\\s+\\S+\\s*;/gm.exec(code);\r\n                if (match !== null) {\r\n                    code = code.substring(0, match.index) + \"layout(location = 0) \" + code.substring(match.index);\r\n                }\r\n            }\r\n            code = code.replace(/dFdy/g, \"(-yFactor_)*dFdy\"); // will also handle dFdyCoarse and dFdyFine\r\n            code = code.replace(\"##INJECTCODE##\", injectCode);\r\n\r\n            if (hasFragCoord) {\r\n                code = InjectStartingAndEndingCode(code, \"void main\", fragCoordCode);\r\n            }\r\n        } else {\r\n            code = code.replace(/gl_InstanceID/g, \"gl_InstanceIndex\");\r\n            code = code.replace(/gl_VertexID/g, \"gl_VertexIndex\");\r\n            const hasMultiviewExtension = defines.indexOf(\"#define MULTIVIEW\") !== -1;\r\n            if (hasMultiviewExtension) {\r\n                return \"#extension GL_OVR_multiview2 : require\\nlayout (num_views = 2) in;\\n\" + code;\r\n            }\r\n        }\r\n\r\n        // Flip Y + convert z range from [-1,1] to [0,1]\r\n        if (!isFragment) {\r\n            const lastClosingCurly = code.lastIndexOf(\"}\");\r\n            code = code.substring(0, lastClosingCurly);\r\n            code += \"gl_Position.y *= yFactor_;\\n\";\r\n            // isNDCHalfZRange is always true in WebGPU\r\n            code += \"}\";\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    private _applyTextureArrayProcessing(code: string, name: string): string {\r\n        // Replaces the occurrences of name[XX] by nameXX\r\n        const regex = new RegExp(name + \"\\\\s*\\\\[(.+)?\\\\]\", \"gm\");\r\n        let match = regex.exec(code);\r\n\r\n        while (match !== null) {\r\n            const index = match[1];\r\n            let iindex = +index;\r\n            if (this._preProcessors && isNaN(iindex)) {\r\n                iindex = +this._preProcessors[index.trim()];\r\n            }\r\n            code = code.replace(match[0], name + iindex);\r\n            match = regex.exec(code);\r\n        }\r\n\r\n        return code;\r\n    }\r\n\r\n    protected _generateLeftOverUBOCode(name: string, uniformBufferDescription: WebGPUBufferDescription): string {\r\n        let ubo = `layout(set = ${uniformBufferDescription.binding.groupIndex}, binding = ${uniformBufferDescription.binding.bindingIndex}) uniform ${name} {\\n    `;\r\n        for (const leftOverUniform of this._webgpuProcessingContext.leftOverUniforms) {\r\n            if (leftOverUniform.length > 0) {\r\n                ubo += `    ${leftOverUniform.type} ${leftOverUniform.name}[${leftOverUniform.length}];\\n`;\r\n            } else {\r\n                ubo += `    ${leftOverUniform.type} ${leftOverUniform.name};\\n`;\r\n            }\r\n        }\r\n        ubo += \"};\\n\\n\";\r\n\r\n        return ubo;\r\n    }\r\n\r\n    public finalizeShaders(vertexCode: string, fragmentCode: string): { vertexCode: string; fragmentCode: string } {\r\n        // make replacements for texture names in the texture array case\r\n        for (let i = 0; i < this._textureArrayProcessing.length; ++i) {\r\n            const name = this._textureArrayProcessing[i];\r\n            vertexCode = this._applyTextureArrayProcessing(vertexCode, name);\r\n            fragmentCode = this._applyTextureArrayProcessing(fragmentCode, name);\r\n        }\r\n\r\n        // inject the missing varying in the fragment shader\r\n        for (let i = 0; i < this._missingVaryings.length; ++i) {\r\n            const decl = this._missingVaryings[i];\r\n            if (decl && decl.length > 0) {\r\n                fragmentCode = decl + \"\\n\" + fragmentCode;\r\n            }\r\n        }\r\n\r\n        // Builds the leftover UBOs.\r\n        const leftOverUBO = this._buildLeftOverUBO();\r\n\r\n        vertexCode = leftOverUBO + vertexCode;\r\n        fragmentCode = leftOverUBO + fragmentCode;\r\n\r\n        this._collectBindingNames();\r\n        this._preCreateBindGroupEntries();\r\n\r\n        this._preProcessors = null as any;\r\n        this._webgpuProcessingContext.vertexBufferKindToNumberOfComponents = {};\r\n\r\n        return { vertexCode, fragmentCode };\r\n    }\r\n}\r\n"]}