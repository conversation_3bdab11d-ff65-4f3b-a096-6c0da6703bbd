{"version": 3, "file": "abstractSound.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/abstractAudio/abstractSound.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAKnD,OAAO,EAAE,mBAAmB,EAA4B,MAAM,uBAAuB,CAAC;AA4CtF;;GAEG;AACH,MAAM,OAAgB,aAAc,SAAQ,mBAAmB;IAa3D,YAAsB,IAAY,EAAE,MAAqB;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,+CAAuC,CAAC,CAAC,4BAA4B;QAbnF,oBAAe,GAAqC,IAAI,CAAC;QACzD,sBAAiB,GAAG,IAAI,GAAG,EAA0B,CAAC;QACtD,WAAM,8BAAkC;QAEtC,eAAU,GAAwC,IAAI,CAAC,iBAAiB,CAAC;QAGnF;;WAEG;QACa,sBAAiB,GAAG,IAAI,UAAU,EAAiB,CAAC;QA6K5D,qBAAgB,GAA+C,CAAC,QAAQ,EAAE,EAAE;YAChF,IAAI,IAAI,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;gBACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAChC,CAAC;YAED,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAExC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,6BAAqB,CAAC;gBACjC,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC;QACL,CAAC,CAAC;IApLF,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3C,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC3C,IAAI,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,IAAW,IAAI,CAAC,KAAc;QAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;IACtC,CAAC;IAED,IAAW,YAAY,CAAC,KAAa;QACjC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IACrC,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,KAAK,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACa,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;IASD;;OAEG;IACI,KAAK;QACR,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,MAAM,4BAAoB,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,MAAM,8BAAsB,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC;QAED,IAAI,CAAC,MAAM,6BAAqB,CAAC;IACrC,CAAC;IAQS,WAAW,CAAC,QAAgC;QAClD,IAAI,IAAI,CAAC,KAAK,8BAAsB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC/D,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO;QACX,CAAC;QAED,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC1D,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;IACpC,CAAC;IAES,UAAU,CAAC,QAAgC;QACjD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;IACjC,CAAC;IAES,kBAAkB;QACxB,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACpC,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC;gBACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC;YACtC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAES,SAAS,CAAC,KAAiB;QACjC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAIS,oBAAoB;QAC1B,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,EAAE,CAAC;YAC/B,MAAM,uBAAuB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,+BAAuB,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;YACnJ,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAEpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,uBAAuB,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC/C,MAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;gBACjC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;IACL,CAAC;CAcJ", "sourcesContent": ["import { Observable } from \"../../Misc/observable\";\nimport type { Nullable } from \"../../types\";\nimport { SoundState } from \"../soundState\";\nimport { AudioNodeType } from \"./abstractAudioNode\";\nimport type { _AbstractSoundInstance } from \"./abstractSoundInstance\";\nimport { AbstractSoundSource, type ISoundSourceOptions } from \"./abstractSoundSource\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { IVolumeAudioOptions } from \"./subNodes/volumeAudioSubNode\";\n\n/** @internal */\nexport interface IAbstractSoundOptionsBase {\n    /**\n     * Whether the sound should start playing automatically. Defaults to `false`.\n     */\n    autoplay: boolean;\n    /**\n     * The maximum number of instances that can play at the same time. Defaults to `Infinity`.\n     */\n    maxInstances: number;\n}\n\n/** @internal */\nexport interface IAbstractSoundPlayOptionsBase {\n    /**\n     * Whether the sound should loop. Defaults to `false`.\n     */\n    loop: boolean;\n    /**\n     * The time within the sound buffer to start playing at, in seconds. Defaults to `0`.\n     */\n    startOffset: number;\n}\n\n/**\n * Options for creating a sound.\n */\nexport interface IAbstractSoundOptions extends IAbstractSoundOptionsBase, IAbstractSoundPlayOptions, ISoundSourceOptions {}\n\n/**\n * Options for playing a sound.\n */\nexport interface IAbstractSoundPlayOptions extends IAbstractSoundPlayOptionsBase, IVolumeAudioOptions {}\n\n/**\n * Options stored in a sound.\n * @internal\n */\nexport interface IAbstractSoundStoredOptions extends IAbstractSoundOptionsBase, IAbstractSoundPlayOptionsBase {}\n\n/**\n * Abstract class representing a sound in the audio engine.\n */\nexport abstract class AbstractSound extends AbstractSoundSource {\n    private _newestInstance: Nullable<_AbstractSoundInstance> = null;\n    private _privateInstances = new Set<_AbstractSoundInstance>();\n    private _state: SoundState = SoundState.Stopped;\n\n    protected _instances: ReadonlySet<_AbstractSoundInstance> = this._privateInstances;\n    protected abstract readonly _options: IAbstractSoundStoredOptions;\n\n    /**\n     * Observable for when the sound stops playing.\n     */\n    public readonly onEndedObservable = new Observable<AbstractSound>();\n\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine, AudioNodeType.HAS_INPUTS_AND_OUTPUTS); // Inputs are for instances.\n    }\n\n    /**\n     * Whether the sound should start playing automatically. Defaults to `false`.\n     */\n    public get autoplay(): boolean {\n        return this._options.autoplay;\n    }\n\n    /**\n     * The current playback time of the sound, in seconds.\n     */\n    public get currentTime(): number {\n        const instance = this._getNewestInstance();\n        return instance ? instance.currentTime : 0;\n    }\n\n    public set currentTime(value: number) {\n        this.startOffset = value;\n\n        const instance = this._getNewestInstance();\n        if (instance) {\n            instance.currentTime = value;\n        }\n    }\n\n    /**\n     * Whether the sound should loop. Defaults to `false`.\n     */\n    public get loop(): boolean {\n        return this._options.loop;\n    }\n\n    public set loop(value: boolean) {\n        this._options.loop = value;\n    }\n\n    /**\n     * The maximum number of instances that can play at the same time. Defaults to `Infinity`.\n     */\n    public get maxInstances(): number {\n        return this._options.maxInstances;\n    }\n\n    public set maxInstances(value: number) {\n        this._options.maxInstances = value;\n    }\n\n    /**\n     * The time within the sound buffer to start playing at, in seconds. Defaults to `0`.\n     */\n    public get startOffset(): number {\n        return this._options.startOffset;\n    }\n\n    public set startOffset(value: number) {\n        this._options.startOffset = value;\n    }\n\n    /**\n     * The state of the sound.\n     */\n    public get state(): SoundState {\n        return this._state;\n    }\n\n    /**\n     * Releases associated resources.\n     */\n    public override dispose(): void {\n        super.dispose();\n\n        this.stop();\n\n        this._newestInstance = null;\n\n        this._privateInstances.clear();\n        this.onEndedObservable.clear();\n    }\n\n    /**\n     * Plays the sound.\n     * - Triggers `onEndedObservable` if played for the full duration and the `loop` option is not set.\n     * @param options The options to use when playing the sound. Options set here override the sound's options.\n     */\n    public abstract play(options?: Partial<IAbstractSoundPlayOptions>): void;\n\n    /**\n     * Pauses the sound.\n     */\n    public pause(): void {\n        const it = this._instances.values();\n        for (let next = it.next(); !next.done; next = it.next()) {\n            next.value.pause();\n        }\n\n        this._state = SoundState.Paused;\n    }\n\n    /**\n     * Resumes the sound.\n     */\n    public resume(): void {\n        if (this._state !== SoundState.Paused) {\n            return;\n        }\n\n        const it = this._instances.values();\n        for (let next = it.next(); !next.done; next = it.next()) {\n            next.value.resume();\n        }\n\n        this._state = SoundState.Started;\n    }\n\n    /**\n     * Stops the sound.\n     * - Triggers `onEndedObservable` if the sound is playing.\n     */\n    public abstract stop(): void;\n\n    protected _beforePlay(instance: _AbstractSoundInstance): void {\n        if (this.state === SoundState.Paused && this._instances.size > 0) {\n            this.resume();\n            return;\n        }\n\n        instance.onEndedObservable.addOnce(this._onInstanceEnded);\n        this._privateInstances.add(instance);\n        this._newestInstance = instance;\n    }\n\n    protected _afterPlay(instance: _AbstractSoundInstance): void {\n        this._state = instance.state;\n    }\n\n    protected _getNewestInstance(): Nullable<_AbstractSoundInstance> {\n        if (this._instances.size === 0) {\n            return null;\n        }\n\n        if (!this._newestInstance) {\n            const it = this._instances.values();\n            for (let next = it.next(); !next.done; next = it.next()) {\n                this._newestInstance = next.value;\n            }\n        }\n\n        return this._newestInstance;\n    }\n\n    protected _setState(state: SoundState): void {\n        this._state = state;\n    }\n\n    protected abstract _createInstance(): _AbstractSoundInstance;\n\n    protected _stopExcessInstances(): void {\n        if (this.maxInstances < Infinity) {\n            const numberOfInstancesToStop = Array.from(this._instances).filter((instance) => instance.state === SoundState.Started).length - this.maxInstances;\n            const it = this._instances.values();\n\n            for (let i = 0; i < numberOfInstancesToStop; i++) {\n                const instance = it.next().value;\n                instance.stop();\n            }\n        }\n    }\n\n    private _onInstanceEnded: (instance: _AbstractSoundInstance) => void = (instance) => {\n        if (this._newestInstance === instance) {\n            this._newestInstance = null;\n        }\n\n        this._privateInstances.delete(instance);\n\n        if (this._instances.size === 0) {\n            this._state = SoundState.Stopped;\n            this.onEndedObservable.notifyObservers(this);\n        }\n    };\n}\n"]}