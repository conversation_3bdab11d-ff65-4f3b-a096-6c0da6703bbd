import type { IDisposable } from "../scene.js";
import type { IActionEvent } from "./actionEvent.js";
import type { IAction } from "./action.js";
import type { Nullable } from "../types.js";
/**
 * Abstract class used to decouple action Manager from scene and meshes.
 * Do not instantiate.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions
 */
export declare abstract class AbstractActionManager implements IDisposable {
    /** Gets the list of active triggers */
    static Triggers: {
        [key: string]: number;
    };
    /** Gets the cursor to use when hovering items */
    hoverCursor: string;
    /** Gets the list of actions */
    actions: IAction[];
    /**
     * Gets or sets a boolean indicating that the manager is recursive meaning that it can trigger action from children
     */
    isRecursive: boolean;
    /**
     * Gets or sets a boolean indicating if this ActionManager should be disposed once the last Mesh using it is disposed
     */
    disposeWhenUnowned: boolean;
    /**
     * Releases all associated resources
     */
    abstract dispose(): void;
    /**
     * Does this action manager has pointer triggers
     */
    abstract get hasPointerTriggers(): boolean;
    /**
     * Does this action manager has pick triggers
     */
    abstract get hasPickTriggers(): boolean;
    /**
     * Process a specific trigger
     * @param trigger defines the trigger to process
     * @param evt defines the event details to be processed
     */
    abstract processTrigger(trigger: number, evt?: IActionEvent): void;
    /**
     * Does this action manager handles actions of any of the given triggers
     * @param triggers defines the triggers to be tested
     * @returns a boolean indicating whether one (or more) of the triggers is handled
     */
    abstract hasSpecificTriggers(triggers: number[]): boolean;
    /**
     * Does this action manager handles actions of any of the given triggers. This function takes two arguments for
     * speed.
     * @param triggerA defines the trigger to be tested
     * @param triggerB defines the trigger to be tested
     * @returns a boolean indicating whether one (or more) of the triggers is handled
     */
    abstract hasSpecificTriggers2(triggerA: number, triggerB: number): boolean;
    /**
     * Does this action manager handles actions of a given trigger
     * @param trigger defines the trigger to be tested
     * @param parameterPredicate defines an optional predicate to filter triggers by parameter
     * @returns whether the trigger is handled
     */
    abstract hasSpecificTrigger(trigger: number, parameterPredicate?: (parameter: any) => boolean): boolean;
    /**
     * Serialize this manager to a JSON object
     * @param name defines the property name to store this manager
     * @returns a JSON representation of this manager
     */
    abstract serialize(name: string): any;
    /**
     * Registers an action to this action manager
     * @param action defines the action to be registered
     * @returns the action amended (prepared) after registration
     */
    abstract registerAction(action: IAction): Nullable<IAction>;
    /**
     * Unregisters an action to this action manager
     * @param action defines the action to be unregistered
     * @returns a boolean indicating whether the action has been unregistered
     */
    abstract unregisterAction(action: IAction): boolean;
    /**
     * Does exist one action manager with at least one trigger
     **/
    static get HasTriggers(): boolean;
    /**
     * Does exist one action manager with at least one pick trigger
     **/
    static get HasPickTriggers(): boolean;
    /**
     * Does exist one action manager that handles actions of a given trigger
     * @param trigger defines the trigger to be tested
     * @returns a boolean indicating whether the trigger is handled by at least one action manager
     **/
    static HasSpecificTrigger(trigger: number): boolean;
}
