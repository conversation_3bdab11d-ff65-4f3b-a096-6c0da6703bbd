{"version": 3, "file": "engine.readTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.readTexture.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAqCtD,cAAc;AACd,OAAO,EAAE,0BAA0B,EAAE,MAAM,wCAAwC,CAAC;AACpF,OAAO,EAAE,0BAA0B,EAAE,CAAC;AAEtC,UAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAC1C,OAAwB,EACxB,KAAa,EACb,MAAc,EACd,SAAS,GAAG,CAAC,CAAC,EACd,KAAK,GAAG,CAAC,EACT,SAAoC,IAAI,EACxC,aAAa,GAAG,IAAI,EACpB,gBAAgB,GAAG,KAAK,EACxB,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC;IAEL,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,IAAI,CAAC,EAAE,EAAE,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAClE,CAAC;IACD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC;QAErC,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IACD,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAE3D,IAAI,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC;QACjB,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAAE,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;IACnK,CAAC;SAAM,CAAC;QACJ,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,iBAAiB,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;IACtI,CAAC;IAED,IAAI,QAAQ,GAAG,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC;IAEvG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACpB,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,EAAE,CAAC,aAAa;gBACjB,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,MAAM,GAAG,IAAI,UAAU,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;gBAChD,CAAC;gBACD,QAAQ,GAAG,EAAE,CAAC,aAAa,CAAC;gBAC5B,MAAM;YACV;gBACI,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,MAAM,GAAG,IAAI,YAAY,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;gBAClD,CAAC;gBACD,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC;gBACpB,MAAM;QACd,CAAC;IACL,CAAC;SAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QACjB,MAAM,GAAG,0BAA0B,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;IAC1E,CAAC;IAED,IAAI,aAAa,EAAE,CAAC;QAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAY,MAAM,CAAC,CAAC;IACxE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAE7D,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,qEAAqE;AACrE,UAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,UACtC,OAAwB,EACxB,KAAa,EACb,MAAc,EACd,SAAS,GAAG,CAAC,CAAC,EACd,KAAK,GAAG,CAAC,EACT,SAAoC,IAAI,EACxC,aAAa,GAAG,IAAI,EACpB,gBAAgB,GAAG,KAAK,EACxB,CAAC,GAAG,CAAC,EACL,CAAC,GAAG,CAAC;IAEL,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjJ,CAAC,CAAC", "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /** @internal */\r\n        _readTexturePixels(\r\n            texture: InternalTexture,\r\n            width: number,\r\n            height: number,\r\n            faceIndex?: number,\r\n            level?: number,\r\n            buffer?: Nullable<ArrayBufferView>,\r\n            flushRenderer?: boolean,\r\n            noDataConversion?: boolean,\r\n            x?: number,\r\n            y?: number\r\n        ): Promise<ArrayBufferView>;\r\n\r\n        /** @internal */\r\n        _readTexturePixelsSync(\r\n            texture: InternalTexture,\r\n            width: number,\r\n            height: number,\r\n            faceIndex?: number,\r\n            level?: number,\r\n            buffer?: Nullable<ArrayBufferView>,\r\n            flushRenderer?: boolean,\r\n            noDataConversion?: boolean,\r\n            x?: number,\r\n            y?: number\r\n        ): ArrayBufferView;\r\n    }\r\n}\r\n\r\n// back-compat\r\nimport { allocateAndCopyTypedBuffer } from \"../../Engines/abstractEngine.functions\";\r\nexport { allocateAndCopyTypedBuffer };\r\n\r\nThinEngine.prototype._readTexturePixelsSync = function (\r\n    texture: InternalTexture,\r\n    width: number,\r\n    height: number,\r\n    faceIndex = -1,\r\n    level = 0,\r\n    buffer: Nullable<ArrayBufferView> = null,\r\n    flushRenderer = true,\r\n    noDataConversion = false,\r\n    x = 0,\r\n    y = 0\r\n): ArrayBufferView {\r\n    const gl = this._gl;\r\n    if (!gl) {\r\n        throw new Error(\"Engine does not have gl rendering context.\");\r\n    }\r\n    if (!this._dummyFramebuffer) {\r\n        const dummy = gl.createFramebuffer();\r\n\r\n        if (!dummy) {\r\n            throw new Error(\"Unable to create dummy framebuffer\");\r\n        }\r\n\r\n        this._dummyFramebuffer = dummy;\r\n    }\r\n    gl.bindFramebuffer(gl.FRAMEBUFFER, this._dummyFramebuffer);\r\n\r\n    if (faceIndex > -1) {\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex, texture._hardwareTexture?.underlyingResource, level);\r\n    } else {\r\n        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture._hardwareTexture?.underlyingResource, level);\r\n    }\r\n\r\n    let readType = texture.type !== undefined ? this._getWebGLTextureType(texture.type) : gl.UNSIGNED_BYTE;\r\n\r\n    if (!noDataConversion) {\r\n        switch (readType) {\r\n            case gl.UNSIGNED_BYTE:\r\n                if (!buffer) {\r\n                    buffer = new Uint8Array(4 * width * height);\r\n                }\r\n                readType = gl.UNSIGNED_BYTE;\r\n                break;\r\n            default:\r\n                if (!buffer) {\r\n                    buffer = new Float32Array(4 * width * height);\r\n                }\r\n                readType = gl.FLOAT;\r\n                break;\r\n        }\r\n    } else if (!buffer) {\r\n        buffer = allocateAndCopyTypedBuffer(texture.type, 4 * width * height);\r\n    }\r\n\r\n    if (flushRenderer) {\r\n        this.flushFramebuffer();\r\n    }\r\n\r\n    gl.readPixels(x, y, width, height, gl.RGBA, readType, <DataView>buffer);\r\n    gl.bindFramebuffer(gl.FRAMEBUFFER, this._currentFramebuffer);\r\n\r\n    return buffer;\r\n};\r\n\r\n// eslint-disable-next-line @typescript-eslint/promise-function-async\r\nThinEngine.prototype._readTexturePixels = function (\r\n    texture: InternalTexture,\r\n    width: number,\r\n    height: number,\r\n    faceIndex = -1,\r\n    level = 0,\r\n    buffer: Nullable<ArrayBufferView> = null,\r\n    flushRenderer = true,\r\n    noDataConversion = false,\r\n    x = 0,\r\n    y = 0\r\n): Promise<ArrayBufferView> {\r\n    return Promise.resolve(this._readTexturePixelsSync(texture, width, height, faceIndex, level, buffer, flushRenderer, noDataConversion, x, y));\r\n};\r\n"]}