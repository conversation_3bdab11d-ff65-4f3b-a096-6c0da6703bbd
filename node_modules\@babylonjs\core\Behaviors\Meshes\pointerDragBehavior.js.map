{"version": 3, "file": "pointerDragBehavior.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Behaviors/Meshes/pointerDragBehavior.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAGpC,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAE9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAC;AAC/D,OAAO,EAAE,GAAG,EAAE,MAAM,mBAAmB,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAEnD,OAAO,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAC;AAGjE,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAErD;;GAEG;AACH,MAAM,OAAO,mBAAmB;IA0B5B;;;OAGG;IACH,gEAAgE;IAChE,IAAW,wBAAwB;QAC/B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IACD,gEAAgE;IAChE,IAAW,wBAAwB,CAAC,wBAAgC;QAChE,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;IAC7D,CAAC;IAiED;;OAEG;IACH,IAAW,OAAO,CAAC,KAAc;QAC7B,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzB,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAyBD;;OAEG;IACH,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO,CAAC,OAA0D;QACzE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAED;;;;;OAKG;IACH,YAAY,OAA2D;QApJ/D,uDAAkD,GAAG,CAAC,GAAG,CAAC;QAC1D,sBAAiB,GAAW,CAAC,CAAC,CAAC;QAEvC;;WAEG;QACI,iBAAY,GAAG,CAAC,CAAC;QACxB;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B;;WAEG;QACI,8CAAyC,GAAG,KAAK,CAAC;QAazD;;WAEG;QACI,6BAAwB,GAAG,CAAC,CAAC,CAAC;QAKrC;;WAEG;QACI,aAAQ,GAAG,KAAK,CAAC;QACxB;;WAEG;QACI,mBAAc,GAAG,GAAG,CAAC;QAC5B;;WAEG;QACI,oBAAe,GAAG,IAAI,CAAC;QAC9B,iEAAiE;QACzD,eAAU,GAAG,KAAK,CAAC;QACnB,YAAO,GAAG,KAAK,CAAC;QACxB;;;;;;;;WAQG;QACI,qBAAgB,GAAG,IAAI,UAAU,EAOpC,CAAC;QACL;;;;;WAKG;QACI,0BAAqB,GAAG,IAAI,UAAU,EAAsF,CAAC;QACpI;;;;;WAKG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAsF,CAAC;QAClI;;WAEG;QACI,wBAAmB,GAAG,IAAI,UAAU,EAAW,CAAC;QAEvD;;WAEG;QACI,iBAAY,GAAG,IAAI,CAAC;QAenB,aAAQ,GAAG,IAAI,CAAC;QAExB;;WAEG;QACI,uCAAkC,GAAG,IAAI,CAAC;QACjD;;WAEG;QACI,yBAAoB,GAAG,IAAI,CAAC;QAEnC;;WAEG;QACI,oCAA+B,GAAG,IAAI,CAAC;QAE9C;;;WAGG;QACI,gCAA2B,GAAG,KAAK,CAAC;QAwC3C;;;;;WAKG;QACH,6DAA6D;QACtD,iBAAY,GAAG,CAAC,MAAe,EAAE,EAAE;YACtC,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC;QAcM,eAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,0BAAqB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,mBAAc,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtC,oBAAe,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,uBAAkB,GAAY,KAAK,CAAC;QAyKpC,kBAAa,GAAG,IAAI,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;QACtD,oBAAe,GAA2B,EAAE,CAAC;QA8D7C,eAAU,GAAG,IAAI,OAAO,EAAE,CAAC;QAmGnC,uDAAuD;QAC/C,YAAO,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,YAAO,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,eAAU,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClC,YAAO,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAzXnC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;QAEvC,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACzB,WAAW,EAAE,CAAC;QAClB,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;YAChC,WAAW,EAAE,CAAC;QAClB,CAAC;QACD,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YAClB,4CAA4C;YAC5C,MAAM,0EAA0E,CAAC;QACrF,CAAC;IACL,CAAC;IAaD;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,IAAI,KAAI,CAAC;IAOhB;;;;OAIG;IACI,MAAM,CAAC,SAAuB,EAAE,SAAwC;QAC3E,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACnC,SAAS,CAAC,eAAe,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAE9B,6DAA6D;QAC7D,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACJ,mBAAmB,CAAC,WAAW,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBACxF,mBAAmB,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;gBAChD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE;oBACzC,mBAAmB,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oBACpC,mBAAmB,CAAC,WAAY,GAAG,IAAI,CAAC;gBAClD,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,WAAW,CACzB,kBAAkB,EAClB,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,CAAC,UAAU,EAAE,EACzF,mBAAmB,CAAC,WAAW,CAClC,CAAC;QAEF,oBAAoB;QACpB,IAAI,CAAC,gBAAgB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAE7C,MAAM,aAAa,GAAG,SAAS;YAC3B,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,CAAC,CAAe,EAAE,EAAE;gBAChB,OAAO,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzE,CAAC,CAAC;QAER,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;YACxE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,0EAA0E;gBAC1E,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,IAAI,CAAC,WAAW,EAAE,CAAC;gBACvB,CAAC;gBAED,OAAO;YACX,CAAC;YAED,uGAAuG;YACvG,0DAA0D;YAC1D,iCAAiC;YACjC,IACI,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,wBAAwB,IAAoB,WAAW,CAAC,KAAM,CAAC,SAAS;gBAC7E,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;gBAC/B,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,iBAAiB;gBACnD,CAAC,IAAI,CAAC,2BAA2B,EACnC,CAAC;gBACC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnB,OAAO;YACX,CAAC;YAED,IAAI,WAAW,CAAC,IAAI,IAAI,iBAAiB,CAAC,WAAW,EAAE,CAAC;gBACpD,IACI,IAAI,CAAC,kCAAkC;oBACvC,CAAC,IAAI,CAAC,QAAQ;oBACd,WAAW,CAAC,QAAQ;oBACpB,WAAW,CAAC,QAAQ,CAAC,GAAG;oBACxB,WAAW,CAAC,QAAQ,CAAC,UAAU;oBAC/B,WAAW,CAAC,QAAQ,CAAC,WAAW;oBAChC,WAAW,CAAC,QAAQ,CAAC,GAAG;oBACxB,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAChD,CAAC;oBACC,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;wBAC7F,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC;wBAClD,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;wBACtC,IAAI,CAAC,UAAU,CAAiB,WAAW,CAAC,KAAM,CAAC,SAAS,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBAC9H,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,IAAI,WAAW,CAAC,IAAI,IAAI,iBAAiB,CAAC,SAAS,EAAE,CAAC;gBACzD,IACI,IAAI,CAAC,kCAAkC;oBACvC,IAAI,CAAC,wBAAwB,IAAoB,WAAW,CAAC,KAAM,CAAC,SAAS;oBAC7E,CAAC,IAAI,CAAC,iBAAiB,KAAK,WAAW,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,CAAC,EACxF,CAAC;oBACC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACvB,CAAC;YACL,CAAC;iBAAM,IAAI,WAAW,CAAC,IAAI,IAAI,iBAAiB,CAAC,WAAW,EAAE,CAAC;gBAC3D,MAAM,SAAS,GAAmB,WAAW,CAAC,KAAM,CAAC,SAAS,CAAC;gBAE/D,4FAA4F;gBAC5F,IAAI,IAAI,CAAC,wBAAwB,KAAK,mBAAmB,CAAC,WAAW,IAAI,SAAS,KAAK,mBAAmB,CAAC,WAAW,EAAE,CAAC;oBACrH,MAAM,GAAG,GAAkB,WAAW,CAAC,KAAK,CAAC;oBAC7C,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC,QAAQ,IAAI,GAAG,YAAY,UAAU,CAAC,CAAC;oBACrI,IAAI,YAAY,EAAE,CAAC;wBACf,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC;4BACtD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;4BACtF,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;wBAC/D,CAAC;wBACD,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;oBAC9C,CAAC;gBACL,CAAC;gBAED,6FAA6F;gBAC7F,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;oBACnC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,OAAO,EAAE,EAAE,IAAI,OAAO,EAAE,CAAC,CAAC;gBAC5E,CAAC;gBACD,IAAI,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;oBACnD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACjF,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAEvF,IAAI,IAAI,CAAC,wBAAwB,IAAI,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAC9D,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;oBAC7C,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE;YACvE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpC,IAAI,gBAAgB,GAAG,KAAK,CAAC;gBAC7B,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACxD,mCAAmC;gBACnC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACxF,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAClD,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBACnF,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBACrC,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACvD,gBAAgB,GAAG,IAAI,CAAC;gBAC5B,CAAC;gBACD,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACjD,IAAI,gBAAgB,EAAE,CAAC;oBACnB,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;gBAC3C,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,WAAW;QACd,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,CAAC,wBAAwB,EAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACxK,CAAC;QAED,IAAI,CAAC,wBAAwB,GAAG,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QAErB,2BAA2B;QAC3B,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YAC3H,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,iBAAiB,EAAE,CAAC;gBAChE,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,YAA+B,CAAC;gBACpE,eAAe,CAAC,aAAa,CACzB,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,EACvE,eAAe,CAAC,kBAAkB,EAClC,eAAe,CAAC,mBAAmB,CACtC,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACtI,CAAC;YACD,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QACpC,CAAC;IACL,CAAC;IAID;;;;;OAKG;IACI,SAAS,CAAC,YAAoB,mBAAmB,CAAC,WAAW,EAAE,OAAa,EAAE,gBAA0B;QAC3G,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAEtD,IAAI,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,SAAS,KAAK,mBAAmB,CAAC,WAAW,EAAE,CAAC;YAChD,OAAO,GAAG,IAAI,CAAC,eAAe,CAAM,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,wDAAwD;YACxD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;IACL,CAAC;IAES,UAAU,CAAC,SAAiB,EAAE,OAAa,EAAE,gBAA0B;QAC7E,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACnE,OAAO;QACX,CAAC;QAED,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxD,iDAAiD;QACjD,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACvD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YACtE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACxE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEzG,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrE,IAAI,WAAW,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;YAC1C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC5C,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,wBAAwB,EAAE,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;YAC5J,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC;YAEvE,0BAA0B;YAC1B,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBACnI,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;oBACpD,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;oBACzC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;gBACpC,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,WAAW,EAAE,CAAC;QACvB,CAAC;QACD,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACrD,CAAC;IAGS,SAAS,CAAC,GAAQ;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;QAEtD,IAAI,WAAW,EAAE,CAAC;YACd,UAAU,CAAC,yBAAyB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAExD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,wBAAwB,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YACpD,CAAC;YACD,IAAI,UAAU,GAAG,CAAC,CAAC;YACnB,qDAAqD;YACrD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACzB,sEAAsE;gBACtE,IAAI,CAAC,+BAA+B;oBAChC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC;oBACxI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC3D,4DAA4D;gBAC5D,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBAElE,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;gBAChC,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC/D,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YAChE,CAAC;iBAAM,CAAC;gBACJ,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;gBACtC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACtE,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACjD,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBAClC,YAAY,EAAE,UAAU;gBACxB,KAAK,EAAE,IAAI,CAAC,UAAU;gBACtB,cAAc,EAAE,WAAW;gBAC3B,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO;gBACxC,SAAS,EAAE,IAAI,CAAC,wBAAwB;gBACxC,WAAW,EAAE,IAAI,CAAC,kBAAkB;aACvC,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAE5C,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAEO,uBAAuB,CAAC,GAAkB;QAC9C,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,+CAA+C;QAC/C,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;QAC3E,6CAA6C;QAC7C,IAAI,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;YACtB,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC;QAC5B,CAAC;QAED,qGAAqG;QACrG,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACrD,IAAI,IAAI,CAAC,yCAAyC,EAAE,CAAC;gBACjD,qDAAqD;gBACrD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACxC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACzF,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,CAAC;gBACvC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,IAAI,CAAC,kDAAkD,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;gBAC5J,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBAEvD,wHAAwH;gBACxH,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;gBACrE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACvD,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;gBAC1E,OAAO,IAAI,CAAC,qBAAqB,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACJ,OAAO,IAAI,CAAC;YAChB,CAAC;QACL,CAAC;QAED,uFAAuF;QACvF,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;QAC5C,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC/C,MAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,OAAO,EAAE,CAAC;YACjC,8CAA8C;YAC9C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC;QAC9D,4EAA4E;QAC5E,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACR,uCAAuC;YACvC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,yDAAyD;QACzD,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,MAAM,iBAAiB,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAOD,kIAAkI;IAC1H,wBAAwB,CAAC,GAAQ,EAAE,iBAA0B;QACjE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACzB,IAAI,CAAC,+BAA+B;gBAChC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC;gBACpI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAEvD,6FAA6F;YAC7F,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;gBAC/D,yGAAyG;gBACzG,wCAAwC;gBACxC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC;oBAClE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;gBAC3C,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAC9C,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,0GAA0G;gBAC1G,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;gBAChE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC7B,CAAC;YAED,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAClD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;YACvC,IAAI,CAAC,+BAA+B;gBAChC,CAAC,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC;gBAC3I,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;YAC9D,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACrD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACJ,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACvF,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9D,CAAC;QACD,6HAA6H;QAC7H,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAE3E,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,MAAM;QACT,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,KAAK,CAAC;QAC9C,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClE,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC7B,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC5E,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;;AA5lBc,+BAAW,GAAG,CAAC,CAAC,AAAL,CAAM", "sourcesContent": ["import type { Behavior } from \"../../Behaviors/behavior\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Scene } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { TmpVectors, Vector3 } from \"../../Maths/math.vector\";\r\nimport type { PointerInfo } from \"../../Events/pointerEvents\";\r\nimport { PointerEventTypes } from \"../../Events/pointerEvents\";\r\nimport { Ray } from \"../../Culling/ray\";\r\nimport { PivotTools } from \"../../Misc/pivotTools\";\r\nimport type { ArcRotateCamera } from \"../../Cameras/arcRotateCamera\";\r\nimport { CreatePlane } from \"../../Meshes/Builders/planeBuilder\";\r\n\r\nimport type { IPointerEvent } from \"../../Events/deviceInputEvents\";\r\nimport { Epsilon } from \"../../Maths/math.constants\";\r\n\r\n/**\r\n * A behavior that when attached to a mesh will allow the mesh to be dragged around the screen based on pointer events\r\n */\r\nexport class PointerDragBehavior implements Behavior<AbstractMesh> {\r\n    private static _AnyMouseId = -2;\r\n    /**\r\n     * Abstract mesh the behavior is set on\r\n     */\r\n    public attachedNode: AbstractMesh;\r\n    protected _dragPlane: Mesh;\r\n    private _scene: Scene;\r\n    private _pointerObserver: Nullable<Observer<PointerInfo>>;\r\n    private _beforeRenderObserver: Nullable<Observer<Scene>>;\r\n    private static _PlaneScene: Scene;\r\n    private _useAlternatePickedPointAboveMaxDragAngleDragSpeed = -1.1;\r\n    private _activeDragButton: number = -1;\r\n    private _activePointerInfo: Nullable<PointerInfo>;\r\n    /**\r\n     * The maximum tolerated angle between the drag plane and dragging pointer rays to trigger pointer events. Set to 0 to allow any angle (default: 0)\r\n     */\r\n    public maxDragAngle = 0;\r\n    /**\r\n     * Butttons that can be used to initiate a drag\r\n     */\r\n    public dragButtons = [0, 1, 2];\r\n    /**\r\n     * @internal\r\n     */\r\n    public _useAlternatePickedPointAboveMaxDragAngle = false;\r\n    /**\r\n     * Get or set the currentDraggingPointerId\r\n     * @deprecated Please use currentDraggingPointerId instead\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public get currentDraggingPointerID(): number {\r\n        return this.currentDraggingPointerId;\r\n    }\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public set currentDraggingPointerID(currentDraggingPointerId: number) {\r\n        this.currentDraggingPointerId = currentDraggingPointerId;\r\n    }\r\n    /**\r\n     * The id of the pointer that is currently interacting with the behavior (-1 when no pointer is active)\r\n     */\r\n    public currentDraggingPointerId = -1;\r\n    /**\r\n     * The last position where the pointer hit the drag plane in world space\r\n     */\r\n    public lastDragPosition: Vector3;\r\n    /**\r\n     * If the behavior is currently in a dragging state\r\n     */\r\n    public dragging = false;\r\n    /**\r\n     * The distance towards the target drag position to move each frame. This can be useful to avoid jitter. Set this to 1 for no delay. (Default: 0.2)\r\n     */\r\n    public dragDeltaRatio = 0.2;\r\n    /**\r\n     * If the drag plane orientation should be updated during the dragging (Default: true)\r\n     */\r\n    public updateDragPlane = true;\r\n    // Debug mode will display drag planes to help visualize behavior\r\n    private _debugMode = false;\r\n    private _moving = false;\r\n    /**\r\n     *  Fires each time the attached mesh is dragged with the pointer\r\n     *  * delta between last drag position and current drag position in world space\r\n     *  * dragDistance along the drag axis\r\n     *  * dragPlaneNormal normal of the current drag plane used during the drag\r\n     *  * dragPlanePoint in world space where the drag intersects the drag plane\r\n     *\r\n     *  (if validatedDrag is used, the position of the attached mesh might not equal dragPlanePoint)\r\n     */\r\n    public onDragObservable = new Observable<{\r\n        delta: Vector3;\r\n        dragPlanePoint: Vector3;\r\n        dragPlaneNormal: Vector3;\r\n        dragDistance: number;\r\n        pointerId: number;\r\n        pointerInfo: Nullable<PointerInfo>;\r\n    }>();\r\n    /**\r\n     *  Fires each time a drag begins (eg. mouse down on mesh)\r\n     *  * dragPlanePoint in world space where the drag intersects the drag plane\r\n     *\r\n     *  (if validatedDrag is used, the position of the attached mesh might not equal dragPlanePoint)\r\n     */\r\n    public onDragStartObservable = new Observable<{ dragPlanePoint: Vector3; pointerId: number; pointerInfo: Nullable<PointerInfo> }>();\r\n    /**\r\n     *  Fires each time a drag ends (eg. mouse release after drag)\r\n     *  * dragPlanePoint in world space where the drag intersects the drag plane\r\n     *\r\n     *  (if validatedDrag is used, the position of the attached mesh might not equal dragPlanePoint)\r\n     */\r\n    public onDragEndObservable = new Observable<{ dragPlanePoint: Vector3; pointerId: number; pointerInfo: Nullable<PointerInfo> }>();\r\n    /**\r\n     *  Fires each time behavior enabled state changes\r\n     */\r\n    public onEnabledObservable = new Observable<boolean>();\r\n\r\n    /**\r\n     *  If the attached mesh should be moved when dragged\r\n     */\r\n    public moveAttached = true;\r\n\r\n    /**\r\n     *  If the drag behavior will react to drag events (Default: true)\r\n     */\r\n    public set enabled(value: boolean) {\r\n        if (value != this._enabled) {\r\n            this.onEnabledObservable.notifyObservers(value);\r\n        }\r\n        this._enabled = value;\r\n    }\r\n\r\n    public get enabled() {\r\n        return this._enabled;\r\n    }\r\n    private _enabled = true;\r\n\r\n    /**\r\n     * If pointer events should start and release the drag (Default: true)\r\n     */\r\n    public startAndReleaseDragOnPointerEvents = true;\r\n    /**\r\n     * If camera controls should be detached during the drag\r\n     */\r\n    public detachCameraControls = true;\r\n\r\n    /**\r\n     * If set, the drag plane/axis will be rotated based on the attached mesh's world rotation (Default: true)\r\n     */\r\n    public useObjectOrientationForDragging = true;\r\n\r\n    /**\r\n     * Normally a drag is canceled when the user presses another button on the same pointer. If this is set to true,\r\n     * the drag will continue even if another button is pressed on the same pointer.\r\n     */\r\n    public allowOtherButtonsDuringDrag = false;\r\n\r\n    private _options: { dragAxis?: Vector3; dragPlaneNormal?: Vector3 };\r\n\r\n    /**\r\n     * Gets the options used by the behavior\r\n     */\r\n    public get options(): { dragAxis?: Vector3; dragPlaneNormal?: Vector3 } {\r\n        return this._options;\r\n    }\r\n\r\n    /**\r\n     * Sets the options used by the behavior\r\n     */\r\n    public set options(options: { dragAxis?: Vector3; dragPlaneNormal?: Vector3 }) {\r\n        this._options = options;\r\n    }\r\n\r\n    /**\r\n     * Creates a pointer drag behavior that can be attached to a mesh\r\n     * @param options The drag axis or normal of the plane that will be dragged across. If no options are specified the drag plane will always face the ray's origin (eg. camera)\r\n     * @param options.dragAxis\r\n     * @param options.dragPlaneNormal\r\n     */\r\n    constructor(options?: { dragAxis?: Vector3; dragPlaneNormal?: Vector3 }) {\r\n        this._options = options ? options : {};\r\n\r\n        let optionCount = 0;\r\n        if (this._options.dragAxis) {\r\n            optionCount++;\r\n        }\r\n        if (this._options.dragPlaneNormal) {\r\n            optionCount++;\r\n        }\r\n        if (optionCount > 1) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw \"Multiple drag modes specified in dragBehavior options. Only one expected\";\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Predicate to determine if it is valid to move the object to a new position when it is moved.\r\n     * In the case of rotation gizmo, target contains the angle.\r\n     * @param target destination position or desired angle delta\r\n     * @returns boolean for whether or not it is valid to move\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public validateDrag = (target: Vector3) => {\r\n        return true;\r\n    };\r\n\r\n    /**\r\n     *  The name of the behavior\r\n     */\r\n    public get name(): string {\r\n        return \"PointerDrag\";\r\n    }\r\n\r\n    /**\r\n     *  Initializes the behavior\r\n     */\r\n    public init() {}\r\n\r\n    private _tmpVector = new Vector3(0, 0, 0);\r\n    private _alternatePickedPoint = new Vector3(0, 0, 0);\r\n    private _worldDragAxis = new Vector3(0, 0, 0);\r\n    private _targetPosition = new Vector3(0, 0, 0);\r\n    private _attachedToElement: boolean = false;\r\n    /**\r\n     * Attaches the drag behavior the passed in mesh\r\n     * @param ownerNode The mesh that will be dragged around once attached\r\n     * @param predicate Predicate to use for pick filtering\r\n     */\r\n    public attach(ownerNode: AbstractMesh, predicate?: (m: AbstractMesh) => boolean): void {\r\n        this._scene = ownerNode.getScene();\r\n        ownerNode.isNearGrabbable = true;\r\n        this.attachedNode = ownerNode;\r\n\r\n        // Initialize drag plane to not interfere with existing scene\r\n        if (!PointerDragBehavior._PlaneScene) {\r\n            if (this._debugMode) {\r\n                PointerDragBehavior._PlaneScene = this._scene;\r\n            } else {\r\n                PointerDragBehavior._PlaneScene = new Scene(this._scene.getEngine(), { virtual: true });\r\n                PointerDragBehavior._PlaneScene.detachControl();\r\n                this._scene.onDisposeObservable.addOnce(() => {\r\n                    PointerDragBehavior._PlaneScene.dispose();\r\n                    (<any>PointerDragBehavior._PlaneScene) = null;\r\n                });\r\n            }\r\n        }\r\n        this._dragPlane = CreatePlane(\r\n            \"pointerDragPlane\",\r\n            { size: this._debugMode ? 1 : 10000, updatable: false, sideOrientation: Mesh.DOUBLESIDE },\r\n            PointerDragBehavior._PlaneScene\r\n        );\r\n\r\n        // State of the drag\r\n        this.lastDragPosition = new Vector3(0, 0, 0);\r\n\r\n        const pickPredicate = predicate\r\n            ? predicate\r\n            : (m: AbstractMesh) => {\r\n                  return this.attachedNode == m || m.isDescendantOf(this.attachedNode);\r\n              };\r\n\r\n        this._pointerObserver = this._scene.onPointerObservable.add((pointerInfo) => {\r\n            if (!this.enabled) {\r\n                // If behavior is disabled before releaseDrag is ever called, call it now.\r\n                if (this._attachedToElement) {\r\n                    this.releaseDrag();\r\n                }\r\n\r\n                return;\r\n            }\r\n\r\n            // If we are dragging and the user presses another button on the same pointer, end the drag. Otherwise,\r\n            // tracking when the drag should end becomes very complex.\r\n            // gizmo.ts has similar behavior.\r\n            if (\r\n                this.dragging &&\r\n                this.currentDraggingPointerId == (<IPointerEvent>pointerInfo.event).pointerId &&\r\n                pointerInfo.event.button !== -1 &&\r\n                pointerInfo.event.button !== this._activeDragButton &&\r\n                !this.allowOtherButtonsDuringDrag\r\n            ) {\r\n                this.releaseDrag();\r\n                return;\r\n            }\r\n\r\n            if (pointerInfo.type == PointerEventTypes.POINTERDOWN) {\r\n                if (\r\n                    this.startAndReleaseDragOnPointerEvents &&\r\n                    !this.dragging &&\r\n                    pointerInfo.pickInfo &&\r\n                    pointerInfo.pickInfo.hit &&\r\n                    pointerInfo.pickInfo.pickedMesh &&\r\n                    pointerInfo.pickInfo.pickedPoint &&\r\n                    pointerInfo.pickInfo.ray &&\r\n                    pickPredicate(pointerInfo.pickInfo.pickedMesh)\r\n                ) {\r\n                    if (this._activeDragButton === -1 && this.dragButtons.indexOf(pointerInfo.event.button) !== -1) {\r\n                        this._activeDragButton = pointerInfo.event.button;\r\n                        this._activePointerInfo = pointerInfo;\r\n                        this._startDrag((<IPointerEvent>pointerInfo.event).pointerId, pointerInfo.pickInfo.ray, pointerInfo.pickInfo.pickedPoint);\r\n                    }\r\n                }\r\n            } else if (pointerInfo.type == PointerEventTypes.POINTERUP) {\r\n                if (\r\n                    this.startAndReleaseDragOnPointerEvents &&\r\n                    this.currentDraggingPointerId == (<IPointerEvent>pointerInfo.event).pointerId &&\r\n                    (this._activeDragButton === pointerInfo.event.button || this._activeDragButton === -1)\r\n                ) {\r\n                    this.releaseDrag();\r\n                }\r\n            } else if (pointerInfo.type == PointerEventTypes.POINTERMOVE) {\r\n                const pointerId = (<IPointerEvent>pointerInfo.event).pointerId;\r\n\r\n                // If drag was started with anyMouseID specified, set pointerID to the next mouse that moved\r\n                if (this.currentDraggingPointerId === PointerDragBehavior._AnyMouseId && pointerId !== PointerDragBehavior._AnyMouseId) {\r\n                    const evt = <IPointerEvent>pointerInfo.event;\r\n                    const isMouseEvent = evt.pointerType === \"mouse\" || (!this._scene.getEngine().hostInformation.isMobile && evt instanceof MouseEvent);\r\n                    if (isMouseEvent) {\r\n                        if (this._lastPointerRay[this.currentDraggingPointerId]) {\r\n                            this._lastPointerRay[pointerId] = this._lastPointerRay[this.currentDraggingPointerId];\r\n                            delete this._lastPointerRay[this.currentDraggingPointerId];\r\n                        }\r\n                        this.currentDraggingPointerId = pointerId;\r\n                    }\r\n                }\r\n\r\n                // Keep track of last pointer ray, this is used simulating the start of a drag in startDrag()\r\n                if (!this._lastPointerRay[pointerId]) {\r\n                    this._lastPointerRay[pointerId] = new Ray(new Vector3(), new Vector3());\r\n                }\r\n                if (pointerInfo.pickInfo && pointerInfo.pickInfo.ray) {\r\n                    this._lastPointerRay[pointerId].origin.copyFrom(pointerInfo.pickInfo.ray.origin);\r\n                    this._lastPointerRay[pointerId].direction.copyFrom(pointerInfo.pickInfo.ray.direction);\r\n\r\n                    if (this.currentDraggingPointerId == pointerId && this.dragging) {\r\n                        this._moveDrag(pointerInfo.pickInfo.ray);\r\n                    }\r\n                }\r\n            }\r\n        });\r\n\r\n        this._beforeRenderObserver = this._scene.onBeforeRenderObservable.add(() => {\r\n            if (this._moving && this.moveAttached) {\r\n                let needMatrixUpdate = false;\r\n                PivotTools._RemoveAndStorePivotPoint(this.attachedNode);\r\n                // Slowly move mesh to avoid jitter\r\n                this._targetPosition.subtractToRef(this.attachedNode.absolutePosition, this._tmpVector);\r\n                this._tmpVector.scaleInPlace(this.dragDeltaRatio);\r\n                this.attachedNode.getAbsolutePosition().addToRef(this._tmpVector, this._tmpVector);\r\n                if (this.validateDrag(this._tmpVector)) {\r\n                    this.attachedNode.setAbsolutePosition(this._tmpVector);\r\n                    needMatrixUpdate = true;\r\n                }\r\n                PivotTools._RestorePivotPoint(this.attachedNode);\r\n                if (needMatrixUpdate) {\r\n                    this.attachedNode.computeWorldMatrix();\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Force release the drag action by code.\r\n     */\r\n    public releaseDrag() {\r\n        if (this.dragging) {\r\n            this.dragging = false;\r\n            this.onDragEndObservable.notifyObservers({ dragPlanePoint: this.lastDragPosition, pointerId: this.currentDraggingPointerId, pointerInfo: this._activePointerInfo });\r\n        }\r\n\r\n        this.currentDraggingPointerId = -1;\r\n        this._activeDragButton = -1;\r\n        this._activePointerInfo = null;\r\n        this._moving = false;\r\n\r\n        // Reattach camera controls\r\n        if (this.detachCameraControls && this._attachedToElement && this._scene.activeCamera && !this._scene.activeCamera.leftCamera) {\r\n            if (this._scene.activeCamera.getClassName() === \"ArcRotateCamera\") {\r\n                const arcRotateCamera = this._scene.activeCamera as ArcRotateCamera;\r\n                arcRotateCamera.attachControl(\r\n                    arcRotateCamera.inputs ? arcRotateCamera.inputs.noPreventDefault : true,\r\n                    arcRotateCamera._useCtrlForPanning,\r\n                    arcRotateCamera._panningMouseButton\r\n                );\r\n            } else {\r\n                this._scene.activeCamera.attachControl(this._scene.activeCamera.inputs ? this._scene.activeCamera.inputs.noPreventDefault : true);\r\n            }\r\n            this._attachedToElement = false;\r\n        }\r\n    }\r\n\r\n    private _startDragRay = new Ray(new Vector3(), new Vector3());\r\n    private _lastPointerRay: { [key: number]: Ray } = {};\r\n    /**\r\n     * Simulates the start of a pointer drag event on the behavior\r\n     * @param pointerId pointerID of the pointer that should be simulated (Default: Any mouse pointer ID)\r\n     * @param fromRay initial ray of the pointer to be simulated (Default: Ray from camera to attached mesh)\r\n     * @param startPickedPoint picked point of the pointer to be simulated (Default: attached mesh position)\r\n     */\r\n    public startDrag(pointerId: number = PointerDragBehavior._AnyMouseId, fromRay?: Ray, startPickedPoint?: Vector3) {\r\n        this._startDrag(pointerId, fromRay, startPickedPoint);\r\n\r\n        let lastRay = this._lastPointerRay[pointerId];\r\n        if (pointerId === PointerDragBehavior._AnyMouseId) {\r\n            lastRay = this._lastPointerRay[<any>Object.keys(this._lastPointerRay)[0]];\r\n        }\r\n\r\n        if (lastRay) {\r\n            // if there was a last pointer ray drag the object there\r\n            this._moveDrag(lastRay);\r\n        }\r\n    }\r\n\r\n    protected _startDrag(pointerId: number, fromRay?: Ray, startPickedPoint?: Vector3) {\r\n        if (!this._scene.activeCamera || this.dragging || !this.attachedNode) {\r\n            return;\r\n        }\r\n\r\n        PivotTools._RemoveAndStorePivotPoint(this.attachedNode);\r\n        // Create start ray from the camera to the object\r\n        if (fromRay) {\r\n            this._startDragRay.direction.copyFrom(fromRay.direction);\r\n            this._startDragRay.origin.copyFrom(fromRay.origin);\r\n        } else {\r\n            this._startDragRay.origin.copyFrom(this._scene.activeCamera.position);\r\n            this.attachedNode.getWorldMatrix().getTranslationToRef(this._tmpVector);\r\n            this._tmpVector.subtractToRef(this._scene.activeCamera.position, this._startDragRay.direction);\r\n        }\r\n\r\n        this._updateDragPlanePosition(this._startDragRay, startPickedPoint ? startPickedPoint : this._tmpVector);\r\n\r\n        const pickedPoint = this._pickWithRayOnDragPlane(this._startDragRay);\r\n        if (pickedPoint) {\r\n            this.dragging = true;\r\n            this.currentDraggingPointerId = pointerId;\r\n            this.lastDragPosition.copyFrom(pickedPoint);\r\n            this.onDragStartObservable.notifyObservers({ dragPlanePoint: pickedPoint, pointerId: this.currentDraggingPointerId, pointerInfo: this._activePointerInfo });\r\n            this._targetPosition.copyFrom(this.attachedNode.getAbsolutePosition());\r\n\r\n            // Detatch camera controls\r\n            if (this.detachCameraControls && this._scene.activeCamera && this._scene.activeCamera.inputs && !this._scene.activeCamera.leftCamera) {\r\n                if (this._scene.activeCamera.inputs.attachedToElement) {\r\n                    this._scene.activeCamera.detachControl();\r\n                    this._attachedToElement = true;\r\n                } else {\r\n                    this._attachedToElement = false;\r\n                }\r\n            }\r\n        } else {\r\n            this.releaseDrag();\r\n        }\r\n        PivotTools._RestorePivotPoint(this.attachedNode);\r\n    }\r\n\r\n    private _dragDelta = new Vector3();\r\n    protected _moveDrag(ray: Ray) {\r\n        this._moving = true;\r\n        const pickedPoint = this._pickWithRayOnDragPlane(ray);\r\n\r\n        if (pickedPoint) {\r\n            PivotTools._RemoveAndStorePivotPoint(this.attachedNode);\r\n\r\n            if (this.updateDragPlane) {\r\n                this._updateDragPlanePosition(ray, pickedPoint);\r\n            }\r\n            let dragLength = 0;\r\n            // depending on the drag mode option drag accordingly\r\n            if (this._options.dragAxis) {\r\n                // Convert local drag axis to world if useObjectOrientationForDragging\r\n                this.useObjectOrientationForDragging\r\n                    ? Vector3.TransformCoordinatesToRef(this._options.dragAxis, this.attachedNode.getWorldMatrix().getRotationMatrix(), this._worldDragAxis)\r\n                    : this._worldDragAxis.copyFrom(this._options.dragAxis);\r\n                // Project delta drag from the drag plane onto the drag axis\r\n                pickedPoint.subtractToRef(this.lastDragPosition, this._tmpVector);\r\n\r\n                this._worldDragAxis.normalize();\r\n                dragLength = Vector3.Dot(this._tmpVector, this._worldDragAxis);\r\n                this._worldDragAxis.scaleToRef(dragLength, this._dragDelta);\r\n            } else {\r\n                dragLength = this._dragDelta.length();\r\n                pickedPoint.subtractToRef(this.lastDragPosition, this._dragDelta);\r\n            }\r\n            this._targetPosition.addInPlace(this._dragDelta);\r\n            this.onDragObservable.notifyObservers({\r\n                dragDistance: dragLength,\r\n                delta: this._dragDelta,\r\n                dragPlanePoint: pickedPoint,\r\n                dragPlaneNormal: this._dragPlane.forward,\r\n                pointerId: this.currentDraggingPointerId,\r\n                pointerInfo: this._activePointerInfo,\r\n            });\r\n            this.lastDragPosition.copyFrom(pickedPoint);\r\n\r\n            PivotTools._RestorePivotPoint(this.attachedNode);\r\n        }\r\n    }\r\n\r\n    private _pickWithRayOnDragPlane(ray: Nullable<Ray>) {\r\n        if (!ray) {\r\n            return null;\r\n        }\r\n\r\n        // Calculate angle between plane normal and ray\r\n        let angle = Math.acos(Vector3.Dot(this._dragPlane.forward, ray.direction));\r\n        // Correct if ray is casted from oposite side\r\n        if (angle > Math.PI / 2) {\r\n            angle = Math.PI - angle;\r\n        }\r\n\r\n        // If the angle is too perpendicular to the plane pick another point on the plane where it is looking\r\n        if (this.maxDragAngle > 0 && angle > this.maxDragAngle) {\r\n            if (this._useAlternatePickedPointAboveMaxDragAngle) {\r\n                // Invert ray direction along the towards object axis\r\n                this._tmpVector.copyFrom(ray.direction);\r\n                this.attachedNode.absolutePosition.subtractToRef(ray.origin, this._alternatePickedPoint);\r\n                this._alternatePickedPoint.normalize();\r\n                this._alternatePickedPoint.scaleInPlace(this._useAlternatePickedPointAboveMaxDragAngleDragSpeed * Vector3.Dot(this._alternatePickedPoint, this._tmpVector));\r\n                this._tmpVector.addInPlace(this._alternatePickedPoint);\r\n\r\n                // Project resulting vector onto the drag plane and add it to the attached nodes absolute position to get a picked point\r\n                const dot = Vector3.Dot(this._dragPlane.forward, this._tmpVector);\r\n                this._dragPlane.forward.scaleToRef(-dot, this._alternatePickedPoint);\r\n                this._alternatePickedPoint.addInPlace(this._tmpVector);\r\n                this._alternatePickedPoint.addInPlace(this.attachedNode.absolutePosition);\r\n                return this._alternatePickedPoint;\r\n            } else {\r\n                return null;\r\n            }\r\n        }\r\n\r\n        // use an infinite plane instead of ray picking a mesh that must be updated every frame\r\n        const planeNormal = this._dragPlane.forward;\r\n        const planePosition = this._dragPlane.position;\r\n        const dotProduct = ray.direction.dot(planeNormal);\r\n        if (Math.abs(dotProduct) < Epsilon) {\r\n            // Ray and plane are parallel, no intersection\r\n            return null;\r\n        }\r\n\r\n        planePosition.subtractToRef(ray.origin, TmpVectors.Vector3[0]);\r\n        const t = TmpVectors.Vector3[0].dot(planeNormal) / dotProduct;\r\n        // Ensure the intersection point is in front of the ray (t must be positive)\r\n        if (t < 0) {\r\n            // Intersection point is behind the ray\r\n            return null;\r\n        }\r\n\r\n        // Calculate the intersection point using the parameter t\r\n        ray.direction.scaleToRef(t, TmpVectors.Vector3[0]);\r\n        const intersectionPoint = ray.origin.add(TmpVectors.Vector3[0]);\r\n        return intersectionPoint;\r\n    }\r\n\r\n    // Variables to avoid instantiation in the below method\r\n    private _pointA = new Vector3(0, 0, 0);\r\n    private _pointC = new Vector3(0, 0, 0);\r\n    private _localAxis = new Vector3(0, 0, 0);\r\n    private _lookAt = new Vector3(0, 0, 0);\r\n    // Position the drag plane based on the attached mesh position, for single axis rotate the plane along the axis to face the camera\r\n    private _updateDragPlanePosition(ray: Ray, dragPlanePosition: Vector3) {\r\n        this._pointA.copyFrom(dragPlanePosition);\r\n        if (this._options.dragAxis) {\r\n            this.useObjectOrientationForDragging\r\n                ? Vector3.TransformCoordinatesToRef(this._options.dragAxis, this.attachedNode.getWorldMatrix().getRotationMatrix(), this._localAxis)\r\n                : this._localAxis.copyFrom(this._options.dragAxis);\r\n\r\n            // Calculate plane normal that is the cross product of local axis and (eye-dragPlanePosition)\r\n            ray.origin.subtractToRef(this._pointA, this._pointC);\r\n            this._pointC.normalize();\r\n            if (Math.abs(Vector3.Dot(this._localAxis, this._pointC)) > 0.999) {\r\n                // the drag axis is colinear with the (eye to position) ray. The cross product will give jittered values.\r\n                // A new axis vector need to be computed\r\n                if (Math.abs(Vector3.Dot(Vector3.UpReadOnly, this._pointC)) > 0.999) {\r\n                    this._lookAt.copyFrom(Vector3.Right());\r\n                } else {\r\n                    this._lookAt.copyFrom(Vector3.UpReadOnly);\r\n                }\r\n            } else {\r\n                Vector3.CrossToRef(this._localAxis, this._pointC, this._lookAt);\r\n                // Get perpendicular line from previous result and drag axis to adjust lineB to be perpendicular to camera\r\n                Vector3.CrossToRef(this._localAxis, this._lookAt, this._lookAt);\r\n                this._lookAt.normalize();\r\n            }\r\n\r\n            this._dragPlane.position.copyFrom(this._pointA);\r\n            this._pointA.addToRef(this._lookAt, this._lookAt);\r\n            this._dragPlane.lookAt(this._lookAt);\r\n        } else if (this._options.dragPlaneNormal) {\r\n            this.useObjectOrientationForDragging\r\n                ? Vector3.TransformCoordinatesToRef(this._options.dragPlaneNormal, this.attachedNode.getWorldMatrix().getRotationMatrix(), this._localAxis)\r\n                : this._localAxis.copyFrom(this._options.dragPlaneNormal);\r\n            this._dragPlane.position.copyFrom(this._pointA);\r\n            this._pointA.addToRef(this._localAxis, this._lookAt);\r\n            this._dragPlane.lookAt(this._lookAt);\r\n        } else {\r\n            if (this._scene.activeCamera) {\r\n                this._scene.activeCamera.getForwardRay().direction.normalizeToRef(this._localAxis);\r\n            }\r\n            this._dragPlane.position.copyFrom(this._pointA);\r\n            this._dragPlane.lookAt(this._pointA.add(this._localAxis));\r\n        }\r\n        // Update the position of the drag plane so it doesn't get out of sync with the node (eg. when moving back and forth quickly)\r\n        this._dragPlane.position.copyFrom(this.attachedNode.getAbsolutePosition());\r\n\r\n        this._dragPlane.computeWorldMatrix(true);\r\n    }\r\n\r\n    /**\r\n     *  Detaches the behavior from the mesh\r\n     */\r\n    public detach(): void {\r\n        this._lastPointerRay = {};\r\n        if (this.attachedNode) {\r\n            this.attachedNode.isNearGrabbable = false;\r\n        }\r\n        if (this._pointerObserver) {\r\n            this._scene.onPointerObservable.remove(this._pointerObserver);\r\n        }\r\n        if (this._beforeRenderObserver) {\r\n            this._scene.onBeforeRenderObservable.remove(this._beforeRenderObserver);\r\n        }\r\n        if (this._dragPlane) {\r\n            this._dragPlane.dispose();\r\n        }\r\n        this.releaseDrag();\r\n    }\r\n}\r\n"]}