{"version": 3, "file": "shaderProcessingOptions.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/shaderProcessingOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["/* eslint-disable jsdoc/require-jsdoc */\r\nimport type { IShaderProcessor } from \"./iShaderProcessor\";\r\nimport type { Nullable } from \"../../types\";\r\n\r\n/**\r\n * Function for custom code generation\r\n */\r\nexport type ShaderCustomProcessingFunction = (shaderType: string, code: string, defines?: string[]) => string;\r\n\r\n/** @internal */\r\nexport interface _IShaderProcessingContext {\r\n    // For engines that check for non float vertex buffers, this object is populated only with the vertex kinds known to be FLOAT by the engine (position, uv, ...)\r\n    // and only if the type of the corresponding vertex buffer is an integer type. If the type is a signed integer type, the value is negated.\r\n    vertexBufferKindToNumberOfComponents?: { [kind: string]: number };\r\n}\r\n\r\n/** @internal */\r\nexport interface _IProcessingOptions {\r\n    defines: string[];\r\n    indexParameters: any;\r\n    isFragment: boolean;\r\n    shouldUseHighPrecisionShader: boolean;\r\n    supportsUniformBuffers: boolean;\r\n    shadersRepository: string;\r\n    includesShadersStore: { [key: string]: string };\r\n    processor: Nullable<IShaderProcessor>;\r\n    version: string;\r\n    platformName: string;\r\n    lookForClosingBracketForUniformBuffer?: boolean;\r\n    processingContext: Nullable<_IShaderProcessingContext>;\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    isNDCHalfZRange: boolean;\r\n    useReverseDepthBuffer: boolean;\r\n    processCodeAfterIncludes?: ShaderCustomProcessingFunction;\r\n}\r\n"]}