{"version": 3, "file": "engine.uniformBuffer.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.uniformBuffer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAGtD,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAC;AA2DrE,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,QAAoB,EAAE,MAAe;IACtF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;IAEpC,IAAI,CAAC,GAAG,EAAE,CAAC;QACP,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvD,CAAC;IACD,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC;IAExC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAE/B,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;QACnC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAgB,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC/F,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IACnG,CAAC;IAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAE7B,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;IACtB,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,0BAA0B,GAAG,UAAU,QAAoB,EAAE,MAAe;IAC7F,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;IAEpC,IAAI,CAAC,GAAG,EAAE,CAAC;QACP,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC;IACxC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAE/B,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;QACnC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAgB,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAChG,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACpG,CAAC;IAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAE7B,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC;IACtB,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,aAAyB,EAAE,QAAoB,EAAE,MAAe,EAAE,KAAc;IACjI,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAEtC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACvB,MAAM,GAAG,CAAC,CAAC;IACf,CAAC;IAED,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QACtB,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;YACnC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,EAAgB,QAAQ,CAAC,CAAC;QACpF,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QACxF,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;YACnC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;QAClG,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,EAAE,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;QACpH,CAAC;IACL,CAAC;IAED,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACjC,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,MAA4B;IAC3E,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC5F,CAAC,CAAC;AAEF,6DAA6D;AAC7D,UAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,MAAkB,EAAE,QAAgB,EAAE,IAAY;IACrG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC1G,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,eAAiC,EAAE,SAAiB,EAAE,KAAa;IACjH,MAAM,OAAO,GAAI,eAAwC,CAAC,OAAQ,CAAC;IAEnE,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAE1E,IAAI,eAAe,KAAK,UAAU,EAAE,CAAC;QACjC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { FloatArray, Nullable } from \"../../types\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\nimport { WebGLDataBuffer } from \"../../Meshes/WebGL/webGLDataBuffer\";\r\nimport type { IPipelineContext } from \"../IPipelineContext\";\r\nimport type { WebGLPipelineContext } from \"../WebGL/webGLPipelineContext\";\r\n\r\ndeclare module \"../../Engines/thinEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface ThinEngine {\r\n        /**\r\n         * Create an uniform buffer\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n         * @param elements defines the content of the uniform buffer\r\n         * @param label defines a name for the buffer (for debugging purpose)\r\n         * @returns the webGL uniform buffer\r\n         */\r\n        createUniformBuffer(elements: FloatArray, label?: string): DataBuffer;\r\n\r\n        /**\r\n         * Create a dynamic uniform buffer\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n         * @param elements defines the content of the uniform buffer\r\n         * @param label defines a name for the buffer (for debugging purpose)\r\n         * @returns the webGL uniform buffer\r\n         */\r\n        createDynamicUniformBuffer(elements: FloatArray, label?: string): DataBuffer;\r\n\r\n        /**\r\n         * Update an existing uniform buffer\r\n         * @see https://doc.babylonjs.com/setup/support/webGL2#uniform-buffer-objets\r\n         * @param uniformBuffer defines the target uniform buffer\r\n         * @param elements defines the content to update\r\n         * @param offset defines the offset in the uniform buffer where update should start\r\n         * @param count defines the size of the data to update\r\n         */\r\n        updateUniformBuffer(uniformBuffer: DataBuffer, elements: FloatArray, offset?: number, count?: number): void;\r\n\r\n        /**\r\n         * Bind an uniform buffer to the current webGL context\r\n         * @param buffer defines the buffer to bind\r\n         */\r\n        bindUniformBuffer(buffer: Nullable<DataBuffer>): void;\r\n\r\n        /**\r\n         * Bind a buffer to the current webGL context at a given location\r\n         * @param buffer defines the buffer to bind\r\n         * @param location defines the index where to bind the buffer\r\n         * @param name Name of the uniform variable to bind\r\n         */\r\n        bindUniformBufferBase(buffer: DataBuffer, location: number, name: string): void;\r\n\r\n        /**\r\n         * Bind a specific block at a given index in a specific shader program\r\n         * @param pipelineContext defines the pipeline context to use\r\n         * @param blockName defines the block name\r\n         * @param index defines the index where to bind the block\r\n         */\r\n        bindUniformBlock(pipelineContext: IPipelineContext, blockName: string, index: number): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createUniformBuffer = function (elements: FloatArray, _label?: string): DataBuffer {\r\n    const ubo = this._gl.createBuffer();\r\n\r\n    if (!ubo) {\r\n        throw new Error(\"Unable to create uniform buffer\");\r\n    }\r\n    const result = new WebGLDataBuffer(ubo);\r\n\r\n    this.bindUniformBuffer(result);\r\n\r\n    if (elements instanceof Float32Array) {\r\n        this._gl.bufferData(this._gl.UNIFORM_BUFFER, <Float32Array>elements, this._gl.STATIC_DRAW);\r\n    } else {\r\n        this._gl.bufferData(this._gl.UNIFORM_BUFFER, new Float32Array(elements), this._gl.STATIC_DRAW);\r\n    }\r\n\r\n    this.bindUniformBuffer(null);\r\n\r\n    result.references = 1;\r\n    return result;\r\n};\r\n\r\nThinEngine.prototype.createDynamicUniformBuffer = function (elements: FloatArray, _label?: string): DataBuffer {\r\n    const ubo = this._gl.createBuffer();\r\n\r\n    if (!ubo) {\r\n        throw new Error(\"Unable to create dynamic uniform buffer\");\r\n    }\r\n\r\n    const result = new WebGLDataBuffer(ubo);\r\n    this.bindUniformBuffer(result);\r\n\r\n    if (elements instanceof Float32Array) {\r\n        this._gl.bufferData(this._gl.UNIFORM_BUFFER, <Float32Array>elements, this._gl.DYNAMIC_DRAW);\r\n    } else {\r\n        this._gl.bufferData(this._gl.UNIFORM_BUFFER, new Float32Array(elements), this._gl.DYNAMIC_DRAW);\r\n    }\r\n\r\n    this.bindUniformBuffer(null);\r\n\r\n    result.references = 1;\r\n    return result;\r\n};\r\n\r\nThinEngine.prototype.updateUniformBuffer = function (uniformBuffer: DataBuffer, elements: FloatArray, offset?: number, count?: number): void {\r\n    this.bindUniformBuffer(uniformBuffer);\r\n\r\n    if (offset === undefined) {\r\n        offset = 0;\r\n    }\r\n\r\n    if (count === undefined) {\r\n        if (elements instanceof Float32Array) {\r\n            this._gl.bufferSubData(this._gl.UNIFORM_BUFFER, offset, <Float32Array>elements);\r\n        } else {\r\n            this._gl.bufferSubData(this._gl.UNIFORM_BUFFER, offset, new Float32Array(elements));\r\n        }\r\n    } else {\r\n        if (elements instanceof Float32Array) {\r\n            this._gl.bufferSubData(this._gl.UNIFORM_BUFFER, 0, elements.subarray(offset, offset + count));\r\n        } else {\r\n            this._gl.bufferSubData(this._gl.UNIFORM_BUFFER, 0, new Float32Array(elements).subarray(offset, offset + count));\r\n        }\r\n    }\r\n\r\n    this.bindUniformBuffer(null);\r\n};\r\n\r\nThinEngine.prototype.bindUniformBuffer = function (buffer: Nullable<DataBuffer>): void {\r\n    this._gl.bindBuffer(this._gl.UNIFORM_BUFFER, buffer ? buffer.underlyingResource : null);\r\n};\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nThinEngine.prototype.bindUniformBufferBase = function (buffer: DataBuffer, location: number, name: string): void {\r\n    this._gl.bindBufferBase(this._gl.UNIFORM_BUFFER, location, buffer ? buffer.underlyingResource : null);\r\n};\r\n\r\nThinEngine.prototype.bindUniformBlock = function (pipelineContext: IPipelineContext, blockName: string, index: number): void {\r\n    const program = (pipelineContext as WebGLPipelineContext).program!;\r\n\r\n    const uniformLocation = this._gl.getUniformBlockIndex(program, blockName);\r\n\r\n    if (uniformLocation !== 0xffffffff) {\r\n        this._gl.uniformBlockBinding(program, uniformLocation, index);\r\n    }\r\n};\r\n"]}