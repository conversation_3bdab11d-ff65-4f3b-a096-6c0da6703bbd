{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Loading/sceneLoaderFlags.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Loading/sceneLoaderFlags.ts"], "sourcesContent": ["import { Constants } from \"../Engines/constants\";\r\n\r\n/**\r\n * Class used to represent data loading progression\r\n */\r\nexport class SceneLoaderFlags {\r\n    // Flags\r\n    private static _ForceFullSceneLoadingForIncremental = false;\r\n    private static _ShowLoadingScreen = true;\r\n    private static _CleanBoneMatrixWeights = false;\r\n    private static _LoggingLevel = Constants.SCENELOADER_NO_LOGGING;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if entire scene must be loaded even if scene contains incremental data\r\n     */\r\n    public static get ForceFullSceneLoadingForIncremental() {\r\n        return SceneLoaderFlags._ForceFullSceneLoadingForIncremental;\r\n    }\r\n\r\n    public static set ForceFullSceneLoadingForIncremental(value: boolean) {\r\n        SceneLoaderFlags._ForceFullSceneLoadingForIncremental = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if loading screen must be displayed while loading a scene\r\n     */\r\n    public static get ShowLoadingScreen(): boolean {\r\n        return SceneLoaderFlags._ShowLoadingScreen;\r\n    }\r\n\r\n    public static set ShowLoadingScreen(value: boolean) {\r\n        SceneLoaderFlags._ShowLoadingScreen = value;\r\n    }\r\n\r\n    /**\r\n     * Defines the current logging level (while loading the scene)\r\n     * @ignorenaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static get loggingLevel(): number {\r\n        return SceneLoaderFlags._LoggingLevel;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static set loggingLevel(value: number) {\r\n        SceneLoaderFlags._LoggingLevel = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or set a boolean indicating if matrix weights must be cleaned upon loading\r\n     */\r\n    public static get CleanBoneMatrixWeights(): boolean {\r\n        return SceneLoaderFlags._CleanBoneMatrixWeights;\r\n    }\r\n\r\n    public static set CleanBoneMatrixWeights(value: boolean) {\r\n        SceneLoaderFlags._CleanBoneMatrixWeights = value;\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAEA;;GAEG;;;AACG,MAAO,gBAAgB;IAOzB;;OAEG,CACI,MAAM,KAAK,mCAAmC,GAAA;QACjD,OAAO,gBAAgB,CAAC,oCAAoC,CAAC;IACjE,CAAC;IAEM,MAAM,KAAK,mCAAmC,CAAC,KAAc,EAAA;QAChE,gBAAgB,CAAC,oCAAoC,GAAG,KAAK,CAAC;IAClE,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,iBAAiB,GAAA;QAC/B,OAAO,gBAAgB,CAAC,kBAAkB,CAAC;IAC/C,CAAC;IAEM,MAAM,KAAK,iBAAiB,CAAC,KAAc,EAAA;QAC9C,gBAAgB,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAChD,CAAC;IAED;;;OAGG,CACH,gEAAgE;IACzD,MAAM,KAAK,YAAY,GAAA;QAC1B,OAAO,gBAAgB,CAAC,aAAa,CAAC;IAC1C,CAAC;IAED,gEAAgE;IACzD,MAAM,KAAK,YAAY,CAAC,KAAa,EAAA;QACxC,gBAAgB,CAAC,aAAa,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,sBAAsB,GAAA;QACpC,OAAO,gBAAgB,CAAC,uBAAuB,CAAC;IACpD,CAAC;IAEM,MAAM,KAAK,sBAAsB,CAAC,KAAc,EAAA;QACnD,gBAAgB,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACrD,CAAC;;AAnDD,QAAQ;AACO,iBAAA,oCAAoC,GAAG,KAAK,CAAC;AAC7C,iBAAA,kBAAkB,GAAG,IAAI,CAAC;AAC1B,iBAAA,uBAAuB,GAAG,KAAK,CAAC;AAChC,iBAAA,aAAa,GAAG,SAAS,CAAC,sBAAsB,CAAC", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Loading/Plugins/babylonFileParser.function.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Loading/Plugins/babylonFileParser.function.ts"], "sourcesContent": ["import type { <PERSON>setContainer } from \"core/assetContainer\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { Nullable } from \"core/types\";\r\n\r\n/**\r\n * Defines how the parser contract is defined.\r\n * These parsers are used to parse a list of specific assets (like particle systems, etc..)\r\n */\r\nexport type BabylonFileParser = (parsedData: any, scene: Scene, container: AssetContainer, rootUrl: string) => void;\r\n\r\n/**\r\n * Defines how the individual parser contract is defined.\r\n * These parser can parse an individual asset\r\n */\r\nexport type IndividualBabylonFileParser = (parsedData: any, scene: Scene, rootUrl: string) => any;\r\n\r\n/**\r\n * Stores the list of available parsers in the application.\r\n */\r\nconst BabylonFileParsers: { [key: string]: BabylonFileParser } = {};\r\n\r\n/**\r\n * Stores the list of available individual parsers in the application.\r\n */\r\nconst IndividualBabylonFileParsers: { [key: string]: IndividualBabylonFileParser } = {};\r\n\r\n/**\r\n * Adds a parser in the list of available ones\r\n * @param name Defines the name of the parser\r\n * @param parser Defines the parser to add\r\n */\r\nexport function AddParser(name: string, parser: BabylonFileParser): void {\r\n    BabylonFileParsers[name] = parser;\r\n}\r\n\r\n/**\r\n * Gets a general parser from the list of available ones\r\n * @param name Defines the name of the parser\r\n * @returns the requested parser or null\r\n */\r\nexport function GetParser(name: string): Nullable<BabylonFileParser> {\r\n    if (BabylonFileParsers[name]) {\r\n        return BabylonFileParsers[name];\r\n    }\r\n\r\n    return null;\r\n}\r\n\r\n/**\r\n * Adds n individual parser in the list of available ones\r\n * @param name Defines the name of the parser\r\n * @param parser Defines the parser to add\r\n */\r\nexport function AddIndividualParser(name: string, parser: IndividualBabylonFileParser): void {\r\n    IndividualBabylonFileParsers[name] = parser;\r\n}\r\n\r\n/**\r\n * Gets an individual parser from the list of available ones\r\n * @param name Defines the name of the parser\r\n * @returns the requested parser or null\r\n */\r\nexport function GetIndividualParser(name: string): Nullable<IndividualBabylonFileParser> {\r\n    if (IndividualBabylonFileParsers[name]) {\r\n        return IndividualBabylonFileParsers[name];\r\n    }\r\n\r\n    return null;\r\n}\r\n\r\n/**\r\n * Parser json data and populate both a scene and its associated container object\r\n * @param jsonData Defines the data to parse\r\n * @param scene Defines the scene to parse the data for\r\n * @param container Defines the container attached to the parsing sequence\r\n * @param rootUrl Defines the root url of the data\r\n */\r\nexport function Parse(jsonData: any, scene: Scene, container: AssetContainer, rootUrl: string): void {\r\n    for (const parserName in BabylonFileParsers) {\r\n        if (Object.prototype.hasOwnProperty.call(BabylonFileParsers, parserName)) {\r\n            BabylonFileParsers[parserName](jsonData, scene, container, rootUrl);\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAgBA;;GAEG;;;;;;;AACH,MAAM,kBAAkB,GAAyC,CAAA,CAAE,CAAC;AAEpE;;GAEG,CACH,MAAM,4BAA4B,GAAmD,CAAA,CAAE,CAAC;AAOlF,SAAU,SAAS,CAAC,IAAY,EAAE,MAAyB;IAC7D,kBAAkB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AACtC,CAAC;AAOK,SAAU,SAAS,CAAC,IAAY;IAClC,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAOK,SAAU,mBAAmB,CAAC,IAAY,EAAE,MAAmC;IACjF,4BAA4B,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;AAChD,CAAC;AAOK,SAAU,mBAAmB,CAAC,IAAY;IAC5C,IAAI,4BAA4B,CAAC,IAAI,CAAC,EAAE,CAAC;QACrC,OAAO,4BAA4B,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AASK,SAAU,KAAK,CAAC,QAAa,EAAE,KAAY,EAAE,SAAyB,EAAE,OAAe;IACzF,IAAK,MAAM,UAAU,IAAI,kBAAkB,CAAE,CAAC;QAC1C,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,CAAC;YACvE,kBAAkB,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACxE,CAAC;IACL,CAAC;AACL,CAAC", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Loading/sceneLoader.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Loading/sceneLoader.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { DeepImmutable, Nullable } from \"../types\";\r\nimport { Scene } from \"../scene\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { AnimationGroup } from \"../Animations/animationGroup\";\r\nimport type { AssetContainer } from \"../assetContainer\";\r\nimport type { IParticleSystem } from \"../Particles/IParticleSystem\";\r\nimport type { Skeleton } from \"../Bones/skeleton\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { SceneLoaderFlags } from \"./sceneLoaderFlags\";\r\nimport type { IFileRequest } from \"../Misc/fileRequest\";\r\nimport type { WebRequest } from \"../Misc/webRequest\";\r\nimport type { LoadFileError } from \"../Misc/fileTools\";\r\nimport { IsBase64DataUrl } from \"../Misc/fileTools\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport type { Geometry } from \"../Meshes/geometry\";\r\nimport type { Light } from \"../Lights/light\";\r\nimport { RuntimeError, ErrorCodes } from \"../Misc/error\";\r\nimport type { ISpriteManager } from \"../Sprites/spriteManager\";\r\nimport { RandomGUID } from \"../Misc/guid\";\r\nimport { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport { _FetchAsync } from \"core/Misc/webRequest.fetch\";\r\n\r\n/**\r\n * Type used for the success callback of ImportMesh\r\n */\r\nexport type SceneLoaderSuccessCallback = (\r\n    meshes: AbstractMesh[],\r\n    particleSystems: IParticleSystem[],\r\n    skeletons: Skeleton[],\r\n    animationGroups: AnimationGroup[],\r\n    transformNodes: TransformNode[],\r\n    geometries: Geometry[],\r\n    lights: Light[],\r\n    spriteManagers: ISpriteManager[]\r\n) => void;\r\n\r\n/**\r\n * Interface used for the result of ImportMeshAsync\r\n */\r\nexport interface ISceneLoaderAsyncResult {\r\n    /**\r\n     * The array of loaded meshes\r\n     */\r\n    readonly meshes: AbstractMesh[];\r\n\r\n    /**\r\n     * The array of loaded particle systems\r\n     */\r\n    readonly particleSystems: IParticleSystem[];\r\n\r\n    /**\r\n     * The array of loaded skeletons\r\n     */\r\n    readonly skeletons: Skeleton[];\r\n\r\n    /**\r\n     * The array of loaded animation groups\r\n     */\r\n    readonly animationGroups: AnimationGroup[];\r\n\r\n    /**\r\n     * The array of loaded transform nodes\r\n     */\r\n    readonly transformNodes: TransformNode[];\r\n\r\n    /**\r\n     * The array of loaded geometries\r\n     */\r\n    readonly geometries: Geometry[];\r\n\r\n    /**\r\n     * The array of loaded lights\r\n     */\r\n    readonly lights: Light[];\r\n\r\n    /**\r\n     * The array of loaded sprite managers\r\n     */\r\n    readonly spriteManagers: ISpriteManager[];\r\n}\r\n\r\n/**\r\n * Interface used to represent data loading progression\r\n */\r\nexport interface ISceneLoaderProgressEvent {\r\n    /**\r\n     * Defines if data length to load can be evaluated\r\n     */\r\n    readonly lengthComputable: boolean;\r\n\r\n    /**\r\n     * Defines the loaded data length\r\n     */\r\n    readonly loaded: number;\r\n\r\n    /**\r\n     * Defines the data length to load\r\n     */\r\n    readonly total: number;\r\n}\r\n\r\n/**\r\n * Interface used by SceneLoader plugins to define supported file extensions\r\n */\r\nexport interface ISceneLoaderPluginExtensions {\r\n    /**\r\n     * Defines the list of supported extensions\r\n     */\r\n    readonly [extension: string]: {\r\n        readonly isBinary: boolean;\r\n        readonly mimeType?: string;\r\n    };\r\n}\r\n\r\n/**\r\n * Metadata for a SceneLoader plugin that must also be provided by a plugin factory\r\n */\r\nexport interface ISceneLoaderPluginMetadata {\r\n    /**\r\n     * The friendly name of the plugin.\r\n     */\r\n    readonly name: string;\r\n\r\n    /**\r\n     * The file extensions supported by the plugin.\r\n     */\r\n    readonly extensions: string | ISceneLoaderPluginExtensions;\r\n\r\n    /**\r\n     * The callback that returns true if the data can be directly loaded.\r\n     * @param data string containing the file data\r\n     * @returns if the data can be loaded directly\r\n     */\r\n    canDirectLoad?(data: string): boolean;\r\n}\r\n\r\n/**\r\n * Interface used by SceneLoader plugin factory\r\n */\r\nexport interface ISceneLoaderPluginFactory extends ISceneLoaderPluginMetadata {\r\n    /**\r\n     * Function called to create a new plugin\r\n     * @param options plugin options that were passed to the SceneLoader operation\r\n     * @returns the new plugin\r\n     */\r\n    createPlugin(options: SceneLoaderPluginOptions): ISceneLoaderPlugin | ISceneLoaderPluginAsync | Promise<ISceneLoaderPlugin | ISceneLoaderPluginAsync>;\r\n}\r\n\r\n/**\r\n * Interface used to define the base of ISceneLoaderPlugin and ISceneLoaderPluginAsync\r\n */\r\nexport interface ISceneLoaderPluginBase extends ISceneLoaderPluginMetadata {\r\n    /**\r\n     * The callback called when loading from a url.\r\n     * @param scene scene loading this url\r\n     * @param fileOrUrl file or url to load\r\n     * @param rootUrl root url to use to load assets\r\n     * @param onSuccess callback called when the file successfully loads\r\n     * @param onProgress callback called while file is loading (if the server supports this mode)\r\n     * @param useArrayBuffer defines a boolean indicating that date must be returned as ArrayBuffer\r\n     * @param onError callback called when the file fails to load\r\n     * @param name defines the name of the file when loading a binary file\r\n     * @returns a file request object\r\n     */\r\n    loadFile?(\r\n        scene: Scene,\r\n        fileOrUrl: File | string | ArrayBufferView,\r\n        rootUrl: string,\r\n        onSuccess: (data: unknown, responseURL?: string) => void,\r\n        onProgress?: (ev: ISceneLoaderProgressEvent) => void,\r\n        useArrayBuffer?: boolean,\r\n        onError?: (request?: WebRequest, exception?: LoadFileError) => void,\r\n        name?: string\r\n    ): Nullable<IFileRequest>;\r\n\r\n    /**\r\n     * The callback that returns the data to pass to the plugin if the data can be directly loaded.\r\n     * @param scene scene loading this data\r\n     * @param data string containing the data\r\n     * @returns data to pass to the plugin\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents\r\n    directLoad?(scene: Scene, data: string): unknown | Promise<unknown>;\r\n\r\n    /**\r\n     * The callback that allows custom handling of the root url based on the response url.\r\n     * @param rootUrl the original root url\r\n     * @param responseURL the response url if available\r\n     * @returns the new root url\r\n     */\r\n    rewriteRootURL?(rootUrl: string, responseURL?: string): string;\r\n}\r\n\r\n/**\r\n * Interface used to define a SceneLoader plugin\r\n */\r\nexport interface ISceneLoaderPlugin extends ISceneLoaderPluginBase {\r\n    /**\r\n     * Import meshes into a scene.\r\n     * @param meshesNames An array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     * @param scene The scene to import into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param meshes The meshes array to import into\r\n     * @param particleSystems The particle systems array to import into\r\n     * @param skeletons The skeletons array to import into\r\n     * @param onError The callback when import fails\r\n     * @returns True if successful or false otherwise\r\n     */\r\n    importMesh(\r\n        meshesNames: string | readonly string[] | null | undefined,\r\n        scene: Scene,\r\n        data: unknown,\r\n        rootUrl: string,\r\n        meshes: AbstractMesh[],\r\n        particleSystems: IParticleSystem[],\r\n        skeletons: Skeleton[],\r\n        onError?: (message: string, exception?: any) => void\r\n    ): boolean;\r\n\r\n    /**\r\n     * Load into a scene.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onError The callback when import fails\r\n     * @returns True if successful or false otherwise\r\n     */\r\n    load(scene: Scene, data: unknown, rootUrl: string, onError?: (message: string, exception?: any) => void): boolean;\r\n\r\n    /**\r\n     * Load into an asset container.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onError The callback when import fails\r\n     * @returns The loaded asset container\r\n     */\r\n    loadAssetContainer(scene: Scene, data: unknown, rootUrl: string, onError?: (message: string, exception?: any) => void): AssetContainer;\r\n}\r\n\r\n/**\r\n * Interface used to define an async SceneLoader plugin\r\n */\r\nexport interface ISceneLoaderPluginAsync extends ISceneLoaderPluginBase {\r\n    /**\r\n     * Import meshes into a scene.\r\n     * @param meshesNames An array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     * @param scene The scene to import into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onProgress The callback when the load progresses\r\n     * @param fileName Defines the name of the file to load\r\n     * @returns The loaded objects (e.g. meshes, particle systems, skeletons, animation groups, etc.)\r\n     */\r\n    importMeshAsync(\r\n        meshesNames: string | readonly string[] | null | undefined,\r\n        scene: Scene,\r\n        data: unknown,\r\n        rootUrl: string,\r\n        onProgress?: (event: ISceneLoaderProgressEvent) => void,\r\n        fileName?: string\r\n    ): Promise<ISceneLoaderAsyncResult>;\r\n\r\n    /**\r\n     * Load into a scene.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onProgress The callback when the load progresses\r\n     * @param fileName Defines the name of the file to load\r\n     * @returns Nothing\r\n     */\r\n    loadAsync(scene: Scene, data: unknown, rootUrl: string, onProgress?: (event: ISceneLoaderProgressEvent) => void, fileName?: string): Promise<void>;\r\n\r\n    /**\r\n     * Load into an asset container.\r\n     * @param scene The scene to load into\r\n     * @param data The data to import\r\n     * @param rootUrl The root url for scene and resources\r\n     * @param onProgress The callback when the load progresses\r\n     * @param fileName Defines the name of the file to load\r\n     * @returns The loaded asset container\r\n     */\r\n    loadAssetContainerAsync(scene: Scene, data: unknown, rootUrl: string, onProgress?: (event: ISceneLoaderProgressEvent) => void, fileName?: string): Promise<AssetContainer>;\r\n}\r\n\r\n/**\r\n * Mode that determines how to handle old animation groups before loading new ones.\r\n */\r\nexport const enum SceneLoaderAnimationGroupLoadingMode {\r\n    /**\r\n     * Reset all old animations to initial state then dispose them.\r\n     */\r\n    Clean = 0,\r\n\r\n    /**\r\n     * Stop all old animations.\r\n     */\r\n    Stop = 1,\r\n\r\n    /**\r\n     * Restart old animations from first frame.\r\n     */\r\n    Sync = 2,\r\n\r\n    /**\r\n     * Old animations remains untouched.\r\n     */\r\n    NoSync = 3,\r\n}\r\n\r\n/**\r\n * Defines internal only plugin members.\r\n */\r\ninterface ISceneLoaderPluginInternal {\r\n    /**\r\n     * An optional observable to notify when the plugin is disposed\r\n     */\r\n    readonly onDisposeObservable: Observable<void>;\r\n}\r\n\r\n/**\r\n * Defines a plugin registered by the SceneLoader\r\n */\r\ninterface IRegisteredPlugin {\r\n    /**\r\n     * Defines the plugin to use\r\n     */\r\n    plugin: ((ISceneLoaderPlugin | ISceneLoaderPluginAsync) & Partial<ISceneLoaderPluginInternal>) | ISceneLoaderPluginFactory;\r\n    /**\r\n     * Defines if the plugin supports binary data\r\n     */\r\n    isBinary: boolean;\r\n    mimeType?: string;\r\n}\r\n\r\nfunction IsFactory(pluginOrFactory: IRegisteredPlugin[\"plugin\"]): pluginOrFactory is ISceneLoaderPluginFactory {\r\n    return !!(pluginOrFactory as ISceneLoaderPluginFactory).createPlugin;\r\n}\r\n\r\n/**\r\n * Defines file information\r\n */\r\ninterface IFileInfo {\r\n    /**\r\n     * Gets the file url\r\n     */\r\n    url: string;\r\n    /**\r\n     * Gets the root url\r\n     */\r\n    rootUrl: string;\r\n    /**\r\n     * Gets filename\r\n     */\r\n    name: string;\r\n    /**\r\n     * Gets the file\r\n     */\r\n    file: Nullable<File>;\r\n\r\n    /**\r\n     * Gets raw binary data.\r\n     */\r\n    rawData: Nullable<ArrayBufferView>;\r\n}\r\n\r\n/**\r\n * Defines options for SceneLoader plugins. This interface is extended by specific plugins.\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type, @typescript-eslint/naming-convention\r\nexport interface SceneLoaderPluginOptions extends Record<string, Record<string, unknown> | undefined> {}\r\n\r\n/**\r\n * Adds default/implicit options to plugin specific options.\r\n */\r\ntype DefaultPluginOptions<BasePluginOptions> = {\r\n    /**\r\n     * Defines if the plugin is enabled\r\n     */\r\n    enabled?: boolean;\r\n} & BasePluginOptions;\r\n\r\n// This captures the type defined inline for the pluginOptions property, which is just SceneLoaderPluginOptions wrapped with DefaultPluginOptions.\r\n// We do it this way rather than explicitly defining the type here and then using it in SceneLoaderOptions because we want the full expanded type\r\n// to show up in the user's intellisense to make it easier to understand what options are available.\r\ntype PluginOptions = ISceneLoaderOptions[\"pluginOptions\"];\r\n\r\ntype SceneSource = string | File | ArrayBufferView;\r\n\r\n/**\r\n * Defines common options for loading operations performed by SceneLoader.\r\n */\r\ninterface ISceneLoaderOptions {\r\n    /**\r\n     * A string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     */\r\n    rootUrl?: string;\r\n\r\n    /**\r\n     * A callback with a progress event for each file being loaded\r\n     */\r\n    onProgress?: (event: ISceneLoaderProgressEvent) => void;\r\n\r\n    /**\r\n     * The extension used to determine the plugin\r\n     */\r\n    pluginExtension?: string;\r\n\r\n    /**\r\n     * Defines the filename, if the data is binary\r\n     */\r\n    name?: string;\r\n\r\n    /**\r\n     * Defines options for the registered plugins\r\n     */\r\n    pluginOptions?: {\r\n        // NOTE: This type is doing two things:\r\n        // 1. Adding an implicit 'enabled' property to the options for each plugin.\r\n        // 2. Creating a mapped type of all the options of all the plugins to make it just look like a consolidated plain object in intellisense for the user.\r\n        [Plugin in keyof SceneLoaderPluginOptions]?: {\r\n            [Option in keyof DefaultPluginOptions<SceneLoaderPluginOptions[Plugin]>]: DefaultPluginOptions<SceneLoaderPluginOptions[Plugin]>[Option];\r\n        };\r\n    };\r\n}\r\n\r\n/**\r\n * Defines options for ImportMeshAsync.\r\n */\r\nexport interface ImportMeshOptions extends ISceneLoaderOptions {\r\n    /**\r\n     * An array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     */\r\n    meshNames?: string | readonly string[] | null | undefined;\r\n}\r\n\r\n/**\r\n * Defines options for LoadAsync.\r\n */\r\nexport interface LoadOptions extends ISceneLoaderOptions {}\r\n\r\n/**\r\n * Defines options for AppendAsync.\r\n */\r\nexport interface AppendOptions extends ISceneLoaderOptions {}\r\n\r\n/**\r\n * Defines options for LoadAssetContainerAsync.\r\n */\r\nexport interface LoadAssetContainerOptions extends ISceneLoaderOptions {}\r\n\r\n/**\r\n * Defines options for ImportAnimationsAsync.\r\n */\r\nexport interface ImportAnimationsOptions extends ISceneLoaderOptions {\r\n    /**\r\n     * When true, animations are cleaned before importing new ones. Animations are appended otherwise\r\n     */\r\n    overwriteAnimations?: boolean;\r\n\r\n    /**\r\n     * Defines how to handle old animations groups before importing new ones\r\n     */\r\n    animationGroupLoadingMode?: SceneLoaderAnimationGroupLoadingMode;\r\n\r\n    /**\r\n     * defines a function used to convert animation targets from loaded scene to current scene (default: search node by name)\r\n     */\r\n    targetConverter?: Nullable<(target: unknown) => unknown>;\r\n}\r\n\r\nfunction isFile(value: unknown): value is File {\r\n    return !!(value as File).name;\r\n}\r\n\r\nconst onPluginActivatedObservable = new Observable<ISceneLoaderPlugin | ISceneLoaderPluginAsync>();\r\nconst registeredPlugins: { [extension: string]: IRegisteredPlugin } = {};\r\nlet showingLoadingScreen = false;\r\n\r\nfunction getDefaultPlugin(): IRegisteredPlugin | undefined {\r\n    return registeredPlugins[\".babylon\"];\r\n}\r\n\r\nfunction getPluginForMimeType(mimeType: string): IRegisteredPlugin | undefined {\r\n    for (const registeredPluginKey in registeredPlugins) {\r\n        const registeredPlugin = registeredPlugins[registeredPluginKey];\r\n        if (registeredPlugin.mimeType === mimeType) {\r\n            return registeredPlugin;\r\n        }\r\n    }\r\n    return undefined;\r\n}\r\n\r\nfunction getPluginForExtension(extension: string, returnDefault: boolean): IRegisteredPlugin | undefined {\r\n    const registeredPlugin = registeredPlugins[extension];\r\n    if (registeredPlugin) {\r\n        return registeredPlugin;\r\n    }\r\n    Logger.Warn(\r\n        \"Unable to find a plugin to load \" +\r\n            extension +\r\n            \" files. Trying to use .babylon default plugin. To load from a specific filetype (eg. gltf) see: https://doc.babylonjs.com/features/featuresDeepDive/importers/loadingFileTypes\"\r\n    );\r\n    return returnDefault ? getDefaultPlugin() : undefined;\r\n}\r\n\r\nfunction isPluginForExtensionAvailable(extension: string): boolean {\r\n    return !!registeredPlugins[extension];\r\n}\r\n\r\nfunction getPluginForDirectLoad(data: string): IRegisteredPlugin | undefined {\r\n    for (const extension in registeredPlugins) {\r\n        const plugin = registeredPlugins[extension].plugin;\r\n\r\n        if (plugin.canDirectLoad && plugin.canDirectLoad(data)) {\r\n            return registeredPlugins[extension];\r\n        }\r\n    }\r\n\r\n    return getDefaultPlugin();\r\n}\r\n\r\nfunction getFilenameExtension(sceneFilename: string): string {\r\n    const queryStringPosition = sceneFilename.indexOf(\"?\");\r\n\r\n    if (queryStringPosition !== -1) {\r\n        sceneFilename = sceneFilename.substring(0, queryStringPosition);\r\n    }\r\n\r\n    const dotPosition = sceneFilename.lastIndexOf(\".\");\r\n\r\n    return sceneFilename.substring(dotPosition, sceneFilename.length).toLowerCase();\r\n}\r\n\r\nfunction getDirectLoad(sceneFilename: string): Nullable<string> {\r\n    if (sceneFilename.substring(0, 5) === \"data:\") {\r\n        return sceneFilename.substring(5);\r\n    }\r\n\r\n    return null;\r\n}\r\n\r\nfunction formatErrorMessage(fileInfo: IFileInfo, message?: string, exception?: any): string {\r\n    const fromLoad = fileInfo.rawData ? \"binary data\" : fileInfo.url;\r\n    let errorMessage = \"Unable to load from \" + fromLoad;\r\n\r\n    if (message) {\r\n        errorMessage += `: ${message}`;\r\n    } else if (exception) {\r\n        errorMessage += `: ${exception}`;\r\n    }\r\n\r\n    return errorMessage;\r\n}\r\n\r\nasync function loadDataAsync(\r\n    fileInfo: IFileInfo,\r\n    scene: Scene,\r\n    onSuccess: (plugin: ISceneLoaderPlugin | ISceneLoaderPluginAsync, data: unknown, responseURL?: string) => void,\r\n    onProgress: ((event: ISceneLoaderProgressEvent) => void) | undefined,\r\n    onError: (message?: string, exception?: any) => void,\r\n    onDispose: () => void,\r\n    pluginExtension: Nullable<string>,\r\n    name: string,\r\n    pluginOptions: PluginOptions\r\n): Promise<Nullable<ISceneLoaderPlugin | ISceneLoaderPluginAsync>> {\r\n    const directLoad = getDirectLoad(fileInfo.url);\r\n\r\n    if (fileInfo.rawData && !pluginExtension) {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"When using ArrayBufferView to load data the file extension must be provided.\";\r\n    }\r\n\r\n    const fileExtension = !directLoad && !pluginExtension ? getFilenameExtension(fileInfo.url) : \"\";\r\n\r\n    let registeredPlugin = pluginExtension\r\n        ? getPluginForExtension(pluginExtension, true)\r\n        : directLoad\r\n          ? getPluginForDirectLoad(fileInfo.url)\r\n          : getPluginForExtension(fileExtension, false);\r\n\r\n    if (!registeredPlugin && fileExtension) {\r\n        if (fileInfo.url && !fileInfo.url.startsWith(\"blob:\")) {\r\n            // Fetching head content to get the mime type\r\n            const response = await _FetchAsync(fileInfo.url, { method: \"HEAD\", responseHeaders: [\"Content-Type\"] });\r\n            const mimeType = response.headerValues ? response.headerValues[\"Content-Type\"] : \"\";\r\n            if (mimeType) {\r\n                // eslint-disable-next-line require-atomic-updates\r\n                registeredPlugin = getPluginForMimeType(mimeType);\r\n            }\r\n        }\r\n\r\n        if (!registeredPlugin) {\r\n            registeredPlugin = getDefaultPlugin();\r\n        }\r\n    }\r\n\r\n    if (!registeredPlugin) {\r\n        throw new Error(`No plugin or fallback for ${pluginExtension ?? fileInfo.url}`);\r\n    }\r\n\r\n    if (pluginOptions?.[registeredPlugin.plugin.name]?.enabled === false) {\r\n        throw new Error(`The '${registeredPlugin.plugin.name}' plugin is disabled via the loader options passed to the loading operation.`);\r\n    }\r\n\r\n    if (fileInfo.rawData && !registeredPlugin.isBinary) {\r\n        // eslint-disable-next-line no-throw-literal\r\n        throw \"Loading from ArrayBufferView can not be used with plugins that don't support binary loading.\";\r\n    }\r\n\r\n    const getPluginInstance = (callback: (plugin: (ISceneLoaderPlugin | ISceneLoaderPluginAsync) & Partial<ISceneLoaderPluginInternal>) => void) => {\r\n        // For plugin factories, the plugin is instantiated on each SceneLoader operation. This makes options handling\r\n        // much simpler as we can just pass the options to the factory, rather than passing options through to every possible\r\n        // plugin call. Given this, options are only supported for plugins that provide a factory function.\r\n        if (IsFactory(registeredPlugin.plugin)) {\r\n            const pluginFactory = registeredPlugin.plugin;\r\n            const partialPlugin = pluginFactory.createPlugin(pluginOptions ?? {});\r\n            if (partialPlugin instanceof Promise) {\r\n                // eslint-disable-next-line github/no-then\r\n                partialPlugin.then(callback).catch((error) => {\r\n                    onError(\"Error instantiating plugin.\", error);\r\n                });\r\n                // When async factories are used, the plugin instance cannot be returned synchronously.\r\n                // In this case, the legacy loader functions will return null.\r\n                return null;\r\n            } else {\r\n                callback(partialPlugin);\r\n                return partialPlugin;\r\n            }\r\n        } else {\r\n            callback(registeredPlugin.plugin);\r\n            return registeredPlugin.plugin;\r\n        }\r\n    };\r\n\r\n    return getPluginInstance((plugin) => {\r\n        if (!plugin) {\r\n            // eslint-disable-next-line no-throw-literal\r\n            throw `The loader plugin corresponding to the '${pluginExtension}' file type has not been found. If using es6, please import the plugin you wish to use before.`;\r\n        }\r\n\r\n        onPluginActivatedObservable.notifyObservers(plugin);\r\n\r\n        // Check if we have a direct load url. If the plugin is registered to handle\r\n        // it or it's not a base64 data url, then pass it through the direct load path.\r\n        if (directLoad && ((plugin.canDirectLoad && plugin.canDirectLoad(fileInfo.url)) || !IsBase64DataUrl(fileInfo.url))) {\r\n            if (plugin.directLoad) {\r\n                const result = plugin.directLoad(scene, directLoad);\r\n                if (result instanceof Promise) {\r\n                    result\r\n                        // eslint-disable-next-line github/no-then\r\n                        .then((data: unknown) => {\r\n                            onSuccess(plugin, data);\r\n                        })\r\n                        // eslint-disable-next-line github/no-then\r\n                        .catch((error: any) => {\r\n                            onError(\"Error in directLoad of _loadData: \" + error, error);\r\n                        });\r\n                } else {\r\n                    onSuccess(plugin, result);\r\n                }\r\n            } else {\r\n                onSuccess(plugin, directLoad);\r\n            }\r\n            return;\r\n        }\r\n\r\n        const useArrayBuffer = registeredPlugin.isBinary;\r\n\r\n        const dataCallback = (data: unknown, responseURL?: string) => {\r\n            if (scene.isDisposed) {\r\n                onError(\"Scene has been disposed\");\r\n                return;\r\n            }\r\n\r\n            onSuccess(plugin, data, responseURL);\r\n        };\r\n\r\n        let request: Nullable<IFileRequest> = null;\r\n        let pluginDisposed = false;\r\n        plugin.onDisposeObservable?.add(() => {\r\n            pluginDisposed = true;\r\n\r\n            if (request) {\r\n                request.abort();\r\n                request = null;\r\n            }\r\n\r\n            onDispose();\r\n        });\r\n\r\n        const manifestChecked = () => {\r\n            if (pluginDisposed) {\r\n                return;\r\n            }\r\n\r\n            const errorCallback = (request?: WebRequest, exception?: LoadFileError) => {\r\n                onError(request?.statusText, exception);\r\n            };\r\n\r\n            if (!plugin.loadFile && fileInfo.rawData) {\r\n                // eslint-disable-next-line no-throw-literal\r\n                throw \"Plugin does not support loading ArrayBufferView.\";\r\n            }\r\n\r\n            request = plugin.loadFile\r\n                ? plugin.loadFile(scene, fileInfo.rawData || fileInfo.file || fileInfo.url, fileInfo.rootUrl, dataCallback, onProgress, useArrayBuffer, errorCallback, name)\r\n                : scene._loadFile(fileInfo.file || fileInfo.url, dataCallback, onProgress, true, useArrayBuffer, errorCallback);\r\n        };\r\n\r\n        const engine = scene.getEngine();\r\n        let canUseOfflineSupport = engine.enableOfflineSupport;\r\n        if (canUseOfflineSupport) {\r\n            // Also check for exceptions\r\n            let exceptionFound = false;\r\n            for (const regex of scene.disableOfflineSupportExceptionRules) {\r\n                if (regex.test(fileInfo.url)) {\r\n                    exceptionFound = true;\r\n                    break;\r\n                }\r\n            }\r\n\r\n            canUseOfflineSupport = !exceptionFound;\r\n        }\r\n\r\n        if (canUseOfflineSupport && AbstractEngine.OfflineProviderFactory) {\r\n            // Checking if a manifest file has been set for this scene and if offline mode has been requested\r\n            scene.offlineProvider = AbstractEngine.OfflineProviderFactory(fileInfo.url, manifestChecked, engine.disableManifestCheck);\r\n        } else {\r\n            manifestChecked();\r\n        }\r\n    });\r\n}\r\n\r\nfunction GetFileInfo(rootUrl: string, sceneSource: SceneSource): Nullable<IFileInfo> {\r\n    let url: string;\r\n    let name: string;\r\n    let file: Nullable<File> = null;\r\n    let rawData: Nullable<ArrayBufferView> = null;\r\n\r\n    if (!sceneSource) {\r\n        url = rootUrl;\r\n        name = Tools.GetFilename(rootUrl);\r\n        rootUrl = Tools.GetFolderPath(rootUrl);\r\n    } else if (isFile(sceneSource)) {\r\n        url = `file:${sceneSource.name}`;\r\n        name = sceneSource.name;\r\n        file = sceneSource;\r\n    } else if (ArrayBuffer.isView(sceneSource)) {\r\n        url = \"\";\r\n        name = RandomGUID();\r\n        rawData = sceneSource;\r\n    } else if (sceneSource.startsWith(\"data:\")) {\r\n        url = sceneSource;\r\n        name = \"\";\r\n    } else if (rootUrl) {\r\n        const filename = sceneSource;\r\n        if (filename.substring(0, 1) === \"/\") {\r\n            Tools.Error(\"Wrong sceneFilename parameter\");\r\n            return null;\r\n        }\r\n\r\n        url = rootUrl + filename;\r\n        name = filename;\r\n    } else {\r\n        url = sceneSource;\r\n        name = Tools.GetFilename(sceneSource);\r\n        rootUrl = Tools.GetFolderPath(sceneSource);\r\n    }\r\n\r\n    return {\r\n        url: url,\r\n        rootUrl: rootUrl,\r\n        name: name,\r\n        file: file,\r\n        rawData,\r\n    };\r\n}\r\n\r\n/**\r\n * Adds a new plugin to the list of registered plugins\r\n * @param plugin defines the plugin to add\r\n */\r\nexport function RegisterSceneLoaderPlugin(plugin: ISceneLoaderPlugin | ISceneLoaderPluginAsync | ISceneLoaderPluginFactory): void {\r\n    if (typeof plugin.extensions === \"string\") {\r\n        const extension = plugin.extensions;\r\n        registeredPlugins[extension.toLowerCase()] = {\r\n            plugin: plugin,\r\n            isBinary: false,\r\n        };\r\n    } else {\r\n        const extensions = plugin.extensions;\r\n        const keys = Object.keys(extensions);\r\n        for (const extension of keys) {\r\n            registeredPlugins[extension.toLowerCase()] = {\r\n                plugin: plugin,\r\n                isBinary: extensions[extension].isBinary,\r\n                mimeType: extensions[extension].mimeType,\r\n            };\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * Adds a new plugin to the list of registered plugins\r\n * @deprecated Please use {@link RegisterSceneLoaderPlugin} instead.\r\n * @param plugin defines the plugin to add\r\n */\r\nexport function registerSceneLoaderPlugin(plugin: ISceneLoaderPlugin | ISceneLoaderPluginAsync | ISceneLoaderPluginFactory): void {\r\n    RegisterSceneLoaderPlugin(plugin);\r\n}\r\n\r\n/**\r\n * Gets metadata for all currently registered scene loader plugins.\r\n * @returns An array where each entry has metadata for a single scene loader plugin.\r\n */\r\nexport function GetRegisteredSceneLoaderPluginMetadata(): DeepImmutable<\r\n    Array<\r\n        Pick<ISceneLoaderPluginMetadata, \"name\"> & {\r\n            /**\r\n             * The extensions supported by the plugin.\r\n             */\r\n            extensions: ({\r\n                /**\r\n                 * The file extension.\r\n                 */\r\n                extension: string;\r\n            } & ISceneLoaderPluginExtensions[string])[];\r\n        }\r\n    >\r\n> {\r\n    return Array.from(\r\n        Object.entries(registeredPlugins).reduce((pluginMap, [extension, extensionRegistration]) => {\r\n            let pluginMetadata = pluginMap.get(extensionRegistration.plugin.name);\r\n            if (!pluginMetadata) {\r\n                pluginMap.set(extensionRegistration.plugin.name, (pluginMetadata = []));\r\n            }\r\n            pluginMetadata.push({ extension, isBinary: extensionRegistration.isBinary, mimeType: extensionRegistration.mimeType });\r\n            return pluginMap;\r\n        }, new Map<string, ({ extension: string } & ISceneLoaderPluginExtensions[string])[]>())\r\n    ).map(([name, extensions]) => ({ name, extensions }));\r\n}\r\n\r\n/**\r\n * Import meshes into a scene\r\n * @param source a string that defines the name of the scene file, or starts with \"data:\" following by the stringified version of the scene, or a File object, or an ArrayBufferView\r\n * @param scene the instance of BABYLON.Scene to append to\r\n * @param options an object that configures aspects of how the scene is loaded\r\n * @returns The loaded list of imported meshes, particle systems, skeletons, and animation groups\r\n */\r\nexport async function ImportMeshAsync(source: SceneSource, scene: Scene, options?: ImportMeshOptions): Promise<ISceneLoaderAsyncResult> {\r\n    const { meshNames, rootUrl = \"\", onProgress, pluginExtension, name, pluginOptions } = options ?? {};\r\n    return await importMeshAsyncCoreAsync(meshNames, rootUrl, source, scene, onProgress, pluginExtension, name, pluginOptions);\r\n}\r\n\r\nasync function importMeshAsync(\r\n    meshNames: string | readonly string[] | null | undefined,\r\n    rootUrl: string,\r\n    sceneFilename: SceneSource = \"\",\r\n    scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n    onSuccess: Nullable<SceneLoaderSuccessCallback> = null,\r\n    onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n    onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n    pluginExtension: Nullable<string> = null,\r\n    name = \"\",\r\n    pluginOptions: PluginOptions = {}\r\n): Promise<Nullable<ISceneLoaderPlugin | ISceneLoaderPluginAsync>> {\r\n    if (!scene) {\r\n        Logger.Error(\"No scene available to import mesh to\");\r\n        return null;\r\n    }\r\n\r\n    const fileInfo = GetFileInfo(rootUrl, sceneFilename);\r\n    if (!fileInfo) {\r\n        return null;\r\n    }\r\n\r\n    const loadingToken = {};\r\n    scene.addPendingData(loadingToken);\r\n\r\n    const disposeHandler = () => {\r\n        scene.removePendingData(loadingToken);\r\n    };\r\n\r\n    const errorHandler = (message?: string, exception?: any) => {\r\n        const errorMessage = formatErrorMessage(fileInfo, message, exception);\r\n\r\n        if (onError) {\r\n            onError(scene, errorMessage, new RuntimeError(errorMessage, ErrorCodes.SceneLoaderError, exception));\r\n        } else {\r\n            Logger.Error(errorMessage);\r\n            // should the exception be thrown?\r\n        }\r\n\r\n        disposeHandler();\r\n    };\r\n\r\n    const progressHandler = onProgress\r\n        ? (event: ISceneLoaderProgressEvent) => {\r\n              try {\r\n                  onProgress(event);\r\n              } catch (e) {\r\n                  errorHandler(\"Error in onProgress callback: \" + e, e);\r\n              }\r\n          }\r\n        : undefined;\r\n\r\n    const successHandler: SceneLoaderSuccessCallback = (meshes, particleSystems, skeletons, animationGroups, transformNodes, geometries, lights, spriteManagers) => {\r\n        scene.importedMeshesFiles.push(fileInfo.url);\r\n\r\n        if (onSuccess) {\r\n            try {\r\n                onSuccess(meshes, particleSystems, skeletons, animationGroups, transformNodes, geometries, lights, spriteManagers);\r\n            } catch (e) {\r\n                errorHandler(\"Error in onSuccess callback: \" + e, e);\r\n            }\r\n        }\r\n\r\n        scene.removePendingData(loadingToken);\r\n    };\r\n\r\n    return await loadDataAsync(\r\n        fileInfo,\r\n        scene,\r\n        (plugin, data, responseURL) => {\r\n            if (plugin.rewriteRootURL) {\r\n                fileInfo.rootUrl = plugin.rewriteRootURL(fileInfo.rootUrl, responseURL);\r\n            }\r\n\r\n            if ((plugin as ISceneLoaderPlugin).importMesh) {\r\n                const syncedPlugin = <ISceneLoaderPlugin>plugin;\r\n                const meshes: AbstractMesh[] = [];\r\n                const particleSystems: IParticleSystem[] = [];\r\n                const skeletons: Skeleton[] = [];\r\n\r\n                if (!syncedPlugin.importMesh(meshNames, scene, data, fileInfo.rootUrl, meshes, particleSystems, skeletons, errorHandler)) {\r\n                    return;\r\n                }\r\n\r\n                scene.loadingPluginName = plugin.name;\r\n                successHandler(meshes, particleSystems, skeletons, [], [], [], [], []);\r\n            } else {\r\n                const asyncedPlugin = <ISceneLoaderPluginAsync>plugin;\r\n                asyncedPlugin\r\n                    .importMeshAsync(meshNames, scene, data, fileInfo.rootUrl, progressHandler, fileInfo.name)\r\n                    // eslint-disable-next-line github/no-then\r\n                    .then((result) => {\r\n                        scene.loadingPluginName = plugin.name;\r\n                        successHandler(\r\n                            result.meshes,\r\n                            result.particleSystems,\r\n                            result.skeletons,\r\n                            result.animationGroups,\r\n                            result.transformNodes,\r\n                            result.geometries,\r\n                            result.lights,\r\n                            result.spriteManagers\r\n                        );\r\n                    })\r\n                    // eslint-disable-next-line github/no-then\r\n                    .catch((error) => {\r\n                        errorHandler(error.message, error);\r\n                    });\r\n            }\r\n        },\r\n        progressHandler,\r\n        errorHandler,\r\n        disposeHandler,\r\n        pluginExtension,\r\n        name,\r\n        pluginOptions\r\n    );\r\n}\r\n\r\nasync function importMeshAsyncCoreAsync(\r\n    meshNames: string | readonly string[] | null | undefined,\r\n    rootUrl: string,\r\n    sceneFilename?: SceneSource,\r\n    scene?: Nullable<Scene>,\r\n    onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n    pluginExtension?: Nullable<string>,\r\n    name?: string,\r\n    pluginOptions?: PluginOptions\r\n): Promise<ISceneLoaderAsyncResult> {\r\n    return await new Promise((resolve, reject) => {\r\n        try {\r\n            importMeshAsync(\r\n                meshNames,\r\n                rootUrl,\r\n                sceneFilename,\r\n                scene,\r\n                (meshes, particleSystems, skeletons, animationGroups, transformNodes, geometries, lights, spriteManagers) => {\r\n                    resolve({\r\n                        meshes: meshes,\r\n                        particleSystems: particleSystems,\r\n                        skeletons: skeletons,\r\n                        animationGroups: animationGroups,\r\n                        transformNodes: transformNodes,\r\n                        geometries: geometries,\r\n                        lights: lights,\r\n                        spriteManagers: spriteManagers,\r\n                    });\r\n                },\r\n                onProgress,\r\n                (scene, message, exception) => {\r\n                    // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                    reject(exception || new Error(message));\r\n                },\r\n                pluginExtension,\r\n                name,\r\n                pluginOptions\r\n                // eslint-disable-next-line github/no-then\r\n            ).catch(reject);\r\n        } catch (error) {\r\n            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n            reject(error);\r\n        }\r\n    });\r\n}\r\n\r\n// This is the core implementation of load scene\r\nasync function loadSceneImplAsync(\r\n    rootUrl: string,\r\n    sceneFilename: SceneSource = \"\",\r\n    engine: Nullable<AbstractEngine> = EngineStore.LastCreatedEngine,\r\n    onSuccess: Nullable<(scene: Scene) => void> = null,\r\n    onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n    onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n    pluginExtension: Nullable<string> = null,\r\n    name = \"\",\r\n    pluginOptions: PluginOptions = {}\r\n): Promise<void> {\r\n    if (!engine) {\r\n        Tools.Error(\"No engine available\");\r\n        return;\r\n    }\r\n\r\n    await appendSceneImplAsync(rootUrl, sceneFilename, new Scene(engine), onSuccess, onProgress, onError, pluginExtension, name, pluginOptions);\r\n}\r\n\r\n/**\r\n * Load a scene\r\n * @param source a string that defines the name of the scene file, or starts with \"data:\" following by the stringified version of the scene, or a File object, or an ArrayBufferView\r\n * @param engine is the instance of BABYLON.Engine to use to create the scene\r\n * @param options an object that configures aspects of how the scene is loaded\r\n * @returns The loaded scene\r\n */\r\nexport async function LoadSceneAsync(source: SceneSource, engine: AbstractEngine, options?: LoadOptions): Promise<Scene> {\r\n    const { rootUrl = \"\", onProgress, pluginExtension, name, pluginOptions } = options ?? {};\r\n    return await loadSceneSharedAsync(rootUrl, source, engine, onProgress, pluginExtension, name, pluginOptions);\r\n}\r\n\r\n/**\r\n * Load a scene\r\n * @deprecated Please use {@link LoadSceneAsync} instead.\r\n * @param source a string that defines the name of the scene file, or starts with \"data:\" following by the stringified version of the scene, or a File object, or an ArrayBufferView\r\n * @param engine is the instance of BABYLON.Engine to use to create the scene\r\n * @param options an object that configures aspects of how the scene is loaded\r\n * @returns The loaded scene\r\n */\r\nexport async function loadSceneAsync(source: SceneSource, engine: AbstractEngine, options?: LoadOptions): Promise<Scene> {\r\n    return await LoadSceneAsync(source, engine, options);\r\n}\r\n\r\n// This function is shared between the new module level loadSceneAsync and the legacy SceneLoader.LoadAsync\r\nasync function loadSceneSharedAsync(\r\n    rootUrl: string,\r\n    sceneFilename?: SceneSource,\r\n    engine?: Nullable<AbstractEngine>,\r\n    onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n    pluginExtension?: Nullable<string>,\r\n    name?: string,\r\n    pluginOptions?: PluginOptions\r\n): Promise<Scene> {\r\n    return await new Promise((resolve, reject) => {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        loadSceneImplAsync(\r\n            rootUrl,\r\n            sceneFilename,\r\n            engine,\r\n            (scene) => {\r\n                resolve(scene);\r\n            },\r\n            onProgress,\r\n            (scene, message, exception) => {\r\n                // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                reject(exception || new Error(message));\r\n            },\r\n            pluginExtension,\r\n            name,\r\n            pluginOptions\r\n        );\r\n    });\r\n}\r\n\r\n// This is the core implementation of append scene\r\nasync function appendSceneImplAsync(\r\n    rootUrl: string,\r\n    sceneFilename: SceneSource = \"\",\r\n    scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n    onSuccess: Nullable<(scene: Scene) => void> = null,\r\n    onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n    onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n    pluginExtension: Nullable<string> = null,\r\n    name = \"\",\r\n    pluginOptions: PluginOptions = {}\r\n): Promise<Nullable<ISceneLoaderPlugin | ISceneLoaderPluginAsync>> {\r\n    if (!scene) {\r\n        Logger.Error(\"No scene available to append to\");\r\n        return null;\r\n    }\r\n\r\n    const fileInfo = GetFileInfo(rootUrl, sceneFilename);\r\n    if (!fileInfo) {\r\n        return null;\r\n    }\r\n\r\n    const loadingToken = {};\r\n    scene.addPendingData(loadingToken);\r\n\r\n    const disposeHandler = () => {\r\n        scene.removePendingData(loadingToken);\r\n    };\r\n\r\n    if (SceneLoaderFlags.ShowLoadingScreen && !showingLoadingScreen) {\r\n        showingLoadingScreen = true;\r\n        scene.getEngine().displayLoadingUI();\r\n        scene.executeWhenReady(() => {\r\n            scene.getEngine().hideLoadingUI();\r\n            showingLoadingScreen = false;\r\n        });\r\n    }\r\n\r\n    const errorHandler = (message?: string, exception?: any) => {\r\n        const errorMessage = formatErrorMessage(fileInfo, message, exception);\r\n\r\n        if (onError) {\r\n            onError(scene, errorMessage, new RuntimeError(errorMessage, ErrorCodes.SceneLoaderError, exception));\r\n        } else {\r\n            Logger.Error(errorMessage);\r\n            // should the exception be thrown?\r\n        }\r\n\r\n        disposeHandler();\r\n    };\r\n\r\n    const progressHandler = onProgress\r\n        ? (event: ISceneLoaderProgressEvent) => {\r\n              try {\r\n                  onProgress(event);\r\n              } catch (e) {\r\n                  errorHandler(\"Error in onProgress callback\", e);\r\n              }\r\n          }\r\n        : undefined;\r\n\r\n    const successHandler = () => {\r\n        if (onSuccess) {\r\n            try {\r\n                onSuccess(scene);\r\n            } catch (e) {\r\n                errorHandler(\"Error in onSuccess callback\", e);\r\n            }\r\n        }\r\n\r\n        scene.removePendingData(loadingToken);\r\n    };\r\n\r\n    return await loadDataAsync(\r\n        fileInfo,\r\n        scene,\r\n        (plugin, data) => {\r\n            if ((plugin as ISceneLoaderPlugin).load) {\r\n                const syncedPlugin = <ISceneLoaderPlugin>plugin;\r\n                if (!syncedPlugin.load(scene, data, fileInfo.rootUrl, errorHandler)) {\r\n                    return;\r\n                }\r\n\r\n                scene.loadingPluginName = plugin.name;\r\n                successHandler();\r\n            } else {\r\n                const asyncedPlugin = <ISceneLoaderPluginAsync>plugin;\r\n                asyncedPlugin\r\n                    .loadAsync(scene, data, fileInfo.rootUrl, progressHandler, fileInfo.name)\r\n                    // eslint-disable-next-line github/no-then\r\n                    .then(() => {\r\n                        scene.loadingPluginName = plugin.name;\r\n                        successHandler();\r\n                    })\r\n                    // eslint-disable-next-line github/no-then\r\n                    .catch((error) => {\r\n                        errorHandler(error.message, error);\r\n                    });\r\n            }\r\n        },\r\n        progressHandler,\r\n        errorHandler,\r\n        disposeHandler,\r\n        pluginExtension,\r\n        name,\r\n        pluginOptions\r\n    );\r\n}\r\n\r\n/**\r\n * Append a scene\r\n * @param source a string that defines the name of the scene file, or starts with \"data:\" following by the stringified version of the scene, or a File object, or an ArrayBufferView\r\n * @param scene is the instance of BABYLON.Scene to append to\r\n * @param options an object that configures aspects of how the scene is loaded\r\n * @returns A promise that resolves when the scene is appended\r\n */\r\nexport async function AppendSceneAsync(source: SceneSource, scene: Scene, options?: AppendOptions): Promise<void> {\r\n    const { rootUrl = \"\", onProgress, pluginExtension, name, pluginOptions } = options ?? {};\r\n    await appendSceneSharedAsync(rootUrl, source, scene, onProgress, pluginExtension, name, pluginOptions);\r\n}\r\n\r\n/**\r\n * Append a scene\r\n * @deprecated Please use {@link AppendSceneAsync} instead.\r\n * @param source a string that defines the name of the scene file, or starts with \"data:\" following by the stringified version of the scene, or a File object, or an ArrayBufferView\r\n * @param scene is the instance of BABYLON.Scene to append to\r\n * @param options an object that configures aspects of how the scene is loaded\r\n * @returns A promise that resolves when the scene is appended\r\n */\r\nexport async function appendSceneAsync(source: SceneSource, scene: Scene, options?: AppendOptions): Promise<void> {\r\n    return await AppendSceneAsync(source, scene, options);\r\n}\r\n\r\n// This function is shared between the new module level appendSceneAsync and the legacy SceneLoader.AppendAsync\r\nasync function appendSceneSharedAsync(\r\n    rootUrl: string,\r\n    sceneFilename?: SceneSource,\r\n    scene?: Nullable<Scene>,\r\n    onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n    pluginExtension?: Nullable<string>,\r\n    name?: string,\r\n    pluginOptions?: PluginOptions\r\n): Promise<Scene> {\r\n    return await new Promise((resolve, reject) => {\r\n        try {\r\n            appendSceneImplAsync(\r\n                rootUrl,\r\n                sceneFilename,\r\n                scene,\r\n                (scene) => {\r\n                    resolve(scene);\r\n                },\r\n                onProgress,\r\n                (scene, message, exception) => {\r\n                    // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                    reject(exception || new Error(message));\r\n                },\r\n                pluginExtension,\r\n                name,\r\n                pluginOptions\r\n                // eslint-disable-next-line github/no-then\r\n            ).catch(reject);\r\n        } catch (error) {\r\n            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n            reject(error);\r\n        }\r\n    });\r\n}\r\n\r\n// This is the core implementation of load asset container\r\nasync function loadAssetContainerImplAsync(\r\n    rootUrl: string,\r\n    sceneFilename: SceneSource = \"\",\r\n    scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n    onSuccess: Nullable<(assets: AssetContainer) => void> = null,\r\n    onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n    onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n    pluginExtension: Nullable<string> = null,\r\n    name = \"\",\r\n    pluginOptions: PluginOptions = {}\r\n): Promise<Nullable<ISceneLoaderPlugin | ISceneLoaderPluginAsync>> {\r\n    if (!scene) {\r\n        Logger.Error(\"No scene available to load asset container to\");\r\n        return null;\r\n    }\r\n\r\n    const fileInfo = GetFileInfo(rootUrl, sceneFilename);\r\n    if (!fileInfo) {\r\n        return null;\r\n    }\r\n\r\n    const loadingToken = {};\r\n    scene.addPendingData(loadingToken);\r\n\r\n    const disposeHandler = () => {\r\n        scene.removePendingData(loadingToken);\r\n    };\r\n\r\n    const errorHandler = (message?: string, exception?: any) => {\r\n        const errorMessage = formatErrorMessage(fileInfo, message, exception);\r\n\r\n        if (onError) {\r\n            onError(scene, errorMessage, new RuntimeError(errorMessage, ErrorCodes.SceneLoaderError, exception));\r\n        } else {\r\n            Logger.Error(errorMessage);\r\n            // should the exception be thrown?\r\n        }\r\n\r\n        disposeHandler();\r\n    };\r\n\r\n    const progressHandler = onProgress\r\n        ? (event: ISceneLoaderProgressEvent) => {\r\n              try {\r\n                  onProgress(event);\r\n              } catch (e) {\r\n                  errorHandler(\"Error in onProgress callback\", e);\r\n              }\r\n          }\r\n        : undefined;\r\n\r\n    const successHandler = (assets: AssetContainer) => {\r\n        if (onSuccess) {\r\n            try {\r\n                onSuccess(assets);\r\n            } catch (e) {\r\n                errorHandler(\"Error in onSuccess callback\", e);\r\n            }\r\n        }\r\n\r\n        scene.removePendingData(loadingToken);\r\n    };\r\n\r\n    return await loadDataAsync(\r\n        fileInfo,\r\n        scene,\r\n        (plugin, data) => {\r\n            if ((plugin as ISceneLoaderPlugin).loadAssetContainer) {\r\n                const syncedPlugin = <ISceneLoaderPlugin>plugin;\r\n                const assetContainer = syncedPlugin.loadAssetContainer(scene, data, fileInfo.rootUrl, errorHandler);\r\n                if (!assetContainer) {\r\n                    return;\r\n                }\r\n                assetContainer.populateRootNodes();\r\n                scene.loadingPluginName = plugin.name;\r\n                successHandler(assetContainer);\r\n            } else if ((plugin as ISceneLoaderPluginAsync).loadAssetContainerAsync) {\r\n                const asyncedPlugin = <ISceneLoaderPluginAsync>plugin;\r\n                asyncedPlugin\r\n                    .loadAssetContainerAsync(scene, data, fileInfo.rootUrl, progressHandler, fileInfo.name)\r\n                    // eslint-disable-next-line github/no-then\r\n                    .then((assetContainer) => {\r\n                        assetContainer.populateRootNodes();\r\n                        scene.loadingPluginName = plugin.name;\r\n                        successHandler(assetContainer);\r\n                    })\r\n                    // eslint-disable-next-line github/no-then\r\n                    .catch((error) => {\r\n                        errorHandler(error.message, error);\r\n                    });\r\n            } else {\r\n                errorHandler(\"LoadAssetContainer is not supported by this plugin. Plugin did not provide a loadAssetContainer or loadAssetContainerAsync method.\");\r\n            }\r\n        },\r\n        progressHandler,\r\n        errorHandler,\r\n        disposeHandler,\r\n        pluginExtension,\r\n        name,\r\n        pluginOptions\r\n    );\r\n}\r\n\r\n/**\r\n * Load a scene into an asset container\r\n * @param source a string that defines the name of the scene file, or starts with \"data:\" following by the stringified version of the scene, or a File object, or an ArrayBufferView\r\n * @param scene is the instance of Scene to append to\r\n * @param options an object that configures aspects of how the scene is loaded\r\n * @returns The loaded asset container\r\n */\r\nexport async function LoadAssetContainerAsync(source: SceneSource, scene: Scene, options?: LoadAssetContainerOptions): Promise<AssetContainer> {\r\n    const { rootUrl = \"\", onProgress, pluginExtension, name, pluginOptions } = options ?? {};\r\n    return await loadAssetContainerSharedAsync(rootUrl, source, scene, onProgress, pluginExtension, name, pluginOptions);\r\n}\r\n\r\n/**\r\n * Load a scene into an asset container\r\n * @deprecated Please use {@link LoadAssetContainerAsync} instead.\r\n * @param source a string that defines the name of the scene file, or starts with \"data:\" following by the stringified version of the scene, or a File object, or an ArrayBufferView\r\n * @param scene is the instance of Scene to append to\r\n * @param options an object that configures aspects of how the scene is loaded\r\n * @returns The loaded asset container\r\n */\r\nexport async function loadAssetContainerAsync(source: SceneSource, scene: Scene, options?: LoadAssetContainerOptions): Promise<AssetContainer> {\r\n    return await LoadAssetContainerAsync(source, scene, options);\r\n}\r\n\r\n// This function is shared between the new module level loadAssetContainerAsync and the legacy SceneLoader.LoadAssetContainerAsync\r\nasync function loadAssetContainerSharedAsync(\r\n    rootUrl: string,\r\n    sceneFilename?: SceneSource,\r\n    scene?: Nullable<Scene>,\r\n    onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n    pluginExtension?: Nullable<string>,\r\n    name?: string,\r\n    pluginOptions?: PluginOptions\r\n): Promise<AssetContainer> {\r\n    return await new Promise((resolve, reject) => {\r\n        try {\r\n            loadAssetContainerImplAsync(\r\n                rootUrl,\r\n                sceneFilename,\r\n                scene,\r\n                (assets) => {\r\n                    resolve(assets);\r\n                },\r\n                onProgress,\r\n                (scene, message, exception) => {\r\n                    // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                    reject(exception || new Error(message));\r\n                },\r\n                pluginExtension,\r\n                name,\r\n                pluginOptions\r\n                // eslint-disable-next-line github/no-then\r\n            ).catch(reject);\r\n        } catch (error) {\r\n            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n            reject(error);\r\n        }\r\n    });\r\n}\r\n\r\n// This is the core implementation of import animations\r\nasync function importAnimationsImplAsync(\r\n    rootUrl: string,\r\n    sceneFilename: SceneSource = \"\",\r\n    scene: Nullable<Scene> = EngineStore.LastCreatedScene,\r\n    overwriteAnimations = true,\r\n    animationGroupLoadingMode = SceneLoaderAnimationGroupLoadingMode.Clean,\r\n    targetConverter: Nullable<(target: any) => any> = null,\r\n    onSuccess: Nullable<(scene: Scene) => void> = null,\r\n    onProgress: Nullable<(event: ISceneLoaderProgressEvent) => void> = null,\r\n    onError: Nullable<(scene: Scene, message: string, exception?: any) => void> = null,\r\n    pluginExtension: Nullable<string> = null,\r\n    name = \"\",\r\n    pluginOptions: PluginOptions = {}\r\n): Promise<void> {\r\n    if (!scene) {\r\n        Logger.Error(\"No scene available to load animations to\");\r\n        return;\r\n    }\r\n\r\n    if (overwriteAnimations) {\r\n        // Reset, stop and dispose all animations before loading new ones\r\n        for (const animatable of scene.animatables) {\r\n            animatable.reset();\r\n        }\r\n        scene.stopAllAnimations();\r\n        const animationGroups = scene.animationGroups.slice();\r\n        for (const animationGroup of animationGroups) {\r\n            animationGroup.dispose();\r\n        }\r\n        const nodes = scene.getNodes();\r\n        for (const node of nodes) {\r\n            if (node.animations) {\r\n                node.animations = [];\r\n            }\r\n        }\r\n    } else {\r\n        switch (animationGroupLoadingMode as number) {\r\n            case SceneLoaderAnimationGroupLoadingMode.Clean:\r\n                const animationGroups = scene.animationGroups.slice();\r\n                for (const animationGroup of animationGroups) {\r\n                    animationGroup.dispose();\r\n                }\r\n                break;\r\n            case SceneLoaderAnimationGroupLoadingMode.Stop:\r\n                for (const animationGroup of scene.animationGroups) {\r\n                    animationGroup.stop();\r\n                }\r\n                break;\r\n            case SceneLoaderAnimationGroupLoadingMode.Sync:\r\n                for (const animationGroup of scene.animationGroups) {\r\n                    animationGroup.reset();\r\n                    animationGroup.restart();\r\n                }\r\n                break;\r\n            case SceneLoaderAnimationGroupLoadingMode.NoSync:\r\n                // nothing to do\r\n                break;\r\n            default:\r\n                Logger.Error(\"Unknown animation group loading mode value '\" + animationGroupLoadingMode + \"'\");\r\n                return;\r\n        }\r\n    }\r\n\r\n    const startingIndexForNewAnimatables = scene.animatables.length;\r\n\r\n    const onAssetContainerLoaded = (container: AssetContainer) => {\r\n        container.mergeAnimationsTo(scene, scene.animatables.slice(startingIndexForNewAnimatables), targetConverter);\r\n\r\n        container.dispose();\r\n\r\n        scene.onAnimationFileImportedObservable.notifyObservers(scene);\r\n\r\n        if (onSuccess) {\r\n            onSuccess(scene);\r\n        }\r\n    };\r\n\r\n    await loadAssetContainerImplAsync(rootUrl, sceneFilename, scene, onAssetContainerLoaded, onProgress, onError, pluginExtension, name, pluginOptions);\r\n}\r\n\r\n/**\r\n * Import animations from a file into a scene\r\n * @param source a string that defines the name of the scene file, or starts with \"data:\" following by the stringified version of the scene, or a File object, or an ArrayBufferView\r\n * @param scene is the instance of BABYLON.Scene to append to\r\n * @param options an object that configures aspects of how the scene is loaded\r\n * @returns A promise that resolves when the animations are imported\r\n */\r\nexport async function ImportAnimationsAsync(source: SceneSource, scene: Scene, options?: ImportAnimationsOptions): Promise<void> {\r\n    const { rootUrl = \"\", overwriteAnimations, animationGroupLoadingMode, targetConverter, onProgress, pluginExtension, name, pluginOptions } = options ?? {};\r\n    await importAnimationsSharedAsync(rootUrl, source, scene, overwriteAnimations, animationGroupLoadingMode, targetConverter, onProgress, pluginExtension, name, pluginOptions);\r\n}\r\n\r\n/**\r\n * Import animations from a file into a scene\r\n * @deprecated Please use {@link ImportAnimationsAsync} instead.\r\n * @param source a string that defines the name of the scene file, or starts with \"data:\" following by the stringified version of the scene, or a File object, or an ArrayBufferView\r\n * @param scene is the instance of BABYLON.Scene to append to\r\n * @param options an object that configures aspects of how the scene is loaded\r\n * @returns A promise that resolves when the animations are imported\r\n */\r\nexport async function importAnimationsAsync(source: SceneSource, scene: Scene, options?: ImportAnimationsOptions): Promise<void> {\r\n    return await ImportAnimationsAsync(source, scene, options);\r\n}\r\n\r\n// This function is shared between the new module level importAnimationsAsync and the legacy SceneLoader.ImportAnimationsAsync\r\nasync function importAnimationsSharedAsync(\r\n    rootUrl: string,\r\n    sceneFilename?: SceneSource,\r\n    scene?: Nullable<Scene>,\r\n    overwriteAnimations?: boolean,\r\n    animationGroupLoadingMode?: SceneLoaderAnimationGroupLoadingMode,\r\n    targetConverter?: Nullable<(target: any) => any>,\r\n    onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n    pluginExtension?: Nullable<string>,\r\n    name?: string,\r\n    pluginOptions?: PluginOptions\r\n): Promise<Scene> {\r\n    return await new Promise((resolve, reject) => {\r\n        try {\r\n            importAnimationsImplAsync(\r\n                rootUrl,\r\n                sceneFilename,\r\n                scene,\r\n                overwriteAnimations,\r\n                animationGroupLoadingMode,\r\n                targetConverter,\r\n                (scene) => {\r\n                    resolve(scene);\r\n                },\r\n                onProgress,\r\n                (scene, message, exception) => {\r\n                    // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                    reject(exception || new Error(message));\r\n                },\r\n                pluginExtension,\r\n                name,\r\n                pluginOptions\r\n                // eslint-disable-next-line github/no-then\r\n            ).catch(reject);\r\n        } catch (error) {\r\n            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n            reject(error);\r\n        }\r\n    });\r\n}\r\n\r\n/**\r\n * Class used to load scene from various file formats using registered plugins\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/importers/loadingFileTypes\r\n * @deprecated The module level functions are more efficient for bundler tree shaking and allow plugin options to be passed through. Future improvements to scene loading will primarily be in the module level functions. The SceneLoader class will remain available, but it will be beneficial to prefer the module level functions.\r\n * @see {@link ImportMeshAsync}, {@link LoadSceneAsync}, {@link AppendSceneAsync}, {@link ImportAnimationsAsync}, {@link LoadAssetContainerAsync}\r\n */\r\nexport class SceneLoader {\r\n    /**\r\n     * No logging while loading\r\n     */\r\n    public static readonly NO_LOGGING = Constants.SCENELOADER_NO_LOGGING;\r\n\r\n    /**\r\n     * Minimal logging while loading\r\n     */\r\n    public static readonly MINIMAL_LOGGING = Constants.SCENELOADER_MINIMAL_LOGGING;\r\n\r\n    /**\r\n     * Summary logging while loading\r\n     */\r\n    public static readonly SUMMARY_LOGGING = Constants.SCENELOADER_SUMMARY_LOGGING;\r\n\r\n    /**\r\n     * Detailed logging while loading\r\n     */\r\n    public static readonly DETAILED_LOGGING = Constants.SCENELOADER_DETAILED_LOGGING;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if entire scene must be loaded even if scene contains incremental data\r\n     */\r\n    public static get ForceFullSceneLoadingForIncremental() {\r\n        return SceneLoaderFlags.ForceFullSceneLoadingForIncremental;\r\n    }\r\n\r\n    public static set ForceFullSceneLoadingForIncremental(value: boolean) {\r\n        SceneLoaderFlags.ForceFullSceneLoadingForIncremental = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if loading screen must be displayed while loading a scene\r\n     */\r\n    public static get ShowLoadingScreen(): boolean {\r\n        return SceneLoaderFlags.ShowLoadingScreen;\r\n    }\r\n\r\n    public static set ShowLoadingScreen(value: boolean) {\r\n        SceneLoaderFlags.ShowLoadingScreen = value;\r\n    }\r\n\r\n    /**\r\n     * Defines the current logging level (while loading the scene)\r\n     * @ignorenaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static get loggingLevel(): number {\r\n        return SceneLoaderFlags.loggingLevel;\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public static set loggingLevel(value: number) {\r\n        SceneLoaderFlags.loggingLevel = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or set a boolean indicating if matrix weights must be cleaned upon loading\r\n     */\r\n    public static get CleanBoneMatrixWeights(): boolean {\r\n        return SceneLoaderFlags.CleanBoneMatrixWeights;\r\n    }\r\n\r\n    public static set CleanBoneMatrixWeights(value: boolean) {\r\n        SceneLoaderFlags.CleanBoneMatrixWeights = value;\r\n    }\r\n\r\n    // Members\r\n\r\n    /**\r\n     * Event raised when a plugin is used to load a scene\r\n     */\r\n    public static readonly OnPluginActivatedObservable = onPluginActivatedObservable;\r\n\r\n    /**\r\n     * Gets the default plugin (used to load Babylon files)\r\n     * @returns the .babylon plugin\r\n     */\r\n    public static GetDefaultPlugin(): IRegisteredPlugin | undefined {\r\n        return getDefaultPlugin();\r\n    }\r\n\r\n    // Public functions\r\n\r\n    /**\r\n     * Gets a plugin that can load the given extension\r\n     * @param extension defines the extension to load\r\n     * @returns a plugin or null if none works\r\n     */\r\n    public static GetPluginForExtension(extension: string): ISceneLoaderPlugin | ISceneLoaderPluginAsync | ISceneLoaderPluginFactory | undefined {\r\n        return getPluginForExtension(extension, true)?.plugin;\r\n    }\r\n\r\n    /**\r\n     * Gets a boolean indicating that the given extension can be loaded\r\n     * @param extension defines the extension to load\r\n     * @returns true if the extension is supported\r\n     */\r\n    public static IsPluginForExtensionAvailable(extension: string): boolean {\r\n        return isPluginForExtensionAvailable(extension);\r\n    }\r\n\r\n    /**\r\n     * Adds a new plugin to the list of registered plugins\r\n     * @param plugin defines the plugin to add\r\n     */\r\n    public static RegisterPlugin(plugin: ISceneLoaderPlugin | ISceneLoaderPluginAsync | ISceneLoaderPluginFactory): void {\r\n        RegisterSceneLoaderPlugin(plugin);\r\n    }\r\n\r\n    /**\r\n     * Import meshes into a scene\r\n     * @param meshNames an array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene the instance of BABYLON.Scene to append to\r\n     * @param onSuccess a callback with a list of imported meshes, particleSystems, skeletons, and animationGroups when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the name of the file, if the data is binary\r\n     * @deprecated Please use the module level {@link ImportMeshAsync} instead\r\n     */\r\n    public static ImportMesh(\r\n        meshNames: string | readonly string[] | null | undefined,\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        scene?: Nullable<Scene>,\r\n        onSuccess?: Nullable<SceneLoaderSuccessCallback>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        onError?: Nullable<(scene: Scene, message: string, exception?: any) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ): void {\r\n        // eslint-disable-next-line github/no-then\r\n        importMeshAsync(meshNames, rootUrl, sceneFilename, scene, onSuccess, onProgress, onError, pluginExtension, name).catch((error) =>\r\n            onError?.(EngineStore.LastCreatedScene!, error?.message, error)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Import meshes into a scene\r\n     * @param meshNames an array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene the instance of BABYLON.Scene to append to\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the name of the file\r\n     * @returns The loaded list of imported meshes, particle systems, skeletons, and animation groups\r\n     * @deprecated Please use the module level {@link ImportMeshAsync} instead\r\n     */\r\n    public static async ImportMeshAsync(\r\n        meshNames: string | readonly string[] | null | undefined,\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        scene?: Nullable<Scene>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ): Promise<ISceneLoaderAsyncResult> {\r\n        return await importMeshAsyncCoreAsync(meshNames, rootUrl, sceneFilename, scene, onProgress, pluginExtension, name);\r\n    }\r\n\r\n    /**\r\n     * Load a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param engine is the instance of BABYLON.Engine to use to create the scene\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the filename, if the data is binary\r\n     * @deprecated Please use the module level {@link LoadSceneAsync} instead\r\n     */\r\n    public static Load(\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        engine?: Nullable<AbstractEngine>,\r\n        onSuccess?: Nullable<(scene: Scene) => void>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        onError?: Nullable<(scene: Scene, message: string, exception?: any) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ) {\r\n        // eslint-disable-next-line github/no-then\r\n        loadSceneImplAsync(rootUrl, sceneFilename, engine, onSuccess, onProgress, onError, pluginExtension, name).catch((error) =>\r\n            onError?.(EngineStore.LastCreatedScene!, error?.message, error)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Load a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param engine is the instance of BABYLON.Engine to use to create the scene\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the filename, if the data is binary\r\n     * @returns The loaded scene\r\n     * @deprecated Please use the module level {@link LoadSceneAsync} instead\r\n     */\r\n    public static async LoadAsync(\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        engine?: Nullable<AbstractEngine>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ): Promise<Scene> {\r\n        return await loadSceneSharedAsync(rootUrl, sceneFilename, engine, onProgress, pluginExtension, name);\r\n    }\r\n\r\n    /**\r\n     * Append a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the name of the file, if the data is binary\r\n     * @deprecated Please use the module level {@link AppendSceneAsync} instead\r\n     */\r\n    public static Append(\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        scene?: Nullable<Scene>,\r\n        onSuccess?: Nullable<(scene: Scene) => void>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        onError?: Nullable<(scene: Scene, message: string, exception?: any) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ) {\r\n        // eslint-disable-next-line github/no-then\r\n        appendSceneImplAsync(rootUrl, sceneFilename, scene, onSuccess, onProgress, onError, pluginExtension, name).catch((error) =>\r\n            onError?.((scene ?? EngineStore.LastCreatedScene)!, error?.message, error)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Append a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the name of the file, if the data is binary\r\n     * @returns The given scene\r\n     * @deprecated Please use the module level {@link AppendSceneAsync} instead\r\n     */\r\n    public static async AppendAsync(\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        scene?: Nullable<Scene>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ): Promise<Scene> {\r\n        return await appendSceneSharedAsync(rootUrl, sceneFilename, scene, onProgress, pluginExtension, name);\r\n    }\r\n\r\n    /**\r\n     * Load a scene into an asset container\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to (default: last created scene)\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the filename, if the data is binary\r\n     * @deprecated Please use the module level {@link LoadAssetContainerAsync} instead\r\n     */\r\n    public static LoadAssetContainer(\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        scene?: Nullable<Scene>,\r\n        onSuccess?: Nullable<(assets: AssetContainer) => void>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        onError?: Nullable<(scene: Scene, message: string, exception?: any) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ) {\r\n        // eslint-disable-next-line github/no-then\r\n        loadAssetContainerImplAsync(rootUrl, sceneFilename, scene, onSuccess, onProgress, onError, pluginExtension, name).catch((error) =>\r\n            onError?.((scene ?? EngineStore.LastCreatedScene)!, error?.message, error)\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Load a scene into an asset container\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene (default: empty string)\r\n     * @param scene is the instance of Scene to append to\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the filename, if the data is binary\r\n     * @returns The loaded asset container\r\n     * @deprecated Please use the module level {@link LoadAssetContainerAsync} instead\r\n     */\r\n    public static async LoadAssetContainerAsync(\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        scene?: Nullable<Scene>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ): Promise<AssetContainer> {\r\n        return await loadAssetContainerSharedAsync(rootUrl, sceneFilename, scene, onProgress, pluginExtension, name);\r\n    }\r\n\r\n    /**\r\n     * Import animations from a file into a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to (default: last created scene)\r\n     * @param overwriteAnimations when true, animations are cleaned before importing new ones. Animations are appended otherwise\r\n     * @param animationGroupLoadingMode defines how to handle old animations groups before importing new ones\r\n     * @param targetConverter defines a function used to convert animation targets from loaded scene to current scene (default: search node by name)\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the filename, if the data is binary\r\n     * @deprecated Please use the module level {@link ImportAnimationsAsync} instead\r\n     */\r\n    public static ImportAnimations(\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        scene?: Nullable<Scene>,\r\n        overwriteAnimations?: boolean,\r\n        animationGroupLoadingMode?: SceneLoaderAnimationGroupLoadingMode,\r\n        targetConverter?: Nullable<(target: any) => any>,\r\n        onSuccess?: Nullable<(scene: Scene) => void>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        onError?: Nullable<(scene: Scene, message: string, exception?: any) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ): void {\r\n        importAnimationsImplAsync(\r\n            rootUrl,\r\n            sceneFilename,\r\n            scene,\r\n            overwriteAnimations,\r\n            animationGroupLoadingMode,\r\n            targetConverter,\r\n            onSuccess,\r\n            onProgress,\r\n            onError,\r\n            pluginExtension,\r\n            name\r\n            // eslint-disable-next-line github/no-then\r\n        ).catch((error) => onError?.((scene ?? EngineStore.LastCreatedScene)!, error?.message, error));\r\n    }\r\n\r\n    /**\r\n     * Import animations from a file into a scene\r\n     * @param rootUrl a string that defines the root url for the scene and resources or the concatenation of rootURL and filename (e.g. http://example.com/test.glb)\r\n     * @param sceneFilename a string that defines the name of the scene file or starts with \"data:\" following by the stringified version of the scene or a File object (default: empty string)\r\n     * @param scene is the instance of BABYLON.Scene to append to (default: last created scene)\r\n     * @param overwriteAnimations when true, animations are cleaned before importing new ones. Animations are appended otherwise\r\n     * @param animationGroupLoadingMode defines how to handle old animations groups before importing new ones\r\n     * @param targetConverter defines a function used to convert animation targets from loaded scene to current scene (default: search node by name)\r\n     * @param onSuccess a callback with the scene when import succeeds\r\n     * @param onProgress a callback with a progress event for each file being loaded\r\n     * @param onError a callback with the scene, a message, and possibly an exception when import fails\r\n     * @param pluginExtension the extension used to determine the plugin\r\n     * @param name defines the filename, if the data is binary\r\n     * @returns the updated scene with imported animations\r\n     * @deprecated Please use the module level {@link ImportAnimationsAsync} instead\r\n     */\r\n    public static async ImportAnimationsAsync(\r\n        rootUrl: string,\r\n        sceneFilename?: SceneSource,\r\n        scene?: Nullable<Scene>,\r\n        overwriteAnimations?: boolean,\r\n        animationGroupLoadingMode?: SceneLoaderAnimationGroupLoadingMode,\r\n        targetConverter?: Nullable<(target: any) => any>,\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        onSuccess?: Nullable<(scene: Scene) => void>,\r\n        onProgress?: Nullable<(event: ISceneLoaderProgressEvent) => void>,\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        onError?: Nullable<(scene: Scene, message: string, exception?: any) => void>,\r\n        pluginExtension?: Nullable<string>,\r\n        name?: string\r\n    ): Promise<Scene> {\r\n        return await importAnimationsSharedAsync(rootUrl, sceneFilename, scene, overwriteAnimations, animationGroupLoadingMode, targetConverter, onProgress, pluginExtension, name);\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA,uDAAA,EAAyD;;;;;;;;;;;;;;;;AACzD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAMrD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAItD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAIpD,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAEzD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,oCAAmC;;;;;;;;;;;;AA8QzD,IAAkB,oCAoBjB;AApBD,CAAA,SAAkB,oCAAoC;IAClD;;OAEG,CACH,oCAAA,CAAA,oCAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAS,CAAA;IAET;;OAEG,CACH,oCAAA,CAAA,oCAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;IAER;;OAEG,CACH,oCAAA,CAAA,oCAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;IAER;;OAEG,CACH,oCAAA,CAAA,oCAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;AACd,CAAC,EApBiB,oCAAoC,IAAA,CAApC,oCAAoC,GAAA,CAAA,CAAA,GAoBrD;AA2BD,SAAS,SAAS,CAAC,eAA4C;IAC3D,OAAO,CAAC,CAAE,eAA6C,CAAC,YAAY,CAAC;AACzE,CAAC;AAsID,SAAS,MAAM,CAAC,KAAc;IAC1B,OAAO,CAAC,CAAE,KAAc,CAAC,IAAI,CAAC;AAClC,CAAC;AAED,MAAM,2BAA2B,GAAG,iKAAI,aAAU,EAAgD,CAAC;AACnG,MAAM,iBAAiB,GAA+C,CAAA,CAAE,CAAC;AACzE,IAAI,oBAAoB,GAAG,KAAK,CAAC;AAEjC,SAAS,gBAAgB;IACrB,OAAO,iBAAiB,CAAC,UAAU,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,oBAAoB,CAAC,QAAgB;IAC1C,IAAK,MAAM,mBAAmB,IAAI,iBAAiB,CAAE,CAAC;QAClD,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;QAChE,IAAI,gBAAgB,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO,gBAAgB,CAAC;QAC5B,CAAC;IACL,CAAC;IACD,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,SAAS,qBAAqB,CAAC,SAAiB,EAAE,aAAsB;IACpE,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;IACtD,IAAI,gBAAgB,EAAE,CAAC;QACnB,OAAO,gBAAgB,CAAC;IAC5B,CAAC;6JACD,SAAM,CAAC,IAAI,CACP,kCAAkC,GAC9B,SAAS,GACT,gLAAgL,CACvL,CAAC;IACF,OAAO,aAAa,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AAC1D,CAAC;AAED,SAAS,6BAA6B,CAAC,SAAiB;IACpD,OAAO,CAAC,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAY;IACxC,IAAK,MAAM,SAAS,IAAI,iBAAiB,CAAE,CAAC;QACxC,MAAM,MAAM,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QAEnD,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,OAAO,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACxC,CAAC;IACL,CAAC;IAED,OAAO,gBAAgB,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,oBAAoB,CAAC,aAAqB;IAC/C,MAAM,mBAAmB,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAEvD,IAAI,mBAAmB,KAAK,CAAC,CAAC,EAAE,CAAC;QAC7B,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAEnD,OAAO,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;AACpF,CAAC;AAED,SAAS,aAAa,CAAC,aAAqB;IACxC,IAAI,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;QAC5C,OAAO,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACtC,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,QAAmB,EAAE,OAAgB,EAAE,SAAe;IAC9E,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;IACjE,IAAI,YAAY,GAAG,sBAAsB,GAAG,QAAQ,CAAC;IAErD,IAAI,OAAO,EAAE,CAAC;QACV,YAAY,IAAI,KAAY,CAAE,CAAC,KAAV,OAAO;IAChC,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;QACnB,YAAY,IAAI,KAAc,CAAE,CAAC,KAAZ,SAAS;IAClC,CAAC;IAED,OAAO,YAAY,CAAC;AACxB,CAAC;AAED,KAAK,UAAU,aAAa,CACxB,QAAmB,EACnB,KAAY,EACZ,SAA8G,EAC9G,UAAoE,EACpE,OAAoD,EACpD,SAAqB,EACrB,eAAiC,EACjC,IAAY,EACZ,aAA4B;;IAE5B,MAAM,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAE/C,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;QACvC,4CAA4C;QAC5C,MAAM,8EAA8E,CAAC;IACzF,CAAC;IAED,MAAM,aAAa,GAAG,CAAC,UAAU,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAEhG,IAAI,gBAAgB,GAAG,eAAe,GAChC,qBAAqB,CAAC,eAAe,EAAE,IAAI,CAAC,GAC5C,UAAU,GACR,sBAAsB,CAAC,QAAQ,CAAC,GAAG,CAAC,GACpC,qBAAqB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAEpD,IAAI,CAAC,gBAAgB,IAAI,aAAa,EAAE,CAAC;QACrC,IAAI,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YACpD,6CAA6C;YAC7C,MAAM,QAAQ,GAAG,gLAAM,cAAA,AAAW,EAAC,QAAQ,CAAC,GAAG,EAAE;gBAAE,MAAM,EAAE,MAAM;gBAAE,eAAe,EAAE;oBAAC,cAAc;iBAAC;YAAA,CAAE,CAAC,CAAC;YACxG,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACpF,IAAI,QAAQ,EAAE,CAAC;gBACX,kDAAkD;gBAClD,gBAAgB,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACtD,CAAC;QACL,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,gBAAgB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,CAAC;IACL,CAAC;IAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,6BAA4D,CAAE,CAAC,CAAC,6DAAnC,eAAe,GAAI,QAAQ,CAAC,GAAG;IAChF,CAAC;IAED,+BAAI,aAAa,mFAAE,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,4HAAE,OAAO,MAAK,KAAK,EAAE,CAAC;QACnE,MAAM,IAAI,KAAK,CAAC,QAAoC,OAA5B,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAA,wCAA8E,CAAC,CAAC;IACxI,CAAC;IAED,IAAI,QAAQ,CAAC,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QACjD,4CAA4C;QAC5C,MAAM,8FAA8F,CAAC;IACzG,CAAC;IAED,MAAM,iBAAiB,GAAG,CAAC,QAAgH,EAAE,EAAE;QAC3I,8GAA8G;QAC9G,qHAAqH;QACrH,mGAAmG;QACnG,IAAI,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YACrC,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC;YAC9C,MAAM,aAAa,GAAG,aAAa,CAAC,YAAY,2BAAC,aAAa,8BAAI,CAAA,CAAE,CAAC,CAAC;YACtE,IAAI,aAAa,YAAY,OAAO,EAAE,CAAC;gBACnC,0CAA0C;gBAC1C,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;oBACzC,OAAO,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBAClD,CAAC,CAAC,CAAC;gBACH,uFAAuF;gBACvF,8DAA8D;gBAC9D,OAAO,IAAI,CAAC;YAChB,CAAC,MAAM,CAAC;gBACJ,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACxB,OAAO,aAAa,CAAC;YACzB,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAClC,OAAO,gBAAgB,CAAC,MAAM,CAAC;QACnC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO,iBAAiB,CAAC,CAAC,MAAM,EAAE,EAAE;YA6ChC,MAAM;QA5CN,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,4CAA4C;YAC5C,MAAM,2CAA0D,OAAf,eAAe,EAAA,+FAAgG,CAAC;QACrK,CAAC;QAED,2BAA2B,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAEpD,4EAA4E;QAC5E,+EAA+E;QAC/E,IAAI,UAAU,IAAI,CAAC,AAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAI,iKAAC,kBAAA,AAAe,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACjH,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACpB,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACpD,IAAI,MAAM,YAAY,OAAO,EAAE,CAAC;oBAC5B,MAAM,AACF,0CAA0C;qBACzC,IAAI,CAAC,CAAC,IAAa,EAAE,EAAE;wBACpB,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBAC5B,CAAC,CAAC,AACF,0CAA0C;qBACzC,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;wBAClB,OAAO,CAAC,oCAAoC,GAAG,KAAK,EAAE,KAAK,CAAC,CAAC;oBACjE,CAAC,CAAC,CAAC;gBACX,CAAC,MAAM,CAAC;oBACJ,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBAC9B,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;YAClC,CAAC;YACD,OAAO;QACX,CAAC;QAED,MAAM,cAAc,GAAG,gBAAgB,CAAC,QAAQ,CAAC;QAEjD,MAAM,YAAY,GAAG,CAAC,IAAa,EAAE,WAAoB,EAAE,EAAE;YACzD,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBACnB,OAAO,CAAC,yBAAyB,CAAC,CAAC;gBACnC,OAAO;YACX,CAAC;YAED,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QACzC,CAAC,CAAC;QAEF,IAAI,OAAO,GAA2B,IAAI,CAAC;QAC3C,IAAI,cAAc,GAAG,KAAK,CAAC;8CACpB,mBAAmB,4FAAE,GAAG,CAAC,GAAG,EAAE;YACjC,cAAc,GAAG,IAAI,CAAC;YAEtB,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,CAAC,KAAK,EAAE,CAAC;gBAChB,OAAO,GAAG,IAAI,CAAC;YACnB,CAAC;YAED,SAAS,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,MAAM,eAAe,GAAG,GAAG,EAAE;YACzB,IAAI,cAAc,EAAE,CAAC;gBACjB,OAAO;YACX,CAAC;YAED,MAAM,aAAa,GAAG,CAAC,OAAoB,EAAE,SAAyB,EAAE,EAAE;gBACtE,OAAO,mDAAC,OAAO,CAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YAC5C,CAAC,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACvC,4CAA4C;gBAC5C,MAAM,kDAAkD,CAAC;YAC7D,CAAC;YAED,OAAO,GAAG,MAAM,CAAC,QAAQ,GACnB,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,IAAI,CAAC,GAC1J,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC;QACxH,CAAC,CAAC;QAEF,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACjC,IAAI,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,CAAC;QACvD,IAAI,oBAAoB,EAAE,CAAC;YACvB,4BAA4B;YAC5B,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,mCAAmC,CAAE,CAAC;gBAC5D,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC3B,cAAc,GAAG,IAAI,CAAC;oBACtB,MAAM;gBACV,CAAC;YACL,CAAC;YAED,oBAAoB,GAAG,CAAC,cAAc,CAAC;QAC3C,CAAC;QAED,IAAI,oBAAoB,wKAAI,iBAAc,CAAC,sBAAsB,EAAE,CAAC;YAChE,iGAAiG;YACjG,KAAK,CAAC,eAAe,uKAAG,iBAAc,CAAC,sBAAsB,CAAC,QAAQ,CAAC,GAAG,EAAE,eAAe,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAC9H,CAAC,MAAM,CAAC;YACJ,eAAe,EAAE,CAAC;QACtB,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,WAAW,CAAC,OAAe,EAAE,WAAwB;IAC1D,IAAI,GAAW,CAAC;IAChB,IAAI,IAAY,CAAC;IACjB,IAAI,IAAI,GAAmB,IAAI,CAAC;IAChC,IAAI,OAAO,GAA8B,IAAI,CAAC;IAE9C,IAAI,CAAC,WAAW,EAAE,CAAC;QACf,GAAG,GAAG,OAAO,CAAC;QACd,IAAI,2JAAG,QAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,OAAO,2JAAG,QAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC,MAAM,IAAI,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;QAC7B,GAAG,GAAG,QAAwB,CAAE,CAAC,KAAnB,WAAW,CAAC,IAAI;QAC9B,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;QACxB,IAAI,GAAG,WAAW,CAAC;IACvB,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;QACzC,GAAG,GAAG,EAAE,CAAC;QACT,IAAI,IAAG,uKAAA,AAAU,EAAE,CAAC;QACpB,OAAO,GAAG,WAAW,CAAC;IAC1B,CAAC,MAAM,IAAI,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;QACzC,GAAG,GAAG,WAAW,CAAC;QAClB,IAAI,GAAG,EAAE,CAAC;IACd,CAAC,MAAM,IAAI,OAAO,EAAE,CAAC;QACjB,MAAM,QAAQ,GAAG,WAAW,CAAC;QAC7B,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oKACnC,QAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,GAAG,GAAG,OAAO,GAAG,QAAQ,CAAC;QACzB,IAAI,GAAG,QAAQ,CAAC;IACpB,CAAC,MAAM,CAAC;QACJ,GAAG,GAAG,WAAW,CAAC;QAClB,IAAI,2JAAG,QAAK,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;QACtC,OAAO,2JAAG,QAAK,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IAC/C,CAAC;IAED,OAAO;QACH,GAAG,EAAE,GAAG;QACR,OAAO,EAAE,OAAO;QAChB,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,IAAI;QACV,OAAO;KACV,CAAC;AACN,CAAC;AAMK,SAAU,yBAAyB,CAAC,MAAgF;IACtH,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QACxC,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC;QACpC,iBAAiB,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG;YACzC,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,KAAK;SAClB,CAAC;IACN,CAAC,MAAM,CAAC;QACJ,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;QACrC,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrC,KAAK,MAAM,SAAS,IAAI,IAAI,CAAE,CAAC;YAC3B,iBAAiB,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,GAAG;gBACzC,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,QAAQ;gBACxC,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,QAAQ;aAC3C,CAAC;QACN,CAAC;IACL,CAAC;AACL,CAAC;AAOK,SAAU,yBAAyB,CAAC,MAAgF;IACtH,yBAAyB,CAAC,MAAM,CAAC,CAAC;AACtC,CAAC;AAMK,SAAU,sCAAsC;IAelD,OAAO,KAAK,CAAC,IAAI,CACb,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS;YAAE,CAAC,SAAS,EAAE,qBAAqB,CAAC,EAAE,EAAE;QACvF,IAAI,cAAc,GAAG,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,EAAE,AAAC,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;QAC5E,CAAC;QACD,cAAc,CAAC,IAAI,CAAC;YAAE,SAAS;YAAE,QAAQ,EAAE,qBAAqB,CAAC,QAAQ;YAAE,QAAQ,EAAE,qBAAqB,CAAC,QAAQ;QAAA,CAAE,CAAC,CAAC;QACvH,OAAO,SAAS,CAAC;IACrB,CAAC,EAAE,IAAI,GAAG,EAA4E,CAAC,CAC1F,CAAC,GAAG,CAAC;YAAC,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,CAAC;eAAC;YAAE,IAAI;YAAE,UAAU;QAAA,CAAE,CAAC,CAAC,CAAC;;AAC1D,CAAC;AASM,KAAK,UAAU,eAAe,CAAC,MAAmB,EAAE,KAAY,EAAE,OAA2B;IAChG,MAAM,EAAE,SAAS,EAAE,OAAO,GAAG,EAAE,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,EAAE,4CAAG,OAAO,GAAI,CAAA,CAAE,CAAC;IACpG,OAAO,MAAM,wBAAwB,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AAC/H,CAAC;AAED,KAAK,UAAU,eAAe,CAC1B,SAAwD,EACxD,OAAe;wBACf,iEAA6B,EAAE,UAC/B,kOAAyB,cAAW,CAAC,gBAAgB,EACrD,6EAAkD,IAAI,eACtD,iEAAmE,IAAI,YACvE,iEAA8E,IAAI,oBAClF,iEAAoC,IAAI,SACxC,IAAI,6DAAG,EAAE,kBACT,iEAA+B,CAAA,CAAE;IAEjC,IAAI,CAAC,KAAK,EAAE,CAAC;iKACT,SAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IACrD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,YAAY,GAAG,CAAA,CAAE,CAAC;IACxB,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAEnC,MAAM,cAAc,GAAG,GAAG,EAAE;QACxB,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;QACvD,MAAM,YAAY,GAAG,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QAEtE,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,4JAAI,eAAY,CAAC,YAAY,0JAAE,aAAU,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,CAAC;QACzG,CAAC,MAAM,CAAC;qKACJ,SAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3B,kCAAkC;QACtC,CAAC;QAED,cAAc,EAAE,CAAC;IACrB,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,UAAU,GAC5B,CAAC,KAAgC,EAAE,EAAE;QACjC,IAAI,CAAC;YACD,UAAU,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;YACT,YAAY,CAAC,gCAAgC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC,GACD,SAAS,CAAC;IAEhB,MAAM,cAAc,GAA+B,CAAC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,EAAE;QAC3J,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAE7C,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC;gBACD,SAAS,CAAC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;YACvH,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;gBACT,YAAY,CAAC,+BAA+B,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACzD,CAAC;QACL,CAAC;QAED,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAO,MAAM,aAAa,CACtB,QAAQ,EACR,KAAK,EACL,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE;QAC1B,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YACxB,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAC5E,CAAC;QAED,IAAK,MAA6B,CAAC,UAAU,EAAE,CAAC;YAC5C,MAAM,YAAY,GAAuB,MAAM,CAAC;YAChD,MAAM,MAAM,GAAmB,EAAE,CAAC;YAClC,MAAM,eAAe,GAAsB,EAAE,CAAC;YAC9C,MAAM,SAAS,GAAe,EAAE,CAAC;YAEjC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE,CAAC;gBACvH,OAAO;YACX,CAAC;YAED,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;YACtC,cAAc,CAAC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QAC3E,CAAC,MAAM,CAAC;YACJ,MAAM,aAAa,GAA4B,MAAM,CAAC;YACtD,aAAa,CACR,eAAe,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,CAAC,IAAI,CAAC,AAC1F,0CAA0C;aACzC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACb,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;gBACtC,cAAc,CACV,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,cAAc,EACrB,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,cAAc,CACxB,CAAC;YACN,CAAC,CACD,AADE,0CACwC;aACzC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACb,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACX,CAAC;IACL,CAAC,EACD,eAAe,EACf,YAAY,EACZ,cAAc,EACd,eAAe,EACf,IAAI,EACJ,aAAa,CAChB,CAAC;AACN,CAAC;AAED,KAAK,UAAU,wBAAwB,CACnC,SAAwD,EACxD,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,UAAiE,EACjE,eAAkC,EAClC,IAAa,EACb,aAA6B;IAE7B,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACzC,IAAI,CAAC;YACD,eAAe,CACX,SAAS,EACT,OAAO,EACP,aAAa,EACb,KAAK,EACL,CAAC,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,EAAE;gBACxG,OAAO,CAAC;oBACJ,MAAM,EAAE,MAAM;oBACd,eAAe,EAAE,eAAe;oBAChC,SAAS,EAAE,SAAS;oBACpB,eAAe,EAAE,eAAe;oBAChC,cAAc,EAAE,cAAc;oBAC9B,UAAU,EAAE,UAAU;oBACtB,MAAM,EAAE,MAAM;oBACd,cAAc,EAAE,cAAc;iBACjC,CAAC,CAAC;YACP,CAAC,EACD,UAAU,EACV,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;gBAC1B,2EAA2E;gBAC3E,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,eAAe,EACf,IAAI,EACJ,aAAa,EAEf,KAAK,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;YACb,2EAA2E;YAC3E,MAAM,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,gDAAgD;AAChD,KAAK,UAAU,kBAAkB,CAC7B,OAAe;wBACf,iEAA6B,EAAE,WAC/B,kOAAmC,cAAW,CAAC,iBAAiB,cAChE,iEAA8C,IAAI,eAClD,iEAAmE,IAAI,EACvE,2EAA8E,IAAI,oBAClF,iEAAoC,IAAI,SACxC,IAAI,6DAAG,EAAE,kBACT,iEAA+B,CAAA,CAAE;IAEjC,IAAI,CAAC,MAAM,EAAE,CAAC;gKACV,QAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACnC,OAAO;IACX,CAAC;IAED,MAAM,oBAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,oJAAI,QAAK,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AAChJ,CAAC;AASM,KAAK,UAAU,cAAc,CAAC,MAAmB,EAAE,MAAsB,EAAE,OAAqB;IACnG,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,EAAE,4CAAG,OAAO,GAAI,CAAA,CAAE,CAAC;IACzF,OAAO,MAAM,oBAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACjH,CAAC;AAUM,KAAK,UAAU,cAAc,CAAC,MAAmB,EAAE,MAAsB,EAAE,OAAqB;IACnG,OAAO,MAAM,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AACzD,CAAC;AAED,2GAA2G;AAC3G,KAAK,UAAU,oBAAoB,CAC/B,OAAe,EACf,aAA2B,EAC3B,MAAiC,EACjC,UAAiE,EACjE,eAAkC,EAClC,IAAa,EACb,aAA6B;IAE7B,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACzC,mEAAmE;QACnE,kBAAkB,CACd,OAAO,EACP,aAAa,EACb,MAAM,EACN,CAAC,KAAK,EAAE,EAAE;YACN,OAAO,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC,EACD,UAAU,EACV,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;YAC1B,2EAA2E;YAC3E,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5C,CAAC,EACD,eAAe,EACf,IAAI,EACJ,aAAa,CAChB,CAAC;IACN,CAAC,CAAC,CAAC;AACP,CAAC;AAED,kDAAkD;AAClD,KAAK,UAAU,oBAAoB,CAC/B,OAAe;wBACf,iEAA6B,EAAE,UAC/B,kOAAyB,cAAW,CAAC,gBAAgB,cACrD,iEAA8C,IAAI,eAClD,iEAAmE,IAAI,YACvE,iEAA8E,IAAI,oBAClF,iEAAoC,IAAI,SACxC,IAAI,6DAAG,EAAE,kBACT,iEAA+B,CAAA,CAAE;IAEjC,IAAI,CAAC,KAAK,EAAE,CAAC;iKACT,SAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAChD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IACrD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,YAAY,GAAG,CAAA,CAAE,CAAC;IACxB,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAEnC,MAAM,cAAc,GAAG,GAAG,EAAE;QACxB,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,0KAAI,mBAAgB,CAAC,iBAAiB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9D,oBAAoB,GAAG,IAAI,CAAC;QAC5B,KAAK,CAAC,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC;QACrC,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE;YACxB,KAAK,CAAC,SAAS,EAAE,CAAC,aAAa,EAAE,CAAC;YAClC,oBAAoB,GAAG,KAAK,CAAC;QACjC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,MAAM,YAAY,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;QACvD,MAAM,YAAY,GAAG,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QAEtE,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,4JAAI,eAAY,CAAC,YAAY,yJAAE,cAAU,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,CAAC;QACzG,CAAC,MAAM,CAAC;qKACJ,SAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3B,kCAAkC;QACtC,CAAC;QAED,cAAc,EAAE,CAAC;IACrB,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,UAAU,GAC5B,CAAC,KAAgC,EAAE,EAAE;QACjC,IAAI,CAAC;YACD,UAAU,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;YACT,YAAY,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;QACpD,CAAC;IACL,CAAC,GACD,SAAS,CAAC;IAEhB,MAAM,cAAc,GAAG,GAAG,EAAE;QACxB,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC;gBACD,SAAS,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;gBACT,YAAY,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;YACnD,CAAC;QACL,CAAC;QAED,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAO,MAAM,aAAa,CACtB,QAAQ,EACR,KAAK,EACL,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;QACb,IAAK,MAA6B,CAAC,IAAI,EAAE,CAAC;YACtC,MAAM,YAAY,GAAuB,MAAM,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC;gBAClE,OAAO;YACX,CAAC;YAED,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;YACtC,cAAc,EAAE,CAAC;QACrB,CAAC,MAAM,CAAC;YACJ,MAAM,aAAa,GAA4B,MAAM,CAAC;YACtD,aAAa,CACR,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,CAAC,IAAI,CACxE,AADyE,0CAC/B;aACzC,IAAI,CAAC,GAAG,EAAE;gBACP,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;gBACtC,cAAc,EAAE,CAAC;YACrB,CAAC,CAAC,AACF,0CAA0C;aACzC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACb,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACX,CAAC;IACL,CAAC,EACD,eAAe,EACf,YAAY,EACZ,cAAc,EACd,eAAe,EACf,IAAI,EACJ,aAAa,CAChB,CAAC;AACN,CAAC;AASM,KAAK,UAAU,gBAAgB,CAAC,MAAmB,EAAE,KAAY,EAAE,OAAuB;IAC7F,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,EAAE,4CAAG,OAAO,GAAI,CAAA,CAAE,CAAC;IACzF,MAAM,sBAAsB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AAC3G,CAAC;AAUM,KAAK,UAAU,gBAAgB,CAAC,MAAmB,EAAE,KAAY,EAAE,OAAuB;IAC7F,OAAO,MAAM,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1D,CAAC;AAED,+GAA+G;AAC/G,KAAK,UAAU,sBAAsB,CACjC,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,UAAiE,EACjE,eAAkC,EAClC,IAAa,EACb,aAA6B;IAE7B,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACzC,IAAI,CAAC;YACD,oBAAoB,CAChB,OAAO,EACP,aAAa,EACb,KAAK,EACL,CAAC,KAAK,EAAE,EAAE;gBACN,OAAO,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,EACD,UAAU,EACV,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;gBAC1B,2EAA2E;gBAC3E,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,eAAe,EACf,IAAI,EACJ,aAAa,EAEf,KAAK,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;YACb,2EAA2E;YAC3E,MAAM,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,0DAA0D;AAC1D,KAAK,UAAU,2BAA2B,CACtC,OAAe;wBACf,iEAA6B,EAAE,EAC/B,0OAAyB,cAAW,CAAC,gBAAgB,cACrD,iEAAwD,IAAI,eAC5D,iEAAmE,IAAI,YACvE,iEAA8E,IAAI,oBAClF,iEAAoC,IAAI,SACxC,IAAI,6DAAG,EAAE,kBACT,iEAA+B,CAAA,CAAE;IAEjC,IAAI,CAAC,KAAK,EAAE,CAAC;iKACT,SAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,QAAQ,GAAG,WAAW,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IACrD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,YAAY,GAAG,CAAA,CAAE,CAAC;IACxB,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IAEnC,MAAM,cAAc,GAAG,GAAG,EAAE;QACxB,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,OAAgB,EAAE,SAAe,EAAE,EAAE;QACvD,MAAM,YAAY,GAAG,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QAEtE,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,4JAAI,eAAY,CAAC,YAAY,0JAAE,aAAU,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,CAAC;QACzG,CAAC,MAAM,CAAC;YACJ,kKAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC3B,kCAAkC;QACtC,CAAC;QAED,cAAc,EAAE,CAAC;IACrB,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,UAAU,GAC5B,CAAC,KAAgC,EAAE,EAAE;QACjC,IAAI,CAAC;YACD,UAAU,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;YACT,YAAY,CAAC,8BAA8B,EAAE,CAAC,CAAC,CAAC;QACpD,CAAC;IACL,CAAC,GACD,SAAS,CAAC;IAEhB,MAAM,cAAc,GAAG,CAAC,MAAsB,EAAE,EAAE;QAC9C,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC;gBACD,SAAS,CAAC,MAAM,CAAC,CAAC;YACtB,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;gBACT,YAAY,CAAC,6BAA6B,EAAE,CAAC,CAAC,CAAC;YACnD,CAAC;QACL,CAAC;QAED,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAO,MAAM,aAAa,CACtB,QAAQ,EACR,KAAK,EACL,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;QACb,IAAK,MAA6B,CAAC,kBAAkB,EAAE,CAAC;YACpD,MAAM,YAAY,GAAuB,MAAM,CAAC;YAChD,MAAM,cAAc,GAAG,YAAY,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACpG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,OAAO;YACX,CAAC;YACD,cAAc,CAAC,iBAAiB,EAAE,CAAC;YACnC,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;YACtC,cAAc,CAAC,cAAc,CAAC,CAAC;QACnC,CAAC,MAAM,IAAK,MAAkC,CAAC,uBAAuB,EAAE,CAAC;YACrE,MAAM,aAAa,GAA4B,MAAM,CAAC;YACtD,aAAa,CACR,uBAAuB,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,CAAC,IAAI,CAAC,AACvF,0CAA0C;aACzC,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;gBACrB,cAAc,CAAC,iBAAiB,EAAE,CAAC;gBACnC,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC;gBACtC,cAAc,CAAC,cAAc,CAAC,CAAC;YACnC,CAAC,CACD,AADE,0CACwC;aACzC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACb,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACX,CAAC,MAAM,CAAC;YACJ,YAAY,CAAC,oIAAoI,CAAC,CAAC;QACvJ,CAAC;IACL,CAAC,EACD,eAAe,EACf,YAAY,EACZ,cAAc,EACd,eAAe,EACf,IAAI,EACJ,aAAa,CAChB,CAAC;AACN,CAAC;AASM,KAAK,UAAU,uBAAuB,CAAC,MAAmB,EAAE,KAAY,EAAE,OAAmC;IAChH,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,EAAE,4CAAG,OAAO,GAAI,CAAA,CAAE,CAAC;IACzF,OAAO,MAAM,6BAA6B,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACzH,CAAC;AAUM,KAAK,UAAU,uBAAuB,CAAC,MAAmB,EAAE,KAAY,EAAE,OAAmC;IAChH,OAAO,MAAM,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AACjE,CAAC;AAED,kIAAkI;AAClI,KAAK,UAAU,6BAA6B,CACxC,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,UAAiE,EACjE,eAAkC,EAClC,IAAa,EACb,aAA6B;IAE7B,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACzC,IAAI,CAAC;YACD,2BAA2B,CACvB,OAAO,EACP,aAAa,EACb,KAAK,EACL,CAAC,MAAM,EAAE,EAAE;gBACP,OAAO,CAAC,MAAM,CAAC,CAAC;YACpB,CAAC,EACD,UAAU,EACV,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;gBAC1B,2EAA2E;gBAC3E,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,eAAe,EACf,IAAI,EACJ,aAAa,EAEf,KAAK,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;YACb,2EAA2E;YAC3E,MAAM,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAED,uDAAuD;AACvD,KAAK,UAAU,yBAAyB,CACpC,OAAe;QACf,iFAA6B,EAAE,UAC/B,kOAAyB,cAAW,CAAC,gBAAgB,wBACrD,mBAAmB,8CAAG,IAAI,8BAC1B,yBAAyB,uBAAA,8CAAA,EAA6C,mBAA7C,qBACzB,iEAAkD,IAAI,cACtD,iEAA8C,IAAI,eAClD,iEAAmE,IAAI,EACvE,2EAA8E,IAAI,oBAClF,iEAAoC,IAAI,SACxC,IAAI,gEAAG,EAAE,kBACT,oEAA+B,CAAA,CAAE;IAEjC,IAAI,CAAC,KAAK,EAAE,CAAC;iKACT,SAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;QACzD,OAAO;IACX,CAAC;IAED,IAAI,mBAAmB,EAAE,CAAC;QACtB,iEAAiE;QACjE,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,WAAW,CAAE,CAAC;YACzC,UAAU,CAAC,KAAK,EAAE,CAAC;QACvB,CAAC;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,MAAM,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QACtD,KAAK,MAAM,cAAc,IAAI,eAAe,CAAE,CAAC;YAC3C,cAAc,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QACD,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,CAAE,CAAC;YACvB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YACzB,CAAC;QACL,CAAC;IACL,CAAC,MAAM,CAAC;QACJ,OAAQ,yBAAmC,EAAE,CAAC;YAC1C,KAAA,EAAA,8CAAA;gBACI,MAAM,eAAe,GAAG,KAAK,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;gBACtD,KAAK,MAAM,cAAc,IAAI,eAAe,CAAE,CAAC;oBAC3C,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC7B,CAAC;gBACD,MAAM;YACV,KAAA,EAAA,6CAAA;gBACI,KAAK,MAAM,cAAc,IAAI,KAAK,CAAC,eAAe,CAAE,CAAC;oBACjD,cAAc,CAAC,IAAI,EAAE,CAAC;gBAC1B,CAAC;gBACD,MAAM;YACV,KAAA,EAAA,6CAAA;gBACI,KAAK,MAAM,cAAc,IAAI,KAAK,CAAC,eAAe,CAAE,CAAC;oBACjD,cAAc,CAAC,KAAK,EAAE,CAAC;oBACvB,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC7B,CAAC;gBACD,MAAM;YACV,KAAA,EAAA,+CAAA;gBAEI,MAAM;YACV;wKACI,UAAM,CAAC,KAAK,CAAC,8CAA8C,GAAG,yBAAyB,GAAG,GAAG,CAAC,CAAC;gBAC/F,OAAO;QACf,CAAC;IACL,CAAC;IAED,MAAM,8BAA8B,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;IAEhE,MAAM,sBAAsB,GAAG,CAAC,SAAyB,EAAE,EAAE;QACzD,SAAS,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,8BAA8B,CAAC,EAAE,eAAe,CAAC,CAAC;QAE7G,SAAS,CAAC,OAAO,EAAE,CAAC;QAEpB,KAAK,CAAC,iCAAiC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAE/D,IAAI,SAAS,EAAE,CAAC;YACZ,SAAS,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,2BAA2B,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,sBAAsB,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACxJ,CAAC;AASM,KAAK,UAAU,qBAAqB,CAAC,MAAmB,EAAE,KAAY,EAAE,OAAiC;IAC5G,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,EAAE,4CAAG,OAAO,GAAI,CAAA,CAAE,CAAC;IAC1J,MAAM,2BAA2B,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;AACjL,CAAC;AAUM,KAAK,UAAU,qBAAqB,CAAC,MAAmB,EAAE,KAAY,EAAE,OAAiC;IAC5G,OAAO,MAAM,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC/D,CAAC;AAED,8HAA8H;AAC9H,KAAK,UAAU,2BAA2B,CACtC,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,mBAA6B,EAC7B,yBAAgE,EAChE,eAAgD,EAChD,UAAiE,EACjE,eAAkC,EAClC,IAAa,EACb,aAA6B;IAE7B,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACzC,IAAI,CAAC;YACD,yBAAyB,CACrB,OAAO,EACP,aAAa,EACb,KAAK,EACL,mBAAmB,EACnB,yBAAyB,EACzB,eAAe,EACf,CAAC,KAAK,EAAE,EAAE;gBACN,OAAO,CAAC,KAAK,CAAC,CAAC;YACnB,CAAC,EACD,UAAU,EACV,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE;gBAC1B,2EAA2E;gBAC3E,MAAM,CAAC,SAAS,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5C,CAAC,EACD,eAAe,EACf,IAAI,EACJ,aAAa,EAEf,KAAK,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC,CAAC,OAAO,KAAK,EAAE,CAAC;YACb,2EAA2E;YAC3E,MAAM,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC;IACL,CAAC,CAAC,CAAC;AACP,CAAC;AAQK,MAAO,WAAW;IAqBpB;;OAEG,CACI,MAAM,KAAK,mCAAmC,GAAA;QACjD,6KAAO,mBAAgB,CAAC,mCAAmC,CAAC;IAChE,CAAC;IAEM,MAAM,KAAK,mCAAmC,CAAC,KAAc,EAAA;8KAChE,mBAAgB,CAAC,mCAAmC,GAAG,KAAK,CAAC;IACjE,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,iBAAiB,GAAA;QAC/B,6KAAO,mBAAgB,CAAC,iBAAiB,CAAC;IAC9C,CAAC;IAEM,MAAM,KAAK,iBAAiB,CAAC,KAAc,EAAA;8KAC9C,mBAAgB,CAAC,iBAAiB,GAAG,KAAK,CAAC;IAC/C,CAAC;IAED;;;OAGG,CACH,gEAAgE;IACzD,MAAM,KAAK,YAAY,GAAA;QAC1B,6KAAO,mBAAgB,CAAC,YAAY,CAAC;IACzC,CAAC;IAED,gEAAgE;IACzD,MAAM,KAAK,YAAY,CAAC,KAAa,EAAA;8KACxC,mBAAgB,CAAC,YAAY,GAAG,KAAK,CAAC;IAC1C,CAAC;IAED;;OAEG,CACI,MAAM,KAAK,sBAAsB,GAAA;QACpC,6KAAO,mBAAgB,CAAC,sBAAsB,CAAC;IACnD,CAAC;IAEM,MAAM,KAAK,sBAAsB,CAAC,KAAc,EAAA;8KACnD,mBAAgB,CAAC,sBAAsB,GAAG,KAAK,CAAC;IACpD,CAAC;IASD;;;OAGG,CACI,MAAM,CAAC,gBAAgB,GAAA;QAC1B,OAAO,gBAAgB,EAAE,CAAC;IAC9B,CAAC;IAED,mBAAmB;IAEnB;;;;OAIG,CACI,MAAM,CAAC,qBAAqB,CAAC,SAAiB,EAAA;;QACjD,uDAA6B,SAAS,EAAE,IAAI,CAAC,2DAAtC,qBAAqB,EAAmB,MAAM,CAAC;IAC1D,CAAC;IAED;;;;OAIG,CACI,MAAM,CAAC,6BAA6B,CAAC,SAAiB,EAAA;QACzD,OAAO,6BAA6B,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;OAGG,CACI,MAAM,CAAC,cAAc,CAAC,MAAgF,EAAA;QACzG,yBAAyB,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;OAYG,CACI,MAAM,CAAC,UAAU,CACpB,SAAwD,EACxD,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,SAAgD,EAChD,UAAiE,EACjE,OAA4E,EAC5E,eAAkC,EAClC,IAAa,EAAA;QAEb,0CAA0C;QAC1C,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,iDAC7H,OAAO,EAAE,gKAAC,cAAW,CAAC,gBAAiB,gDAAE,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAClE,CAAC;IACN,CAAC;IAED;;;;;;;;;;;OAWG,CACI,MAAM,CAAC,KAAK,CAAC,eAAe,CAC/B,SAAwD,EACxD,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,UAAiE,EACjE,eAAkC,EAClC,IAAa,EAAA;QAEb,OAAO,MAAM,wBAAwB,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IACvH,CAAC;IAED;;;;;;;;;;;OAWG,CACI,MAAM,CAAC,IAAI,CACd,OAAe,EACf,aAA2B,EAC3B,MAAiC,EACjC,SAA4C,EAC5C,UAAiE,EACjE,OAA4E,EAC5E,eAAkC,EAClC,IAAa,EAAA;QAEb,0CAA0C;QAC1C,kBAAkB,CAAC,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,iDACtH,OAAO,EAAE,gKAAC,cAAW,CAAC,gBAAiB,gDAAE,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAClE,CAAC;IACN,CAAC;IAED;;;;;;;;;;OAUG,CACI,MAAM,CAAC,KAAK,CAAC,SAAS,CACzB,OAAe,EACf,aAA2B,EAC3B,MAAiC,EACjC,UAAiE,EACjE,eAAkC,EAClC,IAAa,EAAA;QAEb,OAAO,MAAM,oBAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IACzG,CAAC;IAED;;;;;;;;;;;OAWG,CACI,MAAM,CAAC,MAAM,CAChB,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,SAA4C,EAC5C,UAAiE,EACjE,OAA4E,EAC5E,eAAkC,EAClC,IAAa,EAAA;QAEb,0CAA0C;QAC1C,oBAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,iDACvH,OAAO,EAAE,CAAC,mCAAC,KAAK,oKAAI,cAAW,CAAC,gBAAgB,CAAE,+CAAE,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAC7E,CAAC;IACN,CAAC;IAED;;;;;;;;;;OAUG,CACI,MAAM,CAAC,KAAK,CAAC,WAAW,CAC3B,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,UAAiE,EACjE,eAAkC,EAClC,IAAa,EAAA;QAEb,OAAO,MAAM,sBAAsB,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAC1G,CAAC;IAED;;;;;;;;;;;OAWG,CACI,MAAM,CAAC,kBAAkB,CAC5B,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,SAAsD,EACtD,UAAiE,EACjE,OAA4E,EAC5E,eAAkC,EAClC,IAAa,EAAA;QAEb,0CAA0C;QAC1C,2BAA2B,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAC5H,CAD8H,MACvH,EAAE,CAAC,qFAAC,KAAK,oKAAI,cAAW,CAAC,gBAAgB,CAAE,+CAAE,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAC7E,CAAC;IACN,CAAC;IAED;;;;;;;;;;OAUG,CACI,MAAM,CAAC,KAAK,CAAC,uBAAuB,CACvC,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,UAAiE,EACjE,eAAkC,EAClC,IAAa,EAAA;QAEb,OAAO,MAAM,6BAA6B,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IACjH,CAAC;IAED;;;;;;;;;;;;;;OAcG,CACI,MAAM,CAAC,gBAAgB,CAC1B,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,mBAA6B,EAC7B,yBAAgE,EAChE,eAAgD,EAChD,SAA4C,EAC5C,UAAiE,EACjE,OAA4E,EAC5E,eAAkC,EAClC,IAAa,EAAA;QAEb,yBAAyB,CACrB,OAAO,EACP,aAAa,EACb,KAAK,EACL,mBAAmB,EACnB,yBAAyB,EACzB,eAAe,EACf,SAAS,EACT,UAAU,EACV,OAAO,EACP,eAAe,EACf,IAAI,EAEN,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,iDAAC,OAAO,EAAE,CAAC,mCAAC,KAAK,GAAI,+KAAW,CAAC,gBAAgB,CAAE,+CAAE,KAAK,CAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;IACnG,CAAC;IAED;;;;;;;;;;;;;;;OAeG,CACI,MAAM,CAAC,KAAK,CAAC,qBAAqB,CACrC,OAAe,EACf,aAA2B,EAC3B,KAAuB,EACvB,mBAA6B,EAC7B,yBAAgE,EAChE,eAAgD,EAChD,6DAA6D;IAC7D,SAA4C,EAC5C,UAAiE,EACjE,6DAA6D;IAC7D,OAA4E,EAC5E,eAAkC,EAClC,IAAa,EAAA;QAEb,OAAO,MAAM,2BAA2B,CAAC,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;IAChL,CAAC;;AAtYD;;GAEG,CACoB,YAAA,UAAU,GAAG,SAAS,CAAC,sBAAsB,CAAC;AAErE;;GAEG,CACoB,YAAA,eAAe,GAAG,SAAS,CAAC,2BAA2B,CAAC;AAE/E;;GAEG,CACoB,YAAA,eAAe,GAAG,SAAS,CAAC,2BAA2B,CAAC;AAE/E;;GAEG,CACoB,YAAA,gBAAgB,GAAG,SAAS,CAAC,4BAA4B,CAAC;AAiDjF,UAAU;AAEV;;GAEG,CACoB,YAAA,2BAA2B,GAAG,2BAA2B,CAAC", "debugId": null}}, {"offset": {"line": 1074, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Loading/loadingScreen.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Loading/loadingScreen.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { Observer } from \"../Misc/observable\";\r\n/**\r\n * Interface used to present a loading screen while loading a scene\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/customLoadingScreen\r\n */\r\nexport interface ILoadingScreen {\r\n    /**\r\n     * Function called to display the loading screen\r\n     */\r\n    displayLoadingUI: () => void;\r\n    /**\r\n     * Function called to hide the loading screen\r\n     */\r\n    hideLoadingUI: () => void;\r\n    /**\r\n     * Gets or sets the color to use for the background\r\n     */\r\n    loadingUIBackgroundColor: string;\r\n    /**\r\n     * Gets or sets the text to display while loading\r\n     */\r\n    loadingUIText: string;\r\n}\r\n\r\n/**\r\n * Class used for the default loading screen\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/customLoadingScreen\r\n */\r\nexport class DefaultLoadingScreen implements ILoadingScreen {\r\n    private _engine: Nullable<AbstractEngine>;\r\n    private _resizeObserver: Nullable<Observer<AbstractEngine>>;\r\n    private _isLoading: boolean;\r\n    /**\r\n     * Maps a loading `HTMLDivElement` to a tuple containing the associated `HTMLCanvasElement`\r\n     * and its `DOMRect` (or `null` if not yet available).\r\n     */\r\n    private _loadingDivToRenderingCanvasMap: Map<HTMLDivElement, [HTMLCanvasElement, DOMRect | null]> = new Map();\r\n    private _loadingTextDiv: Nullable<HTMLDivElement>;\r\n    private _style: Nullable<HTMLStyleElement>;\r\n\r\n    /** Gets or sets the logo url to use for the default loading screen */\r\n    public static DefaultLogoUrl = \"\";\r\n\r\n    /** Gets or sets the spinner url to use for the default loading screen */\r\n    public static DefaultSpinnerUrl = \"\";\r\n\r\n    /**\r\n     * Creates a new default loading screen\r\n     * @param _renderingCanvas defines the canvas used to render the scene\r\n     * @param _loadingText defines the default text to display\r\n     * @param _loadingDivBackgroundColor defines the default background color\r\n     */\r\n    constructor(\r\n        private _renderingCanvas: HTMLCanvasElement,\r\n        private _loadingText = \"\",\r\n        private _loadingDivBackgroundColor = \"black\"\r\n    ) {}\r\n\r\n    /**\r\n     * Function called to display the loading screen\r\n     */\r\n    public displayLoadingUI(): void {\r\n        if (this._isLoading) {\r\n            // Do not add a loading screen if it is already loading\r\n            return;\r\n        }\r\n\r\n        this._isLoading = true;\r\n        // get current engine by rendering canvas\r\n        this._engine = EngineStore.Instances.find((engine) => engine.getRenderingCanvas() === this._renderingCanvas) as AbstractEngine;\r\n\r\n        const loadingDiv = document.createElement(\"div\");\r\n\r\n        loadingDiv.id = \"babylonjsLoadingDiv\";\r\n        loadingDiv.style.opacity = \"0\";\r\n        loadingDiv.style.transition = \"opacity 1.5s ease\";\r\n        loadingDiv.style.pointerEvents = \"none\";\r\n        loadingDiv.style.display = \"grid\";\r\n        loadingDiv.style.gridTemplateRows = \"100%\";\r\n        loadingDiv.style.gridTemplateColumns = \"100%\";\r\n        loadingDiv.style.justifyItems = \"center\";\r\n        loadingDiv.style.alignItems = \"center\";\r\n\r\n        // Loading text\r\n        this._loadingTextDiv = document.createElement(\"div\");\r\n        this._loadingTextDiv.style.position = \"absolute\";\r\n        this._loadingTextDiv.style.left = \"0\";\r\n        this._loadingTextDiv.style.top = \"50%\";\r\n        this._loadingTextDiv.style.marginTop = \"80px\";\r\n        this._loadingTextDiv.style.width = \"100%\";\r\n        this._loadingTextDiv.style.height = \"20px\";\r\n        this._loadingTextDiv.style.fontFamily = \"Arial\";\r\n        this._loadingTextDiv.style.fontSize = \"14px\";\r\n        this._loadingTextDiv.style.color = \"white\";\r\n        this._loadingTextDiv.style.textAlign = \"center\";\r\n        this._loadingTextDiv.style.zIndex = \"1\";\r\n        this._loadingTextDiv.innerHTML = \"Loading\";\r\n\r\n        loadingDiv.appendChild(this._loadingTextDiv);\r\n\r\n        //set the predefined text\r\n        this._loadingTextDiv.innerHTML = this._loadingText;\r\n\r\n        // Generating keyframes\r\n        this._style = document.createElement(\"style\");\r\n        const keyFrames = `@-webkit-keyframes spin1 {\\\r\n                            0% { -webkit-transform: rotate(0deg);}\r\n                            100% { -webkit-transform: rotate(360deg);}\r\n                        }\\\r\n                        @keyframes spin1 {\\\r\n                            0% { transform: rotate(0deg);}\r\n                            100% { transform: rotate(360deg);}\r\n                        }`;\r\n        this._style.innerHTML = keyFrames;\r\n        document.getElementsByTagName(\"head\")[0].appendChild(this._style);\r\n\r\n        const svgSupport = !!window.SVGSVGElement;\r\n        // Loading img\r\n        const imgBack = new Image();\r\n        if (!DefaultLoadingScreen.DefaultLogoUrl) {\r\n            imgBack.src = !svgSupport\r\n                ? \"https://cdn.babylonjs.com/Assets/babylonLogo.png\"\r\n                : `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxODAuMTcgMjA4LjA0Ij48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2ZmZjt9LmNscy0ye2ZpbGw6I2UwNjg0Yjt9LmNscy0ze2ZpbGw6I2JiNDY0Yjt9LmNscy00e2ZpbGw6I2UwZGVkODt9LmNscy01e2ZpbGw6I2Q1ZDJjYTt9PC9zdHlsZT48L2RlZnM+PHRpdGxlPkJhYnlsb25Mb2dvPC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iUGFnZV9FbGVtZW50cyIgZGF0YS1uYW1lPSJQYWdlIEVsZW1lbnRzIj48cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik05MC4wOSwwLDAsNTJWMTU2bDkwLjA5LDUyLDkwLjA4LTUyVjUyWiIvPjxwb2x5Z29uIGNsYXNzPSJjbHMtMiIgcG9pbnRzPSIxODAuMTcgNTIuMDEgMTUxLjk3IDM1LjczIDEyNC44NSA1MS4zOSAxNTMuMDUgNjcuNjcgMTgwLjE3IDUyLjAxIi8+PHBvbHlnb24gY2xhc3M9ImNscy0yIiBwb2ludHM9IjI3LjEyIDY3LjY3IDExNy4yMSAxNS42NiA5MC4wOCAwIDAgNTIuMDEgMjcuMTIgNjcuNjciLz48cG9seWdvbiBjbGFzcz0iY2xzLTIiIHBvaW50cz0iNjEuODkgMTIwLjMgOTAuMDggMTM2LjU4IDExOC4yOCAxMjAuMyA5MC4wOCAxMDQuMDIgNjEuODkgMTIwLjMiLz48cG9seWdvbiBjbGFzcz0iY2xzLTMiIHBvaW50cz0iMTUzLjA1IDY3LjY3IDE1My4wNSAxNDAuMzcgOTAuMDggMTc2LjcyIDI3LjEyIDE0MC4zNyAyNy4xMiA2Ny42NyAwIDUyLjAxIDAgMTU2LjAzIDkwLjA4IDIwOC4wNCAxODAuMTcgMTU2LjAzIDE4MC4xNyA1Mi4wMSAxNTMuMDUgNjcuNjciLz48cG9seWdvbiBjbGFzcz0iY2xzLTMiIHBvaW50cz0iOTAuMDggNzEuNDYgNjEuODkgODcuNzQgNjEuODkgMTIwLjMgOTAuMDggMTA0LjAyIDExOC4yOCAxMjAuMyAxMTguMjggODcuNzQgOTAuMDggNzEuNDYiLz48cG9seWdvbiBjbGFzcz0iY2xzLTQiIHBvaW50cz0iMTUzLjA1IDY3LjY3IDExOC4yOCA4Ny43NCAxMTguMjggMTIwLjMgOTAuMDggMTM2LjU4IDkwLjA4IDE3Ni43MiAxNTMuMDUgMTQwLjM3IDE1My4wNSA2Ny42NyIvPjxwb2x5Z29uIGNsYXNzPSJjbHMtNSIgcG9pbnRzPSIyNy4xMiA2Ny42NyA2MS44OSA4Ny43NCA2MS44OSAxMjAuMyA5MC4wOCAxMzYuNTggOTAuMDggMTc2LjcyIDI3LjEyIDE0MC4zNyAyNy4xMiA2Ny42NyIvPjwvZz48L2c+PC9zdmc+`;\r\n        } else {\r\n            imgBack.src = DefaultLoadingScreen.DefaultLogoUrl;\r\n        }\r\n\r\n        imgBack.style.width = \"150px\";\r\n        imgBack.style.gridColumn = \"1\";\r\n        imgBack.style.gridRow = \"1\";\r\n        imgBack.style.top = \"50%\";\r\n        imgBack.style.left = \"50%\";\r\n        imgBack.style.transform = \"translate(-50%, -50%)\";\r\n        imgBack.style.position = \"absolute\";\r\n\r\n        const imageSpinnerContainer = document.createElement(\"div\");\r\n        imageSpinnerContainer.style.width = \"300px\";\r\n        imageSpinnerContainer.style.gridColumn = \"1\";\r\n        imageSpinnerContainer.style.gridRow = \"1\";\r\n        imageSpinnerContainer.style.top = \"50%\";\r\n        imageSpinnerContainer.style.left = \"50%\";\r\n        imageSpinnerContainer.style.transform = \"translate(-50%, -50%)\";\r\n        imageSpinnerContainer.style.position = \"absolute\";\r\n\r\n        // Loading spinner\r\n        const imgSpinner = new Image();\r\n\r\n        if (!DefaultLoadingScreen.DefaultSpinnerUrl) {\r\n            imgSpinner.src = !svgSupport\r\n                ? \"https://cdn.babylonjs.com/Assets/loadingIcon.png\"\r\n                : `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzOTIgMzkyIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2UwNjg0Yjt9LmNscy0ye2ZpbGw6bm9uZTt9PC9zdHlsZT48L2RlZnM+PHRpdGxlPlNwaW5uZXJJY29uPC90aXRsZT48ZyBpZD0iTGF5ZXJfMiIgZGF0YS1uYW1lPSJMYXllciAyIj48ZyBpZD0iU3Bpbm5lciI+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNNDAuMjEsMTI2LjQzYzMuNy03LjMxLDcuNjctMTQuNDQsMTItMjEuMzJsMy4zNi01LjEsMy41Mi01YzEuMjMtMS42MywyLjQxLTMuMjksMy42NS00LjkxczIuNTMtMy4yMSwzLjgyLTQuNzlBMTg1LjIsMTg1LjIsMCwwLDEsODMuNCw2Ny40M2EyMDgsMjA4LDAsMCwxLDE5LTE1LjY2YzMuMzUtMi40MSw2Ljc0LTQuNzgsMTAuMjUtN3M3LjExLTQuMjgsMTAuNzUtNi4zMmM3LjI5LTQsMTQuNzMtOCwyMi41My0xMS40OSwzLjktMS43Miw3Ljg4LTMuMywxMi00LjY0YTEwNC4yMiwxMDQuMjIsMCwwLDEsMTIuNDQtMy4yMyw2Mi40NCw2Mi40NCwwLDAsMSwxMi43OC0xLjM5QTI1LjkyLDI1LjkyLDAsMCwxLDE5NiwyMS40NGE2LjU1LDYuNTUsMCwwLDEsMi4wNSw5LDYuNjYsNi42NiwwLDAsMS0xLjY0LDEuNzhsLS40MS4yOWEyMi4wNywyMi4wNywwLDAsMS01Ljc4LDMsMzAuNDIsMzAuNDIsMCwwLDEtNS42NywxLjYyLDM3LjgyLDM3LjgyLDAsMCwxLTUuNjkuNzFjLTEsMC0xLjkuMTgtMi44NS4yNmwtMi44NS4yNHEtNS43Mi41MS0xMS40OCwxLjFjLTMuODQuNC03LjcxLjgyLTExLjU4LDEuNGExMTIuMzQsMTEyLjM0LDAsMCwwLTIyLjk0LDUuNjFjLTMuNzIsMS4zNS03LjM0LDMtMTAuOTQsNC42NHMtNy4xNCwzLjUxLTEwLjYsNS41MUExNTEuNiwxNTEuNiwwLDAsMCw2OC41Niw4N0M2Ny4yMyw4OC40OCw2Niw5MCw2NC42NCw5MS41NnMtMi41MSwzLjE1LTMuNzUsNC43M2wtMy41NCw0LjljLTEuMTMsMS42Ni0yLjIzLDMuMzUtMy4zMyw1YTEyNywxMjcsMCwwLDAtMTAuOTMsMjEuNDksMS41OCwxLjU4LDAsMSwxLTMtMS4xNVM0MC4xOSwxMjYuNDcsNDAuMjEsMTI2LjQzWiIvPjxyZWN0IGNsYXNzPSJjbHMtMiIgd2lkdGg9IjM5MiIgaGVpZ2h0PSIzOTIiLz48L2c+PC9nPjwvc3ZnPg==`;\r\n        } else {\r\n            imgSpinner.src = DefaultLoadingScreen.DefaultSpinnerUrl;\r\n        }\r\n\r\n        imgSpinner.style.animation = \"spin1 0.75s infinite linear\";\r\n        imgSpinner.style.transformOrigin = \"50% 50%\";\r\n\r\n        if (!svgSupport) {\r\n            const logoSize = { w: 16, h: 18.5 };\r\n            const loadingSize = { w: 30, h: 30 };\r\n            // set styling correctly\r\n            imgBack.style.width = `${logoSize.w}vh`;\r\n            imgBack.style.height = `${logoSize.h}vh`;\r\n            imgBack.style.left = `calc(50% - ${logoSize.w / 2}vh)`;\r\n            imgBack.style.top = `calc(50% - ${logoSize.h / 2}vh)`;\r\n\r\n            imgSpinner.style.width = `${loadingSize.w}vh`;\r\n            imgSpinner.style.height = `${loadingSize.h}vh`;\r\n            imgSpinner.style.left = `calc(50% - ${loadingSize.w / 2}vh)`;\r\n            imgSpinner.style.top = `calc(50% - ${loadingSize.h / 2}vh)`;\r\n        }\r\n\r\n        imageSpinnerContainer.appendChild(imgSpinner);\r\n\r\n        loadingDiv.appendChild(imgBack);\r\n        loadingDiv.appendChild(imageSpinnerContainer);\r\n        loadingDiv.style.backgroundColor = this._loadingDivBackgroundColor;\r\n        loadingDiv.style.opacity = \"1\";\r\n\r\n        const canvases: Array<HTMLCanvasElement> = [];\r\n        const views = this._engine.views;\r\n        if (views?.length) {\r\n            for (const view of views) {\r\n                if (view.enabled) {\r\n                    canvases.push(view.target);\r\n                }\r\n            }\r\n        } else {\r\n            canvases.push(this._renderingCanvas);\r\n        }\r\n        for (let i = 0; i < canvases.length; i++) {\r\n            const canvas = canvases[i];\r\n            const clonedLoadingDiv = loadingDiv.cloneNode(true) as HTMLDivElement;\r\n            clonedLoadingDiv.id += `-${i}`;\r\n            this._loadingDivToRenderingCanvasMap.set(clonedLoadingDiv, [canvas, null]);\r\n        }\r\n\r\n        this._resizeLoadingUI();\r\n\r\n        this._resizeObserver = this._engine.onResizeObservable.add(() => {\r\n            this._resizeLoadingUI();\r\n        });\r\n\r\n        this._loadingDivToRenderingCanvasMap.forEach((_, loadingDiv) => {\r\n            document.body.appendChild(loadingDiv);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Function called to hide the loading screen\r\n     */\r\n    public hideLoadingUI(): void {\r\n        if (!this._isLoading) {\r\n            return;\r\n        }\r\n\r\n        let completedTransitions = 0;\r\n\r\n        const onTransitionEnd = (event: TransitionEvent) => {\r\n            const loadingDiv = event.target as HTMLDivElement;\r\n            // ensure that ending transition event is generated by one of the current loadingDivs\r\n            const isTransitionEndOnLoadingDiv = this._loadingDivToRenderingCanvasMap.has(loadingDiv);\r\n\r\n            if (isTransitionEndOnLoadingDiv) {\r\n                completedTransitions++;\r\n                loadingDiv.remove();\r\n\r\n                const allTransitionsCompleted = completedTransitions === this._loadingDivToRenderingCanvasMap.size;\r\n                if (allTransitionsCompleted) {\r\n                    if (this._loadingTextDiv) {\r\n                        this._loadingTextDiv.remove();\r\n                        this._loadingTextDiv = null;\r\n                    }\r\n                    if (this._style) {\r\n                        this._style.remove();\r\n                        this._style = null;\r\n                    }\r\n\r\n                    window.removeEventListener(\"transitionend\", onTransitionEnd);\r\n                    this._engine!.onResizeObservable.remove(this._resizeObserver);\r\n                    this._loadingDivToRenderingCanvasMap.clear();\r\n                    this._engine = null;\r\n                    this._isLoading = false;\r\n                }\r\n            }\r\n        };\r\n\r\n        this._loadingDivToRenderingCanvasMap.forEach((_, loadingDiv) => {\r\n            loadingDiv.style.opacity = \"0\";\r\n        });\r\n\r\n        window.addEventListener(\"transitionend\", onTransitionEnd);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the text to display while loading\r\n     */\r\n    public set loadingUIText(text: string) {\r\n        this._loadingText = text;\r\n\r\n        if (this._loadingTextDiv) {\r\n            this._loadingDivToRenderingCanvasMap.forEach((_, loadingDiv) => {\r\n                // set loadingTextDiv of current loadingDiv\r\n                loadingDiv.children[0].innerHTML = this._loadingText;\r\n            });\r\n        }\r\n    }\r\n\r\n    public get loadingUIText(): string {\r\n        return this._loadingText;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the color to use for the background\r\n     */\r\n    public get loadingUIBackgroundColor(): string {\r\n        return this._loadingDivBackgroundColor;\r\n    }\r\n\r\n    public set loadingUIBackgroundColor(color: string) {\r\n        this._loadingDivBackgroundColor = color;\r\n\r\n        if (!this._isLoading) {\r\n            return;\r\n        }\r\n\r\n        this._loadingDivToRenderingCanvasMap.forEach((_, loadingDiv) => {\r\n            loadingDiv.style.backgroundColor = this._loadingDivBackgroundColor;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Checks if the layout of the canvas has changed by comparing the current layout\r\n     * rectangle with the previous one.\r\n     *\r\n     * This function compares of the two `DOMRect` objects to determine if any of the layout dimensions have changed.\r\n     * If the layout has changed or if there is no previous layout (i.e., `previousCanvasRect` is `null`),\r\n     * it returns `true`. Otherwise, it returns `false`.\r\n     *\r\n     * @param previousCanvasRect defines the previously recorded `DOMRect` of the canvas, or `null` if no previous state exists.\r\n     * @param currentCanvasRect defines the current `DOMRect` of the canvas to compare against the previous layout.\r\n     * @returns `true` if the layout has changed, otherwise `false`.\r\n     */\r\n    private _isCanvasLayoutChanged(previousCanvasRect: DOMRect | null, currentCanvasRect: DOMRect) {\r\n        return (\r\n            !previousCanvasRect ||\r\n            previousCanvasRect.left !== currentCanvasRect.left ||\r\n            previousCanvasRect.top !== currentCanvasRect.top ||\r\n            previousCanvasRect.right !== currentCanvasRect.right ||\r\n            previousCanvasRect.bottom !== currentCanvasRect.bottom ||\r\n            previousCanvasRect.width !== currentCanvasRect.width ||\r\n            previousCanvasRect.height !== currentCanvasRect.height ||\r\n            previousCanvasRect.x !== currentCanvasRect.x ||\r\n            previousCanvasRect.y !== currentCanvasRect.y\r\n        );\r\n    }\r\n\r\n    // Resize\r\n    private _resizeLoadingUI = () => {\r\n        if (!this._isLoading) {\r\n            return;\r\n        }\r\n\r\n        this._loadingDivToRenderingCanvasMap.forEach(([canvas, previousCanvasRect], loadingDiv) => {\r\n            const currentCanvasRect = canvas.getBoundingClientRect();\r\n            if (this._isCanvasLayoutChanged(previousCanvasRect, currentCanvasRect)) {\r\n                const canvasPositioning = window.getComputedStyle(canvas).position;\r\n\r\n                loadingDiv.style.position = canvasPositioning === \"fixed\" ? \"fixed\" : \"absolute\";\r\n                loadingDiv.style.left = currentCanvasRect.left + window.scrollX + \"px\";\r\n                loadingDiv.style.top = currentCanvasRect.top + window.scrollY + \"px\";\r\n                loadingDiv.style.width = currentCanvasRect.width + \"px\";\r\n                loadingDiv.style.height = currentCanvasRect.height + \"px\";\r\n\r\n                this._loadingDivToRenderingCanvasMap.set(loadingDiv, [canvas, currentCanvasRect]);\r\n            }\r\n        });\r\n    };\r\n}\r\n\r\nAbstractEngine.DefaultLoadingScreenFactory = (canvas: HTMLCanvasElement) => {\r\n    return new DefaultLoadingScreen(canvas);\r\n};\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;;;AA6B/C,MAAO,oBAAoB;IA8B7B;;OAEG,CACI,gBAAgB,GAAA;QACnB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,uDAAuD;YACvD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,yCAAyC;QACzC,IAAI,CAAC,OAAO,oKAAG,cAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAG,CAAD,KAAO,CAAC,kBAAkB,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAmB,CAAC;QAE/H,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAEjD,UAAU,CAAC,EAAE,GAAG,qBAAqB,CAAC;QACtC,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAC/B,UAAU,CAAC,KAAK,CAAC,UAAU,GAAG,mBAAmB,CAAC;QAClD,UAAU,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;QACxC,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QAClC,UAAU,CAAC,KAAK,CAAC,gBAAgB,GAAG,MAAM,CAAC;QAC3C,UAAU,CAAC,KAAK,CAAC,mBAAmB,GAAG,MAAM,CAAC;QAC9C,UAAU,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC;QACzC,UAAU,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;QAEvC,eAAe;QACf,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;QACjD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC;QACtC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;QAC9C,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;QAC1C,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QAC3C,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;QAC7C,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;QAC3C,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3C,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE7C,yBAAyB;QACzB,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;QAEnD,uBAAuB;QACvB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,MAAM,SAAS,GAAG;QAQlB,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;QAClC,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAElE,MAAM,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;QAC1C,cAAc;QACd,MAAM,OAAO,GAAG,IAAI,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,GAAG,CAAC,UAAU,GACnB,kDAAkD,GAClD,2lDAA4lD,CAAC;QACvmD,CAAC,MAAM,CAAC;YACJ,OAAO,CAAC,GAAG,GAAG,oBAAoB,CAAC,cAAc,CAAC;QACtD,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;QAC9B,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;QAC/B,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAC5B,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;QAC3B,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,uBAAuB,CAAC;QAClD,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;QAEpC,MAAM,qBAAqB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC5D,qBAAqB,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;QAC5C,qBAAqB,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;QAC7C,qBAAqB,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAC1C,qBAAqB,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;QACxC,qBAAqB,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;QACzC,qBAAqB,CAAC,KAAK,CAAC,SAAS,GAAG,uBAAuB,CAAC;QAChE,qBAAqB,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;QAElD,kBAAkB;QAClB,MAAM,UAAU,GAAG,IAAI,KAAK,EAAE,CAAC;QAE/B,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,EAAE,CAAC;YAC1C,UAAU,CAAC,GAAG,GAAG,CAAC,UAAU,GACtB,kDAAkD,GAClD,2/CAA4/C,CAAC;QACvgD,CAAC,MAAM,CAAC;YACJ,UAAU,CAAC,GAAG,GAAG,oBAAoB,CAAC,iBAAiB,CAAC;QAC5D,CAAC;QAED,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG,6BAA6B,CAAC;QAC3D,UAAU,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;QAE7C,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,QAAQ,GAAG;gBAAE,CAAC,EAAE,EAAE;gBAAE,CAAC,EAAE,IAAI;YAAA,CAAE,CAAC;YACpC,MAAM,WAAW,GAAG;gBAAE,CAAC,EAAE,EAAE;gBAAE,CAAC,EAAE,EAAE;YAAA,CAAE,CAAC;YACrC,wBAAwB;YACxB,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,GAAa,OAAV,QAAQ,CAAC,CAAC,EAAA,GAAI,CAAC;YACxC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAa,GAAI,CAAC,GAAf,QAAQ,CAAC,CAAC,EAAA;YACpC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,cAA4B,OAAd,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAA,IAAK,CAAC;YACvD,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,cAA4B,OAAd,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAA,IAAK,CAAC;YAEtD,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,GAAgB,OAAb,WAAW,CAAC,CAAC,EAAA,GAAI,CAAC;YAC9C,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,GAAgB,GAAI,CAAC,GAAlB,WAAW,CAAC,CAAC,EAAA;YAC1C,UAAU,CAAC,KAAK,CAAC,IAAI,GAAG,cAA+B,OAAjB,WAAW,CAAC,CAAC,GAAG,CAAC,EAAA,IAAK,CAAC;YAC7D,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,cAA+B,OAAjB,WAAW,CAAC,CAAC,GAAG,CAAC,EAAA,IAAK,CAAC;QAChE,CAAC;QAED,qBAAqB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAE9C,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAChC,UAAU,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;QAC9C,UAAU,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACnE,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAE/B,MAAM,QAAQ,GAA6B,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QACjC,kDAAI,KAAK,CAAE,MAAM,EAAE,CAAC;YAChB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAE,CAAC;gBACvB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACf,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/B,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzC,CAAC;QACD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACvC,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC3B,MAAM,gBAAgB,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAmB,CAAC;YACtE,gBAAgB,CAAC,EAAE,IAAI,IAAK,CAAE,CAAC,KAAJ,CAAC;YAC5B,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,gBAAgB,EAAE;gBAAC,MAAM;gBAAE,IAAI;aAAC,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC5D,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE;YAC3D,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG,CACI,aAAa,GAAA;QAChB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAE7B,MAAM,eAAe,GAAG,CAAC,KAAsB,EAAE,EAAE;YAC/C,MAAM,UAAU,GAAG,KAAK,CAAC,MAAwB,CAAC;YAClD,qFAAqF;YACrF,MAAM,2BAA2B,GAAG,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEzF,IAAI,2BAA2B,EAAE,CAAC;gBAC9B,oBAAoB,EAAE,CAAC;gBACvB,UAAU,CAAC,MAAM,EAAE,CAAC;gBAEpB,MAAM,uBAAuB,GAAG,oBAAoB,KAAK,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC;gBACnG,IAAI,uBAAuB,EAAE,CAAC;oBAC1B,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;wBACvB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;wBAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAChC,CAAC;oBACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;wBACd,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;wBACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACvB,CAAC;oBAED,MAAM,CAAC,mBAAmB,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;oBAC7D,IAAI,CAAC,OAAQ,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC9D,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;oBAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACpB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBAC5B,CAAC;YACL,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE;YAC3D,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,gBAAgB,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG,CACH,IAAW,aAAa,CAAC,IAAY,EAAA;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE;gBAC3D,2CAA2C;gBAC3C,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;YACzD,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG,CACH,IAAW,wBAAwB,GAAA;QAC/B,OAAO,IAAI,CAAC,0BAA0B,CAAC;IAC3C,CAAC;IAED,IAAW,wBAAwB,CAAC,KAAa,EAAA;QAC7C,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;QAExC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE;YAC3D,UAAU,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,0BAA0B,CAAC;QACvE,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;;;;;;OAWG,CACK,sBAAsB,CAAC,kBAAkC,EAAE,iBAA0B,EAAA;QACzF,OAAO,AACH,CAAC,kBAAkB,IACnB,kBAAkB,CAAC,IAAI,KAAK,iBAAiB,CAAC,IAAI,IAClD,kBAAkB,CAAC,GAAG,KAAK,iBAAiB,CAAC,GAAG,IAChD,kBAAkB,CAAC,KAAK,KAAK,iBAAiB,CAAC,KAAK,IACpD,kBAAkB,CAAC,MAAM,KAAK,iBAAiB,CAAC,MAAM,IACtD,kBAAkB,CAAC,KAAK,KAAK,iBAAiB,CAAC,KAAK,IACpD,kBAAkB,CAAC,MAAM,KAAK,iBAAiB,CAAC,MAAM,IACtD,kBAAkB,CAAC,CAAC,KAAK,iBAAiB,CAAC,CAAC,IAC5C,kBAAkB,CAAC,CAAC,KAAK,iBAAiB,CAAC,CAAC,CAC/C,CAAC;IACN,CAAC;IA9QD;;;;;OAKG,CACH,YACY,gBAAmC,EACnC,eAAe,EAAE,EACjB,6BAA6B,OAAO,CAAA;QAFpC,IAAA,CAAA,gBAAgB,GAAhB,gBAAgB,CAAmB;QACnC,IAAA,CAAA,YAAY,GAAZ,YAAY,CAAK;QACjB,IAAA,CAAA,0BAA0B,GAA1B,0BAA0B,CAAU;QAvBhD;;;WAGG,CACK,IAAA,CAAA,+BAA+B,GAA6D,IAAI,GAAG,EAAE,CAAC;QA0R9G,SAAS;QACD,IAAA,CAAA,gBAAgB,GAAG,GAAG,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACnB,OAAO;YACX,CAAC;YAED,IAAI,CAAC,+BAA+B,CAAC,OAAO,CAAC,QAA+B,UAAU,EAAE,EAAE;oBAA5C,CAAC,MAAM,EAAE,kBAAkB,CAAC;gBACtE,MAAM,iBAAiB,GAAG,MAAM,CAAC,qBAAqB,EAAE,CAAC;gBACzD,IAAI,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,EAAE,iBAAiB,CAAC,EAAE,CAAC;oBACrE,MAAM,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC;oBAEnE,UAAU,CAAC,KAAK,CAAC,QAAQ,GAAG,iBAAiB,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;oBACjF,UAAU,CAAC,KAAK,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;oBACvE,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,iBAAiB,CAAC,GAAG,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;oBACrE,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,GAAG,IAAI,CAAC;oBACxD,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC;oBAE1D,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,UAAU,EAAE;wBAAC,MAAM;wBAAE,iBAAiB;qBAAC,CAAC,CAAC;gBACtF,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;IA1RC,CAAC;;AAhBJ,oEAAA,EAAsE,CACxD,qBAAA,cAAc,GAAG,EAAE,AAAL,CAAM;AAElC,uEAAA,EAAyE,CAC3D,qBAAA,iBAAiB,GAAG,EAAE,AAAL,CAAM;oKAySzC,iBAAc,CAAC,2BAA2B,GAAG,CAAC,MAAyB,EAAE,EAAE;IACvE,OAAO,IAAI,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Loading/Plugins/babylonFileLoader.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Loading/Plugins/babylonFileLoader.ts"], "sourcesContent": ["import { Logger } from \"../../Misc/logger\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Camera } from \"../../Cameras/camera\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Vector3 } from \"../../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../../Maths/math.color\";\r\nimport { Mesh } from \"../../Meshes/mesh\";\r\nimport type { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { Geometry } from \"../../Meshes/geometry\";\r\nimport type { Node } from \"../../node\";\r\nimport { TransformNode } from \"../../Meshes/transformNode\";\r\nimport { Material } from \"../../Materials/material\";\r\nimport { MultiMaterial } from \"../../Materials/multiMaterial\";\r\nimport { CubeTexture } from \"../../Materials/Textures/cubeTexture\";\r\nimport { HDRCubeTexture } from \"../../Materials/Textures/hdrCubeTexture\";\r\nimport { AnimationGroup } from \"../../Animations/animationGroup\";\r\nimport { Light } from \"../../Lights/light\";\r\nimport { SceneComponentConstants } from \"../../sceneComponent\";\r\nimport { SceneLoader } from \"../../Loading/sceneLoader\";\r\nimport { AssetContainer } from \"../../assetContainer\";\r\nimport { ActionManager } from \"../../Actions/actionManager\";\r\nimport type { IParticleSystem } from \"../../Particles/IParticleSystem\";\r\nimport { Skeleton } from \"../../Bones/skeleton\";\r\nimport { MorphTargetManager } from \"../../Morph/morphTargetManager\";\r\nimport { CannonJSPlugin } from \"../../Physics/v1/Plugins/cannonJSPlugin\";\r\nimport { OimoJSPlugin } from \"../../Physics/v1/Plugins/oimoJSPlugin\";\r\nimport { AmmoJSPlugin } from \"../../Physics/v1/Plugins/ammoJSPlugin\";\r\nimport { ReflectionProbe } from \"../../Probes/reflectionProbe\";\r\nimport { GetClass } from \"../../Misc/typeStore\";\r\nimport { Tools } from \"../../Misc/tools\";\r\nimport { PostProcess } from \"../../PostProcesses/postProcess\";\r\nimport { SpriteManager } from \"core/Sprites/spriteManager\";\r\nimport { GetIndividualParser, Parse } from \"./babylonFileParser.function\";\r\n\r\n/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention, no-var\r\nexport var _BabylonLoaderRegistered = true;\r\n\r\n/**\r\n * Helps setting up some configuration for the babylon file loader.\r\n */\r\nexport class BabylonFileLoaderConfiguration {\r\n    /**\r\n     * The loader does not allow injecting custom physics engine into the plugins.\r\n     * Unfortunately in ES6, we need to manually inject them into the plugin.\r\n     * So you could set this variable to your engine import to make it work.\r\n     */\r\n    public static LoaderInjectedPhysicsEngine: any = undefined;\r\n}\r\n\r\nlet TempIndexContainer: { [key: string]: Node } = {};\r\nlet TempMaterialIndexContainer: { [key: string]: Material } = {};\r\nlet TempMorphTargetManagerIndexContainer: { [key: string]: MorphTargetManager } = {};\r\n\r\nconst ParseMaterialByPredicate = (predicate: (parsedMaterial: any) => boolean, parsedData: any, scene: Scene, rootUrl: string) => {\r\n    if (!parsedData.materials) {\r\n        return null;\r\n    }\r\n\r\n    for (let index = 0, cache = parsedData.materials.length; index < cache; index++) {\r\n        const parsedMaterial = parsedData.materials[index];\r\n        if (predicate(parsedMaterial)) {\r\n            return { parsedMaterial, material: Material.Parse(parsedMaterial, scene, rootUrl) };\r\n        }\r\n    }\r\n    return null;\r\n};\r\n\r\nconst IsDescendantOf = (mesh: any, names: Array<any>, hierarchyIds: Array<number>) => {\r\n    for (const i in names) {\r\n        if (mesh.name === names[i]) {\r\n            hierarchyIds.push(mesh.id);\r\n            return true;\r\n        }\r\n    }\r\n    if (mesh.parentId !== undefined && hierarchyIds.indexOf(mesh.parentId) !== -1) {\r\n        hierarchyIds.push(mesh.id);\r\n        return true;\r\n    }\r\n    return false;\r\n};\r\n\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nconst logOperation = (operation: string, producer: { file: string; name: string; version: string; exporter_version: string }) => {\r\n    return (\r\n        operation +\r\n        \" of \" +\r\n        (producer ? producer.file + \" from \" + producer.name + \" version: \" + producer.version + \", exporter version: \" + producer.exporter_version : \"unknown\")\r\n    );\r\n};\r\n\r\nconst LoadDetailLevels = (scene: Scene, mesh: AbstractMesh) => {\r\n    const mastermesh: Mesh = mesh as Mesh;\r\n\r\n    // Every value specified in the ids array of the lod data points to another mesh which should be used as the lower LOD level.\r\n    // The distances (or coverages) array values specified are used along with the lod mesh ids as a hint to determine the switching threshold for the various LODs.\r\n    if (mesh._waitingData.lods) {\r\n        if (mesh._waitingData.lods.ids && mesh._waitingData.lods.ids.length > 0) {\r\n            const lodmeshes: string[] = mesh._waitingData.lods.ids;\r\n            const wasenabled: boolean = mastermesh.isEnabled(false);\r\n            if (mesh._waitingData.lods.distances) {\r\n                const distances: number[] = mesh._waitingData.lods.distances;\r\n                if (distances.length >= lodmeshes.length) {\r\n                    const culling: number = distances.length > lodmeshes.length ? distances[distances.length - 1] : 0;\r\n                    mastermesh.setEnabled(false);\r\n                    for (let index = 0; index < lodmeshes.length; index++) {\r\n                        const lodid: string = lodmeshes[index];\r\n                        const lodmesh: Mesh = scene.getMeshById(lodid) as Mesh;\r\n                        if (lodmesh != null) {\r\n                            mastermesh.addLODLevel(distances[index], lodmesh);\r\n                        }\r\n                    }\r\n                    if (culling > 0) {\r\n                        mastermesh.addLODLevel(culling, null);\r\n                    }\r\n                    if (wasenabled === true) {\r\n                        mastermesh.setEnabled(true);\r\n                    }\r\n                } else {\r\n                    Tools.Warn(\"Invalid level of detail distances for \" + mesh.name);\r\n                }\r\n            }\r\n        }\r\n        mesh._waitingData.lods = null;\r\n    }\r\n};\r\n\r\nconst FindParent = (parentId: any, parentInstanceIndex: any, scene: Scene) => {\r\n    if (typeof parentId !== \"number\") {\r\n        const parentEntry = scene.getLastEntryById(parentId);\r\n        if (parentEntry && parentInstanceIndex !== undefined && parentInstanceIndex !== null) {\r\n            const instance = (parentEntry as Mesh).instances[parseInt(parentInstanceIndex)];\r\n            return instance;\r\n        }\r\n        return parentEntry;\r\n    }\r\n\r\n    const parent = TempIndexContainer[parentId];\r\n    if (parent && parentInstanceIndex !== undefined && parentInstanceIndex !== null) {\r\n        const instance = (parent as Mesh).instances[parseInt(parentInstanceIndex)];\r\n        return instance;\r\n    }\r\n\r\n    return parent;\r\n};\r\n\r\nconst FindMaterial = (materialId: any, scene: Scene) => {\r\n    if (typeof materialId !== \"number\") {\r\n        return scene.getLastMaterialById(materialId, true);\r\n    }\r\n\r\n    return TempMaterialIndexContainer[materialId];\r\n};\r\n\r\nconst LoadAssetContainer = (scene: Scene, data: string, rootUrl: string, onError?: (message: string, exception?: any) => void, addToScene = false): AssetContainer => {\r\n    const container = new AssetContainer(scene);\r\n\r\n    // Entire method running in try block, so ALWAYS logs as far as it got, only actually writes details\r\n    // when SceneLoader.debugLogging = true (default), or exception encountered.\r\n    // Everything stored in var log instead of writing separate lines to support only writing in exception,\r\n    // and avoid problems with multiple concurrent .babylon loads.\r\n    let log = \"importScene has failed JSON parse\";\r\n    try {\r\n        // eslint-disable-next-line no-var\r\n        var parsedData = JSON.parse(data);\r\n        log = \"\";\r\n        const fullDetails = SceneLoader.loggingLevel === SceneLoader.DETAILED_LOGGING;\r\n\r\n        let index: number;\r\n        let cache: number;\r\n\r\n        // Environment texture\r\n        if (parsedData.environmentTexture !== undefined && parsedData.environmentTexture !== null) {\r\n            // PBR needed for both HDR texture (gamma space) & a sky box\r\n            const isPBR = parsedData.isPBR !== undefined ? parsedData.isPBR : true;\r\n            if (parsedData.environmentTextureType && parsedData.environmentTextureType === \"BABYLON.HDRCubeTexture\") {\r\n                const hdrSize: number = parsedData.environmentTextureSize ? parsedData.environmentTextureSize : 128;\r\n                const hdrTexture = new HDRCubeTexture(\r\n                    (parsedData.environmentTexture.match(/https?:\\/\\//g) ? \"\" : rootUrl) + parsedData.environmentTexture,\r\n                    scene,\r\n                    hdrSize,\r\n                    true,\r\n                    !isPBR,\r\n                    undefined,\r\n                    parsedData.environmentTexturePrefilterOnLoad\r\n                );\r\n                if (parsedData.environmentTextureRotationY) {\r\n                    hdrTexture.rotationY = parsedData.environmentTextureRotationY;\r\n                }\r\n                scene.environmentTexture = hdrTexture;\r\n            } else {\r\n                if (typeof parsedData.environmentTexture === \"object\") {\r\n                    const environmentTexture = CubeTexture.Parse(parsedData.environmentTexture, scene, rootUrl);\r\n                    scene.environmentTexture = environmentTexture;\r\n                } else if ((parsedData.environmentTexture as string).endsWith(\".env\")) {\r\n                    const compressedTexture = new CubeTexture(\r\n                        (parsedData.environmentTexture.match(/https?:\\/\\//g) ? \"\" : rootUrl) + parsedData.environmentTexture,\r\n                        scene,\r\n                        parsedData.environmentTextureForcedExtension\r\n                    );\r\n                    if (parsedData.environmentTextureRotationY) {\r\n                        compressedTexture.rotationY = parsedData.environmentTextureRotationY;\r\n                    }\r\n                    scene.environmentTexture = compressedTexture;\r\n                } else {\r\n                    const cubeTexture = CubeTexture.CreateFromPrefilteredData(\r\n                        (parsedData.environmentTexture.match(/https?:\\/\\//g) ? \"\" : rootUrl) + parsedData.environmentTexture,\r\n                        scene,\r\n                        parsedData.environmentTextureForcedExtension\r\n                    );\r\n                    if (parsedData.environmentTextureRotationY) {\r\n                        cubeTexture.rotationY = parsedData.environmentTextureRotationY;\r\n                    }\r\n                    scene.environmentTexture = cubeTexture;\r\n                }\r\n            }\r\n            if (parsedData.createDefaultSkybox === true) {\r\n                const skyboxScale = scene.activeCamera !== undefined && scene.activeCamera !== null ? (scene.activeCamera.maxZ - scene.activeCamera.minZ) / 2 : 1000;\r\n                const skyboxBlurLevel = parsedData.skyboxBlurLevel || 0;\r\n                scene.createDefaultSkybox(scene.environmentTexture, isPBR, skyboxScale, skyboxBlurLevel);\r\n            }\r\n            container.environmentTexture = scene.environmentTexture;\r\n        }\r\n\r\n        // Environment Intensity\r\n        if (parsedData.environmentIntensity !== undefined && parsedData.environmentIntensity !== null) {\r\n            scene.environmentIntensity = parsedData.environmentIntensity;\r\n        }\r\n\r\n        // IBL Intensity\r\n        if (parsedData.iblIntensity !== undefined && parsedData.iblIntensity !== null) {\r\n            scene.iblIntensity = parsedData.iblIntensity;\r\n        }\r\n\r\n        // Lights\r\n        if (parsedData.lights !== undefined && parsedData.lights !== null) {\r\n            for (index = 0, cache = parsedData.lights.length; index < cache; index++) {\r\n                const parsedLight = parsedData.lights[index];\r\n                const light = Light.Parse(parsedLight, scene);\r\n                if (light) {\r\n                    TempIndexContainer[parsedLight.uniqueId] = light;\r\n                    container.lights.push(light);\r\n                    light._parentContainer = container;\r\n                    log += index === 0 ? \"\\n\\tLights:\" : \"\";\r\n                    log += \"\\n\\t\\t\" + light.toString(fullDetails);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Reflection probes\r\n        if (parsedData.reflectionProbes !== undefined && parsedData.reflectionProbes !== null) {\r\n            for (index = 0, cache = parsedData.reflectionProbes.length; index < cache; index++) {\r\n                const parsedReflectionProbe = parsedData.reflectionProbes[index];\r\n                const reflectionProbe = ReflectionProbe.Parse(parsedReflectionProbe, scene, rootUrl);\r\n                if (reflectionProbe) {\r\n                    container.reflectionProbes.push(reflectionProbe);\r\n                    reflectionProbe._parentContainer = container;\r\n                    log += index === 0 ? \"\\n\\tReflection Probes:\" : \"\";\r\n                    log += \"\\n\\t\\t\" + reflectionProbe.toString(fullDetails);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Animations\r\n        if (parsedData.animations !== undefined && parsedData.animations !== null) {\r\n            for (index = 0, cache = parsedData.animations.length; index < cache; index++) {\r\n                const parsedAnimation = parsedData.animations[index];\r\n                const internalClass = GetClass(\"BABYLON.Animation\");\r\n                if (internalClass) {\r\n                    const animation = internalClass.Parse(parsedAnimation);\r\n                    scene.animations.push(animation);\r\n                    container.animations.push(animation);\r\n                    log += index === 0 ? \"\\n\\tAnimations:\" : \"\";\r\n                    log += \"\\n\\t\\t\" + animation.toString(fullDetails);\r\n                }\r\n            }\r\n        }\r\n\r\n        // Materials\r\n        if (parsedData.materials !== undefined && parsedData.materials !== null) {\r\n            for (index = 0, cache = parsedData.materials.length; index < cache; index++) {\r\n                const parsedMaterial = parsedData.materials[index];\r\n                const mat = Material.Parse(parsedMaterial, scene, rootUrl);\r\n                if (mat) {\r\n                    TempMaterialIndexContainer[parsedMaterial.uniqueId || parsedMaterial.id] = mat;\r\n                    container.materials.push(mat);\r\n                    mat._parentContainer = container;\r\n                    log += index === 0 ? \"\\n\\tMaterials:\" : \"\";\r\n                    log += \"\\n\\t\\t\" + mat.toString(fullDetails);\r\n\r\n                    // Textures\r\n                    const textures = mat.getActiveTextures();\r\n                    for (const t of textures) {\r\n                        if (container.textures.indexOf(t) == -1) {\r\n                            container.textures.push(t);\r\n                            t._parentContainer = container;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (parsedData.multiMaterials !== undefined && parsedData.multiMaterials !== null) {\r\n            for (index = 0, cache = parsedData.multiMaterials.length; index < cache; index++) {\r\n                const parsedMultiMaterial = parsedData.multiMaterials[index];\r\n                const mmat = MultiMaterial.ParseMultiMaterial(parsedMultiMaterial, scene);\r\n                TempMaterialIndexContainer[parsedMultiMaterial.uniqueId || parsedMultiMaterial.id] = mmat;\r\n                container.multiMaterials.push(mmat);\r\n                mmat._parentContainer = container;\r\n\r\n                log += index === 0 ? \"\\n\\tMultiMaterials:\" : \"\";\r\n                log += \"\\n\\t\\t\" + mmat.toString(fullDetails);\r\n\r\n                // Textures\r\n                const textures = mmat.getActiveTextures();\r\n                for (const t of textures) {\r\n                    if (container.textures.indexOf(t) == -1) {\r\n                        container.textures.push(t);\r\n                        t._parentContainer = container;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // Morph targets\r\n        if (parsedData.morphTargetManagers !== undefined && parsedData.morphTargetManagers !== null) {\r\n            for (const parsedManager of parsedData.morphTargetManagers) {\r\n                const manager = MorphTargetManager.Parse(parsedManager, scene);\r\n                TempMorphTargetManagerIndexContainer[parsedManager.id] = manager;\r\n                container.morphTargetManagers.push(manager);\r\n                manager._parentContainer = container;\r\n            }\r\n        }\r\n\r\n        // Skeletons\r\n        if (parsedData.skeletons !== undefined && parsedData.skeletons !== null) {\r\n            for (index = 0, cache = parsedData.skeletons.length; index < cache; index++) {\r\n                const parsedSkeleton = parsedData.skeletons[index];\r\n                const skeleton = Skeleton.Parse(parsedSkeleton, scene);\r\n                container.skeletons.push(skeleton);\r\n                skeleton._parentContainer = container;\r\n                log += index === 0 ? \"\\n\\tSkeletons:\" : \"\";\r\n                log += \"\\n\\t\\t\" + skeleton.toString(fullDetails);\r\n            }\r\n        }\r\n\r\n        // Geometries\r\n        const geometries = parsedData.geometries;\r\n        if (geometries !== undefined && geometries !== null) {\r\n            const addedGeometry = new Array<Nullable<Geometry>>();\r\n\r\n            // VertexData\r\n            const vertexData = geometries.vertexData;\r\n            if (vertexData !== undefined && vertexData !== null) {\r\n                for (index = 0, cache = vertexData.length; index < cache; index++) {\r\n                    const parsedVertexData = vertexData[index];\r\n                    addedGeometry.push(Geometry.Parse(parsedVertexData, scene, rootUrl));\r\n                }\r\n            }\r\n\r\n            for (const g of addedGeometry) {\r\n                if (g) {\r\n                    container.geometries.push(g);\r\n                    g._parentContainer = container;\r\n                }\r\n            }\r\n        }\r\n\r\n        // Transform nodes\r\n        if (parsedData.transformNodes !== undefined && parsedData.transformNodes !== null) {\r\n            for (index = 0, cache = parsedData.transformNodes.length; index < cache; index++) {\r\n                const parsedTransformNode = parsedData.transformNodes[index];\r\n                const node = TransformNode.Parse(parsedTransformNode, scene, rootUrl);\r\n                TempIndexContainer[parsedTransformNode.uniqueId] = node;\r\n                container.transformNodes.push(node);\r\n                node._parentContainer = container;\r\n            }\r\n        }\r\n\r\n        // Meshes\r\n        if (parsedData.meshes !== undefined && parsedData.meshes !== null) {\r\n            for (index = 0, cache = parsedData.meshes.length; index < cache; index++) {\r\n                const parsedMesh = parsedData.meshes[index];\r\n                const mesh = <AbstractMesh>Mesh.Parse(parsedMesh, scene, rootUrl);\r\n                TempIndexContainer[parsedMesh.uniqueId] = mesh;\r\n                container.meshes.push(mesh);\r\n                mesh._parentContainer = container;\r\n                if (mesh.hasInstances) {\r\n                    for (const instance of (mesh as Mesh).instances) {\r\n                        container.meshes.push(instance);\r\n                        instance._parentContainer = container;\r\n                    }\r\n                }\r\n                log += index === 0 ? \"\\n\\tMeshes:\" : \"\";\r\n                log += \"\\n\\t\\t\" + mesh.toString(fullDetails);\r\n            }\r\n        }\r\n\r\n        // Cameras\r\n        if (parsedData.cameras !== undefined && parsedData.cameras !== null) {\r\n            for (index = 0, cache = parsedData.cameras.length; index < cache; index++) {\r\n                const parsedCamera = parsedData.cameras[index];\r\n                const camera = Camera.Parse(parsedCamera, scene);\r\n                TempIndexContainer[parsedCamera.uniqueId] = camera;\r\n                container.cameras.push(camera);\r\n                camera._parentContainer = container;\r\n                log += index === 0 ? \"\\n\\tCameras:\" : \"\";\r\n                log += \"\\n\\t\\t\" + camera.toString(fullDetails);\r\n            }\r\n        }\r\n\r\n        // Postprocesses\r\n        if (parsedData.postProcesses !== undefined && parsedData.postProcesses !== null) {\r\n            for (index = 0, cache = parsedData.postProcesses.length; index < cache; index++) {\r\n                const parsedPostProcess = parsedData.postProcesses[index];\r\n                const postProcess = PostProcess.Parse(parsedPostProcess, scene, rootUrl);\r\n                if (postProcess) {\r\n                    container.postProcesses.push(postProcess);\r\n                    postProcess._parentContainer = container;\r\n                    log += index === 0 ? \"\\nPostprocesses:\" : \"\";\r\n                    log += \"\\n\\t\\t\" + postProcess.toString();\r\n                }\r\n            }\r\n        }\r\n\r\n        // Animation Groups\r\n        if (parsedData.animationGroups !== undefined && parsedData.animationGroups !== null && parsedData.animationGroups.length) {\r\n            // Build the nodeMap only for scenes with animationGroups\r\n            const nodeMap = new Map<Node[\"id\"], Node>();\r\n            // Nodes in scene does not change when parsing animationGroups, so it's safe to build a map.\r\n            // This follows the order of scene.getNodeById: mesh, transformNode, light, camera, bone\r\n            for (let index = 0; index < scene.meshes.length; index++) {\r\n                // This follows the behavior of scene.getXXXById, which picks the first match\r\n                if (!nodeMap.has(scene.meshes[index].id)) {\r\n                    nodeMap.set(scene.meshes[index].id, scene.meshes[index]);\r\n                }\r\n            }\r\n            for (let index = 0; index < scene.transformNodes.length; index++) {\r\n                if (!nodeMap.has(scene.transformNodes[index].id)) {\r\n                    nodeMap.set(scene.transformNodes[index].id, scene.transformNodes[index]);\r\n                }\r\n            }\r\n            for (let index = 0; index < scene.lights.length; index++) {\r\n                if (!nodeMap.has(scene.lights[index].id)) {\r\n                    nodeMap.set(scene.lights[index].id, scene.lights[index]);\r\n                }\r\n            }\r\n            for (let index = 0; index < scene.cameras.length; index++) {\r\n                if (!nodeMap.has(scene.cameras[index].id)) {\r\n                    nodeMap.set(scene.cameras[index].id, scene.cameras[index]);\r\n                }\r\n            }\r\n            for (let skeletonIndex = 0; skeletonIndex < scene.skeletons.length; skeletonIndex++) {\r\n                const skeleton = scene.skeletons[skeletonIndex];\r\n                for (let boneIndex = 0; boneIndex < skeleton.bones.length; boneIndex++) {\r\n                    if (!nodeMap.has(skeleton.bones[boneIndex].id)) {\r\n                        nodeMap.set(skeleton.bones[boneIndex].id, skeleton.bones[boneIndex]);\r\n                    }\r\n                }\r\n            }\r\n            for (index = 0, cache = parsedData.animationGroups.length; index < cache; index++) {\r\n                const parsedAnimationGroup = parsedData.animationGroups[index];\r\n                const animationGroup = AnimationGroup.Parse(parsedAnimationGroup, scene, nodeMap);\r\n                container.animationGroups.push(animationGroup);\r\n                animationGroup._parentContainer = container;\r\n                log += index === 0 ? \"\\n\\tAnimationGroups:\" : \"\";\r\n                log += \"\\n\\t\\t\" + animationGroup.toString(fullDetails);\r\n            }\r\n        }\r\n\r\n        // Sprites\r\n        if (parsedData.spriteManagers) {\r\n            for (let index = 0, cache = parsedData.spriteManagers.length; index < cache; index++) {\r\n                const parsedSpriteManager = parsedData.spriteManagers[index];\r\n                const spriteManager = SpriteManager.Parse(parsedSpriteManager, scene, rootUrl);\r\n                log += \"\\n\\t\\tSpriteManager \" + spriteManager.name;\r\n            }\r\n        }\r\n\r\n        // Browsing all the graph to connect the dots\r\n        for (index = 0, cache = scene.cameras.length; index < cache; index++) {\r\n            const camera = scene.cameras[index];\r\n            if (camera._waitingParentId !== null) {\r\n                camera.parent = FindParent(camera._waitingParentId, camera._waitingParentInstanceIndex, scene);\r\n                camera._waitingParentId = null;\r\n                camera._waitingParentInstanceIndex = null;\r\n            }\r\n        }\r\n\r\n        for (index = 0, cache = scene.lights.length; index < cache; index++) {\r\n            const light = scene.lights[index];\r\n            if (light && light._waitingParentId !== null) {\r\n                light.parent = FindParent(light._waitingParentId, light._waitingParentInstanceIndex, scene);\r\n                light._waitingParentId = null;\r\n                light._waitingParentInstanceIndex = null;\r\n            }\r\n        }\r\n\r\n        // Connect parents & children and parse actions and lods\r\n        for (index = 0, cache = scene.transformNodes.length; index < cache; index++) {\r\n            const transformNode = scene.transformNodes[index];\r\n            if (transformNode._waitingParentId !== null) {\r\n                transformNode.parent = FindParent(transformNode._waitingParentId, transformNode._waitingParentInstanceIndex, scene);\r\n                transformNode._waitingParentId = null;\r\n                transformNode._waitingParentInstanceIndex = null;\r\n            }\r\n        }\r\n        for (index = 0, cache = scene.meshes.length; index < cache; index++) {\r\n            const mesh = scene.meshes[index];\r\n            if (mesh._waitingParentId !== null) {\r\n                mesh.parent = FindParent(mesh._waitingParentId, mesh._waitingParentInstanceIndex, scene);\r\n                mesh._waitingParentId = null;\r\n                mesh._waitingParentInstanceIndex = null;\r\n            }\r\n            if (mesh._waitingData.lods) {\r\n                LoadDetailLevels(scene, mesh);\r\n            }\r\n        }\r\n\r\n        // link multimats with materials\r\n        for (const multimat of scene.multiMaterials) {\r\n            for (const subMaterial of multimat._waitingSubMaterialsUniqueIds) {\r\n                multimat.subMaterials.push(FindMaterial(subMaterial, scene));\r\n            }\r\n            multimat._waitingSubMaterialsUniqueIds = [];\r\n        }\r\n\r\n        // link meshes with materials\r\n        for (const mesh of scene.meshes) {\r\n            if (mesh._waitingMaterialId !== null) {\r\n                mesh.material = FindMaterial(mesh._waitingMaterialId, scene);\r\n                mesh._waitingMaterialId = null;\r\n            }\r\n        }\r\n\r\n        // link meshes with morph target managers\r\n        for (const mesh of scene.meshes) {\r\n            if (mesh._waitingMorphTargetManagerId !== null) {\r\n                mesh.morphTargetManager = TempMorphTargetManagerIndexContainer[mesh._waitingMorphTargetManagerId];\r\n                mesh._waitingMorphTargetManagerId = null;\r\n            }\r\n        }\r\n\r\n        // link skeleton transform nodes\r\n        for (index = 0, cache = scene.skeletons.length; index < cache; index++) {\r\n            const skeleton = scene.skeletons[index];\r\n            if (skeleton._hasWaitingData) {\r\n                if (skeleton.bones != null) {\r\n                    for (const bone of skeleton.bones) {\r\n                        if (bone._waitingTransformNodeId) {\r\n                            const linkTransformNode = scene.getLastEntryById(bone._waitingTransformNodeId) as TransformNode;\r\n                            if (linkTransformNode) {\r\n                                bone.linkTransformNode(linkTransformNode);\r\n                            }\r\n                            bone._waitingTransformNodeId = null;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                skeleton._hasWaitingData = null;\r\n            }\r\n        }\r\n\r\n        // freeze world matrix application\r\n        for (index = 0, cache = scene.meshes.length; index < cache; index++) {\r\n            const currentMesh = scene.meshes[index];\r\n            if (currentMesh._waitingData.freezeWorldMatrix) {\r\n                currentMesh.freezeWorldMatrix();\r\n                currentMesh._waitingData.freezeWorldMatrix = null;\r\n            } else {\r\n                currentMesh.computeWorldMatrix(true);\r\n            }\r\n        }\r\n\r\n        // Lights exclusions / inclusions\r\n        for (index = 0, cache = scene.lights.length; index < cache; index++) {\r\n            const light = scene.lights[index];\r\n            // Excluded check\r\n            if (light._excludedMeshesIds.length > 0) {\r\n                for (let excludedIndex = 0; excludedIndex < light._excludedMeshesIds.length; excludedIndex++) {\r\n                    const excludedMesh = scene.getMeshById(light._excludedMeshesIds[excludedIndex]);\r\n\r\n                    if (excludedMesh) {\r\n                        light.excludedMeshes.push(excludedMesh);\r\n                    }\r\n                }\r\n\r\n                light._excludedMeshesIds = [];\r\n            }\r\n\r\n            // Included check\r\n            if (light._includedOnlyMeshesIds.length > 0) {\r\n                for (let includedOnlyIndex = 0; includedOnlyIndex < light._includedOnlyMeshesIds.length; includedOnlyIndex++) {\r\n                    const includedOnlyMesh = scene.getMeshById(light._includedOnlyMeshesIds[includedOnlyIndex]);\r\n\r\n                    if (includedOnlyMesh) {\r\n                        light.includedOnlyMeshes.push(includedOnlyMesh);\r\n                    }\r\n                }\r\n\r\n                light._includedOnlyMeshesIds = [];\r\n            }\r\n        }\r\n\r\n        for (const g of scene.geometries) {\r\n            g._loadedUniqueId = \"\";\r\n        }\r\n\r\n        Parse(parsedData, scene, container, rootUrl);\r\n\r\n        // Actions (scene) Done last as it can access other objects.\r\n        for (index = 0, cache = scene.meshes.length; index < cache; index++) {\r\n            const mesh = scene.meshes[index];\r\n            if (mesh._waitingData.actions) {\r\n                ActionManager.Parse(mesh._waitingData.actions, mesh, scene);\r\n                mesh._waitingData.actions = null;\r\n            }\r\n        }\r\n        if (parsedData.actions !== undefined && parsedData.actions !== null) {\r\n            ActionManager.Parse(parsedData.actions, null, scene);\r\n        }\r\n    } catch (err) {\r\n        const msg = logOperation(\"loadAssets\", parsedData ? parsedData.producer : \"Unknown\") + log;\r\n        if (onError) {\r\n            onError(msg, err);\r\n        } else {\r\n            Logger.Log(msg);\r\n            throw err;\r\n        }\r\n    } finally {\r\n        TempIndexContainer = {};\r\n        TempMaterialIndexContainer = {};\r\n        TempMorphTargetManagerIndexContainer = {};\r\n\r\n        if (!addToScene) {\r\n            container.removeAllFromScene();\r\n        }\r\n        if (log !== null && SceneLoader.loggingLevel !== SceneLoader.NO_LOGGING) {\r\n            Logger.Log(logOperation(\"loadAssets\", parsedData ? parsedData.producer : \"Unknown\") + (SceneLoader.loggingLevel !== SceneLoader.MINIMAL_LOGGING ? log : \"\"));\r\n        }\r\n    }\r\n\r\n    return container;\r\n};\r\n\r\nSceneLoader.RegisterPlugin({\r\n    name: \"babylon.js\",\r\n    extensions: \".babylon\",\r\n    canDirectLoad: (data: string) => {\r\n        if (data.indexOf(\"babylon\") !== -1) {\r\n            // We consider that the producer string is filled\r\n            return true;\r\n        }\r\n\r\n        return false;\r\n    },\r\n    importMesh: (\r\n        meshesNames: any,\r\n        scene: Scene,\r\n        data: any,\r\n        rootUrl: string,\r\n        meshes: AbstractMesh[],\r\n        particleSystems: IParticleSystem[],\r\n        skeletons: Skeleton[],\r\n        onError?: (message: string, exception?: any) => void\r\n    ): boolean => {\r\n        // Entire method running in try block, so ALWAYS logs as far as it got, only actually writes details\r\n        // when SceneLoader.debugLogging = true (default), or exception encountered.\r\n        // Everything stored in var log instead of writing separate lines to support only writing in exception,\r\n        // and avoid problems with multiple concurrent .babylon loads.\r\n        let log = \"importMesh has failed JSON parse\";\r\n        try {\r\n            // eslint-disable-next-line no-var\r\n            var parsedData = JSON.parse(data);\r\n            log = \"\";\r\n            const fullDetails = SceneLoader.loggingLevel === SceneLoader.DETAILED_LOGGING;\r\n            if (!meshesNames) {\r\n                meshesNames = null;\r\n            } else if (!Array.isArray(meshesNames)) {\r\n                meshesNames = [meshesNames];\r\n            }\r\n\r\n            const hierarchyIds: number[] = [];\r\n            const parsedIdToNodeMap = new Map<number, Node>();\r\n\r\n            // Transform nodes (the overall idea is to load all of them as this is super fast and then get rid of the ones we don't need)\r\n            const loadedTransformNodes = [];\r\n            if (parsedData.transformNodes !== undefined && parsedData.transformNodes !== null) {\r\n                for (let index = 0, cache = parsedData.transformNodes.length; index < cache; index++) {\r\n                    const parsedJSONTransformNode = parsedData.transformNodes[index];\r\n                    const parsedTransformNode = TransformNode.Parse(parsedJSONTransformNode, scene, rootUrl);\r\n                    loadedTransformNodes.push(parsedTransformNode);\r\n                    parsedIdToNodeMap.set(parsedTransformNode._waitingParsedUniqueId!, parsedTransformNode);\r\n                    parsedTransformNode._waitingParsedUniqueId = null;\r\n                }\r\n            }\r\n            if (parsedData.meshes !== undefined && parsedData.meshes !== null) {\r\n                const loadedSkeletonsIds = [];\r\n                const loadedMaterialsIds: string[] = [];\r\n                const loadedMaterialsUniqueIds: string[] = [];\r\n                const loadedMorphTargetManagerIds: number[] = [];\r\n                for (let index = 0, cache = parsedData.meshes.length; index < cache; index++) {\r\n                    const parsedMesh = parsedData.meshes[index];\r\n\r\n                    if (meshesNames === null || IsDescendantOf(parsedMesh, meshesNames, hierarchyIds)) {\r\n                        if (meshesNames !== null) {\r\n                            // Remove found mesh name from list.\r\n                            delete meshesNames[meshesNames.indexOf(parsedMesh.name)];\r\n                        }\r\n\r\n                        //Geometry?\r\n                        if (parsedMesh.geometryId !== undefined && parsedMesh.geometryId !== null) {\r\n                            //does the file contain geometries?\r\n                            if (parsedData.geometries !== undefined && parsedData.geometries !== null) {\r\n                                //find the correct geometry and add it to the scene\r\n                                let found: boolean = false;\r\n                                const geoms = [\"boxes\", \"spheres\", \"cylinders\", \"toruses\", \"grounds\", \"planes\", \"torusKnots\", \"vertexData\"];\r\n                                for (const geometryType of geoms) {\r\n                                    if (!parsedData.geometries[geometryType] || !Array.isArray(parsedData.geometries[geometryType])) {\r\n                                        continue;\r\n                                    }\r\n                                    const geom = parsedData.geometries[geometryType];\r\n                                    for (const parsedGeometryData of geom) {\r\n                                        if (parsedGeometryData.id === parsedMesh.geometryId) {\r\n                                            switch (geometryType) {\r\n                                                case \"vertexData\":\r\n                                                    Geometry.Parse(parsedGeometryData, scene, rootUrl);\r\n                                                    break;\r\n                                            }\r\n                                            found = true;\r\n                                            break;\r\n                                        }\r\n                                    }\r\n\r\n                                    if (found) {\r\n                                        break;\r\n                                    }\r\n                                }\r\n                                if (found === false) {\r\n                                    Logger.Warn(\"Geometry not found for mesh \" + parsedMesh.id);\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        // Material ?\r\n                        if (parsedMesh.materialUniqueId || parsedMesh.materialId) {\r\n                            // if we have a unique ID, look up and store in loadedMaterialsUniqueIds, else use loadedMaterialsIds\r\n                            const materialArray = parsedMesh.materialUniqueId ? loadedMaterialsUniqueIds : loadedMaterialsIds;\r\n                            let materialFound = materialArray.indexOf(parsedMesh.materialUniqueId || parsedMesh.materialId) !== -1;\r\n                            if (materialFound === false && parsedData.multiMaterials !== undefined && parsedData.multiMaterials !== null) {\r\n                                // Loads a submaterial of a multimaterial\r\n                                const loadSubMaterial = (subMatId: string, predicate: (parsedMaterial: any) => boolean) => {\r\n                                    materialArray.push(subMatId);\r\n                                    const mat = ParseMaterialByPredicate(predicate, parsedData, scene, rootUrl);\r\n                                    if (mat && mat.material) {\r\n                                        TempMaterialIndexContainer[mat.parsedMaterial.uniqueId || mat.parsedMaterial.id] = mat.material;\r\n                                        log += \"\\n\\tMaterial \" + mat.material.toString(fullDetails);\r\n                                    }\r\n                                };\r\n                                for (let multimatIndex = 0, multimatCache = parsedData.multiMaterials.length; multimatIndex < multimatCache; multimatIndex++) {\r\n                                    const parsedMultiMaterial = parsedData.multiMaterials[multimatIndex];\r\n                                    if (\r\n                                        (parsedMesh.materialUniqueId && parsedMultiMaterial.uniqueId === parsedMesh.materialUniqueId) ||\r\n                                        parsedMultiMaterial.id === parsedMesh.materialId\r\n                                    ) {\r\n                                        if (parsedMultiMaterial.materialsUniqueIds) {\r\n                                            // if the materials inside the multimat are stored by unique id\r\n                                            for (const subMatId of parsedMultiMaterial.materialsUniqueIds) {\r\n                                                loadSubMaterial(subMatId, (parsedMaterial) => parsedMaterial.uniqueId === subMatId);\r\n                                            }\r\n                                        } else {\r\n                                            // if the mats are stored by id instead\r\n                                            for (const subMatId of parsedMultiMaterial.materials) {\r\n                                                loadSubMaterial(subMatId, (parsedMaterial) => parsedMaterial.id === subMatId);\r\n                                            }\r\n                                        }\r\n                                        materialArray.push(parsedMultiMaterial.uniqueId || parsedMultiMaterial.id);\r\n                                        const mmat = MultiMaterial.ParseMultiMaterial(parsedMultiMaterial, scene);\r\n                                        TempMaterialIndexContainer[parsedMultiMaterial.uniqueId || parsedMultiMaterial.id] = mmat;\r\n                                        if (mmat) {\r\n                                            materialFound = true;\r\n                                            log += \"\\n\\tMulti-Material \" + mmat.toString(fullDetails);\r\n                                        }\r\n                                        break;\r\n                                    }\r\n                                }\r\n                            }\r\n\r\n                            if (materialFound === false) {\r\n                                materialArray.push(parsedMesh.materialUniqueId || parsedMesh.materialId);\r\n                                const mat = ParseMaterialByPredicate(\r\n                                    (parsedMaterial) =>\r\n                                        (parsedMesh.materialUniqueId && parsedMaterial.uniqueId === parsedMesh.materialUniqueId) || parsedMaterial.id === parsedMesh.materialId,\r\n                                    parsedData,\r\n                                    scene,\r\n                                    rootUrl\r\n                                );\r\n                                if (!mat || !mat.material) {\r\n                                    Logger.Warn(\"Material not found for mesh \" + parsedMesh.id);\r\n                                } else {\r\n                                    TempMaterialIndexContainer[mat.parsedMaterial.uniqueId || mat.parsedMaterial.id] = mat.material;\r\n                                    log += \"\\n\\tMaterial \" + mat.material.toString(fullDetails);\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        // Skeleton ?\r\n                        if (\r\n                            parsedMesh.skeletonId !== null &&\r\n                            parsedMesh.skeletonId !== undefined &&\r\n                            parsedData.skeletonId !== -1 &&\r\n                            parsedData.skeletons !== undefined &&\r\n                            parsedData.skeletons !== null\r\n                        ) {\r\n                            const skeletonAlreadyLoaded = loadedSkeletonsIds.indexOf(parsedMesh.skeletonId) > -1;\r\n                            if (!skeletonAlreadyLoaded) {\r\n                                for (let skeletonIndex = 0, skeletonCache = parsedData.skeletons.length; skeletonIndex < skeletonCache; skeletonIndex++) {\r\n                                    const parsedSkeleton = parsedData.skeletons[skeletonIndex];\r\n                                    if (parsedSkeleton.id === parsedMesh.skeletonId) {\r\n                                        const skeleton = Skeleton.Parse(parsedSkeleton, scene);\r\n                                        skeletons.push(skeleton);\r\n                                        loadedSkeletonsIds.push(parsedSkeleton.id);\r\n                                        log += \"\\n\\tSkeleton \" + skeleton.toString(fullDetails);\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        // Morph targets ?\r\n                        if (parsedMesh.morphTargetManagerId > -1 && parsedData.morphTargetManagers !== undefined && parsedData.morphTargetManagers !== null) {\r\n                            const morphTargetManagerAlreadyLoaded = loadedMorphTargetManagerIds.indexOf(parsedMesh.morphTargetManagerId) > -1;\r\n                            if (!morphTargetManagerAlreadyLoaded) {\r\n                                for (let morphTargetManagerIndex = 0; morphTargetManagerIndex < parsedData.morphTargetManagers.length; morphTargetManagerIndex++) {\r\n                                    const parsedManager = parsedData.morphTargetManagers[morphTargetManagerIndex];\r\n                                    if (parsedManager.id === parsedMesh.morphTargetManagerId) {\r\n                                        const morphTargetManager = MorphTargetManager.Parse(parsedManager, scene);\r\n                                        TempMorphTargetManagerIndexContainer[parsedManager.id] = morphTargetManager;\r\n                                        loadedMorphTargetManagerIds.push(parsedManager.id);\r\n                                        log += \"\\nMorph target manager\" + morphTargetManager.toString();\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        const mesh = Mesh.Parse(parsedMesh, scene, rootUrl);\r\n                        meshes.push(mesh);\r\n                        parsedIdToNodeMap.set(mesh._waitingParsedUniqueId!, mesh);\r\n                        mesh._waitingParsedUniqueId = null;\r\n                        log += \"\\n\\tMesh \" + mesh.toString(fullDetails);\r\n                    }\r\n                }\r\n\r\n                // link multimats with materials\r\n                for (const multimat of scene.multiMaterials) {\r\n                    for (const subMaterial of multimat._waitingSubMaterialsUniqueIds) {\r\n                        multimat.subMaterials.push(FindMaterial(subMaterial, scene));\r\n                    }\r\n                    multimat._waitingSubMaterialsUniqueIds = [];\r\n                }\r\n\r\n                // link meshes with materials\r\n                for (const mesh of scene.meshes) {\r\n                    if (mesh._waitingMaterialId !== null) {\r\n                        mesh.material = FindMaterial(mesh._waitingMaterialId, scene);\r\n                        mesh._waitingMaterialId = null;\r\n                    }\r\n                }\r\n\r\n                // link meshes with morph target managers\r\n                for (const mesh of scene.meshes) {\r\n                    if (mesh._waitingMorphTargetManagerId !== null) {\r\n                        mesh.morphTargetManager = TempMorphTargetManagerIndexContainer[mesh._waitingMorphTargetManagerId];\r\n                        mesh._waitingMorphTargetManagerId = null;\r\n                    }\r\n                }\r\n\r\n                // Connecting parents and lods\r\n                for (let index = 0, cache = scene.transformNodes.length; index < cache; index++) {\r\n                    const transformNode = scene.transformNodes[index];\r\n                    if (transformNode._waitingParentId !== null) {\r\n                        let parent = parsedIdToNodeMap.get(parseInt(transformNode._waitingParentId)) || null;\r\n                        if (parent === null) {\r\n                            parent = scene.getLastEntryById(transformNode._waitingParentId);\r\n                        }\r\n                        let parentNode = parent;\r\n                        if (transformNode._waitingParentInstanceIndex) {\r\n                            parentNode = (parent as Mesh).instances[parseInt(transformNode._waitingParentInstanceIndex)];\r\n                            transformNode._waitingParentInstanceIndex = null;\r\n                        }\r\n                        transformNode.parent = parentNode;\r\n                        transformNode._waitingParentId = null;\r\n                    }\r\n                }\r\n                let currentMesh: AbstractMesh;\r\n                for (let index = 0, cache = scene.meshes.length; index < cache; index++) {\r\n                    currentMesh = scene.meshes[index];\r\n                    if (currentMesh._waitingParentId) {\r\n                        let parent = parsedIdToNodeMap.get(parseInt(currentMesh._waitingParentId)) || null;\r\n                        if (parent === null) {\r\n                            parent = scene.getLastEntryById(currentMesh._waitingParentId);\r\n                        }\r\n                        let parentNode = parent;\r\n                        if (currentMesh._waitingParentInstanceIndex) {\r\n                            parentNode = (parent as Mesh).instances[parseInt(currentMesh._waitingParentInstanceIndex)];\r\n                            currentMesh._waitingParentInstanceIndex = null;\r\n                        }\r\n                        currentMesh.parent = parentNode;\r\n                        currentMesh._waitingParentId = null;\r\n                    }\r\n                    if (currentMesh._waitingData.lods) {\r\n                        LoadDetailLevels(scene, currentMesh);\r\n                    }\r\n                }\r\n\r\n                // Remove unused transform nodes\r\n                for (const transformNode of loadedTransformNodes) {\r\n                    const childMeshes = transformNode.getChildMeshes(false);\r\n                    if (!childMeshes.length) {\r\n                        transformNode.dispose();\r\n                    }\r\n                }\r\n\r\n                // link skeleton transform nodes\r\n                for (let index = 0, cache = scene.skeletons.length; index < cache; index++) {\r\n                    const skeleton = scene.skeletons[index];\r\n                    if (skeleton._hasWaitingData) {\r\n                        if (skeleton.bones != null) {\r\n                            for (const bone of skeleton.bones) {\r\n                                if (bone._waitingTransformNodeId) {\r\n                                    const linkTransformNode = scene.getLastEntryById(bone._waitingTransformNodeId) as TransformNode;\r\n                                    if (linkTransformNode) {\r\n                                        bone.linkTransformNode(linkTransformNode);\r\n                                    }\r\n                                    bone._waitingTransformNodeId = null;\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                        skeleton._hasWaitingData = null;\r\n                    }\r\n                }\r\n\r\n                // freeze and compute world matrix application\r\n                for (let index = 0, cache = scene.meshes.length; index < cache; index++) {\r\n                    currentMesh = scene.meshes[index];\r\n                    if (currentMesh._waitingData.freezeWorldMatrix) {\r\n                        currentMesh.freezeWorldMatrix();\r\n                        currentMesh._waitingData.freezeWorldMatrix = null;\r\n                    } else {\r\n                        currentMesh.computeWorldMatrix(true);\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Particles\r\n            if (parsedData.particleSystems !== undefined && parsedData.particleSystems !== null) {\r\n                const parser = GetIndividualParser(SceneComponentConstants.NAME_PARTICLESYSTEM);\r\n                if (parser) {\r\n                    for (let index = 0, cache = parsedData.particleSystems.length; index < cache; index++) {\r\n                        const parsedParticleSystem = parsedData.particleSystems[index];\r\n                        if (hierarchyIds.indexOf(parsedParticleSystem.emitterId) !== -1) {\r\n                            particleSystems.push(parser(parsedParticleSystem, scene, rootUrl));\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            for (const g of scene.geometries) {\r\n                g._loadedUniqueId = \"\";\r\n            }\r\n\r\n            return true;\r\n        } catch (err) {\r\n            const msg = logOperation(\"importMesh\", parsedData ? parsedData.producer : \"Unknown\") + log;\r\n            if (onError) {\r\n                onError(msg, err);\r\n            } else {\r\n                Logger.Log(msg);\r\n                throw err;\r\n            }\r\n        } finally {\r\n            if (log !== null && SceneLoader.loggingLevel !== SceneLoader.NO_LOGGING) {\r\n                Logger.Log(logOperation(\"importMesh\", parsedData ? parsedData.producer : \"Unknown\") + (SceneLoader.loggingLevel !== SceneLoader.MINIMAL_LOGGING ? log : \"\"));\r\n            }\r\n            TempMaterialIndexContainer = {};\r\n            TempMorphTargetManagerIndexContainer = {};\r\n        }\r\n\r\n        return false;\r\n    },\r\n    load: (scene: Scene, data: string, rootUrl: string, onError?: (message: string, exception?: any) => void): boolean => {\r\n        // Entire method running in try block, so ALWAYS logs as far as it got, only actually writes details\r\n        // when SceneLoader.debugLogging = true (default), or exception encountered.\r\n        // Everything stored in var log instead of writing separate lines to support only writing in exception,\r\n        // and avoid problems with multiple concurrent .babylon loads.\r\n        let log = \"importScene has failed JSON parse\";\r\n        try {\r\n            // eslint-disable-next-line no-var\r\n            var parsedData = JSON.parse(data);\r\n            log = \"\";\r\n\r\n            // Scene\r\n            if (parsedData.useDelayedTextureLoading !== undefined && parsedData.useDelayedTextureLoading !== null) {\r\n                scene.useDelayedTextureLoading = parsedData.useDelayedTextureLoading && !SceneLoader.ForceFullSceneLoadingForIncremental;\r\n            }\r\n            if (parsedData.autoClear !== undefined && parsedData.autoClear !== null) {\r\n                scene.autoClear = parsedData.autoClear;\r\n            }\r\n            if (parsedData.clearColor !== undefined && parsedData.clearColor !== null) {\r\n                scene.clearColor = Color4.FromArray(parsedData.clearColor);\r\n            }\r\n            if (parsedData.ambientColor !== undefined && parsedData.ambientColor !== null) {\r\n                scene.ambientColor = Color3.FromArray(parsedData.ambientColor);\r\n            }\r\n            if (parsedData.gravity !== undefined && parsedData.gravity !== null) {\r\n                scene.gravity = Vector3.FromArray(parsedData.gravity);\r\n            }\r\n\r\n            if (parsedData.useRightHandedSystem !== undefined) {\r\n                scene.useRightHandedSystem = !!parsedData.useRightHandedSystem;\r\n            }\r\n\r\n            // Fog\r\n            if (parsedData.fogMode !== undefined && parsedData.fogMode !== null) {\r\n                scene.fogMode = parsedData.fogMode;\r\n            }\r\n            if (parsedData.fogColor !== undefined && parsedData.fogColor !== null) {\r\n                scene.fogColor = Color3.FromArray(parsedData.fogColor);\r\n            }\r\n            if (parsedData.fogStart !== undefined && parsedData.fogStart !== null) {\r\n                scene.fogStart = parsedData.fogStart;\r\n            }\r\n            if (parsedData.fogEnd !== undefined && parsedData.fogEnd !== null) {\r\n                scene.fogEnd = parsedData.fogEnd;\r\n            }\r\n            if (parsedData.fogDensity !== undefined && parsedData.fogDensity !== null) {\r\n                scene.fogDensity = parsedData.fogDensity;\r\n            }\r\n            log += \"\\tFog mode for scene:  \";\r\n            switch (scene.fogMode) {\r\n                case 0:\r\n                    log += \"none\\n\";\r\n                    break;\r\n                // getters not compiling, so using hardcoded\r\n                case 1:\r\n                    log += \"exp\\n\";\r\n                    break;\r\n                case 2:\r\n                    log += \"exp2\\n\";\r\n                    break;\r\n                case 3:\r\n                    log += \"linear\\n\";\r\n                    break;\r\n            }\r\n\r\n            //Physics\r\n            if (parsedData.physicsEnabled) {\r\n                let physicsPlugin;\r\n                if (parsedData.physicsEngine === \"cannon\" || parsedData.physicsEngine === CannonJSPlugin.name) {\r\n                    physicsPlugin = new CannonJSPlugin(undefined, undefined, BabylonFileLoaderConfiguration.LoaderInjectedPhysicsEngine);\r\n                } else if (parsedData.physicsEngine === \"oimo\" || parsedData.physicsEngine === OimoJSPlugin.name) {\r\n                    physicsPlugin = new OimoJSPlugin(undefined, BabylonFileLoaderConfiguration.LoaderInjectedPhysicsEngine);\r\n                } else if (parsedData.physicsEngine === \"ammo\" || parsedData.physicsEngine === AmmoJSPlugin.name) {\r\n                    physicsPlugin = new AmmoJSPlugin(undefined, BabylonFileLoaderConfiguration.LoaderInjectedPhysicsEngine, undefined);\r\n                }\r\n                log = \"\\tPhysics engine \" + (parsedData.physicsEngine ? parsedData.physicsEngine : \"oimo\") + \" enabled\\n\";\r\n                //else - default engine, which is currently oimo\r\n                const physicsGravity = parsedData.gravity ? Vector3.FromArray(parsedData.gravity) : parsedData.physicsGravity ? Vector3.FromArray(parsedData.physicsGravity) : null;\r\n                scene.enablePhysics(physicsGravity, physicsPlugin);\r\n            }\r\n\r\n            // Metadata\r\n            if (parsedData.metadata !== undefined && parsedData.metadata !== null) {\r\n                scene.metadata = parsedData.metadata;\r\n            }\r\n\r\n            //collisions, if defined. otherwise, default is true\r\n            if (parsedData.collisionsEnabled !== undefined && parsedData.collisionsEnabled !== null) {\r\n                scene.collisionsEnabled = parsedData.collisionsEnabled;\r\n            }\r\n\r\n            const container = LoadAssetContainer(scene, data, rootUrl, onError, true);\r\n            if (!container) {\r\n                return false;\r\n            }\r\n\r\n            if (parsedData.autoAnimate) {\r\n                scene.beginAnimation(scene, parsedData.autoAnimateFrom, parsedData.autoAnimateTo, parsedData.autoAnimateLoop, parsedData.autoAnimateSpeed || 1.0);\r\n            }\r\n\r\n            if (parsedData.activeCameraID !== undefined && parsedData.activeCameraID !== null) {\r\n                scene.setActiveCameraById(parsedData.activeCameraID);\r\n            }\r\n\r\n            // Finish\r\n            return true;\r\n        } catch (err) {\r\n            const msg = logOperation(\"importScene\", parsedData ? parsedData.producer : \"Unknown\") + log;\r\n            if (onError) {\r\n                onError(msg, err);\r\n            } else {\r\n                Logger.Log(msg);\r\n                throw err;\r\n            }\r\n        } finally {\r\n            if (log !== null && SceneLoader.loggingLevel !== SceneLoader.NO_LOGGING) {\r\n                Logger.Log(logOperation(\"importScene\", parsedData ? parsedData.producer : \"Unknown\") + (SceneLoader.loggingLevel !== SceneLoader.MINIMAL_LOGGING ? log : \"\"));\r\n            }\r\n        }\r\n        return false;\r\n    },\r\n    loadAssetContainer: (scene: Scene, data: string, rootUrl: string, onError?: (message: string, exception?: any) => void): AssetContainer => {\r\n        const container = LoadAssetContainer(scene, data, rootUrl, onError);\r\n        return container;\r\n    },\r\n});\r\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAE9C,OAAO,EAAE,OAAO,EAAE,MAAM,yBAAyB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAEzC,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAEjD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,sCAAsC,CAAC;AACnE,OAAO,EAAE,cAAc,EAAE,MAAM,yCAAyC,CAAC;AACzE,OAAO,EAAE,cAAc,EAAE,MAAM,iCAAiC,CAAC;AACjE,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAC3C,OAAO,EAAE,uBAAuB,EAAE,MAAM,sBAAsB,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAE5D,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAC;AACpE,OAAO,EAAE,cAAc,EAAE,MAAM,yCAAyC,CAAC;AACzE,OAAO,EAAE,YAAY,EAAE,MAAM,uCAAuC,CAAC;AACrE,OAAO,EAAE,YAAY,EAAE,MAAM,uCAAuC,CAAC;AACrE,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AACzC,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,EAAE,aAAa,EAAE,uCAAmC;AAC3D,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,8BAA8B,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAInE,IAAI,wBAAwB,GAAG,IAAI,CAAC;AAKrC,MAAO,8BAA8B;;AACvC;;;;GAIG,CACW,+BAAA,2BAA2B,GAAQ,SAAS,CAAC;AAG/D,IAAI,kBAAkB,GAA4B,CAAA,CAAE,CAAC;AACrD,IAAI,0BAA0B,GAAgC,CAAA,CAAE,CAAC;AACjE,IAAI,oCAAoC,GAA0C,CAAA,CAAE,CAAC;AAErF,MAAM,wBAAwB,GAAG,CAAC,SAA2C,EAAE,UAAe,EAAE,KAAY,EAAE,OAAe,EAAE,EAAE;IAC7H,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;QAC9E,MAAM,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,SAAS,CAAC,cAAc,CAAC,EAAE,CAAC;YAC5B,OAAO;gBAAE,cAAc;gBAAE,QAAQ,kKAAE,WAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,EAAE,OAAO,CAAC;YAAA,CAAE,CAAC;QACxF,CAAC;IACL,CAAC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,IAAS,EAAE,KAAiB,EAAE,YAA2B,EAAE,EAAE;IACjF,IAAK,MAAM,CAAC,IAAI,KAAK,CAAE,CAAC;QACpB,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACzB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC5E,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,gEAAgE;AAChE,MAAM,YAAY,GAAG,CAAC,SAAiB,EAAE,QAAmF,EAAE,EAAE;IAC5H,OACI,AADG,SACM,GACT,MAAM,GACN,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,GAAG,QAAQ,CAAC,IAAI,GAAG,YAAY,GAAG,QAAQ,CAAC,OAAO,GAAG,sBAAsB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAC3J,CAAC;AACN,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CAAC,KAAY,EAAE,IAAkB,EAAE,EAAE;IAC1D,MAAM,UAAU,GAAS,IAAY,CAAC;IAEtC,6HAA6H;IAC7H,gKAAgK;IAChK,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,MAAM,SAAS,GAAa,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC;YACvD,MAAM,UAAU,GAAY,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACxD,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnC,MAAM,SAAS,GAAa,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;gBAC7D,IAAI,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;oBACvC,MAAM,OAAO,GAAW,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;oBAC7B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;wBACpD,MAAM,KAAK,GAAW,SAAS,CAAC,KAAK,CAAC,CAAC;wBACvC,MAAM,OAAO,GAAS,KAAK,CAAC,WAAW,CAAC,KAAK,CAAS,CAAC;wBACvD,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;4BAClB,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;wBACtD,CAAC;oBACL,CAAC;oBACD,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;wBACd,UAAU,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBAC1C,CAAC;oBACD,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;wBACtB,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBAChC,CAAC;gBACL,CAAC,MAAM,CAAC;4KACJ,QAAK,CAAC,IAAI,CAAC,wCAAwC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;gBACrE,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;IAClC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,QAAa,EAAE,mBAAwB,EAAE,KAAY,EAAE,EAAE;IACzE,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAC/B,MAAM,WAAW,GAAG,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,WAAW,IAAI,mBAAmB,KAAK,SAAS,IAAI,mBAAmB,KAAK,IAAI,EAAE,CAAC;YACnF,MAAM,QAAQ,GAAI,WAAoB,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC;YAChF,OAAO,QAAQ,CAAC;QACpB,CAAC;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,MAAM,MAAM,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;IAC5C,IAAI,MAAM,IAAI,mBAAmB,KAAK,SAAS,IAAI,mBAAmB,KAAK,IAAI,EAAE,CAAC;QAC9E,MAAM,QAAQ,GAAI,MAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAC3E,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,UAAe,EAAE,KAAY,EAAE,EAAE;IACnD,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;QACjC,OAAO,KAAK,CAAC,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,0BAA0B,CAAC,UAAU,CAAC,CAAC;AAClD,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,SAAC,KAAY,EAAE,IAAY,EAAE,OAAe,EAAE,OAAoD;QAAE,UAAU,oEAAG,KAAK,EAAkB,EAAE;IACjK,MAAM,SAAS,GAAG,6JAAI,iBAAc,CAAC,KAAK,CAAC,CAAC;IAE5C,oGAAoG;IACpG,4EAA4E;IAC5E,uGAAuG;IACvG,8DAA8D;IAC9D,IAAI,GAAG,GAAG,mCAAmC,CAAC;IAC9C,IAAI,CAAC;QACD,kCAAkC;QAClC,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,GAAG,GAAG,EAAE,CAAC;QACT,MAAM,WAAW,oKAAG,cAAW,CAAC,YAAY,sKAAK,cAAW,CAAC,gBAAgB,CAAC;QAE9E,IAAI,KAAa,CAAC;QAClB,IAAI,KAAa,CAAC;QAElB,sBAAsB;QACtB,IAAI,UAAU,CAAC,kBAAkB,KAAK,SAAS,IAAI,UAAU,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;YACxF,4DAA4D;YAC5D,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;YACvE,IAAI,UAAU,CAAC,sBAAsB,IAAI,UAAU,CAAC,sBAAsB,KAAK,wBAAwB,EAAE,CAAC;gBACtG,MAAM,OAAO,GAAW,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,CAAC;gBACpG,MAAM,UAAU,GAAG,sLAAI,iBAAc,CACjC,CAAC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,kBAAkB,EACpG,KAAK,EACL,OAAO,EACP,IAAI,EACJ,CAAC,KAAK,EACN,SAAS,EACT,UAAU,CAAC,iCAAiC,CAC/C,CAAC;gBACF,IAAI,UAAU,CAAC,2BAA2B,EAAE,CAAC;oBACzC,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,2BAA2B,CAAC;gBAClE,CAAC;gBACD,KAAK,CAAC,kBAAkB,GAAG,UAAU,CAAC;YAC1C,CAAC,MAAM,CAAC;gBACJ,IAAI,OAAO,UAAU,CAAC,kBAAkB,KAAK,QAAQ,EAAE,CAAC;oBACpD,MAAM,kBAAkB,kLAAG,cAAW,CAAC,KAAK,CAAC,UAAU,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;oBAC5F,KAAK,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;gBAClD,CAAC,MAAM,IAAK,UAAU,CAAC,kBAA6B,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACpE,MAAM,iBAAiB,GAAG,kLAAI,eAAW,CACrC,CAAC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,kBAAkB,EACpG,KAAK,EACL,UAAU,CAAC,iCAAiC,CAC/C,CAAC;oBACF,IAAI,UAAU,CAAC,2BAA2B,EAAE,CAAC;wBACzC,iBAAiB,CAAC,SAAS,GAAG,UAAU,CAAC,2BAA2B,CAAC;oBACzE,CAAC;oBACD,KAAK,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;gBACjD,CAAC,MAAM,CAAC;oBACJ,MAAM,WAAW,kLAAG,cAAW,CAAC,yBAAyB,CACrD,CAAC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,UAAU,CAAC,kBAAkB,EACpG,KAAK,EACL,UAAU,CAAC,iCAAiC,CAC/C,CAAC;oBACF,IAAI,UAAU,CAAC,2BAA2B,EAAE,CAAC;wBACzC,WAAW,CAAC,SAAS,GAAG,UAAU,CAAC,2BAA2B,CAAC;oBACnE,CAAC;oBACD,KAAK,CAAC,kBAAkB,GAAG,WAAW,CAAC;gBAC3C,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,mBAAmB,KAAK,IAAI,EAAE,CAAC;gBAC1C,MAAM,WAAW,GAAG,KAAK,CAAC,YAAY,KAAK,SAAS,IAAI,KAAK,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACrJ,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,IAAI,CAAC,CAAC;gBACxD,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;YAC7F,CAAC;YACD,SAAS,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC;QAC5D,CAAC;QAED,wBAAwB;QACxB,IAAI,UAAU,CAAC,oBAAoB,KAAK,SAAS,IAAI,UAAU,CAAC,oBAAoB,KAAK,IAAI,EAAE,CAAC;YAC5F,KAAK,CAAC,oBAAoB,GAAG,UAAU,CAAC,oBAAoB,CAAC;QACjE,CAAC;QAED,gBAAgB;QAChB,IAAI,UAAU,CAAC,YAAY,KAAK,SAAS,IAAI,UAAU,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAC5E,KAAK,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QACjD,CAAC;QAED,SAAS;QACT,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;YAChE,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBACvE,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC7C,MAAM,KAAK,6JAAG,QAAK,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBAC9C,IAAI,KAAK,EAAE,CAAC;oBACR,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;oBACjD,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC7B,KAAK,CAAC,gBAAgB,GAAG,SAAS,CAAC;oBACnC,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;oBACxC,GAAG,IAAI,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAClD,CAAC;YACL,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,IAAI,UAAU,CAAC,gBAAgB,KAAK,SAAS,IAAI,UAAU,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;YACpF,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBACjF,MAAM,qBAAqB,GAAG,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACjE,MAAM,eAAe,uKAAG,kBAAe,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBACrF,IAAI,eAAe,EAAE,CAAC;oBAClB,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;oBACjD,eAAe,CAAC,gBAAgB,GAAG,SAAS,CAAC;oBAC7C,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC;oBACnD,GAAG,IAAI,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAC5D,CAAC;YACL,CAAC;QACL,CAAC;QAED,aAAa;QACb,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YACxE,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC3E,MAAM,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACrD,MAAM,aAAa,mKAAG,WAAA,AAAQ,EAAC,mBAAmB,CAAC,CAAC;gBACpD,IAAI,aAAa,EAAE,CAAC;oBAChB,MAAM,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;oBACvD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACjC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACrC,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC5C,GAAG,IAAI,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBACtD,CAAC;YACL,CAAC;QACL,CAAC;QAED,YAAY;QACZ,IAAI,UAAU,CAAC,SAAS,KAAK,SAAS,IAAI,UAAU,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YACtE,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC1E,MAAM,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACnD,MAAM,GAAG,mKAAG,WAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC3D,IAAI,GAAG,EAAE,CAAC;oBACN,0BAA0B,CAAC,cAAc,CAAC,QAAQ,IAAI,cAAc,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;oBAC/E,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAC9B,GAAG,CAAC,gBAAgB,GAAG,SAAS,CAAC;oBACjC,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC3C,GAAG,IAAI,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBAE5C,WAAW;oBACX,MAAM,QAAQ,GAAG,GAAG,CAAC,iBAAiB,EAAE,CAAC;oBACzC,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;wBACvB,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;4BACtC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;4BAC3B,CAAC,CAAC,gBAAgB,GAAG,SAAS,CAAC;wBACnC,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,IAAI,UAAU,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YAChF,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC/E,MAAM,mBAAmB,GAAG,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC7D,MAAM,IAAI,wKAAG,gBAAa,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;gBAC1E,0BAA0B,CAAC,mBAAmB,CAAC,QAAQ,IAAI,mBAAmB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;gBAC1F,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;gBAElC,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChD,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAE7C,WAAW;gBACX,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC1C,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAE,CAAC;oBACvB,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;wBACtC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAC3B,CAAC,CAAC,gBAAgB,GAAG,SAAS,CAAC;oBACnC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,IAAI,UAAU,CAAC,mBAAmB,KAAK,SAAS,IAAI,UAAU,CAAC,mBAAmB,KAAK,IAAI,EAAE,CAAC;YAC1F,KAAK,MAAM,aAAa,IAAI,UAAU,CAAC,mBAAmB,CAAE,CAAC;gBACzD,MAAM,OAAO,yKAAG,qBAAkB,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;gBAC/D,oCAAoC,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC;gBACjE,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC5C,OAAO,CAAC,gBAAgB,GAAG,SAAS,CAAC;YACzC,CAAC;QACL,CAAC;QAED,YAAY;QACZ,IAAI,UAAU,CAAC,SAAS,KAAK,SAAS,IAAI,UAAU,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;YACtE,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC1E,MAAM,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACnD,MAAM,QAAQ,+JAAG,WAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;gBACvD,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACnC,QAAQ,CAAC,gBAAgB,GAAG,SAAS,CAAC;gBACtC,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3C,GAAG,IAAI,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACrD,CAAC;QACL,CAAC;QAED,aAAa;QACb,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;QACzC,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;YAClD,MAAM,aAAa,GAAG,IAAI,KAAK,EAAsB,CAAC;YAEtD,aAAa;YACb,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;YACzC,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;gBAClD,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;oBAChE,MAAM,gBAAgB,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;oBAC3C,aAAa,CAAC,IAAI,6JAAC,YAAQ,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;gBACzE,CAAC;YACL,CAAC;YAED,KAAK,MAAM,CAAC,IAAI,aAAa,CAAE,CAAC;gBAC5B,IAAI,CAAC,EAAE,CAAC;oBACJ,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,gBAAgB,GAAG,SAAS,CAAC;gBACnC,CAAC;YACL,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,IAAI,UAAU,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YAChF,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC/E,MAAM,mBAAmB,GAAG,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC7D,MAAM,IAAI,qKAAG,gBAAa,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBACtE,kBAAkB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;gBACxD,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;YACtC,CAAC;QACL,CAAC;QAED,SAAS;QACT,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;YAChE,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBACvE,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5C,MAAM,IAAI,4JAAiB,OAAI,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAClE,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;gBAC/C,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC5B,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;gBAClC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;oBACpB,KAAK,MAAM,QAAQ,IAAK,IAAa,CAAC,SAAS,CAAE,CAAC;wBAC9C,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAChC,QAAQ,CAAC,gBAAgB,GAAG,SAAS,CAAC;oBAC1C,CAAC;gBACL,CAAC;gBACD,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxC,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACjD,CAAC;QACL,CAAC;QAED,UAAU;QACV,IAAI,UAAU,CAAC,OAAO,KAAK,SAAS,IAAI,UAAU,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YAClE,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBACxE,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC/C,MAAM,MAAM,+JAAG,SAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBACjD,kBAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;gBACnD,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/B,MAAM,CAAC,gBAAgB,GAAG,SAAS,CAAC;gBACpC,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzC,GAAG,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACnD,CAAC;QACL,CAAC;QAED,gBAAgB;QAChB,IAAI,UAAU,CAAC,aAAa,KAAK,SAAS,IAAI,UAAU,CAAC,aAAa,KAAK,IAAI,EAAE,CAAC;YAC9E,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC9E,MAAM,iBAAiB,GAAG,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1D,MAAM,WAAW,0KAAG,cAAW,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBACzE,IAAI,WAAW,EAAE,CAAC;oBACd,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC1C,WAAW,CAAC,gBAAgB,GAAG,SAAS,CAAC;oBACzC,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC7C,GAAG,IAAI,QAAQ,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC7C,CAAC;YACL,CAAC;QACL,CAAC;QAED,mBAAmB;QACnB,IAAI,UAAU,CAAC,eAAe,KAAK,SAAS,IAAI,UAAU,CAAC,eAAe,KAAK,IAAI,IAAI,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YACvH,yDAAyD;YACzD,MAAM,OAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;YAC5C,4FAA4F;YAC5F,wFAAwF;YACxF,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACvD,6EAA6E;gBAC7E,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;oBACvC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;YACD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBAC/D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC/C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC7E,CAAC;YACL,CAAC;YACD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACvD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;oBACvC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC7D,CAAC;YACL,CAAC;YACD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,CAAE,CAAC;gBACxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;oBACxC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;YACD,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,EAAE,CAAE,CAAC;gBAClF,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;gBAChD,IAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,CAAE,CAAC;oBACrE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;wBAC7C,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;oBACzE,CAAC;gBACL,CAAC;YACL,CAAC;YACD,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBAChF,MAAM,oBAAoB,GAAG,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC/D,MAAM,cAAc,0KAAG,iBAAc,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAClF,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC/C,cAAc,CAAC,gBAAgB,GAAG,SAAS,CAAC;gBAC5C,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC;gBACjD,GAAG,IAAI,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC;QAED,UAAU;QACV,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;YAC5B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;gBACnF,MAAM,mBAAmB,GAAG,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBAC7D,MAAM,aAAa,sKAAG,gBAAa,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;gBAC/E,GAAG,IAAI,sBAAsB,GAAG,aAAa,CAAC,IAAI,CAAC;YACvD,CAAC;QACL,CAAC;QAED,6CAA6C;QAC7C,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YACnE,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,MAAM,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;gBACnC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBAC/F,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBAC/B,MAAM,CAAC,2BAA2B,GAAG,IAAI,CAAC;YAC9C,CAAC;QACL,CAAC;QAED,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YAClE,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,KAAK,IAAI,KAAK,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;gBAC3C,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBAC5F,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBAC9B,KAAK,CAAC,2BAA2B,GAAG,IAAI,CAAC;YAC7C,CAAC;QACL,CAAC;QAED,wDAAwD;QACxD,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YAC1E,MAAM,aAAa,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAClD,IAAI,aAAa,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;gBAC1C,aAAa,CAAC,MAAM,GAAG,UAAU,CAAC,aAAa,CAAC,gBAAgB,EAAE,aAAa,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBACpH,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBACtC,aAAa,CAAC,2BAA2B,GAAG,IAAI,CAAC;YACrD,CAAC;QACL,CAAC;QACD,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YAClE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;gBACjC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBACzF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;gBAC7B,IAAI,CAAC,2BAA2B,GAAG,IAAI,CAAC;YAC5C,CAAC;YACD,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBACzB,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YAClC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,cAAc,CAAE,CAAC;YAC1C,KAAK,MAAM,WAAW,IAAI,QAAQ,CAAC,6BAA6B,CAAE,CAAC;gBAC/D,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;YACjE,CAAC;YACD,QAAQ,CAAC,6BAA6B,GAAG,EAAE,CAAC;QAChD,CAAC;QAED,6BAA6B;QAC7B,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,CAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;gBAC7D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACnC,CAAC;QACL,CAAC;QAED,yCAAyC;QACzC,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,CAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,4BAA4B,KAAK,IAAI,EAAE,CAAC;gBAC7C,IAAI,CAAC,kBAAkB,GAAG,oCAAoC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;gBAClG,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;YAC7C,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YACrE,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;gBAC3B,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;oBACzB,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAE,CAAC;wBAChC,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;4BAC/B,MAAM,iBAAiB,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,uBAAuB,CAAkB,CAAC;4BAChG,IAAI,iBAAiB,EAAE,CAAC;gCACpB,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;4BAC9C,CAAC;4BACD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;wBACxC,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;YACpC,CAAC;QACL,CAAC;QAED,kCAAkC;QAClC,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YAClE,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,WAAW,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;gBAC7C,WAAW,CAAC,iBAAiB,EAAE,CAAC;gBAChC,WAAW,CAAC,YAAY,CAAC,iBAAiB,GAAG,IAAI,CAAC;YACtD,CAAC,MAAM,CAAC;gBACJ,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;QAED,iCAAiC;QACjC,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YAClE,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAClC,iBAAiB;YACjB,IAAI,KAAK,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,KAAK,CAAC,kBAAkB,CAAC,MAAM,EAAE,aAAa,EAAE,CAAE,CAAC;oBAC3F,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC;oBAEhF,IAAI,YAAY,EAAE,CAAC;wBACf,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC5C,CAAC;gBACL,CAAC;gBAED,KAAK,CAAC,kBAAkB,GAAG,EAAE,CAAC;YAClC,CAAC;YAED,iBAAiB;YACjB,IAAI,KAAK,CAAC,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1C,IAAK,IAAI,iBAAiB,GAAG,CAAC,EAAE,iBAAiB,GAAG,KAAK,CAAC,sBAAsB,CAAC,MAAM,EAAE,iBAAiB,EAAE,CAAE,CAAC;oBAC3G,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,CAAC,CAAC;oBAE5F,IAAI,gBAAgB,EAAE,CAAC;wBACnB,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBACpD,CAAC;gBACL,CAAC;gBAED,KAAK,CAAC,sBAAsB,GAAG,EAAE,CAAC;YACtC,CAAC;QACL,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAE,CAAC;YAC/B,CAAC,CAAC,eAAe,GAAG,EAAE,CAAC;QAC3B,CAAC;QAED,0MAAA,AAAK,EAAC,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAE7C,4DAA4D;QAC5D,IAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YAClE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;mLAC5B,gBAAa,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC5D,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;YACrC,CAAC;QACL,CAAC;QACD,IAAI,UAAU,CAAC,OAAO,KAAK,SAAS,IAAI,UAAU,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;+KAClE,gBAAa,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACL,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC;QACX,MAAM,GAAG,GAAG,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;QAC3F,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACtB,CAAC,MAAM,CAAC;qKACJ,SAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChB,MAAM,GAAG,CAAC;QACd,CAAC;IACL,CAAC,QAAS,CAAC;QACP,kBAAkB,GAAG,CAAA,CAAE,CAAC;QACxB,0BAA0B,GAAG,CAAA,CAAE,CAAC;QAChC,oCAAoC,GAAG,CAAA,CAAE,CAAC;QAE1C,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,SAAS,CAAC,kBAAkB,EAAE,CAAC;QACnC,CAAC;QACD,IAAI,GAAG,KAAK,IAAI,qKAAI,cAAW,CAAC,YAAY,sKAAK,cAAW,CAAC,UAAU,EAAE,CAAC;qKACtE,SAAM,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,kKAAC,cAAW,CAAC,YAAY,qKAAK,eAAW,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjK,CAAC;IACL,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;iKAEF,cAAW,CAAC,cAAc,CAAC;IACvB,IAAI,EAAE,YAAY;IAClB,UAAU,EAAE,UAAU;IACtB,aAAa,EAAE,CAAC,IAAY,EAAE,EAAE;QAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACjC,iDAAiD;YACjD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,UAAU,EAAE,CACR,WAAgB,EAChB,KAAY,EACZ,IAAS,EACT,OAAe,EACf,MAAsB,EACtB,eAAkC,EAClC,SAAqB,EACrB,OAAoD,EAC7C,EAAE;QACT,oGAAoG;QACpG,4EAA4E;QAC5E,uGAAuG;QACvG,8DAA8D;QAC9D,IAAI,GAAG,GAAG,kCAAkC,CAAC;QAC7C,IAAI,CAAC;YACD,kCAAkC;YAClC,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClC,GAAG,GAAG,EAAE,CAAC;YACT,MAAM,WAAW,oKAAG,cAAW,CAAC,YAAY,KAAK,+KAAW,CAAC,gBAAgB,CAAC;YAC9E,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,WAAW,GAAG,IAAI,CAAC;YACvB,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;gBACrC,WAAW,GAAG;oBAAC,WAAW;iBAAC,CAAC;YAChC,CAAC;YAED,MAAM,YAAY,GAAa,EAAE,CAAC;YAClC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAgB,CAAC;YAElD,6HAA6H;YAC7H,MAAM,oBAAoB,GAAG,EAAE,CAAC;YAChC,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,IAAI,UAAU,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;gBAChF,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;oBACnF,MAAM,uBAAuB,GAAG,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;oBACjE,MAAM,mBAAmB,qKAAG,gBAAa,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;oBACzF,oBAAoB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;oBAC/C,iBAAiB,CAAC,GAAG,CAAC,mBAAmB,CAAC,sBAAuB,EAAE,mBAAmB,CAAC,CAAC;oBACxF,mBAAmB,CAAC,sBAAsB,GAAG,IAAI,CAAC;gBACtD,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;gBAChE,MAAM,kBAAkB,GAAG,EAAE,CAAC;gBAC9B,MAAM,kBAAkB,GAAa,EAAE,CAAC;gBACxC,MAAM,wBAAwB,GAAa,EAAE,CAAC;gBAC9C,MAAM,2BAA2B,GAAa,EAAE,CAAC;gBACjD,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;oBAC3E,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAE5C,IAAI,WAAW,KAAK,IAAI,IAAI,cAAc,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC,EAAE,CAAC;wBAChF,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;4BACvB,oCAAoC;4BACpC,OAAO,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC7D,CAAC;wBAED,WAAW;wBACX,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;4BACxE,mCAAmC;4BACnC,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gCACxE,mDAAmD;gCACnD,IAAI,KAAK,GAAY,KAAK,CAAC;gCAC3B,MAAM,KAAK,GAAG;oCAAC,OAAO;oCAAE,SAAS;oCAAE,WAAW;oCAAE,SAAS;oCAAE,SAAS;oCAAE,QAAQ;oCAAE,YAAY;oCAAE,YAAY;iCAAC,CAAC;gCAC5G,KAAK,MAAM,YAAY,IAAI,KAAK,CAAE,CAAC;oCAC/B,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;wCAC9F,SAAS;oCACb,CAAC;oCACD,MAAM,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;oCACjD,KAAK,MAAM,kBAAkB,IAAI,IAAI,CAAE,CAAC;wCACpC,IAAI,kBAAkB,CAAC,EAAE,KAAK,UAAU,CAAC,UAAU,EAAE,CAAC;4CAClD,OAAQ,YAAY,EAAE,CAAC;gDACnB,KAAK,YAAY;iNACb,WAAQ,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;oDACnD,MAAM;4CACd,CAAC;4CACD,KAAK,GAAG,IAAI,CAAC;4CACb,MAAM;wCACV,CAAC;oCACL,CAAC;oCAED,IAAI,KAAK,EAAE,CAAC;wCACR,MAAM;oCACV,CAAC;gCACL,CAAC;gCACD,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;6LAClB,SAAM,CAAC,IAAI,CAAC,8BAA8B,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;gCAChE,CAAC;4BACL,CAAC;wBACL,CAAC;wBAED,aAAa;wBACb,IAAI,UAAU,CAAC,gBAAgB,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;4BACvD,qGAAqG;4BACrG,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,kBAAkB,CAAC;4BAClG,IAAI,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAgB,IAAI,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;4BACvG,IAAI,aAAa,KAAK,KAAK,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,IAAI,UAAU,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;gCAC3G,yCAAyC;gCACzC,MAAM,eAAe,GAAG,CAAC,QAAgB,EAAE,SAA2C,EAAE,EAAE;oCACtF,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oCAC7B,MAAM,GAAG,GAAG,wBAAwB,CAAC,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;oCAC5E,IAAI,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;wCACtB,0BAA0B,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,IAAI,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;wCAChG,GAAG,IAAI,eAAe,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oCAChE,CAAC;gCACL,CAAC,CAAC;gCACF,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,aAAa,GAAG,aAAa,EAAE,aAAa,EAAE,CAAE,CAAC;oCAC3H,MAAM,mBAAmB,GAAG,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;oCACrE,IACI,AAAC,UAAU,CAAC,gBAAgB,IAAI,mBAAmB,CAAC,QAAQ,KAAK,UAAU,CAAC,gBAAgB,CAAC,GAC7F,mBAAmB,CAAC,EAAE,KAAK,UAAU,CAAC,UAAU,EAClD,CAAC;wCACC,IAAI,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;4CACzC,+DAA+D;4CAC/D,KAAK,MAAM,QAAQ,IAAI,mBAAmB,CAAC,kBAAkB,CAAE,CAAC;gDAC5D,eAAe,CAAC,QAAQ,EAAE,CAAC,cAAc,EAAE,CAAG,CAAD,aAAe,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;4CACxF,CAAC;wCACL,CAAC,MAAM,CAAC;4CACJ,uCAAuC;4CACvC,KAAK,MAAM,QAAQ,IAAI,mBAAmB,CAAC,SAAS,CAAE,CAAC;gDACnD,eAAe,CAAC,QAAQ,EAAE,CAAC,cAAc,EAAE,CAAG,CAAD,aAAe,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;4CAClF,CAAC;wCACL,CAAC;wCACD,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,IAAI,mBAAmB,CAAC,EAAE,CAAC,CAAC;wCAC3E,MAAM,IAAI,wKAAG,gBAAa,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;wCAC1E,0BAA0B,CAAC,mBAAmB,CAAC,QAAQ,IAAI,mBAAmB,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;wCAC1F,IAAI,IAAI,EAAE,CAAC;4CACP,aAAa,GAAG,IAAI,CAAC;4CACrB,GAAG,IAAI,qBAAqB,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;wCAC9D,CAAC;wCACD,MAAM;oCACV,CAAC;gCACL,CAAC;4BACL,CAAC;4BAED,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;gCAC1B,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;gCACzE,MAAM,GAAG,GAAG,wBAAwB,CAChC,CAAC,cAAc,EAAE,CACb,AAAC,CADc,SACJ,CAAC,gBAAgB,IAAI,cAAc,CAAC,QAAQ,KAAK,UAAU,CAAC,gBAAgB,CAAC,GAAI,cAAc,CAAC,EAAE,KAAK,UAAU,CAAC,UAAU,EAC3I,UAAU,EACV,KAAK,EACL,OAAO,CACV,CAAC;gCACF,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;6LACxB,SAAM,CAAC,IAAI,CAAC,8BAA8B,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;gCAChE,CAAC,MAAM,CAAC;oCACJ,0BAA0B,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,IAAI,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;oCAChG,GAAG,IAAI,eAAe,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gCAChE,CAAC;4BACL,CAAC;wBACL,CAAC;wBAED,aAAa;wBACb,IACI,UAAU,CAAC,UAAU,KAAK,IAAI,IAC9B,UAAU,CAAC,UAAU,KAAK,SAAS,IACnC,UAAU,CAAC,UAAU,KAAK,CAAC,CAAC,IAC5B,UAAU,CAAC,SAAS,KAAK,SAAS,IAClC,UAAU,CAAC,SAAS,KAAK,IAAI,EAC/B,CAAC;4BACC,MAAM,qBAAqB,GAAG,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;4BACrF,IAAI,CAAC,qBAAqB,EAAE,CAAC;gCACzB,IAAK,IAAI,aAAa,GAAG,CAAC,EAAE,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,aAAa,GAAG,aAAa,EAAE,aAAa,EAAE,CAAE,CAAC;oCACtH,MAAM,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;oCAC3D,IAAI,cAAc,CAAC,EAAE,KAAK,UAAU,CAAC,UAAU,EAAE,CAAC;wCAC9C,MAAM,QAAQ,+JAAG,WAAQ,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;wCACvD,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wCACzB,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;wCAC3C,GAAG,IAAI,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oCAC5D,CAAC;gCACL,CAAC;4BACL,CAAC;wBACL,CAAC;wBAED,kBAAkB;wBAClB,IAAI,UAAU,CAAC,oBAAoB,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,mBAAmB,KAAK,SAAS,IAAI,UAAU,CAAC,mBAAmB,KAAK,IAAI,EAAE,CAAC;4BAClI,MAAM,+BAA+B,GAAG,2BAA2B,CAAC,OAAO,CAAC,UAAU,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC;4BAClH,IAAI,CAAC,+BAA+B,EAAE,CAAC;gCACnC,IAAK,IAAI,uBAAuB,GAAG,CAAC,EAAE,uBAAuB,GAAG,UAAU,CAAC,mBAAmB,CAAC,MAAM,EAAE,uBAAuB,EAAE,CAAE,CAAC;oCAC/H,MAAM,aAAa,GAAG,UAAU,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC;oCAC9E,IAAI,aAAa,CAAC,EAAE,KAAK,UAAU,CAAC,oBAAoB,EAAE,CAAC;wCACvD,MAAM,kBAAkB,yKAAG,qBAAkB,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;wCAC1E,oCAAoC,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC;wCAC5E,2BAA2B,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;wCACnD,GAAG,IAAI,wBAAwB,GAAG,kBAAkB,CAAC,QAAQ,EAAE,CAAC;oCACpE,CAAC;gCACL,CAAC;4BACL,CAAC;wBACL,CAAC;wBAED,MAAM,IAAI,4JAAG,OAAI,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;wBACpD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClB,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAuB,EAAE,IAAI,CAAC,CAAC;wBAC1D,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;wBACnC,GAAG,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;oBACpD,CAAC;gBACL,CAAC;gBAED,gCAAgC;gBAChC,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,cAAc,CAAE,CAAC;oBAC1C,KAAK,MAAM,WAAW,IAAI,QAAQ,CAAC,6BAA6B,CAAE,CAAC;wBAC/D,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;oBACjE,CAAC;oBACD,QAAQ,CAAC,6BAA6B,GAAG,EAAE,CAAC;gBAChD,CAAC;gBAED,6BAA6B;gBAC7B,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,CAAE,CAAC;oBAC9B,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;wBACnC,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;wBAC7D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;oBACnC,CAAC;gBACL,CAAC;gBAED,yCAAyC;gBACzC,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,CAAE,CAAC;oBAC9B,IAAI,IAAI,CAAC,4BAA4B,KAAK,IAAI,EAAE,CAAC;wBAC7C,IAAI,CAAC,kBAAkB,GAAG,oCAAoC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;wBAClG,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC;oBAC7C,CAAC;gBACL,CAAC;gBAED,8BAA8B;gBAC9B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;oBAC9E,MAAM,aAAa,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;oBAClD,IAAI,aAAa,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;wBAC1C,IAAI,MAAM,GAAG,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAAC;wBACrF,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BAClB,MAAM,GAAG,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;wBACpE,CAAC;wBACD,IAAI,UAAU,GAAG,MAAM,CAAC;wBACxB,IAAI,aAAa,CAAC,2BAA2B,EAAE,CAAC;4BAC5C,UAAU,GAAI,MAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC,CAAC;4BAC7F,aAAa,CAAC,2BAA2B,GAAG,IAAI,CAAC;wBACrD,CAAC;wBACD,aAAa,CAAC,MAAM,GAAG,UAAU,CAAC;wBAClC,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC;oBAC1C,CAAC;gBACL,CAAC;gBACD,IAAI,WAAyB,CAAC;gBAC9B,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;oBACtE,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAClC,IAAI,WAAW,CAAC,gBAAgB,EAAE,CAAC;wBAC/B,IAAI,MAAM,GAAG,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAAC;wBACnF,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;4BAClB,MAAM,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;wBAClE,CAAC;wBACD,IAAI,UAAU,GAAG,MAAM,CAAC;wBACxB,IAAI,WAAW,CAAC,2BAA2B,EAAE,CAAC;4BAC1C,UAAU,GAAI,MAAe,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,2BAA2B,CAAC,CAAC,CAAC;4BAC3F,WAAW,CAAC,2BAA2B,GAAG,IAAI,CAAC;wBACnD,CAAC;wBACD,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC;wBAChC,WAAW,CAAC,gBAAgB,GAAG,IAAI,CAAC;oBACxC,CAAC;oBACD,IAAI,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;wBAChC,gBAAgB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;oBACzC,CAAC;gBACL,CAAC;gBAED,gCAAgC;gBAChC,KAAK,MAAM,aAAa,IAAI,oBAAoB,CAAE,CAAC;oBAC/C,MAAM,WAAW,GAAG,aAAa,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;oBACxD,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;wBACtB,aAAa,CAAC,OAAO,EAAE,CAAC;oBAC5B,CAAC;gBACL,CAAC;gBAED,gCAAgC;gBAChC,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;oBACzE,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBACxC,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;wBAC3B,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;4BACzB,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAE,CAAC;gCAChC,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;oCAC/B,MAAM,iBAAiB,GAAG,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,uBAAuB,CAAkB,CAAC;oCAChG,IAAI,iBAAiB,EAAE,CAAC;wCACpB,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;oCAC9C,CAAC;oCACD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gCACxC,CAAC;4BACL,CAAC;wBACL,CAAC;wBAED,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC;oBACpC,CAAC;gBACL,CAAC;gBAED,8CAA8C;gBAC9C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;oBACtE,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAClC,IAAI,WAAW,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;wBAC7C,WAAW,CAAC,iBAAiB,EAAE,CAAC;wBAChC,WAAW,CAAC,YAAY,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBACtD,CAAC,MAAM,CAAC;wBACJ,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBACzC,CAAC;gBACL,CAAC;YACL,CAAC;YAED,YAAY;YACZ,IAAI,UAAU,CAAC,eAAe,KAAK,SAAS,IAAI,UAAU,CAAC,eAAe,KAAK,IAAI,EAAE,CAAC;gBAClF,MAAM,MAAM,qMAAG,sBAAA,AAAmB,2JAAC,0BAAuB,CAAC,mBAAmB,CAAC,CAAC;gBAChF,IAAI,MAAM,EAAE,CAAC;oBACT,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;wBACpF,MAAM,oBAAoB,GAAG,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;wBAC/D,IAAI,YAAY,CAAC,OAAO,CAAC,oBAAoB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;4BAC9D,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;wBACvE,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,UAAU,CAAE,CAAC;gBAC/B,CAAC,CAAC,eAAe,GAAG,EAAE,CAAC;YAC3B,CAAC;YAED,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC;YACX,MAAM,GAAG,GAAG,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;YAC3F,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACtB,CAAC,MAAM,CAAC;yKACJ,SAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAChB,MAAM,GAAG,CAAC;YACd,CAAC;QACL,CAAC,QAAS,CAAC;YACP,IAAI,GAAG,KAAK,IAAI,qKAAI,cAAW,CAAC,YAAY,sKAAK,cAAW,CAAC,UAAU,EAAE,CAAC;yKACtE,SAAM,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,kKAAC,cAAW,CAAC,YAAY,sKAAK,cAAW,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjK,CAAC;YACD,0BAA0B,GAAG,CAAA,CAAE,CAAC;YAChC,oCAAoC,GAAG,CAAA,CAAE,CAAC;QAC9C,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,IAAI,EAAE,CAAC,KAAY,EAAE,IAAY,EAAE,OAAe,EAAE,OAAoD,EAAW,EAAE;QACjH,oGAAoG;QACpG,4EAA4E;QAC5E,uGAAuG;QACvG,8DAA8D;QAC9D,IAAI,GAAG,GAAG,mCAAmC,CAAC;QAC9C,IAAI,CAAC;YACD,kCAAkC;YAClC,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClC,GAAG,GAAG,EAAE,CAAC;YAET,QAAQ;YACR,IAAI,UAAU,CAAC,wBAAwB,KAAK,SAAS,IAAI,UAAU,CAAC,wBAAwB,KAAK,IAAI,EAAE,CAAC;gBACpG,KAAK,CAAC,wBAAwB,GAAG,UAAU,CAAC,wBAAwB,IAAI,kKAAC,cAAW,CAAC,mCAAmC,CAAC;YAC7H,CAAC;YACD,IAAI,UAAU,CAAC,SAAS,KAAK,SAAS,IAAI,UAAU,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;gBACtE,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;YAC3C,CAAC;YACD,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBACxE,KAAK,CAAC,UAAU,oKAAG,SAAM,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;YAC/D,CAAC;YACD,IAAI,UAAU,CAAC,YAAY,KAAK,SAAS,IAAI,UAAU,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;gBAC5E,KAAK,CAAC,YAAY,oKAAG,SAAM,CAAC,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;YACnE,CAAC;YACD,IAAI,UAAU,CAAC,OAAO,KAAK,SAAS,IAAI,UAAU,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;gBAClE,KAAK,CAAC,OAAO,qKAAG,UAAO,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YAC1D,CAAC;YAED,IAAI,UAAU,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;gBAChD,KAAK,CAAC,oBAAoB,GAAG,CAAC,CAAC,UAAU,CAAC,oBAAoB,CAAC;YACnE,CAAC;YAED,MAAM;YACN,IAAI,UAAU,CAAC,OAAO,KAAK,SAAS,IAAI,UAAU,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;gBAClE,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;YACvC,CAAC;YACD,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACpE,KAAK,CAAC,QAAQ,mKAAG,UAAM,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACpE,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;YACzC,CAAC;YACD,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;gBAChE,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;YACrC,CAAC;YACD,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,IAAI,UAAU,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;gBACxE,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;YAC7C,CAAC;YACD,GAAG,IAAI,yBAAyB,CAAC;YACjC,OAAQ,KAAK,CAAC,OAAO,EAAE,CAAC;gBACpB,KAAK,CAAC;oBACF,GAAG,IAAI,QAAQ,CAAC;oBAChB,MAAM;gBACV,4CAA4C;gBAC5C,KAAK,CAAC;oBACF,GAAG,IAAI,OAAO,CAAC;oBACf,MAAM;gBACV,KAAK,CAAC;oBACF,GAAG,IAAI,QAAQ,CAAC;oBAChB,MAAM;gBACV,KAAK,CAAC;oBACF,GAAG,IAAI,UAAU,CAAC;oBAClB,MAAM;YACd,CAAC;YAED,SAAS;YACT,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;gBAC5B,IAAI,aAAa,CAAC;gBAClB,IAAI,UAAU,CAAC,aAAa,KAAK,QAAQ,IAAI,UAAU,CAAC,aAAa,0LAAK,iBAAc,CAAC,IAAI,EAAE,CAAC;oBAC5F,aAAa,GAAG,yLAAI,iBAAc,CAAC,SAAS,EAAE,SAAS,EAAE,8BAA8B,CAAC,2BAA2B,CAAC,CAAC;gBACzH,CAAC,MAAM,IAAI,UAAU,CAAC,aAAa,KAAK,MAAM,IAAI,UAAU,CAAC,aAAa,wLAAK,eAAY,CAAC,IAAI,EAAE,CAAC;oBAC/F,aAAa,GAAG,uLAAI,eAAY,CAAC,SAAS,EAAE,8BAA8B,CAAC,2BAA2B,CAAC,CAAC;gBAC5G,CAAC,MAAM,IAAI,UAAU,CAAC,aAAa,KAAK,MAAM,IAAI,UAAU,CAAC,aAAa,wLAAK,eAAY,CAAC,IAAI,EAAE,CAAC;oBAC/F,aAAa,GAAG,uLAAI,eAAY,CAAC,SAAS,EAAE,8BAA8B,CAAC,2BAA2B,EAAE,SAAS,CAAC,CAAC;gBACvH,CAAC;gBACD,GAAG,GAAG,mBAAmB,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC;gBAC1G,gDAAgD;gBAChD,MAAM,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,mKAAC,UAAO,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,mKAAC,UAAO,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACpK,KAAK,CAAC,aAAa,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;YACvD,CAAC;YAED,WAAW;YACX,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,IAAI,UAAU,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACpE,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;YACzC,CAAC;YAED,oDAAoD;YACpD,IAAI,UAAU,CAAC,iBAAiB,KAAK,SAAS,IAAI,UAAU,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;gBACtF,KAAK,CAAC,iBAAiB,GAAG,UAAU,CAAC,iBAAiB,CAAC;YAC3D,CAAC;YAED,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAC1E,IAAI,CAAC,SAAS,EAAE,CAAC;gBACb,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBACzB,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,EAAE,UAAU,CAAC,eAAe,EAAE,UAAU,CAAC,gBAAgB,IAAI,GAAG,CAAC,CAAC;YACtJ,CAAC;YAED,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,IAAI,UAAU,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;gBAChF,KAAK,CAAC,mBAAmB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;YACzD,CAAC;YAED,SAAS;YACT,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,OAAO,GAAG,EAAE,CAAC;YACX,MAAM,GAAG,GAAG,YAAY,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;YAC5F,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YACtB,CAAC,MAAM,CAAC;wKACJ,UAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAChB,MAAM,GAAG,CAAC;YACd,CAAC;QACL,CAAC,QAAS,CAAC;YACP,IAAI,GAAG,KAAK,IAAI,qKAAI,cAAW,CAAC,YAAY,KAAK,+KAAW,CAAC,UAAU,EAAE,CAAC;yKACtE,SAAM,CAAC,GAAG,CAAC,YAAY,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,kKAAC,cAAW,CAAC,YAAY,sKAAK,cAAW,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClK,CAAC;QACL,CAAC;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IACD,kBAAkB,EAAE,CAAC,KAAY,EAAE,IAAY,EAAE,OAAe,EAAE,OAAoD,EAAkB,EAAE;QACtI,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACpE,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 2352, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Loading/Plugins/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Loading/Plugins/index.ts"], "sourcesContent": ["export * from \"./babylonFileLoader\";\r\nexport * from \"./babylonFileParser.function\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,qBAAqB,CAAC;AACpC,cAAc,8BAA8B,CAAC", "debugId": null}}, {"offset": {"line": 2369, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Loading/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Loading/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-restricted-imports */\r\nexport * from \"./loadingScreen\";\r\nexport * from \"./Plugins/index\";\r\nexport * from \"./sceneLoader\";\r\nexport * from \"./sceneLoaderFlags\";\r\n"], "names": [], "mappings": "AAAA,2DAAA,EAA6D;AAC7D,cAAc,iBAAiB,CAAC;AAChC,cAAc,iBAAiB,CAAC;AAChC,cAAc,eAAe,CAAC;AAC9B,cAAc,oBAAoB,CAAC", "debugId": null}}]}