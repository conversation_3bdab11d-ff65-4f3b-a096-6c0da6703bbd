{"version": 3, "file": "nativeInterfaces.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Native/nativeInterfaces.ts"], "names": [], "mappings": "", "sourcesContent": ["/* eslint-disable @typescript-eslint/naming-convention */\r\nimport type { DeviceType } from \"../../DeviceInput/InputDevices/deviceEnums\";\r\nimport type { IDeviceInputSystem } from \"../../DeviceInput/inputInterfaces\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { ICanvas, IImage, IPath2D } from \"../ICanvas\";\r\nimport type { NativeData, NativeDataStream } from \"./nativeDataStream\";\r\n\r\nexport type NativeTexture = NativeData;\r\nexport type NativeFramebuffer = NativeData;\r\nexport type NativeVertexArrayObject = NativeData;\r\nexport type NativeProgram = NativeData;\r\nexport type NativeUniform = NativeData;\r\n\r\n/** @internal */\r\nexport type NativeFrameStats = {\r\n    /** @internal */\r\n    gpuTimeNs: number;\r\n};\r\n\r\n/** @internal */\r\nexport interface INativeEngine {\r\n    dispose(): void;\r\n\r\n    requestAnimationFrame(callback: () => void): void;\r\n    setDeviceLostCallback(callback: () => void): void;\r\n\r\n    createVertexArray(): NativeData;\r\n\r\n    createIndexBuffer(dataBuffer: ArrayBuffer, dataByteOffset: number, dataByteLength: number, is32Bits: boolean, dynamic: boolean): NativeData;\r\n    recordIndexBuffer(vertexArray: NativeData, indexBuffer: NativeData): void;\r\n    updateDynamicIndexBuffer(indexBuffer: NativeData, data: ArrayBuffer, dataByteOffset: number, dataByteLength: number, startIndex: number): void;\r\n\r\n    createVertexBuffer(dataBuffer: ArrayBuffer, dataByteOffset: number, dataByteLength: number, dynamic: boolean): NativeData;\r\n    recordVertexBuffer(\r\n        vertexArray: NativeData,\r\n        vertexBuffer: NativeData,\r\n        location: number,\r\n        byteOffset: number,\r\n        byteStride: number,\r\n        numElements: number,\r\n        type: number,\r\n        normalized: boolean,\r\n        instanceDivisor: number\r\n    ): void;\r\n    updateDynamicVertexBuffer(vertexBuffer: NativeData, dataBuffer: ArrayBuffer, dataByteOffset: number, dataByteLength: number, vertexByteOffset?: number): void;\r\n\r\n    createProgram(vertexShader: string, fragmentShader: string): NativeProgram;\r\n    createProgramAsync(vertexShader: string, fragmentShader: string, onSuccess: () => void, onError: (error: Error) => void): NativeProgram;\r\n    getUniforms(shaderProgram: NativeProgram, uniformsNames: string[]): WebGLUniformLocation[];\r\n    getAttributes(shaderProgram: NativeProgram, attributeNames: string[]): number[];\r\n\r\n    createTexture(): NativeTexture;\r\n    initializeTexture(texture: NativeTexture, width: number, height: number, hasMips: boolean, format: number, renderTarget: boolean, srgb: boolean, samples: number): void;\r\n    loadTexture(texture: NativeTexture, data: ArrayBufferView, generateMips: boolean, invertY: boolean, srgb: boolean, onSuccess: () => void, onError: () => void): void;\r\n    loadRawTexture(texture: NativeTexture, data: ArrayBufferView, width: number, height: number, format: number, generateMips: boolean, invertY: boolean): void;\r\n    loadRawTexture2DArray(\r\n        texture: NativeTexture,\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        depth: number,\r\n        format: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean\r\n    ): void;\r\n    loadCubeTexture(texture: NativeTexture, data: Array<ArrayBufferView>, generateMips: boolean, invertY: boolean, srgb: boolean, onSuccess: () => void, onError: () => void): void;\r\n    loadCubeTextureWithMips(texture: NativeTexture, data: Array<Array<ArrayBufferView>>, invertY: boolean, srgb: boolean, onSuccess: () => void, onError: () => void): void;\r\n    getTextureWidth(texture: NativeTexture): number;\r\n    getTextureHeight(texture: NativeTexture): number;\r\n    deleteTexture(texture: NativeTexture): void;\r\n    readTexture(\r\n        texture: NativeTexture,\r\n        mipLevel: number,\r\n        x: number,\r\n        y: number,\r\n        width: number,\r\n        height: number,\r\n        buffer: Nullable<ArrayBuffer>,\r\n        bufferOffset: number,\r\n        bufferLength: number\r\n    ): Promise<ArrayBuffer>;\r\n\r\n    createImageBitmap(data: ArrayBufferView | IImage): ImageBitmap;\r\n    resizeImageBitmap(image: ImageBitmap, bufferWidth: number, bufferHeight: number): Uint8Array;\r\n\r\n    createFrameBuffer(\r\n        texture: Nullable<NativeTexture>,\r\n        width: number,\r\n        height: number,\r\n        generateStencilBuffer: boolean,\r\n        generateDepthBuffer: boolean,\r\n        samples: number\r\n    ): NativeFramebuffer;\r\n\r\n    getRenderWidth(): number;\r\n    getRenderHeight(): number;\r\n\r\n    setHardwareScalingLevel(level: number): void;\r\n\r\n    setViewPort(x: number, y: number, width: number, height: number): void;\r\n\r\n    setCommandDataStream(dataStream: NativeDataStream): void;\r\n    submitCommands(): void;\r\n\r\n    populateFrameStats(stats: NativeFrameStats): void;\r\n}\r\n\r\n/** @internal */\r\ninterface INativeEngineInfo {\r\n    version: string;\r\n    nonFloatVertexBuffers: true;\r\n}\r\n\r\n/** @internal */\r\ninterface INativeEngineConstructor {\r\n    prototype: INativeEngine;\r\n    new (info: INativeEngineInfo): INativeEngine;\r\n\r\n    readonly PROTOCOL_VERSION: number;\r\n\r\n    readonly CAPS_LIMITS_MAX_TEXTURE_SIZE: number;\r\n    readonly CAPS_LIMITS_MAX_TEXTURE_LAYERS: number;\r\n\r\n    readonly TEXTURE_NEAREST_NEAREST: number;\r\n    readonly TEXTURE_LINEAR_LINEAR: number;\r\n    readonly TEXTURE_LINEAR_LINEAR_MIPLINEAR: number;\r\n    readonly TEXTURE_NEAREST_NEAREST_MIPNEAREST: number;\r\n    readonly TEXTURE_NEAREST_LINEAR_MIPNEAREST: number;\r\n    readonly TEXTURE_NEAREST_LINEAR_MIPLINEAR: number;\r\n    readonly TEXTURE_NEAREST_LINEAR: number;\r\n    readonly TEXTURE_NEAREST_NEAREST_MIPLINEAR: number;\r\n    readonly TEXTURE_LINEAR_NEAREST_MIPNEAREST: number;\r\n    readonly TEXTURE_LINEAR_NEAREST_MIPLINEAR: number;\r\n    readonly TEXTURE_LINEAR_LINEAR_MIPNEAREST: number;\r\n    readonly TEXTURE_LINEAR_NEAREST: number;\r\n\r\n    readonly DEPTH_TEST_LESS: number;\r\n    readonly DEPTH_TEST_LEQUAL: number;\r\n    readonly DEPTH_TEST_EQUAL: number;\r\n    readonly DEPTH_TEST_GEQUAL: number;\r\n    readonly DEPTH_TEST_GREATER: number;\r\n    readonly DEPTH_TEST_NOTEQUAL: number;\r\n    readonly DEPTH_TEST_NEVER: number;\r\n    readonly DEPTH_TEST_ALWAYS: number;\r\n\r\n    readonly ADDRESS_MODE_WRAP: number;\r\n    readonly ADDRESS_MODE_MIRROR: number;\r\n    readonly ADDRESS_MODE_CLAMP: number;\r\n    readonly ADDRESS_MODE_BORDER: number;\r\n    readonly ADDRESS_MODE_MIRROR_ONCE: number;\r\n\r\n    readonly TEXTURE_FORMAT_BC1: number;\r\n    readonly TEXTURE_FORMAT_BC2: number;\r\n    readonly TEXTURE_FORMAT_BC3: number;\r\n    readonly TEXTURE_FORMAT_BC4: number;\r\n    readonly TEXTURE_FORMAT_BC5: number;\r\n    readonly TEXTURE_FORMAT_BC6H: number;\r\n    readonly TEXTURE_FORMAT_BC7: number;\r\n    readonly TEXTURE_FORMAT_ETC1: number;\r\n    readonly TEXTURE_FORMAT_ETC2: number;\r\n    readonly TEXTURE_FORMAT_ETC2A: number;\r\n    readonly TEXTURE_FORMAT_ETC2A1: number;\r\n    readonly TEXTURE_FORMAT_PTC12: number;\r\n    readonly TEXTURE_FORMAT_PTC14: number;\r\n    readonly TEXTURE_FORMAT_PTC12A: number;\r\n    readonly TEXTURE_FORMAT_PTC14A: number;\r\n    readonly TEXTURE_FORMAT_PTC22: number;\r\n    readonly TEXTURE_FORMAT_PTC24: number;\r\n    readonly TEXTURE_FORMAT_ATC: number;\r\n    readonly TEXTURE_FORMAT_ATCE: number;\r\n    readonly TEXTURE_FORMAT_ATCI: number;\r\n    readonly TEXTURE_FORMAT_ASTC4x4: number;\r\n    readonly TEXTURE_FORMAT_ASTC5x4: number;\r\n    readonly TEXTURE_FORMAT_ASTC5x5: number;\r\n    readonly TEXTURE_FORMAT_ASTC6x5: number;\r\n    readonly TEXTURE_FORMAT_ASTC6x6: number;\r\n    readonly TEXTURE_FORMAT_ASTC8x5: number;\r\n    readonly TEXTURE_FORMAT_ASTC8x6: number;\r\n    readonly TEXTURE_FORMAT_ASTC8x8: number;\r\n    readonly TEXTURE_FORMAT_ASTC10x5: number;\r\n    readonly TEXTURE_FORMAT_ASTC10x6: number;\r\n    readonly TEXTURE_FORMAT_ASTC10x8: number;\r\n    readonly TEXTURE_FORMAT_ASTC10x10: number;\r\n    readonly TEXTURE_FORMAT_ASTC12x10: number;\r\n    readonly TEXTURE_FORMAT_ASTC12x12: number;\r\n\r\n    readonly TEXTURE_FORMAT_R1: number;\r\n    readonly TEXTURE_FORMAT_A8: number;\r\n    readonly TEXTURE_FORMAT_R8: number;\r\n    readonly TEXTURE_FORMAT_R8I: number;\r\n    readonly TEXTURE_FORMAT_R8U: number;\r\n    readonly TEXTURE_FORMAT_R8S: number;\r\n    readonly TEXTURE_FORMAT_R16: number;\r\n    readonly TEXTURE_FORMAT_R16I: number;\r\n    readonly TEXTURE_FORMAT_R16U: number;\r\n    readonly TEXTURE_FORMAT_R16F: number;\r\n    readonly TEXTURE_FORMAT_R16S: number;\r\n    readonly TEXTURE_FORMAT_R32I: number;\r\n    readonly TEXTURE_FORMAT_R32U: number;\r\n    readonly TEXTURE_FORMAT_R32F: number;\r\n    readonly TEXTURE_FORMAT_RG8: number;\r\n    readonly TEXTURE_FORMAT_RG8I: number;\r\n    readonly TEXTURE_FORMAT_RG8U: number;\r\n    readonly TEXTURE_FORMAT_RG8S: number;\r\n    readonly TEXTURE_FORMAT_RG16: number;\r\n    readonly TEXTURE_FORMAT_RG16I: number;\r\n    readonly TEXTURE_FORMAT_RG16U: number;\r\n    readonly TEXTURE_FORMAT_RG16F: number;\r\n    readonly TEXTURE_FORMAT_RG16S: number;\r\n    readonly TEXTURE_FORMAT_RG32I: number;\r\n    readonly TEXTURE_FORMAT_RG32U: number;\r\n    readonly TEXTURE_FORMAT_RG32F: number;\r\n    readonly TEXTURE_FORMAT_RGB8: number;\r\n    readonly TEXTURE_FORMAT_RGB8I: number;\r\n    readonly TEXTURE_FORMAT_RGB8U: number;\r\n    readonly TEXTURE_FORMAT_RGB8S: number;\r\n    readonly TEXTURE_FORMAT_RGB9E5F: number;\r\n    readonly TEXTURE_FORMAT_BGRA8: number;\r\n    readonly TEXTURE_FORMAT_RGBA8: number;\r\n    readonly TEXTURE_FORMAT_RGBA8I: number;\r\n    readonly TEXTURE_FORMAT_RGBA8U: number;\r\n    readonly TEXTURE_FORMAT_RGBA8S: number;\r\n    readonly TEXTURE_FORMAT_RGBA16: number;\r\n    readonly TEXTURE_FORMAT_RGBA16I: number;\r\n    readonly TEXTURE_FORMAT_RGBA16U: number;\r\n    readonly TEXTURE_FORMAT_RGBA16F: number;\r\n    readonly TEXTURE_FORMAT_RGBA16S: number;\r\n    readonly TEXTURE_FORMAT_RGBA32I: number;\r\n    readonly TEXTURE_FORMAT_RGBA32U: number;\r\n    readonly TEXTURE_FORMAT_RGBA32F: number;\r\n    readonly TEXTURE_FORMAT_B5G6R5: number;\r\n    readonly TEXTURE_FORMAT_R5G6B5: number;\r\n    readonly TEXTURE_FORMAT_BGRA4: number;\r\n    readonly TEXTURE_FORMAT_RGBA4: number;\r\n    readonly TEXTURE_FORMAT_BGR5A1: number;\r\n    readonly TEXTURE_FORMAT_RGB5A1: number;\r\n    readonly TEXTURE_FORMAT_RGB10A2: number;\r\n    readonly TEXTURE_FORMAT_RG11B10F: number;\r\n\r\n    readonly TEXTURE_FORMAT_D16: number;\r\n    readonly TEXTURE_FORMAT_D24: number;\r\n    readonly TEXTURE_FORMAT_D24S8: number;\r\n    readonly TEXTURE_FORMAT_D32: number;\r\n    readonly TEXTURE_FORMAT_D16F: number;\r\n    readonly TEXTURE_FORMAT_D24F: number;\r\n    readonly TEXTURE_FORMAT_D32F: number;\r\n    readonly TEXTURE_FORMAT_D0S8: number;\r\n\r\n    readonly ATTRIB_TYPE_INT8: number;\r\n    readonly ATTRIB_TYPE_UINT8: number;\r\n    readonly ATTRIB_TYPE_INT16: number;\r\n    readonly ATTRIB_TYPE_UINT16: number;\r\n    readonly ATTRIB_TYPE_FLOAT: number;\r\n\r\n    readonly ALPHA_DISABLE: number;\r\n    readonly ALPHA_ADD: number;\r\n    readonly ALPHA_COMBINE: number;\r\n    readonly ALPHA_SUBTRACT: number;\r\n    readonly ALPHA_MULTIPLY: number;\r\n    readonly ALPHA_MAXIMIZED: number;\r\n    readonly ALPHA_ONEONE: number;\r\n    readonly ALPHA_PREMULTIPLIED: number;\r\n    readonly ALPHA_PREMULTIPLIED_PORTERDUFF: number;\r\n    readonly ALPHA_INTERPOLATE: number;\r\n    readonly ALPHA_SCREENMODE: number;\r\n\r\n    readonly STENCIL_TEST_LESS: number;\r\n    readonly STENCIL_TEST_LEQUAL: number;\r\n    readonly STENCIL_TEST_EQUAL: number;\r\n    readonly STENCIL_TEST_GEQUAL: number;\r\n    readonly STENCIL_TEST_GREATER: number;\r\n    readonly STENCIL_TEST_NOTEQUAL: number;\r\n    readonly STENCIL_TEST_NEVER: number;\r\n    readonly STENCIL_TEST_ALWAYS: number;\r\n\r\n    readonly STENCIL_OP_FAIL_S_ZERO: number;\r\n    readonly STENCIL_OP_FAIL_S_KEEP: number;\r\n    readonly STENCIL_OP_FAIL_S_REPLACE: number;\r\n    readonly STENCIL_OP_FAIL_S_INCR: number;\r\n    readonly STENCIL_OP_FAIL_S_INCRSAT: number;\r\n    readonly STENCIL_OP_FAIL_S_DECR: number;\r\n    readonly STENCIL_OP_FAIL_S_DECRSAT: number;\r\n    readonly STENCIL_OP_FAIL_S_INVERT: number;\r\n\r\n    readonly STENCIL_OP_FAIL_Z_ZERO: number;\r\n    readonly STENCIL_OP_FAIL_Z_KEEP: number;\r\n    readonly STENCIL_OP_FAIL_Z_REPLACE: number;\r\n    readonly STENCIL_OP_FAIL_Z_INCR: number;\r\n    readonly STENCIL_OP_FAIL_Z_INCRSAT: number;\r\n    readonly STENCIL_OP_FAIL_Z_DECR: number;\r\n    readonly STENCIL_OP_FAIL_Z_DECRSAT: number;\r\n    readonly STENCIL_OP_FAIL_Z_INVERT: number;\r\n\r\n    readonly STENCIL_OP_PASS_Z_ZERO: number;\r\n    readonly STENCIL_OP_PASS_Z_KEEP: number;\r\n    readonly STENCIL_OP_PASS_Z_REPLACE: number;\r\n    readonly STENCIL_OP_PASS_Z_INCR: number;\r\n    readonly STENCIL_OP_PASS_Z_INCRSAT: number;\r\n    readonly STENCIL_OP_PASS_Z_DECR: number;\r\n    readonly STENCIL_OP_PASS_Z_DECRSAT: number;\r\n    readonly STENCIL_OP_PASS_Z_INVERT: number;\r\n\r\n    readonly COMMAND_DELETEVERTEXARRAY: NativeData;\r\n    readonly COMMAND_DELETEINDEXBUFFER: NativeData;\r\n    readonly COMMAND_DELETEVERTEXBUFFER: NativeData;\r\n    readonly COMMAND_SETPROGRAM: NativeData;\r\n    readonly COMMAND_SETMATRIX: NativeData;\r\n    readonly COMMAND_SETMATRIX3X3: NativeData;\r\n    readonly COMMAND_SETMATRIX2X2: NativeData;\r\n    readonly COMMAND_SETMATRICES: NativeData;\r\n    readonly COMMAND_SETINT: NativeData;\r\n    readonly COMMAND_SETINTARRAY: NativeData;\r\n    readonly COMMAND_SETINTARRAY2: NativeData;\r\n    readonly COMMAND_SETINTARRAY3: NativeData;\r\n    readonly COMMAND_SETINTARRAY4: NativeData;\r\n    readonly COMMAND_SETFLOATARRAY: NativeData;\r\n    readonly COMMAND_SETFLOATARRAY2: NativeData;\r\n    readonly COMMAND_SETFLOATARRAY3: NativeData;\r\n    readonly COMMAND_SETFLOATARRAY4: NativeData;\r\n    readonly COMMAND_SETTEXTURESAMPLING: NativeData;\r\n    readonly COMMAND_SETTEXTUREWRAPMODE: NativeData;\r\n    readonly COMMAND_SETTEXTUREANISOTROPICLEVEL: NativeData;\r\n    readonly COMMAND_SETTEXTURE: NativeData;\r\n    readonly COMMAND_UNSETTEXTURE: NativeData;\r\n    readonly COMMAND_DISCARDALLTEXTURES: NativeData;\r\n    readonly COMMAND_BINDVERTEXARRAY: NativeData;\r\n    readonly COMMAND_SETSTATE: NativeData;\r\n    readonly COMMAND_DELETEPROGRAM: NativeData;\r\n    readonly COMMAND_SETZOFFSET: NativeData;\r\n    readonly COMMAND_SETZOFFSETUNITS: NativeData;\r\n    readonly COMMAND_SETDEPTHTEST: NativeData;\r\n    readonly COMMAND_SETDEPTHWRITE: NativeData;\r\n    readonly COMMAND_SETCOLORWRITE: NativeData;\r\n    readonly COMMAND_SETBLENDMODE: NativeData;\r\n    readonly COMMAND_SETFLOAT: NativeData;\r\n    readonly COMMAND_SETFLOAT2: NativeData;\r\n    readonly COMMAND_SETFLOAT3: NativeData;\r\n    readonly COMMAND_SETFLOAT4: NativeData;\r\n    readonly COMMAND_BINDFRAMEBUFFER: NativeData;\r\n    readonly COMMAND_UNBINDFRAMEBUFFER: NativeData;\r\n    readonly COMMAND_DELETEFRAMEBUFFER: NativeData;\r\n    readonly COMMAND_DRAWINDEXED: NativeData;\r\n    readonly COMMAND_DRAWINDEXEDINSTANCED: NativeData;\r\n    readonly COMMAND_DRAW: NativeData;\r\n    readonly COMMAND_DRAWINSTANCED: NativeData;\r\n    readonly COMMAND_CLEAR: NativeData;\r\n    readonly COMMAND_SETSTENCIL: NativeData;\r\n    readonly COMMAND_SETVIEWPORT: NativeData;\r\n    readonly COMMAND_SETSCISSOR: NativeData;\r\n    readonly COMMAND_COPYTEXTURE: NativeData;\r\n}\r\n\r\n/** @internal */\r\nexport interface INativeCamera {\r\n    createVideo(constraints: MediaTrackConstraints): any;\r\n    updateVideoTexture(texture: Nullable<InternalTexture>, video: HTMLVideoElement, invertY: boolean): void;\r\n}\r\n\r\n/** @internal */\r\ninterface INativeCameraConstructor {\r\n    prototype: INativeCamera;\r\n    new (): INativeCamera;\r\n}\r\n\r\n/** @internal */\r\ninterface INativeCanvasConstructor {\r\n    prototype: ICanvas;\r\n    new (): ICanvas;\r\n\r\n    loadTTFAsync(fontName: string, buffer: ArrayBuffer): void;\r\n}\r\n\r\n/** @internal */\r\ninterface INativeImageConstructor {\r\n    prototype: IImage;\r\n    new (): IImage;\r\n}\r\n\r\n/** @internal */\r\ninterface INativePath2DConstructor {\r\n    prototype: IPath2D;\r\n    new (d?: string): IPath2D;\r\n}\r\n\r\n/** @internal */\r\ninterface IDeviceInputSystemConstructor {\r\n    prototype: IDeviceInputSystem;\r\n    new (\r\n        onDeviceConnected: (deviceType: DeviceType, deviceSlot: number) => void,\r\n        onDeviceDisconnected: (deviceType: DeviceType, deviceSlot: number) => void,\r\n        onInputChanged: (deviceType: DeviceType, deviceSlot: number, inputIndex: number, currentState: number) => void\r\n    ): IDeviceInputSystem;\r\n}\r\n\r\n/** @internal */\r\nexport interface INativeDataStream {\r\n    writeBuffer(buffer: ArrayBuffer, length: number): void;\r\n}\r\n\r\n/** @internal */\r\ninterface INativeDataStreamConstructor {\r\n    prototype: INativeDataStream;\r\n    new (requestFlushCallback: () => void): INativeDataStream;\r\n\r\n    readonly VALIDATION_ENABLED: boolean;\r\n    readonly VALIDATION_UINT_32: number;\r\n    readonly VALIDATION_INT_32: number;\r\n    readonly VALIDATION_FLOAT_32: number;\r\n    readonly VALIDATION_UINT_32_ARRAY: number;\r\n    readonly VALIDATION_INT_32_ARRAY: number;\r\n    readonly VALIDATION_FLOAT_32_ARRAY: number;\r\n    readonly VALIDATION_NATIVE_DATA: number;\r\n    readonly VALIDATION_BOOLEAN: number;\r\n}\r\n\r\n/** @internal */\r\nexport interface INative {\r\n    Engine: INativeEngineConstructor;\r\n    Camera: INativeCameraConstructor;\r\n    Canvas: INativeCanvasConstructor;\r\n    Image: INativeImageConstructor;\r\n    Path2D: INativePath2DConstructor;\r\n    XMLHttpRequest: any; // TODO: how to do this?\r\n    DeviceInputSystem: IDeviceInputSystemConstructor;\r\n    NativeDataStream: INativeDataStreamConstructor;\r\n}\r\n"]}