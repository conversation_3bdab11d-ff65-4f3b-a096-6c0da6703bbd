{"version": 3, "file": "runtimeAnimation.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Animations/runtimeAnimation.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAE9C,OAAO,EACH,SAAS,EACT,wBAAwB,EACxB,wBAAwB,EACxB,4BAA4B,EAC5B,sBAAsB,EACtB,yBAAyB,EACzB,yBAAyB,GAC5B,MAAM,aAAa,CAAC;AAMrB;;GAEG;AACH,MAAM,OAAO,gBAAgB;IAgHzB;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/C,CAAC;IAKD;;;;;;OAMG;IACH,YAAmB,MAAW,EAAE,SAAoB,EAAE,KAAY,EAAE,IAAgB;QAnK5E,YAAO,GAAG,IAAI,KAAK,EAAkB,CAAC;QAE9C;;WAEG;QACK,kBAAa,GAAW,CAAC,CAAC;QAiBlC;;WAEG;QACK,mBAAc,GAAG,IAAI,KAAK,EAAO,CAAC;QAE1C;;WAEG;QACK,wBAAmB,GAAkB,IAAI,CAAC;QAElD;;WAEG;QACK,kBAAa,GAA2B,EAAE,CAAC;QAEnD;;WAEG;QACK,qBAAgB,GAA2B,EAAE,CAAC;QAEtD;;WAEG;QACK,aAAQ,GAAG,KAAK,CAAC;QAEzB;;WAEG;QACK,oBAAe,GAAG,CAAC,CAAC;QAO5B;;WAEG;QACK,kBAAa,GAAkB,IAAI,CAAC;QASpC,yBAAoB,GAAkB,IAAI,CAAC;QAC3C,kBAAa,GAAkB,IAAI,CAAC;QAE5C;;WAEG;QACK,gBAAW,GAAW,EAAE,CAAC;QAEjC;;WAEG;QACK,YAAO,GAAG,GAAG,CAAC;QAEtB;;WAEG;QACK,yBAAoB,GAAG,CAAC,CAAC;QAEjC;;WAEG;QACK,yBAAoB,GAAW,CAAC,CAAC;QAEjC,mBAAc,GAAW,CAAC,CAAC;QAEnC;;WAEG;QACK,2BAAsB,GAAW,CAAC,CAAC;QASnC,mBAAc,GAAG,KAAK,CAAC;QAE/B,gBAAgB;QACT,0BAAqB,GAA4B,IAAI,CAAC;QAuDzD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExC,QAAQ;QACR,IAAI,CAAC,eAAe,GAAG;YACnB,GAAG,EAAE,CAAC;YACN,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,IAAI,CAAC,mBAAmB,EAAE;SACvC,CAAC;QAEF,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,KAAK,SAAS,CAAC,oBAAoB,EAAE,CAAC;YAC9D,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;QACnD,CAAC;QAED,SAAS;QACT,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QAEzD,wCAAwC;QACxC,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;YACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,OAAO,YAAY,KAAK,EAAE,CAAC;YAChC,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBAC/B,KAAK,EAAE,CAAC;YACZ,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;QAED,yBAAyB;QACzB,MAAM,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;QACrC,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,KAAK,MAAM,CAAC,IAAI,MAAM,EAAE,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;YAClC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAC7J,CAAC;IAEO,YAAY,CAAC,MAAW,EAAE,WAAW,GAAG,CAAC;QAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;QAE9D,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,IAAI,QAAQ,GAAG,MAAM,CAAC;YACtB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;gBACjE,MAAM,IAAI,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBACvC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC1B,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBACzB,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,uBAAuB,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrG,CAAC;YACL,CAAC;YAED,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACrE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC;QAChD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE,CAAC;YACnE,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,CAAC,WAAW,uBAAuB,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACjH,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,eAAe,GAAG,KAAK;QAChC,IAAI,eAAe,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,OAAO,YAAY,KAAK,EAAE,CAAC;gBAChC,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBAChC,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC;wBAC3C,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBAC9F,CAAC;oBACD,KAAK,EAAE,CAAC;gBACZ,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE,CAAC;oBACvC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACpF,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,SAAS;QACT,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;QACvC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,YAAiB,EAAE,MAAc;QAC7C,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;gBACvD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;YACpF,CAAC;YACD,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEO,kBAAkB,CAAC,WAAW,GAAG,CAAC;QACtC,IAAI,aAAkB,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAEhD,IAAI,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAC1D,YAAY;YACZ,aAAa,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAC5C,CAAC;aAAM,CAAC;YACJ,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;QAC7D,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;QACrD,CAAC;IACL,CAAC;IAEO,sCAAsC,CAAC,gBAAkC,EAAE,aAAkB;QACjG,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,MAAM,CAAC,mCAAmC,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAExE,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAChC,MAAM,CAAC,qBAAqB,GAAG,EAAE,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7D,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,GAAG;gBACxD,WAAW,EAAE,CAAC;gBACd,mBAAmB,EAAE,CAAC;gBACtB,UAAU,EAAE,EAAE;gBACd,kBAAkB,EAAE,EAAE;gBACtB,aAAa,EAAE,aAAa;aAC/B,CAAC;QACN,CAAC;QAED,IAAI,gBAAgB,CAAC,UAAU,EAAE,CAAC;YAC9B,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACpG,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,mBAAmB,IAAI,gBAAgB,CAAC,MAAM,CAAC;QAC7G,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5F,MAAM,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,WAAW,IAAI,gBAAgB,CAAC,MAAM,CAAC;QACrG,CAAC;IACL,CAAC;IAEO,SAAS,CAAC,MAAW,EAAE,WAAgB,EAAE,YAAiB,EAAE,MAAc,EAAE,WAAmB;QACnG,YAAY;QACZ,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;QAExC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,IAAI,GAAG,EAAE,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC5B,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAEpD,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;oBACtB,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;gBACrD,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC;gBAC7C,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC;gBAC7B,SAAS;gBACT,IAAI,SAAS,CAAC,oCAAoC,EAAE,CAAC;oBACjD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;wBACrB,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBAChH,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBAC5G,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;wBACrB,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBACvG,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBACnG,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAChH,CAAC;YAED,MAAM,aAAa,GAAG,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;YACtJ,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC;QAC1C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACtB,IAAI,YAAY,EAAE,KAAK,EAAE,CAAC;oBACtB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC;gBAC9C,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;gBACtC,CAAC;YACL,CAAC;iBAAM,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;gBACrC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YACtC,CAAC;QACL,CAAC;QAED,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,CAAC;YAClB,IAAI,CAAC,sCAAsC,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;QACxF,CAAC;aAAM,CAAC;YACJ,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,SAAS,CAAC,uCAAuC,EAAE,CAAC;gBACtF,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC9B,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;gBACjG,CAAC;qBAAM,CAAC;oBACJ,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;gBAC1F,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;YACvD,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,mBAAmB;QACvB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,QAAkB,CAAC;QACvE,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,KAAa,EAAE,MAAM,GAAG,CAAC,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACxB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC1B,CAAC;aAAM,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YAC7C,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QACxC,CAAC;QAED,iCAAiC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;gBACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;oBAC1B,6BAA6B;oBAC7B,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;gBACvD,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAE/E,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,aAAqB;QACpD,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,aAAa,CAAC,CAAC,GAAG,MAAM,CAAC;QAEjH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,sBAAsB,GAAG,gBAAgB,CAAC;IAC/E,CAAC;IAED;;;;;;;;;OASG;IACI,OAAO,CAAC,8BAAsC,EAAE,IAAY,EAAE,EAAU,EAAE,IAAa,EAAE,UAAkB,EAAE,MAAM,GAAG,CAAC,GAAG;QAC7H,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,CAAC;QACxD,IAAI,CAAC,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,WAAW,GAAG,IAAI,CAAC;QACvB,IAAI,YAAoB,CAAC;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC9B,eAAe;YACf,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjD,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;YAC1B,CAAC;YACD,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC7C,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;YACxB,CAAC;YAED,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC;YACvB,IAAI,WAAgB,CAAC;YAErB,sHAAsH;YACtH,IAAI,aAAa,GAAG,CAAC,8BAA8B,GAAG,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACpI,IAAI,cAAc,GAAG,CAAC,CAAC;YAEvB,sCAAsC;YACtC,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,MAAM,QAAQ,GAAG,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,SAAS,CAAC,sBAAsB,CAAC;YAC5F,IAAI,QAAQ,EAAE,CAAC;gBACX,MAAM,QAAQ,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,UAAU,CAAC;gBAErD,uBAAuB;gBACvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAEnC,0CAA0C;gBAC1C,aAAa,GAAG,YAAY,GAAG,UAAU,GAAG,IAAI,CAAC;gBAEjD,MAAM,SAAS,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;oBACpC,QAAQ,GAAG,IAAI,CAAC;gBACpB,CAAC;gBAED,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YACpC,CAAC;YAED,IAAI,CAAC,oBAAoB,GAAG,8BAA8B,CAAC;YAC3D,IAAI,CAAC,sBAAsB,GAAG,aAAa,CAAC;YAE5C,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,aAAa,IAAI,UAAU,IAAI,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBACrH,4DAA4D;gBAC5D,WAAW,GAAG,KAAK,CAAC;gBACpB,cAAc,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5D,CAAC;iBAAM,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC,aAAa,IAAI,UAAU,IAAI,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5H,WAAW,GAAG,KAAK,CAAC;gBACpB,cAAc,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5D,CAAC;iBAAM,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,SAAS,CAAC,uBAAuB,EAAE,CAAC;gBAC7E,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;oBACjC,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,CAAC,CAAC;oBACrC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,SAAS,CAAC,uBAAuB,CAAC,CAAC,yDAAyD;oBAC5H,MAAM,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBACrE,MAAM,OAAO,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;oBAEjE,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC3D,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;wBACzB,QAAQ;wBACR,KAAK,SAAS,CAAC,mBAAmB;4BAC9B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;4BACpD,MAAM;wBACV,aAAa;wBACb,KAAK,SAAS,CAAC,wBAAwB;4BACnC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV,UAAU;wBACV,KAAK,SAAS,CAAC,qBAAqB;4BAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV,UAAU;wBACV,KAAK,SAAS,CAAC,qBAAqB;4BAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV,OAAO;wBACP,KAAK,SAAS,CAAC,kBAAkB;4BAC7B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV,SAAS;wBACT,KAAK,SAAS,CAAC,oBAAoB;4BAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;4BAC5D,MAAM;wBACV;4BACI,MAAM;oBACd,CAAC;oBAED,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;gBAC/C,CAAC;gBAED,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;gBAClD,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC5B,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;oBACzB,QAAQ;oBACR,KAAK,SAAS,CAAC,mBAAmB;wBAC9B,WAAW,GAAG,CAAC,CAAC;wBAChB,MAAM;oBACV,aAAa;oBACb,KAAK,SAAS,CAAC,wBAAwB;wBACnC,WAAW,GAAG,4BAA4B,CAAC;wBAC3C,MAAM;oBACV,UAAU;oBACV,KAAK,SAAS,CAAC,qBAAqB;wBAChC,WAAW,GAAG,yBAAyB,CAAC;wBACxC,MAAM;oBACV,UAAU;oBACV,KAAK,SAAS,CAAC,qBAAqB;wBAChC,WAAW,GAAG,yBAAyB,CAAC;wBACxC,MAAM;oBACV,OAAO;oBACP,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,WAAW,GAAG,sBAAsB,CAAC;wBACrC,MAAM;oBACV,SAAS;oBACT,KAAK,SAAS,CAAC,oBAAoB;wBAC/B,WAAW,GAAG,wBAAwB,CAAC;wBACvC,MAAM;oBACV,KAAK,SAAS,CAAC,oBAAoB;wBAC/B,WAAW,GAAG,wBAAwB,CAAC;wBACvC,MAAM;gBACd,CAAC;YACL,CAAC;YAED,gBAAgB;YAEhB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACpC,4GAA4G;gBAC5G,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACrC,MAAM,mBAAmB,GAAG,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAClH,YAAY,GAAG,IAAI,GAAG,UAAU,GAAG,mBAAmB,CAAC;YAC3D,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC;oBACvE,YAAY,GAAG,WAAW,IAAI,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC9F,CAAC;qBAAM,CAAC;oBACJ,YAAY,GAAG,WAAW,IAAI,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9F,CAAC;YACL,CAAC;YAED,+BAA+B;YAC/B,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,EAAE,CAAC;gBAC1J,IAAI,CAAC,OAAO,EAAE,CAAC;gBAEf,iCAAiC;gBACjC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;oBACjD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;wBAC1B,wCAAwC;wBACxC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;oBACjC,CAAC;gBACL,CAAC;gBAED,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;YACnF,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;YAC5F,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,cAAc,CAAC;YACrD,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,WAAW,CAAC;QACnD,CAAC;aAAM,CAAC;YACJ,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC;YACvB,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC;YACvD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,WAAW,CAAC;YAC1F,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,cAAc,CAAC;YAChG,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,WAAW,CAAC;QAC9F,CAAC;QAED,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAEhF,YAAY;QACZ,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAEpC,eAAe;QACf,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;gBACjD,kGAAkG;gBAClG,mDAAmD;gBACnD,IACI,CAAC,UAAU,IAAI,CAAC,IAAI,YAAY,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC;oBACvF,CAAC,UAAU,GAAG,CAAC,IAAI,YAAY,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,EACxF,CAAC;oBACC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAChB,gDAAgD;wBAChD,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;4BACjB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;4BACxB,KAAK,EAAE,CAAC;wBACZ,CAAC;wBACD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;wBACpB,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;oBAC/B,CAAC,CAAC,wDAAwD;gBAC9D,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACzB,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Matrix } from \"../Maths/math.vector\";\r\nimport type { _IAnimationState } from \"./animation\";\r\nimport {\r\n    Animation,\r\n    _StaticOffsetValueColor3,\r\n    _StaticOffsetValueColor4,\r\n    _StaticOffsetValueQuaternion,\r\n    _StaticOffsetValueSize,\r\n    _StaticOffsetValueVector2,\r\n    _StaticOffsetValueVector3,\r\n} from \"./animation\";\r\nimport type { AnimationEvent } from \"./animationEvent\";\r\nimport type { Animatable } from \"./animatable\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { IAnimationKey } from \"./animationKey\";\r\n\r\n/**\r\n * Defines a runtime animation\r\n */\r\nexport class RuntimeAnimation {\r\n    private _events = new Array<AnimationEvent>();\r\n\r\n    /**\r\n     * The current frame of the runtime animation\r\n     */\r\n    private _currentFrame: number = 0;\r\n\r\n    /**\r\n     * The animation used by the runtime animation\r\n     */\r\n    public _animation: Animation;\r\n\r\n    /**\r\n     * The target of the runtime animation\r\n     */\r\n    private _target: any;\r\n\r\n    /**\r\n     * The initiating animatable\r\n     */\r\n    private _host: Animatable;\r\n\r\n    /**\r\n     * The original value of the runtime animation\r\n     */\r\n    private _originalValue = new Array<any>();\r\n\r\n    /**\r\n     * The original blend value of the runtime animation\r\n     */\r\n    private _originalBlendValue: Nullable<any> = null;\r\n\r\n    /**\r\n     * The offsets cache of the runtime animation\r\n     */\r\n    private _offsetsCache: { [key: string]: any } = {};\r\n\r\n    /**\r\n     * The high limits cache of the runtime animation\r\n     */\r\n    private _highLimitsCache: { [key: string]: any } = {};\r\n\r\n    /**\r\n     * Specifies if the runtime animation has been stopped\r\n     */\r\n    private _stopped = false;\r\n\r\n    /**\r\n     * The blending factor of the runtime animation\r\n     */\r\n    private _blendingFactor = 0;\r\n\r\n    /**\r\n     * The BabylonJS scene\r\n     */\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * The current value of the runtime animation\r\n     */\r\n    private _currentValue: Nullable<any> = null;\r\n\r\n    /** @internal */\r\n    public _animationState: _IAnimationState;\r\n\r\n    /**\r\n     * The active target of the runtime animation\r\n     */\r\n    private _activeTargets: any[];\r\n    private _currentActiveTarget: Nullable<any> = null;\r\n    private _directTarget: Nullable<any> = null;\r\n\r\n    /**\r\n     * The target path of the runtime animation\r\n     */\r\n    private _targetPath: string = \"\";\r\n\r\n    /**\r\n     * The weight of the runtime animation\r\n     */\r\n    private _weight = 1.0;\r\n\r\n    /**\r\n     * The absolute frame offset of the runtime animation\r\n     */\r\n    private _absoluteFrameOffset = 0;\r\n\r\n    /**\r\n     * The previous elapsed time (since start of animation) of the runtime animation\r\n     */\r\n    private _previousElapsedTime: number = 0;\r\n\r\n    private _yoyoDirection: number = 1;\r\n\r\n    /**\r\n     * The previous absolute frame of the runtime animation (meaning, without taking into account the from/to values, only the elapsed time and the fps)\r\n     */\r\n    private _previousAbsoluteFrame: number = 0;\r\n\r\n    private _enableBlending: boolean;\r\n\r\n    private _keys: IAnimationKey[];\r\n    private _minFrame: number;\r\n    private _maxFrame: number;\r\n    private _minValue: any;\r\n    private _maxValue: any;\r\n    private _targetIsArray = false;\r\n\r\n    /** @internal */\r\n    public _coreRuntimeAnimation: RuntimeAnimation | null = null;\r\n\r\n    /**\r\n     * Gets the current frame of the runtime animation\r\n     */\r\n    public get currentFrame(): number {\r\n        return this._currentFrame;\r\n    }\r\n\r\n    /**\r\n     * Gets the weight of the runtime animation\r\n     */\r\n    public get weight(): number {\r\n        return this._weight;\r\n    }\r\n\r\n    /**\r\n     * Gets the current value of the runtime animation\r\n     */\r\n    public get currentValue(): any {\r\n        return this._currentValue;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the target path of the runtime animation\r\n     */\r\n    public get targetPath(): string {\r\n        return this._targetPath;\r\n    }\r\n\r\n    /**\r\n     * Gets the actual target of the runtime animation\r\n     */\r\n    public get target(): any {\r\n        return this._currentActiveTarget;\r\n    }\r\n\r\n    /**\r\n     * Gets the additive state of the runtime animation\r\n     */\r\n    public get isAdditive(): boolean {\r\n        return this._host && this._host.isAdditive;\r\n    }\r\n\r\n    /** @internal */\r\n    public _onLoop: () => void;\r\n\r\n    /**\r\n     * Create a new RuntimeAnimation object\r\n     * @param target defines the target of the animation\r\n     * @param animation defines the source animation object\r\n     * @param scene defines the hosting scene\r\n     * @param host defines the initiating Animatable\r\n     */\r\n    public constructor(target: any, animation: Animation, scene: Scene, host: Animatable) {\r\n        this._animation = animation;\r\n        this._target = target;\r\n        this._scene = scene;\r\n        this._host = host;\r\n        this._activeTargets = [];\r\n\r\n        animation._runtimeAnimations.push(this);\r\n\r\n        // State\r\n        this._animationState = {\r\n            key: 0,\r\n            repeatCount: 0,\r\n            loopMode: this._getCorrectLoopMode(),\r\n        };\r\n\r\n        if (this._animation.dataType === Animation.ANIMATIONTYPE_MATRIX) {\r\n            this._animationState.workValue = Matrix.Zero();\r\n        }\r\n\r\n        // Limits\r\n        this._keys = this._animation.getKeys();\r\n        this._minFrame = this._keys[0].frame;\r\n        this._maxFrame = this._keys[this._keys.length - 1].frame;\r\n        this._minValue = this._keys[0].value;\r\n        this._maxValue = this._keys[this._keys.length - 1].value;\r\n\r\n        // Add a start key at frame 0 if missing\r\n        if (this._minFrame !== 0) {\r\n            const newKey = { frame: 0, value: this._minValue };\r\n            this._keys.splice(0, 0, newKey);\r\n        }\r\n\r\n        // Check data\r\n        if (this._target instanceof Array) {\r\n            let index = 0;\r\n            for (const target of this._target) {\r\n                this._preparePath(target, index);\r\n                this._getOriginalValues(index);\r\n                index++;\r\n            }\r\n            this._targetIsArray = true;\r\n        } else {\r\n            this._preparePath(this._target);\r\n            this._getOriginalValues();\r\n            this._targetIsArray = false;\r\n            this._directTarget = this._activeTargets[0];\r\n        }\r\n\r\n        // Cloning events locally\r\n        const events = animation.getEvents();\r\n        if (events && events.length > 0) {\r\n            for (const e of events) {\r\n                this._events.push(e._clone());\r\n            }\r\n        }\r\n\r\n        this._enableBlending = target && target.animationPropertiesOverride ? target.animationPropertiesOverride.enableBlending : this._animation.enableBlending;\r\n    }\r\n\r\n    private _preparePath(target: any, targetIndex = 0) {\r\n        const targetPropertyPath = this._animation.targetPropertyPath;\r\n\r\n        if (targetPropertyPath.length > 1) {\r\n            let property = target;\r\n            for (let index = 0; index < targetPropertyPath.length - 1; index++) {\r\n                const name = targetPropertyPath[index];\r\n                property = property[name];\r\n                if (property === undefined) {\r\n                    throw new Error(`Invalid property (${name}) in property path (${targetPropertyPath.join(\".\")})`);\r\n                }\r\n            }\r\n\r\n            this._targetPath = targetPropertyPath[targetPropertyPath.length - 1];\r\n            this._activeTargets[targetIndex] = property;\r\n        } else {\r\n            this._targetPath = targetPropertyPath[0];\r\n            this._activeTargets[targetIndex] = target;\r\n        }\r\n\r\n        if (this._activeTargets[targetIndex][this._targetPath] === undefined) {\r\n            throw new Error(`Invalid property (${this._targetPath}) in property path (${targetPropertyPath.join(\".\")})`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the animation from the runtime animation\r\n     */\r\n    public get animation(): Animation {\r\n        return this._animation;\r\n    }\r\n\r\n    /**\r\n     * Resets the runtime animation to the beginning\r\n     * @param restoreOriginal defines whether to restore the target property to the original value\r\n     */\r\n    public reset(restoreOriginal = false): void {\r\n        if (restoreOriginal) {\r\n            if (this._target instanceof Array) {\r\n                let index = 0;\r\n                for (const target of this._target) {\r\n                    if (this._originalValue[index] !== undefined) {\r\n                        this._setValue(target, this._activeTargets[index], this._originalValue[index], -1, index);\r\n                    }\r\n                    index++;\r\n                }\r\n            } else {\r\n                if (this._originalValue[0] !== undefined) {\r\n                    this._setValue(this._target, this._directTarget, this._originalValue[0], -1, 0);\r\n                }\r\n            }\r\n        }\r\n\r\n        this._offsetsCache = {};\r\n        this._highLimitsCache = {};\r\n        this._currentFrame = 0;\r\n        this._blendingFactor = 0;\r\n\r\n        // Events\r\n        for (let index = 0; index < this._events.length; index++) {\r\n            this._events[index].isDone = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Specifies if the runtime animation is stopped\r\n     * @returns Boolean specifying if the runtime animation is stopped\r\n     */\r\n    public isStopped(): boolean {\r\n        return this._stopped;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the runtime animation\r\n     */\r\n    public dispose(): void {\r\n        const index = this._animation.runtimeAnimations.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._animation.runtimeAnimations.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Apply the interpolated value to the target\r\n     * @param currentValue defines the value computed by the animation\r\n     * @param weight defines the weight to apply to this value (Defaults to 1.0)\r\n     */\r\n    public setValue(currentValue: any, weight: number) {\r\n        if (this._targetIsArray) {\r\n            for (let index = 0; index < this._target.length; index++) {\r\n                const target = this._target[index];\r\n                this._setValue(target, this._activeTargets[index], currentValue, weight, index);\r\n            }\r\n            return;\r\n        }\r\n        this._setValue(this._target, this._directTarget, currentValue, weight, 0);\r\n    }\r\n\r\n    private _getOriginalValues(targetIndex = 0) {\r\n        let originalValue: any;\r\n        const target = this._activeTargets[targetIndex];\r\n\r\n        if (target.getLocalMatrix && this._targetPath === \"_matrix\") {\r\n            // For bones\r\n            originalValue = target.getLocalMatrix();\r\n        } else {\r\n            originalValue = target[this._targetPath];\r\n        }\r\n\r\n        if (originalValue && originalValue.clone) {\r\n            this._originalValue[targetIndex] = originalValue.clone();\r\n        } else {\r\n            this._originalValue[targetIndex] = originalValue;\r\n        }\r\n    }\r\n\r\n    private _registerTargetForLateAnimationBinding(runtimeAnimation: RuntimeAnimation, originalValue: any): void {\r\n        const target = runtimeAnimation.target;\r\n        this._scene._registeredForLateAnimationBindings.pushNoDuplicate(target);\r\n\r\n        if (!target._lateAnimationHolders) {\r\n            target._lateAnimationHolders = {};\r\n        }\r\n\r\n        if (!target._lateAnimationHolders[runtimeAnimation.targetPath]) {\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath] = {\r\n                totalWeight: 0,\r\n                totalAdditiveWeight: 0,\r\n                animations: [],\r\n                additiveAnimations: [],\r\n                originalValue: originalValue,\r\n            };\r\n        }\r\n\r\n        if (runtimeAnimation.isAdditive) {\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath].additiveAnimations.push(runtimeAnimation);\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath].totalAdditiveWeight += runtimeAnimation.weight;\r\n        } else {\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath].animations.push(runtimeAnimation);\r\n            target._lateAnimationHolders[runtimeAnimation.targetPath].totalWeight += runtimeAnimation.weight;\r\n        }\r\n    }\r\n\r\n    private _setValue(target: any, destination: any, currentValue: any, weight: number, targetIndex: number): void {\r\n        // Set value\r\n        this._currentActiveTarget = destination;\r\n\r\n        this._weight = weight;\r\n\r\n        if (this._enableBlending && this._blendingFactor <= 1.0) {\r\n            if (!this._originalBlendValue) {\r\n                const originalValue = destination[this._targetPath];\r\n\r\n                if (originalValue.clone) {\r\n                    this._originalBlendValue = originalValue.clone();\r\n                } else {\r\n                    this._originalBlendValue = originalValue;\r\n                }\r\n            }\r\n\r\n            if (this._originalBlendValue.m) {\r\n                // Matrix\r\n                if (Animation.AllowMatrixDecomposeForInterpolation) {\r\n                    if (this._currentValue) {\r\n                        Matrix.DecomposeLerpToRef(this._originalBlendValue, currentValue, this._blendingFactor, this._currentValue);\r\n                    } else {\r\n                        this._currentValue = Matrix.DecomposeLerp(this._originalBlendValue, currentValue, this._blendingFactor);\r\n                    }\r\n                } else {\r\n                    if (this._currentValue) {\r\n                        Matrix.LerpToRef(this._originalBlendValue, currentValue, this._blendingFactor, this._currentValue);\r\n                    } else {\r\n                        this._currentValue = Matrix.Lerp(this._originalBlendValue, currentValue, this._blendingFactor);\r\n                    }\r\n                }\r\n            } else {\r\n                this._currentValue = Animation._UniversalLerp(this._originalBlendValue, currentValue, this._blendingFactor);\r\n            }\r\n\r\n            const blendingSpeed = target && target.animationPropertiesOverride ? target.animationPropertiesOverride.blendingSpeed : this._animation.blendingSpeed;\r\n            this._blendingFactor += blendingSpeed;\r\n        } else {\r\n            if (!this._currentValue) {\r\n                if (currentValue?.clone) {\r\n                    this._currentValue = currentValue.clone();\r\n                } else {\r\n                    this._currentValue = currentValue;\r\n                }\r\n            } else if (this._currentValue.copyFrom) {\r\n                this._currentValue.copyFrom(currentValue);\r\n            } else {\r\n                this._currentValue = currentValue;\r\n            }\r\n        }\r\n\r\n        if (weight !== -1.0) {\r\n            this._registerTargetForLateAnimationBinding(this, this._originalValue[targetIndex]);\r\n        } else {\r\n            if (this._animationState.loopMode === Animation.ANIMATIONLOOPMODE_RELATIVE_FROM_CURRENT) {\r\n                if (this._currentValue.addToRef) {\r\n                    this._currentValue.addToRef(this._originalValue[targetIndex], destination[this._targetPath]);\r\n                } else {\r\n                    destination[this._targetPath] = this._originalValue[targetIndex] + this._currentValue;\r\n                }\r\n            } else {\r\n                destination[this._targetPath] = this._currentValue;\r\n            }\r\n        }\r\n\r\n        if (target.markAsDirty) {\r\n            target.markAsDirty(this._animation.targetProperty);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the loop pmode of the runtime animation\r\n     * @returns Loop Mode\r\n     */\r\n    private _getCorrectLoopMode(): number | undefined {\r\n        if (this._target && this._target.animationPropertiesOverride) {\r\n            return this._target.animationPropertiesOverride.loopMode as number;\r\n        }\r\n\r\n        return this._animation.loopMode;\r\n    }\r\n\r\n    /**\r\n     * Move the current animation to a given frame\r\n     * @param frame defines the frame to move to\r\n     * @param weight defines the weight to apply to the animation (-1.0 by default)\r\n     */\r\n    public goToFrame(frame: number, weight = -1): void {\r\n        const keys = this._animation.getKeys();\r\n\r\n        if (frame < keys[0].frame) {\r\n            frame = keys[0].frame;\r\n        } else if (frame > keys[keys.length - 1].frame) {\r\n            frame = keys[keys.length - 1].frame;\r\n        }\r\n\r\n        // Need to reset animation events\r\n        const events = this._events;\r\n        if (events.length) {\r\n            for (let index = 0; index < events.length; index++) {\r\n                if (!events[index].onlyOnce) {\r\n                    // reset events in the future\r\n                    events[index].isDone = events[index].frame < frame;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._currentFrame = frame;\r\n        const currentValue = this._animation._interpolate(frame, this._animationState);\r\n\r\n        this.setValue(currentValue, weight);\r\n    }\r\n\r\n    /**\r\n     * @internal Internal use only\r\n     */\r\n    public _prepareForSpeedRatioChange(newSpeedRatio: number): void {\r\n        const newAbsoluteFrame = (this._previousElapsedTime * (this._animation.framePerSecond * newSpeedRatio)) / 1000.0;\r\n\r\n        this._absoluteFrameOffset = this._previousAbsoluteFrame - newAbsoluteFrame;\r\n    }\r\n\r\n    /**\r\n     * Execute the current animation\r\n     * @param elapsedTimeSinceAnimationStart defines the elapsed time (in milliseconds) since the animation was started\r\n     * @param from defines the lower frame of the animation range\r\n     * @param to defines the upper frame of the animation range\r\n     * @param loop defines if the current animation must loop\r\n     * @param speedRatio defines the current speed ratio\r\n     * @param weight defines the weight of the animation (default is -1 so no weight)\r\n     * @returns a boolean indicating if the animation is running\r\n     */\r\n    public animate(elapsedTimeSinceAnimationStart: number, from: number, to: number, loop: boolean, speedRatio: number, weight = -1.0): boolean {\r\n        const animation = this._animation;\r\n        const targetPropertyPath = animation.targetPropertyPath;\r\n        if (!targetPropertyPath || targetPropertyPath.length < 1) {\r\n            this._stopped = true;\r\n            return false;\r\n        }\r\n\r\n        let returnValue = true;\r\n        let currentFrame: number;\r\n        const events = this._events;\r\n        let frameRange = 0;\r\n\r\n        if (!this._coreRuntimeAnimation) {\r\n            // Check limits\r\n            if (from < this._minFrame || from > this._maxFrame) {\r\n                from = this._minFrame;\r\n            }\r\n            if (to < this._minFrame || to > this._maxFrame) {\r\n                to = this._maxFrame;\r\n            }\r\n\r\n            frameRange = to - from;\r\n            let offsetValue: any;\r\n\r\n            // Compute the frame according to the elapsed time and the fps of the animation (\"from\" and \"to\" are not factored in!)\r\n            let absoluteFrame = (elapsedTimeSinceAnimationStart * (animation.framePerSecond * speedRatio)) / 1000.0 + this._absoluteFrameOffset;\r\n            let highLimitValue = 0;\r\n\r\n            // Apply the yoyo function if required\r\n            let yoyoLoop = false;\r\n            const yoyoMode = loop && this._animationState.loopMode === Animation.ANIMATIONLOOPMODE_YOYO;\r\n            if (yoyoMode) {\r\n                const position = (absoluteFrame - from) / frameRange;\r\n\r\n                // Apply the yoyo curve\r\n                const sin = Math.sin(position * Math.PI);\r\n                const yoyoPosition = Math.abs(sin);\r\n\r\n                // Map the yoyo position back to the range\r\n                absoluteFrame = yoyoPosition * frameRange + from;\r\n\r\n                const direction = sin >= 0 ? 1 : -1;\r\n                if (this._yoyoDirection !== direction) {\r\n                    yoyoLoop = true;\r\n                }\r\n\r\n                this._yoyoDirection = direction;\r\n            }\r\n\r\n            this._previousElapsedTime = elapsedTimeSinceAnimationStart;\r\n            this._previousAbsoluteFrame = absoluteFrame;\r\n\r\n            if (!loop && to >= from && ((absoluteFrame >= frameRange && speedRatio > 0) || (absoluteFrame <= 0 && speedRatio < 0))) {\r\n                // If we are out of range and not looping get back to caller\r\n                returnValue = false;\r\n                highLimitValue = animation._getKeyValue(this._maxValue);\r\n            } else if (!loop && from >= to && ((absoluteFrame <= frameRange && speedRatio < 0) || (absoluteFrame >= 0 && speedRatio > 0))) {\r\n                returnValue = false;\r\n                highLimitValue = animation._getKeyValue(this._minValue);\r\n            } else if (this._animationState.loopMode !== Animation.ANIMATIONLOOPMODE_CYCLE) {\r\n                const keyOffset = to.toString() + from.toString();\r\n                if (!this._offsetsCache[keyOffset]) {\r\n                    this._animationState.repeatCount = 0;\r\n                    this._animationState.loopMode = Animation.ANIMATIONLOOPMODE_CYCLE; // force a specific codepath in animation._interpolate()!\r\n                    const fromValue = animation._interpolate(from, this._animationState);\r\n                    const toValue = animation._interpolate(to, this._animationState);\r\n\r\n                    this._animationState.loopMode = this._getCorrectLoopMode();\r\n                    switch (animation.dataType) {\r\n                        // Float\r\n                        case Animation.ANIMATIONTYPE_FLOAT:\r\n                            this._offsetsCache[keyOffset] = toValue - fromValue;\r\n                            break;\r\n                        // Quaternion\r\n                        case Animation.ANIMATIONTYPE_QUATERNION:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        // Vector3\r\n                        case Animation.ANIMATIONTYPE_VECTOR3:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        // Vector2\r\n                        case Animation.ANIMATIONTYPE_VECTOR2:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        // Size\r\n                        case Animation.ANIMATIONTYPE_SIZE:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        // Color3\r\n                        case Animation.ANIMATIONTYPE_COLOR3:\r\n                            this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                            break;\r\n                        default:\r\n                            break;\r\n                    }\r\n\r\n                    this._highLimitsCache[keyOffset] = toValue;\r\n                }\r\n\r\n                highLimitValue = this._highLimitsCache[keyOffset];\r\n                offsetValue = this._offsetsCache[keyOffset];\r\n            }\r\n\r\n            if (offsetValue === undefined) {\r\n                switch (animation.dataType) {\r\n                    // Float\r\n                    case Animation.ANIMATIONTYPE_FLOAT:\r\n                        offsetValue = 0;\r\n                        break;\r\n                    // Quaternion\r\n                    case Animation.ANIMATIONTYPE_QUATERNION:\r\n                        offsetValue = _StaticOffsetValueQuaternion;\r\n                        break;\r\n                    // Vector3\r\n                    case Animation.ANIMATIONTYPE_VECTOR3:\r\n                        offsetValue = _StaticOffsetValueVector3;\r\n                        break;\r\n                    // Vector2\r\n                    case Animation.ANIMATIONTYPE_VECTOR2:\r\n                        offsetValue = _StaticOffsetValueVector2;\r\n                        break;\r\n                    // Size\r\n                    case Animation.ANIMATIONTYPE_SIZE:\r\n                        offsetValue = _StaticOffsetValueSize;\r\n                        break;\r\n                    // Color3\r\n                    case Animation.ANIMATIONTYPE_COLOR3:\r\n                        offsetValue = _StaticOffsetValueColor3;\r\n                        break;\r\n                    case Animation.ANIMATIONTYPE_COLOR4:\r\n                        offsetValue = _StaticOffsetValueColor4;\r\n                        break;\r\n                }\r\n            }\r\n\r\n            // Compute value\r\n\r\n            if (this._host && this._host.syncRoot) {\r\n                // If we must sync with an animatable, calculate the current frame based on the frame of the root animatable\r\n                const syncRoot = this._host.syncRoot;\r\n                const hostNormalizedFrame = (syncRoot.masterFrame - syncRoot.fromFrame) / (syncRoot.toFrame - syncRoot.fromFrame);\r\n                currentFrame = from + frameRange * hostNormalizedFrame;\r\n            } else {\r\n                if ((absoluteFrame > 0 && from > to) || (absoluteFrame < 0 && from < to)) {\r\n                    currentFrame = returnValue && frameRange !== 0 ? to + (absoluteFrame % frameRange) : from;\r\n                } else {\r\n                    currentFrame = returnValue && frameRange !== 0 ? from + (absoluteFrame % frameRange) : to;\r\n                }\r\n            }\r\n\r\n            // Reset event/state if looping\r\n            if ((!yoyoMode && ((speedRatio > 0 && this.currentFrame > currentFrame) || (speedRatio < 0 && this.currentFrame < currentFrame))) || (yoyoMode && yoyoLoop)) {\r\n                this._onLoop();\r\n\r\n                // Need to reset animation events\r\n                for (let index = 0; index < events.length; index++) {\r\n                    if (!events[index].onlyOnce) {\r\n                        // reset event, the animation is looping\r\n                        events[index].isDone = false;\r\n                    }\r\n                }\r\n\r\n                this._animationState.key = speedRatio > 0 ? 0 : animation.getKeys().length - 1;\r\n            }\r\n            this._currentFrame = currentFrame;\r\n            this._animationState.repeatCount = frameRange === 0 ? 0 : (absoluteFrame / frameRange) >> 0;\r\n            this._animationState.highLimitValue = highLimitValue;\r\n            this._animationState.offsetValue = offsetValue;\r\n        } else {\r\n            frameRange = to - from;\r\n            currentFrame = this._coreRuntimeAnimation.currentFrame;\r\n            this._currentFrame = currentFrame;\r\n            this._animationState.repeatCount = this._coreRuntimeAnimation._animationState.repeatCount;\r\n            this._animationState.highLimitValue = this._coreRuntimeAnimation._animationState.highLimitValue;\r\n            this._animationState.offsetValue = this._coreRuntimeAnimation._animationState.offsetValue;\r\n        }\r\n\r\n        const currentValue = animation._interpolate(currentFrame, this._animationState);\r\n\r\n        // Set value\r\n        this.setValue(currentValue, weight);\r\n\r\n        // Check events\r\n        if (events.length) {\r\n            for (let index = 0; index < events.length; index++) {\r\n                // Make sure current frame has passed event frame and that event frame is within the current range\r\n                // Also, handle both forward and reverse animations\r\n                if (\r\n                    (frameRange >= 0 && currentFrame >= events[index].frame && events[index].frame >= from) ||\r\n                    (frameRange < 0 && currentFrame <= events[index].frame && events[index].frame <= from)\r\n                ) {\r\n                    const event = events[index];\r\n                    if (!event.isDone) {\r\n                        // If event should be done only once, remove it.\r\n                        if (event.onlyOnce) {\r\n                            events.splice(index, 1);\r\n                            index--;\r\n                        }\r\n                        event.isDone = true;\r\n                        event.action(currentFrame);\r\n                    } // Don't do anything if the event has already been done.\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!returnValue) {\r\n            this._stopped = true;\r\n        }\r\n\r\n        return returnValue;\r\n    }\r\n}\r\n"]}