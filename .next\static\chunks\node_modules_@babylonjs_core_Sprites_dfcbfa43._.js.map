{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Sprites/thinSprite.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Sprites/thinSprite.ts"], "sourcesContent": ["import type { IVector3Like, IColor4Like } from \"../Maths/math.like\";\r\nimport type { Nullable } from \"../types\";\r\n\r\n/**\r\n * ThinSprite Class used to represent a thin sprite\r\n * This is the base class for sprites but can also directly be used with ThinEngine\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n */\r\nexport class ThinSprite {\r\n    /** Gets or sets the cell index in the sprite sheet */\r\n    public cellIndex: number;\r\n    /** Gets or sets the cell reference in the sprite sheet, uses sprite's filename when added to sprite sheet */\r\n    public cellRef: string;\r\n    /** Gets or sets the current world position */\r\n    public position: IVector3Like;\r\n    /** Gets or sets the main color */\r\n    public color: IColor4Like;\r\n    /** Gets or sets the width */\r\n    public width = 1.0;\r\n    /** Gets or sets the height */\r\n    public height = 1.0;\r\n    /** Gets or sets rotation angle */\r\n    public angle = 0;\r\n    /** Gets or sets a boolean indicating if UV coordinates should be inverted in U axis */\r\n    public invertU = false;\r\n    /** Gets or sets a boolean indicating if UV coordinates should be inverted in B axis */\r\n    public invertV = false;\r\n    /** Gets or sets a boolean indicating if the sprite is visible (renderable). Default is true */\r\n    public isVisible = true;\r\n\r\n    /**\r\n     * Returns a boolean indicating if the animation is started\r\n     */\r\n    public get animationStarted() {\r\n        return this._animationStarted;\r\n    }\r\n\r\n    /** Gets the initial key for the animation (setting it will restart the animation)  */\r\n    public get fromIndex() {\r\n        return this._fromIndex;\r\n    }\r\n\r\n    /** Gets or sets the end key for the animation (setting it will restart the animation)  */\r\n    public get toIndex() {\r\n        return this._toIndex;\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the animation is looping (setting it will restart the animation)  */\r\n    public get loopAnimation() {\r\n        return this._loopAnimation;\r\n    }\r\n\r\n    /** Gets or sets the delay between cell changes (setting it will restart the animation)  */\r\n    public get delay() {\r\n        return Math.max(this._delay, 1);\r\n    }\r\n\r\n    /** @internal */\r\n    public _xOffset: number;\r\n    /** @internal */\r\n    public _yOffset: number;\r\n    /** @internal */\r\n    public _xSize: number;\r\n    /** @internal */\r\n    public _ySize: number;\r\n\r\n    private _animationStarted = false;\r\n    protected _loopAnimation = false;\r\n    protected _fromIndex = 0;\r\n    protected _toIndex = 0;\r\n    protected _delay = 0;\r\n    private _direction = 1;\r\n    private _time = 0;\r\n    private _onBaseAnimationEnd: Nullable<() => void> = null;\r\n\r\n    /**\r\n     * Creates a new Thin Sprite\r\n     */\r\n    constructor() {\r\n        this.position = { x: 1.0, y: 1.0, z: 1.0 };\r\n        this.color = { r: 1.0, g: 1.0, b: 1.0, a: 1.0 };\r\n    }\r\n\r\n    /**\r\n     * Starts an animation\r\n     * @param from defines the initial key\r\n     * @param to defines the end key\r\n     * @param loop defines if the animation must loop\r\n     * @param delay defines the start delay (in ms)\r\n     * @param onAnimationEnd defines a callback for when the animation ends\r\n     */\r\n    public playAnimation(from: number, to: number, loop: boolean, delay: number, onAnimationEnd: Nullable<() => void>): void {\r\n        this._fromIndex = from;\r\n        this._toIndex = to;\r\n        this._loopAnimation = loop;\r\n        this._delay = delay || 1;\r\n        this._animationStarted = true;\r\n        this._onBaseAnimationEnd = onAnimationEnd;\r\n\r\n        if (from < to) {\r\n            this._direction = 1;\r\n        } else {\r\n            this._direction = -1;\r\n            this._toIndex = from;\r\n            this._fromIndex = to;\r\n        }\r\n\r\n        this.cellIndex = from;\r\n        this._time = 0;\r\n    }\r\n\r\n    /** Stops current animation (if any) */\r\n    public stopAnimation(): void {\r\n        this._animationStarted = false;\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _animate(deltaTime: number): void {\r\n        if (!this._animationStarted) {\r\n            return;\r\n        }\r\n\r\n        this._time += deltaTime;\r\n        if (this._time > this._delay) {\r\n            this._time = this._time % this._delay;\r\n            this.cellIndex += this._direction;\r\n            if ((this._direction > 0 && this.cellIndex > this._toIndex) || (this._direction < 0 && this.cellIndex < this._fromIndex)) {\r\n                if (this._loopAnimation) {\r\n                    this.cellIndex = this._direction > 0 ? this._fromIndex : this._toIndex;\r\n                } else {\r\n                    this.cellIndex = this._toIndex;\r\n                    this._animationStarted = false;\r\n                    if (this._onBaseAnimationEnd) {\r\n                        this._onBaseAnimationEnd();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAGA;;;;GAIG;;;AACG,MAAO,UAAU;IAsBnB;;OAEG,CACH,IAAW,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED,oFAAA,EAAsF,CACtF,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,wFAAA,EAA0F,CAC1F,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,2GAAA,EAA6G,CAC7G,IAAW,aAAa,GAAA;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,yFAAA,EAA2F,CAC3F,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IA4BD;;;;;;;OAOG,CACI,aAAa,CAAC,IAAY,EAAE,EAAU,EAAE,IAAa,EAAE,KAAa,EAAE,cAAoC,EAAA;QAC7G,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,mBAAmB,GAAG,cAAc,CAAC;QAE1C,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;YACZ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACxB,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACnB,CAAC;IAED,qCAAA,EAAuC,CAChC,aAAa,GAAA;QAChB,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;OAEG,CACI,QAAQ,CAAC,SAAiB,EAAA;QAC7B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC;QACxB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YACtC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;YAClC,IAAI,AAAC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAK,CAAD,GAAK,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAE,CAAC;gBACvH,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC3E,CAAC,MAAM,CAAC;oBACJ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;oBAC/B,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAC/B,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;wBAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC/B,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAjED;;OAEG,CACH,aAAA;QA7DA,2BAAA,EAA6B,CACtB,IAAA,CAAA,KAAK,GAAG,GAAG,CAAC;QACnB,4BAAA,EAA8B,CACvB,IAAA,CAAA,MAAM,GAAG,GAAG,CAAC;QACpB,gCAAA,EAAkC,CAC3B,IAAA,CAAA,KAAK,GAAG,CAAC,CAAC;QACjB,qFAAA,EAAuF,CAChF,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QACvB,qFAAA,EAAuF,CAChF,IAAA,CAAA,OAAO,GAAG,KAAK,CAAC;QACvB,6FAAA,EAA+F,CACxF,IAAA,CAAA,SAAS,GAAG,IAAI,CAAC;QAsChB,IAAA,CAAA,iBAAiB,GAAG,KAAK,CAAC;QACxB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QACvB,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QACf,IAAA,CAAA,QAAQ,GAAG,CAAC,CAAC;QACb,IAAA,CAAA,MAAM,GAAG,CAAC,CAAC;QACb,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QACf,IAAA,CAAA,KAAK,GAAG,CAAC,CAAC;QACV,IAAA,CAAA,mBAAmB,GAAyB,IAAI,CAAC;QAMrD,IAAI,CAAC,QAAQ,GAAG;YAAE,CAAC,EAAE,GAAG;YAAE,CAAC,EAAE,GAAG;YAAE,CAAC,EAAE,GAAG;QAAA,CAAE,CAAC;QAC3C,IAAI,CAAC,KAAK,GAAG;YAAE,CAAC,EAAE,GAAG;YAAE,CAAC,EAAE,GAAG;YAAE,CAAC,EAAE,GAAG;YAAE,CAAC,EAAE,GAAG;QAAA,CAAE,CAAC;IACpD,CAAC;CA4DJ", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Sprites/sprite.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Sprites/sprite.ts"], "sourcesContent": ["import { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { ActionManager } from \"../Actions/actionManager\";\r\nimport type { ISpriteManager, SpriteManager } from \"./spriteManager\";\r\nimport { Color4 } from \"../Maths/math.color\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { IAnimatable } from \"../Animations/animatable.interface\";\r\nimport { ThinSprite } from \"./thinSprite\";\r\n\r\nimport type { Animation } from \"../Animations/animation\";\r\n\r\n/**\r\n * Class used to represent a sprite\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n */\r\nexport class Sprite extends ThinSprite implements IAnimatable {\r\n    /** Gets or sets the current world position */\r\n    public override position: Vector3;\r\n    /** Gets or sets the main color */\r\n    public override color: Color4;\r\n    /** Gets or sets a boolean indicating that this sprite should be disposed after animation ends */\r\n    public disposeWhenFinishedAnimating: boolean;\r\n    /** Gets the list of attached animations */\r\n    public animations: Nullable<Array<Animation>> = new Array<Animation>();\r\n    /** Gets or sets a boolean indicating if the sprite can be picked */\r\n    public isPickable = false;\r\n    /** Gets or sets a boolean indicating that sprite texture alpha will be used for precise picking (false by default) */\r\n    public useAlphaForPicking = false;\r\n\r\n    /**\r\n     * Gets or sets the associated action manager\r\n     */\r\n    public actionManager: Nullable<ActionManager>;\r\n\r\n    /**\r\n     * An event triggered when the control has been disposed\r\n     */\r\n    public onDisposeObservable = new Observable<Sprite>();\r\n\r\n    private _manager: ISpriteManager;\r\n    private _onAnimationEnd: Nullable<() => void> = null;\r\n\r\n    /**\r\n     * Gets or sets the sprite size\r\n     */\r\n    public get size(): number {\r\n        return this.width;\r\n    }\r\n\r\n    public set size(value: number) {\r\n        this.width = value;\r\n        this.height = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the unique id of the sprite\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * Gets the manager of this sprite\r\n     */\r\n    public get manager() {\r\n        return this._manager;\r\n    }\r\n\r\n    /**\r\n     * Creates a new Sprite\r\n     * @param name defines the name\r\n     * @param manager defines the manager\r\n     */\r\n    constructor(\r\n        /** defines the name */\r\n        public name: string,\r\n        manager: ISpriteManager\r\n    ) {\r\n        super();\r\n        this.color = new Color4(1.0, 1.0, 1.0, 1.0);\r\n        this.position = Vector3.Zero();\r\n\r\n        this._manager = manager;\r\n        this._manager.sprites.push(this);\r\n        this.uniqueId = this._manager.scene.getUniqueId();\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"Sprite\"\r\n     * @returns \"Sprite\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"Sprite\";\r\n    }\r\n\r\n    /** Gets or sets the initial key for the animation (setting it will restart the animation)  */\r\n    public override get fromIndex() {\r\n        return this._fromIndex;\r\n    }\r\n    public override set fromIndex(value: number) {\r\n        this.playAnimation(value, this._toIndex, this._loopAnimation, this._delay, this._onAnimationEnd);\r\n    }\r\n\r\n    /** Gets or sets the end key for the animation (setting it will restart the animation)  */\r\n    public override get toIndex() {\r\n        return this._toIndex;\r\n    }\r\n    public override set toIndex(value: number) {\r\n        this.playAnimation(this._fromIndex, value, this._loopAnimation, this._delay, this._onAnimationEnd);\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the animation is looping (setting it will restart the animation)  */\r\n    public override get loopAnimation() {\r\n        return this._loopAnimation;\r\n    }\r\n    public override set loopAnimation(value: boolean) {\r\n        this.playAnimation(this._fromIndex, this._toIndex, value, this._delay, this._onAnimationEnd);\r\n    }\r\n\r\n    /** Gets or sets the delay between cell changes (setting it will restart the animation)  */\r\n    public override get delay() {\r\n        return Math.max(this._delay, 1);\r\n    }\r\n    public override set delay(value: number) {\r\n        this.playAnimation(this._fromIndex, this._toIndex, this._loopAnimation, value, this._onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Starts an animation\r\n     * @param from defines the initial key\r\n     * @param to defines the end key\r\n     * @param loop defines if the animation must loop\r\n     * @param delay defines the start delay (in ms)\r\n     * @param onAnimationEnd defines a callback to call when animation ends\r\n     */\r\n    public override playAnimation(from: number, to: number, loop: boolean, delay: number, onAnimationEnd: Nullable<() => void> = null): void {\r\n        this._onAnimationEnd = onAnimationEnd;\r\n\r\n        super.playAnimation(from, to, loop, delay, this._endAnimation);\r\n    }\r\n\r\n    private _endAnimation = () => {\r\n        if (this._onAnimationEnd) {\r\n            this._onAnimationEnd();\r\n        }\r\n        if (this.disposeWhenFinishedAnimating) {\r\n            this.dispose();\r\n        }\r\n    };\r\n\r\n    /** Release associated resources */\r\n    public dispose(): void {\r\n        for (let i = 0; i < this._manager.sprites.length; i++) {\r\n            if (this._manager.sprites[i] == this) {\r\n                this._manager.sprites.splice(i, 1);\r\n            }\r\n        }\r\n\r\n        // Callback\r\n        this.onDisposeObservable.notifyObservers(this);\r\n        this.onDisposeObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Serializes the sprite to a JSON object\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.position = this.position.asArray();\r\n        serializationObject.color = this.color.asArray();\r\n        serializationObject.width = this.width;\r\n        serializationObject.height = this.height;\r\n        serializationObject.angle = this.angle;\r\n        serializationObject.cellIndex = this.cellIndex;\r\n        serializationObject.cellRef = this.cellRef;\r\n        serializationObject.invertU = this.invertU;\r\n        serializationObject.invertV = this.invertV;\r\n        serializationObject.disposeWhenFinishedAnimating = this.disposeWhenFinishedAnimating;\r\n        serializationObject.isPickable = this.isPickable;\r\n        serializationObject.isVisible = this.isVisible;\r\n        serializationObject.useAlphaForPicking = this.useAlphaForPicking;\r\n\r\n        serializationObject.animationStarted = this.animationStarted;\r\n        serializationObject.fromIndex = this.fromIndex;\r\n        serializationObject.toIndex = this.toIndex;\r\n        serializationObject.loopAnimation = this.loopAnimation;\r\n        serializationObject.delay = this.delay;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a JSON object to create a new sprite\r\n     * @param parsedSprite The JSON object to parse\r\n     * @param manager defines the hosting manager\r\n     * @returns the new sprite\r\n     */\r\n    public static Parse(parsedSprite: any, manager: SpriteManager): Sprite {\r\n        const sprite = new Sprite(parsedSprite.name, manager);\r\n\r\n        sprite.position = Vector3.FromArray(parsedSprite.position);\r\n        sprite.color = Color4.FromArray(parsedSprite.color);\r\n        sprite.width = parsedSprite.width;\r\n        sprite.height = parsedSprite.height;\r\n        sprite.angle = parsedSprite.angle;\r\n        sprite.cellIndex = parsedSprite.cellIndex;\r\n        sprite.cellRef = parsedSprite.cellRef;\r\n        sprite.invertU = parsedSprite.invertU;\r\n        sprite.invertV = parsedSprite.invertV;\r\n        sprite.disposeWhenFinishedAnimating = parsedSprite.disposeWhenFinishedAnimating;\r\n        sprite.isPickable = parsedSprite.isPickable;\r\n        sprite.isVisible = parsedSprite.isVisible;\r\n        sprite.useAlphaForPicking = parsedSprite.useAlphaForPicking;\r\n\r\n        sprite._fromIndex = parsedSprite.fromIndex;\r\n        sprite._toIndex = parsedSprite.toIndex;\r\n        sprite._loopAnimation = parsedSprite.loopAnimation;\r\n        sprite._delay = parsedSprite.delay;\r\n\r\n        if (parsedSprite.animationStarted) {\r\n            sprite.playAnimation(sprite.fromIndex, sprite.toIndex, sprite.loopAnimation, sprite.delay);\r\n        }\r\n\r\n        return sprite;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAI/C,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;;;;;AAQpC,MAAO,MAAO,yKAAQ,aAAU;IA2BlC;;OAEG,CACH,IAAW,IAAI,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAa,EAAA;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,CAAC;IAOD;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAqBD;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,4FAAA,EAA8F,CAC9F,IAAoB,SAAS,GAAA;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,IAAoB,SAAS,CAAC,KAAa,EAAA;QACvC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACrG,CAAC;IAED,wFAAA,EAA0F,CAC1F,IAAoB,OAAO,GAAA;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IACD,IAAoB,OAAO,CAAC,KAAa,EAAA;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACvG,CAAC;IAED,2GAAA,EAA6G,CAC7G,IAAoB,aAAa,GAAA;QAC7B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IACD,IAAoB,aAAa,CAAC,KAAc,EAAA;QAC5C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACjG,CAAC;IAED,yFAAA,EAA2F,CAC3F,IAAoB,KAAK,GAAA;QACrB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,IAAoB,KAAK,CAAC,KAAa,EAAA;QACnC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IACzG,CAAC;IAED;;;;;;;OAOG,CACa,aAAa,CAAC,IAAY,EAAE,EAAU,EAAE,IAAa,EAAE,KAAa,EAA6C;6BAA3C,iEAAuC,IAAI;QAC7H,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QAEtC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IACnE,CAAC;IAWD,iCAAA,EAAmC,CAC5B,OAAO,GAAA;QACV,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YACpD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAED,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;IACrC,CAAC;IAED;;;OAGG,CACI,SAAS,GAAA;QACZ,MAAM,mBAAmB,GAAQ,CAAA,CAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QACvD,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACjD,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzC,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,4BAA4B,GAAG,IAAI,CAAC,4BAA4B,CAAC;QACrF,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAEjE,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC7D,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3C,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvD,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEvC,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG,CACI,MAAM,CAAC,KAAK,CAAC,YAAiB,EAAE,OAAsB,EAAA;QACzD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAEtD,MAAM,CAAC,QAAQ,qKAAG,UAAO,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC3D,MAAM,CAAC,KAAK,oKAAG,SAAM,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACpD,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;QAClC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;QACpC,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;QAClC,MAAM,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;QAC1C,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACtC,MAAM,CAAC,4BAA4B,GAAG,YAAY,CAAC,4BAA4B,CAAC;QAChF,MAAM,CAAC,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QAC5C,MAAM,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;QAC1C,MAAM,CAAC,kBAAkB,GAAG,YAAY,CAAC,kBAAkB,CAAC;QAE5D,MAAM,CAAC,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC;QAC3C,MAAM,CAAC,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC;QACvC,MAAM,CAAC,cAAc,GAAG,YAAY,CAAC,aAAa,CAAC;QACnD,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;QAEnC,IAAI,YAAY,CAAC,gBAAgB,EAAE,CAAC;YAChC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/F,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IA/JD;;;;OAIG,CACH,YACI,qBAAA,EAAuB,CAChB,IAAY,EACnB,OAAuB,CAAA;QAEvB,KAAK,EAAE,CAAC;QAHD,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QAnDvB,yCAAA,EAA2C,CACpC,IAAA,CAAA,UAAU,GAA+B,IAAI,KAAK,EAAa,CAAC;QACvE,kEAAA,EAAoE,CAC7D,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAC1B,oHAAA,EAAsH,CAC/G,IAAA,CAAA,kBAAkB,GAAG,KAAK,CAAC;QAOlC;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,gKAAI,cAAU,EAAU,CAAC;QAG9C,IAAA,CAAA,eAAe,GAAyB,IAAI,CAAC;QAmG7C,IAAA,CAAA,aAAa,GAAG,GAAG,EAAE;YACzB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,IAAI,CAAC,eAAe,EAAE,CAAC;YAC3B,CAAC;YACD,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACL,CAAC,CAAC;QArEE,IAAI,CAAC,KAAK,GAAG,qKAAI,SAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAE/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;IACtD,CAAC;CA+IJ", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Sprites/spriteSceneComponent.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Sprites/spriteSceneComponent.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { IReadonlyObservable } from \"../Misc/observable\";\r\nimport { Scene } from \"../scene\";\r\nimport type { Sprite } from \"./sprite\";\r\nimport type { ISpriteManager } from \"./spriteManager\";\r\nimport { CreatePickingRayInCameraSpace, CreatePickingRayInCameraSpaceToRef, Ray } from \"../Culling/ray.core\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport type { ISceneComponent } from \"../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { ActionEvent } from \"../Actions/actionEvent\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { IPointerEvent } from \"../Events/deviceInputEvents\";\r\n\r\ndeclare module \"../scene\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface Scene {\r\n        /** @internal */\r\n        _pointerOverSprite: Nullable<Sprite>;\r\n\r\n        /** @internal */\r\n        _pickedDownSprite: Nullable<Sprite>;\r\n\r\n        /** @internal */\r\n        _tempSpritePickingRay: Nullable<Ray>;\r\n\r\n        /**\r\n         * All of the sprite managers added to this scene\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n         */\r\n        spriteManagers?: Array<ISpriteManager>;\r\n\r\n        /**\r\n         * An event triggered when a sprite manager is added to the scene\r\n         */\r\n        readonly onNewSpriteManagerAddedObservable: IReadonlyObservable<ISpriteManager>;\r\n\r\n        /**\r\n         * An event triggered when a sprite manager is removed from the scene\r\n         */\r\n        readonly onSpriteManagerRemovedObservable: IReadonlyObservable<ISpriteManager>;\r\n\r\n        /**\r\n         * An event triggered when sprites rendering is about to start\r\n         * Note: This event can be trigger more than once per frame (because sprites can be rendered by render target textures as well)\r\n         */\r\n        onBeforeSpritesRenderingObservable: Observable<Scene>;\r\n\r\n        /**\r\n         * An event triggered when sprites rendering is done\r\n         * Note: This event can be trigger more than once per frame (because sprites can be rendered by render target textures as well)\r\n         */\r\n        onAfterSpritesRenderingObservable: Observable<Scene>;\r\n\r\n        /** @internal */\r\n        _internalPickSprites(ray: Ray, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo>;\r\n\r\n        /** Launch a ray to try to pick a sprite in the scene\r\n         * @param x position on screen\r\n         * @param y position on screen\r\n         * @param predicate Predicate function used to determine eligible sprites. Can be set to null. In this case, a sprite must have isPickable set to true\r\n         * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n         * @param camera camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n         * @returns a PickingInfo\r\n         */\r\n        pickSprite(x: number, y: number, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo>;\r\n\r\n        /** Use the given ray to pick a sprite in the scene\r\n         * @param ray The ray (in world space) to use to pick meshes\r\n         * @param predicate Predicate function used to determine eligible sprites. Can be set to null. In this case, a sprite must have isPickable set to true\r\n         * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n         * @param camera camera to use. Can be set to null. In this case, the scene.activeCamera will be used\r\n         * @returns a PickingInfo\r\n         */\r\n        pickSpriteWithRay(ray: Ray, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo>;\r\n\r\n        /** @internal */\r\n        _internalMultiPickSprites(ray: Ray, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]>;\r\n\r\n        /** Launch a ray to try to pick sprites in the scene\r\n         * @param x position on screen\r\n         * @param y position on screen\r\n         * @param predicate Predicate function used to determine eligible sprites. Can be set to null. In this case, a sprite must have isPickable set to true\r\n         * @param camera camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n         * @returns a PickingInfo array\r\n         */\r\n        multiPickSprite(x: number, y: number, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]>;\r\n\r\n        /** Use the given ray to pick sprites in the scene\r\n         * @param ray The ray (in world space) to use to pick meshes\r\n         * @param predicate Predicate function used to determine eligible sprites. Can be set to null. In this case, a sprite must have isPickable set to true\r\n         * @param camera camera to use. Can be set to null. In this case, the scene.activeCamera will be used\r\n         * @returns a PickingInfo array\r\n         */\r\n        multiPickSpriteWithRay(ray: Ray, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]>;\r\n\r\n        /**\r\n         * Force the sprite under the pointer\r\n         * @param sprite defines the sprite to use\r\n         */\r\n        setPointerOverSprite(sprite: Nullable<Sprite>): void;\r\n\r\n        /**\r\n         * Gets the sprite under the pointer\r\n         * @returns a Sprite or null if no sprite is under the pointer\r\n         */\r\n        getPointerOverSprite(): Nullable<Sprite>;\r\n    }\r\n}\r\n\r\n/** @internal */\r\nexport type InternalSpriteAugmentedScene = Scene & {\r\n    _onNewSpriteManagerAddedObservable?: Observable<ISpriteManager>;\r\n    _onSpriteManagerRemovedObservable?: Observable<ISpriteManager>;\r\n};\r\n\r\nObject.defineProperty(Scene.prototype, \"onNewSpriteManagerAddedObservable\", {\r\n    get: function (this: InternalSpriteAugmentedScene) {\r\n        if (!this.isDisposed && !this._onNewSpriteManagerAddedObservable) {\r\n            const onNewSpriteManagerAddedObservable = (this._onNewSpriteManagerAddedObservable = new Observable<ISpriteManager>());\r\n            this.onDisposeObservable.addOnce(() => onNewSpriteManagerAddedObservable.clear());\r\n        }\r\n        return this._onNewSpriteManagerAddedObservable;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Scene.prototype, \"onSpriteManagerRemovedObservable\", {\r\n    get: function (this: InternalSpriteAugmentedScene) {\r\n        if (!this.isDisposed && !this._onSpriteManagerRemovedObservable) {\r\n            const onSpriteManagerRemovedObservable = (this._onSpriteManagerRemovedObservable = new Observable<ISpriteManager>());\r\n            this.onDisposeObservable.addOnce(() => onSpriteManagerRemovedObservable.clear());\r\n        }\r\n        return this._onSpriteManagerRemovedObservable;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nScene.prototype._internalPickSprites = function (ray: Ray, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n\r\n    let pickingInfo = null;\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            return null;\r\n        }\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    if (this.spriteManagers && this.spriteManagers.length > 0) {\r\n        for (let spriteIndex = 0; spriteIndex < this.spriteManagers.length; spriteIndex++) {\r\n            const spriteManager = this.spriteManagers[spriteIndex];\r\n\r\n            if (!spriteManager.isPickable) {\r\n                continue;\r\n            }\r\n\r\n            const result = spriteManager.intersects(ray, camera, predicate, fastCheck);\r\n            if (!result || !result.hit) {\r\n                continue;\r\n            }\r\n\r\n            if (!fastCheck && pickingInfo != null && result.distance >= pickingInfo.distance) {\r\n                continue;\r\n            }\r\n\r\n            pickingInfo = result;\r\n\r\n            if (fastCheck) {\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfo || new PickingInfo();\r\n};\r\n\r\nScene.prototype._internalMultiPickSprites = function (ray: Ray, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n\r\n    let pickingInfos: PickingInfo[] = [];\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            return null;\r\n        }\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    if (this.spriteManagers && this.spriteManagers.length > 0) {\r\n        for (let spriteIndex = 0; spriteIndex < this.spriteManagers.length; spriteIndex++) {\r\n            const spriteManager = this.spriteManagers[spriteIndex];\r\n\r\n            if (!spriteManager.isPickable) {\r\n                continue;\r\n            }\r\n\r\n            const results = spriteManager.multiIntersects(ray, camera, predicate);\r\n\r\n            if (results !== null) {\r\n                pickingInfos = pickingInfos.concat(results);\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfos;\r\n};\r\n\r\nScene.prototype.pickSprite = function (x: number, y: number, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo> {\r\n    if (!this._tempSpritePickingRay) {\r\n        return null;\r\n    }\r\n\r\n    CreatePickingRayInCameraSpaceToRef(this, x, y, this._tempSpritePickingRay, camera);\r\n\r\n    const result = this._internalPickSprites(this._tempSpritePickingRay, predicate, fastCheck, camera);\r\n    if (result) {\r\n        result.ray = CreatePickingRayInCameraSpace(this, x, y, camera);\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nScene.prototype.pickSpriteWithRay = function (ray: Ray, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo> {\r\n    if (!this._tempSpritePickingRay) {\r\n        return null;\r\n    }\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            return null;\r\n        }\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    Ray.TransformToRef(ray, camera.getViewMatrix(), this._tempSpritePickingRay);\r\n\r\n    const result = this._internalPickSprites(this._tempSpritePickingRay, predicate, fastCheck, camera);\r\n    if (result) {\r\n        result.ray = ray;\r\n    }\r\n\r\n    return result;\r\n};\r\n\r\nScene.prototype.multiPickSprite = function (x: number, y: number, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]> {\r\n    CreatePickingRayInCameraSpaceToRef(this, x, y, this._tempSpritePickingRay!, camera);\r\n\r\n    return this._internalMultiPickSprites(this._tempSpritePickingRay!, predicate, camera);\r\n};\r\n\r\nScene.prototype.multiPickSpriteWithRay = function (ray: Ray, predicate?: (sprite: Sprite) => boolean, camera?: Camera): Nullable<PickingInfo[]> {\r\n    if (!this._tempSpritePickingRay) {\r\n        return null;\r\n    }\r\n\r\n    if (!camera) {\r\n        if (!this.activeCamera) {\r\n            return null;\r\n        }\r\n        camera = this.activeCamera;\r\n    }\r\n\r\n    Ray.TransformToRef(ray, camera.getViewMatrix(), this._tempSpritePickingRay);\r\n\r\n    return this._internalMultiPickSprites(this._tempSpritePickingRay, predicate, camera);\r\n};\r\n\r\nScene.prototype.setPointerOverSprite = function (sprite: Nullable<Sprite>): void {\r\n    if (this._pointerOverSprite === sprite) {\r\n        return;\r\n    }\r\n\r\n    if (this._pointerOverSprite && this._pointerOverSprite.actionManager) {\r\n        this._pointerOverSprite.actionManager.processTrigger(Constants.ACTION_OnPointerOutTrigger, ActionEvent.CreateNewFromSprite(this._pointerOverSprite, this));\r\n    }\r\n\r\n    this._pointerOverSprite = sprite;\r\n    if (this._pointerOverSprite && this._pointerOverSprite.actionManager) {\r\n        this._pointerOverSprite.actionManager.processTrigger(Constants.ACTION_OnPointerOverTrigger, ActionEvent.CreateNewFromSprite(this._pointerOverSprite, this));\r\n    }\r\n};\r\n\r\nScene.prototype.getPointerOverSprite = function (): Nullable<Sprite> {\r\n    return this._pointerOverSprite;\r\n};\r\n\r\n/**\r\n * Defines the sprite scene component responsible to manage sprites\r\n * in a given scene.\r\n */\r\nexport class SpriteSceneComponent implements ISceneComponent {\r\n    /**\r\n     * The component name helpfull to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_SPRITE;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    /** @internal */\r\n    private _spritePredicate: (sprite: Sprite) => boolean;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene: Scene) {\r\n        this.scene = scene;\r\n        this.scene.spriteManagers = [] as ISpriteManager[];\r\n        // This ray is used to pick sprites in the scene\r\n        this.scene._tempSpritePickingRay = Ray ? Ray.Zero() : null;\r\n        this.scene.onBeforeSpritesRenderingObservable = new Observable<Scene>();\r\n        this.scene.onAfterSpritesRenderingObservable = new Observable<Scene>();\r\n        this._spritePredicate = (sprite: Sprite): boolean => {\r\n            if (!sprite.actionManager) {\r\n                return false;\r\n            }\r\n            return sprite.isPickable && sprite.actionManager.hasPointerTriggers;\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._pointerMoveStage.registerStep(SceneComponentConstants.STEP_POINTERMOVE_SPRITE, this, this._pointerMove);\r\n        this.scene._pointerDownStage.registerStep(SceneComponentConstants.STEP_POINTERDOWN_SPRITE, this, this._pointerDown);\r\n        this.scene._pointerUpStage.registerStep(SceneComponentConstants.STEP_POINTERUP_SPRITE, this, this._pointerUp);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        /** Nothing to do for sprites */\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        this.scene.onBeforeSpritesRenderingObservable.clear();\r\n        this.scene.onAfterSpritesRenderingObservable.clear();\r\n\r\n        const spriteManagers = this.scene.spriteManagers;\r\n        if (!spriteManagers) {\r\n            return;\r\n        }\r\n        while (spriteManagers.length) {\r\n            spriteManagers[0].dispose();\r\n        }\r\n    }\r\n\r\n    private _pickSpriteButKeepRay(originalPointerInfo: Nullable<PickingInfo>, x: number, y: number, fastCheck?: boolean, camera?: Camera): Nullable<PickingInfo> {\r\n        const result = this.scene.pickSprite(x, y, this._spritePredicate, fastCheck, camera);\r\n        if (result) {\r\n            result.ray = originalPointerInfo ? originalPointerInfo.ray : null;\r\n        }\r\n        return result;\r\n    }\r\n\r\n    private _pointerMove(\r\n        unTranslatedPointerX: number,\r\n        unTranslatedPointerY: number,\r\n        pickResult: Nullable<PickingInfo>,\r\n        isMeshPicked: boolean,\r\n        element: Nullable<HTMLElement>\r\n    ): Nullable<PickingInfo> {\r\n        const scene = this.scene;\r\n        if (isMeshPicked) {\r\n            scene.setPointerOverSprite(null);\r\n        } else {\r\n            pickResult = this._pickSpriteButKeepRay(pickResult, unTranslatedPointerX, unTranslatedPointerY, false, scene.cameraToUseForPointers || undefined);\r\n\r\n            if (pickResult && pickResult.hit && pickResult.pickedSprite) {\r\n                scene.setPointerOverSprite(pickResult.pickedSprite);\r\n                if (!scene.doNotHandleCursors && element) {\r\n                    if (scene._pointerOverSprite && scene._pointerOverSprite.actionManager && scene._pointerOverSprite.actionManager.hoverCursor) {\r\n                        element.style.cursor = scene._pointerOverSprite.actionManager.hoverCursor;\r\n                    } else {\r\n                        element.style.cursor = scene.hoverCursor;\r\n                    }\r\n                }\r\n            } else {\r\n                scene.setPointerOverSprite(null);\r\n            }\r\n        }\r\n\r\n        return pickResult;\r\n    }\r\n\r\n    private _pointerDown(unTranslatedPointerX: number, unTranslatedPointerY: number, pickResult: Nullable<PickingInfo>, evt: IPointerEvent): Nullable<PickingInfo> {\r\n        const scene = this.scene;\r\n        scene._pickedDownSprite = null;\r\n        if (scene.spriteManagers && scene.spriteManagers.length > 0) {\r\n            pickResult = scene.pickSprite(unTranslatedPointerX, unTranslatedPointerY, this._spritePredicate, false, scene.cameraToUseForPointers || undefined);\r\n\r\n            if (pickResult && pickResult.hit && pickResult.pickedSprite) {\r\n                if (pickResult.pickedSprite.actionManager) {\r\n                    scene._pickedDownSprite = pickResult.pickedSprite;\r\n                    switch (evt.button) {\r\n                        case 0:\r\n                            pickResult.pickedSprite.actionManager.processTrigger(\r\n                                Constants.ACTION_OnLeftPickTrigger,\r\n                                ActionEvent.CreateNewFromSprite(pickResult.pickedSprite, scene, evt)\r\n                            );\r\n                            break;\r\n                        case 1:\r\n                            pickResult.pickedSprite.actionManager.processTrigger(\r\n                                Constants.ACTION_OnCenterPickTrigger,\r\n                                ActionEvent.CreateNewFromSprite(pickResult.pickedSprite, scene, evt)\r\n                            );\r\n                            break;\r\n                        case 2:\r\n                            pickResult.pickedSprite.actionManager.processTrigger(\r\n                                Constants.ACTION_OnRightPickTrigger,\r\n                                ActionEvent.CreateNewFromSprite(pickResult.pickedSprite, scene, evt)\r\n                            );\r\n                            break;\r\n                    }\r\n                    if (pickResult.pickedSprite.actionManager) {\r\n                        pickResult.pickedSprite.actionManager.processTrigger(\r\n                            Constants.ACTION_OnPickDownTrigger,\r\n                            ActionEvent.CreateNewFromSprite(pickResult.pickedSprite, scene, evt)\r\n                        );\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        return pickResult;\r\n    }\r\n\r\n    private _pointerUp(\r\n        unTranslatedPointerX: number,\r\n        unTranslatedPointerY: number,\r\n        pickResult: Nullable<PickingInfo>,\r\n        evt: IPointerEvent,\r\n        doubleClick: boolean\r\n    ): Nullable<PickingInfo> {\r\n        const scene = this.scene;\r\n        if (scene.spriteManagers && scene.spriteManagers.length > 0) {\r\n            const spritePickResult = scene.pickSprite(unTranslatedPointerX, unTranslatedPointerY, this._spritePredicate, false, scene.cameraToUseForPointers || undefined);\r\n\r\n            if (spritePickResult) {\r\n                if (spritePickResult.hit && spritePickResult.pickedSprite) {\r\n                    if (spritePickResult.pickedSprite.actionManager) {\r\n                        spritePickResult.pickedSprite.actionManager.processTrigger(\r\n                            Constants.ACTION_OnPickUpTrigger,\r\n                            ActionEvent.CreateNewFromSprite(spritePickResult.pickedSprite, scene, evt)\r\n                        );\r\n\r\n                        if (spritePickResult.pickedSprite.actionManager) {\r\n                            if (!this.scene._inputManager._isPointerSwiping()) {\r\n                                spritePickResult.pickedSprite.actionManager.processTrigger(\r\n                                    Constants.ACTION_OnPickTrigger,\r\n                                    ActionEvent.CreateNewFromSprite(spritePickResult.pickedSprite, scene, evt)\r\n                                );\r\n                            }\r\n\r\n                            if (doubleClick) {\r\n                                spritePickResult.pickedSprite.actionManager.processTrigger(\r\n                                    Constants.ACTION_OnDoublePickTrigger,\r\n                                    ActionEvent.CreateNewFromSprite(spritePickResult.pickedSprite, scene, evt)\r\n                                );\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                if (scene._pickedDownSprite && scene._pickedDownSprite.actionManager && scene._pickedDownSprite !== spritePickResult.pickedSprite) {\r\n                    scene._pickedDownSprite.actionManager.processTrigger(Constants.ACTION_OnPickOutTrigger, ActionEvent.CreateNewFromSprite(scene._pickedDownSprite, scene, evt));\r\n                }\r\n            }\r\n        }\r\n\r\n        return pickResult;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAGjC,OAAO,EAAE,6BAA6B,EAAE,kCAAkC,EAAE,GAAG,EAAE,MAAM,qBAAqB,CAAC;AAE7G,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;;;;;;;AA0GrD,MAAM,CAAC,cAAc,iJAAC,QAAK,CAAC,SAAS,EAAE,mCAAmC,EAAE;IACxE,GAAG,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,kCAAkC,EAAE,CAAC;YAC/D,MAAM,iCAAiC,GAAG,AAAC,IAAI,CAAC,kCAAkC,GAAG,iKAAI,aAAU,EAAkB,CAAC,CAAC;YACvH,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAG,CAAD,gCAAkC,CAAC,KAAK,EAAE,CAAC,CAAC;QACtF,CAAC;QACD,OAAO,IAAI,CAAC,kCAAkC,CAAC;IACnD,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,iJAAC,QAAK,CAAC,SAAS,EAAE,kCAAkC,EAAE;IACvE,GAAG,EAAE;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE,CAAC;YAC9D,MAAM,gCAAgC,GAAG,AAAC,IAAI,CAAC,iCAAiC,GAAG,IAAI,0KAAU,EAAkB,CAAC,CAAC;YACrH,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAG,CAAD,+BAAiC,CAAC,KAAK,EAAE,CAAC,CAAC;QACrF,CAAC;QACD,OAAO,IAAI,CAAC,iCAAiC,CAAC;IAClD,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;gJAEH,QAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG,SAAU,GAAQ,EAAE,SAAuC,EAAE,SAAmB,EAAE,MAAe;IACpI,IAAI,qKAAC,cAAW,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,WAAW,GAAG,IAAI,CAAC;IAEvB,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;IAC/B,CAAC;IAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxD,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,CAAE,CAAC;YAChF,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAEvD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;gBAC5B,SAAS;YACb,CAAC;YAED,MAAM,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;YAC3E,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;gBACzB,SAAS;YACb,CAAC;YAED,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC/E,SAAS;YACb,CAAC;YAED,WAAW,GAAG,MAAM,CAAC;YAErB,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,WAAW,IAAI,wKAAI,cAAW,EAAE,CAAC;AAC5C,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,yBAAyB,GAAG,SAAU,GAAQ,EAAE,SAAuC,EAAE,MAAe;IACpH,IAAI,qKAAC,cAAW,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,YAAY,GAAkB,EAAE,CAAC;IAErC,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;IAC/B,CAAC;IAED,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxD,IAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE,CAAE,CAAC;YAChF,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAEvD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;gBAC5B,SAAS;YACb,CAAC;YAED,MAAM,OAAO,GAAG,aAAa,CAAC,eAAe,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;YAEtE,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;gBACnB,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAChD,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,YAAY,CAAC;AACxB,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,UAAU,GAAG,SAAU,CAAS,EAAE,CAAS,EAAE,SAAuC,EAAE,SAAmB,EAAE,MAAe;IACtI,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;yKAED,qCAAA,AAAkC,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IAEnF,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACnG,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,GAAG,GAAG,qMAAA,AAA6B,EAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACnE,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,iBAAiB,GAAG,SAAU,GAAQ,EAAE,SAAuC,EAAE,SAAmB,EAAE,MAAe;IACjI,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;IAC/B,CAAC;qKAED,MAAG,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAE5E,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACnG,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;IACrB,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAEF,wJAAK,CAAC,SAAS,CAAC,eAAe,GAAG,SAAU,CAAS,EAAE,CAAS,EAAE,SAAuC,EAAE,MAAe;yKACtH,qCAAkC,AAAlC,EAAmC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,qBAAsB,EAAE,MAAM,CAAC,CAAC;IAEpF,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAsB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAC1F,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,sBAAsB,GAAG,SAAU,GAAQ,EAAE,SAAuC,EAAE,MAAe;IACjH,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;IAC/B,CAAC;qKAED,MAAG,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;IAE5E,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AACzF,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG,SAAU,MAAwB;IACrE,IAAI,IAAI,CAAC,kBAAkB,KAAK,MAAM,EAAE,CAAC;QACrC,OAAO;IACX,CAAC;IAED,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;QACnE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,cAAc,CAAC,IAAA,gKAAA,CAAA,KAAS,CAAC,QAAA,CAAA,mBAAA,CAA0B,EAAE,EAAA,CAAA,QAAW,CAAC,SAAA,EAAA,IAAA,IAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC;IAC/J,CAAC;IAED,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;IACjC,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;QACnE,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,cAAc,CAAC,GAAA,gKAAA,CAAA,KAAS,CAAC,QAAA,CAAA,mBAAA,CAAA,EAA2B,EAAE,CAAA,UAAW,CAAC,OAAA,EAAA,IAAA,MAAmB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAAC;IAChK,CAAC;AACL,CAAC,CAAC;gJAEF,QAAK,CAAC,SAAS,CAAC,oBAAoB,GAAG;IACnC,OAAO,IAAI,CAAC,kBAAkB,CAAC;AACnC,CAAC,CAAC;AAMI,MAAO,oBAAoB;IAiC7B;;OAEG,CACI,QAAQ,GAAA;QACX,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,YAAY,0JAAC,0BAAuB,CAAC,uBAAuB,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACpH,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,YAAY,yJAAC,2BAAuB,CAAC,uBAAuB,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACpH,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,YAAY,0JAAC,0BAAuB,CAAC,qBAAqB,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAClH,CAAC;IAED;;;OAGG,CACI,OAAO,GAAA;IACV,8BAAA,EAAgC,CACpC,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,KAAK,EAAE,CAAC;QACtD,IAAI,CAAC,KAAK,CAAC,iCAAiC,CAAC,KAAK,EAAE,CAAC;QAErD,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QACjD,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QACD,MAAO,cAAc,CAAC,MAAM,CAAE,CAAC;YAC3B,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;QAChC,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,mBAA0C,EAAE,CAAS,EAAE,CAAS,EAAE,SAAmB,EAAE,MAAe,EAAA;QAChI,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,gBAAgB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QACrF,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,CAAC,GAAG,GAAG,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;QACtE,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,YAAY,CAChB,oBAA4B,EAC5B,oBAA4B,EAC5B,UAAiC,EACjC,YAAqB,EACrB,OAA8B,EAAA;QAE9B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,YAAY,EAAE,CAAC;YACf,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,MAAM,CAAC;YACJ,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,KAAK,EAAE,KAAK,CAAC,sBAAsB,IAAI,SAAS,CAAC,CAAC;YAElJ,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC1D,KAAK,CAAC,oBAAoB,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;gBACpD,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,OAAO,EAAE,CAAC;oBACvC,IAAI,KAAK,CAAC,kBAAkB,IAAI,KAAK,CAAC,kBAAkB,CAAC,aAAa,IAAI,KAAK,CAAC,kBAAkB,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;wBAC3H,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,kBAAkB,CAAC,aAAa,CAAC,WAAW,CAAC;oBAC9E,CAAC,MAAM,CAAC;wBACJ,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC;oBAC7C,CAAC;gBACL,CAAC;YACL,CAAC,MAAM,CAAC;gBACJ,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,YAAY,CAAC,oBAA4B,EAAE,oBAA4B,EAAE,UAAiC,EAAE,GAAkB,EAAA;QAClI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC/B,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,EAAE,KAAK,CAAC,sBAAsB,IAAI,SAAS,CAAC,CAAC;YAEnJ,IAAI,UAAU,IAAI,UAAU,CAAC,GAAG,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC1D,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;oBACxC,KAAK,CAAC,iBAAiB,GAAG,UAAU,CAAC,YAAY,CAAC;oBAClD,OAAQ,GAAG,CAAC,MAAM,EAAE,CAAC;wBACjB,KAAK,CAAC;4BACF,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAChD,GAAA,gKAAA,CAAA,MAAS,CAAC,OAAA,CAAA,mBAAwB,CAAA,CAClC,UAAA,CAAW,CAAC,UAAA,EAAA,OAAmB,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CACvE,CAAC;4BACF,MAAM;wBACV,KAAK,CAAC;4BACF,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAChD,GAAA,MAAS,CAAC,yJAAV,CAAA,cAAU,CAAA,mBAAA,CAAA,CAA0B,EACpC,QAAA,GAAW,CAAC,QAAA,EAAA,OAAA,EAAmB,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CACvE,CAAC;4BACF,MAAM;wBACV,KAAK,CAAC;4BACF,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAChD,GAAA,gKAAA,CAAA,MAAS,CAAC,OAAA,CAAA,mBAAA,CAAyB,EACnC,SAAA,EAAW,CAAC,SAAA,EAAA,OAAA,CAAmB,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CACvE,CAAC;4BACF,MAAM;oBACd,CAAC;oBACD,IAAI,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;wBACxC,UAAU,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAChD,GAAA,gKAAA,CAAA,MAAS,CAAC,OAAA,CAAA,mBAAwB,CAAA,CAClC,UAAA,CAAW,CAAC,UAAA,EAAA,OAAmB,CAAC,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CACvE,CAAC;oBACN,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,UAAU,CACd,oBAA4B,EAC5B,oBAA4B,EAC5B,UAAiC,EACjC,GAAkB,EAClB,WAAoB,EAAA;QAEpB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,MAAM,gBAAgB,GAAG,KAAK,CAAC,UAAU,CAAC,oBAAoB,EAAE,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,EAAE,KAAK,EAAE,KAAK,CAAC,sBAAsB,IAAI,SAAS,CAAC,CAAC;YAE/J,IAAI,gBAAgB,EAAE,CAAC;gBACnB,IAAI,gBAAgB,CAAC,GAAG,IAAI,gBAAgB,CAAC,YAAY,EAAE,CAAC;oBACxD,IAAI,gBAAgB,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;wBAC9C,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CACtD,GAAA,gKAAA,CAAA,MAAS,CAAC,OAAA,CAAA,iBAAsB,EAChC,CAAA,UAAW,CAAC,MAAA,YAAA,CAAmB,CAAC,OAAA,SAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CAC7E,CAAC;wBAEF,IAAI,gBAAgB,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;4BAC9C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE,EAAE,CAAC;gCAChD,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CACtD,GAAA,gKAAA,CAAA,MAAS,CAAC,OAAA,CAAA,eAAoB,EAC9B,EAAA,CAAA,QAAW,CAAC,QAAA,WAAmB,CAAC,EAAA,OAAA,OAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CAC7E,CAAC;4BACN,CAAC;4BAED,IAAI,WAAW,EAAE,CAAC;gCACd,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CACtD,GAAA,gKAAA,CAAA,MAAS,CAAC,OAAA,CAAA,mBAAA,CAAA,CAA0B,EACpC,WAAW,CAAC,EAAA,YAAA,EAAA,GAAmB,CAAC,GAAA,aAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,GAAG,CAAC,CAC7E,CAAC;4BACN,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,IAAI,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,iBAAiB,CAAC,aAAa,IAAI,KAAK,CAAC,iBAAiB,KAAK,gBAAgB,CAAC,YAAY,EAAE,CAAC;oBAChI,KAAK,CAAC,iBAAiB,CAAC,aAAa,CAAC,cAAc,CAAC,IAAA,gKAAA,CAAA,KAAS,CAAC,QAAA,CAAA,iBAAuB,EAAE,CAAA,MAAA,IAAW,CAAC,YAAA,EAAA,KAAmB,CAAC,CAAA,IAAK,CAAC,iBAAiB,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;gBAClK,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IA/KD;;;OAGG,CACH,YAAY,KAAY,CAAA;QAjBxB;;WAEG,CACa,IAAA,CAAA,IAAI,4JAAG,0BAAuB,CAAC,WAAW,CAAC;QAevD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAsB,CAAC;QACnD,gDAAgD;QAChD,IAAI,CAAC,KAAK,CAAC,qBAAqB,oKAAG,MAAG,CAAC,CAAC,kKAAC,MAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3D,IAAI,CAAC,KAAK,CAAC,kCAAkC,GAAG,iKAAI,aAAU,EAAS,CAAC;QACxE,IAAI,CAAC,KAAK,CAAC,iCAAiC,GAAG,iKAAI,aAAU,EAAS,CAAC;QACvE,IAAI,CAAC,gBAAgB,GAAG,CAAC,MAAc,EAAW,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;gBACxB,OAAO,KAAK,CAAC;YACjB,CAAC;YACD,OAAO,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,aAAa,CAAC,kBAAkB,CAAC;QACxE,CAAC,CAAC;IACN,CAAC;CA+JJ", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Sprites/spriteRenderer.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Sprites/spriteRenderer.ts"], "sourcesContent": ["import type { Nullable } from \"../types\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport type { IMatrixLike } from \"../Maths/math.like\";\r\nimport type { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport type { DataBuffer } from \"../Buffers/dataBuffer\";\r\nimport { Buffer, VertexBuffer } from \"../Buffers/buffer\";\r\nimport { DrawWrapper } from \"../Materials/drawWrapper\";\r\nimport type { ThinSprite } from \"./thinSprite\";\r\nimport type { ISize } from \"../Maths/math.size\";\r\n\r\nimport type { ThinTexture } from \"../Materials/Textures/thinTexture\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { ThinEngine } from \"../Engines/thinEngine\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { BindLogDepth } from \"../Materials/materialHelper.functions\";\r\nimport { ShaderLanguage } from \"../Materials/shaderLanguage\";\r\n\r\n/**\r\n * Options for the SpriteRenderer\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface SpriteRendererOptions {\r\n    /**\r\n     * Sets a boolean indicating if the renderer must render sprites with pixel perfect rendering.\r\n     * In this mode, sprites are rendered as \"pixel art\", which means that they appear as pixelated but remain stable when moving or when rotated or scaled.\r\n     * Note that for this mode to work as expected, the sprite texture must use the BILINEAR sampling mode, not NEAREST!\r\n     * Default is false.\r\n     */\r\n    pixelPerfect?: boolean;\r\n}\r\n\r\n/**\r\n * Class used to render sprites.\r\n *\r\n * It can be used either to render Sprites or ThinSprites with ThinEngine only.\r\n */\r\nexport class SpriteRenderer {\r\n    /**\r\n     * Force all the sprites to compile to glsl even on WebGPU engines.\r\n     * False by default. This is mostly meant for backward compatibility.\r\n     */\r\n    public static ForceGLSL = false;\r\n    /**\r\n     * Defines the texture of the spritesheet\r\n     */\r\n    public texture: Nullable<ThinTexture>;\r\n\r\n    /**\r\n     * Defines the default width of a cell in the spritesheet\r\n     */\r\n    public cellWidth: number;\r\n\r\n    /**\r\n     * Defines the default height of a cell in the spritesheet\r\n     */\r\n    public cellHeight: number;\r\n\r\n    /**\r\n     * Blend mode use to render the particle, it can be any of\r\n     * the static Constants.ALPHA_x properties provided in this class.\r\n     * Default value is Constants.ALPHA_COMBINE\r\n     */\r\n    public blendMode = Constants.ALPHA_COMBINE;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if alpha mode is automatically\r\n     * reset.\r\n     */\r\n    public autoResetAlpha = true;\r\n\r\n    /**\r\n     * Disables writing to the depth buffer when rendering the sprites.\r\n     * It can be handy to disable depth writing when using textures without alpha channel\r\n     * and setting some specific blend modes.\r\n     */\r\n    public disableDepthWrite: boolean = false;\r\n\r\n    private _fogEnabled = true;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the manager must consider scene fog when rendering\r\n     */\r\n    public get fogEnabled() {\r\n        return this._fogEnabled;\r\n    }\r\n\r\n    public set fogEnabled(value: boolean) {\r\n        if (this._fogEnabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._fogEnabled = value;\r\n        this._createEffects();\r\n    }\r\n\r\n    protected _useLogarithmicDepth: boolean;\r\n\r\n    /**\r\n     * In case the depth buffer does not allow enough depth precision for your scene (might be the case in large scenes)\r\n     * You can try switching to logarithmic depth.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/logarithmicDepthBuffer\r\n     */\r\n    public get useLogarithmicDepth(): boolean {\r\n        return this._useLogarithmicDepth;\r\n    }\r\n\r\n    public set useLogarithmicDepth(value: boolean) {\r\n        const fragmentDepthSupported = !!this._scene?.getEngine().getCaps().fragmentDepthSupported;\r\n\r\n        if (value && !fragmentDepthSupported) {\r\n            Logger.Warn(\"Logarithmic depth has been requested for a sprite renderer on a device that doesn't support it.\");\r\n        }\r\n\r\n        this._useLogarithmicDepth = value && fragmentDepthSupported;\r\n        this._createEffects();\r\n    }\r\n\r\n    /**\r\n     * Gets the capacity of the manager\r\n     */\r\n    public get capacity() {\r\n        return this._capacity;\r\n    }\r\n\r\n    private _pixelPerfect = false;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the renderer must render sprites with pixel perfect rendering\r\n     * Note that pixel perfect mode is not supported in WebGL 1\r\n     */\r\n    public get pixelPerfect() {\r\n        return this._pixelPerfect;\r\n    }\r\n\r\n    public set pixelPerfect(value: boolean) {\r\n        if (this._pixelPerfect === value) {\r\n            return;\r\n        }\r\n\r\n        this._pixelPerfect = value;\r\n        this._createEffects();\r\n    }\r\n\r\n    /** Shader language used by the material */\r\n    protected _shaderLanguage = ShaderLanguage.GLSL;\r\n\r\n    /**\r\n     * Gets the shader language used in this renderer.\r\n     */\r\n    public get shaderLanguage(): ShaderLanguage {\r\n        return this._shaderLanguage;\r\n    }\r\n\r\n    private readonly _engine: AbstractEngine;\r\n    private readonly _useVAO: boolean = false;\r\n    private readonly _useInstancing: boolean = false;\r\n    private readonly _scene: Nullable<Scene>;\r\n\r\n    private readonly _capacity: number;\r\n    private readonly _epsilon: number;\r\n\r\n    private _vertexBufferSize: number;\r\n    private _vertexData: Float32Array;\r\n    private _buffer: Buffer;\r\n    private _vertexBuffers: { [key: string]: VertexBuffer } = {};\r\n    private _spriteBuffer: Nullable<Buffer>;\r\n    private _indexBuffer: DataBuffer;\r\n    private _drawWrapperBase: DrawWrapper;\r\n    private _drawWrapperDepth: DrawWrapper;\r\n    private _vertexArrayObject: WebGLVertexArrayObject;\r\n    private _isDisposed = false;\r\n\r\n    /**\r\n     * Creates a new sprite renderer\r\n     * @param engine defines the engine the renderer works with\r\n     * @param capacity defines the maximum allowed number of sprites\r\n     * @param epsilon defines the epsilon value to align texture (0.01 by default)\r\n     * @param scene defines the hosting scene\r\n     * @param rendererOptions options for the sprite renderer\r\n     */\r\n    constructor(engine: AbstractEngine, capacity: number, epsilon: number = 0.01, scene: Nullable<Scene> = null, rendererOptions?: SpriteRendererOptions) {\r\n        this._pixelPerfect = rendererOptions?.pixelPerfect ?? false;\r\n        this._capacity = capacity;\r\n        this._epsilon = epsilon;\r\n\r\n        this._engine = engine;\r\n        this._useInstancing = engine.getCaps().instancedArrays && engine._features.supportSpriteInstancing;\r\n        this._useVAO = engine.getCaps().vertexArrayObject && !engine.disableVertexArrayObjects;\r\n        this._scene = scene;\r\n\r\n        if (!this._useInstancing) {\r\n            this._buildIndexBuffer();\r\n        }\r\n\r\n        // VBO\r\n        // 18 floats per sprite (x, y, z, angle, sizeX, sizeY, offsetX, offsetY, invertU, invertV, cellLeft, cellTop, cellWidth, cellHeight, color r, color g, color b, color a)\r\n        // 16 when using instances\r\n        this._vertexBufferSize = this._useInstancing ? 16 : 18;\r\n        this._vertexData = new Float32Array(capacity * this._vertexBufferSize * (this._useInstancing ? 1 : 4));\r\n        this._buffer = new Buffer(engine, this._vertexData, true, this._vertexBufferSize);\r\n\r\n        const positions = this._buffer.createVertexBuffer(VertexBuffer.PositionKind, 0, 4, this._vertexBufferSize, this._useInstancing);\r\n        const options = this._buffer.createVertexBuffer(\"options\", 4, 2, this._vertexBufferSize, this._useInstancing);\r\n\r\n        let offset = 6;\r\n        let offsets: VertexBuffer;\r\n\r\n        if (this._useInstancing) {\r\n            const spriteData = new Float32Array([\r\n                this._epsilon,\r\n                this._epsilon,\r\n                1 - this._epsilon,\r\n                this._epsilon,\r\n                this._epsilon,\r\n                1 - this._epsilon,\r\n                1 - this._epsilon,\r\n                1 - this._epsilon,\r\n            ]);\r\n            this._spriteBuffer = new Buffer(engine, spriteData, false, 2);\r\n            offsets = this._spriteBuffer.createVertexBuffer(\"offsets\", 0, 2);\r\n        } else {\r\n            offsets = this._buffer.createVertexBuffer(\"offsets\", offset, 2, this._vertexBufferSize, this._useInstancing);\r\n            offset += 2;\r\n        }\r\n\r\n        const inverts = this._buffer.createVertexBuffer(\"inverts\", offset, 2, this._vertexBufferSize, this._useInstancing);\r\n        const cellInfo = this._buffer.createVertexBuffer(\"cellInfo\", offset + 2, 4, this._vertexBufferSize, this._useInstancing);\r\n        const colors = this._buffer.createVertexBuffer(VertexBuffer.ColorKind, offset + 6, 4, this._vertexBufferSize, this._useInstancing);\r\n\r\n        this._vertexBuffers[VertexBuffer.PositionKind] = positions;\r\n        this._vertexBuffers[\"options\"] = options;\r\n        this._vertexBuffers[\"offsets\"] = offsets;\r\n        this._vertexBuffers[\"inverts\"] = inverts;\r\n        this._vertexBuffers[\"cellInfo\"] = cellInfo;\r\n        this._vertexBuffers[VertexBuffer.ColorKind] = colors;\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._initShaderSourceAsync();\r\n    }\r\n\r\n    private _shadersLoaded = false;\r\n\r\n    private async _initShaderSourceAsync() {\r\n        const engine = this._engine;\r\n\r\n        if (engine.isWebGPU && !SpriteRenderer.ForceGLSL) {\r\n            this._shaderLanguage = ShaderLanguage.WGSL;\r\n\r\n            await Promise.all([import(\"../ShadersWGSL/sprites.vertex\"), import(\"../ShadersWGSL/sprites.fragment\")]);\r\n        } else {\r\n            await Promise.all([import(\"../Shaders/sprites.vertex\"), import(\"../Shaders/sprites.fragment\")]);\r\n        }\r\n\r\n        this._shadersLoaded = true;\r\n        this._createEffects();\r\n    }\r\n\r\n    private _createEffects() {\r\n        if (this._isDisposed || !this._shadersLoaded) {\r\n            return;\r\n        }\r\n\r\n        this._drawWrapperBase?.dispose();\r\n        this._drawWrapperDepth?.dispose();\r\n\r\n        this._drawWrapperBase = new DrawWrapper(this._engine);\r\n        this._drawWrapperDepth = new DrawWrapper(this._engine, false);\r\n\r\n        if (this._drawWrapperBase.drawContext) {\r\n            this._drawWrapperBase.drawContext.useInstancing = this._useInstancing;\r\n        }\r\n        if (this._drawWrapperDepth.drawContext) {\r\n            this._drawWrapperDepth.drawContext.useInstancing = this._useInstancing;\r\n        }\r\n\r\n        let defines = \"\";\r\n\r\n        if (this._pixelPerfect) {\r\n            defines += \"#define PIXEL_PERFECT\\n\";\r\n        }\r\n        if (this._scene && this._scene.fogEnabled && this._scene.fogMode !== 0 && this._fogEnabled) {\r\n            defines += \"#define FOG\\n\";\r\n        }\r\n        if (this._useLogarithmicDepth) {\r\n            defines += \"#define LOGARITHMICDEPTH\\n\";\r\n        }\r\n\r\n        this._drawWrapperBase.effect = this._engine.createEffect(\r\n            \"sprites\",\r\n            [VertexBuffer.PositionKind, \"options\", \"offsets\", \"inverts\", \"cellInfo\", VertexBuffer.ColorKind],\r\n            [\"view\", \"projection\", \"textureInfos\", \"alphaTest\", \"vFogInfos\", \"vFogColor\", \"logarithmicDepthConstant\"],\r\n            [\"diffuseSampler\"],\r\n            defines,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            undefined,\r\n            this._shaderLanguage\r\n        );\r\n\r\n        this._drawWrapperDepth.effect = this._drawWrapperBase.effect;\r\n        this._drawWrapperBase.effect._refCount++;\r\n        this._drawWrapperDepth.materialContext = this._drawWrapperBase.materialContext;\r\n    }\r\n\r\n    /**\r\n     * Render all child sprites\r\n     * @param sprites defines the list of sprites to render\r\n     * @param deltaTime defines the time since last frame\r\n     * @param viewMatrix defines the viewMatrix to use to render the sprites\r\n     * @param projectionMatrix defines the projectionMatrix to use to render the sprites\r\n     * @param customSpriteUpdate defines a custom function to update the sprites data before they render\r\n     */\r\n    public render(\r\n        sprites: ThinSprite[],\r\n        deltaTime: number,\r\n        viewMatrix: IMatrixLike,\r\n        projectionMatrix: IMatrixLike,\r\n        customSpriteUpdate: Nullable<(sprite: ThinSprite, baseSize: ISize) => void> = null\r\n    ): void {\r\n        if (!this._shadersLoaded || !this.texture || !this.texture.isReady() || !sprites.length) {\r\n            return;\r\n        }\r\n\r\n        const drawWrapper = this._drawWrapperBase;\r\n        const drawWrapperDepth = this._drawWrapperDepth;\r\n        const shouldRenderFog = this.fogEnabled && this._scene && this._scene.fogEnabled && this._scene.fogMode !== 0;\r\n\r\n        const effect = drawWrapper.effect!;\r\n\r\n        // Check\r\n        if (!effect.isReady()) {\r\n            return;\r\n        }\r\n\r\n        const engine = this._engine;\r\n        const useRightHandedSystem = !!(this._scene && this._scene.useRightHandedSystem);\r\n\r\n        // Sprites\r\n        const max = Math.min(this._capacity, sprites.length);\r\n\r\n        let offset = 0;\r\n        let noSprite = true;\r\n        for (let index = 0; index < max; index++) {\r\n            const sprite = sprites[index];\r\n            if (!sprite || !sprite.isVisible) {\r\n                continue;\r\n            }\r\n\r\n            noSprite = false;\r\n            sprite._animate(deltaTime);\r\n            const baseSize = this.texture.getBaseSize(); // This could be change by the user inside the animate callback (like onAnimationEnd)\r\n\r\n            this._appendSpriteVertex(offset++, sprite, 0, 0, baseSize, useRightHandedSystem, customSpriteUpdate);\r\n            if (!this._useInstancing) {\r\n                this._appendSpriteVertex(offset++, sprite, 1, 0, baseSize, useRightHandedSystem, customSpriteUpdate);\r\n                this._appendSpriteVertex(offset++, sprite, 1, 1, baseSize, useRightHandedSystem, customSpriteUpdate);\r\n                this._appendSpriteVertex(offset++, sprite, 0, 1, baseSize, useRightHandedSystem, customSpriteUpdate);\r\n            }\r\n        }\r\n\r\n        if (noSprite) {\r\n            return;\r\n        }\r\n\r\n        this._buffer.update(this._vertexData);\r\n\r\n        const culling = !!engine.depthCullingState.cull;\r\n        const zOffset = engine.depthCullingState.zOffset;\r\n        const zOffsetUnits = engine.depthCullingState.zOffsetUnits;\r\n\r\n        engine.setState(culling, zOffset, false, false, undefined, undefined, zOffsetUnits);\r\n\r\n        // Render\r\n        engine.enableEffect(drawWrapper);\r\n\r\n        effect.setTexture(\"diffuseSampler\", this.texture);\r\n        effect.setMatrix(\"view\", viewMatrix);\r\n        effect.setMatrix(\"projection\", projectionMatrix);\r\n\r\n        // Scene Info\r\n        if (shouldRenderFog) {\r\n            const scene = this._scene;\r\n\r\n            // Fog\r\n            effect.setFloat4(\"vFogInfos\", scene.fogMode, scene.fogStart, scene.fogEnd, scene.fogDensity);\r\n            effect.setColor3(\"vFogColor\", scene.fogColor);\r\n        }\r\n\r\n        // Log. depth\r\n        if (this.useLogarithmicDepth && this._scene) {\r\n            BindLogDepth(drawWrapper.defines, effect, this._scene);\r\n        }\r\n\r\n        if (this._useVAO) {\r\n            if (!this._vertexArrayObject) {\r\n                this._vertexArrayObject = (engine as ThinEngine).recordVertexArrayObject(this._vertexBuffers, this._indexBuffer, effect);\r\n            }\r\n            (engine as ThinEngine).bindVertexArrayObject(this._vertexArrayObject, this._indexBuffer);\r\n        } else {\r\n            // VBOs\r\n            engine.bindBuffers(this._vertexBuffers, this._indexBuffer, effect);\r\n        }\r\n\r\n        // Draw order\r\n        engine.depthCullingState.depthFunc = engine.useReverseDepthBuffer ? Constants.GEQUAL : Constants.LEQUAL;\r\n        if (!this.disableDepthWrite) {\r\n            effect.setBool(\"alphaTest\", true);\r\n            engine.setColorWrite(false);\r\n            engine.enableEffect(drawWrapperDepth);\r\n            if (this._useInstancing) {\r\n                engine.drawArraysType(Constants.MATERIAL_TriangleStripDrawMode, 0, 4, offset);\r\n            } else {\r\n                engine.drawElementsType(Constants.MATERIAL_TriangleFillMode, 0, (offset / 4) * 6);\r\n            }\r\n            engine.enableEffect(drawWrapper);\r\n            engine.setColorWrite(true);\r\n            effect.setBool(\"alphaTest\", false);\r\n        }\r\n\r\n        engine.setAlphaMode(this.blendMode);\r\n        if (this._useInstancing) {\r\n            engine.drawArraysType(Constants.MATERIAL_TriangleStripDrawMode, 0, 4, offset);\r\n        } else {\r\n            engine.drawElementsType(Constants.MATERIAL_TriangleFillMode, 0, (offset / 4) * 6);\r\n        }\r\n\r\n        if (this.autoResetAlpha) {\r\n            engine.setAlphaMode(Constants.ALPHA_DISABLE);\r\n        }\r\n\r\n        // Restore Right Handed\r\n        if (useRightHandedSystem) {\r\n            this._scene.getEngine().setState(culling, zOffset, false, true, undefined, undefined, zOffsetUnits);\r\n        }\r\n\r\n        engine.unbindInstanceAttributes();\r\n    }\r\n\r\n    private _appendSpriteVertex(\r\n        index: number,\r\n        sprite: ThinSprite,\r\n        offsetX: number,\r\n        offsetY: number,\r\n        baseSize: ISize,\r\n        useRightHandedSystem: boolean,\r\n        customSpriteUpdate: Nullable<(sprite: ThinSprite, baseSize: ISize) => void>\r\n    ): void {\r\n        let arrayOffset = index * this._vertexBufferSize;\r\n\r\n        if (offsetX === 0) {\r\n            offsetX = this._epsilon;\r\n        } else if (offsetX === 1) {\r\n            offsetX = 1 - this._epsilon;\r\n        }\r\n\r\n        if (offsetY === 0) {\r\n            offsetY = this._epsilon;\r\n        } else if (offsetY === 1) {\r\n            offsetY = 1 - this._epsilon;\r\n        }\r\n\r\n        if (customSpriteUpdate) {\r\n            customSpriteUpdate(sprite, baseSize);\r\n        } else {\r\n            if (!sprite.cellIndex) {\r\n                sprite.cellIndex = 0;\r\n            }\r\n\r\n            const rowSize = baseSize.width / this.cellWidth;\r\n            const offset = (sprite.cellIndex / rowSize) >> 0;\r\n            sprite._xOffset = ((sprite.cellIndex - offset * rowSize) * this.cellWidth) / baseSize.width;\r\n            sprite._yOffset = (offset * this.cellHeight) / baseSize.height;\r\n            sprite._xSize = this.cellWidth;\r\n            sprite._ySize = this.cellHeight;\r\n        }\r\n\r\n        // Positions\r\n        this._vertexData[arrayOffset] = sprite.position.x;\r\n        this._vertexData[arrayOffset + 1] = sprite.position.y;\r\n        this._vertexData[arrayOffset + 2] = sprite.position.z;\r\n        this._vertexData[arrayOffset + 3] = sprite.angle;\r\n        // Options\r\n        this._vertexData[arrayOffset + 4] = sprite.width;\r\n        this._vertexData[arrayOffset + 5] = sprite.height;\r\n\r\n        if (!this._useInstancing) {\r\n            this._vertexData[arrayOffset + 6] = offsetX;\r\n            this._vertexData[arrayOffset + 7] = offsetY;\r\n        } else {\r\n            arrayOffset -= 2;\r\n        }\r\n\r\n        // Inverts according to Right Handed\r\n        if (useRightHandedSystem) {\r\n            this._vertexData[arrayOffset + 8] = sprite.invertU ? 0 : 1;\r\n        } else {\r\n            this._vertexData[arrayOffset + 8] = sprite.invertU ? 1 : 0;\r\n        }\r\n\r\n        this._vertexData[arrayOffset + 9] = sprite.invertV ? 1 : 0;\r\n\r\n        this._vertexData[arrayOffset + 10] = sprite._xOffset;\r\n        this._vertexData[arrayOffset + 11] = sprite._yOffset;\r\n        this._vertexData[arrayOffset + 12] = sprite._xSize / baseSize.width;\r\n        this._vertexData[arrayOffset + 13] = sprite._ySize / baseSize.height;\r\n\r\n        // Color\r\n        this._vertexData[arrayOffset + 14] = sprite.color.r;\r\n        this._vertexData[arrayOffset + 15] = sprite.color.g;\r\n        this._vertexData[arrayOffset + 16] = sprite.color.b;\r\n        this._vertexData[arrayOffset + 17] = sprite.color.a;\r\n    }\r\n\r\n    private _buildIndexBuffer(): void {\r\n        const indices = [];\r\n        let index = 0;\r\n        for (let count = 0; count < this._capacity; count++) {\r\n            indices.push(index);\r\n            indices.push(index + 1);\r\n            indices.push(index + 2);\r\n            indices.push(index);\r\n            indices.push(index + 2);\r\n            indices.push(index + 3);\r\n            index += 4;\r\n        }\r\n\r\n        this._indexBuffer = this._engine.createIndexBuffer(indices);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the renderer (after a context lost, for eg)\r\n     */\r\n    public rebuild(): void {\r\n        if (this._indexBuffer) {\r\n            this._buildIndexBuffer();\r\n        }\r\n\r\n        if (this._useVAO) {\r\n            this._vertexArrayObject = undefined as any;\r\n        }\r\n\r\n        this._buffer._rebuild();\r\n\r\n        for (const key in this._vertexBuffers) {\r\n            const vertexBuffer = this._vertexBuffers[key];\r\n            vertexBuffer._rebuild();\r\n        }\r\n\r\n        this._spriteBuffer?._rebuild();\r\n    }\r\n\r\n    /**\r\n     * Release associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this._buffer) {\r\n            this._buffer.dispose();\r\n            (<any>this._buffer) = null;\r\n        }\r\n\r\n        if (this._spriteBuffer) {\r\n            this._spriteBuffer.dispose();\r\n            (<any>this._spriteBuffer) = null;\r\n        }\r\n\r\n        if (this._indexBuffer) {\r\n            this._engine._releaseBuffer(this._indexBuffer);\r\n            (<any>this._indexBuffer) = null;\r\n        }\r\n\r\n        if (this._vertexArrayObject) {\r\n            (this._engine as ThinEngine).releaseVertexArrayObject(this._vertexArrayObject);\r\n            (<any>this._vertexArrayObject) = null;\r\n        }\r\n\r\n        if (this.texture) {\r\n            this.texture.dispose();\r\n            (<any>this.texture) = null;\r\n        }\r\n        this._drawWrapperBase?.dispose();\r\n        this._drawWrapperDepth?.dispose();\r\n        this._isDisposed = true;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AAKA,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACzD,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AAOvD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,uCAAuC,CAAC;;;;;AAsB/D,MAAO,cAAc;IA2CvB;;OAEG,CACH,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAc,EAAA;QAChC,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAID;;;;OAIG,CACH,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,IAAW,mBAAmB,CAAC,KAAc,EAAA;YACR;QAAjC,MAAM,sBAAsB,GAAG,CAAC,sBAAK,CAAC,MAAM,8DAAE,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,sBAAsB,CAAC;QAE3F,IAAI,KAAK,IAAI,CAAC,sBAAsB,EAAE,CAAC;qKACnC,SAAM,CAAC,IAAI,CAAC,iGAAiG,CAAC,CAAC;QACnH,CAAC;QAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,IAAI,sBAAsB,CAAC;QAC5D,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAID;;;OAGG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,IAAW,YAAY,CAAC,KAAc,EAAA;QAClC,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE,CAAC;YAC/B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAKD;;OAEG,CACH,IAAW,cAAc,GAAA;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IA2FO,KAAK,CAAC,sBAAsB,GAAA;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5B,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;YAC/C,IAAI,CAAC,eAAe,GAAA,EAAA,uBAAA,EAAsB,CAAC;YAE3C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,+BAA+B,CAAC,EAAE,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC;;;aAAA;QAC5G,CAAC,MAAM,CAAC;YACJ,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,EAAE,MAAM,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC;;;aAAA;QACpG,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1B,CAAC;IAEO,cAAc,GAAA;;QAClB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAC3C,OAAO;QACX,CAAC;sCAEG,CAAC,gBAAgB,2DAArB,uBAAuB,OAAO,EAAE,CAAC;uCAC7B,CAAC,iBAAiB,4DAAtB,wBAAwB,OAAO,EAAE,CAAC;QAElC,IAAI,CAAC,gBAAgB,GAAG,IAAI,iLAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,iBAAiB,GAAG,uKAAI,cAAW,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACpC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1E,CAAC;QACD,IAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3E,CAAC;QAED,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO,IAAI,yBAAyB,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzF,OAAO,IAAI,eAAe,CAAC;QAC/B,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,OAAO,IAAI,4BAA4B,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CACpD,SAAS,EACT;wKAAC,eAAY,CAAC,YAAY;YAAE,SAAS;YAAE,SAAS;YAAE,SAAS;YAAE,UAAU;wKAAE,eAAY,CAAC,SAAS;SAAC,EAChG;YAAC,MAAM;YAAE,YAAY;YAAE,cAAc;YAAE,WAAW;YAAE,WAAW;YAAE,WAAW;YAAE,0BAA0B;SAAC,EACzG;YAAC,gBAAgB;SAAC,EAClB,OAAO,EACP,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,IAAI,CAAC,eAAe,CACvB,CAAC;QAEF,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAC7D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACzC,IAAI,CAAC,iBAAiB,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;IACnF,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CACT,OAAqB,EACrB,SAAiB,EACjB,UAAuB,EACvB,gBAA6B,EACqD;iCAAlF,iEAA8E,IAAI;QAElF,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtF,OAAO;QACX,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAChD,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,CAAC,CAAC;QAE9G,MAAM,MAAM,GAAG,WAAW,CAAC,MAAO,CAAC;QAEnC,QAAQ;QACR,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,MAAM,oBAAoB,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;QAEjF,UAAU;QACV,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAErD,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,GAAG,EAAE,KAAK,EAAE,CAAE,CAAC;YACvC,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9B,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC/B,SAAS;YACb,CAAC;YAED,QAAQ,GAAG,KAAK,CAAC;YACjB,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,qFAAqF;YAElI,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;YACrG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvB,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;gBACrG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;gBACrG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;YACzG,CAAC;QACL,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACX,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEtC,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC;QAChD,MAAM,OAAO,GAAG,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC;QACjD,MAAM,YAAY,GAAG,MAAM,CAAC,iBAAiB,CAAC,YAAY,CAAC;QAE3D,MAAM,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QAEpF,SAAS;QACT,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAEjC,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACrC,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;QAEjD,aAAa;QACb,IAAI,eAAe,EAAE,CAAC;YAClB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YAE1B,MAAM;YACN,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;YAC7F,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1C,sMAAA,AAAY,EAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3B,IAAI,CAAC,kBAAkB,GAAI,MAAqB,CAAC,uBAAuB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAC7H,CAAC;YACA,MAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7F,CAAC,MAAM,CAAC;YACJ,OAAO;YACP,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QACvE,CAAC;QAED,aAAa;QACb,MAAM,CAAC,iBAAiB,CAAC,SAAS,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,MAAA,GAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;QACxG,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAClC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;YACtC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM,CAAC,cAAc,CAAC,GAAA,GAAA,GAAS,CAAC,8BAA8B,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YAClF,CAAC,MAAM,CAAC;gBACJ,MAAM,CAAC,gBAAgB,CAAC,GAAA,GAAA,EAAS,CAAC,MAAA,IAAA,cAAyB,EAAE,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACtF,CAAC;YACD,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACjC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACvC,CAAC;QAED,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,MAAM,CAAC,cAAc,CAAC,GAAA,GAAA,GAAS,CAAC,8BAA8B,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QAClF,CAAC,MAAM,CAAC;YACJ,MAAM,CAAC,gBAAgB,CAAC,GAAA,GAAA,EAAS,CAAC,MAAA,IAAA,cAAyB,EAAE,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC;QAED,uBAAuB;QACvB,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;QACxG,CAAC;QAED,MAAM,CAAC,wBAAwB,EAAE,CAAC;IACtC,CAAC;IAEO,mBAAmB,CACvB,KAAa,EACb,MAAkB,EAClB,OAAe,EACf,OAAe,EACf,QAAe,EACf,oBAA6B,EAC7B,kBAA2E,EAAA;QAE3E,IAAI,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAEjD,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YAChB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChC,CAAC;QAED,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YAChB,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,CAAC,MAAM,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;QAChC,CAAC;QAED,IAAI,kBAAkB,EAAE,CAAC;YACrB,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACzC,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACpB,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;YACzB,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;YAChD,MAAM,MAAM,GAAG,AAAC,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,GAAI,CAAC,CAAC;YACjD,MAAM,CAAC,QAAQ,GAAI,AAAD,CAAE,MAAM,CAAC,SAAS,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,EAAG,QAAQ,CAAC,KAAK,CAAC;YAC5F,MAAM,CAAC,QAAQ,GAAI,AAAD,MAAO,GAAG,IAAI,CAAC,UAAU,CAAC,EAAG,QAAQ,CAAC,MAAM,CAAC;YAC/D,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;YAC/B,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,CAAC;QAED,YAAY;QACZ,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;QACjD,UAAU;QACV,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;QAElD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;YAC5C,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;QAChD,CAAC,MAAM,CAAC;YACJ,WAAW,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,oCAAoC;QACpC,IAAI,oBAAoB,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3D,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;QACrD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;QACrD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;QACpE,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAErE,QAAQ;QACR,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IACxD,CAAC;IAEO,iBAAiB,GAAA;QACrB,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAE,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACxB,KAAK,IAAI,CAAC,CAAC;QACf,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;;QACV,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,kBAAkB,GAAG,SAAgB,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAExB,IAAK,MAAM,GAAG,IAAI,IAAI,CAAC,cAAc,CAAE,CAAC;YACpC,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAC9C,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC;mCAEG,CAAC,aAAa,wDAAlB,oBAAoB,QAAQ,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;YAyBV,wBACA;QAzBA,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAQ,GAAG,IAAI,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,aAAc,GAAG,IAAI,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzC,IAAI,CAAC,YAAa,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACzB,IAAI,CAAC,OAAsB,CAAC,wBAAwB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACzE,IAAI,CAAC,kBAAmB,GAAG,IAAI,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAQ,GAAG,IAAI,CAAC;QAC/B,CAAC;sCACG,CAAC,gBAAgB,kFAAE,OAAO,EAAE,CAAC;uCAC7B,CAAC,iBAAiB,oFAAE,OAAO,EAAE,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IA3ZD;;;;;;;OAOG,CACH,YAAY,MAAsB,EAAE,QAAgB,EAAE,UAAkB,IAAI,EAAE,QAAyB,IAAI,EAAE,eAAuC,CAAA;QA3HpJ;;;;WAIG,CACI,IAAA,CAAA,SAAS,GAAG,SAAS,CAAC,aAAa,CAAC;QAE3C;;;WAGG,CACI,IAAA,CAAA,cAAc,GAAG,IAAI,CAAC;QAE7B;;;;WAIG,CACI,IAAA,CAAA,iBAAiB,GAAY,KAAK,CAAC;QAElC,IAAA,CAAA,WAAW,GAAG,IAAI,CAAC;QA+CnB,IAAA,CAAA,aAAa,GAAG,KAAK,CAAC;QAmB9B,yCAAA,EAA2C,CACjC,IAAA,CAAA,eAAe,GAAA,EAAA,uBAAA,GAAuB;QAU/B,IAAA,CAAA,OAAO,GAAY,KAAK,CAAC;QACzB,IAAA,CAAA,cAAc,GAAY,KAAK,CAAC;QASzC,IAAA,CAAA,cAAc,GAAoC,CAAA,CAAE,CAAC;QAMrD,IAAA,CAAA,WAAW,GAAG,KAAK,CAAC;QAsEpB,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;YA3DN,eAAe;QAApC,IAAI,CAAC,aAAa,sHAAoB,YAAY,yFAAI,KAAK,CAAC;QAC5D,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,eAAe,IAAI,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC;QACnG,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC;QACvF,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC;QAED,MAAM;QACN,wKAAwK;QACxK,0BAA0B;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvG,IAAI,CAAC,OAAO,GAAG,gKAAI,SAAM,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAElF,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,6JAAC,eAAY,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAChI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAE9G,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,OAAqB,CAAC;QAE1B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC;gBAChC,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,QAAQ;gBACb,CAAC,GAAG,IAAI,CAAC,QAAQ;gBACjB,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,QAAQ;gBACb,CAAC,GAAG,IAAI,CAAC,QAAQ;gBACjB,CAAC,GAAG,IAAI,CAAC,QAAQ;gBACjB,CAAC,GAAG,IAAI,CAAC,QAAQ;aACpB,CAAC,CAAC;YACH,IAAI,CAAC,aAAa,GAAG,gKAAI,SAAM,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YAC9D,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrE,CAAC,MAAM,CAAC;YACJ,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC7G,MAAM,IAAI,CAAC,CAAC;QAChB,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACnH,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACzH,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,6JAAC,eAAY,CAAC,SAAS,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEnI,IAAI,CAAC,cAAc,6JAAC,eAAY,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC;QAC3D,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QACzC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QACzC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;QACzC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;QAC3C,IAAI,CAAC,cAAc,6JAAC,eAAY,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;QAErD,mEAAmE;QACnE,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;;AAzMD;;;GAGG,CACW,eAAA,SAAS,GAAG,KAAK,AAAR,CAAS", "debugId": null}}, {"offset": {"line": 1000, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Sprites/spriteManager.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Sprites/spriteManager.ts"], "sourcesContent": ["import type { IDisposable, Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector3, TmpVectors, Matrix } from \"../Maths/math.vector\";\r\nimport { Sprite } from \"./sprite\";\r\nimport { SpriteSceneComponent } from \"./spriteSceneComponent\";\r\nimport type { InternalSpriteAugmentedScene } from \"./spriteSceneComponent\";\r\nimport { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport type { Camera } from \"../Cameras/camera\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { Tools } from \"../Misc/tools\";\r\nimport { WebRequest } from \"../Misc/webRequest\";\r\nimport type { SpriteRendererOptions } from \"./spriteRenderer\";\r\nimport { Sprite<PERSON>enderer } from \"./spriteRenderer\";\r\nimport type { ThinSprite } from \"./thinSprite\";\r\nimport type { ISize } from \"../Maths/math.size\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\nimport type { Ray } from \"../Culling/ray\";\r\n\r\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Reflect\r\ndeclare const Reflect: any;\r\n\r\n/**\r\n * Defines the minimum interface to fulfill in order to be a sprite manager.\r\n */\r\nexport interface ISpriteManager extends IDisposable {\r\n    /**\r\n     * Gets or sets the unique id of the sprite manager\r\n     */\r\n    uniqueId: number;\r\n\r\n    /**\r\n     * Gets manager's name\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * Restricts the camera to viewing objects with the same layerMask.\r\n     * A camera with a layerMask of 1 will render spriteManager.layerMask & camera.layerMask!== 0\r\n     */\r\n    layerMask: number;\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the mesh can be picked (by scene.pick for instance or through actions). Default is true\r\n     */\r\n    isPickable: boolean;\r\n\r\n    /**\r\n     * Gets the hosting scene\r\n     */\r\n    scene: Scene;\r\n\r\n    /**\r\n     * Specifies the rendering group id for this mesh (0 by default)\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/materials/advanced/transparent_rendering#rendering-groups\r\n     */\r\n    renderingGroupId: number;\r\n\r\n    /**\r\n     * Defines the list of sprites managed by the manager.\r\n     */\r\n    sprites: Array<Sprite>;\r\n\r\n    /**\r\n     * Gets or sets the spritesheet texture\r\n     */\r\n    texture: Texture;\r\n\r\n    /** Defines the default width of a cell in the spritesheet */\r\n    cellWidth: number;\r\n    /** Defines the default height of a cell in the spritesheet */\r\n    cellHeight: number;\r\n\r\n    /** @internal */\r\n    _wasDispatched: boolean;\r\n\r\n    /**\r\n     * Tests the intersection of a sprite with a specific ray.\r\n     * @param ray The ray we are sending to test the collision\r\n     * @param camera The camera space we are sending rays in\r\n     * @param predicate A predicate allowing excluding sprites from the list of object to test\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @returns picking info or null.\r\n     */\r\n    intersects(ray: Ray, camera: Camera, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean): Nullable<PickingInfo>;\r\n\r\n    /**\r\n     * Intersects the sprites with a ray\r\n     * @param ray defines the ray to intersect with\r\n     * @param camera defines the current active camera\r\n     * @param predicate defines a predicate used to select candidate sprites\r\n     * @returns null if no hit or a PickingInfo array\r\n     */\r\n    multiIntersects(ray: Ray, camera: Camera, predicate?: (sprite: Sprite) => boolean): Nullable<PickingInfo[]>;\r\n\r\n    /**\r\n     * Renders the list of sprites on screen.\r\n     */\r\n    render(): void;\r\n\r\n    /**\r\n     * Rebuilds the manager (after a context lost, for eg)\r\n     */\r\n    rebuild(): void;\r\n\r\n    /**\r\n     * Serializes the sprite manager to a JSON object\r\n     */\r\n    serialize(serializeTexture?: boolean): any;\r\n}\r\n\r\n/**\r\n * Options for the SpriteManager\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface SpriteManagerOptions {\r\n    /** Options for the sprite renderer */\r\n    spriteRendererOptions: SpriteRendererOptions;\r\n}\r\n\r\n/**\r\n * Class used to manage multiple sprites on the same spritesheet\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n */\r\nexport class SpriteManager implements ISpriteManager {\r\n    /** Define the Url to load snippets */\r\n    public static SnippetUrl = Constants.SnippetUrl;\r\n\r\n    /** Snippet ID if the manager was created from the snippet server */\r\n    public snippetId: string;\r\n\r\n    /** Gets the list of sprites */\r\n    public sprites: Sprite[] = [];\r\n    /** Gets or sets the rendering group id (0 by default) */\r\n    public renderingGroupId = 0;\r\n    /** Gets or sets camera layer mask */\r\n    public layerMask: number = 0x0fffffff;\r\n    /** Gets or sets a boolean indicating if the sprites are pickable */\r\n    public isPickable = false;\r\n\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the sprite manager\r\n     */\r\n    public metadata: any = null;\r\n\r\n    /** @internal */\r\n    public _wasDispatched = false;\r\n\r\n    /**\r\n     * An event triggered when the manager is disposed.\r\n     */\r\n    public onDisposeObservable = new Observable<SpriteManager>();\r\n\r\n    /**\r\n     * Callback called when the manager is disposed\r\n     */\r\n    public set onDispose(callback: () => void) {\r\n        if (this._onDisposeObserver) {\r\n            this.onDisposeObservable.remove(this._onDisposeObserver);\r\n        }\r\n        this._onDisposeObserver = this.onDisposeObservable.add(callback);\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the unique id of the sprite\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * Gets the array of sprites\r\n     */\r\n    public get children() {\r\n        return this.sprites;\r\n    }\r\n\r\n    /**\r\n     * Gets the hosting scene\r\n     */\r\n    public get scene() {\r\n        return this._scene;\r\n    }\r\n\r\n    /**\r\n     * Gets the capacity of the manager\r\n     */\r\n    public get capacity() {\r\n        return this._spriteRenderer.capacity;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the spritesheet texture\r\n     */\r\n    public get texture(): Texture {\r\n        return this._spriteRenderer.texture as Texture;\r\n    }\r\n    public set texture(value: Texture) {\r\n        value.wrapU = Texture.CLAMP_ADDRESSMODE;\r\n        value.wrapV = Texture.CLAMP_ADDRESSMODE;\r\n        this._spriteRenderer.texture = value;\r\n        this._textureContent = null;\r\n    }\r\n\r\n    /** Defines the default width of a cell in the spritesheet */\r\n    public get cellWidth(): number {\r\n        return this._spriteRenderer.cellWidth;\r\n    }\r\n    public set cellWidth(value: number) {\r\n        this._spriteRenderer.cellWidth = value;\r\n    }\r\n\r\n    /** Defines the default height of a cell in the spritesheet */\r\n    public get cellHeight(): number {\r\n        return this._spriteRenderer.cellHeight;\r\n    }\r\n    public set cellHeight(value: number) {\r\n        this._spriteRenderer.cellHeight = value;\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the manager must consider scene fog when rendering */\r\n    public get fogEnabled(): boolean {\r\n        return this._spriteRenderer.fogEnabled;\r\n    }\r\n    public set fogEnabled(value: boolean) {\r\n        this._spriteRenderer.fogEnabled = value;\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the manager must use logarithmic depth when rendering */\r\n    public get useLogarithmicDepth(): boolean {\r\n        return this._spriteRenderer.useLogarithmicDepth;\r\n    }\r\n    public set useLogarithmicDepth(value: boolean) {\r\n        this._spriteRenderer.useLogarithmicDepth = value;\r\n    }\r\n\r\n    /**\r\n     * Blend mode use to render the particle, it can be any of\r\n     * the static Constants.ALPHA_x properties provided in this class.\r\n     * Default value is Constants.ALPHA_COMBINE\r\n     */\r\n    public get blendMode() {\r\n        return this._spriteRenderer.blendMode;\r\n    }\r\n    public set blendMode(blendMode: number) {\r\n        this._spriteRenderer.blendMode = blendMode;\r\n    }\r\n\r\n    private _disableDepthWrite: boolean = false;\r\n    /** Disables writing to the depth buffer when rendering the sprites.\r\n     *  It can be handy to disable depth writing when using textures without alpha channel\r\n     *  and setting some specific blend modes.\r\n     */\r\n    public get disableDepthWrite() {\r\n        return this._disableDepthWrite;\r\n    }\r\n\r\n    public set disableDepthWrite(value: boolean) {\r\n        this._disableDepthWrite = value;\r\n        this._spriteRenderer.disableDepthWrite = value;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a boolean indicating if the renderer must render sprites with pixel perfect rendering\r\n     * In this mode, sprites are rendered as \"pixel art\", which means that they appear as pixelated but remain stable when moving or when rotated or scaled.\r\n     * Note that for this mode to work as expected, the sprite texture must use the BILINEAR sampling mode, not NEAREST!\r\n     */\r\n    public get pixelPerfect() {\r\n        return this._spriteRenderer.pixelPerfect;\r\n    }\r\n\r\n    public set pixelPerfect(value: boolean) {\r\n        this._spriteRenderer.pixelPerfect = value;\r\n        if (value && this.texture.samplingMode !== Constants.TEXTURE_TRILINEAR_SAMPLINGMODE) {\r\n            this.texture.updateSamplingMode(Constants.TEXTURE_TRILINEAR_SAMPLINGMODE);\r\n        }\r\n    }\r\n\r\n    private _spriteRenderer: SpriteRenderer;\r\n    /** Associative array from JSON sprite data file */\r\n    private _cellData: any;\r\n    /** Array of sprite names from JSON sprite data file */\r\n    private _spriteMap: Array<string>;\r\n    /** True when packed cell data from JSON file is ready*/\r\n    private _packedAndReady: boolean = false;\r\n    private _textureContent: Nullable<Uint8Array>;\r\n    private _onDisposeObserver: Nullable<Observer<SpriteManager>>;\r\n    private _fromPacked: boolean;\r\n    private _scene: InternalSpriteAugmentedScene;\r\n\r\n    /**\r\n     * Creates a new sprite manager\r\n     * @param name defines the manager's name\r\n     * @param imgUrl defines the sprite sheet url\r\n     * @param capacity defines the maximum allowed number of sprites\r\n     * @param cellSize defines the size of a sprite cell\r\n     * @param scene defines the hosting scene\r\n     * @param epsilon defines the epsilon value to align texture (0.01 by default)\r\n     * @param samplingMode defines the sampling mode to use with spritesheet\r\n     * @param fromPacked set to false; do not alter\r\n     * @param spriteJSON null otherwise a JSON object defining sprite sheet data; do not alter\r\n     * @param options options used to create the SpriteManager instance\r\n     */\r\n    constructor(\r\n        /** defines the manager's name */\r\n        public name: string,\r\n        imgUrl: string,\r\n        capacity: number,\r\n        cellSize: any,\r\n        scene: Scene,\r\n        epsilon: number = 0.01,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        fromPacked: boolean = false,\r\n        spriteJSON: null | string = null,\r\n        options?: SpriteManagerOptions\r\n    ) {\r\n        if (!scene) {\r\n            scene = EngineStore.LastCreatedScene!;\r\n        }\r\n\r\n        if (!scene._getComponent(SceneComponentConstants.NAME_SPRITE)) {\r\n            scene._addComponent(new SpriteSceneComponent(scene));\r\n        }\r\n        this._fromPacked = fromPacked;\r\n\r\n        this._scene = scene as InternalSpriteAugmentedScene;\r\n        const engine = this._scene.getEngine();\r\n        this._spriteRenderer = new SpriteRenderer(engine, capacity, epsilon, scene, options?.spriteRendererOptions);\r\n\r\n        if (cellSize.width && cellSize.height) {\r\n            this.cellWidth = cellSize.width;\r\n            this.cellHeight = cellSize.height;\r\n        } else if (cellSize !== undefined) {\r\n            this.cellWidth = cellSize;\r\n            this.cellHeight = cellSize;\r\n        } else {\r\n            this._spriteRenderer = <any>null;\r\n            return;\r\n        }\r\n\r\n        this._scene.spriteManagers && this._scene.spriteManagers.push(this);\r\n        this.uniqueId = this.scene.getUniqueId();\r\n\r\n        if (imgUrl) {\r\n            this.texture = new Texture(imgUrl, scene, true, false, samplingMode);\r\n        }\r\n\r\n        if (this._fromPacked) {\r\n            this._makePacked(imgUrl, spriteJSON);\r\n        }\r\n\r\n        this._scene._onNewSpriteManagerAddedObservable?.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"SpriteManager\"\r\n     * @returns \"SpriteManager\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"SpriteManager\";\r\n    }\r\n\r\n    private _makePacked(imgUrl: string, spriteJSON: any) {\r\n        if (spriteJSON !== null) {\r\n            try {\r\n                //Get the JSON and Check its structure.  If its an array parse it if its a JSON string etc...\r\n                let celldata: any;\r\n                if (typeof spriteJSON === \"string\") {\r\n                    celldata = JSON.parse(spriteJSON);\r\n                } else {\r\n                    celldata = spriteJSON;\r\n                }\r\n\r\n                if (celldata.frames.length) {\r\n                    const frametemp: any = {};\r\n                    for (let i = 0; i < celldata.frames.length; i++) {\r\n                        const _f = celldata.frames[i];\r\n                        if (typeof Object.keys(_f)[0] !== \"string\") {\r\n                            throw new Error(\"Invalid JSON Format.  Check the frame values and make sure the name is the first parameter.\");\r\n                        }\r\n\r\n                        const name: string = _f[Object.keys(_f)[0]];\r\n                        frametemp[name] = _f;\r\n                    }\r\n                    celldata.frames = frametemp;\r\n                }\r\n\r\n                const spritemap = <string[]>Reflect.ownKeys(celldata.frames);\r\n\r\n                this._spriteMap = spritemap;\r\n                this._packedAndReady = true;\r\n                this._cellData = celldata.frames;\r\n            } catch (e) {\r\n                this._fromPacked = false;\r\n                this._packedAndReady = false;\r\n                throw new Error(\"Invalid JSON from string. Spritesheet managed with constant cell size.\");\r\n            }\r\n        } else {\r\n            const re = /\\./g;\r\n            let li: number;\r\n            do {\r\n                li = re.lastIndex;\r\n                re.test(imgUrl);\r\n            } while (re.lastIndex > 0);\r\n            const jsonUrl = imgUrl.substring(0, li - 1) + \".json\";\r\n            const onerror = () => {\r\n                Logger.Error(\"JSON ERROR: Unable to load JSON file.\");\r\n                this._fromPacked = false;\r\n                this._packedAndReady = false;\r\n            };\r\n            const onload = (data: string | ArrayBuffer) => {\r\n                try {\r\n                    const celldata = JSON.parse(data as string);\r\n                    const spritemap = <string[]>Reflect.ownKeys(celldata.frames);\r\n                    this._spriteMap = spritemap;\r\n                    this._packedAndReady = true;\r\n                    this._cellData = celldata.frames;\r\n                } catch (e) {\r\n                    this._fromPacked = false;\r\n                    this._packedAndReady = false;\r\n                    throw new Error(\"Invalid JSON format. Please check documentation for format specifications.\");\r\n                }\r\n            };\r\n            Tools.LoadFile(jsonUrl, onload, undefined, undefined, false, onerror);\r\n        }\r\n    }\r\n\r\n    private _checkTextureAlpha(sprite: Sprite, ray: Ray, distance: number, min: Vector3, max: Vector3) {\r\n        if (!sprite.useAlphaForPicking || !this.texture?.isReady()) {\r\n            return true;\r\n        }\r\n\r\n        const textureSize = this.texture.getSize();\r\n        if (!this._textureContent) {\r\n            this._textureContent = new Uint8Array(textureSize.width * textureSize.height * 4);\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this.texture.readPixels(0, 0, this._textureContent);\r\n        }\r\n\r\n        const contactPoint = TmpVectors.Vector3[0];\r\n\r\n        contactPoint.copyFrom(ray.direction);\r\n\r\n        contactPoint.normalize();\r\n        contactPoint.scaleInPlace(distance);\r\n        contactPoint.addInPlace(ray.origin);\r\n\r\n        const contactPointU = (contactPoint.x - min.x) / (max.x - min.x);\r\n        const contactPointV = 1.0 - (contactPoint.y - min.y) / (max.y - min.y);\r\n\r\n        const u = (sprite._xOffset * textureSize.width + contactPointU * sprite._xSize) | 0;\r\n        const v = (sprite._yOffset * textureSize.height + contactPointV * sprite._ySize) | 0;\r\n\r\n        const alpha = this._textureContent[(u + v * textureSize.width) * 4 + 3];\r\n\r\n        return alpha > 0.5;\r\n    }\r\n\r\n    /**\r\n     * Intersects the sprites with a ray\r\n     * @param ray defines the ray to intersect with\r\n     * @param camera defines the current active camera\r\n     * @param predicate defines a predicate used to select candidate sprites\r\n     * @param fastCheck defines if a fast check only must be done (the first potential sprite is will be used and not the closer)\r\n     * @returns null if no hit or a PickingInfo\r\n     */\r\n    public intersects(ray: Ray, camera: Camera, predicate?: (sprite: Sprite) => boolean, fastCheck?: boolean): Nullable<PickingInfo> {\r\n        const count = Math.min(this.capacity, this.sprites.length);\r\n        const min = Vector3.Zero();\r\n        const max = Vector3.Zero();\r\n        let distance = Number.MAX_VALUE;\r\n        let currentSprite: Nullable<Sprite> = null;\r\n        const pickedPoint = TmpVectors.Vector3[0];\r\n        const cameraSpacePosition = TmpVectors.Vector3[1];\r\n        const cameraView = camera.getViewMatrix();\r\n        let activeRay: Ray = ray;\r\n        let pickedRay: Ray = ray;\r\n\r\n        for (let index = 0; index < count; index++) {\r\n            const sprite = this.sprites[index];\r\n            if (!sprite) {\r\n                continue;\r\n            }\r\n\r\n            if (predicate) {\r\n                if (!predicate(sprite)) {\r\n                    continue;\r\n                }\r\n            } else if (!sprite.isPickable) {\r\n                continue;\r\n            }\r\n\r\n            Vector3.TransformCoordinatesToRef(sprite.position, cameraView, cameraSpacePosition);\r\n\r\n            if (sprite.angle) {\r\n                // Create a rotation matrix to rotate the ray to the sprite's rotation\r\n                Matrix.TranslationToRef(-cameraSpacePosition.x, -cameraSpacePosition.y, 0, TmpVectors.Matrix[1]);\r\n                Matrix.TranslationToRef(cameraSpacePosition.x, cameraSpacePosition.y, 0, TmpVectors.Matrix[2]);\r\n                Matrix.RotationZToRef(-sprite.angle, TmpVectors.Matrix[3]);\r\n\r\n                // inv translation x rotation x translation\r\n                TmpVectors.Matrix[1].multiplyToRef(TmpVectors.Matrix[3], TmpVectors.Matrix[4]);\r\n                TmpVectors.Matrix[4].multiplyToRef(TmpVectors.Matrix[2], TmpVectors.Matrix[0]);\r\n\r\n                activeRay = ray.clone();\r\n                Vector3.TransformCoordinatesToRef(ray.origin, TmpVectors.Matrix[0], activeRay.origin);\r\n                Vector3.TransformNormalToRef(ray.direction, TmpVectors.Matrix[0], activeRay.direction);\r\n            } else {\r\n                activeRay = ray;\r\n            }\r\n\r\n            min.copyFromFloats(cameraSpacePosition.x - sprite.width / 2, cameraSpacePosition.y - sprite.height / 2, cameraSpacePosition.z);\r\n            max.copyFromFloats(cameraSpacePosition.x + sprite.width / 2, cameraSpacePosition.y + sprite.height / 2, cameraSpacePosition.z);\r\n\r\n            if (activeRay.intersectsBoxMinMax(min, max)) {\r\n                const currentDistance = Vector3.Distance(cameraSpacePosition, activeRay.origin);\r\n\r\n                if (distance > currentDistance) {\r\n                    if (!this._checkTextureAlpha(sprite, activeRay, currentDistance, min, max)) {\r\n                        continue;\r\n                    }\r\n\r\n                    pickedRay = activeRay;\r\n                    distance = currentDistance;\r\n                    currentSprite = sprite;\r\n\r\n                    if (fastCheck) {\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (currentSprite) {\r\n            const result = new PickingInfo();\r\n\r\n            cameraView.invertToRef(TmpVectors.Matrix[0]);\r\n            result.hit = true;\r\n            result.pickedSprite = currentSprite;\r\n            result.distance = distance;\r\n\r\n            // Get picked point\r\n            const direction = TmpVectors.Vector3[2];\r\n            direction.copyFrom(pickedRay.direction);\r\n            direction.normalize();\r\n            direction.scaleInPlace(distance);\r\n\r\n            pickedRay.origin.addToRef(direction, pickedPoint);\r\n            result.pickedPoint = Vector3.TransformCoordinates(pickedPoint, TmpVectors.Matrix[0]);\r\n\r\n            return result;\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    /**\r\n     * Intersects the sprites with a ray\r\n     * @param ray defines the ray to intersect with\r\n     * @param camera defines the current active camera\r\n     * @param predicate defines a predicate used to select candidate sprites\r\n     * @returns null if no hit or a PickingInfo array\r\n     */\r\n    public multiIntersects(ray: Ray, camera: Camera, predicate?: (sprite: Sprite) => boolean): Nullable<PickingInfo[]> {\r\n        const count = Math.min(this.capacity, this.sprites.length);\r\n        const min = Vector3.Zero();\r\n        const max = Vector3.Zero();\r\n        let distance: number;\r\n        const results: Nullable<PickingInfo[]> = [];\r\n        const pickedPoint = TmpVectors.Vector3[0].copyFromFloats(0, 0, 0);\r\n        const cameraSpacePosition = TmpVectors.Vector3[1].copyFromFloats(0, 0, 0);\r\n        const cameraView = camera.getViewMatrix();\r\n\r\n        for (let index = 0; index < count; index++) {\r\n            const sprite = this.sprites[index];\r\n            if (!sprite) {\r\n                continue;\r\n            }\r\n\r\n            if (predicate) {\r\n                if (!predicate(sprite)) {\r\n                    continue;\r\n                }\r\n            } else if (!sprite.isPickable) {\r\n                continue;\r\n            }\r\n\r\n            Vector3.TransformCoordinatesToRef(sprite.position, cameraView, cameraSpacePosition);\r\n\r\n            min.copyFromFloats(cameraSpacePosition.x - sprite.width / 2, cameraSpacePosition.y - sprite.height / 2, cameraSpacePosition.z);\r\n            max.copyFromFloats(cameraSpacePosition.x + sprite.width / 2, cameraSpacePosition.y + sprite.height / 2, cameraSpacePosition.z);\r\n\r\n            if (ray.intersectsBoxMinMax(min, max)) {\r\n                distance = Vector3.Distance(cameraSpacePosition, ray.origin);\r\n\r\n                if (!this._checkTextureAlpha(sprite, ray, distance, min, max)) {\r\n                    continue;\r\n                }\r\n\r\n                const result = new PickingInfo();\r\n                results.push(result);\r\n\r\n                cameraView.invertToRef(TmpVectors.Matrix[0]);\r\n                result.hit = true;\r\n                result.pickedSprite = sprite;\r\n                result.distance = distance;\r\n\r\n                // Get picked point\r\n                const direction = TmpVectors.Vector3[2];\r\n                direction.copyFrom(ray.direction);\r\n                direction.normalize();\r\n                direction.scaleInPlace(distance);\r\n\r\n                ray.origin.addToRef(direction, pickedPoint);\r\n                result.pickedPoint = Vector3.TransformCoordinates(pickedPoint, TmpVectors.Matrix[0]);\r\n            }\r\n        }\r\n\r\n        return results;\r\n    }\r\n\r\n    /**\r\n     * Render all child sprites\r\n     */\r\n    public render(): void {\r\n        // Check\r\n        if (this._fromPacked && (!this._packedAndReady || !this._spriteMap || !this._cellData)) {\r\n            return;\r\n        }\r\n\r\n        const engine = this._scene.getEngine();\r\n        const deltaTime = engine.getDeltaTime();\r\n        if (this._packedAndReady) {\r\n            this._spriteRenderer.render(this.sprites, deltaTime, this._scene.getViewMatrix(), this._scene.getProjectionMatrix(), this._customUpdate);\r\n        } else {\r\n            this._spriteRenderer.render(this.sprites, deltaTime, this._scene.getViewMatrix(), this._scene.getProjectionMatrix());\r\n        }\r\n    }\r\n\r\n    private _customUpdate = (sprite: ThinSprite, baseSize: ISize): void => {\r\n        if (!sprite.cellRef) {\r\n            sprite.cellIndex = 0;\r\n        }\r\n        const num = sprite.cellIndex;\r\n        if (typeof num === \"number\" && isFinite(num) && Math.floor(num) === num) {\r\n            sprite.cellRef = this._spriteMap[sprite.cellIndex];\r\n        }\r\n        sprite._xOffset = this._cellData[sprite.cellRef].frame.x / baseSize.width;\r\n        sprite._yOffset = this._cellData[sprite.cellRef].frame.y / baseSize.height;\r\n        sprite._xSize = this._cellData[sprite.cellRef].frame.w;\r\n        sprite._ySize = this._cellData[sprite.cellRef].frame.h;\r\n    };\r\n\r\n    /**\r\n     * Rebuilds the manager (after a context lost, for eg)\r\n     */\r\n    public rebuild(): void {\r\n        this._spriteRenderer?.rebuild();\r\n    }\r\n\r\n    /**\r\n     * Release associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (this._spriteRenderer) {\r\n            this._spriteRenderer.dispose();\r\n            (<any>this._spriteRenderer) = null;\r\n        }\r\n\r\n        this._textureContent = null;\r\n\r\n        // Remove from scene\r\n        if (this._scene.spriteManagers) {\r\n            const index = this._scene.spriteManagers.indexOf(this);\r\n            this._scene.spriteManagers.splice(index, 1);\r\n            this._scene._onSpriteManagerRemovedObservable?.notifyObservers(this);\r\n        }\r\n\r\n        // Callback\r\n        this.onDisposeObservable.notifyObservers(this);\r\n        this.onDisposeObservable.clear();\r\n\r\n        this.metadata = null;\r\n    }\r\n\r\n    /**\r\n     * Serializes the sprite manager to a JSON object\r\n     * @param serializeTexture defines if the texture must be serialized as well\r\n     * @returns the JSON object\r\n     */\r\n    public serialize(serializeTexture = false): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.capacity = this.capacity;\r\n        serializationObject.cellWidth = this.cellWidth;\r\n        serializationObject.cellHeight = this.cellHeight;\r\n        serializationObject.fogEnabled = this.fogEnabled;\r\n        serializationObject.blendMode = this.blendMode;\r\n        serializationObject.disableDepthWrite = this.disableDepthWrite;\r\n        serializationObject.pixelPerfect = this.pixelPerfect;\r\n        serializationObject.useLogarithmicDepth = this.useLogarithmicDepth;\r\n\r\n        if (this.texture) {\r\n            if (serializeTexture) {\r\n                serializationObject.texture = this.texture.serialize();\r\n            } else {\r\n                serializationObject.textureUrl = this.texture.name;\r\n                serializationObject.invertY = this.texture._invertY;\r\n            }\r\n        }\r\n\r\n        serializationObject.sprites = [];\r\n\r\n        for (const sprite of this.sprites) {\r\n            serializationObject.sprites.push(sprite.serialize());\r\n        }\r\n\r\n        serializationObject.metadata = this.metadata;\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parses a JSON object to create a new sprite manager.\r\n     * @param parsedManager The JSON object to parse\r\n     * @param scene The scene to create the sprite manager\r\n     * @param rootUrl The root url to use to load external dependencies like texture\r\n     * @returns the new sprite manager\r\n     */\r\n    public static Parse(parsedManager: any, scene: Scene, rootUrl: string): SpriteManager {\r\n        const manager = new SpriteManager(\r\n            parsedManager.name,\r\n            \"\",\r\n            parsedManager.capacity,\r\n            {\r\n                width: parsedManager.cellWidth,\r\n                height: parsedManager.cellHeight,\r\n            },\r\n            scene\r\n        );\r\n\r\n        if (parsedManager.fogEnabled !== undefined) {\r\n            manager.fogEnabled = parsedManager.fogEnabled;\r\n        }\r\n        if (parsedManager.blendMode !== undefined) {\r\n            manager.blendMode = parsedManager.blendMode;\r\n        }\r\n        if (parsedManager.disableDepthWrite !== undefined) {\r\n            manager.disableDepthWrite = parsedManager.disableDepthWrite;\r\n        }\r\n        if (parsedManager.pixelPerfect !== undefined) {\r\n            manager.pixelPerfect = parsedManager.pixelPerfect;\r\n        }\r\n        if (parsedManager.useLogarithmicDepth !== undefined) {\r\n            manager.useLogarithmicDepth = parsedManager.useLogarithmicDepth;\r\n        }\r\n\r\n        if (parsedManager.metadata !== undefined) {\r\n            manager.metadata = parsedManager.metadata;\r\n        }\r\n\r\n        if (parsedManager.texture) {\r\n            manager.texture = Texture.Parse(parsedManager.texture, scene, rootUrl) as Texture;\r\n        } else if (parsedManager.textureName) {\r\n            manager.texture = new Texture(rootUrl + parsedManager.textureUrl, scene, false, parsedManager.invertY !== undefined ? parsedManager.invertY : true);\r\n        }\r\n\r\n        for (const parsedSprite of parsedManager.sprites) {\r\n            Sprite.Parse(parsedSprite, manager);\r\n        }\r\n\r\n        return manager;\r\n    }\r\n\r\n    /**\r\n     * Creates a sprite manager from a snippet saved in a remote file\r\n     * @param name defines the name of the sprite manager to create (can be null or empty to use the one from the json data)\r\n     * @param url defines the url to load from\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a promise that will resolve to the new sprite manager\r\n     */\r\n    public static async ParseFromFileAsync(name: Nullable<string>, url: string, scene: Scene, rootUrl: string = \"\"): Promise<SpriteManager> {\r\n        return await new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const serializationObject = JSON.parse(request.responseText);\r\n                        const output = SpriteManager.Parse(serializationObject, scene || EngineStore.LastCreatedScene, rootUrl);\r\n\r\n                        if (name) {\r\n                            output.name = name;\r\n                        }\r\n\r\n                        resolve(output);\r\n                    } else {\r\n                        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                        reject(\"Unable to load the sprite manager\");\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", url);\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a sprite manager from a snippet saved by the sprite editor\r\n     * @param snippetId defines the snippet to load (can be set to _BLANK to create a default one)\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a promise that will resolve to the new sprite manager\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    public static ParseFromSnippetAsync(snippetId: string, scene: Scene, rootUrl: string = \"\"): Promise<SpriteManager> {\r\n        if (snippetId === \"_BLANK\") {\r\n            return Promise.resolve(new SpriteManager(\"Default sprite manager\", \"//playground.babylonjs.com/textures/player.png\", 500, 64, scene));\r\n        }\r\n\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);\r\n                        const serializationObject = JSON.parse(snippet.spriteManager);\r\n                        const output = SpriteManager.Parse(serializationObject, scene || EngineStore.LastCreatedScene, rootUrl);\r\n\r\n                        output.snippetId = snippetId;\r\n\r\n                        resolve(output);\r\n                    } else {\r\n                        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\r\n                        reject(\"Unable to load the snippet \" + snippetId);\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", this.SnippetUrl + \"/\" + snippetId.replace(/#/g, \"/\"));\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates a sprite manager from a snippet saved by the sprite editor\r\n     * @deprecated Please use ParseFromSnippetAsync instead\r\n     * @param snippetId defines the snippet to load (can be set to _BLANK to create a default one)\r\n     * @param scene defines the hosting scene\r\n     * @param rootUrl defines the root URL to use to load textures and relative dependencies\r\n     * @returns a promise that will resolve to the new sprite manager\r\n     */\r\n    public static CreateFromSnippetAsync = SpriteManager.ParseFromSnippetAsync;\r\n}\r\n"], "names": [], "mappings": ";;;AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AACnE,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAGlD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;;;;;;;;;;;;;AA8G/C,MAAO,aAAa;IA6BtB;;OAEG,CACH,IAAW,SAAS,CAAC,QAAoB,EAAA;QACrC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACrE,CAAC;IAOD;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG,CACH,IAAW,KAAK,GAAA;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG,CACH,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;IACzC,CAAC;IAED;;OAEG,CACH,IAAW,OAAO,GAAA;QACd,OAAO,IAAI,CAAC,eAAe,CAAC,OAAkB,CAAC;IACnD,CAAC;IACD,IAAW,OAAO,CAAC,KAAc,EAAA;QAC7B,KAAK,CAAC,KAAK,GAAG,qLAAO,CAAC,iBAAiB,CAAC;QACxC,KAAK,CAAC,KAAK,8KAAG,UAAO,CAAC,iBAAiB,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,2DAAA,EAA6D,CAC7D,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;IAC1C,CAAC;IACD,IAAW,SAAS,CAAC,KAAa,EAAA;QAC9B,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC;IAC3C,CAAC;IAED,4DAAA,EAA8D,CAC9D,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;IAC3C,CAAC;IACD,IAAW,UAAU,CAAC,KAAa,EAAA;QAC/B,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED,4FAAA,EAA8F,CAC9F,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;IAC3C,CAAC;IACD,IAAW,UAAU,CAAC,KAAc,EAAA;QAChC,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5C,CAAC;IAED,+FAAA,EAAiG,CACjG,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC;IACpD,CAAC;IACD,IAAW,mBAAmB,CAAC,KAAc,EAAA;QACzC,IAAI,CAAC,eAAe,CAAC,mBAAmB,GAAG,KAAK,CAAC;IACrD,CAAC;IAED;;;;OAIG,CACH,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;IAC1C,CAAC;IACD,IAAW,SAAS,CAAC,SAAiB,EAAA;QAClC,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/C,CAAC;IAGD;;;OAGG,CACH,IAAW,iBAAiB,GAAA;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,IAAW,iBAAiB,CAAC,KAAc,EAAA;QACvC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnD,CAAC;IAED;;;;OAIG,CACH,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;IAC7C,CAAC;IAED,IAAW,YAAY,CAAC,KAAc,EAAA;QAClC,IAAI,CAAC,eAAe,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1C,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,GAAA,MAAS,CAAC,8BAA8B,EAAE,CAAC;YAClF,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QAC9E,CAAC;IACL,CAAC;IA8ED;;;OAGG,CACI,YAAY,GAAA;QACf,OAAO,eAAe,CAAC;IAC3B,CAAC;IAEO,WAAW,CAAC,MAAc,EAAE,UAAe,EAAA;QAC/C,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC;gBACD,6FAA6F;gBAC7F,IAAI,QAAa,CAAC;gBAClB,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;oBACjC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;gBACtC,CAAC,MAAM,CAAC;oBACJ,QAAQ,GAAG,UAAU,CAAC;gBAC1B,CAAC;gBAED,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACzB,MAAM,SAAS,GAAQ,CAAA,CAAE,CAAC;oBAC1B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;wBAC9C,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBAC9B,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;4BACzC,MAAM,IAAI,KAAK,CAAC,6FAA6F,CAAC,CAAC;wBACnH,CAAC;wBAED,MAAM,IAAI,GAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5C,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;oBACzB,CAAC;oBACD,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;gBAChC,CAAC;gBAED,MAAM,SAAS,GAAa,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAE7D,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;gBAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;YACrC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;gBACT,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;gBAC7B,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;YAC9F,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,MAAM,EAAE,GAAG,KAAK,CAAC;YACjB,IAAI,EAAU,CAAC;YACf,GAAG,CAAC;gBACA,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC;gBAClB,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpB,CAAC,OAAQ,EAAE,CAAC,SAAS,GAAG,CAAC,CAAE;YAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;YACtD,MAAM,OAAO,GAAG,GAAG,EAAE;yKACjB,SAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACtD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YACjC,CAAC,CAAC;YACF,MAAM,MAAM,GAAG,CAAC,IAA0B,EAAE,EAAE;gBAC1C,IAAI,CAAC;oBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAc,CAAC,CAAC;oBAC5C,MAAM,SAAS,GAAa,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBAC7D,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;oBAC5B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC5B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;gBACrC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;oBACT,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;oBACzB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;oBAC7B,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;gBAClG,CAAC;YACL,CAAC,CAAC;oKACF,QAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,MAAc,EAAE,GAAQ,EAAE,QAAgB,EAAE,GAAY,EAAE,GAAY,EAAA;;QAC7F,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,mBAAC,IAAI,CAAC,OAAO,gEAAE,OAAO,EAAE,GAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAClF,mEAAmE;YACnE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,YAAY,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE3C,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAErC,YAAY,CAAC,SAAS,EAAE,CAAC;QACzB,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACpC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEpC,MAAM,aAAa,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACjE,MAAM,aAAa,GAAG,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAEvE,MAAM,CAAC,GAAG,AAAC,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,KAAK,GAAG,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,EAAG,CAAC,CAAC;QACpF,MAAM,CAAC,GAAG,AAAC,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,MAAM,GAAG,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,EAAG,CAAC,CAAC;QAErF,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAExE,OAAO,KAAK,GAAG,GAAG,CAAC;IACvB,CAAC;IAED;;;;;;;OAOG,CACI,UAAU,CAAC,GAAQ,EAAE,MAAc,EAAE,SAAuC,EAAE,SAAmB,EAAA;QACpG,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,GAAG,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,MAAM,GAAG,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;QAChC,IAAI,aAAa,GAAqB,IAAI,CAAC;QAC3C,MAAM,WAAW,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,mBAAmB,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClD,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAC1C,IAAI,SAAS,GAAQ,GAAG,CAAC;QACzB,IAAI,SAAS,GAAQ,GAAG,CAAC;QAEzB,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YACzC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,SAAS;YACb,CAAC;YAED,IAAI,SAAS,EAAE,CAAC;gBACZ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBACrB,SAAS;gBACb,CAAC;YACL,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBAC5B,SAAS;YACb,CAAC;8KAED,UAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;YAEpF,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,sEAAsE;kLACtE,SAAM,CAAC,gBAAgB,CAAC,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;kLACjG,SAAM,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;iLAC/F,UAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,KAAK,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE3D,2CAA2C;kLAC3C,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,mKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;kLAC/E,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,aAAa,mKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE/E,SAAS,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;kLACxB,UAAO,CAAC,yBAAyB,CAAC,GAAG,CAAC,MAAM,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;kLACtF,UAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;YAC3F,CAAC,MAAM,CAAC;gBACJ,SAAS,GAAG,GAAG,CAAC;YACpB,CAAC;YAED,GAAG,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAC/H,GAAG,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAE/H,IAAI,SAAS,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;gBAC1C,MAAM,eAAe,qKAAG,UAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;gBAEhF,IAAI,QAAQ,GAAG,eAAe,EAAE,CAAC;oBAC7B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;wBACzE,SAAS;oBACb,CAAC;oBAED,SAAS,GAAG,SAAS,CAAC;oBACtB,QAAQ,GAAG,eAAe,CAAC;oBAC3B,aAAa,GAAG,MAAM,CAAC;oBAEvB,IAAI,SAAS,EAAE,CAAC;wBACZ,MAAM;oBACV,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,aAAa,EAAE,CAAC;YAChB,MAAM,MAAM,GAAG,wKAAI,cAAW,EAAE,CAAC;YAEjC,UAAU,CAAC,WAAW,CAAC,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC;YAClB,MAAM,CAAC,YAAY,GAAG,aAAa,CAAC;YACpC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE3B,mBAAmB;YACnB,MAAM,SAAS,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACxC,SAAS,CAAC,SAAS,EAAE,CAAC;YACtB,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAEjC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAClD,MAAM,CAAC,WAAW,qKAAG,UAAO,CAAC,oBAAoB,CAAC,WAAW,oKAAE,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAErF,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG,CACI,eAAe,CAAC,GAAQ,EAAE,MAAc,EAAE,SAAuC,EAAA;QACpF,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,GAAG,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,MAAM,GAAG,qKAAG,UAAO,CAAC,IAAI,EAAE,CAAC;QAC3B,IAAI,QAAgB,CAAC;QACrB,MAAM,OAAO,GAA4B,EAAE,CAAC;QAC5C,MAAM,WAAW,oKAAG,cAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAClE,MAAM,mBAAmB,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1E,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;QAE1C,IAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,CAAE,CAAC;YACzC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,SAAS;YACb,CAAC;YAED,IAAI,SAAS,EAAE,CAAC;gBACZ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;oBACrB,SAAS;gBACb,CAAC;YACL,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBAC5B,SAAS;YACb,CAAC;8KAED,UAAO,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC;YAEpF,GAAG,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAC/H,GAAG,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAE/H,IAAI,GAAG,CAAC,mBAAmB,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;gBACpC,QAAQ,qKAAG,UAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;gBAE7D,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;oBAC5D,SAAS;gBACb,CAAC;gBAED,MAAM,MAAM,GAAG,wKAAI,cAAW,EAAE,CAAC;gBACjC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAErB,UAAU,CAAC,WAAW,mKAAC,aAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC;gBAClB,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC;gBAC7B,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAE3B,mBAAmB;gBACnB,MAAM,SAAS,qKAAG,aAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACxC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAClC,SAAS,CAAC,SAAS,EAAE,CAAC;gBACtB,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAEjC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBAC5C,MAAM,CAAC,WAAW,qKAAG,UAAO,CAAC,oBAAoB,CAAC,WAAW,EAAE,+KAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACzF,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,QAAQ;QACR,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACrF,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC7I,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC;QACzH,CAAC;IACL,CAAC;IAgBD;;OAEG,CACI,OAAO,GAAA;;qCACN,CAAC,eAAe,0DAApB,sBAAsB,OAAO,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,eAAgB,GAAG,IAAI,CAAC;QACvC,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;QAE5B,oBAAoB;QACpB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;gBAG7B;YAFA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;kEACxC,CAAC,MAAM,CAAC,iCAAiC,kIAAE,eAAe,CAAC,IAAI,CAAC,CAAC;QACzE,CAAC;QAED,WAAW;QACX,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QAEjC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACzB,CAAC;IAED;;;;OAIG,CACI,SAAS,GAAyB;+BAAxB,gBAAgB,iDAAG,KAAK;QACrC,MAAM,mBAAmB,GAAQ,CAAA,CAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjD,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/C,mBAAmB,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC/D,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrD,mBAAmB,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEnE,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,gBAAgB,EAAE,CAAC;gBACnB,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3D,CAAC,MAAM,CAAC;gBACJ,mBAAmB,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBACnD,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;YACxD,CAAC;QACL,CAAC;QAED,mBAAmB,CAAC,OAAO,GAAG,EAAE,CAAC;QAEjC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAE,CAAC;YAChC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAE7C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG,CACI,MAAM,CAAC,KAAK,CAAC,aAAkB,EAAE,KAAY,EAAE,OAAe,EAAA;QACjE,MAAM,OAAO,GAAG,IAAI,aAAa,CAC7B,aAAa,CAAC,IAAI,EAClB,EAAE,EACF,aAAa,CAAC,QAAQ,EACtB;YACI,KAAK,EAAE,aAAa,CAAC,SAAS;YAC9B,MAAM,EAAE,aAAa,CAAC,UAAU;SACnC,EACD,KAAK,CACR,CAAC;QAEF,IAAI,aAAa,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACzC,OAAO,CAAC,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;QAClD,CAAC;QACD,IAAI,aAAa,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACxC,OAAO,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;QAChD,CAAC;QACD,IAAI,aAAa,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YAChD,OAAO,CAAC,iBAAiB,GAAG,aAAa,CAAC,iBAAiB,CAAC;QAChE,CAAC;QACD,IAAI,aAAa,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YAC3C,OAAO,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;QACtD,CAAC;QACD,IAAI,aAAa,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YAClD,OAAO,CAAC,mBAAmB,GAAG,aAAa,CAAC,mBAAmB,CAAC;QACpE,CAAC;QAED,IAAI,aAAa,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;QAC9C,CAAC;QAED,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,CAAC,OAAO,8KAAG,UAAO,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAY,CAAC;QACtF,CAAC,MAAM,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC;YACnC,OAAO,CAAC,OAAO,GAAG,+KAAI,UAAO,CAAC,OAAO,GAAG,aAAa,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACxJ,CAAC;QAED,KAAK,MAAM,YAAY,IAAI,aAAa,CAAC,OAAO,CAAE,CAAC;wKAC/C,SAAM,CAAC,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;;;;OAOG,CACI,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAsB,EAAE,GAAW,EAAE,KAAY,EAAsB;sBAApB,iEAAkB,EAAE;QAC1G,OAAO,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,MAAM,OAAO,GAAG,iKAAI,aAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;oBAC1B,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;wBACxB,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAC7D,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,qKAAI,cAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;wBAExG,IAAI,IAAI,EAAE,CAAC;4BACP,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;wBACvB,CAAC;wBAED,OAAO,CAAC,MAAM,CAAC,CAAC;oBACpB,CAAC,MAAM,CAAC;wBACJ,2EAA2E;wBAC3E,MAAM,CAAC,mCAAmC,CAAC,CAAC;oBAChD,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACzB,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;;OAMG,CACH,2FAA2F;IACpF,MAAM,CAAC,qBAAqB,CAAC,SAAiB,EAAE,KAAY,EAAsB;sBAApB,iEAAkB,EAAE;QACrF,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,wBAAwB,EAAE,gDAAgD,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;QAC1I,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,iKAAI,aAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;oBAC1B,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;wBACxB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;wBACzE,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;wBAC9D,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,qKAAI,cAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;wBAExG,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;wBAE7B,OAAO,CAAC,MAAM,CAAC,CAAC;oBACpB,CAAC,MAAM,CAAC;wBACJ,2EAA2E;wBAC3E,MAAM,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC;oBACtD,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IA1iBD;;;;;;;;;;;;OAYG,CACH,YACI,+BAAA,EAAiC,CAC1B,IAAY,EACnB,MAAc,EACd,QAAgB,EAChB,QAAa,EACb,KAAY,EACZ,UAAkB,IAAI,EACtB,0LAAuB,UAAO,CAAC,sBAAsB,EACrD,aAAsB,KAAK,EAC3B,aAA4B,IAAI,EAChC,OAA8B,CAAA;;QATvB,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;QA5KvB,6BAAA,EAA+B,CACxB,IAAA,CAAA,OAAO,GAAa,EAAE,CAAC;QAC9B,uDAAA,EAAyD,CAClD,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAC;QAC5B,mCAAA,EAAqC,CAC9B,IAAA,CAAA,SAAS,GAAW,UAAU,CAAC;QACtC,kEAAA,EAAoE,CAC7D,IAAA,CAAA,UAAU,GAAG,KAAK,CAAC;QAE1B;;WAEG,CACI,IAAA,CAAA,QAAQ,GAAQ,IAAI,CAAC;QAE5B,cAAA,EAAgB,CACT,IAAA,CAAA,cAAc,GAAG,KAAK,CAAC;QAE9B;;WAEG,CACI,IAAA,CAAA,mBAAmB,GAAG,IAAI,0KAAU,EAAiB,CAAC;QA+FrD,IAAA,CAAA,kBAAkB,GAAY,KAAK,CAAC;QAmC5C,sDAAA,EAAwD,CAChD,IAAA,CAAA,eAAe,GAAY,KAAK,CAAC;QAmWjC,IAAA,CAAA,aAAa,GAAG,CAAC,MAAkB,EAAE,QAAe,EAAQ,EAAE;YAClE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAClB,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC;YACzB,CAAC;YACD,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC;YAC7B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC;gBACtE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvD,CAAC;YACD,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC1E,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC3E,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC;QA/UE,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,KAAK,oKAAG,cAAW,CAAC,gBAAiB,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,aAAa,yJAAC,2BAAuB,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5D,KAAK,CAAC,aAAa,CAAC,8KAAI,uBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,MAAM,GAAG,KAAqC,CAAC;QACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,wKAAI,iBAAc,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,oDAAE,OAAO,CAAE,qBAAqB,CAAC,CAAC;QAE5G,IAAI,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC;YAChC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;QACtC,CAAC,MAAM,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;YAC1B,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;QAC/B,CAAC,MAAM,CAAC;YACJ,IAAI,CAAC,eAAe,GAAQ,IAAI,CAAC;YACjC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAEzC,IAAI,MAAM,EAAE,CAAC;YACT,IAAI,CAAC,OAAO,GAAG,+KAAI,UAAO,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACzC,CAAC;+DAEG,CAAC,MAAM,CAAC,kCAAkC,oFAA9C,gDAAgD,eAAe,CAAC,IAAI,CAAC,CAAC;IAC1E,CAAC;;AAjOD,oCAAA,EAAsC,CACxB,cAAA,UAAU,GAAG,QAAS,CAAC,UAAU,AAAvB,CAAwB;AA8sBhD;;;;;;;GAOG,CACW,cAAA,sBAAsB,GAAG,aAAa,CAAC,qBAAjB,CAAuC", "debugId": null}}, {"offset": {"line": 1597, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Sprites/ISprites.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Sprites/ISprites.ts"], "sourcesContent": ["/**\r\n * Defines the basic options interface of a Sprite Frame Source Size.\r\n */\r\nexport interface ISpriteJSONSpriteSourceSize {\r\n    /**\r\n     * number of the original width of the Frame\r\n     */\r\n    w: number;\r\n\r\n    /**\r\n     * number of the original height of the Frame\r\n     */\r\n    h: number;\r\n}\r\n\r\n/**\r\n * Defines the basic options interface of a Sprite Frame Data.\r\n */\r\nexport interface ISpriteJSONSpriteFrameData {\r\n    /**\r\n     * number of the x offset of the Frame\r\n     */\r\n    x: number;\r\n\r\n    /**\r\n     * number of the y offset of the Frame\r\n     */\r\n    y: number;\r\n\r\n    /**\r\n     * number of the width of the Frame\r\n     */\r\n    w: number;\r\n\r\n    /**\r\n     * number of the height of the Frame\r\n     */\r\n    h: number;\r\n}\r\n\r\n/**\r\n * Defines the basic options interface of a JSON Sprite.\r\n */\r\nexport interface ISpriteJSONSprite {\r\n    /**\r\n     * string name of the Frame\r\n     */\r\n    filename: string;\r\n\r\n    /**\r\n     * ISpriteJSONSpriteFrame basic object of the frame data\r\n     */\r\n    frame: ISpriteJSONSpriteFrameData;\r\n\r\n    /**\r\n     * boolean to flag is the frame was rotated.\r\n     */\r\n    rotated: boolean;\r\n\r\n    /**\r\n     * boolean to flag is the frame was trimmed.\r\n     */\r\n    trimmed: boolean;\r\n\r\n    /**\r\n     * ISpriteJSONSpriteFrame basic object of the source data\r\n     */\r\n    spriteSourceSize: ISpriteJSONSpriteFrameData;\r\n\r\n    /**\r\n     * ISpriteJSONSpriteFrame basic object of the source data\r\n     */\r\n    sourceSize: ISpriteJSONSpriteSourceSize;\r\n}\r\n\r\n/**\r\n * Defines the basic options interface of a JSON atlas.\r\n */\r\nexport interface ISpriteJSONAtlas {\r\n    /**\r\n     * Array of objects that contain the frame data.\r\n     */\r\n    frames: Array<ISpriteJSONSprite>;\r\n\r\n    /**\r\n     * object basic object containing the sprite meta data.\r\n     */\r\n    meta?: object;\r\n}\r\n"], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Sprites/spriteMap.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Sprites/spriteMap.ts"], "sourcesContent": ["import type { IDisposable, Scene } from \"../scene\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Vector2, Vector3 } from \"../Maths/math.vector\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\nimport { RawTexture } from \"../Materials/Textures/rawTexture\";\r\nimport { ShaderMaterial } from \"../Materials/shaderMaterial\";\r\nimport type { Mesh } from \"../Meshes/mesh\";\r\nimport type { PickingInfo } from \"../Collisions/pickingInfo\";\r\nimport type { ISpriteJSONSprite, ISpriteJSONAtlas } from \"./ISprites\";\r\nimport { Effect } from \"../Materials/effect\";\r\n\r\nimport { CreatePlane } from \"../Meshes/Builders/planeBuilder\";\r\nimport \"../Shaders/spriteMap.fragment\";\r\nimport \"../Shaders/spriteMap.vertex\";\r\nimport { Constants } from \"core/Engines/constants\";\r\n\r\nexport enum SpriteMapFrameRotationDirection {\r\n    CCW = 0,\r\n    CW = 1,\r\n}\r\n\r\n/**\r\n * Defines the basic options interface of a SpriteMap\r\n */\r\nexport interface ISpriteMapOptions {\r\n    /**\r\n     * Vector2 of the number of cells in the grid.\r\n     */\r\n    stageSize?: Vector2;\r\n\r\n    /**\r\n     * Vector2 of the size of the output plane in World Units.\r\n     */\r\n    outputSize?: Vector2;\r\n\r\n    /**\r\n     * Vector3 of the position of the output plane in World Units.\r\n     */\r\n    outputPosition?: Vector3;\r\n\r\n    /**\r\n     * Vector3 of the rotation of the output plane.\r\n     */\r\n    outputRotation?: Vector3;\r\n\r\n    /**\r\n     * number of layers that the system will reserve in resources.\r\n     */\r\n    layerCount?: number;\r\n\r\n    /**\r\n     * number of max animation frames a single cell will reserve in resources.\r\n     */\r\n    maxAnimationFrames?: number;\r\n\r\n    /**\r\n     * number cell index of the base tile when the system compiles.\r\n     */\r\n    baseTile?: number;\r\n\r\n    /**\r\n     * boolean flip the sprite after its been repositioned by the framing data.\r\n     */\r\n    flipU?: boolean;\r\n\r\n    /**\r\n     * Vector3 scalar of the global RGB values of the SpriteMap.\r\n     */\r\n    colorMultiply?: Vector3;\r\n\r\n    /**\r\n     * Rotation direction of the frame by 90 degrees.\r\n     * Applied when the the frame's \"rotated\" parameter is true.\r\n     * Default is CCW.\r\n     */\r\n    frameRotationDirection?: SpriteMapFrameRotationDirection;\r\n}\r\n\r\n/**\r\n * Defines the IDisposable interface in order to be cleanable from resources.\r\n */\r\nexport interface ISpriteMap extends IDisposable {\r\n    /**\r\n     * String name of the SpriteMap.\r\n     */\r\n    name: string;\r\n\r\n    /**\r\n     * The JSON Array file from a https://www.codeandweb.com/texturepacker export.  Or similar structure.\r\n     */\r\n    atlasJSON: ISpriteJSONAtlas;\r\n\r\n    /**\r\n     * Texture of the SpriteMap.\r\n     */\r\n    spriteSheet: Texture;\r\n\r\n    /**\r\n     * The parameters to initialize the SpriteMap with.\r\n     */\r\n    options: ISpriteMapOptions;\r\n}\r\n\r\n/**\r\n * Class used to manage a grid restricted sprite deployment on an Output plane.\r\n */\r\nexport class SpriteMap implements ISpriteMap {\r\n    /** The Name of the spriteMap */\r\n    public name: string;\r\n\r\n    /** The JSON file with the frame and meta data */\r\n    public atlasJSON: ISpriteJSONAtlas;\r\n\r\n    /** The systems Sprite Sheet Texture */\r\n    public spriteSheet: Texture;\r\n\r\n    /** Arguments passed with the Constructor */\r\n    public options: ISpriteMapOptions;\r\n\r\n    /** Public Sprite Storage array, parsed from atlasJSON */\r\n    public sprites: Array<ISpriteJSONSprite>;\r\n\r\n    /** Returns the Number of Sprites in the System */\r\n    public get spriteCount(): number {\r\n        return this.sprites.length;\r\n    }\r\n\r\n    /** Returns the Position of Output Plane*/\r\n    public get position(): Vector3 {\r\n        return this._output.position;\r\n    }\r\n\r\n    /** Returns the Position of Output Plane*/\r\n    public set position(v: Vector3) {\r\n        this._output.position = v;\r\n    }\r\n\r\n    /** Returns the Rotation of Output Plane*/\r\n    public get rotation(): Vector3 {\r\n        return this._output.rotation;\r\n    }\r\n\r\n    /** Returns the Rotation of Output Plane*/\r\n    public set rotation(v: Vector3) {\r\n        this._output.rotation = v;\r\n    }\r\n\r\n    /** Sets the AnimationMap*/\r\n    public get animationMap() {\r\n        return this._animationMap;\r\n    }\r\n\r\n    /** Sets the AnimationMap*/\r\n    public set animationMap(v: RawTexture) {\r\n        const buffer = v._texture!._bufferView;\r\n        const am = this._createTileAnimationBuffer(buffer);\r\n        this._animationMap.dispose();\r\n        this._animationMap = am;\r\n        this._material.setTexture(\"animationMap\", this._animationMap);\r\n    }\r\n\r\n    /** Gets or sets a boolean indicating if the sprite map must consider scene fog when rendering */\r\n    public get fogEnabled(): boolean {\r\n        return this._material.fogEnabled;\r\n    }\r\n    public set fogEnabled(value: boolean) {\r\n        this._material.fogEnabled = value;\r\n    }\r\n\r\n    protected _useLogarithmicDepth: boolean;\r\n\r\n    /** Gets or sets a boolean indicating if the sprite map must use logarithmic depth when rendering */\r\n    public get useLogarithmicDepth(): boolean {\r\n        return this._material.useLogarithmicDepth;\r\n    }\r\n    public set useLogarithmicDepth(value: boolean) {\r\n        this._material.useLogarithmicDepth = value;\r\n    }\r\n\r\n    /** Scene that the SpriteMap was created in */\r\n    private _scene: Scene;\r\n\r\n    /** Texture Buffer of Float32 that holds tile frame data*/\r\n    private _frameMap: RawTexture;\r\n\r\n    /** Texture Buffers of Float32 that holds tileMap data*/\r\n    private _tileMaps: RawTexture[];\r\n\r\n    /** Texture Buffer of Float32 that holds Animation Data*/\r\n    private _animationMap: RawTexture;\r\n\r\n    /** Custom ShaderMaterial Central to the System*/\r\n    private _material: ShaderMaterial;\r\n\r\n    /** Custom ShaderMaterial Central to the System*/\r\n    private _output: Mesh;\r\n\r\n    /** Systems Time Ticker*/\r\n    private _time: number;\r\n\r\n    /**\r\n     * Creates a new SpriteMap\r\n     * @param name defines the SpriteMaps Name\r\n     * @param atlasJSON is the JSON file that controls the Sprites Frames and Meta\r\n     * @param spriteSheet is the Texture that the Sprites are on.\r\n     * @param options a basic deployment configuration\r\n     * @param scene The Scene that the map is deployed on\r\n     */\r\n    constructor(name: string, atlasJSON: ISpriteJSONAtlas, spriteSheet: Texture, options: ISpriteMapOptions, scene: Scene) {\r\n        this.name = name;\r\n        this.sprites = [];\r\n        this.atlasJSON = atlasJSON;\r\n        this.sprites = this.atlasJSON[\"frames\"];\r\n        this.spriteSheet = spriteSheet;\r\n\r\n        /**\r\n         * Run through the options and set what ever defaults are needed that where not declared.\r\n         */\r\n        this.options = options;\r\n        options.stageSize = options.stageSize || new Vector2(1, 1);\r\n        options.outputSize = options.outputSize || options.stageSize;\r\n        options.outputPosition = options.outputPosition || Vector3.Zero();\r\n        options.outputRotation = options.outputRotation || Vector3.Zero();\r\n        options.layerCount = options.layerCount || 1;\r\n        options.maxAnimationFrames = options.maxAnimationFrames || 0;\r\n        options.baseTile = options.baseTile || 0;\r\n        options.flipU = options.flipU || false;\r\n        options.colorMultiply = options.colorMultiply || new Vector3(1, 1, 1);\r\n\r\n        this._scene = scene;\r\n\r\n        this._frameMap = this._createFrameBuffer();\r\n\r\n        this._tileMaps = [];\r\n        for (let i = 0; i < options.layerCount; i++) {\r\n            this._tileMaps.push(this._createTileBuffer(null, i));\r\n        }\r\n\r\n        this._animationMap = this._createTileAnimationBuffer(null);\r\n\r\n        const defines = [];\r\n        defines.push(\"#define LAYERS \" + options.layerCount);\r\n\r\n        if (options?.frameRotationDirection === SpriteMapFrameRotationDirection.CW) {\r\n            defines.push(\"#define FR_CW\");\r\n        }\r\n\r\n        if (options.flipU) {\r\n            defines.push(\"#define FLIPU\");\r\n        }\r\n\r\n        defines.push(`#define MAX_ANIMATION_FRAMES ${options.maxAnimationFrames}.0`);\r\n\r\n        const shaderString: string = Effect.ShadersStore[\"spriteMapPixelShader\"];\r\n\r\n        let layerSampleString: string;\r\n        if (!scene.getEngine()._features.supportSwitchCaseInShader) {\r\n            layerSampleString = \"\";\r\n            for (let i = 0; i < options.layerCount; i++) {\r\n                layerSampleString += `if (${i} == i) { frameID = texture2D(tileMaps[${i}], (tileID + 0.5) / stageSize, 0.).x; }`;\r\n            }\r\n        } else {\r\n            layerSampleString = \"switch(i) {\";\r\n            for (let i = 0; i < options.layerCount; i++) {\r\n                layerSampleString += \"case \" + i + \" : frameID = texture(tileMaps[\" + i + \"], (tileID + 0.5) / stageSize, 0.).x;\";\r\n                layerSampleString += \"break;\";\r\n            }\r\n            layerSampleString += \"}\";\r\n        }\r\n\r\n        Effect.ShadersStore[\"spriteMap\" + this.name + \"PixelShader\"] = shaderString.replace(\"#define LAYER_ID_SWITCH\", layerSampleString);\r\n\r\n        this._material = new ShaderMaterial(\r\n            \"spriteMap:\" + this.name,\r\n            this._scene,\r\n            {\r\n                vertex: \"spriteMap\",\r\n                fragment: \"spriteMap\" + this.name,\r\n            },\r\n            {\r\n                defines,\r\n                attributes: [\"position\", \"normal\", \"uv\"],\r\n                uniforms: [\r\n                    \"world\",\r\n                    \"view\",\r\n                    \"projection\",\r\n                    \"time\",\r\n                    \"stageSize\",\r\n                    \"outputSize\",\r\n                    \"spriteMapSize\",\r\n                    \"spriteCount\",\r\n                    \"time\",\r\n                    \"colorMul\",\r\n                    \"mousePosition\",\r\n                    \"curTile\",\r\n                    \"flipU\",\r\n                ],\r\n                samplers: [\"spriteSheet\", \"frameMap\", \"tileMaps\", \"animationMap\"],\r\n                needAlphaBlending: true,\r\n            }\r\n        );\r\n\r\n        this._time = 0;\r\n\r\n        this._material.setFloat(\"spriteCount\", this.spriteCount);\r\n        this._material.setVector2(\"stageSize\", options.stageSize);\r\n        this._material.setVector2(\"outputSize\", options.outputSize);\r\n        this._material.setTexture(\"spriteSheet\", this.spriteSheet);\r\n        this._material.setVector2(\"spriteMapSize\", new Vector2(1, 1));\r\n        this._material.setVector3(\"colorMul\", options.colorMultiply);\r\n\r\n        let tickSave = 0;\r\n\r\n        const bindSpriteTexture = () => {\r\n            if (this.spriteSheet && this.spriteSheet.isReady()) {\r\n                if (this.spriteSheet._texture) {\r\n                    this._material.setVector2(\"spriteMapSize\", new Vector2(this.spriteSheet._texture.baseWidth || 1, this.spriteSheet._texture.baseHeight || 1));\r\n                    return;\r\n                }\r\n            }\r\n            if (tickSave < 100) {\r\n                setTimeout(() => {\r\n                    tickSave++;\r\n                    bindSpriteTexture();\r\n                }, 100);\r\n            }\r\n        };\r\n\r\n        bindSpriteTexture();\r\n\r\n        this._material.setVector3(\"colorMul\", options.colorMultiply);\r\n        this._material.setTexture(\"frameMap\", this._frameMap);\r\n        this._material.setTextureArray(\"tileMaps\", this._tileMaps);\r\n        this._material.setTexture(\"animationMap\", this._animationMap);\r\n        this._material.setFloat(\"time\", this._time);\r\n\r\n        this._output = CreatePlane(name + \":output\", { size: 1, updatable: true }, scene);\r\n        this._output.scaling.x = options.outputSize.x;\r\n        this._output.scaling.y = options.outputSize.y;\r\n        this.position = options.outputPosition;\r\n        this.rotation = options.outputRotation;\r\n\r\n        const obfunction = () => {\r\n            this._time += this._scene.getEngine().getDeltaTime();\r\n            this._material.setFloat(\"time\", this._time);\r\n        };\r\n\r\n        this._scene.onBeforeRenderObservable.add(obfunction);\r\n        this._output.material = this._material;\r\n    }\r\n\r\n    /**\r\n     * Returns the index of the frame for a given filename\r\n     * @param name filename of the frame\r\n     * @returns index of the frame\r\n     */\r\n    public getTileIdxByName(name: string): number {\r\n        const idx = this.atlasJSON.frames.findIndex((f) => f.filename === name);\r\n        return idx;\r\n    }\r\n\r\n    /**\r\n     * Returns tileID location\r\n     * @returns Vector2 the cell position ID\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public getTileID(): Vector2 {\r\n        const p = this.getMousePosition();\r\n        p.multiplyInPlace(this.options.stageSize || Vector2.Zero());\r\n        p.x = Math.floor(p.x);\r\n        p.y = Math.floor(p.y);\r\n        return p;\r\n    }\r\n\r\n    /**\r\n     * Gets the UV location of the mouse over the SpriteMap.\r\n     * @returns Vector2 the UV position of the mouse interaction\r\n     */\r\n    public getMousePosition(): Vector2 {\r\n        const out = this._output;\r\n        const pickinfo: Nullable<PickingInfo> = this._scene.pick(this._scene.pointerX, this._scene.pointerY, (mesh) => {\r\n            if (mesh !== out) {\r\n                return false;\r\n            }\r\n            return true;\r\n        });\r\n\r\n        if (!pickinfo || !pickinfo.hit || !pickinfo.getTextureCoordinates) {\r\n            return new Vector2(-1, -1);\r\n        }\r\n\r\n        const coords = pickinfo.getTextureCoordinates();\r\n        if (coords) {\r\n            return coords;\r\n        }\r\n\r\n        return new Vector2(-1, -1);\r\n    }\r\n\r\n    /**\r\n     * Creates the \"frame\" texture Buffer\r\n     * -------------------------------------\r\n     * Structure of frames\r\n     *  \"filename\": \"Falling-Water-2.png\",\r\n     * \"frame\": {\"x\":69,\"y\":103,\"w\":24,\"h\":32},\r\n     * \"rotated\": true,\r\n     * \"trimmed\": true,\r\n     * \"spriteSourceSize\": {\"x\":4,\"y\":0,\"w\":24,\"h\":32},\r\n     * \"sourceSize\": {\"w\":32,\"h\":32}\r\n     * @returns RawTexture of the frameMap\r\n     */\r\n    private _createFrameBuffer(): RawTexture {\r\n        const data = [];\r\n        //Do two Passes\r\n        for (let i = 0; i < this.spriteCount; i++) {\r\n            data.push(0, 0, 0, 0); //frame\r\n            data.push(0, 0, 0, 0); //spriteSourceSize\r\n            data.push(0, 0, 0, 0); //sourceSize, rotated, trimmed\r\n            data.push(0, 0, 0, 0); //Keep it pow2 cause I\"m cool like that... it helps with sampling accuracy as well. Plus then we have 4 other parameters for future stuff.\r\n        }\r\n        //Second Pass\r\n        for (let i = 0; i < this.spriteCount; i++) {\r\n            const f = this.sprites[i][\"frame\"];\r\n            const sss = this.sprites[i][\"spriteSourceSize\"];\r\n            const ss = this.sprites[i][\"sourceSize\"];\r\n            const r = this.sprites[i][\"rotated\"] ? 1 : 0;\r\n            const t = this.sprites[i][\"trimmed\"] ? 1 : 0;\r\n\r\n            //frame\r\n            data[i * 4] = f.x;\r\n            data[i * 4 + 1] = f.y;\r\n            data[i * 4 + 2] = f.w;\r\n            data[i * 4 + 3] = f.h;\r\n            //spriteSourceSize\r\n            data[i * 4 + this.spriteCount * 4] = sss.x;\r\n            data[i * 4 + 1 + this.spriteCount * 4] = sss.y;\r\n            data[i * 4 + 3 + this.spriteCount * 4] = sss.h;\r\n            //sourceSize, rotated, trimmed\r\n            data[i * 4 + this.spriteCount * 8] = ss.w;\r\n            data[i * 4 + 1 + this.spriteCount * 8] = ss.h;\r\n            data[i * 4 + 2 + this.spriteCount * 8] = r;\r\n            data[i * 4 + 3 + this.spriteCount * 8] = t;\r\n        }\r\n\r\n        const floatArray = new Float32Array(data);\r\n\r\n        const t = RawTexture.CreateRGBATexture(floatArray, this.spriteCount, 4, this._scene, false, false, Texture.NEAREST_NEAREST, Constants.TEXTURETYPE_FLOAT);\r\n\r\n        return t;\r\n    }\r\n\r\n    /**\r\n     * Creates the tileMap texture Buffer\r\n     * @param buffer normally and array of numbers, or a false to generate from scratch\r\n     * @param _layer indicates what layer for a logic trigger dealing with the baseTile.  The system uses this\r\n     * @returns RawTexture of the tileMap\r\n     */\r\n    private _createTileBuffer(buffer: any, _layer: number = 0): RawTexture {\r\n        let data = [];\r\n        const _ty = this.options.stageSize!.y || 0;\r\n        const _tx = this.options.stageSize!.x || 0;\r\n\r\n        if (!buffer) {\r\n            let bt = this.options.baseTile;\r\n            if (_layer != 0) {\r\n                bt = 0;\r\n            }\r\n\r\n            for (let y = 0; y < _ty; y++) {\r\n                for (let x = 0; x < _tx * 4; x += 4) {\r\n                    data.push(bt, 0, 0, 0);\r\n                }\r\n            }\r\n        } else {\r\n            data = buffer;\r\n        }\r\n\r\n        const floatArray = new Float32Array(data);\r\n        const t = RawTexture.CreateRGBATexture(floatArray, _tx, _ty, this._scene, false, false, Texture.NEAREST_NEAREST, Constants.TEXTURETYPE_FLOAT);\r\n\r\n        return t;\r\n    }\r\n\r\n    /**\r\n     * Modifies the data of the tileMaps\r\n     * @param _layer is the ID of the layer you want to edit on the SpriteMap\r\n     * @param pos is the iVector2 Coordinates of the Tile\r\n     * @param tile The SpriteIndex of the new Tile\r\n     */\r\n    public changeTiles(_layer: number = 0, pos: Vector2 | Vector2[], tile: number = 0): void {\r\n        const buffer = this._tileMaps[_layer]._texture!._bufferView;\r\n        if (buffer === null) {\r\n            return;\r\n        }\r\n\r\n        let p = [];\r\n        if (pos instanceof Vector2) {\r\n            p.push(pos);\r\n        } else {\r\n            p = pos;\r\n        }\r\n\r\n        const _tx = this.options.stageSize!.x || 0;\r\n\r\n        for (let i = 0; i < p.length; i++) {\r\n            const _p = p[i];\r\n            _p.x = Math.floor(_p.x);\r\n            _p.y = Math.floor(_p.y);\r\n            const id: number = _p.x * 4 + _p.y * (_tx * 4);\r\n            (buffer as any)[id] = tile;\r\n        }\r\n\r\n        const t = this._createTileBuffer(buffer);\r\n        this._tileMaps[_layer].dispose();\r\n        this._tileMaps[_layer] = t;\r\n        this._material.setTextureArray(\"tileMap\", this._tileMaps);\r\n    }\r\n\r\n    /**\r\n     * Creates the animationMap texture Buffer\r\n     * @param buffer normally and array of numbers, or a false to generate from scratch\r\n     * @returns RawTexture of the animationMap\r\n     */\r\n    private _createTileAnimationBuffer(buffer: Nullable<ArrayBufferView>): RawTexture {\r\n        const data = [];\r\n        let floatArray;\r\n        if (!buffer) {\r\n            for (let i = 0; i < this.spriteCount; i++) {\r\n                data.push(0, 0, 0, 0);\r\n                let count = 1;\r\n                while (count < (this.options.maxAnimationFrames || 4)) {\r\n                    data.push(0, 0, 0, 0);\r\n                    count++;\r\n                }\r\n            }\r\n            floatArray = new Float32Array(data);\r\n        } else {\r\n            floatArray = buffer;\r\n        }\r\n\r\n        const t = RawTexture.CreateRGBATexture(\r\n            floatArray,\r\n            this.spriteCount,\r\n            this.options.maxAnimationFrames || 4,\r\n            this._scene,\r\n            false,\r\n            false,\r\n            Texture.NEAREST_NEAREST,\r\n            Constants.TEXTURETYPE_FLOAT\r\n        );\r\n\r\n        return t;\r\n    }\r\n\r\n    /**\r\n     * Modifies the data of the animationMap\r\n     * @param cellID is the Index of the Sprite\r\n     * @param _frame is the target Animation frame\r\n     * @param toCell is the Target Index of the next frame of the animation\r\n     * @param time is a value between 0-1 that is the trigger for when the frame should change tiles\r\n     * @param speed is a global scalar of the time variable on the map.\r\n     */\r\n    public addAnimationToTile(cellID: number = 0, _frame: number = 0, toCell: number = 0, time: number = 0, speed: number = 1): void {\r\n        const buffer: any = this._animationMap._texture!._bufferView;\r\n        const id: number = cellID * 4 + this.spriteCount * 4 * _frame;\r\n        if (!buffer) {\r\n            return;\r\n        }\r\n        buffer[id] = toCell;\r\n        buffer[id + 1] = time;\r\n        buffer[id + 2] = speed;\r\n        const t = this._createTileAnimationBuffer(buffer);\r\n        this._animationMap.dispose();\r\n        this._animationMap = t;\r\n        this._material.setTexture(\"animationMap\", this._animationMap);\r\n    }\r\n\r\n    /**\r\n     * Exports the .tilemaps file\r\n     */\r\n    public saveTileMaps(): void {\r\n        let maps = \"\";\r\n        for (let i = 0; i < this._tileMaps.length; i++) {\r\n            if (i > 0) {\r\n                maps += \"\\n\\r\";\r\n            }\r\n\r\n            maps += this._tileMaps[i]._texture!._bufferView!.toString();\r\n        }\r\n        const hiddenElement = document.createElement(\"a\");\r\n        hiddenElement.href = \"data:octet/stream;charset=utf-8,\" + encodeURI(maps);\r\n        hiddenElement.target = \"_blank\";\r\n        hiddenElement.download = this.name + \".tilemaps\";\r\n        hiddenElement.click();\r\n        hiddenElement.remove();\r\n    }\r\n\r\n    /**\r\n     * Imports the .tilemaps file\r\n     * @param url of the .tilemaps file\r\n     */\r\n    public loadTileMaps(url: string): void {\r\n        const xhr = new XMLHttpRequest();\r\n        xhr.open(\"GET\", url);\r\n\r\n        const _lc = this.options.layerCount || 0;\r\n\r\n        xhr.onload = () => {\r\n            const data = xhr.response.split(\"\\n\\r\");\r\n            for (let i = 0; i < _lc; i++) {\r\n                const d = data[i].split(\",\").map(Number);\r\n                const t = this._createTileBuffer(d);\r\n                this._tileMaps[i].dispose();\r\n                this._tileMaps[i] = t;\r\n            }\r\n            this._material.setTextureArray(\"tileMap\", this._tileMaps);\r\n        };\r\n        xhr.send();\r\n    }\r\n\r\n    /**\r\n     * Release associated resources\r\n     */\r\n    public dispose(): void {\r\n        this._output.dispose();\r\n        this._material.dispose();\r\n        this._animationMap.dispose();\r\n        for (const tm of this._tileMaps) {\r\n            tm.dispose();\r\n        }\r\n        this._frameMap.dispose();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,MAAM,6BAA6B,CAAC;AAI7D,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAC9D,OAAO,+BAA+B,CAAC;AACvC,OAAO,6BAA6B,CAAC;;;;;;;;;AAGrC,IAAY,+BAGX;AAHD,CAAA,SAAY,+BAA+B;IACvC,+BAAA,CAAA,+BAAA,CAAA,MAAA,GAAA,EAAA,GAAA,KAAO,CAAA;IACP,+BAAA,CAAA,+BAAA,CAAA,KAAA,GAAA,EAAA,GAAA,IAAM,CAAA;AACV,CAAC,EAHW,+BAA+B,IAAA,CAA/B,+BAA+B,GAAA,CAAA,CAAA,GAG1C;AAuFK,MAAO,SAAS;IAgBlB,gDAAA,EAAkD,CAClD,IAAW,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED,wCAAA,EAA0C,CAC1C,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED,wCAAA,EAA0C,CAC1C,IAAW,QAAQ,CAAC,CAAU,EAAA;QAC1B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,wCAAA,EAA0C,CAC1C,IAAW,QAAQ,GAAA;QACf,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED,wCAAA,EAA0C,CAC1C,IAAW,QAAQ,CAAC,CAAU,EAAA;QAC1B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;IAC9B,CAAC;IAED,yBAAA,EAA2B,CAC3B,IAAW,YAAY,GAAA;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,yBAAA,EAA2B,CAC3B,IAAW,YAAY,CAAC,CAAa,EAAA;QACjC,MAAM,MAAM,GAAG,CAAC,CAAC,QAAS,CAAC,WAAW,CAAC;QACvC,MAAM,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAClE,CAAC;IAED,+FAAA,EAAiG,CACjG,IAAW,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IACrC,CAAC;IACD,IAAW,UAAU,CAAC,KAAc,EAAA;QAChC,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC;IACtC,CAAC;IAID,kGAAA,EAAoG,CACpG,IAAW,mBAAmB,GAAA;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC;IAC9C,CAAC;IACD,IAAW,mBAAmB,CAAC,KAAc,EAAA;QACzC,IAAI,CAAC,SAAS,CAAC,mBAAmB,GAAG,KAAK,CAAC;IAC/C,CAAC;IA8KD;;;;OAIG,CACI,gBAAgB,CAAC,IAAY,EAAA;QAChC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;QACxE,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;OAGG,CACH,gEAAgE;IACzD,SAAS,GAAA;QACZ,MAAM,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAClC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,sKAAI,UAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG,CACI,gBAAgB,GAAA;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;QACzB,MAAM,QAAQ,GAA0B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE;YAC1G,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;YACD,OAAO,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;YAChE,OAAO,sKAAI,UAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAC;QAChD,IAAI,MAAM,EAAE,CAAC;YACT,OAAO,MAAM,CAAC;QAClB,CAAC;QAED,OAAO,qKAAI,WAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;;;;;;;OAWG,CACK,kBAAkB,GAAA;QACtB,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,eAAe;QACf,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO;YAC9B,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,kBAAkB;YACzC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,8BAA8B;YACrD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,0IAA0I;QACrK,CAAC;QACD,aAAa;QACb,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC;YACxC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;YAChD,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE7C,OAAO;YACP,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtB,kBAAkB;YAClB,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC3C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC/C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC/C,8BAA8B;YAC9B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC1C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3C,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QAE1C,MAAM,CAAC,iLAAG,aAAU,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,6KAAE,UAAO,CAAC,eAAe,EAAE,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAEzJ,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;OAKG,CACK,iBAAiB,CAAC,MAAW,EAAoB;qBAAlB,iEAAiB,CAAC;QACrD,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAU,CAAC,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAU,CAAC,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC/B,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;gBACd,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;YAED,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC3B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAE,CAAC;oBAClC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3B,CAAC;YACL,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,IAAI,GAAG,MAAM,CAAC;QAClB,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1C,MAAM,CAAC,iLAAG,aAAU,CAAC,iBAAiB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,6KAAE,UAAO,CAAC,eAAe,EAAE,SAAS,CAAC,iBAAiB,CAAC,CAAC;QAE9I,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;OAKG,CACI,WAAW,GAA+D;qBAA9D,iEAAiB,CAAC,EAAE,GAAwB,wDAAE,iEAAe,CAAC;QAC7E,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,QAAS,CAAC,WAAW,CAAC;QAC5D,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,GAAG,8KAAY,UAAO,EAAE,CAAC;YACzB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC,MAAM,CAAC;YACJ,CAAC,GAAG,GAAG,CAAC;QACZ,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,SAAU,CAAC,CAAC,IAAI,CAAC,CAAC;QAE3C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAChC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxB,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,EAAE,GAAW,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAC9C,MAAc,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QAC/B,CAAC;QAED,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;IAED;;;;OAIG,CACK,0BAA0B,CAAC,MAAiC,EAAA;QAChE,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAI,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,CAAE,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtB,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,MAAO,KAAK,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAE,CAAC;oBACpD,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;oBACtB,KAAK,EAAE,CAAC;gBACZ,CAAC;YACL,CAAC;YACD,UAAU,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QACxC,CAAC,MAAM,CAAC;YACJ,UAAU,GAAG,MAAM,CAAC;QACxB,CAAC;QAED,MAAM,CAAC,iLAAG,aAAU,CAAC,iBAAiB,CAClC,UAAU,EACV,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,OAAO,CAAC,kBAAkB,IAAI,CAAC,EACpC,IAAI,CAAC,MAAM,EACX,KAAK,EACL,KAAK,6KACL,UAAO,CAAC,eAAe,EACvB,SAAS,CAAC,iBAAiB,CAC9B,CAAC;QAEF,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;;;OAOG,CACI,kBAAkB,GAAgG;qBAA/F,iEAAiB,CAAC,WAAE,iEAAiB,CAAC,WAAE,iEAAiB,CAAC,SAAE,iEAAe,CAAC,UAAE,iEAAgB,CAAC;QACrH,MAAM,MAAM,GAAQ,IAAI,CAAC,aAAa,CAAC,QAAS,CAAC,WAAW,CAAC;QAC7D,MAAM,EAAE,GAAW,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC;QAC9D,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO;QACX,CAAC;QACD,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QACpB,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QACtB,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QACvB,MAAM,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG,CACI,YAAY,GAAA;QACf,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;YAC7C,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACR,IAAI,IAAI,MAAM,CAAC;YACnB,CAAC;YAED,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAS,CAAC,WAAY,CAAC,QAAQ,EAAE,CAAC;QAChE,CAAC;QACD,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QAClD,aAAa,CAAC,IAAI,GAAG,kCAAkC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QAC1E,aAAa,CAAC,MAAM,GAAG,QAAQ,CAAC;QAChC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACjD,aAAa,CAAC,KAAK,EAAE,CAAC;QACtB,aAAa,CAAC,MAAM,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG,CACI,YAAY,CAAC,GAAW,EAAA;QAC3B,MAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;QACjC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAErB,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;QAEzC,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;YACd,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACxC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC3B,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACzC,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACpC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC1B,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9D,CAAC,CAAC;QACF,GAAG,CAAC,IAAI,EAAE,CAAC;IACf,CAAC;IAED;;OAEG,CACI,OAAO,GAAA;QACV,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,SAAS,CAAE,CAAC;YAC9B,EAAE,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IA/aD;;;;;;;OAOG,CACH,YAAY,IAAY,EAAE,SAA2B,EAAE,WAAoB,EAAE,OAA0B,EAAE,KAAY,CAAA;QACjH,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B;;WAEG,CACH,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,4KAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3D,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,SAAS,CAAC;QAC7D,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,sKAAI,UAAO,CAAC,IAAI,EAAE,CAAC;QAClE,OAAO,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,4KAAO,CAAC,IAAI,EAAE,CAAC;QAClE,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;QAC7C,OAAO,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC;QAC7D,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC;QACzC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC;QACvC,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,sKAAI,UAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEtE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QAEpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE3C,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,CAAE,CAAC;YAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;QAE3D,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;QAErD,uDAAI,OAAO,CAAE,sBAAsB,MAAK,+BAA+B,CAAC,EAAE,EAAE,CAAC;YACzE,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,gCAA0D,OAA1B,OAAO,CAAC,kBAAkB,EAAA,GAAI,CAAC,CAAC;QAE7E,MAAM,YAAY,iKAAW,SAAM,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;QAEzE,IAAI,iBAAyB,CAAC;QAC9B,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC;YACzD,iBAAiB,GAAG,EAAE,CAAC;YACvB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC1C,iBAAiB,IAAI,cAAO,CAAC,EAAA,0CAA0C,OAAD,CAAC,EAAA,wCAAyC,CAAC;YACrH,CAAC;QACL,CAAC,MAAM,CAAC;YACJ,iBAAiB,GAAG,aAAa,CAAC;YAClC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,EAAE,CAAE,CAAC;gBAC1C,iBAAiB,IAAI,OAAO,GAAG,CAAC,GAAG,gCAAgC,GAAG,CAAC,GAAG,uCAAuC,CAAC;gBAClH,iBAAiB,IAAI,QAAQ,CAAC;YAClC,CAAC;YACD,iBAAiB,IAAI,GAAG,CAAC;QAC7B,CAAC;sKAED,SAAM,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,CAAC;QAElI,IAAI,CAAC,SAAS,GAAG,0KAAI,iBAAc,CAC/B,YAAY,GAAG,IAAI,CAAC,IAAI,EACxB,IAAI,CAAC,MAAM,EACX;YACI,MAAM,EAAE,WAAW;YACnB,QAAQ,EAAE,WAAW,GAAG,IAAI,CAAC,IAAI;SACpC,EACD;YACI,OAAO;YACP,UAAU,EAAE;gBAAC,UAAU;gBAAE,QAAQ;gBAAE,IAAI;aAAC;YACxC,QAAQ,EAAE;gBACN,OAAO;gBACP,MAAM;gBACN,YAAY;gBACZ,MAAM;gBACN,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,aAAa;gBACb,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,SAAS;gBACT,OAAO;aACV;YACD,QAAQ,EAAE;gBAAC,aAAa;gBAAE,UAAU;gBAAE,UAAU;gBAAE,cAAc;aAAC;YACjE,iBAAiB,EAAE,IAAI;SAC1B,CACJ,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAEf,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACzD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,YAAY,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAC5D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE,IAAI,4KAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QAE7D,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,MAAM,iBAAiB,GAAG,GAAG,EAAE;YAC3B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;gBACjD,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;oBAC5B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE,sKAAI,UAAO,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC;oBAC7I,OAAO;gBACX,CAAC;YACL,CAAC;YACD,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;gBACjB,UAAU,CAAC,GAAG,EAAE;oBACZ,QAAQ,EAAE,CAAC;oBACX,iBAAiB,EAAE,CAAC;gBACxB,CAAC,EAAE,GAAG,CAAC,CAAC;YACZ,CAAC;QACL,CAAC,CAAC;QAEF,iBAAiB,EAAE,CAAC;QAEpB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QAC7D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACtD,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3D,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,OAAO,oLAAG,cAAA,AAAW,EAAC,IAAI,GAAG,SAAS,EAAE;YAAE,IAAI,EAAE,CAAC;YAAE,SAAS,EAAE,IAAI;QAAA,CAAE,EAAE,KAAK,CAAC,CAAC;QAClF,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,cAAc,CAAC;QAEvC,MAAM,UAAU,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,CAAC;YACrD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;IAC3C,CAAC;CA2RJ", "debugId": null}}, {"offset": {"line": 2039, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Sprites/spritePackedManager.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Sprites/spritePackedManager.ts"], "sourcesContent": ["import type { SpriteManagerOptions } from \"./spriteManager\";\r\nimport { SpriteManager } from \"./spriteManager\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Texture } from \"../Materials/Textures/texture\";\r\n\r\n/**\r\n * Class used to manage multiple sprites of different sizes on the same spritesheet\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/sprites\r\n */\r\n\r\nexport class SpritePackedManager extends SpriteManager {\r\n    /**\r\n     * Creates a new sprite manager from a packed sprite sheet\r\n     * @param name defines the manager's name\r\n     * @param imgUrl defines the sprite sheet url\r\n     * @param capacity defines the maximum allowed number of sprites\r\n     * @param scene defines the hosting scene\r\n     * @param spriteJSON null otherwise a JSON object defining sprite sheet data\r\n     * @param epsilon defines the epsilon value to align texture (0.01 by default)\r\n     * @param samplingMode defines the sampling mode to use with spritesheet\r\n     * @param fromPacked set to true; do not alter\r\n     * @param options options for the sprite manager\r\n     */\r\n\r\n    constructor(\r\n        /** defines the packed manager's name */\r\n        public override name: string,\r\n        imgUrl: string,\r\n        capacity: number,\r\n        scene: Scene,\r\n        spriteJSON: string | null = null,\r\n        epsilon: number = 0.01,\r\n        samplingMode: number = Texture.TRILINEAR_SAMPLINGMODE,\r\n        options?: SpriteManagerOptions\r\n    ) {\r\n        //the cellSize parameter is not used when built from JSON which provides individual cell data, defaults to 64 if JSON load fails\r\n        super(name, imgUrl, capacity, 64, scene, epsilon, samplingMode, true, spriteJSON, options);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,OAAO,EAAE,MAAM,+BAA+B,CAAC;;;AAOlD,MAAO,mBAAoB,4KAAQ,gBAAa;IAClD;;;;;;;;;;;OAWG,CAEH,YACI,sCAAA,EAAwC,CACxB,IAAY,EAC5B,MAAc,EACd,QAAgB,EAChB,KAAY,EACZ,aAA4B,IAAI,EAChC,UAAkB,IAAI,EACtB,0LAAuB,UAAO,CAAC,sBAAsB,EACrD,OAA8B,CAAA;QAE9B,gIAAgI;QAChI,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAV3E,IAAA,CAAA,IAAI,GAAJ,IAAI,CAAQ;IAWhC,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 2068, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/@babylonjs/core/Sprites/index.js", "sourceRoot": "", "sources": ["file:///C:/Users/<USER>/dyad-apps/Website-Portofolio/dev/core/src/Sprites/index.ts"], "sourcesContent": ["export * from \"./sprite\";\r\nexport * from \"./ISprites\";\r\nexport * from \"./spriteManager\";\r\nexport * from \"./spriteMap\";\r\nexport * from \"./spritePackedManager\";\r\nexport * from \"./spriteSceneComponent\";\r\n\r\nexport * from \"../Shaders/sprites.fragment\";\r\nexport * from \"../Shaders/sprites.vertex\";\r\nexport * from \"../ShadersWGSL/sprites.fragment\";\r\nexport * from \"../ShadersWGSL/sprites.vertex\";\r\n"], "names": [], "mappings": ";AAAA,cAAc,UAAU,CAAC;AACzB,cAAc,YAAY,CAAC;AAC3B,cAAc,iBAAiB,CAAC;AAChC,cAAc,aAAa,CAAC;AAC5B,cAAc,uBAAuB,CAAC;AACtC,cAAc,wBAAwB,CAAC;AAEvC,cAAc,6BAA6B,CAAC;AAC5C,cAAc,2BAA2B,CAAC;AAC1C,cAAc,iCAAiC,CAAC;AAChD,cAAc,+BAA+B,CAAC", "debugId": null}}]}