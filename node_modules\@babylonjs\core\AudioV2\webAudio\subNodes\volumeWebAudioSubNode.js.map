{"version": 3, "file": "volumeWebAudioSubNode.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/webAudio/subNodes/volumeWebAudioSubNode.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,mBAAmB,EAAE,MAAM,iDAAiD,CAAC;AAEtF,OAAO,EAAE,2BAA2B,EAAE,MAAM,0CAA0C,CAAC;AAIvF,gBAAgB;AAChB,4DAA4D;AAC5D,MAAM,CAAC,KAAK,UAAU,8BAA8B,CAAC,MAAuB;IACxE,OAAO,IAAI,sBAAsB,CAAC,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED,gBAAgB;AAChB,MAAM,OAAO,sBAAuB,SAAQ,mBAAmB;IAS3D,gBAAgB;IAChB,YAAmB,MAAuB;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QAEd,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,OAAO,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC1E,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,gBAAgB;IAChB,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED,gBAAgB;IAChB,IAAW,MAAM,CAAC,KAAa;QAC3B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,gBAAgB;IACT,SAAS,CAAC,KAAa,EAAE,UAAyD,IAAI;QACzF,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC;IAEkB,QAAQ,CAAC,IAAqB;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,wBAAwB,CAAC;IACpC,CAAC;CACJ", "sourcesContent": ["import type { Nullable } from \"../../../types\";\nimport { _VolumeAudioSubNode } from \"../../abstractAudio/subNodes/volumeAudioSubNode\";\nimport type { IAudioParameterRampOptions } from \"../../audioParameter\";\nimport { _WebAudioParameterComponent } from \"../components/webAudioParameterComponent\";\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\nimport type { IWebAudioInNode, IWebAudioSubNode } from \"../webAudioNode\";\n\n/** @internal */\n// eslint-disable-next-line @typescript-eslint/require-await\nexport async function _CreateVolumeAudioSubNodeAsync(engine: _WebAudioEngine): Promise<_VolumeAudioSubNode> {\n    return new _VolumeWebAudioSubNode(engine);\n}\n\n/** @internal */\nexport class _VolumeWebAudioSubNode extends _VolumeAudioSubNode implements IWebAudioSubNode {\n    private _volume: _WebAudioParameterComponent;\n\n    /** @internal */\n    public override readonly engine: _WebAudioEngine;\n\n    /** @internal */\n    public readonly node: AudioNode;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine) {\n        super(engine);\n\n        const gainNode = (this.node = new GainNode(engine._audioContext));\n        this._volume = new _WebAudioParameterComponent(engine, gainNode.gain);\n    }\n\n    /** @internal */\n    public override dispose(): void {\n        super.dispose();\n\n        this._volume.dispose();\n    }\n\n    /** @internal */\n    public get volume(): number {\n        return this._volume.value;\n    }\n\n    /** @internal */\n    public set volume(value: number) {\n        this.setVolume(value);\n    }\n\n    /** @internal */\n    public get _inNode(): AudioNode {\n        return this.node;\n    }\n\n    /** @internal */\n    public get _outNode(): AudioNode {\n        return this.node;\n    }\n\n    /** @internal */\n    public setVolume(value: number, options: Nullable<Partial<IAudioParameterRampOptions>> = null): void {\n        this._volume.setTargetValue(value, options);\n    }\n\n    protected override _connect(node: IWebAudioInNode): boolean {\n        const connected = super._connect(node);\n\n        if (!connected) {\n            return false;\n        }\n\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\n        if (node._inNode) {\n            this.node.connect(node._inNode);\n        }\n\n        return true;\n    }\n\n    protected override _disconnect(node: IWebAudioInNode): boolean {\n        const disconnected = super._disconnect(node);\n\n        if (!disconnected) {\n            return false;\n        }\n\n        if (node._inNode) {\n            this.node.disconnect(node._inNode);\n        }\n\n        return true;\n    }\n\n    /** @internal */\n    public getClassName(): string {\n        return \"_VolumeWebAudioSubNode\";\n    }\n}\n"]}