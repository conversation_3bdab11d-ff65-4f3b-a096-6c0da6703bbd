{"version": 3, "file": "abstractEngine.texture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.texture.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAmBnD,cAAc,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,IAAiB,EAAE,OAAoC,EAAE,SAA8B;IAClJ,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACjB,MAAM,KAAK,GAAuC,IAAK,CAAC,KAAK,IAAY,IAAI,CAAC;QAC9E,OAAO,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;IACrE,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import type { DepthTextureCreationOptions, TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport { AbstractEngine } from \"../abstractEngine\";\r\nimport type { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a depth stencil texture.\r\n         * This is only available in WebGL 2 or with the depth texture extension available.\r\n         * @param size The size of face edge in the texture.\r\n         * @param options The options defining the texture.\r\n         * @param rtWrapper The render target wrapper for which the depth/stencil texture must be created\r\n         * @returns The texture\r\n         */\r\n        createDepthStencilTexture(size: TextureSize, options: DepthTextureCreationOptions, rtWrapper: RenderTargetWrapper): InternalTexture;\r\n    }\r\n}\r\n\r\nAbstractEngine.prototype.createDepthStencilTexture = function (size: TextureSize, options: DepthTextureCreationOptions, rtWrapper: RenderTargetWrapper): InternalTexture {\r\n    if (options.isCube) {\r\n        const width = (<{ width: number; height: number }>size).width || <number>size;\r\n        return this._createDepthStencilCubeTexture(width, options);\r\n    } else {\r\n        return this._createDepthStencilTexture(size, options, rtWrapper);\r\n    }\r\n};\r\n"]}