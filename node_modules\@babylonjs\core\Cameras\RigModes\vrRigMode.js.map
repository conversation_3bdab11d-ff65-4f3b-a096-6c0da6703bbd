{"version": 3, "file": "vrRigMode.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Cameras/RigModes/vrRigMode.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,yBAAyB,CAAC;AACjD,OAAO,EAAE,iCAAiC,EAAE,MAAM,uDAAuD,CAAC;AAC1G,OAAO,EAAE,kCAAkC,EAAE,MAAM,wDAAwD,CAAC;AAC5G,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAErD;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,MAAc,EAAE,SAAc;IACxD,MAAM,OAAO,GAAoB,SAAS,CAAC,eAAe,IAAI,eAAe,CAAC,UAAU,EAAE,CAAC;IAE3F,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,GAAG,OAAO,CAAC;IAC3D,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC9D,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;IACnE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW,CAAC;IACvE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC,iBAAiB,CAAC;IACnF,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC;IAEzF,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,GAAG,OAAO,CAAC;IAC3D,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAChE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;IACnE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC;IACxE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,eAAe,GAAG,OAAO,CAAC,kBAAkB,CAAC;IACpF,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC;IAEzF,uBAAuB;IACvB,+DAA+D;IAC/D,mFAAmF;IACnF,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;YAC9E,OAAO,CAAC,gBAAgB,GAAG,KAAK,CAAC;QACrC,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,yBAAyB,GAAG,IAAI,CAAC;YACxC,MAAM,CAAC,eAAe,GAAG,IAAI,kCAAkC,CAAC,yBAAyB,EAAE,MAAM,EAAE,OAAO,CAAC,sBAAsB,CAAC,CAAC;QACvI,CAAC;IACL,CAAC;IAED,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;QAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,IAAI,iCAAiC,CAAC,8BAA8B,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACrJ,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,GAAG,IAAI,iCAAiC,CAAC,+BAA+B,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACzJ,CAAC;AACL,CAAC", "sourcesContent": ["import type { Camera } from \"../camera\";\r\nimport { Matrix } from \"../../Maths/math.vector\";\r\nimport { VRDistortionCorrectionPostProcess } from \"../../PostProcesses/vrDistortionCorrectionPostProcess\";\r\nimport { VRMultiviewToSingleviewPostProcess } from \"../../PostProcesses/vrMultiviewToSingleviewPostProcess\";\r\nimport { VRCameraMetrics } from \"../VR/vrCameraMetrics\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport { Viewport } from \"../../Maths/math.viewport\";\r\n\r\n/**\r\n * @internal\r\n */\r\nexport function _SetVrRigMode(camera: Camera, rigParams: any) {\r\n    const metrics = <VRCameraMetrics>rigParams.vrCameraMetrics || VRCameraMetrics.GetDefault();\r\n\r\n    camera._rigCameras[0]._cameraRigParams.vrMetrics = metrics;\r\n    camera._rigCameras[0].viewport = new Viewport(0, 0, 0.5, 1.0);\r\n    camera._rigCameras[0]._cameraRigParams.vrWorkMatrix = new Matrix();\r\n    camera._rigCameras[0]._cameraRigParams.vrHMatrix = metrics.leftHMatrix;\r\n    camera._rigCameras[0]._cameraRigParams.vrPreViewMatrix = metrics.leftPreViewMatrix;\r\n    camera._rigCameras[0].getProjectionMatrix = camera._rigCameras[0]._getVRProjectionMatrix;\r\n\r\n    camera._rigCameras[1]._cameraRigParams.vrMetrics = metrics;\r\n    camera._rigCameras[1].viewport = new Viewport(0.5, 0, 0.5, 1.0);\r\n    camera._rigCameras[1]._cameraRigParams.vrWorkMatrix = new Matrix();\r\n    camera._rigCameras[1]._cameraRigParams.vrHMatrix = metrics.rightHMatrix;\r\n    camera._rigCameras[1]._cameraRigParams.vrPreViewMatrix = metrics.rightPreViewMatrix;\r\n    camera._rigCameras[1].getProjectionMatrix = camera._rigCameras[1]._getVRProjectionMatrix;\r\n\r\n    // For multiview camera\r\n    // First multiview will be rendered to camera._multiviewTexture\r\n    // Then this postprocess will run on each eye to copy the right texture to each eye\r\n    if (metrics.multiviewEnabled) {\r\n        if (!camera.getScene().getEngine().getCaps().multiview) {\r\n            Logger.Warn(\"Multiview is not supported, falling back to standard rendering\");\r\n            metrics.multiviewEnabled = false;\r\n        } else {\r\n            camera._useMultiviewToSingleView = true;\r\n            camera._rigPostProcess = new VRMultiviewToSingleviewPostProcess(\"VRMultiviewToSingleview\", camera, metrics.postProcessScaleFactor);\r\n        }\r\n    }\r\n\r\n    if (metrics.compensateDistortion) {\r\n        camera._rigCameras[0]._rigPostProcess = new VRDistortionCorrectionPostProcess(\"VR_Distort_Compensation_Left\", camera._rigCameras[0], false, metrics);\r\n        camera._rigCameras[1]._rigPostProcess = new VRDistortionCorrectionPostProcess(\"VR_Distort_Compensation_Right\", camera._rigCameras[1], true, metrics);\r\n    }\r\n}\r\n"]}