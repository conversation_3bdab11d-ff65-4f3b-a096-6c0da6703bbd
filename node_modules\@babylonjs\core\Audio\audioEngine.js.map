{"version": 3, "file": "audioEngine.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Audio/audioEngine.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAE3D,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAC;AAErE,8CAA8C;AAC9C,cAAc,CAAC,kBAAkB,GAAG,CAChC,WAAkC,EAClC,YAAoC,EACpC,gBAAkF,EACpF,EAAE;IACA,OAAO,IAAI,WAAW,CAAC,WAAW,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACxE,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,OAAO,WAAW;IAWpB;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAe;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;IACxD,CAAC;IAyBD;;;OAGG;IACH,IAAW,uBAAuB;QAC9B,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC,CAAC;IAED,IAAW,uBAAuB,CAAC,KAAc;QAC7C,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QACtC,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG,CAAC,KAAK,CAAC;IACvC,CAAC;IAeD;;OAEG;IACH,IAAW,YAAY;QACnB,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAC/B,yCAAyC;YACzC,mEAAmE;YACnE,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACrC,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;IAClC,CAAC;IAID;;;;;;OAMG;IACH,YACI,cAAqC,IAAI,EACzC,eAAuC,IAAI,EAC3C,mBAAqF,IAAI;QA9FrF,kBAAa,GAA2B,IAAI,CAAC;QAE7C,cAAS,GAAG,KAAK,CAAC;QAClB,6BAAwB,GAAY,KAAK,CAAC;QAElD;;WAEG;QACI,mBAAc,GAAY,IAAI,CAAC;QAatC;;WAEG;QACH,gEAAgE;QACzD,8BAAyB,GAAY,KAAK,CAAC;QAElD;;WAEG;QACI,mBAAc,GAAY,KAAK,CAAC;QAEvC;;WAEG;QACI,mBAAc,GAAY,KAAK,CAAC;QAEvC;;;;WAIG;QACI,aAAQ,GAAY,KAAK,CAAC;QAejC;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAElE;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAgB,CAAC;QA+B5D,MAAM,EAAE,GAAG,IAAI,eAAe,CAAC;YAC3B,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;YACrD,sBAAsB,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;SAC7F,CAAC,CAAC;QAEH,4GAA4G;QAC5G,qGAAqG;QACrG,EAAE,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE5B,IAAI,CAAC,WAAW,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;QAClD,EAAE,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAExC,EAAE,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACtB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,mFAAmF;QACnF,EAAE,CAAC,UAAU,CAAC,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACpD,EAAE,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;YACtC,EAAE,CAAC,sBAAsB,CAAC,eAAe,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAE9C,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,IAAI;QACP,mEAAmE;QACnE,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAEjC,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACrC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,MAAM;QACT,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjB,wDAAwD;gBACxD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACzD,CAAC;YAED,OAAO;QACX,CAAC;QAED,mEAAmE;QACnE,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAED,gBAAgB;IACT,gCAAgC;QACnC,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAChC,aAAa,EACb,GAAG,EAAE;YACD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC3D,mEAAmE;gBACnE,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACpC,CAAC;QACL,CAAC,EACD;YACI,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;SACpC,CACJ,CAAC;IACN,CAAC;IAED,2FAA2F;IACnF,wBAAwB;QAC5B,IAAI,IAAI,CAAC,GAAG,CAAC,2BAA2B,EAAE,CAAC;YACvC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAEnB,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,SAAiB;QACpC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,QAAkB;QACvC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QACnC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;QAC7B,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IACnG,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACnC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAEtC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACzD,CAAC;CACJ", "sourcesContent": ["import type { Analyser } from \"./analyser\";\r\n\r\nimport type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { AbstractEngine } from \"../Engines/abstractEngine\";\r\nimport type { IAudioEngine } from \"./Interfaces/IAudioEngine\";\r\nimport { _WebAudioEngine } from \"../AudioV2/webAudio/webAudioEngine\";\r\n\r\n// Sets the default audio engine to Babylon.js\r\nAbstractEngine.AudioEngineFactory = (\r\n    hostElement: Nullable<HTMLElement>,\r\n    audioContext: Nullable<AudioContext>,\r\n    audioDestination: Nullable<AudioDestinationNode | MediaStreamAudioDestinationNode>\r\n) => {\r\n    return new AudioEngine(hostElement, audioContext, audioDestination);\r\n};\r\n\r\n/**\r\n * This represents the default audio engine used in babylon.\r\n * It is responsible to play, synchronize and analyse sounds throughout the  application.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport class AudioEngine implements IAudioEngine {\r\n    private _audioContext: Nullable<AudioContext> = null;\r\n    private _masterGain: GainNode;\r\n    private _tryToRun = false;\r\n    private _useCustomUnlockedButton: boolean = false;\r\n\r\n    /**\r\n     * Gets whether the current host supports Web Audio and thus could create AudioContexts.\r\n     */\r\n    public canUseWebAudio: boolean = true;\r\n\r\n    /**\r\n     * The master gain node defines the global audio volume of your audio engine.\r\n     */\r\n    public get masterGain(): GainNode {\r\n        return this._masterGain;\r\n    }\r\n\r\n    public set masterGain(value: GainNode) {\r\n        this._masterGain = this._v2.mainOut._inNode = value;\r\n    }\r\n\r\n    /**\r\n     * Defines if Babylon should emit a warning if WebAudio is not supported.\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public WarnedWebAudioUnsupported: boolean = false;\r\n\r\n    /**\r\n     * Gets whether or not mp3 are supported by your browser.\r\n     */\r\n    public isMP3supported: boolean = false;\r\n\r\n    /**\r\n     * Gets whether or not ogg are supported by your browser.\r\n     */\r\n    public isOGGsupported: boolean = false;\r\n\r\n    /**\r\n     * Gets whether audio has been unlocked on the device.\r\n     * Some Browsers have strong restrictions about Audio and won't autoplay unless\r\n     * a user interaction has happened.\r\n     */\r\n    public unlocked: boolean = false;\r\n\r\n    /**\r\n     * Defines if the audio engine relies on a custom unlocked button.\r\n     * In this case, the embedded button will not be displayed.\r\n     */\r\n    public get useCustomUnlockedButton(): boolean {\r\n        return this._useCustomUnlockedButton;\r\n    }\r\n\r\n    public set useCustomUnlockedButton(value: boolean) {\r\n        this._useCustomUnlockedButton = value;\r\n        this._v2._unmuteUIEnabled = !value;\r\n    }\r\n\r\n    /**\r\n     * Event raised when audio has been unlocked on the browser.\r\n     */\r\n    public onAudioUnlockedObservable = new Observable<IAudioEngine>();\r\n\r\n    /**\r\n     * Event raised when audio has been locked on the browser.\r\n     */\r\n    public onAudioLockedObservable = new Observable<IAudioEngine>();\r\n\r\n    /** @internal */\r\n    public _v2: _WebAudioEngine;\r\n\r\n    /**\r\n     * Gets the current AudioContext if available.\r\n     */\r\n    public get audioContext(): Nullable<AudioContext> {\r\n        if (this._v2.state === \"running\") {\r\n            // Do not wait for the promise to unlock.\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._triggerRunningStateAsync();\r\n        }\r\n        return this._v2._audioContext;\r\n    }\r\n\r\n    private _connectedAnalyser: Nullable<Analyser>;\r\n\r\n    /**\r\n     * Instantiates a new audio engine.\r\n     *\r\n     * @param hostElement defines the host element where to display the mute icon if necessary\r\n     * @param audioContext defines the audio context to be used by the audio engine\r\n     * @param audioDestination defines the audio destination node to be used by audio engine\r\n     */\r\n    constructor(\r\n        hostElement: Nullable<HTMLElement> = null,\r\n        audioContext: Nullable<AudioContext> = null,\r\n        audioDestination: Nullable<AudioDestinationNode | MediaStreamAudioDestinationNode> = null\r\n    ) {\r\n        const v2 = new _WebAudioEngine({\r\n            audioContext: audioContext ? audioContext : undefined,\r\n            defaultUIParentElement: hostElement?.parentElement ? hostElement.parentElement : undefined,\r\n        });\r\n\r\n        // Historically the unmute button is disabled until a sound tries to play and can't, which results in a call\r\n        // to `AudioEngine.lock()`, which is where the unmute button is enabled if no custom UI is requested.\r\n        v2._unmuteUIEnabled = false;\r\n\r\n        this._masterGain = new GainNode(v2._audioContext);\r\n        v2._audioDestination = audioDestination;\r\n\r\n        v2.stateChangedObservable.add((state) => {\r\n            if (state === \"running\") {\r\n                this.unlocked = true;\r\n                this.onAudioUnlockedObservable.notifyObservers(this);\r\n            } else {\r\n                this.unlocked = false;\r\n                this.onAudioLockedObservable.notifyObservers(this);\r\n            }\r\n        });\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises, github/no-then\r\n        v2._initAsync({ resumeOnInteraction: false }).then(() => {\r\n            v2.mainOut._inNode = this._masterGain;\r\n            v2.stateChangedObservable.notifyObservers(v2.state);\r\n        });\r\n\r\n        this.isMP3supported = v2.isFormatValid(\"mp3\");\r\n        this.isOGGsupported = v2.isFormatValid(\"ogg\");\r\n\r\n        this._v2 = v2;\r\n    }\r\n\r\n    /**\r\n     * Flags the audio engine in Locked state.\r\n     * This happens due to new browser policies preventing audio to autoplay.\r\n     */\r\n    public lock() {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._v2._audioContext.suspend();\r\n\r\n        if (!this._useCustomUnlockedButton) {\r\n            this._v2._unmuteUIEnabled = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Unlocks the audio engine once a user action has been done on the dom.\r\n     * This is helpful to resume play once browser policies have been satisfied.\r\n     */\r\n    public unlock() {\r\n        if (this._audioContext?.state === \"running\") {\r\n            if (!this.unlocked) {\r\n                // Notify users that the audio stack is unlocked/unmuted\r\n                this.unlocked = true;\r\n                this.onAudioUnlockedObservable.notifyObservers(this);\r\n            }\r\n\r\n            return;\r\n        }\r\n\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this._triggerRunningStateAsync();\r\n    }\r\n\r\n    /** @internal */\r\n    public _resumeAudioContextOnStateChange(): void {\r\n        this._audioContext?.addEventListener(\r\n            \"statechange\",\r\n            () => {\r\n                if (this.unlocked && this._audioContext?.state !== \"running\") {\r\n                    // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                    this._resumeAudioContextAsync();\r\n                }\r\n            },\r\n            {\r\n                once: true,\r\n                passive: true,\r\n                signal: AbortSignal.timeout(3000),\r\n            }\r\n        );\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/promise-function-async, no-restricted-syntax\r\n    private _resumeAudioContextAsync(): Promise<void> {\r\n        if (this._v2._isUsingOfflineAudioContext) {\r\n            return Promise.resolve();\r\n        }\r\n\r\n        return this._v2._audioContext.resume();\r\n    }\r\n\r\n    /**\r\n     * Destroy and release the resources associated with the audio context.\r\n     */\r\n    public dispose(): void {\r\n        this._v2.dispose();\r\n\r\n        this.onAudioUnlockedObservable.clear();\r\n        this.onAudioLockedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Gets the global volume sets on the master gain.\r\n     * @returns the global volume if set or -1 otherwise\r\n     */\r\n    public getGlobalVolume(): number {\r\n        return this.masterGain.gain.value;\r\n    }\r\n\r\n    /**\r\n     * Sets the global volume of your experience (sets on the master gain).\r\n     * @param newVolume Defines the new global volume of the application\r\n     */\r\n    public setGlobalVolume(newVolume: number): void {\r\n        this.masterGain.gain.value = newVolume;\r\n    }\r\n\r\n    /**\r\n     * Connect the audio engine to an audio analyser allowing some amazing\r\n     * synchronization between the sounds/music and your visualization (VuMeter for instance).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser\r\n     * @param analyser The analyser to connect to the engine\r\n     */\r\n    public connectToAnalyser(analyser: Analyser): void {\r\n        if (this._connectedAnalyser) {\r\n            this._connectedAnalyser.stopDebugCanvas();\r\n        }\r\n\r\n        this._connectedAnalyser = analyser;\r\n        this.masterGain.disconnect();\r\n        this._connectedAnalyser.connectAudioNodes(this.masterGain, this._v2._audioContext.destination);\r\n    }\r\n\r\n    private async _triggerRunningStateAsync() {\r\n        if (this._tryToRun) {\r\n            return;\r\n        }\r\n        this._tryToRun = true;\r\n\r\n        await this._resumeAudioContextAsync();\r\n\r\n        this._tryToRun = false;\r\n        this.unlocked = true;\r\n\r\n        this.onAudioUnlockedObservable.notifyObservers(this);\r\n    }\r\n}\r\n"]}