{"version": 3, "file": "audioParameter.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/AudioV2/audioParameter.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAkB,uBAajB;AAbD,WAAkB,uBAAuB;IACrC;;OAEG;IACH,4CAAiB,CAAA;IACjB;;OAEG;IACH,sDAA2B,CAAA;IAC3B;;OAEG;IACH,sDAA2B,CAAA;AAC/B,CAAC,EAbiB,uBAAuB,KAAvB,uBAAuB,QAaxC", "sourcesContent": ["/**\n * The shape of the audio ramp used to set an audio parameter's value, such as a sound's volume.\n */\nexport const enum AudioParameterRampShape {\n    /**\n     * The ramp is linear.\n     */\n    Linear = \"linear\",\n    /**\n     * The ramp is exponential.\n     */\n    Exponential = \"exponential\",\n    /**\n     * The ramp is logarithmic.\n     */\n    Logarithmic = \"logarithmic\",\n}\n\n/**\n * Options for ramping an audio parameter's value.\n */\nexport interface IAudioParameterRampOptions {\n    /**\n     * The ramp time, in seconds. Must be greater than 0. Defaults to 0.01 seconds.\n     * The audio parameter's value will reach the target value at the end of the duration.\n     */\n    duration: number;\n    /**\n     * The shape of the ramp to use for the parameter change. Defaults to {@link AudioParameterRampShape.Linear}.\n     */\n    shape: AudioParameterRampShape;\n}\n"]}