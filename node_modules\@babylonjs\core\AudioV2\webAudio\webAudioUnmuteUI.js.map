{"version": 3, "file": "webAudioUnmuteUI.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/webAudio/webAudioUnmuteUI.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAGxD;;;GAGG;AACH,MAAM,OAAO,iBAAiB;IAM1B,gBAAgB;IAChB,YAAmB,MAAuB,EAAE,aAA2B;QAN/D,YAAO,GAAgC,IAAI,CAAC;QAC5C,aAAQ,GAAY,IAAI,CAAC;QAEzB,WAAM,GAA+B,IAAI,CAAC;QAyE1C,oBAAe,GAAG,GAAG,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChB,OAAO;YACX,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACnC,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;QACL,CAAC,CAAC;QA/EE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,MAAM,MAAM,GAAG,aAAa,IAAI,WAAW,CAAC,iBAAiB,EAAE,eAAe,EAAE,EAAE,aAAa,IAAI,QAAQ,CAAC,IAAI,CAAC;QACjH,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;QAE1C,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,CAAC,WAAW,CACnB,QAAQ,CAAC,cAAc,CACnB,wCAAwC,GAAG,u8BAAu8B,CACr/B,CACJ,CAAC;QACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEvC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,eAAe,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,EAAE,GAAG,qBAAqB,CAAC;QAExC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACxC,mEAAmE;YACnE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEjC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAClE,CAAC;IAED,gBAAgB;IACT,OAAO;QACV,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC7E,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAW,OAAO,CAAC,KAAc;QAC7B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACnC,IAAI,CAAC,KAAK,EAAE,CAAC;YACjB,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,KAAK,EAAE,CAAC;QACjB,CAAC;IACL,CAAC;IAEO,KAAK;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;IACzC,CAAC;IAEO,KAAK;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,CAAC;CAaJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport { EngineStore } from \"../../Engines/engineStore\";\nimport type { _WebAudioEngine } from \"./webAudioEngine\";\n\n/**\n * Adds a UI button that starts the audio engine's underlying audio context when the user presses it.\n * @internal\n */\nexport class _WebAudioUnmuteUI {\n    private _button: Nullable<HTMLButtonElement> = null;\n    private _enabled: boolean = true;\n    private _engine: _WebAudioEngine;\n    private _style: Nullable<HTMLStyleElement> = null;\n\n    /** @internal */\n    public constructor(engine: _WebAudioEngine, parentElement?: HTMLElement) {\n        this._engine = engine;\n        const parent = parentElement || EngineStore.LastCreatedEngine?.getInputElement()?.parentElement || document.body;\n        const top = (parent?.offsetTop || 0) + 20;\n\n        this._style = document.createElement(\"style\");\n        this._style.appendChild(\n            document.createTextNode(\n                `.babylonUnmute{position:absolute;top:${top}px;margin-left:20px;height:40px;width:60px;background-color:rgba(51,51,51,0.7);background-image:url(\"data:image/svg+xml;charset=UTF-8,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2239%22%20height%3D%2232%22%20viewBox%3D%220%200%2039%2032%22%3E%3Cpath%20fill%3D%22white%22%20d%3D%22M9.625%2018.938l-0.031%200.016h-4.953q-0.016%200-0.031-0.016v-12.453q0-0.016%200.031-0.016h4.953q0.031%200%200.031%200.016v12.453zM12.125%207.688l8.719-8.703v27.453l-8.719-8.719-0.016-0.047v-9.938zM23.359%207.875l1.406-1.406%204.219%204.203%204.203-4.203%201.422%201.406-4.219%204.219%204.219%204.203-1.484%201.359-4.141-4.156-4.219%204.219-1.406-1.422%204.219-4.203z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E\");background-size:80%;background-repeat:no-repeat;background-position:center;background-position-y:4px;border:none;outline:none;transition:transform 0.125s ease-out;cursor:pointer;z-index:9999;}.babylonUnmute:hover{transform:scale(1.05)}`\n            )\n        );\n        document.head.appendChild(this._style);\n\n        this._button = document.createElement(\"button\");\n        this._button.className = \"babylonUnmute\";\n        this._button.id = \"babylonUnmuteButton\";\n\n        this._button.addEventListener(\"click\", () => {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this._engine.unlockAsync();\n        });\n\n        parent.appendChild(this._button);\n\n        this._engine.stateChangedObservable.add(this._onStateChanged);\n    }\n\n    /** @internal */\n    public dispose(): void {\n        this._button?.remove();\n        this._button = null;\n\n        this._style?.remove();\n        this._style = null;\n\n        this._engine.stateChangedObservable.removeCallback(this._onStateChanged);\n    }\n\n    /** @internal */\n    public get enabled(): boolean {\n        return this._enabled;\n    }\n\n    public set enabled(value: boolean) {\n        this._enabled = value;\n        if (value) {\n            if (this._engine.state !== \"running\") {\n                this._show();\n            }\n        } else {\n            this._hide();\n        }\n    }\n\n    private _show(): void {\n        if (!this._button) {\n            return;\n        }\n\n        this._button.style.display = \"block\";\n    }\n\n    private _hide(): void {\n        if (!this._button) {\n            return;\n        }\n\n        this._button.style.display = \"none\";\n    }\n\n    private _onStateChanged = () => {\n        if (!this._button) {\n            return;\n        }\n\n        if (this._engine.state === \"running\") {\n            this._hide();\n        } else {\n            this._show();\n        }\n    };\n}\n"]}