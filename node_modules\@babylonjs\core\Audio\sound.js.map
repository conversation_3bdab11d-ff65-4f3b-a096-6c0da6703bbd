{"version": 3, "file": "sound.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Audio/sound.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAK/C,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAGrD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAClD,OAAO,EAAE,cAAc,EAAE,qCAAoC;AAC7D,OAAO,EAAE,kBAAkB,EAAE,+BAA8B;AAE3D;;;;GAIG;AACH,MAAM,OAAO,KAAK;IAWd;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAW,IAAI,CAAC,KAAc;QAC1B,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IACxC,CAAC;IAuDD;;OAEG;IACH,IAAW,WAAW;QAClB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;QAC9C,CAAC;QAED,IAAI,cAAc,CAAC,WAAW,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChF,wGAAwG;YACxG,kCAAkC;YAClC,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YACrH,OAAO,IAAI,CAAC,YAAY,GAAG,kBAAkB,CAAC;QAClD,CAAC;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;OAGG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,IAAW,YAAY,CAAC,QAAiB;QACrC,IAAI,QAAQ,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACjC,OAAO;QACX,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;QAEb,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;YAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACb,IAAI,CAAC,IAAI,EAAE,CAAC;QAChB,CAAC;IACL,CAAC;IA6CD;;;;;;;OAOG;IACH,YAAY,IAAY,EAAE,gBAAqB,EAAE,KAAuB,EAAE,sBAA4C,IAAI,EAAE,OAAuB;QAhLnJ;;WAEG;QACI,aAAQ,GAAY,KAAK,CAAC;QAEzB,UAAK,GAAG,KAAK,CAAC;QAiBtB;;;;WAIG;QACI,yBAAoB,GAAY,KAAK,CAAC;QAK7C;;WAEG;QACI,cAAS,GAAY,KAAK,CAAC;QAClC;;WAEG;QACI,aAAQ,GAAY,KAAK,CAAC;QACjC;;;WAGG;QACI,gBAAW,GAAW,CAAC,CAAC;QAC/B;;;WAGG;QACI,kBAAa,GAAW,CAAC,CAAC;QACjC;;;WAGG;QACI,gBAAW,GAAW,GAAG,CAAC;QACjC;;;WAGG;QACI,kBAAa,GAAW,QAAQ,CAAC;QAMxC;;WAEG;QACI,aAAQ,GAAQ,IAAI,CAAC;QAE5B;;WAEG;QACI,sBAAiB,GAAG,IAAI,UAAU,EAAS,CAAC;QAoD3C,kBAAa,GAAY,KAAK,CAAC;QAC/B,kBAAa,GAAW,YAAY,CAAC;QACrC,kBAAa,GAAW,CAAC,CAAC;QAC1B,eAAU,GAAY,KAAK,CAAC;QAC5B,eAAU,GAAW,CAAC,CAAC;QACvB,iBAAY,GAAW,CAAC,CAAC;QACzB,cAAS,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACpC,oBAAe,GAAY,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAChD,YAAO,GAAW,CAAC,CAAC;QACpB,mBAAc,GAAY,KAAK,CAAC;QAChC,mBAAc,GAAY,KAAK,CAAC;QASxC,oDAAoD;QACpD,gDAAgD;QACxC,oBAAe,GAAW,GAAG,CAAC;QAC9B,oBAAe,GAAW,GAAG,CAAC;QAC9B,mBAAc,GAAW,CAAC,CAAC;QAK3B,uBAAkB,GAAG,KAAK,CAAC;QAE3B,aAAQ,GAAoG,SAAS,CAAC;QAsB1H,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,KAAK,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,KAAK,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;QAE3C,IAAI,CAAC,oBAAoB,GAAG,mBAAmB,CAAC;QAChD,8DAA8D;QAC9D,6DAA6D;QAC7D,IAAI,CAAC,0BAA0B,GAAG,CAAC,aAAqB,EAAE,eAAuB,EAAE,WAAmB,EAAE,WAAmB,EAAE,aAAqB,EAAE,EAAE;YAClJ,IAAI,eAAe,GAAG,WAAW,EAAE,CAAC;gBAChC,OAAO,aAAa,GAAG,CAAC,CAAC,GAAG,eAAe,GAAG,WAAW,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,CAAC;YACb,CAAC;QACL,CAAC,CAAC;QACF,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;YAC1C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC;YACnC,4DAA4D;YAC5D,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC/B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;YAClC,CAAC;YACD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,KAAK,CAAC;YACnD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,GAAG,CAAC;YAC9C,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,KAAK,CAAC;YAClE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;YAChD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC;YAC5C,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;YACvD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;YAC/C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC;YAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;YAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QAClC,CAAC;QAED,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,cAAc,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACxF,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YACvE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;YAC1C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC;YACxC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,cAAc,GAAG,IAAI,CAAC;YAE1B,2FAA2F;YAC3F,IAAI,gBAAgB,EAAE,CAAC;gBACnB,IAAI,CAAC;oBACD,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE,CAAC;wBACvC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;wBACzB,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;oBACjC,CAAC;yBAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE,CAAC;wBACjD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;oBAClC,CAAC;yBAAM,IAAI,gBAAgB,YAAY,gBAAgB,EAAE,CAAC;wBACtD,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC;oBACnC,CAAC;yBAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE,CAAC;wBACjD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;oBAClC,CAAC;yBAAM,IAAI,gBAAgB,YAAY,WAAW,EAAE,CAAC;wBACjD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;oBAClC,CAAC;yBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC;wBACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;oBAC5B,CAAC;oBAED,IAAI,IAAI,GAAa,EAAE,CAAC;oBACxB,IAAI,mBAAmB,GAAG,KAAK,CAAC;oBAEhC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACpB,KAAK,cAAc;4BACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;4BACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4BAC3B,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;4BAE3G,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gCAChB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;4BAC7C,CAAC;4BAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gCAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BAChC,CAAC;4BACD,MAAM;wBACV,KAAK,aAAa;4BACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;4BACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4BAC3B,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;4BAE1G,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gCAChB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;4BAC7C,CAAC;4BAED,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gCAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;4BAChC,CAAC;4BACD,MAAM;wBACV,KAAK,aAAa;4BACd,IAAkB,gBAAiB,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;gCACjD,mBAAmB,GAAG,IAAI,CAAC;gCAC3B,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;4BACxC,CAAC;4BACD,MAAM;wBACV,KAAK,aAAa;4BACd,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;4BAC1C,MAAM;wBACV,KAAK,QAAQ;4BACT,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;wBAChC,0CAA0C;wBAC1C,KAAK,OAAO;4BACR,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gCACpB,IAAI,GAAG,gBAAgB,CAAC;4BAC5B,CAAC;4BACD,2EAA2E;4BAC3E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gCACnC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gCACpB,mBAAmB;oCACf,CAAC,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC;wCACnC,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,cAAc,CAAC,WAAW,CAAC,cAAc,CAAC;wCACzF,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,cAAc,CAAC,WAAW,CAAC,cAAc,CAAC;wCACzF,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;wCAC1C,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;wCAC1C,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;wCAC1C,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;gCAChC,IAAI,mBAAmB,EAAE,CAAC;oCACtB,gBAAgB;oCAChB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;wCACnB,IAAI,CAAC,MAAM,CAAC,SAAS,CACjB,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;4CACL,IAAI,CAAC,YAAY,CAAC,IAAmB,CAAC,CAAC;wCAC3C,CAAC,EACD,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,CAAC,SAAS,EAAE,EAAE;4CACV,IAAI,SAAS,EAAE,CAAC;gDACZ,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;4CACxE,CAAC;4CACD,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;4CACxC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;wCACjD,CAAC,CACJ,CAAC;oCACN,CAAC;oCACD,wCAAwC;yCACnC,CAAC;wCACF,IAAI,CAAC,iBAAiB,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;wCACxC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,GAAG,KAAK,CAAC;wCACxC,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;wCACxC,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;wCACnD,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,MAAM,CAAC;wCACxC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CACnC,gBAAgB,EAChB,GAAG,EAAE;4CACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4CAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gDAChB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;4CAC7C,CAAC;4CACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gDAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;4CAChC,CAAC;wCACL,CAAC,EACD,EAAE,IAAI,EAAE,IAAI,EAAE,CACjB,CAAC;wCACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;wCAClD,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;oCAClC,CAAC;oCACD,MAAM;gCACV,CAAC;4BACL,CAAC;4BACD,MAAM;wBACV;4BACI,cAAc,GAAG,KAAK,CAAC;4BACvB,MAAM;oBACd,CAAC;oBAED,IAAI,CAAC,cAAc,EAAE,CAAC;wBAClB,MAAM,CAAC,KAAK,CAAC,sGAAsG,CAAC,CAAC;oBACzH,CAAC;yBAAM,CAAC;wBACJ,IAAI,CAAC,mBAAmB,EAAE,CAAC;4BACvB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;4BAC3B,+DAA+D;4BAC/D,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gCAC5B,UAAU,CAAC,GAAG,EAAE;oCACZ,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wCAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;oCAChC,CAAC;gCACL,CAAC,EAAE,IAAI,CAAC,CAAC;4BACb,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;gBAAC,OAAO,EAAE,EAAE,CAAC;oBACV,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;oBAC1D,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACjD,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,iFAAiF;YACjF,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,cAAc,CAAC,WAAW,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC;gBACtF,MAAM,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;gBAC5D,cAAc,CAAC,WAAW,CAAC,yBAAyB,GAAG,IAAI,CAAC;YAChE,CAAC;YACD,qFAAqF;YACrF,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,UAAU,CAAC,GAAG,EAAE;oBACZ,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;oBAChC,CAAC;gBACL,CAAC,EAAE,IAAI,CAAC,CAAC;YACb,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;YAC7C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC;iBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBACjC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACjE,CAAC;YACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YAC3B,CAAC;YACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAC7B,CAAC;YACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACpB,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAC7B,CAAC;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YAEzB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBAC/B,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,EAAE,CAAC;gBAChC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAClD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAClC,CAAC;YAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;gBACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACjC,CAAC;YAED,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrD,IAAI,CAAC,uBAAuB,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAClF,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;YACxC,CAAC;YAED,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACtC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,kBAAkB,CAAC,MAAmB;QAC1C,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;YAC5C,OAAO;QACX,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;QACD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,SAAsB;QACvC,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;YAC5C,OAAO;QACX,CAAC;QACD,mEAAmE;QACnE,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,eAAe,CACnD,SAAS,EACT,CAAC,MAAM,EAAE,EAAE;YACP,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC,EACD,CAAC,GAAQ,EAAE,EAAE;YACT,MAAM,CAAC,KAAK,CAAC,uCAAuC,GAAG,IAAI,CAAC,IAAI,GAAG,YAAY,GAAG,GAAG,CAAC,CAAC;QAC3F,CAAC,CACJ,CAAC;IACN,CAAC;IAED;;;OAGG;IACI,cAAc,CAAC,WAAwB;QAC1C,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;YAC7C,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;YAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC/B,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,OAAsB;QACvC,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;YACtC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAC3D,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC;YACtF,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC;YACjE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;YAC3D,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC;YACjE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC;YAChE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC;YAC3C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC;YAC/D,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC;YAC7C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/C,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC5C,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;oBACzD,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;wBAC5C,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;oBAC5C,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBACpB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;wBAC1D,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;4BACvC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;wBACvC,CAAC;wBACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,IAAI,CAAC,OAAO,EAAE,CAAC;4BAC7E,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;wBAC/C,CAAC;wBACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;4BAC3E,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;wBACnE,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAEO,wBAAwB;QAC5B,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,cAAc,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACxF,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACxB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YAChG,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC7C,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAChC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACjD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC;YAC7C,CAAC;QACL,CAAC;IACL,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;QACvC,IAAI,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC/B,CAAC;IAEO,wBAAwB;QAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,OAAO;QACX,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,4DAA4D;gBAC5D,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,QAAQ,CAAC;gBAC3C,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,CAAC,CAAC;gBAClC,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,CAAC,CAAC;gBACpC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,aAAoB,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAoB,CAAC;gBAC5D,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACjD,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,aAAoB,CAAC;YAC/D,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,wBAAwB;QAC3B,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,8BAA8B;QACjC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAEO,mBAAmB;QACvB,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACxF,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,IAAI,CAAC,aAAoB,CAAC;QAC/D,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,4BAA4B,CAAC,mBAA8B;QAC9D,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtE,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;YACvC,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;YACnD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACnC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,cAAsB,EAAE,cAAsB,EAAE,aAAqB;QAC3F,IAAI,cAAc,GAAG,cAAc,EAAE,CAAC;YAClC,MAAM,CAAC,KAAK,CAAC,6FAA6F,CAAC,CAAC;YAC5G,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB,CAAC,KAAa;QAC9C,IAAI,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,eAAe,GAAG,KAAK,EAAE,CAAC;gBAC/B,MAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;YACX,CAAC;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACxF,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC5D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,yBAAyB,CAAC,KAAa;QAC9C,IAAI,KAAK,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAChC,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC/B,MAAM,CAAC,KAAK,CAAC,kGAAkG,CAAC,CAAC;gBACjH,OAAO;YACX,CAAC;YAED,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACxF,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC5D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,WAAoB;QACnC,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAErC,IACI,cAAc,CAAC,WAAW,EAAE,cAAc;YAC1C,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,YAAY;YACjB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACxB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACxB,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAC1B,CAAC;YACC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,uBAAuB,CAAC,iBAA0B;QACrD,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;QAEzC,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC/F,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;IACL,CAAC;IAEO,gBAAgB;QACpB,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACtD,OAAO;QACX,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC;QAC1D,MAAM,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;QACrE,SAAS,CAAC,SAAS,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,gBAAgB;IACT,0BAA0B;QAC7B,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACzJ,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,6BAA6B;gBACtD,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,6BAA6B,EAAE,CAAC,CAAC,MAAM,EAAE;gBACtG,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACjF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACjJ,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,sBAAsB,CAAC,QAAqI;QAC/J,IAAI,CAAC,0BAA0B,GAAG,QAAQ,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACI,IAAI,CAAC,IAAa,EAAE,MAAe,EAAE,MAAe;QACvD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,cAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;YAC9F,IAAI,CAAC;gBACD,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAElC,IAAI,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,CAAC;gBAC1I,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBAC/C,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;4BACnF,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;4BACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;4BACrD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;wBACzD,CAAC;wBACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;4BACtB,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;4BACxD,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;4BACxD,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;4BACtD,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gCAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC;4BAC5B,CAAC;iCAAM,CAAC;gCACJ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;4BAC7G,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;gBACD,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAClB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACnD,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;wBACjH,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,GAAG,EAAE;4BAClC,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACpB,CAAC,CAAC;wBACF,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;oBAC7D,CAAC;oBACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACxB,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;wBACnC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;4BACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;wBACxD,CAAC;oBACL,CAAC;oBACD,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACzB,wEAAwE;wBACxE,uEAAuE;wBACvE,0EAA0E;wBAC1E,wBAAwB;wBACxB,MAAM,SAAS,GAAG,GAAG,EAAE;4BACnB,IAAI,cAAc,CAAC,WAAW,EAAE,QAAQ,EAAE,CAAC;gCACvC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;oCAC1B,OAAO;gCACX,CAAC;gCAED,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,CAAC;gCACjD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;gCAElD,yDAAyD;gCACzD,gCAAgC;gCAChC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;oCAC5B,0CAA0C;oCAC1C,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE;wCACnB,6BAA6B;wCAC7B,sEAAsE;wCACtE,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC;wCACnC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;4CAC7B,IAAI,CAAC,sBAAsB,GAAG,cAAc,CAAC,WAAW,EAAE,yBAAyB,CAAC,OAAO,CAAC,GAAG,EAAE;gDAC7F,SAAS,EAAE,CAAC;4CAChB,CAAC,CAAC,CAAC;wCACP,CAAC;oCACL,CAAC,CAAC,CAAC;gCACP,CAAC;4BACL,CAAC;iCAAM,CAAC;gCACJ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oCAC7B,IAAI,CAAC,sBAAsB,GAAG,cAAc,CAAC,WAAW,EAAE,yBAAyB,CAAC,OAAO,CAAC,GAAG,EAAE;wCAC7F,SAAS,EAAE,CAAC;oCAChB,CAAC,CAAC,CAAC;gCACP,CAAC;4BACL,CAAC;wBACL,CAAC,CAAC;wBACF,SAAS,EAAE,CAAC;oBAChB,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,SAAS,GAAG,GAAG,EAAE;wBACnB,IAAI,cAAc,CAAC,WAAW,EAAE,YAAY,EAAE,CAAC;4BAC3C,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;4BAEhC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gCACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;4BAC5B,CAAC;4BAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gCACpB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC;gCACpC,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;oCACrB,SAAS,CAAC,UAAU,EAAE,CAAC;gCAC3B,CAAC,CAAC;4BACN,CAAC;4BACD,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,kBAAkB,EAAE,CAAC;4BAClF,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gCAC5C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;gCAC7C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gCAChD,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gCACnC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oCACvB,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,MAAM,CAAC;gCACzC,CAAC;gCACD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oCACvB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,CAAC,MAAO,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;gCACvD,CAAC;gCACD,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;gCAC1D,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE;oCAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;gCACpB,CAAC,CAAC;gCACF,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC;gCACrI,MAAM,YAAY,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAO,CAAC,QAAQ,CAAC;gCACzH,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;4BACrF,CAAC;wBACL,CAAC;oBACL,CAAC,CAAC;oBAEF,IAAI,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;wBACjE,uDAAuD;wBACvD,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC,GAAG,EAAE;4BACrC,IAAI,cAAc,CAAC,WAAW,EAAE,YAAa,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;gCAClE,6BAA6B;gCAC7B,sEAAsE;gCACtE,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gCAClC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oCAC7B,IAAI,CAAC,sBAAsB,GAAG,cAAc,CAAC,WAAW,CAAC,yBAAyB,CAAC,OAAO,CAAC,GAAG,EAAE;wCAC5F,SAAS,EAAE,CAAC;oCAChB,CAAC,CAAC,CAAC;gCACP,CAAC;4BACL,CAAC;iCAAM,CAAC;gCACJ,SAAS,EAAE,CAAC;4BAChB,CAAC;wBACL,CAAC,EAAE,GAAG,CAAC,CAAC;oBACZ,CAAC;yBAAM,CAAC;wBACJ,SAAS,EAAE,CAAC;oBAChB,CAAC;gBACL,CAAC;gBACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YAC1B,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC;gBACV,MAAM,CAAC,KAAK,CAAC,oCAAoC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC;YACvF,CAAC;QACL,CAAC;IACL,CAAC;IAEO,QAAQ;QACZ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,IAAI,CAAC,IAAa;QACrB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;oBAC/B,qEAAqE;oBACrE,IAAI,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;wBACzC,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG,CAAC,CAAC;oBAC3C,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,CAAC;gBACxC,CAAC;gBACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAC3B,CAAC;iBAAM,IAAI,cAAc,CAAC,WAAW,EAAE,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvE,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC/F,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE;oBAC7B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;oBACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;oBACpB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;oBACtB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;wBACpB,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;oBAC7C,CAAC;oBACD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACpB,CAAC,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YAC3B,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAC1B,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,CAAC;gBACxC,CAAC;gBACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACzB,CAAC;iBAAM,IAAI,cAAc,CAAC,WAAW,EAAE,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvE,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;gBACzC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBACzB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACrB,IAAI,CAAC,YAAY,IAAI,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;YAC/F,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,SAAiB,EAAE,IAAa;QAC7C,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAChE,IAAI,IAAI,IAAI,cAAc,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;gBAClD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBAChG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBACrH,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC;YACxH,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YAC3C,CAAC;QACL,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,eAAuB;QAC1C,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;QACrC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC5C,IAAI,CAAC,iBAAiB,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;YAC7D,CAAC;iBAAM,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC3B,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;YAC9D,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,aAA4B;QAC5C,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,CAAC,uBAAuB,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC9B,CAAC;QACD,IAAI,CAAC,uBAAuB,GAAG,aAAa,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACtB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC;QACD,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrE,IAAI,CAAC,aAAa,GAAG,CAAC,aAA4B,EAAE,EAAE,CAAC,IAAI,CAAC,iCAAiC,CAAC,aAAa,CAAC,CAAC;QAC7G,IAAI,CAAC,uBAAuB,CAAC,8BAA8B,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACpF,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,CAAC,uBAAuB,CAAC,gCAAgC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,CAAC;IACL,CAAC;IAEO,iCAAiC,CAAC,IAAmB;QACzD,IAAI,CAAO,IAAK,CAAC,eAAe,EAAE,CAAC;YAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,GAAG,IAAoB,CAAC;YAClC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAC5C,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,cAAc,CAAC,WAAW,EAAE,cAAc,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACtF,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACnB,MAAM,eAAe,GAAG,GAAG,EAAE;gBACzB,kBAAkB,CACd,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EACzB,GAAG,EAAE;oBACD,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;oBACjD,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;oBAClC,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;wBACvB,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;oBACpD,CAAC;gBACL,CAAC,EACD,SAAS,EACT,GAAG,CACN,CAAC;YACN,CAAC,CAAC;YAEF,MAAM,cAAc,GAAG;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,IAAI,CAAC,OAAO;gBACpB,YAAY,EAAE,IAAI,CAAC,aAAa;gBAChC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;aACpC,CAAC;YAEF,MAAM,WAAW,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;YAC5G,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,WAAW,CAAC,sBAAsB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxE,CAAC;YACD,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAChD,eAAe,EAAE,CAAC;YAElB,OAAO,WAAW,CAAC;QACvB,CAAC;QACD,gCAAgC;aAC3B,CAAC;YACF,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,cAAc;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ;YAC7B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,GAAG,EAAE,IAAI,CAAC,IAAI;YACd,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,OAAO;YACpB,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,aAAa;YAChC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SAC1B,CAAC;QAEF,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC;YAC1E,CAAC;YAED,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACxD,mBAAmB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACnD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;YAEvD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;YACxD,mBAAmB,CAAC,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC1E,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC1D,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC1D,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC5D,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,KAAK,CAAC,WAAgB,EAAE,KAAY,EAAE,OAAe,EAAE,WAAmB;QACpF,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC;QACnC,IAAI,QAAQ,CAAC;QAEb,IAAI,WAAW,CAAC,GAAG,EAAE,CAAC;YAClB,QAAQ,GAAG,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC;QACzC,CAAC;aAAM,CAAC;YACJ,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAC;QACnC,CAAC;QAED,MAAM,OAAO,GAAG;YACZ,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,YAAY,EAAE,WAAW,CAAC,YAAY;SACzC,CAAC;QAEF,IAAI,QAAe,CAAC;QAEpB,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,QAAQ,GAAG,IAAI,KAAK,CAChB,SAAS,EACT,QAAQ,EACR,KAAK,EACL,GAAG,EAAE;gBACD,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC,EACD,OAAO,CACV,CAAC;YACF,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACJ,MAAM,eAAe,GAAG,GAAG,EAAE;gBACzB,kBAAkB,CACd,GAAG,EAAE,CAAC,WAAW,CAAC,cAAc,EAChC,GAAG,EAAE;oBACD,QAAQ,CAAC,YAAY,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC;oBACrD,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC;oBAC/B,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;wBACpB,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;oBACzD,CAAC;gBACL,CAAC,EACD,SAAS,EACT,GAAG,CACN,CAAC;YACN,CAAC,CAAC;YAEF,QAAQ,GAAG,IAAI,KAAK,CAAC,SAAS,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC1E,eAAe,EAAE,CAAC;QACtB,CAAC;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;YACvB,MAAM,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC9D,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QACxC,CAAC;QACD,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC;YAC5B,QAAQ,CAAC,kBAAkB,CAAC,WAAW,CAAC,cAAc,IAAI,GAAG,EAAE,WAAW,CAAC,cAAc,IAAI,GAAG,EAAE,WAAW,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC;YAClI,IAAI,WAAW,CAAC,oBAAoB,EAAE,CAAC;gBACnC,MAAM,oBAAoB,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;gBACjF,QAAQ,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC;QACD,IAAI,WAAW,CAAC,eAAe,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG,KAAK,CAAC,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YACrE,IAAI,aAAa,EAAE,CAAC;gBAChB,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;QAED,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;YACvB,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;QAC7C,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,UAAU,CAAC,KAAc;QAC7B,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YACzB,OAAO;QACX,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC1B,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAEO,0BAA0B;QAC9B,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACrC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAClC,CAAC;QACD,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC9B,cAAc,CAAC,WAAW,EAAE,yBAAyB,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC1F,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACvC,CAAC;IACL,CAAC;;AA5nCD;;GAEG;AACW,mCAA6B,GAA2B,CAAC,CAAC,EAAE,EAAE;IACxE,MAAM,WAAW,CAAC,qBAAqB,CAAC,CAAC;AAC7C,CAAC,AAF0C,CAEzC;AA0nCN,sBAAsB;AACtB,aAAa,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC", "sourcesContent": ["import { Tools } from \"../Misc/tools\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport type { AbstractMesh } from \"../Meshes/abstractMesh\";\r\nimport type { TransformNode } from \"../Meshes/transformNode\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { _WarnImport } from \"../Misc/devTools\";\r\nimport type { ISoundOptions } from \"./Interfaces/ISoundOptions\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\nimport type { IAudioEngine } from \"./Interfaces/IAudioEngine\";\r\nimport type { Observer } from \"../Misc/observable\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport { AbstractEngine } from \"core/Engines/abstractEngine\";\r\nimport { _RetryWithInterval } from \"core/Misc/timingTools\";\r\n\r\n/**\r\n * Defines a sound that can be played in the application.\r\n * The sound can either be an ambient track or a simple sound played in reaction to a user action.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport class Sound {\r\n    /**\r\n     * The name of the sound in the scene.\r\n     */\r\n    public name: string;\r\n    /**\r\n     * Does the sound autoplay once loaded.\r\n     */\r\n    public autoplay: boolean = false;\r\n\r\n    private _loop = false;\r\n    /**\r\n     * Does the sound loop after it finishes playing once.\r\n     */\r\n    public get loop(): boolean {\r\n        return this._loop;\r\n    }\r\n\r\n    public set loop(value: boolean) {\r\n        if (value === this._loop) {\r\n            return;\r\n        }\r\n\r\n        this._loop = value;\r\n        this.updateOptions({ loop: value });\r\n    }\r\n\r\n    /**\r\n     * Does the sound use a custom attenuation curve to simulate the falloff\r\n     * happening when the source gets further away from the camera.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-your-own-custom-attenuation-function\r\n     */\r\n    public useCustomAttenuation: boolean = false;\r\n    /**\r\n     * The sound track id this sound belongs to.\r\n     */\r\n    public soundTrackId: number;\r\n    /**\r\n     * Is this sound currently played.\r\n     */\r\n    public isPlaying: boolean = false;\r\n    /**\r\n     * Is this sound currently paused.\r\n     */\r\n    public isPaused: boolean = false;\r\n    /**\r\n     * Define the reference distance the sound should be heard perfectly.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public refDistance: number = 1;\r\n    /**\r\n     * Define the roll off factor of spatial sounds.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public rolloffFactor: number = 1;\r\n    /**\r\n     * Define the max distance the sound should be heard (intensity just became 0 at this point).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public maxDistance: number = 100;\r\n    /**\r\n     * Define the distance attenuation model the sound will follow.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public distanceModel: string = \"linear\";\r\n    /**\r\n     * @internal\r\n     * Back Compat\r\n     **/\r\n    public onended: () => any;\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the sound.\r\n     */\r\n    public metadata: any = null;\r\n\r\n    /**\r\n     * Observable event when the current playing sound finishes.\r\n     */\r\n    public onEndedObservable = new Observable<Sound>();\r\n\r\n    /**\r\n     * Gets the current time for the sound.\r\n     */\r\n    public get currentTime(): number {\r\n        if (this._htmlAudioElement) {\r\n            return this._htmlAudioElement.currentTime;\r\n        }\r\n\r\n        if (AbstractEngine.audioEngine?.audioContext && (this.isPlaying || this.isPaused)) {\r\n            // The `_currentTime` member is only updated when the sound is paused. Add the time since the last start\r\n            // to get the actual current time.\r\n            const timeSinceLastStart = this.isPaused ? 0 : AbstractEngine.audioEngine.audioContext.currentTime - this._startTime;\r\n            return this._currentTime + timeSinceLastStart;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Does this sound enables spatial sound.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public get spatialSound(): boolean {\r\n        return this._spatialSound;\r\n    }\r\n\r\n    /**\r\n     * Does this sound enables spatial sound.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public set spatialSound(newValue: boolean) {\r\n        if (newValue == this._spatialSound) {\r\n            return;\r\n        }\r\n\r\n        const wasPlaying = this.isPlaying;\r\n        this.pause();\r\n\r\n        if (newValue) {\r\n            this._spatialSound = newValue;\r\n            this._updateSpatialParameters();\r\n        } else {\r\n            this._disableSpatialSound();\r\n        }\r\n\r\n        if (wasPlaying) {\r\n            this.play();\r\n        }\r\n    }\r\n\r\n    private _spatialSound: boolean = false;\r\n    private _panningModel: string = \"equalpower\";\r\n    private _playbackRate: number = 1;\r\n    private _streaming: boolean = false;\r\n    private _startTime: number = 0;\r\n    private _currentTime: number = 0;\r\n    private _position: Vector3 = Vector3.Zero();\r\n    private _localDirection: Vector3 = new Vector3(1, 0, 0);\r\n    private _volume: number = 1;\r\n    private _isReadyToPlay: boolean = false;\r\n    private _isDirectional: boolean = false;\r\n    private _readyToPlayCallback: Nullable<() => any>;\r\n    private _audioBuffer: Nullable<AudioBuffer>;\r\n    private _soundSource: Nullable<AudioBufferSourceNode>;\r\n    private _streamingSource: Nullable<AudioNode>;\r\n    private _soundPanner: Nullable<PannerNode>;\r\n    private _soundGain: Nullable<GainNode>;\r\n    private _inputAudioNode: Nullable<AudioNode>;\r\n    private _outputAudioNode: Nullable<AudioNode>;\r\n    // Used if you'd like to create a directional sound.\r\n    // If not set, the sound will be omnidirectional\r\n    private _coneInnerAngle: number = 360;\r\n    private _coneOuterAngle: number = 360;\r\n    private _coneOuterGain: number = 0;\r\n    private _scene: Scene;\r\n    private _connectedTransformNode: Nullable<TransformNode>;\r\n    private _customAttenuationFunction: (currentVolume: number, currentDistance: number, maxDistance: number, refDistance: number, rolloffFactor: number) => number;\r\n    private _registerFunc: Nullable<(connectedMesh: TransformNode) => void>;\r\n    private _isOutputConnected = false;\r\n    private _htmlAudioElement: Nullable<HTMLAudioElement>;\r\n    private _urlType: \"Unknown\" | \"String\" | \"Array\" | \"ArrayBuffer\" | \"MediaStream\" | \"AudioBuffer\" | \"MediaElement\" = \"Unknown\";\r\n    private _length?: number;\r\n    private _offset?: number;\r\n    private _tryToPlayTimeout: Nullable<NodeJS.Timeout>;\r\n    private _audioUnlockedObserver?: Nullable<Observer<IAudioEngine>>;\r\n    private _url?: Nullable<string>;\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _SceneComponentInitialization: (scene: Scene) => void = (_) => {\r\n        throw _WarnImport(\"AudioSceneComponent\");\r\n    };\r\n\r\n    /**\r\n     * Create a sound and attach it to a scene\r\n     * @param name Name of your sound\r\n     * @param urlOrArrayBuffer Url to the sound to load async or ArrayBuffer, it also works with MediaStreams and AudioBuffers\r\n     * @param scene defines the scene the sound belongs to\r\n     * @param readyToPlayCallback Provide a callback function if you'd like to load your code once the sound is ready to be played\r\n     * @param options Objects to provide with the current available options: autoplay, loop, volume, spatialSound, maxDistance, rolloffFactor, refDistance, distanceModel, panningModel, streaming\r\n     */\r\n    constructor(name: string, urlOrArrayBuffer: any, scene?: Nullable<Scene>, readyToPlayCallback: Nullable<() => void> = null, options?: ISoundOptions) {\r\n        this.name = name;\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this._scene = scene;\r\n        Sound._SceneComponentInitialization(scene);\r\n\r\n        this._readyToPlayCallback = readyToPlayCallback;\r\n        // Default custom attenuation function is a linear attenuation\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        this._customAttenuationFunction = (currentVolume: number, currentDistance: number, maxDistance: number, refDistance: number, rolloffFactor: number) => {\r\n            if (currentDistance < maxDistance) {\r\n                return currentVolume * (1 - currentDistance / maxDistance);\r\n            } else {\r\n                return 0;\r\n            }\r\n        };\r\n        if (options) {\r\n            this.autoplay = options.autoplay || false;\r\n            this._loop = options.loop || false;\r\n            // if volume === 0, we need another way to check this option\r\n            if (options.volume !== undefined) {\r\n                this._volume = options.volume;\r\n            }\r\n            this._spatialSound = options.spatialSound ?? false;\r\n            this.maxDistance = options.maxDistance ?? 100;\r\n            this.useCustomAttenuation = options.useCustomAttenuation ?? false;\r\n            this.rolloffFactor = options.rolloffFactor || 1;\r\n            this.refDistance = options.refDistance || 1;\r\n            this.distanceModel = options.distanceModel || \"linear\";\r\n            this._playbackRate = options.playbackRate || 1;\r\n            this._streaming = options.streaming ?? false;\r\n            this._length = options.length;\r\n            this._offset = options.offset;\r\n        }\r\n\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && AbstractEngine.audioEngine.audioContext) {\r\n            this._soundGain = AbstractEngine.audioEngine.audioContext.createGain();\r\n            this._soundGain.gain.value = this._volume;\r\n            this._inputAudioNode = this._soundGain;\r\n            this._outputAudioNode = this._soundGain;\r\n            if (this._spatialSound) {\r\n                this._createSpatialParameters();\r\n            }\r\n            this._scene.mainSoundTrack.addSound(this);\r\n            let validParameter = true;\r\n\r\n            // if no parameter is passed, you need to call setAudioBuffer yourself to prepare the sound\r\n            if (urlOrArrayBuffer) {\r\n                try {\r\n                    if (typeof urlOrArrayBuffer === \"string\") {\r\n                        this._urlType = \"String\";\r\n                        this._url = urlOrArrayBuffer;\r\n                    } else if (urlOrArrayBuffer instanceof ArrayBuffer) {\r\n                        this._urlType = \"ArrayBuffer\";\r\n                    } else if (urlOrArrayBuffer instanceof HTMLMediaElement) {\r\n                        this._urlType = \"MediaElement\";\r\n                    } else if (urlOrArrayBuffer instanceof MediaStream) {\r\n                        this._urlType = \"MediaStream\";\r\n                    } else if (urlOrArrayBuffer instanceof AudioBuffer) {\r\n                        this._urlType = \"AudioBuffer\";\r\n                    } else if (Array.isArray(urlOrArrayBuffer)) {\r\n                        this._urlType = \"Array\";\r\n                    }\r\n\r\n                    let urls: string[] = [];\r\n                    let codecSupportedFound = false;\r\n\r\n                    switch (this._urlType) {\r\n                        case \"MediaElement\":\r\n                            this._streaming = true;\r\n                            this._isReadyToPlay = true;\r\n                            this._streamingSource = AbstractEngine.audioEngine.audioContext.createMediaElementSource(urlOrArrayBuffer);\r\n\r\n                            if (this.autoplay) {\r\n                                this.play(0, this._offset, this._length);\r\n                            }\r\n\r\n                            if (this._readyToPlayCallback) {\r\n                                this._readyToPlayCallback();\r\n                            }\r\n                            break;\r\n                        case \"MediaStream\":\r\n                            this._streaming = true;\r\n                            this._isReadyToPlay = true;\r\n                            this._streamingSource = AbstractEngine.audioEngine.audioContext.createMediaStreamSource(urlOrArrayBuffer);\r\n\r\n                            if (this.autoplay) {\r\n                                this.play(0, this._offset, this._length);\r\n                            }\r\n\r\n                            if (this._readyToPlayCallback) {\r\n                                this._readyToPlayCallback();\r\n                            }\r\n                            break;\r\n                        case \"ArrayBuffer\":\r\n                            if ((<ArrayBuffer>urlOrArrayBuffer).byteLength > 0) {\r\n                                codecSupportedFound = true;\r\n                                this._soundLoaded(urlOrArrayBuffer);\r\n                            }\r\n                            break;\r\n                        case \"AudioBuffer\":\r\n                            this._audioBufferLoaded(urlOrArrayBuffer);\r\n                            break;\r\n                        case \"String\":\r\n                            urls.push(urlOrArrayBuffer);\r\n                        // eslint-disable-next-line no-fallthrough\r\n                        case \"Array\":\r\n                            if (urls.length === 0) {\r\n                                urls = urlOrArrayBuffer;\r\n                            }\r\n                            // If we found a supported format, we load it immediately and stop the loop\r\n                            for (let i = 0; i < urls.length; i++) {\r\n                                const url = urls[i];\r\n                                codecSupportedFound =\r\n                                    (options && options.skipCodecCheck) ||\r\n                                    (url.indexOf(\".mp3\", url.length - 4) !== -1 && AbstractEngine.audioEngine.isMP3supported) ||\r\n                                    (url.indexOf(\".ogg\", url.length - 4) !== -1 && AbstractEngine.audioEngine.isOGGsupported) ||\r\n                                    url.indexOf(\".wav\", url.length - 4) !== -1 ||\r\n                                    url.indexOf(\".m4a\", url.length - 4) !== -1 ||\r\n                                    url.indexOf(\".mp4\", url.length - 4) !== -1 ||\r\n                                    url.indexOf(\"blob:\") !== -1;\r\n                                if (codecSupportedFound) {\r\n                                    // Loading sound\r\n                                    if (!this._streaming) {\r\n                                        this._scene._loadFile(\r\n                                            url,\r\n                                            (data) => {\r\n                                                this._soundLoaded(data as ArrayBuffer);\r\n                                            },\r\n                                            undefined,\r\n                                            true,\r\n                                            true,\r\n                                            (exception) => {\r\n                                                if (exception) {\r\n                                                    Logger.Error(\"XHR \" + exception.status + \" error on: \" + url + \".\");\r\n                                                }\r\n                                                Logger.Error(\"Sound creation aborted.\");\r\n                                                this._scene.mainSoundTrack.removeSound(this);\r\n                                            }\r\n                                        );\r\n                                    }\r\n                                    // Streaming sound using HTML5 Audio tag\r\n                                    else {\r\n                                        this._htmlAudioElement = new Audio(url);\r\n                                        this._htmlAudioElement.controls = false;\r\n                                        this._htmlAudioElement.loop = this.loop;\r\n                                        Tools.SetCorsBehavior(url, this._htmlAudioElement);\r\n                                        this._htmlAudioElement.preload = \"auto\";\r\n                                        this._htmlAudioElement.addEventListener(\r\n                                            \"canplaythrough\",\r\n                                            () => {\r\n                                                this._isReadyToPlay = true;\r\n                                                if (this.autoplay) {\r\n                                                    this.play(0, this._offset, this._length);\r\n                                                }\r\n                                                if (this._readyToPlayCallback) {\r\n                                                    this._readyToPlayCallback();\r\n                                                }\r\n                                            },\r\n                                            { once: true }\r\n                                        );\r\n                                        document.body.appendChild(this._htmlAudioElement);\r\n                                        this._htmlAudioElement.load();\r\n                                    }\r\n                                    break;\r\n                                }\r\n                            }\r\n                            break;\r\n                        default:\r\n                            validParameter = false;\r\n                            break;\r\n                    }\r\n\r\n                    if (!validParameter) {\r\n                        Logger.Error(\"Parameter must be a URL to the sound, an Array of URLs (.mp3 & .ogg) or an ArrayBuffer of the sound.\");\r\n                    } else {\r\n                        if (!codecSupportedFound) {\r\n                            this._isReadyToPlay = true;\r\n                            // Simulating a ready to play event to avoid breaking code path\r\n                            if (this._readyToPlayCallback) {\r\n                                setTimeout(() => {\r\n                                    if (this._readyToPlayCallback) {\r\n                                        this._readyToPlayCallback();\r\n                                    }\r\n                                }, 1000);\r\n                            }\r\n                        }\r\n                    }\r\n                } catch (ex) {\r\n                    Logger.Error(\"Unexpected error. Sound creation aborted.\");\r\n                    this._scene.mainSoundTrack.removeSound(this);\r\n                }\r\n            }\r\n        } else {\r\n            // Adding an empty sound to avoid breaking audio calls for non Web Audio browsers\r\n            this._scene.mainSoundTrack.addSound(this);\r\n            if (AbstractEngine.audioEngine && !AbstractEngine.audioEngine.WarnedWebAudioUnsupported) {\r\n                Logger.Error(\"Web Audio is not supported by your browser.\");\r\n                AbstractEngine.audioEngine.WarnedWebAudioUnsupported = true;\r\n            }\r\n            // Simulating a ready to play event to avoid breaking code for non web audio browsers\r\n            if (this._readyToPlayCallback) {\r\n                setTimeout(() => {\r\n                    if (this._readyToPlayCallback) {\r\n                        this._readyToPlayCallback();\r\n                    }\r\n                }, 1000);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release the sound and its associated resources\r\n     */\r\n    public dispose() {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\r\n            if (this.isPlaying) {\r\n                this.stop();\r\n            }\r\n            this._isReadyToPlay = false;\r\n            if (this.soundTrackId === -1) {\r\n                this._scene.mainSoundTrack.removeSound(this);\r\n            } else if (this._scene.soundTracks) {\r\n                this._scene.soundTracks[this.soundTrackId].removeSound(this);\r\n            }\r\n            if (this._soundGain) {\r\n                this._soundGain.disconnect();\r\n                this._soundGain = null;\r\n            }\r\n            if (this._soundPanner) {\r\n                this._soundPanner.disconnect();\r\n                this._soundPanner = null;\r\n            }\r\n            if (this._soundSource) {\r\n                this._soundSource.disconnect();\r\n                this._soundSource = null;\r\n            }\r\n            this._audioBuffer = null;\r\n\r\n            if (this._htmlAudioElement) {\r\n                this._htmlAudioElement.pause();\r\n                this._htmlAudioElement.src = \"\";\r\n                document.body.removeChild(this._htmlAudioElement);\r\n                this._htmlAudioElement = null;\r\n            }\r\n\r\n            if (this._streamingSource) {\r\n                this._streamingSource.disconnect();\r\n                this._streamingSource = null;\r\n            }\r\n\r\n            if (this._connectedTransformNode && this._registerFunc) {\r\n                this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\r\n                this._connectedTransformNode = null;\r\n            }\r\n\r\n            this._clearTimeoutsAndObservers();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets if the sounds is ready to be played or not.\r\n     * @returns true if ready, otherwise false\r\n     */\r\n    public isReady(): boolean {\r\n        return this._isReadyToPlay;\r\n    }\r\n\r\n    /**\r\n     * Get the current class name.\r\n     * @returns current class name\r\n     */\r\n    public getClassName(): string {\r\n        return \"Sound\";\r\n    }\r\n\r\n    private _audioBufferLoaded(buffer: AudioBuffer) {\r\n        if (!AbstractEngine.audioEngine?.audioContext) {\r\n            return;\r\n        }\r\n        this._audioBuffer = buffer;\r\n        this._isReadyToPlay = true;\r\n        if (this.autoplay) {\r\n            this.play(0, this._offset, this._length);\r\n        }\r\n        if (this._readyToPlayCallback) {\r\n            this._readyToPlayCallback();\r\n        }\r\n    }\r\n\r\n    private _soundLoaded(audioData: ArrayBuffer) {\r\n        if (!AbstractEngine.audioEngine?.audioContext) {\r\n            return;\r\n        }\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        AbstractEngine.audioEngine.audioContext.decodeAudioData(\r\n            audioData,\r\n            (buffer) => {\r\n                this._audioBufferLoaded(buffer);\r\n            },\r\n            (err: any) => {\r\n                Logger.Error(\"Error while decoding audio data for: \" + this.name + \" / Error: \" + err);\r\n            }\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Sets the data of the sound from an audiobuffer\r\n     * @param audioBuffer The audioBuffer containing the data\r\n     */\r\n    public setAudioBuffer(audioBuffer: AudioBuffer): void {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio) {\r\n            this._audioBuffer = audioBuffer;\r\n            this._isReadyToPlay = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Updates the current sounds options such as maxdistance, loop...\r\n     * @param options A JSON object containing values named as the object properties\r\n     */\r\n    public updateOptions(options: ISoundOptions): void {\r\n        if (options) {\r\n            this.loop = options.loop ?? this.loop;\r\n            this.maxDistance = options.maxDistance ?? this.maxDistance;\r\n            this.useCustomAttenuation = options.useCustomAttenuation ?? this.useCustomAttenuation;\r\n            this.rolloffFactor = options.rolloffFactor ?? this.rolloffFactor;\r\n            this.refDistance = options.refDistance ?? this.refDistance;\r\n            this.distanceModel = options.distanceModel ?? this.distanceModel;\r\n            this._playbackRate = options.playbackRate ?? this._playbackRate;\r\n            this._length = options.length ?? undefined;\r\n            this.spatialSound = options.spatialSound ?? this._spatialSound;\r\n            this._setOffset(options.offset ?? undefined);\r\n            this.setVolume(options.volume ?? this._volume);\r\n            this._updateSpatialParameters();\r\n            if (this.isPlaying) {\r\n                if (this._streaming && this._htmlAudioElement) {\r\n                    this._htmlAudioElement.playbackRate = this._playbackRate;\r\n                    if (this._htmlAudioElement.loop !== this.loop) {\r\n                        this._htmlAudioElement.loop = this.loop;\r\n                    }\r\n                } else {\r\n                    if (this._soundSource) {\r\n                        this._soundSource.playbackRate.value = this._playbackRate;\r\n                        if (this._soundSource.loop !== this.loop) {\r\n                            this._soundSource.loop = this.loop;\r\n                        }\r\n                        if (this._offset !== undefined && this._soundSource.loopStart !== this._offset) {\r\n                            this._soundSource.loopStart = this._offset;\r\n                        }\r\n                        if (this._length !== undefined && this._length !== this._soundSource.loopEnd) {\r\n                            this._soundSource.loopEnd = (this._offset! | 0) + this._length;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private _createSpatialParameters() {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && AbstractEngine.audioEngine.audioContext) {\r\n            if (this._scene.headphone) {\r\n                this._panningModel = \"HRTF\";\r\n            }\r\n            this._soundPanner = this._soundPanner ?? AbstractEngine.audioEngine.audioContext.createPanner();\r\n            if (this._soundPanner && this._outputAudioNode) {\r\n                this._updateSpatialParameters();\r\n                this._soundPanner.connect(this._outputAudioNode);\r\n                this._inputAudioNode = this._soundPanner;\r\n            }\r\n        }\r\n    }\r\n\r\n    private _disableSpatialSound() {\r\n        if (!this._spatialSound) {\r\n            return;\r\n        }\r\n        this._inputAudioNode = this._soundGain;\r\n        this._soundPanner?.disconnect();\r\n        this._soundPanner = null;\r\n        this._spatialSound = false;\r\n    }\r\n\r\n    private _updateSpatialParameters() {\r\n        if (!this._spatialSound) {\r\n            return;\r\n        }\r\n        if (this._soundPanner) {\r\n            if (this.useCustomAttenuation) {\r\n                // Tricks to disable in a way embedded Web Audio attenuation\r\n                this._soundPanner.distanceModel = \"linear\";\r\n                this._soundPanner.maxDistance = Number.MAX_VALUE;\r\n                this._soundPanner.refDistance = 1;\r\n                this._soundPanner.rolloffFactor = 1;\r\n                this._soundPanner.panningModel = this._panningModel as any;\r\n            } else {\r\n                this._soundPanner.distanceModel = this.distanceModel as any;\r\n                this._soundPanner.maxDistance = this.maxDistance;\r\n                this._soundPanner.refDistance = this.refDistance;\r\n                this._soundPanner.rolloffFactor = this.rolloffFactor;\r\n                this._soundPanner.panningModel = this._panningModel as any;\r\n            }\r\n        } else {\r\n            this._createSpatialParameters();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to HRTF:\r\n     * Renders a stereo output of higher quality than equalpower — it uses a convolution with measured impulse responses from human subjects.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToHRTF() {\r\n        this._panningModel = \"HRTF\";\r\n        this._switchPanningModel();\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to Equal Power:\r\n     * Represents the equal-power panning algorithm, generally regarded as simple and efficient. equalpower is the default value.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToEqualPower() {\r\n        this._panningModel = \"equalpower\";\r\n        this._switchPanningModel();\r\n    }\r\n\r\n    private _switchPanningModel() {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\r\n            this._soundPanner.panningModel = this._panningModel as any;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connect this sound to a sound track audio node like gain...\r\n     * @param soundTrackAudioNode the sound track audio node to connect to\r\n     */\r\n    public connectToSoundTrackAudioNode(soundTrackAudioNode: AudioNode): void {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\r\n            if (this._isOutputConnected) {\r\n                this._outputAudioNode.disconnect();\r\n            }\r\n            this._outputAudioNode.connect(soundTrackAudioNode);\r\n            this._isOutputConnected = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Transform this sound into a directional source\r\n     * @param coneInnerAngle Size of the inner cone in degree\r\n     * @param coneOuterAngle Size of the outer cone in degree\r\n     * @param coneOuterGain Volume of the sound outside the outer cone (between 0.0 and 1.0)\r\n     */\r\n    public setDirectionalCone(coneInnerAngle: number, coneOuterAngle: number, coneOuterGain: number): void {\r\n        if (coneOuterAngle < coneInnerAngle) {\r\n            Logger.Error(\"setDirectionalCone(): outer angle of the cone must be superior or equal to the inner angle.\");\r\n            return;\r\n        }\r\n        this._coneInnerAngle = coneInnerAngle;\r\n        this._coneOuterAngle = coneOuterAngle;\r\n        this._coneOuterGain = coneOuterGain;\r\n        this._isDirectional = true;\r\n\r\n        if (this.isPlaying && this.loop) {\r\n            this.stop();\r\n            this.play(0, this._offset, this._length);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the inner angle for the directional cone.\r\n     */\r\n    public get directionalConeInnerAngle(): number {\r\n        return this._coneInnerAngle;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the inner angle for the directional cone.\r\n     */\r\n    public set directionalConeInnerAngle(value: number) {\r\n        if (value != this._coneInnerAngle) {\r\n            if (this._coneOuterAngle < value) {\r\n                Logger.Error(\"directionalConeInnerAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneInnerAngle = value;\r\n            if (AbstractEngine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\r\n                this._soundPanner.coneInnerAngle = this._coneInnerAngle;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the outer angle for the directional cone.\r\n     */\r\n    public get directionalConeOuterAngle(): number {\r\n        return this._coneOuterAngle;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the outer angle for the directional cone.\r\n     */\r\n    public set directionalConeOuterAngle(value: number) {\r\n        if (value != this._coneOuterAngle) {\r\n            if (value < this._coneInnerAngle) {\r\n                Logger.Error(\"directionalConeOuterAngle: outer angle of the cone must be superior or equal to the inner angle.\");\r\n                return;\r\n            }\r\n\r\n            this._coneOuterAngle = value;\r\n            if (AbstractEngine.audioEngine?.canUseWebAudio && this._spatialSound && this._soundPanner) {\r\n                this._soundPanner.coneOuterAngle = this._coneOuterAngle;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the position of the emitter if spatial sound is enabled\r\n     * @param newPosition Defines the new position\r\n     */\r\n    public setPosition(newPosition: Vector3): void {\r\n        if (newPosition.equals(this._position)) {\r\n            return;\r\n        }\r\n        this._position.copyFrom(newPosition);\r\n\r\n        if (\r\n            AbstractEngine.audioEngine?.canUseWebAudio &&\r\n            this._spatialSound &&\r\n            this._soundPanner &&\r\n            !isNaN(this._position.x) &&\r\n            !isNaN(this._position.y) &&\r\n            !isNaN(this._position.z)\r\n        ) {\r\n            this._soundPanner.positionX.value = this._position.x;\r\n            this._soundPanner.positionY.value = this._position.y;\r\n            this._soundPanner.positionZ.value = this._position.z;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the local direction of the emitter if spatial sound is enabled\r\n     * @param newLocalDirection Defines the new local direction\r\n     */\r\n    public setLocalDirectionToMesh(newLocalDirection: Vector3): void {\r\n        this._localDirection = newLocalDirection;\r\n\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._connectedTransformNode && this.isPlaying) {\r\n            this._updateDirection();\r\n        }\r\n    }\r\n\r\n    private _updateDirection() {\r\n        if (!this._connectedTransformNode || !this._soundPanner) {\r\n            return;\r\n        }\r\n\r\n        const mat = this._connectedTransformNode.getWorldMatrix();\r\n        const direction = Vector3.TransformNormal(this._localDirection, mat);\r\n        direction.normalize();\r\n        this._soundPanner.orientationX.value = direction.x;\r\n        this._soundPanner.orientationY.value = direction.y;\r\n        this._soundPanner.orientationZ.value = direction.z;\r\n    }\r\n\r\n    /** @internal */\r\n    public updateDistanceFromListener() {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._connectedTransformNode && this.useCustomAttenuation && this._soundGain && this._scene.activeCamera) {\r\n            const distance = this._scene.audioListenerPositionProvider\r\n                ? this._connectedTransformNode.position.subtract(this._scene.audioListenerPositionProvider()).length()\r\n                : this._connectedTransformNode.getDistanceToCamera(this._scene.activeCamera);\r\n            this._soundGain.gain.value = this._customAttenuationFunction(this._volume, distance, this.maxDistance, this.refDistance, this.rolloffFactor);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a new custom attenuation function for the sound.\r\n     * @param callback Defines the function used for the attenuation\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-your-own-custom-attenuation-function\r\n     */\r\n    public setAttenuationFunction(callback: (currentVolume: number, currentDistance: number, maxDistance: number, refDistance: number, rolloffFactor: number) => number): void {\r\n        this._customAttenuationFunction = callback;\r\n    }\r\n\r\n    /**\r\n     * Play the sound\r\n     * @param time (optional) Start the sound after X seconds. Start immediately (0) by default.\r\n     * @param offset (optional) Start the sound at a specific time in seconds\r\n     * @param length (optional) Sound duration (in seconds)\r\n     */\r\n    public play(time?: number, offset?: number, length?: number): void {\r\n        if (this._isReadyToPlay && this._scene.audioEnabled && AbstractEngine.audioEngine?.audioContext) {\r\n            try {\r\n                this._clearTimeoutsAndObservers();\r\n\r\n                let startTime = time ? AbstractEngine.audioEngine?.audioContext.currentTime + time : AbstractEngine.audioEngine?.audioContext.currentTime;\r\n                if (!this._soundSource || !this._streamingSource) {\r\n                    if (this._spatialSound && this._soundPanner) {\r\n                        if (!isNaN(this._position.x) && !isNaN(this._position.y) && !isNaN(this._position.z)) {\r\n                            this._soundPanner.positionX.value = this._position.x;\r\n                            this._soundPanner.positionY.value = this._position.y;\r\n                            this._soundPanner.positionZ.value = this._position.z;\r\n                        }\r\n                        if (this._isDirectional) {\r\n                            this._soundPanner.coneInnerAngle = this._coneInnerAngle;\r\n                            this._soundPanner.coneOuterAngle = this._coneOuterAngle;\r\n                            this._soundPanner.coneOuterGain = this._coneOuterGain;\r\n                            if (this._connectedTransformNode) {\r\n                                this._updateDirection();\r\n                            } else {\r\n                                this._soundPanner.setOrientation(this._localDirection.x, this._localDirection.y, this._localDirection.z);\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                if (this._streaming) {\r\n                    if (!this._streamingSource && this._htmlAudioElement) {\r\n                        this._streamingSource = AbstractEngine.audioEngine.audioContext.createMediaElementSource(this._htmlAudioElement);\r\n                        this._htmlAudioElement.onended = () => {\r\n                            this._onended();\r\n                        };\r\n                        this._htmlAudioElement.playbackRate = this._playbackRate;\r\n                    }\r\n                    if (this._streamingSource) {\r\n                        this._streamingSource.disconnect();\r\n                        if (this._inputAudioNode) {\r\n                            this._streamingSource.connect(this._inputAudioNode);\r\n                        }\r\n                    }\r\n                    if (this._htmlAudioElement) {\r\n                        // required to manage properly the new suspended default state of Chrome\r\n                        // When the option 'streaming: true' is used, we need first to wait for\r\n                        // the audio engine to be unlocked by a user gesture before trying to play\r\n                        // an HTML Audio element\r\n                        const tryToPlay = () => {\r\n                            if (AbstractEngine.audioEngine?.unlocked) {\r\n                                if (!this._htmlAudioElement) {\r\n                                    return;\r\n                                }\r\n\r\n                                this._htmlAudioElement.currentTime = offset ?? 0;\r\n                                const playPromise = this._htmlAudioElement.play();\r\n\r\n                                // In browsers that don’t yet support this functionality,\r\n                                // playPromise won’t be defined.\r\n                                if (playPromise !== undefined) {\r\n                                    // eslint-disable-next-line github/no-then\r\n                                    playPromise.catch(() => {\r\n                                        // Automatic playback failed.\r\n                                        // Waiting for the audio engine to be unlocked by user click on unmute\r\n                                        AbstractEngine.audioEngine?.lock();\r\n                                        if (this.loop || this.autoplay) {\r\n                                            this._audioUnlockedObserver = AbstractEngine.audioEngine?.onAudioUnlockedObservable.addOnce(() => {\r\n                                                tryToPlay();\r\n                                            });\r\n                                        }\r\n                                    });\r\n                                }\r\n                            } else {\r\n                                if (this.loop || this.autoplay) {\r\n                                    this._audioUnlockedObserver = AbstractEngine.audioEngine?.onAudioUnlockedObservable.addOnce(() => {\r\n                                        tryToPlay();\r\n                                    });\r\n                                }\r\n                            }\r\n                        };\r\n                        tryToPlay();\r\n                    }\r\n                } else {\r\n                    const tryToPlay = () => {\r\n                        if (AbstractEngine.audioEngine?.audioContext) {\r\n                            length = length || this._length;\r\n\r\n                            if (offset !== undefined) {\r\n                                this._setOffset(offset);\r\n                            }\r\n\r\n                            if (this._soundSource) {\r\n                                const oldSource = this._soundSource;\r\n                                oldSource.onended = () => {\r\n                                    oldSource.disconnect();\r\n                                };\r\n                            }\r\n                            this._soundSource = AbstractEngine.audioEngine?.audioContext.createBufferSource();\r\n                            if (this._soundSource && this._inputAudioNode) {\r\n                                this._soundSource.buffer = this._audioBuffer;\r\n                                this._soundSource.connect(this._inputAudioNode);\r\n                                this._soundSource.loop = this.loop;\r\n                                if (offset !== undefined) {\r\n                                    this._soundSource.loopStart = offset;\r\n                                }\r\n                                if (length !== undefined) {\r\n                                    this._soundSource.loopEnd = (offset! | 0) + length;\r\n                                }\r\n                                this._soundSource.playbackRate.value = this._playbackRate;\r\n                                this._soundSource.onended = () => {\r\n                                    this._onended();\r\n                                };\r\n                                startTime = time ? AbstractEngine.audioEngine?.audioContext.currentTime + time : AbstractEngine.audioEngine.audioContext.currentTime;\r\n                                const actualOffset = ((this.isPaused ? this.currentTime : 0) + (this._offset ?? 0)) % this._soundSource.buffer!.duration;\r\n                                this._soundSource.start(startTime, actualOffset, this.loop ? undefined : length);\r\n                            }\r\n                        }\r\n                    };\r\n\r\n                    if (AbstractEngine.audioEngine?.audioContext.state === \"suspended\") {\r\n                        // Wait a bit for FF as context seems late to be ready.\r\n                        this._tryToPlayTimeout = setTimeout(() => {\r\n                            if (AbstractEngine.audioEngine?.audioContext!.state === \"suspended\") {\r\n                                // Automatic playback failed.\r\n                                // Waiting for the audio engine to be unlocked by user click on unmute\r\n                                AbstractEngine.audioEngine.lock();\r\n                                if (this.loop || this.autoplay) {\r\n                                    this._audioUnlockedObserver = AbstractEngine.audioEngine.onAudioUnlockedObservable.addOnce(() => {\r\n                                        tryToPlay();\r\n                                    });\r\n                                }\r\n                            } else {\r\n                                tryToPlay();\r\n                            }\r\n                        }, 500);\r\n                    } else {\r\n                        tryToPlay();\r\n                    }\r\n                }\r\n                this._startTime = startTime;\r\n                this.isPlaying = true;\r\n                this.isPaused = false;\r\n            } catch (ex) {\r\n                Logger.Error(\"Error while trying to play audio: \" + this.name + \", \" + ex.message);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _onended() {\r\n        this.isPlaying = false;\r\n        this._startTime = 0;\r\n        this._currentTime = 0;\r\n        if (this.onended) {\r\n            this.onended();\r\n        }\r\n        this.onEndedObservable.notifyObservers(this);\r\n    }\r\n\r\n    /**\r\n     * Stop the sound\r\n     * @param time (optional) Stop the sound after X seconds. Stop immediately (0) by default.\r\n     */\r\n    public stop(time?: number): void {\r\n        if (this.isPlaying) {\r\n            this._clearTimeoutsAndObservers();\r\n            if (this._streaming) {\r\n                if (this._htmlAudioElement) {\r\n                    this._htmlAudioElement.pause();\r\n                    // Test needed for Firefox or it will generate an Invalid State Error\r\n                    if (this._htmlAudioElement.currentTime > 0) {\r\n                        this._htmlAudioElement.currentTime = 0;\r\n                    }\r\n                } else {\r\n                    this._streamingSource?.disconnect();\r\n                }\r\n                this.isPlaying = false;\r\n            } else if (AbstractEngine.audioEngine?.audioContext && this._soundSource) {\r\n                const stopTime = time ? AbstractEngine.audioEngine.audioContext.currentTime + time : undefined;\r\n                this._soundSource.onended = () => {\r\n                    this.isPlaying = false;\r\n                    this.isPaused = false;\r\n                    this._startTime = 0;\r\n                    this._currentTime = 0;\r\n                    if (this._soundSource) {\r\n                        this._soundSource.onended = () => void 0;\r\n                    }\r\n                    this._onended();\r\n                };\r\n                this._soundSource.stop(stopTime);\r\n            } else {\r\n                this.isPlaying = false;\r\n            }\r\n        } else if (this.isPaused) {\r\n            this.isPaused = false;\r\n            this._startTime = 0;\r\n            this._currentTime = 0;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Put the sound in pause\r\n     */\r\n    public pause(): void {\r\n        if (this.isPlaying) {\r\n            this._clearTimeoutsAndObservers();\r\n            if (this._streaming) {\r\n                if (this._htmlAudioElement) {\r\n                    this._htmlAudioElement.pause();\r\n                } else {\r\n                    this._streamingSource?.disconnect();\r\n                }\r\n                this.isPlaying = false;\r\n                this.isPaused = true;\r\n            } else if (AbstractEngine.audioEngine?.audioContext && this._soundSource) {\r\n                this._soundSource.onended = () => void 0;\r\n                this._soundSource.stop();\r\n                this.isPlaying = false;\r\n                this.isPaused = true;\r\n                this._currentTime += AbstractEngine.audioEngine.audioContext.currentTime - this._startTime;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets a dedicated volume for this sounds\r\n     * @param newVolume Define the new volume of the sound\r\n     * @param time Define time for gradual change to new volume\r\n     */\r\n    public setVolume(newVolume: number, time?: number): void {\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._soundGain) {\r\n            if (time && AbstractEngine.audioEngine.audioContext) {\r\n                this._soundGain.gain.cancelScheduledValues(AbstractEngine.audioEngine.audioContext.currentTime);\r\n                this._soundGain.gain.setValueAtTime(this._soundGain.gain.value, AbstractEngine.audioEngine.audioContext.currentTime);\r\n                this._soundGain.gain.linearRampToValueAtTime(newVolume, AbstractEngine.audioEngine.audioContext.currentTime + time);\r\n            } else {\r\n                this._soundGain.gain.value = newVolume;\r\n            }\r\n        }\r\n        this._volume = newVolume;\r\n    }\r\n\r\n    /**\r\n     * Set the sound play back rate\r\n     * @param newPlaybackRate Define the playback rate the sound should be played at\r\n     */\r\n    public setPlaybackRate(newPlaybackRate: number): void {\r\n        this._playbackRate = newPlaybackRate;\r\n        if (this.isPlaying) {\r\n            if (this._streaming && this._htmlAudioElement) {\r\n                this._htmlAudioElement.playbackRate = this._playbackRate;\r\n            } else if (this._soundSource) {\r\n                this._soundSource.playbackRate.value = this._playbackRate;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the sound play back rate.\r\n     * @returns the  play back rate of the sound\r\n     */\r\n    public getPlaybackRate(): number {\r\n        return this._playbackRate;\r\n    }\r\n\r\n    /**\r\n     * Gets the volume of the sound.\r\n     * @returns the volume of the sound\r\n     */\r\n    public getVolume(): number {\r\n        return this._volume;\r\n    }\r\n\r\n    /**\r\n     * Attach the sound to a dedicated mesh\r\n     * @param transformNode The transform node to connect the sound with\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#attaching-a-sound-to-a-mesh\r\n     */\r\n    public attachToMesh(transformNode: TransformNode): void {\r\n        if (this._connectedTransformNode && this._registerFunc) {\r\n            this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\r\n            this._registerFunc = null;\r\n        }\r\n        this._connectedTransformNode = transformNode;\r\n        if (!this._spatialSound) {\r\n            this._spatialSound = true;\r\n            this._createSpatialParameters();\r\n            if (this.isPlaying && this.loop) {\r\n                this.stop();\r\n                this.play(0, this._offset, this._length);\r\n            }\r\n        }\r\n        this._onRegisterAfterWorldMatrixUpdate(this._connectedTransformNode);\r\n        this._registerFunc = (transformNode: TransformNode) => this._onRegisterAfterWorldMatrixUpdate(transformNode);\r\n        this._connectedTransformNode.registerAfterWorldMatrixUpdate(this._registerFunc);\r\n    }\r\n\r\n    /**\r\n     * Detach the sound from the previously attached mesh\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#attaching-a-sound-to-a-mesh\r\n     */\r\n    public detachFromMesh() {\r\n        if (this._connectedTransformNode && this._registerFunc) {\r\n            this._connectedTransformNode.unregisterAfterWorldMatrixUpdate(this._registerFunc);\r\n            this._registerFunc = null;\r\n            this._connectedTransformNode = null;\r\n        }\r\n    }\r\n\r\n    private _onRegisterAfterWorldMatrixUpdate(node: TransformNode): void {\r\n        if (!(<any>node).getBoundingInfo) {\r\n            this.setPosition(node.absolutePosition);\r\n        } else {\r\n            const mesh = node as AbstractMesh;\r\n            const boundingInfo = mesh.getBoundingInfo();\r\n            this.setPosition(boundingInfo.boundingSphere.centerWorld);\r\n        }\r\n        if (AbstractEngine.audioEngine?.canUseWebAudio && this._isDirectional && this.isPlaying) {\r\n            this._updateDirection();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clone the current sound in the scene.\r\n     * @returns the new sound clone\r\n     */\r\n    public clone(): Nullable<Sound> {\r\n        if (!this._streaming) {\r\n            const setBufferAndRun = () => {\r\n                _RetryWithInterval(\r\n                    () => this._isReadyToPlay,\r\n                    () => {\r\n                        clonedSound._audioBuffer = this.getAudioBuffer();\r\n                        clonedSound._isReadyToPlay = true;\r\n                        if (clonedSound.autoplay) {\r\n                            clonedSound.play(0, this._offset, this._length);\r\n                        }\r\n                    },\r\n                    undefined,\r\n                    300\r\n                );\r\n            };\r\n\r\n            const currentOptions = {\r\n                autoplay: this.autoplay,\r\n                loop: this.loop,\r\n                volume: this._volume,\r\n                spatialSound: this._spatialSound,\r\n                maxDistance: this.maxDistance,\r\n                useCustomAttenuation: this.useCustomAttenuation,\r\n                rolloffFactor: this.rolloffFactor,\r\n                refDistance: this.refDistance,\r\n                distanceModel: this.distanceModel,\r\n            };\r\n\r\n            const clonedSound = new Sound(this.name + \"_cloned\", new ArrayBuffer(0), this._scene, null, currentOptions);\r\n            if (this.useCustomAttenuation) {\r\n                clonedSound.setAttenuationFunction(this._customAttenuationFunction);\r\n            }\r\n            clonedSound.setPosition(this._position);\r\n            clonedSound.setPlaybackRate(this._playbackRate);\r\n            setBufferAndRun();\r\n\r\n            return clonedSound;\r\n        }\r\n        // Can't clone a streaming sound\r\n        else {\r\n            return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the current underlying audio buffer containing the data\r\n     * @returns the audio buffer\r\n     */\r\n    public getAudioBuffer(): Nullable<AudioBuffer> {\r\n        return this._audioBuffer;\r\n    }\r\n\r\n    /**\r\n     * Gets the WebAudio AudioBufferSourceNode, lets you keep track of and stop instances of this Sound.\r\n     * @returns the source node\r\n     */\r\n    public getSoundSource(): Nullable<AudioBufferSourceNode> {\r\n        return this._soundSource;\r\n    }\r\n\r\n    /**\r\n     * Gets the WebAudio GainNode, gives you precise control over the gain of instances of this Sound.\r\n     * @returns the gain node\r\n     */\r\n    public getSoundGain(): Nullable<GainNode> {\r\n        return this._soundGain;\r\n    }\r\n\r\n    /**\r\n     * Serializes the Sound in a JSON representation\r\n     * @returns the JSON representation of the sound\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {\r\n            name: this.name,\r\n            url: this._url,\r\n            autoplay: this.autoplay,\r\n            loop: this.loop,\r\n            volume: this._volume,\r\n            spatialSound: this._spatialSound,\r\n            maxDistance: this.maxDistance,\r\n            rolloffFactor: this.rolloffFactor,\r\n            refDistance: this.refDistance,\r\n            distanceModel: this.distanceModel,\r\n            playbackRate: this._playbackRate,\r\n            panningModel: this._panningModel,\r\n            soundTrackId: this.soundTrackId,\r\n            metadata: this.metadata,\r\n        };\r\n\r\n        if (this._spatialSound) {\r\n            if (this._connectedTransformNode) {\r\n                serializationObject.connectedMeshId = this._connectedTransformNode.id;\r\n            }\r\n\r\n            serializationObject.position = this._position.asArray();\r\n            serializationObject.refDistance = this.refDistance;\r\n            serializationObject.distanceModel = this.distanceModel;\r\n\r\n            serializationObject.isDirectional = this._isDirectional;\r\n            serializationObject.localDirectionToMesh = this._localDirection.asArray();\r\n            serializationObject.coneInnerAngle = this._coneInnerAngle;\r\n            serializationObject.coneOuterAngle = this._coneOuterAngle;\r\n            serializationObject.coneOuterGain = this._coneOuterGain;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Parse a JSON representation of a sound to instantiate in a given scene\r\n     * @param parsedSound Define the JSON representation of the sound (usually coming from the serialize method)\r\n     * @param scene Define the scene the new parsed sound should be created in\r\n     * @param rootUrl Define the rooturl of the load in case we need to fetch relative dependencies\r\n     * @param sourceSound Define a sound place holder if do not need to instantiate a new one\r\n     * @returns the newly parsed sound\r\n     */\r\n    public static Parse(parsedSound: any, scene: Scene, rootUrl: string, sourceSound?: Sound): Sound {\r\n        const soundName = parsedSound.name;\r\n        let soundUrl;\r\n\r\n        if (parsedSound.url) {\r\n            soundUrl = rootUrl + parsedSound.url;\r\n        } else {\r\n            soundUrl = rootUrl + soundName;\r\n        }\r\n\r\n        const options = {\r\n            autoplay: parsedSound.autoplay,\r\n            loop: parsedSound.loop,\r\n            volume: parsedSound.volume,\r\n            spatialSound: parsedSound.spatialSound,\r\n            maxDistance: parsedSound.maxDistance,\r\n            rolloffFactor: parsedSound.rolloffFactor,\r\n            refDistance: parsedSound.refDistance,\r\n            distanceModel: parsedSound.distanceModel,\r\n            playbackRate: parsedSound.playbackRate,\r\n        };\r\n\r\n        let newSound: Sound;\r\n\r\n        if (!sourceSound) {\r\n            newSound = new Sound(\r\n                soundName,\r\n                soundUrl,\r\n                scene,\r\n                () => {\r\n                    scene.removePendingData(newSound);\r\n                },\r\n                options\r\n            );\r\n            scene.addPendingData(newSound);\r\n        } else {\r\n            const setBufferAndRun = () => {\r\n                _RetryWithInterval(\r\n                    () => sourceSound._isReadyToPlay,\r\n                    () => {\r\n                        newSound._audioBuffer = sourceSound.getAudioBuffer();\r\n                        newSound._isReadyToPlay = true;\r\n                        if (newSound.autoplay) {\r\n                            newSound.play(0, newSound._offset, newSound._length);\r\n                        }\r\n                    },\r\n                    undefined,\r\n                    300\r\n                );\r\n            };\r\n\r\n            newSound = new Sound(soundName, new ArrayBuffer(0), scene, null, options);\r\n            setBufferAndRun();\r\n        }\r\n\r\n        if (parsedSound.position) {\r\n            const soundPosition = Vector3.FromArray(parsedSound.position);\r\n            newSound.setPosition(soundPosition);\r\n        }\r\n        if (parsedSound.isDirectional) {\r\n            newSound.setDirectionalCone(parsedSound.coneInnerAngle || 360, parsedSound.coneOuterAngle || 360, parsedSound.coneOuterGain || 0);\r\n            if (parsedSound.localDirectionToMesh) {\r\n                const localDirectionToMesh = Vector3.FromArray(parsedSound.localDirectionToMesh);\r\n                newSound.setLocalDirectionToMesh(localDirectionToMesh);\r\n            }\r\n        }\r\n        if (parsedSound.connectedMeshId) {\r\n            const connectedMesh = scene.getMeshById(parsedSound.connectedMeshId);\r\n            if (connectedMesh) {\r\n                newSound.attachToMesh(connectedMesh);\r\n            }\r\n        }\r\n\r\n        if (parsedSound.metadata) {\r\n            newSound.metadata = parsedSound.metadata;\r\n        }\r\n\r\n        return newSound;\r\n    }\r\n\r\n    private _setOffset(value?: number) {\r\n        if (this._offset === value) {\r\n            return;\r\n        }\r\n        if (this.isPaused) {\r\n            this.stop();\r\n            this.isPaused = false;\r\n        }\r\n        this._offset = value;\r\n    }\r\n\r\n    private _clearTimeoutsAndObservers() {\r\n        if (this._tryToPlayTimeout) {\r\n            clearTimeout(this._tryToPlayTimeout);\r\n            this._tryToPlayTimeout = null;\r\n        }\r\n        if (this._audioUnlockedObserver) {\r\n            AbstractEngine.audioEngine?.onAudioUnlockedObservable.remove(this._audioUnlockedObserver);\r\n            this._audioUnlockedObserver = null;\r\n        }\r\n    }\r\n}\r\n\r\n// Register Class Name\r\nRegisterClass(\"BABYLON.Sound\", Sound);\r\n"]}