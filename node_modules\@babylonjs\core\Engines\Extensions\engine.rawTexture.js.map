{"version": 3, "file": "engine.rawTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.rawTexture.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAyB,MAAM,0CAA0C,CAAC;AAClG,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AA6K7D,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,UACpC,OAAkC,EAClC,IAA+B,EAC/B,MAAc,EACd,OAAgB,EAChB,cAAgC,IAAI,EACpC,OAAe,SAAS,CAAC,yBAAyB,EAClD,gBAAyB,KAAK;IAE9B,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,OAAO;IACX,CAAC;IACD,kEAAkE;IAClE,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IAE/F,sDAAsD;IACtD,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACvD,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC9D,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEzE,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;QAC3B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACpB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACvC,CAAC;IAED,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAQ,IAAI,CAAC,OAAO,EAAE,CAAC,IAAK,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAY,IAAI,CAAC,CAAC;IACrJ,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,EAAE,kBAAkB,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;IACzI,CAAC;IAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACrD,6BAA6B;IAC7B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,UACpC,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,OAAe,SAAS,CAAC,yBAAyB;AAClD,6DAA6D;AAC7D,aAAa,GAAG,CAAC,EACjB,aAAa,GAAG,KAAK;IAErB,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,oCAA4B,CAAC;IACrE,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;IAC5B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACpC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IACnC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC,eAAe,CAAC,CAAC;IAEjF,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;IACjG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAE9D,UAAU;IACV,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;IAE3E,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACtF,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAEtF,IAAI,eAAe,EAAE,CAAC;QAClB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAErD,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,UACxC,IAAiC,EACjC,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI;IAEpC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,wCAAgC,CAAC;IACzE,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAChC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACpC,CAAC;IAED,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAErD,IAAI,cAAc,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;QAC5B,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED,uGAAuG;IACvG,IAAI,WAAW,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC;QACtE,eAAe,GAAG,KAAK,CAAC;QACxB,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC,mJAAmJ,CAAC,CAAC;IACrK,CAAC;SAAM,IAAI,WAAW,KAAK,IAAI,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC;QAChG,eAAe,GAAG,KAAK,CAAC;QACxB,YAAY,GAAG,SAAS,CAAC,4BAA4B,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC,wJAAwJ,CAAC,CAAC;IAC1K,CAAC;SAAM,IAAI,WAAW,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;QACpE,eAAe,GAAG,KAAK,CAAC;QACxB,MAAM,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAC;IACjG,CAAC;SAAM,IAAI,WAAW,KAAK,EAAE,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;QACvE,eAAe,GAAG,KAAK,CAAC;QACxB,MAAM,CAAC,IAAI,CAAC,oFAAoF,CAAC,CAAC;IACtG,CAAC;IAED,MAAM,KAAK,GAAG,IAAI,CAAC;IACnB,MAAM,MAAM,GAAG,KAAK,CAAC;IAErB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IAEnC,wCAAwC;IACxC,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3G,IAAI,CAAC,KAAK,EAAE,CAAC;QACT,eAAe,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED,gEAAgE;IAChE,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IACjF,CAAC;SAAM,CAAC;QACJ,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,CAAC;QACxE,MAAM,KAAK,GAAG,CAAC,CAAC;QAEhB,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAE9D,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;YACjD,IAAI,WAAW,EAAE,CAAC;gBACd,EAAE,CAAC,oBAAoB,CACnB,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAC1C,KAAK,EACC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAK,CAAC,WAAW,CAAC,EACvC,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,EACd,CAAC,EACD,SAAgB,CACnB,CAAC;YACN,CAAC;iBAAM,CAAC;gBACJ,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;YAC9J,CAAC;QACL,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC/D,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAEpE,UAAU;IACV,IAAI,IAAI,IAAI,eAAe,EAAE,CAAC;QAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;IAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAE1E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,aAAa,CAAC,CAAC;IAC3E,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAErD,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;IAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;IACpC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IAEvB,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG,UACxC,OAAwB,EACxB,IAAuB,EACvB,MAAc,EACd,IAAY,EACZ,OAAgB,EAChB,cAAgC,IAAI,EACpC,QAAgB,CAAC;IAEjB,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAChC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IACxB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IACpB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;IAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;IAEnC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;IACrD,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,CAAC;IAExE,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,cAAc,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;QAC5B,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;QACzB,cAAc,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC9D,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEzE,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,4CAA4C;IAC5C,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;QACjD,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAE/B,IAAI,WAAW,EAAE,CAAC;YACd,EAAE,CAAC,oBAAoB,CACnB,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAC1C,KAAK,EACC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAK,CAAC,WAAW,CAAC,EACvC,OAAO,CAAC,KAAK,EACb,OAAO,CAAC,MAAM,EACd,CAAC,EACS,QAAQ,CACrB,CAAC;QACN,CAAC;aAAM,CAAC;YACJ,IAAI,cAAc,EAAE,CAAC;gBACjB,QAAQ,GAAG,2BAA2B,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC1F,CAAC;YACD,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAClK,CAAC;IACL,CAAC;IAED,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3G,IAAI,KAAK,IAAI,OAAO,CAAC,eAAe,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;QAClD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACvD,CAAC;IACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAE3D,4BAA4B;IAC5B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAC/C,GAAW,EACX,KAAsB,EACtB,IAAY,EACZ,MAAc,EACd,IAAY,EACZ,QAAiB,EACjB,QAAmE,EACnE,eAA4E,EAC5E,SAA+B,IAAI,EACnC,UAAiE,IAAI,EACrE,eAAuB,SAAS,CAAC,8BAA8B,EAC/D,UAAmB,KAAK;IAExB,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACpB,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;IAC5G,KAAK,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;IAC/B,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;IAClB,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;IACxB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1C,MAAM,OAAO,GAAG,CAAC,OAAqB,EAAE,SAAe,EAAE,EAAE;QACvD,KAAK,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;YACrB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAClE,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,IAAS,EAAE,EAAE;QACnC,mCAAmC;QACnC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC5B,OAAO;QACX,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEtC,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YAClB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACrD,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,CAAC;YAExE,IAAI,cAAc,GAAG,KAAK,CAAC;YAC3B,IAAI,cAAc,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;gBAC5B,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;gBACzB,cAAc,GAAG,IAAI,CAAC;YAC1B,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAC9D,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAEzB,MAAM,OAAO,GAAG,eAAe,CAAC,cAAc,CAAC,CAAC;YAChD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;gBAClD,MAAM,OAAO,GAAG,KAAK,IAAI,KAAK,CAAC;gBAE/B,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;oBACjD,IAAI,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC;oBAC5C,IAAI,cAAc,EAAE,CAAC;wBACjB,WAAW,GAAG,2BAA2B,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;oBACnF,CAAC;oBACD,EAAE,CAAC,UAAU,CAAC,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;gBACvH,CAAC;YACL,CAAC;YAED,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QACvB,4BAA4B;QAC5B,KAAK,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAElC,OAAO,CAAC,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACpD,OAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAEnC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;QACb,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,CAAC,SAAS,CACV,GAAG,EACH,CAAC,IAAI,EAAE,EAAE;QACL,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC,EACD,SAAS,EACT,KAAK,EAAE,eAAe,EACtB,IAAI,EACJ,OAAO,CACV,CAAC;IAEF,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF;;GAEG;AACH,SAAS,2BAA2B,CAAC,OAAY,EAAE,KAAa,EAAE,MAAc,EAAE,WAAmB;IACjG,kCAAkC;IAClC,IAAI,QAAa,CAAC;IAClB,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,WAAW,KAAK,SAAS,CAAC,iBAAiB,EAAE,CAAC;QAC9C,QAAQ,GAAG,IAAI,YAAY,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IACpD,CAAC;SAAM,IAAI,WAAW,KAAK,SAAS,CAAC,sBAAsB,EAAE,CAAC;QAC1D,QAAQ,GAAG,IAAI,WAAW,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;QAC/C,IAAI,GAAG,KAAK,CAAC,CAAC,2CAA2C;IAC7D,CAAC;SAAM,IAAI,WAAW,KAAK,SAAS,CAAC,4BAA4B,EAAE,CAAC;QAChE,QAAQ,GAAG,IAAI,WAAW,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IACnD,CAAC;SAAM,CAAC;QACJ,QAAQ,GAAG,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,sBAAsB;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAClC,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAErC,8BAA8B;YAC9B,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAC5C,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAC5C,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAE5C,kCAAkC;YAClC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QAClC,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;;;GAIG;AACH,gEAAgE;AAChE,SAAS,4BAA4B,CAAC,IAAa;IAC/C,OAAO,UAEH,IAA+B,EAC/B,KAAa,EACb,MAAc,EACd,KAAa,EACb,MAAc,EACd,eAAwB,EACxB,OAAgB,EAChB,YAAoB,EACpB,cAAgC,IAAI,EACpC,cAAsB,SAAS,CAAC,yBAAyB;QAEzD,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACtE,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,sCAA6B,CAAC,0CAAiC,CAAC;QACrF,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClD,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;QAC5B,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;QAC3B,OAAO,CAAC,eAAe,GAAG,eAAe,CAAC;QAC1C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACpC,IAAI,IAAI,EAAE,CAAC;YACP,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;QACxB,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;QAC/B,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QACtF,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;QAC3F,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAEjD,UAAU;QACV,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;QAE3E,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzE,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAEzE,IAAI,eAAe,EAAE,CAAC;YAClB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAExC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,OAAO,OAAO,CAAC;IACnB,CAAC,CAAC;AACN,CAAC;AAED,UAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,4BAA4B,CAAC,KAAK,CAAC,CAAC;AACnF,UAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAC;AAE7E;;;;GAIG;AACH,SAAS,4BAA4B,CAAC,IAAa;IAC/C,OAAO,UAEH,OAAwB,EACxB,IAA+B,EAC/B,MAAc,EACd,OAAgB,EAChB,cAAgC,IAAI,EACpC,cAAsB,SAAS,CAAC,yBAAyB;QAEzD,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACtE,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC5D,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,kBAAkB,GAAG,IAAI,CAAC,iCAAiC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAEvF,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAEzE,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAChC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YACxB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;YAC1B,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC;QACvC,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACtB,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,EAAQ,IAAI,CAAC,OAAO,EAAE,CAAC,IAAK,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAC7I,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,kBAAkB,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,cAAc,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;QAC5I,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACxC,4BAA4B;QAC5B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IAC3B,CAAC,CAAC;AACN,CAAC;AAED,UAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,4BAA4B,CAAC,KAAK,CAAC,CAAC;AACnF,UAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAC", "sourcesContent": ["import type { Nullable } from \"../../types\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../Materials/Textures/internalTexture\";\r\nimport { Logger } from \"../../Misc/logger\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Constants } from \"../constants\";\r\nimport { ThinEngine } from \"../thinEngine\";\r\nimport type { IWebRequest } from \"../../Misc/interfaces/iWebRequest\";\r\nimport { IsExponentOfTwo } from \"../../Misc/tools.functions\";\r\n\r\ndeclare module \"../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Update a raw texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store in the texture\r\n         * @param format defines the format of the data\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawTexture(texture: Nullable<InternalTexture>, data: Nullable<ArrayBufferView>, format: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store in the texture\r\n         * @param format defines the format of the data\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the compression used (null by default)\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param useSRGBBuffer defines if the texture must be loaded in a sRGB GPU buffer (if supported by the GPU).\r\n         */\r\n        updateRawTexture(\r\n            texture: Nullable<InternalTexture>,\r\n            data: Nullable<ArrayBufferView>,\r\n            format: number,\r\n            invertY: boolean,\r\n            compression: Nullable<string>,\r\n            type: number,\r\n            useSRGBBuffer: boolean\r\n        ): void;\r\n        /**\r\n         * Update a raw cube texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawCubeTexture(texture: InternalTexture, data: ArrayBufferView[], format: number, type: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw cube texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the compression used (null by default)\r\n         */\r\n        updateRawCubeTexture(texture: InternalTexture, data: ArrayBufferView[], format: number, type: number, invertY: boolean, compression: Nullable<string>): void;\r\n\r\n        /**\r\n         * Update a raw cube texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param type defines the type fo the data (Engine.TEXTURETYPE_UNSIGNED_BYTE by default)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the compression used (null by default)\r\n         * @param level defines which level of the texture to update\r\n         */\r\n        updateRawCubeTexture(texture: InternalTexture, data: ArrayBufferView[], format: number, type: number, invertY: boolean, compression: Nullable<string>, level: number): void;\r\n\r\n        /**\r\n         * Creates a new raw cube texture from a specified url\r\n         * @param url defines the url where the data is located\r\n         * @param scene defines the current scene\r\n         * @param size defines the size of the textures\r\n         * @param format defines the format of the data\r\n         * @param type defines the type fo the data (like Engine.TEXTURETYPE_UNSIGNED_BYTE)\r\n         * @param noMipmap defines if the engine should avoid generating the mip levels\r\n         * @param callback defines a callback used to extract texture data from loaded data\r\n         * @param mipmapGenerator defines to provide an optional tool to generate mip levels\r\n         * @param onLoad defines a callback called when texture is loaded\r\n         * @param onError defines a callback called if there is an error\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createRawCubeTextureFromUrl(\r\n            url: string,\r\n            scene: Nullable<Scene>,\r\n            size: number,\r\n            format: number,\r\n            type: number,\r\n            noMipmap: boolean,\r\n            callback: (ArrayBuffer: ArrayBuffer) => Nullable<ArrayBufferView[]>,\r\n            mipmapGenerator: Nullable<(faces: ArrayBufferView[]) => ArrayBufferView[][]>,\r\n            onLoad: Nullable<() => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Creates a new raw cube texture from a specified url\r\n         * @param url defines the url where the data is located\r\n         * @param scene defines the current scene\r\n         * @param size defines the size of the textures\r\n         * @param format defines the format of the data\r\n         * @param type defines the type fo the data (like Engine.TEXTURETYPE_UNSIGNED_BYTE)\r\n         * @param noMipmap defines if the engine should avoid generating the mip levels\r\n         * @param callback defines a callback used to extract texture data from loaded data\r\n         * @param mipmapGenerator defines to provide an optional tool to generate mip levels\r\n         * @param onLoad defines a callback called when texture is loaded\r\n         * @param onError defines a callback called if there is an error\r\n         * @param samplingMode defines the required sampling mode (like Texture.NEAREST_SAMPLINGMODE)\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @returns the cube texture as an InternalTexture\r\n         */\r\n        createRawCubeTextureFromUrl(\r\n            url: string,\r\n            scene: Nullable<Scene>,\r\n            size: number,\r\n            format: number,\r\n            type: number,\r\n            noMipmap: boolean,\r\n            callback: (ArrayBuffer: ArrayBuffer) => Nullable<ArrayBufferView[]>,\r\n            mipmapGenerator: Nullable<(faces: ArrayBufferView[]) => ArrayBufferView[][]>,\r\n            onLoad: Nullable<() => void>,\r\n            onError: Nullable<(message?: string, exception?: any) => void>,\r\n            samplingMode: number,\r\n            invertY: boolean\r\n        ): InternalTexture;\r\n\r\n        /**\r\n         * Update a raw 3D texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawTexture3D(texture: InternalTexture, data: Nullable<ArrayBufferView>, format: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw 3D texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the used compression (can be null)\r\n         * @param textureType defines the texture Type (Engine.TEXTURETYPE_UNSIGNED_BYTE, Engine.TEXTURETYPE_FLOAT...)\r\n         */\r\n        updateRawTexture3D(texture: InternalTexture, data: Nullable<ArrayBufferView>, format: number, invertY: boolean, compression: Nullable<string>, textureType: number): void;\r\n\r\n        /**\r\n         * Update a raw 2D array texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateRawTexture2DArray(texture: InternalTexture, data: Nullable<ArrayBufferView>, format: number, invertY: boolean): void;\r\n\r\n        /**\r\n         * Update a raw 2D array texture\r\n         * @param texture defines the texture to update\r\n         * @param data defines the data to store\r\n         * @param format defines the data format\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         * @param compression defines the used compression (can be null)\r\n         * @param textureType defines the texture Type (Engine.TEXTURETYPE_UNSIGNED_BYTE, Engine.TEXTURETYPE_FLOAT...)\r\n         */\r\n        updateRawTexture2DArray(\r\n            texture: InternalTexture,\r\n            data: Nullable<ArrayBufferView>,\r\n            format: number,\r\n            invertY: boolean,\r\n            compression: Nullable<string>,\r\n            textureType: number\r\n        ): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.updateRawTexture = function (\r\n    texture: Nullable<InternalTexture>,\r\n    data: Nullable<ArrayBufferView>,\r\n    format: number,\r\n    invertY: boolean,\r\n    compression: Nullable<string> = null,\r\n    type: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n    useSRGBBuffer: boolean = false\r\n): void {\r\n    if (!texture) {\r\n        return;\r\n    }\r\n    // Babylon's internalSizedFomat but gl's texImage2D internalFormat\r\n    const internalSizedFomat = this._getRGBABufferInternalSizedFormat(type, format, useSRGBBuffer);\r\n\r\n    // Babylon's internalFormat but gl's texImage2D format\r\n    const internalFormat = this._getInternalFormat(format);\r\n    const textureType = this._getWebGLTextureType(type);\r\n    this._bindTextureDirectly(this._gl.TEXTURE_2D, texture, true);\r\n    this._unpackFlipY(invertY === undefined ? true : invertY ? true : false);\r\n\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = data;\r\n        texture.format = format;\r\n        texture.type = type;\r\n        texture.invertY = invertY;\r\n        texture._compression = compression;\r\n    }\r\n\r\n    if (texture.width % 4 !== 0) {\r\n        this._gl.pixelStorei(this._gl.UNPACK_ALIGNMENT, 1);\r\n    }\r\n\r\n    if (compression && data) {\r\n        this._gl.compressedTexImage2D(this._gl.TEXTURE_2D, 0, (<any>this.getCaps().s3tc)[compression], texture.width, texture.height, 0, <DataView>data);\r\n    } else {\r\n        this._gl.texImage2D(this._gl.TEXTURE_2D, 0, internalSizedFomat, texture.width, texture.height, 0, internalFormat, textureType, data);\r\n    }\r\n\r\n    if (texture.generateMipMaps) {\r\n        this._gl.generateMipmap(this._gl.TEXTURE_2D);\r\n    }\r\n    this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n    //  this.resetTextureCache();\r\n    texture.isReady = true;\r\n};\r\n\r\nThinEngine.prototype.createRawTexture = function (\r\n    data: Nullable<ArrayBufferView>,\r\n    width: number,\r\n    height: number,\r\n    format: number,\r\n    generateMipMaps: boolean,\r\n    invertY: boolean,\r\n    samplingMode: number,\r\n    compression: Nullable<string> = null,\r\n    type: number = Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    creationFlags = 0,\r\n    useSRGBBuffer = false\r\n): InternalTexture {\r\n    const texture = new InternalTexture(this, InternalTextureSource.Raw);\r\n    texture.baseWidth = width;\r\n    texture.baseHeight = height;\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.format = format;\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n    texture.invertY = invertY;\r\n    texture._compression = compression;\r\n    texture.type = type;\r\n    texture._useSRGBBuffer = this._getUseSRGBBuffer(useSRGBBuffer, !generateMipMaps);\r\n\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferView = data;\r\n    }\r\n\r\n    this.updateRawTexture(texture, data, format, invertY, compression, type, texture._useSRGBBuffer);\r\n    this._bindTextureDirectly(this._gl.TEXTURE_2D, texture, true);\r\n\r\n    // Filters\r\n    const filters = this._getSamplingParameters(samplingMode, generateMipMaps);\r\n\r\n    this._gl.texParameteri(this._gl.TEXTURE_2D, this._gl.TEXTURE_MAG_FILTER, filters.mag);\r\n    this._gl.texParameteri(this._gl.TEXTURE_2D, this._gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n    if (generateMipMaps) {\r\n        this._gl.generateMipmap(this._gl.TEXTURE_2D);\r\n    }\r\n\r\n    this._bindTextureDirectly(this._gl.TEXTURE_2D, null);\r\n\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    return texture;\r\n};\r\n\r\nThinEngine.prototype.createRawCubeTexture = function (\r\n    data: Nullable<ArrayBufferView[]>,\r\n    size: number,\r\n    format: number,\r\n    type: number,\r\n    generateMipMaps: boolean,\r\n    invertY: boolean,\r\n    samplingMode: number,\r\n    compression: Nullable<string> = null\r\n): InternalTexture {\r\n    const gl = this._gl;\r\n    const texture = new InternalTexture(this, InternalTextureSource.CubeRaw);\r\n    texture.isCube = true;\r\n    texture.format = format;\r\n    texture.type = type;\r\n    if (!this._doNotHandleContextLost) {\r\n        texture._bufferViewArray = data;\r\n    }\r\n\r\n    const textureType = this._getWebGLTextureType(type);\r\n    let internalFormat = this._getInternalFormat(format);\r\n\r\n    if (internalFormat === gl.RGB) {\r\n        internalFormat = gl.RGBA;\r\n    }\r\n\r\n    // Mipmap generation needs a sized internal format that is both color-renderable and texture-filterable\r\n    if (textureType === gl.FLOAT && !this._caps.textureFloatLinearFiltering) {\r\n        generateMipMaps = false;\r\n        samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        Logger.Warn(\"Float texture filtering is not supported. Mipmap generation and sampling mode are forced to false and TEXTURE_NEAREST_SAMPLINGMODE, respectively.\");\r\n    } else if (textureType === this._gl.HALF_FLOAT_OES && !this._caps.textureHalfFloatLinearFiltering) {\r\n        generateMipMaps = false;\r\n        samplingMode = Constants.TEXTURE_NEAREST_SAMPLINGMODE;\r\n        Logger.Warn(\"Half float texture filtering is not supported. Mipmap generation and sampling mode are forced to false and TEXTURE_NEAREST_SAMPLINGMODE, respectively.\");\r\n    } else if (textureType === gl.FLOAT && !this._caps.textureFloatRender) {\r\n        generateMipMaps = false;\r\n        Logger.Warn(\"Render to float textures is not supported. Mipmap generation forced to false.\");\r\n    } else if (textureType === gl.HALF_FLOAT && !this._caps.colorBufferFloat) {\r\n        generateMipMaps = false;\r\n        Logger.Warn(\"Render to half float textures is not supported. Mipmap generation forced to false.\");\r\n    }\r\n\r\n    const width = size;\r\n    const height = width;\r\n\r\n    texture.width = width;\r\n    texture.height = height;\r\n    texture.invertY = invertY;\r\n    texture._compression = compression;\r\n\r\n    // Double check on POT to generate Mips.\r\n    const isPot = !this.needPOTTextures || (IsExponentOfTwo(texture.width) && IsExponentOfTwo(texture.height));\r\n    if (!isPot) {\r\n        generateMipMaps = false;\r\n    }\r\n\r\n    // Upload data if needed. The texture won't be ready until then.\r\n    if (data) {\r\n        this.updateRawCubeTexture(texture, data, format, type, invertY, compression);\r\n    } else {\r\n        const internalSizedFomat = this._getRGBABufferInternalSizedFormat(type);\r\n        const level = 0;\r\n\r\n        this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n\r\n        for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\r\n            if (compression) {\r\n                gl.compressedTexImage2D(\r\n                    gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex,\r\n                    level,\r\n                    (<any>this.getCaps().s3tc)[compression],\r\n                    texture.width,\r\n                    texture.height,\r\n                    0,\r\n                    undefined as any\r\n                );\r\n            } else {\r\n                gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex, level, internalSizedFomat, texture.width, texture.height, 0, internalFormat, textureType, null);\r\n            }\r\n        }\r\n\r\n        this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, null);\r\n    }\r\n\r\n    this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, texture, true);\r\n\r\n    // Filters\r\n    if (data && generateMipMaps) {\r\n        this._gl.generateMipmap(this._gl.TEXTURE_CUBE_MAP);\r\n    }\r\n\r\n    const filters = this._getSamplingParameters(samplingMode, generateMipMaps);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MAG_FILTER, filters.mag);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\r\n    gl.texParameteri(gl.TEXTURE_CUBE_MAP, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n\r\n    texture.generateMipMaps = generateMipMaps;\r\n    texture.samplingMode = samplingMode;\r\n    texture.isReady = true;\r\n\r\n    return texture;\r\n};\r\n\r\nThinEngine.prototype.updateRawCubeTexture = function (\r\n    texture: InternalTexture,\r\n    data: ArrayBufferView[],\r\n    format: number,\r\n    type: number,\r\n    invertY: boolean,\r\n    compression: Nullable<string> = null,\r\n    level: number = 0\r\n): void {\r\n    texture._bufferViewArray = data;\r\n    texture.format = format;\r\n    texture.type = type;\r\n    texture.invertY = invertY;\r\n    texture._compression = compression;\r\n\r\n    const gl = this._gl;\r\n    const textureType = this._getWebGLTextureType(type);\r\n    let internalFormat = this._getInternalFormat(format);\r\n    const internalSizedFomat = this._getRGBABufferInternalSizedFormat(type);\r\n\r\n    let needConversion = false;\r\n    if (internalFormat === gl.RGB) {\r\n        internalFormat = gl.RGBA;\r\n        needConversion = true;\r\n    }\r\n\r\n    this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n    this._unpackFlipY(invertY === undefined ? true : invertY ? true : false);\r\n\r\n    if (texture.width % 4 !== 0) {\r\n        gl.pixelStorei(gl.UNPACK_ALIGNMENT, 1);\r\n    }\r\n\r\n    // Data are known to be in +X +Y +Z -X -Y -Z\r\n    for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\r\n        let faceData = data[faceIndex];\r\n\r\n        if (compression) {\r\n            gl.compressedTexImage2D(\r\n                gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex,\r\n                level,\r\n                (<any>this.getCaps().s3tc)[compression],\r\n                texture.width,\r\n                texture.height,\r\n                0,\r\n                <DataView>faceData\r\n            );\r\n        } else {\r\n            if (needConversion) {\r\n                faceData = ConvertRGBtoRGBATextureData(faceData, texture.width, texture.height, type);\r\n            }\r\n            gl.texImage2D(gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndex, level, internalSizedFomat, texture.width, texture.height, 0, internalFormat, textureType, faceData);\r\n        }\r\n    }\r\n\r\n    const isPot = !this.needPOTTextures || (IsExponentOfTwo(texture.width) && IsExponentOfTwo(texture.height));\r\n    if (isPot && texture.generateMipMaps && level === 0) {\r\n        this._gl.generateMipmap(this._gl.TEXTURE_CUBE_MAP);\r\n    }\r\n    this._bindTextureDirectly(this._gl.TEXTURE_CUBE_MAP, null);\r\n\r\n    // this.resetTextureCache();\r\n    texture.isReady = true;\r\n};\r\n\r\nThinEngine.prototype.createRawCubeTextureFromUrl = function (\r\n    url: string,\r\n    scene: Nullable<Scene>,\r\n    size: number,\r\n    format: number,\r\n    type: number,\r\n    noMipmap: boolean,\r\n    callback: (ArrayBuffer: ArrayBuffer) => Nullable<ArrayBufferView[]>,\r\n    mipmapGenerator: Nullable<(faces: ArrayBufferView[]) => ArrayBufferView[][]>,\r\n    onLoad: Nullable<() => void> = null,\r\n    onError: Nullable<(message?: string, exception?: any) => void> = null,\r\n    samplingMode: number = Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n    invertY: boolean = false\r\n): InternalTexture {\r\n    const gl = this._gl;\r\n    const texture = this.createRawCubeTexture(null, size, format, type, !noMipmap, invertY, samplingMode, null);\r\n    scene?.addPendingData(texture);\r\n    texture.url = url;\r\n    texture.isReady = false;\r\n    this._internalTexturesCache.push(texture);\r\n\r\n    const onerror = (request?: IWebRequest, exception?: any) => {\r\n        scene?.removePendingData(texture);\r\n        if (onError && request) {\r\n            onError(request.status + \" \" + request.statusText, exception);\r\n        }\r\n    };\r\n\r\n    const internalCallback = (data: any) => {\r\n        // If the texture has been disposed\r\n        if (!texture._hardwareTexture) {\r\n            return;\r\n        }\r\n\r\n        const width = texture.width;\r\n        const faceDataArrays = callback(data);\r\n\r\n        if (!faceDataArrays) {\r\n            return;\r\n        }\r\n\r\n        if (mipmapGenerator) {\r\n            const textureType = this._getWebGLTextureType(type);\r\n            let internalFormat = this._getInternalFormat(format);\r\n            const internalSizedFomat = this._getRGBABufferInternalSizedFormat(type);\r\n\r\n            let needConversion = false;\r\n            if (internalFormat === gl.RGB) {\r\n                internalFormat = gl.RGBA;\r\n                needConversion = true;\r\n            }\r\n\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, texture, true);\r\n            this._unpackFlipY(false);\r\n\r\n            const mipData = mipmapGenerator(faceDataArrays);\r\n            for (let level = 0; level < mipData.length; level++) {\r\n                const mipSize = width >> level;\r\n\r\n                for (let faceIndex = 0; faceIndex < 6; faceIndex++) {\r\n                    let mipFaceData = mipData[level][faceIndex];\r\n                    if (needConversion) {\r\n                        mipFaceData = ConvertRGBtoRGBATextureData(mipFaceData, mipSize, mipSize, type);\r\n                    }\r\n                    gl.texImage2D(faceIndex, level, internalSizedFomat, mipSize, mipSize, 0, internalFormat, textureType, mipFaceData);\r\n                }\r\n            }\r\n\r\n            this._bindTextureDirectly(gl.TEXTURE_CUBE_MAP, null);\r\n        } else {\r\n            this.updateRawCubeTexture(texture, faceDataArrays, format, type, invertY);\r\n        }\r\n\r\n        texture.isReady = true;\r\n        // this.resetTextureCache();\r\n        scene?.removePendingData(texture);\r\n\r\n        texture.onLoadedObservable.notifyObservers(texture);\r\n        texture.onLoadedObservable.clear();\r\n\r\n        if (onLoad) {\r\n            onLoad();\r\n        }\r\n    };\r\n\r\n    this._loadFile(\r\n        url,\r\n        (data) => {\r\n            internalCallback(data);\r\n        },\r\n        undefined,\r\n        scene?.offlineProvider,\r\n        true,\r\n        onerror\r\n    );\r\n\r\n    return texture;\r\n};\r\n\r\n/**\r\n * @internal\r\n */\r\nfunction ConvertRGBtoRGBATextureData(rgbData: any, width: number, height: number, textureType: number): ArrayBufferView {\r\n    // Create new RGBA data container.\r\n    let rgbaData: any;\r\n    let val1 = 1;\r\n    if (textureType === Constants.TEXTURETYPE_FLOAT) {\r\n        rgbaData = new Float32Array(width * height * 4);\r\n    } else if (textureType === Constants.TEXTURETYPE_HALF_FLOAT) {\r\n        rgbaData = new Uint16Array(width * height * 4);\r\n        val1 = 15360; // 15360 is the encoding of 1 in half float\r\n    } else if (textureType === Constants.TEXTURETYPE_UNSIGNED_INTEGER) {\r\n        rgbaData = new Uint32Array(width * height * 4);\r\n    } else {\r\n        rgbaData = new Uint8Array(width * height * 4);\r\n    }\r\n\r\n    // Convert each pixel.\r\n    for (let x = 0; x < width; x++) {\r\n        for (let y = 0; y < height; y++) {\r\n            const index = (y * width + x) * 3;\r\n            const newIndex = (y * width + x) * 4;\r\n\r\n            // Map Old Value to new value.\r\n            rgbaData[newIndex + 0] = rgbData[index + 0];\r\n            rgbaData[newIndex + 1] = rgbData[index + 1];\r\n            rgbaData[newIndex + 2] = rgbData[index + 2];\r\n\r\n            // Add fully opaque alpha channel.\r\n            rgbaData[newIndex + 3] = val1;\r\n        }\r\n    }\r\n\r\n    return rgbaData;\r\n}\r\n\r\n/**\r\n * Create a function for createRawTexture3D/createRawTexture2DArray\r\n * @param is3D true for TEXTURE_3D and false for TEXTURE_2D_ARRAY\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nfunction MakeCreateRawTextureFunction(is3D: boolean) {\r\n    return function (\r\n        this: ThinEngine,\r\n        data: Nullable<ArrayBufferView>,\r\n        width: number,\r\n        height: number,\r\n        depth: number,\r\n        format: number,\r\n        generateMipMaps: boolean,\r\n        invertY: boolean,\r\n        samplingMode: number,\r\n        compression: Nullable<string> = null,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE\r\n    ): InternalTexture {\r\n        const target = is3D ? this._gl.TEXTURE_3D : this._gl.TEXTURE_2D_ARRAY;\r\n        const source = is3D ? InternalTextureSource.Raw3D : InternalTextureSource.Raw2DArray;\r\n        const texture = new InternalTexture(this, source);\r\n        texture.baseWidth = width;\r\n        texture.baseHeight = height;\r\n        texture.baseDepth = depth;\r\n        texture.width = width;\r\n        texture.height = height;\r\n        texture.depth = depth;\r\n        texture.format = format;\r\n        texture.type = textureType;\r\n        texture.generateMipMaps = generateMipMaps;\r\n        texture.samplingMode = samplingMode;\r\n        if (is3D) {\r\n            texture.is3D = true;\r\n        } else {\r\n            texture.is2DArray = true;\r\n        }\r\n\r\n        if (!this._doNotHandleContextLost) {\r\n            texture._bufferView = data;\r\n        }\r\n\r\n        if (is3D) {\r\n            this.updateRawTexture3D(texture, data, format, invertY, compression, textureType);\r\n        } else {\r\n            this.updateRawTexture2DArray(texture, data, format, invertY, compression, textureType);\r\n        }\r\n        this._bindTextureDirectly(target, texture, true);\r\n\r\n        // Filters\r\n        const filters = this._getSamplingParameters(samplingMode, generateMipMaps);\r\n\r\n        this._gl.texParameteri(target, this._gl.TEXTURE_MAG_FILTER, filters.mag);\r\n        this._gl.texParameteri(target, this._gl.TEXTURE_MIN_FILTER, filters.min);\r\n\r\n        if (generateMipMaps) {\r\n            this._gl.generateMipmap(target);\r\n        }\r\n\r\n        this._bindTextureDirectly(target, null);\r\n\r\n        this._internalTexturesCache.push(texture);\r\n\r\n        return texture;\r\n    };\r\n}\r\n\r\nThinEngine.prototype.createRawTexture2DArray = MakeCreateRawTextureFunction(false);\r\nThinEngine.prototype.createRawTexture3D = MakeCreateRawTextureFunction(true);\r\n\r\n/**\r\n * Create a function for updateRawTexture3D/updateRawTexture2DArray\r\n * @param is3D true for TEXTURE_3D and false for TEXTURE_2D_ARRAY\r\n * @internal\r\n */\r\nfunction MakeUpdateRawTextureFunction(is3D: boolean) {\r\n    return function (\r\n        this: ThinEngine,\r\n        texture: InternalTexture,\r\n        data: Nullable<ArrayBufferView>,\r\n        format: number,\r\n        invertY: boolean,\r\n        compression: Nullable<string> = null,\r\n        textureType: number = Constants.TEXTURETYPE_UNSIGNED_BYTE\r\n    ): void {\r\n        const target = is3D ? this._gl.TEXTURE_3D : this._gl.TEXTURE_2D_ARRAY;\r\n        const internalType = this._getWebGLTextureType(textureType);\r\n        const internalFormat = this._getInternalFormat(format);\r\n        const internalSizedFomat = this._getRGBABufferInternalSizedFormat(textureType, format);\r\n\r\n        this._bindTextureDirectly(target, texture, true);\r\n        this._unpackFlipY(invertY === undefined ? true : invertY ? true : false);\r\n\r\n        if (!this._doNotHandleContextLost) {\r\n            texture._bufferView = data;\r\n            texture.format = format;\r\n            texture.invertY = invertY;\r\n            texture._compression = compression;\r\n        }\r\n\r\n        if (texture.width % 4 !== 0) {\r\n            this._gl.pixelStorei(this._gl.UNPACK_ALIGNMENT, 1);\r\n        }\r\n\r\n        if (compression && data) {\r\n            this._gl.compressedTexImage3D(target, 0, (<any>this.getCaps().s3tc)[compression], texture.width, texture.height, texture.depth, 0, data);\r\n        } else {\r\n            this._gl.texImage3D(target, 0, internalSizedFomat, texture.width, texture.height, texture.depth, 0, internalFormat, internalType, data);\r\n        }\r\n\r\n        if (texture.generateMipMaps) {\r\n            this._gl.generateMipmap(target);\r\n        }\r\n        this._bindTextureDirectly(target, null);\r\n        // this.resetTextureCache();\r\n        texture.isReady = true;\r\n    };\r\n}\r\n\r\nThinEngine.prototype.updateRawTexture2DArray = MakeUpdateRawTextureFunction(false);\r\nThinEngine.prototype.updateRawTexture3D = MakeUpdateRawTextureFunction(true);\r\n"]}