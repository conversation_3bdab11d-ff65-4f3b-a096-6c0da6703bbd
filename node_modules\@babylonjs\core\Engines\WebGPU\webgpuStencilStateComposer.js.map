{"version": 3, "file": "webgpuStencilStateComposer.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGPU/webgpuStencilStateComposer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AAEzE;;IAEI;AACJ,MAAM,OAAO,0BAA2B,SAAQ,oBAAoB;IAGhE,YAAmB,KAAgC;QAC/C,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,IAAoB,IAAI;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAoB,IAAI,CAAC,KAAa;QAClC,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED,IAAoB,QAAQ;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAoB,QAAQ,CAAC,KAAa;QACtC,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,IAAoB,QAAQ;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED,IAAoB,QAAQ,CAAC,KAAa;QACtC,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,IAAoB,aAAa;QAC7B,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAoB,aAAa,CAAC,KAAa;QAC3C,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,IAAoB,WAAW;QAC3B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED,IAAoB,WAAW,CAAC,KAAa;QACzC,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;YAC9B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,IAAoB,kBAAkB;QAClC,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED,IAAoB,kBAAkB,CAAC,KAAa;QAChD,IAAI,IAAI,CAAC,mBAAmB,KAAK,KAAK,EAAE,CAAC;YACrC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,IAAoB,iBAAiB;QACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED,IAAoB,iBAAiB,CAAC,KAAa;QAC/C,IAAI,IAAI,CAAC,kBAAkB,KAAK,KAAK,EAAE,CAAC;YACpC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,IAAoB,eAAe;QAC/B,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED,IAAoB,eAAe,CAAC,KAAa;QAC7C,IAAI,IAAI,CAAC,gBAAgB,KAAK,KAAK,EAAE,CAAC;YAClC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC;IAED,IAAoB,sBAAsB;QACtC,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED,IAAoB,sBAAsB,CAAC,KAAa;QACpD,IAAI,IAAI,CAAC,uBAAuB,KAAK,KAAK,EAAE,CAAC;YACzC,OAAO;QACX,CAAC;QAED,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC;IAED,IAAoB,IAAI;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,IAAoB,IAAI,CAAC,KAAa;QAClC,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;YACvB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,IAAoB,OAAO;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,IAAoB,OAAO,CAAC,KAAc;QACtC,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAEe,KAAK;QACjB,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC;IACpC,CAAC;IAEe,KAAK;QACjB,MAAM,sBAAsB,GAAG,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC;QAE7D,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACnG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QAC1F,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACnG,IAAI,CAAC,QAAQ,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;QACtG,IAAI,CAAC,aAAa,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;QACrH,IAAI,CAAC,WAAW,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;QAC/G,IAAI,CAAC,kBAAkB,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;QACpI,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;IAC9F,CAAC;CACJ", "sourcesContent": ["import type { WebGPUCacheRenderPipeline } from \"./webgpuCacheRenderPipeline\";\r\nimport { StencilStateComposer } from \"../../States/stencilStateComposer\";\r\n\r\n/**\r\n * @internal\r\n **/\r\nexport class WebGPUStencilStateComposer extends StencilStateComposer {\r\n    private _cache: WebGPUCacheRenderPipeline;\r\n\r\n    public constructor(cache: WebGPUCacheRenderPipeline) {\r\n        super(false);\r\n        this._cache = cache;\r\n        this.reset();\r\n    }\r\n\r\n    public override get func(): number {\r\n        return this._func;\r\n    }\r\n\r\n    public override set func(value: number) {\r\n        if (this._func === value) {\r\n            return;\r\n        }\r\n\r\n        this._func = value;\r\n        this._cache.setStencilCompare(value);\r\n    }\r\n\r\n    public override get backFunc(): number {\r\n        return this._backFunc;\r\n    }\r\n\r\n    public override set backFunc(value: number) {\r\n        if (this._backFunc === value) {\r\n            return;\r\n        }\r\n\r\n        this._backFunc = value;\r\n        this._cache.setStencilBackCompare(value);\r\n    }\r\n\r\n    public override get funcMask(): number {\r\n        return this._funcMask;\r\n    }\r\n\r\n    public override set funcMask(value: number) {\r\n        if (this._funcMask === value) {\r\n            return;\r\n        }\r\n\r\n        this._funcMask = value;\r\n        this._cache.setStencilReadMask(value);\r\n    }\r\n\r\n    public override get opStencilFail(): number {\r\n        return this._opStencilFail;\r\n    }\r\n\r\n    public override set opStencilFail(value: number) {\r\n        if (this._opStencilFail === value) {\r\n            return;\r\n        }\r\n\r\n        this._opStencilFail = value;\r\n        this._cache.setStencilFailOp(value);\r\n    }\r\n\r\n    public override get opDepthFail(): number {\r\n        return this._opDepthFail;\r\n    }\r\n\r\n    public override set opDepthFail(value: number) {\r\n        if (this._opDepthFail === value) {\r\n            return;\r\n        }\r\n\r\n        this._opDepthFail = value;\r\n        this._cache.setStencilDepthFailOp(value);\r\n    }\r\n\r\n    public override get opStencilDepthPass(): number {\r\n        return this._opStencilDepthPass;\r\n    }\r\n\r\n    public override set opStencilDepthPass(value: number) {\r\n        if (this._opStencilDepthPass === value) {\r\n            return;\r\n        }\r\n\r\n        this._opStencilDepthPass = value;\r\n        this._cache.setStencilPassOp(value);\r\n    }\r\n\r\n    public override get backOpStencilFail(): number {\r\n        return this._backOpStencilFail;\r\n    }\r\n\r\n    public override set backOpStencilFail(value: number) {\r\n        if (this._backOpStencilFail === value) {\r\n            return;\r\n        }\r\n\r\n        this._backOpStencilFail = value;\r\n        this._cache.setStencilBackFailOp(value);\r\n    }\r\n\r\n    public override get backOpDepthFail(): number {\r\n        return this._backOpDepthFail;\r\n    }\r\n\r\n    public override set backOpDepthFail(value: number) {\r\n        if (this._backOpDepthFail === value) {\r\n            return;\r\n        }\r\n\r\n        this._backOpDepthFail = value;\r\n        this._cache.setStencilBackDepthFailOp(value);\r\n    }\r\n\r\n    public override get backOpStencilDepthPass(): number {\r\n        return this._backOpStencilDepthPass;\r\n    }\r\n\r\n    public override set backOpStencilDepthPass(value: number) {\r\n        if (this._backOpStencilDepthPass === value) {\r\n            return;\r\n        }\r\n\r\n        this._backOpStencilDepthPass = value;\r\n        this._cache.setStencilBackPassOp(value);\r\n    }\r\n\r\n    public override get mask(): number {\r\n        return this._mask;\r\n    }\r\n\r\n    public override set mask(value: number) {\r\n        if (this._mask === value) {\r\n            return;\r\n        }\r\n\r\n        this._mask = value;\r\n        this._cache.setStencilWriteMask(value);\r\n    }\r\n\r\n    public override get enabled(): boolean {\r\n        return this._enabled;\r\n    }\r\n\r\n    public override set enabled(value: boolean) {\r\n        if (this._enabled === value) {\r\n            return;\r\n        }\r\n\r\n        this._enabled = value;\r\n        this._cache.setStencilEnabled(value);\r\n    }\r\n\r\n    public override reset() {\r\n        super.reset();\r\n        this._cache.resetStencilState();\r\n    }\r\n\r\n    public override apply() {\r\n        const stencilMaterialEnabled = this.stencilMaterial?.enabled;\r\n\r\n        this.enabled = stencilMaterialEnabled ? this.stencilMaterial!.enabled : this.stencilGlobal.enabled;\r\n        if (!this.enabled) {\r\n            return;\r\n        }\r\n\r\n        this.func = stencilMaterialEnabled ? this.stencilMaterial!.func : this.stencilGlobal.func;\r\n        this.funcRef = stencilMaterialEnabled ? this.stencilMaterial!.funcRef : this.stencilGlobal.funcRef;\r\n        this.funcMask = stencilMaterialEnabled ? this.stencilMaterial!.funcMask : this.stencilGlobal.funcMask;\r\n        this.opStencilFail = stencilMaterialEnabled ? this.stencilMaterial!.opStencilFail : this.stencilGlobal.opStencilFail;\r\n        this.opDepthFail = stencilMaterialEnabled ? this.stencilMaterial!.opDepthFail : this.stencilGlobal.opDepthFail;\r\n        this.opStencilDepthPass = stencilMaterialEnabled ? this.stencilMaterial!.opStencilDepthPass : this.stencilGlobal.opStencilDepthPass;\r\n        this.mask = stencilMaterialEnabled ? this.stencilMaterial!.mask : this.stencilGlobal.mask;\r\n    }\r\n}\r\n"]}