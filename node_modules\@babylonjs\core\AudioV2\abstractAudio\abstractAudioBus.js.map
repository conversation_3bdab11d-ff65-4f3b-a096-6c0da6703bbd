{"version": 3, "file": "abstractAudioBus.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/abstractAudio/abstractAudioBus.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAM9D;;;;;GAKG;AACH,MAAM,OAAgB,gBAAiB,SAAQ,oBAAoB;IAC/D,YAAsB,IAAY,EAAE,MAAqB;QACrD,KAAK,CAAC,IAAI,EAAE,MAAM,+CAAuC,CAAC;IAC9D,CAAC;CACJ", "sourcesContent": ["import { AudioNodeType } from \"./abstractAudioNode\";\nimport type { IAbstractAudioOutNodeOptions } from \"./abstractAudioOutNode\";\nimport { AbstractAudioOutNode } from \"./abstractAudioOutNode\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\n\n/** @internal */\nexport interface IAbstractAudioBusOptions extends IAbstractAudioOutNodeOptions {}\n\n/**\n * Abstract class representing an audio bus with volume control.\n *\n * An audio bus is a node in the audio graph that can have multiple inputs and outputs. It is typically used to group\n * sounds together and apply effects to them.\n */\nexport abstract class AbstractAudioBus extends AbstractAudioOutNode {\n    protected constructor(name: string, engine: AudioEngineV2) {\n        super(name, engine, AudioNodeType.HAS_INPUTS_AND_OUTPUTS);\n    }\n}\n"]}