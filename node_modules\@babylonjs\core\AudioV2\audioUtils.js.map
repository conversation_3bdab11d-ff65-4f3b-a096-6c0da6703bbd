{"version": 3, "file": "audioUtils.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/AudioV2/audioUtils.ts"], "names": [], "mappings": "AAGA,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAEtE,MAAM,WAAW,GAAG,GAAG,CAAC;AAExB,MAAM,aAAa,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C,IAAI,cAAc,GAA2B,IAAI,CAAC;AAElD,IAAI,QAAQ,GAA2B,IAAI,CAAC;AAC5C,IAAI,QAAQ,GAA2B,IAAI,CAAC;AAE5C;;GAEG;AACH,SAAS,WAAW;IAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,QAAQ,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;QAEzC,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,GAAG,SAAS,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,IAAI,SAAS,CAAC;QACnB,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,SAAS,WAAW;IAChB,IAAI,CAAC,QAAQ,EAAE,CAAC;QACZ,QAAQ,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;QAEzC,MAAM,SAAS,GAAG,CAAC,GAAG,WAAW,CAAC;QAClC,IAAI,CAAC,GAAG,SAAS,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAC1D,CAAC,IAAI,SAAS,CAAC;QACnB,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,yBAAyB,CAAC,KAA8B,EAAE,IAAY,EAAE,EAAU;IAC9F,IAAI,CAAC,cAAc,EAAE,CAAC;QAClB,cAAc,GAAG,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,eAA6B,CAAC;IAElC,IAAI,KAAK,kDAAmC,EAAE,CAAC;QAC3C,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QACxB,aAAa,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACtB,OAAO,aAAa,CAAC;IACzB,CAAC;SAAM,IAAI,KAAK,4DAAwC,EAAE,CAAC;QACvD,eAAe,GAAG,WAAW,EAAE,CAAC;IACpC,CAAC;SAAM,IAAI,KAAK,4DAAwC,EAAE,CAAC;QACvD,eAAe,GAAG,WAAW,EAAE,CAAC;IACpC,CAAC;SAAM,CAAC;QACJ,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;IACvC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;IAElC,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YACnD,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAED,OAAO,cAAc,CAAC;AAC1B,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,SAAS,CAAC,GAAW;IACjC,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACrC,CAAC", "sourcesContent": ["import type { Nullable } from \"../types\";\nimport { AudioParameterRampShape } from \"./audioParameter\";\n\nexport const _FileExtensionRegex = new RegExp(\"\\\\.(\\\\w{3,4})($|\\\\?)\");\n\nconst CurveLength = 100;\n\nconst TmpLineValues = new Float32Array([0, 0]);\nlet TmpCurveValues: Nullable<Float32Array> = null;\n\nlet ExpCurve: Nullable<Float32Array> = null;\nlet LogCurve: Nullable<Float32Array> = null;\n\n/**\n * @returns A Float32Array representing an exponential ramp from (0, 0) to (1, 1).\n */\nfunction GetExpCurve(): Float32Array {\n    if (!ExpCurve) {\n        ExpCurve = new Float32Array(CurveLength);\n\n        const increment = 1 / (CurveLength - 1);\n        let x = increment;\n        for (let i = 1; i < CurveLength; i++) {\n            ExpCurve[i] = Math.exp(-11.512925464970227 * (1 - x));\n            x += increment;\n        }\n    }\n\n    return ExpCurve;\n}\n\n/**\n * @returns A Float32Array representing a logarithmic ramp from (0, 0) to (1, 1).\n */\nfunction GetLogCurve(): Float32Array {\n    if (!LogCurve) {\n        LogCurve = new Float32Array(CurveLength);\n\n        const increment = 1 / CurveLength;\n        let x = increment;\n        for (let i = 0; i < CurveLength; i++) {\n            LogCurve[i] = 1 + Math.log10(x) / Math.log10(CurveLength);\n            x += increment;\n        }\n    }\n\n    return LogCurve;\n}\n\n/** @internal */\nexport function _GetAudioParamCurveValues(shape: AudioParameterRampShape, from: number, to: number): Float32Array {\n    if (!TmpCurveValues) {\n        TmpCurveValues = new Float32Array(CurveLength);\n    }\n\n    let normalizedCurve: Float32Array;\n\n    if (shape === AudioParameterRampShape.Linear) {\n        TmpLineValues[0] = from;\n        TmpLineValues[1] = to;\n        return TmpLineValues;\n    } else if (shape === AudioParameterRampShape.Exponential) {\n        normalizedCurve = GetExpCurve();\n    } else if (shape === AudioParameterRampShape.Logarithmic) {\n        normalizedCurve = GetLogCurve();\n    } else {\n        throw new Error(`Unknown ramp shape: ${shape}`);\n    }\n\n    const direction = Math.sign(to - from);\n    const range = Math.abs(to - from);\n\n    if (direction === 1) {\n        for (let i = 0; i < normalizedCurve.length; i++) {\n            TmpCurveValues[i] = from + range * normalizedCurve[i];\n        }\n    } else {\n        let j = CurveLength - 1;\n        for (let i = 0; i < normalizedCurve.length; i++, j--) {\n            TmpCurveValues[i] = from - range * (1 - normalizedCurve[j]);\n        }\n    }\n\n    return TmpCurveValues;\n}\n\n/** @internal */\nexport function _CleanUrl(url: string) {\n    return url.replace(/#/gm, \"%23\");\n}\n"]}