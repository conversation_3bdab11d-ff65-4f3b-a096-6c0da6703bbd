{"version": 3, "file": "iShaderProcessor.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Processors/iShaderProcessor.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ShaderLanguage } from \"../../Materials/shaderLanguage\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { _IShaderProcessingContext } from \"./shaderProcessingOptions\";\r\n\r\n/** @internal */\r\nexport interface IShaderProcessor {\r\n    shaderLanguage: ShaderLanguage;\r\n\r\n    uniformRegexp?: RegExp;\r\n    uniformBufferRegexp?: RegExp;\r\n    textureRegexp?: RegExp;\r\n    noPrecision?: boolean;\r\n    parseGLES3?: boolean;\r\n\r\n    attributeKeywordName?: string;\r\n    varyingVertexKeywordName?: string;\r\n    varyingFragmentKeywordName?: string;\r\n\r\n    preProcessShaderCode?: (code: string, isFragment: boolean) => string;\r\n    attributeProcessor?: (attribute: string, preProcessors: { [key: string]: string }, processingContext: Nullable<_IShaderProcessingContext>) => string;\r\n    varyingCheck?: (varying: string, isFragment: boolean) => boolean;\r\n    varyingProcessor?: (varying: string, isFragment: boolean, preProcessors: { [key: string]: string }, processingContext: Nullable<_IShaderProcessingContext>) => string;\r\n    uniformProcessor?: (uniform: string, isFragment: boolean, preProcessors: { [key: string]: string }, processingContext: Nullable<_IShaderProcessingContext>) => string;\r\n    uniformBufferProcessor?: (uniformBuffer: string, isFragment: boolean, processingContext: Nullable<_IShaderProcessingContext>) => string;\r\n    textureProcessor?: (texture: string, isFragment: boolean, preProcessors: { [key: string]: string }, processingContext: Nullable<_IShaderProcessingContext>) => string;\r\n    endOfUniformBufferProcessor?: (closingBracketLine: string, isFragment: boolean, processingContext: Nullable<_IShaderProcessingContext>) => string;\r\n    lineProcessor?: (line: string, isFragment: boolean, processingContext: Nullable<_IShaderProcessingContext>) => string;\r\n    preProcessor?: (\r\n        code: string,\r\n        defines: string[],\r\n        preProcessors: { [key: string]: string },\r\n        isFragment: boolean,\r\n        processingContext: Nullable<_IShaderProcessingContext>\r\n    ) => string;\r\n    postProcessor?: (\r\n        code: string,\r\n        defines: string[],\r\n        isFragment: boolean,\r\n        processingContext: Nullable<_IShaderProcessingContext>,\r\n        patameters: {\r\n            [key: string]: number | string | boolean | undefined;\r\n        },\r\n        preProcessors: { [key: string]: string },\r\n        preProcessorsFromCode: { [key: string]: string }\r\n    ) => string;\r\n    initializeShaders?: (processingContext: Nullable<_IShaderProcessingContext>) => void;\r\n    finalizeShaders?: (vertexCode: string, fragmentCode: string, processingContext: Nullable<_IShaderProcessingContext>) => { vertexCode: string; fragmentCode: string };\r\n}\r\n"]}