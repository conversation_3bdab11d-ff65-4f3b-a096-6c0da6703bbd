{"version": 3, "file": "engine.renderTargetCube.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.renderTargetCube.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,kCAAsC;AACjE,OAAO,EAAE,eAAe,EAAyB,MAAM,6CAA6C,CAAC;AAErG,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAgB5C,gBAAgB,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAAU,IAAY,EAAE,OAAqC;IACpH,MAAM,SAAS,GAAG,IAAI,CAAC,kCAAkC,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAE7E,MAAM,WAAW,GAAG;QAChB,eAAe,EAAE,IAAI;QACrB,mBAAmB,EAAE,IAAI;QACzB,qBAAqB,EAAE,KAAK;QAC5B,IAAI,EAAE,SAAS,CAAC,yBAAyB;QACzC,YAAY,EAAE,SAAS,CAAC,8BAA8B;QACtD,MAAM,EAAE,SAAS,CAAC,kBAAkB;QACpC,OAAO,EAAE,CAAC;QACV,GAAG,OAAO;KACb,CAAC;IACF,WAAW,CAAC,qBAAqB,GAAG,WAAW,CAAC,mBAAmB,IAAI,WAAW,CAAC,qBAAqB,CAAC;IAEzG,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,IAAI,qBAAqB,CAAC;IAC7D,SAAS,CAAC,oBAAoB,GAAG,WAAW,CAAC,mBAAmB,CAAC;IACjE,SAAS,CAAC,sBAAsB,GAAG,WAAW,CAAC,qBAAqB,CAAC;IAErE,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,6CAAqC,CAAC;IAE9E,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;IACrB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;IAClB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;IACvB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACtB,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;IACtC,OAAO,CAAC,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;IACtD,OAAO,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;IAChD,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;IAChC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;IAEpC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC1C,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAE/B,IAAI,SAAS,CAAC,oBAAoB,IAAI,SAAS,CAAC,sBAAsB,EAAE,CAAC;QACrE,SAAS,CAAC,yBAAyB,CAC/B,CAAC,EACD,WAAW,CAAC,YAAY,KAAK,SAAS;YAClC,WAAW,CAAC,YAAY,KAAK,SAAS,CAAC,6BAA6B;YACpE,WAAW,CAAC,YAAY,KAAK,SAAS,CAAC,qBAAqB;YAC5D,WAAW,CAAC,YAAY,KAAK,SAAS,CAAC,8BAA8B;YACrE,WAAW,CAAC,YAAY,KAAK,SAAS,CAAC,+BAA+B;YACtE,WAAW,CAAC,YAAY,KAAK,SAAS,CAAC,iCAAiC;YACxE,WAAW,CAAC,YAAY,KAAK,SAAS,CAAC,gCAAgC;YACvE,WAAW,CAAC,YAAY,KAAK,SAAS,CAAC,sBAAsB;YAC7D,WAAW,CAAC,YAAY,KAAK,SAAS,CAAC,gCAAgC,EAC3E,SAAS,CAAC,sBAAsB,EAChC,SAAS,CAAC,OAAO,CACpB,CAAC;IACN,CAAC;IAED,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;QACnE,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;IACnC,CAAC;IAED,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;IAEhE,IAAI,OAAO,IAAI,OAAO,CAAC,aAAa,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;QACnE,OAAO,CAAC,eAAe,GAAG,KAAK,CAAC;IACpC,CAAC;IAED,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC", "sourcesContent": ["import { ThinWebGPUEngine } from \"core/Engines/thinWebGPUEngine\";\r\nimport { InternalTexture, InternalTextureSource } from \"../../../Materials/Textures/internalTexture\";\r\nimport type { RenderTargetCreationOptions } from \"../../../Materials/Textures/textureCreationOptions\";\r\nimport { Constants } from \"../../constants\";\r\nimport type { RenderTargetWrapper } from \"../../renderTargetWrapper\";\r\n\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a new render target cube wrapper\r\n         * @param size defines the size of the texture\r\n         * @param options defines the options used to create the texture\r\n         * @returns a new render target cube wrapper\r\n         */\r\n        createRenderTargetCubeTexture(size: number, options?: RenderTargetCreationOptions): RenderTargetWrapper;\r\n    }\r\n}\r\n\r\nThinWebGPUEngine.prototype.createRenderTargetCubeTexture = function (size: number, options?: RenderTargetCreationOptions): RenderTargetWrapper {\r\n    const rtWrapper = this._createHardwareRenderTargetWrapper(false, true, size);\r\n\r\n    const fullOptions = {\r\n        generateMipMaps: true,\r\n        generateDepthBuffer: true,\r\n        generateStencilBuffer: false,\r\n        type: Constants.TEXTURETYPE_UNSIGNED_BYTE,\r\n        samplingMode: Constants.TEXTURE_TRILINEAR_SAMPLINGMODE,\r\n        format: Constants.TEXTUREFORMAT_RGBA,\r\n        samples: 1,\r\n        ...options,\r\n    };\r\n    fullOptions.generateStencilBuffer = fullOptions.generateDepthBuffer && fullOptions.generateStencilBuffer;\r\n\r\n    rtWrapper.label = fullOptions.label ?? \"RenderTargetWrapper\";\r\n    rtWrapper._generateDepthBuffer = fullOptions.generateDepthBuffer;\r\n    rtWrapper._generateStencilBuffer = fullOptions.generateStencilBuffer;\r\n\r\n    const texture = new InternalTexture(this, InternalTextureSource.RenderTarget);\r\n\r\n    texture.width = size;\r\n    texture.height = size;\r\n    texture.depth = 0;\r\n    texture.isReady = true;\r\n    texture.isCube = true;\r\n    texture.samples = fullOptions.samples;\r\n    texture.generateMipMaps = fullOptions.generateMipMaps;\r\n    texture.samplingMode = fullOptions.samplingMode;\r\n    texture.type = fullOptions.type;\r\n    texture.format = fullOptions.format;\r\n\r\n    this._internalTexturesCache.push(texture);\r\n    rtWrapper.setTextures(texture);\r\n\r\n    if (rtWrapper._generateDepthBuffer || rtWrapper._generateStencilBuffer) {\r\n        rtWrapper.createDepthStencilTexture(\r\n            0,\r\n            fullOptions.samplingMode === undefined ||\r\n                fullOptions.samplingMode === Constants.TEXTURE_BILINEAR_SAMPLINGMODE ||\r\n                fullOptions.samplingMode === Constants.TEXTURE_LINEAR_LINEAR ||\r\n                fullOptions.samplingMode === Constants.TEXTURE_TRILINEAR_SAMPLINGMODE ||\r\n                fullOptions.samplingMode === Constants.TEXTURE_LINEAR_LINEAR_MIPLINEAR ||\r\n                fullOptions.samplingMode === Constants.TEXTURE_NEAREST_LINEAR_MIPNEAREST ||\r\n                fullOptions.samplingMode === Constants.TEXTURE_NEAREST_LINEAR_MIPLINEAR ||\r\n                fullOptions.samplingMode === Constants.TEXTURE_NEAREST_LINEAR ||\r\n                fullOptions.samplingMode === Constants.TEXTURE_LINEAR_LINEAR_MIPNEAREST,\r\n            rtWrapper._generateStencilBuffer,\r\n            rtWrapper.samples\r\n        );\r\n    }\r\n\r\n    if (options && options.createMipMaps && !fullOptions.generateMipMaps) {\r\n        texture.generateMipMaps = true;\r\n    }\r\n\r\n    this._textureHelper.createGPUTextureForInternalTexture(texture);\r\n\r\n    if (options && options.createMipMaps && !fullOptions.generateMipMaps) {\r\n        texture.generateMipMaps = false;\r\n    }\r\n\r\n    return rtWrapper;\r\n};\r\n"]}