{"version": 3, "file": "webGLRenderTargetWrapper.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/WebGL/webGLRenderTargetWrapper.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AAEzC,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;AAG7D,OAAO,EAAE,gBAAgB,EAAE,4DAAwD;AAEnF,gBAAgB;AAChB,MAAM,OAAO,wBAAyB,SAAQ,mBAAmB;IAoC7C,sBAAsB,CAAC,OAAkC,EAAE,eAAe,GAAG,IAAI;QAC7F,KAAK,CAAC,sBAAsB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QAEvD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO;QACX,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAqB,CAAC;QAC1C,MAAM,EAAE,GAAG,IAAI,CAAC,QAAkC,CAAC;QACnD,MAAM,eAAe,GAAG,OAAO,CAAC,gBAAkD,CAAC;QAEnF,IAAI,eAAe,IAAI,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1E,MAAM,SAAS,GAAG,MAAM,CAAC,mBAAmB,CAAC;YAC7C,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACtD,EAAE,CAAC,uBAAuB,CACtB,EAAE,CAAC,WAAW,EACd,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,EACpF,EAAE,CAAC,YAAY,EACf,eAAe,CAAC,mBAAmB,EAAE,CACxC,CAAC;YACF,MAAM,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAED,YAAY,OAAgB,EAAE,MAAe,EAAE,IAAiB,EAAE,MAAkB,EAAE,OAA8B;QAChH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QA1DzC;;WAEG;QACI,iBAAY,GAA+B,IAAI,CAAC;QACvD;;WAEG;QACI,wBAAmB,GAAgC,IAAI,CAAC;QAC/D,gEAAgE;QAChE;;WAEG;QACH,gEAAgE;QACzD,qBAAgB,GAA+B,IAAI,CAAC;QAE3D,YAAY;QACZ;;WAEG;QACI,uBAAkB,GAA2B,IAAI,CAAC;QACzD;;WAEG;QACI,8BAAyB,GAA2B,IAAI,CAAC;QAChE;;WAEG;QACI,6BAAwB,GAAG,KAAK,CAAC;QACxC;;WAEG;QACI,gBAAW,GAAG,CAAC,CAAC;QA6BnB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAEkB,yBAAyB;QACxC,IAAI,GAAG,GAAkC,IAAI,CAAC;QAE9C,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YAC5D,GAAG,GAAI,IAAI,CAAC,OAAkB,CAAC,kCAAkC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3F,GAAG,CAAC,OAAQ,CAAC,OAAO,GAAG,IAAI,CAAC;QAChC,CAAC;aAAM,CAAC;YACJ,GAAG,GAAG,KAAK,CAAC,yBAAyB,EAAE,CAAC;QAC5C,CAAC;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAEkB,wBAAwB,CAAC,MAAgC;QACxE,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;QAEvC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACxC,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QACtD,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAChD,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACpD,MAAM,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAElE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;IAC3I,CAAC;IAED;;;;;;;;;OASG;IACa,yBAAyB,CACrC,qBAA6B,CAAC,EAC9B,oBAA6B,IAAI,EACjC,kBAA2B,KAAK,EAChC,UAAkB,CAAC,EACnB,SAAiB,SAAS,CAAC,2BAA2B,EACtD,KAAc;QAEd,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAqB,CAAC;YAC1C,wFAAwF;YACxF,uFAAuF;YACvF,MAAM,kBAAkB,GAAG,MAAM,CAAC,mBAAmB,CAAC;YACtD,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YAEzB,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAClD,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,wBAAwB,EAAE,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAC/F,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,gBAAgB,EAAE,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YACvF,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,kBAAkB,EAAE,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YACzF,MAAM,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;YACnD,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEhD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,OAAO,KAAK,CAAC,yBAAyB,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3H,CAAC;IAED;;;OAGG;IACa,UAAU,CAAC,YAAsC;QAC7D,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAE/B,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QACzB,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,gBAAgB,IAAI,YAAY,CAAC,YAAY,CAAC;QAC/E,MAAM,MAAM,GAAG,IAAI,CAAC,OAAqB,CAAC;QAE1C,IAAI,YAAY,CAAC,mBAAmB,IAAI,YAAY,CAAC,mBAAmB,KAAK,WAAW,EAAE,CAAC;YACvF,EAAE,CAAC,kBAAkB,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAC5D,CAAC;QACD,YAAY,CAAC,mBAAmB,GAAG,WAAW,CAAC;QAC/C,MAAM,UAAU,GAAG,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC;QAC3G,MAAM,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QAC5C,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QACrF,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;OAMG;IACK,wBAAwB,CAAC,OAAwB,EAAE,kBAA0B,CAAC,EAAE,gBAAyB,EAAE,WAAmB,CAAC;QACnI,MAAM,eAAe,GAAG,OAAO,CAAC,gBAAwC,CAAC;QACzE,IAAI,CAAC,eAAe,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAqB,CAAC;QAC1C,MAAM,SAAS,GAAG,MAAM,CAAC,mBAAmB,CAAC;QAC7C,MAAM,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;QAE5C,IAAI,UAAe,CAAC;QACpB,IAAI,MAAM,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,EAAE,GAAG,IAAI,CAAC,QAAkC,CAAC;YAEnD,UAAU,GAAS,EAAG,CAAC,kBAAkB,GAAG,eAAe,CAAC,CAAC;YAC7D,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACpC,gBAAgB,GAAG,gBAAgB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACjF,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,eAAe,CAAC,kBAAkB,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YAC3H,CAAC;iBAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACxB,mEAAmE;gBACnE,oBAAoB;gBACpB,gBAAgB,GAAG,gBAAgB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAChF,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,2BAA2B,GAAG,gBAAgB,EAAE,eAAe,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YACzJ,CAAC;iBAAM,CAAC;gBACJ,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,UAAU,EAAE,eAAe,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YACrH,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,2BAA2B;YAC3B,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YAEzB,UAAU,GAAS,EAAG,CAAC,kBAAkB,GAAG,eAAe,GAAG,QAAQ,CAAC,CAAC;YACxE,MAAM,MAAM,GAAG,gBAAgB,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,2BAA2B,GAAG,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC;YAElH,EAAE,CAAC,oBAAoB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAC9G,CAAC;QAED,IAAI,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACvD,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;YACzB,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACtD,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE,CAAC,YAAY,EAAE,eAAe,CAAC,mBAAmB,EAAE,CAAC,CAAC;QACnH,CAAC;QAED,MAAM,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;OAKG;IACa,UAAU,CAAC,OAAwB,EAAE,QAAgB,CAAC,EAAE,kBAA2B,IAAI;QACnG,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;QAClD,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAED;;;;OAIG;IACa,sBAAsB,CAAC,MAAgB,EAAE,KAAe;QACpE,KAAK,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAE5C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5D,OAAO;QACX,CAAC;QAED,mHAAmH;QACnH,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACvE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK,EAAE,EAAE,CAAC;YAChD,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,yGAAyG;gBACzG,SAAS;YACb,CAAC;YACD,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;gBACpC,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YAC5E,CAAC;iBAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACxB,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3E,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,wBAAwB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACa,oBAAoB,CAAC,QAAgB,CAAC,EAAE,KAAc,EAAE,IAAa;QACjF,KAAK,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5D,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;QACzF,CAAC;aAAM,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QACxF,CAAC;IACL,CAAC;IAEe,mBAAmB;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAqB,CAAC;QAC1C,MAAM,kBAAkB,GAAG,MAAM,CAAC,mBAAmB,CAAC;QAEtD,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEtD,KAAK,CAAC,mBAAmB,EAAE,CAAC;QAE5B,MAAM,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;IACvD,CAAC;IAEe,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC,wBAAwB;QAC3E,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC;QAEzB,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACrD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACnC,CAAC;YACD,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACjC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;gBAC5D,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAChD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QACpC,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACjC,CAAC;QAED,KAAK,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;IAC3C,CAAC;CACJ", "sourcesContent": ["import type { InternalTexture } from \"../../Materials/Textures/internalTexture\";\r\nimport type { TextureSize } from \"../../Materials/Textures/textureCreationOptions\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { Constants } from \"../constants\";\r\nimport type { Engine } from \"../engine\";\r\nimport { RenderTargetWrapper } from \"../renderTargetWrapper\";\r\nimport type { ThinEngine } from \"../thinEngine\";\r\nimport type { WebGLHardwareTexture } from \"./webGLHardwareTexture\";\r\nimport { HasStencilAspect } from \"core/Materials/Textures/textureHelper.functions\";\r\n\r\n/** @internal */\r\nexport class WebGLRenderTargetWrapper extends RenderTargetWrapper {\r\n    private _context: WebGLRenderingContext;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _framebuffer: Nullable<WebGLFramebuffer> = null;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _depthStencilBuffer: Nullable<WebGLRenderbuffer> = null;\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    /**\r\n     * @internal\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public _MSAAFramebuffer: Nullable<WebGLFramebuffer> = null;\r\n\r\n    // Multiview\r\n    /**\r\n     * @internal\r\n     */\r\n    public _colorTextureArray: Nullable<WebGLTexture> = null;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _depthStencilTextureArray: Nullable<WebGLTexture> = null;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _disposeOnlyFramebuffers = false;\r\n    /**\r\n     * @internal\r\n     */\r\n    public _currentLOD = 0;\r\n\r\n    public override setDepthStencilTexture(texture: Nullable<InternalTexture>, disposeExisting = true) {\r\n        super.setDepthStencilTexture(texture, disposeExisting);\r\n\r\n        if (!texture) {\r\n            return;\r\n        }\r\n\r\n        const engine = this._engine as ThinEngine;\r\n        const gl = this._context as WebGL2RenderingContext;\r\n        const hardwareTexture = texture._hardwareTexture as Nullable<WebGLHardwareTexture>;\r\n\r\n        if (hardwareTexture && texture._autoMSAAManagement && this._MSAAFramebuffer) {\r\n            const currentFb = engine._currentFramebuffer;\r\n            engine._bindUnboundFramebuffer(this._MSAAFramebuffer);\r\n            gl.framebufferRenderbuffer(\r\n                gl.FRAMEBUFFER,\r\n                HasStencilAspect(texture.format) ? gl.DEPTH_STENCIL_ATTACHMENT : gl.DEPTH_ATTACHMENT,\r\n                gl.RENDERBUFFER,\r\n                hardwareTexture.getMSAARenderBuffer()\r\n            );\r\n            engine._bindUnboundFramebuffer(currentFb);\r\n        }\r\n    }\r\n\r\n    constructor(isMulti: boolean, isCube: boolean, size: TextureSize, engine: ThinEngine, context: WebGLRenderingContext) {\r\n        super(isMulti, isCube, size, engine);\r\n\r\n        this._context = context;\r\n    }\r\n\r\n    protected override _cloneRenderTargetWrapper(): Nullable<RenderTargetWrapper> {\r\n        let rtw: Nullable<RenderTargetWrapper> = null;\r\n\r\n        if (this._colorTextureArray && this._depthStencilTextureArray) {\r\n            rtw = (this._engine as Engine).createMultiviewRenderTargetTexture(this.width, this.height);\r\n            rtw.texture!.isReady = true;\r\n        } else {\r\n            rtw = super._cloneRenderTargetWrapper();\r\n        }\r\n\r\n        return rtw;\r\n    }\r\n\r\n    protected override _swapRenderTargetWrapper(target: WebGLRenderTargetWrapper): void {\r\n        super._swapRenderTargetWrapper(target);\r\n\r\n        target._framebuffer = this._framebuffer;\r\n        target._depthStencilBuffer = this._depthStencilBuffer;\r\n        target._MSAAFramebuffer = this._MSAAFramebuffer;\r\n        target._colorTextureArray = this._colorTextureArray;\r\n        target._depthStencilTextureArray = this._depthStencilTextureArray;\r\n\r\n        this._framebuffer = this._depthStencilBuffer = this._MSAAFramebuffer = this._colorTextureArray = this._depthStencilTextureArray = null;\r\n    }\r\n\r\n    /**\r\n     * Creates the depth/stencil texture\r\n     * @param comparisonFunction Comparison function to use for the texture\r\n     * @param bilinearFiltering true if bilinear filtering should be used when sampling the texture\r\n     * @param generateStencil true if the stencil aspect should also be created\r\n     * @param samples sample count to use when creating the texture\r\n     * @param format format of the depth texture\r\n     * @param label defines the label to use for the texture (for debugging purpose only)\r\n     * @returns the depth/stencil created texture\r\n     */\r\n    public override createDepthStencilTexture(\r\n        comparisonFunction: number = 0,\r\n        bilinearFiltering: boolean = true,\r\n        generateStencil: boolean = false,\r\n        samples: number = 1,\r\n        format: number = Constants.TEXTUREFORMAT_DEPTH32_FLOAT,\r\n        label?: string\r\n    ): InternalTexture {\r\n        if (this._depthStencilBuffer) {\r\n            const engine = this._engine as ThinEngine;\r\n            // Dispose previous depth/stencil render buffers and clear the corresponding attachment.\r\n            // Next time this framebuffer is bound, the new depth/stencil texture will be attached.\r\n            const currentFrameBuffer = engine._currentFramebuffer;\r\n            const gl = this._context;\r\n\r\n            engine._bindUnboundFramebuffer(this._framebuffer);\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.DEPTH_STENCIL_ATTACHMENT, gl.RENDERBUFFER, null);\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.DEPTH_ATTACHMENT, gl.RENDERBUFFER, null);\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, gl.STENCIL_ATTACHMENT, gl.RENDERBUFFER, null);\r\n            engine._bindUnboundFramebuffer(currentFrameBuffer);\r\n            gl.deleteRenderbuffer(this._depthStencilBuffer);\r\n\r\n            this._depthStencilBuffer = null;\r\n        }\r\n\r\n        return super.createDepthStencilTexture(comparisonFunction, bilinearFiltering, generateStencil, samples, format, label);\r\n    }\r\n\r\n    /**\r\n     * Shares the depth buffer of this render target with another render target.\r\n     * @param renderTarget Destination renderTarget\r\n     */\r\n    public override shareDepth(renderTarget: WebGLRenderTargetWrapper): void {\r\n        super.shareDepth(renderTarget);\r\n\r\n        const gl = this._context;\r\n        const depthbuffer = this._depthStencilBuffer;\r\n        const framebuffer = renderTarget._MSAAFramebuffer || renderTarget._framebuffer;\r\n        const engine = this._engine as ThinEngine;\r\n\r\n        if (renderTarget._depthStencilBuffer && renderTarget._depthStencilBuffer !== depthbuffer) {\r\n            gl.deleteRenderbuffer(renderTarget._depthStencilBuffer);\r\n        }\r\n        renderTarget._depthStencilBuffer = depthbuffer;\r\n        const attachment = renderTarget._generateStencilBuffer ? gl.DEPTH_STENCIL_ATTACHMENT : gl.DEPTH_ATTACHMENT;\r\n        engine._bindUnboundFramebuffer(framebuffer);\r\n        gl.framebufferRenderbuffer(gl.FRAMEBUFFER, attachment, gl.RENDERBUFFER, depthbuffer);\r\n        engine._bindUnboundFramebuffer(null);\r\n    }\r\n\r\n    /**\r\n     * Binds a texture to this render target on a specific attachment\r\n     * @param texture The texture to bind to the framebuffer\r\n     * @param attachmentIndex Index of the attachment\r\n     * @param faceIndexOrLayer The face or layer of the texture to render to in case of cube texture or array texture\r\n     * @param lodLevel defines the lod level to bind to the frame buffer\r\n     */\r\n    private _bindTextureRenderTarget(texture: InternalTexture, attachmentIndex: number = 0, faceIndexOrLayer?: number, lodLevel: number = 0) {\r\n        const hardwareTexture = texture._hardwareTexture as WebGLHardwareTexture;\r\n        if (!hardwareTexture) {\r\n            return;\r\n        }\r\n\r\n        const framebuffer = this._framebuffer;\r\n        const engine = this._engine as ThinEngine;\r\n        const currentFb = engine._currentFramebuffer;\r\n        engine._bindUnboundFramebuffer(framebuffer);\r\n\r\n        let attachment: any;\r\n        if (engine.webGLVersion > 1) {\r\n            const gl = this._context as WebGL2RenderingContext;\r\n\r\n            attachment = (<any>gl)[\"COLOR_ATTACHMENT\" + attachmentIndex];\r\n            if (texture.is2DArray || texture.is3D) {\r\n                faceIndexOrLayer = faceIndexOrLayer ?? this.layerIndices?.[attachmentIndex] ?? 0;\r\n                gl.framebufferTextureLayer(gl.FRAMEBUFFER, attachment, hardwareTexture.underlyingResource, lodLevel, faceIndexOrLayer);\r\n            } else if (texture.isCube) {\r\n                // if face index is not specified, try to query it from faceIndices\r\n                // default is face 0\r\n                faceIndexOrLayer = faceIndexOrLayer ?? this.faceIndices?.[attachmentIndex] ?? 0;\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndexOrLayer, hardwareTexture.underlyingResource, lodLevel);\r\n            } else {\r\n                gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, gl.TEXTURE_2D, hardwareTexture.underlyingResource, lodLevel);\r\n            }\r\n        } else {\r\n            // Default behavior (WebGL)\r\n            const gl = this._context;\r\n\r\n            attachment = (<any>gl)[\"COLOR_ATTACHMENT\" + attachmentIndex + \"_WEBGL\"];\r\n            const target = faceIndexOrLayer !== undefined ? gl.TEXTURE_CUBE_MAP_POSITIVE_X + faceIndexOrLayer : gl.TEXTURE_2D;\r\n\r\n            gl.framebufferTexture2D(gl.FRAMEBUFFER, attachment, target, hardwareTexture.underlyingResource, lodLevel);\r\n        }\r\n\r\n        if (texture._autoMSAAManagement && this._MSAAFramebuffer) {\r\n            const gl = this._context;\r\n            engine._bindUnboundFramebuffer(this._MSAAFramebuffer);\r\n            gl.framebufferRenderbuffer(gl.FRAMEBUFFER, attachment, gl.RENDERBUFFER, hardwareTexture.getMSAARenderBuffer());\r\n        }\r\n\r\n        engine._bindUnboundFramebuffer(currentFb);\r\n    }\r\n\r\n    /**\r\n     * Set a texture in the textures array\r\n     * @param texture the texture to set\r\n     * @param index the index in the textures array to set\r\n     * @param disposePrevious If this function should dispose the previous texture\r\n     */\r\n    public override setTexture(texture: InternalTexture, index: number = 0, disposePrevious: boolean = true) {\r\n        super.setTexture(texture, index, disposePrevious);\r\n        this._bindTextureRenderTarget(texture, index);\r\n    }\r\n\r\n    /**\r\n     * Sets the layer and face indices of every render target texture\r\n     * @param layers The layer of the texture to be set (make negative to not modify)\r\n     * @param faces The face of the texture to be set (make negative to not modify)\r\n     */\r\n    public override setLayerAndFaceIndices(layers: number[], faces: number[]) {\r\n        super.setLayerAndFaceIndices(layers, faces);\r\n\r\n        if (!this.textures || !this.layerIndices || !this.faceIndices) {\r\n            return;\r\n        }\r\n\r\n        // the length of this._attachments is the right one as it does not count the depth texture, in case we generated it\r\n        const textureCount = this._attachments?.length ?? this.textures.length;\r\n        for (let index = 0; index < textureCount; index++) {\r\n            const texture = this.textures[index];\r\n            if (!texture) {\r\n                // The target type was probably -1 at creation time and setTexture has not been called yet for this index\r\n                continue;\r\n            }\r\n            if (texture.is2DArray || texture.is3D) {\r\n                this._bindTextureRenderTarget(texture, index, this.layerIndices[index]);\r\n            } else if (texture.isCube) {\r\n                this._bindTextureRenderTarget(texture, index, this.faceIndices[index]);\r\n            } else {\r\n                this._bindTextureRenderTarget(texture, index);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the face and layer indices of a texture in the textures array\r\n     * @param index The index of the texture in the textures array to modify\r\n     * @param layer The layer of the texture to be set\r\n     * @param face The face of the texture to be set\r\n     */\r\n    public override setLayerAndFaceIndex(index: number = 0, layer?: number, face?: number): void {\r\n        super.setLayerAndFaceIndex(index, layer, face);\r\n\r\n        if (!this.textures || !this.layerIndices || !this.faceIndices) {\r\n            return;\r\n        }\r\n\r\n        const texture = this.textures[index];\r\n        if (texture.is2DArray || texture.is3D) {\r\n            this._bindTextureRenderTarget(this.textures[index], index, this.layerIndices[index]);\r\n        } else if (texture.isCube) {\r\n            this._bindTextureRenderTarget(this.textures[index], index, this.faceIndices[index]);\r\n        }\r\n    }\r\n\r\n    public override resolveMSAATextures(): void {\r\n        const engine = this._engine as ThinEngine;\r\n        const currentFramebuffer = engine._currentFramebuffer;\r\n\r\n        engine._bindUnboundFramebuffer(this._MSAAFramebuffer);\r\n\r\n        super.resolveMSAATextures();\r\n\r\n        engine._bindUnboundFramebuffer(currentFramebuffer);\r\n    }\r\n\r\n    public override dispose(disposeOnlyFramebuffers = this._disposeOnlyFramebuffers): void {\r\n        const gl = this._context;\r\n\r\n        if (!disposeOnlyFramebuffers) {\r\n            if (this._colorTextureArray) {\r\n                this._context.deleteTexture(this._colorTextureArray);\r\n                this._colorTextureArray = null;\r\n            }\r\n            if (this._depthStencilTextureArray) {\r\n                this._context.deleteTexture(this._depthStencilTextureArray);\r\n                this._depthStencilTextureArray = null;\r\n            }\r\n        }\r\n\r\n        if (this._framebuffer) {\r\n            gl.deleteFramebuffer(this._framebuffer);\r\n            this._framebuffer = null;\r\n        }\r\n\r\n        if (this._depthStencilBuffer) {\r\n            gl.deleteRenderbuffer(this._depthStencilBuffer);\r\n            this._depthStencilBuffer = null;\r\n        }\r\n\r\n        if (this._MSAAFramebuffer) {\r\n            gl.deleteFramebuffer(this._MSAAFramebuffer);\r\n            this._MSAAFramebuffer = null;\r\n        }\r\n\r\n        super.dispose(disposeOnlyFramebuffers);\r\n    }\r\n}\r\n"]}