import { Matrix } from "../../Maths/math.vector.js";
/**
 * This represents all the required metrics to create a VR camera.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/cameras/camera_introduction#device-orientation-camera
 */
export declare class VRCameraMetrics {
    /**
     * Define the horizontal resolution off the screen.
     */
    hResolution: number;
    /**
     * Define the vertical resolution off the screen.
     */
    vResolution: number;
    /**
     * Define the horizontal screen size.
     */
    hScreenSize: number;
    /**
     * Define the vertical screen size.
     */
    vScreenSize: number;
    /**
     * Define the vertical screen center position.
     */
    vScreenCenter: number;
    /**
     * Define the distance of the eyes to the screen.
     */
    eyeToScreenDistance: number;
    /**
     * Define the distance between both lenses
     */
    lensSeparationDistance: number;
    /**
     * Define the distance between both viewer's eyes.
     */
    interpupillaryDistance: number;
    /**
     * Define the distortion factor of the VR postprocess.
     * Please, touch with care.
     */
    distortionK: number[];
    /**
     * Define the chromatic aberration correction factors for the VR post process.
     */
    chromaAbCorrection: number[];
    /**
     * Define the scale factor of the post process.
     * The smaller the better but the slower.
     */
    postProcessScaleFactor: number;
    /**
     * Define an offset for the lens center.
     */
    lensCenterOffset: number;
    /**
     * Define if the current vr camera should compensate the distortion of the lens or not.
     */
    compensateDistortion: boolean;
    /**
     * Defines if multiview should be enabled when rendering (Default: false)
     */
    multiviewEnabled: boolean;
    /**
     * Gets the rendering aspect ratio based on the provided resolutions.
     */
    get aspectRatio(): number;
    /**
     * Gets the aspect ratio based on the FOV, scale factors, and real screen sizes.
     */
    get aspectRatioFov(): number;
    /**
     * @internal
     */
    get leftHMatrix(): Matrix;
    /**
     * @internal
     */
    get rightHMatrix(): Matrix;
    /**
     * @internal
     */
    get leftPreViewMatrix(): Matrix;
    /**
     * @internal
     */
    get rightPreViewMatrix(): Matrix;
    /**
     * Get the default VRMetrics based on the most generic setup.
     * @returns the default vr metrics
     */
    static GetDefault(): VRCameraMetrics;
}
