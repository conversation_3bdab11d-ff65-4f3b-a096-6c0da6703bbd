{"version": 3, "file": "abstractSoundSource.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/AudioV2/abstractAudio/abstractSoundSource.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAuB9D;;GAEG;AACH,MAAM,OAAgB,mBAAoB,SAAQ,oBAAoB;IAGlE,YAAsB,IAAY,EAAE,MAAqB,EAAE,4CAAmD;QAC1G,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAH1B,YAAO,GAA8B,IAAI,CAAC;QAuD1C,sBAAiB,GAAG,GAAG,EAAE;YAC7B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACxB,CAAC,CAAC;IArDF,CAAC;IAED;;;OAGG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED,IAAW,MAAM,CAAC,MAAiC;QAC/C,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;YAC1B,OAAO;QACX,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACxE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACtC,CAAC;QACL,CAAC;IACL,CAAC;IAYD;;OAEG;IACa,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACxB,CAAC;CAKJ", "sourcesContent": ["import type { Nullable } from \"../../types\";\nimport { AudioNodeType } from \"./abstractAudioNode\";\nimport type { IAbstractAudioOutNodeOptions } from \"./abstractAudioOutNode\";\nimport { AbstractAudioOutNode } from \"./abstractAudioOutNode\";\nimport type { PrimaryAudioBus } from \"./audioBus\";\nimport type { AudioEngineV2 } from \"./audioEngineV2\";\nimport type { AbstractSpatialAudio, ISpatialAudioOptions } from \"./subProperties/abstractSpatialAudio\";\nimport type { AbstractStereoAudio, IStereoAudioOptions } from \"./subProperties/abstractStereoAudio\";\n\n/**\n * Options for creating a sound source.\n */\nexport interface ISoundSourceOptions extends IAbstractAudioOutNodeOptions, ISpatialAudioOptions, IStereoAudioOptions {\n    /**\n     * The output bus for the sound source. Defaults to `null`.\n     * - If not set or `null`, and `outBusAutoDefault` is `true`, then the sound source is automatically connected to the audio engine's default main bus.\n     * @see {@link AudioEngineV2.defaultMainBus}\n     */\n    outBus: Nullable<PrimaryAudioBus>;\n\n    /**\n     * Whether the sound's `outBus` should default to the audio engine's main bus. Defaults to `true` for all sound sources except microphones.\n     */\n    outBusAutoDefault: boolean;\n}\n\n/**\n * Abstract class representing a sound in the audio engine.\n */\nexport abstract class AbstractSoundSource extends AbstractAudioOutNode {\n    private _outBus: Nullable<PrimaryAudioBus> = null;\n\n    protected constructor(name: string, engine: AudioEngineV2, nodeType: AudioNodeType = AudioNodeType.HAS_OUTPUTS) {\n        super(name, engine, nodeType);\n    }\n\n    /**\n     * The output bus for the sound.\n     * @see {@link AudioEngineV2.defaultMainBus}\n     */\n    public get outBus(): Nullable<PrimaryAudioBus> {\n        return this._outBus;\n    }\n\n    public set outBus(outBus: Nullable<PrimaryAudioBus>) {\n        if (this._outBus === outBus) {\n            return;\n        }\n\n        if (this._outBus) {\n            this._outBus.onDisposeObservable.removeCallback(this._onOutBusDisposed);\n            if (!this._disconnect(this._outBus)) {\n                throw new Error(\"Disconnect failed\");\n            }\n        }\n\n        this._outBus = outBus;\n\n        if (this._outBus) {\n            this._outBus.onDisposeObservable.add(this._onOutBusDisposed);\n            if (!this._connect(this._outBus)) {\n                throw new Error(\"Connect failed\");\n            }\n        }\n    }\n\n    /**\n     * The spatial features of the sound.\n     */\n    public abstract spatial: AbstractSpatialAudio;\n\n    /**\n     * The stereo features of the sound.\n     */\n    public abstract stereo: AbstractStereoAudio;\n\n    /**\n     * Releases associated resources.\n     */\n    public override dispose(): void {\n        super.dispose();\n\n        this._outBus = null;\n    }\n\n    private _onOutBusDisposed = () => {\n        this._outBus = null;\n    };\n}\n"]}