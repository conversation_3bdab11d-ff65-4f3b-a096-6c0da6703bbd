module.exports = {

"[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/iesTextureLoader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/iesTextureLoader.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/ddsTextureLoader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/ddsTextureLoader.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/basisTextureLoader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/basisTextureLoader.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/envTextureLoader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/envTextureLoader.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/hdrTextureLoader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/hdrTextureLoader.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/ktxTextureLoader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/ktxTextureLoader.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/tgaTextureLoader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/tgaTextureLoader.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/exrTextureLoader.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Materials/Textures/Loaders/exrTextureLoader.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Meshes/mesh.vertexData.functions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Meshes/mesh.vertexData.functions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioBus.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioBus.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioMainBus.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioMainBus.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioStaticSound.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioStaticSound.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioSoundSource.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioSoundSource.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioStreamingSound.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/AudioV2/webAudio/webAudioStreamingSound.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Misc/dumpTools.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Misc/dumpTools.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/postprocess.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/postprocess.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/postprocess.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/postprocess.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Misc/dds.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Misc/dds.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/pass.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/pass.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/pass.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/pass.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/passCube.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/passCube.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/passCube.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/passCube.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/lod.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/lod.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/lodCube.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/lodCube.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/lod.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/lod.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/lodCube.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/lodCube.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/anaglyph.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/anaglyph.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/anaglyph.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/anaglyph.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/vrDistortionCorrection.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/vrDistortionCorrection.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/vrDistortionCorrection.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/vrDistortionCorrection.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/default.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/default.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/default.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/default.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/default.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/default.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/default.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/default.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/picking.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/picking.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/picking.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/picking.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/picking.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/picking.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/picking.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/picking.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Culling/Helper/transformFeedbackBoundingHelper.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Culling/Helper/transformFeedbackBoundingHelper.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Culling/Helper/computeShaderBoundingHelper.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Culling/Helper/computeShaderBoundingHelper.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/color.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/color.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/color.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/color.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/color.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/color.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/color.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/color.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/kernelBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/kernelBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/kernelBlur.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/kernelBlur.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/kernelBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/kernelBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/kernelBlur.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/kernelBlur.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/shadowMap.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/shadowMap.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/shadowMap.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/shadowMap.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/depthBoxBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/depthBoxBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowMapFragmentSoftTransparentShadow.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowMapFragmentSoftTransparentShadow.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/shadowMap.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/shadowMap.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/shadowMap.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/shadowMap.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/depthBoxBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/depthBoxBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowMapFragmentSoftTransparentShadow.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowMapFragmentSoftTransparentShadow.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/depth.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/depth.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/depth.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/depth.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/depth.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/depth.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/depth.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/depth.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/minmaxRedux.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/minmaxRedux.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/minmaxRedux.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/minmaxRedux.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/rgbdDecode.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/rgbdDecode.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/rgbdDecode.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/rgbdDecode.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/rgbdEncode.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/rgbdEncode.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/rgbdEncode.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/rgbdEncode.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Engines/thinEngine.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Engines/thinEngine.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphPlayAnimationBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphPlayAnimationBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphStopAnimationBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphStopAnimationBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphPauseAnimationBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphPauseAnimationBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphInterpolationBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphInterpolationBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphSceneReadyEventBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphSceneReadyEventBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphSceneTickEventBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphSceneTickEventBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphSendCustomEventBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphSendCustomEventBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphReceiveCustomEventBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphReceiveCustomEventBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphMeshPickEventBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphMeshPickEventBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphMathBlocks.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphMathBlocks.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphVectorMathBlocks.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphVectorMathBlocks.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphMatrixMathBlocks.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphMatrixMathBlocks.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphBranchBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphBranchBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphSetDelayBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphSetDelayBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphCancelDelayBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphCancelDelayBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphCounterBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphCounterBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphDebounceBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphDebounceBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphThrottleBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphThrottleBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphDoNBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphDoNBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphFlipFlopBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphFlipFlopBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphForLoopBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphForLoopBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphMultiGateBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphMultiGateBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphSequenceBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphSequenceBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphSwitchBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphSwitchBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphWaitAllBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphWaitAllBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphWhileLoopBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/ControlFlow/flowGraphWhileLoopBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/flowGraphConsoleLogBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/flowGraphConsoleLogBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphConditionalDataBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphConditionalDataBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphConstantBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphConstantBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphTransformCoordinatesSystemBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphTransformCoordinatesSystemBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphGetAssetBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphGetAssetBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphGetPropertyBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphGetPropertyBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/flowGraphSetPropertyBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/flowGraphSetPropertyBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphGetVariableBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphGetVariableBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/flowGraphSetVariableBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/flowGraphSetVariableBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Transformers/flowGraphJsonPointerParserBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Transformers/flowGraphJsonPointerParserBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphMathCombineExtractBlocks.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Math/flowGraphMathCombineExtractBlocks.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Transformers/flowGraphTypeToTypeBlocks.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Transformers/flowGraphTypeToTypeBlocks.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphEasingBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphEasingBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphBezierCurveEasingBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Execution/Animation/flowGraphBezierCurveEasingBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphPointerOverEventBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphPointerOverEventBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphPointerOutEventBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Event/flowGraphPointerOutEventBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphContextBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphContextBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphArrayIndexBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphArrayIndexBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphCodeExecutionBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphCodeExecutionBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphIndexOfBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphIndexOfBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphFunctionReferenceBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/Utils/flowGraphFunctionReferenceBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphDataSwitchBlock.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/FlowGraph/Blocks/Data/flowGraphDataSwitchBlock.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/copyTextureToTexture.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/copyTextureToTexture.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/copyTextureToTexture.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/copyTextureToTexture.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/glowBlurPostProcess.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/glowBlurPostProcess.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/glowBlurPostProcess.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/glowBlurPostProcess.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/glowMapGeneration.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/glowMapGeneration.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/glowMapGeneration.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/glowMapGeneration.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/glowMapGeneration.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/glowMapGeneration.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/glowMapGeneration.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/glowMapGeneration.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/glowMapMerge.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/glowMapMerge.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/glowMapMerge.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/glowMapMerge.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/glowMapMerge.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/glowMapMerge.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/glowMapMerge.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/glowMapMerge.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/blackAndWhite.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/blackAndWhite.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/blackAndWhite.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/blackAndWhite.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/bloomMerge.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/bloomMerge.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/bloomMerge.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/bloomMerge.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/extractHighlights.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/extractHighlights.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/extractHighlights.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/extractHighlights.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/chromaticAberration.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/chromaticAberration.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/chromaticAberration.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/chromaticAberration.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/circleOfConfusion.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/circleOfConfusion.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/circleOfConfusion.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/circleOfConfusion.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/depthOfFieldMerge.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/depthOfFieldMerge.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/depthOfFieldMerge.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/depthOfFieldMerge.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fxaa.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fxaa.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fxaa.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fxaa.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fxaa.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fxaa.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fxaa.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fxaa.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/grain.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/grain.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/grain.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/grain.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/imageProcessing.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/imageProcessing.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/imageProcessing.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/imageProcessing.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/motionBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/motionBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/motionBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/motionBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/screenSpaceReflection2.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/screenSpaceReflection2.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/screenSpaceReflection2.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/screenSpaceReflection2.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/screenSpaceReflection2Blur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/screenSpaceReflection2Blur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/screenSpaceReflection2Blur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/screenSpaceReflection2Blur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/screenSpaceReflection2BlurCombiner.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/screenSpaceReflection2BlurCombiner.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/screenSpaceReflection2BlurCombiner.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/screenSpaceReflection2BlurCombiner.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/taa.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/taa.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/taa.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/taa.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/background.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/background.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/background.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/background.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/background.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/background.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/background.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/background.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/pbr.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/pbr.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/pbr.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/pbr.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/pbr.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/pbr.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/pbr.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/pbr.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/procedural.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/procedural.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/procedural.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/procedural.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/layer.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/layer.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/layer.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/layer.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/layer.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/layer.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/layer.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/layer.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/lensFlare.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/lensFlare.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/lensFlare.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/lensFlare.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/lensFlare.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/lensFlare.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/lensFlare.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/lensFlare.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/hdrFiltering.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/hdrFiltering.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/hdrFiltering.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/hdrFiltering.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/hdrFiltering.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/hdrFiltering.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/hdrFiltering.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/hdrFiltering.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblCdfx.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblCdfx.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblCdfy.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblCdfy.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblScaledLuminance.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblScaledLuminance.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblCdfx.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblCdfx.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblCdfy.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblCdfy.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblScaledLuminance.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblScaledLuminance.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblIcdf.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblIcdf.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblDominantDirection.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblDominantDirection.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblIcdf.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblIcdf.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblDominantDirection.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblDominantDirection.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblCdfDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblCdfDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblCdfDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblCdfDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/hdrIrradianceFiltering.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/hdrIrradianceFiltering.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/hdrIrradianceFiltering.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/hdrIrradianceFiltering.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/hdrIrradianceFiltering.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/hdrIrradianceFiltering.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/hdrIrradianceFiltering.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/hdrIrradianceFiltering.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/sprites.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/sprites.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/sprites.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/sprites.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/sprites.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/sprites.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/sprites.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/sprites.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/gaussianSplatting.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/gaussianSplatting.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/gaussianSplatting.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/gaussianSplatting.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/gaussianSplatting.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/gaussianSplatting.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/gaussianSplatting.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/gaussianSplatting.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bonesDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bonesDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bonesVertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bonesVertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bonesDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bonesDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bonesVertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bonesVertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/morphTargetsVertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/morphTargetsVertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/morphTargetsVertexDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/morphTargetsVertexDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/morphTargetsVertexGlobal.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/morphTargetsVertexGlobal.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/morphTargetsVertexGlobalDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/morphTargetsVertexGlobalDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/morphTargetsVertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/morphTargetsVertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/morphTargetsVertexDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/morphTargetsVertexDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/morphTargetsVertexGlobal.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/morphTargetsVertexGlobal.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/morphTargetsVertexGlobalDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/morphTargetsVertexGlobalDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/helperFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/helperFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/imageProcessingDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/imageProcessingDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/imageProcessingFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/imageProcessingFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/helperFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/helperFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/imageProcessingDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/imageProcessingDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/imageProcessingFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/imageProcessingFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bumpFragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bumpFragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bumpFragmentMainFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bumpFragmentMainFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bumpFragmentFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/bumpFragmentFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bumpFragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bumpFragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bumpFragmentMainFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bumpFragmentMainFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bumpFragmentFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/bumpFragmentFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowMapVertexMetric.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowMapVertexMetric.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/packingFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/packingFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowMapFragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowMapFragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowMapVertexMetric.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowMapVertexMetric.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/packingFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/packingFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowMapFragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowMapFragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/fogFragmentDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/fogFragmentDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/fogFragmentDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/fogFragmentDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightFragmentDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightFragmentDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightFragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightFragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightUboDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightUboDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightVxUboDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightVxUboDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightVxFragmentDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightVxFragmentDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightsFragmentFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/lightsFragmentFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowsFragmentFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowsFragmentFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowsVertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/shadowsVertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/lightFragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/lightFragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/lightUboDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/lightUboDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/lightVxUboDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/lightVxUboDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/lightsFragmentFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/lightsFragmentFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowsFragmentFunctions.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowsFragmentFunctions.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowsVertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/shadowsVertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/reflectionFunction.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/reflectionFunction.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/reflectionFunction.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/reflectionFunction.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/clipPlaneFragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/clipPlaneFragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/clipPlaneFragmentDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/clipPlaneFragmentDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/clipPlaneVertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/clipPlaneVertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/clipPlaneVertexDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ShadersInclude/clipPlaneVertexDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneFragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneFragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneFragmentDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneFragmentDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneVertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneVertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneVertexDeclaration.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ShadersInclude/clipPlaneVertexDeclaration.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/greasedLine.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/greasedLine.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/greasedLine.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/greasedLine.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/greasedLine.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/greasedLine.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/greasedLine.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/greasedLine.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRenderer.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRenderer.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRenderer.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRenderer.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRendererMasker.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRendererMasker.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRendererMasker.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRendererMasker.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRendererFinaliser.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRendererFinaliser.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRendererFinaliser.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/meshUVSpaceRendererFinaliser.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRenderer.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRenderer.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRenderer.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRenderer.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRendererMasker.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRendererMasker.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRendererMasker.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRendererMasker.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRendererFinaliser.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRendererFinaliser.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRendererFinaliser.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/meshUVSpaceRendererFinaliser.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/particles.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/particles.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/particles.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/particles.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/particles.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/particles.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/particles.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/particles.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/colorCorrection.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/colorCorrection.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/colorCorrection.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/colorCorrection.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/convolution.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/convolution.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/convolution.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/convolution.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/displayPass.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/displayPass.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/displayPass.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/displayPass.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/filter.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/filter.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/filter.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/filter.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/highlights.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/highlights.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/highlights.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/highlights.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/geometry.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/geometry.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/geometry.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/geometry.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/geometry.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/geometry.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/geometry.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/geometry.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/sharpen.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/sharpen.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/sharpen.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/sharpen.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ssao2.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ssao2.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ssao2.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ssao2.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/ssaoCombine.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/ssaoCombine.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/ssaoCombine.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/ssaoCombine.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/tonemap.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/tonemap.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/tonemap.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/tonemap.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/boundingBoxRenderer.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/boundingBoxRenderer.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/boundingBoxRenderer.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/boundingBoxRenderer.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/boundingBoxRenderer.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/boundingBoxRenderer.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/boundingBoxRenderer.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/boundingBoxRenderer.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/oitBackBlend.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/oitBackBlend.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/oitBackBlend.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/oitBackBlend.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/oitFinal.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/oitFinal.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/oitFinal.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/oitFinal.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/line.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/line.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/line.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/line.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/line.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/line.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/line.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/line.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblVoxelGrid2dArrayDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblVoxelGrid2dArrayDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelGrid2dArrayDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelGrid2dArrayDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblVoxelGrid3dDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblVoxelGrid3dDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelGrid3dDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelGrid3dDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/copyTexture3DLayerToTexture.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/copyTexture3DLayerToTexture.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/copyTexture3DLayerToTexture.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/copyTexture3DLayerToTexture.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblCombineVoxelGrids.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblCombineVoxelGrids.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblCombineVoxelGrids.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblCombineVoxelGrids.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblGenerateVoxelMip.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblGenerateVoxelMip.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblGenerateVoxelMip.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblGenerateVoxelMip.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblVoxelGrid.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblVoxelGrid.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblVoxelGrid.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblVoxelGrid.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelGrid.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelGrid.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelGrid.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelGrid.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblVoxelSlabDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblVoxelSlabDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblVoxelSlabDebug.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblVoxelSlabDebug.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelSlabDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelSlabDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelSlabDebug.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblVoxelSlabDebug.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblShadowDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblShadowDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblShadowVoxelTracing.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblShadowVoxelTracing.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowVoxelTracing.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowVoxelTracing.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblShadowSpatialBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblShadowSpatialBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowSpatialBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowSpatialBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblShadowAccumulation.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblShadowAccumulation.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowAccumulation.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowAccumulation.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/iblShadowGBufferDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/iblShadowGBufferDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowGBufferDebug.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/iblShadowGBufferDebug.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/outline.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/outline.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/outline.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/outline.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/outline.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/outline.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/outline.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/outline.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleDepth.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleDepth.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleDepth.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleDepth.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleDepth.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleDepth.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleDepth.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleDepth.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleThickness.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleThickness.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleThickness.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleThickness.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleThickness.vertex.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleThickness.vertex.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleThickness.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleThickness.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingBilateralBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingBilateralBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingBilateralBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingBilateralBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingStandardBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingStandardBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingStandardBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingStandardBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingRender.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingRender.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingRender.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingRender.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleDiffuse.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/fluidRenderingParticleDiffuse.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleDiffuse.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/fluidRenderingParticleDiffuse.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/bilateralBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/bilateralBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/bilateralBlurQuality.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/bilateralBlurQuality.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/rsmGlobalIllumination.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/rsmGlobalIllumination.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/Shaders/rsmFullGlobalIllumination.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/Shaders/rsmFullGlobalIllumination.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/bilateralBlur.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/bilateralBlur.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/bilateralBlurQuality.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/bilateralBlurQuality.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/rsmGlobalIllumination.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/rsmGlobalIllumination.fragment.js [app-ssr] (ecmascript)");
    });
});
}),
"[project]/node_modules/@babylonjs/core/ShadersWGSL/rsmFullGlobalIllumination.fragment.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@babylonjs/core/ShadersWGSL/rsmFullGlobalIllumination.fragment.js [app-ssr] (ecmascript)");
    });
});
}),

};