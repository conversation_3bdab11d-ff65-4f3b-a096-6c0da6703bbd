{"version": 3, "file": "ray.core.js", "sourceRoot": "", "sources": ["../../../../dev/core/src/Culling/ray.core.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,mCAAkC;AACpD,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,gCAA+B;AACrE,OAAO,EAAE,UAAU,EAAE,8BAA6B;AAClD,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAMlE,OAAO,EAAE,WAAW,EAAE,qCAAoC;AAC1D,OAAO,EAAE,WAAW,EAAE,kCAAiC;AAIvD,OAAO,EAAE,aAAa,EAAE,4BAA2B;AA+BnD;;GAEG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAA0B;IACvD,qBAAqB,EAAE,SAAS;CACnC,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,GAAG;IAKZ;;;;;;OAMG;IACH;IACI,mBAAmB;IACZ,MAAe;IACtB,gBAAgB;IACT,SAAkB;IACzB,2CAA2C;IACpC,SAAiB,MAAM,CAAC,SAAS;IACxC,+HAA+H;IACxH,UAAkB,OAAO;QANzB,WAAM,GAAN,MAAM,CAAS;QAEf,cAAS,GAAT,SAAS,CAAS;QAElB,WAAM,GAAN,MAAM,CAA2B;QAEjC,YAAO,GAAP,OAAO,CAAkB;IACjC,CAAC;IAEJ,UAAU;IAEV;;;OAGG;IACI,KAAK;QACR,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7E,CAAC;IAED;;;;;;;OAOG;IACI,mBAAmB,CAAC,OAA+B,EAAE,OAA+B,EAAE,uBAA+B,CAAC;QACzH,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC;QAC3J,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,EAAE,OAAO,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC;QAC3J,IAAI,CAAC,GAAG,GAAG,CAAC;QACZ,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;QAChC,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,GAAW,CAAC;QAChB,IAAI,IAAY,CAAC;QACjB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACpB,GAAG,GAAG,QAAQ,CAAC;YACnB,CAAC;YAED,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;gBACZ,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,IAAI,CAAC;YACf,CAAC;YAED,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAE3C,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACpB,GAAG,GAAG,QAAQ,CAAC;YACnB,CAAC;YAED,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;gBACZ,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,IAAI,CAAC;YACf,CAAC;YAED,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;YAC7B,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3C,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAE3C,IAAI,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACpB,GAAG,GAAG,QAAQ,CAAC;YACnB,CAAC;YAED,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;gBACZ,IAAI,GAAG,GAAG,CAAC;gBACX,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,IAAI,CAAC;YACf,CAAC;YAED,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAEnC,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC;gBACf,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAAC,GAA+B,EAAE,uBAA+B,CAAC;QAClF,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;IACpF,CAAC;IAED;;;;;OAKG;IACI,gBAAgB,CAAC,MAAqC,EAAE,uBAA+B,CAAC;QAC3F,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAC;QACpD,MAAM,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC;QAE3B,IAAI,IAAI,IAAI,EAAE,EAAE,CAAC;YACb,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;QAE9B,OAAO,IAAI,IAAI,EAAE,CAAC;IACtB,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,OAA+B,EAAE,OAA+B,EAAE,OAA+B;QACvH,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEhC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAChD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAErC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;QAEvB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAEzC,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QAE5C,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAEtC,MAAM,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QAEtD,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,6DAA6D;QAC7D,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC;QACnD,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,gBAAgB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,KAA2B;QAC9C,IAAI,QAAgB,CAAC;QACrB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1D,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,mBAAmB,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QAChB,CAAC;aAAM,CAAC;YACJ,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvD,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;YAC1C,IAAI,QAAQ,GAAG,GAAG,EAAE,CAAC;gBACjB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,EAAE,CAAC;oBAClC,OAAO,IAAI,CAAC;gBAChB,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,CAAC;gBACb,CAAC;YACL,CAAC;YAED,OAAO,QAAQ,CAAC;QACpB,CAAC;IACL,CAAC;IACD;;;;;OAKG;IACI,cAAc,CAAC,IAAY,EAAE,SAAiB,CAAC;QAClD,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,GAAG,CAAC,CAAC,CAAC;gBACP,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACR,OAAO,IAAI,CAAC;gBAChB,CAAC;gBACD,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7G,CAAC;YACD,KAAK,GAAG,CAAC,CAAC,CAAC;gBACP,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACR,OAAO,IAAI,CAAC;gBAChB,CAAC;gBACD,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7G,CAAC;YACD,KAAK,GAAG,CAAC,CAAC,CAAC;gBACP,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;oBACR,OAAO,IAAI,CAAC;gBAChB,CAAC;gBACD,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC7G,CAAC;YACD;gBACI,OAAO,IAAI,CAAC;QACpB,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,cAAc,CACjB,IAAiC,EACjC,SAAmB,EACnB,iBAA4C,EAC5C,gBAAgB,GAAG,KAAK,EACxB,UAAmB,EACnB,gBAAgB,GAAG,KAAK;QAExB,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,cAAc,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAEtC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;IACvH,CAAC;IAED;;;;;;OAMG;IACI,gBAAgB,CAAC,MAA0C,EAAE,SAAmB,EAAE,OAA4B;QACjH,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACJ,OAAO,GAAG,EAAE,CAAC;QACjB,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YAE3D,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3B,CAAC;QACL,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAEvC,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,mBAAmB,CAAC,YAAwC,EAAE,YAAwC;QAC1G,IAAI,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;YAChD,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;aAAM,IAAI,YAAY,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;YACvD,OAAO,CAAC,CAAC;QACb,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,CAAC;QACb,CAAC;IACL,CAAC;IAKD;;;;;;OAMG;IACH,mBAAmB,CAAC,IAA4B,EAAE,IAA4B,EAAE,SAAiB;QAC7F,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QACtB,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAChC,MAAM,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAE5B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAErB,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzB,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;QAC3C,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,cAAc;QAC3C,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,MAAM,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,MAAM,YAAY,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc;QAClD,IAAI,EAAU,EACV,EAAE,GAAG,YAAY,CAAC,CAAC,oCAAoC;QAC3D,IAAI,EAAU,EACV,EAAE,GAAG,YAAY,CAAC,CAAC,oCAAoC;QAE3D,wDAAwD;QACxD,IAAI,YAAY,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;YAC/B,gCAAgC;YAChC,EAAE,GAAG,GAAG,CAAC,CAAC,qCAAqC;YAC/C,EAAE,GAAG,GAAG,CAAC,CAAC,4CAA4C;YACtD,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,GAAG,CAAC,CAAC;QACX,CAAC;aAAM,CAAC;YACJ,+CAA+C;YAC/C,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACnB,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;gBACX,oCAAoC;gBACpC,EAAE,GAAG,GAAG,CAAC;gBACT,EAAE,GAAG,CAAC,CAAC;gBACP,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;iBAAM,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;gBACjB,oCAAoC;gBACpC,EAAE,GAAG,EAAE,CAAC;gBACR,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;gBACX,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;QACL,CAAC;QAED,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC;YACX,oCAAoC;YACpC,EAAE,GAAG,GAAG,CAAC;YACT,6BAA6B;YAC7B,IAAI,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC;gBACX,EAAE,GAAG,GAAG,CAAC;YACb,CAAC;iBAAM,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChB,EAAE,GAAG,EAAE,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACJ,EAAE,GAAG,CAAC,CAAC,CAAC;gBACR,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;QACL,CAAC;aAAM,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC;YACjB,oCAAoC;YACpC,EAAE,GAAG,EAAE,CAAC;YACR,6BAA6B;YAC7B,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC;gBACf,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;iBAAM,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACpB,EAAE,GAAG,EAAE,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACJ,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACZ,EAAE,GAAG,CAAC,CAAC;YACX,CAAC;QACL,CAAC;QACD,2CAA2C;QAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QACxD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QAExD,+CAA+C;QAC/C,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACtB,MAAM,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;QACtB,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAClB,MAAM,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACjC,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,oBAAoB;QAEhD,MAAM,aAAa,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,aAAa,EAAE,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC,6BAA6B;QAE9H,IAAI,aAAa,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,EAAE,CAAC;QACxB,CAAC;QACD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CACT,CAAS,EACT,CAAS,EACT,aAAqB,EACrB,cAAsB,EACtB,KAA4B,EAC5B,IAA2B,EAC3B,UAAiC,EACjC,uBAAgC,KAAK;QAErC,IAAI,oBAAoB,EAAE,CAAC;YACvB,mGAAmG;YACnG,2GAA2G;YAC3G,yGAAyG;YACzG,6EAA6E;YAC7E,uEAAuE;YACvE,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACnB,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YACjC,CAAC;YAED,GAAG,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,CAAC,gBAAgB,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;YAElH,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAChC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;YACtB,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACzF,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,UAAU;IACV;;;OAGG;IACI,MAAM,CAAC,IAAI;QACd,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,SAAS,CACnB,CAAS,EACT,CAAS,EACT,aAAqB,EACrB,cAAsB,EACtB,KAA4B,EAC5B,IAA2B,EAC3B,UAAiC;QAEjC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QAE1B,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACvF,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,eAAe,CAAC,MAAe,EAAE,GAAY,EAAE,QAA+B,MAAM,CAAC,gBAAgB;QAC/G,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnE,OAAO,GAAG,CAAC,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,iBAAiB,CAAC,MAAe,EAAE,GAAY,EAAE,MAAW,EAAE,QAA+B,MAAM,CAAC,gBAAgB;QAC9H,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC/B,MAAM,SAAS,GAAG,GAAG,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC5G,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAE7B,OAAO,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,SAAS,CAAC,GAAuB,EAAE,MAA6B;QAC1E,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnE,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAExC,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,cAAc,CAAC,GAAuB,EAAE,MAA6B,EAAE,MAAW;QAC5F,OAAO,CAAC,yBAAyB,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACrE,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;QACtE,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAE7B,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC;QAC7B,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QAEzB,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;YACtB,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;YACb,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;YACb,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;YACb,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC;QACzB,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;;;;;;;OASG;IACI,iBAAiB,CACpB,OAAc,EACd,OAAc,EACd,aAAqB,EACrB,cAAsB,EACtB,KAA4B,EAC5B,IAA2B,EAC3B,UAAiC;QAEjC,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACpC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClC,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACzC,MAAM,CAAC,MAAM,EAAE,CAAC;QAEhB,MAAM,MAAM,GAAG,WAAW,CAAC,iBAAiB,CAAC;QAC7C,MAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/C,gBAAgB,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACvD,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3D,gBAAgB,CAAC,CAAC,GAAG,MAAM,EAAE,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE1F,uFAAuF;QACvF,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;QACjH,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,yBAAyB,CAAC,gBAAgB,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QACtE,OAAO,CAAC,yBAAyB,CAAC,eAAe,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QAEpE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC/B,OAAO,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;IAC/B,CAAC;;AAloBuB,eAAW,GAAG,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;AACnD,eAAW,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;AA0VzB,aAAS,GAAG,UAAU,CAAC;AACvB,SAAK,GAAG,IAAI,CAAC;AAyShC;;;;;;;;;GASG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,KAAuB,EAAE,MAAwB,EAAE,eAAe,GAAG,KAAK;IAC3I,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE1B,qBAAqB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;IAE3E,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,qBAAqB,CACjC,KAAY,EACZ,CAAS,EACT,CAAS,EACT,KAAuB,EACvB,MAAW,EACX,MAAwB,EACxB,eAAe,GAAG,KAAK,EACvB,oBAAoB,GAAG,KAAK;IAE5B,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAEjC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,YAAa,CAAC,EAAE,CAAC;QAC7C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC;IACvC,MAAM,YAAY,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;IAC9C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,CAAC;IAEvG,6CAA6C;IAC7C,MAAM,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAC,uBAAuB,EAAE,CAAC;IACtD,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,CAAC;IACtB,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,YAAY,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;IAEhD,MAAM,CAAC,MAAM,CACT,CAAC,EACD,CAAC,EACD,KAAK,EACL,MAAM,EACN,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,EACvC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,EAAE,EAClE,MAAM,CAAC,mBAAmB,EAAE,EAC5B,oBAAoB,CACvB,CAAC;IACF,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,6BAA6B,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,MAAe;IAC7F,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;IAE1B,kCAAkC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAEhE,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,kCAAkC,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,MAAW,EAAE,MAAe;IAC/G,IAAI,CAAC,WAAW,EAAE,CAAC;QACf,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;IAEjC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,YAAa,CAAC,EAAE,CAAC;QAC7C,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC;IACvC,MAAM,YAAY,GAAG,MAAM,CAAC,eAAe,EAAE,CAAC;IAC9C,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,CAAC;IACvG,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;IAEnC,6CAA6C;IAC7C,MAAM,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAC,uBAAuB,EAAE,CAAC;IACtD,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,CAAC;IACtB,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,YAAY,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC;IAChD,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC;IACrF,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,SAAS,mBAAmB,CACxB,WAAkC,EAClC,WAAkE,EAClE,IAAkB,EAClB,KAAa,EACb,SAAmB,EACnB,gBAA0B,EAC1B,iBAA4C,EAC5C,gBAA0B;IAE1B,MAAM,GAAG,GAAG,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAE1D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;IAC7G,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;QAC/E,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,YAAY,CACjB,KAAY,EACZ,WAAkE,EAClE,SAAyB,EACzB,SAAmB,EACnB,gBAA0B,EAC1B,iBAA4C;IAE5C,IAAI,WAAW,GAAG,IAAI,CAAC;IAEvB,MAAM,2BAA2B,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,sBAAsB,KAAK,KAAK,CAAC,YAAY,CAAC,CAAC;IACrJ,MAAM,aAAa,GAAG,KAAK,CAAC,sBAAsB,IAAI,KAAK,CAAC,YAAY,CAAC;IACzE,MAAM,MAAM,GAAG,oBAAoB,CAAC,qBAAqB,IAAI,mBAAmB,CAAC;IAEjF,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;QACnE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAErC,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAClE,SAAS;QACb,CAAC;QAED,MAAM,YAAY,GAAG,2BAA2B,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACxF,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAEnE,IAAI,IAAI,CAAC,gBAAgB,IAAK,IAAa,CAAC,yBAAyB,EAAE,CAAC;YACpE,8EAA8E;YAC9E,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;YAC5F,IAAI,MAAM,EAAE,CAAC;gBACT,IAAI,gBAAgB,EAAE,CAAC;oBACnB,iEAAiE;oBACjE,OAAO,MAAM,CAAC;gBAClB,CAAC;gBACD,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,YAAY,GAAI,IAAa,CAAC,4BAA4B,EAAE,CAAC;gBACnE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;oBACvD,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBACvC,SAAS;oBACb,CAAC;oBACD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACvC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;oBAEvH,IAAI,MAAM,EAAE,CAAC;wBACT,WAAW,GAAG,MAAM,CAAC;wBACrB,WAAW,CAAC,iBAAiB,GAAG,KAAK,CAAC;wBAEtC,IAAI,SAAS,EAAE,CAAC;4BACZ,OAAO,WAAW,CAAC;wBACvB,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,iBAAiB,CAAC,CAAC;YAE7G,IAAI,MAAM,EAAE,CAAC;gBACT,WAAW,GAAG,MAAM,CAAC;gBAErB,IAAI,SAAS,EAAE,CAAC;oBACZ,OAAO,WAAW,CAAC;gBACvB,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,WAAW,IAAI,IAAI,WAAW,EAAE,CAAC;AAC5C,CAAC;AAED,SAAS,iBAAiB,CACtB,KAAY,EACZ,WAAkE,EAClE,SAAyB,EACzB,iBAA4C;IAE5C,IAAI,CAAC,WAAW,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,MAAM,YAAY,GAAkB,EAAE,CAAC;IACvC,MAAM,2BAA2B,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,sBAAsB,KAAK,KAAK,CAAC,YAAY,CAAC,CAAC;IACrJ,MAAM,aAAa,GAAG,KAAK,CAAC,sBAAsB,IAAI,KAAK,CAAC,YAAY,CAAC;IACzE,MAAM,MAAM,GAAG,oBAAoB,CAAC,qBAAqB,IAAI,mBAAmB,CAAC;IAEjF,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;QACnE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAErC,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,SAAS;YACb,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAClE,SAAS;QACb,CAAC;QAED,MAAM,YAAY,GAAG,2BAA2B,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACxF,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAEnE,IAAI,IAAI,CAAC,gBAAgB,IAAK,IAAa,CAAC,yBAAyB,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;YACrF,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,YAAY,GAAI,IAAa,CAAC,4BAA4B,EAAE,CAAC;gBACnE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;oBACvD,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;wBACvC,SAAS;oBACb,CAAC;oBACD,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;oBACvC,UAAU,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;oBAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;oBAEjG,IAAI,MAAM,EAAE,CAAC;wBACT,MAAM,CAAC,iBAAiB,GAAG,KAAK,CAAC;wBACjC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC9B,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAC;YAEvF,IAAI,MAAM,EAAE,CAAC;gBACT,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,YAAY,CAAC;AACxB,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,oBAAoB,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,SAAyB,EAAE,SAAmB,EAAE,MAAyB;IAC9I,IAAI,CAAC,WAAW,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,MAAM,MAAM,GAAG,YAAY,CACvB,KAAK,EACL,CAAC,KAAK,EAAE,EAAE;QACN,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACzB,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QACvC,CAAC;QAED,qBAAqB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,eAAe,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;QACjF,OAAO,KAAK,CAAC,eAAe,CAAC;IACjC,CAAC,EACD,SAAS,EACT,SAAS,EACT,IAAI,CACP,CAAC;IACF,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,GAAG,GAAG,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;IAClF,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,IAAI,CAChB,KAAY,EACZ,CAAS,EACT,CAAS,EACT,SAAyB,EACzB,SAAmB,EACnB,MAAyB,EACzB,iBAA4C,EAC5C,qBAAqB,GAAG,KAAK;IAE7B,MAAM,MAAM,GAAG,YAAY,CACvB,KAAK,EACL,CAAC,KAAK,EAAE,oBAAoB,EAAE,EAAE;QAC5B,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACzB,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QACvC,CAAC;QAED,qBAAqB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,eAAe,EAAE,MAAM,IAAI,IAAI,EAAE,KAAK,EAAE,oBAAoB,CAAC,CAAC;QAC9G,OAAO,KAAK,CAAC,eAAe,CAAC;IACjC,CAAC,EACD,SAAS,EACT,SAAS,EACT,KAAK,EACL,iBAAiB,CACpB,CAAC;IACF,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,GAAG,GAAG,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,IAAI,IAAI,CAAC,CAAC;IAClF,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,WAAW,CAAC,KAAY,EAAE,GAAQ,EAAE,SAAyB,EAAE,SAAmB,EAAE,iBAA4C;IAC5I,MAAM,MAAM,GAAG,YAAY,CACvB,KAAK,EACL,CAAC,KAAK,EAAE,EAAE;QACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC;YACnC,KAAK,CAAC,yBAAyB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACxD,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAEnD,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAChC,KAAK,CAAC,sBAAsB,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QAC9C,CAAC;QAED,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACvF,OAAO,KAAK,CAAC,sBAAsB,CAAC;IACxC,CAAC,EACD,SAAS,EACT,SAAS,EACT,KAAK,EACL,iBAAiB,CACpB,CAAC;IACF,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;IACrB,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,SAAS,CAAC,KAAY,EAAE,CAAS,EAAE,CAAS,EAAE,SAAyB,EAAE,MAAe,EAAE,iBAA4C;IAClJ,OAAO,iBAAiB,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,IAAI,IAAI,CAAC,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAC;AACnI,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAY,EAAE,GAAQ,EAAE,SAAyB,EAAE,iBAA4C;IAC5H,OAAO,iBAAiB,CACpB,KAAK,EACL,CAAC,KAAK,EAAE,EAAE;QACN,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,CAAC;YACnC,KAAK,CAAC,yBAAyB,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;QACxD,CAAC;QACD,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAEnD,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAChC,KAAK,CAAC,sBAAsB,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QAC9C,CAAC;QAED,GAAG,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACvF,OAAO,KAAK,CAAC,sBAAsB,CAAC;IACxC,CAAC,EACD,SAAS,EACT,iBAAiB,CACpB,CAAC;AACN,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,aAAa,CAAC,MAAc,EAAE,MAAM,GAAG,GAAG,EAAE,SAAkB,EAAE,MAAgB;IAC5F,OAAO,kBAAkB,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAClH,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,kBAAkB,CAAC,MAAc,EAAE,MAAW,EAAE,MAAM,GAAG,GAAG,EAAE,SAAkB,EAAE,MAAgB;IAC9G,IAAI,CAAC,SAAS,EAAE,CAAC;QACb,SAAS,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;IACxC,CAAC;IACD,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;IAEvB,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;SAAM,CAAC;QACJ,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IACD,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACtC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3C,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAC/D,OAAO,CAAC,cAAc,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;IAEvD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,gBAAgB,CAAC,UAAwB,EAAE,WAA0B;IACjF,IAAI,WAAW,EAAE,CAAC;QACd,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,MAAM,GAAG,GAAG,EAAE,SAAkB,EAAE,MAAgB;YAC9F,OAAO,kBAAkB,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QAChH,CAAC,CAAC;QAEF,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,MAAW,EAAE,MAAM,GAAG,GAAG,EAAE,SAAkB,EAAE,MAAgB;YAChH,OAAO,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;QACvE,CAAC,CAAC;IACN,CAAC;IAED,IAAI,CAAC,UAAU,EAAE,CAAC;QACd,OAAO;IACX,CAAC;IAED,aAAa,CAAC,mBAAmB,GAAG,IAAI,CAAC;IAEzC,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,CAAS,EAAE,CAAS,EAAE,KAAuB,EAAE,MAAwB,EAAE,eAAe,GAAG,KAAK;QAC9I,OAAO,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;IACxE,CAAC,CAAC;AACN,CAAC", "sourcesContent": ["import { <PERSON><PERSON><PERSON> } from \"core/Maths/math.constants\";\r\nimport { Matrix, TmpVectors, Vector3 } from \"core/Maths/math.vector\";\r\nimport { BuildArray } from \"core/Misc/arrayTools\";\r\nimport { IntersectionInfo } from \"../Collisions/intersectionInfo\";\r\nimport type { BoundingBox } from \"./boundingBox\";\r\nimport type { BoundingSphere } from \"./boundingSphere\";\r\nimport type { DeepImmutable, float, Nullable } from \"core/types\";\r\nimport type { Plane } from \"core/Maths/math.plane\";\r\nimport type { AbstractMesh } from \"core/Meshes/abstractMesh\";\r\nimport { PickingInfo } from \"core/Collisions/pickingInfo\";\r\nimport { EngineStore } from \"core/Engines/engineStore\";\r\nimport type { Scene } from \"core/scene\";\r\nimport type { Camera } from \"core/Cameras/camera\";\r\nimport type { Mesh } from \"core/Meshes/mesh\";\r\nimport { _ImportHelper } from \"core/import.helper\";\r\n\r\n/**\r\n * Type used to define predicate for selecting meshes and instances (if exist)\r\n */\r\nexport type MeshPredicate = (mesh: AbstractMesh, thinInstanceIndex: number) => boolean;\r\n\r\n/**\r\n * Type used to define predicate used to select faces when a mesh intersection is detected\r\n */\r\nexport type TrianglePickingPredicate = (p0: Vector3, p1: Vector3, p2: Vector3, ray: Ray, i0: number, i1: number, i2: number) => boolean;\r\n\r\n/**\r\n * This class allows user to customize internal picking mechanism\r\n */\r\nexport interface IPickingCustomization {\r\n    /**\r\n     * Predicate to select faces when a mesh intersection is detected\r\n     */\r\n    internalPickerForMesh?: (\r\n        pickingInfo: Nullable<PickingInfo>,\r\n        rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n        mesh: AbstractMesh,\r\n        world: Matrix,\r\n        fastCheck?: boolean,\r\n        onlyBoundingInfo?: boolean,\r\n        trianglePredicate?: TrianglePickingPredicate,\r\n        skipBoundingInfo?: boolean\r\n    ) => PickingInfo;\r\n}\r\n\r\n/**\r\n * Use this object to customize mesh picking behavior\r\n */\r\nexport const PickingCustomization: IPickingCustomization = {\r\n    internalPickerForMesh: undefined,\r\n};\r\n\r\n/**\r\n * Class representing a ray with position and direction\r\n */\r\nexport class Ray {\r\n    private static readonly _TmpVector3 = BuildArray(6, Vector3.Zero);\r\n    private static _RayDistant = Ray.Zero();\r\n    private _tmpRay: Ray;\r\n\r\n    /**\r\n     * Creates a new ray\r\n     * @param origin origin point\r\n     * @param direction direction\r\n     * @param length length of the ray\r\n     * @param epsilon The epsilon value to use when calculating the ray/triangle intersection (default: Epsilon from math constants)\r\n     */\r\n    constructor(\r\n        /** origin point */\r\n        public origin: Vector3,\r\n        /** direction */\r\n        public direction: Vector3,\r\n        /** [Number.MAX_VALUE] length of the ray */\r\n        public length: number = Number.MAX_VALUE,\r\n        /** [Epsilon] The epsilon value to use when calculating the ray/triangle intersection (default: Epsilon from math constants) */\r\n        public epsilon: number = Epsilon\r\n    ) {}\r\n\r\n    // Methods\r\n\r\n    /**\r\n     * Clone the current ray\r\n     * @returns a new ray\r\n     */\r\n    public clone(): Ray {\r\n        return new Ray(this.origin.clone(), this.direction.clone(), this.length);\r\n    }\r\n\r\n    /**\r\n     * Checks if the ray intersects a box\r\n     * This does not account for the ray length by design to improve perfs.\r\n     * @param minimum bound of the box\r\n     * @param maximum bound of the box\r\n     * @param intersectionTreshold extra extend to be added to the box in all direction\r\n     * @returns if the box was hit\r\n     */\r\n    public intersectsBoxMinMax(minimum: DeepImmutable<Vector3>, maximum: DeepImmutable<Vector3>, intersectionTreshold: number = 0): boolean {\r\n        const newMinimum = Ray._TmpVector3[0].copyFromFloats(minimum.x - intersectionTreshold, minimum.y - intersectionTreshold, minimum.z - intersectionTreshold);\r\n        const newMaximum = Ray._TmpVector3[1].copyFromFloats(maximum.x + intersectionTreshold, maximum.y + intersectionTreshold, maximum.z + intersectionTreshold);\r\n        let d = 0.0;\r\n        let maxValue = Number.MAX_VALUE;\r\n        let inv: number;\r\n        let min: number;\r\n        let max: number;\r\n        let temp: number;\r\n        if (Math.abs(this.direction.x) < 0.0000001) {\r\n            if (this.origin.x < newMinimum.x || this.origin.x > newMaximum.x) {\r\n                return false;\r\n            }\r\n        } else {\r\n            inv = 1.0 / this.direction.x;\r\n            min = (newMinimum.x - this.origin.x) * inv;\r\n            max = (newMaximum.x - this.origin.x) * inv;\r\n            if (max === -Infinity) {\r\n                max = Infinity;\r\n            }\r\n\r\n            if (min > max) {\r\n                temp = min;\r\n                min = max;\r\n                max = temp;\r\n            }\r\n\r\n            d = Math.max(min, d);\r\n            maxValue = Math.min(max, maxValue);\r\n\r\n            if (d > maxValue) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (Math.abs(this.direction.y) < 0.0000001) {\r\n            if (this.origin.y < newMinimum.y || this.origin.y > newMaximum.y) {\r\n                return false;\r\n            }\r\n        } else {\r\n            inv = 1.0 / this.direction.y;\r\n            min = (newMinimum.y - this.origin.y) * inv;\r\n            max = (newMaximum.y - this.origin.y) * inv;\r\n\r\n            if (max === -Infinity) {\r\n                max = Infinity;\r\n            }\r\n\r\n            if (min > max) {\r\n                temp = min;\r\n                min = max;\r\n                max = temp;\r\n            }\r\n\r\n            d = Math.max(min, d);\r\n            maxValue = Math.min(max, maxValue);\r\n\r\n            if (d > maxValue) {\r\n                return false;\r\n            }\r\n        }\r\n\r\n        if (Math.abs(this.direction.z) < 0.0000001) {\r\n            if (this.origin.z < newMinimum.z || this.origin.z > newMaximum.z) {\r\n                return false;\r\n            }\r\n        } else {\r\n            inv = 1.0 / this.direction.z;\r\n            min = (newMinimum.z - this.origin.z) * inv;\r\n            max = (newMaximum.z - this.origin.z) * inv;\r\n\r\n            if (max === -Infinity) {\r\n                max = Infinity;\r\n            }\r\n\r\n            if (min > max) {\r\n                temp = min;\r\n                min = max;\r\n                max = temp;\r\n            }\r\n\r\n            d = Math.max(min, d);\r\n            maxValue = Math.min(max, maxValue);\r\n\r\n            if (d > maxValue) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Checks if the ray intersects a box\r\n     * This does not account for the ray length by design to improve perfs.\r\n     * @param box the bounding box to check\r\n     * @param intersectionTreshold extra extend to be added to the BoundingBox in all direction\r\n     * @returns if the box was hit\r\n     */\r\n    public intersectsBox(box: DeepImmutable<BoundingBox>, intersectionTreshold: number = 0): boolean {\r\n        return this.intersectsBoxMinMax(box.minimum, box.maximum, intersectionTreshold);\r\n    }\r\n\r\n    /**\r\n     * If the ray hits a sphere\r\n     * @param sphere the bounding sphere to check\r\n     * @param intersectionTreshold extra extend to be added to the BoundingSphere in all direction\r\n     * @returns true if it hits the sphere\r\n     */\r\n    public intersectsSphere(sphere: DeepImmutable<BoundingSphere>, intersectionTreshold: number = 0): boolean {\r\n        const x = sphere.center.x - this.origin.x;\r\n        const y = sphere.center.y - this.origin.y;\r\n        const z = sphere.center.z - this.origin.z;\r\n        const pyth = x * x + y * y + z * z;\r\n        const radius = sphere.radius + intersectionTreshold;\r\n        const rr = radius * radius;\r\n\r\n        if (pyth <= rr) {\r\n            return true;\r\n        }\r\n\r\n        const dot = x * this.direction.x + y * this.direction.y + z * this.direction.z;\r\n        if (dot < 0.0) {\r\n            return false;\r\n        }\r\n\r\n        const temp = pyth - dot * dot;\r\n\r\n        return temp <= rr;\r\n    }\r\n\r\n    /**\r\n     * If the ray hits a triange\r\n     * @param vertex0 triangle vertex\r\n     * @param vertex1 triangle vertex\r\n     * @param vertex2 triangle vertex\r\n     * @returns intersection information if hit\r\n     */\r\n    public intersectsTriangle(vertex0: DeepImmutable<Vector3>, vertex1: DeepImmutable<Vector3>, vertex2: DeepImmutable<Vector3>): Nullable<IntersectionInfo> {\r\n        const edge1 = Ray._TmpVector3[0];\r\n        const edge2 = Ray._TmpVector3[1];\r\n        const pvec = Ray._TmpVector3[2];\r\n        const tvec = Ray._TmpVector3[3];\r\n        const qvec = Ray._TmpVector3[4];\r\n\r\n        vertex1.subtractToRef(vertex0, edge1);\r\n        vertex2.subtractToRef(vertex0, edge2);\r\n        Vector3.CrossToRef(this.direction, edge2, pvec);\r\n        const det = Vector3.Dot(edge1, pvec);\r\n\r\n        if (det === 0) {\r\n            return null;\r\n        }\r\n\r\n        const invdet = 1 / det;\r\n\r\n        this.origin.subtractToRef(vertex0, tvec);\r\n\r\n        const bv = Vector3.Dot(tvec, pvec) * invdet;\r\n\r\n        if (bv < -this.epsilon || bv > 1.0 + this.epsilon) {\r\n            return null;\r\n        }\r\n\r\n        Vector3.CrossToRef(tvec, edge1, qvec);\r\n\r\n        const bw = Vector3.Dot(this.direction, qvec) * invdet;\r\n\r\n        if (bw < -this.epsilon || bv + bw > 1.0 + this.epsilon) {\r\n            return null;\r\n        }\r\n\r\n        //check if the distance is longer than the predefined length.\r\n        const distance = Vector3.Dot(edge2, qvec) * invdet;\r\n        if (distance > this.length) {\r\n            return null;\r\n        }\r\n\r\n        return new IntersectionInfo(1 - bv - bw, bv, distance);\r\n    }\r\n\r\n    /**\r\n     * Checks if ray intersects a plane\r\n     * @param plane the plane to check\r\n     * @returns the distance away it was hit\r\n     */\r\n    public intersectsPlane(plane: DeepImmutable<Plane>): Nullable<number> {\r\n        let distance: number;\r\n        const result1 = Vector3.Dot(plane.normal, this.direction);\r\n        if (Math.abs(result1) < 9.99999997475243e-7) {\r\n            return null;\r\n        } else {\r\n            const result2 = Vector3.Dot(plane.normal, this.origin);\r\n            distance = (-plane.d - result2) / result1;\r\n            if (distance < 0.0) {\r\n                if (distance < -9.99999997475243e-7) {\r\n                    return null;\r\n                } else {\r\n                    return 0;\r\n                }\r\n            }\r\n\r\n            return distance;\r\n        }\r\n    }\r\n    /**\r\n     * Calculate the intercept of a ray on a given axis\r\n     * @param axis to check 'x' | 'y' | 'z'\r\n     * @param offset from axis interception (i.e. an offset of 1y is intercepted above ground)\r\n     * @returns a vector containing the coordinates where 'axis' is equal to zero (else offset), or null if there is no intercept.\r\n     */\r\n    public intersectsAxis(axis: string, offset: number = 0): Nullable<Vector3> {\r\n        switch (axis) {\r\n            case \"y\": {\r\n                const t = (this.origin.y - offset) / this.direction.y;\r\n                if (t > 0) {\r\n                    return null;\r\n                }\r\n                return new Vector3(this.origin.x + this.direction.x * -t, offset, this.origin.z + this.direction.z * -t);\r\n            }\r\n            case \"x\": {\r\n                const t = (this.origin.x - offset) / this.direction.x;\r\n                if (t > 0) {\r\n                    return null;\r\n                }\r\n                return new Vector3(offset, this.origin.y + this.direction.y * -t, this.origin.z + this.direction.z * -t);\r\n            }\r\n            case \"z\": {\r\n                const t = (this.origin.z - offset) / this.direction.z;\r\n                if (t > 0) {\r\n                    return null;\r\n                }\r\n                return new Vector3(this.origin.x + this.direction.x * -t, this.origin.y + this.direction.y * -t, offset);\r\n            }\r\n            default:\r\n                return null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Checks if ray intersects a mesh. The ray is defined in WORLD space. A mesh triangle can be picked both from its front and back sides,\r\n     * irrespective of orientation.\r\n     * @param mesh the mesh to check\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n     * @param onlyBoundingInfo defines a boolean indicating if picking should only happen using bounding info (false by default)\r\n     * @param worldToUse defines the world matrix to use to get the world coordinate of the intersection point\r\n     * @param skipBoundingInfo a boolean indicating if we should skip the bounding info check\r\n     * @returns picking info of the intersection\r\n     */\r\n    public intersectsMesh(\r\n        mesh: DeepImmutable<AbstractMesh>,\r\n        fastCheck?: boolean,\r\n        trianglePredicate?: TrianglePickingPredicate,\r\n        onlyBoundingInfo = false,\r\n        worldToUse?: Matrix,\r\n        skipBoundingInfo = false\r\n    ): PickingInfo {\r\n        const tm = TmpVectors.Matrix[0];\r\n\r\n        mesh.getWorldMatrix().invertToRef(tm);\r\n\r\n        if (this._tmpRay) {\r\n            Ray.TransformToRef(this, tm, this._tmpRay);\r\n        } else {\r\n            this._tmpRay = Ray.Transform(this, tm);\r\n        }\r\n\r\n        return mesh.intersects(this._tmpRay, fastCheck, trianglePredicate, onlyBoundingInfo, worldToUse, skipBoundingInfo);\r\n    }\r\n\r\n    /**\r\n     * Checks if ray intersects a mesh\r\n     * @param meshes the meshes to check\r\n     * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n     * @param results array to store result in\r\n     * @returns Array of picking infos\r\n     */\r\n    public intersectsMeshes(meshes: Array<DeepImmutable<AbstractMesh>>, fastCheck?: boolean, results?: Array<PickingInfo>): Array<PickingInfo> {\r\n        if (results) {\r\n            results.length = 0;\r\n        } else {\r\n            results = [];\r\n        }\r\n\r\n        for (let i = 0; i < meshes.length; i++) {\r\n            const pickInfo = this.intersectsMesh(meshes[i], fastCheck);\r\n\r\n            if (pickInfo.hit) {\r\n                results.push(pickInfo);\r\n            }\r\n        }\r\n\r\n        results.sort(this._comparePickingInfo);\r\n\r\n        return results;\r\n    }\r\n\r\n    private _comparePickingInfo(pickingInfoA: DeepImmutable<PickingInfo>, pickingInfoB: DeepImmutable<PickingInfo>): number {\r\n        if (pickingInfoA.distance < pickingInfoB.distance) {\r\n            return -1;\r\n        } else if (pickingInfoA.distance > pickingInfoB.distance) {\r\n            return 1;\r\n        } else {\r\n            return 0;\r\n        }\r\n    }\r\n\r\n    private static _Smallnum = 0.00000001;\r\n    private static _Rayl = 10e8;\r\n\r\n    /**\r\n     * Intersection test between the ray and a given segment within a given tolerance (threshold)\r\n     * @param sega the first point of the segment to test the intersection against\r\n     * @param segb the second point of the segment to test the intersection against\r\n     * @param threshold the tolerance margin, if the ray doesn't intersect the segment but is close to the given threshold, the intersection is successful\r\n     * @returns the distance from the ray origin to the intersection point if there's intersection, or -1 if there's no intersection\r\n     */\r\n    intersectionSegment(sega: DeepImmutable<Vector3>, segb: DeepImmutable<Vector3>, threshold: number): number {\r\n        const o = this.origin;\r\n        const u = TmpVectors.Vector3[0];\r\n        const rsegb = TmpVectors.Vector3[1];\r\n        const v = TmpVectors.Vector3[2];\r\n        const w = TmpVectors.Vector3[3];\r\n\r\n        segb.subtractToRef(sega, u);\r\n\r\n        this.direction.scaleToRef(Ray._Rayl, v);\r\n        o.addToRef(v, rsegb);\r\n\r\n        sega.subtractToRef(o, w);\r\n\r\n        const a = Vector3.Dot(u, u); // always >= 0\r\n        const b = Vector3.Dot(u, v);\r\n        const c = Vector3.Dot(v, v); // always >= 0\r\n        const d = Vector3.Dot(u, w);\r\n        const e = Vector3.Dot(v, w);\r\n        const discriminant = a * c - b * b; // always >= 0\r\n        let sN: number,\r\n            sD = discriminant; // sc = sN / sD, default sD = D >= 0\r\n        let tN: number,\r\n            tD = discriminant; // tc = tN / tD, default tD = D >= 0\r\n\r\n        // compute the line parameters of the two closest points\r\n        if (discriminant < Ray._Smallnum) {\r\n            // the lines are almost parallel\r\n            sN = 0.0; // force using point P0 on segment S1\r\n            sD = 1.0; // to prevent possible division by 0.0 later\r\n            tN = e;\r\n            tD = c;\r\n        } else {\r\n            // get the closest points on the infinite lines\r\n            sN = b * e - c * d;\r\n            tN = a * e - b * d;\r\n            if (sN < 0.0) {\r\n                // sc < 0 => the s=0 edge is visible\r\n                sN = 0.0;\r\n                tN = e;\r\n                tD = c;\r\n            } else if (sN > sD) {\r\n                // sc > 1 => the s=1 edge is visible\r\n                sN = sD;\r\n                tN = e + b;\r\n                tD = c;\r\n            }\r\n        }\r\n\r\n        if (tN < 0.0) {\r\n            // tc < 0 => the t=0 edge is visible\r\n            tN = 0.0;\r\n            // recompute sc for this edge\r\n            if (-d < 0.0) {\r\n                sN = 0.0;\r\n            } else if (-d > a) {\r\n                sN = sD;\r\n            } else {\r\n                sN = -d;\r\n                sD = a;\r\n            }\r\n        } else if (tN > tD) {\r\n            // tc > 1 => the t=1 edge is visible\r\n            tN = tD;\r\n            // recompute sc for this edge\r\n            if (-d + b < 0.0) {\r\n                sN = 0;\r\n            } else if (-d + b > a) {\r\n                sN = sD;\r\n            } else {\r\n                sN = -d + b;\r\n                sD = a;\r\n            }\r\n        }\r\n        // finally do the division to get sc and tc\r\n        const sc = Math.abs(sN) < Ray._Smallnum ? 0.0 : sN / sD;\r\n        const tc = Math.abs(tN) < Ray._Smallnum ? 0.0 : tN / tD;\r\n\r\n        // get the difference of the two closest points\r\n        const qtc = TmpVectors.Vector3[4];\r\n        v.scaleToRef(tc, qtc);\r\n        const qsc = TmpVectors.Vector3[5];\r\n        u.scaleToRef(sc, qsc);\r\n        qsc.addInPlace(w);\r\n        const dP = TmpVectors.Vector3[6];\r\n        qsc.subtractToRef(qtc, dP); // = S1(sc) - S2(tc)\r\n\r\n        const isIntersected = tc > 0 && tc <= this.length && dP.lengthSquared() < threshold * threshold; // return intersection result\r\n\r\n        if (isIntersected) {\r\n            return qsc.length();\r\n        }\r\n        return -1;\r\n    }\r\n\r\n    /**\r\n     * Update the ray from viewport position\r\n     * @param x position\r\n     * @param y y position\r\n     * @param viewportWidth viewport width\r\n     * @param viewportHeight viewport height\r\n     * @param world world matrix\r\n     * @param view view matrix\r\n     * @param projection projection matrix\r\n     * @param enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\r\n     * @returns this ray updated\r\n     */\r\n    public update(\r\n        x: number,\r\n        y: number,\r\n        viewportWidth: number,\r\n        viewportHeight: number,\r\n        world: DeepImmutable<Matrix>,\r\n        view: DeepImmutable<Matrix>,\r\n        projection: DeepImmutable<Matrix>,\r\n        enableDistantPicking: boolean = false\r\n    ): Ray {\r\n        if (enableDistantPicking) {\r\n            // With world matrices having great values (like 8000000000 on 1 or more scaling or position axis),\r\n            // multiplying view/projection/world and doing invert will result in loss of float precision in the matrix.\r\n            // One way to fix it is to compute the ray with world at identity then transform the ray in object space.\r\n            // This is slower (2 matrix inverts instead of 1) but precision is preserved.\r\n            // This is hidden behind `EnableDistantPicking` flag (default is false)\r\n            if (!Ray._RayDistant) {\r\n                Ray._RayDistant = Ray.Zero();\r\n            }\r\n\r\n            Ray._RayDistant.unprojectRayToRef(x, y, viewportWidth, viewportHeight, Matrix.IdentityReadOnly, view, projection);\r\n\r\n            const tm = TmpVectors.Matrix[0];\r\n            world.invertToRef(tm);\r\n            Ray.TransformToRef(Ray._RayDistant, tm, this);\r\n        } else {\r\n            this.unprojectRayToRef(x, y, viewportWidth, viewportHeight, world, view, projection);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Creates a ray with origin and direction of 0,0,0\r\n     * @returns the new ray\r\n     */\r\n    public static Zero(): Ray {\r\n        return new Ray(Vector3.Zero(), Vector3.Zero());\r\n    }\r\n\r\n    /**\r\n     * Creates a new ray from screen space and viewport\r\n     * @param x position\r\n     * @param y y position\r\n     * @param viewportWidth viewport width\r\n     * @param viewportHeight viewport height\r\n     * @param world world matrix\r\n     * @param view view matrix\r\n     * @param projection projection matrix\r\n     * @returns new ray\r\n     */\r\n    public static CreateNew(\r\n        x: number,\r\n        y: number,\r\n        viewportWidth: number,\r\n        viewportHeight: number,\r\n        world: DeepImmutable<Matrix>,\r\n        view: DeepImmutable<Matrix>,\r\n        projection: DeepImmutable<Matrix>\r\n    ): Ray {\r\n        const result = Ray.Zero();\r\n\r\n        return result.update(x, y, viewportWidth, viewportHeight, world, view, projection);\r\n    }\r\n\r\n    /**\r\n     * Function will create a new transformed ray starting from origin and ending at the end point. Ray's length will be set, and ray will be\r\n     * transformed to the given world matrix.\r\n     * @param origin The origin point\r\n     * @param end The end point\r\n     * @param world a matrix to transform the ray to. Default is the identity matrix.\r\n     * @returns the new ray\r\n     */\r\n    public static CreateNewFromTo(origin: Vector3, end: Vector3, world: DeepImmutable<Matrix> = Matrix.IdentityReadOnly): Ray {\r\n        const result = new Ray(new Vector3(0, 0, 0), new Vector3(0, 0, 0));\r\n        return Ray.CreateFromToToRef(origin, end, result, world);\r\n    }\r\n\r\n    /**\r\n     * Function will update a transformed ray starting from origin and ending at the end point. Ray's length will be set, and ray will be\r\n     * transformed to the given world matrix.\r\n     * @param origin The origin point\r\n     * @param end The end point\r\n     * @param result the object to store the result\r\n     * @param world a matrix to transform the ray to. Default is the identity matrix.\r\n     * @returns the ref ray\r\n     */\r\n    public static CreateFromToToRef(origin: Vector3, end: Vector3, result: Ray, world: DeepImmutable<Matrix> = Matrix.IdentityReadOnly): Ray {\r\n        result.origin.copyFrom(origin);\r\n        const direction = end.subtractToRef(origin, result.direction);\r\n        const length = Math.sqrt(direction.x * direction.x + direction.y * direction.y + direction.z * direction.z);\r\n        result.length = length;\r\n        result.direction.normalize();\r\n\r\n        return Ray.TransformToRef(result, world, result);\r\n    }\r\n\r\n    /**\r\n     * Transforms a ray by a matrix\r\n     * @param ray ray to transform\r\n     * @param matrix matrix to apply\r\n     * @returns the resulting new ray\r\n     */\r\n    public static Transform(ray: DeepImmutable<Ray>, matrix: DeepImmutable<Matrix>): Ray {\r\n        const result = new Ray(new Vector3(0, 0, 0), new Vector3(0, 0, 0));\r\n        Ray.TransformToRef(ray, matrix, result);\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Transforms a ray by a matrix\r\n     * @param ray ray to transform\r\n     * @param matrix matrix to apply\r\n     * @param result ray to store result in\r\n     * @returns the updated result ray\r\n     */\r\n    public static TransformToRef(ray: DeepImmutable<Ray>, matrix: DeepImmutable<Matrix>, result: Ray): Ray {\r\n        Vector3.TransformCoordinatesToRef(ray.origin, matrix, result.origin);\r\n        Vector3.TransformNormalToRef(ray.direction, matrix, result.direction);\r\n        result.length = ray.length;\r\n        result.epsilon = ray.epsilon;\r\n\r\n        const dir = result.direction;\r\n        const len = dir.length();\r\n\r\n        if (!(len === 0 || len === 1)) {\r\n            const num = 1.0 / len;\r\n            dir.x *= num;\r\n            dir.y *= num;\r\n            dir.z *= num;\r\n            result.length *= len;\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Unproject a ray from screen space to object space\r\n     * @param sourceX defines the screen space x coordinate to use\r\n     * @param sourceY defines the screen space y coordinate to use\r\n     * @param viewportWidth defines the current width of the viewport\r\n     * @param viewportHeight defines the current height of the viewport\r\n     * @param world defines the world matrix to use (can be set to Identity to go to world space)\r\n     * @param view defines the view matrix to use\r\n     * @param projection defines the projection matrix to use\r\n     */\r\n    public unprojectRayToRef(\r\n        sourceX: float,\r\n        sourceY: float,\r\n        viewportWidth: number,\r\n        viewportHeight: number,\r\n        world: DeepImmutable<Matrix>,\r\n        view: DeepImmutable<Matrix>,\r\n        projection: DeepImmutable<Matrix>\r\n    ): void {\r\n        const matrix = TmpVectors.Matrix[0];\r\n        world.multiplyToRef(view, matrix);\r\n        matrix.multiplyToRef(projection, matrix);\r\n        matrix.invert();\r\n\r\n        const engine = EngineStore.LastCreatedEngine;\r\n        const nearScreenSource = TmpVectors.Vector3[0];\r\n        nearScreenSource.x = (sourceX / viewportWidth) * 2 - 1;\r\n        nearScreenSource.y = -((sourceY / viewportHeight) * 2 - 1);\r\n        nearScreenSource.z = engine?.useReverseDepthBuffer ? 1 : engine?.isNDCHalfZRange ? 0 : -1;\r\n\r\n        // far Z need to be close but < to 1 or camera projection matrix with maxZ = 0 will NaN\r\n        const farScreenSource = TmpVectors.Vector3[1].copyFromFloats(nearScreenSource.x, nearScreenSource.y, 1.0 - 1e-8);\r\n        const nearVec3 = TmpVectors.Vector3[2];\r\n        const farVec3 = TmpVectors.Vector3[3];\r\n        Vector3.TransformCoordinatesToRef(nearScreenSource, matrix, nearVec3);\r\n        Vector3.TransformCoordinatesToRef(farScreenSource, matrix, farVec3);\r\n\r\n        this.origin.copyFrom(nearVec3);\r\n        farVec3.subtractToRef(nearVec3, this.direction);\r\n        this.direction.normalize();\r\n    }\r\n}\r\n\r\n/**\r\n * Creates a ray that can be used to pick in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x defines the x coordinate of the origin (on-screen)\r\n * @param y defines the y coordinate of the origin (on-screen)\r\n * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\r\n * @param camera defines the camera to use for the picking\r\n * @param cameraViewSpace defines if picking will be done in view space (false by default)\r\n * @returns a Ray\r\n */\r\nexport function CreatePickingRay(scene: Scene, x: number, y: number, world: Nullable<Matrix>, camera: Nullable<Camera>, cameraViewSpace = false): Ray {\r\n    const result = Ray.Zero();\r\n\r\n    CreatePickingRayToRef(scene, x, y, world, result, camera, cameraViewSpace);\r\n\r\n    return result;\r\n}\r\n\r\n/**\r\n * Creates a ray that can be used to pick in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x defines the x coordinate of the origin (on-screen)\r\n * @param y defines the y coordinate of the origin (on-screen)\r\n * @param world defines the world matrix to use if you want to pick in object space (instead of world space)\r\n * @param result defines the ray where to store the picking ray\r\n * @param camera defines the camera to use for the picking\r\n * @param cameraViewSpace defines if picking will be done in view space (false by default)\r\n * @param enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\r\n * @returns the current scene\r\n */\r\nexport function CreatePickingRayToRef(\r\n    scene: Scene,\r\n    x: number,\r\n    y: number,\r\n    world: Nullable<Matrix>,\r\n    result: Ray,\r\n    camera: Nullable<Camera>,\r\n    cameraViewSpace = false,\r\n    enableDistantPicking = false\r\n): Scene {\r\n    const engine = scene.getEngine();\r\n\r\n    if (!camera && !(camera = scene.activeCamera!)) {\r\n        return scene;\r\n    }\r\n\r\n    const cameraViewport = camera.viewport;\r\n    const renderHeight = engine.getRenderHeight();\r\n    const { x: vx, y: vy, width, height } = cameraViewport.toGlobal(engine.getRenderWidth(), renderHeight);\r\n\r\n    // Moving coordinates to local viewport world\r\n    const levelInv = 1 / engine.getHardwareScalingLevel();\r\n    x = x * levelInv - vx;\r\n    y = y * levelInv - (renderHeight - vy - height);\r\n\r\n    result.update(\r\n        x,\r\n        y,\r\n        width,\r\n        height,\r\n        world ? world : Matrix.IdentityReadOnly,\r\n        cameraViewSpace ? Matrix.IdentityReadOnly : camera.getViewMatrix(),\r\n        camera.getProjectionMatrix(),\r\n        enableDistantPicking\r\n    );\r\n    return scene;\r\n}\r\n\r\n/**\r\n * Creates a ray that can be used to pick in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x defines the x coordinate of the origin (on-screen)\r\n * @param y defines the y coordinate of the origin (on-screen)\r\n * @param camera defines the camera to use for the picking\r\n * @returns a Ray\r\n */\r\nexport function CreatePickingRayInCameraSpace(scene: Scene, x: number, y: number, camera?: Camera): Ray {\r\n    const result = Ray.Zero();\r\n\r\n    CreatePickingRayInCameraSpaceToRef(scene, x, y, result, camera);\r\n\r\n    return result;\r\n}\r\n\r\n/**\r\n * Creates a ray that can be used to pick in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x defines the x coordinate of the origin (on-screen)\r\n * @param y defines the y coordinate of the origin (on-screen)\r\n * @param result defines the ray where to store the picking ray\r\n * @param camera defines the camera to use for the picking\r\n * @returns the current scene\r\n */\r\nexport function CreatePickingRayInCameraSpaceToRef(scene: Scene, x: number, y: number, result: Ray, camera?: Camera): Scene {\r\n    if (!PickingInfo) {\r\n        return scene;\r\n    }\r\n\r\n    const engine = scene.getEngine();\r\n\r\n    if (!camera && !(camera = scene.activeCamera!)) {\r\n        throw new Error(\"Active camera not set\");\r\n    }\r\n\r\n    const cameraViewport = camera.viewport;\r\n    const renderHeight = engine.getRenderHeight();\r\n    const { x: vx, y: vy, width, height } = cameraViewport.toGlobal(engine.getRenderWidth(), renderHeight);\r\n    const identity = Matrix.Identity();\r\n\r\n    // Moving coordinates to local viewport world\r\n    const levelInv = 1 / engine.getHardwareScalingLevel();\r\n    x = x * levelInv - vx;\r\n    y = y * levelInv - (renderHeight - vy - height);\r\n    result.update(x, y, width, height, identity, identity, camera.getProjectionMatrix());\r\n    return scene;\r\n}\r\n\r\nfunction InternalPickForMesh(\r\n    pickingInfo: Nullable<PickingInfo>,\r\n    rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n    mesh: AbstractMesh,\r\n    world: Matrix,\r\n    fastCheck?: boolean,\r\n    onlyBoundingInfo?: boolean,\r\n    trianglePredicate?: TrianglePickingPredicate,\r\n    skipBoundingInfo?: boolean\r\n) {\r\n    const ray = rayFunction(world, mesh.enableDistantPicking);\r\n\r\n    const result = mesh.intersects(ray, fastCheck, trianglePredicate, onlyBoundingInfo, world, skipBoundingInfo);\r\n    if (!result || !result.hit) {\r\n        return null;\r\n    }\r\n\r\n    if (!fastCheck && pickingInfo != null && result.distance >= pickingInfo.distance) {\r\n        return null;\r\n    }\r\n\r\n    return result;\r\n}\r\n\r\nfunction InternalPick(\r\n    scene: Scene,\r\n    rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n    predicate?: MeshPredicate,\r\n    fastCheck?: boolean,\r\n    onlyBoundingInfo?: boolean,\r\n    trianglePredicate?: TrianglePickingPredicate\r\n): PickingInfo {\r\n    let pickingInfo = null;\r\n\r\n    const computeWorldMatrixForCamera = !!(scene.activeCameras && scene.activeCameras.length > 1 && scene.cameraToUseForPointers !== scene.activeCamera);\r\n    const currentCamera = scene.cameraToUseForPointers || scene.activeCamera;\r\n    const picker = PickingCustomization.internalPickerForMesh || InternalPickForMesh;\r\n\r\n    for (let meshIndex = 0; meshIndex < scene.meshes.length; meshIndex++) {\r\n        const mesh = scene.meshes[meshIndex];\r\n\r\n        if (predicate) {\r\n            if (!predicate(mesh, -1)) {\r\n                continue;\r\n            }\r\n        } else if (!mesh.isEnabled() || !mesh.isVisible || !mesh.isPickable) {\r\n            continue;\r\n        }\r\n\r\n        const forceCompute = computeWorldMatrixForCamera && mesh.isWorldMatrixCameraDependent();\r\n        const world = mesh.computeWorldMatrix(forceCompute, currentCamera);\r\n\r\n        if (mesh.hasThinInstances && (mesh as Mesh).thinInstanceEnablePicking) {\r\n            // first check if the ray intersects the whole bounding box/sphere of the mesh\r\n            const result = picker(pickingInfo, rayFunction, mesh, world, true, true, trianglePredicate);\r\n            if (result) {\r\n                if (onlyBoundingInfo) {\r\n                    // the user only asked for a bounding info check so we can return\r\n                    return result;\r\n                }\r\n                const tmpMatrix = TmpVectors.Matrix[1];\r\n                const thinMatrices = (mesh as Mesh).thinInstanceGetWorldMatrices();\r\n                for (let index = 0; index < thinMatrices.length; index++) {\r\n                    if (predicate && !predicate(mesh, index)) {\r\n                        continue;\r\n                    }\r\n                    const thinMatrix = thinMatrices[index];\r\n                    thinMatrix.multiplyToRef(world, tmpMatrix);\r\n                    const result = picker(pickingInfo, rayFunction, mesh, tmpMatrix, fastCheck, onlyBoundingInfo, trianglePredicate, true);\r\n\r\n                    if (result) {\r\n                        pickingInfo = result;\r\n                        pickingInfo.thinInstanceIndex = index;\r\n\r\n                        if (fastCheck) {\r\n                            return pickingInfo;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const result = picker(pickingInfo, rayFunction, mesh, world, fastCheck, onlyBoundingInfo, trianglePredicate);\r\n\r\n            if (result) {\r\n                pickingInfo = result;\r\n\r\n                if (fastCheck) {\r\n                    return pickingInfo;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfo || new PickingInfo();\r\n}\r\n\r\nfunction InternalMultiPick(\r\n    scene: Scene,\r\n    rayFunction: (world: Matrix, enableDistantPicking: boolean) => Ray,\r\n    predicate?: MeshPredicate,\r\n    trianglePredicate?: TrianglePickingPredicate\r\n): Nullable<PickingInfo[]> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n    const pickingInfos: PickingInfo[] = [];\r\n    const computeWorldMatrixForCamera = !!(scene.activeCameras && scene.activeCameras.length > 1 && scene.cameraToUseForPointers !== scene.activeCamera);\r\n    const currentCamera = scene.cameraToUseForPointers || scene.activeCamera;\r\n    const picker = PickingCustomization.internalPickerForMesh || InternalPickForMesh;\r\n\r\n    for (let meshIndex = 0; meshIndex < scene.meshes.length; meshIndex++) {\r\n        const mesh = scene.meshes[meshIndex];\r\n\r\n        if (predicate) {\r\n            if (!predicate(mesh, -1)) {\r\n                continue;\r\n            }\r\n        } else if (!mesh.isEnabled() || !mesh.isVisible || !mesh.isPickable) {\r\n            continue;\r\n        }\r\n\r\n        const forceCompute = computeWorldMatrixForCamera && mesh.isWorldMatrixCameraDependent();\r\n        const world = mesh.computeWorldMatrix(forceCompute, currentCamera);\r\n\r\n        if (mesh.hasThinInstances && (mesh as Mesh).thinInstanceEnablePicking) {\r\n            const result = picker(null, rayFunction, mesh, world, true, true, trianglePredicate);\r\n            if (result) {\r\n                const tmpMatrix = TmpVectors.Matrix[1];\r\n                const thinMatrices = (mesh as Mesh).thinInstanceGetWorldMatrices();\r\n                for (let index = 0; index < thinMatrices.length; index++) {\r\n                    if (predicate && !predicate(mesh, index)) {\r\n                        continue;\r\n                    }\r\n                    const thinMatrix = thinMatrices[index];\r\n                    thinMatrix.multiplyToRef(world, tmpMatrix);\r\n                    const result = picker(null, rayFunction, mesh, tmpMatrix, false, false, trianglePredicate, true);\r\n\r\n                    if (result) {\r\n                        result.thinInstanceIndex = index;\r\n                        pickingInfos.push(result);\r\n                    }\r\n                }\r\n            }\r\n        } else {\r\n            const result = picker(null, rayFunction, mesh, world, false, false, trianglePredicate);\r\n\r\n            if (result) {\r\n                pickingInfos.push(result);\r\n            }\r\n        }\r\n    }\r\n\r\n    return pickingInfos;\r\n}\r\n\r\n/** Launch a ray to try to pick a mesh in the scene using only bounding information of the main mesh (not using submeshes)\r\n * @param scene defines the scene to use for the picking\r\n * @param x position on screen\r\n * @param y position on screen\r\n * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n * @returns a PickingInfo (Please note that some info will not be set like distance, bv, bu and everything that cannot be capture by only using bounding infos)\r\n */\r\nexport function PickWithBoundingInfo(scene: Scene, x: number, y: number, predicate?: MeshPredicate, fastCheck?: boolean, camera?: Nullable<Camera>): Nullable<PickingInfo> {\r\n    if (!PickingInfo) {\r\n        return null;\r\n    }\r\n    const result = InternalPick(\r\n        scene,\r\n        (world) => {\r\n            if (!scene._tempPickingRay) {\r\n                scene._tempPickingRay = Ray.Zero();\r\n            }\r\n\r\n            CreatePickingRayToRef(scene, x, y, world, scene._tempPickingRay, camera || null);\r\n            return scene._tempPickingRay;\r\n        },\r\n        predicate,\r\n        fastCheck,\r\n        true\r\n    );\r\n    if (result) {\r\n        result.ray = CreatePickingRay(scene, x, y, Matrix.Identity(), camera || null);\r\n    }\r\n    return result;\r\n}\r\n\r\n/** Launch a ray to try to pick a mesh in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param x position on screen\r\n * @param y position on screen\r\n * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n * @param camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n * @param _enableDistantPicking defines if picking should handle large values for mesh position/scaling (false by default)\r\n * @returns a PickingInfo\r\n */\r\nexport function Pick(\r\n    scene: Scene,\r\n    x: number,\r\n    y: number,\r\n    predicate?: MeshPredicate,\r\n    fastCheck?: boolean,\r\n    camera?: Nullable<Camera>,\r\n    trianglePredicate?: TrianglePickingPredicate,\r\n    _enableDistantPicking = false\r\n): PickingInfo {\r\n    const result = InternalPick(\r\n        scene,\r\n        (world, enableDistantPicking) => {\r\n            if (!scene._tempPickingRay) {\r\n                scene._tempPickingRay = Ray.Zero();\r\n            }\r\n\r\n            CreatePickingRayToRef(scene, x, y, world, scene._tempPickingRay, camera || null, false, enableDistantPicking);\r\n            return scene._tempPickingRay;\r\n        },\r\n        predicate,\r\n        fastCheck,\r\n        false,\r\n        trianglePredicate\r\n    );\r\n    if (result) {\r\n        result.ray = CreatePickingRay(scene, x, y, Matrix.Identity(), camera || null);\r\n    }\r\n    return result;\r\n}\r\n\r\n/**\r\n * Use the given ray to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\r\n * irrespective of orientation.\r\n * @param scene defines the scene to use for the picking\r\n * @param ray The ray to use to pick meshes\r\n * @param predicate Predicate function used to determine eligible meshes. Can be set to null. In this case, a mesh must have isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param fastCheck defines if the first intersection will be used (and not the closest)\r\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n * @returns a PickingInfo\r\n */\r\nexport function PickWithRay(scene: Scene, ray: Ray, predicate?: MeshPredicate, fastCheck?: boolean, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo> {\r\n    const result = InternalPick(\r\n        scene,\r\n        (world) => {\r\n            if (!scene._pickWithRayInverseMatrix) {\r\n                scene._pickWithRayInverseMatrix = Matrix.Identity();\r\n            }\r\n            world.invertToRef(scene._pickWithRayInverseMatrix);\r\n\r\n            if (!scene._cachedRayForTransform) {\r\n                scene._cachedRayForTransform = Ray.Zero();\r\n            }\r\n\r\n            Ray.TransformToRef(ray, scene._pickWithRayInverseMatrix, scene._cachedRayForTransform);\r\n            return scene._cachedRayForTransform;\r\n        },\r\n        predicate,\r\n        fastCheck,\r\n        false,\r\n        trianglePredicate\r\n    );\r\n    if (result) {\r\n        result.ray = ray;\r\n    }\r\n    return result;\r\n}\r\n\r\n/**\r\n * Launch a ray to try to pick a mesh in the scene. A mesh triangle can be picked both from its front and back sides,\r\n * irrespective of orientation.\r\n * @param scene defines the scene to use for the picking\r\n * @param x X position on screen\r\n * @param y Y position on screen\r\n * @param predicate Predicate function used to determine eligible meshes and instances. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param camera camera to use for computing the picking ray. Can be set to null. In this case, the scene.activeCamera will be used\r\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n * @returns an array of PickingInfo\r\n */\r\nexport function MultiPick(scene: Scene, x: number, y: number, predicate?: MeshPredicate, camera?: Camera, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n    return InternalMultiPick(scene, (world) => CreatePickingRay(scene, x, y, world, camera || null), predicate, trianglePredicate);\r\n}\r\n\r\n/**\r\n * Launch a ray to try to pick a mesh in the scene\r\n * @param scene defines the scene to use for the picking\r\n * @param ray Ray to use\r\n * @param predicate Predicate function used to determine eligible meshes and instances. Can be set to null. In this case, a mesh must be enabled, visible and with isPickable set to true. thinInstanceIndex is -1 when the mesh is non-instanced\r\n * @param trianglePredicate defines an optional predicate used to select faces when a mesh intersection is detected\r\n * @returns an array of PickingInfo\r\n */\r\nexport function MultiPickWithRay(scene: Scene, ray: Ray, predicate?: MeshPredicate, trianglePredicate?: TrianglePickingPredicate): Nullable<PickingInfo[]> {\r\n    return InternalMultiPick(\r\n        scene,\r\n        (world) => {\r\n            if (!scene._pickWithRayInverseMatrix) {\r\n                scene._pickWithRayInverseMatrix = Matrix.Identity();\r\n            }\r\n            world.invertToRef(scene._pickWithRayInverseMatrix);\r\n\r\n            if (!scene._cachedRayForTransform) {\r\n                scene._cachedRayForTransform = Ray.Zero();\r\n            }\r\n\r\n            Ray.TransformToRef(ray, scene._pickWithRayInverseMatrix, scene._cachedRayForTransform);\r\n            return scene._cachedRayForTransform;\r\n        },\r\n        predicate,\r\n        trianglePredicate\r\n    );\r\n}\r\n\r\n/**\r\n * Gets a ray in the forward direction from the camera.\r\n * @param camera Defines the camera to use to get the ray from\r\n * @param length Defines the length of the ray to create\r\n * @param transform Defines the transform to apply to the ray, by default the world matrix is used to create a workd space ray\r\n * @param origin Defines the start point of the ray which defaults to the camera position\r\n * @returns the forward ray\r\n */\r\nexport function GetForwardRay(camera: Camera, length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n    return GetForwardRayToRef(camera, new Ray(Vector3.Zero(), Vector3.Zero(), length), length, transform, origin);\r\n}\r\n\r\n/**\r\n * Gets a ray in the forward direction from the camera.\r\n * @param camera Defines the camera to use to get the ray from\r\n * @param refRay the ray to (re)use when setting the values\r\n * @param length Defines the length of the ray to create\r\n * @param transform Defines the transform to apply to the ray, by default the world matrx is used to create a workd space ray\r\n * @param origin Defines the start point of the ray which defaults to the camera position\r\n * @returns the forward ray\r\n */\r\nexport function GetForwardRayToRef(camera: Camera, refRay: Ray, length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n    if (!transform) {\r\n        transform = camera.getWorldMatrix();\r\n    }\r\n    refRay.length = length;\r\n\r\n    if (origin) {\r\n        refRay.origin.copyFrom(origin);\r\n    } else {\r\n        refRay.origin.copyFrom(camera.position);\r\n    }\r\n    const forward = TmpVectors.Vector3[2];\r\n    forward.set(0, 0, camera._scene.useRightHandedSystem ? -1 : 1);\r\n    const worldForward = TmpVectors.Vector3[3];\r\n    Vector3.TransformNormalToRef(forward, transform, worldForward);\r\n    Vector3.NormalizeToRef(worldForward, refRay.direction);\r\n\r\n    return refRay;\r\n}\r\n\r\n/**\r\n * Initialize the minimal interdependecies between the Ray and Scene and Camera\r\n * @param sceneClass defines the scene prototype to use\r\n * @param cameraClass defines the camera prototype to use\r\n */\r\nexport function AddRayExtensions(sceneClass: typeof Scene, cameraClass: typeof Camera): void {\r\n    if (cameraClass) {\r\n        cameraClass.prototype.getForwardRay = function (length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n            return GetForwardRayToRef(this, new Ray(Vector3.Zero(), Vector3.Zero(), length), length, transform, origin);\r\n        };\r\n\r\n        cameraClass.prototype.getForwardRayToRef = function (refRay: Ray, length = 100, transform?: Matrix, origin?: Vector3): Ray {\r\n            return GetForwardRayToRef(this, refRay, length, transform, origin);\r\n        };\r\n    }\r\n\r\n    if (!sceneClass) {\r\n        return;\r\n    }\r\n\r\n    _ImportHelper._IsPickingAvailable = true;\r\n\r\n    sceneClass.prototype.createPickingRay = function (x: number, y: number, world: Nullable<Matrix>, camera: Nullable<Camera>, cameraViewSpace = false): Ray {\r\n        return CreatePickingRay(this, x, y, world, camera, cameraViewSpace);\r\n    };\r\n}\r\n"]}