import { Color3 } from "../Maths/math.color.js";
import type { Scene } from "../scene.js";
import type { Nullable } from "../types.js";
import type { Skeleton } from "../Bones/skeleton.js";
import type { AbstractMesh } from "../Meshes/abstractMesh.js";
import type { LinesMesh } from "../Meshes/linesMesh.js";
import { UtilityLayerRenderer } from "../Rendering/utilityLayerRenderer.js";
import { ShaderMaterial } from "../Materials/shaderMaterial.js";
import type { ISkeletonViewerOptions, IBoneWeightShaderOptions, ISkeletonMapShaderOptions } from "./ISkeletonViewer.js";
/**
 * Class used to render a debug view of a given skeleton
 * @see http://www.babylonjs-playground.com/#1BZJVJ#8
 */
export declare class SkeletonViewer {
    /** defines the skeleton to render */
    skeleton: Skeleton;
    /** defines the mesh attached to the skeleton */
    mesh: Nullable<AbstractMesh>;
    /** [true] defines a boolean indicating if bones matrices must be forced to update before rendering (true by default)  */
    autoUpdateBonesMatrices: boolean;
    /** [3] defines the rendering group id to use with the viewer */
    renderingGroupId: number;
    /** [Object] is the options for the viewer */
    options: Partial<ISkeletonViewerOptions>;
    /** public Display constants BABYLON.SkeletonViewer.DISPLAY_LINES */
    static readonly DISPLAY_LINES = 0;
    /** public Display constants BABYLON.SkeletonViewer.DISPLAY_SPHERES */
    static readonly DISPLAY_SPHERES = 1;
    /** public Display constants BABYLON.SkeletonViewer.DISPLAY_SPHERE_AND_SPURS */
    static readonly DISPLAY_SPHERE_AND_SPURS = 2;
    /** public static method to create a BoneWeight Shader
     * @param options The constructor options
     * @param scene The scene that the shader is scoped to
     * @returns The created ShaderMaterial
     * @see http://www.babylonjs-playground.com/#1BZJVJ#395
     */
    static CreateBoneWeightShader(options: IBoneWeightShaderOptions, scene: Scene): ShaderMaterial;
    /** public static method to create a BoneWeight Shader
     * @param options The constructor options
     * @param scene The scene that the shader is scoped to
     * @returns The created ShaderMaterial
     */
    static CreateSkeletonMapShader(options: ISkeletonMapShaderOptions, scene: Scene): ShaderMaterial;
    /** private static method to create a BoneWeight Shader
     * @param size The size of the buffer to create (usually the bone count)
     * @param colorMap The gradient data to generate
     * @param scene The scene that the shader is scoped to
     * @returns an Array of floats from the color gradient values
     */
    private static _CreateBoneMapColorBuffer;
    /** If SkeletonViewer scene scope. */
    private _scene;
    /** Gets or sets the color used to render the skeleton */
    color: Color3;
    /** Array of the points of the skeleton fo the line view. */
    private _debugLines;
    /** The SkeletonViewers Mesh. */
    private _debugMesh;
    /** The local axes Meshes. */
    private _localAxes;
    /** If SkeletonViewer is enabled. */
    private _isEnabled;
    /** If SkeletonViewer is ready. */
    private _ready;
    /** SkeletonViewer render observable. */
    private _obs;
    /** The Utility Layer to render the gizmos in. */
    private _utilityLayer;
    private _boneIndices;
    /** Gets the Scene. */
    get scene(): Scene;
    /** Gets the utilityLayer. */
    get utilityLayer(): Nullable<UtilityLayerRenderer>;
    /** Checks Ready Status. */
    get isReady(): boolean;
    /** Sets Ready Status. */
    set ready(value: boolean);
    /** Gets the debugMesh */
    get debugMesh(): Nullable<AbstractMesh> | Nullable<LinesMesh>;
    /** Sets the debugMesh */
    set debugMesh(value: Nullable<AbstractMesh> | Nullable<LinesMesh>);
    /** Gets the displayMode */
    get displayMode(): number;
    /** Sets the displayMode */
    set displayMode(value: number);
    /**
     * Creates a new SkeletonViewer
     * @param skeleton defines the skeleton to render
     * @param mesh defines the mesh attached to the skeleton
     * @param scene defines the hosting scene
     * @param autoUpdateBonesMatrices defines a boolean indicating if bones matrices must be forced to update before rendering (true by default)
     * @param renderingGroupId defines the rendering group id to use with the viewer
     * @param options All of the extra constructor options for the SkeletonViewer
     */
    constructor(
    /** defines the skeleton to render */
    skeleton: Skeleton, 
    /** defines the mesh attached to the skeleton */
    mesh: Nullable<AbstractMesh>, 
    /** The Scene scope*/
    scene: Scene, 
    /** [true] defines a boolean indicating if bones matrices must be forced to update before rendering (true by default)  */
    autoUpdateBonesMatrices?: boolean, 
    /** [3] defines the rendering group id to use with the viewer */
    renderingGroupId?: number, 
    /** [Object] is the options for the viewer */
    options?: Partial<ISkeletonViewerOptions>);
    /** The Dynamic bindings for the update functions */
    private _bindObs;
    /** Update the viewer to sync with current skeleton state, only used to manually update. */
    update(): void;
    /** Gets or sets a boolean indicating if the viewer is enabled */
    set isEnabled(value: boolean);
    get isEnabled(): boolean;
    private _getBonePosition;
    private _getLinesForBonesWithLength;
    private _getLinesForBonesNoLength;
    /**
     * function to revert the mesh and scene back to the initial state.
     * @param animationState
     */
    private _revert;
    /**
     * function to get the absolute bind pose of a bone by accumulating transformations up the bone hierarchy.
     * @param bone
     * @param matrix
     */
    private _getAbsoluteBindPoseToRef;
    private _createSpur;
    private _getBoundingSphereForBone;
    /**
     * function to build and bind sphere joint points and spur bone representations.
     * @param spheresOnly
     */
    private _buildSpheresAndSpurs;
    private _buildLocalAxes;
    /** Update the viewer to sync with current skeleton state, only used for the line display. */
    private _displayLinesUpdate;
    /** Changes the displayMode of the skeleton viewer
     * @param mode The displayMode numerical value
     */
    changeDisplayMode(mode: number): void;
    /** Sets a display option of the skeleton viewer
     *
     * | Option           | Type    | Default | Description |
     * | ---------------- | ------- | ------- | ----------- |
     * | midStep          | float   | 0.235   | A percentage between a bone and its child that determines the widest part of a spur. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |
     * | midStepFactor    | float   | 0.15    | Mid step width expressed as a factor of the length. A value of 0.5 makes the spur width half of the spur length. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |
     * | sphereBaseSize   | float   | 2       | Sphere base size. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |
     * | sphereScaleUnit  | float   | 0.865   | Sphere scale factor used to scale spheres in relation to the longest bone. Only used when `displayMode` is set to `DISPLAY_SPHERE_AND_SPURS`. |
     * | spurFollowsChild | boolean | false   | Whether a spur should attach its far end to the child bone. |
     * | showLocalAxes    | boolean | false   | Displays local axes on all bones. |
     * | localAxesSize    | float   | 0.075   | Determines the length of each local axis. |
     *
     * @param option String of the option name
     * @param value The numerical option value
     */
    changeDisplayOptions(option: string, value: number | boolean): void;
    /** Release associated resources */
    dispose(): void;
}
