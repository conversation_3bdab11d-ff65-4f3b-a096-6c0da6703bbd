{"version": 3, "file": "engine.computeShader.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.computeShader.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEtD,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AA2CnD,gBAAgB;AAChB,MAAM,CAAN,IAAkB,kBASjB;AATD,WAAkB,kBAAkB;IAChC,iEAAW,CAAA;IACX,+EAAkB,CAAA;IAClB,6EAAiB,CAAA;IACjB,6EAAiB,CAAA;IACjB,6FAAyB,CAAA;IACzB,iEAAW,CAAA;IACX,iFAAmB,CAAA;IACnB,uEAAc,CAAA;AAClB,CAAC,EATiB,kBAAkB,KAAlB,kBAAkB,QASnC;AAmHD,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,QAAwD,EAAE,OAAsC;IACjJ,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;AAC1F,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,4BAA4B,GAAG;IAChD,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;AACnG,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,oBAAoB,GAAG;IACxC,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,eAAe,GAAG,UACnC,MAAqB,EACrB,OAAwB,EACxB,QAA4B,EAC5B,CAAS,EACT,CAAU,EACV,CAAU,EACV,eAAuC;IAEvC,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;AACtF,CAAC,CAAC;AACF,UAAU,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAC3C,MAAqB,EACrB,OAAwB,EACxB,QAA4B,EAC5B,MAAkB,EAClB,MAAe,EACf,eAAuC;IAEvC,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;AAC9F,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG;IAC7C,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG,cAAmB,CAAC,CAAC;AAElE,UAAU,CAAC,SAAS,CAAC,8BAA8B,GAAG,UAClD,eAAwC,EACxC,iBAAyB,EACzB,oBAA4B,EAC5B,OAAyB,EACzB,UAAkB,IACb,CAAC,CAAC;AAEX,UAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,cAAmB,CAAC,CAAC;AAEnE,cAAc,CAAC,SAAS,CAAC,kCAAkC,GAAG,UAC1D,eAAwC,EACxC,MAAgE;IAEhE,MAAM,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,MAAqB,IAAS,CAAC,CAAC;AAEvF,UAAU,CAAC,SAAS,CAAC,6BAA6B,GAAG,UAAU,eAAwC,IAAS,CAAC,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport type { ComputeEffect, IComputeEffectCreationOptions, IComputeShaderPath } from \"../../Compute/computeEffect\";\r\nimport type { IComputeContext } from \"../../Compute/IComputeContext\";\r\nimport type { IComputePipelineContext } from \"../../Compute/IComputePipelineContext\";\r\nimport { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport type { Nullable } from \"../../types\";\r\nimport { AbstractEngine } from \"../abstractEngine\";\r\nimport type { WebGPUPerfCounter } from \"../WebGPU/webgpuPerfCounter\";\r\nimport type { DataBuffer } from \"../../Buffers/dataBuffer\";\r\n\r\n/**\r\n * Type used to locate a resource in a compute shader.\r\n * TODO: remove this when browsers support reflection for wgsl shaders\r\n */\r\nexport type ComputeBindingLocation = { group: number; binding: number };\r\n\r\n/**\r\n * Type used to lookup a resource and retrieve its binding location\r\n * TODO: remove this when browsers support reflection for wgsl shaders\r\n */\r\nexport type ComputeBindingMapping = { [key: string]: ComputeBindingLocation };\r\n\r\n/**\r\n * Types of messages that can be generated during compilation\r\n */\r\nexport type ComputeCompilationMessageType = \"error\" | \"warning\" | \"info\";\r\n\r\n/**\r\n * Messages generated during compilation\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport interface ComputeCompilationMessages {\r\n    /**\r\n     * Number of errors generated during compilation\r\n     */\r\n    numErrors: number;\r\n    /**\r\n     * List of messages generated during compilation\r\n     */\r\n    messages: {\r\n        type: ComputeCompilationMessageType;\r\n        text: string;\r\n        line?: number;\r\n        column?: number;\r\n        length?: number;\r\n        offset?: number;\r\n    }[];\r\n}\r\n\r\n/** @internal */\r\nexport const enum ComputeBindingType {\r\n    Texture = 0,\r\n    StorageTexture = 1,\r\n    UniformBuffer = 2,\r\n    StorageBuffer = 3,\r\n    TextureWithoutSampler = 4,\r\n    Sampler = 5,\r\n    ExternalTexture = 6,\r\n    DataBuffer = 7,\r\n}\r\n\r\n/** @internal */\r\nexport type ComputeBindingList = { [key: string]: { type: ComputeBindingType; object: any; indexInGroupEntries?: number } };\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Creates a new compute effect\r\n         * @param baseName Name of the effect\r\n         * @param options Options used to create the effect\r\n         * @returns The new compute effect\r\n         */\r\n        createComputeEffect(\r\n            baseName:\r\n                | string\r\n                | (IComputeShaderPath & {\r\n                      /**\r\n                       * @internal\r\n                       */\r\n                      computeToken?: string;\r\n                  }),\r\n            options: IComputeEffectCreationOptions\r\n        ): ComputeEffect;\r\n\r\n        /**\r\n         * Creates a new compute pipeline context\r\n         * @returns the new pipeline\r\n         */\r\n        createComputePipelineContext(): IComputePipelineContext;\r\n\r\n        /**\r\n         * Creates a new compute context\r\n         * @returns the new context\r\n         */\r\n        createComputeContext(): IComputeContext | undefined;\r\n\r\n        /**\r\n         * Dispatches a compute shader\r\n         * @param effect The compute effect\r\n         * @param context The compute context\r\n         * @param bindings The list of resources to bind to the shader\r\n         * @param x The number of workgroups to execute on the X dimension\r\n         * @param y The number of workgroups to execute on the Y dimension\r\n         * @param z The number of workgroups to execute on the Z dimension\r\n         * @param bindingsMapping list of bindings mapping (key is property name, value is binding location)\r\n         * @param gpuPerfCounter GPU time computed for the compute shader will be assigned to this object\r\n         */\r\n        computeDispatch(\r\n            effect: ComputeEffect,\r\n            context: IComputeContext,\r\n            bindings: ComputeBindingList,\r\n            x: number,\r\n            y?: number,\r\n            z?: number,\r\n            bindingsMapping?: ComputeBindingMapping,\r\n            gpuPerfCounter?: WebGPUPerfCounter\r\n        ): void;\r\n\r\n        /**\r\n         * Dispatches a compute shader\r\n         * @param effect The compute effect\r\n         * @param context The compute context\r\n         * @param bindings The list of resources to bind to the shader\r\n         * @param x The number of workgroups to execute on the X dimension\r\n         * @param y The number of workgroups to execute on the Y dimension\r\n         * @param z The number of workgroups to execute on the Z dimension\r\n         * @param bindingsMapping list of bindings mapping (key is property name, value is binding location)\r\n         * @param gpuPerfCounter GPU time computed for the compute shader will be assigned to this object\r\n         */\r\n        computeDispatchIndirect(\r\n            effect: ComputeEffect,\r\n            context: IComputeContext,\r\n            bindings: ComputeBindingList,\r\n            buffer: DataBuffer,\r\n            offset?: number,\r\n            bindingsMapping?: ComputeBindingMapping,\r\n            gpuPerfCounter?: WebGPUPerfCounter\r\n        ): void;\r\n\r\n        /**\r\n         * Gets a boolean indicating if all created compute effects are ready\r\n         * @returns true if all effects are ready\r\n         */\r\n        areAllComputeEffectsReady(): boolean;\r\n\r\n        /**\r\n         * Forces the engine to release all cached compute effects. This means that next effect compilation will have to be done completely even if a similar effect was already compiled\r\n         */\r\n        releaseComputeEffects(): void;\r\n\r\n        /** @internal */\r\n        _prepareComputePipelineContext(\r\n            pipelineContext: IComputePipelineContext,\r\n            computeSourceCode: string,\r\n            rawComputeSourceCode: string,\r\n            defines: Nullable<string>,\r\n            entryPoint: string\r\n        ): void;\r\n\r\n        /** @internal */\r\n        _rebuildComputeEffects(): void;\r\n\r\n        /** @internal */\r\n        _executeWhenComputeStateIsCompiled(pipelineContext: IComputePipelineContext, action: (messages: Nullable<ComputeCompilationMessages>) => void): void;\r\n\r\n        /** @internal */\r\n        _releaseComputeEffect(effect: ComputeEffect): void;\r\n\r\n        /** @internal */\r\n        _deleteComputePipelineContext(pipelineContext: IComputePipelineContext): void;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createComputeEffect = function (baseName: IComputeShaderPath & { computeToken?: string }, options: IComputeEffectCreationOptions): ComputeEffect {\r\n    throw new Error(\"createComputeEffect: This engine does not support compute shaders!\");\r\n};\r\n\r\nThinEngine.prototype.createComputePipelineContext = function (): IComputePipelineContext {\r\n    throw new Error(\"createComputePipelineContext: This engine does not support compute shaders!\");\r\n};\r\n\r\nThinEngine.prototype.createComputeContext = function (): IComputeContext | undefined {\r\n    return undefined;\r\n};\r\n\r\nThinEngine.prototype.computeDispatch = function (\r\n    effect: ComputeEffect,\r\n    context: IComputeContext,\r\n    bindings: ComputeBindingList,\r\n    x: number,\r\n    y?: number,\r\n    z?: number,\r\n    bindingsMapping?: ComputeBindingMapping\r\n): void {\r\n    throw new Error(\"computeDispatch: This engine does not support compute shaders!\");\r\n};\r\nThinEngine.prototype.computeDispatchIndirect = function (\r\n    effect: ComputeEffect,\r\n    context: IComputeContext,\r\n    bindings: ComputeBindingList,\r\n    buffer: DataBuffer,\r\n    offset?: number,\r\n    bindingsMapping?: ComputeBindingMapping\r\n): void {\r\n    throw new Error(\"computeDispatchIndirect: This engine does not support compute shaders!\");\r\n};\r\n\r\nThinEngine.prototype.areAllComputeEffectsReady = function (): boolean {\r\n    return true;\r\n};\r\n\r\nThinEngine.prototype.releaseComputeEffects = function (): void {};\r\n\r\nThinEngine.prototype._prepareComputePipelineContext = function (\r\n    pipelineContext: IComputePipelineContext,\r\n    computeSourceCode: string,\r\n    rawComputeSourceCode: string,\r\n    defines: Nullable<string>,\r\n    entryPoint: string\r\n): void {};\r\n\r\nThinEngine.prototype._rebuildComputeEffects = function (): void {};\r\n\r\nAbstractEngine.prototype._executeWhenComputeStateIsCompiled = function (\r\n    pipelineContext: IComputePipelineContext,\r\n    action: (messages: Nullable<ComputeCompilationMessages>) => void\r\n): void {\r\n    action(null);\r\n};\r\n\r\nThinEngine.prototype._releaseComputeEffect = function (effect: ComputeEffect): void {};\r\n\r\nThinEngine.prototype._deleteComputePipelineContext = function (pipelineContext: IComputePipelineContext): void {};\r\n"]}