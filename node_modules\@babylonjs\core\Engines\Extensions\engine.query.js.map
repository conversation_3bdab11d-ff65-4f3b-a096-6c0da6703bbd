{"version": 3, "file": "engine.query.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Extensions/engine.query.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACtD,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,iCAAiC,CAAC;AAK7D,OAAO,4CAA4C,CAAC;AACpD,OAAO,wCAAwC,CAAC;AAkDhD,UAAU,CAAC,SAAS,CAAC,WAAW,GAAG;IAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;IACrC,IAAI,CAAC,KAAK,EAAE,CAAC;QACT,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACxD,CAAC;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,KAAqB;IAC9D,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAE5B,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAAU,KAAqB;IACzE,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,sBAAsB,CAAY,CAAC;AACzF,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,KAAqB;IACjE,OAAO,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAW,CAAC;AAC9E,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,aAAqB,EAAE,KAAqB;IAC7F,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IAC5D,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAExC,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,aAAqB;IACpE,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IAC5D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAE/B,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG;IACpC,MAAM,UAAU,GAA6B,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IAEvE,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;QAC5B,OAAO,UAAU,CAAC,cAAc,EAAE,CAAC;IACvC,CAAC;IAED,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;AAC9B,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,KAAiB;IAC/D,MAAM,UAAU,GAA6B,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IAEvE,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;QAC5B,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QACjC,OAAO;IACX,CAAC;IAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,KAAiB;IAClE,MAAM,UAAU,GAA6B,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IAEvE,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAC/B,OAAO,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC5E,CAAC;IACD,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACtC,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,KAAiB;IACxE,MAAM,UAAU,GAA6B,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC;IAEvE,IAAI,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAC/B,OAAO,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,0BAA0B,CAAC,CAAC;IACtF,CAAC;IACD,OAAO,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,cAAc,GAAG;IAClC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,IAAI,CAAC,UAAU,EAAE,CAAC;QACd,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,KAAK,GAAG,IAAI,UAAU,EAAE,CAAC;IAC/B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;IACnD,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAEhD,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YACxB,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,yBAAyB,CAAC;QAC1C,CAAC;QAED,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAClD,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC3B,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACnF,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;QAED,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;IAC3C,CAAC;IACD,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,KAAiB;IAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IACnC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE,CAAC;QACxB,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;YACzB,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;YACvB,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9C,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACtB,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;IACL,CAAC;SAAM,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC3B,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;QACD,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;YACzB,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;YAC/C,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;QAC1C,CAAC;QACD,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC;IACxC,CAAC;IAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;IACpE,IAAI,SAAS,GAAY,KAAK,CAAC;IAC/B,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;QACtB,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACpE,CAAC;SAAM,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;QACjC,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACxE,CAAC;IAED,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzB,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBACjD,OAAO,CAAC,CAAC,CAAC;YACd,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAClE,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAE9D,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC;YAC7B,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC3C,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC;YAC7B,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;QAC/B,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,CAAC;gBAC3B,OAAO,CAAC,CAAC,CAAC;YACd,CAAC;YAED,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC3D,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC/C,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC/B,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC;QACzC,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,OAAO,CAAC,CAAC,CAAC;AACd,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,KAAc;IAC/D,IAAI,KAAK,KAAK,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACtC,OAAO;IACX,CAAC;IAED,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;IAElC,IAAI,KAAK,EAAE,CAAC;QACR,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAEnD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC9D,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACpD,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE;YAC1D,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3B,OAAO;YACX,CAAC;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAExD,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC;gBACZ,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;gBAC/B,YAAY,CAAC,aAAa,EAAE,CAAC;gBAC7B,YAAY,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACtC,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;SAAM,CAAC;QACJ,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC/D,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC3D,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;IACpC,CAAC;AACL,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,aAAqB;IACtE,OAAO,aAAa,KAAK,YAAY,CAAC,qCAAqC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACzJ,CAAC,CAAC", "sourcesContent": ["import type { Nullable, int } from \"../../types\";\r\nimport { ThinEngine } from \"../../Engines/thinEngine\";\r\nimport { AbstractMesh } from \"../../Meshes/abstractMesh\";\r\nimport { _TimeToken } from \"../../Instrumentation/timeToken\";\r\nimport type { Observer } from \"../../Misc/observable\";\r\nimport type { AbstractEngine } from \"../abstractEngine\";\r\nimport type { OcclusionQuery } from \"../AbstractEngine/abstractEngine.query\";\r\n\r\nimport \"../AbstractEngine/abstractEngine.timeQuery\";\r\nimport \"../AbstractEngine/abstractEngine.query\";\r\n\r\ndeclare module \"../../Engines/thinEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface ThinEngine {\r\n        /**\r\n         * @internal\r\n         */\r\n        _captureGPUFrameTime: boolean;\r\n\r\n        /**\r\n         * Starts a time query (used to measure time spent by the GPU on a specific frame)\r\n         * Please note that only one query can be issued at a time\r\n         * @returns a time token used to track the time span\r\n         */\r\n        startTimeQuery(): Nullable<_TimeToken>;\r\n\r\n        /**\r\n         * Ends a time query\r\n         * @param token defines the token used to measure the time span\r\n         * @returns the time spent (in ns)\r\n         */\r\n        endTimeQuery(token: _TimeToken): int;\r\n\r\n        /** @internal */\r\n        _currentNonTimestampToken: Nullable<_TimeToken>;\r\n        /** @internal */\r\n        _gpuFrameTimeToken: Nullable<_TimeToken>;\r\n        /** @internal */\r\n        _onBeginFrameObserver: Nullable<Observer<AbstractEngine>>;\r\n        /** @internal */\r\n        _onEndFrameObserver: Nullable<Observer<AbstractEngine>>;\r\n\r\n        /** @internal */\r\n        _createTimeQuery(): Nullable<WebGLQuery>;\r\n\r\n        /** @internal */\r\n        _deleteTimeQuery(query: WebGLQuery): void;\r\n\r\n        /** @internal */\r\n        _getGlAlgorithmType(algorithmType: number): number;\r\n\r\n        /** @internal */\r\n        _getTimeQueryResult(query: WebGLQuery): any;\r\n\r\n        /** @internal */\r\n        _getTimeQueryAvailability(query: WebGLQuery): any;\r\n    }\r\n}\r\n\r\nThinEngine.prototype.createQuery = function (): OcclusionQuery {\r\n    const query = this._gl.createQuery();\r\n    if (!query) {\r\n        throw new Error(\"Unable to create Occlusion Query\");\r\n    }\r\n    return query;\r\n};\r\n\r\nThinEngine.prototype.deleteQuery = function (query: OcclusionQuery): ThinEngine {\r\n    this._gl.deleteQuery(query);\r\n\r\n    return this;\r\n};\r\n\r\nThinEngine.prototype.isQueryResultAvailable = function (query: OcclusionQuery): boolean {\r\n    return this._gl.getQueryParameter(query, this._gl.QUERY_RESULT_AVAILABLE) as boolean;\r\n};\r\n\r\nThinEngine.prototype.getQueryResult = function (query: OcclusionQuery): number {\r\n    return this._gl.getQueryParameter(query, this._gl.QUERY_RESULT) as number;\r\n};\r\n\r\nThinEngine.prototype.beginOcclusionQuery = function (algorithmType: number, query: OcclusionQuery): boolean {\r\n    const glAlgorithm = this._getGlAlgorithmType(algorithmType);\r\n    this._gl.beginQuery(glAlgorithm, query);\r\n\r\n    return true;\r\n};\r\n\r\nThinEngine.prototype.endOcclusionQuery = function (algorithmType: number): ThinEngine {\r\n    const glAlgorithm = this._getGlAlgorithmType(algorithmType);\r\n    this._gl.endQuery(glAlgorithm);\r\n\r\n    return this;\r\n};\r\n\r\nThinEngine.prototype._createTimeQuery = function (): Nullable<WebGLQuery> {\r\n    const timerQuery = <EXT_disjoint_timer_query>this.getCaps().timerQuery;\r\n\r\n    if (timerQuery.createQueryEXT) {\r\n        return timerQuery.createQueryEXT();\r\n    }\r\n\r\n    return this.createQuery();\r\n};\r\n\r\nThinEngine.prototype._deleteTimeQuery = function (query: WebGLQuery): void {\r\n    const timerQuery = <EXT_disjoint_timer_query>this.getCaps().timerQuery;\r\n\r\n    if (timerQuery.deleteQueryEXT) {\r\n        timerQuery.deleteQueryEXT(query);\r\n        return;\r\n    }\r\n\r\n    this.deleteQuery(query);\r\n};\r\n\r\nThinEngine.prototype._getTimeQueryResult = function (query: WebGLQuery): any {\r\n    const timerQuery = <EXT_disjoint_timer_query>this.getCaps().timerQuery;\r\n\r\n    if (timerQuery.getQueryObjectEXT) {\r\n        return timerQuery.getQueryObjectEXT(query, timerQuery.QUERY_RESULT_EXT);\r\n    }\r\n    return this.getQueryResult(query);\r\n};\r\n\r\nThinEngine.prototype._getTimeQueryAvailability = function (query: WebGLQuery): any {\r\n    const timerQuery = <EXT_disjoint_timer_query>this.getCaps().timerQuery;\r\n\r\n    if (timerQuery.getQueryObjectEXT) {\r\n        return timerQuery.getQueryObjectEXT(query, timerQuery.QUERY_RESULT_AVAILABLE_EXT);\r\n    }\r\n    return this.isQueryResultAvailable(query);\r\n};\r\n\r\nThinEngine.prototype.startTimeQuery = function (): Nullable<_TimeToken> {\r\n    const caps = this.getCaps();\r\n    const timerQuery = caps.timerQuery;\r\n    if (!timerQuery) {\r\n        return null;\r\n    }\r\n\r\n    const token = new _TimeToken();\r\n    this._gl.getParameter(timerQuery.GPU_DISJOINT_EXT);\r\n    if (caps.canUseTimestampForTimerQuery) {\r\n        token._startTimeQuery = this._createTimeQuery();\r\n\r\n        if (token._startTimeQuery) {\r\n            timerQuery.queryCounterEXT(token._startTimeQuery, timerQuery.TIMESTAMP_EXT);\r\n        }\r\n    } else {\r\n        if (this._currentNonTimestampToken) {\r\n            return this._currentNonTimestampToken;\r\n        }\r\n\r\n        token._timeElapsedQuery = this._createTimeQuery();\r\n        if (token._timeElapsedQuery) {\r\n            if (timerQuery.beginQueryEXT) {\r\n                timerQuery.beginQueryEXT(timerQuery.TIME_ELAPSED_EXT, token._timeElapsedQuery);\r\n            } else {\r\n                this._gl.beginQuery(timerQuery.TIME_ELAPSED_EXT, token._timeElapsedQuery);\r\n            }\r\n        }\r\n\r\n        this._currentNonTimestampToken = token;\r\n    }\r\n    return token;\r\n};\r\n\r\nThinEngine.prototype.endTimeQuery = function (token: _TimeToken): int {\r\n    const caps = this.getCaps();\r\n    const timerQuery = caps.timerQuery;\r\n    if (!timerQuery || !token) {\r\n        return -1;\r\n    }\r\n\r\n    if (caps.canUseTimestampForTimerQuery) {\r\n        if (!token._startTimeQuery) {\r\n            return -1;\r\n        }\r\n        if (!token._endTimeQuery) {\r\n            token._endTimeQuery = this._createTimeQuery();\r\n            if (token._endTimeQuery) {\r\n                timerQuery.queryCounterEXT(token._endTimeQuery, timerQuery.TIMESTAMP_EXT);\r\n            }\r\n        }\r\n    } else if (!token._timeElapsedQueryEnded) {\r\n        if (!token._timeElapsedQuery) {\r\n            return -1;\r\n        }\r\n        if (timerQuery.endQueryEXT) {\r\n            timerQuery.endQueryEXT(timerQuery.TIME_ELAPSED_EXT);\r\n        } else {\r\n            this._gl.endQuery(timerQuery.TIME_ELAPSED_EXT);\r\n            this._currentNonTimestampToken = null;\r\n        }\r\n        token._timeElapsedQueryEnded = true;\r\n    }\r\n\r\n    const disjoint = this._gl.getParameter(timerQuery.GPU_DISJOINT_EXT);\r\n    let available: boolean = false;\r\n    if (token._endTimeQuery) {\r\n        available = this._getTimeQueryAvailability(token._endTimeQuery);\r\n    } else if (token._timeElapsedQuery) {\r\n        available = this._getTimeQueryAvailability(token._timeElapsedQuery);\r\n    }\r\n\r\n    if (available && !disjoint) {\r\n        let result = 0;\r\n        if (caps.canUseTimestampForTimerQuery) {\r\n            if (!token._startTimeQuery || !token._endTimeQuery) {\r\n                return -1;\r\n            }\r\n            const timeStart = this._getTimeQueryResult(token._startTimeQuery);\r\n            const timeEnd = this._getTimeQueryResult(token._endTimeQuery);\r\n\r\n            result = timeEnd - timeStart;\r\n            this._deleteTimeQuery(token._startTimeQuery);\r\n            this._deleteTimeQuery(token._endTimeQuery);\r\n            token._startTimeQuery = null;\r\n            token._endTimeQuery = null;\r\n        } else {\r\n            if (!token._timeElapsedQuery) {\r\n                return -1;\r\n            }\r\n\r\n            result = this._getTimeQueryResult(token._timeElapsedQuery);\r\n            this._deleteTimeQuery(token._timeElapsedQuery);\r\n            token._timeElapsedQuery = null;\r\n            token._timeElapsedQueryEnded = false;\r\n        }\r\n        return result;\r\n    }\r\n\r\n    return -1;\r\n};\r\n\r\nThinEngine.prototype.captureGPUFrameTime = function (value: boolean) {\r\n    if (value === this._captureGPUFrameTime) {\r\n        return;\r\n    }\r\n\r\n    this._captureGPUFrameTime = value;\r\n\r\n    if (value) {\r\n        const gpuFrameTime = this.getGPUFrameTimeCounter();\r\n\r\n        this._onBeginFrameObserver = this.onBeginFrameObservable.add(() => {\r\n            if (!this._gpuFrameTimeToken) {\r\n                this._gpuFrameTimeToken = this.startTimeQuery();\r\n            }\r\n        });\r\n\r\n        this._onEndFrameObserver = this.onEndFrameObservable.add(() => {\r\n            if (!this._gpuFrameTimeToken) {\r\n                return;\r\n            }\r\n            const time = this.endTimeQuery(this._gpuFrameTimeToken);\r\n\r\n            if (time > -1) {\r\n                this._gpuFrameTimeToken = null;\r\n                gpuFrameTime.fetchNewFrame();\r\n                gpuFrameTime.addCount(time, true);\r\n            }\r\n        });\r\n    } else {\r\n        this.onBeginFrameObservable.remove(this._onBeginFrameObserver);\r\n        this._onBeginFrameObserver = null;\r\n        this.onEndFrameObservable.remove(this._onEndFrameObserver);\r\n        this._onEndFrameObserver = null;\r\n    }\r\n};\r\n\r\nThinEngine.prototype._getGlAlgorithmType = function (algorithmType: number): number {\r\n    return algorithmType === AbstractMesh.OCCLUSION_ALGORITHM_TYPE_CONSERVATIVE ? this._gl.ANY_SAMPLES_PASSED_CONSERVATIVE : this._gl.ANY_SAMPLES_PASSED;\r\n};\r\n"]}