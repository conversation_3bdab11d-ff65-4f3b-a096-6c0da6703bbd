module.exports = {

"[project]/src/scripts/babylon-particles.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Engines/engine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$scene$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/scene.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Cameras$2f$freeCamera$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Cameras/freeCamera.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.vector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Maths/math.color.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$particleSystem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Particles/particleSystem.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/effect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Meshes/meshBuilder.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$standardMaterial$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/standardMaterial.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrMaterial$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/PBR/pbrMaterial.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$dynamicTexture$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/dynamicTexture.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$directionalLight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Lights/directionalLight.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$spotLight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Lights/spotLight.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$pointLight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Lights/pointLight.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$cubeTexture$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Textures/cubeTexture.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Animations/animation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$fxaaPostProcess$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/PostProcesses/fxaaPostProcess.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/PostProcesses/postProcess.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$RenderPipeline$2f$Pipelines$2f$defaultRenderingPipeline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/PostProcesses/RenderPipeline/Pipelines/defaultRenderingPipeline.js [app-ssr] (ecmascript)");
// Import additional Babylon.js modules for advanced features
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Node$2f$Blocks$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/core/Materials/Node/Blocks/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$loaders$2f$glTF$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/loaders/glTF/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$materials$2f$custom$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/materials/custom/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$post$2d$processes$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@babylonjs/post-processes/index.js [app-ssr] (ecmascript) <module evaluation>");
;
;
;
;
;
// Advanced post-processing manager with professional effects
class BabylonPostProcessingManager {
    scene;
    camera;
    engine;
    renderingPipeline = null;
    ssrPostProcess = null;
    // bloomPostProcess: BloomPostProcess | null = null; // Not available
    // chromaticAberrationPostProcess: ChromaticAberrationPostProcess | null = null; // Not available
    // grainPostProcess: GrainPostProcess | null = null; // Not available
    // vignettePostProcess: VignettePostProcess | null = null; // Not available
    constructor(scene, camera){
        this.scene = scene;
        this.camera = camera;
        this.engine = scene.getEngine();
        this.initializeAdvancedPipeline();
    }
    initializeAdvancedPipeline() {
        try {
            // Create default rendering pipeline with advanced features
            this.renderingPipeline = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$RenderPipeline$2f$Pipelines$2f$defaultRenderingPipeline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DefaultRenderingPipeline"]("defaultPipeline", true, this.scene, [
                this.camera
            ]);
            // Configure advanced post-processing effects
            if (this.renderingPipeline) {
                // Enable and configure bloom
                this.renderingPipeline.bloomEnabled = true;
                this.renderingPipeline.bloomThreshold = 0.8;
                this.renderingPipeline.bloomWeight = 0.3;
                this.renderingPipeline.bloomKernel = 64;
                this.renderingPipeline.bloomScale = 0.5;
                // Enable and configure tone mapping
                this.renderingPipeline.toneMappingEnabled = true;
                this.renderingPipeline.toneMappingType = TonemappingPostProcess.TONEMAP_ACES;
                // Enable FXAA anti-aliasing
                this.renderingPipeline.fxaaEnabled = true;
                // Enable and configure chromatic aberration
                this.renderingPipeline.chromaticAberrationEnabled = true;
                this.renderingPipeline.chromaticAberration.aberrationAmount = 30;
                this.renderingPipeline.chromaticAberration.radialIntensity = 1.0;
                // Enable and configure grain effect
                this.renderingPipeline.grainEnabled = true;
                this.renderingPipeline.grain.intensity = 10;
                this.renderingPipeline.grain.animated = true;
                // Enable and configure vignette
                this.renderingPipeline.vignetteEnabled = true;
                this.renderingPipeline.vignette.vignetteStretch = 0.2;
                this.renderingPipeline.vignette.vignetteCentreX = 0.5;
                this.renderingPipeline.vignette.vignetteCentreY = 0.5;
                this.renderingPipeline.vignette.vignetteWeight = 1.5;
                this.renderingPipeline.vignette.vignetteColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 0, 0, 0);
                // Enable depth of field for cinematic effect
                this.renderingPipeline.depthOfFieldEnabled = true;
                this.renderingPipeline.depthOfFieldBlurLevel = 0;
                this.renderingPipeline.depthOfField.focusDistance = 2000;
                this.renderingPipeline.depthOfField.focalLength = 50;
                this.renderingPipeline.depthOfField.fStop = 1.4;
                // Enable screen space reflections for advanced materials
                this.renderingPipeline.screenSpaceReflectionsEnabled = true;
                if (this.renderingPipeline.screenSpaceReflectionPostProcess) {
                    this.renderingPipeline.screenSpaceReflectionPostProcess.strength = 0.5;
                    this.renderingPipeline.screenSpaceReflectionPostProcess.reflectionSpecularFalloffExponent = 1.0;
                    this.renderingPipeline.screenSpaceReflectionPostProcess.maxDistance = 1000;
                }
            }
            console.log("Advanced post-processing pipeline initialized successfully");
        } catch (error) {
            console.error("Error initializing advanced post-processing:", error);
        }
    }
    updateSettings(settings) {
        if (this.renderingPipeline) {
            // Dynamic adjustment of post-processing effects
            if (settings.bloomIntensity !== undefined) {
                this.renderingPipeline.bloomWeight = settings.bloomIntensity;
            }
            if (settings.chromaticAberration !== undefined) {
                this.renderingPipeline.chromaticAberration.aberrationAmount = settings.chromaticAberration;
            }
            if (settings.grainIntensity !== undefined) {
                this.renderingPipeline.grain.intensity = settings.grainIntensity;
            }
            if (settings.vignetteStrength !== undefined) {
                this.renderingPipeline.vignette.vignetteWeight = settings.vignetteStrength;
            }
        }
    }
    dispose() {
        if (this.renderingPipeline) {
            this.renderingPipeline.dispose();
            this.renderingPipeline = null;
        }
        if (this.ssrPostProcess) {
            this.ssrPostProcess.dispose();
            this.ssrPostProcess = null;
        }
        if (this.bloomPostProcess) {
            this.bloomPostProcess.dispose();
            this.bloomPostProcess = null;
        }
        if (this.chromaticAberrationPostProcess) {
            this.chromaticAberrationPostProcess.dispose();
            this.chromaticAberrationPostProcess = null;
        }
        if (this.grainPostProcess) {
            this.grainPostProcess.dispose();
            this.grainPostProcess = null;
        }
        if (this.vignettePostProcess) {
            this.vignettePostProcess.dispose();
            this.vignettePostProcess = null;
        }
    }
}
class BabylonParticleSystem {
    canvas;
    engine;
    scene;
    camera;
    particleSystem;
    postProcessing;
    mouse;
    mouseTarget;
    time;
    colorSchemes;
    lights;
    interactiveObjects;
    animations;
    customShaders;
    dynamicTextures;
    audioAnalyzer;
    constructor(canvas){
        this.canvas = canvas;
        this.engine = null;
        this.scene = null;
        this.camera = null;
        this.particleSystem = null;
        this.postProcessing = null;
        this.mouse = {
            x: 0,
            y: 0
        };
        this.mouseTarget = {
            x: 0,
            y: 0
        };
        this.time = 0;
        this.colorSchemes = {
            cyberpunk: {
                primary: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 1, 1, 1),
                secondary: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](1, 0, 1, 1),
                accent: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 1, 0, 1)
            },
            matrix: {
                primary: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 1, 0, 1),
                secondary: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 0.8, 0, 1),
                accent: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 0.6, 0, 1)
            },
            neon: {
                primary: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](1, 0.4, 0, 1),
                secondary: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 0.5, 1, 1),
                accent: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0.5, 0, 1, 1)
            }
        };
        // Initialize new properties
        this.interactiveObjects = [];
        this.animations = [];
        this.customShaders = {};
        this.dynamicTextures = [];
        this.audioAnalyzer = null;
        this.init();
    }
    async init() {
        try {
            // Create Babylon.js engine
            this.engine = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Engines$2f$engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Engine"](this.canvas, true, {
                preserveDrawingBuffer: true,
                stencil: true,
                antialias: true,
                alpha: true,
                premultipliedAlpha: false
            });
            // Create scene
            this.scene = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$scene$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scene"](this.engine);
            this.scene.clearColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 0, 0, 0); // Transparent background
            // Create camera
            this.camera = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Cameras$2f$freeCamera$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FreeCamera"]("camera", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](0, 0, -10), this.scene);
            this.camera.setTarget(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"].Zero());
            // Setup advanced lighting
            this.setupAdvancedLighting();
            // Create particle system
            await this.createParticleSystem();
            // Create post-processing pipeline
            this.postProcessing = new BabylonPostProcessingManager(this.scene, this.camera);
            // Create interactive 3D objects
            this.createInteractiveObjects();
            // Create dynamic textures
            this.createDynamicTextures();
            // Create advanced post-processing
            this.createAdvancedPostProcessing();
            // Add mouse interaction
            this.addMouseInteraction();
            // Add event listeners
            this.addEventListeners();
            // Start render loop
            this.startRenderLoop();
            console.log("Babylon.js particle system initialized successfully");
        } catch (error) {
            console.error("Error initializing Babylon.js particle system:", error);
        }
    }
    setupAdvancedLighting() {
        // Main directional light (key light)
        const directionalLight = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$directionalLight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DirectionalLight"]("directionalLight", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](-1, -1, -1), this.scene);
        directionalLight.intensity = 1.2;
        directionalLight.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0.8, 0.9, 1.0);
        // Rim light for edge definition
        const rimLight = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$directionalLight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DirectionalLight"]("rimLight", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](1, 0.5, 1), this.scene);
        rimLight.intensity = 0.6;
        rimLight.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0, 1, 1);
        // Accent spot light
        const spotLight = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$spotLight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SpotLight"]("spotLight", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](0, 30, 0), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](0, -1, 0), Math.PI / 3, 2, this.scene);
        spotLight.intensity = 0.8;
        spotLight.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](1, 0, 1);
        // Ambient point lights for atmosphere
        const pointLight1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$pointLight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PointLight"]("pointLight1", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](-20, 10, -20), this.scene);
        pointLight1.intensity = 0.4;
        pointLight1.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0, 1, 0);
        const pointLight2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Lights$2f$pointLight$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PointLight"]("pointLight2", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](20, 10, 20), this.scene);
        pointLight2.intensity = 0.4;
        pointLight2.diffuse = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](1, 0.5, 0);
        // Store lights for animation
        this.lights = {
            directional: directionalLight,
            rim: rimLight,
            spot: spotLight,
            point1: pointLight1,
            point2: pointLight2
        };
    }
    async createParticleSystem() {
        // Create custom particle texture
        const particleTexture = this.createParticleTexture();
        // Create particle system
        this.particleSystem = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$particleSystem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ParticleSystem"]("particles", 2000, this.scene);
        this.particleSystem.particleTexture = particleTexture;
        // Set emitter
        this.particleSystem.emitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"].Zero();
        // Particle properties
        this.particleSystem.minEmitBox = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](-50, -50, -50);
        this.particleSystem.maxEmitBox = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](50, 50, 50);
        // Colors
        this.particleSystem.color1 = this.colorSchemes.cyberpunk.primary;
        this.particleSystem.color2 = this.colorSchemes.cyberpunk.secondary;
        this.particleSystem.colorDead = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 0, 0, 0);
        // Size
        this.particleSystem.minSize = 0.1;
        this.particleSystem.maxSize = 2.0;
        // Life time
        this.particleSystem.minLifeTime = 2.0;
        this.particleSystem.maxLifeTime = 8.0;
        // Emission rate
        this.particleSystem.emitRate = 100;
        // Blend mode
        this.particleSystem.blendMode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$particleSystem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ParticleSystem"].BLENDMODE_ONEONE;
        // Direction
        this.particleSystem.direction1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](-1, -1, -1);
        this.particleSystem.direction2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](1, 1, 1);
        // Angular speed
        this.particleSystem.minAngularSpeed = 0;
        this.particleSystem.maxAngularSpeed = Math.PI;
        // Speed
        this.particleSystem.minInitialRotation = 0;
        this.particleSystem.maxInitialRotation = Math.PI;
        // Gravity
        this.particleSystem.gravity = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](0, -9.81, 0);
        // Start the particle system
        this.particleSystem.start();
        // Create custom shader for advanced effects
        await this.createCustomShader();
    }
    createParticleTexture() {
        // Create a dynamic texture for particles
        const texture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$dynamicTexture$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DynamicTexture"]("particleTexture", 64, this.scene);
        const context = texture.getContext();
        // Draw a glowing circle
        const size = 64;
        const center = size / 2;
        const radius = size / 4;
        context.fillStyle = "rgba(0, 0, 0, 0)";
        context.fillRect(0, 0, size, size);
        // Create gradient for glow effect
        const gradient = context.createRadialGradient(center, center, 0, center, center, radius);
        gradient.addColorStop(0, "rgba(0, 255, 255, 1)");
        gradient.addColorStop(0.5, "rgba(0, 255, 255, 0.5)");
        gradient.addColorStop(1, "rgba(0, 255, 255, 0)");
        context.fillStyle = gradient;
        context.beginPath();
        context.arc(center, center, radius, 0, 2 * Math.PI);
        context.fill();
        texture.update();
        return texture;
    }
    async createCustomShader() {
        // Define custom vertex shader
        const vertexShader = `
      precision highp float;
      
      attribute vec3 position;
      attribute vec2 uv;
      attribute vec4 color;
      attribute float age;
      attribute float life;
      attribute vec3 velocity;
      
      uniform mat4 view;
      uniform mat4 projection;
      uniform float time;
      uniform vec2 mouse;
      uniform vec2 resolution;
      
      varying vec2 vUV;
      varying vec4 vColor;
      varying float vAge;
      
      void main() {
        vUV = uv;
        vColor = color;
        vAge = age / life;
        
        vec3 pos = position;
        
        // Wave animation
        float wave1 = sin(time * 0.002 + position.x * 0.01) * 15.0;
        float wave2 = cos(time * 0.003 + position.y * 0.008) * 10.0;
        float wave3 = sin(time * 0.001 + position.z * 0.005) * 8.0;
        
        pos.y += wave1 + wave2;
        pos.x += wave2 + wave3;
        pos.z += wave1 + wave3;
        
        // Mouse interaction
        vec2 mouseInfluence = (mouse - 0.5) * 2.0;
        float mouseDistance = length(mouseInfluence);
        float influence = 1.0 / (1.0 + mouseDistance * 0.1);
        
        pos.xy += mouseInfluence * influence * 50.0;
        
        gl_Position = projection * view * vec4(pos, 1.0);
        gl_PointSize = 2.0 * (1.0 - vAge) + 1.0;
      }
    `;
        // Define custom fragment shader
        const fragmentShader = `
      precision highp float;
      
      uniform sampler2D textureSampler;
      uniform float time;
      
      varying vec2 vUV;
      varying vec4 vColor;
      varying float vAge;
      
      void main() {
        vec4 textureColor = texture2D(textureSampler, vUV);
        
        // Fade out over lifetime
        float alpha = (1.0 - vAge) * textureColor.a;
        
        // Pulsing effect
        alpha *= 0.8 + 0.2 * sin(time * 0.005);
        
        gl_FragColor = vec4(vColor.rgb * textureColor.rgb, alpha);
      }
    `;
        // Register the shader
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Effect"].ShadersStore["customParticleVertexShader"] = vertexShader;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$effect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Effect"].ShadersStore["customParticleFragmentShader"] = fragmentShader;
    }
    addEventListeners() {
        // Resize handler
        window.addEventListener("resize", ()=>this.onWindowResize(), false);
        // Mouse move handler
        document.addEventListener("mousemove", (event)=>this.onMouseMove(event), false);
        // Touch move handler
        document.addEventListener("touchmove", (event)=>this.onTouchMove(event), false);
    }
    onWindowResize() {
        if (this.engine) {
            this.engine.resize();
        }
    }
    onMouseMove(event) {
        this.mouseTarget.x = event.clientX / window.innerWidth * 2 - 1;
        this.mouseTarget.y = -(event.clientY / window.innerHeight) * 2 + 1;
    }
    onTouchMove(event) {
        if (event.touches.length > 0) {
            const touch = event.touches[0];
            this.mouseTarget.x = touch.clientX / window.innerWidth * 2 - 1;
            this.mouseTarget.y = -(touch.clientY / window.innerHeight) * 2 + 1;
        }
    }
    startRenderLoop() {
        this.engine.runRenderLoop(()=>{
            this.update();
            this.scene.render();
        });
    }
    update() {
        this.time += this.engine.getDeltaTime();
        // Smooth mouse interpolation
        this.mouse.x += (this.mouseTarget.x - this.mouse.x) * 0.05;
        this.mouse.y += (this.mouseTarget.y - this.mouse.y) * 0.05;
        // Update particle system properties
        if (this.particleSystem) {
            // Dynamic color changes
            const colorPhase = Math.sin(this.time * 0.001) * 0.5 + 0.5;
            this.particleSystem.color1 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"].Lerp(this.colorSchemes.cyberpunk.primary, this.colorSchemes.cyberpunk.secondary, colorPhase);
        }
        // Animate lights for dynamic atmosphere
        if (this.lights) {
            const lightTime = this.time * 0.0005;
            // Animate spot light intensity
            this.lights.spot.intensity = 0.8 + Math.sin(lightTime * 3) * 0.3;
            // Rotate point lights
            const radius = 25;
            this.lights.point1.position.x = Math.cos(lightTime) * radius;
            this.lights.point1.position.z = Math.sin(lightTime) * radius;
            this.lights.point2.position.x = Math.cos(lightTime + Math.PI) * radius;
            this.lights.point2.position.z = Math.sin(lightTime + Math.PI) * radius;
            // Pulse point light intensities
            this.lights.point1.intensity = 0.4 + Math.sin(lightTime * 2) * 0.2;
            this.lights.point2.intensity = 0.4 + Math.cos(lightTime * 2.5) * 0.2;
        }
    }
    setColorScheme(scheme) {
        if (this.colorSchemes[scheme] && this.particleSystem) {
            this.particleSystem.color1 = this.colorSchemes[scheme].primary;
            this.particleSystem.color2 = this.colorSchemes[scheme].secondary;
        }
    }
    createInteractiveObjects() {
        if (!this.scene) return;
        // Create advanced floating geometric shapes with PBR materials
        const shapes = [
            {
                type: "box",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](-15, 5, 0),
                materialType: "metallic"
            },
            {
                type: "sphere",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](0, 8, -10),
                materialType: "glass"
            },
            {
                type: "cylinder",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](15, 3, 5),
                materialType: "iridescent"
            },
            {
                type: "torus",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](-8, -5, 8),
                materialType: "clearcoat"
            },
            {
                type: "dodecahedron",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](12, -3, -8),
                materialType: "anisotropic"
            },
            {
                type: "octahedron",
                position: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](-12, 6, 10),
                materialType: "subsurface"
            }
        ];
        shapes.forEach((shapeConfig, index)=>{
            let mesh;
            switch(shapeConfig.type){
                case "box":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MeshBuilder"].CreateBox(`interactiveBox${index}`, {
                        size: 2
                    }, this.scene);
                    break;
                case "sphere":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MeshBuilder"].CreateSphere(`interactiveSphere${index}`, {
                        diameter: 2.5,
                        segments: 32
                    }, this.scene);
                    break;
                case "cylinder":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MeshBuilder"].CreateCylinder(`interactiveCylinder${index}`, {
                        height: 3,
                        diameter: 2,
                        tessellation: 16
                    }, this.scene);
                    break;
                case "torus":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MeshBuilder"].CreateTorus(`interactiveTorus${index}`, {
                        diameter: 3,
                        thickness: 0.8,
                        tessellation: 32
                    }, this.scene);
                    break;
                case "dodecahedron":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MeshBuilder"].CreatePolyhedron(`interactiveDodecahedron${index}`, {
                        type: 2,
                        size: 1.5
                    }, this.scene);
                    break;
                case "octahedron":
                    mesh = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Meshes$2f$meshBuilder$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MeshBuilder"].CreatePolyhedron(`interactiveOctahedron${index}`, {
                        type: 1,
                        size: 1.8
                    }, this.scene);
                    break;
                default:
                    return;
            }
            mesh.position = shapeConfig.position;
            // Create advanced PBR material based on type
            const pbrMaterial = this.createAdvancedPBRMaterial(`pbrMat${index}`, shapeConfig.materialType);
            mesh.material = pbrMaterial;
            // Create wireframe overlay for cyberpunk effect
            const wireframeMaterial = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$standardMaterial$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["StandardMaterial"](`wireframeMat${index}`, this.scene);
            wireframeMaterial.wireframe = true;
            wireframeMaterial.emissiveColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](1, 0, 1);
            wireframeMaterial.alpha = 0.3;
            // Create wireframe clone
            const wireframeMesh = mesh.clone(`wireframe${index}`);
            wireframeMesh.material = wireframeMaterial;
            wireframeMesh.scaling = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](1.05, 1.05, 1.05);
            // Add complex floating animation with multiple axes
            const floatAnimationY = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].CreateAndStartAnimation(`floatY${index}`, mesh, "position.y", 30, 120 + index * 10, mesh.position.y, mesh.position.y + 2 + Math.sin(index) * 0.5, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].ANIMATIONLOOPMODE_YOYO);
            const floatAnimationX = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].CreateAndStartAnimation(`floatX${index}`, mesh, "position.x", 30, 180 + index * 15, mesh.position.x, mesh.position.x + Math.cos(index) * 1.5, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].ANIMATIONLOOPMODE_YOYO);
            // Add complex rotation animation
            const rotateAnimation = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].CreateAndStartAnimation(`rotate${index}`, mesh, "rotation", 30, 300 + index * 20, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"].Zero(), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](Math.PI * 2 * (1 + index * 0.1), Math.PI * 2 * (1 + index * 0.15), Math.PI * 2 * (1 + index * 0.05)), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].ANIMATIONLOOPMODE_CYCLE);
            // Add scaling pulse animation
            const scaleAnimation = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].CreateAndStartAnimation(`scale${index}`, mesh, "scaling", 30, 90 + index * 5, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](1, 1, 1), new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](1.1, 1.1, 1.1), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].ANIMATIONLOOPMODE_YOYO);
            // Store references
            this.interactiveObjects.push({
                mesh,
                wireframeMesh,
                material: pbrMaterial,
                wireframeMaterial,
                originalPosition: shapeConfig.position.clone(),
                floatAnimationY,
                floatAnimationX,
                rotateAnimation,
                scaleAnimation,
                materialType: shapeConfig.materialType
            });
        });
    }
    createAdvancedPBRMaterial(name, materialType) {
        const pbr = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$PBR$2f$pbrMaterial$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PBRMaterial"](name, this.scene);
        // Create environment texture for reflections
        const envTexture = this.createEnvironmentTexture();
        pbr.environmentTexture = envTexture;
        switch(materialType){
            case "metallic":
                // Highly reflective metallic material
                pbr.baseColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0.2, 0.6, 1.0);
                pbr.metallic = 1.0;
                pbr.roughness = 0.1;
                pbr.enableSpecularAntiAliasing = true;
                break;
            case "glass":
                // Refractive glass material
                pbr.baseColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0.9, 0.95, 1.0);
                pbr.metallic = 0.0;
                pbr.roughness = 0.0;
                pbr.alpha = 0.1;
                pbr.indexOfRefraction = 1.52;
                pbr.linkRefractionWithTransparency = true;
                pbr.subSurface.isRefractionEnabled = true;
                pbr.subSurface.refractionIntensity = 1.0;
                break;
            case "iridescent":
                // Iridescent material with color shifting
                pbr.baseColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0.1, 0.1, 0.1);
                pbr.metallic = 1.0;
                pbr.roughness = 0.0;
                pbr.iridescence.isEnabled = true;
                pbr.iridescence.intensity = 1.0;
                pbr.iridescence.indexOfRefraction = 1.3;
                pbr.iridescence.thickness = 400;
                break;
            case "clearcoat":
                // Clear coat material for automotive-like finish
                pbr.baseColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0.8, 0.1, 0.1);
                pbr.metallic = 0.0;
                pbr.roughness = 0.8;
                pbr.clearCoat.isEnabled = true;
                pbr.clearCoat.intensity = 1.0;
                pbr.clearCoat.roughness = 0.1;
                pbr.clearCoat.indexOfRefraction = 1.5;
                break;
            case "anisotropic":
                // Anisotropic material for brushed metal effect
                pbr.baseColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0.7, 0.7, 0.9);
                pbr.metallic = 1.0;
                pbr.roughness = 0.3;
                pbr.anisotropy.isEnabled = true;
                pbr.anisotropy.intensity = 1.0;
                pbr.anisotropy.direction = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](1, 0, 0);
                break;
            case "subsurface":
                // Subsurface scattering for organic materials
                pbr.baseColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0.9, 0.7, 0.6);
                pbr.metallic = 0.0;
                pbr.roughness = 0.4;
                pbr.subSurface.isTranslucencyEnabled = true;
                pbr.subSurface.translucencyIntensity = 0.8;
                pbr.subSurface.tintColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](1.0, 0.8, 0.6);
                break;
            default:
                // Default cyberpunk material
                pbr.baseColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0, 1, 1);
                pbr.metallic = 0.8;
                pbr.roughness = 0.2;
                pbr.emissiveColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0, 0.2, 0.2);
                break;
        }
        return pbr;
    }
    createEnvironmentTexture() {
        // Create a simple procedural environment texture
        const envTexture = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$cubeTexture$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CubeTexture"].CreateFromImages([
            this.createSkyTexture("px"),
            this.createSkyTexture("nx"),
            this.createSkyTexture("py"),
            this.createSkyTexture("ny"),
            this.createSkyTexture("pz"),
            this.createSkyTexture("nz")
        ], this.scene);
        return envTexture;
    }
    createSkyTexture(face) {
        // Create a simple gradient sky texture
        const canvas = document.createElement("canvas");
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext("2d");
        // Create gradient based on face
        const gradient = ctx.createLinearGradient(0, 0, 0, 512);
        gradient.addColorStop(0, "#001122");
        gradient.addColorStop(0.5, "#003366");
        gradient.addColorStop(1, "#000011");
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 512);
        // Add some stars
        for(let i = 0; i < 100; i++){
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const brightness = Math.random();
            ctx.fillStyle = `rgba(255, 255, 255, ${brightness})`;
            ctx.fillRect(x, y, 1, 1);
        }
        return canvas.toDataURL();
    }
    createDynamicTextures() {
        if (!this.scene) return;
        // Create animated noise texture
        const noiseTexture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$dynamicTexture$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DynamicTexture"]("noiseTexture", {
            width: 512,
            height: 512
        }, this.scene);
        const noiseContext = noiseTexture.getContext();
        // Generate noise pattern
        const generateNoise = ()=>{
            // Clear the canvas
            noiseContext.fillStyle = "rgba(0, 0, 0, 0)";
            noiseContext.fillRect(0, 0, 512, 512);
            // Generate noise using canvas drawing
            for(let i = 0; i < 1000; i++){
                const x = Math.random() * 512;
                const y = Math.random() * 512;
                const noise = Math.random();
                noiseContext.fillStyle = `rgba(${noise * 25}, ${noise * 200}, ${noise * 255}, 0.8)`;
                noiseContext.fillRect(x, y, 2, 2);
            }
            noiseTexture.update();
        };
        // Update noise every frame
        this.scene.registerBeforeRender(()=>{
            if (Math.random() < 0.1) {
                // Update 10% of frames for performance
                generateNoise();
            }
        });
        this.dynamicTextures.push(noiseTexture);
        // Create holographic grid texture
        const gridTexture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$dynamicTexture$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DynamicTexture"]("gridTexture", {
            width: 256,
            height: 256
        }, this.scene);
        const gridContext = gridTexture.getContext();
        const drawGrid = ()=>{
            gridContext.fillStyle = "rgba(0, 0, 0, 0)";
            gridContext.fillRect(0, 0, 256, 256);
            gridContext.strokeStyle = "rgba(0, 255, 255, 0.8)";
            gridContext.lineWidth = 2;
            // Draw grid lines
            for(let i = 0; i <= 256; i += 32){
                gridContext.beginPath();
                gridContext.moveTo(i, 0);
                gridContext.lineTo(i, 256);
                gridContext.stroke();
                gridContext.beginPath();
                gridContext.moveTo(0, i);
                gridContext.lineTo(256, i);
                gridContext.stroke();
            }
            gridTexture.update();
        };
        drawGrid();
        this.dynamicTextures.push(gridTexture);
    }
    createAdvancedPostProcessing() {
        if (!this.scene || !this.camera) return;
        // Create FXAA anti-aliasing
        const fxaaPostProcess = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$fxaaPostProcess$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FxaaPostProcess"]("fxaa", 1.0, this.camera);
        // Create custom glitch effect
        const glitchEffect = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PostProcess"]("glitch", "./shaders/glitch", [
            "time",
            "intensity"
        ], [
            "textureSampler"
        ], 1.0, this.camera);
        glitchEffect.onApply = (effect)=>{
            effect.setFloat("time", this.time * 0.001);
            effect.setFloat("intensity", 0.1 + Math.sin(this.time * 0.005) * 0.05);
        };
        // Create chromatic aberration effect
        const chromaticEffect = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$PostProcesses$2f$postProcess$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PostProcess"]("chromatic", "./shaders/chromatic", [
            "aberrationAmount"
        ], [
            "textureSampler"
        ], 1.0, this.camera);
        chromaticEffect.onApply = (effect)=>{
            effect.setFloat("aberrationAmount", 0.002 + Math.sin(this.time * 0.003) * 0.001);
        };
    }
    addMouseInteraction() {
        if (!this.scene || !this.canvas) return;
        // Add mouse/touch interaction with 3D objects
        this.scene.onPointerObservable.add((pointerInfo)=>{
            if (pointerInfo.pickInfo && pointerInfo.pickInfo.hit) {
                const pickedMesh = pointerInfo.pickInfo.pickedMesh;
                // Check if it's one of our interactive objects
                const interactiveObj = this.interactiveObjects.find((obj)=>obj.mesh === pickedMesh || obj.wireframeMesh === pickedMesh);
                if (interactiveObj) {
                    switch(pointerInfo.type){
                        case 1:
                            this.onObjectClick(interactiveObj);
                            break;
                        case 2:
                            break;
                        case 4:
                            this.onObjectHover(interactiveObj);
                            break;
                    }
                }
            }
        });
        // Add mouse move tracking for particle attraction
        this.canvas.addEventListener("mousemove", (event)=>{
            const rect = this.canvas.getBoundingClientRect();
            this.mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;
            this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
            // Smooth mouse target interpolation
            this.mouseTarget.x += (this.mouse.x - this.mouseTarget.x) * 0.1;
            this.mouseTarget.y += (this.mouse.y - this.mouseTarget.y) * 0.1;
        });
    }
    onObjectClick(interactiveObj) {
        // Create explosion effect
        const explosionParticles = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Particles$2f$particleSystem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ParticleSystem"]("explosion", 100, this.scene);
        explosionParticles.particleTexture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Materials$2f$Textures$2f$dynamicTexture$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DynamicTexture"]("explosionTexture", 64, this.scene);
        // Set explosion properties
        explosionParticles.emitter = interactiveObj.mesh;
        explosionParticles.minEmitBox = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](-0.5, -0.5, -0.5);
        explosionParticles.maxEmitBox = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](0.5, 0.5, 0.5);
        explosionParticles.color1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](1, 1, 0, 1);
        explosionParticles.color2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](1, 0.5, 0, 1);
        explosionParticles.colorDead = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color4"](0, 0, 0, 0);
        explosionParticles.minSize = 0.1;
        explosionParticles.maxSize = 0.5;
        explosionParticles.minLifeTime = 0.3;
        explosionParticles.maxLifeTime = 1.0;
        explosionParticles.emitRate = 200;
        explosionParticles.minEmitPower = 5;
        explosionParticles.maxEmitPower = 10;
        explosionParticles.start();
        // Stop explosion after short time
        setTimeout(()=>{
            explosionParticles.stop();
            setTimeout(()=>explosionParticles.dispose(), 2000);
        }, 200);
        // Scale animation on click
        const scaleUp = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].CreateAndStartAnimation("scaleUp", interactiveObj.mesh, "scaling", 30, 15, interactiveObj.mesh.scaling, interactiveObj.mesh.scaling.scale(1.3), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].ANIMATIONLOOPMODE_CONSTANT);
        setTimeout(()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].CreateAndStartAnimation("scaleDown", interactiveObj.mesh, "scaling", 30, 15, interactiveObj.mesh.scaling, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"](1, 1, 1), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Animations$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Animation"].ANIMATIONLOOPMODE_CONSTANT);
        }, 500);
    }
    onObjectHover(interactiveObj) {
        // Increase glow on hover
        if (interactiveObj.material) {
            interactiveObj.material.emissiveColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0.2, 1.2, 1.2);
        }
        // Reset glow after delay
        setTimeout(()=>{
            if (interactiveObj.material) {
                interactiveObj.material.emissiveColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babylonjs$2f$core$2f$Maths$2f$math$2e$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color3"](0, 1, 1);
            }
        }, 200);
    }
    destroy() {
        // Dispose interactive objects
        this.interactiveObjects.forEach((obj)=>{
            if (obj.mesh) obj.mesh.dispose();
            if (obj.wireframeMesh) obj.wireframeMesh.dispose();
            if (obj.material) obj.material.dispose();
            if (obj.wireframeMaterial) obj.wireframeMaterial.dispose();
        });
        this.interactiveObjects = [];
        // Dispose dynamic textures
        this.dynamicTextures.forEach((texture)=>{
            if (texture) texture.dispose();
        });
        this.dynamicTextures = [];
        // Dispose animations
        this.animations.forEach((animation)=>{
            if (animation) animation.dispose();
        });
        this.animations = [];
        // Dispose post-processing
        if (this.postProcessing) {
            this.postProcessing.dispose();
        }
        // Dispose particle system
        if (this.particleSystem) {
            this.particleSystem.dispose();
        }
        // Dispose scene
        if (this.scene) {
            this.scene.dispose();
        }
        // Dispose engine
        if (this.engine) {
            this.engine.dispose();
        }
    }
}
const __TURBOPACK__default__export__ = BabylonParticleSystem;
}),

};

//# sourceMappingURL=src_scripts_babylon-particles_ts_3efdf04c._.js.map