import type { IAbstractAudioOutNodeOptions } from "./abstractAudioOutNode.js";
import { AbstractAudioOutNode } from "./abstractAudioOutNode.js";
import type { AudioEngineV2 } from "./audioEngineV2.js";
/** @internal */
export interface IAbstractAudioBusOptions extends IAbstractAudioOutNodeOptions {
}
/**
 * Abstract class representing an audio bus with volume control.
 *
 * An audio bus is a node in the audio graph that can have multiple inputs and outputs. It is typically used to group
 * sounds together and apply effects to them.
 */
export declare abstract class AbstractAudioBus extends AbstractAudioOutNode {
    protected constructor(name: string, engine: AudioEngineV2);
}
