{"version": 3, "file": "nativeHardwareTexture.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/Native/nativeHardwareTexture.ts"], "names": [], "mappings": "AAIA,gBAAgB;AAChB,MAAM,OAAO,qBAAqB;IAI9B,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,YAAY,eAA8B,EAAE,MAAqB;QAC7D,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC9B,CAAC;IAEM,QAAQ,KAAU,CAAC;IAEnB,GAAG,CAAC,eAA8B;QACrC,IAAI,CAAC,cAAc,GAAG,eAAe,CAAC;IAC1C,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;IAC/B,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;CACJ", "sourcesContent": ["import type { IHardwareTextureWrapper } from \"../../Materials/Textures/hardwareTextureWrapper\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { INativeEngine, NativeTexture } from \"./nativeInterfaces\";\r\n\r\n/** @internal */\r\nexport class NativeHardwareTexture implements IHardwareTextureWrapper {\r\n    private readonly _engine: INativeEngine;\r\n    private _nativeTexture: Nullable<NativeTexture>;\r\n\r\n    public get underlyingResource(): Nullable<NativeTexture> {\r\n        return this._nativeTexture;\r\n    }\r\n\r\n    constructor(existingTexture: NativeTexture, engine: INativeEngine) {\r\n        this._engine = engine;\r\n        this.set(existingTexture);\r\n    }\r\n\r\n    public setUsage(): void {}\r\n\r\n    public set(hardwareTexture: NativeTexture) {\r\n        this._nativeTexture = hardwareTexture;\r\n    }\r\n\r\n    public reset() {\r\n        this._nativeTexture = null;\r\n    }\r\n\r\n    public release() {\r\n        if (this._nativeTexture) {\r\n            this._engine.deleteTexture(this._nativeTexture);\r\n        }\r\n\r\n        this.reset();\r\n    }\r\n}\r\n"]}