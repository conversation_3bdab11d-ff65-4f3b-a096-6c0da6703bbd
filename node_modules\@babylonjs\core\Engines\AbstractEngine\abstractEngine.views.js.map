{"version": 3, "file": "abstractEngine.views.js", "sourceRoot": "", "sources": ["../../../../../dev/core/src/Engines/AbstractEngine/abstractEngine.views.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAEnD;;;GAGG;AACH,MAAM,OAAO,UAAU;CAkBtB;AA4DD,MAAM,4BAA4B,GAAG,IAAI,UAAU,EAAc,CAAC;AAClE,MAAM,2BAA2B,GAAG,IAAI,UAAU,EAAc,CAAC;AAEjE,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,8BAA8B,EAAE;IAC5E,GAAG,EAAE;QACD,OAAO,4BAA4B,CAAC;IACxC,CAAC;CACJ,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,6BAA6B,EAAE;IAC3E,GAAG,EAAE;QACD,OAAO,2BAA2B,CAAC;IACvC,CAAC;CACJ,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,cAAc,EAAE;IAC5D,GAAG,EAAE;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IACD,GAAG,EAAE,UAAgC,KAAkB;QACnD,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC;QAClC,CAAC;IACL,CAAC;CACJ,CAAC,CAAC;AAEH,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG;IACvC,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1D,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,MAAyB,EAAE,MAA0B,EAAE,eAAyB;IAC9H,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IACpB,CAAC;IAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC/C,IAAI,YAAY,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;QAClC,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;IACxC,CAAC;IAED,MAAM,OAAO,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC;IACnH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAEzB,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QACnC,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE;YAChC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,OAAO,OAAO,CAAC;AACnB,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,MAAyB;IACzE,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACzB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAEvC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAChC,CAAC;YACD,MAAM;QACV,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,IAAgB;IACjE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACxC,IAAI,CAAC,OAAO,EAAE,CAAC;QACX,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAG,CAAC;IAE1C,4BAA4B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IACnD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,IAAI,aAAa,GAAqB,IAAI,CAAC;IAC3C,IAAI,cAAc,GAAuB,IAAI,CAAC;IAC9C,IAAI,KAAK,GAAoB,IAAI,CAAC;IAClC,IAAI,MAAM,EAAE,CAAC;QACT,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QAEzE,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC;QACnC,cAAc,GAAG,KAAK,CAAC,aAAa,CAAC;QAErC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACxB,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;QACjC,CAAC;aAAM,CAAC;YACJ,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC;YAC5B,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;QAC/B,CAAC;IACL,CAAC;IACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAEvB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;SAAM,CAAC;QACJ,YAAY;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC1E,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAE5E,MAAM,WAAW,GAAG,KAAK,KAAK,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,IAAI,MAAM,KAAK,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC;QAC3I,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,YAAY,IAAI,WAAW,EAAE,CAAC;YAC3D,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;YACrB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;YACvB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAChC,CAAC;IACL,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,mBAAmB;IACnB,IAAI,CAAC,YAAY,EAAE,CAAC;IAEpB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAExB,iBAAiB;IACjB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACzD,CAAC;IACD,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAEhC,UAAU;IACV,IAAI,KAAK,EAAE,CAAC;QACR,KAAK,CAAC,aAAa,GAAG,cAAc,CAAC;QACrC,KAAK,CAAC,YAAY,GAAG,aAAa,CAAC;IACvC,CAAC;IACD,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAClD,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,cAAc,CAAC,SAAS,CAAC,YAAY,GAAG;IACpC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzC,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAEzC,IAAI,CAAC,MAAM,EAAE,CAAC;QACV,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,IAAI,gBAAgB,CAAC;IACrB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAChB,SAAS;QACb,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,oEAAoE;QACpE,IAAI,MAAM,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC;YAC/B,gBAAgB,GAAG,IAAI,CAAC;YACxB,SAAS;QACb,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED,IAAI,gBAAgB,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IAEvB,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC", "sourcesContent": ["import type { Camera } from \"../../Cameras/camera\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Scene } from \"../../scene\";\r\nimport { Observable } from \"../../Misc/observable\";\r\nimport { AbstractEngine } from \"../abstractEngine\";\r\n\r\n/**\r\n * Class used to define an additional view for the engine\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/multiCanvas\r\n */\r\nexport class EngineView {\r\n    /**\r\n     * A randomly generated unique id\r\n     */\r\n    readonly id: string;\r\n    /** Defines the canvas where to render the view */\r\n    target: HTMLCanvasElement;\r\n    /**\r\n     * Defines an optional camera or array of cameras used to render the view (will use active camera / cameras else)\r\n     * Support for array of cameras @since\r\n     */\r\n    camera?: Camera | Camera[];\r\n    /** Indicates if the destination view canvas should be cleared before copying the parent canvas. Can help if the scene clear color has alpha < 1 */\r\n    clearBeforeCopy?: boolean;\r\n    /** Indicates if the view is enabled (true by default) */\r\n    enabled: boolean;\r\n    /** Defines a custom function to handle canvas size changes. (the canvas to render into is provided to the callback) */\r\n    customResize?: (canvas: HTMLCanvasElement) => void;\r\n}\r\n\r\ndeclare module \"../../Engines/abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /** @internal */\r\n        _inputElement: Nullable<HTMLElement>;\r\n\r\n        /**\r\n         * Gets or sets the  HTML element to use for attaching events\r\n         */\r\n        inputElement: Nullable<HTMLElement>;\r\n\r\n        /**\r\n         * Observable to handle when a change to inputElement occurs\r\n         * @internal\r\n         */\r\n        _onEngineViewChanged?: () => void;\r\n\r\n        /**\r\n         * Will be triggered before the view renders\r\n         */\r\n        readonly onBeforeViewRenderObservable: Observable<EngineView>;\r\n        /**\r\n         * Will be triggered after the view rendered\r\n         */\r\n        readonly onAfterViewRenderObservable: Observable<EngineView>;\r\n\r\n        /**\r\n         * Gets the current engine view\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/scene/multiCanvas\r\n         */\r\n        activeView: Nullable<EngineView>;\r\n\r\n        /** Gets or sets the list of views */\r\n        views: EngineView[];\r\n\r\n        /**\r\n         * Register a new child canvas\r\n         * @param canvas defines the canvas to register\r\n         * @param camera defines an optional camera or array of cameras to use with this canvas (it will overwrite the scene.activeCamera / scene.activeCameras for this view). Support for array of cameras @since\r\n         * @param clearBeforeCopy Indicates if the destination view canvas should be cleared before copying the parent canvas. Can help if the scene clear color has alpha \\< 1\r\n         * @returns the associated view\r\n         */\r\n        registerView(canvas: HTMLCanvasElement, camera?: Camera | Camera[], clearBeforeCopy?: boolean): EngineView;\r\n\r\n        /**\r\n         * Remove a registered child canvas\r\n         * @param canvas defines the canvas to remove\r\n         * @returns the current engine\r\n         */\r\n        unRegisterView(canvas: HTMLCanvasElement): AbstractEngine;\r\n\r\n        /**\r\n         * @internal\r\n         */\r\n        _renderViewStep(view: EngineView): boolean;\r\n    }\r\n}\r\n\r\nconst OnBeforeViewRenderObservable = new Observable<EngineView>();\r\nconst OnAfterViewRenderObservable = new Observable<EngineView>();\r\n\r\nObject.defineProperty(AbstractEngine.prototype, \"onBeforeViewRenderObservable\", {\r\n    get: function (this: AbstractEngine) {\r\n        return OnBeforeViewRenderObservable;\r\n    },\r\n});\r\n\r\nObject.defineProperty(AbstractEngine.prototype, \"onAfterViewRenderObservable\", {\r\n    get: function (this: AbstractEngine) {\r\n        return OnAfterViewRenderObservable;\r\n    },\r\n});\r\n\r\nObject.defineProperty(AbstractEngine.prototype, \"inputElement\", {\r\n    get: function (this: AbstractEngine) {\r\n        return this._inputElement;\r\n    },\r\n    set: function (this: AbstractEngine, value: HTMLElement) {\r\n        if (this._inputElement !== value) {\r\n            this._inputElement = value;\r\n            this._onEngineViewChanged?.();\r\n        }\r\n    },\r\n});\r\n\r\nAbstractEngine.prototype.getInputElement = function (): Nullable<HTMLElement> {\r\n    return this.inputElement || this.getRenderingCanvas();\r\n};\r\n\r\nAbstractEngine.prototype.registerView = function (canvas: HTMLCanvasElement, camera?: Camera | Camera[], clearBeforeCopy?: boolean): EngineView {\r\n    if (!this.views) {\r\n        this.views = [];\r\n    }\r\n\r\n    for (const view of this.views) {\r\n        if (view.target === canvas) {\r\n            return view;\r\n        }\r\n    }\r\n\r\n    const masterCanvas = this.getRenderingCanvas();\r\n    if (masterCanvas) {\r\n        canvas.width = masterCanvas.width;\r\n        canvas.height = masterCanvas.height;\r\n    }\r\n\r\n    const newView = { target: canvas, camera, clearBeforeCopy, enabled: true, id: (Math.random() * 100000).toFixed() };\r\n    this.views.push(newView);\r\n\r\n    if (camera && !Array.isArray(camera)) {\r\n        camera.onDisposeObservable.add(() => {\r\n            this.unRegisterView(canvas);\r\n        });\r\n    }\r\n\r\n    return newView;\r\n};\r\n\r\nAbstractEngine.prototype.unRegisterView = function (canvas: HTMLCanvasElement): AbstractEngine {\r\n    if (!this.views || this.views.length === 0) {\r\n        return this;\r\n    }\r\n\r\n    for (const view of this.views) {\r\n        if (view.target === canvas) {\r\n            const index = this.views.indexOf(view);\r\n\r\n            if (index !== -1) {\r\n                this.views.splice(index, 1);\r\n            }\r\n            break;\r\n        }\r\n    }\r\n\r\n    return this;\r\n};\r\n\r\nAbstractEngine.prototype._renderViewStep = function (view: EngineView): boolean {\r\n    const canvas = view.target;\r\n    const context = canvas.getContext(\"2d\");\r\n    if (!context) {\r\n        return true;\r\n    }\r\n    const parent = this.getRenderingCanvas()!;\r\n\r\n    OnBeforeViewRenderObservable.notifyObservers(view);\r\n    const camera = view.camera;\r\n    let previewCamera: Nullable<Camera> = null;\r\n    let previewCameras: Nullable<Camera[]> = null;\r\n    let scene: Nullable<Scene> = null;\r\n    if (camera) {\r\n        scene = Array.isArray(camera) ? camera[0].getScene() : camera.getScene();\r\n\r\n        previewCamera = scene.activeCamera;\r\n        previewCameras = scene.activeCameras;\r\n\r\n        if (Array.isArray(camera)) {\r\n            scene.activeCameras = camera;\r\n        } else {\r\n            scene.activeCamera = camera;\r\n            scene.activeCameras = null;\r\n        }\r\n    }\r\n    this.activeView = view;\r\n\r\n    if (view.customResize) {\r\n        view.customResize(canvas);\r\n    } else {\r\n        // Set sizes\r\n        const width = Math.floor(canvas.clientWidth / this._hardwareScalingLevel);\r\n        const height = Math.floor(canvas.clientHeight / this._hardwareScalingLevel);\r\n\r\n        const dimsChanged = width !== canvas.width || parent.width !== canvas.width || height !== canvas.height || parent.height !== canvas.height;\r\n        if (canvas.clientWidth && canvas.clientHeight && dimsChanged) {\r\n            canvas.width = width;\r\n            canvas.height = height;\r\n            this.setSize(width, height);\r\n        }\r\n    }\r\n\r\n    if (!parent.width || !parent.height) {\r\n        return false;\r\n    }\r\n\r\n    // Render the frame\r\n    this._renderFrame();\r\n\r\n    this.flushFramebuffer();\r\n\r\n    // Copy to target\r\n    if (view.clearBeforeCopy) {\r\n        context.clearRect(0, 0, parent.width, parent.height);\r\n    }\r\n    context.drawImage(parent, 0, 0);\r\n\r\n    // Restore\r\n    if (scene) {\r\n        scene.activeCameras = previewCameras;\r\n        scene.activeCamera = previewCamera;\r\n    }\r\n    OnAfterViewRenderObservable.notifyObservers(view);\r\n    return true;\r\n};\r\n\r\nAbstractEngine.prototype._renderViews = function () {\r\n    if (!this.views || this.views.length === 0) {\r\n        return false;\r\n    }\r\n\r\n    const parent = this.getRenderingCanvas();\r\n\r\n    if (!parent) {\r\n        return false;\r\n    }\r\n\r\n    let inputElementView;\r\n    for (const view of this.views) {\r\n        if (!view.enabled) {\r\n            continue;\r\n        }\r\n        const canvas = view.target;\r\n        // Always render the view correspondent to the inputElement for last\r\n        if (canvas === this.inputElement) {\r\n            inputElementView = view;\r\n            continue;\r\n        }\r\n\r\n        if (!this._renderViewStep(view)) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    if (inputElementView) {\r\n        if (!this._renderViewStep(inputElementView)) {\r\n            return false;\r\n        }\r\n    }\r\n\r\n    this.activeView = null;\r\n\r\n    return true;\r\n};\r\n"]}