{"version": 3, "file": "spatialWebAudioSubNode.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/AudioV2/webAudio/subNodes/spatialWebAudioSubNode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AACzE,OAAO,EAAE,oBAAoB,EAAE,MAAM,kDAAkD,CAAC;AACxF,OAAO,EAAE,qBAAqB,EAAE,MAAM,wDAAwD,CAAC;AAC/F,OAAO,EAAE,2BAA2B,EAAE,MAAM,0CAA0C,CAAC;AAIvF,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAChC,MAAM,aAAa,GAAG,IAAI,UAAU,EAAE,CAAC;AACvC,MAAM,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;AAEjC,SAAS,GAAG,CAAC,OAAe;IACxB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;AACrC,CAAC;AAED,SAAS,GAAG,CAAC,OAAe;IACxB,OAAO,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACrC,CAAC;AAED,gBAAgB;AAChB,4DAA4D;AAC5D,MAAM,CAAC,KAAK,UAAU,+BAA+B,CAAC,MAAuB;IACzE,OAAO,IAAI,uBAAuB,CAAC,MAAM,CAAC,CAAC;AAC/C,CAAC;AAED,gBAAgB;AAChB,MAAM,OAAO,uBAAwB,SAAQ,oBAAoB;IAwB7D,gBAAgB;IAChB,YAAmB,MAAuB;QACtC,KAAK,CAAC,MAAM,CAAC,CAAC;QAzBV,kBAAa,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACxC,kBAAa,GAAY,OAAO,CAAC,IAAI,EAAE,CAAC;QACxC,4BAAuB,GAAe,IAAI,UAAU,EAAE,CAAC;QAW/D,gBAAgB;QACA,aAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAClE,gBAAgB;QACA,aAAQ,GAAY,qBAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC3E,gBAAgB;QACA,uBAAkB,GAAe,qBAAqB,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAS9F,IAAI,CAAC,IAAI,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEjD,IAAI,CAAC,aAAa,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrF,IAAI,CAAC,aAAa,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrF,IAAI,CAAC,aAAa,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAErF,IAAI,CAAC,UAAU,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/E,IAAI,CAAC,UAAU,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC/E,IAAI,CAAC,UAAU,GAAG,IAAI,2BAA2B,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnF,CAAC;IAED,gBAAgB;IACA,OAAO;QACnB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED,gBAAgB;IAChB,IAAW,cAAc;QACrB,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACzC,CAAC;IAED,IAAW,cAAc,CAAC,KAAa;QACnC,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,gBAAgB;IAChB,IAAW,cAAc;QACrB,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACzC,CAAC;IAED,IAAW,cAAc,CAAC,KAAa;QACnC,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,gBAAgB;IAChB,IAAW,eAAe;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACnC,CAAC;IAED,IAAW,eAAe,CAAC,KAAa;QACpC,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IACpC,CAAC;IAED,gBAAgB;IAChB,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACnC,CAAC;IAED,IAAW,aAAa,CAAC,KAA2C;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAEhC,0DAA0D;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,GAAG,KAAK,CAAC;QAC5C,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACxC,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IACjC,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,gBAAgB;IAChB,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;IACjC,CAAC;IAED,IAAW,WAAW,CAAC,KAAa;QAChC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,gBAAgB;IAChB,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;IAClC,CAAC;IAED,IAAW,YAAY,CAAC,KAA4B;QAChD,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;IACnC,CAAC;IAED,gBAAgB;IAChB,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;IACnC,CAAC;IAED,IAAW,aAAa,CAAC,KAAa;QAClC,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IACpC,CAAC;IAED,gBAAgB;IAChB,IAAW,OAAO;QACd,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,gBAAgB;IAChB,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtD,OAAO;QACX,CAAC;QAED,6GAA6G;QAC7G,wFAAwF;QACxF,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YAC3G,OAAO;QACX,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE9C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED,gBAAgB;IACT,eAAe;QAClB,6GAA6G;QAC7G,wFAAwF;QACxF,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;YACpH,OAAO;QACX,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC3E,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAChD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnE,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9D,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;YAClG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;aAAM,CAAC;YACJ,OAAO;QACX,CAAC;QAED,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACrD,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAE1E,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC;IACjD,CAAC;IAEkB,QAAQ,CAAC,IAAqB;QAC7C,MAAM,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,wFAAwF;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEkB,WAAW,CAAC,IAAqB;QAChD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,CAAC,YAAY,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gBAAgB;IACT,YAAY;QACf,OAAO,yBAAyB,CAAC;IACrC,CAAC;CACJ", "sourcesContent": ["import { Matrix, Quaternion, Vector3 } from \"../../../Maths/math.vector\";\r\nimport { _SpatialAudioSubNode } from \"../../abstractAudio/subNodes/spatialAudioSubNode\";\r\nimport { _SpatialAudioDefaults } from \"../../abstractAudio/subProperties/abstractSpatialAudio\";\r\nimport { _WebAudioParameterComponent } from \"../components/webAudioParameterComponent\";\r\nimport type { _WebAudioEngine } from \"../webAudioEngine\";\r\nimport type { IWebAudioInNode } from \"../webAudioNode\";\r\n\r\nconst TmpMatrix = Matrix.Zero();\r\nconst TmpQuaternion = new Quaternion();\r\nconst TmpVector = Vector3.Zero();\r\n\r\nfunction D2r(degrees: number): number {\r\n    return (degrees * Math.PI) / 180;\r\n}\r\n\r\nfunction R2d(radians: number): number {\r\n    return (radians * 180) / Math.PI;\r\n}\r\n\r\n/** @internal */\r\n// eslint-disable-next-line @typescript-eslint/require-await\r\nexport async function _CreateSpatialAudioSubNodeAsync(engine: _WebAudioEngine): Promise<_SpatialAudioSubNode> {\r\n    return new _SpatialWebAudioSubNode(engine);\r\n}\r\n\r\n/** @internal */\r\nexport class _SpatialWebAudioSubNode extends _SpatialAudioSubNode {\r\n    private _lastPosition: Vector3 = Vector3.Zero();\r\n    private _lastRotation: Vector3 = Vector3.Zero();\r\n    private _lastRotationQuaternion: Quaternion = new Quaternion();\r\n    private _orientationX: _WebAudioParameterComponent;\r\n    private _orientationY: _WebAudioParameterComponent;\r\n    private _orientationZ: _WebAudioParameterComponent;\r\n    private _positionX: _WebAudioParameterComponent;\r\n    private _positionY: _WebAudioParameterComponent;\r\n    private _positionZ: _WebAudioParameterComponent;\r\n\r\n    /** @internal */\r\n    public override readonly engine: _WebAudioEngine;\r\n\r\n    /** @internal */\r\n    public readonly position = _SpatialAudioDefaults.position.clone();\r\n    /** @internal */\r\n    public readonly rotation: Vector3 = _SpatialAudioDefaults.rotation.clone();\r\n    /** @internal */\r\n    public readonly rotationQuaternion: Quaternion = _SpatialAudioDefaults.rotationQuaternion.clone();\r\n\r\n    /** @internal */\r\n    public readonly node: PannerNode;\r\n\r\n    /** @internal */\r\n    public constructor(engine: _WebAudioEngine) {\r\n        super(engine);\r\n\r\n        this.node = new PannerNode(engine._audioContext);\r\n\r\n        this._orientationX = new _WebAudioParameterComponent(engine, this.node.orientationX);\r\n        this._orientationY = new _WebAudioParameterComponent(engine, this.node.orientationY);\r\n        this._orientationZ = new _WebAudioParameterComponent(engine, this.node.orientationZ);\r\n\r\n        this._positionX = new _WebAudioParameterComponent(engine, this.node.positionX);\r\n        this._positionY = new _WebAudioParameterComponent(engine, this.node.positionY);\r\n        this._positionZ = new _WebAudioParameterComponent(engine, this.node.positionZ);\r\n    }\r\n\r\n    /** @internal */\r\n    public override dispose(): void {\r\n        super.dispose();\r\n\r\n        this._orientationX.dispose();\r\n        this._orientationY.dispose();\r\n        this._orientationZ.dispose();\r\n        this._positionX.dispose();\r\n        this._positionY.dispose();\r\n        this._positionZ.dispose();\r\n\r\n        this.node.disconnect();\r\n    }\r\n\r\n    /** @internal */\r\n    public get coneInnerAngle(): number {\r\n        return D2r(this.node.coneInnerAngle);\r\n    }\r\n\r\n    public set coneInnerAngle(value: number) {\r\n        this.node.coneInnerAngle = R2d(value);\r\n    }\r\n\r\n    /** @internal */\r\n    public get coneOuterAngle(): number {\r\n        return D2r(this.node.coneOuterAngle);\r\n    }\r\n\r\n    public set coneOuterAngle(value: number) {\r\n        this.node.coneOuterAngle = R2d(value);\r\n    }\r\n\r\n    /** @internal */\r\n    public get coneOuterVolume(): number {\r\n        return this.node.coneOuterGain;\r\n    }\r\n\r\n    public set coneOuterVolume(value: number) {\r\n        this.node.coneOuterGain = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get distanceModel(): \"linear\" | \"inverse\" | \"exponential\" {\r\n        return this.node.distanceModel;\r\n    }\r\n\r\n    public set distanceModel(value: \"linear\" | \"inverse\" | \"exponential\") {\r\n        this.node.distanceModel = value;\r\n\r\n        // Wiggle the max distance to make the change take effect.\r\n        const maxDistance = this.node.maxDistance;\r\n        this.node.maxDistance = maxDistance + 0.001;\r\n        this.node.maxDistance = maxDistance;\r\n    }\r\n\r\n    /** @internal */\r\n    public get minDistance(): number {\r\n        return this.node.refDistance;\r\n    }\r\n\r\n    public set minDistance(value: number) {\r\n        this.node.refDistance = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get maxDistance(): number {\r\n        return this.node.maxDistance;\r\n    }\r\n\r\n    public set maxDistance(value: number) {\r\n        this.node.maxDistance = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get panningModel(): \"equalpower\" | \"HRTF\" {\r\n        return this.node.panningModel;\r\n    }\r\n\r\n    public set panningModel(value: \"equalpower\" | \"HRTF\") {\r\n        this.node.panningModel = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get rolloffFactor(): number {\r\n        return this.node.rolloffFactor;\r\n    }\r\n\r\n    public set rolloffFactor(value: number) {\r\n        this.node.rolloffFactor = value;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _inNode(): AudioNode {\r\n        return this.node;\r\n    }\r\n\r\n    /** @internal */\r\n    public get _outNode(): AudioNode {\r\n        return this.node;\r\n    }\r\n\r\n    /** @internal */\r\n    public _updatePosition(): void {\r\n        if (this._lastPosition.equalsWithEpsilon(this.position)) {\r\n            return;\r\n        }\r\n\r\n        // If attached and there is a ramp in progress, we assume another update is coming soon that we can wait for.\r\n        // We don't do this for unattached nodes because there may not be another update coming.\r\n        if (this.isAttached && (this._positionX.isRamping || this._positionY.isRamping || this._positionZ.isRamping)) {\r\n            return;\r\n        }\r\n\r\n        this._positionX.targetValue = this.position.x;\r\n        this._positionY.targetValue = this.position.y;\r\n        this._positionZ.targetValue = this.position.z;\r\n\r\n        this._lastPosition.copyFrom(this.position);\r\n    }\r\n\r\n    /** @internal */\r\n    public _updateRotation(): void {\r\n        // If attached and there is a ramp in progress, we assume another update is coming soon that we can wait for.\r\n        // We don't do this for unattached nodes because there may not be another update coming.\r\n        if (this.isAttached && (this._orientationX.isRamping || this._orientationY.isRamping || this._orientationZ.isRamping)) {\r\n            return;\r\n        }\r\n\r\n        if (!this._lastRotationQuaternion.equalsWithEpsilon(this.rotationQuaternion)) {\r\n            TmpQuaternion.copyFrom(this.rotationQuaternion);\r\n            this._lastRotationQuaternion.copyFrom(this.rotationQuaternion);\r\n        } else if (!this._lastRotation.equalsWithEpsilon(this.rotation)) {\r\n            Quaternion.FromEulerAnglesToRef(this.rotation.x, this.rotation.y, this.rotation.z, TmpQuaternion);\r\n            this._lastRotation.copyFrom(this.rotation);\r\n        } else {\r\n            return;\r\n        }\r\n\r\n        Matrix.FromQuaternionToRef(TmpQuaternion, TmpMatrix);\r\n        Vector3.TransformNormalToRef(Vector3.RightReadOnly, TmpMatrix, TmpVector);\r\n\r\n        this._orientationX.targetValue = TmpVector.x;\r\n        this._orientationY.targetValue = TmpVector.y;\r\n        this._orientationZ.targetValue = TmpVector.z;\r\n    }\r\n\r\n    protected override _connect(node: IWebAudioInNode): boolean {\r\n        const connected = super._connect(node);\r\n\r\n        if (!connected) {\r\n            return false;\r\n        }\r\n\r\n        // If the wrapped node is not available now, it will be connected later by the subgraph.\r\n        if (node._inNode) {\r\n            this.node.connect(node._inNode);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    protected override _disconnect(node: IWebAudioInNode): boolean {\r\n        const disconnected = super._disconnect(node);\r\n\r\n        if (!disconnected) {\r\n            return false;\r\n        }\r\n\r\n        if (node._inNode) {\r\n            this.node.disconnect(node._inNode);\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    /** @internal */\r\n    public getClassName(): string {\r\n        return \"_SpatialWebAudioSubNode\";\r\n    }\r\n}\r\n"]}