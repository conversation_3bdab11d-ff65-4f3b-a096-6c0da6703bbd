import type { Nullable } from "../types.js";
import type { Scene } from "../scene.js";
/**
 * Class used to work with sound analyzer using fast fourier transform (FFT)
 * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic
 */
export declare class Analyser {
    /**
     * Gets or sets the smoothing
     */
    SMOOTHING: number;
    /**
     * Gets or sets the FFT table size
     */
    FFT_SIZE: number;
    /**
     * Gets or sets the bar graph amplitude
     */
    BARGRAPHAMPLITUDE: number;
    /**
     * Gets or sets the position of the debug canvas
     */
    DEBUGCANVASPOS: {
        x: number;
        y: number;
    };
    /**
     * Gets or sets the debug canvas size
     */
    DEBUGCANVASSIZE: {
        width: number;
        height: number;
    };
    private _byteFreqs;
    private _byteTime;
    private _floatFreqs;
    private _webAudioAnalyser;
    private _debugCanvas;
    private _debugCanvasContext;
    private _scene;
    private _registerFunc;
    private _audioEngine;
    /**
     * Creates a new analyser
     * @param scene defines hosting scene
     */
    constructor(scene?: Nullable<Scene>);
    /**
     * Get the number of data values you will have to play with for the visualization
     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/frequencyBinCount
     * @returns a number
     */
    getFrequencyBinCount(): number;
    /**
     * Gets the current frequency data as a byte array
     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/getByteFrequencyData
     * @returns a Uint8Array
     */
    getByteFrequencyData(): Uint8Array;
    /**
     * Gets the current waveform as a byte array
     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/getByteTimeDomainData
     * @returns a Uint8Array
     */
    getByteTimeDomainData(): Uint8Array;
    /**
     * Gets the current frequency data as a float array
     * @see https://developer.mozilla.org/en-US/docs/Web/API/AnalyserNode/getByteFrequencyData
     * @returns a Float32Array
     */
    getFloatFrequencyData(): Float32Array;
    /**
     * Renders the debug canvas
     */
    drawDebugCanvas(): void;
    /**
     * Stops rendering the debug canvas and removes it
     */
    stopDebugCanvas(): void;
    /**
     * Connects two audio nodes
     * @param inputAudioNode defines first node to connect
     * @param outputAudioNode defines second node to connect
     */
    connectAudioNodes(inputAudioNode: AudioNode, outputAudioNode: AudioNode): void;
    /**
     * Releases all associated resources
     */
    dispose(): void;
}
