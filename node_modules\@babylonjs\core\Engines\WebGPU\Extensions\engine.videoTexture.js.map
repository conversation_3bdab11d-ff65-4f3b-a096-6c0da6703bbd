{"version": 3, "file": "engine.videoTexture.js", "sourceRoot": "", "sources": ["../../../../../../dev/core/src/Engines/WebGPU/Extensions/engine.videoTexture.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAiBlD,SAAS,iBAAiB,CAAC,OAAqD;IAC5E,OAAO,OAAO,IAAK,OAA2B,CAAC,kBAAkB,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACnG,CAAC;AAED,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,OAAkC,EAAE,KAAmD,EAAE,OAAgB;IAC3J,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QAClC,OAAO;IACX,CAAC;IAED,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;QAC5C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;IACvC,CAAC;IAED,IAAI,iBAAiB,GAAG,OAAO,CAAC,gBAAyC,CAAC;IAE1E,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,CAAC;QAChD,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,kCAAkC,CAAC,OAAO,CAAC,CAAC;IACxF,CAAC;IAED,IAAI,iBAAiB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC3B,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;YAClB,IAAI,CAAC;gBACD,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE,iBAAiB,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC;gBAC3F,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;oBAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;gBACnC,CAAC;YACL,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,kEAAkE;gBAClE,2EAA2E;YAC/E,CAAC;YACD,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAC3B,CAAC;IACL,CAAC;SAAM,IAAI,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;YACzB,0CAA0C;aACzC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YACb,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,EAAE,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACxJ,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;YAED,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAC3B,CAAC,CAAC;YACF,0CAA0C;aACzC,KAAK,CAAC,GAAG,EAAE;YACR,wJAAwJ;YACxJ,qBAAqB;YACrB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;QAC3B,CAAC,CAAC,CAAC;IACX,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["import type { InternalTexture } from \"../../../Materials/Textures/internalTexture\";\r\nimport type { Nullable } from \"../../../types\";\r\nimport { WebGPUEngine } from \"../../webgpuEngine\";\r\nimport type { WebGPUHardwareTexture } from \"../webgpuHardwareTexture\";\r\nimport type { ExternalTexture } from \"../../../Materials/Textures/externalTexture\";\r\n\r\ndeclare module \"../../abstractEngine\" {\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    export interface AbstractEngine {\r\n        /**\r\n         * Update a video texture\r\n         * @param texture defines the texture to update\r\n         * @param video defines the video element to use\r\n         * @param invertY defines if data must be stored with Y axis inverted\r\n         */\r\n        updateVideoTexture(texture: Nullable<InternalTexture>, video: HTMLVideoElement | Nullable<ExternalTexture>, invertY: boolean): void;\r\n    }\r\n}\r\n\r\nfunction IsExternalTexture(texture: Nullable<ExternalTexture> | HTMLVideoElement): texture is ExternalTexture {\r\n    return texture && (texture as ExternalTexture).underlyingResource !== undefined ? true : false;\r\n}\r\n\r\nWebGPUEngine.prototype.updateVideoTexture = function (texture: Nullable<InternalTexture>, video: HTMLVideoElement | Nullable<ExternalTexture>, invertY: boolean): void {\r\n    if (!texture || texture._isDisabled) {\r\n        return;\r\n    }\r\n\r\n    if (this._videoTextureSupported === undefined) {\r\n        this._videoTextureSupported = true;\r\n    }\r\n\r\n    let gpuTextureWrapper = texture._hardwareTexture as WebGPUHardwareTexture;\r\n\r\n    if (!texture._hardwareTexture?.underlyingResource) {\r\n        gpuTextureWrapper = this._textureHelper.createGPUTextureForInternalTexture(texture);\r\n    }\r\n\r\n    if (IsExternalTexture(video)) {\r\n        if (video.isReady()) {\r\n            try {\r\n                this._textureHelper.copyVideoToTexture(video, texture, gpuTextureWrapper.format, !invertY);\r\n                if (texture.generateMipMaps) {\r\n                    this._generateMipmaps(texture);\r\n                }\r\n            } catch (e) {\r\n                // WebGPU doesn't support video element who are not playing so far\r\n                // Ignore this error ensures we can start a video texture in a paused state\r\n            }\r\n            texture.isReady = true;\r\n        }\r\n    } else if (video) {\r\n        this.createImageBitmap(video)\r\n            // eslint-disable-next-line github/no-then\r\n            .then((bitmap) => {\r\n                this._textureHelper.updateTexture(bitmap, texture, texture.width, texture.height, texture.depth, gpuTextureWrapper.format, 0, 0, !invertY, false, 0, 0);\r\n                if (texture.generateMipMaps) {\r\n                    this._generateMipmaps(texture);\r\n                }\r\n\r\n                texture.isReady = true;\r\n            })\r\n            // eslint-disable-next-line github/no-then\r\n            .catch(() => {\r\n                // Sometimes createImageBitmap(video) fails with \"Failed to execute 'createImageBitmap' on 'Window': The provided element's player has no current data.\"\r\n                // Just keep going on\r\n                texture.isReady = true;\r\n            });\r\n    }\r\n};\r\n"]}